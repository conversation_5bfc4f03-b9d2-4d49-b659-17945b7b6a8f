<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="VC_USER_PV_COUNT">


	<resultMap type="com.uinnova.product.eam.comm.model.VcUserPvCount" id="queryResult">
		<result column="ID" jdbcType="BIGINT" property="id"/>	<!-- ID -->
		<result column="DOMAIN_ID" jdbcType="BIGINT" property="domainId"/>	<!-- 所属域 -->
		<result column="DATA_STATUS" jdbcType="INTEGER" property="dataStatus"/>	<!-- 数据状态 -->
		<result column="CREATE_TIME" jdbcType="BIGINT" property="createTime"/>	<!-- 创建时间 -->
		<result column="MODIFY_TIME" jdbcType="BIGINT" property="modifyTime"/>	<!-- 更新时间 -->
		<result column="USER_ID" jdbcType="BIGINT" property="userId"/>	<!-- 用户id -->
		<result column="TARGET_DESC" jdbcType="VARCHAR" property="targetDesc"/>	<!-- 行为描述 -->
		<result column="TARGET_CODE" jdbcType="VARCHAR" property="targetCode"/>	<!-- 行为编码 -->
		<result column="COUNT_DATA_TIME" jdbcType="BIGINT" property="countDataTime"/>	<!-- 统计数据时间 -->
		<result column="PC_COUNT" jdbcType="BIGINT" property="pcCount"/>	<!-- 操作次数 -->
	</resultMap>
	

	<sql id="sql_query_where">
		<if test="cdt != null and cdt.id != null " >and
			ID = #{cdt.id:BIGINT}
		</if>
		<if test="ids != null and ids != '' " >and
			ID in (${ids})
		</if>
		<if test="cdt != null and cdt.startId != null and cdt.startId != '' " >and
			ID &gt; #{cdt.startId:BIGINT}
		</if>
		<if test="cdt != null and cdt.endId != null and cdt.endId != '' " >and
			ID &lt; #{cdt.endId:BIGINT}
		</if>
		<if test="cdt != null and cdt.domainId != null " >and
			DOMAIN_ID = #{cdt.domainId:BIGINT}
		</if>
		<if test="domainIds != null and domainIds != '' " >and
			DOMAIN_ID in (${domainIds})
		</if>
		<if test="cdt != null and cdt.startDomainId != null and cdt.startDomainId != '' " >and
			DOMAIN_ID &gt; #{cdt.startDomainId:BIGINT}
		</if>
		<if test="cdt != null and cdt.endDomainId != null and cdt.endDomainId != '' " >and
			DOMAIN_ID &lt; #{cdt.endDomainId:BIGINT}
		</if>
		<if test="cdt != null and cdt.dataStatus != null " >and
			DATA_STATUS = #{cdt.dataStatus:INTEGER}
		</if>
		<if test="dataStatuss != null and dataStatuss != '' " >and
			DATA_STATUS in (${dataStatuss})
		</if>
		<if test="cdt != null and cdt.startDataStatus != null and cdt.startDataStatus != '' " >and
			DATA_STATUS &gt; #{cdt.startDataStatus:INTEGER}
		</if>
		<if test="cdt != null and cdt.endDataStatus != null and cdt.endDataStatus != '' " >and
			DATA_STATUS &lt; #{cdt.endDataStatus:INTEGER}
		</if>
		<if test="cdt != null and cdt.createTime != null " >and
			CREATE_TIME = #{cdt.createTime:BIGINT}
		</if>
		<if test="createTimes != null and createTimes != '' " >and
			CREATE_TIME in (${createTimes})
		</if>
		<if test="cdt != null and cdt.startCreateTime != null and cdt.startCreateTime != '' " >and
			CREATE_TIME &gt; #{cdt.startCreateTime:BIGINT}
		</if>
		<if test="cdt != null and cdt.endCreateTime != null and cdt.endCreateTime != '' " >and
			CREATE_TIME &lt; #{cdt.endCreateTime:BIGINT}
		</if>
		<if test="cdt != null and cdt.modifyTime != null " >and
			MODIFY_TIME = #{cdt.modifyTime:BIGINT}
		</if>
		<if test="modifyTimes != null and modifyTimes != '' " >and
			MODIFY_TIME in (${modifyTimes})
		</if>
		<if test="cdt != null and cdt.startModifyTime != null and cdt.startModifyTime != '' " >and
			MODIFY_TIME &gt; #{cdt.startModifyTime:BIGINT}
		</if>
		<if test="cdt != null and cdt.endModifyTime != null and cdt.endModifyTime != '' " >and
			MODIFY_TIME &lt; #{cdt.endModifyTime:BIGINT}
		</if>
		<if test="cdt != null and cdt.userId != null " >and
			USER_ID = #{cdt.userId:BIGINT}
		</if>
		<if test="userIds != null and userIds != '' " >and
			USER_ID in (${userIds})
		</if>
		<if test="cdt != null and cdt.startUserId != null and cdt.startUserId != '' " >and
			USER_ID &gt; #{cdt.startUserId:BIGINT}
		</if>
		<if test="cdt != null and cdt.endUserId != null and cdt.endUserId != '' " >and
			USER_ID &lt; #{cdt.endUserId:BIGINT}
		</if>
		<if test="cdt != null and cdt.targetDesc != null " >and
			TARGET_DESC like #{cdt.targetDesc,jdbcType=VARCHAR} 
		</if>
		<if test="cdt != null and cdt.targetDescEqual != null " >and
			TARGET_DESC like #{cdt.targetDescEqual,jdbcType=VARCHAR} 
		</if>
		<if test="targetDescs != null and targetDescs != '' " >and
			TARGET_DESC in ${targetDescs} 
		</if>
		<if test="cdt != null and cdt.targetCode != null " >and
			TARGET_CODE like #{cdt.targetCode,jdbcType=VARCHAR} 
		</if>
		<if test="cdt != null and cdt.targetCodeEqual != null " >and
			TARGET_CODE like #{cdt.targetCodeEqual,jdbcType=VARCHAR} 
		</if>
		<if test="targetCodes != null and targetCodes != '' " >and
			TARGET_CODE in ${targetCodes} 
		</if>
		<if test="cdt != null and cdt.countDataTime != null " >and
			COUNT_DATA_TIME = #{cdt.countDataTime:BIGINT}
		</if>
		<if test="countDataTimes != null and countDataTimes != '' " >and
			COUNT_DATA_TIME in (${countDataTimes})
		</if>
		<if test="cdt != null and cdt.startCountDataTime != null and cdt.startCountDataTime != '' " >and
			COUNT_DATA_TIME &gt; #{cdt.startCountDataTime:BIGINT}
		</if>
		<if test="cdt != null and cdt.endCountDataTime != null and cdt.endCountDataTime != '' " >and
			COUNT_DATA_TIME &lt; #{cdt.endCountDataTime:BIGINT}
		</if>
		<if test="cdt != null and cdt.pcCount != null " >and
			PC_COUNT = #{cdt.pcCount:BIGINT}
		</if>
		<if test="pcCounts != null and pcCounts != '' " >and
			PC_COUNT in (${pcCounts})
		</if>
		<if test="cdt != null and cdt.startPcCount != null and cdt.startPcCount != '' " >and
			PC_COUNT &gt; #{cdt.startPcCount:BIGINT}
		</if>
		<if test="cdt != null and cdt.endPcCount != null and cdt.endPcCount != '' " >and
			PC_COUNT &lt; #{cdt.endPcCount:BIGINT}
		</if>
	</sql>
	

	<sql id="sql_update_columns">
		<if test="record != null and record.id != null"> 
			ID = #{record.id:BIGINT},
		</if>
		<if test="record != null and record.domainId != null"> 
			DOMAIN_ID = #{record.domainId:BIGINT},
		</if>
		<if test="record != null and record.dataStatus != null"> 
			DATA_STATUS = #{record.dataStatus:INTEGER},
		</if>
		<if test="record != null and record.createTime != null"> 
			CREATE_TIME = #{record.createTime:BIGINT},
		</if>
		<if test="record != null and record.modifyTime != null"> 
			MODIFY_TIME = #{record.modifyTime:BIGINT},
		</if>
		<if test="record != null and record.userId != null"> 
			USER_ID = #{record.userId:BIGINT},
		</if>
		<if test="record != null and record.targetDesc != null"> 
			TARGET_DESC = #{record.targetDesc:VARCHAR},
		</if>
		<if test="record != null and record.targetCode != null"> 
			TARGET_CODE = #{record.targetCode:VARCHAR},
		</if>
		<if test="record != null and record.countDataTime != null"> 
			COUNT_DATA_TIME = #{record.countDataTime:BIGINT},
		</if>
		<if test="record != null and record.pcCount != null"> 
			PC_COUNT = #{record.pcCount:BIGINT},
		</if>
	</sql>
	

	<sql id="sql_query_columns">
		ID, DOMAIN_ID, DATA_STATUS, CREATE_TIME, MODIFY_TIME, USER_ID, 
		TARGET_DESC, TARGET_CODE, COUNT_DATA_TIME, PC_COUNT
	</sql>
	

	

	<select id="selectList" parameterType="java.util.Map" resultMap="queryResult">
		select 
			<include refid="VC_USER_PV_COUNT.sql_query_columns" />
		from VC_USER_PV_COUNT 
			<where>
				<include refid="VC_USER_PV_COUNT.sql_query_where"/>
			</where>
		order by 
			<if  test="orders != null and orders != ''">
				${orders}
			</if>
			<if  test="orders == null or orders == ''">
				ID
			</if>
	</select>
	

	

	<select id="selectCount" parameterType="java.util.Map" resultType="java.lang.Long">
		select count(1) from VC_USER_PV_COUNT 
			<where>
				<include refid="VC_USER_PV_COUNT.sql_query_where"></include>
			</where>
	</select>
	

	

	<select id="selectById" parameterType="java.util.Map" resultMap="queryResult">
		select 
			<include refid="VC_USER_PV_COUNT.sql_query_columns" />
		from VC_USER_PV_COUNT where ID=#{id:BIGINT} and DATA_STATUS=1  
	</select>
	

	

	<insert id="insert" parameterType="java.util.Map">
		insert into VC_USER_PV_COUNT(
			ID, DOMAIN_ID, DATA_STATUS, CREATE_TIME, MODIFY_TIME, 
			USER_ID, TARGET_DESC, TARGET_CODE, COUNT_DATA_TIME, PC_COUNT)
		values (
			#{record.id:BIGINT}, #{record.domainId:BIGINT}, #{record.dataStatus:INTEGER}, #{record.createTime:BIGINT}, #{record.modifyTime:BIGINT}, 
			#{record.userId:BIGINT}, #{record.targetDesc:VARCHAR}, #{record.targetCode:VARCHAR}, #{record.countDataTime:BIGINT}, #{record.pcCount:BIGINT})
	</insert>
	

	

	<update id="updateById" parameterType="java.util.Map">
		update VC_USER_PV_COUNT
			<set> 
				<include refid="VC_USER_PV_COUNT.sql_update_columns"/> 
			</set>
		where ID = #{id:BIGINT}
	</update>
	

	<update id="updateByCdt" parameterType="java.util.Map">
		update VC_USER_PV_COUNT
			<set> 
				<include refid="VC_USER_PV_COUNT.sql_update_columns"/> 
			</set>
			<where> 
				<include refid="VC_USER_PV_COUNT.sql_query_where"/> 
			</where>
	</update>
	
	

	

	<delete id="deleteById" parameterType="java.util.Map">
		delete from VC_USER_PV_COUNT where ID = #{id:BIGINT}
	</delete>
	

	

	<delete id="deleteByCdt" parameterType="java.util.Map">
		delete from VC_USER_PV_COUNT
			<where> 
				<include refid="VC_USER_PV_COUNT.sql_query_where"/> 
			</where>
	</delete>
	



</mapper>
