package com.uinnova.product.vmdb.provider.ci;

import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.comm.model.ci.*;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CiClassRltInfo;

import java.util.List;
import java.util.Map;

/**
 * 
 * <AUTHOR>
 *
 */
public interface CcCiClassSvc {

    /**
     * 不分页查询分类数据
     * 
     * @param domainId
     *            数据域
     * @param cdt
     *            : 条件对象
     * @param orders
     *            : 排序字段
     * @return
     */
    public List<CcCiClassDir> queryClassDirList(Long domainId, CCcCiClassDir cdt, String orders);

    /**
     * 跟据主键查询分类数据
     * 
     * @param domainId
     *            数据域
     * @param dirId:
     *            主键值
     * @return
     */
    public CcCiClassDir queryClassDirById(Long domainId, Long dirId);

    /**
     * 保存或更新，判断主键ID[id]是否存在, 存在则更新, 不存在则插入
     * 
     * @param domainId
     *            数据域
     * @param record
     *            : 数据记录
     * @return 当前记录主键[id]值
     */
    public Long saveOrUpdateClassDir(Long domainId, CcCiClassDir record);

    /**
     * 批量保存,存在则忽略,仅保存且默认是一级目录
     * 
     * @param domainId
     *            数据域
     * @param ciType
     *            1=基础CI 2=关系CI
     * @param records
     *            : 数据记录
     * @return 保存成功的数量
     */
    public Integer saveBatchClassDirs(Long domainId, Integer ciType, List<CcCiClassDir> records);

    /**
     * 分页查询CI分类数据
     * 
     * @param domainId
     *            数据域
     * @param pageNum
     *            : 页码
     * @param pageSize
     *            每页数
     * @param cdt
     *            : 条件对象
     * @param orders
     *            : 排序字段
     * @return
     */
    public Page<CcCiClass> queryCiClassPage(Long domainId, Integer pageNum, Integer pageSize, CCcCiClass cdt, String orders);

    /**
     * 不分页查询CI分类数据
     * 
     * @param domainId
     *            数据域
     * @param cdt
     *            : 条件对象
     * @param orders
     *            : 排序字段
     * @return
     */
    public List<CcCiClass> queryCiClassList(Long domainId, CCcCiClass cdt, String orders);

    /**
     * 跟据主键查询CI或者关系分类数据
     * 
     * @param domainId
     *            数据域
     * @param classId:
     *            主键值
     * @return
     */
    public CcCiClass queryCiClassById(Long domainId, Long classId);

    /**
     * 分页查询CI分类数据
     * 
     * @param domainId
     *            数据域
     * @param pageNum
     *            : 页码
     * @param pageSize
     *            每页数
     * @param cdt
     *            : 条件对象
     * @param orders
     *            : 排序字段
     * @return
     */
    public Page<CcCiClassInfo> queryCiClassInfoPage(Long domainId, Integer pageNum, Integer pageSize, CCcCiClass cdt, String orders);

    /**
     * 不分页查询CI分类数据
     * 
     * @param domainId
     *            数据域
     * @param cdt
     *            : 条件对象
     * @param orders
     *            : 排序字段
     * @return
     */
    public List<CcCiClassInfo> queryCiClassInfoList(Long domainId, CCcCiClass cdt, String orders);

    /**
     * 不分页查询CI分类数据, 属性会继承上级属性一起返回
     * 
     * @param domainId
     *            数据域
     * @param cdt
     *            : 条件对象
     * @param orders
     *            : 排序字段
     * @return
     */
    public List<CcCiClassInfo> queryCiClassInfoListAttrCascade(Long domainId, CCcCiClass cdt, String orders);

    /**
     * 查询被使用的图标
     * 
     * @param iconAddrs
     * @return
     */
    public List<String> queryUsedIcon(List<String> iconAddrs);

    /**
     * 跟据主键查询CI分类数据
     * 
     * @param domainId
     *            数据域
     * @param classId:
     *            主键值
     * @return
     */
    public CcCiClassInfo queryCiClassInfoById(Long domainId, Long classId);

    /**
     * 跟据classId查询对应的属性定义
     * 
     * @param domainId
     *            数据域
     * @param classId
     *            所属分类
     * @param cdt
     *            属性条件
     * @param orders
     *            属性排序字段
     * @return
     */
    public List<CcCiAttrDef> queryAttrDefList(Long domainId, CCcCiAttrDef cdt, String orders);

    /**
     * 跟据classId查询对应的属性定义
     * 
     * @param domainId
     *            数据域
     * @param classId
     *            所属分类
     * @param cdt
     *            属性条件
     * @param orders
     *            属性排序字段
     * @return
     */
    public List<CcCiAttrDef> queryCiClassAttrDefList(Long domainId, Long classId, CCcCiAttrDef cdt, String orders);

    /**
     * 跟据classId级联查询对应的属性定义, 包括父级的属性
     * 
     * @param domainId
     *            数据域
     * @param classId
     *            所属分类
     * @param cdt
     *            属性条件
     * @param orders
     *            属性排序字段
     * @return
     */
    public List<CcCiAttrDef> queryCiClassAttrDefsCascade(Long domainId, Long classId, CCcCiAttrDef cdt, String orders);

    /**
     * 跟据classId级联查询对应的属性定义, 包括父级的属性
     * 
     * @param domainId
     *            数据域
     * @param classId
     *            所属分类
     * @param cdt
     *            属性条件
     * @param orders
     *            属性排序字段
     * @return key=classId
     */
    public Map<Long, List<CcCiAttrDef>> queryCiClassAttrDefsCascadeBatch(Long domainId, Long[] classIds, CCcCiAttrDef cdt, String orders);

    /**
     * 跟据classId级联查询对应的属性定义, 包括父级的属性
     * 
     * @param domainId
     *            数据域
     * @param classId
     *            所属分类
     * @param cdt
     *            属性条件
     * @param orders
     *            属性排序字段
     * @return List[0]=最顶级分类, List[length-1]=条件指定的classId
     */
    public List<CcCiClassInfo> queryCiClassAttrDefsCascadeInfo(Long domainId, Long classId, CCcCiAttrDef cdt, String orders);

    /**
     * 通过ci和tag的ids查询相关联的分类信息
     * 
     * @param domainId
     *            数据域
     * @param ciIds
     *            ci的ids
     * @param tagIds
     *            tag的ids
     * @param orders
     *            排序
     * @return
     */
    public List<CcCiClassInfo> queryCiClassByCiIdsAndTagIds(Long domainId, Long[] ciIds, Long[] tagIds, String orders);

    /**
     * 获取分类锁
     * 
     * @return
     */
    public CcClassStatus getClassLock(Long domainId, Long classId, Integer lockedType);

    /**
     * 释放分类锁
     * 
     * @param id
     *            锁ID
     */
    public void releaseClassLockById(Long id);

    /**
     * 释放分类锁
     * 
     * @param domainId
     * @param classId
     */
    public void releaseClassLock(Long domainId, Long classId);

    /**
     * 保存或更新，判断主键ID[id]是否存在, 存在则更新, 不存在则插入
     * 
     * @param domainId
     *            数据域
     * @param record
     *            : 数据记录
     * @return 当前记录主键[id]值
     */
    public Long saveOrUpdateCiClass(Long domainId, CcCiClass record);

    /**
     * 批量更新分类基本信息,仅支持不涉及上下级变动的(比如图标,名字)
     * 
     * @param domainId
     *            数据域
     * @param records
     *            : 数据记录
     * @return 当前记录主键[id]值
     */
    public Integer updateCiClassBatch(Long domainId, List<CcCiClass> records);

    /**
     * 保存或更新属性
     * 
     * @deprecated 推荐使用全量的 {@link #saveOrUpdateCiClassInfo(Long, CcCiClassInfo)}
     * 
     * @param domainId
     *            数据域
     * @param classId
     *            指定CI分类
     * @param attrDefs
     *            CI分类属性定义
     * @return 主键ID
     */
    @Deprecated
    public void saveOrUpdateCiClassAttrDefs(Long domainId, Long classId, List<CcCiAttrDef> attrDefs);

 
    /**
     * 保存或更新ci分类信息
     * 
     * @param domainId
     *            数据域
     * @param record
     * @return 分类ID
     */
    public Long saveOrUpdateCiClassInfo(Long domainId, CcCiClassInfo record);

    /**
     * 当主键修改后删除CI数据
     * 
     * @param domainId
     * @param record
     * @return
     */
    public List<Long> saveOrUpdateCiClassInfoListMajorKeyChangeCleanCi(Long domainId, List<CcCiClassInfo> record);

    /**
     * 跟据dirId删除目录
     * 
     * @param domainId
     * @param dirId
     * @return
     */
    public Integer removeClassDirById(Long domainId, Long dirId);

    /**
     * 跟据dirId批量删除目录(直接删除父目录)
     * 
     * @param domainId
     * @param dirIds
     * @return
     */
    public Integer removeClassDirByIds(Long domainId, Long[] dirIds);

    /**
     * 跟据CI分类ID删除分类,如果分类下有子类则删除失败(属性、CI数据会一并删除)
     * 
     * @param domainId
     *            数据域
     * @param classId
     *            分类ID
     * @return 删除成功返回1, 否则返回0
     */
    public Integer removeCiClassById(Long domainId, Long classId);

    /**
     * 批量跟删除分类,如果分类下有子类则删除失败(属性、CI数据会一并删除)
     * 
     * @param domainId
     *            数据域
     * @param ids
     *            分类ID
     * @return 删除成功返回1, 否则返回0
     */
    public Integer removeCiClassByIds(Long domainId, Long[] ids);

    /**
     * 获取最在更新时间
     * 
     * @param cdt
     * @return
     */
    public Long getMaxModifyTime(Long domainId, CCcCiClass cdt);

    /**
     * 获取记录数
     * 
     * @param cdt
     * @return
     */
    public Long getRecordCount(Long domainId, CCcCiClass cdt);

    /**
     * 根据主键是否为空判断：保存或者更新
     * 
     * @param domainId
     *            数据域
     * @param record
     *            数据
     */
    public Long saveOrUpdateCiPluginAttr(Long domainId, CcCiPluginAttr record);

    /**
     * 批量保存或者更新数据，根据主键判断
     * 
     * @param domainId
     * @param records
     * @return
     */
    public Integer saveOrUpdateCiPluginAttrBatch(Long domainId, List<CcCiPluginAttr> records);

    /**
     * 根据主键查询ci外挂属性
     * 
     * @param id
     *            主键Id
     * @return
     */
    public CcCiPluginAttr queryCiPluginAttrById(Long id);

    /**
     * 根据条件查询ci外挂属性
     * 
     * @param domainId
     *            数据域
     * @param cdt
     *            查询条件
     * @param orders
     *            排序方式
     * @return
     */
    public List<CcCiPluginAttr> queryCiPluginAttrByCdt(Long domainId, CCcCiPluginAttr cdt, String orders);

    /**
     * 分页查询数据,带count数
     * 
     * @param domainId
     * @param pageNum
     * @param pageSize
     * @param cdt
     *            查询条件
     * @param orders
     *            排序方式
     */
    public Page<CcCiPluginAttr> queryCiPluginAttrPageByCdt(Long domainId, Integer pageNum, Integer pageSize, CCcCiPluginAttr cdt, String orders);

    /**
     * 根据主键删除数据
     * 
     * @param id
     *            主键id
     * @return
     */
    public Integer removeCiPluginAttrById(Long id);

    /**
     * 根据主键删除数据
     * 
     * @param domainId
     * @param cdt
     * @return
     */
    public Integer removeCiPluginAttrByCdt(Long domainId, CCcCiPluginAttr cdt);

    /**
     * 不分页查询有添加权限的CI分类数据
     * 
     * @param cdt
     *            : 条件对象
     * @param orders
     *            : 排序字段
     * @return
     */
    public List<CcCiClassInfo> queryLoginUserAddingAuthCiClassInfoList(CCcCiClass cdt, String orders);

    /**
     * 通过CI属性类型查询可添加此类型属性的字段数量
     * 
     * @param typeCode
     *            属性类型名Code
     * @return 数量（-1为查询失败）
     */
    public Integer queryAttrCount(Integer typeCode);

    /**
     * 检查此类型字段在库中够不够
     * 
     * @param attrDefs
     */
    public void checkAttrCount(List<CcCiAttrDef> attrDefs);

    /**
     * 检查属性数量
     * 
     * @param count
     */
    public void checkAttrCount(Integer count);

    /**
     * 保存或者更新分类关系线信息,会自动给子分类建相同的关系(DMV有调用)
     * 
     * @param domainId
     *            数据域
     * @param record
     *            保存对象
     * @return 新增记录的主键ID
     */
    public Long saveOrUpdateCiClassRlt(Long domainId, CcCiClassRlt record);

    /**
     * 不分页查询CI分类关系信息(DMV有调用)
     * 
     * @param cdt
     * @param orders
     * @return
     */
    public List<CiClassRltInfo> queryCiClassRltInfoList(Long domainId, CCcCiClassRlt cdt, String orders);

    /**
     * 校验属性规则是否合法
     *
     * @param attrDefList
     *            属性定义列表
     */
    public void checkAttrDef(List<CcCiAttrDef> attrDefList);

    /**
     * 查询挂载了kpi的分类
     * @return
     */
    public List<CcCiClassInfo> queryCiClassInfoWithKpi(Long domainId);

}
