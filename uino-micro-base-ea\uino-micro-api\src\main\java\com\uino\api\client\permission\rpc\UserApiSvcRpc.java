package com.uino.api.client.permission.rpc;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import com.uino.bean.permission.base.SysModuleOutSide;
import com.uino.bean.permission.query.CAuthDataModuleBean;
import com.uino.dao.BaseConst;
import org.elasticsearch.index.query.QueryBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.binary.jdbc.Page;
import com.uino.bean.cmdb.business.ImportResultMessage;
import com.uino.bean.cmdb.business.ImportSheetMessage;
import com.uino.bean.permission.base.SysRoleDataModuleRlt;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.permission.business.CurrentUserInfo;
import com.uino.bean.permission.business.ModuleNodeInfo;
import com.uino.bean.permission.business.SysUserPwdResetParam;
import com.uino.bean.permission.business.UserInfo;
import com.uino.bean.permission.business.request.SaveUserRoleRltRequestDto;
import com.uino.bean.permission.query.CSysUser;
import com.uino.bean.permission.query.UserSearchBeanExtend;
import com.uino.provider.feign.permission.UserFeign;
import com.uino.api.client.permission.IUserApiSvc;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
@Primary
public class UserApiSvcRpc implements IUserApiSvc {

    @Autowired
    private UserFeign userFeign;

    @Override
    public Long saveOrUpdate(UserInfo userInfo) {
        return userFeign.saveOrUpdate(userInfo);
    }

    @Override
    public ImportSheetMessage saveOrUpdateUserInfoBatch(List<UserInfo> userInfos) {
        return userFeign.saveOrUpdateUserInfoBatch(BaseConst.DEFAULT_DOMAIN_ID, false, userInfos);
    }

    @Override
    public ImportSheetMessage saveOrUpdateUserInfoBatch(Long domainId, boolean isUpdatePassword, List<UserInfo> userInfos) {
        return userFeign.saveOrUpdateUserInfoBatch(domainId, isUpdatePassword, userInfos);
    }

    @Override
    public ImportSheetMessage syncUserBatch(List<UserInfo> userInfos) {
        return userFeign.syncUserBatch(BaseConst.DEFAULT_DOMAIN_ID,userInfos);
    }

    @Override
    public ImportSheetMessage syncUserBatch(Long domainId, List<UserInfo> userInfos) {
        return userFeign.syncUserBatch(domainId,userInfos);
    }

    @Override
    public UserInfo getUserInfoById(Long userId) {
        return userFeign.getUserInfoById(userId);
    }

    @Override
    public UserInfo getUserInfoByLoginCode(String loginCode) {
        return userFeign.getUserInfoByLoginCode(loginCode);
    }

    @Override
    public List<SysUser> getSysUserByCdt(CSysUser cdt) {
        if (cdt == null) { return userFeign.getSysUserByCdtWithoutBody(); }
        return userFeign.getSysUserByCdt(cdt);
    }

    @Override
    public List<UserInfo> getUserInfoByCdt(CSysUser cdt, boolean rmSensitiveData) {
        if (cdt == null) {
            userFeign.getUserInfoByCdtWithoutBody(rmSensitiveData);
        }
        return userFeign.getUserInfoByCdt(cdt, rmSensitiveData);
    }

    @Override
    public Page<UserInfo> getListByQuery(UserSearchBeanExtend bean) {
        return userFeign.getListByQuery(bean);
    }

    @Override
    public List<SysUser> getUserByRoleId(Long roleId) {
        return userFeign.getUserByRoleId(roleId);
    }

    @Override
    public Long updateCurUser(SysUser op, MultipartFile file) {
        return userFeign.updateCurUser(op, file);
    }

    @Override
    public Long resetPwd(SysUserPwdResetParam pwdParam) {
        return userFeign.resetPwd(BaseConst.DEFAULT_DOMAIN_ID,pwdParam);
    }

    @Override
    public Long resetPwd(Long domainId, SysUserPwdResetParam pwdParam) {
        return userFeign.resetPwd(domainId,pwdParam);
    }

    @Override
    public Long resetPwdByAdmin(SysUserPwdResetParam pwdParam) {
        return userFeign.resetPwdByAdmin(pwdParam);
    }

    @Override
    public ImportResultMessage importUserByExcel(MultipartFile file) {
        return userFeign.importUserByExcel(BaseConst.DEFAULT_DOMAIN_ID,file);
    }

    @Override
    public ImportResultMessage importUserByExcel(Long domainId, MultipartFile file) {
        return userFeign.importUserByExcel(domainId,file);
    }

    @Override
    public ResponseEntity<byte[]> exportUserToExcel(Boolean isTpl, CSysUser cdt) {
        if (cdt == null) { return userFeign.exportUserToExcelWithoutBody(isTpl); }
        return userFeign.exportUserToExcel(isTpl, cdt);
    }

    @Override
    public Integer deleteById(Long userId) {
        return userFeign.deleteById(userId);
    }

    @Override
    public Integer deleteSysUserByLoginCodeBatch(List<String> loginCodes) {
        return userFeign.deleteSysUserByLoginCodeBatch(BaseConst.DEFAULT_DOMAIN_ID,loginCodes);
    }

    @Override
    public Integer deleteSysUserByLoginCodeBatch(Long domainId, List<String> loginCodes) {
        return userFeign.deleteSysUserByLoginCodeBatch(domainId,loginCodes);
    }

    @Override
    public Integer saveUserRoleRlt(SaveUserRoleRltRequestDto bean) {
        // TODO Auto-generated method stub
        return null;
    }

    @Override
    public ModuleNodeInfo getModuleTree(Long userId) {
        return userFeign.getModuleTree(BaseConst.DEFAULT_DOMAIN_ID,userId);
    }

    @Override
    public ModuleNodeInfo getModuleTree(Long domainId, Long userId) {
        return userFeign.getModuleTree(domainId,userId);
    }

    @Override
    public Map<String, List<SysRoleDataModuleRlt>> getDataModule(Long userId, List<String> moduleCodes) {
        return userFeign.getDataModule(userId, moduleCodes);
    }

    @Override
    public Long enshrineModule(Long userId, Long moduleId) {
        return userFeign.enshrineModule(BaseConst.DEFAULT_DOMAIN_ID,userId, moduleId);
    }

    @Override
    public Long enshrineModule(Long domainId, Long userId, Long moduleId) {
        return userFeign.enshrineModule(domainId,userId, moduleId);
    }

    @Override
    public Integer unenshrineModule(Long userId, Long moduleId) {
        return userFeign.unenshrineModule(BaseConst.DEFAULT_DOMAIN_ID,userId, moduleId);
    }

    @Override
    public Integer unenshrineModule(Long domainId, Long userId, Long moduleId) {
        return userFeign.unenshrineModule(domainId,userId, moduleId);
    }

    @Override
    public long countByCondition(QueryBuilder query) {
        return userFeign.countByCondition(BaseConst.DEFAULT_DOMAIN_ID,query);
    }

    @Override
    public long countByCondition(Long domainId, QueryBuilder query) {
        return userFeign.countByCondition(domainId,query);
    }

    @Override
    public int recordLoginFailNum(String loginName, boolean success) {
        return userFeign.recordLoginFailNum(BaseConst.DEFAULT_DOMAIN_ID,loginName, success);
    }

    @Override
    public int recordLoginFailNum(Long domainId, String loginName, boolean success) {
        return userFeign.recordLoginFailNum(domainId,loginName, success);
    }

    @Override
    public CurrentUserInfo getCurrentUser() {
        return userFeign.getCurrentUser();
    }

    @Override
    public int unLockUsersByDuration(Long durationSecond) {
        return userFeign.unLockUsersByDuration(durationSecond);
    }
    @Override
    public int getLoginFailLockNum() {
        // TODO Auto-generated method stub
        return userFeign.getLoginFailLockNum();
    }

    @Override
    public long getUserLockDuration() {
        // TODO Auto-generated method stub
        return userFeign.getUserLockDuration();
    }

	@Override
	public void verifyAuth() {
		userFeign.verifyAuth();
	}

    @Override
    public List<SysModuleOutSide> getModulesByLoginCode(String loginCode) {
        return userFeign.getModulesByLoginCode(BaseConst.DEFAULT_DOMAIN_ID,loginCode);
    }

    @Override
    public List<SysModuleOutSide> getModulesByLoginCode(Long domainId, String loginCode) {
        return userFeign.getModulesByLoginCode(domainId,loginCode);
    }

    @Override
    public Map<Long, Map<String, Integer>> getDataPermissionByUser(CAuthDataModuleBean cAuthDataModuleBean) {
        return userFeign.getDataPermissionByUser(cAuthDataModuleBean);
    }

    @Override
    public List<SysUser> getUserByRoleName(String roleName) {
        return userFeign.getUserByRoleName(roleName);
    }

    @Override
    public List<SysUser> getUserInfoByName(String userName) {
        return userFeign.getUserInfoByName(userName);
    }

    @Override
    public Page<SysUser> findUserInfoByNameForPage(Integer pageNum, Integer pageSize, String userName) {
        return userFeign.findUserInfoByNameForPage(pageNum, pageSize, userName);
    }

    @Override
    public Map<String, String> getNameByCodes(Collection<String> loginCodes) {
        return userFeign.getNameByCodes(loginCodes);
    }

    @Override
    public Page<SysUser> queryPageByRoleName(String roleName, String loginName, int pageNum, int pageSize) {
        return null;
    }
}
