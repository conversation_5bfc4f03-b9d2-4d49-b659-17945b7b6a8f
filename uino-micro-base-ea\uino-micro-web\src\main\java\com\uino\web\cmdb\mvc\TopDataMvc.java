package com.uino.web.cmdb.mvc;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import com.uino.bean.permission.base.SysUser;
import com.uino.init.api.ApiResult;
import com.uino.init.api.ApiVersion;
import com.uino.util.sys.SysUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uino.bean.cmdb.SysTopData;
import com.uino.api.client.cmdb.ITopDataApiSvc;

@ApiVersion(1)
@Api(value = "置顶", tags = {"数据集"})
@RestController
@RequestMapping(value = "/cmdb/topData")
public class TopDataMvc {
	@Autowired
	private ITopDataApiSvc topDataApi;

	/**
	 * 取消置顶
	 * 
	 * @param reqDto
	 * @param request
	 * @param response
	 */
	@ApiOperation("取消置顶")
	@PostMapping("unTop")
    @ModDesc(desc = "取消置顶", pDesc = "置顶数据传输对象", pType = SysTopData.class, rDesc = "操作是否成功", rType = Boolean.class)
	public ApiResult<Boolean> unTop(@RequestBody SysTopData reqDto, HttpServletRequest request, HttpServletResponse response) {
		SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
		topDataApi.unTop(currentUserInfo.getDomainId(), reqDto.getTopDataId(), reqDto.getTopDataType());
		return ApiResult.ok(this).data(true);
	}

	/**
	 * 置顶
	 * 
	 * @param reqDto
	 */
	@ApiOperation("置顶")
	@PostMapping("top")
    @ModDesc(desc = "置顶", pDesc = "置顶数据传输对象", pType = SysTopData.class, rDesc = "操作是否成功", rType = Boolean.class)
	public ApiResult<Boolean> top(@RequestBody SysTopData reqDto, HttpServletRequest request, HttpServletResponse response) {
		SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
		topDataApi.top(currentUserInfo.getDomainId(), reqDto.getTopDataId(), reqDto.getTopDataType());
		return ApiResult.ok(this).data(true);
	}

}
