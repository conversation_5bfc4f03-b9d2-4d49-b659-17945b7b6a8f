package com.uinnova.product.eam.model.vo;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

@Data
public class EamArtifactVoCrush {

    @Comment("分类id")
    private Long id;
    @Comment("分类名称")
    private String name;
    @Comment("分类classCode")
    private String classCode;

    /**
     * 是否启用
     */
    private Boolean viewFlag;

    /**
     * 显示名称
     */
    private String viewName;
    /**
     * 制品配置图例
     */
    private String imgFullName;
    /**
     * 架构元素默认图例
     */
    private String shape;
    private Integer orderNo;
    private String icon;
    private Integer viewNumber;
    private String type;
    private Boolean isChecked;
    private String relation;
    private String unique;
    private Boolean assetsFlag;
    private String viewFullName;
    private String viewIcon;
}
