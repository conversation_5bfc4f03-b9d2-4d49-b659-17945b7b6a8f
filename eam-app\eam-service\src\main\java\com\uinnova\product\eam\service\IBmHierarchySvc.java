package com.uinnova.product.eam.service;

import com.uinnova.product.eam.comm.model.es.EamHierarchy;
import com.uinnova.product.eam.model.dto.EamHierarchyDto;
import com.uinnova.product.eam.model.dto.NavigationModelDto;

import java.util.List;

/**
 * 层级配置接口
 * <AUTHOR>
 * @date 2022/1/6
 */
public interface IBmHierarchySvc {

    /**
     * 层级配置信息保存更新
     * @param cdt 层级配置参数
     * @return
     */
    Long saveOrUpdate(EamHierarchyDto cdt);

    /**
     * 根据层级查询配置信息
     * @param level 层级level
     * @param domainId 域
     * @return
     */
    List<EamHierarchyDto> queryByLvl(Integer level, Long domainId);

    /**
     * 通过模型树id及层级查询
     * @param level 层级level
     * @param modelId 模型树id
     * @param domainId 域
     * @return
     */
    EamHierarchyDto queryByLvlAndModelId(Integer level, Long modelId, Long domainId);

    /**
     * 层级配置查询
     * @param cdt 层级模型查询参数
     * @return
     */
    List<EamHierarchyDto> queryList(EamHierarchy cdt);

    /**
     * 通过模型id查询
     * @param modeId 模型树id
     * @return
     */
    List<EamHierarchyDto> queryByModelId(Long modeId);

    /**
     * 通过模型ids查询
     * @param modeIds 模型树ids
     * @return
     */
    List<EamHierarchyDto> queryByModelIds(List<Long> modeIds);

    /**
     * 根据主键删除层级配置及包含层级
     * @param id 层级信息主键
     * @return
     */
    Integer deleteById(Long id);

    /**
     * 去除校验删除层级配置信息
     * @param id
     * @return 执行状态
     */
    Integer deleteNoVerifyById(Long id);

    /**
     * 根据层级查询模型树下的配置信息
     * @param level 层级level
     * @param domainId 域
     * @param modelId 模型树id
     * @return 单个模型树
     */
    EamHierarchy queryModeHierarchyByLvl(Integer level, Long domainId,Long modelId);

    /**
     * 批量保存或修改层级信息
     * @param hierarchyList 多条模型树信息
     * @return
     */
    Integer saveOrUpdateBatch( List<EamHierarchy> hierarchyList);

    /**
     * 通过视图查询层级配置
     * @param diagramId 视图id
     * @return
     */
    List<EamHierarchyDto> queryByDiagramId(String diagramId);

    /**
     * 校验引导页图片使用
     * @param name
     * @return
     */
    boolean checkPictureUse(String name);

    /**
     *  获取建模层级导航信息列表
     * @param state todo = 待创建 done = 已创建
     * @return 层级导航信息列表
     */
    List<NavigationModelDto> getHierarchyNavigation(String state);

    /**
     *  根据视图id获取模型层级导航信息层级
     * @param diagramId
     * @return 层级导航信息
     */
    NavigationModelDto getHierarchyNavigationByDiagramId(String diagramId);

}
