package com.uino.bean.cmdb.base;

import java.io.Serializable;
import java.util.Map;

import com.uinnova.product.vmdb.comm.model.ci.CcCi;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * ES--CI类
 * 
 * <AUTHOR>
 */
@ApiModel(value="CI对象类",description = "CI对象信息")
public class ESCIInfo extends CcCi implements Serializable {

    /**
     * 
     */
    private static final long serialVersionUID = -3266085107590927597L;

    @ApiModelProperty(value="版本",example = "1")
    private Long version;

    /**
     * CI属性
     */
    @ApiModelProperty(value="对象属性")
    private Map<String, Object> attrs;

    /**
     * DCV特有属性
     */
    @ApiModelProperty(value="DCV特有属性")
    private Map<String, String> dcvExtAttrs;

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }

    public Map<String, Object> getAttrs() {
        return attrs;
    }

    public void setAttrs(Map<String, Object> attrs) {
        this.attrs = attrs;
    }

    public Map<String, String> getDcvExtAttrs() {
        return dcvExtAttrs;
    }

    public void setDcvExtAttrs(Map<String, String> dcvExtAttrs) {
        this.dcvExtAttrs = dcvExtAttrs;
    }

    public ESCIInfo ciAttrformat() {
        // Map<String, Object> attrs2 = this.getAttrs();
        // Iterator<String> it = attrs2.keySet().iterator();
        // while (it.hasNext()) {
        // String key = it.next();
        // Object val = attrs2.get(key);
        // // if (!(val instanceof String)) {
        // // attrs2.put(key, JSON.toString(attrs2.get(key)).trim());
        // // }
        // }
        return this;
    }
}
