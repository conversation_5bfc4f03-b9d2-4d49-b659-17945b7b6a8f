package com.uinnova.product.eam.db.impl;


import com.binary.core.util.BinaryUtils;
import com.uinnova.product.eam.comm.model.CVcDiagramDir;
import com.uinnova.product.eam.comm.model.VcDiagramDir;
import com.uinnova.product.eam.db.VcDiagramDirDao;
import com.uinnova.product.eam.db.support.dao.mybatis.ComMyBatisBinaryDaoImpl;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 视图目录表[VC_DIAGRAM_DIR]数据访问对象实现
 */
public class VcDiagramDirDaoImpl extends ComMyBatisBinaryDaoImpl<VcDiagramDir, CVcDiagramDir> implements VcDiagramDirDao {

//    @Override
//    public String getTableName() {
//        return "IAMS." + getDaoDefinition().getTableName();
//    }

    @Override
    public VcDiagramDir getDiagramDirByEsSysId(Long esSysId) {
        BinaryUtils.checkEmpty(esSysId, "esSysId");
        return getSqlSession().selectOne(getTableName() + ".getDiagramDirByEsSysId", esSysId);
    }

    /**
     * 查找给定系统id对应的文件夹id
     *
     * @param systemIdList
     * @return
     */
    @Override
    public List<Long> findDirIdListByEsSysIdList(List<Long> systemIdList) {
        if (BinaryUtils.isEmpty(systemIdList)) {
            return Collections.emptyList();
        }
        return getSqlSession().selectList(getTableName() + ".findDirIdListByEsSysIdList", systemIdList);
    }

    @Override
    public List<Long> findDirIdList(Integer dirType, List<Long> dirIds) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("dirType", dirType);
        map.put("dirIds", dirIds);
        return getSqlSession().selectList(getTableName() + ".findDirIdList", map);
    }
}


