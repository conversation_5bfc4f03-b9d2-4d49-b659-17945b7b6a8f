package com.uinnova.product.eam.service;

import com.binary.jdbc.Page;
import com.uinnova.product.eam.base.diagram.model.ESDiagram;
import com.uinnova.product.eam.comm.model.es.EamArtifactElement;
import com.uinnova.product.eam.model.EamArtifactElementVo;
import com.uinnova.product.eam.model.dto.ArtifactElementDto;
import com.uinnova.product.eam.model.dto.ElementDto;
import com.uinnova.product.eam.model.vo.CiClassRltVo;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR> wangchunlei
 * @Date : 2021/11/12 18:35
 * @description : 新增制品-图例分栏业务实现
 **/
public interface IEamArtifactColumnSvc {

    /**
     * <AUTHOR> wangchunlei
     * @Date : 2021/11/12 18:26
     * @description :新增制品-图例分栏信息添加/修改
     * @Param : 制品分栏信息
     * @Return : integer
     **/

    Integer saveOrUpdate(ArtifactElementDto elements);

    /**
     * 批量保存更新分栏信息
     * @param elements 分栏信息
     * @return 更新结果
     */
    Integer saveOrUpdateBatch(List<EamArtifactElement> elements);

    /**
     * 通过制品id及分类查询
     * @param artifactId 制品id
     * @param types 类型（可空）
     */
    List<EamArtifactElementVo> queryByArtifactId(Long artifactId, List<Integer> types);

    /**
     * 通过制品id及分类查询,不加url前缀
     * @param artifactId 制品id
     * @param types 类型（可空）
     */
    List<EamArtifactElement> queryByArtifactIdWithoutUrl(Long artifactId, List<Integer> types);

    /**
     * <AUTHOR> wangchunlei
     * @Date : 2021/11/12 18:40
     * @description : 根据制品id查询分栏信息
     * @Param ：制品类型id
     * @Return : 制品分栏信息的list
     **/

    List<EamArtifactElementVo> queryAllColumns(ElementDto elementDto);

    /**
     * 根据ids批量查询
     *
     * @param artifactIds
     * @return
     */
    Map<Long, List<EamArtifactElementVo>> queryAllColumnsByIds(List<Long> artifactIds);


    /**
     * @param :
     * <AUTHOR> wcl
     * @Date : 2022/2/21 16:00
     * @description : 去元模型中查询对象之间的关系
     * @Return :
     **/
    List<CiClassRltVo> queryVisualModelRelation(List<Long> ciClassIds);

    List<CiClassRltVo> queryVisualModelRelationByOrgId(List<Long> ciClassIds, List<Long> orgIds);

    /**
     * 根据制品id获取制品模板信息
     *
     * @param artifactId
     * @param pageNum
     * @param pageSize
     * @return
     */
    Page<ESDiagram> queryTemplateByArtifactId(Long artifactId, Integer pageNum, Integer pageSize, String like);

    /**
     * 刷字表数据
     *
     * @return
     */
    Integer refreshData();

    /**
     * 根据制品id和类型批量获取分类信息
     *
     * @param artifactIds
     * @param types
     * @return
     */
    Map<Long, List<EamArtifactElement>> queryByIdsAndType(List<Long> artifactIds, List<Integer> types);

    List<Map<String, Object>> getArchSystemList(Long artifactId);

    /**
     * 获取制品中配置的全部分类id
     * @param artifactId 制品类型id
     * @return 对象分类id
     */
    List<Long> getArtifactClassIds(Long artifactId);

    /**
     * 获取制品中配置的全部分类id
     * @param artifactId 制品类型id
     * @param types 分栏
     * @return 对象分类id
     */
    List<Long> getArtifactClassByType(Long artifactId, List<Integer> types);
}
