package com.uinnova.product.eam.service.impl;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

import jakarta.annotation.Resource;

import com.alibaba.fastjson.JSONArray;
import com.binary.core.util.BinaryUtils;
import com.uinnova.product.eam.model.FlowOperationStatisticsDto;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.binary.jdbc.Page;
import com.uinnova.product.eam.comm.model.es.FlowProcessSystemPublishHistory;
import com.uinnova.product.eam.model.VcCiRltInfo;
import com.uinnova.product.eam.model.enums.FlowSystemType;
import com.uinnova.product.eam.service.FlowStatisticsReportService;
import com.uinnova.product.eam.service.ICIRltSwitchSvc;
import com.uinnova.product.eam.service.ICISwitchSvc;
import com.uinnova.product.eam.service.asset.BmConfigSvc;
import com.uinnova.product.eam.service.es.ActivityOperationDao;
import com.uinnova.product.eam.service.es.FlowProcessSystemPublishHistoryDao;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClass;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uino.api.client.sys.IDictionaryApiSvc;
import com.uino.bean.cmdb.base.ESCIClassInfo;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.base.LibType;
import com.uino.bean.cmdb.query.ESAttrBean;
import com.uino.bean.cmdb.query.ESCIClassSearchBean;
import com.uino.bean.cmdb.query.ESCISearchBean;
import com.uino.bean.sys.base.ESDictionaryItemInfo;
import com.uino.bean.sys.query.ESDictionaryItemSearchBean;
import com.uino.service.cmdb.microservice.ICIClassSvc;

import lombok.extern.slf4j.Slf4j;

/**
 * 流程管理/统计报表服务层实现
 *
 * <AUTHOR>
 * @since 2024/12/4 下午14:07
 */
@Slf4j
@Service
public class FlowStatisticsReportServiceImpl implements FlowStatisticsReportService{
    @Resource
    private ICIClassSvc iciClassSvc;
    
    @Resource
    ICISwitchSvc ciSwitchSvc;
    
    @Resource
    private ICIRltSwitchSvc iciRltSwitchSvc;
    
    @Resource
    private FlowProcessSystemPublishHistoryDao flowProcessSystemPublishHistoryDao;
    
    @Resource
    private IDictionaryApiSvc dictionaryApiSvc;
    
    @Autowired
    private ActivityOperationDao activityOperationDao;

    @Resource
    private BmConfigSvc bmConfigSvc;

    private final static String ACTIVITY_MAPPING = "ACTIVITY_MAPPING_RELATIONSHIP";
	
	@Override
	public Map<String, Object> getFlowDataStatistics() {
		Map<String, Object> map = new LinkedHashMap<>();
        //查询流程组,流程,指标,风险,场景的classId
        CCcCiClass cCcCiClass = new CCcCiClass();
        cCcCiClass.setClassCodes(new String[]{FlowSystemType.FLOW.getFlowSystemTypeName()
                , FlowSystemType.SUB_FLOW.getFlowSystemTypeName(),"指标","风险","场景","档案"});
        ESCIClassSearchBean esciClassSearchBean = new ESCIClassSearchBean();
        esciClassSearchBean.setCdt(cCcCiClass);
       
        List<ESCIClassInfo> esciClassInfos = iciClassSvc.queryESCiClassInfoListBySearchBean(esciClassSearchBean);
        List<Long> ids = esciClassInfos.stream().map(ESCIClassInfo::getId).collect(Collectors.toList());
        Map<String, ESCIClassInfo> classMap = esciClassInfos.stream().collect(Collectors.toMap(ESCIClassInfo::getClassCode, each -> each));
        //查询出来所有流程组和流程、指标,风险,场景的数据
        List<ESCIInfo> allFlowList = getESCIInfoList(null, ids);
        //分类筛选
        //所有流程组数据
        List<ESCIInfo> rootFlowList = allFlowList.stream().filter(item->classMap.get(FlowSystemType.FLOW.getFlowSystemTypeName()).getId().equals(item.getClassId())).collect(Collectors.toList());
        //所有流程数据
        List<ESCIInfo> flowList = allFlowList.stream().filter(item->classMap.get(FlowSystemType.SUB_FLOW.getFlowSystemTypeName()).getId().equals(item.getClassId())).collect(Collectors.toList());
        //所有指标数据
        List<ESCIInfo> zbList = allFlowList.stream().filter(item->classMap.get("指标").getId().equals(item.getClassId())).collect(Collectors.toList());
        //所有风险数据
        List<ESCIInfo> fxList = allFlowList.stream().filter(item->classMap.get("风险").getId().equals(item.getClassId())).collect(Collectors.toList());
        //所有场景数据
        List<ESCIInfo> cjList = allFlowList.stream().filter(item->classMap.get("场景").getId().equals(item.getClassId())).collect(Collectors.toList());
        //所有档案数据
        List<ESCIInfo> daList = allFlowList.stream().filter(item->classMap.get("档案").getId().equals(item.getClassId())).collect(Collectors.toList());
        //过滤业务域数据
        List<Long> collect = rootFlowList.stream().filter(item->("业务域").equals(item.getAttrs().get("流程级别"))).map(ESCIInfo::getId).collect(Collectors.toList());
        map.put("业务域数量", collect.size());
        map.put("流程组数量", rootFlowList.size() - collect.size());
        map.put("流程数量", flowList.size());
        map.put("指标数量", zbList.size());
        map.put("风险数量", fxList.size());
        map.put("场景数量", cjList.size());
        map.put("档案数量", daList.size());
        return map;
	}
	
	@Override
	public List<Map<String, Object>> getFlowNumStatistics() {
		//查询流程组,流程的classId
        CCcCiClass cCcCiClass = new CCcCiClass();
        cCcCiClass.setClassCodes(new String[]{FlowSystemType.FLOW.getFlowSystemTypeName(),
        		FlowSystemType.SUB_FLOW.getFlowSystemTypeName()});
        ESCIClassSearchBean esciClassSearchBean = new ESCIClassSearchBean();
        esciClassSearchBean.setCdt(cCcCiClass);
       
        List<ESCIClassInfo> esciClassInfos = iciClassSvc.queryESCiClassInfoListBySearchBean(esciClassSearchBean);
        Map<String, ESCIClassInfo> classMap = esciClassInfos.stream().collect(Collectors.toMap(ESCIClassInfo::getClassCode, each -> each));
        //查询出来所有流程组 业务域的数据
        ESAttrBean esAttrBean = new ESAttrBean();
        esAttrBean.setKey("流程级别");
        esAttrBean.setValue("业务域");
        esAttrBean.setOptType(1);
        List<ESCIInfo> rootFlowList = getESCIInfoListByRoot(esAttrBean, Collections.singletonList(classMap.get(FlowSystemType.FLOW.getFlowSystemTypeName()).getId()));
		
        //查询出来业务域中所有关联数据
        CcCiClassInfo rltClass = iciRltSwitchSvc.getRltClassByCode("包含");
        
        List<Long> targetClassList = new ArrayList<>();
        for (ESCIClassInfo esciClassInfo : esciClassInfos) {
            targetClassList.add(esciClassInfo.getId());
        }
        List<Long> rltClassIds = Collections.singletonList(rltClass.getCiClass().getId());
        List<Map<String, Object>> reList = new ArrayList<Map<String, Object>>();
        List<Long> ids = rootFlowList.stream().map(ESCIInfo::getId).collect(Collectors.toList());

        if(CollectionUtils.isEmpty(ids)){
            return new ArrayList<>();
        }

        List<VcCiRltInfo> queryUpAndDownRlts = iciRltSwitchSvc.queryUpAndDownRlts(LibType.DESIGN, ids,
        		targetClassList, rltClassIds, 0, 6, false);
        
        Map<Long, List<VcCiRltInfo>> collect = queryUpAndDownRlts.stream().collect(Collectors.groupingBy(rlt -> rlt.getSourceCiInfo().getCi().getId()));
        for (ESCIInfo eSCIInfo : rootFlowList) {
        	Map<String, Object> map = new LinkedHashMap<>();
        	map.put("x", eSCIInfo.getAttrs().get("流程组名称"));
        	Long parentCiId = eSCIInfo.getId();
        	List<VcCiRltInfo> all = new ArrayList<VcCiRltInfo>();
            List<VcCiRltInfo> vcCiRltInfos1 = collect.get(parentCiId);
            if (!CollectionUtils.isEmpty(vcCiRltInfos1)) {
                addVcCiRltInfoList(collect,all,vcCiRltInfos1);
            	all.addAll(vcCiRltInfos1);
            }
            List<VcCiRltInfo> collect2 = all.stream().filter(item->classMap.get(FlowSystemType.SUB_FLOW.getFlowSystemTypeName()).getId().equals(item.getTargetCiInfo().getCi().getClassId())).collect(Collectors.toList());
        	map.put("y", collect2.size());
        	reList.add(map);
        
		}
		return reList;
	}
	@Override
	public List<Map<String, Object>> getFlowPerformanceNumStatistics(){
		//查询流程组,流程，指标的classId
        CCcCiClass cCcCiClass = new CCcCiClass();
        cCcCiClass.setClassCodes(new String[]{FlowSystemType.FLOW.getFlowSystemTypeName(),
        		FlowSystemType.SUB_FLOW.getFlowSystemTypeName(),"指标"});
        ESCIClassSearchBean esciClassSearchBean = new ESCIClassSearchBean();
        esciClassSearchBean.setCdt(cCcCiClass);
       
        List<ESCIClassInfo> esciClassInfos = iciClassSvc.queryESCiClassInfoListBySearchBean(esciClassSearchBean);
        Map<String, ESCIClassInfo> classMap = esciClassInfos.stream().collect(Collectors.toMap(ESCIClassInfo::getClassCode, each -> each));
        //查询出来所有流程组 业务域的数据
        ESAttrBean esAttrBean = new ESAttrBean();
        esAttrBean.setKey("流程级别");
        esAttrBean.setValue("业务域");
        esAttrBean.setOptType(1);
        List<ESCIInfo> rootFlowList = getESCIInfoListByRoot(esAttrBean, Collections.singletonList(classMap.get(FlowSystemType.FLOW.getFlowSystemTypeName()).getId()));
		
        List<ESCIInfo> zbAllList = getESCIInfoList(null, Collections.singletonList(classMap.get("指标").getId()));
        
        Map<Long, List<ESCIInfo>> zbMap = zbAllList.stream().collect(Collectors.groupingBy(rlt -> JSON.parseArray((String) rlt.getAttrs().get("所属末级流程")).getJSONObject(0).getLong("ciCode")));

        //查询出来业务域中所有关联数据
        CcCiClassInfo rltClass = iciRltSwitchSvc.getRltClassByCode("包含");
        List<Long> rltClassIds = Collections.singletonList(rltClass.getCiClass().getId());


        List<Long> targetClassListByFlow = new ArrayList<>();
        targetClassListByFlow.add(classMap.get(FlowSystemType.FLOW.getFlowSystemTypeName()).getId());
        targetClassListByFlow.add(classMap.get(FlowSystemType.SUB_FLOW.getFlowSystemTypeName()).getId());

        List<Map<String, Object>> reList = new ArrayList<>();
        List<Long> ids = rootFlowList.stream().map(ESCIInfo::getId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(ids)){
            return new ArrayList<>();
        }
        List<VcCiRltInfo> queryUpAndDownRlts = iciRltSwitchSvc.queryUpAndDownRlts(LibType.DESIGN, ids,
        		targetClassListByFlow, rltClassIds, 0, 8, false);

        Map<Long, List<VcCiRltInfo>> collect = queryUpAndDownRlts.stream().collect(Collectors.groupingBy(rlt -> rlt.getSourceCiInfo().getCi().getId()));
        for (ESCIInfo eSCIInfo : rootFlowList) {
        	Map<String, Object> map = new LinkedHashMap<>();
        	map.put("x", eSCIInfo.getAttrs().get("流程组名称"));
        	Long parentCiId = eSCIInfo.getId();
        	List<ESCIInfo> all = new ArrayList<>();
            List<VcCiRltInfo> vcCiRltInfos1 = collect.get(parentCiId);
            List<ESCIInfo> zbList = zbMap.get(parentCiId);
            if(!CollectionUtils.isEmpty(zbList)) {
           	 all.addAll(zbList);
            }
            if (!CollectionUtils.isEmpty(vcCiRltInfos1)) {
            	addZbList(collect,all,vcCiRltInfos1,zbMap);
            }
        	map.put("y", all.size());
        	reList.add(map);
        
		}
		return reList;
	}

	@Override
	public Map<String,  Map<String, String>> getFlowActivityNumData(){
		//查询流程的classId
        CCcCiClass cCcCiClass = new CCcCiClass();
        cCcCiClass.setClassCodes(new String[]{FlowSystemType.SUB_FLOW.getFlowSystemTypeName(),"活动"});
        ESCIClassSearchBean esciClassSearchBean = new ESCIClassSearchBean();
        esciClassSearchBean.setCdt(cCcCiClass);
       
        List<ESCIClassInfo> esciClassInfos = iciClassSvc.queryESCiClassInfoListBySearchBean(esciClassSearchBean);
        Map<String, ESCIClassInfo> classMap = esciClassInfos.stream().collect(Collectors.toMap(ESCIClassInfo::getClassCode, each -> each));
        //查询出来所有流程组 业务域的数据
        List<ESCIInfo> flowList = getESCIInfoList(null, Collections.singletonList(classMap.get(FlowSystemType.SUB_FLOW.getFlowSystemTypeName()).getId()));
		//查询流程关联活动
        CcCiClassInfo rltClass = iciRltSwitchSvc.getRltClassByCode("包含");
        List<Long> targetClassList = new ArrayList<>();
        targetClassList.add(classMap.get("活动").getId());
        List<Long> rltClassIds = Collections.singletonList(rltClass.getCiClass().getId());
        List<Long> ids = flowList.stream().map(ESCIInfo::getId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(ids)){
            return new HashMap<>();
        }
        List<VcCiRltInfo> queryUpAndDownRlts = iciRltSwitchSvc.queryUpAndDownRlts(LibType.DESIGN, ids,
        		targetClassList, rltClassIds, 0, 1, false);
        Map<Long, List<VcCiRltInfo>> collect = queryUpAndDownRlts.stream().collect(Collectors.groupingBy(rlt -> rlt.getSourceCiInfo().getCi().getId()));
        Map<String, Integer> numMap = new LinkedHashMap<>(); 
        numMap.put("活动数<10", 0);
        numMap.put("活动数10-20", 0);
        numMap.put("活动数>20", 0);
        for (ESCIInfo eSCIInfo : flowList) {
        	Long id = eSCIInfo.getId();
        	List<VcCiRltInfo> list = collect.get(id);
        	if(!CollectionUtils.isEmpty(list)) {
        		int size = list.size();
            	if(size <= 10) {
            		numMap.put("活动数<10", numMap.get("活动数<10") + 1);
            	} else if (10 <= size && size <= 20) {
            		numMap.put("活动数10-20", numMap.get("活动数10-20") + 1);
            	} else {
            		numMap.put("活动数>20", numMap.get("活动数>20") + 1);
            	}
        	}else {
        		numMap.put("活动数<10", numMap.get("活动数<10") + 1);
        	}
		}
        //转为百分比
        int total = 0;
        for (Integer part : numMap.values()) {
            total += part;
        }
        Map<String,  Map<String, String>> percentages = new LinkedHashMap<>();
        for (Map.Entry<String, Integer> entry : numMap.entrySet()) {
			String key = entry.getKey();
			Integer value = entry.getValue();			
			String percentageStr =total==0?"0%":String.format("%.2f%%", ((value * 100.0) / total));
	        // 如果小数点后为零，去掉小数点和小数部分
	        if (percentageStr.endsWith(".00%")) {
	        	percentageStr = percentageStr.substring(0, percentageStr.length() - 4) + "%";
	        }
            Map<String, String> data = new LinkedHashMap<>();
            data.put("num", value+"");
            data.put("percentage", percentageStr);
            percentages.put(key, data);
		}
		return percentages;
	}
	
	
	@Override
	public Map<String, Object> getFlowRatioStatistics() {
        Map<String, Object> map = new LinkedHashMap<>(); 

		//查询流程组,流程的classId
        CCcCiClass cCcCiClass = new CCcCiClass();
        cCcCiClass.setClassCodes(new String[]{FlowSystemType.FLOW.getFlowSystemTypeName(),
        		FlowSystemType.SUB_FLOW.getFlowSystemTypeName(),"活动"});
        ESCIClassSearchBean esciClassSearchBean = new ESCIClassSearchBean();
        esciClassSearchBean.setCdt(cCcCiClass);
       
        List<ESCIClassInfo> esciClassInfos = iciClassSvc.queryESCiClassInfoListBySearchBean(esciClassSearchBean);
        Map<String, ESCIClassInfo> classMap = esciClassInfos.stream().collect(Collectors.toMap(ESCIClassInfo::getClassCode, each -> each));
        
        ESDictionaryItemSearchBean esDictionaryItemSearchBean = new ESDictionaryItemSearchBean();
        esDictionaryItemSearchBean.setDictName("流程级别");
        Page<ESDictionaryItemInfo> esDictionaryItemInfoPage = dictionaryApiSvc.searchDictItemPageByBean(esDictionaryItemSearchBean);
        if (CollectionUtils.isEmpty(esDictionaryItemInfoPage.getData())) {
            throw new RuntimeException("流程级别字典不存在");
        }
        //查询第三层级别名称（默认为业务域）
        String rootFlowLevel = "业务域";
        for (ESDictionaryItemInfo datum : esDictionaryItemInfoPage.getData()) {
            if (datum.getAttrs().get("流程级别ID").equalsIgnoreCase("3")) {
                rootFlowLevel = datum.getAttrs().get("流程级别名称");
            }
        }
        //查询第三级流程组
        ESAttrBean esAttrBean = new ESAttrBean();
        esAttrBean.setKey("流程级别");
        esAttrBean.setValue(rootFlowLevel);
        esAttrBean.setOptType(1);
        List<ESCIInfo> threeLevelFlowList = getESCIInfoList(esAttrBean, Collections.singletonList(classMap.get(FlowSystemType.FLOW.getFlowSystemTypeName()).getId()));
        List<Long> ids = threeLevelFlowList.stream().map(ESCIInfo::getId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(ids)){
            return new HashMap<>();
        }
        //查询出来三级流程组下的末级流程
        CcCiClassInfo rltClass = iciRltSwitchSvc.getRltClassByCode("包含");
        List<Long> rltClassIds = Collections.singletonList(rltClass.getCiClass().getId());
        List<Long> targetClassListByFlow = new ArrayList<>();
        targetClassListByFlow.add(classMap.get(FlowSystemType.SUB_FLOW.getFlowSystemTypeName()).getId());
        List<VcCiRltInfo> queryUpAndDownRlts = iciRltSwitchSvc.queryUpAndDownRlts(LibType.DESIGN, ids,
        		targetClassListByFlow, rltClassIds, 0, 1, false);
        Map<Long, List<VcCiRltInfo>> collect = queryUpAndDownRlts.stream().collect(Collectors.groupingBy(rlt -> rlt.getSourceCiInfo().getCi().getId()));
        int haveSubFlowNum = 0;
        for (ESCIInfo esciInfo : threeLevelFlowList) {
        	Long id = esciInfo.getId();
        	if(!CollectionUtils.isEmpty(collect.get(id))) {
        		haveSubFlowNum+=1;
        	}
		}
        String flowCoverRatio = threeLevelFlowList.size() == 0?"0%":String.format("%.2f%%", ((haveSubFlowNum * 100.0) / threeLevelFlowList.size()));
        // 如果小数点后为零，去掉小数点和小数部分
        if (flowCoverRatio.endsWith(".00%")) {
        	flowCoverRatio = flowCoverRatio.substring(0, flowCoverRatio.length() - 4) + "%";
        }
        flowCoverRatio = flowCoverRatio.substring(0, flowCoverRatio.length() - 1);
    	// 处理包含小数的字符串
        if (flowCoverRatio.contains(".")) {
            double num1 = Double.parseDouble(flowCoverRatio);
            map.put("flowCoverRatio",num1);
        } else {
            int num1 = Integer.parseInt(flowCoverRatio);
            map.put("flowCoverRatio",num1);
        }
        //查询所有活动
        List<ESCIInfo> activityList = getESCIInfoList(null, Collections.singletonList(classMap.get("活动").getId()));
        int informatizationNum = 0;
        for (ESCIInfo esciInfo : activityList) {
        	Map<String, Object> attrs = esciInfo.getAttrs();
        	if(attrs.get("关联应用")!=null) {
        		informatizationNum +=1;
        	}
		}
        String informatizationRatio = activityList.size() == 0?"0%":String.format("%.2f%%", ((informatizationNum * 100.0) / activityList.size()));
        // 如果小数点后为零，去掉小数点和小数部分
        if (informatizationRatio.endsWith(".00%")) {
        	informatizationRatio = informatizationRatio.substring(0, informatizationRatio.length() - 4) + "%";
        }
    	informatizationRatio = informatizationRatio.substring(0, informatizationRatio.length() - 1);
    	// 处理包含小数的字符串
        if (informatizationRatio.contains(".")) {
            double num1 = Double.parseDouble(informatizationRatio);
            map.put("informatizationRatio",num1);
        } else {
            int num1 = Integer.parseInt(informatizationRatio);
            map.put("informatizationRatio",num1);
        }
		return map;
	}
	@Override
	public List<Map<String, Map<String, Integer>>> getFlowSituationStatistics(){
		 // 获取当前年份
        int currentYear = LocalDate.now().getYear();
 
        // 将当前年份转换为 YYYYMMDD 格式的开始和结束时间
        String startOfYear = currentYear + "0101000000"; // 当前年的第一天
        String endOfYear = (currentYear + 1) + "0101000000"; // 下一年的第一天（不包含，所以设置为下一年的开始时间）
 
        // 将字符串转换为长整型
        long startTime = Long.parseLong(startOfYear);
        long endTime = Long.parseLong(endOfYear) - 1; // 减去1是因为我们的结束时间是下一年的第一秒的前一秒
		
		//查询流程组,流程的classId
        CCcCiClass cCcCiClass = new CCcCiClass();
        cCcCiClass.setClassCodes(new String[]{FlowSystemType.FLOW.getFlowSystemTypeName(),
        		FlowSystemType.SUB_FLOW.getFlowSystemTypeName()});
        ESCIClassSearchBean esciClassSearchBean = new ESCIClassSearchBean();
        esciClassSearchBean.setCdt(cCcCiClass);
       
        List<ESCIClassInfo> esciClassInfos = iciClassSvc.queryESCiClassInfoListBySearchBean(esciClassSearchBean);
        Map<String, ESCIClassInfo> classMap = esciClassInfos.stream().collect(Collectors.toMap(ESCIClassInfo::getClassCode, each -> each));
        //查询出来所有流程组 业务域的数据
        ESAttrBean esAttrBean = new ESAttrBean();
        esAttrBean.setKey("流程级别");
        esAttrBean.setValue("业务域");
        esAttrBean.setOptType(1);
        List<ESCIInfo> rootFlowList = getESCIInfoListByRoot(esAttrBean, Collections.singletonList(classMap.get(FlowSystemType.FLOW.getFlowSystemTypeName()).getId()));
		//末级流程
        List<ESCIInfo> flowList = getESCIInfoList(null, Collections.singletonList(classMap.get(FlowSystemType.SUB_FLOW.getFlowSystemTypeName()).getId()));
        //流程发布记录
        List<String> flowCiCodes = flowList.stream().map(ESCIInfo::getCiCode).collect(Collectors.toList());
        List<String> publishTypeList = new ArrayList<String>();
        publishTypeList.add("published");
        publishTypeList.add("submit");
        
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termsQuery("ciCode.keyword",flowCiCodes));
        boolQueryBuilder.must(QueryBuilders.termsQuery("publishType.keyword", publishTypeList));
        RangeQueryBuilder rangeQuery = QueryBuilders.rangeQuery("createTime")
        		.gte(startTime)
        		.lt(endTime);  
        boolQueryBuilder.must(rangeQuery); 
        List<FlowProcessSystemPublishHistory> flowSystemPublishHistory = flowProcessSystemPublishHistoryDao.getListByQuery(boolQueryBuilder);
        
        
        Map<String, List<FlowProcessSystemPublishHistory>> historyMap = flowSystemPublishHistory.stream().collect(Collectors.groupingBy(history -> history.getCiCode()));

        //查询
        //查询出来业务域中所有关联数据
        CcCiClassInfo rltClass = iciRltSwitchSvc.getRltClassByCode("包含");
        
        List<Long> targetClassList = new ArrayList<>();
        for (ESCIClassInfo esciClassInfo : esciClassInfos) {
            targetClassList.add(esciClassInfo.getId());
        }
        List<Long> rltClassIds = Collections.singletonList(rltClass.getCiClass().getId());
        List<Long> ids = rootFlowList.stream().map(ESCIInfo::getId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(ids)){
            return new ArrayList<>();
        }
        List<VcCiRltInfo> queryUpAndDownRlts = iciRltSwitchSvc.queryUpAndDownRlts(LibType.DESIGN, ids,
        		targetClassList, rltClassIds, 0, 6, false);
        
        Map<Long, List<VcCiRltInfo>> collect = queryUpAndDownRlts.stream().collect(Collectors.groupingBy(rlt -> rlt.getSourceCiInfo().getCi().getId()));

        List<Map<String, Map<String, Integer>>> reList = new ArrayList<>();

        for (ESCIInfo eSCIInfo : rootFlowList) {
        	Map<String, Map<String, Integer>> map = new LinkedHashMap<>();
        	Map<String, Integer> dataMap = new LinkedHashMap<>();
        	Long parentCiId = eSCIInfo.getId();
        	List<VcCiRltInfo> all = new ArrayList<VcCiRltInfo>();
            List<VcCiRltInfo> vcCiRltInfos1 = collect.get(parentCiId);
            if (!CollectionUtils.isEmpty(vcCiRltInfos1)) {
                addVcCiRltInfoList(collect,all,vcCiRltInfos1);
            	all.addAll(vcCiRltInfos1);
            }
            int addNum = 0; //新建数量
            int optimizeNum = 0;//优化数量
            int discardNum = 0;//废弃数量
            List<VcCiRltInfo> collect2 = all.stream().filter(item->classMap.get(FlowSystemType.SUB_FLOW.getFlowSystemTypeName()).getId().equals(item.getTargetCiInfo().getCi().getClassId())).collect(Collectors.toList());
            for (VcCiRltInfo flow : collect2) {
            	Map<String, String> attrs = flow.getTargetCiInfo().getAttrs();
            	String ciCode = flow.getTargetCiInfo().getCi().getCiCode();
            	String createTime = attrs.get("创建时间");
            	String status = attrs.get("资产状态");
            	String updateTime = attrs.get("修改时间");
            	if(createTime.contains(currentYear+"")) {
            		addNum+=1;
            	}
            	if(status.equalsIgnoreCase("已作废") && updateTime.contains(currentYear+"")) {
            		discardNum+=1;
            	}
            	if(!CollectionUtils.isEmpty(historyMap.get(ciCode))) {
            		optimizeNum+=1;
            	}
			}
            dataMap.put("addNum", addNum);
            dataMap.put("optimizeNum", optimizeNum);
            dataMap.put("discardNum", discardNum);
            map.put((String) eSCIInfo.getAttrs().get("流程组名称"), dataMap);
            reList.add(map);
		}
		return reList;
	}
	
	
	/**
	 * 查询顶层流程数据-排序
	 * @param esAttrBean
	 * @param classIds
	 * @return
	 */
	private List<ESCIInfo> getESCIInfoListByRoot(ESAttrBean esAttrBean,List<Long> classIds){

        //查询出来所有流程组 业务域的数据
        ESCISearchBean esciSearchBean = new ESCISearchBean();
        if(esAttrBean!=null) {
            esciSearchBean.setAndAttrs(Collections.singletonList(esAttrBean));
        }
        esciSearchBean.setPageNum(0);
        esciSearchBean.setPageSize(3000);
        esciSearchBean.setClassIds(classIds);
        Page<ESCIInfo> rootFlowPage = ciSwitchSvc.searchESCIByBean(esciSearchBean, LibType.DESIGN);
        List<ESCIInfo> list = rootFlowPage.getData();
        Comparator<ESCIInfo> comparator = new Comparator<ESCIInfo>() {
            @Override
            public int compare(ESCIInfo o1, ESCIInfo o2) {

                String version1 = (String)o1.getAttrs().get("流程组编码");
                String version2 = (String)o2.getAttrs().get("流程组编码");

                String[] parts1 = version1.split("\\.");
                String[] parts2 = version2.split("\\.");

                int length = Math.max(parts1.length, parts2.length);

                for (int i = 0; i < length; i++) {
                	long num1 = i < parts1.length ? Long.parseLong(parts1[i]) : 0;
                    long num2 = i < parts2.length ? Long.parseLong(parts2[i]) : 0;

                    if (num1 < num2) {
                        return -1;
                    } else if (num1 > num2) {
                        return 1;
                    }
                }
                // 如果所有部分都相等，则返回0
                return 0;
            }
        };
        // 应用排序
        list.sort(comparator);
		return list;
	}
	/**
	 * 查询流程相关数据（无排序）
	 * @param esAttrBean
	 * @param classIds
	 * @return
	 */
	private List<ESCIInfo> getESCIInfoList(ESAttrBean esAttrBean,List<Long> classIds){
        ESCISearchBean esciSearchBean = new ESCISearchBean();
        if(esAttrBean!=null) {
            esciSearchBean.setAndAttrs(Collections.singletonList(esAttrBean));
        }
        esciSearchBean.setPageNum(0);
        esciSearchBean.setPageSize(3000);
        esciSearchBean.setClassIds(classIds);
        Page<ESCIInfo> rootFlowPage = ciSwitchSvc.searchESCIByBean(esciSearchBean, LibType.DESIGN);
		return rootFlowPage.getData();
	}
	/**
	 * 递归层级添加数据
	 * @param all
	 */
	private void addVcCiRltInfoList(Map<Long, List<VcCiRltInfo>> collect,List<VcCiRltInfo> all,List<VcCiRltInfo> vcCiRltInfos) {
		for (VcCiRltInfo vcCiRltInfo : vcCiRltInfos) {
    		CcCiInfo targetCiInfo = vcCiRltInfo.getTargetCiInfo();
    		 List<VcCiRltInfo> vcCiRltInfos2 = collect.get(targetCiInfo.getCi().getId());
             if (!CollectionUtils.isEmpty(vcCiRltInfos2)) {
                 addVcCiRltInfoList(collect, all, vcCiRltInfos2);
                 all.addAll(vcCiRltInfos2);
             }
		}
	}
	/**
	 * 递归层级添加数据
	 * @param all
	 */
	private void addZbList(Map<Long, List<VcCiRltInfo>> collect,List<ESCIInfo> all,
			List<VcCiRltInfo> vcCiRltInfos,Map<Long, List<ESCIInfo>> zbMap) {
		for (VcCiRltInfo vcCiRltInfo : vcCiRltInfos) {
    		CcCiInfo targetCiInfo = vcCiRltInfo.getTargetCiInfo();
    		 List<VcCiRltInfo> vcCiRltInfos2 = collect.get(targetCiInfo.getCi().getId());
             List<ESCIInfo> zbList = zbMap.get(targetCiInfo.getCi().getId());
             if(!CollectionUtils.isEmpty(zbList)) {
            	 all.addAll(zbList);
             }
             if (!CollectionUtils.isEmpty(vcCiRltInfos2)) {
            	 addZbList(collect, all, vcCiRltInfos2,zbMap);
             }
		}
	}
	@Override
	public List<Map<String, String>> getAllActivityRuningDuration(FlowOperationStatisticsDto dto) {
		try {
		    BoolQueryBuilder query = QueryBuilders.boolQuery();
		    List<Map<String, String>> mapList = activityOperationDao.getActivityRunningTimeTopList(query, 20);
		    getActivity(mapList);
		    return mapList;
		}catch (Exception e) {
        	log.error("获取活动运行时长异常", e.getMessage());
            return Collections.emptyList();
		}
	}
	/**
     * 获取目标流程id
     *
     * @param mapList
     * @return
     */
    private void getActivity(List<Map<String, String>> mapList) {
        String showType = bmConfigSvc.getConfigType(ACTIVITY_MAPPING);
        if (!BinaryUtils.isEmpty(showType) && !BinaryUtils.isEmpty(mapList)) {
            JSONArray array = JSON.parseArray(showType);
            for (Object o : array) {
                JSONObject obj = (JSONObject) o;
                String targetActivity = obj.getString("targetActivity");
                String sourceActivity = obj.getString("sourceActivity");
                for (Map<String, String> map : mapList) {
                    if (targetActivity.equals(map.get("activityName"))) {
                        map.put("activityName", sourceActivity);
                    }
                }
            }
        }
    }
}
