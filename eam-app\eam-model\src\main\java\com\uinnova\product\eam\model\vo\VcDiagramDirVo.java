package com.uinnova.product.eam.model.vo;

import com.uinnova.product.eam.comm.model.VcDiagramDir;
import lombok.Data;

@Data
public class VcDiagramDirVo extends VcDiagramDir {

    // 所在位置
    private String relationLocation;
    // 关联系统
    private String relationSystem;
    // 系统分类
    private String systemType;
    // 所属系统标识
    private Integer relationSysSign;
    // 分类下系统数量
    private Integer systemNum;
    // 是否已关注 0：未关注  1：已关注
    private Integer isAttention;
    // 系统创建时间
    private Long sysCreateTime;
    // 用户姓名
    private String userName;
    // 制品数量
    private Integer diagramNum;
}
