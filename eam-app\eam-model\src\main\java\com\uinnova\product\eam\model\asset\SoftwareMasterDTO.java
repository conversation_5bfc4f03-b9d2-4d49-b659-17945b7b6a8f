package com.uinnova.product.eam.model.asset;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.base.model.CIDataInfo;
import lombok.Data;

import java.util.Collections;
import java.util.List;

@Data
public class SoftwareMasterDTO {


    @Comment("外购软件主信息元素集合")
    private CIDataInfo software;

    @Comment("外购软件版本信息集合")
    private List<CIDataInfo> editionList = Collections.emptyList();

    @Comment("当前标准规范所属最新版本")
    private String editionNo = "0";
}
