package com.uinnova.product.eam.web.diagram.bean;

import com.binary.framework.bean.Condition;
import com.uino.bean.cmdb.query.ESAttrAggBean;

public class CiAttrValueCdt extends ESAttrAggBean implements Condition{

	private static final long serialVersionUID = 1L;

	/**
	 * 分类类型，1：CI；2：关系
	 */
	Integer classType;

	public Integer getClassType() {
		return classType;
	}

	public void setClassType(Integer classType) {
		this.classType = classType;
	}
}
