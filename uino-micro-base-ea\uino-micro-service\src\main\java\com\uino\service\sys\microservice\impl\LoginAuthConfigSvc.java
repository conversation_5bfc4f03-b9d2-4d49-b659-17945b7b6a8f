package com.uino.service.sys.microservice.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import javax.naming.ldap.LdapContext;

import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.MultiMatchQueryBuilder.Type;
import org.elasticsearch.index.query.Operator;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.sort.SortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.binary.core.exception.MessageException;
import com.binary.core.util.BinaryUtils;
import com.binary.jdbc.Page;
import com.uino.dao.sys.ESLoginAuthConfigSvc;
import com.uino.service.sys.microservice.ILoginAuthConfigSvc;
import com.uino.util.sys.LdapUtil;
import com.uino.util.sys.SysUtil;
import com.uino.bean.sys.base.LdapUserMapping;
import com.uino.bean.sys.base.LoginAuthConfig;
import com.uino.bean.sys.base.LoginLdapAuthConfig;
import com.uino.bean.sys.business.ActiveLoginAuthConfigDto;
import com.uino.bean.sys.business.LoginAuthConfigDto;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class LoginAuthConfigSvc implements ILoginAuthConfigSvc {

    protected static final List<SortBuilder<?>> DEFUALT_SORT =
        new ArrayList<>(Arrays.asList(SortBuilders.fieldSort("modifyTime").order(SortOrder.DESC)));

    @Autowired
    ESLoginAuthConfigSvc authConfigDao;

    @Override
    public LoginAuthConfig queryById(Long id) {
        if (id == null) { return null; }
        return authConfigDao.getById(id);
    }

    @Override
    public void activeConfig(ActiveLoginAuthConfigDto active) {
        active.valid();
        LoginAuthConfig authConfig = queryById(active.getId());
        BinaryUtils.checkEmpty(authConfig, "recode");
        authConfig.setProtoStatus(active.isActive() ? 1 : 0);
        saveOrUpdate(authConfig);
    }

    @Override
    public Page<LoginAuthConfig> queryPage(LoginAuthConfigDto authConfigCdt) {
        BoolQueryBuilder query = QueryBuilders.boolQuery();

        if (!BinaryUtils.isEmpty(authConfigCdt.getProtoName())) {
            query.must(QueryBuilders.multiMatchQuery(authConfigCdt.getProtoName(), "protoName").operator(Operator.AND)
                .type(Type.PHRASE_PREFIX).lenient(true));
        }

        if (authConfigCdt.getProtoStatus() != null) {
            query.must(QueryBuilders.termQuery("protoStatus", authConfigCdt.getProtoStatus()));
        }
        try {
            return authConfigDao.getSortListByQuery(authConfigCdt.getPageNum(), authConfigCdt.getPageSize(), query,
                DEFUALT_SORT);
        } catch (Exception e) {
            log.debug(e.getMessage());
            return new Page<LoginAuthConfig>(authConfigCdt.getPageNum(), authConfigCdt.getPageSize(), 1, 1,
                new ArrayList<LoginAuthConfig>());
        }
        // return authConfigDao.getSortListByQuery(authConfigCdt.getPageNum(), authConfigCdt.getPageSize(), query,
        // new ArrayList<SortBuilder<?>>());
    }

    @Override
    public boolean testConnection(LoginAuthConfig authConfig) {
        try {
            LoginLdapAuthConfig lac = authConfig.getLdapAuthConfig();
            if (lac == null) { return false; }
            String username = authConfig.getTestUserName();
            String passwd = SysUtil.EncryptDES.decryptDES(authConfig.getTestPassword());
            LdapUtil ldapUtil = LdapUtil.createTempLdapUtil(lac.getHostName(), lac.getPort(), lac.getBaseDn(),
                BinaryUtils.isEmpty(lac.getUserNameRdnAttr()) ? lac.getUserNameAttr() : lac.getUserNameRdnAttr(),
                lac.getLoginUserDn(), lac.getPassword());
            String userDn = null;
            LdapContext ldapCtx = null;
            try {
                if (ldapUtil.hasBaseUserAndPwd()) {
                    int i1 = lac.getLoginUserDn().indexOf(",");
                    String baseDn = lac.getLoginUserDn().substring(i1 + 1, lac.getLoginUserDn().length());
                    ldapUtil.setDomain(baseDn);

                    ldapCtx = ldapUtil.getLdapContext();
                    String searchDN = ldapUtil.serachDN(ldapCtx, username);
                    userDn = searchDN;
                } else {
                    String rdn = ldapUtil.getSeachAttr();
                    String baseDn = ldapUtil.getDomain();
                    userDn = rdn + "=" + username + "," + baseDn;
                }
                ldapCtx = ldapUtil.getLdapContext(userDn, passwd);
                return ldapCtx != null;
            } finally {
                LdapUtil.closeLdapCtx(ldapCtx);
            }
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public LdapUserMapping testConnectionAndMappingUser(LoginAuthConfig authConfig) {
        if (authConfig == null || authConfig.getLdapAuthConfig() == null) { return null; }
        LoginLdapAuthConfig lac = authConfig.getLdapAuthConfig();
        String username = authConfig.getTestUserName();
        String passwd = SysUtil.EncryptDES.decryptDES(authConfig.getTestPassword());
        LdapUtil ldapUtil = LdapUtil.createTempLdapUtil(lac.getHostName(), lac.getPort(), lac.getBaseDn(),
            BinaryUtils.isEmpty(lac.getUserNameRdnAttr()) ? lac.getUserNameAttr() : lac.getUserNameRdnAttr(),
            lac.getLoginUserDn(), lac.getPassword());
        String userDn = null;
        LdapContext ldapCtx = null;
        try {
            if (ldapUtil.hasBaseUserAndPwd()) {
                int i1 = lac.getLoginUserDn().indexOf(",");
                String baseDn = lac.getLoginUserDn().substring(i1 + 1, lac.getLoginUserDn().length());
                ldapUtil.setDomain(baseDn);

                ldapCtx = ldapUtil.getLdapContext();
                String searchDN = ldapUtil.serachDN(ldapCtx, username);
                userDn = searchDN;
            } else {
                String rdn = ldapUtil.getSeachAttr();
                String baseDn = ldapUtil.getDomain();
                userDn = rdn + "=" + username + "," + baseDn;
            }
            ldapCtx = ldapUtil.getLdapContext(userDn, passwd);
            if (ldapCtx == null) { return null; } // 没连上
            // 查询映射用户
            LdapUserMapping m = null;
            try {
                LdapUserMapping defs = authConfig.getLdapUserMapping();

                Map<String, String> attrMaps = new HashMap<String, String>();
                Map<String, Object> innerMap = ((JSONObject) com.alibaba.fastjson.JSON.toJSON(defs)).getInnerMap();
                Set<Entry<String, Object>> entrySet = innerMap.entrySet();
                for (Entry<String, Object> entry : entrySet) {
                    attrMaps.put(entry.getKey(), String.valueOf(entry.getValue()));
                }

                Set<String> ldapKeys = new HashSet<String>(attrMaps.values());
                Map<String, String> serachInfo = ldapUtil.serachInfo(ldapCtx, username, ldapKeys);
                m = new LdapUserMapping(serachInfo.get(attrMaps.get("userName")),
                    serachInfo.get(attrMaps.get("emailAdress")), serachInfo.get(attrMaps.get("mobileNo")),
                    serachInfo.get(attrMaps.get("notes")), serachInfo.get(attrMaps.get("roles")));
                return m;
            } catch (Exception e) {
                return new LdapUserMapping();
            }
        } catch (Exception e) {
            return null;
        } finally {
            LdapUtil.closeLdapCtx(ldapCtx);
        }
    }

    @Override
    public Long saveOrUpdate(LoginAuthConfig authConfig) {
        authConfig.valid();
        BoolQueryBuilder query =
            QueryBuilders.boolQuery().must(QueryBuilders.termQuery("protoName.keyword", authConfig.getProtoName()));
        if (authConfig.getId() != null) {
            query.mustNot(QueryBuilders.termQuery("id", authConfig.getId()));
        }
        long cTime = System.currentTimeMillis();
        if (authConfig.getId() == null) {
            authConfig.setCreateTime(cTime);
        } else {
            authConfig.setCreateTime(null);
        }
        authConfig.setLastModifyTime(cTime);

        // 唯一名称验证
        long count = authConfigDao.countByCondition(query);
        if (count > 0) { throw MessageException.i18n("COMMON_NAME_CANNOT_BE_REPEATED"); }
        return authConfigDao.saveOrUpdate(authConfig);
    }

    @Override
    public void removeById(Long id) {
        if (id == null) { return; }
        authConfigDao.deleteById(id);
    }

}
