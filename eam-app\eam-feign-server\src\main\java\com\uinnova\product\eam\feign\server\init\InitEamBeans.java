package com.uinnova.product.eam.feign.server.init;

import com.uinnova.product.vmdb.comm.sso.WebDomainGetter;
import com.uino.tarsier.tarsiercom.feign.ITokenGetter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

@Configuration
@Slf4j
public class InitEamBeans {

    public static final String localToken = "a44c11261f90370b798d43702435fabc";

    @Bean
    public ITokenGetter tokenGetter() {
        log.info("注册tarsier-feign-token成功");
        return () -> localToken;
    }

    @Bean
    public WebDomainGetter webDomainGetter() {
        log.info("开始注册web-domain-getter");
        WebDomainGetter webDomainGetter = new WebDomainGetter();
        webDomainGetter.setDomainId(1L);
        log.info("注册web-domain-getter成功");
        return webDomainGetter;
    }

    @Bean
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }

}
