package com.uino.api.client.sys;

import com.binary.jdbc.Page;
import com.uino.bean.sys.base.TenantDomain;

import java.util.List;

/**
 * 租户域
 *
 * <AUTHOR>
public interface ITenantDomainApiSvc {

    /**
     * 保存或更新租户域
     *
     * @param tenantDomain
     * @return
     */
    Long saveOrUpdate(TenantDomain tenantDomain);

    /**
     * 查询租户域信息
     *
     * @param
     * @return
     */
    Page<TenantDomain> queryPage(int pageNum, int pageSize, String name);


    /**
     * 删除租户域
     *
     * @param id
     * @return
     */
    Long deleteById(Long id);

    /**
     * 启用或停用租户域
     *
     * @param id
     * @return
     */
    Long startOrStop(Long id);

    /**
     * 重置域管理员密码
     * @param id
     * @return
     */
    boolean resetPasswdByAdmin(Long id);

    /**
     * 查询可用的域
     * @return
     */
    List<TenantDomain> queryAvailableList();
}
