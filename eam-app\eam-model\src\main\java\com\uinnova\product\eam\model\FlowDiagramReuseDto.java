package com.uinnova.product.eam.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * 末级流程图复用
 * description
 *
 * <AUTHOR>
 * @since 2024/11/14 15:30
 */
@Data
public class FlowDiagramReuseDto {

    @ApiModelProperty(value = "源流程ci code",required = true)
    private String sourceCiCode;

    @ApiModelProperty(value = "目标流程ci code",required = true)
    private String targetCiCode;

    @ApiModelProperty(value = "流程图编号",required = true)
    private String diagramEnergy;

}
