package com.uinnova.product.eam.comm.model.es;

import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.util.List;

@Data
@Comment("操作计数表[UINO_EAM_ATTENTION]")
public class EamAttention implements EntityBean {

    private Long id;

    private Long createTime;

    private Long modifyTime;
    /** 关注的文件夹或视图id */
    private Long attentionId;
    /** 关注的架构类型，1：企业级架构资产，2：系统级架构资产 */
    @Deprecated
    private Integer attentionBuild;
    /** 关注的文件类型：1:文件夹，2：视图, 3：在线方案 4：ci资产 5:矩阵*/
    private Integer attentionType;
    /** 用户id */
    private Long userId;
    /** 特殊视图 0：否  1:是 */
    private Integer specialView;
    /** 所属文件夹id */
    private Long relationDirId;
    /** 关注的文件夹或视图id或方案id */
    private List<Long> attentionIds;
    @Comment("0:设计空间，1:资产仓库")
    private Integer source;
    @Comment("关注id字符串类型，兼容资产ci导入导出id变化")
    private String attentionCode;

    @Comment("配置项id(资产管理配置)")
    private Long configId;

    public EamAttention() {
        this.source = 1;
    }
}
