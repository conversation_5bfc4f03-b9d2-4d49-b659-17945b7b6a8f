#端口
server.port=1536
#服务名称
spring.application.name=tarsier-comm
#服务前置地址
server.servlet.context-path=/tarsier-comm
#权限模块http server path
permission.http.prefix=${PERMISSION_PATH}
#本地资源http服务地址
http.resource.space=${HTTP_RESOURCE}
#本地资源存储地址
local.resource.space = /usr/local/uino_data

#是否开启oauth鉴权
oauth.open=true
#oauth客户端id/资源端id
oauth.client.id=tarsier-comm
#客户端密钥
oauth.client.secret=secret
#请求授权方式
oauth.client.grant_type=authorization_code
#认证中心地址
oauth.server.url=${OAUTH_PATH}

oauth.server.in_url=${OAUTH_INPATH}
#code换token回调地址
oauth.server.token_callback.url=${TOKEN_CALLBACK}
#覆盖同名bean
spring.main.allow-bean-definition-overriding=true
spring.main.lazy-initialization=true

#elasticsearch相关配置
esIps=${ELASTIC_HOST}
isAuth=false
esUser=admin
esPwd=admin


#基础模块服务加载方式，支持local/rpc
base.load-type=local
eureka.client.enabled=false

spring.servlet.multipart.max-file-size=500MB
spring.servlet.multipart.max-request-size=500MB
server.tomcat.basedir=.
#同步请求超时时间，单位毫秒
spring.mvc.async.request-timeout=1000000
#连接超时时间-1为不限制
server.connection-timeout=-1

logging.level.root=INFO
logging.level.org.springframework.boot=INFO
logging.config=classpath:log4j.properties

#对象管理勾选业务主键数量限制
uino.base.ci.primarykey.maxcount=10
#日志保留时长，单位：天
uino.log.clear.duration=7

#忽略授权认证的地址
license.ignore.filter.pattern=**.mp3;**.mp4;**.wav;**.js;**.css;**.jpg;**.jpeg;**.bmp;**.gif;**.png;**.ico;**.swf;**.eot;**.svg;**.ttf;**.woff;**.woff2;**.htm;**.html;**.txt;**.xml;**.json;**.map;/license/auth/**;/redirectAuth;/getTokenByCode;/permission/user/getCurrentUser;/permission/module/getModuleTree;/sys/getLogos;/**;
#授权跳转地址
project.license.register.url=http://*************/examples/#/license

kpi.units=度,斤