package com.uinnova.product.eam.model.dm;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.comm.model.ci.CcCiClass;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据建模复制实体返回体
 * <AUTHOR>
 */
@Data
public class DataModelCopyCiInfo {
    @Comment("继承节点集合：实体ciCode-继承属性list")
    private Map<CcCiInfo, List<CcCiInfo>> entityMap = new HashMap<>();


    @Comment("实体属性的属性定义")
    private List<CcCiAttrDef> attributeDefs;
    @Comment("实体属性所属分类")
    private CcCiClass attributeCiClass;

    public DataModelCopyCiInfo() {
    }

    public DataModelCopyCiInfo(CcCiClass attributeCiClass, List<CcCiAttrDef> attributeDefs) {
        this.attributeDefs = attributeDefs;
        this.attributeCiClass = attributeCiClass;
    }
}
