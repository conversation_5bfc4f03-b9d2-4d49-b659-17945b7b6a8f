package com.uinnova.product.eam.model.cj.vo;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.comm.model.es.EamDiagramDir;
import com.uinnova.product.eam.model.vo.EamDiagramDirVo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Classname ESDiagramDirPlanVO
 * @Date 2022/3/18 15:59
 */
@Data
public class ESDiagramDirPlanVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @Comment("当前文件夹信息")
    private EamDiagramDir diagramDir;

    @Comment("子文件夹信息")
    private List<EamDiagramDirVo> childrenDirs;

    @Comment("子文件夹下是否有节点")
    private Map<Long,Boolean> dirHasNodeMap;

    @Comment("子文件夹视图数量信息")
    private Map<Long,Integer> dirDiagramCountMap;

    private List<PublishedDiagramPlanVO> publishedDiagramPlanList;

    private Integer attentionCount;

   /* @Comment("视图信息，不分页时使用")
    private List<ESSimpleDiagramDTO> diagramInfos;
    private List<PlanDesignInstance> planDesignInstance;*/
}
