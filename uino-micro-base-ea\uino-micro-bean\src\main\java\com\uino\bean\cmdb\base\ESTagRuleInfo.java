package com.uino.bean.cmdb.base;

import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.util.Assert;

import com.binary.core.util.BinaryUtils;
import com.binary.framework.bean.annotation.Comment;
import com.uino.bean.permission.business.IValidDto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 
 * <AUTHOR>
 *
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value="标签定义详情类",description = "标签定义详情信息")
public class ESTagRuleInfo implements Serializable, IValidDto {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value="id",example = "123")
    @Comment("id")
    private Long id;

    @ApiModelProperty(value="标签定义id",example = "123")
    @Comment("标签定义id")
    private Long tagId;

    @ApiModelProperty(value="分类id",example = "123")
    @Comment("分类id")
    private Long classId;

    @ApiModelProperty(value="序号",example = "1")
    @Comment("序号")
    private Integer orderNo;

    @ApiModelProperty(value="条件")
    @Comment("条件")
    private List<ESTagRuleItemGroup> itemGroups;

    @Override
    public void valid() {
        Assert.notNull(classId, "分类id不可为空");
        Assert.isTrue(!BinaryUtils.isEmpty(itemGroups), "条件组不可为空");
    }
}
