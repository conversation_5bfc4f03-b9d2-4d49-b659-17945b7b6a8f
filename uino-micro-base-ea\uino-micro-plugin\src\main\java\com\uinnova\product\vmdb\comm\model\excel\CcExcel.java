package com.uinnova.product.vmdb.comm.model.excel;

import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;

/**
 * 
 * <AUTHOR>
 *
 */
@Comment("EXCEL表[CC_EXCEL]")
public class CcExcel implements EntityBean {
    private static final long serialVersionUID = 1L;

    @Comment("ID[ID]")
    private Long id;

    @Comment("EXCEL名称[EXCEL_NAME]")
    private String excelName;

    @Comment("EXCEL全名[EXCEL_FULL_NAME]    EXCEL全名:目录名+图像名, 中间以|分隔")
    private String excelFullName;

    @Comment("所属目录[DIR_ID]")
    private Long dirId;

    @Comment("EXCEL描述[EXCEL_DESC]")
    private String excelDesc;

    @Comment("EXCEL格式[EXCEL_TYPE]")
    private Integer excelType;

    @Comment("保存位置[EXCEL_PATH]")
    private String excelPath;

    @Comment("EXCEL范围[EXCEL_RANGE]    EXCEL范围:1=公共 2=私有")
    private Integer excelRange;

    @Comment("EXCEL大小[EXCEL_SIZE]    EXCEL大小:单位：byte")
    private Integer excelSize;

    @Comment("SHEET数[SHEET_COUNT]")
    private Integer sheetCount;

    @Comment("上传人ID[UPOR_ID]")
    private Long uporId;

    @Comment("上传人姓名[UPOR_NAME]")
    private String uporName;

    @Comment("上传时间[UP_TIME]")
    private Long upTime;

    @Comment("备用_1[CUSTOM_1]")
    private Long custom1;

    @Comment("备用_2[CUSTOM_2]")
    private Long custom2;

    @Comment("备用_3[CUSTOM_3]")
    private Long custom3;

    @Comment("备用_4[CUSTOM_4]")
    private String custom4;

    @Comment("备用_5[CUSTOM_5]")
    private String custom5;

    @Comment("备用_6[CUSTOM_6]")
    private String custom6;

    @Comment("搜索字段[SEARCH_FIELD]    搜索字段:图像名称")
    private String searchField;

    @Comment("排序字段[ORDER_NO]")
    private Long orderNo;

    @Comment("所属域[DOMAIN_ID]")
    private Long domainId;

    @Comment("创建人[CREATOR]")
    private String creator;

    @Comment("修改人[MODIFIER]")
    private String modifier;

    @Comment("数据状态[DATA_STATUS]    数据状态:数据状态：1-正常 0-删除")
    private Integer dataStatus;

    @Comment("创建时间[CREATE_TIME]    创建时间:yyyyMMddHHmmss")
    private Long createTime;

    @Comment("修改时间[MODIFY_TIME]    修改时间:yyyyMMddHHmmss")
    private Long modifyTime;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getExcelName() {
        return this.excelName;
    }

    public void setExcelName(String excelName) {
        this.excelName = excelName;
    }

    public String getExcelFullName() {
        return this.excelFullName;
    }

    public void setExcelFullName(String excelFullName) {
        this.excelFullName = excelFullName;
    }

    public Long getDirId() {
        return this.dirId;
    }

    public void setDirId(Long dirId) {
        this.dirId = dirId;
    }

    public String getExcelDesc() {
        return this.excelDesc;
    }

    public void setExcelDesc(String excelDesc) {
        this.excelDesc = excelDesc;
    }

    public Integer getExcelType() {
        return this.excelType;
    }

    public void setExcelType(Integer excelType) {
        this.excelType = excelType;
    }

    public String getExcelPath() {
        return this.excelPath;
    }

    public void setExcelPath(String excelPath) {
        this.excelPath = excelPath;
    }

    public Integer getExcelRange() {
        return this.excelRange;
    }

    public void setExcelRange(Integer excelRange) {
        this.excelRange = excelRange;
    }

    public Integer getExcelSize() {
        return this.excelSize;
    }

    public void setExcelSize(Integer excelSize) {
        this.excelSize = excelSize;
    }

    public Integer getSheetCount() {
        return this.sheetCount;
    }

    public void setSheetCount(Integer sheetCount) {
        this.sheetCount = sheetCount;
    }

    public Long getUporId() {
        return this.uporId;
    }

    public void setUporId(Long uporId) {
        this.uporId = uporId;
    }

    public String getUporName() {
        return this.uporName;
    }

    public void setUporName(String uporName) {
        this.uporName = uporName;
    }

    public Long getUpTime() {
        return this.upTime;
    }

    public void setUpTime(Long upTime) {
        this.upTime = upTime;
    }

    public Long getCustom1() {
        return this.custom1;
    }

    public void setCustom1(Long custom1) {
        this.custom1 = custom1;
    }

    public Long getCustom2() {
        return this.custom2;
    }

    public void setCustom2(Long custom2) {
        this.custom2 = custom2;
    }

    public Long getCustom3() {
        return this.custom3;
    }

    public void setCustom3(Long custom3) {
        this.custom3 = custom3;
    }

    public String getCustom4() {
        return this.custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }

    public String getCustom5() {
        return this.custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }

    public String getCustom6() {
        return this.custom6;
    }

    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }

    public String getSearchField() {
        return this.searchField;
    }

    public void setSearchField(String searchField) {
        this.searchField = searchField;
    }

    public Long getOrderNo() {
        return this.orderNo;
    }

    public void setOrderNo(Long orderNo) {
        this.orderNo = orderNo;
    }

    public Long getDomainId() {
        return this.domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }

    public String getCreator() {
        return this.creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getModifier() {
        return this.modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public Integer getDataStatus() {
        return this.dataStatus;
    }

    public void setDataStatus(Integer dataStatus) {
        this.dataStatus = dataStatus;
    }

    public Long getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getModifyTime() {
        return this.modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }

}
