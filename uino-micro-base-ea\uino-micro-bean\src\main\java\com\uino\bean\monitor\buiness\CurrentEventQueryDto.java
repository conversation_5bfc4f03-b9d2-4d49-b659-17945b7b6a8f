package com.uino.bean.monitor.buiness;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 当前告警查询数据传输类
 */
@Data
@ApiModel(value = "CurrentEventQueryDto.class", description = "当前告警查询数据传输类")
public class CurrentEventQueryDto implements Serializable {

    @ApiModelProperty(value = "条件相等参数，key为参数名称。规定全部大写，具体匹配时根据event_model.xml设置字段为准。例如查询identifier为1的数据，map中key为：IDENTIFIER，value为：1")
    private Map<String, String> equalsParams;

    @ApiModelProperty(value = "条件不相等参数，key为参数名称。规定全部大写，具体匹配时根据event_model.xml设置字段为准")
    private Map<String, String> notEqualsParams;

    @ApiModelProperty(value = "条件包含参数，key为参数名称。规定全部大写，具体匹配时根据event_model.xml设置字段为准")
    private Map<String, String> likeParams;

    @ApiModelProperty(value = "事件标识")
    private String identifier;

    @ApiModelProperty(value = "事件序列号")
    private String serial;

    @ApiModelProperty(value = "事件对象")
    private String ciName;

    @ApiModelProperty(value = "事件级别")
    private Integer severity;

    @ApiModelProperty(value = "事件描述")
    private String summary;

    @ApiModelProperty(value = "事件标题")
    private String eventTitle;

    @ApiModelProperty(value = "事件状态")
    private Integer status;

    @ApiModelProperty(value = "是否已升级")
    private Integer grade;

    @ApiModelProperty(value = "事件来源编号")
    private Integer sourceId;

    @ApiModelProperty(value = "源事件序列号")
    private String sourceEventId;

    @ApiModelProperty(value = "源对象名称")
    private String sourceCiName;

    @ApiModelProperty(value = "事件指标名称")
    private String kpiName;

    @ApiModelProperty(value = "事件指标分类ID")
    private String kpiCategoryID;

    @ApiModelProperty(value = "事件对象ID")
    private String ciId;

    @ApiModelProperty(value = "事件屏蔽标识")
    private Integer blackout;

    @ApiModelProperty(value = "过滤类型")
    private Integer filterType;

    @ApiModelProperty(value = "对象所属应用系统")
    private List<String> ciApplication;

    @ApiModelProperty(value = "开始时间", example = "时间戳格式：1620018196049")
    private Long startTime;

    @ApiModelProperty(value = "结束时间", example = "时间戳格式：1620018196049")
    private Long endTime;

    @ApiModelProperty(value = "所属域")
    private Long domainId;
}
