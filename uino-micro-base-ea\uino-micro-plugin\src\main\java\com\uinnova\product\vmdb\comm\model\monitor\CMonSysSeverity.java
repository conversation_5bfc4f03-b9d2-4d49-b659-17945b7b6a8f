package com.uinnova.product.vmdb.comm.model.monitor;

import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;

/**
 * 
 * <AUTHOR>
 *
 */
@Comment("级别定义表[MON_SYS_SEVERITY]")
public class CMonSysSeverity implements Condition {
    private static final long serialVersionUID = 1L;

    @Comment("ID[ID] operate-Equal[=]")
    private Long id;

    @Comment("ID[ID] operate-In[in]")
    private Long[] ids;

    @Comment("ID[ID] operate-GTEqual[>=]")
    private Long startId;

    @Comment("ID[ID] operate-LTEqual[<=]")
    private Long endId;

    @Comment("级别[SEVERITY] operate-Equal[=]    代码维一标识")
    private Integer severity;

    @Comment("级别[SEVERITY] operate-In[in]    代码维一标识")
    private Integer[] severitys;

    @Comment("级别[SEVERITY] operate-GTEqual[>=]    代码维一标识")
    private Integer startSeverity;

    @Comment("级别[SEVERITY] operate-LTEqual[<=]    代码维一标识")
    private Integer endSeverity;

    @Comment("颜色[COLOR] operate-Like[like]")
    private String color;

    @Comment("颜色[COLOR] operate-Equal[=]")
    private String colorEqual;

    @Comment("颜色[COLOR] operate-In[in]")
    private String[] colors;

    @Comment("中文名称[CHINESE_NAME] operate-Like[like]    1=NUMBER 2=VARCHAR")
    private String chineseName;

    @Comment("中文名称[CHINESE_NAME] operate-Equal[=]    1=NUMBER 2=VARCHAR")
    private String chineseNameEqual;

    @Comment("中文名称[CHINESE_NAME] operate-In[in]    1=NUMBER 2=VARCHAR")
    private String[] chineseNames;

    @Comment("英文名称[ENGLISH_NAME] operate-Like[like]")
    private String englishName;

    @Comment("英文名称[ENGLISH_NAME] operate-Equal[=]")
    private String englishNameEqual;

    @Comment("英文名称[ENGLISH_NAME] operate-In[in]")
    private String[] englishNames;

    @Comment("线的颜色[LINE_COLOR] operate-Like[like]")
    private String lineColor;

    @Comment("线的颜色[LINE_COLOR] operate-Equal[=]")
    private String lineColorEqual;

    @Comment("线的颜色[LINE_COLOR] operate-In[in]")
    private String[] lineColors;

    @Comment("声音文件名称[VOICE_NAME] operate-Like[like]")
    private String voiceName;

    @Comment("声音文件名称[VOICE_NAME] operate-Equal[=]")
    private String voiceNameEqual;

    @Comment("声音文件名称[VOICE_NAME] operate-In[in]")
    private String[] voiceNames;

    @Comment("声音文件资源路径[VOICE_URL] operate-Like[like]")
    private String voiceUrl;

    @Comment("创建时间[CREATE_TIME] operate-Equal[=]    yyyyMMddHHmmss")
    private Long createTime;

    @Comment("创建时间[CREATE_TIME] operate-In[in]    yyyyMMddHHmmss")
    private Long[] createTimes;

    @Comment("创建时间[CREATE_TIME] operate-GTEqual[>=]    yyyyMMddHHmmss")
    private Long startCreateTime;

    @Comment("创建时间[CREATE_TIME] operate-LTEqual[<=]    yyyyMMddHHmmss")
    private Long endCreateTime;

    @Comment("修改时间[MODIFY_TIME] operate-Equal[=]    yyyyMMddHHmmss")
    private Long modifyTime;

    @Comment("修改时间[MODIFY_TIME] operate-In[in]    yyyyMMddHHmmss")
    private Long[] modifyTimes;

    @Comment("修改时间[MODIFY_TIME] operate-GTEqual[>=]    yyyyMMddHHmmss")
    private Long startModifyTime;

    @Comment("修改时间[MODIFY_TIME] operate-LTEqual[<=]    yyyyMMddHHmmss")
    private Long endModifyTime;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long[] getIds() {
        return this.ids;
    }

    public void setIds(Long[] ids) {
        this.ids = ids;
    }

    public Long getStartId() {
        return this.startId;
    }

    public void setStartId(Long startId) {
        this.startId = startId;
    }

    public Long getEndId() {
        return this.endId;
    }

    public void setEndId(Long endId) {
        this.endId = endId;
    }

    public Integer getSeverity() {
        return this.severity;
    }

    public void setSeverity(Integer severity) {
        this.severity = severity;
    }

    public Integer[] getSeveritys() {
        return this.severitys;
    }

    public void setSeveritys(Integer[] severitys) {
        this.severitys = severitys;
    }

    public Integer getStartSeverity() {
        return this.startSeverity;
    }

    public void setStartSeverity(Integer startSeverity) {
        this.startSeverity = startSeverity;
    }

    public Integer getEndSeverity() {
        return this.endSeverity;
    }

    public void setEndSeverity(Integer endSeverity) {
        this.endSeverity = endSeverity;
    }

    public String getColor() {
        return this.color;
    }

    public void setColor(String color) {
        this.color = color;
    }

    public String getColorEqual() {
        return this.colorEqual;
    }

    public void setColorEqual(String colorEqual) {
        this.colorEqual = colorEqual;
    }

    public String[] getColors() {
        return this.colors;
    }

    public void setColors(String[] colors) {
        this.colors = colors;
    }

    public String getChineseName() {
        return this.chineseName;
    }

    public void setChineseName(String chineseName) {
        this.chineseName = chineseName;
    }

    public String getChineseNameEqual() {
        return this.chineseNameEqual;
    }

    public void setChineseNameEqual(String chineseNameEqual) {
        this.chineseNameEqual = chineseNameEqual;
    }

    public String[] getChineseNames() {
        return this.chineseNames;
    }

    public void setChineseNames(String[] chineseNames) {
        this.chineseNames = chineseNames;
    }

    public String getEnglishName() {
        return this.englishName;
    }

    public void setEnglishName(String englishName) {
        this.englishName = englishName;
    }

    public String getEnglishNameEqual() {
        return this.englishNameEqual;
    }

    public void setEnglishNameEqual(String englishNameEqual) {
        this.englishNameEqual = englishNameEqual;
    }

    public String[] getEnglishNames() {
        return this.englishNames;
    }

    public void setEnglishNames(String[] englishNames) {
        this.englishNames = englishNames;
    }

    public String getLineColor() {
        return this.lineColor;
    }

    public void setLineColor(String lineColor) {
        this.lineColor = lineColor;
    }

    public String getLineColorEqual() {
        return this.lineColorEqual;
    }

    public void setLineColorEqual(String lineColorEqual) {
        this.lineColorEqual = lineColorEqual;
    }

    public String[] getLineColors() {
        return this.lineColors;
    }

    public void setLineColors(String[] lineColors) {
        this.lineColors = lineColors;
    }

    public String getVoiceName() {
        return this.voiceName;
    }

    public void setVoiceName(String voiceName) {
        this.voiceName = voiceName;
    }

    public String getVoiceNameEqual() {
        return this.voiceNameEqual;
    }

    public void setVoiceNameEqual(String voiceNameEqual) {
        this.voiceNameEqual = voiceNameEqual;
    }

    public String[] getVoiceNames() {
        return this.voiceNames;
    }

    public void setVoiceNames(String[] voiceNames) {
        this.voiceNames = voiceNames;
    }

    public String getVoiceUrl() {
        return this.voiceUrl;
    }

    public void setVoiceUrl(String voiceUrl) {
        this.voiceUrl = voiceUrl;
    }

    public Long getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long[] getCreateTimes() {
        return this.createTimes;
    }

    public void setCreateTimes(Long[] createTimes) {
        this.createTimes = createTimes;
    }

    public Long getStartCreateTime() {
        return this.startCreateTime;
    }

    public void setStartCreateTime(Long startCreateTime) {
        this.startCreateTime = startCreateTime;
    }

    public Long getEndCreateTime() {
        return this.endCreateTime;
    }

    public void setEndCreateTime(Long endCreateTime) {
        this.endCreateTime = endCreateTime;
    }

    public Long getModifyTime() {
        return this.modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Long[] getModifyTimes() {
        return this.modifyTimes;
    }

    public void setModifyTimes(Long[] modifyTimes) {
        this.modifyTimes = modifyTimes;
    }

    public Long getStartModifyTime() {
        return this.startModifyTime;
    }

    public void setStartModifyTime(Long startModifyTime) {
        this.startModifyTime = startModifyTime;
    }

    public Long getEndModifyTime() {
        return this.endModifyTime;
    }

    public void setEndModifyTime(Long endModifyTime) {
        this.endModifyTime = endModifyTime;
    }

}
