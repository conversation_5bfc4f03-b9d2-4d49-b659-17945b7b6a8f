package com.uinnova.product.eam.service.es;

import com.uinnova.product.eam.comm.model.es.BootTrialPageProgess;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;

@Service
public class BootTrialPageProgressDao extends AbstractESBaseDao<BootTrialPageProgess, BootTrialPageProgess> {

    @Override
    public String getIndex() {
        return "uino_eam_boot_trial_page_progress";
    }

    @Override
    public String getType() {
        return getIndex();
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }
}
