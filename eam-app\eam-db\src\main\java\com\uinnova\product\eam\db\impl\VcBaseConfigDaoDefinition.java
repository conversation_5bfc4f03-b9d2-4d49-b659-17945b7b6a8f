package com.uinnova.product.eam.db.impl;


import com.binary.framework.dao.DaoDefinition;

import com.uinnova.product.eam.comm.model.CVcBaseConfig;
import com.uinnova.product.eam.comm.model.VcBaseConfig;


/**
 * 基础配置表[VC_BASE_CONFIG]数据访问对象定义实现
 */
public class VcBaseConfigDaoDefinition implements DaoDefinition<VcBaseConfig, CVcBaseConfig> {


	@Override
	public Class<VcBaseConfig> getEntityClass() {
		return VcBaseConfig.class;
	}


	@Override
	public Class<CVcBaseConfig> getConditionClass() {
		return CVcBaseConfig.class;
	}


	@Override
	public String getTableName() {
		return "VC_BASE_CONFIG";
	}


	@Override
	public boolean hasDataStatusField() {
		return true;
	}


	@Override
	public void setDataStatusValue(VcBaseConfig record, int status) {
		record.setDataStatus(status);
	}


	@Override
	public void setDataStatusValue(CVcBaseConfig cdt, int status) {
		cdt.setDataStatus(status);
	}


	@Override
	public void setCreatorValue(VcBaseConfig record, String creator) {
		record.setCreator(creator);
	}


	@Override
	public void setModifierValue(VcBaseConfig record, String modifier) {
		record.setModifier(modifier);
	}


}


