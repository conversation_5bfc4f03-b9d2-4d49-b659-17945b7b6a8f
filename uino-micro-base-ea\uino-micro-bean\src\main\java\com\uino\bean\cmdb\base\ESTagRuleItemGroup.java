package com.uino.bean.cmdb.base;

import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.util.Assert;

import com.binary.core.util.BinaryUtils;
import com.binary.framework.bean.annotation.Comment;
import com.uino.bean.permission.business.IValidDto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 
 * <AUTHOR>
 *
 */
@Setter
@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value="标签定义条件类",description = "标签定义条件信息")
public class ESTagRuleItemGroup implements Serializable, IValidDto {

    private static final long serialVersionUID = -2252363014536225303L;

    @ApiModelProperty(value="逻辑运算符",example = "1")
    @Comment("逻辑运算符[LOGIC_OP]  1=与 2=或")
    private Integer logicOp;

    @ApiModelProperty(value="条件详情")
    @Comment("条件详情")
    private List<ESTagRuleItem> items;


    @Override
    public void valid() {
        Assert.notNull(logicOp, "逻辑运算符不可为空");
        Assert.isTrue(!BinaryUtils.isEmpty(items), "条件不可为空");
    }
}
