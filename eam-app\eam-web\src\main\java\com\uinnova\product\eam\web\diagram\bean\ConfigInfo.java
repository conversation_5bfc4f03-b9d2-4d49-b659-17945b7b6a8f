package com.uinnova.product.eam.web.diagram.bean;

import java.io.Serializable;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import com.binary.framework.bean.annotation.Comment;
import com.uino.bean.permission.base.SysUser;

public class ConfigInfo implements Serializable {
	private static final long serialVersionUID = 1L;

	
	@Comment("是否脱敏[1是0否]")
	private Integer isSensitive;
	
	
	@Comment("脱敏值集合")
	private List<String> sensitives;
	
	@Comment("PMV Ip地址")
	private String pmvIpAddress;
	
	@Comment("视频 地址")
	private String rsmRoot;

	@Comment("部署单元规则名称")
	private String[] ruleNames;
	
	@Comment("部署单元模本名称")
	private String templateName;
	
	@Comment("获取当前系统时间,格式：yyyy-MM-dd HH:mm:ss")
	private String currentTime;
	
	@Comment("当前用户是否有转换系统模版的权限，1=是，0=否")
	private Integer haveTemplateAuth;
	
	@Comment("当前用户所属的角色的code")
	private String[] roleCodes;
	
	@Comment("外接数据类型")
	private String[] externalDataTypes;
	
	@Comment("是否是宝马项目:1是，0 否")
	private Integer isBmwProject;
	
	@Comment("显示运维分析菜单名称，不配置就不显示")
	private String showOperationAnalysisMenuName;
	
	@Comment("电力项目站点，业务，路由之间的规则名称")
	private Map<String,Object> powerRuleNames;
	
	@Comment("电力项目，同缆同路由分析关系分类属性名称")
	private String powerRltClassAttrName;
	
	@Comment("SMV模块访问地址")
	private String smvUrl;
	
	@Comment("当前登录用户")
	private SysUser curUser;
	
	@Comment("路由分类code")
	private String routerClassCode;
	
	@Comment("需要隐藏的图标目录")
	private String[] hideImgDirNames;
	
	@Comment("下载浏览器地址:第一个是google,第二个是IE")
	private List<String> browserUrls;
	
	@Comment("接口所属设备分类")
	private String deviceClassCode;
	
	@Comment("视图超级管理员权限")
	private Map<String,Boolean> diagramSuperPower;
	
	@Comment("性能数据来源，PMV或者NOAH")
	private String performanceSwitchKey;
	
	@Comment("admin用户id")
	private Long adminUserId;
	
	@Comment("交易路径类型所在ci分类")
	private String tranPathTypeClassCodeAndAttrName;
	
	@Comment("附加属性")
	private Map<String,Object> attrs=new LinkedHashMap<>();
	
	public String getTranPathTypeClassCodeAndAttrName() {
		return tranPathTypeClassCodeAndAttrName;
	}

	public void setTranPathTypeClassCodeAndAttrName(String tranPathTypeClassCodeAndAttrName) {
		this.tranPathTypeClassCodeAndAttrName = tranPathTypeClassCodeAndAttrName;
	}

	public Long getAdminUserId() {
		return adminUserId;
	}

	public void setAdminUserId(Long adminUserId) {
		this.adminUserId = adminUserId;
	}

	public String getPerformanceSwitchKey() {
		return performanceSwitchKey;
	}

	public void setPerformanceSwitchKey(String performanceSwitchKey) {
		this.performanceSwitchKey = performanceSwitchKey;
	}

	public Map<String, Boolean> getDiagramSuperPower() {
		return diagramSuperPower;
	}

	public void setDiagramSuperPower(Map<String, Boolean> diagramSuperPower) {
		this.diagramSuperPower = diagramSuperPower;
	}

	public String getDeviceClassCode() {
		return deviceClassCode;
	}

	public void setDeviceClassCode(String deviceClassCode) {
		this.deviceClassCode = deviceClassCode;
	}

	public List<String> getBrowserUrls() {
		return browserUrls;
	}

	public void setBrowserUrls(List<String> browserUrls) {
		this.browserUrls = browserUrls;
	}

	public String getPowerRltClassAttrName() {
		return powerRltClassAttrName;
	}

	public void setPowerRltClassAttrName(String powerRltClassAttrName) {
		this.powerRltClassAttrName = powerRltClassAttrName;
	}

	public Map<String, Object> getPowerRuleNames() {
		return powerRuleNames;
	}

	public void setPowerRuleNames(Map<String, Object> powerRuleNames) {
		this.powerRuleNames = powerRuleNames;
	}

	public String getShowOperationAnalysisMenuName() {
		return showOperationAnalysisMenuName;
	}

	public void setShowOperationAnalysisMenuName(String showOperationAnalysisMenuName) {
		this.showOperationAnalysisMenuName = showOperationAnalysisMenuName;
	}

	public String[] getHideImgDirNames() {
		return hideImgDirNames;
	}

	public void setHideImgDirNames(String[] hideImgDirNames) {
		this.hideImgDirNames = hideImgDirNames;
	}

	public String getRouterClassCode() {
		return routerClassCode;
	}

	public void setRouterClassCode(String routerClassCode) {
		this.routerClassCode = routerClassCode;
	}

	public SysUser getCurUser() {
		return curUser;
	}

	public void setCurUser(SysUser curUser) {
		this.curUser = curUser;
	}

	public String[] getRoleCodes() {
		return roleCodes;
	}

	public void setRoleCodes(String[] roleCodes) {
		this.roleCodes = roleCodes;
	}

	public Integer getIsBmwProject() {
		return isBmwProject;
	}

	public void setIsBmwProject(Integer isBmwProject) {
		this.isBmwProject = isBmwProject;
	}

	public String[] getExternalDataTypes() {
		return externalDataTypes;
	}

	public void setExternalDataTypes(String[] externalDataTypes) {
		this.externalDataTypes = externalDataTypes;
	}

	public Integer getHaveTemplateAuth() {
		return haveTemplateAuth;
	}

	public void setHaveTemplateAuth(Integer haveTemplateAuth) {
		this.haveTemplateAuth = haveTemplateAuth;
	}

	public String getCurrentTime() {
		return currentTime;
	}

	public void setCurrentTime(String currentTime) {
		this.currentTime = currentTime;
	}

	public String[] getRuleNames() {
		return ruleNames;
	}

	public void setRuleNames(String[] ruleNames) {
		this.ruleNames = ruleNames;
	}

	public String getTemplateName() {
		return templateName;
	}

	public void setTemplateName(String templateName) {
		this.templateName = templateName;
	}

	public String getRsmRoot() {
		return rsmRoot;
	}

	public void setRsmRoot(String rsmRoot) {
		this.rsmRoot = rsmRoot;
	}

	public Integer getIsSensitive() {
		return isSensitive;
	}

	public void setIsSensitive(Integer isSensitive) {
		this.isSensitive = isSensitive;
	}

	public List<String> getSensitives() {
		return sensitives;
	}

	public void setSensitives(List<String> sensitives) {
		this.sensitives = sensitives;
	}

	public String getPmvIpAddress() {
		return pmvIpAddress;
	}

	public void setPmvIpAddress(String pmvIpAddress) {
		this.pmvIpAddress = pmvIpAddress;
	}

	public String getSmvUrl() {
		return smvUrl;
	}

	public void setSmvUrl(String smvUrl) {
		this.smvUrl = smvUrl;
	}

	public Map<String, Object> getAttrs() {
		return attrs;
	}

	public void setAttrs(Map<String, Object> attrs) {
		this.attrs = attrs;
	}


	
}
