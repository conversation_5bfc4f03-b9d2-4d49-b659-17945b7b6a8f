package com.uino.service.cmdb.dataset.microservice.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.binary.core.exception.MessageException;
import com.binary.core.util.BinaryUtils;
import com.uino.bean.chart.bean.UinoChartDataBean;
import com.uino.bean.chart.enums.UinoChartType;
import com.uino.bean.cmdb.base.ESCIClassInfo;
import com.uino.bean.cmdb.base.dataset.DataSetMallApi;
import com.uino.bean.cmdb.base.dataset.DataSetMallApiMetrics;
import com.uino.bean.cmdb.base.dataset.DataSetMallApiMetrics.AggFlagTypeEnum;
import com.uino.bean.cmdb.base.dataset.log.DataSetMallApiLog;
import com.uino.bean.monitor.base.ESKpiInfo;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.tp.buiness.PerfDataRespDTO;
import com.uino.bean.tp.buiness.PerfDataRespDTO.PerfDataDTO;
import com.uino.bean.tp.query.MetricAttrValQueryDTO;
import com.uino.bean.tp.query.PerfDataReqDTOV2;
import com.uino.bean.tp.query.PerfDataReqDTOV2.AttrInfo;
import com.uino.dao.cmdb.dataset.ESDataSetMallApiLogSvc;
import com.uino.dao.util.ESUtil;
import com.uino.monitor.tp.metric.MetricDataSvc;
import com.uino.service.cmdb.dataset.microservice.IMallApiSvc;
import com.uino.service.cmdb.microservice.ICIClassSvc;
import com.uino.service.simulation.IKpiSvc;
import com.uino.util.sys.CheckAttrUtil;
import com.uino.util.sys.SysUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.time.*;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.regex.Pattern;

/**
 * 指标数据集
 *
 * @Date 2020/11/20 15:46
 * <AUTHOR> zmj
 */
@Service
@Slf4j
public class MallApiMertricsSvc implements IMallApiSvc {

    @Autowired
    private IKpiSvc kpiSvc;

    @Autowired
    private ICIClassSvc classSvc;

    @Autowired
    private MetricDataSvc metricDataSvc;

    @Autowired
    private ESDataSetMallApiLogSvc esDataSetMallApiLogSvc;

    @Override
    public DataSetMallApi checkCharacteristic(SysUser user, JSONObject jsonObject) {
        DataSetMallApiMetrics dataSetMetrics = new DataSetMallApiMetrics(jsonObject);
        dataSetMetrics.valid();
        validMetrics(dataSetMetrics);
        return dataSetMetrics;
    }

    @Override
    public JSONObject execute(DataSetMallApi dataSetMallApi, JSONObject jsonObject) {
        long curTime = System.currentTimeMillis();
        String username = "system";
        try {
            username = SysUtil.getCurrentUserInfo().getLoginCode();
        } catch (Exception e) {
            log.error("获取登陆用户失败，所有值取默认值");
        }
        DataSetMallApiMetrics dataSetMetrics = (DataSetMallApiMetrics) dataSetMallApi;
        Long metricId = jsonObject.containsKey("metricId") ? jsonObject.getLong("metricId") : dataSetMetrics.getMetricId();
        Long classId = jsonObject.containsKey("classId") ? jsonObject.getLong("classId") : dataSetMetrics.getClassId();
        String metricTagName = jsonObject.containsKey("metricTagName") ? jsonObject.getString("metricTagName") : dataSetMetrics.getMetricTagName();
        List<String> metricTagValArr =
                jsonObject.containsKey("metricTagVals") ? JSON.parseArray(JSON.toJSONString(jsonObject.getJSONArray("metricTagVals")), String.class) : dataSetMetrics.getMetricTagVals();
        String timeFlag = jsonObject.containsKey("timeFlag") ? jsonObject.getString("timeFlag") : dataSetMetrics.getTimeFlag();
        String aggFlag = jsonObject.containsKey("aggFlag") ? jsonObject.getString("aggFlag") : dataSetMetrics.getAggFlag();
        String interval = jsonObject.containsKey("interval") ? jsonObject.getString("interval") : dataSetMetrics.getInterval();
        String timeStr = jsonObject.getString("time");
        Long ciCode = jsonObject.getLong("ciCode");
        Long startTime = jsonObject.getLong("startTime");
        Long endTime = jsonObject.getLong("endTime");

        ESKpiInfo kpiInfo = kpiSvc.getKpiInfoById(metricId);
        Assert.notNull(kpiInfo, "指标不存在");
        ESCIClassInfo esciClassInfo = classSvc.queryESClassInfoById(classId);
        Assert.notNull(esciClassInfo, "分类不存在");

        List<String> metricTagVals = new ArrayList<>();
        if (!BinaryUtils.isEmpty(metricTagValArr)) {
            metricTagVals = JSON.parseArray(JSON.toJSONString(metricTagValArr), String.class);
        } else {
            MetricAttrValQueryDTO query = new MetricAttrValQueryDTO();
            query.setClassId(classId);
            query.setMetric(kpiInfo.getKpiName());
            query.setAttrKey(metricTagName);
            metricTagVals = metricDataSvc.queryAttrValue(query);
        }

        boolean isSuccess = true;
        try {
            PerfDataReqDTOV2 query = new PerfDataReqDTOV2();
            query.setClassIds(Collections.singletonList(classId));
            query.setMetrics(Collections.singletonList(kpiInfo.getKpiName()));
            query.setInterval(interval);
            query.setCiId(ciCode);
            if (!BinaryUtils.isEmpty(metricTagName)) {
                List<AttrInfo> attrInfos = new ArrayList<>();
                if (!BinaryUtils.isEmpty(metricTagValArr)) {
                    for (String tagVal : metricTagVals) {
                        AttrInfo attrInfo = new AttrInfo();
                        attrInfo.setAttrKey(metricTagName);
                        attrInfo.setAttrValue(tagVal);
                        attrInfos.add(attrInfo);
                    }
                } else {
                    AttrInfo attrInfo = new AttrInfo();
                    attrInfo.setAttrKey(metricTagName);
                    attrInfos.add(attrInfo);
                }
                query.setAttrInfos(attrInfos);
            }
            if (!aggFlag.equals(DataSetMallApiMetrics.AggFlagTypeEnum.ORIGIN.getType())) {
                query.setAggFlag(true);
            }

            // 2021-09-29 增加时间范围筛选
            if (null != startTime && null != endTime) {
                query.setStartTime(startTime);
                query.setEndTime(endTime);
            } else {
                this.buildTimeFrameByTimeFlag(timeFlag, query, timeStr);
            }

            long time = System.currentTimeMillis();
            log.info("Start execute queryPerfDataV2");
            PerfDataRespDTO perfData = metricDataSvc.queryPerfDataV2(query);
            log.info("Execute queryPerfDataV2 spend : " + (System.currentTimeMillis() - time) + "ms");
            // JSONObject retJSON = new JSONObject();
            // retJSON.put("data", this.buildReturnData(perfData, aggFlag));
            JSONObject retJson = this.buildReturnData(perfData, aggFlag, metricTagName, metricTagVals);
            // retJson.put("MetricName", kpiInfo.getKpiName());
            retJson.put("unit", kpiInfo.getUnitName());
            return retJson;
        } catch (MessageException e) {
            log.error("", e);
            isSuccess = false;
            throw e;
        } finally {
            if (dataSetMallApi.getId() != null) {
                // 保存日志
                DataSetMallApiLog dataSetMallApiLog = new DataSetMallApiLog();
                dataSetMallApiLog.setId(ESUtil.getUUID());
                dataSetMallApiLog.setDomainId(dataSetMallApi.getDomainId());
                dataSetMallApiLog.setDataSetMallApiId(dataSetMallApi.getId());
                dataSetMallApiLog.setDataSetType(dataSetMallApi.getType());
                dataSetMallApiLog.setSuccess(isSuccess);
                dataSetMallApiLog.setRespUserCode(username);
                dataSetMallApiLog.setRespDuration(System.currentTimeMillis() - curTime);
                dataSetMallApiLog.setCreateTime(System.currentTimeMillis());
                esDataSetMallApiLogSvc.saveOrUpdate(dataSetMallApiLog.toJson(), true);
            }
        }
    }

    @Override
    public JSONObject realTimeExecute(DataSetMallApi dataSetMallApi, JSONObject jsonObject) {
        return execute(dataSetMallApi, jsonObject);
    }

    private void validMetrics(DataSetMallApiMetrics dataSetMetrics) {
        Assert.notNull(kpiSvc.getKpiInfoById(dataSetMetrics.getMetricId()), "指标不存在");
        Assert.notNull(classSvc.queryESClassInfoById(dataSetMetrics.getClassId()), "分类不存在");
    }

    /**
     * 根据时间范围计算起止时间
     *
     * @param timeFlag
     * @param query
     */
    private void buildTimeFrameByTimeFlag(String timeFlag, PerfDataReqDTOV2 query, String timeStr) {
        long startTime, endTime;
        LocalDateTime dateTime = LocalDateTime.now();
        if (!BinaryUtils.isEmpty(timeStr)) {
            try {
                dateTime = LocalDateTime.ofEpochSecond(Long.parseLong(timeStr) / 1000L, 0, ZoneOffset.ofHours(8));
            } catch (Exception e) {
                Date date = CheckAttrUtil.getDateByDefaultRule(timeStr);
                Assert.notNull(date, "time参数异常");
                dateTime = LocalDateTime.ofEpochSecond(date.getTime() / 1000L, 0, ZoneOffset.ofHours(8));
            }
        }
        Pattern pattern = Pattern.compile("-?\\d+[\\\\h\\\\d\\\\w\\\\M\\\\y]{1}");
        Assert.isTrue(timeFlag.length() > 1 && pattern.matcher(timeFlag).matches(), "时间范围格式错误");
        String flag = timeFlag.substring(timeFlag.length() - 1);
        long time = Long.parseLong(timeFlag.replace(flag, ""));
        if (time < 0) {
            dateTime = dateTime.withMinute(0).withSecond(0).withNano(0);
        }
        endTime = dateTime.toInstant(ZoneOffset.ofHours(8)).toEpochMilli();
        switch (flag) {
            case "h":
                endTime = dateTime.toInstant(ZoneOffset.ofHours(8)).toEpochMilli();
                dateTime = dateTime.minusHours(Math.abs(time));
                break;
            case "d":
                if (time < 0) {
                    dateTime = dateTime.withHour(0);
                    endTime = dateTime.toInstant(ZoneOffset.ofHours(8)).toEpochMilli();
                }
                dateTime = dateTime.minusDays(Math.abs(time));
                break;
            case "w":
                if (time < 0) {
                    LocalDateTime lastSunday =
                            dateTime.minusWeeks(1).plusDays(7 - LocalDate.now().getDayOfWeek().getValue()).with(LocalTime.MAX).atZone(ZoneId.systemDefault()).toLocalDateTime();
                    endTime = lastSunday.toInstant(ZoneOffset.ofHours(8)).toEpochMilli();
                    dateTime = dateTime.minusDays(dateTime.getDayOfWeek().getValue() - 1).withHour(0);
                }
                dateTime = dateTime.minusWeeks(Math.abs(time));
                break;
            case "M":
                if (time < 0) {
                    LocalDateTime lastEndDay =
                            dateTime.minusMonths(1).with(TemporalAdjusters.lastDayOfMonth()).with(LocalTime.MAX).atZone(ZoneId.systemDefault()).toLocalDateTime();
                    endTime = lastEndDay.toInstant(ZoneOffset.ofHours(8)).toEpochMilli();
                    dateTime = dateTime.withDayOfMonth(1).withHour(0);
                }
                dateTime = dateTime.minusMonths(Math.abs(time));
                break;
            case "y":
                if (time < 0) {
                    LocalDateTime lastEndDay = dateTime.minusYears(1).with(TemporalAdjusters.lastDayOfYear()).with(LocalTime.MAX).atZone(ZoneId.systemDefault()).toLocalDateTime();
                    endTime = lastEndDay.toInstant(ZoneOffset.ofHours(8)).toEpochMilli();
                    dateTime = dateTime.withDayOfYear(1).withHour(0);
                }
                dateTime = dateTime.minusYears(Math.abs(time));
                break;
            default:
                Assert.isTrue(false, "时间范围格式错误");
                break;
        }
        startTime = dateTime.toInstant(ZoneOffset.ofHours(8)).toEpochMilli();
        query.setStartTime(startTime);
        query.setEndTime(endTime);
    }

    private JSONObject buildReturnData(PerfDataRespDTO perfData, String aggFlag, String tagName, List<String> tagVals) {
        UinoChartDataBean<Map<String, List<Double>>> chartDataBean = new UinoChartDataBean<>(UinoChartType.LINESTACK);
        List<String> xData = new ArrayList<>();
        Map<String, List<Double>> chartData = new HashMap<String, List<Double>>();
        if (AggFlagTypeEnum.ORIGIN.getType().equals(aggFlag)) {
            // for (String tagVal : tagVals) {
            // chartData.put(tagVal, new ArrayList<>());
            // }
            chartData.put(tagName, new ArrayList<>());
            List<JSONObject> datas = perfData.getOriginData().getData();
            for (JSONObject object : datas) {
                xData.add(object.getString("time"));
                chartData.get(tagName).add(object.getDouble("value"));
                // String tagVal = object.getString(tagName);
                // if (!BinaryUtils.isEmpty(tagVal) && tagVals.contains(tagVal)) {
                // chartData.get(tagVal).add(object.getString("value"));
                // }
            }
        } else {
            chartData.put(tagName, new ArrayList<>());
            AggFlagTypeEnum typeEnum = DataSetMallApiMetrics.AggFlagTypeEnum.valueOfType(aggFlag);
            switch (typeEnum) {
                case SUM:
                    List<PerfDataDTO> aggDatas = perfData.getAggData();
                    for (PerfDataDTO data : aggDatas) {
                        xData.add(String.valueOf(data.getTime()));
                        BigDecimal val = data.getSum();
                        chartData.get(tagName)
                                .add(val == null ? 0D : val.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
                    }
                    break;
                case MAX:
                    for (PerfDataDTO data : perfData.getAggData()) {
                        xData.add(String.valueOf(data.getTime()));
                        BigDecimal val = data.getMax();
                        chartData.get(tagName)
                                .add(val == null ? 0D : val.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
                    }
                    break;
                case MIN:
                    for (PerfDataDTO data : perfData.getAggData()) {
                        xData.add(String.valueOf(data.getTime()));
                        BigDecimal val = data.getMin();
                        chartData.get(tagName)
                                .add(val == null ? 0D : val.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
                    }
                    break;
                case AVG:
                    for (PerfDataDTO data : perfData.getAggData()) {
                        xData.add(String.valueOf(data.getTime()));
                        BigDecimal val = data.getAvg();
                        chartData.get(tagName)
                                .add(val == null ? 0D : val.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
                    }
                    break;
                default:
                    break;
            }
        }
        chartDataBean.setXData(xData);
        chartDataBean.setData(chartData);
        return JSONObject.parseObject(JSONObject.toJSONString(chartDataBean));
        // jsonObject.put("xData", xData);
        // jsonObject.put("ChartsData", data);
        // return jsonObject;
    }

}
