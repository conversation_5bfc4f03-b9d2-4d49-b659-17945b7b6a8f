package com.uino.bean.cmdb.business;

import java.util.Map;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 
 * <AUTHOR>
 *
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value="修改ci关系类",description = "修改的ci关系信息")
public class UpdateCiRltRequestDto {
	/**
	 * 要修改ci关系的id
	 */
	@ApiModelProperty(value="要修改对象关系的id",example = "123")
	private Long ciRltId;
	/**
	 * 修改后的属性
	 */
	@ApiModelProperty(value="修改后的属性")
	private Map<String, String> attrs;
}
