package com.uinnova.product.eam.service.es;

import com.uinnova.product.eam.comm.model.es.CEamHierarchy;
import com.uinnova.product.eam.comm.model.es.EamHierarchy;
import com.uino.dao.AbstractESBaseDao;
import com.uino.service.util.FileUtil;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.util.List;

/**
 * 层级配置dao
 * <AUTHOR>
 */
@Service
public class BmHierarchyDao extends AbstractESBaseDao<EamHierarchy, CEamHierarchy> {

    @Override
    public String getIndex() {
        return "uino_eam_hierarchy";
    }

    @Override
    public String getType() {
        return "_doc";
    }

    @PostConstruct
    public void init() {
        List<EamHierarchy> list = FileUtil.getData("/initdata/uino_eam_hierarchy.json", EamHierarchy.class);
        super.initIndex(list);
    }
}
