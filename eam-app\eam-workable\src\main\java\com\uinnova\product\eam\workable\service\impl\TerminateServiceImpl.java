package com.uinnova.product.eam.workable.service.impl;

import com.uinnova.product.eam.workable.model.Terminate;
import com.uinnova.product.eam.workable.repository.TerminateDiagramOrPlan;
import com.uinnova.product.eam.workable.service.TerminateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class TerminateServiceImpl implements TerminateService {

    @Autowired
    private TerminateDiagramOrPlan terminateDiagramOrPlan;

    @Autowired
    private FilterUserServiceImpl filterUserService;

    @Override
    public String saveTerminate(Terminate terminate) {
        if (terminate.getId() == null) {
            //这块以后需要生成32位uuid
            terminate.setId(filterUserService.getUUID());
            terminate.setCreateTime(System.currentTimeMillis());
            terminate.setModifyTime(System.currentTimeMillis());
            terminate.setDomainId(1);
        }else{
            terminate.setModifyTime(System.currentTimeMillis());
        }
        Terminate save = terminateDiagramOrPlan.save(terminate);
        return save.getId();
    }

    @Override
    public void deleteTerminate(String businessKey, String processDefinitionKey) {
        terminateDiagramOrPlan.deleteByBusinessKeyAndProcessDefinitionKey(businessKey,processDefinitionKey);
    }

    @Override
    public List<Terminate> selectFlowStatus(String businessKey, String processDefinitionKey) {
        return terminateDiagramOrPlan.findTerminateByBusinessKeyAndProcessDefinitionKey(businessKey,processDefinitionKey);
    }
}
