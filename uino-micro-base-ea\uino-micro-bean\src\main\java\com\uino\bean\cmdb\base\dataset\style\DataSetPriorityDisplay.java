package com.uino.bean.cmdb.base.dataset.style;

import com.alibaba.fastjson.JSONObject;

/**
 * @Classname PriorityDisplay
 * @Description 优先显示
 * @Date 21:43
 * @Created by sh
 */
public class DataSetPriorityDisplay {
    private Long id;
    private String userCode;
    private Long dataSetMallApiId;
    private int display;
    private Long createTime;
    private Long modifyTime;
    private Long domainId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getDataSetMallApiId() {
        return dataSetMallApiId;
    }

    public void setDataSetMallApiId(Long dataSetMallApiId) {
        this.dataSetMallApiId = dataSetMallApiId;
    }

    public String getUserCode() {
        return userCode;
    }

    public void setUserCode(String userCode) {
        this.userCode = userCode;
    }

    public int getDisplay() {
        return display;
    }

    public void setDisplay(int display) {
        this.display = display;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Long getDomainId() {
        return domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }

    public DataSetPriorityDisplay() {
    }

    public DataSetPriorityDisplay(JSONObject json) {
        if (json.containsKey("id")) {
            this.id = json.getLong("id");
        }
        if (json.containsKey("domainId")) {
            this.domainId = json.getLong("domainId");
        }
        if (json.containsKey("userCode")) {
            this.userCode = json.getString("userCode");
        }
        if (json.containsKey("dataSetMallApiId")) {
            this.dataSetMallApiId = json.getLong("dataSetMallApiId");
        }
        if (json.containsKey("display")) {
                this.display = json.getInteger("display");
        }
        if (json.containsKey("createTime")) {
            this.createTime = json.getLong("createTime");
        }
        if (json.containsKey("modifyTime")) {
            this.modifyTime = json.getLong("modifyTime");
        }
    }

    public JSONObject toJson() {
        JSONObject json = new JSONObject();
        json.put("id", id);
        json.put("domainId", domainId);
        json.put("userCode", userCode);
        json.put("dataSetMallApiId", dataSetMallApiId);
        json.put("display", display);
        json.put("createTime", createTime);
        json.put("modifyTime", modifyTime);
        return json;
    }
}
