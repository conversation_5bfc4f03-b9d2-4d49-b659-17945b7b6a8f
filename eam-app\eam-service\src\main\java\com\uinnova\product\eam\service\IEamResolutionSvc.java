package com.uinnova.product.eam.service;

import com.binary.jdbc.Page;
import com.uinnova.product.eam.comm.bean.Resolution;
import com.uinnova.product.eam.comm.bean.ResolutionDoc;
import com.uinnova.product.eam.model.ResolutionCdt;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;
import java.util.Set;

/**
 * 架构决议接口
 * <AUTHOR>
 */
public interface IEamResolutionSvc {

    /**
     * 新增架构决议
     * @param resolution 架构决议信息
     * @param files 架构决议附件文档
     * @return 架构决议id
     */
    Long addResolution(Resolution resolution, MultipartFile[] files);

    /**
     * 删除架构决议文档
     * @param resolutionId 架构决议id
     * @param resolutionDocId 架构决议文档id
     */
    void deleteResolutionDoc(Long resolutionId, Long resolutionDocId);

    /**
     * 分页获取架构决议，按时间倒序
     * @param resolutionsCdt 架构决议查询条件
     * @return 分页数据
     */
    Page<Resolution> searchResolutions(ResolutionCdt resolutionsCdt);

    /**
     * 根据id查询决议信息
     * @param resolutionId 架构决议id
     * @return 决议信息
     */
    Resolution getById(Long resolutionId);

    /**
     * 获取架构决议文档信息
     * @param resolutionDocId 架构决议文档id
     * @return 决议文档信息
     */
    ResolutionDoc getResolutionDocByResolutionDocId(Long resolutionDocId);

    /**
     * 更新架构决议文档
     * @param resolutionId 架构决议id
     * @param files 文件
     * @return 架构决议所有的文档信息，包括之前的和现在的
     */
    Set<Map<String, Object>> uploadResolutionDoc(Long resolutionId, MultipartFile[] files);

    /**
     * 修改架构决议信息
     * @param resolution 架构决议
     */
    void editResolution(Resolution resolution);

    /**
     * 根据id删除决议文档
     * @param id 决议文档id
     */
    void deleteResolutionById(Long id);

    /**
     * 批量删除决议文档
     * @param ids 多个决议文档id
     */
    void deleteResolutionByIds(Long[] ids);

    /**
     * 根绝id获取架构决议文档
     * @param resolutionDocId 架构决议文档id
     * @return 架构决议文档
     */
    ResolutionDoc getResolutionDocById(Long resolutionDocId);

    /**
     * 批量修改架构决议数据
     *
     * @param resolutions
     */
    void batchEditResolution(Resolution[] resolutions);
}
