package com.uinnova.product.eam.model.enums;

/**
 * description
 *
 * <AUTHOR>
 * @since 2024/5/24 13:59
 */
public enum FlowSystemType {

    FLOW_CLASS("业务域"),
    FLOW_GROUP("流程类"),
    FLOW("流程组"),
    SUB_FLOW("流程");

    private String flowSystemTypeName;

    FlowSystemType(String flowSystemTypeName){
        this.flowSystemTypeName = flowSystemTypeName;
    }

    public String getFlowSystemTypeName(){
        return this.flowSystemTypeName;
    }

    public static Boolean exitByCode(String flowSystemTypeName){
        for (FlowSystemType value : FlowSystemType.values()) {
            if(value.flowSystemTypeName.equalsIgnoreCase(flowSystemTypeName)){
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }

}
