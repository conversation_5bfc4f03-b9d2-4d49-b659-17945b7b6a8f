package com.uinnova.product.vmdb.comm.util;

import javax.crypto.Cipher;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESKeySpec;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.security.Key;
import java.security.NoSuchAlgorithmException;
import java.security.spec.InvalidKeySpecException;
import java.util.Base64;

/**
 * 基于BASE64的DES
 * 
 * <pre>
 *  //前台使用 CryptoJS 
 *   var keyHex = CryptoJS.enc.Utf8.parse('key');
 *  
 *   var encrypted = CryptoJS.DES.encrypt('message', keyHex, {
 *       mode: CryptoJS.mode.ECB,
 *       padding: CryptoJS.pad.Pkcs7
 *   });
 *   console.log(encrypted.toString(), encrypted.ciphertext.toString(CryptoJS.enc.Base64));
 * 
 *   var keyHex = CryptoJS.enc.Utf8.parse('abcd1234');
 *   // direct decrypt ciphertext
 *   var decrypted = CryptoJS.DES.decrypt({
 *      ciphertext: CryptoJS.enc.Base64.parse('8dKft9vkZ4I=')
 *  }, keyHex, {
 *       mode: CryptoJS.mode.ECB,
 *       padding: CryptoJS.pad.Pkcs7
 *   });
 * </pre>
 * 
 * 
 * <AUTHOR>
 *
 */
public class CryptoUtils {

    private static final String UTF8 = "UTF-8";

    private static final String KEY_ALGORITHM = "AES";

    // TODO 由于前段插件只支持PKCS7Padding,而后台默认并不支持7.然后时间上来不及,所以暂时使用5来解析前段的7
    private static final String CIPHER_ALGORITHM = "AES/CBC/PKCS5Padding";

    // 默认的secretKey,前端的默认值也应该是一样的.保存16位
    public static final String DEFAULT_SECURE_KEY = "012uinnova123456";

    // 默认偏移 保持16位
    public static final String DEFAULT_IV = "0123456789ABCDEF";

    public static final String DEFAULT_KEY = "base@uinnova";

    private static final String KEY_DES = "DES";

    private static Key getToKey(String key, String keyType) throws Exception {
        SecretKey secretKey = null;
        DESKeySpec dks = new DESKeySpec(key.getBytes(UTF8));
        SecretKeyFactory factory = SecretKeyFactory.getInstance(keyType);
        secretKey = factory.generateSecret(dks);
        return secretKey;
    }

    public static String encryptDES(String msg, String key) {
        try {
            Key k = getToKey(key, KEY_DES);
            Cipher cipher = Cipher.getInstance(KEY_DES);
            cipher.init(Cipher.ENCRYPT_MODE, k);
            byte[] bytes = cipher.doFinal(msg.getBytes(UTF8));
            return new String(Base64.getEncoder().encode(bytes), UTF8);
        } catch (Exception e) {
            return null;
        }
    }

    public static String decryptDES(String msg, String key) {
        try {
            Key k = getToKey(key, KEY_DES);
            Cipher cipher = Cipher.getInstance(KEY_DES);
            cipher.init(Cipher.DECRYPT_MODE, k);
            byte[] bytes = cipher.doFinal(Base64.getDecoder().decode(msg.getBytes(UTF8)));
            return new String(bytes, UTF8);
        } catch (Exception e) {
            return null;
        }
    }

    public static String encryptAES(String msg) {
        return encryptAES(msg, DEFAULT_SECURE_KEY, DEFAULT_IV);
    }

    public static String decryptAES(String msg) {
        return decryptAES(msg, DEFAULT_SECURE_KEY, DEFAULT_IV);
    }

    public static String encryptAES(String msg, String key, String iv) {
        key = key == null ? DEFAULT_SECURE_KEY : key;
        iv = iv == null ? DEFAULT_IV : iv;
        try {
            Cipher cipher = Cipher.getInstance(CIPHER_ALGORITHM);
            cipher.init(Cipher.ENCRYPT_MODE, new SecretKeySpec(key.getBytes(UTF8), KEY_ALGORITHM),
                    new IvParameterSpec(iv.getBytes(UTF8)));
            byte[] bytes = cipher.doFinal(msg.getBytes(UTF8));
            return new String(Base64.getEncoder().encode(bytes), UTF8);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static String decryptAES(String msg, String key, String iv) {
        key = key == null ? DEFAULT_SECURE_KEY : key;
        iv = iv == null ? DEFAULT_IV : iv;
        try {
            Cipher cipher = Cipher.getInstance(CIPHER_ALGORITHM);
            cipher.init(Cipher.DECRYPT_MODE, new SecretKeySpec(key.getBytes(UTF8), KEY_ALGORITHM),
                    new IvParameterSpec(iv.getBytes(UTF8)));
            byte[] bytes = cipher.doFinal(Base64.getDecoder().decode(msg.getBytes(UTF8)));
            return new String(bytes, UTF8);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static void main(String[] args)
            throws NoSuchAlgorithmException, InvalidKeySpecException, NoSuchPaddingException {
        String e;
        System.out.println(e = encryptAES("admin@1233_(#!87*&#^"));
        System.out.println(decryptAES(e));
        System.out.println(decryptAES("GQEvLjNQEGppW1a4ICPWBQ=="));
        
        System.out.println("DES 加密 \"XIONG\",结果 : " + (e = encryptDES("XIONG", "xiongjian")));
        System.out.println("DES 解密 \"2q783sl69Hs=\" : "+decryptDES(e, "xiongjian"));
        
    }
}
