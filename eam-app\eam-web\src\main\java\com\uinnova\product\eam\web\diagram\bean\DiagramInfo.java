package com.uinnova.product.eam.web.diagram.bean;

import java.util.List;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.model.VcDiagramInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;

public class DiagramInfo{

	
	@Comment("视图ci信息列表")
	private List<CcCiInfo> ciInfos;
	
	@Comment("视图信息")
	private VcDiagramInfo diagramInfo;
	

	public VcDiagramInfo getDiagramInfo() {
		return diagramInfo;
	}

	public void setDiagramInfo(VcDiagramInfo diagramInfo) {
		this.diagramInfo = diagramInfo;
	}

	public List<CcCiInfo> getCiInfos() {
		return ciInfos;
	}

	public void setCiInfos(List<CcCiInfo> ciInfos) {
		this.ciInfos = ciInfos;
	}
	
	

}
