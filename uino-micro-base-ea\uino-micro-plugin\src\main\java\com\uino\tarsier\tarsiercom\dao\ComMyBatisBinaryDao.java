package com.uino.tarsier.tarsiercom.dao;

import java.util.List;
import java.util.Map;

import com.binary.framework.bean.Condition;
import com.binary.framework.bean.EntityBean;
import com.binary.framework.dao.DaoDefinition;
import com.binary.jdbc.Page;


public interface ComMyBatisBinaryDao <E extends EntityBean, F extends Condition>{
	/**
	 * 获取DAO定义对象
	 * @return
	 * @throws Exception 
	 */
	public DaoDefinition getDaoDefinition();
	
	/**
	 * 分页查询
	 * @param pageNum : 指定页码
	 * @param pageSize : 指定页行数
	 * @param cdt : 条件对象
	 * @param orders : 排序字段
	 * @return 
	 * @throws Exception 
	 */
	public Page<E> selectPage(long pageNum, long pageSize, F cdt, String orders);
	
	
	/**
	 * 不带count分页查询
	 * @param pageNum : 指定页码
	 * @param pageSize : 指定页行数
	 * @param cdt : 条件对象
	 * @param orders : 排序字段
	 * @return 
	 * @throws Exception 
	 */
	public List<E> selectList(long pageNum, long pageSize, F cdt, String orders);


	/**
	 * 不分页查询
	 * @param cdt : 条件对象
	 * @param orders : 排序字段
	 * @return 
	 * @throws Exception 
	 */
	public List<E> selectList(F cdt, String orders);


	/**
	 * 查询数据行数
	 * @param cdt : 条件对象
	 * @return 查询行数
	 * @throws Exception 
	 */
	public long selectCount(F cdt);


	/**
	 * 跟据主键查询
	 * @param id: 主键值
	 * @return 
	 * @throws Exception 
	 */
	public E selectById(long id);


	/**
	 * 插入记录
	 * @param record : 点映射对象
	 * @return 新插入记录的主键值
	 * @throws Exception 
	 */
	public long insert(E record);


	/**
	 * 批量插入记录
	 * @param records : 映射对象列表
	 * @return 新插入记录的主键值列表
	 * @throws Exception 
	 */
	public long[] insertBatch(List<E> records);


	/**
	 * 跟据主键更新记录
	 * @param record : 更新的映射对象
	 * @param id : 主键值
	 * @return 新插入记录的主键值列表
	 * @throws Exception 
	 */
	public int updateById(E record, long id);


	/**
	 * 跟据条件更新记录
	 * @param record : 更新的映射对象
	 * @param cdt : 条件对象
	 * @return 更新记录数
	 * @throws Exception 
	 */
	public int updateByCdt(E record, F cdt);


	/**
	 * 批量更新记录(跟据记录主键值), 如果指定更新字段, 则批量更新, 如果没有指定, 则逐各更新
	 * @param records : 更新的映射对象列表
	 * @param updateFields : 指定需更新字段
	 * @return 更新记录数列表
	 * @throws Exception 
	 */
	public int[] updateBatch(List<E> records);


	/**
	 * 根据主建删除记录
	 * @param id : 主键值
	 * @return 删除记录数
	 * @throws Exception 
	 */
	public int deleteById(long id);


	/**
	 * 跟据条件删除记录
	 * @param cdt : 条件对象
	 * @return 删除记录数
	 * @throws Exception 
	 */
	public int deleteByCdt(F cdt);


	/**
	 * 批量删除记录
	 * @param ids : 主键值列表
	 * @return 删除记录数列表
	 * @throws Exception 
	 */
	public int[] deleteBatch(long[] ids);


	/**
	 * 保存记录, 跟据record.id是否为empty来判断是做insert or update
	 * @param record : 映射对象
	 * @return 保存记录的主键值
	 * @throws Exception 
	 */
	public long save(E record);


	/**
	 * 批量保存, 跟据record.id是否为empty来判断是做insert or update
	 * @param records : 映射对象列表
	 * @return 保存记录的主键值列表
	 * @throws Exception 
	 */
	public long[] saveBatch(List<E> records);

	/**
	 * 分页查询查询
	 * @param sqlstatement : 执行的SQL名称
	 * @param beanClass : 返回的bean类型
	 * @return Page<Bean>返回类型
	 */	
	public Page<E> selectByPage(String statementName, Object parameterObject,long pageNum,long pageSize);
	
	
	/**分页查询
	 * @param statementName : 执行的SQL名称
	 * @param mapParameter : SQL参数
	 * @param orderby : 排序字段
	 * @param pageNum : 页序
	 * @param pageSize : 每页条数
	 * @param beanClass : 指定Bean类型
	 * @return Page<Bean>返回类型
	 */
	public <T> Page<T> selectByPageOrderby(String statementName, Map<String,Object> mapParameter, String orderby ,long pageNum,long pageSize,Class<T> beanClass);
	
	/**分页查询
	 * @param statementName : 执行的SQL名称
	 * @param mapParameter : SQL参数
	 * @param pageNum : 页序
	 * @param pageSize : 每页条数
	 * @param beanClass : 指定Bean类型
	 * @return Page<Bean>返回类型
	 */
	public <T> Page<T> selectByPage(String statementName, Map<String,Object> mapParameter, long pageNum,long pageSize,Class<T> beanClass);

	/**
	 *执行SQL查询
	 * @param statementid : MyBatis statementid,需要设置为: DAO类名.方法名+ID区别
	 * @param sql : 执行的SQL
	 * @return List<Map>形式查询结果
	 * @throws Exception 
	 */
	public List<Map<String,Object>> executeQuery(String statementid, String sql);
	
	/**执行SQL查询
	 * @param statementid : MyBatis statementid,需要设置为: DAO类名.方法名+ID区别
	 * @param sql : 执行的SQL
	 * @param mapParams : SQL参数
	 * @return List<Map>形式查询结果
	 * @throws Exception
	 */
	public List<Map<String,Object>> executeQueryParam(String statementid, String sql, Map<String,Object> mapParams);
	/**
	 * 执行SQL查询
	 * @param statementid : MyBatis statementid,需要设置为: DAO类名.方法名
	 * @param sqlstatement : 执行的SQL
	 * @param beanClass : 返回的bean类型
	 * @return List<Bean>形式查询结果
	 * @throws Exception 
	 */
	public <T> List<T> executeQueryBean(String statementid, String sql,Class<T> beanClass);


	
	
	/**执行SQL查询 
	 * @param statementid : MyBatis statementid,需要设置为: DAO类名.方法名
	 * @param prepareSql : 执行的SQL
	 * @param params : SQL参数
	 * @param beanClass : 返回的bean类型
	 * @return List<Bean>形式查询结果
	 * @throws Exception
	 */
	public <T> List<T> executeQueryBeanByParam(String statementid, String sql, Map<String,Object> mapParams, Class<T> beanClass);
	
	/**
	 * 执行更新SQL
	 * @param statementid : MyBatis statementid,需要设置为: DAO类名.方法名
	 * @param prepareSql : 执行的SQL
	 * @param params : SQL参数
	 * @return 影响记录条数
	 * @throws Exception 
	 */
	public int executeUpdateByParam(String statementid, String sql, Map<String,Object> mapParams);
	
	/**
	 * 执行插入SQL
	 * @param statementid
	 * @param sql
	 * @param mapParams
	 * @return 影响记录条数
	 * @throws Exception
	 */
	public int executeInsertByParam(String statementid, String sql, Map<String,Object> mapParams);
	
	/**
	 * 执行删除SQL
	 * @param statementid
	 * @param sql
	 * @param mapParams
	 * @return 影响记录条数
	 * @throws Exception
	 */
	public int executeDeleteByParam(String statementid, String sql, Map<String,Object> mapParams);
//	/**
//	 * 批量SQL转换
//	 * @param sql : 执行的SQL
//	 * @param beanList : SQL参数
//	 * @param paramClass : 参数类类型
//	 * @return 转换结果SQL
//	 * @throws Exception 
//	 */
//	public <T> List<String> parseBatchSQL(String sql, List<T> beanList, Class<T> paramClass);
//	
//	/**
//	 * 批量保存, 跟据指定验证字段到数据库中会查一遍记录是否存在, 从而判断是insert or update
//	 * @param records : 映射对象列表
//	 * @param verifyField : 验证字段
//	 * @return 保存记录的主键值列表
//	 */
//	public long[] saveBatch(List<E> records, String verifyField);
	
	
	public void startTransaction() ;
	
	public void commitTransaction() ;
	
	public void endTransaction() ;
}
