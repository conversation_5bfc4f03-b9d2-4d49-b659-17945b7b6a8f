package com.uino.dao.cmdb;

import com.uino.bean.cmdb.base.ESCiClassEncode;
import com.uino.bean.cmdb.base.ESCiClassRlt;
import com.uino.bean.cmdb.query.ESCiClassRltSearchBean;
import com.uino.dao.AbstractESBaseDao;
import com.uino.dao.ESConst;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;

/**
 * CI分类编码字段类型dao
 * <AUTHOR>
 */
@Service
@Slf4j
public class ESCiClassEncodeSvc extends AbstractESBaseDao<ESCiClassEncode, ESCiClassEncode> {

    @Override
    public String getIndex() {
        return ESConst.INDEX_CMDB_CICLASS_ENCODE;
    }

    @Override
    public String getType() {
        return "_doc";
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }
}
