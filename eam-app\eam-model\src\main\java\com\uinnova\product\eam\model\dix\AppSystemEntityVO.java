package com.uinnova.product.eam.model.dix;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class AppSystemEntityVO implements Serializable {
    @Comment("实体id")
    private String id;
    @Comment("实体中文名称")
    private String cnName;
    @Comment("实体英文名称")
    private String enName;
    @Comment("新增:0/更新:1")
    private Integer status;
    @Comment("实体名称")
    private List<AppSystemAttributeVO> attributes = new ArrayList<>();
    @Comment("删除的实体属性id集合")
    private List<String> delAttributeIds = new ArrayList<>();

    public AppSystemEntityVO() {
    }

    public AppSystemEntityVO(String id, String cnName, String enName, Integer status) {
        this.id = id;
        this.cnName = cnName;
        this.enName = enName;
        this.status = status;
    }
}
