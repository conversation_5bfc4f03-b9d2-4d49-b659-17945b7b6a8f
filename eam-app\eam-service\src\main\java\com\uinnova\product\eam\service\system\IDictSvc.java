package com.uinnova.product.eam.service.system;

import com.uinnova.product.eam.base.model.DictInfo;

import java.util.List;
import java.util.Map;

public interface IDictSvc {
    List<DictInfo> selectListByType(List<String> codeTypes, String parentCode, String className);

    Map<String, List<DictInfo>> selectGroupList(List<String> codeTypes, String className);

    Map<String, String> getAllInfo(List<String> codeTypes,String className);
}
