package com.uino.web.express.mvc;

import com.uino.bean.express.ExpressDTO;
import com.uino.bean.express.ExpressExecuteResult;
import com.uino.bean.express.FunctionQueryDto;
import com.uino.init.api.ApiResult;
import com.uino.init.api.ApiVersion;
import com.uino.util.express.AviatorFunctionExecutor;
import com.uino.util.express.common.FunctionCollectionDesc;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@ApiVersion(1)
@Api(value = "表达式调用接口", tags={"计算表达式"})
@RestController
@RequestMapping("/express/")
public class ExpressMvc {

	@GetMapping("/getAllLoaded")
	@ApiOperation(value="获取所有已加载计算公式")
	public ApiResult<List<FunctionCollectionDesc>> getAllLoaded() {
		List<FunctionCollectionDesc> result = AviatorFunctionExecutor.getAllLoadedFunctions();
		return ApiResult.ok(this).data(result);
	}

	@PostMapping("/getLoadedByTags")
	@ApiOperation(value="通过标签获取已加载公式", notes = "参数为标签数组，不传时返回所有")
	public ApiResult<List<FunctionCollectionDesc>> getAllLoaded(@RequestBody FunctionQueryDto body) {
		List<FunctionCollectionDesc> result = AviatorFunctionExecutor.getLoadedFunctionsByTags(body.getTags());
		return ApiResult.ok(this).data(result);
	}

    @PostMapping("/calculate")
    @ApiOperation(value="表达式计算")
    public ApiResult<ExpressExecuteResult> calculate(@RequestBody ExpressDTO expressDTO) {
		ExpressExecuteResult result = AviatorFunctionExecutor.calculate(expressDTO);
		return ApiResult.ok(this).data(result);
    }

	@PostMapping("/validate")
	@ApiOperation(value="验证表达式是否合法，表达式中方法是否在系统中存在")
	public ApiResult<Boolean> validateExpress(@RequestBody ExpressDTO expressDTO) {
		AviatorFunctionExecutor.validateExpress(expressDTO.getExpress(), expressDTO.getParamNames());
		return ApiResult.ok(this).data(true);
	}
}
