package com.uinnova.product.vmdb.comm.i18n;

import com.binary.core.exception.MessageException;
import com.binary.core.i18n.LanguageResolver;
import com.binary.core.lang.Conver;
import com.binary.core.util.BinaryUtils;
import com.uinnova.product.vmdb.comm.util.CommUtil;

import java.util.HashMap;
import java.util.Map;

/**
 * 用于基本的i18n转化和直接转化为异常
 * 
 * <AUTHOR>
 *
 */
public abstract class MessageUtil {

    /**
     * 验证参数是否为null
     * 
     * @param value
     *            验证对象
     * @param fieldName
     *            字段名
     */
    public static void checkNull(Object value, String fieldName) {
        if (value == null) {
            throwVerify(VerifyType.NULL, fieldName, null);
        }
    }

    /**
     * 主要针对验证数组项目是否为null
     * 
     * @param value
     *            验证对象
     * @param fieldName
     *            字段名
     */
    public static void checkNull(Object value, String fieldName, int idx) {
        if (value == null) {
            throwVerify(VerifyType.NULL, fieldName, idx);
        }
    }

    /**
     * 验证参数是否为空
     * 
     * @param value
     *            验证对象
     * @param fieldName
     *            字段名
     */
    public static void checkEmpty(Object value, String fieldName) {
        checkEmpty(value, true, fieldName);
    }

    /**
     * 验证参数是否为空
     * 
     * @param value
     *            验证对象
     * @param trim
     *            如果验证对象为string时是否需要trim之后再验证
     * @param fieldName
     *            字段名
     */
    public static void checkEmpty(Object value, boolean trim, String fieldName) {
        if (BinaryUtils.isEmpty(value, trim)) {
            throwVerify(VerifyType.EMPTY, fieldName, null);
        }
    }

    /**
     * 验证参数是否为空
     * 
     * @param value
     *            验证对象
     * @param fieldName
     *            字段名
     * @param idx
     */
    public static void checkEmpty(Object value, String fieldName, int idx) {
        checkEmpty(value, true, fieldName, idx);
    }

    /**
     * 验证参数是否为空
     * 
     * @param value
     *            验证对象
     * @param trim
     *            如果验证对象为string时是否需要trim之后再验证
     * @param fieldName
     *            字段名
     * @param idx
     *            数组下标
     */
    public static void checkEmpty(Object value, boolean trim, String fieldName, int idx) {
        if (BinaryUtils.isEmpty(value, trim)) {
            throwVerify(VerifyType.EMPTY, fieldName, idx);
        }
    }

    /**
     * 抛出验证异常
     * 
     * @param verifyType
     *            验证类型
     * @param fieldName
     *            验证字段名
     * @param value
     *            字段值 例语言包中定义语言项: 您所添加的${userName}:[${value}]已存在
     */
    public static void throwVerify(VerifyType verifyType, String fieldName, Object value) {
        MessageUtil.checkEmpty(verifyType, "verifyType");

        if (fieldName != null) {
            fieldName = fieldName.trim();
        }
        String bsMname = "BS_MNAME_";
        if (!BinaryUtils.isEmpty(fieldName) && !fieldName.startsWith(bsMname)) {
            fieldName = "BS_MNAME_" + fieldName.toUpperCase();
        }

        String name = LanguageResolver.trans(fieldName);
        String type = LanguageResolver.trans("BS_MVTYPE_" + verifyType.name());
        if (value != null && value != "") {
            // value等于""空字符串时,出现XXX[]不能为空 这种带[]的情况
            value = "[" + Conver.to(value, String.class) + "]";
        } else {
            // 防止出现XXXnull不能为空 这种带null的，给空格防止 值和提示语挨得太紧
            value = " ";
        }

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("name", name);
        map.put("value", value);
        map.put("type", type);
        throw MessageException.i18n("BS_VERIFY_ERROR", map);
    }

    public static void checkOrderField(String orders) {
        if (BinaryUtils.isEmpty(orders)) {
            return;
        }

        if (!CommUtil.isOrderyBy(orders)) {
            throw MessageException.i18n("BS_ORDERBY_ERROR");
        }
    }

    /**
     * 获取指定国际化语言
     * 
     * @param languageCode
     *            语言代码
     * @return
     */
    public static String trans(String languageCode) {
        return I18nMediatorResolver.trans(languageCode);
    }

    /**
     * 获取指定国际化语言
     * 
     * @param languageCode
     *            语言代码
     * @param jsonParams
     *            语言中动态参数(JSON格式)
     * @return
     */
    public static String trans(String languageCode, String jsonParams) {
        return I18nMediatorResolver.trans(languageCode, jsonParams);
    }

    /**
     * 获取指定国际化语言
     * 
     * @param languageCode
     *            语言代码
     * @param params
     *            语言中动态参数, Bean or Map
     * @return
     */
    public static String trans(String languageCode, Object params) {
        return I18nMediatorResolver.trans(languageCode, params);
    }

}
