package com.uinnova.product.vmdb.provider.quality.bean;

import java.util.List;

/**
 * 质量检查配置定义
 */
public class QualityCheckDefine {

    private static final long serialVersionUID = 1L;

    /**
     * 来源定义
     */
    private List<SourceDefine> sourceDefine;

    /**
     * 孤儿规则定义
     */
    private List<OrphanCheckDefine> orphanCheckDefine;

    /**
     * 外部属性定义
     */
    private List<ExternalCheckDefine> externalCheckDefine;

	/**
	 * 权重系数，默认1.0
	 */
	private WeightCoefficient weightCoefficient;

    /**
     * 来源列表
     */
    private List<String> source;

    public static class SourceDefine {

        /**
         * 分类名
         */
        private String className;

        /**
         * 属性名
         */
        private String proName;

        public String getClassName() {
            return className;
        }

        public void setClassName(String className) {
            this.className = className;
        }

        public String getProName() {
            return proName;
        }

        public void setProName(String proName) {
            this.proName = proName;
        }
    }

    public static class OrphanCheckDefine {

        /**
         * 源分类
         */
        private OrphanCheckClassDefine sourceCiClass;

        /**
         * 关系
         */
        private String rlt;

        /**
         * 目标分类
         */
        private OrphanCheckClassDefine targetCiClass;

        public OrphanCheckClassDefine getSourceCiClass() {
            return sourceCiClass;
        }

        public void setSourceCiClass(OrphanCheckClassDefine sourceCiClass) {
            this.sourceCiClass = sourceCiClass;
        }

        public String getRlt() {
            return rlt;
        }

        public void setRlt(String rlt) {
            this.rlt = rlt;
        }

        public OrphanCheckClassDefine getTargetCiClass() {
            return targetCiClass;
        }

        public void setTargetCiClass(OrphanCheckClassDefine targetCiClass) {
            this.targetCiClass = targetCiClass;
        }
    }

    public static class ExternalCheckDefine {

        /**
         * 分类名
         */
        private String ciClassName;

        /**
         * 属性名
         */
        private String ciClassProName;

        /**
         * 外部分类名
         */
        private String externalCiClassName;

        /**
         * 外部属性名
         */
        private String externalCiClassProName;

        public String getCiClassName() {
            return ciClassName;
        }

        public void setCiClassName(String ciClassName) {
            this.ciClassName = ciClassName;
        }

        public String getCiClassProName() {
            return ciClassProName;
        }

        public void setCiClassProName(String ciClassProName) {
            this.ciClassProName = ciClassProName;
        }

        public String getExternalCiClassName() {
            return externalCiClassName;
        }

        public void setExternalCiClassName(String externalCiClassName) {
            this.externalCiClassName = externalCiClassName;
        }

        public String getExternalCiClassProName() {
            return externalCiClassProName;
        }

        public void setExternalCiClassProName(String externalCiClassProName) {
            this.externalCiClassProName = externalCiClassProName;
        }
    }

    public static class WeightCoefficient {

    	public WeightCoefficient() {
    		this.completeness = 1.0;
    		this.orphan = 1.0;
    		this.validity = 1.0;
		}

		/**
		 * 完整性系数
		 */
		Double completeness = 1.0;

		/**
		 * 有效性系数
		 */
		Double validity = 1.0;

		/**
		 * 孤岛系数
		 */
		Double orphan = 1.0;

		public Double getCompleteness() {
			return completeness;
		}

		public void setCompleteness(Double completeness) {
			this.completeness = completeness;
		}

		public Double getValidity() {
			return validity;
		}

		public void setValidity(Double validity) {
			this.validity = validity;
		}

		public Double getOrphan() {
			return orphan;
		}

		public void setOrphan(Double orphan) {
			this.orphan = orphan;
		}
	}

    public List<SourceDefine> getSourceDefine() {
        return sourceDefine;
    }

    public void setSourceDefine(List<SourceDefine> sourceDefine) {
        this.sourceDefine = sourceDefine;
    }

    public List<OrphanCheckDefine> getOrphanCheckDefine() {
        return orphanCheckDefine;
    }

    public void setOrphanCheckDefine(List<OrphanCheckDefine> orphanCheckDefine) {
        this.orphanCheckDefine = orphanCheckDefine;
    }

    public List<ExternalCheckDefine> getExternalCheckDefine() {
        return externalCheckDefine;
    }

    public void setExternalCheckDefine(List<ExternalCheckDefine> externalCheckDefine) {
        this.externalCheckDefine = externalCheckDefine;
    }

	public WeightCoefficient getWeightCoefficient() {
		return weightCoefficient;
	}

	public void setWeightCoefficient(WeightCoefficient weightCoefficient) {
		this.weightCoefficient = weightCoefficient;
	}

	public List<String> getSource() {
        return source;
    }

    public void setSource(List<String> source) {
        this.source = source;
    }
}
