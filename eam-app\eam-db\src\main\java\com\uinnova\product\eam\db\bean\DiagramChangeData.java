package com.uinnova.product.eam.db.bean;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uino.bean.cmdb.base.ESCIRltInfo;

@Comment("视图私有库/设计库要素信息")
public class DiagramChangeData {

	/**
	 * 对象信息
	 */
	@Comment("单条私有库 CI 信息")
	private CcCiInfo privateCiInfo;

	@Comment("单条设计库 CI 信息")
	private CcCiInfo desginCiInfo;

	@Comment("单条私有库 RLT 信息")
	private ESCIRltInfo privateRltInfo;

	@Comment("单条设计库 RLT 信息")
	private ESCIRltInfo desginRltInfo;

	public DiagramChangeData(CcCiInfo privateCiInfo, CcCiInfo desginCiInfo, ESCIRltInfo privateRltInfo, ESCIRltInfo desginRltInfo) {
		this.privateCiInfo = privateCiInfo;
		this.desginCiInfo = desginCiInfo;
		this.privateRltInfo = privateRltInfo;
		this.desginRltInfo = desginRltInfo;
	}

	public CcCiInfo getPrivateCiInfo() {
		return privateCiInfo;
	}

	public void setPrivateCiInfo(CcCiInfo privateCiInfo) {
		this.privateCiInfo = privateCiInfo;
	}

	public CcCiInfo getDesginCiInfo() {
		return desginCiInfo;
	}

	public void setDesginCiInfo(CcCiInfo desginCiInfo) {
		this.desginCiInfo = desginCiInfo;
	}

	public ESCIRltInfo getPrivateRltInfo() {
		return privateRltInfo;
	}

	public void setPrivateRltInfo(ESCIRltInfo privateRltInfo) {
		this.privateRltInfo = privateRltInfo;
	}

	public ESCIRltInfo getDesginRltInfo() {
		return desginRltInfo;
	}

	public void setDesginRltInfo(ESCIRltInfo desginRltInfo) {
		this.desginRltInfo = desginRltInfo;
	}
}
