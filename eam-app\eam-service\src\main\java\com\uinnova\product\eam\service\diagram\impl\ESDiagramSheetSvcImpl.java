package com.uinnova.product.eam.service.diagram.impl;

import com.uinnova.product.eam.base.diagram.model.ESDiagramSheetDTO;
import com.uinnova.product.eam.db.diagram.es.ESDiagramSheetDao;
import com.uinnova.product.eam.service.diagram.ESDiagramSheetSvc;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * 视图sheet页接口业务实现
 * <AUTHOR>
 */
@Service
@Slf4j
public class ESDiagramSheetSvcImpl implements ESDiagramSheetSvc {
    @Autowired
    private ESDiagramSheetDao diagramSheetDao;

    @Override
    public List<ESDiagramSheetDTO> getSheetByDiagram(Collection<Long> diagramIds) {
        Assert.notEmpty(diagramIds, "视图id");
        BoolQueryBuilder nodeQuery = QueryBuilders.boolQuery();
        nodeQuery.must(QueryBuilders.termsQuery("diagramId", diagramIds));
        List<ESDiagramSheetDTO> sheetList = diagramSheetDao.getListByQueryScroll(nodeQuery);
        if(CollectionUtils.isEmpty(sheetList)){
            return Collections.emptyList();
        }
        return sheetList;
    }

    @Override
    public Integer saveOrUpdateBatch(List<ESDiagramSheetDTO> sheets) {
        return diagramSheetDao.saveOrUpdateBatch(sheets);
    }

    @Override
    public void deleteByDiagramIds(Collection<Long> diagramIds){
        if(CollectionUtils.isEmpty(diagramIds)){
            return;
        }
        diagramSheetDao.deleteByQuery(QueryBuilders.termsQuery("diagramId", diagramIds), true);
    }
}
