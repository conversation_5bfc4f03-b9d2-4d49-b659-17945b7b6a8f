package com.uinnova.product.vmdb.comm.model.kpi;

import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;

/**
 * 
 * <AUTHOR>
 *
 */
@Comment("KPI对象组关系表[CC_KPI_CI_GROUP]")
public class CcKpiCiGroup implements EntityBean {
    private static final long serialVersionUID = 1L;

    @Comment("ID[ID]")
    private Long id;

    @Comment("KPI_ID[KPI_ID]")
    private Long kpiId;

    @Comment("对象组类型[OBJ_GROUP_TYPE]    1=标签 2=CI分类 3=关系")
    private Integer objGroupType;

    @Comment("对象组ID[OBJ_GROUP_ID]")
    private Long objGroupId;

    @Comment("所属域[DOMAIN_ID]")
    private Long domainId;

    @Comment("数据状态[DATA_STATUS]    0=删除，1=正常")
    private Integer dataStatus;

    @Comment("创建时间[CREATE_TIME]    yyyyMMddHHmmss")
    private Long createTime;

    @Comment("更新时间[MODIFY_TIME]    yyyyMMddHHmmss")
    private Long modifyTime;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getKpiId() {
        return this.kpiId;
    }

    public void setKpiId(Long kpiId) {
        this.kpiId = kpiId;
    }

    public Integer getObjGroupType() {
        return this.objGroupType;
    }

    public void setObjGroupType(Integer objGroupType) {
        this.objGroupType = objGroupType;
    }

    public Long getObjGroupId() {
        return this.objGroupId;
    }

    public void setObjGroupId(Long objGroupId) {
        this.objGroupId = objGroupId;
    }

    public Long getDomainId() {
        return this.domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }

    public Integer getDataStatus() {
        return this.dataStatus;
    }

    public void setDataStatus(Integer dataStatus) {
        this.dataStatus = dataStatus;
    }

    public Long getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getModifyTime() {
        return this.modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }

}
