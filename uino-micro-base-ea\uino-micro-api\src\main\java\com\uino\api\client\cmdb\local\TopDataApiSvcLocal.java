package com.uino.api.client.cmdb.local;

import com.uino.dao.BaseConst;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.uino.service.cmdb.microservice.ITopDataSvc;
import com.uino.api.client.cmdb.ITopDataApiSvc;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
public class TopDataApiSvcLocal implements ITopDataApiSvc {
	@Autowired
	private ITopDataSvc svc;

	@Override
	public void unTop(Long topData, Long topDataType) {
		svc.unTop(BaseConst.DEFAULT_DOMAIN_ID,topData, topDataType);
	}

	@Override
	public void unTop(Long domainId, Long topData, Long topDataType) {
		svc.unTop(domainId,topData, topDataType);
	}

	@Override
	public void top(Long topData, Long topDataType) {
		svc.top(BaseConst.DEFAULT_DOMAIN_ID,topData, topDataType);
	}

	@Override
	public void top(Long domainId, Long topData, Long topDataType) {
		svc.top(domainId,topData, topDataType);
	}

}
