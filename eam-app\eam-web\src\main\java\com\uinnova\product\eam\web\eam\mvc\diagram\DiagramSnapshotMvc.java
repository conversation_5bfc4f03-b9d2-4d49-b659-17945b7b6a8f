package com.uinnova.product.eam.web.eam.mvc.diagram;


import com.alibaba.fastjson.JSONObject;
import com.binary.framework.web.RemoteResult;
import com.uinnova.product.eam.base.diagram.model.ESResponseStruct;
import com.uinnova.product.eam.model.asset.EamCiRltDTO;
import com.uinnova.product.eam.model.dto.DiagramSnapshotVO;
import com.uinnova.product.eam.model.dto.ExtendSheetInfo;
import com.uinnova.product.eam.service.DiagramMainSvc;
import com.uinnova.product.eam.service.DiagramSheetSvc;
import com.uinnova.product.eam.service.DiagramSnapshotSvc;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uino.util.sys.SysUtil;
import org.apache.http.util.Asserts;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 *  视图-快照
 */
@RestController
@RequestMapping("/eam/diagram")
public class DiagramSnapshotMvc {

    @Resource
    DiagramSnapshotSvc diagramSnapshotSvc;
    @Resource
    DiagramMainSvc diagramMainSvc;
    @Resource
    DiagramSheetSvc diagramSheetSvc;

    @PostMapping("/main/saveOrUpdateDiagramComponent")
    @ModDesc(desc = "画布-批量更新视图组件", pDesc = "", rDesc = "视图信息", rType = RemoteResult.class)
    public RemoteResult saveOrUpdateDiagramComponent(@RequestBody String jsonStr) {
        Map<String, List<ESResponseStruct>> resultMap = diagramMainSvc.saveOrUpdateDiagramComponent(jsonStr);
        return new RemoteResult(resultMap);
    }

    @PostMapping("/sheet/copySheetInfo")
    @ModDesc(desc = "画布sheet页-复制sheet页信息", pDesc = "", rDesc = "视图信息", rType = RemoteResult.class)
    public RemoteResult copySheetInfo(@RequestBody String jsonStr) {
        ExtendSheetInfo esDiagramSheetDTO = diagramSheetSvc.copySheetInfo(jsonStr);
        return new RemoteResult(esDiagramSheetDTO);
    }

    @RequestMapping(value = "/snapshot/generateSnapshotByDiagramIds", method = {RequestMethod.POST})
    @ModDesc(desc = "批量创建视图版本", pDesc = "视图IDs", pType = Long.class, rDesc = "", rType = Long.class)
    public RemoteResult generateSnapshotByDiagramIds(@RequestBody String body, @RequestParam(defaultValue = "false") Boolean isAuto) {
        List<DiagramSnapshotVO> diagramSnapshotVOS = JSONObject.parseArray(body, DiagramSnapshotVO.class);
        Map<String, String> result = diagramSnapshotSvc.generateSnapshotByDiagramIds(diagramSnapshotVOS, SysUtil.getCurrentUserInfo(), isAuto);
        return new RemoteResult(result);
    }

    @RequestMapping(value = "/snapshot/querySnapshotCIByHistoryIds", method = {RequestMethod.POST})
    @ModDesc(desc = "根据快照（历史视图数据）IDS 查询快照内的CI数据", pDesc = "快照视图IDs", pType = Long.class, rDesc = "", rType = Long.class)
    public RemoteResult querySnapshotCIByHistoryIds(@RequestBody String body) {
        JSONObject jsonObject = JSONObject.parseObject(body);
        List<String> diagramIds = jsonObject.getObject("diagramIds", List.class);
        List<String> ciCodes = jsonObject.getObject("ciCodes", List.class);
        Map<String, List<CcCiInfo>> data = diagramSnapshotSvc.querySnapshotCIByHistoryIds(diagramIds, ciCodes);
        return new RemoteResult(data);
    }

    @RequestMapping(value = "/snapshot/querySnapshotRltByHistoryIds", method = {RequestMethod.POST})
    @ModDesc(desc = "根据快照（历史视图数据）IDS 查询快照内的RLT数据", pDesc = "快照视图IDs", pType = Long.class, rDesc = "", rType = Long.class)
    public RemoteResult querySnapshotRltByHistoryIds(@RequestBody String body) {
        JSONObject jsonObject = JSONObject.parseObject(body);
        List<String> diagramIds = jsonObject.getObject("diagramIds", List.class);
        List<String> rltCodes = jsonObject.getObject("rltCodes", List.class);
        Map<String, List<EamCiRltDTO>> data = diagramSnapshotSvc.querySnapshotRltByHistoryIds(diagramIds, rltCodes);
        return new RemoteResult(data);
    }

    @RequestMapping(value = "/snapshot/saveOrUpdateDiagramBySnapshotId", method = {RequestMethod.POST})
    @ModDesc(desc = "根据快照视图ID生成/更新主视图", pDesc = "快照视图IDs", pType = Long.class, rDesc = "", rType = Long.class)
    public RemoteResult saveOrUpdateDiagramBySnapshotId(@RequestBody String body) {
        JSONObject jsonObject = JSONObject.parseObject(body);
        String snapshotId = jsonObject.getString("diagramId");
        String type = jsonObject.getString("type");
        Asserts.notNull(snapshotId, "历史版本数据标识不能为空");
        Asserts.notNull(type, "操作类型参数不能为空");
        String mainId = diagramSnapshotSvc.saveOrUpdateDiagramBySnapshotId(snapshotId, type);
        return new RemoteResult(mainId);
    }

    @RequestMapping(value = "/snapshot/updateSnapshotInfo", method = {RequestMethod.POST})
    @ModDesc(desc = "更新快照基本信息", pDesc = "", pType = Long.class, rDesc = "", rType = Long.class)
    public RemoteResult updateSnapshotInfo(@RequestBody DiagramSnapshotVO snapshotVO) {
        Asserts.notNull(snapshotVO.getSnapshotName(), "名称不能为空");
        Asserts.notNull(snapshotVO.getSnapshotId(), "历史数据标识不能为空");
        return new RemoteResult(diagramSnapshotSvc.updateSnapshotInfo(snapshotVO));
    }

    @RequestMapping(value = "/snapshot/deleteSnapshotInfo", method = {RequestMethod.POST})
    @ModDesc(desc = "删除快照信息", pDesc = "", pType = Long.class, rDesc = "", rType = Long.class)
    public RemoteResult deleteSnapshotInfo(@RequestBody DiagramSnapshotVO snapshotVO) {
        Asserts.notNull(snapshotVO.getSnapshotId(), "历史数据标识不能为空");
        return new RemoteResult(diagramSnapshotSvc.deleteSnapshotInfo(snapshotVO));
    }

}
