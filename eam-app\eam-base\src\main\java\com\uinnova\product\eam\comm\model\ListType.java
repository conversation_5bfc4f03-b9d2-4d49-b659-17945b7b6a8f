package com.uinnova.product.eam.comm.model;

import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Comment("清单基本信息表")
public class ListType implements EntityBean {

    @Comment("id")
    private Long id;

    @Comment("类型名称")
    private String typeName;

    @Comment("创建者")
    private String creator;

    @Comment("排序字段")
    private Integer sort;

    @Comment("域")
    private Long domainId;

    @Comment("创建时间")
    private Long createTime;

    @Comment("更改时间")
    private Long modifyTime;

}
