package com.uinnova.product.eam.comm.bean;

import java.time.LocalDateTime;
import java.util.Objects;


public class WordDoc {

    private Long id;

    private String ciCode;

    private String docName;

    private Long userId;

    private String docType;

    private String savePth;

    private LocalDateTime updateTime;

    private String operator;

    private String fullPath;

    private Long domainId;

    private Long modifyTime;

    private Long createTime;

    private String modifier;

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getFullPath() {
        return fullPath;
    }

    public void setFullPath(String fullPath) {
        this.fullPath = fullPath;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCiCode() {
        return ciCode;
    }

    public void setCiCode(String ciCode) {
        this.ciCode = ciCode;
    }

    public String getDocName() {
        return docName;
    }

    public void setDocName(String docName) {
        this.docName = docName;
    }

    public String getDocType() {
        return docType;
    }

    public void setDocType(String docType) {
        this.docType = docType;
    }

    public String getSavePth() {
        return savePth;
    }

    public void setSavePth(String savePth) {
        this.savePth = savePth;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public Long getDomainId() {
        return domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }

    public Long getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof WordDoc)) return false;
        WordDoc wordDoc = (WordDoc) o;
        return Objects.equals(id, wordDoc.id) &&
                Objects.equals(ciCode, wordDoc.ciCode) &&
                Objects.equals(docName, wordDoc.docName) &&
                Objects.equals(docType, wordDoc.docType) &&
                Objects.equals(savePth, wordDoc.savePth) &&
                Objects.equals(updateTime, wordDoc.updateTime) &&
                Objects.equals(operator, wordDoc.operator) &&
                Objects.equals(fullPath, wordDoc.fullPath) &&
                Objects.equals(domainId, wordDoc.domainId) &&
                Objects.equals(modifier, wordDoc.modifier) &&
                Objects.equals(modifyTime, wordDoc.modifyTime) &&
                Objects.equals(createTime, wordDoc.createTime);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, ciCode, docName, docType, savePth, updateTime, operator, fullPath, domainId, modifier, modifyTime, createTime);
    }
}
