package com.uinnova.product.eam.db.diagram.es;

import com.uinnova.product.eam.base.diagram.model.DiagramShareLink;
import com.uinnova.product.eam.base.diagram.model.DiagramShareLinkQuery;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Repository;

import jakarta.annotation.PostConstruct;

/**
 * 文档管理数据访问层
 *
 * <AUTHOR>
 */
@Repository
public class ESShareLinkDao extends AbstractESBaseDao<DiagramShareLink, DiagramShareLinkQuery> {
    @Override
    public String getIndex() {
        return "uino_eam_share_link";
    }

    @Override
    public String getType() {
        return "_doc";
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }
}
