package com.uinnova.product.vmdb.comm.bean;

import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;

import java.io.Serializable;

/**
 * 
 * <AUTHOR>
 *
 * @param <E>
 */
@Comment("分页查询封装")
public class QueryPageCondition<E extends Condition> implements Serializable {
    private static final long serialVersionUID = 1L;

    @Comment("查询页码")
    private Integer pageNum;

    @Comment("查询每页大小")
    private Integer pageSize;

    @Comment("查询条件")
    private E cdt;

    @Comment("排序字段")
    private String orders;

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getOrders() {
        return orders;
    }

    public void setOrders(String orders) {
        this.orders = orders;
    }

    public E getCdt() {
        return cdt;
    }

    public void setCdt(E cdt) {
        this.cdt = cdt;
    }

}
