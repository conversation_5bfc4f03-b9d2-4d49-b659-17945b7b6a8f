package com.uino.bean.monitor.buiness;

import java.util.Set;

import com.uino.bean.monitor.base.ESAlarm;

import lombok.Data;

/**
 * 告警查询数据传输类
 * 
 * <AUTHOR>
 *
 */
@Data
public class AlarmQueryDto {

    /**
     * 是否为模拟数据
     */
    private Boolean mock;

    /**
     * 告警状态s0:开始1关闭2未确认3已确认4未派单5已派单6已通知7未通知
     * <p>
     * 
     * @see ESAlarm#status
     */
    private Set<Integer> status;

    /**
     * 告警级别id
     */
    private Set<Long> severityIds;

    /**
     * 告警时间起
     */
    private Long alarmTimeStart;

    /**
     * 告警时间止
     */
    private Long alarmTimeEnd;

    /**
     * 关键字
     */
    private String keyword;

    /**
     * 页码
     */
    private int pageNum = 1;

    /**
     * 每页行数
     */
    private int pageSize = 20;

    /**
     * 排序字段
     */
    private String order = "alarmTime";

    /**
     * 是否升序
     */
    private boolean asc;

    /**
     * 所属域
     */
    private Long domainId;
}
