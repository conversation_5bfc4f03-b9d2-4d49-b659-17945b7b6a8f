package com.uinnova.product.eam.feign.workable;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * 健康检查接口
 *
 * <AUTHOR>
 * @since 2022/2/22 15:28
 */
@FeignClient(name = "${flowable-feign-server-name:eam-flowable}"
        , path = "${flowable-feign-server-path:eam-flowable}/healthy")
public interface HealthyFeign {

    @GetMapping("/ping")
    String ping();
}
