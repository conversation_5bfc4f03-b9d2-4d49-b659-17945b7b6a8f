package com.uino.bean.sys.query;

import java.io.Serializable;
import java.util.Set;

import com.uino.bean.cmdb.query.ESSearchBase;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 
 * <AUTHOR>
 *
 */
@Setter
@Getter
@ApiModel(value="操作日志查询",description = "操作日志查询")
public class ESOperateLogSearchBean extends ESSearchBase implements Serializable {

    private static final long serialVersionUID = -1507528971994792973L;

    @ApiModelProperty(value="开始时间")
    private Long startTime;

    @ApiModelProperty(value="结束时间")
    private Long endTime;

    @ApiModelProperty(value="用户名",example = "admin")
    private String userName;

    @ApiModelProperty(value="操作描述")
    private String opDesc;

    @ApiModelProperty(value="模块")
    private Set<String> moduleNames;

    @ApiModelProperty(value="检索值",example = "user")
    private String keyword;

    @ApiModelProperty(value="所属域id",example = "123")
    private Long domainId;
}
