package com.uinnova.product.eam.db.impl;


import com.binary.framework.dao.DaoDefinition;

import com.uinnova.product.eam.comm.model.CVcMetaModelDiagram;
import com.uinnova.product.eam.comm.model.VcMetaModelDiagram;


/**
 * 元模型管理视图[VC_META_MODEL_DIAGRAM]数据访问对象定义实现
 */
public class VcMetaModelDiagramDaoDefinition implements DaoDefinition<VcMetaModelDiagram, CVcMetaModelDiagram> {


	@Override
	public Class<VcMetaModelDiagram> getEntityClass() {
		return VcMetaModelDiagram.class;
	}


	@Override
	public Class<CVcMetaModelDiagram> getConditionClass() {
		return CVcMetaModelDiagram.class;
	}


	@Override
	public String getTableName() {
		return "VC_META_MODEL_DIAGRAM";
	}


	@Override
	public boolean hasDataStatusField() {
		return true;
	}


	@Override
	public void setDataStatusValue(VcMetaModelDiagram record, int status) {
		record.setDataStatus(status);
	}


	@Override
	public void setDataStatusValue(CVcMetaModelDiagram cdt, int status) {
		cdt.setDataStatus(status);
	}


	@Override
	public void setCreatorValue(VcMetaModelDiagram record, String creator) {
		record.setCreator(creator);
	}


	@Override
	public void setModifierValue(VcMetaModelDiagram record, String modifier) {
		record.setModifier(modifier);
	}


}


