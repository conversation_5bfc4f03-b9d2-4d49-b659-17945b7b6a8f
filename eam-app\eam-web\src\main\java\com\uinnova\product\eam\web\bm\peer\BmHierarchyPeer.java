package com.uinnova.product.eam.web.bm.peer;

import com.binary.core.util.BinaryUtils;
import com.uinnova.product.eam.base.util.EamUtil;
import com.uinnova.product.eam.comm.model.es.EamCategory;
import com.uinnova.product.eam.comm.model.es.EamHierarchy;
import com.uinnova.product.eam.model.dto.EamHierarchyDto;
import com.uinnova.product.eam.model.dto.NavigationModelDto;
import com.uinnova.product.eam.service.EamCategorySvc;
import com.uinnova.product.eam.service.IBmHierarchySvc;
import com.uinnova.product.eam.service.IBmNavigationHierarchySvc;
import com.uino.bean.cmdb.base.LibType;
import com.uino.util.sys.SysUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 层级配置业务处理层
 * <AUTHOR>
 */
@Slf4j
@Service
public class BmHierarchyPeer {

    @Autowired
    private IBmHierarchySvc iBmHierarchySvc;
    @Resource
    private EamCategorySvc categorySvc;
    @Resource
    private IBmNavigationHierarchySvc navigationHierarchySvc;

    public Long saveOrUpdate(EamHierarchyDto dto) {
        return iBmHierarchySvc.saveOrUpdate(dto);
    }

    public List<EamHierarchyDto> queryHierarchyList(EamHierarchy cdt) {

        return iBmHierarchySvc.queryList(cdt);
    }

    public EamHierarchyDto queryByLvl(Integer level) {
        return iBmHierarchySvc.queryByLvlAndModelId(level, 0L, SysUtil.getCurrentUserInfo().getDomainId());
    }

    public EamHierarchyDto queryByDirId(Long dirId, String ownerCode) {
        EamCategory category = categorySvc.getById(dirId, LibType.PRIVATE);
        if (BinaryUtils.isEmpty(category)) {
            log.error("查询失败,查无当前视图目录,dirId={}", dirId);
            return null;
        }
        EamCategory modelRoot = categorySvc.getModelRoot(category.getModelId(), category.getOwnerCode(), LibType.PRIVATE);
        Integer dirLvl = category.getDirLvl()-modelRoot.getDirLvl()-1;
        //查询建模层级信息
        if (BinaryUtils.isEmpty(dirLvl)) {
            log.error("查询失败，当前视图目录层级信息为空,dirId={}", dirId);
            return null;
        }
        return iBmHierarchySvc.queryByLvlAndModelId(dirLvl, category.getModelId(), SysUtil.getCurrentUserInfo().getDomainId());
    }

    public Integer deleteById(Long id) {
        return iBmHierarchySvc.deleteById(id);
    }

    public Integer deleteNoVerifyById(Long id) {
        return iBmHierarchySvc.deleteNoVerifyById(id);
    }

    /**
     * 通过视图查询层级配置
     */
    public List<EamHierarchyDto> queryByDiagramId(String diagramId) {
        return iBmHierarchySvc.queryByDiagramId(diagramId);
    }

    /**
     * 给层级模型刷一一条数据：是否开启活动图拖拽开关，默认值false;
     */
    public Integer crushDataActiveFlag() {
        Long domainId = SysUtil.getCurrentUserInfo().getDomainId();
        EamHierarchy cdt = new EamHierarchy();
        cdt.setDomainId(domainId);
        List<EamHierarchyDto> hierarchyList = iBmHierarchySvc.queryList(cdt);
        List<EamHierarchy> saveParam = new ArrayList<>();
        if(!BinaryUtils.isEmpty(hierarchyList)){
            for (EamHierarchyDto hierarchyDto : hierarchyList) {
                EamHierarchy eamHierarchy = EamUtil.copy(hierarchyDto, EamHierarchy.class);
                eamHierarchy.setCheckActive(false);
                saveParam.add(eamHierarchy);
            }
        }
      return iBmHierarchySvc.saveOrUpdateBatch(saveParam);
    }

    public List<NavigationModelDto> getHierarchyNavigation(String state) {
        return iBmHierarchySvc.getHierarchyNavigation(state);
    }

    public NavigationModelDto getHierarchyNavigationByDiagramId(String diagramId) {
        return iBmHierarchySvc.getHierarchyNavigationByDiagramId(diagramId);
    }

    public Boolean changeHierarchyStatus(Long id, Long modelId, Integer state, String ownerCode, LibType libType) {
        return navigationHierarchySvc.changeHierarchyStatus(id, modelId, state, ownerCode, libType);
    }
}
