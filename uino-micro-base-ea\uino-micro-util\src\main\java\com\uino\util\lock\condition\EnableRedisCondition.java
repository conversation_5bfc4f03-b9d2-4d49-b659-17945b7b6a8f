package com.uino.util.lock.condition;

import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Condition;
import org.springframework.context.annotation.ConditionContext;
import org.springframework.core.type.AnnotatedTypeMetadata;

/**
 * redis condition
 */
public class EnableRedisCondition implements Condition {

    @Override
    public boolean matches(ConditionContext context, AnnotatedTypeMetadata metadata) {
        String singleModel = context.getEnvironment().getProperty("spring.data.redis.host");
        String clusterModel = context.getEnvironment().getProperty("spring.data.redis.cluster.nodes");
        String sentinelModel = context.getEnvironment().getProperty("spring.data.redis.sentinel.nodes");
        if(StringUtils.isNotBlank(singleModel) || StringUtils.isNotBlank(clusterModel)
                || StringUtils.isNotBlank(sentinelModel)){
            return true;
        }
        return false;
    }
}
