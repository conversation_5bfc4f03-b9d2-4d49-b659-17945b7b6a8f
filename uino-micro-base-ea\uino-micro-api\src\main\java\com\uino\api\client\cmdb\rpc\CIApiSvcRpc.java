package com.uino.api.client.cmdb.rpc;

import java.util.List;
import java.util.Map;

import com.uino.bean.cmdb.base.LibType;
import com.uino.bean.cmdb.base.SaveBatchCIContext;
import com.uino.bean.cmdb.base.SaveType;
import com.uino.bean.cmdb.business.*;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.search.sort.SortBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.comm.model.ci.CCcCi;
import com.uinnova.product.vmdb.comm.model.ci.CcCi;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CiClassSaveInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CiGroupPage;
import com.uinnova.product.vmdb.provider.ci.bean.CiQueryCdt;
import com.uinnova.product.vmdb.provider.search.bean.CcCiSearchPage;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.query.ESAttrAggBean;
import com.uino.bean.cmdb.query.ESCISearchBean;
import com.uino.provider.feign.cmdb.CIFeign;
import com.uino.api.client.cmdb.ICIApiSvc;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
public class CIApiSvcRpc implements ICIApiSvc {

    @Autowired
    private CIFeign ciFeign;

    @Override
    public CcCiInfo getCiInfoById(Long id) {
        return ciFeign.getCiInfoById(id);
    }

    @Override
    public CiGroupPage queryPageByIndex(Integer pageNum, Integer pageSize, CiQueryCdt cdt, Boolean hasClass) {
        if (cdt == null) {
            return ciFeign.queryPageByIndexWithoutBody(1L,pageNum, pageSize, hasClass);
        }
        return ciFeign.queryPageByIndex(1L, pageNum, pageSize, cdt, hasClass);
    }

    @Override
    public CiGroupPage queryPageByIndex(Long domainId, Integer pageNum, Integer pageSize, CiQueryCdt cdt, Boolean hasClass) {
        if (cdt == null) {
            return ciFeign.queryPageByIndexWithoutBody(domainId,pageNum, pageSize, hasClass);
        }
        return ciFeign.queryPageByIndex(domainId, pageNum, pageSize, cdt, hasClass);
    }

    @Override
    public CiGroupPage queryPageBySearchBean(ESCISearchBean bean, Boolean hasClass) {
        return ciFeign.queryPageBySearchBean(bean, hasClass);
    }

    @Override
    public List<CcCi> queryCiList(Long domainId, CCcCi cdt, String orders, Boolean isAsc) {
        if (cdt == null) {
            return ciFeign.queryCiListWithoutBody(domainId, orders, isAsc);
        }
        return ciFeign.queryCiList(domainId, cdt, orders, isAsc);
    }

    @Override
    public List<ESCIInfo> queryESCIInfoList(Long domainId, CCcCi cdt, String orders, Boolean isAsc) {
        if (cdt==null) {
            return ciFeign.queryESCIInfoListWithoutBody(domainId, orders, isAsc);
        }
        return ciFeign.queryESCIInfoList(domainId, cdt, orders, isAsc);
    }

    @Override
    public List<CcCiInfo> queryCiInfoList(Long domainId, CCcCi cdt, String orders, Boolean isAsc, Boolean hasClass) {
        if (cdt == null) {
            return ciFeign.queryCiInfoListWithoutBody(domainId, orders, isAsc, hasClass);
        }
        return ciFeign.queryCiInfoList(domainId, cdt, orders, isAsc, hasClass);
    }

    @Override
    public Page<CcCiInfo> queryCiInfoPage(Long domainId, Integer pageNum, Integer pageSize, CCcCi cdt, String orders,
            Boolean isAsc, Boolean hasClass) {
        if (cdt == null) {
            return ciFeign.queryCiInfoPageWithoutBody(domainId, pageNum, pageSize, orders, isAsc, hasClass);
        }
        return ciFeign.queryCiInfoPage(domainId, pageNum, pageSize, cdt, orders, isAsc, hasClass);
    }

    @Override
    public CcCiSearchPage searchCIByCdt(int pageNum, int pageSize, CCcCi bean) {
        if (bean == null) {
            return ciFeign.searchCIByCdtWithoutBody(pageNum, pageSize);
        }
        return ciFeign.searchCIByCdt(pageNum, pageSize, bean);
    }

    @Override
    public Page<ESCIInfo> searchESCIByBean(ESCISearchBean bean) {
        return ciFeign.searchESCIByBean(bean);
    }

    @Override
    public CcCiSearchPage searchCIByBean(ESCISearchBean bean) {
        return ciFeign.searchCIByBean(bean);
    }

    @Override
    public Long saveOrUpdateCI(CcCiInfo ciInfo) {
        return ciFeign.saveOrUpdateCI(ciInfo);
    }

    @Override
    public Long saveOrUpdateCI(CcCiInfo ciInfo, SaveType saveType) {
        return ciFeign.saveOrUpdateCI(ciInfo);
    }

    @Override
    public Map<String, ? extends SaveBatchCIContext> saveOrUpdateBatchCI(List<ESCIInfo> ciList, List<Long> classIds, String ownerCode, String loginCode) {
        return null;
    }

    @Override
    public Long saveOrUpdateCIExtra(CcCiInfo ciInfo) {
        return null;
    }

    @Override
    public ImportSheetMessage saveOrUpdateCiBatch(Long domainId, CiClassSaveInfo saveInfo) {
        return ciFeign.saveOrUpdateCiBatch(domainId, saveInfo);
    }

    @Override
    public Integer updateESCIInfoBatch(List<ESCIInfo> esCiInfoList) {
        return ciFeign.updateESCIInfoBatch(esCiInfoList);
    }

    @Override
    public Integer removeById(Long id, Long sourceId) {
        return ciFeign.removeById(id, sourceId);
    }



    @Override
    public Integer removeByPrimaryKeys(List<String> ciPrimaryKeys, Long sourceId) {
        return ciFeign.removeByPrimaryKeys(1L, ciPrimaryKeys, sourceId);
    }

    @Override
    public Integer removeByPrimaryKeys(Long domainId, List<String> ciPrimaryKeys, Long sourceId) {
        return ciFeign.removeByPrimaryKeys(domainId, ciPrimaryKeys, sourceId);
    }

    @Override
    public Integer removeByIds(List<Long> ciIds, Long sourceId) {
        return ciFeign.removeByIds(ciIds, sourceId);
    }

    @Override
    public Integer removeByClassId(Long classId, Long sourceId) {
        return ciFeign.removeByClassId(classId, sourceId);
    }

    @Override
    public Integer removeByOwnerCodeAndClassId(Long classId, String ownerCode) {
        return null;
    }

    @Override
    public ResponseEntity<byte[]> exportCiOrClass(ExportCiDto exportDto) {
        return ciFeign.exportCiOrClass(exportDto);
    }

    @Override
    public ImportResultMessage importCiByCiClsIds(MultipartFile file, Long classId) {
        return ciFeign.importCiByCiClsIds(file, classId);
    }

    @Override
    public ImportExcelMessage importCiExcel(MultipartFile file) {
        return ciFeign.importCiExcel(file);
    }

    @Override
    public ImportResultMessage importCiByClassBatch(CiExcelInfoDto excelInfoDto) {
        return ciFeign.importCiByClassBatch(1L, excelInfoDto);
    }

    @Override
    public ImportResultMessage importCiByClassBatch(Long domainId, CiExcelInfoDto excelInfoDto) {
        return ciFeign.importCiByClassBatch(domainId, excelInfoDto);
    }

    @Override
    public ImportResultMessage importCiByClassBatchForAddAttr(CiExcelInfoDto excelInfoDto, boolean addAttr) {
        return ciFeign.importCiByClassBatchForAddAttr(1L, excelInfoDto, addAttr);
    }

    @Override
    public ImportResultMessage importCiByClassBatchForAddAttr(Long domainId, CiExcelInfoDto excelInfoDto, boolean addAttr) {
        return ciFeign.importCiByClassBatchForAddAttr(domainId, excelInfoDto, addAttr);
    }

    @Override
    public Map<Long, Long> countCiNumGroupClsByQuery(ESCISearchBean bean) {
        return ciFeign.countCiNumGroupClsByQuery(bean);
    }

    @Override
	public Long countByQuery(ESCISearchBean bean) {
		return ciFeign.countByQuery(bean);
	}

	@Override
    public Page<String> getAttrValuesBySearchBean(ESAttrAggBean searchBean) {
        return ciFeign.getAttrValuesBySearchBean(searchBean);
    }

    @Override
    public List<CcCiInfo> getCIInfoListByCIPrimaryKeys(List<List<String>> ciPrimaryKeys) {
        return ciFeign.getCIInfoListByCIPrimaryKeys(1L, ciPrimaryKeys);
    }

    @Override
    public List<CcCiInfo> getCIInfoListByCIPrimaryKeys(Long domainId, List<List<String>> ciPrimaryKeys) {
        return ciFeign.getCIInfoListByCIPrimaryKeys(domainId, ciPrimaryKeys);
    }

    @Override
    public List<ESCIInfo> getESCIInfoListByCIPrimaryKeys(List<List<String>> ciPrimaryKeys) {
        return ciFeign.getESCIInfoListByCIPrimaryKeys(1L, ciPrimaryKeys);
    }

    @Override
    public List<ESCIInfo> getESCIInfoListByCIPrimaryKeys(Long domainId, List<List<String>> ciPrimaryKeys) {
        return ciFeign.getESCIInfoListByCIPrimaryKeys(domainId, ciPrimaryKeys);
    }

    @Override
    public Page<ESCIInfo> getESCIInfoPageByQuery(int pageNum, int pageSize, QueryBuilder query, List<SortBuilder<?>> sorts, Boolean isHighLight) {
        return ciFeign.getESCIInfoPageByQuery(1L, pageNum, pageSize, query, sorts, isHighLight);
    }

    @Override
    public Page<ESCIInfo> getESCIInfoPageByQuery(Long domainId, int pageNum, int pageSize, QueryBuilder query, List<SortBuilder<?>> sorts, Boolean isHighLight) {
        return ciFeign.getESCIInfoPageByQuery(domainId, pageNum, pageSize, query, sorts, isHighLight);
    }


    @Override
    public Integer removeAllCI(QueryBuilder query) {
        return ciFeign.removeAllCI(1L, query);
    }

    @Override
    public Integer removeAllCI(Long domainId, QueryBuilder query) {
        return ciFeign.removeAllCI(domainId, query);
    }


	@Override
	public boolean updateAttrValueBatch(CIAttrValueUpdateDto dto) {
		return ciFeign.updateAttrValueBatch(dto);
	}

    @Override
    public Map<String, Long> queryCiCountByClassId() {
        return ciFeign.queryCiCountByClassId();
    }

    @Override
    public boolean modifyAttrValueBatch(CIAttrValueUpdateDto dto){
        return ciFeign.modifyAttrValueBatch(dto);
    }

    @Override
    public Integer removeCiBatch(CIRemoveBatchDto dto){
        return ciFeign.removeCiBatch(dto);
    }

    @Override
    public Map<String, ? extends SaveBatchCIContext> copyCiListByIds(List<ESCIInfo> ciList, String ownerCode) {
        return ciFeign.copyCiListByIds(ciList, ownerCode);
    }
}
