package com.binary.framework.critical.support;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import com.binary.framework.bean.User;
import com.binary.framework.exception.CriticalException;
import com.binary.framework.web.SessionKey;

public class HttpOauthCriticalObject extends AbstractCriticalObject {


	private HttpServletRequest request;
	private HttpServletResponse response;


	public HttpOauthCriticalObject(HttpServletRequest request, HttpServletResponse response) {
		this.request = request;
		this.response = response;
	}





	@Override
	public String getContextPath() {
		return this.request.getContextPath();
	}





	public HttpServletRequest getRequest() {
		return this.request;
	}


	public HttpServletResponse getResponse() {
		return this.response;
	}





	@Override
	public User getUser() {
		Object obj = request.getAttribute(SessionKey.SYSTEM_USER);
		if(obj!=null && !(obj instanceof User)) throw new CriticalException(" the oauth-user-value '"+obj.getClass().getName()+"' is not typeof com.binary.framework.auth.User! ");
		return (User)obj;
	}





	@Override
	public void setUser(User user) {
		request.setAttribute(SessionKey.SYSTEM_USER, user);
	}



}
