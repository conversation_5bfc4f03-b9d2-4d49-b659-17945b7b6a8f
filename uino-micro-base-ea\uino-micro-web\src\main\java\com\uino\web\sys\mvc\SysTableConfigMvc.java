package com.uino.web.sys.mvc;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import com.uino.bean.permission.base.SysUser;
import com.uino.init.api.ApiResult;
import com.uino.init.api.ApiVersion;
import com.uino.util.sys.SysUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uinnova.product.vmdb.comm.doc.annotation.MvcDesc;
import com.uino.bean.cmdb.base.ESTableDataConfigInfo;
import com.uino.api.client.sys.ITableDataConfigApiSvc;
import org.springframework.web.bind.annotation.RestController;

@ApiVersion(1)
@Api(value = "Table配置管理", tags = {"Table配置管理"})
@RestController
@RequestMapping("/sys/table")
@MvcDesc(author = "zmj", desc = "Table配置管理")
public class SysTableConfigMvc {

    @Autowired
    ITableDataConfigApiSvc configSvc;

    /**
     * 获取CI配置
     * 
     * @param configInfo
     * @param request
     * @param response
     */
    @ApiOperation( "获取对象管理用户自定义表格配置")
    @PostMapping("getTableConfigInfo")
    @ModDesc(desc = "获取对象管理用户自定义表格配置", pDesc = "主键", pType = String.class, rDesc = "配置详情", rType = ESTableDataConfigInfo.class)
    public ApiResult<ESTableDataConfigInfo> getCIDataConfigInfo(@RequestBody String uid, HttpServletRequest request, HttpServletResponse response) {
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        ESTableDataConfigInfo config = configSvc.getCIDataConfigInfo(currentUserInfo.getDomainId(), uid);
        return ApiResult.ok(this).data(config);
    }

    /**
     * 保存CI配置
     * 
     * @param configInfo
     * @param request
     * @param response
     */
    @ApiOperation("保存对象管理用户自定义表格配置")
    @PostMapping("saveTableConfigInfo")
    @ModDesc(desc = "保存对象管理用户自定义表格配置", pDesc = "配置对象", pType = ESTableDataConfigInfo.class, rDesc = "配置id", rType = Long.class)
    public ApiResult<Long> saveCIDataConfigInfo(@RequestBody ESTableDataConfigInfo configInfo, HttpServletRequest request, HttpServletResponse response) {
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        configInfo.setDomainId(currentUserInfo.getDomainId());
        Long id = configSvc.saveCIDataConfigInfo(configInfo);
        return ApiResult.ok(this).data(id);
    }
}
