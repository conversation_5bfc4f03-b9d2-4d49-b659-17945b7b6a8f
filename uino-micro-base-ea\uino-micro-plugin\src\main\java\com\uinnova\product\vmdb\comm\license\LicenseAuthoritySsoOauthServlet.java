package com.uinnova.product.vmdb.comm.license;

import java.util.Calendar;

import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import com.binary.core.util.BinaryUtils;
import com.binary.framework.exception.ServiceException;
import com.uinnova.product.vmdb.comm.integration.SpringContextAware;
import com.uinnova.product.vmdb.comm.log.SysOperateLoggerServlet;
import com.uinnova.product.vmdb.comm.util.SystemUtil;

/**
 * license即将到期提醒状态码为601-699, 后两位为即将到期天数,600为未授权或授权已过期
 *
 * <AUTHOR>
 */
public class LicenseAuthoritySsoOauthServlet extends SysOperateLoggerServlet {
    private static final long serialVersionUID = 1L;

    private static final long DAY_TIME = 24 * 60 * 60 * 1000;
    private final Object syncobj = new Object();
    private final Calendar calender = Calendar.getInstance();

    /** license即将到期提醒时查, 单位:天 **/
    private int licenseResurgeTimeDeff = 30;

    private LicenseProxy licenseProxy;

    @Override
    protected void initFrameworkServlet() throws ServletException {
        String deff = getInitParameter("licenseResurgeTimeDeff");
        if (!BinaryUtils.isEmpty(deff)) {
            int d = Integer.parseInt(deff);
            int cd = 100;
            if (d >= cd) {
                throw new ServiceException("Parameters of 'licenseResurgeTimeDeff' should not exceed 99 days");
            }
            if (d > 0) {
                this.licenseResurgeTimeDeff = d;
            }
        }
        super.initFrameworkServlet();
    }

    private int getCurrentDate() {
        synchronized (syncobj) {
            calender.setTimeInMillis(System.currentTimeMillis());
            int year = calender.get(Calendar.YEAR);
            int month = calender.get(Calendar.MONTH) + 1;
            int date = calender.get(Calendar.DATE);
            return year * 10000 + month * 100 + date;
        }
    }

    private int getDifferDays(int start, int end) {
        if (end <= start) {
            return 0;
        }

        synchronized (syncobj) {
            calender.set(Calendar.HOUR_OF_DAY, 0);
            calender.set(Calendar.MINUTE, 0);
            calender.set(Calendar.SECOND, 0);

            int sy = start / 10000;
            int sm = start % 10000 / 100;
            int sd = start % 100;

            calender.set(Calendar.YEAR, sy);
            calender.set(Calendar.MONTH, sm - 1);
            calender.set(Calendar.DATE, sd);

            long startTime = calender.getTimeInMillis();

            int ey = end / 10000;
            int em = end % 10000 / 100;
            int ed = end % 100;

            calender.set(Calendar.YEAR, ey);
            calender.set(Calendar.MONTH, em - 1);
            calender.set(Calendar.DATE, ed);

            long endTime = calender.getTimeInMillis();

            int deff = (int) ((endTime - startTime) / DAY_TIME);
            return deff;
        }
    }

    protected LicenseProxy getLicenseProxy() {
        if (this.licenseProxy == null) {
            this.licenseProxy = SpringContextAware.getSpringContext().getBean(LicenseProxy.class);
        }
        return this.licenseProxy;
    }

    protected License getLicense() {
        return getLicenseProxy().getLicense();
    }

    @Override
    public boolean beforeWrite(HttpServletRequest request, HttpServletResponse response, Object result) {
        LicenseAuthorityFilter filter = (LicenseAuthorityFilter) request.getAttribute(LicenseAuthorityFilter.class.getName());
        if (filter != null && filter.isIgnoreRequest(request)) {
            return true;
        }

        License license = getLicense();
        if (!SystemUtil.isLicenseValid(license)) {
        	response.setHeader(LicenseAuthoritySsoOauthServlet.SERVER_STATUS,
        			String.valueOf(LicenseAuthorityFilter.licenseInvalidCode));
            //response.setStatus(LicenseAuthorityFilter.licenseInvalidCode);
            return false;
        }

        int curr = getCurrentDate();
        int last = license.getAuthEndDate();
        int deff = getDifferDays(curr, last + 1);
        if (deff == 0) {
        	response.setHeader(LicenseAuthoritySsoOauthServlet.SERVER_STATUS,
        			String.valueOf(LicenseAuthorityFilter.licenseInvalidCode));
            //response.setStatus(LicenseAuthorityFilter.licenseInvalidCode);
            return false;
        }

        if (deff < this.licenseResurgeTimeDeff) {
        	response.setHeader(LicenseAuthoritySsoOauthServlet.SERVER_STATUS,
        			String.valueOf(210 + deff));
            //response.setStatus(210 + deff);
        }

        return super.beforeWrite(request, response, result);
    }

}
