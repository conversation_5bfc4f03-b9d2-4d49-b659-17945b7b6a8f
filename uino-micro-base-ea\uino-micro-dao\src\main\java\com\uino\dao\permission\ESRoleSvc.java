package com.uino.dao.permission;

import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;


import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.uino.dao.AbstractESBaseDao;
import com.uino.dao.ESConst;
import com.uino.dao.permission.rlt.ESPerssionCommSvc;
import com.uino.dao.permission.rlt.ESRoleDataModuleRltSvc;
import com.uino.dao.permission.rlt.ESRoleModuleRltSvc;
import com.uino.util.sys.CommonFileUtil;

import jakarta.annotation.PostConstruct;

import com.uino.bean.permission.base.SysRole;
import com.uino.bean.permission.base.SysUserRoleRlt;
import com.uino.bean.permission.query.CSysRole;

/**
 * <b>角色
 * 
 * <AUTHOR>
 */
@Service
public class ESRoleSvc extends AbstractESBaseDao<SysRole, CSysRole> {

	@Override
	public String getIndex() {
		return ESConst.INDEX_SYS_ROLE;
	}

	@Override
	public String getType() {
		return ESConst.INDEX_SYS_ROLE;
	}

	@PostConstruct
	public void init() {
		List<SysRole> data = CommonFileUtil.getData("/initdata/uino_sys_role.json", SysRole.class);
		super.initIndex(data);
	}

	@Autowired
    ESPerssionCommSvc commSvc;

	@Autowired
	ESRoleModuleRltSvc roleModuleRltSvc;

	@Autowired
	ESRoleDataModuleRltSvc roleDataModuleRltSvc;

	/**
	 * 根据用户ID获取所有角色
	 * 
	 * @param userId
	 *            用户ID
	 * @return
	 */
	public List<SysRole> getListByUserId(Long userId) {
        List<SysUserRoleRlt> rlts = commSvc.getUserRoleRltByUserIds(Collections.singleton(userId));
		Set<Long> roleIds = new HashSet<Long>();
		for (SysUserRoleRlt rlt : rlts) {
			roleIds.add(rlt.getRoleId());
		}
		return super.getListByQuery(1, 500, QueryBuilders.termsQuery("id", roleIds)).getData();
	}

	/**
	 * 获取用户的所有角色ID
	 * 
	 * @param userId
	 * @return
	 */
	public Set<Long> getRoleIdsByUserId(Long userId) {
        List<SysUserRoleRlt> rlts = commSvc.getUserRoleRltByUserIds(Collections.singleton(userId));
		Set<Long> roleIds = new HashSet<Long>();
		for (SysUserRoleRlt rlt : rlts) {
			roleIds.add(rlt.getRoleId());
		}
		return roleIds;
	}

	/**
	 * 根据名字获取
	 * 
	 * @param code
	 * @return
	 */
	public SysRole getByName(Long domainId, String name) {
		BoolQueryBuilder query = QueryBuilders.boolQuery();
		query.must(QueryBuilders.termQuery("domainId", domainId));
		query.must(QueryBuilders.termQuery("roleName.keyword", name));
		List<SysRole> list = super.getListByQuery(1, 1, query).getData();
		if (list.size() > 0) {
			return list.get(0);
		}
		return null;
	}

	/**
	 * 级联删除角色相关的内容
	 * 
	 * @param roleId
	 * @return
	 */
	public Integer deleteRoleById(Long roleId) {
		QueryBuilder query = QueryBuilders.termQuery("roleId", roleId);
        commSvc.deleteUserRoleRltByQuery(query, true);
        commSvc.deleteOrgRoleRltByQuery(query, true);
		roleModuleRltSvc.deleteByQuery(query, true);
		roleDataModuleRltSvc.deleteByQuery(query, true);
		return super.deleteById(roleId);
	}

}
