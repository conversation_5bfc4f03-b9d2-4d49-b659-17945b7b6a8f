package com.uinnova.product.eam.base.util;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;

/**
 * Bean对象转换成Map工具类
 *
 * <AUTHOR>
 */
public class BeanToMapUtils<T> {
    /**
     * 实体类转Map
     * @param object
     * @return
     */
    public static Map<String, Object> entityToMap(Object object) {
        Map<String, Object> map = new HashMap();
        for (Field field : object.getClass().getDeclaredFields()){
            try {
                boolean flag = field.isAccessible();
                field.setAccessible(true);
                Object o = field.get(object);
                map.put(field.getName(), o);
                field.setAccessible(flag);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return map;
    }
}
