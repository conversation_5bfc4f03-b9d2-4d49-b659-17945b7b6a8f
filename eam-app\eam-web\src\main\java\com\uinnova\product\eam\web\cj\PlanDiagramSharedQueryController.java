package com.uinnova.product.eam.web.cj;

import com.uinnova.product.eam.base.cj.ResultMsg;
import com.uinnova.product.eam.model.cj.request.ShareRecordRequest;
import com.uinnova.product.eam.model.cj.vo.PlanShareDetailVO;
import com.uinnova.product.eam.model.cj.vo.ShareVO;
import com.uinnova.product.eam.service.cj.service.PlanDiagramSharedQueryService;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 方案分享查询controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/planDiagramShare")
public class PlanDiagramSharedQueryController {

    @Resource
    private PlanDiagramSharedQueryService planDiagramSharedQueryService;

    /**
     * 查询分享列表
     *
     * @param request {@link ShareRecordRequest}
     * @return {@link ShareVO}
     */
    @PostMapping("/query/list")
    public ResultMsg queryList(@RequestBody ShareRecordRequest request) {
        List<ShareVO> list = planDiagramSharedQueryService.queryShare(request);
        return new ResultMsg(list);
    }

    /**
     * 查询分享的方案详情
     *
     * @param planId 方案Id
     * @return {@link PlanShareDetailVO}
     */
    @GetMapping("/queryPlanShareDetail")
    public ResultMsg queryPlanShareDetail(Long planId) {
        PlanShareDetailVO vo = planDiagramSharedQueryService.queryPlanShareDetail(planId);
        return new ResultMsg(vo);
    }
}
