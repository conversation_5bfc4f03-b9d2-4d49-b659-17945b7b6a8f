<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:flowable="http://flowable.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://www.flowable.org/processdef">
  <process id="system_approve" name="system_approve" isExecutable="true">
    <documentation>华泰应用系统变更审批</documentation>
    <startEvent id="startEvent1"></startEvent>
    <userTask id="sid-1094B4A8-150E-4214-9E6D-8862150E79B8" name="用户发起变更审批"></userTask>
    <sequenceFlow id="sid-5D1C695B-014B-41F1-8AA6-8D14E866CA1D" sourceRef="startEvent1" targetRef="sid-1094B4A8-150E-4214-9E6D-8862150E79B8"></sequenceFlow>
    <exclusiveGateway id="sid-3EE6829D-4C43-405C-86B5-B57BAEB36931"></exclusiveGateway>
    <sequenceFlow id="sid-1223E322-776B-43EC-B71C-6A7569EBD268" sourceRef="sid-1094B4A8-150E-4214-9E6D-8862150E79B8" targetRef="sid-3EE6829D-4C43-405C-86B5-B57BAEB36931"></sequenceFlow>
    <userTask id="sid-28D27278-32FC-48B5-A409-3D6E429FC75C" name="团队及中心架构师" flowable:assignee="${assignee}">
      <extensionElements>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
      <multiInstanceLoopCharacteristics isSequential="false" flowable:collection="assigneeList" flowable:elementVariable="assignee">
        <completionCondition>${multiInstanceCompleteExecution.executeByOneUserConditionImmediately(execution)}</completionCondition>
      </multiInstanceLoopCharacteristics>
    </userTask>
    <exclusiveGateway id="sid-1FA999FF-7725-474A-B642-C74C5C3B3281"></exclusiveGateway>
    <endEvent id="sid-2A633145-7F76-48E9-B683-BC26B849E98F"></endEvent>
    <userTask id="user_edit_node" name="用户修改变更" flowable:assignee="$INITIATOR">
      <extensionElements>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[true]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <sequenceFlow id="sid-650E0C56-B287-4E51-B74E-0089C7B68D51" name="重新提交" sourceRef="user_edit_node" targetRef="sid-3EE6829D-4C43-405C-86B5-B57BAEB36931"></sequenceFlow>
    <sequenceFlow id="sid-1232F1CE-AF1F-4A01-A91F-89C0E08E04D8" name="驳回" sourceRef="sid-1FA999FF-7725-474A-B642-C74C5C3B3281" targetRef="user_edit_node">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${goOut !='pass'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-52829AD2-8776-438A-8CD3-51FD722C59CE" name="通过" sourceRef="sid-1FA999FF-7725-474A-B642-C74C5C3B3281" targetRef="sid-2A633145-7F76-48E9-B683-BC26B849E98F">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${goOut=='pass'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-50237475-2B24-4D2A-93AD-46F9B51C598E" sourceRef="sid-28D27278-32FC-48B5-A409-3D6E429FC75C" targetRef="sid-1FA999FF-7725-474A-B642-C74C5C3B3281"></sequenceFlow>
    <sequenceFlow id="sid-41CF4B5A-2C64-4A41-B63E-E78DCAEFBDEC" name="提交审批" sourceRef="sid-3EE6829D-4C43-405C-86B5-B57BAEB36931" targetRef="sid-28D27278-32FC-48B5-A409-3D6E429FC75C">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${pass=='pass'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-0B4AE73A-14AD-4E35-BCCF-74EB2547724F" name="取消审批" sourceRef="sid-3EE6829D-4C43-405C-86B5-B57BAEB36931" targetRef="sid-2A633145-7F76-48E9-B683-BC26B849E98F">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${pass=='noPass'}]]></conditionExpression>
    </sequenceFlow>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_system_approve">
    <bpmndi:BPMNPlane bpmnElement="system_approve" id="BPMNPlane_system_approve">
      <bpmndi:BPMNShape bpmnElement="startEvent1" id="BPMNShape_startEvent1">
        <omgdc:Bounds height="30.0" width="30.0" x="0.0" y="385.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-1094B4A8-150E-4214-9E6D-8862150E79B8" id="BPMNShape_sid-1094B4A8-150E-4214-9E6D-8862150E79B8">
        <omgdc:Bounds height="80.0" width="100.0" x="135.0" y="360.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-3EE6829D-4C43-405C-86B5-B57BAEB36931" id="BPMNShape_sid-3EE6829D-4C43-405C-86B5-B57BAEB36931">
        <omgdc:Bounds height="40.0" width="40.0" x="285.0" y="380.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-28D27278-32FC-48B5-A409-3D6E429FC75C" id="BPMNShape_sid-28D27278-32FC-48B5-A409-3D6E429FC75C">
        <omgdc:Bounds height="80.0" width="100.0" x="405.0" y="360.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-1FA999FF-7725-474A-B642-C74C5C3B3281" id="BPMNShape_sid-1FA999FF-7725-474A-B642-C74C5C3B3281">
        <omgdc:Bounds height="40.0" width="40.0" x="570.0000000000001" y="380.00000000000006"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-2A633145-7F76-48E9-B683-BC26B849E98F" id="BPMNShape_sid-2A633145-7F76-48E9-B683-BC26B849E98F">
        <omgdc:Bounds height="28.0" width="28.0" x="291.00000000000006" y="555.0000000000003"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="user_edit_node" id="BPMNShape_user_edit_node">
        <omgdc:Bounds height="80.0" width="100.0" x="255.0" y="180.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="sid-5D1C695B-014B-41F1-8AA6-8D14E866CA1D" id="BPMNEdge_sid-5D1C695B-014B-41F1-8AA6-8D14E866CA1D">
        <omgdi:waypoint x="29.949999367560775" y="400.0"></omgdi:waypoint>
        <omgdi:waypoint x="135.0" y="400.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-41CF4B5A-2C64-4A41-B63E-E78DCAEFBDEC" id="BPMNEdge_sid-41CF4B5A-2C64-4A41-B63E-E78DCAEFBDEC">
        <omgdi:waypoint x="324.9433544303797" y="400.0"></omgdi:waypoint>
        <omgdi:waypoint x="405.0" y="400.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-0B4AE73A-14AD-4E35-BCCF-74EB2547724F" id="BPMNEdge_sid-0B4AE73A-14AD-4E35-BCCF-74EB2547724F">
        <omgdi:waypoint x="305.0" y="419.9441011235955"></omgdi:waypoint>
        <omgdi:waypoint x="305.00000000000006" y="555.0000000000003"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-650E0C56-B287-4E51-B74E-0089C7B68D51" id="BPMNEdge_sid-650E0C56-B287-4E51-B74E-0089C7B68D51">
        <omgdi:waypoint x="305.0" y="259.95000000000005"></omgdi:waypoint>
        <omgdi:waypoint x="305.0" y="380.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-1232F1CE-AF1F-4A01-A91F-89C0E08E04D8" id="BPMNEdge_sid-1232F1CE-AF1F-4A01-A91F-89C0E08E04D8">
        <omgdi:waypoint x="590.0000000000001" y="380.00000000000006"></omgdi:waypoint>
        <omgdi:waypoint x="590.0" y="220.0"></omgdi:waypoint>
        <omgdi:waypoint x="354.94999999990716" y="220.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-52829AD2-8776-438A-8CD3-51FD722C59CE" id="BPMNEdge_sid-52829AD2-8776-438A-8CD3-51FD722C59CE">
        <omgdi:waypoint x="590.0000000000001" y="419.9441011235956"></omgdi:waypoint>
        <omgdi:waypoint x="590.0" y="569.0"></omgdi:waypoint>
        <omgdi:waypoint x="318.94992064158265" y="569.0000000000003"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-1223E322-776B-43EC-B71C-6A7569EBD268" id="BPMNEdge_sid-1223E322-776B-43EC-B71C-6A7569EBD268">
        <omgdi:waypoint x="234.95000000000002" y="400.0"></omgdi:waypoint>
        <omgdi:waypoint x="285.0" y="400.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-50237475-2B24-4D2A-93AD-46F9B51C598E" id="BPMNEdge_sid-50237475-2B24-4D2A-93AD-46F9B51C598E">
        <omgdi:waypoint x="504.9499999999727" y="400.0"></omgdi:waypoint>
        <omgdi:waypoint x="570.0000000000001" y="400.00000000000006"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>