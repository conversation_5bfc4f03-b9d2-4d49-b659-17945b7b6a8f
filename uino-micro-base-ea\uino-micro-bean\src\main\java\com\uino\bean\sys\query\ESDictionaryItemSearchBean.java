package com.uino.bean.sys.query;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

import com.uino.bean.cmdb.query.ESAttrBean;
import com.uino.bean.cmdb.query.ESSearchBase;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Builder.Default;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 
 * <AUTHOR>
 *
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "字典表查询DTO", description = "字典表查询DTO")
public class ESDictionaryItemSearchBean extends ESSearchBase implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "字典类ID", example = "3",  required = true)
    private Long dictClassId;

    @ApiModelProperty(value = "字典类IDs", example = "3,4,5",  required = true)
    private Long[] dictClassIds;

	@ApiModelProperty(value = "字典code")
    private String dictCode;

	@ApiModelProperty(value = "字典显示名称")
    private String dictName;

    @ApiModelProperty(value = "字典项定义ID")
    private Long dictDefId;

    @ApiModelProperty(value = "字典项定义IDS")
    private Long[] dictDefIds;

    @ApiModelProperty(value = "字典项属性名称")
    private String dictProName;

    @ApiModelProperty(value = "搜索关键字")
    private String keyword;

    @ApiModelProperty(value = "排序字段")
    private String sortField;

    @ApiModelProperty(value = "AND属性合集")
    private List<ESAttrBean> andAttrs;

    @ApiModelProperty(value = "OR属性合集")
    private List<ESAttrBean> orAttrs;

    @Default
    @ApiModelProperty(value = "是否升序, 默认true")
    private Boolean isAsc = true;
    
    @ApiModelProperty(value = "唯一属性")
    private Collection<String> keyCodes;

    @ApiModelProperty(value = "所属域")
    private Long domainId ;
}
