package com.uinnova.product.vmdb.provider.quality.bean;

import java.util.List;

public class QualityDataTitle {

    private static final long serialVersionUID = 1L;

    public QualityDataTitle(Long id, String key, String title) {
        this.id = id;
        this.key = key;
        this.title = title;
    }

    private Long id;

    private String title;

    private String key;

    private List<String> filters;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }


    public List<String> getFilters() {
        return filters;
    }

    public void setFilters(List<String> filters) {
        this.filters = filters;
    }

    public String getKey() {
        return this.key;
    }

    public void setKey(String key) {
        this.key = key;
    }
}
