package com.uinnova.product.eam.model.dm.bean;

import com.binary.core.util.BinaryUtils;
import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.base.exception.ServerException;
import com.uinnova.product.eam.model.dm.DataModelEntity;
import com.uino.bean.cmdb.base.ESCIInfo;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 数据建模生成DDL实体
 * <AUTHOR>
 */
@Data
public class DataModelEntityVo {
    @Comment("表名")
    private String key;
    @Comment("中文名称")
    private String name;
    @Comment("表字段集合")
    private List<DataModelAttributeVo> attrList = new ArrayList<>();
    @Comment("主键集合")
    private List<String> pkList = new ArrayList<>();

    public DataModelEntityVo(ESCIInfo ci){
        if(!BinaryUtils.isEmpty(ci.getAttrs().get(DataModelEntity.NAME_EN))){
            this.key = ci.getAttrs().get(DataModelEntity.NAME_EN).toString().toUpperCase();
        }else{
            throw new ServerException(DataModelEntity.NAME_EN+"未填写完整!");
        }
        if(!BinaryUtils.isEmpty(ci.getAttrs().get(DataModelEntity.NAME_CN))){
            this.name = ci.getAttrs().get(DataModelEntity.NAME_CN).toString();
        }else{
            throw new ServerException(DataModelEntity.NAME_CN+"未填写完整!");
        }
    }
}
