package com.uino.api.client.permission.local;

import java.util.List;
import java.util.Map;
import java.util.Set;

import com.uino.dao.BaseConst;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import com.uino.service.permission.microservice.IModuleSvc;
import com.uino.bean.permission.base.SysModule;
import com.uino.bean.permission.business.ModuleNodeInfo;
import com.uino.bean.permission.query.CAuthModuleBean;
import com.uino.bean.permission.query.CSysModule;
import com.uino.api.client.permission.IModuleApiSvc;
import org.springframework.web.multipart.MultipartFile;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
public class ModuleApiSvcLocal implements IModuleApiSvc {
	@Autowired
	private IModuleSvc svc;

	@Override
	public ModuleNodeInfo getModuleTree(Long userId) {
		// TODO Auto-generated method stub
		return svc.getModuleTree(BaseConst.DEFAULT_DOMAIN_ID,userId);
	}

	@Override
	public ModuleNodeInfo getModuleTree(Long domainId, Long userId) {
		return svc.getModuleTree(domainId,userId);
	}

	@Override
	public SysModule saveModule(SysModule saveDto) {
		// TODO Auto-generated method stub
		return svc.saveModule(saveDto);
	}

	@Override
	public void delModule(Long id) {
		// TODO Auto-generated method stub
		svc.delModule(id);
	}

	@Override
	public void saveOrder(Map<Long, Integer> orderDict) {
		// TODO Auto-generated method stub
		svc.saveOrder(orderDict);
	}

	@Override
	public Map<Long, SysModule> recoverModules(Set<Long> moduleIds) {
		// TODO Auto-generated method stub
		return svc.recoverModules(moduleIds);
	}


	@Override
    public List<SysModule> getModulesByCdt(CSysModule cdt) {
        // TODO Auto-generated method stub
        return svc.getModulesByCdt(cdt);
    }

    @Override
    public List<SysModule> getAuthModulesBySearchBean(CAuthModuleBean bean) {
        return svc.getAuthModulesBySearchBean(bean);
    }

	@Override
	public ResponseEntity<byte[]> exportModules() {
		return svc.exportModules();
	}

	@Override
	public void importModules(MultipartFile file) {
		svc.importModules(file);
	}

	@Override
	public ModuleNodeInfo getModuleTreeBySign(Long domainId, Long userId, String moduleSign) {
		return svc.getModuleTreeBySign(domainId,userId,moduleSign);
	}
}
