package com.uino.role;

import static org.junit.Assert.assertTrue;

import java.util.ArrayList;
import java.util.List;

import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.uino.StartBaseWebAppliaction;
import com.uino.bean.permission.base.SysRole;
import com.uino.bean.permission.base.SysRoleDataModuleRlt;
import com.uino.bean.permission.base.SysRoleModuleRlt;
import com.uino.bean.permission.base.SysUserDataModuleRlt;
import com.uino.bean.permission.base.SysUserModuleRlt;
import com.uino.bean.permission.query.CAuthBean;
import com.uino.bean.permission.query.SearchKeywordBean;

import lombok.extern.slf4j.Slf4j;

@RunWith(SpringRunner.class)
@WebAppConfiguration
@SpringBootTest(classes = { StartBaseWebAppliaction.class })
@ActiveProfiles("provider-local")
@AutoConfigureMockMvc(addFilters = false)
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
@Slf4j
public class SysRoleMvcTest {

    @Autowired
    private MockMvc mockMvc;

    private String token = "ca14ed042fe7912426b484f6ea5c754b4fb51ae4a72037e9127da51743e0d1f9650e6e78ed4466ac970acb6a0b2f46fd26958c4ab2d115cc71b34ca8a02db24c";

    @Test
    public void aSaveOrUpdate() throws Exception {
        String uri = "/permission/role/saveOrUpdate";
        SysRole role = new SysRole();
        role.setId(1000l);
        role.setRoleName("测试角色1");
        role.setRoleDesc("This is junit test.");
        String body = JSON.toJSONString(role);
        ResultActions reaction = this.mockMvc.perform(MockMvcRequestBuilders.post(uri)
                .contentType(MediaType.APPLICATION_JSON).content(body).accept(MediaType.APPLICATION_JSON)
                .header("Content-Type", "application/json").header("token", token));
        MvcResult mvcResult = reaction.andReturn();
        String rs = mvcResult.getResponse().getContentAsString();
        log.info(rs);
        JSONObject json = JSON.parseObject(rs);
        assertTrue(json.getBooleanValue("success") == true);
    }

    @Test
    public void bTestOnly() throws Exception {
        String uri = "/permission/role/saveOrUpdate";
        SysRole role = new SysRole();
        role.setRoleName("测试角色");
        role.setRoleDesc("This is junit test.");
        String body = JSON.toJSONString(role);
        ResultActions reaction = this.mockMvc.perform(MockMvcRequestBuilders.post(uri)
                .contentType(MediaType.APPLICATION_JSON).content(body).accept(MediaType.APPLICATION_JSON)
                .header("Content-Type", "application/json").header("token", token));
        MvcResult mvcResult = reaction.andReturn();
        String rs = mvcResult.getResponse().getContentAsString();
        log.info(rs);
        JSONObject json = JSON.parseObject(rs);
        assertTrue(json.getBooleanValue("success") == false);
    }

    @Test
    public void cDelete() throws Exception {
        String uri = "/permission/role/deleteById";

        ResultActions reaction = this.mockMvc.perform(MockMvcRequestBuilders.post(uri)
                .contentType(MediaType.TEXT_PLAIN_VALUE).content("1000").accept(MediaType.TEXT_PLAIN_VALUE)
                .header("Content-Type", "application/json").header("token", token));
        MvcResult mvcResult = reaction.andReturn();
        String rs = mvcResult.getResponse().getContentAsString();
        JSONObject json = JSON.parseObject(rs);
        assertTrue(json.getBooleanValue("success") == true);
    }

    @Test
    public void getRolePageByQuery() throws Exception {
        String uri = "/permission/role/getRolePageByQuery";
        SearchKeywordBean query = new SearchKeywordBean();
        query.setPageNum(1);
        query.setPageSize(30);
        query.setKeyword("测试");
        String body = JSON.toJSONString(query);
        ResultActions reaction = this.mockMvc.perform(MockMvcRequestBuilders.post(uri)
                .contentType(MediaType.APPLICATION_JSON).content(body).accept(MediaType.APPLICATION_JSON)
                .header("Content-Type", "application/json").header("token", token));
        MvcResult mvcResult = reaction.andReturn();
        String rs = mvcResult.getResponse().getContentAsString();
        JSONObject json = JSON.parseObject(rs);
        log.info(rs);
        assertTrue(json.getBooleanValue("success") == true);
    }

    @Test
    public void addRoleMenuRlt() throws Exception {
        String uri = "/permission/role/addRoleMenuRlt";
        List<SysRoleModuleRlt> list = new ArrayList<SysRoleModuleRlt>();
        String body = JSON.toJSONString(list);
        ResultActions reaction = this.mockMvc.perform(MockMvcRequestBuilders.post(uri)
                .contentType(MediaType.APPLICATION_JSON).content(body).accept(MediaType.APPLICATION_JSON)
                .header("Content-Type", "application/json").header("token", token));
        MvcResult mvcResult = reaction.andReturn();
        String rs = mvcResult.getResponse().getContentAsString();
        log.info(rs);
        JSONObject json = JSON.parseObject(rs);
        assertTrue(json.getBooleanValue("success") == false);
    }

    @Test
    public void fAddRoleMenuRlt() throws Exception {
        String uri = "/permission/role/addRoleMenuRlt";
        SysRoleModuleRlt param = new SysRoleModuleRlt();
        param.setModuleId(1L);
        param.setRoleId(1L);
        param.setId(3301316716050000l);
        List<SysRoleModuleRlt> list = new ArrayList<SysRoleModuleRlt>();
        list.add(param);
        String body = JSON.toJSONString(list);
        log.info(body);

        ResultActions reaction = this.mockMvc.perform(MockMvcRequestBuilders.post(uri)
                .contentType(MediaType.APPLICATION_JSON).content(body).accept(MediaType.APPLICATION_JSON)
                .header("Content-Type", "application/json").header("token", token));
        MvcResult mvcResult = reaction.andReturn();
        String rs = mvcResult.getResponse().getContentAsString();
        log.info(rs);
    }

    @Test
    public void iAddRoleDataModuleRlt() throws Exception {
        String uri = "/permission/role/addRoleDataModuleRlt";
        SysRoleDataModuleRlt param = new SysRoleDataModuleRlt();
        param.setDataModuleCode("CICLASS");
        param.setRoleId(1L);
        param.setDataValue("100000000515515");
        param.setIssee(1);
        param.setIsupdate(0);
        param.setIscreate(0);
        param.setIsdelete(0);
        param.setUid("treeID");
        List<SysRoleDataModuleRlt> list = new ArrayList<>();
        list.add(param);
        String body = JSON.toJSONString(list);
        ResultActions reaction = this.mockMvc.perform(MockMvcRequestBuilders.post(uri)
                .contentType(MediaType.APPLICATION_JSON).content(body).accept(MediaType.APPLICATION_JSON)
                .header("Content-Type", "application/json").header("token", token));
        MvcResult mvcResult = reaction.andReturn();
        String rs = mvcResult.getResponse().getContentAsString();
        JSONObject json = JSON.parseObject(rs);
        assertTrue(json.getBooleanValue("success") == true);
    }

    @Test
    public void jAddRoleDataModuleRlt() throws Exception {
        String uri = "/permission/role/addRoleDataModuleRlt";
        SysRoleDataModuleRlt param = new SysRoleDataModuleRlt();
        param.setDataModuleCode("CICLASS");
        param.setRoleId(1L);
        param.setUid("treeID");
        param.setDataValue("100000000515515");
        param.setIsupdate(1);
        List<SysRoleDataModuleRlt> list = new ArrayList<>();
        list.add(param);
        String body = JSON.toJSONString(list);
        ResultActions reaction = this.mockMvc.perform(MockMvcRequestBuilders.post(uri)
                .contentType(MediaType.APPLICATION_JSON).content(body).accept(MediaType.APPLICATION_JSON)
                .header("Content-Type", "application/json").header("token", token));
        MvcResult mvcResult = reaction.andReturn();
        String rs = mvcResult.getResponse().getContentAsString();
        JSONObject json = JSON.parseObject(rs);
        assertTrue(json.getBooleanValue("success") == true);
    }

    @Test
    public void kGetAuthDataRoleByRoleId() throws Exception {
        String uri = "/permission/role/getAuthDataRoleByRoleId";
        CAuthBean param = new CAuthBean();
        param.setDataModuleCode("CICLASS");
        param.setRoleId(1L);
        String body = JSON.toJSONString(param);
        ResultActions reaction = this.mockMvc.perform(MockMvcRequestBuilders.post(uri)
                .contentType(MediaType.APPLICATION_JSON).content(body).accept(MediaType.APPLICATION_JSON)
                .header("Content-Type", "application/json").header("token", token));
        MvcResult mvcResult = reaction.andReturn();
        String rs = mvcResult.getResponse().getContentAsString();
        JSONObject json = JSON.parseObject(rs);
        log.info(rs);
        assertTrue(json.getBooleanValue("success") == true);
    }

    @Test
    public void getAllDataRoleMenu() throws Exception {
        String uri = "/permission/role/getAllDataRoleMenu";
        ResultActions reaction = this.mockMvc.perform(MockMvcRequestBuilders.post(uri));
        MvcResult mvcResult = reaction.andReturn();
        String rs = mvcResult.getResponse().getContentAsString();
        JSONObject json = JSON.parseObject(rs);
        log.info(rs);
        assertTrue(json.getBooleanValue("success") == true);
    }

    @Test
    public void getAuthDataRoleByUserId() throws Exception {
        String uri = "/permission/role/getAuthDataRoleByUserId";
        CAuthBean param = new CAuthBean();
        param.setDataModuleCode("CICLASS");
        param.setUserId(1L);
        String body = JSON.toJSONString(param);
        ResultActions reaction = this.mockMvc.perform(MockMvcRequestBuilders.post(uri)
                .contentType(MediaType.APPLICATION_JSON).content(body).accept(MediaType.APPLICATION_JSON)
                .header("Content-Type", "application/json").header("token", token));
        MvcResult mvcResult = reaction.andReturn();
        String rs = mvcResult.getResponse().getContentAsString();
        JSONObject json = JSON.parseObject(rs);
        log.info(rs);
        assertTrue(json.getBooleanValue("success") == true);
    }

    @Test
    public void getAllMenu() throws Exception {
        String uri = "/permission/role/getAllMenu";
        ResultActions reaction = this.mockMvc.perform(MockMvcRequestBuilders.post(uri)
                .contentType(MediaType.APPLICATION_JSON).content("").accept(MediaType.APPLICATION_JSON)
                .header("Content-Type", "application/json").header("token", token));
        MvcResult mvcResult = reaction.andReturn();
        String rs = mvcResult.getResponse().getContentAsString();
        JSONObject json = JSON.parseObject(rs);
        log.info(rs);
        assertTrue(json.getBooleanValue("success") == true);
    }

    @Test
    public void getAuthMenuByRoleId() throws Exception {
        String uri = "/permission/role/getAuthMenuByRoleId";
        ResultActions reaction = this.mockMvc.perform(MockMvcRequestBuilders.post(uri)
                .contentType(MediaType.APPLICATION_JSON).content("3335797691050019").accept(MediaType.APPLICATION_JSON)
                .header("Content-Type", "application/json").header("token", token));
        MvcResult mvcResult = reaction.andReturn();
        String rs = mvcResult.getResponse().getContentAsString();
        JSONObject json = JSON.parseObject(rs);
        log.info(rs);
        assertTrue(json.getBooleanValue("success") == true);
    }

    @Test
    public void getAuthMenuByUserId() throws Exception {
        String uri = "/permission/role/getAuthMenuByUserId";
        String body = "3319202340750001";
        ResultActions reaction = this.mockMvc.perform(MockMvcRequestBuilders.post(uri)
                .contentType(MediaType.APPLICATION_JSON).content(body).accept(MediaType.APPLICATION_JSON)
                .header("Content-Type", "application/json").header("token", token));
        MvcResult mvcResult = reaction.andReturn();
        String rs = mvcResult.getResponse().getContentAsString();
        JSONObject json = JSON.parseObject(rs);
        log.info(rs);
        assertTrue(json.getBooleanValue("success") == true);
    }

    @Test
    public void getRolesByUserId() throws Exception {
        String uri = "/permission/role/getRolesByUserId";
        String body = "3319202340750001";
        ResultActions reaction = this.mockMvc.perform(MockMvcRequestBuilders.post(uri)
                .contentType(MediaType.APPLICATION_JSON).content(body).accept(MediaType.APPLICATION_JSON)
                .header("Content-Type", "application/json").header("token", token));
        MvcResult mvcResult = reaction.andReturn();
        String rs = mvcResult.getResponse().getContentAsString();
        JSONObject json = JSON.parseObject(rs);
        log.info(rs);
        assertTrue(json.getBooleanValue("success") == true);
    }

    @Test
    public void getUserAuthDataRoleByUserId() throws Exception {
        String uri = "/permission/role/getUserAuthDataRoleByUserId";
        CAuthBean bean = new CAuthBean();
        bean.setDataModuleCode("CICLASS");
        bean.setUserId(3319202340750001L);
        String body = JSON.toJSONString(bean);

        ResultActions reaction = this.mockMvc.perform(MockMvcRequestBuilders.post(uri)
                .contentType(MediaType.APPLICATION_JSON).content(body).accept(MediaType.APPLICATION_JSON)
                .header("Content-Type", "application/json").header("token", token));
        MvcResult mvcResult = reaction.andReturn();
        String rs = mvcResult.getResponse().getContentAsString();
        JSONObject json = JSON.parseObject(rs);
        log.info(rs);
        assertTrue(json.getBooleanValue("success") == true);
    }

    @Test
    public void getRoleAuthDataRoleByUserId() throws Exception {
        String uri = "/permission/role/getRoleAuthDataRoleByUserId";
        CAuthBean bean = new CAuthBean();
        bean.setDataModuleCode("CICLASS");
        bean.setUserId(3319202340750001L);
        String body = JSON.toJSONString(bean);

        ResultActions reaction = this.mockMvc.perform(MockMvcRequestBuilders.post(uri)
                .contentType(MediaType.APPLICATION_JSON).content(body).accept(MediaType.APPLICATION_JSON)
                .header("Content-Type", "application/json").header("token", token));
        MvcResult mvcResult = reaction.andReturn();
        String rs = mvcResult.getResponse().getContentAsString();
        JSONObject json = JSON.parseObject(rs);
        log.info(rs);
        assertTrue(json.getBooleanValue("success") == true);
    }

    // @Test
    public void getDataModuleDataById() throws Exception {
        String uri = "/permission/role/getDataModuleDataById";
        String body = "1";
        ResultActions reaction = this.mockMvc.perform(MockMvcRequestBuilders.post(uri)
                .contentType(MediaType.APPLICATION_JSON).content(body).accept(MediaType.APPLICATION_JSON)
                .header("Content-Type", "application/json").header("token", token));
        MvcResult mvcResult = reaction.andReturn();
        String rs = mvcResult.getResponse().getContentAsString();
        JSONObject json = JSON.parseObject(rs);
        log.info(rs);
        assertTrue(json.getBooleanValue("success") == true);
    }

    @Test
    public void getAllCIClassRole() throws Exception {
        String uri = "/permission/role/getAllCIClassRole";
        String body = "1";
        ResultActions reaction = this.mockMvc.perform(MockMvcRequestBuilders.post(uri)
                .contentType(MediaType.APPLICATION_JSON).content(body).accept(MediaType.APPLICATION_JSON)
                .header("Content-Type", "application/json").header("token", token));
        MvcResult mvcResult = reaction.andReturn();
        String rs = mvcResult.getResponse().getContentAsString();
        JSONObject json = JSON.parseObject(rs);
        log.info(rs);
        assertTrue(json.getBooleanValue("success") == true);
    }

    @Test
    public void getAllCITagRole() throws Exception {
        String uri = "/permission/role/getAllCITagRole";
        String body = "1";
        ResultActions reaction = this.mockMvc.perform(MockMvcRequestBuilders.post(uri)
                .contentType(MediaType.APPLICATION_JSON).content(body).accept(MediaType.APPLICATION_JSON)
                .header("Content-Type", "application/json").header("token", token));
        MvcResult mvcResult = reaction.andReturn();
        String rs = mvcResult.getResponse().getContentAsString();
        JSONObject json = JSON.parseObject(rs);
        log.info(rs);
        assertTrue(json.getBooleanValue("success") == true);
    }

    @Test
    public void addUserDataModuleRlt() throws Exception {
        String uri = "/permission/role/addUserDataModuleRlt";
        SysUserDataModuleRlt param = new SysUserDataModuleRlt();
        param.setDataModuleCode("CICLASS");
        param.setDataValue("100000000515515");
        param.setIssee(1);
        param.setIsupdate(0);
        param.setIscreate(0);
        param.setIsdelete(0);
        param.setUid("uidtest");
        param.setUserId(10000l);

        List<SysUserDataModuleRlt> list = new ArrayList<>();
        list.add(param);
        String body = JSON.toJSONString(list);
        ResultActions reaction = this.mockMvc.perform(MockMvcRequestBuilders.post(uri)
                .contentType(MediaType.APPLICATION_JSON).content(body).accept(MediaType.APPLICATION_JSON)
                .header("Content-Type", "application/json").header("token", token));
        MvcResult mvcResult = reaction.andReturn();
        String rs = mvcResult.getResponse().getContentAsString();
        JSONObject json = JSON.parseObject(rs);
        log.info(rs);
        assertTrue(json.getBooleanValue("success") == true);
    }

    @Test
    public void addUserMenuRlt() throws Exception {
        String uri = "/permission/role/addUserMenuRlt";
        List<SysUserModuleRlt> list = new ArrayList<SysUserModuleRlt>();
        SysUserModuleRlt param = new SysUserModuleRlt();
        param.setModuleId(1L);
        param.setUserId(1000L);
        param.setId(3301316716050000l);
        list.add(param);
        String body = JSON.toJSONString(list);
        ResultActions reaction = this.mockMvc.perform(MockMvcRequestBuilders.post(uri)
                .contentType(MediaType.APPLICATION_JSON).content(body).accept(MediaType.APPLICATION_JSON)
                .header("Content-Type", "application/json").header("token", token));
        MvcResult mvcResult = reaction.andReturn();
        String rs = mvcResult.getResponse().getContentAsString();
        log.info(rs);
        JSONObject json = JSON.parseObject(rs);
        assertTrue(json.getBooleanValue("success") == true);
    }

}
