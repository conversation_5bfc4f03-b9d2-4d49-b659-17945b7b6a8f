package com.uino.api.client.sys;

import com.uino.bean.cmdb.base.ESTableDataConfigInfo;

/**
 * CI数据配置
 * 
 * <AUTHOR>
 */
public interface ITableDataConfigApiSvc {

    /**
     * 查询CI数据配置
     * 
     * @param uid
     * @return
     */
    ESTableDataConfigInfo getCIDataConfigInfo(String uid);
    ESTableDataConfigInfo getCIDataConfigInfo(Long domainId,String uid);

    /**
     * 保存CI数据配置
     * 
     * @param configInfo
     * @return
     */
    Long saveCIDataConfigInfo(ESTableDataConfigInfo configInfo);
}
