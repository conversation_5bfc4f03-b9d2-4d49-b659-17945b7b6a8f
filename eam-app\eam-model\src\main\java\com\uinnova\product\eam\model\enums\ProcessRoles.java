package com.uinnova.product.eam.model.enums;

public enum ProcessRoles {

   //流程所有者,流程责任人,流程编写人
    Process_OWNER("流程所有者"),
    Process_RESPONSIBLE("流程责任人"),
    Process_WRITER("流程编写人");


    private String processRolesName;

    ProcessRoles(String processRolesName){
        this.processRolesName = processRolesName;
    }

    public String getProcessRolesName(){
        return this.processRolesName;
    }
}
