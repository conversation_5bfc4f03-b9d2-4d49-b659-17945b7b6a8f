package com.uinnova.product.vmdb.provider.quality.bean;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.vmdb.comm.model.quality.CcCiQualityRule;
import com.uinnova.product.vmdb.comm.model.quality.CcCiQualityRuleAttr;
import com.uinnova.product.vmdb.comm.model.quality.CcCiQualityRuleRlt;
import com.uinnova.product.vmdb.comm.model.quality.CcCiQualitySum;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CiClassRltInfo;
import com.uinnova.product.vmdb.provider.rule.bean.TagDefInfo;

import java.io.Serializable;
import java.util.List;

@Comment("CI质量检测完整规则信息")
public class CiQualityRuleInfo implements Serializable {

	private static final long serialVersionUID = 1L;
	
	@Comment("指定的分类")
	private Long classId;
	
	@Comment("指定的数量")
	private Integer pageSize;
	
	@Comment("指定的页码")
	private Integer pageNum;
	
	@Comment("规则描述信息")
	private CcCiQualityRule ciQualityRule;
	
	@Comment("规则中ci tag的信息")
	private TagDefInfo tagRuleInfo;
	
	@Comment("规则中验证合规的条件  1201 1202")
	private List<CcCiQualityRuleRlt> ciQualityRuleRlts;

	@Comment("规则中验证合规的查询条件  1001 1101")
	private List<CcCiQualityRuleAttr> ciQualityRuleAttrs;
	
	@Comment("规则相关的计算结果")
	private List<CcCiQualitySum> ciQualitySums;
	
	@Comment("tag中定义的分类信息")
	private List<CcCiClassInfo> classInfos;
	
	@Comment("分类与分类关系的数据")
	private List<CiClassRltInfo> ciClassRltInfo;
	
	public CcCiQualityRule getCiQualityRule() {
		return ciQualityRule;
	}

	public void setCiQualityRule(CcCiQualityRule ciQualityRule) {
		this.ciQualityRule = ciQualityRule;
	}


	public TagDefInfo getTagRuleInfo() {
		return tagRuleInfo;
	}

	public void setTagRuleInfo(TagDefInfo tagRuleInfo) {
		this.tagRuleInfo = tagRuleInfo;
	}

	public List<CcCiQualityRuleRlt> getCiQualityRuleRlts() {
		return ciQualityRuleRlts;
	}

	public void setCiQualityRuleRlts(List<CcCiQualityRuleRlt> ciQualityRuleRlts) {
		this.ciQualityRuleRlts = ciQualityRuleRlts;
	}

	public List<CcCiQualityRuleAttr> getCiQualityRuleAttrs() {
		return ciQualityRuleAttrs;
	}

	public void setCiQualityRuleAttrs(List<CcCiQualityRuleAttr> ciQualityRuleAttrs) {
		this.ciQualityRuleAttrs = ciQualityRuleAttrs;
	}

	public List<CcCiQualitySum> getCiQualitySums() {
		return ciQualitySums;
	}

	public void setCiQualitySums(List<CcCiQualitySum> ciQualitySums) {
		this.ciQualitySums = ciQualitySums;
	}

	public List<CcCiClassInfo> getClassInfos() {
		return classInfos;
	}

	public void setClassInfos(List<CcCiClassInfo> classInfos) {
		this.classInfos = classInfos;
	}

	public List<CiClassRltInfo> getCiClassRltInfo() {
		return ciClassRltInfo;
	}

	public void setCiClassRltInfo(List<CiClassRltInfo> ciClassRltInfo) {
		this.ciClassRltInfo = ciClassRltInfo;
	}

	public Long getClassId() {
		return classId;
	}

	public void setClassId(Long classId) {
		this.classId = classId;
	}

	public Integer getPageSize() {
		return pageSize;
	}

	public void setPageSize(Integer pageSize) {
		this.pageSize = pageSize;
	}

	public Integer getPageNum() {
		return pageNum;
	}

	public void setPageNum(Integer pageNum) {
		this.pageNum = pageNum;
	}
	
	
	
}
