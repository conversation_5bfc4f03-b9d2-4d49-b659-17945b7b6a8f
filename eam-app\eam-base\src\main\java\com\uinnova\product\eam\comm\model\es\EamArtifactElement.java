package com.uinnova.product.eam.comm.model.es;

import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.util.List;

@Data
@Comment("制品标签管理表[UINO_EAM_ARTIFACT_ELEMENT]")
public class EamArtifactElement implements EntityBean {

    @Comment("id标识")
    private Long id;

    @Comment("图例分栏名称")
    private String columnName;

    @Comment("制品信息表主id")
    private Long artifactId;

    @Comment("制品类型分类 1=业务流程建模 2=业务组件建模 3=需求关联分析 4=IT架构设计 5=其他 6=数据建模-概念实体关系图 7=数据建模-逻辑实体关系图")
    private Integer typeClassification;

    @Comment("新建制品的所有标签")
    private List<String> elements;

    @Comment("新建制品分栏的顺序")
    private Integer orderNum;

    @Comment(("制品引用class"))
    private String quoteClassInfo;

    @Comment(("制品分栏信息类型 type=1:图形、对象配置项 type=2:资产数据配置项 type=3:关系配置项 type=4:模板配置项"))
    private Integer type;

    @Comment("制品创建时间")
    private Long createTime;

    @Comment("制品更改时间")
    private Long modifyTime;
}
