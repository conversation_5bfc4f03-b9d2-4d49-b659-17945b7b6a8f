package com.uino.service.simulation.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.binary.core.exception.MessageException;
import com.uino.bean.sys.base.ESDictionaryClassInfo;
import com.uino.dao.BaseConst;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

import com.binary.core.io.Resource;
import com.binary.core.util.BinaryUtils;
import com.uino.bean.cmdb.business.ImportResultMessage;
import com.uino.bean.monitor.base.ESMonSysSeverityInfo;
import com.uino.bean.sys.base.ESDictionaryItemInfo;
import com.uino.bean.sys.query.ESDictionaryItemSearchBean;
import com.uino.bean.sys.query.ExportDictionaryDto;
import com.uino.service.simulation.IMonSysSeveritySvc;
import com.uino.service.sys.microservice.IDictionarySvc;
import com.uino.util.change_notify.severity.SeverityChangeProcs;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
public class MonSysSeveritySvc implements IMonSysSeveritySvc {

	// @Autowired
	// private ESMonSysSeveritySvc esMonSysSeveritySvc;

	@Autowired
	private SeverityChangeProcs severityChangeProcs;

	@Autowired
	private IDictionarySvc dictSvc;


	@Value("${http.resource.space:}")
	private String rsmSlaveRoot;

	/**
	 * 获取所属域告警定级字典id
	 * @return
	 */
	private ESDictionaryClassInfo findMonSysSeverityDictClassId(Long domainId){
		ESDictionaryItemSearchBean bean = new ESDictionaryItemSearchBean();
		bean.setDomainId(domainId);
		bean.setDictName(BaseConst._MONSYS_SEVERITY_NAME);

		List<ESDictionaryClassInfo> esDictionaryClassInfos = dictSvc.queryDcitClassInfosByBean(bean);
		if (esDictionaryClassInfos.isEmpty()) {
			throw new MessageException("告警定级字典不存在");
		}
		return esDictionaryClassInfos.get(0);
	}

	@Override
	public List<ESMonSysSeverityInfo> querySeverityList(Long domainId, String searchVal) {
		ESDictionaryItemSearchBean bean = ESDictionaryItemSearchBean.builder().dictName(BaseConst._MONSYS_SEVERITY_NAME).domainId(domainId)
				.sortField("attrs.severity").isAsc(true).build();
		if (searchVal != null && !searchVal.trim().equals("")) {
			bean.setKeyword(searchVal);
		}
		List<ESDictionaryItemInfo> items = dictSvc.searchDictItemListByBean(bean);
		return this.transDictItemToMonSeverityInfo(items);
	}

	@Override
	public ESMonSysSeverityInfo getMonSeverityInfoById(Long id) {
		ESDictionaryItemInfo item = dictSvc.getDictItemInfoById(id);
		List<ESMonSysSeverityInfo> severityInfos = this.transDictItemToMonSeverityInfo(Collections.singletonList(item));
		return severityInfos.get(0);
	}

	@Override
	public Long saveOrUpdateSeverity(ESMonSysSeverityInfo saveDto) {
		if (saveDto.getDomainId() == null) {
			saveDto.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
		}
		List<ESDictionaryItemInfo> items = this.transMonSeverityInfoToDictItem(saveDto.getDomainId(), Collections.singletonList(saveDto));
		severityChangeProcs.saveOrUpdate(Arrays.asList(saveDto));
		return dictSvc.saveOrUpdateDictionaryItem(items.get(0));
	}

	@Override
	public void deleteServrityByIds(Collection<Long> delIds) {
		Assert.notEmpty(delIds, "必须指明事件定义ids");
		dictSvc.deleteItemByIds(delIds);
		severityChangeProcs.deleteByIds(delIds);
	}

	@Override
	public Resource exportSeverityInfos(Long domainId, Boolean isTpl) {
		ExportDictionaryDto dto = new ExportDictionaryDto();
		dto.setDictClassId(findMonSysSeverityDictClassId(domainId).getId());
		dto.setIsTpl(isTpl);
		return dictSvc.exportDictionaryItems(dto);
	}

	@Override
	public ImportResultMessage importSeverityInfos(Long domainId, MultipartFile file) {
		return dictSvc.importDictionaryItems(findMonSysSeverityDictClassId(domainId).getId(), file);
	}

	private List<ESMonSysSeverityInfo> transDictItemToMonSeverityInfo(List<ESDictionaryItemInfo> list) {
		List<ESMonSysSeverityInfo> severityInfos = new ArrayList<>();
		if (!BinaryUtils.isEmpty(list)) {
			for (ESDictionaryItemInfo item : list) {
				Map<String, String> attrs = item.getAttrs();
				ESMonSysSeverityInfo severityInfo = new ESMonSysSeverityInfo();
				severityInfo.setId(item.getId());
				if(!BinaryUtils.isEmpty(attrs.get("severity"))){
					severityInfo.setSeverity(Integer.valueOf(attrs.get("severity")));
				}
				severityInfo.setColor(attrs.get("color"));
				String voiceUrl = attrs.get("voiceUrl");
				if (!BinaryUtils.isEmpty(voiceUrl)) {
					String voiceName = voiceUrl.substring(voiceUrl.lastIndexOf("/") + 1);
					severityInfo.setVoiceName(voiceName);
					//拼接声音路径
					if (voiceUrl.startsWith(rsmSlaveRoot)) {
						voiceUrl = voiceUrl.replaceAll(rsmSlaveRoot, "");
					}
					severityInfo.setVoiceUrl(voiceUrl);
				}
				severityInfo.setChineseName(attrs.get("chineseName"));
				severityInfo.setOption(item.getOption());
				severityInfo.setCreator(item.getCreator());
				severityInfo.setModifier(item.getModifier());
				severityInfo.setCreateTime(item.getCreateTime());
				severityInfo.setModifyTime(item.getModifyTime());
				severityInfo.setDomainId(item.getDomainId());
				severityInfos.add(severityInfo);
			}
		}
		return severityInfos;
	}

	private List<ESDictionaryItemInfo> transMonSeverityInfoToDictItem(Long domainId, List<ESMonSysSeverityInfo> severityInfos) {
		List<ESDictionaryItemInfo> itemInfos = new ArrayList<>();
		if (!BinaryUtils.isEmpty(severityInfos)) {
			ESDictionaryClassInfo dictionaryClassInfo = findMonSysSeverityDictClassId(domainId);
			for (ESMonSysSeverityInfo severityInfo : severityInfos) {
				ESDictionaryItemInfo item = new ESDictionaryItemInfo();
				item.setDictClassId(dictionaryClassInfo.getId());
				item.setId(severityInfo.getId());
				Map<String, String> attrs = new HashMap<String, String>();
				attrs.put("severity", String.valueOf(severityInfo.getSeverity()));
				attrs.put("color", severityInfo.getColor());
				String voiceUrl=severityInfo.getVoiceUrl();

				if (voiceUrl.startsWith(rsmSlaveRoot)) {
					voiceUrl = voiceUrl.replaceAll(rsmSlaveRoot, "");
				}
				//此处key为voiceName为了前端表头取值，实际库中存的是url，查询的时候会再次转换
				attrs.put("voiceUrl", voiceUrl);
				attrs.put("chineseName", severityInfo.getChineseName());
				attrs.values().removeIf(val -> BinaryUtils.isEmpty(val));
				item.setAttrs(attrs);
				item.setOption(severityInfo.getOption());
				item.setCreator(severityInfo.getCreator());
				item.setModifier(severityInfo.getModifier());
				item.setCreateTime(severityInfo.getCreateTime());
				item.setModifyTime(severityInfo.getModifyTime());
				item.setDomainId(severityInfo.getDomainId());
				itemInfos.add(item);
			}
		}
		return itemInfos;
	}

}
