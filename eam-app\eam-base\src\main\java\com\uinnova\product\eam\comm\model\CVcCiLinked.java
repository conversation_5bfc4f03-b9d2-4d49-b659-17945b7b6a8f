package com.uinnova.product.eam.comm.model;




import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;


@Comment("CI链路表[VC_CI_LINKED]")
public class CVcCiLinked implements Condition {
	private static final long serialVersionUID = 1L;


	@Comment("ID[ID] operate-Equal[=]")
	private Long id;


	@Comment("ID[ID] operate-In[in]")
	private Long[] ids;


	@Comment("ID[ID] operate-GTEqual[>=]")
	private Long startId;

	@Comment("ID[ID] operate-LTEqual[<=]")
	private Long endId;


	@Comment("链路名称[LINKED_NAME] operate-Like[like]")
	private String linkedName;


	@Comment("链路名称[LINKED_NAME] operate-Equal[=]")
	private String linkedNameEqual;


	@Comment("链路名称[LINKED_NAME] operate-In[in]")
	private String[] linkedNames;


	@Comment("来源类型[SOURCE_TYPE] operate-Equal[=]    1=视图")
	private Integer sourceType;


	@Comment("来源类型[SOURCE_TYPE] operate-In[in]    1=视图")
	private Integer[] sourceTypes;


	@Comment("来源类型[SOURCE_TYPE] operate-GTEqual[>=]    1=视图")
	private Integer startSourceType;

	@Comment("来源类型[SOURCE_TYPE] operate-LTEqual[<=]    1=视图")
	private Integer endSourceType;


	@Comment("是否显示[IS_DISPLAY] operate-Equal[=]    是否显示当前链路到视图(1=显示,0不显示)")
	private Integer isDisplay;


	@Comment("是否显示[IS_DISPLAY] operate-In[in]    是否显示当前链路到视图(1=显示,0不显示)")
	private Integer[] isDisplays;


	@Comment("是否显示[IS_DISPLAY] operate-GTEqual[>=]    是否显示当前链路到视图(1=显示,0不显示)")
	private Integer startIsDisplay;

	@Comment("是否显示[IS_DISPLAY] operate-LTEqual[<=]    是否显示当前链路到视图(1=显示,0不显示)")
	private Integer endIsDisplay;


	@Comment("来源ID[SOURCE_ID] operate-Equal[=]")
	private Long sourceId;


	@Comment("来源ID[SOURCE_ID] operate-In[in]")
	private Long[] sourceIds;


	@Comment("来源ID[SOURCE_ID] operate-GTEqual[>=]")
	private Long startSourceId;

	@Comment("来源ID[SOURCE_ID] operate-LTEqual[<=]")
	private Long endSourceId;


	@Comment("CI代码列表[CI_CODES] operate-Like[like]    逗号分隔")
	private String ciCodes;


	@Comment("链路颜色[LINKED_COLOR] operate-Like[like]")
	private String linkedColor;


	@Comment("链路颜色[LINKED_COLOR] operate-Equal[=]")
	private String linkedColorEqual;


	@Comment("链路颜色[LINKED_COLOR] operate-In[in]")
	private String[] linkedColors;


	@Comment("备用_1[CUSTOM_1] operate-Like[like]")
	private String custom1;


	@Comment("备用_1[CUSTOM_1] operate-Equal[=]")
	private String custom1Equal;


	@Comment("备用_1[CUSTOM_1] operate-In[in]")
	private String[] custom1s;


	@Comment("备用_2[CUSTOM_2] operate-Like[like]")
	private String custom2;


	@Comment("备用_2[CUSTOM_2] operate-Equal[=]")
	private String custom2Equal;


	@Comment("备用_2[CUSTOM_2] operate-In[in]")
	private String[] custom2s;


	@Comment("备用_3[CUSTOM_3] operate-Like[like]")
	private String custom3;


	@Comment("备用_3[CUSTOM_3] operate-Equal[=]")
	private String custom3Equal;


	@Comment("备用_3[CUSTOM_3] operate-In[in]")
	private String[] custom3s;


	@Comment("备用_4[CUSTOM_4] operate-Like[like]")
	private String custom4;


	@Comment("备用_4[CUSTOM_4] operate-Equal[=]")
	private String custom4Equal;


	@Comment("备用_4[CUSTOM_4] operate-In[in]")
	private String[] custom4s;


	@Comment("备用_5[CUSTOM_5] operate-Like[like]")
	private String custom5;


	@Comment("备用_5[CUSTOM_5] operate-Equal[=]")
	private String custom5Equal;


	@Comment("备用_5[CUSTOM_5] operate-In[in]")
	private String[] custom5s;


	@Comment("备用_6[CUSTOM_6] operate-Like[like]")
	private String custom6;


	@Comment("备用_6[CUSTOM_6] operate-Equal[=]")
	private String custom6Equal;


	@Comment("备用_6[CUSTOM_6] operate-In[in]")
	private String[] custom6s;


	@Comment("所属域[DOMAIN_ID] operate-Equal[=]")
	private Long domainId;


	@Comment("所属域[DOMAIN_ID] operate-In[in]")
	private Long[] domainIds;


	@Comment("所属域[DOMAIN_ID] operate-GTEqual[>=]")
	private Long startDomainId;

	@Comment("所属域[DOMAIN_ID] operate-LTEqual[<=]")
	private Long endDomainId;


	@Comment("数据状态[DATA_STATUS] operate-Equal[=]    数据状态:数据状态：1-正常 0-删除")
	private Integer dataStatus;


	@Comment("数据状态[DATA_STATUS] operate-In[in]    数据状态:数据状态：1-正常 0-删除")
	private Integer[] dataStatuss;


	@Comment("数据状态[DATA_STATUS] operate-GTEqual[>=]    数据状态:数据状态：1-正常 0-删除")
	private Integer startDataStatus;

	@Comment("数据状态[DATA_STATUS] operate-LTEqual[<=]    数据状态:数据状态：1-正常 0-删除")
	private Integer endDataStatus;


	@Comment("创建人[CREATOR] operate-Like[like]")
	private String creator;


	@Comment("创建人[CREATOR] operate-Equal[=]")
	private String creatorEqual;


	@Comment("创建人[CREATOR] operate-In[in]")
	private String[] creators;


	@Comment("修改人[MODIFIER] operate-Like[like]")
	private String modifier;


	@Comment("修改人[MODIFIER] operate-Equal[=]")
	private String modifierEqual;


	@Comment("修改人[MODIFIER] operate-In[in]")
	private String[] modifiers;


	@Comment("创建时间[CREATE_TIME] operate-Equal[=]    创建时间:yyyyMMddHHmmss")
	private Long createTime;


	@Comment("创建时间[CREATE_TIME] operate-In[in]    创建时间:yyyyMMddHHmmss")
	private Long[] createTimes;


	@Comment("创建时间[CREATE_TIME] operate-GTEqual[>=]    创建时间:yyyyMMddHHmmss")
	private Long startCreateTime;

	@Comment("创建时间[CREATE_TIME] operate-LTEqual[<=]    创建时间:yyyyMMddHHmmss")
	private Long endCreateTime;


	@Comment("修改时间[MODIFY_TIME] operate-Equal[=]    修改时间:yyyyMMddHHmmss")
	private Long modifyTime;


	@Comment("修改时间[MODIFY_TIME] operate-In[in]    修改时间:yyyyMMddHHmmss")
	private Long[] modifyTimes;


	@Comment("修改时间[MODIFY_TIME] operate-GTEqual[>=]    修改时间:yyyyMMddHHmmss")
	private Long startModifyTime;

	@Comment("修改时间[MODIFY_TIME] operate-LTEqual[<=]    修改时间:yyyyMMddHHmmss")
	private Long endModifyTime;




	public Long getId() {
		return this.id;
	}
	public void setId(Long id) {
		this.id = id;
	}


	public Long[] getIds() {
		return this.ids;
	}
	public void setIds(Long[] ids) {
		this.ids = ids;
	}


	public Long getStartId() {
		return this.startId;
	}
	public void setStartId(Long startId) {
		this.startId = startId;
	}


	public Long getEndId() {
		return this.endId;
	}
	public void setEndId(Long endId) {
		this.endId = endId;
	}


	public String getLinkedName() {
		return this.linkedName;
	}
	public void setLinkedName(String linkedName) {
		this.linkedName = linkedName;
	}


	public String getLinkedNameEqual() {
		return this.linkedNameEqual;
	}
	public void setLinkedNameEqual(String linkedNameEqual) {
		this.linkedNameEqual = linkedNameEqual;
	}


	public String[] getLinkedNames() {
		return this.linkedNames;
	}
	public void setLinkedNames(String[] linkedNames) {
		this.linkedNames = linkedNames;
	}


	public Integer getSourceType() {
		return this.sourceType;
	}
	public void setSourceType(Integer sourceType) {
		this.sourceType = sourceType;
	}


	public Integer[] getSourceTypes() {
		return this.sourceTypes;
	}
	public void setSourceTypes(Integer[] sourceTypes) {
		this.sourceTypes = sourceTypes;
	}


	public Integer getStartSourceType() {
		return this.startSourceType;
	}
	public void setStartSourceType(Integer startSourceType) {
		this.startSourceType = startSourceType;
	}


	public Integer getEndSourceType() {
		return this.endSourceType;
	}
	public void setEndSourceType(Integer endSourceType) {
		this.endSourceType = endSourceType;
	}


	public Integer getIsDisplay() {
		return this.isDisplay;
	}
	public void setIsDisplay(Integer isDisplay) {
		this.isDisplay = isDisplay;
	}


	public Integer[] getIsDisplays() {
		return this.isDisplays;
	}
	public void setIsDisplays(Integer[] isDisplays) {
		this.isDisplays = isDisplays;
	}


	public Integer getStartIsDisplay() {
		return this.startIsDisplay;
	}
	public void setStartIsDisplay(Integer startIsDisplay) {
		this.startIsDisplay = startIsDisplay;
	}


	public Integer getEndIsDisplay() {
		return this.endIsDisplay;
	}
	public void setEndIsDisplay(Integer endIsDisplay) {
		this.endIsDisplay = endIsDisplay;
	}


	public Long getSourceId() {
		return this.sourceId;
	}
	public void setSourceId(Long sourceId) {
		this.sourceId = sourceId;
	}


	public Long[] getSourceIds() {
		return this.sourceIds;
	}
	public void setSourceIds(Long[] sourceIds) {
		this.sourceIds = sourceIds;
	}


	public Long getStartSourceId() {
		return this.startSourceId;
	}
	public void setStartSourceId(Long startSourceId) {
		this.startSourceId = startSourceId;
	}


	public Long getEndSourceId() {
		return this.endSourceId;
	}
	public void setEndSourceId(Long endSourceId) {
		this.endSourceId = endSourceId;
	}


	public String getCiCodes() {
		return this.ciCodes;
	}
	public void setCiCodes(String ciCodes) {
		this.ciCodes = ciCodes;
	}


	public String getLinkedColor() {
		return this.linkedColor;
	}
	public void setLinkedColor(String linkedColor) {
		this.linkedColor = linkedColor;
	}


	public String getLinkedColorEqual() {
		return this.linkedColorEqual;
	}
	public void setLinkedColorEqual(String linkedColorEqual) {
		this.linkedColorEqual = linkedColorEqual;
	}


	public String[] getLinkedColors() {
		return this.linkedColors;
	}
	public void setLinkedColors(String[] linkedColors) {
		this.linkedColors = linkedColors;
	}


	public String getCustom1() {
		return this.custom1;
	}
	public void setCustom1(String custom1) {
		this.custom1 = custom1;
	}


	public String getCustom1Equal() {
		return this.custom1Equal;
	}
	public void setCustom1Equal(String custom1Equal) {
		this.custom1Equal = custom1Equal;
	}


	public String[] getCustom1s() {
		return this.custom1s;
	}
	public void setCustom1s(String[] custom1s) {
		this.custom1s = custom1s;
	}


	public String getCustom2() {
		return this.custom2;
	}
	public void setCustom2(String custom2) {
		this.custom2 = custom2;
	}


	public String getCustom2Equal() {
		return this.custom2Equal;
	}
	public void setCustom2Equal(String custom2Equal) {
		this.custom2Equal = custom2Equal;
	}


	public String[] getCustom2s() {
		return this.custom2s;
	}
	public void setCustom2s(String[] custom2s) {
		this.custom2s = custom2s;
	}


	public String getCustom3() {
		return this.custom3;
	}
	public void setCustom3(String custom3) {
		this.custom3 = custom3;
	}


	public String getCustom3Equal() {
		return this.custom3Equal;
	}
	public void setCustom3Equal(String custom3Equal) {
		this.custom3Equal = custom3Equal;
	}


	public String[] getCustom3s() {
		return this.custom3s;
	}
	public void setCustom3s(String[] custom3s) {
		this.custom3s = custom3s;
	}


	public String getCustom4() {
		return this.custom4;
	}
	public void setCustom4(String custom4) {
		this.custom4 = custom4;
	}


	public String getCustom4Equal() {
		return this.custom4Equal;
	}
	public void setCustom4Equal(String custom4Equal) {
		this.custom4Equal = custom4Equal;
	}


	public String[] getCustom4s() {
		return this.custom4s;
	}
	public void setCustom4s(String[] custom4s) {
		this.custom4s = custom4s;
	}


	public String getCustom5() {
		return this.custom5;
	}
	public void setCustom5(String custom5) {
		this.custom5 = custom5;
	}


	public String getCustom5Equal() {
		return this.custom5Equal;
	}
	public void setCustom5Equal(String custom5Equal) {
		this.custom5Equal = custom5Equal;
	}


	public String[] getCustom5s() {
		return this.custom5s;
	}
	public void setCustom5s(String[] custom5s) {
		this.custom5s = custom5s;
	}


	public String getCustom6() {
		return this.custom6;
	}
	public void setCustom6(String custom6) {
		this.custom6 = custom6;
	}


	public String getCustom6Equal() {
		return this.custom6Equal;
	}
	public void setCustom6Equal(String custom6Equal) {
		this.custom6Equal = custom6Equal;
	}


	public String[] getCustom6s() {
		return this.custom6s;
	}
	public void setCustom6s(String[] custom6s) {
		this.custom6s = custom6s;
	}


	public Long getDomainId() {
		return this.domainId;
	}
	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}


	public Long[] getDomainIds() {
		return this.domainIds;
	}
	public void setDomainIds(Long[] domainIds) {
		this.domainIds = domainIds;
	}


	public Long getStartDomainId() {
		return this.startDomainId;
	}
	public void setStartDomainId(Long startDomainId) {
		this.startDomainId = startDomainId;
	}


	public Long getEndDomainId() {
		return this.endDomainId;
	}
	public void setEndDomainId(Long endDomainId) {
		this.endDomainId = endDomainId;
	}


	public Integer getDataStatus() {
		return this.dataStatus;
	}
	public void setDataStatus(Integer dataStatus) {
		this.dataStatus = dataStatus;
	}


	public Integer[] getDataStatuss() {
		return this.dataStatuss;
	}
	public void setDataStatuss(Integer[] dataStatuss) {
		this.dataStatuss = dataStatuss;
	}


	public Integer getStartDataStatus() {
		return this.startDataStatus;
	}
	public void setStartDataStatus(Integer startDataStatus) {
		this.startDataStatus = startDataStatus;
	}


	public Integer getEndDataStatus() {
		return this.endDataStatus;
	}
	public void setEndDataStatus(Integer endDataStatus) {
		this.endDataStatus = endDataStatus;
	}


	public String getCreator() {
		return this.creator;
	}
	public void setCreator(String creator) {
		this.creator = creator;
	}


	public String getCreatorEqual() {
		return this.creatorEqual;
	}
	public void setCreatorEqual(String creatorEqual) {
		this.creatorEqual = creatorEqual;
	}


	public String[] getCreators() {
		return this.creators;
	}
	public void setCreators(String[] creators) {
		this.creators = creators;
	}


	public String getModifier() {
		return this.modifier;
	}
	public void setModifier(String modifier) {
		this.modifier = modifier;
	}


	public String getModifierEqual() {
		return this.modifierEqual;
	}
	public void setModifierEqual(String modifierEqual) {
		this.modifierEqual = modifierEqual;
	}


	public String[] getModifiers() {
		return this.modifiers;
	}
	public void setModifiers(String[] modifiers) {
		this.modifiers = modifiers;
	}


	public Long getCreateTime() {
		return this.createTime;
	}
	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}


	public Long[] getCreateTimes() {
		return this.createTimes;
	}
	public void setCreateTimes(Long[] createTimes) {
		this.createTimes = createTimes;
	}


	public Long getStartCreateTime() {
		return this.startCreateTime;
	}
	public void setStartCreateTime(Long startCreateTime) {
		this.startCreateTime = startCreateTime;
	}


	public Long getEndCreateTime() {
		return this.endCreateTime;
	}
	public void setEndCreateTime(Long endCreateTime) {
		this.endCreateTime = endCreateTime;
	}


	public Long getModifyTime() {
		return this.modifyTime;
	}
	public void setModifyTime(Long modifyTime) {
		this.modifyTime = modifyTime;
	}


	public Long[] getModifyTimes() {
		return this.modifyTimes;
	}
	public void setModifyTimes(Long[] modifyTimes) {
		this.modifyTimes = modifyTimes;
	}


	public Long getStartModifyTime() {
		return this.startModifyTime;
	}
	public void setStartModifyTime(Long startModifyTime) {
		this.startModifyTime = startModifyTime;
	}


	public Long getEndModifyTime() {
		return this.endModifyTime;
	}
	public void setEndModifyTime(Long endModifyTime) {
		this.endModifyTime = endModifyTime;
	}


}


