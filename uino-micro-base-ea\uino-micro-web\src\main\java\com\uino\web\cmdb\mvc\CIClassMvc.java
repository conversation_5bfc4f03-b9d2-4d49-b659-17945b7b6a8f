package com.uino.web.cmdb.mvc;

import com.alibaba.fastjson.JSON;
import com.binary.core.util.BinaryUtils;
import com.binary.framework.spring.MultipartFileResource;
import com.binary.framework.util.ControllerUtils;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClass;
import com.uinnova.product.vmdb.comm.model.ci.CcCiClass;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uino.api.client.cmdb.ICIClassApiSvc;
import com.uino.bean.cmdb.base.ESCIAttrDefInfo;
import com.uino.bean.cmdb.base.ESCIClassInfo;
import com.uino.bean.cmdb.business.CIClassInfoDto;
import com.uino.bean.cmdb.business.ClassNodeInfo;
import com.uino.bean.cmdb.business.ClassReOrderDTO;
import com.uino.bean.cmdb.business.ImportResultMessage;
import com.uino.bean.cmdb.query.ESShow3dAttributeDto;
import com.uino.bean.permission.base.SysUser;
import com.uino.init.api.ApiResult;
import com.uino.init.api.ApiVersion;
import com.uino.util.sys.BeanUtil;
import com.uino.util.sys.SysUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@ApiVersion(1)
@Api(value = "对象分类", tags = {"对象管理"})
@RequestMapping("/cmdb/ciClass")
@RestController
@RefreshScope
public class CIClassMvc {

    @Autowired
    private ICIClassApiSvc classSvc;

    @ApiOperation(value = "查询对象分类树结构")
    @RequestMapping(value = "/getClassTree", method = RequestMethod.POST)
    @ModDesc(desc = "查询对象分类树结构", pDesc = "查询条件", pType = CCcCiClass.class, rDesc = "对象分类的树状结构数据", rType = List.class, rcType = ClassNodeInfo.class)
    public ApiResult<List<ClassNodeInfo>> getClassTree(HttpServletRequest request, HttpServletResponse response) {
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        List<ClassNodeInfo> ls = classSvc.getClassTree(currentUserInfo.getDomainId());
        return ApiResult.ok(this).data(ls);
    }

    @ApiOperation(value = "通过分类ID查询分类信息")
    @RequestMapping(value = "/queryById", method = RequestMethod.POST)
    @ModDesc(desc = "通过分类ID查询分类信息", pDesc = "分类ID", pType = Long.class, rDesc = "分类数据", rType = CcCiClassInfo.class)
    public ApiResult<CIClassInfoDto> queryById(HttpServletRequest request, HttpServletResponse response, @RequestBody Long id) {
        ESCIClassInfo esciClassInfo = classSvc.queryESClassInfoById(id);
        // 2021-10-14 修改错误提示信息，不存在时直接返回null
        if (BinaryUtils.isEmpty(esciClassInfo)) {
            return ApiResult.ok(this).data(null);
        }
        CIClassInfoDto result = BeanUtil.converBean(esciClassInfo, CIClassInfoDto.class);
        CcCiClass ciClass = BeanUtil.converBean(esciClassInfo, CcCiClass.class);
        result.setCiClass(ciClass);
        return ApiResult.ok(this).data(result);
    }

    @GetMapping(value = "/selectById")
    @ModDesc(desc = "通过分类ID查询分类信息", pDesc = "分类ID", pType = Long.class, rDesc = "分类数据", rType = CcCiClassInfo.class)
    public void selectById(HttpServletRequest request, HttpServletResponse response, @RequestParam Long id) {
        ESCIClassInfo esciClassInfo = classSvc.queryESClassInfoById(id);
        CIClassInfoDto result = BeanUtil.converBean(esciClassInfo, CIClassInfoDto.class);
        CcCiClass ciClass = BeanUtil.converBean(esciClassInfo, CcCiClass.class);
        result.setCiClass(ciClass);
        ESCIAttrDefInfo defInfo = new ESCIAttrDefInfo();
        defInfo.setIsRequired(1);
        defInfo.setProType(3);
        defInfo.setClassId(id);
        defInfo.setCreateTime(BinaryUtils.getNumberDateTime());
        defInfo.setIsCiDisp(0);
        defInfo.setDataStatus(1);
        defInfo.setIsMajor(1);
        defInfo.setProName("所属用户");
        defInfo.setProStdName("所属用户");
        defInfo.setId(0L);
        defInfo.setDomainId(1L);
        defInfo.setDefVal("");
        defInfo.setOrderNo(99999);
        defInfo.setForbidden(true);
        result.getAttrDefs().add(defInfo);
        ControllerUtils.returnJson(request, response, result);
    }

    @ApiOperation(value = "通过父级分类ID查询子分类信息")
    @RequestMapping(value = "/queryByParentId", method = RequestMethod.POST)
    @ModDesc(desc = "通过父级分类ID查询子分类信息", pDesc = "分类ID", pType = Long.class, rDesc = "分类数据集合", rType = List.class, rcType = CcCiClassInfo.class)
    public ApiResult<List<CcCiClassInfo>> queryByParentId(HttpServletRequest request, HttpServletResponse response, @RequestBody String body) {
        Long id = Long.parseLong(body.trim());
        CCcCiClass cdt = new CCcCiClass();
        cdt.setParentId(id);
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        List<CcCiClassInfo> result = classSvc.queryCiClassInfoList(currentUserInfo.getDomainId(), cdt, "id", true);
        return ApiResult.ok(this).data(result);
    }

    @ApiOperation(value = "条件查询分类数据")
    @RequestMapping(value = "/query", method = RequestMethod.POST)
    @ModDesc(desc = "条件查询分类数据", pDesc = "查询条件", pType = CCcCiClass.class, rDesc = "分类数据集合", rType = List.class, rcType = CcCiClassInfo.class)
    public ApiResult<List<CcCiClassInfo>> query(HttpServletRequest request, HttpServletResponse response, @RequestBody(required = false) CCcCiClass query) {
        query = query == null ? new CCcCiClass() : query;
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        query.setDomainId(currentUserInfo.getDomainId());
        List<CcCiClassInfo> result = classSvc.queryClassByCdt(query);
        return ApiResult.ok(this).data(result);
    }

    @PostMapping(value = "/saveOrUpdate")
    @ApiOperation(value = "保存对象分类信息")
    @ModDesc(desc = "保存对象分类信息", pDesc = "分类信息", pType = CIClassInfoDto.class, rDesc = "是否保存成功", rType = Long.class, rcType = Long.class)
    public ApiResult<Long> saveOrUpdate(@RequestBody CIClassInfoDto dto) {
        List<ESCIAttrDefInfo> defs = dto.getAttrDefs().stream().filter(each -> (!BinaryUtils.isEmpty(each.getId()) && each.getId().longValue() > 0) || BinaryUtils.isEmpty(each.getId())).collect(Collectors.toList());
        dto.setAttrDefs(defs);
        ESCIClassInfo record = BeanUtil.converBean(dto, ESCIClassInfo.class);
        BeanUtil.copyProperties(dto.getCiClass(), record);
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        record.setDomainId(currentUserInfo.getDomainId());
        Long id = classSvc.saveOrUpdateESCIClass(record);
        return ApiResult.ok(this).data(id);
    }

    /*@PostMapping(value = "/saveOrUpdateExtra")
    @ApiOperation(value = "保存对象分类信息")
    @ModDesc(desc = "保存对象分类信息", pDesc = "分类信息", pType = CIClassInfoDto.class, rDesc = "是否保存成功", rType = Long.class, rcType = Long.class)
    public ApiResult<Long> saveOrUpdateExtra(@RequestBody CIClassInfoDto dto) {
        ESCIClassInfo record = BeanUtil.converBean(dto, ESCIClassInfo.class);
        BeanUtil.copyProperties(dto.getCiClass(), record);
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        record.setDomainId(currentUserInfo.getDomainId());
        Long id = classSvc.saveOrUpdateESCIClassExtra(record);
        return ApiResult.ok(this).data(id);
    }*/

    @ApiOperation(value = "通过ID删除分类信息")
    @RequestMapping(value = "/removeById", method = {RequestMethod.POST, RequestMethod.DELETE})
    @ModDesc(desc = "通过ID删除分类信息", pDesc = "分类ID", pType = Long.class, rDesc = "0表示删除失败,1删除成功", rType = Long.class)
    public ApiResult<Integer> removeById(HttpServletRequest request, HttpServletResponse response, @RequestBody String body) {
        Long id = Long.parseLong(body.trim());
        Integer result = classSvc.removeCIClassById(id);
        return ApiResult.ok(this).data(result);
    }

    @ApiOperation(value = "导入对象分类属性信息")
    @RequestMapping(value = "/importClassAttr", method = RequestMethod.POST)
    @ModDesc(desc = "导入对象分类属性信息", pDesc = "要导入的文件", pType = MultipartFileResource.class, rDesc = "导入结果", rType = ImportResultMessage.class)
    public ApiResult<ImportResultMessage> importClassAttr(HttpServletRequest request, HttpServletResponse response,
                                                          @RequestParam("file") MultipartFile file) {
        BinaryUtils.checkEmpty(request.getParameter("ciClassIds"), "ciClassIds");
        Long classId = JSON.parseObject(request.getParameter("ciClassIds"), Long.class);
        ImportResultMessage result = classSvc.importCiClassAttr(file, classId);
        return ApiResult.ok(this).data(result);
    }

    @ApiOperation(value = "下载分类属性或属性模板",notes="- 下载分类属性或属性模板 \r\n -传分类id则表示下载分类属性，不传表示下载模板 \r\n - 返回值: **byte[]**")
    @RequestMapping(value = "/exportClassAttrExcel", method = RequestMethod.POST)
    @ModDesc(desc = "下载分类属性或属性模板(传分类id则表示下载分类属性,不传表示下载模板)", pDesc = "分类id集合", pType = Set.class, pcType = Long.class, rDesc = "分类属性或模板Excel文件", rType = ResponseEntity.class)
    public ResponseEntity<byte[]> exportClassAttrExcel(HttpServletRequest request, HttpServletResponse response,
                                                       @RequestParam(required = false) Set<Long> ciClassIds) {
        return classSvc.exportClassAttrExcel(ciClassIds, false);
    }

    @ApiOperation(value = "下载一键导入模板",notes="- 下载一键导入模板,无参数 \r\n - 返回值: **byte[]**")
    @RequestMapping(value = "/exportImportClassBatchExcel", method = RequestMethod.POST)
    @ModDesc(desc = "下载一键导入模板", pDesc = "无需条件", rDesc = "对象一键导入模板", rType = ResponseEntity.class)
    public ResponseEntity<byte[]> exportImportClassBatchExcel(HttpServletRequest request,
                                                              HttpServletResponse response) {
        return classSvc.exportClassAttrExcel(null, true);
    }

    @ApiOperation(value = "获取分类默认图标地址")
    @RequestMapping(value = "/getDefaultIcon", method = RequestMethod.POST)
    @ModDesc(desc = "获取分类默认图标地址", pDesc = "无需条件", rDesc = "分类默认图标地址", rType = String.class)
    public ApiResult<String> getDefaultIcon(HttpServletRequest request, HttpServletResponse response) {
        String defIcon = classSvc.getHttpResourceSpace() + "/122/default_icon.png";
        return ApiResult.ok(this).data(defIcon);
    }

	@ApiOperation(value = "获取图片属性默认图标地址")
	@RequestMapping(value = "/getDefaultPictureIcon", method = RequestMethod.POST)
	@ModDesc(desc = "获取图片属性默认图标地址", pDesc = "无需条件", rDesc = "图片属性默认图标地址", rType = String.class)
	public ApiResult<String> getDefaultPictureIcon(HttpServletRequest request, HttpServletResponse response) {
		String defIcon = classSvc.getHttpResourceSpace() + "/122/defaultIcon/default_ci_picture_attr_icon.png";
		return ApiResult.ok(this).data(defIcon);
	}

    @GetMapping(value = "/getDefaultIcon3D")
    @ApiOperation(value = "获取分类默认图标地址3D图标地址")
    public ApiResult<String> getDefaultIcon3D() {
        return ApiResult.ok(this).data(classSvc.getHttpResourceSpace() + "/122/default_3d.jpg");
    }

    @PostMapping(value = "/isShow3dAttribute")
    @ApiOperation(value = "创建分类是否增加3D属性")
    public ApiResult<ESShow3dAttributeDto> isShow3dAttribute() {
        ESShow3dAttributeDto esShow3dAttributeDto = new ESShow3dAttributeDto();
        esShow3dAttributeDto.setDefaultIcon(classSvc.getHttpResourceSpace());
        esShow3dAttributeDto.setShow3dAttribute(classSvc.isShow3dAttribute());
        return ApiResult.ok(this).data(esShow3dAttributeDto);
    }

    /**
     * ci分类拖动排序
     *
     * @param request
     * @param response
     * @param reOrderDTO
     * @author: weixuesong
     * @date: 2020/8/5 14:24
     * @return: void
     */
    @ApiOperation(value = "对象分类排序")
    @RequestMapping(value = "/reOrder", method = RequestMethod.POST)
    @ModDesc(desc = "对象分类排序", pDesc = "对象分类的排序参数", pType = ClassReOrderDTO.class, rDesc = "操作是否成功", rType = Boolean.class)
    public ApiResult<Boolean> reOrder(HttpServletRequest request, HttpServletResponse response, @RequestBody ClassReOrderDTO reOrderDTO) {
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        reOrderDTO.setDomainId(currentUserInfo.getDomainId());
        boolean flag = classSvc.reOrder(reOrderDTO);
        return ApiResult.ok(this).data(flag);
    }

    /**
     * 初始化所有ci分类的orderNo
     *
     * @author: weixuesong
     * @date: 2020/8/6 17:00
     * @return: boolean
     */
    @ApiOperation(value = "对象分类排序初始化")
    @RequestMapping(value = "/initAllOrderNo", method = RequestMethod.POST)
    @ModDesc(desc = "对象分类排序初始化", pDesc = "暂无参数", rDesc = "操作是否成功", rType = Boolean.class)
    public ApiResult<Boolean> initAllOrderNo(HttpServletRequest request, HttpServletResponse response) {
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        Boolean flag = classSvc.initAllOrderNo(currentUserInfo.getDomainId());
        return ApiResult.ok(this).data(flag);
    }

    @PostMapping("/queryAttrDefGroupList")
    @ApiOperation(value = "获取属性tag列表", notes = "- 返回属性tag, 当前无参 \r\n - 返回值: **[\"34\",\"12\"]**")
    public ApiResult<List<String>> queryAttrDefGroupList() {
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        List<String> groupList = classSvc.queryAttrDefGroupList(currentUserInfo.getDomainId());
        return ApiResult.ok(this).data(groupList);
    }
}
