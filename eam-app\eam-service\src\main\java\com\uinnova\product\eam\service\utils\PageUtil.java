package com.uinnova.product.eam.service.utils;

import lombok.Data;
import java.util.List;
import java.util.ArrayList;
import java.io.Serializable;

@Data
public class PageUtil<T> implements Serializable {

    private static final long serialVersionUID = -8741766802354222579L;

    // 每页显示多少条记录
    private int pageSize;

    // 当前第几页数据
    private int pageNum;

    // 一共多少条记录
    private int totalRows;

    // 一共多少页
    private int totalPages;

    // 要显示的数据
    private List<T> data = new ArrayList<>();

    private PageUtil() {
    }

    public PageUtil(int pageSize, int currentPage, int totalRecord, int totalPage, List<T> dataList) {
        super();
        this.pageSize = pageSize;
        this.pageNum = currentPage;
        this.totalRows = totalRecord;
        this.totalPages = totalPage;
        this.data = dataList;
    }

    public PageUtil(int pageNum, int pageSize, List<T> sourceList) {
        if (sourceList == null || sourceList.isEmpty()){
            return;
        }
        // 总记录条数
        this.totalRows = sourceList.size();

        // 每页显示多少条记录
        this.pageSize = pageSize;

        // 获取总页数
        this.totalPages = this.totalRows / this.pageSize;
        if (this.totalRows % this.pageSize != 0){
            this.totalPages = this.totalPages + 1;
        }
        // 当前第几页数据
        this.pageNum = Math.min(this.totalPages, pageNum);

        // 起始索引
        int fromIndex = this.pageSize * (this.pageNum - 1);

        // 结束索引
        int toIndex = Math.min(this.pageSize * this.pageNum, this.totalRows);
        this.data = sourceList.subList(fromIndex, toIndex);
    }
}
