package com.uino.api.client.cmdb.rpc;

import com.uino.provider.feign.cmdb.TaskLockFeign;
import com.uino.api.client.cmdb.ITaskLockApiSvc;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Classname TaskLockApiSvcRpc
 * @Description TODO
 * @Date 2020/6/28 18:57
 * <AUTHOR> sh
 */
@Service
public class TaskLockApiSvcRpc implements ITaskLockApiSvc {
    @Autowired
    private TaskLockFeign taskLockFeign;

    @Override
    public boolean getLock(String taskName, long taskExecuteTime) {
        return taskLockFeign.getLock(taskName, taskExecuteTime);
    }

    @Override
    public void breakLock(String taskName) {
        taskLockFeign.breakLock(taskName);
    }

    @Override
    public boolean addLock(String taskName) {
        return taskLockFeign.addLock(taskName);
    }

    @Override
    public boolean isLocked(String taskName) {
        return taskLockFeign.isLocked(taskName);
    }
}
