package com.uinnova.product.eam.model.vo;

import com.binary.framework.bean.annotation.Comment;
import com.uino.bean.cmdb.business.ExportCiDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class ExportCiVO extends ExportCiDto {
    @Comment("范围查询 大于等于这个时间创建的数据")
    private String gteTime;
    @Comment("范围查询 小于等于这个时间创建的数据")
    private String lteTime;
    @Comment("0表示导出不合格数据  1表示走的全部数据")
    private Integer usage=1;
    @Comment("ownerCode")
    private String ownerCode;

    @ApiModelProperty(value = "全文检索关键字列表")
    private List<String> words = new ArrayList<>();

    /**
     * 参与检索的字段-与words配合使用
     */
    @ApiModelProperty(value = "参与检索的字段-与word配合使用", example = "[\"ciCode\",\"ciPrimaryKey\",\"属性名称\"]")
    private List<String> searchKeys = new ArrayList<>();

    @Comment("分类导出属性字段，默认导出全部字段")
    private Map<Long, List<Long>> attrsIdMap=new HashMap<>();
}