[{"id": 0, "clientCode": "tarsier-comm", "codeToTokenUrl": ["http://10.100.33.220/tarsier-comm/getTokenByCode"], "secretRequired": true, "clientSecret": "secret", "refreshTokenValiditySeconds": 2592000, "accessTokenValiditySeconds": 43200, "scoped": true, "scopes": ["tarsier-comm", "tarsier"], "autoApprove": true, "attrs": {"clientIndexUrl": "http://10.100.33.220/examples/"}, "resourceIds": ["tarsier-comm"], "athorities": [], "authorizedGrantTypes": ["authorization_code", "refresh_token", "password"], "domainId": 1, "creator": "init", "modifier": "init", "createTime": 20170516222748, "modifyTime": 20170516222748}, {"id": 1, "clientCode": "tarsier-platform", "codeToTokenUrl": ["http://10.100.33.220/tarsier-platform/getTokenByCode"], "secretRequired": true, "clientSecret": "secret", "refreshTokenValiditySeconds": 2592000, "accessTokenValiditySeconds": 43200, "scoped": true, "scopes": ["tarsier-comm", "tarsier"], "autoApprove": true, "attrs": {"clientIndexUrl": "http://10.100.33.220/examples/"}, "resourceIds": ["tarsier-platform", "tarsier-comm"], "athorities": [], "authorizedGrantTypes": ["authorization_code", "refresh_token", "password"], "domainId": 1, "creator": "init", "modifier": "init", "createTime": 20170516222748, "modifyTime": 20170516222748}, {"id": 2, "clientCode": "tarsier-eam", "codeToTokenUrl": ["http://10.100.33.220/tarsier-eam/getTokenByCode"], "secretRequired": true, "clientSecret": "secret", "refreshTokenValiditySeconds": 2592000, "accessTokenValiditySeconds": 43200, "scoped": true, "scopes": ["tarsier-comm", "tarsier"], "autoApprove": true, "attrs": {"clientIndexUrl": "http://10.100.33.220/examples/"}, "resourceIds": ["tarsier-platform", "tarsier-comm", "tarsier-eam", "tarsier-diagram"], "athorities": [], "authorizedGrantTypes": ["authorization_code", "refresh_token", "password"], "domainId": 1, "creator": "init", "modifier": "init", "createTime": 20170516222748, "modifyTime": 20170516222748}, {"id": 3, "clientCode": "tarsier-diagram", "codeToTokenUrl": ["http://10.100.33.220/tarsier-diagram/getTokenByCode"], "secretRequired": true, "clientSecret": "secret", "refreshTokenValiditySeconds": 31536000, "accessTokenValiditySeconds": 43200, "scoped": true, "scopes": ["tarsier-comm", "tarsier"], "autoApprove": true, "attrs": {"clientIndexUrl": "http://10.100.33.220/examples/"}, "resourceIds": ["tarsier-platform", "tarsier-comm", "tarsier-eam", "tarsier-diagram"], "athorities": [], "authorizedGrantTypes": ["authorization_code", "refresh_token", "password"], "domainId": 1, "creator": "init", "modifier": "init", "createTime": 20210126222748, "modifyTime": 20210126222748}]