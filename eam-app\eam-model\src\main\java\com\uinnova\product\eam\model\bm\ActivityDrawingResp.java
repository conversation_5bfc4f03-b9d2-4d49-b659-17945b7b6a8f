package com.uinnova.product.eam.model.bm;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

/**
 * 活动图自动成图返回
 * <AUTHOR>
 * @date 2022/4/20
 */
@Accessors(chain = true)
@Data
public class ActivityDrawingResp {
    @Comment("角色")
    private CcCiInfo role;
    @Comment("任务")
    private List<CcCiInfo> task = new ArrayList<>();
}
