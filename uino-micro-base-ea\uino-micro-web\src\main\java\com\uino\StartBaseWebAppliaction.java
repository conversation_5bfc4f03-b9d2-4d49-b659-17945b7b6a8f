package com.uino;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.quartz.QuartzAutoConfiguration;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 扫包时禁用默认行为，只认指定扫描目录
 * 
 * <AUTHOR>
 */
@SpringBootApplication(exclude = { DataSourceAutoConfiguration.class, QuartzAutoConfiguration.class})
@ComponentScan(basePackages = {
		"com.uino.init",
		"com.uino.api.init",
		"com.uino.oauth2",
		"com.uino.plugin.client",
		// "com.uino.api.client.permission.local",
		"com.uino.oauth.common"})
@ServletComponentScan
@Configuration
@EnableScheduling
@Slf4j
public class StartBaseWebAppliaction {

	static {
		log.info("开始加载base-web模块");
	}

	public static void main(String[] args) {
		SpringApplication.run(StartBaseWebAppliaction.class, args);
		log.info("eam base Application run success :)");
	}
}
