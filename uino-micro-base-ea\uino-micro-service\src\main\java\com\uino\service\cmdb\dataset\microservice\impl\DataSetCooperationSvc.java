package com.uino.service.cmdb.dataset.microservice.impl;

import com.alibaba.fastjson.JSONObject;
import com.binary.core.exception.MessageException;
import com.binary.framework.exception.ServiceException;
import com.uino.dao.cmdb.dataset.ESDataSetCoordinationSvc;
import com.uino.dao.cmdb.dataset.ESDataSetSvc;
import com.uino.service.cmdb.dataset.microservice.IDataSetCooperationSvc;
import com.uino.service.cmdb.dataset.microservice.IMallApiSvc;
import com.uino.service.permission.microservice.IUserSvc;
import com.uino.dao.util.ESUtil;
import com.uino.util.sys.SpringUtil;
import com.uino.util.sys.SysUtil;
import com.uino.bean.cmdb.base.dataset.DataSetCoordination;
import com.uino.bean.cmdb.base.dataset.DataSetMallApiType;
import com.uino.bean.cmdb.base.dataset.OperateType;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.permission.query.CSysUser;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * @Classname DataSetCooperationServiceImpl
 * @Description TODO
 * @Date 2020/3/23 14:52
 * @Created by sh
 */
@Service
public class DataSetCooperationSvc implements IDataSetCooperationSvc {

    @Autowired
    private IUserSvc userSvc;

    @Autowired
    private ESDataSetSvc dataSetSvc;

    @Autowired
    private ESDataSetCoordinationSvc dataSetCoordinationSvc;

    @Override
    public List<JSONObject> findByDataSetId(Long dataSetId) {
        SysUser user = SysUtil.getCurrentUserInfo();
        try {
            JSONObject dataSet = dataSetSvc.getById(dataSetId);
            if (dataSet == null || dataSet.isEmpty()) {
                //数据集不存在
                throw MessageException.i18n("DCV_BS_OBJ_DATASET_NOT_EXIST");
            }
            BoolQueryBuilder coordinationQuery = QueryBuilders.boolQuery();
            coordinationQuery.must(QueryBuilders.termQuery("dataSetMallApiId", dataSetId));
            List<DataSetCoordination> coordinationList = dataSetCoordinationSvc.getListByQuery(coordinationQuery);
            List<String> coordinationUserCodeList = new ArrayList<>();
            if (coordinationList != null) {
                coordinationList.forEach(dataSetCoordination -> coordinationUserCodeList.add(dataSetCoordination.getCoordinationUserCode()));
            }
            if (!user.getLoginCode().equals(dataSet.getString("creater")) && !coordinationUserCodeList.contains(user.getLoginCode())) {
                throw MessageException.i18n("DCV_BS_OBJ_DATASET_NO_PERMISSION");
            }
            CSysUser cdt = new CSysUser();
            cdt.setDomainId(user.getDomainId());
            List<SysUser> sysUserByCdt = userSvc.getSysUserByCdt(cdt);
            List<JSONObject> result = new ArrayList<>();
            sysUserByCdt.forEach(sysUser -> {
                String loginCode = sysUser.getLoginCode();
                if (!loginCode.equals(dataSet.getString("creater"))) {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("id", sysUser.getId());
                    jsonObject.put("userName", sysUser.getUserName());
                    jsonObject.put("userCode", sysUser.getLoginCode());
                    if (coordinationUserCodeList.contains(loginCode)) {
                        jsonObject.put("isCoordination", true);
                    } else {
                        jsonObject.put("isCoordination", false);
                    }
                    result.add(jsonObject);
                }
            });

            return result;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    private IMallApiSvc getMallApi(Integer type) {
        if (DataSetMallApiType.RelationRule.getCode().equals(type)) {
            return SpringUtil.getBean(MallApiRelationRuleSvc.class);
        } else if (DataSetMallApiType.CiClass.getCode().equals(type)) {
            return SpringUtil.getBean(MallApiClassSvc.class);
        } else if (DataSetMallApiType.RelClass.getCode().equals(type)) {
            return SpringUtil.getBean(MallApiRelSvc.class);
        } else {
            throw MessageException.i18n("规则类型不存在");
        }
    }

    @Override
    public boolean updateCooperation(Long dataSetId, List<JSONObject> userList) {
        SysUser user = SysUtil.getCurrentUserInfo();
        JSONObject dataSet = dataSetSvc.getById(dataSetId);
        if (dataSet == null) {
            //数据集不存在
            throw MessageException.i18n("DCV_BS_OBJ_DATASET_NOT_EXIST");
        }
        int type = dataSet.getIntValue("type");
        IMallApiSvc mallApi = getMallApi(type);
        mallApi.checkCharacteristic(user, dataSet);

        BoolQueryBuilder coordinationQuery = QueryBuilders.boolQuery();
        coordinationQuery.must(QueryBuilders.termQuery("dataSetMallApiId", dataSetId));
        List<DataSetCoordination> coordinationList = dataSetCoordinationSvc.getListByQuery(coordinationQuery);

        Map<String, DataSetCoordination> coordinationMap = new HashMap<>();
        coordinationList.forEach(dataSetCoordination -> coordinationMap.put(dataSetCoordination.getCoordinationUserCode(), dataSetCoordination));
        //校验用户是否有权限修改协作
        if (!user.getLoginCode().equals(dataSet.getString("creater")) && !coordinationMap.containsKey(user.getLoginCode())) {
            throw MessageException.i18n("没有协作权限");
        }
        List<DataSetCoordination> insertList = new ArrayList<>();
        List<Long> removeIds = new ArrayList<>();

        Map<String, JSONObject> userMap = new HashMap<>();
        userList.forEach(jsonObject -> userMap.put(jsonObject.getString("userCode"), jsonObject));
        Set<String> userCodeSet = userMap.keySet();
        coordinationMap.forEach((userCode, coordination) -> {
            if (userCodeSet.contains(userCode)) {
                //包含，不做处理,
                userCodeSet.remove(userCode);
            } else {
                //不包含删除
                removeIds.add(coordination.getId());
            }
        });
        for (String userCode : userCodeSet) {
            DataSetCoordination dataSetCoordination = new DataSetCoordination();
            dataSetCoordination.setDomainId(user.getDomainId());
            dataSetCoordination.setId(ESUtil.getUUID());
            dataSetCoordination.setCoordinationUserCode(userCode);
            dataSetCoordination.setDataSetMallApiId(dataSetId);
            dataSetCoordination.setDataSetType(DataSetMallApiType.valueOf(dataSet.getInteger("type")));
            dataSetCoordination.setPermissionLevel(OperateType.Write.getCode());
            dataSetCoordination.setCreater(user.getLoginCode());
            dataSetCoordination.setModifier(user.getLoginCode());
            dataSetCoordination.setCreateTime(System.currentTimeMillis());
            dataSetCoordination.setModifyTime(System.currentTimeMillis());
            insertList.add(dataSetCoordination);
        }
        Integer inster = 0;
        if (!insertList.isEmpty()) {
            inster = dataSetCoordinationSvc.saveOrUpdateBatch(insertList);
        }
        Integer remove = 0;
        if (!removeIds.isEmpty()) {
            remove = dataSetCoordinationSvc.deleteByIds(removeIds);
        }
        return inster == 0 && remove == 0;
    }
}
