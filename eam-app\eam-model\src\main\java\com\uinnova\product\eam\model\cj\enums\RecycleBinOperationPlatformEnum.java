package com.uinnova.product.eam.model.cj.enums;

import com.uinnova.product.eam.comm.exception.BusinessException;
import com.uinnova.product.eam.model.constants.Constants;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 回收站操作平台枚举类
 *
 * <AUTHOR>
 * @since 2022-3-8 15:26:18
 */
@Getter
@AllArgsConstructor
public enum RecycleBinOperationPlatformEnum {

    /**
     * 不存在的操作
     */
    NOT_FOUND(-99999),

    /**
     * 从工作台操作
     */
    WORKBENCH(0),

    /**
     * 从回收站操作
     */
    RECYCLE_BIN(1);

    /**
     * 操作代码
     */
    private Integer type;

    public static RecycleBinOperationPlatformEnum getByCode(int type) {
        for (RecycleBinOperationPlatformEnum value : RecycleBinOperationPlatformEnum.values()) {
            if (value.getType() == type) {
                return value;
            }
        }

        return RecycleBinOperationPlatformEnum.NOT_FOUND;
    }

    public static RecycleBinOperationPlatformEnum checkOperationPlatform(Integer type) {
        RecycleBinOperationPlatformEnum optPlatform = getByCode(type);
        if (optPlatform == RecycleBinOperationPlatformEnum.NOT_FOUND) {
            throw new BusinessException(Constants.RECYCLE_BIN_OPT_UNSUPPORTED);
        }

        return optPlatform;
    }
}
