package com.uinnova.product.eam.workable;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

@Slf4j
@EnableDiscoveryClient
@SpringBootApplication
public class EamWorkableApplication {

    public static void main(String[] args) {
        SpringApplication.run(EamWorkableApplication.class, args);
        log.info("EAM-flowable application start success :)");
    }
}
