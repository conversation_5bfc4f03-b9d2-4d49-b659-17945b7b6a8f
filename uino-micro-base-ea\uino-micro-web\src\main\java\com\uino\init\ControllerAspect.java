package com.uino.init;

import com.alibaba.fastjson.JSON;
import com.binary.framework.web.ErrorCode;
import com.binary.framework.web.RemoteResult;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uino.api.client.sys.IOperateLogApiSvc;
import com.uino.api.client.sys.IOperateLogModuleApiSvc;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.sys.base.ESOperateLog;
import com.uino.bean.sys.base.ESOperateLogModule;
import com.uino.dao.BaseConst;
import com.uino.init.api.ApiResult;
import com.uino.init.http.response.CustomHttpServletResponseWrapper;
import com.uino.plugin.classloader.IUinoPluginAspectJListener;
import com.uino.plugin.classloader.annotation.UinoPluginAspectJ;
import com.uino.util.exception.LoginException;
import com.uino.util.sys.SysUtil;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.support.DefaultListableBeanFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.core.io.InputStreamSource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.lang.reflect.Method;
import java.util.*;

@Aspect
@Component
@Slf4j
public class ControllerAspect {

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    IOperateLogApiSvc logSvc;

    @Autowired
    IOperateLogModuleApiSvc logModuleSvc;

    /**
     * 打印请求参数最大大小，超过则打印为debug日志
     */
	private int printReqParamMaxSize = 1024 * 100;

    /**
     * 返回数据过大告警阈值
     */
    private int warnSize = 1024 * 1024 * 3;

    /**
     * 日志记录返回值最大长度
     */
    private int returnSize = 2000;

    /**
     * 与前端约定的请求唯一标识字段
     */
    private String requestIdHeaderName = "requestId";

    {
        log.info("web请求日志切面注册成功");
    }

	@Pointcut("(execution(* com.uino..*.mvc..*(..)) || execution(* com.uinnova.product..*.mvc..*(..)) || execution(* com.thingjs.framework..*.mvc..*(..)))"
			+ "&&!execution(* com.uino.plugins..*(..)) && !execution(* com.uino.plugin..*(..))")
    public void requestServer() {
    }

    @Around("requestServer()")
    public Object doAround(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
        Date startTime = new Date();
        // 获取代理地址、请求地址、请求类名、方法名
        ServletRequestAttributes sra = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = sra.getRequest();
        String ip = SysUtil.getIpAddress(request);
        String uri = request.getRequestURI();
        String clsName = proceedingJoinPoint.getTarget().getClass().getSimpleName();
        String fullClsName = proceedingJoinPoint.getTarget().getClass().getName();
        String methodName = proceedingJoinPoint.getSignature().getName();
        String requestId = request.getHeader(requestIdHeaderName);
        log.info("接收到来自【{}】请求，uri为【{}】,请求标识为【{}】", ip, uri, requestId);
        log.debug("路由到类【{}】方法【{}】", clsName, methodName);

        List<IUinoPluginAspectJListener> uinoPluginAspectJListeners = new ArrayList<>();
        DefaultListableBeanFactory defaultListableBeanFactory = (DefaultListableBeanFactory)applicationContext.getAutowireCapableBeanFactory();
        Map<String, Object> beansWithAnnotation = defaultListableBeanFactory.getBeansWithAnnotation(UinoPluginAspectJ.class);
        beansWithAnnotation.forEach((k, v) -> {
            IUinoPluginAspectJListener listener =  (IUinoPluginAspectJListener) v;
            UinoPluginAspectJ annotation = listener.getClass().getAnnotation(UinoPluginAspectJ.class);
            String[] value = annotation.value();
            if (Arrays.asList(value).contains(fullClsName + "." + methodName)) {
                uinoPluginAspectJListeners.add(listener);
            }

        });
        CustomHttpServletResponseWrapper response = null;
        Object paramVal = null;
        Map<String, Object> paramMap = new HashMap<>();
        try {
            // 获取方法参数
            MethodSignature methodSignature = (MethodSignature) proceedingJoinPoint.getSignature();
            String[] paramNames = methodSignature.getParameterNames();
            Object[] paramVals = proceedingJoinPoint.getArgs();
            if (paramNames != null && paramNames.length > 0) {
                for (int index = 0; index < paramNames.length; index++) {
                    String paramName = paramNames[index];
                    paramVal = paramVals[index];
                    if (paramVal instanceof HttpServletRequest) {

                    } else if (paramVal instanceof HttpServletResponse) {
                        try {
                            response = (CustomHttpServletResponseWrapper) paramVal;
                        } catch (Exception e) {
                            log.error("获取请求自定义response失败，可能由于未注册自定义CustomHttpServletResponseWrapper或正确配置请求拦截");
                        }
                    } else if (paramVal instanceof InputStreamSource || paramVal instanceof InputStreamSource[]) {

                    } else {
                        // 代表日志需要打印，根据配置康康需要如何打印
                        String paramStr = JSON.toJSONString(paramVal);
                        paramMap.put(paramName, paramVal);
                        if (printReqParamMaxSize < 0L || paramStr.getBytes().length <= printReqParamMaxSize) {
                            log.info("参数【{}】为【{}】", paramName, paramStr);
                        } else {
                            log.info("参数【{}】值空间占用过大，打印到了打印到了更低级别debug日志中", paramName);
                            log.debug("参数【{}】为【{}】", paramName, JSON.toJSONString(paramVal));
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取参数异常", e);
        }
        try {
            if (!CollectionUtils.isEmpty(uinoPluginAspectJListeners)) {
                log.debug("执行插件切面before, param={}", JSON.toJSONString(paramMap));
                for (IUinoPluginAspectJListener uinoPluginAspectJListener : uinoPluginAspectJListeners) {
                    uinoPluginAspectJListener.doBefore(paramMap);
                }
            }
        } catch (Exception e) {
            log.error("执行插件切面异常", e);
        }

        String data = "";
        // 记录接口访问日志
        SysUser currentUser = null;
        try {
            currentUser = SysUtil.getCurrentUserInfo();
        } catch (LoginException e) {
            log.debug("无法获取当前登陆用户，该持久化信息可能会缺失[创建/修改]人信息和domain域信息不对的问题");
        }
        ESOperateLog opLog = null;
        try {
            // 获取类注解信息
            Class<? extends Object> targetClass = proceedingJoinPoint.getTarget().getClass();
            // 获取方法注解信息
            Method method = targetClass.getMethod(methodName,
                    ((MethodSignature) proceedingJoinPoint.getSignature()).getParameterTypes());
            ModDesc methodAnnotation = method.getAnnotation(ModDesc.class);
            ApiOperation apiOperation = method.getAnnotation(ApiOperation.class);
            ESOperateLogModule moduleInfo = logModuleSvc.getModuleInfoByMvc(targetClass.getName());
            opLog = ESOperateLog.builder().moduleName(SysUtil.StringUtil.isNotBack(moduleInfo.getModuleCode()) ? moduleInfo.getModuleCode() : "")
                    .mvcFullName(targetClass.getName()).mvcModName(methodName)
                    .opDesc(methodAnnotation != null ? methodAnnotation.desc()
                            : (apiOperation != null ? apiOperation.value() : ""))
                    .opPath(uri)
                    .opStatus(response == null ? 200 : response.getStatus())
                    .userId(currentUser == null ? 1L : currentUser.getId()).ipAddress(ip)
                    .domainId((currentUser == null || currentUser.getDomainId() == null) ? BaseConst.DEFAULT_DOMAIN_ID : currentUser.getDomainId())
                    .userName(currentUser == null ? "system" : currentUser.getUserName())
                    .userCode(currentUser == null ? "system" : currentUser.getLoginCode()).build();
        } catch (Exception e) {
            log.warn("构建接口日志异常", e);
        }

        Object result = null;
        try {
            result = proceedingJoinPoint.proceed();
        } catch (Exception e) {
            int errorCode = ErrorCode.SERVER_ERROR.getCode();
            // 未登录异常特殊处理
            if (e instanceof LoginException) {
                errorCode = ErrorCode.NOT_LOGIN.getCode();
            }
            if (opLog != null) {
                data = JSON.toJSONString(new RemoteResult(false, errorCode, e.getMessage()));
                opLog.setOpResult(data.substring(0, Math.min(data.length(), returnSize)));
                logSvc.saveOrUpdate(opLog);
            }

            throw e;
        }
        try {
            Date endTime = new Date();
            log.debug("本次请求【{}】共处理【{}】毫秒", requestId, (endTime.getTime() - startTime.getTime()));
            if (response != null) {
                String responseContentType = response.getContentType();
                if (responseContentType != null) {
                    String responseContentTypeLowerCase = responseContentType.toLowerCase();
                    if (!responseContentTypeLowerCase.contains("stream") && !responseContentTypeLowerCase.contains("image/x-png")) {
                        data = new String(response.getBody());
                        log.debug("返回消息为【{}】", data);
                        if (response.getBody().length >= this.warnSize) {
                            log.warn("类【{}】方法【{}】在本次处理返回数据大小大于阈值【{}】b,请考虑优化返回数据", clsName, methodName, this.warnSize);
                        }
                    }
                }
            }
            if (result != null) {
                boolean writeLog = false;
                if (result instanceof ResponseEntity) {
                    HttpHeaders responseHeaders = ((ResponseEntity<?>) result).getHeaders();
                    MediaType responseContentType = responseHeaders.getContentType();
                    if (responseContentType == MediaType.APPLICATION_OCTET_STREAM) {
                        data = "File";
                    }
                } else if (result instanceof OutputStream) {
                    data = "File";
                } else if (result instanceof ApiResult) {
                    data = JSON.toJSONString(result);
                    writeLog = true;
                }

                if (writeLog) {
                    log.debug("返回消息为【{}】", JSON.toJSONString(result));
                }
            }
        } catch (Exception ignored) {
        }

        try {
            if (!CollectionUtils.isEmpty(uinoPluginAspectJListeners)) {
                log.debug("执行插件切面after，result={}", data);
                for (IUinoPluginAspectJListener uinoPluginAspectJListener : uinoPluginAspectJListeners) {
                    uinoPluginAspectJListener.doAfter(paramMap, data);
                }
            }
        } catch (Exception e) {
            log.error("执行插件切面异常", e);
        }

        if (opLog != null) {
            opLog.setOpResult(data.substring(0, Math.min(data.length(), returnSize)));
            logSvc.saveOrUpdate(opLog);
        }
        return result;
    }

}
