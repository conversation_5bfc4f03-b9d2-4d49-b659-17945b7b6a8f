package com.uinnova.product.eam.service;

import com.uinnova.product.eam.comm.model.es.AssetWarehouseDir;
import com.uinnova.product.eam.model.AssetWarehouseDirVo;

import java.util.List;

public interface AssetWarehouseDirSvc {
    Integer saveOrUpdate(List<AssetWarehouseDirVo> assetWarehouseDir);

    Integer delete(Long id);

    List<AssetWarehouseDirVo> getTree(Long userId);

    List<AssetWarehouseDirVo> getConfigTree();

    List<AssetWarehouseDir> getListByRoleIds(List<Long> roleIdList);

    void migrationAssetModule();

}
