package com.uinnova.product.eam.model.vo;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 关联分析表格实体
 * <AUTHOR>
 */
@Data
public class EamAnalyseTableVo implements Serializable {

    @Comment("表名")
    private String title;

    @Comment("总列数")
    private Integer maxColNum;

    @Comment("数据集")
    private List<Map<String, String>> data = new ArrayList<>();

    @Comment("合并行数map<行value, 合并行数>")
    private Map<Integer, List<EamAnalyseMergeVo>> mergeMap = new HashMap<>();

    public EamAnalyseTableVo() {
    }
}
