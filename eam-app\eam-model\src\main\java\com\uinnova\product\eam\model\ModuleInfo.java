package com.uinnova.product.eam.model;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class ModuleInfo {

    @Comment("模型树id")
    private Long id;

    @Comment("模型树名称")
    private String name;

    @Comment("所属分类")
    private Integer type;

    @Comment("模型说明")
    private String details;

    @Comment("模型树层级+名称")
    private List<Map<String,String>> levelNames;
}
