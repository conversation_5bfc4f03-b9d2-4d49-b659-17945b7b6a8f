package com.uinnova.product.vmdb.provider.rlt.bean;

import com.binary.framework.bean.annotation.Comment;

import java.io.Serializable;
import java.util.List;

@Comment("线条的查询条件")
public class CiLinkedLineCdt implements Serializable {

	private static final long serialVersionUID = 1L;

	@Comment("线条的id在linked中应该是全局唯一的")
	private Long lineId;

	@Comment("linked中节点的ID")
	private Long prveNodeId;

	@Comment("linked中节点的ID")
	private Long nextNodeId;

	@Comment("箭头方向0 A<-B 1A->B")
	private Integer direction;

	@Comment("分类属性的查询关系多个是ORD关系")
	private List<CiLinkedLineRltCdt> rltCdts;

	public Long getLineId() {
		return lineId;
	}

	public void setLineId(Long lineId) {
		this.lineId = lineId;
	}

	public Long getPrveNodeId() {
		return prveNodeId;
	}

	public void setPrveNodeId(Long prveNodeId) {
		this.prveNodeId = prveNodeId;
	}

	public Long getNextNodeId() {
		return nextNodeId;
	}

	public void setNextNodeId(Long nextNodeId) {
		this.nextNodeId = nextNodeId;
	}

	public List<CiLinkedLineRltCdt> getRltCdts() {
		return rltCdts;
	}

	public void setRltCdts(List<CiLinkedLineRltCdt> rltCdts) {
		this.rltCdts = rltCdts;
	}

	public Integer getDirection() {
		return direction;
	}

	public void setDirection(Integer direction) {
		this.direction = direction;
	}

}
