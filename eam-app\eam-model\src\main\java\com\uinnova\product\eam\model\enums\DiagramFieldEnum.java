package com.uinnova.product.eam.model.enums;

import com.uinnova.product.eam.model.vo.DiagramFieldVo;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

@Getter
public enum DiagramFieldEnum {

    PROCESS_MODEL("10", "业务架构-流程建模"),
    COMPONENT_MODEL("100", "业务架构-组件建模"),
    BUSINESS_OTHER("1", "业务架构-产品建模"),
    APP_BUILD("11", "应用架构"),
    DATA_BUILD("101", "数据架构"),
    TECHNOLOGY_BUILD("111", "技术架构"),
    BUILD_PLAN("110", "产品方案");

    DiagramFieldEnum(String sign, String desc) {
        this.sign = sign;
        this.desc = desc;
    }

    private String sign;

    private String desc;

    private static List<DiagramFieldVo> diagramFieldList;

    static {
        diagramFieldList = new ArrayList<>();

        for(DiagramFieldEnum item : DiagramFieldEnum.values()) {
            DiagramFieldVo diagramFieldVo = new DiagramFieldVo();
            diagramFieldVo.setSign(item.getSign());
            diagramFieldVo.setDesc(item.getDesc());
            diagramFieldList.add(diagramFieldVo);
        }

    }

    public static List<DiagramFieldVo> getInstance() {
        return diagramFieldList;
    }

}
