package com.uino.dao.cmdb.dataset;

import com.alibaba.fastjson.JSONObject;
import com.uino.dao.AbstractESBaseDao;
import com.uino.bean.cmdb.base.dataset.style.DataSetPriorityDisplay;
import com.uino.dao.ESConst;
import org.springframework.stereotype.Repository;

import jakarta.annotation.PostConstruct;

/**
 * @Classname PriorityDisplayDao
 * @Description 优先显示
 * @Date 2020/3/19 10:13
 * @Created by sh
 */
@Repository
public class ESDataSetPriorityDisplaySvc extends AbstractESBaseDao<DataSetPriorityDisplay, JSONObject> {

    @Override
    public String getIndex() {
        return ESConst.INDEX_CMDB_DATASET_PRIORITY_DISPLAY;
    }

    @Override
    public String getType() {
        return ESConst.INDEX_CMDB_DATASET_PRIORITY_DISPLAY;
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }
}

