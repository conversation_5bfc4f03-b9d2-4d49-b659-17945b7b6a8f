package com.uinnova.product.eam.service.impl;

import com.binary.core.util.BinaryUtils;
import com.uinnova.product.eam.comm.model.es.DiagramApproveRlt;
import com.uinnova.product.eam.service.DiagramApproveRltSvc;
import com.uinnova.product.eam.service.es.DiagramApproveRltDao;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class DiagramApproveRltSvcImpl implements DiagramApproveRltSvc {

    @Resource
    DiagramApproveRltDao diagramApproveRltDao;

    @Override
    public Long saveDiagramApproveRlt(DiagramApproveRlt diagramApproveRlt) {
        if (BinaryUtils.isEmpty(diagramApproveRlt.getFlowStatus())) {
            // 创建默认为流程中的审批关联数据
            diagramApproveRlt.setFlowStatus(2);
        }
        if (BinaryUtils.isEmpty(diagramApproveRlt.getDEnergysList())) {
            diagramApproveRlt.setReleaseDesc("");
        }
        return diagramApproveRltDao.saveOrUpdate(diagramApproveRlt);
    }

    @Override
    public void deleteApproveRlt(Long id) {
        diagramApproveRltDao.deleteById(id);
    }

    @Override
    public Boolean changeApproveRltFlowStatus(Long id, Integer flowStatus) {
        Map<String, Object> params = new HashMap();
        params.put("flowStatus", flowStatus);
        String script = "ctx._source.flowStatus=params.flowStatus;";
        return diagramApproveRltDao.updateByQuery(QueryBuilders.termQuery("id", id), script, true, params);
    }

    @Override
    public Boolean batchChangeApproveRltFlowStatus(List<Long> ids, Integer flowStatus) {
        List<DiagramApproveRlt> diagramApproveRlts = diagramApproveRltDao.getListByQuery(QueryBuilders.termsQuery("id", ids));
        for (DiagramApproveRlt rlt : diagramApproveRlts) {
            rlt.setFlowStatus(flowStatus);
        }
        diagramApproveRltDao.saveOrUpdateBatch(diagramApproveRlts);
        return Boolean.TRUE;
    }

    @Override
    public List<DiagramApproveRlt> queryApproveRltByInfo(Long modelId, String ownerCode, Long approveRootDirId, String approveDiagramId,
                                                         List<Integer> flowStatus, Boolean isSingle) {
        BoolQueryBuilder bool = QueryBuilders.boolQuery();
        if (!BinaryUtils.isEmpty(modelId)) {
            bool.must(QueryBuilders.termQuery("modelId", modelId));
        }
        if (!BinaryUtils.isEmpty(flowStatus)) {
            bool.must(QueryBuilders.termsQuery("flowStatus", flowStatus));
        }
        if (!BinaryUtils.isEmpty(approveRootDirId)) {
            bool.must(QueryBuilders.termQuery("approveRootDirId", approveRootDirId));
        }
        if (!BinaryUtils.isEmpty(isSingle)) {
            bool.must(QueryBuilders.termQuery("isSingle", isSingle));
        }
        if (!BinaryUtils.isEmpty(approveDiagramId)) {
            bool.must(QueryBuilders.termQuery("approveDiagramId.keyword", approveDiagramId));
        }
        bool.must(QueryBuilders.termQuery("ownerCode.keyword", ownerCode));
        List<DiagramApproveRlt> listByQuery = diagramApproveRltDao.getListByQuery(bool);
        return BinaryUtils.isEmpty(listByQuery) ? new ArrayList<>() : listByQuery;
    }

    @Override
    public DiagramApproveRlt getApproveRlt(Long businesskey) {
        return diagramApproveRltDao.getById(businesskey);
    }

    @Override
    public List<DiagramApproveRlt> getApproveRlts(List<Long> businesskeys) {
        BoolQueryBuilder bool = QueryBuilders.boolQuery();
        bool.must(QueryBuilders.termsQuery("id", businesskeys));
        return diagramApproveRltDao.getListByQuery(bool);
    }
}


