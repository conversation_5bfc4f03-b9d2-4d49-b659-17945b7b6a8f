package com.uinnova.product.vmdb.comm.doc.build;

import com.binary.framework.bean.annotation.Comment;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 
 * <AUTHOR>
 *
 */
public abstract class JavaBeanParser {

    // /**
    // * 解析java文件中的注释
    // * @param javaFile
    // * @return key=fieldName, value=描述
    // */
    // public static Map<String, String> getFieldDesc(String javaFile) {
    // return getFieldDesc(new File(javaFile));
    // }
    //
    //
    //
    //
    // /**
    // * 解析java文件中的注释
    // * @param javaFile
    // * @return key=fieldName, value=描述
    // */
    // public static Map<String, String> getFieldDesc(File javaFile) {
    // String s = FileSystem.read(javaFile, "UTF-8");
    //
    // s = s.substring(s.indexOf('{')+1);
    // s = s.substring(0, s.indexOf("public "));
    // s = s.substring(s.indexOf("/*")).trim();
    //
    // String[] arr = s.split("[;]");
    //
    // Map<String, String> map = new HashMap<String, String>();
    // for(int i=0; i<arr.length; i++) {
    // String desc = arr[i].substring(arr[i].indexOf("/*")+2, arr[i].indexOf("*/"));
    // desc = desc.replace('*', ' ').replace('\n', ' ').trim();
    // String name = arr[i].trim();
    // name = name.substring(name.lastIndexOf(' ')+1);
    //
    // map.put(name, desc);
    // }
    //
    // return map;
    // }
    //

    private static void getSuperClass(Class<?> beanClass, List<Class<?>> ls) {
        Class<?> c = beanClass.getSuperclass();
        if (c == null || c == Object.class) {
            return;
        }
        ls.add(c);
        getSuperClass(c, ls);
    }

    public static Map<String, String> getFieldDesc(Class<?> beanClass) {
        List<Class<?>> ls = new ArrayList<Class<?>>();
        ls.add(beanClass);
        getSuperClass(beanClass, ls);

        Map<String, String> descmap = new HashMap<String, String>();

        for (int i = ls.size() - 1; i >= 0; i--) {
            Class<?> c = ls.get(i);
            Field[] fs = c.getDeclaredFields();

            for (int j = 0; j < fs.length; j++) {
                Comment cmm = fs[j].getAnnotation(Comment.class);
                if (cmm != null) {
                    descmap.put(fs[j].getName(), cmm.value());
                }
            }
        }

        return descmap;
    }

}
