package com.uino.api.client.cmdb.local;

import com.binary.core.io.Resource;
import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uinnova.product.vmdb.provider.rlt.bean.CcCiRltInfo;
import com.uino.api.client.cmdb.ICIRltApiSvc;
import com.uino.bean.cmdb.base.ESCIRltInfo;
import com.uino.bean.cmdb.base.ESCIRltInfoHistory;
import com.uino.bean.cmdb.business.BindCiRltRequestDto;
import com.uino.bean.cmdb.business.DataModuleRltClassDto;
import com.uino.bean.cmdb.business.ImportExcelMessage;
import com.uino.bean.cmdb.business.ImportResultMessage;
import com.uino.bean.cmdb.query.ESAttrAggBean;
import com.uino.bean.cmdb.query.ESRltSearchBean;
import com.uino.dao.BaseConst;
import com.uino.service.cmdb.microservice.impl.CIRltSvc;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Service
@Primary
public class CIRltApiSvcLocal implements ICIRltApiSvc {

    @Autowired
    private CIRltSvc ciRltSvc;

    @Override
    public Long bindCiRlt(BindCiRltRequestDto bindCiRltRequestDto) {
        return ciRltSvc.bindCiRlt(bindCiRltRequestDto);
    }

    @Override
    public ImportResultMessage bindCiRlts(Set<BindCiRltRequestDto> bindRltDtos, boolean repeat) {
        return ciRltSvc.bindCiRlts(BaseConst.DEFAULT_DOMAIN_ID, bindRltDtos, repeat);
    }

    @Override
    public ImportResultMessage bindCiRlts(Long domainId, Set<BindCiRltRequestDto> bindRltDtos, boolean repeat) {
        return ciRltSvc.bindCiRlts(domainId, bindRltDtos, repeat);
    }

    @Override
    public Integer delRltByCiId(Long ciId) {
        return ciRltSvc.delRltByCiId(ciId);
    }

    @Override
    public Long updateCiRltAttr(Long ciRltId, Map<String, String> attrs) {
        return ciRltSvc.updateCiRltAttr(ciRltId, attrs);
    }

    @Override
    public Page<CcCiRltInfo> searchRltByBean(ESRltSearchBean bean) {
        return ciRltSvc.searchRltByBean(bean);
    }

    @Override
    public Integer clearRltByClassId(Long rltClassId) {
        return ciRltSvc.clearRltByClassId(rltClassId);
    }

    @Override
    public ImportResultMessage importCiRlt(String excelFilePath, MultipartFile excelFile, Set<String> rltClsCodes) {
        return ciRltSvc.importCiRlt(BaseConst.DEFAULT_DOMAIN_ID, excelFilePath, excelFile, rltClsCodes);
    }

    @Override
    public ImportResultMessage importCiRlt(Long domainId, String excelFilePath, MultipartFile excelFile, Set<String> rltClsCodes) {
        return ciRltSvc.importCiRlt(domainId, excelFilePath, excelFile, rltClsCodes);
    }

    @Override
    public Integer delRltByIdsOrRltCodes(Set<Long> rltIds, Set<String> rltCodes) {
        return ciRltSvc.delRltByIdsOrRltCodes(rltIds, rltCodes, null);
    }

    @Override
    public Map<String, Boolean> comprehendRltExcel(MultipartFile excelFile) {
        return ciRltSvc.comprehendRltExcel(excelFile);
    }

    @Override
    public ImportExcelMessage parseRltExcel(String excelFilePath, MultipartFile excelFile) {
        return ciRltSvc.parseRltExcel(excelFilePath, excelFile);
    }

    @Override
    public ImportResultMessage bindCiRlts(Set<BindCiRltRequestDto> bindRltDtos) {
        return ciRltSvc.bindCiRlts(BaseConst.DEFAULT_DOMAIN_ID, bindRltDtos);
    }

    @Override
    public ImportResultMessage bindCiRlts(Long domainId, Set<BindCiRltRequestDto> bindRltDtos) {
        return ciRltSvc.bindCiRlts(domainId, bindRltDtos);
    }

    @Override
    public List<CcCiRltInfo> searchRltByIds(Set<Long> ids) {
        return ciRltSvc.searchRltByIds(ids);
    }

    @Override
    public Page<ESCIRltInfo> searchRlt(ESRltSearchBean bean) {
        return ciRltSvc.searchRlt(bean);
    }

    @Override
    public Page<String> groupByField(ESAttrAggBean req) {
        return ciRltSvc.groupByField(BaseConst.DEFAULT_DOMAIN_ID, req);
    }

    @Override
    public Page<String> groupByField(Long domainId, ESAttrAggBean req) {
        return ciRltSvc.groupByField(domainId, req);
    }

    @Override
    public Map<Long, Map<Long, Set<Long>>> getClassRltMapByClsQuery(Set<Long> clsIds, Set<Long> rltClsIds) {
        return ciRltSvc.getClassRltMapByClsQuery(clsIds, rltClsIds);
    }

    @Override
    public Resource exportCiRlt(ESRltSearchBean bean) {
        return ciRltSvc.exportCiRlt(bean);
    }

    @Override
    public List<DataModuleRltClassDto> getClassRltList(Set<Long> classIds, Set<Long> rltClsIds) {
        return ciRltSvc.getClassRltList(classIds, rltClsIds);
    }

    @Override
    public Map<Long, List<ESCIRltInfoHistory>> getRltsHistrysDict(Set<Long> rltIds, boolean hasCurrent) {
        return ciRltSvc.getRltsHistrysDict(rltIds, hasCurrent);
    }

    @Override
    public Map<Long, Long> getRltIdMaxVersion(Set<Long> rltIds) {
        return ciRltSvc.getRltIdMaxVersion(rltIds);
    }

    @Override
    public List<CcCiClassInfo> queryAllClasses(Long domainId) {
        //运行库
        return ciRltSvc.queryAllClasses(domainId);
    }

}
