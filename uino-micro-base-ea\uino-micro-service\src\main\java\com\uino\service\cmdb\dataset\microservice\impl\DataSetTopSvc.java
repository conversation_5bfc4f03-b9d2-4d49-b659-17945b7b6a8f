package com.uino.service.cmdb.dataset.microservice.impl;

import java.util.List;

import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import com.uino.bean.cmdb.base.dataset.style.DataSetTop;
import com.uino.bean.permission.base.SysUser;
import com.uino.dao.cmdb.dataset.ESDataSetTopSvc;
import com.uino.dao.util.ESUtil;
import com.uino.service.cmdb.dataset.microservice.IDataSetTopSvc;
import com.uino.util.sys.SysUtil;

/**
 * @Classname DataSetTopService
 * @Description TODO
 * @Date 2020/3/27 15:32
 * @Created by sh
 */
@Service
public class DataSetTopSvc implements IDataSetTopSvc {

    @Autowired
    private ESDataSetTopSvc esDataSetTopSvc;

    @Override
    public void collectDataSet(Long dataSetId) {
        SysUser user = SysUtil.getCurrentUserInfo();
        BoolQueryBuilder dataSetTopQuery = QueryBuilders.boolQuery();
        dataSetTopQuery.must(QueryBuilders.termQuery("userCode.keyword", user.getLoginCode()));
        dataSetTopQuery.must(QueryBuilders.termQuery("dataSetMallApiId", dataSetId));
        List<DataSetTop> listByQuery = esDataSetTopSvc.getListByQuery(dataSetTopQuery);
        if (!listByQuery.isEmpty()) {
            BoolQueryBuilder query = QueryBuilders.boolQuery();
            query.must(QueryBuilders.termQuery("userCode.keyword", user.getLoginCode()));
            query.must(QueryBuilders.termQuery("dataSetMallApiId", dataSetId));
            esDataSetTopSvc.deleteByQuery(query, true);
        } else {
            DataSetTop dataSetTop = new DataSetTop();
            dataSetTop.setId(ESUtil.getUUID());
            dataSetTop.setDomainId(user.getDomainId());
            dataSetTop.setUserCode(user.getLoginCode());
            dataSetTop.setDataSetMallApiId(dataSetId);
            dataSetTop.setCreateTime(System.currentTimeMillis());
            dataSetTop.setModifyTime(System.currentTimeMillis());
            esDataSetTopSvc.saveOrUpdate(dataSetTop);
        }

    }

	@Override
	public void deleteDataSetTop(Long dataSetId) {
		Assert.notNull(dataSetId, "X_PARAM_NOT_NULL${name:dataSetId}");
		SysUser user = SysUtil.getCurrentUserInfo();
		BoolQueryBuilder query = QueryBuilders.boolQuery();
		query.must(QueryBuilders.termQuery("userCode.keyword", user.getLoginCode()));
		query.must(QueryBuilders.termQuery("domainId", user.getDomainId()));
		query.must(QueryBuilders.termQuery("dataSetMallApiId", dataSetId));
		esDataSetTopSvc.deleteByQuery(query, true);
	}
}
