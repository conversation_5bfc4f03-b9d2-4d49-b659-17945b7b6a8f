package com.uino.bean.dataset.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2021/6/8 13:39
 */
@Getter
@Setter
@ApiModel(value = "孪生服务信息", description = "孪生服务信息")
public class DigitalTwinServicePortalInfo {

	/**
	 * Id
	 */
	@ApiModelProperty(value = "id")
	private Long id;

	/**
	 * 自定义数据集ID
	 */
	@ApiModelProperty(value = "自定义数据集ID")
	private Long datasetId;
	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	private Long createTime;
	/**
	 * 名称
	 */
	@ApiModelProperty(value = "名称")
	private String name;
	/**
	 * 类型
	 */
	@ApiModelProperty(value = "类型")
	private Integer type;
	/**
	 * 真实类型
	 */
	@ApiModelProperty(value = "真实类型")
	private Integer realType;
	/**
	 * 调用量
	 */
	@ApiModelProperty(value = "调用量")
	private Long callNum;
	/**
	 * 调用地址
	 */
	@ApiModelProperty(value = "调用地址")
	private String requestURL;
	/**
	 * query请求参数
	 */
	@ApiModelProperty(value = "query请求参数")
	private String requestParam;
	/**
	 * 权限角色
	 */
	private String permission;
	/**
	 * 请求header
	 */
	@ApiModelProperty(value = "请求header")
	private String requestHeader;
	/**
	 * query请求参数
	 */
	@ApiModelProperty(value = "query请求参数")
	private String requestQuery;
	/**
	 * 返回参数
	 */
	@ApiModelProperty(value = "query请求参数")
	private String responseParam;
	/**
	 * 返回参数
	 */
	@ApiModelProperty(value = "response参数样例")
	private String responseParamExample;
	/**
	 * 返回参数
	 */
	@ApiModelProperty(value = "返回码说明")
	private String responseCodeExplain;
	/**
	 * 服务标签
	 */
	@ApiModelProperty(value = "服务标签")
	private String serviceLabel;

	@ApiModelProperty(value = "所属域")
	private Long domainId;

}
