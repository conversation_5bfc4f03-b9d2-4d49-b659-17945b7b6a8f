package com.uino.bean.chart.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "饼图图表数据项")
public class UinoChartDataItem {

	@ApiModelProperty(value = "数据类别")
	private String name;

	@ApiModelProperty(value = "维度数值")
	private Double value;
}
