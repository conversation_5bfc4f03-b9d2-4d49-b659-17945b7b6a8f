package com.uinnova.product.eam.service.asset;

import com.uinnova.product.eam.model.asset.AppSystemInfoResponse;

import java.util.List;
import java.util.Map;

/**
 * 应用系统(全景)详情业务接口
 * <AUTHOR>
 */
public interface AppSystemDetailSvc {
    /**
     * 查询系统详情基本信息
     * @param ciCode 应用系统ciCode
     * @return 应用系统基本信息
     */
    AppSystemInfoResponse queryAppSystemInfo(String ciCode);

    /**
     * 查询应用系统相关视图
     * @param ciCode 应用系统ciCode
     * @return 视图加密id集合
     */
    List<Map<String, String>> getSystemArchitectureDiagram(String ciCode);
}
