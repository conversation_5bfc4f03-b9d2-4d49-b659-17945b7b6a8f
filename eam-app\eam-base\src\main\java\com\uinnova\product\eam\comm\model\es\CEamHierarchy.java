package com.uinnova.product.eam.comm.model.es;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

/**
 * 层级配置查询VO
 * <AUTHOR>
 * @date 2022/1/5
 */
@Data
public class CEamHierarchy {
    @Comment("主键id")
    private Long id;

    @Comment("层级名称")
    private String name;

    @Comment("业务类型 用于业务建模：BUSINESS")
    private String type;

    @Comment("层级Lvl")
    private Integer dirLvl;

    @Comment("父层级")
    private Long parentId;

    @Comment("关联制品id")
    private Long artifactId;

    @Comment("生成下一层级使用分类Code")
    private String lowerClassCode;

    @Comment("使用分类属性作为下级名称")
    private String dirNameAttr;

    @Comment("继承父级节点分类Code")
    private String inheritClassCode;

    @Comment("继承父级节点布局")
    private String inheritLayout;

    @Comment("使用模板Id,null则不使用模板")
    private Long templateId;

    @Comment("前导提示图")
    private String markedImg;

    @Comment("前导提示语")
    private String markedWord;

    @Comment("下级层级所属元素，当前层级元素选择字段")
    private String belongCIProName;

    @Comment("模型树id")
    private Long modelId;

    @Comment("活动图是否校验拖拽开关 是:true,否:false 默认false")
    private Boolean checkActive = false;
}
