package com.uino.util.change_notify;

import java.util.Collection;
import java.util.List;

/**
 * 变更处理流程
 * 
 * <AUTHOR>
 *
 */
public abstract class AbstractChangeProcs<T> implements IChangeProcs<T> {

    /**
     * 处理链条
     */
    private List<IChangeChain<T>> chains;

    /**
     * 获处理链条核心
     * 
     * @return
     */
    protected abstract List<IChangeChain<T>> getChainsCore();

    /**
     * 获取处理链条
     * 
     * @return
     */
    private List<IChangeChain<T>> getChains() {
        if (chains == null || chains.size() <= 0) {
            synchronized (IChangeProcs.class) {
                if (chains == null || chains.size() <= 0) {
                    chains = getChainsCore();
                }
            }
        }
        return chains;
    }

    @Override
    public void saveOrUpdate(Collection<T> changeDtos) {
        if (getChains() != null && getChains().size() > 0 && changeDtos != null && changeDtos.size() > 0) {
            getChains().forEach(chain -> chain.saveOrUpdate(changeDtos));
        }
    }

    @Override
    public void deleteByIds(Collection<Long> delIds) {
        if (getChains() != null && getChains().size() > 0 && delIds != null && delIds.size() > 0) {
            getChains().forEach(chain -> chain.deleteByIds(delIds));
        }
    }
}
