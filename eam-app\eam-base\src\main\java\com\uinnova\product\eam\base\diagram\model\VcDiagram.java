package com.uinnova.product.eam.base.diagram.model;


import cn.hutool.crypto.SecureUtil;
import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;


@Comment("视图设计表[VC_DIAGRAM]")
public class VcDiagram implements EntityBean {
    private static final long serialVersionUID = 1L;

    @Getter
    @Setter
    @Comment("视图关联的系统CiCode")
    private String sysCiCode;

    @Comment("ID[ID]")
    @JsonIgnore
    private Long id;

    @Comment("视图ID加密字段")
    @JsonProperty("id")
    private String dEnergy;

    @Comment("视图名称[NAME]")
    private String name;


    @Comment("所属用户[USER_ID]")
    private Long userId;


    @Comment("所属目录[DIR_ID]")
    private Long dirId;


    @Comment("目录类型[DIR_TYPE]   ")
    private Integer dirType;


    @Comment("视图类型[DIAGRAM_TYPE]    视图类型:1=单图 2=组合视图 3=视图模版")
    private Integer diagramType;


    @Comment("视图描述[DIAGRAM_DESC]")
    private String diagramDesc;


    @Comment("视图SVG[DIAGRAM_SVG]")
    private String diagramSvg;


    @Comment("视图XML[DIAGRAM_XML]")
    private String diagramXml;


    @Comment("视图JSON[DIAGRAM_JSON]    视图json格式信息")
    private String diagramJson;


    @Comment("背景图[DIAGRAM_BG_IMG]")
    private String diagramBgImg;


    @Comment("背景样式[DIAGRAM_BG_CSS]")
    private String diagramBgCss;


    @Comment("视图图标_1[ICON_1]")
    private String icon1;


    @Comment("视图图标_2[ICON_2]")
    private String icon2;


    @Comment("视图图标_3[ICON_3]")
    private String icon3;


    @Comment("视图图标_4[ICON_4]")
    private String icon4;


    @Comment("视图图标_5[ICON_5]")
    private String icon5;


    @Comment("是否公开[IS_OPEN]    是否公开:1=开放 0=私有")
    private Integer isOpen;


    @Comment("公开时间[OPEN_TIME]")
    private Long openTime;


    @Comment("数据驱动类型[DATA_UP_TYPE]    数据驱动类型:1=不更新 2=标记提示 3=自动更新")
    private Integer dataUpType;


    @Comment("视图状态[STATUS]    视图状态:1=正常 0=回收站")
    private Integer status;


    @Comment("CI3D坐标[CI_3D_POINT]")
    private String ci3dPoint;


    @Comment("CI3D坐标2[CI_3D_POINT2]")
    private String ci3dPoint2;


    @Comment("搜索字段[SEARCH_FIELD]    搜索字段:视图名称")
    private String searchField;


    @Comment("组合视图行数[COMB_ROWS]    组合视图行数:组合视图字段")
    private Integer combRows;


    @Comment("组合视图列数[COMB_COLS]    组合视图列数:组合视图字段")
    private Integer combCols;


    @Comment("查看次数[READ_COUNT]")
    private Long readCount;


    @Comment("应用关联CI[APP_RLT_CI_CODE]    关联CI:针对应用墙点击CI弹出组合视图")
    private String appRltCiCode;


    @Comment("参照版本id[REFER_VERSION_ID]")
    private Long referVersionId;


    @Comment("所属域[DOMAIN_ID]")
    private Long domainId;


    @Comment("数据状态[DATA_STATUS]    数据状态:0=删除，1=正常")
    private Integer dataStatus;


    @Comment("创建人[CREATOR]")
    private String creator;


    @Comment("修改人[MODIFIER]")
    private String modifier;


    @Comment("创建时间[CREATE_TIME]    创建时间:yyyyMMddHHmmss")
    private Long createTime;


    @Comment("更新时间[MODIFY_TIME]    更新时间:yyyyMMddHHmmss")
    private Long modifyTime;

    @Comment("缩略图保存时间[THUMBNAIL_SAVE_ TIME]    yyyyMMddHHmmss")
    private Long thumbnailSaveTime = 19700101000000L;

    @Comment("主题文件夹[SUBJECT_ID]")
    private Long subjectId;

    private Long oldDirId;

    public String getdEnergy() {
        return SecureUtil.md5(String.valueOf(id)).substring(8, 24);
    }

    public void setdEnergy(String dEnergy) {
        this.dEnergy = dEnergy;
    }

    // 转为模板的时间
    @Comment("转换为模板的时间[TEM_CONVERT_TIME]")
    private Long temConvertTime;

    @Comment("视图类型[VIEW_TYPE]")
    private String viewType;

    // 关联的模板id (不写入数据库)
    private Long temDirId;
    // 模板名称 (不写入数据库)
    private String temDirName;
    // 模板类型 (不写入数据库)
    private Integer temType;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }


    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }


    public Long getUserId() {
        return this.userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }


    public Long getDirId() {
        return this.dirId;
    }

    public void setDirId(Long dirId) {
        this.dirId = dirId;
    }


    public Integer getDirType() {
        return this.dirType;
    }

    public void setDirType(Integer dirType) {
        this.dirType = dirType;
    }


    public Integer getDiagramType() {
        return this.diagramType;
    }

    public void setDiagramType(Integer diagramType) {
        this.diagramType = diagramType;
    }


    public String getDiagramDesc() {
        return this.diagramDesc;
    }

    public void setDiagramDesc(String diagramDesc) {
        this.diagramDesc = diagramDesc;
    }


    public String getDiagramSvg() {
        return this.diagramSvg;
    }

    public void setDiagramSvg(String diagramSvg) {
        this.diagramSvg = diagramSvg;
    }


    public String getDiagramXml() {
        return this.diagramXml;
    }

    public void setDiagramXml(String diagramXml) {
        this.diagramXml = diagramXml;
    }


    public String getDiagramJson() {
        return this.diagramJson;
    }

    public void setDiagramJson(String diagramJson) {
        this.diagramJson = diagramJson;
    }


    public String getDiagramBgImg() {
        return this.diagramBgImg;
    }

    public void setDiagramBgImg(String diagramBgImg) {
        this.diagramBgImg = diagramBgImg;
    }


    public String getDiagramBgCss() {
        return this.diagramBgCss;
    }

    public void setDiagramBgCss(String diagramBgCss) {
        this.diagramBgCss = diagramBgCss;
    }


    public String getIcon1() {
        return this.icon1;
    }

    public void setIcon1(String icon1) {
        this.icon1 = icon1;
    }


    public String getIcon2() {
        return this.icon2;
    }

    public void setIcon2(String icon2) {
        this.icon2 = icon2;
    }


    public String getIcon3() {
        return this.icon3;
    }

    public void setIcon3(String icon3) {
        this.icon3 = icon3;
    }


    public String getIcon4() {
        return this.icon4;
    }

    public void setIcon4(String icon4) {
        this.icon4 = icon4;
    }


    public String getIcon5() {
        return this.icon5;
    }

    public void setIcon5(String icon5) {
        this.icon5 = icon5;
    }


    public Integer getIsOpen() {
        return this.isOpen;
    }

    public void setIsOpen(Integer isOpen) {
        this.isOpen = isOpen;
    }


    public Long getOpenTime() {
        return this.openTime;
    }

    public void setOpenTime(Long openTime) {
        this.openTime = openTime;
    }


    public Integer getDataUpType() {
        return this.dataUpType;
    }

    public void setDataUpType(Integer dataUpType) {
        this.dataUpType = dataUpType;
    }


    public Integer getStatus() {
        return this.status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }


    public String getCi3dPoint() {
        return this.ci3dPoint;
    }

    public void setCi3dPoint(String ci3dPoint) {
        this.ci3dPoint = ci3dPoint;
    }


    public String getCi3dPoint2() {
        return this.ci3dPoint2;
    }

    public void setCi3dPoint2(String ci3dPoint2) {
        this.ci3dPoint2 = ci3dPoint2;
    }


    public String getSearchField() {
        return this.searchField;
    }

    public void setSearchField(String searchField) {
        this.searchField = searchField;
    }


    public Integer getCombRows() {
        return this.combRows;
    }

    public void setCombRows(Integer combRows) {
        this.combRows = combRows;
    }


    public Integer getCombCols() {
        return this.combCols;
    }

    public void setCombCols(Integer combCols) {
        this.combCols = combCols;
    }


    public Long getReadCount() {
        return this.readCount;
    }

    public void setReadCount(Long readCount) {
        this.readCount = readCount;
    }


    public String getAppRltCiCode() {
        return this.appRltCiCode;
    }

    public void setAppRltCiCode(String appRltCiCode) {
        this.appRltCiCode = appRltCiCode;
    }


    public Long getReferVersionId() {
        return this.referVersionId;
    }

    public void setReferVersionId(Long referVersionId) {
        this.referVersionId = referVersionId;
    }


    public Long getDomainId() {
        return this.domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }


    public Integer getDataStatus() {
        return this.dataStatus;
    }

    public void setDataStatus(Integer dataStatus) {
        this.dataStatus = dataStatus;
    }


    public String getCreator() {
        return this.creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }


    public String getModifier() {
        return this.modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }


    public Long getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }


    public Long getModifyTime() {
        return this.modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Long getThumbnailSaveTime() {
        return thumbnailSaveTime;
    }

    public void setThumbnailSaveTime(Long thumbnailSaveTime) {
        this.thumbnailSaveTime = thumbnailSaveTime;
    }

    public Long getSubjectId() {
        return subjectId;
    }

    public void setSubjectId(Long subjectId) {
        this.subjectId = subjectId;
    }

    public Long getOldDirId() {
        return oldDirId;
    }

    public void setOldDirId(Long oldDirId) {
        this.oldDirId = oldDirId;
    }

    public Long getTemDirId() {
        return temDirId;
    }

    public void setTemDirId(Long temDirId) {
        this.temDirId = temDirId;
    }

    public String getTemDirName() {
        return temDirName;
    }

    public void setTemDirName(String temDirName) {
        this.temDirName = temDirName;
    }

    public Integer getTemType() {
        return temType;
    }

    public void setTemType(Integer temType) {
        this.temType = temType;
    }

    public Long getTemConvertTime() {
        return temConvertTime;
    }

    public void setTemConvertTime(Long temConvertTime) {
        this.temConvertTime = temConvertTime;
    }

    public String getViewType() {
        return viewType;
    }

    public void setViewType(String viewType) {
        this.viewType = viewType;
    }
}


