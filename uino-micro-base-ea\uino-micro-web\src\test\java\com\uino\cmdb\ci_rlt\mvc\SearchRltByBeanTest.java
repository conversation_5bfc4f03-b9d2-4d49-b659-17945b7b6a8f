package com.uino.cmdb.ci_rlt.mvc;

import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import com.alibaba.fastjson.JSON;
import com.binary.framework.web.RemoteResult;
import com.uino.StartBaseWebAppliaction;
import com.uino.bean.cmdb.query.ESRltSearchBean;

import lombok.extern.slf4j.Slf4j;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = { StartBaseWebAppliaction.class }, webEnvironment = WebEnvironment.RANDOM_PORT)
@Slf4j
@ActiveProfiles("provider-local")
public class SearchRltByBeanTest {
	@Autowired
	private TestRestTemplate restTemplate;
	// @MockBean
	// private ESCIRltSvc esCiRltSvc;
	private static String testUrl;

	@BeforeClass
	public static void initCls() {
		SearchRltByBeanTest.testUrl = "/cmdb/ciRlt/searchRltByBean";
	}

	@Before
	public void start() {
		// Mockito.when(esCiRltSvc.searchRltByBean(Mockito.any())).thenReturn(new
		// Page<>(1, 1, 1, 1, new ArrayList<>()));
	}

	@Test
	public void test01() {
		ESRltSearchBean reqBean = ESRltSearchBean.builder().build();
		String responseBodyStr = restTemplate.postForObject(testUrl, reqBean, String.class);
		RemoteResult responseBody = JSON.parseObject(responseBodyStr, RemoteResult.class);
		Assert.assertTrue(responseBody.isSuccess());

	}
}
