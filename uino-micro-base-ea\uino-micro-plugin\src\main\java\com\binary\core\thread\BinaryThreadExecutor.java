package com.binary.core.thread;

import com.binary.core.exception.BinaryException;
import com.binary.core.util.BinaryUtils;

public class BinaryThreadExecutor {
	
	
	private static class ExecutorRunnable<E> implements Runnable {
		
		ExecutorEntity<E> executorEntity;
		ExecutorCallback<E> callback;
		ExecutorRunnable(ExecutorEntity<E> executorEntity, ExecutorCallback<E> callback) {
			this.executorEntity = executorEntity;
			this.callback = callback;
		}
		
		
		@Override
		public void run() {
			boolean success = true;
			E result = null;
			Throwable tx = null;
			try {
				result = this.executorEntity.run();
			}catch(Throwable t) {
				success = false;
				tx = t;
			}
			
			if(this.callback == null) {
				if(tx != null) throw new BinaryException(tx);
			}else {
				this.callback.callback(success, result, tx);
			}
		}
	}
	
	
	private BinaryThreadPool threadPool;
	
	
	
	
	public BinaryThreadExecutor(BinaryThreadPool threadPool) {
		BinaryUtils.checkEmpty(threadPool, "threadPool");
		this.threadPool = threadPool;
	}
	
	
	
	/**
	 * 执行任务
	 * @param executorEntity
	 */
	public <T> void execute(ExecutorEntity<T> executorEntity) {
		execute(executorEntity, null);
	}
	
	
	
	
	/**
	 * 执行任务
	 * @param executorEntity
	 * @param callback
	 */
	public <T> void execute(ExecutorEntity<T> executorEntity, ExecutorCallback<T> callback) {
		BinaryUtils.checkEmpty(executorEntity, "executorEntity");
		ExecutorRunnable<T> runnable = new ExecutorRunnable<T>(executorEntity, callback);
		this.threadPool.pushTask(runnable);
	}
	
	

}
