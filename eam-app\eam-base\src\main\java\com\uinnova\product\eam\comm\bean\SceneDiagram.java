package com.uinnova.product.eam.comm.bean;

import java.io.Serializable;

import com.binary.framework.bean.annotation.Comment;

/**
 * 组合视图对应的场景关联关系
 * <AUTHOR>
 *
 */
public class SceneDiagram implements Serializable {
	private static final long serialVersionUID = 1L;
	
	@Comment("场景视图关联ID")
	private Long id;
	
	@Comment("场景ID")
	private Long sceneId;
	
	@Comment("视图ID")
	private Long diagramId;
	
	@Comment("视图类型 1=单图 2=组合视图")
	private Long diagramType;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getSceneId() {
		return sceneId;
	}

	public void setSceneId(Long sceneId) {
		this.sceneId = sceneId;
	}

	public Long getDiagramId() {
		return diagramId;
	}

	public void setDiagramId(Long diagramId) {
		this.diagramId = diagramId;
	}

	public Long getDiagramType() {
		return diagramType;
	}

	public void setDiagramType(Long diagramType) {
		this.diagramType = diagramType;
	}
	
	
	

}
