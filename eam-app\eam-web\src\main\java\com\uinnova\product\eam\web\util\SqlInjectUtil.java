package com.uinnova.product.eam.web.util;

import java.util.List;
import java.util.regex.Pattern;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import com.binary.core.exception.MessageException;

/**
 * SQL注入特殊字符过滤工具类
 *
 *
 */
@Slf4j
public class SqlInjectUtil {
	
	/**
	 * 判断字符串中是否有SQL的敏感字符
	 * 
	 * @param str
	 *            需要校验的字符串
	 * @return true:包含特殊字符 false:不包含特殊字符
	 */
	public static void hasSqlInject(String str) {
		Pattern p = Pattern.compile("'|drop|select|delete|%|truncate|declare|;|\\+|,");
		if (p.matcher(str).find()) {
			log.info("Have sql inject str!!!! [" + str + "]");
			throw MessageException.i18n("PARAMETER_FORBIDDON");
		}
	}

	public static void hasSpecialStrs(String[] strs) {
		if (strs != null && strs.length > 0) {
			for (int i = 0; i < strs.length; i++) {
				hasSpecialStr(strs[i]);
			}
		}
	}

	public static void hasSpecialStrs(List<String> strs) {
		if (strs != null && strs.size() > 0) {
			for (int i = 0; i < strs.size(); i++) {
				hasSpecialStr(strs.get(i));
			}
		}
	}

	/**
	 * 判断字符串中是否有SQL的敏感字符
	 * 
	 * @param str 需要校验的字符串
	 * @return true:包含特殊字符 false:不包含特殊字符
	 */
	public static void hasSpecialStr(String str) {

		if (!StringUtils.isEmpty(str)) {
			Pattern p = Pattern.compile("'|\\+|;|%");
			if (p.matcher(str).find()) {
				log.info("Have sql inject str!!!! [" + str + "]");
				throw MessageException.i18n("PARAMETER_FORBIDDON");
			}
		}
	}

}
