package com.uinnova.product.vmdb.comm.expression.support;

import com.uinnova.product.vmdb.comm.expression.Field;
import com.uinnova.product.vmdb.comm.expression.FieldDefinition;

/**
 * 
 * <AUTHOR>
 *
 */
public abstract class AbstractField<E> implements Field<E> {
    private static final long serialVersionUID = 1L;

    protected FieldDefinition<E> fieldDefinition;

    protected AbstractField(FieldDefinition<E> fieldDefinition) {
        this.fieldDefinition = fieldDefinition;
    }

    @Override
    public String getName() {
        return this.fieldDefinition.getName();
    }

    @Override
    public String getMappingName() {
        return this.fieldDefinition.getMappingName();
    }

}
