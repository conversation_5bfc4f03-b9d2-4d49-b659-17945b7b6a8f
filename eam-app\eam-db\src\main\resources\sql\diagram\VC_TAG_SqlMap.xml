<?xml version="1.0" encoding="UTF-8"?><!--Converted at: Sat Dec 08 16:22:42 CST 2018-->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="VC_TAG">


	<resultMap id="queryResult" type="com.uinnova.product.eam.comm.model.VcTag">
		<result property="id" column="ID" jdbcType="BIGINT"/>	<!-- ID -->
		<result property="tagName" column="TAG_NAME" jdbcType="VARCHAR"/>	<!-- 标签名称 -->
		<result property="tagType" column="TAG_TYPE" jdbcType="INTEGER"/>	<!-- 标签类型 -->
		<result property="defDesc" column="DEF_DESC" jdbcType="VARCHAR"/>	<!-- 定义描述 -->
		<result property="parentId" column="PARENT_ID" jdbcType="BIGINT"/>	<!-- 父标签id -->
		<result property="tagAttr" column="TAG_ATTR" jdbcType="INTEGER"/>	<!-- 标签属性 -->
		<result property="domainId" column="DOMAIN_ID" jdbcType="BIGINT"/>	<!-- 所属域 -->
		<result property="dataStatus" column="DATA_STATUS" jdbcType="INTEGER"/>	<!-- 数据状态 -->
		<result property="creator" column="CREATOR" jdbcType="VARCHAR"/>	<!-- 创建人 -->
		<result property="modifier" column="MODIFIER" jdbcType="VARCHAR"/>	<!-- 修改人 -->
		<result property="createTime" column="CREATE_TIME" jdbcType="BIGINT"/>	<!-- 创建时间 -->
		<result property="modifyTime" column="MODIFY_TIME" jdbcType="BIGINT"/>	<!-- 修改时间 -->
	</resultMap>
	

	<sql id="sql_query_where">
		<if test="cdt != null and cdt.id != null">and
			ID = #{cdt.id:BIGINT}
		</if>
		<if test="ids != null and ids != ''">and
			ID in (${ids})
		</if>
		<if test="cdt != null and cdt.startId != null">and
			 ID &gt;= #{cdt.startId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endId != null">and
			 ID &lt;= #{cdt.endId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.tagName != null and cdt.tagName != ''">and
			TAG_NAME like #{cdt.tagName,jdbcType=VARCHAR} 
		</if>
		<if test="cdt != null and cdt.tagNameEqual != null and cdt.tagNameEqual != ''">and
			TAG_NAME = #{cdt.tagNameEqual,jdbcType=VARCHAR}
		</if>
		<if test="tagNames != null and tagNames != ''">and
			TAG_NAME in (${tagNames})
		</if>
		<if test="cdt != null and cdt.tagType != null">and
			TAG_TYPE = #{cdt.tagType:INTEGER}
		</if>
		<if test="tagTypes != null and tagTypes != ''">and
			TAG_TYPE in (${tagTypes})
		</if>
		<if test="cdt != null and cdt.startTagType != null">and
			 TAG_TYPE &gt;= #{cdt.startTagType:INTEGER} 
		</if>
		<if test="cdt != null and cdt.endTagType != null">and
			 TAG_TYPE &lt;= #{cdt.endTagType:INTEGER} 
		</if>
		<if test="cdt != null and cdt.defDesc != null and cdt.defDesc != ''">and
			DEF_DESC like #{cdt.defDesc,jdbcType=VARCHAR} 
		</if>
		<if test="cdt != null and cdt.parentId != null">and
			PARENT_ID = #{cdt.parentId:BIGINT}
		</if>
		<if test="parentIds != null and parentIds != ''">and
			PARENT_ID in (${parentIds})
		</if>
		<if test="cdt != null and cdt.startParentId != null">and
			 PARENT_ID &gt;= #{cdt.startParentId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endParentId != null">and
			 PARENT_ID &lt;= #{cdt.endParentId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.tagAttr != null">and
			TAG_ATTR = #{cdt.tagAttr:INTEGER}
		</if>
		<if test="tagAttrs != null and tagAttrs != ''">and
			TAG_ATTR in (${tagAttrs})
		</if>
		<if test="cdt != null and cdt.startTagAttr != null">and
			 TAG_ATTR &gt;= #{cdt.startTagAttr:INTEGER} 
		</if>
		<if test="cdt != null and cdt.endTagAttr != null">and
			 TAG_ATTR &lt;= #{cdt.endTagAttr:INTEGER} 
		</if>
		<if test="cdt != null and cdt.domainId != null">and
			DOMAIN_ID = #{cdt.domainId:BIGINT}
		</if>
		<if test="domainIds != null and domainIds != ''">and
			DOMAIN_ID in (${domainIds})
		</if>
		<if test="cdt != null and cdt.startDomainId != null">and
			 DOMAIN_ID &gt;= #{cdt.startDomainId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endDomainId != null">and
			 DOMAIN_ID &lt;= #{cdt.endDomainId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.dataStatus != null">and
			DATA_STATUS = #{cdt.dataStatus:INTEGER}
		</if>
		<if test="dataStatuss != null and dataStatuss != ''">and
			DATA_STATUS in (${dataStatuss})
		</if>
		<if test="cdt != null and cdt.startDataStatus != null">and
			 DATA_STATUS &gt;= #{cdt.startDataStatus:INTEGER} 
		</if>
		<if test="cdt != null and cdt.endDataStatus != null">and
			 DATA_STATUS &lt;= #{cdt.endDataStatus:INTEGER} 
		</if>
		<if test="cdt != null and cdt.creator != null and cdt.creator != ''">and
			CREATOR like #{cdt.creator,jdbcType=VARCHAR} 
		</if>
		<if test="cdt != null and cdt.creatorEqual != null and cdt.creatorEqual != ''">and
			CREATOR = #{cdt.creatorEqual,jdbcType=VARCHAR}
		</if>
		<if test="creators != null and creators != ''">and
			CREATOR in (${creators})
		</if>
		<if test="cdt != null and cdt.modifier != null and cdt.modifier != ''">and
			MODIFIER like #{cdt.modifier,jdbcType=VARCHAR} 
		</if>
		<if test="cdt != null and cdt.modifierEqual != null and cdt.modifierEqual != ''">and
			MODIFIER = #{cdt.modifierEqual,jdbcType=VARCHAR}
		</if>
		<if test="modifiers != null and modifiers != ''">and
			MODIFIER in (${modifiers})
		</if>
		<if test="cdt != null and cdt.createTime != null">and
			CREATE_TIME = #{cdt.createTime:BIGINT}
		</if>
		<if test="createTimes != null and createTimes != ''">and
			CREATE_TIME in (${createTimes})
		</if>
		<if test="cdt != null and cdt.startCreateTime != null">and
			 CREATE_TIME &gt;= #{cdt.startCreateTime:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endCreateTime != null">and
			 CREATE_TIME &lt;= #{cdt.endCreateTime:BIGINT} 
		</if>
		<if test="cdt != null and cdt.modifyTime != null">and
			MODIFY_TIME = #{cdt.modifyTime:BIGINT}
		</if>
		<if test="modifyTimes != null and modifyTimes != ''">and
			MODIFY_TIME in (${modifyTimes})
		</if>
		<if test="cdt != null and cdt.startModifyTime != null">and
			 MODIFY_TIME &gt;= #{cdt.startModifyTime:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endModifyTime != null">and
			 MODIFY_TIME &lt;= #{cdt.endModifyTime:BIGINT} 
		</if>
	</sql>
	

	<sql id="sql_update_columns">
		<if test="record != null and record.id != null"> 
			ID = #{record.id:BIGINT}
		,</if>
		<if test="record != null and record.tagName != null"> 
			TAG_NAME = #{record.tagName,jdbcType=VARCHAR}
		,</if>
		<if test="record != null and record.tagType != null"> 
			TAG_TYPE = #{record.tagType:INTEGER}
		,</if>
		<if test="record != null and record.defDesc != null"> 
			DEF_DESC = #{record.defDesc,jdbcType=VARCHAR}
		,</if>
		<if test="record != null and record.parentId != null"> 
			PARENT_ID = #{record.parentId:BIGINT}
		,</if>
		<if test="record != null and record.tagAttr != null"> 
			TAG_ATTR = #{record.tagAttr:INTEGER}
		,</if>
		<if test="record != null and record.domainId != null"> 
			DOMAIN_ID = #{record.domainId:BIGINT}
		,</if>
		<if test="record != null and record.dataStatus != null"> 
			DATA_STATUS = #{record.dataStatus:INTEGER}
		,</if>
		<if test="record != null and record.creator != null"> 
			CREATOR = #{record.creator,jdbcType=VARCHAR}
		,</if>
		<if test="record != null and record.modifier != null"> 
			MODIFIER = #{record.modifier,jdbcType=VARCHAR}
		,</if>
		<if test="record != null and record.createTime != null"> 
			CREATE_TIME = #{record.createTime:BIGINT}
		,</if>
		<if test="record != null and record.modifyTime != null"> 
			MODIFY_TIME = #{record.modifyTime:BIGINT}
		,</if>
	</sql>
	

	<sql id="sql_query_columns">
		ID, TAG_NAME, TAG_TYPE, DEF_DESC, PARENT_ID, TAG_ATTR, 
		DOMAIN_ID, DATA_STATUS, CREATOR, MODIFIER, CREATE_TIME, MODIFY_TIME
	</sql>
	

	

	<select id="selectList" parameterType="java.util.Map" resultMap="queryResult">
		select 
			<include refid="VC_TAG.sql_query_columns"/>
		from VC_TAG 
			<where>
				<include refid="VC_TAG.sql_query_where"/>
			</where>
		order by 
			<if test="orders != null and orders != ''">
				${orders}
			</if>
			<if test="orders == null or orders == ''">
				ID
			</if>
	</select>
	<select id="selectCount" parameterType="java.util.Map" resultType="java.lang.Long">
		select count(1) from VC_TAG 
			<where>
				<include refid="VC_TAG.sql_query_where"/>
			</where>
	</select>
	<select id="selectById" parameterType="java.util.Map" resultMap="queryResult">
		select 
			<include refid="VC_TAG.sql_query_columns"/>
		from VC_TAG where ID=#{id:BIGINT} and DATA_STATUS=1  
	</select>
	

	

	<insert id="insert" parameterType="java.util.Map">
		insert into VC_TAG(
			ID, TAG_NAME, TAG_TYPE, DEF_DESC, PARENT_ID, 
			TAG_ATTR, DOMAIN_ID, DATA_STATUS, CREATOR, MODIFIER, 
			CREATE_TIME, MODIFY_TIME)
		values (
			#{record.id:BIGINT}, #{record.tagName,jdbcType=VARCHAR}, #{record.tagType:INTEGER}, #{record.defDesc,jdbcType=VARCHAR}, #{record.parentId:BIGINT}, 
			#{record.tagAttr:INTEGER}, #{record.domainId:BIGINT}, #{record.dataStatus:INTEGER}, #{record.creator,jdbcType=VARCHAR}, #{record.modifier,jdbcType=VARCHAR}, 
			#{record.createTime:BIGINT}, #{record.modifyTime:BIGINT})
	</insert>
	

	

	<update id="updateById" parameterType="java.util.Map">
		update VC_TAG
			<set> 
				<include refid="VC_TAG.sql_update_columns"/> 
			</set>
		where ID = #{id:BIGINT}
	</update>
	<update id="updateByCdt" parameterType="java.util.Map">
		update VC_TAG
			<set> 
				<include refid="VC_TAG.sql_update_columns"/> 
			</set>
			<where> 
				<include refid="VC_TAG.sql_query_where"/> 
			</where>
	</update>
	
	

	

	<delete id="deleteById" parameterType="java.util.Map">
		delete from VC_TAG where ID = #{id:BIGINT}
	</delete>
	<delete id="deleteByCdt" parameterType="java.util.Map">
		delete from VC_TAG
			<where> 
				<include refid="VC_TAG.sql_query_where"/> 
			</where>
	</delete>
	



</mapper>