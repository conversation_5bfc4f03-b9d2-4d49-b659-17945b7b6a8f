package com.uino.api.client.sys;

import org.springframework.web.multipart.MultipartFile;

import com.binary.core.io.Resource;

public interface ISysApiSvc{
	
	 /**
     * 上传文件
     * 
     * @param file
     *            待上传文件
     * @return 文件路径
     */
    public String uploadFile(MultipartFile file);
    
    public Resource downloadFile(String filePath);

    /**
     * 上传文件
     * 
     * @param fileBytes
     *            待上传文件字节流
     * @param fileName
     *            文件名称
     * @return 文件路径
     */
    public String uploadFile(byte[] fileBytes, String fileName);
}
