package com.uinnova.product.eam.service.asset.impl;

import com.binary.core.exception.BinaryException;
import com.binary.core.util.BinaryUtils;
import com.uinnova.product.eam.comm.model.es.AssetChangeRecord;
import com.uinnova.product.eam.service.asset.AssetChangeRecordSvc;
import com.uinnova.product.eam.service.es.AssetChangeRecordDao;
import com.uino.api.client.permission.IUserApiSvc;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.permission.business.CurrentUserInfo;
import com.uino.util.sys.CheckAttrUtil;
import com.uino.util.sys.SysUtil;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
@Service
public class AssetChangeRecordSvcImpl implements AssetChangeRecordSvc {
    @Resource
    AssetChangeRecordDao changeRecordDao;
    @Resource
    IUserApiSvc userApiSvc;
    @Override
    public Long saveChangeRecord(AssetChangeRecord changeRecord) {
        BinaryUtils.checkEmpty(changeRecord,"变更流程信息");
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        Long domainId = currentUserInfo.getDomainId();
        long timeMillis = System.currentTimeMillis();
        CurrentUserInfo currentUser = userApiSvc.getCurrentUser();
        if (BinaryUtils.isEmpty(changeRecord.getId())) {
            changeRecord.setSubmitter(currentUser.getUserName());
            changeRecord.setCreateTime(timeMillis);
        }
        if (BinaryUtils.isEmpty(currentUser)) {
            throw new BinaryException("用户信息不存在");
        }
        changeRecord.setDomainId(domainId);
        changeRecord.setModifyTime(timeMillis);
        return changeRecordDao.saveOrUpdate(changeRecord);
    }

    @Override
    public List<AssetChangeRecord> getAssetChangeRecordByCiCode(String ciCode) {
        BinaryUtils.checkEmpty(ciCode,"ciCode");
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("ciCode.keyword", ciCode));
        query.must(QueryBuilders.termQuery("domainId", SysUtil.getCurrentUserInfoNotThrow().getDomainId()));
        query.must(QueryBuilders.termQuery("state", 2));
        List<AssetChangeRecord> changeRecords = changeRecordDao.getListByQuery(query);
        if (CollectionUtils.isEmpty(changeRecords)) {
            return Collections.emptyList();
        }
        changeRecords.sort(Comparator.comparing(AssetChangeRecord::getModifyTime).reversed());
        return changeRecords;
    }

    @Override
    public AssetChangeRecord getById(Long id) {
        BinaryUtils.checkEmpty(id,"id");
        return changeRecordDao.getById(id);
    }
}
