package com.uino.bean.permission.business.request;

import java.util.Set;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.util.Assert;

import com.uino.bean.permission.business.IValidDto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 
 * <AUTHOR>
 *
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value="保存用户角色关系",description = "保存用户角色关系")
public class SaveUserRoleRltRequestDto implements IValidDto {

    /**
     * 用户id
     */
    @ApiModelProperty(value="用户id",example = "123")
    private Long userId;
    /**
     * 该用户拥有的角色id
     */
    @ApiModelProperty(value="该用户拥有的角色id")
    private Set<Long> roleIds;

    /**
     * 用户所属域id
     */
    @ApiModelProperty(value="该用户所属域")
    private Long domainId;

    @Override
    public void valid() {
        Assert.notNull(userId, "userIds is null");
    }
}
