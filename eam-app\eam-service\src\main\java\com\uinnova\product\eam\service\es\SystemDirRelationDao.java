package com.uinnova.product.eam.service.es;

import com.uinnova.product.eam.comm.model.es.SystemDirRelation;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Repository;

import jakarta.annotation.PostConstruct;

@Repository
public class SystemDirRelationDao  extends AbstractESBaseDao<SystemDirRelation,SystemDirRelation> {
    @Override
    public String getIndex() {
        return "cj_sys_dir_relation";
    }

    @Override
    public String getType() {
        return "_doc";
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }

}
