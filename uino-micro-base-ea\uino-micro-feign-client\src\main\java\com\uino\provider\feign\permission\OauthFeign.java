package com.uino.provider.feign.permission;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.uino.bean.permission.base.OAuthClientDetail;
import com.uino.bean.permission.base.OauthRefreshTokenDetail;
import com.uino.bean.permission.base.OauthResourceDetail;
import com.uino.bean.permission.base.OauthTokenDetail;
import com.uino.bean.permission.business.request.RegisterClientReq;
import com.uino.provider.feign.config.BaseFeignConfig;

@Component
@FeignClient(name = "${feign-server-name:tarsier-platform}", path = "${feign-server-path:tarsier-platform}/feign/oauth", configuration = {
        BaseFeignConfig.class})
public interface OauthFeign {

    // 客户端信息相关-------------------------
    /**
     * 根据客户端code获取客户端详情
     * 
     * @param clientCode
     * @return
     */
    @PostMapping("getClientInfoByCode")
    OAuthClientDetail getClientInfoByCode(@RequestParam(value = "clientCode", required = false) String clientCode);

    /**
     * 注册客户端
     * 
     * @param req
     */
    @PostMapping("register")
    void register(@RequestBody(required = false) RegisterClientReq req);

    /**
     * 根据code移除客户端
     * 
     * @param clientCode
     */
    @PostMapping("removeClientInfoByCode")
    void removeClientInfoByCode(@RequestBody(required = false) String clientCode);

    /**
     * 获取所有客户端
     * 
     * @return
     */
    @PostMapping("getAllClents")
    List<OAuthClientDetail> getAllClents();
    // 资源信息相关---------------------------------------

    /**
     * 根据资源名称获取资源信息
     * 
     * @param name
     * @return
     */
    @PostMapping("getResourceDetail")
    OauthResourceDetail getResourceDetail(@RequestBody(required = false) String name);
    
   
   /**
	 * 保存oauth资源段
	 * @param saveDto
	 * @return
	 */
    @PostMapping("save")
	public boolean save(@RequestBody(required = false) OauthResourceDetail saveDto);

	/**
	 * 获取所有oauth资源端列表
	 * @return
	 */
    @PostMapping("List")
	public List<OauthResourceDetail> list();

    // token相关--------------------------------------
    /**
     * 持久化
     * 
     * @param tokenDetail
     * @return
     */
    @PostMapping("persistenceToken")
    Long persistenceToken(@RequestBody(required = false) OauthTokenDetail tokenDetail);

    @PostMapping("getTokenDetailByReTokenCode")
    public OauthTokenDetail getTokenDetailByReTokenCode(
            @RequestParam(required = false, value = "reTokenCode") String reTokenCode);

    /**
     * 获取token
     *
     * @param tokenCode
     * @return
     */
    @PostMapping("getTokenDetailByCode")
    OauthTokenDetail getTokenDetailByCode(@RequestParam(value = "tokenCode", required = false) String tokenCode);

    /**
     * 获取token
     * 
     * @param authId
     * @return
     */
    @PostMapping("getTokenDetailByAuthId")
    OauthTokenDetail getTokenDetailByAuthId(@RequestParam(value = "authId", required = false) String authId);

    /**
     * 获取token
     * 
     * @param clientId
     * @param userLoginName
     * @return
     */
    @PostMapping("getTokenDetail")
    List<OauthTokenDetail> getTokenDetail(@RequestParam(required = false, value = "clientId") String clientId,
            @RequestParam(required = false, value = "userLoginName") String userLoginName);

    /**
     * 根据tokenCode删除token
     * 
     * @param tokenCode
     */
    @PostMapping("delByCode")
    void delByCode(@RequestParam(required = false, value = "tokenCode") String tokenCode);

    /**
     * 根据刷新tokencode删除accesstoken
     * 
     * @param reTokenCode
     */
    @PostMapping("delByRefreshTokenCode")
    void delByRefreshTokenCode(@RequestParam(required = false, value = "reTokenCode") String reTokenCode);

    // 刷新token相关-----------分割线----------------------
    /**
     * 持久化刷新token
     * 
     * @param reRokenDetail
     * @return
     */
    @PostMapping("persistenceRefreshToken")
    Long persistenceRefreshToken(@RequestBody(required = false) OauthRefreshTokenDetail reRokenDetail);

    /**
     * 根据tokencode获取刷新token
     * 
     * @param tokenCode
     * @return
     */
    @PostMapping("getRefreshTokenDetail")
    OauthRefreshTokenDetail getRefreshTokenDetail(@RequestBody(required = false) String tokenCode);

    /**
     * 根据tokencode删除刷新token
     * 
     * @param tokenCode
     */
    @PostMapping("delRefreshTokenByCode")
    void delRefreshTokenByCode(@RequestBody(required = false) String tokenCode);
}
