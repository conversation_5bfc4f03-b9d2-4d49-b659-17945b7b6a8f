package com.uinnova.product.eam.model.vo;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.model.enums.CIRltDelType;
import lombok.Data;

import java.util.List;

@Comment("ci关系删除参数")
@Data
public class CIRltDelParam {

    @Comment("关系code")
    private String rltCode;
    @Comment("待删除关系codes")
    private List<String> delRltCodes;
    @Comment("保留关系codes")
    private List<String> ignoreRltCodes;
    @Comment("删除类型")
    private CIRltDelType delType;
    @Comment("待发布视图id")
    private String privateDiagramId;
    @Comment("待发布矩阵id")
    private Long privateMatrixId;
    @Comment("待删除的资产库视图id")
    private String designDiagramId;
    @Comment("待删除的资产库矩阵id")
    private Long designMatrixId;
    @Comment("id")
    private Long id;
    @Comment("创建时间")
    private Long createTime;
    @Comment("修改时间")
    private Long modifyTime;
    @Comment("流程实例id")
    private String processInstanceId;
    @Comment("操作人")
    private String opUser;
}
