package com.uino.web.sys.mvc;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import com.uino.bean.permission.base.SysUser;
import com.uino.init.api.ApiResult;
import com.uino.init.api.ApiVersion;
import com.uino.util.sys.SysUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uino.bean.sys.base.SysLoginLog;
import com.uino.bean.sys.business.QueryLoginLogRequestDto;
import com.uino.api.client.sys.ILogApiSvc;
import org.springframework.web.bind.annotation.RestController;

@ApiVersion(1)
@Api(value = "条件查询登录日志", tags = {"日志管理"})
@RestController
@RequestMapping("/sys/log")
public class SysLogMvc {
	@Autowired
	private ILogApiSvc logApiSvc;

	@ApiOperation("条件查询登录日志")
	@PostMapping("queryLoginLog")
    @ModDesc(desc = "条件查询登录日志", pDesc = "查询条件", pType = QueryLoginLogRequestDto.class, rDesc = "查询结果", rType = Page.class, rcType = SysLoginLog.class)
	public ApiResult<Page<SysLoginLog>> queryLoginLog(@RequestBody QueryLoginLogRequestDto query, HttpServletRequest request,
								   HttpServletResponse response) {
		SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
		query.setDomainId(currentUserInfo.getDomainId());
		Page<SysLoginLog> res = logApiSvc.queryLoginLog(query);
		return ApiResult.ok(this).data(res);
	}
}
