package com.uinnova.product.eam.service.exception;

import com.uinnova.product.eam.base.enums.ResultCodeEnum;
import lombok.Data;

/**
 * @description: 业务功能异常
 * @author: Lc
 * @create: 2021-12-09 16:09
 */
@Data
public class BusinessException extends RuntimeException {

    // 错误码
    private final int code;
    // 错误信息
    private final String message;
    //错误数据
    private transient Object data;

    public BusinessException(int code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }
    public BusinessException(int code, String message,Object data) {
        super(message);
        this.code = code;
        this.message = message;
        this.data = data;
    }

    public BusinessException(String message) {
        this(ResultCodeEnum.SERVICE_ERROR.getCode(), message);
    }

    public BusinessException(String message,Object data) {
        this(ResultCodeEnum.SERVICE_ERROR.getCode(), message,data);
    }

    public BusinessException(ResultCodeEnum resultCodeEnum) {
        this(resultCodeEnum.getCode(), resultCodeEnum.getMessage());
    }

}
