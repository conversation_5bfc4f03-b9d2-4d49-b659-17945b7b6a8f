package com.uino.service.permission.microservice.impl;

import com.binary.core.util.BinaryUtils;
import com.uino.dao.BaseConst;
import com.uino.dao.permission.ESModuleSvc;
import com.uino.service.permission.microservice.IButtonSvc;
import com.uino.service.permission.microservice.IModuleSvc;
import com.uino.bean.permission.base.SysModule;
import com.uino.bean.permission.query.SysModuleCheck;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.Map;
import java.util.prefs.BackingStoreException;

/**
 * @Title: ButtonSvc
 * @Description: The System Button
 * @Author: YGQ
 * @Create: 2021-06-28 10:06
 **/
@Service
public class ButtonSvc implements IButtonSvc {

    private final IModuleSvc iModuleSvc;
    private final ESModuleSvc moduleSvc;

    @Autowired
    public ButtonSvc(IModuleSvc iModuleSvc, ESModuleSvc moduleSvc) {

        this.iModuleSvc = iModuleSvc;
        this.moduleSvc = moduleSvc;
    }

    @Override
    public SysModule saveButton(SysModule buttonDto) {
        if(BinaryUtils.isEmpty(buttonDto.getDomainId())){
            buttonDto.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
        }
        this.parameterVerification(buttonDto);
        return iModuleSvc.saveModule(buttonDto);
    }

    @Override
    public void deleteButton(Long id) {
        iModuleSvc.delModule(id);
    }

    @Override
    public void saveButtonSort(Map<Long, Integer> orderDict) {
        iModuleSvc.saveOrder(orderDict);
    }

    @Override
    public SysModuleCheck checkModuleSign(SysModule sysButton) {
        SysModuleCheck sysModuleCheck = new SysModuleCheck();
        String buttonSign = sysButton.getModuleSign();
        if (BinaryUtils.isEmpty(sysButton.getDomainId())) {
            sysButton.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
        }
        if (null == buttonSign) {
            sysModuleCheck.setMessage("权限标识不可为空");
            return sysModuleCheck;
        }

        if (buttonSign.trim().length() > 32) {
            sysModuleCheck.setMessage("权限标识长度不可大于32");
            return sysModuleCheck;
        }

        BoolQueryBuilder validModuleSignQuery = QueryBuilders.boolQuery();
        Long moduleId = sysButton.getId();
        if (null != moduleId) {
            validModuleSignQuery.mustNot(QueryBuilders.termQuery("id", moduleId));
        }

        validModuleSignQuery.must(QueryBuilders.termQuery("moduleSign.keyword", buttonSign));
        validModuleSignQuery.must(QueryBuilders.termQuery("domainId",sysButton.getDomainId()));
        long validRes = moduleSvc.countByCondition(validModuleSignQuery);

        if (validRes > 0) {
            sysModuleCheck.setMessage("权限标识重复");
            return sysModuleCheck;
        }

        // pass
        sysModuleCheck.setResult(true);
        return sysModuleCheck;
    }

    /**
     * parameter verification
     *
     * @param buttonDto button
     */
    private void parameterVerification(SysModule buttonDto) {
        if (null == buttonDto) {
            throw new IllegalArgumentException("参数不可为空");
        } else {
            String buttonName = buttonDto.getModuleName();
            Assert.notNull(buttonName, "按钮名称不可为空");
            Assert.isTrue(buttonName.trim().length() <= 32, "按钮名称长度不可大于32");
            // check module sign
            SysModuleCheck sysModuleCheck = this.checkModuleSign(buttonDto);
            Assert.isTrue(sysModuleCheck.getResult(), sysModuleCheck.getMessage());
        }
    }
}
