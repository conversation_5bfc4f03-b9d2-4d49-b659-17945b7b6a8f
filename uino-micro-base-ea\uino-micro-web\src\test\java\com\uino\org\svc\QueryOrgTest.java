package com.uino.org.svc;

import com.alibaba.fastjson.JSON;
import com.binary.jdbc.Page;
import com.uino.StartBaseWebAppliaction;
import com.uino.bean.permission.base.SysOrg;
import com.uino.bean.permission.query.CSysOrg;
import com.uino.dao.cmdb.ESCIRltInfoHistorySvc;
import com.uino.dao.cmdb.ESCISvc;
import com.uino.dao.cmdb.ESVisualModelSvc;
import com.uino.dao.permission.ESModuleSvc;
import com.uino.dao.permission.ESOrgSvc;
import com.uino.service.permission.microservice.IOrgSvc;
import com.uino.service.simulation.impl.PerformanceSvc;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.junit.Before;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {StartBaseWebAppliaction.class}, webEnvironment = WebEnvironment.RANDOM_PORT)
@Slf4j
@ActiveProfiles("provider-local")
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class QueryOrgTest {

    @MockBean
    private ESOrgSvc esOrgSvc;

    @Autowired
    private IOrgSvc orgSvc;

    @Autowired
    private ESModuleSvc ems;

    @Autowired
    private ESCISvc esciSvc;

    @Autowired
    PerformanceSvc performanceSvc;

    @Autowired
    private ESVisualModelSvc visualModelSvc;

    @Autowired
    private ESCIRltInfoHistorySvc esCIRltHisSvc;

    @Test
    public void da() {
        // esCIRltHisSvc.getRltIdMaxVersion(new ArrayList<>());
        // List<ESVisualModel> visualModels =
        // visualModelSvc.getListByQuery(QueryBuilders.boolQuery());
        // visualModels.forEach(model -> model.setJson("[" + model.getJson() +
        // "]"));
        // visualModelSvc.saveOrUpdateBatch(visualModels);
        // performanceSvc.searchPerformance(new AlarmQueryDto());
        // String name = "功能区";
        // Long clsId = 5121339876600374L;
        // ESAttrAggBean queryAttr = new ESAttrAggBean();
        // queryAttr.setAttrName(name);
        // queryAttr.setClassId(clsId);
        // queryAttr.setPageSize(99999);
        // List<String> values = esciSvc.queryAttrVal(queryAttr).getData();
        // Set<String> values = esciSvc
        // .groupByCountField("attrs.5121339876600392.keyword",
        // QueryBuilders.termQuery("classId", clsId))
        // .keySet();
        // boolean has = false;
        // log.info(JSON.toJSONString(values));
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        // // List<ESAlarm> alarms = esAlarmSvc.getListByQuery(query);
        // esAlarmSvc.deleteByQuery(query, true);
        // log.info("del sucess");
        // query.should(QueryBuilders.termsQuery("id",
        // Arrays.asList(4376776227450005L, 4395239924850023L)));
        // query.should(QueryBuilders.termsQuery("parentId",
        // Arrays.asList(4376776227450005L, 4395239924850023L)));
        // List<SysModule> modules = ems.getListByQuery(query);
        log.info(JSON.toJSONString(ems.getSortListByQuery(1, 3000, query, "orderNo", true).getData()));
    }

    @Before
    public void before() {
        Mockito.when(esOrgSvc.getListByCdt(Mockito.anyInt(), Mockito.anyInt(), Mockito.any())).thenReturn(null);
        Mockito.when(esOrgSvc.getListByCdt(Mockito.any())).thenReturn(null);
    }

    @Test
    public void test01() {
        Page<SysOrg> re = orgSvc.queryPageByCdt(1, 1, new CSysOrg());
    }

    @Test
    public void test02() {
        List<SysOrg> re = orgSvc.queryListByCdt(new CSysOrg());
    }
}
