package com.uinnova.product.vmdb.provider.image;

import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.comm.model.image.CCcImage;
import com.uinnova.product.vmdb.comm.model.image.CcImage;
import com.uinnova.product.vmdb.provider.image.bean.CcImageInfo;
import com.uinnova.product.vmdb.provider.image.bean.ImageInfo;

import java.util.List;



// --注释掉检查 START (2024/5/17 10:21):
///**
// * 图像服务
// */
//public interface CcImageSvc {
//
//
//	/**
//	 * 分页查询
//	 * @param pageNum : 指定页码
//	 * @param pageSize : 指定页行数
//	 * @param cdt : 条件对象
//	 * @param orders : 排序字段, 多字段以逗号分隔
//	 * @return 操作员表[CC_IMAGE]分页列表对象
//	 */
//	public Page<CcImage> queryImagePage(Long domainId,Integer pageNum, Integer pageSize, CCcImage cdt, String orders);
//
//
//
//
//
//	/**
//	 * 分页查询图标信息(包含关联的3D图标)
//	 * @param pageNum : 指定页码
//	 * @param pageSize : 指定页行数
//	 * @param cdt : 条件对象
//	 * @param orders : 排序字段, 多字段以逗号分隔
//	 * @return 操作员表[CC_IMAGE]分页列表对象
//	 */
//	public Page<CcImageInfo> queryImageInfoPage(Long domainId, Integer pageNum, Integer pageSize, CCcImage cdt, String orders);
//
//
//
//
//
//	/**
//	 * 不分页批量查询
//	 * @param pageNum : 指定页码
//	 * @param pageSize : 指定页行数
//	 * @param cdt : 条件对象
//	 * @param orders : 排序字段, 多字段以逗号分隔
//	 * @return 操作员表[CC_IMAGE]分页列表对象
//	 */
//	public List<CcImage> queryImagePageList(Long domainId,Integer pageNum, Integer pageSize, CCcImage cdt, String orders);
//
//
//
//
//
//	/**
//	 * 不分页查询
//	 * @param cdt : 条件对象
//	 * @param orders : 排序字段, 多字段以逗号分隔
//	 * @return 操作员表[CC_IMAGE]查询列表
//	 */
//	public List<CcImage> queryImageList(Long domainId, CCcImage cdt, String orders);
//
//
//
//
//
//	/**
//	 * 根据图标名字查询图标信息
//	 * @param domainId 数据域
//	 * @param cdt 附加条件
//	 * @param ImageNames 图标名字数组
//	 * @return
//	 */
//	public List<CcImage> queryImagesByNames(Long domainId, CCcImage cdt, String[] imageNames);
//
//
//	/**
//	 * 新增通过图标完整路径(目录名称|图片名称)查询图标信息的接口
//	 * @param domainId
//	 * @param cdt
//	 * @param imageNames
//	 * @return
//	 */
//	public List<CcImage> queryImagesByFullNames(Long domainId, CCcImage cdt, String[] imageNames);
//
//
//
//	/**
//	 * 不分页查询图标信息(包含关联的3D图标)
//	 * @param cdt : 条件对象
//	 * @param orders : 排序字段, 多字段以逗号分隔
//	 * @return 操作员表[CC_IMAGE]查询列表
//	 */
//	public List<CcImageInfo> queryImageInfoList(Long domainId, CCcImage cdt, String orders);
//
//
//
//
//
//	/**
//	 * 跟据主键查询
//	 * @param id : 主键ID
//	 * @return 操作员表[SYS_OP]映射对象
//	 */
//	public CcImage queryImageById(Long domainId,Long id);
//
//
//
//	/**
//	 * 跟据主键查询图标信息(包含关联的3D图标)
//	 * @param id : 主键ID
//	 * @return 操作员表[SYS_OP]映射对象
//	 */
//	public CcImageInfo queryImageInfoById(Long domainId,Long id);
//
//
//
//
//	/**
//	 * 跟据条件查询图标的数量
//	 * @param domainId
//	 * @param cdt
//	 * @return
//	 */
//	public Long queryImageCount(Long domainId, CCcImage cdt);
//
//
//
//
//	/**
//	 * 查询DCV使用目录下的图标
//	 * @param domainId
//	 * @param pageNum
//	 * @param pageSize
//	 * @param dirName
//	 * @param cdt
//	 * @param orders
//	 * @return
//	 */
//	public Page<CcImage> querySystemImagePage(Long domainId,Integer pageNum, Integer pageSize, CCcImage cdt, String orders);
//
//
//
//
//	/**
//	 * 根据DCV领域下CI-CODE查询CI所属分类使用的图标挂载的3D图标(供DCV使用)
//	 * @param domainId 数据域
//	 * @param ciCodes CI-CODE
//	 * @return CI分类使用的图标挂载的3D图标集合
//	 */
//	public List<ImageInfo> query3DImageByCiCode(Long domainId, String[] ciCodes);
//
//
//
//
//
//	/**
//	 * 根据图标相对路径查询图标信息
//	 * @param cdt
//	 * @param imagePaths 图标相对路径数组
//	 * @return
//	 */
//	public List<CcImage> queryImagesByPaths(Long domainId, CCcImage cdt, String[] imagePaths);
//
//
//
//
//
//	/**
//	 * 查询排序字段最大值
//	 * @param cdt
//	 * @return
//	 */
//	public Long queryMaxOrderNo(Long domainId, CCcImage cdt);
//
//
//
//
//	/**
//	 * 保存获更新，判断主键ID[id]是否存在, 存在则更新, 不存在则插入
//	 * @param record : CcImage数据记录
//	 * @return 当前记录主键[id]值
//	 */
//	public Long saveOrUpdateImage(Long domainId,CcImage record);
//
//
//	/**
//	 * 批量保存IMAGE数据, 跟据id在库中是否存在来判断是插入还是更新
//	 * @param records
//	 * @return 对应的id列表
//	 */
//	public Long[] saveOrUpdateImageBatch(Long domainId,List<CcImage> records);
//
//
//
//
//
//	/**
//	 * 跟据图标ID删除图像
//	 * @param classId
//	 * @return
//	 */
//	public Integer removeImageById(Long domainId,Long id);
//
//
//
//	/**
//	 * 跟据图标path删除图像
//	 * @param domainId
//	 * @param path
//	 * @param root
//	 * @return
//	 */
//	public Integer removeImageByPath(Long domainId,String path,String root);
//
//
//	/**
//	 * 跟据图标ID批量删除图像
//	 * @param classId
//	 * @return
//	 */
//	public Integer removeImageByIds(Long domainId,Long[] ids);
//
//
//
//
//	/**
//	 * 重置指定租户下的3D映射关系
//	 * @param domainId
//	 */
//    public void reset3dMapping(Long domainId);
//
//
//
//
//
//
//
//
//
//
//
//}
// --注释掉检查 STOP (2024/5/17 10:21)
