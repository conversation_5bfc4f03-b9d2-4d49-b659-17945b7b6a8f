package com.uino.api.client.cmdb.rpc;

import java.util.Collection;
import java.util.List;
import java.util.Set;

import com.uino.dao.BaseConst;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClass;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uino.bean.cmdb.base.ESCIClassInfo;
import com.uino.bean.cmdb.business.ClassNodeInfo;
import com.uino.bean.cmdb.business.ClassReOrderDTO;
import com.uino.bean.cmdb.business.ImportResultMessage;
import com.uino.bean.cmdb.query.ESCIClassSearchBean;
import com.uino.provider.feign.cmdb.CIClassFeign;
import com.uino.api.client.cmdb.ICIClassApiSvc;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
public class CIClassApiSvcRpc implements ICIClassApiSvc {

    @Autowired
    private CIClassFeign classFeign;

    @Override
    public List<ClassNodeInfo> getClassTree() {
        return classFeign.getClassTree(BaseConst.DEFAULT_DOMAIN_ID);
    }

    @Override
    public List<ClassNodeInfo> getClassTree(Long domainId) {
        return classFeign.getClassTree(domainId);
    }

    @Override
    public ESCIClassInfo queryESClassInfoById(Long id) {
        return classFeign.queryESClassInfoById(id);
    }

    @Override
    public CcCiClassInfo queryClassInfoById(Long id) {
        return classFeign.queryClassById(id);
    }

    @Override
    public List<CcCiClassInfo> queryClassByCdt(CCcCiClass cdt) {
        if (cdt == null) {
            return classFeign.queryClassByCdtWithoutBody();
        }
        return classFeign.queryClassByCdt(cdt);
    }

    @Override
    public List<CcCiClassInfo> queryCiClassInfoList(Long domainId, CCcCiClass cdt, String orders, Boolean isAsc) {
        if (cdt == null) {
            return classFeign.queryCiClassInfoListWithoutBody(domainId, orders, isAsc);
        }
        return classFeign.queryCiClassInfoList(domainId, cdt, orders, isAsc);
    }

    @Override
    public List<CcCiClassInfo> queryCiClassInfoListBySearchBean(ESCIClassSearchBean bean) {
        return classFeign.queryCiClassInfoListBySearchBean(bean);
    }

    @Override
    public List<ESCIClassInfo> queryESCiClassInfoListBySearchBean(ESCIClassSearchBean bean) {
        return classFeign.queryESCiClassInfoListBySearchBean(bean);
    }

    @Override
	public Page<ESCIClassInfo> queryESCiClassInfoPageBySearchBean(ESCIClassSearchBean bean) {
		return classFeign.queryESCiClassInfoPageBySearchBean(bean);
	}

	@Override
    public Long saveOrUpdateCIClass(CcCiClassInfo record) {
        return classFeign.saveOrUpdateCIClass(record);
    }

    @Override
    public Long saveOrUpdateESCIClass(ESCIClassInfo record) {
        return classFeign.saveOrUpdateESCIClass(record);
    }

    @Override
    public Long saveOrUpdateESCIClassExtra(ESCIClassInfo record) {
        return null;
    }

    @Override
    public Integer saveOrUpdateBatch(Long domainId, List<CcCiClassInfo> clsInfos) {
        return classFeign.saveOrUpdateBatch(domainId,clsInfos);
    }

    @Override
    public Integer removeCIClassById(Long id) {
        return classFeign.removeCIClassById(id);
    }

    @Override
    public ImportResultMessage importCiClassAttr(MultipartFile file, Long ciClsId) {
        return classFeign.importCiClassAttr(file, ciClsId);
    }

    @Override
    public ResponseEntity<byte[]> exportClassAttrExcel(Set<Long> clsIds, Boolean isBatchTpl) {
        return classFeign.exportClassAttrExcel(clsIds, isBatchTpl);
    }

    @Override
    public boolean reOrder(ClassReOrderDTO reOrderDTO) {
        return classFeign.reOrder(reOrderDTO);
    }

    @Override
    public boolean initAllOrderNo() {
        return classFeign.initAllOrderNo(BaseConst.DEFAULT_DOMAIN_ID);
    }

    @Override
    public boolean initAllOrderNo(Long domainId) {
        return classFeign.initAllOrderNo(domainId);
    }

    @Override
    public List<ESCIClassInfo> getTargetAttrDefsByClassIds(Collection<Long> classIds) {
        return classFeign.getTargetAttrDefsByClassIds(BaseConst.DEFAULT_DOMAIN_ID, classIds);
    }

    @Override
    public List<ESCIClassInfo> getTargetAttrDefsByClassIds(Long domainId, Collection<Long> classIds) {
        return classFeign.getTargetAttrDefsByClassIds(domainId, classIds);
    }

    @Override
	public List<String> queryAttrDefGroupList() {
		return classFeign.queryAttrDefGroupList(BaseConst.DEFAULT_DOMAIN_ID);
	}

    @Override
    public List<String> queryAttrDefGroupList(Long domainId) {
        return classFeign.queryAttrDefGroupList(domainId);
    }

    @Override
    public String getHttpResourceSpace() {
        return classFeign.getHttpResourceSpace();
    }

    @Override
    public Boolean isShow3dAttribute() {
        return classFeign.isShow3dAttribute();
    }

    @Override
    public List<ESCIClassInfo> queryESClassInfoByIds(List<Long> classIds) {
        return null;
    }

    @Override
    public List<ESCIClassInfo> queryEsClassInfoByDtClassIds(List<String> dtClassIds, Long domainId) {
        return classFeign.queryEsClassInfoByDtClassIds(dtClassIds, domainId);
    }

}
