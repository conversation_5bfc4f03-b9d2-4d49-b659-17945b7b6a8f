package com.uino.cmdb.ci_rlt.mvc;

import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import com.alibaba.fastjson.JSON;
import com.binary.framework.web.RemoteResult;
import com.uino.StartBaseWebAppliaction;
import com.uino.dao.cmdb.ESCIRltSvc;
import com.uino.dao.cmdb.ESCISvc;
import com.uino.dao.cmdb.ESRltClassSvc;

import lombok.extern.slf4j.Slf4j;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = { StartBaseWebAppliaction.class }, webEnvironment = WebEnvironment.RANDOM_PORT)
@Slf4j
@ActiveProfiles("provider-local")
public class DelRltByCiIdTest {
	@Autowired
	private TestRestTemplate restTemplate;
	@MockBean
	private ESRltClassSvc esRltClassSvc;
	@MockBean
	private ESCISvc esCiSvc;
	@MockBean
	private ESCIRltSvc esCiRltSvc;
	private static String testUrl;

	@BeforeClass
	public static void initCls() {
		DelRltByCiIdTest.testUrl = "/cmdb/ciRlt/delRltByCiId";
	}

	@Before
	public void start() {
		Mockito.when(esCiRltSvc.deleteByQuery(Mockito.any(), Mockito.anyBoolean())).thenReturn(1);
	}

	@Test
	public void test01() {
		String responseBodyStr = restTemplate.postForObject(testUrl, 123, String.class);
		RemoteResult responseBody = JSON.parseObject(responseBodyStr, RemoteResult.class);
		Assert.assertTrue(responseBody.isSuccess());

	}
}
