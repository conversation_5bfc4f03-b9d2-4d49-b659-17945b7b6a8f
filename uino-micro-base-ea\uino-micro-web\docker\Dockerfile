FROM dk.uino.cn/java/java8:1.0
MAINTAINER uino
USER root
#WORKDIR /usr/local/uino
ADD uino-micro-web-deploy.tar /usr/local/
ADD docker_start.sh /usr/local/uino-micro-web/bin/
ADD application-provider-local.properties /usr/local/


RUN mv /usr/local/application-provider-local.properties /usr/local/uino-micro-web/conf/ \
	&& chmod -R 777 /usr/local/uino-micro-web/* 
CMD /usr/local/uino-micro-web/bin/docker_start.sh
