package com.uinnova.product.eam.db.bean;

import com.binary.framework.bean.annotation.Comment;

public class DiagramTagGroup {
	@Comment("分组的条件,视图tag的名字")
	private String tagName;
	@Comment("分组的条件,视图tag的id")
	private Long tagId;
	@Comment("视图数量")
	private Long diagramCount;
	
	@Comment("标签描述")
	private String defDesc;

	public String getDefDesc() {
		return defDesc;
	}

	public void setDefDesc(String defDesc) {
		this.defDesc = defDesc;
	}

	public String getTagName() {
		return tagName;
	}

	public void setTagName(String tagName) {
		this.tagName = tagName;
	}
	
	public Long getTagId() {
		return tagId;
	}

	public void setTagId(Long tagId) {
		this.tagId = tagId;
	}

	public Long getDiagramCount() {
		return diagramCount;
	}

	public void setDiagramCount(Long diagramCount) {
		this.diagramCount = diagramCount;
	}

}
