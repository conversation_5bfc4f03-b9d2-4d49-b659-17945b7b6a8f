package com.uinnova.product.eam.web.system.mvc;

import com.binary.framework.util.ControllerUtils;
import com.uinnova.product.vmdb.comm.doc.annotation.MvcDesc;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.core.LoggerContext;
import org.apache.logging.log4j.core.config.Configurator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 系统管理相关接口
 */
@Controller
@RequestMapping("/system/")
@MvcDesc(author="Shaolong.fan",desc="系统管理")
public class SystemManagerMvc {

	private final Logger logger = LoggerFactory.getLogger(getClass());

	@GetMapping("/log/changeLogLevel")
	public void changeLogLevel(HttpServletRequest request, HttpServletResponse response, @RequestParam("logLevel") String logLevel){
		LoggerContext ctx = (LoggerContext) LogManager.getContext(false);
		// 打印当前的日志级别
		logger.info("The current log level: "+(ctx.getConfiguration().getRootLogger().getLevel()));

		// 修改日志级别为 DEBUG
		Configurator.setLevel(ctx.getConfiguration().getRootLogger().getName(),
				org.apache.logging.log4j.Level.toLevel(logLevel));
		logger.info("Change log level to {}", logLevel);
		logger.info("The current log level after change: "+(ctx.getConfiguration().getRootLogger().getLevel()));

		logger.debug("This a debug log test");

		ControllerUtils.returnJson(request, response, "success");
	}
}
