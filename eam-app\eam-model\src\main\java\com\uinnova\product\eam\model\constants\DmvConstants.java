package com.uinnova.product.eam.model.constants;

/**
 * @description:
 * @author: Lc
 * @create: 2022-04-19 16:50
 */
public class DmvConstants {

    // 全景墙配置
    public static final String APP_WALL_CONFIG = "appSet";
    //分层分域配置
    public static final String DOMAIN_CONFIG = "domainSet";
    // 子系统配置
    public static final String SUB_SYS_CONFIG = "subAppConfig";
    // 应用系统配置
    public static final String APP_SYS_CONFIG = "appSysConfig";

    public static final String SUB_SYS = "子系统";

    public static final String APP_SYS = "应用系统";
    // 分层分域配置
    public static final String DOMAIN_SET_CONFIG = "domainSet";

    public static final String APP_CLASS = "appClass";

    public static final String LAYER_RELATION = "layerRelation";

    public static final String FIELD_RELATION = "fieldRelation";

    public static final String NAME = "name";
    // 全景墙配置视图
    public static final Integer APP_WALL_CONFIG_DIAGRAM = 1;
    // 分层分域配置视图
    public static final Integer DOMAIN_SET_CONFIG_DIAGRAM = 2;
    // 包含对象
    public static final Integer INCLUDE_OBJ = 1;
    // 被包含对象
    public static final Integer INCLUDED_OBJ = 2;
    // 层配置
    public static final String LAYER_CONFIG = "layerConfig";
    // 域配置
    public static final String LAYER_DOMAIN_CONFIG = "layerDomainConfig";
    // 层域应用配置
    public static final String LAYER_DOMAIN_APP_CONFIG = "layerDomainAppConfig";
}
