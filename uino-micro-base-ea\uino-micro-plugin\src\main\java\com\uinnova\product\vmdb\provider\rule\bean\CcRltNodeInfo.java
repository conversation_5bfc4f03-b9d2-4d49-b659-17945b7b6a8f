package com.uinnova.product.vmdb.provider.rule.bean;

import com.uinnova.product.vmdb.comm.model.rule.CcRltNode;
import com.uinnova.product.vmdb.comm.model.rule.CcRltNodeCdt;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;

import java.io.Serializable;
import java.util.List;

public class CcRltNodeInfo implements Serializable {
	private static final long serialVersionUID = 1L;
	
	
	
	/**
	 * 关系节点
	 */
	private CcRltNode node;


	/**
	 * 关系节点条件集合
	 */
	private List<CcRltNodeCdt> nodeCdts;


	/**
	 * 当前节点相关的信息
	 */
	private CcCiClassInfo classInfo;


	public CcRltNode getNode() {
		return node;
	}


	public void setNode(CcRltNode node) {
		this.node = node;
	}


	public List<CcRltNodeCdt> getNodeCdts() {
		return nodeCdts;
	}


	public void setNodeCdts(List<CcRltNodeCdt> nodeCdts) {
		this.nodeCdts = nodeCdts;
	}


	public CcCiClassInfo getClassInfo() {
		return classInfo;
	}


	public void setClassInfo(CcCiClassInfo classInfo) {
		this.classInfo = classInfo;
	}
	
	
	

}
