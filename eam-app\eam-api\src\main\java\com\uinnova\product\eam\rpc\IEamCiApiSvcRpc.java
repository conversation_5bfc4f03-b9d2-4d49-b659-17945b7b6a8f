package com.uinnova.product.eam.rpc;

import com.uinnova.product.eam.api.IEamCiApiSvc;
import com.uinnova.product.eam.feign.client.IEamCiFeign;
import com.uinnova.product.eam.model.CiQueryCdtExtend;
import com.uinnova.product.eam.model.VcCiClassInfoDto;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uino.bean.cmdb.base.LibType;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.List;

@Service
public class IEamCiApiSvcRpc implements IEamCiApiSvc {

    @Resource
    private IEamCiFeign iEamCiFeign;

    @Override
    public List<VcCiClassInfoDto> queryCiCountByClass(CiQueryCdtExtend cdt, LibType libType) {
        return iEamCiFeign.queryCiCountByClass(cdt,libType);
    }

    @Override
    public List<CcCiInfo> getCiInfoListByClassName(String ciClassName, LibType libType) {
        return iEamCiFeign.getCiInfoListByClassName(ciClassName,libType);
    }
}
