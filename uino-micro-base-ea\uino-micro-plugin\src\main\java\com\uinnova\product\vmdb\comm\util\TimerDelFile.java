package com.uinnova.product.vmdb.comm.util;

import com.binary.core.util.BinaryUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.nio.file.FileSystems;
import java.util.List;
import java.util.Timer;
import java.util.TimerTask;

/**
 * 定时删除文件任务?
 * 
 * <AUTHOR>
 *
 */
public class TimerDelFile {
	private static final Logger logger = LoggerFactory.getLogger(TimerDelFile.class);

	private String name;

	private List<String> files;

	private Integer retentionTimeHr;

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public List<String> getFiles() {
		return files;
	}

	public void setFiles(List<String> files) {
		this.files = files;
	}

	public Integer getRetentionTimeHr() {
		return retentionTimeHr;
	}

	/**
	 * 保留多少小时
	 * 
	 * @param retentionTimeHr
	 */
	public void setRetentionTimeHr(Integer retentionTimeHr) {
		this.retentionTimeHr = retentionTimeHr;
	}
	
	/**
	 * 
	 * @param name
	 * @param files
	 * @param retentionTimeHr 默认336小时(14天)
	 */
	public TimerDelFile(String name, List<String> files, Integer retentionTimeHr) {
		super();
		this.name = name;
		this.files = files;
		this.retentionTimeHr = retentionTimeHr;
		if (this.retentionTimeHr == null || this.retentionTimeHr < 0) {
			retentionTimeHr = 336;
		}
	}

	private final TimerTask monitorTask = new TimerTask() {

		@Override
		public void run() {
			try {
				doDel();
			} catch (Throwable t) {
				logger.error("extract performance failed!", t);
			}
		}

	};

	private final Object syncobj = new Object();
	private Timer timer;

	public void doDel() {
		Long delcurTime = System.currentTimeMillis() - (retentionTimeHr * 3600000);
		if (!BinaryUtils.isEmpty(files)) {
			for (String string : files) {
				logger.info("定时删除文件任务[" + name + "]执行删除目录[" + string + "]下超过[" + retentionTimeHr + "]小时的文件和文件夹");
				delFile(delcurTime,FileSystems.getDefault().getPath(string).toFile());
			}
		}
	}

	private void delFile(Long time, File file) {
		if (file.exists()) {
			if (file.isDirectory()) {
				File[] cFiles = file.listFiles();
				for (File cFile : cFiles) {
					delFile(time, cFile);
				}
				if (BinaryUtils.isEmpty(file.listFiles())) {
					file.delete();
				}
			} else {
				long lastModified = file.lastModified();
				if (lastModified < time) {
					file.delete();
				}
			}
		}
	}

	public void start() {
		synchronized (syncobj) {
			if (this.timer == null) {
				this.timer = new Timer(TimerDelFile.class.getName(), true);
				this.timer.schedule(this.monitorTask, 5000, 7200 * 1000);
			}
		}
	}

	public void stop() {
		synchronized (syncobj) {
			if (this.timer != null) {
				this.timer.cancel();
				this.timer = null;
			}
		}
	}

}
