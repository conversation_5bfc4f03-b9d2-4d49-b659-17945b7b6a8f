package com.uino.provider.feign.sys;

import com.uino.bean.sys.base.ESOperateLogModule;
import com.uino.provider.feign.config.BaseFeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 
 * <AUTHOR>
 *
 */
@Component
@FeignClient(name = "${feign-server-name:tarsier-platform}", path = "${feign-server-path:tarsier-platform}/feign/sys/opLogModule", configuration = {BaseFeignConfig.class})
public interface OperateLogModuleFeign {

    /**
     * 获取所有操作日志模块
     * @return
     */
    @PostMapping("getAll")
    public List<ESOperateLogModule> getAll();

    /**
     * 通过mvc路径获取Module信息
     * 如果有多个匹配，则匹配最长路径
     * @param mvcPath Mvc路径
     * @return 匹配到的Module信息
     */
    @PostMapping("getModuleInfoByMvc")
    public ESOperateLogModule getModuleInfoByMvc(@RequestBody String mvcPath);
}
