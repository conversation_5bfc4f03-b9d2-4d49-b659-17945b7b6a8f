package com.uinnova.product.eam.web.bm.mvc;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.binary.core.util.BinaryUtils;
import com.binary.framework.web.RemoteResult;
import com.uinnova.product.eam.model.CiQueryCdtExtend;
import com.uinnova.product.eam.model.VcCiClassInfoDto;
import com.uinnova.product.eam.model.vo.EamAnalyseCiVo;
import com.uinnova.product.eam.model.dto.AnalysePathDto;
import com.uinnova.product.eam.model.dto.DataAnalyzeBatch;
import com.uinnova.product.eam.model.enums.AnalyseLeaf;
import com.uinnova.product.eam.model.vo.EamAnalyseTableVo;
import com.uinnova.product.eam.web.bm.peer.BmDataAnalyzePeer;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 数据分析
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/eam/dataAnalyze")
public class BmDataAnalyzeMvc {

    @Resource
    BmDataAnalyzePeer bmDataAnalyzePeer;

    @PostMapping("/queryClassInfoByMetaModel")
    public RemoteResult queryClassInfoByMetaModel(@RequestBody CiQueryCdtExtend cdt) {

        List<VcCiClassInfoDto> classInfoList = bmDataAnalyzePeer.queryClassInfoByMetaModel(cdt);
        return new RemoteResult(classInfoList);
    }

    @PostMapping("/queryCIAndRltInfoByCiCode")
    public RemoteResult queryByCiCode(@RequestBody String query) {
        JSONObject queryJson = JSONObject.parseObject(query);
        String ciCode = queryJson.getString("ciCode");
        Integer position = queryJson.getInteger("position");
        Map<String, Object> result = bmDataAnalyzePeer.queryByCiCode(ciCode, position);
        return new RemoteResult(result);
    }

    @PostMapping("/queryPath")
    public RemoteResult queryPath(@RequestBody String body) {
        JSONObject json = JSONObject.parseObject(body);
        String ciCode = json.getString("ciCode");
        AnalysePathDto result = bmDataAnalyzePeer.queryPath(ciCode);
        return new RemoteResult(result.getDown());
    }

    @PostMapping("/queryByPath")
    public RemoteResult queryByPath(@RequestBody String body) {
        JSONObject json = JSONObject.parseObject(body);
        String ciCode = json.getString("ciCode");
        BinaryUtils.checkEmpty(ciCode, "ciCode");
        AnalyseLeaf leaf = json.getObject("leaf", AnalyseLeaf.class);
        BinaryUtils.checkEmpty(leaf, "leaf");
        List<String> path = json.getJSONArray("path").toJavaList(String.class);
        List<DataAnalyzeBatch> result = bmDataAnalyzePeer.queryByPath(ciCode, leaf, path);
        return new RemoteResult(result);
    }

    @PostMapping("/queryByCiCodes")
    public RemoteResult queryByCiCodes(@RequestBody List<String> ciCodes) {
        List<EamAnalyseCiVo> result = bmDataAnalyzePeer.queryByCiCodes(ciCodes);
        return new RemoteResult(result);
    }

    @PostMapping("/queryPathOneSide")
    public RemoteResult queryPathOneSide(@RequestBody String body) {
        JSONObject json = JSON.parseObject(body);
        String ciCode = json.getString("ciCode");
        BinaryUtils.checkEmpty(ciCode, "ciCode");
        AnalyseLeaf leaf = json.getObject("leaf", AnalyseLeaf.class);
        BinaryUtils.checkEmpty(leaf, "leaf");
        String rootCiCode = json.getString("rootCiCode");
        List<DataAnalyzeBatch> result = bmDataAnalyzePeer.queryPathOneSide(ciCode,leaf,rootCiCode);
        return new RemoteResult(result);
    }

    @PostMapping("/table")
    @ModDesc(desc = "表格获取", pDesc = "ciCode集合及rltCode集合", pType = JSONObject.class, rDesc = "表格数据", rType = ResponseEntity.class)
    public RemoteResult getTable(@RequestBody String jsonString) {
        JSONObject param = JSON.parseObject(jsonString);
        String root = param.getString("root");
        BinaryUtils.checkEmpty(root, "ciCode");
        List<String> ciCodeList = JSON.parseArray(param.getString("ciCodeList"), String.class);
        BinaryUtils.checkEmpty(ciCodeList, "ciCode");
        List<String> rltCodeList = JSON.parseArray(param.getString("rltCodeList"), String.class);
        List<EamAnalyseTableVo> result = bmDataAnalyzePeer.getTable(root, ciCodeList, rltCodeList);
        return new RemoteResult(result);
    }

    @PostMapping("/table/export")
    @ModDesc(desc = "表格导出", pDesc = "ciCode集合及rltCode集合", pType = JSONObject.class, rDesc = "excel", rType = ResponseEntity.class)
    public ResponseEntity<byte[]> export(@RequestBody String jsonString) {
        JSONObject param = JSON.parseObject(jsonString);
        String root = param.getString("root");
        BinaryUtils.checkEmpty(root, "ciCode");
        List<String> ciCodeList = JSON.parseArray(param.getString("ciCodeList"), String.class);
        BinaryUtils.checkEmpty(ciCodeList, "ciCode");
        List<String> rltCodeList = JSON.parseArray(param.getString("rltCodeList"), String.class);
        return bmDataAnalyzePeer.export(root, ciCodeList, rltCodeList);
    }
}
