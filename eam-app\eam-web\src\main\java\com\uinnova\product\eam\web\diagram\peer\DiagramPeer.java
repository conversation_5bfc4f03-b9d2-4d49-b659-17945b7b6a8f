package com.uinnova.product.eam.web.diagram.peer;

import com.uinnova.product.eam.comm.model.VcDiagramDir;
import com.uinnova.product.eam.web.diagram.bean.DiagramDirInfo;

/**
 * 视图相关操作接口
 */
public interface DiagramPeer {


    /**
     * 增加权限
     *
     * @param info 视图信息
     */
    void addDirageAuth(DiagramDirInfo info);

    /**
     * 通过ID查询目录信息
     *
     * @param id 文件夹ID
     * @return 文件夹信息
     */
    VcDiagramDir queryDirById(Long id);

}
