package com.uinnova.product.eam.service.cj.dao;

import com.uinnova.product.eam.model.cj.domain.TemplateType;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;

@Service
public class TemplateTypeDao extends AbstractESBaseDao<TemplateType,TemplateType> {
    @Override
    public String getIndex() {
        return "uino_cj_template_type";
    }

    @Override
    public String getType() {
        return getIndex();
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }
}
