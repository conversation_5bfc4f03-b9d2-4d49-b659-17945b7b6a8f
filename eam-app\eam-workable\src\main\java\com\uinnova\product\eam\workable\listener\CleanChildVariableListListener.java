package com.uinnova.product.eam.workable.listener;

import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.ExecutionListener;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 清理子流程变量监听器
 */
@Slf4j
@Component("cleanChildVariableListListener")
public class CleanChildVariableListListener implements ExecutionListener {

    @Override
    public void notify(DelegateExecution execution) {
        Map<String, Object> variables = execution.getVariables();
        variables.remove("childVariableList");
        execution.setVariables(variables);
    }
}
