package com.binary.core.i18n;


public abstract class LanguageResolver {

	/** 语方翻译器 **/
	private static LanguageTranslator translator = new DefaultTranslator();

	
	public static LanguageTranslator getTranslator() {
		return translator;
	}
	
	public static void setTranslator(LanguageTranslator translator) {
		if(translator == null) throw new IllegalArgumentException(" the translator is null argument! ");
		LanguageResolver.translator = translator;
	}
	
	/**
	 * 获取指定国际化语言
	 * @param languageCode 语言代码
	 * @return
	 */
	public static String trans(String languageCode) {
		return translator.trans(languageCode);
	}

	/**
	 * 获取指定国际化语言
	 * @param languageCode 语言代码
	 * @param jsonParams 语言中动态参数(JSON格式)
	 * @return
	 */
	public static String trans(String languageCode, String jsonParams) {
		return translator.trans(languageCode, jsonParams);
	}
	
	
	
	/**
	 * 获取指定国际化语言
	 * @param languageCode 语言代码
	 * @param params 语言中动态参数, Bean or Map
	 * @return
	 */
	public static String trans(String languageCode, Object params) {
		return translator.trans(languageCode, params);
	}
	
	
	
	

}
