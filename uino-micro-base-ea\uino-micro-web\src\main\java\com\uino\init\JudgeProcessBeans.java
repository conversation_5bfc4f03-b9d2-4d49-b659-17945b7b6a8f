package com.uino.init;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.uino.util.judge.BaseJudgeProcess;
import com.uino.util.judge.common.ContainStrJudge;
import com.uino.util.judge.common.MatchJudge;
import com.uino.util.judge.common.NotNullJudge;

import lombok.extern.slf4j.Slf4j;

@Configuration
@Slf4j
public class JudgeProcessBeans {
	{
		log.info("注册校验链");
	}

	/**
	 * 非法字符校验链(首先非空，其次不认转义符,其次不认加号) 01节点非空校验 02节点使用包含字符校验(只针对string)
	 * 03节点使用正则校验(只针对string) 暂时只提供了这三种校验，可以自己加，这里主要是当作一个demo参考如何使用该校验链
	 * BaseJudgeProcess注释中有使用说明(不嫌累的话参数校验可以做成一个一个node进行链校验解耦。。)
	 * 
	 * @return
	 */
	@Bean(name = "illegalCharacterValid")
	public BaseJudgeProcess illegalCharacterValid() {
		// 01 next 02 next 03,其实为了灵活性建议xml配置
		MatchJudge judge03 = new MatchJudge();
		judge03.setRegex("\\+");
		judge03.setExceptionMessage("+为非法字符，请检查请求或联系管理员");
		ContainStrJudge judge02 = new ContainStrJudge();
		judge02.setContainVal(".");
		judge02.setNextValidNode(judge03);
		judge02.setExceptionMessage(".为非法字符，请检查请求或联系管理员");
		NotNullJudge judge01 = new NotNullJudge();
		judge01.setNextValidNode(judge02);
		judge01.setExceptionMessage("未通过非空校验");
		return judge01;
	}
}
