package com.uinnova.product.eam.service.es;

import com.uinnova.product.eam.comm.model.es.FlowSystemApproveData;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;

/**
 * description
 *
 * <AUTHOR>
 * @since 2024/5/28 13:50
 */
@Service
public class FlowSystemApproveDataDao extends AbstractESBaseDao<FlowSystemApproveData,FlowSystemApproveData> {
    @Override
    public String getIndex() {
        return "uino_flow_system_approve_data";
    }

    @Override
    public String getType() {
        return "_doc";
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }
}
