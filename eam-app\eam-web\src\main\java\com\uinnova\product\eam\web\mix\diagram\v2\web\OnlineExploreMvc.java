package com.uinnova.product.eam.web.mix.diagram.v2.web;

import com.binary.core.exception.BinaryException;
import com.binary.framework.web.RemoteResult;
import com.binary.jdbc.Page;
import com.uinnova.product.eam.base.diagram.mix.model.TingJsDiagramCdt;
import com.uinnova.product.eam.base.diagram.mix.model.VcDiagramDir;
import com.uinnova.product.eam.base.diagram.model.DiagramShareLink;
import com.uinnova.product.eam.base.diagram.model.ESDiagram;
import com.uinnova.product.eam.base.diagram.model.ESDiagramDTO;
import com.uinnova.product.eam.service.diagram.OnlineExploreSvc;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Classname
 * @Description 在线开发mvc
 * <AUTHOR>
 * @Date 2021-09-23
 */
@Slf4j
@RestController
@RequestMapping("/explore")
public class OnlineExploreMvc {

    @Autowired
    private OnlineExploreSvc onlineExploreSvc;


    @RequestMapping("/getExploreKey")
    @ModDesc(desc = "获取指定视图的在线开发key", pDesc = "视图id", pType = VcDiagramDir.class, rDesc = "在线开发key", rType = Long.class)
    public RemoteResult getExploreKey(@RequestBody DiagramShareLink shareLink) {
        String diagramId = shareLink.getDiagramId();
        if (StringUtils.isEmpty(diagramId)) {
            throw new BinaryException("传参错误");
        }
        String exploreKey = onlineExploreSvc.getExploreKey(diagramId);
        return new RemoteResult(exploreKey);
    }

    @RequestMapping("/getDiagramByExploreKey")
    @ModDesc(desc = "根据在线开发key获取视图详情", pDesc = "在线开发key", pType = DiagramShareLink.class, rDesc = "视图详情", rType = Long.class)
    public RemoteResult getDiagramByExploreKey(@RequestBody String exploreKey) {
        ESDiagramDTO result = onlineExploreSvc.getDiagramByExploreKey(exploreKey);
        return new RemoteResult(result);
    }

    @RequestMapping("/queryData")
    @ModDesc(desc = "工作台-根据文件夹id查询其包含的视图和文件夹信息",
            pDesc = "查询参数",
            pType = TingJsDiagramCdt.class,
            rDesc = "展示TingJs用户视图信息",
            rType = Page.class)
    public RemoteResult queryData(@RequestBody TingJsDiagramCdt cdt) {
        Long dirId = cdt.getDirId();
        Integer dirType = cdt.getDirType();
        String like = cdt.getLike();
        Integer type = cdt.getType();
        Integer queryType = cdt.getQueryType();
        String orders = cdt.getOrders();
        Long mmdId = cdt.getMmdId();
        Page<ESDiagram> result = onlineExploreSvc.queryData(cdt.getPageNum(), cdt.getPageSize(), mmdId, dirId, like, type, dirType, queryType, null, orders);
        return new RemoteResult(result);
    }

    @PostMapping("/getImage")
    @ModDesc(desc = "根据base64生成缩略图", pDesc = "在线开发key", pType = DiagramShareLink.class, rDesc = "视图详情", rType = Long.class)
    public RemoteResult getImage(@RequestBody DiagramShareLink link) {
        String diagramId = link.getDiagramId();
        if (StringUtils.isEmpty(diagramId)) {
            throw new BinaryException("diagramId不能为空");
        }
        String content = link.getContent();
        if (StringUtils.isEmpty(content)) {
            throw new BinaryException("content不能为空");
        }
        String result = onlineExploreSvc.getImage(diagramId, content);
        return new RemoteResult(result);
    }
}
