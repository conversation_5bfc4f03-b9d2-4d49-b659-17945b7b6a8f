package com.uinnova.product.eam.service;

import com.binary.jdbc.Page;
import com.uinnova.product.eam.model.DepartmentDto;
import com.uinnova.product.eam.model.ObjectMovingDto;
import com.uinnova.product.eam.model.vo.OrganizationalSystemParamVo;
import com.uinnova.product.eam.model.vo.OrganizationalSystemVo;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.business.ImportExcelMessage;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 组织体系
 *
 * <AUTHOR>
 * @since 2024/7/9 上午10:11
 */
public interface OrganizationalSystemSvc {
    /**
     * 获取岗位名称
     * @return
     */
    String getPositionName();

    /**
     * 新增或修改组织体系
     */
    Boolean saveOrUpdate(OrganizationalSystemVo organizationalSystemVo);

    /**
     * 通过ciCode删除岗位
     * @param ciCode
     * @return
     */
    Boolean remove(String ciCode);

    /**
     * 通过部门code查询岗位信息
     * @param paramVo
     * @return
     */
    Page<ESCIInfo> findOrganizationalSystemList(OrganizationalSystemParamVo paramVo);

    Boolean movePosition(ObjectMovingDto objectMovingDto);

    List<Map<String, String>> getDetail(String ciCode);

    String exportPosition(HttpServletResponse response, String ciCode);

    List<DepartmentDto> getOrganizationalSystemTree();

    Page<ESCIInfo> findOrganizationalSystemListNew(OrganizationalSystemParamVo paramVo);

    ImportExcelMessage importPostExcelBatch(MultipartFile file, Long id);
}
