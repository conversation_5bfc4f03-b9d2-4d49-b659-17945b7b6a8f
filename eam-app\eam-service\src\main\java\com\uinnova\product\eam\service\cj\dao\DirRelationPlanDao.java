package com.uinnova.product.eam.service.cj.dao;

import com.uinnova.product.eam.model.cj.domain.DirRelationPlan;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;

/**
 * @description:
 * @author: Lc
 * @create: 2022-03-17 11:09
 */
@Component
public class DirRelationPlanDao extends AbstractESBaseDao<DirRelationPlan, DirRelationPlan> {

    @Override
    public String getIndex() {
        return "uino_cj_dir_relation_plan";
    }

    @Override
    public String getType() {
        return getIndex();
    }

    @PostConstruct
    public void init() {
        initIndex();
    }

}
