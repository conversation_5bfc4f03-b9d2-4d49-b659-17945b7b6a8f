package com.uino.bean.sys.base;

import java.io.Serializable;

import com.binary.framework.bean.annotation.Comment;
import com.uino.bean.permission.business.IValidDto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * ldap用户与我们的用户映射定义
 * 
 * <AUTHOR>
 *
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value="ldap用户与我们的用户映射定义",description = "ldap用户与我们的用户映射定义")
public class LdapUserMapping implements Serializable, IValidDto {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value="显示名",example = "user")
    @Comment("显示名")
    private String userName;

    @ApiModelProperty(value="电子邮件地址",example ="<EMAIL>")
    @Comment("电子邮件地址")
    private String emailAdress;

    @ApiModelProperty(value="手机号",example ="***********")
    @Comment("手机号")
    private String mobileNo;

    @ApiModelProperty(value="备注",example ="note" )
    @Comment("备注")
    private String notes;

    @ApiModelProperty(value="角色",example = "admin")
    @Comment("角色")
    private String roles;

    @Override
    public void valid() {}

}
