package com.uino.web.cmdb.mvc;

import java.util.List;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import com.uino.bean.permission.base.SysUser;
import com.uino.init.api.ApiResult;
import com.uino.init.api.ApiVersion;
import com.uino.util.sys.SysUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uinnova.product.vmdb.comm.doc.annotation.MvcDesc;
import com.uino.bean.cmdb.base.ESCiClassRlt;
import com.uino.bean.cmdb.business.ClassRltQueryDto;
import com.uino.api.client.cmdb.ICIClassRltApiSvc;
import org.springframework.web.bind.annotation.RestController;

/**
 * 分类间关系mvc
 * 
 * <AUTHOR>
 *
 */

@ApiVersion(1)
@Api(value = "分类间关系", tags = {"关系管理"})
@RestController
@RequestMapping("/cmdb/classRlt")
@MvcDesc(author = "zyj", desc = "分类关系")
public class ClassRltMvc {

    @Autowired
    private ICIClassRltApiSvc ciClassRltApi;

    @ApiOperation(value="不分页查询分类关系")
    @PostMapping("queryClassRlt")
    @ModDesc(desc = "不分页查询分类关系", pDesc = "查询条件", pType = ClassRltQueryDto.class, rDesc = "查询结果", rType = List.class)
    public ApiResult<List<ESCiClassRlt>> bindCiRlt(@RequestBody ClassRltQueryDto queryDto, HttpServletRequest request,
                               HttpServletResponse response) {
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        queryDto.setDomainId(currentUserInfo.getDomainId());
        List<ESCiClassRlt> res = ciClassRltApi.queryClassRlt(queryDto);
        return ApiResult.ok(this).data(res);
    }

    @ApiOperation(value="分页查询分类关系")
    @PostMapping("queryClassRltPage")
    @ModDesc(desc = "分页查询分类关系", pDesc = "查询条件", pType = ClassRltQueryDto.class, rDesc = "查询结果", rType = Page.class)
    public ApiResult<Page<ESCiClassRlt>> queryClassRltPage(@RequestBody ClassRltQueryDto queryDto, HttpServletRequest request,
            HttpServletResponse response) {
        queryDto = queryDto == null ? new ClassRltQueryDto() : queryDto;
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        queryDto.setDomainId(currentUserInfo.getDomainId());
        Page<ESCiClassRlt> res = ciClassRltApi.queryClassRltPage(queryDto);
        return ApiResult.ok(this).data(res);
    }

	@ApiOperation(value="删除分类关系")
    @PostMapping("deleteClassRlt")
	@ModDesc(desc = "删除分类关系", pDesc = "条件", pType = ESCiClassRlt.class, rDesc = "是否删除成功", rType = Boolean.class)
	public ApiResult<Boolean> deleteClassRlt(@RequestBody ESCiClassRlt rlt, HttpServletRequest request,
			HttpServletResponse response) {
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        rlt.setDomainId(currentUserInfo.getDomainId());
        ciClassRltApi.deleteClassRlt(rlt);
        return ApiResult.ok(this).data(true);
	}
}
