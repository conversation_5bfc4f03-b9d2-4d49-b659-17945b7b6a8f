package com.uinnova.product.eam.service.es;

import com.uinnova.product.eam.comm.model.es.ClassSetting;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;

/**
 * <AUTHOR>
 */
@Service
public class EamClassSettingDao extends AbstractESBaseDao<ClassSetting, ClassSetting>  {

    @Override
    public String getIndex() {
        return "uino_eam_class_setting";
    }

    @Override
    public String getType() {
        return getIndex();
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }
}
