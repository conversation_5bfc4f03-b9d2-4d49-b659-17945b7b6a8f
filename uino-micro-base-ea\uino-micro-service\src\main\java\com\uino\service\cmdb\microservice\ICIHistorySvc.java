package com.uino.service.cmdb.microservice;

import java.util.List;
import java.util.Map;

import com.binary.jdbc.Page;
import com.uino.bean.cmdb.base.ESCIHistoryInfo;
import com.uino.bean.cmdb.base.ESCIHistoryInfo.ActionType;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.query.ESCIHistorySearchBean;

public interface ICIHistorySvc {


    void delAll(List<Long> classIds);


    /**
     * 保存CI历史信息
     * 
     * @param ciInfo
     * @param action 0=新增或修改，1=删除
     * @return
     */
    public Long saveOrUpdate(ESCIInfo ciInfo, ActionType action);

    /**
     * 批量保存CI历史信息
     * 
     * @param ciInfos
     * @param action 0=新增或修改，1=删除
     * @return
     */
    public Integer saveOrUpdateBatch(List<ESCIInfo> ciInfos, ActionType action);

    /**
     * 根据id及版本号获取对应版本CI数据
     * 
     * @param ciCode
     * @param classId
     * @param version
     * @return
     */
    public ESCIHistoryInfo getCIInfoHistoryByCIVersion(String ciCode, Long classId, Long version);

    /**
     * 根据CI id获取CI历史记录列表
     * 
     * @param ciCode
     * @param classId
     * @return
     */
    public List<ESCIHistoryInfo> getCIInfoHistoryByCICode(String ciCode, Long classId);

    /**
     * 条件查询CI历史数据
     * 
     * @param bean
     * @return
     */
    public Page<ESCIHistoryInfo> getCIHistoryPageBySearchBean(ESCIHistorySearchBean bean);

    /**
     * 根据CI的id查询CI历史版本与时间对应关系
     * 
     * @param ciCode
     * @param classId
     * @return
     */
    public List<String> getCIVersionList(String ciCode, Long classId);

    List<ESCIHistoryInfo> bathGetCIInfoHistoryByCIVersion(Map<String, Long> ciCodeVersionMap);
}
