package com.uinnova.product.eam.service.cj.service;

import com.binary.jdbc.Page;
import com.uinnova.product.eam.model.cj.domain.DeliverableTemplate;
import com.uinnova.product.eam.model.cj.domain.TemplateType;
import com.uinnova.product.eam.model.cj.vo.*;
import com.uinnova.product.eam.model.dmv.ClassDefinitionVO;

import java.util.List;

public interface DeliverableTemplateService {

    Long saveOrUpdateDlvrTemplate(DeliverableTemplate cdt);

    Page<DlvrTemplateVO> getDlvrTemplateList(DlvrTemplateReq queryBean);

    DlvrTemplateVO getDlvrTemplateById(Long id);

    Long createOrUpdateTemplateType(List<TemplateType> templateTypes);

    List<TemplateType> getListTemplateType();

    Boolean deleteTemplateType(Long id);

    Boolean validDelete(Long id);

    List<DlvrTemplateDTO> getReleaseTemplateList(DlvrTemplateReq dlvrTemplateReq);

    Boolean deleteDraftTemplate(Long templateId);

    List<TemplateType> getListShowTemplateType();

    /**
     * 交付物模板复制
     * @param templateId
     * @return
     */
    Boolean copyTemplateInfoById(Long templateId, String templateName);


    List<DeliverableTemplate> getPublishTemplateInfos();

    /**
     * 通过模板关联的系统
     * 筛选获取表格所有分类
     * @return
     */
    List<TableContentTypeVo> findTableContentTypeList(Long templateId);

    /**
     * 分类定义列表
     * @param templateId
     * @return
     */
    List<ClassDefinitionVO> findClassDefinitionList(Long templateId);

    List<PlanTemplateChapterTreeVo> getTemplateChapterList(Long templateId);

    /**
     * 获取方案模板信息
     * @param id
     * @return
     */
    DeliverableTemplate getDeliverableTemplateById(Long id);
}
