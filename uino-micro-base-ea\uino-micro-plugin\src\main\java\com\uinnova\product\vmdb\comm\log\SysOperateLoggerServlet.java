package com.uinnova.product.vmdb.comm.log;

import com.binary.sso.client.web.SsoOauthServlet;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

/**
 *
 * <AUTHOR>
 *
 */
public class SysOperateLoggerServlet extends SsoOauthServlet {
    private static final long serialVersionUID = 1L;

    @Override
    public void afterWrite(HttpServletRequest request, HttpServletResponse response, Object result) {
        SysOperateLogger.pushLog(request, result);
    }

}
