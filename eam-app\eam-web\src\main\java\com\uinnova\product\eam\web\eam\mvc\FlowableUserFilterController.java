package com.uinnova.product.eam.web.eam.mvc;

import cn.hutool.core.lang.Assert;
import com.binary.framework.util.ControllerUtils;
import com.uinnova.product.eam.comm.model.es.FlowableUser;
import com.uinnova.product.eam.feign.client.FlowableUserFilterFeign;
import com.uinnova.product.eam.service.IFlowableUserFilterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/flowable")
public class FlowableUserFilterController implements FlowableUserFilterFeign {

    @Autowired
    private IFlowableUserFilterService flowableUserFilterService;

    /**
     * 添加当前任务审批人同意信息
     * @param request
     * @param response
     * @param flowableUser
     */
    @Override
    @PostMapping("/addAgreeUser")
    public void addAgreeUser(HttpServletRequest request, HttpServletResponse response, @RequestBody FlowableUser flowableUser) {
        Long result = flowableUserFilterService.addAgreeUser(flowableUser);
        ControllerUtils.returnJson(request,response,result);
    }

    /**
     * 查看当前任务所有同意审批的人
     * @param request
     * @param response
     * @param flowableUser
     */
    @Override
    @PostMapping("/getAgreeUserList")
    public void getAgreeUserList(HttpServletRequest request, HttpServletResponse response, @RequestBody FlowableUser flowableUser) {
        Assert.isTrue(flowableUser.getProcessInstanceId()!=null,"查询的流程实例id不能为空");
        Assert.isTrue(flowableUser.getProcessTaskKey()!=null,"查询的当前任务key不能为空");
        List<FlowableUser> result = flowableUserFilterService.getAgreeUserList(flowableUser);
        ControllerUtils.returnJson(request,response,result);
    }


}
