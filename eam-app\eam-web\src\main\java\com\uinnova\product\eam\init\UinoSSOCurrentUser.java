package com.uinnova.product.eam.init;

import cn.hutool.http.Header;
import com.uinnova.product.eam.base.diagram.utils.RedisUtil;
import com.uino.bean.permission.base.SysUser;
import com.uino.util.sys.CurrentUserGetter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;

/**
 * @Description 自定义获取当前用户方法
 * <AUTHOR>
 * @Date 2021-08-27-17:27
 * @version 1.0
 */
@Component
@Slf4j
@ConditionalOnProperty(prefix = "monet.login",name = "loginMethod",havingValue = "sso")
public class UinoSSOCurrentUser implements CurrentUserGetter {

    @Autowired
    private RedisUtil redisUtil;



    /**
     * <AUTHOR>
     * @Description 获取当前用户的登录信息
     * @Date 17:30 2021/8/27
     * @Param []
     *
     * @return*/
    @Override
    public SysUser getCurrentUserInfo() {
        //已校验的用户无需再次校验
        ServletRequestAttributes attrs = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        assert attrs != null;
        HttpServletRequest request;
        String authorization = "";
        //String cookieVal = "";
        try {
            request = attrs.getRequest();
            authorization = request.getHeader(Header.AUTHORIZATION.getValue());
            /*Cookie[] cookies = request.getCookies();
            if (!BinaryUtils.isEmpty(cookies)) {
                for (Cookie cookie : cookies) {
                    if (cookie.getName().equals("monet")) {
                        cookieVal = cookie.getValue();
                        break;
                    }
                }
            }
            if ("".equals(cookieVal)) {
                cookieVal = request.getHeader(Header.AUTHORIZATION.getValue());
            } else {
                cookieVal = "Bearer " + cookieVal;
            }*/
        } catch (NullPointerException exception) {
            return null;
        }
        if ("".equals(authorization)) {
            return null;
        }
        Object userObj = redisUtil.get(authorization);
        if (userObj != null) {
            return (SysUser) userObj;
        } else {
            return null;
        }
    }

    @Override
    public SysUser getCurrentUserInfo(String token) {
        return getCurrentUserInfo();
    }

    @Override
    public String getCurrentToken() {
        RequestAttributes requestAttributes = RequestContextHolder
                .getRequestAttributes();

        if(requestAttributes==null){
            return null;
        }
        ServletRequestAttributes attrs = (ServletRequestAttributes) requestAttributes;
        // Authorization
        String token;
        token = attrs.getRequest().getHeader("Authorization");
        if (token != null) {
            token = token.split(" ")[1];
        } else {
            token = attrs.getRequest().getParameter("access_token");
            if (token == null) {
                token = attrs.getRequest().getParameter("tk");
            }
        }
        return token;
    }
}


