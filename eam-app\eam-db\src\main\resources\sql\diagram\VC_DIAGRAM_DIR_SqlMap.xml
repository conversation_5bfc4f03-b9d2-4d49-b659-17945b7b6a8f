<?xml version="1.0" encoding="UTF-8"?><!--Converted at: Sat Dec 08 16:22:34 CST 2018-->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="VC_DIAGRAM_DIR">


    <resultMap id="queryResult" type="com.uinnova.product.eam.comm.model.VcDiagramDir">
        <result property="id" column="ID" jdbcType="BIGINT"/>    <!-- ID -->
        <result property="dirName" column="DIR_NAME" jdbcType="VARCHAR"/>    <!-- 目录名称 -->
        <result property="dirType" column="DIR_TYPE" jdbcType="INTEGER"/>    <!-- 目录类型 -->
        <result property="parentId" column="PARENT_ID" jdbcType="BIGINT"/>    <!-- 上级目录ID -->
        <result property="userId" column="USER_ID" jdbcType="BIGINT"/>    <!-- 所属用户ID -->
        <result property="dirLvl" column="DIR_LVL" jdbcType="INTEGER"/>    <!-- 目录层级级别 -->
        <result property="dirPath" column="DIR_PATH" jdbcType="VARCHAR"/>    <!-- 目录层级路径 -->
        <result property="orderNo" column="ORDER_NO" jdbcType="INTEGER"/>    <!-- 显示排序 -->
        <result property="isLeaf" column="IS_LEAF" jdbcType="INTEGER"/>    <!-- 是否末级 -->
        <result property="icon" column="ICON" jdbcType="VARCHAR"/>    <!-- 目录图标 -->
        <result property="dirDesc" column="DIR_DESC" jdbcType="VARCHAR"/>    <!-- 目录描述 -->
        <result property="domainId" column="DOMAIN_ID" jdbcType="BIGINT"/>    <!-- 所属域 -->
        <result property="dataStatus" column="DATA_STATUS" jdbcType="INTEGER"/>    <!-- 数据状态 -->
        <result property="creator" column="CREATOR" jdbcType="VARCHAR"/>    <!-- 创建人 -->
        <result property="modifier" column="MODIFIER" jdbcType="VARCHAR"/>    <!-- 修改人 -->
        <result property="createTime" column="CREATE_TIME" jdbcType="BIGINT"/>    <!-- 创建时间 -->
        <result property="modifyTime" column="MODIFY_TIME" jdbcType="BIGINT"/>    <!-- 更新时间 -->
        <result property="subjectId" column="SUBJECT_ID" jdbcType="BIGINT"/>    <!-- 主题所属目录 -->
        <result property="oldParentId" column="OLD_PARENT_ID" jdbcType="BIGINT"/>    <!-- 老父类id -->
        <result property="dirInit" column="DIR_INIT" jdbcType="INTEGER"/>    <!-- 是否初始目录  1=是，0=否 -->
        <result property="esSysId" column="ES_SYS_ID" jdbcType="VARCHAR"/>
        <result property="sysType" column="SYS_TYPE" jdbcType="VARCHAR"/>
        <result property="sysDir" column="SYS_DIR" jdbcType="INTEGER"/>
    </resultMap>


    <sql id="sql_query_where">
        <if test="cdt != null and cdt.id != null">and
            ID = #{cdt.id:BIGINT}
        </if>
        <if test="ids != null and ids != ''">and
            ID in (${ids})
        </if>
        <if test="cdt != null and cdt.startId != null">and
            ID &gt;= #{cdt.startId:BIGINT}
        </if>
        <if test="cdt != null and cdt.endId != null">and
            ID &lt;= #{cdt.endId:BIGINT}
        </if>
        <if test="cdt != null and cdt.dirName != null and cdt.dirName != ''">and
            DIR_NAME like #{cdt.dirName,jdbcType=VARCHAR}
        </if>
        <if test="cdt != null and cdt.dirNameEqual != null and cdt.dirNameEqual != ''">and
            DIR_NAME = #{cdt.dirNameEqual,jdbcType=VARCHAR}
        </if>
        <if test="dirNames != null and dirNames != ''">and
            DIR_NAME in (${dirNames})
        </if>
        <if test="cdt != null and cdt.dirType != null">and
            DIR_TYPE = #{cdt.dirType:INTEGER}
        </if>
        <if test="dirTypes != null and dirTypes != ''">and
            DIR_TYPE in (${dirTypes})
        </if>
        <if test="cdt != null and cdt.startDirType != null">and
            DIR_TYPE &gt;= #{cdt.startDirType:INTEGER}
        </if>
        <if test="cdt != null and cdt.endDirType != null">and
            DIR_TYPE &lt;= #{cdt.endDirType:INTEGER}
        </if>
        <if test="cdt != null and cdt.parentId != null">and
            PARENT_ID = #{cdt.parentId:BIGINT}
        </if>
        <if test="parentIds != null and parentIds != ''">and
            PARENT_ID in (${parentIds})
        </if>
        <if test="cdt != null and cdt.startParentId != null">and
            PARENT_ID &gt;= #{cdt.startParentId:BIGINT}
        </if>
        <if test="cdt != null and cdt.endParentId != null">and
            PARENT_ID &lt;= #{cdt.endParentId:BIGINT}
        </if>
        <if test="cdt != null and cdt.userId != null">and
            USER_ID = #{cdt.userId:BIGINT}
        </if>
        <if test="userIds != null and userIds != ''">and
            USER_ID in (${userIds})
        </if>
        <if test="cdt != null and cdt.startUserId != null">and
            USER_ID &gt;= #{cdt.startUserId:BIGINT}
        </if>
        <if test="cdt != null and cdt.endUserId != null">and
            USER_ID &lt;= #{cdt.endUserId:BIGINT}
        </if>
        <if test="cdt != null and cdt.dirLvl != null">and
            DIR_LVL = #{cdt.dirLvl:INTEGER}
        </if>
        <if test="dirLvls != null and dirLvls != ''">and
            DIR_LVL in (${dirLvls})
        </if>
        <if test="cdt != null and cdt.startDirLvl != null">and
            DIR_LVL &gt;= #{cdt.startDirLvl:INTEGER}
        </if>
        <if test="cdt != null and cdt.endDirLvl != null">and
            DIR_LVL &lt;= #{cdt.endDirLvl:INTEGER}
        </if>
        <if test="cdt != null and cdt.dirPath != null and cdt.dirPath != ''">and
            DIR_PATH like #{cdt.dirPath,jdbcType=VARCHAR}
        </if>
        <if test="cdt != null and cdt.orderNo != null">and
            ORDER_NO = #{cdt.orderNo:INTEGER}
        </if>
        <if test="orderNos != null and orderNos != ''">and
            ORDER_NO in (${orderNos})
        </if>
        <if test="cdt != null and cdt.startOrderNo != null">and
            ORDER_NO &gt;= #{cdt.startOrderNo:INTEGER}
        </if>
        <if test="cdt != null and cdt.endOrderNo != null">and
            ORDER_NO &lt;= #{cdt.endOrderNo:INTEGER}
        </if>
        <if test="cdt != null and cdt.isLeaf != null">and
            IS_LEAF = #{cdt.isLeaf:INTEGER}
        </if>
        <if test="isLeafs != null and isLeafs != ''">and
            IS_LEAF in (${isLeafs})
        </if>
        <if test="cdt != null and cdt.startIsLeaf != null">and
            IS_LEAF &gt;= #{cdt.startIsLeaf:INTEGER}
        </if>
        <if test="cdt != null and cdt.endIsLeaf != null">and
            IS_LEAF &lt;= #{cdt.endIsLeaf:INTEGER}
        </if>
        <if test="cdt != null and cdt.icon != null and cdt.icon != ''">and
            ICON like #{cdt.icon,jdbcType=VARCHAR}
        </if>
        <if test="cdt != null and cdt.dirDesc != null and cdt.dirDesc != ''">and
            DIR_DESC like #{cdt.dirDesc,jdbcType=VARCHAR}
        </if>
        <if test="cdt != null and cdt.domainId != null">and
            DOMAIN_ID = #{cdt.domainId:BIGINT}
        </if>
        <if test="domainIds != null and domainIds != ''">and
            DOMAIN_ID in (${domainIds})
        </if>
        <if test="cdt != null and cdt.startDomainId != null">and
            DOMAIN_ID &gt;= #{cdt.startDomainId:BIGINT}
        </if>
        <if test="cdt != null and cdt.endDomainId != null">and
            DOMAIN_ID &lt;= #{cdt.endDomainId:BIGINT}
        </if>
        <if test="cdt != null and cdt.dataStatus != null">and
            DATA_STATUS = #{cdt.dataStatus:INTEGER}
        </if>
        <if test="dataStatuss != null and dataStatuss != ''">and
            DATA_STATUS in (${dataStatuss})
        </if>
        <if test="cdt != null and cdt.startDataStatus != null">and
            DATA_STATUS &gt;= #{cdt.startDataStatus:INTEGER}
        </if>
        <if test="cdt != null and cdt.endDataStatus != null">and
            DATA_STATUS &lt;= #{cdt.endDataStatus:INTEGER}
        </if>
        <if test="cdt != null and cdt.creator != null and cdt.creator != ''">and
            CREATOR like #{cdt.creator,jdbcType=VARCHAR}
        </if>
        <if test="cdt != null and cdt.creatorEqual != null and cdt.creatorEqual != ''">and
            CREATOR = #{cdt.creatorEqual,jdbcType=VARCHAR}
        </if>
        <if test="creators != null and creators != ''">and
            CREATOR in (${creators})
        </if>
        <if test="cdt != null and cdt.modifier != null and cdt.modifier != ''">and
            MODIFIER like #{cdt.modifier,jdbcType=VARCHAR}
        </if>
        <if test="cdt != null and cdt.modifierEqual != null and cdt.modifierEqual != ''">and
            MODIFIER = #{cdt.modifierEqual,jdbcType=VARCHAR}
        </if>
        <if test="modifiers != null and modifiers != ''">and
            MODIFIER in (${modifiers})
        </if>
        <if test="cdt != null and cdt.createTime != null">and
            CREATE_TIME = #{cdt.createTime:BIGINT}
        </if>
        <if test="createTimes != null and createTimes != ''">and
            CREATE_TIME in (${createTimes})
        </if>
        <if test="cdt != null and cdt.startCreateTime != null">and
            CREATE_TIME &gt;= #{cdt.startCreateTime:BIGINT}
        </if>
        <if test="cdt != null and cdt.endCreateTime != null">and
            CREATE_TIME &lt;= #{cdt.endCreateTime:BIGINT}
        </if>
        <if test="cdt != null and cdt.modifyTime != null">and
            MODIFY_TIME = #{cdt.modifyTime:BIGINT}
        </if>
        <if test="modifyTimes != null and modifyTimes != ''">and
            MODIFY_TIME in (${modifyTimes})
        </if>
        <if test="cdt != null and cdt.startModifyTime != null">and
            MODIFY_TIME &gt;= #{cdt.startModifyTime:BIGINT}
        </if>
        <if test="cdt != null and cdt.endModifyTime != null">and
            MODIFY_TIME &lt;= #{cdt.endModifyTime:BIGINT}
        </if>
        <if test="cdt != null and cdt.subjectId != null">and
            SUBJECT_ID = #{cdt.subjectId:BIGINT}
        </if>
        <if test="cdt != null and cdt.oldParentId != null">and
            OLD_PARENT_ID = #{cdt.oldParentId:BIGINT}
        </if>
        <if test="cdt != null and cdt.dirInit != null">and
            DIR_INIT = #{cdt.dirInit:INTEGER}
        </if>
        <if test="cdt != null and cdt.esSysId != null">and
            ES_SYS_ID = #{cdt.esSysId,jdbcType=VARCHAR}
        </if>
        <if test="cdt != null and cdt.sysType != null">and
            SYS_TYPE = #{cdt.sysType,jdbcType=VARCHAR}
        </if>
        <if test="cdt != null and cdt.sysDir != null">and
            SYS_DIR = #{cdt.sysDir:INTEGER}
        </if>
    </sql>


    <sql id="sql_update_columns">
        <if test="record != null and record.id != null">
            ID = #{record.id:BIGINT}
            ,
        </if>
        <if test="record != null and record.dirName != null">
            DIR_NAME = #{record.dirName,jdbcType=VARCHAR}
            ,
        </if>
        <if test="record != null and record.dirType != null">
            DIR_TYPE = #{record.dirType:INTEGER}
            ,
        </if>
        <if test="record != null and record.parentId != null">
            PARENT_ID = #{record.parentId:BIGINT}
            ,
        </if>
        <if test="record != null and record.userId != null">
            USER_ID = #{record.userId:BIGINT}
            ,
        </if>
        <if test="record != null and record.dirLvl != null">
            DIR_LVL = #{record.dirLvl:INTEGER}
            ,
        </if>
        <if test="record != null and record.dirPath != null">
            DIR_PATH = #{record.dirPath,jdbcType=VARCHAR}
            ,
        </if>
        <if test="record != null and record.orderNo != null">
            ORDER_NO = #{record.orderNo:INTEGER}
            ,
        </if>
        <if test="record != null and record.isLeaf != null">
            IS_LEAF = #{record.isLeaf:INTEGER}
            ,
        </if>
        <if test="record != null and record.icon != null">
            ICON = #{record.icon,jdbcType=VARCHAR}
            ,
        </if>
        <if test="record != null and record.dirDesc != null">
            DIR_DESC = #{record.dirDesc,jdbcType=VARCHAR}
            ,
        </if>
        <if test="record != null and record.domainId != null">
            DOMAIN_ID = #{record.domainId:BIGINT}
            ,
        </if>
        <if test="record != null and record.dataStatus != null">
            DATA_STATUS = #{record.dataStatus:INTEGER}
            ,
        </if>
        <if test="record != null and record.creator != null">
            CREATOR = #{record.creator,jdbcType=VARCHAR}
            ,
        </if>
        <if test="record != null and record.modifier != null">
            MODIFIER = #{record.modifier,jdbcType=VARCHAR}
            ,
        </if>
        <if test="record != null and record.createTime != null">
            CREATE_TIME = #{record.createTime:BIGINT}
            ,
        </if>
        <if test="record != null and record.modifyTime != null">
            MODIFY_TIME = #{record.modifyTime:BIGINT}
            ,
        </if>
        <if test="record != null and record.subjectId != null">
            SUBJECT_ID = #{record.subjectId:BIGINT}
            ,
        </if>
        <if test="record != null and record.oldParentId != null">
            OLD_PARENT_ID = #{record.oldParentId:BIGINT}
            ,
        </if>
        <if test="record != null and record.dirInit != null">
            DIR_INIT = #{record.dirInit:INTEGER}
            ,
        </if>
        <if test="record != null and record.esSysId != null">
            ES_SYS_ID = #{record.esSysId,jdbcType=VARCHAR}
            ,
        </if>
        <if test="record != null and record.sysType != null">
            SYS_TYPE = #{record.sysType,jdbcType=VARCHAR}
            ,
        </if>
        <if test="record != null and record.sysDir != null">and
            SYS_DIR = #{record.sysDir:INTEGER}
            ,
        </if>
    </sql>


    <sql id="sql_query_columns">
        ID
        , DIR_NAME, DIR_TYPE, PARENT_ID, USER_ID, DIR_LVL,
		DIR_PATH, ORDER_NO, IS_LEAF, ICON, DIR_DESC, DOMAIN_ID, 
		DATA_STATUS, CREATOR, MODIFIER, CREATE_TIME, MODIFY_TIME,
		SUBJECT_ID, DIR_INIT, ES_SYS_ID, SYS_TYPE, SYS_DIR
    </sql>


    <select id="selectList" parameterType="java.util.Map" resultMap="queryResult">
        select
        <include refid="VC_DIAGRAM_DIR.sql_query_columns"/>
        from VC_DIAGRAM_DIR
        <where>
            <include refid="VC_DIAGRAM_DIR.sql_query_where"/>
        </where>
        order by
        <if test="orders != null and orders != ''">
            ${orders}
        </if>
        <if test="orders == null or orders == ''">
            ID
        </if>
    </select>
    <select id="selectCount" parameterType="java.util.Map" resultType="java.lang.Long">
        select count(1) from VC_DIAGRAM_DIR
        <where>
            <include refid="VC_DIAGRAM_DIR.sql_query_where"/>
        </where>
    </select>
    <select id="selectById" parameterType="java.util.Map" resultMap="queryResult">
        select
        <include refid="VC_DIAGRAM_DIR.sql_query_columns"/>
        from VC_DIAGRAM_DIR where ID=#{id:BIGINT} and DATA_STATUS=1
    </select>


    <insert id="insert" parameterType="java.util.Map">
        insert into VC_DIAGRAM_DIR(ID, DIR_NAME, DIR_TYPE, PARENT_ID, USER_ID,
                                   DIR_LVL, DIR_PATH, ORDER_NO, IS_LEAF, ICON,
                                   DIR_DESC, DOMAIN_ID, DATA_STATUS, CREATOR, MODIFIER,
                                   CREATE_TIME, MODIFY_TIME, SUBJECT_ID, DIR_INIT, ES_SYS_ID, SYS_TYPE, SYS_DIR)
        values (#{record.id:BIGINT}, #{record.dirName,jdbcType=VARCHAR}, #{record.dirType:INTEGER},
                #{record.parentId:BIGINT}, #{record.userId:BIGINT},
                #{record.dirLvl:INTEGER}, #{record.dirPath,jdbcType=VARCHAR}, #{record.orderNo:INTEGER},
                #{record.isLeaf:INTEGER}, #{record.icon,jdbcType=VARCHAR},
                #{record.dirDesc,jdbcType=VARCHAR}, #{record.domainId:BIGINT}, #{record.dataStatus:INTEGER},
                #{record.creator,jdbcType=VARCHAR}, #{record.modifier,jdbcType=VARCHAR},
                #{record.createTime:BIGINT}, #{record.modifyTime:BIGINT}, #{record.subjectId:BIGINT},
                #{record.dirInit:INTEGER}, #{record.esSysId,jdbcType=VARCHAR}, #{record.sysType, jdbcType=VARCHAR},
                #{record.sysDir:INTEGER})
    </insert>


    <update id="updateById" parameterType="java.util.Map">
        update VC_DIAGRAM_DIR
        <set>
            <include refid="VC_DIAGRAM_DIR.sql_update_columns"/>
        </set>
        where ID = #{id:BIGINT}
    </update>
    <update id="updateByCdt" parameterType="java.util.Map">
        update VC_DIAGRAM_DIR
        <set>
            <include refid="VC_DIAGRAM_DIR.sql_update_columns"/>
        </set>
        <where>
            <include refid="VC_DIAGRAM_DIR.sql_query_where"/>
        </where>
    </update>


    <delete id="deleteById" parameterType="java.util.Map">
        delete
        from VC_DIAGRAM_DIR
        where ID = #{id:BIGINT}
    </delete>
    <delete id="deleteByCdt" parameterType="java.util.Map">
        delete from VC_DIAGRAM_DIR
        <where>
            <include refid="VC_DIAGRAM_DIR.sql_query_where"/>
        </where>
    </delete>


    <select id="getDiagramDirByEsSysId" parameterType="java.lang.String" resultMap="queryResult">
        SELECT
        <include refid="sql_query_columns"/>
        FROM VC_DIAGRAM_DIR
        WHERE ES_SYS_ID = #{esSysId}
    </select>

    <select id="findDirIdListByEsSysIdList" parameterType="java.lang.String" resultType="java.lang.Long">
        SELECT t.id FROM vc_diagram_dir t WHERE t.SYS_DIR = 1
        AND parent_id = 1
        AND t.DATA_STATUS = 1
        AND t.ES_SYS_ID IN
        <foreach collection="list" item="id" separator="," open="(" close=")">#{id}</foreach>
    </select>

    <select id="findDirIdList" parameterType="java.util.Map" resultType="java.lang.Long">
        SELECT id FROM vc_diagram_dir
        <where>
            <foreach collection="dirIds" item="dirPath" separator="or" open="(" close=")">
                dir_path like concat('%', #{dirPath}, '%')
            </foreach>
            and dir_type = #{dirType}
            and data_status = 1
        </where>
    </select>
</mapper>