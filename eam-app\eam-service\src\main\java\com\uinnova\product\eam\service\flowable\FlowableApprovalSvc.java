package com.uinnova.product.eam.service.flowable;

import java.util.List;

/**
 * 工作流审批需要各业务方实现对应的各流程节点所需逻辑代码
 */
public interface FlowableApprovalSvc {

    /**
     * 根据流程节点查询对应审批用户
     * @param businessKey 业务定义key(方案id OR 视图id)
     * @param taskKey 任务节点标识
     * @return 用户标识集合
     */
    List<String> getApprovalUser(String businessKey, String taskKey);

    /**
     * 审批驳回
     * @param businessKey 业务定义key(方案id OR 视图id)
     */
    void reject(String businessKey);

    /**
     * 子流程被驳回，不涉及子流程无需实现
     * @param childBusinessKey
     */
    default void childReject(String childBusinessKey){
        throw new RuntimeException("子流程驳回未实现");
    }

    /**
     * 审批通过
     * @param businessKey 业务定义key(方案id OR 视图id)
     */
    void pass(String businessKey);

    /**
     * 审批终止
     * @param businessKey 业务定义key(方案id OR 视图id)
     */
    void cancel(String businessKey);
}
