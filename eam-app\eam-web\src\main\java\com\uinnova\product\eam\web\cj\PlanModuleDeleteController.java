package com.uinnova.product.eam.web.cj;

import com.uinnova.product.eam.base.cj.ResultMsg;
import com.uinnova.product.eam.model.cj.domain.PlanModuleDelete;
import com.uinnova.product.eam.service.cj.service.PlanModuleDeleteService;
import com.uinnova.product.eam.service.exception.BusinessException;
import com.uino.bean.permission.base.SysUser;
import com.uino.util.sys.SysUtil;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;

/**
 * @description:
 * @author: Lc
 * @create: 2023-03-08 14:28
 */
@RestController
@RequestMapping("/planModuleDelete")
public class PlanModuleDeleteController {

    @Resource
    private PlanModuleDeleteService planModuleDeleteService;

    @PostMapping("/add")
    public ResultMsg addPlanModule(@RequestBody PlanModuleDelete planModuleDelete) {
        SysUser sysUser = SysUtil.getCurrentUserInfo();
        if (sysUser == null || StringUtils.isEmpty(sysUser.getLoginCode())) {
            throw new BusinessException("当前用户登录已过期，请重新登录!");
        }
        Long id = planModuleDeleteService.addPlanModule(planModuleDelete, sysUser);
        return new ResultMsg(id);
    }

}
