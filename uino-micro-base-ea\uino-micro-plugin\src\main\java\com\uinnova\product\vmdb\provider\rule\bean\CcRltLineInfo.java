package com.uinnova.product.vmdb.provider.rule.bean;

import com.uinnova.product.vmdb.comm.model.rule.CcRltLine;
import com.uinnova.product.vmdb.comm.model.rule.CcRltLineCdt;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;

import java.io.Serializable;
import java.util.List;

public class CcRltLineInfo implements Serializable {
	private static final long serialVersionUID = 1L;
	
	
	
	/**
	 * 关系线
	 */
	private CcRltLine line;


	/**
	 * 关系线条件集合
	 */
	private List<CcRltLineCdt> lineCdts;


	/**
	 * 当前关系相关的信息
	 */
	private CcCiClassInfo classInfo;





	public CcCiClassInfo getClassInfo() {
		return classInfo;
	}


	public void setClassInfo(CcCiClassInfo classInfo) {
		this.classInfo = classInfo;
	}


	public List<CcRltLineCdt> getLineCdts() {
		return lineCdts;
	}


	public void setLineCdts(List<CcRltLineCdt> lineCdts) {
		this.lineCdts = lineCdts;
	}


	public CcRltLine getLine() {
		return line;
	}


	public void setLine(CcRltLine line) {
		this.line = line;
	}
	
	
	

}
