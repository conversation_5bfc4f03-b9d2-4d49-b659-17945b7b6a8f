package com.uinnova.product.eam.model.constants;

/**
 * 路径常量类
 *
 * <AUTHOR>
 * @since 2022-3-1 20:26:03
 */
public class PathConstants {

    /**
     * 方案动态 请求路径前缀
     */
    public static final String PLAN_DYNAMIC_START_PATH = "plan/dynamic";

    /**
     * 方案批注 请求路径前缀
     */
    public static final String PLAN_ANNOTATION_START_PATH = PLAN_DYNAMIC_START_PATH + "/annotation";

    /**
     * 回收站相关接口 请求路径前缀
     */
    public static final String RECYCLE_BIN_START_PATH = "/recycle";

    /**
     * 分享/协作相关接口 请求路径前缀
     */
    public static final String SHARE_START_PATH = "/diagramPlanShare";
}
