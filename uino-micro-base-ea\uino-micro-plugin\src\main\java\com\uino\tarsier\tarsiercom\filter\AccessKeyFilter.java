package com.uino.tarsier.tarsiercom.filter;

import java.io.IOException;
import java.util.Date;

import jakarta.servlet.FilterChain;
import jakarta.servlet.FilterConfig;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import jakarta.servlet.Filter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import com.uino.tarsier.tarsiercom.util.AccessKey;

public class AccessKeyFilter implements Filter {
	
	private static Logger logger = LoggerFactory.getLogger(AccessKeyFilter.class);
	public  static final String ACCESS_KEY="AccessKey";
	
	@Override
	public void init(FilterConfig filterConfig) throws ServletException {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
			throws IOException, ServletException {
		
		HttpServletRequest httpRequest = (HttpServletRequest)request;
		HttpServletResponse httpResp = (HttpServletResponse)response;
		
		try {
			
			String accessKey = httpRequest.getHeader(ACCESS_KEY);
			
			if(accessKey==null || accessKey.trim().equals("")) {
				//验证失败				
				httpResp.setStatus(HttpStatus.UNAUTHORIZED.value());			
			} else {
				Date accessDate = null;
				try {
					accessDate = AccessKey.getDatebyKey(accessKey);	
					logger.info("accessDate="+accessDate);
				} catch(Exception exp0) {
					logger.error("accessDate Exception", exp0);
					httpResp.setStatus(HttpStatus.UNAUTHORIZED.value());
					return;
				}

				//判断访问钥是否在10分钟有效期内
				long lMillis = System.currentTimeMillis() - accessDate.getTime();
				if(lMillis > (10*60*1000)) {
					//访问钥过期				
					httpResp.setStatus(HttpStatus.UNAUTHORIZED.value());
				} else {
					//正常访问
					chain.doFilter(httpRequest, response);
				}				
			}			
		} catch(Exception exp) {
			//验证失败
			logger.error("AccessKeyFilter Exception", exp);
			httpResp.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());		
		}		
	}

	@Override
	public void destroy() {
		// TODO Auto-generated method stub
		
	}
	
}
