package com.uinnova.product.eam.web.diagram.bean;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.comm.model.VcDiagramLog;
import com.uino.bean.permission.base.SysUser;

@Comment("视图操作日志信息")
public class VcDiagramLogInfo {
	
	@Comment("日志信息")
	private VcDiagramLog diagramLog;
	
	@Comment("操作人信息")
	private SysUser op;

	public VcDiagramLog getDiagramLog() {
		return diagramLog;
	}

	public void setDiagramLog(VcDiagramLog diagramLog) {
		this.diagramLog = diagramLog;
	}

	public SysUser getOp() {
		return op;
	}

	public void setOp(SysUser op) {
		this.op = op;
	}
	
}
