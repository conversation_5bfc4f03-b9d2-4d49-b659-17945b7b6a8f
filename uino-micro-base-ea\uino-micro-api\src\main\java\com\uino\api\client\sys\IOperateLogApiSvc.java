package com.uino.api.client.sys;

import com.binary.jdbc.Page;
import com.uino.bean.sys.base.ESOperateLog;
import com.uino.bean.sys.query.ESOperateLogSearchBean;

/**
 * 系统日志
 * 
 * <AUTHOR>
 */
public interface IOperateLogApiSvc {
    /**
     * 条件查询系统操作日志
     * 
     * @param bean
     * @return
     */
    Page<ESOperateLog> getOperateLogPageByCdt(ESOperateLogSearchBean bean);

    /**
     * 保存系统操作日志
     * 
     * @param log
     * @return
     */
    Long saveOrUpdate(ESOperateLog log);

    /**
     * 按保留时长清除系统操作日志
     * 
     * @param clearLogDuration
     * @return
     */
    Integer clearOperateLogByDuration(Integer clearLogDuration);

}
