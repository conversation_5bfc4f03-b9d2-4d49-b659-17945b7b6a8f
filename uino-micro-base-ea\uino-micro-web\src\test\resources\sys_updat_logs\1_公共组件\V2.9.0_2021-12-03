﻿数据权限
	[修复]搜索结果显示问题修复
多租户
	[优化]授权过期后，根据角色是否为超管显示相应过期提示页面
模拟数据
	[优化]移除模拟发送次数设置
系统设置
	[优化]LDAP集成，服务器设置，检索模式中的用户名字段移除长度限制
字典表
	[新增]以后端接口形式提供迁移字典所用接口，以实现初始化字典数据仍保持不可编辑及删除的效果。
	[优化]告警管理字典，告警声音上传交互优化，增加停止按钮
	[优化]导入字典表结果弹窗提示文本优化
	[优化]指标管理字典表增加采集频率单位字段
对象管理
	[新增]导入数据增加覆盖选项，勾选后将清空存量数据后以导入数据重建
	[优化]创建分类默认授权于admin角色
后端
	[新增]增加接口：对象分类的批量保存
	[新增]增加接口：根据uid或dataValue查询/删除数据权限/查询拥有某数据权限的角色
	[新增]增加接口：分类关系查询中ClassRltQueryDto增加原分类、目标分类、关系分类的批量查询
	[优化]Oauth2兼容问题优化
	[优化]性能字典兼容问题优化

	
