package com.uino.util.sys;

import java.util.Collection;
import java.util.LinkedList;
import java.util.List;
import java.util.UUID;

import org.springframework.beans.BeanUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 实体帮助类
 * 
 * <AUTHOR>
 */
@Slf4j
public class BeanUtil extends BeanUtils {
    /**
     * 将source的属性拷贝到targetCls的实例中，targetCls必须有空参数构造方法
     * 
     * @param source
     * @param target
     * @return
     */
    @SuppressWarnings("unchecked")
    public static <T> T converBean(Object source, Class<?> targetCls) {
        if (source == null) {
            return null;
        }
        Object target = null;
        try {
            target = targetCls.newInstance();
        } catch (Exception e) {
            String errorId = UUID.randomUUID().toString();
            log.error("{}反射拷贝属性失败{},{}", errorId, targetCls, e);
            throw new RuntimeException("实体转换失败，请查阅日志" + errorId);
        }
        BeanUtils.copyProperties(source, target);
        return (T) target;
    }

    /**
     * 拷贝beans
     * 
     * @param sources
     * @param targetCls
     * @return
     */
    public static <T> List<T> converBean(Collection<?> sources, Class<?> targetCls) {
        List<T> targets = new LinkedList<>();
        if (sources != null && sources.size() > 0) {
            for (Object source : sources) {
                T target = converBean(source, targetCls);
                targets.add(target);
            }
        }
        return targets;
    }
}
