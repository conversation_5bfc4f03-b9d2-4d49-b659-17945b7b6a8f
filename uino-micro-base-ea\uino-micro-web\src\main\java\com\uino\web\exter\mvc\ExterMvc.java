package com.uino.web.exter.mvc;

import java.util.List;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import com.binary.framework.util.ControllerUtils;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uinnova.product.vmdb.comm.doc.annotation.MvcDesc;
import com.uino.api.client.permission.IUserApiSvc;
import com.uino.bean.cmdb.business.ImportSheetMessage;
import com.uino.bean.permission.business.UserInfo;

@Controller
@RequestMapping("/exter")
@MvcDesc(author = "zmj", desc = "对外提供的接口")
public class ExterMvc {

    @Autowired
    private IUserApiSvc userSvc;

    @RequestMapping("/syncUserBatch")
    @ModDesc(desc = "批量更新用户信息", pDesc = "用户封装对象", pType = List.class, pcType = UserInfo.class, rDesc = "保存明细", rType = ImportSheetMessage.class)
    public void saveOrUpdate(HttpServletRequest request, HttpServletResponse response, @RequestBody List<UserInfo> userInfos) {
        ImportSheetMessage result = userSvc.syncUserBatch(userInfos);
        ControllerUtils.returnJson(request, response, result);
    }

    @ModDesc(desc = "跟据loginCode批量删除用户数据", pDesc = "用户登录名集合", pType = List.class, pcType = String.class, rDesc = "删除操作状态，1=成功，0=失败", rType = Integer.class)
    @RequestMapping("/deleteUserByLoginCodes")
    public void removeUserById(HttpServletRequest request, HttpServletResponse response, @RequestBody List<String> loginCodes) {
        Integer result = userSvc.deleteSysUserByLoginCodeBatch(loginCodes);
        ControllerUtils.returnJson(request, response, result);
    }

}
