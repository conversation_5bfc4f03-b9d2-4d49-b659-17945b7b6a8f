package com.uinnova.product.eam.model.cj.vo;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.base.diagram.model.ESDiagram;
import com.uinnova.product.eam.comm.model.es.EamMatrixInstance;
import com.uinnova.product.eam.model.enums.AssetType;
import com.uino.bean.permission.base.SysUser;
import lombok.Data;

import java.io.Serializable;

/**
 * @description:
 * @author: Lc
 * @create: 2022-02-24 17:20
 */
@Data
public class DiagramPlanVO implements Serializable {

    @Comment("制品")
    private ESDiagram diagram;

    @Comment("视图作者信息")
    private SysUser creator;

    @Comment("业务主键")
    private Long id;

    @Comment("名称")
    private String name;

    @Comment("用户")
    private String user;

    @Comment("方案")
    private PlanDesignResponseVO planDesignInstance;

    @Comment("矩阵")
    private EamMatrixInstance matrixInstance;

    @Comment("用作前端区分资源类型")
    private AssetType assetType;

    @Comment("制品类型分类")
    private Integer artifactType;

    @Comment("是否分享标识")
    private boolean share = false;

    @Comment("修改时间")
    private Long modifyTime;

    @Comment("是否已发布：0：未发布，1：已发布，2：审批中")
    private int releaseStatus;

    @Comment("附加的ciId")
    private long attachCiId;

    @Comment("附加的ciCode")
    private String attachCiCode;

    @Comment("是否被关注：0未关注 1关注")
    private Integer isAttention = 0;

    @Comment("是否需要审批")
    private Boolean isApprove = Boolean.FALSE;

    @Comment("是否是模型下的视图")
    private Boolean modelDiagram = Boolean.FALSE;

    @Comment("是否存在转换模板视图")
    private Boolean tempDiagram = Boolean.FALSE;
}
