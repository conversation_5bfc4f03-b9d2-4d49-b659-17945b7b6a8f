package com.uino.bean.permission.base;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 刷新token
 * 
 * <AUTHOR>
 *
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OauthRefreshTokenDetail implements Serializable {

	private static final long serialVersionUID = 1L;
	/**
	 * 主键
	 */
	private Long id;

	/**
	 * token唯一标识
	 */
	private String tokenCode;

	/**
	 * token
	 */
	private byte[] tokenVal;

	/**
	 * authentication
	 */
	private byte[] authentication;

	/** 所属域 */
	private Long domainId;

	/** 创建人 */
	private String creator;

	/** 修改人 */
	private String modifier;

	/** 创建时间 */
	private Long createTime;

	/** 修改时间 */
	private Long modifyTime;
}
