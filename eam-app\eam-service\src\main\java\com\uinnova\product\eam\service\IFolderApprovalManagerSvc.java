package com.uinnova.product.eam.service;

import com.uinnova.product.eam.comm.model.es.FolderApprovalManager;
import com.uinnova.product.eam.model.dto.FolderApprovalManagerDTO;

import java.util.List;

/**
 * 文件夹审批用户配置
 * <AUTHOR>
 */
public interface IFolderApprovalManagerSvc {


    /**
     * 新增文件夹审批用户
     * @param dto 文件夹id、用户
     * @return 新增结果
     */
    Long addApproval(FolderApprovalManager dto);

    /**
     * 通过文件夹id查询
     * @param dirId 文件夹id
     * @return 审批用户信息
     */
    FolderApprovalManagerDTO queryByDirId(Long dirId);

    /**
     * 获取文件夹审批用户code
     * @param dirId 文件夹id
     * @return 用户标识
     */
    List<String> queryDirApprovalUser(Long dirId);

}
