package com.uino.service.sys.microservice;

import com.uino.bean.cmdb.base.ESTableDataConfigInfo;

/**
 * 
 * <AUTHOR>
 *
 */
public interface ITableDataConfigSvc {

    /**
     * 查询CI数据配置
     * 
     * @param uid
     * @return
     */
    ESTableDataConfigInfo getCIDataConfigInfo(Long domainId,String uid);

    /**
     * 保存CI数据配置
     * 
     * @param configInfo
     * @return
     */
    Long saveCIDataConfigInfo(ESTableDataConfigInfo configInfo);
}
