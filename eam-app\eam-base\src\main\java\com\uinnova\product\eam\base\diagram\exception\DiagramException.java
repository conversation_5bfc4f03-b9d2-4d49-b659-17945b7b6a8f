package com.uinnova.product.eam.base.diagram.exception;

public class DiagramException extends RuntimeException {

    public static final long serialVersionUID = 1;

    public DiagramException() {
        super();
    }

    public DiagramException(String message) {
        super(message);
    }

    public DiagramException(String message, Throwable cause) {
        super(message, cause);
    }

    public DiagramException(Throwable cause) {
        super(cause);
    }
}
