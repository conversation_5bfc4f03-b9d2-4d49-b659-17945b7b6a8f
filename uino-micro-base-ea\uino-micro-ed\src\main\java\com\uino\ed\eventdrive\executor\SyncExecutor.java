package com.uino.ed.eventdrive.executor;

import com.uino.ed.eventdrive.event.Event;
import com.uino.ed.eventdrive.handle.EventHandle;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 同步执行器
 */
@Component
@Slf4j
public class SyncExecutor {

    public void execute(EventHandle handle, Event event) {
        try {
            handle.handle(event);
        } catch (Exception exp) {
            //log.error(handle.getName()+"execute error",exp);
        }
    }
}
