package com.uinnova.product.eam.web.eam.mvc.diagram;

import com.binary.framework.web.RemoteResult;
import com.uinnova.product.eam.model.vo.AssetProcessReq;
import com.uinnova.product.eam.service.DiagramProcessSvc;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 *  视图审批流程相关接口
 */
@RestController
@RequestMapping("/eam/diagram/process")
public class DiagramProcessMvc {

    @Autowired
    DiagramProcessSvc diagramProcessSvc;


    @PostMapping("/submit")
    @ModDesc(desc = "视图提交审批", pDesc = "", rDesc = "", rType = RemoteResult.class)
    public RemoteResult submit(@RequestBody AssetProcessReq assetProcessReq) {
        // Boolean flag = diagramProcessSvc.submit(assetProcessReq.getDiagramId(), assetProcessReq.getDirId(), assetProcessReq.getDesc());
        return new RemoteResult(true);
    }

}
