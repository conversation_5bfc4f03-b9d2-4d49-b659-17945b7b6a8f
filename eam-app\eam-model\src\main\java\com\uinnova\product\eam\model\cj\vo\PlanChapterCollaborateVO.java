package com.uinnova.product.eam.model.cj.vo;

import com.uinnova.product.eam.model.cj.group.AddGroup;
import com.uinnova.product.eam.model.cj.group.ModifyGroup;
import com.uinnova.product.eam.model.cj.group.QueryGroup;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class PlanChapterCollaborateVO implements Serializable {
    /** 方案id */
    @NotNull(message = "方案主键不能为空!", groups = {AddGroup.class, ModifyGroup.class, QueryGroup.class})
    private Long planId;
    /** 章节id */
    @NotNull(message = "章节主键不能为空!", groups = {AddGroup.class, ModifyGroup.class, QueryGroup.class})
    private Long chapterId;
    /** 添加可编辑方案的用户 */
    @NotEmpty(message = "添加或删除可操作章节用户不能为空!", groups = {AddGroup.class})
    private String loginCode;
    /** 用户状态 0：删除操作用户，1：新增操作用户 */
    @NotNull(message = "添加或删除用户状态不能为空!", groups = {AddGroup.class})
    private Integer status;
    /** 是否完成 0：未完成 1：已完成 */
    @NotNull(message = "章节是否已完成状态不能为空!", groups = {AddGroup.class, ModifyGroup.class})
    private Integer complete;

}
