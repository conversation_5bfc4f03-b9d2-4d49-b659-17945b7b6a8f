package com.uinnova.product.eam.comm.model;




import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;


@Comment("视图元素版本表[VC_DIAGRAM_ELE_VERSION]")
public class CVcDiagramEleVersion implements Condition {
	private static final long serialVersionUID = 1L;


	@Comment("ID[ID] operate-Equal[=]")
	private Long id;


	@Comment("ID[ID] operate-In[in]")
	private Long[] ids;


	@Comment("ID[ID] operate-GTEqual[>=]")
	private Long startId;

	@Comment("ID[ID] operate-LTEqual[<=]")
	private Long endId;


	@Comment("视图历史ID[DIAGRAM_ID] operate-Equal[=]")
	private Long diagramId;


	@Comment("视图历史ID[DIAGRAM_ID] operate-In[in]")
	private Long[] diagramIds;


	@Comment("视图历史ID[DIAGRAM_ID] operate-GTEqual[>=]")
	private Long startDiagramId;

	@Comment("视图历史ID[DIAGRAM_ID] operate-LTEqual[<=]")
	private Long endDiagramId;


	@Comment("元素类型[ELE_TYPE] operate-Equal[=]")
	private Integer eleType;


	@Comment("元素类型[ELE_TYPE] operate-In[in]")
	private Integer[] eleTypes;


	@Comment("元素类型[ELE_TYPE] operate-GTEqual[>=]")
	private Integer startEleType;

	@Comment("元素类型[ELE_TYPE] operate-LTEqual[<=]")
	private Integer endEleType;


	@Comment("元素ID[ELE_ID] operate-Like[like]")
	private String eleId;


	@Comment("元素ID[ELE_ID] operate-Equal[=]")
	private String eleIdEqual;


	@Comment("元素ID[ELE_ID] operate-In[in]")
	private String[] eleIds;

	@Comment("元素ID[ELE_CODE] operate-Like[like]")
	private String eleCode;

	@Comment("元素ID[ELE_CODE] operate-Equal[=]")
	private String eleCodeEqual;


	@Comment("元素ID[ELE_CODE] operate-In[in]")
	private String[] eleCodes;


	@Comment("所属域[DOMAIN_ID] operate-Equal[=]")
	private Long domainId;


	@Comment("所属域[DOMAIN_ID] operate-In[in]")
	private Long[] domainIds;


	@Comment("所属域[DOMAIN_ID] operate-GTEqual[>=]")
	private Long startDomainId;

	@Comment("所属域[DOMAIN_ID] operate-LTEqual[<=]")
	private Long endDomainId;


	@Comment("创建时间[CREATE_TIME] operate-Equal[=]    yyyyMMddHHmmss")
	private Long createTime;


	@Comment("创建时间[CREATE_TIME] operate-In[in]    yyyyMMddHHmmss")
	private Long[] createTimes;


	@Comment("创建时间[CREATE_TIME] operate-GTEqual[>=]    yyyyMMddHHmmss")
	private Long startCreateTime;

	@Comment("创建时间[CREATE_TIME] operate-LTEqual[<=]    yyyyMMddHHmmss")
	private Long endCreateTime;


	@Comment("修改时间[MODIFY_TIME] operate-Equal[=]    yyyyMMddHHmmss")
	private Long modifyTime;


	@Comment("修改时间[MODIFY_TIME] operate-In[in]    yyyyMMddHHmmss")
	private Long[] modifyTimes;


	@Comment("修改时间[MODIFY_TIME] operate-GTEqual[>=]    yyyyMMddHHmmss")
	private Long startModifyTime;

	@Comment("修改时间[MODIFY_TIME] operate-LTEqual[<=]    yyyyMMddHHmmss")
	private Long endModifyTime;




	public Long getId() {
		return this.id;
	}
	public void setId(Long id) {
		this.id = id;
	}


	public Long[] getIds() {
		return this.ids;
	}
	public void setIds(Long[] ids) {
		this.ids = ids;
	}


	public Long getStartId() {
		return this.startId;
	}
	public void setStartId(Long startId) {
		this.startId = startId;
	}


	public Long getEndId() {
		return this.endId;
	}
	public void setEndId(Long endId) {
		this.endId = endId;
	}


	public Long getDiagramId() {
		return this.diagramId;
	}
	public void setDiagramId(Long diagramId) {
		this.diagramId = diagramId;
	}


	public Long[] getDiagramIds() {
		return this.diagramIds;
	}
	public void setDiagramIds(Long[] diagramIds) {
		this.diagramIds = diagramIds;
	}


	public Long getStartDiagramId() {
		return this.startDiagramId;
	}
	public void setStartDiagramId(Long startDiagramId) {
		this.startDiagramId = startDiagramId;
	}


	public Long getEndDiagramId() {
		return this.endDiagramId;
	}
	public void setEndDiagramId(Long endDiagramId) {
		this.endDiagramId = endDiagramId;
	}


	public Integer getEleType() {
		return this.eleType;
	}
	public void setEleType(Integer eleType) {
		this.eleType = eleType;
	}


	public Integer[] getEleTypes() {
		return this.eleTypes;
	}
	public void setEleTypes(Integer[] eleTypes) {
		this.eleTypes = eleTypes;
	}


	public Integer getStartEleType() {
		return this.startEleType;
	}
	public void setStartEleType(Integer startEleType) {
		this.startEleType = startEleType;
	}


	public Integer getEndEleType() {
		return this.endEleType;
	}
	public void setEndEleType(Integer endEleType) {
		this.endEleType = endEleType;
	}


	public String getEleId() {
		return this.eleId;
	}
	public void setEleId(String eleId) {
		this.eleId = eleId;
	}


	public String getEleIdEqual() {
		return this.eleIdEqual;
	}
	public void setEleIdEqual(String eleIdEqual) {
		this.eleIdEqual = eleIdEqual;
	}


	public String[] getEleIds() {
		return this.eleIds;
	}
	public void setEleIds(String[] eleIds) {
		this.eleIds = eleIds;
	}


	public Long getDomainId() {
		return this.domainId;
	}
	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}


	public Long[] getDomainIds() {
		return this.domainIds;
	}
	public void setDomainIds(Long[] domainIds) {
		this.domainIds = domainIds;
	}


	public Long getStartDomainId() {
		return this.startDomainId;
	}
	public void setStartDomainId(Long startDomainId) {
		this.startDomainId = startDomainId;
	}


	public Long getEndDomainId() {
		return this.endDomainId;
	}
	public void setEndDomainId(Long endDomainId) {
		this.endDomainId = endDomainId;
	}


	public Long getCreateTime() {
		return this.createTime;
	}
	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}


	public Long[] getCreateTimes() {
		return this.createTimes;
	}
	public void setCreateTimes(Long[] createTimes) {
		this.createTimes = createTimes;
	}


	public Long getStartCreateTime() {
		return this.startCreateTime;
	}
	public void setStartCreateTime(Long startCreateTime) {
		this.startCreateTime = startCreateTime;
	}


	public Long getEndCreateTime() {
		return this.endCreateTime;
	}
	public void setEndCreateTime(Long endCreateTime) {
		this.endCreateTime = endCreateTime;
	}


	public Long getModifyTime() {
		return this.modifyTime;
	}
	public void setModifyTime(Long modifyTime) {
		this.modifyTime = modifyTime;
	}


	public Long[] getModifyTimes() {
		return this.modifyTimes;
	}
	public void setModifyTimes(Long[] modifyTimes) {
		this.modifyTimes = modifyTimes;
	}


	public Long getStartModifyTime() {
		return this.startModifyTime;
	}
	public void setStartModifyTime(Long startModifyTime) {
		this.startModifyTime = startModifyTime;
	}


	public Long getEndModifyTime() {
		return this.endModifyTime;
	}
	public void setEndModifyTime(Long endModifyTime) {
		this.endModifyTime = endModifyTime;
	}


	public String getEleCode() {
		return eleCode;
	}

	public void setEleCode(String eleCode) {
		this.eleCode = eleCode;
	}

	public String getEleCodeEqual() {
		return eleCodeEqual;
	}

	public void setEleCodeEqual(String eleCodeEqual) {
		this.eleCodeEqual = eleCodeEqual;
	}

	public String[] getEleCodes() {
		return eleCodes;
	}

	public void setEleCodes(String[] eleCodes) {
		this.eleCodes = eleCodes;
	}
}


