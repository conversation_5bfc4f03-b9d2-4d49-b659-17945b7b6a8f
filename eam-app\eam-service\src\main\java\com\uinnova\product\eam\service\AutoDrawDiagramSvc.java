package com.uinnova.product.eam.service;


import com.uinnova.product.eam.model.vo.CiClassCiInfosVo;
import com.uinnova.product.eam.model.AutoDrawCIVo;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uino.bean.cmdb.base.ESCIInfo;

import java.util.List;
import java.util.Map;

public interface AutoDrawDiagramSvc {

    /**
     *  自动成图 -- 技术架构图
     * @param dataSetId
     * @param ciCode
     */
    AutoDrawCIVo getTechnologyArchDiagramData(Long dataSetId, String ciCode);

    /**
     *  自动成图 -- 上下文关系图
     * @param dataSetId
     * @param ciCode
     * @return
     */
    Map<String, Object> getContextArchDiagramData(Long dataSetId, String ciCode);

    /**
     *  自动成图 -- 功能架构图
     * @param dataSetId
     * @param ciCode
     * @return
     */
    AutoDrawCIVo getFunctionalArchDiagramData(Long dataSetId, String ciCode);

    /**
     *  自动成图 -- 应用架构图
     * @param dataSetId
     * @param ciCode
     * @return
     */
    AutoDrawCIVo getApplicationArchDiagramData(Long dataSetId, String ciCode);

    /**
     *  自动成图 -- 部署架构图
     * @param dataSetId
     * @param ciCode
     * @return
     */
    List<CiClassCiInfosVo> getDeploymentArchDiagramData(Long dataSetId, String ciCode);

    /**
     *  自动成图 -- 应用架构图左侧数据列表
     * @param dataSetId
     * @param ciCode
     * @return
     */
    List<CcCiInfo> getADCIDataList(Long dataSetId, String ciCode);

    /**
     *  自动成图转化本地数据创建
     * @param ciCodes
     * @param rltCodes
     * @return
     */
    Boolean convertLocalViewData(List<String> ciCodes, List<String> rltCodes);
}
