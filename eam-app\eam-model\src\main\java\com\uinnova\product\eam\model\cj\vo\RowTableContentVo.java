package com.uinnova.product.eam.model.cj.vo;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.io.Serializable;

/**
 * @description: 数据表格内容
 * @author: Lc
 * @create: 2022-01-07 15:30
 */
@Data
@Comment("名称")
public class RowTableContentVo implements Serializable {

    @Comment("名称")
    private String name;
    @Comment("类型")
    private Integer type;
    @Comment("必填")
    private Boolean required;
    /*@Comment("约束")
    private String constraint;*/
    @Comment("约束类型转换")
    private String copyConstraint;
    @Comment("提示")
    private String tip;
    @Comment("行宽 大 中 小 自定义")
    private String rowWidth;
    @Comment("行宽数值")
    private String rowWidthValue;
    @Comment("资产信息项和绑定资产信息项，全路径名称")
    private String content;
    @Comment("资产信息项使用，分类id")
    private Long classId;
    @Comment("表格资产关联数据类型，1：引用筛选数据 2：引用全部数据 3：引用绑定数据")
    private Integer assetType;
    @Comment("列宽 大 中 小 自定义")
    private Integer columnWidth;
    @Comment("列宽数值")
    private Integer columnWidthValue;
    @Comment("表格是否允许多选")
    private Boolean multiple;
}
