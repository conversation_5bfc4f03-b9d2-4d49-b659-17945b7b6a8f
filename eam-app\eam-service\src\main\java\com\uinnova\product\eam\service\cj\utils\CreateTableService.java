package com.uinnova.product.eam.service.cj.utils;

import com.uinnova.product.eam.model.cj.domain.ContextModule;
import com.uinnova.product.eam.model.cj.domain.PlanTemplateChapterData;
import com.uinnova.product.eam.model.cj.vo.PlanChapterVO;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.apache.poi.xwpf.usermodel.XWPFTableRow;


public interface CreateTableService {

    /**
     * 生成矩阵表格
     */
    void getMatrixTable(PlanTemplateChapterData definition, XWPFTableRow comTableRowOne,
                                ContextModule context, XWPFTable ComTable);
    /**
     * 生成垂直表格
     */
    void getVerticalTable(PlanTemplateChapterData definition, XWPFTableRow comTableRowOne,
                          ContextModule context, XWPFTable ComTable);
    /**
     * 生成水平表格
     */
    void getHorizontalTable(PlanTemplateChapterData definition, XWPFTableRow comTableRowOne,
                            ContextModule context, XWPFTable ComTable, PlanChapterVO planChapterVO);
}
