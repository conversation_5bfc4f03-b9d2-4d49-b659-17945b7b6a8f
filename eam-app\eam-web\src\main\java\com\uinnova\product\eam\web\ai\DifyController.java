package com.uinnova.product.eam.web.ai;

import com.binary.core.util.BinaryUtils;
import com.binary.framework.web.RemoteResult;
import com.uinnova.product.eam.base.exception.ServerException;
import com.uinnova.product.eam.model.vo.DifyDiagram;
import com.uinnova.product.eam.service.ai.IDifyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ai")
public class DifyController {

    @Autowired
    private IDifyService difyService;

    @PostMapping("/createDiagram")
    public RemoteResult createDiagram(@RequestBody DifyDiagram difyDiagram) {

        if (BinaryUtils.isEmpty(difyDiagram.getDrawType())) {
            throw new ServerException("制图类型不能为空!");
        }
        if (BinaryUtils.isEmpty(difyDiagram.getDiagramContent())) {
            throw new ServerException("制图指令不能为空!");
        }
        return new RemoteResult(difyService.createDiagram(difyDiagram));
    }
}