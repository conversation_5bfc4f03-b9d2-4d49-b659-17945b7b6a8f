package com.uino.api.client.cmdb.rpc;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.uino.provider.feign.cmdb.DataSetPriorityDisplayFeign;
import com.uino.api.client.cmdb.IDataSetPriorityDisplayApiSvc;

@Service
public class DataSetPriorityDisplayApiSvcRpc implements IDataSetPriorityDisplayApiSvc {

	@Autowired
	private DataSetPriorityDisplayFeign dataSetPriorityDisplayFeign;

	@Override
	public boolean priorityDisplay(Long dataSetId, int displayType) {
		JSONObject body = new JSONObject();
		body.put("dataSetId", dataSetId);
		body.put("displayType", displayType);
		return dataSetPriorityDisplayFeign.priorityDisplay(body);
	}
	

}
