package com.uinnova.product.eam.service;

import java.util.List;
import java.util.Map;

import com.uinnova.product.eam.model.FlowOperationStatisticsDto;

/**
 * 流程管理/统计报表服务层
 *
 * <AUTHOR>
 * @since 2024/12/4 下午14:07
 */
public interface FlowStatisticsReportService {
	
	Map<String, Object> getFlowDataStatistics();
	
	List<Map<String, Object>> getFlowNumStatistics();

	List<Map<String, Object>> getFlowPerformanceNumStatistics();

	Map<String,  Map<String, String>> getFlowActivityNumData();

	Map<String, Object> getFlowRatioStatistics();

	List<Map<String, Map<String, Integer>>> getFlowSituationStatistics();

    List<Map<String, String>> getAllActivityRuningDuration(FlowOperationStatisticsDto dto);

}
