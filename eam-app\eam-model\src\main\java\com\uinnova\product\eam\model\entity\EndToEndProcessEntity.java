package com.uinnova.product.eam.model.entity;

import com.uinnova.product.eam.base.cj.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *  端到端流程实体类
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class EndToEndProcessEntity extends BaseEntity {
    /**
     * 端到端流程编码
     */
    private String code;
    /**
     * 如果是目录，就是目录名称，如果是流程，就是流程名称。
     */
    private String name;
    /**
     * 责任人，目前是一个
     */
    private String responsiblePerson;
    /**
     * 编写人，可以多选，存的是json字符串。
     */
    private String editor;
    /**
     * 层级，1是目录，2是流程
     */
    private Integer level;
    /**
     * 父级id,如果是根目录则为null
     */
    private Long parentId;


}
