package com.uinnova.product.eam.model.cj.domain;


import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.base.cj.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 方案设计实例 EntityBean、Condition
 * <AUTHOR>
 */

@EqualsAndHashCode(callSuper = true)
@Data
public class PlanDesignInstance extends BaseEntity implements Condition {

    /**
     * 主键数组
     */
    private Long[] ids;

    /**
     * 业务主键，已被使用
     */
    private Long businessId;

    /**
     * 业务主键，替换使用
     */
    private String businessKey;

    /**
     * 方案设计名称
     */
    private String name;

    /**
     * 方案设计说明
     */
    private String explain;

    /**
     * 方案设计模板businessId
     */
    private Long templateId;

    /**
     * 版本
     */
    private Integer version;

    /**
     * 是否是最新版本
     */
    private Boolean isCurrentVersion;

    /**
     * <p>状态</p>
     * <p>deleted：已删除</p>
     * <p>published：已发布</p>
     * <p>draft：草稿</p>
     * <p>history：历史版本</p>
     */
    private String status;

    /**
     * 方案类型
     */
    private Long typeId;

    /**
     * 默认系统的ciCode
     */
    private String defaultSystemCiCode;

    /**
     * 默认系统的classId
     */
    private Long defaultSystemClassId;

    /**
     * 主办系统id集合
     */
    private List<String> ciCodeList;

    /**
     * 方案所属文件夹
     */
    private Long dirId;

    /**
     * 可回收状态
     */
    private Boolean recyclable;

    /**
     * 是否在流程审批中
     */
    private boolean processApproval;

    /**
     * 备份字段,便于删除后的恢复
     */
    private PlanDesignInstance temp;

    /**
     * 发布位置
     */
    private Long assetsDirId;

    /**
     * 发布位置中文路径
     */
    private String echoDirName;

    /**
     * 区分方案所属领域
     */
    @Deprecated
    private Integer dirType;

    @Comment("选择发布位置类型 方案模板配置了发布位置和类型")
    @Deprecated
    private Integer assetsDirType;

    /**
     * 资产类型：1:设计库 2:资产库
     */
    private Integer assetsType;

    /**
     * 检出关联方案id
     */
    private Long detectionPlanId;

    /**
     * 方案模板版本
     */
    private Integer planTemplateVersion;

    /**
     * 进入回收站之前状态 发布还是草稿
     */
    private String deleteBeforeStatus;

    /**
     * 任务id
     */
    private String taskId;

    @Comment("分类目录id")
    private Long domainDirId;

    /**
     * 方案设计模板parentId
     */
    private Long templateParentId;
}
