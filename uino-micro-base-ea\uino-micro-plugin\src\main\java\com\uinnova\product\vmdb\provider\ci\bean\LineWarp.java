package com.uinnova.product.vmdb.provider.ci.bean;

import com.uinnova.product.vmdb.comm.model.rule.CcRltLine;
import com.uinnova.product.vmdb.provider.rule.bean.CcRltNodeInfo;

public class LineWarp {
	private CcRltNodeInfo sourceNode;
	private CcRltNodeInfo targetNode;
	private CcRltLine rltLine;

	public LineWarp(CcRltNodeInfo sourceNode, CcRltNodeInfo targetNode,
			CcRltLine rltLine) {
		super();
		this.sourceNode = sourceNode;
		this.targetNode = targetNode;
		this.rltLine = rltLine;
	}

	public CcRltNodeInfo getSourceNode() {
		return sourceNode;
	}

	public void setSourceNode(CcRltNodeInfo sourceNode) {
		this.sourceNode = sourceNode;
	}

	public CcRltNodeInfo getTargetNode() {
		return targetNode;
	}

	public void setTargetNode(CcRltNodeInfo targetNode) {
		this.targetNode = targetNode;
	}

	public CcRltLine getRltLine() {
		return rltLine;
	}

	public void setRltLine(CcRltLine rltLine) {
		this.rltLine = rltLine;
	}

}