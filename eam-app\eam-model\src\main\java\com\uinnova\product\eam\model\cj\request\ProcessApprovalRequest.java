package com.uinnova.product.eam.model.cj.request;

import lombok.Data;

import java.io.Serializable;

/**
 * @description:
 * @author: Lc
 * @create: 2022-03-08 11:08
 */
@Data
public class ProcessApprovalRequest implements Serializable {
    /** 方案主键 */
    private Long planId;
    /** 是否为首次审批 1:方案中提交审批 2:审批页面提交审批 3:审批动作*/
    private Integer sign;
    /** 任务id */
    private String taskId;
    /** 审批意见 */
    private String remark;
    /** 审批动作 1:同意 2:驳回 3:终止*/
    private Integer approval;
    /** 发布过去的资产文件夹id */
    private Long assetsDirId;
    /** 子系统编码 */
    private String ciCode;
    /** 流程实例id */
    private String processInstanceId;
    /** 任务定义主键 */
    private String taskDefinitionKey;
}
