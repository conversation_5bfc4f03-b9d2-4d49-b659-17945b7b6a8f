package com.uinnova.product.eam.web.mix.diagram.v2.webSocekt.bean;

import com.alibaba.fastjson.annotation.JSONField;
import com.uinnova.product.eam.base.diagram.mix.model.SysUserDTO;
import com.uino.bean.permission.base.SysUser;
import lombok.Data;

import java.util.List;

/**
 * @Classname SocketResult
 * @Description 返回值
 * @Date 2021/4/23 11:18
 * <AUTHOR> sh
 */
@Data
public class SocketResult {
    //操作类型：-1：下线，0：修改数据，1：上线
    private Integer msgType;
    //连接用户
    private SysUserDTO onlineUser;
    //下线用户
    private SysUserDTO offlineUser;
    //当前操作用户
    private SysUserDTO currentOperationUser;
    //用户列表
    private List<SysUserDTO> connectionUserList;
    //其他消息体
    private Object body;

    public enum SType{
        ONLINE(1),
        OFFLINE(-1),
        OPERATION(0);
        private final Integer type;

        SType(Integer type) {
            this.type = type;
        }

        public Integer getType() {
            return type;
        }

    }
}
