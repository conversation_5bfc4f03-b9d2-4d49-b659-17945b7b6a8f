package com.uino.api.client.permission.local;

import com.uino.service.permission.microservice.IButtonSvc;
import com.uino.bean.permission.base.SysModule;
import com.uino.bean.permission.query.SysModuleCheck;
import com.uino.api.client.permission.IButtonApiSvc;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * @Title: ButtonApiSvcLocal
 * @Description: ButtonApiSvcLocal
 * @Author: YGQ
 * @Create: 2021-06-28 10:04
 **/
@Service
public class ButtonApiSvcLocal implements IButtonApiSvc {

    private final IButtonSvc iButtonSvc;

    @Autowired
    public ButtonApiSvcLocal(IButtonSvc iButtonSvc) {
        this.iButtonSvc = iButtonSvc;
    }

    @Override
    public SysModule saveButton(SysModule buttonDto) {
        return iButtonSvc.saveButton(buttonDto);
    }

    @Override
    public void deleteButton(Long buttonId) {
        iButtonSvc.deleteButton(buttonId);
    }

    @Override
    public void saveButtonSort(Map<Long, Integer> orderDict) {
        iButtonSvc.saveButtonSort(orderDict);
    }

    @Override
    public SysModuleCheck checkModuleSign(SysModule sysButton) {
        return iButtonSvc.checkModuleSign(sysButton);
    }
}
