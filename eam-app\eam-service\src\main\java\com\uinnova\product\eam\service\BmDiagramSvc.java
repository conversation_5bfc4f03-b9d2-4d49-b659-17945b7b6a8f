package com.uinnova.product.eam.service;


import com.uinnova.product.eam.model.asset.EamReleaseHistoryDTO;
import com.uinnova.product.eam.model.vo.CheckBatchArtifactRuleVo;
import com.uinnova.product.vmdb.provider.rlt.bean.CcCiRltInfo;

import java.util.List;
import java.util.Set;

/**
 * 业务建模视图处理业务层接口
 * <AUTHOR>
 * @date 2021/11/3
 */
public interface BmDiagramSvc {

    /**
     * 查询当前默认视图历史版本信息
     * @param id
     * @return
     */
    List<EamReleaseHistoryDTO> queryReleaseHistory(String id, Boolean historyFlag);

    /**
     * 判断是正常发布/强制发布
     * @param diagramId
     * @return
     */
    boolean handlerRelease(String diagramId);

    List<CcCiRltInfo> getActivityListByTaskRlt(String diagramId, String ciCode, String libType);

    /**
     * 视图制品数量校验接口--批量
     * @param diagramIds
     * @return
     */
    List<CheckBatchArtifactRuleVo> categoryEleNumCheckBatch(List<String> diagramIds);

    /**
     * 更新视图绑定制品id
     * @param diagramIds
     * @param artifactId
     * @return
     */
    Integer updateArtifactId(Set<String> diagramIds, Long artifactId);
}
