package com.uinnova.product.eam.db.impl;


import com.uinnova.product.eam.comm.model.CVcDiagramCiAttrDisp;
import com.uinnova.product.eam.comm.model.VcDiagramCiAttrDisp;
import com.uinnova.product.eam.db.VcDiagramCiAttrDispDao;
import com.uinnova.product.eam.db.support.dao.mybatis.ComMyBatisBinaryDaoImpl;


/**
 * 视图CI属性显示表[VC_DIAGRAM_CI_ATTR_DISP]数据访问对象实现
 */
public class VcDiagramCiAttrDispDaoImpl extends ComMyBatisBinaryDaoImpl<VcDiagramCiAttrDisp, CVcDiagramCiAttrDisp> implements VcDiagramCiAttrDispDao {

//    @Override
//    public String getTableName() {
//        return "IAMS." + getDaoDefinition().getTableName();
//    }
}


