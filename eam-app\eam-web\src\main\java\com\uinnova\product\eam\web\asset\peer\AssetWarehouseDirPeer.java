package com.uinnova.product.eam.web.asset.peer;

import com.uinnova.product.eam.model.AssetWarehouseDirVo;
import com.uinnova.product.eam.service.AssetWarehouseDirSvc;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.List;

@Service
public class AssetWarehouseDirPeer {
    @Resource
    AssetWarehouseDirSvc assetWarehouseDirSvc;

    public Integer saveOrUpdate(List<AssetWarehouseDirVo> assetWarehouseDirs) {
        return assetWarehouseDirSvc.saveOrUpdate(assetWarehouseDirs);
    }

    public Integer delete(Long id) {
        return assetWarehouseDirSvc.delete(id);
    }

    public List<AssetWarehouseDirVo> getTree(Long userId) {
        return assetWarehouseDirSvc.getTree(userId);
    }

    public List<AssetWarehouseDirVo> getConfigTree() {
        return assetWarehouseDirSvc.getConfigTree();
    }

    public void migrationAssetModule() {
        assetWarehouseDirSvc.migrationAssetModule();
    }
}
