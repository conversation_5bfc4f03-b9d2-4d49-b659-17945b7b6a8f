package com.uino.util.message.queue.service.consumer;

/**
 * MessageQueueConsumerInterface, The specific consumer implementation class should be implement {@link MessageQueueConsumerInterface},
 * And implement the listen method
 *
 * @Author: YGQ
 * @Create: 2021-05-25 12:37
 **/
@FunctionalInterface
public interface MessageQueueConsumerInterface {
    /**
     * Enable monitoring
     */
    void listen();
}
