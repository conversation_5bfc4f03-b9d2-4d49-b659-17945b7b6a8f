package com.uino.provider.feign.cmdb;

import com.uino.bean.cmdb.base.dataset.batch.DataSetExeResultSheet;
import com.uino.provider.feign.config.BaseFeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @Title: DataSetExeResultFegin
 * @Description:
 * @Author: YGQ
 * @Create: 2021-05-31 16:38
 **/
@Component
@FeignClient(name = "${feign-server-name:tarsier-platform}", path = "${feign-server-path:tarsier-platform}/feign/cmdb/dataset/result", configuration = {
        BaseFeignConfig.class})
public interface DataSetExeResultFegin {

    /**
     * save or update exe result by batch
     *
     * @param dataSetExeResultSheets execute result
     */
    @PostMapping("saveOrUpdateBatch")
    void saveOrUpdateBatch(@RequestBody List<DataSetExeResultSheet> dataSetExeResultSheets);

    /**
     * delete by data set id
     *
     * @param dataSetId id
     */
    @PostMapping("deleteByDataSetId")
    void deleteByDataSetId(@RequestBody Long dataSetId);
}
