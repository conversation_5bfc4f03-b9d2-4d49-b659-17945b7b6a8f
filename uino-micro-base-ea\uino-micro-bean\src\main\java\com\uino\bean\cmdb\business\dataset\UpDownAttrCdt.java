package com.uino.bean.cmdb.business.dataset;

import java.io.Serializable;
import java.util.List;

import com.uino.bean.cmdb.query.ESAttrBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value="上下层属性",description = "上下层属性")
public class UpDownAttrCdt implements Serializable {

	private static final long serialVersionUID = 3442053700277682429L;

	@ApiModelProperty(value="分类id",example = "123")
	private Long classId;

	@ApiModelProperty(value="and条件")
	private List<ESAttrBean> andAttrs;

	@ApiModelProperty(value="or条件")
	private List<ESAttrBean> orAttrs;
	
	public Long getClassId() {
		return classId;
	}
	public void setClassId(Long classId) {
		this.classId = classId;
	}
	public List<ESAttrBean> getAndAttrs() {
		return andAttrs;
	}
	public void setAndAttrs(List<ESAttrBean> andAttrs) {
		this.andAttrs = andAttrs;
	}
	public List<ESAttrBean> getOrAttrs() {
		return orAttrs;
	}
	public void setOrAttrs(List<ESAttrBean> orAttrs) {
		this.orAttrs = orAttrs;
	}
	
}
