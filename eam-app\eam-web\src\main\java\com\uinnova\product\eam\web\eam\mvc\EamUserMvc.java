package com.uinnova.product.eam.web.eam.mvc;

import com.binary.core.util.BinaryUtils;
import com.binary.framework.web.RemoteResult;
import com.uinnova.product.eam.base.model.RoleQueryDto;
import com.uinnova.product.eam.base.model.UserQueryDto;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uino.api.client.permission.IRoleApiSvc;
import com.uino.bean.permission.base.SysRole;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.permission.business.UserInfo;
import com.uino.bean.permission.query.CSysUser;
import com.uino.service.permission.microservice.IUserSvc;
import com.uino.util.sys.SysUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/eam/user")
public class EamUserMvc {

    @Autowired
    private IUserSvc userSvc;

    @Autowired
    private IRoleApiSvc roleApiSvc;

    @GetMapping("/getUserByRoleName")
    public String getUserByRoleName(@RequestParam String roleName) {
        List<SysUser> userList = userSvc.getUserByRoleNameAndCount(roleName, 200);
        String userIds = "";
        if (CollectionUtils.isNotEmpty(userList)) {
            List<String> collect = userList.stream().map(SysUser::getLoginCode).collect(Collectors.toList());
            userIds = StringUtils.join(collect, ",");
        }
        return userIds;
    }

    @PostMapping("/getUserByLoginCodes")
    @ModDesc(desc = "通过LoginCode批量查询用户信息", pDesc = "loginCode数组", rDesc = "用户列表", rType = RemoteResult.class)
    public RemoteResult getUserByLoginCodes(@RequestBody UserQueryDto dto) {
        if(BinaryUtils.isEmpty(dto.getLoginCodes())){
            return new RemoteResult(new ArrayList<>());
        }
        CSysUser userCdt = new CSysUser();
        userCdt.setLoginCodes(dto.getLoginCodes());
        List<UserInfo> userList = userSvc.getUserInfoByCdt(userCdt, true);
        return new RemoteResult(userList);
    }


    @PostMapping("/getRoleListByIds")
    @ModDesc(desc = "通过角色ID批量查询角色信息", pDesc = "角色ID", rDesc = "角色列表", rType = RemoteResult.class)
    public RemoteResult getRoleListByIds(@RequestBody RoleQueryDto dto) {
        if(BinaryUtils.isEmpty(dto.getRoleIds())){
            return new RemoteResult(new ArrayList<>());
        }
        List<Long> roleIdList = dto.getRoleIds();
        List<SysRole> sysRoles = roleApiSvc.getRoleListByIds(roleIdList, SysUtil.getCurrentUserInfo().getDomainId());
        return new RemoteResult(sysRoles);
    }

}
