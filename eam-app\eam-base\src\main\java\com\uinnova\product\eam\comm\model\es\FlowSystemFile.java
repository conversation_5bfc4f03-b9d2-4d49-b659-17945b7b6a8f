package com.uinnova.product.eam.comm.model.es;

import com.uinnova.product.eam.base.cj.BaseEntity;
import lombok.*;

import java.util.Objects;

/**
 * description
 *
 * <AUTHOR>
 * @since 2024/5/28 13:46
 */
@Getter
@Setter
@RequiredArgsConstructor
@ToString
public class FlowSystemFile {

    private Long id;

    private String ciCode;

    private String objective;

    private String overview;

    /**
     * 流程范围
     */
    private String flowSystemRange;

    /**
     * 活动说明
     */
    private String activeIllustrate;

    /**
     * 关键活动
     */
    private String keyActivities;

    /**
     * 裁剪规则
     */
    private String croppingRules;

    /**
     * 流程关键评测指标版
     */
    private String processKeyEvaluation;

    /**
     * 流程接口描述
     */
    private String flowInterfaceDescription;

    /**
     * 补充说明
     */
    private String additionalNotes;

    /**
     * 状态 0：未发布 1：已发布 2：历史
     */
    private Integer status;

    /**
     * 版本id
     */
    private Long versionId;


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        FlowSystemFile that = (FlowSystemFile) o;
        return Objects.equals(ciCode, that.ciCode) && Objects.equals(objective, that.objective) && Objects.equals(overview, that.overview) && Objects.equals(flowSystemRange, that.flowSystemRange) && Objects.equals(activeIllustrate, that.activeIllustrate) && Objects.equals(keyActivities, that.keyActivities) && Objects.equals(croppingRules, that.croppingRules) && Objects.equals(processKeyEvaluation, that.processKeyEvaluation) && Objects.equals(flowInterfaceDescription, that.flowInterfaceDescription) && Objects.equals(additionalNotes, that.additionalNotes);
    }

    @Override
    public int hashCode() {
        return Objects.hash(ciCode, objective, overview, flowSystemRange, activeIllustrate, keyActivities, croppingRules, processKeyEvaluation, flowInterfaceDescription, additionalNotes);
    }
}
