package com.uino.bean.cmdb.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
/**
 * @Title: ESShowDocumentAttributeDto
 **/

@ApiModel(value = "创建分类是否增加文档属性", description = "创建分类是否增加文档属性")
public class ESShowDocumentAttributeDto {
    @ApiModelProperty(value = "创建分类是否增加文档属性", example = "false")
    private Boolean isShowDocumentAttribute = false;

    public void setShowDocumentAttribute(Boolean showDocumentAttribute) {
        isShowDocumentAttribute = showDocumentAttribute;
    }

    public Boolean getShowDocumentAttribute() {
        return isShowDocumentAttribute;
    }
}














