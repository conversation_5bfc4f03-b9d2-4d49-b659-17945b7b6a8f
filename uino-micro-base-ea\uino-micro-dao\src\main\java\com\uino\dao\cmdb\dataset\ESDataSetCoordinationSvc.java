package com.uino.dao.cmdb.dataset;

import com.alibaba.fastjson.JSONObject;
import com.uino.dao.AbstractESBaseDao;
import com.uino.dao.cmdb.ESVisualModelSvc;
import com.uino.bean.cmdb.base.dataset.DataSetCoordination;
import com.uino.dao.ESConst;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.stereotype.Repository;

import jakarta.annotation.PostConstruct;

/**
 * @Classname DataSetCoordinationDao
 * @Description 协作权限
 * @Date 2020/3/19 10:05
 * @Created by sh
 */
@Repository
public class ESDataSetCoordinationSvc extends AbstractESBaseDao<DataSetCoordination, JSONObject> {

    Log logger = LogFactory.getLog(ESVisualModelSvc.class);

    @Override
    public String getIndex() {
        return ESConst.INDEX_CMDB_DATASET_COORDINATION;
    }

    @Override
    public String getType() {
        return ESConst.INDEX_CMDB_DATASET_COORDINATION;
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }
}

