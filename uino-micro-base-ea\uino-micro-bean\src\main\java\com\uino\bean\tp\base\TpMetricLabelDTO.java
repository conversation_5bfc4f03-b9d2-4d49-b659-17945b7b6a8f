package com.uino.bean.tp.base;

import com.binary.framework.bean.annotation.Comment;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* @Description:  指标的标签
* @Param:
* @return:
* @Author: lizipeng
* @Date: 2020/6/18
*/
@Data
@ApiModel(value = "指标的标签", description = "指标的标签")
public class TpMetricLabelDTO {
    private Long id;

    @Comment("标签名称")
    @ApiModelProperty(value = "标签名称")
    private String name;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "修改人")
    private String modifier;

    @ApiModelProperty(value = "创建时间")
    private Long createTime;

    @ApiModelProperty(value = "修改时间")
    private Long modifyTime;
}
