package com.binary.framework.spring;

import java.io.IOException;
import java.io.InputStream;
import java.net.URL;

import org.springframework.web.multipart.MultipartFile;

import com.binary.core.io.support.AbstractResource;
import com.binary.framework.exception.ServiceException;

public class MultipartFileResource extends AbstractResource {
	
	private MultipartFile formFile;
	
	
	public MultipartFileResource(MultipartFile formFile) {
		this.formFile = formFile;
	}
	

	@Override
	public boolean exists() {
		return !formFile.isEmpty();
	}

	
	
		
	@Override
	public String getPath() {
		return formFile.getOriginalFilename();
	}

	@Override
	public URL getURL() {
		return null;
	}

	
	
	@Override
	public InputStream getInputStream() {
		try {
			return formFile.getInputStream();
		} catch (IOException e) {
			throw new ServiceException(e);
		}
	}
	
	

}
