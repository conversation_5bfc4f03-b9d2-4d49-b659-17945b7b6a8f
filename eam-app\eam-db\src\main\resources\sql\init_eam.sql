/*Table structure for table `vc_base_config` */

#注：一下四条为容器环境下自动创建用户使用，升级mysql镜像时需要加上下面四条sql
# grant all on *.* to 'uinnova';
# create database `db_vmdb` default character set utf8mb4 collate utf8mb4_general_ci;
# create database `eam_flowable` default character set utf8mb4 collate utf8mb4_general_ci;
# use db_vmdb;

DROP TABLE IF EXISTS `vc_base_config`;

CREATE TABLE `vc_base_config` (
  `ID` decimal(16,0) NOT NULL COMMENT 'ID',
  `CFG_CODE` varchar(40) DEFAULT NULL COMMENT '配置代码',
  `CFG_CONTENT` varchar(4000) DEFAULT NULL COMMENT '配置内容',
  `DOMAIN_ID` decimal(16,0) NOT NULL COMMENT '所属域',
  `DATA_STATUS` decimal(2,0) NOT NULL DEFAULT '1' COMMENT '数据状态:数据状态：1-正常 0-删除',
  `CREATOR` varchar(40) DEFAULT NULL COMMENT '创建人',
  `MODIFIER` varchar(40) DEFAULT NULL COMMENT '修改人',
  `CREATE_TIME` decimal(16,0) NOT NULL COMMENT '创建时间:yyyyMMddHHmmss',
  `MODIFY_TIME` decimal(16,0) NOT NULL COMMENT '修改时间:yyyyMMddHHmmss',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='基础配置表';

/*Data for the table `vc_base_config` */

/*Table structure for table `vc_ci_linked` */

DROP TABLE IF EXISTS `vc_ci_linked`;

CREATE TABLE `vc_ci_linked` (
  `ID` decimal(16,0) NOT NULL COMMENT 'ID',
  `LINKED_NAME` varchar(100) DEFAULT NULL,
  `SOURCE_TYPE` decimal(2,0) DEFAULT NULL COMMENT '1=视图',
  `SOURCE_ID` decimal(16,0) DEFAULT NULL,
  `CI_CODES` varchar(4000) DEFAULT NULL COMMENT '逗号分隔',
  `DOMAIN_ID` decimal(16,0) NOT NULL COMMENT '所属域',
  `DATA_STATUS` decimal(2,0) NOT NULL DEFAULT '1' COMMENT '数据状态:数据状态：1-正常 0-删除',
  `CREATOR` varchar(40) DEFAULT NULL COMMENT '创建人',
  `MODIFIER` varchar(40) DEFAULT NULL COMMENT '修改人',
  `CREATE_TIME` decimal(16,0) NOT NULL COMMENT '创建时间:yyyyMMddHHmmss',
  `MODIFY_TIME` decimal(16,0) NOT NULL COMMENT '修改时间:yyyyMMddHHmmss',
  `linked_color` varchar(40) DEFAULT NULL,
  `custom_1` varchar(100) DEFAULT NULL,
  `custom_2` varchar(100) DEFAULT NULL,
  `custom_3` varchar(100) DEFAULT NULL,
  `custom_4` varchar(100) DEFAULT NULL,
  `custom_5` varchar(100) DEFAULT NULL,
  `custom_6` varchar(100) DEFAULT NULL,
  `IS_DISPLAY` decimal(1,0) DEFAULT NULL COMMENT '是否显示当前链路到视图(1=显示,0不显示)',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='CI链路表';

/*Data for the table `vc_ci_linked` */

/*Table structure for table `vc_comment` */

DROP TABLE IF EXISTS `vc_comment`;

CREATE TABLE `vc_comment` (
  `ID` decimal(16,0) DEFAULT NULL COMMENT 'ID',
  `DIAGRAM_ID` decimal(16,0) DEFAULT NULL COMMENT '视图ID',
  `USER_ID` decimal(16,0) NOT NULL COMMENT '评论用户ID',
  `USER_CODE` varchar(40) DEFAULT NULL COMMENT '评论用户代码',
  `USER_NAME` varchar(40) DEFAULT NULL COMMENT '评论用户姓名',
  `COM_TIME` decimal(16,0) DEFAULT NULL COMMENT '评论时间',
  `COM_DESC` varchar(4000) DEFAULT NULL COMMENT '评论内容',
  `DOMAIN_ID` decimal(16,0) NOT NULL COMMENT '所属域',
  `CREATE_TIME` decimal(16,0) NOT NULL COMMENT '创建时间:yyyyMMddHHmmss',
  `MODIFY_TIME` decimal(16,0) NOT NULL COMMENT '修改时间:yyyyMMddHHmmss',
  KEY `AK_PK_VC_COMMENT` (`ID`),
  KEY `IDX_VC_COMMENT_DIAGRAM_ID` (`DIAGRAM_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='视图评论表';

/*Data for the table `vc_comment` */

/*Table structure for table `vc_diagram` */

DROP TABLE IF EXISTS `vc_diagram`;

CREATE TABLE `vc_diagram` (
  `ID` decimal(16,0) NOT NULL COMMENT 'ID',
  `NAME` varchar(100) DEFAULT NULL COMMENT '视图名称',
  `USER_ID` decimal(16,0) DEFAULT NULL COMMENT '所属用户',
  `DIR_ID` decimal(16,0) DEFAULT NULL COMMENT '所属目录',
  `SUBJECT_ID` decimal(16,0) DEFAULT '0' COMMENT '主题目录',
  `DIR_TYPE` decimal(2,0) COMMENT '目录类型[DIR_TYPE]',
  `DIAGRAM_TYPE` decimal(2,0) DEFAULT NULL COMMENT '视图类型:1=单图    2=组合视图    3=视图模版',
  `DIAGRAM_DESC` varchar(200) DEFAULT NULL COMMENT '视图描述',
  `DIAGRAM_SVG` varchar(200) DEFAULT NULL COMMENT '视图SVG',
  `DIAGRAM_XML` varchar(200) DEFAULT NULL COMMENT '视图XML',
  `DIAGRAM_JSON` varchar(200) COMMENT '视图json格式信息',
  `DIAGRAM_BG_IMG` varchar(200) DEFAULT NULL COMMENT '背景图',
  `DIAGRAM_BG_CSS` varchar(200) DEFAULT NULL COMMENT '背景样式',
  `ICON_1` varchar(200) DEFAULT NULL COMMENT '视图图标_1',
  `ICON_2` varchar(200) DEFAULT NULL COMMENT '视图图标_2',
  `ICON_3` varchar(200) DEFAULT NULL COMMENT '视图图标_3',
  `ICON_4` varchar(200) DEFAULT NULL COMMENT '视图图标_4',
  `ICON_5` varchar(200) DEFAULT NULL COMMENT '视图图标_5',
  `IS_OPEN` decimal(1,0) DEFAULT NULL COMMENT '是否公开:1=开放    0=私有',
  `OPEN_TIME` decimal(16,0) DEFAULT NULL COMMENT '公开时间',
  `DATA_UP_TYPE` decimal(2,0) DEFAULT NULL COMMENT '数据驱动类型:1=不更新 2=标记提示  3=自动更新',
  `STATUS` decimal(1,0) DEFAULT NULL COMMENT '视图状态:1=正常    0=回收站',
  `CI_3D_POINT` varchar(200) DEFAULT NULL COMMENT 'CI3D坐标',
  `CI_3D_POINT2` varchar(200) DEFAULT NULL COMMENT 'CI3D坐标2',
  `SEARCH_FIELD` varchar(4000) DEFAULT NULL COMMENT '搜索字段:视图名称',
  `COMB_ROWS` decimal(2,0) DEFAULT NULL COMMENT '组合视图行数:组合视图字段',
  `COMB_COLS` decimal(2,0) DEFAULT NULL COMMENT '组合视图列数:组合视图字段',
  `DOMAIN_ID` decimal(16,0) NOT NULL COMMENT '所属域',
  `DATA_STATUS` decimal(1,0) NOT NULL COMMENT '数据状态:0=删除，1=正常',
  `CREATOR` varchar(40) DEFAULT NULL COMMENT '创建人',
  `MODIFIER` varchar(40) DEFAULT NULL COMMENT '修改人',
  `CREATE_TIME` decimal(16,0) NOT NULL COMMENT '创建时间:yyyyMMddHHmmss',
  `MODIFY_TIME` decimal(16,0) NOT NULL COMMENT '更新时间:yyyyMMddHHmmss',
  `READ_COUNT` decimal(16,0) DEFAULT '0',
  `APP_RLT_CI_CODE` varchar(100) DEFAULT NULL,
  `REFER_VERSION_ID` decimal(16,0) COMMENT '参照视图版本id',
  PRIMARY KEY (`ID`),
  KEY `IDX_VC_DIAGRAM_USER_ID` (`USER_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='视图设计表';


/*Data for the table `vc_diagram` */

/*Table structure for table `vc_diagram_ci` */

DROP TABLE IF EXISTS `vc_diagram_ci`;

CREATE TABLE `vc_diagram_ci` (
  `DIAGRAM_ID` decimal(16,0) NOT NULL COMMENT '视图ID',
  `CI_ID` varchar(100) NOT NULL COMMENT 'CI_ID',
  `DOMAIN_ID` decimal(16,0) NOT NULL COMMENT '所属域',
  `CREATE_TIME` decimal(16,0) NOT NULL COMMENT '创建时间:yyyyMMddHHmmss',
  `MODIFY_TIME` decimal(16,0) NOT NULL COMMENT '修改时间:yyyyMMddHHmmss',
  PRIMARY KEY (`DIAGRAM_ID`,`CI_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='视图CI关系表';

/*Data for the table `vc_diagram_ci` */

/*Table structure for table `vc_diagram_ci_attr_disp` */

DROP TABLE IF EXISTS `vc_diagram_ci_attr_disp`;

CREATE TABLE `vc_diagram_ci_attr_disp` (
  `ID` decimal(16,0) NOT NULL COMMENT 'ID',
  `DIAGRAM_ID` decimal(16,0) DEFAULT NULL COMMENT '视图ID',
  `OBJ_TYPE` decimal(2,0) DEFAULT NULL COMMENT '配置类型:1=CI分类    2=CI',
  `OBJ_ID` varchar(100) DEFAULT NULL COMMENT '配置对象:配置类型为1时为ClassID，类型为2时为CI_ID',
  `ATTR_DEF_ID` decimal(16,0) DEFAULT NULL COMMENT '属性定义ID',
  `DOMAIN_ID` decimal(16,0) NOT NULL COMMENT '所属域',
  `CREATE_TIME` decimal(16,0) NOT NULL COMMENT '创建时间:yyyyMMddHHmmss',
  `MODIFY_TIME` decimal(16,0) NOT NULL COMMENT '修改时间:yyyyMMddHHmmss',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='视图CI属性显示表';

/*Data for the table `vc_diagram_ci_attr_disp` */

/*Table structure for table `vc_diagram_ci_attr_version` */

DROP TABLE IF EXISTS `vc_diagram_ci_attr_version`;

CREATE TABLE `vc_diagram_ci_attr_version` (
  `ID` decimal(16,0) NOT NULL COMMENT 'ID',
  `DIAGRAM_ID` decimal(16,0) DEFAULT NULL COMMENT '视图历史ID',
  `OBJ_TYPE` decimal(2,0) DEFAULT NULL COMMENT '配置类型:1=CI分类    2=CI',
  `OBJ_ID` varchar(100) DEFAULT NULL COMMENT '配置对象:配置类型为1时为ClassID，类型为2时为CI_ID',
  `ATTR_DEF_ID` decimal(16,0) DEFAULT NULL COMMENT '属性定义ID',
  `DOMAIN_ID` decimal(16,0) NOT NULL COMMENT '所属域',
  `CREATE_TIME` decimal(16,0) NOT NULL COMMENT '创建时间:yyyyMMddHHmmss',
  `MODIFY_TIME` decimal(16,0) NOT NULL COMMENT '修改时间:yyyyMMddHHmmss',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='视图CI属性显示版本表';

/*Data for the table `vc_diagram_ci_attr_version` */

/*Table structure for table `vc_diagram_dir` */

DROP TABLE IF EXISTS `vc_diagram_dir`;

CREATE TABLE `vc_diagram_dir` (
  `ID` decimal(16,0) NOT NULL COMMENT 'ID',
  `DIR_NAME` varchar(100) DEFAULT NULL COMMENT '目录名称',
  `DIR_TYPE` decimal(16, 0) NULL DEFAULT NULL COMMENT '目录类型',
  `SUBJECT_ID` decimal(16,0) DEFAULT '0' COMMENT '主题目录',
  `PARENT_ID` decimal(16,0) DEFAULT NULL COMMENT '上级目录ID',
  `USER_ID` decimal(16,0) DEFAULT NULL COMMENT '所属用户ID',
  `DIR_LVL` decimal(2,0) DEFAULT NULL COMMENT '目录层级级别',
  `DIR_PATH` varchar(500) DEFAULT NULL COMMENT '目录层级路径:例：#1#2#7#',
  `ORDER_NO` decimal(8,0) DEFAULT NULL COMMENT '显示排序',
  `IS_LEAF` decimal(1,0) DEFAULT NULL COMMENT '是否末级:1=是    0=否',
  `ICON` varchar(200) DEFAULT NULL COMMENT '目录图标',
  `DIR_DESC` varchar(500) DEFAULT NULL COMMENT '目录描述',
  `DOMAIN_ID` decimal(16,0) NOT NULL COMMENT '所属域',
  `DATA_STATUS` decimal(1,0) NOT NULL COMMENT '数据状态:0=删除，1=正常',
  `CREATOR` varchar(40) DEFAULT NULL COMMENT '创建人',
  `MODIFIER` varchar(40) DEFAULT NULL COMMENT '修改人',
  `CREATE_TIME` decimal(16,0) NOT NULL COMMENT '创建时间:yyyyMMddHHmmss',
  `MODIFY_TIME` decimal(16,0) NOT NULL COMMENT '更新时间:yyyyMMddHHmmss',
  `OLD_PARENT_ID` decimal(16, 0) NULL COMMENT '回收站记录原来的父类id',
  `DIR_INIT` decimal(16, 0) NULL COMMENT '是否初始目录  1=是，0=否',
  `ES_SYS_ID` varchar(100) NULL DEFAULT NULL COMMENT '关联es系统ciCode',
  `SYS_TYPE` varchar(20) NULL COMMENT '系统类型',
  `SYS_DIR` tinyint(1) NULL COMMENT '是否为系统文件夹 0:否，1:是',
  `STATE` decimal(1, 0) NULL COMMENT '0:私有态;1:设计态;2:运行态;',
  PRIMARY KEY (`ID`),
  KEY `IDX_VC_DIAGRAM_DIR_PARENT_ID` (`PARENT_ID`),
  KEY `IDX_VC_DIAGRAM_DIR_USER_ID` (`USER_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='视图目录表';


/*Data for the table `vc_diagram_dir` */
INSERT INTO `vc_diagram_dir` VALUES (1, '架构愿景', 1, 0, 0, 1, NULL, NULL, 1, NULL, NULL, NULL, 1, 1, 'admin', 'admin', 20200320003327, 20200723163134, null, 1, null, null, 0, 2);
INSERT INTO `vc_diagram_dir` VALUES (2, '业务架构', 1, 0, 0, 1, NULL, NULL, 1, NULL, NULL, NULL, 1, 1, 'admin', 'admin', 20200320003327, 20200722165617, null, 1, null, null, 0, 2);
INSERT INTO `vc_diagram_dir` VALUES (3, '应用架构', 1, 0, 0, 1, NULL, NULL, 1, NULL, NULL, NULL, 1, 1, 'admin', 'admin', 20200320003327, 20200723134756, null, 1, null, null, 0, 2);
INSERT INTO `vc_diagram_dir` VALUES (4, '数据架构', 1, 0, 0, 1, NULL, NULL, 1, NULL, NULL, NULL, 1, 1, 'admin', 'admin', 20200320003327, 20200721135614, null, 1, null, null, 0, 2);
INSERT INTO `vc_diagram_dir` VALUES (5, '技术架构', 1, 0, 0, 1, NULL, NULL, 1, NULL, NULL, NULL, 1, 1, 'admin', 'admin', 20210524003327, 20210524135614, null, 1, null, null, 0, 2);
INSERT INTO `vc_diagram_dir` VALUES (6, '架构实现', 1, 0, 0, 1, NULL, NULL, 1, NULL, NULL, NULL, 1, 1, 'admin', 'admin', 20210524003327, 20210524135614, null, 1, null, null, 0, 2);
INSERT INTO `vc_diagram_dir` VALUES (7, '业务架构设计', 2, 0, 0, 1, 0, NULL, 1, NULL, NULL, NULL, 1, 1, 'admin', 'admin', 20200320003324, 20200320003324, null, null, null, null, 0, 2);
INSERT INTO `vc_diagram_dir` VALUES (8, '应用架构设计', 2, 0, 0, 1, 0, NULL, 2, NULL, NULL, NULL, 1, 1, 'admin', 'admin', 20200320003325, 20200320003325, null, null, null, null, 0, 2);
INSERT INTO `vc_diagram_dir` VALUES (9, '技术架构设计', 2, 0, 0, 1, 0, NULL, 3, NULL, NULL, NULL, 1, 1, 'admin', 'admin', 20200320003326, 20200320003326, null, null, null, null, 0, 2);
INSERT INTO `vc_diagram_dir` VALUES (10, '数据架构设计', 2, 0, 0, 1, 0, NULL, 4, NULL, NULL, NULL, 1, 1, 'admin', 'admin', 20200320003327, 20200320003327, null, null, null, null, 0, 2);
INSERT INTO `vc_diagram_dir` VALUES (11, '方案设计', 2, 0, 0, 1, 0, NULL, 5, NULL, NULL, NULL, 1, 1, 'admin', 'admin', 20210524003328, 20210524003328, null, null, null, null, 0, 2);
INSERT INTO `vc_diagram_dir` VALUES (12, '技术架构设计空间', 111, 0, -111, 1, 1, '#5#', NULL, 1, NULL, NULL, 1, 1, 'admin', 'admin', 20220412153335, 20220412155707, NULL, 0, NULL, NULL, 0, 2);
INSERT INTO `vc_diagram_dir` VALUES (13, '数据架构设计空间', 101, 0, -101, 1, 1, '#4#', NULL, 1, NULL, NULL, 1, 1, 'admin', 'admin', 20220412153335, 20220412155707, NULL, 0, NULL, NULL, 0, 2);
INSERT INTO `vc_diagram_dir` VALUES (14, '业务组件建模空间', 100, 0, -100, 1, 1, '#1#', NULL, 1, NULL, NULL, 1, 1, 'admin', 'admin', 20220412153335, 20220412155707, NULL, 0, NULL, NULL, 0, 2);
INSERT INTO `vc_diagram_dir` VALUES (15, '应用架构设计空间', 11, 0, -11, 1, 1, '#3#', NULL, 1, NULL, NULL, 1, 1, 'admin', 'admin', 20220412153335, 20220412155707, NULL, 0, NULL, NULL, 0, 2);
INSERT INTO `vc_diagram_dir` VALUES (16, '其他业务架构设计空间', 1, 0, -1, 1, 1, '#2#', NULL, 1, NULL, NULL, 1, 1, 'admin', 'admin', 20220412153335, 20220412155707, NULL, 0, NULL, NULL, 0, 2);
INSERT INTO `vc_diagram_dir` VALUES (17, '架构方案设计空间', 110, 0, -110, 1, 1, '#17#', NULL, 1, NULL, NULL, 1, 1, 'admin', 'admin', 20220412153335, 20220412155707, NULL, 0, NULL, NULL, 0, 2);
INSERT INTO `vc_diagram_dir` VALUES (1087638491356966, '企业级', 11, 0, 1, 1, 1, '#1087638491356966#', NULL, 1, NULL, NULL, 1, 1, 'admin', 'admin', 20221014153022, 20221014153159, NULL, 0, NULL, NULL, 0, NULL);
INSERT INTO `vc_diagram_dir` VALUES (1087638491356968, '系统级', 11, 0, 1, 1, 1, '#1087638491356968#', NULL, 0, NULL, NULL, 1, 1, 'admin', 'admin', 20221014153030, 20221014153159, NULL, 0, NULL, NULL, 0, NULL);
INSERT INTO `vc_diagram_dir` VALUES (1087638491356992, '客户信息管理（示例）', 11, 0, 1087638491356968, 1, 2, '#1087638491356968#1087638491356992#', NULL, 0, NULL, NULL, 1, 1, 'admin', 'admin', 20221014153151, 20221019213108, NULL, 0, NULL, NULL, 0, NULL);
INSERT INTO `vc_diagram_dir` VALUES (1087638491356996, '系统服务（示例）', 11, 0, 1087638491356992, 1, 3, '#1087638491356968#1087638491356992#1087638491356996#', NULL, 1, NULL, NULL, 1, 1, 'admin', 'admin', 20221014153212, 20221019142723, NULL, 0, NULL, NULL, 0, NULL);


/*Table structure for table `vc_diagram_ele` */

DROP TABLE IF EXISTS `vc_diagram_ele`;

CREATE TABLE `vc_diagram_ele` (
  `ID` decimal(16,0) NOT NULL COMMENT 'ID',
  `DIAGRAM_ID` decimal(16,0) DEFAULT NULL COMMENT '视图ID',
  `ELE_TYPE` decimal(2,0) DEFAULT NULL COMMENT '元素类型:1=CI  2=标签  3=搜索表达式',
  `ELE_ID` varchar(100) DEFAULT NULL COMMENT '元素ID',
  `ELE_CODE` VARCHAR(2000) COMMENT '元素code',
  `DOMAIN_ID` decimal(16,0) NOT NULL COMMENT '所属域',
  `CREATE_TIME` decimal(16,0) NOT NULL COMMENT '创建时间:yyyyMMddHHmmss',
  `MODIFY_TIME` decimal(16,0) NOT NULL COMMENT '修改时间:yyyyMMddHHmmss',
  `RLT_DIAGRAM_IDS` varchar(4000) DEFAULT NULL,
  PRIMARY KEY (`ID`),
  KEY `IDX_VC_DIAGRAM_ELE_ELE` (`ELE_TYPE`,`ELE_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='视图元素表';

/*Data for the table `vc_diagram_ele` */

/*Table structure for table `vc_diagram_ele_version` */

DROP TABLE IF EXISTS `vc_diagram_ele_version`;

CREATE TABLE `vc_diagram_ele_version` (
  `ID` decimal(16,0) NOT NULL COMMENT 'ID',
  `DIAGRAM_ID` decimal(16,0) DEFAULT NULL COMMENT '视图历史ID',
  `ELE_TYPE` decimal(2,0) DEFAULT NULL COMMENT '元素类型:1=CI  2=标签',
  `ELE_ID` varchar(100) DEFAULT NULL COMMENT '元素ID',
  `ELE_CODE` VARCHAR(2000) COMMENT '元素code',
  `DOMAIN_ID` decimal(16,0) NOT NULL COMMENT '所属域',
  `CREATE_TIME` decimal(16,0) NOT NULL COMMENT '创建时间:yyyyMMddHHmmss',
  `MODIFY_TIME` decimal(16,0) NOT NULL COMMENT '修改时间:yyyyMMddHHmmss',
  PRIMARY KEY (`ID`),
  KEY `IDX_VC_DIAGRAM_ELE_VERSION_ELE` (`ELE_TYPE`,`ELE_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='视图元素版本表';

/*Data for the table `vc_diagram_ele_version` */

/*Table structure for table `vc_diagram_ensh` */

DROP TABLE IF EXISTS `vc_diagram_ensh`;

CREATE TABLE `vc_diagram_ensh` (
  `ID` decimal(16,0) NOT NULL COMMENT 'ID',
  `USER_ID` decimal(16,0) DEFAULT NULL COMMENT '用户ID',
  `DIAGRAM_ID` decimal(16,0) DEFAULT NULL COMMENT '视图ID',
  `ORDER_NO` decimal(8,0) DEFAULT NULL COMMENT '排序号',
  `DOMAIN_ID` decimal(16,0) NOT NULL COMMENT '所属域',
  `DATA_STATUS` decimal(2,0) NOT NULL DEFAULT '1' COMMENT '数据状态:数据状态：1-正常 0-删除',
  `CREATE_TIME` decimal(16,0) NOT NULL COMMENT '创建时间:yyyyMMddHHmmss',
  `MODIFY_TIME` decimal(16,0) NOT NULL COMMENT '修改时间:yyyyMMddHHmmss',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='视图收藏表';

/*Data for the table `vc_diagram_ensh` */

/*Table structure for table `vc_diagram_group` */

DROP TABLE IF EXISTS `vc_diagram_group`;

CREATE TABLE `vc_diagram_group` (
  `ID` decimal(16,0) NOT NULL COMMENT 'ID',
  `DIAGRAM_ID` decimal(16,0) DEFAULT NULL COMMENT '视图ID',
  `GROUP_ID` decimal(16,0) DEFAULT NULL COMMENT '组ID',
  `DEPLOY_USER_ID` decimal(16,0) DEFAULT NULL COMMENT '发布人',
  `DEPLOY_TIME` decimal(16,0) DEFAULT NULL COMMENT '发布时间',
  `DOMAIN_ID` decimal(16,0) NOT NULL COMMENT '所属域',
  `CREATE_TIME` decimal(16,0) NOT NULL COMMENT '创建时间:yyyyMMddHHmmss',
  `MODIFY_TIME` decimal(16,0) NOT NULL COMMENT '修改时间:yyyyMMddHHmmss',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='视图组表';

/*Data for the table `vc_diagram_group` */

/*Table structure for table `vc_diagram_item_opte` */

DROP TABLE IF EXISTS `vc_diagram_item_opte`;

CREATE TABLE `vc_diagram_item_opte` (
  `ID` decimal(16,0) NOT NULL COMMENT 'ID',
  `OP_NAME` varchar(200) DEFAULT NULL COMMENT '操作名称',
  `OP_TYPE` decimal(2,0) DEFAULT NULL COMMENT '操作类型:待定',
  `CLASS_ID` decimal(16,0) DEFAULT NULL COMMENT '所属分类',
  `OP_EXP` varchar(1000) DEFAULT NULL COMMENT '操作表达式',
  `OP_DESC` varchar(500) DEFAULT NULL COMMENT '操作描述',
  `DOMAIN_ID` decimal(16,0) NOT NULL COMMENT '所属域',
  `DATA_STATUS` decimal(2,0) NOT NULL DEFAULT '1' COMMENT '数据状态:数据状态：1-正常 0-删除',
  `CREATOR` varchar(40) DEFAULT NULL COMMENT '创建人',
  `MODIFIER` varchar(40) DEFAULT NULL COMMENT '修改人',
  `CREATE_TIME` decimal(16,0) NOT NULL COMMENT '创建时间:yyyyMMddHHmmss',
  `MODIFY_TIME` decimal(16,0) NOT NULL COMMENT '修改时间:yyyyMMddHHmmss',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='视图图标操作表';

/*Data for the table `vc_diagram_item_opte` */

/*Table structure for table `vc_diagram_tag` */

DROP TABLE IF EXISTS `vc_diagram_tag`;

CREATE TABLE `vc_diagram_tag` (
  `ID` decimal(16,0) NOT NULL COMMENT 'ID',
  `TAG_ID` decimal(16,0) NOT NULL COMMENT '标签ID',
  `DIAGRAM_ID` decimal(16,0) NOT NULL COMMENT '视图ID',
  `DOMAIN_ID` decimal(16,0) NOT NULL COMMENT '所属域',
  `CREATE_TIME` decimal(16,0) NOT NULL COMMENT '创建时间:yyyyMMddHHmmss',
  `MODIFY_TIME` decimal(16,0) NOT NULL COMMENT '修改时间:yyyyMMddHHmmss',
  PRIMARY KEY (`ID`),
  KEY `IDX_VC_DIAGRAM_TAG_TAG_ID` (`TAG_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='视图标签表';

/*Data for the table `vc_diagram_tag` */

/*Table structure for table `vc_diagram_version` */

DROP TABLE IF EXISTS `vc_diagram_version`;

CREATE TABLE `vc_diagram_version` (
  `ID` decimal(16,0) NOT NULL COMMENT 'ID',
  `DIAGRAM_ID` decimal(16,0) DEFAULT NULL COMMENT '源视图ID',
  `VERSION_NO` varchar(40) DEFAULT NULL COMMENT '版本号',
  `VERSION_TIME` decimal(16,0) DEFAULT NULL COMMENT '版本时间',
  `VERSION_DESC` varchar(4000) DEFAULT NULL COMMENT '版本描述',
  `VERSION_NAME` varchar(100) DEFAULT NULL COMMENT '版本名称',
  `NAME` varchar(100) DEFAULT NULL COMMENT '视图名称',
  `USER_ID` decimal(16,0) DEFAULT NULL COMMENT '所属用户',
  `DIR_ID` decimal(16,0) DEFAULT NULL COMMENT '所属目录',
  `DIAGRAM_DESC` varchar(200) DEFAULT NULL COMMENT '视图描述',
  `DIAGRAM_SVG` varchar(200) DEFAULT NULL COMMENT '视图SVG',
  `DIAGRAM_XML` varchar(200) DEFAULT NULL COMMENT '视图XML',
  `DIAGRAM_JSON` varchar(200) COMMENT '视图json格式信息',
  `ICON_1` varchar(200) DEFAULT NULL COMMENT '视图图标_1',
  `ICON_2` varchar(200) DEFAULT NULL COMMENT '视图图标_2',
  `ICON_3` varchar(200) DEFAULT NULL COMMENT '视图图标_3',
  `ICON_4` varchar(200) DEFAULT NULL COMMENT '视图图标_4',
  `ICON_5` varchar(200) DEFAULT NULL COMMENT '视图图标_5',
  `STATUS` decimal(1,0) DEFAULT NULL COMMENT '视图状态:1=正常    0=回收站',
  `CI_3D_POINT` varchar(200) DEFAULT NULL COMMENT 'CI3D坐标',
  `CI_3D_POINT2` varchar(200) DEFAULT NULL COMMENT 'CI3D坐标2',
  `DIAGRAM_BG_CSS` varchar(200) COMMENT '背景颜色',
  `DOMAIN_ID` decimal(16,0) NOT NULL COMMENT '所属域',
  `VERSION_DESC_PATH` varchar(200) COMMENT '版本描述文件路径信息',
  `DATA_STATUS` decimal(1,0) NOT NULL COMMENT '数据状态:0=删除，1=正常',
  `CREATOR` varchar(40) DEFAULT NULL COMMENT '创建人',
  `MODIFIER` varchar(40) DEFAULT NULL COMMENT '修改人',
  `CREATE_TIME` decimal(16,0) NOT NULL COMMENT '创建时间:yyyyMMddHHmmss',
  `MODIFY_TIME` decimal(16,0) NOT NULL COMMENT '更新时间:yyyyMMddHHmmss',
  `RELEASE_ID` decimal(16, 0) NULL COMMENT '关联视图发布id',
  `RELEASE_VERSION` decimal(16, 0) NULL COMMENT '已发布视图版本',
  `AUTO_CREAT` decimal(16, 0) NULL COMMENT '是否为自动创建 是：1/null 否：2',
  PRIMARY KEY (`ID`),
  KEY `IDX_VC_DIAGRAM_VERSION_USER_ID` (`USER_ID`),
  KEY `IDX_VC_DIAGRAM_VERSION_DIAGRAM_ID` (`DIAGRAM_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='视图设计版本表';

/*Data for the table `vc_diagram_version` */


/*Table structure for table `vc_feedback` */

DROP TABLE IF EXISTS `vc_feedback`;

CREATE TABLE `vc_feedback` (
  `ID` decimal(16,0) NOT NULL,
  `USER_ID` decimal(16,0) DEFAULT NULL,
  `USER_CODE` varchar(100) DEFAULT NULL,
  `USER_NAME` varchar(100) DEFAULT NULL,
  `FEEDBACK_TIME` decimal(16,0) DEFAULT NULL,
  `FEEDBACK_TYPE` decimal(8,0) DEFAULT NULL,
  `CONTENT` varchar(4000) DEFAULT NULL,
  `DOMAIN_ID` decimal(16,0) NOT NULL,
  `DATA_STATUS` decimal(2,0) NOT NULL DEFAULT '1',
  `CREATE_TIME` decimal(16,0) NOT NULL,
  `MODIFY_TIME` decimal(16,0) NOT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='系统反馈表';

/*Data for the table `vc_feedback` */

/*Table structure for table `vc_group` */

DROP TABLE IF EXISTS `vc_group`;

CREATE TABLE `vc_group` (
  `ID` decimal(16,0) NOT NULL COMMENT 'ID',
  `GROUP_NAME` varchar(100) DEFAULT NULL COMMENT '组名称',
  `GROUP_DESC` varchar(500) DEFAULT NULL COMMENT '组描述',
  `AUTH_REGION` decimal(2,0) DEFAULT NULL COMMENT '权限范围:1=全部',
  `GROUP_IMAGE` varchar(100) DEFAULT NULL COMMENT '组图标',
  `DOMAIN_ID` decimal(16,0) NOT NULL COMMENT '所属域',
  `DATA_STATUS` decimal(2,0) NOT NULL DEFAULT '1' COMMENT '数据状态:数据状态：1-正常 0-删除',
  `CREATOR` varchar(40) DEFAULT NULL COMMENT '创建人',
  `MODIFIER` varchar(40) DEFAULT NULL COMMENT '修改人',
  `CREATE_TIME` decimal(16,0) NOT NULL COMMENT '创建时间:yyyyMMddHHmmss',
  `MODIFY_TIME` decimal(16,0) NOT NULL COMMENT '修改时间:yyyyMMddHHmmss',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='组表';

/*Data for the table `vc_group` */

/*Table structure for table `vc_group_log` */

DROP TABLE IF EXISTS `vc_group_log`;

CREATE TABLE `vc_group_log` (
  `ID` decimal(16,0) NOT NULL,
  `GROUP_ID` decimal(16,0) NOT NULL COMMENT '组ID',
  `LOG_TIME` decimal(16,0) DEFAULT NULL COMMENT '日志时间:yyyyMMddHHmmss',
  `OP_ID` decimal(16,0) DEFAULT NULL COMMENT '操作人ID',
  `OP_NAME` varchar(100) DEFAULT NULL COMMENT '操作人名称',
  `SOURCE_TYPE` decimal(2,0) NOT NULL COMMENT '来源类别:1=组视图    2=组人',
  `LOG_TYPE` decimal(2,0) DEFAULT NULL COMMENT '日志类型:1=添加  2=修改  3=删除    4=查询',
  `LOG_DESC` varchar(2000) DEFAULT NULL COMMENT '日志描述',
  `DOMAIN_ID` decimal(16,0) NOT NULL COMMENT '所属域',
  `CREATE_TIME` decimal(16,0) NOT NULL COMMENT '创建时间:yyyyMMddHHmmss',
  `MODIFY_TIME` decimal(16,0) NOT NULL COMMENT '修改时间:yyyyMMddHHmmss',
  KEY `AK_PK_VC_GROUP_LOG` (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='组操作记录表';

/*Data for the table `vc_group_log` */

/*Table structure for table `vc_group_user` */

DROP TABLE IF EXISTS `vc_group_user`;

CREATE TABLE `vc_group_user` (
  `ID` decimal(16,0) NOT NULL COMMENT 'ID',
  `GROUP_ID` decimal(16,0) DEFAULT NULL COMMENT '组ID',
  `USER_ID` decimal(16,0) DEFAULT NULL COMMENT '用户ID',
  `AUTH_REGION` decimal(2,0) DEFAULT NULL COMMENT '权限范围:1=全部    2=只读',
  `DOMAIN_ID` decimal(16,0) NOT NULL COMMENT '所属域',
  `CREATE_TIME` decimal(16,0) NOT NULL COMMENT '创建时间:yyyyMMddHHmmss',
  `MODIFY_TIME` decimal(16,0) NOT NULL COMMENT '修改时间:yyyyMMddHHmmss',
  PRIMARY KEY (`ID`),
  KEY `VC_GROUP_USER_USER_ID` (`USER_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户组表';

/*Data for the table `vc_group_user` */

/*Table structure for table `vc_tag` */

DROP TABLE IF EXISTS `VC_TAG`;

CREATE TABLE `VC_TAG` (
  `ID` decimal(16,0) NOT NULL COMMENT 'ID',
  `TAG_NAME` varchar(100) COMMENT '标签名称',
  `TAG_TYPE` decimal(2,0) DEFAULT NULL COMMENT '标签类型:1=视图标签',
  `TAG_ATTR` decimal(2,0) COMMENT '标签属性:0=无，1=不能删除，2=不能删除且为团队 3=不能删除且为广场',
  `PARENT_ID` decimal(16,0) DEFAULT NULL COMMENT '父标签id',
  `DEF_DESC` varchar(500) DEFAULT NULL COMMENT '定义描述',
  `DOMAIN_ID` decimal(16,0) NOT NULL COMMENT '所属域',
  `DATA_STATUS` decimal(2,0) NOT NULL DEFAULT '1' COMMENT '数据状态:数据状态：1-正常 0-删除',
  `CREATOR` varchar(40) DEFAULT NULL COMMENT '创建人',
  `MODIFIER` varchar(40) DEFAULT NULL COMMENT '修改人',
  `CREATE_TIME` decimal(16,0) NOT NULL COMMENT '创建时间:yyyyMMddHHmmss',
  `MODIFY_TIME` decimal(16,0) NOT NULL COMMENT '修改时间:yyyyMMddHHmmss',
  PRIMARY KEY (`ID`),
  KEY `IDX_VC_TAG_TAG_NAME` (`TAG_NAME`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='标签表';

/*Data for the table `vc_tag` */
INSERT INTO VC_TAG (ID, TAG_NAME, TAG_TYPE, TAG_ATTR, PARENT_ID, DEF_DESC, DOMAIN_ID, DATA_STATUS, CREATOR, MODIFIER, CREATE_TIME, MODIFY_TIME) VALUES ('1', 'DMV_ATLAS', '2', '1', '0', NULL, '1', '1', '管理员', '管理员', '20180802120000', '20180802120000');
INSERT INTO VC_TAG (ID, TAG_NAME, TAG_TYPE, TAG_ATTR, PARENT_ID, DEF_DESC, DOMAIN_ID, DATA_STATUS, CREATOR, MODIFIER, CREATE_TIME, MODIFY_TIME) VALUES ('2', 'EMV_TEAME', '2', '2', '1', NULL, '1', '1', '管理员', '管理员', '20180802120000', '20180802120000');
INSERT INTO VC_TAG (ID, TAG_NAME, TAG_TYPE, TAG_ATTR, PARENT_ID, DEF_DESC, DOMAIN_ID, DATA_STATUS, CREATOR, MODIFIER, CREATE_TIME, MODIFY_TIME) VALUES ('3', 'COMMON_VIEW_TYPE', '2', '1', '1', '', '1', '1', '管理员', '管理员', '20180802120000', '20180802120000');
INSERT INTO VC_TAG (ID, TAG_NAME, TAG_TYPE, TAG_ATTR, PARENT_ID, DEF_DESC, DOMAIN_ID, DATA_STATUS, CREATOR, MODIFIER, CREATE_TIME, MODIFY_TIME) VALUES ('4', 'DMV_BUSINESS_FLOWCHART', '2', '0', '3', NULL, '1', '1', '管理员', '管理员', '20180802120000', '20180802120000');
INSERT INTO VC_TAG (ID, TAG_NAME, TAG_TYPE, TAG_ATTR, PARENT_ID, DEF_DESC, DOMAIN_ID, DATA_STATUS, CREATOR, MODIFIER, CREATE_TIME, MODIFY_TIME) VALUES ('5', 'DMV_DEPLOYMENT_ARCHITECTURE_DIAGRAM', '2', '0', '3', NULL, '1', '1', '管理员', '管理员', '20180802120000', '20180802120000');
INSERT INTO VC_TAG (ID, TAG_NAME, TAG_TYPE, TAG_ATTR, PARENT_ID, DEF_DESC, DOMAIN_ID, DATA_STATUS, CREATOR, MODIFIER, CREATE_TIME, MODIFY_TIME) VALUES ('6', 'DMV_LOGICAL_DIAGRAM', '2', '0', '3', NULL, '1', '1', '管理员', '管理员', '20180802120000', '20180802120000');
INSERT INTO VC_TAG (ID, TAG_NAME, TAG_TYPE, TAG_ATTR, PARENT_ID, DEF_DESC, DOMAIN_ID, DATA_STATUS, CREATOR, MODIFIER, CREATE_TIME, MODIFY_TIME) VALUES ('7', 'DMV_NETWORK_TOPOLOGY2', '2', '0', '3', NULL, '1', '1', '管理员', '管理员', '20180802120000', '20180802120000');
INSERT INTO VC_TAG (ID, TAG_NAME, TAG_TYPE, TAG_ATTR, PARENT_ID, DEF_DESC, DOMAIN_ID, DATA_STATUS, CREATOR, MODIFIER, CREATE_TIME, MODIFY_TIME) VALUES ('8', 'DMV_SQUARE_VIEWS', '2', '3', '1', NULL, '1', '1', '管理员', 'admin', '20180802120000', '20181106175218');
INSERT INTO VC_TAG (ID, TAG_NAME, TAG_TYPE, TAG_ATTR, PARENT_ID, DEF_DESC, DOMAIN_ID, DATA_STATUS, CREATOR, MODIFIER, CREATE_TIME, MODIFY_TIME) VALUES ('9', 'COMMON_MY_VIEW', '2', '4', '1', NULL, '1', '1', '管理员', 'admin', '20180802120000', '20181106175218');


/*Table structure for table `vc_theme` */
DROP TABLE IF EXISTS `vc_theme`;

CREATE TABLE `vc_theme` (
  `ID` decimal(16,0) NOT NULL COMMENT 'ID',
  `THEME_NAME` varchar(200) DEFAULT NULL COMMENT '主题名称',
  `THEME_TYPE` decimal(2,0) DEFAULT NULL COMMENT '主题类型:待定',
  `THEME_DESC` varchar(500) DEFAULT NULL COMMENT '主题描述',
  `DOMAIN_ID` decimal(16,0) NOT NULL COMMENT '所属域',
  `DATA_STATUS` decimal(2,0) NOT NULL DEFAULT '1' COMMENT '数据状态:数据状态：1-正常 0-删除',
  `CREATOR` varchar(40) DEFAULT NULL COMMENT '创建人',
  `MODIFIER` varchar(40) DEFAULT NULL COMMENT '修改人',
  `CREATE_TIME` decimal(16,0) NOT NULL COMMENT '创建时间:yyyyMMddHHmmss',
  `MODIFY_TIME` decimal(16,0) NOT NULL COMMENT '修改时间:yyyyMMddHHmmss',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='主题表';

/*Data for the table `vc_theme` */

/*Table structure for table `vc_theme_diagram` */

DROP TABLE IF EXISTS `vc_theme_diagram`;

CREATE TABLE `vc_theme_diagram` (
  `ID` decimal(16,0) NOT NULL COMMENT 'ID',
  `THEME_ID` decimal(16,0) DEFAULT NULL COMMENT '所属主题',
  `DIAGRAM_ID` decimal(16,0) DEFAULT NULL COMMENT '视图ID',
  `DIAGRAM_TYPE` decimal(2,0) DEFAULT NULL COMMENT '视图类型:1=单图    2=组合视图',
  `DIAGRAM_XML` varchar(200) DEFAULT NULL COMMENT '视图XML',
  `DOMAIN_ID` decimal(16,0) NOT NULL COMMENT '所属域',
  `DATA_STATUS` decimal(2,0) NOT NULL DEFAULT '1' COMMENT '数据状态:数据状态：1-正常 0-删除',
  `CREATOR` varchar(40) DEFAULT NULL COMMENT '创建人',
  `MODIFIER` varchar(40) DEFAULT NULL COMMENT '修改人',
  `CREATE_TIME` decimal(16,0) NOT NULL COMMENT '创建时间:yyyyMMddHHmmss',
  `MODIFY_TIME` decimal(16,0) NOT NULL COMMENT '修改时间:yyyyMMddHHmmss',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='主题视图表';

/*Data for the table `vc_theme_diagram` */

/*Table structure for table `vc_theme_field` */

DROP TABLE IF EXISTS `vc_theme_field`;

CREATE TABLE `vc_theme_field` (
  `ID` decimal(16,0) NOT NULL COMMENT 'ID',
  `THEME_ID` decimal(16,0) DEFAULT NULL COMMENT '所属主题',
  `CLASS_ID` decimal(16,0) DEFAULT NULL COMMENT '所属分类',
  `FIELD_TYPE` decimal(2,0) DEFAULT NULL COMMENT '字段类型:1=分类属性    2=KPI',
  `FIELD_ID` decimal(16,0) DEFAULT NULL COMMENT '字段ID',
  `FIELD_NAME` varchar(200) DEFAULT NULL COMMENT '字段名称',
  `FIELD_DESC` varchar(500) DEFAULT NULL COMMENT '字段描述',
  `IS_SHOW` decimal(2,0) DEFAULT 1 COMMENT '查看时是否显示0=否 1=是',
  `SHOW_UNIT` varchar(20) COMMENT '显示单位',
  `DOMAIN_ID` decimal(16,0) NOT NULL COMMENT '所属域',
  `DATA_STATUS` decimal(2,0) NOT NULL DEFAULT '1' COMMENT '数据状态:数据状态：1-正常 0-删除',
  `CREATOR` varchar(40) DEFAULT NULL COMMENT '创建人',
  `MODIFIER` varchar(40) DEFAULT NULL COMMENT '修改人',
  `CREATE_TIME` decimal(16,0) NOT NULL COMMENT '创建时间:yyyyMMddHHmmss',
  `MODIFY_TIME` decimal(16,0) NOT NULL COMMENT '修改时间:yyyyMMddHHmmss',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='主题字段表';

/*Data for the table `vc_theme_field` */

/*Table structure for table `vc_diagram_log` */
DROP TABLE IF EXISTS `vc_diagram_log`;

CREATE TABLE `vc_diagram_log` (
`ID`  decimal(16,0) NOT NULL ,
`DIAGRAM_ID`  decimal(16,0) NOT NULL ,
`LOG_TIME`  decimal(16,0) NULL DEFAULT NULL ,
`OP_ID`  decimal(16,0) NULL DEFAULT NULL ,
`OP_NAME`  varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL ,
`SOURCE_TYPE`  decimal(2,0) NULL DEFAULT NULL ,
`OP_TYPE`  decimal(2,0) NULL DEFAULT NULL ,
`LOG_DESC`  varchar(2000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL ,
`DOMAIN_ID`  decimal(16,0) NOT NULL COMMENT '所属域' ,
`CREATE_TIME`  decimal(16,0) NOT NULL COMMENT '创建时间:yyyyMMddHHmmss' ,
`MODIFY_TIME`  decimal(16,0) NOT NULL COMMENT '修改时间:yyyyMMddHHmmss' ,
PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

/*Table structure for table `vc_diagram_op` */
DROP TABLE IF EXISTS `vc_diagram_op`;

CREATE TABLE `vc_diagram_op` (
`ID`  decimal(16,0) NOT NULL ,
`DIAGRAM_ID`  decimal(16,0) NOT NULL ,
`SOURCE_ID`  decimal(16,0) NOT NULL ,
`SOURCE_TYPE`  decimal(2,0) NOT NULL ,
`OP_TYPE`  decimal(2,0) NOT NULL ,
`OP_DESC`  varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL ,
`DOMAIN_ID`  decimal(16,0) NOT NULL COMMENT '所属域' ,
`CREATE_TIME`  decimal(16,0) NOT NULL COMMENT '创建时间:yyyyMMddHHmmss' ,
`MODIFY_TIME`  decimal(16,0) NOT NULL COMMENT '修改时间:yyyyMMddHHmmss' ,
PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;



DROP TABLE IF EXISTS `vc_node_linked`;
create table `vc_node_linked`  (
  `ID` decimal(16,0) NOT NULL,
  `LINKED_NAME` varchar(200) COMMENT '链路名称',
  `START_CODE` varchar(200) NOT NULL COMMENT '开始节点代码',
  `END_CODE` varchar(200) NOT NULL COMMENT '结束节点代码',
  `LINKED_DESC` varchar(1000) NOT NULL COMMENT '链路描述',
  `RELATED_APP_NAME` varchar(200) COMMENT '关联应用名称息',
  `DOMAIN_ID` decimal(16,0) NOT NULL COMMENT '所属域',                   
  `DATA_STATUS` decimal(1,0) NOT NULL DEFAULT '1' COMMENT '数据状态:1-正常  0-删除',
  `CREATOR` varchar(40) DEFAULT NULL COMMENT '创建人',
  `MODIFIER` varchar(40) DEFAULT NULL COMMENT '修改人',
  `CREATE_TIME` decimal(16,0) NOT NULL COMMENT '创建时间:yyyyMMddHHmmss',
  `MODIFY_TIME` decimal(16,0) NOT NULL COMMENT '修改时间:yyyyMMddHHmmss',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='节点链路表';

DROP TABLE IF EXISTS `vc_node_linked_path`;
create table vc_node_linked_path  (
  `ID` decimal(16,0) NOT NULL,
  `LINKED_ID` decimal(16,0) COMMENT '节点路径ID',
  `NODE_CODES` varchar(4000) NOT NULL COMMENT '节点代码列表用''隔开',
  `PATH_ORDER` decimal(2,0) NOT NULL COMMENT '节点列表序号',
  `DOMAIN_ID` decimal(16,0) NOT NULL COMMENT '所属域',                   
  `CREATE_TIME` decimal(16,0) NOT NULL COMMENT '创建时间:yyyyMMddHHmmss',
  `MODIFY_TIME` decimal(16,0) NOT NULL COMMENT '修改时间:yyyyMMddHHmmss',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='节点路径详情表';


DROP TABLE IF EXISTS `vc_diagram_dir_relation`;
create table `vc_diagram_dir_relation`  (
   `ID` decimal(16,0) NOT NULL,
   `DIR_ID` decimal(16,0) NOT NULL COMMENT '目录ID',
   `DIR_TYPE` decimal(2,0) COMMENT '目录类型[DIR_TYPE]',
   `DIAGRAM_ID` decimal(16,0) NOT NULL COMMENT '视图ID',
   `DOMAIN_ID` decimal(16,0) NOT NULL COMMENT '所属域',   
   `CREATE_TIME` decimal(16,0) NOT NULL COMMENT '创建时间:yyyyMMddHHmmss',
   `MODIFY_TIME` decimal(16,0) NOT NULL COMMENT '修改时间:yyyyMMddHHmmss',
   PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='视图目录关系表';


DROP TABLE IF EXISTS `vc_class_add_attr`;
create table `vc_class_add_attr`  (
   `ID`	decimal(16,0) NOT NULL COMMENT 'ID',
   `CLASS_ID` decimal(16,0) COMMENT '分类ID',
   `CLASS_TYPE` decimal(2,0) COMMENT '分类类型',
   `ADD_ATTR_1` varchar(2000) COMMENT '附加属性1',
   `ADD_ATTR_2` varchar(2000) COMMENT '附加属性2',
   `ADD_ATTR_3` varchar(2000) COMMENT '附加属性3',
   `ADD_ATTR_4` varchar(2000) COMMENT '附加属性4',
   `ADD_ATTR_5` varchar(2000) COMMENT '附加属性5',
   `ADD_ATTR_6` varchar(2000) COMMENT '附加属性6',
   `DOMAIN_ID` decimal(16,0) NOT NULL COMMENT '所属域',                   
   `DATA_STATUS` decimal(1,0) NOT NULL DEFAULT '1' COMMENT '数据状态:1-正常  0-删除',
   `CREATOR` varchar(40) DEFAULT NULL COMMENT '创建人',
   `MODIFIER` varchar(40) DEFAULT NULL COMMENT '修改人',
   `CREATE_TIME` decimal(16,0) NOT NULL COMMENT '创建时间:yyyyMMddHHmmss',
   `MODIFY_TIME` decimal(16,0) NOT NULL COMMENT '修改时间:yyyyMMddHHmmss',
   PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='分类附加属性表';


DROP TABLE IF EXISTS `vc_meta_model_diagram`;
create table `vc_meta_model_diagram`  (
   `ID` decimal(16,0) not null COMMENT '创建人',
   `NAME` varchar(200) COMMENT '模型名称',
   `USER_ID` decimal(16,0) not null COMMENT '用户ID',
   `IS_OPEN` decimal(2,0) COMMENT '是否发布0=否 1=是',
   `MODEL_JSON` varchar(200) not null COMMENT '模型JSON',
   `DOMAIN_ID` decimal(16,0) NOT NULL COMMENT '所属域',                   
   `DATA_STATUS` decimal(1,0) NOT NULL DEFAULT '1' COMMENT '数据状态:1-正常  0-删除',
   `CREATOR` varchar(40) DEFAULT NULL COMMENT '创建人',
   `MODIFIER` varchar(40) DEFAULT NULL COMMENT '修改人',
   `CREATE_TIME` decimal(16,0) NOT NULL COMMENT '创建时间:yyyyMMddHHmmss',
   `MODIFY_TIME` decimal(16,0) NOT NULL COMMENT '修改时间:yyyyMMddHHmmss',
   PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='元模型管理视图';


DROP TABLE IF EXISTS `vc_role_org`;
create table `vc_role_org` (
   `ID` decimal(16,0) not null COMMENT 'ID',
   `ROLE_ID` decimal(16,0) not null COMMENT '角色id',
   `ORG_NAMES` varchar(500) COMMENT '组织名称，多个用逗号隔开',
   `SEE_AUTH` decimal(1,0) COMMENT '查看的权限',
   `EDIT_AUTH` decimal(1,0) COMMENT '编辑的权限',
   `DOMAIN_ID` decimal(16,0) not null COMMENT '所属域',
   `DATA_STATUS` decimal(2,0) not null COMMENT '数据状态:0=删除，1=正常',
   `CREATOR` varchar(40) COMMENT '创建人',
   `MODIFIER` varchar(40) COMMENT '修改人',
   `CREATE_TIME` decimal(16,0) not null COMMENT '创建时间:yyyyMMddHHmmss',
   `MODIFY_TIME` decimal(16,0) not null COMMENT '更新时间:yyyyMMddHHmmss',
   PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='角色组织表';



/*==============================================================*/
/* Table: VC_USER_PV_COUNT                                      */
/*==============================================================*/
drop table if exists VC_USER_PV_COUNT;
create table VC_USER_PV_COUNT
(
   ID                   numeric(16,0) not null comment 'ID',
   DOMAIN_ID            numeric(16,0) not null comment '所属域',
   DATA_STATUS          numeric(1,0) not null comment '数据状态:0=删除，1=正常',
   CREATE_TIME          numeric(16,0) not null comment '创建时间:yyyyMMddHHmmss',
   MODIFY_TIME          numeric(16,0) not null comment '更新时间:yyyyMMddHHmmss',
   USER_ID              numeric(16,0) not null comment '用户id',
   TARGET_DESC          varchar(200) comment '行为描述',
   TARGET_CODE          varchar(200) not null comment '行为编码',
   COUNT_DATA_TIME      numeric(16,0) not null comment '统计数据时间yyyyMMdd',
   PC_COUNT             numeric(16,0) not null comment '操作次数',
   primary key (ID)
);

alter table VC_USER_PV_COUNT comment '用户行为计数表';

/*==============================================================*/
/* Table: VC_DIAGRAM_NOTIFY                                      */
/*==============================================================*/
DROP TABLE IF EXISTS VC_DIAGRAM_NOTIFY;
CREATE TABLE VC_DIAGRAM_NOTIFY
(
   ID                   NUMERIC(16,0) NOT NULL COMMENT 'ID',
   DOMAIN_ID            NUMERIC(16,0) NOT NULL COMMENT '所属域',
   CREATOR              VARCHAR(40) COMMENT '创建人',
   MODIFIER             VARCHAR(40) COMMENT '修改人',
   CREATE_TIME          NUMERIC(16,0) NOT NULL COMMENT '创建时间:yyyyMMddHHmmss',
   MODIFY_TIME          NUMERIC(16,0) NOT NULL COMMENT '更新时间:yyyyMMddHHmmss',
   USER_ID              NUMERIC(16,0) COMMENT '用户id',
   DIAGRAM_ID           NUMERIC(16,0) COMMENT '视图id',
   DIAGRAM_NAME         varchar(100) comment '视图名称',
   PRIMARY KEY (ID)
);

ALTER TABLE VC_DIAGRAM_NOTIFY COMMENT '视图信息通知表';

drop table if exists VC_DIAGRAM_OPERATION_RECORD;

/*==============================================================*/
/* Table: VC_DIAGRAM_OPERATION_RECORD                           */
/*==============================================================*/
create table VC_DIAGRAM_OPERATION_RECORD
(
   ID                   numeric(16,0) not null comment 'ID',
   DOMAIN_ID            numeric(16,0) not null comment '所属域',
   CREATOR              varchar(40) comment '创建人',
   MODIFIER             varchar(40) comment '修改人',
   CREATE_TIME          numeric(16,0) not null comment '创建时间:yyyyMMddHHmmss',
   MODIFY_TIME          numeric(16,0) not null comment '更新时间:yyyyMMddHHmmss',
   USER_ID              numeric(16,0) comment '用户id',
   DIAGRAM_ID           numeric(16,0) comment '视图id',
   OPERATION_TYPE       numeric(2,0) comment '视图名称',
   primary key (ID)
);

alter table VC_DIAGRAM_OPERATION_RECORD comment '视图相关操作记录表';


/*==============================================================*/
/* Index: unite_index_01                                        */
/*==============================================================*/

create index unite_index_01 on VC_USER_PV_COUNT
(
   DOMAIN_ID,
   USER_ID,
   COUNT_DATA_TIME,
   TARGET_CODE
);


