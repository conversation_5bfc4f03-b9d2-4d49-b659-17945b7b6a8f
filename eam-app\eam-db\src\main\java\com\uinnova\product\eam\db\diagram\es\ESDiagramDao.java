package com.uinnova.product.eam.db.diagram.es;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.binary.core.util.BinaryUtils;

import com.uinnova.product.eam.base.diagram.model.ESDiagram;
import com.uinnova.product.eam.base.diagram.model.EamDiagramQuery;
import com.uino.dao.AbstractESBaseDao;
import com.uino.service.util.FileUtil;
import org.apache.commons.lang3.StringEscapeUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

import jakarta.annotation.PostConstruct;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Classname Es视图数据访问层
 * @Description 该类用来操作es中的视图index
 * <AUTHOR>
 * @Date 2021-06-02-16:25
 */
@Repository
public class ESDiagramDao extends AbstractESBaseDao<ESDiagram, EamDiagramQuery> {
    @Override
    public String getIndex() {
        return "uino_monet_diagram";
    }

    @Override
    public String getType() {
        return "_doc";
    }

    @PostConstruct
    public void init() {
        List<ESDiagram> list = FileUtil.getData("/initdata/uino_monet_diagram.json", ESDiagram.class);
        super.initIndex(list);
    }

    private static final Logger log = LoggerFactory.getLogger(ESDiagramDao.class);


    public List<Long> remove(){
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.mustNot(QueryBuilders.termsQuery("diagramType",new Integer[]{3,4}));
        List<ESDiagram> esDiagrams = selectListByQuery(1, 3000, boolQuery);
        List<Long> ids = esDiagrams.stream().map(ESDiagram::getId).collect(Collectors.toList());
        deleteByQuery(boolQuery,true);
        return ids;
    }

    public int clearReleaseDiagramId(Collection<String> diagramIds){
        String script = "ctx._source.releaseDiagramId=null;ctx._source.releaseVersion=0";
        boolean suc = updateByQuery(QueryBuilders.termsQuery("releaseDiagramId.keyword", diagramIds), script, true);
        return suc ? 1 : 0;
    }


    public int updateNameByDEnergyId(String newName, String dEnergyId){
        Map<String,Object> params = new HashMap<>();
        params.put("newName",newName);
        String script = "ctx._source.name=params.newName;";
        boolean suc = updateByQuery(QueryBuilders.termQuery("dEnergy.keyword", dEnergyId), script, true, params);
        return suc ? 1 : 0;
    }

    public void updatePrivateDiagramIdVersion(Long id, Integer releaseVersion) {
        String script = "ctx._source.releaseVersion=params.releaseVersion;ctx._source.localVersion=0;";
        Map<String,Object> params = new HashMap<>();
        params.put("releaseVersion", releaseVersion);
        updateByQuery(QueryBuilders.termQuery("id", id), script, true, params);
    }

    public void releasePrivateDiagramIdVersion(Long id) {
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.must(QueryBuilders.termQuery("id", id));
        boolQuery.must(QueryBuilders.existsQuery("releaseVersion"));
        long num = countByCondition(boolQuery);
        String script = "ctx._source.localVersion=0;ctx._source.releaseVersion+=1;";
        if(num <= 0L){
            script = "ctx._source.localVersion=0;ctx._source.releaseVersion=1;";
        }
        updateByQuery(QueryBuilders.termQuery("id", id), script, true);
    }

    public void writeReleaseDiagramId(String releaseDiagramId, Long id) {
        String script = "ctx._source.releaseDiagramId=params.releaseDiagramId;";
        Map<String,Object> params = new HashMap<>();
        params.put("releaseDiagramId", releaseDiagramId);
        updateByQuery(QueryBuilders.termQuery("id", id), script, true, params);
    }

    public int increaseReleaseVersionByDEnergyId(String dEnergyId){
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.must(QueryBuilders.termQuery("dEnergy.keyword", dEnergyId));
        boolQuery.must(QueryBuilders.existsQuery("releaseVersion"));
        long num = countByCondition(boolQuery);
        String script = "ctx._source.releaseVersion+=1;";
        if(num <= 0L){
            script = "ctx._source.releaseVersion=1;";
        }
        boolean suc = updateByQuery(QueryBuilders.termQuery("dEnergy.keyword", dEnergyId), script, true);
        return suc ? 1 : 0;
    }

    public int reSetReleaseVersionByDEnergyId(Integer releaseVersion, String dEnergyId){
        String script = "ctx._source.releaseVersion=params.releaseVersion;";
        Map<String,Object> params = new HashMap<>();
        params.put("releaseVersion", releaseVersion);
        boolean suc = updateByQuery(QueryBuilders.termQuery("dEnergy.keyword", dEnergyId), script, true, params);
        return suc ? 1 : 0;
    }

    /**
     * <b>保存对象
     *
     * @param esDiagram, isRefresh
     * @return
     */
    public Long saveOrUpdateWithOption(ESDiagram esDiagram, Boolean isRefresh) {
        esDiagram.setReleaseDiagramId(null);
        esDiagram.setLocalVersion(null);
        esDiagram.setReleaseVersion(null);
        JSONObject obj = (JSONObject)JSON.toJSON(esDiagram);
        return this.saveOrUpdate(obj, isRefresh);
    }

    public Long updateBatchThumbnailTime(ESDiagram updater, Boolean isRefresh) {
        Map<String,Object> params = new HashMap<>();
        params.put("thumbnailSaveTime", updater.getThumbnailSaveTime());
        params.put("icon1", StringEscapeUtils.escapeJava(updater.getIcon1()));
        params.put("modifier", updater.getModifier());
        params.put("modifyTime", updater.getModifyTime());
        String script = "ctx._source.thumbnailSaveTime=params.thumbnailSaveTime;ctx._source.icon1=params.icon1;ctx._source.modifier=params.modifier;ctx._source.modifyTime=params.modifyTime;";
        updateByQuery(QueryBuilders.termQuery("id", updater.getId()), script, isRefresh, params);
        return updater.getId();
    }

    public int updateModifyTime(Long id) {
        Map<String,Object> params = new HashMap<>();
        params.put("modifyTime", BinaryUtils.getNumberDateTime());
        String script = "ctx._source.modifyTime=params.modifyTime;";
        boolean suc = updateByQuery(QueryBuilders.termQuery("id", id), script, true, params);
        return suc ? 1 : 0;
    }

    public int increaseVersionAndTime(Long id) {
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.must(QueryBuilders.termQuery("id", id));
        boolQuery.must(QueryBuilders.existsQuery("localVersion"));

        Map<String,Object> params = new HashMap<>();
        params.put("modifyTime", BinaryUtils.getNumberDateTime());

        long num = countByCondition(boolQuery);
        String script = "ctx._source.localVersion+=1;ctx._source.modifyTime=params.modifyTime;";
        if(num <= 0L){
            script = "ctx._source.localVersion=1;ctx._source.modifyTime=params.modifyTime;";
        }
        boolean suc = updateByQuery(QueryBuilders.termQuery("id", id), script, true, params);
        return suc ? 1 : 0;
    }

    /**
     * <b>批量保存
     *
     * @param list
     * @return
     */
    public Integer saveOrUpdateBatchWithOption(List<ESDiagram> list) {
        if (BinaryUtils.isEmpty(list)) {
            return 1;
        }
        list.forEach(obj -> {
            savePreOption(obj);
            obj.setReleaseDiagramId(null);
            obj.setLocalVersion(null);
            obj.setReleaseVersion(null);
        });
        JSONArray arr = JSON.parseArray(JSON.toJSONString(list));
        return this.saveOrUpdateBatch(arr, true);
    }

    /**
     * 视图模板相互转换拓展方法
     * @param esDiagram
     * @param isRefresh
     * @return
     */
    public Long saveOrUpdateWithOptionExtra(ESDiagram esDiagram, Boolean isRefresh) {
        JSONObject obj = (JSONObject)JSON.toJSON(esDiagram);
        return this.saveOrUpdate(obj, isRefresh);
    }

}
