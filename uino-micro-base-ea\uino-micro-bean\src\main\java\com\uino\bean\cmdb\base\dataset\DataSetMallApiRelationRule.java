package com.uino.bean.cmdb.base.dataset;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.uino.bean.cmdb.base.dataset.relation.RelationRuleNode;
import com.uino.bean.cmdb.base.dataset.relation.RelationRuleAttrCdt;
import com.uino.bean.cmdb.base.dataset.relation.RelationRuleAttrCdtOp;
import com.uino.bean.cmdb.base.dataset.relation.RelationRuleLine;
import com.uino.bean.cmdb.business.dataset.RltRuleInfo;

import java.util.*;

/**
 * @Classname RelationRuleDataSetMallApi
 * @Description TODO
 * @Date 2020/3/19 9:32
 * @Created by sh
 */
public class DataSetMallApiRelationRule extends DataSetMallApi {
    private Long classId;
    private Long pageNodeId;
    private List<RelationRuleNode> nodes;
    private List<RelationRuleLine> lines;


    public Long getClassId() {
        return classId;
    }

    public void setClassId(Long classId) {
        this.classId = classId;
    }

    public Long getPageNodeId() {
        return pageNodeId;
    }

    public void setPageNodeId(Long pageNodeId) {
        this.pageNodeId = pageNodeId;
    }

    public List<RelationRuleNode> getNodes() {
        return nodes;
    }

    public void setNodes(List<RelationRuleNode> nodes) {
        this.nodes = nodes;
    }

    public List<RelationRuleLine> getLines() {
        return lines;
    }

    public void setLines(List<RelationRuleLine> lines) {
        this.lines = lines;
    }


    public DataSetMallApiRelationRule() {
    }

    public DataSetMallApiRelationRule(JSONObject json) {
        super(json);
        if (json.containsKey("classId")) {
            this.classId = json.getLong("classId");
        }
        if (json.containsKey("pageNodeId")) {
            this.pageNodeId = json.getLong("pageNodeId");
        }
        if (json.containsKey("nodes") && json.getJSONArray("nodes") != null) {
            this.nodes = new ArrayList<>();
            for (int i = 0; i < json.getJSONArray("nodes").size(); i++) {
                this.nodes.add(new RelationRuleNode(json.getJSONArray("nodes").getJSONObject(i)));
            }
        }
        if (json.containsKey("lines") && json.getJSONArray("lines") != null) {
            this.lines = new ArrayList<>();
            for (int i = 0; i < json.getJSONArray("lines").size(); i++) {
                this.lines.add(new RelationRuleLine(json.getJSONArray("lines").getJSONObject(i)));
            }
        }
    }

    @Override
    public JSONObject toJson() {
        JSONObject json = super.toJson();
        json.put("classId", classId);
        json.put("pageNodeId", pageNodeId);
        if (nodes != null) {
            JSONArray arr = new JSONArray();
            for (RelationRuleNode node : nodes) {
                arr.add(node.toJson());
            }
            json.put("nodes", arr);
        }
        if (lines != null) {
            JSONArray arr = new JSONArray();
            for (RelationRuleLine line : lines) {
                arr.add(line.toJson());
            }
            json.put("lines", arr);
        }
        return json;
    }

    public RltRuleInfo convert2RuleInfo() {
        Long maxPageNodeId = -1L;
        for (RelationRuleNode node : nodes) {
            if (node.getPageNodeId() > maxPageNodeId) {
                maxPageNodeId = node.getPageNodeId();
            }
        }
        Map<Long, Map<Integer, RelationRuleLine>> downMap = new HashMap<>();
        Map<Long, Map<Integer, RelationRuleLine>> upMap = new HashMap<>();
        if (lines != null) {
            for (Integer i = 0; i < lines.size(); i++) {
                RelationRuleLine line = lines.get(i);
                Map<Integer, RelationRuleLine> downs = downMap.get(line.getNodeStartId());
                if (downs == null) {
                    downs = new HashMap<>();
                    downMap.put(line.getNodeStartId(), downs);
                }
                downs.put(i, line);
                Map<Integer, RelationRuleLine> ups = upMap.get(line.getNodeEndId());
                if (ups == null) {
                    ups = new HashMap<>();
                    upMap.put(line.getNodeEndId(), ups);
                }
                ups.put(i, line);
            }
        }

        RltRuleInfo ruleInfo = new RltRuleInfo();
        ruleInfo.setName(this.getName());
        ruleInfo.setDesc(this.getDescription());
        ruleInfo.setStartPageNodeId(this.pageNodeId);
        ruleInfo.setStartClassId(this.classId);
        ruleInfo.setDomainId(this.getDomainId());


        List<RltRuleInfo.NodeInfo> nodeInfoList = new ArrayList<>();
        Map<Long, RltRuleInfo.NodeInfo> andMap = new HashMap<>();
        Map<Long, RltRuleInfo.NodeInfo> orMap = new HashMap<>();
        Map<Long, RltRuleInfo.LineInfo> andLineMap = new HashMap<>();
        Map<Long, RltRuleInfo.LineInfo> orLineMap = new HashMap<>();
        for (RelationRuleNode node : nodes) {
            RltRuleInfo.Node nodeInfo = new RltRuleInfo.Node();
            nodeInfo.setPageNodeId(node.getPageNodeId());
            nodeInfo.setClassId(node.getClassId());
            nodeInfo.setNodeType(1);
            nodeInfo.setLogicOperation(0);
            nodeInfo.setX(node.getX().intValue());
            nodeInfo.setY(node.getY().intValue());
            nodeInfo.setReturns(node.getNodeReturns());
            RltRuleInfo.NodeInfo ni = new RltRuleInfo.NodeInfo();
            ni.setNode(nodeInfo);


            List<RltRuleInfo.NodeCdt> cdts = new ArrayList<>();
            if (node.getCdts() != null) {
                for (int i = 0; i < node.getCdts().size(); i++) {
                    RelationRuleAttrCdt rrac = node.getCdts().get(i);
                    RltRuleInfo.NodeCdt cdt = new RltRuleInfo.NodeCdt();
                    cdt.setAttrId(rrac.getAttrId());
                    if (RelationRuleAttrCdtOp.Equal.equals(rrac.getOp())) {
                        cdt.setCdtOp(1);
                    } else if (RelationRuleAttrCdtOp.Like.equals(rrac.getOp())) {
                        cdt.setCdtOp(2);
                    }
                    cdt.setRelRlt(rrac.getRelRlt());
                    cdt.setCdtVal(rrac.getValue());
                    cdts.add(cdt);
                }
            }
            ni.setNodeCdts(cdts);
            nodeInfoList.add(ni);

            boolean hasAnd = false;
            boolean hasOr = false;
            if (downMap.containsKey(node.getPageNodeId())) {
                for (Integer key : downMap.get(node.getPageNodeId()).keySet()) {
                    if (downMap.get(node.getPageNodeId()).get(key).getDirection()) {
                        hasAnd = true;
                    } else {
                        hasOr = true;
                    }
                }
            }
            if (upMap.containsKey(node.getPageNodeId())) {
                for (Integer key : upMap.get(node.getPageNodeId()).keySet()) {
                    if (upMap.get(node.getPageNodeId()).get(key).getDirection()) {
                        hasOr = true;
                    } else {
                        hasAnd = true;
                    }
                }
            }

            if (hasAnd) {
                RltRuleInfo.Node and = new RltRuleInfo.Node();
                and.setPageNodeId(maxPageNodeId + 1);
                and.setNodeType(2);
                and.setLogicOperation(1);
                and.setX(node.getX().intValue());
                and.setY(node.getY().intValue());
                RltRuleInfo.NodeInfo ai = new RltRuleInfo.NodeInfo();
                ai.setNode(and);
                andMap.put(node.getPageNodeId(), ai);

                RltRuleInfo.Line andLine = new RltRuleInfo.Line();
                andLine.setNodeStartId(node.getPageNodeId());
                andLine.setNodeEndId(and.getPageNodeId());
                andLine.setClsStartId(node.getClassId());
                andLine.setLineType(2);
                RltRuleInfo.LineInfo ali = new RltRuleInfo.LineInfo();
                ali.setLine(andLine);
                andLineMap.put(node.getPageNodeId(), ali);
            }

            if (hasOr) {
                RltRuleInfo.Node or = new RltRuleInfo.Node();
                or.setPageNodeId(maxPageNodeId + 2);
                or.setNodeType(2);
                or.setLogicOperation(2);
                or.setX(node.getX().intValue());
                or.setY(node.getY().intValue());
                RltRuleInfo.NodeInfo oi = new RltRuleInfo.NodeInfo();
                oi.setNode(or);
                orMap.put(node.getPageNodeId(), oi);

                RltRuleInfo.Line orLine = new RltRuleInfo.Line();
                orLine.setNodeStartId(or.getPageNodeId());
                orLine.setNodeEndId(node.getPageNodeId());
                orLine.setClsEndId(node.getClassId());
                orLine.setLineType(2);
                RltRuleInfo.LineInfo oli = new RltRuleInfo.LineInfo();
                oli.setLine(orLine);
                orLineMap.put(node.getPageNodeId(), oli);
            }

            maxPageNodeId = maxPageNodeId + 2;
        }

        Map<Long, List<RltRuleInfo.LineInfo>> andNextLineMap = new HashMap<>();
        Map<Long, List<RltRuleInfo.LineInfo>> orNextLineMap = new HashMap<>();
        Set<Long> foundPageNodeIdSet = new HashSet<>();
        List<Long> queue = new ArrayList<>();
        queue.add(this.pageNodeId);
        while (queue.size() > 0) {
            Long nodeId = queue.remove(0);
            if (!foundPageNodeIdSet.contains(nodeId)) {
                Map<Integer, RelationRuleLine> downs = downMap.get(nodeId);
                if (downs != null) {
                    for (Integer key : downs.keySet()) {
                        RelationRuleLine line = downs.get(key);
                        RltRuleInfo.Line lineInfo = new RltRuleInfo.Line();
                        lineInfo.setNodeStartId(andMap.get(line.getDirection() ? line.getNodeStartId() : line.getNodeEndId()).getNode().getPageNodeId());
                        lineInfo.setNodeEndId(orMap.get(line.getDirection() ? line.getNodeEndId() : line.getNodeStartId()).getNode().getPageNodeId());
                        lineInfo.setClsRltId(line.getClassId());
                        lineInfo.setLineType(1);
                        lineInfo.setLineDirect(line.getDirection() ? 2 : 1);
                        lineInfo.setLineOp(line.getLineOp().getOp());
                        lineInfo.setLineVal(line.getLineOpValue());
                        RltRuleInfo.LineInfo li = new RltRuleInfo.LineInfo();
                        li.setLine(lineInfo);
                        List<RltRuleInfo.LineCdt> cdts = new ArrayList<>();
                        if (line.getCdts() != null) {
                            for (int i = 0; i < line.getCdts().size(); i++) {
                                RelationRuleAttrCdt rrac = line.getCdts().get(i);
                                RltRuleInfo.LineCdt cdt = new RltRuleInfo.LineCdt();
                                cdt.setAttrId(rrac.getAttrId());
                                if (RelationRuleAttrCdtOp.Equal.equals(rrac.getOp())) {
                                    cdt.setCdtOp(1);
                                } else if (RelationRuleAttrCdtOp.Like.equals(rrac.getOp())) {
                                    cdt.setCdtOp(7);
                                }
                                cdt.setCdtVal(rrac.getValue());
                                cdts.add(cdt);
                            }
                        }
                        li.setLineCdts(cdts);
                        List<RltRuleInfo.LineInfo> andList = andNextLineMap.get(line.getDirection() ? line.getNodeStartId() : line.getNodeEndId());
                        if (andList == null) {
                            andList = new ArrayList<>();
                            andNextLineMap.put(line.getDirection() ? line.getNodeStartId() : line.getNodeEndId(), andList);
                        }
                        andList.add(li);
                        List<RltRuleInfo.LineInfo> orList = orNextLineMap.get(line.getDirection() ? line.getNodeEndId() : line.getNodeStartId());
                        if (orList == null) {
                            orList = new ArrayList<>();
                            orNextLineMap.put(line.getDirection() ? line.getNodeEndId() : line.getNodeStartId(), orList);
                        }
                        orList.add(li);

                        queue.add(line.getNodeEndId());
                    }
                }

                foundPageNodeIdSet.add(nodeId);
            }
        }

        while (true) {
            boolean breakable = true;
            for (Long andNodeId : andNextLineMap.keySet()) {
                List<RltRuleInfo.LineInfo> list = andNextLineMap.get(andNodeId);
                if (list.size() == 1) {
                    RltRuleInfo.LineInfo andNextLine = list.get(0);
                    RltRuleInfo.NodeInfo and = andMap.get(andNodeId);
                    RltRuleInfo.LineInfo andLine = andLineMap.get(andNodeId);
                    andLine.getLine().setClsRltId(andNextLine.getLine().getClsRltId());
                    andLine.getLine().setNodeEndId(andNextLine.getLine().getNodeEndId());
                    andLine.getLine().setLineType(andNextLine.getLine().getLineType());
                    andLine.getLine().setLineDirect(andNextLine.getLine().getLineDirect());
                    andLine.getLine().setLineOp(andNextLine.getLine().getLineOp());
                    andLine.getLine().setLineVal(andNextLine.getLine().getLineVal());
                    andLine.setLineCdts(andNextLine.getLineCdts());

                    for (Long orNodeId : orNextLineMap.keySet()) {
                        List<RltRuleInfo.LineInfo> orList = orNextLineMap.get(orNodeId);
                        for (RltRuleInfo.LineInfo orNextLine : orList) {
                            if (orNextLine.getLine().getNodeStartId().equals(and.getNode().getPageNodeId())) {
                                orNextLine.getLine().setNodeStartId(andNodeId);
                                orNextLine.getLine().setClsStartId(andLine.getLine().getClsStartId());
                            }
                        }
                    }

                    andMap.remove(andNodeId);
                    andNextLineMap.remove(andNodeId);
                    breakable = false;
                    break;
                }
            }
            if (breakable) {
                break;
            }
        }

        while (true) {
            boolean breakable = true;
            for (Long orNodeId : orNextLineMap.keySet()) {
                List<RltRuleInfo.LineInfo> list = orNextLineMap.get(orNodeId);
                if (list.size() == 1) {
                    RltRuleInfo.LineInfo orNextLine = list.get(0);
                    RltRuleInfo.NodeInfo or = orMap.get(orNodeId);
                    RltRuleInfo.LineInfo orLine = orLineMap.get(orNodeId);
                    orLine.getLine().setClsRltId(orNextLine.getLine().getClsRltId());
                    orLine.getLine().setNodeStartId(orNextLine.getLine().getNodeStartId());
                    orLine.getLine().setClsStartId(orNextLine.getLine().getClsStartId());
                    orLine.getLine().setLineType(orNextLine.getLine().getLineType());
                    orLine.getLine().setLineDirect(orNextLine.getLine().getLineDirect());
                    orLine.getLine().setLineOp(orNextLine.getLine().getLineOp());
                    orLine.getLine().setLineVal(orNextLine.getLine().getLineVal());
                    orLine.setLineCdts(orNextLine.getLineCdts());

                    for (Long andNodeId : andNextLineMap.keySet()) {
                        List<RltRuleInfo.LineInfo> andList = andNextLineMap.get(andNodeId);
                        for (RltRuleInfo.LineInfo andNextLine : andList) {
                            if (andNextLine.getLine().getNodeEndId().equals(or.getNode().getPageNodeId())) {
                                andNextLine.getLine().setNodeEndId(orNodeId);
                                andNextLine.getLine().setClsEndId(orLine.getLine().getClsEndId());
                            }
                        }
                    }
                    for (Long andNodeId : andLineMap.keySet()) {
                        RltRuleInfo.LineInfo andLine = andLineMap.get(andNodeId);
                        if (andLine.getLine().getNodeEndId().equals(or.getNode().getPageNodeId())) {
                            andLine.getLine().setNodeEndId(orNodeId);
                            andLine.getLine().setClsEndId(orLine.getLine().getClsEndId());
                        }
                    }

                    orMap.remove(orNodeId);
                    orNextLineMap.remove(orNodeId);
                    breakable = false;
                    break;
                }
            }
            if (breakable) {
                break;
            }
        }

        for (Long key : andMap.keySet()) {
            nodeInfoList.add(andMap.get(key));
        }
        for (Long key : orMap.keySet()) {
            nodeInfoList.add(orMap.get(key));
        }
        List<RltRuleInfo.LineInfo> lineInfoList = new ArrayList<>();
        for (Long key : andLineMap.keySet()) {
            RltRuleInfo.LineInfo lineInfo = andLineMap.get(key);
            boolean contains = false;
            for (RltRuleInfo.LineInfo line : lineInfoList) {
                if (lineInfo.getLine().getNodeStartId().equals(line.getLine().getNodeStartId()) &&
                        lineInfo.getLine().getNodeEndId().equals(line.getLine().getNodeEndId())) {
                    contains = true;
                    break;
                }
            }
            if (!contains) {
                lineInfo.getLine().setPageLineId(new Long(lineInfoList.size() + 1));
                lineInfoList.add(lineInfo);
            }
        }
        for (Long key : orLineMap.keySet()) {
            RltRuleInfo.LineInfo lineInfo = orLineMap.get(key);
            boolean contains = false;
            for (RltRuleInfo.LineInfo line : lineInfoList) {
                if (lineInfo.getLine().getNodeStartId().equals(line.getLine().getNodeStartId()) &&
                        lineInfo.getLine().getNodeEndId().equals(line.getLine().getNodeEndId())) {
                    contains = true;
                    break;
                }
            }
            if (!contains) {
                lineInfo.getLine().setPageLineId(new Long(lineInfoList.size() + 1));
                lineInfoList.add(lineInfo);
            }
        }
        for (Long key : andNextLineMap.keySet()) {
            for (RltRuleInfo.LineInfo lineInfo : andNextLineMap.get(key)) {
                boolean contains = false;
                for (RltRuleInfo.LineInfo line : lineInfoList) {
                    if (lineInfo.getLine().getNodeStartId().equals(line.getLine().getNodeStartId()) &&
                            lineInfo.getLine().getNodeEndId().equals(line.getLine().getNodeEndId())) {
                        contains = true;
                        break;
                    }
                }
                if (!contains) {
                    lineInfo.getLine().setPageLineId(new Long(lineInfoList.size() + 1));
                    lineInfoList.add(lineInfo);
                }
            }
        }
        for (Long key : orNextLineMap.keySet()) {
            for (RltRuleInfo.LineInfo lineInfo : orNextLineMap.get(key)) {
                boolean contains = false;
                for (RltRuleInfo.LineInfo line : lineInfoList) {
                    if (lineInfo.getLine().getNodeStartId().equals(line.getLine().getNodeStartId()) &&
                            lineInfo.getLine().getNodeEndId().equals(line.getLine().getNodeEndId())) {
                        contains = true;
                        break;
                    }
                }
                if (!contains) {
                    lineInfo.getLine().setPageLineId(new Long(lineInfoList.size() + 1));
                    lineInfoList.add(lineInfo);
                }
            }
        }

        ruleInfo.setNodes(nodeInfoList);
        ruleInfo.setLines(lineInfoList);
        return ruleInfo;
    }
}
