package com.uinnova.product.eam.web.cj;

import com.binary.framework.util.ControllerUtils;
import com.binary.framework.web.RemoteResult;
import com.uinnova.product.eam.base.cj.ResultMsg;
import com.uinnova.product.eam.model.cj.request.PlanChapterInstanceAddRequest;
import com.uinnova.product.eam.model.cj.request.PlanChapterInstanceMoveRequest;
import com.uinnova.product.eam.model.cj.vo.*;
import com.uinnova.product.eam.service.cj.service.EamPlanChapterSvc;
import com.uinnova.product.eam.service.cj.service.PlanChapterInstanceService;
import com.uino.bean.permission.base.SysUser;
import com.uino.util.sys.SysUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;


/**
 * 方案章节 controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/planChapter")
@Slf4j
public class PlanChapterInstanceController {

    @Resource
    private PlanChapterInstanceService planChapterInstanceService;

    @Resource
    private EamPlanChapterSvc eamPlanChapterSvc;

    /**
     * 方案章节新增
     */
    @PostMapping
    public ResultMsg add(@RequestBody @Valid PlanChapterInstanceAddRequest request) {

        return new ResultMsg(planChapterInstanceService.addChapter(request));
    }

    /**
     * 方案章节初始化
     */
    @PutMapping("initChapter")
    public ResultMsg initChapter(@RequestBody Map<String, Long> map) {
        Long planId = map.get("planId");
        planChapterInstanceService.initChapter(planId, null);
        return ResultMsg.ok();
    }

    /**
     * 方案章节查询
     */
    @GetMapping("list/{planId}")
    public ResultMsg getChapter(@PathVariable("planId") Long planId) {

        return new ResultMsg(planChapterInstanceService.getChapterList(planId));
    }

    /**
     * 方案章节查询
     */
    @GetMapping("list")
    public ResultMsg getChapterByName(@RequestParam("planId") Long planId, @RequestParam("like") String like) {
        return new ResultMsg(planChapterInstanceService.getChapterList(planId, like));
    }

    /**
     * 方案章节移动
     */
    @PostMapping("/move")
    public ResultMsg move(@RequestBody @Valid PlanChapterInstanceMoveRequest request) {
        planChapterInstanceService.move(request);
        return ResultMsg.ok();
    }

    /**
     * 方案章节复制
     */
    @PostMapping("copy")
    public ResultMsg copy(@RequestBody Map<String, Object> map) {
        Long id = planChapterInstanceService.copy(map);
        return new ResultMsg(id);
    }

    /**
     * 方案章节删除
     */
    @GetMapping("{id}")
    public ResultMsg delete(@PathVariable("id") Long id) {
        planChapterInstanceService.deleteById(id);
        return ResultMsg.ok();
    }

    /**
     * 章节预览查询章节对应的序号
     */
    @GetMapping("/getChapterSerialNum")
    public void getChapterSerialNum(HttpServletRequest request, HttpServletResponse response,
                                    @RequestParam Long planId, @RequestParam Long chapterId) {
        String serialNum = planChapterInstanceService.getChapterSerialNum(planId, chapterId);
        ControllerUtils.returnJson(request, response, serialNum);
    }

    /**
     * 锁定章节
     * @return
     */
    @PostMapping("/lockChapter")
    public ResultMsg lockChapter(@Validated @RequestBody HandlePlanChapterVO handlePlanChapterVO) {
        SysUser sysUser = SysUtil.getCurrentUserInfo();
        boolean result = planChapterInstanceService.lockChapter(handlePlanChapterVO, sysUser);
        return new ResultMsg(result);
    }

    /**
     * 根据模板引入的章节查找能引入的章节详情
     */
    @PostMapping("/getAllIntroChapterInfo")
    public RemoteResult getAllIntroChapterInfo(@RequestBody List<PlanTemplateIntroduceChapterVo> templateIntroduceChapterVos) {
        Map<String, List<PlanIntroChapterVo>> result = planChapterInstanceService.getAllIntroChapterInfo(templateIntroduceChapterVos);
        return new RemoteResult(result);
    }

    @GetMapping("findDiagramVersions")
    public RemoteResult findDiagramVersions(@RequestParam Long planId) {
        List<DiagramVersionBO> result = planChapterInstanceService.findDiagramVersions(planId);
        return new RemoteResult(result);
    }

    @GetMapping("/ignoreViewVersions")
    public RemoteResult ignoreViewVersions(@RequestParam Long planId) {
        Boolean result = planChapterInstanceService.ignoreViewVersions(planId);
        return new RemoteResult(result);
    }


    /**
     * 校验方案所应用的模板是否有更新
     */
    @GetMapping("/checkPlanTemplate")
    public RemoteResult checkPlanTemplate(@RequestParam("planId") Long planId) {
        Boolean result = planChapterInstanceService.checkPlanTemplate(planId);
        return new RemoteResult(result);
    }

    /**
     * 更新方案模板
     * @return
     */
    @GetMapping("/updatePlanTemplate")
    public RemoteResult updatePlanTemplate(@RequestParam("planId") Long planId) {
        SysUser sysUser = SysUtil.getCurrentUserInfo();
        List<ChapterModuleVO> result = planChapterInstanceService.updatePlanTemplate(planId, sysUser);
        return new RemoteResult(result);
    }

    @GetMapping("/getFullPlanContext")
    public RemoteResult getFullPlanContext(@RequestParam("planId") Long planId,
                                           @RequestParam(name = "processInstanceId", required = false) String processInstanceId, @RequestParam(value = "like", required = false) String like) {
        List<PlanChapterVO> result = eamPlanChapterSvc.getFullPlanContext(planId, processInstanceId, like);
        return new RemoteResult(result);
    }

}

