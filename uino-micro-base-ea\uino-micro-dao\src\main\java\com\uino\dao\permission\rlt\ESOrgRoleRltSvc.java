package com.uino.dao.permission.rlt;

import com.uino.bean.permission.base.SysOrgRoleRlt;
import com.uino.bean.permission.query.CSysOrgRoleRlt;
import com.uino.dao.AbstractESBaseDao;
import com.uino.dao.ESConst;
import com.uino.util.sys.CommonFileUtil;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.util.List;

/**
 * <b>组织 - 角色 关系
 * 
 * <AUTHOR>
 */
@Service
public class ESOrgRoleRltSvc extends AbstractESBaseDao<SysOrgRoleRlt, CSysOrgRoleRlt> {

    @Override
    public String getIndex() {
        return ESConst.INDEX_SYS_ORG_ROLE_RLT;
    }

    @Override
    public String getType() {
        return ESConst.INDEX_SYS_ORG_ROLE_RLT;
    }

    @PostConstruct
    public void init() {
        List<SysOrgRoleRlt> data = CommonFileUtil.getData("/initdata/uino_sys_org_role_rlt.json", SysOrgRoleRlt.class);
        super.initIndex(data);
    }
}
