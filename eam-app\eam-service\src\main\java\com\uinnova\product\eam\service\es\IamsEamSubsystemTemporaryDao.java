package com.uinnova.product.eam.service.es;

import com.uinnova.product.eam.comm.bean.SubsystemTemporary;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;

/**
 * 子系统临时存储
 */
@Service
public class IamsEamSubsystemTemporaryDao extends AbstractESBaseDao<SubsystemTemporary,SubsystemTemporary> {
    @Override
    public String getIndex() {
        return "uino_eam_subsystem_temporary";
    }

    @Override
    public String getType() {
        return "_doc";
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }
}
