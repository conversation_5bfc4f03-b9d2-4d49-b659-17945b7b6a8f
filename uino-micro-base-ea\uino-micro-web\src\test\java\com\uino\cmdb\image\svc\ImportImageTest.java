package com.uino.cmdb.image.svc;

import static org.junit.Assert.assertEquals;

import java.io.File;
import java.io.FileInputStream;
import java.util.ArrayList;
import java.util.UUID;

import org.elasticsearch.index.query.QueryBuilders;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.util.ReflectionTestUtils;

import com.binary.core.exception.MessageException;
import com.uino.dao.cmdb.ESDirSvc;
import com.uino.dao.cmdb.ESImageSvc;
import com.uino.service.cmdb.microservice.impl.ImageSvc;
import com.uino.service.sys.microservice.impl.ResourceSvc;

public class ImportImageTest {
	@InjectMocks
	private ImageSvc testSvc;
	private ESImageSvc esImageSvc;
	private ESDirSvc esDirSvc;
	private String localPath;
    private ResourceSvc resourceSvc;

	@Before
	public void before() {
		MockitoAnnotations.initMocks(this);
		esImageSvc = Mockito.mock(ESImageSvc.class);
		esDirSvc = Mockito.mock(ESDirSvc.class);
        resourceSvc = Mockito.mock(ResourceSvc.class);
		localPath = "./src/test/resources" + UUID.randomUUID().toString();
		ReflectionTestUtils.setField(testSvc, "localPath", localPath);
		ReflectionTestUtils.setField(testSvc, "svc", esImageSvc);
		ReflectionTestUtils.setField(testSvc, "dirSvc", esDirSvc);
        ReflectionTestUtils.setField(testSvc, "resourceSvc", resourceSvc);

		Mockito.when(esImageSvc
				.getListByQuery(Mockito.eq(QueryBuilders.boolQuery().must(QueryBuilders.termQuery("dirId", 1L))
						.must(QueryBuilders.termQuery("imgName.keyword", "noexist")))))
				.thenReturn(new ArrayList<>());
		Mockito.when(esImageSvc.getListByQuery(Mockito.eq(QueryBuilders.boolQuery()
				.must(QueryBuilders.termQuery("dirId", 1L)).must(QueryBuilders.termQuery("imgName.keyword", "10006")))))
				.thenReturn(new ArrayList<>());
		Mockito.when(esImageSvc.saveOrUpdate(Mockito.any())).thenReturn(1L);
        Mockito.when(resourceSvc.saveSyncResourceInfo(Mockito.anyString(), Mockito.anyString(), Mockito.anyBoolean(), Mockito.anyInt())).thenReturn("resourcePath");
	}

	@After
	public void after() {
		try {
			delFile(new File(localPath));
		} catch (Exception e) {
			throw new MessageException(e.getMessage());
		}
	}

	@Test(expected = IllegalArgumentException.class)
	public void test01() {
		testSvc.importImage(null, null);
	}

	@Test
	public void test02() {
		MockMultipartFile imgPng = new MockMultipartFile("noexist", "noexist.png", "application/json", new byte[0]);
        try {
            testSvc.importImage(1L, imgPng);
        } catch (Exception e) {
            assertEquals("上传图片不得为空", e.getMessage());
        }
	}

	@Test
	public void test03() {
		try {
			MockMultipartFile imgPng = new MockMultipartFile("10006.png", "10006.png", "application/json",
					new FileInputStream("./src/test/resources/testdata/10006.png"));
			boolean res = testSvc.importImage(1L, imgPng);
			Assert.assertTrue(res);
		} catch (Exception e) {
			Assert.fail();
		}

	}

	private void delFile(File file) {
		if (!file.exists()) {
			return;
		}

		if (file.isDirectory()) {
			File[] files = file.listFiles();
			for (File f : files) {
				delFile(f);
			}
		}
		file.delete();
	}
}
