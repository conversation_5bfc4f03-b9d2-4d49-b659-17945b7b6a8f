package com.uinnova.product.eam.base.diagram.model;

import com.binary.framework.bean.annotation.Comment;
import com.uino.bean.permission.base.SysUser;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Classname
 * @Description TODO
 * <AUTHOR>
 * @Date 2021-06-18-14:21
 */
@Data
public class ESDiagramDTO implements Serializable {
    @Comment("视图信息")
    private ESDiagramInfoDTO diagram;

    @Comment("视图作者信息")
    private SysUser creator;

    @Comment("制品类型分类 1=业务流程建模 2=业务组件建模 3=需求关联分析 4=IT架构设计 5=其他 6=数据建模-概念实体关系图 7=数据建模-逻辑实体关系图")
    private Integer type;

    @Comment("视图分享记录")
    private List<ESDiagramShareRecordResult> shareRecords;

    @Comment("视图关联元素信息")
    private List<VcDiagramEle> diagramEles;

    @Comment("视图发布位置")
    private String releasePath;

    @Comment("视图提交者信息")
    private SysUser submitter;
}
