package com.uinnova.product.eam.model;

import com.binary.framework.bean.annotation.Comment;
import com.uino.bean.cmdb.base.LibType;
import lombok.Data;

/**
 * 流程模型目录参数
 * <AUTHOR>
 */
@Data
public class EamFlowModelCatalogDto {
    @Comment("库类型")
    private LibType libType = LibType.DESIGN;
    @Deprecated
    @Comment("目录类型：业务建模10、数据建模101")
    private Integer dirType = 10;
    @Comment("版本标签id(可空,若不为空,则是查询某个模型树历史版本)")
    private Long tagId;
}
