package com.uinnova.product.eam.db.diagram.es;

import com.uinnova.product.eam.base.diagram.model.TemDiaRelation;
import com.uinnova.product.eam.base.diagram.model.TemDiaRelationQuery;
import com.uino.dao.AbstractESBaseDao;
import com.uino.service.util.FileUtil;
import org.springframework.stereotype.Repository;

import jakarta.annotation.PostConstruct;
import java.util.List;

@Repository
public class EsTemDiaRelationDao extends AbstractESBaseDao<TemDiaRelation, TemDiaRelationQuery> {

    @Override
    public String getIndex() {
        return "uino_eam_tem_dia_relation";
    }

    @Override
    public String getType() {
        return "_doc";
    }

    @PostConstruct
    public void init() {
        try {
            List<TemDiaRelation> list = FileUtil.getData("/initdata/uino_eam_tem_dia_relation.json", TemDiaRelation.class);
            super.initIndex(list);
        } catch (Exception e) {
            super.initIndex();
        }
    }

}
