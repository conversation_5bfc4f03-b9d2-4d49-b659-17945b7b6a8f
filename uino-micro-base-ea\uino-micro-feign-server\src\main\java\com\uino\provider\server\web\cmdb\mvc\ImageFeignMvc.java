package com.uino.provider.server.web.cmdb.mvc;

import java.util.List;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClassDir;
import com.uino.service.cmdb.microservice.IImageSvc;
import com.uino.bean.cmdb.base.CcImage;
import com.uino.bean.cmdb.business.ImageCount;
import com.uino.bean.cmdb.business.ImportDirMessage;
import com.uino.bean.cmdb.business.ImportResultMessage;
import com.uino.bean.cmdb.query.ESSearchImageBean;
import com.uino.bean.permission.query.SearchKeywordBean;
import com.uino.provider.feign.cmdb.ImageFeign;

/**
 * 
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("feign/image")
//@Slf4j
public class ImageFeignMvc implements ImageFeign {

    @Autowired
    private IImageSvc svc;

    @Override
	public List<ImageCount> queryImageDirList(Long domainId, CCcCiClassDir cdt) {
		return svc.queryImageDirList(domainId, cdt);
    }

    @Override
    public Page<CcImage> queryImagePage(ESSearchImageBean bean) {
        return svc.queryImagePage(bean);
    }

    @Override
	public ImportResultMessage importZipImage(Long domainId,Integer sourceType, MultipartFile file) {
		return svc.importZipImage(domainId,sourceType, file);
    }

    @Override
    public boolean importImage(Long dirId, MultipartFile file) {
        return svc.importImage(dirId, file);
    }

    @Override
    public boolean replaceImage(Long imgId, MultipartFile file) {
        return svc.replaceImage(imgId, file);
    }

    @Override
    public boolean deleteDirImage(Long dirId) {
        return svc.deleteDirImage(dirId);
    }

    @Override
    public boolean deleteImage(CcImage image) {
        return svc.deleteImage(image);
    }

    @Override
    public ResponseEntity<byte[]> exportImageZipByDirIds(Set<Long> dirIds) {
        // byte[] tempBytes = new byte[4096];
        // int readLine = 0;
        // ByteArrayOutputStream outStream = new ByteArrayOutputStream();
        // try {
        // while ((readLine = zip.getInputStream().read(tempBytes)) != -1) {
        // outStream.write(tempBytes, 0, readLine);
        // }
        // } catch (IOException e) {
        // log.error("读流异常", e);
        // }
        // byte[] body = outStream.toByteArray();
        // try {
        // outStream.close();
        // } catch (IOException e) {
        // }
        // ResponseEntity<byte[]> response = ResponseEntity.ok(body);
        // response.getHeaders().setContentType(MediaType.APPLICATION_OCTET_STREAM);
        // response.getHeaders().setContentDispositionFormData("attachment",
        // zip.getName());
        return svc.exportImageZipByDirIds(dirIds);
    }

    @Override
    public ImportDirMessage import3DZipImage(MultipartFile file,Long dirId, boolean isCover) {
        return svc.import3DZipImage(file, dirId, null, isCover);
    }

    @Override
    public List<CcImage> queryTopImage(SearchKeywordBean bean) {
        // TODO Auto-generated method stub
        return svc.queryTopImage(bean);
    }

    @Override
    public Long updateImageRlt(CcImage image) {
        // TODO Auto-generated method stub
        return svc.updateImageRlt(image);
    }

    @Override
    public boolean replace3DImage(Long imgId, MultipartFile file) {
        // TODO Auto-generated method stub
        return svc.replace3DImage(imgId, file);
    }

    @Override
    public boolean delete3DImage(CcImage image) {
        // TODO Auto-generated method stub
        return svc.delete3DImage(image);
    }

    @Override
    public CcImage queryImageById(Long id) {
        // TODO Auto-generated method stub
        return svc.queryImageById(id);
    }

    @Override
    public ImportDirMessage importImages(Long dirId, MultipartFile[] files) {
        // TODO Auto-generated method stub
        return svc.importImages(dirId, files);
    }

    @Override
    public Long countBySearchBean(ESSearchImageBean bean) {
        // TODO Auto-generated method stub
        return svc.countBySearchBean(bean);
    }

	@Override
	public ResponseEntity<byte[]> downloadImageResource(List<Long> ids) {
		// TODO Auto-generated method stub
		return svc.downloadImageResource(ids);
	}

    @Override
    public Page<CcImage> queryImageByPath(ESSearchImageBean bean) {
        return svc.queryImageByPath(bean);
    }

    @Override
    public Boolean isShowDocumentAttribute() {
        return svc.isShowDocumentAttribute();
    }
}
