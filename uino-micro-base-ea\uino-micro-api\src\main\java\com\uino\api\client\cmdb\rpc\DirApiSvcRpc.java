package com.uino.api.client.cmdb.rpc;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.uinnova.product.vmdb.comm.model.ci.CCcCiClassDir;
import com.uino.bean.cmdb.base.CcCiClassDir;
import com.uino.provider.feign.cmdb.DirFeign;
import com.uino.api.client.cmdb.IDirApiSvc;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
public class DirApiSvcRpc implements IDirApiSvc {

    @Autowired
    private DirFeign dirFeign;

    @Override
    public Long saveOrUpdateDir(CcCiClassDir dir) {
        return dirFeign.saveOrUpdateDir(dir);
    }

    @Override
    public Integer removeDirById(Long id) {
        return dirFeign.removeDirById(id);
    }

    @Override
    public List<CcCiClassDir> queryDirList(CCcCiClassDir cdt, String orders, Boolean isAsc) {
        return dirFeign.queryDirList(cdt, orders, isAsc);
    }

}
