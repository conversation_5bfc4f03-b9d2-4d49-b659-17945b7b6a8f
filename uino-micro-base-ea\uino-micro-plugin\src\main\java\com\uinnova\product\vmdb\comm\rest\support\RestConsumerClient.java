package com.uinnova.product.vmdb.comm.rest.support;

import com.binary.core.http.HttpClient;
import com.binary.core.http.HttpUtils;
import com.binary.core.lang.Conver;
import com.binary.core.util.BinaryUtils;
import com.binary.framework.Local;
import com.binary.framework.critical.CriticalObject;
import com.binary.framework.critical.support.HttpCriticalObject;
import com.binary.framework.critical.support.HttpOauthCriticalObject;
import com.binary.framework.exception.ServiceException;
import com.binary.json.JSON;
import com.uinnova.product.vmdb.comm.i18n.MessageUtil;
import com.uinnova.product.vmdb.comm.rest.RestUtil;
import com.uinnova.product.vmdb.comm.util.SystemUtil;
import org.springframework.util.StringValueResolver;

import jakarta.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.lang.reflect.Type;
import java.util.Iterator;
import java.util.Map;
import java.util.Map.Entry;

/**
 *
 * <AUTHOR>
 *
 */
public class RestConsumerClient {

    // private static boolean https = false;
    // private static final Object syncobj = new Object();

    private String root;
    private String headers;
    private HttpClient httpClient;

    public RestConsumerClient(String root, String headers) {
        MessageUtil.checkEmpty(root, "root");
        this.root = root;
        this.headers = headers;
    }

    public void init(StringValueResolver valueResolver) {
        this.root = valueResolver.resolveStringValue(this.root);
        if (this.headers != null) {
            this.headers = valueResolver.resolveStringValue(this.headers);
        }

        if (headers != null && (headers = headers.trim()).length() > 0 && (headers.charAt(0) != '{' || headers.charAt(headers.length() - 1) != '}')) {
            throw new ServiceException(" is wrong headers '" + headers + "'! must be json object type! ");
        }

        root = HttpUtils.formatContextPath(root).substring(1);
        httpClient = HttpClient.getInstance(root);
        httpClient.addRequestProperty("REQUEST_HEADER", "binary-http-client-header");
        if (!BinaryUtils.isEmpty(headers)) {
            addHeaders(headers);
        }
    }

    // private static void setHttpsConfig(String version) {
    // if(!https) {
    // synchronized (syncobj) {
    // if(!https) {
    // try {
    // HttpClient.setDefaultHttpsConfig(version);
    // } catch (Exception e) {
    // throw new ServiceException(" set https config error! ", e);
    // }
    // https = true;
    // }
    // }
    // }
    // }

    @SuppressWarnings({ "unchecked", "rawtypes" })
    protected void addHeaders(String headers) {
        Map<?, ?> map = (Map) JSON.toObject(headers);
        Iterator<Entry<?, ?>> itor = (Iterator) map.entrySet().iterator();
        while (itor.hasNext()) {
            Entry<?, ?> e = itor.next();
            Object k = e.getKey();
            Object v = e.getValue();

            if (BinaryUtils.isEmpty(k) || BinaryUtils.isEmpty(v)) {
                continue;
            }

            this.httpClient.addRequestProperty(Conver.to(k, String.class), Conver.to(v, String.class));
        }
    }

    public Object rest(Class<?> ifaceType, Method method, Object[] params) {
        String uri = "/provider/rest/" + ifaceType.getName() + "/" + method.getName();

        if (Local.isOpen()) {
            CriticalObject co = Local.getCriticalObject();
            if ((co instanceof HttpCriticalObject) || (co instanceof HttpOauthCriticalObject)) {
                HttpServletRequest request = (co instanceof HttpCriticalObject) ? ((HttpCriticalObject) co).getRequest() : ((HttpOauthCriticalObject) co).getRequest();
                String token = SystemUtil.getToken(request);
                if (!BinaryUtils.isEmpty(token)) {
                    uri += "?token=" + token;
                }
            } else {
                Object userObj = co.getUserObject();
                if (userObj != null && (userObj instanceof String)) {
                    uri += "?token=" + userObj;
                }
            }
        }

        String body = JSON.toString(params);
        String s = this.httpClient.rest(uri, body);
        Object result = parseResult(s, method.getReturnType(), method.getGenericReturnType());
        return result;
    }

    protected Object parseResult(String jsonstring, Class<?> resultType, Type resultGenericType) {
        return RestUtil.parseResult(jsonstring, resultType, resultGenericType);
    }

}
