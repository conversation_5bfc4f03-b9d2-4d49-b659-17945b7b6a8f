package com.uino.util.message.queue.config;

import com.uino.util.message.queue.tools.EnableMessageQueue;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.config.KafkaListenerContainerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.kafka.listener.ConcurrentMessageListenerContainer;
import org.springframework.kafka.listener.ConsumerAwareListenerErrorHandler;
import org.springframework.kafka.listener.ContainerProperties;

import java.util.HashMap;
import java.util.Map;

/**
 * Kafka consumer configuration, including batch consumer configuration,
 * </r>as well as single consumer configuration, custom exception listener
 *
 * @Author: YGQ
 * @Create: 2021-05-24 13:09
 **/
@Slf4j
@Configuration
@EnableKafka
@Conditional(EnableMessageQueue.class)
public class KafkaConsumerConfig {

    /**
     * Batch listener factory for map
     * <p>
     * When doing batch consumption, use this listener.
     *
     * @code @KafkaListener(topics = {"tp"}, groupId = "defaultGroup", containerFactory = "batchMapListener")
     */
    @Bean
    KafkaListenerContainerFactory<ConcurrentMessageListenerContainer<String, String>> batchMapListener() {
        ConcurrentKafkaListenerContainerFactory<String, String> factory = this.kafkaListenerContainerFactory();
        factory.setBatchListener(KafkaProp.enableBatchListener);
        factory.setConsumerFactory(new DefaultKafkaConsumerFactory<>(this.consumerConfigs()));
        return factory;
    }

    /**
     * Batch listener factory for list
     * <p>
     * When doing batch consumption, use this listener.
     *
     * @code @KafkaListener(topics = {"tp"}, groupId = "defaultGroup", containerFactory = "batchListListener")
     */
    @Bean
    KafkaListenerContainerFactory<ConcurrentMessageListenerContainer<String, String>> batchListListener() {
        ConcurrentKafkaListenerContainerFactory<String, String> factory = this.kafkaListenerContainerFactory();
        Map<String, Object> batchListConfigs = this.consumerConfigs();
        batchListConfigs.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, 5);
        factory.setConsumerFactory(new DefaultKafkaConsumerFactory<>(batchListConfigs));
        factory.setBatchListener(KafkaProp.enableBatchListener);
        return factory;
    }

    /**
     * Single listener factory.
     * <p>
     * When you need to receive a heads-up message, the consumer comments are as follows.
     *
     * @code @KafkaListener(topics = {"tp"}, groupId = "defaultGroup", containerFactory = "singleListener")
     */
    @Bean
    KafkaListenerContainerFactory<ConcurrentMessageListenerContainer<String, String>> singleListener() {
        ConcurrentKafkaListenerContainerFactory<String, String> factory = this.kafkaListenerContainerFactory();
        factory.setBatchListener(false);
        factory.setConsumerFactory(new DefaultKafkaConsumerFactory<>(this.consumerConfigs()));
        return factory;
    }

    private ConcurrentKafkaListenerContainerFactory<String, String> kafkaListenerContainerFactory() {
        ConcurrentKafkaListenerContainerFactory<String, String> factory = new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConcurrency(KafkaProp.concurrencyNumber);
        factory.getContainerProperties().setPollTimeout(KafkaProp.pollTimeOut);
        boolean enableAutoCommit = KafkaProp.enableAutoCommit;

        // Set ack mode when auto commit is false
        if (!enableAutoCommit) {
            ContainerProperties.AckMode ackMode = ContainerProperties.AckMode.valueOf(KafkaProp.consumerAckMode.trim());
            factory.getContainerProperties().setAckMode(ackMode);
        }

        // Message filter
        String filterStrategyKey = KafkaProp.recordFilterStrategyKey;
        if (null != filterStrategyKey) {
            String initKey = "record-filter-strategy-key";
            String targetFilterStrategyKey = filterStrategyKey.trim();
            if (!initKey.equals(targetFilterStrategyKey)) {
                factory.setRecordFilterStrategy(consumerRecord -> {
                    // Set a filter to only receive messages that contain "kpi" in the message content
                    String value = consumerRecord.value();
                    if (value != null && value.contains(filterStrategyKey)) {
                        System.err.println(consumerRecord.value());
                        // Return false to receive the message
                        return false;
                    }
                    // Return true to discard the message
                    return true;
                });
            }
        }
        return factory;
    }

    @Bean
    public Map<String, Object> consumerConfigs() {
        return Configs.getConfigs();
    }

    /**
     * custom single message exception handler
     */
    @Bean
    public ConsumerAwareListenerErrorHandler listenErrorHandler() {
        return (message, e, consumer) -> {
            throw new RuntimeException(e.getMessage());
        };
    }

    /**
     * custom batch exception handler
     */
    @Bean
    public ConsumerAwareListenerErrorHandler listenBatchErrorHandler() {
        return (message, e, consumer) -> {
            throw new RuntimeException(e.getMessage());
        };
    }

    /**
     * Consumer base configuration
     */
    public static class Configs {
        public static Map<String, Object> getConfigs() {
            Map<String, Object> props = new HashMap<>(10);
            props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, KafkaProp.kafkaServers);
            props.put(ConsumerConfig.GROUP_ID_CONFIG, KafkaProp.consumerGroupId);
            props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, KafkaProp.enableAutoCommit);
            props.put(ConsumerConfig.AUTO_COMMIT_INTERVAL_MS_CONFIG, KafkaProp.autoCommitInterval);
            props.put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG, KafkaProp.sessionTimeOutMs);
            props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, KafkaProp.autoOffsetReset);
            props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, KafkaProp.kafkaDeserializer);
            props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, KafkaProp.kafkaDeserializer);
            props.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, KafkaProp.maxPollRecords);
            props.put(ConsumerConfig.MAX_POLL_INTERVAL_MS_CONFIG, KafkaProp.maxPollInterval);
            return props;
        }
    }
}
