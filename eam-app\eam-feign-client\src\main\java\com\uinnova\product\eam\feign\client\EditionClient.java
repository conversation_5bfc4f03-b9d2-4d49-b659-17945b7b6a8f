package com.uinnova.product.eam.feign.client;

import com.uinnova.product.eam.base.model.CIDataInfo;
import com.uinnova.product.eam.feign.FeignConst;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@Component
@FeignClient(name = FeignConst.APPLICATION,path = FeignConst.EDITION_PATH)
public interface EditionClient {
    @GetMapping("getResIdsById")
    List<Long> getResIdsById(@RequestParam("id") Long id);

    @GetMapping("getEditionById")
    CIDataInfo getEditionById(@RequestParam("id") Long id);

    @PostMapping("deleteByIds")
    int deleteByIds(@RequestBody List<Long> ids);
}
