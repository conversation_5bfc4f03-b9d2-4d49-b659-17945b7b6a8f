package com.uinnova.product.eam.service.es;

import com.uinnova.product.eam.comm.model.es.TemplateBinding;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;

@Service
public class PlanTemplateBindDao extends AbstractESBaseDao<TemplateBinding, TemplateBinding> {
    @Override
    public String getIndex() {
        return "uino_cj_template_bind";
    }

    @Override
    public String getType() {
        return getIndex();
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }
}
