package com.uinnova.product.eam.service.diagram.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import com.binary.core.exception.BinaryException;
import com.binary.core.util.BinaryUtils;
import com.binary.jdbc.Page;
import com.uinnova.product.eam.base.diagram.exception.DiagramNotFoundException;
import com.uinnova.product.eam.base.diagram.exception.UnAuthorizedException;
import com.uinnova.product.eam.base.diagram.model.*;
import com.uinnova.product.eam.base.diagram.utils.FileFilterUtil;
import com.uinnova.product.eam.db.diagram.es.ESDiagramDao;
import com.uinnova.product.eam.db.diagram.es.ESDiagramSheetDao;
import com.uinnova.product.eam.db.diagram.es.ESShareDiagramDao;
import com.uinnova.product.eam.db.diagram.es.ESShareLinkDao;
import com.uinnova.product.eam.model.asset.ShareMsgDTO;
import com.uinnova.product.eam.service.IEamNoticeService;
import com.uinnova.product.eam.service.diagram.ESDiagramSvc;
import com.uinnova.product.eam.service.diagram.ESShareDiagramSvc;
import com.uinnova.product.eam.service.diagram.IEamShareDiagramSvc;
import com.uino.api.client.permission.IUserApiSvc;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.permission.query.CSysUser;
import com.uino.dao.util.ESUtil;
import com.uino.service.sys.microservice.IResourceSvc;
import com.uino.service.util.FileUtil;
import com.uino.tarsier.tarsiercom.util.IdGenerator;
import com.uino.util.rsm.RsmUtils;
import com.uino.util.sys.SysUtil;
import jodd.util.StringUtil;
import org.apache.commons.codec.binary.Base64;
import org.aspectj.weaver.ast.Not;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;

import java.io.File;
import java.io.IOException;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.Comparator.comparing;

@Service
public class ESShareDiagramSvcImpl implements ESShareDiagramSvc {

    @Autowired
    private ESShareDiagramDao esShareDiagramDao;

    @Value("${http.resource.space}")
    private String httpResouceUrl;

    @Value("${local.resource.space}")
    private String localPath;

    @Autowired(required = false)
    private IResourceSvc resourceSvc;

    @Autowired
    private IUserApiSvc userApiSvc;

    @Autowired
    private ESDiagramSvc esDiagramSvc;

    @Autowired
    private ESDiagramDao esDiagramDao;

    @Autowired
    private ESDiagramSheetDao esDiagramSheetDao;

    @Autowired
    private ESShareLinkDao shareLinkDao;

    @Autowired
    private IEamShareDiagramSvc eamShareDiagramSvc;

    @Autowired
    @Lazy
    private IEamNoticeService eamNoticeClient;

    @Autowired
    private RsmUtils rsmUtils;

    public static Logger log = LoggerFactory.getLogger(ESShareDiagramSvcImpl.class);

    public static final String FILE_SEPARATOR = System.getProperty("file.separator");

    private final static String VALID_AUTH = "auth";

    @Override
    public List<ESDiagramShareRecordResult> saveOrUpdateShareRecord(List<DiagramShareRecordDTO> shareRecordDTOList) {
        List<DiagramShareRecord> shareRecordList = new ArrayList<>();
        List<ShareMsgDTO> shareMsgs = new ArrayList<>();
        if (!CollectionUtils.isEmpty(shareRecordDTOList)) {
            //对待分享视图鉴权
            String[] shareDiagramIdArr = shareRecordDTOList.stream().map(DiagramShareRecordDTO::getDiagramId).distinct().toArray(String[]::new);
            Long[] longs = esDiagramSvc.queryDiagramInfoBydEnergy(shareDiagramIdArr);
            judgeSingleDiagramAuth(longs[0], null, true);
            ShareMsgDTO shareMsg;
            //新增分享记录
            for (DiagramShareRecordDTO diagramShareRecord : shareRecordDTOList) {
                if (diagramShareRecord.getId() == null) {
                    diagramShareRecord.setId(ESUtil.getUUID());
                }
                DiagramShareRecord dsr = diagramShareRecord.dtoToDiagramShareRecord(diagramShareRecord, esDiagramSvc.queryDiagramInfoByEnergy(diagramShareRecord.getDiagramId()));
                shareRecordList.add(dsr);
                shareMsg = new ShareMsgDTO();
                shareMsg.setDirType(diagramShareRecord.getDirType());
                shareMsg.setSourceType("diagram");
                shareMsg.setOwnerId(diagramShareRecord.getOwnerId());
                shareMsg.setSharedUserId(diagramShareRecord.getSharedUserId());
                shareMsg.setSourceId(diagramShareRecord.getDiagramId());
                shareMsgs.add(shareMsg);
            }
            esShareDiagramDao.saveOrUpdateBatch(shareRecordList);
        }
        //todo 推送工作台消息
        eamNoticeClient.shareMsgSave(shareMsgs);
        return this.fillSysOPAndESDiagramInfo(shareRecordList);
    }

    @Override
    public Page<ESDiagramShareRecordResult> queryShareRecordPage(ShareRecordQueryBean shareRecordQueryBean) {
        Page<ESDiagramShareRecordResult> diagramShareRecordPage = new Page<>();
        String order = shareRecordQueryBean.getOrder();
        Integer pageNum = shareRecordQueryBean.getPageNum();
        Integer pageSize = shareRecordQueryBean.getPageSize();
        String word = shareRecordQueryBean.getLike();
        Long totalRows = 0L;
        Long totalpages = 0L;

        ShareRecordQuery shareRecordQuery = new ShareRecordQuery();
        shareRecordQuery.setSharedUserId(shareRecordQueryBean.getSharedUserId());
        List<DiagramShareRecord> allShareRecordList = new ArrayList<>();
        if ("modifyTime".equals(order)) {
            //查出当前用户拥有的所有分享记录
            allShareRecordList = esShareDiagramDao.getListByCdt(shareRecordQuery);
        } else if ("shareTime".equals(order)) {
            //TODO 按照视图的分享时间排序
        } else {
            throw new BinaryException("排序字段不符合规定");
        }

        if (!CollectionUtils.isEmpty(allShareRecordList)) {
            diagramShareRecordPage.setPageNum(pageNum);
            diagramShareRecordPage.setPageSize(pageSize);
            diagramShareRecordPage.setTotalRows(totalRows);
            diagramShareRecordPage.setTotalPages(totalpages);
            List<ESDiagramShareRecordResult> resultList = fillSysOPAndDiagramInfoForQuery(allShareRecordList, pageNum, pageSize, word, diagramShareRecordPage, order);
            //按照修改时间排序
            resultList.sort(comparing(ESDiagramShareRecordResult::getDiagramModifyTime).reversed());
            diagramShareRecordPage.setData(resultList);
        }

        return diagramShareRecordPage;
    }

    @Override
    public Map<Long, List<ESDiagramShareRecordResult>> queryDiagramShareRecords(Long[] diagramIds, Boolean needAuth) {
        /*
         * 根据视图id查询该视图的分享记录
         * 1.查询视图的分享记录
         * 2.填充用户信息
         * */
        //查出目标视图基础信息
        Map<Long, ESDiagram> diagramIdMap = new HashMap<>();
        List<ESDiagram> esDiagramList = judgeDiagramAuth(diagramIds, null, needAuth);
        for (ESDiagram esDiagram : esDiagramList) {
            Long diagramId = esDiagram.getId();
            String icon1 = esDiagram.getIcon1();
            if (icon1 != null && !icon1.startsWith(httpResouceUrl)) {
                esDiagram.setIcon1(new StringBuilder(httpResouceUrl).append(icon1).toString());
            }
            diagramIdMap.put(diagramId, esDiagram);
        }

        ShareRecordQuery shareRecordQuery = new ShareRecordQuery();
        shareRecordQuery.setDiagramIds(diagramIds);
        List<DiagramShareRecord> shareRecordList = esShareDiagramDao.getListByCdt(shareRecordQuery);
        Map<Long, List<DiagramShareRecord>> diagramShareRecordMap = shareRecordList.stream().collect(Collectors.groupingBy(DiagramShareRecord::getDiagramId));

        //查询用户信息
        Set<Long> ownerIdSet = shareRecordList.stream().map(DiagramShareRecord::getOwnerId).collect(Collectors.toSet());
        Set<Long> shareIdSet = shareRecordList.stream().map(DiagramShareRecord::getSharedUserId).collect(Collectors.toSet());
        ownerIdSet.addAll(shareIdSet);
        Map<Long, SysUser> sysOpMap = new HashMap<>();
        if (!BinaryUtils.isEmpty(ownerIdSet)) {
            CSysUser sysUser = new CSysUser();
            sysUser.setIds(ownerIdSet.toArray(new Long[0]));
            sysUser.setStatus(1);
            List<SysUser> sysUserByCdt = userApiSvc.getSysUserByCdt(sysUser);
            for (SysUser sysOp : sysUserByCdt) {
                Long id = sysOp.getId();
                // 去除敏感信息
                sysOp.clearSensitive();
                sysOpMap.put(id, sysOp);
            }
        }

        //封装返回结果
        Map<Long, List<ESDiagramShareRecordResult>> resultMap = new HashMap<>();
        for (Map.Entry<Long, List<DiagramShareRecord>> entry : diagramShareRecordMap.entrySet()) {
            List<DiagramShareRecord> tempShareRecordList = entry.getValue();
            List<ESDiagramShareRecordResult> shareRecordResultList = new ArrayList<>();
            for (DiagramShareRecord shareRecord : tempShareRecordList) {
                //封装分享记录对应的视图信息
                ESDiagramShareRecordResult shareRecordResult = new ESDiagramShareRecordResult();
                BeanUtils.copyProperties(shareRecord, shareRecordResult);
                ESDiagram esDiagram = diagramIdMap.get(entry.getKey());
                shareRecordResult.setEsDiagram(esDiagram);
                shareRecordResult.setDiagramModifyTime(esDiagram.getModifyTime());

                //封装用户信息
                shareRecordResult.setOwnerSysUser(sysOpMap.get(shareRecordResult.getOwnerId()));
                shareRecordResult.setSharedSysUser(sysOpMap.get(shareRecordResult.getSharedUserId()));
                shareRecordResultList.add(shareRecordResult);
            }
            resultMap.put(entry.getKey(), shareRecordResultList);
        }
        return resultMap;
    }

    private List<ESDiagramShareRecordResult> fillSysOPAndDiagramInfoForQuery(List<DiagramShareRecord> diagramShareRecords, Integer pageNum, Integer pageSize,
                                                                             String word, Page<ESDiagramShareRecordResult> recordResultPage, String order) {
        SysUser currUser = SysUtil.getCurrentUserInfo();
        Long domainId = currUser.getDomainId();
        Long userId = currUser.getId();

        List<ESDiagramShareRecordResult> diagramShareRecordResults = new ArrayList<>();
        //获取当前用户所有分享记录的视图id集合diagramIds
        Long[] diagramIdArr = diagramShareRecords
                .stream()
                .map(DiagramShareRecord::getDiagramId)
                .distinct()
                .toArray(Long[]::new);
        //根据diagramIds分页排序查询视图信息
        CVcDiagram cVcDiagram = new CVcDiagram();
        cVcDiagram.setIds(diagramIdArr);
        cVcDiagram.setStatus(1);
        cVcDiagram.setDataStatus(1);
        if (!StringUtils.isEmpty(word)) {
            cVcDiagram.setName(word);
        }
        Page<ESDiagram> esDiagramPage = esDiagramSvc.queryESDiagramPage(domainId, pageNum, pageSize, cVcDiagram, order);
        recordResultPage.setTotalRows(esDiagramPage.getTotalRows());
        recordResultPage.setTotalPages(esDiagramPage.getTotalPages());
        //对查询结果
        List<ESDiagram> diagramList = esDiagramPage.getData();
        Map<Long, ESDiagram> diagramIdObjMap = new HashMap<>();
        Set<Long> diagramIdResultSet = new HashSet<>();
        if (!CollectionUtils.isEmpty(diagramList)) {
            for (ESDiagram diagram : diagramList) {
                Long diagramId = diagram.getId();
                String icon1 = diagram.getIcon1();
                if (icon1 != null && !icon1.startsWith(httpResouceUrl)) {
                    diagram.setIcon1(new StringBuilder(httpResouceUrl).append(icon1).toString());
                }
                diagramIdResultSet.add(diagramId);
                diagramIdObjMap.put(diagramId, diagram);
            }
        }

        //获取视图拥有者id集合，并查出其详细用户信息
        Set<Long> userIdSet = diagramList.stream().map(ESDiagram::getUserId).collect(Collectors.toSet());
        userIdSet.add(SysUtil.getCurrentUserInfo().getId());
        CSysUser sysUser = new CSysUser();
        sysUser.setIds(userIdSet.toArray(new Long[0]));
        sysUser.setStatus(1);
        List<SysUser> sysUserByCdt = userApiSvc.getSysUserByCdt(sysUser);
        Map<Long, SysUser> sysOpMap = new HashMap<>(16);
        for (SysUser sysOp : sysUserByCdt) {
            sysOpMap.put(sysOp.getId(), sysOp);
        }

        //根据视图获取查询结果
        List<DiagramShareRecord> shareRecordResultList = diagramShareRecords
                .stream()
                .filter(record -> diagramIdResultSet.contains(record.getDiagramId()))
                .collect(Collectors.toList());

        //封装返回结果
        for (DiagramShareRecord shareRecord : shareRecordResultList) {
            Long diagramId = shareRecord.getDiagramId();
            if (diagramIdObjMap.containsKey(diagramId)) {
                //封装分享记录对应的视图信息
                ESDiagramShareRecordResult shareRecordResult = new ESDiagramShareRecordResult();
                BeanUtils.copyProperties(shareRecord, shareRecordResult);
                ESDiagram diagram = diagramIdObjMap.get(diagramId);
                shareRecordResult.setEsDiagram(diagram);
                shareRecordResult.setDiagramModifyTime(diagram.getModifyTime());
                //封装用户信息
                shareRecordResult.setOwnerSysUser(sysOpMap.get(shareRecordResult.getOwnerId()));
                shareRecordResult.setSharedSysUser(sysOpMap.get(shareRecordResult.getSharedUserId()));
                diagramShareRecordResults.add(shareRecordResult);
            }
        }
        return diagramShareRecordResults;
    }

    /**
     * 补充分享记录的视图信息和用户信息
     *
     * @param diagramShareRecords
     * @return
     */
    private List<ESDiagramShareRecordResult> fillSysOPAndESDiagramInfo(List<DiagramShareRecord> diagramShareRecords) {
        List<ESDiagramShareRecordResult> diagramShareRecordResults = new ArrayList<>();
        Set<Long> diagramIds = new HashSet<>();
        Set<Long> userIds = new HashSet<>();
        for (DiagramShareRecord shareRecord : diagramShareRecords) {
            diagramIds.add(shareRecord.getDiagramId());
            userIds.add(shareRecord.getOwnerId());
            userIds.add(shareRecord.getSharedUserId());
        }

        //查出所有用户信息，并按照id进行map
        CSysUser sysUser = new CSysUser();
        sysUser.setIds(userIds.toArray(new Long[0]));
        sysUser.setStatus(1);
        List<SysUser> sysUserByCdt = userApiSvc.getSysUserByCdt(sysUser);
        Map<Long, SysUser> sysOpMap = new HashMap<>();
        for (SysUser sysOp : sysUserByCdt) {
            Long id = sysOp.getId();
            sysOpMap.put(id, sysOp);
        }

        //查出所有的分享记录相关的视图列表，并按照id进行map
        CVcDiagram cVcDiagram = new CVcDiagram();
        cVcDiagram.setIds(diagramIds.toArray(new Long[0]));
        cVcDiagram.setStatus(1);
        cVcDiagram.setDataStatus(1);
        List<ESDiagram> esDiagramList = esDiagramSvc.queryESDiagramList(1L, cVcDiagram, null);
        Map<Long, ESDiagram> esDiagramMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(esDiagramList)) {
            for (ESDiagram esDiagram : esDiagramList) {
                Long id = esDiagram.getId();
                String icon1 = esDiagram.getIcon1();
                if (icon1 != null && !icon1.startsWith(httpResouceUrl)) {
                    esDiagram.setIcon1(new StringBuilder(httpResouceUrl).append(icon1).toString());
                }
                esDiagramMap.put(id, esDiagram);
            }
        }

        for (DiagramShareRecord diagramShareRecord : diagramShareRecords) {
            Long diagramId = diagramShareRecord.getDiagramId();
            if (esDiagramMap.containsKey(diagramId)) {
                //设置视图信息
                ESDiagramShareRecordResult recordResult = new ESDiagramShareRecordResult();
                BeanUtils.copyProperties(diagramShareRecord, recordResult);
                ESDiagram esDiagram = esDiagramMap.get(diagramId);
                recordResult.setEsDiagram(esDiagram);
                recordResult.setDiagramModifyTime(esDiagram.getModifyTime());
                //设置用户信息
                Long ownerId = recordResult.getOwnerId();
                Long sharedUserId = recordResult.getSharedUserId();
                recordResult.setOwnerSysUser(sysOpMap.get(ownerId));
                recordResult.setSharedSysUser(sysOpMap.get(sharedUserId));
                //添加到结果列表
                diagramShareRecordResults.add(recordResult);
            }
        }

        return diagramShareRecordResults;
    }

    @Override
    public Integer removeShareRecord(Long id) {
        //根据id查询视图信息，对视图鉴权
        DiagramShareRecord shareRecord = esShareDiagramDao.getById(id);
        Long diagramId = 0L;
        if (!BinaryUtils.isEmpty(shareRecord)) {
            diagramId = shareRecord.getDiagramId();
        }
        if (!diagramId.equals(0L)) {
            judgeSingleDiagramAuth(diagramId, null, true);
        }
        return esShareDiagramDao.deleteById(id);
    }

    @Override
    public Map<Long, Set<Long>> queryDiagramSharedUserIds(Long[] diagramIds) {

        Map<Long, Set<Long>> diagramUserIdMap = new HashMap<>();

        ShareRecordQuery shareRecordQuery = new ShareRecordQuery();
        shareRecordQuery.setDiagramIds(diagramIds);

        List<DiagramShareRecord> listByCdt = esShareDiagramDao.getListByCdt(shareRecordQuery);

        if (!CollectionUtils.isEmpty(listByCdt)) {
            for (DiagramShareRecord diagramShareRecord : listByCdt) {
                Long diagramId = diagramShareRecord.getDiagramId();
                Long sharedUserId = diagramShareRecord.getSharedUserId();

                if (diagramUserIdMap.containsKey(diagramId)) {
                    diagramUserIdMap.get(diagramId).add(sharedUserId);
                } else {
                    Set<Long> userIds = new HashSet<>();
                    userIds.add(sharedUserId);
                    diagramUserIdMap.put(diagramId, userIds);
                }
            }
        }

        return diagramUserIdMap;
    }

    @Override
    public DiagramShareLink addSharePic(DiagramShareLink shareBean) {
        DiagramShareLink saveShareBean = new DiagramShareLink();
        //1.判断视图是否存在，以及鉴权
        String diagramId = shareBean.getDiagramId();
        EamDiagramQuery diagramQuery = new EamDiagramQuery();
        diagramQuery.setDEnergyEqual(diagramId);
        List<ESDiagram> esDiagramList = esDiagramDao.getListByCdt(diagramQuery);
        if (!BinaryUtils.isEmpty(esDiagramList)) {
            judgeSingleDiagramAuth(esDiagramList.get(0).getId(), null, true);
        } else {
            throw new DiagramNotFoundException("视图不存在");
        }
        //2.获取图片base64内容
        String fileContent = shareBean.getContent();
        String link = "";
        Long id = null;
        String picName = UUID.randomUUID().toString() + ".png";
        try {
            //真实的存储路径
            String storePath = saveOrUpdatePng(picName, fileContent);
            //生成的访问链接
            link = "/diagram_image/" + picName;
            saveShareBean.setLink(link);
            saveShareBean.setDiagramId(diagramId);
            saveShareBean.setStorePath(storePath);
            saveShareBean.setLinkType(0);
            saveShareBean.setImageName(shareBean.getImageName());
            saveShareBean.setSheetId(shareBean.getSheetId());
            id = shareLinkDao.saveOrUpdate(saveShareBean);
        } catch (Exception e) {
            log.error("生成图片链接失败", e);
        }
        saveShareBean.setStorePath(null);
        saveShareBean.setId(id);
        // 返回重新拼接路径地址
        saveShareBean.setLink(httpResouceUrl + "/diagram_image/" + picName);
        return saveShareBean;
    }

    @Override
    public List<DiagramShareLink> querySharePic(String diagramId, Integer[] types) {
        DiagramShareLinkQuery shareBeanQuery = new DiagramShareLinkQuery();
        shareBeanQuery.setDiagramIdEqual(diagramId);
        shareBeanQuery.setLinkTypes(types);
        log.info("参数1：{}, 参数2:{}", diagramId, types);
        List<DiagramShareLink> shareLinkList = shareLinkDao.getListByCdt(shareBeanQuery);
        if (BinaryUtils.isEmpty(shareLinkList)) {
            return Collections.emptyList();
        }
        boolean cooperFlag = Arrays.stream(types).anyMatch(type -> type == 2);
        boolean picFlag = Arrays.stream(types).anyMatch(type -> type == 0);
        long currentSeconds = DateUtil.currentSeconds();
        //对过期协作链接处理
        if (cooperFlag) {
            List<DiagramShareLink> expireLinkList = new ArrayList<>();
            for (DiagramShareLink shareLink : shareLinkList) {
                if (shareLink.getLinkType() == 2) {
                    Long linkExpiration = shareLink.getLinkExpiration();
                    if (linkExpiration != null && linkExpiration <= currentSeconds) {
                        expireLinkList.add(shareLink);
                    }
                }
            }
            if (expireLinkList.size() > 0) {
                shareLinkList.removeAll(expireLinkList);
                List<Long> expireLinkIds = expireLinkList
                        .stream()
                        .map(DiagramShareLink::getId)
                        .collect(Collectors.toList());
                shareLinkDao.deleteByIds(expireLinkIds);
            }
        } else {
            shareLinkList = shareLinkDao.getListByCdt(shareBeanQuery);
        }
        EamDiagramQuery diagramQuery = new EamDiagramQuery();
        diagramQuery.setDEnergyEqual(diagramId);
        List<ESDiagram> esDiagramList = esDiagramDao.getListByCdt(diagramQuery);
        if (BinaryUtils.isEmpty(esDiagramList)) {
            throw new DiagramNotFoundException("视图不存在");
        }
        ESDiagram diagram = esDiagramList.get(0);
        //视图修改时间大于最新的链接生成时间，需要修改链接名称
        String diagramName = diagram.getName();
        Map<String, String> sheetIdNameMap = new HashMap<>();
        if (picFlag) {
            //如果查询分享图片，需要查询对应的sheetName
            ESDiagramSheetQuery sheetQuery = new ESDiagramSheetQuery();
            sheetQuery.setDiagramId(diagram.getId());
            List<ESDiagramSheetDTO> sheetList = esDiagramSheetDao.getListByCdt(sheetQuery);
            if (BinaryUtils.isEmpty(sheetList)) {
                throw new BinaryException("sheet为空");
            }
            sheetList.forEach(sheet -> sheetIdNameMap.put(sheet.getSheetId(), sheet.getName()));
        }
        for (DiagramShareLink shareLink : shareLinkList) {
            if (picFlag && shareLink.getLinkType().equals(0)) {
                shareLink.setImageName(sheetIdNameMap.get(shareLink.getSheetId()));
                String linkString = shareLink.getLink();
                if(StringUtil.isNotBlank(linkString)){
                    if(linkString.startsWith("/rsm")){
                        linkString = linkString.replace("/rsm","");
                    }
                    shareLink.setLink(new StringBuilder(httpResouceUrl).append(linkString).toString());
                }
            } else {
                shareLink.setImageName(diagramName);
            }
        }
        return shareLinkList;
    }

    @Override
    public Integer deleteSharePic(@RequestBody Long id) {
        DiagramShareLink shareLink = shareLinkDao.getById(id);
        if (!BinaryUtils.isEmpty(shareLink)) {
            String fileUrl = shareLink.getStorePath();
            if (!BinaryUtils.isEmpty(fileUrl)) {
                localPath = localPath.replace("/", FILE_SEPARATOR).replace("\\", FILE_SEPARATOR);
                fileUrl = fileUrl.startsWith(localPath) ? fileUrl : localPath + fileUrl;
                File delFile = new File(FileFilterUtil.parseFilePath(fileUrl));
                rsmUtils.deleteRsm(fileUrl);
                if (delFile.exists()) {
                    delFile.delete();
                    resourceSvc.saveSyncResourceInfo(fileUrl, httpResouceUrl + fileUrl, false, 1);
                    log.info("文件删除成功");
                }
            } else {
                log.info("fileUrl为空，无法删除服务器中文件，id={}", id);
            }
        }
        return shareLinkDao.deleteById(id);
    }

    @Override
    public DiagramShareLink addShareLink(DiagramShareLink shareBean) {
        String diagramId = shareBean.getDiagramId();
        EamDiagramQuery diagramQuery = new EamDiagramQuery();
        diagramQuery.setDEnergyEqual(diagramId);
        List<ESDiagram> esDiagramList = esDiagramDao.getListByCdt(diagramQuery);
        if (!BinaryUtils.isEmpty(esDiagramList)) {
            judgeSingleDiagramAuth(esDiagramList.get(0).getId(), null, true);
        } else {
            throw new DiagramNotFoundException("视图不存在");
        }
        Integer linkType = shareBean.getLinkType();
        String linkKey = RandomUtil.randomString(16);
        long newId = IdGenerator.createGenerator().getID();
        if (linkType == 1) {
            //分享链接
            String linkName = shareBean.getImageName();
            DiagramShareLink addShareLink = new DiagramShareLink();
            addShareLink.setId(newId);
            addShareLink.setImageName(linkName);
            addShareLink.setDiagramId(diagramId);
            addShareLink.setLinkType(linkType);
            addShareLink.setLinkKey(linkKey);
            addShareLink.setLink("/view/link/" + linkKey);
            shareLinkDao.saveOrUpdate(addShareLink);
        } else if (linkType == 2) {
            //协作链接
            String linkName = shareBean.getImageName();
            Integer permission = shareBean.getPermission();
            if (permission == null) {
                throw new BinaryException("权限不能为空");
            }
            DiagramShareLink addShareLink = new DiagramShareLink();
            addShareLink.setId(newId);
            addShareLink.setImageName(linkName);
            addShareLink.setDiagramId(diagramId);
            addShareLink.setLinkType(linkType);
            long expireTime = 60 * 60 * 24 * 7;
            addShareLink.setLinkExpiration(DateUtil.currentSeconds() + expireTime);
            addShareLink.setPermission(permission);
            addShareLink.setLinkKey(linkKey);
            addShareLink.setLink("/view/coopLink/" + linkKey);
            shareLinkDao.saveOrUpdate(addShareLink);
        } else if (linkType == 3) {
            //嵌入链接
            //查一下链接是否已经生成，分享链接和嵌入用的是同一个链接，如果已生成则直接返回
            DiagramShareLinkQuery linkQuery = new DiagramShareLinkQuery();
            linkQuery.setDiagramId(diagramId);
            linkQuery.setLinkType(linkType);
            List<DiagramShareLink> linkList = shareLinkDao.getListByCdt(linkQuery);
            if (!BinaryUtils.isEmpty(linkList)) {
                DiagramShareLink diagramShareLink = linkList.get(0);
                DiagramShareLink returnLink = new DiagramShareLink();
                returnLink.setId(diagramShareLink.getId());
                returnLink.setLinkKey(diagramShareLink.getLinkKey());
                return returnLink;
            }
            String linkName = shareBean.getImageName();
            DiagramShareLink addShareLink = new DiagramShareLink();
            addShareLink.setId(newId);
            addShareLink.setImageName(linkName);
            addShareLink.setDiagramId(diagramId);
            addShareLink.setLinkType(linkType);
            addShareLink.setLinkKey(linkKey);
            addShareLink.setLink("/view/link/" + linkKey);
            shareLinkDao.saveOrUpdate(addShareLink);
        } else {
            throw new BinaryException("linkType不符合要求");
        }
        DiagramShareLink linkResult = new DiagramShareLink();
        linkResult.setId(newId);
        linkResult.setLinkKey(linkKey);
        return linkResult;
    }

    @Override
    public Integer deleteShareLink(Long id) {
        return shareLinkDao.deleteById(id);
    }

    @Override
    public ESDiagramInfoDTO getDiagramByLinkKey(String linkKey) {
        //1.根据linkKey查询链接信息，获取diagramId
        DiagramShareLinkQuery shareBeanQuery = new DiagramShareLinkQuery();
        shareBeanQuery.setLinkKeyEqual(linkKey);
        List<DiagramShareLink> shareLinkList = shareLinkDao.getListByCdt(shareBeanQuery);
        if (BinaryUtils.isEmpty(shareLinkList)) {
            throw new DiagramNotFoundException("分享链接已删除");
        }
        String diagramId = shareLinkList.get(0).getDiagramId();
        EamDiagramQuery diagramQuery = new EamDiagramQuery();
        diagramQuery.setDEnergyEqual(diagramId);
        List<ESDiagram> esDiagramList = esDiagramDao.getListByCdt(diagramQuery);
        if (BinaryUtils.isEmpty(esDiagramList)) {
            throw new DiagramNotFoundException("视图不存在");
        }
        //2.根据diagramId查询视图详情
        Long realDiagramId = esDiagramList.get(0).getId();
        ESDiagramDTO esDiagramDTO = esDiagramSvc.queryESDiagramInfoById(realDiagramId, "shareLink", false);
        return esDiagramDTO.getDiagram();
    }

    @Override
    public String addUserByLink(String linkKey) {
        //1.判断链接是否存在以及是否过期
        DiagramShareLinkQuery shareBeanQuery = new DiagramShareLinkQuery();
        shareBeanQuery.setLinkKeyEqual(linkKey);
        shareBeanQuery.setLinkType(2);
        List<DiagramShareLink> shareLinkList = shareLinkDao.getListByCdt(shareBeanQuery);
        if (BinaryUtils.isEmpty(shareLinkList)) {
            throw new DiagramNotFoundException("分享链接已删除");
        }
        DiagramShareLink shareLink = shareLinkList.get(0);
        String diagramId = shareLink.getDiagramId();
        long linkExpiration = shareLink.getLinkExpiration();
        if (DateUtil.currentSeconds() >= linkExpiration) {
            throw new DiagramNotFoundException("分享链接已过期");
        }
        //判断链接对应的视图是否存在
        EamDiagramQuery diagramQuery = new EamDiagramQuery();
        diagramQuery.setDEnergyEqual(diagramId);
        List<ESDiagram> esDiagramList = esDiagramDao.getListByCdt(diagramQuery);
        if (BinaryUtils.isEmpty(esDiagramList)) {
            throw new DiagramNotFoundException("视图不存在");
        }
        Long realDiagramId = esDiagramList.get(0).getId();
        //2.判断用户是否拥有视图权限，拥有不赋权，没有则赋权
        ESDiagram esDiagram = judgeSingleDiagramAuth(realDiagramId, VALID_AUTH, true);
        if (BinaryUtils.isEmpty(esDiagram)) {
            //用户无权限，赋权
            ESDiagram diagram = esDiagramDao.getById(realDiagramId);
            List<DiagramShareRecordDTO> shareRecordDTOList = new ArrayList<>();
            DiagramShareRecordDTO addShareRecord = new DiagramShareRecordDTO();
            addShareRecord.setId(IdGenerator.createGenerator().getID());
            addShareRecord.setDiagramId(diagramId);
            addShareRecord.setPermission(shareLinkList.get(0).getPermission());
            addShareRecord.setOwnerId(diagram.getUserId());
            addShareRecord.setSharedUserId(SysUtil.getCurrentUserInfo().getId());
            addShareRecord.setDirType(diagram.getDirType());
            shareRecordDTOList.add(addShareRecord);
            saveOrUpdateShareRecordWithAuth(shareRecordDTOList);
        }
        //3.返回视图id给前台
        return diagramId;
    }

    @Override
    public Long updateShareLink(Long id, Integer permission) {
        //校验链接是否存在
        DiagramShareLink shareLink = shareLinkDao.getById(id);
        if (BinaryUtils.isEmpty(shareLink)) {
            throw new BinaryException("链接不存在");
        }
        DiagramShareLink updateLink = new DiagramShareLink();
        updateLink.setId(id);
        updateLink.setPermission(permission);
        return shareLinkDao.saveOrUpdate(updateLink);
    }

    @Override
    public void removeShareByDiagramIds(List<Long> removeIds) {
        esShareDiagramDao.deleteByQuery(QueryBuilders.termsQuery("diagramId", removeIds), true);
    }

    @Override
    public List<DiagramShareRecord> getByDiagramId(String diagramId) {
        ESDiagram diagram = esDiagramSvc.getEsDiagram(diagramId, 0);
        if(diagram == null){
            return Collections.emptyList();
        }
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("diagramId", diagram.getId()));
        query.must(QueryBuilders.termQuery("domainId", SysUtil.getCurrentUserInfo().getDomainId()));
        List<DiagramShareRecord> result = esShareDiagramDao.getListByQuery(query);
        if(result == null){
            return Collections.emptyList();
        }
        return result;
    }

    @Override
    public DiagramShareRecord queryShare(Long diagramId, Long sharedUserId) {
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("diagramId", diagramId));
        query.must(QueryBuilders.termQuery("sharedUserId", sharedUserId));
        List<DiagramShareRecord> records = esShareDiagramDao.getListByQuery(query);
        return CollectionUtils.isEmpty(records) ? null : records.get(0);
    }

    public String saveOrUpdatePng(String filePath, String fileContent) {
        byte[] bs;
        if (fileContent.length() >= fileContent.indexOf(";base64,") + 8) {
            String substring = fileContent.substring(fileContent.indexOf(";base64,") + 8);
            bs = Base64.decodeBase64(substring);
            filePath = this.saveOrUpdateResource(filePath, bs);
        }
        return filePath;
    }

    /**
     * 将字符串保存/更新 为文件，自动加上日期前缀
     *
     * @param filePath    文件名或文件相对路径
     * @param fileContent 文件内容
     * @return 文件名或文件相对路径
     */
    public String saveOrUpdateResource(String filePath, byte[] fileContent) {
        filePath = Paths.get("/diagram_image", filePath).toString();
        try {
            filePath = FileUtil.writeFile(filePath, fileContent);
        } catch (IOException e) {
            log.warn("写入文件：{} 错误！", filePath, e);
        }
        return filePath;
    }

    /**
     * <AUTHOR>
     * @Description 判断用户是否具有当前视图的权限
     * 1.不限制userId进行查询
     * 查不出---视图被删除
     * 查出来---判断视图的userId是否等于当前用户
     * 等于---有权限
     * 不等于---查询该用户是否被分享了该视图
     * 分享了，有两种权限，一种是查看，一种是编辑
     * 没分享，无权限，报错
     **/
    private ESDiagram judgeSingleDiagramAuth(Long diagramId, String type, Boolean needAuth) {
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        Long userId = currentUserInfo.getId();
        Long domainId = currentUserInfo.getDomainId();

        //1.不限制userId进行查询
        EamDiagramQuery diagramQuery = new EamDiagramQuery();
        diagramQuery.setId(diagramId);
        diagramQuery.setStatus(1);
        diagramQuery.setDataStatus(1);
        diagramQuery.setDomainId(domainId);
        if (!BinaryUtils.isEmpty(type) && "version".equals(type)) {
            diagramQuery.setHistoryVersionFlag(0);
        } else {
            diagramQuery.setHistoryVersionFlag(1);
        }
        List<ESDiagram> diagramList = esDiagramDao.getListByCdt(diagramQuery);
        //2.查不出，视图不存在，报错
        if (BinaryUtils.isEmpty(diagramList)) {
            throw new DiagramNotFoundException("操作视图不存在，请核对视图当前信息");
        }
        //3.查出来，判断其是否具有视图权限
        ESDiagram esDiagram = diagramList.get(0);
        if (needAuth) {
            //模板不需要校验权限
            if (esDiagram.getDiagramType().equals(3)) {
                return esDiagram;
            }
            if (esDiagram.getOwnerCode().equals(currentUserInfo.getLoginCode())) {
                //视图属于当前用户
                return esDiagram;
            } else {
                //视图不属于当前用户，判断该视图是否分享给了该用户
                Map<Long, Set<Long>> diagramIdShareRecordMap = queryDiagramSharedUserIds(new Long[]{diagramId});
                Set<Long> shareUserIdSet = diagramIdShareRecordMap.get(diagramId);
                if (!BinaryUtils.isEmpty(shareUserIdSet) && shareUserIdSet.contains(userId)) {
                    return esDiagram;
                } else {
                    log.info("shareLink-type:{}", type);
                    if (!StringUtils.isEmpty(type) && VALID_AUTH.equals(type)) {
                        return null;
                    } else {
                        throw new UnAuthorizedException("用户无当前视图权限");
                    }
                }

            }
        }
        return esDiagram;
    }

    /**
     * <AUTHOR>
     * @Description 判断用户是否具有当前视图的权限
     * 1.不限制userId进行查询
     * 查不出---视图被删除
     * 查出来---判断视图的userId是否等于当前用户
     * 等于---有权限
     * 不等于---查询该用户是否被分享了该视图
     * 分享了，有两种权限，一种是查看，一种是编辑
     * 没分享，无权限，报错
     **/
    private List<ESDiagram> judgeDiagramAuth(Long[] diagramIds, String type, Boolean needAuth) {
        Boolean isSingle = true;
        if (diagramIds.length != 1) {
            isSingle = false;
        }

        //1.不限制userId进行查询
        EamDiagramQuery diagramQuery = new EamDiagramQuery();
        diagramQuery.setIds(diagramIds);
        diagramQuery.setStatus(1);
        diagramQuery.setDataStatus(1);
        SysUser user = null;
        if (needAuth) {
            user = SysUtil.getCurrentUserInfo();
            diagramQuery.setDomainId(user.getDomainId());
        }
        if (!BinaryUtils.isEmpty(type) && "version".equals(type)) {
            diagramQuery.setHistoryVersionFlag(0);
        } else if (!BinaryUtils.isEmpty(type) && "CJ_D_HISTORY".equals(type)) {
            // 支持仓颉 方案视图版本查询 暂时不需要historyVersionFlag查询条件
        } else {
            diagramQuery.setHistoryVersionFlag(1);
        }
        List<ESDiagram> diagramList = esDiagramDao.getListByCdt(diagramQuery);
        //2.查不出，视图不存在，报错
        if (BinaryUtils.isEmpty(diagramList)) {
            throw new BinaryException("操作视图不存在，请核对视图当前信息");
        }
        //3.查出来，判断其是否具有视图权限
        if (isSingle && needAuth) {
            ESDiagram esDiagram = diagramList.get(0);
            if (esDiagram.getDiagramType().equals(3)) {
                return diagramList;
            }
            String loginCode = "";
            if (user != null) {
                loginCode = user.getLoginCode();
            }
            if (esDiagram.getOwnerCode().equals(loginCode)) {
                //视图属于当前用户
                return diagramList;
            } else {
                //视图不属于当前用户，判断该视图是否分享给了该用户
                Map<Long, Set<Long>> diagramIdShareRecordMap = eamShareDiagramSvc.queryDiagramSharedUserIds(diagramIds);
                Set<Long> shareUserIdSet = diagramIdShareRecordMap.get(diagramIds[0]);
                if (!BinaryUtils.isEmpty(shareUserIdSet) && shareUserIdSet.contains(user.getId())) {
                    //视图被分享给了当前用户
                    return diagramList;
                } else {
                    throw new UnAuthorizedException("用户无当前视图权限");
                }
            }
        }
        return diagramList;
    }

    public List<ESDiagramShareRecordResult> saveOrUpdateShareRecordWithAuth(List<DiagramShareRecordDTO> shareRecordDTOList) {
        List<DiagramShareRecord> shareRecordList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(shareRecordDTOList)) {
            //对待分享视图鉴权
            String[] shareDiagramIdArr = shareRecordDTOList.stream().map(DiagramShareRecordDTO::getDiagramId).distinct().toArray(String[]::new);
            Long[] longs = esDiagramSvc.queryDiagramInfoBydEnergy(shareDiagramIdArr);
            //judgeSingleDiagramAuth(longs[0], null, true);
            //新增分享记录
            for (DiagramShareRecordDTO diagramShareRecord : shareRecordDTOList) {
                if (diagramShareRecord.getId() == null) {
                    diagramShareRecord.setId(ESUtil.getUUID());
                }
                DiagramShareRecord dsr = diagramShareRecord.dtoToDiagramShareRecord(diagramShareRecord, esDiagramSvc.queryDiagramInfoByEnergy(diagramShareRecord.getDiagramId()));
                shareRecordList.add(dsr);
            }
            esShareDiagramDao.saveOrUpdateBatch(shareRecordList);
        }
        return this.fillSysOPAndESDiagramInfo(shareRecordList);
    }
}
