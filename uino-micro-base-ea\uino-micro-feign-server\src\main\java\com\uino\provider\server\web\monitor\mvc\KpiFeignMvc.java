package com.uino.provider.server.web.monitor.mvc;

import java.util.Collection;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.binary.core.io.Resource;
import com.binary.jdbc.Page;
import com.uino.bean.cmdb.business.ImportResultMessage;
import com.uino.bean.cmdb.business.ImportSheetMessage;
import com.uino.bean.monitor.base.ESKpiInfo;
import com.uino.bean.monitor.buiness.KpiRltBindDto;
import com.uino.bean.monitor.buiness.SearchKpiBean;
import com.uino.provider.feign.monitor.KpiFeign;
import com.uino.service.simulation.IKpiSvc;

@RestController
@RequestMapping("feign/kpi")
public class KpiFeignMvc implements KpiFeign {

    @Autowired
    private IKpiSvc svc;

    @Override
	public ESKpiInfo getKpiInfoById(Long id) {
		// TODO Auto-generated method stub
		return svc.getKpiInfoById(id);
	}
    
    @Override
    public List<ESKpiInfo> getKpiInfoByIds(Collection<Long> ids) {
        return svc.getKpiInfoByIds(ids);
    }

	@Override
    public Page<ESKpiInfo> queryKpiInfoPage(SearchKpiBean searchDto) {
        // TODO Auto-generated method stub
        return svc.queryKpiInfoPage(searchDto);
    }

    @Override
    public Long saveOrUpdate(ESKpiInfo saveDto) {
        // TODO Auto-generated method stub
        return svc.saveOrUpdate(saveDto);
    }

    @Override
    public void deleteByKpiIds(Collection<Long> kpiIds) {
        // TODO Auto-generated method stub
        svc.deleteByKpiIds(kpiIds);
    }

    @Override
    public Resource exportKpiInfos(Long domainId, Boolean isTpl) {
        // TODO Auto-generated method stub
        return svc.exportKpiInfos(domainId, isTpl);
    }

    @Override
    public ImportResultMessage importKpiInfos(Long domainId,MultipartFile file) {
        // TODO Auto-generated method stub
        return svc.importKpiInfos(domainId, file);
    }

    @Override
    public void bindCiClassRltToKpiInfo(KpiRltBindDto dto) {
        svc.bindCiClassRltToKpiInfo(dto);
    }

    @Override
    public void delCiClassRltToKpiInfo(KpiRltBindDto dto) {
        svc.delCiClassRltToKpiInfo(dto);
    }

	@Override
	public ImportSheetMessage saveOrUpdateBatch(Long domainId, List<ESKpiInfo> kpiInfos) {
		return svc.saveOrUpdateBatch(domainId, kpiInfos);
	}
}
