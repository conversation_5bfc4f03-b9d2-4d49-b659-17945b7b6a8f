package com.uinnova.product.eam.feign.cj;

import com.alibaba.fastjson.JSONObject;
import com.binary.framework.web.RemoteResult;
import com.uinnova.product.eam.base.diagram.model.ESDiagram;
import com.uinnova.product.eam.base.diagram.model.ESSimpleDiagramDTO;
import com.uinnova.product.eam.comm.model.CVcDiagramDir;
import com.uinnova.product.eam.comm.model.VcDiagramDir;
import com.uinnova.product.eam.comm.model.es.*;
import com.uinnova.product.eam.feign.config.EamFeignConfig;
import com.uinnova.product.eam.feign.config.FeignTimeOutConfig;
import com.uinnova.product.eam.model.*;
import com.uinnova.product.eam.model.asset.EamReleaseHistoryDTO;
import com.uinnova.product.eam.model.bm.ReleaseValidResponse;
import com.uinnova.product.eam.model.dmv.ClassDefinitionVO;
import com.uinnova.product.eam.model.dto.DiagramParamDto;
import com.uinnova.product.eam.model.dto.ElementDto;
import com.uinnova.product.eam.model.dto.ReleaseDiagramDTO;
import com.uinnova.product.eam.model.vo.AttentionVo;
import com.uinnova.product.eam.model.vo.MineAssetsVo;
import com.uinnova.product.eam.model.vo.ReleaseModuleDiagramVO;
import com.uinnova.product.eam.model.vo.WorkbenchChargeDoneVO;
import com.uinnova.product.vmdb.comm.model.ci.CcCi;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CiGroupPage;
import com.uino.bean.cmdb.base.ESCIAttrDefInfo;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.base.LibType;
import com.uino.bean.cmdb.business.dataset.DataSetExeResultSheetPage;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @description:
 * @author: ybb
 * @create: 2022-02-23 16:43
 */
@Component
@FeignClient(value = "${fuxi-feign-server-name:eam-fuxi}",
        path = "${fuxi-feign-server-path:tarsier-eam}/cj/system/diagram",
        configuration = {EamFeignConfig.class, FeignTimeOutConfig.class})
public interface SystemDiagramFeign {

    @PostMapping("/getCiDirInfo")
    public RemoteResult getCiDirInfo(@RequestBody String body);

    @PostMapping("/findCiSysList")
    public RemoteResult findCiSysList(@RequestBody String body);

    @PostMapping("/insertCiSysDesign")
    public RemoteResult insertCiSysDesign(@RequestBody String body);

    @PostMapping("/addSysDir")
    public RemoteResult addSysDir(@RequestBody SysDirVO sysDirVO);

    @PostMapping("/saveDiagramRelationSys")
    public RemoteResult saveDiagramRelationSys(@RequestBody EamDiagramRelationSysCdt eamDiagramRelationSysCdt, @RequestParam(defaultValue = "DESIGN") LibType libType);

    @GetMapping("/getEamDiagramRelationSys/{diagramEnergy}")
    public RemoteResult getEamDiagramRelationSys(@PathVariable("diagramEnergy") String diagramEnergy);

    /**
     * 名称模糊分页查询已发布的视图
     */
    @PostMapping("findDiagramLikeName")
    public RemoteResult findDiagramLikeName(@RequestBody PlanDiagramRequest request);

    @PostMapping("/releasePlan")
    public RemoteResult releasePlan(@RequestBody String body);

    @GetMapping("/getCiByCiCode")
    public CcCiInfo getCiByCiCode(@RequestParam(value = "ciCode") String ciCode);

    @PostMapping("/publishDiagram")
    public String publishDiagram(@RequestBody ReleaseDiagramDTO diagramDTO);

    @PostMapping("/checkDiagram")
    public ReleaseValidResponse.ValidType checkDiagram(@RequestBody DiagramParamDto diagramParamDto);

    @GetMapping("/getDiagramSysCiCode")
    public EamDiagramRelationSys getDiagramSysCiCode(@RequestParam("diagramEnergy") String diagramEnergy);

    /**
     * 根据ciCode查询绑定的视图id
     * @param ciCode
     * @return
     */
    @GetMapping("/getDiagramViewBySysCiCode")
    public RemoteResult getDiagramViewBySysCiCode(@RequestParam("ciCode") String ciCode,String diagramClassType);

    @GetMapping("/getBathCi")
    public List<CcCi> getBathCi(@RequestBody EchoCiVO echoCiVO);

    @PostMapping("/getBuildAssets")
    public AttentionVo getBuildAssets(@RequestBody MineAssetsVo mineAssetsVo);

    @GetMapping("/getMyAttentionPlans")
    public List<Long> getMyAttentionPlans();

    @GetMapping("/getRecentlyView")
    public List<EamRecentlyView> getRecentlyView(@RequestParam Integer buildType);

    @GetMapping("/findMyPublishDiagramList")
    public List<ESSimpleDiagramDTO> findMyPublishDiagramList();

    @PostMapping("/changeFlowByDiagramIds")
    Boolean changeFlowByDiagramIds(@RequestBody List<String> eIds, @RequestParam("flowStatus")Integer flowStatus);

    @PostMapping("/getDirName")
    public String getDirNameById(@RequestBody SysDirVO sysDirVO);

    @GetMapping("/getDiagramInfo")
    public ESDiagram getDiagramInfo(@RequestParam("energy") String energy);

    @GetMapping("/getReleaseDiagramVersion")
    public ESDiagram getReleaseDiagramVersion(@RequestParam("diagramId")String diagramId) ;


    @PostMapping("findDiagramByCdt")
    public List<Map<String,Object>> findDiagramByCdt (@RequestBody PlanDiagramRequest request);

    @RequestMapping(value = "/queryAllColumns")
    public List<EamArtifactElementVo> queryAllColumns(@RequestBody ElementDto elementDto);

    @GetMapping("/getClassInfoByCiCode")
    public ESCIInfo getClassInfoByCiCode(@RequestParam("ciCode")String ciCode);

    @PostMapping("/findDiagramListByDirIds")
    public List<ESDiagram> findDiagramListByDirIds(@RequestParam("dirIds") Set<Long> dirIds);

    @GetMapping("/findAttrDefList")
    public List<ESCIAttrDefInfo> findAttrDefList(@RequestParam("classId") Long classId);

    @PostMapping("/checkDiagramConflictByDIds")
    public Map<Integer, Object> checkDiagramConflictByDIds(@RequestBody List<String> diagramIds);

    @PostMapping("/batchPublishDiagram")
    public Map<String, String> batchPublishDiagram(@RequestBody Map<String, Long> publishDirSite);

    @GetMapping("/findDesignCiSysList")
    public CiGroupPage findDesignCiSysList();

    @PostMapping("/batchQueryAllColumns")
    public Map<Long,List<EamArtifactElementVo>> batchQueryAllColumns(@RequestBody List<Long> elementDtoList);

    @PostMapping("/findSimpleDiagramByCdt")
    public List<ESDiagram> findSimpleDiagramByCdt (@RequestBody PlanDiagramRequest request);

    @GetMapping("/findClassDefinitionList")
    public List<ClassDefinitionVO> findClassDefinitionList();

    @GetMapping("/getClassInfoById")
    public CcCiClassInfo getClassInfoById(@RequestParam("id")Long id);

    @GetMapping("/publishBusinessDiagram")
    public String publishBusinessDiagram(@RequestParam("diagramId") String diagramId, @RequestParam(value = "dirId", required = false) Long dirId,
                                          @RequestParam("dirType") Integer dirType);

    @RequestMapping("/queryHistoryDiagramByIds")
    public Map<String, List<EamReleaseHistoryDTO>> queryHistoryDiagramByIds(@RequestBody List<ReleaseModuleDiagramVO> releaseModuleDiagramVOs);

    @PostMapping("/findDiagramListByDiagramIds")
    public List<ESDiagram> findDiagramListByDiagramIds(@RequestBody List<String> diagramIds);

    @PostMapping("/deleteWorkbenchChargeDone")
    public void deleteWorkbenchChargeDone(@RequestBody WorkbenchChargeDoneVO workbenchChargeDoneVO);

    @GetMapping("/getAllRecentlyDiagram")
    public AttentionVo getAllRecentlyDiagram();

    @GetMapping("/getDesignRecentlyDiagram")
    public AttentionVo getDesignRecentlyDiagram();

    @PostMapping("/findDiagramDirList")
    public List<VcDiagramDir> findDiagramDirList(@RequestBody CVcDiagramDir cdt);

    @PostMapping("/getMineFocus")
    public AttentionVo getMineFocus();

    @PostMapping("/cancelAttention")
    public Integer cancelAttention(@RequestBody EamAttention eamAttention);

    @PostMapping("/cancelRecentlyView")
    public void cancelRecentlyView(@RequestBody EamRecentlyView eamRecentlyView);

    @GetMapping("/getMyAttentionDiagramIds")
    public List<String> getMyAttentionDiagramIds();

    @GetMapping("/findDataSetById")
    public JSONObject findDataSetById(@RequestParam("dataSetId") Long dataSetId);

    @GetMapping("/getResultUsingRuleByDataSetId")
    public List<DataSetExeResultSheetPage> getResultUsingRuleByDataSetId(@RequestParam("dataSetId") Long dataSetId);

    @PostMapping("/batchCancelAttention")
    public Integer batchCancelAttention(@RequestBody CEamAttention eamAttention);

    @PostMapping("/batchCancelRecentlyView")
    public void batchCancelRecentlyView(@RequestBody CEamRecentlyView eamRecentlyView);
    @GetMapping("/deletePublishMessage")
    public void deletePublishMessage(@RequestParam String businessKey,@RequestParam Integer dirType);
}