package com.uino.provider.server.web.monitor.mvc;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.binary.jdbc.Page;
import com.uino.bean.monitor.base.ESAlarm;
import com.uino.bean.monitor.buiness.AlarmInfo;
import com.uino.bean.monitor.buiness.AlarmQueryDto;
import com.uino.bean.monitor.buiness.SimulationAlarmBean;
import com.uino.provider.feign.monitor.AlarmFeign;
import com.uino.service.simulation.impl.AlarmSvc;

@RestController
@RequestMapping("feign/alarm")
public class AlarmFeignMvc implements AlarmFeign {

    @Autowired
    private AlarmSvc svc;

    @Override
    public Page<AlarmInfo> searchAlarms(AlarmQueryDto queryDto) {
        // TODO Auto-generated method stub
        return svc.searchAlarms(queryDto);
    }

    @Override
    public void saveAlarm(ESAlarm saveDto) {
        // TODO Auto-generated method stub
        svc.saveAlarm(saveDto);
    }

    @Override
    public Long countByQuery(AlarmQueryDto queryDto) {
        // TODO Auto-generated method stub
        return svc.countByQuery(queryDto);
    }

    @Override
    public ESAlarm queryOpenAlarm(String metric, Long classId, Long objId) {
        // TODO Auto-generated method stub
        return svc.queryOpenAlarm(metric, classId, objId);
    }

	@Override
	public void simulationAlarms(SimulationAlarmBean bean) {
		svc.simulationAlarms(bean);
	}
}
