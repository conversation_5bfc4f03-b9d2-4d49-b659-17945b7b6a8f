package com.uino.dao.cmdb;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.binary.core.util.BinaryUtils;
import com.uino.bean.cmdb.base.ESCIHistoryInfo;
import com.uino.bean.cmdb.base.ESCIHistoryInfo.ActionType;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.base.LibType;
import com.uino.dao.AbstractESBaseDao;
import com.uino.dao.ESConst;
import com.uino.util.sys.BeanUtil;
import com.uino.util.sys.LibTypeUtil;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.math.BigDecimal;
import java.util.*;
import java.util.Map.Entry;

/**
 * ES-CI历史
 * 
 * <AUTHOR>
 */
@Service
@Slf4j
public class ESCIHistorySvc extends AbstractESBaseDao<ESCIHistoryInfo, JSONObject> {

    @Override
    public String getIndex() {
        String libType = LibTypeUtil.getLibType();
        if(LibType.PRIVATE.toString().equals(libType)){
            return ESConst.INDEX_CMDB_CI_HISTORY_PRIVATE;
        }else if(LibType.DESIGN.toString().equals(libType)){
            return ESConst.INDEX_CMDB_CI_HISTORY_DESIGN;
        }
        return ESConst.INDEX_CMDB_CI_HISTORY;
    }

    @Override
    public String getType() {
        String libType = LibTypeUtil.getLibType();
        if(LibType.PRIVATE.toString().equals(libType)){
            return ESConst.INDEX_CMDB_CI_HISTORY_PRIVATE;
        }else if(LibType.DESIGN.toString().equals(libType)){
            return ESConst.INDEX_CMDB_CI_HISTORY_DESIGN;
        }
        return ESConst.INDEX_CMDB_CI_HISTORY;
    }

    @PostConstruct
    public void init() {
        super.initIndex(5);
    }

    @Override
    public Long saveOrUpdate(JSONObject obj, boolean isRefresh) {
        if(LibTypeUtil.isPrivate()) return 0L;
        return this.saveOrUpdateWithPrimaryKey(obj, isRefresh, "uniqueCode");
    }

    @Override
    public Map<String, Object> saveOrUpdateBatchMessage(JSONArray list, Boolean isAsync) {
        if(LibTypeUtil.isPrivate()) return Collections.emptyMap();
        return this.saveOrUpdateBatchMessageWithPrimaryKey(list, isAsync, "uniqueCode");
    }

    public Long saveOrUpdateHistoryInfo(ESCIInfo ciInfo, ActionType action) {
        if(LibTypeUtil.isPrivate()) return 0L;
        if (action == ESCIHistoryInfo.ActionType.SAVE_OR_UPDATE) {
            Map<String, Long> ciCodeMaxVersion = this.getCICodeMaxVersion(ciInfo.getDomainId());
            // 新增CI与历史库ciCode相同时版本号递增？当ciCode相同，所属分类不同时会有问题，还没想好如何避免
            // 此处修改ciInfo对象，CI保存时版本号已更新，无需维护
            if (ciInfo.getVersion().longValue() == 1) {
                Long version = ciCodeMaxVersion.get(ciInfo.getCiCode());
                if (version != null) {
                    ciInfo.setVersion(version + 1);
                }
            }
        }
        ESCIHistoryInfo historyInfo = this.buildCIHistoryInfo(ciInfo, action);
        return this.saveOrUpdate(historyInfo);
    }

    public Integer saveOrUpdateHistoryInfosBatch(List<ESCIInfo> ciInfos, ActionType action) {
        if(LibTypeUtil.isPrivate()) return 0;
        if (!BinaryUtils.isEmpty(ciInfos)) {
            JSONArray historyInfos = new JSONArray();
            Map<String, Long> ciCodeMaxVersion = this.getCICodeMaxVersion(ciInfos.get(0).getDomainId());
            for (ESCIInfo ciInfo : ciInfos) {
                if (action == ESCIHistoryInfo.ActionType.SAVE_OR_UPDATE) {
                    // 新增CI与历史库ciCode相同时版本号递增
                    if (ciInfo.getVersion() == 1) {
                        Long version = ciCodeMaxVersion.get(ciInfo.getCiCode());
                        if (version != null) {
                            ciInfo.setVersion(version + 1);
                        }
                    }
                }
                ESCIHistoryInfo historyInfo = this.buildCIHistoryInfo(ciInfo, action);
                String jsonStr = JSON.toJSONString(historyInfo);
                JSONObject json = JSON.parseObject(jsonStr);
                historyInfos.add(json);
            }
            this.saveOrUpdateBatchMessage(historyInfos, true);
        }
        return 1;
    }

    private ESCIHistoryInfo buildCIHistoryInfo(ESCIInfo esciInfo, ActionType action) {
        action = action == null ? ActionType.SAVE_OR_UPDATE : action;
        ESCIHistoryInfo historyInfo = ESCIHistoryInfo.builder().uniqueCode(esciInfo.getId() + "_" + esciInfo.getVersion()).version(esciInfo.getVersion()).action(action.getValue()).build();
        historyInfo.setDomainId(esciInfo.getDomainId());
        BeanUtil.copyProperties(esciInfo, historyInfo, "createTime", "modifyTime");
        Map<String, Object> attrs = new HashMap<String, Object>();
        Iterator<Entry<String, Object>> it = historyInfo.getAttrs().entrySet().iterator();
        while (it.hasNext()) {
            Entry<String, Object> entry = it.next();
            String key = entry.getKey();
            Object val = entry.getValue() == null ? "" : entry.getValue();
            if (val instanceof Number) {
                attrs.put(key, new BigDecimal(val.toString()).toString());
            } else if (val instanceof String) {
                attrs.put(key, val);
            } else {
                attrs.put(key, JSON.toJSONString(val));
            }
        }
        historyInfo.setAttrs(attrs);
        return historyInfo;
    }

    private Map<String, Long> getCICodeMaxVersion(Long domainId) {
        Map<String, Long> res = new HashMap<>();
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("domainId", domainId));
        Map<String, BigDecimal> rltIdMaxVersionMap = super.groupByFieldMaxVal("ciCode.keyword", "version", query);
        if (rltIdMaxVersionMap != null && rltIdMaxVersionMap.size() > 0) {
            rltIdMaxVersionMap.forEach((key, val) -> res.put(key, val.longValue()));
        }
        return res;
    }
}
