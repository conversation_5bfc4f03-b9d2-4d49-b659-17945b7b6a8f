package com.uinnova.product.eam.comm.model;




import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;


@Comment("用户组表[VC_GROUP_USER]")
public class CVcGroupUser implements Condition {
	private static final long serialVersionUID = 1L;


	@Comment("ID[ID] operate-Equal[=]")
	private Long id;


	@Comment("ID[ID] operate-In[in]")
	private Long[] ids;


	@Comment("ID[ID] operate-GTEqual[>=]")
	private Long startId;

	@Comment("ID[ID] operate-LTEqual[<=]")
	private Long endId;


	@Comment("组ID[GROUP_ID] operate-Equal[=]")
	private Long groupId;


	@Comment("组ID[GROUP_ID] operate-In[in]")
	private Long[] groupIds;


	@Comment("组ID[GROUP_ID] operate-GTEqual[>=]")
	private Long startGroupId;

	@Comment("组ID[GROUP_ID] operate-LTEqual[<=]")
	private Long endGroupId;


	@Comment("用户ID[USER_ID] operate-Equal[=]")
	private Long userId;


	@Comment("用户ID[USER_ID] operate-In[in]")
	private Long[] userIds;


	@Comment("用户ID[USER_ID] operate-GTEqual[>=]")
	private Long startUserId;

	@Comment("用户ID[USER_ID] operate-LTEqual[<=]")
	private Long endUserId;


	@Comment("权限范围[AUTH_REGION] operate-Equal[=]    1=全部")
	private Integer authRegion;


	@Comment("权限范围[AUTH_REGION] operate-In[in]    1=全部")
	private Integer[] authRegions;


	@Comment("权限范围[AUTH_REGION] operate-GTEqual[>=]    1=全部")
	private Integer startAuthRegion;

	@Comment("权限范围[AUTH_REGION] operate-LTEqual[<=]    1=全部")
	private Integer endAuthRegion;


	@Comment("所属域[DOMAIN_ID] operate-Equal[=]")
	private Long domainId;


	@Comment("所属域[DOMAIN_ID] operate-In[in]")
	private Long[] domainIds;


	@Comment("所属域[DOMAIN_ID] operate-GTEqual[>=]")
	private Long startDomainId;

	@Comment("所属域[DOMAIN_ID] operate-LTEqual[<=]")
	private Long endDomainId;


	@Comment("创建时间[CREATE_TIME] operate-Equal[=]    yyyyMMddHHmmss")
	private Long createTime;


	@Comment("创建时间[CREATE_TIME] operate-In[in]    yyyyMMddHHmmss")
	private Long[] createTimes;


	@Comment("创建时间[CREATE_TIME] operate-GTEqual[>=]    yyyyMMddHHmmss")
	private Long startCreateTime;

	@Comment("创建时间[CREATE_TIME] operate-LTEqual[<=]    yyyyMMddHHmmss")
	private Long endCreateTime;


	@Comment("修改时间[MODIFY_TIME] operate-Equal[=]    yyyyMMddHHmmss")
	private Long modifyTime;


	@Comment("修改时间[MODIFY_TIME] operate-In[in]    yyyyMMddHHmmss")
	private Long[] modifyTimes;


	@Comment("修改时间[MODIFY_TIME] operate-GTEqual[>=]    yyyyMMddHHmmss")
	private Long startModifyTime;

	@Comment("修改时间[MODIFY_TIME] operate-LTEqual[<=]    yyyyMMddHHmmss")
	private Long endModifyTime;




	public Long getId() {
		return this.id;
	}
	public void setId(Long id) {
		this.id = id;
	}


	public Long[] getIds() {
		return this.ids;
	}
	public void setIds(Long[] ids) {
		this.ids = ids;
	}


	public Long getStartId() {
		return this.startId;
	}
	public void setStartId(Long startId) {
		this.startId = startId;
	}


	public Long getEndId() {
		return this.endId;
	}
	public void setEndId(Long endId) {
		this.endId = endId;
	}


	public Long getGroupId() {
		return this.groupId;
	}
	public void setGroupId(Long groupId) {
		this.groupId = groupId;
	}


	public Long[] getGroupIds() {
		return this.groupIds;
	}
	public void setGroupIds(Long[] groupIds) {
		this.groupIds = groupIds;
	}


	public Long getStartGroupId() {
		return this.startGroupId;
	}
	public void setStartGroupId(Long startGroupId) {
		this.startGroupId = startGroupId;
	}


	public Long getEndGroupId() {
		return this.endGroupId;
	}
	public void setEndGroupId(Long endGroupId) {
		this.endGroupId = endGroupId;
	}


	public Long getUserId() {
		return this.userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}


	public Long[] getUserIds() {
		return this.userIds;
	}
	public void setUserIds(Long[] userIds) {
		this.userIds = userIds;
	}


	public Long getStartUserId() {
		return this.startUserId;
	}
	public void setStartUserId(Long startUserId) {
		this.startUserId = startUserId;
	}


	public Long getEndUserId() {
		return this.endUserId;
	}
	public void setEndUserId(Long endUserId) {
		this.endUserId = endUserId;
	}


	public Integer getAuthRegion() {
		return this.authRegion;
	}
	public void setAuthRegion(Integer authRegion) {
		this.authRegion = authRegion;
	}


	public Integer[] getAuthRegions() {
		return this.authRegions;
	}
	public void setAuthRegions(Integer[] authRegions) {
		this.authRegions = authRegions;
	}


	public Integer getStartAuthRegion() {
		return this.startAuthRegion;
	}
	public void setStartAuthRegion(Integer startAuthRegion) {
		this.startAuthRegion = startAuthRegion;
	}


	public Integer getEndAuthRegion() {
		return this.endAuthRegion;
	}
	public void setEndAuthRegion(Integer endAuthRegion) {
		this.endAuthRegion = endAuthRegion;
	}


	public Long getDomainId() {
		return this.domainId;
	}
	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}


	public Long[] getDomainIds() {
		return this.domainIds;
	}
	public void setDomainIds(Long[] domainIds) {
		this.domainIds = domainIds;
	}


	public Long getStartDomainId() {
		return this.startDomainId;
	}
	public void setStartDomainId(Long startDomainId) {
		this.startDomainId = startDomainId;
	}


	public Long getEndDomainId() {
		return this.endDomainId;
	}
	public void setEndDomainId(Long endDomainId) {
		this.endDomainId = endDomainId;
	}


	public Long getCreateTime() {
		return this.createTime;
	}
	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}


	public Long[] getCreateTimes() {
		return this.createTimes;
	}
	public void setCreateTimes(Long[] createTimes) {
		this.createTimes = createTimes;
	}


	public Long getStartCreateTime() {
		return this.startCreateTime;
	}
	public void setStartCreateTime(Long startCreateTime) {
		this.startCreateTime = startCreateTime;
	}


	public Long getEndCreateTime() {
		return this.endCreateTime;
	}
	public void setEndCreateTime(Long endCreateTime) {
		this.endCreateTime = endCreateTime;
	}


	public Long getModifyTime() {
		return this.modifyTime;
	}
	public void setModifyTime(Long modifyTime) {
		this.modifyTime = modifyTime;
	}


	public Long[] getModifyTimes() {
		return this.modifyTimes;
	}
	public void setModifyTimes(Long[] modifyTimes) {
		this.modifyTimes = modifyTimes;
	}


	public Long getStartModifyTime() {
		return this.startModifyTime;
	}
	public void setStartModifyTime(Long startModifyTime) {
		this.startModifyTime = startModifyTime;
	}


	public Long getEndModifyTime() {
		return this.endModifyTime;
	}
	public void setEndModifyTime(Long endModifyTime) {
		this.endModifyTime = endModifyTime;
	}


}


