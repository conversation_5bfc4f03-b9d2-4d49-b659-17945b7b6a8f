package com.uinnova.product.eam.service.fx;

import com.uinnova.product.eam.db.bean.DiagramChangeData;

import java.util.List;
import java.util.Map;

/**
 *  伏羲 通用检出相关
 */
public interface GeneralPullSvc {

    /**
     *  视图检出
     * @param diagramId
     * @returnenv
     */
    String generalCheckOutDiagram(String diagramId, Long dirId, Integer actionType, String diagramName);

    /**
     *  视图检出前校验 - 本地是否存在与设计库关联视图
     * @param diagramId
     * @return
     */
    Map<String, Object> existRelateData(String diagramId, Long dirId);

    /**
     *  视图检出前校验 - 设计库视图与本地视图是否存在版本冲突
     * @param diagramIds
     * @return
     */
    List<DiagramChangeData> pullCheck(List<String> diagramIds);
}
