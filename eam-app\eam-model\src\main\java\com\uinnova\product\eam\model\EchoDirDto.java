package com.uinnova.product.eam.model;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Classname EchoDirDto
 * @Date 2022/3/17 16:03
 */
@Data
public class EchoDirDto {

    private Long dirId;

    //0表示普通文件夹 1表示系统文件夹 2表示分类文件夹 3.表示系统文件夹下面的自动生成文件夹
    private Integer sysDir;

    //回显的文件夹名称
    private String echoDirName;

    //显示普通文件夹树
    private List<OrdinaryDirDto> ordinaryDirDto;

}
