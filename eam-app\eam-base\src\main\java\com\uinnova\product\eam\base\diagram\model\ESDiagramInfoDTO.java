package com.uinnova.product.eam.base.diagram.model;

import com.binary.framework.bean.annotation.Comment;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description //视图链接dto
 * @Date 16:26 2021/6/3
 * @Param
 * @return
 **/
@Data
public class ESDiagramInfoDTO extends ESDiagramEnergy implements Serializable {
	private static final long serialVersionUID = 1L;

	@JsonIgnore
	private Long newCopyId;

	@Comment("转化的视图加密id")
	private List<String> convertIds;

	@Comment("视图id[id]")
	@JsonIgnore
	private Long id;

	@Comment("视图名称[NAME]")
	private String name;

	@Comment("视图设置")
	private String diagramSetting;

	@Comment("所属用户[USER_ID]")
	private Long userId;

	@Comment("所属用户编码[OWNER_CODE]")
	private String ownerCode;

	@Comment("所属目录[DIR_ID]")
	private Long dirId;

	@Comment("目录类型[DIR_TYPE]   ")
	private Integer dirType;

	@Comment("视图类型[DIAGRAM_TYPE]    视图类型:1=视图 3=公共模版 4=个人模板 5=wiki新建视图")
	private Integer diagramType;

	@Comment("历史版本标识，1--主版本，0--历史版本")
	private Integer historyVersionFlag;

	@Comment("视图描述[DIAGRAM_DESC]")
	private String diagramDesc;

	@Comment("视图SVG[DIAGRAM_SVG]")
	private String diagramSvg;

	@Comment("视图XML[DIAGRAM_XML]")
	private String diagramXml;

	@Comment("视图JSON[DIAGRAM_JSON]    视图json格式信息")
	private String diagramJson;

	@Comment("背景图[DIAGRAM_BG_IMG]")
	private String diagramBgImg;

	@Comment("背景样式[DIAGRAM_BG_CSS]")
	private String diagramBgCss;

	@Comment("视图图标_1[ICON_1]")
	private String icon1;

	@Comment("视图图标_2[ICON_2]")
	private String icon2;

	@Comment("视图图标_3[ICON_3]")
	private String icon3;

	@Comment("视图图标_4[ICON_4]")
	private String icon4;

	@Comment("视图图标_5[ICON_5]")
	private String icon5;

	@Comment("是否公开[IS_OPEN]    是否公开:1=开放 0=私有")
	private Integer isOpen;

	@Comment("公开时间[OPEN_TIME]")
	private Long openTime;

	@Comment("数据驱动类型[DATA_UP_TYPE]    数据驱动类型:1=不更新 2=标记提示 3=自动更新")
	private Integer dataUpType;

	@Comment("视图状态[STATUS]    视图状态:1=正常 0=回收站")
	private Integer status;

	@Comment("CI3D坐标[CI_3D_POINT]")
	private String ci3dPoint;

	@Comment("CI3D坐标2[CI_3D_POINT2]")
	private String ci3dPoint2;

	@Comment("搜索字段[SEARCH_FIELD]    搜索字段:视图名称")
	private String searchField;

	@Comment("组合视图行数[COMB_ROWS]    组合视图行数:组合视图字段")
	private Integer combRows;

	@Comment("组合视图列数[COMB_COLS]    组合视图列数:组合视图字段")
	private Integer combCols;

	@Comment("查看次数[READ_COUNT]")
	private Long readCount;

	@Comment("应用关联CI[APP_RLT_CI_CODE]    关联CI:针对应用墙点击CI弹出组合视图")
	private String appRltCiCode;

	@Comment("参照版本id[REFER_VERSION_ID]")
	private Long referVersionId;

	@Comment("所属域[DOMAIN_ID]")
	private Long domainId;

	@Comment("数据状态[DATA_STATUS]    数据状态:0=从回收站删除，1=正常")
	private Integer dataStatus;

	@Comment("创建人[CREATOR]")
	private String creator;

	@Comment("修改人[MODIFIER]")
	private String modifier;

	@Comment("转换老视图标识，0-老视图")
	private Integer transFlag;

	@Comment("创建时间[CREATE_TIME]    创建时间:yyyyMMddHHmmss")
	private Long createTime;

	@Comment("更新时间[MODIFY_TIME]    更新时间:yyyyMMddHHmmss")
	private Long modifyTime;

	@Comment("缩略图保存时间[THUMBNAIL_SAVE_ TIME]    yyyyMMddHHmmss")
	private Long thumbnailSaveTime;

	@Comment("主题文件夹[SUBJECT_ID]")
	private Long subjectId;

	private Long oldDirId;

	@Comment("视图svg内容信息,用于生产缩略图")
	private String svg;

	@Comment("缩略图base64编码")
	private String thumbnail;

	@Comment("版本描述")
	private String versionDesc;

	@Comment("视图版本id")
	private Long diagramVersionId;

	@Comment("版本名称")
	private String versionName;

	@Comment("版本描述文件存储路径")
	private String versionDescPath;

	@Comment("自定义版本号")
	private String versionNo;

	@Comment("字体库")
	private String fontLibrary;

	@Comment("视图关联元素信息")
	private List<VcDiagramEle> diagramEles;

	@Comment("视图分享记录")
	private List<DiagramShareRecordResult> shareRecords = new ArrayList<>();

	@Comment("转换为模板的时间[TEM_CONVERT_TIME]")
	private Long temConvertTime;

	@Comment("视图类型[VIEW_TYPE]")
	private String viewType;

	@Comment("视图类型  1: 空白视图 2：制品视图 3：基于模版 4：默认视图")
	private Integer diagramSubType;

	@Comment("视图使用形状json")
	private List<ESDiagramShape> leftPanelModel;

	@Comment("视图的水印信息")
	private ESWaterMarkInfo watermarkInfo;

	@Comment("视图的信息")
	private String colorHistoryMap;

	@Comment("视图版本标识")
	private String version;

	/**
	 * 关联的模板id (不写入数据库)
	 */
	private Long temDirId;

	/**
	 * 模板名称 (不写入数据库)
	 */
	private String temDirName;

	/**
	 * 模板类型 (不写入数据库)
	 */
	private Integer temType;

	@Comment("sheet集合")
	private List<ESDiagramSheetDTO> sheetList;

	@Comment("model集合")
	private List<ESDiagramModel> modelList;

	@Comment("非莫奈视图：0否，1是")
	private Integer nonMonet;

	@Comment("视图类型：1视图，2矩阵，3清单")
	private Integer queryType;

	@Comment("系统编码")
	private String ciCode;

	@Comment("视图ID加密字段")
	@JsonProperty("id")
	private String dEnergy;

	@Comment("附加的ciId")
	private long attachCiId;

	@Comment("附加的ciCode")
	private String attachCiCode;

	@Comment("视图本地版本")
	private Integer localVersion = 0;

	@Comment("视图发布版本")
	private Integer releaseVersion = 0;

	@Comment("视图发布后新视图id")
	private String releaseDiagramId;

	@Comment("发布说明")
	private String releaseDesc;

	@Comment("预制视图发布ID")
	private String prepareDiagramId;

	@Comment("视图是否处于流程中 true = 流程中 / false = 未在流程中")
	private Integer flowStatus;

	@Comment("json文件的类型 diagram_json:表示视图 artifact_json:表示制品 visualmodel_json:表示元模型")
	private String ident;

	public void setNewCopyId(Long newCopyId) {
		this.newCopyId = newCopyId;
	}
}


