package com.uinnova.product.eam.base.diagram.model;

import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

/**
 * @Classname
 * @Description 我的图形
 * <AUTHOR>
 * @Date 2021-08-24-10:21
 */
@Data
public class ESEnterpriseSysUser implements EntityBean {

    private static final Long serialVersionUID = 1L;

    @Comment("key")
    private Long id;

    @Comment("userId")
    private Long userId;

    // thingjs唯一标识mmd_id
    @Comment("mmdId")
    private Long mmdId;

    @Comment("createTime")
    private Long createTime;

    @Comment("modifyTime")
    private Long modifyTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getMmdId() {
        return mmdId;
    }

    public void setMmdId(Long mmdId) {
        this.mmdId = mmdId;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }
}
