package com.uinnova.product.eam.model.dto;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.comm.model.es.FolderApprovalUser;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 文件夹审批用户配置响应体
 * <AUTHOR>
 */
@Data
public class FolderApprovalManagerDTO implements Serializable {
    @Comment("主键")
    private Long id;
    @Comment("目录id")
    private Long dirId;
    @Comment("领域id")
    private Long domainId;
    @Comment("审批规则：或签0")
    private Integer rule;
    @Comment("用户信息")
    private List<FolderApprovalUser> users = new ArrayList<>();
}
