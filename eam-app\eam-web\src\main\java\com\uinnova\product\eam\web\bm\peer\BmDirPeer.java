package com.uinnova.product.eam.web.bm.peer;

import com.binary.core.exception.MessageException;
import com.binary.core.util.BinaryUtils;
import com.binary.jdbc.Page;
import com.uinnova.product.eam.base.diagram.model.CVcDiagram;
import com.uinnova.product.eam.base.diagram.model.ESDiagram;
import com.uinnova.product.eam.base.diagram.model.ESDiagramDTO;
import com.uinnova.product.eam.base.diagram.model.ESSimpleDiagramDTO;
import com.uinnova.product.eam.base.model.PageDTO;
import com.uinnova.product.eam.model.diagram.DiagramDirResultInfo;
import com.uinnova.product.eam.service.IEamArtifactSvc;
import com.uinnova.product.eam.service.diagram.ESDiagramSvc;
import com.uinnova.product.eam.web.bm.bean.QueryDir;
import com.uino.bean.cmdb.base.LibType;
import com.uino.bean.permission.base.SysUser;
import com.uino.util.sys.SysUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Deprecated
@Slf4j
@Service
public class BmDirPeer {

    @Resource
    ESDiagramSvc diagramApiClient;

    @Resource
    IEamArtifactSvc eamArtifactSvc;


    public DiagramDirResultInfo getModuleDiagramPage(PageDTO<QueryDir> pageDTO) {
        SysUser user = SysUtil.getCurrentUserInfo();
        String loginCode = user.getLoginCode();
        QueryDir queryData = pageDTO.getData();
        CVcDiagram cdt = new CVcDiagram();
        cdt.setDataStatus(queryData.getDataStatus());
        cdt.setStatus(1);

        cdt.setName(queryData.getLike());
        if (BinaryUtils.isEmpty(queryData.getViewType())) {
            //进行发布后区分
            cdt.setViewType(queryData.getViewType());
        }

        cdt.setDiagramTypes(new Integer[]{1, 2, 3, 4});

        Long artifactId;
        if (queryData.getLibType() == LibType.PRIVATE) {
            cdt.setOwnerCodeEqual(loginCode);
            cdt.setIsOpen(0);
        } else {
            cdt.setIsOpen(1);
            //根据文件夹名称、
            if (!BinaryUtils.isEmpty(queryData.getDirName())) {
                artifactId = eamArtifactSvc.getIdByArtifactName(queryData.getDirName());
                if (BinaryUtils.isEmpty(artifactId)) {
                    cdt.setDiagramType(1);
                    cdt.setDiagramSubType(1);
                } else {
                    cdt.setViewType(artifactId.toString());
                }
            }
        }
        Page<ESSimpleDiagramDTO> diagramPage = new Page<>();
        try {
            diagramPage = diagramApiClient.queryESDiagramInfoPage(user.getDomainId(), pageDTO.getPageNum(), pageDTO.getPageSize(), cdt, queryData.getOrders());
        } catch (MessageException e1) {
            log.error(e1.getMessage());
            log.error(e1.getMessage(), e1);
        } catch (Exception e) {
            log.error(e.getMessage());
            log.error(e.getMessage(), e);
        }
        Page<ESDiagramDTO> page = new Page<>();
        page.setPageNum(diagramPage.getPageNum());
        page.setPageSize(diagramPage.getPageSize());
        page.setTotalPages(diagramPage.getTotalPages());
        page.setTotalRows(diagramPage.getTotalRows());
        if (!BinaryUtils.isEmpty(diagramPage.getData())) {
            List<ESDiagramDTO> diagramInfoAllList = diagramApiClient.queryDiagramInfoByIds(diagramPage.getData().stream().map(ESSimpleDiagramDTO::getDiagram)
                    .map(ESDiagram::getId).toArray(Long[]::new), null, false, false);
            if (CollectionUtils.isNotEmpty(diagramInfoAllList)) {
                diagramInfoAllList.sort((o1, o2) -> (int) (o2.getDiagram().getModifyTime() - o1.getDiagram().getModifyTime()));
                page.setData(diagramInfoAllList);
            }
        }
        DiagramDirResultInfo resultInfo = new DiagramDirResultInfo();
        resultInfo.setDiagramDir(null);
        resultInfo.setChildrenDirs(Collections.emptyList());
        resultInfo.setDirDiagramCountMap(Collections.emptyMap());
        resultInfo.setDirHasNodeMap(Collections.emptyMap());
        resultInfo.setDiagramInfoPage(page);
        return resultInfo;
    }
}
