package com.uinnova.product.eam.web.asset.mvc;

import com.binary.framework.util.ControllerUtils;
import com.uinnova.product.eam.base.model.DataPermissionInfo;
import com.uino.service.util.FileUtil;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.List;

@RestController
@RequestMapping("/asset/permission")
public class DataPermissionMVC {

    @PostMapping("/getDataPermission")
    public void getDataPermission(HttpServletRequest request, HttpServletResponse response){
//        DataPermissionInfo dataPermissionInfo = JSONObject.parseObject(Env.DATA_PERMISSION.getPageConfig(), DataPermissionInfo.class);
        List<DataPermissionInfo> list = FileUtil.getData("/initdata/uino_data_permission_function.json", DataPermissionInfo.class);
        ControllerUtils.returnJson(request, response, list);
    }

}
