package com.uino.api.client.sys.local;

import com.uino.api.client.sys.IOperateLogModuleApiSvc;
import com.uino.bean.sys.base.ESOperateLogModule;
import com.uino.service.sys.microservice.IOperateLogModuleSvc;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class OperateLogModuleApiSvcLocal implements IOperateLogModuleApiSvc {

    @Autowired
    private IOperateLogModuleSvc operateLogModuleSvc;

    @Override
    public List<ESOperateLogModule> getAll() {
        return operateLogModuleSvc.getAll();
    }

    @Override
    public ESOperateLogModule getModuleInfoByMvc(String mvcPath) {
        return operateLogModuleSvc.getModuleInfoByMvc(mvcPath);
    }
}
