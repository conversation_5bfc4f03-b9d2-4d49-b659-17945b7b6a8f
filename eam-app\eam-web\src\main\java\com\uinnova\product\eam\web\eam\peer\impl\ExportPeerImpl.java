package com.uinnova.product.eam.web.eam.peer.impl;

import com.binary.core.exception.BinaryException;
import com.binary.core.i18n.LanguageResolver;
import com.binary.core.util.BinaryUtils;
import com.uinnova.product.eam.comm.model.CVcDiagramEle;
import com.uinnova.product.eam.comm.model.VcDiagramEle;
import com.uinnova.product.eam.service.ICISwitchSvc;
import com.uinnova.product.eam.service.VcDiagramSvc;
import com.uinnova.product.eam.service.impl.IamsCIRltSwitchSvc;
import com.uinnova.product.eam.web.eam.peer.IExportPeer;
import com.uino.bean.cmdb.base.LibType;
import com.uino.bean.cmdb.business.ExportCiDto;
import com.uino.bean.cmdb.query.ESRltSearchBean;
import com.uino.util.sys.SysUtil;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class ExportPeerImpl implements IExportPeer {

    @Resource
    private ICISwitchSvc iciSwitchSvc;

    @Resource
    private VcDiagramSvc diagramSvc;

    @Resource
    private IamsCIRltSwitchSvc cIRltSwitchSvcIams;

    @Override
    public ResponseEntity<byte[]> exportCiByDiagramId(Long diagramId, LibType libType){
        Set<Long> ids = selectDiagramEleList(diagramId, 1);
        ExportCiDto exportDto = new ExportCiDto();
        exportDto.setHasClsDef(1);
        exportDto.setHasData(1);
        exportDto.setCiIds(ids);
        exportDto.setCiClassIds(Collections.emptySet());
        return iciSwitchSvc.exportCiOrClass(exportDto, libType);
    }

    @Override
    public com.binary.core.io.Resource exportRltByDiagramId(Long diagramId, LibType libType) {
        Set<Long> ids = selectDiagramEleList(diagramId, 4);
        ESRltSearchBean bean = new ESRltSearchBean();
        bean.setRltId(ids);
        bean.setLibType(libType);
        return cIRltSwitchSvcIams.exportCiRlt(bean);
    }

    private Set<Long> selectDiagramEleList(Long diagramId, int eleType){
        Long domainId = SysUtil.getCurrentUserInfo().getDomainId();
        CVcDiagramEle eleCdt = new CVcDiagramEle();
        eleCdt.setEleType(eleType);
        eleCdt.setDiagramId(diagramId);
        eleCdt.setDomainId(domainId);
        List<VcDiagramEle> eleList = diagramSvc.queryDiagramEleList(domainId, eleCdt, null);
        if(BinaryUtils.isEmpty(eleList)){
            throw new BinaryException(LanguageResolver.trans("DCV_DATA_IS_EMPTY")); //数据为空
        }
        return eleList.stream().map(VcDiagramEle::getEleId).map(Long::valueOf).collect(Collectors.toSet());
    }

}
