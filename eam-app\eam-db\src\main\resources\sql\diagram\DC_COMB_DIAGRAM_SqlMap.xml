<?xml version="1.0" encoding="UTF-8"?><!--Converted at: Sat Dec 08 16:22:31 CST 2018-->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="DC_COMB_DIAGRAM">


	<resultMap id="queryResult" type="com.uinnova.product.eam.comm.model.DcCombDiagram">
		<result property="id" column="ID" jdbcType="BIGINT"/>	<!-- ID -->
		<result property="combDiagramId" column="COMB_DIAGRAM_ID" jdbcType="BIGINT"/>	<!-- 所属组合视图 -->
		<result property="diagramId" column="DIAGRAM_ID" jdbcType="BIGINT"/>	<!-- 视图ID -->
		<result property="px" column="PX" jdbcType="INTEGER"/>	<!-- 位置X -->
		<result property="py" column="PY" jdbcType="INTEGER"/>	<!-- 位置Y -->
		<result property="direct" column="DIRECT" jdbcType="INTEGER"/>	<!-- 方向 -->
		<result property="domainId" column="DOMAIN_ID" jdbcType="BIGINT"/>	<!-- 所属域 -->
		<result property="dataStatus" column="DATA_STATUS" jdbcType="INTEGER"/>	<!-- 数据状态 -->
		<result property="createTime" column="CREATE_TIME" jdbcType="BIGINT"/>	<!-- 创建时间 -->
		<result property="modifyTime" column="MODIFY_TIME" jdbcType="BIGINT"/>	<!-- 更新时间 -->
	</resultMap>
	

	<sql id="sql_query_where">
		<if test="cdt != null and cdt.id != null">and
			ID = #{cdt.id:BIGINT}
		</if>
		<if test="ids != null and ids != ''">and
			ID in (${ids})
		</if>
		<if test="cdt != null and cdt.startId != null">and
			 ID &gt;= #{cdt.startId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endId != null">and
			 ID &lt;= #{cdt.endId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.combDiagramId != null">and
			COMB_DIAGRAM_ID = #{cdt.combDiagramId:BIGINT}
		</if>
		<if test="combDiagramIds != null and combDiagramIds != ''">and
			COMB_DIAGRAM_ID in (${combDiagramIds})
		</if>
		<if test="cdt != null and cdt.startCombDiagramId != null">and
			 COMB_DIAGRAM_ID &gt;= #{cdt.startCombDiagramId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endCombDiagramId != null">and
			 COMB_DIAGRAM_ID &lt;= #{cdt.endCombDiagramId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.diagramId != null">and
			DIAGRAM_ID = #{cdt.diagramId:BIGINT}
		</if>
		<if test="diagramIds != null and diagramIds != ''">and
			DIAGRAM_ID in (${diagramIds})
		</if>
		<if test="cdt != null and cdt.startDiagramId != null">and
			 DIAGRAM_ID &gt;= #{cdt.startDiagramId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endDiagramId != null">and
			 DIAGRAM_ID &lt;= #{cdt.endDiagramId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.px != null">and
			PX = #{cdt.px:INTEGER}
		</if>
		<if test="pxs != null and pxs != ''">and
			PX in (${pxs})
		</if>
		<if test="cdt != null and cdt.startPx != null">and
			 PX &gt;= #{cdt.startPx:INTEGER} 
		</if>
		<if test="cdt != null and cdt.endPx != null">and
			 PX &lt;= #{cdt.endPx:INTEGER} 
		</if>
		<if test="cdt != null and cdt.py != null">and
			PY = #{cdt.py:INTEGER}
		</if>
		<if test="pys != null and pys != ''">and
			PY in (${pys})
		</if>
		<if test="cdt != null and cdt.startPy != null">and
			 PY &gt;= #{cdt.startPy:INTEGER} 
		</if>
		<if test="cdt != null and cdt.endPy != null">and
			 PY &lt;= #{cdt.endPy:INTEGER} 
		</if>
		<if test="cdt != null and cdt.direct != null">and
			DIRECT = #{cdt.direct:INTEGER}
		</if>
		<if test="directs != null and directs != ''">and
			DIRECT in (${directs})
		</if>
		<if test="cdt != null and cdt.startDirect != null">and
			 DIRECT &gt;= #{cdt.startDirect:INTEGER} 
		</if>
		<if test="cdt != null and cdt.endDirect != null">and
			 DIRECT &lt;= #{cdt.endDirect:INTEGER} 
		</if>
		<if test="cdt != null and cdt.domainId != null">and
			DOMAIN_ID = #{cdt.domainId:BIGINT}
		</if>
		<if test="domainIds != null and domainIds != ''">and
			DOMAIN_ID in (${domainIds})
		</if>
		<if test="cdt != null and cdt.startDomainId != null">and
			 DOMAIN_ID &gt;= #{cdt.startDomainId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endDomainId != null">and
			 DOMAIN_ID &lt;= #{cdt.endDomainId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.dataStatus != null">and
			DATA_STATUS = #{cdt.dataStatus:INTEGER}
		</if>
		<if test="dataStatuss != null and dataStatuss != ''">and
			DATA_STATUS in (${dataStatuss})
		</if>
		<if test="cdt != null and cdt.startDataStatus != null">and
			 DATA_STATUS &gt;= #{cdt.startDataStatus:INTEGER} 
		</if>
		<if test="cdt != null and cdt.endDataStatus != null">and
			 DATA_STATUS &lt;= #{cdt.endDataStatus:INTEGER} 
		</if>
		<if test="cdt != null and cdt.createTime != null">and
			CREATE_TIME = #{cdt.createTime:BIGINT}
		</if>
		<if test="createTimes != null and createTimes != ''">and
			CREATE_TIME in (${createTimes})
		</if>
		<if test="cdt != null and cdt.startCreateTime != null">and
			 CREATE_TIME &gt;= #{cdt.startCreateTime:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endCreateTime != null">and
			 CREATE_TIME &lt;= #{cdt.endCreateTime:BIGINT} 
		</if>
		<if test="cdt != null and cdt.modifyTime != null">and
			MODIFY_TIME = #{cdt.modifyTime:BIGINT}
		</if>
		<if test="modifyTimes != null and modifyTimes != ''">and
			MODIFY_TIME in (${modifyTimes})
		</if>
		<if test="cdt != null and cdt.startModifyTime != null">and
			 MODIFY_TIME &gt;= #{cdt.startModifyTime:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endModifyTime != null">and
			 MODIFY_TIME &lt;= #{cdt.endModifyTime:BIGINT} 
		</if>
	</sql>
	

	<sql id="sql_update_columns">
		<if test="record != null and record.id != null"> 
			ID = #{record.id:BIGINT}
		,</if>
		<if test="record != null and record.combDiagramId != null"> 
			COMB_DIAGRAM_ID = #{record.combDiagramId:BIGINT}
		,</if>
		<if test="record != null and record.diagramId != null"> 
			DIAGRAM_ID = #{record.diagramId:BIGINT}
		,</if>
		<if test="record != null and record.px != null"> 
			PX = #{record.px:INTEGER}
		,</if>
		<if test="record != null and record.py != null"> 
			PY = #{record.py:INTEGER}
		,</if>
		<if test="record != null and record.direct != null"> 
			DIRECT = #{record.direct:INTEGER}
		,</if>
		<if test="record != null and record.domainId != null"> 
			DOMAIN_ID = #{record.domainId:BIGINT}
		,</if>
		<if test="record != null and record.dataStatus != null"> 
			DATA_STATUS = #{record.dataStatus:INTEGER}
		,</if>
		<if test="record != null and record.createTime != null"> 
			CREATE_TIME = #{record.createTime:BIGINT}
		,</if>
		<if test="record != null and record.modifyTime != null"> 
			MODIFY_TIME = #{record.modifyTime:BIGINT}
		,</if>
	</sql>
	

	<sql id="sql_query_columns">
		ID, COMB_DIAGRAM_ID, DIAGRAM_ID, PX, PY, DIRECT, 
		DOMAIN_ID, DATA_STATUS, CREATE_TIME, MODIFY_TIME
	</sql>
	

	

	<select id="selectList" parameterType="java.util.Map" resultMap="queryResult">
		select 
			<include refid="DC_COMB_DIAGRAM.sql_query_columns"/>
		from DC_COMB_DIAGRAM 
			<where>
				<include refid="DC_COMB_DIAGRAM.sql_query_where"/>
			</where>
		order by 
			<if test="orders != null and orders != ''">
				${orders}
			</if>
			<if test="orders == null or orders == ''">
				ID
			</if>
	</select>
	<select id="selectCount" parameterType="java.util.Map" resultType="java.lang.Long">
		select count(1) from DC_COMB_DIAGRAM 
			<where>
				<include refid="DC_COMB_DIAGRAM.sql_query_where"/>
			</where>
	</select>
	<select id="selectById" parameterType="java.util.Map" resultMap="queryResult">
		select 
			<include refid="DC_COMB_DIAGRAM.sql_query_columns"/>
		from DC_COMB_DIAGRAM where ID=#{id:BIGINT} and DATA_STATUS=1  
	</select>
	

	

	<insert id="insert" parameterType="java.util.Map">
		insert into DC_COMB_DIAGRAM(
			ID, COMB_DIAGRAM_ID, DIAGRAM_ID, PX, PY, 
			DIRECT, DOMAIN_ID, DATA_STATUS, CREATE_TIME, MODIFY_TIME)
		values (
			#{record.id:BIGINT}, #{record.combDiagramId:BIGINT}, #{record.diagramId:BIGINT}, #{record.px:INTEGER}, #{record.py:INTEGER}, 
			#{record.direct:INTEGER}, #{record.domainId:BIGINT}, #{record.dataStatus:INTEGER}, #{record.createTime:BIGINT}, #{record.modifyTime:BIGINT})
	</insert>
	

	

	<update id="updateById" parameterType="java.util.Map">
		update DC_COMB_DIAGRAM
			<set> 
				<include refid="DC_COMB_DIAGRAM.sql_update_columns"/> 
			</set>
		where ID = #{id:BIGINT}
	</update>
	<update id="updateByCdt" parameterType="java.util.Map">
		update DC_COMB_DIAGRAM
			<set> 
				<include refid="DC_COMB_DIAGRAM.sql_update_columns"/> 
			</set>
			<where> 
				<include refid="DC_COMB_DIAGRAM.sql_query_where"/> 
			</where>
	</update>
	
	

	

	<delete id="deleteById" parameterType="java.util.Map">
		delete from DC_COMB_DIAGRAM where ID = #{id:BIGINT}
	</delete>
	<delete id="deleteByCdt" parameterType="java.util.Map">
		delete from DC_COMB_DIAGRAM
			<where> 
				<include refid="DC_COMB_DIAGRAM.sql_query_where"/> 
			</where>
	</delete>
	



</mapper>