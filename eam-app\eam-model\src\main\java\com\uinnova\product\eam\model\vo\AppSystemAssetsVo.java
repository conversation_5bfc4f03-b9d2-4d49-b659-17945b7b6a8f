package com.uinnova.product.eam.model.vo;

import com.binary.framework.bean.annotation.Comment;
import com.uino.bean.cmdb.base.ESCIAttrDefInfo;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class AppSystemAssetsVo {

    @Comment("当前资产code")
    private String code;

    @Comment("父级资产code")
    private String parentCode;

    @Comment("资产名称")
    private String name;

    @Comment("资产详细信息")
    private Object value;

    @Comment("属性定义")
    private List<ESCIAttrDefInfo> attrDefs;
}
