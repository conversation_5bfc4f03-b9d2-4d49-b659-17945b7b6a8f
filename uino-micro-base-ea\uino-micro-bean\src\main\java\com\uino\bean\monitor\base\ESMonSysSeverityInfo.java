package com.uino.bean.monitor.base;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.vmdb.comm.model.monitor.MonSysSeverity;
import com.uino.bean.sys.enums.DictionaryOptionEnum;

import lombok.Getter;
import lombok.Setter;

/**
 * 告警级别
 * 
 * <AUTHOR>
 *
 */
@Getter
@Setter
public class ESMonSysSeverityInfo extends MonSysSeverity {

	private static final long serialVersionUID = 1L;

    @Comment("操作类型：READ:只读，WRITE:读写")
    private DictionaryOptionEnum option;

    @Comment("所属域[DOMAIN_ID]")
    private Long domainId;

    @Comment("创建人[CREATOR]")
    private String creator;

    @Comment("修改人[MODIFIER]")
    private String modifier;

}
