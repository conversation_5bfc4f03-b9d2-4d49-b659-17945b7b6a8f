package com.binary.sso.client.web;

import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import com.binary.core.util.BinaryUtils;
import com.binary.framework.Local;
import com.binary.framework.bean.User;
import com.binary.framework.critical.support.HttpOauthCriticalObject;
import com.binary.framework.spring.BinarySpringServlet;
import com.binary.framework.util.AjaxResultListener;
import com.binary.framework.util.ControllerUtils;
import com.binary.framework.web.SessionKey;
import com.binary.sso.comm.bean.SsoOauthUser;

public class SsoOauthServlet extends BinarySpringServlet implements AjaxResultListener {
	private static final long serialVersionUID = 1L;

	protected static final String SERVER_STATUS="ServerStatus";

	/** 相对失效复活时差, 单位:分钟 **/
	private int resurgeTimeDeff = 5;

	/** 复活状态 **/
	private int resurgeCode = 210;



	public SsoOauthServlet() {
		ControllerUtils.setAjaxResultListener(this);
	}



	@Override
	protected void initFrameworkServlet() throws ServletException {
		String deff = getInitParameter("resurgeTimeDeff");
		String code = getInitParameter("resurgeCode");

		if(!BinaryUtils.isEmpty(deff)) {
			int d = Integer.parseInt(deff);
			if(d > 0) this.resurgeTimeDeff = d;
		}
		if(!BinaryUtils.isEmpty(code)) {
			int d = Integer.parseInt(code);
			if(d > 0) this.resurgeCode = d;
		}
		super.initFrameworkServlet();
	}



	/**
	 * 开启环境
	 */
	@Override
	protected void open(HttpServletRequest request, HttpServletResponse response) {
//		User user = (User)request.getAttribute(SessionKey.SYSTEM_USER);
//		Local.open(user);
		Local.open(new HttpOauthCriticalObject(request, response));
	}




	@Override
	public boolean beforeWrite(HttpServletRequest request, HttpServletResponse response, Object result) {
		User user = (User)request.getAttribute(SessionKey.SYSTEM_USER);
		if(user!=null && (user instanceof SsoOauthUser)) {
			String contextpath = request.getContextPath();
	        String url = request.getRequestURI();
	        String path = url.substring(contextpath.length());
	        if(path.contains("/sso/client/oauth/refreshToken")) {
	        	return true;
	        }

			SsoOauthUser su = (SsoOauthUser)user;
			Long createTime = su.getTokenCreateTime();
			Integer tokenValidTime = su.getTokenValidTime();

			//如果token需要复活, 则通过状态码告知前端
			if(createTime!=null && tokenValidTime!=null) {
				long currentTime = System.currentTimeMillis();
				long startTime = createTime.longValue() + (tokenValidTime.intValue()-this.resurgeTimeDeff)*60*1000;
				long endTime = createTime.longValue() + tokenValidTime.intValue()*60*1000;

				if(currentTime>=startTime && currentTime<=endTime) {
		        	response.setHeader(SsoOauthServlet.SERVER_STATUS,
		        			String.valueOf(this.resurgeCode));
					//response.setStatus(this.resurgeCode);
				}
			}
		}
		return true;
	}




	@Override
	public void afterWrite(HttpServletRequest request, HttpServletResponse response, Object result) {
	}






}
