package com.uinnova.product.eam.comm.model;




import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;


@Comment("分类附加属性表[VC_CLASS_ADD_ATTR]")
public class VcClassAddAttr implements EntityBean {
	private static final long serialVersionUID = 1L;


	@Comment("ID[ID]")
	private Long id;


	@Comment("分类ID[CLASS_ID]")
	private Long classId;


	@Comment("分类类型[CLASS_TYPE]")
	private Integer classType;


	@Comment("附加属性1[ADD_ATTR_1]")
	private String addAttr1;


	@Comment("附加属性2[ADD_ATTR_2]")
	private String addAttr2;


	@Comment("附加属性3[ADD_ATTR_3]")
	private String addAttr3;


	@Comment("附加属性4[ADD_ATTR_4]    附加属性3")
	private String addAttr4;


	@Comment("附加属性5[ADD_ATTR_5]    附加属性3")
	private String addAttr5;


	@Comment("附加属性6[ADD_ATTR_6]    附加属性3")
	private String addAttr6;


	@Comment("所属域[DOMAIN_ID]")
	private Long domainId;


	@Comment("数据状态[DATA_STATUS]    数据状态:0=删除，1=正常")
	private Integer dataStatus;


	@Comment("创建人[CREATOR]")
	private String creator;


	@Comment("修改人[MODIFIER]")
	private String modifier;


	@Comment("创建时间[CREATE_TIME]    创建时间:yyyyMMddHHmmss")
	private Long createTime;


	@Comment("更新时间[MODIFY_TIME]    更新时间:yyyyMMddHHmmss")
	private Long modifyTime;




	public Long getId() {
		return this.id;
	}
	public void setId(Long id) {
		this.id = id;
	}


	public Long getClassId() {
		return this.classId;
	}
	public void setClassId(Long classId) {
		this.classId = classId;
	}


	public Integer getClassType() {
		return this.classType;
	}
	public void setClassType(Integer classType) {
		this.classType = classType;
	}


	public String getAddAttr1() {
		return this.addAttr1;
	}
	public void setAddAttr1(String addAttr1) {
		this.addAttr1 = addAttr1;
	}


	public String getAddAttr2() {
		return this.addAttr2;
	}
	public void setAddAttr2(String addAttr2) {
		this.addAttr2 = addAttr2;
	}


	public String getAddAttr3() {
		return this.addAttr3;
	}
	public void setAddAttr3(String addAttr3) {
		this.addAttr3 = addAttr3;
	}


	public String getAddAttr4() {
		return this.addAttr4;
	}
	public void setAddAttr4(String addAttr4) {
		this.addAttr4 = addAttr4;
	}


	public String getAddAttr5() {
		return this.addAttr5;
	}
	public void setAddAttr5(String addAttr5) {
		this.addAttr5 = addAttr5;
	}


	public String getAddAttr6() {
		return this.addAttr6;
	}
	public void setAddAttr6(String addAttr6) {
		this.addAttr6 = addAttr6;
	}


	public Long getDomainId() {
		return this.domainId;
	}
	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}


	public Integer getDataStatus() {
		return this.dataStatus;
	}
	public void setDataStatus(Integer dataStatus) {
		this.dataStatus = dataStatus;
	}


	public String getCreator() {
		return this.creator;
	}
	public void setCreator(String creator) {
		this.creator = creator;
	}


	public String getModifier() {
		return this.modifier;
	}
	public void setModifier(String modifier) {
		this.modifier = modifier;
	}


	public Long getCreateTime() {
		return this.createTime;
	}
	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}


	public Long getModifyTime() {
		return this.modifyTime;
	}
	public void setModifyTime(Long modifyTime) {
		this.modifyTime = modifyTime;
	}


}


