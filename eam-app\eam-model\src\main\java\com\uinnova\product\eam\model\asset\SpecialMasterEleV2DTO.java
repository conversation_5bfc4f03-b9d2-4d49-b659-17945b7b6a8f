package com.uinnova.product.eam.model.asset;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.base.model.AttrDefV2Info;
import lombok.Data;

import java.io.Serializable;

@Data
public class SpecialMasterEleV2DTO implements Serializable {

    @Comment("标准规范主信息元素定义")
    private AttrDefV2Info specialEle;

    @Comment("标准规范版本信息元素定义")
    private AttrDefV2Info editionEle;
}
