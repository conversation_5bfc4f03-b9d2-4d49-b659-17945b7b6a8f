package com.uinnova.product.vmdb.provider.quality.bean;

/**
 * 孤儿规则检查分类定义
 */
public class OrphanCheckClassDefine {

    private static final long serialVersionUID = 1L;

    private String name;

    /**
     * 是否检查孤儿规则
     */
    private Integer check;

    private UncheckDefine uncheck;

    public static class UncheckDefine {
        String proName;
        String proValue;

        public String getProName() {
            return proName;
        }

        public void setProName(String proName) {
            this.proName = proName;
        }

        public String getProValue() {
            return proValue;
        }

        public void setProValue(String proValue) {
            this.proValue = proValue;
        }
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getCheck() {
        return check;
    }

    public void setCheck(Integer check) {
        this.check = check;
    }

    public UncheckDefine getUncheck() {
        return uncheck;
    }

    public void setUncheck(UncheckDefine uncheck) {
        this.uncheck = uncheck;
    }
}
