package com.uinnova.product.eam.db.diagram.es;

import com.uinnova.product.eam.base.diagram.model.ESUploadManage;
import com.uinnova.product.eam.base.diagram.model.ESUploadManageQuery;
import com.uino.dao.AbstractESBaseDao;
import com.uino.util.sys.CommonFileUtil;
import org.springframework.stereotype.Repository;

import jakarta.annotation.PostConstruct;
import java.util.List;

/**
 * @Classname 上传(图片)管理访问层
 * @Description 该类用来操作es中的上传(图片)管理index
 * <AUTHOR>
 * @Date 2021-10-21-13:57
 */
@Repository
public class ESUploadManageDao extends AbstractESBaseDao<ESUploadManage, ESUploadManageQuery> {
    @Override
    public String getIndex() {
        return "uino_eam_upload_manage";
    }

    @Override
    public String getType() {
        return "_doc";
    }

    @PostConstruct
    public void init() {
        List<ESUploadManage> list = CommonFileUtil.getData("/initdata/uino_eam_upload_manage.json", ESUploadManage.class);
        super.initIndex(list);
    }


}
