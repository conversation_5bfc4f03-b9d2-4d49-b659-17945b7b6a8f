package com.uino.api.client.sys.rpc;

import java.util.Collection;
import java.util.List;

import com.uino.dao.BaseConst;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.binary.core.io.Resource;
import com.binary.jdbc.Page;
import com.uino.bean.cmdb.business.ImportResultMessage;
import com.uino.bean.cmdb.business.ImportSheetMessage;
import com.uino.bean.sys.base.ESDictionaryClassInfo;
import com.uino.bean.sys.base.ESDictionaryItemInfo;
import com.uino.bean.sys.business.DictionaryInfoDto;
import com.uino.bean.sys.query.ESDictionaryItemSearchBean;
import com.uino.bean.sys.query.ExportDictionaryDto;
import com.uino.provider.feign.sys.DictionaryFeign;
import com.uino.api.client.sys.IDictionaryApiSvc;

@Service
public class DictionaryApiSvcRpc implements IDictionaryApiSvc {

    @Autowired
    private DictionaryFeign dictFeign;
    @Override
    public Long saveDictionaryClassInfo(ESDictionaryClassInfo dictClassInfo) {
        return dictFeign.saveDictionaryClassInfo(dictClassInfo);
    }

    @Override
    public List<DictionaryInfoDto> getDictionaryClassList() {
        return dictFeign.getDictionaryClassList(BaseConst.DEFAULT_DOMAIN_ID);
    }

    @Override
    public List<DictionaryInfoDto> getDictionaryClassList(Long domainId) {
        return dictFeign.getDictionaryClassList(domainId);
    }

    @Override
    public ESDictionaryClassInfo getDictClassInfoById(Long dictClassId) {
        return dictFeign.getDictClassInfoById(dictClassId);
    }

    @Override
    public Integer deleteDictClassInfoById(Long dictClassId) {
        return dictFeign.deleteDictClassInfoById(dictClassId);
    }

    @Override
	public ESDictionaryItemInfo getDictItemInfoById(Long id) {
		return dictFeign.getDictItemInfoById(id);
	}

	@Override
    public Page<ESDictionaryItemInfo> searchDictItemPageByBean(ESDictionaryItemSearchBean bean) {
        return dictFeign.searchDictItemPageByBean(bean);
    }

    @Override
    public List<ESDictionaryItemInfo> searchDictItemListByBean(ESDictionaryItemSearchBean bean) {
        return dictFeign.searchDictItemListByBean(bean);
    }

    @Override
    public Long saveOrUpdateDictionaryItem(ESDictionaryItemInfo item) {
        return dictFeign.saveOrUpdateDictionaryItem(item);
    }

    @Override
    public ImportSheetMessage saveDictionaryItemsBatch(Long dictClassId, List<ESDictionaryItemInfo> items) {
        return dictFeign.saveDictionaryItemsBatch(dictClassId, items);
    }

    @Override
    public Integer deleteItemByIds(Collection<Long> ids) {
        return dictFeign.deleteItemByIds(ids);
    }

    @Override
    public Resource exportDictionaryItems(ExportDictionaryDto dto) {
        return dictFeign.exportDictionaryItems(dto);
    }

    @Override
    public ImportResultMessage importDictionaryItems(Long dictClassId, MultipartFile file) {
        return dictFeign.importDictionaryItems(dictClassId, file);
    }

    @Override
    public List<String> getExteralDictValues(ESDictionaryItemSearchBean bean) {
        return dictFeign.getExteralDictValues(bean);
    }

    @Override
    public Page<ESDictionaryItemInfo> searchDictItemPageByIds(ESDictionaryItemSearchBean bean) {
        return null;
    }

    @Override
    public String checkAttrUsedMethod(Long id) {
        return null;
    }

    @Override
    public List<String> getExteralDictValues(Long domainId, Long dictClassId, Long[] dictDefIds) {
        return null;
    }
}
