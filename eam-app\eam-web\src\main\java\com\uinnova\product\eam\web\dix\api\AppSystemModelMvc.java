package com.uinnova.product.eam.web.dix.api;

import com.binary.framework.web.RemoteResult;
import com.uinnova.product.eam.service.dix.api.AppSystemModelSvc;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 应用系统对接DDM平台接口
 *
 * <AUTHOR>
 * @date 2023/3/1
 */
@Slf4j
@RestController
@RequestMapping("/api/dataModel")
public class AppSystemModelMvc {
    @Autowired
    private AppSystemModelSvc appSystemModelSvc;

    @GetMapping("/getAllSystemCategory")
    @ModDesc(desc = "全量系统目录", pDesc = "", rDesc = "系统目录", rType = RemoteResult.class)
    public RemoteResult getAllSystemCategory() {
        return new RemoteResult(appSystemModelSvc.getAllSystemCategory());
    }

    @GetMapping("/getModel/{systemId}")
    @ModDesc(desc = "根据系统id获取最新实体信息", pDesc = "系统id", rDesc = "更新状态", rType = RemoteResult.class)
    public RemoteResult getModel(@PathVariable String systemId) {
        return new RemoteResult(appSystemModelSvc.getModel(systemId));
    }
}
