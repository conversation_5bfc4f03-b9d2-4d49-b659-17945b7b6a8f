package com.uino.provider.feign.cmdb;

import java.util.List;
import java.util.Map;

import com.uino.bean.cmdb.base.LibType;
import com.uino.bean.cmdb.base.SaveBatchCIContext;
import com.uino.bean.cmdb.business.*;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.search.sort.SortBuilder;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.comm.model.ci.CCcCi;
import com.uinnova.product.vmdb.comm.model.ci.CcCi;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CiClassSaveInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CiGroupPage;
import com.uinnova.product.vmdb.provider.ci.bean.CiQueryCdt;
import com.uinnova.product.vmdb.provider.search.bean.CcCiSearchPage;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.query.ESAttrAggBean;
import com.uino.bean.cmdb.query.ESCISearchBean;
import com.uino.provider.feign.config.BaseFeignConfig;

/**
 * 
 * <AUTHOR>
 *
 */
@Component
@FeignClient(name = "${feign-server-name:tarsier-platform}", path = "${feign-server-path:tarsier-platform}/feign/ci", configuration = {
		BaseFeignConfig.class })
public interface CIFeign {
	/**
	 * 根据id查询CI信息
	 * 
	 * @param id
	 * @return
	 */
	@PostMapping("getCiInfoById")
	CcCiInfo getCiInfoById(@RequestBody Long id);

    /**
     * 按条件分页查询CI
     * 
     * @param pageNum
     * @param pageSize
     * @return
     */
    @PostMapping("queryPageByIndexWithoutBody")
    CiGroupPage queryPageByIndexWithoutBody(@RequestParam(value = "domainId") Long domainId, @RequestParam(value = "pageNum") Integer pageNum, @RequestParam(value = "pageSize") Integer pageSize,
        @RequestParam(value = "hasClass", required = false) Boolean hasClass);

	/**
	 * 按条件分页查询CI
	 * 
	 * @param pageNum
	 * @param pageSize
	 * @param cdt
	 * @return
	 */
	@PostMapping("queryPageByIndex")
	CiGroupPage queryPageByIndex(@RequestParam(value = "domainId") Long domainId, @RequestParam(value = "pageNum") Integer pageNum,
        @RequestParam(value = "pageSize") Integer pageSize, @RequestBody CiQueryCdt cdt, @RequestParam(value = "hasClass", required = false) Boolean hasClass);

    /**
     * 按条件分页查询CI-支持属性排序
     * 
     * @param bean
     * @param hasClass
     * @return
     */
    @PostMapping("queryPageBySearchBean")
    CiGroupPage queryPageBySearchBean(@RequestBody ESCISearchBean bean, @RequestParam(value = "hasClass", required = false) Boolean hasClass);

    /**
     * 条件查询CI
     * 
     * @param domainId
     * @param orders
     * @param isAsc
     * @return
     */
    @PostMapping("queryCiListWithoutBody")
    List<CcCi> queryCiListWithoutBody(@RequestParam(value = "domainId", required = false) Long domainId, @RequestParam(value = "orders", required = false) String orders,
        @RequestParam(value = "isAsc", required = false) Boolean isAsc);

    /**
     * 条件查询CI
     * 
     * @param domainId
     * @param cdt
     * @param orders
     * @param isAsc
     * @return
     */
    @PostMapping("queryCiList")
    List<CcCi> queryCiList(@RequestParam(value = "domainId", required = false) Long domainId, @RequestBody CCcCi cdt, @RequestParam(value = "orders", required = false) String orders,
        @RequestParam(value = "isAsc", required = false) Boolean isAsc);

    /**
     * 条件查询CI
     * 
     * @param domainId
     * @param cdt
     * @param orders 排序字段
     * @param isAsc 是否升序 * @param hasClass 是否查询CiClass信息
     * @return
     */
    @PostMapping("queryESCIInfoListWithoutBody")
    List<ESCIInfo> queryESCIInfoListWithoutBody(@RequestParam(value = "domainId", required = false) Long domainId, @RequestParam(value = "orders", required = false) String orders,
        @RequestParam(value = "isAsc", required = false) Boolean isAsc);

    /**
     * 条件查询CI
     * 
     * @param domainId
     * @param cdt
     * @param orders 排序字段
     * @param isAsc 是否升序 * @param hasClass 是否查询CiClass信息
     * @return
     */
    @PostMapping("queryESCIInfoList")
    List<ESCIInfo> queryESCIInfoList(@RequestParam(value = "domainId", required = false) Long domainId, @RequestBody CCcCi cdt, @RequestParam(value = "orders", required = false) String orders,
        @RequestParam(value = "isAsc", required = false) Boolean isAsc);

    /**
     * 条件查询CI
     * 
     * @param domainId
     * @param orders
     * @param isAsc
     * @param hasClass
     * @return
     */
    @PostMapping("queryCiInfoListWithoutBody")
    List<CcCiInfo> queryCiInfoListWithoutBody(@RequestParam(value = "domainId", required = false) Long domainId, @RequestParam(value = "orders", required = false) String orders,
        @RequestParam(value = "isAsc", required = false) Boolean isAsc, @RequestParam(value = "hasClass", required = false) Boolean hasClass);

    /**
     * 条件查询CI
     * 
     * @param domainId
     * @param cdt
     * @param orders
     * @param isAsc
     * @param hasClass
     * @return
     */
    @PostMapping("queryCiInfoList")
    List<CcCiInfo> queryCiInfoList(@RequestParam(value = "domainId", required = false) Long domainId, @RequestBody CCcCi cdt, @RequestParam(value = "orders", required = false) String orders,
        @RequestParam(value = "isAsc", required = false) Boolean isAsc, @RequestParam(value = "hasClass", required = false) Boolean hasClass);

    /**
     * 分页查询CI
     * 
     * @param domainId
     * @param pageNum
     * @param pageSize
     * @param orders
     * @param isAsc
     * @param hasClass
     * @return
     */
    @PostMapping("queryCiInfoPageWithoutBody")
    Page<CcCiInfo> queryCiInfoPageWithoutBody(@RequestParam(value = "domainId", required = false) Long domainId, @RequestParam(value = "pageNum") Integer pageNum,
        @RequestParam(value = "pageSize") Integer pageSize, @RequestParam(value = "orders", required = false) String orders, @RequestParam(value = "isAsc", required = false) Boolean isAsc,
        @RequestParam(value = "hasClass", required = false) Boolean hasClass);

    /**
     * 分页查询CI
     * 
     * @param domainId
     * @param pageNum
     * @param pageSize
     * @param cdt
     * @param orders
     * @param isAsc
     * @param hasClass
     * @return
     */
    @PostMapping("queryCiInfoPage")
    Page<CcCiInfo> queryCiInfoPage(@RequestParam(value = "domainId", required = false) Long domainId, @RequestParam(value = "pageNum") Integer pageNum,
        @RequestParam(value = "pageSize") Integer pageSize, @RequestBody CCcCi cdt, @RequestParam(value = "orders", required = false) String orders,
        @RequestParam(value = "isAsc", required = false) Boolean isAsc,
        @RequestParam(value = "hasClass", required = false) Boolean hasClass);

    /**
     * <b>根据cdt对象查询分类
     * 
     * @param pageNum
     * @param pageSize
     * @return
     */
    @PostMapping("searchCIByCdtWithoutBody")
    public CcCiSearchPage searchCIByCdtWithoutBody(@RequestParam(value = "pageNum") int pageNum, @RequestParam(value = "pageSize") int pageSize);

    /**
     * <b>根据cdt对象查询分类
     * 
     * @param pageNum
     * @param pageSize
     * @param bean 自动生成的分类查询对象
     * @return
     */
    @PostMapping("searchCIByCdt")
    public CcCiSearchPage searchCIByCdt(@RequestParam(value = "pageNum") int pageNum, @RequestParam(value = "pageSize") int pageSize, @RequestBody CCcCi bean);

    /**
     * es分页查询ci接口支持<br/>
     * 1、模糊匹配<br/>
     * 2、根据指定属性查询(and /or )<br/>
     * 3、ciCode/ciId/hashcode筛选<br/>
     * 4、classIds筛选<br/>
     * 5、tagIds筛选<br/>
     * 6、默认根据匹配度排序<br/>
     * 7、排序字段 如果是字符串类型必须加keyword
     * 
     * @param bean 查询BEAN，定义了各个字段之间的关系
     * @return
     */
    @PostMapping("searchESCIByBean")
    public Page<ESCIInfo> searchESCIByBean(@RequestBody ESCISearchBean bean);

    /**
     * es分页查询ci接口支持<br/>
     * 1、模糊匹配<br/>
     * 2、根据指定属性查询(and /or )<br/>
     * 3、ciCode/ciId/hashcode筛选<br/>
     * 4、classIds筛选<br/>
     * 5、tagIds筛选<br/>
     * 6、默认根据匹配度排序<br/>
     * 7、排序字段 如果是字符串类型必须加keyword
     * 
     * @param bean 查询BEAN，定义了各个字段之间的关系
     * @return
     */
    @PostMapping("searchCIByBean")
    public CcCiSearchPage searchCIByBean(@RequestBody ESCISearchBean bean);

	/**
	 * 保存或更新CI
	 * 
	 * @param ciInfo
	 * @return
	 */
	@PostMapping("saveOrUpdateCI")
	Long saveOrUpdateCI(@RequestBody CcCiInfo ciInfo);

    /**
     * 批量保存CI信息并返回保存明细
     * 
     * @param domainId 数据域
     * @param saveInfo 保存对象
     * @return 保存明细
     */
    @PostMapping("saveOrUpdateCiBatch")
    ImportSheetMessage saveOrUpdateCiBatch(@RequestParam(value = "domainId", required = false) Long domainId, @RequestBody CiClassSaveInfo saveInfo);

    /**
     * dcv特有，不允许别人调用，否则可能有严重问题
     * 
     * @param esCiInfoList
     * @return
     */
    @PostMapping("updateESCIInfoBatch")
    Integer updateESCIInfoBatch(@RequestBody List<ESCIInfo> esCiInfoList);

	/**
     * 根据id删除CI
     * 
     * @param id
     * @param sourceId 来源id,页面=1;CP=2;DIX=3
     * @return
     */
	@PostMapping("removeById")
    Integer removeById(@RequestBody Long id, @RequestParam(value = "sourceId", required = false) Long sourceId);

	/**
     * 根据ids批量删除CI
     * 
     * @param ciIds
     * @param sourceId 来源id,页面=1;CP=2;DIX=3
     * @return
     */
	@PostMapping("removeByIds")
    Integer removeByIds(@RequestBody List<Long> ciIds, @RequestParam(value = "sourceId", required = false) Long sourceId);

    @PostMapping("removeByPrimaryKeys")
    Integer removeByPrimaryKeys(@RequestParam(value = "domainId") Long domainId, @RequestBody List<String> ciPrimaryKeys,@RequestParam(value = "sourceId", required = false) Long sourceId);
	/**
     * 根据分类id删除CI
     * 
     * @param classId
     * @param sourceId 来源id,页面=1;CP=2;DIX=3
     * @return
     */
	@PostMapping("removeByClassId")
    Integer removeByClassId(@RequestBody Long classId, @RequestParam(value = "sourceId", required = false) Long sourceId);

	/**
	 * 根据条件导出CI或分类属性
	 * 
	 * @param exportDto
	 * @return
	 */
	@PostMapping(value = "exportCiOrClass")
	ResponseEntity<byte[]> exportCiOrClass(@RequestBody ExportCiDto exportDto);

	/**
	 * 根据分类id导入CI，classIds为空默认导入所有
	 * 
	 * @param file
	 * @param classId
	 * @return
	 */
	@PostMapping(value = "importCiByCiClsIds", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    ImportResultMessage importCiByCiClsIds(@RequestPart(value = "file") MultipartFile file,
        @RequestParam(value = "classId", required = false) Long classId);

    /**
     * 一键导入上传Excel文件
     * 
     * @param file
     * @return
     */
    @PostMapping(value = "importCiExcel", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    ImportExcelMessage importCiExcel(@RequestPart(value = "file") MultipartFile file);

    /**
     * 一键导入根据分类名称批量导入CI
     * 
     * @param excelInfoDto
     * @return
     */
    @PostMapping("importCiByClassBatch")
    ImportResultMessage importCiByClassBatch(@RequestParam(value = "domainId") Long domainId, @RequestBody CiExcelInfoDto excelInfoDto);

    @PostMapping("importCiByClassBatchForAddAttr")
    ImportResultMessage importCiByClassBatchForAddAttr(@RequestParam(value = "domainId") Long domainId, @RequestBody CiExcelInfoDto excelInfoDto, @RequestParam(value = "addAttr", required = false) boolean addAttr);

    /**
     * 统计ci数量汇总至分类，支持对ci条件筛选
     * 
     * @param bean
     * @return
     */
    @PostMapping("countCiNumGroupClsByQuery")
    Map<Long, Long> countCiNumGroupClsByQuery(@RequestBody ESCISearchBean bean);
    
    /**
     * 根据条件count
     * 
     * @param bean
     * @return
     */
    @PostMapping("countByQuery")
    public Long countByQuery(@RequestBody ESCISearchBean bean);

    /**
     * 获取属性值列表
     * 
     * @param searchBean
     * @return
     */
    @PostMapping("getAttrValuesBySearchBean")
    public Page<String> getAttrValuesBySearchBean(@RequestBody ESAttrAggBean searchBean);

    /**
     * 根据业务主键查询CI
     * 
     * @param ciPrimaryKeys
     * @return
     */
    @PostMapping("getCIInfoListByCIPrimaryKeys")
    public List<CcCiInfo> getCIInfoListByCIPrimaryKeys(@RequestParam(value = "domainId") Long domainId, @RequestBody List<List<String>> ciPrimaryKeys);

    /**
     * 根据业务主键查询CI
     * 
     * @param ciPrimaryKeys
     * @return
     */
    @PostMapping("getESCIInfoListByCIPrimaryKeys")
    public List<ESCIInfo> getESCIInfoListByCIPrimaryKeys(@RequestParam(value = "domainId") Long domainId, @RequestBody List<List<String>> ciPrimaryKeys);

    /**
     * 条件查询CI信息-提供原生query查询接口,根据属性查询时需进行属性转换(属性实际存储名称与显示名称不同)
     * <b>结合classSvc.getTargetAttrDefsByClassIds(classIds)方法转换属性后使用，排序字段也许转换后排序
     * 
     * @param pageNum
     * @param pageSize
     * @param query
     * @param sorts
     * @return
     */
    @PostMapping("getESCIInfoPageByQuery")
    public Page<ESCIInfo> getESCIInfoPageByQuery(@RequestParam(value = "domainId") Long domainId, @RequestParam("pageNum") int pageNum, @RequestParam("pageSize") int pageSize, @RequestBody QueryBuilder query,
        @RequestParam("sorts") List<SortBuilder<?>> sorts, @RequestParam("isHighLight") Boolean isHighLight);

	/**
	 * 批量修改对象属性
	 * 
	 * @param dto
	 * @return
	 */
	@PostMapping("updateAttrValueBatch")
	public boolean updateAttrValueBatch(@RequestBody CIAttrValueUpdateDto dto);


    /**
     * 根据分类id查找 对象数据条数
     *
     * @param
     * @return
     */
    @PostMapping("queryCiCountByClassId")
    Map<String, Long> queryCiCountByClassId();

    Map<String, ? extends SaveBatchCIContext> copyCiListByIds(List<ESCIInfo> ciList, String ownerCode);


    /**
     * 删除所有的的CI
     * @param query
     * @return
     */
    @PostMapping("removeAllCI")
    Integer removeAllCI(@RequestParam(value = "domainId") Long domainId, QueryBuilder query);


    /**
     * 批量修改对象属性,选择本页数据或全部数据
     * @param dto
     * @return
     */
    @PostMapping("modifyAttrValueBatch")
    public boolean modifyAttrValueBatch(@RequestBody CIAttrValueUpdateDto dto);


    /**
     * 批量删除对象,选择本页或全部数据
     * @param dto
     * @return
     */
    @PostMapping("removeCiBatch")
    public Integer removeCiBatch(@RequestBody CIRemoveBatchDto dto);
}
