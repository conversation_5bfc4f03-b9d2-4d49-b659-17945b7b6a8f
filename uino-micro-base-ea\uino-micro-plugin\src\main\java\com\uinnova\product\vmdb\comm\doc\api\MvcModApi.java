package com.uinnova.product.vmdb.comm.doc.api;

import java.io.Serializable;

/**
 * 
 * <AUTHOR>
 *
 */
public class MvcModApi implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 方法名称 **/
    private String modName;

    /** 方法路径 **/
    private String path;

    /** 方法访问全路径 **/
    private String fullPath;

    /**
     * 方法描述
     */
    private String desc;

    /** 参数 **/
    private FieldDesc parameterDesc;

    /** 结果 **/
    private FieldDesc resultDesc;

    public String getFullPath() {
        return fullPath;
    }

    public void setFullPath(String fullPath) {
        this.fullPath = fullPath;
    }

    public String getModName() {
        return modName;
    }

    public void setModName(String modName) {
        this.modName = modName;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public FieldDesc getResultDesc() {
        return resultDesc;
    }

    public void setResultDesc(FieldDesc resultDesc) {
        this.resultDesc = resultDesc;
    }

    public FieldDesc getParameterDesc() {
        return parameterDesc;
    }

    public void setParameterDesc(FieldDesc parameterDesc) {
        this.parameterDesc = parameterDesc;
    }

}
