package com.uino.api.client.monitor.local;

import com.binary.jdbc.Page;
import com.uino.api.client.monitor.IUinoEventApiSvc;
import com.uino.bean.event.EventCiKpiCount;
import com.uino.bean.event.modeling.EventModel;
import com.uino.bean.event.param.EventCiKpiCountParam;
import com.uino.bean.event.param.EventCiSeverityQueryParam;
import com.uino.bean.monitor.base.ESAlarm;
import com.uino.bean.monitor.base.ESMonEapEvent;
import com.uino.bean.monitor.base.ESMonSysSeverityInfo;
import com.uino.bean.monitor.buiness.CurrentEventQueryDto;
import com.uino.bean.monitor.buiness.EventQueryDto;
import com.uino.dao.BaseConst;
import com.uino.monitor.event.service.IEventService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

@Service
public class UinoEventApiSvcLocal implements IUinoEventApiSvc {

	@Autowired
	private IEventService eventService;

	@Override
	public EventModel getEventModel() {
		return eventService.getEventModel();
	}

	@Override
	public Page<ESMonEapEvent> searchEventPage(EventQueryDto queryDto) {
		return eventService.searchEventPage(queryDto);
	}

	@Override
	public void saveAlarm(ESAlarm saveBean) {
		eventService.saveAlarm(saveBean);
	}

	@Override
	public void saveCurrentEvent(ESMonEapEvent fmEvent) {
		eventService.saveCurrentEvent(fmEvent);
	}

	@Override
	public List<ESMonEapEvent> listCurrentEventByParam(CurrentEventQueryDto queryDto) {
		return eventService.listCurrentEventByParam(queryDto);
	}

	@Override
	public List<ESMonEapEvent> listCurrentEventByParam(List<CurrentEventQueryDto> queryDtos) {
		return eventService.listCurrentEventByParam(queryDtos);
	}

	@Override
	public void delCurrentEvent(ESMonEapEvent fmEvent) {
		eventService.delCurrentEvent(fmEvent);
	}

	@Override
	public List<ESMonEapEvent> listCurrentEventByIds(Set<String> ids) {
		return eventService.listCurrentEventByIds(ids);
	}

	@Override
	public List<ESMonEapEvent> listCurrentEventByCiIds(List<Long> ciIds) {
		return eventService.listCurrentEventByCiIds(ciIds);
	}

	@Override
	public ESMonEapEvent currentEventById(String id) {
		return eventService.currentEventById(id);
	}

	@Override
	public void clearCurrentEvent() {
		eventService.clearCurrentEvent(BaseConst.DEFAULT_DOMAIN_ID);
	}

	@Override
	public void clearCurrentEvent(Long domainId) {
		eventService.clearCurrentEvent(domainId);
	}

	@Override
	public ESMonEapEvent saveEventHis(ESMonEapEvent esMonEapEvent) {
		return eventService.saveEventHis(esMonEapEvent);
	}

	@Override
	public ESMonEapEvent hisEventById(String id) {
		return eventService.hisEventById(id);
	}

	@Override
	public List<ESMonEapEvent> listCurrentEventByCiCodes(List<String> ciCodes) {
		return eventService.listCurrentEventByCiCodes(BaseConst.DEFAULT_DOMAIN_ID, ciCodes);
	}

	@Override
	public List<ESMonEapEvent> listCurrentEventByCiCodes(Long domainId, List<String> ciCodes) {
		return eventService.listCurrentEventByCiCodes(domainId, ciCodes);
	}

	@Override
	public List<ESMonSysSeverityInfo> getAllEventSeverity() {
		return eventService.getAllEventSeverity(BaseConst.DEFAULT_DOMAIN_ID);
	}

	@Override
	public List<ESMonSysSeverityInfo> getAllEventSeverity(Long domainId) {
		return eventService.getAllEventSeverity(domainId);
	}

	@Override
	public List<EventCiKpiCount> getEventCountByCiCodeAndKpiCodes(EventCiKpiCountParam param) {
		return eventService.getEventCountByCiCodeAndKpiCodes(BaseConst.DEFAULT_DOMAIN_ID, param.getCiCodes(), param.getKpiCodes());
	}

	@Override
	public List<EventCiKpiCount> getEventCountByCiCodeAndKpiCodes(Long domainId, EventCiKpiCountParam param) {
		return eventService.getEventCountByCiCodeAndKpiCodes(domainId, param.getCiCodes(), param.getKpiCodes());
	}

	@Override
	public boolean saveEventBatch(List<ESMonEapEvent> events) {
		return eventService.saveEventBatch(events);
	}

	@Override
	public List<ESMonEapEvent> queryCurrentEventsByCiCodesAndServeritys(EventCiSeverityQueryParam queryParam) {
		return eventService.queryCurrentEventsByCiCodesAndServeritys(BaseConst.DEFAULT_DOMAIN_ID, queryParam.getCiCodes(), queryParam.getSeveritys(), queryParam.getStatus());
	}

	@Override
	public List<ESMonEapEvent> queryCurrentEventsByCiCodesAndServeritys(Long domainId, EventCiSeverityQueryParam queryParam) {
		return eventService.queryCurrentEventsByCiCodesAndServeritys(domainId, queryParam.getCiCodes(), queryParam.getSeveritys(), queryParam.getStatus());
	}
}
