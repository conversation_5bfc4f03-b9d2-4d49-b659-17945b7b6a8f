package com.uinnova.product.eam.model.diagram;

import com.binary.framework.bean.annotation.Comment;

import java.util.Objects;


public class DiagramRelationInfo {

    @Comment("视图ID")
    private String value;

    @Comment("预制ID")
    private String prepareDiagramId;

    @Comment("资产库视图id")
    private String releaseDiagramId;

    @Comment("视图名称")
    private String name;

    @Comment("视图sheet页ID")
    private String page;

    @Comment("展示方式")
    private String showType;

    @Comment("视图ICON信息")
    private String icon;

    public String getReleaseDiagramId() {
        return releaseDiagramId;
    }

    public void setReleaseDiagramId(String releaseDiagramId) {
        this.releaseDiagramId = releaseDiagramId;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getPrepareDiagramId() {
        return prepareDiagramId;
    }

    public void setPrepareDiagramId(String prepareDiagramId) {
        this.prepareDiagramId = prepareDiagramId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPage() {
        return page;
    }

    public void setPage(String page) {
        this.page = page;
    }

    public String getShowType() {
        return showType;
    }

    public void setShowType(String showType) {
        this.showType = showType;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof DiagramRelationInfo)) return false;
        DiagramRelationInfo that = (DiagramRelationInfo) o;
        return getValue().equals(that.getValue()) &&
                getPrepareDiagramId().equals(that.getPrepareDiagramId()) &&
                getName().equals(that.getName()) &&
                getPage().equals(that.getPage()) &&
                getShowType().equals(that.getShowType()) &&
                getIcon().equals(that.getIcon());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getValue(), getPrepareDiagramId(), getName(), getPage(), getShowType(), getIcon());
    }

}
