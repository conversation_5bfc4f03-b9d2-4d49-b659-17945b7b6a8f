package com.uinnova.product.eam.service;

import com.binary.jdbc.Page;
import com.uinnova.product.eam.comm.model.es.EamCategory;
import com.uinnova.product.eam.model.ObjectMovingDto;
import com.uinnova.product.eam.model.vo.C2CTreeNodeDto;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;

import java.util.List;

/**
 * 流程管理/端到端流程服务层
 *
 * <AUTHOR>
 */
public interface C2CProcessService {

    void delete( Long id);

    List<C2CTreeNodeDto> getC2CTree(Long id);

    Page<CcCiInfo> queryChildInfoPageById(Long id, Integer pageNum, Integer pageSize, String words);

    Long saveOrUpdateCategory(EamCategory vo);

    Integer moveCi(ObjectMovingDto objectMovingDto);

    void publishC2CFlowProcessSystem(String ciCode,String loginCode,String publishType);
}
