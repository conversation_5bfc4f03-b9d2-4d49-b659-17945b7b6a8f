package com.uino.provider.server.web.cmdb.mvc;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.uino.service.cmdb.microservice.impl.TopDataSvc;
import com.uino.provider.feign.cmdb.TopDataFeign;

import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("feign/topData")
@Slf4j
public class TopDataFeignMvc implements TopDataFeign {

    @Autowired
    private TopDataSvc svc;

    @Override
    public void unTop(Long domainId,Long topData, Long topDataType) {
        // TODO Auto-generated method stub
        svc.unTop(domainId,topData, topDataType);
    }

    @Override
    public void top(Long domainId,Long topData, Long topDataType) {
        // TODO Auto-generated method stub
        svc.top(domainId,topData, topDataType);
    }
}
