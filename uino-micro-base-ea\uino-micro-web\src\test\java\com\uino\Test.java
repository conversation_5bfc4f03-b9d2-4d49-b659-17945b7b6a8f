package com.uino;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;

import com.binary.core.encrypt.Encrypt;
import com.binary.core.exception.MessageException;

public class Test {
	public static void main(String[] args) throws Exception {
		String pwd = Encrypt.encrypt("admin@admin");
		System.out.println(pwd);
		// RestTemplate rest = new RestTemplate();
		// HttpHeaders requestHeaders = new HttpHeaders();
		// requestHeaders.add(HttpHeaders.COOKIE,
		// "trace=AQAAAHI21ymW3QEACpzMb6cshWZoxkbF;
		// cna=OesRFjgJeDYCAW/MnAp2IO33; ctoken=Y8UKY1nz9qH-ixXS4oQ_b1kl;
		// EGG_SESS_ICONFONT=U8AXvqwdm-42-umGXGwgKq_Emj2wuVCkA87TjZ3dn6xm2T4whio3sIKoy4kjkuBSusLMQ-0MhcjWBE1FwhfGmMbpO9xPCEANAHIhoET_7kJ_pbscGV6FmfCh8QTWcmCiTv5lhhXEW-AxLfe1otCy-eI-zPgODc0D5EZxlVSk4mqOdEz-94IZi5OAcsu3pRkTAQs9KRTgwyfMtp67P9YXwDeVNoXPHTR1XHpaQgBHgWZxIoXczyxCXVtKz5kL3XUgvwp6JLe2wev9xkYzghiHak_C9KQNcCprAHLtoqqP_9PnlEYIg-9HCGy_wFeqZSARwkgx3d1dgU2Ajj42Sg2m5P38HPhA3KW9FOsArS6tArnQG8tYYWdhGYiWmSKuDvPFE1ZPSlfwUZ1WkFHeir_Tl174lrzGR8utF9X1bWduzm3SgynzwbFnpmZ-e6H1q5xmYmyQh-6hLRi3OYlNSgquF3kTTpBoJyfvzumhKk4Awja-V5Q6hwlBfcD9S8pAE3kYxK9V8tU5vc_QhRfnOSitLp11F5tRbj0Fzwy_8rZxuFJGNqIon4MP8cK9tLEDD3LxuH_AOy5vGXkknSU-4HI1s2-rsMBjAhpvx2XUJVBGVF0nBINweiPkYZ38P2g-xK8FwuImfooN1d9FVNSTxBUzLtUCeMOlmLAewiyesHgN4YNZL7QmBpTklX4D_ylpgkH41MO3uWVS8JPcxHUwnkvyRaTT-qscNCLZvV1_XweZwEVKzzG96nUovrxlaULBtg4eULmGpgh-EDK_ZBOJaS_aLLIq2k9Mkpj61qUc2c6wQxbq-Ypm_sVtRk5dfCfQTzdI9p5nHymoT6Z1Kh4QUKWf2X6Pc7gSmX3j_U2Ngz_2PGWpUTIpgVa0cBnp9LG7pzjCfftwG7wWCQxxclu8vuJmzett88_XvrZSy6VpY-dvd768fHJQtKdk07hOC4AbTSWXWfKOYyott0UFjEKTeCCMXQqU52_A-6eMpmGlpLjrJnY=;
		// u=5114504; u.sig=yOyiI4LF9IyWpraWBK7sRYhujLRM80_-rQK_F9WzPr0;
		// isg=BIWF--frdIhwyFDUdrb4Aej8lMG_QjnUbUfBvIf-6b5PHrqQSpPtpXm8LAJNXlGM");
		// HttpEntity<String> requestEntity = new
		// HttpEntity<String>(requestHeaders);
		// ResponseEntity<String> re = rest.exchange(
		// "https://www.iconfont.cn/api/project/detail.json?pid=1398753&t=1569490180124&ctoken=Y8UKY1nz9qH-ixXS4oQ_b1kl",
		// HttpMethod.GET, requestEntity, String.class, new HashMap<>());
		// // = rest.getForEntity("", String.class, requestEntity);
		// String json = re.getBody();
		// // System.out.println(json);
		// JSONObject jsonObj = JSON.parseObject(json);
		// jsonObj = jsonObj.getJSONObject("data");
		// JSONArray iconArray = jsonObj.getJSONArray("icons");
		// Map<String, String> dic = new HashMap<>();
		// for (int index = 0; index < iconArray.size(); index++) {
		// JSONObject icon = iconArray.getJSONObject(index);
		// dic.put(icon.getString("font_class"), icon.getString("name"));
		// }
		// System.out.println(JSON.toJSONString(dic));
		// String allUid = "";
		// int i = 0;
		// for (int index = 0; index < iconArray.size(); index++) {
		// i++;
		// JSONObject icon = iconArray.getJSONObject(index);
		// int projectId = icon.getIntValue("projectId");
		// int id = icon.getIntValue("id");
		// String uid = id + "|" + projectId;
		// allUid += uid + ",";
		// if (i == 15) {
		// i = 0;
		// allUid = allUid.substring(0, allUid.lastIndexOf(","));
		//
		// ResponseEntity<byte[]> re1 = rest.exchange(
		// "https://www.iconfont.cn/api/icon/downloadIcon?type=png&color=333333&size=200&ctoken=Y8UKY1nz9qH-ixXS4oQ_b1kl&ids="
		// + allUid,
		// HttpMethod.GET, requestEntity, byte[].class, new HashMap<>());
		// getFileByBytes(re1.getBody(), "C:\\Users\\<USER>\\Desktop\\sxk\\",
		// UUID.randomUUID().toString() + ".zip");
		// allUid = "";
		// }
		// }
		// allUid = allUid.substring(0, allUid.lastIndexOf(","));
		// ResponseEntity<byte[]> re1 = rest.exchange(
		// "https://www.iconfont.cn/api/icon/downloadIcon?type=png&color=333333&size=200&ctoken=Y8UKY1nz9qH-ixXS4oQ_b1kl&ids="
		// + allUid,
		// HttpMethod.GET, requestEntity, byte[].class, new HashMap<>());
		// getFileByBytes(re1.getBody(), "C:\\Users\\<USER>\\Desktop\\sxk\\",
		// UUID.randomUUID().toString() + ".zip");
	}

	public static void getFileByBytes(byte[] bytes, String filePath, String fileName) {
		BufferedOutputStream bos = null;
		FileOutputStream fos = null;
		File file = null;
		try {
			File dir = new File(filePath);
			if (!dir.exists() && dir.isDirectory()) {// 判断文件目录是否存在
				dir.mkdirs();
			}
			file = new File(filePath + "\\" + fileName);
			fos = new FileOutputStream(file);
			bos = new BufferedOutputStream(fos);
			bos.write(bytes);
		} catch (Exception e) {
			throw new MessageException(e.getMessage());
		} finally {
			if (bos != null) {
				try {
					bos.close();
				} catch (IOException e) {
					throw new MessageException(e.getMessage());
				}
			}
			if (fos != null) {
				try {
					fos.close();
				} catch (IOException e) {
					throw new MessageException(e.getMessage());
				}
			}
		}
	}
}
