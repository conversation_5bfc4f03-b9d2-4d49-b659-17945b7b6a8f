package com.uinnova.product.eam.model.bm;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.comm.model.es.EamCategory;
import com.uinnova.product.eam.model.dto.EamHierarchyDto;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.base.LibType;
import lombok.Data;

/**
 * 业务建模自动成图
 * <AUTHOR>
 */
@Data
public class QuickDrawingDto {
    @Comment("领域id")
    private Long domainId;
    @Comment("默认私有库")
    private LibType libType = LibType.PRIVATE;
    @Comment("视图id")
    private String diagramId;
    @Comment("视图所属人")
    private String ownerCode;
    @Comment("制品id")
    private Long artifactId;
    @Comment("源端ciCode")
    private String ciCode;
    @Comment("当前成图目录")
    private EamCategory category;
    @Comment("层级配置信息")
    private EamHierarchyDto hierarchy;
    @Comment("视图绑定ci")
    private ESCIInfo ci;
}
