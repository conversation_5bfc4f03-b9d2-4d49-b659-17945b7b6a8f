package com.uinnova.product.eam.service.bm;

import com.uinnova.product.eam.comm.model.es.CEamDiagramCatalog;
import com.uinnova.product.eam.comm.model.es.EamDiagramCatalog;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;

@Service
@Deprecated
public class EsDiagramCatalogPrivateDao extends AbstractESBaseDao<EamDiagramCatalog, CEamDiagramCatalog> {

    @Override
    public String getIndex() {
        return "uino_eam_diagram_catalog_private";
    }

    @Override
    public String getType() {
        return getIndex();
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }
}
