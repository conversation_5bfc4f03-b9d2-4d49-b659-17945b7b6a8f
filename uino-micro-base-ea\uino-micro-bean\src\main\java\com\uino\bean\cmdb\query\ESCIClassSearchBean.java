package com.uino.bean.cmdb.query;

import com.uinnova.product.vmdb.comm.model.ci.CCcCiClass;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * CI分类查询类
 * 
 * <AUTHOR>
 *
 */
@ApiModel(value = "对象分类查询类", description = "对象分类查询类")
@Getter
@Setter
public class ESCIClassSearchBean extends ESSearchBase implements Serializable {

    /**
     * 
     */
    private static final long serialVersionUID = -7198209059988453310L;

	@ApiModelProperty(value = "分类/孪生模型查询条件")
    private CCcCiClass cdt;

	@ApiModelProperty(value = "排序字段", example = "abc")
    private String orders;

	@ApiModelProperty(value = "是否升序", example = "true/false 默认false")
    private Boolean isAsc = false;

	@ApiModelProperty(value = "是否查询绑定kpi的分类", example = "true/false 默认false")
    private Boolean isBindKpi = false;
    
	@ApiModelProperty(value = "分类/孪生模型查询关键字")
    private String keyword;

	@ApiModelProperty(value = "分类/孪生模型id")
	private String dtClassId;

    @ApiModelProperty(value = "属性名称")
    private String proName;
	
}
