package com.uinnova.product.eam.model;

import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 业务架构的节点对象
 *
 * <AUTHOR>
 * @version 2020-8-20
 */
public class BizNodeDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 业务名称
     */
    public String name;

    /**
     * 此业务系统自身ci信息
     */
    public CcCiInfo ciInfo;

    /**
     * 关联应用的数量
     */
    public List<BizNodeDto> children = new ArrayList<>();


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public CcCiInfo getCiInfo() {
        return ciInfo;
    }

    public void setCiInfo(CcCiInfo ci) {
        this.ciInfo = ci;
    }

    public List<BizNodeDto> getChildren() {
        return children;
    }

    public void setChildren(List<BizNodeDto> children) {
        this.children = children;
    }
}
