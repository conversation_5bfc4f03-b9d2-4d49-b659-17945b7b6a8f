package com.uino.init.http.response;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;

import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.WriteListener;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpServletResponseWrapper;

import lombok.AllArgsConstructor;

/**
 * 自定义response 主要重写了写入方法，使其http输出流保持的同时新增一个字节输出流保证程序可以读取response种的responsebody
 * 中心逻辑就是将写入到ServletOutputStream修改为写入到ByteArrayOutputStream；
 * 准备flush时再从ByteArrayOutputStream拷贝到ServletOutputStream
 *
 * <AUTHOR>
 */
public class CustomHttpServletResponseWrapper extends HttpServletResponseWrapper {
	private ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
	private HttpServletResponse response;

	public CustomHttpServletResponseWrapper(HttpServletResponse response) {
		super(response);
		this.response = response;
	}

	/**
	 * 获取请求体输出字节流
	 *
	 * @return
	 */
	public byte[] getBody() {
		return byteArrayOutputStream.toByteArray();
	}

	@Override
	public ServletOutputStream getOutputStream() {
		return new ServletOutputStreamWrapper(this.byteArrayOutputStream, this.response, null);
	}

	@Override
	public PrintWriter getWriter() throws IOException {
		return new PrintWriter(new OutputStreamWriter(getOutputStream(), this.response.getCharacterEncoding()));
	}

	/**
	 * <AUTHOR>
	 */
	@AllArgsConstructor
	private static class ServletOutputStreamWrapper extends ServletOutputStream {

		private ByteArrayOutputStream byteArrayOutputStream;
		private HttpServletResponse response;
		private ServletOutputStream servletOutputStream;

		@Override
		public boolean isReady() {
			return true;
		}

		@Override
		public void setWriteListener(WriteListener listener) {

		}

		@Override
		public void write(int b) throws IOException {
			String responseContentTypeLowerCase = response.getContentType().toLowerCase();
			if (!responseContentTypeLowerCase.contains("stream") && !responseContentTypeLowerCase.contains("image/x-png")) {
				this.byteArrayOutputStream.write(b);
			} else {
				//文件不加入缓存
				if (servletOutputStream == null) {
					servletOutputStream = this.response.getOutputStream();
				}
				servletOutputStream.write(b);
			}
		}

		@Override
		public void write(byte[] b) throws IOException {
			String responseContentTypeLowerCase = response.getContentType().toLowerCase();
			if (!responseContentTypeLowerCase.contains("stream") && !responseContentTypeLowerCase.contains("image/x-png")) {
				this.byteArrayOutputStream.write(b);
			} else {
				//文件不加入缓存
				if (servletOutputStream == null) {
					servletOutputStream = this.response.getOutputStream();
				}
				servletOutputStream.write(b);
			}
		}

		/**
		 * 保障复写前逻辑，最后要写到ServletOutputStream，否则无法传递client
		 */
		@Override
		public void flush() throws IOException {
			if (!this.response.isCommitted()) {
				String responseContentTypeLowerCase = response.getContentType().toLowerCase();
				if (!responseContentTypeLowerCase.contains("stream") && !responseContentTypeLowerCase.contains("image/x-png")) {
					byte[] body = this.byteArrayOutputStream.toByteArray();
					this.response.resetBuffer();
					ServletOutputStream outputStream = this.response.getOutputStream();
					outputStream.write(body);
					outputStream.flush();
				} else {
					ServletOutputStream outputStream = this.response.getOutputStream();
					outputStream.flush();
				}

			}
		}
	}
}
