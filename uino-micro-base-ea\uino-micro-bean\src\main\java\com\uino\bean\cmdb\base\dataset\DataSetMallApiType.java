package com.uino.bean.cmdb.base.dataset;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Classname OperateType
 * @Description 接口类型
 * @Date 2020/3/18 15:19
 * @Created by u<PERSON>ova
 */
@ApiModel(value="接口类型",description = "接口类型")
public enum DataSetMallApiType {
    //
    RelationRule(1), CiClass(2), RelClass(3), UpDownNFloor(4), Metrics(5), Statistic(6);

    @ApiModelProperty(value="code",example = "123")
    private Integer code;

    private DataSetMallApiType(Integer code) {
        this.code = code;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public static DataSetMallApiType valueOf(Integer code) {
        if (RelationRule.code.equals(code)) {
            return RelationRule;
        } else if (CiClass.code.equals(code)) {
            return CiClass;
        } else if (RelClass.code.equals(code)) {
            return RelClass;
        } else if (UpDownNFloor.code.equals(code)) {
            return UpDownNFloor;
        } else if (Metrics.code.equals(code)) {
            return Metrics;
        } else if (Statistic.code.equals(code)) {
            return Statistic;
        } else {
            return null;
        }
    }
}
