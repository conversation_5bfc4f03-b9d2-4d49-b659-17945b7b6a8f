package com.uinnova.product.eam.service.impl;

import com.binary.core.util.BinaryUtils;
import com.uinnova.product.eam.comm.model.es.WorkbenchLayoutConf;
import com.uinnova.product.eam.service.WorkbenchLayoutConfSvc;
import com.uinnova.product.eam.service.es.WorkbenchLayoutConfDao;
import com.uino.bean.permission.base.SysUser;
import com.uino.dao.util.ESUtil;
import com.uino.util.sys.SysUtil;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

@Service
public class WorkbenchLayoutConfSvcImpl implements WorkbenchLayoutConfSvc {

    @Resource
    WorkbenchLayoutConfDao workbenchLayoutConfDao;

    @Override
    public Long saveOrUpdate(WorkbenchLayoutConf workbenchLayoutConf) {
        WorkbenchLayoutConf layout = getLayout();
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        if (BinaryUtils.isEmpty(layout)) {
            workbenchLayoutConf.setId(ESUtil.getUUID());
            workbenchLayoutConf.setCreateTime(System.currentTimeMillis());
        } else {
            workbenchLayoutConf.setId(layout.getId());
        }
        workbenchLayoutConf.setCreator(currentUserInfo.getLoginCode());
        workbenchLayoutConf.setDomainId(currentUserInfo.getDomainId());
        workbenchLayoutConf.setModifyTime(System.currentTimeMillis());

        return workbenchLayoutConfDao.saveOrUpdate(workbenchLayoutConf);
    }

    @Override
    public WorkbenchLayoutConf getLayout() {
        return workbenchLayoutConfDao.selectOne(QueryBuilders.termQuery("creator.keyword", SysUtil.getCurrentUserInfo().getLoginCode()));
    }
}
