package com.uinnova.product.eam.comm.model.es;

import com.alibaba.fastjson.JSONObject;
import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class AssetListAttrConf {
    @Comment("主键id")
    private Long id;
    @Comment("广场资产配置ID")
    private Long appSquareConfId;
    @Comment("资产分类")
    private String classCode;
    @Comment("类型 1:卡片，2:表单")
    private Integer type;
    @Comment("展示字段")
    private List<JSONObject> showAttrs;

    @Comment("标签展示")
    private List<JSONObject> tagAttrs;

    @Comment("领域id")
    private Long domainId;
    @Comment("创建人")
    private String creator;
    @Comment("修改人")
    private String modifier;
    @Comment("创建时间")
    private Long createTime;
    @Comment("修改时间")
    private Long modifyTime;

    @Comment("定制字段-统计数据开关")
    private Boolean countButton;
}
