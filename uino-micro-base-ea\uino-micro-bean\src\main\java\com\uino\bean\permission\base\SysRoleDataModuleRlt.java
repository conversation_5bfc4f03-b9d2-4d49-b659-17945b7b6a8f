package com.uino.bean.permission.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * mapping-table: 角色与数据关联表[SYS_ROLE_DATA_MODULE_RLT]
 * 
 * <AUTHOR>
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value="角色与数据关联表",description = "角色与数据关联表")
public class SysRoleDataModuleRlt implements Serializable {
    private static final long serialVersionUID = 1L;


    /** ID */
    @ApiModelProperty(value="id",example = "123")
    private Long id;


    /** 数据模块代码(处理数据权限的类别) */
    @ApiModelProperty(value="数据模块代码(处理数据权限的类别)")
    private String dataModuleCode;


    /** 唯一ID(与前台交互) */
    @ApiModelProperty(value="唯一ID(与前台交互)")
    private String uid;


    /** 数据值（表达式或实例ID） */
    @ApiModelProperty(value="数据值(表达式或实例ID)")
    private String dataValue;


    /** 角色ID */
    @ApiModelProperty(value="角色ID")
    private Long roleId;


    /** 所属域 */
    @ApiModelProperty(value="所属域id")
    private Long domainId;


    /** 删除权限 */
    @ApiModelProperty(value="删除权限")
    private Integer isdelete;


    /** 修改权限 */
    @ApiModelProperty(value="修改权限")
    private Integer isupdate;


    /** 创建权限 */
    @ApiModelProperty(value="创建权限")
    private Integer iscreate;


    /** 查看权限 */
    @ApiModelProperty(value="查看权限")
    private Integer issee;


    /** 创建人 */
    @ApiModelProperty(value="创建人")
    private String creator;


    /** 修改人 */
    @ApiModelProperty(value="修改人")
    private String modifier;


    /** 创建时间 */
    @ApiModelProperty(value="创建时间")
    private Long createTime;


    /** 修改时间 */
    @ApiModelProperty(value="修改时间")
    private Long modifyTime;

    /** 扩展权限 */
    @ApiModelProperty(value="扩展权限")
    private List<ExtendedPermission> extendedPermissions;


    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }


    public String getDataModuleCode() {
        return this.dataModuleCode;
    }

    public void setDataModuleCode(String dataModuleCode) {
        this.dataModuleCode = dataModuleCode;
    }


    public String getUid() {
        return this.uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }


    public String getDataValue() {
        return this.dataValue;
    }

    public void setDataValue(String dataValue) {
        this.dataValue = dataValue;
    }


    public Long getRoleId() {
        return this.roleId;
    }

    public void setRoleId(Long roleId) {
        this.roleId = roleId;
    }

    public Long getDomainId() {
        return this.domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }


    public Integer getIsdelete() {
        return this.isdelete;
    }

    public void setIsdelete(Integer isdelete) {
        this.isdelete = isdelete;
    }


    public Integer getIsupdate() {
        return this.isupdate;
    }

    public void setIsupdate(Integer isupdate) {
        this.isupdate = isupdate;
    }


    public Integer getIscreate() {
        return this.iscreate;
    }

    public void setIscreate(Integer iscreate) {
        this.iscreate = iscreate;
    }


    public Integer getIssee() {
        return this.issee;
    }

    public void setIssee(Integer issee) {
        this.issee = issee;
    }


    public String getCreator() {
        return this.creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }


    public String getModifier() {
        return this.modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }


    public Long getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }


    public Long getModifyTime() {
        return this.modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }

    public List<ExtendedPermission> getExtendedPermissions() {
        return extendedPermissions;
    }

    public void setExtendedPermissions(List<ExtendedPermission> extendedPermissions) {
        this.extendedPermissions = extendedPermissions;
    }
}
