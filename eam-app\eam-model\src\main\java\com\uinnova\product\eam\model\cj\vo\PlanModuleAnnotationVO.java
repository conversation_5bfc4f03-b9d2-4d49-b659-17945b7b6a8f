package com.uinnova.product.eam.model.cj.vo;

import com.uinnova.product.eam.model.vo.ReplyAnnotationVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 方案批注VO
 *
 * <AUTHOR>
 * @since 2022-3-1 21:12:15
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PlanModuleAnnotationVO {

    /**
     * 标注的标题
     */
    private String annotationTitle;

    /**
     * 章节名称
     */
    private String planChapterName;

    /**
     * 章节id
     */
    private Long planChapterId;

    /**
     * 内容块id
     */
    private Long planChapterModuleId;

    /**
     * 内容块名称
     */
    private String planChapterModuleName;

    /**
     * 批注id
     */
    private Long annotationId;

    /**
     * 批注内容
     */
    private String annotationContent;

    /**
     * 是否归档为问题
     */
    private Boolean problem;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 写批注的人
     */
    private String author;

    /**
     * 写批注的人Code
     */
    private String authorCode;

    /**
     * 归档为问题的时间
     */
    private Long questionDate;

    /**
     * 问题处理状态
     */
    private String questionState;

    /**
     * 是否有归档为问题的权限
     */
    private boolean questionPower;

    /**
     * 如果是批注 封装可能回复的批注的信息
     */
    private ReplyAnnotationVO replyAnnotationVO;
}
