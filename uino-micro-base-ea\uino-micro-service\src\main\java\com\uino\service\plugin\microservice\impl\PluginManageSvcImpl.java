package com.uino.service.plugin.microservice.impl;

import com.binary.core.exception.MessageException;
import com.binary.core.io.Resource;
import com.binary.core.io.support.FileResource;
import com.binary.jdbc.Page;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.plugin.base.ESPluginInfo;
import com.uino.bean.plugin.base.LoadStatusEnum;
import com.uino.bean.plugin.query.CPluginInfo;
import com.uino.dao.plugin.ESPluginSvc;
import com.uino.plugin.bean.OperatePluginDetails;
import com.uino.service.plugin.init.PluginDistributionManage;
import com.uino.service.plugin.microservice.PluginManageSvc;
import com.uino.plugin.classloader.PluginClassLoader;
import com.uino.util.sys.SysUtil;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.sort.SortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2021/9/27
 * @Version 1.0
 */
@Service
@Slf4j
public class PluginManageSvcImpl implements PluginManageSvc {

    @Autowired
    private ESPluginSvc esPluginSvc;

    @Autowired
    private PluginDistributionManage pluginDistributionManage;

    @Override
    public List<String> getServiceList() {
        List<String> serviceList = pluginDistributionManage.getServiceList();
        if (serviceList == null) {
            serviceList = new ArrayList<>();
        }
        return serviceList;
    }

    @Override
    public Long saveOrUpdate(ESPluginInfo pluginInfo) {
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        pluginInfo.setUserCode(currentUserInfo.getLoginCode());
        String name = pluginInfo.getName();
        String fileName = pluginInfo.getFileName();
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        if (pluginInfo.getId() != null) {
            query.mustNot(QueryBuilders.termQuery("id", pluginInfo.getId()));
        }
        query.must(QueryBuilders.termQuery("name.keyword", name));
//        query.must(QueryBuilders.termQuery("fileName.keyword", fileName));
        List<ESPluginInfo> listByQuery = esPluginSvc.getListByQuery(query);
        if (!listByQuery.isEmpty()) {
            throw new MessageException("插件名称已存在");
        }

        //
        if (pluginInfo.getAutoload()) {
            distributionPlugin(pluginInfo);
            List<String> ownedService = pluginInfo.getOwnedService();
            List<OperatePluginDetails> loadPlugin = pluginDistributionManage.loadPlugin(ownedService, fileName);
            boolean loadSuccess = true;
            for (OperatePluginDetails operatePluginDetails : loadPlugin) {
                if (!operatePluginDetails.isSuccess()) {
                    pluginInfo.getErrorDescription().add(operatePluginDetails);
                    loadSuccess = false;
                }
            }
            pluginInfo.setLoadStatus(loadSuccess ? LoadStatusEnum.startUp.getCode() : LoadStatusEnum.startError.getCode());
        } else {
            pluginInfo.setLoadStatus(0);
        }
        return esPluginSvc.saveOrUpdate(pluginInfo);
    }

    /**
     * 分发插件
     *
     * @param pluginInfo
     */
    private void distributionPlugin(ESPluginInfo pluginInfo) {
        File file = new File(PluginClassLoader._PRELOADING_PLUGIN_PATH + pluginInfo.getFileName());
        if (!file.exists()) {
            throw new MessageException("插件文件不存在，请重新上传");
        }
        MultipartFile multipartFile = null;
        try {
            InputStream inputStream = new FileInputStream(file);
            multipartFile = new MockMultipartFile(file.getName(), file.getName(), null, inputStream);
        } catch (IOException e) {
            log.error("文件流读取异常", e);
            throw new MessageException("", e);
        }
        List<String> ownedService = pluginInfo.getOwnedService();
        List<OperatePluginDetails> operatePluginDetailsList = pluginDistributionManage.uploadPlugin(ownedService, multipartFile);
        operatePluginDetailsList.forEach(operatePluginDetails -> {
            if (!operatePluginDetails.isSuccess()) {
                pluginInfo.getErrorDescription().add(operatePluginDetails);
            }
        });
    }

    @Override
    public String uploadPlugin(Long id, MultipartFile file) {
        if (id != null) {
            ESPluginInfo pluginInfo = esPluginSvc.getById(id);
            if (!pluginInfo.getFileName().equals(file.getOriginalFilename())) {
                throw new MessageException("替换文件与原文件名不一致");
            }
        } else {
            BoolQueryBuilder query = QueryBuilders.boolQuery();
            query.must(QueryBuilders.termQuery("fileName.keyword", file.getOriginalFilename()));
            List<ESPluginInfo> listByQuery = esPluginSvc.getListByQuery(query);
            if (!listByQuery.isEmpty()) {
                throw new MessageException("插件已存在");
            }
        }
        String fileName = file.getOriginalFilename();
        try {
            File dir = new File(PluginClassLoader._PRELOADING_PLUGIN_PATH);
            if (!dir.exists()) {
                dir.mkdirs();
            }
            byte[] fileBytes = file.getBytes();
            FileOutputStream fos = new FileOutputStream(PluginClassLoader._PRELOADING_PLUGIN_PATH + fileName);
            fos.write(fileBytes);
            fos.close();
        } catch (IOException e) {
            log.error("获取上传文件流异常", e);
            throw new MessageException("获取上传文件流异常");
        }
        boolean b = pluginDistributionManage.synchronizePluginFile(fileName);
        if (!b) {
            File jarFile = new File(PluginClassLoader._PRELOADING_PLUGIN_PATH + fileName);
            jarFile.delete();
            log.error("插件同步失败");
            throw new MessageException("插件上传失败");
        }
        return file.getOriginalFilename();
    }

    @Override
    public boolean syncPlugin(MultipartFile file) {
        try {
            File dir = new File(PluginClassLoader._PRELOADING_PLUGIN_PATH);
            if (!dir.exists()) {
                dir.mkdirs();
            }
            String fileName = file.getOriginalFilename();
            byte[] fileBytes = file.getBytes();
            FileOutputStream fos = new FileOutputStream(PluginClassLoader._PRELOADING_PLUGIN_PATH + fileName);
            fos.write(fileBytes);
            fos.close();
        } catch (IOException e) {
            log.error("获取上传文件流异常", e);
            throw new MessageException("获取上传文件流异常");
        }
        return true;
    }

    @Override
    public Resource downloadPlugin(Long id) {
        ESPluginInfo pluginInfo = esPluginSvc.getById(id);
        if (pluginInfo == null) {
            throw new MessageException("插件不存在");
        }
        Resource resource = new FileResource(PluginClassLoader._PRELOADING_PLUGIN_PATH + pluginInfo.getFileName());
        if (!resource.exists()) {
            throw new MessageException("插件文件不存在");
        }
        return resource;
    }

    @Override
    public Page<ESPluginInfo> queryList(int pageNum, int pageSize, CPluginInfo cPluginInfo) {
        List<SortBuilder<?>> sorts = new LinkedList<>();
        sorts.add(SortBuilders.fieldSort("createTime").order(SortOrder.DESC));
        return esPluginSvc.getSortListByCdt(pageNum, pageSize, cPluginInfo, sorts);
    }

    @Override
    public boolean loadOrUnloadPlugin(Long id) {
        ESPluginInfo pluginInfo = esPluginSvc.getById(id);
        if (pluginInfo == null) {
            throw new MessageException("插件信息不存在");
        }
        boolean loadSuccess = true;
        List<OperatePluginDetails> operatePluginDetailsList = new ArrayList<>();

        Integer loadStatus = pluginInfo.getLoadStatus();
        //停止状态和启动失败
        if (loadStatus == LoadStatusEnum.stop.getCode() || loadStatus == LoadStatusEnum.startError.getCode()) {
            distributionPlugin(pluginInfo);

            List<OperatePluginDetails> loadPlugin = pluginDistributionManage.loadPlugin(pluginInfo.getOwnedService(), pluginInfo.getFileName());
            for (OperatePluginDetails operatePluginDetails : loadPlugin) {
                if (!operatePluginDetails.isSuccess()) {
                    operatePluginDetailsList.add(operatePluginDetails);
                    loadSuccess = false;
                }
            }
            pluginInfo.setLoadStatus(loadSuccess ? LoadStatusEnum.startUp.getCode() : LoadStatusEnum.startError.getCode());
        } else {
            List<OperatePluginDetails> loadPlugin = pluginDistributionManage.unloadAndDeletePlugin(pluginInfo.getOwnedService(), pluginInfo.getFileName());
            loadPlugin.forEach(operatePluginDetails -> {
                if (!operatePluginDetails.isSuccess()) {
                    operatePluginDetailsList.add(operatePluginDetails);
                }
            });
            pluginInfo.setLoadStatus(LoadStatusEnum.stop.getCode());
        }
        pluginInfo.setErrorDescription(operatePluginDetailsList);
        esPluginSvc.saveOrUpdate(pluginInfo);
        return loadSuccess;
    }


    @Override
    public boolean deletePlugin(Long id) {
        ESPluginInfo pluginInfo = esPluginSvc.getById(id);
        if (pluginInfo == null) {
            throw new MessageException("插件信息不存在");
        }
        pluginDistributionManage.unloadAndDeletePlugin(pluginInfo.getOwnedService(), pluginInfo.getFileName());

        String fileName = pluginInfo.getFileName();
        File file = new File(PluginClassLoader._PRELOADING_PLUGIN_PATH + fileName);
        if (file.exists()) {
            file.delete();
        }
        esPluginSvc.deleteById(id);
        return true;
    }

    @Override
    public List<String> queryListByOpenServer(String serverName) {
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("loadStatus", LoadStatusEnum.startUp.getCode()));
        query.must(QueryBuilders.termQuery("ownedService.keyword", serverName));
        List<ESPluginInfo> pluginInfos = esPluginSvc.getListByQuery(query);
        List<String> result = new ArrayList<>();
        for (ESPluginInfo pluginInfo : pluginInfos) {
            result.add(pluginInfo.getFileName());
        }
        return result;
    }
}
