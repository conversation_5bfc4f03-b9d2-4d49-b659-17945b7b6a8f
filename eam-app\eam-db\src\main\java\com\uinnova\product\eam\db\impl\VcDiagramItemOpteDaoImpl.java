package com.uinnova.product.eam.db.impl;


import com.uinnova.product.eam.comm.model.CVcDiagramItemOpte;
import com.uinnova.product.eam.comm.model.VcDiagramItemOpte;
import com.uinnova.product.eam.db.VcDiagramItemOpteDao;
import com.uinnova.product.eam.db.support.dao.mybatis.ComMyBatisBinaryDaoImpl;


/**
 * 视图图标操作表[VC_DIAGRAM_ITEM_OPTE]数据访问对象实现
 */
public class VcDiagramItemOpteDaoImpl extends ComMyBatisBinaryDaoImpl<VcDiagramItemOpte, CVcDiagramItemOpte> implements VcDiagramItemOpteDao {

//    @Override
//    public String getTableName() {
//        return "IAMS." + getDaoDefinition().getTableName();
//    }
}


