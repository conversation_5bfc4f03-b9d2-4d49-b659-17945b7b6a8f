package com.uinnova.product.eam.web.diagram.bean;

import com.binary.framework.bean.annotation.Comment;

public class DiagramTotalCount {

	@Comment("视图总数")
	private Long totalDiagram;
	
	@Comment("广场视图总数")
	private Long totalOpenDiagram;

	public Long getTotalDiagram() {
		return totalDiagram;
	}

	public void setTotalDiagram(Long totalDiagram) {
		this.totalDiagram = totalDiagram;
	}

	public Long getTotalOpenDiagram() {
		return totalOpenDiagram;
	}

	public void setTotalOpenDiagram(Long totalOpenDiagram) {
		this.totalOpenDiagram = totalOpenDiagram;
	}
	
	
}
