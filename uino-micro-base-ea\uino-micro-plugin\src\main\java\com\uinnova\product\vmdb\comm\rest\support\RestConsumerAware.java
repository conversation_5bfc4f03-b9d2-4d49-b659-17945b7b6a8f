package com.uinnova.product.vmdb.comm.rest.support;

import com.binary.framework.exception.ServiceException;
import com.uinnova.product.vmdb.comm.i18n.MessageUtil;

import java.lang.reflect.Proxy;
import java.util.HashSet;
import java.util.Set;

/**
 * 
 * <AUTHOR>
 *
 */
public class RestConsumerAware {

    private static final Set<String> ignoreMethods = new HashSet<String>();

    static {
        ignoreMethods.add("wait");
        ignoreMethods.add("equals");
        ignoreMethods.add("toString");
        ignoreMethods.add("hashCode");
        ignoreMethods.add("getClass");
        ignoreMethods.add("notify");
        ignoreMethods.add("notifyAll");
    }

    private RestConsumerClient restClient;

    private String beanName;
    private Class<?> proxyClass;
    private Object proxyObject;

    public RestConsumerAware(String beanName, Class<?> proxyClass, RestConsumerClient restClient) {
        MessageUtil.checkEmpty(beanName, "beanName");
        MessageUtil.checkEmpty(proxyClass, "proxyClass");
        MessageUtil.checkEmpty(restClient, "restClient");

        if (!proxyClass.isInterface()) {
            throw new ServiceException(" proxy class '" + proxyClass.getName() + "' must be interface ! ");
        }
        this.beanName = beanName;
        this.proxyClass = proxyClass;
        this.restClient = restClient;
        createProxyObject();
    }

    protected void createProxyObject() {
        Class<?> type = getProxyClass();
        Object proxyObj = Proxy.newProxyInstance(type.getClassLoader(), new Class[] {type }, new RestConsumerHandler(this));
        setProxyObject(proxyObj);
    }

    protected void setProxyObject(Object proxyObject) {
        this.proxyObject = proxyObject;
    }

    /**
     * 获取服务名
     * 
     * @return
     */
    public String getBeanName() {
        return this.beanName;
    }

    /**
     * 获取代理对象
     * 
     * @return
     */
    public Object getProxyObject() {
        return this.proxyObject;
    }

    /**
     * 获取代理客户端类名
     * 
     * @return
     */
    public Class<?> getProxyClass() {
        return this.proxyClass;
    }

    /**
     * 获取rest客户服务对象
     * 
     * @return
     */
    public RestConsumerClient getRestClient() {
        return restClient;
    }

}
