package com.uinnova.product.eam.service.impl;

import com.binary.framework.web.RemoteResult;
import com.binary.jdbc.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.uinnova.product.eam.base.exception.ServerException;
import com.uinnova.product.eam.model.WorkbenchChargeDoneDto;
import com.uinnova.product.eam.model.cj.dto.PlanDesignInstanceDTO;
import com.uinnova.product.eam.comm.exception.BusinessException;
import com.uinnova.product.eam.comm.model.es.EamNoticeKeyword;
import com.uinnova.product.eam.comm.model.es.WorkbenchChargeDone;
import com.uinnova.product.eam.feign.workable.FlowableFeign;
import com.uinnova.product.eam.feign.workable.entity.TaskResponse;
import com.uinnova.product.eam.model.cj.domain.PlanDesignInstance;
import com.uinnova.product.eam.model.constants.FlowableConstant;
import com.uinnova.product.eam.model.vo.WorkbenchBO;
import com.uinnova.product.eam.model.vo.WorkbenchChargeDoneVO;
import com.uinnova.product.eam.service.ICISwitchSvc;
import com.uinnova.product.eam.service.WorkbenchChargeDoneSvc;
import com.uinnova.product.eam.service.cj.service.PlanDesignInstanceService;
import com.uinnova.product.eam.service.es.WorkbenchChargeDoneDao;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uino.bean.cmdb.base.LibType;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.permission.business.UserInfo;
import com.uino.bean.permission.query.CSysUser;
import com.uino.dao.permission.ESUserSvc;
import com.uino.dao.util.ESUtil;
import com.uino.service.permission.microservice.IUserSvc;
import com.uino.util.sys.SysUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.sort.SortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Service
@Slf4j
public class WorkbenchChargeDoneSvcImpl implements WorkbenchChargeDoneSvc {

    @Autowired
    private WorkbenchChargeDoneDao workbenchChargeDoneDao;
    
    @Value("${oauth.server.systemName:架构管理平台}")
	private String systemName;
    
    @Value("${oauth.server.systemUrl:127.0.0.1/ea}")
	private String systemUrl;

    @Resource
    private PlanDesignInstanceService planDesignInstanceService;
    @Autowired
    private FlowableFeign flowableFeign;

    @Autowired
    private ESUserSvc userSvc;

    @Resource
    private IUserSvc iuserSvc;

    @Resource
    private ICISwitchSvc ciSwitchSvc;

    private static final String TAG_PLAN = "plan";
    private static final String TAG_DIAGRAM = "diagram";

    private static final String SOURCE_TYPE = "sourceType";
    private static final String PLAN_ID = "planId";

    @Override
    public Boolean saveOrUpdate(List<WorkbenchChargeDone> workbenchChargeDoneList) {
        if (workbenchChargeDoneList == null || workbenchChargeDoneList.size() < 1) {
            return true;
        }
        List<String> businessIdList = workbenchChargeDoneList.stream().filter(item -> item.getBusinessId() != null).map(item -> item.getBusinessId()).distinct().collect(Collectors.toList());
        List<String> userId = workbenchChargeDoneList.stream().filter(item -> item.getUserId() != null).map(item -> item.getUserId()).distinct().collect(Collectors.toList());
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termsQuery("userId.keyword",userId));
        boolQueryBuilder.must(QueryBuilders.termsQuery("businessId.keyword",businessIdList));
        List<WorkbenchChargeDone> queryList = workbenchChargeDoneDao.getListByQuery(boolQueryBuilder);
        if (queryList == null || queryList.size() < 1) {
            //全量保存
            for (WorkbenchChargeDone workbenchChargeDone : workbenchChargeDoneList) {
                workbenchChargeDone.setId(ESUtil.getUUID());
                workbenchChargeDone.setDomainId(1l);
                workbenchChargeDone.setCreateTime(ESUtil.getNumberDateTime());
                workbenchChargeDone.setModifyTime(ESUtil.getNumberDateTime());
            }
            workbenchChargeDoneDao.saveOrUpdateBatch(workbenchChargeDoneList);
        }else{
            //幂等性处理在保存
            List<WorkbenchChargeDone> insertList = Lists.newArrayList();
            List<String> filterBusinessIdList = queryList.stream().map(item -> item.getBusinessId()).collect(Collectors.toList());
            for (WorkbenchChargeDone workbenchChargeDone : workbenchChargeDoneList) {
                if (!filterBusinessIdList.contains(workbenchChargeDone.getBusinessId())) {
                    insertList.add(workbenchChargeDone);
                }
            }
            if (!CollectionUtils.isEmpty(insertList)) {
                for (WorkbenchChargeDone workbenchChargeDone : insertList) {
                    workbenchChargeDone.setId(ESUtil.getUUID());
                    workbenchChargeDone.setDomainId(1l);
                    workbenchChargeDone.setCreateTime(ESUtil.getNumberDateTime());
                    workbenchChargeDone.setModifyTime(ESUtil.getNumberDateTime());
                }
                workbenchChargeDoneDao.saveOrUpdateBatch(insertList);
            }
        }
        return true;
    }

    @Override
    public boolean changeAction(String businessId) {
        WorkbenchChargeDone workbenchChargeDone = new WorkbenchChargeDone();
        workbenchChargeDone.setBusinessId(businessId);
        List<WorkbenchChargeDone> queryList = workbenchChargeDoneDao.getListByCdt(workbenchChargeDone);
        if (queryList == null || queryList.size() < 1) {
          return false;
        }
        for (WorkbenchChargeDone chargeDone : queryList) {
            if (chargeDone.getType()==5) {
                CcCiInfo ciInfoById = ciSwitchSvc.getCiInfoById(Long.parseLong(businessId), LibType.DESIGN);
                CcCiInfo ciInfoById1 = ciSwitchSvc.getCiInfoById(Long.parseLong(businessId), LibType.PRIVATE);
                if (ciInfoById == null && ciInfoById1 == null) {
                    if (chargeDone.getAction() == 1 || chargeDone.getAction() == 3) {
                        chargeDone.setAction(2);
                        chargeDone.setModifyTime(ESUtil.getNumberDateTime());
                        chargeDone.setTaskEndTime(new Date());
                        workbenchChargeDoneDao.saveOrUpdateBatch(queryList);
                        throw new ServerException("流程条目已删除,无法查看");
                    } else if (chargeDone.getAction() == 2) {
                        throw new ServerException("流程条目已删除,无法查看");
                    }
                }
            }
            if (chargeDone.getAction() == 1 || chargeDone.getAction() == 3) {
                chargeDone.setAction(2);
                chargeDone.setModifyTime(ESUtil.getNumberDateTime());
                chargeDone.setTaskEndTime(new Date());
            }else{
                continue;
            }
        }
        workbenchChargeDoneDao.saveOrUpdateBatch(queryList);
        return true;
    }

    @Override
    public Page<WorkbenchChargeDone> pageQueryList(WorkbenchChargeDoneDto doneDto) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termQuery("action",doneDto.getAction()));
        boolQueryBuilder.must(QueryBuilders.termQuery("userId.keyword",SysUtil.getCurrentUserInfo().getLoginCode()));
        boolQueryBuilder.mustNot(QueryBuilders.termQuery("processDefinitionKey.keyword", FlowableConstant.DECISION_DEFINITION_KEY));
        if(StringUtils.isNotBlank(doneDto.getTitle())){
            boolQueryBuilder.must(QueryBuilders.matchPhraseQuery("processInstanceName", doneDto.getTitle()));
        }
        if(StringUtils.isNotBlank(doneDto.getTypes())){
            String[] types = doneDto.getTypes().split(",");
            boolQueryBuilder.must(QueryBuilders.termsQuery("type",types));
        }
        long count = workbenchChargeDoneDao.countByCondition(boolQueryBuilder);
        Page<WorkbenchChargeDone> queryPage = null;
        if (count == 0) {
            return queryPage;
        }
        if (doneDto.getAction() == 1) {
            List<SortBuilder<?>> sorts = new LinkedList<>();
            sorts.add(SortBuilders.fieldSort("taskCreateTime").order(SortOrder.DESC));
            queryPage = workbenchChargeDoneDao.getSortListByQuery(doneDto.getPageNum(), doneDto.getPageSize(), boolQueryBuilder, sorts);
        }else{
            List<SortBuilder<?>> sorts = new LinkedList<>();
            sorts.add(SortBuilders.fieldSort("taskEndTime").order(SortOrder.DESC));
            queryPage = workbenchChargeDoneDao.getSortListByQuery(doneDto.getPageNum(), doneDto.getPageSize(), boolQueryBuilder, sorts);
        }

        if (queryPage == null || queryPage.getData() == null) {
            return new Page<WorkbenchChargeDone>();
        }

        List<WorkbenchChargeDone> data = queryPage.getData();

        //转换用户信息
        convertTaskUserName(data);
        //处理已办“待处理人”
        dealDoneTaskCurrentAssignees(data);
        queryPage.setData(data);
        return queryPage;
    }

    private void dealDoneTaskCurrentAssignees(List<WorkbenchChargeDone> datas) {
        List<String> processInstanceIds = datas.stream()
                .filter(data -> data.getType() != null && data.getType() != 2)
                .filter(data -> data.getProcessInstanceId() != null)
                .map(WorkbenchChargeDone::getProcessInstanceId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(processInstanceIds)) {
            return;
        }
        List<TaskResponse> taskResponses = flowableFeign.getDoneTaskList(SysUtil.getCurrentUserInfo().getLoginCode(), processInstanceIds);
        Set<String> userIdSet = Sets.newHashSet();
        for (TaskResponse taskRespons : taskResponses) {
            userIdSet.add(taskRespons.getUserId());
            userIdSet.add(taskRespons.getSubmitter());
            if (StringUtils.isNotBlank(taskRespons.getCurrentAssignees()) && !"-".equals(taskRespons.getCurrentAssignees())) {
                String[] split = taskRespons.getCurrentAssignees().split(",");
                userIdSet.addAll(Arrays.asList(split));
            }
        }
        Map<String, SysUser> sysUserMap = getStringSysUserMap(userIdSet);
        Map<String, String> processIdWithCurrentAssingees = new ConcurrentHashMap<>();
        Map<String, String> processIdWithCategory = new ConcurrentHashMap<>();
        for (TaskResponse taskRespons : taskResponses) {
            if (StringUtils.isNotBlank(taskRespons.getCurrentAssignees()) && !"-".equals(taskRespons.getCurrentAssignees())) {
                String[] split = taskRespons.getCurrentAssignees().split(",");
                Set<String> collect = Arrays.stream(split).filter(s -> sysUserMap.get(s) != null)
                        .map(s -> sysUserMap.get(s).getUserName()).collect(Collectors.toSet());
                taskRespons.setCurrentAssignees(StringUtils.join(collect, ","));
            }
            String key = taskRespons.getProcessInstanceId() + taskRespons.getTaskDefinitionKey();
            processIdWithCurrentAssingees.put(key, taskRespons.getCurrentAssignees());
            processIdWithCategory.put(key, taskRespons.getCategory());
        }
        for (WorkbenchChargeDone data : datas) {
            if (StringUtils.isBlank(data.getProcessInstanceId())) {
                continue;
            }
            String key = data.getProcessInstanceId() + data.getTaskDefinitionKey();
            if (processIdWithCurrentAssingees.containsKey(key)) {
                String currentAssingees = processIdWithCurrentAssingees.get(key);
                data.setCurrentAssignees("-".equals(currentAssingees) ? "流程结束" : currentAssingees);
                data.setCategory(processIdWithCategory.get(key));
            }
        }
    }

    @Override
    public RemoteResult transform(String businessId,String transId,Integer dirType) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termQuery("businessId.keyword",businessId));
        List<WorkbenchChargeDone> workbenchChargeDoneList = workbenchChargeDoneDao.getListByQuery(boolQueryBuilder);
        WorkbenchChargeDone workbenchChargeDone = workbenchChargeDoneList.get(0);



        List<EamNoticeKeyword> keywords = workbenchChargeDone.getKeywords();
        String transPlanId = null;
        for (EamNoticeKeyword keyword : keywords) {
            Map<String, Object> params = keyword.getParams();
            if (Integer.valueOf(params.get(SOURCE_TYPE).toString()) == 0) {
                continue;
            }
            try {
                Long.valueOf(params.get(PLAN_ID).toString());
                continue;
            } catch (Exception e) {}
            String businssKey = params.get(PLAN_ID).toString();
            if (!transId.equals(businssKey)) {
                continue;
            }
            //通过businessKey + dirType获取最新版本方案
            PlanDesignInstanceDTO planInstance = planDesignInstanceService.getAssetsLatestPlan(businssKey, null);
            if (planInstance == null) {
                return new RemoteResult(false, 30001, "方案已被删除");
            }
            transPlanId = planInstance.getId().toString();
        }
        return new RemoteResult(transPlanId);
    }

    @Override
    public WorkbenchBO getCount() {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termQuery("userId.keyword",SysUtil.getCurrentUserInfo().getLoginCode()));
        List<WorkbenchChargeDone> queryList = workbenchChargeDoneDao.getListByQuery(boolQueryBuilder);
        if (queryList == null || queryList.size() < 1) {
            return new WorkbenchBO();
        }
        WorkbenchBO workbenchBO = new WorkbenchBO();
        List<WorkbenchChargeDone> todoList = queryList.stream().filter(item -> item.getAction().equals(1)).collect(Collectors.toList());
        List<WorkbenchChargeDone> doList = queryList.stream().filter(item -> item.getAction().equals(2)).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(todoList)) {
            workbenchBO.setTodoCount(todoList.size());
        }
        if (!CollectionUtils.isEmpty(doList)) {
            workbenchBO.setDoneCount(doList.size());
        }
        return workbenchBO;
    }
    
    @Override
    public Map<String, Object> todoCount(String loginCode) {
    	UserInfo user = iuserSvc.getUserInfoByLoginCode(loginCode);
    	if (user == null) {
            throw new BusinessException("用户编码loginCode不正确");
        }
    	LinkedHashMap< String, Object> map = new LinkedHashMap<String, Object>();
    	BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termQuery("userId.keyword",loginCode));
        boolQueryBuilder.must(QueryBuilders.termQuery("action",1));
        List<WorkbenchChargeDone> todoList = workbenchChargeDoneDao.getListByQuery(boolQueryBuilder);
        map.put("name", systemName);
    	map.put("systemurl", systemUrl);
    	map.put("count", 0);
        if (!CollectionUtils.isEmpty(todoList)) {
        	map.put("count", todoList.size());
        	return map;
        }
    	return map;
    }

    @Override
    public boolean updateInfoStatus(Long planId, String diagramId, Integer type) {
        String planIdString = null;
        PlanDesignInstanceDTO planForFeign = null;
        PlanDesignInstance planDesignInstance = planDesignInstanceService.getPlanDesignInstance(planId);
        if (planDesignInstance != null) {
            planForFeign = new PlanDesignInstanceDTO();
            BeanUtils.copyProperties(planDesignInstance, planForFeign);
        }
        if (planForFeign != null && planForFeign.getAssetsType() == 1) {
            planIdString = planForFeign.getId().toString();
        }
        if (planForFeign != null && planForFeign.getAssetsType() == 2) {
            planIdString = planForFeign.getBusinessKey();
        }
        if (type == 1) {
            //表示全部更新
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
            boolQueryBuilder.must(QueryBuilders.termQuery("planId.keyword",planIdString));
            boolQueryBuilder.must(QueryBuilders.termQuery("action",1));
            boolQueryBuilder.must(QueryBuilders.termQuery("userId.keyword",SysUtil.getCurrentUserInfo().getLoginCode()));
            List<WorkbenchChargeDone> queryList = workbenchChargeDoneDao.getListByQuery(boolQueryBuilder);
            for (WorkbenchChargeDone workbenchChargeDone : queryList) {
                workbenchChargeDone.setAction(2);
                workbenchChargeDone.setTaskEndTime(new Date());
            }
            workbenchChargeDoneDao.saveOrUpdateBatch(queryList);
        }else{
            //只更新单图的
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
            boolQueryBuilder.must(QueryBuilders.termQuery("planId.keyword",planIdString));
            boolQueryBuilder.must(QueryBuilders.termQuery("action",1));
            boolQueryBuilder.must(QueryBuilders.termQuery("userId.keyword",SysUtil.getCurrentUserInfo().getLoginCode()));
            boolQueryBuilder.must(QueryBuilders.wildcardQuery("diagramIds.keyword",diagramId));
            List<WorkbenchChargeDone> queryList = workbenchChargeDoneDao.getListByQuery(boolQueryBuilder);
            for (WorkbenchChargeDone workbenchChargeDone : queryList) {
                workbenchChargeDone.setAction(2);
                workbenchChargeDone.setTaskEndTime(new Date());
            }
            workbenchChargeDoneDao.saveOrUpdateBatch(queryList);
        }
        return true;
    }

    @Override
    public void deleteWorkbenchChargeDone(WorkbenchChargeDoneVO workbenchChargeDoneVO) {
        List<Long> deleteDafPlanIdList = workbenchChargeDoneVO.getDeleteDafPlanIdList();
        List<String> deletePublishPlanIdList = workbenchChargeDoneVO.getDeletePublishPlanIdList();
        if (!CollectionUtils.isEmpty(deleteDafPlanIdList)) {
            List<String> changeDeleteDafPlanIdList = deleteDafPlanIdList.stream().map(item -> item + "").collect(Collectors.toList());
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
            boolQueryBuilder.must(QueryBuilders.termsQuery("planId.keyword",changeDeleteDafPlanIdList));
            workbenchChargeDoneDao.deleteByQuery(boolQueryBuilder,true);
        }

        if (!CollectionUtils.isEmpty(deletePublishPlanIdList)) {
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
            boolQueryBuilder.must(QueryBuilders.termsQuery("planId.keyword",deletePublishPlanIdList));
            workbenchChargeDoneDao.deleteByQuery(boolQueryBuilder,true);
        }

    }

    @Override
    public void deleteWorkbenchPublishedChargeDone(String businessKey,Integer dirType) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termQuery("planId.keyword",businessKey));
        workbenchChargeDoneDao.deleteByQuery(boolQueryBuilder,true);
    }

    @Override
    public boolean deleteByCondition(WorkbenchChargeDone workbenchChargeDone) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        if (StringUtils.isNotEmpty(workbenchChargeDone.getProcessInstanceId())) {
            boolQueryBuilder.must(QueryBuilders.termQuery("processInstanceId.keyword",workbenchChargeDone.getProcessInstanceId()));
        }else{
            throw new BusinessException("流程实例已经删除请刷新");
        }
        boolQueryBuilder.must(QueryBuilders.termQuery("action",1));
        workbenchChargeDoneDao.deleteByQuery(boolQueryBuilder,true);
        return true;
    }

    @Override
    public Boolean batchModifyWorkbenchTask(String processInstanceId,String businessKey) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        if (StringUtils.isNotEmpty(processInstanceId)) {
            boolQueryBuilder.must(QueryBuilders.termQuery("processInstanceId.keyword",processInstanceId));
            if(StringUtils.isNotEmpty(businessKey)){
                boolQueryBuilder.must(QueryBuilders.termQuery("businessKey.keyword",businessKey));
            }
        }else{
            log.info("工作台没有相关流程实例的代办");
        }
        boolQueryBuilder.must(QueryBuilders.termQuery("action",1));
        List<WorkbenchChargeDone> workbenchChargeDoneList = workbenchChargeDoneDao.getListByQuery(boolQueryBuilder);
        for (WorkbenchChargeDone workbenchChargeDone : workbenchChargeDoneList) {
            workbenchChargeDone.setAction(3);
            workbenchChargeDone.setModifyTime(ESUtil.getNumberDateTime());
            workbenchChargeDone.setTaskEndTime(new Date());
        }
        workbenchChargeDoneDao.saveOrUpdateBatch(workbenchChargeDoneList);
        return true;
    }

    private void convertTaskUserName(List<WorkbenchChargeDone> WorkbenchChargeDoneList) {
        Set<String> userIdSet = Sets.newHashSet();
        for (WorkbenchChargeDone workbenchChargeDone : WorkbenchChargeDoneList) {
            if (workbenchChargeDone.getType() != null) {
                if (workbenchChargeDone.getType() == 2) {
                    continue;
                }
                userIdSet.add(workbenchChargeDone.getUserId());
                userIdSet.add(workbenchChargeDone.getSubmitter());
                if (StringUtils.isNotBlank(workbenchChargeDone.getCurrentAssignees()) && !"-".equals(workbenchChargeDone.getCurrentAssignees())) {
                    String[] split = workbenchChargeDone.getCurrentAssignees().split(",");
                    userIdSet.addAll(Arrays.asList(split));
                }
            }
        }
        Map<String, SysUser> sysUserMap = getStringSysUserMap(userIdSet);
        for (WorkbenchChargeDone workbenchChargeDone : WorkbenchChargeDoneList) {
            if (workbenchChargeDone.getType() != null) {
                if (workbenchChargeDone.getType() == 2) {
                    continue;
                }
                if (sysUserMap.get(workbenchChargeDone.getUserId()) != null) {
                    workbenchChargeDone.setUserId(sysUserMap.get(workbenchChargeDone.getUserId()).getUserName());
                }
                if (sysUserMap.get(workbenchChargeDone.getSubmitter()) != null) {
                    workbenchChargeDone.setSubmitter(sysUserMap.get(workbenchChargeDone.getSubmitter()).getUserName());
                }
                if (StringUtils.isNotBlank(workbenchChargeDone.getCurrentAssignees()) && !"-".equals(workbenchChargeDone.getCurrentAssignees())) {
                    String[] split = workbenchChargeDone.getCurrentAssignees().split(",");
                    List<String> collect = Arrays.stream(split).filter(s -> sysUserMap.get(s) != null)
                            .map(s -> sysUserMap.get(s).getUserName()).collect(Collectors.toList());
                    workbenchChargeDone.setCurrentAssignees(StringUtils.join(collect, ","));
                }
            }
        }
    }

    private Map<String, SysUser> getStringSysUserMap(Set<String> userIdSet) {
        if (userIdSet == null || userIdSet.size() < 1) {
            return new HashMap<String, SysUser>();
        }
        Map<String, SysUser> sysUserMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(userIdSet)) {
            String[] userIds = new String[userIdSet.size()];
            userIdSet.toArray(userIds);
            CSysUser cSysUser = new CSysUser();
            cSysUser.setLoginCodes(userIds);
            int pageSize = userIdSet.size();
            if (pageSize > 20000) {
                pageSize = 20000;
            }
            Page<SysUser> userPage = userSvc.getListByCdt(1, pageSize, cSysUser);
            if (userPage != null && !CollectionUtils.isEmpty(userPage.getData())) {
                sysUserMap = userPage.getData().parallelStream().collect(Collectors.toMap(SysUser::getLoginCode, u -> u));
            }
        }
        return sysUserMap;
    }
}
