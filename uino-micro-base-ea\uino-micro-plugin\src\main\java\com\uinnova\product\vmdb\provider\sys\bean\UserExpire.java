package com.uinnova.product.vmdb.provider.sys.bean;

import com.alibaba.fastjson.JSONObject;

public class UserExpire {

	private Long id;
	private Long domainId;
	private String loginCode;
	private Long setPwdTime;
	
	public UserExpire() {
		
	}
	
	public UserExpire(JSONObject json) {
		if (json.containsKey("id") && json.getLong("id")!=null) {
			this.id = json.getLong("id");
		}
		if (json.containsKey("domainId") && json.getLong("domainId")!=null) {
			this.domainId = json.getLong("domainId");
		}
		if (json.containsKey("loginCode") && json.getString("loginCode")!=null && !"".equals(json.getString("loginCode").trim())) {
			this.loginCode = json.getString("loginCode").trim();
		}
		if (json.containsKey("setPwdTime") && json.getLong("setPwdTime")!=null) {
			this.setPwdTime = json.getLong("setPwdTime");
		}
	}
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getDomainId() {
		return domainId;
	}
	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}
	public String getLoginCode() {
		return loginCode;
	}
	public void setLoginCode(String loginCode) {
		this.loginCode = loginCode;
	}
	public Long getSetPwdTime() {
		return setPwdTime;
	}
	public void setSetPwdTime(Long setPwdTime) {
		this.setPwdTime = setPwdTime;
	}
	
	public JSONObject toJson() {
		JSONObject json = new JSONObject();
		json.put("id", this.id);
		json.put("domainId", this.domainId);
		json.put("loginCode", this.loginCode);
		json.put("setPwdTime", this.setPwdTime);
		return json;
	}
}
