package com.uino.bean.tp.enums;

import io.swagger.annotations.ApiModel;

/**
 * tp规则类型
 *
 * <AUTHOR>
 * @create: 2020/06/09 14:56
 **/
@ApiModel(value = "tp规则类型")
public enum TpRuleTypeEnum {
    /**
     * 标签丰富
     */
    LABEL_RICH,
    /**
     * 普通阈值
     */
    THRESHOLD,
    /**
     * 衍生指标
     */
    NEW_METRIC,
    /**
     * 指标合并
     */
    MERGE_METRIC,
    /**
     * 指标萃取
     */
    EXTRACT_METRIC,
    /**
     * 标签扩展
     */
    METRIC_EXTEND,
    /**
     * 孪生-联动服务-触发条件
     */
    DIGITAL_TWIN_TRIGGER,
    /**
     * 孪生-联动服务-判断条件
     */
    DIGITAL_TWIN_JUDGE
}
