package com.uinnova.product.eam.comm.model;




import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;


@Comment("视图元素表[VC_DIAGRAM_ELE]")
public class VcDiagramEle implements EntityBean {
	private static final long serialVersionUID = 1L;


	@Comment("ID[ID]")
	private Long id;


	@Comment("视图ID[DIAGRAM_ID]")
	private Long diagramId;


	@Comment("元素类型[ELE_TYPE]    元素类型:1=CI 2=标签 3=搜索表达式")
	private Integer eleType;


	@Comment("元素ID[ELE_ID]")
	private String eleId;


	@Comment("元素ciCode[ELE_CODE]")
	private String eleCode;


	@Comment("关联视图[RLT_DIAGRAM_IDS]    多个以逗号分隔")
	private String rltDiagramIds;


	@Comment("所属域[DOMAIN_ID]")
	private Long domainId;


	@Comment("创建时间[CREATE_TIME]    创建时间:yyyyMMddHHmmss")
	private Long createTime;


	@Comment("修改时间[MODIFY_TIME]    修改时间:yyyyMMddHHmmss")
	private Long modifyTime;




	public Long getId() {
		return this.id;
	}
	public void setId(Long id) {
		this.id = id;
	}


	public Long getDiagramId() {
		return this.diagramId;
	}
	public void setDiagramId(Long diagramId) {
		this.diagramId = diagramId;
	}


	public Integer getEleType() {
		return this.eleType;
	}
	public void setEleType(Integer eleType) {
		this.eleType = eleType;
	}


	public String getEleId() {
		return this.eleId;
	}
	public void setEleId(String eleId) {
		this.eleId = eleId;
	}


	public String getEleCode() {
		return this.eleCode;
	}
	public void setEleCode(String eleCode) {
		this.eleCode = eleCode;
	}


	public String getRltDiagramIds() {
		return this.rltDiagramIds;
	}
	public void setRltDiagramIds(String rltDiagramIds) {
		this.rltDiagramIds = rltDiagramIds;
	}


	public Long getDomainId() {
		return this.domainId;
	}
	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}


	public Long getCreateTime() {
		return this.createTime;
	}
	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}


	public Long getModifyTime() {
		return this.modifyTime;
	}
	public void setModifyTime(Long modifyTime) {
		this.modifyTime = modifyTime;
	}


}


