package com.uino.api.init;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * 微服务化部署扫描类
 *
 * <AUTHOR>
 */
@ComponentScan(basePackages = { "com.uino.api.client.*.rpc", "com.uino.provider.feign" })
@Configuration
@EnableFeignClients(basePackages = { "com.uino.provider.feign" })
@ConditionalOnProperty(prefix = "base", name = "load-type", havingValue = "rpc")
@Slf4j
public class RpcRunConfig {

    static {
        log.info("发现spring-boot配置为rpc加载");
    }
}
