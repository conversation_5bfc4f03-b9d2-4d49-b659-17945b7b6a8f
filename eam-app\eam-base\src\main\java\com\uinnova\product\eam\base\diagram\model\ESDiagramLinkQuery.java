package com.uinnova.product.eam.base.diagram.model;


import com.alibaba.fastjson.annotation.JSONField;
import com.binary.framework.bean.EntityBean;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description //视图链接dto
 * @Date 16:26 2021/6/3
 * @Param
 * @return
 **/
@Data
public class ESDiagramLinkQuery implements EntityBean {
	private static final long serialVersionUID = 1L;

	@JSONField(name = "id")
	private Long id;
	private Long[] ids;
	@JSONField(name = "链接所属的视图id")
	private Long diagramId;
	private Long[] diagramIds;
	@JSONField(name = "链接所属的sheetid")
	private Long sheetId;
	private Long[] sheetIds;
	@JSONField(name = "category")
	private String category;
	@JSONField(name = "childShapeId")
	private Integer childShapeId;
	@JSONField(name = "corner")
	private Integer corner;
	@JSONField(name = "curve")
	private String curve;
	@JSONField(name = "curviness")
	private Integer curviness;
	@JSONField(name = "fontColor")
	private String fontColor;
	@JSONField(name = "fontFamily")
	private String fontFamily;
	@JSONField(name = "fontSize")
	private Integer fontSize;
	@JSONField(name = "fontStyle")
	private String fontStyle;
	@JSONField(name = "fontWeight")
	private String fontWeight;
	@JSONField(name = "from")
	private String from;
	@JSONField(name = "fromArrow")
	private String fromArrow;
	@JSONField(name = "fromArrowOutline")
	private String fromArrowOutline;
	@JSONField(name = "fromEndSegmentLength")
	private Integer fromEndSegmentLength;
	@JSONField(name = "fromInner")
	private Boolean fromInner;
	@JSONField(name = "fromSpot")
	private String fromSpot;
	@JSONField(name = "initialShapeName")
	private String initialShapeName;
	@JSONField(name = "isLockNodeRatio")
	private Boolean isLockNodeRatio;
	@JSONField(name = "isUnderline")
	private Boolean isUnderline;
	@JSONField(name = "key")
	private String key;
	@JSONField(name = "layerName")
	private String layerName;
	@JSONField(name = "linkItems")
	private List<LinkItemsDTO> linkItems;
	@JSONField(name = "lock")
	private Boolean lock;
	@JSONField(name = "points")
	private List<Integer> points;
	@JSONField(name = "reshaped")
	private Boolean reshaped;
	@JSONField(name = "routing")
	private String routing;
	@JSONField(name = "shapeParentName")
	private String shapeParentName;
	@JSONField(name = "stroke")
	private String stroke;
	@JSONField(name = "strokeDashArray")
	private List<Integer> strokeDashArray;
	@JSONField(name = "strokeWidth")
	private Integer strokeWidth;
	@JSONField(name = "to")
	private String to;
	@JSONField(name = "toArrow")
	private String toArrow;
	@JSONField(name = "toArrowOutline")
	private Boolean toArrowOutline;
	@JSONField(name = "toEndSegmentLength")
	private Integer toEndSegmentLength;
	@JSONField(name = "toInner")
	private Boolean toInner;
	@JSONField(name = "toSpot")
	private String toSpot;
	@JSONField(name = "zOrder")
	private Integer zOrder;
	private Long createTime;
	private Long modifyTime;


	public static class LinkItemsDTO {
		@JSONField(name = "fontColor")
		private String fontColor;
		@JSONField(name = "label")
		private String label;
		@JSONField(name = "segmentFraction")
		private Double segmentFraction;
		@JSONField(name = "segmentOffset")
		private String segmentOffset;

		public String getFontColor() {
			return fontColor;
		}

		public void setFontColor(String fontColor) {
			this.fontColor = fontColor;
		}

		public String getLabel() {
			return label;
		}

		public void setLabel(String label) {
			this.label = label;
		}

		public Double getSegmentFraction() {
			return segmentFraction;
		}

		public void setSegmentFraction(Double segmentFraction) {
			this.segmentFraction = segmentFraction;
		}

		public String getSegmentOffset() {
			return segmentOffset;
		}

		public void setSegmentOffset(String segmentOffset) {
			this.segmentOffset = segmentOffset;
		}
	}

}


