package com.uinnova.product.eam.db.impl;


import com.binary.framework.dao.DaoDefinition;

import com.uinnova.product.eam.comm.model.CVcDiagramCiAttrDisp;
import com.uinnova.product.eam.comm.model.VcDiagramCiAttrDisp;


/**
 * 视图CI属性显示表[VC_DIAGRAM_CI_ATTR_DISP]数据访问对象定义实现
 */
public class VcDiagramCiAttrDispDaoDefinition implements DaoDefinition<VcDiagramCiAttrDisp, CVcDiagramCiAttrDisp> {


	@Override
	public Class<VcDiagramCiAttrDisp> getEntityClass() {
		return VcDiagramCiAttrDisp.class;
	}


	@Override
	public Class<CVcDiagramCiAttrDisp> getConditionClass() {
		return CVcDiagramCiAttrDisp.class;
	}


	@Override
	public String getTableName() {
		return "VC_DIAGRAM_CI_ATTR_DISP";
	}


	@Override
	public boolean hasDataStatusField() {
		return false;
	}


	@Override
	public void setDataStatusValue(VcDiagramCiAttrDisp record, int status) {
	}


	@Override
	public void setDataStatusValue(CVcDiagramCiAttrDisp cdt, int status) {
	}


	@Override
	public void setCreatorValue(VcDiagramCiAttrDisp record, String creator) {
	}


	@Override
	public void setModifierValue(VcDiagramCiAttrDisp record, String modifier) {
	}


}


