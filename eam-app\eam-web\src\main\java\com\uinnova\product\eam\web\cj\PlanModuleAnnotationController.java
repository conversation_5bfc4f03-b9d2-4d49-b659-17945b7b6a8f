package com.uinnova.product.eam.web.cj;

import com.uinnova.product.eam.base.cj.ResultMsg;
import com.uinnova.product.eam.model.cj.group.AddGroup;
import com.uinnova.product.eam.model.cj.group.QueryGroup;
import com.uinnova.product.eam.model.cj.request.PlanModuleAnnotationRequest;
import com.uinnova.product.eam.model.constants.PathConstants;
import com.uinnova.product.eam.service.cj.service.PlanModuleAnnotationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.Map;


/**
 * 方案动态 - 批注 controller
 *
 * <AUTHOR>
 * @since 2022-3-1 20:40:43
 */
@RestController
@RequestMapping(PathConstants.PLAN_ANNOTATION_START_PATH)
@Slf4j
public class PlanModuleAnnotationController {

    @Resource
    private PlanModuleAnnotationService planModuleAnnotationService;


    /**
     * 批注新增
     */
    @PostMapping("saveAnnotation")
    public ResultMsg saveAnnotation(@RequestBody @Validated(AddGroup.class) PlanModuleAnnotationRequest request) {
        Long id = planModuleAnnotationService.saveAnnotation(request);
        return new ResultMsg(id);
    }

    /**
     * 批注修改
     **/
    @PostMapping("list")
    public ResultMsg list(@RequestBody @Validated(QueryGroup.class) PlanModuleAnnotationRequest request) {

       Map<String, Object> vos = planModuleAnnotationService.list(request);
        return new ResultMsg(vos);
    }

}

