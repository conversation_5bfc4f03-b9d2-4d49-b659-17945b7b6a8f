package com.uinnova.product.vmdb.comm.model.rule;

import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;

/**
 * 
 * <AUTHOR>
 *
 */
@Comment("标签定义表[CC_CI_TAG_DEF]")
public class CcCiTagDef implements EntityBean {
    private static final long serialVersionUID = 1L;

    @Comment("ID[ID]")
    private Long id;

    @Comment("标签名称[TAG_NAME]")
    private String tagName;

    @Comment("所属目录[DIR_ID]")
    private Long dirId;

    @Comment("标签类型[TAG_TYPE]    1=权限标签 2=视图标签 3=数据标签")
    private Integer tagType;

    @Comment("定义描述[DEF_DESC]")
    private String defDesc;

    @Comment("有效状态[IS_VALID]    1=有效 0=无效")
    private Integer isValid;

    @Comment("无效原因[INV_REASON]")
    private String invReason;

    @Comment("构建状态[BUILD_STATUS]    1=待构建 2=构建中")
    private Integer buildStatus;

    @Comment("所属域[DOMAIN_ID]")
    private Long domainId;

    @Comment("数据状态[DATA_STATUS]    数据状态：1-正常 0-删除")
    private Integer dataStatus;

    @Comment("创建人[CREATOR]")
    private String creator;

    @Comment("修改人[MODIFIER]")
    private String modifier;

    @Comment("创建时间[CREATE_TIME]    yyyyMMddHHmmss")
    private Long createTime;

    @Comment("修改时间[MODIFY_TIME]    yyyyMMddHHmmss")
    private Long modifyTime;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTagName() {
        return this.tagName;
    }

    public void setTagName(String tagName) {
        this.tagName = tagName;
    }

    public Long getDirId() {
        return this.dirId;
    }

    public void setDirId(Long dirId) {
        this.dirId = dirId;
    }

    public Integer getTagType() {
        return this.tagType;
    }

    public void setTagType(Integer tagType) {
        this.tagType = tagType;
    }

    public String getDefDesc() {
        return this.defDesc;
    }

    public void setDefDesc(String defDesc) {
        this.defDesc = defDesc;
    }

    public Integer getIsValid() {
        return this.isValid;
    }

    public void setIsValid(Integer isValid) {
        this.isValid = isValid;
    }

    public String getInvReason() {
        return this.invReason;
    }

    public void setInvReason(String invReason) {
        this.invReason = invReason;
    }

    public Integer getBuildStatus() {
        return this.buildStatus;
    }

    public void setBuildStatus(Integer buildStatus) {
        this.buildStatus = buildStatus;
    }

    public Long getDomainId() {
        return this.domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }

    public Integer getDataStatus() {
        return this.dataStatus;
    }

    public void setDataStatus(Integer dataStatus) {
        this.dataStatus = dataStatus;
    }

    public String getCreator() {
        return this.creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getModifier() {
        return this.modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public Long getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getModifyTime() {
        return this.modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }

}
