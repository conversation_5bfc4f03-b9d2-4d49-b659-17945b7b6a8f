package com.uinnova.product.eam.web.asset.bean;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class NonFunctionalMasterVO {
    private Long classId;
    private Long id;
    private String ciCode;
    private String ciVersion;
    private Map<String, String> attrs;
    @Comment("非功能指标信息隐藏字段")
    private List<String> hiddenAttrs;
    private List<String> orderAttribute;
    private Map<String,String> dictInfo;
    private Long totalNum;
}
