package com.uino.bean.cmdb.base.dataset;

/**
 * @Classname OperateType
 * @Description 权限类型
 * @Date 2020/3/18 15:19
 * @Created by uinnova
 */
public enum OperateType {
    //
    Invisible(0),
    Read(1),
    Down(2),
    Write(3);


    private Integer code;

    OperateType(Integer code) {
        this.code = code;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public static OperateType valueOf(Integer code) {
        if (Invisible.code.equals(code)) {
            return Invisible;
        } else if (Read.code.equals(code)) {
            return Read;
        } else if (Down.code.equals(code)) {
            return Down;
        }  else if (Write.code.equals(code)) {
            return Write;
        } else {
            return null;
        }
    }
}
