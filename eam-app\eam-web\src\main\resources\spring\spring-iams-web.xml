<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	   xmlns:context="http://www.springframework.org/schema/context"
	   xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.1.xsd
		  http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.1.xsd">
	<context:component-scan base-package="com.uinnova.product.eam.web.**">
		<context:include-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
	</context:component-scan>

	<bean class="com.uinnova.product.eam.web.diagram.peer.impl.DiagramPeerImpl"/>
	<bean class="com.uinnova.product.eam.web.analog.peer.impl.ExternalDataPeerImpl" />

</beans>
