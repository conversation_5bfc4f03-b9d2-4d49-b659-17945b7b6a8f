package com.uinnova.product.eam.comm.model;




import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;


@Comment("视图收藏表[VC_DIAGRAM_ENSH]")
public class CVcDiagramEnsh implements Condition {
	private static final long serialVersionUID = 1L;


	@Comment("ID[ID] operate-Equal[=]")
	private Long id;


	@Comment("ID[ID] operate-In[in]")
	private Long[] ids;


	@Comment("ID[ID] operate-GTEqual[>=]")
	private Long startId;

	@Comment("ID[ID] operate-LTEqual[<=]")
	private Long endId;


	@Comment("用户ID[USER_ID] operate-Equal[=]")
	private Long userId;


	@Comment("用户ID[USER_ID] operate-In[in]")
	private Long[] userIds;


	@Comment("用户ID[USER_ID] operate-GTEqual[>=]")
	private Long startUserId;

	@Comment("用户ID[USER_ID] operate-LTEqual[<=]")
	private Long endUserId;


	@Comment("视图ID[DIAGRAM_ID] operate-Equal[=]")
	private Long diagramId;


	@Comment("视图ID[DIAGRAM_ID] operate-In[in]")
	private Long[] diagramIds;


	@Comment("视图ID[DIAGRAM_ID] operate-GTEqual[>=]")
	private Long startDiagramId;

	@Comment("视图ID[DIAGRAM_ID] operate-LTEqual[<=]")
	private Long endDiagramId;


	@Comment("排序号[ORDER_NO] operate-Equal[=]")
	private Integer orderNo;


	@Comment("排序号[ORDER_NO] operate-In[in]")
	private Integer[] orderNos;


	@Comment("排序号[ORDER_NO] operate-GTEqual[>=]")
	private Integer startOrderNo;

	@Comment("排序号[ORDER_NO] operate-LTEqual[<=]")
	private Integer endOrderNo;


	@Comment("所属域[DOMAIN_ID] operate-Equal[=]")
	private Long domainId;


	@Comment("所属域[DOMAIN_ID] operate-In[in]")
	private Long[] domainIds;


	@Comment("所属域[DOMAIN_ID] operate-GTEqual[>=]")
	private Long startDomainId;

	@Comment("所属域[DOMAIN_ID] operate-LTEqual[<=]")
	private Long endDomainId;


	@Comment("数据状态[DATA_STATUS] operate-Equal[=]    数据状态:数据状态：1-正常 0-删除")
	private Integer dataStatus;


	@Comment("数据状态[DATA_STATUS] operate-In[in]    数据状态:数据状态：1-正常 0-删除")
	private Integer[] dataStatuss;


	@Comment("数据状态[DATA_STATUS] operate-GTEqual[>=]    数据状态:数据状态：1-正常 0-删除")
	private Integer startDataStatus;

	@Comment("数据状态[DATA_STATUS] operate-LTEqual[<=]    数据状态:数据状态：1-正常 0-删除")
	private Integer endDataStatus;


	@Comment("创建时间[CREATE_TIME] operate-Equal[=]    创建时间:yyyyMMddHHmmss")
	private Long createTime;


	@Comment("创建时间[CREATE_TIME] operate-In[in]    创建时间:yyyyMMddHHmmss")
	private Long[] createTimes;


	@Comment("创建时间[CREATE_TIME] operate-GTEqual[>=]    创建时间:yyyyMMddHHmmss")
	private Long startCreateTime;

	@Comment("创建时间[CREATE_TIME] operate-LTEqual[<=]    创建时间:yyyyMMddHHmmss")
	private Long endCreateTime;


	@Comment("修改时间[MODIFY_TIME] operate-Equal[=]    修改时间:yyyyMMddHHmmss")
	private Long modifyTime;


	@Comment("修改时间[MODIFY_TIME] operate-In[in]    修改时间:yyyyMMddHHmmss")
	private Long[] modifyTimes;


	@Comment("修改时间[MODIFY_TIME] operate-GTEqual[>=]    修改时间:yyyyMMddHHmmss")
	private Long startModifyTime;

	@Comment("修改时间[MODIFY_TIME] operate-LTEqual[<=]    修改时间:yyyyMMddHHmmss")
	private Long endModifyTime;




	public Long getId() {
		return this.id;
	}
	public void setId(Long id) {
		this.id = id;
	}


	public Long[] getIds() {
		return this.ids;
	}
	public void setIds(Long[] ids) {
		this.ids = ids;
	}


	public Long getStartId() {
		return this.startId;
	}
	public void setStartId(Long startId) {
		this.startId = startId;
	}


	public Long getEndId() {
		return this.endId;
	}
	public void setEndId(Long endId) {
		this.endId = endId;
	}


	public Long getUserId() {
		return this.userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}


	public Long[] getUserIds() {
		return this.userIds;
	}
	public void setUserIds(Long[] userIds) {
		this.userIds = userIds;
	}


	public Long getStartUserId() {
		return this.startUserId;
	}
	public void setStartUserId(Long startUserId) {
		this.startUserId = startUserId;
	}


	public Long getEndUserId() {
		return this.endUserId;
	}
	public void setEndUserId(Long endUserId) {
		this.endUserId = endUserId;
	}


	public Long getDiagramId() {
		return this.diagramId;
	}
	public void setDiagramId(Long diagramId) {
		this.diagramId = diagramId;
	}


	public Long[] getDiagramIds() {
		return this.diagramIds;
	}
	public void setDiagramIds(Long[] diagramIds) {
		this.diagramIds = diagramIds;
	}


	public Long getStartDiagramId() {
		return this.startDiagramId;
	}
	public void setStartDiagramId(Long startDiagramId) {
		this.startDiagramId = startDiagramId;
	}


	public Long getEndDiagramId() {
		return this.endDiagramId;
	}
	public void setEndDiagramId(Long endDiagramId) {
		this.endDiagramId = endDiagramId;
	}


	public Integer getOrderNo() {
		return this.orderNo;
	}
	public void setOrderNo(Integer orderNo) {
		this.orderNo = orderNo;
	}


	public Integer[] getOrderNos() {
		return this.orderNos;
	}
	public void setOrderNos(Integer[] orderNos) {
		this.orderNos = orderNos;
	}


	public Integer getStartOrderNo() {
		return this.startOrderNo;
	}
	public void setStartOrderNo(Integer startOrderNo) {
		this.startOrderNo = startOrderNo;
	}


	public Integer getEndOrderNo() {
		return this.endOrderNo;
	}
	public void setEndOrderNo(Integer endOrderNo) {
		this.endOrderNo = endOrderNo;
	}


	public Long getDomainId() {
		return this.domainId;
	}
	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}


	public Long[] getDomainIds() {
		return this.domainIds;
	}
	public void setDomainIds(Long[] domainIds) {
		this.domainIds = domainIds;
	}


	public Long getStartDomainId() {
		return this.startDomainId;
	}
	public void setStartDomainId(Long startDomainId) {
		this.startDomainId = startDomainId;
	}


	public Long getEndDomainId() {
		return this.endDomainId;
	}
	public void setEndDomainId(Long endDomainId) {
		this.endDomainId = endDomainId;
	}


	public Integer getDataStatus() {
		return this.dataStatus;
	}
	public void setDataStatus(Integer dataStatus) {
		this.dataStatus = dataStatus;
	}


	public Integer[] getDataStatuss() {
		return this.dataStatuss;
	}
	public void setDataStatuss(Integer[] dataStatuss) {
		this.dataStatuss = dataStatuss;
	}


	public Integer getStartDataStatus() {
		return this.startDataStatus;
	}
	public void setStartDataStatus(Integer startDataStatus) {
		this.startDataStatus = startDataStatus;
	}


	public Integer getEndDataStatus() {
		return this.endDataStatus;
	}
	public void setEndDataStatus(Integer endDataStatus) {
		this.endDataStatus = endDataStatus;
	}


	public Long getCreateTime() {
		return this.createTime;
	}
	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}


	public Long[] getCreateTimes() {
		return this.createTimes;
	}
	public void setCreateTimes(Long[] createTimes) {
		this.createTimes = createTimes;
	}


	public Long getStartCreateTime() {
		return this.startCreateTime;
	}
	public void setStartCreateTime(Long startCreateTime) {
		this.startCreateTime = startCreateTime;
	}


	public Long getEndCreateTime() {
		return this.endCreateTime;
	}
	public void setEndCreateTime(Long endCreateTime) {
		this.endCreateTime = endCreateTime;
	}


	public Long getModifyTime() {
		return this.modifyTime;
	}
	public void setModifyTime(Long modifyTime) {
		this.modifyTime = modifyTime;
	}


	public Long[] getModifyTimes() {
		return this.modifyTimes;
	}
	public void setModifyTimes(Long[] modifyTimes) {
		this.modifyTimes = modifyTimes;
	}


	public Long getStartModifyTime() {
		return this.startModifyTime;
	}
	public void setStartModifyTime(Long startModifyTime) {
		this.startModifyTime = startModifyTime;
	}


	public Long getEndModifyTime() {
		return this.endModifyTime;
	}
	public void setEndModifyTime(Long endModifyTime) {
		this.endModifyTime = endModifyTime;
	}


}


