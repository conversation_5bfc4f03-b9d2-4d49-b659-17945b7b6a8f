package com.uinnova.product.eam.comm.bean;

import com.binary.framework.bean.annotation.Comment;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

/**
 * 文件夹下协作者信息表
 *
 * <AUTHOR>
 */

public class DirCollaboratorInfo implements Serializable {

    @Comment("表的Id")
    private Long id;

    @Comment("文件夹ID")
    private Long dirId;

    @Comment("协作者Code")
    private String coordinationUserCode;

    @Comment("权限等级")
    private Integer permissionLevel;

    @Comment("文件夹创建者")
    private String creator;

    @Comment("文件修改者")
    private String modifier;

    @Comment("创建时间")
    private Date createTime;

    @Comment("修改时间")
    private Date modifyTime;

    @Comment("领域id")
    private Long domainId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getDirId() {
        return dirId;
    }

    public void setDirId(Long dirId) {
        this.dirId = dirId;
    }


    public String getCoordinationUserCode() {
        return coordinationUserCode;
    }

    public void setCoordinationUserCode(String coordinationUserCode) {
        this.coordinationUserCode = coordinationUserCode;
    }

    public Integer getPermissionLevel() {
        return permissionLevel;
    }

    public void setPermissionLevel(Integer permissionLevel) {
        this.permissionLevel = permissionLevel;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Long getDomainId() {
        return domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }
}
