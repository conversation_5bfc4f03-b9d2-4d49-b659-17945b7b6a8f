package com.uino.web.permission.mvc;

import com.binary.framework.web.RemoteResult;
import com.uino.api.client.permission.IOauthApiSvc;
import com.uino.api.client.permission.IUserApiSvc;
import com.uino.bean.permission.base.OauthResourceDetail;
import com.uino.init.api.ApiResult;
import com.uino.init.api.ApiVersion;
import com.uino.service.cmdb.microservice.IVisualModelSvc;
import com.uino.util.cache.CacheKeyPrefix;
import com.uino.util.cache.ICacheService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.List;

///**
// * oauth控制器
// *
// * <AUTHOR>
// *
// */
@ApiVersion(1)
@Api(value = "oauth控制器", tags = {"oauth控制器"})
@RestController
@RequestMapping(value = "/permission/oauth")
public class Oauth2Mvc {
	@Autowired
	private IOauthApiSvc oauthApi;

	@Autowired
	private IUserApiSvc userApi;

//	@Autowired(required = false)
//	private UinoTokenStore tokenStore;

	@Value("${oauth.client.id:}")
	private String clientId;

    @Autowired
    private ICacheService cacheService;

	@Autowired
	private IVisualModelSvc iVisualModelSvc;

	/**
	 * 获取资源列表
	 *
	 * @param userId
	 * @param request
	 * @param response
	 */
	@ApiOperation("获取资源列表")
	@PostMapping("resource/list")
	public ApiResult<List<OauthResourceDetail>> resourceList(@RequestBody(required = false) Long userId, HttpServletRequest request,
								  HttpServletResponse response) {
		List<OauthResourceDetail> res = oauthApi.list();
		return ApiResult.ok(this).data(res);
	}

	/**
	 * 保存资源端
	 *
	 * @param reqDto
	 * @param request
	 * @param response
	 */
	@ApiOperation("保存资源端")
	@PostMapping("resource/save")
	public ApiResult<Boolean> resourceSave(@RequestBody(required = false) OauthResourceDetail reqDto, HttpServletRequest request,
			HttpServletResponse response) {
		//ControllerUtils.returnJson(request, response, oauthApi.save(reqDto));
		return ApiResult.ok(this).data(oauthApi.save(reqDto));
	}

	/**
	 * 获取资源端详情
	 *
	 * @param name
	 * @param request
	 * @param response
	 */
	@ApiOperation("获取资源端详情")
	@PostMapping("resource/detail")
	public ApiResult<OauthResourceDetail> resourceDetail(@RequestBody(required = false) String name, HttpServletRequest request,
			HttpServletResponse response) {
		OauthResourceDetail res = oauthApi.getResourceDetail(name);
		//ControllerUtils.returnJson(request, response, res);
		return ApiResult.ok(this).data(res);
	}

    @ApiOperation("删除在线用户")
    @GetMapping("resource/cleanOnlineUser")
    public RemoteResult cleanOnlineUser(@RequestParam String loginCode){
        // 删除token清除在线人数
        cacheService.delKey(CacheKeyPrefix.UINO_USER_ONLINE_TOKEN_PREFIX + ":" + loginCode);
		// 清空当前用户的有效的元模型操作记录
		iVisualModelSvc.delValidVisData();
        return new RemoteResult("success");
    }

//	@ModDesc(desc = "跟据用户名、密码获取访问token", pDesc = "用户名、密码", rDesc = "访问token", rType = String.class)
//	@RequestMapping("/login")
//	public void login(HttpServletRequest request, HttpServletResponse response,
//			@RequestBody Map<String, String> param) {
//		String userName = param.get("username");
//		String password = param.get("password");
//		Assert.isTrue(!BinaryUtils.isEmpty(userName) && !BinaryUtils.isEmpty(password), "用户名/密码不可为空");
//		// 获取用户信息
//		CSysUser cdt = new CSysUser();
//		cdt.setLoginCodeEqual(userName);
//		List<SysUser> users = userApi.getSysUserByCdt(cdt);
//		Assert.isTrue(!BinaryUtils.isEmpty(users), "用户不存在");
//		SysUser user = users.get(0);
//		password = SysUtil.EncryptDES.decryptDES(password);
//		Assert.isTrue(!BinaryUtils.isEmpty(password), "密码格式错误");
//		password = DigestUtils.sha256Hex(password);
//		Assert.isTrue(password.equals(user.getLoginPasswd()), "密码错误");
//		// 生成token
//		String userStr = user.getId() + "&" + user.getLoginCode() + "&" + user.getDomainId();
//		userStr = SysUtil.StringUtil.Base64.encry(userStr);
//		String token = UUID.randomUUID().toString() + userStr;
//
//		// token持久化至ES
//		DefaultOAuth2AccessToken accessToken = new DefaultOAuth2AccessToken(token);
//		OAuthClientDetail client = oauthApi.getClientInfoByCode(clientId);
//		OAuth2Request oAuth2Request = new OAuth2Request(null, client.getClientCode(), null, true, client.getScopes(),
//				client.getResourceIds(), null, null, null);
//		OAuth2Authentication authentication = new OAuth2Authentication(oAuth2Request, null);
//		tokenStore.storeAccessToken(accessToken, authentication);
//		ControllerUtils.returnJson(request, response, token);
//	}
}
