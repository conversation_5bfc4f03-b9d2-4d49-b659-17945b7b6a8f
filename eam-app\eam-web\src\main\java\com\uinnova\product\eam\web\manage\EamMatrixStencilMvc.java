package com.uinnova.product.eam.web.manage;

import com.binary.framework.web.RemoteResult;
import com.binary.jdbc.Page;
import com.uinnova.product.eam.comm.dto.EamMatrixQueryVO;
import com.uinnova.product.eam.comm.model.es.EamMatrixStencil;
import com.uinnova.product.eam.service.manage.EamMatrixStencilSvc;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 矩阵制品管理
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/matrix")
public class EamMatrixStencilMvc {

    @Autowired
    private EamMatrixStencilSvc matrixStencilSvc;

    @PostMapping("/base/saveOrUpdate")
    @ModDesc(desc = "保存或更新基本信息", pDesc = "矩阵制品信息", rDesc = "制品id", rType = RemoteResult.class)
    public RemoteResult saveOrUpdate(@RequestBody EamMatrixStencil vo) {
        Long id = matrixStencilSvc.saveOrUpdate(vo);
        return new RemoteResult(id);
    }

    @PostMapping("/stencil/getById")
    @ModDesc(desc = "列表查询", pDesc = "查询条件", rDesc = "矩阵制品列表", rType = RemoteResult.class)
    public RemoteResult getById(@RequestParam Long id) {
        EamMatrixStencil result = matrixStencilSvc.getById(id);
        return new RemoteResult(result);
    }

    @PostMapping("/stencil/deleteById")
    @ModDesc(desc = "删除", pDesc = "矩阵制品id", rDesc = "结果", rType = RemoteResult.class)
    public RemoteResult deleteById(@RequestParam Long id) {
        Integer result = matrixStencilSvc.deleteById(id);
        return new RemoteResult(result);
    }

    @PostMapping("/detail/saveOrUpdate")
    @ModDesc(desc = "保存或更新详细信息", pDesc = "矩阵制品信息", rDesc = "制品id", rType = RemoteResult.class)
    public RemoteResult saveOrUpdateDetail(@RequestBody EamMatrixStencil vo) {
        Long id = matrixStencilSvc.saveOrUpdateDetail(vo);
        return new RemoteResult(id);
    }

    @PostMapping("/update/published")
    @ModDesc(desc = "发布/禁用", pDesc = "矩阵制品信息", rDesc = "制品id", rType = RemoteResult.class)
    public RemoteResult updatePublished(@RequestBody EamMatrixStencil vo) {
        Long id = matrixStencilSvc.updatePublished(vo.getId(), vo.getPublished());
        return new RemoteResult(id);
    }

    @PostMapping("/stencil/queryList")
    @ModDesc(desc = "列表查询", pDesc = "查询条件", rDesc = "矩阵制品列表", rType = RemoteResult.class)
    public RemoteResult queryList(@RequestBody EamMatrixQueryVO dto) {
        Page<EamMatrixStencil> result = matrixStencilSvc.queryList(dto);
        return new RemoteResult(result);
    }

}
