package com.uinnova.product.vmdb.provider.rlt.bean;

import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class FriendInfo implements Serializable {

	private static final long serialVersionUID = 1L;
	
	private List<CcCiClassInfo> ciClassInfos;

    //节点上的Ci,value值为cicode
    private Map<Long, Set<String>> ciNodeMap;

	private List<CiNode> ciNodes;

	private List<CiRltLine> ciRltLines;
	
	private List<FriendPath> friendPaths;
	
	public List<CcCiClassInfo> getCiClassInfos() {
		return ciClassInfos;
	}

	public void setCiClassInfos(List<CcCiClassInfo> ciClassInfos) {
		this.ciClassInfos = ciClassInfos;
	}

    public Map<Long, Set<String>> getCiNodeMap() {
        return ciNodeMap;
    }

    public void setCiNodeMap(Map<Long, Set<String>> ciNodeMap) {
        this.ciNodeMap = ciNodeMap;
    }

    public List<CiNode> getCiNodes() {
		return ciNodes;
	}

	public void setCiNodes(List<CiNode> ciNodes) {
		this.ciNodes = ciNodes;
	}

	public List<CiRltLine> getCiRltLines() {
		return ciRltLines;
	}

	public void setCiRltLines(List<CiRltLine> ciRltLines) {
		this.ciRltLines = ciRltLines;
	}

	public List<FriendPath> getFriendPaths() {
		return friendPaths;
	}

	public void setFriendPaths(List<FriendPath> friendPaths) {
		this.friendPaths = friendPaths;
	}
	
	
	
}
