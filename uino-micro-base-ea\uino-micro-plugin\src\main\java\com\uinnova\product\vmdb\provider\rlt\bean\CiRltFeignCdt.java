package com.uinnova.product.vmdb.provider.rlt.bean;

import com.uinnova.product.vmdb.comm.model.rlt.CCcCiRlt;
import com.uinnova.product.vmdb.comm.model.rlt.CcCiRlt;
import com.uinnova.product.vmdb.comm.util.SaveType;

import java.io.Serializable;
import java.util.Map;

/**
 * 合并使用RequestBody传输的对象
 * <AUTHOR>
 */
public class CiRltFeignCdt implements Serializable {
    private static final long serialVersionUID = 1L;

    private CcCiRlt ciRlt;

    CCcCiRlt cdt;

    private Map<String, String> attrs;

    private SaveType saveType;

    CiRltQ[] ciRltQs;

    public CCcCiRlt getCdt() {
        return cdt;
    }

    public void setCdt(CCcCiRlt cdt) {
        this.cdt = cdt;
    }

    public CiRltQ[] getCiRltQs() {
        return ciRltQs;
    }

    public void setCiRltQs(CiRltQ[] ciRltQs) {
        this.ciRltQs = ciRltQs;
    }

    public CcCiRlt getCiRlt() {
        return ciRlt;
    }

    public void setCiRlt(CcCiRlt ciRlt) {
        this.ciRlt = ciRlt;
    }

    public Map<String, String> getAttrs() {
        return attrs;
    }

    public void setAttrs(Map<String, String> attrs) {
        this.attrs = attrs;
    }

    public SaveType getSaveType() {
        return saveType;
    }

    public void setSaveType(SaveType saveType) {
        this.saveType = saveType;
    }
}
