package com.uinnova.product.vmdb.comm.model.rule;

import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;

/**
 * 
 * <AUTHOR>
 *
 */
@Comment("标签定义表[CC_CI_TAG_DEF]")
public class CCcCiTagDef implements Condition {
    private static final long serialVersionUID = 1L;

    @Comment("ID[ID] operate-Equal[=]")
    private Long id;

    @Comment("ID[ID] operate-In[in]")
    private Long[] ids;

    @Comment("ID[ID] operate-GTEqual[>=]")
    private Long startId;

    @Comment("ID[ID] operate-LTEqual[<=]")
    private Long endId;

    @Comment("标签名称[TAG_NAME] operate-Like[like]")
    private String tagName;

    @Comment("标签名称[TAG_NAME] operate-Equal[=]")
    private String tagNameEqual;

    @Comment("标签名称[TAG_NAME] operate-In[in]")
    private String[] tagNames;

    @Comment("所属目录[DIR_ID] operate-Equal[=]")
    private Long dirId;

    @Comment("所属目录[DIR_ID] operate-In[in]")
    private Long[] dirIds;

    @Comment("所属目录[DIR_ID] operate-GTEqual[>=]")
    private Long startDirId;

    @Comment("所属目录[DIR_ID] operate-LTEqual[<=]")
    private Long endDirId;

    @Comment("标签类型[TAG_TYPE] operate-Equal[=]    1=权限标签 2=视图标签 3=数据标签")
    private Integer tagType;

    @Comment("标签类型[TAG_TYPE] operate-In[in]    1=权限标签 2=视图标签 3=数据标签")
    private Integer[] tagTypes;

    @Comment("标签类型[TAG_TYPE] operate-GTEqual[>=]    1=权限标签 2=视图标签 3=数据标签")
    private Integer startTagType;

    @Comment("标签类型[TAG_TYPE] operate-LTEqual[<=]    1=权限标签 2=视图标签 3=数据标签")
    private Integer endTagType;

    @Comment("定义描述[DEF_DESC] operate-Like[like]")
    private String defDesc;

    @Comment("有效状态[IS_VALID] operate-Equal[=]    1=有效 0=无效")
    private Integer isValid;

    @Comment("有效状态[IS_VALID] operate-In[in]    1=有效 0=无效")
    private Integer[] isValids;

    @Comment("有效状态[IS_VALID] operate-GTEqual[>=]    1=有效 0=无效")
    private Integer startIsValid;

    @Comment("有效状态[IS_VALID] operate-LTEqual[<=]    1=有效 0=无效")
    private Integer endIsValid;

    @Comment("无效原因[INV_REASON] operate-Like[like]")
    private String invReason;

    @Comment("构建状态[BUILD_STATUS] operate-Equal[=]    1=待构建 2=构建中")
    private Integer buildStatus;

    @Comment("构建状态[BUILD_STATUS] operate-In[in]    1=待构建 2=构建中")
    private Integer[] buildStatuss;

    @Comment("构建状态[BUILD_STATUS] operate-GTEqual[>=]    1=待构建 2=构建中")
    private Integer startBuildStatus;

    @Comment("构建状态[BUILD_STATUS] operate-LTEqual[<=]    1=待构建 2=构建中")
    private Integer endBuildStatus;

    @Comment("所属域[DOMAIN_ID] operate-Equal[=]")
    private Long domainId;

    @Comment("所属域[DOMAIN_ID] operate-In[in]")
    private Long[] domainIds;

    @Comment("所属域[DOMAIN_ID] operate-GTEqual[>=]")
    private Long startDomainId;

    @Comment("所属域[DOMAIN_ID] operate-LTEqual[<=]")
    private Long endDomainId;

    @Comment("数据状态[DATA_STATUS] operate-Equal[=]    数据状态：1-正常 0-删除")
    private Integer dataStatus;

    @Comment("数据状态[DATA_STATUS] operate-In[in]    数据状态：1-正常 0-删除")
    private Integer[] dataStatuss;

    @Comment("数据状态[DATA_STATUS] operate-GTEqual[>=]    数据状态：1-正常 0-删除")
    private Integer startDataStatus;

    @Comment("数据状态[DATA_STATUS] operate-LTEqual[<=]    数据状态：1-正常 0-删除")
    private Integer endDataStatus;

    @Comment("创建人[CREATOR] operate-Like[like]")
    private String creator;

    @Comment("创建人[CREATOR] operate-Equal[=]")
    private String creatorEqual;

    @Comment("创建人[CREATOR] operate-In[in]")
    private String[] creators;

    @Comment("修改人[MODIFIER] operate-Like[like]")
    private String modifier;

    @Comment("修改人[MODIFIER] operate-Equal[=]")
    private String modifierEqual;

    @Comment("修改人[MODIFIER] operate-In[in]")
    private String[] modifiers;

    @Comment("创建时间[CREATE_TIME] operate-Equal[=]    yyyyMMddHHmmss")
    private Long createTime;

    @Comment("创建时间[CREATE_TIME] operate-In[in]    yyyyMMddHHmmss")
    private Long[] createTimes;

    @Comment("创建时间[CREATE_TIME] operate-GTEqual[>=]    yyyyMMddHHmmss")
    private Long startCreateTime;

    @Comment("创建时间[CREATE_TIME] operate-LTEqual[<=]    yyyyMMddHHmmss")
    private Long endCreateTime;

    @Comment("修改时间[MODIFY_TIME] operate-Equal[=]    yyyyMMddHHmmss")
    private Long modifyTime;

    @Comment("修改时间[MODIFY_TIME] operate-In[in]    yyyyMMddHHmmss")
    private Long[] modifyTimes;

    @Comment("修改时间[MODIFY_TIME] operate-GTEqual[>=]    yyyyMMddHHmmss")
    private Long startModifyTime;

    @Comment("修改时间[MODIFY_TIME] operate-LTEqual[<=]    yyyyMMddHHmmss")
    private Long endModifyTime;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long[] getIds() {
        return this.ids;
    }

    public void setIds(Long[] ids) {
        this.ids = ids;
    }

    public Long getStartId() {
        return this.startId;
    }

    public void setStartId(Long startId) {
        this.startId = startId;
    }

    public Long getEndId() {
        return this.endId;
    }

    public void setEndId(Long endId) {
        this.endId = endId;
    }

    public String getTagName() {
        return this.tagName;
    }

    public void setTagName(String tagName) {
        this.tagName = tagName;
    }

    public String getTagNameEqual() {
        return this.tagNameEqual;
    }

    public void setTagNameEqual(String tagNameEqual) {
        this.tagNameEqual = tagNameEqual;
    }

    public String[] getTagNames() {
        return this.tagNames;
    }

    public void setTagNames(String[] tagNames) {
        this.tagNames = tagNames;
    }

    public Long getDirId() {
        return this.dirId;
    }

    public void setDirId(Long dirId) {
        this.dirId = dirId;
    }

    public Long[] getDirIds() {
        return this.dirIds;
    }

    public void setDirIds(Long[] dirIds) {
        this.dirIds = dirIds;
    }

    public Long getStartDirId() {
        return this.startDirId;
    }

    public void setStartDirId(Long startDirId) {
        this.startDirId = startDirId;
    }

    public Long getEndDirId() {
        return this.endDirId;
    }

    public void setEndDirId(Long endDirId) {
        this.endDirId = endDirId;
    }

    public Integer getTagType() {
        return this.tagType;
    }

    public void setTagType(Integer tagType) {
        this.tagType = tagType;
    }

    public Integer[] getTagTypes() {
        return this.tagTypes;
    }

    public void setTagTypes(Integer[] tagTypes) {
        this.tagTypes = tagTypes;
    }

    public Integer getStartTagType() {
        return this.startTagType;
    }

    public void setStartTagType(Integer startTagType) {
        this.startTagType = startTagType;
    }

    public Integer getEndTagType() {
        return this.endTagType;
    }

    public void setEndTagType(Integer endTagType) {
        this.endTagType = endTagType;
    }

    public String getDefDesc() {
        return this.defDesc;
    }

    public void setDefDesc(String defDesc) {
        this.defDesc = defDesc;
    }

    public Integer getIsValid() {
        return this.isValid;
    }

    public void setIsValid(Integer isValid) {
        this.isValid = isValid;
    }

    public Integer[] getIsValids() {
        return this.isValids;
    }

    public void setIsValids(Integer[] isValids) {
        this.isValids = isValids;
    }

    public Integer getStartIsValid() {
        return this.startIsValid;
    }

    public void setStartIsValid(Integer startIsValid) {
        this.startIsValid = startIsValid;
    }

    public Integer getEndIsValid() {
        return this.endIsValid;
    }

    public void setEndIsValid(Integer endIsValid) {
        this.endIsValid = endIsValid;
    }

    public String getInvReason() {
        return this.invReason;
    }

    public void setInvReason(String invReason) {
        this.invReason = invReason;
    }

    public Integer getBuildStatus() {
        return this.buildStatus;
    }

    public void setBuildStatus(Integer buildStatus) {
        this.buildStatus = buildStatus;
    }

    public Integer[] getBuildStatuss() {
        return this.buildStatuss;
    }

    public void setBuildStatuss(Integer[] buildStatuss) {
        this.buildStatuss = buildStatuss;
    }

    public Integer getStartBuildStatus() {
        return this.startBuildStatus;
    }

    public void setStartBuildStatus(Integer startBuildStatus) {
        this.startBuildStatus = startBuildStatus;
    }

    public Integer getEndBuildStatus() {
        return this.endBuildStatus;
    }

    public void setEndBuildStatus(Integer endBuildStatus) {
        this.endBuildStatus = endBuildStatus;
    }

    public Long getDomainId() {
        return this.domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }

    public Long[] getDomainIds() {
        return this.domainIds;
    }

    public void setDomainIds(Long[] domainIds) {
        this.domainIds = domainIds;
    }

    public Long getStartDomainId() {
        return this.startDomainId;
    }

    public void setStartDomainId(Long startDomainId) {
        this.startDomainId = startDomainId;
    }

    public Long getEndDomainId() {
        return this.endDomainId;
    }

    public void setEndDomainId(Long endDomainId) {
        this.endDomainId = endDomainId;
    }

    public Integer getDataStatus() {
        return this.dataStatus;
    }

    public void setDataStatus(Integer dataStatus) {
        this.dataStatus = dataStatus;
    }

    public Integer[] getDataStatuss() {
        return this.dataStatuss;
    }

    public void setDataStatuss(Integer[] dataStatuss) {
        this.dataStatuss = dataStatuss;
    }

    public Integer getStartDataStatus() {
        return this.startDataStatus;
    }

    public void setStartDataStatus(Integer startDataStatus) {
        this.startDataStatus = startDataStatus;
    }

    public Integer getEndDataStatus() {
        return this.endDataStatus;
    }

    public void setEndDataStatus(Integer endDataStatus) {
        this.endDataStatus = endDataStatus;
    }

    public String getCreator() {
        return this.creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getCreatorEqual() {
        return this.creatorEqual;
    }

    public void setCreatorEqual(String creatorEqual) {
        this.creatorEqual = creatorEqual;
    }

    public String[] getCreators() {
        return this.creators;
    }

    public void setCreators(String[] creators) {
        this.creators = creators;
    }

    public String getModifier() {
        return this.modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public String getModifierEqual() {
        return this.modifierEqual;
    }

    public void setModifierEqual(String modifierEqual) {
        this.modifierEqual = modifierEqual;
    }

    public String[] getModifiers() {
        return this.modifiers;
    }

    public void setModifiers(String[] modifiers) {
        this.modifiers = modifiers;
    }

    public Long getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long[] getCreateTimes() {
        return this.createTimes;
    }

    public void setCreateTimes(Long[] createTimes) {
        this.createTimes = createTimes;
    }

    public Long getStartCreateTime() {
        return this.startCreateTime;
    }

    public void setStartCreateTime(Long startCreateTime) {
        this.startCreateTime = startCreateTime;
    }

    public Long getEndCreateTime() {
        return this.endCreateTime;
    }

    public void setEndCreateTime(Long endCreateTime) {
        this.endCreateTime = endCreateTime;
    }

    public Long getModifyTime() {
        return this.modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Long[] getModifyTimes() {
        return this.modifyTimes;
    }

    public void setModifyTimes(Long[] modifyTimes) {
        this.modifyTimes = modifyTimes;
    }

    public Long getStartModifyTime() {
        return this.startModifyTime;
    }

    public void setStartModifyTime(Long startModifyTime) {
        this.startModifyTime = startModifyTime;
    }

    public Long getEndModifyTime() {
        return this.endModifyTime;
    }

    public void setEndModifyTime(Long endModifyTime) {
        this.endModifyTime = endModifyTime;
    }

}
