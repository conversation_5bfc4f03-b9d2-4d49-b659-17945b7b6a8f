package com.uinnova.product.eam.service.es;

import com.uinnova.product.eam.comm.model.es.AppSquareConfig;
import com.uinnova.product.eam.comm.model.es.AssetAttrDataScreen;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Repository;

import jakarta.annotation.PostConstruct;

@Repository
public class AssetAttrDataScreenDao extends AbstractESBaseDao<AssetAttrDataScreen, AssetAttrDataScreen> {
    @Override
    public String getIndex() {
        return "uino_eam_asset_data_screen";
    }

    @Override
    public String getType() {
        return "_doc";
    }
    @PostConstruct
    public void init() {
        super.initIndex();
    }}

