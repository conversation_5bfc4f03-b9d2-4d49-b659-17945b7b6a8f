package com.uinnova.product.eam.model.dto;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

/**
 * 新疆农信定制服务分析配置项
 * <AUTHOR>
 * @date 2022/4/27
 */
@Data
public class ServiceAnalysisConfig {
    @Comment("调用关系分类名")
    private String useRltName;
    @Comment("属于关系分类名")
    private String belRltName;
    @Comment("映射关系分类名")
    private String mapRltName;
    @Comment("接口服务")
    private String serviceCiName;
    @Comment("服务类型")
    private String serviceTypeName;
    @Comment("原生服务")
    private String serviceType1;
    @Comment("映射服务")
    private String serviceType2;
}
