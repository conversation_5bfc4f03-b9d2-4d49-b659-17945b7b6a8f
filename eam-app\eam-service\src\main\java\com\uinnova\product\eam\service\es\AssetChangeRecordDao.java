package com.uinnova.product.eam.service.es;

import com.uinnova.product.eam.comm.model.es.AssetChangeRecord;
import com.uinnova.product.eam.comm.model.es.AssetDetailAttrConf;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;

/**
 * 资产流程记录表（新建、变更、作废）
 * <AUTHOR>
 */
@Service
public class AssetChangeRecordDao extends AbstractESBaseDao<AssetChangeRecord, AssetChangeRecord> {
    @Override
    public String getIndex() {
        return "uino_eam_asset_change_record";
    }

    @Override
    public String getType() {
        return "uino_eam_asset_change_record";
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }
}
