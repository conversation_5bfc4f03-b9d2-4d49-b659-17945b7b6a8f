package com.uinnova.product.eam.service.flowable.impl;

import com.uinnova.product.eam.model.constants.FlowableConstant;
import com.uinnova.product.eam.service.flowable.FlowableApprovalSvc;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component(value = FlowableConstant.PLAN_DEFINITION_KEY1)
public class EamPlanApprovalSvcImplV1 implements FlowableApprovalSvc {


    @Override
    public List<String> getApprovalUser(String businessKey, String taskKey) {
        return new ArrayList<>();
    }

    @Override
    public void reject(String businessKey) {
    }

    @Override
    public void pass(String businessKey) {
    }

    @Override
    public void cancel(String businessKey) {

    }
}
