package com.uino.init.http.safe;

import com.binary.core.util.BinaryUtils;
import com.binary.core.util.WildcardPatternBuilder;
import com.uino.api.client.permission.IModuleApiSvc;
import com.uino.bean.permission.base.SysModule;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.permission.query.CAuthModuleBean;
import com.uino.util.exception.LoginException;
import com.uino.util.sys.SysUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;

import jakarta.servlet.FilterConfig;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 功能权限拦截器
 * 
 * <AUTHOR>
 *
 */
//@WebFilter(urlPatterns = "/*")
@Slf4j
//@Order(4)
public class PermissionFilter extends WebSafeFilter {

	private ApplicationContext applicationContext;

	private boolean needAuthorityCheck;

	private String strIgnoreFilterPattern;

	public PermissionFilter(ApplicationContext applicationContext) {
		this.applicationContext = applicationContext;
	}

	@Override
	public void init(FilterConfig filterConfig) throws ServletException {
		this.needAuthorityCheck = Boolean.parseBoolean(filterConfig.getInitParameter("needAuthorityCheck"));
		this.strIgnoreFilterPattern = filterConfig.getInitParameter("strIgnoreFilterPattern");
	}


	@Override
	protected boolean validity(HttpServletRequest request) {
		// Development environment closure verification
		Boolean isDevelop = applicationContext.getBean(DeveloperEnableApi.class).isDevelopEnvironment();
		if (isDevelop != null && isDevelop) {
			return true;
		}
		// 如里不需要过滤
		if (isIgnoreRequest(request)) {
			return true;
		}
		// 校验用户访问权限
		String moduleUrl = request.getHeader("moduleUrl");
		if (BinaryUtils.isEmpty(moduleUrl)) {
			return false;
		}

		if (moduleUrl.equals("/plugin") || moduleUrl.equals("/")) {
			return true;
		}

		SysUser currentUser = null;
		try {
			currentUser = SysUtil.getCurrentUserInfo();
		} catch (LoginException e) {
			log.debug("无法获取当前登陆用户，该持久化信息可能会缺失[创建/修改]人信息和domain域信息不对的问题");
			return false;
		}
		//租户域和超级用户绑定，如果不是系统用户访问租户域管理则拦截
		if ("/domain-manage".equals(moduleUrl) && !"superadmin".equals(currentUser.getLoginCode())) {
			log.warn("用户【{}】没有权限访问【{}】", currentUser.getLoginCode(), request.getRequestURI());
			return false;
		}
		List<SysModule> authModules = applicationContext.getBean(IModuleApiSvc.class)
				.getAuthModulesBySearchBean(CAuthModuleBean.builder().userId(currentUser.getId()).build());
		List<String> authModuleUrls = authModules.stream().filter(module -> !BinaryUtils.isEmpty(module.getModuleUrl()))
				.map(module -> {
					String url = module.getModuleUrl();
					return url.indexOf("/", 1) != -1 ? url.substring(0, url.indexOf("/", 1)) : url;
				}).collect(Collectors.toList());
		if (!authModuleUrls.contains(moduleUrl) && (moduleUrl.indexOf("?") == -1
				|| !authModuleUrls.contains(moduleUrl.substring(0, moduleUrl.indexOf("?"))))) {
			log.warn("用户【{}】没有权限访问【{}】", currentUser.getLoginCode(), request.getRequestURI());
			return false;
		}
		return true;
	}

	@Override
	protected boolean support(HttpServletRequest httpRequest) {
		return needAuthorityCheck ? true : !BinaryUtils.isEmpty(httpRequest.getHeader("moduleUrl"));
	}

	private boolean isIgnoreRequest(HttpServletRequest request) {
		Pattern ignoreFilterPattern = WildcardPatternBuilder
				.build(splitStringPattern(strIgnoreFilterPattern.toUpperCase()));
		String contextpath = request.getContextPath();
		String url = request.getRequestURI();
		String path = url.substring(contextpath.length()).toUpperCase();
		if (path.length() == 0) {
			path = "/";
		}
		// header中传isPlugin:true为插件调用接口
		String isPluginStr = request.getHeader("isPlugin");
		boolean isPlugin = false;
		if (!BinaryUtils.isEmpty(isPluginStr)) {
			try {
				isPlugin = Boolean.parseBoolean(isPluginStr);
			} catch (Exception e) {
				log.debug("header中isPlugin值${}错误，必须为true或false", isPluginStr);
			}
		}

		return (ignoreFilterPattern != null && ignoreFilterPattern.matcher(path).matches())
				|| path.startsWith("/PLUGIN") || isPlugin;
	}

	private String[] splitStringPattern(String strFilterPattern) {
		char m = ';';
		if (strFilterPattern.indexOf(m) > 0) {
			String[] arr = strFilterPattern.split("[;]");
			List<String> ls = new ArrayList<String>();
			for (int i = 0; i < arr.length; i++) {
				String s = arr[i].trim();
				if (s.length() > 0) {
					ls.add(s);
				}
			}
			return ls.toArray(new String[0]);
		} else {
			return new String[] { strFilterPattern };
		}
	}
}