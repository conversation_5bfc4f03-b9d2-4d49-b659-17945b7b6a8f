package com.uino.web.sys.mvc;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import com.uino.bean.permission.base.SysUser;
import com.uino.init.api.ApiResult;
import com.uino.init.api.ApiVersion;
import com.uino.util.sys.SysUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uinnova.product.vmdb.comm.doc.annotation.MvcDesc;
import com.uino.bean.sys.base.ESCIOperateLog;
import com.uino.bean.sys.query.ESCIOperateLogSearchBean;
import com.uino.api.client.sys.ICIOperateLogApiSvc;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@ApiVersion(1)
@Api(value = "日志管理-配置日志", tags = {"日志管理"})
@RestController
@RequestMapping("/sys/ciLog")
@MvcDesc(author = "zmj", desc = "日志管理-配置日志")
public class SysCIOperateLogMvc {

    @Autowired
    ICIOperateLogApiSvc logSvc;

    @ApiOperation("条件查询配置日志")
    @RequestMapping(value="/searchPageByCdt",method = RequestMethod.POST)
    @ModDesc(desc = "条件查询配置日志", pDesc = "查询条件", pType = ESCIOperateLogSearchBean.class, rDesc = "查询结果", rType = Page.class, rcType = ESCIOperateLog.class)
    public ApiResult<Page<ESCIOperateLog>> searchPageByCdt(HttpServletRequest request, HttpServletResponse response, @RequestBody ESCIOperateLogSearchBean bean) {
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        bean.setDomainId(currentUserInfo.getDomainId());
        Page<ESCIOperateLog> rs = logSvc.getCIOperateLogPageByCdt(bean);
        return ApiResult.ok(this).data(rs);
    }
}
