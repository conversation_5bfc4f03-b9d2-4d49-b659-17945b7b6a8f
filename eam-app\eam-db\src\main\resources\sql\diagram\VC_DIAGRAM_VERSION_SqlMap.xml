<?xml version="1.0" encoding="UTF-8"?><!--Converted at: Sat Dec 08 16:22:37 CST 2018-->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="VC_DIAGRAM_VERSION">


	<resultMap id="queryResult" type="com.uinnova.product.eam.base.diagram.model.VcDiagramVersion">
		<result property="id" column="ID" jdbcType="BIGINT"/>	<!-- ID -->
		<result property="diagramId" column="DIAGRAM_ID" jdbcType="BIGINT"/>	<!-- 源视图ID -->
		<result property="versionNo" column="VERSION_NO" jdbcType="VARCHAR"/>	<!-- 版本号 -->
		<result property="versionName" column="VERSION_NAME" jdbcType="VARCHAR"/>
		<result property="versionTime" column="VERSION_TIME" jdbcType="BIGINT"/>	<!-- 版本时间 -->
		<result property="versionDesc" column="VERSION_DESC" jdbcType="VARCHAR"/>	<!-- 版本描述 -->
		<result property="name" column="NAME" jdbcType="VARCHAR"/>	<!-- 视图名称 -->
		<result property="userId" column="USER_ID" jdbcType="BIGINT"/>	<!-- 所属用户 -->
		<result property="dirId" column="DIR_ID" jdbcType="BIGINT"/>	<!-- 所属目录 -->
		<result property="diagramDesc" column="DIAGRAM_DESC" jdbcType="VARCHAR"/>	<!-- 视图描述 -->
		<result property="diagramSvg" column="DIAGRAM_SVG" jdbcType="VARCHAR"/>	<!-- 视图SVG -->
		<result property="diagramXml" column="DIAGRAM_XML" jdbcType="VARCHAR"/>	<!-- 视图XML -->
		<result property="diagramJson" column="DIAGRAM_JSON" jdbcType="VARCHAR"/>	<!-- 视图JSON -->
		<result property="diagramBgCss" column="DIAGRAM_BG_CSS" jdbcType="VARCHAR"/>	<!-- 背景样式 -->
		<result property="icon1" column="ICON_1" jdbcType="VARCHAR"/>	<!-- 视图图标_1 -->
		<result property="icon2" column="ICON_2" jdbcType="VARCHAR"/>	<!-- 视图图标_2 -->
		<result property="icon3" column="ICON_3" jdbcType="VARCHAR"/>	<!-- 视图图标_3 -->
		<result property="icon4" column="ICON_4" jdbcType="VARCHAR"/>	<!-- 视图图标_4 -->
		<result property="icon5" column="ICON_5" jdbcType="VARCHAR"/>	<!-- 视图图标_5 -->
		<result property="status" column="STATUS" jdbcType="INTEGER"/>	<!-- 视图状态 -->
		<result property="ci3dPoint" column="CI_3D_POINT" jdbcType="VARCHAR"/>	<!-- CI3D坐标 -->
		<result property="ci3dPoint2" column="CI_3D_POINT2" jdbcType="VARCHAR"/>	<!-- CI3D坐标2 -->
		<result property="versionDescPath" column="VERSION_DESC_PATH" jdbcType="VARCHAR"/>	<!-- 版本描述文件路径信息 -->
		<result property="domainId" column="DOMAIN_ID" jdbcType="BIGINT"/>	<!-- 所属域 -->
		<result property="dataStatus" column="DATA_STATUS" jdbcType="INTEGER"/>	<!-- 数据状态 -->
		<result property="creator" column="CREATOR" jdbcType="VARCHAR"/>	<!-- 创建人 -->
		<result property="modifier" column="MODIFIER" jdbcType="VARCHAR"/>	<!-- 修改人 -->
		<result property="createTime" column="CREATE_TIME" jdbcType="BIGINT"/>	<!-- 创建时间 -->
		<result property="modifyTime" column="MODIFY_TIME" jdbcType="BIGINT"/>	<!-- 更新时间 -->
		<result property="releaseId" column="RELEASE_ID" jdbcType="BIGINT"/>	<!-- 关联视图发布id -->
		<result property="releaseVersion" column="RELEASE_VERSION" jdbcType="BIGINT"/>	<!-- 已发布视图版本 -->
		<result property="autoCreat" column="AUTO_CREAT" jdbcType="BIGINT"/>	<!-- 是否为自动创建的版本 是：1/null 否：2 -->
	</resultMap>


	<sql id="sql_query_where">
		<if test="cdt != null and cdt.id != null">and
			ID = #{cdt.id:BIGINT}
		</if>
		<if test="ids != null and ids != ''">and
			ID in (${ids})
		</if>
		<if test="cdt != null and cdt.startId != null">and
			ID &gt;= #{cdt.startId:BIGINT}
		</if>
		<if test="cdt != null and cdt.endId != null">and
			ID &lt;= #{cdt.endId:BIGINT}
		</if>
		<if test="cdt != null and cdt.diagramId != null">and
			DIAGRAM_ID = #{cdt.diagramId:BIGINT}
		</if>
		<if test="diagramIds != null and diagramIds != ''">and
			DIAGRAM_ID in (${diagramIds})
		</if>
		<if test="cdt != null and cdt.startDiagramId != null">and
			DIAGRAM_ID &gt;= #{cdt.startDiagramId:BIGINT}
		</if>
		<if test="cdt != null and cdt.endDiagramId != null">and
			DIAGRAM_ID &lt;= #{cdt.endDiagramId:BIGINT}
		</if>
		<if test="cdt != null and cdt.versionNo != null and cdt.versionNo != ''">and
			VERSION_NO like #{cdt.versionNo,jdbcType=VARCHAR}
		</if>
		<if test="cdt != null and cdt.versionNoEqual != null and cdt.versionNoEqual != ''">and
			VERSION_NO = #{cdt.versionNoEqual,jdbcType=VARCHAR}
		</if>
		<if test="versionNos != null and versionNos != ''">and
			VERSION_NO in (${versionNos})
		</if>
		<if test="cdt != null and cdt.versionName != null and cdt.versionName != ''">and
			VERSION_NAME like #{cdt.versionName,jdbcType=VARCHAR}
		</if>
		<if test="cdt != null and cdt.versionNameEqual != null and cdt.versionNameEqual != ''">and
			VERSION_NAME = #{cdt.versionNameEqual,jdbcType=VARCHAR}
		</if>
		<if test="versionNames != null and versionNames != ''">and
			VERSION_NAME in (${versionNames})
		</if>
		<if test="cdt != null and cdt.versionTime != null">and
			VERSION_TIME = #{cdt.versionTime:BIGINT}
		</if>
		<if test="versionTimes != null and versionTimes != ''">and
			VERSION_TIME in (${versionTimes})
		</if>
		<if test="cdt != null and cdt.startVersionTime != null">and
			VERSION_TIME &gt;= #{cdt.startVersionTime:BIGINT}
		</if>
		<if test="cdt != null and cdt.endVersionTime != null">and
			VERSION_TIME &lt;= #{cdt.endVersionTime:BIGINT}
		</if>
		<if test="cdt != null and cdt.versionDesc != null and cdt.versionDesc != ''">and
			VERSION_DESC like #{cdt.versionDesc,jdbcType=VARCHAR}
		</if>
		<if test="cdt != null and cdt.name != null and cdt.name != ''">and
			NAME like #{cdt.name,jdbcType=VARCHAR}
		</if>
		<if test="cdt != null and cdt.diagramBgCss != null and cdt.diagramBgCss != ''">and
			DIAGRAM_BG_CSS like #{cdt.diagramBgCss,jdbcType=VARCHAR}
		</if>
		<if test="cdt != null and cdt.nameEqual != null and cdt.nameEqual != ''">and
			NAME = #{cdt.nameEqual,jdbcType=VARCHAR}
		</if>
		<if test="names != null and names != ''">and
			NAME in (${names})
		</if>
		<if test="cdt != null and cdt.userId != null">and
			USER_ID = #{cdt.userId:BIGINT}
		</if>
		<if test="userIds != null and userIds != ''">and
			USER_ID in (${userIds})
		</if>
		<if test="cdt != null and cdt.startUserId != null">and
			USER_ID &gt;= #{cdt.startUserId:BIGINT}
		</if>
		<if test="cdt != null and cdt.endUserId != null">and
			USER_ID &lt;= #{cdt.endUserId:BIGINT}
		</if>
		<if test="cdt != null and cdt.dirId != null">and
			DIR_ID = #{cdt.dirId:BIGINT}
		</if>
		<if test="dirIds != null and dirIds != ''">and
			DIR_ID in (${dirIds})
		</if>
		<if test="cdt != null and cdt.startDirId != null">and
			DIR_ID &gt;= #{cdt.startDirId:BIGINT}
		</if>
		<if test="cdt != null and cdt.endDirId != null">and
			DIR_ID &lt;= #{cdt.endDirId:BIGINT}
		</if>
		<if test="cdt != null and cdt.diagramDesc != null and cdt.diagramDesc != ''">and
			DIAGRAM_DESC like #{cdt.diagramDesc,jdbcType=VARCHAR}
		</if>
		<if test="cdt != null and cdt.diagramSvg != null and cdt.diagramSvg != ''">and
			DIAGRAM_SVG like #{cdt.diagramSvg,jdbcType=VARCHAR}
		</if>
		<if test="cdt != null and cdt.diagramXml != null and cdt.diagramXml != ''">and
			DIAGRAM_XML like #{cdt.diagramXml,jdbcType=VARCHAR}
		</if>
		<if test="cdt != null and cdt.diagramJson != null and cdt.diagramJson != ''">and
			DIAGRAM_JSON like #{cdt.diagramJson,jdbcType=VARCHAR}
		</if>
		<if test="cdt != null and cdt.icon1 != null and cdt.icon1 != ''">and
			ICON_1 like #{cdt.icon1,jdbcType=VARCHAR}
		</if>
		<if test="cdt != null and cdt.icon2 != null and cdt.icon2 != ''">and
			ICON_2 like #{cdt.icon2,jdbcType=VARCHAR}
		</if>
		<if test="cdt != null and cdt.icon3 != null and cdt.icon3 != ''">and
			ICON_3 like #{cdt.icon3,jdbcType=VARCHAR}
		</if>
		<if test="cdt != null and cdt.icon4 != null and cdt.icon4 != ''">and
			ICON_4 like #{cdt.icon4,jdbcType=VARCHAR}
		</if>
		<if test="cdt != null and cdt.icon5 != null and cdt.icon5 != ''">and
			ICON_5 like #{cdt.icon5,jdbcType=VARCHAR}
		</if>
		<if test="cdt != null and cdt.status != null">and
			STATUS = #{cdt.status:INTEGER}
		</if>
		<if test="statuss != null and statuss != ''">and
			STATUS in (${statuss})
		</if>
		<if test="cdt != null and cdt.startStatus != null">and
			STATUS &gt;= #{cdt.startStatus:INTEGER}
		</if>
		<if test="cdt != null and cdt.endStatus != null">and
			STATUS &lt;= #{cdt.endStatus:INTEGER}
		</if>
		<if test="cdt != null and cdt.ci3dPoint != null and cdt.ci3dPoint != ''">and
			CI_3D_POINT like #{cdt.ci3dPoint,jdbcType=VARCHAR}
		</if>
		<if test="cdt != null and cdt.ci3dPoint2 != null and cdt.ci3dPoint2 != ''">and
			CI_3D_POINT2 like #{cdt.ci3dPoint2,jdbcType=VARCHAR}
		</if>
		<if test="cdt != null and cdt.versionDescPath != null and cdt.versionDescPath != ''">and
			VERSION_DESC_PATH like #{cdt.versionDescPath,jdbcType=VARCHAR}
		</if>
		<if test="cdt != null and cdt.domainId != null">and
			DOMAIN_ID = #{cdt.domainId:BIGINT}
		</if>
		<if test="domainIds != null and domainIds != ''">and
			DOMAIN_ID in (${domainIds})
		</if>
		<if test="cdt != null and cdt.startDomainId != null">and
			DOMAIN_ID &gt;= #{cdt.startDomainId:BIGINT}
		</if>
		<if test="cdt != null and cdt.endDomainId != null">and
			DOMAIN_ID &lt;= #{cdt.endDomainId:BIGINT}
		</if>
		<if test="cdt != null and cdt.dataStatus != null">and
			DATA_STATUS = #{cdt.dataStatus:INTEGER}
		</if>
		<if test="dataStatuss != null and dataStatuss != ''">and
			DATA_STATUS in (${dataStatuss})
		</if>
		<if test="cdt != null and cdt.startDataStatus != null">and
			DATA_STATUS &gt;= #{cdt.startDataStatus:INTEGER}
		</if>
		<if test="cdt != null and cdt.endDataStatus != null">and
			DATA_STATUS &lt;= #{cdt.endDataStatus:INTEGER}
		</if>
		<if test="cdt != null and cdt.creator != null and cdt.creator != ''">and
			CREATOR like #{cdt.creator,jdbcType=VARCHAR}
		</if>
		<if test="cdt != null and cdt.creatorEqual != null and cdt.creatorEqual != ''">and
			CREATOR = #{cdt.creatorEqual,jdbcType=VARCHAR}
		</if>
		<if test="creators != null and creators != ''">and
			CREATOR in (${creators})
		</if>
		<if test="cdt != null and cdt.modifier != null and cdt.modifier != ''">and
			MODIFIER like #{cdt.modifier,jdbcType=VARCHAR}
		</if>
		<if test="cdt != null and cdt.modifierEqual != null and cdt.modifierEqual != ''">and
			MODIFIER = #{cdt.modifierEqual,jdbcType=VARCHAR}
		</if>
		<if test="modifiers != null and modifiers != ''">and
			MODIFIER in (${modifiers})
		</if>
		<if test="cdt != null and cdt.createTime != null">and
			CREATE_TIME = #{cdt.createTime:BIGINT}
		</if>
		<if test="createTimes != null and createTimes != ''">and
			CREATE_TIME in (${createTimes})
		</if>
		<if test="cdt != null and cdt.startCreateTime != null">and
			CREATE_TIME &gt;= #{cdt.startCreateTime:BIGINT}
		</if>
		<if test="cdt != null and cdt.endCreateTime != null">and
			CREATE_TIME &lt;= #{cdt.endCreateTime:BIGINT}
		</if>
		<if test="cdt != null and cdt.modifyTime != null">and
			MODIFY_TIME = #{cdt.modifyTime:BIGINT}
		</if>
		<if test="modifyTimes != null and modifyTimes != ''">and
			MODIFY_TIME in (${modifyTimes})
		</if>
		<if test="cdt != null and cdt.startModifyTime != null">and
			MODIFY_TIME &gt;= #{cdt.startModifyTime:BIGINT}
		</if>
		<if test="cdt != null and cdt.endModifyTime != null">and
			MODIFY_TIME &lt;= #{cdt.endModifyTime:BIGINT}
		</if>
		<if test="cdt != null and cdt.releaseId != null">and
			RELEASE_ID = #{cdt.releaseId:BIGINT}
		</if>
		<if test="cdt != null and cdt.releaseVersion != null">and
			RELEASE_VERSION = #{cdt.releaseVersion:BIGINT}
		</if>
		<if test="cdt != null and cdt.autoCreat != null and cdt.autoCreat == 1">and
			(AUTO_CREAT = #{cdt.autoCreat:BIGINT} OR AUTO_CREAT IS NULL)
		</if>
		<if test="cdt != null and cdt.autoCreat != null and cdt.autoCreat != 1">and
			AUTO_CREAT = #{cdt.autoCreat:BIGINT}
		</if>
		<if test="cdt != null and cdt.autoCreatNotEqual != null and cdt.autoCreatNotEqual != ''">and
			AUTO_CREAT != #{cdt.autoCreatNotEqual:BIGINT}
		</if>
	</sql>


	<sql id="sql_update_columns">
		<if test="record != null and record.id != null">
			ID = #{record.id:BIGINT}
			,</if>
		<if test="record != null and record.diagramId != null">
			DIAGRAM_ID = #{record.diagramId:BIGINT}
			,</if>
		<if test="record != null and record.versionNo != null">
			VERSION_NO = #{record.versionNo,jdbcType=VARCHAR}
			,</if>
		<if test="record != null and record.versionName != null">
			VERSION_NAME = #{record.versionName,jdbcType=VARCHAR}
			,</if>
		<if test="record != null and record.versionTime != null">
			VERSION_TIME = #{record.versionTime:BIGINT}
			,</if>
		<if test="record != null and record.versionDesc != null">
			VERSION_DESC = #{record.versionDesc,jdbcType=VARCHAR}
			,</if>
		<if test="record != null and record.name != null">
			NAME = #{record.name,jdbcType=VARCHAR}
			,</if>
		<if test="record != null and record.userId != null">
			USER_ID = #{record.userId:BIGINT}
			,</if>
		<if test="record != null and record.dirId != null">
			DIR_ID = #{record.dirId:BIGINT}
			,</if>
		<if test="record != null and record.diagramDesc != null">
			DIAGRAM_DESC = #{record.diagramDesc,jdbcType=VARCHAR}
			,</if>
		<if test="record != null and record.diagramSvg != null">
			DIAGRAM_SVG = #{record.diagramSvg,jdbcType=VARCHAR}
			,</if>
		<if test="record != null and record.diagramXml != null">
			DIAGRAM_XML = #{record.diagramXml,jdbcType=VARCHAR}
			,</if>
		<if test="record != null and record.diagramJson != null">
			DIAGRAM_JSON = #{record.diagramJson,jdbcType=VARCHAR}
			,</if>
		<if test="record != null and record.icon1 != null">
			ICON_1 = #{record.icon1,jdbcType=VARCHAR}
			,</if>
		<if test="record != null and record.icon2 != null">
			ICON_2 = #{record.icon2,jdbcType=VARCHAR}
			,</if>
		<if test="record != null and record.diagramBgCss != null">
			DIAGRAM_BG_CSS = #{record.diagramBgCss,jdbcType=VARCHAR}
			,</if>
		<if test="record != null and record.icon3 != null">
			ICON_3 = #{record.icon3,jdbcType=VARCHAR}
			,</if>
		<if test="record != null and record.icon4 != null">
			ICON_4 = #{record.icon4,jdbcType=VARCHAR}
			,</if>
		<if test="record != null and record.icon5 != null">
			ICON_5 = #{record.icon5,jdbcType=VARCHAR}
			,</if>
		<if test="record != null and record.status != null">
			STATUS = #{record.status:INTEGER}
			,</if>
		<if test="record != null and record.ci3dPoint != null">
			CI_3D_POINT = #{record.ci3dPoint,jdbcType=VARCHAR}
			,</if>
		<if test="record != null and record.ci3dPoint2 != null">
			CI_3D_POINT2 = #{record.ci3dPoint2,jdbcType=VARCHAR}
			,</if>
		<if test="record != null and record.versionDescPath != null">
			VERSION_DESC_PATH = #{record.versionDescPath,jdbcType=VARCHAR}
			,</if>
		<if test="record != null and record.domainId != null">
			DOMAIN_ID = #{record.domainId:BIGINT}
			,</if>
		<if test="record != null and record.dataStatus != null">
			DATA_STATUS = #{record.dataStatus:INTEGER}
			,</if>
		<if test="record != null and record.creator != null">
			CREATOR = #{record.creator,jdbcType=VARCHAR}
			,</if>
		<if test="record != null and record.modifier != null">
			MODIFIER = #{record.modifier,jdbcType=VARCHAR}
			,</if>
		<if test="record != null and record.createTime != null">
			CREATE_TIME = #{record.createTime:BIGINT}
			,</if>
		<if test="record != null and record.modifyTime != null">
			MODIFY_TIME = #{record.modifyTime:BIGINT}
			,</if>
		<if test="record != null and record.releaseId != null">
			RELEASE_ID = #{record.releaseId:BIGINT}
			,</if>
		<if test="record != null and record.releaseVersion != null">
			RELEASE_VERSION = #{record.releaseVersion:BIGINT}
			,</if>
		<if test="record != null and record.autoCreat != null">
			AUTO_CREAT = #{record.autoCreat:BIGINT}
			,</if>
	</sql>


	<sql id="sql_query_columns">
		ID, DIAGRAM_ID, VERSION_NO, VERSION_NAME, VERSION_TIME, VERSION_DESC, NAME,
		USER_ID, DIR_ID, DIAGRAM_DESC, DIAGRAM_SVG, DIAGRAM_XML, DIAGRAM_JSON,
		ICON_1, ICON_2, ICON_3, ICON_4, ICON_5, STATUS,
		CI_3D_POINT, CI_3D_POINT2, VERSION_DESC_PATH, DOMAIN_ID, DATA_STATUS, CREATOR,
		MODIFIER, CREATE_TIME, MODIFY_TIME, DIAGRAM_BG_CSS, RELEASE_ID, RELEASE_VERSION, AUTO_CREAT
	</sql>




	<select id="selectList" parameterType="java.util.Map" resultMap="queryResult">
		select
		<include refid="VC_DIAGRAM_VERSION.sql_query_columns"/>
		from VC_DIAGRAM_VERSION
		<where>
			<include refid="VC_DIAGRAM_VERSION.sql_query_where"/>
		</where>
		order by
		<if test="orders != null and orders != ''">
			${orders}
		</if>
		<if test="orders == null or orders == ''">
			ID
		</if>
	</select>
	<select id="selectCount" parameterType="java.util.Map" resultType="java.lang.Long">
		select count(1) from VC_DIAGRAM_VERSION
		<where>
			<include refid="VC_DIAGRAM_VERSION.sql_query_where"/>
		</where>
	</select>
	<select id="selectById" parameterType="java.util.Map" resultMap="queryResult">
		select
		<include refid="VC_DIAGRAM_VERSION.sql_query_columns"/>
		from VC_DIAGRAM_VERSION where ID=#{id:BIGINT} and DATA_STATUS=1
	</select>




	<insert id="insert" parameterType="java.util.Map">
		insert into VC_DIAGRAM_VERSION(
			ID, DIAGRAM_ID, VERSION_NO, VERSION_NAME, VERSION_TIME, VERSION_DESC,
			NAME, USER_ID, DIR_ID, DIAGRAM_DESC, DIAGRAM_SVG,
			DIAGRAM_XML, DIAGRAM_JSON, ICON_1, ICON_2, ICON_3,
			ICON_4, ICON_5, STATUS, CI_3D_POINT, CI_3D_POINT2,
			VERSION_DESC_PATH, DOMAIN_ID, DATA_STATUS, CREATOR, MODIFIER,
			CREATE_TIME, MODIFY_TIME, DIAGRAM_BG_CSS, RELEASE_ID, RELEASE_VERSION, AUTO_CREAT)
		values (
				   #{record.id:BIGINT}, #{record.diagramId:BIGINT}, #{record.versionNo,jdbcType=VARCHAR}, #{record.versionName,jdbcType=VARCHAR}, #{record.versionTime:BIGINT}, #{record.versionDesc,jdbcType=VARCHAR},
				   #{record.name,jdbcType=VARCHAR}, #{record.userId:BIGINT}, #{record.dirId:BIGINT}, #{record.diagramDesc,jdbcType=VARCHAR}, #{record.diagramSvg,jdbcType=VARCHAR},
				   #{record.diagramXml,jdbcType=VARCHAR}, #{record.diagramJson,jdbcType=VARCHAR}, #{record.icon1,jdbcType=VARCHAR}, #{record.icon2,jdbcType=VARCHAR}, #{record.icon3,jdbcType=VARCHAR},
				   #{record.icon4,jdbcType=VARCHAR}, #{record.icon5,jdbcType=VARCHAR}, #{record.status:INTEGER}, #{record.ci3dPoint,jdbcType=VARCHAR}, #{record.ci3dPoint2,jdbcType=VARCHAR},
				   #{record.versionDescPath,jdbcType=VARCHAR}, #{record.domainId:BIGINT}, #{record.dataStatus:INTEGER}, #{record.creator,jdbcType=VARCHAR}, #{record.modifier,jdbcType=VARCHAR},
				   #{record.createTime:BIGINT}, #{record.modifyTime:BIGINT}, #{record.diagramBgCss,jdbcType=VARCHAR}, #{record.releaseId:BIGINT}, #{record.releaseVersion:BIGINT}, #{record.autoCreat:BIGINT})
	</insert>




	<update id="updateById" parameterType="java.util.Map">
		update VC_DIAGRAM_VERSION
		<set>
			<include refid="VC_DIAGRAM_VERSION.sql_update_columns"/>
		</set>
		where ID = #{id:BIGINT}
	</update>
	<update id="updateByCdt" parameterType="java.util.Map">
		update VC_DIAGRAM_VERSION
		<set>
			<include refid="VC_DIAGRAM_VERSION.sql_update_columns"/>
		</set>
		<where>
			<include refid="VC_DIAGRAM_VERSION.sql_query_where"/>
		</where>
	</update>





	<delete id="deleteById" parameterType="java.util.Map">
		delete from VC_DIAGRAM_VERSION where ID = #{id:BIGINT}
	</delete>
	<delete id="deleteByCdt" parameterType="java.util.Map">
		delete from VC_DIAGRAM_VERSION
		<where>
			<include refid="VC_DIAGRAM_VERSION.sql_query_where"/>
		</where>
	</delete>




</mapper>