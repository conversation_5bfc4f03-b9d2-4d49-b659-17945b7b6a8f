package com.uinnova.product.eam.model;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.comm.model.es.EamArtifact;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class EamArtifactVo extends EamArtifact {

    @Comment("图片路径")
    private String resPath;

    @Comment("图片id")
    private Long fileId;

    @Comment("制品类型分类-字典表名称")
    private String artifactTypeName;

    @Comment("创建人-中文")
    private String creatorName;

    @Comment("修改人")
    private String modifier;

    @Comment("制品是否支持拖入相同的资产在画布上")
    private Boolean isSupportDragAsset;

    @Comment("制品资产面板配置")
    private String assetPanelConfig = "shape";
}
