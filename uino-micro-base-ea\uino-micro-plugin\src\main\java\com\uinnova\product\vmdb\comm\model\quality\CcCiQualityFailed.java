package com.uinnova.product.vmdb.comm.model.quality;

import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;

/**
 * 
 * <AUTHOR>
 *
 */
@Comment("CI数据质量不合格表[CC_CI_QUALITY_FAILED]")
public class CcCiQualityFailed implements EntityBean {
    private static final long serialVersionUID = 1L;

    @Comment("ID[ID]")
    private Long id;

    @Comment("所属统计ID[SUM_ID]    所属图标ID")
    private Long sumId;

    @Comment("统计规则[RULE_ID]")
    private Long ruleId;

    @Comment("规则类型[RULE_TYPE]    规则类型:10=完整性 11=合规性 12=准确性")
    private Integer ruleType;

    @Comment("规则子类型[RULE_SUB_TYPE]    规则子类型:1201=过期 1202=孤儿")
    private Integer ruleSubType;

    @Comment("统计时间[SUM_TIME]")
    private Long sumTime;

    @Comment("统计分类[SUM_CLASS_ID]")
    private Long sumClassId;

    @Comment("不合格CI_ID[CI_ID]    CI分类")
    private Long ciId;

    @Comment("不合格CI_CODE[CI_CODE]")
    private String ciCode;

    @Comment("所属域[DOMAIN_ID]")
    private Long domainId;

    @Comment("数据状态[DATA_STATUS]    数据状态:0=删除，1=正常")
    private Integer dataStatus;

    @Comment("创建时间[CREATE_TIME]    创建时间:yyyyMMddHHmmss")
    private Long createTime;

    @Comment("更新时间[MODIFY_TIME]    更新时间:yyyyMMddHHmmss")
    private Long modifyTime;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getSumId() {
        return this.sumId;
    }

    public void setSumId(Long sumId) {
        this.sumId = sumId;
    }

    public Long getRuleId() {
        return this.ruleId;
    }

    public void setRuleId(Long ruleId) {
        this.ruleId = ruleId;
    }

    public Integer getRuleType() {
        return this.ruleType;
    }

    public void setRuleType(Integer ruleType) {
        this.ruleType = ruleType;
    }

    public Integer getRuleSubType() {
        return this.ruleSubType;
    }

    public void setRuleSubType(Integer ruleSubType) {
        this.ruleSubType = ruleSubType;
    }

    public Long getSumTime() {
        return this.sumTime;
    }

    public void setSumTime(Long sumTime) {
        this.sumTime = sumTime;
    }

    public Long getSumClassId() {
        return this.sumClassId;
    }

    public void setSumClassId(Long sumClassId) {
        this.sumClassId = sumClassId;
    }

    public Long getCiId() {
        return this.ciId;
    }

    public void setCiId(Long ciId) {
        this.ciId = ciId;
    }

    public String getCiCode() {
        return this.ciCode;
    }

    public void setCiCode(String ciCode) {
        this.ciCode = ciCode;
    }

    public Long getDomainId() {
        return this.domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }

    public Integer getDataStatus() {
        return this.dataStatus;
    }

    public void setDataStatus(Integer dataStatus) {
        this.dataStatus = dataStatus;
    }

    public Long getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getModifyTime() {
        return this.modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }

}
