package com.uino.provider.feign.cmdb;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.binary.jdbc.Page;
import com.uino.bean.cmdb.base.ESRltRuleInfo;
import com.uino.provider.feign.config.BaseFeignConfig;

/**
 * <AUTHOR>
 * @data 2019/8/1 15:55.
 */
@Component
@FeignClient(name = "${feign-server-name:tarsier-platform}", path = "${feign-server-path:tarsier-platform}/feign/rltRule", configuration = {
        BaseFeignConfig.class})
public interface RltRuleFeign {

    /**
     * 保存关系规则
     * 
     * @param rltRuleInfo
     * @return
     */
    @PostMapping("saveOrUpdate")
    public Long saveOrUpdate(@RequestBody ESRltRuleInfo rltRuleInfo);

    /**
     * 根据id删除
     * 
     * @param id
     * @return 0:失败，1：成功
     */
    @PostMapping("delete")
    public int deleteById(@RequestBody Long id);

    /**
     * 根据id查询规则
     * 
     * @param id
     * @return
     */
    @PostMapping("getById")
    public ESRltRuleInfo queryInfoById(@RequestBody Long id);

    /**
     * 查询规则
     * 
     * @param domainId
     * @return
     */
    @PostMapping("getByDomainId")
    List<ESRltRuleInfo> queryInfo(@RequestBody Long domainId);

    /**
     * 分页查询规则
     * 
     * @param pageNum
     * @param pageSize
     * @param name
     * @param domainId
     * @param orders
     * @param isAsc
     * @return
     */
    @PostMapping("queryInfoPage")
    public Page<ESRltRuleInfo> queryInfoPage(@RequestParam(value = "pageNum") int pageNum, @RequestParam(value = "pageSize") int pageSize,
                                             @RequestParam(value = "name", required = false) String name, @RequestParam(value = "domainId") Long domainId,
                                             @RequestParam(value = "orders", required = false) String orders, @RequestParam(value = "isAsc", required = false) boolean isAsc);
}
