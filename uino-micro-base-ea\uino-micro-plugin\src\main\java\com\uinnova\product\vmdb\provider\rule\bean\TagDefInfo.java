package com.uinnova.product.vmdb.provider.rule.bean;

import com.uinnova.product.vmdb.comm.model.rule.CcCiTagDef;

import java.io.Serializable;
import java.util.List;

public class TagDefInfo implements Serializable {
	private static final long serialVersionUID = 1L;

	
	
	/** tag定义 **/
	private CcCiTagDef def;
	
	
	
	/** tag定义规则 **/
	private List<TagRuleInfo> rules;



	
	public CcCiTagDef getDef() {
		return def;
	}



	public void setDef(CcCiTagDef def) {
		this.def = def;
	}



	public List<TagRuleInfo> getRules() {
		return rules;
	}



	public void setRules(List<TagRuleInfo> rules) {
		this.rules = rules;
	}
	
	
	
	
	
	
	
	
}
