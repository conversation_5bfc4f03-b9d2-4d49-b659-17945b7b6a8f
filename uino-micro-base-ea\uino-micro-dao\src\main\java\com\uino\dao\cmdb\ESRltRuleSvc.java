package com.uino.dao.cmdb;

import com.uino.dao.AbstractESBaseDao;
import com.uino.dao.ESConst;
import com.uino.bean.cmdb.base.ESRltRuleInfo;
import com.uino.bean.cmdb.query.ESRltRuleBean;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;

/**
 * 关系遍历规则
 *
 * <AUTHOR>
 * @data 2019/7/31 11:10.
 */
@Service
public class ESRltRuleSvc extends AbstractESBaseDao<ESRltRuleInfo, ESRltRuleBean> {

    @Override
    public String getIndex() {
        return ESConst.INDEX_CMDB_RLT_RULE;
    }

    @Override
    public String getType() {
        return ESConst.INDEX_CMDB_RLT_RULE;
    }

    @PostConstruct
    public void init() {
        super.initIndex();

    }
}
