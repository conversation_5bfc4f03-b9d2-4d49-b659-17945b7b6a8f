package com.uinnova.product.eam.comm.model.es;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 元模型3D全景配置
 * <AUTHOR>
 */
@Data
@Comment("[uino_eam_model_panorama_3d]")
public class ModelPanorama3D implements Serializable {

    @Comment("主键")
    private Long id;
    
    @Comment("元模型id")
    private Long modelId;
    
    @Comment("3D分层页面")
    private List<String> pages3d;
    
    @Comment("2D全景图")
    private String page2d;
    
    @Comment("层间关系")
    private List<String> linkList;
    
    @Comment("渲染效果配置")
    private List<EffectConfig> effectConfigs;
    
    @Comment("创建人")
    private String creator;
    
    @Comment("创建时间")
    private Long createTime;
    
    @Comment("修改人")
    private String modifier;
    
    @Comment("修改时间")
    private Long modifyTime;
    
    @Comment("领域ID")
    private Long domainId;
    
    /**
     * 3D图例配置
     */
    @Data
    public static class EffectConfig implements Serializable {
        @Comment("颜色")
        private String effect;
        
        @Comment("分类code")
        private List<String> assets;
    }
}
