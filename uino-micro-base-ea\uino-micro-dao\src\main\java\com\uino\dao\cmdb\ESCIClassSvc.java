package com.uino.dao.cmdb;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.binary.core.exception.MessageException;
import com.binary.core.util.BinaryUtils;
import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClass;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uino.bean.cmdb.base.*;
import com.uino.bean.cmdb.base.ESCIHistoryInfo.ActionType;
import com.uino.bean.cmdb.enums.AttrNameKeyEnum;
import com.uino.bean.cmdb.query.ESAttrAggBean;
import com.uino.bean.sys.base.ESCIOperateLog;
import com.uino.dao.AbstractESBaseDao;
import com.uino.dao.ESConst;
import com.uino.dao.sys.ESCIOperateLogSvc;
import com.uino.dao.sys.ESDictionaryItemSvc;
import com.uino.dao.util.ESUtil;
import com.uino.util.sys.CheckAttrUtil;
import com.uino.util.sys.CommonFileUtil;
import com.uino.util.sys.SysUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.elasticsearch.action.admin.indices.settings.put.UpdateSettingsRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.indices.CreateIndexRequest;
import org.elasticsearch.client.indices.GetIndexRequest;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.sort.SortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import jakarta.annotation.PostConstruct;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * ES-CI分类服务
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class ESCIClassSvc extends AbstractESBaseDao<ESCIClassInfo, CCcCiClass> {

	Log logger = LogFactory.getLog(ESCIClassSvc.class);

	@Autowired
	@Lazy
	private ESCmdbCommSvc commSvc;

	@Autowired
	private ESCISvc esCiSvc;

	@Autowired
	private ESCIAttrTransConfigSvc attrConfigSvc;

	@Autowired
	private ESCIHistorySvc ciHistorySvc;

	@Autowired
	private ESCIOperateLogSvc logSvc;

	@Autowired
	private ESDictionaryItemSvc dictSvc;

	private String defaultIcon = "/122/default_icon.png";

	private final static String DEFAULT_3D_MODEL = "/122/default_3d.jpg";

	private final static String DEFAULT_PICTURE_ICON = "/122/defaultIcon/default_ci_picture_attr_icon.png";

	public String getDefaultIcon() {
		return defaultIcon;
	}

	public String getDefault3dModel() {
		return DEFAULT_3D_MODEL;
	}

	public String getDefaultPictureIcon() {
		return DEFAULT_PICTURE_ICON;
	}

	@Override
	public String getIndex() {
		return ESConst.INDEX_CMDB_CICLASS;
	}

	@Override
	public String getType() {
		return ESConst.INDEX_CMDB_CICLASS;
	}

	@PostConstruct
	public void init() {
		List<ESCIClassInfo> list = CommonFileUtil.getData("/initdata/uino_ciclass.json", ESCIClassInfo.class);
		super.initIndex(list);
	}

	@Override
	public Long saveOrUpdate(ESCIClassInfo esClsInfo) {
		// 校验属性合法性
		if (!BinaryUtils.isEmpty(esClsInfo.getId())) {
			this.checkAttrDef(esClsInfo);
		}
		if (esClsInfo.getOrderNo() == null) {
			// 设置orderNo
			esClsInfo.setOrderNo(getNextOrderNo(esClsInfo.getDirId(), esClsInfo.getParentId()));
		}
		// 属性转换
		this.saveAttrTransConfigs(Collections.singletonList(esClsInfo));
		return super.saveOrUpdate(esClsInfo);
	}

	/**
	 * 只适用于批量新增，未校验存量CI
	 */
	@Override
	public Integer saveOrUpdateBatch(List<ESCIClassInfo> list) {
		// 校验属性合法性
		// for (ESCIClassInfo esClsInfo : list) {
		// if (!BinaryUtils.isEmpty(esClsInfo.getId())) {
		// this.checkAttrDef(esClsInfo);
		// }
		// }
		// 属性转换
		this.saveAttrTransConfigs(list);
		// 设置orderNo
		Map<Long, Map<Long, List<ESCIClassInfo>>> groupByDirAndParent = list.stream()
				.filter(esClsInfo -> esClsInfo.getOrderNo() == null).collect(Collectors
						.groupingBy(ESCIClassInfo::getDirId, Collectors.groupingBy(ESCIClassInfo::getParentId)));
		groupByDirAndParent.forEach((dirId, data) -> data.forEach((parentId, classData) -> {
			AtomicInteger orderNo = new AtomicInteger(getNextOrderNo(dirId, parentId));
			classData.forEach(esClsInfo -> esClsInfo.setOrderNo(orderNo.getAndIncrement()));
		}));
		return super.saveOrUpdateBatch(list);
	}

	/**
	 * 初始化所有ci分类的orderNo
	 *
	 * @author: weixuesong
	 * @date: 2020/8/6 17:00
	 * @return: boolean
	 */
	public boolean initAllOrderNo(Long domainId) {
		AtomicInteger orderNo = new AtomicInteger((int) super.countByCondition(QueryBuilders.termQuery("domainId", domainId)));
		Page<ESCIClassInfo> page = super.getSortListByQuery(1, 9999, QueryBuilders.termQuery("domainId", domainId), "createTime", true);
		if (page.getData().isEmpty()) {
			return true;
		}
		page.getData().forEach(esCiClass -> esCiClass.setOrderNo(orderNo.getAndIncrement()));
		Integer result = super.saveOrUpdateBatch(page.getData());
		if (result == 1) {
			return true;
		}
		return false;
	}

	/**
	 * 获取下一个orderNo
	 *
	 * @param dirId
	 * @param parentId
	 * @author: weixuesong
	 * @date: 2020/8/6 10:45
	 * @return: int
	 */
	public int getNextOrderNo(long dirId, long parentId) {
		BoolQueryBuilder countQuery = QueryBuilders.boolQuery();
		countQuery.must(QueryBuilders.termQuery("dirId", dirId));
		countQuery.must(QueryBuilders.termQuery("parentId", parentId));
		List<SortBuilder<?>> sorts = new LinkedList<>();
		sorts.add(SortBuilders.fieldSort("orderNo").order(SortOrder.DESC).unmappedType("long"));
		sorts.add(SortBuilders.fieldSort("createTime").order(SortOrder.DESC));
		Page<ESCIClassInfo> page = super.getSortListByQuery(1, 1, QueryBuilders.constantScoreQuery(countQuery), sorts);
		if (page.getData().isEmpty()) {
			return 0;
		}
		if (page.getData().get(0).getOrderNo() == null) {
			return 0;
		}
		return page.getData().get(0).getOrderNo() + 1;
	}

	/**
	 * <b> 根据ID获取CI分类
	 *
	 * @param id 分类ID
	 * @return
	 */
	public CcCiClassInfo queryClassInfoById(Long id) {
		ESCIClassInfo ciClass = this.getById(id);
		return commSvc.tranCcCiClassInfo(ciClass);
	}

	/**
	 * <b>根据cdt条件获取CI分类
	 *
	 * @param pageNum
	 * @param pageSize
	 * @param cdt      自动生成的查询的对象
	 * @return
	 */
	public List<CcCiClassInfo> queryClassByCdt(int pageNum, int pageSize, CCcCiClass cdt) {
		if (cdt == null) {
			cdt = new CCcCiClass();
		}
		BoolQueryBuilder query = ESUtil.cdtToBuilder(cdt);
		Page<ESCIClassInfo> page = this.getListByQuery(pageNum, pageSize, query);
		List<CcCiClassInfo> list = new ArrayList<CcCiClassInfo>();
		List<ESCIClassInfo> datas = page.getData();
		for (ESCIClassInfo data : datas) {
			list.add(commSvc.tranCcCiClassInfo(data));
		}
		return list;
	}

	/**
	 * <b>根据cdt条件获取CI分类
	 *
	 * @param cdt 自动生成的查询的对象
	 * @return
	 */
	public List<CcCiClassInfo> queryClassByCdt(CCcCiClass cdt) {
		if (cdt == null) {
			cdt = new CCcCiClass();
		}
		Page<ESCIClassInfo> page = this.getSortListByQuery(1, 1000, ESUtil.cdtToBuilder(cdt), "id", true);
		List<ESCIClassInfo> datas = page.getData();
		if(BinaryUtils.isEmpty(datas)){
			return Collections.emptyList();
		}
		List<CcCiClassInfo> list = new ArrayList<>();
		Map<String, Long> clsCiCountMap = esCiSvc.groupByCountField("classId", QueryBuilders.matchAllQuery());
		for (ESCIClassInfo data : datas) {
			Long ciCount = clsCiCountMap.get(String.valueOf(data.getId()));
			ciCount = ciCount == null ? 0L : ciCount;
			CcCiClassInfo clsInfo = commSvc.tranCcCiClassInfo(data);
			clsInfo.setCiCount(ciCount);
			list.add(clsInfo);
		}
		return list;
	}

	/**
	 * 条件查询CI分类
	 *
	 * @param cdt
	 * @param orders 排序字段
	 * @param isAsc  是否升序
	 * @return
	 */
	public List<CcCiClassInfo> queryCiClassInfoList(CCcCiClass cdt, String orders, Boolean isAsc) {
		List<CcCiClassInfo> classInfos = new ArrayList<>();
		if (cdt == null) {
			cdt = new CCcCiClass();
		}
		orders = BinaryUtils.isEmpty(orders) ? "modifyTime" : orders;
		BoolQueryBuilder query = ESUtil.cdtToBuilder(cdt);
		orders = ESUtil.underlineToCamel(orders);
		List<ESCIClassInfo> esClassInfos = this.getSortListByQuery(1, 10000, query, orders, isAsc).getData();
		for (ESCIClassInfo esciClassInfo : esClassInfos) {
			classInfos.add(commSvc.tranCcCiClassInfo(esciClassInfo));
		}
		return classInfos;
	}

	/**
	 * <b>获取所有CI分类
	 *
	 * @return
	 */
	public List<CcCiClassInfo> queryAllClasses() {
		List<ESCIClassInfo> datas = this.getListByQueryScroll(QueryBuilders.boolQuery());
		List<CcCiClassInfo> list = new ArrayList<CcCiClassInfo>();
		for (ESCIClassInfo data : datas) {
			list.add(commSvc.tranCcCiClassInfo(data));
		}
		return list;
	}

	/**
	 * <b>获取类定义的字段对照表
	 *
	 * @param classId 分类id
	 * @return
	 */
	public Map<Long, String> getClassAttrField(Long classId) {
		Map<Long, String> map = new HashMap<Long, String>();
		ESCIClassInfo cls = this.getTargetAttrDefsByClassId(classId);
		for (ESCIAttrDefInfo def : cls.getAttrDefs()) {
			map.put(def.getId(), def.getProStdName());
		}
		// RestHighLevelClient client = getClient();
		// SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
		// SearchRequest searchRequest = new SearchRequest(getIndex());
		// searchRequest.types(getType());
		// TermQueryBuilder query = QueryBuilders.termQuery("id", classId);
		// sourceBuilder.query(query);
		// searchRequest.source(sourceBuilder);
		//
		// try {
		// SearchResponse response = client.search(searchRequest,
		// RequestOptions.DEFAULT);
		// SearchHit[] hits = response.getHits().getHits();
		// for (SearchHit hit : hits) {
		// String recordStr = hit.getSourceAsString();
		// JSONObject result = JSON.parseObject(recordStr);
		// JSONArray attrDefs = result.getJSONArray("attrDefs");
		// for (int i = 0; i < attrDefs.size(); i++) {
		// JSONObject attrs = attrDefs.getJSONObject(i);
		// Long id = attrs.getLong("id");
		// String name = attrs.getString("proStdName");
		// map.put(id, name);
		// }
		// }
		// } catch (Exception e) {
		// logger.error(e.getMessage(), e);
		// e.printStackTrace();
		// }
		return map;
	}

	/**
	 * <b>获取类定义的字段类型对照表(key:属性id,value:属性的类型)
	 *
	 * @param classId
	 * @return
	 */
	public Map<Long, Integer> getClassAttrFieldType(Long classId) {
		Map<Long, Integer> map = new HashMap<Long, Integer>();
		ESCIClassInfo cls = this.getTargetAttrDefsByClassId(classId);
		for (ESCIAttrDefInfo def : cls.getAttrDefs()) {
			map.put(def.getId(), def.getProType());
		}
		// RestHighLevelClient client = getClient();
		// SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
		// SearchRequest searchRequest = new SearchRequest(getIndex());
		// searchRequest.types(getType());
		// TermQueryBuilder query = QueryBuilders.termQuery("id", classId);
		// sourceBuilder.query(query);
		// searchRequest.source(sourceBuilder);
		//
		// try {
		// SearchResponse response = client.search(searchRequest,
		// RequestOptions.DEFAULT);
		// SearchHit[] hits = response.getHits().getHits();
		// for (SearchHit hit : hits) {
		// String recordStr = hit.getSourceAsString();
		// JSONObject result = JSON.parseObject(recordStr);
		// JSONArray attrDefs = result.getJSONArray("attrDefs");
		// for (int i = 0; i < attrDefs.size(); i++) {
		// JSONObject attrs = attrDefs.getJSONObject(i);
		// Long id = attrs.getLong("id");
		// Integer name = attrs.getInteger("proType");
		// map.put(id, name);
		// }
		// }
		// } catch (Exception e) {
		// logger.error(e.getMessage(), e);
		// e.printStackTrace();
		// }
		return map;
	}

	/**
	 * 根据id查询当前及父级分类的属性
	 *
	 * @param classId
	 * @return
	 */
	public List<CcCiAttrDef> getAllDefsByClassId(Long domainId, Long classId) {
		List<CcCiAttrDef> defs = new ArrayList<>();
		List<ESCIClassInfo> classInfos = this.getAllDefESClassInfosByClassIds(domainId, Collections.singletonList(classId));
		if (!BinaryUtils.isEmpty(classInfos)) {
			defs = classInfos.get(0).getCcAttrDefs();
		}
		return defs;
	}

	public List<ESCIClassInfo> getAllDefESClassInfosByClassIds(Long domainId, Collection<Long> classIds) {
		List<ESCIClassInfo> res = new ArrayList<>();
		if (!BinaryUtils.isEmpty(classIds)) {
			// 获取分类及其父类信息
			BoolQueryBuilder clsQuery = QueryBuilders.boolQuery();
			clsQuery.must(QueryBuilders.termQuery("domainId",domainId));
			clsQuery.should(QueryBuilders.termsQuery("id", classIds));
			clsQuery.should(QueryBuilders.termQuery("parentId", 0L));
			List<ESCIClassInfo> clsList = this.getListByQuery(clsQuery);
			this.transAttrDefsToShowName(clsList);
			Map<Long, ESCIClassInfo> clsMap = BinaryUtils.toObjectMap(clsList, "id");
			for (Long classId : classIds) {
				ESCIClassInfo cls = clsMap.get(classId);
				if (cls != null) {
					// 获取父类属性
					List<ESCIAttrDefInfo> defs = cls.getAttrDefs();
					if (cls.getParentId() != null && cls.getParentId() != 0L) {
						ESCIClassInfo parCls = clsMap.get(cls.getParentId());
						Assert.notNull(parCls, "父分类不存在");
						defs.addAll(parCls.getAttrDefs());
					}
					cls.setAttrDefs(defs);
					res.add(cls);
				}
			}
		}
		return res;
	}

	@Override
	public ESCIClassInfo getById(Long id) {
		ESCIClassInfo res = super.getById(id);
		if (res != null) {
			this.transAttrDefsToShowName(Collections.singletonList(res));
		}
		return res;
	}

	@Override
	public Map<String, Page<ESCIClassInfo>> getScrollByQuery(int pageNum, int pageSize, QueryBuilder query,
															 String sortField, boolean isAsc) {
		Map<String, Page<ESCIClassInfo>> res = super.getScrollByQuery(pageNum, pageSize, query, sortField, isAsc);
		res.forEach((sc, page) -> this.transAttrDefsToShowName(page.getData()));
		return res;
	}

	@Override
	public Page<ESCIClassInfo> getListByQuery(int pageNum, int pageSize, QueryBuilder query) {
		Page<ESCIClassInfo> res = super.getListByQuery(pageNum, pageSize, query);
		this.transAttrDefsToShowName(res.getData());
		return res;
	}

	@Override
	public List<ESCIClassInfo> selectListByQuery(int pageNum, int pageSize, QueryBuilder query) {
		List<ESCIClassInfo> list = super.selectListByQuery(pageNum, pageSize, query);
		this.transAttrDefsToShowName(list);
		return list;
	}

	public Map<Long,ESCIClassInfo> selectMapByQuery(int pageNum, int pageSize, QueryBuilder query) {
		List<ESCIClassInfo> list = super.selectListByQuery(pageNum, pageSize, query);
		this.transAttrDefsToShowName(list);
		return list.stream().collect(Collectors.toMap(ESCIClassInfo::getId, each -> each, (k1, k2) -> k2));
	}

	@Override
	public List<ESCIClassInfo> getListByQuery(QueryBuilder query) {
		List<ESCIClassInfo> res = super.getListByQuery(query);
		this.transAttrDefsToShowName(res);
		return res;
	}

	@Override
	public List<ESCIClassInfo> getListByQueryScroll(QueryBuilder query) {
		List<ESCIClassInfo> res = super.getListByQueryScroll(query);
		this.transAttrDefsToShowName(res);
		return res;
	}

	@Override
	public Page<ESCIClassInfo> getSortListByQuery(int pageNum, int pageSize, QueryBuilder query, String sortField,
												  boolean isAsc) {
		Page<ESCIClassInfo> res = super.getSortListByQuery(pageNum, pageSize, query, sortField, isAsc);
		this.transAttrDefsToShowName(res.getData());
		return res;
	}

	@Override
	public Page<ESCIClassInfo> getSortListByQuery(int pageNum, int pageSize, QueryBuilder query,
												  List<SortBuilder<?>> sorts) {
		Page<ESCIClassInfo> res = super.getSortListByQuery(pageNum, pageSize, query, sorts);
		this.transAttrDefsToShowName(res.getData());
		return res;
	}

	@Override
	public Page<ESCIClassInfo> getSortListByQuery(int pageNum, int pageSize, QueryBuilder searchQuery,
												  QueryBuilder sortQuery) {
		Page<ESCIClassInfo> res = super.getSortListByQuery(pageNum, pageSize, searchQuery, sortQuery);
		this.transAttrDefsToShowName(res.getData());
		return res;
	}

	@Override
	public Page<ESCIClassInfo> getSortListByCdt(int pageNum, int pageSize, CCcCiClass searchCdt, CCcCiClass sortCdt) {
		Page<ESCIClassInfo> res = super.getSortListByCdt(pageNum, pageSize, searchCdt, sortCdt);
		this.transAttrDefsToShowName(res.getData());
		return res;
	}

	@Override
	public Page<ESCIClassInfo> getListByCdt(int pageNum, int pageSize, CCcCiClass obj) {
		Page<ESCIClassInfo> res = super.getListByCdt(pageNum, pageSize, obj);
		this.transAttrDefsToShowName(res.getData());
		return res;
	}

	@Override
	public Page<ESCIClassInfo> getSortListByCdt(int pageNum, int pageSize, CCcCiClass obj, List<SortBuilder<?>> sorts) {
		Page<ESCIClassInfo> res = super.getSortListByCdt(pageNum, pageSize, obj, sorts);
		this.transAttrDefsToShowName(res.getData());
		return res;
	}

	@Override
	public Page<ESCIClassInfo> getSortListByCdt(int pageNum, int pageSize, CCcCiClass obj, String sortField,
												boolean isAsc) {
		Page<ESCIClassInfo> res = super.getSortListByCdt(pageNum, pageSize, obj, sortField, isAsc);
		this.transAttrDefsToShowName(res.getData());
		return res;
	}

	@Override
	public List<ESCIClassInfo> getListByCdt(CCcCiClass obj) {
		List<ESCIClassInfo> res = super.getListByCdt(obj);
		this.transAttrDefsToShowName(res);
		return res;
	}

	@Override
	public List<ESCIClassInfo> getSortListByCdt(CCcCiClass obj, List<SortBuilder<?>> sorts) {
		List<ESCIClassInfo> res = super.getSortListByCdt(obj, sorts);
		this.transAttrDefsToShowName(res);
		return res;
	}

	@Override
	public List<ESCIClassInfo> getListByScroll(String scrollId) {
		List<ESCIClassInfo> res = super.getListByScroll(scrollId);
		this.transAttrDefsToShowName(res);
		return res;
	}

	/**
	 * 校验属性合法性,若分类下已有数据，禁止修改属性
	 *
	 * @param esciClassInfo
	 */
	private void checkAttrDef(ESCIClassInfo esciClassInfo) {
		// Assert.notEmpty(esciClassInfo.getCcAttrDefs(),
		// "BS_FIELD_EMPTY_VAL${\"field\":\"attrDefs\"}");
		Long clsId = esciClassInfo.getId();
		// 根据分类id查询ci的个数，包括子类
		List<ESCIClassInfo> childCls = this.getListByQuery(QueryBuilders.termQuery("parentId", clsId));
		Set<Long> classIds = childCls.stream().filter(cls -> cls.getId() != null).map(ESCIClassInfo::getId)
				.collect(Collectors.toSet());
		classIds.add(clsId);
		BoolQueryBuilder query = QueryBuilders.boolQuery();
		query.should(QueryBuilders.termsQuery("classId", classIds));
		long clsCiCount = esCiSvc.countByCondition(query);
		if (clsCiCount > 0) {
			ESCIClassInfo oldCls = this.getById(clsId);
			// 新属性模板信息
			List<CcCiAttrDef> newDefs = esciClassInfo.getCcAttrDefs();
			newDefs = newDefs == null ? new LinkedList<>() : newDefs;
			Map<Long, CcCiAttrDef> newDefMap = BinaryUtils.toObjectMap(newDefs, "id");
			// 旧的属性模板相关信息
			List<CcCiAttrDef> oldDefs = oldCls.getCcAttrDefs();
			oldDefs = oldDefs == null ? new LinkedList<>() : oldDefs;
			// List<String> changeMajorNames = new ArrayList<>();
			// 验证旧属性合法性
			for (CcCiAttrDef def : oldDefs) {
				CcCiAttrDef newDef = newDefMap.get(def.getId());
				if (newDef != null) {
					boolean changeToMajor = def.getIsMajor() == 0 && newDef.getIsMajor() == 1;
					Assert.isTrue(!changeToMajor, "该分类或其子类下已有数据不允许新增主键");
					if (def.getIsMajor() == 1) {
						Assert.isTrue(this.equalsAttrDef(newDef, def), "该分类或其子类下已有数据不允许修改主键");
						Assert.isTrue(CheckAttrUtil.equalsForModel(newDef.getProType(), def.getProType()),
								"该分类或其子类下已有数据不允许修改主键类型");
					}
					boolean changeToRequired = def.getIsRequired() == 0 && newDef.getIsRequired() == 1;
					if (changeToRequired) {
						Assert.isTrue(this.getAttrMissingCount(clsId, def.getProName()) <= 0,
								"[" + newDef.getProName() + "]属性值缺失，不可设置为必填");
					}

					//图片不能转普通属性
					if (!def.getProType().equals(newDef.getProType()) && (def.getProType() == ESPropertyType.PICTURE.getValue() || newDef.getProType() == ESPropertyType.PICTURE.getValue())) {
						Assert.isTrue(false, "[" + newDef.getProName() + "]属性类型不能转为" + AttrNameKeyEnum.valueOf(newDef.getProType()).getValue());
					}
					//文档不能转普通属性
					if (!def.getProType().equals(newDef.getProType()) && (def.getProType() == ESPropertyType.DOCUMENT.getValue() || newDef.getProType() == ESPropertyType.DOCUMENT.getValue())) {
						Assert.isTrue(false, "[" + newDef.getProName() + "]属性类型不能转为" + AttrNameKeyEnum.valueOf(newDef.getProType()).getValue());
					}
				} else {
					Assert.isTrue(def.getIsMajor() != 1, "分类下已有数据不允许删除主键");
					// 清除CI属性值
					esCiSvc.clearCIAttrByProName(clsId, def.getProName());
				}
			}
			List<CcCiAttrDef> validList = newDefs.stream().filter(
					def -> def.getId() == null && ((def.getIsMajor() != null && def.getIsMajor() == 1)))
					.collect(Collectors.toList());
			Assert.isTrue(validList.size() <= 0, "该分类或其子类下已有数据不允许新增主键");
		}
	}

	/**
	 * 比较两个CI分类属性定义是否相同，用于修改分类时过滤未修改属性
	 *
	 * @param attrDef
	 * @return
	 */
	private boolean equalsAttrDef(CcCiAttrDef attrDef, CcCiAttrDef attrDef2) {
		if (attrDef == attrDef2) {
			return true;
		}
		return CheckAttrUtil.equalsForModel(attrDef.getId(), attrDef2.getId())
				&& CheckAttrUtil.equalsForModel(attrDef.getIsMajor(), attrDef2.getIsMajor())
				&& CheckAttrUtil.equalsForModel(attrDef.getIsRequired(), attrDef2.getIsRequired())
				&& CheckAttrUtil.equalsForModel(attrDef.getEnumValues(), attrDef2.getEnumValues());
	}

	private void transAttrDefsToShowName(List<ESCIClassInfo> esciClassInfos) {
		if (!BinaryUtils.isEmpty(esciClassInfos)) {
			Set<Long> clsIds = esciClassInfos.stream().filter(esCls -> esCls.getId() != null).map(ESCIClassInfo::getId)
					.collect(Collectors.toSet());
			List<ESCIAttrTransConfig> attrConfigs = attrConfigSvc
					.getListByQuery(QueryBuilders.termsQuery("classId", clsIds));
			Map<Object, ESCIAttrTransConfig> attrConfigMap = BinaryUtils.toObjectMap(attrConfigs, "defId");
			esciClassInfos.forEach(esciClassInfo -> {
				Assert.notNull(esciClassInfo.getId(), "分类id不能为空");
				List<ESCIAttrDefInfo> attrDefs = esciClassInfo.getAttrDefs();
				if (!BinaryUtils.isEmpty(attrDefs)) {
					attrDefs.forEach(def -> {
						ESCIAttrTransConfig attrConfig = attrConfigMap.get(def.getId());
						def.setClassId(esciClassInfo.getId());
						// 通过中间表映射属性
						if (!BinaryUtils.isEmpty(attrConfig)) {
							def.setProName(attrConfig.getShowName());
							def.setProStdName(attrConfig.getShowName().toUpperCase());
							if (attrConfig.getUpType() != 1) {
								def.setProType(attrConfig.getTargetAttrType());
							}
						}
						if(def.getProType().equals(150)&& StringUtils.isEmpty(def.getEnumValues())){
							def.setEnumValues("0");
						}
					});
				}
			});
		}
	}

	public ESCIClassInfo getSourceCIClassDefById(Long id) {
		return super.getById(id);
	}

	public List<ESCIClassInfo> getSourceCIClassDefByQuery(QueryBuilder query) {
		return super.getListByQuery(1, 3000, query).getData();
	}

	/**
	 * 获取分类实际属性对应关系
	 *
	 * @param classId
	 * @return
	 */
	public ESCIClassInfo getTargetAttrDefsByClassId(Long classId) {
		ESCIClassInfo res = this.getById(classId);
		Assert.notNull(res, "BS_MNAME_CLASS_NOT_EXSIT");
		List<CcCiAttrDef> defs = this.getAllDefsByClassId(res.getDomainId(), classId);
		Set<Long> defIds = defs.stream().map(CcCiAttrDef::getId).collect(Collectors.toSet());
		List<ESCIAttrTransConfig> attrConfigs = attrConfigSvc.getListByQuery(QueryBuilders.termsQuery("defId", defIds));
		Map<Long, ESCIAttrTransConfig> defMap = BinaryUtils.toObjectMap(attrConfigs, "defId");
		for (CcCiAttrDef def : defs) {
			def.setClassId(classId);
			ESCIAttrTransConfig attrConfig = defMap.get(def.getId());
			if (!BinaryUtils.isEmpty(attrConfig)) {
				// 通过中间表映射属性
				if (!BinaryUtils.isEmpty(attrConfig)) {
					def.setProName(attrConfig.getShowName());
					def.setProStdName(attrConfig.getSourceAttrName().toUpperCase());
					if (attrConfig.getUpType() > 1) {
						def.setProStdName(attrConfig.getTargetAttrName());
						def.setProType(attrConfig.getTargetAttrType());
					}
				}
			}
		}
		res.setCcAttrDefs(defs);
		return res;
	}

	/**
	 * 获取分类实际属性对应关系<br>
	 * classIds为空时返回全部分类
	 *
	 * @param classIds
	 * @return
	 */
	public List<ESCIClassInfo> getTargetAttrDefsByClassIds(Long domainId, Collection<Long> classIds) {
		List<ESCIClassInfo> res = new ArrayList<>();
		// if (!BinaryUtils.isEmpty(classIds)) {
		// 获取分类及其父类信息
		BoolQueryBuilder clsQuery = QueryBuilders.boolQuery();
		clsQuery.must(QueryBuilders.termQuery("domainId", domainId));
		if (!BinaryUtils.isEmpty(classIds)) {
			clsQuery.should(QueryBuilders.termsQuery("id", classIds));
			clsQuery.should(QueryBuilders.termQuery("parentId", 0L));
		}
		List<ESCIClassInfo> clsList = this.getListByQuery(clsQuery);
		Map<Long, ESCIClassInfo> clsMap = BinaryUtils.toObjectMap(clsList, "id");
		// 获取中间表数据，用于属性匹配
		List<ESCIAttrTransConfig> attrConfigs = attrConfigSvc
				.getListByQuery(QueryBuilders.termsQuery("classId", clsMap.keySet()));
		Map<Long, ESCIAttrTransConfig> defMap = BinaryUtils.toObjectMap(attrConfigs, "defId");
		for (ESCIClassInfo classInfo : clsList) {
			ESCIClassInfo cls = clsMap.get(classInfo.getId());
			if (cls != null && (BinaryUtils.isEmpty(classIds) || classIds.contains(classInfo.getId()))) {
				// 获取父类属性
				List<ESCIAttrDefInfo> defs = cls.getAttrDefs();
				if (cls.getParentId() != null && cls.getParentId() != 0L) {
					ESCIClassInfo parCls = clsMap.get(cls.getParentId());
					Assert.notNull(parCls, "父分类不存在");
					defs.addAll(parCls.getAttrDefs());
				}
				// 遍历分类所有属性，映射中间表
				for (CcCiAttrDef def : defs) {
					def.setClassId(classInfo.getId());
					ESCIAttrTransConfig attrConfig = defMap.get(def.getId());
					if (!BinaryUtils.isEmpty(attrConfig)) {
						// 通过中间表映射属性
						if (!BinaryUtils.isEmpty(attrConfig)) {
							def.setProName(attrConfig.getShowName());
							def.setProStdName(attrConfig.getSourceAttrName().toUpperCase());
							if (attrConfig.getUpType() > 1) {
								def.setProStdName(attrConfig.getTargetAttrName());
								def.setProType(attrConfig.getTargetAttrType());
							}
						}
					}
				}
				cls.setAttrDefs(defs);
				res.add(cls);
			}
		}
		// }
		return res;
	}

	private void saveAttrTransConfigs(List<ESCIClassInfo> esClsInfos) {
		// Assert.notEmpty(esClsInfo.getCcAttrDefs(), "属性定义不能为空");
		Map<String, String> sourceAttrDefNames = new HashMap<>();
		// 查询CI mapping,防止与定义过的属性重复，属性类型更改，增加新字段、类型，需要在中间表里创建对应关系
		Map<String, Object> mapping = esCiSvc.getCIMapping();
		Map<String, Map<String, Object>> mappingMap = JSON.parseObject(JSON.toJSONString(mapping.get("properties")),
				new TypeReference<Map<String, Map<String, Object>>>() {
				});
		try {
			Object object = mappingMap.get("attrs").get("properties");
			Map<String, Map<String, String>> parseObject = JSONObject.parseObject(JSON.toJSONString(object),
					new TypeReference<Map<String, Map<String, String>>>() {
					});
			parseObject.forEach((key, value) -> {
				String symbol = value.get("type");
				sourceAttrDefNames.put(key.toUpperCase(), symbol);
			});
			// sourceAttrDefNames.addAll(parseObject.keySet().stream().map(String::toUpperCase).collect(Collectors.toSet()));
		} catch (Exception e) {
			log.error("CI mapping 获取失败");
		}
		// 查询中间表数据，用于属性匹配
		Set<Long> classIds = esClsInfos.stream().filter(cls -> cls.getId() != null).map(ESCIClassInfo::getId)
				.collect(Collectors.toSet());
		List<ESCIAttrTransConfig> attrsConfigs = attrConfigSvc
				.getListByQuery(QueryBuilders.termsQuery("classId", classIds));
		Map<Long, ESCIAttrTransConfig> attrConfigMap = BinaryUtils.toObjectMap(attrsConfigs, "defId");
		// 查询分类属性，重复需记录中间表
		Map<String, Set<Long>> sourseDefNameToClassIdMap = new HashMap<>();
		List<ESCIClassInfo> sourceClsDefs = this.getSourceCIClassDefByQuery(QueryBuilders.boolQuery());
		Map<Long, ESCIClassInfo> clsMap = BinaryUtils.toObjectMap(sourceClsDefs, "id");
		for (ESCIClassInfo clsDef : sourceClsDefs) {
			for (ESCIAttrDefInfo def : clsDef.getAttrDefs()) {
				sourceAttrDefNames.put(def.getProStdName(), this.getSymbolByProType(def.getProType()));
				if (!sourseDefNameToClassIdMap.containsKey(def.getProStdName())) {
					sourseDefNameToClassIdMap.put(def.getProStdName(), new HashSet<>());
				}
				sourseDefNameToClassIdMap.get(def.getProStdName()).add(clsDef.getId());
				ESCIAttrTransConfig attrConfig = attrConfigMap.get(def.getId());
				if (!BinaryUtils.isEmpty(attrConfig)) {
					// 通过中间表映射属性
					if (!BinaryUtils.isEmpty(attrConfig)) {
						def.setProName(attrConfig.getShowName());
						def.setProStdName(attrConfig.getSourceAttrName().toUpperCase());
						if (attrConfig.getUpType() > 1) {
							def.setProStdName(attrConfig.getTargetAttrName());
							def.setProType(attrConfig.getTargetAttrType());
						}
					}
				}
				// sourceAttrDefNames.add(def.getProStdName());
			}
		}

		for (ESCIClassInfo esClsInfo : esClsInfos) {
			List<ESCIAttrTransConfig> attrConfigs = new ArrayList<>();
			List<ESCIInfo> historys = new ArrayList<>();
			List<ESCIOperateLog> ciLogs = new ArrayList<>();
			List<CcCiInfo> sourceCIInfos = null;
			if (BinaryUtils.isEmpty(esClsInfo.getId())) {
				esClsInfo.setId(ESUtil.getUUID());
			}
			Map<Long, CcCiAttrDef> oldDefMap = new HashMap<>();
			ESCIClassInfo oldCls = clsMap.get(esClsInfo.getId());
			if (oldCls != null) {
				oldDefMap = BinaryUtils.toObjectMap(oldCls.getCcAttrDefs(), "id");
			} else {
				clsMap.put(esClsInfo.getId(), esClsInfo);
			}
			for (CcCiAttrDef def : esClsInfo.getCcAttrDefs()) {
				// 兼容带id保存，但是新增属性的情况
				CcCiAttrDef oldDef = null;
				if (def.getId() == null) {
					def.setId(ESUtil.getUUID());
				} else {
					oldDef = oldDefMap.get(def.getId());
				}
				if (oldDef == null) {
					// def.setId(ESUtil.getUUID());
					String esSymbol = sourceAttrDefNames.get(def.getProStdName());
					if (esSymbol != null && !esSymbol.equals(getSymbolByProType(def.getProType()))) {
						// 同名，不同类型属性新建映射表
						ESCIAttrTransConfig attrConfig = ESCIAttrTransConfig.builder().classId(esClsInfo.getId())
								.defId(def.getId()).sourceAttrName(def.getProName()).sourceAttrType(def.getProType())
								.showName(def.getProName()).targetAttrName(ESUtil.getUUID() + "")
								.targetAttrType(def.getProType()).upType(2).domainId(esClsInfo.getDomainId()).build();
						attrConfigs.add(attrConfig);
					} else {
						// 同名，同类型、不同分类属性复用，不存映射表
						Set<Long> sourceClsIds = sourseDefNameToClassIdMap.get(def.getProStdName());
						if (!BinaryUtils.isEmpty(sourceClsIds) && sourceClsIds.contains(esClsInfo.getId())) {
							ESCIAttrTransConfig attrConfig = ESCIAttrTransConfig.builder().classId(esClsInfo.getId())
									.defId(def.getId()).sourceAttrName(def.getProName())
									.sourceAttrType(def.getProType()).showName(def.getProName())
									.targetAttrName(ESUtil.getUUID() + "").targetAttrType(def.getProType()).upType(2)
									.domainId(esClsInfo.getDomainId())
									.build();
							attrConfigs.add(attrConfig);
						}
					}
				} else {
					// 修改属性
					// CcCiAttrDef oldDef = oldDefMap.get(def.getId());
					// Assert.notNull(oldDef, "属性[" + def.getId() + "]不存在");
					// 修改属性名称或类型记录到中间表
					boolean isDefNameEqual = CheckAttrUtil.equalsForModel(oldDef.getProName(), def.getProName());
					boolean isDefTypeEqual = CheckAttrUtil.equalsForModel(oldDef.getProType(), def.getProType());
					if (!(isDefNameEqual && isDefTypeEqual)) {
						ESCIAttrTransConfig attrConfig = attrConfigMap.get(def.getId());
						if (BinaryUtils.isEmpty(attrConfig)) {
							attrConfig = ESCIAttrTransConfig.builder().classId(esClsInfo.getId()).defId(def.getId())
									.sourceAttrName(oldDef.getProName()).sourceAttrType(oldDef.getProType())
									.showName(def.getProName()).domainId(esClsInfo.getDomainId()).build();
						}
						if (!isDefNameEqual) {
							if (BinaryUtils.isEmpty(attrConfig.getUpType())) {
								attrConfig.setUpType(1);
							}
							attrConfig.setShowName(def.getProName());
						}
						if (!isDefTypeEqual) {
							QueryBuilder ciQuery = QueryBuilders.termQuery("classId", esClsInfo.getId());
							long count = esCiSvc.countByCondition(ciQuery);
							if (sourceCIInfos == null) {
								sourceCIInfos = esCiSvc
										.getCIInfoPageByQuery(1, new BigDecimal(count).intValue(), ciQuery, false)
										.getData();
							}
							List<ESCIClassInfo> childCls = this
									.getListByQuery(QueryBuilders.termQuery("parentId", esClsInfo.getId()));
							Set<Long> childIds = childCls.stream().filter(cls -> cls.getId() != null)
									.map(ESCIClassInfo::getId).collect(Collectors.toSet());
							childIds.add(esClsInfo.getId());
							String targetName = String.valueOf(ESUtil.getUUID());
							String constraintRule = def.getConstraintRule();
							if (def.getProType() == ESPropertyType.ENUM.getValue()) {
								constraintRule = def.getEnumValues();
							} else if (def.getProType() == ESPropertyType.DICT.getValue()) {
								@SuppressWarnings("unchecked")
								Long dictClassId = CiClassProDropSourceDefHelper.getCiClassProDropSourceClassId(def.getProDropSourceDef().trim());
								Long[] dictDefIds = CiClassProDropSourceDefHelper.getCiClassProDropSourceDefIds(def.getProDropSourceDef().trim());
								List<String> dictValues = dictSvc.getAttrValues(dictClassId, dictDefIds);
								constraintRule = JSON.toJSONString(dictValues);
							} else if (def.getProType() == ESPropertyType.EXTERNAL_ATTR.getValue()) {
								@SuppressWarnings("unchecked")
								Long ciClassId = CiClassProDropSourceDefHelper.getCiClassProDropSourceClassId(def.getProDropSourceDef().trim());
								Long[] ciDefIds = CiClassProDropSourceDefHelper.getCiClassProDropSourceDefIds(def.getProDropSourceDef().trim());
								ESCIClassInfo esciClassInfo = clsMap.get(ciClassId);
								if (esciClassInfo != null) {
									Map<Long, String> attrMap = esciClassInfo.getAttrDefs().stream().collect(Collectors.toMap(ESCIAttrDefInfo::getId, ESCIAttrDefInfo::getProName));
									List<String> attrNames = new ArrayList<>();
									for (Long ciDefId : ciDefIds) {
										String proName = attrMap.get(ciDefId);
										Assert.notNull(proName, "属性不存在");
										attrNames.add(proName);
									}
									long countByCondition = esCiSvc.countByCondition(QueryBuilders.termQuery("classId", esciClassInfo.getId()));
									constraintRule = "[]";
									if (countByCondition != 0L) {
										Page<String> page = esCiSvc.queryAttrVal(esciClassInfo.getDomainId(), ciClassId, attrNames, false, new ESAttrAggBean());
										constraintRule = JSON.toJSONString(page.getData());
									}
								} else {
									throw new MessageException("分类不存在");
								}
							}
							if (attrConfig.getUpType() != null && attrConfig.getUpType().longValue() > 1) {
								this.updateAttrValToNewAttr(childIds, attrConfig.getTargetAttrName(),
										attrConfig.getTargetAttrType(), targetName, def.getProType(), constraintRule,
										def.getDefVal());
							} else {
								this.updateAttrValToNewAttr(childIds, attrConfig.getSourceAttrName().toUpperCase(),
										attrConfig.getSourceAttrType(), targetName, def.getProType(), constraintRule,
										def.getDefVal());
							}
							attrConfig.setTargetAttrName(targetName);
							attrConfig.setTargetAttrType(def.getProType());
							attrConfig.setUpType(2);
						}
						// 不更改原属性名称，通过中间表匹配
						def.setProName(attrConfig.getSourceAttrName());
						def.setProStdName(attrConfig.getSourceAttrName().toUpperCase());
						// 不更改原属性类型，通过中间表匹配
						def.setProType(attrConfig.getSourceAttrType());
						attrConfigs.add(attrConfig);
					}
				}
				sourceAttrDefNames.put(def.getProName().toUpperCase(), this.getSymbolByProType(def.getProType()));
				// sourceAttrDefNames.add(def.getProName().toUpperCase());
			}
			// 先保存完属性映射，再保存历史版本，否则历史版本属性转换有问题
			if (!BinaryUtils.isEmpty(attrConfigs)) {
				attrConfigSvc.saveOrUpdateBatch(attrConfigs);
			}
			// 更改类型，记录历史版本与配置日志
			if (!BinaryUtils.isEmpty(sourceCIInfos)) {
				QueryBuilder ciQuery = QueryBuilders.termQuery("classId", esClsInfo.getId());
				long count = esCiSvc.countByCondition(ciQuery);
				List<ESCIInfo> esciInfos = esCiSvc.getListByQuery(1, new BigDecimal(count).intValue(), ciQuery)
						.getData();
				// List<CcCiInfo> targetCIInfos = esCiSvc.getCIInfoPageByQuery(1, new
				// BigDecimal(count).intValue(),
				// ciQuery, false).getData();
				Map<Long, ESCIInfo> targetCIMap = BinaryUtils.toObjectMap(esciInfos, "id");
				sourceCIInfos.forEach(sourceCiInfo -> {
					ESCIInfo targetEsInfo = targetCIMap.get(sourceCiInfo.getCi().getId());
					CcCiInfo targetCiInfo = commSvc.tranCcCiInfo(targetEsInfo, false);
					if (!CheckAttrUtil.checkAttrMapEqual(sourceCiInfo.getAttrs(), targetCiInfo.getAttrs())) {
						targetEsInfo.setVersion(targetEsInfo.getVersion() + 1);
						historys.add(targetEsInfo);
						ciLogs.add(ESCIOperateLogSvc.buildLogRecord(1L, SysUtil.StaticUtil.LOG_DYNAMIC_UPDATE,
								esClsInfo.getCcAttrDefs(), sourceCiInfo.getAttrs(), targetCiInfo.getAttrs(),
								esClsInfo.getClassName(), targetCiInfo.getCi()));
					}
				});
			}
			if (!BinaryUtils.isEmpty(historys)) {
				esCiSvc.transCIAttrs(historys, false);
				Set<Long> ids = historys.stream().map(ESCIInfo::getId).collect(Collectors.toSet());
				esCiSvc.updateByQuery(QueryBuilders.termsQuery("id", ids), "ctx._source.version+=1", false);
				ciHistorySvc.saveOrUpdateHistoryInfosBatch(historys, ActionType.SAVE_OR_UPDATE);
			}
			if (!BinaryUtils.isEmpty(ciLogs)) {
				logSvc.saveOrUpdateBatch(ciLogs);
			}
		}
	}

	private String getSymbolByProType(Integer proType) {
		ESPropertyType type = ESPropertyType.valueOf(proType);
		switch (type) {
			case PREFIX_INTEGER_CODE:
			case INTEGER_CODE:
			case DATE:
			case INTEGER:
				return "long";
			case DOUBLE:
				return "double";
			default:
				return "text";
		}
	}

	/**
	 * 将指定分类下原属性值复制到目标属性中，并按类型转换
	 *
	 * @param classIds
	 * @param sourceName
	 * @param sourceType
	 * @param targetName
	 * @param targetType
	 */
	private void updateAttrValToNewAttr(Set<Long> classIds, String sourceName, Integer sourceType, String targetName,
										Integer targetType, String constraintRule, String defValue) {
		String destAttrStr = "ctx._source.attrs['" + targetName + "']";
		String sourceAttrStr = "ctx._source.attrs['" + sourceName + "']";
		String sourceAttrVal = "String val=String.valueOf(" + sourceAttrStr + ");";
		if (sourceType == ESPropertyType.DOUBLE.getValue()) {
			sourceAttrVal = "String val=new BigDecimal(" + sourceAttrStr + ".toString()).toPlainString();";
		}
		if (sourceType == ESPropertyType.DATE.getValue()) {
			sourceAttrVal = "String val=new SimpleDateFormat('" + CheckAttrUtil.DATE_FORMAT_DEFAULT + "').format("
					+ sourceAttrStr + ");";
		}
		String condition = null;
		ESPropertyType type = ESPropertyType.valueOf(targetType);
		switch (type) {
			case INTEGER_CODE:
			case PREFIX_INTEGER_CODE:
			case INTEGER:
				if (sourceType == ESPropertyType.DOUBLE.getValue()) {
					condition = sourceAttrVal + destAttrStr
							+ "=Long.parseLong(val.lastIndexOf(\".\")!=-1?val.substring(0,val.lastIndexOf(\".\")):val)";
				} else {
					condition = sourceAttrVal + "if(/" + CheckAttrUtil.INTEGER_REGEX.toString()
							+ "/.matcher(val).matches()){" + destAttrStr + "=Long.parseLong(val)}else{" + destAttrStr
							+ "=null}";
				}
				break;
			case DOUBLE:
				condition = sourceAttrVal + "if(/" + CheckAttrUtil.DOUBLE_REGEX2.toString() + "/.matcher(val).matches()){"
						+ destAttrStr + "=new BigDecimal(val)}else{" + destAttrStr + "=null}";
				break;
			case ENCODE:
			case EXTERNAL_ATTR:
			case VARCHAR:
				condition = sourceAttrVal + destAttrStr + "=val.substring(0,val.length()<200?val.length():200)";
				break;
			case LONG_VARCHAR:
				condition = sourceAttrVal + destAttrStr + "=val.substring(0,val.length()<1000?val.length():1000)";
				break;
			case ATTACHMENT:
			case CLOB:
				// 此处不需要处理，直接转
				condition = sourceAttrVal + destAttrStr + "=val";
				break;
			case DATE:
				String dateAttrStr = "ctx._source.attrs['" + targetName + "_date']";
				condition = sourceAttrVal + "DateFormat format = new SimpleDateFormat('" + CheckAttrUtil.DATE_FORMAT_DEFAULT
						+ "');format.setLenient(false);DateFormat format2 = new SimpleDateFormat('yyyy-MM-dd');format2.setLenient(false);"
						+ destAttrStr + "=null;try{" + destAttrStr + "=format.parse(val).getTime();" + dateAttrStr
						+ "=val;}catch(Exception e){}if(" + destAttrStr + "==null){try{" + destAttrStr
						+ "=format2.parse(val).getTime();" + dateAttrStr + "=val;}catch(Exception e){}}";
				break;
			case ENUM:
			case PERSION:
			case ORGANIZATION:
			case DICT:
				condition = sourceAttrVal + "if(" + constraintRule + ".contains(val)){" + destAttrStr + "=val}else{"
						+ destAttrStr + "=null}";
				break;
			case PICTURE:
				condition = sourceAttrVal + destAttrStr + "=val";
				break;
			default:
				break;
		}
		String script = "if(" + sourceAttrStr + "!=null){" + condition + "}else{" + destAttrStr + "=null}";
		// 将原属性值复制到新属性中并做格式转换，并删除原属性
		esCiSvc.updateByQuery(QueryBuilders.termsQuery("classId", classIds),
				script + "ctx._source.attrs.remove('" + sourceName + "')", true);
		// 修改类型，把历史表数据一起刷了
		String historyScript = destAttrStr + "=" + sourceAttrStr + ";ctx._source.attrs.remove('" + sourceName + "')";
		if (sourceType == ESPropertyType.DATE.getValue()) {
			String dateSourceAttrStr = "ctx._source.attrs['" + sourceName + "_date']";
			historyScript = "if(" + dateSourceAttrStr + "!=null){" + destAttrStr + "=" + dateSourceAttrStr + "}else{"
					+ destAttrStr + "=" + sourceAttrStr + "}ctx._source.attrs.remove('" + sourceName + "')";
		}
		ciHistorySvc.updateByQuery(QueryBuilders.termsQuery("classId", classIds), historyScript, true);
	}

	public long getAttrMissingCount(Long classId, String proName) {
		ESCIClassInfo classInfo = this.getTargetAttrDefsByClassId(classId);
		for (ESCIAttrDefInfo def : classInfo.getAttrDefs()) {
			if (def.getProName().equals(proName)) {
				List<ESCIClassInfo> childCls = this.getListByQuery(QueryBuilders.termQuery("parentId", classId));
				Set<Long> classIds = childCls.stream().filter(cls -> cls.getId() != null).map(ESCIClassInfo::getId)
						.collect(Collectors.toSet());
				BoolQueryBuilder query = QueryBuilders.boolQuery();
				query.should(QueryBuilders.termsQuery("classId", classIds));
				query.mustNot(QueryBuilders.existsQuery("attrs." + def.getProStdName()));
				long count = esCiSvc.countByCondition(query);
				return count;
			}
		}
		return 0;
	}
}
