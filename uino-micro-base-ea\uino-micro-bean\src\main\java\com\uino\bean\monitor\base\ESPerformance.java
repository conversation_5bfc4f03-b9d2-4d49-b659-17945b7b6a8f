package com.uino.bean.monitor.base;

import java.io.Serializable;
import java.math.BigDecimal;

import lombok.Data;

/**
 * 性能
 * 
 * <AUTHOR>
 *
 */
@Data
public class ESPerformance implements Serializable {

    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /**
     * 唯一key
     * <p>
     * ${objId}_${kpiName}
     */
    private String uniqueKey;

    /**
     * 告警对象类型0:对象1:关系
     */
    private int objType;

    /**
     * 告警对象id alarmObjType为对象时为对象id，为关系时为关系id
     */
    private Long objId;

    /**
     * 产生告警分类的id alarmObjType为对象时为对象分类id，为关系时为关系分类id
     */
    private Long classId;

    /**
     * 指标名称
     */
    private String kpiName;

    /**
     * 对象来源名称
     */
    private String sourceObjName;

    /**
     * 性能值
     */
    private BigDecimal val;

    /**
     * 该性能对应时间,时间戳
     */
    private Long time;

    /**
     * 是否为模拟数据
     */
    private boolean mock;

    /**
     * 是否为最后一条
     */
    private boolean last;

    /** 所属域 */
    private Long domainId;

    /** 创建人 */
    private String creator;

    /** 修改人 */
    private String modifier;

    /**
     * 由于无法保证递增id，该创建时间同时会被各个server当作指针起点使用
     */
    private Long createTime;

    /** 修改时间 */
    private Long modifyTime;

    /**
     * 根据性能对象和指标构建唯一key
     */
    public void setUniqueKey() {
        this.uniqueKey = new String(this.getObjId() + "_" + this.getKpiName()).toUpperCase();
    }
}
