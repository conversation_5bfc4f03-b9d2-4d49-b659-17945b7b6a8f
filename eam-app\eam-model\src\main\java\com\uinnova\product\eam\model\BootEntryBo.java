package com.uinnova.product.eam.model;

import com.uinnova.product.eam.comm.model.es.BootEntryConfig;
import com.uinnova.product.eam.comm.model.es.BootEntryFunctionConfig;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class BootEntryBo  extends BootEntryConfig implements Serializable{

    private static final long serialVersionUID = -6427578855813973770L;
    private List<BootEntryFunctionConfig> bootEntryFunctionConfigList;
}
