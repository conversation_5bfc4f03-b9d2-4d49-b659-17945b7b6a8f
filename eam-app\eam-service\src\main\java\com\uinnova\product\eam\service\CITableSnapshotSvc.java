package com.uinnova.product.eam.service;

import com.uinnova.product.eam.model.CITableDataInfo;

import java.util.List;

/**
 *  ci数据表格临时数据接口
 */
public interface CITableSnapshotSvc {

    /**
     *  创建表格临时数据
     * @param assetKey
     * @param assetType
     * @param ciCodes
     * @return
     */
    Long saveorupdateCiTableSnapshot(String assetKey, Integer assetType, List<String> ciCodes);


    /**
     *  查询表格临时数据
     * @param assetKey
     * @param assetType
     * @param ciCodes
     */
    List<CITableDataInfo> getCiTableInfoByAssetKey(String assetKey, Integer assetType, List<String> ciCodes);

    /**
     *  删除表格临时数据
     * @param assetKey
     * @return
     */
    Long deleteCiTableSnapshotByAssetKey(String assetKey);
}
