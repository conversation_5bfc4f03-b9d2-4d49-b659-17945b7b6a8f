package com.uinnova.product.vmdb.provider.kpiTpl.bean;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.vmdb.comm.model.kpi.CcKpi;
import com.uinnova.product.vmdb.comm.model.kpiTpl.CcKpiTpl;
import com.uinnova.product.vmdb.comm.model.kpiTpl.CcKpiTplItem;

import java.io.Serializable;
import java.util.List;

public class CcKpiTplInfo implements Serializable{
	private static final long serialVersionUID = 1L;

	@Comment("KPI模版")
	private CcKpiTpl kpiTpl;
	
	@Comment("KPI模版下的KPI")
	private List<CcKpi> kpis;
	
	@Comment("对应指标模版项目")
	private List<CcKpiTplItem> tplItems;

	public CcKpiTpl getKpiTpl() {
		return kpiTpl;
	}

	public void setKpiTpl(CcKpiTpl kpiTpl) {
		this.kpiTpl = kpiTpl;
	}

	public List<CcKpi> getKpis() {
		return kpis;
	}

	public void setKpis(List<CcKpi> kpis) {
		this.kpis = kpis;
	}

	public List<CcKpiTplItem> getTplItems() {
		return tplItems;
	}

	public void setTplItems(List<CcKpiTplItem> tplItems) {
		this.tplItems = tplItems;
	}


	
	
	
}
