package com.uinnova.product.eam.base.diagram.model;




import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;


@Comment("组合视图表[DC_COMB_DIAGRAM]")
public class DcCombDiagram implements EntityBean {
	private static final long serialVersionUID = 1L;


	@Comment("ID[ID]")
	private Long id;


	@Comment("所属组合视图[COMB_DIAGRAM_ID]")
	private Long combDiagramId;


	@Comment("视图ID[DIAGRAM_ID]")
	private Long diagramId;


	@Comment("位置X[PX]")
	private Integer px;


	@Comment("位置Y[PY]")
	private Integer py;


	@Comment("方向[DIRECT]    0=水平 1=垂直")
	private Integer direct;


	@Comment("所属域[DOMAIN_ID]")
	private Long domainId;


	@Comment("数据状态[DATA_STATUS]    0=删除，1=正常")
	private Integer dataStatus;


	@Comment("创建时间[CREATE_TIME]    yyyyMMddHHmmss")
	private Long createTime;


	@Comment("更新时间[MODIFY_TIME]    yyyyMMddHHmmss")
	private Long modifyTime;




	public Long getId() {
		return this.id;
	}
	public void setId(Long id) {
		this.id = id;
	}


	public Long getCombDiagramId() {
		return this.combDiagramId;
	}
	public void setCombDiagramId(Long combDiagramId) {
		this.combDiagramId = combDiagramId;
	}


	public Long getDiagramId() {
		return this.diagramId;
	}
	public void setDiagramId(Long diagramId) {
		this.diagramId = diagramId;
	}


	public Integer getPx() {
		return this.px;
	}
	public void setPx(Integer px) {
		this.px = px;
	}


	public Integer getPy() {
		return this.py;
	}
	public void setPy(Integer py) {
		this.py = py;
	}


	public Integer getDirect() {
		return this.direct;
	}
	public void setDirect(Integer direct) {
		this.direct = direct;
	}


	public Long getDomainId() {
		return this.domainId;
	}
	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}


	public Integer getDataStatus() {
		return this.dataStatus;
	}
	public void setDataStatus(Integer dataStatus) {
		this.dataStatus = dataStatus;
	}


	public Long getCreateTime() {
		return this.createTime;
	}
	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}


	public Long getModifyTime() {
		return this.modifyTime;
	}
	public void setModifyTime(Long modifyTime) {
		this.modifyTime = modifyTime;
	}


}


