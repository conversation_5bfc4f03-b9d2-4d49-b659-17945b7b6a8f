package com.uino.service.sys.microservice.impl;

import com.binary.core.exception.MessageException;
import com.binary.jdbc.Page;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.sys.base.TenantDomain;
import com.uino.dao.BaseConst;
import com.uino.dao.permission.ESUserSvc;
import com.uino.dao.sys.ESTenantDomainSvc;
import com.uino.dao.util.ESUtil;
import com.uino.service.cmdb.microservice.ITaskLockSvc;
import com.uino.service.sys.microservice.ITenantDomainSvc;
import com.uino.util.sys.SysUtil;
import lombok.extern.log4j.Log4j;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.codec.digest.DigestUtils;
import org.elasticsearch.index.query.*;
import org.elasticsearch.search.sort.SortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.LinkedList;
import java.util.List;

@Slf4j
@Service
public class TenantDomainSvc implements ITenantDomainSvc {

    private static final long saveTenantDomainLockTime = 5 * 60 * 1000;

    private static final String lockName = "saveTenantDomain";

    @Autowired
    private ESTenantDomainSvc esTenantDomainSvc;

    @Autowired
    private ESUserSvc esUserSvc;

    @Autowired
    private ITaskLockSvc taskLockSvc;

    @Autowired
    private TenantDomainDataOperate tenantDomainDataOperate;

    @Override
    public Long saveOrUpdate(TenantDomain tenantDomain) {
        boolean isAdd = tenantDomain.getId() == null;
        if (!isAdd && tenantDomain.getId() == 1L) {
            throw new MessageException("默认租户不支持此操作");
        }
        String name = tenantDomain.getName();
        Assert.isTrue(name != null, "X_PARAM_NOT_NULL${name:name}");
        //校验重复
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        if (!isAdd) {
            query.mustNot(QueryBuilders.termQuery("id", tenantDomain.getId()));
        }
        query.must(QueryBuilders.termQuery("name.keyword", name));
        query.mustNot(QueryBuilders.termQuery("dataStatus", 0));
        List<TenantDomain> tenantDomains = esTenantDomainSvc.getListByQuery(query);
        if (!tenantDomains.isEmpty()) {
            throw new MessageException("租户域名称已存在");
        }
        if (isAdd) {
            boolean saveTenantDomainLock = taskLockSvc.getLock(lockName, saveTenantDomainLockTime);
            //获取锁失败
            if (!saveTenantDomainLock) {
                throw new MessageException("正在创建域,请稍后重试！");
            }
            long domainId = ESUtil.getUUID();
            tenantDomain.setId(domainId);
            tenantDomain.setCreator(BaseConst._SUPER_ADMIN_LOGIN_CODE);
            tenantDomain.setEnableStatus(-1);
            tenantDomain.setDataStatus(1);
            //先保存
            esTenantDomainSvc.saveOrUpdate(tenantDomain);
            //初始化
            try {
                log.info("开始初始化域数据。。。");
                tenantDomainDataOperate.initDomainData(domainId);
                log.info("初始化数据完成。。。");
            } catch (Exception e) {
                log.error("初始化数据失败", e);
                //初始化失败
                esTenantDomainSvc.deleteById(domainId);
                //todo:清除已经处理初始化资源

                throw new MessageException("初始化资源失败");
            } finally {
                taskLockSvc.breakLock(lockName);
            }
            tenantDomain.setEnableStatus(1);
        }

        Long id = esTenantDomainSvc.saveOrUpdate(tenantDomain);
        return id;
    }


    @Override
    public Page<TenantDomain> queryPage(int pageNum, int pageSize, String name) {
        List<SortBuilder<?>> sorts = new LinkedList<>();
        sorts.add(SortBuilders.fieldSort("createTime").order(SortOrder.DESC));
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("dataStatus", 1));
        query.mustNot(QueryBuilders.termQuery("enableStatus", -1));
        if (name != null) {
            query.must(QueryBuilders.multiMatchQuery(name.replaceAll("%", ""), "name")
                    .operator(Operator.AND).type(MultiMatchQueryBuilder.Type.PHRASE_PREFIX).lenient(true));

        }

        return esTenantDomainSvc.getSortListByQuery(pageNum, pageSize, query, sorts);
    }

    @Override
    public Long deleteById(Long id) {
        if (id == 1L) {
            throw new MessageException("默认用户不支持此操作");
        }
        TenantDomain tenantDomain = esTenantDomainSvc.getById(id);
        Assert.notNull(tenantDomain, "租户域不存在");
        tenantDomain.setDataStatus(0);
        return esTenantDomainSvc.saveOrUpdate(tenantDomain);
    }

    @Override
    public Long startOrStop(Long id) {
        if (id == 1L) {
            throw new MessageException("默认用户不支持此操作");
        }
        TenantDomain tenantDomain = esTenantDomainSvc.getById(id);
        Assert.notNull(tenantDomain, "租户域不存在");
        if (tenantDomain.getEnableStatus() == 0) {
            tenantDomain.setEnableStatus(1);
        } else {
            tenantDomain.setEnableStatus(0);
        }
        return esTenantDomainSvc.saveOrUpdate(tenantDomain);
    }

    @Override
    public boolean resetPasswdByAdmin(Long id) {
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("domainId", id));
        query.must(QueryBuilders.termQuery("loginCode.keyword", "admin"));
        List<SysUser> listByQuery = esUserSvc.getListByQuery(query);
        if (listByQuery.isEmpty()) {
            throw new MessageException("此租户域对应管理员用户不存在");
        }
        SysUser sysUser = listByQuery.get(0);
        sysUser.setLoginPasswd("240be518fabd2724ddb6f04eeb1da5967448d7e831c08c8fa822809f74c720a9");
        esUserSvc.saveOrUpdate(sysUser);
        return true;
    }

    @Override
    public List<TenantDomain> queryAvailableList() {
        BoolQueryBuilder querybuilder = QueryBuilders.boolQuery();
        querybuilder.filter(QueryBuilders.termQuery("dataStatus", 1));
        querybuilder.filter(QueryBuilders.termQuery("enableStatus", 1));
        Page<TenantDomain> page = esTenantDomainSvc.getSortListByQuery(1, 3000, querybuilder, "createTime", true);
        return page.getData();
    }
}
