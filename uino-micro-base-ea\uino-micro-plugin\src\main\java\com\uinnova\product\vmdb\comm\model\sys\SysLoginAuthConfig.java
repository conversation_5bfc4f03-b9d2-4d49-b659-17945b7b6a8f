package com.uinnova.product.vmdb.comm.model.sys;

import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;

/**
 * 
 * <AUTHOR>
 *
 */
@Comment("登录集成配置表[SYS_LOGIN_AUTH_CONFIG]")
public class SysLoginAuthConfig implements EntityBean {

    private static final long serialVersionUID = 1L;

    @Comment("ID[ID]")
    private Long id;

    @Comment("协议代码[PROTO_CODE]    协议代码: LDAP")
    private String protoCode;

    @Comment("协议名称[PROTO_NAME]")
    private String protoName;

    @Comment("协议描述[PROTO_DESC]")
    private String protoDesc;

    @Comment("协议配置[PROTO_PROPS]")
    private String protoProps;

    @Comment("协议状态[PROTO_STATUS]    协议状态:0=无效 1=有效")
    private Integer protoStatus;

    @Comment("是否选中[IS_ACTIVE]    是否选中:1=选中 0=没选中")
    private Integer isActive;

    @Comment("备用_1[CUSTOM_1]")
    private String custom1;

    @Comment("备用_2[CUSTOM_2]")
    private String custom2;

    @Comment("备用_3[CUSTOM_3]")
    private String custom3;

    @Comment("数据状态[DATA_STATUS]    数据状态:数据状态：1-正常 0-删除")
    private Integer dataStatus;

    @Comment("创建时间[CREATE_TIME]")
    private Long createTime;

    @Comment("修改时间[MODIFY_TIME]")
    private Long modifyTime;

    @Comment("配置映射[PROTO_DEFS]")
    private String protoDefs;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getProtoCode() {
        return this.protoCode;
    }

    public void setProtoCode(String protoCode) {
        this.protoCode = protoCode;
    }

    public String getProtoName() {
        return this.protoName;
    }

    public void setProtoName(String protoName) {
        this.protoName = protoName;
    }

    public String getProtoDesc() {
        return this.protoDesc;
    }

    public void setProtoDesc(String protoDesc) {
        this.protoDesc = protoDesc;
    }

    public String getProtoProps() {
        return this.protoProps;
    }

    public void setProtoProps(String protoProps) {
        this.protoProps = protoProps;
    }

    public Integer getProtoStatus() {
        return this.protoStatus;
    }

    public void setProtoStatus(Integer protoStatus) {
        this.protoStatus = protoStatus;
    }

    public Integer getIsActive() {
        return this.isActive;
    }

    public void setIsActive(Integer isActive) {
        this.isActive = isActive;
    }

    public String getCustom1() {
        return this.custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }

    public String getCustom2() {
        return this.custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }

    public String getCustom3() {
        return this.custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }

    public Integer getDataStatus() {
        return this.dataStatus;
    }

    public void setDataStatus(Integer dataStatus) {
        this.dataStatus = dataStatus;
    }

    public Long getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getModifyTime() {
        return this.modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getProtoDefs() {
        return this.protoDefs;
    }

    public void setProtoDefs(String protoDefs) {
        this.protoDefs = protoDefs;
    }
}
