-- 工作流77张表用这个进行主键删除
ALTER TABLE `act_app_appdef`
    DROP FOREIGN KEY `ACT_FK_APP_DEF_DPLY`;

ALTER TABLE `act_app_deployment_resource`
    DROP FOREIGN KEY `ACT_FK_APP_RSRC_DPL`;

ALTER TABLE `act_cmmn_casedef`
    DROP FOREIGN KEY `ACT_FK_CASE_DEF_DPLY`;

ALTER TABLE `act_cmmn_deployment_resource`
    DROP FOREIGN KEY `ACT_FK_CMMN_RSRC_DPL`;

ALTER TABLE `act_cmmn_ru_case_inst`
    DROP FOREIGN KEY `ACT_FK_CASE_INST_CASE_DEF`;

ALTER TABLE `act_cmmn_ru_mil_inst`
	DROP FOREIGN KEY `ACT_FK_MIL_CASE_DEF`,
	DROP FOREIGN KEY `ACT_FK_MIL_CASE_INST`;

ALTER TABLE `act_cmmn_ru_plan_item_inst`
    DROP FOREIGN KEY `ACT_FK_PLAN_ITEM_CASE_DEF`,
    DROP FOREIGN KEY `ACT_FK_PLAN_ITEM_CASE_INST`;

ALTER TABLE `act_cmmn_ru_sentry_part_inst`
    DROP FOREIGN KEY `ACT_FK_SENTRY_CASE_DEF`,
    DROP FOREIGN KEY `ACT_FK_SENTRY_CASE_INST`,
    DROP FOREIGN KEY `ACT_FK_SENTRY_PLAN_ITEM`;

ALTER TABLE `act_de_model_relation`
    DROP FOREIGN KEY `fk_relation_child`,
    DROP FOREIGN KEY `fk_relation_parent`;

ALTER TABLE `act_ge_bytearray`
    DROP FOREIGN KEY `ACT_FK_BYTEARR_DEPL`;

ALTER TABLE `act_id_membership`
    DROP FOREIGN KEY `ACT_FK_MEMB_GROUP`,
    DROP FOREIGN KEY `ACT_FK_MEMB_USER`;

ALTER TABLE `act_id_priv_mapping`
    DROP FOREIGN KEY `ACT_FK_PRIV_MAPPING`;

ALTER TABLE `act_procdef_info`
    DROP FOREIGN KEY `ACT_FK_INFO_JSON_BA`,
    DROP FOREIGN KEY `ACT_FK_INFO_PROCDEF`;

ALTER TABLE `act_re_model`
    DROP FOREIGN KEY `ACT_FK_MODEL_DEPLOYMENT`,
    DROP FOREIGN KEY `ACT_FK_MODEL_SOURCE`,
    DROP FOREIGN KEY `ACT_FK_MODEL_SOURCE_EXTRA`;

ALTER TABLE `act_ru_deadletter_job`
    DROP FOREIGN KEY `ACT_FK_DEADLETTER_JOB_CUSTOM_VALUES`,
    DROP FOREIGN KEY `ACT_FK_DEADLETTER_JOB_EXCEPTION`,
    DROP FOREIGN KEY `ACT_FK_DEADLETTER_JOB_EXECUTION`,
    DROP FOREIGN KEY `ACT_FK_DEADLETTER_JOB_PROCESS_INSTANCE`,
    DROP FOREIGN KEY `ACT_FK_DEADLETTER_JOB_PROC_DEF`;

ALTER TABLE `act_ru_event_subscr`
    DROP FOREIGN KEY `ACT_FK_EVENT_EXEC`;

ALTER TABLE `act_ru_execution`
    DROP FOREIGN KEY `ACT_FK_EXE_PARENT`,
    DROP FOREIGN KEY `ACT_FK_EXE_PROCDEF`,
    DROP FOREIGN KEY `ACT_FK_EXE_PROCINST`,
    DROP FOREIGN KEY `ACT_FK_EXE_SUPER`;

ALTER TABLE `act_ru_identitylink`
    DROP FOREIGN KEY `ACT_FK_ATHRZ_PROCEDEF`,
    DROP FOREIGN KEY `ACT_FK_IDL_PROCINST`,
    DROP FOREIGN KEY `ACT_FK_TSKASS_TASK`;

ALTER TABLE `act_ru_job`
    DROP FOREIGN KEY `ACT_FK_JOB_CUSTOM_VALUES`,
    DROP FOREIGN KEY `ACT_FK_JOB_EXCEPTION`,
    DROP FOREIGN KEY `ACT_FK_JOB_EXECUTION`,
    DROP FOREIGN KEY `ACT_FK_JOB_PROCESS_INSTANCE`,
    DROP FOREIGN KEY `ACT_FK_JOB_PROC_DEF`;

ALTER TABLE `act_ru_suspended_job`
    DROP FOREIGN KEY `ACT_FK_SUSPENDED_JOB_CUSTOM_VALUES`,
    DROP FOREIGN KEY `ACT_FK_SUSPENDED_JOB_EXCEPTION`,
    DROP FOREIGN KEY `ACT_FK_SUSPENDED_JOB_EXECUTION`,
    DROP FOREIGN KEY `ACT_FK_SUSPENDED_JOB_PROCESS_INSTANCE`,
    DROP FOREIGN KEY `ACT_FK_SUSPENDED_JOB_PROC_DEF`;

ALTER TABLE `act_ru_task`
    DROP FOREIGN KEY `ACT_FK_TASK_EXE`,
    DROP FOREIGN KEY `ACT_FK_TASK_PROCDEF`,
    DROP FOREIGN KEY `ACT_FK_TASK_PROCINST`;

ALTER TABLE `act_ru_timer_job`
    DROP FOREIGN KEY `ACT_FK_TIMER_JOB_CUSTOM_VALUES`,
    DROP FOREIGN KEY `ACT_FK_TIMER_JOB_EXCEPTION`,
    DROP FOREIGN KEY `ACT_FK_TIMER_JOB_EXECUTION`,
    DROP FOREIGN KEY `ACT_FK_TIMER_JOB_PROCESS_INSTANCE`,
    DROP FOREIGN KEY `ACT_FK_TIMER_JOB_PROC_DEF`;

ALTER TABLE `act_ru_variable`
    DROP FOREIGN KEY `ACT_FK_VAR_BYTEARRAY`,
    DROP FOREIGN KEY `ACT_FK_VAR_EXE`,
    DROP FOREIGN KEY `ACT_FK_VAR_PROCINST`;

