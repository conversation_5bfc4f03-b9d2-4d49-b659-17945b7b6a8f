package com.uinnova.product.eam.model.dto;

import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

/**
 * 我的关注数据接收
 */
@Data
public class AttentionDto implements Condition {

    /** 用户id */
    private Long userId;
    /** 关注的文件夹或视图id、方案id、ciId*/
    private String attentionId;
    /** 关注的架构类型，1：企业级架构资产，2：系统级架构资产  3：应用架构资产 */
    @Deprecated
    private Integer attentionBuild;
    /** 关注的文件类型：1:文件夹，2：视图 3:设计方案 4：ci资产*/
    private Integer attentionType;
    /** 0:取消关注，1：关注 */
    private Integer isFocus;
    /** 特殊视图 0：否  1:是 */
    private Integer specialView;
    /** 关注模糊搜索 */
    private String like;
    /** 所属文件夹id */
    private Long relationDirId;
    @Comment("0:设计空间，1:资产仓库")
    private Integer source;
    @Comment("关注id字符串类型，兼容资产ci导入导出id变化")
    private String attentionCode;
    @Comment("配置项id(资产管理配置)")
    private Long appSquareConfId;
}
