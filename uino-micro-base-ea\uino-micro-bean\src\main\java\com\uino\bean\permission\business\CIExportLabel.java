package com.uino.bean.permission.business;

import java.util.ArrayList;
import java.util.List;

import com.binary.core.i18n.Language;
import com.uinnova.product.vmdb.comm.util.SystemUtil;

/**
 * 
 * <AUTHOR>
 *
 */
public class CIExportLabel {

    private boolean isZhEnv = true;

    /**
     * 默认的使用中文作为导出对象
     */
    public CIExportLabel() {
        try {
            Language language = SystemUtil.getLoginUser().getLanguage();
            if (!Language.ZHC.equals(language)) {
                isZhEnv = false;
            }
        } catch (Exception e) {
            // 默认中文环境
        }
    }

    public CIExportLabel(Language lan) {
        if (!Language.ZHC.equals(lan)) {
            isZhEnv = false;
        }
    }

    public String getAttrFileName(boolean isTpl) {
        if (isTpl) {
            return isZhEnv ? "分类属性模板" : "Classification Attribute Template";
        }
        return isZhEnv ? "分类属性" : "Classification Attribute";
    }

    public String getImportTemplateName() {
        return isZhEnv ? "批量导入对象模板" : "Import CI Template";
    }

    public String getCIFileName(boolean isTpl) {
        if (isTpl) {
            return isZhEnv ? "对象定义" : "Classification Data Template";
        }
        return isZhEnv ? "分类数据" : "Classification Data";
    }

    public List<String> getCiClassAttrTemplate() {
        List<String> title = new ArrayList<>();
        title.add("CI Code");
        title.add(isZhEnv ? "属性名称1" : "Attribute Name 1");
        title.add(isZhEnv ? "属性名称2" : "Attribute Name 2");
        title.add(isZhEnv ? "属性名称3" : "Attribute Name 3");
        return title;
    }
    /**
     * 获取用户须知
     * 
     * @return
     */
    public List<String> getInfoAttention() {
        List<String> result = new ArrayList<>();
        if (isZhEnv) {
            result.add("填写须知");
            result.add("1、分类已存在则不能在该Excel表中对属性信息类别进行增加、删除或修改；");
            result.add("2、Excel中第一列默认为ciCode，存储系统中CI的唯一标识，全局唯一。可手动输入，若输入为空，则系统会自动生成一串16位编码。ciCode不作为分类属性，输入系统后不可更改。");
            result.add("3、支持多业务主键导入，在属性后添加[主键*],表示业务主键，其中*表示必填，业务主键默认必填，若用户未设定，则默认除ciCode外的第一列为业务主键");
            result.add("4、所有业务主键组成的字符串全局唯一。例如：两个CI中业务主键A,B对应的值分别为a,b和b,a，仍认为这两个CI重复。");
            result.add("5、通过导入创建的分类，所有属性值默认为字符串类型。");
        } else {
        }
        return result;
    }
}
