package com.uino.org.mvc;

import java.util.ArrayList;
import java.util.Arrays;

import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.binary.framework.web.RemoteResult;
import com.binary.jdbc.Page;
import com.uino.StartBaseWebAppliaction;
import com.uino.dao.permission.ESOrgSvc;
import com.uino.dao.permission.ESUserSvc;
import com.uino.dao.permission.rlt.ESUserOrgRltSvc;
import com.uino.bean.permission.base.SysOrg;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.permission.base.SysUserOrgRlt;
import com.uino.bean.permission.query.CSysOrg;

import lombok.extern.slf4j.Slf4j;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = { StartBaseWebAppliaction.class }, webEnvironment = WebEnvironment.RANDOM_PORT)
@Slf4j
@ActiveProfiles("provider-local")
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class GetOrgTreeTest {
	@Autowired
	private TestRestTemplate restTemplate;
	@MockBean
	private ESOrgSvc esOrgSvc;
	@MockBean
	private ESUserSvc esUserSvc;
	@MockBean
	private ESUserOrgRltSvc esUserOrgRltSvc;
	private static String testUrl;

	@BeforeClass
	public static void initCls() {
		GetOrgTreeTest.testUrl = "/permission/org/getTree";
	}

	@Test
	public void test01() {
		Page<SysOrg> orgPageResults = new Page<>(1, 3000, 0, 0, new ArrayList<>());
		Mockito.when(esOrgSvc.getListByCdt(Mockito.anyInt(), Mockito.anyInt(), Mockito.any(CSysOrg.class)))
				.thenReturn(orgPageResults);
		Mockito.when(esUserSvc.getListByCdt(Mockito.anyInt(), Mockito.anyInt(), Mockito.any()))
				.thenReturn(new Page<>(1, 1, 0, 1, new ArrayList<>()));
		Mockito.when(esUserOrgRltSvc.getListByQuery(Mockito.anyInt(), Mockito.anyInt(), Mockito.any()))
				.thenReturn(new Page<>(1, 1, 0, 1, new ArrayList<>()));
		Mockito.when(esUserSvc.getListByQuery(Mockito.anyInt(), Mockito.anyInt(), Mockito.any()))
				.thenReturn(new Page<>(1, 1, 0, 1, new ArrayList<>()));
		// test
		JSONArray requestBody = new JSONArray();
		String responseBodyStr = restTemplate.postForObject(testUrl, requestBody, String.class);
		RemoteResult responseBody = JSON.parseObject(responseBodyStr, RemoteResult.class);
		log.info("mvc测试结果：{}", responseBodyStr);
		Assert.assertTrue(responseBody.isSuccess());
	}

	@Test
	public void test02() {
		Page<SysOrg> orgPageResults = new Page<>(1, 3000, 0, 0,
				new ArrayList<>(Arrays.asList(SysOrg.builder().orgName("org01").id(1L).parentOrgId(0L).build(),
						SysOrg.builder().orgName("org02").id(2L).parentOrgId(0L).build(),
						SysOrg.builder().orgName("org01-01").id(3L).parentOrgId(1L).build(),
						SysOrg.builder().orgName("org01-02").id(4L).parentOrgId(1L).build())));
		Mockito.when(esOrgSvc.getListByCdt(Mockito.anyInt(), Mockito.anyInt(), Mockito.any(CSysOrg.class)))
				.thenReturn(orgPageResults);
		Mockito.when(esOrgSvc.getSortListByCdt(Mockito.any(), Mockito.anyList())).thenReturn(orgPageResults.getData());

		Mockito.when(esUserSvc.getListByCdt(Mockito.anyInt(), Mockito.anyInt(), Mockito.any()))
				.thenReturn(new Page<>(1, 1, 0, 1,
						new ArrayList<>(Arrays.asList(SysUser.builder().id(1L).userName("user01").build(),
								SysUser.builder().id(2L).userName("user02").build()))));
		Mockito.when(esUserOrgRltSvc.getListByQuery(Mockito.anyInt(), Mockito.anyInt(), Mockito.any()))
				.thenReturn(new Page<>(1, 1, 0, 1,
						new ArrayList<>(Arrays.asList(SysUserOrgRlt.builder().orgId(2L).userId(1L).build(),
								SysUserOrgRlt.builder().orgId(3L).userId(2L).build(),
								SysUserOrgRlt.builder().orgId(1L).userId(2L).build(),
								SysUserOrgRlt.builder().orgId(1L).userId(222L).build()))));
		Mockito.when(esUserSvc.getListByQuery(Mockito.anyInt(), Mockito.anyInt(), Mockito.any())).thenReturn(new Page<>(
				1, 1, 0, 1, new ArrayList<>(Arrays.asList(SysUser.builder().id(3L).userName("user03NoOrg").build()))));
		// test
		JSONArray requestBody = new JSONArray();
		String responseBodyStr = restTemplate.postForObject(testUrl, requestBody, String.class);
		RemoteResult responseBody = JSON.parseObject(responseBodyStr, RemoteResult.class);
		log.info("mvc测试结果：{}", responseBodyStr);
		Assert.assertTrue(responseBody.isSuccess());
	}
}
