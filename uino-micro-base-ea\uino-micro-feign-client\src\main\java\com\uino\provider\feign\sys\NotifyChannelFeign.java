package com.uino.provider.feign.sys;

import java.util.Collection;
import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.uino.bean.sys.base.NotifyChannel;
import com.uino.bean.sys.business.NotifyChannelReqDto;
import com.uino.bean.sys.business.NotifyData;
import com.uino.provider.feign.config.BaseFeignConfig;

@Component
@FeignClient(name = "${feign-server-name:tarsier-platform}", path = "${feign-server-path:tarsier-platform}/feign/sys/notify_channel", configuration = {BaseFeignConfig.class})
public interface NotifyChannelFeign {

    /**
     * 保存
     * 
     * @param saveInfo
     * @return
     */
    @PostMapping("save")
    public NotifyChannel save(@RequestBody NotifyChannel saveInfo);

    /**
     * 根据ids删除
     * 
     * @param ids
     */
    @PostMapping("delete")
    public void delete(@RequestBody Collection<Long> ids);

    /**
     * 查询
     * 
     * @param searchDto
     * @return
     */
    @PostMapping("search")
    public List<NotifyChannel> search(@RequestBody NotifyChannelReqDto searchDto);

    /**
     * 发送通知
     * 
     * @param notifyData
     * @return
     */
    @PostMapping("sendNotify")
    public boolean sendNotify(@RequestBody NotifyData notifyData);
}
