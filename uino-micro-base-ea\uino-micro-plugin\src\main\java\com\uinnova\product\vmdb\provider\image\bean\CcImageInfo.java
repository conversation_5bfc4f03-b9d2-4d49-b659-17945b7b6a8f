package com.uinnova.product.vmdb.provider.image.bean;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.vmdb.comm.model.image.CcImage;

import java.io.Serializable;

/**
 * 图标信息包含关联的3D图标信息
 * <AUTHOR>
 *
 */
public class CcImageInfo implements Serializable {
	private static final long serialVersionUID = 1L;
	
	
	@Comment("当前图标对象")
	private CcImage image;
	
	@Comment("关联的3D图标")
	private CcImage image3dOne;
	
	@Comment("关联的DMV3D图标")
	private CcImage image3dTwo;
	
	@Comment("关联的DCV3D图标")
	private CcImage image3dThree;

	public CcImage getImage() {
		return image;
	}

	public void setImage(CcImage image) {
		this.image = image;
	}

	public CcImage getImage3dOne() {
		return image3dOne;
	}

	public void setImage3dOne(CcImage image3dOne) {
		this.image3dOne = image3dOne;
	}

	public CcImage getImage3dTwo() {
		return image3dTwo;
	}

	public void setImage3dTwo(CcImage image3dTwo) {
		this.image3dTwo = image3dTwo;
	}

	public CcImage getImage3dThree() {
		return image3dThree;
	}

	public void setImage3dThree(CcImage image3dThree) {
		this.image3dThree = image3dThree;
	}



}
