package com.uinnova.product.eam.model.cj.vo;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.model.vo.VcDiagramDirVo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 文件夹 + 制品 + 方案
 * @author: Lc
 * @create: 2022-02-24 11:31
 */
@Data
public class DirDiagramPlanVO implements Serializable {

    @Comment("文件夹信息")
    private List<VcDiagramDirVo> childrenDirs;

    @Comment("制品+方案列表")
    private List<DiagramPlanVO> diagramPlanList;

}
