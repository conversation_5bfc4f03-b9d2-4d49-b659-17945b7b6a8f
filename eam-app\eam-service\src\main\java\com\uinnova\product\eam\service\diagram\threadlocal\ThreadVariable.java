package com.uinnova.product.eam.service.diagram.threadlocal;


import com.uino.bean.permission.base.SysUser;

/**
 * @description: 线程局部变量副本
 * @author: Lc
 * @create: 2021-11-25 10:25
 */
public class ThreadVariable {

    private static ThreadLocal<SysUser> threadLocal = new ThreadLocal<>();

    public static void setSysUser(SysUser sysUser) {
        if (sysUser != null) {
            threadLocal.set(sysUser);
        }
    }

    public static SysUser getSysUser() {
        SysUser sysUser = threadLocal.get();
        return sysUser;
    }

    public static void clearThreadLocal() {
        threadLocal.remove();
    }

}
