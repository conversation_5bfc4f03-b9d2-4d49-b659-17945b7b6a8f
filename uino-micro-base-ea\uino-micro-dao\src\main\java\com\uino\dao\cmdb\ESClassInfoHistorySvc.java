package com.uino.dao.cmdb;

import com.uino.bean.cmdb.base.ClassInfoHistory;
import com.uino.dao.AbstractESBaseDao;
import com.uino.dao.ESConst;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;

/**
 * description
 *
 * <AUTHOR>
 * @since 2025/4/14 13:47
 */
@Service
public class ESClassInfoHistorySvc extends AbstractESBaseDao<ClassInfoHistory, ClassInfoHistory> {

    @Override
    public String getIndex() {
        return ESConst.INDEX_CMDB_VISUALMODEL+"_class_history";
    }

    @Override
    public String getType() {
        return "_doc";
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }
}
