package com.uino.bean.cmdb.business;

import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import com.binary.jdbc.Page;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.uino.bean.cmdb.base.ESCIInfo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "CI树状图节点", description = "Ci树状图节点")
public class CITreeNode {

    /**
     * 节点名称
     */
    @ApiModelProperty(value = "节点名称", example = "fruit")
    private String nodeName;

    /**
     * 节点值
     */
    @ApiModelProperty(value = "节点值", example = "apple")
    private Object nodeVal;

    /**
     * 属性std名称
     */
    @ApiModelProperty(value = "属性std名称")
    @JsonIgnoreProperties
    private String attrStdName;

    /**
     * 子节点
     */
    @ApiModelProperty(value = "子节点")
    @Builder.Default
    private List<CITreeNode> children = new LinkedList<>();

    @ApiModelProperty(value = "父节点")
    @JsonIgnoreProperties
    private CITreeNode parent;

    /**
     * 层级-从0开始
     */
    @ApiModelProperty(value = "层级，从0开始", example = "0")
    private int level;

    /**
     * 节点下ci数量-包含所有子节点
     */
    @ApiModelProperty(value = "节点下ci数量,包含所有子节点", example = "1")
    private long ciNum;

    /**
     * 带分页的ci信息
     */
    @ApiModelProperty(value = "带分页的ci信息")
    private Page<ESCIInfo> ciPageInfo;

    /**
     * 设置忽略字段
     *
     * @param node
     */
    public static void ignoreColumn(CITreeNode node) {
        node.setParent(null);
        node.setAttrStdName(null);
        if (node.getChildren() != null && node.getChildren().size() > 0) {
            node.getChildren().forEach(child -> {
                CITreeNode.ignoreColumn(child);
            });
        }
    }

    /**
     * 记录ci数量(会为上级也加上)
     *
     * @param num
     * @param node
     */
    public static void recordCiNum(long num, CITreeNode node) {
        node.setCiNum(node.getCiNum() + num);
        if (node.getParent() != null) {
            CITreeNode.recordCiNum(num, node.getParent());
        }
    }

    /**
     * 构建querymap
     *
     * @param queryMap
     * @param node
     */
    public static void buildQueryMap(Map<String, Object> queryMap, CITreeNode node) {
        if (node.getLevel() == 0) {
            return;
        }
        queryMap.put(node.getAttrStdName(), node.getNodeVal());
        if (node.getParent() != null && node.getParent().getLevel() != 0) {
            CITreeNode.buildQueryMap(queryMap, node.getParent());
        }
    }
}
