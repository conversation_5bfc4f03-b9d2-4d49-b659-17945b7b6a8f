package com.binary.framework.critical.support;

import com.binary.framework.critical.CriticalObject;

public abstract class AbstractCriticalObject implements CriticalObject {
	
	
	private Object userObject;
	
	
	
	protected AbstractCriticalObject() {
	}
	
	
	
	
	@Override
	public void setUserObject(Object userObject) {
		this.userObject = userObject;
	}
	
	
	
	@Override
	public Object getUserObject() {
		return this.userObject;
	}
	
	
}
