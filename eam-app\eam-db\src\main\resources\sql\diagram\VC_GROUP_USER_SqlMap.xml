<?xml version="1.0" encoding="UTF-8"?><!--Converted at: Sat Dec 08 16:22:39 CST 2018-->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="VC_GROUP_USER">


	<resultMap id="queryResult" type="com.uinnova.product.eam.comm.model.VcGroupUser">
		<result property="id" column="ID" jdbcType="BIGINT"/>	<!-- ID -->
		<result property="groupId" column="GROUP_ID" jdbcType="BIGINT"/>	<!-- 组ID -->
		<result property="userId" column="USER_ID" jdbcType="BIGINT"/>	<!-- 用户ID -->
		<result property="authRegion" column="AUTH_REGION" jdbcType="INTEGER"/>	<!-- 权限范围 -->
		<result property="domainId" column="DOMAIN_ID" jdbcType="BIGINT"/>	<!-- 所属域 -->
		<result property="createTime" column="CREATE_TIME" jdbcType="BIGINT"/>	<!-- 创建时间 -->
		<result property="modifyTime" column="MODIFY_TIME" jdbcType="BIGINT"/>	<!-- 修改时间 -->
	</resultMap>
	

	<sql id="sql_query_where">
		<if test="cdt != null and cdt.id != null">and
			ID = #{cdt.id:BIGINT}
		</if>
		<if test="ids != null and ids != ''">and
			ID in (${ids})
		</if>
		<if test="cdt != null and cdt.startId != null">and
			 ID &gt;= #{cdt.startId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endId != null">and
			 ID &lt;= #{cdt.endId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.groupId != null">and
			GROUP_ID = #{cdt.groupId:BIGINT}
		</if>
		<if test="groupIds != null and groupIds != ''">and
			GROUP_ID in (${groupIds})
		</if>
		<if test="cdt != null and cdt.startGroupId != null">and
			 GROUP_ID &gt;= #{cdt.startGroupId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endGroupId != null">and
			 GROUP_ID &lt;= #{cdt.endGroupId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.userId != null">and
			USER_ID = #{cdt.userId:BIGINT}
		</if>
		<if test="userIds != null and userIds != ''">and
			USER_ID in (${userIds})
		</if>
		<if test="cdt != null and cdt.startUserId != null">and
			 USER_ID &gt;= #{cdt.startUserId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endUserId != null">and
			 USER_ID &lt;= #{cdt.endUserId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.authRegion != null">and
			AUTH_REGION = #{cdt.authRegion:INTEGER}
		</if>
		<if test="authRegions != null and authRegions != ''">and
			AUTH_REGION in (${authRegions})
		</if>
		<if test="cdt != null and cdt.startAuthRegion != null">and
			 AUTH_REGION &gt;= #{cdt.startAuthRegion:INTEGER} 
		</if>
		<if test="cdt != null and cdt.endAuthRegion != null">and
			 AUTH_REGION &lt;= #{cdt.endAuthRegion:INTEGER} 
		</if>
		<if test="cdt != null and cdt.domainId != null">and
			DOMAIN_ID = #{cdt.domainId:BIGINT}
		</if>
		<if test="domainIds != null and domainIds != ''">and
			DOMAIN_ID in (${domainIds})
		</if>
		<if test="cdt != null and cdt.startDomainId != null">and
			 DOMAIN_ID &gt;= #{cdt.startDomainId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endDomainId != null">and
			 DOMAIN_ID &lt;= #{cdt.endDomainId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.createTime != null">and
			CREATE_TIME = #{cdt.createTime:BIGINT}
		</if>
		<if test="createTimes != null and createTimes != ''">and
			CREATE_TIME in (${createTimes})
		</if>
		<if test="cdt != null and cdt.startCreateTime != null">and
			 CREATE_TIME &gt;= #{cdt.startCreateTime:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endCreateTime != null">and
			 CREATE_TIME &lt;= #{cdt.endCreateTime:BIGINT} 
		</if>
		<if test="cdt != null and cdt.modifyTime != null">and
			MODIFY_TIME = #{cdt.modifyTime:BIGINT}
		</if>
		<if test="modifyTimes != null and modifyTimes != ''">and
			MODIFY_TIME in (${modifyTimes})
		</if>
		<if test="cdt != null and cdt.startModifyTime != null">and
			 MODIFY_TIME &gt;= #{cdt.startModifyTime:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endModifyTime != null">and
			 MODIFY_TIME &lt;= #{cdt.endModifyTime:BIGINT} 
		</if>
	</sql>
	

	<sql id="sql_update_columns">
		<if test="record != null and record.id != null"> 
			ID = #{record.id:BIGINT}
		,</if>
		<if test="record != null and record.groupId != null"> 
			GROUP_ID = #{record.groupId:BIGINT}
		,</if>
		<if test="record != null and record.userId != null"> 
			USER_ID = #{record.userId:BIGINT}
		,</if>
		<if test="record != null and record.authRegion != null"> 
			AUTH_REGION = #{record.authRegion:INTEGER}
		,</if>
		<if test="record != null and record.domainId != null"> 
			DOMAIN_ID = #{record.domainId:BIGINT}
		,</if>
		<if test="record != null and record.createTime != null"> 
			CREATE_TIME = #{record.createTime:BIGINT}
		,</if>
		<if test="record != null and record.modifyTime != null"> 
			MODIFY_TIME = #{record.modifyTime:BIGINT}
		,</if>
	</sql>
	

	<sql id="sql_query_columns">
		ID, GROUP_ID, USER_ID, AUTH_REGION, DOMAIN_ID, CREATE_TIME, 
		MODIFY_TIME
	</sql>
	

	

	<select id="selectList" parameterType="java.util.Map" resultMap="queryResult">
		select 
			<include refid="VC_GROUP_USER.sql_query_columns"/>
		from VC_GROUP_USER 
			<where>
				<include refid="VC_GROUP_USER.sql_query_where"/>
			</where>
		order by 
			<if test="orders != null and orders != ''">
				${orders}
			</if>
			<if test="orders == null or orders == ''">
				ID
			</if>
	</select>
	<select id="selectCount" parameterType="java.util.Map" resultType="java.lang.Long">
		select count(1) from VC_GROUP_USER 
			<where>
				<include refid="VC_GROUP_USER.sql_query_where"/>
			</where>
	</select>
	<select id="selectById" parameterType="java.util.Map" resultMap="queryResult">
		select 
			<include refid="VC_GROUP_USER.sql_query_columns"/>
		from VC_GROUP_USER where ID=#{id:BIGINT} 
	</select>
	

	

	<insert id="insert" parameterType="java.util.Map">
		insert into VC_GROUP_USER(
			ID, GROUP_ID, USER_ID, AUTH_REGION, DOMAIN_ID, 
			CREATE_TIME, MODIFY_TIME)
		values (
			#{record.id:BIGINT}, #{record.groupId:BIGINT}, #{record.userId:BIGINT}, #{record.authRegion:INTEGER}, #{record.domainId:BIGINT}, 
			#{record.createTime:BIGINT}, #{record.modifyTime:BIGINT})
	</insert>
	

	

	<update id="updateById" parameterType="java.util.Map">
		update VC_GROUP_USER
			<set> 
				<include refid="VC_GROUP_USER.sql_update_columns"/> 
			</set>
		where ID = #{id:BIGINT}
	</update>
	<update id="updateByCdt" parameterType="java.util.Map">
		update VC_GROUP_USER
			<set> 
				<include refid="VC_GROUP_USER.sql_update_columns"/> 
			</set>
			<where> 
				<include refid="VC_GROUP_USER.sql_query_where"/> 
			</where>
	</update>
	
	

	

	<delete id="deleteById" parameterType="java.util.Map">
		delete from VC_GROUP_USER where ID = #{id:BIGINT}
	</delete>
	<delete id="deleteByCdt" parameterType="java.util.Map">
		delete from VC_GROUP_USER
			<where> 
				<include refid="VC_GROUP_USER.sql_query_where"/> 
			</where>
	</delete>
	



</mapper>