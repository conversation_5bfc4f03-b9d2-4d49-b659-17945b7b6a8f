<?xml version="1.0" encoding="UTF-8"?><!--Converted at: Thu Sep 19 16:29:50 CST 2019-->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="VC_DIAGRAM_OPERATION_RECORD">


	<resultMap id="queryResult" type="com.uinnova.product.eam.comm.model.VcDiagramOperationRecord">
		<result property="id" column="ID" jdbcType="BIGINT"/>	<!-- ID -->
		<result property="domainId" column="DOMAIN_ID" jdbcType="BIGINT"/>	<!-- 所属域 -->
		<result property="creator" column="CREATOR" jdbcType="VARCHAR"/>	<!-- 创建人 -->
		<result property="modifier" column="MODIFIER" jdbcType="VARCHAR"/>	<!-- 修改人 -->
		<result property="createTime" column="CREATE_TIME" jdbcType="BIGINT"/>	<!-- 创建时间 -->
		<result property="modifyTime" column="MODIFY_TIME" jdbcType="BIGINT"/>	<!-- 更新时间 -->
		<result property="userId" column="USER_ID" jdbcType="BIGINT"/>	<!-- 用户id -->
		<result property="diagramId" column="DIAGRAM_ID" jdbcType="BIGINT"/>	<!-- 视图id -->
		<result property="operationType" column="OPERATION_TYPE" jdbcType="INTEGER"/>	<!-- 操作类型 -->
	</resultMap>
	

	<sql id="sql_query_where">
		<if test="cdt != null and cdt.id != null">and
			ID = #{cdt.id:BIGINT}
		</if>
		<if test="ids != null and ids != ''">and
			ID in (${ids})
		</if>
		<if test="cdt != null and cdt.startId != null">and
			 ID &gt;= #{cdt.startId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endId != null">and
			 ID &lt;= #{cdt.endId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.domainId != null">and
			DOMAIN_ID = #{cdt.domainId:BIGINT}
		</if>
		<if test="domainIds != null and domainIds != ''">and
			DOMAIN_ID in (${domainIds})
		</if>
		<if test="cdt != null and cdt.startDomainId != null">and
			 DOMAIN_ID &gt;= #{cdt.startDomainId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endDomainId != null">and
			 DOMAIN_ID &lt;= #{cdt.endDomainId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.creator != null and cdt.creator != ''">and
			CREATOR like #{cdt.creator,jdbcType=VARCHAR} 
		</if>
		<if test="cdt != null and cdt.creatorEqual != null and cdt.creatorEqual != ''">and
			CREATOR = #{cdt.creatorEqual,jdbcType=VARCHAR}
		</if>
		<if test="creators != null and creators != ''">and
			CREATOR in (${creators})
		</if>
		<if test="cdt != null and cdt.modifier != null and cdt.modifier != ''">and
			MODIFIER like #{cdt.modifier,jdbcType=VARCHAR} 
		</if>
		<if test="cdt != null and cdt.modifierEqual != null and cdt.modifierEqual != ''">and
			MODIFIER = #{cdt.modifierEqual,jdbcType=VARCHAR}
		</if>
		<if test="modifiers != null and modifiers != ''">and
			MODIFIER in (${modifiers})
		</if>
		<if test="cdt != null and cdt.createTime != null">and
			CREATE_TIME = #{cdt.createTime:BIGINT}
		</if>
		<if test="createTimes != null and createTimes != ''">and
			CREATE_TIME in (${createTimes})
		</if>
		<if test="cdt != null and cdt.startCreateTime != null">and
			 CREATE_TIME &gt;= #{cdt.startCreateTime:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endCreateTime != null">and
			 CREATE_TIME &lt;= #{cdt.endCreateTime:BIGINT} 
		</if>
		<if test="cdt != null and cdt.modifyTime != null">and
			MODIFY_TIME = #{cdt.modifyTime:BIGINT}
		</if>
		<if test="modifyTimes != null and modifyTimes != ''">and
			MODIFY_TIME in (${modifyTimes})
		</if>
		<if test="cdt != null and cdt.startModifyTime != null">and
			 MODIFY_TIME &gt;= #{cdt.startModifyTime:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endModifyTime != null">and
			 MODIFY_TIME &lt;= #{cdt.endModifyTime:BIGINT} 
		</if>
		<if test="cdt != null and cdt.userId != null">and
			USER_ID = #{cdt.userId:BIGINT}
		</if>
		<if test="userIds != null and userIds != ''">and
			USER_ID in (${userIds})
		</if>
		<if test="cdt != null and cdt.startUserId != null">and
			 USER_ID &gt;= #{cdt.startUserId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endUserId != null">and
			 USER_ID &lt;= #{cdt.endUserId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.diagramId != null">and
			DIAGRAM_ID = #{cdt.diagramId:BIGINT}
		</if>
		<if test="diagramIds != null and diagramIds != ''">and
			DIAGRAM_ID in (${diagramIds})
		</if>
		<if test="cdt != null and cdt.startDiagramId != null">and
			 DIAGRAM_ID &gt;= #{cdt.startDiagramId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endDiagramId != null">and
			 DIAGRAM_ID &lt;= #{cdt.endDiagramId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.operationType != null">and
			OPERATION_TYPE = #{cdt.operationType:INTEGER}
		</if>
		<if test="operationTypes != null and operationTypes != ''">and
			OPERATION_TYPE in (${operationTypes})
		</if>
		<if test="cdt != null and cdt.startOperationType != null">and
			 OPERATION_TYPE &gt;= #{cdt.startOperationType:INTEGER} 
		</if>
		<if test="cdt != null and cdt.endOperationType != null">and
			 OPERATION_TYPE &lt;= #{cdt.endOperationType:INTEGER} 
		</if>
	</sql>
	

	<sql id="sql_update_columns">
		<if test="record != null and record.id != null"> 
			ID = #{record.id:BIGINT}
		,</if>
		<if test="record != null and record.domainId != null"> 
			DOMAIN_ID = #{record.domainId:BIGINT}
		,</if>
		<if test="record != null and record.creator != null"> 
			CREATOR = #{record.creator,jdbcType=VARCHAR}
		,</if>
		<if test="record != null and record.modifier != null"> 
			MODIFIER = #{record.modifier,jdbcType=VARCHAR}
		,</if>
		<if test="record != null and record.createTime != null"> 
			CREATE_TIME = #{record.createTime:BIGINT}
		,</if>
		<if test="record != null and record.modifyTime != null"> 
			MODIFY_TIME = #{record.modifyTime:BIGINT}
		,</if>
		<if test="record != null and record.userId != null"> 
			USER_ID = #{record.userId:BIGINT}
		,</if>
		<if test="record != null and record.diagramId != null"> 
			DIAGRAM_ID = #{record.diagramId:BIGINT}
		,</if>
		<if test="record != null and record.operationType != null"> 
			OPERATION_TYPE = #{record.operationType:INTEGER}
		,</if>
	</sql>
	

	<sql id="sql_query_columns">
		ID, DOMAIN_ID, CREATOR, MODIFIER, CREATE_TIME, MODIFY_TIME, 
		USER_ID, DIAGRAM_ID, OPERATION_TYPE
	</sql>
	

	

	<select id="selectList" parameterType="java.util.Map" resultMap="queryResult">
		select 
			<include refid="VC_DIAGRAM_OPERATION_RECORD.sql_query_columns"/>
		from VC_DIAGRAM_OPERATION_RECORD 
			<where>
				<include refid="VC_DIAGRAM_OPERATION_RECORD.sql_query_where"/>
			</where>
		order by 
			<if test="orders != null and orders != ''">
				${orders}
			</if>
			<if test="orders == null or orders == ''">
				ID
			</if>
	</select>
	<select id="selectCount" parameterType="java.util.Map" resultType="java.lang.Long">
		select count(1) from VC_DIAGRAM_OPERATION_RECORD 
			<where>
				<include refid="VC_DIAGRAM_OPERATION_RECORD.sql_query_where"/>
			</where>
	</select>
	<select id="selectById" parameterType="java.util.Map" resultMap="queryResult">
		select 
			<include refid="VC_DIAGRAM_OPERATION_RECORD.sql_query_columns"/>
		from VC_DIAGRAM_OPERATION_RECORD where ID=#{id:BIGINT} 
	</select>
	

	

	<insert id="insert" parameterType="java.util.Map">
		insert into VC_DIAGRAM_OPERATION_RECORD(
			ID, DOMAIN_ID, CREATOR, MODIFIER, CREATE_TIME, 
			MODIFY_TIME, USER_ID, DIAGRAM_ID, OPERATION_TYPE)
		values (
			#{record.id:BIGINT}, #{record.domainId:BIGINT}, #{record.creator,jdbcType=VARCHAR}, #{record.modifier,jdbcType=VARCHAR}, #{record.createTime:BIGINT}, 
			#{record.modifyTime:BIGINT}, #{record.userId:BIGINT}, #{record.diagramId:BIGINT}, #{record.operationType:INTEGER})
	</insert>
	

	

	<update id="updateById" parameterType="java.util.Map">
		update VC_DIAGRAM_OPERATION_RECORD
			<set> 
				<include refid="VC_DIAGRAM_OPERATION_RECORD.sql_update_columns"/> 
			</set>
		where ID = #{id:BIGINT}
	</update>
	<update id="updateByCdt" parameterType="java.util.Map">
		update VC_DIAGRAM_OPERATION_RECORD
			<set> 
				<include refid="VC_DIAGRAM_OPERATION_RECORD.sql_update_columns"/> 
			</set>
			<where> 
				<include refid="VC_DIAGRAM_OPERATION_RECORD.sql_query_where"/> 
			</where>
	</update>
	
	

	

	<delete id="deleteById" parameterType="java.util.Map">
		delete from VC_DIAGRAM_OPERATION_RECORD where ID = #{id:BIGINT}
	</delete>
	<delete id="deleteByCdt" parameterType="java.util.Map">
		delete from VC_DIAGRAM_OPERATION_RECORD
			<where> 
				<include refid="VC_DIAGRAM_OPERATION_RECORD.sql_query_where"/> 
			</where>
	</delete>
	



</mapper>