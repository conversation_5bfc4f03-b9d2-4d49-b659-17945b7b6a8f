package com.uinnova.product.vmdb.comm.model.ci;

import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;

@Comment("CI属性定义表[CC_CI_ATTR_DEF]")
public class CCcCiAttrDef implements Condition {
    private static final long serialVersionUID = 1L;

    @Comment("ID[ID] operate-Equal[=]")
    private Long id;

    @Comment("ID[ID] operate-In[in]")
    private Long[] ids;

    @Comment("ID[ID] operate-GTEqual[>=]")
    private Long startId;

    @Comment("ID[ID] operate-LTEqual[<=]")
    private Long endId;

    @Comment("CI分类ID[CLASS_ID] operate-Equal[=]")
    private Long classId;

    @Comment("CI分类ID[CLASS_ID] operate-In[in]")
    private Long[] classIds;

    @Comment("CI分类ID[CLASS_ID] operate-GTEqual[>=]")
    private Long startClassId;

    @Comment("CI分类ID[CLASS_ID] operate-LTEqual[<=]")
    private Long endClassId;

    @Comment("分类类型[CI_TYPE] operate-Equal[=]    分类类型:1=基础CI 2=关系CI")
    private Integer ciType;

    @Comment("分类类型[CI_TYPE] operate-In[in]    分类类型:1=基础CI 2=关系CI")
    private Integer[] ciTypes;

    @Comment("分类类型[CI_TYPE] operate-GTEqual[>=]    分类类型:1=基础CI 2=关系CI")
    private Integer startCiType;

    @Comment("分类类型[CI_TYPE] operate-LTEqual[<=]    分类类型:1=基础CI 2=关系CI")
    private Integer endCiType;

    @Comment("属性名[PRO_NAME] operate-Like[like]")
    private String proName;

    @Comment("属性名[PRO_NAME] operate-Equal[=]")
    private String proNameEqual;

    @Comment("属性名[PRO_NAME] operate-In[in]")
    private String[] proNames;

    @Comment("标准名[PRO_STD_NAME] operate-Like[like]    标准名:全部大写")
    private String proStdName;

    @Comment("标准名[PRO_STD_NAME] operate-Equal[=]    标准名:全部大写")
    private String proStdNameEqual;

    @Comment("标准名[PRO_STD_NAME] operate-In[in]    标准名:全部大写")
    private String[] proStdNames;

    @Comment("属性类型[PRO_TYPE] operate-Equal[=]    属性类型:1=整数 2=小数 3=短文本(<=200) 4=长文本(<=1000) 5=文章 6=枚举 7=日期 8=字典 9=外部属性")
    private Integer proType;

    @Comment("属性类型[PRO_TYPE] operate-In[in]    属性类型:1=整数 2=小数 3=短文本(<=200) 4=长文本(<=1000) 5=文章 6=枚举 7=日期 8=字典 9=外部属性")
    private Integer[] proTypes;

    @Comment("属性类型[PRO_TYPE] operate-GTEqual[>=]    属性类型:1=整数 2=小数 3=短文本(<=200) 4=长文本(<=1000) 5=文章 6=枚举 7=日期 8=字典 9=外部属性")
    private Integer startProType;

    @Comment("属性类型[PRO_TYPE] operate-LTEqual[<=]    属性类型:1=整数 2=小数 3=短文本(<=200) 4=长文本(<=1000) 5=文章 6=枚举 7=日期 8=字典 9=外部属性")
    private Integer endProType;

    @Comment("字典类型来源类型[PRO_DROP_SOURCE_TYPE] operate-Equal[=]    字典类型来源类型: 1=标签 2=CI分类")
    private Integer proDropSourceType;

    @Comment("字典类型来源类型[PRO_DROP_SOURCE_TYPE] operate-In[in]    字典类型来源类型: 1=标签 2=CI分类")
    private Integer[] proDropSourceTypes;

    @Comment("字典类型来源类型[PRO_DROP_SOURCE_TYPE] operate-GTEqual[>=]    字典类型来源类型: 1=标签 2=CI分类")
    private Integer startProDropSourceType;

    @Comment("字典类型来源类型[PRO_DROP_SOURCE_TYPE] operate-LTEqual[<=]    字典类型来源类型: 1=标签 2=CI分类")
    private Integer endProDropSourceType;

    @Comment("字典类型来源ID[PRO_DROP_SOURCE_ID] operate-Equal[=]")
    private Long proDropSourceId;

    @Comment("字典类型来源ID[PRO_DROP_SOURCE_ID] operate-In[in]")
    private Long[] proDropSourceIds;

    @Comment("字典类型来源ID[PRO_DROP_SOURCE_ID] operate-GTEqual[>=]")
    private Long startProDropSourceId;

    @Comment("字典类型来源ID[PRO_DROP_SOURCE_ID] operate-LTEqual[<=]")
    private Long endProDropSourceId;

    @Comment("外部属性[PRO_DROP_SOURCE_DEF] {classId:attrDefId}格式的JSON字符串")
    private String proDropSourceDef;

    @Comment("属性描述[PRO_DESC] operate-Like[like]")
    private String proDesc;

    @Comment("映射字段[MP_CI_FIELD] operate-Equal[=]")
    private Integer mpCiField;

    @Comment("映射字段[MP_CI_FIELD] operate-In[in]")
    private Integer[] mpCiFields;

    @Comment("映射字段[MP_CI_FIELD] operate-GTEqual[>=]")
    private Integer startMpCiField;

    @Comment("映射字段[MP_CI_FIELD] operate-LTEqual[<=]")
    private Integer endMpCiField;

    @Comment("是否主键[IS_MAJOR] operate-Equal[=]    是否主键:0=否，1=是")
    private Integer isMajor;

    @Comment("是否主键[IS_MAJOR] operate-In[in]    是否主键:0=否，1=是")
    private Integer[] isMajors;

    @Comment("是否主键[IS_MAJOR] operate-GTEqual[>=]    是否主键:0=否，1=是")
    private Integer startIsMajor;

    @Comment("是否主键[IS_MAJOR] operate-LTEqual[<=]    是否主键:0=否，1=是")
    private Integer endIsMajor;

    @Comment("是否必填[IS_REQUIRED] operate-Equal[=]    是否必填:0=否，1=是")
    private Integer isRequired;

    @Comment("是否必填[IS_REQUIRED] operate-In[in]    是否必填:0=否，1=是")
    private Integer[] isRequireds;

    @Comment("是否必填[IS_REQUIRED] operate-GTEqual[>=]    是否必填:0=否，1=是")
    private Integer startIsRequired;

    @Comment("是否必填[IS_REQUIRED] operate-LTEqual[<=]    是否必填:0=否，1=是")
    private Integer endIsRequired;

    @Comment("是否作为CI显示[IS_CI_DISP] operate-Equal[=]    是否作为CI显示:0=否，1=是")
    private Integer isCiDisp;

    @Comment("是否作为CI显示[IS_CI_DISP] operate-In[in]    是否作为CI显示:0=否，1=是")
    private Integer[] isCiDisps;

    @Comment("是否作为CI显示[IS_CI_DISP] operate-GTEqual[>=]    是否作为CI显示:0=否，1=是")
    private Integer startIsCiDisp;

    @Comment("是否作为CI显示[IS_CI_DISP] operate-LTEqual[<=]    是否作为CI显示:0=否，1=是")
    private Integer endIsCiDisp;

    @Comment("缺省值[DEF_VAL] operate-Like[like]")
    private String defVal;

    @Comment("枚举值[ENUM_VALUES] operate-Like[like]    枚举值:多个以逗号分隔")
    private String enumValues;

    @Comment("排列顺序[ORDER_NO] operate-Equal[=]")
    private Integer orderNo;

    @Comment("排列顺序[ORDER_NO] operate-In[in]")
    private Integer[] orderNos;

    @Comment("排列顺序[ORDER_NO] operate-GTEqual[>=]")
    private Integer startOrderNo;

    @Comment("排列顺序[ORDER_NO] operate-LTEqual[<=]")
    private Integer endOrderNo;

    @Comment("关系标签位置[LINE_LABEL_ALIGN] operate-Equal[=]    关系标签位置:1=偏向源 2=偏向目标 3=居中")
    private Integer lineLabelAlign;

    @Comment("关系标签位置[LINE_LABEL_ALIGN] operate-In[in]    关系标签位置:1=偏向源 2=偏向目标 3=居中")
    private Integer[] lineLabelAligns;

    @Comment("关系标签位置[LINE_LABEL_ALIGN] operate-GTEqual[>=]    关系标签位置:1=偏向源 2=偏向目标 3=居中")
    private Integer startLineLabelAlign;

    @Comment("关系标签位置[LINE_LABEL_ALIGN] operate-LTEqual[<=]    关系标签位置:1=偏向源 2=偏向目标 3=居中")
    private Integer endLineLabelAlign;

    @Comment("是否是代码[IS_CODE] operate-Equal[=]    是否是代码 1=是 0=否")
    private Integer isCode;

    @Comment("是否是代码[IS_CODE] operate-In[in]    是否是代码 1=是 0=否")
    private Integer[] isCodes;

    @Comment("是否是代码[IS_CODE] operate-GTEqual[>=]    是否是代码 1=是 0=否")
    private Integer startIsCode;

    @Comment("是否是代码[IS_CODE] operate-LTEqual[<=]    是否是代码 1=是 0=否")
    private Integer endIsCode;

    @Comment("重要等级[IMPORTANCE_LEVEL] operate-Equal[=]")
    private Integer importanceLevel;

    @Comment("重要等级[IMPORTANCE_LEVEL] operate-In[in]")
    private Integer[] importanceLevels;

    @Comment("重要等级[IMPORTANCE_LEVEL] operate-GTEqual[>=]")
    private Integer startImportanceLevel;

    @Comment("重要等级[IMPORTANCE_LEVEL] operate-LTEqual[<=]")
    private Integer endImportanceLevel;

    @Comment("是否审计[IS_AUDIT] operate-Equal[=]    是否审计:1=是，0=否")
    private Integer isAudit;

    @Comment("是否审计[IS_AUDIT] operate-In[in]    是否审计:1=是，0=否")
    private Integer[] isAudits;

    @Comment("是否审计[IS_AUDIT] operate-GTEqual[>=]    是否审计:1=是，0=否")
    private Integer startIsAudit;

    @Comment("是否审计[IS_AUDIT] operate-LTEqual[<=]    是否审计:1=是，0=否")
    private Integer endIsAudit;

    @Comment("约束规则[CONSTRAINT_RULE] operate-Like[like]")
    private String constraintRule;

    @Comment("源端或者目标端[SOURCE_OR_TARGET]")
    private String sourceOrTarget;

    @Comment("生成的关系id[CONSTRUCT_RLT_ID]")
    private String constructRltId;

    @Comment("所属域[DOMAIN_ID] operate-Equal[=]")
    private Long domainId;

    @Comment("所属域[DOMAIN_ID] operate-In[in]")
    private Long[] domainIds;

    @Comment("所属域[DOMAIN_ID] operate-GTEqual[>=]")
    private Long startDomainId;

    @Comment("所属域[DOMAIN_ID] operate-LTEqual[<=]")
    private Long endDomainId;

    @Comment("数据状态[DATA_STATUS] operate-Equal[=]    数据状态:0=删除，1=正常")
    private Integer dataStatus;

    @Comment("数据状态[DATA_STATUS] operate-In[in]    数据状态:0=删除，1=正常")
    private Integer[] dataStatuss;

    @Comment("数据状态[DATA_STATUS] operate-GTEqual[>=]    数据状态:0=删除，1=正常")
    private Integer startDataStatus;

    @Comment("数据状态[DATA_STATUS] operate-LTEqual[<=]    数据状态:0=删除，1=正常")
    private Integer endDataStatus;

    @Comment("创建人[CREATOR] operate-Like[like]")
    private String creator;

    @Comment("创建人[CREATOR] operate-Equal[=]")
    private String creatorEqual;

    @Comment("创建人[CREATOR] operate-In[in]")
    private String[] creators;

    @Comment("修改人[MODIFIER] operate-Like[like]")
    private String modifier;

    @Comment("修改人[MODIFIER] operate-Equal[=]")
    private String modifierEqual;

    @Comment("修改人[MODIFIER] operate-In[in]")
    private String[] modifiers;

    @Comment("创建时间[CREATE_TIME] operate-Equal[=]    创建时间:yyyyMMddHHmmss")
    private Long createTime;

    @Comment("创建时间[CREATE_TIME] operate-In[in]    创建时间:yyyyMMddHHmmss")
    private Long[] createTimes;

    @Comment("创建时间[CREATE_TIME] operate-GTEqual[>=]    创建时间:yyyyMMddHHmmss")
    private Long startCreateTime;

    @Comment("创建时间[CREATE_TIME] operate-LTEqual[<=]    创建时间:yyyyMMddHHmmss")
    private Long endCreateTime;

    @Comment("更新时间[MODIFY_TIME] operate-Equal[=]    更新时间:yyyyMMddHHmmss")
    private Long modifyTime;

    @Comment("更新时间[MODIFY_TIME] operate-In[in]    更新时间:yyyyMMddHHmmss")
    private Long[] modifyTimes;

    @Comment("更新时间[MODIFY_TIME] operate-GTEqual[>=]    更新时间:yyyyMMddHHmmss")
    private Long startModifyTime;

    @Comment("更新时间[MODIFY_TIME] operate-LTEqual[<=]    更新时间:yyyyMMddHHmmss")
    private Long endModifyTime;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long[] getIds() {
        return this.ids;
    }

    public void setIds(Long[] ids) {
        this.ids = ids;
    }

    public Long getStartId() {
        return this.startId;
    }

    public void setStartId(Long startId) {
        this.startId = startId;
    }

    public Long getEndId() {
        return this.endId;
    }

    public void setEndId(Long endId) {
        this.endId = endId;
    }

    public Long getClassId() {
        return this.classId;
    }

    public void setClassId(Long classId) {
        this.classId = classId;
    }

    public Long[] getClassIds() {
        return this.classIds;
    }

    public void setClassIds(Long[] classIds) {
        this.classIds = classIds;
    }

    public Long getStartClassId() {
        return this.startClassId;
    }

    public void setStartClassId(Long startClassId) {
        this.startClassId = startClassId;
    }

    public Long getEndClassId() {
        return this.endClassId;
    }

    public void setEndClassId(Long endClassId) {
        this.endClassId = endClassId;
    }

    public Integer getCiType() {
        return this.ciType;
    }

    public void setCiType(Integer ciType) {
        this.ciType = ciType;
    }

    public Integer[] getCiTypes() {
        return this.ciTypes;
    }

    public void setCiTypes(Integer[] ciTypes) {
        this.ciTypes = ciTypes;
    }

    public Integer getStartCiType() {
        return this.startCiType;
    }

    public void setStartCiType(Integer startCiType) {
        this.startCiType = startCiType;
    }

    public Integer getEndCiType() {
        return this.endCiType;
    }

    public void setEndCiType(Integer endCiType) {
        this.endCiType = endCiType;
    }

    public String getProName() {
        return this.proName;
    }

    public void setProName(String proName) {
        this.proName = proName;
    }

    public String getProNameEqual() {
        return this.proNameEqual;
    }

    public void setProNameEqual(String proNameEqual) {
        this.proNameEqual = proNameEqual;
    }

    public String[] getProNames() {
        return this.proNames;
    }

    public void setProNames(String[] proNames) {
        this.proNames = proNames;
    }

    public String getProStdName() {
        return this.proStdName;
    }

    public void setProStdName(String proStdName) {
        this.proStdName = proStdName;
    }

    public String getProStdNameEqual() {
        return this.proStdNameEqual;
    }

    public void setProStdNameEqual(String proStdNameEqual) {
        this.proStdNameEqual = proStdNameEqual;
    }

    public String[] getProStdNames() {
        return this.proStdNames;
    }

    public void setProStdNames(String[] proStdNames) {
        this.proStdNames = proStdNames;
    }

    public Integer getProType() {
        return this.proType;
    }

    public void setProType(Integer proType) {
        this.proType = proType;
    }

    public Integer[] getProTypes() {
        return this.proTypes;
    }

    public void setProTypes(Integer[] proTypes) {
        this.proTypes = proTypes;
    }

    public Integer getStartProType() {
        return this.startProType;
    }

    public void setStartProType(Integer startProType) {
        this.startProType = startProType;
    }

    public Integer getEndProType() {
        return this.endProType;
    }

    public void setEndProType(Integer endProType) {
        this.endProType = endProType;
    }

    public Integer getProDropSourceType() {
        return this.proDropSourceType;
    }

    public void setProDropSourceType(Integer proDropSourceType) {
        this.proDropSourceType = proDropSourceType;
    }

    public Integer[] getProDropSourceTypes() {
        return this.proDropSourceTypes;
    }

    public void setProDropSourceTypes(Integer[] proDropSourceTypes) {
        this.proDropSourceTypes = proDropSourceTypes;
    }

    public Integer getStartProDropSourceType() {
        return this.startProDropSourceType;
    }

    public void setStartProDropSourceType(Integer startProDropSourceType) {
        this.startProDropSourceType = startProDropSourceType;
    }

    public Integer getEndProDropSourceType() {
        return this.endProDropSourceType;
    }

    public void setEndProDropSourceType(Integer endProDropSourceType) {
        this.endProDropSourceType = endProDropSourceType;
    }

    public Long getProDropSourceId() {
        return this.proDropSourceId;
    }

    public void setProDropSourceId(Long proDropSourceId) {
        this.proDropSourceId = proDropSourceId;
    }

    public Long[] getProDropSourceIds() {
        return this.proDropSourceIds;
    }

    public void setProDropSourceIds(Long[] proDropSourceIds) {
        this.proDropSourceIds = proDropSourceIds;
    }

    public Long getStartProDropSourceId() {
        return this.startProDropSourceId;
    }

    public void setStartProDropSourceId(Long startProDropSourceId) {
        this.startProDropSourceId = startProDropSourceId;
    }

    public Long getEndProDropSourceId() {
        return this.endProDropSourceId;
    }

    public void setEndProDropSourceId(Long endProDropSourceId) {
        this.endProDropSourceId = endProDropSourceId;
    }

    public String getProDropSourceDef() {
        return proDropSourceDef;
    }

    public void setProDropSourceDef(String proDropSourceDef) {
        this.proDropSourceDef = proDropSourceDef;
    }

    public String getProDesc() {
        return this.proDesc;
    }

    public void setProDesc(String proDesc) {
        this.proDesc = proDesc;
    }

    public Integer getMpCiField() {
        return this.mpCiField;
    }

    public void setMpCiField(Integer mpCiField) {
        this.mpCiField = mpCiField;
    }

    public Integer[] getMpCiFields() {
        return this.mpCiFields;
    }

    public void setMpCiFields(Integer[] mpCiFields) {
        this.mpCiFields = mpCiFields;
    }

    public Integer getStartMpCiField() {
        return this.startMpCiField;
    }

    public void setStartMpCiField(Integer startMpCiField) {
        this.startMpCiField = startMpCiField;
    }

    public Integer getEndMpCiField() {
        return this.endMpCiField;
    }

    public void setEndMpCiField(Integer endMpCiField) {
        this.endMpCiField = endMpCiField;
    }

    public Integer getIsMajor() {
        return this.isMajor;
    }

    public void setIsMajor(Integer isMajor) {
        this.isMajor = isMajor;
    }

    public Integer[] getIsMajors() {
        return this.isMajors;
    }

    public void setIsMajors(Integer[] isMajors) {
        this.isMajors = isMajors;
    }

    public Integer getStartIsMajor() {
        return this.startIsMajor;
    }

    public void setStartIsMajor(Integer startIsMajor) {
        this.startIsMajor = startIsMajor;
    }

    public Integer getEndIsMajor() {
        return this.endIsMajor;
    }

    public void setEndIsMajor(Integer endIsMajor) {
        this.endIsMajor = endIsMajor;
    }

    public Integer getIsRequired() {
        return this.isRequired;
    }

    public void setIsRequired(Integer isRequired) {
        this.isRequired = isRequired;
    }

    public Integer[] getIsRequireds() {
        return this.isRequireds;
    }

    public void setIsRequireds(Integer[] isRequireds) {
        this.isRequireds = isRequireds;
    }

    public Integer getStartIsRequired() {
        return this.startIsRequired;
    }

    public void setStartIsRequired(Integer startIsRequired) {
        this.startIsRequired = startIsRequired;
    }

    public Integer getEndIsRequired() {
        return this.endIsRequired;
    }

    public void setEndIsRequired(Integer endIsRequired) {
        this.endIsRequired = endIsRequired;
    }

    public Integer getIsCiDisp() {
        return this.isCiDisp;
    }

    public void setIsCiDisp(Integer isCiDisp) {
        this.isCiDisp = isCiDisp;
    }

    public Integer[] getIsCiDisps() {
        return this.isCiDisps;
    }

    public void setIsCiDisps(Integer[] isCiDisps) {
        this.isCiDisps = isCiDisps;
    }

    public Integer getStartIsCiDisp() {
        return this.startIsCiDisp;
    }

    public void setStartIsCiDisp(Integer startIsCiDisp) {
        this.startIsCiDisp = startIsCiDisp;
    }

    public Integer getEndIsCiDisp() {
        return this.endIsCiDisp;
    }

    public void setEndIsCiDisp(Integer endIsCiDisp) {
        this.endIsCiDisp = endIsCiDisp;
    }

    public String getDefVal() {
        return this.defVal;
    }

    public void setDefVal(String defVal) {
        this.defVal = defVal;
    }

    public String getEnumValues() {
        return this.enumValues;
    }

    public void setEnumValues(String enumValues) {
        this.enumValues = enumValues;
    }

    public Integer getOrderNo() {
        return this.orderNo;
    }

    public void setOrderNo(Integer orderNo) {
        this.orderNo = orderNo;
    }

    public Integer[] getOrderNos() {
        return this.orderNos;
    }

    public void setOrderNos(Integer[] orderNos) {
        this.orderNos = orderNos;
    }

    public Integer getStartOrderNo() {
        return this.startOrderNo;
    }

    public void setStartOrderNo(Integer startOrderNo) {
        this.startOrderNo = startOrderNo;
    }

    public Integer getEndOrderNo() {
        return this.endOrderNo;
    }

    public void setEndOrderNo(Integer endOrderNo) {
        this.endOrderNo = endOrderNo;
    }

    public Integer getLineLabelAlign() {
        return this.lineLabelAlign;
    }

    public void setLineLabelAlign(Integer lineLabelAlign) {
        this.lineLabelAlign = lineLabelAlign;
    }

    public Integer[] getLineLabelAligns() {
        return this.lineLabelAligns;
    }

    public void setLineLabelAligns(Integer[] lineLabelAligns) {
        this.lineLabelAligns = lineLabelAligns;
    }

    public Integer getStartLineLabelAlign() {
        return this.startLineLabelAlign;
    }

    public void setStartLineLabelAlign(Integer startLineLabelAlign) {
        this.startLineLabelAlign = startLineLabelAlign;
    }

    public Integer getEndLineLabelAlign() {
        return this.endLineLabelAlign;
    }

    public void setEndLineLabelAlign(Integer endLineLabelAlign) {
        this.endLineLabelAlign = endLineLabelAlign;
    }

    public Integer getIsCode() {
        return this.isCode;
    }

    public void setIsCode(Integer isCode) {
        this.isCode = isCode;
    }

    public Integer[] getIsCodes() {
        return this.isCodes;
    }

    public void setIsCodes(Integer[] isCodes) {
        this.isCodes = isCodes;
    }

    public Integer getStartIsCode() {
        return this.startIsCode;
    }

    public void setStartIsCode(Integer startIsCode) {
        this.startIsCode = startIsCode;
    }

    public Integer getEndIsCode() {
        return this.endIsCode;
    }

    public void setEndIsCode(Integer endIsCode) {
        this.endIsCode = endIsCode;
    }

    public Integer getImportanceLevel() {
        return this.importanceLevel;
    }

    public void setImportanceLevel(Integer importanceLevel) {
        this.importanceLevel = importanceLevel;
    }

    public Integer[] getImportanceLevels() {
        return this.importanceLevels;
    }

    public void setImportanceLevels(Integer[] importanceLevels) {
        this.importanceLevels = importanceLevels;
    }

    public Integer getStartImportanceLevel() {
        return this.startImportanceLevel;
    }

    public void setStartImportanceLevel(Integer startImportanceLevel) {
        this.startImportanceLevel = startImportanceLevel;
    }

    public Integer getEndImportanceLevel() {
        return this.endImportanceLevel;
    }

    public void setEndImportanceLevel(Integer endImportanceLevel) {
        this.endImportanceLevel = endImportanceLevel;
    }

    public Integer getIsAudit() {
        return this.isAudit;
    }

    public void setIsAudit(Integer isAudit) {
        this.isAudit = isAudit;
    }

    public Integer[] getIsAudits() {
        return this.isAudits;
    }

    public void setIsAudits(Integer[] isAudits) {
        this.isAudits = isAudits;
    }

    public Integer getStartIsAudit() {
        return this.startIsAudit;
    }

    public void setStartIsAudit(Integer startIsAudit) {
        this.startIsAudit = startIsAudit;
    }

    public Integer getEndIsAudit() {
        return this.endIsAudit;
    }

    public void setEndIsAudit(Integer endIsAudit) {
        this.endIsAudit = endIsAudit;
    }

    public String getConstraintRule() {
        return this.constraintRule;
    }

    public void setConstraintRule(String constraintRule) {
        this.constraintRule = constraintRule;
    }

    public Long getDomainId() {
        return this.domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }

    public Long[] getDomainIds() {
        return this.domainIds;
    }

    public void setDomainIds(Long[] domainIds) {
        this.domainIds = domainIds;
    }

    public Long getStartDomainId() {
        return this.startDomainId;
    }

    public void setStartDomainId(Long startDomainId) {
        this.startDomainId = startDomainId;
    }

    public Long getEndDomainId() {
        return this.endDomainId;
    }

    public void setEndDomainId(Long endDomainId) {
        this.endDomainId = endDomainId;
    }

    public Integer getDataStatus() {
        return this.dataStatus;
    }

    public void setDataStatus(Integer dataStatus) {
        this.dataStatus = dataStatus;
    }

    public Integer[] getDataStatuss() {
        return this.dataStatuss;
    }

    public void setDataStatuss(Integer[] dataStatuss) {
        this.dataStatuss = dataStatuss;
    }

    public Integer getStartDataStatus() {
        return this.startDataStatus;
    }

    public void setStartDataStatus(Integer startDataStatus) {
        this.startDataStatus = startDataStatus;
    }

    public Integer getEndDataStatus() {
        return this.endDataStatus;
    }

    public void setEndDataStatus(Integer endDataStatus) {
        this.endDataStatus = endDataStatus;
    }

    public String getCreator() {
        return this.creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getCreatorEqual() {
        return this.creatorEqual;
    }

    public void setCreatorEqual(String creatorEqual) {
        this.creatorEqual = creatorEqual;
    }

    public String[] getCreators() {
        return this.creators;
    }

    public void setCreators(String[] creators) {
        this.creators = creators;
    }

    public String getModifier() {
        return this.modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public String getModifierEqual() {
        return this.modifierEqual;
    }

    public void setModifierEqual(String modifierEqual) {
        this.modifierEqual = modifierEqual;
    }

    public String[] getModifiers() {
        return this.modifiers;
    }

    public void setModifiers(String[] modifiers) {
        this.modifiers = modifiers;
    }

    public Long getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long[] getCreateTimes() {
        return this.createTimes;
    }

    public void setCreateTimes(Long[] createTimes) {
        this.createTimes = createTimes;
    }

    public Long getStartCreateTime() {
        return this.startCreateTime;
    }

    public void setStartCreateTime(Long startCreateTime) {
        this.startCreateTime = startCreateTime;
    }

    public Long getEndCreateTime() {
        return this.endCreateTime;
    }

    public void setEndCreateTime(Long endCreateTime) {
        this.endCreateTime = endCreateTime;
    }

    public Long getModifyTime() {
        return this.modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Long[] getModifyTimes() {
        return this.modifyTimes;
    }

    public void setModifyTimes(Long[] modifyTimes) {
        this.modifyTimes = modifyTimes;
    }

    public Long getStartModifyTime() {
        return this.startModifyTime;
    }

    public void setStartModifyTime(Long startModifyTime) {
        this.startModifyTime = startModifyTime;
    }

    public Long getEndModifyTime() {
        return this.endModifyTime;
    }

    public void setEndModifyTime(Long endModifyTime) {
        this.endModifyTime = endModifyTime;
    }

}
