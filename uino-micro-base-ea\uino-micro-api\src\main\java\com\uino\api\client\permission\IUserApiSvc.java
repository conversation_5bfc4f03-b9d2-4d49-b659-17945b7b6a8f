package com.uino.api.client.permission;

import com.binary.jdbc.Page;
import com.uino.bean.cmdb.business.ImportResultMessage;
import com.uino.bean.cmdb.business.ImportSheetMessage;
import com.uino.bean.permission.base.SysModuleOutSide;
import com.uino.bean.permission.base.SysRoleDataModuleRlt;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.permission.business.CurrentUserInfo;
import com.uino.bean.permission.business.ModuleNodeInfo;
import com.uino.bean.permission.business.SysUserPwdResetParam;
import com.uino.bean.permission.business.UserInfo;
import com.uino.bean.permission.business.request.SaveUserRoleRltRequestDto;
import com.uino.bean.permission.query.CAuthDataModuleBean;
import com.uino.bean.permission.query.CSysUser;
import com.uino.bean.permission.query.UserSearchBeanExtend;
import org.elasticsearch.index.query.QueryBuilder;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 用戶相关服务
 *
 * <AUTHOR>
 */
public interface IUserApiSvc {

    /**
     * 保存用户
     *
     * @param userInfo
     * @return 0表示失败，其它表示成功
     */
    Long saveOrUpdate(UserInfo userInfo);

    /**
     * 批量保存用户信息
     *
     * @param userInfos
     * @return
     */
    ImportSheetMessage saveOrUpdateUserInfoBatch(List<UserInfo> userInfos);

    /**
     *
     * @param domainId
     * @param isUpdatePassword  更新操作时是否更新密码
     * @param userInfos
     * @return
     */
    ImportSheetMessage saveOrUpdateUserInfoBatch(Long domainId, boolean isUpdatePassword, List<UserInfo> userInfos);

    /**
     * 批量保存用户-用于dix批量更新用户
     *
     * @param userInfos
     * @return
     */
    ImportSheetMessage syncUserBatch(List<UserInfo> userInfos);
    ImportSheetMessage syncUserBatch(Long domainId,List<UserInfo> userInfos);

    /**
     * 获取用户
     *
     * @param userId
     * @return
     */
    UserInfo getUserInfoById(Long userId);

    /**
     * 获取用户
     *
     * @param loginCode
     * @return
     */
    UserInfo getUserInfoByLoginCode(String loginCode);

    /**
     * 获取用户
     *
     * @param cdt
     * @return
     */
    List<SysUser> getSysUserByCdt(CSysUser cdt);

    /**
     * 条件查询用户信息-包含角色
     * 
     * @param cdt
     * @param rmSensitiveData
     *            是否清除用户敏感信息
     * @return
     */
    List<UserInfo> getUserInfoByCdt(CSysUser cdt, boolean rmSensitiveData);

    /**
     * 全文检索--分页
     *
     * @param bean
     * @return
     */
    Page<UserInfo> getListByQuery(UserSearchBeanExtend bean);

    /**
     * 获取角色下的用户
     *
     * @param roleId
     * @return
     */
    List<SysUser> getUserByRoleId(Long roleId);

    /**
     * 更新当前用户信息
     *
     * @param op
     * @param file
     * @return
     */
    Long updateCurUser(SysUser op, MultipartFile file);

    /**
     * 用户更新个人密码
     *
     * @param pwdParam
     * @return
     */
    Long resetPwd(SysUserPwdResetParam pwdParam);
    Long resetPwd(Long domainId,SysUserPwdResetParam pwdParam);

    /**
     * 管理员更新密码
     *
     * @param pwdParam
     * @return
     */
    Long resetPwdByAdmin(SysUserPwdResetParam pwdParam);

    /**
     * 导入用户
     * 
     * @param file
     * @return
     */
    ImportResultMessage importUserByExcel(MultipartFile file);
    ImportResultMessage importUserByExcel(Long domainId,MultipartFile file);
    /**
     * 导出用户到Excel文件
     * 
     * @param isTpl
     * @param cdt
     * @return
     */
    ResponseEntity<byte[]> exportUserToExcel(Boolean isTpl, CSysUser cdt);

    /**
     * 根据id删除用户及其关联数据
     *
     * @param userId
     * @return
     */
    Integer deleteById(Long userId);

    /**
     * 根据登录代码LoginCode批量删除用户
     *
     * @param loginCodes
     * @return
     */
    Integer deleteSysUserByLoginCodeBatch(List<String> loginCodes);
    Integer deleteSysUserByLoginCodeBatch(Long domainId,List<String> loginCodes);

    /**
     * 绑定用户角色关系
     * 
     * @param bean
     * @return
     */
    Integer saveUserRoleRlt(SaveUserRoleRltRequestDto bean);

    /**
     * 获取菜单tree结构
     *
     * @param userId
     *            为空则获取所有tree；不为空则获取该用户有权限的菜单tree
     * @return
     */
    ModuleNodeInfo getModuleTree(Long userId);

    ModuleNodeInfo getModuleTree(Long domainId,Long userId);

    /**
     * 获取指定用户拥有的数据权限字典
     *
     * @param userId
     * @param moduleCodes
     * @return
     */
    Map<String, List<SysRoleDataModuleRlt>> getDataModule(Long userId, List<String> moduleCodes);

    /**
     * 收藏菜单
     *
     * @param userId
     * @param moduleId
     * @return
     */
    Long enshrineModule(Long userId, Long moduleId);
    Long enshrineModule(Long domainId,Long userId, Long moduleId);

    /**
     * 取消菜单收藏
     *
     * @param userId
     * @param moduleId
     * @return
     */
    Integer unenshrineModule(Long userId, Long moduleId);

    Integer unenshrineModule(Long domainId,Long userId, Long moduleId);

    /**
     * 统计符合条件的用户
     * 
     * @param query
     * @return
     */
    long countByCondition(QueryBuilder query);

    long countByCondition(Long domainId,QueryBuilder query);
    /**
     * 记录用户登陆失败次数
     * 
     * @param loginName
     * @param success
     * @return
     */
    int recordLoginFailNum(String loginName, boolean success);
    int recordLoginFailNum(Long domainId,String loginName, boolean success);
    /**
     * 获取登陆失败次数锁定
     * 
     * @return
     */
    public int getLoginFailLockNum();

    /**
     * 获取用户锁定时长
     * 
     * @return
     */
    public long getUserLockDuration();

    /**
     * 获取当前登陆用户
     * 
     * @return
     */
    CurrentUserInfo getCurrentUser();

    /**
     * 根据锁定时长解锁用户
     * 
     * @param durationSecond
     * @return 解锁的用户数量
     */
    int unLockUsersByDuration(Long durationSecond);


    /**
	 * 校验用户权限
	 */
	public void verifyAuth();


	/**
     * 根据登录名查询用户权限
     *
     * @param loginCode login code
     * @return {@link List<SysModuleOutSide>}
     * */
	List<SysModuleOutSide> getModulesByLoginCode(String loginCode);

    List<SysModuleOutSide> getModulesByLoginCode(Long domainId,String loginCode);


    /**
     * Query the data module permissions corresponding to the user
     *
     * @param cAuthDataModuleBean cAuthDataModuleBean
     * @return {@link  Map<String, Integer>}
     * */
    Map<Long, Map<String, Integer>> getDataPermissionByUser(CAuthDataModuleBean cAuthDataModuleBean);

    /**
     * 根据角色名称获取人员信息
     * @param roleName
     * @return
     */
    List<SysUser> getUserByRoleName(String roleName);

    /**
     * 根据用户名或登录名模糊查询用户数据
     * @param userName
     * @return
     */
    List<SysUser> getUserInfoByName(String userName);

    /**
     * 模糊搜索用户分页显示
     * @param userName
     * @return
     */
    Page<SysUser> findUserInfoByNameForPage(Integer pageNum, Integer pageSize, String userName);

    /**
     * 根据用户code集合获取用户名称集合
     * @param loginCodes 登录名
     * @return 登录名-用户名
     */
    Map<String, String> getNameByCodes(Collection<String> loginCodes);

    /**
     *  根据角色名称获取人员信息分页查 默认每页100条
     * @param roleName
     * @param pageNum 1
     * @param pageSize 100
     * @return
     */
    Page<SysUser> queryPageByRoleName(String roleName, String loginName, int pageNum, int pageSize);
}
