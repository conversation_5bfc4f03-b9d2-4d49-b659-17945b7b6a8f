package com.uino.util.sys;

import com.binary.core.exception.BinaryException;
import com.uino.bean.cmdb.base.LibType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 私有库/运行库/设计库工具类
 */
public class LibTypeUtil {

    private static Logger logger =  LoggerFactory.getLogger(LibTypeUtil.class);

    private static ThreadLocal<String> libTypeStorage = new ThreadLocal<>();

    /**
     * 设置本地线程数据，使用完及时调用removeLibType() 删除数据
     * @param libType
     */
    public static void setLibType(String libType){
        LibTypeUtil.libTypeStorage.set(libType.toUpperCase());
    }

    public static String getLibType(){
        return LibTypeUtil.libTypeStorage.get();
    }

    public static boolean isPrivate(){
        return LibTypeUtil.libTypeStorage.get().equals(LibType.PRIVATE.toString());
    }

    public static void removeLibType(){
        LibTypeUtil.libTypeStorage.remove();
    }

    public static <R> R execute(Execute<R> execute, LibType env){
        try {
            setLibType(env.toString());
            return execute.apply();
        }catch (Exception e){
            logger.error(e.getMessage(), e);
            throw new BinaryException(e);
        }finally {
            removeLibType();
        }
    }

    public static void execute(Executor execute, LibType env){
        try {
            setLibType(env.toString());
            execute.apply();
        }catch (Exception e){
            logger.error(e.getMessage(), e);
            throw new BinaryException(e);
        }finally {
            removeLibType();
        }
    }

    @FunctionalInterface
    public interface Executor{
         void apply();
    }

    @FunctionalInterface
    public interface Execute<R>{
        R apply();
    }
}
