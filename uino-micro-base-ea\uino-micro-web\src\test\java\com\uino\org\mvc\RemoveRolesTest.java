package com.uino.org.mvc;

import java.util.Arrays;
import java.util.HashSet;

import org.elasticsearch.index.query.QueryBuilder;
import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import com.alibaba.fastjson.JSON;
import com.binary.framework.web.RemoteResult;
import com.uino.StartBaseWebAppliaction;
import com.uino.dao.permission.ESOrgSvc;
import com.uino.dao.permission.rlt.ESOrgRoleRltSvc;
import com.uino.bean.permission.business.request.AddOrRemoveRoleToOrgRequestDto;

import lombok.extern.slf4j.Slf4j;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = { StartBaseWebAppliaction.class }, webEnvironment = WebEnvironment.RANDOM_PORT)
@Slf4j
@ActiveProfiles("provider-local")
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class RemoveRolesTest {
	@Autowired
	private TestRestTemplate restTemplate;
	@MockBean
	private ESOrgSvc esOrgSvc;
	@MockBean
	private ESOrgRoleRltSvc esOrgRoleRltSvc;
	private static String testUrl;

	@BeforeClass
	public static void initCls() {
		RemoveRolesTest.testUrl = "/permission/org/removeRoles";
	}

	@Before
	public void start() {
		Mockito.when(esOrgRoleRltSvc.deleteByQuery(Mockito.any(QueryBuilder.class), Mockito.anyBoolean()))
				.thenReturn(0);
	}

	@Test
	public void test01() {
		AddOrRemoveRoleToOrgRequestDto requestBody = AddOrRemoveRoleToOrgRequestDto.builder().orgId(1L).build();
		String responseBodyStr = restTemplate.postForObject(testUrl, requestBody, String.class);
		RemoteResult responseBody = JSON.parseObject(responseBodyStr, RemoteResult.class);
		Assert.assertFalse(responseBody.isSuccess());
	}

	@Test
	public void test02() {
		AddOrRemoveRoleToOrgRequestDto requestBody = AddOrRemoveRoleToOrgRequestDto.builder()
				.roleIds(new HashSet<>(Arrays.asList(1L))).build();
		String responseBodyStr = restTemplate.postForObject(testUrl, requestBody, String.class);
		RemoteResult responseBody = JSON.parseObject(responseBodyStr, RemoteResult.class);
		Assert.assertFalse(responseBody.isSuccess());
	}

	@Test
	public void test03() {
		AddOrRemoveRoleToOrgRequestDto requestBody = AddOrRemoveRoleToOrgRequestDto.builder()
				.roleIds(new HashSet<>(Arrays.asList(1L))).orgId(1L).build();
		String responseBodyStr = restTemplate.postForObject(testUrl, requestBody, String.class);
		RemoteResult responseBody = JSON.parseObject(responseBodyStr, RemoteResult.class);
		Assert.assertTrue(responseBody.isSuccess());
	}
}
