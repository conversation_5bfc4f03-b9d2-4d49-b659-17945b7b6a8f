package com.uinnova.product.eam.service.dm;

import com.uinnova.product.eam.base.diagram.model.ESDiagramModel;
import com.uinnova.product.eam.model.dm.*;
import com.uinnova.product.eam.model.dm.bean.AttrAndCiDto;
import com.uinnova.product.eam.model.dto.DealAttributeDto;
import com.uinnova.product.eam.model.dto.MoveAttributeDto;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uinnova.product.vmdb.provider.rlt.bean.CcCiRltInfo;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.base.LibType;
import com.uino.bean.cmdb.business.BindCiRltRequestDto;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface DataModelSvc {

    /**
     * 保存、更新实体或实体属性
     * @param dto 参数
     * @return ci全量信息
     */
    DataModelCiInfo saveOrUpdate(DealAttributeDto dto);

    /**
     * 批量保存或更新实体属性信息
     * @param ciList 实体属性对象信息
     * @return 1成功0失败
     */
    Integer saveOrUpdateAttrBatch(List<ESCIInfo> ciList);

    /**
     * 数据建模实体批量保存、删除属性
     * @param modelData 实体及属性
     * @return
     */
    DataModelBatchResp dealAttributeBatch(DealAttributeDto modelData);




    /**
     * 绑定实体间关系
     * @param reqBean 请求参数
     * @param fastShape 是否通过快捷形状创建
     * @param libType 库类型
     * @return 关系信息及继承节点集合
     */
    DataModelRltInfo bindCiRlt(BindCiRltRequestDto reqBean, Boolean fastShape, LibType libType);

    /**
     * 删除单条关系线
     * @param dto 请求参数
     * @return 画布更新信息
     */
    DataModelRltInfo delRltByCode(DealAttributeDto dto);

    /**
     * 单条删除实体或实体属性
     * @param dto 请求参数
     * @return 画布更新信息
     */
    DataModelRltInfo delEntityOrAttribute(DealAttributeDto dto);

    /**
     * 移动实体属性到另一个实体中
     * @param dto 参数
     * @return 画布更新信息
     */
    DataModelRltInfo moveAttribute(MoveAttributeDto dto);

    /**
     * 复制实体
     * @param dto 参数
     * @return 实体及实体属性全量信息
     */
    List<DataModelCopyInfo> copyEntity(DealAttributeDto dto);

    /**
     * 复制实体属性
     * @param dto 属性
     * @return 实体属性全量信息
     */
    DataModelCiInfo copyAttribute(DealAttributeDto dto);

    /**
     * 根据关系及实体code获取实体属性
     * @param rltClassId 关系分类id
     * @param attrClassId 实体属性分类id
     * @param entityCiCode 实体ciCode
     * @param ownerCode 用户code
     * @param libType 库
     * @return 关系
     */
    List<CcCiRltInfo> getAttrByEntity(Long rltClassId, Long attrClassId, Set<String> entityCiCode, String ownerCode, LibType libType);

    /**
     * 实体属性继承处理
     * @param entityCiCode 实体ciCode
     * @param modelParam 参数
     * @param ciList 做继承的属性集合
     * @return <实体code, 继承的属性集合>
     */
    Map<String, List<CcCiInfo>> inheritAttribute(String entityCiCode, DataModelDiagramParam modelParam, List<DataModelCiInfo> ciList);

    /**
     * 获取视图node、link参数
     * @param energyId 视图加密id
     * @param sheetId 分页id
     * @return 参数实体
     */
    DataModelDiagramParam getDiagramParam(String energyId, String sheetId);

    /**
     * 由于前端接收到继承节点后，无法及时更新视图继承节点数据库信息，刷新页面会出现继承节点数据未保存问题
     * 此处直接更新视图节点实体继承到的实体属性信息
     * @param model 视图model信息
     * @param entityAttrMap 视图更新节点实体属性map
     */
    void updateInheritDiagramNode(ESDiagramModel model, Map<String, List<CcCiInfo>> entityAttrMap);

    /**
     * 批量保存、更新实体
     * @param dto 实体ci集合
     * @return 实体
     */
    DataModelCiInfo saveOrUpdateEntityBatch(DealAttributeDto dto);

    /**
     * ER图自动成图
     * @param diagramId 视图id
     * @param libType 数据来源
     * @return 成图数据
     */
    AttrAndCiDto quickDrawingEr(String diagramId, LibType libType);

}
