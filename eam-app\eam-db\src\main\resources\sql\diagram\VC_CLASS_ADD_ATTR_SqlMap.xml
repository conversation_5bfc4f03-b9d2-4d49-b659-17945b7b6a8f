<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="VC_CLASS_ADD_ATTR">


	<resultMap type="com.uinnova.product.eam.comm.model.VcClassAddAttr" id="queryResult">
		<result column="ID" jdbcType="BIGINT" property="id"/>	<!-- ID -->
		<result column="CLASS_ID" jdbcType="BIGINT" property="classId"/>	<!-- 分类ID -->
		<result column="CLASS_TYPE" jdbcType="INTEGER" property="classType"/>	<!-- 分类类型 -->
		<result column="ADD_ATTR_1" jdbcType="VARCHAR" property="addAttr1"/>	<!-- 附加属性1 -->
		<result column="ADD_ATTR_2" jdbcType="VARCHAR" property="addAttr2"/>	<!-- 附加属性2 -->
		<result column="ADD_ATTR_3" jdbcType="VARCHAR" property="addAttr3"/>	<!-- 附加属性3 -->
		<result column="ADD_ATTR_4" jdbcType="VARCHAR" property="addAttr4"/>	<!-- 附加属性4 -->
		<result column="ADD_ATTR_5" jdbcType="VARCHAR" property="addAttr5"/>	<!-- 附加属性5 -->
		<result column="ADD_ATTR_6" jdbcType="VARCHAR" property="addAttr6"/>	<!-- 附加属性6 -->
		<result column="DOMAIN_ID" jdbcType="BIGINT" property="domainId"/>	<!-- 所属域 -->
		<result column="DATA_STATUS" jdbcType="INTEGER" property="dataStatus"/>	<!-- 数据状态 -->
		<result column="CREATOR" jdbcType="VARCHAR" property="creator"/>	<!-- 创建人 -->
		<result column="MODIFIER" jdbcType="VARCHAR" property="modifier"/>	<!-- 修改人 -->
		<result column="CREATE_TIME" jdbcType="BIGINT" property="createTime"/>	<!-- 创建时间 -->
		<result column="MODIFY_TIME" jdbcType="BIGINT" property="modifyTime"/>	<!-- 更新时间 -->
	</resultMap>
	

	<sql id="sql_query_where">
		<if test="cdt != null and cdt.id != null " >and
			ID = #{cdt.id:BIGINT}
		</if>
		<if test="ids != null and ids != '' " >and
			ID in (${ids})
		</if>
		<if test="cdt != null and cdt.startId != null and cdt.startId != '' " >and
			ID &gt; #{cdt.startId:BIGINT}
		</if>
		<if test="cdt != null and cdt.endId != null and cdt.endId != '' " >and
			ID &lt; #{cdt.endId:BIGINT}
		</if>
		<if test="cdt != null and cdt.classId != null " >and
			CLASS_ID = #{cdt.classId:BIGINT}
		</if>
		<if test="classIds != null and classIds != '' " >and
			CLASS_ID in (${classIds})
		</if>
		<if test="cdt != null and cdt.startClassId != null and cdt.startClassId != '' " >and
			CLASS_ID &gt; #{cdt.startClassId:BIGINT}
		</if>
		<if test="cdt != null and cdt.endClassId != null and cdt.endClassId != '' " >and
			CLASS_ID &lt; #{cdt.endClassId:BIGINT}
		</if>
		<if test="cdt != null and cdt.classType != null " >and
			CLASS_TYPE = #{cdt.classType:INTEGER}
		</if>
		<if test="classTypes != null and classTypes != '' " >and
			CLASS_TYPE in (${classTypes})
		</if>
		<if test="cdt != null and cdt.startClassType != null and cdt.startClassType != '' " >and
			CLASS_TYPE &gt; #{cdt.startClassType:INTEGER}
		</if>
		<if test="cdt != null and cdt.endClassType != null and cdt.endClassType != '' " >and
			CLASS_TYPE &lt; #{cdt.endClassType:INTEGER}
		</if>
		<if test="cdt != null and cdt.addAttr1 != null " >and
			ADD_ATTR_1 like #{cdt.addAttr1,jdbcType=VARCHAR} 
		</if>
		<if test="cdt != null and cdt.addAttr2 != null " >and
			ADD_ATTR_2 like #{cdt.addAttr2,jdbcType=VARCHAR} 
		</if>
		<if test="cdt != null and cdt.addAttr3 != null " >and
			ADD_ATTR_3 like #{cdt.addAttr3,jdbcType=VARCHAR} 
		</if>
		<if test="cdt != null and cdt.addAttr4 != null " >and
			ADD_ATTR_4 like #{cdt.addAttr4,jdbcType=VARCHAR} 
		</if>
		<if test="cdt != null and cdt.addAttr5 != null " >and
			ADD_ATTR_5 like #{cdt.addAttr5,jdbcType=VARCHAR} 
		</if>
		<if test="cdt != null and cdt.addAttr6 != null " >and
			ADD_ATTR_6 like #{cdt.addAttr6,jdbcType=VARCHAR} 
		</if>
		<if test="cdt != null and cdt.domainId != null " >and
			DOMAIN_ID = #{cdt.domainId:BIGINT}
		</if>
		<if test="domainIds != null and domainIds != '' " >and
			DOMAIN_ID in (${domainIds})
		</if>
		<if test="cdt != null and cdt.startDomainId != null and cdt.startDomainId != '' " >and
			DOMAIN_ID &gt; #{cdt.startDomainId:BIGINT}
		</if>
		<if test="cdt != null and cdt.endDomainId != null and cdt.endDomainId != '' " >and
			DOMAIN_ID &lt; #{cdt.endDomainId:BIGINT}
		</if>
		<if test="cdt != null and cdt.dataStatus != null " >and
			DATA_STATUS = #{cdt.dataStatus:INTEGER}
		</if>
		<if test="dataStatuss != null and dataStatuss != '' " >and
			DATA_STATUS in (${dataStatuss})
		</if>
		<if test="cdt != null and cdt.startDataStatus != null and cdt.startDataStatus != '' " >and
			DATA_STATUS &gt; #{cdt.startDataStatus:INTEGER}
		</if>
		<if test="cdt != null and cdt.endDataStatus != null and cdt.endDataStatus != '' " >and
			DATA_STATUS &lt; #{cdt.endDataStatus:INTEGER}
		</if>
		<if test="cdt != null and cdt.creator != null " >and
			CREATOR like #{cdt.creator,jdbcType=VARCHAR} 
		</if>
		<if test="cdt != null and cdt.creatorEqual != null " >and
			CREATOR like #{cdt.creatorEqual,jdbcType=VARCHAR} 
		</if>
		<if test="creators != null and creators != '' " >and
			CREATOR in ${creators} 
		</if>
		<if test="cdt != null and cdt.modifier != null " >and
			MODIFIER like #{cdt.modifier,jdbcType=VARCHAR} 
		</if>
		<if test="cdt != null and cdt.modifierEqual != null " >and
			MODIFIER like #{cdt.modifierEqual,jdbcType=VARCHAR} 
		</if>
		<if test="modifiers != null and modifiers != '' " >and
			MODIFIER in ${modifiers} 
		</if>
		<if test="cdt != null and cdt.createTime != null " >and
			CREATE_TIME = #{cdt.createTime:BIGINT}
		</if>
		<if test="createTimes != null and createTimes != '' " >and
			CREATE_TIME in (${createTimes})
		</if>
		<if test="cdt != null and cdt.startCreateTime != null and cdt.startCreateTime != '' " >and
			CREATE_TIME &gt; #{cdt.startCreateTime:BIGINT}
		</if>
		<if test="cdt != null and cdt.endCreateTime != null and cdt.endCreateTime != '' " >and
			CREATE_TIME &lt; #{cdt.endCreateTime:BIGINT}
		</if>
		<if test="cdt != null and cdt.modifyTime != null " >and
			MODIFY_TIME = #{cdt.modifyTime:BIGINT}
		</if>
		<if test="modifyTimes != null and modifyTimes != '' " >and
			MODIFY_TIME in (${modifyTimes})
		</if>
		<if test="cdt != null and cdt.startModifyTime != null and cdt.startModifyTime != '' " >and
			MODIFY_TIME &gt; #{cdt.startModifyTime:BIGINT}
		</if>
		<if test="cdt != null and cdt.endModifyTime != null and cdt.endModifyTime != '' " >and
			MODIFY_TIME &lt; #{cdt.endModifyTime:BIGINT}
		</if>
	</sql>
	

	<sql id="sql_update_columns">
		<if test="record != null and record.id != null"> 
			ID = #{record.id:BIGINT},
		</if>
		<if test="record != null and record.classId != null"> 
			CLASS_ID = #{record.classId:BIGINT},
		</if>
		<if test="record != null and record.classType != null"> 
			CLASS_TYPE = #{record.classType:INTEGER},
		</if>
		<if test="record != null and record.addAttr1 != null"> 
			ADD_ATTR_1 = #{record.addAttr1:VARCHAR},
		</if>
		<if test="record != null and record.addAttr2 != null"> 
			ADD_ATTR_2 = #{record.addAttr2:VARCHAR},
		</if>
		<if test="record != null and record.addAttr3 != null"> 
			ADD_ATTR_3 = #{record.addAttr3:VARCHAR},
		</if>
		<if test="record != null and record.addAttr4 != null"> 
			ADD_ATTR_4 = #{record.addAttr4:VARCHAR},
		</if>
		<if test="record != null and record.addAttr5 != null"> 
			ADD_ATTR_5 = #{record.addAttr5:VARCHAR},
		</if>
		<if test="record != null and record.addAttr6 != null"> 
			ADD_ATTR_6 = #{record.addAttr6:VARCHAR},
		</if>
		<if test="record != null and record.domainId != null"> 
			DOMAIN_ID = #{record.domainId:BIGINT},
		</if>
		<if test="record != null and record.dataStatus != null"> 
			DATA_STATUS = #{record.dataStatus:INTEGER},
		</if>
		<if test="record != null and record.creator != null"> 
			CREATOR = #{record.creator:VARCHAR},
		</if>
		<if test="record != null and record.modifier != null"> 
			MODIFIER = #{record.modifier:VARCHAR},
		</if>
		<if test="record != null and record.createTime != null"> 
			CREATE_TIME = #{record.createTime:BIGINT},
		</if>
		<if test="record != null and record.modifyTime != null"> 
			MODIFY_TIME = #{record.modifyTime:BIGINT},
		</if>
	</sql>
	

	<sql id="sql_query_columns">
		ID, CLASS_ID, CLASS_TYPE, ADD_ATTR_1, ADD_ATTR_2, ADD_ATTR_3, 
		ADD_ATTR_4, ADD_ATTR_5, ADD_ATTR_6, DOMAIN_ID, DATA_STATUS, CREATOR, 
		MODIFIER, CREATE_TIME, MODIFY_TIME
	</sql>
	

	

	<select id="selectList" parameterType="java.util.Map" resultMap="queryResult">
		select 
			<include refid="VC_CLASS_ADD_ATTR.sql_query_columns" />
		from VC_CLASS_ADD_ATTR 
			<where>
				<include refid="VC_CLASS_ADD_ATTR.sql_query_where"/>
			</where>
		order by 
			<if  test="orders != null and orders != ''">
				${orders}
			</if>
			<if  test="orders == null or orders == ''">
				ID
			</if>
	</select>
	

	

	<select id="selectCount" parameterType="java.util.Map" resultType="java.lang.Long">
		select count(1) from VC_CLASS_ADD_ATTR 
			<where>
				<include refid="VC_CLASS_ADD_ATTR.sql_query_where"></include>
			</where>
	</select>
	

	

	<select id="selectById" parameterType="java.util.Map" resultMap="queryResult">
		select 
			<include refid="VC_CLASS_ADD_ATTR.sql_query_columns" />
		from VC_CLASS_ADD_ATTR where ID=#{id:BIGINT} and DATA_STATUS=1  
	</select>
	

	

	<insert id="insert" parameterType="java.util.Map">
		insert into VC_CLASS_ADD_ATTR(
			ID, CLASS_ID, CLASS_TYPE, ADD_ATTR_1, ADD_ATTR_2, 
			ADD_ATTR_3, ADD_ATTR_4, ADD_ATTR_5, ADD_ATTR_6, DOMAIN_ID, 
			DATA_STATUS, CREATOR, MODIFIER, CREATE_TIME, MODIFY_TIME)
		values (
			#{record.id:BIGINT}, #{record.classId:BIGINT}, #{record.classType:INTEGER}, #{record.addAttr1:VARCHAR}, #{record.addAttr2:VARCHAR}, 
			#{record.addAttr3:VARCHAR}, #{record.addAttr4:VARCHAR}, #{record.addAttr5:VARCHAR}, #{record.addAttr6:VARCHAR}, #{record.domainId:BIGINT}, 
			#{record.dataStatus:INTEGER}, #{record.creator:VARCHAR}, #{record.modifier:VARCHAR}, #{record.createTime:BIGINT}, #{record.modifyTime:BIGINT})
	</insert>
	

	

	<update id="updateById" parameterType="java.util.Map">
		update VC_CLASS_ADD_ATTR
			<set> 
				<include refid="VC_CLASS_ADD_ATTR.sql_update_columns"/> 
			</set>
		where ID = #{id:BIGINT}
	</update>
	

	<update id="updateByCdt" parameterType="java.util.Map">
		update VC_CLASS_ADD_ATTR
			<set> 
				<include refid="VC_CLASS_ADD_ATTR.sql_update_columns"/> 
			</set>
			<where> 
				<include refid="VC_CLASS_ADD_ATTR.sql_query_where"/> 
			</where>
	</update>
	
	

	

	<delete id="deleteById" parameterType="java.util.Map">
		delete from VC_CLASS_ADD_ATTR where ID = #{id:BIGINT}
	</delete>
	

	

	<delete id="deleteByCdt" parameterType="java.util.Map">
		delete from VC_CLASS_ADD_ATTR
			<where> 
				<include refid="VC_CLASS_ADD_ATTR.sql_query_where"/> 
			</where>
	</delete>
	



</mapper>
