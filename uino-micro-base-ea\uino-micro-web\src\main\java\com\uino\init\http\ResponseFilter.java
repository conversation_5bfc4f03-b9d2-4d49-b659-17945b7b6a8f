package com.uino.init.http;

import com.binary.core.util.WildcardPatternBuilder;
import com.uino.init.http.response.CustomHttpServletResponseWrapper;
import com.uino.util.sys.SysUtil.StringUtil;
import lombok.extern.slf4j.Slf4j;

import jakarta.servlet.Filter;
import jakarta.servlet.FilterChain;
import jakarta.servlet.FilterConfig;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

/**
 * 该拦截器为了复写response，为了切面日志读取结果 不在这直接做日志是为了可以通过切面看到请求路由到的方法
 * 
 * <AUTHOR>
 *
 */
//@WebFilter(urlPatterns = "/*", filterName = "AResponseFilter")
@Slf4j
//@Order(1)
public class ResponseFilter implements Filter {

	// private Set<String> noCoverCustomResponseUrls;

	private Pattern noCoverCustomResponseUrlPattern;

	{
		log.info("response请求拦截器注册成功");
	}

	@Override
	public void init(FilterConfig filterConfig) throws ServletException {
		String noCoverCustomResponse = filterConfig.getInitParameter("noCoverCustomResponse");
		if (StringUtil.isNotBack(noCoverCustomResponse)) {
			noCoverCustomResponseUrlPattern = WildcardPatternBuilder
					.build(splitStringPattern(noCoverCustomResponse.toUpperCase()));
		}
	}


    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        if (response instanceof HttpServletResponse && support(request)) {
            chain.doFilter(request, new CustomHttpServletResponseWrapper((HttpServletResponse) response));
        } else {
            chain.doFilter(request, response);
        }
    }

    private boolean support(ServletRequest request) {
		if (request instanceof HttpServletRequest && noCoverCustomResponseUrlPattern != null) {
            HttpServletRequest httpReq = (HttpServletRequest) request;
            String uri = httpReq.getRequestURI();
            String contextpath = httpReq.getContextPath();
			uri = uri.substring(contextpath.length()).toUpperCase();
			return !noCoverCustomResponseUrlPattern.matcher(uri).matches();
//            return !noCoverCustomResponseUrls.contains(uri);
        }
        return true;
    }

	private String[] splitStringPattern(String strFilterPattern) {
		char m = ';';
		if (strFilterPattern.indexOf(m) > 0) {
			String[] arr = strFilterPattern.split("[;]");
			List<String> ls = new ArrayList<String>();
			for (int i = 0; i < arr.length; i++) {
				String s = arr[i].trim();
				if (s.length() > 0) {
					ls.add(s);
				}
			}
			return ls.toArray(new String[0]);
		} else {
			return new String[] { strFilterPattern };
		}
	}
}
