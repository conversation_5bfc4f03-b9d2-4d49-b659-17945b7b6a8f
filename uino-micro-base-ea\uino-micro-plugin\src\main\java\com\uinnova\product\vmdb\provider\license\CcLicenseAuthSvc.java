package com.uinnova.product.vmdb.provider.license;

import com.uinnova.product.vmdb.comm.license.License;
import com.uinnova.product.vmdb.comm.model.license.CcLicenseAuth;
import com.uinnova.product.vmdb.comm.model.license.CcLicenseAuthServer;
import com.uinnova.product.vmdb.provider.license.bean.CcLicenseAuthInfo;

import java.util.List;

public interface CcLicenseAuthSvc {
	
	
	
	/**
	 * 初始化
	 */
	public void initialization();
	
	
	public void initSelfClient();

	/**
	 * 查询授权许可信息
	 * @return
	 */
	public CcLicenseAuth queryLicenseAuth();
	
	
	
	
	/**
	 * 查询授权许可信息
	 * @return
	 */
	public CcLicenseAuthInfo queryLicenseAuthInfo();
	
	
	
	
	/**
	 * 查询服务器列表
	 * @return
	 */
	public List<CcLicenseAuthServer> queryServerList();
	
	
	
	/**
	 * 删除服务器
	 * @param id
	 */
	public Integer removeServer(Long serverId);
	
	
	
	
	/**
	 * 创建客户识别码
	 * @return
	 */
	public String createClientCode();
	
	
	
	
	/**
	 * 更新累计时间
	 * @param time
	 */
	public void updateUtTime(Long utTime);
	
	
	
	
	/**
	 * 根据授权码更新系统运行时间
	 * @param authCode
	 * @param utTime
	 * @return
	 */
	public Integer updateUtTimeByAuthCode(String authCode, Long utTime);
	
	
	
	
	
	/**
	 * 注册授权
	 * @param authCode
	 */
	public void registerLicense(String authCode, String authUser);
	
	
	
	
	/**
	 * 跟据数据库中登记的授权许可证注册
	 */
	public void registerLicenseByDb();
	
	
	
	
	/**
	 * 获取实际授权信息
	 * @return
	 */
	public License getRealLicense();
	
	
	
	
	/**
	 * 设置license是否更新运行时间
	 * @param updated
	 */
	public void setLicenseCumulativeTimerUpdated(Boolean updated);
	
	
	

}




