package com.uinnova.product.eam.rpc.diagram;

import com.uinnova.product.eam.api.diagram.ESDiagramVersionApiClient;
import com.uinnova.product.eam.service.diagram.ESDiagramVersionSvc;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Service
public class ESDiagramVersionSvcRpc implements ESDiagramVersionApiClient {

    @Autowired
    private ESDiagramVersionSvc esDiagramVersionSvc;


    @Override
    public Long restoreDiagramByVersionId(Long currDiagramId, Long historyVersionId) {
        return esDiagramVersionSvc.restoreDiagramByVersionId(currDiagramId, historyVersionId);
    }

    @Override
    public Long createDiagramByCurrVersion(Long diagramId, boolean versionFlag) {
        return esDiagramVersionSvc.createDiagramByCurrVersion(diagramId, versionFlag);
    }
}
