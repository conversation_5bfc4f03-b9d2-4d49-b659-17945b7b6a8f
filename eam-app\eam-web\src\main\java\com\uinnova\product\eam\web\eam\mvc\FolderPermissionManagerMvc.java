package com.uinnova.product.eam.web.eam.mvc;

import com.binary.framework.web.RemoteResult;
import com.google.common.collect.Lists;
import com.uinnova.product.eam.comm.model.es.FolderPermissionManager;
import com.uinnova.product.eam.model.dto.FolderPermissionManagerVo;
import com.uinnova.product.eam.service.IFolderPermissionManagerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 目录权限功能相关接口
 *
 * <AUTHOR>
 * @since 2022/6/30 11:28
 */
@RestController
@RequestMapping("/folderPermissionManager")
public class FolderPermissionManagerMvc {

    @Autowired
    private IFolderPermissionManagerService folderPermissionManagerService;

    /**
     * 批量新增权限
     * @param folderPermissionManagerVo
     * @return
     */
    @PostMapping("/addFolderPermissionManagers")
    public RemoteResult addFolderPermissionManagers(@RequestBody FolderPermissionManagerVo folderPermissionManagerVo){
        folderPermissionManagerService.saveFolderPermissions(folderPermissionManagerVo);
        return new RemoteResult("success");
    }

    /**
     * 修改权限
     * @param folderPermissionManager
     * @return
     */
    @PostMapping("/updateFolderPermissionManager")
    public RemoteResult updateFolderPermissionManager(@RequestBody FolderPermissionManager folderPermissionManager){
        Assert.notNull(folderPermissionManager.getId(),"修改需要指定id");
        folderPermissionManagerService.updateFolderPermissionManager(folderPermissionManager);
        return new RemoteResult("success");
    }

    /**
     *  删除文件夹权限
     * @param folderPermissionManager 需要删除删除的id
     * @return
     */
    @PostMapping("/deleteFolderPermissions")
    public RemoteResult deleteFolderPermissions(@RequestBody FolderPermissionManager folderPermissionManager) {
        Assert.notNull(folderPermissionManager.getId(),"文件夹权限id不能为空");
        folderPermissionManagerService.deleteFolderPermissions(folderPermissionManager.getId());
        return new RemoteResult("success");
    }

    /**
     *  删除文件夹权限
     * @param ids 需要删除删除的id
     * @return
     */
    @PostMapping("/batchDeleteFolderPermissions")
    public RemoteResult deleteFolderPermissions(@RequestBody List<Long> ids) {
        Assert.notEmpty(ids,"文件夹权限id不能为空");
        for (Long id : ids) {
            folderPermissionManagerService.deleteFolderPermissions(id);
        }
        return new RemoteResult("success");
    }

    /**
     * 根据文件夹id获取文件夹用户权限
     * @param dirId 文件夹id
     * @return 文件夹权限列表
     */
    @GetMapping("/getFolderPermissions")
    public RemoteResult getFolderPermissionsByDirId(Long dirId){
        List<FolderPermissionManager> folderPermissions = folderPermissionManagerService.getFolderPermissionsByDirId(dirId);
        return new RemoteResult(folderPermissions);
    }
}
