package com.uinnova.product.eam.service.cj.service.impl;

import com.binary.jdbc.Page;
import com.uinnova.product.eam.service.cj.dao.PlanDesignInstanceDao;
import com.uinnova.product.eam.service.cj.dao.PlanSystemRelationDao;
import com.uinnova.product.eam.model.cj.domain.PlanDesignInstance;
import com.uinnova.product.eam.model.cj.domain.PlanSystemRelation;
import com.uinnova.product.eam.model.cj.vo.SystemPlanVo;
import com.uinnova.product.eam.service.cj.service.ArchitectureAssetService;
import com.uinnova.product.eam.service.exception.BusinessException;
import org.springframework.util.CollectionUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: Lc
 * @create: 2022-01-12 11:29
 */
@Service
public class ArchitectureAssetServiceImpl implements ArchitectureAssetService {

    @Resource
    private PlanSystemRelationDao planSystemRelationDao;

    @Resource
    private PlanDesignInstanceDao planDesignInstanceDao;

    @Override
    public Page<PlanDesignInstance> findPlanList(SystemPlanVo systemPlanVo) {
        if (systemPlanVo == null) {
            throw new BusinessException("参数不能为空!");
        }
        if (systemPlanVo.getSystemId() == null) {
            throw new BusinessException("系统信息不能为空!");
        }
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("systemId", systemPlanVo.getSystemId()));
        query.must(QueryBuilders.termQuery("status", 2));
        List<PlanSystemRelation> planSystemList = planSystemRelationDao.getListByQuery(query);
        if (!CollectionUtils.isEmpty(planSystemList)) {
            Set<Long> planIdSet = planSystemList.stream().map(PlanSystemRelation::getPlanId).collect(Collectors.toSet());
            PlanDesignInstance planDesign = new PlanDesignInstance();
            planDesign.setIds(planIdSet.toArray(new Long[0]));
            return planDesignInstanceDao.getSortListByCdt(systemPlanVo.getPageNum(), systemPlanVo.getPageSize(), planDesign, "modifyTime", false);
        }
        return null;
    }
}
