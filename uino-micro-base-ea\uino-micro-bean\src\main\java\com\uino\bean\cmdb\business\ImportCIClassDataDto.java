package com.uino.bean.cmdb.business;

import com.binary.core.i18n.LanguageResolver;
import com.binary.core.util.BinaryUtils;
import com.uino.bean.permission.business.IValidDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.Assert;

/**
 * 导入CI类定义数据传输类
 * 
 * <AUTHOR>
 *
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ImportCIClassDataDto implements IValidDto {

    /**
     * 行号
     */
    private Integer index;
    /**
     * 领域名称
     */
    private String dirName;
    /*
     * 分类名称
     */
    private String className;
    /*
     * 分类名称
     */
    private String classCode;
    /*
     * 父类名称 
     */
    private String parentName;
    /*
     * 图标名称
     */
    private String imgName;
    /**
     * 属性名称
     */
    private String attrName;
    /*
     * 属性类型
     */
    private String attrType;
    /*
     * 是否必填
     */
    private String isRequired;
    /*
     * 是否label
     */
    private String isLabel;
    /*
     * 是否主键
     */
    private String isMajor;
    /*
     * 属性默认值
     */
    private String defValue;

    /*
     * 属性默认值
     */
    private String attrDesc;

    /*
     * 属性TAG
     */
    private String attrTag;

    /*
     * 类型约束
     */
    private String attrTypeRule;

    public boolean isRequired() {
        return "是".equals(isRequired);
    }
    
    public boolean islabel() {
        return "是".equals(isLabel);
    }

    public boolean isMajor() {
        return "是".equals(isMajor);
    }

    @Override
    public void valid() {
        // 分类数据有误时，领域依然保存，将领域单独校验
        // Assert.notNull(dirName, "领域名称不可为空");
        // Assert.isTrue(dirName.trim().length() <= 20, "领域名称不可超过20位");
        // Assert.isTrue(dirName.matches("[A-Za-z0-9 _\\-\\@\\.\\(\\)\\u4e00-\\u9fa5]+"),
        // LanguageResolver.trans("BS_MNAME_IMAGE_DIR_NAME_FORMAT"));

        Assert.notNull(className, "分类名称不可为空");
        Assert.isTrue(className.matches("[^\\:\\/\\?\\*\\[\\]\\\\]{1,30}"), "分类名称不可超过30位，且不可包含:/\\?*[]等字符");

        Assert.notNull(attrName, "属性名称不可为空");
        Assert.isTrue(attrName.trim().length() <= 50, "属性名称不可超过50位");
        Assert.isTrue(attrName.trim().matches("^[A-Za-z0-9\\u4e00-\\u9fa5 @\\（\\）\\(\\)\\&\\-\\|\\/\\\\'_]+$"), "属性[" + attrName + "]包含非法字符，仅支持 _&()（）|/\\'-@");

        Assert.notNull(attrType, "属性类型不可为空");
        // this.checkAttrType(attrType);
        Assert.isTrue(BinaryUtils.isEmpty(isRequired) || (isRequired.equals("是") || isRequired.equals("否")), "[" + isRequired + "]字符违法");
        Assert.isTrue(BinaryUtils.isEmpty(isLabel) || (isLabel.equals("是") || isLabel.equals("否")), "[" + isLabel + "]字符违法");
        Assert.isTrue(BinaryUtils.isEmpty(isMajor) || (isMajor.equals("是") || isMajor.equals("否")), "[" + isMajor + "]字符违法");
    }

    public void validDir() {
        Assert.notNull(dirName, "领域名称不可为空");
        Assert.isTrue(dirName.trim().length() <= 20, "领域名称不可超过20位");
        Assert.isTrue(dirName.matches("[A-Za-z0-9 _\\-\\@\\.\\(\\)\\u4e00-\\u9fa5]+"), LanguageResolver.trans("BS_MNAME_IMAGE_DIR_NAME_FORMAT"));
    }

}
