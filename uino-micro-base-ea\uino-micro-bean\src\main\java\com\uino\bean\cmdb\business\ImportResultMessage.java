package com.uino.bean.cmdb.business;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import com.uino.bean.cmdb.base.ESCIRltInfo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 
 * <AUTHOR>
 *
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value="导入数据类",description = "导入数据信息")
public class ImportResultMessage implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value="默认导入信息总记录数",example = "0")
    @Builder.Default
    private Integer defTotalNum = 0;

    @ApiModelProperty(value="默认成功导入记录数",example = "0")
    @Builder.Default
    private Integer defSuccessNum = 0;

    @ApiModelProperty(value="默认失败导入记录数",example = "0")
    @Builder.Default
    private Integer defFailNum = 0;

    @ApiModelProperty(value="导入记录数",example = "0")
    @Builder.Default
    private Integer totalNum = 0;

    @ApiModelProperty(value="成功导入记录数",example = "0")
    @Builder.Default
    private Integer successNum = 0;
    @ApiModelProperty(value="插入记录数",example="0")
    @Builder.Default
    private Integer insertNum = 0;

    @ApiModelProperty(value="修改记录数",example="0")
    @Builder.Default
    private Integer updateNum = 0;

    @ApiModelProperty(value="失败记录数",example="0")
    @Builder.Default
    private Integer failNum = 0;

/*
    @Builder.Default
    private Integer dbFail = 0;
*/
    @ApiModelProperty(value="导入明细地址",example = "http://ip/abc.xlsx")
    private String failFile;

    @ApiModelProperty(value="保存成功的关系数据")
    @Builder.Default
    private List<ESCIRltInfo> sucessRlts = new ArrayList<>();

    public ImportResultMessage(ImportSheetMessage importSheetMsg) {
        this.successNum = importSheetMsg.getSuccessNum();
        this.failNum = importSheetMsg.getFailNum() + importSheetMsg.getIgnoreNum();
       // this.dbFail = importSheetMsg.getDbFailNum();
        this.failFile = importSheetMsg.getDetailUrl();
    }
}
