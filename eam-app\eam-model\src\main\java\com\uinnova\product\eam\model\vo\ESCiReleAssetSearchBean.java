package com.uinnova.product.eam.model.vo;

import com.uino.bean.cmdb.query.ESSearchBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 关联资产列表查询
 */
@Data
public class ESCiReleAssetSearchBean extends ESSearchBase implements Serializable {


    @ApiModelProperty(value = "数据筛选ID")
    private Long bindId;

    /**
     * CI分类ID
     */
    @ApiModelProperty(value = "CI所属分类id")
    private Long classId;

    /**
     * CICODE 以前的唯一值，现在可能不是唯一值
     */
    @ApiModelProperty(value = "ciCode列表")
    private List<String> ciCodes = new ArrayList<>();


    @ApiModelProperty(value = "模糊搜索值，查询主键")
    private String like;

}
