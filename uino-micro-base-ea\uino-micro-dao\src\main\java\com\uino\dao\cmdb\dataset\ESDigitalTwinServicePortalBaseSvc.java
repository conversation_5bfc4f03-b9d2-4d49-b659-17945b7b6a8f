package com.uino.dao.cmdb.dataset;


import com.uino.dao.AbstractESBaseDao;
import com.uino.dao.ESConst;
import com.uino.dao.util.ESUtil;
import com.uino.bean.dataset.base.DigitalTwinServicePortalInfo;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import jakarta.annotation.PostConstruct;

/**
 * <AUTHOR>
 * @date 2021/6/8 13:36
 */
@Component
@Log4j2
public class ESDigitalTwinServicePortalBaseSvc extends AbstractESBaseDao<DigitalTwinServicePortalInfo, DigitalTwinServicePortalInfo> {
    @Override
    public String getIndex() {
        return ESConst.INDEX_DIGITAL_TWIN_SERVICE_PORTAL;
    }

    @Override
    public String getType() {
        return ESConst.INDEX_DIGITAL_TWIN_SERVICE_PORTAL;
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }

    public DigitalTwinServicePortalInfo queryTwinServiceDetailsById(Long id) {
        return this.getById(id);
    }

    public Long saveOrUpdateDigitalTwinServiceInfo(DigitalTwinServicePortalInfo info){
        if (ObjectUtils.isEmpty(info.getId())){
            long id = ESUtil.getUUID();
            info.setId(id);

        }
        return this.saveOrUpdate(info);
    }
}
