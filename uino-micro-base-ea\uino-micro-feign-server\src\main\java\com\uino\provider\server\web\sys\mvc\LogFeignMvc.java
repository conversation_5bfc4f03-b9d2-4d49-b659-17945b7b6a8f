package com.uino.provider.server.web.sys.mvc;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.binary.jdbc.Page;
import com.uino.service.sys.microservice.ILogSvc;
import com.uino.bean.sys.base.SysLoginLog;
import com.uino.bean.sys.business.QueryLoginLogRequestDto;
import com.uino.provider.feign.sys.LogFeign;

@RestController
@RequestMapping("feign/sys/log")
public class LogFeignMvc implements LogFeign {

    @Autowired
    private ILogSvc svc;

    @Override
    public SysLoginLog addLoginLog(Long userId) {
        return svc.addLoginLog(userId);
    }

    @Override
    public Page<SysLoginLog> queryLoginLog(QueryLoginLogRequestDto query) {
        return svc.queryLoginLog(query);
    }

    @Override
    public SysLoginLog addLoginLog(Long domainId, String userCode) {
        return svc.addLoginLog(domainId, userCode);
    }
}
