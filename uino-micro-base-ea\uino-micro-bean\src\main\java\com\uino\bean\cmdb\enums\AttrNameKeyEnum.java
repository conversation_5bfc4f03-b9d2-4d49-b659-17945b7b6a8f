package com.uino.bean.cmdb.enums;

import com.binary.framework.exception.ServiceException;

public enum AttrNameKeyEnum {

	INTEGER("整数", 1), DOUBLE("小数", 2), VARCHAR("字符串", 3),
	LONG_VARCHAR("文本", 4), CLOB("文章", 5), ENUM("枚举", 6),
	DATE("日期", 7), DICT("数据字典", 8), MODEL("3D模型", 9),
	PICTURE("图片", 10),EXTERNAL_ATTR("关联属性",11),ENCODE("编码", 150),ORGANIZATION("部门", 160),
	DOCUMENT("文档",12),INTERFACE("接口",13),ATTACHMENT("附件",14),
	PERSION("人员",15),CALCULATE("计算",16),PERCENT("百分比",17),
	LINK_CI("关联资产",18),LINK_PLAN("关联方案",19),INTEGER_CODE("整数编码",20),
	PREFIX_INTEGER_CODE("前缀编码",21);

	private String value;

	private int type;

	private AttrNameKeyEnum(String value, Integer type) {
		this.value = value;
		this.type = type;
	}

	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}

	public int getType() {
		return type;
	}

	public void setType(int type) {
		this.type = type;
	}

	public static AttrNameKeyEnum valueOf(int v) {
		switch (v) {
			case 1:
				return INTEGER;
			case 2:
				return DOUBLE;
			case 3:
				return VARCHAR;
			case 4:
				return LONG_VARCHAR;
			case 5:
				return CLOB;
			case 6:
				return ENUM;
			case 7:
				return DATE;
			case 8:
				return DICT;
			case 9:
				return MODEL;
			case 10:
				return PICTURE;
			case 11:
				return EXTERNAL_ATTR;
			case 12:
				return DOCUMENT;
			case 13:
				return INTERFACE;
			case 14:
				return ATTACHMENT;
			case 15:
				return PERSION;
			case 16:
				return CALCULATE;
			case 17:
				return PERCENT;
			case 18:
				return LINK_CI;
			case 19:
				return LINK_PLAN;
			case 20:
				return INTEGER_CODE;
			case 21:
				return PREFIX_INTEGER_CODE;
			case 150:
				return ENCODE;
			case 160:
				return ORGANIZATION;


			default:
				throw new ServiceException(" is wrong PropertyType-value '" + v + "'! ");
		}
	}

	public static Integer checkAttrType(String attrType) {
		switch (attrType) {
			case "整数":
				return 1;
			case "小数":
				return 2;
			case "字符串":
				return 3;
			case "文本":
				return 4;
			case "文章":
				return 5;
			case "枚举":
				return 6;
			case "日期":
				return 7;
			case "数据字典":
				return 8;
			case "3D模型":
				return 9;
			case "图片":
				return 10;
			case "关联属性":
				return 11;
			case "文档":
				return 12;
			case "编码":
				return 150;
			case "接口":
				return 13;
			case "附件":
				return 14;
			case "人员":
				return 15;
			case "计算":
				return 16;
			case "百分比":
				return 17;
			case "关联资产":
				return 18;
			case "关联方案":
				return 19;
			case "整数编码":
				return 20;
			case "前缀编码":
				return 21;
			default:
				return null;
		}
	}

}
