package com.uino.service.cmdb.dataset.microservice;

import java.util.List;

import com.uino.bean.cmdb.business.dataset.FriendInfo;
import com.uino.bean.cmdb.business.dataset.UpDownAttrCdt;
import com.uino.service.cmdb.microservice.ICISvc;
import com.uino.service.cmdb.microservice.ICIRltSvc;

public interface IGraphAnalysisBase {

	/**
     * 上下N层(多入口CI)
     *
     * @param domainId        	domainId
     * @param startCiIds        起始ciIds
     * @param ciConditions		CI过滤条件
     * @param rltConditions		关系过滤条件
     * @param rltLvls			关系子CODE过滤条件
     * @param upLevel			上几层
     * @param downLevel			下几层
     * @param hasAttr			是否需要属性
     * @param iciSvc			CI服务
     * @param iciRltSvc			关系服务
     * @return FriendInfo
     */
	public FriendInfo queryCiUpDownByCiIds(Long domainId, List<Long> startCiIds, List<UpDownAttrCdt> ciConditions, List<UpDownAttrCdt> rltConditions,
										   List<Long> rltLvls, Integer upLevel, Integer downLevel, Boolean hasAttr, ICISvc iciSvc, ICIRltSvc iciRltSvc);
	
	/**
     * 上下N层(单入口CI)
     *
     * @param domainId        	domainId
     * @param startCiId         起始ciId
     * @param ciConditions		CI过滤条件
     * @param rltConditions		关系过滤条件
     * @param rltLvls			关系子CODE过滤条件
     * @param upLevel			上几层
     * @param downLevel			下几层
     * @param hasAttr			是否需要属性
     * @param iciSvc			CI服务
     * @param iciRltSvc			关系服务
     * @return FriendInfo
     */
	public FriendInfo queryCiUpDownByCiId(Long domainId, Long startCiId, List<UpDownAttrCdt> ciConditions, List<UpDownAttrCdt> rltConditions, 
			List<Long> rltLvls, Integer upLevel, Integer downLevel, Boolean hasAttr, ICISvc iciSvc, ICIRltSvc iciRltSvc);
}
