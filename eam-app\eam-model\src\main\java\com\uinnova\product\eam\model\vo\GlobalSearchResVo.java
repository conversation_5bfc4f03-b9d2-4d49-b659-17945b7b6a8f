package com.uinnova.product.eam.model.vo;

import com.binary.framework.bean.annotation.Comment;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.base.LibType;
import lombok.Data;

import java.util.List;

@Data
public class GlobalSearchResVo {

    @Comment("资源主键id")
    private Long id;
    @Comment("资源标题")
    private String name;
    @Comment("资源创建者code")
    private String creator;
    @Comment("资源创建者姓名")
    private String creatorName;
    @Comment("资源状态-已转译")
    private String status;
    @Comment("资源类型:plan=在线方案 diagram=架构视图 model=模型 asset=资产")
    private String type;
    @Comment("资源创建时间")
    private String createTime;
    @Comment("资源修改时间")
    private String modifyTime;
    @Comment("目录id")
    private Long dirId;
    @Comment("资产类型")
    private LibType libType;
    @Comment("资产路径(绝对路径)")
    private List<GlobalSearchResDirVo> dirPath;
    @Comment("资产仓库根目录id")
    private Long rootId;

    //方案专属字段------------begin------------
    @Comment("视图类型")
    private String viewType;
    @Comment("视图id加密字段")
    private String dEnergy;
    @Comment("视图是否公开:1=开放 0=私有")
    private Integer isOpen;
    //方案专属字段------------end--------------


    //视图专属字段------------begin------------
    @Comment("方案资产类型：1:设计库 2:资产库")
    private Integer assetsType;
    //视图专属字段------------end--------------


    //资产专属字段------------begin------------
    @Comment("所属分类code")
    private String classCode;
    @Comment("所属分类id")
    private Long classId;
    @Comment("资产cicode")
    private String ciCode;
    @Comment("资产管理卡片id")
    private Long appSquareConfId;
    @Comment("资产ci")
    private ESCIInfo ciInfo;

    //资产专属字段------------end--------------

    public String getdEnergy() {
        return dEnergy;
    }

    public void setdEnergy(String dEnergy) {
        this.dEnergy = dEnergy;
    }
}
