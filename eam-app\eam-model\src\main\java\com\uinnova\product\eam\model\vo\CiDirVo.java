package com.uinnova.product.eam.model.vo;

import lombok.Data;

import java.io.Serializable;

@Data
public class CiDirVo implements Serializable {

    /** 子系统名称 */
    private String systemName;
    /** 子系统标识 */
    private String systemSign;
    /** 子系统分类 */
    private String systemType;
    /** 重要性等级 */
    private String level;
    /** 投产状态 */
    private String productStatus;
    /** 归属处室 */
    private String belongDepart;
    /** 子系统负责人 */
    private String leader;
    /** 业务联系人及部门 */
    private String contacts;
    /** 子系统简介 */
    private String remark;
    /** 1:子系统 2：应用系统 */
    private Integer sysSign;
}
