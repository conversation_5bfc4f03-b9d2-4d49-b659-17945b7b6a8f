package com.uinnova.product.eam.base.model;

import com.uinnova.product.eam.comm.model.es.EamResource;
import lombok.Data;

import java.io.Serializable;

@Data
public class ResourceInfo implements Serializable {

    private Long id;

    private String name;

    private Integer type;

    private String operator;

    private Long createTime;

    private String resPath;

    public ResourceInfo(){
    }

    public ResourceInfo(EamResource resource){
        this.id = resource.getId();
        this.name = resource.getName() + "." + resource.getResType();
        this.operator = resource.getOperator();
        this.createTime = resource.getCreateTime();
        this.resPath = resource.getResPath();
        this.type = resource.getType();
    }

}
