package com.uino.bean.sys.base;

import java.io.Serializable;
import java.util.regex.Pattern;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;

import com.binary.core.util.BinaryUtils;
import com.uino.bean.permission.business.IValidDto;

import lombok.Data;

@Data
@ApiModel(value = "渠道通知", description = "渠道通知")
public class NotifyChannel implements Serializable, IValidDto {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID", example = "27393335556218", required = true)
    private Long id;

    @ApiModelProperty(value = "是否启用", example = "true/false", required = true)
    private boolean enable;

    @ApiModelProperty(value = "渠道名称", example = "渠道", required = true)
    private String name;

    @ApiModelProperty(value = "渠道类型", example = "EMAIL:邮件 / HTTP:http", required = true)
    private String channelType;

    @ApiModelProperty(value = "邮件渠道详情", example = "邮件渠道详情")
    private EmailChannelInfo emailChannelInfo;

    @ApiModelProperty(value = "http渠道详情", example = "http渠道详情")
    private HttpChannelInfo httpChannelInfo;

    @ApiModelProperty(value = "模板序号", example = "1", required = true)
    private String templateNum;

    @ApiModelProperty(value = "详情信息", example = "详情信息", required = true)
    private String desc;

    @ApiModelProperty(value = "创建者昵称", example = "创建者昵称", required = true)
    private String createNickName;

    @ApiModelProperty(value = "所属域", example = "1L", required = true)
    private Long domainId;

    @ApiModelProperty(value = "创建人", example = "创建人", required = true)
    private String creator;

    @ApiModelProperty(value = "修改人", example = "修改人", required = true)
    private String modifier;

    @ApiModelProperty(value = "创建时间", required = true)
    private Long createTime;

    @ApiModelProperty(value = "创建时间", required = true)
    private Long modifyTime;

    /**
     * <AUTHOR>
     */
    @Data
    @ApiModel(value = "邮件渠道详细信息", description = "邮件渠道详细信息")
    public static class EmailChannelInfo {

        @ApiModelProperty(value = "收件人邮箱信息", example = "收件人邮箱信息", required = true)
        private String destUrl;

        @ApiModelProperty(value = "SMTP主机地址", example = "smtp.exmail.qq.com", required = true)
        private String smtpHost;

        @ApiModelProperty(value = "SMTP端口号", example = "465", required = true)
        private String smtpPort;

        @ApiModelProperty(value = "用户名", required = true)
        private String smtpUser;

        @ApiModelProperty(value = "密码", required = true)
        private String smtpPwd;

        @ApiModelProperty(value = "发件人信息", required = true)
        private String mailSender;

    }

    @Data
    @ApiModel(value = "HTTP渠道详细信息", description = "HTTP渠道详细信息")
    public static class HttpChannelInfo {

        @ApiModelProperty(value = "URL", example = "http://", required = true)
        private String destUrl;

        @ApiModelProperty(value = "参数", required = true)
        private String param;
    }

    @Override
    public void valid() {
        // TODO Auto-generated method stub
        Assert.isTrue(StringUtils.isNotBlank(this.getName()), "渠道名称不可为空");
        Assert.isTrue(!BinaryUtils.isEmpty(channelType), "渠道类型不可为空");
        Assert.isTrue(NotifyTypeConstants.getNotifyTypes().contains(channelType), "不支持的渠道类型");
        if (channelType.equals(NotifyTypeConstants.EMAIL)) {
            Assert.isTrue(!BinaryUtils.isEmpty(emailChannelInfo.getSmtpHost()), "X_PARAM_NOT_NULL${name:smtpHost}");
            Assert.isTrue(!BinaryUtils.isEmpty(emailChannelInfo.getSmtpUser()), "X_PARAM_NOT_NULL${name:smtpUser}");
            Assert.isTrue(!BinaryUtils.isEmpty(emailChannelInfo.getSmtpPwd()), "X_PARAM_NOT_NULL${name:smtpPwd}");
            if (BinaryUtils.isEmpty(emailChannelInfo.getSmtpPort())) {
                emailChannelInfo.setSmtpPort("25");
            }
            Assert.isTrue(Pattern.matches("\\w[-\\w.+]*@([a-z0-9A-Z]+(-[a-z0-9A-Z]+)?\\.)+[a-zA-Z]{2,}$", emailChannelInfo.getSmtpUser()), "smtpUser邮箱格式错误");
            Assert.isTrue(Pattern.compile("[0-9]{1,}").matcher(emailChannelInfo.getSmtpPort()).matches(), "smtpPort格式错误");
        } else if (channelType.equals(NotifyTypeConstants.HTTP)) {
            Assert.isTrue(!BinaryUtils.isEmpty(httpChannelInfo.getDestUrl()), "X_PARAM_NOT_NULL${name:url}");
            Assert.isTrue(Pattern.compile("^((https|http)?://)[^\\s]+").matcher(httpChannelInfo.getDestUrl()).matches(), "URL格式错误");
        }
    }
}
