package com.uinnova.product.eam.model.dto;

import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.io.Serializable;

/**
 * @description: 元模型Dto
 * @author: Lc
 * @create: 2021-11-05 10:30
 */
@Data
public class VisualModelsDto implements Serializable {

    // 上传的json文件
    private String jsonFileStr;
    // 元模型id
    private Long visualModelId;
    // sheetId
    private Long sheetId;

    private String publishDescription;

}
