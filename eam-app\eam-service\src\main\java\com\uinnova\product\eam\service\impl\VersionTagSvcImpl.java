package com.uinnova.product.eam.service.impl;

import com.binary.core.util.BinaryUtils;
import com.uinnova.product.eam.comm.model.es.EamVersionDir;
import com.uinnova.product.eam.comm.model.es.EamVersionTag;
import com.uinnova.product.eam.service.IVersionTagSvc;
import com.uinnova.product.eam.service.es.EamVersionDirDao;
import com.uinnova.product.eam.service.es.EamVersionTagDao;
import com.uinnova.product.vmdb.comm.i18n.MessageUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 版本标签数据操作层接口实现
 * <AUTHOR>
 */
@Service
public class VersionTagSvcImpl implements IVersionTagSvc {

    @Autowired
    private EamVersionDirDao versionDirDao;

    @Autowired
    private EamVersionTagDao versionTagDao;

    @Override
    public EamVersionTag getByTagId(Long id) {
        return versionTagDao.getById(id);
    }

    @Override
    public EamVersionDir getByDirId(Long id) {
        return versionDirDao.getById(id);
    }

    @Override
    public Long saveOrUpdateTag(EamVersionTag dto) {
        return versionTagDao.saveOrUpdate(dto);
    }

    @Override
    public Integer saveVersionDirBatch(List<EamVersionDir> list) {
        return versionDirDao.saveOrUpdateBatch(list);
    }

    @Override
    public List<EamVersionTag> getVersionTagByBranchId(Long branchId) {
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.must(QueryBuilders.termQuery("branchId", branchId));
        boolQuery.must(QueryBuilders.termQuery("dataStatus", 1));
        return versionTagDao.getListByQuery(boolQuery);
    }

    @Override
    public Map<Long, List<EamVersionTag>> getVersionTagMapByBranchId(Set<Long> branchIds) {
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.must(QueryBuilders.termsQuery("branchId", branchIds));
        boolQuery.must(QueryBuilders.termQuery("dataStatus", 1));
        List<EamVersionTag> tagList = versionTagDao.getListByQuery(boolQuery);
        if(BinaryUtils.isEmpty(tagList)){
            return new HashMap<>(1);
        }
        tagList.sort(Comparator.comparing(EamVersionTag::getCreateTime).reversed());
        return tagList.stream().collect(Collectors.groupingBy(EamVersionTag::getBranchId));
    }

    @Override
    public List<EamVersionDir> getVersionDirByTagId(Long tagId) {
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.must(QueryBuilders.termQuery("tagId", tagId));
        return versionDirDao.getListByQuery(boolQuery);
    }

    @Override
    public Integer deleteByBranchId(Long branchId) {
        MessageUtil.checkEmpty(branchId, "branchId");
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.must(QueryBuilders.termQuery("branchId", branchId));
        List<EamVersionTag> tagList = versionTagDao.getListByQuery(boolQuery);
        if(CollectionUtils.isEmpty(tagList)){
            return 1;
        }
        List<Long> tagIds = tagList.stream().map(EamVersionTag::getId).collect(Collectors.toList());
        BoolQueryBuilder delDir = QueryBuilders.boolQuery();
        delDir.must(QueryBuilders.termsQuery("tagId", tagIds));
        versionDirDao.deleteByQuery(delDir, true);
        return versionTagDao.deleteByIds(tagIds);
    }
}
