package com.uinnova.product.eam.comm.model;




import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;


@Comment("视图评论表[VC_COMMENT]")
public class CVcComment implements Condition {
	private static final long serialVersionUID = 1L;


	@Comment("ID[ID] operate-Equal[=]")
	private Long id;


	@Comment("ID[ID] operate-In[in]")
	private Long[] ids;


	@Comment("ID[ID] operate-GTEqual[>=]")
	private Long startId;

	@Comment("ID[ID] operate-LTEqual[<=]")
	private Long endId;


	@Comment("视图ID[DIAGRAM_ID] operate-Equal[=]")
	private Long diagramId;


	@Comment("视图ID[DIAGRAM_ID] operate-In[in]")
	private Long[] diagramIds;


	@Comment("视图ID[DIAGRAM_ID] operate-GTEqual[>=]")
	private Long startDiagramId;

	@Comment("视图ID[DIAGRAM_ID] operate-LTEqual[<=]")
	private Long endDiagramId;


	@Comment("评论用户ID[USER_ID] operate-Equal[=]")
	private Long userId;


	@Comment("评论用户ID[USER_ID] operate-In[in]")
	private Long[] userIds;


	@Comment("评论用户ID[USER_ID] operate-GTEqual[>=]")
	private Long startUserId;

	@Comment("评论用户ID[USER_ID] operate-LTEqual[<=]")
	private Long endUserId;


	@Comment("评论用户代码[USER_CODE] operate-Like[like]")
	private String userCode;


	@Comment("评论用户代码[USER_CODE] operate-Equal[=]")
	private String userCodeEqual;


	@Comment("评论用户代码[USER_CODE] operate-In[in]")
	private String[] userCodes;


	@Comment("评论用户姓名[USER_NAME] operate-Like[like]")
	private String userName;


	@Comment("评论用户姓名[USER_NAME] operate-Equal[=]")
	private String userNameEqual;


	@Comment("评论用户姓名[USER_NAME] operate-In[in]")
	private String[] userNames;


	@Comment("评论时间[COM_TIME] operate-Equal[=]")
	private Long comTime;


	@Comment("评论时间[COM_TIME] operate-In[in]")
	private Long[] comTimes;


	@Comment("评论时间[COM_TIME] operate-GTEqual[>=]")
	private Long startComTime;

	@Comment("评论时间[COM_TIME] operate-LTEqual[<=]")
	private Long endComTime;


	@Comment("评论内容[COM_DESC] operate-Like[like]")
	private String comDesc;


	@Comment("所属域[DOMAIN_ID] operate-Equal[=]")
	private Long domainId;


	@Comment("所属域[DOMAIN_ID] operate-In[in]")
	private Long[] domainIds;


	@Comment("所属域[DOMAIN_ID] operate-GTEqual[>=]")
	private Long startDomainId;

	@Comment("所属域[DOMAIN_ID] operate-LTEqual[<=]")
	private Long endDomainId;


	@Comment("创建时间[CREATE_TIME] operate-Equal[=]    yyyyMMddHHmmss")
	private Long createTime;


	@Comment("创建时间[CREATE_TIME] operate-In[in]    yyyyMMddHHmmss")
	private Long[] createTimes;


	@Comment("创建时间[CREATE_TIME] operate-GTEqual[>=]    yyyyMMddHHmmss")
	private Long startCreateTime;

	@Comment("创建时间[CREATE_TIME] operate-LTEqual[<=]    yyyyMMddHHmmss")
	private Long endCreateTime;


	@Comment("修改时间[MODIFY_TIME] operate-Equal[=]    yyyyMMddHHmmss")
	private Long modifyTime;


	@Comment("修改时间[MODIFY_TIME] operate-In[in]    yyyyMMddHHmmss")
	private Long[] modifyTimes;


	@Comment("修改时间[MODIFY_TIME] operate-GTEqual[>=]    yyyyMMddHHmmss")
	private Long startModifyTime;

	@Comment("修改时间[MODIFY_TIME] operate-LTEqual[<=]    yyyyMMddHHmmss")
	private Long endModifyTime;




	public Long getId() {
		return this.id;
	}
	public void setId(Long id) {
		this.id = id;
	}


	public Long[] getIds() {
		return this.ids;
	}
	public void setIds(Long[] ids) {
		this.ids = ids;
	}


	public Long getStartId() {
		return this.startId;
	}
	public void setStartId(Long startId) {
		this.startId = startId;
	}


	public Long getEndId() {
		return this.endId;
	}
	public void setEndId(Long endId) {
		this.endId = endId;
	}


	public Long getDiagramId() {
		return this.diagramId;
	}
	public void setDiagramId(Long diagramId) {
		this.diagramId = diagramId;
	}


	public Long[] getDiagramIds() {
		return this.diagramIds;
	}
	public void setDiagramIds(Long[] diagramIds) {
		this.diagramIds = diagramIds;
	}


	public Long getStartDiagramId() {
		return this.startDiagramId;
	}
	public void setStartDiagramId(Long startDiagramId) {
		this.startDiagramId = startDiagramId;
	}


	public Long getEndDiagramId() {
		return this.endDiagramId;
	}
	public void setEndDiagramId(Long endDiagramId) {
		this.endDiagramId = endDiagramId;
	}


	public Long getUserId() {
		return this.userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}


	public Long[] getUserIds() {
		return this.userIds;
	}
	public void setUserIds(Long[] userIds) {
		this.userIds = userIds;
	}


	public Long getStartUserId() {
		return this.startUserId;
	}
	public void setStartUserId(Long startUserId) {
		this.startUserId = startUserId;
	}


	public Long getEndUserId() {
		return this.endUserId;
	}
	public void setEndUserId(Long endUserId) {
		this.endUserId = endUserId;
	}


	public String getUserCode() {
		return this.userCode;
	}
	public void setUserCode(String userCode) {
		this.userCode = userCode;
	}


	public String getUserCodeEqual() {
		return this.userCodeEqual;
	}
	public void setUserCodeEqual(String userCodeEqual) {
		this.userCodeEqual = userCodeEqual;
	}


	public String[] getUserCodes() {
		return this.userCodes;
	}
	public void setUserCodes(String[] userCodes) {
		this.userCodes = userCodes;
	}


	public String getUserName() {
		return this.userName;
	}
	public void setUserName(String userName) {
		this.userName = userName;
	}


	public String getUserNameEqual() {
		return this.userNameEqual;
	}
	public void setUserNameEqual(String userNameEqual) {
		this.userNameEqual = userNameEqual;
	}


	public String[] getUserNames() {
		return this.userNames;
	}
	public void setUserNames(String[] userNames) {
		this.userNames = userNames;
	}


	public Long getComTime() {
		return this.comTime;
	}
	public void setComTime(Long comTime) {
		this.comTime = comTime;
	}


	public Long[] getComTimes() {
		return this.comTimes;
	}
	public void setComTimes(Long[] comTimes) {
		this.comTimes = comTimes;
	}


	public Long getStartComTime() {
		return this.startComTime;
	}
	public void setStartComTime(Long startComTime) {
		this.startComTime = startComTime;
	}


	public Long getEndComTime() {
		return this.endComTime;
	}
	public void setEndComTime(Long endComTime) {
		this.endComTime = endComTime;
	}


	public String getComDesc() {
		return this.comDesc;
	}
	public void setComDesc(String comDesc) {
		this.comDesc = comDesc;
	}


	public Long getDomainId() {
		return this.domainId;
	}
	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}


	public Long[] getDomainIds() {
		return this.domainIds;
	}
	public void setDomainIds(Long[] domainIds) {
		this.domainIds = domainIds;
	}


	public Long getStartDomainId() {
		return this.startDomainId;
	}
	public void setStartDomainId(Long startDomainId) {
		this.startDomainId = startDomainId;
	}


	public Long getEndDomainId() {
		return this.endDomainId;
	}
	public void setEndDomainId(Long endDomainId) {
		this.endDomainId = endDomainId;
	}


	public Long getCreateTime() {
		return this.createTime;
	}
	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}


	public Long[] getCreateTimes() {
		return this.createTimes;
	}
	public void setCreateTimes(Long[] createTimes) {
		this.createTimes = createTimes;
	}


	public Long getStartCreateTime() {
		return this.startCreateTime;
	}
	public void setStartCreateTime(Long startCreateTime) {
		this.startCreateTime = startCreateTime;
	}


	public Long getEndCreateTime() {
		return this.endCreateTime;
	}
	public void setEndCreateTime(Long endCreateTime) {
		this.endCreateTime = endCreateTime;
	}


	public Long getModifyTime() {
		return this.modifyTime;
	}
	public void setModifyTime(Long modifyTime) {
		this.modifyTime = modifyTime;
	}


	public Long[] getModifyTimes() {
		return this.modifyTimes;
	}
	public void setModifyTimes(Long[] modifyTimes) {
		this.modifyTimes = modifyTimes;
	}


	public Long getStartModifyTime() {
		return this.startModifyTime;
	}
	public void setStartModifyTime(Long startModifyTime) {
		this.startModifyTime = startModifyTime;
	}


	public Long getEndModifyTime() {
		return this.endModifyTime;
	}
	public void setEndModifyTime(Long endModifyTime) {
		this.endModifyTime = endModifyTime;
	}


}


