package com.uino.init.api;

import java.util.regex.Matcher;
import java.util.regex.Pattern;
import jakarta.servlet.http.HttpServletRequest;

import org.springframework.web.servlet.mvc.condition.RequestCondition;

/**
 * @Title: ApiVersionCondition
 * @Description: ApiVersionCondition
 * @Author: YGQ
 * @Create: 2021-04-24 00:02
 **/
public class ApiVersionCondition implements RequestCondition<ApiVersionCondition> {
    private final static Pattern VERSION_PREFIX_PATTERN = Pattern.compile("/v(\\d+)/");

    private final int apiVersion;

    public ApiVersionCondition(int apiVersion) {
        this.apiVersion = apiVersion;
    }

    /**
     * latest defined would be take effect, that means, methods definition with
     * <br/>
     * override the classes definition.
     */
    @Override
    public ApiVersionCondition combine(ApiVersionCondition other) {
        return new ApiVersionCondition(other.getApiVersion());
    }


    /**
     * when more than one configured version number passed the match rule, then only
     * <br/>
     * the biggest one will take effect.
     */
    @Override
    public int compareTo(ApiVersionCondition other, HttpServletRequest request) {
        return other.getApiVersion() - this.apiVersion;
    }

    @Override
    public ApiVersionCondition getMatchingCondition(HttpServletRequest request) {
        Matcher m = VERSION_PREFIX_PATTERN.matcher(request.getRequestURI());
        if (m.find()) {
            int version = Integer.parseInt(m.group(1));
            // when applying version number bigger than configuration, then it will take effect
            if (version >= this.apiVersion) {
                return this;
            } else {
                return null;
            }
        } else {
            return this;
        }

    }

    public int getApiVersion() {
        return apiVersion;
    }
}
