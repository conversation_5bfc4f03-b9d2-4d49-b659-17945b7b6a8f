package com.uinnova.product.eam.service.diagram.impl;

import cn.hutool.core.util.RandomUtil;
import com.binary.core.util.BinaryUtils;
import com.binary.jdbc.Page;
import com.uinnova.product.eam.base.diagram.exception.DiagramNotFoundException;
import com.uinnova.product.eam.base.diagram.exception.UnAuthorizedException;
import com.uinnova.product.eam.base.diagram.model.*;
import com.uinnova.product.eam.base.diagram.utils.FileFilterUtil;
import com.uinnova.product.eam.base.diagram.utils.RedisUtil;
import com.uinnova.product.eam.db.diagram.es.ESDiagramDao;
import com.uinnova.product.eam.db.diagram.es.ESShareLinkDao;
import com.uinnova.product.eam.service.diagram.ESDiagramSvc;
import com.uinnova.product.eam.service.diagram.ESShareDiagramSvc;
import com.uinnova.product.eam.service.diagram.OnlineExploreSvc;
import com.uinnova.product.eam.service.sys.IEamSysSvc;
import com.uino.bean.permission.base.SysUser;
import com.uino.service.util.FileUtil;
import com.uino.tarsier.tarsiercom.util.IdGenerator;
import com.uino.util.sys.SysUtil;
import org.apache.commons.codec.binary.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.nio.file.Paths;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;


@Service
public class OnlineExploreSvcImpl implements OnlineExploreSvc {

    @Autowired
    private ESShareLinkDao shareLinkDao;

    @Autowired
    private ESDiagramDao esDiagramDao;

    @Autowired
    private ESDiagramSvc esDiagramSvc;

    @Autowired
    private ESShareDiagramSvc esShareDiagramSvc;

    @Autowired
    private RedisUtil redisUtil;

    @Value("${http.resource.space}")
    private String httpResouceUrl;

    @Autowired
    private IEamSysSvc iEamSysSvc;

    private static Logger log = LoggerFactory.getLogger(OnlineExploreSvcImpl.class);

    @Override
    public String getExploreKey(String diagramId) {
        //校验视图是否存在以及鉴权
        EamDiagramQuery diagramQuery = new EamDiagramQuery();
        diagramQuery.setDEnergyEqual(diagramId);
        List<ESDiagram> esDiagramList = esDiagramDao.getListByCdt(diagramQuery);
        if (!BinaryUtils.isEmpty(esDiagramList)) {
            judgeSingleDiagramAuth(esDiagramList.get(0).getId(), null, true);
        } else {
            throw new DiagramNotFoundException("视图不存在");
        }
        //生成链接
        DiagramShareLink addShareLink = new DiagramShareLink();
        String exploreKey = RandomUtil.randomString(16);
        addShareLink.setId(IdGenerator.createGenerator().getID());
        addShareLink.setDiagramId(diagramId);
        addShareLink.setLinkType(5);
        addShareLink.setLinkKey(exploreKey);
        shareLinkDao.saveOrUpdate(addShareLink);
        return exploreKey;
    }

    @Override
    public ESDiagramDTO getDiagramByExploreKey(String diagramId) {
        /*//1.根据linkKey查询链接信息，获取diagramIdl
        DiagramShareLinkQuery shareBeanQuery = new DiagramShareLinkQuery();
        shareBeanQuery.setLinkKeyEqual(exploreKey);
        List<DiagramShareLink> shareLinkList = shareLinkDao.getListByCdt(shareBeanQuery);
        if (BinaryUtils.isEmpty(shareLinkList)) {
            throw new BinaryException("key已失效");
        }
        String diagramId = shareLinkList.get(0).getDiagramId();*/
        EamDiagramQuery diagramQuery = new EamDiagramQuery();
        diagramQuery.setDEnergyEqual(diagramId);
        List<ESDiagram> esDiagramList = esDiagramDao.getListByCdt(diagramQuery);
        if (BinaryUtils.isEmpty(esDiagramList)) {
            throw new DiagramNotFoundException("视图不存在");
        }
        //2.根据diagramId查询视图详情
        Long realDiagramId = esDiagramList.get(0).getId();
        return esDiagramSvc.queryESDiagramInfoById(realDiagramId, "shareLink", false);
    }

    @Override
    public Page<ESDiagram> queryData(Integer pageNum, Integer pageSize, Long mmdId, Long pId, String like, Integer type, Integer dirType, Integer queryType, Integer dataStatus, String orders) {

        Long domainId = 1L;
        SysUser loginUser = null;
        Page<ESDiagram> page = null;
        if (mmdId == null) {
            loginUser = SysUtil.getCurrentUserInfo();
        } else {
            //预留：目前是当前登陆用户ID,后续要改成mmdId,通过mmdId查询用户ID
            ESEnterpriseSysUserQuery query = new ESEnterpriseSysUserQuery();
            query.setMmdId(mmdId);
            loginUser = iEamSysSvc.getUserIdByMmdId(query);
        }
        CVcDiagram dCdt = new CVcDiagram();
        if (loginUser != null && loginUser.getId() != null) {
            domainId = loginUser.getDomainId();
            dCdt.setUserId(loginUser.getId());
            dCdt.setDomainId(domainId);
        } else {
            return page;
        }
        dCdt.setStatus(1);
        // 查询全部视图类型diagramType 1--普通视图，3--公共模板， 4--个人模板
        dCdt.setDiagramTypes(new Integer[]{1});
        // 查询类型
        if (BinaryUtils.isEmpty(queryType)) {
            queryType = 0;
        }
        // 按照关键字模糊查询
        if (queryType == 0) {
            if (!StringUtils.isEmpty(like)) {
                dCdt.setName(like);
            }
        }
        page = esDiagramSvc.queryData(domainId, pageNum, pageSize, dCdt, "modifyTime");
        return page;
    }

    @Override
    public String getImage(String diagramId, String content) {
        Object filePathObj = redisUtil.get("explore_" + diagramId);
        String filePath = "";
        boolean pngCreation = false;
        if (filePathObj == null) {
            //新增
            pngCreation = true;
            filePath = UUID.randomUUID().toString() + ".png";
        } else {
            //更新
            filePath = (String) filePathObj;
            filePath = filePath.replace(httpResouceUrl, "");
        }
        filePath = saveOrUpdatePng(filePath, content, pngCreation);
        if (pngCreation) {
            redisUtil.set("explore_" + diagramId, filePath);
        }
        //真实的存储路径
        return filePath;
    }

    public String saveOrUpdatePng(String filePath, String fileContent, boolean isCreate) {
        byte[] contentByte;
        if (fileContent.length() >= fileContent.indexOf(";base64,") + 8) {
            String realContent = fileContent.substring(fileContent.indexOf(";base64,") + 8);
            contentByte = Base64.decodeBase64(realContent);
            if (isCreate) {
                filePath = FileFilterUtil.parseFilePath(Paths.get("/diagram_image", filePath).toString());
            }
            try {
                FileUtil.writeFile(filePath, contentByte);
            } catch (IOException e) {
//                log.warn("写入文件：{} 错误！", filePath, e);
            }
        }
        filePath = !filePath.startsWith(httpResouceUrl) ? httpResouceUrl + filePath : filePath;
        return filePath;
    }

    /**
     * <AUTHOR>
     * @Description 判断用户是否具有当前视图的权限
     * 1.不限制userId进行查询
     * 查不出---视图被删除
     * 查出来---判断视图的userId是否等于当前用户
     * 等于---有权限
     * 不等于---查询该用户是否被分享了该视图
     * 分享了，有两种权限，一种是查看，一种是编辑
     * 没分享，无权限，报错
     **/
    private ESDiagram judgeSingleDiagramAuth(Long diagramId, String type, Boolean needAuth) {
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        Long userId = currentUserInfo.getId();
        Long domainId = currentUserInfo.getDomainId();

        //1.不限制userId进行查询
        EamDiagramQuery diagramQuery = new EamDiagramQuery();
        diagramQuery.setId(diagramId);
        diagramQuery.setStatus(1);
        diagramQuery.setDataStatus(1);
        diagramQuery.setDomainId(domainId);
        if (!BinaryUtils.isEmpty(type) && "version".equals(type)) {
            diagramQuery.setHistoryVersionFlag(0);
        } else {
            diagramQuery.setHistoryVersionFlag(1);
        }
        List<ESDiagram> diagramList = esDiagramDao.getListByCdt(diagramQuery);
        //2.查不出，视图不存在，报错
        if (BinaryUtils.isEmpty(diagramList)) {
            throw new DiagramNotFoundException("操作视图不存在，请核对视图当前信息");
        }
        //3.查出来，判断其是否具有视图权限
        ESDiagram esDiagram = diagramList.get(0);
        if (needAuth) {
            //模板不需要校验权限
            if (esDiagram.getDiagramType().equals(3)) {
                return esDiagram;
            }
            if (esDiagram.getUserId().equals(userId)) {
                //视图属于当前用户
                return esDiagram;
            } else {
                //视图不属于当前用户，判断该视图是否分享给了该用户
                Map<Long, Set<Long>> diagramIdShareRecordMap = esShareDiagramSvc.queryDiagramSharedUserIds(new Long[]{diagramId});
                Set<Long> shareUserIdSet = diagramIdShareRecordMap.get(diagramId);
                if (!BinaryUtils.isEmpty(shareUserIdSet) && shareUserIdSet.contains(userId)) {
                    return esDiagram;
                } else {
                    throw new UnAuthorizedException("用户无当前视图权限");
                }
            }
        }
        return esDiagram;
    }
}
