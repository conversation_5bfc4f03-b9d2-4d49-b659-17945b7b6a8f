package com.uinnova.product.vmdb.provider.rule.bean;

import com.uinnova.product.vmdb.comm.model.rule.CcRltNode;
import com.uinnova.product.vmdb.comm.model.rule.CcRltRuleInstNode;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;

public class CcRltRuleInstNodeInfo extends CcRltRuleInstNode{
	private static final long serialVersionUID = 1L;
	
	private Integer lvl;

	private CcRltNode rltNode;
	
	private CcCiClassInfo ciClassInfo;
	
	public Integer getLvl() {
		return lvl;
	}
	
	public void setLvl(Integer lvl) {
		this.lvl = lvl;
	}

	public CcRltNode getRltNode() {
		return rltNode;
	}

	public void setRltNode(CcRltNode rltNode) {
		this.rltNode = rltNode;
	}

	public CcCiClassInfo getCiClassInfo() {
		return ciClassInfo;
	}

	public void setCiClassInfo(CcCiClassInfo ciClassInfo) {
		this.ciClassInfo = ciClassInfo;
	}
	
}
