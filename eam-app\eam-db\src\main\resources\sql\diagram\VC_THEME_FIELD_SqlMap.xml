<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="VC_THEME_FIELD">


	<resultMap type="com.uinnova.product.eam.comm.model.VcThemeField" id="queryResult">
		<result column="ID" jdbcType="BIGINT" property="id"/>	<!-- ID -->
		<result column="THEME_ID" jdbcType="BIGINT" property="themeId"/>	<!-- 所属主题 -->
		<result column="CLASS_ID" jdbcType="BIGINT" property="classId"/>	<!-- 所属分类 -->
		<result column="FIELD_TYPE" jdbcType="INTEGER" property="fieldType"/>	<!-- 字段类型 -->
		<result column="FIELD_ID" jdbcType="BIGINT" property="fieldId"/>	<!-- 字段ID -->
		<result column="FIELD_NAME" jdbcType="VARCHAR" property="fieldName"/>	<!-- 字段名称 -->
		<result column="FIELD_DESC" jdbcType="VARCHAR" property="fieldDesc"/>	<!-- 字段描述 -->
		<result column="IS_SHOW" jdbcType="INTEGER" property="isShow"/>	<!-- 查看时是否显示 -->
		<result column="SHOW_UNIT" jdbcType="VARCHAR" property="showUnit"/>	<!-- 显示单位 -->
		<result column="DOMAIN_ID" jdbcType="BIGINT" property="domainId"/>	<!-- 所属域 -->
		<result column="DATA_STATUS" jdbcType="INTEGER" property="dataStatus"/>	<!-- 数据状态 -->
		<result column="CREATOR" jdbcType="VARCHAR" property="creator"/>	<!-- 创建人 -->
		<result column="MODIFIER" jdbcType="VARCHAR" property="modifier"/>	<!-- 修改人 -->
		<result column="CREATE_TIME" jdbcType="BIGINT" property="createTime"/>	<!-- 创建时间 -->
		<result column="MODIFY_TIME" jdbcType="BIGINT" property="modifyTime"/>	<!-- 修改时间 -->
	</resultMap>
	

	<sql id="sql_query_where">
		<if test="cdt != null and cdt.id != null " >and
			ID = #{cdt.id:BIGINT}
		</if>
		<if test="ids != null and ids != '' " >and
			ID in (${ids})
		</if>
		<if test="cdt != null and cdt.startId != null and cdt.startId != '' " >and
			ID &gt; #{cdt.startId:BIGINT}
		</if>
		<if test="cdt != null and cdt.endId != null and cdt.endId != '' " >and
			ID &lt; #{cdt.endId:BIGINT}
		</if>
		<if test="cdt != null and cdt.themeId != null " >and
			THEME_ID = #{cdt.themeId:BIGINT}
		</if>
		<if test="themeIds != null and themeIds != '' " >and
			THEME_ID in (${themeIds})
		</if>
		<if test="cdt != null and cdt.startThemeId != null and cdt.startThemeId != '' " >and
			THEME_ID &gt; #{cdt.startThemeId:BIGINT}
		</if>
		<if test="cdt != null and cdt.endThemeId != null and cdt.endThemeId != '' " >and
			THEME_ID &lt; #{cdt.endThemeId:BIGINT}
		</if>
		<if test="cdt != null and cdt.classId != null " >and
			CLASS_ID = #{cdt.classId:BIGINT}
		</if>
		<if test="classIds != null and classIds != '' " >and
			CLASS_ID in (${classIds})
		</if>
		<if test="cdt != null and cdt.startClassId != null and cdt.startClassId != '' " >and
			CLASS_ID &gt; #{cdt.startClassId:BIGINT}
		</if>
		<if test="cdt != null and cdt.endClassId != null and cdt.endClassId != '' " >and
			CLASS_ID &lt; #{cdt.endClassId:BIGINT}
		</if>
		<if test="cdt != null and cdt.fieldType != null " >and
			FIELD_TYPE = #{cdt.fieldType:INTEGER}
		</if>
		<if test="fieldTypes != null and fieldTypes != '' " >and
			FIELD_TYPE in (${fieldTypes})
		</if>
		<if test="cdt != null and cdt.startFieldType != null and cdt.startFieldType != '' " >and
			FIELD_TYPE &gt; #{cdt.startFieldType:INTEGER}
		</if>
		<if test="cdt != null and cdt.endFieldType != null and cdt.endFieldType != '' " >and
			FIELD_TYPE &lt; #{cdt.endFieldType:INTEGER}
		</if>
		<if test="cdt != null and cdt.fieldId != null " >and
			FIELD_ID = #{cdt.fieldId:BIGINT}
		</if>
		<if test="fieldIds != null and fieldIds != '' " >and
			FIELD_ID in (${fieldIds})
		</if>
		<if test="cdt != null and cdt.startFieldId != null and cdt.startFieldId != '' " >and
			FIELD_ID &gt; #{cdt.startFieldId:BIGINT}
		</if>
		<if test="cdt != null and cdt.endFieldId != null and cdt.endFieldId != '' " >and
			FIELD_ID &lt; #{cdt.endFieldId:BIGINT}
		</if>
		<if test="cdt != null and cdt.fieldName != null " >and
			FIELD_NAME like #{cdt.fieldName,jdbcType=VARCHAR} 
		</if>
		<if test="cdt != null and cdt.fieldNameEqual != null " >and
			FIELD_NAME like #{cdt.fieldNameEqual,jdbcType=VARCHAR} 
		</if>
		<if test="fieldNames != null and fieldNames != '' " >and
			FIELD_NAME in ${fieldNames} 
		</if>
		<if test="cdt != null and cdt.fieldDesc != null " >and
			FIELD_DESC like #{cdt.fieldDesc,jdbcType=VARCHAR} 
		</if>
		<if test="cdt != null and cdt.isShow != null " >and
			IS_SHOW = #{cdt.isShow:INTEGER}
		</if>
		<if test="isShows != null and isShows != '' " >and
			IS_SHOW in (${isShows})
		</if>
		<if test="cdt != null and cdt.startIsShow != null and cdt.startIsShow != '' " >and
			IS_SHOW &gt; #{cdt.startIsShow:INTEGER}
		</if>
		<if test="cdt != null and cdt.endIsShow != null and cdt.endIsShow != '' " >and
			IS_SHOW &lt; #{cdt.endIsShow:INTEGER}
		</if>
		<if test="cdt != null and cdt.showUnit != null " >and
			SHOW_UNIT like #{cdt.showUnit,jdbcType=VARCHAR} 
		</if>
		<if test="cdt != null and cdt.showUnitEqual != null " >and
			SHOW_UNIT like #{cdt.showUnitEqual,jdbcType=VARCHAR} 
		</if>
		<if test="showUnits != null and showUnits != '' " >and
			SHOW_UNIT in ${showUnits} 
		</if>
		<if test="cdt != null and cdt.domainId != null " >and
			DOMAIN_ID = #{cdt.domainId:BIGINT}
		</if>
		<if test="domainIds != null and domainIds != '' " >and
			DOMAIN_ID in (${domainIds})
		</if>
		<if test="cdt != null and cdt.startDomainId != null and cdt.startDomainId != '' " >and
			DOMAIN_ID &gt; #{cdt.startDomainId:BIGINT}
		</if>
		<if test="cdt != null and cdt.endDomainId != null and cdt.endDomainId != '' " >and
			DOMAIN_ID &lt; #{cdt.endDomainId:BIGINT}
		</if>
		<if test="cdt != null and cdt.dataStatus != null " >and
			DATA_STATUS = #{cdt.dataStatus:INTEGER}
		</if>
		<if test="dataStatuss != null and dataStatuss != '' " >and
			DATA_STATUS in (${dataStatuss})
		</if>
		<if test="cdt != null and cdt.startDataStatus != null and cdt.startDataStatus != '' " >and
			DATA_STATUS &gt; #{cdt.startDataStatus:INTEGER}
		</if>
		<if test="cdt != null and cdt.endDataStatus != null and cdt.endDataStatus != '' " >and
			DATA_STATUS &lt; #{cdt.endDataStatus:INTEGER}
		</if>
		<if test="cdt != null and cdt.creator != null " >and
			CREATOR like #{cdt.creator,jdbcType=VARCHAR} 
		</if>
		<if test="cdt != null and cdt.creatorEqual != null " >and
			CREATOR like #{cdt.creatorEqual,jdbcType=VARCHAR} 
		</if>
		<if test="creators != null and creators != '' " >and
			CREATOR in ${creators} 
		</if>
		<if test="cdt != null and cdt.modifier != null " >and
			MODIFIER like #{cdt.modifier,jdbcType=VARCHAR} 
		</if>
		<if test="cdt != null and cdt.modifierEqual != null " >and
			MODIFIER like #{cdt.modifierEqual,jdbcType=VARCHAR} 
		</if>
		<if test="modifiers != null and modifiers != '' " >and
			MODIFIER in ${modifiers} 
		</if>
		<if test="cdt != null and cdt.createTime != null " >and
			CREATE_TIME = #{cdt.createTime:BIGINT}
		</if>
		<if test="createTimes != null and createTimes != '' " >and
			CREATE_TIME in (${createTimes})
		</if>
		<if test="cdt != null and cdt.startCreateTime != null and cdt.startCreateTime != '' " >and
			CREATE_TIME &gt; #{cdt.startCreateTime:BIGINT}
		</if>
		<if test="cdt != null and cdt.endCreateTime != null and cdt.endCreateTime != '' " >and
			CREATE_TIME &lt; #{cdt.endCreateTime:BIGINT}
		</if>
		<if test="cdt != null and cdt.modifyTime != null " >and
			MODIFY_TIME = #{cdt.modifyTime:BIGINT}
		</if>
		<if test="modifyTimes != null and modifyTimes != '' " >and
			MODIFY_TIME in (${modifyTimes})
		</if>
		<if test="cdt != null and cdt.startModifyTime != null and cdt.startModifyTime != '' " >and
			MODIFY_TIME &gt; #{cdt.startModifyTime:BIGINT}
		</if>
		<if test="cdt != null and cdt.endModifyTime != null and cdt.endModifyTime != '' " >and
			MODIFY_TIME &lt; #{cdt.endModifyTime:BIGINT}
		</if>
	</sql>
	

	<sql id="sql_update_columns">
		<if test="record != null and record.id != null"> 
			ID = #{record.id:BIGINT},
		</if>
		<if test="record != null and record.themeId != null"> 
			THEME_ID = #{record.themeId:BIGINT},
		</if>
		<if test="record != null and record.classId != null"> 
			CLASS_ID = #{record.classId:BIGINT},
		</if>
		<if test="record != null and record.fieldType != null"> 
			FIELD_TYPE = #{record.fieldType:INTEGER},
		</if>
		<if test="record != null and record.fieldId != null"> 
			FIELD_ID = #{record.fieldId:BIGINT},
		</if>
		<if test="record != null and record.fieldName != null"> 
			FIELD_NAME = #{record.fieldName:VARCHAR},
		</if>
		<if test="record != null and record.fieldDesc != null"> 
			FIELD_DESC = #{record.fieldDesc:VARCHAR},
		</if>
		<if test="record != null and record.isShow != null"> 
			IS_SHOW = #{record.isShow:INTEGER},
		</if>
		<if test="record != null and record.showUnit != null"> 
			SHOW_UNIT = #{record.showUnit:VARCHAR},
		</if>
		<if test="record != null and record.domainId != null"> 
			DOMAIN_ID = #{record.domainId:BIGINT},
		</if>
		<if test="record != null and record.dataStatus != null"> 
			DATA_STATUS = #{record.dataStatus:INTEGER},
		</if>
		<if test="record != null and record.creator != null"> 
			CREATOR = #{record.creator:VARCHAR},
		</if>
		<if test="record != null and record.modifier != null"> 
			MODIFIER = #{record.modifier:VARCHAR},
		</if>
		<if test="record != null and record.createTime != null"> 
			CREATE_TIME = #{record.createTime:BIGINT},
		</if>
		<if test="record != null and record.modifyTime != null"> 
			MODIFY_TIME = #{record.modifyTime:BIGINT},
		</if>
	</sql>
	

	<sql id="sql_query_columns">
		ID, THEME_ID, CLASS_ID, FIELD_TYPE, FIELD_ID, FIELD_NAME, 
		FIELD_DESC, IS_SHOW, SHOW_UNIT, DOMAIN_ID, DATA_STATUS, CREATOR, 
		MODIFIER, CREATE_TIME, MODIFY_TIME
	</sql>
	

	

	<select id="selectList" parameterType="java.util.Map" resultMap="queryResult">
		select 
			<include refid="VC_THEME_FIELD.sql_query_columns" />
		from VC_THEME_FIELD 
			<where>
				<include refid="VC_THEME_FIELD.sql_query_where"/>
			</where>
		order by 
			<if  test="orders != null and orders != ''">
				${orders}
			</if>
			<if  test="orders == null or orders == ''">
				ID
			</if>
	</select>
	

	

	<select id="selectCount" parameterType="java.util.Map" resultType="java.lang.Long">
		select count(1) from VC_THEME_FIELD 
			<where>
				<include refid="VC_THEME_FIELD.sql_query_where"></include>
			</where>
	</select>
	

	

	<select id="selectById" parameterType="java.util.Map" resultMap="queryResult">
		select 
			<include refid="VC_THEME_FIELD.sql_query_columns" />
		from VC_THEME_FIELD where ID=#{id:BIGINT} and DATA_STATUS=1  
	</select>
	

	

	<insert id="insert" parameterType="java.util.Map">
		insert into VC_THEME_FIELD(
			ID, THEME_ID, CLASS_ID, FIELD_TYPE, FIELD_ID, 
			FIELD_NAME, FIELD_DESC, IS_SHOW, SHOW_UNIT, DOMAIN_ID, 
			DATA_STATUS, CREATOR, MODIFIER, CREATE_TIME, MODIFY_TIME)
		values (
			#{record.id:BIGINT}, #{record.themeId:BIGINT}, #{record.classId:BIGINT}, #{record.fieldType:INTEGER}, #{record.fieldId:BIGINT}, 
			#{record.fieldName:VARCHAR}, #{record.fieldDesc:VARCHAR}, #{record.isShow:INTEGER}, #{record.showUnit:VARCHAR}, #{record.domainId:BIGINT}, 
			#{record.dataStatus:INTEGER}, #{record.creator:VARCHAR}, #{record.modifier:VARCHAR}, #{record.createTime:BIGINT}, #{record.modifyTime:BIGINT})
	</insert>
	

	

	<update id="updateById" parameterType="java.util.Map">
		update VC_THEME_FIELD
			<set> 
				<include refid="VC_THEME_FIELD.sql_update_columns"/> 
			</set>
		where ID = #{id:BIGINT}
	</update>
	

	<update id="updateByCdt" parameterType="java.util.Map">
		update VC_THEME_FIELD
			<set> 
				<include refid="VC_THEME_FIELD.sql_update_columns"/> 
			</set>
			<where> 
				<include refid="VC_THEME_FIELD.sql_query_where"/> 
			</where>
	</update>
	
	

	

	<delete id="deleteById" parameterType="java.util.Map">
		delete from VC_THEME_FIELD where ID = #{id:BIGINT}
	</delete>
	

	

	<delete id="deleteByCdt" parameterType="java.util.Map">
		delete from VC_THEME_FIELD
			<where> 
				<include refid="VC_THEME_FIELD.sql_query_where"/> 
			</where>
	</delete>
	



</mapper>
