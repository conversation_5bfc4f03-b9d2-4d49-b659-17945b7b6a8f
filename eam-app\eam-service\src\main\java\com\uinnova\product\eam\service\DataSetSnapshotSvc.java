package com.uinnova.product.eam.service;

import com.uinnova.product.eam.model.DataSetSnapshotCdt;

import java.util.List;

/**
 *  数据集临时数据接口
 */
public interface DataSetSnapshotSvc {

    /**
     *  创建数据集临时数据
     * @param assetKey
     * @param assetType 资产类型： 方案-3
     * @param dataSetId
     * @return
     */
    Long saveorupdateDataSetSnapshot(String assetKey, Integer assetType, List<Long> dataSetIds);

    /**
     *  根据资产唯一标识和数据集ID获取临时数据集数据
     * @param assetKey
     * @param assetType 资产类型： 方案-3
     * @param dataSetId
     */
    DataSetSnapshotCdt getDataSetInfoByAssetKey(String assetKey, Integer assetType, Long dataSetId);

    /**
     *  删除关联资产绑定的临时数据集
     *  @param assetKey
     * @return
     */
    Long deleteDataSetSnapshotByAssetKey(String assetKey);
}
