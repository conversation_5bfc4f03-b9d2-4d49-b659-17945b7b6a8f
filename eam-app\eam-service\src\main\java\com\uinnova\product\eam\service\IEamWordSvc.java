package com.uinnova.product.eam.service;

import com.binary.jdbc.Page;
import com.uinnova.product.eam.comm.bean.WordDoc;
import org.springframework.web.multipart.MultipartFile;

/**
 * 文档管理服务层
 *
 * <AUTHOR>
 */
@Deprecated
public interface IEamWordSvc {

    /**
     * 上传word文档
     *
     * @param ciId     ci的唯一标识
     * @param wordFile 文档数据流
     * @return 执行结果
     */
    WordDoc uploadWord(String ciId, MultipartFile wordFile);

    /**
     * 替换文档
     *
     * @param id       文档在es中的id
     * @param wordFile 文档数据流
     * @return 执行结果
     */
    boolean replaceWord(Long id, MultipartFile wordFile);

    /**
     * 删除文档
     *
     * @param id 文档唯一标识
     * @return 执行结果
     */
    boolean deleteWord(long id);

    /**
     * 根据id获取文档信息
     *
     * @param id 文档id
     * @return 文档信息
     */
    WordDoc getById(Long id);

    /**
     * 根据ciId获取所关联的文档信息
     *
     * @param ciCode 对象唯一标识
     * @return 分页对象
     */
    Page<WordDoc> getByCiCode(String ciCode, int pageNum, int pageSize);

    /**
     * 判断word文档是否存在
     *
     * @param fileName 文件名
     * @return 是否存在
     */
    boolean wordDocIsExist(String ciCode, String fileName);
}
