package com.uinnova.product.vmdb.comm.model.es;

import java.io.Serializable;
import java.util.List;

/**
 * ES使用的CLASS对象
 * 
 * <AUTHOR>
 *
 */
public class ESCiClassInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private String classCode;

    private String className;

    private String classStdCode;

    private Long dirId;

    private Integer ciType;

    private Integer classKind;

    private Long parentId;

    private Integer classLvl;

    private String classPath;

    private Integer orderNo;

    private Integer isLeaf;

    private String icon;

    private String shape;

    private String classDesc;

    private String classColor;

    private String dispFieldIds;

    private String dispFieldNames;

    private Integer costType;

    private String lineType;

    private Integer lineBorder;

    private String lineColor;

    private String lineDirect;

    private Integer lineAnime;

    private Integer lineDispType;

    private Integer lineLabelAlign;

    private Long domainId;

    private Integer dataStatus;

    private String creator;

    private String modifier;

    private Long createTime;

    private Long modifyTime;

    private List<ESCiClassAttrDef> attrDefs;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getClassCode() {
        return classCode;
    }

    public void setClassCode(String classCode) {
        this.classCode = classCode;
    }

    public String getClassName() {
        return className;
    }

    public void setClassName(String className) {
        this.className = className;
    }

    public String getClassStdCode() {
        return classStdCode;
    }

    public void setClassStdCode(String classStdCode) {
        this.classStdCode = classStdCode;
    }

    public Long getDirId() {
        return dirId;
    }

    public void setDirId(Long dirId) {
        this.dirId = dirId;
    }

    public Integer getCiType() {
        return ciType;
    }

    public void setCiType(Integer ciType) {
        this.ciType = ciType;
    }

    public Integer getClassKind() {
        return classKind;
    }

    public void setClassKind(Integer classKind) {
        this.classKind = classKind;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public Integer getClassLvl() {
        return classLvl;
    }

    public void setClassLvl(Integer classLvl) {
        this.classLvl = classLvl;
    }

    public String getClassPath() {
        return classPath;
    }

    public void setClassPath(String classPath) {
        this.classPath = classPath;
    }

    public Integer getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(Integer orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getIsLeaf() {
        return isLeaf;
    }

    public void setIsLeaf(Integer isLeaf) {
        this.isLeaf = isLeaf;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getShape() {
        return shape;
    }

    public void setShape(String shape) {
        this.shape = shape;
    }

    public String getClassDesc() {
        return classDesc;
    }

    public void setClassDesc(String classDesc) {
        this.classDesc = classDesc;
    }

    public String getClassColor() {
        return classColor;
    }

    public void setClassColor(String classColor) {
        this.classColor = classColor;
    }

    public String getDispFieldIds() {
        return dispFieldIds;
    }

    public void setDispFieldIds(String dispFieldIds) {
        this.dispFieldIds = dispFieldIds;
    }

    public String getDispFieldNames() {
        return dispFieldNames;
    }

    public void setDispFieldNames(String dispFieldNames) {
        this.dispFieldNames = dispFieldNames;
    }

    public Integer getCostType() {
        return costType;
    }

    public void setCostType(Integer costType) {
        this.costType = costType;
    }

    public String getLineType() {
        return lineType;
    }

    public void setLineType(String lineType) {
        this.lineType = lineType;
    }

    public Integer getLineBorder() {
        return lineBorder;
    }

    public void setLineBorder(Integer lineBorder) {
        this.lineBorder = lineBorder;
    }

    public String getLineColor() {
        return lineColor;
    }

    public void setLineColor(String lineColor) {
        this.lineColor = lineColor;
    }

    public String getLineDirect() {
        return lineDirect;
    }

    public void setLineDirect(String lineDirect) {
        this.lineDirect = lineDirect;
    }

    public Integer getLineAnime() {
        return lineAnime;
    }

    public void setLineAnime(Integer lineAnime) {
        this.lineAnime = lineAnime;
    }

    public Integer getLineDispType() {
        return lineDispType;
    }

    public void setLineDispType(Integer lineDispType) {
        this.lineDispType = lineDispType;
    }

    public Integer getLineLabelAlign() {
        return lineLabelAlign;
    }

    public void setLineLabelAlign(Integer lineLabelAlign) {
        this.lineLabelAlign = lineLabelAlign;
    }

    public Long getDomainId() {
        return domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }

    public Integer getDataStatus() {
        return dataStatus;
    }

    public void setDataStatus(Integer dataStatus) {
        this.dataStatus = dataStatus;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }

    public List<ESCiClassAttrDef> getAttrDefs() {
        return attrDefs;
    }

    public void setAttrDefs(List<ESCiClassAttrDef> attrDefs) {
        this.attrDefs = attrDefs;
    }

}
