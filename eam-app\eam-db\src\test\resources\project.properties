

## 通用配置 start ------------------------------------------------------------------------------------------------------
##主键服务入口地址
binarys.primarykey.root=http://101.200.75.149:1900/binarys-primarykey

##批量获取主键数量
binarys.primarykey.batch=1


## 通用配置 end ------------------------------------------------------------------------------------------------------





## 项目配置 start ----------------------------------------------------------------------------------------------------



## 项目配置 end ----------------------------------------------------------------------------------------------------






## 数据源 start ----------------------------------------------------------------------------------------------------

##数据公共属性(连接池配置)
ds.conn.pool.initialSize=0
ds.conn.pool.maxActive=10
ds.conn.pool.maxIdle=10
ds.conn.pool.minIdle=3
ds.conn.pool.validationQuery=select 1 from dual
ds.conn.pool.maxOpenPreparedStatements=100


ds.jdbc.diagram.printtype=DEBUG
ds.jdbc.diagram.writertype=CONSOLE
ds.jdbc.diagram.dsname=ds_dev_cloud
ds.jdbc.diagram.dstype=Oracle10G
ds.jdbc.diagram.driver=oracle.jdbc.driver.OracleDriver
ds.jdbc.diagram.url=*******************************************
ds.jdbc.diagram.user=vmdb
ds.jdbc.diagram.passwd=vmdb


ds.jdbc.monitor.mysql.printtype=DEBUG
ds.jdbc.monitor.mysql.writertype=CONSOLE
ds.jdbc.monitor.mysql.dsname=ds_dev_cloud_mysql
ds.jdbc.monitor.mysql.dstype=MySQL5
ds.jdbc.monitor.mysql.driver=com.mysql.jdbc.Driver
ds.jdbc.monitor.mysql.url=*********************************************************************************
ds.jdbc.monitor.mysql.user=root
ds.jdbc.monitor.mysql.passwd=root


##*************************************************
ds.mongo.uri=***********************************************************
ds.mongo.dbName=TARSIER124
ds.mongo.printCmd=true
ds.mongo.name=mongodb

## 数据源 end ----------------------------------------------------------------------------------------------------





