package com.uino.bean.cmdb.business.dataset;

import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uino.bean.cmdb.base.ESCIRltInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @Classname FriendBatchInfo
 * @Description 批量拓扑图数据集
 * @Date 2022/3/18
 * <AUTHOR>
 */
@ApiModel(value="查询数据类",description = "查询数据信息")
@Data
public class FriendBatchInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value="ci分类")
    private List<CcCiClassInfo> ciClassInfos;

    @ApiModelProperty(value="节点上的Ci,value值为ci的id")
    private Map<Long, Set<Long>> ciIdByNodeMap;

    @ApiModelProperty(value = "ci节点")
    private List<CcCiFriendInfo> ciNodes;

    @ApiModelProperty(value="ci关系线集合")
    private List<CiRltFriendInfo> ciRltLines;

}
