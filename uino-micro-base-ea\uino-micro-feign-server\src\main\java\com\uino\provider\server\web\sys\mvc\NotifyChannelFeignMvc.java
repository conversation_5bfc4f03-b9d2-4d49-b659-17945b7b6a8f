package com.uino.provider.server.web.sys.mvc;

import java.util.Collection;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.uino.service.sys.microservice.INotifyChannelSvc;
import com.uino.bean.sys.base.NotifyChannel;
import com.uino.bean.sys.business.NotifyChannelReqDto;
import com.uino.bean.sys.business.NotifyData;
import com.uino.provider.feign.sys.NotifyChannelFeign;

@RestController
@RequestMapping("feign/sys/notify_channel")
public class NotifyChannelFeignMvc implements NotifyChannelFeign {

    @Autowired
    private INotifyChannelSvc svc;

    @Override
    public NotifyChannel save(NotifyChannel saveInfo) {
        // TODO Auto-generated method stub
        return svc.save(saveInfo);
    }

    @Override
    public void delete(Collection<Long> ids) {
        // TODO Auto-generated method stub
        svc.delete(ids);
    }

    @Override
    public List<NotifyChannel> search(NotifyChannelReqDto searchDto) {
        // TODO Auto-generated method stub
        return svc.search(searchDto);
    }

    @Override
    public boolean sendNotify(NotifyData notifyData) {
        // TODO Auto-generated method stub
        return svc.sendNotify(notifyData);
    }
}
