package com.uinnova.product.eam.web.eam.mvc;

import com.binary.core.util.BinaryUtils;
import com.binary.framework.web.RemoteResult;
import com.uinnova.product.eam.comm.model.es.FolderApprovalManager;
import com.uinnova.product.eam.model.dto.FolderApprovalManagerDTO;
import com.uinnova.product.eam.service.IFolderApprovalManagerSvc;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;

/**
 * 文件夹审批用户配置
 * <AUTHOR>
 */
@RestController
@RequestMapping("/eam/approval")
public class FolderApprovalManagerMvc {
    @Resource
    private IFolderApprovalManagerSvc approvalManagerSvc;

    @PostMapping("/folder/add")
    @ModDesc(desc = "新增文件夹审批用户", pDesc = "文件夹id,用户id", rDesc = "新增结果", rType = RemoteResult.class)
    public RemoteResult addApproval(@RequestBody FolderApprovalManager dto) {
        BinaryUtils.checkEmpty(dto.getDirId(), "dirId");
        Long result = approvalManagerSvc.addApproval(dto);
        return new RemoteResult(result);
    }

    @GetMapping("/folder/query")
    @ModDesc(desc = "通过文件夹id查询", pDesc = "文件夹id", rDesc = "审批用户信息", rType = RemoteResult.class)
    public RemoteResult queryByDirId(@RequestParam Long dirId) {
        BinaryUtils.checkEmpty(dirId, "dirId");
        FolderApprovalManagerDTO result = approvalManagerSvc.queryByDirId(dirId);
        return new RemoteResult(result);
    }
}
