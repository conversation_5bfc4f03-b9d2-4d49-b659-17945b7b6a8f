package com.uino.util.express.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "方法集合描述", description = "方法集合描述，包含一个方法中的若干个重载")
public class FunctionCollectionDesc {

    @ApiModelProperty("方法名")
    private String functionName;

    @ApiModelProperty("方法别名")
    private String functionShortName;

    @ApiModelProperty("方法描述")
    private String functionDescription;

    @ApiModelProperty("方法标签")
    private List<String> tags;

    @ApiModelProperty("重载的所有方法列表")
    private List<FunctionDescription> functions;

    @ApiModelProperty("返回值类型")
    private Class<?> returnType;

}
