package com.binary.framework.bean;

import com.binary.core.i18n.Language;

public class SimpleUser implements User {
	private static final long serialVersionUID = 1L;
	
	
	private Long id;
	private String userCode;
	private String userName;
	private String loginCode;
	private String authCode;
	private Language language;
	private Long domainId;
	private Integer kind;
	
	
	
	@Override
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	
	@Override
	public String getUserCode() {
		return userCode;
	}
	public void setUserCode(String userCode) {
		this.userCode = userCode;
	}
	
	@Override
	public String getUserName() {
		return userName;
	}
	public void setUserName(String userName) {
		this.userName = userName;
	}
	
	@Override
	public String getLoginCode() {
		return loginCode;
	}
	public void setLoginCode(String loginCode) {
		this.loginCode = loginCode;
	}
	
	@Override
	public String getAuthCode() {
		return authCode;
	}
	public void setAuthCode(String authCode) {
		this.authCode = authCode;
	}
	
	@Override
	public Language getLanguage() {
		return language;
	}
	public void setLanguage(Language language) {
		this.language = language;
	}
	
	
	@Override
	public Long getDomainId() {
		return this.domainId;
	}
	
	
	
	@Override
	public Integer getKind() {
		return this.kind;
	}
	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}
	public void setKind(Integer kind) {
		this.kind = kind;
	}
	
	
	
	

	
	

}
