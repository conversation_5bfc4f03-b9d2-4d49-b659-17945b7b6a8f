package com.uino.service.sys.microservice;

import java.util.Map;

import org.springframework.web.multipart.MultipartFile;

import com.uino.bean.sys.base.Logo;

/**
 * 
 * <AUTHOR>
 *
 */
public interface ILogoSvc {
	/**
	 * 获取logo信息
	 * 
	 * @return {logoType:{logoObj}}
	 */
	public Map<String, Logo> getLogos();

	/**
	 * 修改logo
	 *
	 * @param logoType 要修改的logo类型
	 * @param file     新logo文件
	 * @return 修改后的logo信息
	 */
	public Map<String, Logo> updateLogo(String logoType, MultipartFile file);

	/**
	 * 删除logo
	 *
	 * @param logoType
	 * @return
	 */
	public Map<String, Logo> deleteLogo(String logoType);

	/**
	 * 通过图片地址更新系统Logo
	 * @param logoType 类型
	 * @param path 图片地址
	 * @param fileId 文件id
	 * @return logo信息
	 */
	Map<String, Logo> updateLogoByPath(String logoType, String path, Long fileId);
}
