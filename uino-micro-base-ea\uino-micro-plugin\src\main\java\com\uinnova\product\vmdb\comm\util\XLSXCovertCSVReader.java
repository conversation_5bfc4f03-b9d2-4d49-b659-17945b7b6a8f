package com.uinnova.product.vmdb.comm.util;

import com.binary.core.lang.Conver;
import com.binary.core.util.BinaryUtils;
import com.binary.jdbc.Page;
import com.binary.json.JSON;
import org.apache.poi.openxml4j.exceptions.OpenXML4JException;
import org.apache.poi.openxml4j.opc.OPCPackage;
import org.apache.poi.openxml4j.opc.PackageAccess;
import org.apache.poi.ss.usermodel.BuiltinFormats;
import org.apache.poi.ss.usermodel.DataFormatter;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.xssf.eventusermodel.ReadOnlySharedStringsTable;
import org.apache.poi.xssf.eventusermodel.XSSFReader;
import org.apache.poi.xssf.model.StylesTable;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFRichTextString;
import org.xml.sax.Attributes;
import org.xml.sax.InputSource;
import org.xml.sax.SAXException;
import org.xml.sax.XMLReader;
import org.xml.sax.helpers.DefaultHandler;

import javax.xml.parsers.ParserConfigurationException;
import javax.xml.parsers.SAXParser;
import javax.xml.parsers.SAXParserFactory;
import java.io.*;
import java.text.SimpleDateFormat;
import java.util.*;

/* ====================================================================
Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at
    http://www.apache.org/licenses/LICENSE-2.0
Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
==================================================================== */

/**
 * 使用CVS模式解决XLSX文件，可以有效解决用户模式内存溢出的问题
 * 该模式是POI官方推荐的读取大数据的模式，在用户模式下，数据量较大、Sheet较多、或者是有很多无用的空行的情况
 * ，容易出现内存溢出,用户模式读取Excel的典型代码如下： FileInputStream file=new
 * FileInputStream("c:\\test.xlsx"); Workbook wb=new XSSFWorkbook(file);
 * 
 * <AUTHOR>
 * 
 */
public class XLSXCovertCSVReader {

	/**
	 * The type of the data value is indicated by an attribute on the cell. The
	 * value is usually in a "v" element within the cell.
	 * 
	 * <AUTHOR>
	 */
	enum xssfDataType {

	/**
	 * ERROR
	 */
	BOOL,

	/**
	 * ERROR
	 */
	ERROR,

	/**
	 * FORMULA
	 */
	FORMULA,

	/**
	 * INLINESTR
	 */
	INLINESTR,

	/**
	 * SSTINDEX
	 */
	SSTINDEX,

	/**
	 * NUMBER
	 */
	NUMBER,
	}

	/**
	 * 使用xssf_sax_API处理Excel,请参考：
	 * http://poi.apache.org/spreadsheet/how-to.html#xssf_sax_api
	 * <p/>
	 * Also see Standard ECMA-376, 1st edition, part 4, pages 1928ff, at
	 * http://www.ecma-international.org/publications/standards/Ecma-376.htm
	 * <p/>
	 * A web-friendly version is http://openiso.org/Ecma/376/Part4
	 * 
	 * <AUTHOR>
	 */
	class MyXSSFSheetHandler extends DefaultHandler {

		/**
		 * Table with styles
		 */
		private StylesTable stylesTable;

		/**
		 * Table with unique strings
		 */
		private ReadOnlySharedStringsTable sharedStringsTable;

		/**
		 * Destination for data
		 */
		private final PrintStream output;

		/**
		 * Number of columns to read starting with leftmost
		 */
		private final int minColumnCount;

		/**
		 * Set when V start element is seen
		 */
		private boolean vIsOpen;

		/**
		 * Set when cell start element is seen; used when cell close element is seen.
		 */
		private xssfDataType nextDataType;

		/**
		 * Used to format numeric cell values.
		 */
		private short formatIndex;
		private String formatString;
		private final DataFormatter formatter;

		private int thisColumn = -1;

		/**
		 * The last column printed to the output stream
		 */
		private int lastColumnNumber = -1;

		/**
		 * Gathers characters as they are seen.
		 */
		private StringBuffer value;
		private String[] record;
		private List<String[]> rows = new ArrayList<String[]>();

		/**
		 * 保存标题行内容
		 */
		private List<String> titleRecords = new ArrayList<String>();

		/**
		 * 分页使用
		 */
		private int curRow = -1;
		private int startRow = -1;
		private int endRow = -1;
		private String[] pageRecords;

		/**
		 * Accepts objects needed while parsing.
		 * 
		 * @param styles  Table of styles
		 * @param strings Table of shared strings
		 * @param cols    Minimum number of columns to show
		 * @param target  Sink for output
		 */
		MyXSSFSheetHandler(StylesTable styles, ReadOnlySharedStringsTable strings, int cols, PrintStream target) {
			this.stylesTable = styles;
			this.sharedStringsTable = strings;
			this.minColumnCount = cols;
			this.output = target;
			this.value = new StringBuffer();
			this.nextDataType = xssfDataType.NUMBER;
			this.formatter = new DataFormatter();
			if (this.minColumnCount > 0) {
				record = new String[this.minColumnCount];
			}
			// 每次读取都清空行集合
			rows.clear();
		}

		MyXSSFSheetHandler(StylesTable styles, ReadOnlySharedStringsTable strings, int cols, PrintStream target,
				int pageNum, int pageSize) {
			this.stylesTable = styles;
			this.sharedStringsTable = strings;
			this.minColumnCount = cols;
			this.output = target;
			this.value = new StringBuffer();
			this.nextDataType = xssfDataType.NUMBER;
			this.formatter = new DataFormatter();

			this.curRow = 0;
			if (pageNum == 0) {
				pageNum = 1;
			}
			if (pageSize > 0) {
				// 分页只计算正文行
				this.startRow = (pageNum * pageSize) - pageSize + 2;
				this.endRow = pageNum * pageSize + 1;
			}

			// 每次读取都清空行集合
			rows.clear();
		}

		/**
		 * (non-Javadoc)
		 * 
		 * @see DefaultHandler#startElement(String,
		 *      String, String, Attributes)
		 */
		public void startElement(String uri, String localName, String name, Attributes attributes) throws SAXException {
			String ilStr = "inlineStr";
			String vstr = "v";
			String tstr = "t";
			String cstr = "c";
			if (ilStr.equals(name) || vstr.equals(name) || tstr.equals(name)) {
				vIsOpen = true;
				// Clear contents cache
				value.setLength(0);
			} else if (cstr.equals(name)) {
				// Get the cell reference
				String r = attributes.getValue("r");
				int firstDigit = -1;
				for (int c = 0; c < r.length(); ++c) {
					if (Character.isDigit(r.charAt(c))) {
						firstDigit = c;
						break;
					}
				}
				thisColumn = nameToColumn(r.substring(0, firstDigit));

				// Set up defaults.
				this.nextDataType = xssfDataType.NUMBER;
				this.formatIndex = -1;
				this.formatString = null;
				String cellType = attributes.getValue("t");
				String cellStyleStr = attributes.getValue("s");

				String bstr = "b";
				String estr = "e";
				String sstr = "s";
				String strStr = "str";
				if (bstr.equals(cellType)) {
					nextDataType = xssfDataType.BOOL;
				} else if (estr.equals(cellType)) {
					nextDataType = xssfDataType.ERROR;
				} else if (ilStr.equals(cellType)) {
					nextDataType = xssfDataType.INLINESTR;
				} else if (sstr.equals(cellType)) {
					nextDataType = xssfDataType.SSTINDEX;
				} else if (strStr.equals(cellType)) {
					nextDataType = xssfDataType.FORMULA;
				} else if (cellStyleStr != null) {
					// It's a number, but almost certainly one
					// with a special style or format
					int styleIndex = Integer.parseInt(cellStyleStr);
					XSSFCellStyle style = stylesTable.getStyleAt(styleIndex);
					this.formatIndex = style.getDataFormat();
					this.formatString = style.getDataFormatString();
					if (this.formatString == null) {
						this.formatString = BuiltinFormats.getBuiltinFormat(this.formatIndex);
					}
				}
			}

		}

		/**
		 * (non-Javadoc)
		 * 
		 * @see DefaultHandler#endElement(String,
		 *      String, String)
		 */
		public void endElement(String uri, String localName, String name) throws SAXException {
			String thisStr = null;

			String vstr = "v";
			String tstr = "t";
			String rowStr = "row";

			int minCol = -2;

			// v => contents of a cell
			if (vstr.equals(name)) {
				// Process the value contents as required.
				// Do now, as characters() may be called more than once
				switch (nextDataType) {

				case BOOL:
					char first = value.charAt(0);
					boolean str = first == '0' ? false : true;
					thisStr = String.valueOf(str);
					break;

				case ERROR:
					thisStr = "\"ERROR:" + value.toString() + '"';
					break;

				case FORMULA:
					// A formula could result in a string value,
					// so always add double-quote characters.
					thisStr = value.toString();
					break;

				case INLINESTR:
					// TODO: have seen an example of this, so it's untested.
					XSSFRichTextString rtsi = new XSSFRichTextString(value.toString());
					thisStr = rtsi.toString();
					break;

				case SSTINDEX:
					String sstIndex = value.toString();
					try {
						int idx = Integer.parseInt(sstIndex);
						XSSFRichTextString rtss = new XSSFRichTextString(sharedStringsTable.getItemAt(idx).getString());
						thisStr = rtss.toString();
					} catch (NumberFormatException ex) {
						output.println("Failed to parse SST index '" + sstIndex + "': " + ex.toString());
					}
					break;

				case NUMBER:
					String n = value.toString();
                    // 判断是否是日期格式
                    if (DateUtil.isADateFormat(this.formatIndex, n)) {
                        Double d = Double.parseDouble(n);
                        Date date = DateUtil.getJavaDate(d);
                        thisStr = formateDateToString(date);
                    } else if (this.formatString != null) {
                        thisStr = formatter.formatRawCellContents(Double.parseDouble(n), this.formatIndex, this.formatString);
                    } else {
                        thisStr = n;
                    }
					break;

				default:
					thisStr = "(TODO: Unexpected type: " + nextDataType + ")";
					break;
				}

				// Output after we've seen the string contents
				// Emit commas for any fields that were missing on this row
				if (lastColumnNumber == -1) {
					lastColumnNumber = 0;
				}

				if (thisStr == null) {
					thisStr = "";
				}

				if (minColumns != minCol) {
					// 正常读取
					if (record != null && record.length > 0 && thisColumn < record.length) {
						record[thisColumn] = thisStr;
					} else {
						// 没有指定读取列数则先保存列头值
						titleRecords.add(thisStr.trim());
					}
				} else if (minColumns == minCol) {
					// 分页读取
					if (pageRecords != null && pageRecords.length > 0) {
						if (thisColumn < pageRecords.length) {
							pageRecords[thisColumn] = thisStr;
						}
					} else {
						// 没有指定读取列数则先保存列头值
						titleRecords.add(thisStr);
					}
				}

				// Update column
				if (thisColumn > -1) {
					lastColumnNumber = thisColumn;
				}

			} else if (tstr.equals(name)) {
				thisStr = value.toString();
				if (thisStr == null) {
					thisStr = "";
				}

				if (lastColumnNumber == -1) {
					lastColumnNumber = 0;
				}

				if (minColumns != minCol) {
					// 正常读取
					if (record != null && record.length > 0 && thisColumn < record.length) {
						record[thisColumn] = thisStr;
					} else {
						// 没有指定读取列数则先保存列头值
						titleRecords.add(thisStr);
					}
				} else if (minColumns == minCol) {
					// 分页读取
					if (pageRecords != null && pageRecords.length > 0) {
						if (thisColumn < pageRecords.length) {
							pageRecords[thisColumn] = thisStr;
						}
					} else {
						// 分页的第一行没有指定数组长度,读取的第一行是标题行
						titleRecords.add(thisStr);
					}
				}
				// Update column
				if (thisColumn > -1) {
					lastColumnNumber = thisColumn;
				}

			} else if (rowStr.equals(name)) {
				if (curRow < 0) {
					curRow = 0;
				}
				curRow++;

				// Print out any missing commas if needed
				if (minColumns != minCol) {
					// Columns are 0 based
					if (lastColumnNumber == -1) {
						lastColumnNumber = 0;
					}

					if (record != null && record.length > 0) {
						// 不分页
						boolean isEmptyRow = true;
						for (String cv : record) {
							if (!BinaryUtils.isEmpty(cv)) {
								isEmptyRow = false;
								break;
							}
						}

						if (!isEmptyRow) {
							rows.add(record.clone());
						}

						for (int i = 0; i < record.length; i++) {
							record[i] = null;
						}
					} else if (curRow == 1 && titleRecords.size() > 0) {
						// 没有指定读取列书时
						String[] strs = new String[titleRecords.size()];
						boolean isEmptyRow = true;
						for (int i = 0; i < strs.length; i++) {
							if (BinaryUtils.isEmpty(titleRecords.get(i))) {
								continue;
							}
							strs[i] = titleRecords.get(i);
							isEmptyRow = false;
						}
						if (!isEmptyRow) {
							rows.add(strs);
						}

						// 第一行是标题行,取标题行的内容长度,重新创建数组
						int length = titleRecords.size();
						record = new String[length];
						titleRecords.clear();
					}

				} else if (minColumns == minCol) {
					// 不管何时第一行标题行都要加入到结果集中
					if (curRow == 1 && titleRecords.size() > 0) {
						String[] strs = new String[titleRecords.size()];
						boolean isEmptyRow = true;
						for (int i = 0; i < strs.length; i++) {
							if (BinaryUtils.isEmpty(titleRecords.get(i))) {
								continue;
							}
							strs[i] = titleRecords.get(i);
							isEmptyRow = false;
						}
						if (!isEmptyRow) {
							rows.add(strs);
						}

						// 第一行是标题行,取标题行的内容长度,重新创建数组
						int length = titleRecords.size();
						pageRecords = new String[length];
						titleRecords.clear();

					} else if (curRow >= startRow && curRow <= endRow) {
						// 分页
						if (pageRecords != null && pageRecords.length > 0) {
							boolean isEmptyRow = true;
							for (String cv : pageRecords) {
								if (!BinaryUtils.isEmpty(cv)) {
									isEmptyRow = false;
									break;
								}
							}

							if (!isEmptyRow) {
								rows.add(pageRecords.clone());
							}

							for (int i = 0; i < pageRecords.length; i++) {
								pageRecords[i] = null;
							}
						}
					}
				}

				lastColumnNumber = -1;
			}
		}

		public List<String[]> getRows() {
			// 分页时将总行数放在每一页结果集合最后一项
			int minCol = -2;
			if (minColumns == minCol && this.curRow > 0) {
				rows.add(new String[] { String.valueOf(this.curRow - 1) });
			}
			return rows;
		}

		public void setRows(List<String[]> rows) {
			this.rows = rows;
		}

		/**
		 * Captures characters only if a suitable element is open. Originally was just
		 * "v"; extended for inlineStr also.
		 */
		public void characters(char[] ch, int start, int length) throws SAXException {
			if (vIsOpen) {
				value.append(ch, start, length);
			}
		}

		/**
		 * Converts an Excel column name like "C" to a zero-based index.
		 * 
		 * @param name
		 * @return Index corresponding to the specified name
		 */
		private int nameToColumn(String name) {
			int column = -1;
			for (int i = 0; i < name.length(); ++i) {
				int c = name.charAt(i);
				column = (column + 1) * 26 + c - 'A';
			}
			return column;
		}

		private String formateDateToString(Date date) {
			// 格式化日期
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			return sdf.format(date);
		}

	}

	// /////////////////////////////////////

	private OPCPackage xlsxPackage;
	/**
	 * 最小读取列数 -2:用于分页读取
	 */
	private int minColumns;
	private PrintStream output;
	private String sheetName;

	private int pageSize;
	private int pageNum;

	/**
	 * Creates a new XLSX -> CSV converter
	 * 
	 * @param pkg        The XLSX package to process
	 * @param output     The PrintStream to output the CSV to
	 * @param minColumns The minimum number of columns to output, or -1 for no
	 *                   minimum
	 */
	public XLSXCovertCSVReader(OPCPackage pkg, PrintStream output, String sheetName, int minColumns) {
		this.xlsxPackage = pkg;
		this.output = output;
		this.minColumns = minColumns;
		this.sheetName = sheetName;
	}

	public XLSXCovertCSVReader(OPCPackage pkg, PrintStream output, String sheetName, int minColumns, int pageNum,
			int pageSize) {
		this.xlsxPackage = pkg;
		this.output = output;
		this.minColumns = minColumns;
		this.sheetName = sheetName;

		this.pageSize = pageSize;
		this.pageNum = pageNum;
	}

	/**
	 * Parses and shows the content of one sheet using the specified styles and
	 * shared-strings tables.
	 * 
	 * @param styles
	 * @param strings
	 * @param sheetInputStream
	 */
	public List<String[]> processSheet(StylesTable styles, ReadOnlySharedStringsTable strings,
			InputStream sheetInputStream) throws IOException, ParserConfigurationException, SAXException {

		InputSource sheetSource = new InputSource(sheetInputStream);
		SAXParserFactory saxFactory = SAXParserFactory.newInstance();
		SAXParser saxParser = saxFactory.newSAXParser();
		XMLReader sheetParser = saxParser.getXMLReader();
		MyXSSFSheetHandler handler = new MyXSSFSheetHandler(styles, strings, this.minColumns, this.output);
		sheetParser.setContentHandler(handler);
		sheetParser.parse(sheetSource);
		return handler.getRows();
	}

	/**
	 * Parses and shows the content of one sheet using the specified styles and
	 * shared-strings tables.
	 * 
	 * @param styles
	 * @param strings
	 * @param sheetInputStream
	 */
	public List<String[]> pageProcessSheet(StylesTable styles, ReadOnlySharedStringsTable strings,
			InputStream sheetInputStream) throws IOException, ParserConfigurationException, SAXException {

		InputSource sheetSource = new InputSource(sheetInputStream);
		SAXParserFactory saxFactory = SAXParserFactory.newInstance();
		SAXParser saxParser = saxFactory.newSAXParser();
		XMLReader sheetParser = saxParser.getXMLReader();
		MyXSSFSheetHandler handler = new MyXSSFSheetHandler(styles, strings, this.minColumns, this.output, this.pageNum,
				this.pageSize);
		sheetParser.setContentHandler(handler);
		// 2018/11/14 0014 耗时
		sheetParser.parse(sheetSource);
		return handler.getRows();
	}

	/**
	 * Parses and shows the content of one sheet using the specified styles and
	 * shared-strings tables.
	 *
	 * @param styles
	 * @param strings
	 * @param sheetInputStream
	 */
	public List<String[]> pageProcessSheetWhithoutSheetSource(StylesTable styles, ReadOnlySharedStringsTable strings,
			InputStream sheetInputStream) throws IOException, ParserConfigurationException, SAXException {

		SAXParserFactory saxFactory = SAXParserFactory.newInstance();
		SAXParser saxParser = saxFactory.newSAXParser();
		XMLReader sheetParser = saxParser.getXMLReader();
		MyXSSFSheetHandler handler = new MyXSSFSheetHandler(styles, strings, this.minColumns, this.output, this.pageNum,
				this.pageSize);
		sheetParser.setContentHandler(handler);
		return handler.getRows();
	}

	/**
	 * 初始化这个处理程序 将
	 * 
	 * @throws IOException
	 * @throws OpenXML4JException
	 * @throws ParserConfigurationException
	 * @throws SAXException
	 */
	public List<String[]> process() throws IOException, OpenXML4JException, ParserConfigurationException, SAXException {
		ReadOnlySharedStringsTable strings = new ReadOnlySharedStringsTable(this.xlsxPackage);
		XSSFReader xssfReader = new XSSFReader(this.xlsxPackage);
		List<String[]> list = null;
		StylesTable styles = xssfReader.getStylesTable();
		XSSFReader.SheetIterator iter = (XSSFReader.SheetIterator) xssfReader.getSheetsData();
		while (iter.hasNext()) {
			InputStream stream = iter.next();
			String sheetNameTemp = iter.getSheetName().trim();
			if (this.sheetName.equals(sheetNameTemp)) {
				list = processSheet(styles, strings, stream);
				stream.close();

			}
		}

		return list;
	}

	/**
	 * 初始化这个处理程序 将
	 *
	 * @throws IOException
	 * @throws OpenXML4JException
	 * @throws ParserConfigurationException
	 * @throws SAXException
	 */
	public List<String[]> pageProcess()
			throws IOException, OpenXML4JException, ParserConfigurationException, SAXException {
		ReadOnlySharedStringsTable strings = new ReadOnlySharedStringsTable(this.xlsxPackage);
		XSSFReader xssfReader = new XSSFReader(this.xlsxPackage);
		List<String[]> list = null;
		StylesTable styles = xssfReader.getStylesTable();
		XSSFReader.SheetIterator iter = (XSSFReader.SheetIterator) xssfReader.getSheetsData();
		while (iter.hasNext()) {
			InputStream stream = iter.next();
			String sheetNameTemp = iter.getSheetName().trim();
			if (this.sheetName.equals(sheetNameTemp)) {
				List<String[]> ls = pageProcessSheet(styles, strings, stream);

				if (list == null) {
					list = new ArrayList<String[]>();
				}
				// 分页时集合第一项是标题行占用,所有集合大小必须大于1才算读取到有正文内容
				if (ls != null && ls.size() > 1) {
					list.addAll(ls);
				}
				stream.close();
				break;
			}
		}

		return list;
	}

	/**
	 * 读取Excel中所有的sheet页名字
	 * 
	 * @return Excel文件中所有的Sheet页名字
	 * @throws IOException
	 * @throws OpenXML4JException
	 * @throws ParserConfigurationException
	 * @throws SAXException
	 */
	public List<String> getSheetNamesProcess()
			throws IOException, OpenXML4JException, ParserConfigurationException, SAXException {
		XSSFReader xssfReader = new XSSFReader(this.xlsxPackage);
		XSSFReader.SheetIterator iter = (XSSFReader.SheetIterator) xssfReader.getSheetsData();
		List<String> list = new ArrayList<String>();
		while (iter.hasNext()) {
			InputStream stream = iter.next();
			String sheetNameTemp = iter.getSheetName();
			list.add(sheetNameTemp.trim());
			stream.close();
		}
		return list;
	}

	/**
	 * 读取一个Excel中所有的sheet页名字和标题行内容
	 * 
	 * @return Excel文件中所有的Sheet页名字和标题行内容
	 * @throws IOException
	 * @throws OpenXML4JException
	 * @throws ParserConfigurationException
	 * @throws SAXException
	 */
	public Map<String, List<String>> getSheetTitlesProcess()
			throws IOException, OpenXML4JException, ParserConfigurationException, SAXException {
		ReadOnlySharedStringsTable strings = new ReadOnlySharedStringsTable(this.xlsxPackage);
		XSSFReader xssfReader = new XSSFReader(this.xlsxPackage);
		StylesTable styles = xssfReader.getStylesTable();
		XSSFReader.SheetIterator iter = (XSSFReader.SheetIterator) xssfReader.getSheetsData();

		Map<String, List<String>> map = new HashMap<String, List<String>>();

		while (iter.hasNext()) {
			InputStream stream = iter.next();
			String sheetNameTemp = iter.getSheetName().trim();

			List<String[]> ls = pageProcessSheet(styles, strings, stream);
			List<String> titles = getTitlesBySheetDatas(ls);
			map.put(sheetNameTemp, titles);

			stream.close();
		}

		return map;
	}

	/**
	 * 读取Excel中所有的sheet页名字和标题行内容并保存到指定的目录
	 * 
	 * @return Excel文件中所有的Sheet页名字和标题行内容
	 * @throws IOException
	 * @throws OpenXML4JException
	 * @throws ParserConfigurationException
	 * @throws SAXException
	 */
	public List<Map<String, List<String>>> getSheetTitlesProcess(File fileDir)
			throws IOException, OpenXML4JException, ParserConfigurationException, SAXException {
		ReadOnlySharedStringsTable strings = new ReadOnlySharedStringsTable(this.xlsxPackage);
		XSSFReader xssfReader = new XSSFReader(this.xlsxPackage);
		StylesTable styles = xssfReader.getStylesTable();
		XSSFReader.SheetIterator iter = (XSSFReader.SheetIterator) xssfReader.getSheetsData();

		List<Map<String, List<String>>> list = new ArrayList<Map<String, List<String>>>();

		while (iter.hasNext()) {
			InputStream stream = iter.next();
			String sheetNameTemp = iter.getSheetName().trim();

			List<String[]> ls = pageProcessSheet(styles, strings, stream);
			if (ls != null && ls.size() > 1) {
				if (ls.size() > 82) {
					saveFileTemp(fileDir, sheetNameTemp, ls.subList(0, 81));
				} else {
					saveFileTemp(fileDir, sheetNameTemp, ls);
				}
				List<String> titles = getTitlesBySheetDatas(ls);

				Map<String, List<String>> map = new HashMap<String, List<String>>();
				map.put(sheetNameTemp, titles);
				list.add(map);
			}

			stream.close();
		}

		return list;
	}

	/**
	 * 保存生成的json到临时目录，用于快速预览
	 * 
	 * @param parent      父目录
	 * @param sheetName
	 * @param sheetTitles
	 * @param pageData
	 */
	public static void saveFileTemp(File parent, String sheetName, List<String> sheetTitles,
			Page<Map<Integer, String>> pageData) {
		Map<String, Object> jsonText = new HashMap<String, Object>();
		jsonText.put("code", -1);
		jsonText.put("attrCount", sheetTitles.size());

		Map<String, Object> data = new HashMap<String, Object>();
		data.put("curSheetName", sheetName);
		data.put("fieldNames", sheetTitles);
		data.put("datas", pageData.getData());
		data.put("totalRows", pageData.getTotalRows());
		data.put("pageNum", pageData.getPageNum());
		data.put("pageSize", pageData.getPageSize());
		data.put("fileName", sheetName);
		jsonText.put("data", data);
		jsonText.put("success", true);
		String jsonData = JSON.toString(jsonText);

		try {

			File file = CommUtil.newFile(parent, sheetName);

			// if file doesnt exists, then create it
			if (!file.exists()) {
				file.createNewFile();
			}

			PrintWriter out = new PrintWriter(
					new BufferedWriter(new OutputStreamWriter(new FileOutputStream(file), "utf-8")));
			out.write(jsonData);
			out.close();

		} catch (IOException e) {
			e.printStackTrace();
		}

	}

	/**
	 * 保存生成的json到临时目录，用于快速预览
	 * 
	 * @param parent    父目录
	 * @param sheetName
	 * @param pageList
	 */
	public static void saveFileTemp(File parent, String sheetName, List<String[]> pageList) {

		int totalRows = 0;
		// 获取总行数
		String[] remove = pageList.remove(pageList.size() - 1);
		if (remove != null && remove.length == 1) {
			totalRows = Conver.to(remove[0], int.class);
		}
		// 获取标题内容
		List<String> sheetTitles = ExcelCovertCSVReaderUtil.getTitlesBySheetDatas(pageList);
		// 获取正文内容
		List<Map<Integer, String>> datas = XLSXCovertCSVReader.arrayConvertIdxMapsBySheetDatas(pageList);

		Map<String, Object> jsonText = new HashMap<String, Object>();
		jsonText.put("code", -1);
		jsonText.put("attrCount", sheetTitles.size());

		Map<String, Object> data = new HashMap<String, Object>();
		data.put("curSheetName", sheetName);
		data.put("fieldNames", sheetTitles);
		data.put("datas", datas);
		data.put("totalRows", totalRows);
		data.put("pageNum", 1);
		data.put("pageSize", pageList.size());
		data.put("fileName", sheetName);

		jsonText.put("data", data);
		jsonText.put("success", true);
		String jsonData = JSON.toString(jsonText);

		try {

			File file = new File(parent, sheetName);

			// if file doesnt exists, then create it
			if (!file.exists()) {
				file.createNewFile();
			}

			PrintWriter out = new PrintWriter(
					new BufferedWriter(new OutputStreamWriter(new FileOutputStream(file), "utf-8")));
			out.write(jsonData);
			out.close();

		} catch (IOException e) {
			e.printStackTrace();
		}

	}

	/**
	 * 读取Excel且返回结果是String[]数组(行数据)集合
	 * 
	 * @param path       文件路径
	 * @param sheetName  sheet名称
	 * @param minColumns 列总数：0或者小于0都读全部有值的列
	 * @return List<String[]> 格式行数据
	 * @throws SAXException
	 * @throws ParserConfigurationException
	 * @throws OpenXML4JException
	 * @throws IOException
	 */
	public static List<String[]> readerExcel(String path, String sheetName, int minColumns)
			throws IOException, OpenXML4JException, ParserConfigurationException, SAXException {
		OPCPackage p = OPCPackage.open(path, PackageAccess.READ);
		XLSXCovertCSVReader xlsx2csv = new XLSXCovertCSVReader(p, System.out, sheetName, minColumns);
		List<String[]> list = xlsx2csv.process();
		p.close();
		return list;
	}

	/**
	 * 读取Excel指定Sheet页内容且返回结果是String[]数组(行数据)集合
	 * 
	 * @param file       文件流
	 * @param sheetName  sheet名称
	 * @param minColumns 列总数：0或者小于0都读全部有值的列
	 * @return String[]格式的行数据集合
	 * @throws SAXException
	 * @throws ParserConfigurationException
	 * @throws OpenXML4JException
	 * @throws IOException
	 */
	public static List<String[]> readerExcelByFile(File file, String sheetName, int minColumns)
			throws IOException, OpenXML4JException, ParserConfigurationException, SAXException {
		OPCPackage p = OPCPackage.open(file, PackageAccess.READ);
		XLSXCovertCSVReader xlsx2csv = new XLSXCovertCSVReader(p, System.out, sheetName, minColumns);
		List<String[]> list = xlsx2csv.process();
		p.close();
		return list;
	}

	/**
	 * 读取Excel指定Sheet页内容且返回结果是String[]数组(行数据)集合
	 * 
	 * @param is         文件流 读取完成后会自动关闭流
	 * @param sheetName  sheet名称
	 * @param minColumns 列总数：0或者小于0都读全部有值的列
	 * @return String[]格式的行数据集合
	 * @throws SAXException
	 * @throws ParserConfigurationException
	 * @throws OpenXML4JException
	 * @throws IOException
	 */
	public static List<String[]> readerExcelByIs(InputStream is, String sheetName, int minColumns)
			throws IOException, OpenXML4JException, ParserConfigurationException, SAXException {
		OPCPackage p = OPCPackage.open(is);
		XLSXCovertCSVReader xlsx2csv = new XLSXCovertCSVReader(p, System.out, sheetName, minColumns);
		List<String[]> list = xlsx2csv.process();
		p.close();
		return list;
	}

	/**
	 * 分页读取Excel指定Sheet页内容且返回结果是String[]数组(行数据)集合[每一页的最后一项均是总页数,不是正文]
	 * 
	 * @param pageNum   页码
	 * @param pageSize  每页数量 文件流
	 * @param sheetName sheet名称
	 * 
	 * @return String[]格式的行数据集合
	 * @throws SAXException
	 * @throws ParserConfigurationException
	 * @throws OpenXML4JException
	 * @throws IOException
	 */
	public static List<String[]> readerExcelPageByFile(int pageNum, int pageSize, File file, String sheetName)
			throws IOException, OpenXML4JException, ParserConfigurationException, SAXException {

		int minColumns = -2;
		OPCPackage p = OPCPackage.open(file, PackageAccess.READ);
		XLSXCovertCSVReader xlsx2csv = new XLSXCovertCSVReader(p, System.out, sheetName, minColumns, pageNum, pageSize);
		List<String[]> list = xlsx2csv.pageProcess();
		p.close();
		return list;
	}

	/**
	 * 分页读取Excel指定Sheet页内容且返回结果是String[]数组(行数据)集合[每一页的最后一项均是总页数,不是正文]
	 * 
	 * @param pageNum   页码
	 * @param pageSize  每页数量
	 * @param is        读取完成后会自动关闭流
	 * @param sheetName sheet名称
	 * 
	 * @return String[]格式的行数据集合
	 * @throws SAXException
	 * @throws ParserConfigurationException
	 * @throws OpenXML4JException
	 * @throws IOException
	 */
	public static List<String[]> readerExcelPageByIs(int pageNum, int pageSize, InputStream is, String sheetName)
			throws IOException, OpenXML4JException, ParserConfigurationException, SAXException {

		int minColumns = -2;
		OPCPackage p = OPCPackage.open(is);
		XLSXCovertCSVReader xlsx2csv = new XLSXCovertCSVReader(p, System.out, sheetName, minColumns, pageNum, pageSize);
		List<String[]> list = xlsx2csv.pageProcess();
		p.close();
		return list;
	}

	/**
	 * 读取Excel中所有的sheet页名字
	 * 
	 * @param file
	 * @return Excel文件中所有的Sheet页名字
	 * @throws IOException
	 * @throws OpenXML4JException
	 * @throws ParserConfigurationException
	 * @throws SAXException
	 */
	public static List<String> readerSheetNamesByFile(File file)
			throws IOException, OpenXML4JException, ParserConfigurationException, SAXException {
		OPCPackage p = OPCPackage.open(file, PackageAccess.READ);
		XLSXCovertCSVReader xlsx2csv = new XLSXCovertCSVReader(p, System.out, null, 0);
		List<String> list = xlsx2csv.getSheetNamesProcess();
		p.close();
		return list;
	}

	/**
	 * 读取Excel中所有的sheet页名字
	 * 
	 * @param is 读取完成后会自动关闭流
	 * @return Excel文件中所有的Sheet页名字
	 * @throws IOException
	 * @throws OpenXML4JException
	 * @throws ParserConfigurationException
	 * @throws SAXException
	 */
	public static List<String> readerSheetNamesByIs(InputStream is)
			throws IOException, OpenXML4JException, ParserConfigurationException, SAXException {
		OPCPackage p = OPCPackage.open(is);
		XLSXCovertCSVReader xlsx2csv = new XLSXCovertCSVReader(p, System.out, null, 0);
		List<String> list = xlsx2csv.getSheetNamesProcess();
		p.close();
		return list;
	}

	/**
	 * 读取一个Excel中所有的Sheet页名字和每个Sheet页的标题行内容
	 * 
	 * @param file
	 * @return Excel文件中所有的Sheet页名字和每个Sheet页的标题行内容
	 * @throws IOException
	 * @throws OpenXML4JException
	 * @throws ParserConfigurationException
	 * @throws SAXException
	 */
	public static Map<String, List<String>> readerSheetTitlesByFile(File file)
			throws IOException, OpenXML4JException, ParserConfigurationException, SAXException {
		OPCPackage p = OPCPackage.open(file, PackageAccess.READ);
		XLSXCovertCSVReader xlsx2csv = new XLSXCovertCSVReader(p, System.out, null, -2, 1, 10);
		// 耗时
		Map<String, List<String>> map = xlsx2csv.getSheetTitlesProcess();
		p.close();
		return map;
	}

	/**
	 * 读取Excel中所有的Sheet页名字和每个Sheet页的标题行内容并保存到指定位置
	 * 
	 * @param file
	 * @return Excel文件中所有的Sheet页名字和每个Sheet页的标题行内容
	 * @throws IOException
	 * @throws OpenXML4JException
	 * @throws ParserConfigurationException
	 * @throws SAXException
	 */
	public static List<Map<String, List<String>>> readerSheetTitlesByFile(File file, File fileDir)
			throws IOException, OpenXML4JException, ParserConfigurationException, SAXException {
		OPCPackage p = OPCPackage.open(file, PackageAccess.READ);
		XLSXCovertCSVReader xlsx2csv = new XLSXCovertCSVReader(p, System.out, null, -2, 1, 80);
		// 这一行耗时
		List<Map<String, List<String>>> list = xlsx2csv.getSheetTitlesProcess(fileDir);
		p.close();
		return list;
	}

	/**
	 * 将读取到的List<String[]>格式的行数据第一行 结果转为List<String>
	 * 
	 * @return
	 */
	public static List<String> getTitlesBySheetDatas(List<String[]> datas) {
		if (datas == null || datas.size() == 0) {
			return null;
		}
		String[] titles = datas.get(0);
		List<String> sheetTitles = new ArrayList<String>();
		if (titles != null && titles.length > 0) {
			// 第一行列头不计入正文读取,但要保持索引和值至MAP
			for (int j = 0; j < titles.length; j++) {
				String t = titles[j];
				if (BinaryUtils.isEmpty(t)) {
					t = "";
				} else {
					t = t.trim();
				}
				sheetTitles.add(t);
			}
		}
		return sheetTitles;
	}

	/**
	 * 将读取到的List<String[]>格式的行数据 结果转为List<Map<String, String>>
	 * 
	 * @param datas    String[]格式的行数据集合
	 * @param isStdKey 是否需要将结果中Map的Key值(列头值)标准化(转成大写)[true Or false]
	 * @return List<Map<String, String>>: Map中Key为列头值;Value列头对应的行坐标的正文列值
	 */
	public static List<Map<String, String>> arrayConvertMapsBySheetDatas(List<String[]> datas, boolean isStdKey) {
		if (datas == null || datas.size() == 0) {
			return null;
		}
		List<Map<String, String>> maps = new ArrayList<Map<String, String>>();

		// 遍历行值
		// 记录标题行类容: key:列索引; Value:列值
		Map<Integer, String> titleIdxStdValMap = new HashMap<Integer, String>();
		for (int i = 0; i < datas.size(); i++) {
			String[] rowDatas = datas.get(i);

			if (i == 0) {
				// 列头行值为空直接终止
				if (rowDatas == null || rowDatas.length == 0) {
					break;
				}

				// 第一行列头不计入正文读取,但要保持索引和值至MAP
				for (int j = 0; j < rowDatas.length; j++) {
					if (BinaryUtils.isEmpty(rowDatas[j])) {
						continue;
					}
					String titleCellVal = rowDatas[j];
					// 是否需要标准化
					if (isStdKey) {
						titleCellVal = titleCellVal.toUpperCase();
					}
					if (!BinaryUtils.isEmpty(titleCellVal)) {
						titleIdxStdValMap.put(j, titleCellVal.trim());
					}
				}
			}

			if (i > 0) {
				if (rowDatas == null || rowDatas.length == 0) {
					continue;
				}
				// 遍历每行取出列值并存入Map： key:属性标准名; Value:对应的列值
				Map<String, String> commMap = new HashMap<String, String>();
				boolean isEmptyRow = true;
				for (int j = 0; j < rowDatas.length; j++) {
					String val = "";
					if (!BinaryUtils.isEmpty(rowDatas[j])) {
						val = rowDatas[j].trim();
						isEmptyRow = false;
					}
					// 存入map:key:列头值; Value:对应的正文列值
					if (!BinaryUtils.isEmpty(titleIdxStdValMap.get(j))) {
						commMap.put(titleIdxStdValMap.get(j), val);
					}
				}

				if (!commMap.isEmpty() && !isEmptyRow) {
					maps.add(commMap);
				}
			}
		}

		return maps;
	}

	/**
	 * 将读取到的List<String[]>格式的行数据 结果转为List<Map<String, String>>
	 *
	 * @param datas    String[]格式的行数据集合
	 * @param isStdKey 是否需要将结果中Map的Key值(列头值)标准化(转成大写)[true Or false]
	 * @return List<Map<String, String>>: Map中Key为列头值;Value列头对应的行坐标的正文列值
	 */
	public static List<CiRltAttrMapBean> arrayConvertBeansBySheetDatas(List<String[]> datas, boolean isStdKey) {
		if (datas == null || datas.size() == 0) {
			return null;
		}
		List<CiRltAttrMapBean> maps = new ArrayList<CiRltAttrMapBean>();

		// 遍历行值
		// 记录标题行类容: key:列索引; Value:列值
		Map<Integer, String> titleIdxStdValMap = new HashMap<Integer, String>();

		Integer sourceCiCodeIndex = null;
		Integer targetCiCodeIndex = null;
		for (int i = 0; i < datas.size(); i++) {
			String[] rowDatas = datas.get(i);

			if (i == 0) {
				// 列头行值为空直接终止
				if (rowDatas == null || rowDatas.length == 0) {
					break;
				}

				// 第一行列头不计入正文读取,但要保持索引和值至MAP
				for (int j = 0; j < rowDatas.length; j++) {
					if (BinaryUtils.isEmpty(rowDatas[j])) {
						continue;
					}
					String titleCellVal = rowDatas[j];
					if (sourceCiCodeIndex == null && (titleCellVal.equals("Source ciCode") || titleCellVal.equals("源ciCode")
													|| titleCellVal.equals("源对象") || titleCellVal.equals("Source object"))) {
						sourceCiCodeIndex = j;
					}
					if (targetCiCodeIndex == null && (titleCellVal.equals("Target ciCode") || titleCellVal.equals("目标ciCode")
													|| titleCellVal.equals("目标对象") || titleCellVal.equals("Target object"))) {
						targetCiCodeIndex = j;
					}
					// 是否需要标准化
					if (isStdKey) {
						titleCellVal = titleCellVal.toUpperCase();
					}
					if (!BinaryUtils.isEmpty(titleCellVal)) {
						titleIdxStdValMap.put(j, titleCellVal.trim());
					}
				}
			}

			if (i > 0) {
				if (rowDatas == null || rowDatas.length == 0) {
					continue;
				}
				// 遍历每行取出列值并存入Map： key:属性标准名; Value:对应的列值
				CiRltAttrMapBean bean = new CiRltAttrMapBean();
//				Map<String, String> commMap = new HashMap<String, String>();
				boolean isEmptyRow = true;
				for (int j = 0; j < rowDatas.length; j++) {
					String val = "";
					if (!BinaryUtils.isEmpty(rowDatas[j])) {
						val = rowDatas[j].trim();
						isEmptyRow = false;
					}
					// 存入map:key:列头值; Value:对应的正文列值
					if (!BinaryUtils.isEmpty(titleIdxStdValMap.get(j))) {
						if (sourceCiCodeIndex != null && j == sourceCiCodeIndex) {
							bean.setSourceCiCode(val);
						} else if (targetCiCodeIndex != null && j == targetCiCodeIndex) {
							bean.setTargetCiCode(val);
						} else {
							bean.getAttrs().put(titleIdxStdValMap.get(j), val);
						}
//						commMap.put(titleIdxStdValMap.get(j), val);
					}
				}

				if (!isEmptyRow) {
					maps.add(bean);
				}
			}
		}

		return maps;
	}

	/**
	 * 将读取到的List<String[]>格式的行数据(不含标题行) 结果转为List<Map<Integer, String>>
	 * 
	 * @param datas
	 * @return 所有正文内容MAP集合中,key为索引
	 */
	public static List<Map<Integer, String>> arrayConvertIdxMapsBySheetDatas(List<String[]> datas) {
		List<Map<Integer, String>> maps = new ArrayList<Map<Integer, String>>();
		int dataStdSize = 2;
		if (datas == null || datas.size() < dataStdSize) {
			return maps;
		}

		// 遍历行值
		int titleSize = datas.get(0).length;
		for (int i = 1; i < datas.size(); i++) {
			String[] rowDatas = datas.get(i);
			if (rowDatas == null || rowDatas.length == 0) {
				continue;
			}
			int len = rowDatas.length;

			Map<Integer, String> cellIdxMap = new HashMap<Integer, String>();
			boolean isEmptyRow = true;
			for (int j = 0; j < titleSize; j++) {
				String v = "";
				if (j < len && !BinaryUtils.isEmpty(rowDatas[j])) {
					v = rowDatas[j].trim();
					isEmptyRow = false;
				}
				cellIdxMap.put(j, v);
			}
			if (!cellIdxMap.isEmpty() && !isEmptyRow) {
				maps.add(cellIdxMap);
			}
		}

		return maps;
	}

	/**
	 * 读取Excel指定Sheet页内容且返回结果是Map集合
	 * 
	 * @param file
	 * @param sheetName  sheet名称
	 * @param minColumns 列总数：0或者小于0都读全部有值的列
	 * @param isStdKey   是否需要将结果中Map的Key值(列头值)标准化(转成大写)[true Or false]
	 * @return List<Map<String, String>>: Map中Key为列头值;Value列头对应的行坐标的正文列值
	 * @throws SAXException
	 * @throws ParserConfigurationException
	 * @throws OpenXML4JException
	 * @throws IOException
	 */
	public static List<Map<String, String>> readerExcelToMaps(File file, String sheetName, int minColumns,
			boolean isStdKey) throws IOException, OpenXML4JException, ParserConfigurationException, SAXException {
		List<String[]> list = readerExcelByFile(file, sheetName, minColumns);
		return arrayConvertMapsBySheetDatas(list, isStdKey);
	}


	public static class CiRltAttrMapBean {
		String sourceCiCode = null;
		String targetCiCode = null;
		Map<String, String> attrs = new HashMap<String, String>();

		public String getSourceCiCode() {
			return sourceCiCode;
		}

		public void setSourceCiCode(String sourceCiCode) {
			this.sourceCiCode = sourceCiCode;
		}

		public String getTargetCiCode() {
			return targetCiCode;
		}

		public void setTargetCiCode(String targetCiCode) {
			this.targetCiCode = targetCiCode;
		}

		public Map<String, String> getAttrs() {
			return attrs;
		}

		public void setAttrs(Map<String, String> attrs) {
			this.attrs = attrs;
		}
	}

	/**
	 * 读取Excel指定Sheet页内容且返回结果是Map集合
	 * 
	 * @param is         读取完成后会自动关闭流
	 * @param sheetName  sheet名称
	 * @param minColumns 列总数：0或者小于0都读全部有值的列
	 * @param isStdKey   是否需要将结果中Map的Key值(列头值)标准化(转成大写)[true Or false]
	 * @return List<Map<String, String>>: Map中Key为列头值;Value列头对应的行坐标的正文列值
	 * @throws SAXException
	 * @throws ParserConfigurationException
	 * @throws OpenXML4JException
	 * @throws IOException
	 */
	public static List<Map<String, String>> readerExcelToMapsByIs(InputStream is, String sheetName, int minColumns,
			boolean isStdKey) throws IOException, OpenXML4JException, ParserConfigurationException, SAXException {
		List<String[]> list = readerExcelByIs(is, sheetName, minColumns);
		return arrayConvertMapsBySheetDatas(list, isStdKey);
	}

	public static List<CiRltAttrMapBean> readerExcelToBeansByIs(InputStream is, String sheetName, int minColumns,
																  boolean isStdKey) throws IOException, OpenXML4JException, ParserConfigurationException, SAXException {
		List<String[]> list = readerExcelByIs(is, sheetName, minColumns);
		return arrayConvertBeansBySheetDatas(list, isStdKey);
	}

	/**
	 * 分页读取Excel指定Sheet页内容且返回结果是Map集合[每一页的最后一项均是总页数(key:totalRows),不是正文]
	 * 
	 * @param pageNum   页码
	 * @param pageSize  每页数量
	 * @param file
	 * @param sheetName sheet名称
	 * @param isStdKey  是否需要将结果中Map的Key值(列头值)标准化(转成大写)[true:需要 Or false:不需要]
	 * 
	 * @return List<Map<String, String>>: Map中Key为列头值;Value列头对应的行坐标的正文列值
	 * @throws SAXException
	 * @throws ParserConfigurationException
	 * @throws OpenXML4JException
	 * @throws IOException
	 */
	public static List<Map<String, String>> readerExcelPageToMaps(int pageNum, int pageSize, File file,
			String sheetName, boolean isStdKey)
			throws IOException, OpenXML4JException, ParserConfigurationException, SAXException {

		List<String[]> list = readerExcelPageByFile(pageNum, pageSize, file, sheetName);

		Map<String, String> endMap = new HashMap<String, String>();
		if (list != null && list.size() > 0) {
			int lastIndx = list.size() - 1;
			String[] remove = list.remove(lastIndx);

			int totalRows = 0;
			if (remove != null && remove.length == 1) {
				totalRows = Conver.to(remove[0], int.class);
			}
			endMap.put("totalRows", String.valueOf(totalRows));
		}
		List<Map<String, String>> pageMaps = arrayConvertMapsBySheetDatas(list, isStdKey);
		if (!endMap.isEmpty()) {
			pageMaps.add(endMap);
		}
		return pageMaps;
	}

}