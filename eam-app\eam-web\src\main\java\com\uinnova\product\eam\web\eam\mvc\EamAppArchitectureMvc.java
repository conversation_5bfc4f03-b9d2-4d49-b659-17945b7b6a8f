package com.uinnova.product.eam.web.eam.mvc;

import com.binary.framework.util.ControllerUtils;
import com.binary.framework.web.RemoteResult;
import com.uinnova.product.eam.model.AppStrategyDto;
import com.uinnova.product.eam.service.IAppArchitectureSvc;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uino.bean.cmdb.base.LibType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;

/**
 * 应用架构相关服务的MVC
 *
 * <AUTHOR>
 * @version 2020/7/28
 */
@Controller
@RequestMapping("/eam/app")
public class EamAppArchitectureMvc {

    @Autowired
    IAppArchitectureSvc appArchitectureSvc;

    @ModDesc(desc = "获取应用策略信息", pDesc = "对象库", pType = String.class, rDesc = "应用策略信息", rType = AppStrategyDto.class)
    @RequestMapping("/getAppStrategy")
    public void getAppStrategy(@RequestParam(defaultValue = "DESIGN") LibType libType,
                            HttpServletRequest request, HttpServletResponse response) {
        List<AppStrategyDto> result = appArchitectureSvc.getAppStrategy(libType);
        ControllerUtils.returnJson(request, response, result);
    }

    /**
     * 业务能力/人员矩阵
     *
     * @param libType 仓库类型
     * @return 业务能力/人员矩阵的图数据
     */
    @GetMapping("/getStaffMatrixView")
    @ResponseBody
    public RemoteResult getStaffMatrixView(@RequestParam(defaultValue = "BASELINE") LibType libType) {
        HashMap<String, Object> resultMap = appArchitectureSvc.getStaffMatrixView(libType);
        return new RemoteResult(resultMap);
    }

    /**
     * 业务能力/策略矩阵
     *
     * @param libType 仓库类型
     * @return 业务能力/策略矩阵的图数据
     */
    @GetMapping("/getStrategyMatrixView")
    @ResponseBody
    public RemoteResult getStrategyMatrixView(@RequestParam(defaultValue = "BASELINE") LibType libType) {
        HashMap<String, Object> resultMap = appArchitectureSvc.getStrategyMatrixView(libType);
        return new RemoteResult(resultMap);
    }

    /**
     * 应用系统时间轴线图
     *
     * @param libType 仓库类型
     * @return 应用系统时间轴线图
     */
    @GetMapping("/getTimeAxisView")
    @ResponseBody
    public RemoteResult getTimeAxisView(@RequestParam(defaultValue = "BASELINE") LibType libType) {
        return new RemoteResult(appArchitectureSvc.getTimeAxisView(libType));
    }

    /**
     * 保存或更新应用系统信息,并创建对应架构设计文件夹
     * @param ciInfo
     * @return
     */
    @RequestMapping("/saveOrUpdateAppInfo")
    @ModDesc(desc = "保存或更新应用系统信息,并创建对应架构设计文件夹", pDesc = "对象数据", pType = CcCiInfo.class, rDesc = "1成功0失败", rType = Integer.class)
    public void saveOrUpdateAppInfo(HttpServletRequest request, HttpServletResponse response, @RequestBody CcCiInfo ciInfo) {
        Long id = appArchitectureSvc.saveOrUpdateAppInfo(ciInfo);
        ControllerUtils.returnJson(request, response, id);
    }

}
