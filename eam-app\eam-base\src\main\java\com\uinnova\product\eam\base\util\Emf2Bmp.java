package com.uinnova.product.eam.base.util;

import java.io.File;

import com.binary.core.util.BinaryUtils;
import com.binary.core.util.Command;
import com.binary.framework.exception.ServiceException;

public class Emf2Bmp {
	
	
	
	private String exePath;
	
	
	
	public Emf2Bmp(String exePath) {
		BinaryUtils.checkEmpty(exePath, "exePath");
		this.exePath = exePath;
		
		if(!(new File(this.exePath).isFile())) {
			throw new ServiceException(" not found emf2Bmp.exe '"+this.exePath+"'! ");
		}
	}
	
	
	
	
	public void conver(String emfPath, String bmpPath, boolean scaleImage) {
		BinaryUtils.checkEmpty(emfPath, "emfPath");
		BinaryUtils.checkEmpty(bmpPath, "bmpPath");
		
		if(!(new File(emfPath).isFile())) {
			throw new ServiceException(" not found emf file '"+emfPath+"'! ");
		}
		File targetDir = new File(bmpPath).getParentFile();
		if(!targetDir.isDirectory() && !targetDir.mkdirs()) {
			throw new ServiceException(" is not directory '"+targetDir.getPath()+"'! ");
		}
		Command.exec(new String[]{this.exePath, emfPath, bmpPath, String.valueOf(scaleImage?1:0)});
	}
	

}
