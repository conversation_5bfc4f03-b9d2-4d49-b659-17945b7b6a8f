package com.uinnova.product.eam.service.asset;

import com.uinnova.product.eam.comm.bean.CcCiClassInfoConfVO;
import com.uinnova.product.eam.comm.model.es.AppSquareConfig;
import com.uinnova.product.eam.comm.model.es.AssetDetailAttrConf;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface AssetDetailAttrConfSvc {
    /**
     * 新增或修改资产详情信息
     * @param target
     * @return
     */
    Long saveOrUpdateDetailAttr(AssetDetailAttrConf target);

    /**
     * 获取资产详情配置根据应用广场配置ID
     * @param appSquareConfId
     * @return
     */
    AssetDetailAttrConf getDetailAttr(Long appSquareConfId);

    /**
     * 根据ID删除详情配置
     * @param id
     * @return
     */
    Integer deleteDetailAttrById(Long id);

    /**
     * 根据appSquareConfId删除详情配置
     * @param appSquareConfId
     * @return
     */
    Integer deleteDetailAttrByAppSquareConfId(Long appSquareConfId);

    /**
     * 获取详情属性信息
     * @param appSquareConfId
     * @return
     */
    CcCiClassInfoConfVO getClassInfoDetailAttr(Long appSquareConfId);

    Map<String, AppSquareConfig> getAppSquareByClassCode(List<String> classCodes);

    /**
     * 根据分类标识获取
     * @param classCodes 分类标识
     * @return 配置map
     */
    Map<String, AssetDetailAttrConf> getAssetConfigMap(List<String> classCodes);
}
