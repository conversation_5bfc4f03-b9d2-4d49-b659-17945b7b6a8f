package com.uinnova.product.vmdb.comm.bean;

import java.util.HashSet;
import java.util.Set;

/**
 * 
 * <AUTHOR>
 *
 */
public class RefererWhitelistConfig {
    private boolean validateReferer;
    private Set<String> whitelistSet;

    public RefererWhitelistConfig() {
        whitelistSet = new HashSet<String>();
    }

    public boolean isValidateReferer() {
        return validateReferer;
    }

    public void setValidateReferer(boolean validateReferer) {
        this.validateReferer = validateReferer;
    }

    /**
     * 设置信任名单.多个用;隔开.如*************;localhost;*********;
     * 
     * @param string
     */
    public void setWhitelists(String string) {
        if (string == null || string.trim().length() == 0) {
            return;
        }
        string = string.trim();
        String[] split = string.split(";");
        for (String string2 : split) {
            String t = string2.trim();
            if (t.length() > 0) {
                whitelistSet.add(t);
            }
        }
    }

    public Set<String> getWhitelistSet() {
        return whitelistSet;
    }

    public void setWhitelistSet(Set<String> whitelistSet) {
        if (this.whitelistSet == null) {
            this.whitelistSet = whitelistSet;
        } else if (whitelistSet != null) {
            this.whitelistSet.addAll(whitelistSet);
        }
    }

    /**
     * 白名单是否包含指定字符串
     * 
     * @param str
     * @return
     */
    public boolean contains(String str) {
        return this.whitelistSet.contains(str);
    }

    /**
     * url中包含白名单内容
     * 
     * @param str
     * @return
     */
    public boolean whitelistInUrl(String str) {
        if (str == null || str.trim().length() == 0) {
            return false;
        }

        str = getDomain(str);

        for (String string : whitelistSet) {
            int idx = str.indexOf(string);
            // 前缀为http:// 或 https://
            if (idx >= 0) {
                return true;
            }
        }
        return false;
    }

    public String getDomain(String str) {
        if (str == null) {
            return "";
        }
        String https = "HTTPS";
        String http = "HTTP";
        if (str.toUpperCase().startsWith(https)) {
            str = str.substring(8);
        } else if (str.toUpperCase().startsWith(http)) {
            str = str.substring(7);
        } else {
            return "";
        }

        int i1 = str.indexOf(":");
        int i2 = str.indexOf("/");
        int i3 = str.indexOf("\\");
        i1 = i1 < 0 ? 1000000 : i1;

        i1 = i1 < i2 || i2 < 0 ? i1 : i2;
        i1 = i1 < i3 || i3 < 0 ? i1 : i3;

        return str.substring(0, i1);
    }
}
