package com.uinnova.product.eam.rpc.diagram;

import com.binary.jdbc.Page;
import com.binary.json.JSONArray;
import com.binary.json.JSONObject;
import com.uinnova.product.eam.api.diagram.ESDiagramApiClient;
import com.uinnova.product.eam.base.diagram.enums.DiagramCopyEnum;
import com.uinnova.product.eam.base.diagram.model.*;
import com.uinnova.product.eam.model.diagram.*;
import com.uinnova.product.eam.model.diagram.event.RuleParams;
import org.elasticsearch.index.query.QueryBuilder;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class ESDiagramSvcRpc implements ESDiagramApiClient {


    @Override
    public Map<String, String> saveESDiagram(ESDiagramInfoDTO esDiagramInfo) {
        return null;
    }

    @Override
    public List<ESDiagramDTO> queryDiagramInfoByIds(Long[] diagramIds, String type, Boolean needAuth, Boolean asset) {
        return null;
    }

    @Override
    public String deleteDiagramByIds(JSONObject jsonObject, JSONArray opArr) {
        return null;
    }

    @Override
    public String deleteDiagramById(Long diagramId) {
        return null;
    }

    @Override
    public Integer deleteDiagramByIds(Long[] diagramIds) {
        return null;
    }

    @Override
    public List<ESDiagramNode> selectNodeByDiagramIds(List<Long> diagramIds) {
        return null;
    }

    @Override
    public List<ESDiagram> selectDiagramsFromRecycle(List<Long> dirIds, String ownerCode, List<String> diagramIds) {
        return null;
    }

    @Override
    public List<ESDiagramLink> selectLinkByDiagramIds(List<Long> diagramIds) {
        return null;
    }

    @Override
    public List<ESDiagramNode> selectNodeByCiCodes(List<String> ciCodes, String ownerCode) {
        return null;
    }

    @Override
    public List<ESDiagramLink> selectLinkByRltCiCodes(List<String> uniqueCodes) {
        return null;
    }

    @Override
    public List<ESDiagramLink> selectLinkByRltCodes(Collection<String> uniqueCodes, String ownerCode) {
        return null;
    }

    @Override
    public List<ESDiagram> selectByIds(Collection<String> diagramIds, List<Integer> dirTypes, List<Integer> isOpens) {
        return null;
    }

    @Override
    public List<ESDiagram> queryByArtifactIds(List<Long> artifactIds) {
        return null;
    }

    @Override
    public List<ESDiagram> getByIds(Collection<Long> diagramIds, List<Integer> dirTypes, List<Integer> isOpens) {
        return null;
    }

    @Override
    public List<ESDiagram> selectMyOwnDiagramList(String ownerCode, List<Integer> dirTypes) {
        return null;
    }

    @Override
    public void replaceLinkList(List<ESDiagramLink> linkList) {

    }

    @Override
    public void replaceNodeList(List<ESDiagramNode> nodeList) {

    }

    @Override
    public int saveLinkList(List<ESDiagramLink> linkList) {
        return 0;
    }

    @Override
    public int saveNodeList(List<ESDiagramNode> nodeList) {
        return 0;
    }

    @Override
    public int delLinkList(List<Long> ids, List<String> keys) {
        return 0;
    }

    @Override
    public int delNodeList(List<Long> ids, List<String> keys) {
        return 0;
    }

    @Override
    public DiagramPushResponse diagramPush(DiagramPushRequest request) {
        return null;
    }

    @Override
    public DiagramPullResponse diagramPull(DiagramPullRequest request) {
        return null;
    }

    @Override
    public Page<VcDiagramInfo> queryDiagramInfoPage(Long domainId, Integer pageNum, Integer pageSize, CVcDiagram dCdt, String order) {
        return null;
    }

    @Override
    public List<Long> saveESDiagramBatch(List<ESDiagramInfoDTO> esDiagramInfoList, String newName, Long newDirId, String type) {
        return null;
    }

    @Override
    public Map<String, List<ESResponseStruct>> saveOrUpdateDiagramComponent(String jsonStr) {
        return null;
    }

    @Override
    public ESDiagramDTO queryESDiagramInfoById(Long diagramId, String type, Boolean versionFlag) {
        return null;
    }

    @Override
    public ESDiagramDTO updateFullDiagramNew(Long diagramId, ESDiagramInfoDTO esDiagramInfo) {
        return null;
    }

    @Override
    public String copyDiagramById(ESDiagramMoveCdt diagramMoveCdt) {
        return null;
    }

    @Override
    public Integer moveDirAndDiagram(MoveDirAndDiagramCdt move) {
        return null;
    }

    @Override
    public List<Long> copyDiagramByIds(ESDiagramMoveCdt diagramMoveCdt) {
        return null;
    }

    @Override
    public List<Long> copyDirById(Long targetDirId, Long[] dirIds, String[] diagramIds) {
        return null;
    }

    @Override
    public void processBatchThumbnail(ThumbnailBatch thumbnailBatch) {

    }

    @Override
    public Page<ESSimpleDiagramDTO> queryESDiagramInfoPage(Long domainId, Integer pageNum, Integer pageSize, CVcDiagram dCdt, String modifyTime) {
        return null;
    }

    @Override
    public Long[] queryDiagramInfoBydEnergy(String[] diagramIds) {
        return new Long[0];
    }

    @Override
    public Long queryDiagramInfoByEnergy(String diagramId) {
        return null;
    }

    @Override
    public Long getCountByDirId(Long dirId, Integer type) {
        return null;
    }

    @Override
    public void removeDiagrams(List<Long> dirIds, String ownerCode) {

    }

    @Override
    public List<ESDiagram> findEsDiagramList(Long dirId, String ownerCode) {
        return null;
    }

    @Override
    public ESDiagram getEsDiagram(String dEnergy, Integer isOpen) {
        return null;
    }

    @Override
    public int updateNameByDiagramId(String newName, String dEnergyId) {
        return 0;
    }

    @Override
    public List<ESDiagram> selectByDirIds(Collection<Long> dirIds, List<Integer> dirTypes, String ownerCode, List<Integer> isOpens) {
        return null;
    }

    @Override
    public Boolean clearReleaseDiagramIdBydEnergyId(Collection<String> dEnergyId, String ownerCode) {
        return null;
    }

    @Override
    public List<Long> fxCopyDiagramByIds(ESDiagramMoveCdt diagramMoveCdt) {
        return null;
    }

    @Override
    public List<ESDiagram> selectByDirType(Integer dirTypes, String ownerCode, List<Integer> isOpens) {
        return null;
    }

    @Override
    public int createHiddenLink(RuleParams params) {
        return 0;
    }

    @Override
    public ESDiagram querySimpleDiagramInfoById(Long diagramId) {
        return null;
    }

    @Override
    public List<ESDiagram> queryDBDiagramInfoByIds(String[] toArray) {
        return null;
    }

    @Override
    public Long[] queryDBDiagramInfoBydEnergy(String[] diagramIds) {
        return new Long[0];
    }

    @Override
    public Map<String, Set<DiagramRelationInfo>> getRelateInfoByDiagramIds(String diagramId, Integer browseStatus) {
        return null;
    }

    @Override
    public Page<ESDiagram> selectListByQuery(Integer pageNum, Integer pageSize, QueryBuilder query) {
        return null;
    }

    @Override
    public List<ESDiagramDTO> queryFullDiagramByIds(List<Long> diagramIds) {
        return null;
    }

    @Override
    public List<ESDiagramDTO> queryFullDiagramByIds(Collection<String> diagramIds) {
        return null;
    }

    @Override
    public Integer saveOrUpdateBatch(List<ESDiagram> list) {
        return null;
    }

    @Override
    public String deleteDiagramWithType(List<String> diagramIds, Long delDirId, Integer type) {
        return null;
    }

    @Override
    public Integer queryFlowStatusById(String diagramId) {
        return null;
    }

    @Override
    public Map<String, String> copyDiagramBatch(Map<String, Long> diagramDirIdMap, List<ESDiagram> diagramList, DiagramCopyEnum type) {
        return null;
    }
}
