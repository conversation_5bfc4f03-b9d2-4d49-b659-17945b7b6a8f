<?xml version="1.0" encoding="UTF-8"?><!--Converted at: Sat Dec 08 16:22:38 CST 2018-->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="VC_GROUP_LOG">


	<resultMap id="queryResult" type="com.uinnova.product.eam.comm.model.VcGroupLog">
		<result property="id" column="ID" jdbcType="BIGINT"/>	<!-- ID -->
		<result property="groupId" column="GROUP_ID" jdbcType="BIGINT"/>	<!-- 组ID -->
		<result property="logTime" column="LOG_TIME" jdbcType="BIGINT"/>	<!-- 日志时间 -->
		<result property="opId" column="OP_ID" jdbcType="BIGINT"/>	<!-- 操作人ID -->
		<result property="opName" column="OP_NAME" jdbcType="VARCHAR"/>	<!-- 操作人名称 -->
		<result property="sourceType" column="SOURCE_TYPE" jdbcType="INTEGER"/>	<!-- 来源类别 -->
		<result property="logType" column="LOG_TYPE" jdbcType="INTEGER"/>	<!-- 日志类型 -->
		<result property="logDesc" column="LOG_DESC" jdbcType="VARCHAR"/>	<!-- 日志描述 -->
		<result property="domainId" column="DOMAIN_ID" jdbcType="BIGINT"/>	<!-- 所属域 -->
		<result property="createTime" column="CREATE_TIME" jdbcType="BIGINT"/>	<!-- 创建时间 -->
		<result property="modifyTime" column="MODIFY_TIME" jdbcType="BIGINT"/>	<!-- 修改时间 -->
	</resultMap>
	

	<sql id="sql_query_where">
		<if test="cdt != null and cdt.id != null">and
			ID = #{cdt.id:BIGINT}
		</if>
		<if test="ids != null and ids != ''">and
			ID in (${ids})
		</if>
		<if test="cdt != null and cdt.startId != null">and
			 ID &gt;= #{cdt.startId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endId != null">and
			 ID &lt;= #{cdt.endId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.groupId != null">and
			GROUP_ID = #{cdt.groupId:BIGINT}
		</if>
		<if test="groupIds != null and groupIds != ''">and
			GROUP_ID in (${groupIds})
		</if>
		<if test="cdt != null and cdt.startGroupId != null">and
			 GROUP_ID &gt;= #{cdt.startGroupId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endGroupId != null">and
			 GROUP_ID &lt;= #{cdt.endGroupId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.logTime != null">and
			LOG_TIME = #{cdt.logTime:BIGINT}
		</if>
		<if test="logTimes != null and logTimes != ''">and
			LOG_TIME in (${logTimes})
		</if>
		<if test="cdt != null and cdt.startLogTime != null">and
			 LOG_TIME &gt;= #{cdt.startLogTime:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endLogTime != null">and
			 LOG_TIME &lt;= #{cdt.endLogTime:BIGINT} 
		</if>
		<if test="cdt != null and cdt.opId != null">and
			OP_ID = #{cdt.opId:BIGINT}
		</if>
		<if test="opIds != null and opIds != ''">and
			OP_ID in (${opIds})
		</if>
		<if test="cdt != null and cdt.startOpId != null">and
			 OP_ID &gt;= #{cdt.startOpId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endOpId != null">and
			 OP_ID &lt;= #{cdt.endOpId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.opName != null and cdt.opName != ''">and
			OP_NAME like #{cdt.opName,jdbcType=VARCHAR} 
		</if>
		<if test="cdt != null and cdt.opNameEqual != null and cdt.opNameEqual != ''">and
			OP_NAME = #{cdt.opNameEqual,jdbcType=VARCHAR}
		</if>
		<if test="opNames != null and opNames != ''">and
			OP_NAME in (${opNames})
		</if>
		<if test="cdt != null and cdt.sourceType != null">and
			SOURCE_TYPE = #{cdt.sourceType:INTEGER}
		</if>
		<if test="sourceTypes != null and sourceTypes != ''">and
			SOURCE_TYPE in (${sourceTypes})
		</if>
		<if test="cdt != null and cdt.startSourceType != null">and
			 SOURCE_TYPE &gt;= #{cdt.startSourceType:INTEGER} 
		</if>
		<if test="cdt != null and cdt.endSourceType != null">and
			 SOURCE_TYPE &lt;= #{cdt.endSourceType:INTEGER} 
		</if>
		<if test="cdt != null and cdt.logType != null">and
			LOG_TYPE = #{cdt.logType:INTEGER}
		</if>
		<if test="logTypes != null and logTypes != ''">and
			LOG_TYPE in (${logTypes})
		</if>
		<if test="cdt != null and cdt.startLogType != null">and
			 LOG_TYPE &gt;= #{cdt.startLogType:INTEGER} 
		</if>
		<if test="cdt != null and cdt.endLogType != null">and
			 LOG_TYPE &lt;= #{cdt.endLogType:INTEGER} 
		</if>
		<if test="cdt != null and cdt.logDesc != null and cdt.logDesc != ''">and
			LOG_DESC like #{cdt.logDesc,jdbcType=VARCHAR} 
		</if>
		<if test="cdt != null and cdt.domainId != null">and
			DOMAIN_ID = #{cdt.domainId:BIGINT}
		</if>
		<if test="domainIds != null and domainIds != ''">and
			DOMAIN_ID in (${domainIds})
		</if>
		<if test="cdt != null and cdt.startDomainId != null">and
			 DOMAIN_ID &gt;= #{cdt.startDomainId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endDomainId != null">and
			 DOMAIN_ID &lt;= #{cdt.endDomainId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.createTime != null">and
			CREATE_TIME = #{cdt.createTime:BIGINT}
		</if>
		<if test="createTimes != null and createTimes != ''">and
			CREATE_TIME in (${createTimes})
		</if>
		<if test="cdt != null and cdt.startCreateTime != null">and
			 CREATE_TIME &gt;= #{cdt.startCreateTime:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endCreateTime != null">and
			 CREATE_TIME &lt;= #{cdt.endCreateTime:BIGINT} 
		</if>
		<if test="cdt != null and cdt.modifyTime != null">and
			MODIFY_TIME = #{cdt.modifyTime:BIGINT}
		</if>
		<if test="modifyTimes != null and modifyTimes != ''">and
			MODIFY_TIME in (${modifyTimes})
		</if>
		<if test="cdt != null and cdt.startModifyTime != null">and
			 MODIFY_TIME &gt;= #{cdt.startModifyTime:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endModifyTime != null">and
			 MODIFY_TIME &lt;= #{cdt.endModifyTime:BIGINT} 
		</if>
	</sql>
	

	<sql id="sql_update_columns">
		<if test="record != null and record.id != null"> 
			ID = #{record.id:BIGINT}
		,</if>
		<if test="record != null and record.groupId != null"> 
			GROUP_ID = #{record.groupId:BIGINT}
		,</if>
		<if test="record != null and record.logTime != null"> 
			LOG_TIME = #{record.logTime:BIGINT}
		,</if>
		<if test="record != null and record.opId != null"> 
			OP_ID = #{record.opId:BIGINT}
		,</if>
		<if test="record != null and record.opName != null"> 
			OP_NAME = #{record.opName,jdbcType=VARCHAR}
		,</if>
		<if test="record != null and record.sourceType != null"> 
			SOURCE_TYPE = #{record.sourceType:INTEGER}
		,</if>
		<if test="record != null and record.logType != null"> 
			LOG_TYPE = #{record.logType:INTEGER}
		,</if>
		<if test="record != null and record.logDesc != null"> 
			LOG_DESC = #{record.logDesc,jdbcType=VARCHAR}
		,</if>
		<if test="record != null and record.domainId != null"> 
			DOMAIN_ID = #{record.domainId:BIGINT}
		,</if>
		<if test="record != null and record.createTime != null"> 
			CREATE_TIME = #{record.createTime:BIGINT}
		,</if>
		<if test="record != null and record.modifyTime != null"> 
			MODIFY_TIME = #{record.modifyTime:BIGINT}
		,</if>
	</sql>
	

	<sql id="sql_query_columns">
		ID, GROUP_ID, LOG_TIME, OP_ID, OP_NAME, SOURCE_TYPE, 
		LOG_TYPE, LOG_DESC, DOMAIN_ID, CREATE_TIME, MODIFY_TIME
	</sql>
	

	

	<select id="selectList" parameterType="java.util.Map" resultMap="queryResult">
		select 
			<include refid="VC_GROUP_LOG.sql_query_columns"/>
		from VC_GROUP_LOG 
			<where>
				<include refid="VC_GROUP_LOG.sql_query_where"/>
			</where>
		order by 
			<if test="orders != null and orders != ''">
				${orders}
			</if>
			<if test="orders == null or orders == ''">
				ID
			</if>
	</select>
	<select id="selectCount" parameterType="java.util.Map" resultType="java.lang.Long">
		select count(1) from VC_GROUP_LOG 
			<where>
				<include refid="VC_GROUP_LOG.sql_query_where"/>
			</where>
	</select>
	<select id="selectById" parameterType="java.util.Map" resultMap="queryResult">
		select 
			<include refid="VC_GROUP_LOG.sql_query_columns"/>
		from VC_GROUP_LOG where ID=#{id:BIGINT} 
	</select>
	

	

	<insert id="insert" parameterType="java.util.Map">
		insert into VC_GROUP_LOG(
			ID, GROUP_ID, LOG_TIME, OP_ID, OP_NAME, 
			SOURCE_TYPE, LOG_TYPE, LOG_DESC, DOMAIN_ID, CREATE_TIME, 
			MODIFY_TIME)
		values (
			#{record.id:BIGINT}, #{record.groupId:BIGINT}, #{record.logTime:BIGINT}, #{record.opId:BIGINT}, #{record.opName,jdbcType=VARCHAR}, 
			#{record.sourceType:INTEGER}, #{record.logType:INTEGER}, #{record.logDesc,jdbcType=VARCHAR}, #{record.domainId:BIGINT}, #{record.createTime:BIGINT}, 
			#{record.modifyTime:BIGINT})
	</insert>
	

	

	<update id="updateById" parameterType="java.util.Map">
		update VC_GROUP_LOG
			<set> 
				<include refid="VC_GROUP_LOG.sql_update_columns"/> 
			</set>
		where ID = #{id:BIGINT}
	</update>
	<update id="updateByCdt" parameterType="java.util.Map">
		update VC_GROUP_LOG
			<set> 
				<include refid="VC_GROUP_LOG.sql_update_columns"/> 
			</set>
			<where> 
				<include refid="VC_GROUP_LOG.sql_query_where"/> 
			</where>
	</update>
	
	

	

	<delete id="deleteById" parameterType="java.util.Map">
		delete from VC_GROUP_LOG where ID = #{id:BIGINT}
	</delete>
	<delete id="deleteByCdt" parameterType="java.util.Map">
		delete from VC_GROUP_LOG
			<where> 
				<include refid="VC_GROUP_LOG.sql_query_where"/> 
			</where>
	</delete>
	



</mapper>