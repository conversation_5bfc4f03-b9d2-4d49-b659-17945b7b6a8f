package com.uino.service.cmdb.microservice.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.binary.core.exception.BinaryException;
import com.binary.core.exception.MessageException;
import com.binary.core.i18n.LanguageResolver;
import com.binary.core.util.BinaryUtils;
import com.binary.framework.exception.ServiceException;
import com.binary.framework.spring.MultipartFileResource;
import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.comm.i18n.MessageUtil;
import com.uinnova.product.vmdb.comm.i18n.VerifyType;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClass;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClassDir;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.comm.model.ci.CcCiClass;
import com.uinnova.product.vmdb.comm.util.CommUtil;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uino.bean.cmdb.SysTopData;
import com.uino.bean.cmdb.base.*;
import com.uino.bean.cmdb.business.*;
import com.uino.bean.cmdb.enums.AttrNameKeyEnum;
import com.uino.bean.cmdb.query.ESCIClassSearchBean;
import com.uino.bean.monitor.base.ESKpiInfo;
import com.uino.bean.monitor.buiness.SearchKpiBean;
import com.uino.bean.permission.base.*;
import com.uino.bean.permission.query.CSysRoleDataModuleRlt;
import com.uino.bean.sys.base.ESDictionaryAttrDef;
import com.uino.bean.sys.base.ESDictionaryClassInfo;
import com.uino.bean.sys.query.ESDictionaryItemSearchBean;
import com.uino.dao.BaseConst;
import com.uino.dao.cmdb.*;
import com.uino.dao.permission.ESDataModuleSvc;
import com.uino.dao.permission.ESRoleSvc;
import com.uino.dao.util.ESUtil;
import com.uino.service.cmdb.microservice.ICIClassSvc;
import com.uino.service.permission.data.ClassViewPermission;
import com.uino.service.permission.microservice.impl.RoleSvc;
import com.uino.service.simulation.impl.KpiSvc;
import com.uino.service.sys.microservice.impl.DictionarySvc;
import com.uino.service.util.FileUtil;
import com.uino.util.sys.BeanUtil;
import com.uino.util.sys.CheckAttrUtil;
import com.uino.util.sys.SysUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.MultiMatchQueryBuilder.Type;
import org.elasticsearch.index.query.Operator;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.sort.SortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.*;
import java.util.Map.Entry;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@RefreshScope
public class CIClassSvc implements ICIClassSvc {

    private static final Logger logger = org.slf4j.LoggerFactory.getLogger(CIClassSvc.class);

    @Autowired
    private ESCIClassSvc esciClassSvc;

    @Autowired
    private ESCISvc esCiSvc;

    @Autowired
    private ESDirSvc esDirSvc;

    @Autowired
    @Lazy
    private ESCmdbCommSvc commSvc;

    @Autowired
    private TopDataSvc topSvc;

    @Autowired
    private ESImageSvc esImageSvc;

    @Autowired
    private ESCIAttrTransConfigSvc attrTransConfigSvc;

    @Autowired
    private KpiSvc kpiSvc;

    @Autowired
    private ESCIHistorySvc ciHistorySvc;

    @Autowired
    private DictionarySvc dictSvc;

    @Autowired
    private RoleSvc roleSvc;

    @Autowired
    private ESRoleSvc esRoleSvc;

    @Autowired
    ESDataModuleSvc dataModuleSvc;

    @Value("${http.resource.space:}")
    private String rsmSlaveRoot;

    @Value("${uino.base.ci.primarykey.maxcount:5}")
    private Integer primaryKeyCount;

    @Value("${is.show.3d.attribute:false}")
    private Boolean isShow3dAttribute;

    @Value("${3d.attribute.name:3D模型}")
    private String attributeDefinitionFor3dModel;

    private static final String TEXT_REGEX = "[ `\\\\~!@$%^&*()+={}':;',\\[\\].<>/?~！@￥%……&*（）——+{}【】‘；：”“’。，、？]|\n|\r|\t";
    private static final String IMG_NAME_REGEX = ".*[.](jpg|png|gif|jpeg|bmp|tiff|pcx|tga|exif|fpx|svg|psd|cdr|pcd|dxf|ufo|eps|ai|raw|WMF)";
    private static final String DOC_NAME_REGEX = ".*[.](doc|docx|xls|xlsx|ppt|pptx|pdf)";
    private static final List<String> DEFAULT_TAGS = Arrays.asList("系统设置","单选","搜索项","不显示","禁用","角色","多选","制度","标准","表单","流程","复制清空","不可重复","不可编辑","术语","要素","只读");

    @Override
    public String getHttpResourceSpace() {
        return rsmSlaveRoot;
    }

    @Override
    public Boolean isShow3dAttribute() {
        return isShow3dAttribute;
    }

    @Override
    public List<ESCIClassInfo> queryESClassInfoByIds(List<Long> classIds) {
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termsQuery("id", classIds));
        return esciClassSvc.getListByQuery(query);
    }

    @Override
    public List<ESCIClassInfo> queryEsClassInfoByDtClassIds(List<String> dtClassIds, Long domainId) {
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("domainId", null != domainId ? domainId : BaseConst.DEFAULT_DOMAIN_ID));

        List<Long> classIds = new ArrayList<>(8);
        try {
            classIds = dtClassIds.stream().map(Long::parseLong).collect(Collectors.toList());
        } catch (Exception ignored) {
        }

        BoolQueryBuilder shouldQuery = QueryBuilders.boolQuery();
        if (!CollectionUtils.isEmpty(classIds)) {
            shouldQuery.should(QueryBuilders.termsQuery("id", classIds));
        }
        shouldQuery.should(QueryBuilders.termsQuery("dtClassId.keyword", dtClassIds));

        query.must(shouldQuery);
        return esciClassSvc.getListByQuery(query);
    }

    @Override
    public Long saveOrUpdate(CcCiClassInfo record) {
        if (record.getCiClass().getDomainId() == null) {
            record.getCiClass().setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
        }
        // Assert.notEmpty(record.getCcAttrDefs(),
        // "BS_FIELD_EMPTY_VAL${\"field\":\"attrDefs\"}");
        // 分类信息格式化
        ESCIClassInfo esClsInfo = commSvc.tranESCIClassInfo(record);
        return this.saveOrUpdate(esClsInfo);
    }

    @Override
    public Long saveOrUpdate(ESCIClassInfo esClsInfo) {
        if (esClsInfo.getDomainId() == null) {
            esClsInfo.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
        }
        boolean isAdd = esClsInfo.getId() == null;
        if (!isAdd) {
            ESCIClassInfo oldCls = esciClassSvc.getById(esClsInfo.getId());
            Assert.notNull(oldCls, "BS_MNAME_CLASS_NOT_EXSIT");
            esClsInfo.setCreateTime(oldCls.getCreateTime());
        }
        this.validAndBuild(esClsInfo);
        Assert.isTrue(esClsInfo.getIcon().matches(IMG_NAME_REGEX), "不支持的图标类型");
        SysUser loginUser = null;
        try {
            loginUser = SysUtil.getCurrentUserInfo();
        } catch (Exception e) {
        }
        String classCode = esClsInfo.getClassCode().trim();
        // 校验分类重复
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("domainId", esClsInfo.getDomainId()));
        query.must(QueryBuilders.termQuery("classStdCode.keyword", classCode.toUpperCase()));
        if (!isAdd) {
            query.mustNot(QueryBuilders.termQuery("id", esClsInfo.getId()));
        }
        Assert.isTrue(BinaryUtils.isEmpty(esciClassSvc.getListByQuery(query)), "BS_CC_CLASS_EXIST");
        Assert.isTrue(!BinaryUtils.isEmpty(esDirSvc.getById(esClsInfo.getDirId())), "所属领域不存在！");
        // 校验属性重复
        List<String> proNames = new ArrayList<String>();
        if (esClsInfo.getParentId() != null && esClsInfo.getParentId() != 0) {
            ESCIClassInfo parClass = esciClassSvc.getById(esClsInfo.getParentId());
            Assert.isTrue(!BinaryUtils.isEmpty(parClass), "BS_PARNET_CLASS_NOT_EXIST");
            Assert.isTrue(parClass.getParentId() == 0, "至多可创建一级子类");
            List<String> parDefs = parClass.getCcAttrDefs().stream().map(CcCiAttrDef::getProStdName).collect(Collectors.toList());
            proNames.addAll(parDefs);
        } else if (!isAdd) {
            List<ESCIClassInfo> list = esciClassSvc.getListByQuery(QueryBuilders.termQuery("parentId", esClsInfo.getId()));
            list.forEach(cls -> proNames.addAll(cls.getCcAttrDefs().stream().map(CcCiAttrDef::getProStdName).collect(Collectors.toList())));
        }
        List<CcCiAttrDef> attrDefs = esClsInfo.getCcAttrDefs();
        for (CcCiAttrDef attrDef : attrDefs) {
            String proStdName = attrDef.getProStdName();
            Assert.isTrue(!proNames.contains(proStdName), "BS_CC_CLASS_DEF_EXIST${proName:" + proStdName + "}");
            proNames.add(proStdName);
            // attrDef.setId(attrDef.getId() == null ? ESUtil.getUUID() :
            // attrDef.getId());
        }

        // 3D model attribute verification
        this.attributeVerificationFor3dModel(attrDefs);

        if (isAdd) {
            long id = ESUtil.getUUID();
            esClsInfo.setId(id);
            // dtClassId默认与id相同
            esClsInfo.setDtClassId(esClsInfo.getDtClassId() == null ? String.valueOf(id) : esClsInfo.getDtClassId());
            esClsInfo.setCreator(loginUser == null ? "system" : loginUser.getLoginCode());
            //新增分类默认授权
            addAdminDataModuleRlt(esClsInfo.getDomainId(), Collections.singletonList(esClsInfo));
        } else {
            // 保持子分类与父分类所属同领域
            esciClassSvc.updateByQuery(QueryBuilders.termQuery("parentId", esClsInfo.getId()), "ctx._source.dirId=" + esClsInfo.getDirId() + "L", true);
            esClsInfo.setModifier(loginUser == null ? "system" : loginUser.getLoginCode());
        }
        return esciClassSvc.saveOrUpdate(esClsInfo);
    }

    @Override
    public Long saveOrUpdateExtra(ESCIClassInfo esClsInfo) {
        if (esClsInfo.getDomainId() == null) {
            esClsInfo.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
        }
        boolean isAdd = esClsInfo.getId() == null;
        if (!isAdd) {
            ESCIClassInfo oldCls = esciClassSvc.getById(esClsInfo.getId());
            Assert.notNull(oldCls, "BS_MNAME_CLASS_NOT_EXSIT");
            esClsInfo.setCreateTime(oldCls.getCreateTime());
        }
        this.validAndBuild(esClsInfo);
        Assert.isTrue(esClsInfo.getIcon().matches(IMG_NAME_REGEX), "不支持的图标类型");
        SysUser loginUser = null;
        try {
            loginUser = SysUtil.getCurrentUserInfo();
        } catch (Exception e) {
        }
        String className = esClsInfo.getClassName();
        String classCode = esClsInfo.getClassCode().trim();
        // 校验分类重复
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("domainId", esClsInfo.getDomainId()));
        query.must(QueryBuilders.termQuery("className.keyword", className));
        query.must(QueryBuilders.termQuery("classCode.keyword",classCode));
        if (!isAdd) {
            query.mustNot(QueryBuilders.termQuery("id", esClsInfo.getId()));
        }
        Assert.isTrue(BinaryUtils.isEmpty(esciClassSvc.getListByQuery(query)), "BS_CC_CLASS_EXIST");
        Assert.isTrue(!BinaryUtils.isEmpty(esDirSvc.getById(esClsInfo.getDirId())), "所属领域不存在！");
        // 校验属性重复
        List<String> proNames = new ArrayList<String>();
        if (esClsInfo.getParentId() != null && esClsInfo.getParentId() != 0) {
            ESCIClassInfo parClass = esciClassSvc.getById(esClsInfo.getParentId());
            Assert.isTrue(!BinaryUtils.isEmpty(parClass), "BS_PARNET_CLASS_NOT_EXIST");
            Assert.isTrue(parClass.getParentId() == 0, "至多可创建一级子类");
            List<String> parDefs = parClass.getCcAttrDefs().stream().map(CcCiAttrDef::getProStdName).collect(Collectors.toList());
            proNames.addAll(parDefs);
        } else if (!isAdd) {
            List<ESCIClassInfo> list = esciClassSvc.getListByQuery(QueryBuilders.termQuery("parentId", esClsInfo.getId()));
            list.forEach(cls -> proNames.addAll(cls.getCcAttrDefs().stream().map(CcCiAttrDef::getProStdName).collect(Collectors.toList())));
        }
        List<CcCiAttrDef> attrDefs = esClsInfo.getCcAttrDefs();
        for (CcCiAttrDef attrDef : attrDefs) {
            String proStdName = attrDef.getProStdName();
            Assert.isTrue(!proNames.contains(proStdName), "BS_CC_CLASS_DEF_EXIST${proName:" + proStdName + "}");
            proNames.add(proStdName);
            // attrDef.setId(attrDef.getId() == null ? ESUtil.getUUID() :
            // attrDef.getId());
        }

        // 3D model attribute verification
        this.attributeVerificationFor3dModel(attrDefs);

        if (isAdd) {
            long id = ESUtil.getUUID();
            esClsInfo.setId(id);
            // dtClassId默认与id相同
            esClsInfo.setDtClassId(esClsInfo.getDtClassId() == null ? String.valueOf(id) : esClsInfo.getDtClassId());
            esClsInfo.setClassCode(esClsInfo.getClassCode() == null ? esClsInfo.getId().toString() : esClsInfo.getClassCode());
            esClsInfo.setCreator(loginUser == null ? "system" : loginUser.getLoginCode());
            //新增分类默认授权
            addAdminDataModuleRlt(esClsInfo.getDomainId(), Collections.singletonList(esClsInfo));
        } else {
            // 保持子分类与父分类所属同领域
            esciClassSvc.updateByQuery(QueryBuilders.termQuery("parentId", esClsInfo.getId()), "ctx._source.dirId=" + esClsInfo.getDirId() + "L", true);
            esClsInfo.setModifier(loginUser == null ? "system" : loginUser.getLoginCode());
        }
        return esciClassSvc.saveOrUpdate(esClsInfo);
    }

    @Override
    public Integer saveOrUpdateBatch(Long domainId, List<CcCiClassInfo> clsInfos) {
        if (domainId == null) {
            domainId = BaseConst.DEFAULT_DOMAIN_ID;
        }

        //查询该租户域下所有的领域
        List<CcCiClassDir> dirs = esDirSvc.getListByQuery(QueryBuilders.termQuery("domainId", domainId));
        List<Long> dirIds = dirs.stream().filter(dir -> dir.getCiType() == 1).map(CcCiClassDir::getId).collect(Collectors.toList());
        //查询该租户域下所有的分类
        List<ESCIClassInfo> clsList = esciClassSvc.getListByQuery(QueryBuilders.termQuery("domainId", domainId));
        Map<String, ESCIClassInfo> clsMap = BinaryUtils.toObjectMap(clsList, "className");
        //获取该租户域下所有的父级分类
        Map<Long, List<CcCiAttrDef>> parentClsAttrMap = clsList.stream().filter(cls -> cls.getParentId() == 0).collect(Collectors.toMap(ESCIClassInfo::getId, ESCIClassInfo::getCcAttrDefs));

        List<ESCIClassInfo> saveCls = new ArrayList<>();
        Set<String> saveClsNames=new HashSet<>();
        for (CcCiClassInfo clsInfo : clsInfos) {
            //校验领域是否存在
            Long dirId = clsInfo.getCiClass().getDirId();
            Assert.isTrue(dirIds.contains(dirId), "领域[" + dirId + "]不存在！");
            //校验分类重复
            String className=clsInfo.getCiClass().getClassName();
            Assert.isTrue(!saveClsNames.contains(className),"分类[" + className + "]重复");
            saveClsNames.add(className);
            if (clsInfo.getCiClass().getId() == null) {
                Assert.isTrue(!clsMap.containsKey(className), "分类[" + className + "]已存在");
            }
            ESCIClassInfo saveClass = commSvc.tranESCIClassInfo(clsInfo);
            if (BinaryUtils.isEmpty(saveClass.getDomainId())) {
                saveClass.setDomainId(domainId);
            }
            //丰富校验分类及属性信息
            this.validAndBuild(saveClass);
            Assert.isTrue(saveClass.getIcon().matches(IMG_NAME_REGEX), "不支持的图标类型");
            //校验属性是否重复
            List<String> proNames = new ArrayList<>();
            if (saveClass.getParentId() != null) {
                if (saveClass.getParentId() == 0) {
                    parentClsAttrMap.put(saveClass.getId(), saveClass.getCcAttrDefs());
                } else {
                    Long parentId=saveClass.getParentId();
                    Assert.isTrue(parentClsAttrMap.containsKey(parentId), "父级分类[" + parentId + "]不存在或[" + parentId + "]不可创建一级子类");
                    List<String> parentAttrNames = parentClsAttrMap.get(saveClass.getParentId()).stream().map(CcCiAttrDef::getProStdName).collect(Collectors.toList());
                    proNames.addAll(parentAttrNames);
                }
            }
            List<CcCiAttrDef> saveDefs = saveClass.getCcAttrDefs();
            //3D模型属性校验
            this.attributeVerificationFor3dModel(saveDefs);
            for (CcCiAttrDef saveDef : saveDefs) {
                String proStdName = saveDef.getProStdName();
                Assert.isTrue(!proNames.contains(proStdName), "BS_CC_CLASS_DEF_EXIST${proName:" + proStdName + "}");
                proNames.add(proStdName);
            }
            saveCls.add(saveClass);
        }
        //初始化分类权限
        addAdminDataModuleRlt(domainId, saveCls);
        return esciClassSvc.saveOrUpdateBatch(saveCls);
    }

    @Override
    public ESCIClassInfo queryESClassInfoById(Long id) {
        ESCIClassInfo ciclass = esciClassSvc.getById(id);
//        Assert.notNull(ciclass, "BS_MNAME_CLASS_NOT_EXSIT");
        if (BinaryUtils.isEmpty(ciclass)) {
            return null;
        }
        String icon = BinaryUtils.isEmpty(ciclass.getIcon()) ? esciClassSvc.getDefaultIcon() : ciclass.getIcon();
        if (icon.startsWith(rsmSlaveRoot)) {
            icon = icon.replaceAll(rsmSlaveRoot, "");
        }
        ciclass.setIcon(rsmSlaveRoot + icon);
        //填充3d模型URL
        if (isShow3dAttribute && StringUtils.isNotBlank(ciclass.getModelIcon3D())) {
            String modelIcon3D = ciclass.getModelIcon3D();
            if (modelIcon3D.startsWith(rsmSlaveRoot)) {
                modelIcon3D = modelIcon3D.replaceAll(rsmSlaveRoot, "");
            }
            ciclass.setModelIcon3D(rsmSlaveRoot + modelIcon3D);
        }
        // 3D attribute verification
        attributeVerificationForImgByQuery(ciclass.getAttrDefs());
        return ciclass;
    }

    @Override
    public CcCiClassInfo queryClassInfoById(Long id) {
        return esciClassSvc.queryClassInfoById(id);
    }

    @Override
//    @CIViewPermission(child = true, childName = "ciClass")
    public List<CcCiClassInfo> queryClassByCdt(CCcCiClass cdt) {
        if (cdt == null) {
            cdt = new CCcCiClass();
        }
        if (cdt.getDomainId() == null) {
            cdt.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
        }
        return esciClassSvc.queryClassByCdt(cdt);
    }

    @Override
//    @ClassViewPermission(child = true, childName = "ciClass")
    public List<CcCiClassInfo> queryCiClassInfoList(Long domainId, CCcCiClass cdt, String orders, Boolean isAsc) {
        if (cdt == null) {
            cdt = new CCcCiClass();
        }
        domainId = domainId == null ? BaseConst.DEFAULT_DOMAIN_ID : domainId;
        cdt.setDomainId(domainId);
        return esciClassSvc.queryCiClassInfoList(cdt, orders, isAsc);
    }

    @Override
    public List<CcCiClassInfo> queryCiClassInfoListBySearchBean(ESCIClassSearchBean bean) {
        List<ESCIClassInfo> esClsInfos = this.queryESCiClassInfoListBySearchBean(bean);
        List<CcCiClassInfo> res = new ArrayList<>();
        for (ESCIClassInfo esciClassInfo : esClsInfos) {
            res.add(commSvc.tranCcCiClassInfo(esciClassInfo));
        }
        return res;
    }

    @Override
    public List<ESCIClassInfo> queryESCiClassInfoListBySearchBean(ESCIClassSearchBean bean) {
        bean.setPageSize(99999);
        return this.queryESCiClassInfoPageBySearchBean(bean).getData();
    }

    @Override
    public Page<ESCIClassInfo> queryESCiClassInfoPageBySearchBean(ESCIClassSearchBean bean) {
        CCcCiClass cdt = bean.getCdt();
        if (cdt == null) {
            cdt = new CCcCiClass();
        }
        if (cdt.getDomainId() == null) {
            cdt.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
        }
        if (bean.getIsBindKpi()) {
            SearchKpiBean searchDto = SearchKpiBean.builder().pageNum(1).pageSize(9999).build();
            List<ESKpiInfo> kpis = kpiSvc.queryKpiInfoPage(searchDto).getData();
            if (BinaryUtils.isEmpty(kpis)) {
                return new Page<>(bean.getPageNum(), bean.getPageSize(), 0, 0, new ArrayList<>());
            }
            Set<Object> clsIds = new HashSet<>();
            kpis.forEach(kpi -> {
                List<Long> kpiClassIds = kpi.getClassIds();
                if (!BinaryUtils.isEmpty(kpiClassIds)) {
                    clsIds.add(kpiClassIds);
                }
            });
            if (cdt.getIds() != null) {
                Collections.addAll(clsIds, cdt.getIds());
            }
            cdt.setIds(clsIds.toArray(new Long[]{}));
        }
        List<SortBuilder<?>> sorts = new LinkedList<>();
        String orders = ESUtil.underlineToCamel(bean.getOrders());
        // sorts.add(SortBuilders.fieldSort("dirId").order(bean.getIsAsc() ?
        // SortOrder.ASC : SortOrder.DESC));
        if (!BinaryUtils.isEmpty(orders)) {
            sorts.add(SortBuilders.fieldSort(orders).order(bean.getIsAsc() ? SortOrder.ASC : SortOrder.DESC));
        } else {
            sorts.add(SortBuilders.fieldSort("orderNo").order(SortOrder.ASC).unmappedType("long"));
            sorts.add(SortBuilders.fieldSort("createTime").order(SortOrder.ASC));
        }
        BoolQueryBuilder query = ESUtil.cdtToBuilder(cdt);
        if (!BinaryUtils.isEmpty(bean.getKeyword())) {
            query.must(QueryBuilders.multiMatchQuery(bean.getKeyword(), "className", "dtClassId").operator(Operator.AND)
                    .type(Type.PHRASE_PREFIX).lenient(true));
        }
        if (!BinaryUtils.isEmpty(bean.getDtClassId())) {
            query.must(QueryBuilders.termQuery("dtClassId.keyword", bean.getDtClassId()));
        }
        if (!BinaryUtils.isEmpty(bean.getProName())) {
            query.must(QueryBuilders.multiMatchQuery(bean.getProName(), "attrDefs.proName"));
        }
        Page<ESCIClassInfo> clsPage = esciClassSvc.getSortListByQuery(bean.getPageNum(), bean.getPageSize(), query,
                sorts);
        // Page<ESCIClassInfo> clsPage =
        // esciClassSvc.getSortListByCdt(bean.getPageNum(), bean.getPageSize(), cdt,
        // orders,
        // bean.getIsAsc());
        if (!BinaryUtils.isEmpty(clsPage.getData())) {
            for (ESCIClassInfo ciclass : clsPage.getData()) {
                String icon = BinaryUtils.isEmpty(ciclass.getIcon()) ? esciClassSvc.getDefaultIcon()
                        : ciclass.getIcon();
                if (icon.startsWith(rsmSlaveRoot)) {
                    icon = icon.replaceAll(rsmSlaveRoot, "");
                }
                //填充3d模型URL
                if (isShow3dAttribute && StringUtils.isNotBlank(ciclass.getModelIcon3D())) {
                    String modelIcon3D = ciclass.getModelIcon3D();
                    if (modelIcon3D.startsWith(rsmSlaveRoot)) {
                        modelIcon3D = modelIcon3D.replaceAll(rsmSlaveRoot, "");
                    }
                    ciclass.setModelIcon3D(rsmSlaveRoot + modelIcon3D);
                    attributeVerificationForImgByQuery(ciclass.getAttrDefs());
                }
                ciclass.setIcon(rsmSlaveRoot + icon);
            }
        }
        return clsPage;
    }

    @Override
    public CcCiClassInfo getCiClassByClassName(String className) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termQuery("className.keyword", className));
        List<ESCIClassInfo> listByQuery = esciClassSvc.getListByQuery(boolQueryBuilder);
        if (CollectionUtils.isEmpty(listByQuery)) {
            throw new BinaryException("分类名称不存在");
        }
        return commSvc.tranCcCiClassInfo(listByQuery.get(0));
    }

    @Override
    public CcCiClassInfo getCiClassByClassCode(String classCode) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termQuery("classCode.keyword", classCode));
        List<ESCIClassInfo> listByQuery = esciClassSvc.getListByQuery(boolQueryBuilder);
        if (CollectionUtils.isEmpty(listByQuery)) {
            throw new BinaryException("分类Code不存在");
        }
        return commSvc.tranCcCiClassInfo(listByQuery.get(0));
    }

    @Override
//    @ClassViewPermission(subset = true)
    public List<ClassNodeInfo> getClassTree(Long domainId) {
        domainId = domainId == null ? BaseConst.DEFAULT_DOMAIN_ID : domainId;
        // 获取所有分类
        List<SortBuilder<?>> sorts = new LinkedList<>();
        sorts.add(SortBuilders.fieldSort("orderNo").order(SortOrder.ASC).unmappedType("long"));
        sorts.add(SortBuilders.fieldSort("createTime").order(SortOrder.ASC));
        List<ESCIClassInfo> clsList = esciClassSvc.getSortListByQuery(1, 9999, QueryBuilders.termQuery("domainId", domainId), sorts).getData();
        Map<Long, List<CcCiClass>> map = new HashMap<Long, List<CcCiClass>>();
        for (ESCIClassInfo cls : clsList) {
            Long dirId = cls.getDirId();
            CcCiClass ciClass = BeanUtil.converBean(cls, CcCiClass.class);
            String icon = BinaryUtils.isEmpty(ciClass.getIcon()) ? esciClassSvc.getDefaultIcon() : ciClass.getIcon();
            if (!icon.startsWith(this.rsmSlaveRoot)) {
                ciClass.setIcon(this.rsmSlaveRoot + icon);
            }
            map.computeIfAbsent(dirId, k -> new ArrayList<>());
            map.get(dirId).add(ciClass);
        }
        // 根据分类id查询ci的个数
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.matchAllQuery());
        boolQueryBuilder.filter(QueryBuilders.termQuery("domainId", domainId));
        Map<String, Long> clsCiCountMap = esCiSvc.groupByCountField("classId", boolQueryBuilder);
        // 获取领域
        CCcCiClassDir dirCdt = new CCcCiClassDir();
        // ciType=1表示CI分类目录
        dirCdt.setCiType(1);
        dirCdt.setDomainId(domainId);
        List<CcCiClassDir> dirs = esDirSvc.getSortListByCdt(dirCdt, new ArrayList<>(Arrays.asList(SortBuilders.fieldSort("createTime").order(SortOrder.ASC))));
        Map<Long, CcCiClassDir> dirMap = BinaryUtils.toObjectMap(dirs, "id");
        List<ClassNodeInfo> list = new ArrayList<>();
        // 查询置顶领域
        List<SysTopData> topDatas = topSvc.searTopData(4L);
        for (SysTopData top : topDatas) {
            Long dirId = top.getTopDataId();
            if (dirMap.containsKey(dirId)) {
                CcCiClassDir dir = dirMap.get(dirId);
                ClassNodeInfo node = ClassNodeInfo.builder().id(dir.getId()).name(dir.getDirName()).type("dir").build();
                this.classListToTree(node, map.get(dir.getId()), clsCiCountMap);
                list.add(node);
                dirs.removeIf(d -> d.getId().longValue() == dirId.longValue());
            }
        }
        for (CcCiClassDir dir : dirs) {
            ClassNodeInfo node = ClassNodeInfo.builder().id(dir.getId()).name(dir.getDirName()).type("dir").isSubClass(dir.getParentId() == 0 ? false : true).build();
            this.classListToTree(node, map.get(dir.getId()), clsCiCountMap);
            list.add(node);
        }
        return list;
    }

    @Override
    public ImportResultMessage importCiClassAttr(MultipartFile file, Long classId) {
        ImportResultMessage result = ImportResultMessage.builder().successNum(0).failNum(0).build();
        if (file == null || file.isEmpty()) {
            throw new ServiceException(" the upload file is empty! ");
        }
        if (file.getSize() > CommUtil.MAX_EXCEL_SIZE) {
            MessageUtil.throwVerify(VerifyType.OVERLENGTH, "file", file);
        }
        // 根据分类ID查询分类信息
        ESCIClassInfo esciClassInfo = esciClassSvc.getById(classId);
        if (esciClassInfo == null) {
            throw MessageException.i18n("BS_MNAME_CLASS_NOT_EXSIT");
        }
        Long domainId = esciClassInfo.getDomainId();
        String className = esciClassInfo.getClassName();
        ImportSheetMessage sheetMessage = ImportSheetMessage.builder().className(className).build();
        // 创建EXCEL文件
        MultipartFileResource resource = new MultipartFileResource(file);
        String fileType = CommUtil.getImportExcelType(resource.getName());
        InputStream is = resource.getInputStream();
        try {
            Workbook wb = null;
            Assert.isTrue(!BinaryUtils.isEmpty(fileType), "X_PARAM_NOT_NULL${name:fileType}");
            try {
                if (fileType.toUpperCase().equals(CommUtil.EXCEL03_XLS_EXTENSION.toUpperCase())) {
                    wb = new HSSFWorkbook(is);
                } else if (fileType.toUpperCase().equals(CommUtil.EXCEL07_XLSX_EXTENSION.toUpperCase())) {
                    wb = new XSSFWorkbook(is);
                } else {
                    // 无法操作不支持的文件类型！
                    throw MessageException.i18n("BS_MNAME_NOT_SUPPORT_FILETYPE");
                }
            } catch (Exception e) {
                throw BinaryUtils.transException(e, ServiceException.class);
            }
            List<CcCiAttrDef> attrDefs = esciClassInfo.getCcAttrDefs();
            // 记录已有数据的排序
            Integer startOrderNo = 0;
            if (attrDefs != null && attrDefs.size() > 0) {
                Integer lastOrderNo = attrDefs.get(attrDefs.size() - 1).getOrderNo();
                if (!BinaryUtils.isEmpty(lastOrderNo)) {
                    startOrderNo = lastOrderNo;
                }
            } else {
                attrDefs = new ArrayList<>();
            }
            // 获取已经存在的属性-包括父级属性
            List<CcCiAttrDef> defs = esciClassSvc.getAllDefsByClassId(domainId, classId);
            List<String> proNames = defs.stream().map(CcCiAttrDef::getProName).collect(Collectors.toList());
            // 保存可保存的记录
            List<String> saves = new ArrayList<String>();
            // sheet名称改为<领域名称>&<分类名称>格式，此处校验领域一致
            String sheetName = className;
            sheetMessage.setSheetName(sheetName);
            // 获取该分类的表头信息
            Sheet sheet = wb.getSheet(sheetName);
            if (sheet == null) {
                // 兼容不包含领域名情况，选中分类情况下，没有必要再校验领域
                sheet = wb.getSheet(className);
                if (sheet == null) {
                    throw MessageException.i18n("BS_MNAME_CLASS_DATA_ERROR", "{\"field\":" + className + "}");
                }
            }
            Row row = sheet.getRow(0);
            if (FileUtil.ExcelUtil.isBlankRow(row)) {
                throw MessageException.i18n("BS_MNAME_NOT_CLASS_DATA");
            }
            List<String> sheetTitles = new ArrayList<>();
            for (Cell cell : row) {
                sheetTitles.add(this.getValue(cell));
            }
            // 识别属性标识-主键或必填
            Map<String, List<String>> attrMark = FileUtil.ExcelUtil.getAttrMarkByTitles(sheetTitles);
            List<String> majorAttrs = attrMark.get("majorAttrs") == null ? new ArrayList<>() : attrMark.get("majorAttrs");
            List<String> requireAttrs = attrMark.get("requireAttrs") == null ? new ArrayList<>() : attrMark.get("requireAttrs");
            Assert.isTrue(esciClassInfo.getParentId() == 0 || majorAttrs.size() <= 0, "子分类不允许设置主键");
            sheetTitles = attrMark.get("titles");
            BoolQueryBuilder countQuery = QueryBuilders.boolQuery();
            countQuery.must(QueryBuilders.matchAllQuery());
            countQuery.must(QueryBuilders.termQuery("domainId", domainId));
            Map<String, Long> countMap = esCiSvc.groupByCountField("classId", countQuery);
            boolean hasData = countMap.get(String.valueOf(classId)) != null && countMap.get(String.valueOf(classId)) > 0;
            if (BinaryUtils.isEmpty(sheetTitles)) {
                sheetMessage.setTotalNum(0);
                sheetMessage.setFailNum(0);
                sheetMessage.setSuccessNum(0);
                throw MessageException.i18n("BS_MNAME_NOT_CLASS_DATA");
            }
            List<ImportCellMessage> cellMessages = new ArrayList<ImportCellMessage>();
            Integer ignoreNum = 0;
            Integer failNum = 0;
            Integer totalNum = 0;
            Integer insertNum = 0;
            List<CcCiAttrDef> newDefs = new ArrayList<>();
            for (int i = 0; i < sheetTitles.size(); i++) {
                ImportCellMessage cellMessage = new ImportCellMessage();
                if (i == 0 || BinaryUtils.isEmpty(sheetTitles.get(i))) {
                    // 统一默认第一列为数据库中ciCode,非属性定义
                    continue;
                }
                String title = sheetTitles.get(i);
                totalNum++;
                if (!title.matches("[A-Za-z0-9 \\_\\&\\(\\)\\（\\）\\|\\/\\'\\-\\@\\u4e00-\\u9fa5]+")) {
                    // 属性包含非法字符
                    failNum++;
                    cellMessage.setErrorDesc("属性[" + title + "]包含非法字符，仅支持 _&()（）|/'-@");
                    cellMessages.add(cellMessage);
                    continue;
                }
                if (proNames.contains(title)) {
                    // 已经存在该属性
                    failNum++;
                    cellMessage.setErrorDesc("属性【" + title + "】已存在");
                    cellMessages.add(cellMessage);
                    continue;
                }
                if (saves.contains(title)) {
                    // 保存的属性重复
                    ignoreNum++;
                    cellMessage.setErrorDesc("属性【" + title + "】重复");
                    cellMessages.add(cellMessage);
                    continue;
                }
                if (title.trim().length() > 50) {
                    // 名称太长
                    failNum++;
                    cellMessage.setErrorDesc("属性名称超过50字符");
                    cellMessages.add(cellMessage);
                    continue;
                }
                Assert.isTrue(!(majorAttrs.contains(title) && hasData), "分类下已有数据不允许新增业务主键");
                Assert.isTrue(!(requireAttrs.contains(title) && hasData), "分类下已有数据不允许新增必填属性");
                // 组建对象
                CcCiAttrDef attrDef = new CcCiAttrDef();
                attrDef.setDomainId(domainId);
                // attrDef.setId(ESUtil.getUUID());
                attrDef.setProName(title);
                attrDef.setProStdName(title.toUpperCase());
                attrDef.setClassId(classId);
                attrDef.setProType(3);
                attrDef.setIsMajor(0);
                attrDef.setIsRequired(0);
                if (majorAttrs.contains(title)) {
                    attrDef.setIsMajor(1);
                    attrDef.setIsRequired(1);
                }
                if (requireAttrs.contains(title)) {
                    attrDef.setIsRequired(1);
                }
                attrDef.setIsCiDisp(0);
                attrDef.setCreateTime(ESUtil.getNumberDateTime());
                attrDef.setOrderNo(startOrderNo + i + 1);
                newDefs.add(attrDef);
                saves.add(title);
                insertNum++;
            }
            // 校验主键数量
            defs.addAll(newDefs);
            List<CcCiAttrDef> majorDefs = defs.stream().filter(def -> (def.getIsMajor() != null && def.getIsMajor().longValue() == 1)).collect(Collectors.toList());
            Assert.isTrue(!BinaryUtils.isEmpty(majorDefs), "缺少业务主键");
            Assert.isTrue(majorDefs.size() <= primaryKeyCount, "主键数量超过最大限制:" + primaryKeyCount);
            if (!BinaryUtils.isEmpty(cellMessages)) {
                ImportRowMessage rowMessage = new ImportRowMessage();
                rowMessage.setRowNum(1);
                rowMessage.setMessageItems(cellMessages);
                List<ImportRowMessage> rowMessages = new ArrayList<ImportRowMessage>();
                rowMessages.add(rowMessage);
                sheetMessage.setRowMessages(rowMessages);
            }
            attrDefs.addAll(newDefs);
            esciClassInfo.setCcAttrDefs(attrDefs);
            sheetMessage.setTotalNum(totalNum);
            sheetMessage.setFailNum(failNum);
            sheetMessage.setIgnoreNum(ignoreNum);
            sheetMessage.setInsertNum(insertNum);
            sheetMessage.setUpdateNum(saves.size() - insertNum);
            if (saves.size() > 0) {
                // 保存该分类属性
                Long saveDef = esciClassSvc.saveOrUpdate(esciClassInfo);
                if (saveDef != 0L) {
                    sheetMessage.setSuccessNum(saves.size());
                } else {
                    sheetMessage.setSuccessNum(0);
                    sheetMessage.setFailNum(failNum + saves.size());
                    sheetMessage.setInsertNum(0);
                    sheetMessage.setUpdateNum(0);
                }
            }
        } catch (Exception e) {
            throw BinaryUtils.transException(e, ServiceException.class);
        } finally {
            try {
                if (is != null) {
                    is.close();
                }
            } catch (Exception e2) {
            }
        }
        List<ImportSheetMessage> sheetMessages = new ArrayList<>();
        sheetMessages.add(sheetMessage);
        result = FileUtil.ExcelUtil.writeSheetMessageToFile(sheetMessages, "分类属性导入明细");
        return result;
    }

    /**
     * 导出分类模板
     *
     * @return
     */
    @Override
    public ResponseEntity<byte[]> exportClassAttrExcel(Set<Long> clsIds, Boolean isBatchTpl) {
        ResponseEntity<byte[]> entity = null;
        InputStream is = null;
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        Workbook swb = null;
        String fileName = null;
        if (isBatchTpl == null) {
            isBatchTpl = false;
        }
        try {
            if (isBatchTpl) {
                fileName = FileUtil.ExcelUtil.getExportFileName("批量导入对象模板", CommUtil.EXCEL07_XLSX_EXTENSION, true);
                String templateName = "/static_res/ci_new_import_template.xlsx";
                is = this.getClass().getResourceAsStream(templateName);
            } else {
                is = this.getClass().getResourceAsStream("/static_res/ci_attr_template.xlsx");
            }
            swb = new XSSFWorkbook(is);
        } catch (Exception e) {
            log.error("模板文件获取失败");
        }
        if (!isBatchTpl) {
            if (clsIds != null && clsIds.size() > 0) {
                fileName = FileUtil.ExcelUtil.getExportFileName("分类属性", CommUtil.EXCEL07_XLSX_EXTENSION, true);
                List<ESCIClassInfo> clsInfos = esciClassSvc.getListByQuery(QueryBuilders.termsQuery("id", clsIds));
                Assert.isTrue(clsInfos.size() == clsIds.size(), "未查询到指定分类");
                this.exportCIClassDef(swb, clsInfos);
                for (ESCIClassInfo ciClass : clsInfos) {
                    Sheet sheet = swb.createSheet(ciClass.getClassCode());
                    Row titleRow = sheet.createRow(0);
                    Cell codeCell = titleRow.createCell(0);
                    codeCell.setCellValue(SysUtil.StaticUtil.CICODE_LABEL);
                    codeCell.setCellStyle(getCICodeCellStyle(swb));
                    if (ciClass.getCcAttrDefs() != null && ciClass.getCcAttrDefs().size() > 0) {
                        for (int titIndex = 0; titIndex < ciClass.getCcAttrDefs().size(); titIndex++) {
                            CcCiAttrDef attrDef = ciClass.getCcAttrDefs().get(titIndex);
                            Cell titleCell = titleRow.createCell(titIndex + 1);
                            titleCell.setCellValue(attrDef.getProName());
                            boolean major = (attrDef.getIsMajor() != null && attrDef.getIsMajor() == 1);
                            boolean required = (attrDef.getIsRequired() != null && attrDef.getIsRequired() == 1);
                            titleCell = FileUtil.ExcelUtil.setCellMark(swb, titleCell, SysUtil.StaticUtil.CICLS_MAJOR_MARK, major, required);
                        }
                    }
                    FileUtil.ExcelUtil.setAutoColumnSizeByRow(sheet, titleRow);
                }
            } else {
                fileName = FileUtil.ExcelUtil.getExportFileName("分类属性模板", CommUtil.EXCEL07_XLSX_EXTENSION, false);
                Sheet sheet = swb.createSheet("领域名称&分类名称");
                Row titleRow = sheet.createRow(0);
                Cell titleCell = titleRow.createCell(0);
                titleCell.setCellValue(SysUtil.StaticUtil.CICODE_LABEL);
                titleCell.setCellStyle(this.getCICodeCellStyle(swb));
                titleCell = titleRow.createCell(1);
                titleCell.setCellValue("属性名称1");
                titleCell = FileUtil.ExcelUtil.setCellMark(swb, titleCell, SysUtil.StaticUtil.CICLS_MAJOR_MARK, true, true);
                titleCell = titleRow.createCell(2);
                titleCell.setCellValue("属性名称2");
                titleCell = FileUtil.ExcelUtil.setCellMark(swb, titleCell, SysUtil.StaticUtil.CICLS_MAJOR_MARK, false, false);
                FileUtil.ExcelUtil.setAutoColumnSizeByRow(sheet, titleRow);
            }
        }
        try {
            swb.write(os);
            HttpHeaders headers = new HttpHeaders();
            byte[] bytes = os.toByteArray();
            headers.add("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            entity = new ResponseEntity<byte[]>(bytes, headers, HttpStatus.OK);
        } catch (Exception e) {
            log.error("模板文件创建失败");
        } finally {
            try {
                if (os != null) {
                    os.close();
                }
                if (is != null) {
                    is.close();
                }
                if (swb != null) {
                    swb.close();
                }
            } catch (Exception e2) {
            }
        }
        return entity;
    }

    @Override
    public Integer deleteById(Long id) {
        ESCIClassInfo classInfo = esciClassSvc.getById(id);
        if (classInfo == null) {
            //不存在此分类，不需要删除
            return 1;
        }
        List<ESCIClassInfo> childs = esciClassSvc.getListByQuery(QueryBuilders.boolQuery().must(QueryBuilders.termQuery("parentId", id)));
        List<String> childNames = childs.stream().map(ESCIClassInfo::getClassName).collect(Collectors.toList());
        if (!BinaryUtils.isEmpty(childs)) {
            throw MessageException.i18n("BS_CLASS_DELETE_EXIST_SUB_CLASS", "{\"field\":\"" + StringUtils.join(childNames, ",") + "\"}");
        }
        Map<Object, Long> countMap = esCiSvc.countCIByQuery(QueryBuilders.termQuery("classId", id));
        Long count = countMap.get(id);
        if (count != null && count > 0) {
            throw MessageException.i18n("BS_CC_CLASS_HAS_DATA");
        }
        CSysRoleDataModuleRlt cdt = new CSysRoleDataModuleRlt();
        cdt.setDataValueEqual(id+"");
        cdt.setDomainId(classInfo.getDomainId());
        roleSvc.deleteRoleDataModuleRlt(cdt);
        attrTransConfigSvc.deleteByQuery(QueryBuilders.termQuery("classId", id), false);
        ciHistorySvc.deleteByQuery(QueryBuilders.termQuery("classId", id), false);
        return esciClassSvc.deleteById(id);
    }

    public void validAndBuild(ESCIClassInfo esClsInfo) {
        Assert.isTrue(!BinaryUtils.isEmpty(esClsInfo.getClassName().trim()), "分类名称不能为空");
        Assert.isTrue(esClsInfo.getClassName().matches("[^\\:\\/\\?\\*\\[\\]\\\\]{1,30}"), "分类名称不可超过30位，且不可包含:/\\?*[]等字符");
        SysUser loginUser = null;
        try {
            loginUser = SysUtil.getCurrentUserInfo();
        } catch (Exception e) {
        }
        Long domainId = loginUser == null ? BaseConst.DEFAULT_DOMAIN_ID : loginUser.getDomainId();
        // 丰富分类信息
        if (esClsInfo.getId() == null) {
            esClsInfo.setId(ESUtil.getUUID());
            esClsInfo.setParentId(esClsInfo.getParentId() == null ? 0 : esClsInfo.getParentId());
            esClsInfo.setCiType(1);
            esClsInfo.setDataStatus(1);
            esClsInfo.setIsLeaf(BinaryUtils.isEmpty(esClsInfo.getIsLeaf()) ? 1 : esClsInfo.getIsLeaf());
            esClsInfo.setDomainId(domainId);
            esClsInfo.setClassLvl(BinaryUtils.isEmpty(esClsInfo.getClassLvl()) ? 1 : esClsInfo.getClassLvl());
            esClsInfo.setCreator(loginUser == null ? "system" : loginUser.getLoginCode());
            esClsInfo.setCreateTime(ESUtil.getNumberDateTime());
            // dtClassId默认与id相同
            esClsInfo.setDtClassId(esClsInfo.getDtClassId() == null ? String.valueOf(esClsInfo.getId()) : esClsInfo.getDtClassId());
        }
        esClsInfo.setClassPath("#" + esClsInfo.getId() + "#");
        if (esClsInfo.getParentId() != null && esClsInfo.getParentId() != 0) {
            esClsInfo.setClassLvl(2);
            esClsInfo.setClassPath("#" + esClsInfo.getParentId() + esClsInfo.getClassPath());
        } else {
            // 非子分类，属性不可为空
            Assert.notEmpty(esClsInfo.getCcAttrDefs(), "属性定义不能为空");
        }
        esClsInfo.setClassStdCode(esClsInfo.getClassCode().trim().toUpperCase());
        esClsInfo.setClassName(esClsInfo.getClassName().trim());
        esClsInfo.setClassStdName(esClsInfo.getClassName().toUpperCase());
        esClsInfo.setModifier(loginUser == null ? "system" : loginUser.getLoginCode());
        // 丰富icon信息
        String icon = BinaryUtils.isEmpty(esClsInfo.getIcon()) ? esciClassSvc.getDefaultIcon() : esClsInfo.getIcon();
        if (!BinaryUtils.isEmpty(icon) && icon.startsWith(rsmSlaveRoot)) {
            icon = icon.substring(rsmSlaveRoot.length(), icon.length());
        }
        esClsInfo.setIcon(icon);
        List<CcCiAttrDef> attrDefs = esClsInfo.getCcAttrDefs();
        if (!BinaryUtils.isEmpty(attrDefs)) {
            // 校验主键数量
            List<CcCiAttrDef> majorDefs = attrDefs.stream().filter(def -> (def.getIsMajor() != null && def.getIsMajor().longValue() == 1)).collect(Collectors.toList());
            Assert.isTrue(esClsInfo.getParentId() == 0 || majorDefs.size() <= 0, "子分类不允许设置主键");
            if (esClsInfo.getParentId() == null || esClsInfo.getParentId() == 0) {
                Assert.isTrue(!BinaryUtils.isEmpty(majorDefs), "缺少业务主键");
            }
            Assert.isTrue(majorDefs.size() <= primaryKeyCount, "主键数量超过最大限制:" + primaryKeyCount);
            majorDefs.forEach(majorDef -> {
                Assert.isTrue(ESPropertyType.LONG_VARCHAR.getValue() != majorDef.getProType()
                                && ESPropertyType.CLOB.getValue() != majorDef.getProType()
                                && ESPropertyType.PICTURE.getValue() != majorDef.getProType()
                                && ESPropertyType.EXTERNAL_ATTR.getValue() != majorDef.getProType()
                                && ESPropertyType.DICT.getValue() != majorDef.getProType()
                                && ESPropertyType.DOCUMENT.getValue() != majorDef.getProType()
                        , "文本、文章、图片类型、数据字典、关联属性、文档不可作为主键");
            });
            // 丰富属性信息
            for (CcCiAttrDef attrDef : attrDefs) {
                Assert.isTrue(!BinaryUtils.isEmpty(attrDef.getProName().trim()), "属性名称不得为空");
                Assert.isTrue(attrDef.getProName().trim().matches("^[A-Za-z0-9\\u4e00-\\u9fa5 @\\（\\）\\(\\)\\&\\-\\|\\/\\\\'_]+$"), "属性[" + attrDef.getProName() + "]包含非法字符，仅支持 _&()（）|/\\'-@");
                // 验证枚举值定义是否符合规范
                if (AttrNameKeyEnum.ENUM.getType() == attrDef.getProType()) {
                    Assert.isTrue(!BinaryUtils.isEmpty(attrDef.getEnumValues()), "枚举型类型约束不可为空");
                    List<String> enumValues = JSONArray.parseArray(attrDef.getEnumValues(), String.class);
                    for (String enumVal : enumValues) {
                        Integer checkResult = CheckAttrUtil.validateAttrValType(attrDef, enumVal);
                        switch (checkResult) {
                            case CheckAttrUtil.OVER_LENGTH:
                                Assert.isTrue(false, "枚举值[" + enumVal + "]超过类型限定长度");
                            case CheckAttrUtil.FORMAT_ERROR:
                                Assert.isTrue(false, "枚举值[" + enumVal + "]格式错误");
                            default:
                                break;
                        }
                    }
                } else if (AttrNameKeyEnum.DICT.getType() == attrDef.getProType()) {
                    Assert.isTrue(!BinaryUtils.isEmpty(attrDef.getProDropSourceDef()), "数据字典类型约束不可为空");
                    try {
                        @SuppressWarnings("unchecked")
                        Long dictClassId = CiClassProDropSourceDefHelper.getCiClassProDropSourceClassId(attrDef.getProDropSourceDef().trim());
                        Long[] dictDefIds = CiClassProDropSourceDefHelper.getCiClassProDropSourceDefIds(attrDef.getProDropSourceDef().trim());
                        ESDictionaryItemSearchBean bean = ESDictionaryItemSearchBean.builder().dictClassId(dictClassId).build();
                        bean.setDomainId(domainId);
                        List<ESDictionaryClassInfo> dictClsList = dictSvc.queryDcitClassInfosByBean(bean);
                        Assert.notEmpty(dictClsList, "属性[" + attrDef.getProName().toUpperCase() + "]引用不合法");
                        Map<Long, Integer> dictDefMap = dictClsList.get(0).getDictAttrDefs().stream().collect(Collectors.toMap(ESDictionaryAttrDef::getId, ESDictionaryAttrDef::getProType));
                        for (Long dictDefId : dictDefIds) {
                            Integer dictType = dictDefMap.get(dictDefId);
                            // 引用类型不能是文件或其他数据字典
                            Assert.isTrue(dictType != null && dictType != ESDictionaryAttrDef.DictProTypeEnum.EXTERNAL_DICT.getType()
                                    && dictType != ESDictionaryAttrDef.DictProTypeEnum.FILE.getType(), "属性[" + attrDef.getProName().toUpperCase() + "]引用不合法");
                        }
                    } catch (Exception e) {
                        Assert.isTrue(false, "属性[" + attrDef.getProName().toUpperCase() + "]引用不合法");
                    }
                } else if (AttrNameKeyEnum.MODEL.getType() == attrDef.getProType()) {
                    // 3D model verification
                    int isRequired = attrDef.getIsRequired();
                    Assert.isTrue(isRequired == 1, "3D模型类型应为必填项目");
                    String defaulfModel = attrDef.getDefVal();
                    if (defaulfModel == null || defaulfModel.trim().length() == 0) {
                        defaulfModel = esciClassSvc.getDefault3dModel();
                    }
                    Assert.isTrue(defaulfModel.matches(IMG_NAME_REGEX), "不支持的图标类型");
                    if (defaulfModel.startsWith(this.rsmSlaveRoot)) {
                        defaulfModel = defaulfModel.substring(rsmSlaveRoot.length());
                    }
                    esClsInfo.setModelIcon3D(defaulfModel);
                    attrDef.setDefVal(defaulfModel);
                } else if (AttrNameKeyEnum.PICTURE.getType() == attrDef.getProType()) {
                    Assert.isTrue(attrDef.getIsCiDisp() == null || attrDef.getIsCiDisp() == 0,
                            "图片类型不可作为Label");
                    String pictureUri = attrDef.getDefVal();
                    if (pictureUri == null || pictureUri.trim().length() == 0) {
                        pictureUri = esciClassSvc.getDefaultPictureIcon();
                    }
                    Assert.isTrue(pictureUri.matches(IMG_NAME_REGEX), "不支持的图标类型");
                    if (pictureUri.startsWith(rsmSlaveRoot)) {
                        pictureUri = pictureUri.substring(rsmSlaveRoot.length());
                    }
                    attrDef.setDefVal(pictureUri);
                } else if (AttrNameKeyEnum.DOCUMENT.getType() == attrDef.getProType()) {
                    Assert.isTrue(attrDef.getIsCiDisp() == null || attrDef.getIsCiDisp() == 0,
                            "文档类型不可作为Label");
                } else if (AttrNameKeyEnum.EXTERNAL_ATTR.getType() == attrDef.getProType()) {
                    Assert.isTrue(!BinaryUtils.isEmpty(attrDef.getProDropSourceDef()), "关联属性类型约束不可为空");
                    try {
                        @SuppressWarnings("unchecked")
                        Long externalCiClassId = CiClassProDropSourceDefHelper.getCiClassProDropSourceClassId(attrDef.getProDropSourceDef().trim());
                        Long[] externalCiDefIds = CiClassProDropSourceDefHelper.getCiClassProDropSourceDefIds(attrDef.getProDropSourceDef().trim());

                        ESCIClassSearchBean clsBean = new ESCIClassSearchBean();
                        CCcCiClass cdt = new CCcCiClass();
                        cdt.setId(externalCiClassId);
                        cdt.setDomainId(domainId);
                        clsBean.setCdt(cdt);
                        List<ESCIClassInfo> esciClassInfoList = this.queryESCiClassInfoListBySearchBean(clsBean);
                        Assert.notEmpty(esciClassInfoList, "属性[" + attrDef.getProName().toUpperCase() + "]引用不合法");
                        Map<Long, Integer> attrDefMap = esciClassInfoList.get(0).getCcAttrDefs().stream().collect(Collectors.toMap(CcCiAttrDef::getId, CcCiAttrDef::getProType));
                        for (Long externalCiDefId : externalCiDefIds) {
                            Integer proType = attrDefMap.get(externalCiDefId);
                            Assert.isTrue(proType != null && proType != ESPropertyType.EXTERNAL_ATTR.getValue()
                                            && proType != ESPropertyType.MODEL.getValue() && proType != ESPropertyType.PICTURE.getValue()
                                            && proType != ESPropertyType.DOCUMENT.getValue(),
                                    "属性[" + attrDef.getProName().toUpperCase() + "]引用不合法");
                        }
                    } catch (Exception e) {
                        Assert.isTrue(false, "属性[" + attrDef.getProName().toUpperCase() + "]引用不合法");
                    }
                }

                Integer checkResult = CheckAttrUtil.validateAttrValType(attrDef, attrDef.getDefVal());
                switch (checkResult) {
                    case CheckAttrUtil.OVER_LENGTH:
                        Assert.isTrue(false, "属性[" + attrDef.getProName() + "]默认值超过类型限定长度");
                    case CheckAttrUtil.FORMAT_ERROR:
                        Assert.isTrue(false, "属性[" + attrDef.getProName() + "]默认值格式错误");
                    default:
                        break;
                }
                attrDef.setDomainId(domainId);
                attrDef.setDataStatus(1);
                attrDef.setCreateTime(ESUtil.getNumberDateTime());
                attrDef.setProStdName(attrDef.getProName().trim().toUpperCase());
                // attrDef.setOrderNo(attrDefs.indexOf(attrDef) + 1);
                attrDef.setIsMajor(attrDef.getIsMajor() == null || attrDef.getIsMajor() != 1 ? 0 : 1);
                Integer isRequired = attrDef.getIsMajor() == 1 ? 1 : (attrDef.getIsRequired() == null || attrDef.getIsRequired() != 1 ? 0 : 1);
                attrDef.setIsRequired(isRequired);
                attrDef.setIsCiDisp(attrDef.getIsCiDisp() == null || attrDef.getIsCiDisp() != 1 ? 0 : 1);
                attrDef.setProType(attrDef.getProType() == null ? 3 : attrDef.getProType());
            }
        }
    }

    @Override
    public List<String> queryAttrDefGroupList(Long domainId) {
        ESCIClassSearchBean esciClassSearchBean = new ESCIClassSearchBean();
        CCcCiClass cdt = new CCcCiClass();
        cdt.setDomainId(domainId);
        esciClassSearchBean.setCdt(cdt);
        List<ESCIClassInfo> classInfos = this.queryESCiClassInfoListBySearchBean(esciClassSearchBean);
        List<String> userClassTags = classInfos.stream()
                .flatMap(cls -> cls.getAttrDefs().stream().map(ESCIAttrDefInfo::getGroup))
                .filter(groupList -> !BinaryUtils.isEmpty(groupList))
                .flatMap(group -> group.stream().map(String::trim)).distinct()
                .collect(Collectors.toList());
        return new ArrayList<>(org.apache.commons.collections4.CollectionUtils.union(userClassTags, DEFAULT_TAGS));
    }

    /**
     * 平铺转树结构
     *
     * @param dir     领域信息
     * @param clsList 属于该领域下的所有分类
     * @return
     */
    private void classListToTree(ClassNodeInfo dir, List<CcCiClass> clsList, Map<String, Long> clsCiCountMap) {
        // 虚拟一个根节点
        Long rootNodeId = 0L;
        ClassNodeInfo tree = ClassNodeInfo.builder().id(rootNodeId).build();
        if (clsList == null) {
            clsList = new LinkedList<>();
        }
        // parentId:[childInfos]字典
        Map<Long, Set<CcCiClass>> parentIdChildsDict = new LinkedHashMap<>();
        clsList.forEach(cls -> {
            if (parentIdChildsDict.get(cls.getParentId()) == null) {
                parentIdChildsDict.put(cls.getParentId(), new LinkedHashSet<>());
            }
            parentIdChildsDict.get(cls.getParentId()).add(cls);
        });
        // 平铺列表转换tree结构
        this.listToTree(tree, parentIdChildsDict, clsCiCountMap);
        dir.setChildren(tree.getChildren());
    }

    private void listToTree(ClassNodeInfo node, Map<Long, Set<CcCiClass>> parToChildMap, Map<String, Long> clsCiCountMap) {
        long parentNodeId = node.getId();
        Set<CcCiClass> childs = parToChildMap.get(parentNodeId);
        if (!BinaryUtils.isEmpty(childs)) {
            childs.forEach(child -> {
                node.getChildren()
                        .add(ClassNodeInfo.builder().id(child.getId()).name(child.getClassName()).type((parentNodeId == 0 ? "class" : "sub_class")).icon(child.getIcon())
                                .count(clsCiCountMap.get(String.valueOf(child.getId())) == null ? 0L : clsCiCountMap.get(String.valueOf(child.getId()))).shape(child.getShape())
                                .orderNo(child.getOrderNo()).classCode(child.getClassCode()).isSubClass(child.getParentId() == 0 ? false : true)
                                .build());
            });
            node.getChildren().forEach((nestNode) -> {
                listToTree(nestNode, parToChildMap, clsCiCountMap);
            });
        }
    }

    /**
     * 把HSSFCell转为String
     */
    private String getValue(Cell hssfCell) {
        if (BinaryUtils.isEmpty(hssfCell)) {
            return "";
        }
        if (hssfCell.getCellType() == CellType.BOOLEAN) {
            return String.valueOf(hssfCell.getBooleanCellValue());
        } else if (hssfCell.getCellType() == CellType.NUMERIC) {
            String value = String.valueOf(hssfCell.getNumericCellValue());
            value = value.substring(0, value.lastIndexOf('.'));
            return value;
        } else {
            return String.valueOf(hssfCell.getStringCellValue());
        }
    }

    private CellStyle getCICodeCellStyle(Workbook swb) {
        Font cellFont = swb.createFont();
        cellFont.setFontName("微软雅黑");
        cellFont.setFontHeightInPoints((short) 14);
        cellFont.setBold(true);
        cellFont.setColor(IndexedColors.WHITE.index);
        CellStyle cellStyle = swb.createCellStyle();
        cellStyle.setFont(cellFont);
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        cellStyle.setFillForegroundColor(IndexedColors.RED.index);
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        return cellStyle;
    }

    public void exportCIClassDef(Workbook swb, List<ESCIClassInfo> ciClassInfos) {
        if (!BinaryUtils.isEmpty(ciClassInfos)) {
            // 获取分类所属领域信息
            Set<Long> dirIds = ciClassInfos.stream().filter(cls -> cls.getDirId() != null).map(ESCIClassInfo::getDirId).collect(Collectors.toSet());
            List<CcCiClassDir> dirs = esDirSvc.getListByQuery(QueryBuilders.termsQuery("id", dirIds));
            Map<Long, CcCiClassDir> dirMap = BinaryUtils.toObjectMap(dirs, "id");
            // 获取分类图标信息
            List<CcCiClassDir> imgDirs = esDirSvc.getListByQuery(QueryBuilders.termQuery("ciType", 3));
            Map<Long, CcCiClassDir> imgDirMap = BinaryUtils.toObjectMap(imgDirs, "id");
            Set<String> icons = ciClassInfos.stream().filter(cls -> !BinaryUtils.isEmpty(cls.getIcon())).map(ESCIClassInfo::getIcon).collect(Collectors.toSet());
            List<CcImage> images = esImageSvc.getListByQuery(QueryBuilders.termsQuery("imgPath.keyword", icons));
            Map<String, CcImage> imgMap = BinaryUtils.toObjectMap(images, "imgPath");
            List<ESCIClassInfo> clsList = esciClassSvc.getListByQuery(QueryBuilders.termQuery("parentId", 0L));
            Map<Long, ESCIClassInfo> clsMap = BinaryUtils.toObjectMap(clsList, "id");
            String clsDefKey = LanguageResolver.trans("BS_CI_CLASS_EXPORT_CLASS_DEFINITION");
            String dirNameKey = LanguageResolver.trans("BS_CI_CLASS_EXPORT_DIR_NAME");
            String clsNameKey = LanguageResolver.trans("BS_CI_CLASS_EXPORT_CLASS_NAME");
            String clsCodeKey = "分类标识";
            String parNameKey = LanguageResolver.trans("BS_CI_CLASS_EXPORT_PARENT_CLASS_NAME");
            String imgNameKey = LanguageResolver.trans("BS_CI_CLASS_EXPORT_CLASS_IMAGE_NAME");
            String attrNameKey = LanguageResolver.trans("BS_CI_CLASS_EXPORT_ATTR_NAME");
            String attrTypeKey = LanguageResolver.trans("BS_CI_CLASS_EXPORT_ATTR_TYPE");
            String isRequiredKey = LanguageResolver.trans("BS_CI_CLASS_EXPORT_IS_REQUIRED");
            String isLabelKey = LanguageResolver.trans("BS_CI_CLASS_EXPORT_IS_LABEL");
            String isMajorKey = LanguageResolver.trans("BS_CI_CLASS_EXPORT_IS_MAJOR");
            String defValKey = LanguageResolver.trans("BS_CI_CLASS_EXPORT_ATTR_DEFVALUE");
            String attrTag = LanguageResolver.trans("BS_CI_CLASS_EXPORT_ATTR_TAG");
            String attrRuleKey = LanguageResolver.trans("BS_CI_CLASS_EXPORT_ATTR_TYPE_RULE");
            String attrDesc = "属性描述";
            // 创建类定义标题行
            List<String> title = Arrays.asList(dirNameKey, clsNameKey, clsCodeKey, parNameKey, imgNameKey, attrNameKey, attrTypeKey, isRequiredKey, isLabelKey, isMajorKey, defValKey, attrTag, attrRuleKey, attrDesc);
            List<String> reqCellValues = Arrays.asList(dirNameKey, clsNameKey, clsCodeKey, attrNameKey, attrTypeKey);
            Sheet sheet = FileUtil.ExcelUtil.createExcelSheetAndTitle(swb, clsDefKey, title, new HashSet<>(reqCellValues), null, null);
            // 组装类定义数据
            List<Map<String, String>> classDatas = new ArrayList<>();
            for (ESCIClassInfo classInfo : ciClassInfos) {
                CcCiClassDir dir = dirMap.get(classInfo.getDirId());
                // 避免脏数据
                if (dir == null) {
                    continue;
                }
                String dirName = dir.getDirName();
                String className = classInfo.getClassName();
                String classCode = classInfo.getClassCode();
                String imgName = null;
                CcImage img = imgMap.get(classInfo.getIcon());
                if (img != null) {
                    imgName = imgDirMap.get(img.getDirId()).getDirName() + "|" + img.getImgName();
                }
                String parClsName = null;
                if (classInfo.getParentId() != null && classInfo.getParentId().longValue() != 0L) {
                    ESCIClassInfo parCls = clsMap.get(classInfo.getParentId());
                    parClsName = parCls.getClassName();
                }
                for (ESCIAttrDefInfo def : classInfo.getAttrDefs()) {
                    Map<String, String> map = new HashMap<String, String>();
                    map.put(dirNameKey, dirName);
                    map.put(clsNameKey, className);
                    map.put(clsCodeKey, classCode);
                    map.put(parNameKey, parClsName);
                    map.put(imgNameKey, imgName);
                    map.put(attrNameKey, def.getProName());
                    map.put(attrTypeKey, AttrNameKeyEnum.valueOf(def.getProType()).getValue());
                    map.put(isRequiredKey, def.getIsRequired().intValue() == 1 ? "是" : "否");
                    map.put(isLabelKey, def.getIsCiDisp().intValue() == 1 ? "是" : "否");
                    map.put(isMajorKey, def.getIsMajor().intValue() == 1 ? "是" : "否");
                    map.put(defValKey, def.getDefVal());
                    map.put(attrDesc, def.getProDesc());
                    // add attribute tag when export ci class
                    String attrTagStr = "";
                    List<String> attrTagList = def.getGroup();
                    if (null != attrTagList && attrTagList.size() > 0) {
                        attrTagStr = StringUtils.join(attrTagList.toArray(), ",");
                    }
                    map.put(attrTag, attrTagStr);

                    map.put(attrRuleKey, def.getConstraintRule());
                    int proType = def.getProType();
                    if (proType == ESPropertyType.ENUM.getValue()) {
                        map.put(attrRuleKey, StringUtils.join(JSONArray.parseArray(def.getEnumValues()), ","));
                    } else if (proType == ESPropertyType.DICT.getValue()) {
                        //外部属性描述
                        Assert.notNull(def.getProDropSourceDef(), "X_PARAM_NOT_NULL${name:proDropSourceDef}");
                        @SuppressWarnings("unchecked")
                        //外部属性描述转成map类型:字典类型id:对应属性Id,对应属性Id
                        //分别得到对应的id值
                        Long dictClassId = CiClassProDropSourceDefHelper.getCiClassProDropSourceClassId(def.getProDropSourceDef().trim());
                        Long[] dictDefIds = CiClassProDropSourceDefHelper.getCiClassProDropSourceDefIds(def.getProDropSourceDef().trim());
                        //分别根据Id值去查询
                        ESDictionaryClassInfo dictClassInfo = dictSvc.getDictClassInfoById(dictClassId);
                        Assert.notEmpty(dictClassInfo.getDictAttrDefs(), "字典定义不存在");
                        Map<Long, ESDictionaryAttrDef> defMap = dictClassInfo.getDictAttrDefs()
                                .stream().collect(Collectors.toMap(ESDictionaryAttrDef::getId, e -> e));
                        List<String> dictValues = new ArrayList<>();
                        for (Long dictDefId : dictDefIds) {
                            ESDictionaryAttrDef d = defMap.get(dictDefId);
                            Assert.notNull(d, "字典定义不存在");
                            dictValues.add(d.getProName());
                        }
                        map.put(attrRuleKey, dictClassInfo.getDictName().concat(":")
                                .concat(String.join(CiClassProDropSourceDefHelper.EXPORT_DEF_SEPARATOR, dictValues)));
                    } else if (proType == ESPropertyType.EXTERNAL_ATTR.getValue()) {
                        //判断约束规则
                        Assert.notNull(def.getProDropSourceDef(), "X_PARAM_NOT_NULL${name:proDropSourceDef}");
                        Long ciClassId = CiClassProDropSourceDefHelper.getCiClassProDropSourceClassId(def.getProDropSourceDef().trim());
                        Long[] ciDefIds = CiClassProDropSourceDefHelper.getCiClassProDropSourceDefIds(def.getProDropSourceDef().trim());
                        ESCIClassInfo esciClassInfo = this.queryESClassInfoById(ciClassId);
                        Assert.notNull(esciClassInfo, "关联属性不存在");
                        Assert.notEmpty(esciClassInfo.getAttrDefs(), "关联属性不存在");
                        Map<Long, ESCIAttrDefInfo> attrDefInfoMap = esciClassInfo.getAttrDefs()
                                .stream().collect(Collectors.toMap(ESCIAttrDefInfo::getId, e -> e));
                        List<String> ciValues = new ArrayList<>();
                        for (Long ciDefId : ciDefIds) {
                            ESCIAttrDefInfo d = attrDefInfoMap.get(ciDefId);
                            Assert.notNull(d, "关联属性不存在");
                            ciValues.add(d.getProName());
                        }
                        map.put(attrRuleKey, esciClassInfo.getClassName().concat(":")
                                .concat(String.join(CiClassProDropSourceDefHelper.EXPORT_DEF_SEPARATOR, ciValues)));
                    } else if (proType == AttrNameKeyEnum.MODEL.getType() && !isShow3dAttribute) {
                        // The 3D model is closed, and the exported data does not contain 3D model attributes
                        continue;
                    }
                    classDatas.add(map);
                }
            }
            if (!BinaryUtils.isEmpty(classDatas)) {
                FileUtil.ExcelUtil.writeExcelComment(swb, sheet, 1, title, new HashSet<>(Arrays.asList(attrNameKey)), null, classDatas);
            }
        }
    }

    /**
     * 导入领域及类定义
     *
     * @param sheetVal   类定义值
     * @param classNames 属性定义与数据库不一致的分类名称
     *                   //     * @param overwriteData 是否更新存量数据
     * @return List<String>
     */
    public ImportSheetMessage importDirAndCIClass(Long domainId, List<String[]> sheetVal, List<String> classNames, boolean addAttr) {
        ImportSheetMessage clsSheetMessage = ImportSheetMessage.builder().build();
        List<ImportRowMessage> rowMessages = new ArrayList<>();
        clsSheetMessage.setRowMessages(rowMessages);
        if (BinaryUtils.isEmpty(sheetVal) || BinaryUtils.isEmpty(classNames)) {
            return clsSheetMessage;
        }
        int totalNum = 0, failNum = 0;
        // 要保存的领域集合
        List<CcCiClassDir> saveDirs = new ArrayList<>();
        // 获取所在领域及分类
        BoolQueryBuilder dirQuery = QueryBuilders.boolQuery();
        dirQuery.must(QueryBuilders.termQuery("domainId", domainId));
        dirQuery.must(QueryBuilders.termQuery("ciType", 1));
        List<CcCiClassDir> dirs = esDirSvc.getListByQuery(dirQuery);
        Map<String, CcCiClassDir> dirMap = BinaryUtils.toObjectMap(dirs, "dirName");
        classNames = classNames.stream().collect(Collectors.toList());
        // 验证类定义填写规范
        int rIdx = 1;
        List<ImportCIClassDataDto> ciClassDataDtos = new ArrayList<>();
        for (String[] rows : sheetVal) {
            rIdx++;
            // 忽略CI Code属性
            if (SysUtil.StaticUtil.CICODE_LABEL.equalsIgnoreCase(rows[5])) {
                continue;
            }
            ImportCIClassDataDto record = null;
            try {
                //2.7版本增加了属性tag，修改向下兼容
                record = ImportCIClassDataDto.builder().index(rIdx).dirName(rows[0]).className(rows[1]).classCode(rows[2])
                        .parentName(rows[3]).imgName(rows[4]).attrName(rows[5]).attrType(rows[6]).isRequired(rows[7])
                        .isLabel(rows[8]).isMajor(rows[9]).defValue(rows[10]).attrTag(rows[11]).attrTypeRule(rows[12]).attrDesc(rows[13]).build();

            } catch (Exception e) {
                Assert.isTrue(false, "格式有误，请下载导入模板重试！");
            }
            String className = record.getClassName();
            if (!BinaryUtils.isEmpty(className) && classNames.contains(className)) {
                try {
                    // 先校验领域
                    record.validDir();
                    if (!dirMap.containsKey(record.getDirName())) {
                        CcCiClassDir dir = new CcCiClassDir();
                        dir.setId(ESUtil.getUUID());
                        dir.setDomainId(domainId);
                        dir.setCiType(1);
                        dir.setDirName(record.getDirName());
                        dir.setParentId(0L);
                        saveDirs.add(dir);
                        dirMap.put(dir.getDirName(), dir);
                    }
                    // 验证类定义填写规范,此处校验了必填项与所填内容的合法性
                    record.valid();
                    ciClassDataDtos.add(record);
                } catch (Exception e) {
                    totalNum++;
                    failNum++;
                    rowMessages.add(this.buildRowMessage(rIdx, e.getMessage()));
                }
            }
        }
        // 获取所有图标OverwriteData
        BoolQueryBuilder ciClassDirQuery = QueryBuilders.boolQuery();
        ciClassDirQuery.must(QueryBuilders.termQuery("domainId", domainId));
        ciClassDirQuery.must(QueryBuilders.termQuery("ciType", 3));
        List<CcCiClassDir> imgDirs = esDirSvc.getListByQuery(ciClassDirQuery);
        List<CcImage> images = esImageSvc.getListByQuery(1, 9999, QueryBuilders.termQuery("domainId", domainId)).getData();
        Map<Long, List<CcImage>> imgMap = BinaryUtils.toObjectGroupMap(images, "dirId");
        Map<String, CcImage> dirImgMap = BinaryUtils.toObjectMap(images, "imgName");
        imgDirs.forEach(dir -> {
            List<CcImage> imgs = imgMap.get(dir.getId());
            if (!BinaryUtils.isEmpty(imgs)) {
                imgs.forEach(img -> dirImgMap.put(dir.getDirName() + "|" + img.getImgName(), img));
            }
        });
        // 获取所有分类
        List<ESCIClassInfo> clsList = esciClassSvc.getListByQuery(QueryBuilders.termQuery("domainId", domainId));
        Map<String, ESCIClassInfo> clsMap = new TreeMap<>(String.CASE_INSENSITIVE_ORDER);
        clsMap.putAll(BinaryUtils.toObjectMap(clsList, "className"));
        // 要保存的领分类集合
        List<ESCIClassInfo> saveCls = new ArrayList<>();
        // 按分类遍历,防止子类在上面定义找不到父类，子类数据后遍历
        Map<String, List<ImportCIClassDataDto>> childClsDefMap = new LinkedHashMap<>();
        Map<String, List<ImportCIClassDataDto>> clsDefMap = new TreeMap<>(String.CASE_INSENSITIVE_ORDER);
        for (ImportCIClassDataDto dto : ciClassDataDtos) {
            String className = dto.getClassName();
            if (classNames.contains(className)) {
                List<ImportCIClassDataDto> dtoList = clsDefMap.computeIfAbsent(className, k -> new ArrayList<>());
                dtoList.add(dto);
            }
        }
        clsSheetMessage.setTotalNum(totalNum + clsDefMap.size());
        // 校验每个分类所填领域、父类、图标是否一致，创建需要新建的领域及非子类分类，子类放在后面操作
        for (Entry<String, List<ImportCIClassDataDto>> entry : clsDefMap.entrySet()) {
            String className = entry.getKey();
            List<ImportCIClassDataDto> dtos = entry.getValue();
            Integer index = dtos.get(0).getIndex();
            if (!addAttr && clsMap.containsKey(className)) {
                failNum++;
                rowMessages.add(this.buildRowMessage(index, "分类[" + className + "]已存在"));
                continue;
            }
            Set<String> dirNames = dtos.stream().filter(e -> !BinaryUtils.isEmpty(e.getDirName())).map(ImportCIClassDataDto::getDirName).collect(Collectors.toSet());
            if (dirNames.size() > 1) {
                failNum++;
                rowMessages.add(this.buildRowMessage(index, "分类[" + className + "]领域填写不一致"));
                continue;
            }
            // 组装新建领域
            String dirName = new ArrayList<>(dirNames).get(0);
            CcCiClassDir classDir = dirMap.get(dirName);
            if (classDir == null) {
                failNum++;
                rowMessages.add(this.buildRowMessage(index, "分类[" + className + "]所属领域不存在"));
                continue;
            }
            Set<String> parNames = dtos.stream().filter(dto -> !BinaryUtils.isEmpty(dto.getParentName())).map(ImportCIClassDataDto::getParentName).collect(Collectors.toSet());
            // 创建新建的父级分类数据
            if (parNames.size() == 0) {
                ESCIClassInfo clsRecord = this.buildESCIClassInfoByDtos(domainId, dtos, clsSheetMessage, dirMap, clsMap, dirImgMap, addAttr);
                if (clsRecord == null) {
                    failNum++;
                    continue;
                }
                saveCls.add(clsRecord);
                clsMap.put(className, clsRecord);
            } else {
                // 子分类暂存，后面处理，避免找不到父类
                childClsDefMap.put(className, dtos);
            }
        }
        // 遍历子类数据
        for (Entry<String, List<ImportCIClassDataDto>> entry : childClsDefMap.entrySet()) {
            String className = entry.getKey();
            List<ImportCIClassDataDto> childDtos = entry.getValue();
            ESCIClassInfo clsRecord = this.buildESCIClassInfoByDtos(domainId, childDtos, clsSheetMessage, dirMap, clsMap, dirImgMap, addAttr);
            if (clsRecord == null) {
                failNum++;
                continue;
            }
            saveCls.add(clsRecord);
            clsMap.put(className, clsRecord);
        }
        if (!BinaryUtils.isEmpty(saveCls)) {
            //做classCode校验
            Set<String> classStdCodes = saveCls.stream().map(ESCIClassInfo::getClassStdCode).collect(Collectors.toSet());
            if(classStdCodes.size() < saveCls.size()){
                throw new BinaryException("分类标识重复");
            }
            Set<Long> classIdList = saveCls.stream().map(ESCIClassInfo::getId).collect(Collectors.toSet());
            BoolQueryBuilder clsCdt = QueryBuilders.boolQuery();
            clsCdt.must(QueryBuilders.termQuery("domainId", domainId));
            clsCdt.must(QueryBuilders.termsQuery("classStdCode.keyword", classStdCodes));
            if(!BinaryUtils.isEmpty(classIdList)){
                clsCdt.mustNot(QueryBuilders.termsQuery("id", classIdList));
            }
            List<ESCIClassInfo> exitClsStdCodes = esciClassSvc.getListByQuery(clsCdt);
            if(!BinaryUtils.isEmpty(exitClsStdCodes)){
                List<String> exitClassNames = new ArrayList<>();
                List<String> exitClassCodes = new ArrayList<>();
                for (ESCIClassInfo each : exitClsStdCodes) {
                    exitClassNames.add(each.getClassName());
                    exitClassCodes.add(each.getClassStdCode());
                }
                throw new BinaryException("导入失败:文件中分类标识与已存在分类" + exitClassNames + ",分类标识:" + exitClassCodes + ",冲突");
            }
        }
        if (!BinaryUtils.isEmpty(saveDirs)) {
            esDirSvc.saveOrUpdateBatch(saveDirs);
        }
        if (!BinaryUtils.isEmpty(saveCls)) {
            //初始化权限
            addAdminDataModuleRlt(domainId, saveCls);
            // 此处需重写批量保存方法，重复属性需在关联表中存记录
            esciClassSvc.saveOrUpdateBatch(saveCls);
        }
        clsSheetMessage.setSuccessNum(saveCls.size());
        clsSheetMessage.setInsertNum(saveCls.size());
        clsSheetMessage.setFailNum(failNum);
        return clsSheetMessage;
    }

    private ESCIClassInfo buildESCIClassInfoByDtos(Long domainId, List<ImportCIClassDataDto> dtos, ImportSheetMessage sheetMessage, Map<String, CcCiClassDir> dirMap, Map<String, ESCIClassInfo> clsMap, Map<String, CcImage> dirImgMap, boolean addAttr) {
        String className = dtos.get(0).getClassName();
        String clsCode = dtos.get(0).getClassCode();
        boolean emptyClassCode = false;
        if(BinaryUtils.isEmpty(clsCode)){
            emptyClassCode = true;
            clsCode = className;
        }
        Set<String> parNames = dtos.stream().filter(dto -> !BinaryUtils.isEmpty(dto.getParentName())).map(ImportCIClassDataDto::getParentName).collect(Collectors.toSet());
        int index = dtos.get(0).getIndex();
        if (parNames.size() > 1) {
            sheetMessage.getRowMessages().add(this.buildRowMessage(index, "分类[" + className + "]所属父类填写不一致"));
            return null;
        }
        String parentName = parNames.size() > 0 ? new ArrayList<>(parNames).get(0) : null;
        String dirName = dtos.get(0).getDirName();
        List<String> proNames = new ArrayList<>();
        CcCiClassDir classDir = dirMap.get(dirName);
        if (!BinaryUtils.isEmpty(parentName)) {
            ESCIClassInfo parCls = clsMap.get(parentName);
            if (parCls == null) {
                sheetMessage.getRowMessages().add(this.buildRowMessage(index, "分类[" + className + "]所属父类[" + parentName + "]不存在"));
                return null;
            }
            if (parCls.getParentId() != null && parCls.getParentId() != 0) {
                sheetMessage.getRowMessages().add(this.buildRowMessage(index, "至多可创建一级子类"));
                return null;
            }
            proNames.addAll(parCls.getCcAttrDefs().stream().map(CcCiAttrDef::getProStdName).collect(Collectors.toList()));
            // Assert.notNull(classDir, "所属领域不存在");
            if (classDir.getId().longValue() != parCls.getDirId().longValue()) {
                sheetMessage.getRowMessages().add(this.buildRowMessage(index, "子类[" + className + "]与父类[" + parentName + "]所属领域不一致"));
                return null;
            }
        }
        // 创建分类
        ESCIClassInfo clsRecord;
        List<CcCiAttrDef> defs;
        if (addAttr && clsMap.containsKey(className)) {
            ESCIClassInfo clsInfoTem = clsMap.get(className);
            proNames.addAll(clsInfoTem.getCcAttrDefs().stream().map(CcCiAttrDef::getProStdName).collect(Collectors.toList()));
            clsRecord = JSON.toJavaObject(JSON.parseObject(JSON.toJSONString(clsInfoTem)), ESCIClassInfo.class);
            defs = clsRecord.getCcAttrDefs();
        } else {
            clsRecord = new ESCIClassInfo();
            defs = new ArrayList<>();
        }
        clsRecord.setDomainId(domainId);
        clsRecord.setClassName(className);
        clsRecord.setClassCode(clsCode);
        clsRecord.setClassStdCode(clsCode.toUpperCase());
        clsRecord.setClassStdName(className.toUpperCase());
        clsRecord.setDirId(classDir.getId());
        clsRecord.setIcon(dtos.get(0).getImgName());
        if (parentName != null) {
            clsRecord.setParentId(clsMap.get(parentName).getId());
        }

        // 3D 属性校验前置准备
        Map<String, CcCiAttrDef> currentClassAttributes = new HashMap<>(32);
        for (int i = 0; i < dtos.size(); i++) {
            ImportCIClassDataDto dto = dtos.get(i);
            index = dto.getIndex();
            String defName = dto.getAttrName();
            if (proNames.contains(defName.toUpperCase())) {
                if (addAttr) {
                    continue;
                } else {
                    sheetMessage.getRowMessages().add(this.buildRowMessage(index, "属性[" + defName + "]已存在"));
                    return null;
                }
            }
            ESCIAttrDefInfo def = new ESCIAttrDefInfo();
            def.setProName(defName);
            Integer proType = AttrNameKeyEnum.checkAttrType(dto.getAttrType());
            if (proType == null) {
                sheetMessage.getRowMessages().add(this.buildRowMessage(index, "属性[" + defName + "]类型[" + dto.getAttrType() + "]违法"));
                return null;
            }
            def.setProType(proType);
            def.setIsMajor(dto.isMajor() ? 1 : 0);
            def.setIsCiDisp(dto.islabel() ? 1 : 0);
            def.setIsRequired(dto.isRequired() ? 1 : 0);
            def.setDefVal(dto.getDefValue());
            def.setProDesc(dto.getAttrDesc());
            // Rich attribute tags
            Set<String> attrTagSet = new HashSet<>();
            String attrTagStr = dto.getAttrTag();
            if (null != attrTagStr && !"".equals(attrTagStr.trim())) {
                attrTagStr = attrTagStr.replaceAll("\\s*", "");
                String[] attrTagList = attrTagStr.split(",");

                for (String singleTag : attrTagList) {
                    // tag should not be empty
                    if (!"".equals(singleTag)) {
                        // tag length should be less than 20
                        if (singleTag.length() > 20) {
                            sheetMessage.getRowMessages().add(this.buildRowMessage(index, "属性Tag长度不能超过20字符, 当前值 " + singleTag + " 超长"));
                            return null;
                        }
                        // tag can only contain special characters #_-|
                        Pattern p = Pattern.compile(TEXT_REGEX);
                        Matcher m = p.matcher(singleTag);
                        boolean regularCheck = m.find();
                        if (regularCheck) {
                            sheetMessage.getRowMessages().add(this.buildRowMessage(index, "属性tag仅可包含#_-|等特殊字符, 当前非法tag值为: " + singleTag));
                            return null;
                        }
                        attrTagSet.add(singleTag);
                    }
                }
                if (attrTagSet.size() > 0) {
                    def.setGroup(new ArrayList<>(attrTagSet));
                }
            }

            if (AttrNameKeyEnum.ENUM.getType() == proType) {
                if (BinaryUtils.isEmpty(dto.getAttrTypeRule())) {
                    sheetMessage.getRowMessages().add(this.buildRowMessage(index, "枚举型类型约束不可为空"));
                    return null;
                }
                List<String> enumValues = Arrays.asList(dto.getAttrTypeRule().split(","));
                if (BinaryUtils.isEmpty(enumValues)) {
                    sheetMessage.getRowMessages().add(this.buildRowMessage(index, "枚举型类型约束不可为空"));
                    return null;
                }
                long count = enumValues.stream().distinct().count();
                if (enumValues.size() > count) {
                    sheetMessage.getRowMessages().add(this.buildRowMessage(index, "枚举值[" + dto.getAttrTypeRule() + "]有重复项"));
                    return null;
                }
                def.setEnumValues(JSON.toJSONString(enumValues));
                for (String enumVal : enumValues) {
                    Integer checkResult = CheckAttrUtil.validateAttrValType(def, enumVal);
                    switch (checkResult) {
                        case CheckAttrUtil.OVER_LENGTH:
                            sheetMessage.getRowMessages().add(this.buildRowMessage(index, "枚举值[" + enumVal + "]超过类型限定长度"));
                            return null;
                        case CheckAttrUtil.FORMAT_ERROR:
                            sheetMessage.getRowMessages().add(this.buildRowMessage(index, "枚举值[" + enumVal + "]格式错误"));
                            return null;
                        default:
                            break;
                    }
                }
            } else if (AttrNameKeyEnum.DICT.getType() == proType) {
                if (BinaryUtils.isEmpty(dto.getAttrTypeRule())) {
                    sheetMessage.getRowMessages().add(this.buildRowMessage(index, "数据字典类型约束不可为空"));
                    return null;
                }
                String[] split = dto.getAttrTypeRule().split(":");
                if (split.length == 2) {
                    ESDictionaryItemSearchBean bean = ESDictionaryItemSearchBean.builder().domainId(domainId).dictName(split[0]).build();
                    List<ESDictionaryClassInfo> dictClsList = dictSvc.queryDcitClassInfosByBean(bean);
                    if (!BinaryUtils.isEmpty(dictClsList)) {
                        ESDictionaryClassInfo dictClassInfo = dictClsList.get(0);
                        Set<String> proNameSet = Stream.of(split[1].split(
                                CiClassProDropSourceDefHelper.IMPORT_DEF_SEPARATOR)).collect(Collectors.toSet());
                        List<String> attrDefIds = new ArrayList<>();
                        for (ESDictionaryAttrDef dictDef : dictClassInfo.getDictAttrDefs()) {
                            if (proNameSet.contains(dictDef.getProName())) {
                                attrDefIds.add(dictDef.getId().toString());
                            }
                        }
                        if (BinaryUtils.isEmpty(attrDefIds) || attrDefIds.size() != proNameSet.size()) {
                            sheetMessage.getRowMessages().add(this.buildRowMessage(index, "数据字典[" + split[1] + "]不存在"));
                            return null;
                        }
                        def.setProDropSourceDef("{" + dictClassInfo.getId() + ":" +
                                String.join(CiClassProDropSourceDefHelper.DEF_IDS_SEPARATOR, attrDefIds) + "}");
                    } else {
                        sheetMessage.getRowMessages().add(this.buildRowMessage(index, "数据字典[" + split[0] + "]不存在"));
                        return null;
                    }
                } else {
                    sheetMessage.getRowMessages().add(this.buildRowMessage(index, "数据字典类型约束格式错误"));
                    return null;
                }
            } else if (AttrNameKeyEnum.EXTERNAL_ATTR.getType() == proType) {
                if (BinaryUtils.isEmpty(dto.getAttrTypeRule())) {
                    sheetMessage.getRowMessages().add(this.buildRowMessage(index, "关联属性类型约束不可为空"));
                    return null;
                }
                String[] split = dto.getAttrTypeRule().split(":");

                if (split.length == 2) {
                    ESCIClassSearchBean clsBean = new ESCIClassSearchBean();
                    CCcCiClass cdt = new CCcCiClass();
                    //  cdt.setClassNameEqual(split[0]);
                    cdt.setDomainId(domainId);
                    cdt.setClassNameEqual(split[0]);
                    clsBean.setCdt(cdt);
                    List<ESCIClassInfo> esciClassInfoList = this.queryESCiClassInfoListBySearchBean(clsBean);

                    if (!BinaryUtils.isEmpty(esciClassInfoList)) {
                        ESCIClassInfo esciClassInfo = esciClassInfoList.get(0);
                        Set<String> proNameSet = Stream.of(split[1].split(
                                CiClassProDropSourceDefHelper.IMPORT_DEF_SEPARATOR)).collect(Collectors.toSet());
                        List<String> attrDefIds = new ArrayList<>();
                        for (ESCIAttrDefInfo esCiClassAttrDef : esciClassInfo.getAttrDefs()) {
                            if (proNameSet.contains(esCiClassAttrDef.getProName())) {
                                attrDefIds.add(esCiClassAttrDef.getId().toString());
                            }
                        }
                        if (BinaryUtils.isEmpty(attrDefIds) || attrDefIds.size() != proNameSet.size()) {
                            sheetMessage.getRowMessages().add(this.buildRowMessage(index, "关联属性[" + split[1] + "]不存在"));
                            return null;
                        }
                        def.setProDropSourceDef("{" + esciClassInfo.getId() + ":" +
                                String.join(CiClassProDropSourceDefHelper.DEF_IDS_SEPARATOR, attrDefIds) + "}");
                    } else {
                        sheetMessage.getRowMessages().add(this.buildRowMessage(index, "关联属性[" + split[0] + "]不存在"));
                        return null;
                    }
                } else {
                    sheetMessage.getRowMessages().add(this.buildRowMessage(index, "关联属性类型约束格式错误"));
                    return null;
                }
            }


            Integer checkResult = CheckAttrUtil.validateAttrValType(def, dto.getDefValue());
            switch (checkResult) {
                case CheckAttrUtil.OVER_LENGTH:
                    sheetMessage.getRowMessages().add(this.buildRowMessage(index, "属性[" + def.getProName() + "]默认值超过类型限定长度"));
                    return null;
                case CheckAttrUtil.FORMAT_ERROR:
                    sheetMessage.getRowMessages().add(this.buildRowMessage(index, "属性[" + def.getProName() + "]默认值格式错误"));
                    return null;
                default:
                    break;
            }
            String proName = defName.toUpperCase();
            proNames.add(proName);
            defs.add(def);
            currentClassAttributes.put(proName.trim(), def);
        }

        // 3D model attribute verification
        defs = this.importCiClassVerificationFor3dModel(currentClassAttributes, sheetMessage, index, defs);
        if (defs == null) {
            return null;
        }

        clsRecord.setCcAttrDefs(defs);
        try {
            this.validAndBuild(clsRecord);
        } catch (Exception e) {
            sheetMessage.getRowMessages().add(this.buildRowMessage(dtos.get(0).getIndex(), e.getMessage()));
            return null;
        }
        // 校验图标
        Map<String, ImportCIClassDataDto> imgNames =
                dtos.stream().filter(dto -> !BinaryUtils.isEmpty(dto.getImgName())).collect(Collectors.toMap(ImportCIClassDataDto::getImgName, Function.identity(), (e1, e2) -> e1));
        String imgPath = esciClassSvc.getDefaultIcon();
        // 导入明细中注明图标提示
        String errMsg = null;
        Integer errIndex = dtos.get(0).getIndex();
        if (imgNames.size() > 1) {
            errMsg = "分类[" + className + "]图标填写不一致,已使用默认图标替代";
            errIndex = new ArrayList<>(imgNames.values()).get(1).getIndex();
        } else if (imgNames.size() == 0) {
            errMsg = "分类[" + className + "]图标未填写,已使用默认图标替代";
        } else {
            String imgName = new ArrayList<>(imgNames.keySet()).get(0);
            clsRecord.setShape(imgName);
            CcImage image = dirImgMap.get(imgName);
            if (image == null) {
                errMsg = "分类[" + className + "]图标不存在,已使用默认图标替代";
            } else {
                imgPath = image.getImgPath();
            }
        }
        clsRecord.setIcon(imgPath);
        ESCIClassInfo clsInfo = clsMap.get(className);
        if (clsInfo != null) {
            boolean dirEqual = clsInfo.getDirId().longValue() == clsRecord.getDirId().longValue();
            boolean clsCodeEqual = clsInfo.getClassStdCode().equals(clsRecord.getClassStdCode());
            boolean parEqual = clsInfo.getParentId().longValue() == clsRecord.getParentId().longValue();
            boolean imgEqual = clsInfo.getIcon().equals(clsRecord.getIcon());
            // 若分类存在，类定义必须全等，若不全等则不允许导入
            boolean clsEqual = this.equalAttrDefs(clsInfo.getCcAttrDefs(), defs);
            if(!emptyClassCode && !clsCodeEqual){
                sheetMessage.getRowMessages().add(this.buildRowMessage(dtos.get(0).getIndex(), "分类[" + className + "]分类标识与存量数据不一致"));
                return null;
            }
            if (!(dirEqual && parEqual && imgEqual && clsEqual) && !addAttr) {
                // notEqualCls.add(className);
                sheetMessage.getRowMessages().add(this.buildRowMessage(dtos.get(0).getIndex(), "分类[" + className + "]类定义与存量数据不一致"));
                return null;
            } else {
                clsRecord.setId(clsInfo.getId());
            }
        }
        if (!BinaryUtils.isEmpty(errMsg)) {
            sheetMessage.getRowMessages().add(this.buildRowMessage(errIndex, errMsg));
        }
        return clsRecord;
    }

    private ImportRowMessage buildRowMessage(int rowNum, String message) {
        ImportRowMessage rowMessage = new ImportRowMessage();
        rowMessage.setRowNum(rowNum);
        ImportCellMessage cellMessage = new ImportCellMessage();
        cellMessage.setErrorDesc(message);
        rowMessage.setMessageItems(Collections.singletonList(cellMessage));
        return rowMessage;
    }

    private boolean equalAttrDefs(List<CcCiAttrDef> sourceDefs, List<CcCiAttrDef> targetDefs) {
        if (!isShow3dAttribute && sourceDefs.size() != targetDefs.size()) {
            return false;
        }
        Map<String, CcCiAttrDef> sourceMap = BinaryUtils.toObjectMap(sourceDefs, "proName");
        for (CcCiAttrDef targetDef : targetDefs) {
            String targetDefName = targetDef.getProName();
            CcCiAttrDef sourceDef = sourceMap.get(targetDefName);
            if (sourceDef == null) {
                if (attributeDefinitionFor3dModel.equalsIgnoreCase(targetDefName)) {
                    continue;
                } else {
                    return false;
                }
            }
            boolean no3dEqual = CheckAttrUtil.equalsForModel(sourceDef.getProType(), targetDef.getProType()) && CheckAttrUtil.equalsForModel(sourceDef.getIsCiDisp(), targetDef.getIsCiDisp())
                    && CheckAttrUtil.equalsForModel(sourceDef.getIsMajor(), targetDef.getIsMajor()) && CheckAttrUtil.equalsForModel(sourceDef.getIsRequired(), targetDef.getIsRequired())
                    && CheckAttrUtil.equalsForModel(sourceDef.getEnumValues(), targetDef.getEnumValues());
            boolean equalValidFor3d = true;
            if (!isShow3dAttribute) {
                equalValidFor3d = CheckAttrUtil.equalsForModel(sourceDef.getDefVal(), targetDef.getDefVal());
            }
            if (!no3dEqual && !equalValidFor3d) {
                return false;
            }
        }
        return true;
    }

    @Override
    public boolean reOrder(ClassReOrderDTO reOrderDTO) {
        if (reOrderDTO.getOriginOrder() == reOrderDTO.getNewOrder()) {
            return true;
        }
        boolean flag;
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("dirId", reOrderDTO.getDirId()));
        query.must(QueryBuilders.termQuery("parentId", reOrderDTO.getParentId()));
        if (reOrderDTO.getOriginOrder() > reOrderDTO.getNewOrder()) {
            // 向上移动
            query.must(QueryBuilders.rangeQuery("orderNo").lt(reOrderDTO.getOriginOrder()).gte(reOrderDTO.getNewOrder()));
            flag = esciClassSvc.updateByQuery(QueryBuilders.constantScoreQuery(query), "ctx._source.orderNo+=1", true, new HashMap<>());
        } else {
            // 向下移动
            query.must(QueryBuilders.rangeQuery("orderNo").lte(reOrderDTO.getNewOrder()).gt(reOrderDTO.getOriginOrder()));
            flag = esciClassSvc.updateByQuery(QueryBuilders.constantScoreQuery(query), "ctx._source.orderNo-=1", true, new HashMap<>());
        }
        // 最后更新传入的节点
        if (flag) {
            return esciClassSvc.updateByQuery(QueryBuilders.termQuery("id", reOrderDTO.getClassId()), "ctx._source.orderNo=" + reOrderDTO.getNewOrder(), true, new HashMap<>());
        }
        return false;
    }

    @Override
    public boolean initAllOrderNo(Long domainId) {
        domainId = domainId == null ? BaseConst.DEFAULT_DOMAIN_ID : domainId;
        return esciClassSvc.initAllOrderNo(domainId);
    }

    @Override
    public List<ESCIClassInfo> getTargetAttrDefsByClassIds(Long domainId, Collection<Long> classIds) {
        domainId = domainId == null ? BaseConst.DEFAULT_DOMAIN_ID : domainId;
        List<ESCIClassInfo> classList = esciClassSvc.getTargetAttrDefsByClassIds(domainId, classIds);
        if (!CollectionUtils.isEmpty(classList)) {
            for (ESCIClassInfo ciclass : classList) {
                String icon = BinaryUtils.isEmpty(ciclass.getIcon()) ? esciClassSvc.getDefaultIcon() : ciclass.getIcon();
                if (icon.startsWith(rsmSlaveRoot)) {
                    icon = icon.replaceAll(rsmSlaveRoot, "");
                }
                ciclass.setIcon(rsmSlaveRoot + icon);
                //填充3d模型URL
                if (isShow3dAttribute && StringUtils.isNotBlank(ciclass.getModelIcon3D())) {
                    String modelIcon3D = ciclass.getModelIcon3D();
                    if (modelIcon3D.startsWith(rsmSlaveRoot)) {
                        modelIcon3D = modelIcon3D.replaceAll(rsmSlaveRoot, "");
                    }
                    ciclass.setModelIcon3D(rsmSlaveRoot + modelIcon3D);
                }
                // 3D attribute verification
                attributeVerificationForImgByQuery(ciclass.getAttrDefs());
            }
        }
        return classList;
    }


    /**
     * When the 3D model setting switch is turned on, verify whether the 3D type attribute is unique
     * <br>
     * When the 3D model setting switch is turned off, verify whether the attribute name contains a 3D model
     *
     * @param attrDefs The set of attributes to be verified
     */
    private void attributeVerificationFor3dModel(List<CcCiAttrDef> attrDefs) {
        if (isShow3dAttribute) {
            List<CcCiAttrDef> count3DModelAttrNumber = attrDefs.stream()
                    .filter(attrDef -> AttrNameKeyEnum.MODEL.getType() == attrDef.getProType())
                    .collect(Collectors.toList());
            Assert.isTrue(count3DModelAttrNumber.size() <= 1, "3D模型类型的属性只可包含一个, 请重新设置");
        } else {
            List<CcCiAttrDef> attrs = attrDefs.stream()
                    .filter(attrDef -> attributeDefinitionFor3dModel.equalsIgnoreCase(attrDef.getProStdName()))
                    .collect(Collectors.toList());
            Assert.isTrue(attrs.size() == 0, "属性名称不可设置为 [3D模型]");
        }
    }

    /**
     * 3D model attribute verification, when the switch is turned on, the icon path is filtered
     * <br>
     * When the 3D model switch is turned off, the properties of the existing 3D model are not displayed
     *
     * @param attrDefs Properties to be verified
     */
    private void attributeVerificationForImgByQuery(List<ESCIAttrDefInfo> attrDefs) {
        Iterator<ESCIAttrDefInfo> it = attrDefs.iterator();
        while (it.hasNext()) {
            CcCiAttrDef ccCiAttrDef = it.next();
            int attrType = ccCiAttrDef.getProType();

            if (AttrNameKeyEnum.PICTURE.getType() == attrType) {
                String defVal = ccCiAttrDef.getDefVal();
                if (!BinaryUtils.isEmpty(defVal)) {
                    if (defVal.startsWith(rsmSlaveRoot)) {
                        defVal = defVal.replaceAll(rsmSlaveRoot, "");
                    }
                    ccCiAttrDef.setDefVal(rsmSlaveRoot + defVal);
                }
            }

            if (AttrNameKeyEnum.MODEL.getType() == attrType) {
                if (isShow3dAttribute) {
                    String defaulfModel = ccCiAttrDef.getDefVal();
                    if (defaulfModel.startsWith(rsmSlaveRoot)) {
                        defaulfModel = defaulfModel.replaceAll(rsmSlaveRoot, "");
                    }
                    ccCiAttrDef.setDefVal(rsmSlaveRoot + defaulfModel);
                } else {
                    it.remove();
                }
            }
        }
    }


    /**
     * One-click import for verification of 3D model attributes
     * <br>
     *
     * @param currentClassAttributes Current classification attribute collection
     * @param sheetMessage           sheetMessage
     * @param index                  excel index
     * @param defs                   Properties to be verified
     */
    private List<CcCiAttrDef> importCiClassVerificationFor3dModel(Map<String, CcCiAttrDef> currentClassAttributes, ImportSheetMessage sheetMessage, int index, List<CcCiAttrDef> defs) {
        // 3D model verification
        CcCiAttrDef attrFor3dModel = currentClassAttributes.get(attributeDefinitionFor3dModel);
        if (attrFor3dModel != null) {
            if (isShow3dAttribute) {
                int proType = attrFor3dModel.getProType();
                boolean typeNormal = proType == AttrNameKeyEnum.MODEL.getType();
                if (!typeNormal) {
                    sheetMessage.getRowMessages().add(this.buildRowMessage(index, "3D模型属性类型设置错误, 当前为[" + AttrNameKeyEnum.valueOf(proType).getValue() + "]"));
                    return null;
                }
            } else {
                sheetMessage.getRowMessages().add(this.buildRowMessage(index, "模型名称不可设置为3D模型"));
                return null;
            }
        } else {
            if (isShow3dAttribute) {
                ESCIAttrDefInfo new3dModelAttr = new ESCIAttrDefInfo();
                new3dModelAttr.setIsRequired(1);
                new3dModelAttr.setProType(AttrNameKeyEnum.MODEL.getType());
                new3dModelAttr.setProName(attributeDefinitionFor3dModel);
                new3dModelAttr.setProStdName(attributeDefinitionFor3dModel);
                new3dModelAttr.setIsMajor(0);
                new3dModelAttr.setIsCiDisp(0);
                new3dModelAttr.setProDesc("系统内部使用");
                new3dModelAttr.setGroup(Collections.singletonList("系统管理"));
                defs.add(new3dModelAttr);
            }
        }
        return defs;
    }


    /**
     * 赋予admin角色默认权限
     *
     * @param domainId
     * @param esClsInfos
     */
    private void addAdminDataModuleRlt(Long domainId, List<ESCIClassInfo> esClsInfos) {

        //获取admin角色
        BoolQueryBuilder roleQuery = QueryBuilders.boolQuery();
        roleQuery.must(QueryBuilders.termQuery("domainId", domainId));
        roleQuery.must(QueryBuilders.termQuery("roleName.keyword", "admin"));
        List<SysRole> admin = esRoleSvc.getListByQuery(roleQuery);

        BoolQueryBuilder dataModuleQuery = QueryBuilders.boolQuery();
        dataModuleQuery.must(QueryBuilders.termQuery("domainId", domainId));
        dataModuleQuery.must(QueryBuilders.termQuery("dataModuleCode.keyword", "CICLASS"));
        List<SysDataModule> dataModuleList = dataModuleSvc.getListByQuery(dataModuleQuery);
        if (!dataModuleList.isEmpty() && !admin.isEmpty()) {
            List<SysRoleDataModuleRlt> rlts = new ArrayList<>();
            //获取对象分类的数据权限
            SysDataModule sysDataModule = dataModuleList.get(0);
            Long roleId = admin.get(0).getId();

            for (ESCIClassInfo esClsInfo : esClsInfos) {
                SysRoleDataModuleRlt rlt = SysRoleDataModuleRlt.builder()
                        .domainId(domainId)
                        .roleId(roleId)
                        .dataModuleCode("CICLASS")
                        .uid(String.valueOf(esClsInfo.getId()))
                        .dataValue(String.valueOf(esClsInfo.getId())).build();

                if (sysDataModule.getIscreate() == 1) {
                    rlt.setIscreate(1);
                }
                if (sysDataModule.getIsdelete() == 1) {
                    rlt.setIsdelete(1);
                }
                if (sysDataModule.getIssee() == 1) {
                    rlt.setIssee(1);
                }
                if (sysDataModule.getIsupdate() == 1) {
                    rlt.setIsupdate(1);
                }
                List<ExtendedPermission> extendedPermissions = sysDataModule.getExtendedPermissions();
                if (extendedPermissions != null) {
                    List<ExtendedPermission> extendedPermissionRltList = new ArrayList<>();
                    for (ExtendedPermission extendedPermission : extendedPermissions) {
                        if (extendedPermission.getValue() == 1) {
                            extendedPermissionRltList.add(new ExtendedPermission(extendedPermission.getKey(), 1));
                        }
                    }
                    rlt.setExtendedPermissions(extendedPermissionRltList);
                }
                rlts.add(rlt);
            }
            roleSvc.addRoleDataModuleRlt(domainId, rlts, false);
        }
    }

}
