package com.uino.bean.cmdb.business.dataset;

import java.util.List;

/**
 * @Classname QueryCondition
 * @Description TODO
 * @Date 2020/6/3 11:24
 * <AUTHOR> sh
 */
public class QueryCondition {
    //通过起止集合Id查询路径
    private Long pageId;
    private Long startPageId;
    private Long endPageId;
    private Integer[] val;
    private Integer op;
    private Long rltClsId;
    private List<RltRuleInfo.LineCdt> lineCdts;

    public Long getPageId() {
        return pageId;
    }

    public void setPageId(Long pageId) {
        this.pageId = pageId;
    }

    public Long getStartPageId() {
        return startPageId;
    }

    public void setStartPageId(Long startPageId) {
        this.startPageId = startPageId;
    }

    public Long getEndPageId() {
        return endPageId;
    }

    public void setEndPageId(Long endPageId) {
        this.endPageId = endPageId;
    }

    public Integer[] getVal() {
        return val;
    }

    public void setVal(Integer[] val) {
        this.val = val;
    }

    public Integer getOp() {
        return op;
    }

    public void setOp(Integer op) {
        this.op = op;
    }

    public Long getRltClsId() {
        return rltClsId;
    }

    public void setRltClsId(Long rltClsId) {
        this.rltClsId = rltClsId;
    }

    public List<RltRuleInfo.LineCdt> getLineCdts() {
        return lineCdts;
    }

    public void setLineCdts(List<RltRuleInfo.LineCdt> lineCdts) {
        this.lineCdts = lineCdts;
    }
}
