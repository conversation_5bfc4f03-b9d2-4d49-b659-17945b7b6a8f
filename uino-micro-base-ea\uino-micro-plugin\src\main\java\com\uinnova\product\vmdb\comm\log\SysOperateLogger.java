package com.uinnova.product.vmdb.comm.log;

import com.binary.core.bean.BMProxy;
import com.binary.core.io.FileSystem;
import com.binary.core.util.BinaryUtils;
import com.binary.framework.Local;
import com.binary.framework.bean.User;
import com.binary.framework.util.PrimaryKey;
import com.binary.framework.web.RemoteResult;
import com.binary.json.JSON;
import com.uinnova.product.vmdb.comm.doc.api.MvcApi;
import com.uinnova.product.vmdb.comm.doc.api.MvcModApi;
import com.uinnova.product.vmdb.comm.doc.build.DocBuilder;
import com.uinnova.product.vmdb.comm.i18n.MessageUtil;
import com.uinnova.product.vmdb.comm.model.sys.SysOperateLog;
import com.uino.tarsier.tarsiercom.dao.mybatis.ComMyBatisSQLDaoImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import jakarta.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;

/**
 * <AUTHOR>
 */
public class SysOperateLogger extends ComMyBatisSQLDaoImpl {
    private static final Logger logger = LoggerFactory.getLogger(SysOperateLogger.class);

    private static SysOperateLogger instance;

    private final Map<String, OperateDesc> opermap = new HashMap<String, OperateDesc>();

    private final Object syncobj = new Object();
    private final LinkedList<SysOperateLog> buff = new LinkedList<SysOperateLog>();

    /**
     * 一次写日志批次大小
     **/
    private int batchSize = 100;

    /**
     * 定时间隔时间, 单位:秒
     **/
    private int intervalTime = 10;

    private boolean startTimered = false;

    public SysOperateLogger() {
        instance = this;
    }

    private void startTimer() {
        synchronized (syncobj) {
            if (!startTimered) {
                logger.info(" start system operate log writer ... ");

                Timer timer = new Timer();
                timer.schedule(new TimerTask() {
                    @Override
                    public void run() {
                        writeLog();
                    }
                }, intervalTime * 10, intervalTime * 10);

                startTimered = true;
                logger.info(" start system operate log writer successful. ");
            }
        }
    }

    private void push(SysOperateLog log) {
        synchronized (syncobj) {
            buff.add(log);

            if (!startTimered) {
                startTimer();
            }
        }
    }

    private SysOperateLog pop() {
        synchronized (syncobj) {
            if (buff.size() > 0) {
                return buff.pop();
            }
            return null;
        }
    }

    private void writeLog() {
        try {
            Local.open((User) null);

            while (true) {
                List<SysOperateLog> logs = new ArrayList<SysOperateLog>();
                while (true) {
                    SysOperateLog log = pop();
                    if (log == null) {
                        break;
                    }

                    logs.add(log);
                    if (logs.size() >= this.batchSize) {
                        break;
                    }
                }

                if (logs.size() > 0) {
                    StringBuffer sql = new StringBuffer();
                    sql.append(" insert into SYS_OPERATE_LOG")
                            .append("(ID, LOG_TIME, USER_ID, USER_CODE, USER_NAME, OP_NAME, OP_PATH, OP_DESC, OP_STATUS, OP_PARAM, ")
                            .append("OP_RESULT, MVC_FULL_NAME, MVC_MOD_NAME, DOMAIN_ID, USER_DOMAIN_ID, CREATE_TIME, MODIFY_TIME) ")
                            .append("values(#{ID}, #{LOGTIME}, #{USERID}, #{USERCODE}, #{USERNAME}, #{OPNAME}, #{OPPATH}, #{OPDESC}, ")
                            .append("#{OPSTATUS}, #{OPPARAM}, #{OPRESULT}, #{MVCFULLNAME}, #{MVCMODNAME}, #{DOMAINID}, #{USERDOMAINID}, ")
                            .append("#{CREATETIME}, #{MODIFYTIME}) ");
                   /* JdbcOperator jo = JdbcOperatorFactory.getMomentFactory().getJdbcOperator();
                    jo.executeUpdateBatch(sql.toString(), logs, SysOperateLog.class);
                    jo.getTransaction().commit();*/

                    for (SysOperateLog obj : logs) {
                        BMProxy<SysOperateLog> proxy = BMProxy.getInstance(obj);
                        Map<String, Object> map = new HashMap<String, Object>();
                        proxy.copyTo(map);

                        Map<String, Object> map2 = new HashMap<String, Object>();
                        Set<String> keySet = map.keySet();
                        for (String key : keySet) {
                            map2.put(key.toUpperCase(), map.get(key));
                        }

                        this.executeInsertByParam("SYS_OPERATE_LOG.insertlog", sql.toString(), map2);
                    }
                }

                if (logs.size() < this.batchSize) {
                    break;
                }
            }

            Local.commit();
        } catch (Throwable t) {
            Local.rollback();
        } finally {
            Local.close();
        }
    }

    private OperateDesc getOperateDescByPath(String path) {
        MessageUtil.checkEmpty(path, "path");
        path = path.trim();

        OperateDesc desc = opermap.get(path);
        if (desc == null) {
            boolean ba = buildOperateDescMap();
            if (ba) {
                desc = opermap.get(path);
            }
        }
        return desc;
    }

    /**
     * 返回是否重新构建
     *
     * @return
     */
    private synchronized boolean buildOperateDescMap() {
        if (opermap.size() > 0) {
            return false;
        }
        List<MvcApi> apils = DocBuilder.getMvcApi("com.uinnova.product.**");

        for (int i = 0; i < apils.size(); i++) {
            MvcApi api = apils.get(i);
            String mvc = api.getMvc();
            List<MvcModApi> mods = api.getModApis();

            if (mods != null && mods.size() > 0) {
                for (int j = 0; j < mods.size(); j++) {
                    MvcModApi m = mods.get(j);

                    String path = m.getFullPath();
                    String desc = m.getDesc();
                    if (desc != null && desc.length() > 200) {
                        desc = desc.substring(0, 200);
                    }
                    opermap.put(path.trim(), new OperateDesc(m.getModName(), path, desc, mvc));
                }
            }
        }
        return true;
    }

    private String getRequestBody(HttpServletRequest request) throws IOException {
        InputStream is = null;
        try {
            is = request.getInputStream();
            return FileSystem.read(is, "UTF-8");
        } finally {
            if (is != null) {
                is.close();
            }
        }

    }

    public void push(HttpServletRequest request, User user, Object result) throws IOException {
        String contextPath = request.getContextPath();
        String uri = request.getRequestURI();
        if (uri.startsWith(contextPath)) {
            uri = uri.substring(contextPath.length());
        }
        OperateDesc od = getOperateDescByPath(uri);
        if (od == null) {
            return;
        }
        ModuNamed mn = ModuNamed.valueOfMvc(od.getMvc());
        if (mn == null) {
            return;
        }

        String p = "";
        String r = "";
        boolean rs = true;

        String contentType = request.getContentType();
        String contentTypeStart = "multipart/form-data";
        if (contentType == null || !contentType.startsWith(contentTypeStart)) {
            p = getRequestBody(request);
        }

        if (result instanceof RemoteResult) {
            RemoteResult rr = (RemoteResult) result;
            rs = rr.isSuccess();
        }
        r = JSON.toString(result);

        int length = 1000;
        if (p != null && p.length() > length) {
            p = p.substring(0, 1000);
        }
        if (r != null && r.length() > length) {
            r = r.substring(0, 1000);
        }
        Long time = BinaryUtils.getNumberDateTime();

        SysOperateLog log = new SysOperateLog();
        log.setId(PrimaryKey.getInstance("SYS_OPERATE_LOG").next());
        log.setLogTime(time);
        log.setUserId(user.getId());
        log.setUserCode(user.getLoginCode());
        log.setUserName(user.getUserName());
        log.setOpName(mn.name());
        log.setOpPath(od.getFullPath());
        log.setOpDesc(od.getDesc());
        log.setOpStatus(rs ? 1 : 0);
        log.setOpParam(p);
        log.setOpResult(r);
        log.setMvcFullName(od.getMvc());
        log.setMvcModName(od.getModName());
        log.setDomainId(1l);
        log.setUserDomainId(user.getDomainId());
        log.setCreateTime(time);
        log.setModifyTime(time);
        push(log);
    }

    public static void pushLog(HttpServletRequest request, Object result) {
        if (instance != null && Local.isOpen() && Local.getUser(false) != null) {
            try {
                instance.push(request, Local.getUser(), result);
            } catch (Throwable t) {
                logger.error(" push system operate log error! ", t);
            }
        }
    }

}
