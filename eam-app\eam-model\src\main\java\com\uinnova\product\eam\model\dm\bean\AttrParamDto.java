package com.uinnova.product.eam.model.dm.bean;

import com.uino.bean.cmdb.base.LibType;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class AttrParamDto {

    @NotNull(message = "不能为空")
    private List<String> ciCodes;
    @NotNull(message = "不能为空")
    private String ownerCode;
    @NotNull(message = "不能为空")
    private LibType libType;
}
