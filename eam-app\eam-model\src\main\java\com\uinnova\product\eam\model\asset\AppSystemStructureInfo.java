package com.uinnova.product.eam.model.asset;


import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
@Data
public class AppSystemStructureInfo {
    private String title;
    private List<Map<String, String>> body = new ArrayList<>();

    public AppSystemStructureInfo() {
    }

    public AppSystemStructureInfo(String title) {
        this.title = title;
    }
}
