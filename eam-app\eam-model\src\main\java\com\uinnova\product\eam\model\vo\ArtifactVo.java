package com.uinnova.product.eam.model.vo;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.comm.model.es.EamArtifact;
import com.uinnova.product.eam.model.EamArtifactElementVo;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ArtifactVo {

    @Comment("制品基本信息")
    private EamArtifact artifact;

    @Comment("制品分栏信息")
    private List<EamArtifactElementVo> elements;

    @Comment("导出制品文件标识")
    private String ident;

    public ArtifactVo() {}

    public ArtifactVo(EamArtifact artifact, List<EamArtifactElementVo> elements) {
        this.artifact = artifact;
        this.elements = elements;
    }

}
