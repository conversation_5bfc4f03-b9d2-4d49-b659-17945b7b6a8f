package com.uino.dao.cmdb;

import jakarta.annotation.PostConstruct;

import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.uino.dao.AbstractESBaseDao;
import com.uino.dao.ESConst;
import com.uino.bean.cmdb.SysTopData;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
public class ESSysTopData extends AbstractESBaseDao<SysTopData, JSONObject> {

	@Override
	public String getIndex() {
		// TODO Auto-generated method stub
		return ESConst.INDEX_SYS_TOP_DATA;
	}

	@Override
	public String getType() {
		// TODO Auto-generated method stub
		return ESConst.INDEX_SYS_TOP_DATA;
	}

	@PostConstruct
	public void init() {
		super.initIndex();
	}

}
