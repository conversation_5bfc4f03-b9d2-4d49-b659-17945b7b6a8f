package com.uino.provider.feign.cmdb;

import java.util.List;
import java.util.Set;

import com.uino.bean.cmdb.business.ClassReOrderDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import com.uinnova.product.vmdb.comm.model.ci.CCcCiClass;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uino.bean.cmdb.base.ESCIClassInfo;
import com.uino.provider.feign.config.BaseFeignConfig;

/**
 * 关系分类feign定义
 * 
 * <AUTHOR>
 *
 */
@Component
@FeignClient(name = "${feign-server-name:tarsier-platform}", path = "${feign-server-path:tarsier-platform}/feign/rltClass", configuration = {
        BaseFeignConfig.class})
public interface RltClassFeign {

    /**
     * 持久化关系分类
     * 
     * @param rltClass
     * @return
     */
    @PostMapping("saveOrUpdate")
    public Long saveOrUpdate(@RequestBody ESCIClassInfo rltClass);

    /**
     * 根据关系分类ids删除关系分类
     * 
     * @param rltClassIds
     * @return
     */
    @PostMapping("deleteByIds")
    public Integer deleteByIds(@RequestBody Set<Long> rltClassIds);

    /**
     * 获取所有关系分类
     * 
     * @return
     */
    @PostMapping("queryAllClasses")
    public List<CcCiClassInfo> queryAllClasses(@RequestBody Long domainId);

    /**
     * 根据id获取关系分类信息
     * 
     * @param rltClassId
     * @return
     */
    @PostMapping("getRltClassById")
    public CcCiClassInfo getRltClassById(@RequestParam(value = "rltClassId", required = false) Long rltClassId);

    /**
     * 根据关系分类名称获取关系分类信息
     * @param domainId 域id
     * @param className 分类名称
     * @return 分类信息
     */
    @PostMapping("getRltClassById")
    CcCiClassInfo getRltClassByName(@RequestParam(value = "domainId", required = false) Long domainId,
                                    @RequestParam(value = "className", required = false) String className);

    @PostMapping("exportAttrDefs")
    public ResponseEntity<byte[]> exportAttrDefs(@RequestBody(required = false) Set<Long> clsIds);

    @PostMapping("importAttrDefs")
    public ESCIClassInfo importAttrDefs(@RequestParam(value = "file", required = false) MultipartFile excelFile,
            @RequestParam(required = false, value = "clsId") Long clsId);

    @PostMapping("getRltClassByCdt")
    public List<CcCiClassInfo> getRltClassByCdt(@RequestBody(required = false) CCcCiClass cdt);

    @PostMapping("reOrder")
    boolean reOrder(@RequestBody ClassReOrderDTO reOrderDTO);

    @PostMapping("initAllOrderNo")
    boolean initAllOrderNo(@RequestBody Long domainId);
}
