package com.uino.api.client.sys;

import com.binary.jdbc.Page;
import com.uino.bean.sys.base.LdapUserMapping;
import com.uino.bean.sys.base.LoginAuthConfig;
import com.uino.bean.sys.business.ActiveLoginAuthConfigDto;
import com.uino.bean.sys.business.LoginAuthConfigDto;

/**
 * 登录集成规则配置
 * 
 * <AUTHOR>
 *
 */
public interface ILoginAuthConfigApiSvc {

    /**
     * 通过ID 查看配置信息
     * 
     * @param id 配置ID
     * @return
     */
    LoginAuthConfig queryById(Long id);

    /**
     * 查询当前集成配置项
     * 
     * @param authConfigCdt
     * @return
     */
    Page<LoginAuthConfig> queryPage(LoginAuthConfigDto authConfigCdt);

    /**
     * 保存或更新集成登录配置
     * 
     * @param authConfig
     * @return id
     */
    Long saveOrUpdate(LoginAuthConfig authConfig);

    /**
     * 启用配置项
     * 
     * @param active
     */
    void activeConfig(ActiveLoginAuthConfigDto active);

    /**
     * 测试连接是否可用
     * 
     * @param authConfig
     * @return
     */
    boolean testConnection(LoginAuthConfig authConfig);

    /**
     * 测试并返回用户映射关系
     * 
     * @param authConfig
     * @return null时表示测试失败
     */
    LdapUserMapping testConnectionAndMappingUser(LoginAuthConfig authConfig);

    /**
     * 删除配置项
     * 
     * @param id
     */
    void removeById(Long id);

}
