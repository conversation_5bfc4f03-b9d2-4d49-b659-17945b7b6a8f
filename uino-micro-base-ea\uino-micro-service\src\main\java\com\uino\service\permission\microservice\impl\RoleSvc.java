package com.uino.service.permission.microservice.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.binary.core.exception.MessageException;
import com.binary.core.util.BinaryUtils;
import com.binary.jdbc.Page;
import com.uino.bean.cmdb.base.ESCIClassInfo;
import com.uino.bean.cmdb.base.ESCITagInfo;
import com.uino.bean.permission.base.*;
import com.uino.bean.permission.business.DataRole;
import com.uino.bean.permission.business.ModuleNodeInfo;
import com.uino.bean.permission.business.ValidDataModule;
import com.uino.bean.permission.business.request.OptionUserModuleAuthRequestDto;
import com.uino.bean.permission.business.request.SaveRoleOrgRltRequestDto;
import com.uino.bean.permission.business.request.SaveRoleUserRltRequestDto;
import com.uino.bean.permission.enums.SysDataModuleEnum;
import com.uino.bean.permission.query.CAuthBean;
import com.uino.bean.permission.query.CSysDataModule;
import com.uino.bean.permission.query.CSysRoleDataModuleRlt;
import com.uino.bean.permission.query.SearchKeywordBean;
import com.uino.dao.BaseConst;
import com.uino.dao.cmdb.ESCIClassSvc;
import com.uino.dao.cmdb.ESTagSvc;
import com.uino.dao.permission.ESDataModuleSvc;
import com.uino.dao.permission.ESModuleSvc;
import com.uino.dao.permission.ESRoleSvc;
import com.uino.dao.permission.ESUserSvc;
import com.uino.dao.permission.rlt.*;
import com.uino.dao.util.ESUtil;
import com.uino.service.permission.microservice.IRoleSvc;
import com.uino.service.util.FileUtil;
import com.uino.util.sys.BeanUtil;
import com.uino.util.sys.CommonFileUtil;
import com.uino.util.sys.SysUtil;
import com.uino.util.sys.ValidDtoUtil;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.MultiMatchQueryBuilder.Type;
import org.elasticsearch.index.query.Operator;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
@Slf4j
public class RoleSvc implements IRoleSvc {

    @Autowired
    ESRoleSvc roleSvc;

    @Autowired
    ESRoleModuleRltSvc rmRltSvc;

    @Autowired
    ESUserModuleRltSvc umRltSvc;

    @Autowired
    ESRoleDataModuleRltSvc rdRltSvc;

    @Autowired
    ESUserDataModuleRltSvc udRltSvc;

    @Autowired
    ESDataModuleSvc dataModuleSvc;

    @Autowired
    ESModuleSvc moduleSvc;

    @Autowired(required = false)
    ESCIClassSvc classSvc;

    @Autowired(required = false)
    ESTagSvc tagSvc;

    @Autowired
    ESPerssionCommSvc commSvc;

    @Autowired(required = false)
    private ValidDataModule validDataModule;

    @Autowired
    private ESUserSvc userSvc;

    @Override
    public Long saveOrUpdate(SysRole role) {
        BinaryUtils.checkEmpty(role.getRoleName(), "roleName");
        Assert.isTrue(role.getRoleName().length() <= 50, "角色名称不能超过50位");
        Assert.isTrue(role.getRoleDesc().length() <= 200, "BS_USER_NOTE_LENGTH_ERR");
        if(BinaryUtils.isEmpty(role.getDomainId())){
            role.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
        }
        SysRole oRole = roleSvc.getByName(role.getDomainId(), role.getRoleName());
        if (role.getId() != null) {
            SysRole esRole = roleSvc.getById(role.getId());
            Assert.isTrue(!esRole.getRoleName().equals("admin"),"admin角色不能修改");
            if (oRole != null && role.getId().longValue() != oRole.getId().longValue()) {
                throw MessageException.i18n("BS_ROLE_NAME_EXIST", "{\"roleName\":\"" + role.getRoleName() + "\"}");
            }
            return roleSvc.saveOrUpdate(role);
        }

        if (oRole != null) {
            throw MessageException.i18n("BS_ROLE_NAME_EXIST", "{\"roleName\":\"" + role.getRoleName() + "\"}");
        }
        return roleSvc.saveOrUpdate(role);
    }

    @Override
    public Integer saveOrUpdateBatch(Long domainId,List<SysRole> roles) {
        BinaryUtils.checkEmpty(roles, "roles");
        domainId = domainId == null ? BaseConst.DEFAULT_DOMAIN_ID : domainId;
        Map<String, Long> roleMap = new HashMap<>();
        List<Long> roleIds=new ArrayList<>();
        for (SysRole role : roles) {
            BinaryUtils.checkEmpty(role.getRoleName(), "roleName");
            Assert.isTrue(role.getRoleName().length() <= 50, "角色名称不能超过50位");
            Assert.isTrue(role.getRoleDesc().length() <= 200, "BS_USER_NOTE_LENGTH_ERR");
            if(role.getDomainId() == null){
                role.setDomainId(domainId);
            }
            roleMap.put(role.getRoleName(), role.getId());
            if(!BinaryUtils.isEmpty(role.getId())){
                roleIds.add(role.getId());
            }
        }
        if (roleMap.size() < roles.size()) {
            throw new MessageException("角色名称有重复请检查后重试");
        }
        if(roleIds!=null && roleIds.size()>0){
            BoolQueryBuilder query=QueryBuilders.boolQuery();
            query.must(QueryBuilders.termQuery("doaminId",domainId));
            query.must(QueryBuilders.termsQuery("id",roleIds));
            List<SysRole> esRoles = roleSvc.getListByQuery(query);
            if(esRoles!=null && esRoles.size()>0){
                List<String> roleNames = esRoles.stream().map(SysRole::getRoleName).collect(Collectors.toList());
                Assert.isTrue(!roleNames.contains("admin"),"admin角色不能修改");
            }
        }
        BoolQueryBuilder query=QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("domainId",domainId));
        query.must(QueryBuilders.termsQuery("roleName.keyword", roleMap.keySet()));
        List<SysRole> roleList = roleSvc.getListByQuery(query);
        for (SysRole sysRole : roleList) {
            if (!sysRole.getId().equals(roleMap.get(sysRole.getRoleName()))) {
                throw MessageException.i18n("BS_ROLE_NAME_EXIST", "{\"roleName\":\"" + sysRole.getRoleName() + "\"}");
            }
        }
        return roleSvc.saveOrUpdateBatch(roles);
    }
    @Override
    public Integer deleteById(Long roleId) {
        BinaryUtils.checkEmpty(roleId, "roleId");
        Assert.isTrue(commSvc.getUserRoleRltByRoleIds(Collections.singleton(roleId)).size() <= 0L, "角色已绑定用户，不可删除");
        SysRole role = roleSvc.getById(roleId);
        if(role != null){
            Assert.isTrue(!role.getRoleName().equals("admin"),"admin角色不能删除");
        }
        return roleSvc.deleteRoleById(roleId);
    }

    @Override
    public Page<SysRole> getRolePageByQuery(SearchKeywordBean bean) {
        String keyword = bean.getKeyword();
        Long domainId = bean.getDomainId() == null ? BaseConst.DEFAULT_DOMAIN_ID : bean.getDomainId();
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.rangeQuery("id").from(1));
        query.must(QueryBuilders.termQuery("domainId",domainId));
        if (keyword != null && keyword.length() > 0) {
            query.must(QueryBuilders.multiMatchQuery(keyword, "roleName").operator(Operator.AND).type(Type.PHRASE_PREFIX).lenient(true));
        }
        return roleSvc.getSortListByQuery(bean.getPageNum(), bean.getPageSize(), query, "modifyTime", false);
    }

    @Override
    public Integer addRoleUserRlt(SaveRoleUserRltRequestDto bean) {
        Long roleId = bean.getRoleId();
        BinaryUtils.checkEmpty(roleId, "roleId");
        if (BinaryUtils.isEmpty(bean.getDomainId())) {
            bean.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
        }
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("roleId", roleId));
        query.must(QueryBuilders.termQuery("domainId", bean.getDomainId()));
        Integer rs = commSvc.deleteUserRoleRltByQuery(query, true);
        if (!BinaryUtils.isEmpty(bean.getUserIds())) {
            List<SysUserRoleRlt> list = new ArrayList<>();
            bean.getUserIds().forEach(userId -> {
                list.add(SysUserRoleRlt.builder().userId(userId).roleId(roleId).domainId(bean.getDomainId()).build());
            });
            return commSvc.saveUserRoleRltBatch(list);
        }
        return rs;
    }

    @Override
    public Integer addRoleOrgRlt(SaveRoleOrgRltRequestDto bean) {
        Long roleId = bean.getRoleId();
        Assert.notNull(bean.getRoleId(), "X_PARAM_NOT_NULL${name:roleId}");
        if(BinaryUtils.isEmpty(bean.getDomainId())){
            bean.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
        }
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("roleId", roleId));
        query.must(QueryBuilders.termQuery("domainId",bean.getDomainId())) ;
        Integer rs = commSvc.deleteOrgRoleRltByQuery(query, true);
        if (!BinaryUtils.isEmpty(bean.getOrgIds())) {
            List<SysOrgRoleRlt> list = new ArrayList<>();
            bean.getOrgIds().forEach(orgId -> {
                list.add(SysOrgRoleRlt.builder().roleId(roleId).orgId(orgId).domainId(bean.getDomainId()).build());
            });
            return commSvc.saveOrgRoleRltBatch(list);
        }
        return rs;
    }

    @Override
    public Integer addRoleMenuRlt(Long domainId,List<SysRoleModuleRlt> list) {
        if(domainId==null){
            domainId=BaseConst.DEFAULT_DOMAIN_ID;
        }
        // 如果全取消，前台传的是moduleId=0,roleId照常
        BinaryUtils.checkEmpty(list, "list");
        Long roleId = 0L;
        for (SysRoleModuleRlt bean : list) {
            BinaryUtils.checkEmpty(bean.getModuleId(), "moduleId");
            BinaryUtils.checkEmpty(bean.getRoleId(), "roleId");
            roleId = bean.getRoleId();
            if(BinaryUtils.isEmpty(bean.getDomainId())){
                bean.setDomainId(domainId);
            }
        }
        rmRltSvc.deleteByQuery(QueryBuilders.termQuery("roleId", roleId), true);
        list.removeIf(next -> next.getModuleId().longValue() == 0L);
        return rmRltSvc.saveOrUpdateBatch(list);
    }

    @Override
    public Integer addRoleDataModuleRlt(Long domainId, List<SysRoleDataModuleRlt> list) {
		return this.addRoleDataModuleRlt(domainId, list, true);
    }

    @Override
	public Integer addRoleDataModuleRlt(Long domainId, List<SysRoleDataModuleRlt> rlts, boolean isComplete) {
		// dataValue=0,uid=0 表示删除
		BinaryUtils.checkEmpty(rlts, "list");
        if (validDataModule != null) {
            validDataModule.valid(rlts);
        }
		domainId = domainId == null ? BaseConst.DEFAULT_DOMAIN_ID : domainId;
		List<Long> roleIds = new ArrayList<>();
		String dataModuleCode = "";
		for (SysRoleDataModuleRlt bean : rlts) {
			BinaryUtils.checkEmpty(bean.getRoleId(), "roleId");
			BinaryUtils.checkEmpty(bean.getDataModuleCode(), "dataModuleCode");
			BinaryUtils.checkEmpty(bean.getDataValue(), "dataValue");
			BinaryUtils.checkEmpty(bean.getUid(), "uid");
			roleIds.add(bean.getRoleId());
			dataModuleCode = bean.getDataModuleCode();
			if (BinaryUtils.isEmpty(bean.getDomainId())) {
				bean.setDomainId(domainId);
			}
		}
		BoolQueryBuilder query = QueryBuilders.boolQuery();
		query.must(QueryBuilders.termsQuery("roleId", roleIds))
				.must(QueryBuilders.termQuery("dataModuleCode.keyword", dataModuleCode));
		if (isComplete) {
			query.must(QueryBuilders.termQuery("domainId", domainId));
			rdRltSvc.deleteByQuery(query, true);
		} else {
			// 增量保存，过滤重复数据
			List<String> uids = rlts.stream().map(SysRoleDataModuleRlt::getUid).collect(Collectors.toList());
			query.must(QueryBuilders.termsQuery("uid.keyword", uids));
			List<SysRoleDataModuleRlt> dataModuleRlts = rdRltSvc.getListByQuery(1, 10000, query).getData();
			Map<String, SysRoleDataModuleRlt> dataModuleRltMap = dataModuleRlts.stream()
					.collect(Collectors.toMap(rlt -> rlt.getUid().concat("_").concat(rlt.getRoleId().toString()),
							Function.identity()));
			for (SysRoleDataModuleRlt rlt : rlts) {
				String keyCode = rlt.getUid().concat("_").concat(rlt.getRoleId().toString());
				if (dataModuleRltMap.containsKey(keyCode)) {
					SysRoleDataModuleRlt dataModuleRlt = dataModuleRltMap.get(keyCode);
					rlt.setId(dataModuleRlt.getId());
				}
			}
		}
		rlts.removeIf(next -> "0".equals(next.getDataValue()) && "0".equals(next.getUid()));
		return rdRltSvc.saveOrUpdateBatch(rlts);
	}

	@Override
    public Integer addUserDataModuleRlt(Long domainId,List<SysUserDataModuleRlt> list) {
        if(domainId == null){
            domainId=BaseConst.DEFAULT_DOMAIN_ID;
        }
        // dataValue=0,uid=0 表示删除
        BinaryUtils.checkEmpty(list, "list");
        Long userId = 0L;
        String dataModuleCode = "";
        for (SysUserDataModuleRlt bean : list) {
            BinaryUtils.checkEmpty(bean.getUserId(), "userId");
            BinaryUtils.checkEmpty(bean.getDataModuleCode(), "dataModuleCode");
            BinaryUtils.checkEmpty(bean.getDataValue(), "dataValue");
            BinaryUtils.checkEmpty(bean.getUid(), "uid");
            userId = bean.getUserId();
            dataModuleCode = bean.getDataModuleCode();
            if(BinaryUtils.isEmpty(bean.getDomainId())){
                bean.setDomainId(domainId);
            }
        }
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("userId", userId)).must(QueryBuilders.termQuery("dataModuleCode.keyword", dataModuleCode));
        udRltSvc.deleteByQuery(query, true);
        list.removeIf(next -> "0".equals(next.getDataValue()) && "0".equals(next.getUid()));
        return udRltSvc.saveOrUpdateBatch(list);
    }

    @Override
    public Integer addUserMenuRlt(Long domainId,List<SysUserModuleRlt> list) {
        if(domainId == null){
            domainId=BaseConst.DEFAULT_DOMAIN_ID;
        }
        // 与前台约定，如果全不选传moduleId=0，userId照常
        BinaryUtils.checkEmpty(list, "list");
        Long userId = 0L;
        for (SysUserModuleRlt bean : list) {
            BinaryUtils.checkEmpty(bean.getUserId(), "userId");
            userId = bean.getUserId();
            BinaryUtils.checkEmpty(bean.getModuleId(), "moduleId");
            if(BinaryUtils.isEmpty(bean.getDomainId())){
                bean.setDomainId(domainId);
            }
        }
        umRltSvc.deleteByQuery(QueryBuilders.termQuery("userId", userId), true);
        list.removeIf(next -> next.getModuleId().longValue() == 0L);
        return umRltSvc.saveOrUpdateBatch(list);
    }

    @Override
    public Long addDataModuleMenu(SysDataModule dataModule) {
        BinaryUtils.checkEmpty(dataModule.getDataModuleName(), "dataModuleName");
        BinaryUtils.checkEmpty(dataModule.getDataSourceUrl(), "dataSourceUrl");
        Long domainId = dataModule.getDomainId() == null ? BaseConst.DEFAULT_DOMAIN_ID : dataModule.getDomainId();
        CSysDataModule cdt = new CSysDataModule();
        cdt.setDataModuleCodeEqual(dataModule.getDataModuleCode());
        cdt.setDomainId(domainId);
        List<SysDataModule> dataModules = dataModuleSvc.getListByCdt(cdt);
        if (!BinaryUtils.isEmpty(dataModules)) {
            Long dataModeleId = dataModules.get(0).getId();
            dataModule.setId(dataModeleId);
        }
        if (BinaryUtils.isEmpty(dataModule.getIssee())) {
            dataModule.setIssee(0);
        }
        if (BinaryUtils.isEmpty(dataModule.getIscreate())) {
            dataModule.setIscreate(0);
        }
        if (BinaryUtils.isEmpty(dataModule.getIsupdate())) {
            dataModule.setIsupdate(0);
        }
        if (BinaryUtils.isEmpty(dataModule.getIsdelete())) {
            dataModule.setIsdelete(0);
        }
        if (BinaryUtils.isEmpty(dataModule.getIsenable())) {
            dataModule.setIsenable(1L);
        }
        if (BinaryUtils.isEmpty(dataModule.getDisplayType())) {
            dataModule.setDisplayType(1L);
        }
        if(BinaryUtils.isEmpty(dataModule.getDomainId())){
            dataModule.setDomainId(dataModules.get(0).getDomainId());
        }
        return dataModuleSvc.saveOrUpdate(dataModule);
    }

    @Override
    public List<DataRole> getDataRoleByCIClass(Long domainId) {
        domainId=domainId==null?BaseConst.DEFAULT_DOMAIN_ID:domainId;
        BoolQueryBuilder query=QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("domainId",domainId));
        Page<ESCIClassInfo> page = classSvc.getListByQuery(0, 1000, query);
        List<ESCIClassInfo> list = page.getData();
        List<DataRole> datas = new ArrayList<>();
        for (ESCIClassInfo ciClass : list) {
            DataRole data = new DataRole();
            data.setUid(ciClass.getId().toString());
            data.setDataValue(ciClass.getId().toString());
            data.setName(ciClass.getClassName());
            datas.add(data);
        }
        return datas;
    }

    @Override
    public List<DataRole> getDataRoleByCITag(Long domainId) {
        domainId = domainId == null ? BaseConst.DEFAULT_DOMAIN_ID : domainId;
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("domainId",domainId));
        Page<ESCITagInfo> page = tagSvc.getListByQuery(0, 1000, query);
        List<ESCITagInfo> list = page.getData();
        List<DataRole> datas = new ArrayList<>();
        for (ESCITagInfo tag : list) {
            DataRole data = new DataRole();
            data.setUid(tag.getId().toString());
            data.setDataValue(tag.getId().toString());
            data.setName(tag.getTagName());
            datas.add(data);
        }
        return datas;
    }

    @Override
    public List<SysDataModule> getAllDataRoleMenu(Long domainId) {
        domainId = domainId == null ? BaseConst.DEFAULT_DOMAIN_ID : domainId;
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("domainId", domainId));
        query.must(QueryBuilders.termQuery("isenable", 1));
        return dataModuleSvc.getSortListByQuery(1, 100, query, "orderNo", false).getData();
    }

    @Override
    public ModuleNodeInfo getAllMenu(Long domainId) {
        return moduleSvc.getModuleTree(domainId);
    }

    @Override
    public List<SysRoleModuleRlt> getAuthMenuByRoleId(Long roleId) {
        HashSet<Long> roleIds = new HashSet<>();
        roleIds.add(roleId);
        return rmRltSvc.getListByRoleIds(roleIds);
    }

    @Override
    public List<SysRoleDataModuleRlt> getAuthDataRoleByRoleId(CAuthBean bean) {
        Long roleId = bean.getRoleId();
        String dataModuleCode = bean.getDataModuleCode();
        BinaryUtils.checkEmpty(bean.getRoleId(), "roleId");
        BinaryUtils.checkEmpty(bean.getDataModuleCode(), "dataModuleCode");
        HashSet<Long> roleIds = new HashSet<Long>();
        roleIds.add(bean.getRoleId());
        List<String> moduleCodes = new ArrayList<String>();
        moduleCodes.add(bean.getDataModuleCode());
        List<SysRoleDataModuleRlt> dm = rdRltSvc.getListByRoleIds(roleIds, moduleCodes);
        List<SysRoleDataModuleRlt> sysRoleDataModuleRlts = new ArrayList<>();
        if (roleId == 1 && "BUTTON".equals(dataModuleCode) && CollectionUtils.isEmpty(dm)) {
            List<JSONObject> data = CommonFileUtil.getData("/initdata/uino_data_permission_function.json",
                    JSONObject.class);
            for (JSONObject jsonObject : data) {
                jsonObject.getString("uid");
                jsonObject.getString("name");
                jsonObject.getString("dataValue");
                SysRoleDataModuleRlt sysRoleDataModuleRlt = new SysRoleDataModuleRlt();
                sysRoleDataModuleRlt.setDataModuleCode("BUTTON");
                sysRoleDataModuleRlt.setRoleId(roleId);
                sysRoleDataModuleRlt.setUid(jsonObject.getString("uid"));
                sysRoleDataModuleRlt.setDataValue(jsonObject.getString("dataValue"));
                sysRoleDataModuleRlt.setIssee(1);
                sysRoleDataModuleRlts.add(sysRoleDataModuleRlt);
            }
            // 保存
            rdRltSvc.saveOrUpdateBatch(sysRoleDataModuleRlts);
            dm = rdRltSvc.getListByRoleIds(roleIds, moduleCodes);
        }

        return mergeRole(dm, new ArrayList<SysUserDataModuleRlt>());
    }

    @Override
    public List<SysRoleDataModuleRlt> getAuthDataRoleByUserId(CAuthBean bean) {
        BinaryUtils.checkEmpty(bean.getUserId(), "userId");
        BinaryUtils.checkEmpty(bean.getDataModuleCode(), "dataModuleCode");
        List<String> moduleCodes = new ArrayList<String>();
        moduleCodes.add(bean.getDataModuleCode());
        Set<Long> roleIds = roleSvc.getRoleIdsByUserId(bean.getUserId());
        // 用户下的数据权限
        List<SysUserDataModuleRlt> userDMList = udRltSvc.getListByUserId(bean.getUserId(), moduleCodes);

        // 角色下的数据权限
        List<SysRoleDataModuleRlt> roleDMList = rdRltSvc.getListByRoleIds(roleIds, moduleCodes);

        return mergeRole(roleDMList, userDMList);
    }

    @Override
    public List<SysRoleModuleRlt> getAuthMenuByUserId(Long userId) {
        BinaryUtils.checkEmpty(userId, "userId");
        Set<Long> roleIds = roleSvc.getRoleIdsByUserId(userId);
        // 合并菜单
        List<SysRoleModuleRlt> roleMenus = rmRltSvc.getListByRoleIds(roleIds);
        List<SysUserModuleRlt> uMenus = umRltSvc.getListByUserId(userId);
        List<SysRoleModuleRlt> ums = BeanUtil.converBean(uMenus, SysRoleModuleRlt.class);
        roleMenus.addAll(ums);
        return roleMenus;
    }

    /**
     * 数据权限合并
     * 
     * @return
     */
    private List<SysRoleDataModuleRlt> mergeRole(List<SysRoleDataModuleRlt> roleDMList, List<SysUserDataModuleRlt> userDMList) {

        List<SysRoleDataModuleRlt> dataModules = new ArrayList<SysRoleDataModuleRlt>();

        Map<String, SysRoleDataModuleRlt> map = new HashMap<String, SysRoleDataModuleRlt>();
        List<SysRoleDataModuleRlt> ulist = BeanUtil.converBean(userDMList, SysRoleDataModuleRlt.class);
        roleDMList.addAll(ulist);

        for (SysRoleDataModuleRlt rlt : roleDMList) {
            String key = rlt.getDataModuleCode() + "xx@@@@xx" + rlt.getDataValue();
            if (map.get(key) == null) {
                map.put(key, rlt);
            } else {
                // 新的对象
                int d = rlt.getIsdelete() == null ? 0 : rlt.getIsdelete();
                int r = rlt.getIssee() == null ? 0 : rlt.getIssee();
                int u = rlt.getIsupdate() == null ? 0 : rlt.getIsupdate();
                int c = rlt.getIscreate() == null ? 0 : rlt.getIscreate();
                // 原有对象
                SysRoleDataModuleRlt dm = map.get(key);
                int dd = dm.getIsdelete() == null ? 0 : dm.getIsdelete();
                int dr = dm.getIssee() == null ? 0 : dm.getIssee();
                int du = dm.getIsupdate() == null ? 0 : dm.getIsupdate();
                int dc = dm.getIscreate() == null ? 0 : dm.getIscreate();
                if (d > dd) {
                    dm.setIsdelete(d);
                }
                if (r > dr) {
                    dm.setIssee(r);
                }
                if (u > du) {
                    dm.setIsupdate(u);
                }
                if (c > dc) {
                    dm.setIscreate(c);
                }

            }
        }

        Iterator<String> it = map.keySet().iterator();
        while (it.hasNext()) {
            String key = it.next();
            SysRoleDataModuleRlt dm = map.get(key);
            dataModules.add(dm);
        }
        return dataModules;
    }

    @Override
    public List<SysRole> getRolesByUserId(Long userId) {
        Assert.notNull(userId, "USER_NOT_NULL");
        List<SysUserRoleRlt> userRoleMappings = commSvc.getUserRoleRltByUserIds(Collections.singleton(userId));
        // 正常触发不了,由于未提供getlist等方法，固定最多获取3000，若发现符合条件总条数大于3000，则直接断言
        if (BinaryUtils.isEmpty(userRoleMappings)) {
            return new LinkedList<>();
        }
        Set<Long> roleIds = new HashSet<>();
        userRoleMappings.forEach(mapping -> {
            roleIds.add(mapping.getRoleId());
        });
        Page<SysRole> roles = roleSvc.getListByQuery(1, roleIds.size(), QueryBuilders.termsQuery("id", roleIds));
        if (roles.getData().size() != roleIds.size()) {
            // 代表用户对应角色关系中存在无效角色（脏数据），不主动清理，打印error日志，让管理员自己处理分析
            log.error("用户id【{}】对应角色列表【{}】有无效数据,请管理员检查", userId, JSON.toJSONString(roleIds));
        }
        return roles.getData();
    }

    @Override
	@SuppressWarnings("all")
    public Object getDataModuleDataById(Long dataModuleId) {
        Assert.notNull(dataModuleId, "PARAM_NOT_NULL");
        ServletRequestAttributes attrs = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        SysDataModule dataModule = dataModuleSvc.getById(dataModuleId);
        Assert.notNull(dataModule, "BS_MVTYPE_NOT_EXIST");
        // String sendRequestUrl = dataModule.getDataSourceUrl();

        String dataModuleCode = dataModule.getDataModuleCode();
        Assert.notNull(dataModuleCode, "BS_MVCODE_NOT_EXIST");
        log.info("#######数据权限类型code:{}", dataModuleCode);
        SysDataModuleEnum type = SysDataModuleEnum.getType(dataModuleCode);
        Long domainId = SysUtil.getCurrentUserInfo().getDomainId();
        switch (type) {
            case CI_TAG:
                return this.getDataRoleByCITag(domainId);
            case A_BUTTON:
                return FileUtil.getData("/initdata/uino_data_permission_function.json", Object.class);
            case CI_CLASS:
                return this.getDataRoleByCIClass(domainId);
            default:
                throw new RuntimeException("数据模块【" + dataModuleCode + "】功能未实现");
        }

        /*log.debug("接收到数据模块【{}】的数据转发请求，即将发送post请求至【{}】", dataModuleId, sendRequestUrl);
        HttpHeaders headers = new HttpHeaders();
        headers.add("token", TokenUtil.getCurrentThreadToken());
        headers.add("tk", TokenUtil.getCurrentThreadToken());
        headers.add("Authorization", "Bearer " + TokenUtil.getCurrentThreadToken());
        headers.add("Content-Type", "application/json");
        headers.add(HttpHeaders.COOKIE, attrs.getRequest().getHeader(HttpHeaders.COOKIE));
		Object returnVal = null;
		DefaultHttpClient httpClient = new DefaultHttpClient();
		try {
			HttpUriRequest httpReq = new HttpPost(sendRequestUrl);
			httpReq.setHeader("token", TokenUtil.getCurrentThreadToken());
			httpReq.setHeader("tk", TokenUtil.getCurrentThreadToken());
			httpReq.setHeader("Authorization", "Bearer " + TokenUtil.getCurrentThreadToken());
			httpReq.setHeader("Content-Type", "application/json");
			httpReq.setHeader(HttpHeaders.COOKIE, attrs.getRequest().getHeader(HttpHeaders.COOKIE));
			ResponseHandler<String> responseHandler = new BasicResponseHandler();
			String reponse = httpClient.execute(httpReq, responseHandler);
			JSONObject bodyObject = JSON.parseObject(reponse);
			returnVal = bodyObject.get("data");
			log.debug("转发返回数据为【{}】", bodyObject);
		} catch (Exception e) {
			throw new RuntimeException(e);
		} finally {
			httpClient.getConnectionManager().shutdown();
		}

		return returnVal;*/
    }

    @Override
    public List<SysRoleDataModuleRlt> getUserAuthDataRoleByUserId(CAuthBean bean) {
        BinaryUtils.checkEmpty(bean.getUserId(), "userId");
        BinaryUtils.checkEmpty(bean.getDataModuleCode(), "dataModuleCode");
        List<String> moduleCodes = new ArrayList<String>();
        moduleCodes.add(bean.getDataModuleCode());
        // 用户下的数据权限
        List<SysUserDataModuleRlt> userDMList = udRltSvc.getListByUserId(bean.getUserId(), moduleCodes);
        return mergeRole(new ArrayList<SysRoleDataModuleRlt>(), userDMList);
    }

    @Override
    public List<SysRoleDataModuleRlt> getRoleAuthDataRoleByUserId(CAuthBean bean) {
        BinaryUtils.checkEmpty(bean.getUserId(), "userId");
        BinaryUtils.checkEmpty(bean.getDataModuleCode(), "dataModuleCode");
        List<String> moduleCodes = new ArrayList<String>();
        moduleCodes.add(bean.getDataModuleCode());
        Set<Long> roleIds = roleSvc.getRoleIdsByUserId(bean.getUserId());

        // 角色下的数据权限
        List<SysRoleDataModuleRlt> roleDMList = rdRltSvc.getListByRoleIds(roleIds, moduleCodes);

        return mergeRole(roleDMList, new ArrayList<SysUserDataModuleRlt>());
    }

    @Override
    public List<SysRole> getRolesByOrgId(Long orgId) {
        Assert.notNull(orgId, "USER_NOT_NULL");
        List<SysOrgRoleRlt> orgRoleMappings = commSvc.getOrgRoleRltByOrgIds(Collections.singleton(orgId));
        // 正常触发不了,由于未提供getlist等方法，固定最多获取3000，若发现符合条件总条数大于3000，则直接断言
        if (BinaryUtils.isEmpty(orgRoleMappings)) {
            return new LinkedList<>();
        }
        Set<Long> roleIds = new HashSet<>();
        orgRoleMappings.forEach(mapping -> {
            roleIds.add(mapping.getRoleId());
        });
        Page<SysRole> roles = roleSvc.getListByQuery(1, roleIds.size(), QueryBuilders.termsQuery("id", roleIds));
        if (roles.getData().size() != roleIds.size()) {
            // 代表用户对应角色关系中存在无效角色（脏数据），不主动清理，打印error日志，让管理员自己处理分析
            log.error("用户id【{}】对应角色列表【{}】有无效数据,请管理员检查", orgId, JSON.toJSONString(roleIds));
        }
        return roles.getData();
    }

    @Override
    public long countByCondition(QueryBuilder query) {
        return roleSvc.countByCondition(query);
    }

    @Override
    public List<SysRole> getRolesByQuery(QueryBuilder query) {
        return roleSvc.getListByQuery(query);
    }

    @Override
    public void optionUserModuleAuth(OptionUserModuleAuthRequestDto req) {
        ValidDtoUtil.valid(req);
        Set<Long> userIds = req.getUserIds();
        Set<Long> moduleIds = req.getModuleIds();
        // 先删后绑，解除操作就只删
        boolean mapping = req.getMapping().booleanValue();
        umRltSvc.deleteByQuery(QueryBuilders.boolQuery().must(QueryBuilders.termsQuery("userId", userIds)).must(QueryBuilders.termsQuery("moduleId", moduleIds)), true);
        if (mapping) {
            List<SysUserModuleRlt> saveDatas = new LinkedList<>();
            userIds.forEach(userId -> {
                moduleIds.forEach(moduleId -> saveDatas.add(SysUserModuleRlt.builder().userId(userId).moduleId(moduleId).build()));
            });
            umRltSvc.saveOrUpdateBatch(saveDatas);
        }
    }

    @Override
    public List<SysUserRoleRlt> getUserRoleRltByRoleId(Long roleId) {
        return commSvc.getUserRoleRltByRoleIds(Collections.singleton(roleId));
    }

    @Override
    public List<SysRoleDataModuleRlt> getRoleDataModuleRltByCdt(CSysRoleDataModuleRlt cdt) {
        cdt = cdt == null ? new CSysRoleDataModuleRlt() : cdt;
        if (BinaryUtils.isEmpty(cdt.getDomainId())) {
            cdt.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
        }
        return rdRltSvc.getListByCdt(cdt);
    }

    @Override
    public Integer deleteRoleDataModuleRlt(CSysRoleDataModuleRlt cdt) {
        cdt = cdt == null ? new CSysRoleDataModuleRlt() : cdt;
        if(BinaryUtils.isEmpty(cdt.getDomainId())){
            cdt.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
        }
        BoolQueryBuilder query = ESUtil.cdtToBuilder(cdt);
        return rdRltSvc.deleteByQuery(query, true);
    }

    @Override
    public List<SysRole> getRoleListByIds(List<Long> ids,Long domainId) {
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termsQuery("id", ids));
        query.must(QueryBuilders.termQuery("domainId",domainId));
        return roleSvc.getListByQuery(query);
    }

    @Override
    public List<SysRoleDataModuleRlt> getRoleByUserAndCode(Long userId, String code) {
        if(userId == null){
            userId = SysUtil.getCurrentUserInfo().getId();
        }
        CAuthBean bean = new CAuthBean();
        bean.setUserId(userId);
        bean.setDataModuleCode(code);
        List<SysRoleDataModuleRlt> dataModules = this.getAuthDataRoleByUserId(bean);
        return CollectionUtils.isEmpty(dataModules)?new ArrayList<>() : dataModules;
    }

    @Override
    public void sanyuanCondition(BoolQueryBuilder query,String type) {
        SysRole securityManagerRole = roleSvc.getByName(1L, "安全管理员");
        SysRole securityAuditManagerRole = roleSvc.getByName(1L, "安全审计员");
        SysRole systemManagerRole = roleSvc.getByName(1L, "系统管理员");
        if (securityManagerRole != null && securityAuditManagerRole != null) {
            //获取当前用户的角色，仅展示安全管理员和系统管理员用户的操作日志，不能增删改日志内容
            List<SysRole> rolesByUserId = getRolesByUserId(SysUtil.getCurrentUserInfo().getId());
            boolean securityManager = false;
            boolean securityAudit = false;
            for (SysRole sysRole : rolesByUserId) {
                if ("安全管理员".equalsIgnoreCase(sysRole.getRoleName())) {
                    securityManager = true;
                } else if ("安全审计员".equalsIgnoreCase(sysRole.getRoleName())) {
                    securityAudit = true;
                }
            }
            SysRole adminRole = roleSvc.getByName(1L, "admin");
            Set<String> loginCodes = new HashSet<>();
            if (securityManager && !securityAudit) {
                //安全管理员，一般用户和安全审计员，不能增删改日志内容。
                List<SysUserRoleRlt> userRoleRltByRoleId = getUserRoleRltByRoleId(securityManagerRole.getId());
                List<SysUserRoleRlt> systemManagerRoleRlt = getUserRoleRltByRoleId(systemManagerRole.getId());
                List<SysUserRoleRlt> adminRoleRlt = getUserRoleRltByRoleId(adminRole.getId());
                if (!CollectionUtils.isEmpty(systemManagerRoleRlt)) {
                    userRoleRltByRoleId.addAll(systemManagerRoleRlt);
                }
                if (!CollectionUtils.isEmpty(adminRoleRlt)) {
                    userRoleRltByRoleId.addAll(adminRoleRlt);
                }
                loginCodes.addAll(getConditionUser(userRoleRltByRoleId));
                loginCodes.add("system");
                if ("ciOperate".equalsIgnoreCase(type)) {
                    query.mustNot(QueryBuilders.termsQuery("operator.keyword", loginCodes));
                } else {
                    query.mustNot(QueryBuilders.termsQuery("userCode.keyword", loginCodes));
                }
            } else if (securityAudit && !securityManager) {
                //安全审计员，安全管理员和系统管理员
                List<SysUserRoleRlt> userRoleRltByRoleId = getUserRoleRltByRoleId(securityManagerRole.getId());
                List<SysUserRoleRlt> systemManagerRoleRlt = getUserRoleRltByRoleId(systemManagerRole.getId());
                if (!CollectionUtils.isEmpty(systemManagerRoleRlt)) {
                    userRoleRltByRoleId.addAll(systemManagerRoleRlt);
                }
                loginCodes.addAll(getConditionUser(userRoleRltByRoleId));
                if("ciOperate".equalsIgnoreCase(type)){
                    query.must(QueryBuilders.termsQuery("operator.keyword", loginCodes));
                }else {
                    query.must(QueryBuilders.termsQuery("userCode.keyword", loginCodes));
                }
            }
        }
    }

    private Set<String> getConditionUser(List<SysUserRoleRlt> userRoleRltByRoleId) {
        Set<Long> operators = new HashSet<>();
        for (SysUserRoleRlt sysUserRoleRlt : userRoleRltByRoleId) {
            operators.add(sysUserRoleRlt.getUserId());
        }
        List<SysUser> sysUsers = userSvc.getByIds(operators);
        return sysUsers.stream().map(SysUser::getLoginCode).collect(Collectors.toSet());
    }
}
