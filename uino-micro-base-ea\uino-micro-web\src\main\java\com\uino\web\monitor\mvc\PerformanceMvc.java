package com.uino.web.monitor.mvc;

import java.util.List;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import com.uino.bean.permission.base.SysUser;
import com.uino.util.sys.SysUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.binary.core.io.Resource;
import com.binary.framework.util.ControllerUtils;
import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uino.api.client.monitor.IPerformanceApiSvc;
import com.uino.bean.chart.bean.UinoChartDataBean;
import com.uino.bean.monitor.base.SimulationRuleInfo;
import com.uino.bean.monitor.buiness.ImportPerformanceReqDto;
import com.uino.bean.monitor.buiness.PerformanceQueryDto;
import com.uino.bean.tp.base.FinalPerformanceDTO;
import com.uino.init.api.ApiResult;
import com.uino.init.api.ApiVersion;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 性能mvc
 * 
 * <AUTHOR>
 *
 */
@ApiVersion(1)
@Api(value = "模拟性能", tags = { "模拟性能" })
@RestController
@RequestMapping("/monitor/performance")
public class PerformanceMvc {

	@Autowired
	private IPerformanceApiSvc performanceApi;

	/**
	 * 导入性能
	 * 
	 * @param file
	 * @param objId
	 * @param objType
	 * @param request
	 * @param response
	 */
	@PostMapping("importPerformance")
	@ApiOperation(value = "导入性能文件")
	@ModDesc(desc = "导入性能", pDesc = "性能对象信息", rDesc = "导入结果", rType = Boolean.class)
	public ApiResult<Boolean> importPerformance(@RequestParam("file") MultipartFile file,
			ImportPerformanceReqDto importDto, HttpServletRequest request, HttpServletResponse response) {
		SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
		importDto.setDomainId(currentUserInfo.getDomainId());
		performanceApi.importPerformance(file, importDto);
		return ApiResult.ok(this).data(true);
	}

	/**
	 * 导入性能
	 * 
	 * @param reqDto
	 * @param request
	 * @param response
	 */
	@PostMapping("importPerformanceByContent")
	@ApiOperation(value = "导入性能数据")
	@ModDesc(desc = "导入性能", pDesc = "性能对象信息", rDesc = "导入结果", rType = Boolean.class)
	public ApiResult<Boolean> importPerformanceByContent(@RequestBody ImportPerformanceReqDto reqDto,
			HttpServletRequest request, HttpServletResponse response) {
		reqDto = reqDto == null ? new ImportPerformanceReqDto() : reqDto;
		SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
		reqDto.setDomainId(currentUserInfo.getDomainId());
		performanceApi.importPerformance(reqDto);
		return ApiResult.ok(this).data(true);
	}

	/**
	 * 导出性能模板
	 * 
	 * @param objId
	 * @param objType
	 * @param request
	 * @param response
	 */
	@PostMapping("exportPerformanceTemplate")
	@ApiOperation(value = "导出性能模板")
	@ModDesc(desc = "导出性能", pDesc = "导出模板对象信息", rDesc = "输出流", rType = Resource.class)
	public void exportPerformanceTemplate(@RequestParam("objId") Long objId, @RequestParam("objType") Integer objType,
			HttpServletRequest request, HttpServletResponse response) {
		SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
		Resource res = performanceApi.exportPerformanceTemplate(currentUserInfo.getDomainId(), objId, objType);
		ControllerUtils.returnResource(request, response, res);
	}

	/**
	 * 查询性能
	 * 
	 * @param queryDto
	 * @param request
	 * @param response
	 * @return
	 */
	@PostMapping("searchPerformance")
	@ApiOperation(value = "查询性能数据")
	@ModDesc(desc = "查询性能", pDesc = "查询条件", rDesc = "查询结果", rType = Page.class)
	public ApiResult<Page<FinalPerformanceDTO>> searchPerformance(@RequestBody PerformanceQueryDto queryDto,
			HttpServletRequest request, HttpServletResponse response) {
		SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
		queryDto.setDomainId(currentUserInfo.getDomainId());
		Page<FinalPerformanceDTO> res = performanceApi.searchPerformance(queryDto);
		return ApiResult.ok(this).data(res);
	}

	@PostMapping("searchPerformanceGraph")
	@ApiOperation(value = "查询性能数据历史曲线图")
	@ModDesc(desc = "查询性能数据历史曲线图", pDesc = "查询条件", rDesc = "查询结果", rType = UinoChartDataBean.class)
	public ApiResult<UinoChartDataBean<List<Double>>> searchPerformanceGraph(@RequestBody PerformanceQueryDto queryDto,
			HttpServletRequest request, HttpServletResponse response) {
		SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
		queryDto.setDomainId(currentUserInfo.getDomainId());
		UinoChartDataBean<List<Double>> res = performanceApi.searchPerformanceGraph(queryDto);
		return ApiResult.ok(this).data(res);
	}

	@PostMapping("searchNoCiPerformance")
	@ApiOperation(value = "查询未关联ci的性能数据")
	@ModDesc(desc = "查询未关联ci的性能数据", pDesc = "查询条件", rDesc = "查询结果", rType = Page.class)
	public ApiResult<Page<FinalPerformanceDTO>> searchNoCiPerformance(@RequestBody PerformanceQueryDto queryDto,
			HttpServletRequest request, HttpServletResponse response) {
		SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
		queryDto.setDomainId(currentUserInfo.getDomainId());
		Page<FinalPerformanceDTO> res = performanceApi.searchNoCiPerformance(queryDto);
		return ApiResult.ok(this).data(res);
	}

	/**
	 * 查询性能数据标签
	 * 
	 * @author: weixuesong
	 * @date: 2021/2/24 17:33
	 * @param classId
	 * @return: java.util.List<java.lang.String>
	 */
	@PostMapping("getPerfDataLabel")
	@ApiOperation(value = "查询性能标签")
	public ApiResult<List<String>> getPerfDataLabel(HttpServletRequest request, HttpServletResponse response,
			@RequestBody Long classId) {
		List<String> res = performanceApi.getPerfDataLabel(classId);
		return ApiResult.ok(this).data(res);
	}

	@PostMapping("simulationPerformance")
	@ApiOperation(value = "模拟性能历史数据")
	public ApiResult<Boolean> simulationPerformance(HttpServletRequest request, HttpServletResponse response,
			@RequestBody SimulationRuleInfo bean) {
		SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
		bean.setDomainId(currentUserInfo.getDomainId());
		performanceApi.simulationPerformance(bean);
		return ApiResult.ok(this).data(true);
	}

}
