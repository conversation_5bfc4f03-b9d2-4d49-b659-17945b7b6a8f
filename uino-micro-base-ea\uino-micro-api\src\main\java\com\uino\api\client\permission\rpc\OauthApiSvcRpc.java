package com.uino.api.client.permission.rpc;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import com.uino.bean.permission.base.OAuthClientDetail;
import com.uino.bean.permission.base.OauthRefreshTokenDetail;
import com.uino.bean.permission.base.OauthResourceDetail;
import com.uino.bean.permission.base.OauthTokenDetail;
import com.uino.bean.permission.business.request.RegisterClientReq;
import com.uino.provider.feign.permission.OauthFeign;
import com.uino.api.client.permission.IOauthApiSvc;

@Service
@Primary
public class OauthApiSvcRpc implements IOauthApiSvc {

    @Autowired
    private OauthFeign feign;

    @Override
    public OAuthClientDetail getClientInfoByCode(String clientCode) {
        // TODO Auto-generated method stub
        return feign.getClientInfoByCode(clientCode);
    }

    @Override
    public void register(RegisterClientReq req) {
        // TODO Auto-generated method stub
        feign.register(req);
    }

    @Override
    public void removeClientInfoByCode(String clientCode) {
        // TODO Auto-generated method stub
        feign.removeClientInfoByCode(clientCode);
    }

    @Override
    public List<OAuthClientDetail> getAllClents() {
        // TODO Auto-generated method stub
        return feign.getAllClents();
    }

    @Override
    public OauthResourceDetail getResourceDetail(String name) {
        // TODO Auto-generated method stub
        return feign.getResourceDetail(name);
    }

    @Override
    public Long persistenceToken(OauthTokenDetail tokenDetail) {
        // TODO Auto-generated method stub
        return feign.persistenceToken(tokenDetail);
    }

    @Override
    public OauthTokenDetail getTokenDetailByCode(String tokenCode) {
        // TODO Auto-generated method stub
        return feign.getTokenDetailByCode(tokenCode);
    }

    @Override
    public OauthTokenDetail getTokenDetailByAuthId(String authId) {
        // TODO Auto-generated method stub
        return feign.getTokenDetailByAuthId(authId);
    }

    @Override
    public List<OauthTokenDetail> getTokenDetail(String clientId, String userLoginName) {
        // TODO Auto-generated method stub
        return feign.getTokenDetail(clientId, userLoginName);
    }

    @Override
    public void delByCode(String tokenCode) {
        // TODO Auto-generated method stub
        feign.delByCode(tokenCode);
    }

    @Override
    public void delByRefreshTokenCode(String reTokenCode) {
        // TODO Auto-generated method stub
        feign.delByRefreshTokenCode(reTokenCode);
    }

    @Override
    public Long persistenceRefreshToken(OauthRefreshTokenDetail reRokenDetail) {
        // TODO Auto-generated method stub
        return feign.persistenceRefreshToken(reRokenDetail);
    }

    @Override
    public OauthRefreshTokenDetail getRefreshTokenDetail(String tokenCode) {
        // TODO Auto-generated method stub
        return feign.getRefreshTokenDetail(tokenCode);
    }

    @Override
    public void delRefreshTokenByCode(String tokenCode) {
        // TODO Auto-generated method stub
        feign.delRefreshTokenByCode(tokenCode);
    }

    @Override
    public OauthTokenDetail getTokenDetailByReTokenCode(String reTokenCode) {
        // TODO Auto-generated method stub
        return feign.getTokenDetailByReTokenCode(reTokenCode);
    }

	@Override
	public boolean save(OauthResourceDetail saveDto) {
		// TODO Auto-generated method stub
		return feign.save(saveDto);
	}

	@Override
	public List<OauthResourceDetail> list() {
		// TODO Auto-generated method stub
		return feign.list();
	}
}
