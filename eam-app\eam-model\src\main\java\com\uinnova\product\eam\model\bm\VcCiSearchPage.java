package com.uinnova.product.eam.model.bm;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.vmdb.provider.search.bean.CcCiClassObj;
import com.uinnova.product.vmdb.provider.search.bean.CcCiObj;


public class VcCiSearchPage implements Serializable {
	private static final long serialVersionUID = 1L;
	
	@Comment("页码")
	private long pageNum;
	
	@Comment("每页数量")
	private long pageSize;
	
	@Comment("总记录数")
	private long totalRows;
	
	@Comment("总页数")
	private long totalPages;
	
	@Comment("CI记录, key=word")
	private Map<String,List<CcCiObj>> data;
	
	@Comment("CI分类集合, key=classId")
	private Map<Long, CcCiClassObj> classMp;
	

	public VcCiSearchPage() {
	}
	
	
	public VcCiSearchPage(long pageNum, long pageSize, Map<String,List<CcCiObj>> data) {
		this.pageNum = pageNum;
		this.pageSize = pageSize;
		this.data = data;
	}
	
	
	public VcCiSearchPage(long pageNum, long pageSize, Map<String,List<CcCiObj>> data,Map<Long, CcCiClassObj> classMp) {
		this.pageNum = pageNum;
		this.pageSize = pageSize;
		this.data = data;
		this.classMp = classMp;
	}
	
	
	public VcCiSearchPage(long pageNum, long pageSize, long totalRows, long totalPages, Map<String,List<CcCiObj>> data) {
		this.pageNum = pageNum;
		this.pageSize = pageSize;
		this.totalRows = totalRows;
		this.totalPages = totalPages;
		this.data = data;
	}
	
	
	
	
	
	public Map<Long, CcCiClassObj> getClassMp() {
		return classMp;
	}


	public void setClassMp(Map<Long, CcCiClassObj> classMp) {
		this.classMp = classMp;
	}


	public long getPageNum() {
		return pageNum;
	}
	public void setPageNum(long pageNum) {
		this.pageNum = pageNum;
	}
	public long getPageSize() {
		return pageSize;
	}
	public void setPageSize(long pageSize) {
		this.pageSize = pageSize;
	}
	public long getTotalRows() {
		return totalRows;
	}
	public void setTotalRows(long totalRows) {
		this.totalRows = totalRows;
	}
	public long getTotalPages() {
		return totalPages;
	}
	public void setTotalPages(long totalPages) {
		this.totalPages = totalPages;
	}

	public Map<String, List<CcCiObj>> getData() {
		return data;
	}

	public void setData(Map<String, List<CcCiObj>> data) {
		this.data = data;
	}

	
	
	
	
	
	
	
	
}
