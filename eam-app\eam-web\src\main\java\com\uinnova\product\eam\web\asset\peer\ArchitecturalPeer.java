package com.uinnova.product.eam.web.asset.peer;

import com.binary.core.exception.BinaryException;
import com.uinnova.product.eam.api.IArchitecturalApiClient;
import com.uinnova.product.eam.base.model.AttrConfInfo;
import com.uinnova.product.eam.base.model.AttrDefInfo;
import com.uinnova.product.eam.base.model.FileResourceMeta;
import com.uinnova.product.eam.base.util.EamUtil;
import com.uinnova.product.eam.model.asset.ArchitecturalDTO;
import com.uinnova.product.eam.model.asset.ArchitecturalNameCheckDTO;
import com.uinnova.product.eam.model.asset.ArchitecturalResolutionDTO;
import com.uinnova.product.eam.model.asset.SearchArchitecturalDTO;
import com.uinnova.product.eam.web.asset.bean.ArchitecturalNameCheckVo;
import com.uinnova.product.eam.web.asset.bean.ArchitecturalResolutionVO;
import com.uinnova.product.eam.web.asset.bean.ArchitecturalVo;
import com.uinnova.product.eam.web.asset.bean.SearchArchitecturalVo;
import com.uino.bean.cmdb.base.ESCIInfo;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;

@Service
public class ArchitecturalPeer {

    @Resource
    private IArchitecturalApiClient architecturalApiClient;

    private String architecturalClassName="架构决议";

    public Long createArchitecturalResolution(ArchitecturalVo architecturalVo){
        ArchitecturalDTO architecturalDTO = EamUtil.copy(architecturalVo, ArchitecturalDTO.class);
        return architecturalApiClient.createArchitecturalResolution(architecturalDTO);
    }

    public List<Map<String,Object>> getInfoBySubsystemCode(ArchitecturalResolutionVO architecturalResolutionVO){
        ArchitecturalResolutionDTO architecturalResolutionDTO = EamUtil.copy(architecturalResolutionVO,ArchitecturalResolutionDTO.class);
        return architecturalApiClient.getInfoBySubsystemCode(architecturalResolutionDTO);
    }

    public ArchitecturalResolutionDTO getInfoByCiId(long ciId){
        return architecturalApiClient.getInfoByCiId(ciId);
    }

    public AttrDefInfo selectAttrConf(AttrConfInfo attrConfInfo) {
        AttrDefInfo data = architecturalApiClient.selectAttrConf(attrConfInfo);
        if(data == null){
            return null;
        }
        return data;
    }

    public boolean checkArchitecturalName(ArchitecturalNameCheckVo architecturalNameCheckVo) {
        ArchitecturalNameCheckDTO architecturalNameCheckDTO = EamUtil.copy(architecturalNameCheckVo,ArchitecturalNameCheckDTO.class);
        architecturalNameCheckDTO.setArchitecturalClassName(architecturalClassName);
        return architecturalApiClient.checkArchitecturalName(architecturalNameCheckDTO);
    }

    public ESCIInfo getArchitecturalById(SearchArchitecturalVo searchArchitecturalVo) {
        SearchArchitecturalDTO searchArchitecturalDTO = EamUtil.copy(searchArchitecturalVo,SearchArchitecturalDTO.class);
        searchArchitecturalDTO.setArchitecturalClassName(architecturalClassName);
        return architecturalApiClient.searchArchitecturalDTO(searchArchitecturalDTO);
    }

    public List<Long> getResIdsById(Long architecturalId) {
        return architecturalApiClient.getResIdsById(architecturalId);
    }

    public List<FileResourceMeta> download(List<Long> resIds) {
        return architecturalApiClient.download(resIds);
    }

    public List<Long> changeSubsystemCode(String sourceCode, String targetCode, Long ciClassIdByName) throws BinaryException {
        return architecturalApiClient.changeSubsystemCode(sourceCode,targetCode,ciClassIdByName);
    }
}
