package com.uinnova.product.vmdb.comm.doc.api;

import java.io.Serializable;
import java.util.List;

/**
 * 
 * <AUTHOR>
 *
 */
public class FieldDesc implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 字段名 **/
    private String name;

    /** 字段类型 **/
    private String type;

    /** 字段描述 **/
    private String desc;

    /** 数据类型 (1=基础数据, 2=数组, 3=对象, 4=对象数组) **/
    private Integer dtype;

    /** 字段描述 **/
    private List<FieldDesc> childs;

    public FieldDesc() {
    }

    public FieldDesc(String name, String type, Integer dtype, String desc) {
        this.name = name;
        this.type = type;
        this.dtype = dtype;
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public List<FieldDesc> getChilds() {
        return childs;
    }

    public void setChilds(List<FieldDesc> childs) {
        this.childs = childs;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getDtype() {
        return dtype;
    }

    public void setDtype(Integer dtype) {
        this.dtype = dtype;
    }

}
