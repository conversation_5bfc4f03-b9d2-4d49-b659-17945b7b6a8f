package com.uinnova.product.eam.model.dto;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.base.util.EamUtil;
import com.uinnova.product.eam.model.enums.AnalyseLeaf;
import com.uinnova.product.eam.model.vo.EamAnalyseCiVo;
import com.uinnova.product.vmdb.comm.model.rlt.CcCiRlt;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uinnova.product.vmdb.provider.rlt.bean.CcCiRltInfo;
import lombok.Data;

/**
 * 批量处理多层级关联分析
 * <AUTHOR>
 * @date 2022/6/8
 */
@Data
public class DataAnalyzeBatch {
    @Comment("源端ciCode")
    private String sourceCiCode;
    @Comment("源端ci")
    private EamAnalyseCiVo sourceCi;
    @Comment("目标端ciCode")
    private String targetCiCode;
    @Comment("目标端ci")
    private EamAnalyseCiVo targetCi;
    @Comment("关系")
    private CcCiRlt rlt;
    @Comment("关系分类")
    private CcCiClassInfo rltClass;
    @Comment("方向")
    private AnalyseLeaf leaf;
    @Comment("层级")
    private Integer level;

    public DataAnalyzeBatch(CcCiRltInfo rltInfo){
        this.rlt = rltInfo.getCiRlt();
        this.sourceCi = EamUtil.copy(rltInfo.getSourceCiInfo(), EamAnalyseCiVo.class);
        this.sourceCiCode = rltInfo.getSourceCiInfo().getCi().getCiCode();
        this.targetCi = EamUtil.copy(rltInfo.getTargetCiInfo(), EamAnalyseCiVo.class);
        this.targetCiCode = rltInfo.getTargetCiInfo().getCi().getCiCode();
    }
}
