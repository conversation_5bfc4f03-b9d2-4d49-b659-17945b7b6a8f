package com.uinnova.product.eam.model.vo;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

/**
 * 关联视图查询参数
 */
@Data
public class RelateionDiagramVo {

    @Comment("视图名称")
    private String diagramName;
    @Comment("视图类型：0-自由视图，1-全部类型, 非0-制品id")
    private String viewType;
    @Comment("视图来源：0-我的空间，1-资产仓库")
    private Integer location;
    @Comment("创建者：1-我创建的，2-与我协作，3-全部")
    private Integer owner;
    @Comment("资产分库目录id")
    private Long libraryId;
    private Integer pageNum = 1;
    private Integer pageSize = 20;
}
