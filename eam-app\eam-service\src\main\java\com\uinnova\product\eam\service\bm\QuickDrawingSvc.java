package com.uinnova.product.eam.service.bm;

import com.uinnova.product.eam.model.bm.QuickDrawingDto;
import com.uinnova.product.eam.model.bm.QuickDrawingResp;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;

import java.util.List;

/**
 * 业务建模自动成图
 * <AUTHOR>
 * @date 2022/5/10
 */
public interface QuickDrawingSvc {
    /**
     * 自动成图
     * @param dto 条件
     * @return ci对象集合
     */
    QuickDrawingResp quickDrawing(QuickDrawingDto dto);

    /**
     * 根据步骤获取规则
     * @param drawingDto 条件
     * @return ci对象集合
     */
    List<CcCiInfo> getRulesByStep(QuickDrawingDto drawingDto);
}
