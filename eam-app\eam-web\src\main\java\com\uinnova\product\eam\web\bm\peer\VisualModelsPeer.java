package com.uinnova.product.eam.web.bm.peer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.binary.core.exception.BinaryException;
import com.binary.core.lang.StringUtils;
import com.binary.core.util.BinaryUtils;
import com.binary.framework.exception.ServiceException;
import com.uinnova.product.eam.base.diagram.utils.FileFilterUtil;
import com.uinnova.product.eam.feign.workable.FlowableFeign;
import com.uinnova.product.eam.feign.workable.entity.*;
import com.uinnova.product.eam.model.constants.FlowableConstant;
import com.uinnova.product.eam.model.dto.VisualModelsDto;
import com.uinnova.product.eam.model.vo.ThumbnailVo;
import com.uinnova.product.eam.model.vo.VisualModelsVo;
import com.uinnova.product.eam.comm.model.es.ModelPanorama3D;
import com.uinnova.product.eam.service.IModelPanorama3DSvc;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClass;
import com.uinnova.product.vmdb.comm.model.ci.CcCiClass;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uino.api.client.cmdb.IVisualModelApiSvc;
import com.uino.api.client.permission.IOrgApiSvc;
import com.uino.api.client.permission.IUserApiSvc;
import com.uino.bean.cmdb.base.*;
import com.uino.bean.permission.base.SysOrg;
import com.uino.bean.permission.base.SysRole;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.permission.business.UserInfo;
import com.uino.bean.permission.query.CSysUser;
import com.uino.dao.cmdb.*;
import com.uino.dao.permission.ESRoleSvc;
import com.uino.dao.util.ESUtil;
import com.uino.service.cmdb.microservice.ICIClassSvc;
import com.uino.service.cmdb.microservice.IRltClassSvc;
import com.uino.service.util.FileUtil;
import com.uino.util.cache.ICacheService;
import com.uino.util.sys.SysUtil;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.io.IOException;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description: 元模型
 * @author: Lc
 * @create: 2021-11-04 10:49
 */
@Slf4j
@Service
public class VisualModelsPeer {

    // 编辑/取消编辑
    private static final Integer CANCEL_EDIT = 0;
    private static final Integer IS_EDIT = 1;

    private static final String VISUAL_MODEL_KEY = "VISUAL:MODEL:KEY:";

    @Autowired
    private ICacheService iCacheService;

    @Resource
    private IVisualModelApiSvc iVisualModelApiSvc;

    @Value("${local.resource.space}")
    private String localPath;

    @Autowired
    private IUserApiSvc userSvc;

    @Autowired
    private ESRoleSvc esRoleSvc;

    @Resource
    private IOrgApiSvc orgApiSvc;

    @Autowired
    private ICIClassSvc iciClassSvc;

    @Autowired
    private IRltClassSvc iRltClassSvc;

    @Autowired
    private ESVisualModelSvc visualModelSvc;

    @Autowired
    private ESVisualModelPrivateSvc visualModelPrivateSvc;

    @Resource
    private ESVisualModelHistorySvc esVisualModelHistorySvc;

    @Resource
    private ESRltInfoHistorySvc esRltInfoHistorySvc;

    @Resource
    private ESClassInfoHistorySvc esClassInfoHistorySvc;

    @Resource
    private FlowableFeign flowableFeign;

    @Resource
    private IModelPanorama3DSvc modelPanorama3DSvc;

    /**
     * 处理元模型编辑或取消编辑
     *
     * @param modelId
     * @param status
     * @return
     */
    public Map<String, Object> handlerVisualModels(Long modelId, Integer status) {
        if (modelId == null) {
            throw new ServiceException("元模型唯一标识不能为空!");
        }
        SysUser userInfo = SysUtil.getCurrentUserInfo();
        if (userInfo == null) {
            throw new ServiceException("用户不存在!");
        }
        Long sysUserId = userInfo.getId();
        Map<String, Object> resultMap = new HashMap<>();

        List<ESVisualModel> esVisualModels = iVisualModelApiSvc.queryVisualModels(userInfo.getDomainId());
        List<String> visKey = new ArrayList<>();
        for (ESVisualModel esVisualModel : esVisualModels) {
            visKey.add(VISUAL_MODEL_KEY + esVisualModel.getId());
        }

        if (Objects.equals(status, IS_EDIT)) {
            // 查看当前元模型是否上锁
            Object obj = iCacheService.getCache(VISUAL_MODEL_KEY + modelId);
            if (obj != null) {
                Long cacheUserId = Long.valueOf(String.valueOf(obj));
                if (Objects.equals(sysUserId, cacheUserId)) {
                    resultMap.put("status", 1);
                    return resultMap;
                } else {
                    UserInfo user = userSvc.getUserInfoById(cacheUserId);
                    if (user != null && !StringUtils.isEmpty(user.getLoginCode())) {
                        resultMap.put("name", user.getLoginCode());
                    }
                    resultMap.put("status", 0);
                    return resultMap;
                }
            }
            // 因为元模型有多条数据 编辑时后台逻辑仅能控制当前编辑数据 但其他数据对于其他用户依然是不可编辑状态 将所有元模型上锁
            for (String vis : visKey) {
                iCacheService.setCache(vis, sysUserId, 60000*10);
            }
            // redisUtil.set(VISUAL_MODEL_KEY + modelId, sysUserId, 600);
            resultMap.put("status", 1);
            return resultMap;
        } else if (Objects.equals(status, CANCEL_EDIT)) {
            Object obj = iCacheService.getCache(VISUAL_MODEL_KEY + modelId);
            if (obj != null) {
                Long cacheUserId = Long.valueOf(String.valueOf(obj));
                if (Objects.equals(sysUserId, cacheUserId)) {
                    // 解锁全部模型
                    for (String vis : visKey) {
                        iCacheService.delKey(vis);
                    }
                    resultMap.put("status", 1);
                    return resultMap;
                } else {
                    resultMap.put("status", 0);
                    return resultMap;
                }
            }
            // 删除首次进来设置的值
            Object first = iCacheService.getCache(VISUAL_MODEL_KEY + 0);
            if (first != null) {
                iCacheService.delKey(VISUAL_MODEL_KEY + 0);
            }
            resultMap.put("status", 1);
            return resultMap;
        } else {
            throw new ServiceException("参数异常!");
        }
    }

    /**
     * 导出
     *
     * @return
     */
    public boolean exportVisualModels() {
        SysUser userInfo = SysUtil.getCurrentUserInfo();
        if (userInfo == null) {
            throw new ServiceException("用户不存在!");
        }
        List<ESVisualModel> esVisualModels = iVisualModelApiSvc.queryVisualModels(userInfo.getDomainId());
        for (ESVisualModel model : esVisualModels) {
            boolean enable = model.getEnable();
            if (Objects.equals(enable, false)) {
                continue;
            }
            String json = model.getJson();
            String filePath = Paths.get(FileFilterUtil.parseFilePath(localPath + "/" + LocalDate.now()), UUID.randomUUID().toString() + ".json").toString();
            try {
                FileUtil.writeFile(filePath, json.getBytes());
            } catch (IOException e) {
                throw new ServiceException("导出失败");
            }
        }
        return true;
    }

    /**
     * 导入
     * @param dto
     * @return
     */
    public boolean importVisualModels(VisualModelsDto dto) {
        checkVisualModel(dto);
        try {
            /*MultipartFile jsonFile = dto.getJsonFile();
            // 获取原始名字
            String fileName = jsonFile.getOriginalFilename();
            // 获取后缀名
            String suffixName = fileName.substring(fileName.lastIndexOf("."));
            //先将.json文件转为字符串类型
            File file = new File("/" + fileName);
            //将MultipartFile类型转换为File类型
            FileUtils.copyInputStreamToFile(jsonFile.getInputStream(), file);
            String jsonString = FileUtils.readFileToString(file, "UTF-8");
            //如果是json或者txt文件
            if (".json".equals(suffixName)) {*/
            //再将json字符串转为实体类
            SysUser userInfo = SysUtil.getCurrentUserInfo();
            List<ESVisualModel> models = iVisualModelApiSvc.queryVisualModels(userInfo.getDomainId());
            for (ESVisualModel esVisualModel : models) {
                boolean enable = esVisualModel.getEnable();
                if (Objects.equals(enable, false)) {
                    continue;
                }
                Long id = esVisualModel.getId();
                if (!Objects.equals(id, dto.getVisualModelId())) {
                    throw new ServiceException("元模型数据错误!");
                }
                JSONArray originalJsonArr = JSONArray.parseArray(esVisualModel.getJson());
                JSONArray newJsonArr = new JSONArray();
                for (int i = 0; i < originalJsonArr.size(); i++) {
                    JSONObject jsonObject = originalJsonArr.getJSONObject(i);
                    Long sheetId = jsonObject.getLong("sheetId");
                    if (sheetId == null) {
                        continue;
                    }
                    newJsonArr.add(jsonObject);
                    if (Objects.equals(dto.getSheetId(), sheetId)) {
                        JSONArray addJsonArr = JSONObject.parseArray(dto.getJsonFileStr());
                        for (int j = 0; j < addJsonArr.size(); j++) {
                            JSONObject addObject = addJsonArr.getJSONObject(j);
                            addObject.put("sheetId", ESUtil.getUUID());
                            addObject.put("active", false);
                            newJsonArr.add(addObject);
                        }
                    }
                }
                if(!Objects.equals(newJsonArr.toString(), esVisualModel.getJson())) {
                    esVisualModel.setJson(newJsonArr.toString());
                    iVisualModelApiSvc.saveVisualModel(esVisualModel);
                }
            }
            //}
        } catch (Exception e) {
            throw new ServiceException("导入文件出错");
        }
        return true;
    }

    /**
     * 通过sheetId获取元模型
     * @param sheetIds
     * @param domainId
     * @return
     */
    public List<VisualModelsVo> queryVisualModels(List<Long> sheetIds, Long domainId) {
        List<ESVisualModel> models = iVisualModelApiSvc.queryVisualModels(domainId);
        List<VisualModelsVo> visualModelsVos = new ArrayList<>();
        for (ESVisualModel visualModel : models) {
            boolean enable = visualModel.getEnable();
            if (Objects.equals(enable, false)) {
                continue;
            }
            VisualModelsVo visualModelsVo = new VisualModelsVo();
            BeanUtils.copyProperties(visualModel, visualModelsVo);
            String json = visualModelsVo.getJson();
            JSONArray jsonArray = JSONArray.parseArray(json);
            JSONArray newJsonArray = new JSONArray(jsonArray.size());
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                boolean hasSheetId = jsonObject.containsKey("sheetId");
                if (!hasSheetId) {
                    continue;
                }
                Long sheetId = jsonObject.getLong("sheetId");
                if (sheetIds.contains(sheetId)) {
                    newJsonArray.add(jsonObject);
                }
            }
            visualModelsVo.setJson(newJsonArray.toString());
            String thumbnail = visualModelsVo.getThumbnail();
            if (!StringUtils.isEmpty(thumbnail) && !thumbnail.startsWith("data:image/png;base64")) {
                Map<Long, String> map = (Map) JSONObject.parseObject(thumbnail);
                Iterator<Map.Entry<Long, String>> iterator = map.entrySet().iterator();
                List<ThumbnailVo> thumbnailList = new ArrayList<>();
                while (iterator.hasNext()) {
                    Map.Entry<Long, String> next = iterator.next();
                    String oldStatusIdId = String.valueOf(next.getKey());
                    boolean contains = sheetIds.contains(Long.valueOf(oldStatusIdId));
                    if (contains) {
                        for (int i = 0; i < newJsonArray.size(); i++) {
                            JSONObject jsonObject = newJsonArray.getJSONObject(i);
                            Long sheetId = jsonObject.getLong("sheetId");
                            if (Objects.equals(sheetId, Long.valueOf(oldStatusIdId))) {
                                ThumbnailVo thumbnailVo = new ThumbnailVo();
                                thumbnailVo.setName(jsonObject.getString("name"));
                                thumbnailVo.setThumbnail(next.getValue());
                                thumbnailList.add(thumbnailVo);
                            }
                        }
                    }
                }
                visualModelsVo.setThumbnailList(thumbnailList);
            }
            visualModelsVos.add(visualModelsVo);
        }
        return visualModelsVos;
    }

    public Long saveVisualModel(ESVisualModel model) {
        SysUser userInfo = SysUtil.getCurrentUserInfo();
        if (userInfo == null) {
            throw new ServiceException("用户不存在!");
        }
        model.setDomainId(userInfo.getDomainId());
        List<ESVisualModel> esVisualModels = iVisualModelApiSvc.queryVisualModels(userInfo.getDomainId());
        if (model.getId() != null) {
            Object obj = iCacheService.getCache(VISUAL_MODEL_KEY + model.getId());
            if (obj == null) {
                return 0L;
            }
            Long sysUserId = userInfo.getId();
            Long originalUserId = Long.valueOf(String.valueOf(obj));
            if (Objects.equals(sysUserId, originalUserId)) {
                for (ESVisualModel esVisualModel : esVisualModels) {
                    // 所有模型都上锁
                    iCacheService.setCache(VISUAL_MODEL_KEY + esVisualModel.getId(), sysUserId, 60000*10);     // 十分钟
                }
            } else {
                throw new BinaryException("无法获取用户信息，请重新登录");
            }
        }
        Long status = iVisualModelApiSvc.saveVisualModel(model);

        // 当前模型数据为新建 同原数据上锁
        if (BinaryUtils.isEmpty(model.getId())) {
            for (ESVisualModel esVisualModel : esVisualModels) {
                Object obj = iCacheService.getCache(VISUAL_MODEL_KEY + esVisualModel.getId());
                if (BinaryUtils.isEmpty(obj)) {
                    continue;
                }
                Long originalUserId = Long.valueOf(String.valueOf(obj));
                iCacheService.setCache(VISUAL_MODEL_KEY + status, originalUserId, 60000*10);
                return status;
            }
        }
        return status;
    }


    public List<SysOrg> getVisualModelOrgList() {
        return orgApiSvc.getOrgListByParentId(1L);
    }

    public Long saveVisualModelPrivate(ESVisualModel model) {
        SysUser userInfo = SysUtil.getCurrentUserInfo();
        if (userInfo == null) {
            throw new ServiceException("用户不存在!");
        }
        model.setDomainId(userInfo.getDomainId());
        return iVisualModelApiSvc.saveVisualModelPrivate(model);
    }

    /**
     * 发布元模型至资产库
     * @param id
     * @return
     */
    public Long publishVisualModel(Long id,String publishDescription) {

        ESVisualModel esVisualModel = iVisualModelApiSvc.queryPrivateVisualModelById(1L, id);
        //1、先发布至资产库
        Long linkPublishedId = esVisualModel.getLinkPublishedId();
        ESVisualModel designVisualModel = iVisualModelApiSvc.queryVisualModelById(1L, linkPublishedId);
        if (designVisualModel == null) {
            //新增
            designVisualModel = new ESVisualModel();
            designVisualModel.setId(linkPublishedId);
            designVisualModel.setPublishVersion(0L);
            designVisualModel.setCreateTime(ESUtil.getNumberDateTime());
        }
        designVisualModel.setDomainId(1L);
        designVisualModel.setEnable(Boolean.TRUE);
        designVisualModel.setName(esVisualModel.getName());
        designVisualModel.setViewRoleIds(esVisualModel.getViewRoleIds());
        designVisualModel.setEditRoleIds(esVisualModel.getEditRoleIds());
        designVisualModel.setOrgId(esVisualModel.getOrgId());
        if (designVisualModel.getHistory()) {
            designVisualModel.setPublishVersion(0L);
        }
        designVisualModel.setPublishVersion(designVisualModel.getPublishVersion() + 1);
        designVisualModel.setHistory(Boolean.FALSE);
        designVisualModel.setSelectedShapeGroupIds(esVisualModel.getSelectedShapeGroupIds());
        designVisualModel.setPagesetting(esVisualModel.getPagesetting());
        designVisualModel.setJson(esVisualModel.getJson());
        designVisualModel.setThumbnail(esVisualModel.getThumbnail());
        designVisualModel.setModifyTime(ESUtil.getNumberDateTime());
        Long designVisualId = visualModelSvc.saveOrUpdate(designVisualModel);
        //2、保存元模型版本信息
        saveVisualModelVersion(designVisualModel,publishDescription);
        //3、将所有可用元模型查出来查询所有ci之间的关系然后进行更新
        iVisualModelApiSvc.flushAllEnableVisualModelCiRlt();
        //4、发布完删除本地
        delPrivateVisualModel(id);
        //返回资产库id
        return designVisualId;
    }

    /**
     * 记录元模型发布版本数据
     * @return
     */
    public Long saveVisualModelVersion(ESVisualModel designVisualModel,String publishDescription) {
        Long id = designVisualModel.getId();
        //首先记录下元模型的版本信息
        ESVisualModelHistory esVisualModelHistory = new ESVisualModelHistory();
        BeanUtils.copyProperties(designVisualModel, esVisualModelHistory);
        esVisualModelHistory.setId(null);
        esVisualModelHistory.setLinkPublishedId(id);
        esVisualModelHistory.setCreateTime(ESUtil.getNumberDate());
        esVisualModelHistory.setCreater(SysUtil.getCurrentUserInfo().getLoginCode());
        esVisualModelHistory.setPublishDescription(publishDescription);
        Long publishVersionId = esVisualModelHistorySvc.saveOrUpdate(esVisualModelHistory);
        //查询元模型的ci信息和关系信息并记录
        String json = esVisualModelHistory.getJson();
        JSONArray objects = JSON.parseArray(json);

        Set<Long> visualRltId = new HashSet<>();
        Set<Long> visualClassId = new HashSet<>();

        for (int i = 0; i < objects.size(); i++) {
            JSONObject object = objects.getJSONObject(i);
            JSONArray linkDataArray = object.getJSONArray("linkDataArray");
            JSONArray nodeDataArray = object.getJSONArray("nodeDataArray");
            for (int j = 0; j < linkDataArray.size(); j++) {
                JSONObject linkData = linkDataArray.getJSONObject(j);
                if (linkData.get("classId") != null) {
                    visualRltId.add(linkData.getLong("classId"));
                }
            }
            for (int j = 0; j < nodeDataArray.size(); j++) {
                JSONObject nodeData = nodeDataArray.getJSONObject(j);
                if (nodeData.get("classId") != null) {
                    visualClassId.add(nodeData.getLong("classId"));
                }
            }
        }

        List<ClassInfoHistory> classInfoHistories = new ArrayList<>();
        List<RltInfoHistory> rltInfoHistories = new ArrayList<>();

        if (!CollectionUtils.isEmpty(visualClassId)) {
            CCcCiClass cCcCiClass = new CCcCiClass();
            cCcCiClass.setIds(visualClassId.toArray(new Long[0]));
            List<CcCiClassInfo> ccCiClassInfos = iciClassSvc.queryClassByCdt(cCcCiClass);

            if(!CollectionUtils.isEmpty(ccCiClassInfos)){
                for (CcCiClassInfo ccCiClassInfo : ccCiClassInfos) {
                    ClassInfoHistory classInfoHistory = new ClassInfoHistory();
                    classInfoHistory.setCiClass(ccCiClassInfo.getCiClass());
                    classInfoHistory.setAttrDefs(ccCiClassInfo.getAttrDefs());
                    classInfoHistory.setClassId(ccCiClassInfo.getCiClass().getId());
                    classInfoHistory.setClassCode(ccCiClassInfo.getCiClass().getClassCode());
                    classInfoHistory.setVisualPublishVersionId(publishVersionId);
                    classInfoHistories.add(classInfoHistory);
                }
                esClassInfoHistorySvc.saveOrUpdateBatch(classInfoHistories);
            }
        }
        if (!CollectionUtils.isEmpty(visualRltId)) {
            CCcCiClass cCcCiClass = new CCcCiClass();
            cCcCiClass.setIds(visualRltId.toArray(new Long[0]));
            List<CcCiClassInfo> rltClassByCdt = iRltClassSvc.getRltClassByCdt(cCcCiClass);
            if(!CollectionUtils.isEmpty(rltClassByCdt)){
                for (CcCiClassInfo ccCiClassInfo : rltClassByCdt) {
                    RltInfoHistory rltInfoHistory = new RltInfoHistory();
                    rltInfoHistory.setRltClass(ccCiClassInfo.getCiClass());
                    rltInfoHistory.setAttrDefs(ccCiClassInfo.getAttrDefs());
                    rltInfoHistory.setRltId(ccCiClassInfo.getCiClass().getId());
                    rltInfoHistory.setRltCode(ccCiClassInfo.getCiClass().getClassCode());
                    rltInfoHistory.setVisualPublishVersionId(publishVersionId);
                    rltInfoHistories.add(rltInfoHistory);
                }
                esRltInfoHistorySvc.saveOrUpdateBatch(rltInfoHistories);
            }
        }
        return publishVersionId;
    }

    public LinkedList<ESVisualModelVo> getVisualModelList() {
        //获取所有已发布的元模型
        List<ESVisualModelVo> designVisualModels = iVisualModelApiSvc.queryVisualModelsNoChickExit(1L, LibType.DESIGN, null);
        List<ESVisualModelVo> privateVisualModels = iVisualModelApiSvc.queryVisualModelsNoChickExit(1L, LibType.PRIVATE, SysUtil.getCurrentUserInfo().getLoginCode());

        Map<Long, Long> visualModelVersionMap = new HashMap<>();
        for (ESVisualModelVo designVisualModel : designVisualModels) {
            visualModelVersionMap.put(designVisualModel.getId(), designVisualModel.getPublishVersion());
        }
        for (ESVisualModelVo privateVisualModel : privateVisualModels) {
            Long publishVersion = visualModelVersionMap.get(privateVisualModel.getLinkPublishedId());
            if (publishVersion != null) {
                if (privateVisualModel.getPublishVersion()==null||!privateVisualModel.getPublishVersion().equals(publishVersion)) {
                    privateVisualModel.setOldVersion(Boolean.TRUE);
                }
            }
        }

        // 查询所有元模型的3D全景配置
        Set<Long> modelIds = new HashSet<>(visualModelVersionMap.keySet());
        if (!CollectionUtils.isEmpty(modelIds)) {
            Map<Long, ModelPanorama3D> panoramaMap = modelPanorama3DSvc.queryByModelIds(modelIds);
            if (!CollectionUtils.isEmpty(panoramaMap)) {
                // 设置3D全景配置ID到元模型对象中
                for (ESVisualModelVo designVisualModel : designVisualModels) {
                    ModelPanorama3D panorama = panoramaMap.get(designVisualModel.getId());
                    if (panorama != null) {
                        designVisualModel.setModelPanorama3DId(panorama.getId());
                    }
                }
            }
        }

        LinkedList<ESVisualModelVo> resultList = new LinkedList<>();

        resultList.addAll(privateVisualModels);
        resultList.addAll(designVisualModels);
        return resultList;
    }

    public Long getVisualModelPrivate(Long visId) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.termQuery("linkPublishedId", visId));
        boolQueryBuilder.filter(QueryBuilders.termQuery("status", 1));
        boolQueryBuilder.filter(QueryBuilders.termQuery("creator.keyword", SysUtil.getCurrentUserInfo().getLoginCode()));
        List<ESVisualModel> visualModelByQuery = iVisualModelApiSvc.getVisualModelByQuery(boolQueryBuilder, LibType.PRIVATE);
        if (!CollectionUtils.isEmpty(visualModelByQuery)) {
            if (visualModelByQuery.get(0).getApprove() != 0) {
                throw new BinaryException("存在审批中模型，不可编辑");
            }
            return visualModelByQuery.get(0).getId();
        } else {
            //检出到当前用户本地一份元模型进行编辑
            BoolQueryBuilder designQueryBuilder = QueryBuilders.boolQuery();
            designQueryBuilder.filter(QueryBuilders.termQuery("id", visId));
            List<ESVisualModel> designVisualList = iVisualModelApiSvc.getVisualModelByQuery(designQueryBuilder, LibType.DESIGN);
            if (CollectionUtils.isEmpty(designVisualList)) {
                throw new BinaryException("未查到元模型");
            }
            ESVisualModel designVisualModel = designVisualList.get(0);
            ESVisualModel privateVisualModel = new ESVisualModel();
            BeanUtils.copyProperties(designVisualModel, privateVisualModel);
            privateVisualModel.setId(null);
            privateVisualModel.setLinkPublishedId(designVisualModel.getId());
            privateVisualModel.setStatus(1);
            return visualModelPrivateSvc.saveOrUpdate(privateVisualModel);
        }
    }

    public ESVisualModel getVisualModelPrivateById(Long privateVisId) {
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.filter(QueryBuilders.termQuery("id", privateVisId));
        List<ESVisualModel> visualModelByQuery = iVisualModelApiSvc.getVisualModelByQuery(queryBuilder, LibType.PRIVATE);
        if(CollectionUtils.isEmpty(visualModelByQuery)){
            return null;
        }else {
            return visualModelByQuery.get(0);
        }
    }

    public ESVisualModel getVisualModelById(Long visualModelId) {
        ESVisualModel visualModelPrivateById = getVisualModelPrivateById(visualModelId);
        ESVisualModelVo esVisualModelVo = new ESVisualModelVo();
        if(visualModelPrivateById==null){
            BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
            queryBuilder.filter(QueryBuilders.termQuery("id", visualModelId));
            List<ESVisualModel> visualModelByQuery = iVisualModelApiSvc.getVisualModelByQuery(queryBuilder, LibType.DESIGN);
            if(CollectionUtils.isEmpty(visualModelByQuery)){
                return null;
            }else {
                BeanUtils.copyProperties(visualModelByQuery.get(0),esVisualModelVo);
                esVisualModelVo.setLibType(LibType.DESIGN);
                return esVisualModelVo;
            }
        }else {
            BeanUtils.copyProperties(visualModelPrivateById,esVisualModelVo);
            esVisualModelVo.setLibType(LibType.PRIVATE);
            return esVisualModelVo;
        }
    }

    public void delPrivateVisualModel(Long visualModelId) {
        //逻辑删除
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.termQuery("id", visualModelId));
        visualModelPrivateSvc.updateByQuery(boolQueryBuilder,"ctx._source.status=0",true);
    }

    public void delDesignVisualModel(Long visualModelId) {
        visualModelSvc.deleteById(visualModelId);
        //同时将所有私有模型的版本号重置成0
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.termQuery("linkPublishedId", visualModelId));
        visualModelPrivateSvc.updateByQuery(boolQueryBuilder,"ctx._source.publishVersion=0",true);
        //刷新ci间关系
        iVisualModelApiSvc.flushAllEnableVisualModelCiRlt();
    }

    public Long convertVisualModelToExample(Long id) {
        //1、根据id查询当前发布的元模型
        ESVisualModel visualModel = visualModelSvc.getById(id);
        visualModel.setEnable(Boolean.FALSE);
        visualModel.setId(ESUtil.getUUID());
        return visualModelSvc.saveOrUpdate(visualModel);
    }


    public ESVisualModel updatePrivateVisualModel(ESVisualModel model) {

        ESVisualModel privateVisualModel = visualModelPrivateSvc.getById(model.getId());
        ESVisualModel designVisualModel = visualModelSvc.getById(privateVisualModel.getLinkPublishedId());

        if (designVisualModel != null && !designVisualModel.getPublishVersion().equals(privateVisualModel.getPublishVersion())) {
            privateVisualModel.setPublishVersion(designVisualModel.getPublishVersion());
            privateVisualModel.setName(designVisualModel.getName());
            privateVisualModel.setJson(designVisualModel.getJson());
            privateVisualModel.setThumbnail(designVisualModel.getThumbnail());
            privateVisualModel.setEditRoleIds(designVisualModel.getEditRoleIds());
            privateVisualModel.setViewRoleIds(designVisualModel.getViewRoleIds());
            privateVisualModel.setSelectedShapeGroupIds(designVisualModel.getSelectedShapeGroupIds());
            visualModelPrivateSvc.saveOrUpdate(privateVisualModel);
        }
        return privateVisualModel;
    }

    public PorcessResponse approveVisualModel(VisualModelsDto dto) {
        //查询元模型审批角色下是否配置用户，没配置提示配置用户
        //1、查询元模型管理员角色是否存在
         SysRole visualModeRole = esRoleSvc.getByName(1L, "元模型管理员");;
        if (visualModeRole == null) {
            throw new BinaryException("请先配置元模型管理员角色");
        }
        //2、查询元模型管理员角色下是否配置用户
        List<SysUser> visualModeAdminUser = userSvc.getUserByRoleId(visualModeRole.getId());;
        if (CollectionUtils.isEmpty(visualModeAdminUser)) {
            throw new BinaryException("请先配置元模型管理员角色用户");
        }

        //元模型发布前检查
        ESVisualModel modelPrivate = visualModelPrivateSvc.getById(dto.getVisualModelId());
        if (modelPrivate == null) {
            throw new BinaryException("元模型不存在");
        }
        String loginCode = SysUtil.getCurrentUserInfo().getLoginCode();
        //执行元模型发布审批
        ProcessRequest processRequest = new ProcessRequest();
        processRequest.setBusinessKey(dto.getVisualModelId().toString());
        processRequest.setProcessDefinitionKey(FlowableConstant.VISUAL_MODEL_PUBLISH_APPROVE);
        processRequest.setProcessInstanceName(modelPrivate.getName() + "元模型审批流程");
        processRequest.setOwner(loginCode);
        processRequest.setUserId(loginCode);
        HashMap<String, Object> routerVariables = new HashMap<>();
        routerVariables.put("publishDescription",dto.getPublishDescription());
        processRequest.setRouterVariables(routerVariables);
        PorcessResponse porcessResponse = flowableFeign.startProcessBindAssignee(processRequest);
        TaskRequest taskRequest = new TaskRequest();
        taskRequest.setAction(FLOWACTION.ACCETP);
        taskRequest.setTaskId(porcessResponse.getTaskId());
        flowableFeign.completeTask(taskRequest);
        //修改元模型状态
        modelPrivate.setApprove(1);
        visualModelPrivateSvc.saveOrUpdate(modelPrivate);
        return porcessResponse;
    }

    public TaskResponse completeTask(TaskRequest taskRequest) {
        TaskResponse taskInfoByTaskId = flowableFeign.getTaskInfoByTaskId(taskRequest.getTaskId());
        if (FLOWACTION.REJECT.equals(taskRequest.getAction())) {
            String businessKey = taskInfoByTaskId.getBusinessKey();
            ESVisualModel esVisualModel = visualModelPrivateSvc.getById(Long.valueOf(businessKey));
            esVisualModel.setApprove(2);
            visualModelPrivateSvc.saveOrUpdate(esVisualModel);
        } else if (FLOWACTION.ACCETP.equals(taskRequest.getAction()) && "rectification".equalsIgnoreCase(taskInfoByTaskId.getDescription())
                || (FLOWACTION.ACCEPT.equals(taskRequest.getAction()) && "rectification".equalsIgnoreCase(taskInfoByTaskId.getDescription()))) {
            //如果是提交人重新提交的话需要重新将状态改为审批中不可编辑
            String businessKey = taskInfoByTaskId.getBusinessKey();
            ESVisualModel esVisualModel = visualModelPrivateSvc.getById(Long.valueOf(businessKey));
            esVisualModel.setApprove(1);
            String publishDescription = taskRequest.getPublishDescription();
            if (!StringUtils.isBlank(publishDescription)) {
                HashMap<String, Object> routerVariables = new HashMap<>();
                routerVariables.put("publishDescription", taskRequest.getPublishDescription());
                taskRequest.setRouterVariables(routerVariables);
            }
            visualModelPrivateSvc.saveOrUpdate(esVisualModel);
        } else if (FLOWACTION.CANCEL.equals(taskRequest.getAction())) {
            String businessKey = taskInfoByTaskId.getBusinessKey();
            ESVisualModel esVisualModel = visualModelPrivateSvc.getById(Long.valueOf(businessKey));
            esVisualModel.setApprove(0);
            visualModelPrivateSvc.saveOrUpdate(esVisualModel);
        }

        TaskResponse taskResponse = flowableFeign.completeTask(taskRequest);
        if (taskResponse.getProcessEnd() && FLOWACTION.ACCETP.equals(taskRequest.getAction())||
                taskResponse.getProcessEnd() && FLOWACTION.ACCEPT.equals(taskRequest.getAction())) {
            //流程审批通过就执行发布
            PorcessResponse processInstanceByProcessInstanceId = flowableFeign.getProcessInstanceByProcessInstanceId(taskInfoByTaskId.getProcessInstanceId());
            Map<String, Object> routerVariables = processInstanceByProcessInstanceId.getRouterVariables();
            Object o = routerVariables.get("publishDescription");
            String publishDescription = "";
            if (o != null) {
                publishDescription = o.toString();
            }
            publishVisualModel(Long.parseLong(taskInfoByTaskId.getBusinessKey()), publishDescription);
        }

        return taskResponse;
    }

    public List<ESVisualModelHistory> getVisualPublishHistory(String designVisualModelId) {

        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.termQuery("linkPublishedId", designVisualModelId));
        List<ESVisualModelHistory> listByQuery = esVisualModelHistorySvc.getSortListByQueryScroll(boolQueryBuilder,"publishVersion",false);
        //从listByQuery中获取creater字段的数据并放到set中，判断下creater为空就从modifier获取
        Set<String> createrSet = new HashSet<>();
        for (ESVisualModelHistory esVisualModelHistory : listByQuery) {
            if (StringUtils.isBlank(esVisualModelHistory.getCreater())) {
                createrSet.add(esVisualModelHistory.getModifier());
            } else {
                createrSet.add(esVisualModelHistory.getCreater());
            }
        }

        //使用userSvc获取所有用户信息
        CSysUser cSysUser = new CSysUser();
        cSysUser.setLoginCodes(createrSet.toArray(new String[0]));
        List<SysUser> sysUserByCdt = userSvc.getSysUserByCdt(cSysUser);
        Map<String, SysUser> sysUserMap = sysUserByCdt.stream().collect(Collectors.toMap(SysUser::getLoginCode, item -> item, (v1, v2) -> v2));
        for (ESVisualModelHistory esVisualModelHistory : listByQuery) {
            if (StringUtils.isBlank(esVisualModelHistory.getCreater())) {
                esVisualModelHistory.setPublishUserName(sysUserMap.get(esVisualModelHistory.getModifier()).getUserName());
            } else {
                esVisualModelHistory.setPublishUserName(sysUserMap.get(esVisualModelHistory.getCreater()).getUserName());
            }
        }

        return listByQuery;
    }

    /**
     * 查询元模型历史CI
     * @param visualModeHistoryId
     * @param classId
     * @return
     */
    public ClassInfoHistory getVisualModelHistoryCi(Long visualModelId, Long classId) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.termQuery("visualPublishVersionId", visualModelId));
        boolQueryBuilder.filter(QueryBuilders.termQuery("classId", classId));
        ClassInfoHistory cInfoHistory = esClassInfoHistorySvc.selectOne(boolQueryBuilder);
        return cInfoHistory;
    }

    /**
     * 查询元模型历史关系信息
     * @param visualModeHistoryId
     * @param rltId
     * @return
     */
    public RltInfoHistory getVisualModelHistoryRlt(Long visualModeHistoryId, Long rltId) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.termQuery("visualPublishVersionId", visualModeHistoryId));
        boolQueryBuilder.filter(QueryBuilders.termQuery("rltId", rltId));
        RltInfoHistory rltInfoHistory = esRltInfoHistorySvc.selectOne(boolQueryBuilder);
        return rltInfoHistory;
    }

    private void checkVisualModel(VisualModelsDto dto) {
        if (dto == null) {
            throw new ServiceException("导入元模型参数不能为空!");
        }
        if (StringUtils.isEmpty(dto.getJsonFileStr())) {
            throw new ServiceException("上传的json数据不能为空!");
        }
        if (dto.getVisualModelId() == null) {
            throw new ServiceException("元模型不能为空!");
        }
        if (dto.getSheetId() == null) {
            throw new ServiceException("sheetId不能为空!");
        }
    }


    public void delVMThumbnailBySheetId(Long id, Long sheetId) {
        if (BinaryUtils.isEmpty(sheetId)) {
            throw new ServiceException("sheetId不能为空!");
        }
        if (BinaryUtils.isEmpty(id)) {
            throw new ServiceException("元模型Id不能为空!");
        }
        iVisualModelApiSvc.delVMThumbnailBySheetId(id, sheetId);
    }


    public Map<Long, Long> refreshClassIdByVisId(Long visId) {
        Map<Long, Long> data = new HashMap<>();
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        // 根据visId查询元模型数据
        ESVisualModel visualModel = iVisualModelApiSvc.queryVisualModelById(currentUserInfo.getDomainId(), visId);
        if (BinaryUtils.isEmpty(visualModel)) {
            throw new BinaryException("元模型不存在");
        }
        // 查询所有class信息 code 和 id 组成 map
        CCcCiClass cdt = new CCcCiClass();
        cdt.setDomainId(currentUserInfo.getDomainId());
        List<CcCiClassInfo> ciClassList = iciClassSvc.queryClassByCdt(cdt);
        Map<String, Long> ciClassMap = ciClassList.stream().map(CcCiClassInfo::getCiClass).collect(Collectors.toMap(CcCiClass::getClassCode, CcCiClass::getId, (k1, k2) -> k2));

        List<CcCiClassInfo> rltClassList = iRltClassSvc.queryAllClasses(currentUserInfo.getDomainId());
        Map<String, Long> rltCodeMap = rltClassList.stream().map(CcCiClassInfo::getCiClass).collect(Collectors.toMap(CcCiClass::getClassCode, CcCiClass::getId, (k1, k2) -> k2));

        JSONArray jsonArray = JSON.parseArray(visualModel.getJson());
        if (!BinaryUtils.isEmpty(jsonArray)) {
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject json = jsonArray.getJSONObject(i);
                JSONArray nodeList = json.getJSONArray("nodeDataArray");
                if(BinaryUtils.isEmpty(nodeList)){
                    continue;
                }
                Map<Integer, Long> keyClassMap = replaceCiClassId(nodeList, ciClassMap, data);
                json.put("nodeDataArray", nodeList);
                JSONArray linkList = json.getJSONArray("linkDataArray");
                if(BinaryUtils.isEmpty(linkList)){
                    continue;
                }
                replaceRltClassId(linkList, rltCodeMap, keyClassMap, data);
                json.put("linkDataArray", linkList);
            }
        }
        visualModel.setJson(jsonArray.toJSONString());
        visualModelSvc.saveOrUpdate(visualModel);
        return data;
    }

    /**
     * 刷新元模型node节点分类id
     * @param nodeList 元模型节点集合
     * @param ciClassMap classCode->classId映射
     * @param data 刷新的classId映射
     * @return node节点key与匹配到的分类id映射
     */
    private Map<Integer, Long> replaceCiClassId(JSONArray nodeList, Map<String, Long> ciClassMap, Map<Long, Long> data){
        Map<Integer, Long> keyClassMap = new HashMap<>();
        for (int i = 0; i < nodeList.size(); i++) {
            JSONObject node = nodeList.getJSONObject(i);
            String classCode = node.getString("classCode");
            Long classId = node.getLong("classId");
            if (!BinaryUtils.isEmpty(classCode)) {
                // 刷新数据中的classId
                Long id = ciClassMap.get(classCode);
                if (!BinaryUtils.isEmpty(id)) {
                    data.put(classId, id);
                    node.put("classId", id);
                    keyClassMap.put(node.getInteger("key"), id);
                } else {
                    // json中的classId 在全局分类匹配不到 当前导入分类按照图标处理
                    node.remove("classId");
                }
            } else {
                log.info("vis_nodeData========" + node.toJSONString());
            }
        }
        return keyClassMap;
    }

    /**
     * 刷新元模型关系线分类id
     * @param linkList 关系线集合
     * @param rltCodeMap 关系分类code->分类id映射
     * @param keyClassMap node节点key与匹配到的分类id映射
     * @param data 刷新的classId映射
     */
    private void replaceRltClassId(JSONArray linkList, Map<String, Long> rltCodeMap, Map<Integer, Long> keyClassMap, Map<Long, Long> data){
        for (int i = 0; i < linkList.size(); i++) {
            JSONObject link = linkList.getJSONObject(i);
            // 存量json文件可能缺少关系线的code信息 如果缺失取label 如果都缺失 即为普通线段
            String classCode = !BinaryUtils.isEmpty(link.getString("classCode")) ? link.getString("classCode") : link.getString("label");
            Integer from = link.getInteger("from");
            Integer to = link.getInteger("to");
            if(BinaryUtils.isEmpty(classCode)){
                log.info("vis_nodeData========" + link.toJSONString());
                continue;
            }
            Long id = rltCodeMap.get(classCode);
            if (keyClassMap.get(from) == null || keyClassMap.get(to) == null || BinaryUtils.isEmpty(id)) {
                // json中的classId 在全局分类匹配不到 当前导入分类按照图标处理
                link.remove("classId");
            }else{
                // 刷新数据中的classId
                data.put(link.getLong("classId"), id);
                link.put("classId", id);
                // 同时更新一下classCode/label信息
                link.put("classCode", classCode);
                link.put("label", classCode);
            }
        }
    }

    public Boolean isEmploy(Long visId) {
        Long id = SysUtil.getCurrentUserInfo().getId();
        Object userId = iCacheService.getCache(VISUAL_MODEL_KEY + visId);
        // 只要当前用户没在登录状态都是false
        if (BinaryUtils.isEmpty(userId)) {
            // redis中无信息 当前用户可以编辑
            return false;
        }
        if (Objects.equals(id, Long.valueOf(userId.toString()))) {
            // 当前登录用户是编辑用户
            return true;
        }
        return false;
    }

    /**
     * 保存元模型3D全景配置
     * @param modelPanorama3D 元模型3D全景配置
     * @return 保存结果ID
     */
    public Long saveModelPanorama3D(ModelPanorama3D modelPanorama3D) {
        if (modelPanorama3D == null) {
            throw new ServiceException("元模型3D全景配置不能为空!");
        }
        if (modelPanorama3D.getModelId() == null) {
            throw new ServiceException("元模型ID不能为空!");
        }
        return modelPanorama3DSvc.saveModelPanorama3D(modelPanorama3D);
    }

    /**
     * 根据元模型ID查询3D全景配置
     * @param modelId 元模型ID
     * @return 元模型3D全景配置
     */
    public ModelPanorama3D queryModelPanorama3DByModelId(Long modelId) {
        if (modelId == null) {
            throw new ServiceException("元模型ID不能为空!");
        }
        return modelPanorama3DSvc.queryByModelId(modelId);
    }
}
