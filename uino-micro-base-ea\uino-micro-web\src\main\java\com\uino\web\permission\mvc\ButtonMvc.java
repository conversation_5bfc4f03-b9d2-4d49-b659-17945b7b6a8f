package com.uino.web.permission.mvc;

import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uino.bean.permission.base.SysModule;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.permission.query.SysModuleCheck;
import com.uino.init.api.ApiResult;
import com.uino.init.api.ApiVersion;
import com.uino.api.client.permission.IButtonApiSvc;
import com.uino.util.sys.SysUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * @Title: ButtonMvc
 * @Description:
 * @Author: YGQ
 * @Create: 2021-06-27 23:49
 **/
@ApiVersion(1)
@Api(value = "系统菜单-按钮", tags = {"系统菜单"})
@RestController
@RequestMapping(value = "/permission/button")
public class ButtonMvc {

    private final IButtonApiSvc iButtonApiSvc;

    @Autowired
    public ButtonMvc(IButtonApiSvc iButtonApiSvc) {
        this.iButtonApiSvc = iButtonApiSvc;
    }

    @PostMapping("checkModuleSign")
    @ApiOperation("校验模块权限")
    @ModDesc(desc = "校验模块权限", pDesc = "按钮对象", pType = SysModule.class, rDesc = "按钮对象", rType = SysModule.class)
    public ApiResult<SysModuleCheck> checkModuleSign(@RequestBody SysModule sysButton) {
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        sysButton.setDomainId(currentUserInfo.getDomainId());
        SysModuleCheck result = iButtonApiSvc.checkModuleSign(sysButton);
        return ApiResult.ok(this).data(result);
    }

    @PostMapping("saveButton")
    @ApiOperation("保存按钮")
    @ModDesc(desc = "保存按钮", pDesc = "按钮对象", pType = SysModule.class, rDesc = "按钮对象", rType = SysModule.class)
    public ApiResult<SysModule> saveButton(@RequestBody SysModule sysButton) {
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        sysButton.setDomainId(currentUserInfo.getDomainId());
        SysModule resButton = iButtonApiSvc.saveButton(sysButton);
        return ApiResult.ok(this).data(resButton);
    }

    @PostMapping("deleteButton")
    @ApiOperation("删除按钮")
    @ModDesc(desc = "删除按钮", pDesc = "按钮ID", pType = Long.class, rDesc = "操作是否成功", rType = Boolean.class)
    public ApiResult<Boolean> deleteButton(@RequestBody Long buttonId) {
        iButtonApiSvc.deleteButton(buttonId);
        return ApiResult.ok(this).data(true);
    }

    @PostMapping("saveButtonSort")
    @ApiOperation("保存按钮顺序")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orderDict", value = "按钮顺序 <按钮ID, 序号>, example: {7330638252500000:1}", required = true, dataType = "Map<Long, Integer>")
    })
    @ModDesc(desc = "保存按钮顺序", pDesc = "保存按钮顺序", pType = Map.class, rDesc = "操作是否成功", rType = Boolean.class)
    public ApiResult<Boolean> saveButtonSort(@RequestBody Map<Long, Integer> orderDict) {
        iButtonApiSvc.saveButtonSort(orderDict);
        return ApiResult.ok(this).data(true);
    }
}
