package com.uinnova.product.eam.model.plmp;

public class SubsystemPlmpDto {

    /**
     * 1-新增,2-变更
     */
    private int reqTyp;
    /**
     * 子系统标识（旧的四位、新的六位）
     */
    private String ssysId;
    /**
     * iams中对系统的唯一标识
     */
    private String systIAMSId;
    /**
     * plmp中对系统的唯一标识(为空的情况下，传空字符串)父系统对应的UID
     */
    private String systPLMPId;
    /**
     * 系统数据
     */
    private MasterSystem systAttrs;
    /**
     * 子系统数据
     */
    private Subsystem ssysAttrs;

    public int getReqTyp() {
        return reqTyp;
    }

    public void setReqTyp(int reqTyp) {
        this.reqTyp = reqTyp;
    }

    public String getSsysId() {
        return ssysId;
    }

    public void setSsysId(String ssysId) {
        this.ssysId = ssysId;
    }

    public String getSystIAMSId() {
        return systIAMSId;
    }

    public void setSystIAMSId(String systIAMSId) {
        this.systIAMSId = systIAMSId;
    }

    public String getSystPLMPId() {
        return systPLMPId;
    }

    public void setSystPLMPId(String systPLMPId) {
        this.systPLMPId = systPLMPId;
    }

    public MasterSystem getSystAttrs() {
        return systAttrs;
    }

    public void setSystAttrs(MasterSystem systAttrs) {
        this.systAttrs = systAttrs;
    }

    public Subsystem getSsysAttrs() {
        return ssysAttrs;
    }

    public void setSsysAttrs(Subsystem ssysAttrs) {
        this.ssysAttrs = ssysAttrs;
    }
}
