package com.uinnova.product.eam.feign.client;

import com.uinnova.product.eam.base.model.FileResourceMeta;
import com.uinnova.product.eam.base.model.ResourceInfo;
import com.uinnova.product.eam.feign.FeignConst;
import com.uinnova.product.eam.model.vo.DefaultFileVo;
import com.uino.bean.permission.base.SysUser;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Component
@FeignClient(name = FeignConst.APPLICATION,path = FeignConst.RESOURCE_PATH)
public interface EamResourceClient {
    @PostMapping("upload")
    List<ResourceInfo> upload(@RequestParam("files") MultipartFile[] files, @RequestBody SysUser sysUser);

    @PostMapping("download")
    List<FileResourceMeta> download(@RequestParam("ids") List<Long> ids);

    @PostMapping("uploadFileByType")
    List<ResourceInfo> uploadFileByType(MultipartFile[] files, Integer type);

    @PostMapping("deleteResource")
    Integer deleteResource(Long id);

    @GetMapping("getImages")
    List<DefaultFileVo> getImages(@RequestParam("type") Integer type);
}
