package com.uinnova.product.eam.model.vo;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.util.List;

@Data
public class AppArchWallConfigVo {

    @Comment("展示标题属性名称")
    private String title;

    @Comment("展示logo属性名称")
    private String icon;

    @Comment("标签项")
    private List<AppArchWallConfigLabelVo> labels;

    @Comment("展示简介属性名称")
    private String desc;

    @Comment("属性字段所属分类id")
    private Long classId;
}
