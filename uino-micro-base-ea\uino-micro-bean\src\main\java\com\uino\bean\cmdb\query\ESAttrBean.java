package com.uino.bean.cmdb.query;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 
 * <AUTHOR>
 *
 */
@ApiModel(value = "属性实体", description = "属性实体")
public class ESAttrBean implements Serializable {

    private static final long serialVersionUID = 9077323117120638470L;

    @ApiModelProperty(value = "属性关键字")
    private String key;

    @ApiModelProperty(value = "属性值")
    private Object value;

    @ApiModelProperty(value = "操作类型 1表示等于，2表示like,3:<,4:<=,5:>,6>=")
    private int optType;

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public int getOptType() {
        return optType;
    }

    public void setOptType(int optType) {
        this.optType = optType;
    }

    public Object getValue() {
        return value;
    }

    public void setValue(Object value) {
        this.value = value;
    }
}
