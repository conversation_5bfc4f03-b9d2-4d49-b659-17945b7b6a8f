package com.uino.ed.eventdrive.executor;

import com.uino.ed.eventdrive.event.Event;
import com.uino.ed.eventdrive.handle.EventHandle;

public class ConcWorker extends Thread{

    private EventHandle handle;
    private Event event;

    public  ConcWorker(EventHandle handle, Event event){
        this.handle = handle;
        this.event = event;
    }

    @Override
    public void run() {
        handle.handle(event);
    }
}
