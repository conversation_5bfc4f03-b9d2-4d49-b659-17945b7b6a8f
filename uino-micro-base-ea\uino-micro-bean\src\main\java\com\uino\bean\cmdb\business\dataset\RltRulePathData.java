package com.uino.bean.cmdb.business.dataset;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;
import java.util.Map;

/**
 * @Classname RltRulePathData
 * @Description 关系白能力路径数据
 * @Date 2020/4/16 10:19
 * @Created by sh
 */
@ApiModel(value="")
public class RltRulePathData {

    //nodeId 用“-”拼接
    @ApiModelProperty(value="节点路径")
    private String nodePath;

    @ApiModelProperty(value="表头")
    private List<Map<String, Object>> headers;

    @ApiModelProperty(value="列表内容")
    private List<List<Object>> data;

    public String getNodePath() {
        return nodePath;
    }

    public void setNodePath(String nodePath) {
        this.nodePath = nodePath;
    }

    public List<Map<String, Object>> getHeaders() {
        return headers;
    }

    public void setHeaders(List<Map<String, Object>> headers) {
        this.headers = headers;
    }

    public List<List<Object>> getData() {
        return data;
    }

    public void setData(List<List<Object>> data) {
        this.data = data;
    }
}
