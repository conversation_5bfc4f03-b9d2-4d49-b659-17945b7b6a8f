package com.uino.api.client.cmdb.rpc;

import java.util.List;

import com.uino.dao.BaseConst;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uino.bean.cmdb.base.ESCITagInfo;
import com.uino.bean.cmdb.business.ClassNodeInfo;
import com.uino.bean.cmdb.query.ESAttrAggBean;
import com.uino.bean.cmdb.query.ESTagSearchBean;
import com.uino.provider.feign.cmdb.TagFeign;
import com.uino.api.client.cmdb.ITagApiSvc;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
public class TagApiSvcRpc implements ITagApiSvc {

    @Autowired
    private TagFeign tagFeign;

    @Override
    public Long saveOrUpdateCITagRule(ESCITagInfo tagInfo) {
        return tagFeign.saveOrUpdateCITagRule(tagInfo);
    }

    @Override
    public ESCITagInfo getCITagRuleById(Long id) {
        return tagFeign.getCITagRuleById(id);
    }

    @Override
    public List<ClassNodeInfo> getTagTree() {
        return tagFeign.getTagTree(BaseConst.DEFAULT_DOMAIN_ID);
    }

    @Override
    public List<ClassNodeInfo> getTagTree(Long domainId) {
        return tagFeign.getTagTree(domainId);
    }

    @Override
    public Page<CcCiInfo> getCIInfoListByTag(ESTagSearchBean bean) {
        return tagFeign.getCIInfoListByTag(bean);
    }

    @Override
    public Integer deleteById(Long tagId) {
        return tagFeign.deleteById(tagId);
    }

    @Override
    public Page<String> getAttrValuesBySearchBean(ESAttrAggBean searchBean) {
        return tagFeign.getAttrValuesBySearchBean(BaseConst.DEFAULT_DOMAIN_ID, searchBean);
    }

    @Override
    public Page<String> getAttrValuesBySearchBean(Long domainId, ESAttrAggBean searchBean) {
        return tagFeign.getAttrValuesBySearchBean(domainId, searchBean);
    }

    @Override
    public Boolean changeTagDir(ESCITagInfo tagInfo) {
        return tagFeign.changeTagDir(tagInfo);
    }
}
