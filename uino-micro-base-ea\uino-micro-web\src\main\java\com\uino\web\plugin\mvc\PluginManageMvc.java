package com.uino.web.plugin.mvc;

import com.alibaba.fastjson.JSONObject;
import com.binary.core.io.Resource;
import com.binary.framework.util.ControllerUtils;
import com.binary.jdbc.Page;
import com.uino.api.client.plugin.IPluginManageSvc;
import com.uino.bean.plugin.base.ESPluginInfo;
import com.uino.bean.plugin.query.CPluginInfo;
import com.uino.init.api.ApiResult;
import com.uino.init.api.ApiVersion;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2021/9/27
 * @Version 1.0
 */

@ApiVersion(1)
@Api(value = "插件管理", tags = {"插件管理"})
@RestController
@RequestMapping("pluginManage")
public class PluginManageMvc {

    @Autowired
    private IPluginManageSvc pluginManageSvc;

//    @ApiOperation("反射方式调用插件")
//    @GetMapping(value = "/invoke")
//    public ApiResult<Object> invokeBean(@ApiParam("Bean名称-类名全名,区分大小写") String beanName, @ApiParam("方法名称,区分大小写") String invokeMethod) {
//        Method method = ReflectionUtils.findMethod(SpringContextUtil.getBean(beanName).getClass(), invokeMethod);
//        Object result = ReflectionUtils.invokeMethod(method, SpringContextUtil.getBean(beanName));
//        return ApiResult.ok(this).data(result);
//    }


    @ApiOperation("获取所有服务")
    @PostMapping(value = "/getServiceList")
    public ApiResult<List<String>> getServiceList() {
        List<String> serviceList = pluginManageSvc.getServiceList();
        return ApiResult.ok(this).data(serviceList);
    }

    @ApiOperation("添加插件")
    @PostMapping(value = "/saveOrUpdate")
    public ApiResult<Long> saveOrUpdate(@RequestBody ESPluginInfo pluginInfo) {
        Long id = pluginManageSvc.saveOrUpdate(pluginInfo);
        return ApiResult.ok(this).data(id);
    }

    @ApiOperation("上传插件")
    @PostMapping(value = "/uploadPlugin")
    public ApiResult<String> uploadPlugin(@RequestParam(name = "id", required = false) Long id, @RequestParam(name = "file") MultipartFile file) {
        String fileName = pluginManageSvc.uploadPlugin(id, file);
        return ApiResult.ok(this).data(fileName);
    }
    @ApiOperation("同步插件")
    @PostMapping(value = "/syncPlugin")
    public ApiResult<String> syncPlugin(@RequestParam(name = "file") MultipartFile file) {
        boolean b = pluginManageSvc.syncPlugin(file);
        return ApiResult.ok(this).data(b);
    }



    @ApiOperation("下载插件")
    @PostMapping(value = "/downloadPlugin")
    public void downloadPlugin(HttpServletRequest request, HttpServletResponse response, @RequestBody Long id) {
        Resource resource = pluginManageSvc.downloadPlugin(id);
        ControllerUtils.returnResource(request, response, resource);
    }

    @ApiOperation("查询插件列表")
    @PostMapping(value = "/queryList")
    public ApiResult<Page<ESPluginInfo>> queryList(@RequestBody String body) {
        JSONObject jsonObject = JSONObject.parseObject(body);
        int pageNum = jsonObject.getInteger("pageNum");
        int pageSize = jsonObject.getInteger("pageSize");
        CPluginInfo cPluginInfo = JSONObject.parseObject(jsonObject.getString("cPluginInfo"), CPluginInfo.class);
        Page<ESPluginInfo> page = pluginManageSvc.queryList(pageNum, pageSize, cPluginInfo);
        return ApiResult.ok(this).data(page);
    }

    @ApiOperation("加载卸载插件")
    @PostMapping(value = "/loadOrUnloadPlugin")
    public ApiResult<Boolean> loadOrUnloadPlugin(@RequestBody Long id) {
        boolean b = pluginManageSvc.loadOrUnloadPlugin(id);
        return ApiResult.ok(this).data(b);
    }


    @ApiOperation("删除插件")
    @PostMapping(value = "/deletePlugin")
    public ApiResult<Boolean> deletePlugin(@RequestBody Long id) {
        boolean b = pluginManageSvc.deletePlugin(id);
        return ApiResult.ok(this).data(b);
    }

    @ApiOperation("查询服务所要加载的插件名称集合")
    @PostMapping(value = "/queryListByOpenServer")
    public ApiResult<List<String>> queryListByOpenServer(@RequestBody String serverName) {
        List<String> list = pluginManageSvc.queryListByOpenServer(serverName);
        return ApiResult.ok(this).data(list);
    }

}
