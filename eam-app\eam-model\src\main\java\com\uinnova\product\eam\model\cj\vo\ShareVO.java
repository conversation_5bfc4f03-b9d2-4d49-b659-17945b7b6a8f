package com.uinnova.product.eam.model.cj.vo;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.model.cj.enums.ShareTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import jakarta.validation.constraints.NotNull;

/**
 * 分享VO
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NotNull
@Builder
public class ShareVO {

    @Comment("分享类型")
    private ShareTypeEnum shareType;

    @Comment("分享时间")
    private Long time;

    @Comment("分享名称")
    private String title;

    @Comment("具体数据")
    private Object data;
}