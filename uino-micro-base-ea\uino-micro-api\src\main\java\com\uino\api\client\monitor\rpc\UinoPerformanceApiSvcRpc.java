package com.uino.api.client.monitor.rpc;

import java.util.Base64;
import java.util.List;

import com.uino.dao.BaseConst;
import org.elasticsearch.index.query.QueryBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import com.binary.jdbc.Page;
import com.uino.bean.monitor.buiness.PerformanceQueryDto;
import com.uino.bean.tp.base.FinalPerformanceDTO;
import com.uino.provider.feign.monitor.UinoPerformanceFeign;
import com.uino.api.client.monitor.IUinoPerformanceApiSvc;

@Service
@Primary
public class UinoPerformanceApiSvcRpc implements IUinoPerformanceApiSvc {

	@Autowired
	private UinoPerformanceFeign perfFeign;

	@Override
	public Boolean saveOrUpdatePerformance(FinalPerformanceDTO perf) {
		return perfFeign.saveOrUpdatePerformance(perf);
	}

	@Override
	public Page<FinalPerformanceDTO> queryPerformancePage(PerformanceQueryDto queryDto) {
		return perfFeign.queryPerformancePage(queryDto);
	}

	@Override
	public Boolean saveOrUpdateBatch(List<FinalPerformanceDTO> perfs) {
		return perfFeign.saveOrUpdateBatch(perfs);
	}

	@Override
	public Page<FinalPerformanceDTO> getSortListByQuery(int pageNum, int pageSize, QueryBuilder query, String order,
			Long timeStart, Long timeEnd, boolean asc) {
		return perfFeign.getSortListByQuery(BaseConst.DEFAULT_DOMAIN_ID,pageNum, pageSize, query, order, timeStart, timeEnd, asc);
	}

	@Override
	public Page<FinalPerformanceDTO> getSortListByQuery(Long domainId, int pageNum, int pageSize, QueryBuilder query, String order, Long timeStart, Long timeEnd, boolean asc) {
		return perfFeign.getSortListByQuery(domainId,pageNum, pageSize, query, order, timeStart, timeEnd, asc);
	}

	@Override
	public Page<FinalPerformanceDTO> getLastPerfListByQuery(int pageNum, int pageSize, QueryBuilder query, String order,
			boolean asc) {
		return perfFeign.getLastPerfListByQuery(BaseConst.DEFAULT_DOMAIN_ID,pageNum, pageSize, query, order, asc);
	}

	@Override
	public Page<FinalPerformanceDTO> getLastPerfListByQuery(Long domainId,int pageNum, int pageSize,  QueryBuilder query, String order, boolean asc) {
		return perfFeign.getLastPerfListByQuery(domainId ,pageNum, pageSize,query, order, asc);
	}

	@Override
	public List<FinalPerformanceDTO> getCurrentPerformance(List<String> ciCodes, List<String> kpiCodes) {
		return perfFeign.getCurrentPerformance(BaseConst.DEFAULT_DOMAIN_ID,ciCodes, kpiCodes);
	}

	@Override
	public List<FinalPerformanceDTO> getCurrentPerformance(Long domainId, List<String> ciCodes, List<String> kpiCodes) {
		return perfFeign.getCurrentPerformance(domainId,ciCodes, kpiCodes);
	}

	@Override
	public List<FinalPerformanceDTO> getCurrentPerformanceByCiCodes(List<String> ciCodes) {
		return perfFeign.getCurrentPerformanceByCiCodes(BaseConst.DEFAULT_DOMAIN_ID,ciCodes);
	}

	@Override
	public List<FinalPerformanceDTO> getCurrentPerformanceByCiCodes(Long domainId, List<String> ciCodes) {
		return perfFeign.getCurrentPerformanceByCiCodes(domainId,ciCodes);
	}

	@Override
	public List<FinalPerformanceDTO> getPerformanceByConditional(String ciCode, String kpiCode, Long startTime,
			Long endTime) {
		return perfFeign.getPerformanceByConditional(BaseConst.DEFAULT_DOMAIN_ID,ciCode, kpiCode, startTime, endTime);
	}

	@Override
	public List<FinalPerformanceDTO> getPerformanceByConditional(Long domainId, String ciCode, String kpiCode, Long startTime, Long endTime) {
		return perfFeign.getPerformanceByConditional(domainId,ciCode, kpiCode, startTime, endTime);
	}

}
