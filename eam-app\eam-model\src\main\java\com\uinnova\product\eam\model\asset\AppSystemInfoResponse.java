package com.uinnova.product.eam.model.asset;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 应用系统详情页返回体
 * <AUTHOR>
 */
@Data
public class AppSystemInfoResponse {
    private String ciCode;
    private String title;
    private String icon;
    private List<Map<String, String>> oneRow = new ArrayList<>();
    private List<String> twoRow = new ArrayList<>();
    private String threeRow;
    private List<AppSystemStructureInfo> body;


}
