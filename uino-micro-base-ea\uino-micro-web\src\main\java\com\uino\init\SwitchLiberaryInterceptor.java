package com.uino.init;

import com.uino.util.sys.LibTypeUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

@Component
public class SwitchLiberaryInterceptor implements HandlerInterceptor {

    @Value("${uino.cmdb.libtype:BASELINE}")
    private String libType;

    /**
     * 添加三库标识
     * @param request
     * @param response
     * @param handler
     * @return
     * @throws Exception
     */
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
//        String url = request.getRequestURI();
//        //配置三库标识只对数据超市接口有用，如需全部生效可去掉
//        if (!url.contains("/cmdb/dataSet/")) {
//            return true;
//        }
        String paramLibType = request.getParameter("libType");
        if (StringUtils.isEmpty(paramLibType)) {
            LibTypeUtil.setLibType(libType);
        } else {
            LibTypeUtil.setLibType(paramLibType);
        }
        return true;
    }

    /**
     * 清理三库标识
     * @param request
     * @param response
     * @param handler
     * @param ex
     * @throws Exception
     */
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        LibTypeUtil.removeLibType();
    }
}
