package com.uino.bean.permission.base;




import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;


/**
 * mapping-table: 角色表[SYS_ROLE]
 * 
 * <AUTHOR>
 */
@ApiModel(value="角色表",description = "角色表")
public class SysRole implements Serializable {
	private static final long serialVersionUID = 1L;


	 /**ID*/
	 @ApiModelProperty(value="id",example = "123")
	private Long id;


	 /**角色名称*/
	 @ApiModelProperty(value="角色名称",example = "admin")
	private String roleName;
	/**
	 * 引导向人员id配置
	 */
	 private List<Long> bootEntryRoleIds;


	 /**角色描述*/
	 @ApiModelProperty(value="角色描述")
	private String roleDesc;


	 /**状态*/
	 @ApiModelProperty(value="状态",example = "1")
	private Integer status;


	 /**所属域*/
	 @ApiModelProperty(value="所属域id",example = "123")
	private Long domainId;


	 /**创建人*/
	 @ApiModelProperty(value="创建人",example = "123")
	private String creator;


	 /**修改人*/
	 @ApiModelProperty(value="修改人",example = "mike")
	private String modifier;


	 /**创建时间*/
	 @ApiModelProperty(value="创建时间")
	private Long createTime;


	 /**修改时间*/
	 @ApiModelProperty(value="修改时间")
	private Long modifyTime;




	public Long getId() {
		return this.id;
	}
	public void setId(Long id) {
		this.id = id;
	}


	public String getRoleName() {
		return this.roleName;
	}
	public void setRoleName(String roleName) {
		this.roleName = roleName;
	}


	public String getRoleDesc() {
		return this.roleDesc;
	}
	public void setRoleDesc(String roleDesc) {
		this.roleDesc = roleDesc;
	}


	public Integer getStatus() {
		return this.status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}


	public Long getDomainId() {
		return this.domainId;
	}
	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}


	public String getCreator() {
		return this.creator;
	}
	public void setCreator(String creator) {
		this.creator = creator;
	}


	public String getModifier() {
		return this.modifier;
	}
	public void setModifier(String modifier) {
		this.modifier = modifier;
	}


	public Long getCreateTime() {
		return this.createTime;
	}
	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}


	public Long getModifyTime() {
		return this.modifyTime;
	}
	public void setModifyTime(Long modifyTime) {
		this.modifyTime = modifyTime;
	}


	public List<Long> getBootEntryRoleIds() {
		return bootEntryRoleIds;
	}

	public void setBootEntryRoleIds(List<Long> bootEntryRoleIds) {
		this.bootEntryRoleIds = bootEntryRoleIds;
	}

	@Override
	public boolean equals(Object o) {
		if (this == o) return true;
		if (o == null || getClass() != o.getClass()) return false;
		SysRole sysRole = (SysRole) o;
		return Objects.equals(id, sysRole.id) && Objects.equals(roleName, sysRole.roleName) && Objects.equals(bootEntryRoleIds, sysRole.bootEntryRoleIds) && Objects.equals(roleDesc, sysRole.roleDesc) && Objects.equals(status, sysRole.status) && Objects.equals(domainId, sysRole.domainId) && Objects.equals(creator, sysRole.creator) && Objects.equals(modifier, sysRole.modifier) && Objects.equals(createTime, sysRole.createTime) && Objects.equals(modifyTime, sysRole.modifyTime);
	}

	@Override
	public int hashCode() {
		return Objects.hash(id, roleName, bootEntryRoleIds, roleDesc, status, domainId, creator, modifier, createTime, modifyTime);
	}
}


