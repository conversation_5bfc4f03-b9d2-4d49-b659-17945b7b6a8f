package com.uino.util.sys;

import com.binary.core.encrypt.Encrypt;
import com.binary.core.encrypt.EncryptCipher;
import com.binary.core.encrypt.EncryptKey;
import com.binary.core.exception.EncryptException;
import com.binary.core.io.SerializationUtils;
import com.binary.core.util.BinaryUtils;
import com.binary.core.util.NumberCompressor;
import com.binary.framework.Local;
import com.binary.framework.bean.User;
import com.uino.bean.permission.base.SysUser;
import com.uino.util.exception.LoginException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.helpers.MessageFormatter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESKeySpec;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import java.nio.charset.Charset;
import java.security.Key;
import java.security.KeyPairGenerator;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Base64;

/**
 * 公共帮助类
 *
 * <AUTHOR>
 */
@Component
@RefreshScope
@Slf4j
public class SysUtil implements ApplicationRunner {

    private static boolean needAuth;
    private static Boolean apiEnable;
    private static String apiEnableUserId;
    private static String apiEnableLoginCode;
    private static String apiEnableUserName;
    private static String apiEnableDomainId;
    private static SysUser developer;


    private static CurrentUserGetter currentUserGetter;

    @Autowired(required = false)
    public void setCurrentUserGetter(CurrentUserGetter currentUserGetter) {
        SysUtil.currentUserGetter = currentUserGetter;
    }

    @Value("${oauth.open:false}")
    public void setNeedAuth(boolean needAuth) {
        SysUtil.needAuth = needAuth;
    }

    @Value("${base.api.enable:false}")
    public void setApiEnable(boolean apiEnable) {
        SysUtil.apiEnable = apiEnable;
    }

    @Value("${api.enable.user-id:null}")
    public void setApiEnableUserId(String apiEnableUserId) {
        SysUtil.apiEnableUserId = apiEnableUserId;
    }

    @Value("${api.enable.login-code:null}")
    public void setApiEnableLoginCode(String apiEnableLoginCode) {
        SysUtil.apiEnableLoginCode = apiEnableLoginCode;
    }

    @Value("${api.enable.user-name:null}")
    public void setApiEnableUserName(String apiEnableUserName) {
        SysUtil.apiEnableUserName = apiEnableUserName;
    }

    @Value("${api.enable.domain-id:null}")
    public void setApiEnableDomainId(String apiEnableDomainId) {
        SysUtil.apiEnableDomainId = apiEnableDomainId;
    }

    /**
     * The development environment obtains local configuration users
     *
     * @return user
     */
    private static SysUser developerEnvironment() {
        if (developer == null) {
            try {
                if (apiEnableUserId != null && apiEnableLoginCode != null && apiEnableUserName != null && apiEnableDomainId != null) {
                    long id = Long.parseLong(apiEnableUserId);
                    long domainId = Long.parseLong(apiEnableDomainId);
                    String L_G_C_D = apiEnableLoginCode.trim();
                    String U_S_N_M = apiEnableUserName.trim();
                    developer = new SysUser();
                    developer.setId(id);
                    developer.setDomainId(domainId);
                    developer.setLoginCode(L_G_C_D);
                    developer.setUserName(U_S_N_M);
                }
            } catch (RuntimeException ignored) {
            }
        }
        return developer;
    }

    /**
     * 获取当前登陆用户
     *
     * @return
     */
    public static SysUser getCurrentUserInfo() {
        if (currentUserGetter != null) {
            return currentUserGetter.getCurrentUserInfo();
        }
        String token = TokenUtil.getCurrentThreadToken();
        if (needAuth && token==null) {
            try {
                if (Local.isOpen()) {
                    User user = Local.getUser();
                    if (user == null) {
                        throw new LoginException("USER_DONOT_LOGIN");
                    }
                    return SysUser.builder().id(user.getId())
                            .domainId(user.getDomainId())
                            .userName(user.getUserName())
                            .loginCode(user.getLoginCode()).build();
                }else {
                    throw new LoginException("USER_DONOT_LOGIN");
                }
            }catch (Exception e){
                throw new LoginException("USER_DONOT_LOGIN");
            }

        }else {
            return getCurrentUserInfo(token);
        }
    }

    /**
     * 获取当前登陆用户
     *
     * @return
     */
    public static SysUser getCurrentUserInfoNotThrow() {
        try {
            return getCurrentUserInfo();
        }catch (Exception e){
            log.error("无法获取用户信息:{}",e.getMessage());
            // 封装默认值
            return SysUser.builder().id(null)
                    .domainId(1L)
                    .userName("")
                    .loginCode(null).build();
        }
    }


    /**
     * 获取当前登陆用户信息by-token
     *
     * @param token
     * @return
     */
    public static SysUser getCurrentUserInfo(String token) {
        if (currentUserGetter != null) {
            return currentUserGetter.getCurrentUserInfo(token);
        }
        if (needAuth) {
            SysUser currentUser = null;
            try {
                if (token != null && !"".equals(token.trim())) {
                    String userStr = token.substring(36);
                    userStr = StringUtil.Base64.decode(userStr);
                    String[] userStrs = userStr.split("&");
                    Long id = Long.valueOf(userStrs[0]);
                    String loginCode = userStrs[1];
                    Long domainId = Long.valueOf(userStrs[2]);
                    currentUser = SysUser.builder().id(id).domainId(domainId).loginCode(loginCode).build();
                }
            } catch (Exception e) {
                throw new LoginException("异常格式凭据");
            }

            // currentUser = TokenUtil.getUser(token);
            if (currentUser == null) {
                throw new LoginException("USER_DONOT_LOGIN");
            }
            return currentUser;
        } else {
            SysUser currentUser = null;
            if (apiEnable != null && apiEnable && !needAuth) {
                SysUser localDeveloper = developerEnvironment();
                if (localDeveloper != null) {
                    currentUser = localDeveloper;
                }
            }
            if (currentUser == null && token != null) {
                token = token.trim();
                if (!"".equals(token) && !"null".equals(token)) {
                    currentUser = TokenUtil.getUser(token);
                }
            }

            if (currentUser == null) {
                throw new LoginException("USER_DONOT_LOGIN");
            }
            return currentUser;
        }
    }

    /**
     * 获取请求ip
     *
     * @param request
     * @return
     */
    public static String getIpAddress(HttpServletRequest request) {
            String ip = "";
            try {
            if (request == null) {
                ServletRequestAttributes attrs = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
                // 兼容oauth子线程调用
                ip = (String) attrs.getAttribute("Authorization-Client-IP", 1);
                request = attrs.getRequest();
            }

            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("x-forwarded-for");
            }
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("Proxy-Client-IP");
            }
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("WL-Proxy-Client-IP");
            }
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("HTTP_CLIENT_IP");
            }
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("HTTP_X_FORWARDED_FOR");
            }
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getRemoteAddr();
            }
        } catch (Exception ignored) {
        }
        return ip;
    }

    @Override
    public void run(ApplicationArguments args) throws Exception {
    }

    /**
     * 静态属性帮助类
     *
     * <AUTHOR>
     */
    public static class StaticUtil {

        /**
         * 关系分类主键标识名称
         */
        public static final String RLTCLS_MAJOR_MARK = "主键";

        /**
         * 关系分类主键标识名称
         */
        public static final String CICLS_MAJOR_MARK = "主键";

        /**
         * cicode显示名称
         */
        public static final String CICODE_LABEL = "CI Code";

        /**
         * readme的sheet名
         */
        public static final String README_SHEETNAME = "填写须知";

        public static final String RLT_DEF = "关系定义";

        public static final String CI_DEF = "对象定义";

        public static final String CI = "CI";

        public static final String RLT = "RLT";

        /**
         * 配置日志-新增
         */
        public static final Integer LOG_DYNAMIC_INSERT = 1;

        /**
         * 配置日志-修改
         */
        public static final Integer LOG_DYNAMIC_UPDATE = 2;

        /**
         * 配置日志-删除
         */
        public static final Integer LOG_DYNAMIC_DELETE = 3;
    }

    public static class EncryptDES {

        private static final String DEFAULT_KEY = "base@uinnova";

        private static final String KEY_DES = "DES";

        private static final String UTF8 = "UTF-8";

        private static Key getToKey() throws Exception {
            SecretKey secretKey = null;
            DESKeySpec dks = new DESKeySpec(DEFAULT_KEY.getBytes(UTF8));
            SecretKeyFactory factory = SecretKeyFactory.getInstance(KEY_DES);
            secretKey = factory.generateSecret(dks);
            return secretKey;
        }

        public static String encryptDES(String msg) {
            if (msg == null || "".equals(msg.trim())) {
                return msg;
            }
            try {
                Key k = getToKey();
                Cipher cipher = Cipher.getInstance(KEY_DES);
                cipher.init(Cipher.ENCRYPT_MODE, k);
                byte[] bytes = cipher.doFinal(msg.getBytes(UTF8));
                return new String(Base64.getEncoder().encode(bytes), UTF8);
            } catch (Exception e) {
                return null;
            }
        }

        public static String decryptDES(String msg) {
            if (msg == null || "".equals(msg.trim())) {
                return msg;
            }
            try {
                Key k = getToKey();
                Cipher cipher = Cipher.getInstance(KEY_DES);
                cipher.init(Cipher.DECRYPT_MODE, k);
                byte[] bytes = cipher.doFinal(Base64.getDecoder().decode(msg.getBytes(UTF8)));
                return new String(bytes, UTF8);
            } catch (Exception e) {
                return null;
            }
        }
    }

    public static class EncryptRSA {

        public static class KeyPair {

            private String publicKey;

            private String privateKey;

            public KeyPair(String publicKey, String privateKey) {
                this.publicKey = publicKey;
                this.privateKey = privateKey;
            }

            public String getPublicKey() {
                return publicKey;
            }

            public String getPrivateKey() {
                return privateKey;
            }
        }

        /**
         * 获取公钥/私钥健值对
         *
         * @return
         */
        public static KeyPair getKeyPair() {
            return getKeyPair(1024);
        }

        /**
         * 获取公钥/私钥健值对
         *
         * @param keysize : 指定密钥长度, 必须是512的倍数
         * @return
         */
        public static KeyPair getKeyPair(int keysize) {
            try {
                KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance("RSA");
                SecureRandom secrand = new SecureRandom();
                secrand.setSeed(BinaryUtils.getUUID().getBytes());
                keyPairGenerator.initialize(keysize, secrand);
                java.security.KeyPair keyPair = keyPairGenerator.generateKeyPair();
                Key publicKey = keyPair.getPublic();
                Key privateKey = keyPair.getPrivate();
                String strPublicKey = Encrypt.byte2String(SerializationUtils.serialize(publicKey));
                String strPrivateKey = Encrypt.byte2String(SerializationUtils.serialize(privateKey));
                return new KeyPair(strPublicKey, strPrivateKey);
            } catch (NoSuchAlgorithmException e) {
                throw BinaryUtils.transException(e, EncryptException.class);
            }
        }

        /**
         * 加密
         *
         * @param key  : 公钥/私钥
         * @param data : 被加密数据
         * @return 加密之后的密文
         */
        public static String encrypt(String key, String data) {
            BinaryUtils.checkEmpty(key, "key");
            BinaryUtils.checkEmpty(data, false, "data");
            try {
                Cipher cipher = Cipher.getInstance("RSA");
                byte[] array = Encrypt.string2Byte(key);
                Key keyObj = SerializationUtils.deserialize(array, Key.class);
                cipher.init(Cipher.ENCRYPT_MODE, keyObj);
                byte[] code = cipher.doFinal(data.getBytes());
                return Encrypt.byte2String(code);
            } catch (Exception e) {
                throw BinaryUtils.transException(e, EncryptException.class);
            }
        }

        /**
         * 加密
         *
         * @param key  : 公钥/私钥
         * @param data : 被加密数据
         * @return 加密之后的密文
         */
        public static String encrypt(EncryptKey key, String data) {
            BinaryUtils.checkEmpty(key, "key");
            BinaryUtils.checkEmpty(data, false, "data");
            try {
                Cipher cipher = Cipher.getInstance("RSA");
                cipher.init(Cipher.ENCRYPT_MODE, key.getKey());
                byte[] code = cipher.doFinal(data.getBytes());
                return Encrypt.byte2String(code);
            } catch (Exception e) {
                throw BinaryUtils.transException(e, EncryptException.class);
            }
        }

        /**
         * 加密
         *
         * @param cipher : 公钥/私钥
         * @param data   : 被加密数据
         * @return 加密之后的密文
         */
        public static String encrypt(EncryptCipher cipher, String data) {
            BinaryUtils.checkEmpty(cipher, "cipher");
            BinaryUtils.checkEmpty(data, false, "data");
            try {
                byte[] code = cipher.getCipher().doFinal(data.getBytes());
                return Encrypt.byte2String(code);
            } catch (Exception e) {
                throw BinaryUtils.transException(e, EncryptException.class);
            }
        }

        /**
         * 解密
         *
         * @param key  : 公钥/私钥
         * @param code : 密文
         * @return 解密之后数据
         */
        public static String decrypt(String key, String code) {
            BinaryUtils.checkEmpty(code, "code");
            EncryptCipher cipher = new EncryptCipher(key, EncryptCipher.TRANS_RSA, 2);
            synchronized (EncryptRSA.class) {
                String[] codes = deSpilt(code);
                StringBuffer sBuf = new StringBuffer();
                for (String sCode : codes) {
                    sBuf.append(EncryptRSA.decrypt(cipher, sCode));
                }
                return sBuf.toString();
            }
        }

        private final static int DE_MANX_LENGTH = 128;

        private static String[] deSpilt(String token) {
            int len = token.length();
            int size = len % DE_MANX_LENGTH == 0 ? len / DE_MANX_LENGTH : len / DE_MANX_LENGTH + 1;
            if (size == 1) {
                return new String[]{token};
            }
            String[] result = new String[size];
            for (int i = 0; i < size; i++) {
                int st = i * DE_MANX_LENGTH;
                int end = st + DE_MANX_LENGTH;
                end = end > len ? len : end;
                result[i] = token.substring(st, end);
            }
            return result;
        }
        // public static String decrypt(String key, String code) {
        // BinaryUtils.checkEmpty(key, "key");
        // BinaryUtils.checkEmpty(code, false, "code");
        //
        // try {
        // Cipher cipher = Cipher.getInstance("RSA");
        //
        // byte[] array = Encrypt.string2Byte(key);
        // Key keyObj = SerializationUtils.deserialize(array, Key.class);
        // cipher.init(Cipher.DECRYPT_MODE, keyObj);
        //
        // byte[] data = cipher.doFinal(Encrypt.string2Byte(code));
        // return new String(data);
        // } catch (Exception e) {
        // throw BinaryUtils.transException(e, EncryptException.class);
        // }
        // }

        /**
         * 解密
         *
         * @param key  : 公钥/私钥
         * @param code : 密文
         * @return 解密之后数据
         */
        public static String decrypt(EncryptKey key, String code) {
            BinaryUtils.checkEmpty(key, "key");
            BinaryUtils.checkEmpty(code, false, "code");
            try {
                Cipher cipher = Cipher.getInstance("RSA");
                cipher.init(Cipher.DECRYPT_MODE, key.getKey());
                byte[] data = cipher.doFinal(Encrypt.string2Byte(code));
                return new String(data);
            } catch (Exception e) {
                throw BinaryUtils.transException(e, EncryptException.class);
            }
        }

        /**
         * 解密
         *
         * @param cipher : 公钥/私钥
         * @param code   : 密文
         * @return 解密之后数据
         */
        public static String decrypt(EncryptCipher cipher, String code) {
            BinaryUtils.checkEmpty(cipher, "cipher");
            BinaryUtils.checkEmpty(code, false, "code");
            try {
                byte[] data = cipher.getCipher().doFinal(Encrypt.string2Byte(code));
                return new String(data);
            } catch (Exception e) {
                throw BinaryUtils.transException(e, EncryptException.class);
            }
        }
    }

    public static class TokenUtil {

        private static String giveUp0(String s) {
            int a = 0;
            for (int i = 0; i < s.length(); i++) {
                if (s.charAt(i) != 0) {
                    a = i;
                    break;
                }
            }
            return s.substring(a, s.length());
        }

        /**
         * //userId|loginCode|userName|Language|IP|createTime|validTime|
         * resurgeTime //userId|Language|createTime|validTime|loginCode userId
         * domainId kind Language createTime validTime loginCode userName
         * 将token转换为user对象
         *
         * @param t
         * @return
         */
        public static SysUser getUser(String t) {
            // 8+4+1+1+5+2+16+16
            String token = EncryptRSA.decrypt(
                    "aced0005737200146a6176612e73656375726974792e4b6579526570bdf94fb3889aa5430200044c0009616c676f726974686d7400124c6a6176612f6c616e672f537472696e673b5b0007656e636f6465647400025b424c0006666f726d617471007e00014c00047479706574001b4c6a6176612f73656375726974792f4b657952657024547970653b7870740003525341757200025b42acf317f8060854e002000078700000005e305c300d06092a864886f70d0101010500034b003048024100d0f8f0740d7425ae172085716c7c7e06de29020e4dd4ba37f82310840238435d1c46496581d44743133ae69accc7b67b72249b1db116998d4ae576ffe65fbcf50203010001740005582e3530397e7200196a6176612e73656375726974792e4b6579526570245479706500000000000000001200007872000e6a6176612e6c616e672e456e756d000000000000000012000078707400065055424c4943",
                    t);
            String userId = giveUp0(token.substring(0, 8));
            String domainId = giveUp0(token.substring(8, 12));
            String cn = giveUp0(token.substring(21));
            char[] arr = cn.toCharArray();
            int zi = -1;
            int uzi = -1;
            for (int i = 0; i < arr.length; i++) {
                if (arr[i] == 0) {
                    if (zi < 0) {
                        zi = i;
                    }
                } else {
                    if (zi > 0 && uzi < 0) {
                        uzi = i;
                    }
                }
            }
            String loginCode = null;
            String userName = null;
            if (zi < 0) {
                loginCode = token.substring(21, 37);
                userName = token.substring(37);
            } else {
                loginCode = new String(arr, 0, zi);
                userName = uzi > 0 ? new String(arr, uzi, arr.length - uzi) : "";
            }
            SysUser user = new SysUser();
            user.setId(NumberCompressor.decompress(userId));
            user.setDomainId(NumberCompressor.decompress(domainId));
            user.setLoginCode(loginCode);
            user.setUserName(userName);
            return user;
        }

        /**
         * 获取当前线程token
         *
         * @return
         */
        public static String getCurrentThreadToken() {
            if (currentUserGetter != null) {
                return currentUserGetter.getCurrentToken();
            }
            String token = null;
            if (needAuth) {
                try {
                    ServletRequestAttributes attrs = (ServletRequestAttributes) RequestContextHolder
                            .getRequestAttributes();
                    // Authorization
                    token = attrs.getRequest().getHeader("Authorization");
                    if (token != null) {
                        token = token.split(" ")[1];
                    } else {
                        token = attrs.getRequest().getParameter("access_token");
                        if (token == null) {
                            token = attrs.getRequest().getParameter("tk");
                        }
                    }
                    // if (token == null) {
                    // for (Cookie cookie : attrs.getRequest().getCookies()) {
                    // if
                    // (cookie.getName().toLowerCase().equals("access_token")) {
                    // token = cookie.getValue();
                    // break;
                    // }
                    // }
                    // }
                } catch (NullPointerException e) {
                    // TODO: handle exception
                }
            } else {
                try {
                    ServletRequestAttributes attrs = (ServletRequestAttributes) RequestContextHolder
                            .getRequestAttributes();
                    token = attrs.getRequest().getHeader("token");
                    if (token == null) {
                        token = attrs.getRequest().getHeader("tk");
                        if (token == null) {
                            for (Cookie cookie : attrs.getRequest().getCookies()) {
                                String cookieName = cookie.getName().toLowerCase();
                                if ("token".equals(cookieName) || "tk".equals(cookieName)) {
                                    token = cookie.getValue();
                                    break;
                                }
                            }
                        }
                    }
                } catch (NullPointerException e) {
                    // TODO: handle exception
                }
                // 针对proxima默认值dafault_tk，做特殊处理，否则解析失败
                if (token != null && "default_tk".equals(token)) {
                    token = null;
                }
            }
            return token;
        }

    }

    /**
     * 字符串操作帮助类
     *
     * <AUTHOR>
     */
    public static class StringUtil {

        /**
         * 将带表达式字符串填补转化为填充后完整字符串
         *
         * @param formatStr 带表达式字符串eg: 我叫{}，今天去{}玩了
         * @param values    ["小明","苏州"]
         * @return 我叫小明，今天去苏州玩了
         * @see MessageFormatter#arrayFormat
         */
        public static String newFormatString(String formatStr, String... values) {
            if (values != null && values.length > 0) {
                return MessageFormatter.arrayFormat(formatStr, values).getMessage();
            } else {
                return formatStr;
            }
        }

        /**
         * 判断字符串是否不为空
         *
         * @param validStr 验证字符串
         * @return true:不为空false:为空或空字符串
         */
        public static boolean isNotBack(String validStr) {
            if (validStr == null || validStr.trim().equals("")) {
                return false;
            } else {
                return true;
            }
        }

        /**
         * 判断字符串是否为空
         *
         * @param validStr 验证字符串
         * @return true:为空或空字符串false:不为空
         */
        public static boolean isBack(String validStr) {
            return !isNotBack(validStr);
        }

        /**
         * base64编解码工具类
         *
         * <AUTHOR>
         */
        // @SuppressWarnings("deprecation")
        public static class Base64 {

            /**
             * 编码-会替换base +/=特殊字符为._ _. _
             *
             * @param str
             * @return
             */
            public static String encry(String str) {
                str = java.util.Base64.getEncoder().encodeToString(str.getBytes());
                str = str.replaceAll("\\+", "._");
                str = str.replaceAll("/", "_.");
                str = str.replaceAll("=", "_");
                return str;
            }

            /**
             * 解码-会将._ _. _转回+/=
             *
             * @param str
             * @return
             */
            public static String decode(String str) {
                str = str.replaceAll("\\._", "+");
                str = str.replaceAll("_\\.", "/");
                str = str.replaceAll("_", "=");
                str = StringUtils.toEncodedString(java.util.Base64.getDecoder().decode(str.getBytes()),
                        Charset.forName("UTF-8"));
                return str;
            }
        }
    }
}
