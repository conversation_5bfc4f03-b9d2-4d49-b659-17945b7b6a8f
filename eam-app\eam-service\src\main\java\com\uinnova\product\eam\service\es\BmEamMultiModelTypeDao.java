package com.uinnova.product.eam.service.es;

import com.uinnova.product.eam.comm.model.es.CEamMultiModelType;
import com.uinnova.product.eam.comm.model.es.EamMultiModelType;
import com.uino.dao.AbstractESBaseDao;
import com.uino.service.util.FileUtil;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.util.List;

/**
 * 多模型类型Dao
 * <AUTHOR>
 */
@Service
public class BmEamMultiModelTypeDao extends AbstractESBaseDao<EamMultiModelType, CEamMultiModelType> {

    @Override
    public String getIndex() {
        return "uino_eam_multi_model_type";
    }

    @Override
    public String getType() {
        return "_doc";
    }

    @PostConstruct
    public void init() {
        List<EamMultiModelType> list = FileUtil.getData("/initdata/uino_eam_multi_model_type.json", EamMultiModelType.class);
        super.initIndex(list);
    }
}
