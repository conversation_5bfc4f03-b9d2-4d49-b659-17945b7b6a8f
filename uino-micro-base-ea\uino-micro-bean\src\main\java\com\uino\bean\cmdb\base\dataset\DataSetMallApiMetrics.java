package com.uino.bean.cmdb.base.dataset;

import java.util.List;
import java.util.regex.Pattern;

import org.springframework.util.Assert;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.binary.core.util.BinaryUtils;
import com.uino.bean.permission.business.IValidDto;

import lombok.Getter;
import lombok.Setter;

/**
 * @Classname DataSetMallApiMetrics
 * @Description 指标数据集
 * @Date 2020/11/20 15:46
 * <AUTHOR> zmj
 */
@Getter
@Setter
public class DataSetMallApiMetrics extends DataSetMallApi implements IValidDto {

    private static final long serialVersionUID = 1L;

    private Long metricId;

    private Long classId;

    private String metricTagName;

    private List<String> metricTagVals;

    private String timeFlag = "1h";

    private String aggFlag = "sum";

    /**
     * 聚合时间维度，1m，1h，1w，1M
     */
    private String interval = "5m";

    /**
     * 图表类型,0=柱状图，1=饼图，2=堆叠图，3=条形图,4=曲线图
     */
    private Integer chartType = 4;

    public DataSetMallApiMetrics() {}

    public DataSetMallApiMetrics(JSONObject json) {
        super(json);
        if (json.containsKey("metricId")) {
            this.metricId = json.getLong("metricId");
        }
        if (json.containsKey("classId")) {
            this.classId = json.getLong("classId");
        }
        if (json.containsKey("metricTagName")) {
            this.metricTagName = json.getString("metricTagName");
        }
        if (json.containsKey("metricTagVals")) {
            this.metricTagVals = JSON.parseArray(JSON.toJSONString(json.getJSONArray("metricTagVals")), String.class);
        }
        if (json.containsKey("timeFlag")) {
            this.timeFlag = json.getString("timeFlag");
        }
        if (json.containsKey("aggFlag")) {
            AggFlagTypeEnum aggFlagTypeEnum = AggFlagTypeEnum.valueOfType(json.getString("aggFlag"));
            Assert.notNull(aggFlagTypeEnum, "聚合方式设置错误");
            this.aggFlag = aggFlagTypeEnum == null ? AggFlagTypeEnum.SUM.getType() : aggFlagTypeEnum.getType();
        }
        if (json.containsKey("interval")) {
            this.interval = json.getString("interval");
        }
        if (json.containsKey("chartType")) {
            this.chartType = json.getInteger("chartType");
        }
    }

    @Override
    public JSONObject toJson() {
        JSONObject json = super.toJson();
        json.put("classId", classId);
        json.put("metricId", metricId);
        json.put("metricTagName", metricTagName);
        json.put("metricTagVals", metricTagVals);
        json.put("timeFlag", timeFlag);
        json.put("aggFlag", aggFlag);
        json.put("interval", interval);
        json.put("chartType", chartType);
        return json;
    }

    @Override
    public void valid() {
        Assert.isTrue(!BinaryUtils.isEmpty(getMetricId()), "X_PARAM_NOT_NULL${name:metricId}");
        Assert.isTrue(!BinaryUtils.isEmpty(getClassId()), "X_PARAM_NOT_NULL${name:classId}");
        Assert.isTrue(!BinaryUtils.isEmpty(getName()), "X_PARAM_NOT_NULL${name:dataSetName}");
        Assert.isTrue(!BinaryUtils.isEmpty(getMetricTagName()), "X_PARAM_NOT_NULL${name:metricTagName}");
        Pattern pattern = Pattern.compile("-?\\d+[\\\\m\\\\h\\\\d\\\\w\\\\M\\\\y]{1}");
        Assert.isTrue(timeFlag.length() > 1 && pattern.matcher(timeFlag).matches(), "时间范围格式错误");
        Assert.isTrue(interval.length() > 1 && pattern.matcher(interval).matches(), "聚合维度格式错误");
    }

    public enum AggFlagTypeEnum {
        SUM("sum"), MAX("max"), MIN("min"), AVG("avg"), ORIGIN("origin");
        private String type;

        private AggFlagTypeEnum(String type) {
            this.type = type;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public static AggFlagTypeEnum valueOfType(String type) {
            switch (type) {
                case "sum":
                    return SUM;
                case "max":
                    return MAX;
                case "min":
                    return MIN;
                case "avg":
                    return AVG;
                case "origin":
                    return ORIGIN;
                default:
                    Assert.isTrue(false, "聚合方式设置错误");
                    break;
            }
            return null;
        }

    }

}
