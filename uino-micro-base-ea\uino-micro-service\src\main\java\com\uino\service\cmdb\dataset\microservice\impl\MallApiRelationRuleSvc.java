package com.uino.service.cmdb.dataset.microservice.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.binary.core.exception.MessageException;
import com.binary.core.util.BinaryUtils;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClass;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uino.bean.cmdb.base.ESCiClassRlt;
import com.uino.bean.cmdb.base.dataset.DataSetMallApi;
import com.uino.bean.cmdb.base.dataset.DataSetMallApiRelationRule;
import com.uino.bean.cmdb.base.dataset.OperateType;
import com.uino.bean.cmdb.base.dataset.log.DataSetMallApiLog;
import com.uino.bean.cmdb.base.dataset.relation.RelationRuleAttrCdt;
import com.uino.bean.cmdb.base.dataset.relation.RelationRuleLine;
import com.uino.bean.cmdb.base.dataset.relation.RelationRuleNode;
import com.uino.bean.cmdb.business.ClassRltQueryDto;
import com.uino.bean.cmdb.business.dataset.DataSetExeResultSheetPage;
import com.uino.bean.cmdb.business.dataset.FriendInfo;
import com.uino.bean.cmdb.query.ESCiClassRltSearchBean;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.permission.query.CSysUser;
import com.uino.dao.cmdb.dataset.ESDataSetMallApiLogSvc;
import com.uino.dao.util.ESUtil;
import com.uino.service.cmdb.dataset.microservice.IDataSetSvc;
import com.uino.service.cmdb.dataset.microservice.IMallApiSvc;
import com.uino.service.cmdb.microservice.ICIClassRltSvc;
import com.uino.service.cmdb.microservice.ICIClassSvc;
import com.uino.service.cmdb.microservice.ICISvc;
import com.uino.service.cmdb.microservice.IRltClassSvc;
import com.uino.service.permission.microservice.IUserSvc;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 关系遍历
 *
 * @Date 2020/3/25 16:42
 * @Created by sh
 */
@Service
public class MallApiRelationRuleSvc implements IMallApiSvc {

    Log logger = LogFactory.getLog(MallApiRelationRuleSvc.class);

    @Autowired
    private ICIClassRltSvc classRltSvc;

    @Autowired
    private ICIClassSvc classSvc;

    @Autowired
    private ICISvc iciSvc;

    @Autowired
    private RelationRuleAnalysisSvc relationRuleAnalysisSvc;

    @Autowired
    private IRltClassSvc iRltClassSvc;

    @Autowired
    private IDataSetSvc iDataSetSvc;

    @Autowired
    private ESDataSetMallApiLogSvc esDataSetMallApiLogSvc;

    @Autowired
    private IUserSvc userSvc;

    @Override
    public DataSetMallApi checkCharacteristic(SysUser user, JSONObject jsonObject) {
        DataSetMallApiRelationRule dataSetMallApiRelationRule = new DataSetMallApiRelationRule(jsonObject);
        dataSetMallApiRelationRule.setDomainId(user.getDomainId());
        Map<Long, Map<Long, String>> classInfoMap = new HashMap<Long, Map<Long, String>>();
        CCcCiClass ciClassCdt = new CCcCiClass();
        ciClassCdt.setDomainId(user.getDomainId());
        List<CcCiClassInfo> classInfoList = classSvc.queryClassByCdt(ciClassCdt);
        if (classInfoList != null) {
            for (CcCiClassInfo classInfo : classInfoList) {
                Map<Long, String> attrMap = new HashMap<Long, String>();
                if (classInfo.getAttrDefs() != null) {
                    for (CcCiAttrDef attrDef : classInfo.getAttrDefs()) {
                        attrMap.put(attrDef.getId(), attrDef.getProStdName());
                    }
                }
                classInfoMap.put(classInfo.getCiClass().getId(), attrMap);
            }
        }
        Map<Long, Map<Long, String>> rltClassInfoMap = new HashMap<Long, Map<Long, String>>();
        CCcCiClass rltCdt = new CCcCiClass();
        rltCdt.setDomainId(user.getDomainId());
        List<CcCiClassInfo> rltClassInfoList = iRltClassSvc.getRltClassByCdt(rltCdt);
        if (rltClassInfoList != null) {
            for (CcCiClassInfo classInfo : rltClassInfoList) {
                Map<Long, String> attrMap = new HashMap<Long, String>();
                if (classInfo.getAttrDefs() != null) {
                    for (CcCiAttrDef attrDef : classInfo.getAttrDefs()) {
                        attrMap.put(attrDef.getId(), attrDef.getProStdName());
                    }
                }
                rltClassInfoMap.put(classInfo.getCiClass().getId(), attrMap);
            }
        }
        ESCiClassRltSearchBean esCiClassRltSearchBean = new ESCiClassRltSearchBean();
        esCiClassRltSearchBean.setDomainId(user.getDomainId());
        ClassRltQueryDto classRltQueryDto = new ClassRltQueryDto();
        classRltQueryDto.setDomainId(user.getDomainId());
        List<ESCiClassRlt> ciClassRltList = classRltSvc.queryClassRlt(classRltQueryDto);
        validRelationRule(dataSetMallApiRelationRule, classInfoMap, rltClassInfoMap, ciClassRltList);
        return dataSetMallApiRelationRule;
    }

    private void validRelationRule(DataSetMallApiRelationRule dataSetMallApiRelationRul, Map<Long, Map<Long, String>> classInfoMap, Map<Long, Map<Long, String>> rltClassInfoMap, List<ESCiClassRlt> ciClassRltList) {
        Map<Long, List<ESCiClassRlt>> rltClassIdMap = new HashMap<>();
        for (ESCiClassRlt ciClassRlt : ciClassRltList) {
            List<ESCiClassRlt> list = rltClassIdMap.computeIfAbsent(ciClassRlt.getClassId(), k -> new ArrayList<>());
            list.add(ciClassRlt);
        }
        if (dataSetMallApiRelationRul.getClassId() == null) {
            return;
//            throw MessageException.i18n("CMV_RULE_ILLEGAL");
        }
        if (!classInfoMap.containsKey(dataSetMallApiRelationRul.getClassId())) {
            //分类不存在
            throw MessageException.i18n("BS_MNAME_CLASS_NOT_EXSIT");
        }
        Map<Long, Long> nodeIdClassIdMap = new HashMap<Long, Long>();
        if (dataSetMallApiRelationRul.getNodes() != null && dataSetMallApiRelationRul.getNodes().size() > 0) {
            for (RelationRuleNode node : dataSetMallApiRelationRul.getNodes()) {
                nodeIdClassIdMap.put(node.getPageNodeId(), node.getClassId());
                if (classInfoMap.containsKey(node.getClassId())) {
                    Map<Long, String> attrMap = classInfoMap.get(node.getClassId());
                    JSONArray attrs = JSONArray.parseArray(node.getNodeReturns());
                    for (int i = 0; i < attrs.size(); i++) {
                        Long attrId = attrs.getJSONObject(i).getLong("id");
                        String stdName = attrs.getJSONObject(i).getString("proStdName");
                        if (attrMap.containsKey(attrId)) {
                            if (!stdName.equals(attrMap.get(attrId))) {
                                //属性不存在
                                throw MessageException.i18n("BS_MNAME_NOT_CI_ATTR_DEF");
                            }
                        } else {
                            //属性不存在
                            throw MessageException.i18n("BS_MNAME_NOT_CI_ATTR_DEF");
                        }
                    }
                    if (node.getCdts() != null) {
                        for (RelationRuleAttrCdt cdt : node.getCdts()) {
                            if (!attrMap.containsKey(cdt.getAttrId())) {
                                throw MessageException.i18n("BS_MNAME_NOT_CI_ATTR_DEF");
                            }
                        }
                    }
                } else {
                    //分类不存在
                    throw MessageException.i18n("BS_MNAME_CLASS_NOT_EXSIT");
                }
            }
        } else {
            //规则非法
            throw MessageException.i18n("CMV_RULE_ILLEGAL");
        }
        if (dataSetMallApiRelationRul.getLines() != null) {
            for (RelationRuleLine line : dataSetMallApiRelationRul.getLines()) {
                if (rltClassInfoMap.containsKey(line.getClassId()) && rltClassIdMap.containsKey(line.getClassId())) {
                    Map<Long, String> attrMap = rltClassInfoMap.get(line.getClassId());
                    if (line.getCdts() != null) {
                        for (RelationRuleAttrCdt cdt : line.getCdts()) {
                            if (!attrMap.containsKey(cdt.getAttrId())) {
                                throw MessageException.i18n("CMV_RULE_ILLEGAL");
                            }
                        }
                    }
                    Long sId = nodeIdClassIdMap.get(line.getNodeStartId());
                    Long tId = nodeIdClassIdMap.get(line.getNodeEndId());
                    if (!line.getDirection()) {
                        sId = nodeIdClassIdMap.get(line.getNodeEndId());
                        tId = nodeIdClassIdMap.get(line.getNodeStartId());
                    }
                    boolean legal = false;
                    for (ESCiClassRlt ciClassRlt : rltClassIdMap.get(line.getClassId())) {
                        if (ciClassRlt.getSourceClassId().equals(sId) && ciClassRlt.getTargetClassId().equals(tId)) {
                            legal = true;
                            break;
                        }
                    }
                    if (!legal) {
                        //关系规则已残缺：节点关系在可视化建模中被删除！
                        throw MessageException.i18n("CMV_MNAME_RLTRULE_CLASSRLTDELETED");
                    }
                } else {
                    //关系不存在
                    throw MessageException.i18n("CMV_RLT_NOT_EXISTS");
                }
            }
        }
    }


    @Override
    public JSONObject execute(DataSetMallApi dataSetMallApi, JSONObject jsonObject) {
        Long domainId = dataSetMallApi.getDomainId();
        long startTime = System.currentTimeMillis();

        //2021-08-18 增加apikey校验
        String authentication = jsonObject.getString("authentication");
        String oauthAppName = jsonObject.getString("oauthAppName");

        String username = jsonObject.getString("username");
        String password = jsonObject.getString("password");
        Integer pageNum = jsonObject.getInteger("pageNum");
        Integer pageSize = jsonObject.getInteger("pageSize");
        int ciTotal = 0;
        boolean isSuccess = true;
        try {
            //2021-08-18 增加apikey校验 记录调用系统名称
            if (!BinaryUtils.isEmpty(authentication)) {
                username = oauthAppName;
            } else {
                // Base64解密
                password = new String(Base64.decodeBase64(password.getBytes("utf-8")), "utf-8");
                password = DigestUtils.sha256Hex(password);
                //todo: 用户名密码直接查询判断，没有通过登陆
                CSysUser cSysUser = new CSysUser();
                cSysUser.setDomainId(domainId);
                cSysUser.setLoginCodeEqual(username);
                List<SysUser> listByCdt = userSvc.getSysUserByCdt(cSysUser);
                if (listByCdt == null || listByCdt.isEmpty()) {
                    throw new RuntimeException("用户名错误");
                } else if (!listByCdt.get(0).getLoginPasswd().equals(password)) {
                    throw new RuntimeException("密码错误");
                }

                // 如果数据集的权限是非公开的，需要校验权限
                if (OperateType.valueOf(dataSetMallApi.getShareLevel()).equals(OperateType.Invisible)) {
                    iDataSetSvc.checkOperate(listByCdt.get(0).getLoginCode(), dataSetMallApi);
                }
            }

            //DataSetMallApiRelationRule dataSetMallApi = (DataSetMallApiRelationRule) dataSet;
            JSONArray condition = new JSONArray();
            if (jsonObject.containsKey("condition")) {
                jsonObject.getJSONArray("condition");
            }
            DataSetExeResultSheetPage retPage = iDataSetSvc.queryDataSetResultBySheet(domainId, dataSetMallApi.getId(), null,
                    pageNum, pageSize, null, true, condition, null);

            CCcCiClass cdt = new CCcCiClass();
            cdt.setDomainId(domainId);
            List<CcCiClassInfo> classList = classSvc.queryClassByCdt(cdt);
            Map<String, String> classIdNameMap = new HashMap<>();
            for (CcCiClassInfo ccCiClass : classList) {
                classIdNameMap.put(String.valueOf(ccCiClass.getCiClass().getId()), ccCiClass.getCiClass().getClassName());
            }

            List<JSONObject> retList = new ArrayList<>();
            List<Map<String, Object>> dataList = retPage.getData();
            JSONObject dataObject = null;
            for (Map<String, Object> itemData : dataList) {
                dataObject = new JSONObject();
                Set<Map.Entry<String, Object>> entries = itemData.entrySet();
                // pathNode_classId_attrName 更换为 pathNode_className_attrName
                for (Map.Entry<String, Object> entry : entries) {
                    dataObject.put(entry.getKey().substring(entry.getKey().indexOf("_") + 1), entry.getValue());
                }
                retList.add(dataObject);
            }
            ciTotal = retList.size();

            JSONObject retJSON = new JSONObject();
            retJSON.put("totalCount", retPage.getTotalCount());
            retJSON.put("pageNum", retPage.getPageNum());
            retJSON.put("pageSize", retPage.getPageSize());
            retJSON.put("data", retList);
            return retJSON;
        } catch (RuntimeException re) {
            throw re;
        } catch (Exception e) {
            isSuccess = false;
            throw new RuntimeException(e);
        } finally {
            //保存日志
            DataSetMallApiLog dataSetMallApiLog = new DataSetMallApiLog();
            dataSetMallApiLog.setId(ESUtil.getUUID());
            dataSetMallApiLog.setDomainId(domainId);
            dataSetMallApiLog.setDataSetMallApiId(dataSetMallApi.getId());
            dataSetMallApiLog.setDataSetType(dataSetMallApi.getType());
            dataSetMallApiLog.setSuccess(isSuccess);
            dataSetMallApiLog.setRespUserCode(username);
            dataSetMallApiLog.setRespDuration(System.currentTimeMillis() - startTime);
            dataSetMallApiLog.setCreateTime(System.currentTimeMillis());
            dataSetMallApiLog.setCiTotal(ciTotal);
            dataSetMallApiLog.setRelTotal(0);
            esDataSetMallApiLogSvc.saveOrUpdate(dataSetMallApiLog.toJson(), true);
        }
    }

    @Override
    public JSONObject realTimeExecute(DataSetMallApi dataSetMallApi, JSONObject jsonObject) {
        long startTime = System.currentTimeMillis();
        Long domainId = dataSetMallApi.getDomainId();
        String username = jsonObject.getString("username");
        String password = jsonObject.getString("password");
        JSONArray enter = jsonObject.getJSONArray("enter");
        int ciTotal = 0;
        int rltTotal = 0;
        boolean isSuccess = true;
        try {
            // Base64解密
            password = new String(Base64.decodeBase64(password.getBytes("utf-8")), "utf-8");
            password = DigestUtils.sha256Hex(password);
            //todo: 用户名密码直接查询判断，没有通过登陆
            CSysUser cSysUser = new CSysUser();
            cSysUser.setDomainId(domainId);
            cSysUser.setLoginCodeEqual(username);
            List<SysUser> listByCdt = userSvc.getSysUserByCdt(cSysUser);
            if (listByCdt == null || listByCdt.isEmpty()) {
                throw new RuntimeException("用户名错误");
            } else if (!listByCdt.get(0).getLoginPasswd().equals(password)) {
                throw new RuntimeException("密码错误");
            }

            // 如果数据集的权限是非公开的，需要校验权限
            if (OperateType.valueOf(dataSetMallApi.getShareLevel()).equals(OperateType.Invisible)) {
                iDataSetSvc.checkOperate(listByCdt.get(0).getLoginCode(), dataSetMallApi);
            }

            List<List<String>> ciPrimaryKeys = new ArrayList<List<String>>();
            List<String> pk = new ArrayList<String>();
            for (int i = 0; i < enter.size(); i++) {
                pk.add(enter.getString(i));
            }
            ciPrimaryKeys.add(pk);
            //todo:
            List<CcCiInfo> ciInfos = iciSvc.getCIInfoListByCIPrimaryKeys(domainId, ciPrimaryKeys);
            if (ciInfos == null || ciInfos.size() == 0) {
                throw new RuntimeException("入口ci不存在");
            }
            CcCiInfo startCiInfo = new CcCiInfo();
            startCiInfo.setCi(ciInfos.get(0).getCi());
            startCiInfo.setAttrs(ciInfos.get(0).getAttrs());
            FriendInfo friendInfo = relationRuleAnalysisSvc.queryCiFriendByCiIdAndRule(startCiInfo, (DataSetMallApiRelationRule) dataSetMallApi);
            ciTotal = friendInfo.getCiNodes().size();
            rltTotal = friendInfo.getCiRltLines().size();
//            friendInfo.setCiIdByNodeMap(null);
//            friendInfo.setCiClassInfos(null);
            return JSONObject.parseObject(JSONObject.toJSONString(friendInfo));
        } catch (Exception e) {
            isSuccess = false;
            throw new RuntimeException(e.getMessage());
        } finally {
            //保存日志
            DataSetMallApiLog dataSetMallApiLog = new DataSetMallApiLog();
            dataSetMallApiLog.setId(ESUtil.getUUID());
            dataSetMallApiLog.setDomainId(domainId);
            dataSetMallApiLog.setDataSetMallApiId(dataSetMallApi.getId());
            dataSetMallApiLog.setDataSetType(dataSetMallApi.getType());
            dataSetMallApiLog.setSuccess(isSuccess);
            dataSetMallApiLog.setRespUserCode(username);
            dataSetMallApiLog.setRespDuration(System.currentTimeMillis() - startTime);
            dataSetMallApiLog.setCreateTime(System.currentTimeMillis());
            dataSetMallApiLog.setCiTotal(ciTotal);
            dataSetMallApiLog.setRelTotal(rltTotal);
            esDataSetMallApiLogSvc.saveOrUpdate(dataSetMallApiLog.toJson(), true);
        }
    }
}
