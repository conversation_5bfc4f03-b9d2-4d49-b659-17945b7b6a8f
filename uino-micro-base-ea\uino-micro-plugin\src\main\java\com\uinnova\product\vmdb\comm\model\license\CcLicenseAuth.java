package com.uinnova.product.vmdb.comm.model.license;

import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;

/**
 * 
 * <AUTHOR>
 *
 */
@Comment("受权许可表[CC_LICENSE_AUTH]")
public class CcLicenseAuth implements EntityBean {
    private static final long serialVersionUID = 1L;

    @Comment("ID[ID]")
    private Long id;

    @Comment("客户标识[CLIENT_CODE]")
    private String clientCode;

    @Comment("标识生成时间[CODE_TIME]")
    private Long codeTime;

    @Comment("标识生成人[CODE_USER]")
    private String codeUser;

    @Comment("受权码[AUTH_CODE]")
    private String authCode;

    @Comment("受权时间[AUTH_TIME]")
    private Long authTime;

    @Comment("累计使用时间[UT_TIME]    单位：分钟")
    private Long utTime;

    @Comment("注册受权人[AUTH_USER]")
    private String authUser;

    @Comment("受权截止日期[AUTH_END_DATE]")
    private Integer authEndDate;

    @Comment("受权备注1[AUTH_CUSTOM_1]    用户数")
    private Integer authCustom1;

    @Comment("受权备注2[AUTH_CUSTOM_2]    机柜数")
    private Integer authCustom2;

    @Comment("受权备注3[AUTH_CUSTOM_3]    开门数")
    private Integer authCustom3;

    @Comment("受权备注4[AUTH_CUSTOM_4]    场景数")
    private Integer authCustom4;

    @Comment("受权备注5[AUTH_CUSTOM_5]    视图数")
    private Integer authCustom5;

    @Comment("受权备注6[AUTH_CUSTOM_6]    使用时间")
    private Integer authCustom6;

    @Comment("受权备注7[AUTH_CUSTOM_7]    冻结数据 >=1:冻结 0=不冻结")
    private Integer authCustom7;

    @Comment("受权备注8[AUTH_CUSTOM_8]")
    private Integer authCustom8;

    @Comment("受权备注9[AUTH_CUSTOM_9]")
    private Integer authCustom9;

    @Comment("受权备注10[AUTH_CUSTOM_10]")
    private Integer authCustom10;

    @Comment("受权备注11[AUTH_CUSTOM_11]")
    private Integer authCustom11;

    @Comment("受权备注12[AUTH_CUSTOM_12]")
    private Integer authCustom12;

    @Comment("受权备注13[AUTH_CUSTOM_13]")
    private Integer authCustom13;

    @Comment("受权备注14[AUTH_CUSTOM_14]")
    private Integer authCustom14;

    @Comment("变更版本号[UP_VERSION]")
    private Integer upVersion;

    @Comment("创建时间[CREATE_TIME]    yyyyMMddHHmmss")
    private Long createTime;

    @Comment("修改时间[MODIFY_TIME]    yyyyMMddHHmmss")
    private Long modifyTime;

    @Comment("受权备注15[AUTH_CUSTOM_15]")
    private Integer authCustom15;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getClientCode() {
        return this.clientCode;
    }

    public void setClientCode(String clientCode) {
        this.clientCode = clientCode;
    }

    public Long getCodeTime() {
        return this.codeTime;
    }

    public void setCodeTime(Long codeTime) {
        this.codeTime = codeTime;
    }

    public String getCodeUser() {
        return this.codeUser;
    }

    public void setCodeUser(String codeUser) {
        this.codeUser = codeUser;
    }

    public String getAuthCode() {
        return this.authCode;
    }

    public void setAuthCode(String authCode) {
        this.authCode = authCode;
    }

    public Long getAuthTime() {
        return this.authTime;
    }

    public void setAuthTime(Long authTime) {
        this.authTime = authTime;
    }

    public Long getUtTime() {
        return this.utTime;
    }

    public void setUtTime(Long utTime) {
        this.utTime = utTime;
    }

    public String getAuthUser() {
        return this.authUser;
    }

    public void setAuthUser(String authUser) {
        this.authUser = authUser;
    }

    public Integer getAuthEndDate() {
        return this.authEndDate;
    }

    public void setAuthEndDate(Integer authEndDate) {
        this.authEndDate = authEndDate;
    }

    public Integer getAuthCustom1() {
        return this.authCustom1;
    }

    public void setAuthCustom1(Integer authCustom1) {
        this.authCustom1 = authCustom1;
    }

    public Integer getAuthCustom2() {
        return this.authCustom2;
    }

    public void setAuthCustom2(Integer authCustom2) {
        this.authCustom2 = authCustom2;
    }

    public Integer getAuthCustom3() {
        return this.authCustom3;
    }

    public void setAuthCustom3(Integer authCustom3) {
        this.authCustom3 = authCustom3;
    }

    public Integer getAuthCustom4() {
        return this.authCustom4;
    }

    public void setAuthCustom4(Integer authCustom4) {
        this.authCustom4 = authCustom4;
    }

    public Integer getAuthCustom5() {
        return this.authCustom5;
    }

    public void setAuthCustom5(Integer authCustom5) {
        this.authCustom5 = authCustom5;
    }

    public Integer getAuthCustom6() {
        return this.authCustom6;
    }

    public void setAuthCustom6(Integer authCustom6) {
        this.authCustom6 = authCustom6;
    }

    public Integer getAuthCustom7() {
        return this.authCustom7;
    }

    public void setAuthCustom7(Integer authCustom7) {
        this.authCustom7 = authCustom7;
    }

    public Integer getAuthCustom8() {
        return this.authCustom8;
    }

    public void setAuthCustom8(Integer authCustom8) {
        this.authCustom8 = authCustom8;
    }

    public Integer getAuthCustom9() {
        return this.authCustom9;
    }

    public void setAuthCustom9(Integer authCustom9) {
        this.authCustom9 = authCustom9;
    }

    public Integer getAuthCustom10() {
        return this.authCustom10;
    }

    public void setAuthCustom10(Integer authCustom10) {
        this.authCustom10 = authCustom10;
    }

    public Integer getAuthCustom11() {
        return this.authCustom11;
    }

    public void setAuthCustom11(Integer authCustom11) {
        this.authCustom11 = authCustom11;
    }

    public Integer getAuthCustom12() {
        return this.authCustom12;
    }

    public void setAuthCustom12(Integer authCustom12) {
        this.authCustom12 = authCustom12;
    }

    public Integer getAuthCustom13() {
        return this.authCustom13;
    }

    public void setAuthCustom13(Integer authCustom13) {
        this.authCustom13 = authCustom13;
    }

    public Integer getAuthCustom14() {
        return this.authCustom14;
    }

    public void setAuthCustom14(Integer authCustom14) {
        this.authCustom14 = authCustom14;
    }

    public Integer getUpVersion() {
        return this.upVersion;
    }

    public void setUpVersion(Integer upVersion) {
        this.upVersion = upVersion;
    }

    public Long getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getModifyTime() {
        return this.modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Integer getAuthCustom15() {
        return this.authCustom15;
    }

    public void setAuthCustom15(Integer authCustom15) {
        this.authCustom15 = authCustom15;
    }

}
