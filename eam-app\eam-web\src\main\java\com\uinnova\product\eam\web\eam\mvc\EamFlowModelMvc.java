package com.uinnova.product.eam.web.eam.mvc;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.binary.core.util.BinaryUtils;
import com.binary.framework.web.RemoteResult;
import com.uinnova.product.eam.base.diagram.model.ESDiagramDTO;
import com.uinnova.product.eam.base.exception.ServerException;
import com.uinnova.product.eam.base.model.BaseQueryDiagramDto;
import com.uinnova.product.eam.model.bm.EamDiagramUpdateInfo;
import com.uinnova.product.eam.model.enums.DataBaseType;
import com.uinnova.product.eam.service.merge.EamMergeSvc;
import com.uinnova.product.eam.service.bm.IEamFlowModelSvc;
import com.uinnova.product.eam.service.dm.DataModelSqlSvc;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uino.bean.cmdb.base.LibType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 流程模型(业务建模、数据建模)接口
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/flowModel")
public class EamFlowModelMvc {

    @Resource
    private IEamFlowModelSvc flowModelCatalogSvc;
    @Resource
    private DataModelSqlSvc dataModelSqlSvc;
    @Resource
    private EamMergeSvc mergeSvc;


    @PostMapping("/queryDiagramByIds")
    @ModDesc(desc = "通过视图id查询视图全量信息(兼容历史版本视图)", pDesc = "视图加密id", rDesc = "视图信息", rType = RemoteResult.class)
    public RemoteResult queryDiagramByIds(@RequestBody BaseQueryDiagramDto dto) {
        BinaryUtils.checkEmpty(dto.getDiagramIds(), "视图id");
        List<ESDiagramDTO> result = flowModelCatalogSvc.queryDiagramByIds(dto);
        if (BinaryUtils.isEmpty(result)) {
            return new RemoteResult(false, 404, "查询的视图，已删除或没有权限");
        }
        return new RemoteResult(result);
    }

    @PostMapping("/exportCreateTable")
    @ModDesc(desc = "导出创建表ddl", pDesc = "视图加密id", rDesc = "视图信息", rType = RemoteResult.class)
    public ResponseEntity<byte[]> exportCreateTable(@RequestBody JSONObject body) {
        String diagramId = body.getString("diagramId");
        LibType libType = body.getObject("libType", LibType.class);
        DataBaseType dataBase = body.getObject("dataBase", DataBaseType.class);
        BinaryUtils.checkEmpty(diagramId, "视图id");
        BinaryUtils.checkEmpty(libType, "libType");
        return dataModelSqlSvc.exportCreateTable(diagramId, libType, dataBase);
    }

    @PostMapping("/queryModelUpdateInfo")
    @ModDesc(desc = "查询模型下视图更新简要信息", pDesc = "目录集合", rDesc = "视图更新信息", rType = RemoteResult.class)
    public RemoteResult queryModelUpdateInfo(@RequestBody JSONObject body) {
        List<Long> dirIds = null;
        if(!BinaryUtils.isEmpty(body.getJSONArray("dirIds"))){
            dirIds = body.getJSONArray("dirIds").toJavaList(Long.class);
        }
        String diagramId = body.getString("diagramId");
        List<EamDiagramUpdateInfo> result = flowModelCatalogSvc.queryModelUpdateInfo(dirIds, diagramId);
        return new RemoteResult(result);
    }

    @PostMapping("/diagramPush")
    @ModDesc(desc = "业务建模目录/视图发布", pDesc = "目录id/视图id", rDesc = "发布结果", rType = RemoteResult.class)
    public RemoteResult diagramPush(@RequestBody JSONObject body) {
        List<Long> dirIds = null;
        JSONArray dirIdJson = body.getJSONArray("dirIds");
        if(!BinaryUtils.isEmpty(dirIdJson)){
            dirIds = dirIdJson.toJavaList(Long.class);
        }
        String diagramId = body.getString("diagramId");
        if(BinaryUtils.isEmpty(dirIds) && BinaryUtils.isEmpty(diagramId)){
            throw new ServerException("参数不可为空!");
        }
        Long parentId = body.getLong("parentId");
        if (BinaryUtils.isEmpty(parentId)) {
            throw new ServerException("发布位置参数不能为空!");
        }
        String desc = body.getString("desc");
        mergeSvc.modelPush(dirIds, diagramId, desc, parentId);
        return new RemoteResult(1);
    }

    @PostMapping("/diagramPull")
    @ModDesc(desc = "业务建模目录/视图检出", pDesc = "目录id/视图id", rDesc = "检出结果", rType = RemoteResult.class)
    public RemoteResult diagramPull(@RequestBody JSONObject body) {
        Long dirId = body.getLong("dirId");
        String diagramId = body.getString("diagramId");
        Long targetDirId = body.getLong("targetDirId");
        if (BinaryUtils.isEmpty(targetDirId)) {
            throw new ServerException("检出位置不能为空!");
        }
        if(BinaryUtils.isEmpty(dirId) && BinaryUtils.isEmpty(diagramId)){
            throw new ServerException("参数不可为空!");
        }
        LibType libType = body.getObject("libType", LibType.class);
        mergeSvc.modelPull(dirId, diagramId, targetDirId, libType);
        return new RemoteResult(1);
    }
}
