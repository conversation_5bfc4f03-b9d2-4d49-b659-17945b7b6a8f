package com.uinnova.product.eam.service.es;

import com.uinnova.product.vmdb.comm.model.ci.CCcCi;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;

@Service
public class IamsCIDesignNonComplianceDao extends AbstractESBaseDao<ESCIInfo, CCcCi> {
    @Override
    public String getIndex() {
        return "uino_cmdb_no_compliance_design_ci";
    }

    @Override
    public String getType() {
        return this.getIndex();
    }
    @PostConstruct
    public void init() {
        super.initIndex();
    }
}
