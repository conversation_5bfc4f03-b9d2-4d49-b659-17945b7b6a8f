package com.uino.api.client.sys.rpc;

import com.uino.dao.BaseConst;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.binary.jdbc.Page;
import com.uino.bean.sys.base.ESOperateLog;
import com.uino.bean.sys.query.ESOperateLogSearchBean;
import com.uino.provider.feign.sys.OperateLogFeign;
import com.uino.api.client.sys.IOperateLogApiSvc;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
public class OperateLogApiSvcRpc implements IOperateLogApiSvc {

    @Autowired
    private OperateLogFeign opLogFeign;
    @Override
    public Page<ESOperateLog> getOperateLogPageByCdt(ESOperateLogSearchBean bean) {
        return opLogFeign.getOperateLogPageByCdt(bean);
    }

    @Override
    public Long saveOrUpdate(ESOperateLog log) {
        return opLogFeign.saveOrUpdate(log);
    }

    @Override
    public Integer clearOperateLogByDuration(Integer clearLogDuration) {
        return opLogFeign.clearOperateLogByDuration(clearLogDuration);
    }



}
