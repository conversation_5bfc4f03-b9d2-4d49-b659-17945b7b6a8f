package com.uino.bean.permission.base;

import java.io.Serializable;
import java.util.Map;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.StringUtils;

import lombok.Builder;
import lombok.experimental.Tolerate;

/**
 * mapping-table: 组织表[SYS_ORG]
 * 
 * <AUTHOR>
 */
@Builder
@ApiModel(value="组织表",description = "组织表")
public class SysOrg implements Serializable {
	private static final long serialVersionUID = 1L;

	@Tolerate
	public SysOrg() {

	}

	/** ID */
	@ApiModelProperty(value="id",example = "123")
	private Long id;

	/** 组织代码 */
	@ApiModelProperty(value="组织代码")
	private String orgCode;

	/** 组织名称 */
	@ApiModelProperty(value="组织名称",example = "olympic")
	private String orgName;

	/**
	 * 组织带全层级路径的名称
	 */
	@ApiModelProperty(value="组织带全层级路径的名称")
	private String orgAllLevelName;

	/** 组织级别 */
	@ApiModelProperty(value="组织级别",example = "1")
	private Integer orgLevel;

	/** 父节点ID */
	@ApiModelProperty(value="父节点ID",example = "23")
	private Long parentOrgId;

	/** 组织层级路径 */
	@ApiModelProperty(value="组织层级路径")
	private String orgLvlPath;

	/** 显示排序 */
	@ApiModelProperty(value="显示排序",example = "1")
	private Integer orderNo;

	/** 是否末级 */
	@ApiModelProperty(value="是否末级",example = "1")
	private Integer isLeaf;

	/** 组织图标 */
	@ApiModelProperty(value="组织图标")
	private String icon;

	/** 备注 */
	@ApiModelProperty(value="备注")
	private String remark;

	/** 所属域 */
	@ApiModelProperty(value="所属域id",example = "123")
	private Long domainId;

	/** 创建人 */
	@ApiModelProperty(value="创建人",example = "mike")
	private String creator;

	/** 修改人 */
	@ApiModelProperty(value="修改人",example = "mike")
	private String modifier;

	/** 创建时间 */
	@ApiModelProperty(value="创建时间")
	private Long createTime;

	/** 修改时间 */
	@ApiModelProperty(value="修改时间")
	private Long modifyTime;

	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getOrgCode() {
		return this.orgCode;
	}

	public void setOrgCode(String orgCode) {
		this.orgCode = orgCode;
	}

	public String getOrgName() {
		return this.orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	public Integer getOrgLevel() {
		return this.orgLevel;
	}

	public void setOrgLevel(Integer orgLevel) {
		this.orgLevel = orgLevel;
	}

	public Long getParentOrgId() {
		return this.parentOrgId;
	}

	public void setParentOrgId(Long parentOrgId) {
		this.parentOrgId = parentOrgId;
	}

	public String getOrgLvlPath() {
		return this.orgLvlPath;
	}

	public void setOrgLvlPath(String orgLvlPath) {
		this.orgLvlPath = orgLvlPath;
	}

	public Integer getOrderNo() {
		return this.orderNo;
	}

	public void setOrderNo(Integer orderNo) {
		this.orderNo = orderNo;
	}

	public Integer getIsLeaf() {
		return this.isLeaf;
	}

	public void setIsLeaf(Integer isLeaf) {
		this.isLeaf = isLeaf;
	}

	public String getIcon() {
		return this.icon;
	}

	public void setIcon(String icon) {
		this.icon = icon;
	}

	public String getRemark() {
		return this.remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public Long getDomainId() {
		return this.domainId;
	}

	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}

	public String getCreator() {
		return this.creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public String getModifier() {
		return this.modifier;
	}

	public void setModifier(String modifier) {
		this.modifier = modifier;
	}

	public Long getCreateTime() {
		return this.createTime;
	}

	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}

	public Long getModifyTime() {
		return this.modifyTime;
	}

	public void setModifyTime(Long modifyTime) {
		this.modifyTime = modifyTime;
	}

	/**
	 * 获取组织全路径名称
	 * 
	 * @param orgId
	 * @param orgMap
	 * @return
	 */
	public static String getAllName(Long orgId, Map<Long, SysOrg> orgMap) {
		String orgAllName = "";
		String allDescName = getAllDescName(orgId, orgMap, null);
		String[] descNames = allDescName.split("/");
		for (int index = descNames.length - 1; index >= 0; index--) {
			orgAllName += "/" + descNames[index];
		}
		return orgAllName.substring(1);
	}

	private static String getAllDescName(Long orgId, Map<Long, SysOrg> orgMap, String name) {
		SysOrg currentOrg = orgMap.get(orgId);
		Long domainId = currentOrg.getDomainId();
		if (StringUtils.isBlank(name)){
			name = currentOrg.getOrgName();
		}else {
            name += "/" + currentOrg.getOrgName();
        }
		if (currentOrg.getParentOrgId() != null && currentOrg.getId().longValue() != domainId) {
			return getAllDescName(currentOrg.getParentOrgId(), orgMap, name);
		} else {
			return name;
		}
	}

	public String getOrgAllLevelName() {
		return orgAllLevelName;
	}

	public void setOrgAllLevelName(String orgAllLevelName) {
		this.orgAllLevelName = orgAllLevelName;
	}

}
