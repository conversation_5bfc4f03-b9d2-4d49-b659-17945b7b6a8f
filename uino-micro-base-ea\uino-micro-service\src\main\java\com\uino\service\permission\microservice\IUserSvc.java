package com.uino.service.permission.microservice;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import com.uino.bean.permission.base.SysModuleOutSide;
import com.uino.bean.permission.query.CAuthDataModuleBean;
import org.elasticsearch.index.query.QueryBuilder;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import com.binary.jdbc.Page;
import com.uino.bean.cmdb.business.ImportResultMessage;
import com.uino.bean.cmdb.business.ImportSheetMessage;
import com.uino.bean.permission.base.SysRoleDataModuleRlt;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.permission.business.CurrentUserInfo;
import com.uino.bean.permission.business.ModuleNodeInfo;
import com.uino.bean.permission.business.SysUserPwdResetParam;
import com.uino.bean.permission.business.UserInfo;
import com.uino.bean.permission.business.request.SaveUserRoleRltRequestDto;
import com.uino.bean.permission.query.CSysUser;
import com.uino.bean.permission.query.UserSearchBeanExtend;

/**
 * 用户管理 结构处理完毕后删除（有些方法可能需要改造下）
 *
 * <AUTHOR>
 */
public interface IUserSvc {

    /**
     * 保存用户
     *
     * @param userInfo
     * @return 0表示失败，其它表示成功
     */
    public Long saveOrUpdate(UserInfo userInfo);

    /**
     * 获取系统所有用户（同域）
     *
     * @return
     */
    public List<SysUser> getAllUser();

    /**
     * 批量保存用户信息
     *
     * @param userInfos
     * @return
     */
    public ImportSheetMessage saveOrUpdateUserInfoBatch(Long domainId, boolean isUpdatePassword, List<UserInfo> userInfos);

    /**
     * 批量保存用户-提供给dix使用
     *
     * @param userInfos
     * @return
     */
    public ImportSheetMessage syncUserBatch(Long domainId,List<UserInfo> userInfos);

    /**
     * 获取用户
     *
     * @param userId
     * @return
     */
    public UserInfo getUserInfoById(Long userId);

    /**
     * 获取用户
     *
     * @param loginCode
     * @return
     */
    UserInfo getUserInfoByLoginCode(String loginCode);

    /**
     * 获取用户(登录接口用，因为含有密码)
     *
     * @param loginCode
     * @return
     */
    SysUser getUserByLoginCode(String loginCode);

    /**
     * 通过条件获取SysUser
     *
     * @param cdt
     * @return
     */
    public List<SysUser> getSysUserByCdt(CSysUser cdt);

    /**
     * 条件查询用户信息-包含角色
     *
     * @param cdt
     * @param rmSensitiveData
     *            是否清除用户敏感数据
     * @return
     */
    public List<UserInfo> getUserInfoByCdt(CSysUser cdt, boolean rmSensitiveData);

    /**
     * 获取角色下的用户
     *
     * @param roleId
     * @return
     */
    public List<SysUser> getUserByRoleId(Long roleId);

    /**
     * 全文检索--分页
     *
     * @param bean
     * @return
     */
    public Page<UserInfo> getListByQuery(UserSearchBeanExtend bean);

    /**
     * 用户更新个人密码
     *
     * @param pwdParam
     * @return
     */
    public Long resetPwd(Long domainId,SysUserPwdResetParam pwdParam);

    /**
     * 管理员更新密码
     *
     * @param pwdParam
     * @return
     */
    public Long resetPwdByAdmin(SysUserPwdResetParam pwdParam);

    /**
     * 更新当前用户信息
     *
     * @param op
     * @param file
     * @return
     */
    public Long updateCurUser(SysUser op, MultipartFile file);

    /**
     * 导入用户
     *
     * @param file
     * @return
     */
    public ImportResultMessage importUserByExcel(Long domainId,MultipartFile file);

    /**
     * 导出用户到Excel文件
     *
     * @param isTpl
     * @param cdt
     * @return
     */
    public ResponseEntity<byte[]> exportUserToExcel(Boolean isTpl, CSysUser cdt);

    /**
     * 根据id删除用户及其关联数据
     *
     * @param userId
     * @return
     */
    public Integer deleteById(Long userId);

    /**
     * 根据登录代码LoginCode批量删除用户-dix使用
     *
     * @param loginCodes
     * @return
     */
    public Integer deleteSysUserByLoginCodeBatch(Long domainId,List<String> loginCodes);

    /**
     * 绑定用户角色关系-全量
     *
     * @param bean
     * @return
     */
    public Integer saveUserRoleRlt(SaveUserRoleRltRequestDto bean);

    /**
     * 获取菜单tree结构
     *
     * @param userId
     *            为空则获取所有tree；不为空则获取该用户有权限的菜单tree
     * @return
     */
    public ModuleNodeInfo getModuleTree(Long domainId,Long userId);

    /**
     * 根据权限标识获取下级菜单树结构
     *
     * @param userId 为空则获取所有tree；不为空则获取该用户有权限的菜单tree
     * @return
     */
    ModuleNodeInfo getModuleTreeBySign(Long domainId, Long userId, String moduleSign);

    /**
     * 获取指定用户拥有的数据权限字典
     *
     * @param userId
     * @param moduleCodes
     * @return
     */
    public Map<String, List<SysRoleDataModuleRlt>> getDataModule(Long userId, List<String> moduleCodes);

    /**
     * 收藏菜单
     *
     * @param userId
     * @param moduleId
     * @return
     */
    public Long enshrineModule(Long domainId,Long userId, Long moduleId);

    /**
     * 取消菜单收藏
     *
     * @param userId
     * @param moduleId
     * @return
     */
    public Integer unenshrineModule(Long domainId,Long userId, Long moduleId);

    /**
     * 统计符合条件的用户数量
     *
     * @param query
     * @return
     */
    long countByCondition(Long domainId,QueryBuilder query);

    /**
     * 记录用户登陆失败次数
     *
     * @param loginName
     * @param success
     * @return
     */
    public int recordLoginFailNum(Long domainId,String loginName, boolean success);

    /**
     * 获取登陆失败次数锁定
     *
     * @return
     */
    public int getLoginFailLockNum();

    /**
     * 获取用户锁定时长
     *
     * @return
     */
    public long getUserLockDuration();

    /**
     * 获取当前登陆用户信息
     *
     * @return
     */
    public CurrentUserInfo getCurrentUser();

    /**
     * 根据锁定时长解锁用户
     *
     * @param durationSecond
     * @return 解锁的用户数量
     */
    public int unLockUsersByDuration(Long durationSecond);

	/**
	 * 校验用户权限
	 */
	public void verifyAuth();

    /**
     * 根据登录名查询用户权限
     *
     * @param loginCode login code
     * @return {@link List<SysModuleOutSide>}
     * */
    List<SysModuleOutSide> getModulesByLoginCode(Long domainId,String loginCode);

    /**
     * Query the data module permissions corresponding to the user
     *
     * @param cAuthDataModuleBean cAuthDataModuleBean
     * @return {@link  Map<String, Integer>}
     * */
    Map<Long, Map<String, Integer>> getDataPermissionByUser(CAuthDataModuleBean cAuthDataModuleBean);

    List<SysUser> getUserByRoleName(String roleName);

    /**
     * 根据用户名或登录名模糊查询用户数据
     * @param userName
     * @return
     */
    List<SysUser> getUserInfoByName(String userName);

    /**
     *
     * @param loginCode
     * @return
     */
    void initUserCiData(String loginCode);

    List<SysUser> getUserByRoleNameAndCount(String roleName, Integer count);

    /**
     * 模糊搜索用户，分页展示
     * @param pageNum
     * @param pageSize
     * @param userName
     * @return
     */
    Page<SysUser> findUserInfoByNameForPage(Integer pageNum, Integer pageSize, String userName);

    /**
     * 根据用户code集合获取用户名称集合
     * @param loginCodes 登录名
     * @return 登录名-用户名
     */
    Map<String, String> getNameByCodes(Collection<String> loginCodes);

    /**
     *  根据角色名称获取人员信息分页查 默认每页100条
     * @param roleName
     * @param pageNum
     * @param pageSize
     * @return
     */
    Page<SysUser> queryPageByRoleName(String roleName, String loginName, int pageNum, int pageSize);
}
