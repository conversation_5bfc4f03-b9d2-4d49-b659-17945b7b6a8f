package com.uino.web.cmdb.mvc;

import java.util.List;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import com.uino.bean.permission.base.SysUser;
import com.uino.init.api.ApiResult;
import com.uino.init.api.ApiVersion;
import com.uino.util.sys.SysUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uinnova.product.vmdb.comm.doc.annotation.MvcDesc;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uino.bean.cmdb.base.ESCITagInfo;
import com.uino.bean.cmdb.business.ClassNodeInfo;
import com.uino.bean.cmdb.query.ESAttrAggBean;
import com.uino.bean.cmdb.query.ESTagSearchBean;
import com.uino.api.client.cmdb.ITagApiSvc;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@ApiVersion(1)
@Api(value = "标签", tags = {"标签管理"})
@RestController
@RequestMapping("/cmdb/tag")
@MvcDesc(author = "zmj", desc = "标签管理")
public class TagMvc {

    @Autowired
    private ITagApiSvc tagSvc;

    /**
     * 获取标签树
     * 
     * @param request
     * @param response
     */
    @ApiOperation("获取标签树结构")
    @RequestMapping(value="/getTagTree",method = RequestMethod.POST)
    @ModDesc(desc = "获取标签树结构", pDesc = "无", rDesc = "标签树列表", rType = List.class, rcType = ClassNodeInfo.class)
    public ApiResult<List<ClassNodeInfo>> getTagTree(HttpServletRequest request, HttpServletResponse response) {
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        return ApiResult.ok(this).data(tagSvc.getTagTree(currentUserInfo.getDomainId()));
    }

    /**
     * 
     * 保存标签
     * 
     * @param tagInfo
     */
    @ApiOperation("保存标签定义")
    @RequestMapping(value="/saveOrUpdate",method = RequestMethod.POST)
    @ModDesc(desc = "保存标签定义", pDesc = "标签定义", pType = ESCITagInfo.class, rDesc = "标签id", rType = Long.class)
    public ApiResult<Long> saveOrUpdate(HttpServletRequest request, HttpServletResponse response, @RequestBody ESCITagInfo tagInfo) {
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        tagInfo.setDomainId(currentUserInfo.getDomainId());
        return ApiResult.ok(this).data(tagSvc.saveOrUpdateCITagRule(tagInfo));
    }

    /**
     * 获取标签详情
     */
    @ApiOperation(value = "根据id查询标签定义详情" )
    @RequestMapping(value="/getTagRuleById",method = RequestMethod.POST)
    @ModDesc(desc = "跟据id查询标签定义详情", pDesc = "标签id", pType = Long.class, rDesc = "标签信息", rType = ESCITagInfo.class)
    public ApiResult<ESCITagInfo> getTagRuleById(HttpServletRequest request, HttpServletResponse response, @RequestBody Long tagId) {
        return ApiResult.ok(this).data(tagSvc.getCITagRuleById(tagId));
    }

    @ApiOperation("根据标签定义预览数据")
    @RequestMapping(value="/getCIInfoListByTag",method = RequestMethod.POST)
    @ModDesc(desc = "根据标签定义预览数据", pDesc = "标签数据查询对象", pType = ESTagSearchBean.class, rDesc = "标签数据分页查询结果", rType = Page.class, rcType = CcCiInfo.class)
    public ApiResult<Page<CcCiInfo>> getCIInfoListByTag(HttpServletRequest request, HttpServletResponse response, @RequestBody ESTagSearchBean bean) {
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        bean.getTagInfo().setDomainId(currentUserInfo.getDomainId());
        return ApiResult.ok(this).data(tagSvc.getCIInfoListByTag(bean));
    }

    @ApiOperation("根据id删除标签")
    @RequestMapping(value="/deleteById",method=RequestMethod.POST)
    @ModDesc(desc = "跟据id删除标签", pDesc = "标签id", pType = Long.class, rDesc = "操作是否成功,1=成功,0=失败", rType = Integer.class)
    public ApiResult<Integer> deleteById(HttpServletRequest request, HttpServletResponse response, @RequestBody Long tagId) {
        return ApiResult.ok(this).data(tagSvc.deleteById(tagId));
    }

    @ApiOperation("获取对应属性值列表")
    @RequestMapping(value="/getAttrValuesBySearchBean",method = RequestMethod.POST)
    @ModDesc(desc = "获取对应属性值列表", pDesc = "属性条件查询对象", pType = ESAttrAggBean.class, rDesc = "属性值分页查询结果", rType = Page.class, rcType = String.class)
    public ApiResult<Page<String>> getAttrValuesBySearchBean(HttpServletRequest request, HttpServletResponse response, @RequestBody ESAttrAggBean bean) {
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        return  ApiResult.ok(this).data(tagSvc.getAttrValuesBySearchBean(currentUserInfo.getDomainId(), bean));
    }
    @ApiOperation("更改标签所属领域")
    @RequestMapping(value="/changeTagDir",method = RequestMethod.POST)
    @ModDesc(desc = "更改标签所属领域", pDesc = "标签数据传输对象", pType = ESCITagInfo.class, rDesc = "操作是否成功", rType = Boolean.class)
    public ApiResult<Boolean> changeTagDir(HttpServletRequest request, HttpServletResponse response, @RequestBody ESCITagInfo tagInfo) {
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        tagInfo.setDomainId(currentUserInfo.getDomainId());
        return ApiResult.ok(this).data(tagSvc.changeTagDir(tagInfo));
    }
}
