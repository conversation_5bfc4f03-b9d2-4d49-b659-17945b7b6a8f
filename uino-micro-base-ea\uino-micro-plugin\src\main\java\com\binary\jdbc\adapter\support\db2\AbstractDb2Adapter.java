package com.binary.jdbc.adapter.support.db2;

import java.math.BigDecimal;
import java.sql.Blob;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Time;
import java.sql.Timestamp;
import java.util.HashMap;
import java.util.Map;

import com.binary.jdbc.adapter.FieldMapping;
import com.binary.jdbc.adapter.support.AbstractJdbcAdapter;
import com.binary.jdbc.exception.JdbcException;

public abstract class AbstractDb2Adapter  extends AbstractJdbcAdapter {
	
	
	protected Map<String,FieldMapping> newFieldMappings() {
		Map<String,FieldMapping> mapping = new HashMap<String,FieldMapping>();
		mapping.put("BIGINT", new FieldMapping("BIGINT", Long.class, "getLong"));
		mapping.put("BINARY_LARGE_OBJECT", new FieldMapping("BINARY_LARGE_OBJECT", Blob.class, "getBlob"));
		mapping.put("BLOB", new FieldMapping("BLOB", Blob.class, "getBlob"));
		mapping.put("CHAR", new FieldMapping("CHAR", String.class, "getString"));
		mapping.put("CHAR_LARGE_OBJECT", new FieldMapping("CHAR_LARGE_OBJECT", String.class, "getString"));
		mapping.put("CHAR_VARYING", new FieldMapping("CHAR_VARYING", String.class, "getString"));
		mapping.put("CHARACTER", new FieldMapping("CHARACTER", String.class, "getString"));
		mapping.put("CHARACTER_LARGE_OBJECT", new FieldMapping("CHARACTER_LARGE_OBJECT", String.class, "getString"));
		mapping.put("CHARACTER_VARYING", new FieldMapping("CHARACTER_VARYING", String.class, "getString"));
		mapping.put("CLOB", new FieldMapping("CLOB", String.class, "getString"));
		mapping.put("DATE", new FieldMapping("DATE", java.sql.Date.class, "getDate"));
		mapping.put("DBCLOB", new FieldMapping("DBCLOB", String.class, "getString"));
		mapping.put("DEC", new FieldMapping("DEC", BigDecimal.class, "getBigDecimal"));
		mapping.put("DECFLOAT", new FieldMapping("DECFLOAT", BigDecimal.class, "getBigDecimal"));
		mapping.put("DECIMAL", new FieldMapping("DECIMAL", BigDecimal.class, "getBigDecimal"));
		mapping.put("DOUBLE", new FieldMapping("DOUBLE", Double.class, "getDouble"));
		mapping.put("DOUBLE_PRECISION", new FieldMapping("DOUBLE_PRECISION", Double.class, "getDouble"));
		mapping.put("FLOAT", new FieldMapping("FLOAT", Double.class, "getDouble"));
		mapping.put("GRAPHIC", new FieldMapping("GRAPHIC", String.class, "getString"));
		mapping.put("INT", new FieldMapping("INT", Integer.class, "getInt"));
		mapping.put("INTEGER", new FieldMapping("INTEGER", Integer.class, "getInt"));
		mapping.put("LONG_VARCHAR", new FieldMapping("LONG_VARCHAR", String.class, "getString"));
		mapping.put("LONG_VARGRAPHIC", new FieldMapping("LONG_VARGRAPHIC", String.class, "getString"));
		mapping.put("NUMERIC", new FieldMapping("NUMERIC", BigDecimal.class, "getBigDecimal"));
		mapping.put("REAL", new FieldMapping("REAL", Float.class, "getFloat"));
		mapping.put("SMALLINT", new FieldMapping("SMALLINT", Integer.class, "getInt"));
		mapping.put("TIME", new FieldMapping("TIME", Time.class, "getTime"));
		mapping.put("TIMESTAMP", new FieldMapping("TIMESTAMP", Timestamp.class, "getTimestamp"));
		mapping.put("VARCHAR", new FieldMapping("VARCHAR", String.class, "getString"));
		mapping.put("VARGRAPHIC", new FieldMapping("VARGRAPHIC", String.class, "getString"));
		mapping.put("XML", new FieldMapping("XML", String.class, "getString"));
		
		return mapping;
	}
	
	
	
	@Override
	public String getSchema(Connection conn, String userName) {
		if(userName != null) userName = userName.toUpperCase(); 
		return userName;
	}
	
	
	@Override
	public PreparedStatement prepareUpdateStatement(Connection conn, String sql) {
		try {
			return conn.prepareStatement(sql, ResultSet.TYPE_SCROLL_INSENSITIVE, ResultSet.CONCUR_UPDATABLE);
		} catch (SQLException e) {
			throw new JdbcException(e);
		}
	}
	
	
	
	

}
