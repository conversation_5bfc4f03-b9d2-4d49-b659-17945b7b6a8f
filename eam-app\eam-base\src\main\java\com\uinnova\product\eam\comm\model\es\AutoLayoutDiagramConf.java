package com.uinnova.product.eam.comm.model.es;

import com.alibaba.fastjson.JSONObject;
import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.util.List;

/**
 * 自由绘图自动成图配置
 */
@Data
public class AutoLayoutDiagramConf {


    @Comment("Id")
    private Long id;

    @Comment("视图Id")
    private String diagramId;

    @Comment("分页ID")
    private String sheetId;

    @Comment("元素分类")
    private String classCode;
    @Comment("中心元素")
    private String ciCode;
    @Comment("关联关系")
    private List<Long> rltClassIds;
    @Comment("关联分类")
    private List<String> classCodes;
    @Comment("布局")
    private JSONObject layout;

//    @Comment("布局方向")
//    private Integer layoutDirection;
//
//    @Comment("横向间距")
//    private Integer aspect;
//
//    @Comment("纵向间距")
//    private Integer verticalSpacing;

    @Comment("领域id")
    private Long domainId;
    @Comment("创建人")
    private String creator;
    @Comment("修改人")
    private String modifier;
    @Comment("创建时间")
    private Long createTime;
    @Comment("修改时间")
    private Long modifyTime;

}
