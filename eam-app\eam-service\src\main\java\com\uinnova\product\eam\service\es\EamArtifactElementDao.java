package com.uinnova.product.eam.service.es;

import com.uinnova.product.eam.comm.model.es.CEamArtifactElement;
import com.uinnova.product.eam.comm.model.es.EamArtifactElement;
import com.uino.dao.AbstractESBaseDao;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;

@Service
public class EamArtifactElementDao extends AbstractESBaseDao<EamArtifactElement, CEamArtifactElement> {

    @Override
    public String getIndex() {
        return "uino_eam_artifact_element";
    }

    @Override
    public String getType() {
        return getIndex();
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }


}
