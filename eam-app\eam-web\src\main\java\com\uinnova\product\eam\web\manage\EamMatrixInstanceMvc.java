package com.uinnova.product.eam.web.manage;

import com.binary.framework.web.RemoteResult;
import com.binary.jdbc.Page;
import com.uinnova.product.eam.comm.dto.EamMatrixQueryVO;
import com.uinnova.product.eam.comm.dto.EamMatrixTableQuery;
import com.uinnova.product.eam.comm.dto.EamMatrixTableSaveVO;
import com.uinnova.product.eam.comm.model.es.EamMatrixInstance;
import com.uinnova.product.eam.model.cj.dto.RenameRequestDTO;
import com.uinnova.product.eam.model.vo.ESAttrAggScreenBean;
import com.uinnova.product.eam.service.manage.EamMatrixInstanceSvc;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uino.bean.cmdb.base.ESCIInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 矩阵表格
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/matrix/instance")
public class EamMatrixInstanceMvc {

    @Autowired
    private EamMatrixInstanceSvc instanceSvc;
    @PostMapping("/create")
    @ModDesc(desc = "创建", pDesc = "矩阵制品id,文件夹id", rDesc = "表格id", rType = RemoteResult.class)
    public RemoteResult create(@RequestBody EamMatrixInstance vo) {
        Long id = instanceSvc.create(vo.getMatrixId(), vo.getDirId());
        return new RemoteResult(id);
    }

    @PostMapping("/table/saveOrUpdate")
    @ModDesc(desc = "表格数据保存或更新", pDesc = "矩阵表格信息", rDesc = "表格id", rType = RemoteResult.class)
    public RemoteResult saveOrUpdateTable(@RequestBody EamMatrixTableSaveVO vo) {
        Long id = instanceSvc.saveOrUpdateTable(vo);
        return new RemoteResult(id);
    }

    @PostMapping("/table")
    @ModDesc(desc = "查询表格数据", pDesc = "id", rDesc = "表格信息", rType = RemoteResult.class)
    public RemoteResult getTableInfo(@RequestBody EamMatrixQueryVO queryVO) {
        EamMatrixTableSaveVO result = instanceSvc.getTableInfo(queryVO);
        return new RemoteResult(result);
    }

    @PostMapping("/rename")
    @ModDesc(desc = "重命名", pDesc = "重命名参数", rDesc = "重命名结果", rType = RemoteResult.class)
    public RemoteResult renameMatrix(@Valid @RequestBody RenameRequestDTO renameRequestDTO){
        return new RemoteResult(instanceSvc.renameMatrix(renameRequestDTO));
    }

    @PostMapping("/table/ci")
    @ModDesc(desc = "查询表格数据", pDesc = "字段定义id,模糊匹配name,分类id", rDesc = "表格信息", rType = RemoteResult.class)
    public RemoteResult getCiByClass(@RequestBody ESAttrAggScreenBean query) {
        Page<ESCIInfo> result = instanceSvc.getCiByClass(query);
        return new RemoteResult(result);
    }

    @PostMapping("/table/rlt")
    @ModDesc(desc = "通过源端目标端查询关系", pDesc = "ciCode", rDesc = "关系属性数据", rType = RemoteResult.class)
    public RemoteResult getRltByCode(@RequestBody EamMatrixTableQuery query) {
        Map<String, String> result = instanceSvc.getRltByCode(query);
        return new RemoteResult(result);
    }

    @GetMapping("/table/history")
    @ModDesc(desc = "查询表格历史版本", pDesc = "矩阵id", rDesc = "表格信息", rType = RemoteResult.class)
    public RemoteResult getTableHistory(@RequestParam Long id) {
        List<EamMatrixInstance> result = instanceSvc.getTableHistory(id);
        return new RemoteResult(result);
    }

    @PostMapping("/table/data")
    @ModDesc(desc = "查询行数据对象及关系", pDesc = "", rDesc = "", rType = RemoteResult.class)
    public RemoteResult getTableData(@RequestBody EamMatrixTableQuery query) {
        Object result = instanceSvc.getTableData(query);
        return new RemoteResult(result);
    }

}
