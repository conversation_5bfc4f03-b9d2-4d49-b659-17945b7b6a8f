package com.uinnova.product.eam.service.es;

import com.uinnova.product.eam.comm.model.CVcBaseConfig;
import com.uinnova.product.eam.comm.model.VcBaseConfig;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;

@Component
public class BaseConfigDao extends AbstractESBaseDao<VcBaseConfig, CVcBaseConfig> {
    @Override
    public String getIndex() {
        return "uino_base_config";
    }

    @Override
    public String getType() {
        return "_doc";
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }
}
