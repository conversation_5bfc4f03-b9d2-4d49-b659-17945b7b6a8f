package com.uinnova.product.eam.web.diagram.bean;

import com.binary.core.io.Resource;
import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.vmdb.comm.model.excel.CcExcel;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.permission.query.CSysUser;

public class FileInfo {

	@Comment("上传人信息")
	private SysUser creator;
	
	@Comment("文件信息")
	private CcExcel file;
	
	@Comment("文件资源")
	private Resource resource;
	
	public Resource getResource() {
		return resource;
	}

	public void setResource(Resource resource) {
		this.resource = resource;
	}

	public SysUser getCreator() {
		return creator;
	}

	public void setCreator(SysUser creator) {
		this.creator = creator;
	}

	public CcExcel getFile() {
		return file;
	}

	public void setFile(CcExcel file) {
		this.file = file;
	}
}
