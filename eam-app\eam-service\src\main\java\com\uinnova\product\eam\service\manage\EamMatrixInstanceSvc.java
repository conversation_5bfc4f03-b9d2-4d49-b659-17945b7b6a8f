package com.uinnova.product.eam.service.manage;

import com.binary.jdbc.Page;
import com.uinnova.product.eam.comm.dto.EamMatrixQueryVO;
import com.uinnova.product.eam.comm.dto.EamMatrixTableQuery;
import com.uinnova.product.eam.comm.dto.EamMatrixTableSaveVO;
import com.uinnova.product.eam.comm.model.es.EamMatrixInstance;
import com.uinnova.product.eam.model.cj.dto.RenameRequestDTO;
import com.uinnova.product.eam.model.vo.ESAttrAggScreenBean;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.base.LibType;
import org.elasticsearch.index.query.QueryBuilder;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface EamMatrixInstanceSvc {

    /**
     * 创建矩阵表格
     * @param matrixId 制品id
     * @param dirId 目录id
     * @return id
     */
    Long create(Long matrixId, Long dirId);

    /**
     * 保存矩阵表格
     * @param instance 表格信息
     * @return 表格id
     */
    Long saveOrUpdate(EamMatrixInstance instance, LibType libType);

    /**
     * 保存表格数据
     * @param vo 表格数据
     * @return id
     */
    Long saveOrUpdateTable(EamMatrixTableSaveVO vo);

    /**
     * 获取矩阵表格基本信息
     * @param id 表格id
     * @return 基本信息
     */
    EamMatrixInstance getBaseInfoById(Long id, LibType libType);

    /**
     * 根据查询条件分页获取矩阵表格列表
     * @param query 查询条件
     * @param pageNum 页数
     * @param pageSize 条数
     * @param sortField 排序
     * @param libType 库
     * @return 表格列表
     */
    Page<EamMatrixInstance> getSortListByQuery(QueryBuilder query, int pageNum, int pageSize, String sortField, LibType libType);

    /**
     * 查询表格数据
     * @param queryVO 查询条件
     * @return 表格数据
     */
    EamMatrixTableSaveVO getTableInfo(EamMatrixQueryVO queryVO);

    /**
     * 保存或者更新矩阵信息
     * @param matrixInstance matrixInstance
     * @return EamMatrixInstance
     */
    Integer saveOrUpdateBatchMatrix(List<EamMatrixInstance> matrixInstance, LibType libType);

    /**
     * 批量查询矩阵表格信息
     * @param matrixIds matrixIds
     * @param libType libType
     * @return List<EamMatrixInstance>
     */
    List<EamMatrixInstance> selectByMatrixIds(List<Long> matrixIds, LibType libType);

    /**
     * 删除矩阵表格
     *
     * @param matrixInstances 矩阵表格信息
     * @param dirId               删除之后的目录
     * @param libType  库
     * @param delType         1 逻辑删除 2 物理删除
     */
    void deleteBatchMatrix(List<EamMatrixInstance> matrixInstances, long dirId, LibType libType, int delType);

    /**
     * 根据矩阵id删除物理删除
     * @param libType 库
     * @param matrixIds 矩阵ids
     */
    void deleteBatchByMatrixIdsPhysics(LibType libType, List<Long> matrixIds);

    /**
     * 重命名
     *
     * @param renameRequestDTO 重命名请求
     * @return Integer
     */
    Long renameMatrix(RenameRequestDTO renameRequestDTO);

    /**
     * 查询矩阵关联的ci
     * @param query 字段定义id,模糊匹配name,分类id
     * @return ci分页
     */
    Page<ESCIInfo> getCiByClass(ESAttrAggScreenBean query);

    /**
     * 查询ci源端目标端关系
     * @param query 矩阵表格id 源端ciCode 目标ciCode
     * @return 关系属性
     */
    Map<String, String> getRltByCode(EamMatrixTableQuery query);

    /**
     * 根据目录id查询矩阵
     * @param dirIds 目录信息
     * @param libType 库
     * @return List<EamMatrixInstance>
     */
    List<EamMatrixInstance> queryMatrixByDirIds(Set<Long> dirIds, LibType libType);

    /**
     * 根据发布id查询矩阵表格
     * @param id 发布id
     * @param ownerCode 用户标识
     * @param libType 库
     * @return 矩阵
     */
    List<EamMatrixInstance> queryByPublishId(Long id, String ownerCode, LibType libType);

    /**
     * 查询表格历史版本
     * @param id 矩阵表格id
     * @return 历史版本
     */
    List<EamMatrixInstance> getTableHistory(Long id);

    /**
     * 查询表格对象及关系数据
     * @param query 查询条件
     * @return 对象及关系
     */
    Object getTableData(EamMatrixTableQuery query);
}
