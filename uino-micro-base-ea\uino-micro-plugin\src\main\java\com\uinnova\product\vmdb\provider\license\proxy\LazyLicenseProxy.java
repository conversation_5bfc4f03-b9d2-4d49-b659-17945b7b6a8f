package com.uinnova.product.vmdb.provider.license.proxy;

import com.binary.core.util.BinaryUtils;
import com.binary.framework.exception.ServiceException;
import com.uinnova.product.vmdb.comm.license.License;
import com.uinnova.product.vmdb.comm.license.LicenseProxy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Timer;
import java.util.TimerTask;

public class LazyLicenseProxy extends AbstractLicenseProxy {
	private static final Logger logger = LoggerFactory.getLogger(LicenseProxy.class);

	
	private final Object syncobj = new Object();
	
	
	private final TimerTask refreshTask = new TimerTask() {
		
		@Override
		public void run() {
			try {
				refreshLicense();
			}catch(Throwable t) {
				logger.error(" refresh license failed! ", t);
			}
		}
	};
	
	
	
	/** 刷新间隔时间 (单位:秒) **/
	private int refreshIntervalTime = 10;
	
	
	
	private boolean started = false;

	
	
	private License license;
	
	
	
	
	public LazyLicenseProxy() {
	}
	public LazyLicenseProxy(int refreshIntervalTime) {
		if(refreshIntervalTime < 1) throw new ServiceException(" is wrong refreshIntervalTime '"+refreshIntervalTime+"'! ");
		this.refreshIntervalTime = refreshIntervalTime;
	}
	
	
	
	
	
	private void startTask() {
    	if(this.started) return ;
    	
    	Timer timer = new Timer(LicenseProxy.class.getName(), true);
    	int intervalTime = this.refreshIntervalTime*1000;
        timer.schedule(refreshTask, intervalTime, intervalTime);
        
        this.started = true;
    }
    
    
    
	private void refreshLicense() {
		synchronized (syncobj) {
			startTask();
			
			try {
				this.license = super.getLicense();
			}catch(Throwable t) {
				throw BinaryUtils.transException(t, ServiceException.class);
			}
		}
	}
	
	
		
	
	
	
	@Override
	public License getLicense() {
		synchronized (syncobj) {
			if(this.license == null) {
				try {
					refreshLicense();
				}catch(Throwable t) {
					throw new ServiceException(" get license failed! ", t);
				}
			}
			return this.license;
		}
	}
	
	
	
	
	
	
}
