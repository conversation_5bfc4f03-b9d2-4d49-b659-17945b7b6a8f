package com.uino.api.client.permission.local;

import java.util.Collections;
import java.util.List;
import java.util.Set;

import com.uino.dao.BaseConst;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.binary.jdbc.Page;
import com.uino.service.permission.microservice.IOrgSvc;
import com.uino.bean.permission.base.SysOrg;
import com.uino.bean.permission.business.OrgNodeInfo;
import com.uino.bean.permission.business.request.AddOrRemoveRoleToOrgRequestDto;
import com.uino.bean.permission.business.request.AddOrRemoveUserToOrgRequestDto;
import com.uino.bean.permission.business.request.InterchangeOrgNoRequestDto;
import com.uino.bean.permission.business.request.SaveOrgRequestDto;
import com.uino.bean.permission.business.request.SetUsersOrgsRequestDto;
import com.uino.bean.permission.query.CSysOrg;
import com.uino.api.client.permission.IOrgApiSvc;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
public class OrgApiSvcLocal implements IOrgApiSvc {
	@Autowired
	private IOrgSvc orgSvc;

	@Override
	public OrgNodeInfo getOrgTree() {
		// TODO Auto-generated method stub
		return orgSvc.getOrgTree(BaseConst.DEFAULT_DOMAIN_ID);
	}

	@Override
	public OrgNodeInfo getOrgTree(Long domainId, Long rootOrg, boolean findUser) {
		return orgSvc.getOrgTree(domainId,rootOrg,findUser);
	}

	@Override
	public OrgNodeInfo getOrgTree(Long domainId) {
		return orgSvc.getOrgTree(domainId);
	}

	@Override
	public List<SysOrg> getOrgListByParentId(Long parentId) {
		return orgSvc.getOrgListByParentId(parentId);
	}

	@Override
	public OrgNodeInfo getOrgTreeV2(Long orgId) {
		// TODO Auto-generated method stub
		return orgSvc.getOrgTreeV2(BaseConst.DEFAULT_DOMAIN_ID,orgId);
	}

	@Override
	public OrgNodeInfo getOrgTreeV2(Long domainId, Long orgId) {
		return orgSvc.getOrgTreeV2(domainId,orgId);
	}

	@Override
	public OrgNodeInfo getOrgTreeV3(Long domainId) {
		return orgSvc.getOrgTreeV3(domainId);
	}

	@Override
	public Long saveOrUpdateOrg(SaveOrgRequestDto request) {
		// TODO Auto-generated method stub
		return orgSvc.saveOrUpdateOrg(request);
	}

	@Override
	public void deleteOrg(Set<Long> removeOrgIds) {
		// TODO Auto-generated method stub
		orgSvc.deleteOrg(removeOrgIds);
	}

	@Override
	public void addUserForOrg(AddOrRemoveUserToOrgRequestDto request) {
		// TODO Auto-generated method stub
		orgSvc.addUserForOrg(request);
	}

	@Override
	public void removeUserForOrg(AddOrRemoveUserToOrgRequestDto request) {
		// TODO Auto-generated method stub
		orgSvc.removeUserForOrg(request);
	}

	@Override
	public void addRoleForOrg(AddOrRemoveRoleToOrgRequestDto request) {
		// TODO Auto-generated method stub
		orgSvc.addRoleForOrg(request);
	}

	@Override
	public void removeRoleForOrg(AddOrRemoveRoleToOrgRequestDto request) {
		// TODO Auto-generated method stub
		orgSvc.removeRoleForOrg(request);
	}

	@Override
	public Set<Long> getUserIds(Long orgId) {
		// TODO Auto-generated method stub
		return orgSvc.getUserIds(orgId);
	}

	@Override
	public Set<Long> getRoleIds(Long orgId) {
		// TODO Auto-generated method stub
		return orgSvc.getRoleIds(orgId);
	}

	@Override
    public List<SysOrg> getOrgByRoleId(Long roleId) {
        return orgSvc.getOrgByRoleId(roleId);
    }

    @Override
	public Page<SysOrg> queryPageByCdt(int pageNum, int pageSize, CSysOrg query) {
		// TODO Auto-generated method stub
		return orgSvc.queryPageByCdt(pageNum, pageSize, query);
	}

	@Override
	public List<SysOrg> queryListByCdt(CSysOrg query) {
		// TODO Auto-generated method stub
		return orgSvc.queryListByCdt(query);
	}

	@Override
	public void interchangeOrgNo(InterchangeOrgNoRequestDto reqDto) {
		// TODO Auto-generated method stub
		orgSvc.interchangeOrgNo(reqDto);
	}

	@Override
	public void setUsersOrgs(SetUsersOrgsRequestDto reqDto) {
		// TODO Auto-generated method stub
		orgSvc.setUsersOrgs(reqDto);
	}

}
