package com.uino.provider.server.web.cmdb.mvc;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.uino.provider.feign.cmdb.DataSetTopFeign;
import com.uino.service.cmdb.dataset.microservice.IDataSetTopSvc;

/**
 * 
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("feign/cmdb/datasetTop")
public class DataSetTopFeignMvc implements DataSetTopFeign {

	Log logger = LogFactory.getLog(DataSetTopFeignMvc.class);
	
	@Autowired
	private IDataSetTopSvc dataSetTopSvc;

	@Override
	public void collectDataSet(Long dataSetId) {
		logger.info("collectDataSet param: "+dataSetId);
		Long now = System.currentTimeMillis();
		dataSetTopSvc.collectDataSet(dataSetId);
		logger.info("collectDataSet time: "+(System.currentTimeMillis()-now));
	}

	@Override
	public void deleteDataSetTop(Long dataSetId) {
		dataSetTopSvc.deleteDataSetTop(dataSetId);
	}

}
