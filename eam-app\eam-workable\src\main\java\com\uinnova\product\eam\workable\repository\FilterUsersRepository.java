package com.uinnova.product.eam.workable.repository;

import com.uinnova.product.eam.workable.model.FilterUser;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * description
 *
 * <AUTHOR>
 * @since 2022/3/20 18:18
 */
public interface FilterUsersRepository extends JpaRepository<FilterUser, String> {

    List<FilterUser> findFilterUserByFlowableInstanceIdAndFlowableTaskKey(String flowableInstanceId, String flowableTaskKey);

    @Transactional
    void deleteFilterUserByFlowableInstanceId(String flowableInstanceId);
}
