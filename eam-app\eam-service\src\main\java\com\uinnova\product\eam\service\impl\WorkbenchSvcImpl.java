package com.uinnova.product.eam.service.impl;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.uinnova.product.eam.base.diagram.model.ESDiagram;
import com.uinnova.product.eam.db.diagram.es.ESDiagramDao;
import com.uinnova.product.eam.model.ArchitectureCount;
import com.uinnova.product.eam.model.UserArchitectureTreeBO;
import com.uinnova.product.eam.model.cj.domain.PlanDesignInstance;
import com.uinnova.product.eam.model.cj.enums.PlanStatusEnum;
import com.uinnova.product.eam.model.constants.Constants;
import com.uinnova.product.eam.service.WorkbenchSvc;
import com.uinnova.product.eam.service.cj.dao.PlanDesignInstanceDao;
import com.uino.api.client.permission.IModuleApiSvc;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.permission.business.ModuleNodeInfo;
import com.uino.util.sys.SysUtil;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.TermQueryBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class WorkbenchSvcImpl implements WorkbenchSvc {

    @Autowired
    private IModuleApiSvc moduleApiSvc;

    @Autowired
    private ESDiagramDao esDiagramDao;

    @Resource
    private PlanDesignInstanceDao planDesignInstanceDao;

    private static final String QuickEAModuleCode = "8948830619007584";

    private static final String DesignSpaceModuleCode = "8948830619007619";

    private static final String BusinessStructureModuleCode = "8948830619008341";

    private static final String ViewTemplateModuleCode = "1214090313207077";

    private static final String OtherModuleCode = "8948830619008748";

    private static final String ApplicationModuleCode = "8964026895302176";

    private static final String ArchitecturePlan = "1384295824926386";

    private static final List<String> ModuleCodeList = Lists.newArrayList("8964026895302176","8963553354803586","8963553354803502","1384295824926386");

    private static final Map<String,Integer> map = ImmutableMap.<String,Integer>builder()
            .put("业务架构设计-流程建模",10)
            .put("业务架构设计-组件建模",100)
            .put("业务架构设计-产品建模",1)
            .put("应用架构设计",11)
            .put("数据架构设计",101)
            .put("技术架构设计",111)
            .put("产品方案设计",110)
            .build();

    @Override
    public List<UserArchitectureTreeBO> getUserArchitectureTree(Integer type) {
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        ModuleNodeInfo tree = moduleApiSvc.getModuleTree(currentUserInfo.getDomainId(), currentUserInfo.getId());
        List<ModuleNodeInfo> result = new ArrayList<>();
        if (tree == null || tree.getChildren() == null || tree.getChildren().size() < 1) {
            return new ArrayList<>();
        }
        Optional<ModuleNodeInfo> quickEAModule = tree.getChildren().stream().filter(item -> item.getModuleCode().equals(QuickEAModuleCode)).findFirst();
        ModuleNodeInfo quickEAModuleNodeInfo = quickEAModule.orElse(null);
        if (null == quickEAModuleNodeInfo || quickEAModuleNodeInfo.getChildren() == null || quickEAModuleNodeInfo.getChildren().size() < 1) {
            return new ArrayList<>();
        }
        Optional<ModuleNodeInfo> designModule = quickEAModuleNodeInfo.getChildren().stream().filter(item -> item.getModuleCode().equals(DesignSpaceModuleCode)).findFirst();
        ModuleNodeInfo designModuleNodeInfo = designModule.orElse(null);
        if (designModuleNodeInfo == null || designModuleNodeInfo.getChildren().size() < 1) {
            return new ArrayList<>();
        }
        List<ModuleNodeInfo> children = designModuleNodeInfo.getChildren();
        Optional<ModuleNodeInfo> businessStructureModule = children.stream().filter(item -> item.getModuleCode().equals(BusinessStructureModuleCode)).findFirst();
        ModuleNodeInfo businessStructureNodeInfo = businessStructureModule.orElse(null);
        if (type == 1) {
            if (businessStructureNodeInfo != null && businessStructureNodeInfo.getChildren().size() > 0) {
                for (ModuleNodeInfo child : businessStructureNodeInfo.getChildren()) {
                    child.setLabel(businessStructureNodeInfo.getLabel()+"-"+child.getLabel());
                    result.add(child);
                }
            }
            children.remove(businessStructureNodeInfo);
            List<ModuleNodeInfo> otherNodeInfo = children.stream().filter(item -> ModuleCodeList.contains(item.getModuleCode())).collect(Collectors.toList());
            if (otherNodeInfo != null && otherNodeInfo.size() > 0) {
                result.addAll(otherNodeInfo);
            }
        }else{
            if (businessStructureNodeInfo != null && businessStructureNodeInfo.getChildren().size() > 0) {
                Optional<ModuleNodeInfo> OtherModuleNodeInfo = businessStructureNodeInfo.getChildren().stream().filter(item -> item.getModuleCode().equals(OtherModuleCode)).findFirst();
                ModuleNodeInfo moduleNodeInfo = OtherModuleNodeInfo.orElse(null);
                if (moduleNodeInfo != null) {
                    moduleNodeInfo.setLabel(businessStructureNodeInfo.getLabel()+"-"+moduleNodeInfo.getLabel());
                    result.add(moduleNodeInfo);
                }
            }
            children.remove(businessStructureNodeInfo);
            List<ModuleNodeInfo> otherNodeInfo = children.stream().filter(item -> ModuleCodeList.contains(item.getModuleCode())).collect(Collectors.toList());
            List<ModuleNodeInfo> findModuleList = otherNodeInfo.stream().filter(item -> item.getModuleCode().equals(ApplicationModuleCode) || item.getModuleCode().equals(ArchitecturePlan)).collect(Collectors.toList());

            if (findModuleList != null && findModuleList.size() > 0) {
                result.addAll(findModuleList);
            }
        }
        List<UserArchitectureTreeBO> packResult = new ArrayList<>();
        for (ModuleNodeInfo moduleNodeInfo : result) {
            UserArchitectureTreeBO userArchitectureTreeBO = new UserArchitectureTreeBO();
            userArchitectureTreeBO.setModuleName(moduleNodeInfo.getLabel());
            userArchitectureTreeBO.setDirType(map.get(moduleNodeInfo.getLabel()));
            packResult.add(userArchitectureTreeBO);
        }
        return packResult;
    }

    @Override
    public ArchitectureCount getArchitectureCount() {
        ArchitectureCount architectureCount = new ArchitectureCount();
        TermQueryBuilder dataStatus = QueryBuilders.termQuery("dataStatus", 1);
        TermQueryBuilder status = QueryBuilders.termQuery("status", 1);
        TermQueryBuilder historyVersionFlag = QueryBuilders.termQuery("historyVersionFlag", 1);
        TermQueryBuilder isOpen = QueryBuilders.termQuery("isOpen", 0);
        BoolQueryBuilder mustQuery = QueryBuilders.boolQuery()
                .must(dataStatus)
                .must(historyVersionFlag)
                .must(isOpen)
                .must(status);

        List<ESDiagram> listByQuery = esDiagramDao.getListByQuery(mustQuery);
        architectureCount.setDiagramCount(CollectionUtils.isEmpty(listByQuery) ? 0 : listByQuery.size());

        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.must(QueryBuilders.termQuery("status.keyword", PlanStatusEnum.published));
        queryBuilder.must(QueryBuilders.termQuery("assetsType", Constants.ASSETS));
        queryBuilder.must(QueryBuilders.termQuery("isCurrentVersion", true));
        List<PlanDesignInstance> planDesignInstances = planDesignInstanceDao.getListByQuery(queryBuilder);
        architectureCount.setPlanCount(CollectionUtils.isEmpty(planDesignInstances) ? 0 : planDesignInstances.size());
        /*List<VcArchitectureReviewList> architectureReviewLists = architectureReviewService.queryArchitectureReviewList(null);
        architectureCount.setArchitectureCount(CollectionUtils.isEmpty(architectureReviewLists) ? 0 : architectureReviewLists.size());*/
        return architectureCount;
    }
}
