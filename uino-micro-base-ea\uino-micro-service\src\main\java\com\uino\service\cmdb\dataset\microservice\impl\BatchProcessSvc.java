package com.uino.service.cmdb.dataset.microservice.impl;

import com.alibaba.fastjson.JSONObject;
import com.uino.bean.cmdb.base.dataset.DataSetMallApiRelationRule;
import com.uino.bean.cmdb.base.dataset.DataSetMallApiType;
import com.uino.bean.cmdb.base.dataset.batch.DataSetExeResultSheet;
import com.uino.bean.cmdb.business.dataset.FriendInfo;
import com.uino.bean.cmdb.business.dataset.RltRulePathData;
import com.uino.bean.cmdb.business.dataset.RltRuleTableData;
import com.uino.dao.cmdb.dataset.ESDataSetExeResultSheetSvc;
import com.uino.dao.cmdb.dataset.ESDataSetSvc;
import com.uino.service.cmdb.dataset.microservice.IBatchProcessSvc;
import com.uino.service.cmdb.dataset.microservice.IDataSetSvc;
import com.uino.service.cmdb.dataset.microservice.IRelationRuleAnalysisSvc;
import com.uino.service.cmdb.microservice.ITaskLockSvc;
import com.uino.util.cache.ICacheService;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.*;

/**
 * 批处理关系遍历规则的服务
 *
 * <AUTHOR>
 * @version 2020/4/23
 */
@Service
@Slf4j
public class BatchProcessSvc implements IBatchProcessSvc {


    @Autowired
    private ITaskLockSvc taskLockSvc;

    @Autowired
    private IDataSetSvc dataSetSvc;

    @Autowired
    private ESDataSetSvc esDataSetSvc;

    @Autowired
    private IRelationRuleAnalysisSvc relationRuleAnalysisSvc;

    @Autowired
    private ESDataSetExeResultSheetSvc esDataSetExeResultSheetSvc;

    @Resource
    private ICacheService cacheService;

    //业务架构全景缓存key
    public static final String EAM_PANORAMA_TREE_KEY = "eam:panorama:tree:";

    /**
     * 取消svc层定时任务批处理，转移到web，代码暂时保留
     */
//    @Scheduled(cron = "${batch.process.relation.rule.cron:0 0 */1 * * ?}")
    public void execute() {
        log.info("####The batch process job start...");

        String taskName = "batchProcessRelationRule";
        // 任务执行时长默认估算为5分钟
        long taskExecuteTime = 5 * 60 * 1000L;
        // 无法获得任务锁则直接返回
        if (!taskLockSvc.getLock(taskName, taskExecuteTime)) {
            log.info("Can not get lock, return.");
            return;
        }
        long jobStartTime = System.currentTimeMillis();
        try {
            BoolQueryBuilder dataSetMallApiQuery = QueryBuilders.boolQuery();
            dataSetMallApiQuery.must(QueryBuilders.termQuery("type", DataSetMallApiType.RelationRule.getCode()));
            List<JSONObject> dataSetMallApiAll = esDataSetSvc.getListByQuery(dataSetMallApiQuery);
            for (JSONObject dataset : dataSetMallApiAll) {
                // 数据集获得遍历结果，并按路径拆分为Sheet页进行存储
                processDataSetRule(new DataSetMallApiRelationRule(dataset));
            }
        } catch (Exception e1) {
            log.error("Task execute error", e1);
        } finally {
            // 解除任务锁
            taskLockSvc.breakLock(taskName);
        }
        long jobEndTime = System.currentTimeMillis();
        log.info("####The batch process job end. Cost time:[" + (jobEndTime - jobStartTime) + "]ms");
    }


    /**
     * 数据集获得遍历结果，并按路径拆分为Sheet页进行存储
     *
     * @param dataSetRelationRule 关系遍历数据集
     */
    @Override
    public void processDataSetRule(DataSetMallApiRelationRule dataSetRelationRule) {

        long t1 = System.currentTimeMillis();
        //DataSetMallApiRelationRule dataSetRelationRule = dataSetMallApiList.get(0);
        log.info("###############Start batch process dataset [" + dataSetRelationRule.getName() + "]");

        // 为了防止批处理任务和编辑任务冲突，每个数据集进行批处理之前加锁，完成之后解锁
        String taskName = "DataSetTask_" + dataSetRelationRule.getId();
        // 任务执行时长默认估算为5分钟
        long taskExecuteTime = 5 * 60 * 1000L;
        if (!taskLockSvc.getLock(taskName, taskExecuteTime)) {
            log.info("Can not get the dataset execute lock.");
            return;
        }
        try {
            this.process(dataSetRelationRule, t1);
        } catch (Exception e) {
            log.error("新增", e);
            log.error("Process dataset[" + dataSetRelationRule.getName() + " error]", e);
        } finally {
            taskLockSvc.breakLock(taskName);
        }
        long t2 = System.currentTimeMillis();
        log.info("###############End batch process dataset, [" + dataSetRelationRule.getName() + "] cost:" + (t2 - t1));
        cacheService.delKey(EAM_PANORAMA_TREE_KEY + dataSetRelationRule.getId());
        log.info("===============Clear Business Panorama Cache==============");
    }

    /**
     * 数据集遍历结果执行方法
     *
     * @param dataSetRelationRule 数据集
     * @throws Exception 异常
     */
    private void process(DataSetMallApiRelationRule dataSetRelationRule, Long createTime) {
        Map<Long, FriendInfo> friendInfoMap = relationRuleAnalysisSvc.queryCiFriendByRule(dataSetRelationRule);
		
        // 存数据集查询出的关系遍历结果结果
        dataSetSvc.updateMallApiExeResult(dataSetRelationRule, friendInfoMap);

        // 清除上次执行结果
        BoolQueryBuilder delQuery = QueryBuilders.boolQuery();
        delQuery.must(QueryBuilders.termQuery("dataSetId", dataSetRelationRule.getId()));
        esDataSetExeResultSheetSvc.deleteByQuery(delQuery, true);


        //<NodePath,sheet页ID>
        Map<String, Integer> nodePathSheetMap = new HashMap<>();

        //<NodePath,Header>
        Map<String, List<Map<String, String>>> nodePathHeaderMap = new HashMap<>();

        int size = 0;
        int ruleDataSize = 0;
        // 将关系遍历结果按路径拆分Sheet并存取
        Long now = System.currentTimeMillis();
        Long nano = System.nanoTime();
        for (Map.Entry<Long, FriendInfo> friendInfoMapEntry : friendInfoMap.entrySet()) {
            // 获取有效返回FriendInfo结果的入口
            RltRuleTableData rltRuleTableData = relationRuleAnalysisSvc.disassembleFriendInfoDataByPath(dataSetRelationRule, Arrays.asList(friendInfoMapEntry.getKey()), friendInfoMapEntry.getValue());

            List<RltRulePathData> rltRulePathDateList = rltRuleTableData.getSheetList();

            List<Map<String, String>> headerList = null;
            List<Long> classIds = null;
            List<String> classNamePath = null;
            log.info("#######KEY CI Start" + friendInfoMapEntry.getKey());
            log.info("#######RuleTableData Size:" + rltRuleTableData.getSheetList().get(0).getData().size());
            ruleDataSize += rltRuleTableData.getSheetList().get(0).getData().size();
            // 按路径进行Sheet页的拆分
            for (int j = 0; j < rltRulePathDateList.size(); j++) {
                RltRulePathData rltRulePathDate = rltRulePathDateList.get(j);
                List<List<Object>> ruleDatas = rltRulePathDate.getData();

                classIds = new ArrayList<>();
                classNamePath = new ArrayList<>();
                headerList = new ArrayList<>();
                // 表头信息
                Map<String, String> headMap = null;
                String nodePath = rltRulePathDate.getNodePath();
                String[] pageNodeArray = nodePath.split("-");
                List<Map<String, Object>> headers = rltRulePathDate.getHeaders();
                for (int i = 0; i < headers.size(); i++) {
                    Map<String, Object> header = headers.get(i);
                    Long classId = (Long) header.get("classId");
                    String className = (String) header.get("className");
                    List<String> labels = (List<String>) header.get("labels");
                    List<Integer> proTypes = (List<Integer>) header.get("proTypes");
                    List<String> constraintRules = (List<String>) header.get("constraintRules");
                    for (int k=0;k<labels.size();k++) {
                    	String label = labels.get(k);
                        headMap = new HashMap<>();
                        headMap.put("className", className);
                        headMap.put("classId", classId + "");
                        headMap.put("attrKey", pageNodeArray[i] + "_" + className + "_" + label);
                        headMap.put("attrName", label);
                        headMap.put("proType", proTypes.get(k)+"");
                        headMap.put("constraintRule", constraintRules.get(k));
                        headerList.add(headMap);
                    }
                    classIds.add(classId);
                    classNamePath.add(className);
                }

                // 如果此路径(NodePath)还没有对应的Sheet页
                if (!nodePathSheetMap.containsKey(rltRulePathDate.getNodePath())) {
                    nodePathSheetMap.put(rltRulePathDate.getNodePath(), nodePathSheetMap.size() + 1);
                }

                // 如果此路径(NodePath)还没有对应的Sheet页
                if (!nodePathHeaderMap.containsKey(rltRulePathDate.getNodePath())) {
                    nodePathHeaderMap.put(rltRulePathDate.getNodePath(), headerList);
                }
                List<DataSetExeResultSheet> resultSheetDataList = new ArrayList<>();
                for (List<Object> ruleData : ruleDatas) {
                    Map<String, Object> attrsMap = new HashMap<>();
                    for (int i = 0; i < ruleData.size(); i++) {
                        attrsMap.put(headerList.get(i).get("attrKey"), ruleData.get(i));
                    }
                    DataSetExeResultSheet retSheet = new DataSetExeResultSheet();
                    Long differ = System.nanoTime() - nano;
                    Long mills = differ / 1000 / 1000;
                    String nn = (differ % (1000 * 1000)) + "";
                    while (nn.length() < 6) {
                        nn = "0" + nn;
                    }
                    retSheet.setId(((now + mills) + "") + nn);
                    retSheet.setDataSetId(dataSetRelationRule.getId());
                    retSheet.setStartCiId(friendInfoMapEntry.getKey());
                    retSheet.setAttrs(attrsMap);
                    retSheet.setSheetName(rltRulePathDate.getNodePath());
                    retSheet.setSheetId("Path" + (nodePathSheetMap.get(rltRulePathDate.getNodePath())));
                    retSheet.setClassIds(classIds);
                    retSheet.setClassNamePath(classNamePath);
                    retSheet.setCreateTime(createTime);
                    retSheet.setHeaders(nodePathHeaderMap.get(rltRulePathDate.getNodePath()));
                    resultSheetDataList.add(retSheet);
                    //resultSheetDao.saveOrUpdate(retSheet.toJson());
                    size++;
                }
                //size+=resultSheetDataList.size();
                // 将拆分为Sheet后的遍历结果入库
                // resultSheetDao.saveOrUpdateBatchNoRefresh(resultSheetDataList);
                // 测试表示点的快的时候，任务锁已经解锁了，但是由于ES的延时入库，数据可能看不到
                esDataSetExeResultSheetSvc.saveOrUpdateBatch(resultSheetDataList);
            }
        }
        log.info("###############Rule data total " + ruleDataSize);
        log.info("###############Insert total " + size);

    }
}
