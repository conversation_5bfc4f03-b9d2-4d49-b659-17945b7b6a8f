package com.uinnova.product.eam.web.assetManager;

import com.binary.framework.web.RemoteResult;
import com.uinnova.product.eam.model.AssetWarehouseDirVo;
import com.uinnova.product.eam.model.enums.AssetType;
import com.uinnova.product.eam.service.asset.ProjectAssetWarehouseDicSvc;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 资产仓库目录
 * <p>
 *
 * @autor szq
 * @date 2025/01/09
 */
@Slf4j
@RestController
@RequestMapping("/eam")
public class ProjectAssetWarehouseDirMvc {

    @Autowired
    private ProjectAssetWarehouseDicSvc warehouseDicSvc;

    @GetMapping("/assetWareHouse/getPublishDirTree")
    @ModDesc(desc = "获取资产发布分库目录树", pDesc = "用户标识,资产类型,资产id",
            rDesc = "目录树列表", rType = RemoteResult.class)
    public RemoteResult getPublishDirTree(@RequestParam(required = false) Long userId,
                                    @RequestParam(required = false) AssetType assetType,
                                    @RequestParam String assetId) {
        List<AssetWarehouseDirVo> result = warehouseDicSvc.getPublishDirTree(assetType,
                userId, assetId);
        return new RemoteResult(result);
    }
}
