package com.uinnova.product.eam.base.diagram.model;




import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;


@Comment("标签表[VC_TAG]")
public class VcTag implements EntityBean {
	private static final long serialVersionUID = 1L;


	@Comment("ID[ID]")
	private Long id;


	@Comment("标签名称[TAG_NAME]")
	private String tagName;


	@Comment("标签类型[TAG_TYPE]    标签类型:1=视图标签")
	private Integer tagType;


	@Comment("定义描述[DEF_DESC]")
	private String defDesc;


	@Comment("父标签id[PARENT_ID]")
	private Long parentId;


	@Comment("标签属性[TAG_ATTR]    0=无,1=不能删除,2=不能删除且为团队")
	private Integer tagAttr;


	@Comment("所属域[DOMAIN_ID]")
	private Long domainId;


	@Comment("数据状态[DATA_STATUS]    数据状态:数据状态：1-正常 0-删除")
	private Integer dataStatus;


	@Comment("创建人[CREATOR]")
	private String creator;


	@Comment("修改人[MODIFIER]")
	private String modifier;


	@Comment("创建时间[CREATE_TIME]    创建时间:yyyyMMddHHmmss")
	private Long createTime;


	@Comment("修改时间[MODIFY_TIME]    修改时间:yyyyMMddHHmmss")
	private Long modifyTime;




	public Long getId() {
		return this.id;
	}
	public void setId(Long id) {
		this.id = id;
	}


	public String getTagName() {
		return this.tagName;
	}
	public void setTagName(String tagName) {
		this.tagName = tagName;
	}


	public Integer getTagType() {
		return this.tagType;
	}
	public void setTagType(Integer tagType) {
		this.tagType = tagType;
	}


	public String getDefDesc() {
		return this.defDesc;
	}
	public void setDefDesc(String defDesc) {
		this.defDesc = defDesc;
	}


	public Long getParentId() {
		return this.parentId;
	}
	public void setParentId(Long parentId) {
		this.parentId = parentId;
	}


	public Integer getTagAttr() {
		return this.tagAttr;
	}
	public void setTagAttr(Integer tagAttr) {
		this.tagAttr = tagAttr;
	}


	public Long getDomainId() {
		return this.domainId;
	}
	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}


	public Integer getDataStatus() {
		return this.dataStatus;
	}
	public void setDataStatus(Integer dataStatus) {
		this.dataStatus = dataStatus;
	}


	public String getCreator() {
		return this.creator;
	}
	public void setCreator(String creator) {
		this.creator = creator;
	}


	public String getModifier() {
		return this.modifier;
	}
	public void setModifier(String modifier) {
		this.modifier = modifier;
	}


	public Long getCreateTime() {
		return this.createTime;
	}
	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}


	public Long getModifyTime() {
		return this.modifyTime;
	}
	public void setModifyTime(Long modifyTime) {
		this.modifyTime = modifyTime;
	}


}


