package com.uinnova.product.eam.model.vo;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 矩阵表格实体
 * <AUTHOR>
 */
@Data
public class EamMatrixTableVo implements Serializable {

    @Comment("矩阵类型:1=关系矩阵,2=属性矩阵")
    private Integer matrixType;

    @Comment("属性字段名")
    private String attrName;

    @Comment("标题")
    private String label;

    @Comment("分类名称")
    private String className;

    @Comment("横向表头数据集")
    private List<EamCiTableVo> headers = new ArrayList<>();

    @Comment("纵向表头数据集")
    private List<Map<String, Object>> data = new ArrayList<>();

    @Comment("合并行数map<行value, 合并行数>")
    private Map<String, AtomicInteger> countMap = new HashMap<>();

    public EamMatrixTableVo() {
    }

    public EamMatrixTableVo(Integer matrixType, String label, String className, List<EamCiTableVo> headers) {
        this.matrixType = matrixType;
        this.label = label;
        this.className = className;
        this.headers = headers;
    }
}
