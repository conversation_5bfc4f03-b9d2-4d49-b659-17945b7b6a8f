package com.uinnova.product.eam.service.flowable.impl;

import com.binary.core.util.BinaryUtils;
import com.uinnova.product.eam.base.exception.ServerException;
import com.uinnova.product.eam.feign.workable.FlowableFeign;
import com.uinnova.product.eam.feign.workable.entity.PorcessResponse;
import com.uinnova.product.eam.model.constants.FlowableConstant;
import com.uinnova.product.eam.service.EamCategorySvc;
import com.uinnova.product.eam.service.diagram.ESDiagramSvc;
import com.uinnova.product.eam.service.flowable.FlowableApprovalSvc;
import com.uinnova.product.eam.service.flowable.FlowableApprovalUserSvc;
import com.uinnova.product.eam.service.fx.GeneralPushSvc;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Slf4j
@Component(value = FlowableConstant.DIAGRAM_DEFINITION_KEY)
public class EamDiagramApprovalSvcImpl implements FlowableApprovalSvc {

    @Resource
    private FlowableFeign flowableFeign;
    @Resource
    private GeneralPushSvc generalPushSvc;
    @Resource
    private EamCategorySvc categorySvc;
    @Resource
    private ESDiagramSvc esDiagramSvc;
    @Resource
    private FlowableApprovalUserSvc approvalUserSvc;

    @Override
    public List<String> getApprovalUser(String businessKey, String taskKey) {
        return approvalUserSvc.getApprovalUser(FlowableConstant.DIAGRAM_DEFINITION_KEY, businessKey, taskKey);
    }

    @Override
    public void reject(String businessKey) {
        Map<String, Object> routerVariables = getApprovalRouterVariables(businessKey);
        Long diagramId = Long.valueOf(routerVariables.get("diagramId").toString());
        // 处理视图状态
        esDiagramSvc.updateFlowStatusByIds(Collections.singletonList(diagramId), 1);
    }

    @Override
    public void pass(String businessKey) {
        Map<String, Object> routerVariables = getApprovalRouterVariables(businessKey);
        Long dirId = Long.valueOf(routerVariables.get("releaseDirId").toString());
        String releaseDesc = BinaryUtils.isEmpty(routerVariables.get("releaseDesc")) ? "" : routerVariables.get("releaseDesc").toString();
        // 发布视图 发布视图中有处理视图状态的逻辑
        log.info("############# 工作流开启视图发布，发布参数：diagramId【{}】，dirDesc【{}】，dirId【{}】", businessKey, releaseDesc, dirId);
        generalPushSvc.publishDiagram(businessKey, releaseDesc, dirId, null, Boolean.FALSE);
        log.info("############# 工作流完成视图发布");
    }

    @Override
    public void cancel(String businessKey) {
        Map<String, Object> routerVariables = getApprovalRouterVariables(businessKey);
        Long diagramId = Long.valueOf(routerVariables.get("diagramId").toString());
        // 处理视图状态
        esDiagramSvc.updateFlowStatusByIds(Collections.singletonList(diagramId), 0);
    }

    /**
     *  根据业务定义key (视图ID) 获取当前流程中的全局变量
     * @param businessKey 业务定义key (视图ID)
     * @return 流程全局变量
     */
    public Map<String, Object> getApprovalRouterVariables(String businessKey) {
        // 获取审批流程信息 todo 这里的 processDefinitionKey 先写死
        PorcessResponse processInstance = flowableFeign.getProcessInstanceByBusinessIdAndProcessDefinitionKey(businessKey, FlowableConstant.DIAGRAM_DEFINITION_KEY);
        if (BinaryUtils.isEmpty(processInstance)) {
            throw new ServerException("流程异常");
        }
        return processInstance.getRouterVariables();
    }
}
