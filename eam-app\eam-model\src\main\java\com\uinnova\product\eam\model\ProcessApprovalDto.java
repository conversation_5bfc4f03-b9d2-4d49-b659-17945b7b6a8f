package com.uinnova.product.eam.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * description
 *
 * <AUTHOR>
 * @since 2024/11/05 10:30
 */
@Data
public class ProcessApprovalDto {

    @ApiModelProperty(value = "任务id",required = true)
    private String taskId;

    @ApiModelProperty(value = "当前流程的ciCode",required = true)
    private String businessKey;

    @ApiModelProperty(value = "审核状态  0：通过  1：驳回",required = true)
    private Integer approvalStatus;

    @ApiModelProperty(value = "下一个审批人id(可多个   xxx,sss)",required = true)
    private String nextUserIds;

    @ApiModelProperty(value = "备注",required = true)
    private String remarks;
}
