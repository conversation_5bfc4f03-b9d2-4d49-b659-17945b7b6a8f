package com.uinnova.product.eam.service.impl;

import com.uinnova.product.eam.base.util.BeanToMapUtils;
import com.uinnova.product.eam.comm.bean.ApplicationSysFigureRlt;
import com.uinnova.product.eam.model.VcDiagramInfo;
import com.uinnova.product.eam.service.IEamDiagramSvc;
import com.uinnova.product.eam.service.es.IamsEsApplicationSysFigureRltSvc;
import com.uinnova.product.eam.service.sys.IEamApplicationSysFigureRltSvc;
import com.uino.dao.util.ESUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * 应用系统的图关系接口
 *
 * <AUTHOR>
 */
@Service
public class EamApplicationSysFigureRltSvcImpl implements IEamApplicationSysFigureRltSvc {

    private static final Logger log = LoggerFactory.getLogger(EamApplicationSysFigureRltSvcImpl.class);

    @Autowired
    private IamsEsApplicationSysFigureRltSvc iamsEsApplicationSysFigureRltSvc;

    @Autowired
    private IEamDiagramSvc iEamDiagramSvc;

    @Override
    public Map<String, Object> getByCiCode(String ciCode) {
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery().
                must(QueryBuilders.termQuery("ciCode.keyword", ciCode));
        List<ApplicationSysFigureRlt> applicationSysFigureRlts = iamsEsApplicationSysFigureRltSvc.getListByQuery(queryBuilder);
        if (CollectionUtils.isEmpty(applicationSysFigureRlts)) {

            ApplicationSysFigureRlt applicationSysFigureRlt = new ApplicationSysFigureRlt();
            applicationSysFigureRlt.setCiCode(ciCode);
            addFigureRlt(applicationSysFigureRlt);
            Map<String, Object> entityObjectMap = BeanToMapUtils.entityToMap(applicationSysFigureRlt);
            return entityObjectMap;
        }
        ApplicationSysFigureRlt applicationSysFigureRlt = applicationSysFigureRlts.get(0);
        Map<String, Object> entityObjectMap = BeanToMapUtils.entityToMap(applicationSysFigureRlt);

        entityObjectMap.remove("id");
        entityObjectMap.remove("ciCode");
        Long[] ids = entityObjectMap.values().toArray(new Long[0]);
        List<VcDiagramInfo> vcDiagramInfos = iEamDiagramSvc.queryDiagramInfoByIds(ids);
        for (VcDiagramInfo vcDiagramInfo : vcDiagramInfos) {
            if (!(vcDiagramInfo.getDiagram().getStatus() == 1)) {
                if (entityObjectMap.containsValue(vcDiagramInfo.getDiagram().getId())) {
                    Iterator<String> iterator = entityObjectMap.keySet().iterator();
                    while (iterator.hasNext()) {
                        String next = iterator.next();
                        Object o = entityObjectMap.get(next);
                        if (vcDiagramInfo.getDiagram().getId().equals(o)) {
                            iterator.remove();
                        }
                    }
                }
            }
        }
        entityObjectMap.put("id", applicationSysFigureRlt.getId());
        entityObjectMap.put("ciCode", applicationSysFigureRlt.getCiCode());
        return entityObjectMap;
    }

    @Override
    public Long addFigureRlt(ApplicationSysFigureRlt applicationSysFigureRlt) {

        if (applicationSysFigureRlt.getId() == null) {
            applicationSysFigureRlt.setId(ESUtil.getUUID());
        }
        return iamsEsApplicationSysFigureRltSvc.saveOrUpdate(applicationSysFigureRlt);
    }
}
