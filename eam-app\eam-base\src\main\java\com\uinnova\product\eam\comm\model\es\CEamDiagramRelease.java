package com.uinnova.product.eam.comm.model.es;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.base.diagram.model.EamDiagramQuery;
import lombok.Data;

import java.io.Serializable;

/**
 * 视图发布表
 * <AUTHOR>
 */
@Data
@Comment("uino_eam_diagram_release")
public class CEamDiagramRelease implements Serializable {

    @Comment("ID[ID] operate-Equal[=]")
    private Long id;

    @Comment("ID[ID] operate-In[in]")
    private Long[] ids;

    @Comment("ID[ID] operate-GTEqual[>=]")
    private Long startId;

    @Comment("ID[ID] operate-LTEqual[<=]")
    private Long endId;

    @Comment("视图ID[DIAGRAM_ID] operate-Equal[=]")
    private Long diagramId;

    @Comment("视图ID[DIAGRAM_ID] operate-In[in]")
    private Long[] diagramIds;

    @Comment("视图ID[DIAGRAM_ID] operate-GTEqual[>=]")
    private Long startDiagramId;

    @Comment("视图ID[DIAGRAM_ID] operate-LTEqual[<=]")
    private Long endDiagramId;

    @Comment("视图类型[DIAGRAM_TYPE] operate-Equal[=]")
    private Long diagramType;

    @Comment("视图类型[DIAGRAM_TYPE] operate-In[in]")
    private Long[] diagramTypes;

    @Comment("视图类型[DIAGRAM_TYPE] operate-GTEqual[>=]")
    private Long startDiagramType;

    @Comment("视图类型[DIAGRAM_TYPE] operate-LTEqual[<=]")
    private Long endDiagramType;

    @Comment("自定义版本号[VERSION_NO] operate-Equal[=]")
    private Integer versionNo;

    @Comment("自定义版本号[VERSION_NO] operate-In[in]")
    private Integer[] versionNos;

    @Comment("自定义版本号[VERSION_NO] operate-GTEqual[>=]")
    private Integer startVersionNo;

    @Comment("自定义版本号[VERSION_NO] operate-LTEqual[<=]")
    private Integer endVersionNo;

    @Comment("版本ID[VERSION_ID] operate-Equal[=]")
    private Long versionId;

    @Comment("版本ID[VERSION_ID] operate-In[in]")
    private Long[] versionIds;

    @Comment("版本ID[VERSION_ID] operate-GTEqual[>=]")
    private Long startVersionId;

    @Comment("版本ID[VERSION_ID] operate-LTEqual[<=]")
    private Long endVersionId;

    @Comment("历史版本标识，1--主版本，0--历史版本[HISTORY_VERSION_FLAG] operate-Equal[=]")
    private Integer historyVersionFlag;

    @Comment("所属目录ID[DIR_ID] operate-Equal[=]")
    private Long dirId;

    @Comment("所属目录ID[DIR_ID] operate-In[in]")
    private Long[] dirIds;

    @Comment("所属目录ID[DIR_ID] operate-GTEqual[>=]")
    private Long startDirId;

    @Comment("所属目录ID[DIR_ID] operate-LTEqual[<=]")
    private Long endDirId;

    @Comment("视图创建者")
    private String creatorId;

    @Comment("创建时间")
    private Long createTime;

    @Comment("修改时间")
    private Long modifyTime;

    @Comment("类型，1--企业级架构设计，2--系统级架构设计")
    private Integer dirType;

    @Comment("视图基础信息")
    private transient EamDiagramQuery diagram;
}
