package com.uinnova.product.eam.base.diagram.model;

import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClassDir;
import lombok.Data;

/**
 * @Classname
 * @Description TODO
 * <AUTHOR>
 * @Date 2021-08-24-10:21
 */
@Data
@Comment("用户图形ES查询类")
public class ESShapeDirQuery extends CCcCiClassDir implements EntityBean {

    @Comment("userId")
    private Long userId;

    @Comment("dirName")
    private String dirName;

    @Comment("dirNameEqual")
    private String dirNameEqual;

}
