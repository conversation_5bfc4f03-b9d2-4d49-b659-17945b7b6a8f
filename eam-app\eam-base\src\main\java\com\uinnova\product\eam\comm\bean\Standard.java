package com.uinnova.product.eam.comm.bean;

import java.util.HashSet;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * 标准规范
 *
 * <AUTHOR>
 * @version 2020-9-22
 */
public class Standard {

    private Long id;

    /**
     * 规范标题
     */
    private String title;

    /**
     * 规范版本
     */
    private String version;

    /**
     * 规范描述
     */
    private String desc;

    /**
     * 规范文档
     */
    private Set<Map<String, Object>> docs = new HashSet<>();


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public Set<Map<String, Object>> getDocs() {
        return docs;
    }

    public void setDocs(Set<Map<String, Object>> docs) {
        this.docs = docs;
    }


    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof Standard)) return false;
        Standard that = (Standard) o;
        return Objects.equals(id, that.id) &&
                Objects.equals(title, that.title) &&
                Objects.equals(version, that.version) &&
                Objects.equals(desc, that.desc) &&
                Objects.equals(docs, that.docs);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, title, version, desc, docs);
    }
}
