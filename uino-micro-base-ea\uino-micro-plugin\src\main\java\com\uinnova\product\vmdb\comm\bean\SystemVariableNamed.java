package com.uinnova.product.vmdb.comm.bean;

/**
 * 
 * <AUTHOR>
 *
 */
public enum SystemVariableNamed {

    /** 登录处logo图标地址 **/
    LOGO_PATH_LOGIN(2),

    /** 浏览器标题处logo图标地址 **/
    LOGO_PATH_FAV(2),

    /** 导航处logo图标地址 **/
    LOGO_PATH_NAV(2),
	
	/** 登录页面背景文字 **/
	BACK_PATH_TEXT(2),
	
	/** 登录页面背景图 **/
	BACK_PATH_PICT(2);

    private int type;

    SystemVariableNamed(int type) {
        this.type = type;
    }

    public int getType() {
        return this.type;
    }

}
