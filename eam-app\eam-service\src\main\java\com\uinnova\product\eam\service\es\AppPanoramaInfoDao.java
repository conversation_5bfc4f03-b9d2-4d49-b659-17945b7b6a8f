package com.uinnova.product.eam.service.es;

import com.uinnova.product.eam.comm.model.es.AppPanoramaInfo;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Repository;

import jakarta.annotation.PostConstruct;

/**
 * <AUTHOR>
 */
@Repository
public class AppPanoramaInfoDao extends AbstractESBaseDao<AppPanoramaInfo, AppPanoramaInfo> {
    @Override
    public String getIndex() {
        return "uino_eam_asset_panorama_info";
    }

    @Override
    public String getType() {
        return "_doc";
    }
    @PostConstruct
    public void init() {
        super.initIndex();
    }
}
