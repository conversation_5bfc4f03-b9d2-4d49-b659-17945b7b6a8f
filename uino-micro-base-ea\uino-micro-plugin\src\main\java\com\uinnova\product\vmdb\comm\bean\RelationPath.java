package com.uinnova.product.vmdb.comm.bean;

import com.binary.framework.bean.annotation.Comment;

import java.io.Serializable;
import java.util.List;

/**
 * 关系路径
 * 
 * <AUTHOR>
 * 
 */
@Comment("关系路径")
public class RelationPath implements Serializable {
    private static final long serialVersionUID = 1L;
    @Comment("关系方向0表示A<--B<--C.1表示A-->B-->C")
    private Integer direction;
    @Comment("这条路径上的关系")
    private List<RelationPathEle> paths;

    public Integer getDirection() {
        return direction;
    }

    public void setDirection(Integer direction) {
        this.direction = direction;
    }

    public List<RelationPathEle> getPaths() {
        return paths;
    }

    public void setPaths(List<RelationPathEle> paths) {
        this.paths = paths;
    }

}
