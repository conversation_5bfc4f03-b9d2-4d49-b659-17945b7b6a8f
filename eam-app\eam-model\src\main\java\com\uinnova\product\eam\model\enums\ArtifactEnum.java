package com.uinnova.product.eam.model.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * @description: 制品分类
 * @author: Lc
 * @create: 2022-09-08 16:42
 */
@Getter
public enum ArtifactEnum {

    FREEDOM_DIAGRAM(0, "自由视图"),
    BUSINESS_FLOW(1, "业务流程建模"),
    BUSINESS_ASSEMBLY(2, "业务组件建模"),
    REQUIRE_ANALYSIS(3, "需求关联分析"),
    IT_FRAMEWORK(4, "IT架构设计"),
    OTHER(5, "其他"),
    CONCEPTUAL_ENTITY(6, "数据建模-概念实体关系图"),
    RELATION_ENTITY(7, "数据建模-逻辑实体关系图"),
    SYS_LOGICAL_ENTITY(11,"数据建模-应用级逻辑模型实体ER图"),
    PHYSICAL_ENTITY(12,"数据建模-应用级物理模型实体ER图");



    private Integer artifactType;

    private String artifactDesc;

    ArtifactEnum(Integer artifactType, String artifactDesc) {
        this.artifactType = artifactType;
        this.artifactDesc = artifactDesc;
    }

    public static String getArtifactTypeInfo(Integer artifactType) {
        for (ArtifactEnum artifactEnum : ArtifactEnum.values()) {
            if (Objects.equals(artifactEnum.getArtifactType(), artifactType)) {
                return artifactEnum.getArtifactDesc();
            }
        }
        return null;
    }
}
