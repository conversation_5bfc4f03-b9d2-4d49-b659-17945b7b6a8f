package com.uinnova.product.vmdb.provider.rule.bean;

import com.uinnova.product.vmdb.comm.model.rlt.CcCiRlt;
import com.uinnova.product.vmdb.comm.model.rule.CcRltLine;

import java.util.List;

public class RuleLineInfo {
	private CcRltLine rltLine;
	private RuleNodeInfo sourceNode;
	private RuleNodeInfo targetNode;
	private List<CcCiRlt> ciRlts;

	public RuleLineInfo(CcRltLine rltLine) {
		super();
		this.rltLine = rltLine;
	}

	public CcRltLine getRltLine() {
		return rltLine;
	}

	public void setRltLine(CcRltLine rltLine) {
		this.rltLine = rltLine;
	}

	public RuleNodeInfo getSourceNode() {
		return sourceNode;
	}

	public void setSourceNode(RuleNodeInfo sourceNode) {
		this.sourceNode = sourceNode;
	}

	public RuleNodeInfo getTargetNode() {
		return targetNode;
	}

	public void setTargetNode(RuleNodeInfo targetNode) {
		this.targetNode = targetNode;
	}

	public List<CcCiRlt> getCiRlts() {
		return ciRlts;
	}

	public void setCiRlts(List<CcCiRlt> ciRlts) {
		this.ciRlts = ciRlts;
	}

}
