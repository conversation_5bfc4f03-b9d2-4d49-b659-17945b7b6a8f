package com.uino.api.client.license.local;

import java.util.List;

import com.uino.bean.license.BaseLicenseAuthInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.binary.core.io.Resource;
import com.uinnova.product.vmdb.comm.model.license.CcLicenseAuthServer;
import com.uino.service.license.microservice.ILicenseAuthSvc;
import com.uino.api.client.license.ILicenseAuthApiSvc;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
public class LicenseAuthApiSvcLocal implements ILicenseAuthApiSvc {

    @Autowired
    private ILicenseAuthSvc licenseSvc;

    @Override
    public BaseLicenseAuthInfo queryLicenseAuthInfo() {
        return licenseSvc.queryLicenseAuthInfo();
    }

    @Override
    public List<CcLicenseAuthServer> queryServerList() {
        return licenseSvc.queryServerList();
    }

    @Override
    public Integer removeServer(Long serverId) {
        return licenseSvc.removeServer(serverId);
    }

    @Override
    public String createClientCode() {
        return licenseSvc.createClientCode();
    }

    @Override
    public void registerLicense(String authCode, String authUser) {
        licenseSvc.registerLicense(authCode, authUser);
    }

    @Override
    public void registerLicenseByDb() {
        licenseSvc.registerLicenseByDb();
    }

    @Override
    public Resource getQRCodeImage(Integer width, Integer height) {
        return licenseSvc.getQRCodeImage(width, height);
    }
}
