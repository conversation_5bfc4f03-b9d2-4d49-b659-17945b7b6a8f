package com.uino.bean.event.modeling;

import java.io.Serializable;
import java.util.*;

public class EventModel implements Serializable {

	/**
	 * serialUID.
	 */
	private static final long serialVersionUID = 5628868923874097718L;
	private String name;
	private String desc;
	private String table;
	
	/**
	 * 内存表的表名称
	 */
	private String memoryTable;
	
	/**
	 * 表分区字段
	 */
	private String partitionColumn;

	private Map<String, EventModelAttribute> attributes;

	private final Map<String, String> attrKeys;

	private final Set<String> timeKeys;

	//缓存查询字段，加载model时初始化，减少attribute遍历次数
	private Set<String> redisSearchKeys;

	public EventModel() {
		attributes = new LinkedHashMap<>();
		attrKeys = new HashMap<>();
		timeKeys = new HashSet<>();
		redisSearchKeys = new HashSet<>();
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getDesc() {
		return desc;
	}

	public void setDesc(String desc) {
		this.desc = desc;
	}

	public String getTable() {
		return table;
	}

	public void setTable(String table) {
		this.table = table;
	}

	public String getMemoryTable() {
		return memoryTable;
	}

	public void setMemoryTable(String memoryTable) {
		this.memoryTable = memoryTable;
	}
	public void addAttribute(EventModelAttribute attr) {
		attributes.put(attr.getName().toUpperCase(), attr);
		attrKeys.put(attr.getName().toUpperCase(), attr.getName());
		if(attr.getDataType().equalsIgnoreCase("timestamp")
				|| attr.getDataType().equalsIgnoreCase("datetime")){
			timeKeys.add(attr.getName().toUpperCase());
		}
		if (attr.isRedisSearchKey()) {
			redisSearchKeys.add(attr.getName());
		}
	}

	public EventModelAttribute[] listAttributes() {

		return attributes.values().toArray(new EventModelAttribute[0]);
	}

	public EventModelAttribute getAttribute(String name) {
		if(name == null ) return null;
		return attributes.get(name.toUpperCase());
	}

	public Set<String> getTimeKeys() {
		return timeKeys;
	}

	public Map<String, String> getAttrKeys(){
		return attrKeys;
	}

	public String toString() {

		return "name==" + name + "<====>desc==" + desc + "<====>table=="
				+ table;
	}

	public String getPartitionColumn() {
		return partitionColumn;
	}

	public void setPartitionColumn(String partitionColumn) {
		this.partitionColumn = partitionColumn;
	}

	public Set<String> getRedisSearchKeys() {
		return redisSearchKeys;
	}

	public void setRedisSearchKeys(Set<String> redisSearchKeys) {
		this.redisSearchKeys = redisSearchKeys;
	}
}
