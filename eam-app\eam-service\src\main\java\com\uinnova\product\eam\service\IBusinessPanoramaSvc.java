package com.uinnova.product.eam.service;

import com.alibaba.fastjson.JSONObject;
import com.uinnova.product.eam.base.diagram.model.ESDiagram;
import com.uinnova.product.eam.model.BizNodeDto;
import com.uinnova.product.eam.model.vo.PanoramaCiInfoVo;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uino.bean.cmdb.base.LibType;

import java.util.List;
import java.util.Map;

/**
 * 业务架构全景接口类
 *
 * <AUTHOR>
 * @version 2020/7/28
 */
public interface IBusinessPanoramaSvc {

    /**
     * 业务组件全景矩阵图
     * @param libType
     * @return
     */
    Map<String, Object> getBusMatrixView(LibType libType);

    /**
     * 获取业务系统列表
     *
     * @param libType 库类型
     */
    @Deprecated
    List<BizNodeDto> getBusinessTree(LibType libType);

    String queryPanoramaId(String codeName, LibType libType);

    List<ESDiagram> selectPanoramicWallDiagram(LibType libType, String body);

    String  saveOrUpdatePanoramaDict(JSONObject jsonObj, LibType libType);

    /**
     * 通过数据集获取业务能力全景图信息
     * @param dataSetId 数据集id
     * @return 全景信息
     */
    List<PanoramaCiInfoVo> businessTree(Long dataSetId);

    /**
     * 查询数据集全量ci
     * @param dataSetId 数据集id
     * @return ci全量信息
     */
    List<CcCiInfo> businessCi(Long dataSetId);
}
