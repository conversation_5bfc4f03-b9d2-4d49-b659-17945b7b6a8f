package com.uino.bean.cmdb.query;

import java.io.Serializable;

import com.uino.bean.cmdb.base.ESCITagInfo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
@ApiModel(value="标签搜索类",description = "标签搜索信息")
public class ESTagSearchBean extends ESSearchBase implements Serializable {

    private static final long serialVersionUID = 5720549574293669774L;

    @ApiModelProperty(value="标签信息")
    private ESCITagInfo tagInfo;

}
