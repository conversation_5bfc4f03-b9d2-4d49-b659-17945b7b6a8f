package com.uino.api.client.monitor.rpc;

import java.util.List;
import java.util.Set;

import com.uino.dao.BaseConst;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.binary.core.io.Resource;
import com.uino.bean.cmdb.business.ImportResultMessage;
import com.uino.bean.monitor.base.ESMonSysSeverityInfo;
import com.uino.provider.feign.monitor.MonSeverityFeign;
import com.uino.api.client.monitor.IMonSeverityApiSvc;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
@Primary
public class MonSeverityApiSvcRpc implements IMonSeverityApiSvc {

    @Autowired
    private MonSeverityFeign feign;

    @Override
    public List<ESMonSysSeverityInfo> querySeverityList(String searchVal) {
        return feign.querySeverityList(BaseConst.DEFAULT_DOMAIN_ID, searchVal);
    }

    @Override
    public List<ESMonSysSeverityInfo> querySeverityList(Long domainId, String searchVal) {
        return feign.querySeverityList(domainId, searchVal);
    }

    @Override
    public Long saveOrUpdateSeverity(ESMonSysSeverityInfo saveDto) {
        return feign.saveOrUpdateSeverity(saveDto);
    }

    @Override
    public void deleteServrityByIds(Set<Long> delIds) {
        feign.deleteServrityByIds(delIds);
    }

    @Override
    public Resource exportServrityInfos(Boolean isTpl) {
        return feign.exportSeverityInfos(BaseConst.DEFAULT_DOMAIN_ID, isTpl);
    }

    @Override
    public Resource exportServrityInfos(Long domainId, Boolean isTpl) {
        return feign.exportSeverityInfos(domainId, isTpl);
    }

    @Override
    public ImportResultMessage importSeverityInfos(MultipartFile file) {
        return feign.importSeverityInfos(BaseConst.DEFAULT_DOMAIN_ID, file);
    }

    @Override
    public ImportResultMessage importSeverityInfos(Long domainId, MultipartFile file) {
        return feign.importSeverityInfos(domainId, file);
    }
}
