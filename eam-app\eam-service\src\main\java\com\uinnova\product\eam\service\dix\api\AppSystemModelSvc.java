package com.uinnova.product.eam.service.dix.api;

import com.uinnova.product.eam.model.dix.AppSystemCategoryDTO;
import com.uinnova.product.eam.model.dix.AppSystemConfig;
import com.uinnova.product.eam.model.dix.AppSystemDataDTO;
import com.uino.bean.cmdb.base.ESCIInfo;

import java.util.List;

/**
 * 应用系统对接DDM平台接口
 * <AUTHOR>
 */
public interface AppSystemModelSvc {
    /**
     * 全量系统目录
     * @return 全量系统目录
     */
    List<AppSystemCategoryDTO> getAllSystemCategory();

    /**
     * 获取应用系统配置
     */
    AppSystemConfig getAppConfig();

    /**
     * 根据系统id获取最新实体信息
     * @param systemId 系统id
     * @return 更新结果
     */
    AppSystemDataDTO getModel(String systemId);

    /**
     * 同步应用子系统实体信息
     * @param releaseCiList 发布的ci集合
     */
    void syncAppSystemDataModel(List<ESCIInfo> releaseCiList);

}
