package com.uinnova.product.eam.web.diagram.bean;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

import com.binary.framework.bean.annotation.Comment;

/**
 * 层级分类参数
 * <AUTHOR>
 *
 */
public class LevelClassCdt implements Serializable {

	private static final long serialVersionUID = 1L;
	
	@Comment("层级信息")
	private List<LevelClass> levelClass;
	
	@Comment("分类IDs")
	private List<Long> classIds;
	
	@Comment("关系IDs")
	private List<Long> rltIds;
	
	@Comment("最近发生时间[LASTOCCURRENCE] operate-GTEqual[>=]")
	private Timestamp startLastoccurrence;
	
	@Comment("最近发生时间[LASTOCCURRENCE] operate-LTEqual[<=]")
	private Timestamp endLastoccurrence;


	public List<LevelClass> getLevelClass() {
		return levelClass;
	}

	public void setLevelClass(List<LevelClass> levelClass) {
		this.levelClass = levelClass;
	}

	public List<Long> getClassIds() {
		return classIds;
	}

	public void setClassIds(List<Long> classIds) {
		this.classIds = classIds;
	}

	public List<Long> getRltIds() {
		return rltIds;
	}

	public void setRltIds(List<Long> rltIds) {
		this.rltIds = rltIds;
	}

	public Timestamp getStartLastoccurrence() {
		return startLastoccurrence;
	}

	public void setStartLastoccurrence(Timestamp startLastoccurrence) {
		this.startLastoccurrence = startLastoccurrence;
	}

	public Timestamp getEndLastoccurrence() {
		return endLastoccurrence;
	}

	public void setEndLastoccurrence(Timestamp endLastoccurrence) {
		this.endLastoccurrence = endLastoccurrence;
	}

	
}
