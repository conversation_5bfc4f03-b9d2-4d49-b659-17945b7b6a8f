package com.uinnova.product.vmdb.comm.i18n;

import com.binary.core.i18n.LanguageResolver;
import com.binary.core.i18n.LanguageTranslator;

/**
 * 将{@link PageLanguageTranslator}作为全局默认的语言转换器. 并且提供一个其他模式的转换给{@link I18nMediatorResolver}使用; 具体要翻译国际化的请使用
 * 
 * <AUTHOR>
 *
 */
public class PageI18nMediator implements I18nMediator {

    /**
     * 页面进行语言转化的的配置
     */
    private PageLanguageTranslator pageTranslator;
    private LanguageTranslator i18nTranslator;

    public PageI18nMediator() {
        this(null);
    }

    /**
     * 
     * @param customTranslator
     *            其他模式的转换器
     */
    public PageI18nMediator(LanguageTranslator customTranslator) {
        this.pageTranslator = new PageLanguageTranslator();
        this.i18nTranslator = customTranslator;
        I18nMediatorResolver.setMediator(this);
    }

    @Override
    public LanguageTranslator getCustomTranslator() {
        if (i18nTranslator == null) {
            this.i18nTranslator = LanguageResolver.getTranslator();
        }
        return i18nTranslator;
    }

    @Override
    public LanguageTranslator getGlobalTranslator() {
        return pageTranslator;
    }

}
