package com.uino.api.client.cmdb.rpc;

import java.util.List;
import java.util.Set;

import com.uino.bean.cmdb.business.ClassReOrderDTO;
import com.uino.dao.BaseConst;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.binary.core.io.Resource;
import com.binary.core.io.support.ByteArrayResource;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClass;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uino.bean.cmdb.base.ESCIClassInfo;
import com.uino.provider.feign.cmdb.RltClassFeign;
import com.uino.api.client.cmdb.IRltClassApiSvc;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
public class RltClassApiSvcRpc implements IRltClassApiSvc {

    @Autowired
    private RltClassFeign rltClassSvc;

    @Override
    public Long saveOrUpdate(ESCIClassInfo rltClass) {
        return rltClassSvc.saveOrUpdate(rltClass);
    }

    @Override
    public Integer deleteByIds(Set<Long> rltClassIds) {
        return rltClassSvc.deleteByIds(rltClassIds);
    }

    @Override
    public List<CcCiClassInfo> queryAllClasses() {
        return rltClassSvc.queryAllClasses(BaseConst.DEFAULT_DOMAIN_ID);
    }

    @Override
    public List<CcCiClassInfo> queryAllClasses(Long domainId) {
        return rltClassSvc.queryAllClasses(domainId);
    }

    @Override
    public CcCiClassInfo getRltClassById(Long rltClassId) {
        return rltClassSvc.getRltClassById(rltClassId);
    }

    @Override
    public CcCiClassInfo getRltClassByName(Long domainId, String className) {
        return rltClassSvc.getRltClassByName(domainId, className);
    }

    @Override
    public Resource exportAttrDefs(Set<Long> clsIds) {
        ResponseEntity<byte[]> httpResponse = rltClassSvc.exportAttrDefs(clsIds);
        String fileName = httpResponse.getHeaders().getContentDisposition().getFilename();
        return new ByteArrayResource(httpResponse.getBody(), fileName);
    }

    @Override
    public ESCIClassInfo importAttrDefs(MultipartFile excelFile, Long clsId) {
        return rltClassSvc.importAttrDefs(excelFile, clsId);
    }

    @Override
    public boolean reOrder(ClassReOrderDTO reOrderDTO) {
        return rltClassSvc.reOrder(reOrderDTO);
    }

    @Override
    public boolean initAllOrderNo() {
        return rltClassSvc.initAllOrderNo(BaseConst.DEFAULT_DOMAIN_ID);
    }

    @Override
    public boolean initAllOrderNo(Long domainId) {
        return rltClassSvc.initAllOrderNo(domainId);
    }

    @Override
    public List<CcCiClassInfo> getRltClassByCdt(CCcCiClass cdt) {
        return rltClassSvc.getRltClassByCdt(cdt);
    }
}
