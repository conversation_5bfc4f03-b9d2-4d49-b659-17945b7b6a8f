package com.uinnova.product.vmdb.provider.sys.bean;

import com.binary.core.util.BinaryUtils;
import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.vmdb.comm.i18n.MessageUtil;
import com.uinnova.product.vmdb.comm.sso.LdapUtil;
import com.uinnova.product.vmdb.comm.sso.LdapUtil.Builder;

import java.util.HashMap;
import java.util.Map;

public class SysLoginLdapPropBean {

    public static final SysLoginLdapPropDefs EMPTY_SYS_LOGIN_LDAP_PROP_DEFS = new SysLoginLdapPropDefs();

    public static void refreshLdapProperties(Long id, SysLoginLdapPropBean prop, SysLoginLdapPropDefs defs) {
        MessageUtil.checkEmpty(prop, "connect Ldap Properties");
        MessageUtil.checkEmpty(prop.getHostName(), "connect Ldap Properties 'url'");
        MessageUtil.checkEmpty(prop.getPort(), "connect Ldap Properties 'url'");
        // 兼容老的版本
        // buildLdap(id, prop, defs, userFullDNscript, true);
        buildLdap(id, prop, defs, true);
    }

    public static LdapUtil buildLdap(Long id, SysLoginLdapPropBean prop, SysLoginLdapPropDefs userMapping,
            boolean cache) {
        Builder builder = LdapUtil.newBuilder()
                // 配置连接信息
                .connection(prop.getHostName(), prop.getPort(), prop.getBaseDn(), prop.isUseSsl(), prop.getTimeout())
                // 配置登录Ldap的用户.
                .account(prop.getLoginUserDn(), prop.getPassword());
        // 配置获取用户DN的方式
        String compatibleOldUserFullDNScript = compatibleOldUserFullDNGetter(prop);
        if (!BinaryUtils.isEmpty(compatibleOldUserFullDNScript)) {
            builder.userFullDNByScript(compatibleOldUserFullDNScript);
        } else if (prop.getUserFullDNExpression() != null) {
            builder.userFullDNByExpression(prop.getUserFullDNExpression());
        } else {
            builder.userFullDNByScript(prop.getUserFullDNScript());
        }
        // 配置用户映射的方法
        if (userMapping != null) {
            if (BinaryUtils.isEmpty(userMapping.getScript())) {
                builder.userInfoMapping(toUserAttrMapping(userMapping));
            } else {
                builder.userInfoMappingByScript(userMapping.getScript());
            }
        }
        return cache ? builder.builderToCache(id) : builder.builderTempLdapUtil();
    }

    public static Map<String, String> toUserAttrMapping(SysLoginLdapPropDefs defs) {
        Map<String, String> attrMaps = new HashMap<String, String>();
        if (defs != null) {
            if (defs.getMobileNo() != null) {
                attrMaps.put("mobileNo", defs.getMobileNo());
            }
            if (defs.getRoles() != null) {
                attrMaps.put("role", defs.getRoles());
            }
            if (defs.getNotes() != null) {
                attrMaps.put("notes", defs.getNotes());
            }
            if (defs.getCustom1() != null) {
                attrMaps.put("custom1", defs.getCustom1());
            }
        }
        defs = defs == null ? EMPTY_SYS_LOGIN_LDAP_PROP_DEFS : defs;
        attrMaps.put("loginCode", defs.getLoginCode());
        attrMaps.put("opCode", defs.getOpCode());
        attrMaps.put("shortName", defs.getShortName());
        attrMaps.put("opName", defs.getOpName());
        attrMaps.put("emailAdress", defs.getEmailAdress());
        return attrMaps;
    }

    private static String compatibleOldUserFullDNGetter(SysLoginLdapPropBean prop) {
        StringBuffer sbuf = new StringBuffer();
        if (BinaryUtils.isEmpty(prop.getLoginUserDn()) && !BinaryUtils.isEmpty(prop.getUserNameRdnAttr())) {
            // username这个参数由脚本提供
            sbuf.append("return '").append(prop.getUserNameRdnAttr()).append("=' + username");
            if (!BinaryUtils.isEmpty(prop.getBaseDn())) {
                sbuf.append(" + ',").append(prop.getBaseDn()).append("';");
            }
        } else if (!BinaryUtils.isEmpty(prop.getLoginUserDn()) && !BinaryUtils.isEmpty(prop.getUserNameAttr())) {
            int i1 = prop.getLoginUserDn().indexOf(",");
            String searchDn = prop.getLoginUserDn().substring(i1 + 1, prop.getLoginUserDn().length());
            sbuf.append("ldapInvoker.connect(); ");
            sbuf.append("return ldapInvoker.searchUserDN('").append(searchDn).append("','(")
                    .append(prop.getUserNameAttr()).append("='+").append(" username +')','SUBTREE_SCOPE');");
        }
        return sbuf.toString();
    }
    // ======================= ldap 连接相关的配置start=============================

    @Comment("主机名")
    private String hostName;

    @Comment("端口")
    private String port;

    @Comment("基本DN")
    private String baseDn;

    @Comment("超时时间(毫秒)")
    private Long timeout;

    @Comment("使用ssl")
    private boolean useSsl;

    @Comment("登陆用户")
    private String loginUserDn;

    @Comment("登陆用户密码")
    private String password;
    // ======================= ldap 连接相关的配置end===============================
    //
    //
    // ===================== ldap 配置用户登录使用的DN配置 start ========================

    /**
     * 之前是通过{@linkplain #userFullDNExpression }=用户名,{@linkplain #baseDn }的方式拼接用户DN.
     * 现在应该使用更加自由的方式拼接
     * 
     * @deprecated 请使用 {@linkplain #userFullDNExpression }
     */
    @Deprecated
    @Comment("用户id属性(登陆名)")
    private String userNameAttr;

    @Comment("用户名RDN(相对DN)属性")
    @Deprecated
    private String userNameRdnAttr;

    @Comment("用户显示名属性")
    @Deprecated
    private String displayNameAttr;

    @Comment("用户邮箱属性")
    @Deprecated
    private String mailAttr;

    @Comment("使用表达式的方式生成用户DN")
    private String userFullDNExpression;

    @Comment("使用脚本的方式生成用户的DN")
    private String userFullDNScript;

    // ===================== ldap 配置用户登录使用的DN配置 end ====================
    public String getHostName() {
        return hostName;
    }

    public void setHostName(String hostName) {
        this.hostName = hostName;
    }

    public String getPort() {
        return port;
    }

    public void setPort(String port) {
        this.port = port;
    }

    public boolean isUseSsl() {
        return useSsl;
    }

    public void setUseSsl(boolean useSsl) {
        this.useSsl = useSsl;
    }

    public String getLoginUserDn() {
        return loginUserDn;
    }

    public void setLoginUserDn(String loginUserDn) {
        this.loginUserDn = loginUserDn;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getBaseDn() {
        return baseDn;
    }

    public void setBaseDn(String baseDn) {
        this.baseDn = baseDn;
    }

    public String getUserNameAttr() {
        return userNameAttr;
    }

    public void setUserNameAttr(String userIdAttr) {
        this.userNameAttr = userIdAttr;
    }

    public String getUserNameRdnAttr() {
        return userNameRdnAttr;
    }

    public void setUserNameRdnAttr(String userNameRdnAttr) {
        this.userNameRdnAttr = userNameRdnAttr;
    }

    public String getDisplayNameAttr() {
        return displayNameAttr;
    }

    public void setDisplayNameAttr(String displayNameAttr) {
        this.displayNameAttr = displayNameAttr;
    }

    public String getMailAttr() {
        return mailAttr;
    }

    public void setMailAttr(String mailAttr) {
        this.mailAttr = mailAttr;
    }

    public Long getTimeout() {
        return timeout;
    }

    public void setTimeout(Long timeout) {
        this.timeout = timeout;
    }

    public String getUserFullDNExpression() {
        return userFullDNExpression;
    }

    public void setUserFullDNExpression(String userFullDNExpression) {
        this.userFullDNExpression = userFullDNExpression;
    }

    public String getUserFullDNScript() {
        return userFullDNScript;
    }

    public void setUserFullDNScript(String userFullDNScript) {
        this.userFullDNScript = userFullDNScript;
    }
}
