package com.uinnova.product.eam.web.util;

import com.binary.jdbc.Page;
import com.uinnova.product.eam.web.diagram.bean.AttrInfo;
import com.uinnova.product.eam.web.integration.bean.VcCiInfo;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.provider.search.bean.CcCiClassObj;
import com.uinnova.product.vmdb.provider.search.bean.CcCiObj;
import com.uinnova.product.vmdb.provider.search.bean.CcCiSearchData;
import com.uinnova.product.vmdb.provider.search.bean.CcCiSearchPage;

import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * 存放公用的结果按前端需求格式的工具类
 *
 * <AUTHOR>
 * @version 2020/7/14
 */
public class FormatUtil {

    /**
     * 将ES返回的CI查询结果转换为便于前端解析的格式
     *
     * @param ciInfoPage CI查询结果
     * @return 转换后的信息
     */
    public static Page<VcCiInfo> esCiDtoToCmvDto(CcCiSearchPage ciInfoPage) {
        Page<VcCiInfo> result = new Page<>();
        result.setPageNum(ciInfoPage.getPageNum());
        result.setPageSize(ciInfoPage.getPageSize());
        result.setTotalPages(ciInfoPage.getTotalPages());
        result.setTotalRows(ciInfoPage.getTotalRows());
        List<VcCiInfo> datas = new LinkedList<>();
        CcCiSearchData ciSearchData = ciInfoPage.getData();
        for (CcCiObj searchCiBean : ciSearchData.getRecords()) {
            CcCiClassObj searchClassBean = ciSearchData.getClassMp().get(searchCiBean.getCi().getClassId());
            VcCiInfo addData = new VcCiInfo();
            addData.setCiClass(searchClassBean.getCls());
            addData.setFixMapping(searchClassBean.getFix());
            addData.setCi(searchCiBean.getCi());
            addData.setAttrDefs(searchClassBean.getAttrDefs());
            addData.setAttrsMap(searchCiBean.getAttrs());
            addData.setAttrs(new LinkedList<>());
            if (addData.getAttrDefs() != null && addData.getAttrsMap() != null) {
                Map<String, String> attrMap = addData.getAttrsMap();
                for (CcCiAttrDef attrDef : addData.getAttrDefs()) {
                    AttrInfo addAttr = new AttrInfo();
                    addData.getAttrs().add(addAttr);
                    addAttr.setKey(attrDef.getProName());
                    addAttr.setValue(attrMap.get(attrDef.getProStdName()));
                }
            }
            datas.add(addData);
        }
        result.setData(datas);
        return result;
    }

    /**
     * 将ES返回的CI查询结果转换为便于前端解析的格式，不包含属性定义和分类信息
     *
     * @param ciInfoPage CI查询结果
     * @return 转换后的信息
     */
    public static Page<VcCiInfo> esCiDtoToCmvDtoWithoutAttrDefs(CcCiSearchPage ciInfoPage) {
        Page<VcCiInfo> result = new Page<>();
        result.setPageNum(ciInfoPage.getPageNum());
        result.setPageSize(ciInfoPage.getPageSize());
        result.setTotalPages(ciInfoPage.getTotalPages());
        result.setTotalRows(ciInfoPage.getTotalRows());
        List<VcCiInfo> datas = new LinkedList<>();
        CcCiSearchData ciSearchData = ciInfoPage.getData();
        for (CcCiObj searchCiBean : ciSearchData.getRecords()) {
            CcCiClassObj searchClassBean = ciSearchData.getClassMp().get(searchCiBean.getCi().getClassId());
            VcCiInfo addData = new VcCiInfo();
            addData.setCi(searchCiBean.getCi());
            addData.setAttrs(new LinkedList<>());
            if (searchClassBean.getAttrDefs() != null && searchCiBean.getAttrs() != null) {
                Map<String, String> attrMap = searchCiBean.getAttrs();
                for (CcCiAttrDef attrDef : searchClassBean.getAttrDefs()) {
                    AttrInfo addAttr = new AttrInfo();
                    addData.getAttrs().add(addAttr);
                    addAttr.setKey(attrDef.getProName());
                    addAttr.setValue(attrMap.get(attrDef.getProStdName()));
                }
            }
            datas.add(addData);
        }
        result.setData(datas);
        return result;
    }
}
