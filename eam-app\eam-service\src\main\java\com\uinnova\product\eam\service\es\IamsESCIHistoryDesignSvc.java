package com.uinnova.product.eam.service.es;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.binary.core.util.BinaryUtils;
import com.uinnova.product.eam.model.cj.enums.FormulaTypeEnum;
import com.uinnova.product.eam.service.utils.CalculateUtil;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.comm.util.PropertyType;
import com.uino.bean.cmdb.base.ESCIAttrTransConfig;
import com.uino.bean.cmdb.base.ESCIClassInfo;
import com.uino.bean.cmdb.base.ESCIHistoryInfo;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.dao.AbstractESBaseDao;
import com.uino.dao.ESConst;
import com.uino.dao.cmdb.ESCIAttrTransConfigSvc;
import com.uino.dao.cmdb.ESCIClassSvc;
import com.uino.util.sys.BeanUtil;
import com.uino.util.sys.CheckAttrUtil;
import jakarta.annotation.PostConstruct;
import org.elasticsearch.common.settings.Settings;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.Map.Entry;
import java.util.stream.Collectors;

/**
 * ES-CI历史
 * 
 * <AUTHOR>
 */
@Service
public class IamsESCIHistoryDesignSvc extends AbstractESBaseDao<ESCIHistoryInfo, JSONObject> {

    private static final Logger log = LoggerFactory.getLogger(IamsESCIHistoryDesignSvc.class);

    @Autowired
    @Lazy
    ESCIClassSvc classSvc;

    @Autowired
    private ESCIAttrTransConfigSvc attrConfigSvc;

    @Override
    public String getIndex() {
        return ESConst.INDEX_CMDB_CI_HISTORY + "_design";
    }

    @Override
    public String getType() {
        return ESConst.INDEX_CMDB_CI_HISTORY + "_design";
    }

    @PostConstruct
    public void init() {
        super.initIndex(5);
    }

    @Override
    public Settings getSetting(String analyzerName, int number_of_shards) {
        return Settings.builder()
                .put("number_of_shards", number_of_shards)
                .put("number_of_replicas", 1)
                //修改索引的最大字段，对象管理会自定义字段，超出会报错，这里改为2w个字段，初步推算在500~800个分类
                .put("index.mapping.total_fields.limit",20000)
                .put("index.max_result_window", 1000000)
                // analysis 配置开始
                .put("analysis.normalizer.my_normalizer.type", "custom")
                .putList("analysis.normalizer.my_normalizer.filter", "lowercase")
                .put("analysis.analyzer." + analyzerName + ".type", "custom")
                .put("analysis.analyzer." + analyzerName + ".tokenizer", "standard")
                .putList("analysis.analyzer." + analyzerName + ".filter", "lowercase", "reverse")
                .build();
    }

    @Override
    public Long saveOrUpdate(JSONObject obj, boolean isRefresh) {
        return this.saveOrUpdateWithPrimaryKey(obj, isRefresh, "uniqueCode");
    }

    @Override
    public Map<String, Object> saveOrUpdateBatchMessage(JSONArray list, Boolean isAsync) {
        return this.saveOrUpdateBatchMessageWithPrimaryKey(list, isAsync, "uniqueCode");
    }

    public Long saveOrUpdateHistoryInfo(ESCIInfo ciInfo, ESCIHistoryInfo.ActionType action) {
        if (action == ESCIHistoryInfo.ActionType.SAVE_OR_UPDATE) {
            Map<String, Long> ciCodeMaxVersion = this.getCICodeMaxVersion(Collections.singletonList(ciInfo.getCiCode()));
            // todo 新增CI与历史库ciCode相同时版本号递增？当ciCode相同，所属分类不同时会有问题，还没想好如何避免
            // 此处修改ciInfo对象，CI保存时版本号已更新，无需维护

            /*
            *   设计库历史表中保存的version字段如果根据设计库的version来 在数据导入导出删除再导入时会混乱
            *   这里还是单独维护一下吧 保证生成的历史数据列表顺序唯一且递增
            *
            * */
            Long version = ciCodeMaxVersion.get(ciInfo.getCiCode());
            if (version != null) {
                log.info("########## 历史库ciCode：【{}】，对应历史库最大版本号：【{}】", ciInfo.getCiCode(), version);
                ciInfo.setVersion(version + 1);
            }

        }
        ESCIHistoryInfo historyInfo = this.buildCIHistoryInfo(ciInfo, action);
        return this.saveOrUpdate(historyInfo);
    }

    public Integer saveOrUpdateHistoryInfosBatch(List<ESCIInfo> ciInfos, ESCIHistoryInfo.ActionType action) {
        if (!BinaryUtils.isEmpty(ciInfos)) {
            JSONArray historyInfos = new JSONArray();

            // 这里的批量保存和单个保存的version逻辑相同 历史表的version数据自己维护
            List<String> ciCodes = ciInfos.stream().map(ESCIInfo::getCiCode).collect(Collectors.toList());
            Map<String, Long> ciCodeMaxVersion = this.getCICodeMaxVersion(ciCodes);
            for (ESCIInfo ciInfo : ciInfos) {
                if (action == ESCIHistoryInfo.ActionType.SAVE_OR_UPDATE) {
                    // 新增CI与历史库ciCode相同时版本号递增
                    Long version = ciCodeMaxVersion.get(ciInfo.getCiCode());
                    if (version != null) {
                        log.info("########## 历史库ciCode：【{}】，对应历史库最大版本号：【{}】", ciInfo.getCiCode(), version);
                        ciInfo.setVersion(version + 1);
                    }
                }
                ESCIHistoryInfo historyInfo = this.buildCIHistoryInfo(ciInfo, action);
                String jsonStr = JSON.toJSONString(historyInfo);
                JSONObject json = JSON.parseObject(jsonStr);
                historyInfos.add(json);
            }
            // 这里在发布数据的时候 需要及时获取到最新版本的历史数据 需要做成实时处理
            this.saveOrUpdateBatchMessage(historyInfos, false);
        }
        return 1;
    }

    private ESCIHistoryInfo buildCIHistoryInfo(ESCIInfo esciInfo, ESCIHistoryInfo.ActionType action) {
        action = action == null ? ESCIHistoryInfo.ActionType.SAVE_OR_UPDATE : action;
        ESCIHistoryInfo historyInfo = ESCIHistoryInfo.builder().uniqueCode(esciInfo.getId() + "_" + esciInfo.getVersion()).version(esciInfo.getVersion()).action(action.getValue()).build();
        BeanUtil.copyProperties(esciInfo, historyInfo, "createTime", "modifyTime");
        Map<String, Object> attrs = new HashMap<String, Object>();
        for (Entry<String, Object> entry : historyInfo.getAttrs().entrySet()) {
            String key = entry.getKey();
            Object val = entry.getValue() == null ? "" : entry.getValue();
            if (val instanceof Number) {
                attrs.put(key, new BigDecimal(val.toString()).toString());
            } else if (val instanceof String) {
                attrs.put(key, val);
            } else {
                attrs.put(key, JSON.toJSONString(val));
            }
        }
        historyInfo.setAttrs(attrs);
        return historyInfo;
    }

    public Map<String, Long> getCICodeMaxVersion(List<String> ciCodes) {
        Map<String, Long> res = new HashMap<>();
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        if(!BinaryUtils.isEmpty(ciCodes)){
            query.must(QueryBuilders.termsQuery("ciCode.keyword", ciCodes));
        }
        Map<String, BigDecimal> rltIdMaxVersionMap = super.groupByFieldMaxVal("ciCode.keyword", "version", query);
        if (rltIdMaxVersionMap != null && !rltIdMaxVersionMap.isEmpty()) {
            rltIdMaxVersionMap.forEach((key, val) -> res.put(key, val.longValue()));
        }
        return res;
    }

    public Map<String, Long> getMaxVersionByPrimaryKey(List<String> primaryKeys) {
        Map<String, Long> res = new HashMap<>();
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        if(!BinaryUtils.isEmpty(primaryKeys)){
            query.must(QueryBuilders.termsQuery("ciPrimaryKey.keyword", primaryKeys));
        }
        Map<String, BigDecimal> rltIdMaxVersionMap = super.groupByFieldMaxVal("ciPrimaryKey.keyword", "version", query);
        if (rltIdMaxVersionMap != null && !rltIdMaxVersionMap.isEmpty()) {
            rltIdMaxVersionMap.forEach((key, val) -> res.put(key, val.longValue()));
        }
        return res;
    }

    public ESCIHistoryInfo getByVersion(String ciCode, Long version, boolean transAttr) {
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("ciCode.keyword", ciCode));
        query.must(QueryBuilders.termQuery("publicVersion", version));
        List<ESCIHistoryInfo> ciList = this.getListByQuery(query);
        if(CollectionUtils.isEmpty(ciList)){
            return null;
        }
        if(!transAttr){
            return ciList.get(0);
        }
        this.transCIAttrs(ciList, true);
        return ciList.get(0);
    }

    public void transCIAttrs(List<ESCIHistoryInfo> ciList, boolean changeToShowName) {

        Set<Long> classIds = ciList.stream().map(ESCIHistoryInfo::getClassId).filter(Objects::nonNull).collect(Collectors.toSet());
        List<ESCIClassInfo> ciClassInfos = classSvc.getListByQuery(0, classIds.size(),
                QueryBuilders.boolQuery().must(QueryBuilders.termsQuery("id", classIds))).getData();
        if(CollectionUtils.isEmpty(ciClassInfos)){
            return;
        }

        Map<Long, List<CcCiAttrDef>> clsDefMap = ciClassInfos.stream().collect(
                Collectors.toMap(ESCIClassInfo::getId, ESCIClassInfo::getCcAttrDefs, (k1, k2)->k2));
        List<ESCIAttrTransConfig> attrConfigs = attrConfigSvc.getListByQuery(
                QueryBuilders.boolQuery().must(QueryBuilders.termsQuery("classId", classIds)));
        Map<Long, List<ESCIAttrTransConfig>> attrConfigGroup = new HashMap<>();
        if(!CollectionUtils.isEmpty(attrConfigs)){
            attrConfigGroup = attrConfigs.stream().collect(Collectors.groupingBy(ESCIAttrTransConfig::getClassId));
        }
        for (ESCIHistoryInfo each : ciList) {
            List<CcCiAttrDef> defList = clsDefMap.get(each.getClassId());
            if (defList == null || defList.isEmpty()) {
                throw new RuntimeException("BS_MNAME_CLASS_NOT_EXSIT");
            }
            List<ESCIAttrTransConfig> transConfigs = attrConfigGroup.getOrDefault(each.getClassId(), new ArrayList<>());
            Map<Object, ESCIAttrTransConfig> defConfigMap = transConfigs.stream().collect(Collectors.toMap(ESCIAttrTransConfig::getDefId, e -> e));
            // 属性类型转换
            if (!changeToShowName) {
                each.setAttrs(this.transCIAttrType(each.getAttrs(), defList, transConfigs));
            }
            Map<String, Object> attrs = each.getAttrs();
            Map<String, Object> transAttrs = new HashMap<>();
            for (CcCiAttrDef def : defList) {
                ESCIAttrTransConfig attrConfig = defConfigMap.get(def.getId());
                if (attrConfig != null) {
                    int targetType = attrConfig.getTargetAttrType() != null ? attrConfig.getTargetAttrType() : def.getProType();
                    // 转换为显示名称
                    if (changeToShowName) {
                        // 属性名称转换，修改过属性类型，实际属性值存储在目标字段中
                        String attrName = attrConfig.getTargetAttrName();
                        if (!BinaryUtils.isEmpty(attrName)) {
                            this.transAttrByType(attrName, attrs, attrConfig, targetType, transAttrs);
                        } else {
                            this.transAttrByType(attrConfig.getSourceAttrName().toUpperCase(), attrs, attrConfig, targetType, transAttrs);
                        }
                    } else {
                        // 转换为实际存储名称
                        Object object = attrs.get(attrConfig.getShowName().toUpperCase());
                        String attrName = attrConfig.getTargetAttrName();
                        if (BinaryUtils.isEmpty(attrName)) {
                            attrName = attrConfig.getSourceAttrName().toUpperCase();
                        }
                        transAttrs.put(attrName, object);
                        if (targetType == 7) {
                            transAttrs.put(attrName + "_date", attrs.get(attrConfig.getShowName().toUpperCase() + "_date"));
                        }
                    }
                } else {
                    Object object = attrs.get(def.getProName().toUpperCase());
                    transAttrs.put(def.getProName().toUpperCase(), object);
                    if (!BinaryUtils.isEmpty(object)) {
                        if(changeToShowName){
                            if (def.getProType() == 7) {
                                transAttrs.put(def.getProName().toUpperCase(), attrs.get(def.getProName().toUpperCase().concat("_date")));
                            }else if(def.getProType() == 2){
                                // 丰富ciLabel字段
                                String transVal = object.toString();
                                try {
                                    transVal = new BigDecimal(object.toString()).toPlainString();
                                } catch (Exception e) {
                                    log.error("转换数字异常：{}", object);
                                }
                                transAttrs.put(def.getProName().toUpperCase(), transVal);
                            }
                        }else{
                            if (def.getProType() == 7) {
                                transAttrs.put(def.getProName().toUpperCase() + "_date", attrs.get(def.getProName().toUpperCase().concat("_date")));
                            }
                        }
                    }
                }
            }
            if (changeToShowName) {
                // 丰富ciLabel字段逻辑
                List<CcCiAttrDef> labelDefList = defList.stream().filter(def -> def.getIsCiDisp() != null && def.getIsCiDisp() == 1).collect(Collectors.toList());
                List<String> ciLabels = new ArrayList<>();
                for (CcCiAttrDef labelDef : labelDefList) {
                    ESCIAttrTransConfig esciAttrTransConfig = defConfigMap.get(labelDef.getId());
                    Object object ;
                    if(esciAttrTransConfig!=null){
                        object = transAttrs.get(esciAttrTransConfig.getShowName().toUpperCase());
                    }else {
                        object = transAttrs.get(labelDef.getProName().toUpperCase());
                    }
                    if (!BinaryUtils.isEmpty(object)) {
                        ciLabels.add(object.toString());
                    }
                }
                each.setCiLabel(JSON.toJSONString(ciLabels));
            }
            each.setAttrs(transAttrs);
        }
    }

    private void transAttrByType(String attrName, Map<String, Object> attrs, ESCIAttrTransConfig attrConfig, int type, Map<String, Object> transAttrs){
        Object object = attrs.get(attrName);
        transAttrs.put(attrConfig.getShowName().toUpperCase(), object);
        if (BinaryUtils.isEmpty(object)) {
            return;
        }
        // 转换时间显示格式
        if (type == 7) {
            Object dateStr = attrs.get(attrName.concat("_date"));
            if (dateStr != null) {
                transAttrs.put(attrConfig.getShowName().toUpperCase(), dateStr);
            } else {
                transAttrs.put(attrConfig.getShowName().toUpperCase(), object);
            }
        }
        if (type == 2) {
            String transVal = object.toString();
            try {
                transVal = new BigDecimal(object.toString()).toPlainString();
            } catch (Exception e) {
                log.error("{}转换数字异常：{}", attrConfig.getShowName(), object);
            }
            transAttrs.put(attrConfig.getShowName().toUpperCase(), transVal);
        }
    }

    private Map<String, Object> transCIAttrType(Map<String, Object> attrs, List<CcCiAttrDef> attrDefs, List<ESCIAttrTransConfig> transConfigs) {
        Map<String, Object> esAttr = new HashMap<>();
        if (attrs == null || attrs.isEmpty()) {
            return esAttr;
        }
        Map<Long, ESCIAttrTransConfig> attrConfigMap = BinaryUtils.toObjectMap(transConfigs, "defId");
        for (CcCiAttrDef def : attrDefs) {
            ESCIAttrTransConfig attrConfig = attrConfigMap.get(def.getId());
            // 通过中间表映射属性
            if (!BinaryUtils.isEmpty(attrConfig)) {
                def.setProName(attrConfig.getShowName());
                def.setProStdName(attrConfig.getShowName().toUpperCase());
                if (attrConfig.getUpType() > 1) {
                    def.setProType(attrConfig.getTargetAttrType());
                }
            }
        }
        Map<String, CcCiAttrDef> defMap = BinaryUtils.toObjectMap(attrDefs, "proStdName");
        for (Entry<String, Object> entry : attrs.entrySet()) {
            String key = entry.getKey();
            CcCiAttrDef def = defMap.get(key);
            if (BinaryUtils.isEmpty(def)) {
                continue;
            }
            Integer proType = def.getProType();
            ESCIAttrTransConfig transConfig = attrConfigMap.get(def.getId());
            if (transConfig != null && transConfig.getTargetAttrType() != null) {
                proType = transConfig.getTargetAttrType();
            }
            PropertyType type = PropertyType.valueOf(proType);
            if (BinaryUtils.isEmpty(entry.getValue()) && !PropertyType.CALCULATE.equals(type)) {
                continue;
            }
            String value = String.valueOf(entry.getValue());
            // CI保存前已校验过属性，此处可以直接转换类型
            switch (type) {
                case INTEGER: {
                    esAttr.put(key, Long.valueOf(value));
                    break;
                }
                case DOUBLE: {
                    esAttr.put(key, Double.valueOf(value));
                    break;
                }
                case ENUM:
                case VARCHAR:
                case LONG_VARCHAR:
                case DICT:
                case ATTACHMENT:
                case PERSION:
                case CLOB:
                case EXTERNAL_ATTR: {
                    esAttr.put(key, value);
                    break;
                }
                case DATE: {
                    // 日期类型目前未做校验，先按字符串存储
                    Date date = CheckAttrUtil.getDate(value, def.getConstraintRule());
                    if (date != null) {
                        esAttr.put(key, date.getTime());
                    } else {
                        log.error("[{}]转换为时间类型出错！", value);
                    }
                    // 额外存储日期字符串，用于查询
                    esAttr.put(key + "_date", value);
                    break;
                }
                case CALCULATE: {
                    String formula = defMap.get(key).getConstraintRule();
                    String formulaType = FormulaTypeEnum.CALCULATE.getType();
                    BigDecimal score = CalculateUtil.calculate(formula, attrs, formulaType, defMap, attrConfigMap);
                    esAttr.put(key, score);
                    attrs.put(key, score);
                    break;
                }
                default: {
                    esAttr.put(key, value);
                }
            }
        }
        return esAttr;
    }
}
