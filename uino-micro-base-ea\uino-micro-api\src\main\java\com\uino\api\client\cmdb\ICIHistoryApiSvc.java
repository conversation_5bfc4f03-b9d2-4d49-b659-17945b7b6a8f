package com.uino.api.client.cmdb;

import java.util.List;
import java.util.Map;

import com.uino.bean.cmdb.base.ESCIHistoryInfo;

public interface ICIHistoryApiSvc {


    void delAll(List<Long> classIds);

    /**
     * 根据CI的id查询CI历史版本与时间对应关系
     * 
     * @param ciCode
     * @param classId
     * @return
     */
    public List<String> getCIVersionList(String ciCode, Long classId);

    /**
     * 根据id及版本号获取对应版本CI数据
     * 
     * @param ciCode
     * @param classId
     * @param version
     * @return
     */
    public ESCIHistoryInfo getCIInfoHistoryByCIVersion(String ciCode, Long classId, Long version);


    public List<ESCIHistoryInfo> bathGetCIInfoHistoryByCIVersion(Map<String,Long> ciCodeVersionMap);
}
