package com.uino.service.cmdb.microservice;

import com.binary.core.exception.BinaryException;
import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.comm.model.ci.CCcCi;
import com.uinnova.product.vmdb.comm.model.ci.CcCi;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CiClassSaveInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CiGroupPage;
import com.uinnova.product.vmdb.provider.ci.bean.CiQueryCdt;
import com.uinnova.product.vmdb.provider.search.bean.CcCiSearchPage;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.base.LibType;
import com.uino.bean.cmdb.base.SaveBatchCIContext;
import com.uino.bean.cmdb.base.SaveType;
import com.uino.bean.cmdb.business.*;
import com.uino.bean.cmdb.query.ESAttrAggBean;
import com.uino.bean.cmdb.query.ESCISearchBean;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.search.sort.SortBuilder;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface ICISvc {

    /**
     * 根据id查询CI信息
     *
     * @param id
     * @return
     */
    CcCiInfo getCiInfoById(Long id);

    /**
     * 根据ciCode查询CI信息
     *
     * @param ciCode
     * @return
     */
    CcCiInfo getCiInfoByCiCode(String ciCode,String ownerCode);

    /**
     * 按条件分页查询CI
     *
     * @param pageNum
     * @param pageSize
     * @param cdt
     * @param hasClass
     * @return
     */
    CiGroupPage queryPageByIndex(Long domainId, Integer pageNum, Integer pageSize, CiQueryCdt cdt, Boolean hasClass);

    /**
     * 按条件分页查询CI-支持属性排序
     *
     * @param bean
     * @param hasClass
     * @return
     */
    CiGroupPage queryPageBySearchBean(ESCISearchBean bean, Boolean hasClass);

    /**
     * 条件查询CI
     *
     * @param domainId
     * @param cdt
     * @param orders   排序字段
     * @param isAsc    是否升序
     * @return
     */
    List<CcCi> queryCiList(Long domainId, CCcCi cdt, String orders, Boolean isAsc);

    /**
     * 条件查询CI
     *
     * @param domainId
     * @param cdt
     * @param orders   排序字段
     * @param isAsc    是否升序 * @param hasClass 是否查询CiClass信息
     * @return
     */
    List<ESCIInfo> queryESCIInfoList(Long domainId, CCcCi cdt, String orders, Boolean isAsc);

    /**
     * 条件查询CI
     *
     * @param domainId
     * @param cdt
     * @param orders   排序字段
     * @param isAsc    是否升序 * @param hasClass 是否查询CiClass信息
     * @return
     */
    List<CcCiInfo> queryCiInfoList(Long domainId, CCcCi cdt, String orders, Boolean isAsc, Boolean hasClass);

    /**
     * 分页查询CI
     *
     * @param domainId
     * @param pageNum
     * @param pageSize
     * @param cdt
     * @param orders   排序字段
     * @param isAsc    是否升序
     * @param hasClass 是否查询CiClass信息
     * @return
     */
    Page<CcCiInfo> queryCiInfoPage(Long domainId, Integer pageNum, Integer pageSize, CCcCi cdt, String orders,
                                   Boolean isAsc, Boolean hasClass);

    /**
     * <b>根据cdt对象查询分类
     *
     * @param pageNum
     * @param pageSize
     * @param bean     自动生成的分类查询对象
     * @return
     */
    CcCiSearchPage searchCIByCdt(int pageNum, int pageSize, CCcCi bean);

    /**
     * es分页查询ci接口支持<br/>
     * 1、模糊匹配<br/>
     * 2、根据指定属性查询(and /or )<br/>
     * 3、ciCode/ciId/hashcode筛选<br/>
     * 4、classIds筛选<br/>
     * 5、tagIds筛选<br/>
     * 6、默认根据匹配度排序<br/>
     * 7、排序字段 如果是字符串类型必须加keyword
     *
     * @param bean 查询BEAN，定义了各个字段之间的关系
     * @return
     */
    Page<ESCIInfo> searchESCIByBean(ESCISearchBean bean);

    /**
     * @param bean
     * @return
     * @see ICISvc#searchESCIByBean
     * <p>
     * 在ICISvc#searchESCIByBean基础上丰富了信息
     */
    CcCiSearchPage searchCIByBean(ESCISearchBean bean);

    /**
     * 保存或更新CI
     *
     * @param ciInfo
     * @return
     */
    Long saveOrUpdate(CcCiInfo ciInfo);

    /**
     * 保存或更新CI ---- Extra
     *
     * @param ciInfo
     * @return
     */
    Long saveOrUpdate(CcCiInfo ciInfo, SaveType saveType);

    /**
     * 保存或更新CI
     *
     * @param ciInfo
     * @return
     */
    Long saveOrUpdateExtra(CcCiInfo ciInfo);

    /**
     * dcv特有，不允许别人调用，否则可能有严重问题，只更改dcvExt信息，不修改CI本身数据
     *
     * @param esCiInfoList
     * @return
     */
    Integer updateESCIInfoBatch(List<ESCIInfo> esCiInfoList);

    /**
     * 根据id删除CI
     *
     * @param id
     * @param sourceId 来源id,页面=1;CP=2;DIX=3
     * @return
     */
    Integer deleteById(Long id, Long sourceId);

    /**
     * 根据ids批量删除CI
     *
     * @param ciIds
     * @param sourceId 来源id,页面=1;CP=2;DIX=3
     * @return
     */
    Integer removeByIds(List<Long> ciIds, Long sourceId);


    Integer removeByPrimaryKeys(Long domainId, List<String> ciPrimaryKeys, Long sourceId);

    /**
     * 根据分类id删除CI
     *
     * @param classId
     * @param sourceId 来源id,页面=1;CP=2;DIX=3
     * @return
     */
    Integer removeByClassId(Long classId, Long sourceId);

    /**
     * 批量保存CI
     *
     * @param domainId
     * @param saveInfo
     * @return
     */
    ImportSheetMessage saveOrUpdateCiBath(Long domainId, CiClassSaveInfo saveInfo);

    /**
     * 根据条件导出CI或分类属性
     *
     * @param exportDto
     * @return
     */
    ResponseEntity<byte[]> exportCiOrClass(ExportCiDto exportDto);

    /**
     * 根据分类id导入CI，classIds为空默认导入所有
     *
     * @param file
     * @param classId
     * @return
     */
    ImportResultMessage importCiByCiClsIds(MultipartFile file, Long classId);

    /**
     * 一键导入上传Excel文件
     *
     * @param file
     * @return
     */
    ImportExcelMessage importCiExcel(MultipartFile file);

    /**
     * 一键导入批量导入CI
     *
     * @param excelInfoDto
     * @return
     */
//    ImportResultMessage importCiByClassBatch(CiExcelInfoDto excelInfoDto);
    ImportResultMessage importCiByClassBatch(Long domainId, CiExcelInfoDto excelInfoDto, boolean addAttr);

    /**
     * 统计ci数量汇总至分类，支持对ci条件筛选
     *
     * @param bean 筛选条件
     * @return {分类Id:ci数量}
     */
    Map<Long, Long> countCiNumGroupClsByQuery(ESCISearchBean bean);

    /**
     * 根据条件count
     *
     * @param bean
     * @return
     */
    Long countByQuery(ESCISearchBean bean);

    /**
     * 获取属性值列表
     *
     * @param searchBean
     * @return
     */
    Page<String> getAttrValuesBySearchBean(ESAttrAggBean searchBean);

    /**
     * 根据业务主键查询CI
     *
     * @param ciPrimaryKeys
     * @return
     */
    List<CcCiInfo> getCIInfoListByCIPrimaryKeys(Long domainId, List<List<String>> ciPrimaryKeys);

    /**
     * 根据业务主键查询CI
     *
     * @param ciPrimaryKeys
     * @return
     */
    List<ESCIInfo> getESCIInfoListByCIPrimaryKeys(Long domainId, List<List<String>> ciPrimaryKeys);

    /**
     * 条件查询CI信息-提供原生query查询接口,根据属性查询时需进行属性转换(属性实际存储名称与显示名称不同)
     * <b>结合classSvc.getTargetAttrDefsByClassIds(classIds)方法转换属性后使用，排序字段也许转换后排序
     *
     * @param pageNum
     * @param pageSize
     * @param query
     * @param sorts
     * @param isHighLight
     * @return
     */
    Page<ESCIInfo> getESCIInfoPageByQuery(Long domainId, int pageNum, int pageSize, QueryBuilder query, List<SortBuilder<?>> sorts, Boolean isHighLight);


    /**
     * 删除所有的的CI
     *
     * @param query
     * @return
     */
    Integer removeAllCI(Long domainId, QueryBuilder query);


    /**
     * 批量修改对象属性,选择本页数据或全部数据
     *
     * @param dto
     * @return
     */
    boolean modifyAttrValueBatch(CIAttrValueUpdateDto dto);

    /**
     * 批量删除对象,选择本页数据或全部数据
     *
     * @param dto
     * @return
     */
    Integer removeCiBatch(CIRemoveBatchDto dto);

    /**
     * 批量修改对象属性
     *
     * @param dto
     * @return
     */
    public boolean updateAttrValueBatch(CIAttrValueUpdateDto dto);


    Map<String, Long> queryCiCountByClassId();

    /**
     * 查询ci对象分页列表
     *
     * @return
     */
    Page<ESCIInfo> queryCiInfoPage(int pageNum, int pageSize, QueryBuilder query, String sortField, boolean isAsc);

    Integer removeByOwnerCodeAndClassId(Long classId, String ownerCode);

    /* List<CcCiInfo> saveOrUpdateBatchCI(List<CcCiInfo> ciList, List<Long> classIds, String ownerCode, SysUser loginUser);*/
    Map<String, ? extends SaveBatchCIContext> saveOrUpdateBatchCI(List<ESCIInfo> ciList, List<Long> classIds, String ownerCode, String loginCode);

    Map<String, ? extends SaveBatchCIContext> copyCiListByIds(List<ESCIInfo> ciList, String ownerCode);

    default Map<String, ? extends SaveBatchCIContext> extendCopyCiListByIds(List<ESCIInfo> ciList, String ownerCode, String postfix) {
        throw new BinaryException("当前方法仅在私有库定制实现");
    }
}
