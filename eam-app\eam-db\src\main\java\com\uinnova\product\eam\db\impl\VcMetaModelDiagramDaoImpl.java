package com.uinnova.product.eam.db.impl;


import com.uinnova.product.eam.comm.model.CVcMetaModelDiagram;
import com.uinnova.product.eam.comm.model.VcMetaModelDiagram;
import com.uinnova.product.eam.db.VcMetaModelDiagramDao;
import com.uinnova.product.eam.db.support.dao.mybatis.ComMyBatisBinaryDaoImpl;


/**
 * 元模型管理视图[VC_META_MODEL_DIAGRAM]数据访问对象实现
 */
public class VcMetaModelDiagramDaoImpl extends ComMyBatisBinaryDaoImpl<VcMetaModelDiagram, CVcMetaModelDiagram> implements VcMetaModelDiagramDao {

//    @Override
//    public String getTableName() {
//        return "IAMS." + getDaoDefinition().getTableName();
//    }
}


