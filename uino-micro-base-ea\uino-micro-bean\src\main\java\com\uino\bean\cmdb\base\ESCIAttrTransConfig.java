package com.uino.bean.cmdb.base;

import java.io.Serializable;

import com.binary.framework.bean.annotation.Comment;
import com.uino.bean.permission.business.IValidDto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 
 * <AUTHOR>
 *
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ESCIAttrTransConfig implements Serializable, IValidDto {

    @Comment("id")
    private Long id;

    @Comment("分类id")
    private Long classId;

    @Comment("属性id")
    private Long defId;

    @Comment("原属性名称")
    private String sourceAttrName;

    @Comment("原属性类型")
    private Integer sourceAttrType;

    @Comment("目标属性名称")
    private String targetAttrName;

    @Comment("目标属性类型")
    private Integer targetAttrType;

    @Comment("显示名称")
    private String showName;

    @Comment("更改类型 1：名称 ，2：类型")
    private Integer upType;

    @Comment("所属域")
    private Long domainId;
    /**
     * 
     */
    private static final long serialVersionUID = -3475810212967468774L;

    @Override
    public void valid() {

    }

}
