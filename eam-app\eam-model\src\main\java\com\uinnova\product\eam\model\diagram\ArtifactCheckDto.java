package com.uinnova.product.eam.model.diagram;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.io.Serializable;

/**
 * 视图合规性校验
 * <AUTHOR>
 */
@Data
public class ArtifactCheckDto implements Serializable {
    @Comment("关系节点key")
    private String key;
    @Comment("关系名称")
    private String name;
    @Comment("关系code")
    private String uniqueCode;
    @Comment("源端对象名称")
    private String sourceName;
    @Comment("目标端对象名称")
    private String targetName;
}
