package com.uino.service.cmdb.dataset.microservice.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.binary.core.exception.MessageException;
import com.binary.core.util.BinaryUtils;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClass;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CiGroupPage;
import com.uino.bean.cmdb.base.dataset.DataSetMallApi;
import com.uino.bean.cmdb.base.dataset.log.DataSetMallApiLog;
import com.uino.bean.cmdb.query.ESCISearchBean;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.permission.query.CSysUser;
import com.uino.dao.cmdb.dataset.ESDataSetMallApiLogSvc;
import com.uino.dao.util.ESUtil;
import com.uino.service.cmdb.dataset.microservice.IMallApiSvc;
import com.uino.service.cmdb.microservice.ICIClassSvc;
import com.uino.service.cmdb.microservice.ICISvc;
import com.uino.service.permission.microservice.IUserSvc;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

/**
 * 根据分类查ci
 *
 * @Date 2020/4/2 15:08
 * @Created by sh
 */
@Service
@Slf4j
public class MallApiClassSvc implements IMallApiSvc {

    @Autowired
    private ICISvc iciSvc;

    @Autowired
    private ICIClassSvc iciClassSvc;

    @Autowired
    private ESDataSetMallApiLogSvc esDataSetMallApiLogSvc;

    @Autowired
    private IUserSvc userSvc;

    @Override
    public DataSetMallApi checkCharacteristic(SysUser user, JSONObject jsonObject) {
        return null;
    }

    @Override
    public JSONObject execute(DataSetMallApi dataSetMallApi, JSONObject jsonObject) {
        Long domainId = dataSetMallApi.getDomainId();
        long startTime = System.currentTimeMillis();

        //2021-08-18 增加apikey校验
        String authentication = jsonObject.getString("authentication");
        String oauthAppName = jsonObject.getString("oauthAppName");

        String username = jsonObject.getString("username");
        String password = jsonObject.getString("password");
        //考虑code代码的查询，所以此处用了name
        List<String> ciClassNames = jsonObject.getJSONArray("ciClassNames").toJavaList(String.class);
        Integer pageNum = jsonObject.getInteger("pageNum");
        Integer pageSize = jsonObject.getInteger("pageSize");
        int ciTotal = 0;
        boolean isSuccess = true;
        try {
            //2021-08-18 增加apikey校验 记录调用系统名称
            if (!BinaryUtils.isEmpty(authentication)) {
                username = oauthAppName;
            } else {
                // Base64解密
                password = new String(Base64.decodeBase64(password.getBytes(StandardCharsets.UTF_8)), StandardCharsets.UTF_8);
                password = DigestUtils.sha256Hex(password);
                //todo: 用户名密码直接查询判断，没有通过登陆
                CSysUser cSysUser = new CSysUser();
                cSysUser.setDomainId(domainId);
                cSysUser.setLoginCodeEqual(username);
                List<SysUser> listByCdt = userSvc.getSysUserByCdt(cSysUser);
                if (listByCdt == null || listByCdt.isEmpty()) {
                    throw MessageException.i18n("DCV_USERNAME_ERROR");
                } else if (!listByCdt.get(0).getLoginPasswd().equals(password)) {
                    throw MessageException.i18n("DCV_PASSWORD_ERROR");
                }
            }

            CCcCiClass cCcCiClass = new CCcCiClass();
            cCcCiClass.setDomainId(domainId);
            cCcCiClass.setClassNames(ciClassNames.toArray(new String[0]));
            List<CcCiClassInfo> ccCiClassInfos = iciClassSvc.queryClassByCdt(cCcCiClass);
            List<Long> classIds = new ArrayList<>();
            if (ccCiClassInfos != null && !ccCiClassInfos.isEmpty()) {
                ccCiClassInfos.forEach(ccCiClassInfo -> classIds.add(ccCiClassInfo.getCiClass().getId()));
            } else {
                throw MessageException.i18n("BS_MNAME_CLASS_NOT_EXSIT");
            }
            ESCISearchBean bean = new ESCISearchBean();
            bean.setDomainId(domainId);
            bean.setPageSize(pageSize);
            bean.setPageNum(pageNum);
            bean.setClassIds(classIds);

            CiGroupPage ciGroupPage = iciSvc.queryPageBySearchBean(bean, false);
            ciTotal = ciGroupPage.getData().size();
            return JSONObject.parseObject(JSONObject.toJSONString(ciGroupPage));
        } catch (MessageException e) {
            log.error("", e);
            isSuccess = false;
            throw e;
        } finally {
            //保存日志
            DataSetMallApiLog dataSetMallApiLog = new DataSetMallApiLog();
            dataSetMallApiLog.setId(ESUtil.getUUID());
            dataSetMallApiLog.setDomainId(domainId);
            dataSetMallApiLog.setDataSetMallApiId(dataSetMallApi.getId());
            dataSetMallApiLog.setDataSetType(dataSetMallApi.getType());
            dataSetMallApiLog.setSuccess(isSuccess);
            dataSetMallApiLog.setRespUserCode(username);
            dataSetMallApiLog.setRespDuration(System.currentTimeMillis() - startTime);
            dataSetMallApiLog.setCreateTime(System.currentTimeMillis());
            dataSetMallApiLog.setCiTotal(ciTotal);
            dataSetMallApiLog.setRelTotal(0);
            esDataSetMallApiLogSvc.saveOrUpdate(dataSetMallApiLog.toJson(), true);
        }
    }

    @Override
    public JSONObject realTimeExecute(DataSetMallApi dataSetMallApi, JSONObject jsonObject) {
        Long domainId = dataSetMallApi.getDomainId();
        long startTime = System.currentTimeMillis();
        String username = jsonObject.getString("username");
        String password = jsonObject.getString("password");
        JSONArray ci = jsonObject.getJSONArray("ci");
        int ciTotal = 0;
        boolean isSuccess = true;
        try {
            // Base64解密
            password = new String(Base64.decodeBase64(password.getBytes("utf-8")), "utf-8");
            password = DigestUtils.sha256Hex(password);
            //todo: 用户名密码直接查询判断，没有通过登陆
            CSysUser cSysUser = new CSysUser();
            cSysUser.setDomainId(domainId);
            cSysUser.setLoginCodeEqual(username);
            List<SysUser> listByCdt = userSvc.getSysUserByCdt(cSysUser);
            if (listByCdt == null || listByCdt.isEmpty()) {
                throw MessageException.i18n("DCV_USERNAME_ERROR");
            } else if (!listByCdt.get(0).getLoginPasswd().equals(password)) {
                throw MessageException.i18n("DCV_PASSWORD_ERROR");
            }

            List<List<String>> ciPrimaryKeys = new ArrayList<List<String>>();
            List<String> pk = new ArrayList<String>();
            for (int i = 0; i < ci.size(); i++) {
                pk.add(ci.getString(i));
            }
            ciPrimaryKeys.add(pk);
            //todo:
            List<CcCiInfo> ciInfos = iciSvc.getCIInfoListByCIPrimaryKeys(domainId, ciPrimaryKeys);
            if (ciInfos == null || ciInfos.size() == 0) {
                //ci不存在
                throw MessageException.i18n("BS_CI_NO_EXIST");
            } else {
                ciTotal = 1;
                return JSONObject.parseObject(JSONObject.toJSONString(ciInfos.get(0)));
            }
        } catch (Exception e) {
            isSuccess = false;
            log.error("", e);
            throw new RuntimeException(e.getMessage());
        } finally {
            //保存日志
            DataSetMallApiLog dataSetMallApiLog = new DataSetMallApiLog();
            dataSetMallApiLog.setId(ESUtil.getUUID());
            dataSetMallApiLog.setDomainId(domainId);
            dataSetMallApiLog.setDataSetMallApiId(dataSetMallApi.getId());
            dataSetMallApiLog.setDataSetType(dataSetMallApi.getType());
            dataSetMallApiLog.setSuccess(isSuccess);
            dataSetMallApiLog.setRespUserCode(username);
            dataSetMallApiLog.setRespDuration(System.currentTimeMillis() - startTime);
            dataSetMallApiLog.setCreateTime(System.currentTimeMillis());
            dataSetMallApiLog.setCiTotal(ciTotal);
            dataSetMallApiLog.setRelTotal(0);
            esDataSetMallApiLogSvc.saveOrUpdate(dataSetMallApiLog.toJson(), true);
        }
    }
}
