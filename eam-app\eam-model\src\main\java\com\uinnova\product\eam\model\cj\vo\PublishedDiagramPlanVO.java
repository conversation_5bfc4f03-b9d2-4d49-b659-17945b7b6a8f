package com.uinnova.product.eam.model.cj.vo;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.base.diagram.model.ESDiagram;
import com.uinnova.product.eam.base.diagram.model.ESDiagramShareRecordResult;
import com.uinnova.product.eam.comm.model.es.EamMatrixInstance;
import com.uinnova.product.eam.model.asset.AssetCiDetailInfoDTO;
import com.uinnova.product.eam.model.cj.domain.PlanDesignInstance;
import com.uinnova.product.eam.model.enums.AssetType;
import com.uino.bean.permission.base.SysUser;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Classname PublishedDiagramPlanVO
 * @Date 2022/3/21 16:09
 */
@Data
public class PublishedDiagramPlanVO {

    @Comment("制品")
    private ESDiagram diagram;

    @Comment("视图作者信息")
    private SysUser creator;

    @Comment("视图分享记录")
    private List<ESDiagramShareRecordResult> shareRecords;

    @Comment("方案")
    private PlanDesignInstance planDesignInstance;

    @Comment("资产Ci")
    private AssetCiDetailInfoDTO assetCiDetailInfoDTO;

    @Comment("矩阵")
    private EamMatrixInstance eamMatrixInstance;
    //TODO attentinType查询字段中ci资产为4 这里为3  所以为了保险起见统一设置为5
    @Comment("类型 1:制品 2:方案 3:ci资产 5:矩阵")
    @Deprecated
    private Integer type;

    @Comment("用作前端区分资源类型")
    private AssetType assetType;

    @Comment("修改时间")
    private Long modifyTime;

    @Comment("关注的时间")
    private Long attentionTime;

    @Comment("/最近查看时间")
    private Long viewTime;

    @Comment("方案是否被关注")
    private Integer isAttention;

    @Comment("关注来源，1：设计空间，2：资产仓库")
    private Integer attentionSource;
    @Comment("转换的最近查看时间")
    private String conversionViewTime;

    @Comment("转换的关注时间")
    private String conversionAttentionTime;

    @Comment("视图或方案的更新时间")
    private Long updateTime;

    @Comment("视图或方案的转换的更新时间")
    private String conversionUpdate;
}
