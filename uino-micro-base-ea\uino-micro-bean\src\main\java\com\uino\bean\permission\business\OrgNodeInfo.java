package com.uino.bean.permission.business;

import java.io.Serializable;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

import com.uino.bean.permission.base.SysOrg;
import com.uino.bean.permission.base.SysUser;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Tolerate;

/**
 * 组织tree信息
 * 
 * <AUTHOR>
 */
@Getter
@Setter
@Builder
@ApiModel(value="组织tree信息",description = "组织tree信息")
public class OrgNodeInfo implements Serializable {
	private static final long serialVersionUID = 1L;

	@Tolerate
	public OrgNodeInfo() {
		this.nodeId = UUID.randomUUID().toString();
		this.typeOrg = true;
		this.children = new LinkedList<>();
		this.childrenNameMap = new LinkedHashMap<>();
	}

	/**
	 * 组织信息
	 */
	@ApiModelProperty(value="组织信息")
	private SysOrg orgInfo;
	/**
	 * 用户信息
	 */
	@ApiModelProperty("用户信息")
	private SysUser userInfo;
	/**
	 * 节点类型是否未组织
	 */
	@Builder.Default
	@ApiModelProperty("节点类型是否未组织")
	private boolean typeOrg = true;
	/**
	 * 每个节点的唯一标识，与id含义一致
	 */
	@Builder.Default
	@ApiModelProperty("每个节点的唯一标识，与id含义一致")
	private String nodeId = UUID.randomUUID().toString();
	/**
	 * 节点名称
	 */
	@ApiModelProperty(value="节点名称")
	private String nodeName;
	/**
	 * 真实数据id
	 */
	@ApiModelProperty(value="真实数据id")
	private Long dataId;
	/**
	 * 子节点
	 */
	@ApiModelProperty(value="子节点")
	@Builder.Default
	private List<OrgNodeInfo> children = new LinkedList<>();

	/**
	 * map结构子节点
	 */
	@Builder.Default
	@ApiModelProperty(value="map结构子节点")
	private Map<String, OrgNodeInfo> childrenNameMap = new LinkedHashMap<>();

	/**
	 * 组织下用户数量(只是本级)
	 */
	@ApiModelProperty(value="组织下用户数量(只是本级)")
	private int userCount;

	/**
	 * 组织下用户ids
	 */
	@Builder.Default
	@ApiModelProperty(value="组织下用户id集合")
	private Set<Long> userIds = new HashSet<>();
	
	/**
	 * 是否含有子组织
	 */
	@ApiModelProperty(value="是否含有子组织",example = "true")
	private Boolean hasChilds;
	
	/**
	 * 是否全量返回
	 */
	@ApiModelProperty(value="是否全量返回",example = "true")
	private Boolean isTotal;

	public void addUserIds(Long userId) {
		this.getUserIds().add(userId);
		this.setUserCount(this.getUserIds().size());
	}

	public void addAllUserIds(Set<Long> userIds) {
		this.getUserIds().addAll(userIds);
		this.setUserCount(this.getUserIds().size());
	}

	public static void countAllOrgHasUserCount(OrgNodeInfo node) {
		if (node.isTypeOrg()) {
			countOrgHasUserCount(node, node);
			node.getChildren().forEach(nextNode -> {
				if (nextNode.isTypeOrg()) {
					countAllOrgHasUserCount(nextNode);
				}
			});
		}
	}

	public static void countOrgHasUserCount(OrgNodeInfo node, OrgNodeInfo currentNode) {
		node.addAllUserIds(currentNode.getUserIds());
		for (OrgNodeInfo childNode : currentNode.getChildren()) {
			if (childNode.typeOrg) {
				node.addAllUserIds(childNode.getUserIds());
				countOrgHasUserCount(node, childNode);
			}
		}
	}

}
