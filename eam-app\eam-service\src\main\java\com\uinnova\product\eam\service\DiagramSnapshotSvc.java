package com.uinnova.product.eam.service;


import com.uinnova.product.eam.model.asset.EamCiRltDTO;
import com.uinnova.product.eam.model.dto.DiagramSnapshotVO;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uino.bean.permission.base.SysUser;

import java.util.List;
import java.util.Map;

/**
 *  视图历史版本相关接口
 */
public interface DiagramSnapshotSvc {

    /**
     *  根据视图ID生成视图内CI/RLT的私有历史库的数据 ***功能暂时仅覆盖私有库视图
     * @param diagramSnapshotVOS 私有库视图与生成的快照名称 集合
     * @param currentUserInfo 当前登录人操作人信息
     * @param isAuto true - 自动版本  false - 手动版本
     * @return 返回map <原私有库视图加密ID,新生成的快照视图加密ID>
     */
    Map<String, String> generateSnapshotByDiagramIds(List<DiagramSnapshotVO> diagramSnapshotVOS, SysUser currentUserInfo, Boolean isAuto);

    /**
     *  根据快照（历史数据）IDS 查询快照内的CI数据
     * @param hisDiagramIds 视图快照（历史）IDS
     * @param ciCode 快照视图内的 ciCode 非必传
     * @return DIAGRAM_ID 与 CI_LIST 集合
     */
    Map<String, List<CcCiInfo>> querySnapshotCIByHistoryIds(List<String> hisDiagramIds, List<String> ciCode);

    /**
     *  根据快照（历史数据）IDS 查询快照内的RLT数据
     * @param hisDiagramIds 视图快照（历史）IDS
     * @param rltCodes 关系ID
     * @return DIAGRAM_ID 与 RLT_LIST 集合
     */
    Map<String, List<EamCiRltDTO>> querySnapshotRltByHistoryIds(List<String> hisDiagramIds, List<String> rltCodes);

    /**
     *  根据视图快照ID 新建/回滚 主视图
     * @param snapshotId 源端快照ID
     * @param type 操作类型  CREATE-新建  ROLLBACK-回滚
     * @return
     */
    String saveOrUpdateDiagramBySnapshotId(String snapshotId, String type);

    /**
     *  更新历史视图数据基本信息
     * @param snapshotVO
     */
    Boolean updateSnapshotInfo(DiagramSnapshotVO snapshotVO);

    /**
     *  删除视图快照
     * @param snapshotVO
     * @return
     */
    Boolean deleteSnapshotInfo(DiagramSnapshotVO snapshotVO);

    /**
     *  是-私有库历史数据
     * @param diagramId
     * @return
     */
    Boolean isPrivateHistoryInfo(String diagramId);
}
