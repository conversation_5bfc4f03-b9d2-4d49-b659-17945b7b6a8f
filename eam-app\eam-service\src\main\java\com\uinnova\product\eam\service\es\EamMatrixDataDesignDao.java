package com.uinnova.product.eam.service.es;

import com.uinnova.product.eam.comm.model.es.EamMatrixInstanceData;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;

/**
 * 矩阵表格
 * <AUTHOR>
 */
@Service
public class EamMatrixDataDesignDao extends AbstractESBaseDao<EamMatrixInstanceData, EamMatrixInstanceData> {
    @Override
    public String getIndex() {
        return "uino_eam_matrix_data_design";
    }

    @Override
    public String getType() {
        return "_doc";
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }
}
