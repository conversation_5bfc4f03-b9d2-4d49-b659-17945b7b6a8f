package com.uinnova.product.eam.web.bm.bean;

import com.binary.framework.bean.annotation.Comment;
import com.uino.bean.cmdb.base.LibType;
import lombok.Data;

@Data
@Deprecated
public class QueryDir {

    @Comment("模糊查询字段信息")
    private String like;

    private Integer dataStatus;

    private LibType libType;

    private String viewType;

    @Comment("文件夹名称")
    private String dirName;

    @Comment("按照字段排序")
    private String orders;

}
