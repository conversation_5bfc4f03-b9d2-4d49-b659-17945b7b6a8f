package com.uinnova.product.eam.web.xinwang.cas;

import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.session.data.redis.config.ConfigureRedisAction;
import org.springframework.session.data.redis.config.annotation.web.http.RedisHttpSessionConfiguration;

/**
 * description
 *
 * <AUTHOR>
 * @since 2023/3/21 11:43
 */
@Configuration
public class HttpSessionConfig {

    @Bean
    @ConditionalOnClass(RedisHttpSessionConfiguration.class)
    public static ConfigureRedisAction configureRedisAction() {
        return ConfigureRedisAction.NO_OP;
    }
}
