package com.uino.bean.cmdb.business;

import java.util.List;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 
 * <AUTHOR>
 *
 */
@Setter
@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value="Excel文件类",description = "Excel文件")
public class CiExcelInfoDto {

    @ApiModelProperty(value="临时文件名",example="temp")
    @Comment("临时文件名")
    private String fileName;

    @Comment("临时文件名")
    private String ownerCode;

    @ApiModelProperty(value="sheet页名",example = "123")
    @Comment("sheet页名")
    private List<String> sheetNames;

    @ApiModelProperty(value="对应的ci分类信息",example = "sport")
    @Comment("对应的ci分类信息")
    private CcCiClassInfo ciClassInfo;

    @ApiModelProperty(value="回显Excel数据时使用的页码",example="1")
    @Comment("回显Excel数据时使用的页码")
    private Integer pageNum;

    @ApiModelProperty(value="回显Excel数据时使用的一页容量",example="100")
    @Comment("回显Excel数据时使用的一页容量")
    private Integer pageSize;

    @ApiModelProperty(value="分类目录id",example = "1")
    private Long dirId;

    @ApiModelProperty(value="是否清空存量数据",example = "1")
    private boolean overwriteData;

    @ApiModelProperty(value="目标文件目录id",example = "100")
    private Long directoryId;
}
