package com.uinnova.product.eam.comm.dto;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.comm.model.es.EamMatrixInstanceData;
import com.uino.bean.cmdb.base.LibType;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 矩阵表对象关系查询参数
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class EamMatrixTableQuery extends EamMatrixInstanceData {

    @Comment("用户标识")
    private String code;

    @Comment("用户标识")
    private String ownerCode;

    @Comment("libType")
    private LibType libType = LibType.PRIVATE;

}
