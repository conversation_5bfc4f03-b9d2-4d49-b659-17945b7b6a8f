package com.uinnova.product.eam.comm.bean;

import java.util.Objects;

public class StandardDoc {

    private Long id;
    private Long standardId;
    private String name;
    private String realPath;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getStandardId() {
        return standardId;
    }

    public void setStandardId(Long standardId) {
        this.standardId = standardId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getRealPath() {
        return realPath;
    }

    public void setRealPath(String realPath) {
        this.realPath = realPath;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof StandardDoc)) return false;
        StandardDoc that = (StandardDoc) o;
        return Objects.equals(id, that.id) &&
                Objects.equals(standardId, that.standardId) &&
                Objects.equals(name, that.name) &&
                Objects.equals(realPath, that.realPath);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, standardId, name, realPath);
    }
}
