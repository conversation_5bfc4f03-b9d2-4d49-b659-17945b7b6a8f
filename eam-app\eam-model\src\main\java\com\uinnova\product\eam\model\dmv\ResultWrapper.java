package com.uinnova.product.eam.model.dmv;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel("返回值封装")
public class ResultWrapper<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 无异常时error-code
     */
    public static final int NO_ERROR_CODE = 200;

    @ApiModelProperty("返回状态,true:返回正常,false:返回异常")
    private boolean success;

    @ApiModelProperty("返回状态码,200 请求正常，但是需要注意返回值中的success，当sucees为true时接口正常，false为已知并捕获的异常，需要前台抛出异常信息给用户。\r\n" + "210 登录即将过期，需要前台刷新token post xxxx:xxxx/sso/client/oauth/refreshToken。\r\n" + "211-239 授权即将过期，数值-210得数即为即将过期天数，如：211表示授权1天后过期，220表示授权10天后过期，其它数值以此类推。\r\n"
            + "302 一般是nginx重定向了。\r\n" + "401 登录验证失败，token本身过期或伪造，token没放到header里面，服务异常了(例如被扫描坏了)。\r\n" + "402 授权过期，前台应该调到授权页面去。\r\n" + "500或其他 服务器未知异常。\r\n" + "502 一般是服务还没有启动完成。\r\n" + "504 nginx超时时间配置不对，或者服务接口查询数据居多需要后台排查。")
    private int code = -1;

    @ApiModelProperty("返回具体数据")
    private T data;

    @ApiModelProperty("错误详情信息")
    private String message;

    public ResultWrapper(T data) {
        this(true, NO_ERROR_CODE, null, data);
    }

    public ResultWrapper(Throwable t) {
        this(false, 500, t.getMessage(), null);
    }

    public ResultWrapper(boolean success, int code, String message) {
        this(success, code, message, null);
    }

    public ResultWrapper(boolean success, int code, String message, T data) {
        this.success = success;
        this.code = code;
        this.message = message;
        this.data = data;
    }

}
