package com.uinnova.product.eam.web.mix.diagram.v2.webSocekt;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.binary.framework.web.RemoteResult;
import com.uinnova.product.eam.base.diagram.mix.model.SysUserDTO;
import com.uinnova.product.eam.web.mix.diagram.v2.webSocekt.bean.SocketResult;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.permission.business.UserInfo;
import com.uino.bean.permission.query.CSysUser;
import com.uino.api.client.permission.IUserApiSvc;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.CrossOrigin;

import jakarta.websocket.OnClose;
import jakarta.websocket.OnError;
import jakarta.websocket.OnMessage;
import jakarta.websocket.OnOpen;
import jakarta.websocket.Session;
import jakarta.websocket.server.PathParam;
import jakarta.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArraySet;

/**
 * @Classname
 * @Description TODO
 * @Date 2021/4/7 16:52
 * <AUTHOR> sh
 */
@CrossOrigin(origins = {})
@ServerEndpoint("/websocket/{userId}/{diagramId}")
@Component
@Slf4j
public class DiagramServer implements ApplicationRunner {

    private static final int _COLLABORATION_CEILING = 100;

    //todo：由于时间有限，暂放在内存，此处后期引入redis要放到缓存中
    private static ConcurrentHashMap<String, Set<DiagramServer>> webSocketMap = new ConcurrentHashMap<>();

    //与某个客户端的连接会话，需要通过它来给客户端发送数据
    private Session session;
    //接收sid
    private Long userId;

    private String diagramId;

    private static IUserApiSvc userSvc;

    @Autowired
    public void setUserSvc(IUserApiSvc userSvc) {
        DiagramServer.userSvc = userSvc;
    }

    /**
     * 建立连接
     *
     * @param session
     * @param userId
     */
    @OnOpen
    public void onOpen(Session session, @PathParam("userId") Long userId, @PathParam("diagramId") String diagramId) {
        log.info("连接socket");
        this.session = session;
        this.userId = userId;
        this.diagramId = diagramId;
        boolean isOnline = true;
        Set<DiagramServer> diagramServers = webSocketMap.get(diagramId);
        if (diagramServers == null) {
            diagramServers = new CopyOnWriteArraySet<>();
            webSocketMap.put(diagramId, diagramServers);
        }
        if (diagramServers.size() < _COLLABORATION_CEILING) {
            //判断用户是否重复打开多个页面，如果打开多个则不提示
            for (DiagramServer diagramServer : diagramServers) {
                if (diagramServer.getUserId().equals(userId)) {
                    isOnline = false;
                }
            }
            diagramServers.add(this);
            try {
                SocketResult socketResult = new SocketResult();
                socketResult.setMsgType(SocketResult.SType.ONLINE.getType());
                List<Long> ids = new ArrayList<>();
                for (DiagramServer diagramServer : webSocketMap.get(diagramId)) {
                    ids.add(diagramServer.userId);
                }
                CSysUser cdt = new CSysUser();
                cdt.setIds(ids.toArray(new Long[0]));
                List<SysUser> sysUserByCdt = userSvc.getSysUserByCdt(cdt);
                List<SysUserDTO> connectionUserList = new ArrayList<>();
                Map<Long, SysUserDTO> userMap = new HashMap<>();
                for (SysUser user : sysUserByCdt) {
                    SysUserDTO sysUserDTO = new SysUserDTO();
                    BeanUtils.copyProperties(user, sysUserDTO);
                    userMap.put(user.getId(), sysUserDTO);
                    if (user.getId().equals(userId)) {
                        socketResult.setOnlineUser(sysUserDTO);
                    }
                    connectionUserList.add(sysUserDTO);
                }
                socketResult.setConnectionUserList(connectionUserList);
                if (isOnline) {
                    //通知所有用户
                    sendInfo(socketResult, userMap);
                }else {
                    socketResult.setCurrentOperationUser(userMap.get(userId));
                    sendMessage(JSONObject.toJSONString(new RemoteResult(socketResult), SerializerFeature.DisableCircularReferenceDetect));
                }
            } catch (IOException e) {
                log.error("websocket IO异常" + e.getMessage());
            }
        } else {
            try {
                sendMessage(JSONObject.toJSONString(new RemoteResult(true, 0, "用户连接数已满")));
            } catch (Exception e) {
                log.error("websocket IO异常" + e.getMessage());
            }
        }



    }

    /**
     * 关闭连接
     */
    @OnClose
    public void onClose() {
        boolean isOffline = true;
        List<Long> ids = new ArrayList<>();
        int num = 0;
        for (DiagramServer diagramServer : webSocketMap.get(diagramId)) {
            ids.add(diagramServer.getUserId());
            if (diagramServer.getUserId().equals(this.userId)) {
                num++;
            }
        }
        if (num > 1) {
            isOffline = false;
        }

        //从set中删除
        webSocketMap.get(diagramId).remove(this);
        if (isOffline) {
            try {
                SocketResult socketResult = new SocketResult();
                socketResult.setMsgType(SocketResult.SType.OFFLINE.getType());

                CSysUser cdt = new CSysUser();
                cdt.setIds(ids.toArray(new Long[0]));
                List<SysUser> sysUserByCdt = userSvc.getSysUserByCdt(cdt);
                List<SysUserDTO> connectionUserList = new ArrayList<>();
                Map<Long, SysUserDTO> userMap = new HashMap<>();
                for (SysUser user : sysUserByCdt) {
                    SysUserDTO sysUserDTO = new SysUserDTO();
                    BeanUtils.copyProperties(user, sysUserDTO);
                    userMap.put(user.getId(), sysUserDTO);
                    if (user.getId().equals(userId)) {
                        socketResult.setOfflineUser(sysUserDTO);
                    } else {
                        connectionUserList.add(sysUserDTO);
                    }

                }
                socketResult.setConnectionUserList(connectionUserList);
                //向客户端发送消息
                sendInfo(socketResult, userMap);
            } catch (IOException e) {
                log.error("websocket IO异常" + e.getMessage());
            }
        }
    }

    /**
     * 接收客户端消息
     *
     * @param body 客户端发送过来的消息内容
     * @param
     */
    @OnMessage(maxMessageSize = 1048576)
    public void onMessage(String body) {
        // log.info("收到的同步消息：{}", body);
        JSONObject jsonObject = JSONObject.parseObject(body);
        if ("heartbeat".equals(jsonObject.getString("type"))) {
            log.info("连接判断");
            try {
                sendMessage(body);
            } catch (IOException e) {
                onClose();
            }
        } else {
            SocketResult socketResult = new SocketResult();
            socketResult.setMsgType(SocketResult.SType.OPERATION.getType());
            UserInfo userInfo = userSvc.getUserInfoById(userId);
            SysUserDTO sysUserDTO = new SysUserDTO();
            BeanUtils.copyProperties(userInfo, sysUserDTO);
            socketResult.setCurrentOperationUser(sysUserDTO);
            socketResult.setBody(body);
            String shareMsg = JSONObject.toJSONString(new RemoteResult(socketResult), SerializerFeature.DisableCircularReferenceDetect);
            //log.info("发出的消息：" + shareMsg);
            for (DiagramServer item : webSocketMap.get(diagramId)) {
                if (!item.session.getId().equals(this.session.getId())) {

                    // 代码扫描安全漏洞 try块在循环体内
                    this.trySendMessage(item, shareMsg);

                    /*try {
                        item.sendMessage(shareMsg);
                    } catch (IOException e) {
                        e.printStackTrace();
                        onClose();
                    }*/
                }

            }
        }
    }

    /**
     * 异常
     *
     * @param
     * @param error
     */
    @OnError
    public void onError(Throwable error) {
        log.error(error.getMessage());
        error.printStackTrace();
    }

    /**
     * 服务器主动推送到客户端，这个客户端时当前连接的客户端（this.session）
     */
    public void sendMessage(String message) throws IOException {
        synchronized (this.session) {
            log.info("当前会话为：" + this.session.getId());
            log.info("当前线程为：" + Thread.currentThread().getName());
            this.session.getBasicRemote().sendText(message);
        }
    }


    /**
     * 群发自定义消息
     *
     * @param
     * @throws IOException
     */
    public void sendInfo(SocketResult socketResult, Map<Long, SysUserDTO> userMap) throws IOException {
        if (webSocketMap.get(diagramId) != null) {
            for (DiagramServer item : webSocketMap.get(diagramId)) {

                // 代码扫描安全漏洞 try块在循环体内
                this.trySendMessage1(socketResult, userMap, item);

                /*try {
                    socketResult.setCurrentOperationUser(userMap.get(item.userId));
                    String result = JSONObject.toJSONString(new RemoteResult(socketResult), SerializerFeature.DisableCircularReferenceDetect);
                    item.sendMessage(result);
                } catch (IOException e) {
                    e.printStackTrace();
                }*/
            }
        }
    }

    /**
     * 群发一个对象信息
     *
     * @param studentList
     */
    public static void sendInfo(List<String> studentList) {
//        for (DiagramServer item : webSocketSet) {
        // 代码扫描安全漏洞 try块在循环体内 无处使用
//            try {
//                item.sendMessage(JSON.toJSONString(studentList));
//            } catch (IOException e) {
//                continue;
//            }
//        }
    }


    public Session getSession() {
        return session;
    }

    public void setSession(Session session) {
        this.session = session;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getDiagramId() {
        return diagramId;
    }

    public void setDiagramId(String diagramId) {
        this.diagramId = diagramId;
    }


    private void trySendMessage(DiagramServer item, String shareMsg) {
        try {
            item.sendMessage(shareMsg);
        } catch (IOException e) {
            e.printStackTrace();
            onClose();
        }
    }

    private void trySendMessage1(SocketResult socketResult, Map<Long, SysUserDTO> userMap, DiagramServer item) {
        try {
            socketResult.setCurrentOperationUser(userMap.get(item.userId));
            String result = JSONObject.toJSONString(new RemoteResult(socketResult), SerializerFeature.DisableCircularReferenceDetect);
            item.sendMessage(result);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("DiagramServer start success");
    }
}
