package com.uino.provider.feign.sys;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.uino.bean.cmdb.base.ESResource;
import com.uino.provider.feign.config.BaseFeignConfig;

@Component
@FeignClient(name = "${feign-server-name:tarsier-platform}", path = "${feign-server-path:tarsier-platform}/feign/resource", configuration = {
        BaseFeignConfig.class})
public interface ResourceFeign {

    /**
     * 保存同步资源信息
     * 
     * @see #saveSyncResourceInfo(String, String, boolean, boolean, Integer)
     * @param path
     * @param publicUrl
     * @param unzip
     * @param optionType
     * @return
     */
    @PostMapping("saveSyncResourceInfoSomeParam")
    String saveSyncResourceInfo(@RequestParam(required = false, value = "path") String path,
            @RequestParam(required = false, value = "publicUrl") String publicUrl,
            @RequestParam(required = false, value = "unzip") boolean unzip,
            @RequestParam(required = false, value = "optionType") Integer optionType);

    /**
     * 保存同步资源信息
     * 
     * @param path
     *            资源在本地的真实路径eg:/usr/local/data/111.txt
     * @param publicUrl
     *            资源公开可访问地址eg:http://************/dara/111.txt
     * @param unzip
     *            是否解压
     * @param currentDir
     *            是否解压当前目录
     * @param optionType
     *            操作类型0:创建1:删除2:修改
     * @return
     */
    @PostMapping("saveSyncResourceInfoAllParam")
    String saveSyncResourceInfo(@RequestParam(required = false, value = "path") String path,
            @RequestParam(required = false, value = "publicUrl") String publicUrl,
            @RequestParam(required = false, value = "unzip") boolean unzip,
            @RequestParam(required = false, value = "currentDir") boolean currentDir,
            @RequestParam(required = false, value = "optionType") Integer optionType);

    /**
     * 批量保存同步资源信息
     * 
     * @see #saveSyncResourceInfo(String, String, String, Integer)
     * @param saveDtos
     */
    @PostMapping("saveSyncResourceInfoList")
    void saveSyncResourceInfo(@RequestBody(required = false) List<ESResource> saveDtos);

    /**
     * 获取待同步资源信息(从当前时间前一天开始获取)
     * 
     * @param startDataId
     *            起点数据id
     * @return
     */
    @PostMapping("getWaitSyncResources")
    List<ESResource> getWaitSyncResources(@RequestParam(value = "createTime", required = false) Long createTime);
}
