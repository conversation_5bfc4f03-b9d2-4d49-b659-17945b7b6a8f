package com.uinnova.product.eam.web.flow.mvc;

import com.binary.core.util.BinaryUtils;
import com.binary.framework.web.RemoteResult;
import com.uinnova.product.eam.comm.model.es.EamCategory;
import com.uinnova.product.eam.model.FlowProcessPublishDto;
import com.uinnova.product.eam.model.ObjectMovingDto;
import com.uinnova.product.eam.service.C2CProcessService;
import com.uinnova.product.eam.service.FlowProcessSystemService;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uino.util.sys.SysUtil;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.Map;


/**
 * 流程管理/端到端流程controller
 *
 * <AUTHOR>
  */
@RestController
@RequestMapping("/flowManager/c2cProcess")
public class C2CProcessController {

    @Resource
    C2CProcessService c2CProcessService;

    @Resource
    private FlowProcessSystemService flowProcessSystemService;


    @PostMapping("saveOrUpdate")
    @ModDesc(desc = "保存或更新文件夹信息", pDesc = "文件夹信息", rDesc = "文件夹id", rType = RemoteResult.class)
    public RemoteResult saveOrUpdate(@RequestBody EamCategory vo) {
        BinaryUtils.checkEmpty(vo, "vo");
        BinaryUtils.checkEmpty(vo.getDirName(),"dirName");
        //假定根目录的父级id为888
        Assert.notNull(vo.getParentId(), "父文件夹id不可为空!");
        Long id = c2CProcessService.saveOrUpdateCategory(vo);
        return new RemoteResult(id);
    }

    @PostMapping("delete")
    public RemoteResult delete(@RequestParam Long id) {
        BinaryUtils.checkEmpty(id, "目录id不能为空");
        c2CProcessService.delete(id);
        return new RemoteResult(true);
    }

    /**
     * 移动目录
     * @param objectMovingDto
     * @return 结果
     */
    @PostMapping("moveCi")
    public RemoteResult moveCi(@RequestBody ObjectMovingDto objectMovingDto) {
        BinaryUtils.checkEmpty(objectMovingDto, "参数不能为空");
        return new RemoteResult(c2CProcessService.moveCi(objectMovingDto));
    }
    /**
     * 查询流程树
     */
    @GetMapping("/queryTree")
    public RemoteResult queryTree(Long id) {
        return new RemoteResult(c2CProcessService.getC2CTree(id));
    }

    /**
     * 根据某个目录，分页查询下面的流程信息
     */
    @GetMapping("/queryChildInfoPageById")
    public RemoteResult queryChildInfoPageById(Long id,Integer pageNum,Integer pageSize,String word) {
        return new RemoteResult(c2CProcessService.queryChildInfoPageById(id,pageNum,pageSize,word));
    }

    /**
     *  流程体系发布
     * @param flowProcessPublishDto 需要发布得流程ciCode
     * @return
     */
    @PostMapping("publishC2CFlowProcessSystem")
    public RemoteResult publishFlowProcessSystem(@RequestBody FlowProcessPublishDto flowProcessPublishDto){
        Assert.notNull(flowProcessPublishDto.getCiCode(),"资产标识ciCode不能为空");
        Map<String, Object> errorMap = flowProcessSystemService.checkFlowProcessSystem(flowProcessPublishDto.getCiCode());
        if(!errorMap.keySet().isEmpty()){
            return new RemoteResult(false,500,"校验未通过",errorMap);
        }
        String loginCode = SysUtil.getCurrentUserInfo().getLoginCode();
        c2CProcessService.publishC2CFlowProcessSystem(flowProcessPublishDto.getCiCode(),loginCode,"submit");
        return new RemoteResult("success");
    }

}
