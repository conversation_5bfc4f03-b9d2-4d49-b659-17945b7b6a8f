package com.uinnova.product.eam.model.diagram;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.base.diagram.model.*;
import com.uino.bean.permission.base.SysUser;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Data
public class VcDiagramInfo implements Serializable{
	private static final long serialVersionUID = 1L;
	
	@Comment("自动生成视图名字")
	private Boolean autoName;
	
	@Comment("视图基本信息")
	private VcDiagram diagram;

	@Comment("组合视图的信息")
	private List<DcCombDiagram> combDiagrams;
	
	@Comment("组合视图下单图的详细信息")
	private List<VcDiagramInfo> combDiagramInfos;
	
	@Comment("视图svg内容信息,用于生产缩略图")
	private String svg;
	
	@Comment("缩略图base64编码")
	private String thumbnail;

	@Comment("视图具体内容")
	private String xml;
	
	@Comment("视图json内容")
	private String json;

	@Comment("版本描述")
	private String versionDesc;
	
	@Comment("自定义版本号")
	private String versionNo;
	
	@Comment("视图关联元素信息")
	private List<VcDiagramEle> diagramEles;

	@Comment("视图自定义分类labels")
	private List<VcDiagramCiAttrDisp> ciAttrDisps;

	@Comment("视图标签关系")
	private List<VcDiagramTag> diagramTags;
	
	@Comment("标签")
	private List<VcTag> tags;
	
	@Comment("视图组信息")
	private List<VcDiagramGroup> diagramGroups;

	@Comment("视图组信息")
	private List<VcGroup> groups;

	@Comment("小组成员权限信息")
	private Map<Long,List<SysOpInfo>> groupUsers;
	
	@Comment("保存时默认放到小组的位置")
	private Long createGroupId;
	
	@Comment("视图作者信息")
	private SysUser creator;
	
	@Comment("版本更新来源  1：历史版本来源   2：自动更新来源")
	private Integer updateType;
	
	@Comment("更新的关系数据")
	private List<String> upRelations;
	
	@Comment("视图规则信息")
	private VcDiagramEle rltRule;
	
	@Comment("视图规则所使用的模版信息")
	private VcDiagramEle template;
	
	@Comment("是否是我收藏的视图：1=是  0：否")
	private Integer isCollection;
	
	@Comment("操作代码,当前用户对当前视图的操作类型")
	private Integer[] opTypes;
	
	@Comment("版本描述路径信息")
	private String versionDescPath;
	
	private Boolean seeAuth;
	
	private Boolean editAuth;

	private Long diagramVersionId;

	private String versionName;

	@Comment("视图分享记录")
	private List<DiagramShareRecordResult> shareRecords = new ArrayList<>();

	private Integer flowStatus;
}
