package com.uino.api.client.cmdb.local;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.uino.api.client.cmdb.IDataSetTopApiSvc;
import com.uino.service.cmdb.dataset.microservice.IDataSetTopSvc;

/**
 * @Classname DataSetTopSvcLocal
 * @Description TODO
 * @Date 2020/5/29 18:01
 * @Created by sh
 */
@Service
public class DataSetTopSvcLocal implements IDataSetTopApiSvc {
    @Autowired
    private IDataSetTopSvc dataSetTopService;

    @Override
    public void collectDataSet(Long dataSetId) {
        dataSetTopService.collectDataSet(dataSetId);
    }

	@Override
	public void deleteDataSetTop(Long dataSetId) {
		dataSetTopService.deleteDataSetTop(dataSetId);
	}
}
