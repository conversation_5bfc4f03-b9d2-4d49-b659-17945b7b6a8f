package com.uinnova.product.vmdb.comm.model.quality;

import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;

/**
 * 
 * <AUTHOR>
 *
 */
@Comment("CI数据质量图表序列表[CC_CI_QUALITY_CHART_SERIES]")
public class CcCiQualityChartSeries implements EntityBean {
    private static final long serialVersionUID = 1L;

    @Comment("ID[ID]")
    private Long id;

    @Comment("所属图表ID[CHART_ID]    所属图标ID")
    private Long chartId;

    @Comment("CI分类[CLASS_ID]")
    private Long classId;

    @Comment("序列类型[SERIES_TYPE]    序列类型:1=线 2=区域 3=柱")
    private Long seriesType;

    @Comment("序列颜色[SERIES_COLOR]")
    private String seriesColor;

    @Comment("序列顺序[SERIES_ORDER_NO]")
    private Integer seriesOrderNo;

    @Comment("备用1[CUSTOM_1]")
    private String custom1;

    @Comment("备用2[CUSTOM_2]")
    private String custom2;

    @Comment("备用3[CUSTOM_3]")
    private String custom3;

    @Comment("备用4[CUSTOM_4]")
    private String custom4;

    @Comment("备用5[CUSTOM_5]")
    private String custom5;

    @Comment("备用6[CUSTOM_6]")
    private String custom6;

    @Comment("所属域[DOMAIN_ID]")
    private Long domainId;

    @Comment("数据状态[DATA_STATUS]    数据状态:0=删除，1=正常")
    private Integer dataStatus;

    @Comment("创建时间[CREATE_TIME]    创建时间:yyyyMMddHHmmss")
    private Long createTime;

    @Comment("更新时间[MODIFY_TIME]    更新时间:yyyyMMddHHmmss")
    private Long modifyTime;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getChartId() {
        return this.chartId;
    }

    public void setChartId(Long chartId) {
        this.chartId = chartId;
    }

    public Long getClassId() {
        return this.classId;
    }

    public void setClassId(Long classId) {
        this.classId = classId;
    }

    public Long getSeriesType() {
        return this.seriesType;
    }

    public void setSeriesType(Long seriesType) {
        this.seriesType = seriesType;
    }

    public String getSeriesColor() {
        return this.seriesColor;
    }

    public void setSeriesColor(String seriesColor) {
        this.seriesColor = seriesColor;
    }

    public Integer getSeriesOrderNo() {
        return this.seriesOrderNo;
    }

    public void setSeriesOrderNo(Integer seriesOrderNo) {
        this.seriesOrderNo = seriesOrderNo;
    }

    public String getCustom1() {
        return this.custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }

    public String getCustom2() {
        return this.custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }

    public String getCustom3() {
        return this.custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }

    public String getCustom4() {
        return this.custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }

    public String getCustom5() {
        return this.custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }

    public String getCustom6() {
        return this.custom6;
    }

    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }

    public Long getDomainId() {
        return this.domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }

    public Integer getDataStatus() {
        return this.dataStatus;
    }

    public void setDataStatus(Integer dataStatus) {
        this.dataStatus = dataStatus;
    }

    public Long getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getModifyTime() {
        return this.modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }

}
