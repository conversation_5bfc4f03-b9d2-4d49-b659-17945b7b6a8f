package com.uino.api.client.cmdb;

import java.util.List;

import com.uino.bean.cmdb.base.ESVisualModel;
import com.uino.bean.cmdb.base.ESVisualModelVo;
import com.uino.bean.cmdb.base.LibType;
import org.elasticsearch.index.query.QueryBuilder;

/**
 * 可视化模型存储类
 * 
 * <AUTHOR>
 */
public interface IVisualModelApiSvc {

    /**
     * 查询可视化模型列表
     *
     * @param domainId
     * @return
     */
    List<ESVisualModel> queryVisualModels(Long domainId);

    /**
     * 保存可视化模型
     *
     * @param model
     * @return
     */
    Long saveVisualModel(ESVisualModel model);

    /**
     * 保存可视化模型
     *
     * @param model
     * @return
     */
    Long saveVisualModelPrivate(ESVisualModel model);

    /**
     * 执行元模型中ci间关系重建
     * @return
     */
    Boolean flushAllEnableVisualModelCiRlt();

    /**
     * 根据查询条件查询元模型数据
     * @param query
     * @param libType
     * @return
     */
    List<ESVisualModel> getVisualModelByQuery(QueryBuilder query, LibType libType);

    /**
     * 查询所有已经发布的并且启用的元模型
     *
     * @param domainId
     * @param libType
     * @param loginCode
     * @return
     */
    List<ESVisualModelVo> queryVisualModels(Long domainId, LibType libType, String loginCode);

    List<ESVisualModelVo> queryVisualModelsNoChickExit(Long domainId, LibType libType, String loginCode);

    /**
     * 修改可视化模型名称
     *
     * @param model
     * @return
     */
    void updateVisualModelName(ESVisualModel model);

    /**
     * 删除可视化模型
     *
     * @param id
     * @return
     */
    void deleteVisualModel(Long id);

    /**
     * 删除元模型下的sheet页缩略图
     *
     * @param id
     * @return
     */
    void delVMThumbnailBySheetId(Long id, Long sheetId);

    /**
     * 根据ID查询可视化模型
     *
     * @param id
     * @return
     */
    ESVisualModel queryVisualModelById(Long id, Long domainId);

    /**
     * 根据ID查询私有库可视化模型列表
     *
     * @param domainId
     * @return
     */
    ESVisualModel queryPrivateVisualModelById(Long domainId, Long visId);


    /**
     * 删除有效的元模型操作记录
     */
    void delValidVisData();
}