package com.uinnova.product.eam.web.diagram.bean;

import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;

/**
 * <AUTHOR>
 *
 */
public class CiRltQueryCdt implements Condition {

	private static final long serialVersionUID = 1L;
	
	@Comment("模糊收索字段")
	private String like;
	
	@Comment("ID")
	private Long tagId;

	@Comment("IDS")
	private Long[] tagIds;

	@Comment("ci关系分类Id")
	private Long classId;
	
	@Comment("ci关系分类ID")
	private Long[] classIds;
	
	@Comment("是否刷新分类的数据0不刷新,1刷新")
	private Integer queryClass;
	
	@Comment("关系级别")
	private Integer[] ciRltLvls;
	
	@Comment("来源CI分类ID")
	private Long sourceClassId;
	
	@Comment("目标CI分类ID")
	private Long targetClassId;
	

	public String getLike() {
		return like;
	}

	public void setLike(String like) {
		this.like = like;
	}

	public Long getTagId() {
		return tagId;
	}

	public void setTagId(Long tagId) {
		this.tagId = tagId;
	}

	public Long[] getTagIds() {
		return tagIds;
	}

	public void setTagIds(Long[] tagIds) {
		this.tagIds = tagIds;
	}

	public Long getClassId() {
		return classId;
	}

	public void setClassId(Long classId) {
		this.classId = classId;
	}

	public Long[] getClassIds() {
		return classIds;
	}

	public void setClassIds(Long[] classIds) {
		this.classIds = classIds;
	}

	public Integer getQueryClass() {
		return queryClass;
	}

	public void setQueryClass(Integer queryClass) {
		this.queryClass = queryClass;
	}

	public Integer[] getCiRltLvls() {
		return ciRltLvls;
	}

	public void setCiRltLvls(Integer[] ciRltLvls) {
		this.ciRltLvls = ciRltLvls;
	}

	public Long getSourceClassId() {
		return sourceClassId;
	}

	public void setSourceClassId(Long sourceClassId) {
		this.sourceClassId = sourceClassId;
	}

	public Long getTargetClassId() {
		return targetClassId;
	}

	public void setTargetClassId(Long targetClassId) {
		this.targetClassId = targetClassId;
	}

	
	
	
}
