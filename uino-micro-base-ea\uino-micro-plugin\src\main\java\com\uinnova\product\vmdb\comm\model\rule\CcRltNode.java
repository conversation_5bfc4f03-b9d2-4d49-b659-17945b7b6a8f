package com.uinnova.product.vmdb.comm.model.rule;

import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;

/**
 * 
 * <AUTHOR>
 *
 */
@Comment("关系节点表[CC_RLT_NODE]")
public class CcRltNode implements EntityBean {
    private static final long serialVersionUID = 1L;

    @Comment("ID[ID]")
    private Long id;

    @Comment("所属定义[DEF_ID]")
    private Long defId;

    @Comment("定义类型[DEF_TYPE]    定义类型:1=朋友圈")
    private Integer defType;

    @Comment("参照分类[CLASS_ID]")
    private Long classId;

    @Comment("节点分类ID[NODE_CLASS_ID]")
    private Long nodeClassId;

    @Comment("页面节点ID[PAGE_NODE_ID]")
    private Long pageNodeId;

    @Comment("X坐标[X]    X坐标:依赖、调用、等等")
    private Integer x;

    @Comment("Y坐标[Y]    Y坐标:供页面用")
    private Integer y;

    @Comment("所属域[DOMAIN_ID]")
    private Long domainId;

    @Comment("数据状态[DATA_STATUS]    数据状态:数据状态：1-正常 0-删除")
    private Integer dataStatus;

    @Comment("创建时间[CREATE_TIME]    创建时间:yyyyMMddHHmmss")
    private Long createTime;

    @Comment("修改时间[MODIFY_TIME]    修改时间:yyyyMMddHHmmss")
    private Long modifyTime;

    @Comment("节点类型[NODE_TYPE]    节点类型 1=CI节点、2=运算符节点")
    private Integer nodeType;

    @Comment("逻辑节点ID[LOGIC_OPERATION_ID]    逻辑节点ID 1=and、2=or")
    private Integer logicOperationId;
    
    @Comment("返回字段[NODE_RETURNS]")
    private String nodeReturns;

	public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getDefId() {
        return this.defId;
    }

    public void setDefId(Long defId) {
        this.defId = defId;
    }

    public Integer getDefType() {
        return this.defType;
    }

    public void setDefType(Integer defType) {
        this.defType = defType;
    }

    public Long getClassId() {
        return this.classId;
    }

    public void setClassId(Long classId) {
        this.classId = classId;
    }

    public Long getNodeClassId() {
        return this.nodeClassId;
    }

    public void setNodeClassId(Long nodeClassId) {
        this.nodeClassId = nodeClassId;
    }

    public Long getPageNodeId() {
        return this.pageNodeId;
    }

    public void setPageNodeId(Long pageNodeId) {
        this.pageNodeId = pageNodeId;
    }

    public Integer getX() {
        return this.x;
    }

    public void setX(Integer x) {
        this.x = x;
    }

    public Integer getY() {
        return this.y;
    }

    public void setY(Integer y) {
        this.y = y;
    }

    public Long getDomainId() {
        return this.domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }

    public Integer getDataStatus() {
        return this.dataStatus;
    }

    public void setDataStatus(Integer dataStatus) {
        this.dataStatus = dataStatus;
    }

    public Long getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getModifyTime() {
        return this.modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Integer getNodeType() {
        return this.nodeType;
    }

    public void setNodeType(Integer nodeType) {
        this.nodeType = nodeType;
    }

    public Integer getLogicOperationId() {
        return this.logicOperationId;
    }

    public void setLogicOperationId(Integer logicOperationId) {
        this.logicOperationId = logicOperationId;
    }

    public String getNodeReturns() {
		return nodeReturns;
	}

	public void setNodeReturns(String nodeReturns) {
		this.nodeReturns = nodeReturns;
	}

}
