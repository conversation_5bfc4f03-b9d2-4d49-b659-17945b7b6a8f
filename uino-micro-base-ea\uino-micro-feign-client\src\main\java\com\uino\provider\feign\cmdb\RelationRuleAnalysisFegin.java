package com.uino.provider.feign.cmdb;

import com.alibaba.fastjson.JSONObject;
import com.uino.bean.cmdb.base.dataset.DataSetMallApiRelationRule;
import com.uino.bean.cmdb.base.dataset.query.QueryCiFriendDto;
import com.uino.bean.cmdb.business.dataset.FriendInfo;
import com.uino.bean.cmdb.business.dataset.QueryCondition;
import com.uino.bean.cmdb.business.dataset.RltRuleTableData;
import com.uino.provider.feign.config.BaseFeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

/**
 * @Title: RelationRuleAnalysisFegin
 * @Description:
 * @Author: YGQ
 * @Create: 2021-05-31 14:32
 **/
@Component
@FeignClient(name = "${feign-server-name:tarsier-platform}", path = "${feign-server-path:tarsier-platform}/feign/cmdb/dataset/analysis", configuration = {
        BaseFeignConfig.class})
public interface RelationRuleAnalysisFegin {

    /**
     * find traval tree by relation rule
     *
     * @param relationRule relationRule
     * @return map
     */
    @PostMapping("findTravalTree")
    Map<Long, List<QueryCondition>> findTravalTree(@RequestBody DataSetMallApiRelationRule relationRule);

    /**
     * query ci friend by ci id
     *
     * @param queryCiFriendDto (sci, dataSetId)
     * @return friend info
     */
    @PostMapping("queryCiFriendByCiId")
    FriendInfo queryCiFriendByCiId(@RequestBody QueryCiFriendDto queryCiFriendDto);

    /**
     * query ci friend by ci id and rule
     *
     * @param queryCiFriendDto (sci, relationRule)
     * @return friend info
     */
    @PostMapping("queryCiFriendByCiIdAndRule")
    FriendInfo queryCiFriendByCiIdAndRule(@RequestBody QueryCiFriendDto queryCiFriendDto);

    /**
     * query ci friend by ci ids
     *
     * @param queryCiFriendDto (sCis, dataSetId, isIncludeAllStartCI)
     * @return map
     */
    @PostMapping("queryCiFriendByCiIds")
    Map<Long, FriendInfo> queryCiFriendByCiIds(@RequestBody QueryCiFriendDto queryCiFriendDto);

    /**
     * 指定起始CI查询朋友圈数据
     *
     * @param queryCiFriendDto (sCis, relationRule, isIncludeAllStartCI)
     * @return
     */
    @PostMapping("queryCiFriendByCiIdsAndRule")
    Map<Long, FriendInfo> queryCiFriendByCiIdsAndRule(@RequestBody QueryCiFriendDto queryCiFriendDto);

    /**
     * 根据规则查询所有数据,区分入口返回
     *
     * @param relationRule 规则
     * @return 规则结果
     */
    @PostMapping("queryCiFriendByRule")
    Map<Long, FriendInfo> queryCiFriendByRule(@RequestBody DataSetMallApiRelationRule relationRule);

    /**
     * query ci friend by rule with limit
     *
     * @param queryCiFriendDto (relationRule, limit)
     * @return map
     * */
    @PostMapping("queryCiFriendByRuleWithLimit")
    Map<Long, FriendInfo> queryCiFriendByRuleWithLimit(@RequestBody QueryCiFriendDto queryCiFriendDto);


    /**
     * 合并数据
     *
     * @param friendInfoMap
     * @return
     */
    @PostMapping("mergeFriendInfoMap")
    FriendInfo mergeFriendInfoMap(@RequestBody Map<Long, FriendInfo> friendInfoMap);


    /**
     * 拼接数据结果
     *
     * @param queryCiFriendDto (relationRule, ciIds, friendInfo)
     * @return
     */
    @PostMapping("disassembleFriendInfoDataByPath")
    RltRuleTableData disassembleFriendInfoDataByPath(@RequestBody QueryCiFriendDto queryCiFriendDto);

    /**
     * 根据入口ci查询并拼接数据结果
     *
     * @param queryCiFriendDto (sCis, relationRule)
     * @return
     */
    @PostMapping("disassembleFriendInfoDataByCisAndRule")
    RltRuleTableData disassembleFriendInfoDataByCisAndRule(@RequestBody QueryCiFriendDto queryCiFriendDto);

    /**
     * 统计
     * @param queryCiFriendDto (relationRule, simpleFriendInfoMap, chart)
     * @return
     */
    @PostMapping("countStatistics")
    JSONObject countStatistics(@RequestBody QueryCiFriendDto queryCiFriendDto);
}
