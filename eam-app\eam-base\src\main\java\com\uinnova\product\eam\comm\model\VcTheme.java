package com.uinnova.product.eam.comm.model;




import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;


@Comment("主题表[VC_THEME]")
public class VcTheme implements EntityBean {
	private static final long serialVersionUID = 1L;


	@Comment("ID[ID]")
	private Long id;


	@Comment("主题名称[THEME_NAME]")
	private String themeName;


	@Comment("主题类型[THEME_TYPE]    待定")
	private Integer themeType;


	@Comment("主题描述[THEME_DESC]")
	private String themeDesc;


	@Comment("所属域[DOMAIN_ID]")
	private Long domainId;


	@Comment("数据状态[DATA_STATUS]    数据状态：1-正常 0-删除")
	private Integer dataStatus;


	@Comment("创建人[CREATOR]")
	private String creator;


	@Comment("修改人[MODIFIER]")
	private String modifier;


	@Comment("创建时间[CREATE_TIME]    yyyyMMddHHmmss")
	private Long createTime;


	@Comment("修改时间[MODIFY_TIME]    yyyyMMddHHmmss")
	private Long modifyTime;




	public Long getId() {
		return this.id;
	}
	public void setId(Long id) {
		this.id = id;
	}


	public String getThemeName() {
		return this.themeName;
	}
	public void setThemeName(String themeName) {
		this.themeName = themeName;
	}


	public Integer getThemeType() {
		return this.themeType;
	}
	public void setThemeType(Integer themeType) {
		this.themeType = themeType;
	}


	public String getThemeDesc() {
		return this.themeDesc;
	}
	public void setThemeDesc(String themeDesc) {
		this.themeDesc = themeDesc;
	}


	public Long getDomainId() {
		return this.domainId;
	}
	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}


	public Integer getDataStatus() {
		return this.dataStatus;
	}
	public void setDataStatus(Integer dataStatus) {
		this.dataStatus = dataStatus;
	}


	public String getCreator() {
		return this.creator;
	}
	public void setCreator(String creator) {
		this.creator = creator;
	}


	public String getModifier() {
		return this.modifier;
	}
	public void setModifier(String modifier) {
		this.modifier = modifier;
	}


	public Long getCreateTime() {
		return this.createTime;
	}
	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}


	public Long getModifyTime() {
		return this.modifyTime;
	}
	public void setModifyTime(Long modifyTime) {
		this.modifyTime = modifyTime;
	}


}


