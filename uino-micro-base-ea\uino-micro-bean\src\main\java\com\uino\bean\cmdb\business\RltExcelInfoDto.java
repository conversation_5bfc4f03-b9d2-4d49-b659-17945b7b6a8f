package com.uino.bean.cmdb.business;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Set;

/**
 * @Classname RltExcelInfoDto
 * @Description 关系导入excel选择项
 * @Date 2020/8/26 15:04
 * <AUTHOR> sh
 */
@ApiModel(value="关系导入excel选择项类",description = "关系导入excel选择项")
public class RltExcelInfoDto implements Serializable {
    @ApiModelProperty(value="临时文件路径")
    //临时文件路径
    String excelFilePath;

    @ApiModelProperty(value="选择的sheet页及分类名称")
    //选择的sheet页，及分类名称
    private Set<String> rltClsCodes;

    public String getExcelFilePath() {
        return excelFilePath;
    }

    public void setExcelFilePath(String excelFilePath) {
        this.excelFilePath = excelFilePath;
    }

    public Set<String> getRltClsCodes() {
        return rltClsCodes;
    }

    public void setRltClsCodes(Set<String> rltClsCodes) {
        this.rltClsCodes = rltClsCodes;
    }
}
