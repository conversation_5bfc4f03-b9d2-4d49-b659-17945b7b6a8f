package com.uinnova.product.eam.feign.client;

import com.uinnova.product.eam.base.model.AttrConfInfo;
import com.uinnova.product.eam.base.model.AttrDefInfo;
import com.uinnova.product.eam.base.model.FileResourceMeta;
import com.uinnova.product.eam.feign.FeignConst;
import com.uinnova.product.eam.model.asset.ArchitecturalDTO;
import com.uinnova.product.eam.model.asset.ArchitecturalNameCheckDTO;
import com.uinnova.product.eam.model.asset.ArchitecturalResolutionDTO;
import com.uinnova.product.eam.model.asset.SearchArchitecturalDTO;
import com.uino.bean.cmdb.base.ESCIInfo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

@Component
@FeignClient(name = FeignConst.APPLICATION,path = FeignConst.ARCHITECTURAL_PATH)
public interface ArchitecturalClient {

    @PostMapping("/saveArchitectural")
    Long saveArchitectural(@RequestBody ArchitecturalDTO record);

    @PostMapping("/getInfoBySubsystemCode")
    List<Map<String, Object>> getInfoBySubsystemCode(ArchitecturalResolutionDTO architecturalResolutionDTO);
    @GetMapping("/getInfoByCiId")
    ArchitecturalResolutionDTO getInfoByCiId(Long ciId);
    @GetMapping("/selectAttrConf")
    AttrDefInfo selectAttrConf(AttrConfInfo attrConfInfo);
    @GetMapping("/checkArchitecturalName")
    boolean checkArchitecturalName(ArchitecturalNameCheckDTO architecturalNameCheckDTO);
    @PostMapping("/searchArchitecturalById")
    ESCIInfo searchArchitecturalDTO(SearchArchitecturalDTO searchArchitecturalDTO);
    @GetMapping("/getResIdsById")
    List<Long> getResIdsById(Long architecturalId);
    @GetMapping("/download")
    List<FileResourceMeta> download(List<Long> resIds);
}
