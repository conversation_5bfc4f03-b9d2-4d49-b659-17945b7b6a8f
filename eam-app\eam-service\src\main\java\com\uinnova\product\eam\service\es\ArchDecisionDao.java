package com.uinnova.product.eam.service.es;

import com.uinnova.product.eam.comm.model.es.ArchDecision;
import com.uinnova.product.eam.comm.model.es.EamVersionTag;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;

/**
 * 架构决策
 * <AUTHOR>
 */
@Service
public class ArchDecisionDao extends AbstractESBaseDao<ArchDecision, ArchDecision> {
    @Override
    public String getIndex() {
        return "uino_eam_arch_decision";
    }

    @Override
    public String getType() {
        return "_doc";
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }
}
