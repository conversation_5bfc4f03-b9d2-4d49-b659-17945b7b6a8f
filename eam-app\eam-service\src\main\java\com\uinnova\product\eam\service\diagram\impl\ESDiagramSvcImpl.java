package com.uinnova.product.eam.service.diagram.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.binary.core.exception.BinaryException;
import com.binary.core.exception.MessageException;
import com.binary.core.util.BinaryUtils;
import com.binary.jdbc.Page;
import com.google.common.collect.Lists;
import com.uinnova.product.eam.base.diagram.enums.DiagramSubTypeEnum;
import com.uinnova.product.eam.base.diagram.exception.DiagramNotFoundException;
import com.uinnova.product.eam.base.diagram.exception.UnAuthorizedException;
import com.uinnova.product.eam.base.diagram.model.*;
import com.uinnova.product.eam.base.diagram.utils.FileFilterUtil;
import com.uinnova.product.eam.db.VcDiagramVersionDao;
import com.uinnova.product.eam.db.diagram.es.*;
import com.uinnova.product.eam.model.diagram.*;
import com.uinnova.product.eam.service.diagram.*;
import com.uinnova.product.eam.service.diagram.event.IHiddenService;
import com.uinnova.product.eam.service.diagram.threadlocal.ThreadVariable;
import com.uinnova.product.vmdb.comm.i18n.MessageUtil;
import com.uinnova.product.vmdb.comm.i18n.VerifyType;
import com.uinnova.product.vmdb.comm.util.CommUtil;
import com.uino.api.client.permission.IUserApiSvc;
import com.uino.bean.cmdb.base.ESCIClassInfo;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.permission.business.UserInfo;
import com.uino.bean.permission.query.CSysUser;
import com.uino.dao.cmdb.ESCIClassSvc;
import com.uino.dao.util.ESUtil;
import com.uino.service.sys.microservice.IResourceSvc;
import com.uino.service.util.FileUtil;
import com.uino.tarsier.tarsiercom.util.IdGenerator;
import com.uino.util.rsm.RsmUtils;
import com.uino.util.sys.SysUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.elasticsearch.index.query.*;
import org.elasticsearch.search.sort.SortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ESDiagramSvcImpl implements ESDiagramSvc {

    public static final String FILE_SEPARATOR = System.getProperty("file.separator");
    @Value("${http.resource.space}")
    private String httpResourceUrl;

    @Autowired
    private ESDiagramDao esDiagramDao;

    @Autowired
    private VcDiagramVersionDao diagramVersionDao;

    @Autowired
    private IUserApiSvc userApiSvc;

    @Autowired
    private ESDiagramVersionSvc esDiagramVersionSvc;

    @Autowired
    private ESDiagramSheetDao esDiagramSheetDao;

    @Autowired
    private ESDiagramNodeDao esDiagramNodeDao;

    @Resource
    private ESDiagramNodeSvc diagramNodeSvc;

    @Autowired
    private ESShareDiagramDao esShareDiagramDao;

    @Autowired
    private ESDiagramLinkDao esDiagramLinkDao;

    @Resource
    private ESDiagramLinkSvc diagramLinkSvc;

    @Autowired
    private IEamShareDiagramSvc eamShareDiagramSvc;

    @Autowired
    private ESShareDiagramSvc esShareDiagramSvc;

    @Resource
    private IHiddenService hiddenService;

    @Autowired
    private RsmUtils rsmUtils;

    @Value("${local.resource.space}")
    private String localPath;

    @Value("${monet.exportJson.exportPath}")
    private String exportPath;

    @Autowired
    private ESShareLinkDao shareLinkDao;

    @Autowired(required = false)
    private IResourceSvc resourceSvc;

    @Autowired
    private ESCIClassSvc esciClassSvc;

    private static final Long SYS_DOMAIN_ID = 1L;
    private static final Logger logger = LoggerFactory.getLogger(ESDiagramSvcImpl.class);
    public static ExecutorService executor = Executors.newFixedThreadPool(10);
    private final static String DIAGRAM_OPTYPE = "DIAGRAM";
    private final static String SHEET_OPTYPE = "SHEET";
    private final static String SHEET_COPY_OPTYPE = "SHEET_COPY";
    private final static String NODE_OPTYPE = "NODE";
    private final static String LINK_OPTYPE = "LINK";
    private final static String ADD_OPTYPE = "ADD";
    private final static String DELETE_OPTYPE = "DELETE";
    private final static String UPDATE_OPTYPE = "UPDATE";
    private final static long MAX_QUERY_COUNT = 30000;
    private final static long PAGE_COUNT = 3000;

    // 关联视图跳转之后的状态
    private final static String SKIP_PRI_LOOKUP_OPTYPE = "PRI_LOOKUP";  // 私有库查看权限
    private final static String SKIP_PRI_STUDIO_OPTYPE = "PRI_STUDIO";  // 私有库编辑权限
    private final static String SKIP_DES_LOOKUP_OPTYPE = "DES_LOOKUP";  // 设计库查看状态
    private final static String SKIP_DELETE_OPTYPE = "DELETE";          // 关联视图已删除
    private final static String SKIP_FORBID_OPTYPE = "FORBID";          // 关联视图禁止跳转

    @Override
    public Map<String, String> saveESDiagram(ESDiagramInfoDTO esDiagramInfo) {
        if (BinaryUtils.isEmpty(esDiagramInfo.getLocalVersion())) {
            esDiagramInfo.setLocalVersion(0);
        }
        if (BinaryUtils.isEmpty(esDiagramInfo.getReleaseVersion())) {
            esDiagramInfo.setReleaseVersion(0);
        }
        if (BinaryUtils.isEmpty(esDiagramInfo.getOwnerCode())) {
            SysUser loginUser = SysUtil.getCurrentUserInfo();
            esDiagramInfo.setOwnerCode(BinaryUtils.isEmpty(loginUser) ? "system" : loginUser.getLoginCode());
        }
        // 视图名称为空校验
        String diagramName = esDiagramInfo.getName();
        if (StringUtils.isEmpty(diagramName)) {
            MessageUtil.throwVerify(VerifyType.EMPTY, "diagramName", diagramName);
        }
        //获取info中的diagram信息，保存到diagram中，同时自定义修改信息，保存视图
        ESDiagram esDiagram = CommUtil.copy(esDiagramInfo, ESDiagram.class);
        Long diagramId = esDiagramInfo.getId();
        if (BinaryUtils.isEmpty(diagramId)) {
            diagramId = IdGenerator.createGenerator().getID();
        }
        esDiagram.setId(diagramId);
        setDiagramBaseInfo(esDiagram);
        esDiagram.setDEnergy(SecureUtil.md5(String.valueOf(diagramId)).substring(8, 24));

        esDiagramDao.saveOrUpdate(esDiagram);
        //新增空白视图时，只会新增一个空白sheet
        ESDiagramSheetDTO sheetDTO = esDiagramInfo.getSheetList().get(0);
        if (BinaryUtils.isEmpty(sheetDTO)) {
            MessageUtil.throwVerify(VerifyType.EMPTY, "sheet名称", sheetDTO.getName());
        }
        sheetDTO.setDiagramId(diagramId);
        Long sheetId = IdGenerator.createGenerator().getID();
        sheetDTO.setId(sheetId);
        ESDiagramModel diagramModel = esDiagramInfo.getModelList().get(0);
        sheetDTO.setDiagramClass(diagramModel.getDiagramClass());
        sheetDTO.setLinkFromPortIdProperty(diagramModel.getLinkFromPortIdProperty());
        sheetDTO.setLinkToPortIdProperty(diagramModel.getLinkToPortIdProperty());
        sheetDTO.setModelData(diagramModel.getModelData());
        esDiagramSheetDao.saveOrUpdate(sheetDTO);
        //新建当前视图的历史版本记录，指向当前视图
        Long[] diagramIdArray = new Long[]{diagramId};
        createESDiagramVersionRecord(diagramIdArray);
        //返回视图id和sheet id给前台
        Map<String, String> resultMap = new HashMap<>();
        resultMap.put("diagramId", esDiagram.getDEnergy());
        return resultMap;
    }

    @Override
    public ESDiagramDTO queryDiagramInfoById(Long diagramId, String type, Boolean needAuth) {
        //1.根据视图id集合查询视图数据
        Long[] diagramIds = new Long[]{diagramId};
        ESDiagram esDiagram = judgeSingleDiagramAuth(diagramId, null, needAuth);
        Long creatorId = esDiagram.getUserId();
        String icon1 = esDiagram.getIcon1();
        if (icon1 != null && !icon1.startsWith(httpResourceUrl)) {
            esDiagram.setIcon1(new StringBuilder(httpResourceUrl).append(icon1).toString());
        }
        //2.根据视图id集合查询其包含的所有sheet数据
        ESDiagramSheetQuery sheetQuery = new ESDiagramSheetQuery();
        sheetQuery.setDiagramId(diagramId);
        List<ESDiagramSheetDTO> sheetList = esDiagramSheetDao.getListByCdt(sheetQuery);
        if (BinaryUtils.isEmpty(sheetList)) {
            throw new BinaryException("视图不包含sheet，逻辑错误");
        }
        String[] sheetIdArr = sheetList.stream().map(ESDiagramSheetDTO::getSheetId).distinct().toArray(String[]::new);
        //3.查询所有的node节点，将其按照diagramId和sheetId进行分组
        ESDiagramNodeQueryBean nodeQuery = new ESDiagramNodeQueryBean();
        nodeQuery.setDiagramId(diagramId);
        nodeQuery.setSheetIds(sheetIdArr);
        List<ESDiagramNode> nodeList = new ArrayList<>();
        nodeList = getAllNode(nodeQuery, nodeList);
        Map<String, List<ESDiagramNode>> sheetNodeMap = new HashMap<>();
        if (!BinaryUtils.isEmpty(nodeList)) {
            sheetNodeMap = nodeList
                    .stream()
                    .collect(Collectors.groupingBy(ESDiagramNode::getSheetId));
        }
        //4.查询所有的link节点，将其按照diagramId和sheetId进行分组
        ESDiagramLinkQueryBean linkQuery = new ESDiagramLinkQueryBean();
        linkQuery.setDiagramId(diagramId);
        linkQuery.setSheetIds(sheetIdArr);
        List<ESDiagramLink> linkList = new ArrayList<>();
        linkList = getAllLink(linkQuery, linkList);
        Map<String, List<ESDiagramLink>> sheetLinkMap = new HashMap<>(16);
        if (!BinaryUtils.isEmpty(linkList)) {
            sheetLinkMap = linkList
                    .stream()
                    .collect(Collectors.groupingBy(ESDiagramLink::getSheetId));
        }
        //5.查询视图分享记录，并按照diagramId进行对结果进行分组
        Map<Long, List<ESDiagramShareRecordResult>> diagramShareRecordMap = new HashMap<>(16);
        if (BinaryUtils.isEmpty(type) || !"copy".equals(type)) {
            diagramShareRecordMap = esShareDiagramSvc.queryDiagramShareRecords(diagramIds, false);
        }
        //6.查询视图作者信息，并按照userId对结果进行分组
        SysUser creator = new SysUser();
        if (BinaryUtils.isEmpty(type) || !"copy".equals(type)) {
            CSysUser cSysUser = new CSysUser();
            cSysUser.setDomainId(SYS_DOMAIN_ID);
            cSysUser.setId(creatorId);
            cSysUser.setSuperUserFlags(new Integer[]{0, 1});
            List<SysUser> creatorList = userApiSvc.getSysUserByCdt(cSysUser);
            if (BinaryUtils.isEmpty(creatorList)) {
                // 根据视图userId没查到用户信息 暂时不知道咋处理。。。先不动
                log.info("##########用户数据异常的视图信息diagramId：【{}】", diagramId);
            } else {
                creator = creatorList.get(0);
                creator.setLoginPasswd(null);
            }
        }
        //7.封装返回结果
        ESDiagramDTO esDiagramDTO = new ESDiagramDTO();
        ESDiagramInfoDTO esDiagramInfoDTO = new ESDiagramInfoDTO();
        //封装视图基础信息
        BeanUtils.copyProperties(esDiagram, esDiagramInfoDTO);
        //封装sheetList
        List<ESDiagramSheetDTO> sortedSheetDTOList = sheetList.stream().sorted(Comparator.comparing(ESDiagramSheetDTO::getSheetOrder)).collect(Collectors.toList());
        esDiagramInfoDTO.setSheetList(sortedSheetDTOList);
        //封装modelList
        List<ESDiagramModel> modelList = new ArrayList<>();
        for (ESDiagramSheetDTO sheetDTO : sheetList) {
            ESDiagramModel esDiagramModel = new ESDiagramModel();
            String sheetId = sheetDTO.getSheetId();
            //封装node
            List<ESDiagramNode> nodeDTOList = new ArrayList<>();
            if (!BinaryUtils.isEmpty(sheetNodeMap)) {
                nodeDTOList = sheetNodeMap.get(sheetId);
            }
            if (!BinaryUtils.isEmpty(nodeDTOList)) {
                esDiagramModel.setNodeDataArray(nodeDTOList);
            }
            //封装link
            List<ESDiagramLink> linkDTOList = new ArrayList<>();
            if (!BinaryUtils.isEmpty(sheetLinkMap)) {
                linkDTOList = sheetLinkMap.get(sheetId);
            }
            if (!BinaryUtils.isEmpty(linkDTOList)) {
                esDiagramModel.setLinkDataArray(linkDTOList);
            }
            //封装model基础信息
            esDiagramModel.setDiagramId(diagramId);
            esDiagramModel.setModelData(sheetDTO.getModelData());
            esDiagramModel.setDiagramClass(sheetDTO.getDiagramClass());
            esDiagramModel.setLinkToPortIdProperty(sheetDTO.getLinkToPortIdProperty());
            esDiagramModel.setLinkFromPortIdProperty(sheetDTO.getLinkFromPortIdProperty());
            esDiagramModel.setSheetId(sheetDTO.getSheetId());
            modelList.add(esDiagramModel);
        }
        esDiagramInfoDTO.setModelList(modelList);
        esDiagramDTO.setDiagram(esDiagramInfoDTO);
        //封装视图作者信息
        esDiagramDTO.setCreator(creator);
        //封装视图分享记录信息
        esDiagramDTO.setShareRecords(diagramShareRecordMap.get(diagramId));
        return esDiagramDTO;
    }

    @Override
    public List<ESDiagram> selectByIds(Collection<String> diagramIds, List<Integer> dirTypes, List<Integer> isOpens) {
        if (BinaryUtils.isEmpty(diagramIds)) {
            return Collections.emptyList();
        }
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.must(QueryBuilders.termsQuery("dEnergy.keyword", diagramIds));
        boolQuery.must(QueryBuilders.termQuery("status", 1));
        boolQuery.must(QueryBuilders.termQuery("historyVersionFlag", 1));
        boolQuery.must(QueryBuilders.termQuery("dataStatus", 1));
        if (!BinaryUtils.isEmpty(dirTypes)) {
            boolQuery.must(QueryBuilders.termsQuery("dirType", dirTypes));
        }
        if (!BinaryUtils.isEmpty(isOpens)) {
            boolQuery.must(QueryBuilders.termsQuery("isOpen", isOpens));
        }
        return esDiagramDao.selectListByQuery(1, diagramIds.size(), boolQuery);
    }

    @Override
    public List<ESDiagram> getByIds(Collection<Long> diagramIds, List<Integer> dirTypes, List<Integer> isOpens) {
        if (BinaryUtils.isEmpty(diagramIds)) {
            return Collections.emptyList();
        }
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.must(QueryBuilders.termsQuery("id", diagramIds));
        boolQuery.must(QueryBuilders.termQuery("status", 1));
        boolQuery.must(QueryBuilders.termQuery("historyVersionFlag", 1));
        boolQuery.must(QueryBuilders.termQuery("dataStatus", 1));
        if (!BinaryUtils.isEmpty(dirTypes)) {
            boolQuery.must(QueryBuilders.termsQuery("dirType", dirTypes));
        }
        if (!BinaryUtils.isEmpty(isOpens)) {
            boolQuery.must(QueryBuilders.termsQuery("isOpen", isOpens));
        }
        return esDiagramDao.selectListByQuery(1, diagramIds.size(), boolQuery);
    }

    @Override
    public List<ESDiagram> selectMyOwnDiagramList(String ownerCode, List<Integer> dirTypes) {
        if (BinaryUtils.isEmpty(ownerCode)) {
            return Collections.emptyList();
        }
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.must(QueryBuilders.termQuery("ownerCode.keyword", ownerCode));
        boolQuery.must(QueryBuilders.termQuery("status", 1));
        boolQuery.must(QueryBuilders.termQuery("isOpen", 0));
        boolQuery.must(QueryBuilders.termQuery("historyVersionFlag", 1));
        boolQuery.must(QueryBuilders.termQuery("dataStatus", 1));
        if (!BinaryUtils.isEmpty(dirTypes)) {
            boolQuery.must(QueryBuilders.termsQuery("dirType", dirTypes));
        }
        return esDiagramDao.selectListByQuery(1, 3000, boolQuery);
    }


    @Override
    public List<ESDiagramDTO> queryDiagramInfoByIds(Long[] diagramIds, String type, Boolean needAuth, Boolean asset) {
        //架构资产打开视图时做特殊处理
        List<Long> diagramIdList = new ArrayList<>(Arrays.asList(diagramIds));
        List<ESDiagramDTO> assetDiagramList = new ArrayList<>();
        Iterator<Long> iterator = diagramIdList.iterator();
        while (iterator.hasNext()) {
            Long diagramId = iterator.next();
            ESDiagram esDiagram = esDiagramDao.getById(diagramId);
            if (esDiagram != null && esDiagram.getNonMonet() != null && esDiagram.getNonMonet() == 1) {
                ESDiagramDTO esDiagramDTO = new ESDiagramDTO();
                ESDiagramInfoDTO diagram = new ESDiagramInfoDTO();
                BeanUtils.copyProperties(esDiagram, diagram);
                esDiagramDTO.setDiagram(diagram);

                Long userId = diagram.getUserId();
                UserInfo userInfo = userApiSvc.getUserInfoById(userId);
                esDiagramDTO.setCreator(userInfo);

                Map<Long, List<ESDiagramShareRecordResult>> diagramShareRecordMap = esShareDiagramSvc.queryDiagramShareRecords(new Long[]{diagramId}, needAuth);
                esDiagramDTO.setShareRecords(diagramShareRecordMap.get(diagramId));
                assetDiagramList.add(esDiagramDTO);

                iterator.remove();
            }
        }
        if (!CollectionUtils.isEmpty(diagramIdList)) {
            diagramIds = new Long[diagramIdList.size()];
            for (int i = 0; i < diagramIdList.size(); i++) {
                diagramIds[i] = diagramIdList.get(i);
            }
        } else {
            return assetDiagramList;
        }

        //1.根据视图id集合查询视图数据
        diagramIds = ArrayUtil.distinct(diagramIds);
        List<ESDiagram> diagramList = judgeDiagramAuth(diagramIds, type, needAuth);
        if (diagramList.size() != diagramIds.length) {
            logger.info("传入的id数量：{},包含元素：{}, 查出的视图数量：{}, 包含元素：{}", diagramIds.length, diagramIds, diagramList.size(), diagramList.stream().map(ESDiagram::getId).toArray(Long[]::new));
            throw new BinaryException("批量查询的视图，部分已删除或没有权限");
        }
        Map<Long, ESDiagram> diagramIdMap = new HashMap<>();
        Set<Long> creatorIdSet = new HashSet<>();
        Set<Long> diagramIdSet = new HashSet<>();
        for (ESDiagram esDiagram : diagramList) {
            diagramIdMap.put(esDiagram.getId(), esDiagram);
            diagramIdSet.add(esDiagram.getId());
            creatorIdSet.add(esDiagram.getUserId());
            String icon1 = esDiagram.getIcon1();
            if (icon1 != null && !icon1.startsWith(httpResourceUrl)) {
                esDiagram.setIcon1(new StringBuilder(httpResourceUrl).append(icon1).toString());
            }
        }
        //2.根据视图id集合查询其包含的所有sheet数据
        ESDiagramSheetQuery sheetQuery = new ESDiagramSheetQuery();
        sheetQuery.setDiagramIds(diagramIds);
        List<ESDiagramSheetDTO> sheetList = esDiagramSheetDao.getListByCdt(sheetQuery);
        String[] sheetIdArr = sheetList.stream().map(ESDiagramSheetDTO::getSheetId).distinct().toArray(String[]::new);
        if (BinaryUtils.isEmpty(sheetIdArr)) {
            throw new BinaryException("错误sheet，没有sheetId");
        }
        //将sheet按照视图id进行分组
        Map<Long, List<ESDiagramSheetDTO>> diagramSheetMap = sheetList
                .stream()
                .collect(Collectors.groupingBy(ESDiagramSheetDTO::getDiagramId));
        //3.查询所有的node节点，将其按照diagramId和sheetId进行分组
        List<ESDiagramNode> nodeList = new ArrayList<>();
        ESDiagramNodeQueryBean nodeQuery = new ESDiagramNodeQueryBean();
        nodeQuery.setDiagramIds(diagramIdSet.toArray(new Long[]{}));
        nodeQuery.setSheetIds(sheetIdArr);
        nodeList = getAllNode(nodeQuery, nodeList);
        Map<Long, Map<String, List<ESDiagramNode>>> diagramSheetNodeMap = new HashMap<>();
        if (!BinaryUtils.isEmpty(nodeList)) {
            Map<Long, List<ESDiagramNode>> diagramNodeMap = nodeList
                    .stream()
                    .collect(Collectors.groupingBy(ESDiagramNode::getDiagramId));
            for (Map.Entry<Long, List<ESDiagramNode>> entry : diagramNodeMap.entrySet()) {
                List<ESDiagramNode> esDiagramNodeList = entry.getValue();
                Map<String, List<ESDiagramNode>> sheetNodeMap = new HashMap<>();
                if (!BinaryUtils.isEmpty(esDiagramNodeList)) {
                    sheetNodeMap = esDiagramNodeList.stream().collect(Collectors.groupingBy(ESDiagramNode::getSheetId));
                }
                diagramSheetNodeMap.put(entry.getKey(), sheetNodeMap);
            }
        }
        //4.查询所有的link节点，将其按照diagramId和sheetId进行分组
        List<ESDiagramLink> linkList = new ArrayList<>();
        ESDiagramLinkQueryBean linkQuery = new ESDiagramLinkQueryBean();
        linkQuery.setDiagramIds(diagramIdSet.toArray(new Long[]{}));
        linkQuery.setSheetIds(sheetIdArr);
        linkList = getAllLink(linkQuery, linkList);
        Map<Long, Map<String, List<ESDiagramLink>>> diagramSheetLinkMap = new HashMap<>();
        if (!BinaryUtils.isEmpty(linkList)) {
            Map<Long, List<ESDiagramLink>> diagramLinkMap = linkList
                    .stream()
                    .collect(Collectors.groupingBy(ESDiagramLink::getDiagramId));
            for (Map.Entry<Long, List<ESDiagramLink>> entry : diagramLinkMap.entrySet()) {
                List<ESDiagramLink> esDiagramLinkList = entry.getValue();
                Map<String, List<ESDiagramLink>> sheetLinkMap = new HashMap<>();
                if (!BinaryUtils.isEmpty(esDiagramLinkList)) {
                    sheetLinkMap = esDiagramLinkList
                            .stream()
                            .collect(Collectors.groupingBy(ESDiagramLink::getSheetId));
                }
                diagramSheetLinkMap.put(entry.getKey(), sheetLinkMap);
            }
        }

        //5.查询视图分享记录，并按照diagramId进行对结果进行分组
        Map<Long, List<ESDiagramShareRecordResult>> diagramShareRecordMap = new HashMap<>();
        if (BinaryUtils.isEmpty(type) ||
                (!"copy".equals(type) && !"FX_D_HISTORY".equals(type) && !"CJ_D_HISTORY".equals(type) && !"SNAPSHOT".equals(type))) {
            diagramShareRecordMap = esShareDiagramSvc.queryDiagramShareRecords(diagramIds, needAuth);
        }
        Map<Long, SysUser> diagramCreatorMap = new HashMap<>();
        if (BinaryUtils.isEmpty(type) || !"copy".equals(type)) {
            //6.查询视图作者信息，并按照userId对结果进行分组
            CSysUser cSysUser = new CSysUser();
            cSysUser.setDomainId(SYS_DOMAIN_ID);
            cSysUser.setIds(creatorIdSet.toArray(new Long[]{}));
            cSysUser.setSuperUserFlags(new Integer[]{0, 1});
            List<SysUser> creatorList = userApiSvc.getSysUserByCdt(cSysUser);
            creatorList.forEach(
                    creator -> {
                        creator.clearSensitive();
                        diagramCreatorMap.put(creator.getId(), creator);
                    }
            );
        }
        //7.封装返回结果
        List<ESDiagramDTO> esDiagramDTOList = new ArrayList<>();
        for (Long diagramId : diagramIdSet) {
            ESDiagramDTO esDiagramDTO = new ESDiagramDTO();
            ESDiagramInfoDTO esDiagramInfoDTO = new ESDiagramInfoDTO();
            //封装视图基础信息
            ESDiagram esDiagram = diagramIdMap.get(diagramId);
            BeanUtils.copyProperties(esDiagram, esDiagramInfoDTO);
            //封装sheetList
            List<ESDiagramSheetDTO> sheetDTOList = diagramSheetMap.get(diagramId);
            List<ESDiagramSheetDTO> sortedSheetDTOList = sheetDTOList.stream().sorted(Comparator.comparing(ESDiagramSheetDTO::getSheetOrder)).collect(Collectors.toList());
            esDiagramInfoDTO.setSheetList(sortedSheetDTOList);
            //封装modelList
            List<ESDiagramModel> modelList = new ArrayList<>();
            for (ESDiagramSheetDTO sheetDTO : sheetDTOList) {
                ESDiagramModel esDiagramModel = new ESDiagramModel();
                String sheetId = sheetDTO.getSheetId();
                if (!BinaryUtils.isEmpty(diagramSheetNodeMap)) {
                    List<ESDiagramNode> nodeDTOList = new ArrayList<>();
                    Map<String, List<ESDiagramNode>> diagramNodeMap = diagramSheetNodeMap.get(diagramId);
                    if (!BinaryUtils.isEmpty(diagramNodeMap)) {
                        nodeDTOList = diagramNodeMap.get(sheetId);
                    }
                    if (!BinaryUtils.isEmpty(nodeDTOList)) {
                        esDiagramModel.setNodeDataArray(nodeDTOList);
                    }
                }
                if (!BinaryUtils.isEmpty(diagramSheetLinkMap)) {
                    List<ESDiagramLink> linkDTOList = new ArrayList<>();
                    Map<String, List<ESDiagramLink>> diagramLinkMap = diagramSheetLinkMap.get(diagramId);
                    if (!BinaryUtils.isEmpty(diagramLinkMap)) {
                        linkDTOList = diagramLinkMap.get(sheetId);
                    }
                    if (!BinaryUtils.isEmpty(linkDTOList)) {
                        esDiagramModel.setLinkDataArray(linkDTOList);
                    }
                }
                esDiagramModel.setDiagramId(diagramId);
                esDiagramModel.setModelData(sheetDTO.getModelData());
                esDiagramModel.setDiagramClass(sheetDTO.getDiagramClass());
                esDiagramModel.setLinkToPortIdProperty(sheetDTO.getLinkToPortIdProperty());
                esDiagramModel.setLinkFromPortIdProperty(sheetDTO.getLinkFromPortIdProperty());
                esDiagramModel.setSheetId(sheetDTO.getSheetId());
                modelList.add(esDiagramModel);
            }
            esDiagramInfoDTO.setModelList(modelList);
            esDiagramDTO.setDiagram(esDiagramInfoDTO);
            //封装视图作者信息
            esDiagramDTO.setCreator(diagramCreatorMap.get(esDiagramInfoDTO.getUserId()));
            //封装视图分享记录信息
            esDiagramDTO.setShareRecords(diagramShareRecordMap.get(diagramId));
            esDiagramDTOList.add(esDiagramDTO);
        }
        return esDiagramDTOList;
    }

    public List<ESDiagramInfoDTO> queryESDiagramInfoByIds(Long[] diagramIds) {
        //1.根据视图id集合查询视图数据
        List<ESDiagram> diagramList = judgeDiagramStatus(diagramIds, null);
        //建立id和diagram的映射关系，以及收集id集合
        Map<Long, ESDiagram> diagramIdMap = new HashMap<>(16);
        Set<Long> diagramIdSet = new HashSet<>();
        diagramList.forEach(
                esDiagram -> {
                    diagramIdMap.put(esDiagram.getId(), esDiagram);
                    diagramIdSet.add(esDiagram.getId());
                }
        );
        Long[] diagramIdArr = diagramIdSet.toArray(new Long[0]);
        //2.根据视图id集合查询其包含的所有sheet数据
        ESDiagramSheetQuery sheetQuery = new ESDiagramSheetQuery();
        sheetQuery.setDiagramIds(diagramIdArr);
        List<ESDiagramSheetDTO> sheetList = esDiagramSheetDao.getListByCdt(sheetQuery);
        String[] sheetIdArr = sheetList
                .stream()
                .map(ESDiagramSheetDTO::getSheetId)
                .distinct()
                .toArray(String[]::new);
        //将sheet按照视图id进行分组
        Map<Long, List<ESDiagramSheetDTO>> diagramSheetMap = sheetList
                .stream()
                .collect(Collectors.groupingBy(ESDiagramSheetDTO::getDiagramId));
        //3.查询所有的node节点，将其按照diagramId和sheetId进行分组
        ESDiagramNodeQueryBean nodeQuery = new ESDiagramNodeQueryBean();
        nodeQuery.setDiagramIds(diagramIdArr);
        nodeQuery.setSheetIds(sheetIdArr);
        List<ESDiagramNode> nodeList = new ArrayList<>();
        nodeList = getAllNode(nodeQuery, nodeList);
        Map<Long, Map<String, List<ESDiagramNode>>> diagramSheetNodeMap = new HashMap<>();
        if (!BinaryUtils.isEmpty(nodeList)) {
            Map<Long, List<ESDiagramNode>> diagramNodeMap = nodeList
                    .stream()
                    .collect(Collectors.groupingBy(ESDiagramNode::getDiagramId));
            for (Map.Entry<Long, List<ESDiagramNode>> entry : diagramNodeMap.entrySet()) {
                List<ESDiagramNode> nodeDTOList = entry.getValue();
                Map<String, List<ESDiagramNode>> sheetNodeMap = new HashMap<>();
                if (!BinaryUtils.isEmpty(nodeDTOList)) {
                    sheetNodeMap = nodeDTOList
                            .stream()
                            .collect(Collectors.groupingBy(ESDiagramNode::getSheetId));
                }
                diagramSheetNodeMap.put(entry.getKey(), sheetNodeMap);
            }
        }
        //4.查询所有的link节点，将其按照diagramId和sheetId进行分组
        ESDiagramLinkQueryBean linkQuery = new ESDiagramLinkQueryBean();
        linkQuery.setDiagramIds(diagramIdArr);
        linkQuery.setSheetIds(sheetIdArr);
        List<ESDiagramLink> linkList = new ArrayList<>();
        linkList = getAllLink(linkQuery, linkList);
        Map<Long, Map<String, List<ESDiagramLink>>> diagramSheetLinkMap = new HashMap<>();
        if (!BinaryUtils.isEmpty(linkList)) {
            Map<Long, List<ESDiagramLink>> diagramLinkMap = linkList
                    .stream()
                    .collect(Collectors.groupingBy(ESDiagramLink::getDiagramId));
            for (Map.Entry<Long, List<ESDiagramLink>> entry : diagramLinkMap.entrySet()) {
                List<ESDiagramLink> esDiagramLinkList = entry.getValue();
                Map<String, List<ESDiagramLink>> sheetLinkMap = new HashMap<>();
                if (!BinaryUtils.isEmpty(esDiagramLinkList)) {
                    sheetLinkMap = esDiagramLinkList
                            .stream()
                            .collect(Collectors.groupingBy(ESDiagramLink::getSheetId));
                }
                diagramSheetLinkMap.put(entry.getKey(), sheetLinkMap);
            }
        }
        //5.封装返回结果
        List<ESDiagramInfoDTO> esDiagramInfoList = new ArrayList<>();
        for (Long diagramId : diagramIdArr) {
            ESDiagramInfoDTO esDiagramInfo = new ESDiagramInfoDTO();
            //封装视图基础信息
            ESDiagram esDiagram = diagramIdMap.get(diagramId);
            BeanUtils.copyProperties(esDiagram, esDiagramInfo);
            if (esDiagram.getNonMonet() == null || esDiagram.getNonMonet() != 1) {
                //封装排序sheetList
                List<ESDiagramSheetDTO> sheetDTOList = diagramSheetMap.get(diagramId);
                List<ESDiagramSheetDTO> sortedSheetDTOList = sheetDTOList
                        .stream()
                        .sorted(Comparator.comparing(ESDiagramSheetDTO::getSheetOrder))
                        .collect(Collectors.toList());
                esDiagramInfo.setSheetList(sortedSheetDTOList);
                //封装modelList
                List<ESDiagramModel> modelList = new ArrayList<>();
                for (ESDiagramSheetDTO sheetDTO : sheetDTOList) {
                    ESDiagramModel esDiagramModel = new ESDiagramModel();
                    String sheetId = sheetDTO.getSheetId();
                    if (!BinaryUtils.isEmpty(diagramSheetNodeMap)) {
                        Map<String, List<ESDiagramNode>> sheetNodeMap = diagramSheetNodeMap.get(diagramId);
                        List<ESDiagramNode> nodeDTOList = new ArrayList<>();
                        if (!BinaryUtils.isEmpty(sheetNodeMap)) {
                            nodeDTOList = sheetNodeMap.get(sheetId);
                        }
                        esDiagramModel.setNodeDataArray(nodeDTOList);
                    }
                    if (!BinaryUtils.isEmpty(diagramSheetLinkMap)) {
                        Map<String, List<ESDiagramLink>> sheetLinkMap = diagramSheetLinkMap.get(diagramId);
                        List<ESDiagramLink> linkDTOList = new ArrayList<>();
                        if (!BinaryUtils.isEmpty(sheetLinkMap)) {
                            linkDTOList = sheetLinkMap.get(sheetId);
                        }
                        esDiagramModel.setLinkDataArray(linkDTOList);
                    }
                    esDiagramModel.setDiagramId(diagramId);
                    esDiagramModel.setModelData(sheetDTO.getModelData());
                    esDiagramModel.setDiagramClass(sheetDTO.getDiagramClass());
                    esDiagramModel.setLinkToPortIdProperty(sheetDTO.getLinkToPortIdProperty());
                    esDiagramModel.setLinkFromPortIdProperty(sheetDTO.getLinkFromPortIdProperty());
                    esDiagramModel.setSheetId(sheetDTO.getSheetId());
                    modelList.add(esDiagramModel);
                }
                esDiagramInfo.setModelList(modelList);
            }
            esDiagramInfoList.add(esDiagramInfo);
        }
        return esDiagramInfoList;
    }

    @Override
    public ESDiagramDTO queryESDiagramInfoById(Long diagramId, String type, boolean versionFlag) {
        ESDiagram esDiagram = new ESDiagram();
        ESDiagramInfoDTO diagramInfo = new ESDiagramInfoDTO();
        if (!BinaryUtils.isEmpty(type) && "shareLink".equals(type)) {
            //分享链接查询，不需要鉴权
            logger.info("分享链接查询，无需鉴权");
            esDiagram = judgeSingleDiagramAuth(diagramId, null, false);
        } else {
            //历史版本查询
            if (versionFlag) {
                if (!BinaryUtils.isEmpty(type) && "BM_D_HISTORY".equals(type)) {
                    esDiagram = judgeSingleDiagramAuth(diagramId, "BM_D_HISTORY", false);
                } else {
                    esDiagram = judgeSingleDiagramAuth(diagramId, "version", false);
                }
            } else {
                esDiagram = judgeSingleDiagramAuth(diagramId, null, true);
            }
        }
        BeanUtils.copyProperties(esDiagram, diagramInfo);
        //2.查询视图包含的sheet数据
        ESDiagramSheetQuery sheetQuery = new ESDiagramSheetQuery();
        sheetQuery.setDiagramId(diagramId);
        List<ESDiagramSheetDTO> sheetList = esDiagramSheetDao.getListByCdt(sheetQuery);
        if (ObjectUtil.isEmpty(sheetList)) {
            throw new BinaryException("查询视图不包含sheet，请联系管理员");
        }
        List<ESDiagramSheetDTO> sortedSheetList = sheetList
                .stream()
                .sorted(Comparator.comparing(ESDiagramSheetDTO::getSheetOrder))
                .collect(Collectors.toList());
        diagramInfo.setSheetList(sortedSheetList);
        String[] sheetIdArr = sheetList.stream().map(ESDiagramSheetDTO::getSheetId).distinct().toArray(String[]::new);
        //3.查询sheet包含的node和link数据，并将结果封装到modelList中
        List<ESDiagramModel> modelList = new ArrayList<>();
        //查出所有的node数据，并按照sheetId进行分组
        ESDiagramNodeQueryBean nodeQuery = new ESDiagramNodeQueryBean();
        nodeQuery.setDiagramId(diagramId);
        nodeQuery.setSheetIds(sheetIdArr);
        List<ESDiagramNode> nodeList = new ArrayList<>();
        nodeList = getAllNode(nodeQuery, nodeList);
        Map<String, List<ESDiagramNode>> sheetNodeMap = nodeList
                .stream()
                .collect(Collectors.groupingBy(ESDiagramNode::getSheetId));
        //查出所有的link数据，并按照sheetId进行分组
        ESDiagramLinkQueryBean linkQuery = new ESDiagramLinkQueryBean();
        linkQuery.setDiagramId(diagramId);
        linkQuery.setSheetIds(sheetIdArr);
        List<ESDiagramLink> linkList = new ArrayList<>();
        linkList = getAllLink(linkQuery, linkList);
        Map<String, List<ESDiagramLink>> linkNodeMap = linkList
                .stream()
                .collect(Collectors.groupingBy(ESDiagramLink::getSheetId));
        for (ESDiagramSheetDTO sheetDTO : sheetList) {
            ESDiagramModel tempModel = new ESDiagramModel();
            //根据sheetId查出对应的nodeDTOList和linkDTOList
            String sheetId = sheetDTO.getSheetId();
            List<ESDiagramNode> nodeDTOList = sheetNodeMap.get(sheetId);
            List<ESDiagramLink> linkDTOList = linkNodeMap.get(sheetId);
            //填充model
            tempModel.setNodeDataArray(nodeDTOList);
            tempModel.setLinkDataArray(linkDTOList);
            tempModel.setDiagramId(diagramId);
            tempModel.setSheetId(sheetId);
            tempModel.setDiagramClass(sheetDTO.getDiagramClass());
            tempModel.setLinkFromPortIdProperty(sheetDTO.getLinkFromPortIdProperty());
            tempModel.setLinkToPortIdProperty(sheetDTO.getLinkToPortIdProperty());
            tempModel.setModelData(sheetDTO.getModelData());
            modelList.add(tempModel);
        }
        diagramInfo.setModelList(modelList);
        //封装最后的返回信息
        ESDiagramDTO responseDTO = new ESDiagramDTO();
        responseDTO.setDiagram(diagramInfo);
        //type=copy代表是复制视图操作，复制视图只复制视图基础信息和组件信息
        if (BinaryUtils.isEmpty(type) || (!"copy".equals(type) && !"version".equals(type) && !"shareLink".equals(type) && !"BM_D_HISTORY".equals(type))) {
            //5.查询视图分享记录，并按照diagramId进行对结果进行分组
            Map<Long, List<ESDiagramShareRecordResult>> diagramShareRecordMap = esShareDiagramSvc.queryDiagramShareRecords(new Long[]{diagramId}, false);
            //6.查询视图作者信息，并按照userId对结果进行分组
            CSysUser cSysUser = new CSysUser();
            cSysUser.setDomainId(SYS_DOMAIN_ID);
            Long userId;
            SysUser sysUser = ThreadVariable.getSysUser();
            if (sysUser != null) {
                userId = sysUser.getId();
            } else {
                SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
                userId = currentUserInfo.getId();
            }
            cSysUser.setId(userId);
            cSysUser.setSuperUserFlags(new Integer[]{0, 1});
            List<SysUser> creatorList = userApiSvc.getSysUserByCdt(cSysUser);
            responseDTO.setShareRecords(diagramShareRecordMap.get(diagramId));
            responseDTO.setCreator(creatorList.get(0));
        }
        return responseDTO;
    }

    @Override
    public Page<ESDiagram> queryESDiagramPage(Long domainId, Integer pageNum, Integer pageSize, CVcDiagram cVcDiagram, String orders) {
        //根据查询条件查询所有基础视图
        String like = cVcDiagram.getName();
        MessageUtil.checkEmpty(domainId, "domainId");
        EamDiagramQuery esDiagramForQuery = new EamDiagramQuery();
        BeanUtils.copyProperties(cVcDiagram, esDiagramForQuery);
        esDiagramForQuery.setStatus(1);
        esDiagramForQuery.setDataStatus(1);
        esDiagramForQuery.setHistoryVersionFlag(1);
        esDiagramForQuery.setName(null);
        BoolQueryBuilder boolQueryBuilder = ESUtil.cdtToBuilder(esDiagramForQuery);
        if (!StringUtils.isEmpty(like)) {
            boolQueryBuilder.must(QueryBuilders.multiMatchQuery(like, "name")
                    .operator(Operator.AND).type(MultiMatchQueryBuilder.Type.PHRASE_PREFIX).lenient(true));
        }
        //查询视图信息并进行填充
        orders = BinaryUtils.isEmpty(orders) ? "modifyTime" : orders;
        orders = ESUtil.underlineToCamel(orders);
        Page<ESDiagram> esDiagramPage = esDiagramDao.getSortListByQuery(pageNum, pageSize, boolQueryBuilder, orders, false);
        List<ESDiagram> esDiagramList = esDiagramPage.getData();
        if (!BinaryUtils.isEmpty(esDiagramPage.getData())) {
            esDiagramList.forEach(diagram -> {
                // 更新缩略图地址
                String icon1 = diagram.getIcon1();
                if (icon1 != null && !icon1.startsWith(httpResourceUrl)) {
                    StringBuilder stringBuilder = new StringBuilder();
                    stringBuilder.append(httpResourceUrl);
                    stringBuilder.append(icon1);
//                icon1 = httpResourceUrl + icon1;
                    diagram.setIcon1(stringBuilder.toString());
                }
            });
        }
        return esDiagramPage;
    }

    @Override
    public List<ESDiagram> queryESDiagramList(Long domainId, CVcDiagram cVcDiagram, String orders) {
        MessageUtil.checkEmpty(domainId, "domainId");
        cVcDiagram = (cVcDiagram == null) ? new CVcDiagram() : cVcDiagram;
        EamDiagramQuery esDiagramForQuery = new EamDiagramQuery();
        BeanUtils.copyProperties(cVcDiagram, esDiagramForQuery);
        esDiagramForQuery.setDataStatus(1);
        esDiagramForQuery.setName(null);
        esDiagramForQuery.setHistoryVersionFlag(1);
        BoolQueryBuilder boolQueryBuilder = ESUtil.cdtToBuilder(esDiagramForQuery);
        String keyword = cVcDiagram.getName();
        if (!StringUtils.isEmpty(keyword)) {
            boolQueryBuilder.must(QueryBuilders.wildcardQuery("name.keyword", "*" + keyword.trim() + "*"));
        }
        return esDiagramDao.getListByQuery(boolQueryBuilder);
    }


    @Override
    public Page<VcDiagramInfo> queryDiagramInfoPage(Long domainId, Integer pageNum, Integer pageSize, CVcDiagram cVcDiagram, String orders) {
        //1.构建查询条件，查询视图详情
        String like = cVcDiagram.getName();
        MessageUtil.checkEmpty(domainId, "domainId");
        EamDiagramQuery esDiagramForQuery = new EamDiagramQuery();
        BeanUtils.copyProperties(cVcDiagram, esDiagramForQuery);
        esDiagramForQuery.setDataStatus(1);
        esDiagramForQuery.setHistoryVersionFlag(1);
        esDiagramForQuery.setName(null);
        BoolQueryBuilder boolQueryBuilder = ESUtil.cdtToBuilder(esDiagramForQuery);
        if (!StringUtils.isEmpty(like)) {
            boolQueryBuilder.must(QueryBuilders.wildcardQuery("name.keyword", "*" + like.trim() + "*"));
        }
        orders = BinaryUtils.isEmpty(orders) ? "modifyTime" : orders;
        orders = ESUtil.underlineToCamel(orders);
        Page<ESDiagram> esDiagramPage = esDiagramDao.getSortListByQuery(pageNum, pageSize, boolQueryBuilder, orders, false);
        List<ESDiagram> esDiagramList = esDiagramPage.getData();
        if (!BinaryUtils.isEmpty(esDiagramList)) {
            for (ESDiagram esDiagram : esDiagramList) {
                String icon1 = esDiagram.getIcon1();
                if (icon1 != null && !icon1.startsWith(httpResourceUrl)) {
                    StringBuilder stringBuilder = new StringBuilder();
                    stringBuilder.append(httpResourceUrl);
                    stringBuilder.append(icon1);
//                icon1 = httpResourceUrl + icon1;
                    esDiagram.setIcon1(stringBuilder.toString());
                }
            }
        }
        if (BinaryUtils.isEmpty(esDiagramList)) {
            esDiagramList = new ArrayList<>();
        }
        //2.根据视图作者id查询作者信息
        Map<Long, SysUser> userIdObjMap = new HashMap<>();
        if (!BinaryUtils.isEmpty(esDiagramList)) {
            Set<Long> creatorIdSet = new HashSet<>();
            esDiagramList.forEach(
                    esDiagram -> {
                        if (!BinaryUtils.isEmpty(esDiagram.getUserId())) {
                            creatorIdSet.add(esDiagram.getUserId());
                        }
                    }
            );
            userIdObjMap = queryUserList(creatorIdSet);
        }
        //3.封装返回结果
        Page<VcDiagramInfo> vcDiagramInfoPage = new Page<>();
        List<VcDiagramInfo> vcDiagramInfoList = new ArrayList<>();
        if (!BinaryUtils.isEmpty(esDiagramList)) {
            for (ESDiagram esDiagram : esDiagramList) {
                VcDiagram vcDiagram = new VcDiagram();
                VcDiagramInfo vcDiagramInfo = new VcDiagramInfo();
                BeanUtils.copyProperties(esDiagram, vcDiagram);
                vcDiagramInfo.setDiagram(vcDiagram);
                vcDiagramInfo.setCreator(userIdObjMap.get(esDiagram.getUserId()));
                vcDiagramInfoList.add(vcDiagramInfo);
            }
        }
        BeanUtils.copyProperties(esDiagramPage, vcDiagramInfoPage);
        vcDiagramInfoPage.setData(vcDiagramInfoList);
        //4.返回结果
        return vcDiagramInfoPage;
    }

    @Override
    public Page<ESSimpleDiagramDTO> queryESDiagramInfoPage(Long domainId, Integer pageNum, Integer pageSize, CVcDiagram cVcDiagram, String orders) {
        //1.构建查询条件，查询视图详情
        MessageUtil.checkEmpty(domainId, "domainId");
        cVcDiagram = cVcDiagram == null ? new CVcDiagram() : cVcDiagram;
        String like = cVcDiagram.getName();
        EamDiagramQuery esDiagramForQuery = new EamDiagramQuery();
        BeanUtils.copyProperties(cVcDiagram, esDiagramForQuery);
        esDiagramForQuery.setDataStatus(1);
        esDiagramForQuery.setHistoryVersionFlag(1);
        esDiagramForQuery.setName(null);
        String releaseDiagramId = esDiagramForQuery.getReleaseDiagramId();
        esDiagramForQuery.setReleaseDiagramId(null);
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        if (!StringUtils.isEmpty(like)) {
            boolQuery.must(QueryBuilders.wildcardQuery("name.keyword", "*" + like.trim() + "*"));
        }
        BoolQueryBuilder boolQueryBuilder1 = QueryBuilders.boolQuery();
        if (!BinaryUtils.isEmpty(cVcDiagram.getCooperateIds())) {
            boolQueryBuilder1.should(QueryBuilders.termsQuery("id", cVcDiagram.getCooperateIds()));
        }
        BoolQueryBuilder boolQueryBuilder = ESUtil.cdtToBuilder(esDiagramForQuery);
        if (!StringUtils.isEmpty(releaseDiagramId)) {
            boolQueryBuilder.must(QueryBuilders.termQuery("releaseDiagramId.keyword", releaseDiagramId));
        }
        boolQueryBuilder1.should(boolQueryBuilder);
        boolQuery.must(boolQueryBuilder1);
        if (!BinaryUtils.isEmpty(cVcDiagram.getDiagramSubType())) {
            boolQuery.must(QueryBuilders.termQuery("diagramType", cVcDiagram.getDiagramType()));
        }
        if (!BinaryUtils.isEmpty(cVcDiagram.getDiagramSubType())) {
            boolQuery.must(QueryBuilders.termQuery("diagramSubType", cVcDiagram.getDiagramSubType()));
        }
        orders = BinaryUtils.isEmpty(orders) ? "modifyTime" : orders;
        orders = ESUtil.underlineToCamel(orders);
        List<SortBuilder<?>> sorts = new LinkedList<>();
        sorts.add(SortBuilders.fieldSort("viewType.keyword").order(SortOrder.DESC));
        sorts.add(SortBuilders.fieldSort(orders).order(SortOrder.DESC));
        Page<ESDiagram> esDiagramPage = esDiagramDao.getSortListByQuery(pageNum, pageSize, boolQuery, sorts);
        //2.填充缩略图路径信息
        List<ESDiagram> esDiagramList = esDiagramPage.getData();
        Set<Long> creatorIdSet = new HashSet<>();
        Set<Long> diagramIdSet = new HashSet<>();
        if (!BinaryUtils.isEmpty(esDiagramList)) {
            for (ESDiagram esDiagram : esDiagramList) {
                String icon1 = esDiagram.getIcon1();
                if (icon1 != null && !icon1.startsWith(httpResourceUrl)) {
                    StringBuilder stringBuilder = new StringBuilder();
                    stringBuilder.append(httpResourceUrl);
                    stringBuilder.append(icon1);
                    esDiagram.setIcon1(stringBuilder.toString());
                }
                String diagramBgImg = esDiagram.getDiagramBgImg();
                if (diagramBgImg != null && !BinaryUtils.isEmpty(diagramBgImg.trim())
                        && !diagramBgImg.startsWith(httpResourceUrl)) {
                    esDiagram.setDiagramBgImg(new StringBuilder(httpResourceUrl).append(diagramBgImg).toString());
                }
                Long userId = esDiagram.getUserId();
                Long diagramId = esDiagram.getId();
                if (!BinaryUtils.isEmpty(userId)) {
                    creatorIdSet.add(esDiagram.getUserId());
                }
                if (!BinaryUtils.isEmpty(diagramId)) {
                    diagramIdSet.add(esDiagram.getId());
                }
            }
        }
        //3.根据视图作者id查询作者信息
        if (BinaryUtils.isEmpty(esDiagramList)) {
            esDiagramList = new ArrayList<>();
        }
        Map<Long, SysUser> userIdObjMap = new HashMap<>();
        if (!BinaryUtils.isEmpty(creatorIdSet)) {
            userIdObjMap = queryUserList(creatorIdSet);
        }
        //4.查询视图的分享记录
        Map<Long, List<ESDiagramShareRecordResult>> diagramShareRecordMap = new HashMap<>();
        if (!BinaryUtils.isEmpty(diagramIdSet)) {
            diagramShareRecordMap = esShareDiagramSvc.queryDiagramShareRecords(diagramIdSet.toArray(new Long[0]), false);
        }
        //5.封装返回结果
        Page<ESSimpleDiagramDTO> esSimpleDiagramDTOPage = new Page<>();
        List<ESSimpleDiagramDTO> esSimpleDiagramDTOList = new ArrayList<>();
        if (!BinaryUtils.isEmpty(esDiagramList)) {
            for (ESDiagram esDiagram : esDiagramList) {
                ESSimpleDiagramDTO esSimpleDiagram = new ESSimpleDiagramDTO();
                esSimpleDiagram.setDiagram(esDiagram);
                esSimpleDiagram.setCreator(userIdObjMap.get(esDiagram.getUserId()));
                esSimpleDiagram.setShareRecords(diagramShareRecordMap.get(esDiagram.getId()));
                esSimpleDiagramDTOList.add(esSimpleDiagram);
            }
        }
        esSimpleDiagramDTOPage.setPageNum(esDiagramPage.getPageNum());
        esSimpleDiagramDTOPage.setPageSize(esDiagramPage.getPageSize());
        esSimpleDiagramDTOPage.setTotalPages(esDiagramPage.getTotalPages());
        esSimpleDiagramDTOPage.setTotalRows(esDiagramPage.getTotalRows());
        esSimpleDiagramDTOPage.setData(esSimpleDiagramDTOList);
        return esSimpleDiagramDTOPage;
    }

    /**
     * 将设计库资产备份历史版本
     *
     * @param diagramMoveCdt
     * @return
     */
    private String copyBmHistoryDiagramById(ESDiagramMoveCdt diagramMoveCdt) {
        String referDiagramId = diagramMoveCdt.getDiagramId();//目标视图id
        Long newDirId = diagramMoveCdt.getNewDirId();
        Long newDiagramId = diagramMoveCdt.getCopyDiagramId();
        Long aLong = queryDiagramInfoByEnergy(referDiagramId);
        ESDiagramInfoDTO esDiagramInfo = queryESDiagramInfoById(aLong, "BM_D_HISTORY", true).getDiagram();
        esDiagramInfo.setNewCopyId(newDiagramId);
        if (diagramMoveCdt.isCreateHistory()) {
            esDiagramInfo.setStatus(0);
        }
        if (!BinaryUtils.isEmpty(newDirId)) {
            esDiagramInfo.setDirId(newDirId);
        }
        //初始化localVersion
        esDiagramInfo.setLocalVersion(0);
        // 更新视图的
        esDiagramInfo.setDEnergy(SecureUtil.md5(String.valueOf(newDiagramId)).substring(8, 24));
        //根据视图类型判断调用的创建方法
        return copyDiagram(diagramMoveCdt, newDirId, esDiagramInfo);
    }


    /**
     * 首次将私有库视图发布设计库，复制视图修改状态
     *
     * @param diagramMoveCdt
     * @return
     */
    private String copyBmFirstReleaseDiagramById(ESDiagramMoveCdt diagramMoveCdt) {
        String referDiagramId = diagramMoveCdt.getDiagramId();//目标视图id
        Long newDirId = diagramMoveCdt.getNewDirId();
        Long newDiagramId = diagramMoveCdt.getCopyDiagramId();
        Long aLong = queryDiagramInfoByEnergy(referDiagramId);
        ESDiagramInfoDTO esDiagramInfo = queryESDiagramInfoById(aLong, "copy", false).getDiagram();
        esDiagramInfo.setNewCopyId(newDiagramId);
        Long createTime = diagramMoveCdt.getCreateTime();
        if (diagramMoveCdt.isCreateHistory()) {
            esDiagramInfo.setStatus(0);
        }
        if (!BinaryUtils.isEmpty(newDirId)) {
            esDiagramInfo.setDirId(newDirId);
        }
        if (!BinaryUtils.isEmpty(createTime)) {
            // 在视图发布的时候 需要让发布的视图的创建时间等于本地视图的创建时间
            esDiagramInfo.setCreateTime(createTime);
        }
        //初始化localVersion
        esDiagramInfo.setLocalVersion(0);
        esDiagramInfo.setOpenTime(BinaryUtils.getNumberDateTime());
        esDiagramInfo.setIsOpen(diagramMoveCdt.getIsOpen());
        esDiagramInfo.setReleaseDesc(diagramMoveCdt.getReleaseDesc());
        // 更新视图的
        esDiagramInfo.setDEnergy(diagramMoveCdt.getReleaseDiagramId());
        esDiagramInfo.setReleaseDiagramId(diagramMoveCdt.getReleaseDiagramId());
        //根据视图类型判断调用的创建方法
        return copyDiagram(diagramMoveCdt, newDirId, esDiagramInfo);
    }

    private String copyDiagram(ESDiagramMoveCdt diagramMoveCdt, Long newDirId, ESDiagramInfoDTO esDiagramInfo) {
        Long newDiagramId;
        if (esDiagramInfo.getDiagramType() != 1) {
            //根据模板新建
            // 基于模板转化ownerCode处理
            esDiagramInfo.setOwnerCode(null);
            newDiagramId = saveESDiagram(esDiagramInfo, null, newDirId, "template");
        } else {
            //根据普通视图新建
            newDiagramId = saveESDiagram(esDiagramInfo, null, null, "copy");
        }
        //创建视图的历史版本记录
        if (!BinaryUtils.isEmpty(newDiagramId) && diagramMoveCdt.isCreateHistoryVersion()) {
            Long[] diagramIdArray = new Long[]{newDiagramId};
            createESDiagramVersionRecord(diagramIdArray);
        }
        return SecureUtil.md5(String.valueOf(newDiagramId)).substring(8, 24);
    }

    @Override
    public String copyDiagramById(ESDiagramMoveCdt diagramMoveCdt) {
        String referDiagramId = diagramMoveCdt.getDiagramId();//目标视图id
        String newName = diagramMoveCdt.getNewName();//新视图名称
        Long newDirId = diagramMoveCdt.getNewDirId();//新视图所属目录
        String prepareDiagramId = diagramMoveCdt.getPrepareDiagramId(); //新视图的业务主键
        String ownerCode = diagramMoveCdt.getOwnerCode();   // 视图创建人
        Long createTime = diagramMoveCdt.getCreateTime();   // 发布视图的创建时间
        Long newDiagramId = diagramMoveCdt.getCopyDiagramId();
        String releaseDesc = diagramMoveCdt.getReleaseDesc();
        Integer dirType = diagramMoveCdt.getDirType();
        Long aLong = queryDiagramInfoByEnergy(referDiagramId);
        ESDiagramInfoDTO esDiagramInfo = queryESDiagramInfoById(aLong, "copy", false).getDiagram();
        esDiagramInfo.setNewCopyId(newDiagramId);
        if (!BinaryUtils.isEmpty(releaseDesc)) {
            esDiagramInfo.setReleaseDesc(releaseDesc);
        }
        if (!BinaryUtils.isEmpty(newName)) {
            esDiagramInfo.setName(newName);
        }
        if (!BinaryUtils.isEmpty(newDirId)) {
            esDiagramInfo.setDirId(newDirId);
        }
        if (!BinaryUtils.isEmpty(diagramMoveCdt.getViewType())) {
            esDiagramInfo.setViewType(diagramMoveCdt.getViewType());
        }
        if (!BinaryUtils.isEmpty(createTime)) {
            // 在视图发布的时候 需要让发布的视图的创建时间等于本地视图的创建时间
            esDiagramInfo.setCreateTime(createTime);
        }
        if (!BinaryUtils.isEmpty(prepareDiagramId)) {
            // 检出另存为需要重置视图的业务主键
            esDiagramInfo.setPrepareDiagramId(prepareDiagramId);
        }
        if (!BinaryUtils.isEmpty(ownerCode)) {
            // 检出需重置创建人
            esDiagramInfo.setOwnerCode(ownerCode);
        }
        if(!BinaryUtils.isEmpty(dirType)){
            esDiagramInfo.setDirType(dirType);
        }
        // copy出的视图流程状态默认未 0
        esDiagramInfo.setFlowStatus(0);
        if (!BinaryUtils.isEmpty(diagramMoveCdt.getIsOpen())) {
            esDiagramInfo.setIsOpen(diagramMoveCdt.getIsOpen());
            if (diagramMoveCdt.getIsOpen() == 1) {
                esDiagramInfo.setOpenTime(BinaryUtils.getNumberDateTime());
            } else {
                esDiagramInfo.setOpenTime(null);
            }
        }
        if (!BinaryUtils.isEmpty(diagramMoveCdt.getReleaseDiagramId())) {
            esDiagramInfo.setReleaseDiagramId(diagramMoveCdt.getReleaseDiagramId());
        }
        if (diagramMoveCdt.isCreateHistory()) {
            esDiagramInfo.setStatus(0);
        }
        esDiagramInfo.setLocalVersion(0);
        //初始化localVersion
        esDiagramInfo.setLocalVersion(0);
        // 将releaseDiagramId字段清空
        esDiagramInfo.setReleaseDiagramId(null);
        //设置视图类型
        if (!BinaryUtils.isEmpty(diagramMoveCdt.getDiagramSubType())) {
            int subType = diagramMoveCdt.getDiagramSubType() == 4 ? 2 : diagramMoveCdt.getDiagramSubType();
            esDiagramInfo.setDiagramSubType(subType);
        }
        //根据视图类型判断调用的创建方法
        return copyDiagram(diagramMoveCdt, newDirId, esDiagramInfo);
    }

    @Override
    public List<Long> copyDiagramByIds(ESDiagramMoveCdt diagramMoveCdt) {
        List<String> referDiagramIdList = diagramMoveCdt.getDiagramIds();
        String newName = diagramMoveCdt.getNewName();
        Long newDirId = diagramMoveCdt.getNewDirId();
        if (BinaryUtils.isEmpty(referDiagramIdList)) {
            throw new BinaryException("目标视图id不能为空");
        }
        List<Long> diagramIdList;
        //根据视图id集合查询视图详细信息
        Long[] longs = queryDiagramInfoBydEnergy(referDiagramIdList.toArray(new String[0]));
        List<ESDiagramInfoDTO> esDiagramInfoList = queryESDiagramInfoByIds(longs);
        for (ESDiagramInfoDTO esDiagramInfoDTO : esDiagramInfoList) {
            if (esDiagramInfoDTO.getNonMonet() == null || esDiagramInfoDTO.getNonMonet() != 1) {
                esDiagramInfoDTO.setDiagramType(1);
            }
            esDiagramInfoDTO.setReleaseDiagramId(null);
            esDiagramInfoDTO.setReleaseDesc(null);
            esDiagramInfoDTO.setReleaseVersion(0);
            esDiagramInfoDTO.setDiagramSubType(null);
            //如果是由老视图复制，需要手动去除老视图转换标识
            esDiagramInfoDTO.setTransFlag(null);
            // 将releaseDiagramId字段置为null
            esDiagramInfoDTO.setReleaseDiagramId(null);
            esDiagramInfoDTO.setCreateTime(System.currentTimeMillis());
        }
        //批量复制视图，可以指定名称和路径
        diagramIdList = saveESDiagramBatch(esDiagramInfoList, newName, newDirId, "copy");
        if (!BinaryUtils.isEmpty(diagramIdList)) {
            createESDiagramVersionRecord(diagramIdList.toArray(new Long[0]));
        }
        return diagramIdList;
    }

    private List<Long> copyDiagramByIds(ESDiagramMoveCdt diagramMoveCdt, Map<String, String> sourceEnergyAndNewEnergy) {
        List<String> referDiagramIdList = diagramMoveCdt.getDiagramIds();
        String newName = diagramMoveCdt.getNewName();
        Long newDirId = diagramMoveCdt.getNewDirId();
        if (BinaryUtils.isEmpty(referDiagramIdList)) {
            throw new BinaryException("目标视图id不能为空");
        }
        List<Long> diagramIdList;
        //根据视图id集合查询视图详细信息
        Long[] longs = queryDiagramInfoBydEnergy(referDiagramIdList.toArray(new String[0]));
        List<ESDiagramInfoDTO> esDiagramInfoList = queryESDiagramInfoByIds(longs);
        for (ESDiagramInfoDTO esDiagramInfoDTO : esDiagramInfoList) {
            if (esDiagramInfoDTO.getNonMonet() == null || esDiagramInfoDTO.getNonMonet() != 1) {
                esDiagramInfoDTO.setDiagramType(1);
            }
            //如果是由老视图复制，需要手动去除老视图转换标识
            esDiagramInfoDTO.setTransFlag(null);
            // 将releaseDiagramId字段置为null
            esDiagramInfoDTO.setReleaseDiagramId(null);
            esDiagramInfoDTO.setReleaseDesc(null);
            esDiagramInfoDTO.setReleaseVersion(0);
            esDiagramInfoDTO.setDiagramSubType(null);
            esDiagramInfoDTO.setCreateTime(System.currentTimeMillis());
        }
        //批量复制视图，可以指定名称和路径
        diagramIdList = saveESDiagramBatch(esDiagramInfoList, newName, newDirId, "copy", sourceEnergyAndNewEnergy);
        if (!BinaryUtils.isEmpty(diagramIdList)) {
            createESDiagramVersionRecord(diagramIdList.toArray(new Long[0]));
        }
        return diagramIdList;
    }

    /**
     * <AUTHOR>
     * @Description 更新指定视图，或者以指定视图新建视图
     * @Date 9:59 2021/8/18
     * @Param [esDiagramInfo, newName, newDirId, type]
     * @Return 新建或更新的视图id
     **/
    public Long saveESDiagram(ESDiagramInfoDTO esDiagramInfo, String newName, Long newDirId, String type) {
        MessageUtil.checkEmpty(esDiagramInfo.getName(), "diagramName");
        Long newDiagramId = esDiagramInfo.getNewCopyId();
        List<ESDiagramSheetDTO> sheetList = esDiagramInfo.getSheetList();
        List<ESDiagramModel> modelList = esDiagramInfo.getModelList();
        Map<String, List<ESDiagramModel>> sheetModelMap = modelList.stream().collect(Collectors.groupingBy(ESDiagramModel::getSheetId));
        if (BinaryUtils.isEmpty(newDiagramId)) {
            newDiagramId = IdGenerator.createGenerator().getID();
        }
        ESDiagram esDiagram = CommUtil.copy(esDiagramInfo, ESDiagram.class);
        if (!BinaryUtils.isEmpty(esDiagramInfo.getOwnerCode())) {
            // 传参时取参数的值 不传参取当前用户
            esDiagram.setOwnerCode(esDiagramInfo.getOwnerCode());
        } else {
            esDiagram.setOwnerCode(SysUtil.getCurrentUserInfo().getLoginCode());
        }
        // 兼容工作流无法携带用户信息
        if (BinaryUtils.isEmpty(esDiagramInfo.getCreator())) {
            esDiagram.setCreator(SysUtil.getCurrentUserInfo().getLoginCode());
        }
        //重新生成缩略图
        String icon1 = esDiagram.getIcon1();
        String iconPath = "";
        try {
            if (!StringUtils.isEmpty(icon1)) {
                rsmUtils.downloadRsmAndUpdateLocalRsm(icon1);
                String path = icon1.replace(httpResourceUrl, "");
                StringBuilder stringBuilder = new StringBuilder();
                stringBuilder.append(localPath);
                stringBuilder.append(path);
                byte[] iconBytes = FileUtils.readFileToByteArray(new File(FileFilterUtil.parseFilePath(stringBuilder.toString())));
                iconPath = this.saveOrUpdateResource(new StringBuilder(UUID.randomUUID().toString()).append(".png").toString(), iconBytes, true);
            }
        } catch (Exception e) {
            logger.error("读取文件失败:", e);
        }
        if (!StringUtils.isEmpty(iconPath)) {
            esDiagram.setIcon1(iconPath);
        }
        //全量更新不需要重新设置视图基础信息
        if (!BinaryUtils.isEmpty(type) && "updateFull".equals(type)) {
            newDiagramId = esDiagramInfo.getId();
            esDiagram.setModifier(SysUtil.getCurrentUserInfo().getLoginCode());
            esDiagram.setOwnerCode(SysUtil.getCurrentUserInfo().getLoginCode());
        } else {
            //设置视图的默认信息
            esDiagram.setId(newDiagramId);
            //老视图复制的视图需要去掉老视图标识
            Integer transFlag = esDiagram.getTransFlag();
            if (!BinaryUtils.isEmpty(transFlag) && transFlag == 0L) {
                esDiagram.setTransFlag(null);
            }
            setDiagramBaseInfo(esDiagram);
        }
        if (!BinaryUtils.isEmpty(newName)) {
            esDiagram.setName(newName);
        }
        if (!BinaryUtils.isEmpty(newDirId)) {
            esDiagram.setDirId(newDirId);
        }
        //根据模板新建视图
        if (!BinaryUtils.isEmpty(type) && "template".equals(type)) {
            esDiagram.setDiagramType(1);
            // 使用模板时发布的本地视图 releaseDiagram 字段清空
            esDiagram.setReleaseDiagramId(null);
        }
        //获取待保存的sheet、dao、link集合
        List<ESDiagramNode> saveNodeList = new ArrayList<>();
        List<ESDiagramLink> saveLinkList = new ArrayList<>();
        for (ESDiagramSheetDTO sheetDTO : sheetList) {
            String sheetId = sheetDTO.getSheetId();
            ESDiagramModel diagramModel = sheetModelMap.get(sheetId).get(0);
            sheetDTO.setId(IdGenerator.createGenerator().getID());
            sheetDTO.setDiagramId(newDiagramId);
            if (BinaryUtils.isEmpty(type) || (!"template".equals(type) && !"copy".equals(type))) {
                sheetDTO.setDiagramClass(diagramModel.getDiagramClass());
                sheetDTO.setLinkFromPortIdProperty(diagramModel.getLinkFromPortIdProperty());
                sheetDTO.setLinkToPortIdProperty(diagramModel.getLinkToPortIdProperty());
                sheetDTO.setModelData(diagramModel.getModelData());
            }
            List<ESDiagramNode> nodeDataArray = diagramModel.getNodeDataArray();
            if (!BinaryUtils.isEmpty(nodeDataArray)) {
                for (ESDiagramNode nodeDTO : nodeDataArray) {
                    Long nodeId = IdGenerator.createGenerator().getID();
                    nodeDTO.setId(nodeId);
                    nodeDTO.setDiagramId(newDiagramId);
                    nodeDTO.setSheetId(sheetId);
                    nodeToEnergy(nodeDTO, type);
                }
                saveNodeList.addAll(nodeDataArray);
            }
            List<ESDiagramLink> linkDataArray = diagramModel.getLinkDataArray();
            if (!BinaryUtils.isEmpty(linkDataArray)) {
                for (ESDiagramLink linkDTO : linkDataArray) {
                    Long linkId = IdGenerator.createGenerator().getID();
                    linkDTO.setId(linkId);
                    linkDTO.setSheetId(sheetId);
                    linkDTO.setDiagramId(newDiagramId);
                    linkToEnergy(linkDTO, type);
                }
                saveLinkList.addAll(linkDataArray);
            }
        }
        //保存视图和其组件
        esDiagramSheetDao.saveOrUpdateBatch(sheetList);
        if (!BinaryUtils.isEmpty(saveNodeList)) {
            diagramNodeSvc.saveOrUpdateBatch(saveNodeList);
        }
        if (!BinaryUtils.isEmpty(saveLinkList)) {
            diagramLinkSvc.saveOrUpdateBatch(saveLinkList);
        }
        esDiagramDao.saveOrUpdate(esDiagram);
        return newDiagramId;
    }

    private void nodeToEnergy(ESDiagramNode nodeDTO, String type) {
        String nodeJson = nodeDTO.getNodeJson();
        if (BinaryUtils.isEmpty(nodeJson)) {
            return;
        }
        HashMap<String, Object> ri = JSONObject.parseObject(nodeJson, HashMap.class);
        nodeDTO.setCiCode(ri.containsKey("ciCode") && !BinaryUtils.isEmpty(ri.get("ciCode")) ? String.valueOf(ri.get("ciCode")) : null);
        if ("template".equals(type)) {
            ri.put("defaultBMText", true);
        }
        if (nodeJson.contains("relationInfo") && nodeJson.contains("document")) {
            RelationInfo info = JSON.toJavaObject((JSON) ri.get("relationInfo"), RelationInfo.class);
        }
        nodeDTO.setNodeJson(JSONObject.toJSONString(ri));
    }

    private void linkToEnergy(ESDiagramLink linkDTO, String type) {
        String linkJson = linkDTO.getLinkJson();
        if (BinaryUtils.isEmpty(linkJson)) {
            return;
        }
        HashMap<String, Object> ri = JSONObject.parseObject(linkJson, HashMap.class);
        linkDTO.setUniqueCode(ri.containsKey("rltCode") && !BinaryUtils.isEmpty(ri.get("rltCode")) ?
                String.valueOf(ri.get("rltCode")) : null);
        if ("template".equals(type)) {
            ri.put("defaultBMText", true);
        }
        linkDTO.setLinkJson(JSONObject.toJSONString(ri));
    }


    private List<Long> saveESDiagramBatch(List<ESDiagramInfoDTO> esDiagramInfoList, String newName, Long newDirId, String type, Map<String, String> sourceEnergyAndNewEnergy) {
        List<Long> diagramIdList = new ArrayList<>();
        List<ESDiagram> saveList = new ArrayList<>();
        List<ESDiagramNode> saveNodeList = new ArrayList<>();
        List<ESDiagramLink> saveLinkList = new ArrayList<>();
        List<ESDiagramSheetDTO> saveSheetList = new ArrayList<>();
        for (ESDiagramInfoDTO esDiagramInfo : esDiagramInfoList) {
            //如果version=0.1，表示是老视图导入，这种数据没有sheetId，需要特殊的兼容处理
            String version = esDiagramInfo.getVersion();
            if (!StringUtils.isEmpty(version) && "0.1".equals(version)) {
                Long newDiagramId = importOldDiagramJSON(esDiagramInfo, null);
                diagramIdList.add(newDiagramId);
                continue;
            }

            MessageUtil.checkEmpty(esDiagramInfo.getName(), "diagramName");
            Long diagramId = IdGenerator.createGenerator().getID();
            ESDiagram esDiagram = new ESDiagram();
            BeanUtils.copyProperties(esDiagramInfo, esDiagram);
            esDiagram.setFlowStatus(0);
            esDiagram.setIsOpen(0);
            esDiagram.setReleaseDiagramId("");
            //更新缩略图
            String icon1 = esDiagramInfo.getIcon1();
            String iconPath = "";

            // 代码扫描漏洞
            tryDownloadRsmAndUpdateLocalRsm1(icon1, iconPath);

            /*try {
                if (!StringUtils.isEmpty(icon1)) {
                    rsmUtils.downloadRsmAndUpdateLocalRsm(icon1);
                    String path = icon1.replace(httpResourceUrl, "");
                    StringBuilder stringBuilder = new StringBuilder();
                    stringBuilder.append(localPath);
                    stringBuilder.append(path);
                    byte[] iconBytes = FileUtils.readFileToByteArray(new File(FileFilterUtil.parseFilePath(stringBuilder.toString())));
                    iconPath = this.saveOrUpdateResource(new StringBuilder(UUID.randomUUID().toString()).append(".png").toString(), iconBytes, true);
                }
            } catch (Exception e) {
                logger.error("读取文件失败:", e);
            }*/
            if (!StringUtils.isEmpty(iconPath)) {
                esDiagram.setIcon1(iconPath);
            }
            //设置视图的默认信息
            esDiagram.setId(diagramId);
            setDiagramBaseInfo(esDiagram);
            sourceEnergyAndNewEnergy.put(esDiagramInfo.getDEnergy(), esDiagram.getDEnergy());
            if (!BinaryUtils.isEmpty(newName)) {
                esDiagram.setName(newName);
            }
            if (!BinaryUtils.isEmpty(newDirId)) {
                esDiagram.setDirId(newDirId);
            }
            diagramIdList.add(diagramId);
            //根据模板新建视图，或者复制模板，都需要将视图类型设置为1
            if (!BinaryUtils.isEmpty(type) && "template".equals(type)) {
                esDiagram.setDiagramType(1);
            }
            //获取待保存的sheet、dao、link集合
            if (esDiagramInfo.getNonMonet() == null || esDiagramInfo.getNonMonet() != 1) {
                List<ESDiagramSheetDTO> sheetList = esDiagramInfo.getSheetList();
                Map<String, List<ESDiagramModel>> sheetModelMap = esDiagramInfo.getModelList().stream().collect(Collectors.groupingBy(ESDiagramModel::getSheetId));
                for (ESDiagramSheetDTO sheetDTO : sheetList) {
                    String sheetId = sheetDTO.getSheetId();
                    sheetDTO.setDiagramId(diagramId);
                    ESDiagramModel diagramModel = sheetModelMap.get(sheetId).get(0);
                    sheetDTO.setDiagramClass(diagramModel.getDiagramClass());
                    sheetDTO.setLinkFromPortIdProperty(diagramModel.getLinkFromPortIdProperty());
                    sheetDTO.setLinkToPortIdProperty(diagramModel.getLinkToPortIdProperty());
                    sheetDTO.setModelData(diagramModel.getModelData());
                    sheetDTO.setId(IdGenerator.createGenerator().getID());

                    List<ESDiagramNode> nodeDataArray = diagramModel.getNodeDataArray();
                    if (!BinaryUtils.isEmpty(nodeDataArray)) {
                        for (ESDiagramNode nodeDTO : nodeDataArray) {
                            nodeDTO.setId(IdGenerator.createGenerator().getID());
                            nodeDTO.setDiagramId(diagramId);
                            nodeDTO.setSheetId(sheetId);
                            nodeToEnergy(nodeDTO, type);
                        }
                        saveNodeList.addAll(nodeDataArray);
                    }

                    List<ESDiagramLink> linkDataArray = diagramModel.getLinkDataArray();
                    if (!BinaryUtils.isEmpty(linkDataArray)) {
                        for (ESDiagramLink linkDTO : linkDataArray) {
                            linkDTO.setId(IdGenerator.createGenerator().getID());
                            linkDTO.setSheetId(sheetId);
                            linkDTO.setDiagramId(diagramId);
                            linkToEnergy(linkDTO, type);
                        }
                        saveLinkList.addAll(linkDataArray);
                    }
                }
                saveSheetList.addAll(sheetList);
            }
            //保存视图和其组件
            saveList.add(esDiagram);
        }
        esDiagramSheetDao.saveOrUpdateBatch(saveSheetList);
        if (!BinaryUtils.isEmpty(saveNodeList)) {
            diagramNodeSvc.saveOrUpdateBatch(saveNodeList);
        }
        if (!BinaryUtils.isEmpty(saveLinkList)) {
            diagramLinkSvc.saveOrUpdateBatch(saveLinkList);
        }
        esDiagramDao.saveOrUpdateBatch(saveList);
        return diagramIdList;
    }

    @Override
    public List<Long> saveESDiagramBatch(List<ESDiagramInfoDTO> esDiagramInfoList, String newName, Long newDirId, String type) {
        List<Long> diagramIdList = new ArrayList<>();
        List<ESDiagram> saveList = new ArrayList<>();
        List<ESDiagramNode> saveNodeList = new ArrayList<>();
        List<ESDiagramLink> saveLinkList = new ArrayList<>();
        List<ESDiagramSheetDTO> saveSheetList = new ArrayList<>();
        for (ESDiagramInfoDTO esDiagramInfo : esDiagramInfoList) {
            //如果version=0.1，表示是老视图导入，这种数据没有sheetId，需要特殊的兼容处理
            String version = esDiagramInfo.getVersion();
            if (!StringUtils.isEmpty(version) && "0.1".equals(version)) {
                Long newDiagramId = importOldDiagramJSON(esDiagramInfo, null);
                diagramIdList.add(newDiagramId);
                continue;
            }

            MessageUtil.checkEmpty(esDiagramInfo.getName(), "diagramName");
            Long diagramId = IdGenerator.createGenerator().getID();
            ESDiagram esDiagram = new ESDiagram();
            BeanUtils.copyProperties(esDiagramInfo, esDiagram);
            esDiagram.setFlowStatus(0);
            esDiagram.setIsOpen(0);
            esDiagram.setReleaseDiagramId("");
            //更新缩略图
            String icon1 = esDiagramInfo.getIcon1();
            String iconPath = "";

            // 代码扫描漏洞
            this.tryDownloadRsmAndUpdateLocalRsm1(icon1, iconPath);
            /*try {
                if (!StringUtils.isEmpty(icon1)) {
                    rsmUtils.downloadRsmAndUpdateLocalRsm(icon1);
                    String path = icon1.replace(httpResourceUrl, "");
                    StringBuilder stringBuilder = new StringBuilder();
                    stringBuilder.append(localPath);
                    stringBuilder.append(path);
                    byte[] iconBytes = FileUtils.readFileToByteArray(new File(FileFilterUtil.parseFilePath(stringBuilder.toString())));
                    iconPath = this.saveOrUpdateResource(new StringBuilder(UUID.randomUUID().toString()).append(".png").toString(), iconBytes, true);
                }
            } catch (Exception e) {
                logger.error("读取文件失败:", e);
            }*/
            if (!StringUtils.isEmpty(iconPath)) {
                esDiagram.setIcon1(iconPath);
            }
            //设置视图的默认信息
            esDiagram.setId(diagramId);
            setDiagramBaseInfo(esDiagram);
            if (!BinaryUtils.isEmpty(newName)) {
                esDiagram.setName(newName);
            }
            if (!BinaryUtils.isEmpty(newDirId)) {
                esDiagram.setDirId(newDirId);
            }
            diagramIdList.add(diagramId);
            //根据模板新建视图，或者复制模板，都需要将视图类型设置为1
            if (!BinaryUtils.isEmpty(type) && "template".equals(type)) {
                esDiagram.setDiagramType(1);
            }
            if (!BinaryUtils.isEmpty(type) && "copy".equals(type)) {
                // 当前视图进行复制操作 需要将PID重新赋值
                esDiagram.setPrepareDiagramId(SecureUtil.md5(String.valueOf(diagramId)).substring(8, 24));
            }
            //获取待保存的sheet、dao、link集合
            if (esDiagramInfo.getNonMonet() == null || esDiagramInfo.getNonMonet() != 1) {
                List<ESDiagramSheetDTO> sheetList = esDiagramInfo.getSheetList();
                List<ESDiagramModel> modelList = esDiagramInfo.getModelList();
                Map<String, List<ESDiagramModel>> sheetModelMap =
                        modelList.stream().collect(Collectors.groupingBy(ESDiagramModel::getSheetId));
                for (ESDiagramSheetDTO sheetDTO : sheetList) {
                    String sheetId = sheetDTO.getSheetId();
                    sheetDTO.setDiagramId(diagramId);
                    ESDiagramModel diagramModel = sheetModelMap.get(sheetId).get(0);
                    sheetDTO.setDiagramClass(diagramModel.getDiagramClass());
                    sheetDTO.setLinkFromPortIdProperty(diagramModel.getLinkFromPortIdProperty());
                    sheetDTO.setLinkToPortIdProperty(diagramModel.getLinkToPortIdProperty());
                    sheetDTO.setModelData(diagramModel.getModelData());
                    sheetDTO.setId(IdGenerator.createGenerator().getID());

                    List<ESDiagramNode> nodeDataArray = diagramModel.getNodeDataArray();
                    if (!BinaryUtils.isEmpty(nodeDataArray)) {
                        for (ESDiagramNode nodeDTO : nodeDataArray) {
                            nodeDTO.setId(IdGenerator.createGenerator().getID());
                            nodeDTO.setDiagramId(diagramId);
                            nodeDTO.setSheetId(sheetId);
                            nodeToEnergy(nodeDTO, type);
                        }
                        saveNodeList.addAll(nodeDataArray);
                    }

                    List<ESDiagramLink> linkDataArray = diagramModel.getLinkDataArray();
                    if (!BinaryUtils.isEmpty(linkDataArray)) {
                        for (ESDiagramLink linkDTO : linkDataArray) {
                            linkDTO.setId(IdGenerator.createGenerator().getID());
                            linkDTO.setSheetId(sheetId);
                            linkDTO.setDiagramId(diagramId);
                            linkToEnergy(linkDTO, type);
                        }
                        saveLinkList.addAll(linkDataArray);
                    }
                }
                saveSheetList.addAll(sheetList);
            }
            //保存视图和其组件
            saveList.add(esDiagram);
        }
        if (!BinaryUtils.isEmpty(saveNodeList)) {
            diagramNodeSvc.saveOrUpdateBatch(saveNodeList);
        }
        if (!BinaryUtils.isEmpty(saveLinkList)) {
            diagramLinkSvc.saveOrUpdateBatch(saveLinkList);
        }
        esDiagramSheetDao.saveOrUpdateBatch(saveSheetList);
        esDiagramDao.saveOrUpdateBatch(saveList);
        return diagramIdList;
    }

    @Override
    public List<Long> creatSnapshotBatch(List<ESDiagramInfoDTO> esDiagramInfoList) {
        List<Long> diagramIdList = new ArrayList<>();
        for (ESDiagramInfoDTO esDiagramInfo : esDiagramInfoList) {
            MessageUtil.checkEmpty(esDiagramInfo.getName(), "diagramName");
            Long diagramId = esDiagramInfo.getNewCopyId();
            ESDiagram esDiagram = new ESDiagram();
            BeanUtils.copyProperties(esDiagramInfo, esDiagram);
            //更新缩略图
            String icon1 = esDiagramInfo.getIcon1();
            String iconPath = "";

            // 代码扫描漏洞
            this.saveTryResource(icon1, iconPath);

            /*try {
                if (!StringUtils.isEmpty(icon1)) {
                    String path = icon1.replace(httpResourceUrl, "");
                    byte[] iconBytes = FileUtils.readFileToByteArray(new File(FileFilterUtil.parseFilePath(new StringBuilder(localPath).append(path).toString())));
                    iconPath = this.saveOrUpdateResource(new StringBuilder(UUID.randomUUID().toString()).append(".png").toString(), iconBytes, true);
                }
            } catch (Exception e) {
                logger.error("读取文件失败:", e);
            }*/
            if (!StringUtils.isEmpty(iconPath)) {
                esDiagram.setIcon1(iconPath);
            }
            //设置视图的默认信息
            esDiagram.setId(diagramId);
            setDiagramBaseInfo(esDiagram);
            esDiagram.setHistoryVersionFlag(0);

            diagramIdList.add(diagramId);
            //获取待保存的sheet、dao、link集合
            List<ESDiagramNode> saveNodeList = new ArrayList<>();
            List<ESDiagramLink> saveLinkList = new ArrayList<>();
            if (esDiagramInfo.getNonMonet() == null || esDiagramInfo.getNonMonet() != 1) {
                List<ESDiagramSheetDTO> sheetList = esDiagramInfo.getSheetList();
                List<ESDiagramModel> modelList = esDiagramInfo.getModelList();
                Map<String, List<ESDiagramModel>> sheetModelMap =
                        modelList.stream().collect(Collectors.groupingBy(ESDiagramModel::getSheetId));
                for (ESDiagramSheetDTO sheetDTO : sheetList) {
                    String sheetId = sheetDTO.getSheetId();
                    sheetDTO.setDiagramId(diagramId);
                    ESDiagramModel diagramModel = sheetModelMap.get(sheetId).get(0);
                    sheetDTO.setDiagramClass(diagramModel.getDiagramClass());
                    sheetDTO.setLinkFromPortIdProperty(diagramModel.getLinkFromPortIdProperty());
                    sheetDTO.setLinkToPortIdProperty(diagramModel.getLinkToPortIdProperty());
                    sheetDTO.setModelData(diagramModel.getModelData());
                    sheetDTO.setId(IdGenerator.createGenerator().getID());

                    List<ESDiagramNode> nodeDataArray = diagramModel.getNodeDataArray();
                    if (!BinaryUtils.isEmpty(nodeDataArray)) {
                        for (ESDiagramNode nodeDTO : nodeDataArray) {
                            nodeDTO.setId(IdGenerator.createGenerator().getID());
                            nodeDTO.setDiagramId(diagramId);
                            nodeDTO.setSheetId(sheetId);
                            nodeToEnergy(nodeDTO, "");
                        }
                        saveNodeList.addAll(nodeDataArray);
                    }

                    List<ESDiagramLink> linkDataArray = diagramModel.getLinkDataArray();
                    if (!BinaryUtils.isEmpty(linkDataArray)) {
                        for (ESDiagramLink linkDTO : linkDataArray) {
                            linkDTO.setId(IdGenerator.createGenerator().getID());
                            linkDTO.setSheetId(sheetId);
                            linkDTO.setDiagramId(diagramId);
                            linkToEnergy(linkDTO, "");
                        }
                        saveLinkList.addAll(linkDataArray);
                    }
                }
                esDiagramSheetDao.saveOrUpdateBatch(sheetList);
            }
            //保存视图和其组件
            esDiagramDao.saveOrUpdate(esDiagram);
            if (!BinaryUtils.isEmpty(saveNodeList)) {
                diagramNodeSvc.saveOrUpdateBatch(saveNodeList);
            }
            if (!BinaryUtils.isEmpty(saveLinkList)) {
                diagramLinkSvc.saveOrUpdateBatch(saveLinkList);
            }
        }
        return diagramIdList;
    }



    @Override
    public ESDiagramDTO updateFullDiagram(Long diagramId, ESDiagramInfoDTO esDiagramInfo) {
        ESWaterMarkInfo watermarkInfo = esDiagramInfo.getWatermarkInfo();
        MessageUtil.checkEmpty(diagramId, "diagramId");
        MessageUtil.checkEmpty(esDiagramInfo, "待更新视图信息");
        //1.视图校验
        ESDiagram esDiagram = judgeSingleDiagramAuth(diagramId, "", true);
        ESDiagram oldDiagram = new ESDiagram();
        BeanUtils.copyProperties(esDiagram, oldDiagram);
        esDiagramInfo.setId(diagramId);
        esDiagramInfo.setCreateTime(esDiagram.getCreateTime());
        esDiagramInfo.setCreator(esDiagram.getCreator());
        //2.查出未更新前视图拥有的sheet、node、link
        ESDiagramSheetQuery sheetQuery = new ESDiagramSheetQuery();
        sheetQuery.setDiagramId(diagramId);
        List<ESDiagramSheetDTO> deleteSheetList = esDiagramSheetDao.getListByCdt(sheetQuery);
        Set<Long> deleteSheetIdSet = new HashSet<>();
        if (!BinaryUtils.isEmpty(deleteSheetList)) {
            deleteSheetIdSet = deleteSheetList
                    .stream()
                    .map(ESDiagramSheetDTO::getId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());
        }
        ESDiagramNodeQueryBean nodeQuery = new ESDiagramNodeQueryBean();
        nodeQuery.setDiagramId(diagramId);
        List<ESDiagramNode> deleteNodeList = new ArrayList<>();
        deleteNodeList = getAllNode(nodeQuery, deleteNodeList);
        Set<Long> deleteNodeIdSet = new HashSet<>();
        if (!BinaryUtils.isEmpty(deleteNodeList)) {
            deleteNodeIdSet = deleteNodeList
                    .stream()
                    .map(ESDiagramNode::getId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());
        }
        ESDiagramLinkQueryBean linkQuery = new ESDiagramLinkQueryBean();
        linkQuery.setDiagramId(diagramId);
        List<ESDiagramLink> deleteLinkList = new ArrayList<>();
        deleteLinkList = getAllLink(linkQuery, deleteLinkList);
        Set<Long> deleteLinkIdSet = new HashSet<>();
        if (!BinaryUtils.isEmpty(deleteLinkList)) {
            deleteLinkIdSet = deleteLinkList
                    .stream()
                    .map(ESDiagramLink::getId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());
        }
        //3.更新视图数据
        String version = esDiagramInfo.getVersion();
        try {
            //version=0.1代表是老视图导入，对于老视图导入需要特殊处理
            if (!StringUtils.isEmpty(version) && "0.1".equals(version)) {
                importOldDiagramJSON(esDiagramInfo, "updateFull");
            } else {
                saveESDiagram(esDiagramInfo, null, null, "updateFull");
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new BinaryException(e.getMessage());
        }

        //视图保存成功后，删除过时的sheet、node、link
        if (!BinaryUtils.isEmpty(deleteSheetIdSet)) {
            sheetQuery.setIds(deleteSheetIdSet.toArray(new Long[0]));
            if (!BinaryUtils.isEmpty(sheetQuery) && sheetQuery.getIds().length > 0) {
                esDiagramSheetDao.deleteByQuery(ESUtil.cdtToBuilder(sheetQuery), true);
            }
        }
        if (!BinaryUtils.isEmpty(deleteNodeIdSet)) {
            nodeQuery.setIds(deleteNodeIdSet.toArray(new Long[0]));
            if (!BinaryUtils.isEmpty(nodeQuery) && nodeQuery.getIds().length > 0) {
                esDiagramNodeDao.deleteByQuery(ESUtil.cdtToBuilder(nodeQuery), true);
            }
        }
        if (!BinaryUtils.isEmpty(deleteLinkIdSet)) {
            linkQuery.setIds(deleteLinkIdSet.toArray(new Long[0]));
            if (!BinaryUtils.isEmpty(linkQuery) && nodeQuery.getIds().length > 0) {
                esDiagramLinkDao.deleteByQuery(ESUtil.cdtToBuilder(linkQuery), true);
            }
        }
        if (watermarkInfo == null) {
            //手动更新
            BoolQueryBuilder query = QueryBuilders.boolQuery();
            query.should(QueryBuilders.termQuery("id", diagramId));
            String scriptStr = "ctx._source.watermarkInfo=null";
            esDiagramDao.updateByQuery(query, scriptStr, true);
        }
        //判断是否需要保存历史版本
        Long modifyTime = oldDiagram.getModifyTime();
        Long currTime = BinaryUtils.getNumberDateTime();
        //更新前需要判断是否要新建历史版本，查出信息，异步保存
        boolean isCreateVersion = false;
        VcDiagramVersion currDiagramVersion = diagramVersionDao.selectById(diagramId);
        if (currDiagramVersion != null) {
        /*
            创建历史版本的条件判断
                首先判断版本名称是否为空，
                    不为空，创建
                    为空，判断距离视图上一次修改时间是否超过三分钟
                        超过，创建
        */
            String versionName = currDiagramVersion.getVersionName();
            if (!BinaryUtils.isEmpty(versionName)) {
                isCreateVersion = true;
            }
        }
        if (!isCreateVersion) {
            if ((currTime - modifyTime) > 300) {
                isCreateVersion = true;
            }
        }
        if (isCreateVersion) {
            //满足创建版本条件，创建当前视图的历史版本
            //2.获取上一次操作用户和本次操作用户的信息，然后保存历史版本
            ESSimpleDiagram esSimpleDiagram = new ESSimpleDiagram(oldDiagram, deleteSheetList, deleteNodeList, deleteLinkList);
            String currLoginCode = SysUtil.getCurrentUserInfo().getLoginCode();
            String lastModifierCode = esDiagram.getModifier();
            SysUser tmpModifier = new SysUser();
            SysUser currUser = new SysUser();
            boolean objFlag = true;
            //当前用户不是上一次操作用户，需要手动查询上一次操作用户信息
            CSysUser userQuery = new CSysUser();
            if (!lastModifierCode.equals(currLoginCode)) {
                userQuery.setLoginCodes(new String[]{currLoginCode, lastModifierCode});
            } else {
                userQuery.setLoginCodes(new String[]{currLoginCode});
            }
            userQuery.setDomainId(SYS_DOMAIN_ID);
            userQuery.setSuperUserFlags(new Integer[]{0, 1});
            List<SysUser> sysUsers = userApiSvc.getSysUserByCdt(userQuery);
            if (!BinaryUtils.isEmpty(sysUsers)) {
                for (SysUser sysUser : sysUsers) {
                    String loginCode = sysUser.getLoginCode();
                    if (loginCode.equals(currLoginCode)) {
                        currUser = sysUser;
                        continue;
                    }
                    if (loginCode.equals(lastModifierCode)) {
                        tmpModifier = sysUser;
                        objFlag = false;
                    }
                }
            } else {
                logger.error("用户信息为空，新增历史版本失败");
            }
            if (objFlag) {
                logger.info("上次修改用户已被删除，取当前用户作为上次修改用户");
                tmpModifier = currUser;
            }
            SysUser lastModifier = tmpModifier;
            SysUser finalCurrUser = currUser;
            executor.submit(() ->
                    {
                        try {
                            //因为不同线程之间token不共享，而保存历史版本时需要用户信息，所以需要手动传递
                            esDiagramVersionSvc.saveESDiagramVersion(esSimpleDiagram, finalCurrUser, lastModifier);
                            logger.info("保存历史版本成功");
                        } catch (Exception e) {
                            e.printStackTrace();
                            logger.info("保存历史版本失败，失败的diagramId：{}, 报错信息：{}", esSimpleDiagram.getEsDiagram().getId(), e.getMessage());
                        }
                    }
            );
        }
        //异步删除该视图包含的所有分享图片(视图内容更新，导致分享图片内容过期)
        executor.submit(() ->
                {
                    try {
                        //删除历史记录
                        DiagramShareLinkQuery shareLinkQuery = new DiagramShareLinkQuery();
                        shareLinkQuery.setDiagramIdEqual(SecureUtil.md5(String.valueOf(diagramId)).substring(8, 24));
                        shareLinkQuery.setLinkType(0);
                        List<DiagramShareLink> deleteShareLinkList = shareLinkDao.getListByCdt(shareLinkQuery);
                        if (!BinaryUtils.isEmpty(deleteShareLinkList)) {
                            //删除链接
                            Set<Long> linkIdSet = deleteShareLinkList
                                    .stream()
                                    .map(DiagramShareLink::getId)
                                    .collect(Collectors.toSet());
                            shareLinkDao.deleteByIds(linkIdSet);
                            //删除文件
                            for (DiagramShareLink shareLink : deleteShareLinkList) {
                                if (!BinaryUtils.isEmpty(shareLink)) {
                                    String fileUrl = shareLink.getStorePath();
                                    if (!BinaryUtils.isEmpty(fileUrl)) {
                                        localPath = localPath.replace("/", FILE_SEPARATOR).replace("\\", FILE_SEPARATOR);
                                        fileUrl = fileUrl.startsWith(localPath) ? fileUrl : localPath + fileUrl;
                                        File delFile = new File(FileFilterUtil.parseFilePath(fileUrl));
                                        rsmUtils.deleteRsm(fileUrl);
                                        if (delFile.exists()) {
                                            delFile.delete();
                                            resourceSvc.saveSyncResourceInfo(fileUrl, httpResourceUrl + fileUrl, false, 1);
                                            logger.info("文件删除成功");
                                        }
                                    } else {
                                        logger.info("fileUrl为空，无法删除服务器中文件，ids={}", shareLink.getId());
                                    }
                                }
                            }
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                        logger.info("删除分享图片失败，视图id={}", diagramId);
                    }
                }
        );

        return queryDiagramInfoById(diagramId, null, false);
    }

    @Override
    public ESDiagramDTO updateFullDiagramNew(Long designDiagramId, ESDiagramInfoDTO privateEsDiagramInfo) {
        return updateFullDiagramNew(designDiagramId, privateEsDiagramInfo, true);
    }

    private ESDiagramDTO updateFullDiagramNew(Long designDiagramId, ESDiagramInfoDTO privateEsDiagramInfo, boolean copyFlag) {
        if (BinaryUtils.isEmpty(designDiagramId)) {
            // 排查检出问题
            log.info("######:(####### params: designDiagramId【{}】, privateEsDiagramInfo：【{}】", designDiagramId, JSONObject.toJSONString(privateEsDiagramInfo));
            return new ESDiagramDTO();
        }
        ESWaterMarkInfo watermarkInfo = privateEsDiagramInfo.getWatermarkInfo();
        MessageUtil.checkEmpty(designDiagramId, "designDiagramId");
        MessageUtil.checkEmpty(privateEsDiagramInfo, "待更新视图信息");
        //1.视图校验
//        ESDiagram lastVersionDiagramData = judgeSingleDiagramAuth(designDiagramId, "", false);
        ESDiagram lastVersionDiagramData = esDiagramDao.getById(designDiagramId);
        //copy视图历史
        if(copyFlag){
            long historyDiagramId = ESUtil.getUUID();
            ESDiagramMoveCdt cdt = setDiagramMoveCdt(lastVersionDiagramData, historyDiagramId, lastVersionDiagramData.getReleaseDiagramId());
            cdt.setCreateHistory(true);
            cdt.setCreateHistoryVersion(false);
            cdt.setReleaseDesc(privateEsDiagramInfo.getReleaseDesc());
            copyBmHistoryDiagramById(cdt);
        }

        privateEsDiagramInfo.setId(designDiagramId);
        privateEsDiagramInfo.setStatus(1);
        privateEsDiagramInfo.setCreateTime(lastVersionDiagramData.getCreateTime());
        privateEsDiagramInfo.setCreator(lastVersionDiagramData.getCreator());
        //更新发布时间
        privateEsDiagramInfo.setOpenTime(BinaryUtils.getNumberDateTime());
        //2.查出未更新前视图拥有的sheet、node、link
        ESDiagramSheetQuery sheetQuery = new ESDiagramSheetQuery();
        sheetQuery.setDiagramId(designDiagramId);
        List<ESDiagramSheetDTO> deleteSheetList = esDiagramSheetDao.getListByCdt(sheetQuery);
        Set<Long> deleteSheetIdSet = new HashSet<>();
        if (!BinaryUtils.isEmpty(deleteSheetList)) {
            deleteSheetIdSet = deleteSheetList
                    .stream()
                    .map(ESDiagramSheetDTO::getId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());
        }
        ESDiagramNodeQueryBean nodeQuery = new ESDiagramNodeQueryBean();
        nodeQuery.setDiagramId(designDiagramId);
        List<ESDiagramNode> deleteNodeList = new ArrayList<>();
        deleteNodeList = getAllNode(nodeQuery, deleteNodeList);
        Set<Long> deleteNodeIdSet = new HashSet<>();
        if (!BinaryUtils.isEmpty(deleteNodeList)) {
            deleteNodeIdSet = deleteNodeList
                    .stream()
                    .map(ESDiagramNode::getId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());
        }
        ESDiagramLinkQueryBean linkQuery = new ESDiagramLinkQueryBean();
        linkQuery.setDiagramId(designDiagramId);
        List<ESDiagramLink> deleteLinkList = new ArrayList<>();
        deleteLinkList = getAllLink(linkQuery, deleteLinkList);
        Set<Long> deleteLinkIdSet = new HashSet<>();
        if (!BinaryUtils.isEmpty(deleteLinkList)) {
            deleteLinkIdSet = deleteLinkList
                    .stream()
                    .map(ESDiagramLink::getId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());
        }
        //3.更新视图数据
        String version = privateEsDiagramInfo.getVersion();
        try {
            //version=0.1代表是老视图导入，对于老视图导入需要特殊处理
            if (!StringUtils.isEmpty(version) && "0.1".equals(version)) {
                importOldDiagramJSON(privateEsDiagramInfo, "updateFull");
            } else {
                saveESDiagram(privateEsDiagramInfo, privateEsDiagramInfo.getName(), privateEsDiagramInfo.getDirId(), "updateFull");
            }
        } catch (Exception e) {
            throw new BinaryException(e.getMessage(), e);
        }

        //视图保存成功后，删除过时的sheet、node、link
        if (!BinaryUtils.isEmpty(deleteSheetIdSet)) {
            sheetQuery.setIds(deleteSheetIdSet.toArray(new Long[0]));
            if (!BinaryUtils.isEmpty(sheetQuery) && sheetQuery.getIds().length > 0) {
                esDiagramSheetDao.deleteByQuery(ESUtil.cdtToBuilder(sheetQuery), true);
            }
        }
        if (!BinaryUtils.isEmpty(deleteNodeIdSet)) {
            nodeQuery.setIds(deleteNodeIdSet.toArray(new Long[0]));
            if (!BinaryUtils.isEmpty(nodeQuery) && nodeQuery.getIds().length > 0) {
                esDiagramNodeDao.deleteByQuery(ESUtil.cdtToBuilder(nodeQuery), true);
            }
        }
        if (!BinaryUtils.isEmpty(deleteLinkIdSet)) {
            linkQuery.setIds(deleteLinkIdSet.toArray(new Long[0]));
            if (!BinaryUtils.isEmpty(linkQuery) && nodeQuery.getIds().length > 0) {
                esDiagramLinkDao.deleteByQuery(ESUtil.cdtToBuilder(linkQuery), true);
            }
        }
        if (watermarkInfo == null) {
            //手动更新
            BoolQueryBuilder query = QueryBuilders.boolQuery();
            query.should(QueryBuilders.termQuery("id", designDiagramId));
            String scriptStr = "ctx._source.watermarkInfo=null";
            esDiagramDao.updateByQuery(query, scriptStr, true);
        }
        //异步删除该视图包含的所有分享图片(视图内容更新，导致分享图片内容过期)
        executor.submit(() ->
                {
                    try {
                        //删除历史记录
                        DiagramShareLinkQuery shareLinkQuery = new DiagramShareLinkQuery();
                        shareLinkQuery.setDiagramIdEqual(SecureUtil.md5(String.valueOf(designDiagramId)).substring(8, 24));
                        shareLinkQuery.setLinkType(0);
                        List<DiagramShareLink> deleteShareLinkList = shareLinkDao.getListByCdt(shareLinkQuery);
                        if (!BinaryUtils.isEmpty(deleteShareLinkList)) {
                            //删除链接
                            Set<Long> linkIdSet = deleteShareLinkList
                                    .stream()
                                    .map(DiagramShareLink::getId)
                                    .collect(Collectors.toSet());
                            shareLinkDao.deleteByIds(linkIdSet);
                            //删除文件
                            for (DiagramShareLink shareLink : deleteShareLinkList) {
                                if (!BinaryUtils.isEmpty(shareLink)) {
                                    String fileUrl = shareLink.getStorePath();
                                    if (!BinaryUtils.isEmpty(fileUrl)) {
                                        localPath = localPath.replace("/", FILE_SEPARATOR).replace("\\", FILE_SEPARATOR);
                                        fileUrl = fileUrl.startsWith(localPath) ? fileUrl : localPath + fileUrl;
                                        File delFile = new File(FileFilterUtil.parseFilePath(fileUrl));
                                        rsmUtils.deleteRsm(fileUrl);
                                        if (delFile.exists()) {
                                            delFile.delete();
                                            resourceSvc.saveSyncResourceInfo(fileUrl, httpResourceUrl + fileUrl, false, 1);
                                            logger.info("文件删除成功");
                                        }
                                    } else {
                                        logger.info("fileUrl为空，无法删除服务器中文件，ids={}", shareLink.getId());
                                    }
                                }
                            }
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                        logger.info("删除分享图片失败，视图id={}", designDiagramId);
                    }
                }
        );

        return queryDiagramInfoById(designDiagramId, null, false);
    }

    private ESDiagramMoveCdt setDiagramMoveCdt(ESDiagram diagram, Long copyDiagramId, String releaseDiagramId) {
        ESDiagramMoveCdt cdt = new ESDiagramMoveCdt();
        cdt.setCopyDiagramId(copyDiagramId);
        cdt.setDiagramId(diagram.getDEnergy());
        cdt.setDirType(diagram.getDirType());
        cdt.setNewDirId(diagram.getDirId());
        cdt.setNewName(diagram.getName());
        cdt.setViewType(diagram.getViewType());
        cdt.setIsOpen(1);
        cdt.setReleaseDiagramId(releaseDiagramId);
        return cdt;
    }


    private ESDiagramMoveCdt createDiagramMoveCdt(ESDiagramInfoDTO diagram, Long dirCode, Long releaseDiagramId, String newEnergyId, String releaseDesc, Integer open) {
        ESDiagramMoveCdt cdt = new ESDiagramMoveCdt();
        cdt.setCopyDiagramId(releaseDiagramId);
        cdt.setDiagramId(diagram.getDEnergy());
        cdt.setDirType(diagram.getDirType());
        cdt.setNewDirId(dirCode);
        cdt.setNewName(diagram.getName());
        cdt.setViewType(diagram.getViewType());
        cdt.setIsOpen(open);
        cdt.setReleaseDiagramId(newEnergyId);
        cdt.setCreateHistoryVersion(false);
        if (!StringUtils.isEmpty(releaseDesc)) {
            cdt.setReleaseDesc(releaseDesc);
        }
        return cdt;
    }

    @Override
    public List<String> importDiagramBatch(List<ESDiagramInfoDTO> esDiagramInfoList) {
        List<Long> diagramIdList = saveESDiagramBatch(esDiagramInfoList, null, null, null);
        List<String> dList = new ArrayList<>();
        if (!BinaryUtils.isEmpty(diagramIdList)) {
            createESDiagramVersionRecord(diagramIdList.toArray(new Long[0]));
            for (Long diagramId : diagramIdList) {
                dList.add(SecureUtil.md5(String.valueOf(diagramId)).substring(8, 24));
            }
        }
        return dList;
    }

    public Long importOldDiagramJSON(ESDiagramInfoDTO esDiagramInfo, String type) {
        List<ESDiagramSheetDTO> sheetList = esDiagramInfo.getSheetList();
        List<ESDiagramModel> modelList = esDiagramInfo.getModelList();
        Map<String, List<ESDiagramModel>> sheetModelMap = modelList.stream().collect(Collectors.groupingBy(ESDiagramModel::getSheetId));
        //1.生成视图id
        Long diagramId = IdGenerator.createGenerator().getID();
        ESDiagram esDiagram = new ESDiagram();
        BeanUtils.copyProperties(esDiagramInfo, esDiagram);
        esDiagram.setIcon1(null);
        if (!BinaryUtils.isEmpty(type) && "updateFull".equals(type)) {
            diagramId = esDiagramInfo.getId();
        } else {
            //设置视图的默认信息
            esDiagram.setId(diagramId);
            setDiagramBaseInfo(esDiagram);
        }
        esDiagram.setDEnergy(SecureUtil.md5(String.valueOf(diagramId)).substring(8, 24));
        esDiagram.setVersion(null);
        //获取待保存的sheet、dao、link集合
        List<ESDiagramNode> saveNodeList = new ArrayList<>();
        List<ESDiagramLink> saveLinkList = new ArrayList<>();
        for (ESDiagramSheetDTO sheetDTO : sheetList) {
            String sheetId = sheetDTO.getSheetId();
            sheetDTO.setDiagramId(diagramId);
            ESDiagramModel diagramModel = sheetModelMap.get(sheetId).get(0);
            sheetDTO.setDiagramClass(diagramModel.getDiagramClass());
            sheetDTO.setLinkFromPortIdProperty(diagramModel.getLinkFromPortIdProperty());
            sheetDTO.setLinkToPortIdProperty(diagramModel.getLinkToPortIdProperty());
            sheetDTO.setModelData(diagramModel.getModelData());
            sheetDTO.setSheetId(sheetId);
            Long sheetLogicId = IdGenerator.createGenerator().getID();
            sheetDTO.setId(sheetLogicId);

            List<ESDiagramNode> tmpNodeDataList = diagramModel.getNodeDataArray();
            if (!BinaryUtils.isEmpty(tmpNodeDataList)) {
                for (ESDiagramNode nodeDTO : tmpNodeDataList) {
                    Long nodeId = IdGenerator.createGenerator().getID();
                    nodeDTO.setId(nodeId);
                    nodeDTO.setDiagramId(diagramId);
                    nodeDTO.setSheetId(sheetId);
                    nodeToEnergy(nodeDTO, type);
                }
                saveNodeList.addAll(tmpNodeDataList);
            }

            List<ESDiagramLink> tmpLinkDataList = diagramModel.getLinkDataArray();
            if (!BinaryUtils.isEmpty(tmpLinkDataList)) {
                for (ESDiagramLink linkDTO : tmpLinkDataList) {
                    Long linkId = IdGenerator.createGenerator().getID();
                    linkDTO.setId(linkId);
                    linkDTO.setSheetId(sheetId);
                    linkDTO.setDiagramId(diagramId);
                    linkToEnergy(linkDTO, type);
                }
                saveLinkList.addAll(tmpLinkDataList);
            }

        }
        //保存视图和其组件
        esDiagramDao.saveOrUpdate(esDiagram);
        esDiagramSheetDao.saveOrUpdateBatch(sheetList);
        if (!BinaryUtils.isEmpty(saveNodeList)) {
            //修改node之前判断有关联视图ID，进行加密
            diagramNodeSvc.saveOrUpdateBatch(saveNodeList);
        }
        if (!BinaryUtils.isEmpty(saveLinkList)) {
            diagramLinkSvc.saveOrUpdateBatch(saveLinkList);
        }
        return diagramId;
    }

    @Override
    public void processBatchThumbnail(ThumbnailBatch thumbnailBatch) {
        List<ThumbnailBatch.ThumbnailInfo> thumbnailInfoList = thumbnailBatch.getThumbnailList();
        BinaryUtils.checkEmpty(thumbnailBatch.getThumbnailList(), "缩略图列表信息");
        for (ThumbnailBatch.ThumbnailInfo thumbnailInfo : thumbnailInfoList) {
            int resultCode = 1;
            String resultMsg = "success";

            // 代码扫描漏洞
            this.dealThumbnailInfo(thumbnailInfo, resultCode, resultMsg);

            /*try {
                String thumbnail = thumbnailInfo.getContent();
                String diagramId = thumbnailInfo.getId();
                BinaryUtils.checkEmpty(thumbnail, "缩略图信息串");
                String iconPath = thumbnailInfo.getPath();
                boolean pngCreation = false;
                //处理两张图用了同一个缩略图的情况
                if (StringUtils.isEmpty(iconPath) || queryByIcon(iconPath, diagramId) != null) {
                    pngCreation = true;
                    StringBuilder stringBuilder = new StringBuilder();
                    stringBuilder.append(UUID.randomUUID());
                    stringBuilder.append(".png");
                    iconPath = stringBuilder.toString();
                }else{
                    iconPath = iconPath.replace(httpResourceUrl, "");
                }
                iconPath = saveOrUpdatePng(iconPath, thumbnail, pngCreation);
                thumbnailInfo.setPath(new StringBuilder(httpResourceUrl).append(iconPath).toString());

                Long aLong = queryDiagramInfoByEnergy(diagramId);
                ESDiagram esDiagram = esDiagramDao.getById(aLong);
                if (BinaryUtils.isEmpty(esDiagram)) {
                    throw new BinaryException("操作的视图不存在");
                } else {
                    ESDiagram esDiagramForUpdate = new ESDiagram();
                    esDiagramForUpdate.setId(aLong);
                    esDiagramForUpdate.setDEnergy(diagramId);
                    esDiagramForUpdate.setThumbnailSaveTime(thumbnailInfo.getLastModifyTime());
                    esDiagramForUpdate.setIcon1(iconPath);
                    esDiagramForUpdate.setModifier(esDiagram.getModifier());
                    esDiagramForUpdate.setModifyTime(esDiagram.getModifyTime());
                    esDiagramDao.updateBatchThumbnailTime(esDiagramForUpdate, true);
                    thumbnailInfo.setContent(null);
                }
            } catch (Exception e) {
                logger.error("处理缩略图失败", e);
                resultCode = 0;
                resultMsg = e.getMessage();
            }*/
            thumbnailInfo.setResultCode(resultCode);
            thumbnailInfo.setResultMsg(resultMsg);
        }
    }

    private ESDiagram queryByIcon(String icon, String diagramId){
        if(BinaryUtils.isEmpty(icon)){
            return null;
        }
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.mustNot(QueryBuilders.termQuery("dEnergy.keyword", diagramId));
        query.must(QueryBuilders.termQuery("status", 1));
        query.must(QueryBuilders.termQuery("icon1.keyword", icon.replace(httpResourceUrl, "")));
        return esDiagramDao.selectOne(query);
    }

    public String saveOrUpdatePng(String filePath, String fileContent, boolean create) {
        byte[] bs;
        if (fileContent.length() >= fileContent.indexOf(";base64,") + 8) {
            String substring = fileContent.substring(fileContent.indexOf(";base64,") + 8);
            bs = Base64.decodeBase64(substring);
            filePath = this.saveOrUpdateResource(filePath, bs, create);
        }
        return filePath;
    }

    /**
     * 导出json为json文件，支持路径自定义
     *
     * @param filePath    文件名或文件相对路径
     * @param fileContent 文件内容
     * @return 文件名或文件相对路径
     */
    public String saveOrUpdateJson(String filePath, byte[] fileContent, boolean create) {
        if (create) {
            filePath = FileFilterUtil.parseFilePath(Paths.get(exportPath, filePath).toString());
        }
        try {
            FileUtil.writeFile(filePath, fileContent);
        } catch (IOException e) {
//            logger.warn("写入文件：{} 错误！", filePath, e);
        }
        return filePath;
    }

    /**
     * 将字符串保存/更新 为文件，自动加上日期前缀
     *
     * @param filePath    文件名或文件相对路径
     * @param fileContent 文件内容
     * @return 文件名或文件相对路径
     */
    public String saveOrUpdateResource(String filePath, byte[] fileContent, boolean create) {
        if (create) {
            filePath = Paths.get("/" + LocalDate.now(), filePath).toString();
        }
        try {
            FileUtil.writeFile(filePath, fileContent);
        } catch (IOException e) {
//            logger.warn("写入文件：{} 错误！", filePath, e);
        }
        return filePath;
    }

    /**
     * 通过class类型获取获取对应类型的值
     *
     * @param typeClass      class类型
     * @param fieldValueJson 值
     * @return Object
     */
    private static Object getClassTypeValue(Class<?> typeClass, String fieldValueJson) {
        if (typeClass == Integer.class) {
            if (null == fieldValueJson) {
                return 0;
            }
            return Integer.valueOf(fieldValueJson);
        } else if (typeClass == String.class) {
            if (null == fieldValueJson) {
                return "";
            }
            return fieldValueJson;
        } else if (typeClass == Boolean.class) {
            if (null == fieldValueJson) {
                return true;
            }
            return Boolean.valueOf(fieldValueJson);
        } else if (typeClass == List.class) {
            if (null == fieldValueJson) {
                return null;
            }
            return JSONUtil.toList(fieldValueJson, ESDiagramSheetDTO.SheetSettingDTO.LayerDTO.class);
        } else {
            throw new BinaryException("类型不符合要求");
        }
    }


    @Override
    public Integer deleteDiagramByIds(Long[] diagramIds) {
        if (BinaryUtils.isEmpty(diagramIds) || diagramIds.length <= 0) {
            throw new BinaryException("id不能为空");
        }
        List<ESDiagram> diagramInfoList = esDiagramDao.getListByQuery(QueryBuilders.termsQuery("id", diagramIds));
        if (BinaryUtils.isEmpty(diagramInfoList)) {
            return 0;
        }
        this.refreshLocalDiagramInfo(diagramInfoList);
        //根据diagramId集合物理删除视图
        //1.根据视图id集合查询视图数据
        EamDiagramQuery query = new EamDiagramQuery();
        query.setIds(diagramIds);
        Integer count = esDiagramDao.deleteByQuery(ESUtil.cdtToBuilder(query), true);
        //2.根据视图id集合查询其包含的所有sheet数据
        ESDiagramSheetQuery sheetQuery = new ESDiagramSheetQuery();
        sheetQuery.setDiagramIds(diagramIds);
        esDiagramSheetDao.deleteByQuery(ESUtil.cdtToBuilder(sheetQuery), true);
        //3.查询所有的node节点，将其按照diagramId和sheetId进行分组
        ESDiagramNodeQuery nodeQuery = new ESDiagramNodeQuery();
        nodeQuery.setDiagramIds(diagramIds);
        esDiagramNodeDao.deleteByQuery(ESUtil.cdtToBuilder(sheetQuery), true);
        //4.查询所有的link节点，将其按照diagramId和sheetId进行分组
        ESDiagramLinkQuery linkQuery = new ESDiagramLinkQuery();
        linkQuery.setDiagramIds(diagramIds);
        esDiagramLinkDao.deleteByQuery(ESUtil.cdtToBuilder(sheetQuery), true);
//        Set<String> energyIds = diagramInfoList.stream().map(ESDiagram::getDEnergy).collect(Collectors.toSet());
//        esDiagramDao.clearReleaseDiagramId(energyIds);
        esShareDiagramSvc.removeShareByDiagramIds(Arrays.asList(diagramIds));
        return count;
    }

    @Override
    public String deleteDiagramById(Long diagramId) {
        StringBuilder strBuilder = new StringBuilder("删除视图记录：");
        if (BinaryUtils.isEmpty(diagramId)) {
            throw new BinaryException("视图id不能为空");
        }
        List<ESDiagram> esDiagrams = judgeDiagramStatus(new Long[]{diagramId}, "");
        //1.根据视图id集合查询视图数据
        EamDiagramQuery query = new EamDiagramQuery();
        query.setId(diagramId);
        Integer deleteDiagramCount = esDiagramDao.deleteByQuery(ESUtil.cdtToBuilder(query), true);
        strBuilder.append("视图是否删除成功：").append(deleteDiagramCount);
        //2.根据视图id集合查询其包含的所有sheet数据
        ESDiagramSheetQuery sheetQuery = new ESDiagramSheetQuery();
        sheetQuery.setDiagramId(diagramId);
        Integer deleteSheetCount = esDiagramSheetDao.deleteByQuery(ESUtil.cdtToBuilder(sheetQuery), true);
        strBuilder.append("sheet是否删除成功：").append(deleteSheetCount);
        //3.查询所有的node节点，将其按照diagramId和sheetId进行分组
        ESDiagramNodeQuery nodeQuery = new ESDiagramNodeQuery();
        nodeQuery.setDiagramId(diagramId);
        Integer deleteNodeCount = esDiagramNodeDao.deleteByQuery(ESUtil.cdtToBuilder(sheetQuery), true);
        strBuilder.append("node是否删除成功：").append(deleteNodeCount);
        //4.查询所有的link节点，将其按照diagramId和sheetId进行分组
        ESDiagramLinkQuery linkQuery = new ESDiagramLinkQuery();
        linkQuery.setDiagramId(diagramId);
        Integer deleteLinkCount = esDiagramLinkDao.deleteByQuery(ESUtil.cdtToBuilder(sheetQuery), true);
        strBuilder.append("link是否删除成功：").append(deleteLinkCount);

        esShareDiagramSvc.removeShareByDiagramIds(Collections.singletonList(diagramId));
//        Set<String> diagramIdStrs = esDiagrams.stream().map(ESDiagram::getDEnergy).collect(Collectors.toSet());
//        esDiagramDao.clearReleaseDiagramId(diagramIdStrs);

        //本地源视图与删除的发布视图解除绑定关系
        if (!CollectionUtils.isEmpty(esDiagrams)) {
            this.releaseLocalDiagram(esDiagrams.get(0).getDEnergy());
        }
        return strBuilder.toString();
    }

    /**
     * 本地源视图与删除的发布视图解除绑定关系
     * @param diagramId
     */
    private void releaseLocalDiagram(String diagramId) {
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("releaseDiagramId.keyword", diagramId));
        query.must(QueryBuilders.termQuery("isOpen", 0));
        List<ESDiagram> diagrams = esDiagramDao.getListByQuery(query);
        for (ESDiagram esDiagram : diagrams) {
            esDiagram.setReleaseDiagramId("");
            esDiagram.setReleaseVersion(0);
            esDiagramDao.saveOrUpdate(esDiagram);
        }
    }

    @Override
    public String exportDiagram(String jsonStr) {
        // 保存视图信息实体文件
        return this.saveOrUpdateJson(UUID.randomUUID().toString() + ".json", jsonStr.getBytes(), true);
    }

    @Override
    public Long[] queryDiagramInfoBydEnergy(String[] encryptDiagramIds) {
        EamDiagramQuery diagramQuery = new EamDiagramQuery();
        diagramQuery.setDEnergys(encryptDiagramIds);
//        diagramQuery.setStatus(1);
//        diagramQuery.setDataStatus(1);
//        diagramQuery.setHistoryVersionFlag(1);
        Page<ESDiagram> diagramPage = esDiagramDao.getSortListByCdt(0, encryptDiagramIds.length, diagramQuery, "modifyTime", false);
        List<ESDiagram> diagramList = diagramPage.getData();
        if (diagramList.isEmpty()) {
            throw new BinaryException("当前视图已被删除,无法操作!");
        }
        if (encryptDiagramIds.length != diagramList.size()) {
            logger.info("传入的加密id数量：{},包含元素：{}, 查出的视图数量：{}, 包含元素：{}", encryptDiagramIds.length, encryptDiagramIds, diagramList.size(), diagramList.stream().map(ESDiagram::getId).toArray(Long[]::new));
            throw new BinaryException("批量查询的视图，部分已删除或没有权限");
        }
        return diagramList.stream().map(ESDiagram::getId).distinct().toArray(Long[]::new);
    }

    @Override
    public Long queryDiagramInfoByEnergy(String diagramId) {
        EamDiagramQuery diagramQuery = new EamDiagramQuery();
        diagramQuery.setDEnergyEqual(diagramId);
//        diagramQuery.setStatus(1);
//        diagramQuery.setDataStatus(1);
//        diagramQuery.setHistoryVersionFlag(1);
        Page<ESDiagram> sortListByCdt = esDiagramDao.getSortListByCdt(0, 1, diagramQuery, "modifyTime", false);
        List<ESDiagram> data = sortListByCdt.getData();
        if (1 != data.size()) {
            throw new BinaryException("查询的视图，已删除或没有权限");
        }
        if (!diagramId.equals(data.get(0).getDEnergy())) {
            throw new BinaryException("查询的视图，已删除或没有权限");
        }
        Long[] nodeKeyArr = data.stream().map(ESDiagram::getId).distinct().toArray(Long[]::new);
        return nodeKeyArr[0];
    }

    @Override
    public Page<ESDiagram> queryData(Long domainId, Integer pageNum, Integer pageSize, CVcDiagram dCdt, String orders) {
        //1.构建查询条件，查询视图详情
        MessageUtil.checkEmpty(domainId, "domainId");
        dCdt = dCdt == null ? new CVcDiagram() : dCdt;
        String like = dCdt.getName();
        EamDiagramQuery esDiagramForQuery = new EamDiagramQuery();
        BeanUtils.copyProperties(dCdt, esDiagramForQuery);
        esDiagramForQuery.setDataStatus(1);
        esDiagramForQuery.setHistoryVersionFlag(1);
        esDiagramForQuery.setName(null);
        BoolQueryBuilder boolQueryBuilder = ESUtil.cdtToBuilder(esDiagramForQuery);
        if (!StringUtils.isEmpty(like)) {
            boolQueryBuilder.must(QueryBuilders.wildcardQuery("name.keyword", "*" + like.trim() + "*"));
        }
        orders = BinaryUtils.isEmpty(orders) ? "modifyTime" : orders;
        orders = ESUtil.underlineToCamel(orders);
        Page<ESDiagram> esDiagramPage = esDiagramDao.getSortListByQuery(pageNum, pageSize, boolQueryBuilder, orders, false);
        //2.填充缩略图路径信息
        List<ESDiagram> esDiagramList = esDiagramPage.getData();
        if (!BinaryUtils.isEmpty(esDiagramList)) {
            for (ESDiagram esDiagram : esDiagramList) {
                String icon1 = esDiagram.getIcon1();
                if (icon1 != null && !icon1.startsWith(httpResourceUrl)) {
                    StringBuilder stringBuilder = new StringBuilder();
                    stringBuilder.append(httpResourceUrl);
                    stringBuilder.append(icon1);
//                icon1 = httpResourceUrl + icon1;
                    esDiagram.setIcon1(stringBuilder.toString());
                }
                String diagramBgImg = esDiagram.getDiagramBgImg();
                if (diagramBgImg != null && !BinaryUtils.isEmpty(diagramBgImg.trim())
                        && !diagramBgImg.startsWith(httpResourceUrl)) {
                    esDiagram.setDiagramBgImg(new StringBuilder(httpResourceUrl).append(diagramBgImg).toString());
                }
            }
        }
        //3.封装返回结果
        Page<ESDiagram> DiagramDTOPage = new Page<>();
        DiagramDTOPage.setPageNum(esDiagramPage.getPageNum());
        DiagramDTOPage.setPageSize(esDiagramPage.getPageSize());
        DiagramDTOPage.setTotalPages(esDiagramPage.getTotalPages());
        DiagramDTOPage.setTotalRows(esDiagramPage.getTotalRows());
        DiagramDTOPage.setData(esDiagramList);
        return DiagramDTOPage;
    }

    @Override
    public void clearData() {
        List<Long> diagramIds = esDiagramDao.remove();
        esDiagramNodeDao.remove(diagramIds);
        esDiagramLinkDao.remove(diagramIds);
        esDiagramSheetDao.remove(diagramIds);
    }

    @Override
    public void removeDiagrams(List<Long> dirIds, String ownerCode) {
        dirIds.forEach(dirId -> {
            BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
            queryBuilder.must(QueryBuilders.termQuery("dirId", dirId));
            queryBuilder.must(QueryBuilders.termQuery("isOpen", 0));
            queryBuilder.must(QueryBuilders.termQuery("ownerCode.keyword", ownerCode));
            List<ESDiagram> diagramList = esDiagramDao.getListByQuery(queryBuilder);
            if (CollectionUtils.isNotEmpty(diagramList)) {
                List<Long> collect = diagramList.stream().map(ESDiagram::getId).collect(Collectors.toList());
                this.deleteDiagramByIds(collect.toArray(new Long[0]));
            }
        });
    }

    @Override
    public List<ESDiagram> findEsDiagramList(Long dirId, String ownerCode) {
        MessageUtil.checkEmpty(dirId, "dirId");
        MessageUtil.checkEmpty(ownerCode, "ownerCode");
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.must(QueryBuilders.termQuery("dirId", dirId));
        queryBuilder.must(QueryBuilders.termQuery("isOpen", 0));
        queryBuilder.must(QueryBuilders.termQuery("ownerCode.keyword", ownerCode));
        queryBuilder.must(QueryBuilders.termQuery("historyVersionFlag", 1));
        return esDiagramDao.getListByQuery(queryBuilder);
    }

    @Override
    public ESDiagram getEsDiagram(String dEnergy, Integer isOpen) {
        MessageUtil.checkEmpty(dEnergy, "dEnergy");
        MessageUtil.checkEmpty(isOpen, "isOpen");
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.must(QueryBuilders.termQuery("dEnergy.keyword", dEnergy));
        queryBuilder.must(QueryBuilders.termQuery("isOpen", isOpen));
        queryBuilder.must(QueryBuilders.termQuery("historyVersionFlag", 1));
        Page<ESDiagram> diagramPage = esDiagramDao.getSortListByQuery(1, 1, queryBuilder, "releaseVersion", false);
        List<ESDiagram> data = diagramPage.getData();
        if (CollectionUtils.isNotEmpty(data)) {
            return data.get(0);
        }
        return null;
    }

    @Override
    public int updateNameByDEnergyId(String newName, String dEnergyId) {
        return esDiagramDao.updateNameByDEnergyId(newName, dEnergyId);
    }

    @Override
    public List<ESDiagramNode> selectNodeByDiagramIds(List<Long> diagramIds) {
        return diagramNodeSvc.getNodeByDiagram(diagramIds, null);
    }

    @Override
    public List<ESDiagramLink> selectLinkByDiagramIds(List<Long> diagramIds) {
        return diagramLinkSvc.getLinkByDiagram(diagramIds, null);
    }

    @Override
    public List<ESDiagramNode> selectNodeByCiCodes(List<String> ciCodes, String ownerCode) {
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.must(QueryBuilders.termsQuery("ciCode.keyword", ciCodes));
        if(!BinaryUtils.isEmpty(ownerCode)){
            // List<ESDiagram> diagramList = esDiagramDao.getListByQuery(QueryBuilders.termQuery("ownerCode.keyword", ownerCode));
            // 用户数据超过 3000 变更查询方法
            List<ESDiagram> diagramList = esDiagramDao.getListByQueryScroll(QueryBuilders.termQuery("ownerCode.keyword", ownerCode));
            if(BinaryUtils.isEmpty(diagramList)){
                return Collections.emptyList();
            }
            List<Long> diagramIds = diagramList.stream().map(ESDiagram::getId).collect(Collectors.toList());
            boolQuery.must(QueryBuilders.termsQuery("diagramId", diagramIds));
        }
        return esDiagramNodeDao.selectListByQuery(1, 3000, boolQuery);
    }

    @Override
    public List<ESDiagramLink> selectLinkByRltCiCodes(List<String> uniqueCodes) {
        return esDiagramLinkDao.selectListByQuery(1, 3000, QueryBuilders.termsQuery("uniqueCode.keyword", uniqueCodes));
    }

    @Override
    public List<ESDiagramLink> selectLinkByRltCodes(Collection<String> uniqueCodes, String ownerCode) {
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.must(QueryBuilders.termsQuery("uniqueCode.keyword", uniqueCodes));
        if(!BinaryUtils.isEmpty(ownerCode)){
            List<ESDiagram> diagramList = esDiagramDao.getListByQueryScroll(QueryBuilders.termQuery("ownerCode.keyword", ownerCode));
            if(BinaryUtils.isEmpty(diagramList)){
                return Collections.emptyList();
            }
            List<Long> diagramIds = diagramList.stream().map(ESDiagram::getId).collect(Collectors.toList());
            boolQuery.must(QueryBuilders.termsQuery("diagramId", diagramIds));
        }
        return esDiagramLinkDao.selectListByQuery(1, 3000, boolQuery);
    }

    @Override
    public void replaceLinkList(List<ESDiagramLink> linkList) {
        if (BinaryUtils.isEmpty(linkList)) {
            return;
        }
        diagramLinkSvc.saveOrUpdateBatch(linkList);
    }

    @Override
    public void replaceNodeList(List<ESDiagramNode> nodeList) {
        if (BinaryUtils.isEmpty(nodeList)) {
            return;
        }
        diagramNodeSvc.saveOrUpdateBatch(nodeList);
    }

    /**
     *  删除视图如果为资产仓库的视图
     *  重置我的空间关联视图releaseDiagramId/releaseVersion/flowStatus
     * @param diagramInfo
     */
    private void refreshLocalDiagramInfo(List<ESDiagram> diagramInfo) {
        List<String> releaseDiagramIds = new ArrayList<>();
        for (ESDiagram diagram : diagramInfo) {
            if (diagram.getIsOpen() == 1) {
                releaseDiagramIds.add(diagram.getDEnergy());
            }
        }
        if (CollectionUtils.isEmpty(releaseDiagramIds)) {
            return;
        }
        BoolQueryBuilder bool = QueryBuilders.boolQuery();
        // 所有用户我的空间与删除的资产仓库有关联的视图
        bool.must(QueryBuilders.termQuery("isOpen", 0));
        bool.must(QueryBuilders.termsQuery("releaseDiagramId.keyword", releaseDiagramIds));
        List<ESDiagram> localDiagramList = esDiagramDao.getListByQuery(bool);
        if (CollectionUtils.isEmpty(localDiagramList)) {
            return;
        }
        for (ESDiagram localDiagram : localDiagramList) {
            localDiagram.setReleaseDiagramId("");
            localDiagram.setReleaseVersion(0);
            localDiagram.setFlowStatus(0);
        }
        esDiagramDao.saveOrUpdateBatch(localDiagramList);
    }

    private Map<Long, SysUser> queryUserList(Set<Long> creatorIdSet) {
        CSysUser cSysUser = new CSysUser();
        cSysUser.setDomainId(1L);
        cSysUser.setIds(creatorIdSet.toArray(new Long[]{}));
        cSysUser.setSuperUserFlags(new Integer[]{0, 1});
        List<SysUser> creatorList = userApiSvc.getSysUserByCdt(cSysUser);
        Map<Long, SysUser> userIdObjMap = new HashMap<>();
        creatorList.forEach(
                creator -> {
                    creator.setLoginPasswd(null);
                    userIdObjMap.put(creator.getId(), creator);
                    String icon = creator.getIcon();
                    // 这儿判断是因为当前用户是同一个对象，所以再修改会无限拼接，勿删！！！！！！
                    if (icon != null && !icon.startsWith(this.httpResourceUrl)) {
                        StringBuilder stringBuilder = new StringBuilder();
                        stringBuilder.append(httpResourceUrl);
                        stringBuilder.append(icon);
                        creator.setIcon(stringBuilder.toString());
                    }
                }
        );
        return userIdObjMap;
    }

    /**
     * 设置视图的基础信息
     */
    private void setDiagramBaseInfo(ESDiagram esDiagram) {
        if (BinaryUtils.isEmpty(esDiagram.getDirId())) {
            //所属目录id，默认为根级别
            esDiagram.setDirId(0L);
        }
        //目录类型默认为私人
        if (BinaryUtils.isEmpty(esDiagram.getDirType())) {
            esDiagram.setDirType(0);
        }
        //默认类型空白视图
        if (BinaryUtils.isEmpty(esDiagram.getDiagramSubType())) {
            esDiagram.setDiagramSubType(DiagramSubTypeEnum.BLANK.getVal());
            if (!BinaryUtils.isEmpty(esDiagram.getViewType()) && !"0".equals(esDiagram.getViewType())) {
                esDiagram.setDiagramSubType(DiagramSubTypeEnum.PRODUCT.getVal());
            }
        }
        esDiagram.setDiagramType(1);
        if (BinaryUtils.isEmpty(esDiagram.getSubjectId())) {
            esDiagram.setSubjectId(0L);
        }
        esDiagram.setDEnergy(SecureUtil.md5(String.valueOf(esDiagram.getId())).substring(8, 24));
        if (BinaryUtils.isEmpty(esDiagram.getIsOpen())) {
            esDiagram.setIsOpen(0);
        }
        //是否放到回收站标识，默认为1，未放
        if (BinaryUtils.isEmpty(esDiagram.getStatus())) {
            esDiagram.setStatus(1);
        }
        //是否从回收站删除标识，默认为1，未放
        if (BinaryUtils.isEmpty(esDiagram.getDataStatus())) {
            esDiagram.setDataStatus(1);
        }
        Long currTime = BinaryUtils.getNumberDateTime();
        esDiagram.setCreateTime(currTime);
        esDiagram.setThumbnailSaveTime(19700101000000L);
        //设置历史视图标识，默认为1，既主视图
        esDiagram.setHistoryVersionFlag(1);
        //2.设置用户信息
        SysUser currUser = new SysUser();
        if (BinaryUtils.isEmpty(esDiagram.getOwnerCode())) {
            currUser = ThreadVariable.getSysUser();
        } else {
            CSysUser cSysUser = new CSysUser();
            cSysUser.setLoginCodeEqual(esDiagram.getOwnerCode());
            List<SysUser> userList = userApiSvc.getSysUserByCdt(cSysUser);
            if (!CollectionUtils.isEmpty(userList)) {
                currUser = userList.get(0);
            }
        }
        if (currUser == null) {
            currUser = SysUtil.getCurrentUserInfo();
        }
        esDiagram.setUserId(currUser.getId());
        esDiagram.setOwnerCode(currUser.getLoginCode());
        esDiagram.setDomainId(currUser.getDomainId());
        esDiagram.setCreator(currUser.getLoginCode());
        // 添加预制ID字段 业务意义与releaseDiagramId相同 只是提前创建出来
        String prepareDiagramId = esDiagram.getPrepareDiagramId();
        if (BinaryUtils.isEmpty(prepareDiagramId)) {
            Long prepareId;
            if (!BinaryUtils.isEmpty(esDiagram.getId())) {
                prepareId = esDiagram.getId();
            } else {
                prepareId = IdGenerator.createGenerator().getID();
            }
            esDiagram.setPrepareDiagramId(SecureUtil.md5(String.valueOf(prepareId)).substring(8, 24));
        }

    }

    /**
     * 前端想要实现的是拉一个sheet，没有id，有sheetId（即前端自己生成的sheet标识）
     * 这样就不用等待后台返回id，就能对sheet进行各种操作
     * 前台只传递sheetId，不传递id
     * 实现思路：
     * 对于新增操作，无需干预，会自动新增
     * 对于更新操作，先根据sheetId查出对于的obj，
     * 如果有，就将obj的id设为updateSheet的id
     * 如果没有，就还是一个新增操作
     */
    private List<ESResponseStruct> saveOrUpdateSheet(Long diagramId, String opList, Boolean flag) {
        MessageUtil.checkEmpty(diagramId, "保存或更新link时diagramId不能为空");
        List<ESDiagramSheetDTO> saveOrUpdateSheetList = JSONUtil.toList(opList, ESDiagramSheetDTO.class);
        List<ESResponseStruct> structList = new ArrayList<>();
        ESResponseStruct esResponseStruct = new ESResponseStruct();
        //更新操作
        if (flag) {
            //获取所有的sheetId，并根据sheetId查出其对应的sheet对象
            String[] sheetIdArr = saveOrUpdateSheetList
                    .stream()
                    .map(ESDiagramSheetDTO::getSheetId)
                    .distinct()
                    .filter(Objects::nonNull)
                    .toArray(String[]::new);
            if (BinaryUtils.isEmpty(sheetIdArr)) {
                ESResponseStruct struct = new ESResponseStruct();
                struct.setErrorMessage("更新link时需要设置linkKey");
                structList.add(struct);
                return structList;
            }
            ESDiagramSheetQuery sheetQuery = new ESDiagramSheetQuery();
            sheetQuery.setSheetIds(sheetIdArr);
            sheetQuery.setDiagramId(diagramId);
            List<ESDiagramSheetDTO> originSheetList = esDiagramSheetDao.getListByCdt(sheetQuery);

            //对于数据库sheet集合，建立sheetId和logicId之间的映射关系
            Map<String, Long> sheetIdMap = new HashMap<>(16);
            if (!BinaryUtils.isEmpty(originSheetList)) {
                for (ESDiagramSheetDTO originSheet : originSheetList) {
                    sheetIdMap.put(originSheet.getSheetId(), originSheet.getId());
                }
            } else {
                return structList;
            }
            if (BinaryUtils.isEmpty(sheetIdMap)) {
                return structList;
            }
            //根据sheetId获取sheet对象和逻辑id
            List<ESDiagramSheetDTO> skipSheetList = new ArrayList<>();
            for (ESDiagramSheetDTO sheetDTO : saveOrUpdateSheetList) {
                String sheetId = sheetDTO.getSheetId();
                Long sheetLogicId = sheetIdMap.get(sheetId);
                if (!BinaryUtils.isEmpty(sheetLogicId)) {
                    sheetDTO.setId(sheetLogicId);
                } else {
                    //说明已经存在，但是数据库还没处理完，需要跳过处理
                    skipSheetList.add(sheetDTO);
                }
            }
            if (!BinaryUtils.isEmpty(skipSheetList)) {
                saveOrUpdateSheetList.removeAll(skipSheetList);
            }
            if (!BinaryUtils.isEmpty(saveOrUpdateSheetList)) {
                esDiagramSheetDao.saveOrUpdateBatch(saveOrUpdateSheetList);
            }
        } else {
            //新增操作，需要设置diagramId
            List<ESDiagramSheetDTO> errorSheetList = new ArrayList<>();
            for (ESDiagramSheetDTO saveSheet : saveOrUpdateSheetList) {
                if (StringUtils.isEmpty(saveSheet.getSheetId())) {
                    esResponseStruct.setDiagramId(diagramId);
                    esResponseStruct.setErrorMessage("错误sheet，没有sheetId");
                    structList.add(esResponseStruct);
                    errorSheetList.add(saveSheet);
                    continue;
                }
                saveSheet.setDiagramId(diagramId);
            }
            if (!BinaryUtils.isEmpty(errorSheetList)) {
                saveOrUpdateSheetList.removeAll(errorSheetList);
            }
            if (!BinaryUtils.isEmpty(saveOrUpdateSheetList)) {
                esDiagramSheetDao.saveOrUpdateBatch(saveOrUpdateSheetList);
            }
        }
        return structList;
    }

    /**
     * @return
     * <AUTHOR>
     * @Description 判断当前操作的视图是否被删除
     * @Date 13:18 2021/7/2
     * @Param
     **/
    private List<ESDiagram> judgeDiagramStatus(Long[] diagramIds, String type) {
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
//        Long userId = currentUserInfo.getId();
        Long domainId = currentUserInfo.getDomainId();
        EamDiagramQuery diagramQuery = new EamDiagramQuery();
        diagramQuery.setIds(diagramIds);
        diagramQuery.setStatus(1);
        diagramQuery.setDataStatus(1);
//        diagramQuery.setUserId(userId);
        diagramQuery.setDomainId(domainId);
        if (!BinaryUtils.isEmpty(type) && "version".equals(type)) {
            diagramQuery.setHistoryVersionFlag(0);
        } else {
            diagramQuery.setHistoryVersionFlag(1);
        }
        List<ESDiagram> diagramList = esDiagramDao.getListByCdt(diagramQuery);
        if (BinaryUtils.isEmpty(diagramList)) {
//            throw new BinaryException("操作视图不存在，请核对视图当前信息");
            logger.error("操作视图不存在，请核对视图当前信息,ids:" + diagramIds);
        }
        return diagramList;
    }

    /**
     * <AUTHOR>
     * @Description 判断用户是否具有当前视图的权限
     * 1.不限制userId进行查询
     * 查不出---视图被删除
     * 查出来---判断视图的userId是否等于当前用户
     * 等于---有权限
     * 不等于---查询该用户是否被分享了该视图
     * 分享了，有两种权限，一种是查看，一种是编辑
     * 没分享，无权限，报错
     **/
    private List<ESDiagram> judgeDiagramAuth(Long[] diagramIds, String type, Boolean needAuth) {
        SysUser currUser = null;
        //1.不限制userId进行查询
        EamDiagramQuery diagramQuery = new EamDiagramQuery();
        diagramQuery.setIds(diagramIds);
        diagramQuery.setStatus(1);
        if (!BinaryUtils.isEmpty(type) && ("BM_D_HISTORY".equals(type) || "FX_D_HISTORY".equals(type) || "SNAPSHOT".equals(type))) {
            diagramQuery.setStatus(null);
        }
        diagramQuery.setDataStatus(1);
        if (needAuth) {
            currUser = SysUtil.getCurrentUserInfo();
            diagramQuery.setDomainId(currUser.getDomainId());
        }
        diagramQuery.setHistoryVersionFlag(1);
        if (!BinaryUtils.isEmpty(type) && "version".equals(type)) {
            diagramQuery.setHistoryVersionFlag(0);
        }
        if (!BinaryUtils.isEmpty(type) && "FX_D_HISTORY".equals(type)) {
            diagramQuery.setHistoryVersionFlag(null);
        }
        if (!BinaryUtils.isEmpty(type) && "CJ_D_HISTORY".equals(type)) {
            diagramQuery.setHistoryVersionFlag(null);
        }
        if (!BinaryUtils.isEmpty(type) && "SNAPSHOT".equals(type)) {
            diagramQuery.setHistoryVersionFlag(null);
        }
        List<ESDiagram> diagramList = esDiagramDao.getListByCdt(diagramQuery);
        //2.查不出，视图不存在，报错
        if (BinaryUtils.isEmpty(diagramList)) {
            throw new BinaryException("操作视图不存在，请核对视图当前信息");
        }
        if (needAuth) {
            if (currUser != null) {
                Long userId = currUser.getId();
                String loginCode = currUser.getLoginCode();
                //3.查出来，判断其是否具有视图权限
                boolean authFlag = true;
                List<ESDiagram> needAuthDiagramList = diagramList
                        .stream()
                        .filter(diagram -> !diagram.getOwnerCode().equals(loginCode) && !diagram.getDiagramType().equals(3))
                        .collect(Collectors.toList());
                //存在不属于该用户的视图
                if (!BinaryUtils.isEmpty(needAuthDiagramList)) {
                    Long[] needAuthIdArr = needAuthDiagramList.stream().map(ESDiagram::getId).distinct().toArray(Long[]::new);
                    Map<Long, Set<Long>> diagramIdShareRecordMap = eamShareDiagramSvc.queryDiagramSharedUserIds(needAuthIdArr);

                    if (BinaryUtils.isEmpty(diagramIdShareRecordMap)) {
                        authFlag = false;
                    }
                    if (authFlag) {
                        for (Set<Long> userIdSet : diagramIdShareRecordMap.values()) {
                            if (!userIdSet.contains(userId)) {
                                authFlag = false;
                                break;
                            }
                        }
                    }
                }
                if (!authFlag) {
                    throw new UnAuthorizedException("用户无当前视图权限");
                }
            }

        }
        return diagramList;
    }

    private List<ESDiagram> judgeDiagramAuthNew(Long[] diagramIds, String type, Boolean needAuth) {
        SysUser currUser = null;
        //1.不限制userId进行查询
        EamDiagramQuery diagramQuery = new EamDiagramQuery();
        diagramQuery.setIds(diagramIds);
        diagramQuery.setStatus(1);
        if (!BinaryUtils.isEmpty(type) && ("BM_D_HISTORY".equals(type) || "FX_D_HISTORY".equals(type))) {
            diagramQuery.setStatus(null);
        }
        diagramQuery.setDataStatus(1);
        if (needAuth) {
            currUser = SysUtil.getCurrentUserInfo();
            diagramQuery.setDomainId(currUser.getDomainId());
        }
        diagramQuery.setHistoryVersionFlag(1);
        if (!BinaryUtils.isEmpty(type) && "version".equals(type)) {
            diagramQuery.setHistoryVersionFlag(0);
        }
        if (!BinaryUtils.isEmpty(type) && "FX_D_HISTORY".equals(type)) {
            diagramQuery.setHistoryVersionFlag(null);
        }
        if (!BinaryUtils.isEmpty(type) && "CJ_D_HISTORY".equals(type)) {
            diagramQuery.setHistoryVersionFlag(null);
        }
        List<ESDiagram> diagramList = esDiagramDao.getListByCdt(diagramQuery);
        //2.查不出，视图不存在，返回空数组
        if (BinaryUtils.isEmpty(diagramList)) {
            return Lists.newArrayList();
        }
        if (needAuth && currUser != null) {
            Long userId = currUser.getId();
            String loginCode = currUser.getLoginCode();
            //3.查出来，判断其是否具有视图权限
            boolean authFlag = true;
            List<ESDiagram> needAuthDiagramList = diagramList
                    .stream()
                    .filter(diagram -> !diagram.getOwnerCode().equals(loginCode) && !diagram.getDiagramType().equals(3))
                    .collect(Collectors.toList());
            //存在不属于该用户的视图
            if (!BinaryUtils.isEmpty(needAuthDiagramList)) {
                Long[] needAuthIdArr = needAuthDiagramList.stream().map(ESDiagram::getId).distinct().toArray(Long[]::new);
                Map<Long, Set<Long>> diagramIdShareRecordMap = eamShareDiagramSvc.queryDiagramSharedUserIds(needAuthIdArr);

                if (BinaryUtils.isEmpty(diagramIdShareRecordMap)) {
                    authFlag = false;
                }
                if (authFlag) {
                    for (Set<Long> userIdSet : diagramIdShareRecordMap.values()) {
                        if (!userIdSet.contains(userId)) {
                            authFlag = false;
                            break;
                        }
                    }
                }
            }
            if (!authFlag) {
                throw new UnAuthorizedException("用户无当前视图权限");
            }
        }
        return diagramList;
    }

    /**
     * <AUTHOR>
     * @Description 判断用户是否具有当前视图的权限
     * 1.不限制userId进行查询
     * 查不出---视图被删除
     * 查出来---判断视图的userId是否等于当前用户
     * 等于---有权限
     * 不等于---查询该用户是否被分享了该视图
     * 分享了，有两种权限，一种是查看，一种是编辑
     * 没分享，无权限，报错
     **/
    @Override
    public ESDiagram judgeSingleDiagramAuth(Long diagramId, String type, Boolean needAuth) {
        //1.不限制userId进行查询
        EamDiagramQuery diagramQuery = new EamDiagramQuery();
        diagramQuery.setId(diagramId);
        diagramQuery.setStatus(1);
        if (!BinaryUtils.isEmpty(type) && "BM_D_HISTORY".equals(type)) {
            diagramQuery.setStatus(null);
        }
        diagramQuery.setDataStatus(1);
        diagramQuery.setDomainId(SYS_DOMAIN_ID);
        diagramQuery.setHistoryVersionFlag(1);
        if (!BinaryUtils.isEmpty(type) && "version".equals(type)) {
            diagramQuery.setHistoryVersionFlag(0);
        }
        List<ESDiagram> diagramList = esDiagramDao.getListByCdt(diagramQuery);
        //2.查不出，视图不存在，报错
        if (BinaryUtils.isEmpty(diagramList)) {
            throw new DiagramNotFoundException("操作视图不存在，请核对视图当前信息");
        }
        //3.查出来，判断其是否具有视图权限
        ESDiagram esDiagram = diagramList.get(0);
        return esDiagram;
    }

    /**
     * @return void
     * <AUTHOR>
     * @Description 批量为主版本保存视图组件和创建历史记录
     * @Date 10:19 2021/8/12
     * @Param [saveSimpleDiagramList]
     **/
    private void saveSimpleDiagramBatch(List<ESSimpleDiagram> saveSimpleDiagramList) {
        List<ESDiagram> esDiagramList = saveSimpleDiagramList
                .stream()
                .map(ESSimpleDiagram::getEsDiagram)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        List<ESDiagramSheetDTO> esDiagramSheetList = saveSimpleDiagramList
                .stream()
                .map(ESSimpleDiagram::getSheetList)
                .flatMap(List::stream)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        List<ESDiagramNode> esDiagramNodeList = saveSimpleDiagramList
                .stream()
                .map(ESSimpleDiagram::getNodeList)
                .flatMap(List::stream)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        List<ESDiagramLink> esDiagramLinkList = saveSimpleDiagramList
                .stream()
                .map(ESSimpleDiagram::getLinkList)
                .flatMap(List::stream)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        if (!BinaryUtils.isEmpty(esDiagramList)) {
            esDiagramDao.saveOrUpdateBatchWithOption(esDiagramList);
        }
        if (!BinaryUtils.isEmpty(esDiagramSheetList)) {
            esDiagramSheetDao.saveOrUpdateBatch(esDiagramSheetList);
        }
        if (!BinaryUtils.isEmpty(esDiagramNodeList)) {
            diagramNodeSvc.saveOrUpdateBatch(esDiagramNodeList);
        }
        if (!BinaryUtils.isEmpty(esDiagramLinkList)) {
            diagramLinkSvc.saveOrUpdateBatch(esDiagramLinkList);
        }
        //为主版本创建历史记录
        List<ESDiagram> finalESDiagramList = esDiagramList
                .stream()
                .filter(esDiagram -> esDiagram.getHistoryVersionFlag().equals(1))
                .collect(Collectors.toList());
        for (ESDiagram esDiagram : finalESDiagramList) {
            if (esDiagram.getHistoryVersionFlag().equals(1)) {

                // 代码扫描漏洞
                this.createTrySingleVersionRecord(esDiagram);

                /*try {
                    createSingleVersionRecord(esDiagram);
                } catch (Exception e) {
                    logger.error("生成主版本记录时发生错误，diagramId={}", esDiagram.getId());
                    e.printStackTrace();
                }*/
            }
        }
    }

    private void createESDiagramVersionRecord(Long[] diagramIds) {
        List<VcDiagramVersion> diagramVersionList = new ArrayList<>();
        SysUser sysUser = ThreadVariable.getSysUser();
        if (sysUser == null) {
            sysUser = SysUtil.getCurrentUserInfo();
        }
        for (Long diagramId : diagramIds) {
            VcDiagramVersion diagramVersion = new VcDiagramVersion();
            diagramVersion.setId(diagramId);
            diagramVersion.setStatus(1);
            diagramVersion.setDiagramId(diagramId);
            diagramVersion.setDomainId(SYS_DOMAIN_ID);
            diagramVersion.setVersionTime(BinaryUtils.getNumberDateTime());
            diagramVersion.setCreator(buildCreator(sysUser));
            diagramVersionList.add(diagramVersion);
        }
        diagramVersionDao.insertBatchAsync(diagramVersionList, sysUser);
    }

    private void createSingleVersionRecord(ESDiagram esDiagram) {
        VcDiagramVersion diagramVersion = new VcDiagramVersion();
        diagramVersion.setId(esDiagram.getId());
        diagramVersion.setStatus(1);
        diagramVersion.setDiagramId(esDiagram.getId());
        diagramVersion.setDomainId(SYS_DOMAIN_ID);
        diagramVersion.setVersionTime(esDiagram.getModifyTime());
        diagramVersion.setCreator(buildOldCreator(esDiagram.getUserId()));
        diagramVersionDao.insert(diagramVersion);
    }

    /**
     * 组件创建者 格式：用户名[用户code]
     *
     * @return
     */
    private String buildCreator(SysUser loginUser) {
        String creator;
        //查询当前用户最新的信息
        UserInfo op = userApiSvc.getUserInfoById(loginUser.getId());
        if (!BinaryUtils.isEmpty(op)) {
            creator = op.getUserName() + "[" + op.getLoginCode() + "]";
        } else {
            creator = loginUser.getUserName() + "[" + loginUser.getLoginCode() + "]";
        }
        return creator;
    }

    /**
     * 组件创建者 格式：用户名[用户code]
     *
     * @return
     */
    private String buildOldCreator(Long userId) {
        String creator = "";
        //查询当前用户最新的信息
        UserInfo op = userApiSvc.getUserInfoById(userId);
        if (!BinaryUtils.isEmpty(op)) {
            creator = op.getUserName() + "[" + op.getLoginCode() + "]";
        }
        return creator;
    }


    @Override
    public void updateDiagramByQuery(Long diagramId, Long value) {
        EamDiagramQuery diagramQuery = new EamDiagramQuery();
        diagramQuery.setId(diagramId);
        ESDiagram before = esDiagramDao.getById(diagramId);
        System.out.println("before: " + before);
        String scriptStr = "ctx._source.dirId=params.dirId";
        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("dirId", value);
        esDiagramDao.updateByQuery(ESUtil.cdtToBuilder(diagramQuery), scriptStr, true, paramsMap);
    }


    public List<ESDiagramNode> getAllNode(ESDiagramNodeQueryBean nodeQuery, List<ESDiagramNode> nodeList) {
        long totalNodeCount = esDiagramNodeDao.countByCondition(ESUtil.cdtToBuilder(nodeQuery));
        if (totalNodeCount > MAX_QUERY_COUNT) {
            throw new BinaryException("一次查询数据不能超过30000条");
        }
        if (totalNodeCount > PAGE_COUNT) {
            long loopCount = totalNodeCount / PAGE_COUNT + 1;
            for (int start = 1; start <= loopCount; ++start) {
                //分页查询
                Page<ESDiagramNode> tmpList = esDiagramNodeDao.getListByCdt(start, 3000, nodeQuery);
                nodeList.addAll(tmpList.getData());
            }
        } else {
            nodeList = esDiagramNodeDao.getListByCdt(nodeQuery);
        }
        List<ESCIClassInfo> listByQueryScroll = esciClassSvc.getListByQueryScroll(QueryBuilders.matchAllQuery());
        Map<String, Long> convertMap = listByQueryScroll.stream().collect(Collectors.toMap(ESCIClassInfo::getClassName, ESCIClassInfo::getId, (k1, k2) -> k1));

        nodeList.forEach(n -> {
            String nodeJson = n.getNodeJson();
            JSONObject jsonObject = JSON.parseObject(nodeJson);
            String className = jsonObject.getString("className");
            Long classId = jsonObject.getLong("classId");
            if (!BinaryUtils.isEmpty(classId) && org.apache.commons.lang3.StringUtils.isNotEmpty(className)) {
                Long aLong = convertMap.get(className);
                if (aLong != null && !classId.equals(aLong)) {
                    jsonObject.put("classId", aLong);
                    n.setNodeJson(JSON.toJSONString(jsonObject));
                }
            }
        });
        return nodeList;
    }

    public List<ESDiagramLink> getAllLink(ESDiagramLinkQueryBean linkQuery, List<ESDiagramLink> linkList) {
        long totalLinkCount = esDiagramLinkDao.countByCondition(ESUtil.cdtToBuilder(linkQuery));
        if (totalLinkCount > MAX_QUERY_COUNT) {
            throw new BinaryException("一次查询数据不能超过30000条");
        }
        if (totalLinkCount > PAGE_COUNT) {
            long loopCount = totalLinkCount / PAGE_COUNT + 1;
            for (int start = 1; start <= loopCount; ++start) {
                //分页查询
                Page<ESDiagramLink> tmpList = esDiagramLinkDao.getListByCdt(start, 3000, linkQuery);
                linkList.addAll(tmpList.getData());
            }
        } else {
            linkList = esDiagramLinkDao.getListByCdt(linkQuery);
        }
        return linkList;
    }

    //根据目录信息获取目录下的视图
    @Override
    public List<ESDiagram> selectByDirIds(Collection<Long> dirIds, List<Integer> dirTypes, String ownerCode, List<Integer> isOpens) {
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.must(QueryBuilders.termsQuery("dirId", dirIds));
        boolQuery.must(QueryBuilders.termQuery("status", 1));
        boolQuery.must(QueryBuilders.termQuery("historyVersionFlag", 1));
        boolQuery.must(QueryBuilders.termQuery("dataStatus", 1));
        if (!BinaryUtils.isEmpty(ownerCode)) {
            boolQuery.must(QueryBuilders.termQuery("ownerCode.keyword", ownerCode));
        }
        if (!BinaryUtils.isEmpty(dirTypes)) {
            boolQuery.must(QueryBuilders.termsQuery("dirType", dirTypes));
        }
        if (!BinaryUtils.isEmpty(isOpens)) {
            boolQuery.must(QueryBuilders.termsQuery("isOpen", isOpens));
        }
        return esDiagramDao.selectListByQuery(1, 1000, boolQuery);
    }

    @Override
    public Boolean clearReleaseDiagramIdBydEnergyId(Collection<String> dEnergyId, String ownerCode) {
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.must(QueryBuilders.termsQuery("releaseDiagramId.keyword", dEnergyId));
        boolQuery.must(QueryBuilders.termQuery("isOpen", 0));
        if (!BinaryUtils.isEmpty(ownerCode)) {
            boolQuery.must(QueryBuilders.termQuery("ownerCode.keyword", ownerCode));
        }
        String scriptStr = "ctx._source.releaseDiagramId=null;ctx._source.releaseVersion=0";
        return esDiagramDao.updateByQuery(boolQuery, scriptStr, true);
    }

    @Override
    public List<Long> fxCopyDiagramByIds(ESDiagramMoveCdt diagramMoveCdt) {
        List<String> referDiagramIdList = diagramMoveCdt.getDiagramIds();
        String newName = diagramMoveCdt.getNewName();
        Long newDirId = diagramMoveCdt.getNewDirId();
        if (BinaryUtils.isEmpty(referDiagramIdList)) {
            throw new BinaryException("目标视图id不能为空");
        }
        List<Long> diagramIdList;
        //根据视图id集合查询视图详细信息
        Long[] longs = queryDiagramInfoBydEnergy(referDiagramIdList.toArray(new String[0]));
        List<ESDiagramInfoDTO> esDiagramInfoList = queryESDiagramInfoByIds(longs);
        for (ESDiagramInfoDTO esDiagramInfoDTO : esDiagramInfoList) {
            if (esDiagramInfoDTO.getNonMonet() == null || esDiagramInfoDTO.getNonMonet() != 1) {
                esDiagramInfoDTO.setDiagramType(1);
            }
            //如果是由老视图复制，需要手动去除老视图转换标识
            esDiagramInfoDTO.setTransFlag(null);
            esDiagramInfoDTO.setReleaseDesc(null);
            esDiagramInfoDTO.setReleaseDiagramId(null);
            esDiagramInfoDTO.setReleaseVersion(0);
            esDiagramInfoDTO.setCreateTime(System.currentTimeMillis());
        }
        //批量复制视图，可以指定名称和路径
        diagramIdList = saveESDiagramBatch(esDiagramInfoList, newName, newDirId, "copy");
        if (!BinaryUtils.isEmpty(diagramIdList)) {
            createESDiagramVersionRecord(diagramIdList.toArray(new Long[0]));
        }
        return diagramIdList;
    }

    @Override
    public List<ESDiagram> selectByDirType(Integer dirTypes, String ownerCode, List<Integer> isOpens) {
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.must(QueryBuilders.termQuery("status", 1));
        boolQuery.must(QueryBuilders.termQuery("historyVersionFlag", 1));
        boolQuery.must(QueryBuilders.termQuery("dataStatus", 1));
        boolQuery.must(QueryBuilders.termQuery("dataStatus", 1));
        if (!BinaryUtils.isEmpty(ownerCode)) {
            boolQuery.must(QueryBuilders.termQuery("ownerCode.keyword", ownerCode));
        }
        boolQuery.must(QueryBuilders.termQuery("dirType", dirTypes));
        boolQuery.must(QueryBuilders.termsQuery("isOpen", isOpens));
        return esDiagramDao.selectListByQuery(1, 1000, boolQuery);
    }

    @Override
    public Page<ESDiagram> queryDiagramByParentDirCode(DiagramSearchParam param, SysUser sysUser, int pageNum, int pageSize) {
        String like = null;
        Integer dirType = null;
        Integer userFlag = null;
        Integer isOpen = null;
        if (!BinaryUtils.isEmpty(param)) {
            like = param.getWord();
            dirType = param.getDirType();
            userFlag = param.getUserFlag();
            isOpen = param.getIsOpen();
        }
        Map<String, ESDiagram> mapVo = new HashMap<>();
        //查询：别人已发布的   【isOpen = 1, 非当前登录人，当前版本 = 1】
        List<ESDiagram> otherDiagrams = new ArrayList<>();
        Boolean otherFlag1 = !BinaryUtils.isEmpty(userFlag) && userFlag == 3 || BinaryUtils.isEmpty(userFlag);
        Boolean otherFlag2 = !BinaryUtils.isEmpty(isOpen) && isOpen == 1 || BinaryUtils.isEmpty(isOpen);
        if (otherFlag1 && otherFlag2) {
            otherDiagrams = getOtherEsDiagrams(sysUser, like, dirType);
        }
        //查询与我协作视图
        List<ESDiagram> cooperateDiagrams = new ArrayList<>();
        if (!BinaryUtils.isEmpty(userFlag) && userFlag == 2 || BinaryUtils.isEmpty(userFlag)) {
            cooperateDiagrams = getShareEsDiagrams(sysUser, like, dirType,isOpen);
        }
        //查询我的全部数据
        List<ESDiagram> ownerDiagrams = new ArrayList<>();
        if (!BinaryUtils.isEmpty(userFlag) && userFlag == 1 || BinaryUtils.isEmpty(userFlag)) {
            ownerDiagrams = getOwnerEsDiagrams(sysUser, like, dirType, isOpen);
        }
        ArrayList<ESDiagram> esDiagramLists = Lists.newArrayList();
        esDiagramLists.addAll(otherDiagrams);
        esDiagramLists.addAll(cooperateDiagrams);
        esDiagramLists.addAll(ownerDiagrams);

        Page<ESDiagram> page = new Page();

        if (BinaryUtils.isEmpty(esDiagramLists)) {
            page.setPageNum(pageNum);      // 页数
            page.setPageSize(pageSize);     // 条数
            page.setTotalRows(0);        // 总行数
            page.setTotalPages(0);       // 总页数
            page.setData(new ArrayList<>());     // 分页数据
            return page;
        }
        for (ESDiagram esDiagram : esDiagramLists) {
            String icon1 = esDiagram.getIcon1();
            if (icon1 != null && !icon1.startsWith(httpResourceUrl)) {
                StringBuilder stringBuilder = new StringBuilder();
                stringBuilder.append(httpResourceUrl);
                stringBuilder.append(icon1);
//                icon1 = httpResourceUrl + icon1;
                esDiagram.setIcon1(stringBuilder.toString());
            }
            mapVo.putIfAbsent(esDiagram.getPrepareDiagramId(), esDiagram);
            // 需求调整 合并视图时展示私有库数据
            if (esDiagram.getIsOpen() != 1) {
                mapVo.put(esDiagram.getPrepareDiagramId(), esDiagram);
            }
        }
        List<ESDiagram> showDiagramInfos = mapVo.values().stream().collect(Collectors.toList());

        // 手动分页处理查询结果
        List<ESDiagram> subInfos = new ArrayList<>();
        int totalRows = showDiagramInfos.size();
        int pageStart  = pageNum == 1 ? 0 : (pageNum - 1) * pageSize;       // 截取的开始位置
        int pageEnd = totalRows < pageNum * pageSize ? totalRows : pageNum * pageSize;        // 截取的结束位置
        if(totalRows > pageStart){
            subInfos =showDiagramInfos.subList(pageStart, pageEnd);
        }
        int totalPage = showDiagramInfos.size() % pageSize == 0 ? showDiagramInfos.size() / pageSize : showDiagramInfos.size() / pageSize + 1;     // 总页数

        page.setPageNum(pageNum);      // 页数
        page.setPageSize(pageSize);     // 条数
        page.setTotalRows(totalRows);        // 总行数
        page.setTotalPages(totalPage);       // 总页数
        page.setData(subInfos);     // 分页数据

        return page;
    }

    //查询我得全部数据
    private List<ESDiagram> getOwnerEsDiagrams(SysUser sysUser, String like, Integer dirType, Integer isOpen) {
        BoolQueryBuilder query2 = QueryBuilders.boolQuery();
        query2.must(QueryBuilders.termQuery("ownerCode.keyword", sysUser.getLoginCode()))
                .must(QueryBuilders.termQuery("dataStatus", 1))
                .must(QueryBuilders.termQuery("status", 1))
                .must(QueryBuilders.termQuery("historyVersionFlag", 1))
                .must(QueryBuilders.termsQuery("diagramType", Arrays.asList(1,2)))
                .must(QueryBuilders.termQuery("domainId", sysUser.getDomainId()));
        if (!StringUtils.isEmpty(like)) {
            query2.must(QueryBuilders.wildcardQuery("name.keyword", "*" + like.trim() + "*"));
        }
        if (!StringUtils.isEmpty(isOpen)) {
            query2.must(QueryBuilders.termQuery("isOpen", isOpen));
        }
        if (!StringUtils.isEmpty(dirType)) {
            query2.must(QueryBuilders.termQuery("dirType", dirType));
        }
        return esDiagramDao.getListByQueryScroll(query2);
    }

    //查询别人已发布的
    private List<ESDiagram> getOtherEsDiagrams(SysUser sysUser, String like, Integer dirType) {
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.must(QueryBuilders.termQuery("isOpen", 1))
                .must(QueryBuilders.termQuery("dataStatus", 1))
                .must(QueryBuilders.termQuery("status", 1))
                .mustNot(QueryBuilders.termQuery("ownerCode.keyword", sysUser.getLoginCode()))
                .must(QueryBuilders.termQuery("historyVersionFlag", 1))
                .must(QueryBuilders.termsQuery("diagramType", Arrays.asList(1,2)))
                .must(QueryBuilders.termQuery("domainId", sysUser.getDomainId()));
        if (!StringUtils.isEmpty(like)) {
            queryBuilder.must(QueryBuilders.wildcardQuery("name.keyword", "*" + like.trim() + "*"));
        }
        if (!StringUtils.isEmpty(dirType)) {
            queryBuilder.must(QueryBuilders.termQuery("dirType", dirType));
        }
        return esDiagramDao.getListByQueryScroll(queryBuilder);
    }

    //查询与我协作的视图
    private List<ESDiagram> getShareEsDiagrams(SysUser sysUser, String like, Integer dirType, Integer isOpen) {
        //查询与我协作：包括已发布和未发布
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("sharedUserId", sysUser.getId()));
        List<DiagramShareRecord> listByQuery1 = esShareDiagramDao.getListByQueryScroll(query);
        List<Long> diagramIds = null;
        if (!BinaryUtils.isEmpty(listByQuery1)) {
            diagramIds = listByQuery1.stream().map(DiagramShareRecord::getDiagramId)
                    .collect(Collectors.toList());
        }
        BoolQueryBuilder query1 = QueryBuilders.boolQuery();
        //如果与我协作没有视图的话，就不走查询逻辑
        List<ESDiagram> listByQuery2 = new ArrayList<>();
        if (!BinaryUtils.isEmpty(diagramIds)) {
            query1.must(QueryBuilders.termsQuery("id", diagramIds))
                    .must(QueryBuilders.termQuery("dataStatus", 1))
                    .must(QueryBuilders.termQuery("status", 1))
                    .must(QueryBuilders.termQuery("historyVersionFlag", 1))
                    .must(QueryBuilders.termsQuery("diagramType", Arrays.asList(1,2)))
                    .must(QueryBuilders.termQuery("domainId", sysUser.getDomainId()));
            if (!BinaryUtils.isEmpty(isOpen)) {
                query1.must(QueryBuilders.termQuery("isOpen", isOpen));
            }
            if (!BinaryUtils.isEmpty(dirType)) {
                query1.must(QueryBuilders.termQuery("dirType", dirType));
            }
            if (!StringUtils.isEmpty(like)) {
                query1.must(QueryBuilders.wildcardQuery("name.keyword", "*" + like.trim() + "*"));
            }
            listByQuery2 = esDiagramDao.getListByQueryScroll(query1);
        }
        return listByQuery2;
    }

    @Override
    public int saveLinkList(List<ESDiagramLink> linkList) {
        return diagramLinkSvc.saveOrUpdateBatch(linkList);
    }

    @Override
    public int saveNodeList(List<ESDiagramNode> nodeList) {
        return diagramNodeSvc.saveOrUpdateBatch(nodeList);
    }

    @Override
    public int delLinkList(List<Long> ids, List<String> keys) {
        if (!BinaryUtils.isEmpty(ids)) {
            esDiagramLinkDao.deleteByIds(ids);
        }
        if (!BinaryUtils.isEmpty(keys)) {
            esDiagramLinkDao.deleteByQuery(QueryBuilders.termQuery("key.keyword", keys), true);
        }
        return 1;
    }

    @Override
    public int delNodeList(List<Long> ids, List<String> keys) {
        if (!BinaryUtils.isEmpty(ids)) {
            esDiagramNodeDao.deleteByIds(ids);
        }
        if (!BinaryUtils.isEmpty(keys)) {
            esDiagramNodeDao.deleteByQuery(QueryBuilders.termQuery("key.keyword", keys), true);
        }
        return 1;
    }

    @Override
    public ESDiagram querySimpleDiagramInfoById(Long diagramId) {
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("id", diagramId));
        query.must(QueryBuilders.termQuery("status", 1));
        query.must(QueryBuilders.termQuery("domainId", SysUtil.getCurrentUserInfo().getDomainId()));
        return esDiagramDao.selectOne(query);
    }

    @Override
    public Boolean updateFlowStatusByIds(List<Long> unPublishIds, Integer flowStatus) {
        Map<String, Object> params = new HashMap<>();
        params.put("flowStatus", flowStatus);
        String script = "ctx._source.flowStatus=params.flowStatus;";
        return esDiagramDao.updateByQuery(QueryBuilders.termsQuery("id", unPublishIds), script, true, params);
    }

    @Override
    public List<ESDiagram> queryDBDiagramInfoByIds(String[] toArray) {
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termsQuery("dEnergy.keyword", toArray));
        query.must(QueryBuilders.termQuery("status", 1));
        List<ESDiagram> listByQuery = esDiagramDao.getListByQuery(query);
        listByQuery.forEach(e -> {
            e.setIcon1(httpResourceUrl + e.getIcon1());
        });
        return listByQuery;
    }

    @Override
    public Long[] queryDBDiagramInfoBydEnergy(String[] encryptDiagramIds) {
        EamDiagramQuery diagramQuery = new EamDiagramQuery();
        diagramQuery.setDEnergys(encryptDiagramIds);
//        diagramQuery.setStatus(1);
//        diagramQuery.setDataStatus(1);
//        diagramQuery.setHistoryVersionFlag(1);
        Page<ESDiagram> diagramPage = esDiagramDao.getSortListByCdt(0, encryptDiagramIds.length, diagramQuery, "modifyTime", false);
        List<ESDiagram> diagramList = diagramPage.getData();
        if (CollectionUtils.isEmpty(diagramList)) {
            throw new BinaryException("当前视图已被删除,无法操作!");
        }
//        if (encryptDiagramIds.length != diagramList.size()) {
//            logger.info("传入的加密id数量：{},包含元素：{}, 查出的视图数量：{}, 包含元素：{}", encryptDiagramIds.length, encryptDiagramIds, diagramList.size(), diagramList.stream().map(ESDiagram::getId).toArray(Long[]::new));
//            throw new BinaryException("批量查询的视图，部分已删除或没有权限");
//        }
        return diagramList.stream().map(ESDiagram::getId).distinct().toArray(Long[]::new);
    }

    @Override
    public Map<String, Set<DiagramRelationInfo>> getRelateInfoByDiagramIds(String diagramId, Integer browseStatus) {
        Map<String, List<DiagramRelationInfo>> data = new HashMap<>();
        if (BinaryUtils.isEmpty(diagramId)) {
            throw new BinaryException("视图ID不能为空!");
        }
        Long id = this.queryDiagramInfoByEnergy(diagramId);
        ESDiagramDTO esDiagramDTO = this.queryDiagramInfoById(id, "", false);       // 去除权限校验

        // 获取当前视图下节点的关联视图信息 modelList[] -> nodeDataArray[] -> nodeJson -> relationInfo -> data[]
        List<ESDiagramModel> modelList = esDiagramDTO.getDiagram().getModelList();
        List<String> relationDiagramIds = new ArrayList<>();
        for (ESDiagramModel diagramModel : modelList) {
            if (CollectionUtils.isEmpty(diagramModel.getNodeDataArray())) {
                continue;
            }
            for (ESDiagramNode esDiagramNode : diagramModel.getNodeDataArray()) {
                String nodeJson = esDiagramNode.getNodeJson();
                // 解析出视图中关联的视图数据 relationInfo下的data[],若未关联视图则没有relationInfo字段
                JSONObject nodeJsonObject = JSON.parseObject(nodeJson);
                if (BinaryUtils.isEmpty(nodeJsonObject.get("relationInfo"))) {     // 当前视图上的节点关联了视图 解析出data[]
                    continue;
                }
                JSONObject relationInfoJson = nodeJsonObject.getJSONObject("relationInfo");
                if (BinaryUtils.isEmpty(relationInfoJson.get("data"))) {
                    continue;
                }
                JSONArray dataJsonList = relationInfoJson.getJSONArray("data");
                List<DiagramRelationInfo> relationInfos = new ArrayList<>();        // 关联视图数组
                for (int i = 0; i < dataJsonList.size(); i++) {
                    JSONObject dataJson = dataJsonList.getJSONObject(i);
                    DiagramRelationInfo diagramRelationInfo = JSON.parseObject(dataJson.toString(), DiagramRelationInfo.class);
                    diagramRelationInfo.setShowType(SKIP_DELETE_OPTYPE);        // 默认附上删除 下面查询视图的就仅查询正常的视图
                    relationInfos.add(diagramRelationInfo);
                    relationDiagramIds.add(diagramRelationInfo.getValue());
                }
                data.put(esDiagramNode.getKey(), relationInfos);
            }
        }
        // 视图不存在关联数据
        if (data.isEmpty()) {
            return Collections.emptyMap();
        }
        Map<String, Set<DiagramRelationInfo>> result = new HashMap<>();      // 返回值 图上节点中的id 对应节点关联的视图id和跳转方式
        // 查询所有关联数据信息
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termsQuery("dEnergy.keyword", relationDiagramIds));
        boolQueryBuilder.must(QueryBuilders.termQuery("dataStatus", 1));
        boolQueryBuilder.must(QueryBuilders.termQuery("status", 1));
        List<ESDiagram> relationDiagramList = esDiagramDao.getListByQuery(boolQueryBuilder);

        Map<Integer, List<ESDiagram>> relationDiagramGroupByOpen = relationDiagramList.stream().collect(Collectors.groupingBy(ESDiagram::getIsOpen));       // 基于isOpen将关联数据分组


        // 视图关联设计库数据时跳转规则相同 showType = SKIP_DES_LOOKUP_OPTYPE 这里统一处理一下
        if (!CollectionUtils.isEmpty(relationDiagramGroupByOpen.get(1))) {
            Set<String> pubDiagramIdList = relationDiagramGroupByOpen.get(1).stream().map(ESDiagram::getDEnergy).collect(Collectors.toSet());
            Map<String, ESDiagram> pubDiagramIdMap = relationDiagramGroupByOpen.get(1).stream().collect(Collectors.toMap(ESDiagram::getDEnergy, each -> each, (k1, k2) -> k1));
            for (Map.Entry<String, List<DiagramRelationInfo>> entry : data.entrySet()) {
                Set<DiagramRelationInfo> relationInfos = new HashSet<>();
                for (DiagramRelationInfo diagramRelationInfo : entry.getValue()) {
                    if (pubDiagramIdList.contains(diagramRelationInfo.getValue())) {
                        // 节点中发布的关联数据重置showType
                        diagramRelationInfo.setShowType(SKIP_DES_LOOKUP_OPTYPE);
                        diagramRelationInfo.setIcon(new StringBuilder(httpResourceUrl).append(pubDiagramIdMap.get(diagramRelationInfo.getValue()).getIcon1()).toString());
                        relationInfos.add(diagramRelationInfo);
                    }
                }
                if (!CollectionUtils.isEmpty(relationInfos)) {
                    result.put(entry.getKey(), relationInfos);
                }
            }
        }

        if (!CollectionUtils.isEmpty(relationDiagramGroupByOpen.get(0))) {
            List<ESDiagram> priDiagramList = relationDiagramGroupByOpen.get(0);

            // 根据但前视图的发布状态进行关联视图的跳转判断 加上预览模式
            if (esDiagramDTO.getDiagram().getIsOpen() == 1 || !BinaryUtils.isEmpty(browseStatus)) {
                /*
                 *  当前视图为发布状态，关联视图跳转的规则如下：
                 *       一.关联的视图为私有库数据
                 *           1.若关联视图始终为未发布状态，不进行跳转
                 *           2.若关联视图发布，显示设计库发布的数据。（此处关联的数据为私有库，需要处理查询对应的设计库数据）
                 *
                 *
                 *       二.关联的视图为设计库数据
                 *           直接显示关联视图设计库数据，跳转进入发布查看模式
                 * */

                // 将关联的私有库数据根据releaseDiagramId进行区分
                Set<String> priPubIdList = new HashSet<>();      // 私有库数据发布过
                List<String> priPubPIdList = new ArrayList<>();      // 私有库数据发布过的pId
                List<String> priPriIdList = new ArrayList<>();      // 私有库数据未发布过
                Set<String> priRelIdList = new HashSet<>();

                for (ESDiagram esDiagram : priDiagramList) {
                    if (!BinaryUtils.isEmpty(esDiagram.getReleaseDiagramId())) {
                        priPubIdList.add(esDiagram.getDEnergy());
                        priPubPIdList.add(esDiagram.getPrepareDiagramId());
                        priRelIdList.add(esDiagram.getReleaseDiagramId());
                    } else {
                        priPriIdList.add(esDiagram.getDEnergy());
                    }
                }

                // 根据私有库PID/ipOpen/historyVersionFlag查询 priPubIdList中对应的设计库数据
                Map<String, ESDiagram> priPubDataMap = new HashMap<>();
                if (!CollectionUtils.isEmpty(priPubPIdList)) {
                    BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
                    boolQuery.must(QueryBuilders.termsQuery("prepareDiagramId.keyword", priPubPIdList));
                    boolQuery.must(QueryBuilders.termQuery("historyVersionFlag", 1));
                    boolQuery.must(QueryBuilders.termQuery("isOpen", 1));
                    List<ESDiagram> priPubDataList = esDiagramDao.getListByQuery(boolQuery);
                    priPubDataMap = priPubDataList.stream().filter(e -> priRelIdList.contains(e.getDEnergy()))
                            .collect(Collectors.toMap(ESDiagram::getPrepareDiagramId, value -> value, (key1, key2) -> key2));
                }

                // todo 这里的发布视图可能删除 priPubDataMap 后期需要判空吗。。。。
                for (Map.Entry<String, List<DiagramRelationInfo>> entry : data.entrySet()) {
//                    Map<Long, List<DiagramRelationInfo>> addData = new HashMap<>();
                    Set<DiagramRelationInfo> relationInfos = new HashSet<>();
                    String nodeId = entry.getKey();
                    for (DiagramRelationInfo diagramRelationInfo : entry.getValue()) {
//                            List<DiagramRelationInfo> delInfos = new ArrayList<>();     // 需要删除的关联数据
//                            List<DiagramRelationInfo> addInfos = new ArrayList<>();     // 需要添加的关联数据
//                        if (priPriIdList.contains(diagramRelationInfo.getValue())) {
//                            // 节点中未发布的关联数据本地始终未发布 重置showType = SKIP_FORBID_OPTYPE
//                            diagramRelationInfo.setShowType(SKIP_FORBID_OPTYPE);
//                            relationInfos.add(diagramRelationInfo);
//                        }
                        if (priPubIdList.contains(diagramRelationInfo.getValue())
                                && priPubDataMap.containsKey((diagramRelationInfo.getPrepareDiagramId()))) {
                            // 节点中未发布的关联数据本地已经发布 重置showType = SKIP_DES_LOOKUP_OPTYPE 并且将原关联数据删除 替换为新发布视图的数据
//                            delInfos.add(diagramRelationInfo);      // 该条数据要删除
//                            DiagramRelationInfo diaRel = new DiagramRelationInfo();
//                            diaRel.setShowType(SKIP_DES_LOOKUP_OPTYPE);
//                            diaRel.setPrepareDiagramId(diagramRelationInfo.getPrepareDiagramId());
//                            diaRel.setName(diagramRelationInfo.getName());
//                            diaRel.setPage(diagramRelationInfo.getPage());
//                            diaRel.setValue(priPubDataMap.get(diagramRelationInfo.getPrepareDiagramId()).getDEnergy());     // 设置新的关联信息
                            diagramRelationInfo.setShowType(SKIP_DES_LOOKUP_OPTYPE);
                            // 返回给前端当前数据对应的设计库数据
                            ESDiagram pubDiagram = priPubDataMap.get(diagramRelationInfo.getPrepareDiagramId());
                            diagramRelationInfo.setValue(pubDiagram.getDEnergy());
                            diagramRelationInfo.setPrepareDiagramId(pubDiagram.getPrepareDiagramId());
                            diagramRelationInfo.setName(pubDiagram.getName());
                            StringBuilder stringBuilder = new StringBuilder();
                            stringBuilder.append(httpResourceUrl);
                            stringBuilder.append(pubDiagram.getIcon1());
                            diagramRelationInfo.setIcon(stringBuilder.toString());
                            relationInfos.add(diagramRelationInfo);
                        }
                    }

                    if (!CollectionUtils.isEmpty(relationInfos)) {
                        if (!CollectionUtils.isEmpty(result.get(nodeId))) {
                            result.get(nodeId).addAll(relationInfos);
                        } else {
                            result.put(nodeId, relationInfos);
                        }
                    }
                }


            } else {
                /*
                 *   当前视图为未发布状态，关联视图跳转的规则如下：
                 *       一.关联的视图为私有库数据
                 *          1.数据为用户自己或别人分享（编辑权限分享），跳转进入编辑权限
                 *          2.数据为分享（查看权限分享），跳转进入特殊查看模式
                 *
                 *
                 *       二.关联视图为设计库数据
                 *          直接显示关联视图设计库数据，跳转进入发布查看模式
                 * */

                // 根据ownerCode进行区分
                SysUser currentUserInfo = SysUtil.getCurrentUserInfo();// 获取当前登录用户

                Set<String> curUserDataList = new HashSet<>();       // 私有库当前用户的数据code
                Map<String, ESDiagram> curUserDataMap = new HashMap<>();
                List<Long> otherUserDataList = new ArrayList<>();       // 私有库其他用户的数据id
                Map<String, ESDiagram> otherUserDataMap = new HashMap<>();
                Map<String, Long> eIdAndIdMap = new HashMap<>();
                Map<Long, String> idAndEIdMap = new HashMap<>();            // 冷静。。。

                for (ESDiagram esDiagram : priDiagramList) {
                    if (currentUserInfo.getLoginCode().equals(esDiagram.getOwnerCode())) {
                        curUserDataList.add(esDiagram.getDEnergy());
                        curUserDataMap.put(esDiagram.getDEnergy(), esDiagram);
                    } else {
                        otherUserDataList.add(esDiagram.getId());
                        otherUserDataMap.put(esDiagram.getDEnergy(), esDiagram);
                        eIdAndIdMap.put(esDiagram.getDEnergy(), esDiagram.getId());
                        idAndEIdMap.put(esDiagram.getId(), esDiagram.getDEnergy());
                    }
                }

                // 当前用户的关联数据直接附上编辑权限 重置showType = SKIP_PRI_STUDIO_OPTYPE
                if (!CollectionUtils.isEmpty(curUserDataList)) {
                    for (Map.Entry<String, List<DiagramRelationInfo>> entry : data.entrySet()) {
//                        Map<Long, List<DiagramRelationInfo>> addData = new HashMap<>();
                        Set<DiagramRelationInfo> relationInfos = new HashSet<>();

                        for (DiagramRelationInfo diagramRelationInfo : entry.getValue()) {
                            if (curUserDataList.contains(diagramRelationInfo.getValue())) {
                                diagramRelationInfo.setShowType(SKIP_PRI_STUDIO_OPTYPE);
                                diagramRelationInfo.setIcon(new StringBuilder(httpResourceUrl).append(curUserDataMap.get(diagramRelationInfo.getValue()).getIcon1()).toString());
                                diagramRelationInfo.setName(curUserDataMap.get(diagramRelationInfo.getValue()).getName());
                                relationInfos.add(diagramRelationInfo);
                            }
                        }
                        if (!CollectionUtils.isEmpty(relationInfos)) {
                            if (!CollectionUtils.isEmpty(result.get(entry.getKey()))) {
                                result.get(entry.getKey()).addAll(relationInfos);
                            } else {
                                result.put(entry.getKey(), relationInfos);
                            }
                        }
                    }
                }

                /*
                 *  非当前用户数据 可能存在三种情况：
                 *      1.分享给当前用户编辑权限 -》 跳转到编辑页面 重置showType = SKIP_PRI_STUDIO_OPTYPE
                 *      2.分享给当前用户查看权限 -》 跳转到查看页面 重置showType = SKIP_PRI_LOOKUP_OPTYPE
                 *      3.当前用户没有查询视图的权限 -》 不进行跳转，禁止查看 重置showType = SKIP_FORBID_OPTYPE
                * */

                if (!CollectionUtils.isEmpty(otherUserDataList)) {
                    // 根据视图ID查询分享权限
                    BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
                    boolQuery.must(QueryBuilders.termsQuery("diagramId", otherUserDataList));
                    boolQuery.must(QueryBuilders.termQuery("sharedUserId", currentUserInfo.getId()));
                    List<DiagramShareRecord> shareDataList = esShareDiagramDao.getListByQuery(boolQuery);

                    Map<Long, DiagramShareRecord> idAndShareDataMap = shareDataList.stream()
                            .collect(Collectors.toMap(DiagramShareRecord::getDiagramId, value -> value, (key1, key2) -> key2));

                    Set<String> shareEId = new HashSet<>();
                    for (Long shareId : idAndShareDataMap.keySet()) {      // 遍历出分享数据的EId (shareDiagram表应该把eid存起来 。。。。。)
                        shareEId.add(idAndEIdMap.get(shareId));
                    }
                    for (Map.Entry<String, List<DiagramRelationInfo>> entry : data.entrySet()) {
                        Set<DiagramRelationInfo> relationInfos = new HashSet<>();
                        String nodeId = entry.getKey();
                        for (DiagramRelationInfo diagramRelationInfo : entry.getValue()) {
                            // 此处取查询出的分享数据的eid进行比对
                            if (shareEId.contains(diagramRelationInfo.getValue())) {
                                // 节点中未发布的关联数据，不属于当前用户本身数据 判断 permission: 1=编辑和分享 2=编辑 3=查看
                                if (idAndShareDataMap.get(eIdAndIdMap.get(diagramRelationInfo.getValue())).getPermission() == 3) {
                                    // 重置showType = SKIP_PRI_STUDIO_OPTYPE
                                    diagramRelationInfo.setShowType(SKIP_PRI_LOOKUP_OPTYPE);
                                } else {
                                    diagramRelationInfo.setShowType(SKIP_PRI_STUDIO_OPTYPE);
                                }
                                diagramRelationInfo.setIcon(new StringBuilder(httpResourceUrl).append(otherUserDataMap.get(diagramRelationInfo.getValue()).getIcon1()).toString());
                                relationInfos.add(diagramRelationInfo);
                            }
//                            else {
//                                // 节点中未发布的关联数据，不属于当前用户本身数据 也没有分享权限
//                                diagramRelationInfo.setShowType(SKIP_FORBID_OPTYPE);
//                            }
                        }
                        if (!CollectionUtils.isEmpty(relationInfos)) {
                            if (!CollectionUtils.isEmpty(result.get(nodeId))) {
                                result.get(nodeId).addAll(relationInfos);
                            } else {
                                result.put(nodeId, relationInfos);
                            }
                        }
                    }
                }
            }
        }
        
        return result;
    }

    @Override
    public Page<ESDiagram> selectListByQuery(Integer pageNum, Integer pageSize, QueryBuilder query) {
        if(BinaryUtils.isEmpty(pageNum)){
            pageNum = 1;
        }
        if(BinaryUtils.isEmpty(pageSize)){
            pageSize = 25;
        }
        Page<ESDiagram> result = esDiagramDao.getListByQuery(pageNum, pageSize, query);
        if(BinaryUtils.isEmpty(result.getData())){
            result.setData(Collections.emptyList());
        }
        for (ESDiagram diagram : result.getData()) {
            String icon1 = diagram.getIcon1();
            if (icon1 != null && !icon1.startsWith(httpResourceUrl)) {
                StringBuilder stringBuilder = new StringBuilder();
                stringBuilder.append(httpResourceUrl);
                stringBuilder.append(icon1);
//                icon1 = httpResourceUrl + icon1;
                diagram.setIcon1(stringBuilder.toString());
            }
            String diagramBgImg = diagram.getDiagramBgImg();
            if (diagramBgImg != null && !BinaryUtils.isEmpty(diagramBgImg.trim())
                    && !diagramBgImg.startsWith(httpResourceUrl)) {
                diagram.setDiagramBgImg(new StringBuilder(httpResourceUrl).append(diagramBgImg).toString());
            }
        }
        return result;
    }

    @Override
    public List<ESDiagramDTO> queryDiagramInfosById(Long[] diagramIds, String type, Boolean needAuth, Boolean asset) {
        //架构资产打开视图时做特殊处理
        List<Long> diagramIdList = new ArrayList<>(Arrays.asList(diagramIds));
        List<ESDiagramDTO> assetDiagramList = new ArrayList<>();
        Iterator<Long> iterator = diagramIdList.iterator();
        while (iterator.hasNext()) {
            Long diagramId = iterator.next();
            ESDiagram esDiagram = esDiagramDao.getById(diagramId);
            if (esDiagram != null && esDiagram.getNonMonet() != null && esDiagram.getNonMonet() == 1) {
                ESDiagramDTO esDiagramDTO = new ESDiagramDTO();
                ESDiagramInfoDTO diagram = new ESDiagramInfoDTO();
                BeanUtils.copyProperties(esDiagram, diagram);
                esDiagramDTO.setDiagram(diagram);

                Long userId = diagram.getUserId();
                UserInfo userInfo = userApiSvc.getUserInfoById(userId);
                esDiagramDTO.setCreator(userInfo);

                Map<Long, List<ESDiagramShareRecordResult>> diagramShareRecordMap = esShareDiagramSvc.queryDiagramShareRecords(new Long[]{diagramId}, needAuth);
                esDiagramDTO.setShareRecords(diagramShareRecordMap.get(diagramId));
                assetDiagramList.add(esDiagramDTO);

                iterator.remove();
            }
        }
        if (!CollectionUtils.isEmpty(diagramIdList)) {
            diagramIds = new Long[diagramIdList.size()];
            for (int i = 0; i < diagramIdList.size(); i++) {
                diagramIds[i] = diagramIdList.get(i);
            }
        } else {
            return assetDiagramList;
        }

        //1.根据视图id集合查询视图数据
        diagramIds = ArrayUtil.distinct(diagramIds);
        List<ESDiagram> diagramList = judgeDiagramAuthNew(diagramIds, type, needAuth);
        if (diagramList.isEmpty() ||  diagramList.size() != diagramIds.length) {
            return Lists.newArrayList();
        }
        Map<Long, ESDiagram> diagramIdMap = new HashMap<>();
        Set<Long> creatorIdSet = new HashSet<>();
        Set<Long> diagramIdSet = new HashSet<>();
        for (ESDiagram esDiagram : diagramList) {
            diagramIdMap.put(esDiagram.getId(), esDiagram);
            diagramIdSet.add(esDiagram.getId());
            creatorIdSet.add(esDiagram.getUserId());
            String icon1 = esDiagram.getIcon1();
            if (icon1 != null && !icon1.startsWith(httpResourceUrl)) {
                StringBuilder stringBuilder = new StringBuilder();
                stringBuilder.append(httpResourceUrl);
                stringBuilder.append(icon1);
//                icon1 = httpResourceUrl + icon1;
                esDiagram.setIcon1(stringBuilder.toString());
            }
        }
        //2.根据视图id集合查询其包含的所有sheet数据
        ESDiagramSheetQuery sheetQuery = new ESDiagramSheetQuery();
        sheetQuery.setDiagramIds(diagramIds);
        List<ESDiagramSheetDTO> sheetList = esDiagramSheetDao.getListByCdt(sheetQuery);
        String[] sheetIdArr = sheetList.stream().map(ESDiagramSheetDTO::getSheetId).distinct().toArray(String[]::new);
        if (BinaryUtils.isEmpty(sheetIdArr)) {
            throw new BinaryException("错误sheet，没有sheetId");
        }
        //将sheet按照视图id进行分组
        Map<Long, List<ESDiagramSheetDTO>> diagramSheetMap = sheetList
                .stream()
                .collect(Collectors.groupingBy(ESDiagramSheetDTO::getDiagramId));
        //3.查询所有的node节点，将其按照diagramId和sheetId进行分组
        List<ESDiagramNode> nodeList = new ArrayList<>();
        ESDiagramNodeQueryBean nodeQuery = new ESDiagramNodeQueryBean();
        nodeQuery.setDiagramIds(diagramIdSet.toArray(new Long[]{}));
        nodeQuery.setSheetIds(sheetIdArr);
        nodeList = getAllNode(nodeQuery, nodeList);
        Map<Long, Map<String, List<ESDiagramNode>>> diagramSheetNodeMap = new HashMap<>();
        if (!BinaryUtils.isEmpty(nodeList)) {
            Map<Long, List<ESDiagramNode>> diagramNodeMap = nodeList
                    .stream()
                    .collect(Collectors.groupingBy(ESDiagramNode::getDiagramId));
            for (Map.Entry<Long, List<ESDiagramNode>> entry : diagramNodeMap.entrySet()) {
                Map<String, List<ESDiagramNode>> sheetNodeMap = new HashMap<>();
                if (!BinaryUtils.isEmpty(entry.getValue())) {
                    sheetNodeMap = entry.getValue().stream().collect(Collectors.groupingBy(ESDiagramNode::getSheetId));
                }
                diagramSheetNodeMap.put(entry.getKey(), sheetNodeMap);
            }
        }
        //4.查询所有的link节点，将其按照diagramId和sheetId进行分组
        List<ESDiagramLink> linkList = new ArrayList<>();
        ESDiagramLinkQueryBean linkQuery = new ESDiagramLinkQueryBean();
        linkQuery.setDiagramIds(diagramIdSet.toArray(new Long[]{}));
        linkQuery.setSheetIds(sheetIdArr);
        linkList = getAllLink(linkQuery, linkList);
        Map<Long, Map<String, List<ESDiagramLink>>> diagramSheetLinkMap = new HashMap<>();
        if (!BinaryUtils.isEmpty(linkList)) {
            Map<Long, List<ESDiagramLink>> diagramLinkMap = linkList
                    .stream()
                    .collect(Collectors.groupingBy(ESDiagramLink::getDiagramId));
            for (Map.Entry<Long, List<ESDiagramLink>> entry : diagramLinkMap.entrySet()) {
                List<ESDiagramLink> esDiagramLinkList = entry.getValue();
                Map<String, List<ESDiagramLink>> sheetLinkMap = new HashMap<>();
                if (!BinaryUtils.isEmpty(esDiagramLinkList)) {
                    sheetLinkMap = esDiagramLinkList
                            .stream()
                            .collect(Collectors.groupingBy(ESDiagramLink::getSheetId));
                }
                diagramSheetLinkMap.put(entry.getKey(), sheetLinkMap);
            }
        }

        //5.查询视图分享记录，并按照diagramId进行对结果进行分组
        Map<Long, List<ESDiagramShareRecordResult>> diagramShareRecordMap = new HashMap<>();
        if (BinaryUtils.isEmpty(type) || (!"copy".equals(type) && !"FX_D_HISTORY".equals(type) && !"CJ_D_HISTORY".equals(type))) {
            diagramShareRecordMap = esShareDiagramSvc.queryDiagramShareRecords(diagramIds, needAuth);
        }
        Map<Long, SysUser> diagramCreatorMap = new HashMap<>();
        if (BinaryUtils.isEmpty(type) || !"copy".equals(type)) {
            //6.查询视图作者信息，并按照userId对结果进行分组
            CSysUser cSysUser = new CSysUser();
            cSysUser.setDomainId(SYS_DOMAIN_ID);
            cSysUser.setIds(creatorIdSet.toArray(new Long[]{}));
            cSysUser.setSuperUserFlags(new Integer[]{0, 1});
            List<SysUser> creatorList = userApiSvc.getSysUserByCdt(cSysUser);
            creatorList.forEach(
                    creator -> {
                        creator.setLoginPasswd(null);
                        diagramCreatorMap.put(creator.getId(), creator);
                    }
            );
        }
        //7.封装返回结果
        List<ESDiagramDTO> esDiagramDTOList = new ArrayList<>();
        for (Long diagramId : diagramIdSet) {
            ESDiagramDTO esDiagramDTO = new ESDiagramDTO();
            ESDiagramInfoDTO esDiagramInfoDTO = new ESDiagramInfoDTO();
            //封装视图基础信息
            ESDiagram esDiagram = diagramIdMap.get(diagramId);
            BeanUtils.copyProperties(esDiagram, esDiagramInfoDTO);
            //封装sheetList
            List<ESDiagramSheetDTO> sheetDTOList = diagramSheetMap.get(diagramId);
            List<ESDiagramSheetDTO> sortedSheetDTOList = sheetDTOList.stream().sorted(Comparator.comparing(ESDiagramSheetDTO::getSheetOrder)).collect(Collectors.toList());
            esDiagramInfoDTO.setSheetList(sortedSheetDTOList);
            //封装modelList
            List<ESDiagramModel> modelList = new ArrayList<>();
            for (ESDiagramSheetDTO sheetDTO : sheetDTOList) {
                ESDiagramModel esDiagramModel = new ESDiagramModel();
                String sheetId = sheetDTO.getSheetId();
                if (!BinaryUtils.isEmpty(diagramSheetNodeMap)) {
                    List<ESDiagramNode> nodeDTOList = new ArrayList<>();
                    Map<String, List<ESDiagramNode>> diagramNodeMap = diagramSheetNodeMap.get(diagramId);
                    if (!BinaryUtils.isEmpty(diagramNodeMap)) {
                        nodeDTOList = diagramNodeMap.get(sheetId);
                    }
                    if (!BinaryUtils.isEmpty(nodeDTOList)) {
                        esDiagramModel.setNodeDataArray(nodeDTOList);
                    }
                }
                if (!BinaryUtils.isEmpty(diagramSheetLinkMap)) {
                    List<ESDiagramLink> linkDTOList = new ArrayList<>();
                    Map<String, List<ESDiagramLink>> diagramLinkMap = diagramSheetLinkMap.get(diagramId);
                    if (!BinaryUtils.isEmpty(diagramLinkMap)) {
                        linkDTOList = diagramLinkMap.get(sheetId);
                    }
                    if (!BinaryUtils.isEmpty(linkDTOList)) {
                        esDiagramModel.setLinkDataArray(linkDTOList);
                    }
                }
                esDiagramModel.setDiagramId(diagramId);
                esDiagramModel.setModelData(sheetDTO.getModelData());
                esDiagramModel.setDiagramClass(sheetDTO.getDiagramClass());
                esDiagramModel.setLinkToPortIdProperty(sheetDTO.getLinkToPortIdProperty());
                esDiagramModel.setLinkFromPortIdProperty(sheetDTO.getLinkFromPortIdProperty());
                esDiagramModel.setSheetId(sheetDTO.getSheetId());
                modelList.add(esDiagramModel);
            }
            esDiagramInfoDTO.setModelList(modelList);
            esDiagramDTO.setDiagram(esDiagramInfoDTO);
            //封装视图作者信息
            esDiagramDTO.setCreator(diagramCreatorMap.get(esDiagramInfoDTO.getUserId()));
            //封装视图分享记录信息
            esDiagramDTO.setShareRecords(diagramShareRecordMap.get(diagramId));
            esDiagramDTOList.add(esDiagramDTO);
        }
        return esDiagramDTOList;
    }

    @Override
    public Integer saveOrUpdateBatch(List<ESDiagram> list) {
        return esDiagramDao.saveOrUpdateBatch(list);
    }

    @Override
    public List<ESDiagram> selectDiagramsFromRecycle(List<Long> dirIds,String ownerCode,List<String> diagramIds) {
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        if(!BinaryUtils.isEmpty(dirIds)){
            boolQuery.must(QueryBuilders.termsQuery("dirId", dirIds));
        }
        if(!BinaryUtils.isEmpty(diagramIds)){
            boolQuery.must(QueryBuilders.termsQuery("dEnergy.keyword", diagramIds));
        }
        boolQuery.must(QueryBuilders.termQuery("status", 0));
        boolQuery.must(QueryBuilders.termQuery("historyVersionFlag", 1));
        boolQuery.must(QueryBuilders.termQuery("dataStatus", 1));
        if (!BinaryUtils.isEmpty(ownerCode)) {
            boolQuery.must(QueryBuilders.termQuery("ownerCode.keyword", ownerCode));
        }
        return esDiagramDao.selectListByQuery(1, 1000, boolQuery);
    }


    @Override
    public Integer queryFlowStatusById(String diagramId) {
        List<ESDiagram> esDiagramList = esDiagramDao.getListByQuery(QueryBuilders.termQuery("dEnergy.keyword", diagramId));
        if (BinaryUtils.isEmpty(esDiagramList)) {
            return 0;
        }
        ESDiagram esDiagram = esDiagramList.get(0);
        return BinaryUtils.isEmpty(esDiagram.getFlowStatus()) ? 0 : esDiagram.getFlowStatus();
    }

    private void saveTryResource(String icon1, String iconPath) {
        try {
            if (!StringUtils.isEmpty(icon1)) {
                String path = icon1.replaceAll(httpResourceUrl, "");
                byte[] iconBytes = FileUtils.readFileToByteArray(new File(FileFilterUtil.parseFilePath(localPath + path)));
                iconPath = this.saveOrUpdateResource(UUID.randomUUID().toString() + ".png", iconBytes, true);
            }
        } catch (Exception e) {
            logger.error("读取文件失败:", e);
        }
    }

    private void createTrySingleVersionRecord(ESDiagram esDiagram) {
        try {
            createSingleVersionRecord(esDiagram);
        } catch (Exception e) {
            logger.error("生成主版本记录时发生错误，diagramId={}", esDiagram.getId());
            e.printStackTrace();
        }
    }

    private void checkSheetId(String oldSheetId, int i, List<ESDiagramSheetDTO> tmpSheetList) throws Exception {
        try {
            if (StringUtils.isEmpty(oldSheetId)) {
                oldSheetId = tmpSheetList.get(i).getSheetId();
            }
        } catch (Exception e) {
            throw new Exception("modelList中不存在id字段，sheetList中也无与此对应的字段");
        }
    }

    private void tryDownloadRsmAndUpdateLocalRsm(String icon1, String iconPath) {
        try {
            if (!StringUtils.isEmpty(icon1)) {
                rsmUtils.downloadRsmAndUpdateLocalRsm(icon1);
                StringBuilder stringBuilder = new StringBuilder();
                stringBuilder.append(localPath);
                stringBuilder.append(icon1);
                byte[] iconBytes = FileUtils.readFileToByteArray(new File(FileFilterUtil.parseFilePath(stringBuilder.toString())));
                iconPath = this.saveOrUpdateResource(new StringBuilder(UUID.randomUUID().toString()).append(".png").toString() , iconBytes, true);
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.info("生成缩略图发生异常，iconPath:{}", iconPath);
        }
    }

    private void tryDownloadRsmAndUpdateLocalRsm1(String icon1, String iconPath) {
        try {
            if (!StringUtils.isEmpty(icon1)) {
                rsmUtils.downloadRsmAndUpdateLocalRsm(icon1);
                String path = icon1.replace(httpResourceUrl, "");
                StringBuilder stringBuilder = new StringBuilder();
                stringBuilder.append(localPath);
                stringBuilder.append(path);
                byte[] iconBytes = FileUtils.readFileToByteArray(new File(FileFilterUtil.parseFilePath(stringBuilder.toString())));
                iconPath = this.saveOrUpdateResource(new StringBuilder(UUID.randomUUID().toString()).append(".png").toString(), iconBytes, true);
            }
        } catch (Exception e) {
            logger.error("读取文件失败:", e);
        }
    }

    private void dealThumbnailInfo(ThumbnailBatch.ThumbnailInfo thumbnailInfo, int resultCode, String resultMsg) {
        try {
            String thumbnail = thumbnailInfo.getContent();
            String diagramId = thumbnailInfo.getId();
            BinaryUtils.checkEmpty(thumbnail, "缩略图信息串");
            String iconPath = thumbnailInfo.getPath();
            boolean pngCreation = false;
            //处理两张图用了同一个缩略图的情况
            if (StringUtils.isEmpty(iconPath) || queryByIcon(iconPath, diagramId) != null) {
                pngCreation = true;
                StringBuilder stringBuilder = new StringBuilder();
                stringBuilder.append(UUID.randomUUID());
                stringBuilder.append(".png");
                iconPath = stringBuilder.toString();
            }else{
                iconPath = iconPath.replace(httpResourceUrl, "");
            }
            iconPath = saveOrUpdatePng(iconPath, thumbnail, pngCreation);
            thumbnailInfo.setPath(new StringBuilder(httpResourceUrl).append(iconPath).toString());

            Long aLong = queryDiagramInfoByEnergy(diagramId);
            ESDiagram esDiagram = esDiagramDao.getById(aLong);
            if (BinaryUtils.isEmpty(esDiagram)) {
                throw new BinaryException("操作的视图不存在");
            } else {
                ESDiagram esDiagramForUpdate = new ESDiagram();
                esDiagramForUpdate.setId(aLong);
                esDiagramForUpdate.setDEnergy(diagramId);
                esDiagramForUpdate.setThumbnailSaveTime(thumbnailInfo.getLastModifyTime());
                esDiagramForUpdate.setIcon1(iconPath);
                esDiagramForUpdate.setModifier(esDiagram.getModifier());
                esDiagramForUpdate.setModifyTime(esDiagram.getModifyTime());
                esDiagramDao.updateBatchThumbnailTime(esDiagramForUpdate, true);
                thumbnailInfo.setContent(null);
            }
        } catch (Exception e) {
            logger.error("处理缩略图失败", e);
            resultCode = 0;
            resultMsg = e.getMessage();
        }
    }
}
