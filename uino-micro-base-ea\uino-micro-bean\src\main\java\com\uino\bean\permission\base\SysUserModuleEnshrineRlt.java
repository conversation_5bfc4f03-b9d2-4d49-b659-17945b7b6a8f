package com.uino.bean.permission.base;

import java.io.Serializable;

/**
 * mapping-table: 用户菜单收藏关联表[SYS_USER_MODULE_ENSHRINE_RLT]
 * 
 * <AUTHOR>
 */
public class SysUserModuleEnshrineRlt implements Serializable {

    /**
     * 
     */
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 模块ID */
    private Long moduleId;

    /** 用户ID */
    private Long userId;

    /** 所属域 */
    private Long domainId;

    /** 创建时间 */
    private Long createTime;

    /** 修改时间 */
    private Long modifyTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getModuleId() {
        return moduleId;
    }

    public void setModuleId(Long moduleId) {
        this.moduleId = moduleId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getDomainId() {
        return domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }

}
