package com.uinnova.product.eam.comm.model.es;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

@Data
public class EamNoticeKeyword implements Serializable {

    public EamNoticeKeyword(String name, Map<String, Object> params) {
        this.name = name;
        this.params = params;
    }

    @Comment("关键字名称")
    private String name;

    @Comment("参数")
    private Map<String, Object> params;
}