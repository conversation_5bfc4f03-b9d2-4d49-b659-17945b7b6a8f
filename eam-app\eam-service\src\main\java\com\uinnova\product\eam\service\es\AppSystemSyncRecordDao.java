package com.uinnova.product.eam.service.es;

import com.uinnova.product.eam.comm.model.es.AppSystemSyncRecord;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;

/**
 * 应用系统对接记录(DDM)
 * <AUTHOR>
 */
@Service
public class AppSystemSyncRecordDao extends AbstractESBaseDao<AppSystemSyncRecord, AppSystemSyncRecord> {

    @Override
    public String getIndex() {
        return "uino_eam_app_sync_record";
    }

    @Override
    public String getType() {
        return "_doc";
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }
}
