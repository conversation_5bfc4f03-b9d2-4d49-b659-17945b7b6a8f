package com.uinnova.product.eam.base.local;

public class TaskFromWorkflowContext {

    private static ThreadLocal<TaskFromWorkflowContextValue> context = new ThreadLocal<>();

    public static TaskFromWorkflowContextValue getContext(){
        return context.get();
    }

    public static void setContext(TaskFromWorkflowContextValue contextValue){
        context.set(contextValue);
    }

    /**
     * 释放当前线程数据
     */
    public static void release() {
        context.remove();
    }
}
