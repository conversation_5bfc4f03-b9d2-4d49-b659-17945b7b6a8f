package com.uino.service.util;

import com.alibaba.fastjson.JSON;
import com.binary.core.exception.MessageException;
import com.binary.core.lang.Conver;
import com.binary.core.util.BinaryUtils;
import com.uinnova.product.vmdb.comm.util.CommUtil;
import com.uino.bean.cmdb.business.*;
import com.uino.service.sys.microservice.IResourceSvc;
import com.uino.dao.util.ESUtil;
import com.uino.util.excel.EasyExcelUtil;
import com.uino.util.rsm.RsmUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.poi.openxml4j.exceptions.OpenXML4JException;
import org.apache.poi.openxml4j.opc.OPCPackage;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.usermodel.Row.MissingCellPolicy;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.DefaultIndexedColorMap;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFColor;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;
import org.xml.sax.SAXException;

import jakarta.annotation.PostConstruct;
import javax.xml.parsers.ParserConfigurationException;
import java.awt.Color;
import java.io.*;
import java.nio.file.Paths;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.List;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 *
 * <AUTHOR>
 *
 */
@Slf4j
@Component
@RefreshScope
public class FileUtil implements ApplicationRunner {
    private static IResourceSvc resourceSvc;

    private static RsmUtils rsmUtils;

    private static String localPath;

    private static String httpPath;

    private static String syncHttpPath;

    @Autowired(required = false)
    public void  setRsmUtils (RsmUtils rsmUtils){
        FileUtil.rsmUtils = rsmUtils;
    }

    @Autowired(required = false)
    public void setResourceSvc(IResourceSvc resourceSvc) {
        FileUtil.resourceSvc = resourceSvc;
    }

    @Value("${local.resource.space:}")
    public void setLocalPath(String localPath) {
        FileUtil.localPath = localPath;
    }

    @Value("${http.resource.space:}")
    public void setHttpPath(String httpPath) {
        FileUtil.httpPath = httpPath;
    }

    @Value("${http.resource.sync:}")
    public void setSyncHttpPath(String syncHttpPath) {
        FileUtil.syncHttpPath = syncHttpPath;
    }

    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("FileUtil Initialization method called");
    }

    /**
     * 删除资源目录下文件或者文件夹
     *
     * @param url
     */
    public static void delFileOrDir(String url) {
        url = url.startsWith(localPath) ? url : localPath + url;
        File delFile = new File(url);
        if (delFile.exists()) {
            if (delFile.isDirectory()) {
                FileUtil.delDirAndChildFiles(delFile);
            } else {
                delFile.delete();

                // 删除对象存储中的对应的文件
                rsmUtils.deleteRsm(url);
            }
            FileUtil.resourceSvc.saveSyncResourceInfo(url, FileUtil.syncHttpPath + url, false, 1);
        }
    }

    /**
     * 向资源根目录下写入文件，并带有同步记录
     *
     * @param fileUrl
     * @param body
     * @return
     * @throws IOException
     */
    public static String writeFile(String fileUrl, byte[] body) throws IOException {
        fileUrl = fileUrl.startsWith(localPath) ? fileUrl : localPath + fileUrl;
        return writeFile(new File(fileUrl), body);
    }

    /**
     * 写入文件，带有同步记录
     *
     * @param file
     * @param body
     * @return
     * @throws IOException
     */
    public static String writeFile(File file, byte[] body) throws IOException {
        // Assert.isTrue(file.getPath().startsWith(localPath), "write file path must start with " + localPath);
        int optionType = file.exists() ? 2 : 0;
        if (!file.getParentFile().exists()) {
            file.getParentFile().mkdirs();
        }
        FileOutputStream fos = new FileOutputStream(file);
        fos.write(body);
        fos.close();

        // 调用文件保存到对象存储的方法
        FileUtil.rsmUtils.uploadRsmFromFile(file);

        FileUtil.resourceSvc.saveSyncResourceInfo(file.getPath().replaceFirst(localPath, ""),
                FileUtil.syncHttpPath + file.getPath().replaceFirst(localPath, ""), false, optionType);
        return file.getPath();
    }

    /**
     * 删除文件夹以及文件夹下文件
     *
     * @param dir
     */
    public static void delDirAndChildFiles(File dir) {
        Assert.notNull(dir, "dir must not null");
        Assert.isTrue(dir.isDirectory(), "file must dir");
        Assert.isTrue(dir.getPath().startsWith(localPath), "del file path must start with " + localPath);
        if (dir.listFiles() != null) {
            for (File childFile : dir.listFiles()) {
                if (childFile.isDirectory()) {
                    delDirAndChildFiles(childFile);
                } else {
                    childFile.delete();

                    // 删除对象存储中对应的资源
                    rsmUtils.deleteRsm(childFile.getAbsolutePath());
                }
            }
        }
        dir.delete();
    }

    /**
     * 读取文件
     *
     * @param file
     * @return
     */
    public static byte[] readFileToByteArray(File file) {
        try {
            return FileUtils.readFileToByteArray(file);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 读取指定路径文件转换为List<T>
     *
     * @param path
     * @param obj
     *            <T>
     * @return
     */
    public static <T> List<T> getData(String path, Class<T> obj) {
        InputStream is = null;
        BufferedReader br = null;
        List<T> list = new ArrayList<T>();
        try {
            is = FileUtil.class.getResourceAsStream(path);
            br = new BufferedReader(new InputStreamReader(is, "utf-8"));
            StringBuffer sf = new StringBuffer();
            String line = "";
            while ((line = br.readLine()) != null) {
                sf.append(line.trim());
            }
            list = JSON.parseArray(sf.toString(), obj);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            try {
                if (br != null) {
                    br.close();
                }
                if (is != null) {
                    is.close();
                }
            } catch (Exception e) {
                throw new MessageException(e.getMessage());
            }
        }
        return list;
    }

    /**
     * 读取指定路径文件转换为List<T>
     *
     * @param path
     * @param obj
     *            <T>
     * @return
     */
    public static <T> T readObject(String path, Class<T> obj) {
        InputStream is = null;
        BufferedReader br = null;
        T result = null;
        try {
            is = FileUtil.class.getResourceAsStream(path);
            br = new BufferedReader(new InputStreamReader(is, "utf-8"));
            StringBuffer sf = new StringBuffer();
            String line = "";
            while ((line = br.readLine()) != null) {
                sf.append(line.trim());
            }
            result = JSON.parseObject(sf.toString(), obj);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            try {
                if (br != null) {
                    br.close();
                }
                if (is != null) {
                    is.close();
                }
            } catch (Exception e) {
                throw new MessageException(e.getMessage());
            }
        }
        return result;
    }

    /**
     * 读取输入流返回输入流对应bytes
     *
     * @param inStream
     * @return
     * @throws Exception
     */
    public static byte[] readStream(InputStream inStream, boolean clostInputStream) {
        try {
            ByteArrayOutputStream outSteam = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int len = -1;
            while ((len = inStream.read(buffer)) != -1) {
                outSteam.write(buffer, 0, len);
            }
            outSteam.close();
            if (clostInputStream) {
                inStream.close();
            }
            return outSteam.toByteArray();
        } catch (Exception e) {
            log.error("读取流异常", e);
            throw new RuntimeException(e);
        }
    }

    /**
     * base64解码
     * @param path 文件路径
     * @param content base64编码
     * @return 文件路径
     */
    public static String decodeBase64(String path, String content, boolean create) {
        if (content.length() >= content.indexOf(";base64,") + 8) {
            try {
                String substring = content.substring(content.indexOf(";base64,") + 8);
                byte[] bs = Base64.decodeBase64(substring);
                if (create) {
                    path = Paths.get("/" + LocalDate.now(), path).toString();
                }
                FileUtil.writeFile(path, bs);
            } catch (IOException e) {
                log.error("写入文件：{} 错误！", path, e);
            }
            return path;
        }
        return path;
    }

    /**
     * excel相关操作
     *
     * <AUTHOR>
     *
     */
    public static class ExcelUtil {

        /**
         * 导出的时候带的时间戳
         */
        public static final SimpleDateFormat EXPORT_TIME_FORMAT = new SimpleDateFormat("yyyyMMdd_HHmmss");

        /**
         * 根据某一行设置列自动适应大小(伪自适应,是根据这一行的内容宽度将列都设为这个宽度.因为自适应在row过多时影响运行效率)
         *
         * @param sheet
         * @param row
         */
        public static void setAutoColumnSizeByRow(Sheet sheet, Row row) {
            if (sheet != null && row != null) {
                row.cellIterator().forEachRemaining(cell -> {
                    // excel中一个字符长度为1/256;最大允许容纳30个字符宽度,最小容纳16个字符宽度
                    int minLength = 16 * 256;
                    int maxLength = 30 * 256;
                    String val = cell.getStringCellValue();
                    int length = val.getBytes().length * 256;
                    length = length < minLength ? minLength : length;
                    length = length > maxLength ? maxLength : length;
                    sheet.setColumnWidth(cell.getColumnIndex(), length);
                });
            }
        }

        /**
         * 获取title-cell的值(获取出值后会去除[****]返回去除后的值)eg: 业务主键[主键*]=>业务主键
         *
         * @param cell
         *            待处理单元格
         * @return 处理后的值
         */
        public static String getCellTitleString(Cell cell) {
            String val = ExcelUtil.getStringValue(cell);
            return getCellTitleString(val);
        }

        /**
         * 获取title-cell的值(获取出值后会去除[****]返回去除后的值)eg: 业务主键[主键*]=>业务主键
         *
         * @param originalVal
         * @return
         */
        public static String getCellTitleString(String originalVal) {
            String markReg = "\\[(.*?)\\]$";
            return originalVal.replaceAll(markReg, "");
        }

        /**
         * 判断指定cell是否为主键
         *
         * @param cell
         *            待判断单元格
         * @param majorMark
         *            主键标识
         * @return
         */
        public static boolean isMajorCell(Cell cell, String majorMark) {
            String val = ExcelUtil.getStringValue(cell);
            return isMajorCell(val, majorMark);
        }

        /**
         * 判断指定cell值是否为主键
         *
         * @param val
         *            待判断单元格
         * @param majorMark
         *            主键标识
         * @return
         */
        public static boolean isMajorCell(String val, String majorMark) {
            String markReg = "\\[(.*?)\\]$";
            Matcher mat = Pattern.compile(markReg).matcher(val);
            while (mat.find()) {
                int indexOf = val.lastIndexOf(mat.group(0));
                val = val.substring(0, indexOf);
                String pkAttr = mat.group(1).replaceAll(" ", "");
                if (pkAttr.contains(majorMark.trim())) { return true; }
            }
            return false;
        }

        /**
         * 判断单元格是否为必填
         *
         * @param cell
         *            待判断单元格
         * @return
         */
        public static boolean isRequireCell(Cell cell) {
            String val = ExcelUtil.getStringValue(cell);
            return isRequireCell(val);
        }

        /**
         * 判断判断单元格值是否为必填
         *
         * @param val
         * @return
         */
        public static boolean isRequireCell(String val) {
            String markReg = "\\[(.*?)\\]$";
            Matcher mat = Pattern.compile(markReg).matcher(val);
            while (mat.find()) {
                int indexOf = val.lastIndexOf(mat.group(0));
                val = val.substring(0, indexOf);
                String pkAttr = mat.group(1).replaceAll(" ", "");
                if (pkAttr.contains("*")) { return true; }
            }
            return false;
        }

        /**
         * 设置单元格(填补必填以及主键标识)
         *
         * @param cell
         *            待设置单元格(值已经确定的)
         * @param markName
         *            主键名称
         * @param isMajor
         *            是否为主键
         * @param isRequire
         *            是否为必填
         * @return
         */
        public static Cell setCellMark(Workbook workBook, Cell cell, String markName, boolean isMajor,
                boolean isRequire) {
            // 内容字体
            Font contentFont = workBook.createFont();
            contentFont.setFontName("微软雅黑");
            contentFont.setFontHeightInPoints((short) 14);
            // contentFont.setBold(true);
            // 标识字体
            Font markFont = workBook.createFont();
            markFont.setFontName("微软雅黑");
            markFont.setFontHeightInPoints((short) 14);
            // markFont.setBold(true);
            markFont.setColor(IndexedColors.RED.index);

            XSSFCellStyle cellStyle = (XSSFCellStyle) workBook.createCellStyle();
            cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            cellStyle.setFillForegroundColor(new XSSFColor(new Color(255, 242, 204),new DefaultIndexedColorMap()));
            // cellStyle.setFillForegroundColor(IndexedColors.LIGHT_CORNFLOWER_BLUE.index);
            cellStyle.setAlignment(HorizontalAlignment.CENTER);
            cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            cellStyle.setBorderBottom(BorderStyle.THIN);
            cellStyle.setBorderLeft(BorderStyle.THIN);
            cellStyle.setBorderRight(BorderStyle.THIN);
            cellStyle.setBorderTop(BorderStyle.THIN);
            String value = ExcelUtil.getStringValue(cell);
            String mark = "";
            if (isMajor) {
                mark += markName;
            }
            if (isRequire) {
                mark += "*";
            }
            value = (mark != null && !"".equals(mark.trim())) ? (value + "[" + mark + "]") : value;
            // RichTextString cellVal = null;
            // if (workBook instanceof XSSFWorkbook) {
            // cellVal = new XSSFRichTextString(value);
            // } else if (workBook instanceof HSSFWorkbook) {
            // cellVal = new HSSFRichTextString(value);
            // } else {
            // Assert.isTrue(false, "暂不支持该格式excel");
            // }
            if (mark != null && !"".equals(mark.trim())) {
                cellStyle.setFont(markFont);
                // cellVal.applyFont(0, value.indexOf("["), contentFont);
                // cellVal.applyFont(value.indexOf("["), value.length(),
                // markFont);
            } else {
                cellStyle.setFont(contentFont);
                // cellVal.applyFont(contentFont);
            }
            // cell.setCellValue(cellVal);
            cell.setCellValue(value);
            cell.setCellStyle(cellStyle);
            return cell;
        }

        public static Map<String, List<String>> getAttrMarkByTitles(Collection<String> titles) {
            Map<String, List<String>> result = new HashMap<>();
            List<String> majorAttrs = new ArrayList<>();
            List<String> requireAttrs = new ArrayList<>();
            List<String> totalTitles = new ArrayList<>();
            String markReg = "\\[(.*?)\\]$";
            for (String title : titles) {
                Matcher mat = Pattern.compile(markReg).matcher(title);
                while (mat.find()) {
                    int indexOf = title.indexOf(mat.group(0));
                    title = title.substring(0, indexOf);
                    String pkAttr = mat.group(1).replaceAll(" ", "");
                    if (pkAttr.contains("*")) {
                        requireAttrs.add(title);
                    }
                    if (pkAttr.contains("主键")) {
                        majorAttrs.add(title);
                    }
                }
                totalTitles.add(title);
            }
            if (!BinaryUtils.isEmpty(majorAttrs)) {
                result.put("majorAttrs", majorAttrs);
            }
            if (!BinaryUtils.isEmpty(requireAttrs)) {
                result.put("requireAttrs", requireAttrs);
            }
            result.put("titles", totalTitles);
            return result;
        }

        public static String setTitleMark(String title, String markName, boolean isMajor, boolean isRequire) {
            String mark = "";
            if (isMajor) {
                mark += markName;
            }
            if (isRequire) {
                mark += "*";
            }
            title += "[" + mark + "]";
            return title;
        }

        /**
         * 获取shht标题
         *
         * @param sheet
         * @return
         */
        public static List<String> getSheetTitlesListFirstIsBlank(Sheet sheet) {
            Map<Integer, String> sheetTitles = getSheetTitles(sheet);
            List<String> titles = new ArrayList<String>(sheetTitles.size());
            titles.addAll(sheetTitles.values());
            return titles;
        }

        /**
         * 获取sheet标题字典
         *
         * @param sheet
         * @return
         */
        public static Map<Integer, String> getSheetTitles(Sheet sheet) {
            LinkedHashMap<Integer, String> ret = new LinkedHashMap<Integer, String>();
            Row titleRow = sheet.getRow(0);
            if (titleRow == null) { return ret; }
            short lastCellNum = titleRow.getLastCellNum();
            for (int i = 0; i < lastCellNum; i++) {
                Cell cell = titleRow.getCell(i);
                String title = getStringValue(cell);
                if (!BinaryUtils.isEmpty(title, true)) {
                    ret.put(i, title.trim());
                } else {
                    break;
                }
            }
            return ret;
        }

        /**
         * 获取单元格string val
         *
         * @param cell
         * @return
         */
        public static String getStringValue(Cell cell) {
            Object value = getValue(cell);
            if (value == null) { return ""; }
            return value.toString().trim();
        }

        /**
         * 获取单元格obj val
         *
         * @param cell
         * @return
         */
        private static Object getValue(Cell cell) {
            if (cell == null) { return null; }
            CellType cellType = cell.getCellType();
            if (CellType.STRING == cellType) {
                return cell.getStringCellValue();
            } else if (CellType.NUMERIC == cellType) {
                if (DateUtil.isCellDateFormatted(cell)) {
                    Date d = cell.getDateCellValue();
                    long time = d.getTime();
                    return Conver.to(d, String.class, time > 1500000000000L ? "yyyy-MM-dd HH:mm:ss" : "HH:mm:ss");
                }
                Double doubleValue = Double.valueOf(cell.getNumericCellValue());
                return doubleValue - doubleValue.intValue() == 0 ? doubleValue.intValue() + "" : doubleValue + "";
            } else if (CellType.BOOLEAN == cellType) {
                return Boolean.valueOf(cell.getBooleanCellValue());
            } else if (CellType.BLANK == cellType) {
                return "";
            } else {
                return cell.getStringCellValue();
            }
        }

        public static List<String[]> getSheetDatasArraybyPage(int pageNum, int pageSize, Sheet sheet,
                Map<String, String> fieldMap) {
            List<String[]> ret = new ArrayList<String[]>();
            if (sheet == null) { return ret; }
            // 防止存在空列时的映射错误, 获得titleNums
            Map<Integer, String> sheetTitleMap = getSheetTitles(sheet);
            Set<Integer> titleNums = sheetTitleMap.keySet();
            int lastRowNum = sheet.getLastRowNum();
            int starRow = (pageNum - 1) * pageSize + 1;
            int endRow = pageNum * pageSize;
            endRow = endRow > lastRowNum ? lastRowNum : endRow;
            for (int i = starRow; i <= endRow; i++) {
                Row row = sheet.getRow(i);
                if (row != null && !isBlankRow(row)) {
                    String[] data = new String[titleNums.size()];
                    // 计数器j用于从fieldMap中取映射关系
                    int j = 0;
                    for (Integer titleNum : titleNums) {
                        data[j] = getStringValue(row.getCell(titleNum));
                        ++j;
                    }
                    ret.add(data);
                } else {
                    break;
                }
            }
            return ret;
        }

        /**
         * 判断 row 的数据是否为空
         *
         * @param row
         * @return
         */
        public static boolean isBlankRow(Row row) {
            if (row == null) { return true; }
            boolean result = true;
            for (int i = row.getFirstCellNum(); i < row.getLastCellNum(); i++) {
                Cell cell = row.getCell(i, MissingCellPolicy.RETURN_BLANK_AS_NULL);
                String value = "";
                if (cell != null) {
                    CellType cellType = cell.getCellType();
                    if (cellType == CellType.STRING) {
                        value = cell.getStringCellValue();
                    } else if (cellType == CellType.NUMERIC) {
                        value = String.valueOf((int) cell.getNumericCellValue());
                    } else if (cellType == CellType.BOOLEAN) {
                        value = String.valueOf(cell.getBooleanCellValue());
                    } else if (cellType == CellType.FORMULA) {
                        value = String.valueOf(cell.getCellFormula());
                    } else {
                        value = cell.getStringCellValue();
                    }
                    if (!"".equals(value.trim())) {
                        result = false;
                        break;
                    }
                }
            }
            return result;
        }

        /**
         * 创建sheet并且设置好标题行<br />
         *
         * @param swb
         * @param sheetName
         *            sheet名字,默认是"Sheet(编号)"
         * @param titleCellValues
         *            标题内容(含主键列和所有业务主键列)
         * @param reqCellValues
         *            必填列值(属性后追加[*])
         * @param pkbCellValues
         *            业务主键列(属性后追加[主键])
         * @return sheet对象
         */
        public static Sheet createExcelSheetAndTitle(Workbook swb, String sheetName, List<String> titleCellValues,
                Set<String> reqCellValues, Set<String> pkbCellValues, String specialTitle) {
            if (swb == null) {
                swb = new XSSFWorkbook();
            }
            if (BinaryUtils.isEmpty(sheetName)) {
                int sheets = swb.getNumberOfSheets();
                sheetName = "Sheet(" + (sheets + 1) + ")";
            }
            if (pkbCellValues == null) {
                pkbCellValues = new HashSet<String>();
            }
            if (reqCellValues == null) {
                reqCellValues = new HashSet<String>();
            }
            Sheet sheet = swb.createSheet(sheetName);
            // sheet.setDefaultColumnWidth(20);
            // 没有标题内容则不创建标题
            if (!BinaryUtils.isEmpty(titleCellValues)) {
                Row titleRow = sheet.createRow(0);
                titleRow.setHeight((short) 400);
                Font cellFont = swb.createFont();
                cellFont.setFontName("微软雅黑");
                cellFont.setFontHeightInPoints((short) 14);
                // cellFont.setBold(true);
                cellFont.setColor(IndexedColors.RED.index);
                XSSFCellStyle cellStyle = (XSSFCellStyle) swb.createCellStyle();
                cellStyle.setFont(cellFont);
                cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                cellStyle.setFillForegroundColor(new XSSFColor(new Color(255, 242, 204),new DefaultIndexedColorMap()));
                // cellStyle.setFillForegroundColor(IndexedColors.WHITE.index);
                cellStyle.setAlignment(HorizontalAlignment.CENTER);
                cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                DataFormat dataFormat = swb.createDataFormat();
                cellStyle.setDataFormat(dataFormat.getFormat("@"));
                // 标记是否已经处理过ciCode列样式
                // Boolean majorStyle = false;
                for (int cellnum = 0; cellnum < titleCellValues.size(); cellnum++) {
                    Cell cell = titleRow.createCell(cellnum);
                    String value = titleCellValues.get(cellnum);
                    if (BinaryUtils.isEmpty(value)) {
                        continue;
                    }
                    cell.setCellValue(value);
                    // if (!BinaryUtils.isEmpty(majorCellValue) &&
                    // value.equals(majorCellValue) && !majorStyle) {
                    // cell.setCellStyle(majorCellStyle);
                    // majorStyle = true;
                    if (value.equals(specialTitle)) {
                        cell.setCellStyle(cellStyle);
                    } else if (pkbCellValues.contains(value)) {
                        setCellMark(swb, cell, "主键", true, false);
                    } else if (reqCellValues.contains(value)) {
                        setCellMark(swb, cell, "主键", false, true);
                    } else {
                        setCellMark(swb, cell, "主键", false, false);
                    }
                }
                setAutoColumnSizeByRow(sheet, titleRow);
            }
            return sheet;
        }

        /**
         * 获取标题列样式
         *
         * @param swb
         * @param isRequire
         *            是否必填项 (必填列字体加粗置红)
         * @param isMajor
         *            是否主键列(主键列字体加粗背景置黄)
         * @return
         */
        public static CellStyle createExcelTitleCellStyle(SXSSFWorkbook swb, boolean isRequire, boolean isMajor) {
            Font cellFont = null;
            if (isRequire) {
                cellFont = createCellFont(swb, (short) 12, IndexedColors.RED.index, true);
            } else {
                cellFont = createCellFont(swb, (short) 12, (short) 0, true);
            }
            CellStyle cellStyle = swb.createCellStyle();
            if (isMajor) {
                cellStyle.setFillForegroundColor(IndexedColors.LIGHT_YELLOW.index);
            } else {
                cellStyle.setFillForegroundColor(IndexedColors.LIGHT_CORNFLOWER_BLUE.index);
            }
            cellStyle.setFont(cellFont);
            cellStyle.setAlignment(HorizontalAlignment.CENTER);
            cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            cellStyle.setBorderBottom(BorderStyle.THIN);
            cellStyle.setBorderLeft(BorderStyle.THIN);
            cellStyle.setBorderRight(BorderStyle.THIN);
            cellStyle.setBorderTop(BorderStyle.THIN);
            cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            return cellStyle;
        }

        /**
         * 设置列值样式
         *
         * @param swb
         * @param fontSize
         *            字体大小
         * @param fontColor
         *            字体颜色
         * @param isBold
         *            是否加粗
         * @return
         */
        public static Font createCellFont(Workbook swb, short fontSize, short fontColor, Boolean isBold) {
            Font cellFont = swb.createFont();
            cellFont.setFontName("微软雅黑");
            if (fontSize != 0) {
                cellFont.setFontHeightInPoints(fontSize);
            }
            if (fontColor != 0) {
                cellFont.setColor(fontColor);
            }
            cellFont.setBold(isBold);
            return cellFont;
        }

        /**
         * 写入正文内容
         *
         * @param swb
         * @param sheet
         * @param writeTotalRows
         *            已经写入的总行数,也是开始写入行数
         * @param titleCellValues
         *            标题列值集合
         * @param reqCellValues
         *            必填列标题值(可选)
         * @param pkbCellValues
         *            业务主键值(可选)
         * @param commentValueMaps
         *            正文Map集合:Map的Key必须是大写(值长度,不得超过最大最大值(16000),超过则不导入真实值)
         * @return 写入的总行数
         */
        public static Integer writeExcelComment(Workbook swb, Sheet sheet, int writeTotalRows,
                List<String> titleCellValues, Set<String> reqCellValues, Set<String> pkbCellValues,
                List<Map<String, String>> commentValueMaps) {
            Set<String> stdReqCellValues = new HashSet<String>();
            if (!BinaryUtils.isEmpty(reqCellValues)) {
                // 将必填属性转成大写
                for (String reqKey : reqCellValues) {
                    stdReqCellValues.add(reqKey.toUpperCase());
                }
            }
            Set<String> stdPkbCellValues = new HashSet<String>();
            if (!BinaryUtils.isEmpty(pkbCellValues)) {
                // 将业务主键属性转成大写
                for (String pkbkey : pkbCellValues) {
                    stdPkbCellValues.add(pkbkey.toUpperCase());
                }
            }
            // 将集合中的Map的key转为大写
            List<Map<String, String>> commentValueNewMaps = commentValueMapKeyToUpperCase(commentValueMaps);
            // 内容和标题为空则不执行
            if (!BinaryUtils.isEmpty(titleCellValues) && commentValueNewMaps != null
                    && commentValueNewMaps.size() > 0) {
                // 获取标题列总数
                int totalCellNum = titleCellValues.size();
                // 获取本次写入总行数
                int totalSize = commentValueNewMaps.size();
                // 计算写入截止行
                int writeEndRows = writeTotalRows + totalSize;
                // 设置正文公共样式
                XSSFCellStyle reqCellStyle = (XSSFCellStyle) createExcelCellStyle(swb, true, false);
                // 必填项背景蓝色
                reqCellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                reqCellStyle.setFillForegroundColor(new XSSFColor(new Color(255, 242, 204),new DefaultIndexedColorMap()));
                // reqCellStyle.setFillForegroundColor(IndexedColors.LIGHT_CORNFLOWER_BLUE.index);
                CellStyle cellStyle = createExcelCellStyle(swb, false, false);
                CellStyle pkbCellStyle = createExcelCellStyle(swb, false, true);
                // 记录取正文值的索引
                int rowIndex = 0;
                for (int rownum = writeTotalRows; rownum < writeEndRows; rownum++) {
                    Row row = sheet.createRow(rownum);
                    row.setHeight((short) 300);
                    // 避免数组下标越界
                    if (rowIndex == totalSize) {
                        break;
                    }
                    Map<String, String> attrs = commentValueNewMaps.get(rowIndex);
                    rowIndex++;
                    if (attrs == null || attrs.isEmpty()) {
                        continue;
                    }
                    for (int column = 0; column < totalCellNum; column++) {
                        Cell cell = row.createCell(column);
                        String key = titleCellValues.get(column).toUpperCase();
                        String value = attrs.get(key);
                        // 判断长度,不得超过最大最大值(32767),超过则不导入真实值
                        if (!BinaryUtils.isEmpty(value) && value.length() > 32767) {
                            value = "（字数超过列的极限，无法导出!）";
                            cell.setCellStyle(reqCellStyle);
                        } else {
                            // 字数正常
                            // 设置单列字体样式
                            if (stdPkbCellValues.contains(key)) {
                                cell.setCellStyle(pkbCellStyle);
                            } else if (stdReqCellValues.contains(key)) {
                                cell.setCellStyle(reqCellStyle);
                            } else {
                                cell.setCellStyle(cellStyle);
                            }
                        }
                        cell.setCellValue(value);
                    }
                    writeTotalRows++;
                }
            }
            return writeTotalRows;
        }

        /**
         * 将正文值集合中的Map的Key统一转成大写
         *
         * @param commentValueMaps
         *            正文值结合
         * @return 新的正文值集合
         */
        @SuppressWarnings("rawtypes")
        public static List<Map<String, String>> commentValueMapKeyToUpperCase(
                List<Map<String, String>> commentValueMaps) {
            List<Map<String, String>> valueMaps = new ArrayList<Map<String, String>>();
            if (commentValueMaps == null || commentValueMaps.size() == 0) { return valueMaps; }
            for (Map<String, String> map : commentValueMaps) {
                if (map == null || map.isEmpty()) {
                    continue;
                }
                Map<String, String> mapNew = new HashMap<String, String>();
                Iterator iter = map.entrySet().iterator();
                while (iter.hasNext()) {
                    Map.Entry entry = (Map.Entry) iter.next();
                    Object key = entry.getKey();
                    Object val = entry.getValue();
                    String keyNew = null;
                    String valNew = "";
                    if (!BinaryUtils.isEmpty(key)) {
                        keyNew = key.toString().toUpperCase();
                    }
                    if (!BinaryUtils.isEmpty(val)) {
                        valNew = val.toString();
                    }
                    if (!BinaryUtils.isEmpty(keyNew)) {
                        mapNew.put(keyNew, valNew);
                    }
                }
                if (!mapNew.isEmpty()) {
                    valueMaps.add(mapNew);
                }
            }
            return valueMaps;
        }

        /**
         * 获取正文列样式
         *
         * @param swb
         * @param isRequire
         *            是否必填项(加粗)
         * @param isMajor
         *            是否主键列(加粗)
         * @return
         */
        public static CellStyle createExcelCellStyle(Workbook swb, boolean isRequire, boolean isMajor) {
            Font cellFont = null;
            if (isRequire || isMajor) {
                cellFont = createCellFont(swb, (short) 10, (short) 0, true);
            } else {
                cellFont = createCellFont(swb, (short) 10, (short) 0, false);
            }
            CellStyle cellStyle = swb.createCellStyle();
            cellStyle.setFont(cellFont);
            cellStyle.setAlignment(HorizontalAlignment.LEFT);
            cellStyle.setBorderBottom(BorderStyle.THIN);
            cellStyle.setBorderLeft(BorderStyle.THIN);
            cellStyle.setBorderRight(BorderStyle.THIN);
            cellStyle.setBorderTop(BorderStyle.THIN);
            cellStyle.setWrapText(true);
            DataFormat dataFormat = swb.createDataFormat();
            cellStyle.setDataFormat(dataFormat.getFormat("@"));
            return cellStyle;
        }

        public static CellStyle createAttentionCellStyle(Workbook swb, short fontColor) {
            CellStyle cellStyle = swb.createCellStyle();
            cellStyle.setWrapText(true);
            Font cellFont = swb.createFont();
            cellFont.setFontName("微软雅黑");
            cellFont.setFontHeightInPoints((short) 14);
            if (fontColor != 0) {
                cellFont.setColor(fontColor);
            }
            cellStyle.setFont(cellFont);
            cellStyle.setAlignment(HorizontalAlignment.LEFT);
            cellStyle.setBorderBottom(BorderStyle.THIN);
            cellStyle.setBorderLeft(BorderStyle.THIN);
            cellStyle.setBorderRight(BorderStyle.THIN);
            cellStyle.setBorderTop(BorderStyle.THIN);
            cellStyle.setWrapText(true);
            return cellStyle;
        }

        /**
         * 导出数据的文件名字
         *
         * @param fileName
         *            文件名称(不传则默认为Export)
         * @param fileType
         *            文件类型[.后缀名](无默认值)
         * @param timestamp
         *            文件名是否带时间戳
         * @return 名字后面带时间戳的文件名
         */
        public static String getExportFileName(String fileName, String fileType, Boolean timestamp) {
            if (BinaryUtils.isEmpty(fileName)) {
                fileName = "Export";
            }
            if (timestamp) {
                fileName = fileName + "-" + EXPORT_TIME_FORMAT.format(new Date());
            }
            if (!BinaryUtils.isEmpty(fileType)) {
                fileName = fileName + fileType.toLowerCase();
            }
            return fileName;
        }

        /**
         * 将sheetMessages写入到csv文件中
         *
         * @param sheetMessages
         * @param fileName
         * @return
         */
        public static ImportResultMessage writeSheetMessageToFile(List<ImportSheetMessage> sheetMessages, String fileName, String... ignoreNumSheet) {
            ImportResultMessage result = new ImportResultMessage();
            // 创造出该虚拟文件夹下操作对应的文件系统里的文件夹
            Long dateTimeFolder = ESUtil.getNumberDate();
            int totalNum = 0;
            int successNum = 0;
            int failNum = 0;
            int insertNum = 0;
            int updateNum = 0;
            String filePath = "/" + dateTimeFolder + "/" + fileName + "_" + new SimpleDateFormat("yyyyMMdd-HHmmss").format(new Date()) + ".xlsx";
            Workbook swb = new XSSFWorkbook();
            FileOutputStream fileOutputStream = null;
            try {
                File output = new File(localPath + filePath);
                if (!output.getParentFile().exists()) {
                    output.getParentFile().mkdirs();
                }
                fileOutputStream = new FileOutputStream(output);
                List<String> ignoreSheets=new ArrayList();
                if (!BinaryUtils.isEmpty(ignoreNumSheet)){
                    ignoreSheets = Arrays.asList(ignoreNumSheet);
                }
                if(CollectionUtils.isEmpty(sheetMessages)){
                    swb.createSheet("对象定义");
                }else {
                for (ImportSheetMessage sheetMessage : sheetMessages) {
                    String sheetName = sheetMessage.getSheetName();
                    if (!ignoreSheets.contains(sheetName)) {
                        totalNum += sheetMessage.getTotalNum();
                        successNum += sheetMessage.getSuccessNum();
                        insertNum += sheetMessage.getInsertNum();
                        updateNum += sheetMessage.getUpdateNum();
                        failNum += sheetMessage.getFailNum() + sheetMessage.getIgnoreNum();
                    }
                    int rowNum = 0;
                    Sheet sheet = swb.createSheet(sheetName);
                    List<ImportRowMessage> rowMessages = sheetMessage.getRowMessages();
                    Row titleRow = sheet.createRow(rowNum++);
                    Cell titleCell0 = titleRow.createCell(0, CellType.STRING);
                    titleCell0.setCellValue(sheetMessage.getSheetName() + "-导入明细");
                    Row successRow = sheet.createRow(rowNum++);
                    Cell successCell = successRow.createCell(0, CellType.STRING);
                    successCell.setCellValue(
                        MessageFormat.format("成功：{0}/{1}（新增{2}/更新{3}）", sheetMessage.getSuccessNum(), sheetMessage.getTotalNum(), sheetMessage.getInsertNum(), sheetMessage.getUpdateNum()));
                    Row failRow = sheet.createRow(rowNum++);
                    Cell failCell = failRow.createCell(0, CellType.STRING);
                    failCell.setCellValue(MessageFormat.format("失败：{0}/{1}", sheetMessage.getFailNum() + sheetMessage.getIgnoreNum(), sheetMessage.getTotalNum()));
                    if (!BinaryUtils.isEmpty(sheetMessage.getErrMsg())) {
                        Row row = sheet.createRow(rowNum++);
                        Cell cell = row.createCell(0, CellType.STRING);
                        cell.setCellValue(sheetMessage.getErrMsg());
                        continue;
                    }
                    if (!BinaryUtils.isEmpty(rowMessages)) {
                        Collections.sort(rowMessages, getRowMessageComparator(rowMessages));
                        for (int i = 0; i < rowMessages.size(); i++) {
                            ImportRowMessage rowMessage = rowMessages.get(i);
                            Row row = sheet.createRow(rowNum++);
                            Cell cell = row.createCell(0, CellType.STRING);
                            if (rowMessage.getRowNum() != null) {
                                cell.setCellValue("第" + rowMessage.getRowNum() + "行");
                            }
                            if (rowMessage.getMessageItems() != null) {
                                for (int j = 0; j < rowMessage.getMessageItems().size(); j++) {
                                    ImportCellMessage cellMessage = rowMessage.getMessageItems().get(j);
                                    Cell cell2 = row.createCell(j + 1, CellType.STRING);
                                    cell2.setCellValue(cellMessage.getErrorDesc());
                                }
                            }
                        }
                        sheetMessage.setRowMessages(null);
                    }
                }
                }
                result.setTotalNum(totalNum);
                result.setSuccessNum(successNum);
                result.setInsertNum(insertNum);
                result.setUpdateNum(updateNum);
                result.setFailNum(failNum);
                result.setFailFile(httpPath + filePath);
                swb.write(fileOutputStream);
                swb.close();
                fileOutputStream.close();

                // 将明细数据上传至对象存储
                rsmUtils.uploadRsmFromFile(output);
            } catch (Exception e) {
                log.error("文件写入失败：" + e.getMessage());
				throw new MessageException(e.getMessage());
            } finally {
                try {
                    if (swb != null) {
                        swb.write(fileOutputStream);
                        swb.close();
                    }
                    if (fileOutputStream != null) {
                        fileOutputStream.close();
                    }
                } catch (Exception e2) {
                }
            }
            return result;
        }

        /**
         * 校验Excel导入文件
         *
         * @param file
         */
        public static boolean validExcelImportFile(MultipartFile file) {
            // 校验文件
            Assert.notNull(file, " the upload file is empty! ");
            Assert.isTrue(file.getSize() < CommUtil.MAX_EXCEL_SIZE, "BS_MNAME_FILE_OVERLENGTH");
            String fileType = CommUtil.getImportExcelType(file.getOriginalFilename());
            boolean isXlsx = true;
            if (fileType.toUpperCase().equals(CommUtil.EXCEL07_XLSX_EXTENSION.toUpperCase())) {
                isXlsx = true;
            } else if (fileType.toUpperCase().equals(CommUtil.EXCEL03_XLS_EXTENSION.toUpperCase())) {
                isXlsx = false;
            } else {
                throw MessageException.i18n("BS_MNAME_NOT_SUPPORT_FILETYPE");
            }
            return isXlsx;
        }

        public static Comparator<ImportRowMessage> getRowMessageComparator(List<ImportRowMessage> rowMessages) {
            return new Comparator<ImportRowMessage>() {

                @Override
                public int compare(ImportRowMessage r1, ImportRowMessage r2) {
                    Integer row1 = r1.getRowNum() == null ? 0 : r1.getRowNum();
                    Integer row2 = r2.getRowNum() == null ? 0 : r2.getRowNum();
                    return row1 - row2;
                }
            };
        }

        public static class XLSXCovertCSVReader extends com.uinnova.product.vmdb.comm.util.XLSXCovertCSVReader {

            public XLSXCovertCSVReader(OPCPackage pkg, PrintStream output, String sheetName, int minColumns) {
                super(pkg, output, sheetName, minColumns);
                // TODO Auto-generated constructor stub
            }

            /**
             * 读取一个Excel中所有的Sheet页名字和每个Sheet页的标题行内容
             *
             * @param is
             * @return Excel文件中所有的Sheet页名字和每个Sheet页的标题行内容
             * @throws IOException
             * @throws OpenXML4JException
             * @throws ParserConfigurationException
             * @throws SAXException
             */
            public static Map<String, List<String>> readerSheetTitlesByIs(InputStream is, boolean close)
                    throws IOException, OpenXML4JException, ParserConfigurationException, SAXException {
                OPCPackage p = OPCPackage.open(is);
                com.uinnova.product.vmdb.comm.util.XLSXCovertCSVReader xlsx2csv = new com.uinnova.product.vmdb.comm.util.XLSXCovertCSVReader(
                        p, System.out, null, -2, 1, 10);
                // 耗时
                Map<String, List<String>> map = xlsx2csv.getSheetTitlesProcess();
                if (close) {
                    p.close();
                }
                return map;
            }

            /**
             * 读取Excel指定Sheet页内容且返回结果是Map集合
             *
             * @param is
             *            读取完成后会自动关闭流
             * @param sheetName
             *            sheet名称
             * @param minColumns
             *            列总数：0或者小于0都读全部有值的列
             * @param isStdKey
             *            是否需要将结果中Map的Key值(列头值)标准化(转成大写)[true Or false]
             * @return List<Map<String, String>>: Map中Key为列头值;Value列头对应的行坐标的正文列值
             * @throws SAXException
             * @throws ParserConfigurationException
             * @throws OpenXML4JException
             * @throws IOException
             */
            public static List<Map<String, String>> readerExcelToMapsByIs(InputStream is, String sheetName,
                    int minColumns, boolean isStdKey)
                    throws IOException, OpenXML4JException, ParserConfigurationException, SAXException {
                List<String[]> list = readerExcelByIs(is, sheetName, minColumns);
                return arrayConvertMapsBySheetDatas(list, isStdKey);
            }

            /**
             * 读取Excel中所有的sheet页名字
             *
             * @param is
             *
             * @param close
             *            是否关闭流
             * @return Excel文件中所有的Sheet页名字
             * @throws IOException
             * @throws OpenXML4JException
             * @throws ParserConfigurationException
             * @throws SAXException
             */
            public static List<String> readerSheetNamesByIs(InputStream is, boolean close)
                    throws IOException, OpenXML4JException, ParserConfigurationException, SAXException {
                OPCPackage p = OPCPackage.open(is);
                XLSXCovertCSVReader xlsx2csv = new XLSXCovertCSVReader(p, System.out, null, 0);
                List<String> list = xlsx2csv.getSheetNamesProcess();
                if (close) {
                    p.close();
                }
                return list;
            }

            /**
             * 读取Excel指定Sheet页内容且返回结果是String[]数组(行数据)集合
             *
             * @param is
             *            文件流 读取完成后会自动关闭流
             * @param sheetName
             *            sheet名称
             * @param minColumns
             *            列总数：0或者小于0都读全部有值的列
             * @param close
             *            是否关闭流
             * @return String[]格式的行数据集合
             * @throws SAXException
             * @throws ParserConfigurationException
             * @throws OpenXML4JException
             * @throws IOException
             */
            public static List<String[]> readerExcelByIs(InputStream is, String sheetName, int minColumns,
                    boolean close) throws IOException, OpenXML4JException, ParserConfigurationException, SAXException {
                OPCPackage p = OPCPackage.open(is);
                XLSXCovertCSVReader xlsx2csv = new XLSXCovertCSVReader(p, System.out, sheetName, minColumns);
                List<String[]> list = xlsx2csv.process();
                if (close) {
                    p.close();
                }
                return list;
            }

        }

        /**
         * 导入excel设置返回数据表头及数据列表
         * @param message 导入数据信息
         * @param excelFile 文件
         */
        public static void setImportExcelData(ImportExcelMessage message, File excelFile){
            Map<String, List<Map<String, String>>> titleMap = new HashMap<>();
            Map<String, List<Map<String, String>>> dataMap = new HashMap<>();
            for (String sheetName : message.getSheetNames()) {
                EasyExcelUtil.SheetData sheetData = EasyExcelUtil.readSheet(sheetName, null, excelFile);
                List<Map<String, String>> titleList = new ArrayList<>();
                for (String title : sheetData.getTitles()) {
                    Map<String, String> map = new HashMap<>();
                    map.put("title", title);
                    map.put("key", title);
                    titleList.add(map);
                }
                titleMap.put(sheetName, titleList);
                List<Map<String, String>> dataList = new ArrayList<>();
                //循环sheetData.getRows()前25条
                int index = Math.min(sheetData.getRows().size(), 25);
                for (int i = 0; i < index; i++) {
                    String[] dataRow = sheetData.getRows().get(i);
                    Map<String, String> map = new HashMap<>();
                    for (int j = 0; j < dataRow.length; j++) {
                        map.put(titleList.get(j).get("title"), dataRow[j]);
                    }
                    dataList.add(map);
                }
                dataMap.put(sheetName, dataList);
            }
            message.setTitleMap(titleMap);
            message.setDataMap(dataMap);
        }
    }
}
