#!/bin/bash
cd `dirname $0`
BIN_DIR=`pwd`
cd ..
DEPLOY_DIR=`pwd`
CONF_DIR=$DEPLOY_DIR/conf

MAINCLASS=com.uinnova.product.eam.EamApplication

PARAMTER="--spring.profiles.active=local"

PIDS=`ps -f | grep java | grep "$CONF_DIR" |awk '{print $2}'`


LOGS_DIR=$DEPLOY_DIR/logs
if [ ! -d $LOGS_DIR ]; then
    mkdir $LOGS_DIR
fi
STDOUT_FILE=$LOGS_DIR/register.log

LIB_DIR=$DEPLOY_DIR/lib
LIB_JARS=$DEPLOY_DIR/lib/*

JAVA_OPTS=" -Djava.awt.headless=true -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=../logs/jvmErrorDump.log"
# 添加JDK 17模块访问权限
JAVA_MODULE_OPTS="--add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED"

JAVA_DEBUG_OPTS=""
if [ "$1" = "debug" ]; then
    JAVA_DEBUG_OPTS=" -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:8000 "
fi
JAVA_JMX_OPTS=""
if [ "$1" = "jmx" ]; then
    JAVA_JMX_OPTS=" -Dcom.sun.management.jmxremote.port=1099 -Dcom.sun.management.jmxremote.ssl=false -Dcom.sun.management.jmxremote.authenticate=false "
fi
JAVA_MEM_OPTS=""
BITS=`java -version 2>&1 | grep -i 64-bit`
if [ -n "$BITS" ]; then
    # 使用G1GC作为默认垃圾收集器，适合JDK 17
    JAVA_MEM_OPTS=" -server -Xmx2048m -Xms2048m -XX:MaxMetaspaceSize=512m -XX:+UseG1GC -XX:+DisableExplicitGC -XX:G1HeapRegionSize=4M -XX:InitiatingHeapOccupancyPercent=30 -XX:G1ReservePercent=15 "
else
    JAVA_MEM_OPTS=" -server -Xms1024m -Xmx1024m -XX:MaxMetaspaceSize=512m -XX:+UseG1GC "
fi


echo -e "Starting the application ...\c"

nohup java $JAVA_OPTS $JAVA_MEM_OPTS $JAVA_MODULE_OPTS $JAVA_DEBUG_OPTS $JAVA_JMX_OPTS -classpath $CONF_DIR:$LIB_DIR:$LIB_JARS $MAINCLASS $PARAMTER >> /dev/null 2>&1 &


echo "OK!"
PIDS=`ps -f | grep java | grep "$DEPLOY_DIR" | awk '{print $2}'`
echo "PID: $PIDS"
