package com.uino.util.sys;

import lombok.extern.slf4j.Slf4j;
import org.xhtmlrenderer.pdf.ITextRenderer;

import java.io.FileOutputStream;
import java.io.OutputStream;

/**
 * pdf工具类
 */
@Slf4j
public class PDFUtil {
    /**
     * html内容转化为pdf文件
     *
     * @param htmlContent   html文档内容
     * @param pdfOutputPath pdf输出地址
     */
    public static void html2Pdf(String htmlContent, String pdfOutputPath) {
        try (OutputStream os = new FileOutputStream(pdfOutputPath)) {
            ITextRenderer renderer = new ITextRenderer();
            renderer.setDocumentFromString(htmlContent);
            renderer.layout();
            renderer.createPDF(os);
        } catch (Exception e) {
            log.error("html to pdf error", e);
        }

    }
}
