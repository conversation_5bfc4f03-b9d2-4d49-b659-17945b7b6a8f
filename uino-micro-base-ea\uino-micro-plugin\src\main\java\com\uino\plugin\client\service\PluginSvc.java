package com.uino.plugin.client.service;

import com.uino.plugin.bean.OperatePluginDetails;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @Description
 * @Date 2021/9/26
 * @Version 1.0
 */
public interface PluginSvc {
    OperatePluginDetails uploadPlugin(MultipartFile file);

    OperatePluginDetails loadPlugin(String jarName);

    OperatePluginDetails unloadAndDeletePlugin(String jarName);

}
