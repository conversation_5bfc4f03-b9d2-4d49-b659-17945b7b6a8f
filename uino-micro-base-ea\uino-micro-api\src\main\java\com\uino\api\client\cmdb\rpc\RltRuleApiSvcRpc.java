package com.uino.api.client.cmdb.rpc;

import com.binary.jdbc.Page;
import com.uino.bean.cmdb.base.ESRltRuleInfo;
import com.uino.provider.feign.cmdb.RltRuleFeign;
import com.uino.api.client.cmdb.IRltRuleApiSvc;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @data 2019/8/1 10:10.
 */
@Service
public class RltRuleApiSvcRpc implements IRltRuleApiSvc {

    @Autowired
    private RltRuleFeign rltRuleFeign;

    @Override
    public Long saveOrUpdate(ESRltRuleInfo rltRuleInfo) {
        return rltRuleFeign.saveOrUpdate(rltRuleInfo);
    }

    @Override
    public Integer deleteById(Long id) {
        return rltRuleFeign.deleteById(id);
    }

    @Override
    public ESRltRuleInfo queryInfoById(Long id) {
        ESRltRuleInfo esRltRuleInfo = rltRuleFeign.queryInfoById(id);
        return esRltRuleInfo;
    }

    @Override
    public List<ESRltRuleInfo> queryInfo(Long domainId) {
        return rltRuleFeign.queryInfo(domainId);
    }

    @Override
    public Page<ESRltRuleInfo> queryInfoPage(Integer pageNum, Integer pageSize, String name, Long domainId, String orders, boolean isAsc) {
        return rltRuleFeign.queryInfoPage(pageNum, pageSize, name, domainId, orders, isAsc);
    }

}
