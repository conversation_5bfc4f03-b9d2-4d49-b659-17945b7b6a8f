package com.uinnova.product.eam.model.vo;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

/**
 * 系统分类定制查询
 */
@Data
public class AppSystemQueryVoV2 extends AppSystemQueryVo{

    @Comment("职责类型： 1：我负责的，2：我参与的")
    private Integer respType;

    @Comment("规则id")
    private Long appSquareConfigId;

    @Comment("层级属性名")
    private String levelAttrName;
    @Comment("层级属性值")
    private String levelValue;
}