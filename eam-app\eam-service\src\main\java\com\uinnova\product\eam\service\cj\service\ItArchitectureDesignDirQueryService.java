package com.uinnova.product.eam.service.cj.service;

import com.uinnova.product.eam.model.cj.domain.PlanDesignInstance;

import java.util.Collection;
import java.util.Map;

/**
 * IT架构设计文件夹查询service
 * <p>因为文件夹伏羲操作,所以只提供一个查询接口</p>
 *
 * <AUTHOR>
 */
public interface ItArchitectureDesignDirQueryService {

    /**
     * 获取一个map,key是文件夹Id,value表示文件夹是否存在,true存在,false不存在(被删除)
     *
     * @param planList 方案实例的List,方案的dirId为必填项
     * @param fromTemp 文件夹id是否从备份字段获取, 从回收站恢复时使用备份字段传入true, 删除到回收站时不使用传入false, 或者其他场景时传入false
     * @return key是文件夹Id, value表示文件夹是否存在, true存在, false不存在(被删除)
     */
    Map<Long, Boolean> getDirExistMap(Collection<PlanDesignInstance> planList, Boolean fromTemp);
}
