<?xml version="1.0" encoding="UTF-8"?><!--Converted at: Thu Sep 19 16:23:49 CST 2019-->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="VC_DIAGRAM_ELE">


	<resultMap id="queryResult" type="com.uinnova.product.eam.comm.model.VcDiagramEle">
		<result property="id" column="ID" jdbcType="BIGINT"/>	<!-- ID -->
		<result property="diagramId" column="DIAGRAM_ID" jdbcType="BIGINT"/>	<!-- 视图ID -->
		<result property="eleType" column="ELE_TYPE" jdbcType="INTEGER"/>	<!-- 元素类型 -->
		<result property="eleId" column="ELE_ID" jdbcType="VARCHAR"/>	<!-- 元素ID -->
		<result property="eleCode" column="ELE_CODE" jdbcType="VARCHAR"/>	<!-- 元素ciCode -->
		<result property="rltDiagramIds" column="RLT_DIAGRAM_IDS" jdbcType="VARCHAR"/>	<!-- 关联视图 -->
		<result property="domainId" column="DOMAIN_ID" jdbcType="BIGINT"/>	<!-- 所属域 -->
		<result property="createTime" column="CREATE_TIME" jdbcType="BIGINT"/>	<!-- 创建时间 -->
		<result property="modifyTime" column="MODIFY_TIME" jdbcType="BIGINT"/>	<!-- 修改时间 -->
	</resultMap>
	

	<sql id="sql_query_where">
		<if test="cdt != null and cdt.id != null">and
			ID = #{cdt.id:BIGINT}
		</if>
		<if test="ids != null and ids != ''">and
			ID in (${ids})
		</if>
		<if test="cdt != null and cdt.startId != null">and
			 ID &gt;= #{cdt.startId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endId != null">and
			 ID &lt;= #{cdt.endId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.diagramId != null">and
			DIAGRAM_ID = #{cdt.diagramId:BIGINT}
		</if>
		<if test="diagramIds != null and diagramIds != ''">and
			DIAGRAM_ID in (${diagramIds})
		</if>
		<if test="cdt != null and cdt.startDiagramId != null">and
			 DIAGRAM_ID &gt;= #{cdt.startDiagramId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endDiagramId != null">and
			 DIAGRAM_ID &lt;= #{cdt.endDiagramId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.eleType != null">and
			ELE_TYPE = #{cdt.eleType:INTEGER}
		</if>
		<if test="eleTypes != null and eleTypes != ''">and
			ELE_TYPE in (${eleTypes})
		</if>
		<if test="cdt != null and cdt.startEleType != null">and
			 ELE_TYPE &gt;= #{cdt.startEleType:INTEGER} 
		</if>
		<if test="cdt != null and cdt.endEleType != null">and
			 ELE_TYPE &lt;= #{cdt.endEleType:INTEGER} 
		</if>
		<if test="cdt != null and cdt.eleId != null and cdt.eleId != ''">and
			ELE_ID like #{cdt.eleId,jdbcType=VARCHAR} 
		</if>
		<if test="cdt != null and cdt.eleIdEqual != null and cdt.eleIdEqual != ''">and
			ELE_ID = #{cdt.eleIdEqual,jdbcType=VARCHAR}
		</if>
		<if test="eleIds != null and eleIds != ''">and
			ELE_ID in (${eleIds})
		</if>
		<if test="cdt != null and cdt.eleCode != null and cdt.eleCode != ''">and
			ELE_CODE like #{cdt.eleCode,jdbcType=VARCHAR} 
		</if>
		<if test="cdt != null and cdt.eleCodeEqual != null and cdt.eleCodeEqual != ''">and
			ELE_CODE = #{cdt.eleCodeEqual,jdbcType=VARCHAR}
		</if>
		<if test="eleCodes != null and eleCodes != ''">and
			ELE_CODE in (${eleCodes})
		</if>
		<if test="cdt != null and cdt.rltDiagramIds != null and cdt.rltDiagramIds != ''">and
			RLT_DIAGRAM_IDS like #{cdt.rltDiagramIds,jdbcType=VARCHAR} 
		</if>
		<if test="cdt != null and cdt.domainId != null">and
			DOMAIN_ID = #{cdt.domainId:BIGINT}
		</if>
		<if test="domainIds != null and domainIds != ''">and
			DOMAIN_ID in (${domainIds})
		</if>
		<if test="cdt != null and cdt.startDomainId != null">and
			 DOMAIN_ID &gt;= #{cdt.startDomainId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endDomainId != null">and
			 DOMAIN_ID &lt;= #{cdt.endDomainId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.createTime != null">and
			CREATE_TIME = #{cdt.createTime:BIGINT}
		</if>
		<if test="createTimes != null and createTimes != ''">and
			CREATE_TIME in (${createTimes})
		</if>
		<if test="cdt != null and cdt.startCreateTime != null">and
			 CREATE_TIME &gt;= #{cdt.startCreateTime:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endCreateTime != null">and
			 CREATE_TIME &lt;= #{cdt.endCreateTime:BIGINT} 
		</if>
		<if test="cdt != null and cdt.modifyTime != null">and
			MODIFY_TIME = #{cdt.modifyTime:BIGINT}
		</if>
		<if test="modifyTimes != null and modifyTimes != ''">and
			MODIFY_TIME in (${modifyTimes})
		</if>
		<if test="cdt != null and cdt.startModifyTime != null">and
			 MODIFY_TIME &gt;= #{cdt.startModifyTime:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endModifyTime != null">and
			 MODIFY_TIME &lt;= #{cdt.endModifyTime:BIGINT} 
		</if>
	</sql>
	

	<sql id="sql_update_columns">
		<if test="record != null and record.id != null"> 
			ID = #{record.id:BIGINT}
		,</if>
		<if test="record != null and record.diagramId != null"> 
			DIAGRAM_ID = #{record.diagramId:BIGINT}
		,</if>
		<if test="record != null and record.eleType != null"> 
			ELE_TYPE = #{record.eleType:INTEGER}
		,</if>
		<if test="record != null and record.eleId != null"> 
			ELE_ID = #{record.eleId,jdbcType=VARCHAR}
		,</if>
		<if test="record != null and record.eleCode != null"> 
			ELE_CODE = #{record.eleCode,jdbcType=VARCHAR}
		,</if>
		<if test="record != null and record.rltDiagramIds != null"> 
			RLT_DIAGRAM_IDS = #{record.rltDiagramIds,jdbcType=VARCHAR}
		,</if>
		<if test="record != null and record.domainId != null"> 
			DOMAIN_ID = #{record.domainId:BIGINT}
		,</if>
		<if test="record != null and record.createTime != null"> 
			CREATE_TIME = #{record.createTime:BIGINT}
		,</if>
		<if test="record != null and record.modifyTime != null"> 
			MODIFY_TIME = #{record.modifyTime:BIGINT}
		,</if>
	</sql>
	

	<sql id="sql_query_columns">
		ID, DIAGRAM_ID, ELE_TYPE, ELE_ID, ELE_CODE, RLT_DIAGRAM_IDS, 
		DOMAIN_ID, CREATE_TIME, MODIFY_TIME
	</sql>
	

	

	<select id="selectList" parameterType="java.util.Map" resultMap="queryResult">
		select 
			<include refid="VC_DIAGRAM_ELE.sql_query_columns"/>
		from VC_DIAGRAM_ELE 
			<where>
				<include refid="VC_DIAGRAM_ELE.sql_query_where"/>
			</where>
		order by 
			<if test="orders != null and orders != ''">
				${orders}
			</if>
			<if test="orders == null or orders == ''">
				ID
			</if>
	</select>
	<select id="selectCount" parameterType="java.util.Map" resultType="java.lang.Long">
		select count(1) from VC_DIAGRAM_ELE 
			<where>
				<include refid="VC_DIAGRAM_ELE.sql_query_where"/>
			</where>
	</select>
	<select id="selectById" parameterType="java.util.Map" resultMap="queryResult">
		select 
			<include refid="VC_DIAGRAM_ELE.sql_query_columns"/>
		from VC_DIAGRAM_ELE where ID=#{id:BIGINT} 
	</select>
	

	

	<insert id="insert" parameterType="java.util.Map">
		insert into VC_DIAGRAM_ELE(
			ID, DIAGRAM_ID, ELE_TYPE, ELE_ID, ELE_CODE, 
			RLT_DIAGRAM_IDS, DOMAIN_ID, CREATE_TIME, MODIFY_TIME)
		values (
			#{record.id:BIGINT}, #{record.diagramId:BIGINT}, #{record.eleType:INTEGER}, #{record.eleId,jdbcType=VARCHAR}, #{record.eleCode,jdbcType=VARCHAR}, 
			#{record.rltDiagramIds,jdbcType=VARCHAR}, #{record.domainId:BIGINT}, #{record.createTime:BIGINT}, #{record.modifyTime:BIGINT})
	</insert>
	

	

	<update id="updateById" parameterType="java.util.Map">
		update VC_DIAGRAM_ELE
			<set> 
				<include refid="VC_DIAGRAM_ELE.sql_update_columns"/> 
			</set>
		where ID = #{id:BIGINT}
	</update>
	<update id="updateByCdt" parameterType="java.util.Map">
		update VC_DIAGRAM_ELE
			<set> 
				<include refid="VC_DIAGRAM_ELE.sql_update_columns"/> 
			</set>
			<where> 
				<include refid="VC_DIAGRAM_ELE.sql_query_where"/> 
			</where>
	</update>
	
	

	

	<delete id="deleteById" parameterType="java.util.Map">
		delete from VC_DIAGRAM_ELE where ID = #{id:BIGINT}
	</delete>
	<delete id="deleteByCdt" parameterType="java.util.Map">
		delete from VC_DIAGRAM_ELE
			<where> 
				<include refid="VC_DIAGRAM_ELE.sql_query_where"/> 
			</where>
	</delete>
	



</mapper>