package com.uino.service.cmdb.dataset.microservice.impl;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import com.binary.core.util.BinaryUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.binary.core.exception.MessageException;
import com.binary.jdbc.Page;
import com.google.common.collect.Lists;
import com.uinnova.product.vmdb.comm.model.ci.CCcCi;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClass;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.comm.model.rlt.CCcCiRlt;
import com.uinnova.product.vmdb.comm.util.PropertyType;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uinnova.product.vmdb.provider.search.bean.CcCiObj;
import com.uinnova.product.vmdb.provider.search.bean.CcCiSearchPage;
import com.uino.bean.chart.bean.UinoChartDataBean;
import com.uino.bean.chart.bean.UinoChartDataItem;
import com.uino.bean.chart.enums.UinoChartType;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.base.ESCIRltInfo;
import com.uino.bean.cmdb.base.dataset.DataSetMallApiRelationRule;
import com.uino.bean.cmdb.base.dataset.batch.SimpleFriendInfo;
import com.uino.bean.cmdb.base.dataset.chart.Chart;
import com.uino.bean.cmdb.base.dataset.chart.ChartEnum;
import com.uino.bean.cmdb.base.dataset.chart.StatisticalEnum;
import com.uino.bean.cmdb.business.dataset.FriendInfo;
import com.uino.bean.cmdb.business.dataset.QueryCondition;
import com.uino.bean.cmdb.business.dataset.RltRuleInfo;
import com.uino.bean.cmdb.business.dataset.RltRulePathData;
import com.uino.bean.cmdb.business.dataset.RltRuleTableData;
import com.uino.bean.cmdb.query.ESAttrBean;
import com.uino.bean.cmdb.query.ESCISearchBean;
import com.uino.bean.cmdb.query.ESRltSearchBean;
import com.uino.bean.permission.base.SysUser;
import com.uino.service.cmdb.dataset.microservice.IRelationRuleAnalysisBase;
import com.uino.service.cmdb.microservice.ICIClassSvc;
import com.uino.service.cmdb.microservice.ICIRltSvc;
import com.uino.service.cmdb.microservice.ICISvc;
import com.uino.service.cmdb.microservice.IRltClassSvc;
import com.uino.util.sys.CheckAttrUtil;
import com.uino.util.sys.SysUtil;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class RelationRuleAnalysisBase implements IRelationRuleAnalysisBase {

    @Autowired
    private ICIClassSvc iciClassSvc;

    @Autowired
    private IRltClassSvc iRltClassSvc;

    @Setter
    @Getter
    private class Restriction {
        // 存储筛选条件的数组
        private List<String> childIds;
        private Boolean isAnd;
    }

    @Override
    public Map<Long, List<QueryCondition>> findTravalTree(DataSetMallApiRelationRule relationRule) {
        RltRuleInfo rltRuleInfo = relationRule.convert2RuleInfo();
        return findTravalTree(rltRuleInfo);
    }

    public Map<Long, List<QueryCondition>> findTravalTree(RltRuleInfo rltRuleInfo) {
        //输入node图ruleInfo，ci图wholeRles，起始ci
        List<Map<Long, List<RltRuleInfo.LineInfo>>> upAndDownMap = this.generateUpAndDownRelMap(rltRuleInfo.getLines(), null);
        //遍历顺序规则
        return generateTravalTree(rltRuleInfo.getStartPageNodeId(), upAndDownMap.get(0), upAndDownMap.get(1),
                generateNodeMap(rltRuleInfo.getNodes(), null));
    }

    @Override
    public Map<Long, FriendInfo> queryCiFriendByCiIdsAndRule(List<CcCiInfo> sCis, DataSetMallApiRelationRule relationRule, boolean isIncludeAllStartCI, ICISvc iciSvc, ICIRltSvc iciRltSvc) {
        RltRuleInfo rltRuleInfo = relationRule.convert2RuleInfo();
        Map<Long, CcCiClassInfo> classInfo = findClassInfo(rltRuleInfo);
        return traversal(rltRuleInfo, sCis, classInfo, null, System.currentTimeMillis() + "", isIncludeAllStartCI, iciSvc, iciRltSvc);
    }

    @Override
    public Map<Long, FriendInfo> queryCiFriendByRule(DataSetMallApiRelationRule relationRule, ICISvc iciSvc, ICIRltSvc iciRltSvc) {
        Map<Long, FriendInfo> result = new HashMap<>();
        Long domainId = relationRule.getDomainId();
        RltRuleInfo rltRuleInfo = relationRule.convert2RuleInfo();
        Map<Long, CcCiClassInfo> classInfo = findClassInfo(rltRuleInfo);
        Long classId = relationRule.getClassId();
        CCcCi cdt = new CCcCi();
        cdt.setDomainId(domainId);
        int pageNum = 1;
        cdt.setClassIds(new Long[]{classId});
        while (true) {
            Page<CcCiInfo> page = iciSvc.queryCiInfoPage(relationRule.getDomainId(), pageNum, 2000, cdt, null, true, false);
            if (page.getData() != null) {
                Map<Long, FriendInfo> friendInfoMap = traversal(rltRuleInfo, page.getData(), classInfo, null, System.currentTimeMillis() + "", false,
                        iciSvc, iciRltSvc);
                result.putAll(friendInfoMap);
            }
            if (pageNum >= page.getTotalPages()) {
                break;
            }
            pageNum++;
        }
        return result;
    }

    @Override
    public Map<Long, FriendInfo> queryCiFriendByRuleWithLimit(DataSetMallApiRelationRule relationRule, Integer limit, ICISvc iciSvc, ICIRltSvc iciRltSvc) {
        Map<Long, FriendInfo> result = new HashMap<>();
        RltRuleInfo rltRuleInfo = relationRule.convert2RuleInfo();
        Map<Long, CcCiClassInfo> classInfo = findClassInfo(rltRuleInfo);
        Long classId = relationRule.getClassId();
        CCcCi cdt = new CCcCi();
        int pageNum = 1;
        cdt.setClassIds(new Long[]{classId});
        Page<CcCiInfo> page = iciSvc.queryCiInfoPage(relationRule.getDomainId(), pageNum, limit, cdt, null, true, false);
        if (page.getData() != null) {
            Map<Long, FriendInfo> friendInfoMap = traversal(rltRuleInfo, page.getData(), classInfo, null, System.currentTimeMillis() + "", false,
                    iciSvc, iciRltSvc);
            result.putAll(friendInfoMap);
        }
        return result;
    }

    @Override
    public FriendInfo mergeFriendInfoMap(Map<Long, FriendInfo> friendInfoMap) {
        FriendInfo result = new FriendInfo();

        Map<Long, ESCIRltInfo> resultLineMap = new HashMap<>();
        Map<Long, CcCiInfo> resultCiMap = new HashMap<>();
        Map<Long, Set<Long>> resultNodeMap = new HashMap<>();
        List<CcCiClassInfo> resultClassInfos = null;
        boolean b = true;
        for (FriendInfo friendInfo : friendInfoMap.values()) {
            if (b) {
                resultClassInfos = friendInfo.getCiClassInfos();
                b = false;
            }
            for (Long pageNodeId : friendInfo.getCiIdByNodeMap().keySet()) {
                if (resultNodeMap.get(pageNodeId) == null) {
                    resultNodeMap.put(pageNodeId, friendInfo.getCiIdByNodeMap().get(pageNodeId));
                } else {
                    resultNodeMap.get(pageNodeId).addAll(friendInfo.getCiIdByNodeMap().get(pageNodeId));
                }
            }

            for (ESCIRltInfo ciLine : friendInfo.getCiRltLines()) {
                resultLineMap.put(ciLine.getId(), ciLine);
            }

            for (CcCiInfo ciNode : friendInfo.getCiNodes()) {
                resultCiMap.put(ciNode.getCi().getId(), ciNode);
            }
        }
        List<CcCiInfo> resultCiList = new ArrayList<>();
        List<ESCIRltInfo> resultLineList = new ArrayList<>();
        for (Long id : resultCiMap.keySet()) {
            resultCiList.add(resultCiMap.get(id));
        }
        for (Long id : resultLineMap.keySet()) {
            resultLineList.add(resultLineMap.get(id));
        }
        result.setCiIdByNodeMap(resultNodeMap);
        result.setCiNodes(resultCiList);
        result.setCiRltLines(resultLineList);
        result.setCiClassInfos(resultClassInfos);
        return result;
    }

    @Override
    public RltRuleTableData disassembleFriendInfoDataByPath(List<CcCiInfo> sCis, DataSetMallApiRelationRule relationRule, ICISvc iciSvc, ICIRltSvc iciRltSvc) {
        Map<Long, FriendInfo> friendInfoMap = queryCiFriendByCiIdsAndRule(sCis, relationRule, false, iciSvc, iciRltSvc);
        RltRuleTableData rltRuleTableData = disassembleFriendInfoDataByPath(relationRule, new ArrayList<>(friendInfoMap.keySet()), mergeFriendInfoMap(friendInfoMap));
        return rltRuleTableData;
    }

    @Override
    public RltRuleTableData disassembleFriendInfoDataByPath(DataSetMallApiRelationRule relationRule, List<Long> ciIds, FriendInfo friendInfo) {
        return disassembleFriendInfoDataByPath(relationRule, ciIds, friendInfo, findTravalTree(relationRule), getClassChilders(relationRule.getDomainId()), false);
    }

    //***************************************************************************************
    //***************************************************************************************
    //***************************************************************************************

    /**
     * 将遍历结果按Sheet页进行拆分
     *
     * @param apiRelationRule
     * @param ciIds
     * @param friendInfo
     * @param travalTreeMap
     * @param classChilders      ci分类父子结构
     * @param isContainStartCiId 是否包含入口ci
     * @return
     */
    private RltRuleTableData disassembleFriendInfoDataByPath(DataSetMallApiRelationRule apiRelationRule,
                                                             List<Long> ciIds,
                                                             FriendInfo friendInfo,
                                                             Map<Long, List<QueryCondition>> travalTreeMap,
                                                             Map<Long, List<Long>> classChilders,
                                                             boolean isContainStartCiId) {
        //
        RltRuleInfo ruleInfo = apiRelationRule.convert2RuleInfo();

        List<CcCiClassInfo> classInfos;
        if (friendInfo == null) {
            classInfos = getClassInfoByRule(ruleInfo);
        } else {
            classInfos = friendInfo.getCiClassInfos();
        }
        Map<Long, RltRuleInfo.NodeInfo> nodeMap = this.generateNodeMap(ruleInfo.getNodes(), null);


        //最终ci路径
        Map<String, List<String>> ultimatelyPath = new LinkedHashMap<>();
        if (friendInfo != null) {
            Map<String, List<ESCIRltInfo>> ciLinesMap = getCiLinesMap(friendInfo.getCiRltLines());

            getCiLongPath(travalTreeMap, ciLinesMap, nodeMap, friendInfo.getCiIdByNodeMap(),
                    ruleInfo.getStartPageNodeId(), ciIds, new ArrayList<>(), ultimatelyPath, classChilders);
        }
        //节点对应名称
        Map<Long, String> nodeNameMap = new HashMap<>();
        //节点应显示属性字段
        Map<Long, List<String>> nodeAttrProNameMap = new HashMap<>();
        //ci中属性key存的大写，用于取值
        Map<Long, List<String>> nodeAttrProStdNameMap = new HashMap<>();
        //属性类型
        Map<Long, List<Integer>> proTypeMap = new HashMap<Long, List<Integer>>();
        //属性约束条件
        Map<Long, List<String>> constraintRuleMap = new HashMap<Long, List<String>>();
        getPreParamByRule(ruleInfo, classInfos, nodeNameMap, nodeAttrProNameMap, nodeAttrProStdNameMap, proTypeMap, constraintRuleMap);

        //返回结果
        RltRuleTableData rrtd = new RltRuleTableData();

        List<RltRulePathData> sheetList = new ArrayList<>();
        ultimatelyPath.forEach((nodePath, ciPath) -> {

            RltRulePathData rltRulePathData = new RltRulePathData();
            rltRulePathData.setNodePath(nodePath);
            //拼接头和数据占位
            List<Map<String, Object>> headers = new ArrayList<>();
            //节点对应的占位符
            Map<Long, List<Integer>> placeholder = new HashMap<>();
            int n = -1;
            for (String s : nodePath.split("-")) {
                long nodePageId = Long.parseLong(s);
                //拼接header
                Map<String, Object> header = new HashMap<>();
                header.put("className", nodeNameMap.get(nodePageId));
                header.put("classId", nodeMap.get(nodePageId).getNode().getClassId());
                header.put("labels", nodeAttrProNameMap.get(nodePageId));
                header.put("proTypes", proTypeMap.get(nodePageId));
                header.put("constraintRules", constraintRuleMap.get(nodePageId));
                headers.add(header);
                //分配占位
                List<Integer> location = new ArrayList<>();
                List<String> attr = nodeAttrProNameMap.get(nodePageId);
                for (int i = 1; i <= attr.size(); i++) {
                    location.add(++n);
                }
                placeholder.put(nodePageId, location);
            }

            //拼接每一条数据及每一条ci的属性
            Map<String, CcCiInfo> ciMap;
            if (friendInfo == null) {
                ciMap = new HashMap<>();
            } else {
                ciMap = getCIInfoMap(friendInfo.getCiNodes());
            }
            Map<String, List<String>> pathMap = new HashMap<>();
            pathMap.put(nodePath, ciPath);
            List<List<Object>> lineData = getLineData(pathMap, nodeAttrProStdNameMap, proTypeMap, constraintRuleMap,
                    placeholder, n, ciMap, isContainStartCiId);
            rltRulePathData.setData(lineData);
            rltRulePathData.setHeaders(headers);
            sheetList.add(rltRulePathData);
        });
        rrtd.setSheetList(sheetList);
        return rrtd;
    }

    /**
     * @param ultimatelyPath        分类路径对应ci路径  nodePageId：ciId
     * @param nodeAttrProStdNameMap
     * @param placeholder
     * @param rowSize               属性总个数(一行有多少个)
     * @param ciMap
     * @return
     */
    private List<List<Object>> getLineData(Map<String, List<String>> ultimatelyPath,
                                           Map<Long, List<String>> nodeAttrProStdNameMap, Map<Long, List<Integer>> proTypeMap, Map<Long, List<String>> constraintRuleMap,
                                           Map<Long, List<Integer>> placeholder, int rowSize, Map<String, CcCiInfo> ciMap, boolean isContainStartCiId) {
        List<List<Object>> datas = new ArrayList<>();
        //遍历分类路径
        for (Map.Entry<String, List<String>> entry : ultimatelyPath.entrySet()) {
            String[] nodePath = entry.getKey().split("-");
            //获取该路径的占位符
            List<List<Integer>> placeholderPath = new ArrayList<>();  //该路径节点对应的占位符
            for (int i = 0; i < nodePath.length; i++) {
                String nodePageId = nodePath[i];
                placeholderPath.add(placeholder.get(Long.parseLong(nodePageId)));
            }
            //遍历实际ci路径，拼接路径结果
            for (String s : entry.getValue()) {
                Map<Integer, Object> line = new HashMap<>();
                String[] ciIds = s.split("-");
                for (int i = 0; i < ciIds.length; i++) {
                    //获取该节点的占位
                    List<Integer> placeholderByNode = placeholderPath.get(i);
                    CcCiInfo ciInfo = ciMap.get(ciIds[i]);
                    if (ciInfo == null) {
                        log.error("此ci不存在，ciId为：" + ciIds[i]);
                    } else {
                        Map<String, String> attrs = ciInfo.getAttrs();
                        List<String> attrList = nodeAttrProStdNameMap.get(Long.parseLong(nodePath[i]));   //要展示的属性
                        List<Integer> proTypeList = proTypeMap.get(Long.parseLong(nodePath[i]));  //属性类型
                        List<String> constraintRuleList = constraintRuleMap.get(Long.parseLong(nodePath[i]));  //属性约束条件
                        for (int j = 0; j < attrList.size(); j++) {
                            Integer proTypeValue = proTypeList.get(j);
                            String constraintRule = constraintRuleList.get(j);
                            String attrValue = attrs.get(attrList.get(j));
                            if(BinaryUtils.isEmpty(attrValue)){
                                line.put(placeholderByNode.get(j), "");
                                continue;
                            }
                            PropertyType proType = PropertyType.valueOf(proTypeValue);
                            if (proType.equals(PropertyType.INTEGER)) { //整数
                                Long val = null;
                                try {
                                    val = Long.parseLong(attrValue);
                                } catch (NumberFormatException e) {
									throw new MessageException(e.getMessage());
                                }
                                line.put(placeholderByNode.get(j), val);
                            } else if (proType.equals(PropertyType.DOUBLE)) { //小数
                                Double val = null;
                                try {
                                    val = Double.parseDouble(attrValue);
                                } catch (NumberFormatException e) {
									throw new MessageException(e.getMessage());
                                }
                                line.put(placeholderByNode.get(j), val);
                            } else if (proType.equals(PropertyType.DATE)) { //日期
                                Long timestamp = null;
                                List<String> sdfList = new ArrayList<String>();
                                if (constraintRule != null && !"".equals(constraintRule.trim())) {
                                    sdfList.add(constraintRule.trim());
                                } else {
                                    sdfList.addAll(CheckAttrUtil.getDefaultRuleByType(PropertyType.DATE.getValue()));
                                }
                                for (String sdfFormat : sdfList) {
                                    try {
                                        SimpleDateFormat sdf = new SimpleDateFormat(sdfFormat);
                                        timestamp = sdf.parse(attrValue.trim()).getTime();
                                        break;
                                    } catch (ParseException e) {
										throw new MessageException(e.getMessage());
                                    }
                                }
                                line.put(placeholderByNode.get(j), timestamp);
                            } else { //string
                                line.put(placeholderByNode.get(j), attrValue);
                            }
                        }
                    }
                }
                //没有属性的替补空值
                List<Object> data = new ArrayList<>();
                //第一个值放入口ciId
                if (isContainStartCiId) {
                    data.add(ciIds[0]);
                }
                for (int k = 0; k <= rowSize; k++) {
                    data.add(line.get(k));
                }
                datas.add(data);
            }
        }

        return datas;
    }

    /**
     * 拼接节点展示顺序和ci路径顺序
     *
     * @param travalTreeMap        节点关系
     * @param ciLinesMap           ci关系字典
     * @param nodeMap              节点id对应的分类
     * @param ciIdByNodeMap        节点对应的ci
     * @param startPageNodeId      入口节点
     * @param showOrderNodePageIds 节点顺序
     * @param ultimatelyPath       最终路径     规则路径对应ci路径
     * @param classChilders        构建父分类对应的子分类
     */
    private void getCiLongPath(Map<Long, List<QueryCondition>> travalTreeMap,
                               Map<String, List<ESCIRltInfo>> ciLinesMap,
                               Map<Long, RltRuleInfo.NodeInfo> nodeMap,
                               Map<Long, Set<Long>> ciIdByNodeMap,
                               Long startPageNodeId,
                               List<Long> startciIds,
                               List<Long> showOrderNodePageIds,
                               Map<String, List<String>> ultimatelyPath,
                               Map<Long, List<Long>> classChilders) {
        //非最长路径
        Map<String, List<String>> notLongPath = new HashMap<>();
        //中转路径
        Map<String, List<String>> transitionPath = new LinkedHashMap<>();
        List<String> startPath = new ArrayList<>();
        startciIds.forEach(startciId -> startPath.add(String.valueOf(startciId)));
        transitionPath.put(startPageNodeId + "", startPath);

        Set<Long> foundLinetIds = new HashSet<>();
        LinkedList<Long> queue = new LinkedList<>();
        queue.addFirst(startPageNodeId);
        while (!queue.isEmpty()) {
            Long startPageId = queue.removeLast();
            if (!showOrderNodePageIds.contains(startPageId)) {
                showOrderNodePageIds.add(startPageId);
            }

            if (travalTreeMap.containsKey(startPageId)) {
                List<QueryCondition> validList = new ArrayList<>(); //有效关系
                for (QueryCondition qc : travalTreeMap.get(startPageId)) {
                    if (!foundLinetIds.contains(qc.getPageId())) {
                        validList.add(qc);
                        Boolean direction_equals_down = startPageId.equals(qc.getStartPageId());//判断是否下行
                        Long nextId = direction_equals_down ? qc.getEndPageId() : qc.getStartPageId();
                        foundLinetIds.add(qc.getPageId());
                        queue.addFirst(nextId);
                    }
                }
                //************拼接路径************
                if (!validList.isEmpty()) {
                    /*
                    遍历现有路径查看那些路径的结尾节点是当前的宿主节点，
                    如果是，并且有ci关系，就拼接宿主节点和从节点生成新的路径，放入newTransitionPath中，
                    宿主节点的ci如果没有下一级则存入最终路径中，
                    最后删除以宿主节点为结尾的路径
                     */
                    //处理过的路径待会清除
                    List<String> findPath = new ArrayList<>();

                    //新的（深一层）路径和对应的值
                    Map<String, List<String>> newTransitionPath = new HashMap<>();
                    for (Map.Entry<String, List<String>> entry : transitionPath.entrySet()) {
                        //符合规则路径则拼接ci路径
                        if (entry.getKey().contains("-") ? entry.getKey().endsWith("-" + startPageId.toString()) : entry.getKey().equals(startPageId.toString())) {
                            findPath.add(entry.getKey());
                            List<String> ciPathList = entry.getValue(); //ci路径
                            List<String> findCiPathList = new ArrayList<>(); //已经找到下一级ci的路径
                            for (QueryCondition qc : validList) {
                                Boolean direction_equals_down = startPageId.equals(qc.getStartPageId());//判断是否下行
                                //查询符合要求的关系线
                                Map<String, List<String>> legalCiMap = getLegalCiRlt(ciLinesMap, nodeMap, ciIdByNodeMap, qc, direction_equals_down, classChilders);
                                if (!legalCiMap.isEmpty()) {
                                    //深一层路径的ci路径
                                    List<String> nextCiPathList = new ArrayList<>();
                                    for (Map.Entry<String, List<String>> listEntry : legalCiMap.entrySet()) {
                                        String key = listEntry.getKey();
                                        List<String> value = listEntry.getValue();
                                        for (String path : ciPathList) {
                                            if (path.endsWith(key)) {
                                                findCiPathList.add(path);
                                                for (String aLong : value) {
                                                    nextCiPathList.add(path + "-" + aLong);
                                                }
                                            }
                                        }
                                    }
                                    newTransitionPath.put(entry.getKey() + "-" + (direction_equals_down ? qc.getEndPageId() : qc.getStartPageId()), nextCiPathList);

                                } else {
                                    newTransitionPath.put(entry.getKey() + "-" + (direction_equals_down ? qc.getEndPageId() : qc.getStartPageId()), new ArrayList<>());
                                }

                            }
                            //清除已经找到下级的，剩下的为最终路径
                            ciPathList.removeAll(findCiPathList);
                        }
                    }
                    //清除路径
                    for (String s : findPath) {
                        List<String> list = transitionPath.get(s);
                        if (list != null && !list.isEmpty()) {
                            notLongPath.put(s, list);
                        }
                        transitionPath.remove(s);
                    }
                    transitionPath.putAll(newTransitionPath);
                }

                //************拼接路径************
            }
        }

        //处理不完全ci路径,transitionPath最终为完整路径
        transitionPath.forEach((ultimatelyNodePath, ultimatelyCiPath) -> {
            for (String notLongNodePath : notLongPath.keySet()) {
                //0-14-11   0-1-
                if (ultimatelyNodePath.contains(notLongNodePath + "-")) {
                    List<String> notLongCiPath = notLongPath.get(notLongNodePath);
                    ultimatelyCiPath.addAll(notLongCiPath);
                }
            }
            if (!ultimatelyCiPath.isEmpty()) {
                ultimatelyPath.put(ultimatelyNodePath, ultimatelyCiPath);
            }
        });

    }

    /**
     * 拼接节点展示顺序和ci路径顺序
     *
     * @param travalTreeMap        节点关系
     * @param ciLinesMap           ci关系字典
     * @param nodeMap              节点id对应的分类
     * @param ciNodeMap            节点对应的ci
     * @param startPageNodeId      入口节点
     * @param showOrderNodePageIds 节点顺序
     * @param ultimatelyPath       最终路径     规则路径对应ci路径
     * @param classChilders        构建父分类对应的子分类
     */
    private void getCiAllPath(Map<Long, List<QueryCondition>> travalTreeMap,
                              Map<String, List<ESCIRltInfo>> ciLinesMap,
                              Map<Long, RltRuleInfo.NodeInfo> nodeMap,
                              Map<Long, Set<Long>> ciNodeMap,
                              Long startPageNodeId,
                              List<String> startPath,
                              List<Long> showOrderNodePageIds,
                              Map<String, List<String>> ultimatelyPath,
                              Map<Long, List<Long>> classChilders) {
        //中转路径
        Map<String, List<String>> transitionPath = new LinkedHashMap<>();
//        List<String> startPath = new ArrayList<>();
//        startPath.add(String.valueOf(startciId));
        transitionPath.put(startPageNodeId + "", startPath);

        Set<Long> foundLinetIds = new HashSet<>();
        LinkedList<Long> queue = new LinkedList<>();
        queue.addFirst(startPageNodeId);
        while (!queue.isEmpty()) {
            Long startPageId = queue.removeLast();
            if (!showOrderNodePageIds.contains(startPageId)) {
                showOrderNodePageIds.add(startPageId);
            }

            if (travalTreeMap.containsKey(startPageId)) {
                List<QueryCondition> validList = new ArrayList<>(); //有效关系
                for (QueryCondition qc : travalTreeMap.get(startPageId)) {
                    if (!foundLinetIds.contains(qc.getPageId())) {
                        validList.add(qc);
                        Boolean direction_equals_down = startPageId.equals(qc.getStartPageId());//判断是否下行
                        Long nextId = direction_equals_down ? qc.getEndPageId() : qc.getStartPageId();
                        foundLinetIds.add(qc.getPageId());
                        queue.addFirst(nextId);
                    }
                }
                //************拼接路径************
                if (!validList.isEmpty()) {
                    /*
                    遍历现有路径查看那些路径的结尾节点是当前的宿主节点，
                    如果是，并且有ci关系，就拼接宿主节点和从节点生成新的路径，放入newTransitionPath中，
                    宿主节点的ci如果没有下一级则存入最终路径中，
                    最后删除以宿主节点为结尾的路径
                     */
                    //处理过的路径待会清除
                    List<String> findPath = new ArrayList<>();

                    //新的（深一层）路径和对应的值
                    Map<String, List<String>> newTransitionPath = new HashMap<>();
                    for (Map.Entry<String, List<String>> entry : transitionPath.entrySet()) {
                        //符合规则路径则拼接ci路径
//                        if (entry.getKey().endsWith(startPageId.toString())) {
                        if (entry.getKey().contains("-") ? entry.getKey().endsWith("-" + startPageId.toString()) : entry.getKey().equals(startPageId.toString())) {
                            findPath.add(entry.getKey());
                            List<String> ciPathList = entry.getValue(); //ci路径
                            List<String> findCiPathList = new ArrayList<>(); //已经找到下一级ci的路径
                            for (QueryCondition qc : validList) {
                                Boolean direction_equals_down = startPageId.equals(qc.getStartPageId());//判断是否下行
                                //查询符合要求的关系线
                                Map<String, List<String>> legalCiMap = getLegalCiRlt(ciLinesMap, nodeMap, ciNodeMap, qc, direction_equals_down, classChilders);
                                if (!legalCiMap.isEmpty()) {
                                    //深一层路径的ci路径
                                    List<String> nextCiPathList = new ArrayList<>();
                                    for (Map.Entry<String, List<String>> listEntry : legalCiMap.entrySet()) {
                                        String key = listEntry.getKey();
                                        List<String> value = listEntry.getValue();
                                        for (String path : ciPathList) {
                                            if (path.endsWith(key)) {
                                                findCiPathList.add(path);
                                                for (String aLong : value) {
                                                    nextCiPathList.add(path + "-" + aLong);
                                                }
                                            }
                                        }
                                    }
                                    newTransitionPath.put(entry.getKey() + "-" + (direction_equals_down ? qc.getEndPageId() : qc.getStartPageId()), nextCiPathList);

                                }

                            }
                            //清除已经找到下级的，剩下的为最终路径
                            ciPathList.removeAll(findCiPathList);
                        }
                    }


                    //清除路径
                    for (String s : findPath) {
                        List<String> list = transitionPath.get(s);
                        if (list != null && !list.isEmpty()) {
                            ultimatelyPath.put(s, list);
                        }
                        transitionPath.remove(s);
                    }
                    transitionPath.putAll(newTransitionPath);
                }

                //************拼接路径************
            }
        }
        ultimatelyPath.putAll(transitionPath);
    }


    /**
     * ci字典
     *
     * @param ciNodes
     * @return
     */
    private Map<String, CcCiInfo> getCIInfoMap(List<CcCiInfo> ciNodes) {
        Map<String, CcCiInfo> ciMap = new HashMap<>();
        for (CcCiInfo ciNode : ciNodes) {
            ciMap.put(String.valueOf(ciNode.getCi().getId()), ciNode);
        }
        return ciMap;
    }

    /**
     * 获取头字段
     *
     * @param ruleInfo
     * @param classInfos
     * @param nodeNameMap           分类节点编号对应节点名称
     * @param nodeAttrProNameMap    分类节点编号对应显示属性名称
     * @param nodeAttrProStdNameMap 分类节点编号对应显示属性名称
     */
    private void getPreParamByRule(RltRuleInfo ruleInfo,
                                   List<CcCiClassInfo> classInfos,
                                   Map<Long, String> nodeNameMap,
                                   Map<Long, List<String>> nodeAttrProNameMap,
                                   Map<Long, List<String>> nodeAttrProStdNameMap,
                                   Map<Long, List<Integer>> proTypeMap,
                                   Map<Long, List<String>> constraintRuleMap) {

        Map<Long, CcCiClassInfo> classInfoMap = new HashMap<>();
        for (CcCiClassInfo classInfo : classInfos) {
            classInfoMap.put(classInfo.getCiClass().getId(), classInfo);
        }
        for (RltRuleInfo.NodeInfo nodeInfo : ruleInfo.getNodes()) {
            RltRuleInfo.Node node = nodeInfo.getNode();
            if (node != null && node.getNodeType() == 1) {
                nodeNameMap.put(node.getPageNodeId(), classInfoMap.get(node.getClassId()).getCiClass().getClassName());
//                nodeNameMap.put(node.getPageNodeId(), String.valueOf(node.getNodeClassId()));
                if (node.getReturns() != null) {
                    List<CcCiAttrDef> ciAttrDefList = JSONArray.parseArray(node.getReturns(), CcCiAttrDef.class);
                    List<String> AttrProNames = new ArrayList<>();
                    List<String> AttrProStdNames = new ArrayList<>();
                    List<Integer> proTypes = new ArrayList<>();
                    List<String> constraintRules = new ArrayList<>();
                    for (CcCiAttrDef ccCiAttrDef : ciAttrDefList) {
                        AttrProNames.add(ccCiAttrDef.getProName());
                        AttrProStdNames.add(ccCiAttrDef.getProStdName());
                        proTypes.add(ccCiAttrDef.getProType());
                        constraintRules.add(ccCiAttrDef.getConstraintRule());
                    }
                    nodeAttrProNameMap.put(node.getPageNodeId(), AttrProNames);
                    nodeAttrProStdNameMap.put(node.getPageNodeId(), AttrProStdNames);
                    proTypeMap.put(node.getPageNodeId(), proTypes);
                    constraintRuleMap.put(node.getPageNodeId(), constraintRules);
                }
            }
        }

    }

    /**
     * 查询符合要求的ci数据，以主节点的ciid为key
     *
     * @param ciLinesMap
     * @param nodeMap
     * @param ciNodeMap
     * @param qc
     * @param direction_equals_down true：主节点为入口
     * @return 宿主ci对应连接节点的ci
     */
    private Map<String, List<String>> getLegalCiRlt(Map<String, List<ESCIRltInfo>> ciLinesMap,
                                                    Map<Long, RltRuleInfo.NodeInfo> nodeMap,
                                                    Map<Long, Set<Long>> ciNodeMap,
                                                    QueryCondition qc,
                                                    Boolean direction_equals_down,
                                                    Map<Long, List<Long>> classChilders) {
        Map<String, List<String>> legalRltInfoMap = new HashMap<>();
        Long startClassId = nodeMap.get(qc.getStartPageId()).getNode().getClassId();
        Long endClassId = nodeMap.get(qc.getEndPageId()).getNode().getClassId();
        List<ESCIRltInfo> allRltInfos = new ArrayList<>();
        List<Long> startChilders = classChilders.get(startClassId);
        List<Long> endChilders = classChilders.get(endClassId);
        //父子匹配
        if (startChilders != null) {
            for (Long startChilder : startChilders) {
                List<ESCIRltInfo> childerRltLines = ciLinesMap.get(startChilder + "-" + qc.getRltClsId() + "-" + endClassId);
                if (childerRltLines != null) {
                    allRltInfos.addAll(childerRltLines);
                }
            }
        }
        if (endChilders != null) {
            for (Long endChilder : endChilders) {
                List<ESCIRltInfo> childerRltLines = ciLinesMap.get(startClassId + "-" + qc.getRltClsId() + "-" + endChilder);
                if (childerRltLines != null) {
                    allRltInfos.addAll(childerRltLines);
                }
            }
        }
        //子子匹配
        if (startChilders != null && endChilders != null) {
            for (Long startChilder : startChilders) {
                for (Long endChilder : endChilders) {
                    List<ESCIRltInfo> childerRltLines = ciLinesMap.get(startChilder + "-" + qc.getRltClsId() + "-" + endChilder);
                    if (childerRltLines != null) {
                        allRltInfos.addAll(childerRltLines);
                    }
                }
            }
        }
        //父父匹配
        List<ESCIRltInfo> rltInfos = ciLinesMap.get(startClassId + "-" + qc.getRltClsId() + "-" + endClassId);
        if (rltInfos != null) {
            allRltInfos.addAll(rltInfos);
        }


        if (!allRltInfos.isEmpty()) {
            for (ESCIRltInfo rltInfo : allRltInfos) {
                Long sourceCiId = rltInfo.getSourceCiId();
                Long targetCiId = rltInfo.getTargetCiId();
                //判断该ci关系线是否是复合这两个节点（通过ci是否属于该节点来判断）
                if (ciNodeMap.get(qc.getStartPageId()).contains(sourceCiId) && ciNodeMap.get(qc.getEndPageId()).contains(targetCiId)) {
                    String legalRltInfoMapKey = String.valueOf(direction_equals_down ? sourceCiId : targetCiId);
                    List<String> nextCiId = legalRltInfoMap.get(legalRltInfoMapKey);
                    if (nextCiId == null) {
                        nextCiId = new ArrayList<>();
                        nextCiId.add(String.valueOf(direction_equals_down ? targetCiId : sourceCiId));
                        legalRltInfoMap.put(legalRltInfoMapKey, nextCiId);
                    } else {
                        nextCiId.add(String.valueOf(direction_equals_down ? targetCiId : sourceCiId));
                    }
                }
            }
        }

        return legalRltInfoMap;
    }

    /**
     * key:开始分类_关系分类_结束分类
     *
     * @param ciLines
     * @return
     */
    private Map<String, List<ESCIRltInfo>> getCiLinesMap(List<ESCIRltInfo> ciLines) {
        Map<String, List<ESCIRltInfo>> ciLinesMap = new HashMap<>();
        for (ESCIRltInfo ciLine : ciLines) {
            Long classId = ciLine.getClassId();
            Long sourceClassId = ciLine.getSourceClassId();
            Long targetClassId = ciLine.getTargetClassId();
            String key = sourceClassId + "-" + classId + "-" + targetClassId;
            List<ESCIRltInfo> esciRltInfos = ciLinesMap.get(key);
            if (esciRltInfos == null) {
                esciRltInfos = new ArrayList<>();
                esciRltInfos.add(ciLine);
                ciLinesMap.put(key, esciRltInfos);
            } else {
                esciRltInfos.add(ciLine);
            }
        }
        return ciLinesMap;
    }

    /**
     * 获取关系遍历所有分类信息（分类和关系）
     *
     * @param rule
     * @return
     */
    private List<CcCiClassInfo> getClassInfoByRule(RltRuleInfo rule) {
        List<CcCiClassInfo> result = new ArrayList<>();
        //分类
        Set<Long> ciClassIds = new HashSet<>();
        rule.getNodes().forEach(nodeInfo -> {
            RltRuleInfo.Node node = nodeInfo.getNode();
            if (node != null && node.getNodeType() == 1) {
                Long categoryId = node.getClassId();
                ciClassIds.add(categoryId);
            }
        });
        CCcCiClass ciClassCdt = new CCcCiClass();
        ciClassCdt.setIds(ciClassIds.toArray(new Long[0]));
        ciClassCdt.setDomainId(rule.getDomainId());
        List<CcCiClassInfo> ccCiClassInfos = iciClassSvc.queryClassByCdt(ciClassCdt);
        result.addAll(ccCiClassInfos);
        //关系
        Set<Long> rltClassIds = new HashSet<>();
        rule.getLines().forEach(lineInfo -> {
            RltRuleInfo.Line line = lineInfo.getLine();
            if (line != null && line.getLineType() == 1) {
                Long categoryId = line.getClsRltId();
                rltClassIds.add(categoryId);
            }
        });
        CCcCiClass rltClassCdt = new CCcCiClass();
        rltClassCdt.setDomainId(rule.getDomainId());
        rltClassCdt.setIds(rltClassIds.toArray(new Long[0]));
        List<CcCiClassInfo> rltClassByCdt = iRltClassSvc.getRltClassByCdt(rltClassCdt);
        result.addAll(rltClassByCdt);
        return result;
    }

    /**
     * 获取子分类
     *
     * @return 子分类Map
     */
    private Map<Long, List<Long>> getClassChilders(Long domainId) {
        CCcCiClass ciClassCdt = new CCcCiClass();
        ciClassCdt.setDomainId(domainId);
        List<CcCiClassInfo> ccCiClassInfos = iciClassSvc.queryClassByCdt(ciClassCdt);
        Map<Long, List<Long>> classChilders = new HashMap<>();
        for (CcCiClassInfo classInfo : ccCiClassInfos) {
            if (!classInfo.getCiClass().getParentId().equals(0L)) {
                List<Long> childers = classChilders.get(classInfo.getCiClass().getParentId());
                if (childers == null) {
                    childers = new ArrayList<>();
                    classChilders.put(classInfo.getCiClass().getParentId(), childers);
                }
                childers.add(classInfo.getCiClass().getId());
            }
        }
        return classChilders;
    }


    @Override
    public JSONObject countStatistics(DataSetMallApiRelationRule dataSetMallRelationApi, Map<Long, SimpleFriendInfo> simpleFriendInfoMap, Chart chart, ICISvc iciSvc, ICIRltSvc iciRltSvc) {
        Map<String, Map<String, String>> ciAttrMap = new HashMap<>();
        Map<String, Map<String, List<String>>> statisticsCi = filterPath(dataSetMallRelationApi, simpleFriendInfoMap, ciAttrMap, chart, iciSvc, iciRltSvc);
        JSONObject jsonObject = null;
        if (ChartEnum.HISTOGRAM.getValue() == chart.getChartType()) {
            jsonObject = coutHistogram(statisticsCi, ciAttrMap, chart);
        } else if (ChartEnum.PIE.getValue() == chart.getChartType()) {
            jsonObject = coutPie(statisticsCi, ciAttrMap, chart);
        } else if (ChartEnum.COLUMN_HISTOGRAM.getValue() == chart.getChartType()) {
            jsonObject = coutColumnHistogram(statisticsCi, ciAttrMap, chart);
        }
        return jsonObject;
    }

    /**
     * 饼图结果统计
     *
     * @param statisticsCi
     * @param ciAttrMap
     */
    private JSONObject coutPie(Map<String, Map<String, List<String>>> statisticsCi, Map<String, Map<String, String>> ciAttrMap, Chart chart) {
        Map<String, Integer> countMap = new HashMap<>();
        Set<String> ciIds = statisticsCi.keySet();
        ciIds.forEach(ciId -> {
            String s = ciAttrMap.get(ciId).get(chart.getLegendAttrName());
            Integer count = countMap.get(s);
            if (count == null) {
                countMap.put(s, 1);
            } else {
                countMap.put(s, ++count);
            }
        });
		UinoChartDataBean<List<UinoChartDataItem>> chartData = new UinoChartDataBean<>(UinoChartType.PIE);
		List<UinoChartDataItem> items = new ArrayList<>();
		chartData.setData(items);
		countMap.forEach((key, val) -> {
			UinoChartDataItem item = new UinoChartDataItem();
			item.setName(key);
			item.setValue((double) val);
			items.add(item);
		});
		return JSONObject.parseObject(JSONObject.toJSONString(chartData));
    }

    /**
     * 堆叠图结果统计
     * count 属性去重
     *
     * @param statisticsCi
     * @param ciAttrMap
     * @param chart
     */
    private JSONObject coutColumnHistogram(Map<String, Map<String, List<String>>> statisticsCi, Map<String, Map<String, String>> ciAttrMap, Chart chart) {
        //图例
        List<String> seriesList = new ArrayList<String>();
		// JSONArray legendArr = new JSONArray();
		Map<String, Object> legendArr = new HashMap<>();
        if (StatisticalEnum.DISTINCT_COUNT.getValue() == chart.getStatisticalType() || StatisticalEnum.COUNT.getValue() == chart.getStatisticalType()) {
            Map<String, Map<String, List<String>>> countMap = new HashMap<>();
            Set<String> legendSet = new HashSet<String>();
            for (String seriesCiId : statisticsCi.keySet()) {
                String series = ciAttrMap.get(seriesCiId).get(chart.getSeriesAttrName());
                if (series == null) {
                    series = "";
                } else {
                    series = series.trim();
                }
                Map<String, List<String>> statisticsMap = statisticsCi.get(seriesCiId);
                for (String legendCiId : statisticsMap.keySet()) {
                    String legend = null;
                    if (legendCiId != null) {
                        legend = ciAttrMap.get(legendCiId).get(chart.getLegendAttrName());
                    }

                    if (legend == null) {
                        legend = "";
                    } else {
                        legend = legend.trim();
                    }
                    legendSet.add(legend);
                    for (String valueCiId : statisticsMap.get(legendCiId)) {
                        String value = ciAttrMap.get(valueCiId).get(chart.getValueAttrName());
                        if (value == null) {
                            value = "";
                        } else {
                            value = value.trim();
                        }
                        Map<String, List<String>> m = countMap.get(series);
                        if (m == null) {
                            m = new HashMap<String, List<String>>();
                            countMap.put(series, m);
                        }
                        List<String> s = m.get(legend);
                        if (s == null) {
                            s = new ArrayList<String>();
                            m.put(legend, s);
                        }
                        s.add(value);
                    }
                }
            }
            seriesList.addAll(countMap.keySet());
            Collections.sort(seriesList);
            List<String> legendList = new ArrayList<String>();
            legendList.addAll(legendSet);
            Collections.sort(legendList);
            for (String legend : legendList) {
                List<Integer> counts = new ArrayList<Integer>();
                for (String series : seriesList) {
                    Integer count = 0;
                    if (countMap.containsKey(series) && countMap.get(series).containsKey(legend)) {
                        if (StatisticalEnum.DISTINCT_COUNT.getValue() == chart.getStatisticalType()) {
                            count = new HashSet<>(countMap.get(series).get(legend)).size();
                        } else {
                            count = countMap.get(series).get(legend).size();
                        }
                    }
                    counts.add(count);
                }
				// JSONObject legendObj = new JSONObject();
				// legendObj.put("name", legend);
				// legendObj.put("counts", counts);
				legendArr.put(legend, counts);
				// legendArr.add(legendObj);
            }
        } else {
            Map<String, Map<String, Double>> sumMap = new HashMap<>();
            Set<String> seriesSet = new HashSet<String>();
            for (String seriesCiId : statisticsCi.keySet()) {
                String series = ciAttrMap.get(seriesCiId).get(chart.getSeriesAttrName());
                if (series == null) {
                    series = "";
                } else {
                    series = series.trim();
                }
                seriesSet.add(series);
                Map<String, List<String>> statisticsMap = statisticsCi.get(seriesCiId);
                for (String legendCiId : statisticsMap.keySet()) {
                    String legend = null;
                    if (legendCiId != null) {
                        legend = ciAttrMap.get(legendCiId).get(chart.getLegendAttrName());
                    }
                    if (legend == null) {
                        legend = "";
                    } else {
                        legend = legend.trim();
                    }
                    for (String valueCiId : statisticsMap.get(legendCiId)) {
                        double value = 0;
                        try {
                            value = Double.parseDouble(ciAttrMap.get(valueCiId).get(chart.getValueAttrName()));
                        } catch (Exception e) {
                        }

                        Map<String, Double> m = sumMap.get(legend);
                        if (m == null) {
                            m = new HashMap<>();
                            sumMap.put(legend, m);
                        }
                        Double sum = m.get(series);
                        if (sum == null) {
                            sum = 0.00;
                        }
                        sum = Double.sum(sum, value);
                        m.put(series, sum);
                    }
                }
            }
            seriesList.addAll(seriesSet);
            Collections.sort(seriesList);
            List<String> legendList = new ArrayList<String>();
            legendList.addAll(sumMap.keySet());
            Collections.sort(legendList);
            for (String legend : legendList) {
                List<Double> counts = new ArrayList<>();
                for (String series : seriesList) {
                    double count = 0;
                    if (sumMap.containsKey(legend) && sumMap.get(legend).containsKey(series)) {
                        count = sumMap.get(legend).get(series);
                    }
                    counts.add(count);
                }
				// JSONObject legendObj = new JSONObject();
				// legendObj.put("name", legend);
				// legendObj.put("counts", counts);
				// legendArr.add(legendObj);
				legendArr.put(legend, counts);
            }
        }
        JSONObject jsonObject = new JSONObject();
		jsonObject.put("xData", seriesList);
		jsonObject.put("data", legendArr);
        return jsonObject;
    }


    /**
     * 柱状图结果统计
     *
     * @param statisticsCi series:legend:value
     * @param ciAttrMap
     * @param chart
     */
    private JSONObject coutHistogram(Map<String, Map<String, List<String>>> statisticsCi, Map<String, Map<String, String>> ciAttrMap, Chart chart) {
        String seriesAttrName = chart.getSeriesAttrName();
        String valueAttrName = chart.getValueAttrName();
        Map<String, Double> countMap = new HashMap<>();
        if (StatisticalEnum.DISTINCT_COUNT.getValue() == chart.getStatisticalType() || StatisticalEnum.COUNT.getValue() == chart.getStatisticalType()) {
            Map<String, List<String>> attrMap = new HashMap<>();
            statisticsCi.forEach((seriesCiId, v) -> {
                List<String> attrList = attrMap.get(ciAttrMap.get(seriesCiId).get(seriesAttrName));
                if (attrList == null) {
                    attrList = new ArrayList<>();
                    attrMap.put(ciAttrMap.get(seriesCiId).get(seriesAttrName), attrList);
                }
                List<String> valueCiIds = v.get(null);
                List<String> finalAttrSet = attrList;
                valueCiIds.forEach(valueCiId -> {
                    if (ciAttrMap.get(valueCiId) != null) {
                        finalAttrSet.add(ciAttrMap.get(valueCiId).get(valueAttrName));
                    }
                });
            });
            //统计个数
            if (StatisticalEnum.DISTINCT_COUNT.getValue() == chart.getStatisticalType()) {
                attrMap.forEach((k, v) -> {
                    countMap.put(k, (double) new HashSet<>(v).size());
                });
            } else {
                attrMap.forEach((k, v) -> {
                    countMap.put(k, (double) v.size());
                });
            }
        } else {
            statisticsCi.forEach((seriesCiId, v) -> {
                String seriesAttrV = ciAttrMap.get(seriesCiId).get(seriesAttrName);
                List<String> valueCiIds = v.get(null);
                if (countMap.get(seriesAttrV) == null) {
                    countMap.put(seriesAttrV, 0.00);
                }
                valueCiIds.forEach(valueCiId -> {
                    double i = 0.00;
                    try {
                        i = Double.parseDouble(ciAttrMap.get(valueCiId).get(valueAttrName));
                    } catch (NumberFormatException e) {
                    }
                    Double sum = countMap.get(seriesAttrV);
                    countMap.put(seriesAttrV, Double.sum(i, sum));

                });
            });
        }
        JSONObject jsonObject = new JSONObject();
		jsonObject.put("xData", countMap.keySet());
		jsonObject.put("data", countMap.values());
        return jsonObject;
    }

    /**
     * 查询所有结果，进行ci关联拼接去重
     *
     * @param dataSetMallRelationApi 关系遍历规则
     * @param ciAttrMap              ciid对应所选属性的属性值
     * @param chart                  图表参数
     * @return 存储ciid，柱：{seriesAxis:null:[valueAxis]}；堆叠柱：{seriesAxis:legend:[valueAxis]};饼{legend:null}
     */
    private Map<String, Map<String, List<String>>> filterPath(DataSetMallApiRelationRule dataSetMallRelationApi, Map<Long, SimpleFriendInfo> simpleFriendInfoMap,
                                                              Map<String, Map<String, String>> ciAttrMap, Chart chart, ICISvc iciSvc, ICIRltSvc iciRltSvc) {
        RltRuleInfo ruleInfo = dataSetMallRelationApi.convert2RuleInfo();
        //存储ciid组，柱：{x:null:[y]}；堆叠柱：{x:legend:[y]},饼{legend:null}
        Map<String, Map<String, List<String>>> result = new HashMap<>();
        //遍历顺序规则
        Map<Long, List<QueryCondition>> travalTreeMap = findTravalTree(ruleInfo);
        Map<Long, List<Long>> classChilders = getClassChilders(dataSetMallRelationApi.getDomainId());
        Map<Long, RltRuleInfo.NodeInfo> nodeMap = this.generateNodeMap(ruleInfo.getNodes(), null);
        filterFriendInfoMapBychartType(ruleInfo, ciAttrMap, result, travalTreeMap, nodeMap, simpleFriendInfoMap, classChilders, chart, iciSvc, iciRltSvc);
        return result;
    }

    /**
     * 根据friendInfoMap过滤所有ci路径生成result层次数据
     *
     * @param ruleInfo            规则
     * @param ciAttrMap           ciid对应所选属性值
     * @param result              结果
     * @param travalTreeMap       节点关联关系
     * @param nodeMap
     * @param simpleFriendInfoMap 关系遍历查询出来的结果
     */
    private void filterFriendInfoMapBychartType(RltRuleInfo ruleInfo, Map<String, Map<String, String>> ciAttrMap, Map<String, Map<String, List<String>>> result,
                                                Map<Long, List<QueryCondition>> travalTreeMap, Map<Long, RltRuleInfo.NodeInfo> nodeMap, Map<Long, SimpleFriendInfo> simpleFriendInfoMap, Map<Long, List<Long>> classChilders, Chart chart,
                                                ICISvc iciSvc, ICIRltSvc iciRltSvc) {
        SysUser user = null;
        try {
            user = SysUtil.getCurrentUserInfo();
        } catch (Exception e) {
            log.error("获取登陆用户失败，所有值取默认值");
        }
        int chartType = chart.getChartType();
        String seriesPageNodeId = chart.getSeriesPageNodeId();
        String seriesAttrProStdName = chart.getSeriesAttrName();
        String valuePageNodeId = chart.getValuePageNodeId();
        String valueAttrProStdName = chart.getValueAttrName();
        String legendPageNodeId = chart.getLegendPageNodeId();
        String legendAttrProStdName = chart.getLegendAttrName();

        List<String> startCiId = new ArrayList<>();
        Set<Long> ciIds = new HashSet<>();
        Set<Long> ciRltLineIds = new HashSet<>();
        Map<Long, Set<Long>> ciIdByNodeMap = new HashMap<>();

        for (Map.Entry<Long, SimpleFriendInfo> entry : simpleFriendInfoMap.entrySet()) {
            Long key = entry.getKey();
            SimpleFriendInfo value = entry.getValue();
            startCiId.add(key + "");
            ciIds.addAll(value.getCiIds());
            ciRltLineIds.addAll(value.getCiRltLineIds());
            for (Map.Entry<Long, Set<Long>> longSetEntry : value.getCiIdMap().entrySet()) {
                Set<Long> set = ciIdByNodeMap.computeIfAbsent(longSetEntry.getKey(), v -> new HashSet<>());
                set.addAll(longSetEntry.getValue());
            }
            if (ciIds.size() > 5000) {
                //查询ci
                CCcCi cdt = new CCcCi();
                cdt.setIds(ciIds.toArray(new Long[0]));
                List<ESCIInfo> records = iciSvc.queryESCIInfoList(user == null ? 1L : user.getDomainId(), cdt, null, false);
                //查询关系
                List<ESCIRltInfo> ciLines = new ArrayList<>();
                if (!ciRltLineIds.isEmpty()) {
                    ESRltSearchBean bean = new ESRltSearchBean();
                    CCcCiRlt rltCdt = new CCcCiRlt();
                    rltCdt.setIds(ciRltLineIds.toArray(new Long[0]));
                    bean.setCdt(rltCdt);
                    bean.setPageNum(1);
                    bean.setPageSize(ciRltLineIds.size());
                    Page<ESCIRltInfo> page = iciRltSvc.searchRlt(bean);
                    ciLines = page.getData();
                }
                calculatePathAndCount(ruleInfo, ciAttrMap, result, travalTreeMap, nodeMap, classChilders, chartType, seriesPageNodeId, seriesAttrProStdName, valuePageNodeId, valueAttrProStdName, legendPageNodeId, legendAttrProStdName, startCiId, ciIdByNodeMap, records, ciLines);
                startCiId = new ArrayList<>();
                ciIds = new HashSet<>();
                ciRltLineIds = new HashSet<>();
                ciIdByNodeMap = new HashMap<>();
            }

        }
        if (!ciIds.isEmpty()) {
            //查询ci
            CCcCi cdt = new CCcCi();
            cdt.setIds(ciIds.toArray(new Long[0]));
            List<ESCIInfo> records = iciSvc.queryESCIInfoList(user == null ? 1L : user.getDomainId(), cdt, null, false);
            //查询关系
            List<ESCIRltInfo> ciLines = new ArrayList<>();
            if (!ciRltLineIds.isEmpty()) {
                ESRltSearchBean bean = new ESRltSearchBean();
                CCcCiRlt rltCdt = new CCcCiRlt();
                rltCdt.setIds(ciRltLineIds.toArray(new Long[0]));
                bean.setCdt(rltCdt);
                bean.setPageNum(1);
                bean.setPageSize(ciRltLineIds.size());
                Page<ESCIRltInfo> page = iciRltSvc.searchRlt(bean);
                ciLines = page.getData();
            }
            calculatePathAndCount(ruleInfo, ciAttrMap, result, travalTreeMap, nodeMap, classChilders, chartType, seriesPageNodeId, seriesAttrProStdName, valuePageNodeId, valueAttrProStdName, legendPageNodeId, legendAttrProStdName, startCiId, ciIdByNodeMap, records, ciLines);
        }

    }

    /**
     * 计算路径并统计
     *
     * @param ruleInfo
     * @param ciAttrMap
     * @param result
     * @param travalTreeMap
     * @param nodeMap
     * @param classChilders
     * @param chartType
     * @param seriesPageNodeId
     * @param seriesAttrProStdName
     * @param valuePageNodeId
     * @param valueAttrProStdName
     * @param legendPageNodeId
     * @param legendAttrProStdName
     * @param startCiId
     * @param ciIdByNodeMap
     * @param records
     * @param ciLines
     */
    private void calculatePathAndCount(RltRuleInfo ruleInfo, Map<String, Map<String, String>> ciAttrMap, Map<String, Map<String, List<String>>> result, Map<Long, List<QueryCondition>> travalTreeMap, Map<Long, RltRuleInfo.NodeInfo> nodeMap, Map<Long, List<Long>> classChilders, int chartType, String seriesPageNodeId, String seriesAttrProStdName, String valuePageNodeId, String valueAttrProStdName, String legendPageNodeId, String legendAttrProStdName, List<String> startCiId, Map<Long, Set<Long>> ciIdByNodeMap, List<ESCIInfo> records, List<ESCIRltInfo> ciLines) {
        //最终路径
        Map<String, List<String>> ultimatelyPath = new LinkedHashMap<>();
        Map<String, List<ESCIRltInfo>> ciLinesMap = getCiLinesMap(ciLines);
        getCiAllPath(travalTreeMap, ciLinesMap, nodeMap, ciIdByNodeMap, ruleInfo.getStartPageNodeId(), startCiId, new ArrayList<>(), ultimatelyPath, classChilders);
        for (Map.Entry<String, List<String>> path : ultimatelyPath.entrySet()) {
            List<String> nodePath = Arrays.asList(path.getKey().split("-"));
            //在统计之列予以处理
            //后端没有条形图的概念，前端把x和y互换则为条
            if (ChartEnum.PIE.getValue() == chartType) {
                //饼图
                if (nodePath.contains(legendPageNodeId)) {
                    Map<String, ESCIInfo> ciNodeMap = getCIInfoMapCIObj(records);

                    int legendIndex = nodePath.indexOf(legendPageNodeId);

                    path.getValue().forEach(paths -> {
                        String[] ciPath = paths.split("-");
                        String legendCiId = ciPath[legendIndex];
                        Map<String, String> attrMap = ciAttrMap.get(legendCiId);
                        if (attrMap == null) {
                            attrMap = new HashMap<>();
                            ciAttrMap.put(legendCiId, attrMap);
                        }
                        attrMap.put(legendAttrProStdName, ciNodeMap.get(legendCiId).getAttrs().get(legendAttrProStdName).toString());
                        result.put(legendCiId, null);
                    });
                }
            }
            if (ChartEnum.HISTOGRAM.getValue() == chartType || ChartEnum.COLUMN_HISTOGRAM.getValue() == chartType) {
                //柱和堆叠柱
                if (nodePath.contains(seriesPageNodeId)) {
                    Map<String, ESCIInfo> ciNodeMap = getCIInfoMapCIObj(records);

                    //获取ci位置，必然存在
                    int xIndex = nodePath.indexOf(seriesPageNodeId);
                    //可能不存在，究其原因可查阅路径拼接逻辑
                    int yIndex = nodePath.indexOf(valuePageNodeId);
                    int legendIndex;
                    if (legendPageNodeId == null) {
                        legendIndex = -1;
                    } else {
                        legendIndex = nodePath.indexOf(legendPageNodeId);
                    }
                    path.getValue().forEach(paths -> {
                        String[] ciPath = paths.split("-");

                        String xCiId = ciPath[xIndex];
                        Map<String, String> xAttrMap = ciAttrMap.get(xCiId);
                        if (xAttrMap == null) {
                            xAttrMap = new HashMap<>();
                            ciAttrMap.put(xCiId, xAttrMap);
                        }
                        xAttrMap.put(seriesAttrProStdName, ciNodeMap.get(xCiId).getAttrs().get(seriesAttrProStdName).toString());
                        Map<String, List<String>> map = result.get(xCiId);
                        if (map == null) {
                            map = new HashMap<>();
                            result.put(xCiId, map);
                        }
                        String legendCiId;
                        if (legendIndex != -1) {
                            legendCiId = ciPath[legendIndex];
                            Map<String, String> attrMap = ciAttrMap.get(legendCiId);
                            if (attrMap == null) {
                                attrMap = new HashMap<>();
                                ciAttrMap.put(legendCiId, attrMap);
                            }
                            if (ciNodeMap.get(legendCiId).getAttrs().get(legendAttrProStdName) != null) {
                                attrMap.put(legendAttrProStdName, ciNodeMap.get(legendCiId).getAttrs().get(legendAttrProStdName).toString());
                            }

                        } else {
                            legendCiId = null;
                        }
                        List<String> yCiIds = map.get(legendCiId);
                        if (yCiIds == null) {
                            yCiIds = new ArrayList<>();
                            map.put(legendCiId, yCiIds);
                        }
                        if (yIndex != -1) {
                            String yCiId = ciPath[yIndex];
                            Map<String, String> attrMap = ciAttrMap.get(yCiId);
                            if (attrMap == null) {
                                attrMap = new HashMap<>();
                                ciAttrMap.put(yCiId, attrMap);
                            }
                            attrMap.put(valueAttrProStdName, ciNodeMap.get(yCiId).getAttrs().get(valueAttrProStdName).toString());
                            yCiIds.add(yCiId);
                        }
                    });
                }
            }

        }
    }

    private Map<String, ESCIInfo> getCIInfoMapCIObj(List<ESCIInfo> ciNodes) {
        Map<String, ESCIInfo> ciMap = new HashMap<>();
        for (ESCIInfo ciNode : ciNodes) {
            ciMap.put(String.valueOf(ciNode.getId()), ciNode);
        }
        return ciMap;
    }

    /**
     * 查询所有ci和关系分类
     *
     * @return
     */
    private Map<Long, CcCiClassInfo> findClassInfo(RltRuleInfo rltRuleInfo) {
        // 获取模型中ci 分类的分类信息
        List<Long> ciClassIds = new ArrayList<>();
        List<RltRuleInfo.NodeInfo> nodes = rltRuleInfo.getNodes();
        for (RltRuleInfo.NodeInfo nodeInfo : nodes) {
            if (nodeInfo.getNode().getNodeType() == 1) {
                Long classId = nodeInfo.getNode().getClassId();
                ciClassIds.add(classId);
            }
        }
        Map<Long, CcCiClassInfo> classInfoMap = new HashMap<>();
        CCcCiClass cdt = new CCcCiClass();
        cdt.setDomainId(rltRuleInfo.getDomainId());
        cdt.setIds(ciClassIds.toArray(new Long[0]));
        List<CcCiClassInfo> ccCiClassInfos = iciClassSvc.queryClassByCdt(cdt);
        for (CcCiClassInfo classInfo : ccCiClassInfos) {
//            String icon = classInfo.getCiClass().getIcon();
//            classInfo.getCiClass().setIcon(rsmSlaveRoot + icon);
            classInfoMap.put(classInfo.getCiClass().getId(), classInfo);
        }

        // 分类关系
        List<Long> rltIds = new ArrayList<>();
        List<RltRuleInfo.LineInfo> lines = rltRuleInfo.getLines();
        if (lines != null && !lines.isEmpty()) {
            for (RltRuleInfo.LineInfo lineInfo : lines) {
                if (lineInfo.getLine().getLineType() == 1) {
                    rltIds.add(lineInfo.getLine().getClsRltId());
                }
            }
        }
        CCcCiClass rltCdt = new CCcCiClass();
        rltCdt.setDomainId(rltRuleInfo.getDomainId());
        rltCdt.setIds(rltIds.toArray(new Long[0]));
        List<CcCiClassInfo> rltClassByCdt = iRltClassSvc.getRltClassByCdt(rltCdt);
        for (CcCiClassInfo classInfo : rltClassByCdt) {
            classInfoMap.put(classInfo.getCiClass().getId(), classInfo);
        }
        return classInfoMap;
    }

    private Map<Long, FriendInfo> traversal(RltRuleInfo ruleInfo, List<CcCiInfo> ciInfos,
                                            Map<Long, CcCiClassInfo> classMap, Integer limit, String serial, boolean isIncludeAllStartCI,
                                            ICISvc iciSvc, ICIRltSvc iciRltSvc) {
        // 模型中包含的ci和关系分类id
        Set<Long> ruleClassIds = new HashSet<>();
        List<Map<Long, List<RltRuleInfo.LineInfo>>> upAndDownMap = generateUpAndDownRelMap(ruleInfo.getLines(), ruleClassIds);
        Map<Long, RltRuleInfo.NodeInfo> nodeMap = generateNodeMap(ruleInfo.getNodes(), ruleClassIds);
        //遍历顺序规则
        Map<Long, List<QueryCondition>> travalTreeMap = generateTravalTree(ruleInfo.getStartPageNodeId(),
                upAndDownMap.get(0), upAndDownMap.get(1), nodeMap);
        //构建父分类对应的子分类
        Map<Long, List<Long>> classChilders = new HashMap<>();

        Set<Long> classIds = classMap.keySet();
        classMap.forEach((id, classInfo) -> {
            Long parentId = classInfo.getCiClass().getParentId();
            if (classIds.contains(parentId)) {
                List<Long> childers = classChilders.computeIfAbsent(parentId, k -> new ArrayList<>());
                childers.add(id);
            }
        });
        Map<Long, FriendInfo> friendInfoMap = new HashMap<>();

        if (travalTreeMap.size() == 0) {
            //单NODE
            Set<Long> unknownStartCiIds = new HashSet<>();
            for (CcCiInfo ciInfo : ciInfos) {
                unknownStartCiIds.add(ciInfo.getCi().getId());
            }
            RltRuleInfo.NodeInfo node = nodeMap.get(ruleInfo.getStartPageNodeId());
            if (unknownStartCiIds.size() > 0 && node.getNodeCdts() != null && node.getNodeCdts().size() > 0) {
                Map<Long, CcCiInfo> queryLegalMap = new HashMap<>();
                Map<Long, CcCiInfo> queryIllegalMap = new HashMap<>();
                queryCiByNodeCdt(classMap, node, unknownStartCiIds, queryLegalMap, queryIllegalMap, classChilders, ruleInfo.getDomainId(), iciSvc);
                for (Long key : queryLegalMap.keySet()) {
                    boolean withinLimit = limit == null || friendInfoMap.size() < limit;
                    if (withinLimit) {
                        CcCiInfo ciInfo = queryLegalMap.get(key);
                        friendInfoMap.put(ciInfo.getCi().getId(), generateFriendInfoBySingleCiInfo(ciInfo,
                                ruleInfo.getStartPageNodeId(), classMap, ruleClassIds));
                    }
                }

            } else {
                for (CcCiInfo ciInfo : ciInfos) {
                    boolean withinLimit = limit == null || friendInfoMap.size() < limit;
                    if (withinLimit) {
                        friendInfoMap.put(ciInfo.getCi().getId(), generateFriendInfoBySingleCiInfo(ciInfo,
                                ruleInfo.getStartPageNodeId(), classMap, ruleClassIds));
                    }
                }
            }
        } else {//有LINE有NODE
            //约束规则
//            拼接and or条件
            Map<Long, List<Map<String, Restriction>>> restrictionMap = generateRestrictionForTraversal(travalTreeMap, upAndDownMap.get(0), upAndDownMap.get(1), nodeMap);

            int start = 0;
            while (start < ciInfos.size()) {
                int end = start + 1000;
                if (end > ciInfos.size()) {
                    end = ciInfos.size();
                }
                List<CcCiInfo> subCiInfos = ciInfos.subList(start, end);
                Map<Long, FriendInfo> subFriendInfoMap = subTraversal(ruleInfo, subCiInfos, classMap, nodeMap,
                        travalTreeMap, restrictionMap, ruleClassIds, classChilders, serial, iciSvc, iciRltSvc);
                for (Long ciId : subFriendInfoMap.keySet()) {
                    boolean withinLimit = limit == null || friendInfoMap.size() < limit;
                    if (withinLimit) {
                        friendInfoMap.put(ciId, subFriendInfoMap.get(ciId));
                        if (limit != null && friendInfoMap.size() >= limit) {
                            break;
                        }
                    }
                }
                start = end;
                if (limit != null && friendInfoMap.size() >= limit) {
                    break;
                }
            }

        }
        if (isIncludeAllStartCI) {
            boolean withinLimit = limit == null || friendInfoMap.size() < limit;
            if (withinLimit) {
                for (CcCiInfo ciInfo : ciInfos) {
                    if (!friendInfoMap.containsKey(ciInfo.getCi().getId())) {
                        friendInfoMap.put(ciInfo.getCi().getId(), generateFriendInfoBySingleCiInfo(ciInfo,
                                ruleInfo.getStartPageNodeId(), classMap, ruleClassIds));
                        if (limit != null && friendInfoMap.size() >= limit) {
                            break;
                        }
                    }
                }
            }


        }
        return friendInfoMap;
    }

    /**
     * @param ruleInfo       规则
     * @param ciInfos        入口ci
     * @param classMap       分类信息
     * @param nodeMap        页面节点对应节点信息
     * @param travalTreeMap  节点直系节点及关系
     * @param restrictionMap and or条件
     * @param serial
     * @return
     * @throws Exception
     */
    private Map<Long, FriendInfo> subTraversal(RltRuleInfo ruleInfo,
                                               List<CcCiInfo> ciInfos,
                                               Map<Long, CcCiClassInfo> classMap,
                                               Map<Long, RltRuleInfo.NodeInfo> nodeMap,
                                               Map<Long, List<QueryCondition>> travalTreeMap,
                                               Map<Long, List<Map<String, Restriction>>> restrictionMap,
                                               Set<Long> ruleClassIds,
                                               Map<Long, List<Long>> classChilders,
                                               String serial, ICISvc iciSvc, ICIRltSvc iciRltSvc) {
        // 页面节点编号对应ciId
        Map<Long, Set<Long>> ciMap = new HashMap<>();
        // 页面关系线编号：{ci关系id：关系详情}
        Map<Long, Map<Long, ESCIRltInfo>> relMap = new HashMap<>();
        // 节点上合法和不合法的CI
        Map<Long, List<Map<Long, CcCiInfo>>> nodeLegalCiInfoMap = new HashMap<>();

        Long now = System.currentTimeMillis();
        // 从数据库中查询CI和关系
        loadCiAndRltForTraversal(ruleInfo.getStartPageNodeId(), ciInfos, classMap, travalTreeMap, nodeMap, ciMap,
                relMap, nodeLegalCiInfoMap, classChilders, ruleInfo.getDomainId(), iciSvc, iciRltSvc);
        log.info(
                "Traversal.loadCiAndRltForTraversal====" + serial + "  time====" + (System.currentTimeMillis() - now));

        now = System.currentTimeMillis();
        // 按入口集合每个CI分组对应的图, 然后做过滤
        // 入口：节点：ciid
        Map<Long, Map<Long, Set<Long>>> groupCiMap = new HashMap<>();
        Map<Long, Map<Long, Map<String, Map<Long, Map<Long, Map<Long, ESCIRltInfo>>>>>> groupRelGroupMap = new HashMap<>();
        groupAndRestrictEntryForTraversal(ruleInfo.getStartPageNodeId(), travalTreeMap, restrictionMap, ciMap, relMap,
                groupCiMap, groupRelGroupMap);
        log.info("Traversal.groupAndRestrictEntryForTraversal====" + serial + "  time===="
                + (System.currentTimeMillis() - now));
        // 按FriendInfo格式返回
        now = System.currentTimeMillis();
        Map<Long, FriendInfo> friendInfoMap = formatForTraversal(groupCiMap, groupRelGroupMap, nodeLegalCiInfoMap,
                classMap, ruleClassIds, ruleInfo.getDomainId(), iciSvc);
        log.info("Traversal.formatForTraversal====" + serial + "  time====" + (System.currentTimeMillis() - now));
        return friendInfoMap;
    }

    /**
     * 查询数据库
     *
     * @param startPageNodeId
     * @param entryCiInfos
     * @param classMap
     * @param travalTreeMap
     * @param nodeMap
     * @param ciMap
     * @param relMap
     * @param nodeLegalCiInfoMap
     * @throws Exception
     */
    private void loadCiAndRltForTraversal(Long startPageNodeId, List<CcCiInfo> entryCiInfos,
                                          Map<Long, CcCiClassInfo> classMap,
                                          Map<Long, List<QueryCondition>> travalTreeMap,
                                          Map<Long, RltRuleInfo.NodeInfo> nodeMap,
                                          Map<Long, Set<Long>> ciMap,
                                          Map<Long, Map<Long, ESCIRltInfo>> relMap,
                                          Map<Long, List<Map<Long, CcCiInfo>>> nodeLegalCiInfoMap,
                                          Map<Long, List<Long>> classChilders, Long domainId,
                                          ICISvc iciSvc, ICIRltSvc iciRltSvc) {

        for (Long startPageId : travalTreeMap.keySet()) {
            // 遍历所有作为路径，将节点和路径的Id存入ciMap和relMap作为key
            ciMap.put(startPageId, new HashSet<Long>());
            if (nodeMap.get(startPageId).getNodeCdts() != null && nodeMap.get(startPageId).getNodeCdts().size() > 0) {
                List<Map<Long, CcCiInfo>> l = new ArrayList<>();
                l.add(new HashMap<>());
                l.add(new HashMap<>());
                nodeLegalCiInfoMap.put(startPageId, l);
            }
            // 直系节点
            for (QueryCondition path : travalTreeMap.get(startPageId)) {
                ciMap.put(startPageId.equals(path.getStartPageId()) ? path.getEndPageId() : path.getStartPageId(),
                        new HashSet<>());
                relMap.put(path.getPageId(), new HashMap<>());
                Long nextNodeId = startPageId.equals(path.getStartPageId()) ? path.getEndPageId()
                        : path.getStartPageId();
                if (nodeMap.get(nextNodeId).getNodeCdts() != null && nodeMap.get(nextNodeId).getNodeCdts().size() > 0) {
                    List<Map<Long, CcCiInfo>> l = new ArrayList<>();
                    l.add(new HashMap<>());
                    l.add(new HashMap<>());
                    nodeLegalCiInfoMap.put(nextNodeId, l);
                }
            }
        }
        for (CcCiInfo entryCiInfo : entryCiInfos) {
            ciMap.get(startPageNodeId).add(entryCiInfo.getCi().getId());
        }
        //对于有条件的node预查出CI，如果CI数量较少放入preCiMap
        Map<Long, List<Long>> preCiMap = new HashMap<Long, List<Long>>();
        for (Long pageNodeId : nodeMap.keySet()) {
            if (nodeMap.get(pageNodeId).getNodeCdts() != null && nodeMap.get(pageNodeId).getNodeCdts().size() > 0 && startPageNodeId != pageNodeId) {
                //List<ESAttrBean> attrBeans = generateAttrBeansByNodeCdt(classMap, nodeMap.get(pageNodeId));
                Map<String, List<ESAttrBean>> stringListMap = generateAttrBeansByNodeCdtNew(classMap, nodeMap.get(pageNodeId));
                List<ESAttrBean> attrBeans = stringListMap.get("AND");
                List<ESAttrBean> orBeans = stringListMap.get("OR");
                ESCISearchBean searchCI = new ESCISearchBean();
                searchCI.setDomainId(domainId);
                if (!attrBeans.isEmpty()) {
                    searchCI.setAndAttrs(attrBeans);
                }
                if (!attrBeans.isEmpty()) {
                    searchCI.setOrAttrs(orBeans);
                }
                List<Long> classIds = new ArrayList<Long>();
                classIds.add(nodeMap.get(pageNodeId).getNode().getClassId());
                if (classChilders.get(nodeMap.get(pageNodeId).getNode().getClassId()) != null) {
                    classIds.addAll(classChilders.get(nodeMap.get(pageNodeId).getNode().getClassId()));
                }
                searchCI.setClassIds(classIds);
                searchCI.setPageSize(200);
                searchCI.setPageNum(1);
                CcCiSearchPage page = iciSvc.searchCIByBean(searchCI);
                if (page.getTotalPages() < 2) {
                    List<Long> ciIdList = new ArrayList<Long>();
                    if (page.getData() != null && page.getData().getRecords() != null) {
                        for (CcCiObj esciInfo : page.getData().getRecords()) {
                            ciIdList.add(esciInfo.getCi().getId());
                        }
                    }
                    preCiMap.put(pageNodeId, ciIdList);
                }
            }
        }

        Set<Long> foundLinetIds = new HashSet<Long>();
        LinkedList<Long> queue = new LinkedList<Long>();
        queue.addFirst(startPageNodeId);
        while (!queue.isEmpty()) {
            Long startPageId = queue.removeLast();
            List<Long> startCiIds = new ArrayList<>();
            if (ciMap.get(startPageId) != null) {
                for (Long ciId : ciMap.get(startPageId)) {
                    startCiIds.add(ciId);
                }
            }
            if (nodeLegalCiInfoMap.containsKey(startPageId)) {
                startCiIds = filterIllegalCiForTraversal(classMap, startCiIds,
                        nodeLegalCiInfoMap.get(startPageId).get(0), nodeLegalCiInfoMap.get(startPageId).get(1),
                        nodeMap.get(startPageId), ciMap.get(startPageId), classChilders, domainId, iciSvc);
            }
            if (startCiIds.size() > 0) {
                if (travalTreeMap.containsKey(startPageId)) {
                    for (QueryCondition qc : travalTreeMap.get(startPageId)) {
                        if (!foundLinetIds.contains(qc.getPageId())) {
                            Long startClsId = nodeMap.get(startPageId).getNode().getClassId();
                            // 判断是否下行
                            Boolean directionEqualsDown = startPageId.equals(qc.getStartPageId());
                            Long nextId = directionEqualsDown ? qc.getEndPageId() : qc.getStartPageId();
                            List<Long> nextCiIds = new ArrayList<>();
                            Map<Long, List<ESCIRltInfo>> nextCiCodeRelMap = new HashMap<>();
                            List<ESCIRltInfo> rels = queryRltForTraversal(classMap, qc, startClsId,
                                    nodeMap.get(nextId).getNode().getClassId(), startCiIds, preCiMap.get(nextId), directionEqualsDown, classChilders, iciRltSvc);
                            for (ESCIRltInfo rel : rels) {
                                Long nextCiId = directionEqualsDown ? rel.getTargetCiId() : rel.getSourceCiId();
                                nextCiIds.add(nextCiId);
                                List<ESCIRltInfo> rs = nextCiCodeRelMap.computeIfAbsent(nextCiId, k -> new ArrayList<>());
                                rs.add(rel);
                            }
                            if (nodeLegalCiInfoMap.containsKey(nextId)) {
                                nextCiIds = filterIllegalCiForTraversal(classMap, nextCiIds,
                                        nodeLegalCiInfoMap.get(nextId).get(0), nodeLegalCiInfoMap.get(nextId).get(1),
                                        nodeMap.get(nextId), ciMap.get(nextId), classChilders, domainId, iciSvc);
                            }
                            for (Long nextCiId : nextCiIds) {
                                ciMap.get(nextId).add(nextCiId);
                                for (ESCIRltInfo rel : nextCiCodeRelMap.get(nextCiId)) {
                                    relMap.get(qc.getPageId()).put(rel.getId(), rel);
                                }
                            }
                            foundLinetIds.add(qc.getPageId());
                            queue.addFirst(nextId);
                        }
                    }
                }
            }
        }
    }

    /**
     * 根据条件查询ci是否复合条件，返回符合条件的ciid
     *
     * @param classMap
     * @param ciIds
     * @param legalMap
     * @param illegalMap
     * @param nodeInfo
     * @param ciIdSet
     * @return
     */
    private List<Long> filterIllegalCiForTraversal(Map<Long, CcCiClassInfo> classMap, List<Long> ciIds,
                                                   Map<Long, CcCiInfo> legalMap, Map<Long, CcCiInfo> illegalMap, RltRuleInfo.NodeInfo nodeInfo,
                                                   Set<Long> ciIdSet, Map<Long, List<Long>> classChilders, Long domainId,
                                                   ICISvc iciSvc) {
        Set<Long> legalStartCiIds = new HashSet<>();
        Set<Long> illegalStartCiIds = new HashSet<>();
        Set<Long> unknownStartCiIds = new HashSet<>();
        for (Long ciId : ciIds) {
            if (legalMap.containsKey(ciId)) {
                legalStartCiIds.add(ciId);
            } else {
                if (illegalMap.containsKey(ciId)) {
                    illegalStartCiIds.add(ciId);
                } else {
                    unknownStartCiIds.add(ciId);
                }
            }
        }
        if (unknownStartCiIds.size() > 0) {
            Map<Long, CcCiInfo> queryLegalMap = new HashMap<>();
            Map<Long, CcCiInfo> queryIllegalMap = new HashMap<>();
            queryCiByNodeCdt(classMap, nodeInfo, unknownStartCiIds, queryLegalMap, queryIllegalMap, classChilders, domainId, iciSvc);
            for (Long ciId : queryLegalMap.keySet()) {
                legalMap.put(ciId, queryLegalMap.get(ciId));
                legalStartCiIds.add(ciId);
            }
            for (Long ciId : queryIllegalMap.keySet()) {
                illegalMap.put(ciId, queryIllegalMap.get(ciId));
                illegalStartCiIds.add(ciId);
            }
            for (Long ciId : illegalStartCiIds) {
                ciIdSet.remove(ciId);
            }
        }
        List<Long> ret = new ArrayList<>();
        for (Long ciId : legalStartCiIds) {
            ret.add(ciId);
        }
        return ret;
    }

    private List<ESCIRltInfo> queryRltForTraversal(Map<Long, CcCiClassInfo> classMap, QueryCondition qc,
                                                   Long whereClsId, Long selectClsId, List<Long> ciIds, List<Long> preTargetCiIdList, Boolean direction, Map<Long, List<Long>> classChilders,
                                                   ICIRltSvc iciRltSvc) {
        Map<Long, String> attrMap = new HashMap<>();
        if (classMap.containsKey(qc.getRltClsId())) {
            if (classMap.get(qc.getRltClsId()).getAttrDefs() != null) {
                for (CcCiAttrDef attrDef : classMap.get(qc.getRltClsId()).getAttrDefs()) {
                    attrMap.put(attrDef.getId(), attrDef.getProStdName());
                }
            }
        }
        List<ESCIRltInfo> ret = new ArrayList<>();
        if (preTargetCiIdList == null || preTargetCiIdList.size() > 0) {
            ESRltSearchBean cqb = new ESRltSearchBean();
            List<Long> rltClassIds = new ArrayList<Long>();
            rltClassIds.add(qc.getRltClsId());
            cqb.setRltClassIds(rltClassIds);
            Set<Long> ciIdSet = new HashSet<>(ciIds);
            if (direction) {
                List<Long> sourceClassIds = new ArrayList<Long>();
                sourceClassIds.add(whereClsId);
                if (classChilders.get(whereClsId) != null) {
                    sourceClassIds.addAll(classChilders.get(whereClsId));
                }
                cqb.setSourceClassIds(sourceClassIds);
                List<Long> targetClassIds = new ArrayList<>();
                targetClassIds.add(selectClsId);
                if (classChilders.get(selectClsId) != null) {
                    targetClassIds.addAll(classChilders.get(selectClsId));
                }
                cqb.setTargetClassIds(targetClassIds);
                if (preTargetCiIdList != null) {
                    cqb.setTargetCiIds(preTargetCiIdList);
                }
                if (qc.getLineCdts() != null && qc.getLineCdts().size() > 0) {
                    List<ESAttrBean> attrs = new ArrayList<>();
                    for (RltRuleInfo.LineCdt cdt : qc.getLineCdts()) {
                        if (attrMap.containsKey(cdt.getAttrId())) {
                            ESAttrBean ab = new ESAttrBean();
                            if (cdt.getCdtOp() == 1) {
                                ab.setKey(attrMap.get(cdt.getAttrId()));
                                ab.setOptType(1);
                                ab.setValue(cdt.getCdtVal());
                                attrs.add(ab);
                            } else if (cdt.getCdtOp() == 2) {
                                ab.setKey(attrMap.get(cdt.getAttrId()));
                                ab.setOptType(2);
                                ab.setValue(cdt.getCdtVal());
                                attrs.add(ab);
                            }
                        }
                    }
                    if (attrs.size() > 0) {
                        cqb.setAndAttrs(attrs);
                    }
                }
                List<Long> ccs = new ArrayList<>();
                for (Long ciId : ciIdSet) {
                    ccs.add(ciId);
                    if (ccs.size() == 2000) {
                        cqb.setSourceCiIds(ccs);
                        ret.addAll(queryRlt(cqb, iciRltSvc));
                        ccs = new ArrayList<>();
                    }
                }
                if (ccs.size() > 0) {
                    cqb.setSourceCiIds(ccs);
                    ret.addAll(queryRlt(cqb, iciRltSvc));
                }
            } else {
                List<Long> targetClassIds = new ArrayList<Long>();
                targetClassIds.add(whereClsId);
                if (classChilders.get(whereClsId) != null) {
                    targetClassIds.addAll(classChilders.get(whereClsId));
                }
                cqb.setTargetClassIds(targetClassIds);
                List<Long> sourceClassIds = new ArrayList<Long>();
                sourceClassIds.add(selectClsId);
                if (classChilders.get(selectClsId) != null) {
                    sourceClassIds.addAll(classChilders.get(selectClsId));
                }
                cqb.setSourceClassIds(sourceClassIds);
                if (preTargetCiIdList != null) {
                    cqb.setSourceCiIds(preTargetCiIdList);
                }
                if (qc.getLineCdts() != null && qc.getLineCdts().size() > 0) {
                    List<ESAttrBean> attrs = new ArrayList<>();
                    for (RltRuleInfo.LineCdt cdt : qc.getLineCdts()) {
                        if (attrMap.containsKey(cdt.getAttrId())) {
                            ESAttrBean ab = new ESAttrBean();
                            if (cdt.getCdtOp() == 1) {
                                ab.setKey(attrMap.get(cdt.getAttrId()));
                                ab.setOptType(1);
                                ab.setValue(cdt.getCdtVal());
                                attrs.add(ab);
                            } else if (cdt.getCdtOp() == 2) {
                                ab.setKey(attrMap.get(cdt.getAttrId()));
                                ab.setOptType(2);
                                ab.setValue(cdt.getCdtVal());
                                attrs.add(ab);
                            }
                        }
                    }
                    if (attrs.size() > 0) {
                        cqb.setAndAttrs(attrs);
                    }
                }
                List<Long> ccs = new ArrayList<>();
                for (Long ciId : ciIdSet) {
                    ccs.add(ciId);
                    if (ccs.size() == 2000) {
                        cqb.setTargetCiIds(ccs);
                        ret.addAll(queryRlt(cqb, iciRltSvc));
                        ccs = new ArrayList<>();
                    }
                }
                if (ccs.size() > 0) {
                    cqb.setTargetCiIds(ccs);
                    ret.addAll(queryRlt(cqb, iciRltSvc));
                }
            }
        }

        return ret;
    }

    private List<ESCIRltInfo> queryRlt(ESRltSearchBean query, ICIRltSvc iciRltSvc) {
        query.setPageSize(2000);
        List<ESCIRltInfo> rels = new ArrayList<>();
        int pageNum = 1;
        while (true) {
            query.setPageNum(pageNum);
            Page<ESCIRltInfo> page = iciRltSvc.searchRlt(query);


            List<ESCIRltInfo> data = page.getData();
            if (data != null) {
                rels.addAll(data);
            }
            if (page.getTotalPages() <= pageNum) {
                break;
            }
            pageNum++;
        }
        return rels;
    }

    /**
     * @param entryNodeId
     * @param travalTreeMap
     * @param restrictionMap
     * @param ciMap
     * @param relMap
     * @param groupCiMap
     * @param groupRelGroupMap Map<Long, Map<Long, Map<String, Map<Long, Map<Long, Map<Long,
     *                         CcCiRltInfo>>>>>> 入口ciId 关系线分类 start、end 开始或结束id
     *                         结束或开始id（和前面相反） ci关系id，ci关系
     */
    private void groupAndRestrictEntryForTraversal(Long entryNodeId, Map<Long, List<QueryCondition>> travalTreeMap, Map<Long, List<Map<String, Restriction>>> restrictionMap, Map<Long, Set<Long>> ciMap, Map<Long, Map<Long, ESCIRltInfo>> relMap, Map<Long, Map<Long, Set<Long>>> groupCiMap, Map<Long, Map<Long, Map<String, Map<Long, Map<Long, Map<Long, ESCIRltInfo>>>>>> groupRelGroupMap) {

        Map<Long, QueryCondition> lineMap = new HashMap<>();
        for (Long startId : travalTreeMap.keySet()) {
            for (QueryCondition qc : travalTreeMap.get(startId)) {
                lineMap.put(qc.getPageId(), qc);
            }
        }
        Map<Long, Map<String, Map<Long, Map<Long, Map<Long, ESCIRltInfo>>>>> relGroupMap = new HashMap<>();
        for (Long linePageId : relMap.keySet()) {
            // 对每个linePage的操作
            Map<String, Map<Long, Map<Long, Map<Long, ESCIRltInfo>>>> directionMap = relGroupMap.get(linePageId);
            Map<Long, Map<Long, Map<Long, ESCIRltInfo>>> startMap = null;
            Map<Long, Map<Long, Map<Long, ESCIRltInfo>>> endMap = null;
            if (directionMap == null) {
                // 为空则初始化
                // directionMap包含startMap和endMap，使用linePageId为key存于relGroupMap
                // baseMap使用linePageId为key存于relBaseMap
                directionMap = new HashMap<>();
                startMap = new HashMap<>();
                directionMap.put("start", startMap);
                endMap = new HashMap<>();
                directionMap.put("end", endMap);
                relGroupMap.put(linePageId, directionMap);
            } else {
                startMap = directionMap.get("start");
                endMap = directionMap.get("end");
            }
            for (Long relId : relMap.get(linePageId).keySet()) {
                ESCIRltInfo rel = relMap.get(linePageId).get(relId);
                // 对lingPage中每个rel的操作
                // 起始ci
                Long startCi = rel.getSourceCiId();
                // 结束ci
                Long endCi = rel.getTargetCiId();
                // 将rel中的关系以ciId为key存于startMap和endMap中
                Map<Long, Map<Long, ESCIRltInfo>> startList = startMap.get(startCi);
                if (startList == null) {
                    // startMap不包含startlist则初始化这一项，使用ciId作为key
                    startList = new HashMap<>();
                    startMap.put(startCi, startList);
                }
                Map<Long, ESCIRltInfo> sl = startList.get(endCi);
                if (sl == null) {
                    sl = new HashMap<>();
                    startList.put(endCi, sl);
                }
                sl.put(relId, rel);
                Map<Long, Map<Long, ESCIRltInfo>> endList = endMap.get(endCi);
                if (endList == null) {
                    // endMap不包含endlist则初始化这一项，使用ciId作为key
                    endList = new HashMap<>();
                    endMap.put(endCi, endList);
                }
                Map<Long, ESCIRltInfo> el = startList.get(startCi);
                if (el == null) {
                    el = new HashMap<>();
                    endList.put(startCi, el);
                }
                el.put(relId, rel);
            }
        }

        for (Long entryCiId : ciMap.get(entryNodeId)) {
            // 节点对应ciId
            Map<Long, Set<Long>> subCiMap = new HashMap<>();
            Map<Long, Map<String, Map<Long, Map<Long, Map<Long, ESCIRltInfo>>>>> subRelGroupMap = new HashMap<>();
            // 按入口ci分组
            groupEntryForTraversal(entryNodeId, entryCiId, travalTreeMap, ciMap, relGroupMap, subCiMap, subRelGroupMap);
            // 按约束条件过滤
            restrictDataForTraversal(travalTreeMap, restrictionMap, lineMap, subCiMap, subRelGroupMap);

            if (subCiMap.get(entryNodeId).size() > 0) {
                groupCiMap.put(entryCiId, subCiMap);
                groupRelGroupMap.put(entryCiId, subRelGroupMap);
            }
        }
    }

    /**
     * @param entryNodeId
     * @param entryCiId
     * @param travalTreeMap
     * @param ciMap
     * @param relGroupMap    Map<Long, Map<String, Map<String, Map<String, Map<String,
     *                       CcCiRltInfo>>>>> 关系线分类 start、end 开始或结束cicode
     *                       结束或开始cicode（和前面相反） ci关系code，ci关系
     * @param subCiMap
     * @param subRelGroupMap
     */
    private void groupEntryForTraversal(Long entryNodeId, Long entryCiId, Map<Long, List<QueryCondition>> travalTreeMap,
                                        Map<Long, Set<Long>> ciMap,
                                        Map<Long, Map<String, Map<Long, Map<Long, Map<Long, ESCIRltInfo>>>>> relGroupMap,
                                        Map<Long, Set<Long>> subCiMap,
                                        Map<Long, Map<String, Map<Long, Map<Long, Map<Long, ESCIRltInfo>>>>> subRelGroupMap) {

        for (Long pageNodeId : ciMap.keySet()) {
            subCiMap.put(pageNodeId, new HashSet<Long>());
        }
        for (Long pageLineId : relGroupMap.keySet()) {
            Map<String, Map<Long, Map<Long, Map<Long, ESCIRltInfo>>>> directionMap = new HashMap<>();
            for (String direction : relGroupMap.get(pageLineId).keySet()) {
                directionMap.put(direction, new HashMap<>());
            }
            subRelGroupMap.put(pageLineId, directionMap);
        }

        Set<Long> foundLineIds = new HashSet<Long>();
        LinkedList<Long> nodeQueue = new LinkedList<Long>();
        nodeQueue.addFirst(entryNodeId);
        LinkedList<Set<Long>> ciQueue = new LinkedList<>();
        Set<Long> entryCiIdSet = new HashSet<>();
        entryCiIdSet.add(entryCiId);
        ciQueue.addFirst(entryCiIdSet);
        while (!nodeQueue.isEmpty()) {
            Long startPageId = nodeQueue.removeLast();
            Set<Long> startCiIds = ciQueue.removeLast();
            if (startCiIds.size() > 0) {
                subCiMap.get(startPageId).addAll(startCiIds);
                if (travalTreeMap.containsKey(startPageId)) {
                    for (QueryCondition qc : travalTreeMap.get(startPageId)) {
                        if (!foundLineIds.contains(qc.getPageId())) {
                            // 判断是否下行
                            Boolean directionEqualsDown = startPageId.equals(qc.getStartPageId());
                            Long nextId = directionEqualsDown ? qc.getEndPageId() : qc.getStartPageId();
                            Set<Long> nextCiIds = new HashSet<>();
                            // Map<String, List<CcCiRltInfo>> reverseRelMap =
                            // new HashMap<String, List<CcCiRltInfo>>();
                            for (Long ciId : startCiIds) {
                                Map<Long, Map<Long, ESCIRltInfo>> rels = relGroupMap.get(qc.getPageId())
                                        .get(directionEqualsDown ? "start" : "end").get(ciId);
                                if (rels != null) {
                                    Map<Long, Map<Long, ESCIRltInfo>> subRels = subRelGroupMap.get(qc.getPageId())
                                            .get(directionEqualsDown ? "start" : "end").get(ciId);
                                    if (subRels == null) {
                                        subRels = new HashMap<>();
                                        subRelGroupMap.get(qc.getPageId()).get(directionEqualsDown ? "start" : "end")
                                                .put(ciId, subRels);
                                    }
                                    for (Long nextCiId : rels.keySet()) {
                                        Map<Long, ESCIRltInfo> rMap = subRels.get(nextCiId);
                                        if (rMap == null) {
                                            rMap = new HashMap<>();
                                            subRels.put(nextCiId, rMap);
                                        }
                                        for (Long relId : rels.get(nextCiId).keySet()) {
                                            rMap.put(relId, rels.get(nextCiId).get(relId));

                                            Map<Long, Map<Long, ESCIRltInfo>> reverseSubRels = subRelGroupMap
                                                    .get(qc.getPageId()).get(directionEqualsDown ? "end" : "start")
                                                    .get(nextCiId);
                                            if (reverseSubRels == null) {
                                                reverseSubRels = new HashMap<>();
                                                subRelGroupMap.get(qc.getPageId())
                                                        .get(directionEqualsDown ? "end" : "start")
                                                        .put(nextCiId, reverseSubRels);
                                            }
                                            Map<Long, ESCIRltInfo> reverseRelMap = reverseSubRels.get(ciId);
                                            if (reverseRelMap == null) {
                                                reverseRelMap = new HashMap<>();
                                                reverseSubRels.put(ciId, reverseRelMap);
                                            }
                                            reverseRelMap.put(relId, rels.get(nextCiId).get(relId));
                                        }
                                        nextCiIds.add(nextCiId);
                                    }
                                }
                            }
                            if (nextCiIds.size() > 0) {
                                nodeQueue.addFirst(nextId);
                                ciQueue.addFirst(nextCiIds);
                            }
                            foundLineIds.add(qc.getPageId());
                        }
                    }
                }
            }
        }
    }

    /**
     * @param groupCiMap
     * @param groupRelGroupMap
     * @param nodeLegalCiInfoMap
     * @param classMap
     * @return
     */
    private Map<Long, FriendInfo> formatForTraversal(Map<Long, Map<Long, Set<Long>>> groupCiMap,
                                                     Map<Long, Map<Long, Map<String, Map<Long, Map<Long, Map<Long, ESCIRltInfo>>>>>> groupRelGroupMap,
                                                     Map<Long, List<Map<Long, CcCiInfo>>> nodeLegalCiInfoMap, Map<Long, CcCiClassInfo> classMap,
                                                     Set<Long> ruleClassIds, Long domainId, ICISvc iciSvc) {
        Map<Long, FriendInfo> ret = new HashMap<>();

        // 补全ciInfos
        Long now = System.currentTimeMillis();
        Map<Long, Set<String>> labelAttrMap = new HashMap<>();
        for (Long classId : classMap.keySet()) {
            if (classMap.get(classId).getAttrDefs() != null) {
                Set<String> labelAttrs = new HashSet<>();
                for (CcCiAttrDef attrDef : classMap.get(classId).getAttrDefs()) {
                    if (attrDef.getIsCiDisp() != null && attrDef.getIsCiDisp() > 0) {
                        labelAttrs.add(attrDef.getProStdName());
                    }
                }
                labelAttrMap.put(classId, labelAttrs);
            }
        }
        Set<Long> unknownCiIds = new HashSet<>();
        Map<Long, CcCiInfo> finalCiMap = new HashMap<>();
        for (Long entryCiId : groupCiMap.keySet()) {
            Map<Long, Set<Long>> ciMap = groupCiMap.get(entryCiId);
            for (Long pageNodeId : ciMap.keySet()) {
                for (Long ciId : ciMap.get(pageNodeId)) {
                    if (!nodeLegalCiInfoMap.containsKey(pageNodeId)
                            || !nodeLegalCiInfoMap.get(pageNodeId).get(0).containsKey(ciId)) {
                        unknownCiIds.add(ciId);
                        if (unknownCiIds.size() == 200) {
                            List<Long> ciIds = new ArrayList<>();
                            for (Long id : unknownCiIds) {
                                ciIds.add(id);
                            }
                            CCcCi cdt = new CCcCi();
                            cdt.setDomainId(domainId);
                            cdt.setIds(ciIds.toArray(new Long[ciIds.size()]));
                            Page<CcCiInfo> page = iciSvc.queryCiInfoPage(domainId, 1, 200, cdt, null, true, false);
                            if (page.getData() != null) {
                                for (CcCiInfo esciInfo : page.getData()) {
                                    JSONArray ciLabel = new JSONArray();
                                    for (String labelKey : labelAttrMap.get(esciInfo.getCi().getClassId())) {
                                        if (esciInfo.getAttrs().containsKey(labelKey)) {
                                            ciLabel.add(esciInfo.getAttrs().get(labelKey));
                                        } else {
                                            ciLabel.add("");
                                        }
                                    }
                                    esciInfo.getCi().setCiLabel(ciLabel.toString());
                                    finalCiMap.put(esciInfo.getCi().getId(), esciInfo);
                                }
                            }
                            unknownCiIds = new HashSet<>();
                        }
                    } else {
                        CcCiInfo ciInfo = nodeLegalCiInfoMap.get(pageNodeId).get(0).get(ciId);
                        finalCiMap.put(ciId, ciInfo);
                    }
                }
            }
        }
        if (unknownCiIds.size() > 0) {
            List<Long> ciIds = new ArrayList<>();
            for (Long id : unknownCiIds) {
                ciIds.add(id);
            }
            CCcCi cdt = new CCcCi();
            cdt.setDomainId(domainId);
            cdt.setIds(ciIds.toArray(new Long[ciIds.size()]));
            Page<CcCiInfo> page = iciSvc.queryCiInfoPage(domainId, 1, 200, cdt, null, true, false);
            if (page.getData() != null) {
                for (CcCiInfo esciInfo : page.getData()) {
                    JSONArray ciLabel = new JSONArray();
                    for (String labelKey : labelAttrMap.get(esciInfo.getCi().getClassId())) {
                        if (esciInfo.getAttrs().containsKey(labelKey)) {
                            ciLabel.add(esciInfo.getAttrs().get(labelKey));
                        } else {
                            ciLabel.add("");
                        }
                    }
                    esciInfo.getCi().setCiLabel(ciLabel.toString());
                    finalCiMap.put(esciInfo.getCi().getId(), esciInfo);
                }
            }
        }
        log.info("formatForTraversal.queryPageByCodes.time====" + (System.currentTimeMillis() - now));

        // 返回FriendInfo格式
        for (Long entryCiId : groupCiMap.keySet()) {
            Map<Long, ESCIRltInfo> ciRltLineMap = new HashMap<>();

            for (Long pageLineId : groupRelGroupMap.get(entryCiId).keySet()) {
                for (Long ciId : groupRelGroupMap.get(entryCiId).get(pageLineId).get("start").keySet()) {
                    for (Long nextCiId : groupRelGroupMap.get(entryCiId).get(pageLineId).get("start").get(ciId)
                            .keySet()) {
                        for (Long relId : groupRelGroupMap.get(entryCiId).get(pageLineId).get("start").get(ciId)
                                .get(nextCiId).keySet()) {
                            ESCIRltInfo rel = groupRelGroupMap.get(entryCiId).get(pageLineId).get("start").get(ciId)
                                    .get(nextCiId).get(relId);
                            ciRltLineMap.put(rel.getId(), rel);
                        }
                    }
                }
            }
            FriendInfo friendInfo = new FriendInfo();
            List<CcCiInfo> ciNodes = new ArrayList<>();
            Set<Long> ciSet = new HashSet<>();
            Map<Long, Set<Long>> ciMap = groupCiMap.get(entryCiId);
            for (Long pageNodeId : ciMap.keySet()) {
                ciSet.addAll(ciMap.get(pageNodeId));
            }
            for (Long ciId : ciSet) {
                if (finalCiMap.containsKey(ciId) && classMap.containsKey(finalCiMap.get(ciId).getCi().getClassId())) {
                    CcCiInfo esciInfo = finalCiMap.get(ciId);
                    ciNodes.add(esciInfo);

                }
            }

            List<ESCIRltInfo> ciRltLines = new ArrayList<>();
            for (Long key : ciRltLineMap.keySet()) {
                ciRltLines.add(ciRltLineMap.get(key));
            }
            List<CcCiClassInfo> ciClassInfos = new ArrayList<>();
            for (Long classId : ruleClassIds) {
                if (classMap.containsKey(classId)) {
                    ciClassInfos.add(classMap.get(classId));
                }
            }
            friendInfo.setCiIdByNodeMap(ciMap);
            friendInfo.setCiNodes(ciNodes);
            friendInfo.setCiRltLines(ciRltLines);
            friendInfo.setCiClassInfos(ciClassInfos);
            ret.put(finalCiMap.get(entryCiId).getCi().getId(), friendInfo);
        }
        return ret;
    }

    private void restrictDataForTraversal(Map<Long, List<QueryCondition>> travalTreeMap,
                                          Map<Long, List<Map<String, Restriction>>> restrictionMap,
                                          Map<Long, QueryCondition> lineMap,
                                          Map<Long, Set<Long>> ciMap,
                                          Map<Long, Map<String, Map<Long, Map<Long, Map<Long, ESCIRltInfo>>>>> relGroupMap) {
        while (true) {
            Boolean changed = false;

            Map<Long, Set<Long>> illegalCisMap = new HashMap<>();
            for (Long nodeId : restrictionMap.keySet()) {
                // 对每个节点的操作
                Set<Long> illegalCis = new HashSet<>();
                for (Long ci : ciMap.get(nodeId)) {
                    // 对一个节点包含的所有ci的操作
                    Boolean legal = true;
                    for (Map<String, Restriction> restriction : restrictionMap.get(nodeId)) {
                        Map<String, Boolean> logicValMap = new HashMap<String, Boolean>();
                        for (int i = 0; ; i++) {
                            if (restriction.containsKey("logic" + i)) {
                                // 如果限制条件还没用完
                                Restriction rest = restriction.get("logic" + i);
                                for (String childId : rest.getChildIds()) {
                                    // 对限制条件中的每个childId操作
                                    Boolean judge = null;
                                    if (childId.startsWith("logic")) {
                                        // 判断节点是否包含一个更深的分支，包含则调用更深分支的判断结果
                                        judge = logicValMap.get(childId);
                                    } else {
                                        QueryCondition qc = lineMap.get(Long.parseLong(childId));
                                        Map<Long, Map<Long, ESCIRltInfo>> rels = relGroupMap
                                                .get(Long.parseLong(childId))
                                                .get(nodeId.equals(qc.getStartPageId()) ? "start" : "end").get(ci);
                                        // rels中包含的以指定ci为起点的所有rel
                                        int size = rels == null ? 0 : rels.size();
                                        Long sourcePageId = nodeId.equals(qc.getStartPageId()) ? qc.getStartPageId() : qc.getEndPageId();
                                        Boolean existLine = false;
                                        // 判断childId是否包含在travalTreeMap的pageId中
                                        if (travalTreeMap.containsKey(sourcePageId)) {
                                            for (QueryCondition cond : travalTreeMap.get(sourcePageId)) {
                                                if (cond.getPageId().equals(Long.parseLong(childId))) {
                                                    existLine = true;
                                                    break;
                                                }
                                            }
                                        }
                                        if (rest.getIsAnd()) {
                                            // 如果限制的关系是and
                                            if (existLine) {
                                                if (qc.getOp().equals(2)) {
                                                    judge = size >= qc.getVal()[0];
                                                } else if (qc.getOp().equals(3)) {
                                                    judge = size <= qc.getVal()[0];
                                                } else if (qc.getOp().equals(1)) {
                                                    judge = size >= qc.getVal()[0] && size <= qc.getVal()[1];
                                                }
                                            } else {
                                                judge = size > 0;
                                            }
                                        } else {
                                            // 如果限制的关系是or
                                            if (existLine) {
                                                if (qc.getOp().equals(2)) {
                                                    judge = size >= qc.getVal()[0];
                                                } else if (qc.getOp().equals(3)) {
                                                    judge = size <= qc.getVal()[0];
                                                } else if (qc.getOp().equals(1)) {
                                                    judge = size >= qc.getVal()[0] && size <= qc.getVal()[1];
                                                }
                                            } else {
                                                judge = true;
                                            }
                                        }
                                    }
                                    if (rest.getIsAnd() && !judge) {
                                        // 为and时如果有一个child不符合则直接记为false
                                        logicValMap.put("logic" + i, false);
                                        break;
                                    } else if (!rest.getIsAnd() && judge) {
                                        // 为or时如果有一个child符合则直接记为true
                                        logicValMap.put("logic" + i, true);
                                        break;
                                    }
                                }
                                if (!logicValMap.containsKey("logic" + i)) {
                                    // 上述判断结束如果没有记录，如是and则为true，为or则为false
                                    if (rest.getIsAnd()) {
                                        logicValMap.put("logic" + i, true);
                                    } else {
                                        logicValMap.put("logic" + i, false);
                                    }
                                }
                            } else {
                                legal = legal && logicValMap.get("logic" + (i - 1));
                                break;
                            }
                        }
                        if (!legal) {
                            break;
                        }
                    }

                    if (!legal) {
                        // 如果有任意一个约束里面的一级为illegal，则将ci记为illegal，Map做过改动
                        changed = true;
                        illegalCis.add(ci);
                    }
                }
                illegalCisMap.put(nodeId, illegalCis);
            }

            if (changed) {
                for (Long nodeId : illegalCisMap.keySet()) {
                    // 清CI
                    Set<Long> nodeCis = ciMap.get(nodeId);
                    for (Long ci : illegalCisMap.get(nodeId)) {
                        // 对每个节点移除ciMap中的illegalde的ci
                        nodeCis.remove(ci);
                    }
                    // 清关系
                    for (Long key : travalTreeMap.keySet()) {
                        for (QueryCondition qc : travalTreeMap.get(key)) {
                            Map<String, Map<Long, Map<Long, Map<Long, ESCIRltInfo>>>> lineRels = relGroupMap
                                    .get(qc.getPageId());
                            if (nodeId.equals(qc.getStartPageId())) {
                                for (Long ci : illegalCisMap.get(nodeId)) {
                                    if (lineRels.get("start").containsKey(ci)) {
                                        for (Long endCi : lineRels.get("start").get(ci).keySet()) {
                                            lineRels.get("end").get(endCi).remove(ci);
                                        }
                                        lineRels.get("start").remove(ci);
                                    }
                                }
                            } else if (nodeId.equals(qc.getEndPageId())) {
                                for (Long ci : illegalCisMap.get(nodeId)) {
                                    if (lineRels.get("end").containsKey(ci)) {
                                        for (Long startCi : lineRels.get("end").get(ci).keySet()) {
                                            lineRels.get("start").get(startCi).remove(ci);
                                        }
                                        lineRels.get("end").remove(ci);
                                    }
                                }
                            }
                        }
                    }
                }
            } else {
                // 如果上次循环没有过更改，则结束
                break;
            }
        }
    }

    /**
     * 节点对应的关系（包括分类节点和逻辑节点）
     *
     * @param lines
     * @return
     */
    private List<Map<Long, List<RltRuleInfo.LineInfo>>> generateUpAndDownRelMap(List<RltRuleInfo.LineInfo> lines,
                                                                                Set<Long> ruleClassIds) {
        Map<Long, List<RltRuleInfo.LineInfo>> upMap = new HashMap<>();
        Map<Long, List<RltRuleInfo.LineInfo>> downMap = new HashMap<>();
        for (RltRuleInfo.LineInfo lineInfo : lines) {
            RltRuleInfo.Line line = lineInfo.getLine();
            if (line.getLineType() == 1 && ruleClassIds != null) {
                ruleClassIds.add(line.getClsRltId());
            }
            Long startId = line.getNodeStartId();
            Long endId = line.getNodeEndId();
            List<RltRuleInfo.LineInfo> upSet = upMap.computeIfAbsent(endId, k -> new ArrayList<>());
            upSet.add(lineInfo);
            List<RltRuleInfo.LineInfo> downSet = downMap.computeIfAbsent(startId, k -> new ArrayList<>());
            downSet.add(lineInfo);
        }
        List<Map<Long, List<RltRuleInfo.LineInfo>>> ret = new ArrayList<>();
        ret.add(upMap);
        ret.add(downMap);
        return ret;
    }

    /**
     * 页面节点编号对应节点
     *
     * @param nodes
     * @return
     */
    private Map<Long, RltRuleInfo.NodeInfo> generateNodeMap(List<RltRuleInfo.NodeInfo> nodes,
                                                            Set<Long> ruleClassIds) {
        // 以PageNodeId为键值存储nodes
        Map<Long, RltRuleInfo.NodeInfo> nodeMap = new HashMap<>();
        for (RltRuleInfo.NodeInfo info : nodes) {
            RltRuleInfo.Node node = info.getNode();
            if (node.getNodeType() == 1 && ruleClassIds != null) {
                ruleClassIds.add(node.getClassId());
            }
            nodeMap.put(node.getPageNodeId(), info);
        }
        return nodeMap;
    }

    /**
     * 分类节点对应影响自己的关系约束
     *
     * @param entryPageId 节点页面对应编号
     * @param upMap
     * @param downMap
     * @param nodeMap
     * @return
     */
    public Map<Long, List<QueryCondition>> generateTravalTree(Long entryPageId, Map<Long, List<RltRuleInfo.LineInfo>> upMap, Map<Long, List<RltRuleInfo.LineInfo>> downMap, Map<Long, RltRuleInfo.NodeInfo> nodeMap) {
        Map<Long, List<QueryCondition>> ret = new HashMap<>();

        List<List<Object>> foundPaths = new ArrayList<>();
        // 遍历时存储节点Id的队列
        LinkedList<Long> queue = new LinkedList<>();
        queue.addFirst(entryPageId);
        while (!queue.isEmpty()) {
            Long id = queue.removeLast();
            List<List<List<Object>>> upAndDownPaths = findPathByNode(id, upMap, downMap, nodeMap);

            // 向下寻找
            List<QueryCondition> downTravalConditions = new ArrayList<>();
            for (List<Object> path : upAndDownPaths.get(1)) {
                Boolean found = false;
                for (List<Object> foundPath : foundPaths) {
                    // 判断是否是已经找到过的完全相同的路径
                    if (path.size() == foundPath.size()) {
                        Boolean b = true;
                        for (int i = 0; i < path.size(); i++) {
                            if (i % 2 == 0) {
                                Long pageNodeId = ((RltRuleInfo.NodeInfo) path.get(i)).getNode().getPageNodeId();
                                Long foundPageNodeId = ((RltRuleInfo.NodeInfo) foundPath.get(i)).getNode()
                                        .getPageNodeId();
                                if (!pageNodeId.equals(foundPageNodeId)) {
                                    b = false;
                                    break;
                                }
                            }
                        }
                        if (b) {
                            found = true;
                            break;
                        }
                    }
                }
                if (!found) {
                    Long pageId = null;
                    Integer direction = null;
                    Integer[] val = null;
                    Integer op = null;
                    Long rltClsId = null;
                    List<RltRuleInfo.LineCdt> lineCdts = null;
                    for (int i = 1; i < path.size() - 1; i++) {
                        // 判断路径上的lines
                        if (i % 2 == 1) {
                            Integer d = ((RltRuleInfo.LineInfo) path.get(i)).getLine().getLineDirect();
                            // 获取line起始还是终止
                            if (d != null) {
                                pageId = ((RltRuleInfo.LineInfo) path.get(i)).getLine().getPageLineId();
                                direction = d;
                                // 拆分值至数组
                                String[] vals = ((RltRuleInfo.LineInfo) path.get(i)).getLine().getLineVal()
                                        .split(",");
                                val = new Integer[vals.length];
                                for (int j = 0; j < vals.length; j++) {
                                    val[j] = Integer.parseInt(vals[j]);
                                }
                                op = ((RltRuleInfo.LineInfo) path.get(i)).getLine().getLineOp();
                                rltClsId = ((RltRuleInfo.LineInfo) path.get(i)).getLine().getClsRltId();
                                lineCdts = ((RltRuleInfo.LineInfo) path.get(i)).getLineCdts();
                                break;
                            }
                        }
                    }
                    if (direction == 2) {
                        // 如果line是找结束
                        foundPaths.add(path);
                        Long nextId = ((RltRuleInfo.NodeInfo) path.get(path.size() - 1)).getNode().getPageNodeId();
                        QueryCondition qc = new QueryCondition();
                        qc.setPageId(pageId);
                        qc.setStartPageId(id);
                        qc.setEndPageId(nextId);
                        qc.setVal(val);
                        qc.setOp(op);
                        qc.setRltClsId(rltClsId);
                        qc.setLineCdts(lineCdts);
                        downTravalConditions.add(qc);
                        queue.addFirst(nextId);
                    }
                }
            }
            List<QueryCondition> upTravalConditions = new ArrayList<>();
            for (List<Object> path : upAndDownPaths.get(0)) {
                Boolean found = false;
                for (List<Object> foundPath : foundPaths) {
                    if (path.size() == foundPath.size()) {
                        Boolean b = true;
                        for (int i = 0; i < path.size(); i++) {
                            if (i % 2 == 0) {
                                if (!((RltRuleInfo.NodeInfo) path.get(i)).getNode().getPageNodeId()
                                        .equals(((RltRuleInfo.NodeInfo) foundPath.get(foundPath.size() - i - 1))
                                                .getNode().getPageNodeId())) {
                                    b = false;
                                    break;
                                }
                            }
                        }
                        if (b) {
                            found = true;
                            break;
                        }
                    }
                }
                if (!found) {
                    Long pageId = null;
                    Integer direction = null;
                    Integer[] val = null;
                    Integer op = null;
                    Long rltClsId = null;
                    List<RltRuleInfo.LineCdt> lineCdts = null;
                    for (int i = 1; i < path.size() - 1; i++) {
                        if (i % 2 == 1) {
                            Integer d = ((RltRuleInfo.LineInfo) path.get(i)).getLine().getLineDirect();
                            if (d != null) {
                                pageId = ((RltRuleInfo.LineInfo) path.get(i)).getLine().getPageLineId();
                                direction = d;
                                String[] vals = ((RltRuleInfo.LineInfo) path.get(i)).getLine().getLineVal()
                                        .split(",");
                                val = new Integer[vals.length];
                                for (int j = 0; j < vals.length; j++) {
                                    val[j] = Integer.parseInt(vals[j]);
                                }
                                op = ((RltRuleInfo.LineInfo) path.get(i)).getLine().getLineOp();
                                rltClsId = ((RltRuleInfo.LineInfo) path.get(i)).getLine().getClsRltId();
                                lineCdts = ((RltRuleInfo.LineInfo) path.get(i)).getLineCdts();
                                break;
                            }
                        }
                    }
                    if (direction == 1) {
                        path = Lists.reverse(path);
                        foundPaths.add(path);
                        Long nextId = ((RltRuleInfo.NodeInfo) path.get(0)).getNode().getPageNodeId();
                        QueryCondition qc = new QueryCondition();
                        qc.setPageId(pageId);
                        qc.setStartPageId(nextId);
                        qc.setEndPageId(id);
                        qc.setVal(val);
                        qc.setOp(op);
                        qc.setRltClsId(rltClsId);
                        qc.setLineCdts(lineCdts);
                        upTravalConditions.add(qc);
                        queue.addFirst(nextId);
                    }
                }
            }
            List<QueryCondition> travalConditionList = new ArrayList<>();
            travalConditionList.addAll(downTravalConditions);
            travalConditionList.addAll(upTravalConditions);
            if (travalConditionList.size() > 0) {
                ret.put(id, travalConditionList);
            }
        }

        return ret;
    }

    /**
     * 记录id对应的节点到下一个分类节点之间的点和线
     *
     * @param id      节点页面编号
     * @param upMap
     * @param downMap
     * @param nodeMap
     * @return 第一层list时up和down，第二层list多条路径，第三层点或线
     */
    private List<List<List<Object>>> findPathByNode(Long id, Map<Long, List<RltRuleInfo.LineInfo>> upMap,
                                                    Map<Long, List<RltRuleInfo.LineInfo>> downMap, Map<Long, RltRuleInfo.NodeInfo> nodeMap) {
        LinkedList<List<Object>> q = new LinkedList<List<Object>>();
        List<Object> l = new ArrayList<Object>();
        l.add(nodeMap.get(id));
        q.addFirst(l);
        List<List<Object>> downPaths = new ArrayList<List<Object>>();
        while (!q.isEmpty()) {
            List<Object> path = q.removeLast();
            RltRuleInfo.NodeInfo lastNode = (RltRuleInfo.NodeInfo) path.get(path.size() - 1);
            if (downMap.containsKey(lastNode.getNode().getPageNodeId())) {
                for (RltRuleInfo.LineInfo line : downMap.get(lastNode.getNode().getPageNodeId())) {
                    RltRuleInfo.NodeInfo nextNode = nodeMap.get(line.getLine().getNodeEndId());
                    List<Object> newPath = new ArrayList<Object>();
                    newPath.addAll(path);
                    newPath.add(line);
                    newPath.add(nextNode);
                    if (nextNode.getNode().getNodeType() == 1) {
                        // 找到分类节点为止
                        downPaths.add(newPath);
                    } else {
                        q.addFirst(newPath);
                    }
                }
            }
        }

        q.addFirst(l);
        List<List<Object>> upPaths = new ArrayList<List<Object>>();
        while (!q.isEmpty()) {
            List<Object> path = q.removeLast();
            RltRuleInfo.NodeInfo lastNode = (RltRuleInfo.NodeInfo) path.get(path.size() - 1);
            if (upMap.containsKey(lastNode.getNode().getPageNodeId())) {
                for (RltRuleInfo.LineInfo line : upMap.get(lastNode.getNode().getPageNodeId())) {
                    RltRuleInfo.NodeInfo nextNode = nodeMap.get(line.getLine().getNodeStartId());
                    List<Object> newPath = new ArrayList<Object>();
                    for (Object o : path) {
                        newPath.add(o);
                    }
                    newPath.add(line);
                    newPath.add(nextNode);
                    if (nextNode.getNode().getNodeType() == 1) {
                        // 找到分类节点为止
                        upPaths.add(newPath);
                    } else {
                        q.addFirst(newPath);
                    }
                }
            }
        }
        List<List<List<Object>>> ret = new ArrayList<List<List<Object>>>();
        ret.add(upPaths);
        ret.add(downPaths);
        return ret;
    }

    /**
     * 根据条件查询ci
     *
     * @param classMap        分类
     * @param nodeInfo        节点
     * @param ciIds
     * @param queryLegalMap
     * @param queryIllegalMap
     */
    private void queryCiByNodeCdt(Map<Long, CcCiClassInfo> classMap, RltRuleInfo.NodeInfo nodeInfo, Set<Long> ciIds,
                                  Map<Long, CcCiInfo> queryLegalMap, Map<Long, CcCiInfo> queryIllegalMap, Map<Long, List<Long>> classChilders, Long domainId, ICISvc iciSvc) {
        Set<String> labelAttrs = new HashSet<>();
        if (classMap.containsKey(nodeInfo.getNode().getClassId())) {
            for (CcCiAttrDef attrDef : classMap.get(nodeInfo.getNode().getClassId()).getAttrDefs()) {
                if (attrDef.getIsCiDisp() != null && attrDef.getIsCiDisp() > 0) {
                    labelAttrs.add(attrDef.getProStdName());
                }
            }
        }
        Map<String, List<ESAttrBean>> stringListMap = generateAttrBeansByNodeCdtNew(classMap, nodeInfo);
        List<ESAttrBean> orBeans = stringListMap.get("OR");
        List<ESAttrBean> attrBeans = stringListMap.get("AND");
        if (!attrBeans.isEmpty() || !orBeans.isEmpty()) {
            Map<Long, CcCiInfo> ciInfoMap = new HashMap<>();
            ESCISearchBean searchCI = new ESCISearchBean();
            if (!attrBeans.isEmpty()) {
                searchCI.setAndAttrs(attrBeans);
            }
            if (!orBeans.isEmpty()) {
                searchCI.setOrAttrs(orBeans);
            }
            searchCI.setDomainId(domainId);
            List<Long> classIds = new ArrayList<Long>();
            classIds.add(nodeInfo.getNode().getClassId());
            if (classChilders.get(nodeInfo.getNode().getClassId()) != null) {
                classIds.addAll(classChilders.get(nodeInfo.getNode().getClassId()));
            }
            searchCI.setClassIds(classIds);
            searchCI.setPageSize(1000);
            int pageNum = 1;
            while (true) {
                searchCI.setPageNum(pageNum);
                CcCiSearchPage page = iciSvc.searchCIByBean(searchCI);
                if (page.getData() != null && page.getData().getRecords() != null) {
                    for (CcCiObj esciInfo : page.getData().getRecords()) {
                        JSONArray ciLabel = new JSONArray();
                        for (String labelKey : labelAttrs) {
                            if (esciInfo.getAttrs().containsKey(labelKey)) {
                                ciLabel.add(esciInfo.getAttrs().get(labelKey));
                            } else {
                                ciLabel.add("");
                            }
                        }
                        CcCiInfo ciInfo = new CcCiInfo();
                        ciInfo.setCi(esciInfo.getCi());
                        ciInfo.setAttrs(esciInfo.getAttrs());
                        ciInfo.getCi().setCiLabel(ciLabel.toString());
                        ciInfoMap.put(ciInfo.getCi().getId(), ciInfo);
                    }
                }
                if (pageNum >= page.getTotalPages()) {
                    break;
                }
                pageNum++;
            }
            for (Long ciId : ciIds) {
                if (ciInfoMap.containsKey(ciId)) {
                    queryLegalMap.put(ciId, ciInfoMap.get(ciId));
                } else {
                    queryIllegalMap.put(ciId, ciInfoMap.get(ciId));
                }
            }
        } else {
            List<Long> ccs = new ArrayList<>();
            for (Long ciId : ciIds) {
                ccs.add(ciId);
                if (ccs.size() == 200) {
                    CCcCi cdt = new CCcCi();
                    cdt.setDomainId(domainId);
                    cdt.setIds(ccs.toArray(new Long[0]));
                    Page<CcCiInfo> page = iciSvc.queryCiInfoPage(domainId, 1, 200, cdt, null, true, false);
                    if (page.getData() != null) {
                        for (CcCiInfo ciObj : page.getData()) {
                            JSONArray ciLabel = new JSONArray();
                            for (String labelKey : labelAttrs) {
                                if (ciObj.getAttrs().containsKey(labelKey)) {
                                    ciLabel.add(ciObj.getAttrs().get(labelKey));
                                } else {
                                    ciLabel.add("");
                                }
                            }
                            ciObj.getCi().setCiLabel(ciLabel.toString());
                            queryLegalMap.put(ciObj.getCi().getId(), ciObj);
                        }
                    }
                    ccs = new ArrayList<>();
                }
            }
            if (ccs.size() > 0) {
                CCcCi cdt = new CCcCi();
                cdt.setDomainId(domainId);
                cdt.setIds(ccs.toArray(new Long[0]));
                Page<CcCiInfo> page = iciSvc.queryCiInfoPage(domainId, 1, 200, cdt, null, true, false);
                if (page.getData() != null) {
                    for (CcCiInfo ciObj : page.getData()) {
                        queryLegalMap.put(ciObj.getCi().getId(), ciObj);
                    }
                }
            }
        }
    }

    private List<ESAttrBean> generateAttrBeansByNodeCdt(Map<Long, CcCiClassInfo> classMap, RltRuleInfo.NodeInfo nodeInfo) {
        Map<Long, String> attrMap = new HashMap<Long, String>();
        if (classMap.containsKey(nodeInfo.getNode().getClassId())) {
            for (CcCiAttrDef attrDef : classMap.get(nodeInfo.getNode().getClassId()).getAttrDefs()) {
                attrMap.put(attrDef.getId(), attrDef.getProStdName());
            }
        }
        List<ESAttrBean> attrBeans = new ArrayList<>();
        for (RltRuleInfo.NodeCdt nodeCdt : nodeInfo.getNodeCdts()) {
            if (attrMap.containsKey(nodeCdt.getAttrId())) {

                ESAttrBean ab = new ESAttrBean();
                if (nodeCdt.getCdtOp() == 1) {
                    // =
                    ab.setKey(attrMap.get(nodeCdt.getAttrId()));
                    ab.setOptType(1);
                    ab.setValue(nodeCdt.getCdtVal());
                    attrBeans.add(ab);
                } else if (nodeCdt.getCdtOp() == 2) {
                    // like
                    ab.setKey(attrMap.get(nodeCdt.getAttrId()));
                    ab.setOptType(2);
                    ab.setValue(nodeCdt.getCdtVal());
                    attrBeans.add(ab);
                } else if (nodeCdt.getCdtOp() == 3) {
                    // >
                    ab.setKey(attrMap.get(nodeCdt.getAttrId()));
                    ab.setOptType(3);
                    ab.setValue(Double.parseDouble(nodeCdt.getCdtVal()));
                    attrBeans.add(ab);
                } else if (nodeCdt.getCdtOp() == 4) {
                    // >=
                    ab.setKey(attrMap.get(nodeCdt.getAttrId()));
                    ab.setOptType(4);
                    ab.setValue(Double.parseDouble(nodeCdt.getCdtVal()));
                    attrBeans.add(ab);
                } else if (nodeCdt.getCdtOp() == 5) {
                    // <
                    ab.setKey(attrMap.get(nodeCdt.getAttrId()));
                    ab.setOptType(5);
                    ab.setValue(Double.parseDouble(nodeCdt.getCdtVal()));
                    attrBeans.add(ab);
                } else if (nodeCdt.getCdtOp() == 6) {
                    // <=
                    ab.setKey(attrMap.get(nodeCdt.getAttrId()));
                    ab.setOptType(6);
                    ab.setValue(Double.parseDouble(nodeCdt.getCdtVal()));
                    attrBeans.add(ab);
                }
            }
        }
        return attrBeans;
    }


    /**
     *  node节点 增加or的关联关系
     * @param classMap
     * @param nodeInfo
     * @return
     */
    private Map<String, List<ESAttrBean>> generateAttrBeansByNodeCdtNew(Map<Long, CcCiClassInfo> classMap, RltRuleInfo.NodeInfo nodeInfo) {
        Map<String, List<ESAttrBean>> resultMap = new HashMap<>();
        Map<Long, String> attrMap = classMap.getOrDefault(nodeInfo.getNode().getClassId(), new CcCiClassInfo())
                .getAttrDefs().stream()
                .collect(Collectors.toMap(CcCiAttrDef::getId, CcCiAttrDef::getProStdName));

        List<ESAttrBean> attrBeans = new ArrayList<>();
        List<ESAttrBean> orBeans = new ArrayList<>();

        for (RltRuleInfo.NodeCdt nodeCdt : nodeInfo.getNodeCdts()) {
            if (!attrMap.containsKey(nodeCdt.getAttrId())) {
                continue;
            }
            String relRlt = nodeCdt.getRelRlt();
            List<ESAttrBean> targetList = ("OR".equals(relRlt) && !BinaryUtils.isEmpty(relRlt)) ? orBeans : attrBeans;

            ESAttrBean ab = new ESAttrBean();
            ab.setKey(attrMap.get(nodeCdt.getAttrId()));

            switch (nodeCdt.getCdtOp()) {
                case 1: // =
                    ab.setOptType(1);
                    ab.setValue(nodeCdt.getCdtVal());
                    break;
                case 2: // like
                    ab.setOptType(2);
                    ab.setValue(nodeCdt.getCdtVal());
                    break;
                case 3: // >
                case 4: // >=
                case 5: // <
                case 6: // <=
                    ab.setOptType(nodeCdt.getCdtOp());
                    ab.setValue(Double.parseDouble(nodeCdt.getCdtVal()));
                    break;
                default:
                    continue;
            }
            targetList.add(ab);
        }
        resultMap.put("AND", attrBeans);
        resultMap.put("OR", orBeans);
        return resultMap;
    }
    private FriendInfo generateFriendInfoBySingleCiInfo(CcCiInfo ciInfo, Long startPageNodeId,
                                                        Map<Long, CcCiClassInfo> classMap, Set<Long> ruleClassIds) {
        FriendInfo friendInfo = new FriendInfo();
        Map<Long, Set<Long>> ciNodeMap = new HashMap<>();
        Set<Long> set = new HashSet<>();
        set.add(ciInfo.getCi().getId());
        ciNodeMap.put(startPageNodeId, set);
        friendInfo.setCiIdByNodeMap(ciNodeMap);
        List<CcCiClassInfo> classInfos = new ArrayList<>();
        for (Long ruleClassId : ruleClassIds) {
            if (classMap.containsKey(ruleClassId)) {
                classInfos.add(classMap.get(ruleClassId));
            }
        }
        friendInfo.setCiClassInfos(classInfos);
        List<CcCiInfo> ciNodes = new ArrayList<>();
        ciNodes.add(ciInfo);
        friendInfo.setCiNodes(ciNodes);

        friendInfo.setCiRltLines(Collections.emptyList());
        return friendInfo;
    }

    /**
     * 生成每个有效节点的restriction筛选条件
     *
     * @param travalTreeMap
     * @param upMap
     * @param downMap
     * @param nodeMap
     * @return 每个有效节点Id为键值，对应节点的restriction为value
     */
    private Map<Long, List<Map<String, Restriction>>> generateRestrictionForTraversal(
            Map<Long, List<QueryCondition>> travalTreeMap, Map<Long, List<RltRuleInfo.LineInfo>> upMap,
            Map<Long, List<RltRuleInfo.LineInfo>> downMap, Map<Long, RltRuleInfo.NodeInfo> nodeMap) {

        Map<Long, List<Map<String, Restriction>>> restrictionMap = new HashMap<>();
        Map<String, Long> legalLineMap = new HashMap<>();
        for (Long startPageId : travalTreeMap.keySet()) {
            for (QueryCondition qc : travalTreeMap.get(startPageId)) {
                // 以每个line的起始结束节点Id为key初始化restrictionMap
                // 以起始结束节点双向为key初始化legalLineMap
                restrictionMap.put(qc.getStartPageId(), new ArrayList<>());
                restrictionMap.put(qc.getEndPageId(), new ArrayList<>());
                legalLineMap.put(qc.getStartPageId() + "-" + qc.getEndPageId(), qc.getPageId());
                legalLineMap.put(qc.getEndPageId() + "-" + qc.getStartPageId(), qc.getPageId());
            }
        }

        Map<Long, Map<String, Set<Long>>> logicNodeMap = new HashMap<>();
        for (Long pageNodeId : restrictionMap.keySet()) {
            List<Map<String, Restriction>> restrictionList = restrictionMap.get(pageNodeId);

            List<List<List<Object>>> upAndDownPaths = findPathByNode(pageNodeId, upMap, downMap, nodeMap);
            // 查询pageNode到下一个非And&Or节点的路径，存在upAndDownPaths
            List<List<Map<String, String>>> simplePaths = new ArrayList<>();
            for (List<List<Object>> paths : upAndDownPaths) {
                for (List<Object> path : paths) {
                    // 节点之间的关系
                    Long linePageId = legalLineMap.get(((RltRuleInfo.NodeInfo) path.get(0)).getNode().getPageNodeId()
                            + "-" + ((RltRuleInfo.NodeInfo) path.get(path.size() - 1)).getNode().getPageNodeId());
                    // 通过pageNodeId-下一个非And&Or的pageNodeId，找到lingPageId
                    if (linePageId != null) {
                        List<Map<String, String>> simplePath = new ArrayList<Map<String, String>>();
                        for (int i = 2; i < path.size(); i++) {
                            if (i % 2 == 0) {
                                // 处理节点
                                RltRuleInfo.NodeInfo node = (RltRuleInfo.NodeInfo) path.get(i);
                                // 如果是And&Or
                                if (node.getNode().getNodeType() == 2) {

                                    Map<String, Set<Long>> lnMap = logicNodeMap.get(node.getNode().getPageNodeId());
                                    if (lnMap == null) {
                                        lnMap = generateLogicNodeSingleAndMultiple(node.getNode().getPageNodeId(),
                                                upMap, downMap, nodeMap);
                                        // 输出And&Or的单连接和多连接边
                                        logicNodeMap.put(node.getNode().getPageNodeId(), lnMap);
                                    }
                                    // 节点在单边，起作用
                                    if (lnMap.get("single").contains(pageNodeId)) {
                                        // 如果pageNode是连接单连接边，将andOr节点Id和分类放入simplePath
                                        Map<String, String> logicMap = new HashMap<String, String>();
                                        logicMap.put("name", node.getNode().getPageNodeId() + "");
                                        logicMap.put("andOr", node.getNode().getLogicOperation() + "");
                                        simplePath.add(logicMap);
                                    }
                                } else {
                                    // 如果不是And&Or，将节点Id放入simplePath
                                    Map<String, String> nm = new HashMap<String, String>();
                                    nm.put("name", node.getNode().getPageNodeId() + "");
                                    simplePath.add(nm);
                                }
                            }
                        }
                        if (simplePath.size() > 1) {
                            simplePaths.add(simplePath);
                        } else {
                            Restriction restriction = new Restriction();
                            List<String> childIds = new ArrayList<String>();
                            childIds.add(linePageId + "");
                            restriction.setChildIds(childIds);
                            restriction.setIsAnd(true);
                            Map<String, Restriction> rm = new HashMap<>();
                            rm.put("logic0", restriction);
                            restrictionList.add(rm);
                        }
                    }
                }
            }
            Map<String, Restriction> rm = new HashMap<>();
            int logicIndex = 0;
            while (simplePaths.size() > 0) {
                int maxDepthIndex = 0;
                for (int i = 0; i < simplePaths.size(); i++) {
                    // 找到最深的simplePath
                    if (simplePaths.get(i).size() > simplePaths.get(maxDepthIndex).size()) {
                        maxDepthIndex = i;
                    }
                }
                if (simplePaths.get(maxDepthIndex).size() > 1) {
                    List<Map<String, String>> lastNodes = new ArrayList<Map<String, String>>();
                    List<List<Map<String, String>>> newSimplePaths = new ArrayList<List<Map<String, String>>>();
                    for (List<Map<String, String>> path : simplePaths) {
                        // 判断path是否和最深路径一样深
                        if (path.size() == simplePaths.get(maxDepthIndex).size()) {
                            Boolean match = true;
                            for (int i = 0; i < path.size() - 1; i++) {
                                // 判断该path和最深的路径是否除了末尾节点是完全相同的分支，不是则match记false
                                if (!path.get(i).get("name")
                                        .equals(simplePaths.get(maxDepthIndex).get(i).get("name"))) {
                                    match = false;
                                    break;
                                }
                            }
                            if (match) {
                                lastNodes.add(path.get(path.size() - 1));
                            } else {
                                newSimplePaths.add(path);
                            }
                            // 属于同分支则将末尾节点记录，不属于同分支则将path加入下一次查找最深路径的集合
                        } else {
                            // 不一样深则将path加入下一次查找最深路径的集合
                            newSimplePaths.add(path);
                        }
                    }
                    // 将和最深路径同分支的路径合并
                    List<Map<String, String>> newLogicPath = new ArrayList<Map<String, String>>();
                    for (int i = 0; i < simplePaths.get(maxDepthIndex).size() - 1; i++) {
                        newLogicPath.add(simplePaths.get(maxDepthIndex).get(i));
                    }
                    Restriction restriction = new Restriction();
                    List<String> childIds = new ArrayList<String>();
                    for (Map<String, String> node : lastNodes) {
                        if (node.get("name").startsWith("logic")) {
                            childIds.add(node.get("name"));
                        } else {
                            childIds.add(legalLineMap.get(pageNodeId + "-" + node.get("name")) + "");
                        }
                    }
                    restriction.setChildIds(childIds);
                    restriction.setIsAnd("1".equals(newLogicPath.get(newLogicPath.size() - 1).get("andOr")));
                    String newLogicName = "logic" + logicIndex;
                    rm.put(newLogicName, restriction);
                    // 移除最后一位的andOr
                    newLogicPath.remove(newLogicPath.size() - 1);
                    Map<String, String> newLogicNode = new HashMap<String, String>();
                    newLogicNode.put("name", newLogicName);
                    newLogicPath.add(newLogicNode);
                    newSimplePaths.add(newLogicPath);
                    logicIndex++;
                    simplePaths = newSimplePaths;
                } else {
                    break;
                }
            }
            if (rm.size() > 0) {
                restrictionList.add(rm);
            }
        }
        return restrictionMap;
    }

    /**
     * 逻辑节点对应的单边和多边分类节点
     *
     * @param pageNodeId
     * @param upMap
     * @param downMap
     * @param nodeMap
     * @return
     */
    private Map<String, Set<Long>> generateLogicNodeSingleAndMultiple(Long pageNodeId,
                                                                      Map<Long, List<RltRuleInfo.LineInfo>> upMap, Map<Long, List<RltRuleInfo.LineInfo>> downMap,
                                                                      Map<Long, RltRuleInfo.NodeInfo> nodeMap) {
        // 输入：pageNodeId，upMap，downMap，nodeMap
        // 输出：single：单连接边，multiple：多连接边s
        Map<String, Set<Long>> logicNodeMap = new HashMap<String, Set<Long>>();
        List<List<List<Object>>> upAndDownPaths = findPathByNode(pageNodeId, upMap, downMap, nodeMap);
        List<List<Object>> singlePaths = upAndDownPaths.get(0).size() > 1 ? upAndDownPaths.get(1)
                : upAndDownPaths.get(0);
        List<List<Object>> multiplePaths = upAndDownPaths.get(0).size() > 1 ? upAndDownPaths.get(0)
                : upAndDownPaths.get(1);
        // 将连接And&Or的节点的单连接边放进single，多连接边放进multiple
        Set<Long> singleIds = new HashSet<Long>();
        singleIds.add(((RltRuleInfo.NodeInfo) singlePaths.get(0).get(singlePaths.get(0).size() - 1)).getNode()
                .getPageNodeId());
        logicNodeMap.put("single", singleIds);
        Set<Long> multipleIds = new HashSet<Long>();
        for (List<Object> path : multiplePaths) {
            multipleIds.add(((RltRuleInfo.NodeInfo) path.get(path.size() - 1)).getNode().getPageNodeId());
        }
        logicNodeMap.put("multiple", multipleIds);
        return logicNodeMap;
    }


}
