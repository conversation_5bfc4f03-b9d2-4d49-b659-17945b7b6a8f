package com.uinnova.product.eam.model.vo;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;
import java.io.Serializable;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 专题分析表格合并vo
 * <AUTHOR>
 */
@Data
public class EamAnalyseMergeVo implements Serializable {

    @Comment("标识名称")
    private String name;

    @Comment("起始行号")
    private Integer startNum;

    @Comment("终止行号")
    private Integer endNum;

    @Comment("终止标识")
    private Boolean theEnd = false;

    @Comment("计数")
    private AtomicInteger count = new AtomicInteger(0);

    public EamAnalyseMergeVo() {
        this.count.incrementAndGet();
    }

    public EamAnalyseMergeVo(String name) {
        this.name = name;
        this.count.incrementAndGet();
    }

    public EamAnalyseMergeVo(String name, Integer startNum) {
        this.name = name;
        this.startNum = startNum;
        this.endNum = startNum;
        this.count.incrementAndGet();
    }
}
