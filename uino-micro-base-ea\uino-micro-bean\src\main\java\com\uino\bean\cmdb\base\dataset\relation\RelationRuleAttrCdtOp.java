package com.uino.bean.cmdb.base.dataset.relation;

public enum RelationRuleAttrCdtOp {
	//
	Equal(1),
	Like(2);
	
	private Integer code;
	
	private RelationRuleAttrCdtOp(Integer code) {
		this.code = code;
	}
	
	public Integer getOp() {
		return this.code;
	}
	
	public static RelationRuleAttrCdtOp valueOf(Integer code) {
		if (Equal.code.equals(code)) {
            return Equal;
        } else if (Like.code.equals(code)) {
            return Like;
        } else {
            return null;
        }
	}
}
