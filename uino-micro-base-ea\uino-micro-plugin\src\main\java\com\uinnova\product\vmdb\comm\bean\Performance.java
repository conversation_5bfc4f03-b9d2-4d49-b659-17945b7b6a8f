package com.uinnova.product.vmdb.comm.bean;

import com.binary.core.util.BinaryUtils;

import java.io.Serializable;

/**
 * 
 * <AUTHOR>
 *
 */
public class Performance implements Serializable {
    private static final long serialVersionUID = -7850454637347220254L;

    private long id;
    private String ci;
    private String kpi;
    private String instance;
    private String value;
    private String desc;
    private Long time;
    private String unit;
    /**
     * 删除标识
     */
    private String deletieIdentifier; 
    private Long createTime;
    private Long modifyTime;

    private static final Long SYSTEM_TIME = System.currentTimeMillis();

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getCi() {
        return ci;
    }

    public void setCi(String ci) {
        this.ci = ci;
    }

    public String getKpi() {
        return kpi;
    }

    public void setKpi(String kpi) {
        this.kpi = kpi;
    }

    public String getInstance() {
        return instance;
    }

    public void setInstance(String instance) {
        this.instance = instance;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public Long getTime() {
        return time;
    }

    public void setTime(Long time) {
        if (BinaryUtils.isEmpty(time, true)) {
            time = SYSTEM_TIME;
        }
        this.time = time;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getDeletieIdentifier() {
        return deletieIdentifier;
    }

    public void setDeletieIdentifier(String deletieIdentifier) {
        this.deletieIdentifier = deletieIdentifier;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }

}
