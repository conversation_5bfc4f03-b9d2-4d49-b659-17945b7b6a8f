package com.uino.bean.permission.business;

import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 
 * <AUTHOR>
 *
 */
@Setter
@Getter
@ApiModel(value="CI分类数据项",description = "CI分类数据项")
public class DataRole implements Serializable {

    /**
     * 
     */
    private static final long serialVersionUID = 1L;

    /**
     * 唯一ID与前台交互
     */
    @ApiModelProperty(value="唯一ID与前台交互")
    private String uid;

    /**
     * 数据项名称
     */
    @ApiModelProperty(value="数据项名称")
    private String name;

    /**
     * 数据值
     */
    @ApiModelProperty(value="数据值")
    private String dataValue;

    /**
     * 子节点，树状使用
     */
    @ApiModelProperty(value="子节点，树状使用")
    private List<DataRole> children;

}
