package com.uinnova.product.eam.web.asset.bean;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;


@Data
public class SearchNonFunctionalVO {
    @Comment("前端传过来查询关键字")
    private String searchData;
    @Comment("处理前端查询关键字")
    private String[] words;
    private Integer pageSize;
    private Integer pageNum;
    @Comment("排序字段")
    private String sortField;
    private boolean asc;
}
