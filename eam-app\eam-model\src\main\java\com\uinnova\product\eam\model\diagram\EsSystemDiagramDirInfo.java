package com.uinnova.product.eam.model.diagram;

import com.binary.framework.bean.annotation.Comment;
import com.binary.jdbc.Page;
import com.uinnova.product.eam.base.diagram.model.ESSimpleDiagramDTO;
import com.uinnova.product.eam.model.vo.EamDiagramDirVo;
import com.uinnova.product.eam.model.vo.VcDiagramDirVo;
import com.uinnova.product.vmdb.provider.ci.bean.CiGroupPage;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
public class EsSystemDiagramDirInfo implements Serializable{

    @Comment("当前文件夹信息")
    private EamDiagramDirVo diagramDir;

    @Comment("子文件夹信息")
    private List<VcDiagramDirVo> childrenDirs;

    @Comment("子文件夹视图数量信息")
    private Map<Long,Integer> dirDiagramCountMap;

    private Page<ESSimpleDiagramDTO> diagramInfoPage;

    @Comment("系统详情")
    private List<CiGroupPage> ciGroupPage;
}
