package com.uinnova.product.eam.model.enums;

public enum AssetType {

    //DIAGRAM-视图;MODEL-模型;SCHEME-方案

    DIAGRAM(1,"视图"),
    MODEL(2,"模型"),
    SCHEME(3,"方案"),

    //仅复制-移动有用到
    FOLDER(4, "文件夹"),

    TEMPLATE(5, "交付物模板"),
    CI(6,"资产ci"),

    MATRIX(7, "矩阵制品");

    public final int assetType ;
    public final String typeName ;
    AssetType(int assetType, String typeName) {
        this.assetType = assetType;
        this.typeName = typeName;
    }
}
