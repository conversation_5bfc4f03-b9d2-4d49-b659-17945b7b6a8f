package com.uino.service.plugin.init;

import com.uino.plugin.bean.OperatePluginDetails;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 插件分发加载
 *
 * 注：实现该接口请自行丰富OperatePluginDetails中地址和服务信息
 *
 * <AUTHOR>
 * @Date 2021/9/26
 * @Version 1.0
 */
public interface PluginDistributionManage {
    /**
     * 获取所有服务名称
     *
     * @return
     */
    List<String> getServiceList();

    /**
     * 同步插件
     * @return
     */
    boolean synchronizePluginFile(String jarName);

    /**
     * 上传插件
     *      上传插件地址：/currentServer/plugin/uploadPlugin
     * @param ownedService  插件所属服务
     * @param multipartFile 插件
     * @return  上传结果
     */
    List<OperatePluginDetails> uploadPlugin(List<String> ownedService, MultipartFile multipartFile);

    /**
     * 加载插件
     *      加载插件地址：/currentServer/plugin/loadPlugin
     * @param ownedService  插件所属服务
     * @param jarName   插件文件名称
     * @return  加载结果
     */
    List<OperatePluginDetails> loadPlugin(List<String> ownedService, String jarName);

    /**
     * 卸载插件
     *      卸载插件地址：/currentServer/plugin/unloadAndDeletePlugin
     * @param ownedService  插件所属服务
     * @param jarName   插件文件名称
     * @return  卸载结果
     */
    List<OperatePluginDetails> unloadAndDeletePlugin(List<String> ownedService, String jarName);
}
