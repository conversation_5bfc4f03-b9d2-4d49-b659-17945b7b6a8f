package com.uino.provider.feign.cmdb;

import java.util.Collection;
import java.util.List;
import java.util.Set;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClass;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uino.bean.cmdb.base.ESCIClassInfo;
import com.uino.bean.cmdb.business.ClassNodeInfo;
import com.uino.bean.cmdb.business.ClassReOrderDTO;
import com.uino.bean.cmdb.business.ImportResultMessage;
import com.uino.bean.cmdb.query.ESCIClassSearchBean;
import com.uino.provider.feign.config.BaseFeignConfig;

/**
 * 
 * <AUTHOR>
 *
 */
@Component
@FeignClient(name = "${feign-server-name:tarsier-platform}", path = "${feign-server-path:tarsier-platform}/feign/ciClass", configuration = {
        BaseFeignConfig.class})
public interface CIClassFeign {

    /**
     * 获取分类树(包含CI统计结果)
     * 
     * @param clsCdt
     * @return
     */
    @PostMapping("getClassTree")
    List<ClassNodeInfo> getClassTree(@RequestBody Long domainId);

    /**
     * 根据id查询CI分类-包含各模块扩展属性
     * 
     * @param id
     * @return
     */
    @PostMapping("queryESClassInfoById")
    ESCIClassInfo queryESClassInfoById(@RequestBody Long id);

    /**
     * 根据id查询CI分类
     * 
     * @param id
     * @return
     */
    @PostMapping("queryClassById")
    CcCiClassInfo queryClassById(@RequestBody Long id);

    /**
     * 根据条件查询CI分类信息
     * 
     * @return
     */
    @PostMapping("queryClassByCdtWithoutBody")
    List<CcCiClassInfo> queryClassByCdtWithoutBody();

    /**
     * 根据条件查询CI分类信息
     * 
     * @param cdt
     * @return
     */
    @PostMapping("queryClassByCdt")
    List<CcCiClassInfo> queryClassByCdt(@RequestBody CCcCiClass cdt);

    /**
     * 条件查询CI分类
     * 
     * @param domainId
     * @param orders
     * @param isAsc
     * @return
     */
    @PostMapping(value = "queryCiClassInfoListWithoutBody")
    List<CcCiClassInfo> queryCiClassInfoListWithoutBody(@RequestParam(value = "domainId", required = false) Long domainId, @RequestParam(value = "orders", required = false) String orders,
        @RequestParam(value = "isAsc", required = false) Boolean isAsc);

    /**
     * 条件查询CI分类
     * 
     * @param domainId
     * @param cdt
     * @param orders
     * @param isAsc
     * @return
     */
    @PostMapping(value = "queryCiClassInfoList")
    List<CcCiClassInfo> queryCiClassInfoList(@RequestParam(value = "domainId", required = false) Long domainId, @RequestBody CCcCiClass cdt,
        @RequestParam(value = "orders", required = false) String orders, @RequestParam(value = "isAsc", required = false) Boolean isAsc);

    /**
     * 条件查询CI分类
     * 
     * @param bean
     * @return
     */
    @PostMapping(value = "queryCiClassInfoListBySearchBean")
    List<CcCiClassInfo> queryCiClassInfoListBySearchBean(@RequestBody ESCIClassSearchBean bean);

    /**
     * 条件查询CI分类-包含各模块扩展属性
     * 
     * @param bean
     * @return
     */
    @PostMapping("queryESCiClassInfoListBySearchBean")
    List<ESCIClassInfo> queryESCiClassInfoListBySearchBean(@RequestBody ESCIClassSearchBean bean);

    /**
	 * 分页查询CI分类
	 * 
	 * @param bean
	 * @return
	 */
	@PostMapping("queryESCiClassInfoPageBySearchBean")
	Page<ESCIClassInfo> queryESCiClassInfoPageBySearchBean(@RequestBody ESCIClassSearchBean bean);

	/**
	 * 保存或更新CI分类
	 * 
	 * @param record
	 * @return
	 */
    @PostMapping("saveOrUpdateCIClass")
    Long saveOrUpdateCIClass(@RequestBody CcCiClassInfo record);

    /**
     * 保存或更新CI分类
     * 
     * @param record
     * @return
     */
    @PostMapping("saveOrUpdateESCIClass")
    Long saveOrUpdateESCIClass(@RequestBody ESCIClassInfo record);

    /**
     * 批量保存或更新分类
     * @param clsInfos
     * @return
     */
    @PostMapping("saveOrUpdateBatch")
    Integer saveOrUpdateBatch(@RequestParam(value = "domainId", required = false)Long domainId,@RequestBody List<CcCiClassInfo> clsInfos);

    /**
     * 根据id删除CI分类
     * 
     * @param id
     * @return
     */
    @PostMapping("removeCIClassById")
    Integer removeCIClassById(@RequestBody Long id);

    /**
     * 导入CI分类属性
     * 
     * @param file
     * @param classId
     * @return
     */
    @PostMapping(value = "importCiClassAttr", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    ImportResultMessage importCiClassAttr(@RequestPart(value = "file") MultipartFile file,
        @RequestParam(value = "classId") Long classId);

    /**
     * 导出分类属性模板
     * 
     * @param clsIds
     * @param isBatchTpl
     * @return
     */
    @PostMapping(value = "exportClassAttrExcel")
    ResponseEntity<byte[]> exportClassAttrExcel(@RequestBody Set<Long> clsIds,
        @RequestParam(value = "isBatchTpl", required = false) Boolean isBatchTpl);

    /**
     * ci分类拖动排序
     * @author: weixuesong
     * @date: 2020/8/5 14:24
     * @param reOrderDTO
     * @return: void
     */
    @PostMapping(value = "reOrder")
    boolean reOrder(@RequestBody ClassReOrderDTO reOrderDTO);

    /**
     * 初始化所有ci分类的orderNo
     * @author: weixuesong
     * @date: 2020/8/6 17:00
     * @return: boolean
     */
    @PostMapping(value = "initAllOrderNo")
    boolean initAllOrderNo(@RequestBody Long domainId);

    /**
     * 根据id查询分类-(属性返回实际存储名称：proName为显示名称；proStdName为实际存储名称)
     * 
     * @param classIds
     * @return
     */
    @PostMapping("getTargetAttrDefsByClassIds")
    List<ESCIClassInfo> getTargetAttrDefsByClassIds(@RequestParam(value = "domainId") Long domainId,@RequestBody Collection<Long> classIds);

	/**
	 * 获取属性tag列表
	 * 
	 * @return
	 */
	@PostMapping("queryAttrDefGroupList")
	List<String> queryAttrDefGroupList(@RequestBody Long domainId);

    /**
     * get http resource path
     *
     * @return custom path
     * */
    @PostMapping("getHttpResourceSpace")
    String getHttpResourceSpace();

    /**
     * whether to display the 3d model
     *
     * @return boolean
     * */
    @PostMapping("isShow3dAttribute")
    Boolean isShow3dAttribute();

    /**
     * query class info by dt class id
     *
     * @param dtClassIds dt class id
     * @param domainId do main id
     *
     * @return {@link List<ESCIClassInfo>}
     * */
    @PostMapping("queryEsClassInfoByDtClassIds")
    List<ESCIClassInfo> queryEsClassInfoByDtClassIds(@RequestBody List<String> dtClassIds, @RequestParam(value = "domainId") Long domainId);
}
