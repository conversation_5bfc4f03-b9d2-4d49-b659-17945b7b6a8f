package com.uinnova.product.eam.service.cj.service;

import com.uinnova.product.eam.model.cj.domain.PlanModuleDelete;
import com.uino.bean.permission.base.SysUser;

import java.util.List;

public interface PlanModuleDeleteService {

    /**
     * 新增方案模块信息
     * @param planModuleDelete
     */
    Long addPlanModule(PlanModuleDelete planModuleDelete, SysUser sysUser);

    /**
     * 获取模块删除信息
     * @param planId
     * @param moduleId
     * @return
     */
    List<PlanModuleDelete> findPlanModuleDeleteList(Long planId, List<Long> templateChapterIds, Long moduleId, String loginCode);

    /**
     * 查询是否模块已被删除
     * @param planModule
     * @param sysUser
     * @return
     */
    PlanModuleDelete getPlanModuleDelete(PlanModuleDelete planModule, SysUser sysUser);
}
