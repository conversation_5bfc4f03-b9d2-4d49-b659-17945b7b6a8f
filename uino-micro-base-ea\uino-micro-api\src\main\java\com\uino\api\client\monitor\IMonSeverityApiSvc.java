package com.uino.api.client.monitor;

import java.util.List;
import java.util.Set;

import org.springframework.web.multipart.MultipartFile;

import com.binary.core.io.Resource;
import com.uino.bean.cmdb.business.ImportResultMessage;
import com.uino.bean.monitor.base.ESMonSysSeverityInfo;

/**
 * 事件定义api
 * 
 * <AUTHOR>
 */
public interface IMonSeverityApiSvc {
    /**
     * 全文检索搜索告警定义
     * 
     * @param searchVal
     * @return
     */
    List<ESMonSysSeverityInfo> querySeverityList(String searchVal);
    List<ESMonSysSeverityInfo> querySeverityList(Long domainId, String searchVal);

    /**
     * 持久化告警定义
     * 
     * @param saveDto
     * @return
     */
    Long saveOrUpdateSeverity(ESMonSysSeverityInfo saveDto);

    /**
     * 根据告警定义ids删除告警定义
     * 
     * @param delIds
     */
    void deleteServrityByIds(Set<Long> delIds);

    /**
     * 导出告警定级数据
     * 
     * @param isTpl
     * @return
     */
    public Resource exportServrityInfos(Boolean isTpl);
    public Resource exportServrityInfos(Long domainId, Boolean isTpl);

    /**
     * 导入告警定级数据
     * 
     * @param file
     * @return
     */
    public ImportResultMessage importSeverityInfos(MultipartFile file);
    public ImportResultMessage importSeverityInfos(Long domainId, MultipartFile file);
}
