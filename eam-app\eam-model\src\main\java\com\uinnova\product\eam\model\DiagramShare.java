package com.uinnova.product.eam.model;

import java.io.Serializable;
import java.util.List;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.comm.model.VcTag;

public class DiagramShare implements Serializable {

	private static final long serialVersionUID = 1L;

	@Comment("视图ID")
	private Long diagramId;
	
	@Comment("是否公开")
	private Integer isOpen;
	
	@Comment("标签")
	private List<VcTag> tags;
	
	@Comment("小组的ID")
	private List<Long> groupIds;
	
	@Comment("批量视图ID")
	private List<Long> diagramIds;
	
	public List<Long> getDiagramIds() {
		return diagramIds;
	}

	public void setDiagramIds(List<Long> diagramIds) {
		this.diagramIds = diagramIds;
	}

	public Long getDiagramId() {
		return diagramId;
	}

	public void setDiagramId(Long diagramId) {
		this.diagramId = diagramId;
	}

	public List<VcTag> getTags() {
		return tags;
	}

	public void setTags(List<VcTag> tags) {
		this.tags = tags;
	}

	public List<Long> getGroupIds() {
		return groupIds;
	}

	public void setGroupIds(List<Long> groupIds) {
		this.groupIds = groupIds;
	}

	public Integer getIsOpen() {
		return isOpen;
	}

	public void setIsOpen(Integer isOpen) {
		this.isOpen = isOpen;
	}
	
	
}
