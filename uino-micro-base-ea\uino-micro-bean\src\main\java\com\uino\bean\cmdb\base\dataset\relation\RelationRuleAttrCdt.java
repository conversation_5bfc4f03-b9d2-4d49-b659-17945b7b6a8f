package com.uino.bean.cmdb.base.dataset.relation;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;

public class RelationRuleAttrCdt implements Serializable {

	private static final long serialVersionUID = 1L;

	private Long attrId;
	@JSONField(deserializeUsing = RelationRuleAttrCdtOpToInt.class)
	private RelationRuleAttrCdtOp op;
	private String value;
	private String relRlt;
	
	public RelationRuleAttrCdt() {
		
	}
	
	public RelationRuleAttrCdt(JSONObject json) {
		if (json.containsKey("attrId")) {
			this.attrId = json.getLong("attrId");
		}
		if (json.containsKey("op")) {
			this.op = RelationRuleAttrCdtOp.valueOf(json.getInteger("op"));
		}
		if (json.containsKey("value")) {
			this.value = json.getString("value");
		}
		if (json.containsKey("relRlt")) {
			this.relRlt = json.getString("relRlt");
		}

	}
	
	public Long getAttrId() {
		return attrId;
	}
	public void setAttrId(Long attrId) {
		this.attrId = attrId;
	}
	public RelationRuleAttrCdtOp getOp() {
		return op;
	}
	public void setOp(RelationRuleAttrCdtOp op) {
		this.op = op;
	}
	public String getValue() {
		return value;
	}
	public void setValue(String value) {
		this.value = value;
	}

	public String getRelRlt() {
		return relRlt;
	}

	public void setRelRlt(String relRlt) {
		this.relRlt = relRlt;
	}

	public JSONObject toJson() {
		JSONObject json = new JSONObject();
		json.put("attrId", attrId);
		if (this.op!=null) {
			json.put("op", op.getOp());
		}
		if (this.relRlt != null) {
			json.put("relRlt", relRlt);
		}
		json.put("value", value);
		return json;
	}
}
