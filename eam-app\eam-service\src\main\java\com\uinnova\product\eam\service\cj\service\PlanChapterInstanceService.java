package com.uinnova.product.eam.service.cj.service;

import com.uinnova.product.eam.model.cj.domain.ChapterInstance;
import com.uinnova.product.eam.model.cj.request.PlanChapterInstanceAddRequest;
import com.uinnova.product.eam.model.cj.request.PlanChapterInstanceMoveRequest;
import com.uinnova.product.eam.model.cj.vo.*;
import com.uino.bean.permission.base.SysUser;

import java.util.List;
import java.util.Map;

/**
 * 方案章节实例service接口
 *
 * <AUTHOR>
 */
public interface PlanChapterInstanceService {

    /**
     * 新增章节
     *
     * @param request {@link PlanChapterInstanceAddRequest}
     * @return 章节id
     */
    Long addChapter(PlanChapterInstanceAddRequest request);

    /**
     * 批量新增或修改
     * @param chapterInstanceList
     * @return
     */
    Integer saveOrUpdateBatch(List<ChapterInstance> chapterInstanceList);

    /**
     * 初始化章节
     *
     * @param planId     方案id
     * @param templateId 方案模板id
     */
    void initChapter(Long planId, Long templateId);


    /**
     * 方案查找章节列表
     *
     * @param planId 方案id
     * @return 方案列表 树形
     */
    List<PlanChapterVO> getChapterList(Long planId, String... like);

    /**
     * 查询方案所有章节详情
     * @param planId 方案id
     * @return 章节详情
     */
    List<ChapterInstance> getChapterByPlanId(Long planId, String... like);

    /**
     * 获取方案章节列表
     * @param planId
     * @return
     */
    List<ChapterInstance> findPlanChapterList(Long planId);

    /**
     * 方案章节移动
     *
     * @param request {@link PlanChapterInstanceMoveRequest}
     */
    void move(PlanChapterInstanceMoveRequest request);

    /**
     * 方案章节复制
     * <p>description：复制给定id的章节内容</p>
     *
     * @param map 参数， 原章节id， 新章节名称newName
     * @return 新章节id
     */
    Long copy(Map<String, Object> map);

    /**
     * 章节删除
     *
     * @param id 章节id
     */
    void deleteById(Long id);

    String getChapterSerialNum(Long planId, Long chapterId);

    /**
     * 方案预览
     *
     * @param planId 方案id
     * @return 方案预览数据
     */
    PlanPreviewVO planPreview(Long planId);

    /**
     * 获取方案章节详情
     * @param planChapterId
     * @return
     */
    ChapterInstance getPlanChapterInstance(Long planChapterId);

    /**
     * 锁章节
     * @param sysUser
     * @return
     */
    boolean lockChapter(HandlePlanChapterVO handlePlanChapterVO, SysUser sysUser);

    Map<String,List<PlanIntroChapterVo>> getAllIntroChapterInfo(List<PlanTemplateIntroduceChapterVo> templateIntroduceChapterVos);

    List<DiagramVersionBO> findDiagramVersions(Long planId);

    /**
     * 通过方案id和状态获取章节列表
     * @return
     */
    List<ChapterInstance> findChapterInstanceList(Long planId, Integer status);

    Boolean ignoreViewVersions(Long planId);

    /**
     * 校验方案所应用的模板是否有更新
     * @param planId
     * @return
     */
    Boolean checkPlanTemplate(Long planId);

    /**
     * 更新方案模板
     * @param planId
     * @return
     */
    List<ChapterModuleVO> updatePlanTemplate(Long planId, SysUser sysUser);

    /**
     * 删除方案章节
     * @param id
     */
    void deletePlanChapter(Long id);
}
