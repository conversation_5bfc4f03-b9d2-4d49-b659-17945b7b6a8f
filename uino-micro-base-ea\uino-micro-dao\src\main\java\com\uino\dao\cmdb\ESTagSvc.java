package com.uino.dao.cmdb;

import java.util.*;
import java.util.stream.Collectors;

import jakarta.annotation.PostConstruct;

import com.uinnova.product.vmdb.comm.model.rule.CcCiTagDef;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.Operator;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.MultiMatchQueryBuilder.Type;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.binary.core.util.BinaryUtils;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiTag;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uino.dao.AbstractESBaseDao;
import com.uino.dao.ESConst;
import com.uino.bean.cmdb.base.ESCIClassInfo;
import com.uino.bean.cmdb.base.ESCITagInfo;
import com.uino.bean.cmdb.base.ESTagRuleInfo;
import com.uino.bean.cmdb.base.ESTagRuleItem;
import com.uino.bean.cmdb.base.ESTagRuleItemGroup;
import org.springframework.util.CollectionUtils;

/**
 * ES标签服务
 *
 * <AUTHOR>
 */
@Service
public class ESTagSvc extends AbstractESBaseDao<ESCITagInfo, CCcCiTag> {

    @Autowired
    private ESCIClassSvc classSvc;

    @Autowired
    private ESCISvc ciSvc;

    @Override
    public String getIndex() {
        return ESConst.INDEX_CMDB_TAG;
    }

    @Override
    public String getType() {
        return ESConst.INDEX_CMDB_TAG;
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }

    /**
     * <b>根据tag获取查询条件
     *
     * @param tag 标签对象
     * @return
     */
    public QueryBuilder getQueryByTag(ESCITagInfo tag) {
        List<ESTagRuleInfo> rules = tag.getRules();
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        if (rules.size() <= 0) {
            return null;
        }
        // 获取分类实际存储的属性名称
        Set<Long> classIds = rules.stream().filter(rule -> rule.getClassId() != null).map(ESTagRuleInfo::getClassId).collect(Collectors.toSet());
        List<ESCIClassInfo> clsList = classSvc.getTargetAttrDefsByClassIds(tag.getDomainId(), classIds);
        Map<Long, ESCIClassInfo> clsMap = BinaryUtils.toObjectMap(clsList, "id");
        for (int i = 0; i < rules.size(); i++) {
            BoolQueryBuilder itemQuery = QueryBuilders.boolQuery();
            ESTagRuleInfo rule = rules.get(i);
            Long classId = rule.getClassId();
            itemQuery.must(QueryBuilders.termQuery("classId", classId));
            List<ESTagRuleItemGroup> groups = rule.getItemGroups();

            // Map<Long, String> attrMap = classSvc.getClassAttrField(classId);
            ESCIClassInfo classInfo = clsMap.get(classId);
            // ESCIClassInfo classInfo = classSvc.getTargetAttrDefsByClassId(classId);

            if (classInfo != null) {
                for (int j = 0; j < groups.size(); j++) {
                    ESTagRuleItemGroup group = groups.get(j);
                    // JSONObject ruleJson = items.getJSONObject(j);
                    itemQuery.must(getQueryOp(classInfo.getCcAttrDefs(), group));
                }
            }
            query.should(itemQuery);
        }

        return query;
    }

    /**
     * <b>获取查询对象
     *
     * @param realDefs 经过中间表转换后的实际属性
     * @param
     * @return
     */
    public QueryBuilder getQueryOp(List<CcCiAttrDef> realDefs, ESTagRuleItemGroup itemGroup) {
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        List<ESTagRuleItem> items = itemGroup.getItems();
        int logicOp = itemGroup.getLogicOp();
        Map<Long, CcCiAttrDef> attrMap = BinaryUtils.toObjectMap(realDefs, "id");
        for (ESTagRuleItem item : items) {
            Long classAttrId = item.getClassAttrId();
            Object ruleVal = item.getRuleVal();
            int ruleOp = item.getRuleOp();
            CcCiAttrDef def = attrMap.get(classAttrId);
            String field = (def == null) ? "" + classAttrId : def.getProStdName();
            Integer attrType = (def == null) ? 3 : def.getProType();
            if (!"ciCode".equals(field)) {
                if (attrType == 7) {
                    field = field + "_date";
                }
                field = "attrs." + field + (attrType > 2 ? ".keyword" : "");
            }

            BoolQueryBuilder itemQuery = QueryBuilders.boolQuery();
            switch (ruleOp) {
                case 1:
                    // 等于
                    itemQuery.must(QueryBuilders.termQuery(field, ruleVal));
                    break;
                case 2:
                    // 不等于
                    itemQuery.mustNot(QueryBuilders.termQuery(field, ruleVal));
                    break;
                case 3:
                    // 小于
                    itemQuery.must(QueryBuilders.rangeQuery(field).lt(ruleVal));
                    break;
                case 4:
                    // 小于或等于
                    itemQuery.must(QueryBuilders.rangeQuery(field).lte(ruleVal));
                    break;
                case 5:
                    // 大于
                    itemQuery.must(QueryBuilders.rangeQuery(field).gt(ruleVal));
                    break;
                case 6:
                    // 大于或等于
                    itemQuery.must(QueryBuilders.rangeQuery(field).gte(ruleVal));
                    break;
                case 7:
                    // 模糊匹配
                    String field1 = (def == null) ? "" + classAttrId : def.getProStdName();
                    if (!"ciCode".equals(field1)) {
                        field1 = "attrs." + field1.toUpperCase();
                        if (attrType == 7) {
                            field1 = field1 + "_date";
                        }
                    }
                    // 整数、小数类型不支持模糊搜索
                    Assert.isTrue(attrType > 2, "整数、小数类型不支持like查询");
                    itemQuery.must(QueryBuilders.multiMatchQuery(ruleVal.toString(), field1).operator(Operator.AND).type(Type.PHRASE_PREFIX).lenient(true));
                    break;
                case 8:
                    // 模糊匹配取反
                    String field2 = (attrMap.get(classAttrId) == null) ? "" + classAttrId : def.getProStdName();
                    if (!field2.equals("ciCode")) {
                        field2 = "attrs." + field2.toUpperCase();
                        if (attrType == 7) {
                            field2 = field2 + "_date";
                        }
                    }
                    Assert.isTrue(attrType > 2, "整数、小数类型不支持not like查询");
                    itemQuery.mustNot(QueryBuilders.multiMatchQuery(ruleVal.toString(), field2).operator(Operator.AND).type(Type.PHRASE_PREFIX).lenient(true));
                    break;
                case 9:
                    // 包含
                    JSONArray words = JSON.parseArray(ruleVal.toString());
                    itemQuery.must(QueryBuilders.termsQuery(field, words));
                    break;
                case 10:
                    // 不包含
                    JSONArray words1 = JSON.parseArray(ruleVal.toString());
                    itemQuery.mustNot(QueryBuilders.termsQuery(field, words1));
                    break;
                default:
                    break;
            }
            if (logicOp == 1) {
                query.must(itemQuery);
            } else if (logicOp == 2) {
                query.should(itemQuery);
            }
        }
        return query;
    }

    public Map<Long, ESCITagInfo> getTagMapByIds(Collection<Long> tagIds){
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termsQuery("id", tagIds));
        List<ESCITagInfo> resultList = this.getListByQueryScroll(QueryBuilders.termsQuery("id", tagIds));
        if(CollectionUtils.isEmpty(resultList)){
            return Collections.emptyMap();
        }
        return resultList.stream().collect(Collectors.toMap(CcCiTagDef::getId, e->e, (k1,k2)->k2));
    }

}
