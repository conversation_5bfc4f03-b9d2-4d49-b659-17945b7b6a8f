package com.uinnova.product.eam.service.diagram;


import com.uinnova.product.eam.base.diagram.model.ESDiagramSheetDTO;

import java.util.Collection;
import java.util.List;

/**
 * 视图sheet接口
 * <AUTHOR>
 */
public interface ESDiagramSheetSvc {

    /**
     * 获取视图sheet
     * @param diagramIds 视图id集合
     * @return 视图node节点
     */
    List<ESDiagramSheetDTO> getSheetByDiagram(Collection<Long> diagramIds);

    /**
     * 批量更新或保存分页信息
     * @param sheets 分页信息
     * @return 结果
     */
    Integer saveOrUpdateBatch(List<ESDiagramSheetDTO> sheets);

    /**
     * 批量通过视图id删除视图分页信息
     * @param diagramIds 视图id
     */
    void deleteByDiagramIds(Collection<Long> diagramIds);
}
