package com.uinnova.product.vmdb.comm.expression;

import java.io.Serializable;

/**
 * 
 * <AUTHOR>
 *
 */
public interface Expression<E> extends Serializable {

    /**
     * 添加表达式
     * 
     * @param appends
     * @return
     */
    Expression<E> and(Expression<E> exp);

    /**
     * 或表达式
     * 
     * @param or
     * @return
     */
    Expression<E> or(Expression<E> exp);

    /**
     * 获取表达式在SQL中的片段
     * 
     * @return
     */
    String getSqlFragment();

}
