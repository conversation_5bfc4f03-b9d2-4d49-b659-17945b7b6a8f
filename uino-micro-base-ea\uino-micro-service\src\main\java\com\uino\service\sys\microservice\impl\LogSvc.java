package com.uino.service.sys.microservice.impl;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.uino.dao.BaseConst;
import com.uino.service.sys.microservice.ILogSvc;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.MultiMatchQueryBuilder.Type;
import org.elasticsearch.index.query.Operator;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import com.binary.jdbc.Page;
import com.uino.dao.permission.ESUserSvc;
import com.uino.dao.sys.ESSysLoginLogSvc;
import com.uino.util.sys.SysUtil;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.sys.base.SysLoginLog;
import com.uino.bean.sys.business.QueryLoginLogRequestDto;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
public class LogSvc implements ILogSvc {

    @Autowired
    private ESSysLoginLogSvc esSysLogSvc;

    @Autowired
    private ESUserSvc esUserSvc;

    @Override
    public SysLoginLog addLoginLog(Long userId) {
        SysUser user = esUserSvc.getById(userId);
        String ipAddress = SysUtil.getIpAddress(null);
        SysLoginLog saveInfo = SysLoginLog.builder().userId(userId).userCode(user.getLoginCode()).ipAddress(ipAddress).domainId(user.getDomainId()).build();
        Long id = esSysLogSvc.saveOrUpdate(saveInfo);
        saveInfo.setId(id);
        return saveInfo;
    }

    @Override
    public Page<SysLoginLog> queryLoginLog(QueryLoginLogRequestDto query) {
        if (query.getDomainId() == null) {
            query.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
        }
        BoolQueryBuilder queryLoginLog = new BoolQueryBuilder();
        queryLoginLog.must(QueryBuilders.termQuery("domainId", query.getDomainId()));
        if (query.getCreateTimeStart() != null) {
            queryLoginLog.must(QueryBuilders.rangeQuery("createTime").gte(query.getCreateTimeStart()));
        }
        if (query.getCreateTimeEnd() != null) {
            queryLoginLog.must(QueryBuilders.rangeQuery("createTime").lte(query.getCreateTimeEnd()));
        }
        if (query.getUserId() != null) {
            queryLoginLog.must(QueryBuilders.termQuery("userId", query.getUserId()));
        }
        if (query.getUserCodeOrName() != null && !"".equals(query.getUserCodeOrName())) {
            Set<Long> userIds = new HashSet<>();
            BoolQueryBuilder queryUser = new BoolQueryBuilder();
            // queryUser.should(QueryBuilders.fuzzyQuery("loginCode.keyword",
            // query.getUserCodeOrName()));
            // queryUser.should(QueryBuilders.fuzzyQuery("userName.keyword",
            // query.getUserCodeOrName()));
            queryUser.must(QueryBuilders.multiMatchQuery(query.getUserCodeOrName(), "userName", "loginCode")
                    .operator(Operator.AND).type(Type.PHRASE_PREFIX).lenient(true));
            List<SysUser> users = esUserSvc.getListByQuery(queryUser);
            users.forEach(user -> userIds.add(user.getId()));
            queryLoginLog.must(QueryBuilders.termsQuery("userId", userIds));
        }
        Page<SysLoginLog> res = esSysLogSvc.getSortListByQuery(query.getPageNum(), query.getPageSize(), queryLoginLog,
                "createTime", false);
        if (res.getData() != null && res.getData().size() > 0) {
            Set<Long> userIds = new HashSet<>();
            res.getData().forEach(loginLog -> {
                userIds.add(loginLog.getUserId());
            });
            Map<Long, SysUser> userDict = new java.util.HashMap<>();
            List<SysUser> users = esUserSvc.getListByQuery(QueryBuilders.termsQuery("id", userIds));
            users.forEach(user -> {
                userDict.put(user.getId(), user);
            });
            res.getData().forEach(loginLog -> {
                if (userDict.get(loginLog.getUserId()) != null) {
                    loginLog.setUserName(userDict.get(loginLog.getUserId()).getUserName());
                } else {
                    loginLog.setUserName("用户已删除");
                }
            });
        }
        return res;
    }

    @Override
    public SysLoginLog addLoginLog(Long domainId, String userCode) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.termQuery("domainId", domainId));
        boolQueryBuilder.filter(QueryBuilders.termQuery("userCode.keyword", userCode));
        List<SysUser> users = esUserSvc.getListByQuery(boolQueryBuilder);
        Assert.notEmpty(users, "所记录用户不存在");
        return addLoginLog(users.get(0).getId());
    }

}
