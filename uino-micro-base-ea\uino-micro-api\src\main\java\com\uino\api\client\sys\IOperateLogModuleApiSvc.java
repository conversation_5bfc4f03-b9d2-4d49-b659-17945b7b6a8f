package com.uino.api.client.sys;

import com.uino.bean.sys.base.ESOperateLogModule;

import java.util.List;

/**
 * 操作日志模块接口api
 */
public interface IOperateLogModuleApiSvc {

    /**
     * 获取所有操作日志模块
     * @return
     */
    public List<ESOperateLogModule> getAll();

    /**
     * 通过mvc路径获取Module信息
     * 如果有多个匹配，则匹配最长路径
     * @param mvcPath Mvc路径
     * @return 匹配到的Module信息
     */
    public ESOperateLogModule getModuleInfoByMvc(String mvcPath);
}
