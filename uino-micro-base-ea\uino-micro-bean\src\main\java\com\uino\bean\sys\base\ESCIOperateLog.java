package com.uino.bean.sys.base;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

import org.springframework.util.Assert;

import com.binary.framework.bean.annotation.Comment;
import com.uino.bean.permission.business.IValidDto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 
 * <AUTHOR>
 *
 */
@Comment("CI操作日志")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value="ci操作日志",description = "ci操作日志")
public class ESCIOperateLog implements Serializable, IValidDto {

    private static final long serialVersionUID = -655176515871669944L;

    @ApiModelProperty(value="ID",example = "123")
    @Comment("ID 自动生成")
    private Long id;

    @ApiModelProperty(value="对象id",example = "123")
    @Comment("CI_ID 以备不时之需")
    private Long ciId;

    @ApiModelProperty(value="CI分类名")
    @Comment("CI分类名")
    private String ciClassName;

    @ApiModelProperty(value="CI_CODE")
    @Comment("CI_CODE")
    private String ciCode;

    @ApiModelProperty(value="CI业务主键")
    @Comment("CI业务主键")
    private String ciPrimaryKey;

    @ApiModelProperty(value="动态,1：添加；2：修改；3删除",example = "1")
    @Comment("动态   1：添加；2：修改；3删除")
    private Integer dynamic;

    @ApiModelProperty(value="操作者",example = "mike")
    @Comment("操作者")
    private String operator;

    @ApiModelProperty(value="日志来源类型,1：用户页面；2：配置处理；3：DIX",example = "1")
    @Comment("日志来源类型    1：用户页面；2：配置处理；3：DIX")
    private Long sourceId;

    @ApiModelProperty(value="旧属性值")
    @Comment("旧属性值")
    private Map<String, String> oldAttrs;

    @ApiModelProperty(value="新属性值")
    @Comment("新属性值")
    private Map<String, String> newAttrs;

    @ApiModelProperty(value="属性定义顺序")
    @Comment("属性定义顺序")
    private List<String> proNames;

    @ApiModelProperty(value="所属域id",example = "123")
    @Comment("所属域[DOMAIN_ID]")
    private Long domainId;

    @ApiModelProperty(value="创建时间")
    @Comment("创建时间[CREATE_TIME]    创建时间:yyyyMMddHHmmss")
    private Long createTime;

    @ApiModelProperty(value="修改时间")
    @Comment("修改时间[MODIFY_TIME]    修改时间:yyyyMMddHHmmss")
    private Long modifyTime;

    @ApiModelProperty(value="所属库类型",example = "1")
    @Comment("所属库类型    1：运行库；2：私有库；3：设计库")
    private Integer libaryId;

    @Comment("发起请求的IP地址")
    private String ipAddress;

    @Override
    public void valid() {
        Assert.notNull(ciClassName, "分类名称不可为空");
        Assert.notNull(ciCode, "ciCode不可为空");
        Assert.notNull(ciPrimaryKey, "业务主键不可为空");
        Assert.notNull(operator, "操作用户不可为空");
    }

}
