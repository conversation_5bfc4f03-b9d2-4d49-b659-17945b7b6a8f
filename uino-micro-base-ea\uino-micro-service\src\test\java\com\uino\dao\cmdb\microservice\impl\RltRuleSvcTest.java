package com.uino.dao.cmdb.microservice.impl;

import static org.assertj.core.api.Assertions.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import java.util.ArrayList;
import java.util.List;

import com.uino.service.cmdb.microservice.impl.RltRuleSvc;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

import com.uinnova.product.vmdb.comm.model.ci.CCcCiClass;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uino.dao.cmdb.ESCIClassSvc;
import com.uino.dao.cmdb.ESRltClassSvc;
import com.uino.dao.cmdb.ESRltRuleSvc;
import com.uino.bean.cmdb.base.ESCIClassInfo;
import com.uino.bean.cmdb.base.ESRltRuleInfo;

/**
 * 
 * <AUTHOR>
 *
 */
public class RltRuleSvcTest {

    @InjectMocks
    private RltRuleSvc svc;

    private ESRltRuleSvc esRltRuleSvc;

    private ESCIClassSvc esciClassSvc;

    private ESRltClassSvc esRltClassSvc;


    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);

        esRltRuleSvc = mock(ESRltRuleSvc.class);
        ReflectionTestUtils.setField(svc, "esRltRuleSvc", esRltRuleSvc);
        esciClassSvc = mock(ESCIClassSvc.class);
        ReflectionTestUtils.setField(svc, "esciClassSvc", esciClassSvc);
        esRltClassSvc = mock(ESRltClassSvc.class);
        ReflectionTestUtils.setField(svc, "esRltClassSvc", esRltClassSvc);

    }

    /**
     * 校验规则1
     */
    @Test
    public void verifyRltRuleInfo1(){

        //缺少name
        ESRltRuleInfo model = new ESRltRuleInfo();
        ESRltRuleInfo rule1 = new ESRltRuleInfo();
        checkVerifyRltRuleInfoException(model);

        //有重名的规则
        model.setName("rule1");
        rule1.setName("rule1");
        List<ESRltRuleInfo> ruleList = new ArrayList<>();
        ruleList.add(rule1);
        when(esRltRuleSvc.getListByQuery(any(BoolQueryBuilder.class))).thenReturn(ruleList);
        checkVerifyRltRuleInfoException(model);
    }

    /**
     * 校验规则2
     */
    @Test
    public void verifyRltRuleInfo2(){

        //有关系分类不存在
        ESRltRuleInfo model = new ESRltRuleInfo();
        ESRltRuleInfo rule1 = new ESRltRuleInfo();
        model.setName("rule1");
        rule1.setName("rule2");
        List<ESRltRuleInfo> ruleList = new ArrayList<>();
        ruleList.add(rule1);
        when(esRltRuleSvc.getListByQuery(any(BoolQueryBuilder.class))).thenReturn(ruleList);

        List<ESRltRuleInfo.LineInfo> lineInfos = new ArrayList<>();
        ESRltRuleInfo.LineInfo lineInfo1 = new ESRltRuleInfo.LineInfo();
        ESRltRuleInfo.LineInfo lineInfo2 = new ESRltRuleInfo.LineInfo();
        ESRltRuleInfo.Line line1 = new ESRltRuleInfo.Line();
        ESRltRuleInfo.Line line2 = new ESRltRuleInfo.Line();
        line1.setLineType(1);
        line2.setLineType(1);
        line1.setClsRltId(11L);
        line2.setClsRltId(22L);
        lineInfo1.setLine(line1);
        lineInfo2.setLine(line2);
        lineInfos.add(lineInfo1);
        lineInfos.add(lineInfo2);
        model.setLines(lineInfos);

        List<ESCIClassInfo> rltClasses = new ArrayList<>();
        ESCIClassInfo rltClass = new ESCIClassInfo();
        rltClasses.add(rltClass);

        when(esRltClassSvc.getListByCdt(any(CCcCiClass.class))).thenReturn(rltClasses);
        checkVerifyRltRuleInfoException(model);
    }

    /**
     * 校验规则3
     */
    @Test
    public void verifyRltRuleInfo3() {

        //有ci分类不存在
        ESRltRuleInfo model = new ESRltRuleInfo();
        ESRltRuleInfo rule1 = new ESRltRuleInfo();
        model.setName("rule1");
        rule1.setName("rule2");
        List<ESRltRuleInfo> ruleList = new ArrayList<>();
        ruleList.add(rule1);
        when(esRltRuleSvc.getListByQuery(any(BoolQueryBuilder.class))).thenReturn(ruleList);

        List<ESRltRuleInfo.LineInfo> lineInfos = new ArrayList<>();
        ESRltRuleInfo.LineInfo lineInfo1 = new ESRltRuleInfo.LineInfo();
        ESRltRuleInfo.Line line1 = new ESRltRuleInfo.Line();
        line1.setLineType(1);
        line1.setClsRltId(11L);
        lineInfo1.setLine(line1);
        lineInfos.add(lineInfo1);
        model.setLines(lineInfos);
        List<ESCIClassInfo> rltClasses = new ArrayList<>();
        ESCIClassInfo rltClass = new ESCIClassInfo();
        rltClasses.add(rltClass);
        when(esRltClassSvc.getListByCdt(any(CCcCiClass.class))).thenReturn(rltClasses);

        List<ESRltRuleInfo.NodeInfo> nodeInfos = new ArrayList<>();
        ESRltRuleInfo.NodeInfo nodeInfo1 = new ESRltRuleInfo.NodeInfo();
        ESRltRuleInfo.NodeInfo nodeInfo2 = new ESRltRuleInfo.NodeInfo();
        ESRltRuleInfo.Node node1 = new ESRltRuleInfo.Node();
        ESRltRuleInfo.Node node2 = new ESRltRuleInfo.Node();
        node1.setNodeType(1);
        node2.setNodeType(1);
        node1.setClassId(11L);
        node2.setClassId(22L);
        nodeInfo1.setNode(node1);
        nodeInfo2.setNode(node2);
        nodeInfos.add(nodeInfo1);
        nodeInfos.add(nodeInfo2);
        model.setNodes(nodeInfos);
        List<ESCIClassInfo> ciClasses = new ArrayList<>();
        ESCIClassInfo ciClass = new ESCIClassInfo();
        ciClasses.add(rltClass);
        when(esciClassSvc.getListByCdt(any(CCcCiClass.class))).thenReturn(ciClasses);

        checkVerifyRltRuleInfoException(model);

    }

    /**
     * 校验规则4
     */
    @Test
    public void verifyRltRuleInfo4() {

        //节点中找不到当前分类
        ESRltRuleInfo model = new ESRltRuleInfo();
        ESRltRuleInfo rule1 = new ESRltRuleInfo();
        model.setName("rule1");
        rule1.setName("rule2");
        List<ESRltRuleInfo> ruleList = new ArrayList<>();
        ruleList.add(rule1);
        when(esRltRuleSvc.getListByQuery(any(BoolQueryBuilder.class))).thenReturn(ruleList);

        List<ESRltRuleInfo.LineInfo> lineInfos = new ArrayList<>();
        ESRltRuleInfo.LineInfo lineInfo1 = new ESRltRuleInfo.LineInfo();
        ESRltRuleInfo.Line line1 = new ESRltRuleInfo.Line();
        line1.setLineType(1);
        line1.setClsRltId(11L);
        lineInfo1.setLine(line1);
        lineInfos.add(lineInfo1);
        model.setLines(lineInfos);
        List<ESCIClassInfo> rltClasses = new ArrayList<>();
        ESCIClassInfo rltClass = new ESCIClassInfo();
        rltClasses.add(rltClass);
        when(esRltClassSvc.getListByCdt(any(CCcCiClass.class))).thenReturn(rltClasses);

        List<ESRltRuleInfo.NodeInfo> nodeInfos = new ArrayList<>();
        ESRltRuleInfo.NodeInfo nodeInfo1 = new ESRltRuleInfo.NodeInfo();
        ESRltRuleInfo.Node node1 = new ESRltRuleInfo.Node();
        node1.setNodeType(1);
        node1.setClassId(11L);
        nodeInfo1.setNode(node1);
        nodeInfos.add(nodeInfo1);
        model.setNodes(nodeInfos);
        List<ESCIClassInfo> ciClasses = new ArrayList<>();
        ESCIClassInfo ciClass = new ESCIClassInfo();
        ciClasses.add(ciClass);
        when(esciClassSvc.getListByCdt(any(CCcCiClass.class))).thenReturn(ciClasses);

        List<CcCiAttrDef> attrDefs = new ArrayList<>();
        CcCiAttrDef attrDef = new CcCiAttrDef();
        attrDefs.add(attrDef);
        ciClass.setCcAttrDefs(attrDefs);
        attrDef.setProStdName("业务主键");
        ciClass.setId(2L);
        node1.setReturns("[{\"isRequired\":1,\"proType\":3,\"creator\":\"admin\",\"orderNo\":1,\"importanceLevel\":2,\"isAudit\":0,\"modifier\":\"admin\",\"dataStatus\":1,\"domainId\":1,\"mpCiField\":0,\"classId\":100000000662502,\"modifyTime\":20190805163836,\"createTime\":20190805163836,\"isCode\":0,\"isCiDisp\":0,\"isMajor\":1,\"proName\":\"业务主键\",\"id\":100000000824001,\"proStdName\":\"业务主键\",\"ciType\":1}]");
        node1.setClassId(1L);

        checkVerifyRltRuleInfoException(model);
    }

    /**
     * 校验规则5
     */
    @Test
    public void verifyRltRuleInfo5() {

        //节点显示属性不是当前分类属性
        ESRltRuleInfo model = new ESRltRuleInfo();
        ESRltRuleInfo rule1 = new ESRltRuleInfo();
        model.setName("rule1");
        rule1.setName("rule2");
        List<ESRltRuleInfo> ruleList = new ArrayList<>();
        ruleList.add(rule1);
        when(esRltRuleSvc.getListByQuery(any(BoolQueryBuilder.class))).thenReturn(ruleList);

        List<ESRltRuleInfo.LineInfo> lineInfos = new ArrayList<>();
        ESRltRuleInfo.LineInfo lineInfo1 = new ESRltRuleInfo.LineInfo();
        ESRltRuleInfo.Line line1 = new ESRltRuleInfo.Line();
        line1.setLineType(1);
        line1.setClsRltId(11L);
        lineInfo1.setLine(line1);
        lineInfos.add(lineInfo1);
        model.setLines(lineInfos);
        List<ESCIClassInfo> rltClasses = new ArrayList<>();
        ESCIClassInfo rltClass = new ESCIClassInfo();
        rltClasses.add(rltClass);
        when(esRltClassSvc.getListByCdt(any(CCcCiClass.class))).thenReturn(rltClasses);

        List<ESRltRuleInfo.NodeInfo> nodeInfos = new ArrayList<>();
        ESRltRuleInfo.NodeInfo nodeInfo1 = new ESRltRuleInfo.NodeInfo();
        ESRltRuleInfo.Node node1 = new ESRltRuleInfo.Node();
        node1.setNodeType(1);
        node1.setClassId(11L);
        nodeInfo1.setNode(node1);
        nodeInfos.add(nodeInfo1);
        model.setNodes(nodeInfos);
        List<ESCIClassInfo> ciClasses = new ArrayList<>();
        ESCIClassInfo ciClass = new ESCIClassInfo();
        ciClasses.add(ciClass);
        when(esciClassSvc.getListByCdt(any(CCcCiClass.class))).thenReturn(ciClasses);

        List<CcCiAttrDef> attrDefs = new ArrayList<>();
        CcCiAttrDef attrDef = new CcCiAttrDef();
        attrDefs.add(attrDef);
        ciClass.setCcAttrDefs(attrDefs);
        attrDef.setProStdName("业务主键1");
        ciClass.setId(1L);
        node1.setReturns("[{\"isRequired\":1,\"proType\":3,\"creator\":\"admin\",\"orderNo\":1,\"importanceLevel\":2,\"isAudit\":0,\"modifier\":\"admin\",\"dataStatus\":1,\"domainId\":1,\"mpCiField\":0,\"classId\":100000000662502,\"modifyTime\":20190805163836,\"createTime\":20190805163836,\"isCode\":0,\"isCiDisp\":0,\"isMajor\":1,\"proName\":\"业务主键\",\"id\":100000000824001,\"proStdName\":\"业务主键\",\"ciType\":1}]");
        node1.setClassId(1L);

        checkVerifyRltRuleInfoException(model);
    }

    /**
     * 校验规则6
     */
    @Test
    public void verifyRltRuleInfo6() {

        ESRltRuleInfo model = new ESRltRuleInfo();
        ESRltRuleInfo rule1 = new ESRltRuleInfo();
        model.setName("rule1");
        rule1.setName("rule2");
        List<ESRltRuleInfo> ruleList = new ArrayList<>();
        ruleList.add(rule1);
        when(esRltRuleSvc.getListByQuery(any(BoolQueryBuilder.class))).thenReturn(ruleList);

        List<ESRltRuleInfo.LineInfo> lineInfos = new ArrayList<>();
        ESRltRuleInfo.LineInfo lineInfo1 = new ESRltRuleInfo.LineInfo();
        ESRltRuleInfo.Line line1 = new ESRltRuleInfo.Line();
        line1.setLineType(1);
        line1.setClsRltId(11L);
        lineInfo1.setLine(line1);
        lineInfos.add(lineInfo1);
        model.setLines(lineInfos);
        List<ESCIClassInfo> rltClasses = new ArrayList<>();
        ESCIClassInfo rltClass = new ESCIClassInfo();
        rltClasses.add(rltClass);
        when(esRltClassSvc.getListByCdt(any(CCcCiClass.class))).thenReturn(rltClasses);

        List<ESRltRuleInfo.NodeInfo> nodeInfos = new ArrayList<>();
        ESRltRuleInfo.NodeInfo nodeInfo1 = new ESRltRuleInfo.NodeInfo();
        ESRltRuleInfo.Node node1 = new ESRltRuleInfo.Node();
        node1.setNodeType(1);
        node1.setClassId(11L);
        nodeInfo1.setNode(node1);
        nodeInfos.add(nodeInfo1);
        model.setNodes(nodeInfos);
        List<ESCIClassInfo> ciClasses = new ArrayList<>();
        ESCIClassInfo ciClass = new ESCIClassInfo();
        ciClasses.add(ciClass);
        when(esciClassSvc.getListByCdt(any(CCcCiClass.class))).thenReturn(ciClasses);

        List<CcCiAttrDef> attrDefs = new ArrayList<>();
        CcCiAttrDef attrDef = new CcCiAttrDef();
        attrDefs.add(attrDef);
        ciClass.setCcAttrDefs(attrDefs);
        attrDef.setProStdName("业务主键");
        ciClass.setId(1L);
        node1.setReturns("[{\"isRequired\":1,\"proType\":3,\"creator\":\"admin\",\"orderNo\":1,\"importanceLevel\":2,\"isAudit\":0,\"modifier\":\"admin\",\"dataStatus\":1,\"domainId\":1,\"mpCiField\":0,\"classId\":100000000662502,\"modifyTime\":20190805163836,\"createTime\":20190805163836,\"isCode\":0,\"isCiDisp\":0,\"isMajor\":1,\"proName\":\"业务主键\",\"id\":100000000824001,\"proStdName\":\"业务主键\",\"ciType\":1}]");
        node1.setClassId(1L);

        when(esRltRuleSvc.saveOrUpdate(any(ESRltRuleInfo.class))).thenReturn(1L);
        checkVerifyRltRuleInfo(model);
    }

    private void checkVerifyRltRuleInfoException(ESRltRuleInfo model) {
        try {
            svc.saveOrUpdate(model);
            fail("");
        } catch (Exception e) {
//			e.printStackTrace();
        }
    }

    private void checkVerifyRltRuleInfo(ESRltRuleInfo model) {
        try {
            svc.saveOrUpdate(model);
//            fail("");
        } catch (Exception e) {
//			e.printStackTrace();
        }
    }


    /**
     * 按id删除1
     */
    @Test
    public void deleteByIdTest1(){
        //id为null
        checkDeleteByIdException(null);
    }

    /**
     * 按id删除2
     */
    @Test
    public void deleteByIdTest2(){
        //规则不存在
        long id = 1;
        when(esRltRuleSvc.getById(1L)).thenReturn(null);
        checkDeleteByIdException(id);
    }

    /**
     * 按id删除3
     */
    @Test
    public void deleteByIdTest3(){
        long id = 1;
        ESRltRuleInfo model = new ESRltRuleInfo();
        when(esRltRuleSvc.getById(1L)).thenReturn(model);
        when(esRltRuleSvc.deleteById(1L)).thenReturn(1);
        checkDeleteById(id);
    }

    private void checkDeleteByIdException(Long id) {
        try {
            svc.deleteById(id);
            fail("");
        } catch (Exception e) {
//			e.printStackTrace();
        }
    }

    private void checkDeleteById(Long id) {
        try {
            svc.deleteById(id);
//            fail("");
        } catch (Exception e) {
//			e.printStackTrace();
        }
    }


    /**
     * 按id查找Info
     */
    @Test
    public void queryInfoById(){
        long id = 1;
        ESRltRuleInfo model = new ESRltRuleInfo();
        List<ESRltRuleInfo.NodeInfo> nodeInfos = new ArrayList<>();
        ESRltRuleInfo.NodeInfo nodeInfo1 = new ESRltRuleInfo.NodeInfo();
        ESRltRuleInfo.Node node1 = new ESRltRuleInfo.Node();
        node1.setNodeType(1);
        node1.setClassId(11L);
        nodeInfo1.setNode(node1);
        nodeInfos.add(nodeInfo1);
        model.setNodes(nodeInfos);
        List<ESCIClassInfo> ciClasses = new ArrayList<>();
        ESCIClassInfo ciClass = new ESCIClassInfo();
        ciClasses.add(ciClass);
        ciClass.setId(11L);

        when(esRltRuleSvc.getById(1L)).thenReturn(model);
        when(esciClassSvc.getListByCdt(any(CCcCiClass.class))).thenReturn(ciClasses);

        checkQueryInfoByIdException(id);
    }

    private void checkQueryInfoByIdException(Long id) {
        try {
            svc.queryInfoById(id);
//            fail("");
        } catch (Exception e) {
//			e.printStackTrace();
        }
    }


    /**
     * 按id查找Info1
     */
    @Test
    public void queryInfoPageTest1(){
        Integer pageNum = 1;
        Integer pageSize = 1;
        String name = "11";
        Long domainId = 1L;
        checkqueryInfoPageTest1Exception(pageNum, pageSize, name, domainId);
    }

    private void checkqueryInfoPageTest1Exception(Integer pageNum, Integer pageSize, String name, Long domainId) {
        try {
            svc.queryInfoPage(pageNum, pageSize, name, domainId);
//            fail("");
        } catch (Exception e) {
//			e.printStackTrace();
        }
    }


    /**
     * 按id查找Info2
     */
    @Test
    public void queryInfoPageTest2(){
        Integer pageNum = 1;
        Integer pageSize = 1;
        Long domainId = 1L;
        checkqueryInfoPageTest2Exception(pageNum, pageSize, domainId);
    }

    private void checkqueryInfoPageTest2Exception(Integer pageNum, Integer pageSize, Long domainId) {
        try {
            svc.queryInfoPage(pageNum, pageSize, domainId);
//            fail("");
        } catch (Exception e) {
//			e.printStackTrace();
        }
    }


    /**
     * 按id查找Info3
     */
    @Test
    public void queryInfoPageTest3(){
        Integer pageNum = 1;
        Integer pageSize = 1;
        Long domainId = 1L;
        String orders = "1";
        boolean isAsc = true;
        checkqueryInfoPageTest3Exception(pageNum, pageSize, domainId, orders, isAsc);
    }

    private void checkqueryInfoPageTest3Exception(Integer pageNum, Integer pageSize, Long domainId, String orders, boolean isAsc) {
        try {
            svc.queryInfoPage(pageNum, pageSize, domainId, orders, isAsc);
//            fail("");
        } catch (Exception e) {
//			e.printStackTrace();
        }
    }


    /**
     * 按id查找Info4
     */
    @Test
    public void queryInfoPageTest4(){
        Integer pageNum = 1;
        Integer pageSize = 1;
        String name = "1";
        Long domainId = 1L;
        String orders = "1";
        boolean isAsc = true;
        checkqueryInfoPageTest4Exception(pageNum, pageSize, name, domainId, orders, isAsc);
    }

    private void checkqueryInfoPageTest4Exception(Integer pageNum, Integer pageSize, String name, Long domainId, String orders, boolean isAsc) {
        try {
            svc.queryInfoPage(pageNum, pageSize, name, domainId, orders, isAsc);
//            fail("");
        } catch (Exception e) {
//			e.printStackTrace();
        }
    }
}
