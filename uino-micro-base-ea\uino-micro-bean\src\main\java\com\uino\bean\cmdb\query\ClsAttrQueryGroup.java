package com.uino.bean.cmdb.query;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import org.springframework.util.Assert;

import com.uino.bean.permission.business.IValidDto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 按照属性打组得ci查询
 * 
 * <AUTHOR>
 *
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ClsAttrQueryGroup implements Serializable, IValidDto {

    private static final long serialVersionUID = 1L;

    /**
     * 分类id
     */
	@ApiModelProperty(value = "分类id")
    private Long classId;

    /**
     * 属性之间是AND 关系
     */
	@ApiModelProperty(value = "属性查询(and关系)")
    @Builder.Default
    private List<ESAttrBean> andAttrs = new ArrayList<ESAttrBean>();

    /**
     * 属性之间是OR 关系
     */
	@ApiModelProperty(value = "属性查询(or关系)")
    @Builder.Default
    private List<ESAttrBean> orAttrs = new ArrayList<ESAttrBean>();

    @Override
    public void valid() {
        Assert.notNull(classId, "classId not null");
        Assert.isTrue((andAttrs != null && andAttrs.size() > 0) || (orAttrs != null && orAttrs.size() > 0),
                "orAttrs or andAttrs notempty");
    }
}
