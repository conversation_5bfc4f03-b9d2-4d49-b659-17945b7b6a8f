package com.uinnova.product.eam.service.diagram;

import com.binary.jdbc.Page;
import com.uinnova.product.eam.base.diagram.mix.model.ConvertTemplateDTO;
import com.uinnova.product.eam.base.diagram.mix.model.TemplateDir;
import com.uinnova.product.eam.base.diagram.mix.model.TemplateDirQueryBean;
import com.uinnova.product.eam.base.diagram.model.ESDiagram;

import java.util.List;

public interface ESTemplateDirSvc {

    Integer convertDiagramAndTemplate(ConvertTemplateDTO convertTemplateBean);

    /**
     * 保存或更新模板目录
     * @param templateDir
     * @return
     */
    TemplateDir saveOrUpdate(TemplateDir templateDir);

    /**
     * 查询所有的模板目录信息
     * @return
     */
    List<TemplateDir> queryTemplateDirs(Boolean all);

    /**
     * 通过目录id分页查询关联的视图信息
     * @param dirId
     * @return
     */
    Page<ESDiagram> queryDiagramByTemplateDirId(TemplateDirQueryBean queryBean);

    /**
     * 通过模板名称模糊查询
     * @param queryBean
     * @return
     */
    Page<ESDiagram> queryDiagramTemplateByLike(TemplateDirQueryBean queryBean);

    /**
     * <AUTHOR>
     * @Description 根据目录id移除模板目录
     * @Date 15:42 2021/7/13
     * @Param [dirId]
     * @return void
     **/
    void removeTemDirById(Long dirId);

    void relationTemDirByDiagramId(List<ESDiagram> esDiagramList);

    /**
     * 搜索查询目录模板信息
     * @param like 搜索字段
     * @return
     */
    List<TemplateDir> getPublicDirAndTemplate(String like);

    Integer convertDiagramToTemplate(ConvertTemplateDTO convertTemplateBean);
}
