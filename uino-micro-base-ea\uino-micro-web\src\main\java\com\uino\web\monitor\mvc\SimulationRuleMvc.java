package com.uino.web.monitor.mvc;

import com.uino.bean.permission.base.SysUser;
import com.uino.util.sys.SysUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.binary.jdbc.Page;
import com.uino.api.client.monitor.ISimulationRuleApiSvc;
import com.uino.bean.monitor.base.SimulationRuleInfo;
import com.uino.bean.monitor.query.SimulationRuleSearchBean;
import com.uino.init.api.ApiResult;
import com.uino.init.api.ApiVersion;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 
 * <AUTHOR>
 * @Date 2021-08-13
 */
@ApiVersion(1)
@Api(value = "模拟规则", tags = { "模拟规则" })
@RestController
@RequestMapping("/monitor/simulationRule")
public class SimulationRuleMvc {

	@Autowired
	private ISimulationRuleApiSvc ruleApi;

	@PostMapping("querySimulationRuleInfoPage")
	@ApiOperation(value = "分页查询模拟规则")
	public ApiResult<Page<SimulationRuleInfo>> querySimulationRuleInfoPage(@RequestBody SimulationRuleSearchBean bean) {
		SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
		bean.setDomainId(currentUserInfo.getDomainId());
		Page<SimulationRuleInfo> page = ruleApi.querySimulationRuleInfoPage(bean);
		return ApiResult.ok(this).data(page);
	}

	@PostMapping("saveOrUpdateSimulationRule")
	@ApiOperation(value = "保存数据模拟规则")
	public ApiResult<Long> saveOrUpdateSimulationRule(@RequestBody SimulationRuleInfo ruleInfo) {
		SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
		ruleInfo.setDomainId(currentUserInfo.getDomainId());
		Long id = ruleApi.saveOrUpdateSimulationRule(ruleInfo);
		return ApiResult.ok(this).data(id);
	}

	@PostMapping("deleteSimulationRuleById")
	@ApiOperation(value = "根据id删除数据模拟规则")
	public ApiResult<Boolean> deleteSimulationRuleById(@RequestBody Long id) {
		ruleApi.deleteSimulationRuleById(id);
		return ApiResult.ok(this).data(true);
	}
}
