{"version": "1.0", "sheetList": [{"id": 5563843272352340, "name": "页面 1", "sheetId": "diagram-ehyxq17c", "sheetOrder": 1, "active": false}], "modelList": [{"linkFromPortIdProperty": "fromPort", "linkToPortIdProperty": "to<PERSON><PERSON>", "nodeDataArray": [{"shapeName": "Vertical-Pool", "attatch": true, "category": "uml-SwimLaneAutoLayout", "fill": "#fff", "label": "流程图", "height": 1160, "headerHeight": 40, "strokeWidth": 3, "textOrientation": true, "items": [{"width": 320, "label": "流程接口", "textAlign": "Top"}, {"width": 320, "label": "", "textAlign": "Top"}, {"width": 300, "label": "", "fill": "transparent", "textOption": {"padding": 0}, "textAlign": "Center", "textEditable": true, "fontSize": 10}, {"width": 300, "label": "", "fill": "transparent", "textOption": {"padding": 0}, "textAlign": "Center", "textEditable": true, "fontSize": 10}], "shapeParentName": "Containers", "isLockNodeRatio": false, "layerName": "", "key": "1f1ea617-8758-401c-adbb-02b6b411336f", "zOrder": 0, "loc": "820 420", "textOriginalHeight": 1160}], "sheetId": "diagram-ehyxq17c", "diagramId": "a43d5f4eb6c14d20", "linkDataArray": [], "class": "GraphLinksModel", "linkKeyProperty": "key"}], "leftPanelModel": [{"id": ["2"], "originalId": "2", "type": "shape", "opened": true}, {"id": ["4"], "originalId": "4", "type": "shape", "opened": true}, {"id": ["6"], "originalId": "6", "type": "shape", "opened": true}, {"id": ["7"], "originalId": "7", "type": "shape", "opened": true}], "ident": "diagram_json", "viewType": ""}