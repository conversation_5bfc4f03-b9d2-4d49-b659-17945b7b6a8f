package com.uinnova.product.eam.web.diagram.bean;

import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;

public class OpenDiagramCdt implements Condition{

	private static final long serialVersionUID = 1L;

	@Comment("多个查询条件")
	private String[] likes;
	
	@Comment("用户IDs")
	private Long[] userIds;
	
	@Comment("小组IDs")
	private Long[] groupIds;
	
	@Comment("tagIDs")
	private Long[] tagIds;

	public String[] getLikes() {
		return likes;
	}

	public void setLikes(String[] likes) {
		this.likes = likes;
	}

	public Long[] getUserIds() {
		return userIds;
	}

	public void setUserIds(Long[] userIds) {
		this.userIds = userIds;
	}

	public Long[] getGroupIds() {
		return groupIds;
	}

	public void setGroupIds(Long[] groupIds) {
		this.groupIds = groupIds;
	}

	public Long[] getTagIds() {
		return tagIds;
	}

	public void setTagIds(Long[] tagIds) {
		this.tagIds = tagIds;
	}
	
	
}
