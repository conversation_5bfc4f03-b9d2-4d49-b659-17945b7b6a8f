package com.uinnova.product.eam.model.bm;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;


@Data
public class SimpDiagramInfo {
    @Comment("所属用户编码[OWNER_CODE]")
    private String ownerCode;
    @Comment("视图名称[NAME]")
    private String name;
    @Comment("视图图标_1[ICON_1]")
    private String icon1;
    @Comment("创建人[CREATOR]")
    private String creator;
    @Comment("创建时间[CREATE_TIME]    创建时间:yyyyMMddHHmmss")
    private Long createTime;
    @Comment("更新时间[MODIFY_TIME]    更新时间:yyyyMMddHHmmss")
    private Long modifyTime;
    @Comment("视图ID加密字段")
    private String dEnergy;
    @Comment("所属目录[DIR_ID]")
    private Long dirId;
    @Comment("是否是模型主视图")
    private Boolean isMain;
    @Comment("视图类型[VIEW_TYPE]")
    private String viewType;
    @Comment("视图缩略图保存时间")
    private Long thumbnailSaveTime;
    @Comment("视图流程状态")
    private Integer flowStatus;
}

