package com.uino.bean.monitor.base;

import java.io.Serializable;
import java.sql.Timestamp;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

/**
 * 告警
 * 
 * <AUTHOR>
 *
 */
@Data
public class ESAlarm implements Serializable {

    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /**
     * 唯一key
     * <p>
     * ${objId}_${kpiName}
     */
    private String uniqueKey;

    /**
     * 告警编号
     */
    private Long alarmCode;

    /**
     * 告警标志
     */
    private String alarmMark;

    /**
     * 告警对象类型0:对象1:关系
     */
    private int alarmObjType;

    /**
     * 告警对象id alarmObjType为对象时为对象id，为关系时为关系id
     */
    private Long alarmObjId;

    /**
     * 产生告警分类的id alarmObjType为对象时为对象分类id，为关系时为关系分类id
     */
    private Long classId;

    /**
     * 指标id
     * 
     * @deprecated 告警的指标允许是随意输入不存在的所以此处不再依靠关联
     */
    @Deprecated
    private Long kpiId;

    /**
     * 指标名称
     */
    private String kpiName;

    /**
     * 告警级别id
     */
    private Long severityId;

    /**
     * 告警级别
     */
    private Integer severityLevel;

    /**
     * 详情
     */
    private String desc;

    /**
     * 来源
     */
    private String source;

    /**
     * 来源id
     */
    private  Integer sourceId;

    /**
     * 告警产生时间毫秒值
     */
    private Long alarmTime;

    /**
     * 告警状态0:开始1关闭2未确认3已确认4未派单5已派单6已通知7未通知
     */
    private int status;

    /**
     * 是否为模拟数据
     */
    private boolean mock;

    /** 所属域 */
    private Long domainId;

    /** 创建人 */
    private String creator;

    /** 修改人 */
    private String modifier;

    /**
     * 由于无法保证递增id，该创建时间同时会被各个server当作指针起点使用
     */
    private Long createTime;

    /** 修改时间 */
    private Long modifyTime;

    /**
     * 告警最后一次发生时间
     */
    private Timestamp lastOccurrence;

    /**
     *告警首次 发生时间
     */
    private Timestamp firstOccurrence;

    @Comment("自定义字段")
    private String customize;

}
