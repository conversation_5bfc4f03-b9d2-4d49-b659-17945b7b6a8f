package com.uino.api.client.cmdb.local;

import java.util.List;

import com.uino.dao.BaseConst;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uino.service.cmdb.microservice.ITagSvc;
import com.uino.bean.cmdb.base.ESCITagInfo;
import com.uino.bean.cmdb.business.ClassNodeInfo;
import com.uino.bean.cmdb.query.ESAttrAggBean;
import com.uino.bean.cmdb.query.ESTagSearchBean;
import com.uino.api.client.cmdb.ITagApiSvc;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
public class TagApiSvcLocal implements ITagApiSvc {

    @Autowired
    ITagSvc tagSvc;

    @Override
    public Long saveOrUpdateCITagRule(ESCITagInfo tagInfo) {
        return tagSvc.saveOrUpdateCITagRule(tagInfo);
    }

    @Override
    public ESCITagInfo getCITagRuleById(Long id) {
        return tagSvc.getCITagRuleById(id);
    }

    @Override
    public List<ClassNodeInfo> getTagTree() {
        return tagSvc.getTagTree(BaseConst.DEFAULT_DOMAIN_ID);
    }

    @Override
    public List<ClassNodeInfo> getTagTree(Long domainId) {
        return tagSvc.getTagTree(domainId);
    }

    @Override
    public Page<CcCiInfo> getCIInfoListByTag(ESTagSearchBean bean) {
        return tagSvc.getCIInfoListByTag(bean);
    }

    @Override
    public Integer deleteById(Long tagId) {
        return tagSvc.deleteById(tagId);
    }

    @Override
    public Page<String> getAttrValuesBySearchBean(ESAttrAggBean searchBean) {
        return tagSvc.getAttrValuesBySearchBean(BaseConst.DEFAULT_DOMAIN_ID, searchBean);
    }

    @Override
    public Page<String> getAttrValuesBySearchBean(Long domainId, ESAttrAggBean searchBean) {
        return tagSvc.getAttrValuesBySearchBean(domainId, searchBean);
    }


    @Override
    public Boolean changeTagDir(ESCITagInfo tagInfo) {
        return tagSvc.changeTagDir(tagInfo);
    }

}
