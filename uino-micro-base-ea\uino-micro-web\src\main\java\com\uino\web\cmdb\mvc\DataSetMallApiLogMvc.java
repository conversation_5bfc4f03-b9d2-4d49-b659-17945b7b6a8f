package com.uino.web.cmdb.mvc;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import com.uino.init.api.ApiResult;
import com.uino.init.api.ApiVersion;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.binary.jdbc.Page;
import com.uino.bean.cmdb.base.dataset.log.DataSetMallApiLog;
import com.uino.api.client.cmdb.IDataSetMallApiLogApiSvc;
import com.uino.web.BaseMvc;

import io.swagger.annotations.ApiOperation;

/**
 * @Classname DataSetMallApiLogController
 * @Description 调用日志
 * @Date 2020/3/20 9:13
 * @Created by sh
 */
@ApiVersion(1)
@Api(value = "调用日志", tags = {"数据超市"})
@RestController
@RequestMapping("cmdb/dataSet/log")
public class DataSetMallApiLogMvc extends BaseMvc {

    @Autowired
    private IDataSetMallApiLogApiSvc dataSetMallApiLogApiSvc;

    @RequestMapping(value = {"/findPage"}, method = RequestMethod.POST)
    @ApiOperation("分页查询日志")
    public ApiResult<Page<DataSetMallApiLog>> findDataSet(HttpServletRequest request, HttpServletResponse response, @RequestBody String body) {
        JSONObject jsonObject = JSONObject.parseObject(body);
        String dataSetId = jsonObject.getString("dataSetId");
        String respUserName = jsonObject.getString("respUserName");
        String respUserCode = jsonObject.getString("respUserCode");
        Integer pageNum = jsonObject.getInteger("pageNum");
        Integer pageSize = jsonObject.getInteger("pageSize");
        Page<DataSetMallApiLog> page = dataSetMallApiLogApiSvc.findPage(dataSetId, pageNum, pageSize, respUserName, respUserCode);
        return ApiResult.ok(this).data(page);
    }

}
