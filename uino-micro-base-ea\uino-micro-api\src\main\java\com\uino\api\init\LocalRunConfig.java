package com.uino.api.init;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

import lombok.extern.slf4j.Slf4j;

/**
 * 本地化部署扫描类
 *
 * <AUTHOR>
 */
@Configuration
@ComponentScan(basePackages = {
        "com.uino.bean",
        "com.uino.service",
        "com.uino.monitor",
        "com.uino.dao",
        "com.uino.util",
        "com.uino.api.client.*.local"})
@ConditionalOnProperty(prefix = "base", name = "load-type", havingValue = "local", matchIfMissing = true)
@Slf4j
public class LocalRunConfig {

    static {
        log.info("发现spring-boot配置为本地加载");
    }
}
