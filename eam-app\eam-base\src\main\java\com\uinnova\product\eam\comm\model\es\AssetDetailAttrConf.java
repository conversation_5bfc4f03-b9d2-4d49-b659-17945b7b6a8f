package com.uinnova.product.eam.comm.model.es;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.comm.bean.CcCiAttrDefTapGroupConfVO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class AssetDetailAttrConf {

    @Comment("主键id")
    private Long id;
    @Comment("广场资产配置ID")
    private Long appSquareConfId;
    @Comment("资产分类")
    private String ClassCode;
    @Comment("展示形式 1:文本，2:Tab页")
    private Integer type;
    @Comment("展示设置")
    private List<CcCiAttrDefTapGroupConfVO> showAttrs;
    @Comment("领域id")
    private Long domainId;
    @Comment("创建人")
    private String creator;
    @Comment("修改人")
    private String modifier;
    @Comment("创建时间")
    private Long createTime;
    @Comment("修改时间")
    private Long modifyTime;
}
