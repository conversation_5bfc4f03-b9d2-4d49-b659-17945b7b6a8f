package com.uinnova.product.eam.service.diagram;


import com.binary.jdbc.Page;
import com.uinnova.product.eam.base.diagram.model.ESDiagram;
import com.uinnova.product.eam.base.diagram.model.ESDiagramDTO;

/**
 * <AUTHOR>
 * @Description 视图历史记录操作svc
 * @Date 11:13 2021/7/7
 **/
public interface OnlineExploreSvc {

    /**
     * <AUTHOR>
     * @Description 根据diagramId获取在线开发key
     * @Date 18:04 2021/9/23
     * @Param [shareLink]
     * @Return 在线开发key
     **/
    String getExploreKey(String diagramId);

    /**
     * <AUTHOR>
     * @Description 根据在线开发key获取视图详情
     * @Date 18:07 2021/9/23
     * @Param [exploreKey]
     * @Return 视图详情
     **/
    ESDiagramDTO getDiagramByExploreKey(String exploreKey);

    Page<ESDiagram> queryData(Integer pageNum, Integer pageSize, Long mmdId, Long dirId, String like, Integer type, Integer dirType, Integer queryType, Integer dataStatus, String orders);

    /**
     * <AUTHOR>
     * @description base64字符串转图片，返回给前台图片路径
     * @date 14:01 2021/9/28
     * @param
     * @return com.uinnova.project.base.diagram.comm.model.ESDiagramDTO
     **/
    String getImage(String diagramId, String content);
}
