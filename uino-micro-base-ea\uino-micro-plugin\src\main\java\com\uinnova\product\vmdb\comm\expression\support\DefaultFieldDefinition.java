package com.uinnova.product.vmdb.comm.expression.support;

import com.uinnova.product.vmdb.comm.exception.ExpressionException;
import com.uinnova.product.vmdb.comm.expression.FieldDefinition;

/**
 * 
 * <AUTHOR>
 *
 */
public class DefaultFieldDefinition<E> implements FieldDefinition<E> {
    private static final long serialVersionUID = 1L;

    /** 模型字段名 **/
    private String name;

    /** 映射JAVA字段名 **/
    private String mappingName;

    public DefaultFieldDefinition(String name, String mappingName) {
        if (name == null || (name = name.trim().toUpperCase()).length() == 0) {
            throw new ExpressionException(" the column-name is empty argument! ");
        }
        if (mappingName == null || (mappingName = mappingName.trim()).length() == 0) {
            throw new ExpressionException(" the column-mapping is empty argument! ");
        }

        this.name = name;
        this.mappingName = mappingName;
    }

    @Override
    public String getName() {
        return this.name;
    }

    @Override
    public String getMappingName() {
        return this.mappingName;
    }

}
