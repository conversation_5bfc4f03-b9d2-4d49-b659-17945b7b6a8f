package com.uinnova.product.eam.web.mix.diagram.v2.web;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.binary.core.exception.BinaryException;
import com.binary.core.util.BinaryUtils;
import com.binary.framework.web.RemoteResult;
import com.binary.json.JSON;
import com.uinnova.product.eam.api.diagram.ESDiagramApiClient;
import com.uinnova.product.eam.base.diagram.model.*;
import com.uinnova.product.eam.model.diagram.DiagramRelationInfo;
import com.uinnova.product.eam.model.diagram.ESDiagramDirInfo;
import com.uinnova.product.eam.model.diagram.MoveDirAndDiagramCdt;
import com.uinnova.product.eam.model.diagram.ThumbnailBatch;
import com.uinnova.product.eam.model.diagram.event.DiagramRuleEnum;
import com.uinnova.product.eam.model.diagram.event.RuleParams;
import com.uinnova.product.eam.model.diagram.event.RuleParamsReq;
import com.uinnova.product.eam.service.diagram.ESDiagramSvc;
import com.uinnova.product.eam.service.diagram.ESTemplateDirSvc;
import com.uinnova.product.eam.service.diagram.EsDiagramSvcV2;
import com.uinnova.product.eam.web.diagram.bean.DiagramDirInfo;
import com.uinnova.product.eam.web.diagram.bean.MyDiagramCdt;
import com.uinnova.product.eam.web.mix.diagram.v2.peer.ESDiagramPeer;
import com.uinnova.product.vmdb.comm.bean.QueryPageCondition;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uinnova.product.vmdb.comm.i18n.MessageUtil;
import com.uinnova.product.vmdb.comm.util.RestTypeUtil;
import com.uino.bean.cmdb.base.LibType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Classname
 * @Description ES视图控制类
 * <AUTHOR>
 * @Date 2021-06-03-11:02
 */
@RestController
@RequestMapping("/eam/esDiagram")
public class ESDiagramMVC {

    @Autowired
    private ESDiagramSvc esDiagramSvc;
    @Autowired
    private EsDiagramSvcV2 esDiagramSvcV2;

    @Autowired
    private ESDiagramPeer esDiagramPeer;

    @Autowired
    private ESDiagramApiClient esDiagramApiClient;

    @Autowired
    private ESTemplateDirSvc esTemplateDirSvc;

    private static final Logger logger = LoggerFactory.getLogger(ESDiagramMVC.class);

    @CrossOrigin(origins = {})
    @PostMapping("/saveESDiagram")
    @ModDesc(desc = "新建空白视图", pDesc = "", rDesc = "视图信息", rType = RemoteResult.class)
    private RemoteResult saveESDiagram(@RequestBody ESDiagramInfoDTO esDiagramInfo) {
        Map<String, String> resultMap = esDiagramPeer.saveESDiagram(esDiagramInfo);
        return new RemoteResult(resultMap);
    }

    @PostMapping("/saveESDiagramBatch")
    @ModDesc(desc = "新建全量视图", pDesc = "", rDesc = "视图信息", rType = RemoteResult.class)
    private RemoteResult saveESDiagramBatch(@RequestBody List<ESDiagramInfoDTO> esDiagramInfoList) {
        List<Long> diagramIdList = esDiagramPeer.saveESDiagramBatch(esDiagramInfoList);
        Map<String, List<Long>> resultMap = new HashMap<>(16);
        resultMap.put("diagramIdList", diagramIdList);
        return new RemoteResult(resultMap);
    }

    @RequestMapping("/queryDiagramInfoById")
    @ModDesc(desc = "根据视图id集合查询视图全部信息", pDesc = "", rDesc = "视图信息", rType = RemoteResult.class)
    private RemoteResult queryDiagramInfoById(@RequestBody String jsonStr) {
        JSONObject jsonObj = new JSONObject(jsonStr);
        Boolean needAuth = jsonObj.getBool("needAuth");
        if (needAuth == null) {
            needAuth = false;
        }
        String diagramId = jsonObj.getStr("diagramId");
        if (StringUtils.isEmpty(diagramId)) {
            throw new BinaryException("传参错误，视图id不能为空");
        }
        Long[] ids = esDiagramPeer.queryDiagramInfoBydEnergy(new String[]{diagramId});
        ESDiagramDTO esDiagramDTO = esDiagramSvc.queryDiagramInfoById(ids[0], null, needAuth);
        return new RemoteResult(esDiagramDTO);
    }

    @CrossOrigin(origins = {})
    @RequestMapping("/queryDiagramInfoByIds")
    @ModDesc(desc = "根据视图id集合查询视图全部信息", pDesc = "", rDesc = "视图信息", rType = RemoteResult.class)
    private RemoteResult queryDiagramInfoByIds(@RequestBody String jsonStr) {
        JSONObject jsonObj = new JSONObject(jsonStr);
        Boolean needAuth = jsonObj.getBool("needAuth");
        if (needAuth == null) {
            needAuth = true;
        }
        JSONArray diagramIdArr = jsonObj.getJSONArray("diagramIds");
        if (BinaryUtils.isEmpty(diagramIdArr)) {
            throw new BinaryException("传参错误，视图id数组不能为空");
        }
        Boolean asset = jsonObj.getBool("asset");
        String[] diagramIds = diagramIdArr.toArray(new String[0]);
        diagramIds = ArrayUtil.distinct(diagramIds);
        Long[] ids = new Long[0];
        try {
            ids = esDiagramPeer.queryDiagramInfoBydEnergy(diagramIds);
        } catch (Exception e) {
            logger.error("视图id查询的视图为空!!");
            return new RemoteResult(Collections.emptyList());
        }
        List<ESDiagramDTO> esDiagramDTOList = esDiagramPeer.queryDiagramInfoByIds(ids, null, needAuth, asset);
        if (BinaryUtils.isEmpty(esDiagramDTOList)) {
            return new RemoteResult(false, 404, "查询的视图不存在");
        }

        Object dataStatusObj = jsonObj.get("dataStatus");
        if (dataStatusObj instanceof Integer) {
            Integer dataStatus = (Integer) dataStatusObj;
            esDiagramDTOList.removeIf(dto -> !dto.getDiagram().getDataStatus().equals(dataStatus));
        }
        return new RemoteResult(esDiagramDTOList);
    }

    @RequestMapping("/queryDiagramInfosById")
    @ModDesc(desc = "根据视图id集合查询视图全部信息", pDesc = "", rDesc = "视图信息", rType = RemoteResult.class)
    private RemoteResult queryDiagramInfosById(@RequestBody String jsonStr) {
        JSONObject jsonObj = new JSONObject(jsonStr);
        Boolean needAuth = jsonObj.getBool("needAuth");
        if (needAuth == null) {
            needAuth = true;
        }
        JSONArray diagramIdArr = jsonObj.getJSONArray("diagramIds");
        if (BinaryUtils.isEmpty(diagramIdArr)) {
            throw new BinaryException("传参错误，视图id数组不能为空");
        }
        Boolean asset = jsonObj.getBool("asset");
        String[] diagramIds = diagramIdArr.toArray(new String[0]);
        diagramIds = ArrayUtil.distinct(diagramIds);
        Long[] ids = new Long[0];
        try {
            ids = esDiagramPeer.queryDiagramInfoBydEnergy(diagramIds);
        } catch (Exception e) {
            logger.error("视图id查询的视图为空!!");
            return new RemoteResult(-1);
        }
        List<ESDiagramDTO> esDiagramDTOList = esDiagramSvc.queryDiagramInfosById(ids, null, needAuth, asset);
        if (BinaryUtils.isEmpty(esDiagramDTOList)) {
            return new RemoteResult(-1);
        }

        Object dataStatusObj = jsonObj.get("dataStatus");
        if (dataStatusObj instanceof Integer) {
            Integer dataStatus = (Integer) dataStatusObj;
            esDiagramDTOList.removeIf(dto -> !dto.getDiagram().getDataStatus().equals(dataStatus));
        }
        return new RemoteResult(esDiagramDTOList);
    }

   /* @RequestMapping("/querySimpleDiagramInfoByIds")
    @ModDesc(desc = "暂时废弃：工作台--根据视图id集合查询视图部分信息，生成缩略图", pDesc = "", rDesc = "视图信息", rType = RemoteResult.class)
    private RemoteResult querySimpleDiagramInfoByIds(@RequestBody Long[] diagramIds) {
        List<ESDiagramDTO> esDiagramDTOList = esDiagramPeer.querySimpleDiagramInfoByIds(diagramIds, "");
        if(BinaryUtils.isEmpty(esDiagramDTOList)) {
            return new RemoteResult(false, 404, "查询的视图不存在");
        }
        return new RemoteResult(esDiagramDTOList);
    }*/

    @CrossOrigin(origins = {})
    @PostMapping(value = "/processBatchThumbnail")
    @ModDesc(desc = "工作台-批量更新视图的缩略图信息", pDesc = "缩略图信息", rDesc = "缩略图信息", rType = RemoteResult.class)
    public RemoteResult processBatchThumbnail(@RequestBody ThumbnailBatch thumbnailBatch) {
        esDiagramPeer.processBatchThumbnail(thumbnailBatch);
        return new RemoteResult(thumbnailBatch);
    }

    @PostMapping("/saveOrUpdateDiagramComponent")
    @ModDesc(desc = "画布-批量更新视图组件", pDesc = "", rDesc = "视图信息", rType = RemoteResult.class)
    private RemoteResult saveOrUpdateDiagramComponent(@RequestBody String jsonStr) {
        Map<String, List<ESResponseStruct>> resultMap = esDiagramPeer.saveOrUpdateDiagramComponent(jsonStr);
        return new RemoteResult(resultMap);
    }

    /*@PostMapping("/saveOrUpdateDiagramComponent2")
    @ModDesc(desc = "画布-批量更新视图组件", pDesc = "", rDesc = "视图信息", rType = RemoteResult.class)
    private RemoteResult saveOrUpdateDiagramComponent2(@RequestBody String jsonStr) {
        Map<String, List<ESResponseStruct>> resultMap = esDiagramPeer.saveOrUpdateDiagramComponent2(jsonStr);
        return new RemoteResult(resultMap);
    }*/

    @PostMapping("/createHiddenLink")
    @ModDesc(desc = "在图中创建隐藏所属关系", pDesc = "", rDesc = "视图信息", rType = RemoteResult.class)
    private RemoteResult createHiddenLink(@RequestBody RuleParamsReq paramsReq) {
        Long diagramId = esDiagramPeer.queryDiagramInfoByEnergy(paramsReq.getDiagramId());
        ESDiagram esDiagram = esDiagramPeer.querySimpleDiagramInfoById(diagramId);
        RuleParams params = new RuleParams();
        params.setDiagramId(esDiagram.getId());
        params.setRuleType(DiagramRuleEnum.COMPONENT_DIAGRAM);
        params.setOwnerCode(esDiagram.getOwnerCode());
        params.setDomainId(esDiagram.getDomainId());
        params.setSheetId(paramsReq.getSheetId());
        params.setSrcCiCode(paramsReq.getSrcCiCode());
        params.setTargetCiCode(paramsReq.getTargetCiCode());
        int r = esDiagramPeer.createHiddenLink(params);
        return new RemoteResult(r);
    }

    @RequestMapping("/queryMyDirInfoPageByParentId")
    @ModDesc(desc = "工作台-根据文件夹id查询其包含的视图和文件夹信息", pDesc = "我的，查询参数", pType = MyDiagramCdt.class, rDesc = "展示相关信息", rType = DiagramDirInfo.class)
    public RemoteResult queryMyDirInfoPageByParentId(@RequestBody String body) {
        QueryPageCondition<MyDiagramCdt> pageCondition = RestTypeUtil.toPageCondition(body, MyDiagramCdt.class);
        MyDiagramCdt cdt = pageCondition.getCdt();
        Long dirId = cdt.getDirId();
        Integer dirType = cdt.getDirType();
        String like = cdt.getLike();
        Integer type = cdt.getType();
        Integer queryType = cdt.getQueryType();
        Integer dataStatus = cdt.getDataStatus();
        String orders = cdt.getOrders();
        LibType libType = cdt.getLibType();
//        if(BinaryUtils.isEmpty(dirId)) {
//            dirId = 0L;
//        }
        ESDiagramDirInfo result = esDiagramPeer.queryMyDirInfoPageByParentId(pageCondition.getPageNum(), pageCondition.getPageSize(), dirId, like, type, dirType, queryType, dataStatus, orders,libType);
        List<ESSimpleDiagramDTO> esSimpleDiagramList = result.getDiagramInfoPage().getData();
        List<ESDiagram> esDiagramList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(esSimpleDiagramList)) {
            for (ESSimpleDiagramDTO esSimpleDiagram : esSimpleDiagramList) {
                esDiagramList.add(esSimpleDiagram.getDiagram());
            }
            esTemplateDirSvc.relationTemDirByDiagramId(esDiagramList);
        }
//        ControllerUtils.returnJson(request, response, result);
        return new RemoteResult(result);
    }

    @PostMapping("/updateFullDiagram")
    @ModDesc(desc = "根据前台传的数据更新当前视图,即画布-导入", pDesc = "", rDesc = "视图信息", rType = RemoteResult.class)
    private RemoteResult updateFullDiagram(@RequestBody String jsonStr) {
        JSONObject jsonObject = new JSONObject(jsonStr);
        //被替换的diagramId
        String diagramId = jsonObject.getStr("diagramId");
        if (StringUtils.isEmpty(diagramId)) {
            throw new BinaryException("传参不符合规定，diagramId不能为空");
        }
        //进行替换的json
        //StringEscapeUtils.unescapeJava(jsonStr);
        String diagram = jsonObject.getStr("diagram");
        if (StringUtils.isEmpty(diagram)) {
            throw new BinaryException("传参不符合规定，diagram不能为空");
        }
        com.alibaba.fastjson.JSONObject diagramJson = com.alibaba.fastjson.JSONObject.parseObject(diagram);
        Object ident = diagramJson.get("ident");
        if (BinaryUtils.isEmpty(ident)) {
            // ident为旧的，改造之前的
            throw new BinaryException("当前导入的文件未检测到类型信息，请联系开发人员");
        }
        if (!ident.toString().equals("diagram_json")) {
            // 当前json数据不是视图
            throw new BinaryException("当前导入的文件不是视图文件，请重新检查");
        }
        Long aLong = esDiagramPeer.queryDiagramInfoByEnergy(diagramId);
        ESDiagramInfoDTO esDiagramInfo = JSONUtil.toBean(diagram, ESDiagramInfoDTO.class);
        ESDiagramDTO esDiagramDTO = esDiagramSvc.updateFullDiagram(aLong, esDiagramInfo);
        return new RemoteResult(esDiagramDTO);
    }

    @PostMapping("/importDiagramBatch")
    @ModDesc(desc = "工作台-导入", pDesc = "", rDesc = "视图信息", rType = RemoteResult.class)
    private RemoteResult importDiagramBatch(@RequestBody String body) {
        List<ESDiagramInfoDTO> esDiagramInfoList = JSONUtil.toList(body, ESDiagramInfoDTO.class);
        // 过滤出格式异常的数据
        for (ESDiagramInfoDTO esDiagramInfoDTO : esDiagramInfoList) {
            if (StringUtils.isEmpty(esDiagramInfoDTO.getIdent())) {
                // ident为旧的，改造之前的
                throw new BinaryException("当前导入的文件未检测到类型信息，请联系开发人员");
            }
            if (!esDiagramInfoDTO.getIdent().equals("diagram_json")) {
                // 当前json数据不是视图
                throw new BinaryException("当前导入的文件不是视图文件，请重新检查");
            }
        }
        for (ESDiagramInfoDTO esDiagramInfoDTO : esDiagramInfoList) {
            esDiagramInfoDTO.setPrepareDiagramId("");
        }
        List<String> diagramIdList = new ArrayList<>();
        // 通过文件类型校验之后出现的异常统一做成json数据异常
        try {
            diagramIdList = esDiagramSvc.importDiagramBatch(esDiagramInfoList);
        } catch (Exception e) {
            throw new BinaryException("当前导入的json数据存在异常，请检查数据");
        }
        return new RemoteResult(diagramIdList);
    }

    @PostMapping("/copyDiagramById")
    @ModDesc(desc = "画布-创建当前视图的副本 && 根据模板新建", pDesc = "", rDesc = "视图信息", rType = RemoteResult.class)
    private RemoteResult copyDiagramById(@RequestBody ESDiagramMoveCdt diagramMoveCdt) {
        String newDiagramId = esDiagramApiClient.copyDiagramById(diagramMoveCdt);
        return new RemoteResult(newDiagramId);
    }

    @PostMapping("/copyDiagramByIds")
    @ModDesc(desc = "工作台-根据视图id集合复制视图", pDesc = "", rDesc = "视图信息", rType = RemoteResult.class)
    private RemoteResult copyDiagramByIds(@RequestBody ESDiagramMoveCdt diagramMoveCdt) {
        List<Long> diagramIdList = esDiagramApiClient.copyDiagramByIds(diagramMoveCdt);
        List<String> energyList = new ArrayList<>(diagramIdList.size());
        diagramIdList.forEach(t -> {
            energyList.add(SecureUtil.md5(String.valueOf(t)).substring(8, 24));
        });
        return new RemoteResult(energyList);
    }

    @PostMapping("/copyDirById")
    @ModDesc(desc = "工作台-复制文件夹和视图到指定文件夹", pDesc = "复制文件夹操作", pType = MoveDirAndDiagramCdt.class, rDesc = "0失败1成功", rType = Integer.class)
    public RemoteResult copyDirById(@RequestBody String body) {
        MoveDirAndDiagramCdt moveDirAndDiagramCdt = JSON.toObject(body, MoveDirAndDiagramCdt.class);
        Long targetDirId = moveDirAndDiagramCdt.getTargetDirId();
        Long[] dirIds = moveDirAndDiagramCdt.getDirIds();
        String[] diagramIds = moveDirAndDiagramCdt.getDiagramIds();
        if (BinaryUtils.isEmpty(targetDirId)) {
            return new RemoteResult(false, 500, "目标文件夹不能为空");
        }
        if ((BinaryUtils.isEmpty(dirIds) && BinaryUtils.isEmpty(diagramIds))) {
            return new RemoteResult(false, 500, "待复制文件夹id和视图id不能同时为空");
        }
        List<Long> result = esDiagramApiClient.copyDirById(targetDirId, dirIds, diagramIds);
        return new RemoteResult(result);
    }

    @PostMapping("/moveDirAndDiagram")
    @ModDesc(desc = "工作台-移动目录和视图到指定文件夹", pDesc = "移动目录视图和目标目录", pType = MoveDirAndDiagramCdt.class, rDesc = "0失败1成功", rType = Integer.class)
    public RemoteResult moveDirAndDiagram(@RequestBody String body) {
        Integer result;
        if(BinaryUtils.isEmpty(body)){
            throw new BinaryException("传参不能为空");
        }
        com.alibaba.fastjson.JSONObject jsonObject = com.alibaba.fastjson.JSONObject.parseObject(body);
        if (!BinaryUtils.isEmpty(jsonObject.get("targetDirId"))) {
            Object targetDirId1 = jsonObject.get("targetDirId");
            String regex = ".*[a-zA-Z]+.*";
            Matcher matcher = Pattern.compile(regex).matcher(targetDirId1.toString());
            if (matcher.matches()) {
                // ? 分裂了
                return new RemoteResult(false, 500, "");
            }
        }
        MoveDirAndDiagramCdt move = JSON.toObject(body, MoveDirAndDiagramCdt.class);
        Long targetDirId = move.getTargetDirId();
        Long[] dirIds = move.getDirIds();
        String[] diagramIds = move.getDiagramIds();
        if (BinaryUtils.isEmpty(targetDirId)) {
            return new RemoteResult("目标文件夹不能为空");
        }
        if (BinaryUtils.isEmpty(dirIds) && BinaryUtils.isEmpty(diagramIds)) {
            return new RemoteResult("待移动目录和视图不能同时为空");
        }
        result = esDiagramPeer.moveDirAndDiagram(move);
        if (500 == result) {
            return new RemoteResult(false, 500, "文件夹名称存在重名");
        }
        return new RemoteResult(result);
    }

    @PostMapping("/getCountByDirId")
    public RemoteResult getCountByDirId(@RequestBody String jsonStr) {
        JSONObject jsonObject = new JSONObject(jsonStr);
        Long dirId = jsonObject.getLong("dirId");
        Integer type = jsonObject.getInt("type");
        MessageUtil.checkEmpty(dirId, "dirId");
        MessageUtil.checkEmpty(type, "type");
        // long totalCount = esDiagramSvc.getCountByDirId(dirId, type);
        return new RemoteResult(0L);
    }

    @PostMapping("/transOldDiagram")
    public RemoteResult transOldDiagram(@RequestBody String body) {
//        logger.info("transOldDiagram进来了，带的参数：" + body);
        JSONObject jsonObject = new JSONObject(body);
        int pageNum = jsonObject.getInt("pageNum");
        MessageUtil.checkEmpty(pageNum, "pageNum");
        int pageSize = jsonObject.getInt("pageSize");
        MessageUtil.checkEmpty(pageSize, "pageSize");
        JSONArray diagramIdArray = jsonObject.getJSONArray("diagramIds");
        Long[] diagramIdArr = new Long[0];
        if (diagramIdArray != null) {
            diagramIdArr = diagramIdArray.toArray(new Long[0]);
        }
        // String result = esDiagramSvc.transOldDiagram(diagramIdArr, pageNum, pageSize);
        return new RemoteResult(null);
    }

    @RequestMapping("/deleteDiagramByIds")
    @ModDesc(desc = "物理删除视图", pDesc = "", rDesc = "视图信息", rType = RemoteResult.class)
    private RemoteResult deleteDiagramByIds(@RequestBody String[] diagramIds) {
        Long[] ids = esDiagramPeer.queryDiagramInfoBydEnergy(diagramIds);
        Integer integer = esDiagramSvc.deleteDiagramByIds(ids);
        return new RemoteResult(integer);
    }

    @RequestMapping("/deleteDiagramById")
    @ModDesc(desc = "物理删除视图", pDesc = "", rDesc = "视图信息", rType = RemoteResult.class)
    private RemoteResult deleteDiagramById(@RequestBody String diagramId) {
        Long[] ids = esDiagramPeer.queryDiagramInfoBydEnergy(new String[]{diagramId});
        String resultStr = esDiagramSvc.deleteDiagramById(ids[0]);
        return new RemoteResult(resultStr);
    }

    @RequestMapping("/updateDiagramByQuery")
    @ModDesc(desc = "测试updateByQuery", pDesc = "", rDesc = "视图信息", rType = RemoteResult.class)
    private RemoteResult updateDiagramByQuery(@RequestBody String jsonStr) {
        JSONObject jsonObject = new JSONObject(jsonStr);
        Long diagramId = jsonObject.getLong("diagramId");
        Long value = jsonObject.getLong("value");
        esDiagramSvc.updateDiagramByQuery(diagramId, value);
        return new RemoteResult("");
    }

    @RequestMapping("/exportDiagram")
    @ModDesc(desc = "导出视图信息为json", pDesc = "", rDesc = "视图信息", rType = RemoteResult.class)
    private RemoteResult exportDiagram(@RequestBody String jsonStr) {
        String jsonPath = esDiagramSvc.exportDiagram(jsonStr);
        return new RemoteResult(jsonPath);
    }

    @PostMapping("/fxCopyDiagramByIds")
    @ModDesc(desc = "工作台-根据视图id集合复制视图-(伏羲项目)", pDesc = "", rDesc = "视图信息", rType = RemoteResult.class)
    private RemoteResult fxCopyDiagramByIds(@RequestBody ESDiagramMoveCdt diagramMoveCdt) {
        List<Long> diagramIdList = esDiagramApiClient.fxCopyDiagramByIds(diagramMoveCdt);
        List<String> energyList = new ArrayList<>(diagramIdList.size());
        diagramIdList.forEach(t -> {
            energyList.add(SecureUtil.md5(String.valueOf(t)).substring(8, 24));
        });
        return new RemoteResult(energyList);
    }

    @PostMapping("/getSkipStatusByDiagramIds")
    @ModDesc(desc = "根据视图id返现下钻标识", pDesc = "", rDesc = "视图信息", rType = RemoteResult.class)
    private RemoteResult getSkipStatusByDiagramIds(@RequestBody Map<String, List<String>> data) {
        Map<String, Boolean> result = esDiagramPeer.getSkipStatusByDiagramIds(data);
        return new RemoteResult(result);
    }

    @PostMapping("/queryDiagramDBInfoByIds")
    @ModDesc(desc = "根据视图id在数据库直接查询", pDesc = "", rDesc = "视图信息", rType = RemoteResult.class)
    private RemoteResult queryDiagramDBInfoByIds(@RequestBody String jsonStr) {
        JSONObject jsonObj = new JSONObject(jsonStr);
        Boolean needAuth = jsonObj.getBool("needAuth");
        if (needAuth == null) {
            needAuth = true;
        }
        JSONArray diagramIdArr = jsonObj.getJSONArray("diagramIds");
        if (BinaryUtils.isEmpty(diagramIdArr)) {
            throw new BinaryException("传参错误，视图id数组不能为空");
        }
        String[] diagramIds = diagramIdArr.toArray(new String[0]);
        diagramIds = ArrayUtil.distinct(diagramIds);

        List<ESDiagram> esDiagrams = esDiagramPeer.queryDBDiagramInfoByIds(diagramIds);

        return new RemoteResult(esDiagrams);
    }

/*
    @RequestMapping("/iconRmHttp")
    @ModDesc(desc = "iconRmHttp", pDesc = "", rDesc = "", rType = RemoteResult.class)
    private RemoteResult iconRmHttp() {
        return new RemoteResult(esDiagramSvc.iconRmHttp());
    }*/

    @PostMapping("/getRelateInfoByDiagramIds")
    @ModDesc(desc = "批量查询当前视图关联视图的跳转状态", pDesc = "", rDesc = "视图信息", rType = RemoteResult.class)
    private RemoteResult getRelateInfoByDiagramIds(HttpServletResponse response, @RequestBody ESDiagramMoveCdt diagramMoveCdt) {
        try {
            Map<String, Set<DiagramRelationInfo>> result = esDiagramPeer.getRelateInfoByDiagramIds(diagramMoveCdt.getDiagramId(), diagramMoveCdt.getBrowseStatus());
            return new RemoteResult(result);
        }catch (Exception e){
            response.setStatus(HttpStatus.NOT_FOUND.value());
            return new RemoteResult(false,404,e.getMessage());
        }
    }

    @GetMapping("getReleaseDiagramList")
    public RemoteResult getReleaseDiagramList(@RequestParam String like) {
        List<ESDiagram> result = esDiagramPeer.getReleaseDiagramList(like);
        return new RemoteResult(result);
    }

    @GetMapping("deleteUselessDiagramInfo")
    public RemoteResult deleteUselessDiagramInfo() {
        return new RemoteResult(esDiagramSvcV2.deleteUselessDiagramInfo());
    }

    @GetMapping("/queryFlowStatusById")
    @ModDesc(desc = "画布-获取视图审批状态", pDesc = "", rDesc = "视图审批状态", rType = RemoteResult.class)
    private RemoteResult queryFlowStatusById(@RequestParam String diagramId) {
        Integer flowStatus = esDiagramPeer.queryFlowStatusById(diagramId);
        return new RemoteResult(flowStatus);
    }
}
