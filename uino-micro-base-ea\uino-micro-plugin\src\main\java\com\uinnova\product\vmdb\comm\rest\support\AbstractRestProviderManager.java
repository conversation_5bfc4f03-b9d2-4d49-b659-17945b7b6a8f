package com.uinnova.product.vmdb.comm.rest.support;

import com.binary.core.lang.Conver;
import com.binary.core.util.BinaryUtils;
import com.binary.framework.exception.ServiceException;
import com.binary.json.JSON;
import com.uinnova.product.vmdb.comm.i18n.MessageUtil;
import com.uinnova.product.vmdb.comm.rest.RestProviderManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Method;
import java.lang.reflect.Type;
import java.util.*;

/**
 * 
 * <AUTHOR>
 *
 */
public class AbstractRestProviderManager implements RestProviderManager {
    private static final Logger logger = LoggerFactory.getLogger(AbstractRestProviderManager.class);

    private static final Set<String> objectMethods = new HashSet<String>();

    static {
        objectMethods.add("equals");
        objectMethods.add("toString");
        objectMethods.add("hashCode");
        objectMethods.add("getClass");
    }

    private final Object syncobj = new Object();
    private final Map<String, ProviderPoint> pointMap = new HashMap<String, ProviderPoint>();

    protected void addPoint(Method method, Class<?> ifaceType, Object instance) {
        MessageUtil.checkEmpty(method, "method");
        MessageUtil.checkEmpty(ifaceType, "ifaceType");
        MessageUtil.checkEmpty(instance, "instance");
        if (!ifaceType.isAssignableFrom(instance.getClass())) {
            throw new ServiceException(" instance '" + instance.getClass().getName() + "' not typeof '" + ifaceType.getName() + "'! ");
        }

        String name = ifaceType.getName() + "." + method.getName();
        logger.info(" add provider '" + name + "' ... ");

        synchronized (syncobj) {
            if (pointMap.containsKey(name)) {
                throw new ServiceException(" is exists provider.method '" + name + "'! ");
            }

            ProviderPoint point = new ProviderPoint(name, method, ifaceType, instance);
            pointMap.put(name, point);
        }
    }

    protected void addPointBatch(Class<?> ifaceType, Object instance) {
        MessageUtil.checkEmpty(ifaceType, "ifaceType");
        MessageUtil.checkEmpty(instance, "instance");
        if (!ifaceType.isAssignableFrom(instance.getClass())) {
            throw new ServiceException(" instance '" + instance.getClass().getName() + "' not typeof '" + ifaceType.getName() + "'! ");
        }

        Method[] ms = ifaceType.getMethods();
        if (ms != null && ms.length > 0) {
            for (int i = 0; i < ms.length; i++) {
                Method m = ms[i];
                String name = m.getName();
                if (objectMethods.contains(name)) {
                    continue;
                }

                addPoint(m, ifaceType, instance);
            }
        }
    }

    @Override
    public Object execute(String ifaceName, String methodName, String jsonParams) {
        MessageUtil.checkEmpty(ifaceName, "ifaceName");
        MessageUtil.checkEmpty(methodName, "methodName");
        String name = ifaceName.trim() + "." + methodName.trim();
        ProviderPoint point = pointMap.get(name);
        if (point == null) {
            throw new ServiceException(" not found provider method '" + name + "'! ");
        }
        Object[] params = doParseJsonParams(point, jsonParams);
        Object result = doExecutePoint(point, params);
        return result;
    }

    @SuppressWarnings("rawtypes")
    protected Object[] doParseJsonParams(ProviderPoint point, String jsonParams) {
        if (jsonParams == null || (jsonParams = jsonParams.trim()).length() == 0) {
            return null;
        }
        char left = '[';
        char right = ']';
        if (jsonParams.charAt(0) != left || jsonParams.charAt(jsonParams.length() - 1) != right) {
            throw new ServiceException(" the params is wrong '" + jsonParams + "'! ");
        }

        Type[] pts = point.getGenericParameterTypes();
        Object obj = JSON.toObject(jsonParams);
        if (!(obj instanceof List)) {
            return null;
        }
        List values = (List) obj;
        Object[] params = new Object[pts.length];
        for (int i = 0; i < pts.length && i < values.size(); i++) {
            params[i] = Conver.mapping(pts[i], values.get(i));
        }
        return params;
    }

    protected Object doExecutePoint(ProviderPoint point, Object[] params) {
        // 方法调用
        try {
            return point.getMethod().invoke(point.getInstance(), params);
        } catch (Throwable t) {
            throw BinaryUtils.transException(t, ServiceException.class, " execute provider method '" + point.getName() + "' error! ");
        }
    }

}
