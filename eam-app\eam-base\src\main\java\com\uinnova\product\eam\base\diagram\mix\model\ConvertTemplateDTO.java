package com.uinnova.product.eam.base.diagram.mix.model;

public class ConvertTemplateDTO {

    private String diagramId;

    private Long templateDirId;

    // 1为转为模板  2为取消模板
    private Integer type = 1;

    public String getDescribeInfo() {
        return describeInfo;
    }

    public void setDescribeInfo(String describeInfo) {
        this.describeInfo = describeInfo;
    }

    private String describeInfo;

    public String getDiagramId() {
        return diagramId;
    }

    public void setDiagramId(String diagramId) {
        this.diagramId = diagramId;
    }

    public Long getTemplateDirId() {
        return templateDirId;
    }

    public void setTemplateDirId(Long templateDirId) {
        this.templateDirId = templateDirId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }
}
