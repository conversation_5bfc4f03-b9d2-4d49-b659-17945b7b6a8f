package com.uinnova.product.eam.comm.model;




import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;


@Comment("角色组织表[VC_ROLE_ORG]")
public class CVcRoleOrg implements Condition {
	private static final long serialVersionUID = 1L;


	@Comment("ID[ID] operate-Equal[=]")
	private Long id;


	@Comment("ID[ID] operate-In[in]")
	private Long[] ids;


	@Comment("ID[ID] operate-GTEqual[>=]")
	private Long startId;

	@Comment("ID[ID] operate-LTEqual[<=]")
	private Long endId;


	@Comment("角色ID[ROLE_ID] operate-Equal[=]")
	private Long roleId;


	@Comment("角色ID[ROLE_ID] operate-In[in]")
	private Long[] roleIds;


	@Comment("角色ID[ROLE_ID] operate-GTEqual[>=]")
	private Long startRoleId;

	@Comment("角色ID[ROLE_ID] operate-LTEqual[<=]")
	private Long endRoleId;


	@Comment("组织名称[ORG_NAMES] operate-Like[like]")
	private String orgNames;
	
	@Comment("组织名称[ORG_NAMES] operate-In[in]")
	private String[] orgNamesIn;


	@Comment("查看的权限[SEE_AUTH] operate-Equal[=]")
	private Integer seeAuth;


	@Comment("查看的权限[SEE_AUTH] operate-In[in]")
	private Integer[] seeAuths;


	@Comment("查看的权限[SEE_AUTH] operate-GTEqual[>=]")
	private Integer startSeeAuth;

	@Comment("查看的权限[SEE_AUTH] operate-LTEqual[<=]")
	private Integer endSeeAuth;


	@Comment("编辑的权限[EDIT_AUTH] operate-Equal[=]")
	private Integer editAuth;


	@Comment("编辑的权限[EDIT_AUTH] operate-In[in]")
	private Integer[] editAuths;


	@Comment("编辑的权限[EDIT_AUTH] operate-GTEqual[>=]")
	private Integer startEditAuth;

	@Comment("编辑的权限[EDIT_AUTH] operate-LTEqual[<=]")
	private Integer endEditAuth;


	@Comment("所属域[DOMAIN_ID] operate-Equal[=]")
	private Long domainId;


	@Comment("所属域[DOMAIN_ID] operate-In[in]")
	private Long[] domainIds;


	@Comment("所属域[DOMAIN_ID] operate-GTEqual[>=]")
	private Long startDomainId;

	@Comment("所属域[DOMAIN_ID] operate-LTEqual[<=]")
	private Long endDomainId;


	@Comment("数据状态[DATA_STATUS] operate-Equal[=]    数据状态:0=删除，1=正常")
	private Integer dataStatus;


	@Comment("数据状态[DATA_STATUS] operate-In[in]    数据状态:0=删除，1=正常")
	private Integer[] dataStatuss;


	@Comment("数据状态[DATA_STATUS] operate-GTEqual[>=]    数据状态:0=删除，1=正常")
	private Integer startDataStatus;

	@Comment("数据状态[DATA_STATUS] operate-LTEqual[<=]    数据状态:0=删除，1=正常")
	private Integer endDataStatus;


	@Comment("创建人[CREATOR] operate-Like[like]")
	private String creator;


	@Comment("创建人[CREATOR] operate-Equal[=]")
	private String creatorEqual;


	@Comment("创建人[CREATOR] operate-In[in]")
	private String[] creators;


	@Comment("修改人[MODIFIER] operate-Like[like]")
	private String modifier;


	@Comment("修改人[MODIFIER] operate-Equal[=]")
	private String modifierEqual;


	@Comment("修改人[MODIFIER] operate-In[in]")
	private String[] modifiers;


	@Comment("创建时间[CREATE_TIME] operate-Equal[=]    创建时间:yyyyMMddHHmmss")
	private Long createTime;


	@Comment("创建时间[CREATE_TIME] operate-In[in]    创建时间:yyyyMMddHHmmss")
	private Long[] createTimes;


	@Comment("创建时间[CREATE_TIME] operate-GTEqual[>=]    创建时间:yyyyMMddHHmmss")
	private Long startCreateTime;

	@Comment("创建时间[CREATE_TIME] operate-LTEqual[<=]    创建时间:yyyyMMddHHmmss")
	private Long endCreateTime;


	@Comment("更新时间[MODIFY_TIME] operate-Equal[=]    更新时间:yyyyMMddHHmmss")
	private Long modifyTime;


	@Comment("更新时间[MODIFY_TIME] operate-In[in]    更新时间:yyyyMMddHHmmss")
	private Long[] modifyTimes;


	@Comment("更新时间[MODIFY_TIME] operate-GTEqual[>=]    更新时间:yyyyMMddHHmmss")
	private Long startModifyTime;

	@Comment("更新时间[MODIFY_TIME] operate-LTEqual[<=]    更新时间:yyyyMMddHHmmss")
	private Long endModifyTime;




	public Long getId() {
		return this.id;
	}
	public void setId(Long id) {
		this.id = id;
	}


	public Long[] getIds() {
		return this.ids;
	}
	public void setIds(Long[] ids) {
		this.ids = ids;
	}


	public Long getStartId() {
		return this.startId;
	}
	public void setStartId(Long startId) {
		this.startId = startId;
	}


	public Long getEndId() {
		return this.endId;
	}
	public void setEndId(Long endId) {
		this.endId = endId;
	}


	public Long getRoleId() {
		return this.roleId;
	}
	public void setRoleId(Long roleId) {
		this.roleId = roleId;
	}


	public Long[] getRoleIds() {
		return this.roleIds;
	}
	public void setRoleIds(Long[] roleIds) {
		this.roleIds = roleIds;
	}


	public Long getStartRoleId() {
		return this.startRoleId;
	}
	public void setStartRoleId(Long startRoleId) {
		this.startRoleId = startRoleId;
	}


	public Long getEndRoleId() {
		return this.endRoleId;
	}
	public void setEndRoleId(Long endRoleId) {
		this.endRoleId = endRoleId;
	}


	public String getOrgNames() {
		return this.orgNames;
	}
	public void setOrgNames(String orgNames) {
		this.orgNames = orgNames;
	}


	public Integer getSeeAuth() {
		return this.seeAuth;
	}
	public void setSeeAuth(Integer seeAuth) {
		this.seeAuth = seeAuth;
	}


	public Integer[] getSeeAuths() {
		return this.seeAuths;
	}
	public void setSeeAuths(Integer[] seeAuths) {
		this.seeAuths = seeAuths;
	}


	public Integer getStartSeeAuth() {
		return this.startSeeAuth;
	}
	public void setStartSeeAuth(Integer startSeeAuth) {
		this.startSeeAuth = startSeeAuth;
	}


	public Integer getEndSeeAuth() {
		return this.endSeeAuth;
	}
	public void setEndSeeAuth(Integer endSeeAuth) {
		this.endSeeAuth = endSeeAuth;
	}


	public Integer getEditAuth() {
		return this.editAuth;
	}
	public void setEditAuth(Integer editAuth) {
		this.editAuth = editAuth;
	}


	public Integer[] getEditAuths() {
		return this.editAuths;
	}
	public void setEditAuths(Integer[] editAuths) {
		this.editAuths = editAuths;
	}


	public Integer getStartEditAuth() {
		return this.startEditAuth;
	}
	public void setStartEditAuth(Integer startEditAuth) {
		this.startEditAuth = startEditAuth;
	}


	public Integer getEndEditAuth() {
		return this.endEditAuth;
	}
	public void setEndEditAuth(Integer endEditAuth) {
		this.endEditAuth = endEditAuth;
	}


	public Long getDomainId() {
		return this.domainId;
	}
	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}


	public Long[] getDomainIds() {
		return this.domainIds;
	}
	public void setDomainIds(Long[] domainIds) {
		this.domainIds = domainIds;
	}


	public Long getStartDomainId() {
		return this.startDomainId;
	}
	public void setStartDomainId(Long startDomainId) {
		this.startDomainId = startDomainId;
	}


	public Long getEndDomainId() {
		return this.endDomainId;
	}
	public void setEndDomainId(Long endDomainId) {
		this.endDomainId = endDomainId;
	}


	public Integer getDataStatus() {
		return this.dataStatus;
	}
	public void setDataStatus(Integer dataStatus) {
		this.dataStatus = dataStatus;
	}


	public Integer[] getDataStatuss() {
		return this.dataStatuss;
	}
	public void setDataStatuss(Integer[] dataStatuss) {
		this.dataStatuss = dataStatuss;
	}


	public Integer getStartDataStatus() {
		return this.startDataStatus;
	}
	public void setStartDataStatus(Integer startDataStatus) {
		this.startDataStatus = startDataStatus;
	}


	public Integer getEndDataStatus() {
		return this.endDataStatus;
	}
	public void setEndDataStatus(Integer endDataStatus) {
		this.endDataStatus = endDataStatus;
	}


	public String getCreator() {
		return this.creator;
	}
	public void setCreator(String creator) {
		this.creator = creator;
	}


	public String getCreatorEqual() {
		return this.creatorEqual;
	}
	public void setCreatorEqual(String creatorEqual) {
		this.creatorEqual = creatorEqual;
	}


	public String[] getCreators() {
		return this.creators;
	}
	public void setCreators(String[] creators) {
		this.creators = creators;
	}


	public String getModifier() {
		return this.modifier;
	}
	public void setModifier(String modifier) {
		this.modifier = modifier;
	}


	public String getModifierEqual() {
		return this.modifierEqual;
	}
	public void setModifierEqual(String modifierEqual) {
		this.modifierEqual = modifierEqual;
	}


	public String[] getModifiers() {
		return this.modifiers;
	}
	public void setModifiers(String[] modifiers) {
		this.modifiers = modifiers;
	}


	public Long getCreateTime() {
		return this.createTime;
	}
	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}


	public Long[] getCreateTimes() {
		return this.createTimes;
	}
	public void setCreateTimes(Long[] createTimes) {
		this.createTimes = createTimes;
	}


	public Long getStartCreateTime() {
		return this.startCreateTime;
	}
	public void setStartCreateTime(Long startCreateTime) {
		this.startCreateTime = startCreateTime;
	}


	public Long getEndCreateTime() {
		return this.endCreateTime;
	}
	public void setEndCreateTime(Long endCreateTime) {
		this.endCreateTime = endCreateTime;
	}


	public Long getModifyTime() {
		return this.modifyTime;
	}
	public void setModifyTime(Long modifyTime) {
		this.modifyTime = modifyTime;
	}


	public Long[] getModifyTimes() {
		return this.modifyTimes;
	}
	public void setModifyTimes(Long[] modifyTimes) {
		this.modifyTimes = modifyTimes;
	}


	public Long getStartModifyTime() {
		return this.startModifyTime;
	}
	public void setStartModifyTime(Long startModifyTime) {
		this.startModifyTime = startModifyTime;
	}


	public Long getEndModifyTime() {
		return this.endModifyTime;
	}
	public void setEndModifyTime(Long endModifyTime) {
		this.endModifyTime = endModifyTime;
	}
	public String[] getOrgNamesIn() {
		return orgNamesIn;
	}
	public void setOrgNamesIn(String[] orgNamesIn) {
		this.orgNamesIn = orgNamesIn;
	}
	public void setOrgNamesIn(Long[] orgNamesIn) {
		if(orgNamesIn!=null&&orgNamesIn.length>0){
			this.orgNamesIn=new String[orgNamesIn.length];
			for(int i=0;i<orgNamesIn.length;i++){
				this.orgNamesIn[i]=String.valueOf(orgNamesIn[i]);
			}
		}
	}
	


}


