package com.uino.service.sys.microservice;

import com.binary.jdbc.Page;
import com.uino.bean.sys.base.LdapUserMapping;
import com.uino.bean.sys.base.LoginAuthConfig;
import com.uino.bean.sys.business.ActiveLoginAuthConfigDto;
import com.uino.bean.sys.business.LoginAuthConfigDto;

/**
 * 登录集成规则配置
 * 
 * <AUTHOR>
 *
 */
public interface ILoginAuthConfigSvc {

    /**
     * 通过ID 查看配置信息
     * 
     * @param id 配置ID
     * @return
     */
    LoginAuthConfig queryById(Long id);

    /**
     * 激活权限配置项
     * 
     * @param active
     */
    void activeConfig(ActiveLoginAuthConfigDto active);

    /**
     * 查询当前集成配置项
     * 
     * @param authConfigCdt 查询条件
     * @return
     */
    Page<LoginAuthConfig> queryPage(LoginAuthConfigDto authConfigCdt);

    /**
     * 测试连接是否可用
     * 
     * @param authConfig
     * @return
     */
    boolean testConnection(LoginAuthConfig authConfig);

    /**
     * 
     * @param authConfig
     * @return
     */
    LdapUserMapping testConnectionAndMappingUser(LoginAuthConfig authConfig);

    /**
     * 保存或更新集成登录配置
     * 
     * @param authConfig
     * @return id
     */
    Long saveOrUpdate(LoginAuthConfig authConfig);

    /**
     * 删除配置项
     * 
     * @param id
     */
    void removeById(Long id);

}
