package com.uinnova.product.eam.service;

import com.binary.jdbc.Page;
import com.uinnova.product.eam.comm.model.CVcBaseConfig;
import com.uinnova.product.eam.comm.model.VcBaseConfig;

import java.util.List;

/**
 * @deprecated 使用新的接口 新的接口 {@link BaseConfigService}
 */
@Deprecated
public interface VcBaseConfigSvc {

	/**
	 * 查询配置列表
	 * @param domainId
	 * @param cdt
	 * @param orders
	 * @return
	 * */
	public List<VcBaseConfig> queryBaseConfigList(Long domainId , CVcBaseConfig cdt,String orders);

	/**
	 * 根据code查询内容信息
	 * @param domainId
	 * @param cfgCode
	 * @return cfgContent
	 * */
	public String queryCfgContentByCode(Long domainId , String cfgCode);

	/**
	 * 分页查询配置列表
	 * @param domainId
	 * @param pageNum
	 * @param pageSize
	 * @param cdt
	 * @param orders
	 * @return
	 * */
	public Page<VcBaseConfig> queryBaseConfigPage(Long domainId , Integer pageNum, Integer pageSize ,CVcBaseConfig cdt,String orders);

	/**
	 * 根据id查询配置信息
	 * @param id
	 * @return
	 * */
	public VcBaseConfig queryConfigById(Long id);

	/**
	 * 保存或者更新配置
	 * @param domainId
	 * @param record
	 * @return
	 * */
	public Long saveOrUpdateBaseConfig(Long domainId, VcBaseConfig record);

	/**
	 * 保存或者更新配置
	 * @param domainId
	 * @param records批量
	 * @return
	 * */
	public Integer saveOrUpdateBaseConfigBatch(Long domainId, List<VcBaseConfig> record);


	/**
	 * 条件删除配置
	 * @param domainId
	 * @param cdt
	 * @return
	 * */
	public Integer removeBaseConfigByCdt(Long domainId ,CVcBaseConfig cdt);

	/**
	 * 通过id删除配置
	 * @param id
	 * @return
	 * */
	public Integer removeBaseConfigById(Long id);

}
