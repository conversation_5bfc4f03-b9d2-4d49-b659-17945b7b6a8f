package com.uino.service.cmdb.dataset.microservice;

import com.alibaba.fastjson.JSONObject;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.cmdb.base.dataset.DataSetMallApi;

/**
 * @Classname MallApi
 * @Description 数据处理
 * @Date 2020/3/23 8:29
 * @Created by sh
 */
public interface IMallApiSvc {
    /**
     * 特性校验（例如关系遍历只校验规则）
     *
     * @param user
     * @param jsonObject
     * @return
     */
    DataSetMallApi checkCharacteristic(SysUser user, JSONObject jsonObject);


    /**
     * 批量
     *
     * @param dataSetMallApi
     * @param jsonObject
     * @return
     */
    JSONObject execute(DataSetMallApi dataSetMallApi, JSONObject jsonObject);

    /**
     * 实时
     *
     * @param dataSetMallApi
     * @param jsonObject
     * @return
     */
    JSONObject realTimeExecute(DataSetMallApi dataSetMallApi, JSONObject jsonObject);


}
