package com.uino.provider.server.web.cmdb.mvc;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.binary.core.io.Resource;
import com.uino.bean.cmdb.base.dataset.DataSetMallApiRelationRule;
import com.uino.bean.cmdb.base.dataset.DataSetMallApiType;
import com.uino.bean.cmdb.base.dataset.batch.DataSetTableResult;
import com.uino.bean.cmdb.base.dataset.chart.Chart;
import com.uino.bean.cmdb.base.dataset.query.DataSetMallApiDto;
import com.uino.bean.cmdb.base.dataset.query.DataSetMallApiRelationRuleDto;
import com.uino.bean.cmdb.business.dataset.*;
import com.uino.bean.dataset.base.DataSetPathInfoVo;
import com.uino.bean.dataset.base.DataSetThumbnailDTO;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.tp.query.MetricAttrValQueryDTO;
import com.uino.bean.tp.query.QueryDataTableDTO;
import com.uino.bean.tp.query.TpRuleReqDTO;
import com.uino.provider.feign.cmdb.DataSetFeign;
import com.uino.service.cmdb.dataset.microservice.IDataSetSvc;
import com.uino.util.sys.SysUtil;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

/**
 * 
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("feign/cmdb/dataset")
public class DataSetFeignMvc implements DataSetFeign {

	Log logger = LogFactory.getLog(DataSetFeignMvc.class);
	
	@Autowired
	private IDataSetSvc datasetSvc;
	
	@Override
	public Long saveOrUpdateDataSet(JSONObject body) {
		logger.info("saveOrUpdateDataSet param: "+body.toString());
		Long now = System.currentTimeMillis();
		Long ret = datasetSvc.saveOrUpdateDataSet(body.getJSONObject("json"), body.getBoolean("ifExeBatchProcess"));
		logger.info("saveOrUpdateDataSet time: "+(System.currentTimeMillis()-now));
		return ret;
	}

	@Override
	public Boolean delete(Long id) {
		logger.info("delete param: "+id);
		Long now = System.currentTimeMillis();
		boolean ret = datasetSvc.delete(id);
		logger.info("delete time: "+(System.currentTimeMillis()-now));
		return ret;
	}

	@Override
	public List<JSONObject> findDataSet(JSONObject body) {
		logger.info("findDataSet param: "+body.toString());
		Long now = System.currentTimeMillis();
		List<JSONObject> ret = datasetSvc.findDataSet(body.getString("name"), body.getBoolean("isMyself"), body.getString("url"), JSONArray.parseArray(body.getString("typeList"), DataSetMallApiType.class));
		logger.info("findDataSet time: "+(System.currentTimeMillis()-now));
		return ret;
	}

	@Override
	public JSONObject findDataSetById(JSONObject body) {
		logger.info("findDataSetById param: "+body);
		Long now = System.currentTimeMillis();
		Long id = body.getLong("id");
		Boolean isCheck = body.getBoolean("isCheck");
		JSONObject ret = datasetSvc.findDataSetById(id, isCheck);
		logger.info("findDataSetById time: "+(System.currentTimeMillis()-now));
		return ret;
	}

	@Override
	public JSONObject execute(JSONObject body) {
		logger.info("execute param: "+body.toString());
		Long now = System.currentTimeMillis();
		Long id = body.getLong("id");
		JSONObject ret;
		if (null == id) {
			ret = datasetSvc.execute(body.getLong("domainId"), body.getJSONObject("body"));
		} else {
			ret = datasetSvc.execute(body.getLong("domainId"), body.getString("language"), body.getLong("id"), body.getJSONObject("body"));
		}
		logger.info("execute time: "+(System.currentTimeMillis()-now));
		return ret;
	}

	@Override
	public JSONObject realTimeExecute(JSONObject body) {
		logger.info("realTimeExecute param: "+body.toString());
		Long now = System.currentTimeMillis();
		JSONObject ret = datasetSvc.realTimeExecute(body.getLong("domainId"), body.getString("language"), body.getLong("id"), body.getJSONObject("body"));
		logger.info("realTimeExecute time: "+(System.currentTimeMillis()-now));
		return ret;
	}

	@Override
	public Boolean shareDataSet(JSONObject body) {
		logger.info("execshareDataSetute param: "+body.toString());
		Long now = System.currentTimeMillis();
		boolean ret = datasetSvc.shareDataSet(body.getLong("id"), body.getInteger("shareLevel"));
		logger.info("shareDataSet time: "+(System.currentTimeMillis()-now));
		return ret;
	}

	@Override
	public List<Map<String, Object>> getDataSetSheets(Long dataSetId) {
		logger.info("getDataSetSheets param: "+dataSetId);
		Long now = System.currentTimeMillis();
		List<Map<String, Object>> ret = datasetSvc.getDataSetSheets(dataSetId);
		logger.info("getDataSetSheets time: "+(System.currentTimeMillis()-now));
		return ret;
	}

	@Override
	public JSONObject queryRuleLegitimateCi(JSONObject body) {
		logger.info("queryRuleLegitimateCi param: "+body.toString());
		Long now = System.currentTimeMillis();
		JSONObject ret = datasetSvc.queryRuleLegitimateCi(body.getInteger("pageNum"), body.getInteger("pageSize"), body.getLong("dataSetId"), body.getString("name"), body.getString("like"));
		logger.info("queryRuleLegitimateCi time: "+(System.currentTimeMillis()-now));
		return ret;
	}

	@Override
	public FriendInfo queryFriendByStartCiId(JSONObject body) {
		logger.info("queryFriendByStartCiId param: "+body.toString());
		Long now = System.currentTimeMillis();
		FriendInfo ret = datasetSvc.queryFriendByStartCiId(body.getLong("dataSetId"), body.getLong("startCiId"));
		logger.info("queryFriendByStartCiId time: "+(System.currentTimeMillis()-now));
		return ret;
	}
	@Override
	public Map<Long,Integer> queryRuleNodeCiNum(JSONObject body) {
		logger.info("queryRuleNodeCiNum param: "+body.toString());
		Long now = System.currentTimeMillis();
		Map<Long, Integer> map = datasetSvc.queryRuleNodeCiNum(body.getLong("dataSetId"));
		logger.info("queryRuleNodeCiNum time: "+(System.currentTimeMillis()-now));
		return map;
	}

	@Override
	public RltRuleTableData queryDisassembleFriendInfoDataByPath(JSONObject body) {
		logger.info("queryDisassembleFriendInfoDataByPath param: "+body.toString());
		Long now = System.currentTimeMillis();
		RltRuleTableData ret = datasetSvc.queryDisassembleFriendInfoDataByPath(body.getLong("dataSetId"), body.getLong("startCiId"));
		logger.info("queryDisassembleFriendInfoDataByPath time: "+(System.currentTimeMillis()-now));
		return ret;
	}

	@Override
	public FriendInfo queryFriendByStartCiIdAndTargetClass(JSONObject body) {
		logger.info("queryFriendByStartCiIdAndTargetClass param: "+body.toString());
		Long now = System.currentTimeMillis();
		FriendInfo ret = datasetSvc.queryFriendByStartCiIdAndTargetClass(body.getLong("dataSetId"), body.getLong("startCiId"), new HashSet<>(JSONArray.parseArray(body.getString("targetClassIds"), Long.class)));
		logger.info("queryFriendByStartCiIdAndTargetClass time: "+(System.currentTimeMillis()-now));
		return ret;
	}

	@Override
	public JSONObject countStatistics(JSONObject body) {
		logger.info("countStatistics param: "+body.toString());
		Long now = System.currentTimeMillis();
		JSONObject ret = datasetSvc.countStatistics(body.getLong("dataSetId"), JSONObject.parseObject(body.getJSONObject("chart").toString(), Chart.class));
		logger.info("countStatistics time: "+(System.currentTimeMillis()-now));
		return ret;
	}

	@Override
	public List<JSONObject> getQueryCondition(JSONObject body) {
		logger.info("getQueryCondition param: "+body.toString());
		Long now = System.currentTimeMillis();
		List<JSONObject> ret = datasetSvc.getQueryCondition(body.getLong("dataSetId"), body.getString("sheetId"));
		logger.info("getQueryCondition time: "+(System.currentTimeMillis()-now));
		return ret;
	}

	@Override
	public DataSetExeResultSheetPage queryDataSetResultBySheet(JSONObject body) {
		logger.info("queryDataSetResultBySheet param: "+body.toString());
		Long now = System.currentTimeMillis();
		DataSetExeResultSheetPage ret = datasetSvc.queryDataSetResultBySheet(body.getLong("domainId"),body.getLong("dataSetId"), body.getString("sheetId"), body.getInteger("pageNum"),
				body.getInteger("pageSize"), body.getString("sortCol"), body.getBoolean("isDesc"), body.getJSONArray("condition"), body.getString("userCode"));
		logger.info("queryDataSetResultBySheet time: "+(System.currentTimeMillis()-now));
		return ret;
	}

	@Override
	public List<DataSetExeResultSheetPage> queryDataSetResultList(JSONObject body) {
		logger.info("queryDataSetResultList param: "+body.toString());
		Long now = System.currentTimeMillis();
		List<DataSetExeResultSheetPage> ret = datasetSvc.queryDataSetResultList(body.getJSONArray("dataSetIds").toJavaList(Long.class), body.getString("sheetId"), body.getString("sortCol"), body.getBoolean("isDesc"));
		logger.info("queryDataSetResultList time: "+(System.currentTimeMillis()-now));
		return ret;
	}
	
	@Override
	public List<DataSetExeResultSheetPage> getResultUsingRule(JSONObject body) {
		logger.info("getResultUsingRule param: "+body.toString());
		Long now = System.currentTimeMillis();
		List<DataSetExeResultSheetPage> ret = datasetSvc.getResultUsingRule(body.getJSONObject("rule"), body.getInteger("limit"));
		logger.info("getResultUsingRule time: "+(System.currentTimeMillis()-now));
		return ret;
	}

	@Override
	public List<DataSetExeResultSheetPage> getResultUsingRuleByDataSetId(Long dataSetId) {
		// 通过id获取数据集
		JSONObject dataSetJson = new JSONObject();
		dataSetJson.put("id", dataSetId);
		JSONObject ruleJson = findDataSetById(dataSetJson);

		// 通过数据集获取数据
		if (ruleJson != null) {
			Integer limit = 200;
			SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
			ruleJson.put("domainId", currentUserInfo.getDomainId());
			return datasetSvc.getResultUsingRule(ruleJson, limit);
		}
		return Collections.EMPTY_LIST;
	}

	@Override
	public Resource downloadSheetData(JSONObject body) {
		logger.info("downloadSheetData param: "+body.toString());
		Long now = System.currentTimeMillis();
		Resource ret = datasetSvc.downloadSheetData(body.getLong("domainId"), body.getLong("dataSetId"), body.getString("sheetId"),
				body.getString("sortCol"), body.getBoolean("isDesc"), body.getJSONArray("condition"));
		logger.info("downloadSheetData time: "+(System.currentTimeMillis()-now));
		return ret;
	}

	@Override
	public Integer isTaskRunning(Long dataSetId) {
		logger.info("isTaskRunning param: "+dataSetId);
		Long now = System.currentTimeMillis();
		int ret = datasetSvc.isTaskRunning(dataSetId);
		logger.info("isTaskRunning time: "+(System.currentTimeMillis()-now));
		return ret;
	}

	@Override
	public String getCode(String jsonStr) throws Exception {
		logger.info("getCode param: "+jsonStr);
		Long now = System.currentTimeMillis();
		String ret = datasetSvc.getCode(jsonStr);
		logger.info("getCode time: "+(System.currentTimeMillis()-now));
		return ret;
	}

	@Override
	public List<Map<String, Object>> groupDataSetMallApiLogCount(Long domainId) {
		logger.info("groupDataSetMallApiLogCount param: ");
		Long now = System.currentTimeMillis();
		List<Map<String, Object>> ret = datasetSvc.groupDataSetMallApiLogCount(domainId);
		logger.info("groupDataSetMallApiLogCount time: "+(System.currentTimeMillis()-now));
		return ret;
	}

    @Override
    public List<String> getTpMetricLabelDTOList(TpRuleReqDTO ruleReqDTO) {
        return datasetSvc.getTpMetricLabelDTOList(ruleReqDTO);
    }

    @Override
    public List<String> queryMetricAttrValue(MetricAttrValQueryDTO query) {
        return datasetSvc.queryMetricAttrValue(query);
    }

	@Override
	public List<DataSetMallApiRelationRule> findAllRelationDateSet(Long domainId) {
		return datasetSvc.findAllRelationDateSet(domainId);
	}

	@Override
	public void updateMallApiExeResult(DataSetMallApiRelationRuleDto dataSetMallApiRelationRuleDto) {
		datasetSvc.updateMallApiExeResult(dataSetMallApiRelationRuleDto.getDataSetRelationRule(), dataSetMallApiRelationRuleDto.getFriendInfoMap());
	}

	@Override
	public void checkOperate(DataSetMallApiDto dataSetMallApiDto) {
		datasetSvc.checkOperate(dataSetMallApiDto.getUserCode(), dataSetMallApiDto.getDataSetMallApi());
	}

	@Override
	public FriendBatchInfo queryFriendByStartCiIds(List<FriendInfoRequestDto> body) {
		return datasetSvc.queryFriendByStartCiIds(body);
	}

	@Override
	public List<DataSetExeResultSheetPage> findPathList(JSONObject body) {
		return datasetSvc.findDataSetRuleList(body.getJSONObject("rule"), body.getInteger("limit"));
	}

	@Override
	public String updateThumbnail(DataSetThumbnailDTO body) {
		return datasetSvc.updateThumbnail(body);
	}

	@Override
	public List<DataSetPathInfoVo> getPathInfo(List<Long> dataSetIds) {
		return datasetSvc.getPathInfo(dataSetIds);
	}

	@Override
	public void updateDataSetById(Long id) {
		datasetSvc.updateDataSetById(id);
	}

	@Override
	public List<DataSetTableResult> queryCiTableList(QueryDataTableDTO body) {
		logger.info("queryCiTableList param: "+body.toString());
		Long now = System.currentTimeMillis();
		List<DataSetTableResult> ret = datasetSvc.queryCiTableList(body);
		logger.info("queryCiTableList time: "+(System.currentTimeMillis()-now));
		return ret;
	}


	@Override
	public ResponseEntity<byte[]> ciTableListExport(List<DataSetTableResult> ret, QueryDataTableDTO body) {
		return datasetSvc.ciTableListExport(ret,body);
	}
}
