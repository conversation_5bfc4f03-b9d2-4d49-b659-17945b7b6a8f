package com.uinnova.product.eam.web.eam.mvc;

import com.alibaba.fastjson.JSONObject;
import com.binary.core.util.BinaryUtils;
import com.binary.framework.util.ControllerUtils;
import com.binary.framework.web.RemoteResult;
import com.uinnova.product.eam.comm.model.CListType;
import com.uinnova.product.eam.comm.model.ListType;
import com.uinnova.product.eam.comm.model.es.CEamListing;
import com.uinnova.product.eam.comm.model.es.EamListing;
import com.uinnova.product.eam.model.DirListConfigDto;
import com.uinnova.product.eam.model.ListCondition;
import com.uinnova.product.eam.service.IEamListingSvc;
import com.uinnova.product.eam.service.IListTypeSvc;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uinnova.product.vmdb.comm.doc.annotation.MvcDesc;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uino.bean.cmdb.business.dataset.DataSetExeResultSheetPage;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/eam/listing")
@MvcDesc(author = "wcl", desc = "清单管理")
public class EamListingMvc {

    @Resource
    private IEamListingSvc listingSvc;

    @Resource
    private IListTypeSvc typeSvc;

    @PostMapping(value = "/saveOrUpdate")
    @ModDesc(desc = "新增/修改清单基本信息", pDesc = "清单基本信息参数", rDesc = "清单id", rType = RemoteResult.class)
    public void saveOrUpdate(HttpServletRequest request, HttpServletResponse response,
                             @RequestBody @Valid CEamListing eamList) {
        Long id = listingSvc.saveOrUpdate(eamList);
        ControllerUtils.returnJson(request, response, id);
    }


    @PostMapping("/queryLists")
    @ModDesc(desc = "清单列表查询（支持发布状态，清单名称模糊搜索）", pDesc = "搜索参数", rDesc = "清单列表", rType = RemoteResult.class)
    public RemoteResult queryLists(@RequestBody ListCondition dto) {
        List<EamListing> eamLists = listingSvc.queryLists(dto);
        if (BinaryUtils.isEmpty(eamLists) || eamLists.size() == 0) {
            return new RemoteResult(new ArrayList<>());
        }
        return new RemoteResult(eamLists);
    }


    @RequestMapping("/queryListById")
    @ModDesc(desc = "根据id查询单个清单信息", pDesc = "清单id", rDesc = "单个清单信息", rType = RemoteResult.class)
    public void queryListById(HttpServletRequest request, HttpServletResponse response, @RequestParam Long id) {
        EamListing eamList = listingSvc.queryListById(id);
        ControllerUtils.returnJson(request, response, eamList);
    }


    @GetMapping(value = "/deleteListing")
    @ModDesc(desc = "逻辑删除清单", pDesc = "清单id", rDesc = "删除响应", rType = RemoteResult.class)
    public void deleteListing(HttpServletRequest request, HttpServletResponse response, @RequestParam Long id) {
        String result = listingSvc.deleteListing(id);
        ControllerUtils.returnJson(request, response, result);
    }


    @GetMapping(value = "/releaseListing")
    @ModDesc(desc = " 清单的发布/取消发布", pDesc = "清单id，发布标识", rDesc = "发布响应", rType = RemoteResult.class)
    public void releaseListing(HttpServletRequest request, HttpServletResponse response, @RequestParam Long id, @RequestParam Integer releaseState) {
        Long listId = listingSvc.releaseListing(id, releaseState);
        ControllerUtils.returnJson(request, response, listId);
    }

    @GetMapping(value = "/dataAssetsList")
    @ModDesc(desc = "数据模型资产清单左侧列表", pDesc = "卡片id", rDesc = "左侧列表数据集合", rType = RemoteResult.class)
    public void dataAssetsList(HttpServletRequest request, HttpServletResponse response,@RequestParam Long cardId) {
        List<DirListConfigDto>  assetsList = listingSvc.dataAssetsList(cardId);
        ControllerUtils.returnJson(request, response, assetsList);
    }

    @PostMapping("/getListingData")
    @ModDesc(desc = "根据资产清单id查询对应数据超市数据列表", pDesc = "资产清单id", rDesc = "视图更新信息", rType = RemoteResult.class)
    public RemoteResult getListingData(@RequestBody JSONObject body) {
        Long id = body.getLong("id");
        BinaryUtils.checkEmpty(id, "资产清单id");
        int pageNum = body.getInteger("pageNum");
        int pageSize = body.getInteger("pageSize");
        String like = body.getString("like");
        DataSetExeResultSheetPage result = listingSvc.getListingData(id, like, pageNum, pageSize);
        return new RemoteResult(result);
    }

    @GetMapping("/getClassByDataSet")
    @ModDesc(desc = "根据数据集id获取对象分类信息", pDesc = "数据集id", rDesc = "对象分类信息", rType = RemoteResult.class)
    public RemoteResult getClassByDataSet(@RequestParam Long id) {
        BinaryUtils.checkEmpty(id, "数据集id");
        List<CcCiClassInfo> result = listingSvc.getClassByDataSet(id);
        return new RemoteResult(result);
    }

    @PostMapping(value = "/saveOrUpdateListType")
    @ModDesc(desc = "保存清单类型接口", pDesc = "清单类型参数", rDesc = "类型id", rType = RemoteResult.class)
    public void saveOrUpdateListType(HttpServletRequest request, HttpServletResponse response,
                             @RequestBody @Valid List<CListType> listTypes) {
        Integer id = typeSvc.saveOrUpdateListType(listTypes);
        ControllerUtils.returnJson(request, response, id);
    }

    @GetMapping(value = "/deleteListingType")
    @ModDesc(desc = "删除清单类型接口", pDesc = "清单类型id", rDesc = "删除响应", rType = RemoteResult.class)
    public void deleteListingType(HttpServletRequest request, HttpServletResponse response, @RequestParam Long id) {
        Integer delId = typeSvc.deleteListingType(id);
        ControllerUtils.returnJson(request, response, delId);
    }

    @RequestMapping(value = "/queryListTypes")
    @ModDesc(desc = "查询清单类型", pDesc = "无", rDesc = "清单类型列表", rType = RemoteResult.class)
    public void queryListTypes(HttpServletRequest request, HttpServletResponse response) {
        List<ListType> collect = typeSvc.queryListTypes();
        ControllerUtils.returnJson(request, response, collect);
    }


}
