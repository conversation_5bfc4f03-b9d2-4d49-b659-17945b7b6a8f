# EAM企业架构管理系统 - 项目描述

## 1. 项目概述

EAM（Enterprise Architecture Management）企业架构管理系统是一个基于Spring Boot 3.x微服务架构的现代化企业级应用平台，集成了AI智能分析、专题分析、数据建模、工作流管理等先进功能，为企业提供全方位的架构管理解决方案。

### 1.1 项目背景
- **项目名称**: EAM企业架构管理系统
- **开发组织**: Uinnova（优锘科技）
- **项目版本**: fuxi-1.0.0-SNAPSHOT
- **开发语言**: Java 17
- **架构模式**: 微服务架构 + AI智能化

### 1.2 核心价值
- 提供企业架构的统一管理和可视化展示
- 支持AI驱动的智能架构分析和建议
- 集成专题分析功能，支持矩阵分析和图谱分析
- 提供先进的数据建模工具，支持概念、逻辑、物理模型管理
- 集成工作流引擎，支持架构变更审批流程
- 支持多库管理（设计库、基线库、私有库）

## 2. 技术架构

### 2.1 整体架构
```
┌─────────────────────────────────────────────────────────────┐
│                        前端展示层                              │
├─────────────────────────────────────────────────────────────┤
│                     API网关 + 负载均衡                         │
├─────────────────────────────────────────────────────────────┤
│  EAM-Web    │  EAM-Project  │  EAM-Workable  │  Base-Web    │
├─────────────────────────────────────────────────────────────┤
│              微服务层 (Feign + Spring Cloud)                  │
├─────────────────────────────────────────────────────────────┤
│  AI服务层   │  专题分析层   │  数据建模层   │  工作流层      │
├─────────────────────────────────────────────────────────────┤
│                      业务服务层                               │
├─────────────────────────────────────────────────────────────┤
│                      数据访问层                               │
├─────────────────────────────────────────────────────────────┤
│    MySQL     │  Elasticsearch  │   Redis    │   Nacos      │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 技术栈

#### 2.2.1 后端技术栈
- **框架**: Spring Boot 3.4.5
- **微服务**: Spring Cloud 2024.0.1
- **服务调用**: OpenFeign 4.2.1
- **服务注册**: Nacos 2023.0.3.2
- **工作流引擎**: Flowable
- **数据库**: MySQL 8.0.33
- **搜索引擎**: Elasticsearch 7.17.14/7.17.28
- **缓存**: Redis
- **安全框架**: Spring Security + OAuth2
- **ORM框架**: MyBatis 3.5.13
- **连接池**: Druid 1.2.8
- **AI集成**: Dify工作流API

#### 2.2.2 开发工具
- **构建工具**: Maven 3.x
- **代码质量**: Checkstyle
- **测试框架**: JUnit
- **日志框架**: Log4j2 2.24.0
- **容器化**: Docker
- **API文档**: Swagger/Knife4j
- **网络通信**: Netty 4.1.121.Final

## 3. 项目结构

### 3.1 主要模块

#### 3.1.1 EAM应用模块 (eam-app)
```
eam-app/
├── eam-api/                    # API接口定义模块
├── eam-base/                   # 基础组件和配置模块
├── eam-db/                     # 数据库操作模块
├── eam-feign-client/           # Feign客户端模块
├── eam-feign-server/           # Feign服务端模块
├── eam-model/                  # 数据模型和DTO模块
├── eam-service/                # 业务服务模块
│   ├── ai/                     # AI智能分析服务
│   ├── dm/                     # 数据建模服务
│   └── cj/                     # 仓颉项目服务
├── eam-web/                    # Web控制器模块
│   ├── ai/api/                 # AI接口控制器
│   ├── asset/                  # 架构资产控制器
│   ├── dm/                     # 数据建模控制器
│   └── eam/mvc/                # EAM核心控制器
├── eam-workable/               # 工作流模块
└── eam-workable-feign-client/  # 工作流Feign客户端
```

#### 3.1.2 微服务基础框架 (uino-micro-base-ea)
```
uino-micro-base-ea/
├── uino-micro-api/             # 外部调用API
├── uino-micro-bean/            # Bean定义和实体类
├── uino-micro-comm/            # 通用组件
├── uino-micro-dao/             # 数据访问层
├── uino-micro-ed/              # 扩展组件
├── uino-micro-feign-client/    # Feign客户端
├── uino-micro-feign-server/    # Feign服务端
├── uino-micro-monitor/         # 监控组件
├── uino-micro-plugin/          # 插件组件
├── uino-micro-service/         # 服务实现
├── uino-micro-util/            # 工具类
└── uino-micro-web/             # Web服务
```

## 4. 核心功能

### 4.1 AI智能分析
- **AI绘图**: 基于Dify工作流的智能图表生成
- **数据分析**: 自动化数据分析和可视化
- **架构建议**: AI驱动的架构优化建议
- **智能问答**: 基于企业架构知识的智能问答系统

### 4.2 专题分析
- **矩阵分析**: 支持多维度矩阵分析和可视化
- **关系图谱**: 应用系统关系图谱分析
- **接口服务分析**: 系统间接口服务依赖分析
- **数据导出**: 支持Excel格式的分析结果导出

### 4.3 数据建模
- **概念建模**: 概念实体和属性定义
- **逻辑建模**: 逻辑实体关系建模
- **物理建模**: 物理数据模型和DDL生成
- **数据标准**: 统一的数据标准和规范管理
- **模型版本**: 支持模型版本控制和变更追踪

### 4.4 企业架构管理
- **业务架构**: 业务能力建模、业务流程管理
- **应用架构**: 应用系统管理、应用关系建模
- **数据架构**: 数据模型管理、数据血缘分析
- **技术架构**: 技术组件管理、技术标准管理
- **架构评估**: 架构成熟度评估和优化建议

### 4.5 工作流管理
- **流程定义**: 支持BPMN 2.0标准的流程建模
- **流程部署**: 动态流程部署和版本管理
- **任务管理**: 任务分配、执行、监控
- **流程监控**: 流程实例监控和性能分析
- **审批流程**: 架构变更审批和发布管理

### 4.6 架构资产管理
- **多库管理**: 支持设计库、基线库、私有库
- **资产目录**: 统一的架构资产目录管理
- **版本控制**: 支持资产版本管理和变更追踪
- **审批流程**: 集成工作流的资产变更审批
- **发布管理**: 支持资产的发布和生命周期管理

### 4.7 权限管理
- **用户管理**: 用户账号、角色、组织管理
- **数据权限**: 细粒度的数据权限控制
- **OAuth2认证**: 支持OAuth2标准的认证授权
- **单点登录**: 支持企业级单点登录集成
- **动作通知**: 支持用户操作的动态代理通知

## 5. 部署架构

### 5.1 部署模式
- **单体部署**: 适用于开发和测试环境
- **微服务部署**: 适用于生产环境，支持水平扩展
- **容器化部署**: 基于Docker的容器化部署
- **云原生部署**: 支持Kubernetes编排

### 5.2 环境要求

#### 5.2.1 硬件要求
- **CPU**: 最低4核，推荐8核以上
- **内存**: 最低16GB，推荐32GB以上（支持AI功能）
- **存储**: 最低200GB，推荐SSD存储
- **网络**: 千兆网络

#### 5.2.2 软件要求
- **操作系统**: Linux (CentOS 7+/Ubuntu 18+) 或 Windows Server 2019+
- **Java**: JDK 17+
- **数据库**: MySQL 8.0+
- **搜索引擎**: Elasticsearch 7.17+
- **缓存**: Redis 6.0+
- **服务注册**: Nacos 2.x
- **容器**: Docker 20.0+ (可选)

## 6. 快速开始

### 6.1 环境准备
1. 安装JDK 17
2. 安装Maven 3.8+
3. 安装MySQL 8.0
4. 安装Elasticsearch 7.17
5. 安装Redis 6.0
6. 安装Nacos 2.x

### 6.2 数据库初始化
```sql
-- 创建数据库
CREATE DATABASE eam_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE eam_flowable CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 导入初始化脚本
source /path/to/init.sql;
```

### 6.3 配置文件
```yaml
# application.yml
spring:
  datasource:
    url: **********************************
    username: your_username
    password: your_password
  cloud:
    nacos:
      server-addr: localhost:8848
      discovery:
        group: tarsier-eam
```

### 6.4 启动服务
```bash
# 编译项目
mvn clean install

# 启动基础服务
java -jar uino-micro-base-ea/uino-micro-web/target/uino-micro-web.jar

# 启动EAM主服务
java -jar eam-app/eam-web/target/eam-web.jar

# 启动工作流服务
java -jar eam-app/eam-workable/target/eam-workable.jar
```

## 7. 配置说明

### 7.1 数据源配置
- 支持多数据源配置
- 支持读写分离
- 支持数据库连接池配置
- 支持Elasticsearch集群配置

### 7.2 AI服务配置
- Dify工作流API配置
- AI模型调用超时配置
- AI结果缓存配置
- AI安全策略配置

### 7.3 安全配置
- OAuth2客户端配置
- JWT Token配置
- 权限拦截器配置
- 数据权限配置

## 8. 监控与运维

### 8.1 健康检查
- Spring Boot Actuator健康检查
- 数据库连接状态监控
- Elasticsearch集群状态监控
- AI服务可用性监控

### 8.2 日志管理
- 结构化日志输出
- 日志级别动态调整
- 日志文件轮转配置
- AI调用链路日志

### 8.3 性能监控
- JVM性能监控
- 接口响应时间监控
- 数据库性能监控
- AI服务调用性能监控

## 9. 扩展开发

### 9.1 插件机制
- 支持业务插件扩展
- 支持自定义数据源插件
- 支持自定义认证插件
- 支持AI模型插件扩展

### 9.2 API扩展
- RESTful API标准
- 支持自定义业务API
- 支持第三方系统集成
- AI API标准化接口

## 10. 版本历史

- **v1.0.0-SNAPSHOT**: 初始版本，包含核心功能模块和AI智能分析
- 更多版本信息请查看 [CHANGELOG.md](eam-app/CHANGELOG.md)

## 11. 技术支持

- **项目地址**: https://git.uinnova.com/eam/eam.git
- **文档地址**: 内部文档系统
- **技术支持**: 联系项目技术负责人

---

**注意**: 本文档会随着项目的发展持续更新，请关注最新版本。特别是AI功能模块正在快速迭代中。
