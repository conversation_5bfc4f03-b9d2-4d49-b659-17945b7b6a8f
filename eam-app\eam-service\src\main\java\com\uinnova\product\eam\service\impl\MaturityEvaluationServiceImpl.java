package com.uinnova.product.eam.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.binary.core.exception.MessageException;
import com.binary.core.util.BinaryUtils;
import com.uinnova.product.eam.comm.model.es.AppSquareConfig;
import com.uinnova.product.eam.service.ICISwitchSvc;
import com.uinnova.product.eam.service.IEamCIClassApiSvc;
import com.uinnova.product.eam.service.MaturityEvaluationService;
import com.uinnova.product.eam.service.es.AppSquareConfigDao;
import com.uinnova.product.eam.service.es.IamsESCIDesignSvc;
import com.uinnova.product.eam.service.es.IamsESCmdbCommDesignSvc;
import com.uinnova.product.vmdb.comm.model.ci.CCcCi;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uino.bean.cmdb.base.ESCIAttrDefInfo;
import com.uino.bean.cmdb.base.ESCIClassInfo;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.base.LibType;
import com.uino.bean.permission.base.SysUser;
import com.uino.dao.util.ESUtil;
import com.uino.service.cmdb.microservice.ICIClassSvc;
import com.uino.util.sys.SysUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
@Slf4j
@Service
public class MaturityEvaluationServiceImpl implements MaturityEvaluationService {


    @Resource
    private ICISwitchSvc iciSwitchSvc;

    @Resource
    private ICIClassSvc iciClassSvc;

    @Autowired
    private IEamCIClassApiSvc classApiSvc;

    @Autowired
    @Lazy
    IamsESCmdbCommDesignSvc commSvc;

    @Autowired
    private AppSquareConfigDao basicOperationSettingDao;

    @Autowired
    private IamsESCIDesignSvc ciDesignSvc;

    @Override
    public Long copyData(Long copiedClassId, Long classidAfterReplication) {
        String loginCode = SysUtil.getCurrentUserInfo().getLoginCode();
        // 获取源类和目标类的信息
        List<Long> classIds = Arrays.asList(copiedClassId, classidAfterReplication);
        List<ESCIClassInfo> classInfoList = classApiSvc.selectCiClassByIds(classIds);
        if (CollectionUtils.isEmpty(classInfoList)) {
            throw MessageException.i18n("分类id有误");
        }
        // 分别获取源类和目标类的信息
        Map<Long, ESCIClassInfo> classInfoMap = classInfoList.stream()
                .collect(Collectors.toMap(ESCIClassInfo::getId, info -> info));
        ESCIClassInfo sourceClass = classInfoMap.get(copiedClassId);
        ESCIClassInfo targetClass = classInfoMap.get(classidAfterReplication);
        if (sourceClass == null || targetClass == null) {
            throw MessageException.i18n("分类id有误");
        }
        // 获取目标类的所有属性
        List<ESCIAttrDefInfo> targetAttrs = targetClass.getAttrDefs();

        // 获取需要复制值的属性列表（只读和主键）
        Set<String> copyAttrSet = new HashSet<>();
        Map<String, ESCIAttrDefInfo> targetAttrMap = targetClass.getAttrDefs().stream()
                .collect(Collectors.toMap(ESCIAttrDefInfo::getProName, attr -> attr));

        for (ESCIAttrDefInfo sourceAttr : targetClass.getAttrDefs()) {
            if (sourceAttr.getGroup() != null && sourceAttr.getGroup().contains("只读")) {
                ESCIAttrDefInfo targetAttr = targetAttrMap.get(sourceAttr.getProName());
                if (targetAttr != null) {
                    copyAttrSet.add(sourceAttr.getProName());
                }
            }
        }

        // 获取源类数据
        List<ESCIInfo> designCiList = iciSwitchSvc.getCiByClassIds(
                Collections.singletonList(copiedClassId), loginCode, LibType.DESIGN);
        if (!CollectionUtils.isEmpty(designCiList)) {
            List<ESCIInfo> copiedCiList = new ArrayList<>();
            for (ESCIInfo designCi : designCiList) {
                ESCIInfo copiedCi = new ESCIInfo();
                // 创建新的属性Map，包含所有目标类属性
                Map<String, Object> newAttrs = new HashMap<>();
                // 为所有目标类属性设置初始值
                for (ESCIAttrDefInfo targetAttr : targetAttrs) {
                    String attrName = targetAttr.getProName();
                    if (copyAttrSet.contains(attrName)) {
                        // 如果是需要复制的属性，复制源数据的值
                        Object value = designCi.getAttrs().get(attrName);
                        newAttrs.put(attrName, value != null ? value : "");
                    } else if(copyAttrSet.contains("创建时间")||copyAttrSet.contains("修改时间")) {
                        String dateTime = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(LocalDateTime.now());
                        newAttrs.put(attrName,dateTime );
                    } else {
                        // 其他属性设置为空字符串
                        newAttrs.put(attrName, "");
                    }
                }
                long uuid = ESUtil.getUUID();
                copiedCi.setCiCode(String.valueOf(uuid));
                copiedCi.setClassId(classidAfterReplication);
                copiedCi.setAttrs(newAttrs);
                copiedCiList.add(copiedCi);
            }

            if (!copiedCiList.isEmpty()) {
                iciSwitchSvc.saveOrUpdateBatchCI(copiedCiList, Collections.singletonList(classidAfterReplication), loginCode, loginCode, LibType.DESIGN);
                return (long) copiedCiList.size();
            }
        }
        return 0L;
    }

    @Override
    public Map<String, Object> queryClassificationFullData(Long id, LibType libType,String sortFieldName,Boolean isAsc) {
        Map<String, Object> result = new HashMap<>();
        AppSquareConfig config = basicOperationSettingDao.getById(id);
        if (config == null) {
            return Collections.emptyMap();
        }
        CcCiClassInfo classInfo = iciClassSvc.getCiClassByClassCode(config.getClassCode());
        if (classInfo == null) {
            return Collections.emptyMap();
        }
        // 检查是否包含级别和得分属性
        boolean hasLevel = false;
        boolean hasScore = false;
        for (CcCiAttrDef attrDef : classInfo.getAttrDefs()) {
            String proName = attrDef.getProName();
            if ("级别".equals(proName)) {
                hasLevel = true;
            } else if ("得分".equals(proName)) {
                hasScore = true;
            }
            if (hasLevel && hasScore) {
                break;
            }
        }
        // 查询CI信息
        CCcCi ci1 = new CCcCi();
        ci1.setClassId(classInfo.getCiClass().getId());
        if (libType.equals(LibType.PRIVATE)) {
            ci1.setOwnerCodeEqual(SysUtil.getCurrentUserInfo().getLoginCode());
        }
        String sort = !BinaryUtils.isEmpty(sortFieldName) ? "attrs."+sortFieldName: "attrs."+config.getSortField();
        Set<Long> classIds = new HashSet<>();
        classIds.add(classInfo.getCiClass().getId());
        sort=ciDesignSvc.transSortField(classIds, sort);
        List<ESCIInfo> esciInfos = iciSwitchSvc.queryESCIInfoList(1L, ci1, sort, isAsc==null?true:isAsc, libType);
        List<CcCiInfo> ciInfoList = commSvc.transEsInfoList(esciInfos, true);
        result.put("ciInfo", ciInfoList);

        // 6. 如果同时具有级别和得分属性，计算统计数据
        if (hasLevel && hasScore && !ciInfoList.isEmpty()) {
            Map<String, Double> levelScoreMap = new HashMap<>();
            // 按级别分组并计算得分总和
            for (CcCiInfo ci : ciInfoList) {
                Map<String, String> attrs = ci.getAttrs();
                String level = attrs.get("级别");
                String scoreStr = attrs.get("得分");
                if (!BinaryUtils.isEmpty(level) && !BinaryUtils.isEmpty(scoreStr)) {
                    try {
                        double score = Double.parseDouble(scoreStr);
                        levelScoreMap.merge(level, score, Double::sum);
                    } catch (NumberFormatException e) {
                        log.warn("得分转换失败: {}", scoreStr);
                    }
                }
            }

            // 对结果进行四舍五入处理
            if (!levelScoreMap.isEmpty()) {
                Map<String, Double> roundedMap = new HashMap<>();
                for (Map.Entry<String, Double> entry : levelScoreMap.entrySet()) {
                    double roundedValue = BigDecimal.valueOf(entry.getValue())
                            .setScale(2, RoundingMode.HALF_UP)
                            .doubleValue();
                    roundedMap.put(entry.getKey(), roundedValue);
                }
                result.put("levelScore", roundedMap);
            }
        }

        return result;
    }

    @Override
    public Long publish(Long id) {
        // 1. 权限和状态检查
        SysUser currentUser = SysUtil.getCurrentUserInfo();
        AppSquareConfig config = basicOperationSettingDao.getById(id);
        if (config == null) {
            throw new MessageException("未找到卡片信息");
        }
        // 检查数据维护状态和配置状态
        if (config.getDataMaintain() != 1 || config.getStatus() != 1) {
            throw new MessageException("无发布权限");
        }
        // 检查角色权限
        List<JSONObject> rolePermission = config.getRolePermission();
        if (!CollectionUtils.isEmpty(rolePermission)) {
            for (JSONObject permission : rolePermission) {
                if (permission.getString("roleId").equals(currentUser.getId())) {
                    List<Integer> operations = (List<Integer>) permission.get("operation");
                    if (operations != null && operations.contains(1)) {
                        throw new MessageException("无发布权限");
                    }
                }
            }
        }
        // 2. 获取分类信息
        CcCiClassInfo classInfo = iciClassSvc.getCiClassByClassCode(config.getClassCode());
        if (classInfo == null) {
            throw new MessageException("分类信息不存在");
        }
        Long classId = classInfo.getCiClass().getId();
        String loginCode = currentUser.getLoginCode();
        // 3. 获取私有库和公共库数据
        List<ESCIInfo> privateData = iciSwitchSvc.getCiByClassIds(Collections.singletonList(classId), loginCode, LibType.PRIVATE);
        if (CollectionUtils.isEmpty(privateData)) {
            return 0L;
        }
        List<ESCIInfo> publicData = iciSwitchSvc.getCiByClassIds(Collections.singletonList(classId), loginCode, LibType.DESIGN);
        // 4. 创建公共库数据的映射
        Map<String, ESCIInfo> publicDataMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(publicData)) {
            for (ESCIInfo ci : publicData) {
                // 使用属性值组合作为键
                String key = ci.getAttrs().entrySet().stream()
                        .filter(e -> e.getValue() != null)
                        .map(e -> e.getKey() + ":" + e.getValue())
                        .sorted()
                        .collect(Collectors.joining("|"));
                publicDataMap.put(key, ci);
            }
        }
        // 5. 准备需要更新的数据
        List<ESCIInfo> toUpdateList = new ArrayList<>();
        for (ESCIInfo privateCi : privateData) {
            String key = privateCi.getAttrs().entrySet().stream()
                    .filter(e -> e.getValue() != null)
                    .map(e -> e.getKey() + ":" + e.getValue())
                    .sorted()
                    .collect(Collectors.joining("|"));

            ESCIInfo publicCi = publicDataMap.get(key);
            if (publicCi == null) {
                // 创建新的公共数据
                ESCIInfo newPublicCi = new ESCIInfo();
                newPublicCi.setClassId(classId);
                newPublicCi.setCiCode(privateCi.getCiCode());
                newPublicCi.setAttrs(new HashMap<>(privateCi.getAttrs()));
                toUpdateList.add(newPublicCi);
            } else {
                // 更新现有公共数据
                publicCi.setAttrs(new HashMap<>(privateCi.getAttrs()));
                toUpdateList.add(publicCi);
            }
        }
        //  批量保存或更新
        if (!toUpdateList.isEmpty()) {
            iciSwitchSvc.saveOrUpdateBatchCI(toUpdateList, Collections.singletonList(classId), loginCode, loginCode, LibType.DESIGN);
        }
        return 1L;
    }
}
