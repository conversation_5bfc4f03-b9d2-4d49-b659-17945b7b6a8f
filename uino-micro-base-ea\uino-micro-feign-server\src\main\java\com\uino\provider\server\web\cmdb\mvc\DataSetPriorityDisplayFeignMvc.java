package com.uino.provider.server.web.cmdb.mvc;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.uino.service.cmdb.dataset.microservice.IDataSetPriorityDisplaySvc;
import com.uino.provider.feign.cmdb.DataSetPriorityDisplayFeign;

/**
 * 
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("feign/cmdb/datasetPriorityDisplay")
public class DataSetPriorityDisplayFeignMvc implements DataSetPriorityDisplayFeign {

	Log logger = LogFactory.getLog(DataSetPriorityDisplayFeignMvc.class);
	
	@Autowired
	private IDataSetPriorityDisplaySvc dataSetPriorityDisplaySvc;

	@Override
	public Boolean priorityDisplay(JSONObject body) {
		logger.info("priorityDisplay param: "+body.toString());
		Long now = System.currentTimeMillis();
		Boolean ret = dataSetPriorityDisplaySvc.priorityDisplay(body.getLong("dataSetId"), body.getInteger("displayType"));
		logger.info("priorityDisplay time: "+(System.currentTimeMillis()-now));
		return ret;
	}

}
