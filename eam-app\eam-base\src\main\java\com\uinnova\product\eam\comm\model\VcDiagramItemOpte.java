package com.uinnova.product.eam.comm.model;




import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;


@Comment("视图图标操作表[VC_DIAGRAM_ITEM_OPTE]")
public class VcDiagramItemOpte implements EntityBean {
	private static final long serialVersionUID = 1L;


	@Comment("ID[ID]")
	private Long id;


	@Comment("操作名称[OP_NAME]")
	private String opName;


	@Comment("操作类型[OP_TYPE]    待定")
	private Integer opType;


	@Comment("所属分类[CLASS_ID]")
	private Long classId;


	@Comment("操作表达式[OP_EXP]")
	private String opExp;


	@Comment("操作描述[OP_DESC]")
	private String opDesc;


	@Comment("所属域[DOMAIN_ID]")
	private Long domainId;


	@Comment("数据状态[DATA_STATUS]    数据状态：1-正常 0-删除")
	private Integer dataStatus;


	@Comment("创建人[CREATOR]")
	private String creator;


	@Comment("修改人[MODIFIER]")
	private String modifier;


	@Comment("创建时间[CREATE_TIME]    yyyyMMddHHmmss")
	private Long createTime;


	@Comment("修改时间[MODIFY_TIME]    yyyyMMddHHmmss")
	private Long modifyTime;




	public Long getId() {
		return this.id;
	}
	public void setId(Long id) {
		this.id = id;
	}


	public String getOpName() {
		return this.opName;
	}
	public void setOpName(String opName) {
		this.opName = opName;
	}


	public Integer getOpType() {
		return this.opType;
	}
	public void setOpType(Integer opType) {
		this.opType = opType;
	}


	public Long getClassId() {
		return this.classId;
	}
	public void setClassId(Long classId) {
		this.classId = classId;
	}


	public String getOpExp() {
		return this.opExp;
	}
	public void setOpExp(String opExp) {
		this.opExp = opExp;
	}


	public String getOpDesc() {
		return this.opDesc;
	}
	public void setOpDesc(String opDesc) {
		this.opDesc = opDesc;
	}


	public Long getDomainId() {
		return this.domainId;
	}
	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}


	public Integer getDataStatus() {
		return this.dataStatus;
	}
	public void setDataStatus(Integer dataStatus) {
		this.dataStatus = dataStatus;
	}


	public String getCreator() {
		return this.creator;
	}
	public void setCreator(String creator) {
		this.creator = creator;
	}


	public String getModifier() {
		return this.modifier;
	}
	public void setModifier(String modifier) {
		this.modifier = modifier;
	}


	public Long getCreateTime() {
		return this.createTime;
	}
	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}


	public Long getModifyTime() {
		return this.modifyTime;
	}
	public void setModifyTime(Long modifyTime) {
		this.modifyTime = modifyTime;
	}


}


