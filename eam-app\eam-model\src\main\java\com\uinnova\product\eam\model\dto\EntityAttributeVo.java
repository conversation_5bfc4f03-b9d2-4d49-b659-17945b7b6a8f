package com.uinnova.product.eam.model.dto;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.comm.diagram.CheckType;
import lombok.Data;

/**
 * 实体及实体属性类
 * <AUTHOR>
 */
@Data
public class EntityAttributeVo {

    @Comment("实体ciCode")
    private String entityCode;

    @Comment("ADD:添加,DELETE:删除")
    private CheckType type;

    @Comment("实体属性ciCode")
    private String attrCode;
}