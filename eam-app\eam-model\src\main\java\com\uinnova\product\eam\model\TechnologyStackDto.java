package com.uinnova.product.eam.model;

import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;

import java.util.List;

/**
 * 存放技术栈的对象信息
 *
 * <AUTHOR>
 * @version 2020/7/15
 */
public class TechnologyStackDto {

    /**
     * 技术栈列表
     */
    List<CcCiInfo> stackList;

    /**
     * 技术组件列表
     */
    List<CcCiInfo> componentList;

    public List<CcCiInfo> getStackList() {
        return stackList;
    }

    public void setStackList(List<CcCiInfo> stackList) {
        this.stackList = stackList;
    }

    public List<CcCiInfo> getComponentList() {
        return componentList;
    }

    public void setComponentList(List<CcCiInfo> componentList) {
        this.componentList = componentList;
    }

}
