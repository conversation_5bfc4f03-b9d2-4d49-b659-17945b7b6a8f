package com.uinnova.product.eam.workable.repository;

import com.uinnova.product.eam.workable.model.Terminate;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface TerminateDiagramOrPlan extends JpaRepository<Terminate, String> {

    List<Terminate> findTerminateByBusinessKeyAndProcessDefinitionKey(String businessKey, String processDefinitionKey);

    @Transactional
    void deleteByBusinessKeyAndProcessDefinitionKey(String businessKey, String processDefinitionKey);
}

