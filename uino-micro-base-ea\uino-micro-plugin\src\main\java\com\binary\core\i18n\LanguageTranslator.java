package com.binary.core.i18n;

import java.util.Map;



/**
 * 语言翻译器
 * <AUTHOR>
 */
public interface LanguageTranslator {
	
	
	
	
	
	
	/**
	 * 获取指定国际化语言
	 * @param languageCode 语言代码
	 * @return
	 */
	public String trans(String languageCode);
	
	
	
	
	/**
	 * 获取指定国际化语言
	 * @param languageCode 语言代码
	 * @param jsonParams 语言中动态参数(JSON格式)
	 * @return
	 */
	public String trans(String languageCode, String jsonParams);
	
	
	
	
	
	/**
	 * 获取指定国际化语言
	 * @param languageCode 语言代码
	 * @param params 语言中动态参数, Bean or Map
	 * @return
	 */
	public String trans(String languageCode, Object params);
	
	
	
		
	
	/**
	 * 获取语言字典,即当前语言环境下所有定义数据
	 * @return
	 */
	public Map<String, String> getLanguageDictionary();
	
	
	
	
	
	/**
	 * 获取指定语言字典,即当前语言环境下所有定义数据
	 * @param language 指定语言
	 * @return
	 */
	public Map<String, String> getLanguageDictionary(Language language);
	
	

}
