package com.uinnova.product.eam.init;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 清理用户上下文,这里提供示例，只在taibao登录方式下使用，其他项目对接时要根据实际情况修改
 * 这里是对taibao之前逻辑进行修改，新的项目可以直接在过滤器或拦截器中获取完用户后直接存到threaLocal中,
 * 将对应的CurrentUserGetter实现类直接从ThreadLocal中获取即可
 * <AUTHOR>
 * @since 2025/3/5 14:50
 */
@Component
@ConditionalOnProperty(prefix = "monet.login", name = "loginMethod", havingValue = "taibao")
public class RedisUserGetterInterceptor implements HandlerInterceptor {

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        UserContext.clear(); // 必须清理！
    }
}
