package com.uino.service.sys.microservice;

import java.util.List;
import java.util.Map;

import com.uino.bean.cmdb.base.ESResource;

/**
 * 资源相关服务
 * 
 * <AUTHOR>
 *
 */
public interface IResourceSvc {

	/**
     * 保存同步资源信息
     * 
     * @see #saveSyncResourceInfo(String, String, boolean, boolean, Integer)
     * @param path
     * @param publicUrl
     * @param unzip
     * @param optionType
     * @return
     */
	public String saveSyncResourceInfo(String path, String publicUrl, boolean unzip, Integer optionType);

	/**
     * 保存同步资源信息
     * 
     * @param path 资源在本地的真实路径eg:/usr/local/data/111.txt
     * @param publicUrl 资源公开可访问地址eg:http://************/dara/111.txt
     * @param unzip 是否解压
     * @param currentDir 是否解压当前目录
     * @param optionType 操作类型0:创建1:删除2:修改
     * @return
     */
	public String saveSyncResourceInfo(String path, String publicUrl, boolean unzip, boolean currentDir,
			Integer optionType);

	/**
	 * 保存同步资源信息
	 * @param pathMap <资源在本地的真实路径,资源公开可访问地址>
	 * @param unzip 是否解压
	 * @param currentDir 是否解压当前目录
	 * @param optionType 操作类型0:创建1:删除2:修改
	 */
	void saveSyncResourceBatch(Map<String, String> pathMap, boolean unzip, boolean currentDir, Integer optionType);

	/**
	 * 批量保存
	 * 
	 * @see #saveSyncResourceInfo(String, String, String, Integer)
	 * @param saveDtos
	 */
	public void saveSyncResourceInfo(List<ESResource> saveDtos);

	/**
	 * 获取待同步资源信息(从当前时间前一天开始获取)
	 * 
	 * @param startDataId
	 *            起点数据id
	 * @return
	 */
	public List<ESResource> getWaitSyncResources(Long createTime);

	/**
	 * 保存文件-没时间做，先放着
	 * 
	 * @param fileBytes
	 *            文件内容字节流
	 * @param path
	 *            文件应写入全路径
	 * @param sourceServer
	 *            源资源所属服务
	 * @return
	 */
	// public String saveFile(byte[] fileBytes, String path, String
	// sourceServer);

	/**
	 * 删除文件，先放着
	 * 
	 * @param path
	 * @param sourceServer
	 * @return
	 */
	// public String delFile(String path, String sourceServer);

}
