package com.uinnova.product.eam.comm.model;




import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;


@Comment("视图目录表[VC_DIAGRAM_DIR]")
public class CVcDiagramDir implements Condition {
	private static final long serialVersionUID = 1L;


	@Comment("ID[ID] operate-Equal[=]")
	private Long id;


	@Comment("ID[ID] operate-In[in]")
	private Long[] ids;


	@Comment("ID[ID] operate-GTEqual[>=]")
	private Long startId;

	@Comment("ID[ID] operate-LTEqual[<=]")
	private Long endId;


	@Comment("目录名称[DIR_NAME] operate-Like[like]")
	private String dirName;


	@Comment("目录名称[DIR_NAME] operate-Equal[=]")
	private String dirNameEqual;


	@Comment("目录名称[DIR_NAME] operate-In[in]")
	private String[] dirNames;


	@Comment("目录类型[DIR_TYPE] operate-Equal[=] ")
	private Integer dirType;


	@Comment("目录类型[DIR_TYPE] operate-In[in]  ")
	private Integer[] dirTypes;


	@Comment("目录类型[DIR_TYPE] operate-GTEqual[>=]  ")
	private Integer startDirType;

	@Comment("目录类型[DIR_TYPE] operate-LTEqual[<=]   ")
	private Integer endDirType;


	@Comment("上级目录ID[PARENT_ID] operate-Equal[=]")
	private Long parentId;


	@Comment("上级目录ID[PARENT_ID] operate-In[in]")
	private Long[] parentIds;


	@Comment("上级目录ID[PARENT_ID] operate-GTEqual[>=]")
	private Long startParentId;

	@Comment("上级目录ID[PARENT_ID] operate-LTEqual[<=]")
	private Long endParentId;


	@Comment("所属用户ID[USER_ID] operate-Equal[=]")
	private Long userId;


	@Comment("所属用户ID[USER_ID] operate-In[in]")
	private Long[] userIds;


	@Comment("所属用户ID[USER_ID] operate-GTEqual[>=]")
	private Long startUserId;

	@Comment("所属用户ID[USER_ID] operate-LTEqual[<=]")
	private Long endUserId;


	@Comment("目录层级级别[DIR_LVL] operate-Equal[=]")
	private Integer dirLvl;


	@Comment("目录层级级别[DIR_LVL] operate-In[in]")
	private Integer[] dirLvls;


	@Comment("目录层级级别[DIR_LVL] operate-GTEqual[>=]")
	private Integer startDirLvl;

	@Comment("目录层级级别[DIR_LVL] operate-LTEqual[<=]")
	private Integer endDirLvl;


	@Comment("目录层级路径[DIR_PATH] operate-Like[like]    目录层级路径:例：#1#2#7#")
	private String dirPath;


	@Comment("显示排序[ORDER_NO] operate-Equal[=]")
	private Integer orderNo;


	@Comment("显示排序[ORDER_NO] operate-In[in]")
	private Integer[] orderNos;


	@Comment("显示排序[ORDER_NO] operate-GTEqual[>=]")
	private Integer startOrderNo;

	@Comment("显示排序[ORDER_NO] operate-LTEqual[<=]")
	private Integer endOrderNo;


	@Comment("是否末级[IS_LEAF] operate-Equal[=]    是否末级:1=是 0=否")
	private Integer isLeaf;


	@Comment("是否末级[IS_LEAF] operate-In[in]    是否末级:1=是 0=否")
	private Integer[] isLeafs;


	@Comment("是否末级[IS_LEAF] operate-GTEqual[>=]    是否末级:1=是 0=否")
	private Integer startIsLeaf;

	@Comment("是否末级[IS_LEAF] operate-LTEqual[<=]    是否末级:1=是 0=否")
	private Integer endIsLeaf;


	@Comment("目录图标[ICON] operate-Like[like]")
	private String icon;


	@Comment("目录描述[DIR_DESC] operate-Like[like]")
	private String dirDesc;


	@Comment("所属域[DOMAIN_ID] operate-Equal[=]")
	private Long domainId;


	@Comment("所属域[DOMAIN_ID] operate-In[in]")
	private Long[] domainIds;


	@Comment("所属域[DOMAIN_ID] operate-GTEqual[>=]")
	private Long startDomainId;

	@Comment("所属域[DOMAIN_ID] operate-LTEqual[<=]")
	private Long endDomainId;


	@Comment("数据状态[DATA_STATUS] operate-Equal[=]    数据状态:0=删除，1=正常")
	private Integer dataStatus;


	@Comment("数据状态[DATA_STATUS] operate-In[in]    数据状态:0=删除，1=正常")
	private Integer[] dataStatuss;


	@Comment("数据状态[DATA_STATUS] operate-GTEqual[>=]    数据状态:0=删除，1=正常")
	private Integer startDataStatus;

	@Comment("数据状态[DATA_STATUS] operate-LTEqual[<=]    数据状态:0=删除，1=正常")
	private Integer endDataStatus;


	@Comment("创建人[CREATOR] operate-Like[like]")
	private String creator;


	@Comment("创建人[CREATOR] operate-Equal[=]")
	private String creatorEqual;


	@Comment("创建人[CREATOR] operate-In[in]")
	private String[] creators;


	@Comment("修改人[MODIFIER] operate-Like[like]")
	private String modifier;


	@Comment("修改人[MODIFIER] operate-Equal[=]")
	private String modifierEqual;


	@Comment("修改人[MODIFIER] operate-In[in]")
	private String[] modifiers;


	@Comment("创建时间[CREATE_TIME] operate-Equal[=]    创建时间:yyyyMMddHHmmss")
	private Long createTime;


	@Comment("创建时间[CREATE_TIME] operate-In[in]    创建时间:yyyyMMddHHmmss")
	private Long[] createTimes;


	@Comment("创建时间[CREATE_TIME] operate-GTEqual[>=]    创建时间:yyyyMMddHHmmss")
	private Long startCreateTime;

	@Comment("创建时间[CREATE_TIME] operate-LTEqual[<=]    创建时间:yyyyMMddHHmmss")
	private Long endCreateTime;


	@Comment("更新时间[MODIFY_TIME] operate-Equal[=]    更新时间:yyyyMMddHHmmss")
	private Long modifyTime;


	@Comment("更新时间[MODIFY_TIME] operate-In[in]    更新时间:yyyyMMddHHmmss")
	private Long[] modifyTimes;


	@Comment("更新时间[MODIFY_TIME] operate-GTEqual[>=]    更新时间:yyyyMMddHHmmss")
	private Long startModifyTime;

	@Comment("更新时间[MODIFY_TIME] operate-LTEqual[<=]    更新时间:yyyyMMddHHmmss")
	private Long endModifyTime;

	@Comment("回收站记录原来的父类Id")
	private Long oldParentId;

	@Comment("主题目录")
	private Long subjectId;

	@Comment("是否初始目录  1=是，0=否")
	private Integer dirInit;

	@Comment("关联es系统id")
	private String esSysId;

	@Comment("系统类型")
	private String sysType;

	@Comment("是否是系统文件夹，0:否，1:是")
	private Integer sysDir;

	public Integer getDirInit() { return dirInit; }
	public void setDirInit(Integer dirInit) { this.dirInit = dirInit; }

	public Long getOldParentId() { return oldParentId; }
	public void setOldParentId(Long oldParentId) { this.oldParentId = oldParentId; }

	public void setSubjectId(Long subjectId){
		this.subjectId=subjectId;
	}
	public Long getSubjectId(){
		return subjectId;
	}

	public Long getId() {
		return this.id;
	}
	public void setId(Long id) {
		this.id = id;
	}


	public Long[] getIds() {
		return this.ids;
	}
	public void setIds(Long[] ids) {
		this.ids = ids;
	}


	public Long getStartId() {
		return this.startId;
	}
	public void setStartId(Long startId) {
		this.startId = startId;
	}


	public Long getEndId() {
		return this.endId;
	}
	public void setEndId(Long endId) {
		this.endId = endId;
	}


	public String getDirName() {
		return this.dirName;
	}
	public void setDirName(String dirName) {
		this.dirName = dirName;
	}


	public String getDirNameEqual() {
		return this.dirNameEqual;
	}
	public void setDirNameEqual(String dirNameEqual) {
		this.dirNameEqual = dirNameEqual;
	}


	public String[] getDirNames() {
		return this.dirNames;
	}
	public void setDirNames(String[] dirNames) {
		this.dirNames = dirNames;
	}


	public Integer getDirType() {
		return this.dirType;
	}
	public void setDirType(Integer dirType) {
		this.dirType = dirType;
	}


	public Integer[] getDirTypes() {
		return this.dirTypes;
	}
	public void setDirTypes(Integer[] dirTypes) {
		this.dirTypes = dirTypes;
	}


	public Integer getStartDirType() {
		return this.startDirType;
	}
	public void setStartDirType(Integer startDirType) {
		this.startDirType = startDirType;
	}


	public Integer getEndDirType() {
		return this.endDirType;
	}
	public void setEndDirType(Integer endDirType) {
		this.endDirType = endDirType;
	}


	public Long getParentId() {
		return this.parentId;
	}
	public void setParentId(Long parentId) {
		this.parentId = parentId;
	}


	public Long[] getParentIds() {
		return this.parentIds;
	}
	public void setParentIds(Long[] parentIds) {
		this.parentIds = parentIds;
	}


	public Long getStartParentId() {
		return this.startParentId;
	}
	public void setStartParentId(Long startParentId) {
		this.startParentId = startParentId;
	}


	public Long getEndParentId() {
		return this.endParentId;
	}
	public void setEndParentId(Long endParentId) {
		this.endParentId = endParentId;
	}


	public Long getUserId() {
		return this.userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}


	public Long[] getUserIds() {
		return this.userIds;
	}
	public void setUserIds(Long[] userIds) {
		this.userIds = userIds;
	}


	public Long getStartUserId() {
		return this.startUserId;
	}
	public void setStartUserId(Long startUserId) {
		this.startUserId = startUserId;
	}


	public Long getEndUserId() {
		return this.endUserId;
	}
	public void setEndUserId(Long endUserId) {
		this.endUserId = endUserId;
	}


	public Integer getDirLvl() {
		return this.dirLvl;
	}
	public void setDirLvl(Integer dirLvl) {
		this.dirLvl = dirLvl;
	}


	public Integer[] getDirLvls() {
		return this.dirLvls;
	}
	public void setDirLvls(Integer[] dirLvls) {
		this.dirLvls = dirLvls;
	}


	public Integer getStartDirLvl() {
		return this.startDirLvl;
	}
	public void setStartDirLvl(Integer startDirLvl) {
		this.startDirLvl = startDirLvl;
	}


	public Integer getEndDirLvl() {
		return this.endDirLvl;
	}
	public void setEndDirLvl(Integer endDirLvl) {
		this.endDirLvl = endDirLvl;
	}


	public String getDirPath() {
		return this.dirPath;
	}
	public void setDirPath(String dirPath) {
		this.dirPath = dirPath;
	}


	public Integer getOrderNo() {
		return this.orderNo;
	}
	public void setOrderNo(Integer orderNo) {
		this.orderNo = orderNo;
	}


	public Integer[] getOrderNos() {
		return this.orderNos;
	}
	public void setOrderNos(Integer[] orderNos) {
		this.orderNos = orderNos;
	}


	public Integer getStartOrderNo() {
		return this.startOrderNo;
	}
	public void setStartOrderNo(Integer startOrderNo) {
		this.startOrderNo = startOrderNo;
	}


	public Integer getEndOrderNo() {
		return this.endOrderNo;
	}
	public void setEndOrderNo(Integer endOrderNo) {
		this.endOrderNo = endOrderNo;
	}


	public Integer getIsLeaf() {
		return this.isLeaf;
	}
	public void setIsLeaf(Integer isLeaf) {
		this.isLeaf = isLeaf;
	}


	public Integer[] getIsLeafs() {
		return this.isLeafs;
	}
	public void setIsLeafs(Integer[] isLeafs) {
		this.isLeafs = isLeafs;
	}


	public Integer getStartIsLeaf() {
		return this.startIsLeaf;
	}
	public void setStartIsLeaf(Integer startIsLeaf) {
		this.startIsLeaf = startIsLeaf;
	}


	public Integer getEndIsLeaf() {
		return this.endIsLeaf;
	}
	public void setEndIsLeaf(Integer endIsLeaf) {
		this.endIsLeaf = endIsLeaf;
	}


	public String getIcon() {
		return this.icon;
	}
	public void setIcon(String icon) {
		this.icon = icon;
	}


	public String getDirDesc() {
		return this.dirDesc;
	}
	public void setDirDesc(String dirDesc) {
		this.dirDesc = dirDesc;
	}


	public Long getDomainId() {
		return this.domainId;
	}
	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}


	public Long[] getDomainIds() {
		return this.domainIds;
	}
	public void setDomainIds(Long[] domainIds) {
		this.domainIds = domainIds;
	}


	public Long getStartDomainId() {
		return this.startDomainId;
	}
	public void setStartDomainId(Long startDomainId) {
		this.startDomainId = startDomainId;
	}


	public Long getEndDomainId() {
		return this.endDomainId;
	}
	public void setEndDomainId(Long endDomainId) {
		this.endDomainId = endDomainId;
	}


	public Integer getDataStatus() {
		return this.dataStatus;
	}
	public void setDataStatus(Integer dataStatus) {
		this.dataStatus = dataStatus;
	}


	public Integer[] getDataStatuss() {
		return this.dataStatuss;
	}
	public void setDataStatuss(Integer[] dataStatuss) {
		this.dataStatuss = dataStatuss;
	}


	public Integer getStartDataStatus() {
		return this.startDataStatus;
	}
	public void setStartDataStatus(Integer startDataStatus) {
		this.startDataStatus = startDataStatus;
	}


	public Integer getEndDataStatus() {
		return this.endDataStatus;
	}
	public void setEndDataStatus(Integer endDataStatus) {
		this.endDataStatus = endDataStatus;
	}


	public String getCreator() {
		return this.creator;
	}
	public void setCreator(String creator) {
		this.creator = creator;
	}


	public String getCreatorEqual() {
		return this.creatorEqual;
	}
	public void setCreatorEqual(String creatorEqual) {
		this.creatorEqual = creatorEqual;
	}


	public String[] getCreators() {
		return this.creators;
	}
	public void setCreators(String[] creators) {
		this.creators = creators;
	}


	public String getModifier() {
		return this.modifier;
	}
	public void setModifier(String modifier) {
		this.modifier = modifier;
	}


	public String getModifierEqual() {
		return this.modifierEqual;
	}
	public void setModifierEqual(String modifierEqual) {
		this.modifierEqual = modifierEqual;
	}


	public String[] getModifiers() {
		return this.modifiers;
	}
	public void setModifiers(String[] modifiers) {
		this.modifiers = modifiers;
	}


	public Long getCreateTime() {
		return this.createTime;
	}
	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}


	public Long[] getCreateTimes() {
		return this.createTimes;
	}
	public void setCreateTimes(Long[] createTimes) {
		this.createTimes = createTimes;
	}


	public Long getStartCreateTime() {
		return this.startCreateTime;
	}
	public void setStartCreateTime(Long startCreateTime) {
		this.startCreateTime = startCreateTime;
	}


	public Long getEndCreateTime() {
		return this.endCreateTime;
	}
	public void setEndCreateTime(Long endCreateTime) {
		this.endCreateTime = endCreateTime;
	}


	public Long getModifyTime() {
		return this.modifyTime;
	}
	public void setModifyTime(Long modifyTime) {
		this.modifyTime = modifyTime;
	}


	public Long[] getModifyTimes() {
		return this.modifyTimes;
	}
	public void setModifyTimes(Long[] modifyTimes) {
		this.modifyTimes = modifyTimes;
	}


	public Long getStartModifyTime() {
		return this.startModifyTime;
	}
	public void setStartModifyTime(Long startModifyTime) {
		this.startModifyTime = startModifyTime;
	}


	public Long getEndModifyTime() {
		return this.endModifyTime;
	}
	public void setEndModifyTime(Long endModifyTime) {
		this.endModifyTime = endModifyTime;
	}

	public String getEsSysId() {
		return esSysId;
	}

	public void setEsSysId(String esSysId) {
		this.esSysId = esSysId;
	}

	public String getSysType() {
		return sysType;
	}

	public void setSysType(String sysType) {
		this.sysType = sysType;
	}

	public void setSysDir(Integer sysDir) {
		this.sysDir = sysDir;
	}
}


