package com.uinnova.product.eam.web.diagram.bean;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.web.integration.bean.VcCiRltQ;
import com.uinnova.product.vmdb.comm.model.rlt.CCcCiRlt;

public class CiRltCdt extends CCcCiRlt {

	private static final long serialVersionUID = 1L;

	/**
	 * 关系的查询深度的条件 
	 * */
	@Comment("关系的查询深度的条件 ")
	private  VcCiRltQ[] ciRltQs;
	
	@Comment("是否是部署实例查询，0=否，1=是，不填为否")
	private Integer isInstance;
	
	@Comment("ciIds")
	private Long[] ciIds;

	@Comment("查询深度")
	private Integer depth;
	
	@Comment("过滤的ci分类ids")
	private Long[] filterCiClassIds;
	
	@Comment("起始ciIds")
	private Long[] sCiIds;
	
	@Comment("结束ciIds")
	private Long[] tCiIds;
	
	public Long[] getFilterCiClassIds() {
		return filterCiClassIds;
	}

	public void setFilterCiClassIds(Long[] filterCiClassIds) {
		this.filterCiClassIds = filterCiClassIds;
	}

	public Long[] getsCiIds() {
		return sCiIds;
	}

	public void setsCiIds(Long[] sCiIds) {
		this.sCiIds = sCiIds;
	}

	public Long[] gettCiIds() {
		return tCiIds;
	}

	public void settCiIds(Long[] tCiIds) {
		this.tCiIds = tCiIds;
	}

	public Integer getDepth() {
		return depth;
	}

	public void setDepth(Integer depth) {
		this.depth = depth;
	}

	public Long[] getCiIds() {
		return ciIds;
	}

	public void setCiIds(Long[] ciIds) {
		this.ciIds = ciIds;
	}

	public Integer getIsInstance() {
		return isInstance;
	}

	public void setIsInstance(Integer isInstance) {
		this.isInstance = isInstance;
	}

	public VcCiRltQ[] getCiRltQs() {
		return ciRltQs;
	}

	public void setCiRltQs(VcCiRltQ[] ciRltQs) {
		this.ciRltQs = ciRltQs;
	}
	
	
}
