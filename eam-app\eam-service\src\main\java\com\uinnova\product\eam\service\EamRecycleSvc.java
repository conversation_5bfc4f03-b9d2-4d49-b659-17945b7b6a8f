package com.uinnova.product.eam.service;

import com.uinnova.product.eam.model.EamCategoryCdt;
import com.uinnova.product.eam.model.diagram.SpaceResourceResultInfo;

/**
 * 回收站相关接口
 * <AUTHOR>
 */
public interface EamRecycleSvc {

    /**
     * 通过回收站目录id获取视图、系统、方案等资源信息
     * @param id 回收站目录id
     * @param like 模糊搜索字段
     * @return 回收站目录下资源信息(文件夹、视图、方案)
     */
    SpaceResourceResultInfo queryResource(Long id, String like);

    /**
     * 回收站还原文件夹
     * @param cdt 文件夹id或视图id或方案id
     * @return 还原结果
     */
    Integer restoreBatch(EamCategoryCdt cdt);

    /**
     * 回收站文件夹清除
     * @param cdt 文件夹id或视图id或方案id
     * @return 清除结果
     */
    Integer deleteBatch(EamCategoryCdt cdt);

    /**
     * 校验还原文件夹是否已删除
     * @param cdt 文件夹id或视图id或方案id
     * @return 校验信息
     */
    String restoreCheck(EamCategoryCdt cdt);
}
