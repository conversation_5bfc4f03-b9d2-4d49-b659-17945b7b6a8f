package com.uinnova.product.vmdb.comm.model.rule;

import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;

/**
 * 
 * <AUTHOR>
 *
 */
@Comment("标签规则项表[CC_CI_TAG_RULE_ITEM]")
public class CcCiTagRuleItem implements EntityBean {
    private static final long serialVersionUID = 1L;

    @Comment("ID[ID]")
    private Long id;

    @Comment("所属定义ID[DEF_ID]")
    private Long defId;

    @Comment("所属规则ID[RULE_ID]")
    private Long ruleId;

    @Comment("规则分类[CLASS_ID]")
    private Long classId;

    @Comment("规则类型[RULE_TYPE]    1=属性 2=朋友圈")
    private Integer ruleType;

    @Comment("关联分类[RLT_CLASS_ID]")
    private Long rltClassId;

    @Comment("规则属性[CLASS_ATTR_ID]")
    private Long classAttrId;

    @Comment("运算符[RULE_OP]")
    private Integer ruleOp;

    @Comment("条件值[RULE_VAL]")
    private String ruleVal;

    @Comment("非运算[IS_NOT]")
    private Integer isNot;

    @Comment("排列顺序[ORDER_NO]")
    private Integer orderNo;

    @Comment("所属域[DOMAIN_ID]")
    private Long domainId;

    @Comment("数据状态[DATA_STATUS]    数据状态：1-正常 0-删除")
    private Integer dataStatus;

    @Comment("创建时间[CREATE_TIME]    yyyyMMddHHmmss")
    private Long createTime;

    @Comment("修改时间[MODIFY_TIME]    yyyyMMddHHmmss")
    private Long modifyTime;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getDefId() {
        return this.defId;
    }

    public void setDefId(Long defId) {
        this.defId = defId;
    }

    public Long getRuleId() {
        return this.ruleId;
    }

    public void setRuleId(Long ruleId) {
        this.ruleId = ruleId;
    }

    public Long getClassId() {
        return this.classId;
    }

    public void setClassId(Long classId) {
        this.classId = classId;
    }

    public Integer getRuleType() {
        return this.ruleType;
    }

    public void setRuleType(Integer ruleType) {
        this.ruleType = ruleType;
    }

    public Long getRltClassId() {
        return this.rltClassId;
    }

    public void setRltClassId(Long rltClassId) {
        this.rltClassId = rltClassId;
    }

    public Long getClassAttrId() {
        return this.classAttrId;
    }

    public void setClassAttrId(Long classAttrId) {
        this.classAttrId = classAttrId;
    }

    public Integer getRuleOp() {
        return this.ruleOp;
    }

    public void setRuleOp(Integer ruleOp) {
        this.ruleOp = ruleOp;
    }

    public String getRuleVal() {
        return this.ruleVal;
    }

    public void setRuleVal(String ruleVal) {
        this.ruleVal = ruleVal;
    }

    public Integer getIsNot() {
        return this.isNot;
    }

    public void setIsNot(Integer isNot) {
        this.isNot = isNot;
    }

    public Integer getOrderNo() {
        return this.orderNo;
    }

    public void setOrderNo(Integer orderNo) {
        this.orderNo = orderNo;
    }

    public Long getDomainId() {
        return this.domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }

    public Integer getDataStatus() {
        return this.dataStatus;
    }

    public void setDataStatus(Integer dataStatus) {
        this.dataStatus = dataStatus;
    }

    public Long getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getModifyTime() {
        return this.modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }

}
