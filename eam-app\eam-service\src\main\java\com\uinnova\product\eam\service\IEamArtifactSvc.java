package com.uinnova.product.eam.service;

import com.binary.jdbc.Page;
import com.uinnova.product.eam.comm.model.es.EamArtifact;
import com.uinnova.product.eam.model.EamArtifactVo;
import com.uinnova.product.eam.model.dto.ElementConditionDto;
import com.uinnova.product.eam.model.vo.CiSimpleInfoVo;
import com.uinnova.product.eam.model.vo.DefaultFileVo;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import org.springframework.http.ResponseEntity;

import java.io.File;
import java.io.FileNotFoundException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date : 2021/11/23 14:42
 * @description : 制品类型业务实现接口
 **/
public interface IEamArtifactSvc {

    /**
     * 新建制品添加或修改
     * @param cdt 添加或者修改的制品类型参数字段值
     * @return 状态值
     */
    Long saveOrUpdate(EamArtifactVo cdt);

    /**
     * 新建制品列表查询
     * @param dto
     * @return 制品类型集合
     */
    Page<EamArtifactVo> queryArtifactList(ElementConditionDto dto);

    /**
     * 获取制品类型字典表Map
      * @return Map<ID,名称>
     */
    Map<String, String> getArtifactTypeDictMap();

    /**
     * 通过制品类型查询制品基本信息
     * @param type 制品类型
     * @return 制品类型集合
     */
    List<EamArtifactVo> queryByType(List<Integer> type);

    /**
     * 查询单个制品类型
     * @param artifactId 制品类型id
     * @return 制品类型信息
     */
    EamArtifactVo queryArtifact(Long artifactId);
    /**
     * 查询制品
     * @param artifactIds 制品类型id集合
     * @return 制品类型信息
     */
    List<EamArtifactVo> queryArtifactByIds(List<Long> artifactIds);

    /**
     * 新建制品中默认图片
     * @return 制品类型的默认图片
     */
    List<DefaultFileVo> defaultImage();

    /**
     * 发布制品类型
     * @param artifactId 制品类型id
     * @param releaseState 发布状态
     * @return 状态值
     */
    Long releaseArtifact(Long artifactId,Integer releaseState);

    /**
     * 删除制品类型
     * @param artifactId 制品类型id
     * @return 状态值
     */
    Long deleteArtifact(Long artifactId);

    /**
     * 根据制品名称获取制品id
     * @param artifactName 制品类型名称
     * @return 状态值
     */
    Long getIdByArtifactName(String artifactName);


    /**
     * 复制制品
     * @param artifactId 制品类型id
     * @return 状态值
     */
    Long  copyArtifact(Long artifactId);

    /**
     * 导出制品
     * @param artifactId 制品类型id
     * @return 制品二进制流的json文件
     */
    ResponseEntity<byte[]> exportArtifact(Long artifactId);

    /**
     * 导入制品
     *
     * @param file 压缩包
     * @return 若不为空，则为异常信息
     */
    Long importArtifact(File file) throws FileNotFoundException;

    /**
     * 制品条件查询
     * @param artifact 制品id
     * @return 制品类型基本信息集合
     */
    List<EamArtifact> queryByConditions(EamArtifact artifact);

    /**
     * 制品取消发布前校验
     * @param artifactId 制品id
     * @return 若不为空，则为校验返回信息
     */
    String checkBeforeReleaseArtifact(Long artifactId);

    /**
     * 根据制品id和数据状态查询制品信息
     * @param artifactIds 多个制品id
     * @param dataStatus 数据状态
     * @return 制品基本信息
     */
    List<EamArtifact> queryArtifactListByIds(List<Long> artifactIds, Integer dataStatus);

    /**
     * 通过id查询
     * @param id 制品id
     * @param dataStatus 数据状态
     * @return 制品信息
     */
    EamArtifact getArtifactId(Long id, Integer dataStatus);

    String crushArtifactColumns();

    String crushArtifactColumnsRelation();

    List<EamArtifactVo> queryArtifactByClass(List<EamArtifactVo> eamArtifactVoList, Map<String, String> param);

    String crushCreator();

    /**
     * 查看单个制品全量信息
     * @param artifactId 制品id
     * @return
     */
    Map<String, Object> queryArtifactInfoById(Long artifactId);

    /**
     * 查询制品中配置的对象分类id
     * @param artifactId 制品id
     * @return 对象分类id
     */
    List<Long> getArtifactClassIds(Long artifactId);

    /**
     * 查询制品示例图
     * @return 示例图信息
     */
    List<DefaultFileVo> getImages();

    /**
     * 根据制品id获取制品分类信息
     * @param artifactId 制品类型id
     * @return 对象分类信息
     */
    List<CiSimpleInfoVo> getArtifactClass(Long artifactId);

    /**
     * 根据制品id和分类id获取制品关系信息
     * @param artifactId 制品类型id
     * @param classId 分类id
     * @return 制品关系信息
     */
    List<CcCiClassInfo> getArtifactRlt(Long artifactId, Long classId);

    /**
     * 根据中心元素及关系分类获取分类信息
     * @param classId 分类id
     * @param artifactId 制品id
     * @param rltIds 关系分类id集合
     * @return 分类信息
     */
    List<CiSimpleInfoVo> getArtifactClassByRlt(Long classId, Long artifactId, List<Long> rltIds);

    /**
     * 根据制品ids查询制品信息
     * @param artifactIds 多个制品id
     * @return 制品基本信息
     */
    List<EamArtifact> queryByIds(List<Long> artifactIds);

    /**
     *  获取制品内定义的classShape信息
     * @param productId
     * @return
     */
    Map<String, String> getArtifactClassShapeById(Long productId);

}
