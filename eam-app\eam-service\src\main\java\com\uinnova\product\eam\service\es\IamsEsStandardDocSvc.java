package com.uinnova.product.eam.service.es;

import com.uinnova.product.eam.comm.bean.StandardDoc;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Repository;

import jakarta.annotation.PostConstruct;

/**
 * 架构决议文档信息
 *
 * <AUTHOR>
 * @version 2020-9-23
 */
@Deprecated
@Repository
public class IamsEsStandardDocSvc extends AbstractESBaseDao<StandardDoc, StandardDoc> {
    @Override
    public String getIndex() {
        return "uino_eam_standard_doc";
    }

    @Override
    public String getType() {
        return "uino_eam_standard_doc";
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }
}
