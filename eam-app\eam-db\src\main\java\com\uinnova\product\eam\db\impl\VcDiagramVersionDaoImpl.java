package com.uinnova.product.eam.db.impl;


import com.binary.core.util.BinaryUtils;
import com.binary.framework.exception.DaoException;
import com.binary.framework.ibatis.IBatisUtils;
import com.uinnova.product.eam.base.diagram.model.CVcDiagramVersion;
import com.uinnova.product.eam.base.diagram.model.VcDiagramVersion;
import com.uinnova.product.eam.db.VcDiagramVersionDao;
import com.uinnova.product.eam.db.support.dao.mybatis.ComMyBatisBinaryDaoImpl;
import com.uino.bean.permission.base.SysUser;
import com.uino.tarsier.tarsiercom.util.IdGenerator;
import com.uino.util.sys.SysUtil;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 视图设计版本表[VC_DIAGRAM_VERSION]数据访问对象实现
 */
public class VcDiagramVersionDaoImpl extends ComMyBatisBinaryDaoImpl<VcDiagramVersion, CVcDiagramVersion> implements VcDiagramVersionDao {

	@Override
	public void updateDiagramVersionDescAndVersionNo(Long domainId, Long id, String versionDesc, String versionNo) {
		
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("id", id);
		map.put("domainId", domainId);
		map.put("versionDesc",versionDesc);
		map.put("versionNo", versionNo);
		getSqlSession().update(getTableName()+".updateDiagramVersionDescAndVersionNo", map);
	}

	@Override
	public long insertAsync(VcDiagramVersion record, SysUser loginUser) {
		IBatisUtils.validateEntityEmpty(record);

		String tableName = getTableName();

		Long id = record.getId();
		if(id == null) {
			id = IdGenerator.createGenerator().getID();
			record.setId(id);
		}

		long time = BinaryUtils.getNumberDateTime();
		String loginCode = loginUser.getLoginCode();
		setDataStatusValue(record, 1, true);
		record.setCreateTime(time);
		record.setModifyTime(time);
		if (BinaryUtils.isEmpty(record.getCreator())) {
			setCreatorValue(record, loginCode);
		}
		setModifierValue(record, loginCode);

		Map<String, Object> map = new HashMap<String, Object>();
		map.put("record", record);

		getSqlSession().insert(tableName+".insert", map);
		return id;
	}

	@Override
	public int updateByIdAsync(VcDiagramVersion record, Long id, String loginCode) {
		record.setModifyTime(BinaryUtils.getNumberDateTime());
		record.setModifier(loginCode);
		Map<String, Object> map = new HashMap<>(16);
		map.put("record", record);
		map.put("id", id);
		return getSqlSession().update(getTableName()+".updateById", map);
	}

	@Override
	public int deleteByIdAsync(Long versionId, String loginCode) {
		try{
			VcDiagramVersion record = new VcDiagramVersion();
			record.setDataStatus(0);
			return updateByIdAsync(record, versionId, loginCode);
		} catch (Exception exp) {
			throw new DaoException(exp.getMessage()+"-"+getClass().getName(),exp);
		}
	}

	@Override
	public long[] insertBatchAsync(List<VcDiagramVersion> diagramVersionList, SysUser sysUser) {
		if(diagramVersionList == null||diagramVersionList.isEmpty()) {
            return new long[0];
        }

		String tableName = getTableName();

		long[] cs = new long[diagramVersionList.size()];
		long time = BinaryUtils.getNumberDateTime();
		if (sysUser == null) {
			sysUser = SysUtil.getCurrentUserInfo();
		}
		String loguser = sysUser.getLoginCode();
		for(int i=0; i<cs.length; i++) {
			VcDiagramVersion record = diagramVersionList.get(i);
			Long id = record.getId();
			if(id == null) {
				id = IdGenerator.createGenerator().getID();
				record.setId(id);
			}

			setDataStatusValue(record, 1, true);
			record.setCreateTime(time);
			record.setModifyTime(time);
			if (StringUtils.isEmpty(record.getCreator())) {
				setCreatorValue(record, loguser);
			}
			setModifierValue(record, loguser);

			cs[i] = id;
		}

		Map<String, Object> map = new HashMap<String, Object>();
		String statement = new StringBuilder(tableName).append(".insert").toString();
		for(int i=0; i<cs.length; i++) {
			VcDiagramVersion record = diagramVersionList.get(i);
			map.put("record", record);
			getSqlSession().insert(statement, map);
		}

		return cs;
	}

	@Override
	public int deleteByIds(CVcDiagramVersion versionCdt) {
		try{
			IBatisUtils.validateConditionEmpty(versionCdt);
			Map<String, Object> map = new HashMap<String, Object>();
			map.put("cdt", versionCdt);
			fillCondition(versionCdt, map);
			return getSqlSession().delete(getTableName()+".deleteByCdt", map);
		} catch (Exception exp) {
			throw new DaoException(exp.getMessage()+"-"+getClass().getName(),exp);
		}
	}
}


