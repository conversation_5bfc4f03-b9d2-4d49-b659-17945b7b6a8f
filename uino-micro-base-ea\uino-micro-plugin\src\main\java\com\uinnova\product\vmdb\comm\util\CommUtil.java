package com.uinnova.product.vmdb.comm.util;

import com.binary.core.bean.BMProxy;
import com.binary.core.exception.MessageException;
import com.binary.core.i18n.LanguageResolver;
import com.binary.core.io.FileSystem;
import com.binary.core.util.BinaryUtils;
import com.binary.framework.Local;
import com.binary.framework.exception.ServiceException;
import com.binary.jdbc.Page;
import com.binary.json.JSON;
import com.google.common.collect.Lists;
import com.uinnova.product.vmdb.comm.i18n.MessageUtil;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.FileSystems;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 
 * <AUTHOR>
 *
 */
public abstract class CommUtil {

	private static Logger logger = LoggerFactory.getLogger(CommUtil.class);

	/**
	 * 用于CI index中拼接index内容
	 */
	public static final String INDEX_SPLIT = "&&&";

	private static final int INDEX_MAX_SIZE = 2000;
	/**
	 * 导出的时候带的时间戳
	 */
	public static final SimpleDateFormat EXPORT_TIME_FORMAT = new SimpleDateFormat("yyyyMMdd-HHmmss");
	public static final Pattern INTEGER_REGEX = Pattern.compile("[0-9]{1,16}");
	public static final Pattern DOUBLE_REGEX = Pattern.compile("[0-9]{1,16}(([.][0-9]+)?)");
	public static final Pattern STRING_REGEX = Pattern.compile("\'");
	public static final Pattern DATE_REGEX = Pattern
			.compile("[0-9]{4}(\\-|\\/|\\.)[0-9]{1,2}(\\-|\\/|\\.)[0-9]{1,2}( [0-9]{1,2}:[0-9]{1,2}:[0-9]{1,2})?");

	/**
	 * excel2003扩展名.xls
	 */
	public static final String EXCEL03_XLS_EXTENSION = ".xls";
	/**
	 * excel2007扩展名.xlsx
	 */
	public static final String EXCEL07_XLSX_EXTENSION = ".xlsx";
	/**
	 * CSV文件扩展名.csv
	 */
	public static final String CSV_EXTENSION = ".csv";

	/**
	 * 限制文件上传最大大小
	 */
	public static final Long MAX_EXCEL_SIZE = 262144000L;

	/**
	 * Excel导出的单文件最大数据量
	 */
	public static final Long EXCEL_MAX_DATA_COUNT = 300000L;

	/**
	 * 每个属性类型的一张表中属性数量
	 */
	public static final Integer[] TABLE_ATTR_COUNT = { 0, 60, 60, 35, 7, 10 };

	/**
	 * 各类型Attr所对应的最大定义数量（0弃用，1到5同数据库）
	 */
	private static Integer[] ATTR_MAX_COUNT = { 0, 240, 240, 140, 21, 40 };

	/**
	 * 将CI属性名标准化
	 * 
	 * @param attrName
	 * @return
	 */
	public static String toStdCiAttrName(String attrName) {
		if (attrName == null) {
			return null;
		}
		return attrName.trim().toUpperCase();
	}

	@SuppressWarnings("unchecked")
	public static <T> ArrayList<T> asList(T... a) {
		if (a == null) {
			return null;
		}
		return new ArrayList<T>(Arrays.asList(a));
	}

	/**
	 * 将集合转化为数组
	 * 
	 * @param data
	 * @param a
	 * @return 注意当集合为null或size为0时返回的是null
	 */
	public static <T> T[] toArray(Collection<?> data, T[] a) {
		if (data == null || data.size() < 1) {
			return null;
		}
		return data.toArray(a);
	}

	/**
	 * 创建索引的通用方法
	 * 
	 * @param codeStd 分类定义的Code的标准名(大写属性名)
	 * @param descStd 分类定义的desc的标准名(大写属性名)
	 * @param attrs   ci的全部属性(key应该是大写的属性名)
	 * @return 拼接好的字符串
	 */
	public static String buildCiIndex(String codeStd, String descStd, Map<String, String> attrs) {
		int sLen = CommUtil.INDEX_SPLIT.length();

		if (BinaryUtils.isEmpty(attrs)) {
			return null;
		}

		StringBuffer str = new StringBuffer();

		if (codeStd != null) {
			String codeVal = attrs.remove(codeStd);
			str.append(codeVal).append(CommUtil.INDEX_SPLIT);
		}

		if (descStd != null) {
			String descVal = attrs.remove(descStd);
			str.append(descVal).append(CommUtil.INDEX_SPLIT);
		}

		if (attrs.size() > 0) {
			// 剩余空间
			int sSize = INDEX_MAX_SIZE - str.length() - attrs.size() * sLen;

			int pSize = sSize / attrs.size();

			Set<String> keySet = attrs.keySet();
			int curMapsize = keySet.size();

			for (String string : keySet) {
				String val = attrs.get(string);
				curMapsize--;
				if (!BinaryUtils.isEmpty(val)) {
					val = val.toUpperCase();

					if (val.length() > pSize) {
						val = val.substring(0, pSize - sLen);
					} else {
						sSize = sSize - val.length() - sLen;
						if (curMapsize == 0) {
							pSize = sSize;
						} else {
							pSize = sSize / curMapsize;
						}
					}
					str.append(val).append(CommUtil.INDEX_SPLIT);
				}
			}
		}

		return str.toString().toUpperCase();
	}

	/**
	 * 将文件上传到服务器(上传完成后会关闭流)
	 * 
	 * @param is
	 * @return
	 */
	public static File uploadTmp(InputStream is, String fileName) {
		File dir = new File(Local.getTmpSpace(), BinaryUtils.getUUID());
		File childDir = new File(dir, fileName);
		try {
			// 将文件保存到childDir目录下
			if (!childDir.exists()) {
				childDir.getParentFile().mkdirs();
			}
			// 将上传的文件信息保存到相应的文件目录里
			FileSystem.copy(is, childDir);

		} catch (Exception e) {
			throw BinaryUtils.transException(e, ServiceException.class);
		} finally {
			if (is != null) {
				try {
					is.close();
				} catch (IOException e) {
					throw BinaryUtils.transException(e, ServiceException.class);
				}
			}
		}
		return childDir;

	}

	/**
	 * 获取导入的文件类型
	 *
	 * @return 文件类型[.后缀名]
	 */
	public static String getImportExcelType(String fileName) {
		String fileType = null;
		if (BinaryUtils.isEmpty(fileName)) {
			return fileType;
		}
		fileName = fileName.toLowerCase();
		if (fileName.endsWith(EXCEL03_XLS_EXTENSION)) {
			fileType = EXCEL03_XLS_EXTENSION;
		}
		if (fileName.endsWith(EXCEL07_XLSX_EXTENSION)) {
			fileType = EXCEL07_XLSX_EXTENSION;
		}
		return fileType;
	}

	/**
	 * CI导出数据xls的名字
	 * 
	 * @return
	 */
	public static String getExportCiFileName() {
		return "CI-" + EXPORT_TIME_FORMAT.format(new Date()) + EXCEL03_XLS_EXTENSION;
	}

	/**
	 * CI关系导出数据xls的名字
	 * 
	 * @return
	 */
	public static String getExportCiRltFileName() {
		return "CIRelation-" + EXPORT_TIME_FORMAT.format(new Date()) + EXCEL03_XLS_EXTENSION;
	}

	/**
	 * 导出数据的文件名字
	 * 
	 * @param fileName  文件名称(不传则默认为Export)
	 * @param fileType 文件类型[.后缀名](无默认值)
	 * @return 名字后面带时间戳的文件名
	 */
	public static String getExportFileName(String fileName, String fileType) {
		if (BinaryUtils.isEmpty(fileName)) {
			fileName = "Export";
		}
		fileName = fileName + "-" + EXPORT_TIME_FORMAT.format(new Date());
		if (!BinaryUtils.isEmpty(fileType)) {
			fileName = fileName + fileType.toLowerCase();
		}
		return fileName;
	}

	/**
	 * 复制对象
	 * 
	 * @param obj    待复制对象
	 * @param toType 目标类型
	 * @return
	 */
	public static <T> T copy(Object obj, Class<T> toType) {
		BMProxy<T> proxy = BMProxy.getInstance(toType);
		T t = proxy.newInstance();
		proxy.copyFrom(obj);
		return t;
	}

	/**
	 * 复制列表信息
	 * 
	 * @param datas
	 * @param toType
	 * @return
	 */
	public static <T> List<T> copy(List<?> datas, Class<T> toType) {
		if (datas == null) {
			return null;
		}

		List<T> retData = new ArrayList<T>();
		BMProxy<T> proxy = BMProxy.getInstance(toType);
		for (Object obj : datas) {
			T t = proxy.newInstance();
			proxy.copyFrom(obj);
			retData.add(t);
		}
		return retData;
	}

	/**
	 * 复制分页信息
	 * 
	 * @param pageData
	 * @param toType
	 * @return
	 */
	public static <T> Page<T> copy(Page<?> pageData, Class<T> toType) {
		if (pageData == null) {
			return null;
		}

		Page<T> ret = new Page<T>();
		List<?> data = pageData.getData();

		if (data != null) {
			List<T> retData = new ArrayList<T>();
			BMProxy<T> proxy = BMProxy.getInstance(toType);
			for (Object obj : data) {
				T t = proxy.newInstance();
				proxy.copyFrom(obj);
				retData.add(t);
			}
			ret.setData(retData);
		}

		ret.setPageNum(pageData.getPageNum());
		ret.setPageSize(pageData.getPageSize());
		ret.setTotalPages(pageData.getTotalPages());
		ret.setTotalRows(pageData.getTotalRows());
		return ret;
	}

	/**
	 * 验证字符串是否是整数
	 * 
	 * @param v
	 * @return
	 */
	public static boolean isInteger(String v) {
		return v != null && INTEGER_REGEX.matcher(v).matches();
	}

	/**
	 * 验证字符串是否是小数
	 * 
	 * @param v
	 * @return
	 */
	public static boolean isDouble(String v) {
		return v != null && DOUBLE_REGEX.matcher(v).matches();
	}

	/**
	 * 验证短文本长度(<=200字符)
	 * 
	 * @param v
	 * @return
	 */
	public static boolean isShortVar(String v) {
		return v != null && v.length() <= 200 && !STRING_REGEX.matcher(v).find();
	}

	/**
	 * 验证长文本长度(<=1000字符)
	 * 
	 * @param v
	 * @return
	 */
	public static boolean isLongVar(String v) {
		return v != null && v.length() <= 1000 && !STRING_REGEX.matcher(v).find();
	}

	/**
	 * 验证枚举长度(<=200字符)
	 * 
	 * @param v
	 * @return
	 */
	public static boolean isEnum(String v) {
		return v != null && v.length() <= 200 && !STRING_REGEX.matcher(v).find();
	}

	/**
	 * 验证日期
	 * 
	 * @param v
	 * @return
	 */
	public static boolean isDate(String v) {
		return v != null && DATE_REGEX.matcher(v).matches();
	}

	/**
	 * 验证属性的值是否符合要求
	 * 
	 * @param def
	 * @param val 被验证的值
	 * @return
	 */
	public static boolean validateAttrValType(CcCiAttrDef def, String val) {
		if (def == null) {
			throw new NullPointerException("def is null");
		}

		PropertyType type = PropertyType.valueOf(def.getProType());

		if (BinaryUtils.isEmpty(val)) {
			return def.getIsRequired() != 1;
		}

		switch (type) {
		case INTEGER: {
			return isInteger(val);
		}
		case DOUBLE: {
			return isDouble(val);
		}
		case ENUM: {
			return isEnum(val);
		}
		case DATE: {
			return true;
		}
		case VARCHAR: {
			return isShortVar(val);
		}
		case LONG_VARCHAR: {
			return isLongVar(val);
		}
		case ATTACHMENT:
		case CLOB: {
			return true;
		}
		case DICT: {
			return isShortVar(val);
		}
		case PERSION: {
			return true;
		}
		case ORGANIZATION: {
			return true;
		}
		default: {
			return false;
		}
		}
	}

	/**
	 * 
	 * @param str null时认为合规 a-z A-Z _ ,
	 * @return true 符合规则
	 */
	public static boolean isOrderyBy(String str) {
		if (str == null) {
			return true;
		}
		int strLen = 100;
		if (str.length() > strLen) {
			return false;
		}
		for (int i = 0; i < str.length(); i++) {
			char charAt = str.charAt(i);

			// " "
			// _
			// ,
			// 数字0-9
			// A-Z
			// a-z
			if (charAt != 32 && charAt != 95 && charAt != 44 && !(charAt >= 48 && charAt <= 57)
					&& !(charAt >= 65 && charAt <= 90) && !(charAt >= 97 && charAt <= 122)) {
				return false;
			}
		}
		return true;
	}


	/**
	 * 计算utf-8编码的字符串的字节长度
	 *
	 * @param str
	 * @return
	 */
	public static int countUtf8ByteSize(String str) {
		int byteSize = 0;
		if (BinaryUtils.isEmpty(str)) {
			return byteSize;
		}
		for (int i = 0; i < str.length(); i++) {
			char charCode = str.charAt(i);
			if (0 <= charCode && charCode <= 0x7f) {
				byteSize += 1;
			} else if (128 <= charCode && charCode <= 0x7ff) {
				byteSize += 2;
			} else if (2048 <= charCode && charCode <= 0xffff) {
				byteSize += 3;
			} else if (65536 < charCode && charCode <= 0x1FFFFF) {
				byteSize += 4;
			} else if (0x200000 < charCode && charCode <= 0x3FFFFFF) {
				byteSize += 5;
			} else if (0x4000000 < charCode && charCode <= 0x7FFFFFFF) {
				byteSize += 6;
			}
		}
		return byteSize;
	}

	public static String getAttrTypeName(Integer typeCode) {
		String typeName;
		switch (typeCode) {
		case 1:
			typeName = "int";
			break;
		case 2:
			typeName = "doub";
			break;
		case 3:
		case 6:
		case 7:
		case 8:
			typeName = "short";
			break;
		case 4:
			typeName = "long";
			break;
		case 5:
			typeName = "big";
			break;
		default:
			typeName = null;
			break;
		}
		return typeName;
	}

	public static Integer getAttrMaxCount(Integer index) {
		int cs = 5;
		if (index >= 1 && index <= cs) {
			return ATTR_MAX_COUNT[index];
		} else {
			return -1;
		}
	}

	public static void setAttrMaxCount(Integer index, Integer value) {
		int cs = 5;
		if (index >= 1 && index <= cs && value > TABLE_ATTR_COUNT[index]) {
			ATTR_MAX_COUNT[index] = value;
		}
	}

	/**
	 * 将MAP的KEY转为标准值(大写)
	 * 
	 * @param map 值MAP
	 * @return 标准属性值
	 */
	public static Map<String, String> toStdMap(Map<String, String> map) {
		Map<String, String> ret = new HashMap<String, String>();
		if (map == null) {
			return ret;
		}
		Set<String> keySet = map.keySet();
		for (String key : keySet) {
			if (key != null) {
				ret.put(key.toUpperCase(), map.get(key));
			} else {
				ret.put(null, map.get(key));
			}
		}
		return ret;
	}

	/**
	 * 根据CI属性值和业务主键属性定义获取CI业务主键值的hashCode
	 * 
	 * @param attrs             CI属性值(非空)
	 * @param majorAttrDefNames 业务主键属性定义名称(非空)
	 * @return CI业务主键值的hashCode
	 */
	public static Integer getCiMajorHashCode(Map<String, String> attrs, Set<String> majorAttrDefNames) {
		if (attrs == null || attrs.isEmpty()) {
			MessageUtil.checkEmpty("", "attrs");
		}
		if (majorAttrDefNames == null || majorAttrDefNames.isEmpty()) {
			MessageUtil.checkEmpty("", "attrDefs");
		}
		// 避免属性值的key和属性定义存在差异,全部转大写处理
		Map<String, String> stdMap = toStdMap(attrs);
		List<String> values = new ArrayList<String>();
		for (String name : majorAttrDefNames) {
			String value = stdMap.get(name.toUpperCase());
			values.add(value);
		}
		return getCiMajorHashCode(values);
	}

	/**
	 * 根据CI属性值和业务主键属性定义获取CI业务主键值的hashCode
	 * 
	 * @param attrs             CI属性值(非空)
	 * @param majorAttrDefNames 业务主键属性定义名称(非空)
	 * @return CI业务主键值的hashCode
	 */
	public static Integer getCiMajorHashCode(Map<String, String> attrs, List<String> majorAttrDefNames) {
		if (attrs == null || attrs.isEmpty()) {
			MessageUtil.checkEmpty("", "attrs");
		}
		if (majorAttrDefNames == null || majorAttrDefNames.isEmpty()) {
			MessageUtil.checkEmpty("", "attrDefs");
		}
		// 避免属性值的key和属性定义存在差异,全部转大写处理
		Map<String, String> stdMap = toStdMap(attrs);
		List<String> values = new ArrayList<String>();
		for (String name : majorAttrDefNames) {
			String value = stdMap.get(name.toUpperCase());
			values.add(value);
		}
		return getCiMajorHashCode(values);
	}


	/**
	 * @param :
	 * <AUTHOR> wcl
	 * @Date : 2021/12/15 11:20
	 * @description : 获取ci主键的hashCode值
	 * @Return :
	 **/
	public static Integer getCiMajorHashCodeExtra(Map<String, String> attrs, List<String> majorAttrDefNames, String classCode) {
		if (attrs == null || attrs.isEmpty()) {
			MessageUtil.checkEmpty("", "attrs");
		}
		if (majorAttrDefNames == null || majorAttrDefNames.isEmpty()) {
			MessageUtil.checkEmpty("", "attrDefs");
		}
		// 避免属性值的key和属性定义存在差异,全部转大写处理
		Map<String, String> stdMap = toStdMap(attrs);
		List<String> values = new ArrayList<String>();
		for (String name : majorAttrDefNames) {
			String value = stdMap.get(name.toUpperCase());
			values.add(value);
		}
		values.add(classCode);
		return getCiMajorHashCode(values);
	}
	/**
	 * 根据CI属性值和业务主键属性定义获取CI业务主键值的hashCode
	 *
	 * @param attrs             CI属性值(非空)
	 * @param majorAttrDefNames 业务主键属性定义名称(非空)
	 * @param elements         CI分类编码
	 * @return CI业务主键值的hashCode
	 */
	public static Integer getCiMajorHashCode(Map<String, String> attrs, Collection<String> majorAttrDefNames, String... elements) {
		if (attrs == null || attrs.isEmpty()) {
			MessageUtil.checkEmpty("", "attrs");
		}
		if (majorAttrDefNames == null || majorAttrDefNames.isEmpty()) {
			MessageUtil.checkEmpty("", "attrDefs");
		}
		// 避免属性值的key和属性定义存在差异,全部转大写处理
		Map<String, String> stdMap = toStdMap(attrs);
		List<String> values = new ArrayList<>();
		for (String name : majorAttrDefNames) {
			String value = stdMap.get(name.toUpperCase());
			values.add(value);
		}
		if(!BinaryUtils.isEmpty(elements)){
			values.addAll(Lists.newArrayList(elements));
		}
		return getCiMajorHashCode(values);
	}

	/**
	 * 对比两组CI业务主键属性值是否一样
	 * 
	 * @param ciPks1 CI业务主键值集合
	 * @param ciPks2 CI业务主键值集合
	 * @return true:一样; false:不一样
	 * @throws Exception
	 */
	public static Boolean compareToCiPks(List<String> ciPks1, List<String> ciPks2) throws Exception {
		if(!ciPks1.get(0).equals(ciPks2.get(0))){
			return false;
		}
		Map<String, Integer> map1 = pkList2PkMap(ciPks1);
		Map<String, Integer> map2 = pkList2PkMap(ciPks2);
		if (map1.size() == map2.size()) {
			for (String key : map1.keySet()) {
				Integer count2 = map2.get(key);
				if (count2 == null || map1.get(key) != count2) {
					return false;
				}
			}
			return true;
		} else {
			return false;
		}
	}

	/**
	 * 统计CI业务主键值每个值出现的次数
	 * 
	 * @param values CI业务主键值
	 * @return {业务主键值:出现的次数}
	 * @throws Exception
	 */
	public static Map<String, Integer> pkList2PkMap(List<String> values) throws Exception {
		Map<String, Integer> map = new HashMap<String, Integer>();
		for (String value : values) {
			if (value == null || "".equals(value.trim())) {
				MessageUtil.checkEmpty("", "ciPrimaryKey");
			}
			if (map.containsKey(value)) {
				map.put(value, map.get(value) + 1);
			} else {
				map.put(value, 1);
			}
		}
		return map;
	}

	/**
	 * 根据业务主键值生成hashCode值
	 * 
	 * @param values 业务主键值(非空,不要去重)
	 * @return hashCode
	 * @throws Exception
	 */
	public static Integer getCiMajorHashCode(List<String> values) {
		if (values == null || values.isEmpty()) {
			throw MessageException.i18n("BS_MNAME_CANNOTEMPTY_PKVALUE");
		}
		Map<String, String> params = new HashMap<String, String>();
		params.put("primaryKeysJsonStr", JSON.toString(values));

		Integer hash = 0;
		try {
			hash = _getCiMajorHashCodeString(values).hashCode();
		} catch (Exception e) {
			String message = "业务主键" + JSON.toString(values) + "生成hashCode失败！";
			try {
				String i18nMessage = LanguageResolver.trans("BS_MNAME_GENERATECIHASHCODE_FAIL", params);
				if (!i18nMessage.startsWith("BS_")) {
					message = i18nMessage;
				}
			} catch (Exception ee) {
				// 没有国际化的地方可能会获取失败
				ee.printStackTrace();
			}
			throw new MessageException(message, e);
		}
		return hash;
	}
	
	/**
	 * 根据业务主键值生成hashCode的string值
	 * 
	 * @param values 业务主键值(非空,不要去重)
	 * @return hashCode
	 * @throws Exception
	 */
	public static String getCiMajorHashCodeString(List<String> values) {
		if (values == null || values.isEmpty()) {
			throw MessageException.i18n("BS_MNAME_CANNOTEMPTY_PKVALUE");
		}
		Map<String, String> params = new HashMap<String, String>();
		params.put("primaryKeysJsonStr", JSON.toString(values));

		String hash = null;
		try {
			hash = _getCiMajorHashCodeString(values);
		} catch (Exception e) {
			String message = "业务主键" + JSON.toString(values) + "生成hashCode失败！";
			try {
				String i18nMessage = LanguageResolver.trans("BS_MNAME_GENERATECIHASHCODE_FAIL", params);
				if (!i18nMessage.startsWith("BS_")) {
					message = i18nMessage;
				}
			} catch (Exception ee) {
				// 没有国际化的地方可能会获取失败
				ee.printStackTrace();
			}
			throw new MessageException(message, e);
		}
		return hash;
	}
	
	private static String _getCiMajorHashCodeString(List<String> values) throws Exception {
		Map<String, Integer> map = pkList2PkMap(values);
		String[] keys = new String[map.size()];
		int index = 0;
		for (String key : map.keySet()) {
			keys[index] = key;
			index++;
		}
		Arrays.sort(keys);
		StringBuilder sb = new StringBuilder();
		for (String key : keys) {
			sb.append(key + "=" + map.get(key) + " ");
		}
		return sb.toString();
	}

	/**
	 * 根据CI属性值和业务主键属性定义获取CI所有的业务主键值(和业务主键顺序一致)
	 * 
	 * @param attrs             CI属性值(非空)
	 * @param majorAttrDefNames 业务主键属性定义名称(非空)
	 * @return CI业务主键值集合(不去重)
	 */
	public static List<String> getCiPrimaryKeys(String classStdCode, Map<String, String> attrs, Collection<String> majorAttrDefNames) {
		if (attrs == null || attrs.isEmpty()) {
			MessageUtil.checkEmpty("", "attrs");
		}
		if (majorAttrDefNames == null || majorAttrDefNames.isEmpty()) {
			MessageUtil.checkEmpty("", "attrDefs");
		}
		// 避免属性值的key和属性定义存在差异,全部转大写处理
		Map<String, String> stdMap = toStdMap(attrs);

		List<String> ciPrimaryKeys = new ArrayList<>();
		for (String name : majorAttrDefNames) {
			String value = stdMap.get(name.toUpperCase());
			BinaryUtils.checkEmpty(value,name);
			ciPrimaryKeys.add(value);
		}
		ciPrimaryKeys.add(0,classStdCode);
		return ciPrimaryKeys;
	}

	public static String findCopyKey(Map<String, Object> attrs, Collection<String> majorAttrDefNames, List<CcCiAttrDef> attrDefs) {
		if (attrs == null || attrs.isEmpty()) {
			return "";
		}
		if (majorAttrDefNames == null || majorAttrDefNames.isEmpty()) {
			MessageUtil.checkEmpty("", "attrDefs");
		}
		//如果主键中包含编码类型字段, 则只用针对label字段做处理
		Set<String> labelEncodeKeys = new HashSet<>();
		Set<String> labelPkKeys = new HashSet<>();
		Set<String> labelKeys = new HashSet<>();
		for (CcCiAttrDef each : attrDefs) {
			if(each.getIsCiDisp() == 1){
				labelKeys.add(each.getProStdName());
			}
			if(each.getIsCiDisp() == 1 && each.getIsMajor() == 1){
				labelPkKeys.add(each.getProStdName());
			}
			if(each.getIsMajor() == 1 && each.getProType() == PropertyType.ENCODE.getValue()){
				labelEncodeKeys.add(each.getProStdName());
			}
		}
		String changeKey = "";
		String findKey = "";
		for (String key : attrs.keySet()) {
			String keyUpper = key.toUpperCase();
			if(CollectionUtils.isEmpty(labelEncodeKeys)){
				if(labelPkKeys.contains(keyUpper)){
					changeKey = key;
					break;
				}
			}else{
				if(labelKeys.contains(keyUpper)){
					changeKey = key;
					break;
				}
			}
			if(BinaryUtils.isEmpty(changeKey) && BinaryUtils.isEmpty(findKey) && majorAttrDefNames.contains(keyUpper)){
				findKey = labelEncodeKeys.contains(keyUpper)?"":key;
			}
		}
		if(BinaryUtils.isEmpty(changeKey)){
			changeKey = findKey;
		}
		return changeKey;
	}

	public static String tryCopyVal(Map<String, Object> attrs, String replaceValKey, int index){
		String tail = index == 1 ? "": String.valueOf(index);
		String keyVal = attrs.get(replaceValKey).toString();
		keyVal += ("_copy" + tail);
		return keyVal;
	}


	public static void ensurePKAttr(Map<String, Object> attrs, String replaceValKey, String finalVal) {
		if (attrs == null || attrs.isEmpty() || BinaryUtils.isEmpty(replaceValKey)) {
			return;
		}
		attrs.put(replaceValKey, finalVal);
	}

	/**
	 * 根据CI属性值和CI属性定义获取业务主键和label
	 * 
	 * @param attrDefs 属性定义(非空)
	 * @param attrs    CI属性值(非空)
	 * @return Map: {1:业务主键值集合, 2:ciLabel集合}
	 */
	public static Map<Integer, List<String>> getCiPKAndLabels(List<CcCiAttrDef> attrDefs, Map<String, String> attrs) {
		if (attrDefs == null || attrDefs.isEmpty()) {
			MessageUtil.checkEmpty("", "attrDefs");
		}
		if (attrs == null || attrs.isEmpty()) {
			MessageUtil.checkEmpty("", "attrs");
		}
		// 先排序
		Collections.sort(attrDefs, new Comparator<CcCiAttrDef>() {

			@Override
			public int compare(CcCiAttrDef o1, CcCiAttrDef o2) {
				Integer o1No = o1.getOrderNo();
				Integer o2No = o2.getOrderNo();
				if (o1No == null && o2No == null)
					return 0;
				if (o1No == null)
					return 0 - o2No;
				if (o2No == null)
					return o1No - 0;

				return o1No - o2No;
			}
		});

		// 避免属性值的key和属性定义存在差异,全部转大写处理
		Map<String, String> stdMap = toStdMap(attrs);

		List<String> ciPrimaryKeys = new ArrayList<>();
		List<String> ciLabels = new ArrayList<>();
		for (CcCiAttrDef def : attrDefs) {
			String value = stdMap.get(def.getProStdName());
			if (def.getIsMajor().equals(1)) {
				ciPrimaryKeys.add(value);
			}
			if (def.getIsCiDisp().equals(1) && !BinaryUtils.isEmpty(value)) {
				ciLabels.add(value);
			}
		}
		Map<Integer, List<String>> pkAndLabelMap = new HashMap<Integer, List<String>>();
		pkAndLabelMap.put(1, ciPrimaryKeys);
		pkAndLabelMap.put(2, ciLabels);
		return pkAndLabelMap;
	}

	/**
	 * 根据属性定义按照orderNo排序后获取有序的业务主键属性定义名称
	 * 
	 * @param attrDefs 分类属性定义集合(非空)
	 * @return 有序的业务主键属性定义名称集合
	 */
	public static List<String> getCiPKAttrDefNames(List<CcCiAttrDef> attrDefs) {
		if (attrDefs == null || attrDefs.isEmpty()) {
			MessageUtil.checkEmpty("", "attrDefs");
		}
		// 先排序
		Collections.sort(attrDefs, new Comparator<CcCiAttrDef>() {

			@Override
			public int compare(CcCiAttrDef o1, CcCiAttrDef o2) {
				Integer o1No = o1.getOrderNo();
				Integer o2No = o2.getOrderNo();
				if (o1No == null && o2No == null)
					return 0;
				if (o1No == null)
					return 0 - o2No;
				if (o2No == null)
					return o1No - 0;

				return o1No - o2No;
			}
		});
		List<String> majorAttrDefNames = new ArrayList<>();
		for (CcCiAttrDef def : attrDefs) {
			if (def.getIsMajor().equals(1)) {
				majorAttrDefNames.add(def.getProStdName());
			}
		}
		return majorAttrDefNames;
	}

	public static String getCiPrimaryKeySplit(String pk, String split) {
		StringBuilder sb = new StringBuilder();
		try {
			List<String> ls = JSON.toList(pk, String.class);
			for (int i = 0; i < ls.size(); i++) {
				sb.append(ls.get(i));
				if (i != ls.size() - 1) {
					sb.append(split);
				}
			}
		} catch (Exception e) {
			logger.error("error primary key : " + pk);
		}
		return sb.toString();
	}

	/**
	 * 属性值Json转map
	 * @param attrsJson
	 * @return
	 */
	@SuppressWarnings("unchecked")
    public static Map<String, String> attrs2jsonMap(String attrsJson, List<CcCiAttrDef> attrDefs) {

		if (attrDefs != null && attrDefs.size() > 0) {
			Map<String, String> idValMap = new HashMap<String, String>();
			if (!BinaryUtils.isEmpty(attrsJson)) {
				try {
					idValMap = JSON.toObject(attrsJson, Map.class);
				} catch (Exception e) {
					logger.error("Error attrsJson:【" + attrsJson + "】");
				}
			}
			Map<String, String> attrMap = new HashMap<>();
			for (CcCiAttrDef attrDef : attrDefs) {
				Long id = attrDef.getId();
				String stdName = attrDef.getProStdName();
				String val = "";
				if (idValMap != null && idValMap.containsKey(id.toString()) && idValMap.get(id.toString()) != null) {
					val = idValMap.get(id.toString());
				}
				attrMap.put(stdName, val);
			}
			return attrMap;
        }
        else {
        	return new HashMap<String, String>();
		}
	}

	/**
	 * 属性值Map转Json
	 * @param attrMap
	 * @return
	 */
	public static String attrMap2stdJson(Map<String, String> attrMap, List<CcCiAttrDef> attrDefs) {
		if (attrDefs == null || attrDefs.size() == 0) {
			return null;
		}
		else {
			Map<String, String> stdAttrMap = toStdMap(attrMap);
			Map<Long, String> idValMap = new HashMap<>();
			for (CcCiAttrDef attrDef : attrDefs) {
				Long id = attrDef.getId();
				String stdName = attrDef.getProStdName();
				String val = "";
				if (stdAttrMap.containsKey(stdName) && stdAttrMap.get(stdName) != null) {
				    val = stdAttrMap.get(stdName).replace("\n", " ");
				}
				idValMap.put(id, val);
			}
			return JSON.toString(idValMap);
		}
	}

	/**
	 * 对比Model中字段值，字符串“”与null相等
	 * @param a
	 * @param b
	 * @return
	 */
	public static boolean equalsForModel(Object a, Object b) {

		if ((a != null && a.getClass() == String.class && a == "" && b == null)
				|| (a == null && b != null && b.getClass() == String.class && b == "")) {
			return true;
		}
		else {
			return (a == b) || (a != null && a.equals(b));
		}
	}

    /**
     * 拆分数据库查询CDT的IN参数
     * @param inParams
     * @return
     */
	public static<T> List<List<T>> buildCdtIn(List<T> inParams) {
	    if (BinaryUtils.isEmpty(inParams)) {
            ArrayList<List<T>> lists = new ArrayList<List<T>>();
            lists.add(null);
            return lists;
        }
	    int pageSize = 500;
	    int pageNum = inParams.size() / pageSize + 1;
	    if (pageNum == 1) {
	        List<List<T>> ret = new ArrayList<List<T>>();
	        ret.add(inParams);
	        return ret;
        }

        List<List<T>> ret = new ArrayList<List<T>>();
	    for (int i = 0; i < pageNum; i++) {
	        if (i != pageNum - 1) {
	            List<T> target = inParams.subList(i * pageSize, (i + 1) * pageSize);
	            ret.add(target);
            }
            else {
                List<T> target = inParams.subList(i * pageSize, inParams.size());
                if (target.size() > 0) {
                    ret.add(target);
                }
            }
        }
        return ret;
    }

    public static String replaceForKpi(String s) {
		if (!BinaryUtils.isEmpty(s)) {
			return s.replace("~", "~0")
					.replace("_", "~a")
					.replace("%", "~b")
					.replace("\\", "~c");
		}
		else {
			return s;
		}
	}

	public static String reverseReForKpi(String s) {
		if (!BinaryUtils.isEmpty(s)) {
			return s.replace("~a", "_")
					.replace("~b", "%")
					.replace("~c", "\\")
					.replace("~0", "~");
		}
		else {
			return s;
		}
	}

	/**
	 * 保留n位小数(四舍五舍)
	 * @param val 原始值
	 * @param n 小数位数
	 * @return
	 */
	public static Double doubleValue(double val, int n) {
		return ((double) Double.valueOf(val * Math.pow(10, n)).intValue() / Math.pow(10, n));
	}

	/**
	 * 保留两位小数(四舍五舍)
	 * @param val 原始值
	 * @return
	 */
	public static Double doubleValue(double val) {
		return doubleValue(val, 2);
	}


	public static String removeHost(String url) {

		if (!BinaryUtils.isEmpty(url) && url.startsWith("http")) {
			StringBuilder sb = new StringBuilder();
			String[] split = url.split("/");
			for (int i = 3; i < split.length; ++i) {
				sb.append("/").append(split[i]);
			}
			return sb.toString();
		} else {
			return url;
		}
	}
	
    public static File newFile(File file, String name) {
        return FileSystems.getDefault().getPath(file.getPath(), name).toFile();
    }
	
}
