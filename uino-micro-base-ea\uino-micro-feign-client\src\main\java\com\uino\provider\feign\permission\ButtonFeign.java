package com.uino.provider.feign.permission;

import com.uino.bean.permission.base.SysModule;
import com.uino.bean.permission.query.SysModuleCheck;
import com.uino.provider.feign.config.BaseFeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;

@Component
@FeignClient(name = "${feign-server-name:tarsier-platform}", path = "${feign-server-path:tarsier-platform}/feign/button", configuration = {
        BaseFeignConfig.class})
public interface ButtonFeign {

    /**
     * Save button
     *
     * @param sysButton module of button
     * @return {@link SysModule}
     */
    @PostMapping("saveButton")
    SysModule saveButton(@RequestBody(required = false) SysModule sysButton);

    /**
     * delete button
     * 
     * @param buttonId button id
     */
    @PostMapping("deleteButton")
    void deleteButton(@RequestParam(required = false, value = "id") Long buttonId);

    /**
     * save button sort
     * 
     * @param orderDict order dict
     */
    @PostMapping("saveButtonSort")
    void saveButtonSort(@RequestBody(required = false) Map<Long, Integer> orderDict);

    /**
     * check module sign
     *
     * @param sysButton save dto
     * @return {@code true/false}
     */
    @PostMapping("checkModuleSign")
    SysModuleCheck checkModuleSign(SysModule sysButton);
}
