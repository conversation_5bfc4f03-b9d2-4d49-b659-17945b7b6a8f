package com.uinnova.product.eam.base.diagram.model;

import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.util.List;

@Data
public class ESDiagramMoveCdt implements Condition {

	private static final long serialVersionUID = 1L;

	@Comment("视图的新的名字")
	private String newName;
	@Comment("视图的新的dir")
	private Long newDirId;
	@Comment("单个视图的id")
	private String diagramId;
	@Comment("多个视图的ids")
	private List<String> diagramIds;
	@Comment("视图的新的名字")
	private String opType;
	@Comment("视图类型")
	private String viewType;
	@Comment("制品类型分类")
	private Integer dirType;
	@Comment("是否公开[IS_OPEN]    是否公开:1=开放 0=私有")
	private Integer isOpen;
	@Comment("视图发布后新视图id")
	private String releaseDiagramId;
	@Comment("新视图id")
	private Long copyDiagramId;
	@Comment("是否历史版")
	private boolean createHistory;
	@Comment("是否monet本地历史版")
	private boolean createHistoryVersion = true;
	@Comment("视图类型  1: 空白视图 2：制品视图 3：基于模版 4：默认视图")
	private Integer diagramSubType;
	@Comment("发布描述")
	private String releaseDesc;
	@Comment("视图业务主键")
	private String prepareDiagramId;
	@Comment("创建人")
	private String ownerCode;
	@Comment("创建时间")
	private Long createTime;
	@Comment("流程状态")
	private Integer flowStatus;
	@Comment("浏览状态 当传1时为预览模式")
	private Integer browseStatus;
}
