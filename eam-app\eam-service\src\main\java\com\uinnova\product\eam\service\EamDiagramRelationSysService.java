package com.uinnova.product.eam.service;

import com.uinnova.product.eam.base.diagram.model.ESDiagram;
import com.uinnova.product.eam.comm.model.es.EamDiagramRelationSys;
import com.uinnova.product.eam.model.EamDiagramRelationSysCdt;
import com.uino.bean.cmdb.base.LibType;

import java.util.Collection;
import java.util.List;

/**
 * @description: 制品关联系统
 * @author: Lc
 * @create: 2022-01-19 21:01
 */
public interface EamDiagramRelationSysService {
    /**
     * 新增视图关联系统
     *
     * @param eamDiagramRelationSysCdt
     * @return
     */
    boolean saveDiagramRelationSys(EamDiagramRelationSysCdt eamDiagramRelationSysCdt, LibType libType);

    /**
     * 批量新增视图关联系统
     *
     * @param list {@link EamDiagramRelationSysCdt}
     */
    void saveDiagramRelationSys(List<EamDiagramRelationSysCdt> list);

    /**
     * 查询视图关联系统信息
     *
     * @param diagramEnergy
     * @return
     */
    EamDiagramRelationSys getEamDiagramRelationSys(String diagramEnergy);

    /**
     * 批量查询视图关联系统信息
     *
     * @param diagramEnergy 视图加密Id
     * @return {@link EamDiagramRelationSys}
     */
    List<EamDiagramRelationSys> getEamDiagramRelationSys(Collection<String> diagramEnergy);

    /**
     * 批量查询视图关联系统信息
     *
     * @param diagramEnergy 视图加密Id
     * @param delFlag       删除标识
     * @return {@link EamDiagramRelationSys}
     */
    List<EamDiagramRelationSys> getEamDiagramRelationSys(Collection<String> diagramEnergy, Boolean delFlag,LibType libType);

    /**
     * 给{@link ESDiagram}对象的系统相关字段赋值
     *
     * @param esDiagramList {@link ESDiagram}
     */
    void esDiagramSetRelationProperties(List<ESDiagram> esDiagramList);

    /**
     * 视图关联系统列表
     * @param esSysId
     * @return
     */
    List<EamDiagramRelationSys> findDiagramRelationSysList(String esSysId);

    List<EamDiagramRelationSys> findDiagramRelationSysPirvataList(String esSysId);
    List<EamDiagramRelationSys> findFlowDiagramRelationSysList(String esSysId);
    List<EamDiagramRelationSys> findAllDiagramRelationSysPirvataList(String esSysId);

    List<EamDiagramRelationSys> findFlowSystemDiagramRelation(String esSysId,String diagramClassType);

    List<EamDiagramRelationSys> findDiagramRelationSysList(Collection<String> esSysId,String diagramClassType);

    List<EamDiagramRelationSys> findFlowSystemDiagramRelationPrivate(String esSysId,String diagramClassType);

    List<EamDiagramRelationSys> findFlowSystemDiagramRelationPrivateByOwnerCode(String esSysId,String ownerCode,String diagramClassType);


    /**
     * 修改文件夹下视图关联系统信息
     * @param dirId
     */
    List<EamDiagramRelationSys> updateDiagramRelationSys(Long dirId);

    void deleteDiagramRelationSys(String ciCode);

    void deleteDiagramRelationSys(String ciCode,LibType libType);
}
