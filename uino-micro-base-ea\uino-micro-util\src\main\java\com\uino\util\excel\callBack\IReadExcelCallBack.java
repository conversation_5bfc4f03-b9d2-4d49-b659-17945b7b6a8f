package com.uino.util.excel.callBack;

import java.util.List;

/**
 * 读取excel的回调处理
 * 
 * <AUTHOR>
 *
 */
public interface IReadExcelCallBack {

    /**
     * 执行缓存数据（暂不提供异常处理，实现类实现该方法时自己处理）
     * 
     * @param titles
     *            本sheet标题
     * @param rows
     *            本sheet除标题外的有效数据行数据
     * @param sheetName
     *            本sheet名称
     */
    public void execCacheDatas(List<String> titles, List<String[]> rows, String sheetName);
}
