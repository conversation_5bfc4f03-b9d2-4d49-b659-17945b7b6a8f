package com.uinnova.product.eam.web.eam.mvc;

import com.binary.core.io.Resource;
import com.binary.core.io.support.FileResource;
import com.binary.core.util.BinaryUtils;
import com.binary.framework.util.ControllerUtils;
import com.binary.framework.web.RemoteResult;
import com.uinnova.product.eam.comm.bean.Standard;
import com.uinnova.product.eam.comm.bean.StandardDoc;
import com.uinnova.product.eam.model.StandardCdt;
import com.uinnova.product.eam.service.IEamStandardSvc;
import com.uino.util.rsm.RsmUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.File;
import java.util.Map;
import java.util.Set;

/**
 * 标准规范Controller
 * 
 * <AUTHOR>
 * @version 2020-9-23
 */
@RestController
@RequestMapping("/eam/standard")
public class EamStandardMvc {

    @Autowired
    private IEamStandardSvc standardSvc;

    @Autowired
    private RsmUtils rsmUtils;

    /**
     * 新增标准数据
     *
     * @param files           标准文档
     * @param standard        标准数据
     * @return 执行结果
     */
    @PostMapping("/addStandard")
    public RemoteResult addStandard(MultipartFile[] files, Standard standard) {
        //判断入参
        BinaryUtils.checkEmpty(standard.getTitle(), "title");
        return new RemoteResult(standardSvc.addStandard(standard, files));
    }

    /**
     * 根据id查询标准信息
     *
     * @param id 标准id
     * @return 执行结果
     */
    @GetMapping("/getStandardById")
    public RemoteResult getStandardById(Long id) {
        //判断入参
        BinaryUtils.checkEmpty(id, "id");
        return new RemoteResult(standardSvc.getById(id));
    }

    /**
     * 修改标准
     *
     * @param standard 标准数据
     * @return 执行结果
     */
    @PostMapping("/editStandard")
    public RemoteResult editStandard(@RequestBody Standard standard) {
        BinaryUtils.checkEmpty(standard.getId(), "id");
        standardSvc.editStandard(standard);
        return new RemoteResult("success");
    }

    /**
     * 根据标准id上传标准文档
     *
     * @param standardId 标准id
     * @param files        文档
     * @return 执行结果
     */
    @PostMapping("/uploadStandardDoc")
    public RemoteResult uploadStandardDoc(Long standardId, MultipartFile[] files) {
        Set<Map<String, Object>> maps = standardSvc.uploadStandardDoc(standardId, files);
        return new RemoteResult(maps);
    }

    /**
     * 根据标准文档id删除文档
     *
     * @param standardDocIds 标准文档id
     * @return 执行结果
     */
    @PostMapping("/deleteStandardDoc")
    public RemoteResult deleteStandardDoc(Long standardId, Long[] standardDocIds) {
        for (Long standardDocId : standardDocIds) {
            standardSvc.deleteStandardDoc(standardId, standardDocId);
        }
        return new RemoteResult(true);
    }

    /**
     * 删除标准
     *
     * @param ids 标准id
     * @return 执行结果
     */
    @PostMapping("/deleteStandard")
    public RemoteResult deleteStandard(Long[] ids) {
        standardSvc.deleteStandardByIds(ids);
        return new RemoteResult(true);
    }

    /**
     * 查询标准列表
     *
     * @param standardsCdt 标准查询条件
     * @return 标准信息
     */
    @GetMapping("/searchStandards")
    public RemoteResult searchStandards(StandardCdt standardsCdt) {
        return new RemoteResult(standardSvc.searchStandards(standardsCdt));
    }

    /**
     * 下载标准文档
     *
     * @param standardDocId 标准文档id
     * @param request         请求
     * @param response        响应
     */
    @GetMapping("/downloadStandardDoc")
    public void downloadStandardDoc(Long standardDocId,
                                      HttpServletRequest request, HttpServletResponse response) {
        StandardDoc standardDoc = standardSvc.getStandardDocById(standardDocId);
        String realPath = standardDoc.getRealPath();

        rsmUtils.downloadRsmAndUpdateLocalRsm(realPath);

        String[] split = realPath.split("\\.");
        File file = new File(realPath);
        Resource resource = new FileResource(file);
        ControllerUtils.returnResource(request, response, resource, null,
                false, standardDoc.getName() + "." + split[1]);
    }

    /**
     * 判断标准中文件是否存在
     *
     * @param standardId 标准id
     * @param fileName 文件名
     * @return 执行结果
     */
    @GetMapping("/docIsExist")
    public RemoteResult docIsExist(Long standardId, String fileName) {
        BinaryUtils.checkEmpty(standardId, "standardId");
        BinaryUtils.checkEmpty(fileName, "fileName");
        String docName = fileName.substring(0, fileName.lastIndexOf('.'));
        String docType = fileName.substring(fileName.lastIndexOf('.') + 1);
        Standard standard = standardSvc.getById(standardId);
        Set<Map<String, Object>> docs = standard.getDocs();
        for (Map<String, Object> docMap : docs) {
            if (docName.equals(docMap.get("docName"))&&docType.equals(docMap.get("docType"))) {
                return new RemoteResult(true);
            }
        }
        return new RemoteResult(false);
    }
}
