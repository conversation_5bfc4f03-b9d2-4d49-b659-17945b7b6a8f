package com.uinnova.product.vmdb.provider.search.bean;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.comm.model.ci.CcCiClass;
import com.uinnova.product.vmdb.comm.model.ci.CcFixAttrMapping;

import java.io.Serializable;
import java.util.List;

public class CcCiClassObj implements Serializable {
	private static final long serialVersionUID = 1L;
	
	
	
	@Comment("当前CI所属分类")
	private CcCiClass cls;
	
	
	
	@Comment("常柱属性映射对象")
	private CcFixAttrMapping fix;
	
	
	
	@Comment("当前CI属性定义")
	private List<CcCiAttrDef> attrDefs;

	


	public CcCiClass getCls() {
		return cls;
	}



	public void setCls(CcCiClass cls) {
		this.cls = cls;
	}



	public CcFixAttrMapping getFix() {
		return fix;
	}



	public void setFix(CcFixAttrMapping fix) {
		this.fix = fix;
	}



	public List<CcCiAttrDef> getAttrDefs() {
		return attrDefs;
	}



	public void setAttrDefs(List<CcCiAttrDef> attrDefs) {
		this.attrDefs = attrDefs;
	}


	
	


	
	
	

}
