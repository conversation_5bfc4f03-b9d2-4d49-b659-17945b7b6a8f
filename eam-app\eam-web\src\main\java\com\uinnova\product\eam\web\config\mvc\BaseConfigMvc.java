package com.uinnova.product.eam.web.config.mvc;

import com.binary.framework.util.ControllerUtils;
import com.binary.json.JSON;
import com.uinnova.product.eam.comm.model.CVcBaseConfig;
import com.uinnova.product.eam.comm.model.VcBaseConfig;
import com.uinnova.product.eam.service.BaseConfigService;
import com.uinnova.product.eam.web.util.SqlInjectUtil;
import com.uinnova.product.vmdb.comm.bean.QueryListCondition;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uinnova.product.vmdb.comm.doc.annotation.MvcDesc;
import com.uinnova.product.vmdb.comm.util.RestTypeUtil;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.List;


@Controller
@RequestMapping("/config")
@MvcDesc(author = "wangchuanping", desc = "提供配置相关接口")
public class BaseConfigMvc {

	@Resource
	private BaseConfigService baseConfigService;

	@RequestMapping("/queryConfigList")
	@ModDesc(desc = "查询配置列表", pDesc = "查询条件", pType = CVcBaseConfig.class, rDesc = "配置信息列表", rType = List.class,
			rcType = VcBaseConfig.class)
	public void queryConfigList(HttpServletRequest request, HttpServletResponse response, @RequestBody String body) {
		QueryListCondition<CVcBaseConfig> condition = RestTypeUtil.toListCondition(body, CVcBaseConfig.class);
		List<VcBaseConfig> result = baseConfigService.queryBaseConfigList(condition.getCdt());
		ControllerUtils.returnJson(request, response, result);
	}

	@RequestMapping("/saveOrUpdateConfig")
	@ModDesc(desc = "保存或者更新配置", pDesc = "配置信息", pType = VcBaseConfig.class, rDesc = "保存id", rType = Long.class)
	public void saveOrUpdateConfig(HttpServletRequest request, HttpServletResponse response,
								   @RequestBody String body) {
		VcBaseConfig record = JSON.toObject(body, VcBaseConfig.class);
		if (record != null && record.getCfgCode() != null) {
			SqlInjectUtil.hasSpecialStr(record.getCfgCode());
		}

		Long result = baseConfigService.saveOrUpdateBaseConfig(record);
		ControllerUtils.returnJson(request, response, result);
	}
	
	@PostMapping("/saveOrUpdateBaseConfigBatch")
	public void saveOrUpdateBaseConfigBatch(HttpServletRequest request, HttpServletResponse response, @RequestBody List<VcBaseConfig> baseConfigList){
		Long result = baseConfigService.saveOrUpdateBaseConfigBatch(baseConfigList);
		ControllerUtils.returnJson(request, response, result);
	}
	

	
}
