package com.uino.bean.tp.buiness;

import com.alibaba.fastjson.JSONObject;
import com.binary.jdbc.Page;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 返回的性能数据DTO
 * @author: we<PERSON><PERSON><PERSON>
 * @data: 2020/09/14 15:01
 **/
@Data
public class PerfDataRespDTO {
    /**
     * 原始数据
     */
    private Page<JSONObject> originData;

    /**
     * 聚合数据
     */
    private List<PerfDataDTO> aggData;

    @Data
    public static class PerfDataDTO {
        private Long time;
        private BigDecimal avg;
        private BigDecimal max;
        private BigDecimal min;
        private BigDecimal sum;
    }
}
