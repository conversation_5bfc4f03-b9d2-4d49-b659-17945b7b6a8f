package com.uinnova.product.vmdb.comm.util;

import com.binary.core.util.BinaryUtils;
import org.springframework.context.EnvironmentAware;
import org.springframework.core.env.Environment;

/**
 * 可以在SingleApplication中使用,用于加载自定义配置文件
 * <AUTHOR>
 *
 */
public class ApplicationProperties implements EnvironmentAware {

    private static Environment env;

    @Override
    public void setEnvironment(Environment environment) {
        env = environment;
    }

    public void loadProperties(String... profiles) {
        env.acceptsProfiles(profiles);
    }

    public static String getProperty(String key) {
        BinaryUtils.checkEmpty(env, "environment");
        if (!env.containsProperty(key)) {
            return null;
        }
        return env.getProperty(key);
    }

}
