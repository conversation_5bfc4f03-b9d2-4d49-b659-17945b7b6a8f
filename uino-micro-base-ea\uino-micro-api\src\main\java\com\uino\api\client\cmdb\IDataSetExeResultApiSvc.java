package com.uino.api.client.cmdb;

import com.uino.bean.cmdb.base.dataset.batch.DataSetExeResultSheet;

import java.util.List;

/**
 * @Title: IDataSetExeResultApiSvc
 * @Description: IDataSetExeResultApiSvc
 * @Author: YGQ
 * @Create: 2021-05-31 15:55
 **/
public interface IDataSetExeResultApiSvc {

    /**
     * save or update exe result by batch
     *
     * @param dataSetExeResultSheets execute result
     */
    void saveOrUpdateBatch(List<DataSetExeResultSheet> dataSetExeResultSheets);

    /**
     * delete by data set id
     *
     * @param dataSetId id
     */
    void deleteByDataSetId(Long dataSetId);
}
