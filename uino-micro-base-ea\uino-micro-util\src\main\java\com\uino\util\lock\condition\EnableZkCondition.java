package com.uino.util.lock.condition;

import org.springframework.context.annotation.Condition;
import org.springframework.context.annotation.ConditionContext;
import org.springframework.core.type.AnnotatedTypeMetadata;

/**
 * zookeeper condition
 */
public class EnableZkCondition implements Condition {

    @Override
    public boolean matches(ConditionContext context, AnnotatedTypeMetadata metadata) {
        boolean enableZookeeper = false;
        String zookeeperServer = context.getEnvironment().getProperty("zookeeper.server");
        if (zookeeperServer != null) {
            if (!"".equals(zookeeperServer.trim())) {
                enableZookeeper = true;
            }
        }
        return enableZookeeper;
    }
}
