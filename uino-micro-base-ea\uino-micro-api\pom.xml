<?xml version="1.0"?>
<project
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd"
	xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.uino</groupId>
		<artifactId>eam-base</artifactId>
		<version>1.0.0-SNAPSHOT</version>
	</parent>
	<artifactId>uino-eam-micro-api</artifactId>
	<name>uino-eam-micro-api</name>
	<url>http://maven.apache.org</url>
	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
	</properties>
	<dependencies>
		<dependency>
			<groupId>com.uino</groupId>
			<artifactId>uino-eam-micro-feign-client</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>com.uino</groupId>
			<artifactId>uino-eam-micro-service</artifactId>
			<version>${project.version}</version>
		</dependency>
	</dependencies>
</project>
