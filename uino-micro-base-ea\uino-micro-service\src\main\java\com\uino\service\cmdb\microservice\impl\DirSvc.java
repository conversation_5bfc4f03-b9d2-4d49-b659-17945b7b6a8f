package com.uino.service.cmdb.microservice.impl;

import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;

import com.uino.dao.BaseConst;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.sort.SortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import com.binary.core.exception.MessageException;
import com.binary.core.util.BinaryUtils;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClassDir;
import com.uino.dao.cmdb.ESDirSvc;
import com.uino.service.cmdb.microservice.IDirSvc;
import com.uino.bean.cmdb.base.CcCiClassDir;
import com.uino.bean.cmdb.enums.DirTypeEnum;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
public class DirSvc implements IDirSvc {

    @Autowired
    private ESDirSvc esDirSvc;

    @Override
    public Long saveOrUpdate(CcCiClassDir dir) {
        BinaryUtils.checkEmpty(dir, "dir");
        BinaryUtils.checkEmpty(dir.getDirName(), "dirName");
        BinaryUtils.checkEmpty(dir.getCiType(), "ciType");
        BinaryUtils.checkEmpty(dir.getParentId(), "parentId");
        Long domainId = dir.getDomainId() == null ? BaseConst.DEFAULT_DOMAIN_ID : dir.getDomainId();
        dir.setDomainId(domainId);
        // 图标文件夹命名限制
        if (dir.getCiType() == 3 && !dir.getDirName().matches("[A-Za-z0-9 _\\-\\@\\.\\(\\)\\u4e00-\\u9fa5]+")) {
            throw MessageException.i18n("BS_MNAME_IMAGE_DIR_NAME_FORMAT");
        }
        // 领域命名限制
        if (dir.getCiType() == 1) {
            Assert.isTrue(dir.getDirName().trim().length() <= 20, "领域名称不可超过20位");
            Assert.isTrue(dir.getDirName().matches("[A-Za-z0-9 _\\-\\@\\.\\(\\)\\u4e00-\\u9fa5]+"), "领域名称仅允许输入中文、字母、数字空格以及()._-@符号");
        }
        // 校验重复
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("domainId",domainId));
        query.must(QueryBuilders.termQuery("dirName.keyword", dir.getDirName()));
		if (dir.getCiType() == DirTypeEnum.IMAGE.getType() || dir.getCiType() == DirTypeEnum.IMAGE3D.getType()
				|| dir.getCiType() == DirTypeEnum.VEDIO.getType() || dir.getCiType() == DirTypeEnum.DOCUMENT.getType()) {
			query.must(QueryBuilders.termsQuery("ciType", Arrays.asList(DirTypeEnum.IMAGE.getType(),
					DirTypeEnum.IMAGE3D.getType(), DirTypeEnum.VEDIO.getType(),DirTypeEnum.DOCUMENT.getType())));
		} else {
			query.must(QueryBuilders.termQuery("ciType", dir.getCiType()));
		}
        if (dir.getId() != null) {
            query.mustNot(QueryBuilders.termQuery("id", dir.getId()));
        }
        List<CcCiClassDir> dirs = esDirSvc.getListByQuery(query);

		if (dir.getCiType() == DirTypeEnum.IMAGE.getType() || dir.getCiType() == DirTypeEnum.IMAGE3D.getType()
				|| dir.getCiType() == DirTypeEnum.VEDIO.getType() || dir.getCiType() == DirTypeEnum.DOCUMENT.getType()) {
            //图标管理重名判断
            for (CcCiClassDir ccCiClassDir : dirs) {
                if (ccCiClassDir.getParentId().equals(dir.getParentId())) {
                    Assert.isTrue(false, "BS_VERIFY_ERROR#{name: BS_DIR_FOLDER }#{type:BS_MVTYPE_DUPLICATE}${value:[" + dir.getDirName() + "]}");
                }
            }
        }else {
            if (!BinaryUtils.isEmpty(dirs)) {
                String errorCode = dir.getCiType() == DirTypeEnum.CLASS.getType() ? "BS_DIR_CLASS_DOMAIN" : (dir.getCiType() == DirTypeEnum.TAG.getType() ? "BS_DIR_GROUP" : "BS_DIR_FOLDER");
                Assert.isTrue(false, "BS_VERIFY_ERROR#{name:" + errorCode + "}#{type:BS_MVTYPE_DUPLICATE}${value:[" + dir.getDirName() + "]}");
            }
            Assert.isTrue(BinaryUtils.isEmpty(dirs), "BS_DIR_NAME_EXIST${dirName:" + dir.getDirName() + "}");
        }


        //父目录判断
        if (dir.getParentId() != 0) {
            //父判断
            CcCiClassDir parentDir = esDirSvc.getById(dir.getParentId());
            Assert.notNull(parentDir, "父目录文件夹不存在");
            //层级判断
            //父的父
            if (parentDir.getParentId() != 0) {
                CcCiClassDir grandfatherDir = esDirSvc.getById(parentDir.getParentId());
                if (grandfatherDir.getParentId() != 0) {
                    throw new RuntimeException("超过目录层级");
                }
            }
        }

        if (dir.getId() != null) {
            CcCiClassDir dir2 = esDirSvc.getById(dir.getId());
            Assert.notNull(dir2, "文件夹不存在");
            BeanUtils.copyProperties(dir, dir2, "createTime");
            return esDirSvc.saveOrUpdate(dir2);
        }
        return esDirSvc.saveOrUpdate(dir);
    }

    @Override
    public Integer deleteById(Long id) {
        return esDirSvc.deleteById(id);
    }

    @Override
    public List<CcCiClassDir> queryDirList(CCcCiClassDir cdt, String orders, Boolean isAsc) {
        if (cdt == null) {
            cdt = new CCcCiClassDir();
            cdt.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
        } else {
            Long domainId = cdt.getDomainId() == null ? BaseConst.DEFAULT_DOMAIN_ID : cdt.getDomainId();
            cdt.setDomainId(domainId);
        }

        // 默认查询分类领域
        cdt.setCiType(cdt.getCiType() == null ? 1 : cdt.getCiType());
        SortOrder orderType = (isAsc == null || isAsc) ? SortOrder.ASC : SortOrder.DESC;
        if (BinaryUtils.isEmpty(orders)) {
            orders = "createTime";
        }
        List<SortBuilder<?>> sorts = new LinkedList<>();
        sorts.add(SortBuilders.fieldSort(orders).order(orderType));
        return esDirSvc.getSortListByCdt(cdt, sorts);
    }

    @Override
    public List<CcCiClassDir> queryDirByDirName(CCcCiClassDir cdt){
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("dirName.keyword" , cdt.getDirName()));
        return esDirSvc.getListByQuery(query);
    }
}
