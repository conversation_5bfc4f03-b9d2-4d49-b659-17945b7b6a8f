package com.uinnova.product.eam.feign.client;

import com.uinnova.product.eam.comm.model.es.ClassSetting;
import com.uino.provider.feign.config.BaseFeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;

/**
 * <AUTHOR>
 */
@FeignClient(name = "${fuxi-feign-server-name:eam-fuxi}"
        , path = "${fuxi-feign-server-path:tarsier-eam}/eam/operateSetting",
        configuration = {BaseFeignConfig.class})
public interface OpenSettingFeign {

    @PostMapping("getSettings")
    List<ClassSetting> getSettings();
}
