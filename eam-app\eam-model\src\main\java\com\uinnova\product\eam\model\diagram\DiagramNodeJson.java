package com.uinnova.product.eam.model.diagram;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.model.dm.bean.DataModelEntityNodeVo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 视图节点nodeJson实体类
 * <AUTHOR>
 */
@Data
public class DiagramNodeJson implements Serializable {
    private String shapeName;
    private Integer erType;
    private String category;
    private String figure;
    private String stroke;
    private String fill;
    private String label;
    private String ciPrimaryKey;
    private Integer width;
    private Integer height;
    private Integer strokeWidth;
    private Integer parameter1;
    private Long classId;
    private String classCode;
    private Long ciId;
    private String className;
    private String ciCode;
    private String sourceLibType;
    private Boolean isDesignLibrary;
    private String key;
    private String loc;
    private Integer zOrder;
    private String erIcon;
    @Comment("实体是否折叠")
    private Boolean fold;
    @Comment("键区集合")
    private List<DataModelEntityNodeVo> items;
}
