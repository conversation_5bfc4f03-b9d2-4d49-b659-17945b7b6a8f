package com.uinnova.product.eam.web.asset.bean;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.base.model.CIDataInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class SpecialMasterVO implements Serializable {

    @Comment("标准规范主信息元素集合")
    private CIDataInfo special;

    @Comment("标准规范版本信息集合")
    private List<CIDataInfo> editionList;


    @Comment("标准规范主信息隐藏字段")
    private List<String> specialHiddenAttrs;

    @Comment("标准规范版本隐藏字段")
    private List<String> editionHiddenAttrs;

}
