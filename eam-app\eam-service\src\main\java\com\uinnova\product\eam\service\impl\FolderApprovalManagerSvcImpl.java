package com.uinnova.product.eam.service.impl;

import com.uinnova.product.eam.base.util.EamUtil;
import com.uinnova.product.eam.comm.model.es.FolderApprovalManager;
import com.uinnova.product.eam.comm.model.es.FolderApprovalUser;
import com.uinnova.product.eam.model.dto.FolderApprovalManagerDTO;
import com.uinnova.product.eam.service.IFolderApprovalManagerSvc;
import com.uinnova.product.eam.service.es.FolderApprovalManagerDao;
import com.uino.api.client.permission.IUserApiSvc;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.permission.query.CSysUser;
import com.uino.dao.util.ESUtil;
import com.uino.util.sys.SysUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

/**
 * 文件夹审批用户配置
 * <AUTHOR>
 */
@Slf4j
@Service
public class FolderApprovalManagerSvcImpl implements IFolderApprovalManagerSvc {

    @Resource
    private IUserApiSvc userApiSvc;
    @Resource
    private FolderApprovalManagerDao approvalManagerDao;

    @Override
    public Long addApproval(FolderApprovalManager dto) {
        FolderApprovalManager folderApprovalManager = approvalManagerDao.selectOne(QueryBuilders.termQuery("dirId", dto.getDirId()));
        if(folderApprovalManager == null){
            folderApprovalManager = new FolderApprovalManager();
            SysUser user = SysUtil.getCurrentUserInfo();
            folderApprovalManager.setCreator(user.getLoginCode());
            folderApprovalManager.setDomainId(user.getDomainId());
            folderApprovalManager.setId(ESUtil.getUUID());
        }
        folderApprovalManager.setDirId(dto.getDirId());
        folderApprovalManager.setUsers(dto.getUsers());
        folderApprovalManager.setRule(dto.getRule());
        return approvalManagerDao.saveOrUpdate(folderApprovalManager);
    }

    @Override
    public FolderApprovalManagerDTO queryByDirId(Long dirId){
        FolderApprovalManager manager = approvalManagerDao.selectOne(QueryBuilders.termQuery("dirId", dirId));
        if(manager == null){
            return null;
        }
        FolderApprovalManagerDTO result = EamUtil.copy(manager, FolderApprovalManagerDTO.class);
        if(CollectionUtils.isNotEmpty(manager.getUsers())){
            CSysUser cdt = new CSysUser();
            cdt.setLoginCodes(manager.getUsers().toArray(new String[0]));
            List<SysUser> userList = userApiSvc.getSysUserByCdt(cdt);
            userList.sort(Comparator.comparing(e -> manager.getUsers().indexOf(e.getLoginCode())));
            List<FolderApprovalUser> users = EamUtil.copy(userList, FolderApprovalUser.class);
            result.setUsers(users);
        }
        return result;
    }

    @Override
    public List<String> queryDirApprovalUser(Long dirId){
        FolderApprovalManager manager = approvalManagerDao.selectOne(QueryBuilders.termQuery("dirId", dirId));
        if(manager == null){
            return Collections.emptyList();
        }
        return manager.getUsers();
    }
}
