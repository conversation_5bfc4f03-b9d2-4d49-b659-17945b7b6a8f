package com.uinnova.product.eam.base.model;

import com.binary.core.util.BinaryUtils;
import com.uinnova.product.eam.comm.model.es.EamResource;
import lombok.Data;

import java.io.Serializable;

@Data
public class FileResourceMeta implements Serializable {

    private Long id;

    private String name;

    private Integer type;

    private String resPath;

    private String operator;

    private Long createTime;

    public FileResourceMeta(){
    }

    public FileResourceMeta(EamResource resource, String pathPrefix){
        if(!BinaryUtils.isEmpty(resource)){
            this.id = resource.getId();
            this.name = resource.getName() + "." + resource.getResType();
            this.resPath = pathPrefix + resource.getResPath();
            this.operator = resource.getOperator();
            this.createTime = resource.getCreateTime();
            this.type = resource.getType();
        }
    }

}
