package com.uinnova.product.vmdb.comm.idx;

import com.uinnova.product.vmdb.comm.expression.OP;

import java.io.Serializable;

/**
 * 
 * <AUTHOR>
 *
 */
public class CiAttrExp implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 属性定义 **/
    private Long attrDefId;

    /** 运算符 **/
    private OP op;

    /** 条件值 **/
    private Object value;

    public CiAttrExp() {
    }

    public CiAttrExp(Long attrDefId, OP op, Object value) {
        this.attrDefId = attrDefId;
        this.op = op;
        this.value = value;
    }

    public Long getAttrDefId() {
        return attrDefId;
    }

    public void setAttrDefId(Long attrDefId) {
        this.attrDefId = attrDefId;
    }

    public OP getOp() {
        return op;
    }

    public void setOp(OP op) {
        this.op = op;
    }

    public Object getValue() {
        return value;
    }

    public void setValue(Object value) {
        this.value = value;
    }

}
