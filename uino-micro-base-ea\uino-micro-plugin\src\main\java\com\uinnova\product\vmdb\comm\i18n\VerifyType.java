package com.uinnova.product.vmdb.comm.i18n;

/**
 * 
 * <AUTHOR>
 *
 */
public enum VerifyType {

    /**
     * 验证是否为空
     */
    EMPTY,

    /**
     * 验证是不为null
     */
    NULL,

    /**
     * 验证条件错误
     */
    CDT,

    /**
     * 已存在
     */
    EXIST,

    /**
     * 不存在
     */
    NOT_EXIST,

    /**
     * 参数错误
     */
    ARG_ERROR,

    /**
     * 数据超长
     */
    OVERLENGTH,

    /**
     * 使用中
     */
    USING,

    /** 重名 */
    DUPLICATE,

    /** 整数类型不匹配 */
    INT_MISMATCH,

    /** 小数类型不匹配 */
    DB_MISMATCH,

    /** 短文本类型不匹配 */
    STR_MISMATCH,

    /** 长文本类型不匹配 */
    LONGSTR_MISMATCH,

    /** 日期类型不匹配 */
    DATE_MISMATCH,

    /** 枚举类型不匹配 */
    ENUM_MISMATCH;

}
