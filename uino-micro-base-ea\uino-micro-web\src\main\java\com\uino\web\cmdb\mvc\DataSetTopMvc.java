package com.uino.web.cmdb.mvc;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import com.uino.init.api.ApiResult;
import com.uino.init.api.ApiVersion;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.uino.api.client.cmdb.IDataSetTopApiSvc;
import com.uino.web.BaseMvc;

import io.swagger.annotations.ApiOperation;

/**
 * @Classname DataSetTopController
 * @Description 置顶
 * @Date 2020/3/19 15:55
 * @Created by sh
 */
@ApiVersion(1)
@Api(value = "置顶", tags = {"数据集"})
@RestController
@RequestMapping("cmdb/dataSet/top")
public class DataSetTopMvc extends BaseMvc {

    @Autowired
    private IDataSetTopApiSvc dataSetTopApiSvc;

    @RequestMapping(value = {"/collectDataSet"}, method = RequestMethod.POST)
    @ApiOperation("置顶或取消置顶")
    public ApiResult<Boolean> collectDataSet(HttpServletRequest request, HttpServletResponse response, @RequestBody String body) {
        JSONObject jsonObject = JSONObject.parseObject(body);
        Long dataSetId = jsonObject.getLong("id");
        dataSetTopApiSvc.collectDataSet(dataSetId);
        return ApiResult.ok(this).data(true);
    }

}
