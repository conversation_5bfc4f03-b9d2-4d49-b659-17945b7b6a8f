package com.uinnova.product.eam.model.dto;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

/**
 * <AUTHOR> wangchun<PERSON>i
 * @Date :
 * @Description : 逻辑删除新建制品参数
 */
@Data
public class ReleaseArtifactDto {

    @Comment("制品类型id")
    private Long id;

    @Comment("数据状态[DATA_STATUS]   0=删除，1=正常")
    private Integer dataStatus;

    @Comment("是否发布   0=未发布（默认），1=已发布")
    private Integer releaseState;

}
