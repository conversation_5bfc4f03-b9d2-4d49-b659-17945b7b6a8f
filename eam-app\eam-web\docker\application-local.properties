#spring.profiles.include=minor
spring.config.import=application-minor.properties
#声明循环依赖
spring.main.allow-circular-references=true

#服务的访问路径
server.servlet.context-path=${CONTEXTPATH}

#=====================================Nacos相关配置=====================================
# 是否开启配置中心
spring.cloud.nacos.config.enabled=false
# 是否开启服务注册调用
spring.cloud.nacos.discovery.enabled=true
# 注册中心地址
spring.cloud.nacos.config.server-addr=${NACOS_SERVER:NACOS_PORT}
# 注册中心分组
spring.cloud.nacos.discovery.group=DEFAULT_GROUP
# 注册中心namespace配置
#spring.cloud.nacos.discovery.namespace=${NACOS_NAMESPACE}
spring.cloud.nacos.discovery.server-addr=${spring.cloud.nacos.config.server-addr}
# 注册中心账号密码
#spring.cloud.nacos.discovery.username=${NACOS_USER}
#spring.cloud.nacos.discovery.password=${NACOS_PASSWORD}

#=====================================redis相关配置=====================================
# Redis服务器地址
spring.data.redis.host=${REDIS_HOST}
# Redis服务器连接端口
spring.data.redis.port=${REDIS_PORT}
# Redis集群地址
#spring.data.redis.cluster.nodes=${REDIS_NODES}
# Redis哨兵
#spring.data.redis.sentinel.master=${REDIS_SENTINEL_MASTER}
#spring.data.redis.sentinel.nodes=${REDIS_SENTINEL_NODES}
# Redis数据库索引（默认为0）
spring.data.redis.database=0
# Redis服务器连接密码（默认为空）
spring.data.redis.password=${REDIS_PASSWORD}
# 连接超时时间（毫秒）
spring.data.redis.timeout=10000
# Lettuce
# 连接池最大连接数（使用负值表示没有限制）
spring.data.redis.lettuce.pool.max-active=8
# 连接池最大阻塞等待时间（使用负值表示没有限制）
spring.data.redis.lettuce.pool.max-wait=10000
# 连接池中的最大空闲连接
spring.data.redis.lettuce.pool.max-idle=8
# 连接池中的最小空闲连接
spring.data.redis.lettuce.pool.min-idle=0
# 自动刷新redis集群
spring.data.redis.lettuce.cluster.refresh.adaptive=true
spring.data.redis.lettuce.cluster.refresh.period=30000

#=====================================elasticsearch相关配置=====================================
##ElasticSearch地址
esIps=${ES_HOST}
##ElasticSearch数据库用户名
esUser=${ES_USER}
##ElasticSearch数据库密码
esPwd=${ES_PASSWORD}
##ElasticSearch是否开启认证
isAuth=true

#=====================================mysql相关配置=====================================
# Mysql数据库地址
ds.jdbc.vmdb.url=jdbc:mysql://${MYSQL_HOST}:${MYSQL_PORT}/${MYSQL_DB_NAME}?useUnicode=true&characterEncoding=utf-8&useSSL=false&autoReconnect=true&serverTimezone=GMT%2b8
ds.jdbc.vmdb.user=${MYSQL_USER}
ds.jdbc.vmdb.passwd=${MYSQL_PASSWORD}

#=====================================资源地址相关配置=====================================
#本地资源http服务地址
http.resource.space=${HTTP_RESOURCE}
#本地资源存储地址
local.resource.space=${LOCAL_RESOURCE}

#二级菜单默认图相对路径
local.resource.pic.url =/122/defaultIcon/default_menu_pic.png
local.web.resource.space = /home/<USER>/quickea/

#======================================对象存储相关配置=====================================
#对象存储
spring.cloud.obs.endpoint=${OBS_ENDPOINT}
# 对象存储访问ak
spring.cloud.obs.access-key=${OBS_ACCESS_KEY}
# 对象存储访问 sk
spring.cloud.obs.secret-key=${OBS_SECRET_KEY}
# 对象存储桶名称
spring.cloud.obs.bucketName=${OBS_BUCKET_NAME}
# 对象存储资源访问链接的签名有效期（s）
spring.cloud.obs.urlExpireSeconds=${OBS_URL_EXP}
# 对象存储region
spring.cloud.obs.region=${OBS_REGION}
spring.cloud.obs.isHttps=${OBS_IS_HTTPS}
# 实现对象存储的操作的SDK（huawei：华为官方提供的sdk，其余默认为阿里云sdk）
rsm.util.sdkType=${OBS_SDK_TYPE}
# 当前环境是否使用对象存储
obs.use=${OBS_USE}
# 资源路径前缀（后台部分功能，处理资源的路径时，并未使用http.resource.space配置，而是在路径前拼接了“/rsm”，对于这部分功能需要使用该配置处理返回给前台的资源路径）。（新增配置，非必需。不配置时默认值为：/tarsier-eam/rsm）
obs.rsm.url.prefix=${OBS_RSM_URL_PREFIX}

#=====================================登录鉴权相关配置=====================================
#登录方式：sso-扫码、短信、thingjs, thingjs-只支持thingjs登录， oauth-用户密码方式登录
monet.login.loginMethod=${LOGIN_METHOD}

spring.autoconfigure.exclude=${CONFIG_EXCLUDE}
#oauth鉴权相关配置
oauth.open=true
oauth.client.id=tarsier-eam
oauth.client.secret=secret
oauth.client.grant_type=authorization_code
oauth.server.url=${OAUTH_PATH}
oauth.server.in_url=${OAUTH_INPATH}
oauth.server.token_callback.url=${TOKEN_CALLBACK}
#权限模块http server path
permission.http.prefix=${PERMISSION_PATH}
#License服务地址
project.license.register.url=http://192.168.21.143/examples/#/license

#=====================================项目功能相关配置=====================================
#调用公共组件的服务方式
base.load-type=local
#对象管理勾选业务主键数量限制
uino.base.ci.primarykey.maxcount=5
#日志保留时长，单位：天
uino.log.clear.duration=7
#指标单位
kpi.units=度,斤
#颁发token时是否携带随机参数
oauth.token.random_param=false
uino.monitor.ep.exist=false
uino.monitor.event.send.url=
#配置对象管理显示库 // 私有库:PRIVATE，设计库：DESIGN，运行库/基线库：BASELINE。不配置默认为BASELINE
uino.eam.lib_type.show=PRIVATE,DESIGN,BASELINE
#允许跨域请求的主机地址
uino.eam.allowed.origins=${ALLOWED_ORIGINS}
#文档上传正则表达式
uino.eam.word_name_regex=.*[.](?i)(doc|docx|xls|xlsx|ppt|pptx|pdf|txt|jpg|jpeg|bmp|gif|png|ico|swf|eot|svg|ttf|woff|woff2)

#在线开发返回前台json保存路径
monet.exportJson.exportPath = /topo/rsm
#在topobuilder环境为true，其它默认为false
monet.use.topobuilder.thingjs=false
#数据超市使用的三库标识
uino.cmdb.libtype=DESIGN
#上传文件大小之和
total.file.size=524288000
#自动成图-部署架构图排序(DMZ-WEB-APP-DB)
auto.deployment.arch.diagram.sort=0-1-2-3
# license是否开启
uino.license=false
# 初始化加载索引
init.data.action=true
# 自动成图缓存时间
uino.auto.draw.cache.timeout=60000
# 数据建模C'实体发布后推送应用子系统相关数据到DIX(默认false不开启推送),以及dix接收数据接口
uino.eam.sync.system.open=${SYNC_SYSTEM_OPEN}
uino.eam.sync.dix.url=${SYNC_DIX_URL}

process.definition.key=cj_technical_scheme_approve
uino.eam.word.chapter_name_regex=.*[.](?i)(doc|docx|xls|xlsx|ppt|pptx|pdf|jpg|jpeg|png|bmp)
# xss漏洞相关配置
mica.xss.mode=${MICA_XSS_MODE}
mica.xss.enable-escape=${MICA_XSS_ENABLE_ESCAPE}
# 成熟度计算规则 {分类属性名称}拼接计算规则（加减乘除）
maturity.calculate.rule={MATURITY_CALC_RULE}
# 成熟度匹配规则 成熟度字典主键名称:判断规则（X表示得分值，配置大于、小于、等于条件）
maturity.match.rule={MATURITY_MATCH_RULE}
# 成熟度属性名 默认名称“成熟度”
maturity.attribute.name={MATURITY_ATTR_NAME}

# 关系遍历批处理任务的执行周期(cron表达式)
batch.process.relation.rule.cron=${BATCH_PROCESS_RELATION_RULE_CRON}
# 是否开启关系自动构建任务
batch.process.relation.rule.job.enable=${BATCH_PROCESS_RELATION_RULE_JOB_ENABLE}

# Dify相关配置
dify.diagram.api.url=${DIFY_DIAGRM_API_URL}
dify.diagram.api.key=${DIFY_DIAGRM_API_KEY}
dify.diagram.api.user=${DIFY_DIAGRM_API_USER}