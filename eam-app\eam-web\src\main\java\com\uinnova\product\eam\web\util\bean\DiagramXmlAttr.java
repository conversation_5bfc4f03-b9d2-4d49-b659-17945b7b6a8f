package com.uinnova.product.eam.web.util.bean;

import java.util.List;

import com.uinnova.product.vmdb.comm.model.ci.CCcCi;
import com.uinnova.product.vmdb.comm.model.rule.CcCiTagRuleItem;

public class DiagramXmlAttr {
	
	private List<CcCiTagRuleItem> ruleItems;
	
	private List<DiagramXmlRule> rules;
	
	private Long classId;
	
	private Long[] classIds;
	
	private Long[] tagIds;
	
	private CCcCi ciCdt;
	
	private Long id;
	
	private Integer type;
	
	private String name;
	
	public List<DiagramXmlRule> getRules() {
		return rules;
	}

	public void setRules(List<DiagramXmlRule> rules) {
		this.rules = rules;
	}

	public Long getClassId() {
		return classId;
	}

	public void setClassId(Long classId) {
		this.classId = classId;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}


	public Long[] getClassIds() {
		return classIds;
	}

	public void setClassIds(Long[] classIds) {
		this.classIds = classIds;
	}

	public Long[] getTagIds() {
		return tagIds;
	}

	public void setTagIds(Long[] tagIds) {
		this.tagIds = tagIds;
	}

	public CCcCi getCiCdt() {
		return ciCdt;
	}

	public void setCiCdt(CCcCi ciCdt) {
		this.ciCdt = ciCdt;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public List<CcCiTagRuleItem> getRuleItems() {
		return ruleItems;
	}

	public void setRuleItems(List<CcCiTagRuleItem> ruleItems) {
		this.ruleItems = ruleItems;
	}

}
