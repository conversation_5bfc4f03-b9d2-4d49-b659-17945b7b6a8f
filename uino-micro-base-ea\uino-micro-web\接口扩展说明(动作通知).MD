扩展接口前后动作使用动态代理是十分合理的行为，可对对应业务的变更进行切面在前/后增加自己的逻辑，如调用三方进行通知

如下接口为变更用户操作的接口
IUserApiSvc#saveOrUpdate
IUserApiSvc#saveOrUpdateUserInfoBatch
IUserApiSvc#syncUserBatch
IUserApiSvc#deleteById
IUserApiSvc#deleteSysUserByLoginCodeBatch

如下接口为用户与角色间关系操作接口
IUserApiSvc#saveUserRoleRlt
IRoleApiSvc#addRoleUserRlt

如下接口为角色操作接口
IRoleApiSvc#saveOrUpdate
IRoleApiSvc#deleteById
IRoleApiSvc#saveOrUpdate
IRoleApiSvc#saveOrUpdate
IRoleApiSvc#saveOrUpdate