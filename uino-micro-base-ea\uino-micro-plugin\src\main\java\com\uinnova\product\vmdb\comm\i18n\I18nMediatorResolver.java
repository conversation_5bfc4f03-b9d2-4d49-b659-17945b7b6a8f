package com.uinnova.product.vmdb.comm.i18n;

import com.binary.core.i18n.LanguageResolver;
import com.binary.core.i18n.LanguageTranslator;

/**
 * 
 * 该类用于处理国际化暂时不统一的情况 例如系统的策略是将翻译工作给前台处理, 而后台本身也需要进行翻译工作时可以设置想要{@link I18nMediator}.
 * 
 * 
 * <AUTHOR>
 *
 */
public abstract class I18nMediatorResolver {
    private static I18nMediator i18nMediator = new I18nMediator() {

        @Override
        public LanguageTranslator getGlobalTranslator() {
            return LanguageResolver.getTranslator();
        }

        @Override
        public LanguageTranslator getCustomTranslator() {
            return LanguageResolver.getTranslator();
        }
    };

    public static void setMediator(I18nMediator mediator) {
        if (mediator == null) {
            throw new IllegalArgumentException(" the mediator is null argument! ");
        }
        I18nMediatorResolver.i18nMediator = mediator;
    }

    /**
     * 获取指定国际化语言
     * 
     * @param languageCode
     *            语言代码
     * @return
     */
    public static String trans(String languageCode) {
        return getCustomTranslator().trans(languageCode);
    }

    /**
     * 获取指定国际化语言
     * 
     * @param languageCode
     *            语言代码
     * @param jsonParams
     *            语言中动态参数(JSON格式)
     * @return
     */
    public static String trans(String languageCode, String jsonParams) {
        return getCustomTranslator().trans(languageCode, jsonParams);
    }

    /**
     * 获取指定国际化语言
     * 
     * @param languageCode
     *            语言代码
     * @param params
     *            语言中动态参数, Bean or Map
     * @return
     */
    public static String trans(String languageCode, Object params) {
        return getCustomTranslator().trans(languageCode, params);
    }

    /**
     * 获取转化器
     * 
     * @return
     */
    public static LanguageTranslator getCustomTranslator() {
        LanguageTranslator translator = i18nMediator.getCustomTranslator();
        if (translator == null) {
            return LanguageResolver.getTranslator();
        }
        return translator;
    }

}
