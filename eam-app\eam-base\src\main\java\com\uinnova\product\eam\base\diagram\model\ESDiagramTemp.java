package com.uinnova.product.eam.base.diagram.model;

import com.binary.framework.bean.annotation.Comment;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * @Classname Es视图信息表
 * @Description TODO
 * <AUTHOR>
 * @Date 2021-06-16-15:58
 */
@Data
public class ESDiagramTemp implements Serializable {

    private static final long serialVersionUID = 1L;

    @Comment("视图id[id]")
    private String id;

    @Comment("dId[dId]")
    private Long dId;

    @Comment("视图ids[ids]")
    private Long[] ids;

    @Comment("视图名称[NAME]")
    private String name;

    @Comment("所属用户[USER_ID]")
    private Long userId;

    @Comment("所属目录[DIR_ID]")
    private Long dirId;

    @Comment("所属目录[DIR_IDS]")
    private Long[] dirIds;

    @Comment("目录类型[DIR_TYPE]  1=我的，私有库，2=设计库，3=基线库 ")
    private Integer dirType;

    @Comment("字体库")
    private String fontLibrary;

    @Comment("视图类型[DIAGRAM_TYPE]    视图类型:1=视图 3=公共模版 4=个人模板 5=wiki新建视图")
    private Integer diagramType;

    @Comment("视图描述[DIAGRAM_DESC]")
    private String diagramDesc;

    @Comment("视图SVG[DIAGRAM_SVG]")
    private String diagramSvg;

    @Comment("背景图[DIAGRAM_BG_IMG]")
    private String diagramBgImg;

    @Comment("背景样式[DIAGRAM_BG_CSS]")
    private String diagramBgCss;

    @Comment("视图图标_1[ICON_1]")
    private String icon1;

    @Comment("视图图标_2[ICON_2]")
    private String icon2;

    @Comment("视图图标_3[ICON_3]")
    private String icon3;

    @Comment("视图图标_4[ICON_4]")
    private String icon4;

    @Comment("视图图标_5[ICON_5]")
    private String icon5;

    @Comment("是否公开[IS_OPEN]    是否公开:1=开放 0=私有")
    private Integer isOpen;

    @Comment("公开时间[OPEN_TIME]")
    private Long openTime;

    @Comment("数据驱动类型[DATA_UP_TYPE]    数据驱动类型:1=不更新 2=标记提示 3=自动更新")
    private Integer dataUpType;

    @Comment("视图状态[STATUS]    视图状态:1=正常 0=回收站")
    private Integer status;

    @Comment("CI3D坐标[CI_3D_POINT]")
    private String ci3dPoint;

    @Comment("CI3D坐标2[CI_3D_POINT2]")
    private String ci3dPoint2;

    @Comment("搜索字段[SEARCH_FIELD]    搜索字段:视图名称")
    private String searchField;

    @Comment("查看次数[READ_COUNT]")
    private Long readCount;

    @Comment("应用关联CI[APP_RLT_CI_CODE]    关联CI:针对应用墙点击CI弹出组合视图")
    private String appRltCiCode;

    @Comment("参照版本id[REFER_VERSION_ID]")
    private Long referVersionId;

    @Comment("所属域[DOMAIN_ID]")
    private Long domainId;

    @Comment("数据状态[DATA_STATUS]    数据状态:0=删除，1=正常")
    private Integer dataStatus;

    @Comment("创建人[CREATOR]")
    private String creator;

    @Comment("创建人[CREATOR] operate-In[in]")
    private String[] creators;

    @Comment("修改人[MODIFIER]")
    private String modifier;

    @Comment("创建时间[CREATE_TIME]    创建时间:yyyyMMddHHmmss")
    private Long createTime;

    @Comment("更新时间[MODIFY_TIME]    更新时间:yyyyMMddHHmmss")
    private Long modifyTime;

    @Comment("缩略图保存时间[THUMBNAIL_SAVE_ TIME]    yyyyMMddHHmmss")
    private Long thumbnailSaveTime;

    @Comment("主题文件夹[SUBJECT_ID]")
    private Long subjectId;

    private Long oldDirId;

    @Comment("视图svg内容信息,用于生产缩略图")
    private String svg;

    @Comment("缩略图base64编码")
    private String thumbnail;

    @Comment("版本描述")
    private String versionDesc;

    @Comment("自定义版本号")
    private String versionNo;

    @Comment("视图关联元素信息")
    private List<VcDiagramEle> diagramEles;

    @Comment("视图分享记录")
    private List<DiagramShareRecordResult> shareRecords = new ArrayList<>();

    @Comment("转换为模板的时间[TEM_CONVERT_TIME]")
    private Long temConvertTime;

    @Comment("视图类型[VIEW_TYPE]")
    private String viewType;

    @Comment("视图使用形状json")
    private List<ESDiagramShape> leftPanelModel;

    @Comment("视图的水印信息")
    private ESWaterMarkInfo watermarkInfo;

    @Comment("视图颜色历史")
    private String colorHistoryMap;

    @Comment("视图版本,用于兼容")
    private String version;

    @Comment("历史版本标识，1--主版本，0--历史版本")
    private Integer historyVersionFlag;

    @Comment("转换老视图标识，0-老视图")
    private Integer transFlag;

    @Comment("视图设置")
    private String diagramSetting;

    @Comment("视图ID加密字段")
    @JsonProperty("id")
    private String dEnergy;

    @Comment("保留字段1")
    private String reserveredField1;
    @Comment("保留字段2")
    private String reserveredField2;
    @Comment("保留字段3")
    private String reserveredField3;
    @Comment("保留字段4")
    private String reserveredField4;
    @Comment("保留字段5")
    private String reserveredField5;

    @Comment("关联的模板id (不写入数据库)")
    private Long temDirId;
    @Comment("模板名称 (不写入数据库)")
    private String temDirName;
    @Comment("模板类型 (不写入数据库)")
    private Integer temType;

    public ESDiagram tempToESDiagram(ESDiagramTemp temp) {
        ESDiagram esDiagram = new ESDiagram();
        esDiagram.setId(temp.getDId());
        esDiagram.setDirId(temp.getDirId());
        esDiagram.setName(temp.getName());
        esDiagram.setIcon1(temp.getIcon1());
        return esDiagram;
    }

}
