package com.uinnova.product.eam.web.diagram.bean;

import java.io.Serializable;
import java.util.List;

import com.binary.framework.bean.annotation.Comment;
import com.binary.tools.excel.ExcelUtils.SheetInfo;
import com.uinnova.product.eam.comm.model.VcDiagram;

public class ExportDiagramInfo implements Serializable {
	private static final long serialVersionUID = 1L;
	
	
	@Comment("视图基本信息")
	private VcDiagram diagram;
	
	
	@Comment("视图下配置信息")
	private List<SheetInfo> sheetInfos;


	public VcDiagram getDiagram() {
		return diagram;
	}


	public void setDiagram(VcDiagram diagram) {
		this.diagram = diagram;
	}


	public List<SheetInfo> getSheetInfos() {
		return sheetInfos;
	}


	public void setSheetInfos(List<SheetInfo> sheetInfos) {
		this.sheetInfos = sheetInfos;
	}
	
	
	
	

}
