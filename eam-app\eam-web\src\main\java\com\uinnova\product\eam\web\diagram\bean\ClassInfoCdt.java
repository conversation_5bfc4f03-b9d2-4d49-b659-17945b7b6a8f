package com.uinnova.product.eam.web.diagram.bean;

import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;

public class ClassInfoCdt implements Condition {

	private static final long serialVersionUID = 1L;
	
	
	@Comment("CI的ID数组")
	private Long[] ciIds;
	
	@Comment("CI标签ID数组")
	private Long[] tagIds;
	
	public Long[] getCiIds() {
		return ciIds;
	}
	public void setCiIds(Long[] ciIds) {
		this.ciIds = ciIds;
	}
	public Long[] getTagIds() {
		return tagIds;
	}
	public void setTagIds(Long[] tagIds) {
		this.tagIds = tagIds;
	}
	


	
}
