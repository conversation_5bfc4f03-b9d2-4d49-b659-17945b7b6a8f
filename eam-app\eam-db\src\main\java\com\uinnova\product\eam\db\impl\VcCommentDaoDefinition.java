package com.uinnova.product.eam.db.impl;


import com.binary.framework.dao.DaoDefinition;

import com.uinnova.product.eam.comm.model.CVcComment;
import com.uinnova.product.eam.comm.model.VcComment;


/**
 * 视图评论表[VC_COMMENT]数据访问对象定义实现
 */
public class VcCommentDaoDefinition implements DaoDefinition<VcComment, CVcComment> {


	@Override
	public Class<VcComment> getEntityClass() {
		return VcComment.class;
	}


	@Override
	public Class<CVcComment> getConditionClass() {
		return CVcComment.class;
	}


	@Override
	public String getTableName() {
		return "VC_COMMENT";
	}


	@Override
	public boolean hasDataStatusField() {
		return false;
	}


	@Override
	public void setDataStatusValue(VcComment record, int status) {
	}


	@Override
	public void setDataStatusValue(CVcComment cdt, int status) {
	}


	@Override
	public void setCreatorValue(VcComment record, String creator) {
	}


	@Override
	public void setModifierValue(VcComment record, String modifier) {
	}


}


