package com.uino.bean.sys.business;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 
 * <AUTHOR>
 *
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value="条件查询登录日志",description = "条件查询登录日志")
public class QueryLoginLogRequestDto implements Serializable {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value="用户id",example = "123")
	private Long userId;

	@ApiModelProperty(value="开始时间")
	private Long createTimeStart;

	@ApiModelProperty(value="结束时间")
	private Long createTimeEnd;

	@ApiModelProperty(value = "登录用户",example = "admin")
	private String userCodeOrName;

	@ApiModelProperty(value="所属域",example = "1")
	private Long domainId;

	@ApiModelProperty(value="页码",example = "1")
	@Builder.Default
	private int pageNum = 1;

	@ApiModelProperty(value="每页记录数")
	@Builder.Default
	private int pageSize = 20;
}
