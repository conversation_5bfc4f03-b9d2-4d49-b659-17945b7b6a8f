package com.uinnova.product.eam.service;

import com.binary.jdbc.Page;
import com.uinnova.product.eam.comm.model.VcDiagram;
import com.uinnova.product.eam.comm.model.VcDiagramDir;
import com.uinnova.product.eam.model.DiagramQueryBean;
import com.uinnova.product.eam.model.SysDirVO;
import com.uinnova.product.eam.model.VcDiagramInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CiGroupPage;

import java.util.List;

@Deprecated
public interface IEamDiagramSvc {
    /**
     * 查询视图列表
     *
     * @param cdt
     * @param orders
     * @return
     */
    List<VcDiagram> queryDiagramList(DiagramQueryBean cdt, String orders);

    /**
     * 分页查询视图列表
     *
     * @param cdt
     * @param orders
     * @return
     */
    Page<VcDiagram> queryDiagramListPage(DiagramQueryBean cdt, String orders);

    /**
     * 复制视图
     *
     * @param newName 新的视图名称
     * @param newDirId 新的目录ID
     * @param diagramId 视图ID
     *
     * @return 新视图ID
     */
    Long copyDiagramById(String newName, Long newDirId, Long diagramId);

    /**
     * 更新视图名字和目录信息
     *
     * @param newName   新的视图名字
     * @param newDirId  新的目录ID
     * @param diagramId 视图ID
     * @return
     */
    Integer updateDiagramNameById(String newName, Long newDirId, Long diagramId);

    /**
     * 更新视图的基本信息
     *
     * @param record 视图信息
     * @return 保存后的结果
     */
    VcDiagram updateDiagramBaseInfo(VcDiagram record);

    /**
     * 根据视图ID数组查询视图的详情
     * @param ids 视图ID数组
     * @return
     */
    List<VcDiagramInfo> queryDiagramInfoByIds(Long[] ids);

    /**
     * 获取ci系统列表
     * @param type
     * @return
     */
    List findCiSysList(Integer type, String name);

    /**
     * 新增系统设计
     * @param type
     * @param ciId
     */
    Long insertCiSysDesign(Integer type, Long ciId);

    List<VcDiagramDir> querypublicDesignDir();

    Long insertCiSysDir(int type, Long esSysId);

    Long addSysDir(SysDirVO sysDirVO);

    /**
     * 设计库系统列表
     * @return
     */
    CiGroupPage findDesignCiSysList();

    /**
     *  根据视图ID改变流程状态
     * @param eIds
     * @return
     */
    Boolean changeFlowByDiagramIds(List<String> eIds, Integer flowStatus);

}
