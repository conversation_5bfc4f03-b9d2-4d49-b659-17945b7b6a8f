package com.uino.bean.permission.business.request;

import java.util.Set;

import org.springframework.util.Assert;

import com.uino.bean.permission.business.IValidDto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户继承组织对应角色。
 * 
 * <AUTHOR>
 *
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ExtendOrgRoleRequestDto implements IValidDto {

	/**
	 * 要继承角色的组织id
	 */
	private Long orgId;
	/**
	 * 要继承组织角色的用户id
	 */
	private Set<Long> userIds;

	private Long domainId;

	@Override
	public void valid() {
		Assert.notNull(orgId, "ORG_NOT_NULL");
		Assert.notEmpty(userIds, "USER_NOT_NULL");

	}
}
