<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.4.5</version>
    </parent>
    <groupId>com.uinnova.product.eam</groupId>
    <artifactId>eam</artifactId>
    <packaging>pom</packaging>
    <version>fuxi-1.0.0-SNAPSHOT</version>
    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <java.version>17</java.version>
        <uino-micro-base.version>1.0.0-SNAPSHOT</uino-micro-base.version>
        <i18n.version>5.140.1</i18n.version>
        <lombok.version>1.18.30</lombok.version>
        <powermock.version>2.0.2</powermock.version>
        <tomcat.version>11.0.6</tomcat.version>
        <mysql.version>8.0.33</mysql.version>
        <spring.log4j2.version>3.4.5</spring.log4j2.version>
        <log4j2.version>2.24.0</log4j2.version>
        <nacos.config.version>2023.0.3.2</nacos.config.version>
        <spring-cloud.version>2024.0.1</spring-cloud.version>
        <io.netty.version>4.1.121.Final</io.netty.version>
        <guava.version>33.4.8-jre</guava.version>
        <poi.version>4.1.2</poi.version>
        <junit.version>4.13.2</junit.version>
        <openfeign.version>4.2.1</openfeign.version>
        <elasticsearch.version>7.17.14</elasticsearch.version>
        <alibaba.druid.version>1.2.8</alibaba.druid.version>
        <jakarta.validation.version>3.0.2</jakarta.validation.version>
        <jakarta.servlet-api.version>6.1.0</jakarta.servlet-api.version>
        <commons.compress.version>1.21</commons.compress.version>
        <loadbalancer.version>4.1.1</loadbalancer.version>
        <spring.kafka.version>3.3.5</spring.kafka.version>
        <kafka.clients.version>3.4.0</kafka.clients.version>
        <json.version>20231013</json.version>
        <bcpkix.jdk18on.version>1.74</bcpkix.jdk18on.version>
        <lettuce.version>6.5.5.RELEASE</lettuce.version>
    </properties>

    <modules>
        <module>eam-api</module>
        <module>eam-base</module>
        <module>eam-db</module>
        <module>eam-feign-client</module>
        <!--        <module>eam-feign-server</module>-->
        <module>eam-model</module>
        <module>eam-service</module>
        <module>eam-web</module>
        <module>eam-workable</module>
        <module>eam-workable-feign-client</module>
<!--        <module>eam-project-web</module>-->
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcpkix-jdk18on</artifactId>
                <version>${bcpkix.jdk18on.version}</version>
            </dependency>
            <dependency>
                <groupId>org.json</groupId>
                <artifactId>json</artifactId>
                <version>${json.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>kafka-clients</artifactId>
                <version>${kafka.clients.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-fileupload</groupId>
                <artifactId>commons-fileupload</artifactId>
                <version>1.5</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-codec-dns</artifactId>
                <version>${io.netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-codec-http</artifactId>
                <version>${io.netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-transport-native-unix-common</artifactId>
                <version>${io.netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-transport-native-epoll</artifactId>
                <version>${io.netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-transport-classes-epoll</artifactId>
                <version>${io.netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-codec-http2</artifactId>
                <version>${io.netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-common</artifactId>
                <version>${io.netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-codec</artifactId>
                <version>${io.netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-buffer</artifactId>
                <version>${io.netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-transport</artifactId>
                <version>${io.netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-resolver</artifactId>
                <version>${io.netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-resolver-dns</artifactId>
                <version>${io.netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-handler</artifactId>
                <version>${io.netty.version}</version>
            </dependency>

            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcprov-jdk15on</artifactId>
                <version>1.67</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-compress</artifactId>
                <version>${commons.compress.version}</version>
            </dependency>
            <dependency>
                <groupId>org.xerial.snappy</groupId>
                <artifactId>snappy-java</artifactId>
                <version>1.1.10.4</version>
            </dependency>
            <dependency>
                <groupId>commons-net</groupId>
                <artifactId>commons-net</artifactId>
                <version>3.9.0</version>
            </dependency>
            <dependency>
                <groupId>org.jsoup</groupId>
                <artifactId>jsoup</artifactId>
                <version>1.17.2</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>
            <dependency>
                <groupId>org.yaml</groupId>
                <artifactId>snakeyaml</artifactId>
                <version>${snakeyaml.version}</version>
            </dependency>
            <dependency>
                <groupId>jakarta.validation</groupId>
                <artifactId>jakarta.validation-api</artifactId>
                <version>${jakarta.validation.version}</version>
            </dependency>
            <dependency>
                <groupId>jakarta.servlet</groupId>
                <artifactId>jakarta.servlet-api</artifactId>
                <version>${jakarta.servlet-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.uino</groupId>
                <artifactId>i18n</artifactId>
                <version>${i18n.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>1.2.83</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>2.8.9</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis</artifactId>
                <version>3.5.13</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis-spring</artifactId>
                <version>3.0.3</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>2.7</version>
            </dependency>
            <dependency>
                <groupId>xerces</groupId>
                <artifactId>xercesImpl</artifactId>
                <version>2.12.2</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <distributionManagement>
        <repository>
            <id>uinnova-releases</id>
            <name>internal nexus repository</name>
            <url>https://mvn-dev.uino.cn/repository/uinnova-releases</url>
        </repository>
        <snapshotRepository>
            <id>uinnova-snapshots</id>
            <name>internal nexus repository</name>
            <url>https://mvn-dev.uino.cn/repository/uinnova-snapshots</url>
        </snapshotRepository>
    </distributionManagement>
    <repositories>
        <repository>
            <id>uino-releases</id>
            <name>Release of Uino</name>
            <url>https://mvn.uino.cn/repository/i18n-releases/</url>
        </repository>
    </repositories>
</project>