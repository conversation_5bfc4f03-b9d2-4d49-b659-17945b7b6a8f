package com.uinnova.product.eam.base.diagram.mix.model;




import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;


@Comment("视图CI属性显示版本表[VC_DIAGRAM_CI_ATTR_VERSION]")
public class VcDiagramCiAttrVersion implements EntityBean {
	private static final long serialVersionUID = 1L;


	@Comment("ID[ID]")
	private Long id;


	@Comment("视图历史ID[DIAGRAM_ID]")
	private Long diagramId;


	@Comment("配置类型[OBJ_TYPE]    1=CI分类 2=CI")
	private Integer objType;


	@Comment("配置对象[OBJ_ID]    配置类型为1时为ClassID，类型为2时为CI_ID")
	private String objId;


	@Comment("属性定义ID[ATTR_DEF_ID]")
	private Long attrDefId;


	@Comment("所属域[DOMAIN_ID]")
	private Long domainId;


	@Comment("创建时间[CREATE_TIME]    yyyyMMddHHmmss")
	private Long createTime;


	@Comment("修改时间[MODIFY_TIME]    yyyyMMddHHmmss")
	private Long modifyTime;




	public Long getId() {
		return this.id;
	}
	public void setId(Long id) {
		this.id = id;
	}


	public Long getDiagramId() {
		return this.diagramId;
	}
	public void setDiagramId(Long diagramId) {
		this.diagramId = diagramId;
	}


	public Integer getObjType() {
		return this.objType;
	}
	public void setObjType(Integer objType) {
		this.objType = objType;
	}


	public String getObjId() {
		return this.objId;
	}
	public void setObjId(String objId) {
		this.objId = objId;
	}


	public Long getAttrDefId() {
		return this.attrDefId;
	}
	public void setAttrDefId(Long attrDefId) {
		this.attrDefId = attrDefId;
	}


	public Long getDomainId() {
		return this.domainId;
	}
	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}


	public Long getCreateTime() {
		return this.createTime;
	}
	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}


	public Long getModifyTime() {
		return this.modifyTime;
	}
	public void setModifyTime(Long modifyTime) {
		this.modifyTime = modifyTime;
	}


}


