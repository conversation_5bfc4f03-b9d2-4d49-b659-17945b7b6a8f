package com.uinnova.product.eam.model.cj.domain;

import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.base.cj.BaseEntity;
import com.uinnova.product.eam.model.cj.vo.PlanTemplateIntroduceChapterVo;
import lombok.*;

import java.util.List;

/**
 * 方案章节 EntityBean、Condition
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ChapterInstance extends BaseEntity implements Condition {

    /**
     * 章节 id
     */
    private Long id;

    /**
     * 章节是否来自模板
     */
    private Boolean isFromTemplate;

    /**
     * 章节名称
     */
    private String name;

    /**
     * 章节层级
     */
    private Integer level;

    /**
     * 序号
     */
    private Double orderId;

    /**
     * 章节序号，前端显示
     */
    private String serialNum;

    /**
     * 是否展开
     */
    private Boolean expand;

    /**
     * 章节是否必填
     */
    private Boolean required;

    /**
     * 父章节id
     */
    private Long parentId;

    /**
     * 所属方案的id
     */
    private Long planId;

    /**
     * 章节模板的id
     */
    private Long templateId;

    /**
     * 章节模板的parentId
     */
    private Long templateParentId;

    /**
     * 删除状态
     */
    private Boolean deleted;

    private List<ChapterResource> chapterResources;

    @Comment("引入章节信息")
    private List<PlanTemplateIntroduceChapterVo> planTemplateIntroduceChapterVoList;

    /**
     * 引入章节的名称
     */
    private String introduceChapterName;
    /**
     * 引用方式  1表示插入 2表示覆盖
     */
    private Integer introduceWay;
    /**
     * 引入章节的方案的id
     */
    private Long introducePlanId;
    /**
     * 引入章节的id
     */
    private Long introduceChapterId;

    /**
     * 业务主键
     */
    private String businessId;

    /**
     * 版本号
     */
    private String version;

    /**
     * 状态 0:历史版本 1:草稿状态 2:发布状态
     */
    private Integer status;

    /**
     * 模板是否已被删除 1: 已删除
     */
    private Integer templateDelete;
}
