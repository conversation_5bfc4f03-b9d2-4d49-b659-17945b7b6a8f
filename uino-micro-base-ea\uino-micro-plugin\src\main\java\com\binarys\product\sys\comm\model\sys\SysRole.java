package com.binarys.product.sys.comm.model.sys;




import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;


@Comment("角色表[SYS_ROLE]")
public class SysRole implements EntityBean {
	private static final long serialVersionUID = 1L;


	@Comment("ID[ID]")
	private Long id;


	@Comment("角色代码[ROLE_CODE]")
	private String roleCode;


	@Comment("角色名称[ROLE_NAME]")
	private String roleName;


	@Comment("角色类型[ROLE_TYPE]    角色类型:1=全局角色 2=私有角色")
	private Integer roleType;


	@Comment("角色描述[ROLE_DESC]")
	private String roleDesc;


	@Comment("状态[STATUS]    状态:0=无效 1=有效")
	private Integer status;


	@Comment("所属域[DOMAIN_ID]")
	private Long domainId;


	@Comment("所属用户域[USER_DOMAIN_ID]")
	private Long userDomainId;


	@Comment("数据状态[DATA_STATUS]    数据状态:数据状态：1-正常 0-删除")
	private Integer dataStatus;


	@Comment("创建人[CREATOR]")
	private String creator;


	@Comment("修改人[MODIFIER]")
	private String modifier;


	@Comment("创建时间[CREATE_TIME]    创建时间:yyyyMMddHHmmss")
	private Long createTime;


	@Comment("修改时间[MODIFY_TIME]    修改时间:yyyyMMddHHmmss")
	private Long modifyTime;




	public Long getId() {
		return this.id;
	}
	public void setId(Long id) {
		this.id = id;
	}


	public String getRoleCode() {
		return this.roleCode;
	}
	public void setRoleCode(String roleCode) {
		this.roleCode = roleCode;
	}


	public String getRoleName() {
		return this.roleName;
	}
	public void setRoleName(String roleName) {
		this.roleName = roleName;
	}


	public Integer getRoleType() {
		return this.roleType;
	}
	public void setRoleType(Integer roleType) {
		this.roleType = roleType;
	}


	public String getRoleDesc() {
		return this.roleDesc;
	}
	public void setRoleDesc(String roleDesc) {
		this.roleDesc = roleDesc;
	}


	public Integer getStatus() {
		return this.status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}


	public Long getDomainId() {
		return this.domainId;
	}
	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}


	public Long getUserDomainId() {
		return this.userDomainId;
	}
	public void setUserDomainId(Long userDomainId) {
		this.userDomainId = userDomainId;
	}


	public Integer getDataStatus() {
		return this.dataStatus;
	}
	public void setDataStatus(Integer dataStatus) {
		this.dataStatus = dataStatus;
	}


	public String getCreator() {
		return this.creator;
	}
	public void setCreator(String creator) {
		this.creator = creator;
	}


	public String getModifier() {
		return this.modifier;
	}
	public void setModifier(String modifier) {
		this.modifier = modifier;
	}


	public Long getCreateTime() {
		return this.createTime;
	}
	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}


	public Long getModifyTime() {
		return this.modifyTime;
	}
	public void setModifyTime(Long modifyTime) {
		this.modifyTime = modifyTime;
	}


}


