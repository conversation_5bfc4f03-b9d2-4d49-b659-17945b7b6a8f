package com.uino.provider.feign.cmdb;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uino.bean.cmdb.base.ESCITagInfo;
import com.uino.bean.cmdb.business.ClassNodeInfo;
import com.uino.bean.cmdb.query.ESAttrAggBean;
import com.uino.bean.cmdb.query.ESTagSearchBean;
import com.uino.provider.feign.config.BaseFeignConfig;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 
 * <AUTHOR>
 *
 */
@Component
@FeignClient(name = "${feign-server-name:tarsier-platform}", path = "${feign-server-path:tarsier-platform}/feign/tag", configuration = {BaseFeignConfig.class})
public interface TagFeign {

    /**
     * 保存标签规则
     * 
     * @param tagInfo
     * @return
     */
    @PostMapping("saveOrUpdateCITagRule")
    Long saveOrUpdateCITagRule(@RequestBody ESCITagInfo tagInfo);

    /**
     * 根据id查询标签规则
     * 
     * @param id
     * @return
     */
    @PostMapping("getCITagRuleById")
    ESCITagInfo getCITagRuleById(@RequestBody Long id);

    /**
     * 获取标签树
     * 
     * @return
     */
    @PostMapping("getTagTree")
    List<ClassNodeInfo> getTagTree(Long domainId);

    /**
     * 根据标签获取CI信息
     * 
     * @param bean
     * @return
     */
    @PostMapping("getCIInfoListByTag")
    Page<CcCiInfo> getCIInfoListByTag(@RequestBody ESTagSearchBean bean);

    /**
     * 删除标签
     * 
     * @param tagId
     * @return
     */
    @PostMapping("deleteById")
    Integer deleteById(@RequestBody Long tagId);

    /**
     * 获取属性值列表
     * 
     * @param searchBean
     * @return
     */
    @PostMapping("getAttrValuesBySearchBean")
    Page<String> getAttrValuesBySearchBean(@RequestParam(value = "domainId") Long domainId, @RequestBody ESAttrAggBean searchBean);

    /**
     * 更改标签所属目录
     * 
     * @param tagInfo
     * @return
     */
    @PostMapping("changeTagDir")
    Boolean changeTagDir(@RequestBody ESCITagInfo tagInfo);

}
