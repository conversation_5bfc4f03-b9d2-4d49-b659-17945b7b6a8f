package com.uinnova.product.vmdb.provider.quality;

import com.binary.core.io.Resource;
import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.comm.model.es.CESCiQualityData;
import com.uinnova.product.vmdb.comm.model.es.ESCiQualityData;
import com.uinnova.product.vmdb.comm.model.es.ESCiQualityDataCount;
import com.uinnova.product.vmdb.provider.quality.bean.ProblemCount;
import com.uinnova.product.vmdb.provider.quality.bean.QualityCheckDefine;
import com.uinnova.product.vmdb.provider.quality.bean.QualityDataTitle;
import com.uinnova.product.vmdb.provider.quality.bean.QualitySourceCount;

import java.io.File;
import java.util.List;



public interface CiQualityDataSvc {

    /**
     * 获取配置文件中的信息
     * @param domainId
     * @return
     */
    public QualityCheckDefine queryQualityCheckConfigFromJson(Long domainId);

    /**
     * 查询当前最后一次统计的问题数据来源以及数量
     * @return List{source, count};
     */
    public List<QualitySourceCount> queryCurrentSourceCount(Long domainId);

    /**
     * 通过问题来源查询问题数量曲线
     * @param pageNum pageNum
     * @param pageSize pageSize
     * @param source 来源
     * @param sortOrder 顺序
     * @return List{time, count};
     */
    public List<ProblemCount> queryCountBySource(Long domainId, Integer pageNum, Integer pageSize, String source, Integer sourceType, String sortOrder);


    /**
     * 通过问题来源查询Title
     * @param source 问题来源
     * @return ls
     */
    public List<QualityDataTitle> queryTitle(Long domainId, String source, Integer sourceType);


    /**
     * 查询质量检查数据
     * @param domainId domainId
     * @param pageNum pageNum
     * @param pageSize pageSize
     * @param cdt 查询条件
     * @return
     */
    public Page<ESCiQualityData> queryQualityDataPageList(Long domainId, Integer pageNum, Integer pageSize, CESCiQualityData cdt);

    /**
     * 批量保存质量数据
     * @param records
     * @return
     */
    public boolean saveQualityDataBatch(Long domainId, List<ESCiQualityData> records);

    /**
     * 保存质量检查结果统计数据
     * @param record
     * @return
     */
    public boolean saveQualityDataCount(Long domainId, ESCiQualityDataCount record);

    /**
     * 删除所有质量数据
     * @param domainId
     * @return
     */
    public boolean deleteQualityDataAll(Long domainId);

    /**
     * 通过时间戳删除质量数据
     * @param domainId
     * @param time
     * @return
     */
    public boolean deleteQualityDataByTime(Long domainId, Long time);

    /**
     * 导出数据
     * @param domainId
     * @param cdt
     * @return
     */
    public File exportXlsxQualityData(Long domainId, CESCiQualityData cdt);

    /**
     * 通过导出返回的文件名获取Excel
     * @param fileName
     * @return
     */
    public Resource getResource(String fileName);
}
