package com.uinnova.product.eam.service.es;

import com.uinnova.product.eam.comm.model.es.CEamRecentlyView;
import com.uinnova.product.eam.comm.model.es.EamRecentlyView;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;

@Service
public class IamsEsRecentlyViewDao extends AbstractESBaseDao<EamRecentlyView, CEamRecentlyView> {
    @Override
    public String getIndex() {
        return "uino_eam_recently_view";
    }

    @Override
    public String getType() {
        return getIndex();
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }

}
