package com.binary.jdbc.adapter.support.kingbase;

import com.binary.jdbc.adapter.SqlDissolver;
import com.binary.jdbc.adapter.support.AbstractSqlParser;
import com.binary.jdbc.exception.SqlParserException;

public abstract class AbstractKingbaseSqlParser extends AbstractSqlParser {

	
	

	@Override
	public String parseCountSql(String sql) {
		return "select count(1) from ("+sql+")";
	}
	

	
	@Override
	public String parseSimpleCountSql(String sql) {
		return parseCountSql(sql);
	}
	
	

	
	@Override
	public String parsePagingSql(SqlDissolver dissolver, String orderByFields, long pageNum, long pageSize) {
		String[] fields = dissolver.getFields();
		return paging(dissolver.getBaseSql(), fields, orderByFields, pageNum, pageSize);
	}
	
	
	
	private String paging(String sql, String[] fields, String orderByFields, long pageNum, long pageSize) {
		if(orderByFields==null || (orderByFields=orderByFields.trim()).length()==0) throw new SqlParserException(" the orderByFields is NULL argument! ");
		StringBuffer sb = new StringBuffer();
		long startRow = (pageNum - 1) * pageSize + 1;
		sb.append(sql).append(" order by ").append(orderByFields).append(" limit ").append(pageSize).append(" offset ").append(startRow-1).append(" ");
		return sb.toString();
	}
	
	
	
}
