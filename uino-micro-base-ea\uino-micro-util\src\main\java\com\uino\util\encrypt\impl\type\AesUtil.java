package com.uino.util.encrypt.impl.type;

import lombok.extern.log4j.Log4j2;
import org.apache.commons.codec.binary.Base64;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;

/**
 * @Auther: YGQ
 * @Date: 2020/09/22 13:06
 * @Description:
 */
@Log4j2
public class AesUtil {

    /**
     * encrypt
     *
     * @param sKey The aes-128-ecb encryption mode is used. The key must be 16 bits
     * @param sSrc String to encrypt
     */
    public static String encrypt(String sKey, String sSrc) {
        checkKey(sKey);
        try {

            byte[] raw = sKey.getBytes(StandardCharsets.UTF_8);
            SecretKeySpec sKeySpec = new SecretKeySpec(raw, "AES");

            // Algorithm/pattern/complement mode
            Cipher cipher = null;
            cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");

            cipher.init(Cipher.ENCRYPT_MODE, sKeySpec);
            byte[] encrypted = cipher.doFinal(sSrc.getBytes(StandardCharsets.UTF_8));

            // Here, BASE64 is used for transcoding, and it encrypts twice.
            return new Base64().encodeToString(encrypted);
        } catch (Exception e) {
            log.error(">>> encrypt error : ", e);
            return null;
        }
    }

    /**
     * decrypt
     *
     * @param sKey The aes-128-ecb encryption mode is used. The key must be 16 bits
     * @param sSrc String to decrypt
     */
    public static String decrypt(String sKey, String sSrc) {
        try {
            checkKey(sKey);
            byte[] raw = sKey.getBytes(StandardCharsets.UTF_8);
            SecretKeySpec sKeySpec = new SecretKeySpec(raw, "AES");
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
            cipher.init(Cipher.DECRYPT_MODE, sKeySpec);

            // Decrypt with Base64 first
            byte[] encrypted1 = new Base64().decode(sSrc);
            try {
                byte[] original = cipher.doFinal(encrypted1);
                return new String(original, StandardCharsets.UTF_8);
            } catch (Exception e) {
                System.out.println(e.toString());
                return null;
            }
        } catch (Exception ex) {
            System.out.println(ex.toString());
            return null;
        }
    }

    /**
     * verify that the key conforms to the specification
     *
     * @param sKey key
     */
    private static void checkKey(String sKey) {
        // Check whether the Key is correct
        if (sKey == null) {
            throw new IllegalArgumentException("key is null");
        }
        // Check whether the Key is 16 bits
        if (sKey.length() != 16) {
            throw new IllegalArgumentException("key length is not 16 bits");
        }
    }

    /**
     * invoke the sample
     */
    public static void main(String[] args) throws Exception {
        // The aes-128-ecb encryption mode is used. The key must be 16 bits
        String cKey = "%CodeReivewsAes$";
        // The string to be encrypted
        String cSrc = "diCodeReviews&%";
        // encrypt
        String enString = AesUtil.encrypt(cKey, cSrc);
        System.out.println("the encrypted string is：" + enString);
        // decrypt
        String deString = AesUtil.decrypt(cKey, enString);
        System.out.println("the decrypted string is：" + deString);
    }
}