package com.uino.bean.permission.query;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

/**
 * condition-table: 组织表[SYS_ORG]
 * 
 * <AUTHOR>
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CSysOrg implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * condition-field: ID[ID] operate-Equal[=]
	 */
	private Long id;

	/**
	 * condition-field: ID[ID] operate-In[in]
	 */
	private Long[] ids;

	/**
	 * condition-field: ID[ID] operate-GTEqual[>=]
	 */
	private Long startId;

	/**
	 * condition-field: ID[ID] operate-LTEqual[<=]
	 */
	private Long endId;

	/**
	 * condition-field: 组织代码[ORG_CODE] operate-Like[like]
	 */
	private String orgCode;

	/**
	 * condition-field: 组织代码[ORG_CODE] operate-Equal[=]
	 */
	private String orgCodeEqual;

	/**
	 * condition-field: 组织代码[ORG_CODE] operate-In[in]
	 */
	private String[] orgCodes;

	/**
	 * condition-field: 组织名称[ORG_NAME] operate-Like[like]
	 */
	private String orgName;

	/**
	 * condition-field: 组织级别[ORG_LEVEL] operate-Equal[=] 组织级别:组织级别：1-一级 2-二级
	 * 3-三级 4-四级 ...
	 */
	private Integer orgLevel;

	/**
	 * condition-field: 组织级别[ORG_LEVEL] operate-In[in] 组织级别:组织级别：1-一级 2-二级 3-三级
	 * 4-四级 ...
	 */
	private Integer[] orgLevels;

	/**
	 * condition-field: 组织级别[ORG_LEVEL] operate-GTEqual[>=] 组织级别:组织级别：1-一级 2-二级
	 * 3-三级 4-四级 ...
	 */
	private Integer startOrgLevel;

	/**
	 * condition-field: 组织级别[ORG_LEVEL] operate-LTEqual[<=] 组织级别:组织级别：1-一级 2-二级
	 * 3-三级 4-四级 ...
	 */
	private Integer endOrgLevel;

	/**
	 * condition-field: 父节点ID[PARENT_ORG_ID] operate-Equal[=]
	 * 权限组织ID:权限组织ID（记录该组织具有权限的组织ID，如公司类组织的权限组织是其自身，部门类组织的权限组织是所属公司）
	 */
	private Long parentOrgId;

	/**
	 * condition-field: 父节点ID[PARENT_ORG_ID] operate-In[in]
	 * 权限组织ID:权限组织ID（记录该组织具有权限的组织ID，如公司类组织的权限组织是其自身，部门类组织的权限组织是所属公司）
	 */
	private Long[] parentOrgIds;

	/**
	 * condition-field: 父节点ID[PARENT_ORG_ID] operate-GTEqual[>=]
	 * 权限组织ID:权限组织ID（记录该组织具有权限的组织ID，如公司类组织的权限组织是其自身，部门类组织的权限组织是所属公司）
	 */
	private Long startParentOrgId;

	/**
	 * condition-field: 父节点ID[PARENT_ORG_ID] operate-LTEqual[<=]
	 * 权限组织ID:权限组织ID（记录该组织具有权限的组织ID，如公司类组织的权限组织是其自身，部门类组织的权限组织是所属公司）
	 */
	private Long endParentOrgId;

	/**
	 * condition-field: 组织层级路径[ORG_LVL_PATH] operate-Like[like]
	 * 组织层级路径:组织层级路径（从根组织ID到当前组织ID的上下级层级路径串，用#号分隔）
	 */
	private String orgLvlPath;

	/**
	 * condition-field: 显示排序[ORDER_NO] operate-Equal[=]
	 */
	private Integer orderNo;

	/**
	 * condition-field: 显示排序[ORDER_NO] operate-In[in]
	 */
	private Integer[] orderNos;

	/**
	 * condition-field: 显示排序[ORDER_NO] operate-GTEqual[>=]
	 */
	private Integer startOrderNo;

	/**
	 * condition-field: 显示排序[ORDER_NO] operate-LTEqual[<=]
	 */
	private Integer endOrderNo;

	/**
	 * condition-field: 是否末级[IS_LEAF] operate-Equal[=] 是否末级:0=非末级 1=末级
	 */
	private Integer isLeaf;

	/**
	 * condition-field: 是否末级[IS_LEAF] operate-In[in] 是否末级:0=非末级 1=末级
	 */
	private Integer[] isLeafs;

	/**
	 * condition-field: 是否末级[IS_LEAF] operate-GTEqual[>=] 是否末级:0=非末级 1=末级
	 */
	private Integer startIsLeaf;

	/**
	 * condition-field: 是否末级[IS_LEAF] operate-LTEqual[<=] 是否末级:0=非末级 1=末级
	 */
	private Integer endIsLeaf;

	/**
	 * condition-field: 组织图标[ICON] operate-Like[like]
	 */
	private String icon;

	/**
	 * condition-field: 备注[REMARK] operate-Like[like]
	 */
	private String remark;

	/**
	 * condition-field: 所属域[DOMAIN_ID] operate-Equal[=]
	 */
	private Long domainId;

	/**
	 * condition-field: 所属域[DOMAIN_ID] operate-In[in]
	 */
	private Long[] domainIds;

	/**
	 * condition-field: 所属域[DOMAIN_ID] operate-GTEqual[>=]
	 */
	private Long startDomainId;

	/**
	 * condition-field: 所属域[DOMAIN_ID] operate-LTEqual[<=]
	 */
	private Long endDomainId;

	/**
	 * condition-field: 创建人[CREATOR] operate-Like[like]
	 */
	private String creator;

	/**
	 * condition-field: 创建人[CREATOR] operate-Equal[=]
	 */
	private String creatorEqual;

	/**
	 * condition-field: 创建人[CREATOR] operate-In[in]
	 */
	private String[] creators;

	/**
	 * condition-field: 修改人[MODIFIER] operate-Like[like]
	 */
	private String modifier;

	/**
	 * condition-field: 修改人[MODIFIER] operate-Equal[=]
	 */
	private String modifierEqual;

	/**
	 * condition-field: 修改人[MODIFIER] operate-In[in]
	 */
	private String[] modifiers;

	/**
	 * condition-field: 创建时间[CREATE_TIME] operate-Equal[=] 创建时间:yyyyMMddHHmmss
	 */
	private Long createTime;

	/**
	 * condition-field: 创建时间[CREATE_TIME] operate-In[in] 创建时间:yyyyMMddHHmmss
	 */
	private Long[] createTimes;

	/**
	 * condition-field: 创建时间[CREATE_TIME] operate-GTEqual[>=]
	 * 创建时间:yyyyMMddHHmmss
	 */
	private Long startCreateTime;

	/**
	 * condition-field: 创建时间[CREATE_TIME] operate-LTEqual[<=]
	 * 创建时间:yyyyMMddHHmmss
	 */
	private Long endCreateTime;

	/**
	 * condition-field: 修改时间[MODIFY_TIME] operate-Equal[=] 修改时间:yyyyMMddHHmmss
	 */
	private Long modifyTime;

	/**
	 * condition-field: 修改时间[MODIFY_TIME] operate-In[in] 修改时间:yyyyMMddHHmmss
	 */
	private Long[] modifyTimes;

	/**
	 * condition-field: 修改时间[MODIFY_TIME] operate-GTEqual[>=]
	 * 修改时间:yyyyMMddHHmmss
	 */
	private Long startModifyTime;

	/**
	 * condition-field: 修改时间[MODIFY_TIME] operate-LTEqual[<=]
	 * 修改时间:yyyyMMddHHmmss
	 */
	private Long endModifyTime;

	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long[] getIds() {
		return this.ids;
	}

	public void setIds(Long[] ids) {
		this.ids = ids;
	}

	public Long getStartId() {
		return this.startId;
	}

	public void setStartId(Long startId) {
		this.startId = startId;
	}

	public Long getEndId() {
		return this.endId;
	}

	public void setEndId(Long endId) {
		this.endId = endId;
	}

	public String getOrgCode() {
		return this.orgCode;
	}

	public void setOrgCode(String orgCode) {
		this.orgCode = orgCode;
	}

	public String getOrgCodeEqual() {
		return this.orgCodeEqual;
	}

	public void setOrgCodeEqual(String orgCodeEqual) {
		this.orgCodeEqual = orgCodeEqual;
	}

	public String[] getOrgCodes() {
		return this.orgCodes;
	}

	public void setOrgCodes(String[] orgCodes) {
		this.orgCodes = orgCodes;
	}

	public String getOrgName() {
		return this.orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	public Integer getOrgLevel() {
		return this.orgLevel;
	}

	public void setOrgLevel(Integer orgLevel) {
		this.orgLevel = orgLevel;
	}

	public Integer[] getOrgLevels() {
		return this.orgLevels;
	}

	public void setOrgLevels(Integer[] orgLevels) {
		this.orgLevels = orgLevels;
	}

	public Integer getStartOrgLevel() {
		return this.startOrgLevel;
	}

	public void setStartOrgLevel(Integer startOrgLevel) {
		this.startOrgLevel = startOrgLevel;
	}

	public Integer getEndOrgLevel() {
		return this.endOrgLevel;
	}

	public void setEndOrgLevel(Integer endOrgLevel) {
		this.endOrgLevel = endOrgLevel;
	}

	public Long getParentOrgId() {
		return this.parentOrgId;
	}

	public void setParentOrgId(Long parentOrgId) {
		this.parentOrgId = parentOrgId;
	}

	public Long[] getParentOrgIds() {
		return this.parentOrgIds;
	}

	public void setParentOrgIds(Long[] parentOrgIds) {
		this.parentOrgIds = parentOrgIds;
	}

	public Long getStartParentOrgId() {
		return this.startParentOrgId;
	}

	public void setStartParentOrgId(Long startParentOrgId) {
		this.startParentOrgId = startParentOrgId;
	}

	public Long getEndParentOrgId() {
		return this.endParentOrgId;
	}

	public void setEndParentOrgId(Long endParentOrgId) {
		this.endParentOrgId = endParentOrgId;
	}

	public String getOrgLvlPath() {
		return this.orgLvlPath;
	}

	public void setOrgLvlPath(String orgLvlPath) {
		this.orgLvlPath = orgLvlPath;
	}

	public Integer getOrderNo() {
		return this.orderNo;
	}

	public void setOrderNo(Integer orderNo) {
		this.orderNo = orderNo;
	}

	public Integer[] getOrderNos() {
		return this.orderNos;
	}

	public void setOrderNos(Integer[] orderNos) {
		this.orderNos = orderNos;
	}

	public Integer getStartOrderNo() {
		return this.startOrderNo;
	}

	public void setStartOrderNo(Integer startOrderNo) {
		this.startOrderNo = startOrderNo;
	}

	public Integer getEndOrderNo() {
		return this.endOrderNo;
	}

	public void setEndOrderNo(Integer endOrderNo) {
		this.endOrderNo = endOrderNo;
	}

	public Integer getIsLeaf() {
		return this.isLeaf;
	}

	public void setIsLeaf(Integer isLeaf) {
		this.isLeaf = isLeaf;
	}

	public Integer[] getIsLeafs() {
		return this.isLeafs;
	}

	public void setIsLeafs(Integer[] isLeafs) {
		this.isLeafs = isLeafs;
	}

	public Integer getStartIsLeaf() {
		return this.startIsLeaf;
	}

	public void setStartIsLeaf(Integer startIsLeaf) {
		this.startIsLeaf = startIsLeaf;
	}

	public Integer getEndIsLeaf() {
		return this.endIsLeaf;
	}

	public void setEndIsLeaf(Integer endIsLeaf) {
		this.endIsLeaf = endIsLeaf;
	}

	public String getIcon() {
		return this.icon;
	}

	public void setIcon(String icon) {
		this.icon = icon;
	}

	public String getRemark() {
		return this.remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public Long getDomainId() {
		return this.domainId;
	}

	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}

	public Long[] getDomainIds() {
		return this.domainIds;
	}

	public void setDomainIds(Long[] domainIds) {
		this.domainIds = domainIds;
	}

	public Long getStartDomainId() {
		return this.startDomainId;
	}

	public void setStartDomainId(Long startDomainId) {
		this.startDomainId = startDomainId;
	}

	public Long getEndDomainId() {
		return this.endDomainId;
	}

	public void setEndDomainId(Long endDomainId) {
		this.endDomainId = endDomainId;
	}

	public String getCreator() {
		return this.creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public String getCreatorEqual() {
		return this.creatorEqual;
	}

	public void setCreatorEqual(String creatorEqual) {
		this.creatorEqual = creatorEqual;
	}

	public String[] getCreators() {
		return this.creators;
	}

	public void setCreators(String[] creators) {
		this.creators = creators;
	}

	public String getModifier() {
		return this.modifier;
	}

	public void setModifier(String modifier) {
		this.modifier = modifier;
	}

	public String getModifierEqual() {
		return this.modifierEqual;
	}

	public void setModifierEqual(String modifierEqual) {
		this.modifierEqual = modifierEqual;
	}

	public String[] getModifiers() {
		return this.modifiers;
	}

	public void setModifiers(String[] modifiers) {
		this.modifiers = modifiers;
	}

	public Long getCreateTime() {
		return this.createTime;
	}

	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}

	public Long[] getCreateTimes() {
		return this.createTimes;
	}

	public void setCreateTimes(Long[] createTimes) {
		this.createTimes = createTimes;
	}

	public Long getStartCreateTime() {
		return this.startCreateTime;
	}

	public void setStartCreateTime(Long startCreateTime) {
		this.startCreateTime = startCreateTime;
	}

	public Long getEndCreateTime() {
		return this.endCreateTime;
	}

	public void setEndCreateTime(Long endCreateTime) {
		this.endCreateTime = endCreateTime;
	}

	public Long getModifyTime() {
		return this.modifyTime;
	}

	public void setModifyTime(Long modifyTime) {
		this.modifyTime = modifyTime;
	}

	public Long[] getModifyTimes() {
		return this.modifyTimes;
	}

	public void setModifyTimes(Long[] modifyTimes) {
		this.modifyTimes = modifyTimes;
	}

	public Long getStartModifyTime() {
		return this.startModifyTime;
	}

	public void setStartModifyTime(Long startModifyTime) {
		this.startModifyTime = startModifyTime;
	}

	public Long getEndModifyTime() {
		return this.endModifyTime;
	}

	public void setEndModifyTime(Long endModifyTime) {
		this.endModifyTime = endModifyTime;
	}

}
