package com.uino.api.client.sys.local;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.binary.core.io.Resource;
import com.uino.service.sys.microservice.ISysSvc;
import com.uino.api.client.sys.ISysApiSvc;

@Service
public class SysApiSvcLocal implements ISysApiSvc {

    @Autowired
    private ISysSvc svc;

    @Override
    public String uploadFile(MultipartFile file) {
        // TODO Auto-generated method stub
        return svc.uploadFile(file);
    }

    @Override
    public Resource downloadFile(String filePath) {
        return svc.downloadFile(filePath);
    }

    @Override
    public String uploadFile(byte[] fileBytes, String fileName) {
        // TODO Auto-generated method stub
        return svc.uploadFile(fileBytes, fileName);
    }
}
