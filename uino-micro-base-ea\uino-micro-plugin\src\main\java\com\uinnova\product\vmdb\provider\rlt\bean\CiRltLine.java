package com.uinnova.product.vmdb.provider.rlt.bean;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.vmdb.comm.model.rlt.CcCiRlt;

import java.io.Serializable;
import java.util.Map;

public class CiRltLine implements Serializable {

	private static final long serialVersionUID = 1L;

	@Comment("CI关系")
	private CcCiRlt ciRlt;

	@Comment("Rlt属性")
	private Map<String, String> attrs;
	
	public CiRltLine() {
	}
	
	public CiRltLine(CcCiRltInfo rltInfo) {
		this.ciRlt = rltInfo.getCiRlt();
		this.attrs = rltInfo.getAttrs();
	}

	public Long getId() {
		return ciRlt.getId();
	}
	
	public void setId(Long id){
		
	}
	
	public String getCiCode() {
		return ciRlt.getCiCode();
	}

	public void setCiCode(String ciCode) {
		// ciRlt.getciCode = ciCode;
	}

	public String getCiDesc() {
		return ciRlt.getCiDesc();
	}

	public void setCiDesc(String ciDesc) {
		// ciRlt.getciDesc = ciDesc;
	}

	public Long getClassId() {
		return ciRlt.getClassId();
	}

	public void setClassId(Long classId) {
	}

	public Long getSourceId() {
		return ciRlt.getSourceId();
	}

	public void setSourceId(Long sourceId) {
	}

	public String getOwnerCode() {
		return ciRlt.getOwnerCode();
	}

	public void setOwnerCode(String ownerCode) {
	}

	public Long getOrgId() {
		return ciRlt.getOrgId();
	}

	public void setOrgId(Long orgId) {
		// ciRlt.getorgId = orgId;
	}

	public Long getSource() {
		return ciRlt.getSourceCiId();
	}
	
	public void setSource(Long source) {
	}

//	public Long getSourceCiId() {
//		return ciRlt.getSourceCiId();
//	}
//
//	public void setSourceCiId(Long sourceCiId) {
//	}

	public String getSourceCiCode() {
		return ciRlt.getSourceCiCode();
	}

	public void setSourceCiCode(String sourceCiCode) {
	}

	public Long getSourceClassId() {
		return ciRlt.getSourceClassId();
	}

	public void setSourceClassId(Long sourceClassId) {
	}
	
//	public Long getTargetCiId() {
//		return ciRlt.getTargetCiId();
//	}
//	
//	public void setTargetCiId(Long targetCiId) {
//	}

	public Long getTarget() {
		return ciRlt.getTargetCiId();
	}

	public void setTarget(Long target) {
	}

	public String getTargetCiCode() {
		return ciRlt.getTargetCiCode();
	}

	public void setTargetCiCode(String targetCiCode) {
	}

	public Long getTargetClassId() {
		return ciRlt.getTargetClassId();
	}

	public void setTargetClassId(Long targetClassId) {
	}

	public Map<String, String> getAttrs() {
		return attrs;
	}

	public void setAttrs(Map<String, String> attrs) {
		this.attrs = attrs;
	}

	public CcCiRlt getCiRlt() {
		return ciRlt;
	}

	public void setCiRlt(CcCiRlt ciRlt) {
		this.ciRlt = ciRlt;
	}
	
	
	
}
