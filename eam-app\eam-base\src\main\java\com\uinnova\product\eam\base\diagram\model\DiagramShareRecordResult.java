package com.uinnova.product.eam.base.diagram.model;

import com.binary.framework.bean.annotation.Comment;
import com.uino.bean.permission.base.SysUser;

public class DiagramShareRecordResult {

    private Long id;

    private Long diagramId;

    private Long ownerId;

    private SysUser ownerSysUser;

    private Long sharedUserId;

    private SysUser sharedSysUser;

    private Integer permission;

    private VcDiagram vcDiagram;

    @Comment("记录创建时间[CREATE_TIME]    创建时间:yyyyMMddHHmmss")
    private Long diagramModifyTime;

    @Comment("记录更新时间[MODIFY_TIME]    更新时间:yyyyMMddHHmmss")
    private Long modifyTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getDiagramId() {
        return diagramId;
    }

    public void setDiagramId(Long diagramId) {
        this.diagramId = diagramId;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public Long getSharedUserId() {
        return sharedUserId;
    }

    public void setSharedUserId(Long sharedUserId) {
        this.sharedUserId = sharedUserId;
    }

    public Integer getPermission() {
        return permission;
    }

    public void setPermission(Integer permission) {
        this.permission = permission;
    }

    public VcDiagram getVcDiagram() {
        return vcDiagram;
    }

    public void setVcDiagram(VcDiagram vcDiagram) {
        this.vcDiagram = vcDiagram;
    }

    public SysUser getOwnerSysUser() {
        return ownerSysUser;
    }

    public void setOwnerSysUser(SysUser ownerSysUser) {
        this.ownerSysUser = ownerSysUser;
    }

    public SysUser getSharedSysUser() {
        return sharedSysUser;
    }

    public void setSharedSysUser(SysUser sharedSysUser) {
        this.sharedSysUser = sharedSysUser;
    }

    public Long getDiagramModifyTime() {
        return diagramModifyTime;
    }

    public void setDiagramModifyTime(Long diagramModifyTime) {
        this.diagramModifyTime = diagramModifyTime;
    }

    public Long getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }
}
