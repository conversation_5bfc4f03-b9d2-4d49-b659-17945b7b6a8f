package com.uinnova.product.eam.db.impl;


import com.uinnova.product.eam.comm.model.CVcDiagramDirRelation;
import com.uinnova.product.eam.comm.model.VcDiagramDirRelation;
import com.uinnova.product.eam.db.VcDiagramDirRelationDao;
import com.uinnova.product.eam.db.support.dao.mybatis.ComMyBatisBinaryDaoImpl;


/**
 * 视图目录关系表[VC_DIAGRAM_DIR_RELATION]数据访问对象实现
 */
public class VcDiagramDirRelationDaoImpl extends ComMyBatisBinaryDaoImpl<VcDiagramDirRelation, CVcDiagramDirRelation> implements VcDiagramDirRelationDao {

//    @Override
//    public String getTableName() {
//        return "IAMS." + getDaoDefinition().getTableName();
//    }
}


