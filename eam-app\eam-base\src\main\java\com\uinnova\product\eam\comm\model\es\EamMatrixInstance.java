package com.uinnova.product.eam.comm.model.es;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.io.Serializable;

/**
 * 矩阵表格
 * <AUTHOR>
 */
@Data
public class EamMatrixInstance implements Serializable {

    @Comment("主键")
    private Long id;

    @Comment("领域id")
    private Long domainId;

    @Comment("矩阵制品Id")
    private Long matrixId;

    @Comment("矩阵制品版本(发布时记录)")
    private Integer matrixVersion;

    @Comment("名称")
    private String name;

    @Comment("文件夹id")
    private Long dirId;

    @Comment("记录逻辑删除前文件夹id")
    private Long oldDirId;

    @Comment("发布版本")
    private Integer version;

    @Comment("发布路径文件夹id")
    private Long releasePath;

    @Comment("是否已发布: 1已发布,0未发布,2审批中")
    private Integer published;

    @Comment("发布Id,对应资产库矩阵表格ID")
    private Long publishId;

    @Comment("状态StatusConstant：1正常,0 已删除(架构设计)/历史版本(资产仓库)")
    private Integer status;

    @Comment("说明")
    private String description;

    @Comment("所属用户")
    private String ownerCode;

    @Comment("创建人")
    private String creator;

    @Comment("创建时间")
    private Long createTime;

    @Comment("修改人")
    private String modifier;

    @Comment("修改时间")
    private Long modifyTime;
}
