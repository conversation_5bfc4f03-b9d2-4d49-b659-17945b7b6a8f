package com.uinnova.product.eam.service;

import com.uinnova.product.eam.model.EamCiUpdateDto;
import com.uinnova.product.eam.model.diagram.ArtifactCheckDto;
import com.uinnova.product.eam.model.dm.DataModelBatchResp;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;

import java.util.List;

/**
 * 莫奈画布操作接口类
 * <AUTHOR>
 * @version 2020/10/28
 */
public interface ICanvasDiagramSvc {

    /**
     * 查询对象间存在的关系
     * @param from
     * @param to
     * @return
     */
    List<CcCiClassInfo> queryCiRltInfo(Long from, Long to);

    /**
     * 通过视图ID校验设计库CI对象是否有更新
     * @param diagramId
     * @param needDetail
     * @return
     */
    List<EamCiUpdateDto> checkCiInfoByDiagramId(String diagramId, Boolean needDetail);

    /**
     * 通过视图Id更新对象及关系数据
     * @param diagramId
     * @return
     */
    DataModelBatchResp updateCiInfoByDiagramId(String diagramId);

    /**
     * 通过制品校验视图关系是否合规
     * @param diagramId 视图id
     * @return 校验结果
     */
    List<ArtifactCheckDto> checkByArtifact(String diagramId);
}
