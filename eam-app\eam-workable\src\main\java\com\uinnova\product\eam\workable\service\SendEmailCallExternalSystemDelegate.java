package com.uinnova.product.eam.workable.service;

import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.JavaDelegate;

/**
 * 小栗子
 *
 * <AUTHOR>
 * @since 2022/2/22 17:55
 */
public class SendEmailCallExternalSystemDelegate implements JavaDelegate {
    @Override
    public void execute(DelegateExecution execution) {
        System.out.println("Calling the external system for employee "
                + execution.getVariable("employee"));
    }
}
