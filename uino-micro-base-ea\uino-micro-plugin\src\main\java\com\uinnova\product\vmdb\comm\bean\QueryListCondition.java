package com.uinnova.product.vmdb.comm.bean;

import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;

import java.io.Serializable;

/**
 * 
 * <AUTHOR>
 *
 * @param <E>
 */
@Comment("列表查询封装")
public class QueryListCondition<E extends Condition> implements Serializable {
    private static final long serialVersionUID = 1L;

    @Comment("查询条件")
    private E cdt;

    @Comment("排序字段")
    private String orders;

    public E getCdt() {
        return cdt;
    }

    public void setCdt(E cdt) {
        this.cdt = cdt;
    }

    public String getOrders() {
        return orders;
    }

    public void setOrders(String orders) {
        this.orders = orders;
    }

}
