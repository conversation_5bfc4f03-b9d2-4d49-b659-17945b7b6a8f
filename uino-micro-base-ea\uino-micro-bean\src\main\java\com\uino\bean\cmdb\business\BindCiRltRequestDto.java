package com.uino.bean.cmdb.business;

import com.uino.bean.permission.business.IValidDto;
import com.uino.bean.sys.base.RltSourceId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.springframework.util.Assert;

import java.util.Map;

/**
 * 创建ci关系
 * 
 * <AUTHOR>
 *
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value="保存ci关系类",description="ci关系信息")
public class BindCiRltRequestDto implements IValidDto {

	@ApiModelProperty(value = "关系id")
	private Long id;

	/**
	 * 发现重复是否Error
	 *
	 */
	@ApiModelProperty(value="发现重复是否错误",example="false")
	@Builder.Default
	private boolean repetitionError = false;
	/**
	 * 源ciId
	 */
	@ApiModelProperty(value="源对象Id",example="122")
	private Long sourceCiId;
	/**
	 * 目标ciId
	 */
	@ApiModelProperty(value ="目标对象Id",example="620")
	private Long targetCiId;

	@ApiModelProperty(value="源端ciCode")
	private String sourceCiCode;

	@ApiModelProperty(value="目标端ciCode")
	private String targetCiCode;

	@ApiModelProperty(value="视图id")
	private String diagramId;

	@ApiModelProperty(value="分页id")
	private String sheetId;

	/**
	 * ci关系所属分类
	 */
	@ApiModelProperty(value="ci关系所属分类",example = "123")
	private Long rltClassId;
	/**
	 * ci关系属性
	 */
	@ApiModelProperty(value="ci关系属性")
	private Map<String, String> attrs;

	private String ownerCode;

	/**
	 * 前端源端ci节点标识key
	 */
	private String sourceKey;

	/**
	 * 前端目标端ci节点标识key
	 */
	private String targetKey;

	/**
	 * 备用字段
	 */
	private String custom1;

	/**
	 * 断线重连参数
	 */
	private String oldCode;

	/**
	 * 数据来源枚举
	 */
	private RltSourceId rltSourceId;

	@Override
	public void valid() {
		Assert.notNull(this.getRltClassId(), "rltClassId not null");
	}
}
