package com.uinnova.product.eam.model;

import java.util.HashMap;

public class EamProcessBean {

    /**
     * 必传，流程定义key
     */
    String processDefinitionKey;
    /**
     * 业务id
     */
    String businessKey;

    /**
     * 任务名
     */
    String processInstanceName;

    /**
     * 任务领取人
     */
    String assignee;

    /**
     * taskId
     */
    String taskId;

    /**
     * 额外参数
     */
    HashMap<String,Object> variables;

    public String getProcessDefinitionKey() {
        return processDefinitionKey;
    }

    public void setProcessDefinitionKey(String processDefinitionKey) {
        this.processDefinitionKey = processDefinitionKey;
    }

    public String getProcessInstanceName() {
        return processInstanceName;
    }

    public void setProcessInstanceName(String processInstanceName) {
        this.processInstanceName = processInstanceName;
    }

    public String getAssignee() {
        return assignee;
    }

    public void setAssignee(String assignee) {
        this.assignee = assignee;
    }

    public String getBusinessKey() {
        return businessKey;
    }

    public void setBusinessKey(String businessKey) {
        this.businessKey = businessKey;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public HashMap<String, Object> getVariables() {
        return variables;
    }

    public void setVariables(HashMap<String, Object> variables) {
        this.variables = variables;
    }
}
