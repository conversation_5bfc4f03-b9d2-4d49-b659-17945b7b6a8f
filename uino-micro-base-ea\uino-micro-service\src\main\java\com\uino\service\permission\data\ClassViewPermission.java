package com.uino.service.permission.data;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 对象分类数据查看权限控制注解
 * <AUTHOR>
 */

@Target({ElementType.METHOD, ElementType.LOCAL_VARIABLE})
@Retention(RetentionPolicy.RUNTIME)
public @interface ClassViewPermission {
    //是否使用子集过滤
    boolean subset() default false;
    //选择过滤字段
    String field() default "id";
    //是否使用子对象中字段过滤
    boolean child() default false;
    //子对象名称
    String childName() default "ci";
}
