package com.uinnova.product.eam.model.asset;

import com.uinnova.product.vmdb.comm.model.ci.CcCi;
import com.uinnova.product.vmdb.comm.model.ci.CcCiClass;
import lombok.Data;

import java.util.Map;

@Data
public class SubsystemDTO {

    private String systemMasterCiId;

    private Long ciClassId;

    /**
     * 子系统的ci信息
     */
    private CcCi subsystemCi;

    /**
     * 子系统的属性
     */
    private Map<String,String> subsystemAttrs;

    /**
     * 非功能信息ci
     */
    private CcCi noFunctionalCi;

    /**
     * 非功能信息的属性
     */
    private Map<String,String> noFunctionalAttrs;

    /**
     * 系统的属性
     */
    private Map<String,Object> systemMasterAttrs;

    private CcCiClass ciClass;

    private Long newSystemMasterCiId;
}
