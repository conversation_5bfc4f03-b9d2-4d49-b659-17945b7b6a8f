package com.uino.web.cmdb.mvc;

import java.util.List;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import com.uino.init.api.ApiResult;
import com.uino.init.api.ApiVersion;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.binary.core.exception.MessageException;
import com.uino.api.client.cmdb.IDataSetCooperationApiSvc;
import com.uino.web.BaseMvc;

import io.swagger.annotations.ApiOperation;

/**
 * @Classname DataSetCooperationController
 * @Description 协作
 * @Date 2020/3/19 21:58
 * @Created by sh
 */
@ApiVersion(1)
@Api(value = "协作", tags = {"数据集"})
@RestController
@RequestMapping("cmdb/dataSet/cooperation")
public class DataSetCooperationMvc extends BaseMvc {

    @Autowired
    private IDataSetCooperationApiSvc dataSetCooperationApiSvc;

    @RequestMapping(value = {"/findByDataSetId"}, method = RequestMethod.POST)
    @ApiOperation("查询协作")
    public ApiResult<List<JSONObject>> findByDataSetId(HttpServletRequest request, HttpServletResponse response, @RequestBody String body) {
        JSONObject jsonObject = JSONObject.parseObject(body);
        Long id = jsonObject.getLong("id");
        List<JSONObject> list = dataSetCooperationApiSvc.findByDataSetId(id);
        return ApiResult.ok(this).data(list);
    }

    @RequestMapping(value = {"/updateByDataSetId"}, method = RequestMethod.POST)
    @ApiOperation("更新协作")
    public ApiResult<Boolean> updateByDataSetId(HttpServletRequest request, HttpServletResponse response, @RequestBody String body) {
        JSONObject jsonObject = JSONObject.parseObject(body);

        Long id = jsonObject.getLong("id");
        if (!JSONArray.isValidArray(jsonObject.getString("userList"))){
            throw MessageException.i18n("参数错误");
        }
        List<JSONObject> userList = JSONArray.parseArray(jsonObject.getString("userList"), JSONObject.class);
        dataSetCooperationApiSvc.updateCooperation(id,userList);
        return ApiResult.ok(this).data(true);
    }

}
