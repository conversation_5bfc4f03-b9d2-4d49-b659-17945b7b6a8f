package com.uinnova.product.eam.model.vo;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;


/**
 * 制品示意图
 * <AUTHOR>
 */
@Data
public class DefaultFileVo {

    @Comment("上传图片时返回的id")
    private Long fileId;

    @Comment("图片路径")
    private String resPath;

    @Comment("图片名称")
    private String imageName;

    @Comment("默认图片")
    private Boolean defaultImg = false;

    @Comment("创建时间")
    private Long createTime;
}
