package com.uino.bean.cmdb.query;

import com.uino.bean.permission.business.IValidDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.Assert;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 按照属性打组得ci查询
 * 
 * <AUTHOR>
 *
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AndAttrsQueryGroup implements Serializable, IValidDto {

    private static final long serialVersionUID = 1L;

    /**
     * 分类id
     */
	@ApiModelProperty(value = "分类id")
    private Long classId;

    /**
     * 属性之间是AND 关系
     */
	@ApiModelProperty(value = "属性查询(and关系)")
    @Builder.Default
    private List<List<ESAttrBean>> andAttrs = new ArrayList<>();

    @Override
    public void valid() {
        Assert.notNull(classId, "classId not null");
    }
}
