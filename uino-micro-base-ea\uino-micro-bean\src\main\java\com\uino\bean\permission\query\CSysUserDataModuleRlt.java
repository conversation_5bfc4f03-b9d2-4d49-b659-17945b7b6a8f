package com.uino.bean.permission.query;




import java.io.Serializable;


/**
 * condition-table: 用户与数据关联表[SYS_USER_DATA_MODULE_RLT]
 * 
 * <AUTHOR>
 *
 */
public class CSysUserDataModuleRlt implements Serializable {
	private static final long serialVersionUID = 1L;


	/**
	 * condition-field: ID[ID] operate-Equal[=]
	 */
	private Long id;


	/**
	 * condition-field: ID[ID] operate-In[in]
	 */
	private Long[] ids;


	/**
	 * condition-field: ID[ID] operate-GTEqual[>=]
	 */
	private Long startId;

	/**
	 * condition-field: ID[ID] operate-LTEqual[<=]
	 */
	private Long endId;


	/**
	 * condition-field: 数据模块代码(处理数据权限的类别)[DATA_MODULE_CODE] operate-Like[like]
	 * 模块名称
	 */
	private String dataModuleCode;


	/**
	 * condition-field: 唯一ID(与前台交互)[UID] operate-Like[like]
	 */
	private String uid;


	/**
	 * condition-field: 数据项值（表达式或实例ID）[DATA_VALUE] operate-Like[like]
	 * 模块描述
	 */
	private String dataValue;


	/**
	 * condition-field: 用户ID[USER_ID] operate-Equal[=]
	 * 所属域
	 */
	private Long userId;


	/**
	 * condition-field: 用户ID[USER_ID] operate-In[in]
	 * 所属域
	 */
	private Long[] userIds;


	/**
	 * condition-field: 用户ID[USER_ID] operate-GTEqual[>=]
	 * 所属域
	 */
	private Long startUserId;

	/**
	 * condition-field: 用户ID[USER_ID] operate-LTEqual[<=]
	 * 所属域
	 */
	private Long endUserId;


	/**
	 * condition-field: 删除权限[ISDELETE] operate-Equal[=]
	 * 创建人
	 */
	private Integer isdelete;


	/**
	 * condition-field: 删除权限[ISDELETE] operate-In[in]
	 * 创建人
	 */
	private Integer[] isdeletes;


	/**
	 * condition-field: 删除权限[ISDELETE] operate-GTEqual[>=]
	 * 创建人
	 */
	private Integer startIsdelete;

	/**
	 * condition-field: 删除权限[ISDELETE] operate-LTEqual[<=]
	 * 创建人
	 */
	private Integer endIsdelete;


	/**
	 * condition-field: 修改权限[ISUPDATE] operate-Equal[=]
	 * 创建人
	 */
	private Integer isupdate;


	/**
	 * condition-field: 修改权限[ISUPDATE] operate-In[in]
	 * 创建人
	 */
	private Integer[] isupdates;


	/**
	 * condition-field: 修改权限[ISUPDATE] operate-GTEqual[>=]
	 * 创建人
	 */
	private Integer startIsupdate;

	/**
	 * condition-field: 修改权限[ISUPDATE] operate-LTEqual[<=]
	 * 创建人
	 */
	private Integer endIsupdate;


	/**
	 * condition-field: 创建权限[ISCREATE] operate-Equal[=]
	 * 创建人
	 */
	private Integer iscreate;


	/**
	 * condition-field: 创建权限[ISCREATE] operate-In[in]
	 * 创建人
	 */
	private Integer[] iscreates;


	/**
	 * condition-field: 创建权限[ISCREATE] operate-GTEqual[>=]
	 * 创建人
	 */
	private Integer startIscreate;

	/**
	 * condition-field: 创建权限[ISCREATE] operate-LTEqual[<=]
	 * 创建人
	 */
	private Integer endIscreate;


	/**
	 * condition-field: 查看权限[ISSEE] operate-Equal[=]
	 * 创建人
	 */
	private Integer issee;


	/**
	 * condition-field: 查看权限[ISSEE] operate-In[in]
	 * 创建人
	 */
	private Integer[] issees;


	/**
	 * condition-field: 查看权限[ISSEE] operate-GTEqual[>=]
	 * 创建人
	 */
	private Integer startIssee;

	/**
	 * condition-field: 查看权限[ISSEE] operate-LTEqual[<=]
	 * 创建人
	 */
	private Integer endIssee;


	/**
	 * condition-field: 创建人[CREATOR] operate-Like[like]
	 */
	private String creator;


	/**
	 * condition-field: 创建人[CREATOR] operate-Equal[=]
	 */
	private String creatorEqual;


	/**
	 * condition-field: 创建人[CREATOR] operate-In[in]
	 */
	private String[] creators;


	/**
	 * condition-field: 修改人[MODIFIER] operate-Like[like]
	 */
	private String modifier;


	/**
	 * condition-field: 修改人[MODIFIER] operate-Equal[=]
	 */
	private String modifierEqual;


	/**
	 * condition-field: 修改人[MODIFIER] operate-In[in]
	 */
	private String[] modifiers;


	/**
	 * condition-field: 所属域[DOMAIN_ID] operate-Equal[=]
	 */
	private Long domainId;


	/**
	 * condition-field: 所属域[DOMAIN_ID] operate-In[in]
	 */
	private Long[] domainIds;


	/**
	 * condition-field: 所属域[DOMAIN_ID] operate-GTEqual[>=]
	 */
	private Long startDomainId;

	/**
	 * condition-field: 所属域[DOMAIN_ID] operate-LTEqual[<=]
	 */
	private Long endDomainId;


	/**
	 * condition-field: 创建时间[CREATE_TIME] operate-Equal[=]
	 * 创建时间:yyyyMMddHHmmss
	 */
	private Long createTime;


	/**
	 * condition-field: 创建时间[CREATE_TIME] operate-In[in]
	 * 创建时间:yyyyMMddHHmmss
	 */
	private Long[] createTimes;


	/**
	 * condition-field: 创建时间[CREATE_TIME] operate-GTEqual[>=]
	 * 创建时间:yyyyMMddHHmmss
	 */
	private Long startCreateTime;

	/**
	 * condition-field: 创建时间[CREATE_TIME] operate-LTEqual[<=]
	 * 创建时间:yyyyMMddHHmmss
	 */
	private Long endCreateTime;


	/**
	 * condition-field: 修改时间[MODIFY_TIME] operate-Equal[=]
	 * 修改时间:yyyyMMddHHmmss
	 */
	private Long modifyTime;


	/**
	 * condition-field: 修改时间[MODIFY_TIME] operate-In[in]
	 * 修改时间:yyyyMMddHHmmss
	 */
	private Long[] modifyTimes;


	/**
	 * condition-field: 修改时间[MODIFY_TIME] operate-GTEqual[>=]
	 * 修改时间:yyyyMMddHHmmss
	 */
	private Long startModifyTime;

	/**
	 * condition-field: 修改时间[MODIFY_TIME] operate-LTEqual[<=]
	 * 修改时间:yyyyMMddHHmmss
	 */
	private Long endModifyTime;




	public Long getId() {
		return this.id;
	}
	public void setId(Long id) {
		this.id = id;
	}


	public Long[] getIds() {
		return this.ids;
	}
	public void setIds(Long[] ids) {
		this.ids = ids;
	}


	public Long getStartId() {
		return this.startId;
	}
	public void setStartId(Long startId) {
		this.startId = startId;
	}


	public Long getEndId() {
		return this.endId;
	}
	public void setEndId(Long endId) {
		this.endId = endId;
	}


	public String getDataModuleCode() {
		return this.dataModuleCode;
	}
	public void setDataModuleCode(String dataModuleCode) {
		this.dataModuleCode = dataModuleCode;
	}


	public String getUid() {
		return this.uid;
	}
	public void setUid(String uid) {
		this.uid = uid;
	}


	public String getDataValue() {
		return this.dataValue;
	}
	public void setDataValue(String dataValue) {
		this.dataValue = dataValue;
	}


	public Long getUserId() {
		return this.userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}


	public Long[] getUserIds() {
		return this.userIds;
	}
	public void setUserIds(Long[] userIds) {
		this.userIds = userIds;
	}


	public Long getStartUserId() {
		return this.startUserId;
	}
	public void setStartUserId(Long startUserId) {
		this.startUserId = startUserId;
	}


	public Long getEndUserId() {
		return this.endUserId;
	}
	public void setEndUserId(Long endUserId) {
		this.endUserId = endUserId;
	}


	public Integer getIsdelete() {
		return this.isdelete;
	}
	public void setIsdelete(Integer isdelete) {
		this.isdelete = isdelete;
	}


	public Integer[] getIsdeletes() {
		return this.isdeletes;
	}
	public void setIsdeletes(Integer[] isdeletes) {
		this.isdeletes = isdeletes;
	}


	public Integer getStartIsdelete() {
		return this.startIsdelete;
	}
	public void setStartIsdelete(Integer startIsdelete) {
		this.startIsdelete = startIsdelete;
	}


	public Integer getEndIsdelete() {
		return this.endIsdelete;
	}
	public void setEndIsdelete(Integer endIsdelete) {
		this.endIsdelete = endIsdelete;
	}


	public Integer getIsupdate() {
		return this.isupdate;
	}
	public void setIsupdate(Integer isupdate) {
		this.isupdate = isupdate;
	}


	public Integer[] getIsupdates() {
		return this.isupdates;
	}
	public void setIsupdates(Integer[] isupdates) {
		this.isupdates = isupdates;
	}


	public Integer getStartIsupdate() {
		return this.startIsupdate;
	}
	public void setStartIsupdate(Integer startIsupdate) {
		this.startIsupdate = startIsupdate;
	}


	public Integer getEndIsupdate() {
		return this.endIsupdate;
	}
	public void setEndIsupdate(Integer endIsupdate) {
		this.endIsupdate = endIsupdate;
	}


	public Integer getIscreate() {
		return this.iscreate;
	}
	public void setIscreate(Integer iscreate) {
		this.iscreate = iscreate;
	}


	public Integer[] getIscreates() {
		return this.iscreates;
	}
	public void setIscreates(Integer[] iscreates) {
		this.iscreates = iscreates;
	}


	public Integer getStartIscreate() {
		return this.startIscreate;
	}
	public void setStartIscreate(Integer startIscreate) {
		this.startIscreate = startIscreate;
	}


	public Integer getEndIscreate() {
		return this.endIscreate;
	}
	public void setEndIscreate(Integer endIscreate) {
		this.endIscreate = endIscreate;
	}


	public Integer getIssee() {
		return this.issee;
	}
	public void setIssee(Integer issee) {
		this.issee = issee;
	}


	public Integer[] getIssees() {
		return this.issees;
	}
	public void setIssees(Integer[] issees) {
		this.issees = issees;
	}


	public Integer getStartIssee() {
		return this.startIssee;
	}
	public void setStartIssee(Integer startIssee) {
		this.startIssee = startIssee;
	}


	public Integer getEndIssee() {
		return this.endIssee;
	}
	public void setEndIssee(Integer endIssee) {
		this.endIssee = endIssee;
	}


	public String getCreator() {
		return this.creator;
	}
	public void setCreator(String creator) {
		this.creator = creator;
	}


	public String getCreatorEqual() {
		return this.creatorEqual;
	}
	public void setCreatorEqual(String creatorEqual) {
		this.creatorEqual = creatorEqual;
	}


	public String[] getCreators() {
		return this.creators;
	}
	public void setCreators(String[] creators) {
		this.creators = creators;
	}


	public String getModifier() {
		return this.modifier;
	}
	public void setModifier(String modifier) {
		this.modifier = modifier;
	}


	public String getModifierEqual() {
		return this.modifierEqual;
	}
	public void setModifierEqual(String modifierEqual) {
		this.modifierEqual = modifierEqual;
	}


	public String[] getModifiers() {
		return this.modifiers;
	}
	public void setModifiers(String[] modifiers) {
		this.modifiers = modifiers;
	}


	public Long getDomainId() {
		return this.domainId;
	}
	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}


	public Long[] getDomainIds() {
		return this.domainIds;
	}
	public void setDomainIds(Long[] domainIds) {
		this.domainIds = domainIds;
	}


	public Long getStartDomainId() {
		return this.startDomainId;
	}
	public void setStartDomainId(Long startDomainId) {
		this.startDomainId = startDomainId;
	}


	public Long getEndDomainId() {
		return this.endDomainId;
	}
	public void setEndDomainId(Long endDomainId) {
		this.endDomainId = endDomainId;
	}


	public Long getCreateTime() {
		return this.createTime;
	}
	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}


	public Long[] getCreateTimes() {
		return this.createTimes;
	}
	public void setCreateTimes(Long[] createTimes) {
		this.createTimes = createTimes;
	}


	public Long getStartCreateTime() {
		return this.startCreateTime;
	}
	public void setStartCreateTime(Long startCreateTime) {
		this.startCreateTime = startCreateTime;
	}


	public Long getEndCreateTime() {
		return this.endCreateTime;
	}
	public void setEndCreateTime(Long endCreateTime) {
		this.endCreateTime = endCreateTime;
	}


	public Long getModifyTime() {
		return this.modifyTime;
	}
	public void setModifyTime(Long modifyTime) {
		this.modifyTime = modifyTime;
	}


	public Long[] getModifyTimes() {
		return this.modifyTimes;
	}
	public void setModifyTimes(Long[] modifyTimes) {
		this.modifyTimes = modifyTimes;
	}


	public Long getStartModifyTime() {
		return this.startModifyTime;
	}
	public void setStartModifyTime(Long startModifyTime) {
		this.startModifyTime = startModifyTime;
	}


	public Long getEndModifyTime() {
		return this.endModifyTime;
	}
	public void setEndModifyTime(Long endModifyTime) {
		this.endModifyTime = endModifyTime;
	}


}


