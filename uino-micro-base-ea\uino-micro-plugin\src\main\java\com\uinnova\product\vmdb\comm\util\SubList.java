package com.uinnova.product.vmdb.comm.util;

import com.binary.core.util.BinaryUtils;

import java.util.List;

/**
 * 
 * <AUTHOR>
 *
 */
public class SubList {

    /**
     * 将数据进行分页
     * 
     * @param datas
     * @param pageSize
     * @param handler
     */
    public static <T> void subList(List<T> datas, int pageSize, SubListHandler<T> handler) {
        if (BinaryUtils.isEmpty(datas) || handler == null) {
            return;
        }
        pageSize = pageSize <= 0 ? 100 : pageSize;
        if (datas.size() <= pageSize) {
            handler.handler(datas);
            return;
        }

        int pageNum = datas.size() / pageSize;
        pageNum = datas.size() % pageSize == 0 ? pageNum : pageNum + 1;

        for (int i = 0; i < pageNum; i++) {
            int fromIndex = i * pageSize;
            int toIndex = fromIndex + pageSize;
            toIndex = toIndex > datas.size() ? datas.size() : toIndex;
            List<T> subList = datas.subList(fromIndex, toIndex);
            handler.handler(subList);
        }
    }

}
