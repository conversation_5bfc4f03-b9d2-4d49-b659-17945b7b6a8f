package com.uinnova.product.eam.web.bm.bean;

import com.binary.framework.bean.annotation.Comment;
import com.uino.bean.cmdb.base.LibType;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class ReleaseModuleDiagramDTO {
    private String diagramId;
    private String viewType;
    private Long dirType;
    private String ownerCode;
    private String assetCode;

    @Comment("检出我的空间位置")
    private Long targetDirId;

    @Comment("检出类型 1-视图 2-模型")
    private Integer type;

    @Comment("三库标识")
    private LibType libType;

    @Comment("发布说明")
    private String releaseDesc;

    @Comment("资产仓库目录文件id")
    private Long dirId;

    @Comment("视图名称集合")
    private List<String> names;

    @Comment("组件建模指定发布到的视图id")
    private String releaseDiagramId;

    @Comment("批量发布ids")
    private List<String> diagramIds;

    @Comment("业务主键ids")
    private String prepareId;

    @Comment("检出类型 1-检出 2-检出另存为")
    private Integer actionType;

    @Comment("视图名称")
    private String diagramName;

    @Comment("视图版本号")
    private Integer releaseVersion;

    @Comment("视图版本号列表")
    private List<Integer> releaseVersions;

    @Comment("视图要发布到资产仓库的位置")
    private Map<String, Long> puiblishDirSite;

    @Comment("CI业务主键")
    private List<String> ciPrimaryKeys;

    @Comment("sheet页ID")
    private String sheetId;

    @Comment("业务领域ciCode")
    private String targetCiCode;

    @Comment("是否分享")
    private Boolean shareFlag = Boolean.FALSE;

    @Comment("是否进行审批")
    private Boolean needApprove = Boolean.TRUE;
}
