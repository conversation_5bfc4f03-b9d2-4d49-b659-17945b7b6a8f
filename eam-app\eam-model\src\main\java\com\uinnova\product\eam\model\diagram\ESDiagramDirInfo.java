package com.uinnova.product.eam.model.diagram;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.base.diagram.model.ESSimpleDiagramDTO;
import com.uinnova.product.eam.comm.model.es.EamDiagramDir;
import com.uinnova.product.eam.model.vo.EamDiagramDirVo;
import lombok.Data;
import java.io.Serializable;
import java.util.List;
import java.util.Map;
import com.binary.jdbc.Page;

@Data
@Comment("查询文件夹和视图信息")
public class ESDiagramDirInfo implements Serializable {
	private static final long serialVersionUID = 1L;

	@Comment("当前文件夹信息")
	private EamDiagramDir diagramDir;
	
	@Comment("子文件夹信息")
	private List<EamDiagramDirVo> childrenDirs;
	
	@Comment("子文件夹下是否有节点")
	private Map<Long,Boolean> dirHasNodeMap;
	
	@Comment("子文件夹视图数量信息")
	private Map<Long,Integer> dirDiagramCountMap;

	@Comment("视图信息，不分页时使用")
	private List<ESSimpleDiagramDTO> diagramInfos;
	
	@Comment("视图信息，分页时使用")
	private Page<ESSimpleDiagramDTO> diagramInfoPage;

	@Comment("错误状态码")
	private int errorCode;
}
