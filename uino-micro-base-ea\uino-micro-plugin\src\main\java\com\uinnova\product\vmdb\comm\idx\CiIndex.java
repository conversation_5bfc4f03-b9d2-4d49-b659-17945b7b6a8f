package com.uinnova.product.vmdb.comm.idx;

import com.uinnova.product.vmdb.comm.expression.Expression;
import com.uinnova.product.vmdb.comm.expression.support.DefaultFieldDefinition;
import com.uinnova.product.vmdb.comm.expression.support.FieldOperator;
import com.uinnova.product.vmdb.comm.expression.support.VarcharField;

import java.io.Serializable;

/**
 * CI索引字段
 * 
 * <AUTHOR>
 */
public class CiIndex implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 全属性索引 **/
    public static final CiIndex PROS = new CiIndex("IDX_0", "idx0");

    private VarcharField<CiIndex> field;

    private CiIndex(String name, String mapping) {
        this.field = new VarcharField<CiIndex>(new DefaultFieldDefinition<CiIndex>(name, mapping));
    }

    /**
     * 等于
     */
    public Expression<CiIndex> EQ(String value) {
        value = repacle(value);
        return FieldOperator.operator.equal(this.field, value);
    }

    /**
     * 不等于
     */
    public Expression<CiIndex> NEQ(String value) {
        value = repacle(value);
        return FieldOperator.operator.notEqual(this.field, value);
    }

    /**
     * 小于
     */
    public Expression<CiIndex> LT(String value) {
        value = repacle(value);
        return FieldOperator.operator.less(this.field, value);
    }

    /**
     * 小于或等于
     */
    public Expression<CiIndex> LTEQ(String value) {
        value = repacle(value);
        return FieldOperator.operator.lessEqual(this.field, value);
    }

    /**
     * 大于
     */
    public Expression<CiIndex> GT(String value) {
        value = repacle(value);
        return FieldOperator.operator.greater(this.field, value);
    }

    /**
     * 大于或等于
     */
    public Expression<CiIndex> GTEQ(String value) {
        value = repacle(value);
        return FieldOperator.operator.greaterEqual(this.field, value);
    }

    /**
     * 模糊匹配...
     */
    public Expression<CiIndex> LIKE(String value) {
        value = repacle(value);
        return FieldOperator.operator.like(this.field, value);
    }

    /**
     * 字符串包含
     */
    public Expression<CiIndex> INSTR(String value) {
        value = repacle(value);
        return FieldOperator.operator.instr(this.field, value);
    }

    /**
     * 包含...
     */
    public Expression<CiIndex> IN(String[] values) {
        values = repalce(values);
        return FieldOperator.operator.in(this.field, values);
    }

    /**
     * 不包含...
     */
    public Expression<CiIndex> NIN(String[] values) {
        values = repalce(values);
        return FieldOperator.operator.notIn(this.field, values);
    }

    public String[] repalce(String[] values) {
        if (values == null) {
            return null;
        }
        String[] ret = new String[values.length];
        for (int i = 0; i < ret.length; i++) {
            ret[i] = repacle(values[i]);
        }
        return ret;
    }

    public String repacle(String value) {
        if (value == null) {
            return value;
        }
        return value.replace("'", "");
    }

}
