package com.uinnova.product.eam.service.cj.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.binary.core.util.BinaryUtils;
import com.uinnova.product.eam.comm.model.es.EamCategory;
import com.uinnova.product.eam.model.cj.domain.PlanDesignInstance;
import com.uinnova.product.eam.model.cj.enums.PlanStatusEnum;
import com.uinnova.product.eam.model.cj.vo.DlvrTemplateVO;
import com.uinnova.product.eam.model.constants.Constants;
import com.uinnova.product.eam.service.EamCategorySvc;
import com.uinnova.product.eam.service.cj.dao.PlanDesignInstanceDao;
import com.uinnova.product.eam.service.cj.service.DeliverableTemplateService;
import com.uinnova.product.eam.service.cj.service.PlanInstanceServiceExtend;
import com.uino.bean.cmdb.base.LibType;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

/**
 * 方案操作扩展
 * <AUTHOR>
 */
@Slf4j
@Service
public class PlanInstanceServiceImplExtend implements PlanInstanceServiceExtend {

    @Resource
    private EamCategorySvc categorySvc;
    @Resource
    private PlanDesignInstanceDao planDesignInstanceDao;
    @Autowired
    private DeliverableTemplateService deliverableTemplateService;

    @Override
    public void getPlanReleaseDirInfo(PlanDesignInstance plan) {
        //判断方案发布位置
        DlvrTemplateVO template = null;
        if (plan.getTemplateId() != null) {
            template = deliverableTemplateService.getDlvrTemplateById(plan.getTemplateId());
        }
        this.getPlanReleaseDirInfo(plan, template);
    }

    @Override
    public void getPlanReleaseDirInfo(PlanDesignInstance plan, DlvrTemplateVO template){
        //不处理资产仓库的方案
        if(Constants.ASSETS.equals(plan.getAssetsType())){
            return;
        }
        if(BinaryUtils.isEmpty(plan.getAssetsDirId())){
            plan.setAssetsDirId(-1L);
        }else{
            //校验一次文件夹是否存在
            EamCategory category = categorySvc.getById(plan.getAssetsDirId(), LibType.DESIGN);
            if(category == null){
                plan.setAssetsDirId(-1L);
            }
        }
        if(!BinaryUtils.isEmpty(plan.getBusinessKey())){
            //已发布的,找资产对应方案位置
            PlanDesignInstance assetPlan = getPlanPublishByBusinessKey(plan.getBusinessKey());
            if(assetPlan != null){
                EamCategory category = categorySvc.getById(assetPlan.getAssetsDirId(), LibType.DESIGN);
                if(category != null){
                    plan.setAssetsDirId(assetPlan.getAssetsDirId());
                }
            }else if(template != null && !BinaryUtils.isEmpty(template.getAssetsDirId()) && plan.getAssetsDirId().equals(-1L)){
                plan.setAssetsDirId(template.getAssetsDirId());
            }
        }else if(template != null && plan.getAssetsDirId().equals(-1L) && !BinaryUtils.isEmpty(template.getAssetsDirId())){
            plan.setAssetsDirId(template.getAssetsDirId());
        }
        //如果未发布,查看发布位置是否失效
        String echoName = categorySvc.getDirPathName(plan.getAssetsDirId(), LibType.DESIGN);
        if (BinaryUtils.isEmpty(echoName)) {
            plan.setAssetsDirId(null);
            plan.setDomainDirId(null);
        }
        plan.setEchoDirName("/".equals(echoName)?null:echoName);
        JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(plan, SerializerFeature.WriteMapNullValue));
        planDesignInstanceDao.saveOrUpdate(jsonObject, true);
    }

    public PlanDesignInstance getPlanPublishByBusinessKey(String businessKey) {
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.must(QueryBuilders.termQuery("status.keyword", PlanStatusEnum.published.name()));
        queryBuilder.must(QueryBuilders.termQuery("assetsType", Constants.ASSETS));
        queryBuilder.must(QueryBuilders.termQuery("businessKey.keyword", businessKey));
        return planDesignInstanceDao.selectOne(queryBuilder);
    }
}
