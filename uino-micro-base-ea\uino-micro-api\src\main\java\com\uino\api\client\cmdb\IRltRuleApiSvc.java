package com.uino.api.client.cmdb;

import java.util.List;

import com.binary.jdbc.Page;
import com.uino.bean.cmdb.base.ESRltRuleInfo;

/**
 * 关系遍历
 *
 * <AUTHOR>
 * @data 2019/7/31 11:18.
 */
public interface IRltRuleApiSvc {
    /**
     * 保存或更新CI
     *
     * @param rltRuleInfo 规则
     * @return
     */
    Long saveOrUpdate(ESRltRuleInfo rltRuleInfo);

    /**
     * 根据id删除CI
     *
     * @param id id
     * @return
     */
    Integer deleteById(Long id);


    /**
     * 根据id查询规则信息
     *
     * @param id id
     * @return
     */
    ESRltRuleInfo queryInfoById(Long id);

    /**
     * 不分页查询规则数据列表
     * 
     * @param domainId
     * @return 规则列表
     */
    List<ESRltRuleInfo> queryInfo(Long domainId);

    /**
     * 分页查询规则
     *
     * @param pageNum  pageNum
     * @param pageSize pageSize
     * @param name     name匹配
     * @param domainId domainId
     * @param orders   orders
     * @param isAsc    isAsc
     * @return
     */
    Page<ESRltRuleInfo> queryInfoPage(Integer pageNum, Integer pageSize, String name, Long domainId, String orders, boolean isAsc);

}
