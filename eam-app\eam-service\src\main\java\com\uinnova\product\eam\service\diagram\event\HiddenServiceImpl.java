package com.uinnova.product.eam.service.diagram.event;

import com.uinnova.product.eam.model.diagram.event.RuleParams;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.List;

@Service
public class HiddenServiceImpl implements IHiddenService {

    @Resource
    List<IHiddenEventListener> hiddenEventListeners;

    @Override
    public void process(RuleParams params) {
        for (IHiddenEventListener hiddenEventListener : hiddenEventListeners) {
            if(hiddenEventListener.getDiagramRule() != params.getRuleType()){
                continue;
            }
            hiddenEventListener.process(params);
        }
    }
}
