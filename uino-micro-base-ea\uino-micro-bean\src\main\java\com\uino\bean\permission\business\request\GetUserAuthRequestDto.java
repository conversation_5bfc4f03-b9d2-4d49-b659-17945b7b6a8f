package com.uino.bean.permission.business.request;

import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.util.Assert;

import com.uino.bean.permission.business.IValidDto;

import lombok.Data;

/**
 * 获取用户拥有权限请求数据传输类
 * 
 * <AUTHOR>
 *
 */
@Data
@ApiModel(value="获取用户拥有权限请求数据传输类",description = "获取用户拥有权限请求数据类")
public class GetUserAuthRequestDto implements IValidDto {
	/**
	 * 用户id
	 */
	@ApiModelProperty(value="用户id",example = "123")
	private Long userId;
	/**
	 * 筛选的模块
	 */
	@ApiModelProperty(value="筛选的模块")
	private List<String> moduleCodes;

	@Override
	public void valid() {
		Assert.notNull(userId, "X_PARAM_NOT_NULL#{BS_MNAME_USERID}");
	}

}
