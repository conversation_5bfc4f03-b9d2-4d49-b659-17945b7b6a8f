package com.uino.bean.cmdb.base;

import com.binary.framework.exception.ServiceException;

/**
 * @Title: ESPropertyType
 * @Description: ESPropertyType
 * @Author: YGQ
 * @Create: 2021-07-27 00:00
 **/

public enum ESPropertyType {

    /**
     * INTEGER
     */
    INTEGER(1, "CC_CI_INT_ATTR_", "IV_", 1),
    /**
     * DOUBLE
     */
    DOUBLE(2, "CC_CI_DOUB_ATTR_", "DV_", 2),
    /**
     * VARCHAR
     */
    VARCHAR(3, "CC_CI_SHORT_ATTR_", "SV_", 3),
    /**
     * LONG_VARCHAR
     */
    LONG_VARCHAR(4, "CC_CI_LONG_ATTR_", "LV_", 4),
    /**
     * CLOB
     */
    CLOB(5, "CC_CI_BIG_ATTR_", "BV_", 5),
    /**
     * ENUM
     */
    ENUM(6, "CC_CI_SHORT_ATTR_", "SV_", 3),
    /**
     * DATE
     */
    DATE(7, "CC_CI_SHORT_ATTR_", "SV_", 3),
    /**
     * DICT
     */
    DICT(8, "CC_CI_SHORT_ATTR_", "SV_", 3),
    /**
     * MODEL
     */
    MODEL(9, "CC_CI_SHORT_ATTR_", "SV_", 3),
    /**
     * PICTURE
     */
    PICTURE(10, "CC_CI_SHORT_ATTR_", "SV_", 5),
    /**
     * EXTERNAL_ATTR
     */
    EXTERNAL_ATTR(11, "CC_CI_SHORT_ATTR_", "SV_", 3),
    /**
     * DOCUMENT
     */
    DOCUMENT(12, "CC_CI_SHORT_ATTR_", "SV_", 5),
    INTERFACE(13,"CC_CI_SHORT_ATTR_", "SV_", 3),

    ATTACHMENT(14,"CC_CI_SHORT_ATTR_", "SV_", 5),

    PERSION(15, "CC_CI_SHORT_ATTR_", "SV_", 3),
    /**
     * CALCULATE 计算属性
     */
    CALCULATE(16, "CC_CI_SHORT_ATTR_", "SV_", 3),
    /**
     * PERCENT 百分比属性
     */
    PERCENT(17, "CC_CI_SHORT_ATTR_", "SV_", 3),

    LINK_CI(18, "CC_CI_SHORT_ATTR_", "SV_", 3),

    LINK_PLAN(19, "CC_CI_SHORT_ATTR_", "SV_", 3),

    INTEGER_CODE(20, "CC_CI_INT_ATTR_", "IV_", 1),

    PREFIX_INTEGER_CODE(21, "CC_CI_INT_ATTR_", "IV_", 1),

    ENCODE(150,"CC_CI_SHORT_ATTR_", "SV_", 3),
    /**
     * 机构
     */
    ORGANIZATION(160, "CC_CI_SHORT_ATTR_", "SV_", 3);

    private int v;
    private String tablePrefix;
    private String fieldPrefix;
    private int shardingType;

    private ESPropertyType(int v, String tablePrefix, String fieldPrefix, int shardingType) {
        this.v = v;
        this.tablePrefix = tablePrefix;
        this.fieldPrefix = fieldPrefix;
        this.shardingType = shardingType;
    }

    public int getValue() {
        return this.v;
    }

    public String getTablePrefix() {
        return this.tablePrefix;
    }

    public String getFieldPrefix() {
        return this.fieldPrefix;
    }

    public int getShardingType() {
        return this.shardingType;
    }

    public static ESPropertyType valueOf(int v) {
        switch (v) {
            case 1:
                return INTEGER;
            case 2:
                return DOUBLE;
            case 3:
                return VARCHAR;
            case 4:
                return LONG_VARCHAR;
            case 5:
                return CLOB;
            case 6:
                return ENUM;
            case 7:
                return DATE;
            case 8:
                return DICT;
            case 9:
                return MODEL;
            case 10:
                return PICTURE;
            case 11:
                return EXTERNAL_ATTR;
            case 12:
                return DOCUMENT;
            case 13:
                return INTERFACE;
            case 14:
                return ATTACHMENT;
            case 15:
                return PERSION;
            case 16:
                return CALCULATE;
            case 17:
                return PERCENT;
            case 18:
                return LINK_CI;
            case 19:
                return LINK_PLAN;
            case 20:
                return INTEGER_CODE;
            case 21:
                return PREFIX_INTEGER_CODE;
            case 150:
                return ENCODE;
            case 160:
                return ORGANIZATION;
            default:
                throw new ServiceException(" is wrong PropertyType-value '" + v + "'! ");
        }
    }
}
