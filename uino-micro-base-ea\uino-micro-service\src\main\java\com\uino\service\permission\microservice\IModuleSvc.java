package com.uino.service.permission.microservice;

import java.util.List;
import java.util.Map;
import java.util.Set;

import com.uino.bean.permission.base.SysModule;
import com.uino.bean.permission.business.ModuleNodeInfo;
import com.uino.bean.permission.query.CAuthModuleBean;
import com.uino.bean.permission.query.CSysModule;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 */
public interface IModuleSvc {

    /**
     * 获取菜单tree结构
     *
     * @param userId 为空则获取所有tree；不为空则获取该用户有权限的菜单tree
     * @return
     */
    public ModuleNodeInfo getModuleTree(Long domainId, Long userId);
    /**
     * 根据权限标识获取下级菜单树结构
     *
     * @param userId 为空则获取所有tree；不为空则获取该用户有权限的菜单tree
     * @return
     */
    ModuleNodeInfo getModuleTreeBySign(Long domainId, Long userId, String moduleSign);

    /**
     * 保存模块
     *
     * @param saveDto
     * @return
     */
    public SysModule saveModule(SysModule saveDto);

    /**
     * 删除模块
     *
     * @param id
     */
    public void delModule(Long id);

    /**
     * 保存排序
     *
     * @param orderDict
     */
    public void saveOrder(Map<Long, Integer> orderDict);

    /**
     * 恢复模块信息
     *
     * @param moduleIds 要还原模块的ids(只能是初始化类型的) if empty 则恢复所有初始化类型模块
     * @return {被影响的模块id:模块信息}
     */
    public Map<Long, SysModule> recoverModules(Set<Long> moduleIds);

    /**
     * 查询模块数据
     *
     * @param cdt
     * @return
     */
    public List<SysModule> getModulesByCdt(CSysModule cdt);

    /**
     * 获取用户或角色模块权限<br>
     * 根据userId查询，得到用户本身及其所属角色菜单权限的并集
     *
     * @param bean
     * @return
     */
    public List<SysModule> getAuthModulesBySearchBean(CAuthModuleBean bean);

    ResponseEntity<byte[]> exportModules();

    void importModules(MultipartFile file);

    List<SysModule> getSysModuleIdList(List<Integer> businessGroupList, List<Integer> businessModuleList);
}
