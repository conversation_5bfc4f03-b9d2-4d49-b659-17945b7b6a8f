package com.uinnova.product.eam.model.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * @description: 版本管理
 * @author: LC
 * @date: 2024/06/05
 **/
@Data
public class VersionManageVo implements Serializable {

    /** 流程名称 */
    private String name;

    /** 版本号 */
    private String version;

    /** 发布人 */
    private String publisher;

    /** 发布说明 */
    private String explain;

    /** 发布时间 */
    private String date;

    /** 审核人 */
    private String reviewer;
}
