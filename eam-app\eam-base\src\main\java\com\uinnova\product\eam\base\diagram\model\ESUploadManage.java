package com.uinnova.product.eam.base.diagram.model;

import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

/**
 * @Classname
 * @Description 上传(图片)管理
 * <AUTHOR>
 * @Date 2021-10-21-16:21
 */
@Data
public class ESUploadManage implements EntityBean {

    private static final Long serialVersionUID = 1L;

    @Comment("id")
    private Long id;

    @Comment("userId")
    private Long userId;

    @Comment("fileType")
    private Long fileType;

    @Comment("status")
    private Long status;

    @Comment("fileUrl")
    private String fileUrl;

    @Comment("fileSize")
    private Long fileSize;

    @Comment("createTime")
    private Long createTime;

    @Comment("modifyTime")
    private Long modifyTime;

    @Comment("预留字段1")
    private String 预留字段1;

    @Comment("预留字段2")
    private String 预留字段2;


}
