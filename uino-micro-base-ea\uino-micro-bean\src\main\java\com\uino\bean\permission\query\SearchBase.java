
package com.uino.bean.permission.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 
 * <AUTHOR>
 *
 */
@ApiModel(value="查询类",description = "查询信息")
public class SearchBase implements Serializable {
	private static final long serialVersionUID = 6398899597432596632L;
	@ApiModelProperty(value="页数",example = "1")
	private int pageNum = 1;

	@ApiModelProperty(value="每页记录数",example = "20")
	private int pageSize = 20;

	@ApiModelProperty(value="顺序")
	private String order;

	@ApiModelProperty(value="是否降序",example = "false")
	private boolean desc = false;

	public int getPageNum() {
		return pageNum;
	}

	public int getPageSize() {
		return pageSize;
	}

	public void setPageNum(int pageNum) {
		this.pageNum = pageNum;
	}

	public void setPageSize(int pageSize) {
		this.pageSize = pageSize;
	}

	public boolean isDesc() {
		return desc;
	}

	public void setDesc(boolean desc) {
		this.desc = desc;
	}

	public String getOrder() {
		return order;
	}

	public void setOrder(String order) {
		this.order = order;
	}

}
