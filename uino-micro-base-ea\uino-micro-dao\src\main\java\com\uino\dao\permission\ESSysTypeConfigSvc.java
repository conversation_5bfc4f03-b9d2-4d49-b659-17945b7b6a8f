package com.uino.dao.permission;

import com.uino.bean.permission.SysTypeConfig;
import com.uino.dao.AbstractESBaseDao;
import com.uino.util.sys.CommonFileUtil;
import lombok.extern.log4j.Log4j;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.util.List;

@Service
@Slf4j
public class ESSysTypeConfigSvc extends AbstractESBaseDao<SysTypeConfig, SysTypeConfig> {

    @Override
    public String getIndex() {
        return "uino_sys_type_config";
    }

    @Override
    public String getType() {
        return getIndex();
    }

    @PostConstruct
    public void init() {
        List<SysTypeConfig> data = CommonFileUtil.getData("/initdata/uino_sys_type_config.json", SysTypeConfig.class);
        this.initIndex(data, true);
    }
}
