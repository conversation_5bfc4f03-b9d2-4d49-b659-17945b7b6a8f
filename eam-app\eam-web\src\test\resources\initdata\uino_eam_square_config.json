[{"addressLink": "/diagram#/fullview", "cardName": "数据架构全景视图", "cardType": "multiDiagram", "catalogList": [{"catalogId": 752767525725209, "catalogName": "企业级数据主题域", "diagramId": ""}, {"catalogId": 752767525725210, "catalogName": "企业级概念数据模型", "diagramId": ""}, {"catalogId": 752767525725211, "catalogName": "企业级逻辑数据模型", "diagramId": ""}], "classification": "1", "configure": "{\"dataSubject\":\"51aa6d725890219f\",\"conceptualDataModel\":\"b121eea57575172c\",\"logicalDataModel\":\"6b45665242341dac\",\"config\":{\"dataSubject\":\"企业级数据主题域\",\"conceptualDataModel\":\"企业级概念数据模型\",\"logicalDataModel\":\"企业级逻辑数据模型\"}}", "createTime": 20220527204415, "creator": "admin", "id": 2, "isInit": 0, "modifier": "admin", "modifyTime": 20220817100621, "pictureInfo": "{\"iconfont\":\"shuju1\",\"background\":\"#FFB516\",\"iconIndex\":16,\"iconName\":\"icon4-4\"}", "sort": 3.0, "status": 1}, {"addressLink": "/diagram#/fullview", "cardName": "业务架构全景视图", "cardType": "multiDiagram", "catalogList": [{"catalogId": 504800148967302, "catalogName": "业务能力全景", "diagramId": ""}, {"catalogId": 504800148967303, "catalogName": "业务领域全景", "diagramId": ""}, {"catalogId": 504800148967304, "catalogName": "业务组件全景", "diagramId": ""}, {"catalogId": 504800148967305, "catalogName": "业务IT全景分析", "diagramId": ""}], "classification": "1", "configure": "{\"businessCapability\":\"Business\",\"businessArea\":\"a22224110ffd12df\",\"businessComponent\":\"\",\"businessIT\":\"\",\"config\":{\"businessCapability\":\"业务能力全景\",\"businessArea\":\"业务领域全景\",\"businessComponent\":\"业务组件全景\",\"businessIT\":\"业务IT全景分析\"}}", "createTime": 20220530095533, "creator": "admin", "id": 1, "isInit": 0, "modifier": "admin", "modifyTime": 20220817100623, "pictureInfo": "{\"iconfont\":\"yewu<PERSON><PERSON><PERSON><PERSON>-zhu<PERSON>\",\"background\":\"#5485FE\",\"iconIndex\":1,\"iconName\":\"icon1-1\"}", "sort": 1.0, "status": 1}, {"addressLink": "/eam#/theme/TreeFriendView", "cardName": "关联分析", "rolePermission": [], "catalogList": [], "classification": "2", "configure": "{\"isConfig\":false,\"data\":[]}", "createTime": 20220530101251, "creator": "admin", "id": 12, "isInit": 0, "modifier": "admin", "modifyTime": 20220817100634, "pictureInfo": "{\"iconfont\":\"yewu<PERSON><PERSON><PERSON><PERSON>-zhu<PERSON>\",\"background\":\"#5485FE\",\"iconIndex\":1,\"iconName\":\"icon1-1\"}", "sort": 6.0, "status": 1}, {"addressLink": "/eam#/theme/DataStoreFull", "cardName": "应用系统关系图谱", "classification": "2", "configure": "{\"pattern\":\"panoramatile\",\"dataConfig\":\"230953703957659\",\"showApiDetail\":\"true\"}", "createTime": 20220527184504, "creator": "admin", "id": 7, "isInit": 0, "modifier": "admin", "modifyTime": 20220817100639, "pictureInfo": "{\"iconfont\":\"yingyongxitongguanxitupu\",\"background\":\"#2DC4FF\",\"iconIndex\":5,\"iconName\":\"icon2-1\"}", "sort": 8.0, "status": 1}, {"addressLink": "/eam#/theme/DataStore", "cardName": "微服务访问关系分析", "classification": "2", "configure": "{\"pattern\":\"objectanalysis\",\"dataConfig\":\"[282558679481883,282558679481912]\"}", "createTime": 20220527185538, "creator": "admin", "id": 11, "isInit": 0, "modifier": "admin", "modifyTime": 20220817100648, "pictureInfo": "{\"iconfont\":\"yewu<PERSON><PERSON><PERSON><PERSON>-zhu<PERSON>\",\"background\":\"#5485FE\",\"iconIndex\":1,\"iconName\":\"icon1-1\"}", "sort": 12.0, "status": 1}, {"addressLink": "/eam#/theme/DataStore", "cardName": "流程关系图谱", "classification": "2", "configure": "{\"pattern\":\"objectanalysis\",\"dataConfig\":\"[282558679481748,245143821551999]\"}", "createTime": 20220527184116, "creator": "admin", "id": 6, "isInit": 0, "modifier": "admin", "modifyTime": 20220817100636, "pictureInfo": "{\"iconfont\":\"liuchengguanxitupu\",\"background\":\"#5485FE\",\"iconIndex\":23,\"iconName\":\"icon6-3\"}", "sort": 7.0, "status": 1}, {"addressLink": "/eam#/theme/DataStore", "cardName": "活动与任务关联分析", "classification": "2", "configure": "{\"pattern\":\"objectanalysis\",\"dataConfig\":\"[8973583925636440]\"}", "createTime": 20220527192202, "creator": "admin", "id": 9, "isInit": 0, "modifier": "admin", "modifyTime": 20220817100643, "pictureInfo": "{\"iconfont\":\"huodongyurenwuguanlian\",\"background\":\"#5485FE\",\"iconIndex\":26,\"iconName\":\"icon4-4\"}", "sort": 10.0, "status": 1}, {"addressLink": "/eam#/theme/DataStore", "cardName": "岗位角色执行分析", "classification": "2", "configure": "{\"pattern\":\"objectanalysis\",\"dataConfig\":\"[8973583925636585,8973583925636665]\"}", "createTime": 20220527192606, "creator": "admin", "id": 10, "isInit": 0, "modifier": "admin", "modifyTime": 20220817100646, "pictureInfo": "{\"iconfont\":\"gangweizhihang\",\"background\":\"#52669A\",\"iconIndex\":21,\"iconName\":\"icon6-1\"}", "sort": 11.0, "status": 1}, {"addressLink": "/eam#/theme/DataStore", "cardName": "业务到IT端到端分析", "classification": "2", "configure": "{\"pattern\":\"objectanalysis\",\"dataConfig\":\"[230953703954921]\"}", "createTime": 20220527184923, "creator": "admin", "id": 8, "isInit": 0, "modifier": "admin", "modifyTime": 20220817100641, "pictureInfo": "{\"iconfont\":\"yewudaoITduandaoduanfenxi\",\"background\":\"#1ED6A2\",\"iconIndex\":17,\"iconName\":\"icon5-1\"}", "sort": 9.0, "status": 1}, {"addressLink": "/diagram#/fullview", "cardName": "应用架构全景视图", "cardType": "diagram", "classification": "1", "configure": "{\"diagramId\":\"b121eea57575172c\"}", "createTime": 20220530095532, "creator": "admin", "id": 3, "isInit": 0, "modifier": "admin", "modifyTime": 20220817100748, "pictureInfo": "{\"iconfont\":\"yingyong\",\"background\":\"#1ED6A2\",\"iconIndex\":14,\"iconName\":\"icon4-2\"}", "sort": 2.0, "status": 1}, {"addressLink": "/diagram#/fullview", "cardName": "技术架构全景视图", "cardType": "diagram", "classification": "1", "configure": "{\"diagramId\":\"51aa6d725890219f\"}", "createTime": 20220530095525, "creator": "admin", "id": 4, "isInit": 0, "modifier": "admin", "modifyTime": 20220817100750, "pictureInfo": "{\"iconfont\":\"jishu\",\"background\":\"#2DC4FF\",\"iconIndex\":10,\"iconName\":\"icon3-2\"}", "sort": 4.0, "status": 1}, {"addressLink": "/app-wall#/?isShowBack=true", "cardName": "应用架构全景墙", "cardType": "link", "classification": "1", "createTime": 20220530095524, "creator": "admin", "id": 5, "isInit": 0, "modifier": "admin", "modifyTime": 20220817100753, "pictureInfo": "{\"iconfont\":\"yingyongjiago<PERSON>\",\"background\":\"#FFB516\",\"iconIndex\":27,\"iconName\":\"icon7-3\"}", "sort": 5.0, "status": 1}, {"classCode": "流程组", "creator": "admin", "cardName": "流程组", "modifier": "admin", "sort": 14, "classification": "5", "pictureInfo": "{\"iconName\":\"entrance0\"}", "assetType": 3, "rolePermission": [{"roleId": [1], "operation": "2"}], "modifyTime": 20250304155812, "createTime": 20250304155812, "showLayout": [1, 0], "id": 14, "isInit": 1, "status": 1}, {"classCode": "流程", "creator": "admin", "cardName": "流程", "modifier": "admin", "sort": 15, "classification": "5", "pictureInfo": "{\"iconName\":\"entrance0\"}", "domainId": 1, "assetType": 3, "rolePermission": [{"roleId": [1], "operation": "2"}], "modifyTime": 20250304155837, "createTime": 20250304155837, "showLayout": [1, 0], "id": 15, "isInit": 1, "status": 1}]