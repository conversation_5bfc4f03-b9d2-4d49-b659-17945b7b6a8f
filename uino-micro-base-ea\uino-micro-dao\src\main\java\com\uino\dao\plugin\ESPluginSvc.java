package com.uino.dao.plugin;

import com.binary.json.JSONObject;
import com.uino.bean.plugin.base.ESPluginInfo;
import com.uino.bean.plugin.query.CPluginInfo;
import com.uino.dao.AbstractESBaseDao;
import com.uino.dao.ESConst;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;

/**
 * <AUTHOR>
 * @Description
 * @Date 2021/9/27
 * @Version 1.0
 */
@Component
public class ESPluginSvc extends AbstractESBaseDao<ESPluginInfo, CPluginInfo> {

    @Override
    public String getIndex() {
        return ESConst.INDEX_PLUGIN;
    }

    @Override
    public String getType() {
        return ESConst.INDEX_PLUGIN;
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }
}
