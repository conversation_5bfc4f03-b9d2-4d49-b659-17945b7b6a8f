package com.binary.framework.spring;

import java.sql.SQLException;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.context.request.ServletWebRequest;
import org.springframework.web.servlet.DispatcherServlet;
import org.springframework.web.servlet.HandlerAdapter;
import org.springframework.web.servlet.HandlerExecutionChain;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.ModelAndViewDefiningException;
import org.springframework.web.servlet.View;
import org.springframework.web.servlet.view.RedirectView;
import org.springframework.web.util.NestedServletException;
import org.springframework.web.util.UrlPathHelper;
import org.springframework.web.util.WebUtils;

import com.binary.framework.Application;
import com.binary.framework.Local;
import com.binary.framework.SingleApplication;
import com.binary.framework.bean.User;
import com.binary.framework.exception.FrameworkException;
import com.binary.framework.util.ControllerUtils;
import com.binary.framework.util.ExceptionUtil;
import com.binary.framework.web.ErrorCode;
import com.binary.framework.web.RemoteResult;
import com.binary.jdbc.exception.JdbcException;



public class BinarySpringServlet extends DispatcherServlet {
	private static final long serialVersionUID = 1L;

	private static final Logger logger = LoggerFactory.getLogger(BinarySpringServlet.class);

	private static final UrlPathHelper urlPathHelper = new UrlPathHelper();

	private Map<String, String> resultRequestHeaderMap;


	public BinarySpringServlet() {
		super();
	}
	public BinarySpringServlet(WebApplicationContext webApplicationContext) {
		super(webApplicationContext);
	}



	@Override
	protected void initFrameworkServlet() throws ServletException {
		super.initFrameworkServlet();
		String strResultRequestHeader = getInitParameter("result-request-header");
		if(strResultRequestHeader!=null && (strResultRequestHeader=strResultRequestHeader.trim()).length()>0) {
			String[] arr = ControllerUtils.splitStringPattern(strResultRequestHeader);
			resultRequestHeaderMap = new HashMap<String, String>();
			for(int i=0; i<arr.length; i++) {
				String item = arr[i].trim().toUpperCase();
				int index = item.indexOf("=");
				if(index<=0 || index>=item.length()-1) continue ;
				String key = item.substring(0, index).trim();
				String value = item.substring(index+1).trim();
				resultRequestHeaderMap.put(key, value);
			}
		}

		try {
			Local.open((User)null);
			Application.afterInitialization(getWebApplicationContext());
			SingleApplication.afterInitialization(getWebApplicationContext());

			try {
				Local.commit();
			}catch(Exception ex) {
				logger.error("Local.commit()出现错误!", ex);
			}
		}catch(Exception e) {
			try {
				Local.rollback();
			}catch(Exception ex) {
				logger.error("Local.rollback()出现错误!", ex);
			}

			throw new FrameworkException(e);
		}finally {
			try {
				Local.close();
			}catch(Exception ex) {
				logger.error("Local.close()出现错误!", ex);
			}
		}
	}



	@SuppressWarnings("unchecked")
	private boolean isJsonResult(HttpServletRequest request) {
		boolean exists = false;
		if(this.resultRequestHeaderMap != null) {
			Enumeration<String> enu = request.getHeaderNames();
			if(enu != null) {
				while(enu.hasMoreElements()) {
					String name = enu.nextElement();
					String value = request.getHeader(name);
					if(name==null || value==null) continue ;

					String s = this.resultRequestHeaderMap.get(name.trim().toUpperCase());
					exists = s!=null && s.equalsIgnoreCase(value.trim());
					if(exists) break;
				}
			}
		}

		return exists;
	}





	/**
	 * 开启环境
	 */
	protected void open(HttpServletRequest request, HttpServletResponse response) {
		Local.open(request, response);
	}

	/**
	 * 提交事物
	 */
	protected void commit(HttpServletRequest request, HttpServletResponse response) {
		Local.commit();
	}

	/**
	 * 回滚事物
	 */
	protected void rollback(HttpServletRequest request, HttpServletResponse response) {
		Local.rollback();
	}


	/**
	 * 关闭环境
	 */
	protected void close(HttpServletRequest request, HttpServletResponse response) {
		Local.close();
	}



	/**
	 * 链接异常处理
	 */
	protected ModelAndView processForwardError(HttpServletRequest request, HttpServletResponse response,
						HandlerExecutionChain mappedHandler, Exception ex) throws Exception {
		Object handler = (mappedHandler != null ? mappedHandler.getHandler() : null);
		return processHandlerException(request, response, handler, ex);
	}




	/**
	 * ajax异常处理
	 */
	protected void processAjaxError(HttpServletRequest request, HttpServletResponse response, Throwable t) throws Exception {
	    Throwable realThrowable = ExceptionUtil.getRealThrowable(t);
	    String message = realThrowable.getMessage();
        message = message != null ? message.toUpperCase() : "";
        RemoteResult rs = null;
        if (realThrowable instanceof JdbcException || realThrowable instanceof SQLException || message.indexOf("YOU HAVE AN ERROR IN YOUR SQL SYNTAX") > 0|| message.indexOf("JAVA.SQL.SQLSYNTAXERROREXCEPTION") > 0) {
            rs = new RemoteResult(false, ErrorCode.SERVER_ERROR.getCode(), "DB ERROR !", null);
        } else {
            rs = new RemoteResult(realThrowable);
        }
		ControllerUtils.returnSimpleJson(request, response, rs);
	}



	protected void doDispatch(HttpServletRequest request, HttpServletResponse response) throws Exception {
		HttpServletRequest processedRequest = request;
		HandlerExecutionChain mappedHandler = null;
		int interceptorIndex = -1;

		try {
			Exception throwexp = null;
			ModelAndView mv = null;
			boolean errorView = false;
			boolean isjson = isJsonResult(request);

			try {
				processedRequest = checkMultipart(request);

				// Determine handler for the current request.
				mappedHandler = getHandler(processedRequest);
				if (mappedHandler == null || mappedHandler.getHandler() == null) {
					noHandlerFound(processedRequest, response);
					return;
				}

				// Determine handler adapter for the current request.
				HandlerAdapter ha = getHandlerAdapter(mappedHandler.getHandler());

                // Process last-modified header, if supported by the handler.
				String method = request.getMethod();
				boolean isGet = "GET".equals(method);
				if (isGet || "HEAD".equals(method)) {
					long lastModified = ha.getLastModified(request, mappedHandler.getHandler());
					if (logger.isDebugEnabled()) {
						String requestUri = urlPathHelper.getRequestUri(request);
						logger.debug("Last-Modified value for [" + requestUri + "] is: " + lastModified);
					}
					if (new ServletWebRequest(request, response).checkNotModified(lastModified) && isGet) {
						return;
					}
				}

				// Apply preHandle methods of registered interceptors.
				HandlerInterceptor[] interceptors = mappedHandler.getInterceptors();
				if (interceptors != null) {
					for (int i = 0; i < interceptors.length; i++) {
						HandlerInterceptor interceptor = interceptors[i];
						if (!interceptor.preHandle(processedRequest, response, mappedHandler.getHandler())) {
							triggerAfterCompletion(mappedHandler, interceptorIndex, processedRequest, response, null);
							return;
						}
						interceptorIndex = i;
					}
				}



				// Actually invoke the handler.
				try {
					open(processedRequest, response);

					mv = ha.handle(processedRequest, response, mappedHandler.getHandler());

					try {
						commit(processedRequest, response);
					}catch(Exception ex) {
						logger.error("Local.commit()出现错误!", ex);
					}
				}catch(Exception e) {
					try {
						rollback(processedRequest, response);
					}catch(Exception ex) {
						logger.error("Local.rollback()出现错误!", ex);
					}
					throw e;
				}finally {
					try {
						close(processedRequest, response);
					}catch(Exception ex) {
						logger.error("Local.close()出现错误!", ex);
					}
				}


				// Do we need view name translation?
				if (mv != null && !mv.hasView()) {
					mv.setViewName(getDefaultViewName(request));
				}

				// Apply postHandle methods of registered interceptors.
				if (interceptors != null) {
					for (int i = interceptors.length - 1; i >= 0; i--) {
						HandlerInterceptor interceptor = interceptors[i];
						interceptor.postHandle(processedRequest, response, mappedHandler.getHandler(), mv);
					}
				}
			}
			catch (ModelAndViewDefiningException ex) {
				logger.debug("ModelAndViewDefiningException encountered", ex);
				mv = ex.getModelAndView();
				throwexp = ex;
			}
			catch (Exception ex) {
				if(!isjson) {
					mv = processForwardError(processedRequest, response, mappedHandler, ex);
					errorView = (mv != null);
				}
				throwexp = ex;
			}

			if(throwexp != null) {
				logger.error(" mvc process error! "+request.getRequestURI(), throwexp);
			}

			if(isjson) {
				if(throwexp != null) {
					processAjaxError(processedRequest, response, throwexp);
				}
			}else {
				// Did the handler return a view to render?
				if (mv != null && !mv.wasCleared()) {
					render(mv, processedRequest, response);
					if (errorView) {
						WebUtils.clearErrorRequestAttributes(request);
					}
				}
				else {
					if (logger.isDebugEnabled()) {
						logger.debug("Null ModelAndView returned to DispatcherServlet with name '" + getServletName() +
								"': assuming HandlerAdapter completed request handling");
					}
				}
			}

			// Trigger after-completion for successful outcome.
			triggerAfterCompletion(mappedHandler, interceptorIndex, processedRequest, response, null);
		}

		catch (Exception ex) {
			// Trigger after-completion for thrown exception.
			triggerAfterCompletion(mappedHandler, interceptorIndex, processedRequest, response, ex);
			throw ex;
		}
		catch (Error err) {
			ServletException ex = new NestedServletException("Handler processing failed", err);
			// Trigger after-completion for thrown exception.
			triggerAfterCompletion(mappedHandler, interceptorIndex, processedRequest, response, ex);
			throw ex;
		}

		finally {
			// Clean up any resources used by a multipart request.
			if (processedRequest != request) {
				cleanupMultipart(processedRequest);
			}
		}
	}




	private void triggerAfterCompletion(HandlerExecutionChain mappedHandler,
			int interceptorIndex,
			HttpServletRequest request,
			HttpServletResponse response,
			Exception ex) throws Exception {

		// Apply afterCompletion methods of registered interceptors.
		if (mappedHandler != null) {
			HandlerInterceptor[] interceptors = mappedHandler.getInterceptors();
			if (interceptors != null) {
				for (int i = interceptorIndex; i >= 0; i--) {
					HandlerInterceptor interceptor = interceptors[i];
					try {
						interceptor.afterCompletion(request, response, mappedHandler.getHandler(), ex);
					}
					catch (Throwable ex2) {
						logger.error("HandlerInterceptor.afterCompletion threw exception", ex2);
					}
				}
			}
		}
	}




	@Override
	protected View resolveViewName(String viewName, Map<String, Object> model, Locale locale, HttpServletRequest request) throws Exception {
		View view = super.resolveViewName(viewName, model, locale, request);

		if(view!=null && view instanceof RedirectView) {
			view = new BinaryRedirectView((RedirectView)view);
		}
		return view;
	}



}
