package com.uino.provider.feign.cmdb;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.uino.bean.cmdb.base.ESCIHistoryInfo;
import com.uino.provider.feign.config.BaseFeignConfig;

/**
 * 
 * <AUTHOR>
 *
 */
@Component
@FeignClient(name = "${feign-server-name:tarsier-platform}", path = "${feign-server-path:tarsier-platform}/feign/ciHistory", configuration = {BaseFeignConfig.class})
public interface CIHistoryFeign {

    /**
     * 根据CI的id查询CI历史版本与时间对应关系
     * 
     * @param ciCode
     * @param classId
     * @return
     */
    @PostMapping("getCIVersionList")
    public List<String> getCIVersionList(@RequestBody String ciCode, @RequestParam(value = "classId") Long classId);

    /**
     * 根据id及版本号获取对应版本CI数据
     * 
     * @param ciCode
     * @param classId
     * @param version
     * @return
     */
    @PostMapping("getCIInfoHistoryByCIVersion")
    public ESCIHistoryInfo getCIInfoHistoryByCIVersion(@RequestBody String ciCode, @RequestParam(value = "classId") Long classId, @RequestParam(value = "version") Long version);

    void delAll(List<Long> classIds);
}
