package com.uinnova.product.eam.service.impl;

import com.binary.core.exception.BinaryException;
import com.uinnova.product.eam.model.vo.ESCISearchBeanVO;
import com.uinnova.product.eam.service.FlowFormService;
import com.uinnova.product.eam.service.ICIRltSwitchSvc;
import com.uinnova.product.eam.service.ICISwitchSvc;
import com.uinnova.product.eam.service.IEamCIClassApiSvc;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CiGroupPage;
import com.uinnova.product.vmdb.provider.rlt.bean.CcCiRltInfo;
import com.uino.bean.cmdb.base.ESCIClassInfo;
import com.uino.bean.cmdb.base.LibType;
import com.uino.bean.cmdb.query.ESRltSearchBean;
import com.uino.service.cmdb.microservice.ICIRltSvc;
import com.uino.util.sys.SysUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * description
 *
 * <AUTHOR>
 * @since 2024/6/26 下午1:49
 */
@Service
public class FlowFormServiceImpl implements FlowFormService {

    @Resource
    private IEamCIClassApiSvc ciClassApiSvc;

    @Resource
    ICISwitchSvc ciSwitchSvc;

    @Resource
    private ICIRltSwitchSvc iciRltSwitchSvc;

    @Override
    public CcCiClassInfo queryCIClassByCode(String classCode) {
        CcCiClassInfo ccCiClassInfo = ciClassApiSvc.queryClassAndAttrMappingByCode(classCode);
        List<CcCiAttrDef> attrDefs = ccCiClassInfo.getAttrDefs();
        CcCiAttrDef def = new CcCiAttrDef();
        def.setProName("关联活动");
        def.setProType(3);
        attrDefs.add(def);
        CcCiAttrDef def2 = new CcCiAttrDef();
        def2.setProName("关联流程");
        def2.setProType(3);
        attrDefs.add(def2);
        return ccCiClassInfo;
    }

    @Override
    public CiGroupPage queryFlowFormList(ESCISearchBeanVO bean) {

        if (!CollectionUtils.isEmpty(bean.getClassCodes())) {
            List<CcCiClassInfo> classInfos = ciClassApiSvc.getByClassCodes(bean.getClassCodes(), SysUtil.getCurrentUserInfo().getDomainId());
            if (CollectionUtils.isEmpty(classInfos) || classInfos.size() < bean.getClassCodes().size()) {
                throw new BinaryException("分类信息不存在");
            }
            List<Long> classIds = classInfos.stream().map(e -> e.getCiClass().getId()).collect(Collectors.toList());
            bean.getClassIds().addAll(classIds);
        }
        CiGroupPage result = ciSwitchSvc.queryPageBySearchBeanVO(bean, false, LibType.DESIGN);
        List<CcCiInfo> data = result.getData();
        if (CollectionUtils.isEmpty(data)) {
            return result;
        }
        Set<String> ciCodes = data.stream().map(ci -> ci.getCi().getCiCode()).collect(Collectors.toSet());
        //查询活动
        ESCIClassInfo activeClass = ciClassApiSvc.getCIClassByCode("活动");
        ICIRltSvc ciRltSvc = iciRltSwitchSvc.getCiRltSvc(LibType.DESIGN);
        ESRltSearchBean esRltSearchBean = new ESRltSearchBean();
        esRltSearchBean.setSourceClassIds(Collections.singletonList(activeClass.getId()));
        esRltSearchBean.setTargetCiCodes(ciCodes);
        List<CcCiRltInfo> ccCiRltInfoPage = ciRltSvc.searchRltByScroll(esRltSearchBean);
        Set<String> activeCiCodes = ccCiRltInfoPage.stream().map(e -> e.getSourceCiInfo().getCi().getCiCode()).collect(Collectors.toSet());

        //查询子流程
        ESCIClassInfo subFlowClass = ciClassApiSvc.getCIClassByCode("流程");
        esRltSearchBean.setTargetCiCodes(activeCiCodes);
        esRltSearchBean.setSourceClassIds(Collections.singletonList(subFlowClass.getId()));
        List<CcCiRltInfo> subFlowCiRltInfoPage = ciRltSvc.searchRltByScroll(esRltSearchBean);
        Map<String, List<CcCiRltInfo>> subFlowMap = subFlowCiRltInfoPage.stream().collect(Collectors.groupingBy(rlt -> rlt.getTargetCiInfo().getCi().getCiCode()));
        Map<String, List<CcCiRltInfo>> formActiveMap = ccCiRltInfoPage.stream().collect(Collectors.groupingBy(rlt -> rlt.getTargetCiInfo().getCi().getCiCode()));
        for (CcCiInfo datum : data) {
            Map<String, String> attrs = datum.getAttrs();
            Set<String> strings1 = new HashSet<>();
            Set<String> strings2 = new HashSet<>();
            List<CcCiRltInfo> ccCiRltInfos = formActiveMap.get(datum.getCi().getCiCode());
            if (!CollectionUtils.isEmpty(ccCiRltInfos)) {
                for (CcCiRltInfo ccCiRltInfo : ccCiRltInfos) {
                    CcCiInfo sourceCiInfo = ccCiRltInfo.getSourceCiInfo();
                    strings1.add(sourceCiInfo.getAttrs().get("活动名称"));

                    List<CcCiRltInfo> ccCiRltInfos1 = subFlowMap.get(sourceCiInfo.getCi().getCiCode());
                    if(!CollectionUtils.isEmpty(ccCiRltInfos1)){
                        ccCiRltInfos1.forEach(rlt -> {
                            CcCiInfo targetCiInfo = rlt.getSourceCiInfo();
                            strings2.add(targetCiInfo.getAttrs().get("流程名称"));
                        });
                    }
                }
            }
            attrs.put("关联活动", StringUtils.join(strings1, ","));
            attrs.put("关联流程", StringUtils.join(strings2, ","));
        }
        return result;
    }

}
