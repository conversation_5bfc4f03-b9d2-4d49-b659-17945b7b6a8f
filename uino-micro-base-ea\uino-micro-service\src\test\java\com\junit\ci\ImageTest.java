package com.junit.ci;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.uino.dao.BaseConst;
import org.elasticsearch.index.query.QueryBuilders;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import com.alibaba.fastjson.JSON;
import com.uino.dao.cmdb.ESCIAttrTransConfigSvc;
import com.uino.dao.cmdb.ESCIClassSvc;
import com.uino.dao.cmdb.ESCIHistorySvc;
import com.uino.dao.cmdb.ESCISvc;
import com.uino.dao.cmdb.ESDirSvc;
import com.uino.dao.cmdb.ESImageSvc;
import com.uino.dao.cmdb.ESSysTopData;
import com.uino.dao.cmdb.ESTagSvc;
import com.uino.service.cmdb.microservice.impl.ImageSvc;
import com.uino.service.cmdb.microservice.impl.TopDataSvc;
import com.uino.dao.sys.ESCIOperateLogSvc;
import com.uino.dao.sys.ESDictionaryClassSvc;
import com.uino.dao.sys.ESDictionaryItemSvc;
import com.uino.dao.sys.ESResourceSvc;
import com.uino.service.sys.microservice.impl.ResourceSvc;
import com.uino.service.sys.microservice.impl.SysSvc;
import com.uino.util.sys.CommonFileUtil;
import com.uino.bean.cmdb.base.CcCiClassDir;
import com.uino.bean.cmdb.base.CcImage;
import com.uino.bean.cmdb.business.ImageCount;
import com.uino.bean.cmdb.query.ESSearchImageBean;

import lombok.extern.slf4j.Slf4j;

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = { ImageTest.class, ESDirSvc.class, ESImageSvc.class, ImageSvc.class, TopDataSvc.class,
		ResourceSvc.class, ESCIClassSvc.class, SysSvc.class, ESSysTopData.class, ESResourceSvc.class, ESCISvc.class,
		ESCIHistorySvc.class, ESCIAttrTransConfigSvc.class, ESTagSvc.class, ESCIOperateLogSvc.class,
		ESDictionaryItemSvc.class, ESDictionaryClassSvc.class })
@ActiveProfiles("providerDev")
@Slf4j
public class ImageTest {

	@Autowired
	ESImageSvc esImgSvc;

	@Autowired
	ImageSvc imgSvc;

	@Autowired
	ESDirSvc dirSvc;

	@Test
	public void imageTest() {
		List<CcImage> list = CommonFileUtil.getData("/static/cc_image.json", CcImage.class);
		log.info(JSON.toJSONString(list));
		esImgSvc.saveOrUpdateBatch(list);
	}

	@Test
	public void imageTest2() {
		List<ImageCount> rs = imgSvc.queryImageDirList(BaseConst.DEFAULT_DOMAIN_ID, null);
		log.info(JSON.toJSONString(rs));
	}

	@Test
	public void imageTest3() {
		Map<Long, String> map = new HashMap<Long, String>();

		map.put(1L, "Tarsier");
		map.put(2L, "Common Icons");
		map.put(3L, "自定义形状");
		map.put(4L, "Tarsier DCV");
		map.put(5L, "Tarsier DMV-3D");
		map.put(6L, "Tarsier DCV-3D");
		map.put(7L, "表格");
		map.put(8L, "Business");
		map.put(11L, "Visio");
		map.put(12L, "VMware");
		map.put(100000000000001L, "test");
		map.put(100000000000002L, "test1");
		map.put(3106669866150500L, "Tarsier_DCV-3D");
		map.put(3106669866150501L, "Tarsier_DMV-3D");

		Map<String, Long> rs = esImgSvc.groupByCountField("dirId", QueryBuilders.matchAllQuery());
		log.info(JSON.toJSONString(rs));
		List<CcCiClassDir> list = new ArrayList<CcCiClassDir>();
		Iterator<String> it = rs.keySet().iterator();
		while (it.hasNext()) {
			String key = it.next();
			CcCiClassDir dir = new CcCiClassDir();
			dir.setId(Long.parseLong(key));
			dir.setParentId(0L);
			dir.setCiType(3);
			dir.setDirName(map.get(Long.parseLong(key)));
			list.add(dir);
		}

		dirSvc.saveOrUpdateBatch(list);
	}

	@Test
	public void imageTest4() {
		// esImgSvc.deleteByQuery(QueryBuilders.termQuery("dataStatus", 0),
		// true);
		dirSvc.deleteByQuery(QueryBuilders.termQuery("id", 0), true);
		ESSearchImageBean bean = new ESSearchImageBean();
		log.info(JSON.toJSONString(bean));
		List<CcImage> rs = imgSvc.queryImagePage(bean).getData();
		log.info(JSON.toJSONString(rs));
	}

}
