package com.uino.util.judge.common;

import com.uino.util.judge.BaseJudgeProcess;

import lombok.Setter;

/**
 * 包含字符串验证 当containTrue为true时包含通过 当containTrue为false时不包含通过
 * 
 * <AUTHOR>
 *
 */
public class ContainStrJudge extends BaseJudgeProcess {
	/**
	 * 包含字符串
	 */
	@Setter
	private String containVal;
	/**
	 * 若包含返回true 默认false
	 */
	@Setter
	private boolean containTrue = false;

	@Override
	protected boolean validCore(Object validObj) {
		String validStr = (String) validObj;
		boolean validResult = validStr.contains(containVal);
		return containTrue ? validResult : !validResult;
	}

}
