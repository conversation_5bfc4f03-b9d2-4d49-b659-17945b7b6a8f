package com.uino.dao.cmdb;

import java.util.List;

import jakarta.annotation.PostConstruct;

import org.springframework.stereotype.Service;

import com.uinnova.product.vmdb.comm.model.ci.CCcCiClassDir;
import com.uino.dao.AbstractESBaseDao;
import com.uino.dao.ESConst;
import com.uino.util.sys.CommonFileUtil;
import com.uino.bean.cmdb.base.CcCiClassDir;

import lombok.extern.slf4j.Slf4j;

/**
 * 分类目录
 * 
 * <AUTHOR>
 */
@Service
@Slf4j
public class ESDirSvc extends AbstractESBaseDao<CcCiClassDir, CCcCiClassDir> {

	@Override
	public String getIndex() {
		return ESConst.INDEX_CMDB_DIR;
	}

	@Override
	public String getType() {
		return ESConst.INDEX_CMDB_DIR;
	}

	@PostConstruct
	public void init() {
		List<CcCiClassDir> list = CommonFileUtil.getData("/initdata/uino_ci_dir.json", CcCiClassDir.class);
		super.initIndex(list);
	}

}
