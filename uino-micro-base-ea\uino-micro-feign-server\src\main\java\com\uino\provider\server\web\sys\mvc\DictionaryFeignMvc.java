package com.uino.provider.server.web.sys.mvc;

import java.util.Collection;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.binary.core.io.Resource;
import com.binary.jdbc.Page;
import com.uino.service.sys.microservice.IDictionarySvc;
import com.uino.bean.cmdb.business.ImportResultMessage;
import com.uino.bean.cmdb.business.ImportSheetMessage;
import com.uino.bean.sys.base.ESDictionaryClassInfo;
import com.uino.bean.sys.base.ESDictionaryItemInfo;
import com.uino.bean.sys.business.DictionaryInfoDto;
import com.uino.bean.sys.query.ESDictionaryItemSearchBean;
import com.uino.bean.sys.query.ExportDictionaryDto;
import com.uino.provider.feign.sys.DictionaryFeign;

@RestController
@RequestMapping("feign/sys/dict")
public class DictionaryFeignMvc implements DictionaryFeign {

    @Autowired
    private IDictionarySvc dictSvc;

    @Override
    public Long saveDictionaryClassInfo(ESDictionaryClassInfo dictClassInfo) {
        return dictSvc.saveDictionaryClassInfo(dictClassInfo);
    }

    @Override
    public List<DictionaryInfoDto> getDictionaryClassList(Long domainId) {
        return dictSvc.getDictionaryClassList(domainId);
    }

    @Override
    public ESDictionaryClassInfo getDictClassInfoById(Long dictClassId) {
        return dictSvc.getDictClassInfoById(dictClassId);
    }

    @Override
    public Integer deleteDictClassInfoById(Long dictClassId) {
        return dictSvc.deleteDictClassInfoById(dictClassId);
    }

    @Override
	public ESDictionaryItemInfo getDictItemInfoById(Long id) {
		return dictSvc.getDictItemInfoById(id);
	}

	@Override
    public Page<ESDictionaryItemInfo> searchDictItemPageByBean(ESDictionaryItemSearchBean bean) {
        return dictSvc.searchDictItemPageByBean(bean);
    }

    @Override
    public List<ESDictionaryItemInfo> searchDictItemListByBean(ESDictionaryItemSearchBean bean) {
        return dictSvc.searchDictItemListByBean(bean);
    }

    @Override
    public Long saveOrUpdateDictionaryItem(ESDictionaryItemInfo item) {
        return dictSvc.saveOrUpdateDictionaryItem(item);
    }

    @Override
    public ImportSheetMessage saveDictionaryItemsBatch(Long dictClassId, List<ESDictionaryItemInfo> items) {
        return dictSvc.saveDictionaryItemsBatch(dictClassId, items);
    }

    @Override
    public Integer deleteItemByIds(Collection<Long> ids) {
        return dictSvc.deleteItemByIds(ids);
    }

    @Override
    public Resource exportDictionaryItems(ExportDictionaryDto dto) {
        return dictSvc.exportDictionaryItems(dto);
    }

    @Override
    public ImportResultMessage importDictionaryItems(Long dictClassId, MultipartFile file) {
        return dictSvc.importDictionaryItems(dictClassId, file);
    }

    @Override
    public List<String> getExteralDictValues(ESDictionaryItemSearchBean bean) {
        return dictSvc.getExteralDictValues(bean);
    }

}
