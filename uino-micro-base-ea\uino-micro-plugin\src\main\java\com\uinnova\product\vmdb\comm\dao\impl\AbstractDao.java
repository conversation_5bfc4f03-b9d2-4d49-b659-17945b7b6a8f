package com.uinnova.product.vmdb.comm.dao.impl;

import com.binary.jdbc.JdbcOperator;
import com.binary.jdbc.JdbcOperatorFactory;

/**
 * 
 * <AUTHOR>
 *
 */
public abstract class AbstractDao {

    private String dataSourceName;

    public String getDataSourceName() {
        return dataSourceName;
    }

    public void setDataSourceName(String dataSourceName) {
        this.dataSourceName = dataSourceName;
    }

    protected JdbcOperator getJdbcOperator() {
        JdbcOperatorFactory factory = JdbcOperatorFactory.getMomentFactory();
        JdbcOperator jo = factory.getJdbcOperator(this.dataSourceName);
        return jo;
    }

    protected JdbcOperator getJdbcOperator(String dsName) {
        JdbcOperatorFactory factory = JdbcOperatorFactory.getMomentFactory();
        return factory.getJdbcOperator(dsName);
    }

}
