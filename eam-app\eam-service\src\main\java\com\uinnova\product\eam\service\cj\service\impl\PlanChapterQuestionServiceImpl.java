package com.uinnova.product.eam.service.cj.service.impl;

import com.uinnova.product.eam.model.cj.domain.ChapterInstance;
import com.uinnova.product.eam.model.cj.domain.PlanChapterQuestion;
import com.uinnova.product.eam.model.cj.domain.PlanTemplateChapterData;
import com.uinnova.product.eam.model.cj.enums.ChapterDataTypeEnum;
import com.uinnova.product.eam.model.cj.enums.QuestionCheckEnum;
import com.uinnova.product.eam.model.cj.enums.QuestionStatusEnum;
import com.uinnova.product.eam.model.cj.request.PlanChapterQuestionAnswerRequest;
import com.uinnova.product.eam.model.cj.request.PlanChapterQuestionRequest;
import com.uinnova.product.eam.model.cj.request.PlanModuleAnnotationRequest;
import com.uinnova.product.eam.model.cj.vo.PlanChapterQuestionVO;
import com.uinnova.product.eam.model.cj.vo.PlanDesignInstanceVO;
import com.uinnova.product.eam.model.cj.vo.QuestionAnswerVO;
import com.uinnova.product.eam.model.constants.Constants;
import com.uinnova.product.eam.service.cj.dao.PlanChapterQuestionDao;
import com.uinnova.product.eam.service.cj.service.*;
import com.uinnova.product.eam.service.exception.BusinessException;
import org.springframework.util.CollectionUtils;
import com.binary.core.lang.StringUtils;
import com.binary.jdbc.Page;
import com.uino.api.client.permission.IUserApiSvc;
import com.uino.bean.permission.business.UserInfo;
import com.uino.dao.util.ESUtil;
import com.uino.util.sys.SysUtil;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * @description:
 * @author: Lc
 * @create: 2022-03-01 19:53
 */
@Service
public class PlanChapterQuestionServiceImpl implements PlanChapterQuestionService {

    @Resource
    private PlanChapterQuestionDao planChapterQuestionDao;

    @Resource
    private PlanChapterInstanceService planChapterInstanceService;

    @Resource
    private PlanTemplateChapterService planTemplateChapterService;

    @Resource
    private IUserApiSvc userApiSvc;

    @Resource
    private PlanModuleAnnotationService planModuleAnnotationService;

    @Resource
    @Lazy
    private PlanDesignInstanceService planDesignInstanceService;

    @Override
    public Long saveOrUpdate(PlanChapterQuestion question) {
        // 校验参数
        checkQuestion(question);
        // 新增批注
        PlanModuleAnnotationRequest request = new PlanModuleAnnotationRequest();
        request.setChapterId(question.getPlanChapterId());
        request.setPlanChapterModuleId(question.getPlanChapterContextId());
        request.setAnnotationContent(question.getQuestion());
        request.setProblem(true);
        request.setProcessInstanceId(question.getProcessInstanceId());
        request.setTaskDefinitionKey(question.getTaskDefinitionKey());
        Long annotationId = planModuleAnnotationService.saveAnnotation(request);

        // 设置状态默认值
        question.setQuestionState(QuestionStatusEnum.NOT_RECTIFIED.getState());
        question.setCheckResult(QuestionCheckEnum.LATER_CHECK.getCode());
        question.setAnnotationId(annotationId);
        return planChapterQuestionDao.saveOrUpdate(question);
    }

    @Override
    public Long saveArchivingQuestion(PlanChapterQuestion question) {
        // 校验参数
        checkQuestion(question);

        if (question.getAnnotationId() == null) {
            throw new BusinessException("批注主键不能为空!");
        }
        // 设置状态默认值
        question.setQuestionState(QuestionStatusEnum.NOT_RECTIFIED.getState());
        question.setCheckResult(QuestionCheckEnum.LATER_CHECK.getCode());
        Long result = planChapterQuestionDao.saveOrUpdate(question);

        // 修改批注问题状态
        PlanModuleAnnotationRequest request = new PlanModuleAnnotationRequest();
        request.setAnnotationId(question.getAnnotationId());
        request.setProblem(true);
        planModuleAnnotationService.modifyAnnotation(request);
        return result;
    }

    @Override
    public Long saveAnswer(PlanChapterQuestionAnswerRequest request) {
        if (request == null) {
            throw new BusinessException("问题回复参数不能为空");
        }
        if (request.getId() == null) {
            throw new BusinessException("问题主键不能为空!");
        }
        PlanChapterQuestion question = planChapterQuestionDao.getById(request.getId());
        if (request.getState() != null) {
            question.setQuestionState(request.getState());
            if (QuestionCheckEnum.NO_PASS.getCode().equals(question.getCheckResult())) {
                question.setCheckResult(QuestionCheckEnum.LATER_CHECK.getCode());
            }
        }
        if (request.getCheck() != null) {
            question.setCheckResult(request.getCheck());
            /*if (QuestionCheckEnum.NO_PASS.getCode().equals(request.getCheck())) {
                question.setQuestionState(QuestionStatusEnum.NOT_RECTIFIED.getState());
            }*/
        }
        if (!StringUtils.isEmpty(request.getAnswer())) {
            QuestionAnswerVO answer = new QuestionAnswerVO();
            answer.setAnswer(request.getAnswer());
            answer.setAnswerTime(ESUtil.getNumberDateTime());
            UserInfo userInfo = userApiSvc.getUserInfoById(SysUtil.getCurrentUserInfo().getId());
            answer.setUsername(userInfo.getUserName());
            if (CollectionUtils.isEmpty(question.getAnswerList())) {
                List<QuestionAnswerVO> answerList = new ArrayList<>();
                answerList.add(answer);
                question.setAnswerList(answerList);
            } else {
                List<QuestionAnswerVO> answerList = question.getAnswerList();
                answerList.add(answer);
            }
        }
        return planChapterQuestionDao.saveOrUpdate(question);
    }

    @Override
    public Page<PlanChapterQuestionVO> findQuestionList(PlanChapterQuestionRequest request) {
        if (request == null) {
            throw new BusinessException("请求参数不能为空!");
        }
        if (request.getPlanId() == null) {
            throw new BusinessException("方案主键不能为空!");
        }
        if (StringUtils.isEmpty(request.getProcessInstanceId())) {
            throw new BusinessException("流程定义不能为空!");
        }
        PlanDesignInstanceVO plan = planDesignInstanceService.getById(request.getPlanId());
        if (plan == null) {
            throw new BusinessException("获取方案错误!");
        }
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.must(QueryBuilders.termQuery("planId", request.getPlanId()));
        queryBuilder.must(QueryBuilders.termQuery("processInstanceId.keyword", request.getProcessInstanceId()));
        if (!StringUtils.isEmpty(request.getQuestion())) {
            queryBuilder.must(QueryBuilders.wildcardQuery("question.keyword", "*" + request.getQuestion() + "*"));
        }
        // 判断当前登录人是否是提交人，提交人查询所有问题，审批人只查询自己的问题
        if (!Objects.equals(plan.getCreatorCode(), SysUtil.getCurrentUserInfo().getLoginCode()) || plan.isProcessApproval()) {
            if (StringUtils.isEmpty(request.getProcessInstanceId())) {
                throw new BusinessException("流程实例不能为空!");
            }
            if (StringUtils.isEmpty(request.getTaskDefinitionKey())) {
                throw new BusinessException("任务定义不能为空!");
            }
            queryBuilder.must(QueryBuilders.termQuery("creator.keyword", SysUtil.getCurrentUserInfo().getLoginCode()));
            queryBuilder.must(QueryBuilders.termQuery("taskDefinitionKey.keyword", request.getTaskDefinitionKey()));
        }
        Page<PlanChapterQuestion> questionPage = planChapterQuestionDao.getSortListByQuery(request.getPageNum(), request.getPageSize(), queryBuilder, "createTime", false);
        List<PlanChapterQuestionVO> questionVoList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(questionPage.getData())) {
            questionPage.getData().forEach(question -> {
                PlanChapterQuestionVO questionVo = new PlanChapterQuestionVO();
                questionVo.setId(question.getId());
                questionVo.setQuestion(question.getQuestion());
                // 问题回复内容
                if (!CollectionUtils.isEmpty(question.getAnswerList())) {
                    List<QuestionAnswerVO> answerList = question.getAnswerList();
                    questionVo.setAnswer(answerList.get(answerList.size() - 1).getAnswer());
                } else {
                    questionVo.setAnswer(Constants.CENTRE_LINE);
                }
                // 回复状态
                questionVo.setQuestionState(QuestionStatusEnum.getQuestionDesc(question.getQuestionState()));
                // 验证功能是否可用
                if (Objects.equals(question.getQuestionState(), QuestionStatusEnum.NOT_RECTIFIED.getState()) && Objects.equals(question.getCheckResult(), QuestionCheckEnum.LATER_CHECK.getCode())) {
                    questionVo.setCheckSign(false);
                } else {
                    questionVo.setCheckSign(true);
                }
                // 验证状态
                questionVo.setCheckResult(QuestionCheckEnum.getCheckDesc(question.getCheckResult()));
                // 章节信息
                ChapterInstance chapterInstance = planChapterInstanceService.getPlanChapterInstance(question.getPlanChapterId());
                // 模板数据内容
                PlanTemplateChapterData chapterData = planTemplateChapterService.getTemplateChapterData(question.getPlanChapterContextId());
                if (chapterInstance != null && !StringUtils.isEmpty(chapterInstance.getName())) {
                    String module = chapterInstance.getName();
                    if (chapterData != null) {
                        if (ChapterDataTypeEnum.PRODUCT.getDataType().equals(chapterData.getType())
                                && !StringUtils.isEmpty(chapterData.getProductName())) {
                            module = module + Constants.SLASH + chapterData.getProductName();
                        } else if (ChapterDataTypeEnum.DATA_TABLE.getDataType().equals(chapterData.getType())
                                && !StringUtils.isEmpty(chapterData.getDataTableName())) {
                            module = module + Constants.SLASH + chapterData.getDataTableName();
                        } else if (ChapterDataTypeEnum.RICH_TEXT.getDataType().equals(chapterData.getType())
                                && !StringUtils.isEmpty(chapterData.getRichTextName())) {
                            module = module + Constants.SLASH + chapterData.getRichTextName();
                        }
                    }
                    questionVo.setModule(module);
                }
                questionVoList.add(questionVo);
            });
        }
        // 重新控制分页
        Page<PlanChapterQuestionVO> page = new Page<>();
        page.setPageNum(questionPage.getPageNum());
        page.setPageSize(questionPage.getPageSize());
        page.setTotalPages(questionPage.getTotalPages());
        page.setTotalRows(questionPage.getTotalRows());
        page.setData(questionVoList);
        return page;
    }

    @Override
    public List<QuestionAnswerVO> findAnswerList(Long id) {
        if (id == null) {
            throw new BusinessException("主键不能为空!");
        }
        PlanChapterQuestion question = planChapterQuestionDao.getById(id);
        if (question != null) {
            return question.getAnswerList();
        }
        return Collections.emptyList();
    }

    @Override
    public Boolean updateQuestionAndAnnotation(PlanModuleAnnotationRequest request) {
        if (request == null) {
            throw new BusinessException("参数不能为空!");
        }
        if (request.getAnnotationId() == null) {
            throw new BusinessException("批注主键不能为空!");
        }
        if (StringUtils.isEmpty(request.getAnnotationContent())) {
            throw new BusinessException("内容不能为空!");
        }
        // 修改问题
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.must(QueryBuilders.termQuery("annotationId", request.getAnnotationId()));
        List<PlanChapterQuestion> questionList = planChapterQuestionDao.getListByQuery(queryBuilder);
        if (!CollectionUtils.isEmpty(questionList)) {
            PlanChapterQuestion chapterQuestion = questionList.get(0);
            chapterQuestion.setQuestion(request.getAnnotationContent());
            planChapterQuestionDao.saveOrUpdate(chapterQuestion);
        }

        // 同时修改批注
        PlanModuleAnnotationRequest annotationRequest = new PlanModuleAnnotationRequest();
        annotationRequest.setAnnotationId(request.getAnnotationId());
        annotationRequest.setAnnotationContent(request.getAnnotationContent());
        planModuleAnnotationService.modifyAnnotation(request);
        return true;
    }

    @Override
    public Boolean deleteQuestionAndAnnotation(Long annotationId) {
        if (annotationId == null) {
            throw new BusinessException("批注主键不能为空!");
        }
        // 删除问题
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.must(QueryBuilders.termQuery("annotationId", annotationId));
        planChapterQuestionDao.deleteByQuery(queryBuilder, true);

        // 同时删除批注
        PlanModuleAnnotationRequest request = new PlanModuleAnnotationRequest();
        request.setAnnotationId(annotationId);
        planModuleAnnotationService.deleteAnnotation(request);
        return true;
    }

    @Override
    public Boolean deleteQuestionAndAnnotationByPlanId(Long planId) {
        if (planId == null) {
            throw new BusinessException("方案主键不能为空!");
        }
        // 删除问题
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.must(QueryBuilders.termQuery("planId", planId));
        planChapterQuestionDao.deleteByQuery(queryBuilder, true);

        // 删除批注
        planModuleAnnotationService.deleteAnnotationByPlanId(planId);
        return true;
    }

    @Override
    public Boolean cancelArchiving(Long annotationId) {
        if (annotationId == null) {
            throw new BusinessException("批注主键不能为空!");
        }

        // 删除问题
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.must(QueryBuilders.termQuery("annotationId", annotationId));
        List<PlanChapterQuestion> questionList = planChapterQuestionDao.getListByQuery(queryBuilder);
        if (!CollectionUtils.isEmpty(questionList)) {
            PlanChapterQuestion chapterQuestion = questionList.get(0);
            planChapterQuestionDao.deleteById(chapterQuestion.getId());
        }

        // 设置批注为非问题
        PlanModuleAnnotationRequest request = new PlanModuleAnnotationRequest();
        request.setAnnotationId(annotationId);
        request.setProblem(false);
        planModuleAnnotationService.modifyAnnotation(request);
        return true;
    }

    @Override
    public Long countQuestion(PlanChapterQuestion question) {
        if (question == null) {
            throw new BusinessException("传递参数不能为空!");
        }
        if (question.getPlanId() == null) {
            throw new BusinessException("方案主键不能为空!");
        }
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.must(QueryBuilders.termQuery("planId", question.getPlanId()));
        if (question.getQuestionState() != null) {
            queryBuilder.must(QueryBuilders.termQuery("questionState", question.getQuestionState()));
        }
        if (question.getCheckResult() != null) {
            queryBuilder.mustNot(QueryBuilders.termQuery("checkResult", question.getCheckResult()));
        }
        if (!StringUtils.isEmpty(question.getCreatorCode())) {
            queryBuilder.must(QueryBuilders.termQuery("creator.keyword", question.getCreatorCode()));
        }
        if (!StringUtils.isEmpty(question.getProcessInstanceId())) {
            queryBuilder.must(QueryBuilders.termQuery("processInstanceId.keyword", question.getProcessInstanceId()));
        }
        if (!StringUtils.isEmpty(question.getTaskDefinitionKey())) {
            queryBuilder.must(QueryBuilders.termQuery("taskDefinitionKey.keyword", question.getTaskDefinitionKey()));
        }
        return planChapterQuestionDao.countByCondition(queryBuilder);
    }

    @Override
    public List<PlanChapterQuestion> findPlanChapterQuestionList(PlanChapterQuestion question) {
        if (question == null) {
            throw new BusinessException("传递参数不能为空!");
        }
        if (question.getPlanId() == null) {
            throw new BusinessException("方案主键不能为空!");
        }
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.must(QueryBuilders.termQuery("planId", question.getPlanId()));
        if (question.getQuestionState() != null) {
            queryBuilder.must(QueryBuilders.termQuery("questionState", question.getQuestionState()));
        }
        if (question.getCheckResult() != null) {
            queryBuilder.mustNot(QueryBuilders.termQuery("checkResult", question.getCheckResult()));
        }
        if (!StringUtils.isEmpty(question.getCreatorCode())) {
            queryBuilder.must(QueryBuilders.termQuery("creator.keyword", question.getCreatorCode()));
        }
        if (!StringUtils.isEmpty(question.getProcessInstanceId())) {
            queryBuilder.must(QueryBuilders.termQuery("processInstanceId.keyword", question.getProcessInstanceId()));
        }
        if (!StringUtils.isEmpty(question.getTaskDefinitionKey())) {
            queryBuilder.must(QueryBuilders.termQuery("taskDefinitionKey.keyword", question.getTaskDefinitionKey()));
        }
        return planChapterQuestionDao.getListByQuery(queryBuilder);
    }

    private void checkQuestion(PlanChapterQuestion question) {
        if (question == null) {
            throw new BusinessException("问题参数不能为空!");
        }
        if (question.getPlanId() == null) {
            throw new BusinessException("方案主键不能为空!");
        }
        if (question.getPlanChapterId() == null) {
            throw new BusinessException("方案章节不能为空!");
        }
        if (question.getPlanChapterContextId() == null) {
            throw new BusinessException("方案章节内容不能为空!");
        }
        if (question.getQuestion() == null) {
            throw new BusinessException("问题不能为空!");
        }
        if (question.getTaskDefinitionKey() == null) {
            throw new BusinessException("任务定义主键");
        }
    }

}
