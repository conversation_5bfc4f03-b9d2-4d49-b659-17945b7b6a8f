[{"CONF_TYPE": "AUTO_PHYSICS_CONFIG", "CONF_NAME": "智能成图-网络部署图中心及网络分区配置", "CONF_JSON": "{\"dataCenter\":[{\"id\":1,\"name\":\"单中心\",\"children\":[{\"id\":1,\"name\":\"生产中心\"}]},{\"id\":2,\"name\":\"双中心\",\"children\":[{\"id\":1,\"name\":\"生产中心\"},{\"id\":2,\"name\":\"网络中心\"}]},{\"id\":3,\"name\":\"三中心\",\"children\":[{\"id\":1,\"name\":\"生产中心\"},{\"id\":2,\"name\":\"网络中心\"},{\"id\":3,\"name\":\"内部网络分区\"}]}],\"partition\":[{\"id\":\"1\",\"name\":\"内部网络分区\",\"children\":[{\"id\":\"1\",\"name\":\"生产互联网DMZ区\"},{\"id\":\"2\",\"name\":\"办公互联网DMZ区\"},{\"id\":\"3\",\"name\":\"开发测试网DMZ区\"},{\"id\":\"4\",\"name\":\"生产互联网APP/DB区\"},{\"id\":\"5\",\"name\":\"办公互联网APP/DB区\"},{\"id\":\"6\",\"name\":\"开发测试网APP/DB区\"},{\"id\":\"7\",\"name\":\"生产服务器区\"},{\"id\":\"8\",\"name\":\"办公服务器区\"},{\"id\":\"9\",\"name\":\"开发测试网服务器区\"},{\"id\":\"10\",\"name\":\"生产客户端区\"},{\"id\":\"11\",\"name\":\"办公互客户端区\"},{\"id\":\"12\",\"name\":\"开发测试网客户端区\"},{\"id\":\"13\",\"name\":\"外联区\"},{\"id\":\"14\",\"name\":\"数据分析区\"},{\"id\":\"15\",\"name\":\"网管安管区\"},{\"id\":\"16\",\"name\":\"开发运维一体化专区\"}]},{\"id\":\"2\",\"name\":\"外部网络分区\",\"children\":[{\"id\":\"1\",\"name\":\"互联网\"},{\"id\":\"2\",\"name\":\"专线网络\"}]}]}", "所属用户": "admin"}, {"CONF_TYPE": "mapAssetTypeList", "CONF_NAME": "架构地图卡片分类配置", "CONF_JSON": "[\n    {\n        \"label\": \"战略与转型\",\n        \"value\": \"1\",\n        \"color\": \"rgb(36,101,255)\",\n        \"bgColor\": \"rgb(237, 242, 255)\",\n        \"children\": []\n    },\n    {\n        \"label\": \"业务架构\",\n        \"value\": \"2\",\n        \"color\": \"rgb(24, 195, 135)\",\n        \"bgColor\": \"rgb(233, 251, 245)\",\n        \"children\": []\n    },\n    {\n        \"label\": \"应用架构\",\n        \"value\": \"3\",\n        \"color\": \"rgb(138, 88, 248)\",\n        \"bgColor\": \"rgb(243, 237, 255)\",\n        \"children\": []\n    },\n    {\n        \"label\": \"数据架构\",\n        \"value\": \"4\",\n        \"color\": \"rgb(250, 100, 1)\",\n        \"bgColor\": \"rgb(255, 239, 228)\",\n        \"children\": []\n    },\n    {\n        \"label\": \"技术架构\",\n        \"value\": \"5\",\n        \"color\": \"rgb(24, 171, 195)\",\n        \"bgColor\": \"rgb(233, 249, 251)\",\n        \"children\": []\n    }\n]", "所属用户": "admin"}, {"CONF_TYPE": "FLOW_HOME_DIAGRAM", "CONF_NAME": "流程架构首页视图", "CONF_JSON": "1", "所属用户": "admin"}, {"CONF_TYPE": "ORGANIZATIONAL_POSITION", "CONF_NAME": "流程管理-组织体系中使用的分类", "CONF_JSON": "岗位", "所属用户": "admin"}, {"CONF_TYPE": "assetTypeList", "CONF_NAME": "资产卡片分类配置", "CONF_JSON": "[{\"label\":\"架构治理\",\"value\":1,\"color\":\"rgb(36, 101, 255)\",\"bgColor\":\"rgb(237, 242, 255)\",\"children\":[]},{\"label\":\"业务架构\",\"value\":2,\"color\":\"rgb(24, 195, 135)\",\"bgColor\":\"rgb(233, 251, 245)\",\"children\":[]},{\"label\":\"应用架构\",\"value\":3,\"color\":\"rgb(138, 88, 248)\",\"bgColor\":\"rgb(243, 237, 255)\",\"children\":[]},{\"label\":\"数据架构\",\"value\":4,\"color\":\"rgb(250, 100, 1)\",\"bgColor\":\"rgb(255, 239, 228)\",\"children\":[]},{\"label\":\"技术架构\",\"value\":5,\"color\":\"rgb(36, 101, 255)\",\"bgColor\":\"rgb(237, 242, 255)\",\"children\":[]},{\"label\":\"安全架构\",\"value\":6,\"color\":\"rgb(36, 101, 255)\",\"bgColor\":\"rgb(237, 242, 255)\",\"children\":[]},{\"label\":\"其他\",\"value\":7,\"color\":\"rgb(24, 195, 135)\",\"bgColor\":\"rgb(233, 251, 245)\",\"children\":[]}]", "所属用户": "admin"}, {"CONF_TYPE": "architectureMapShowType", "CONF_NAME": "", "CONF_JSON": "2", "所属用户": "zhaokaixi"}, {"CONF_TYPE": "NX_SERVICE_CONFIG", "CONF_NAME": "应用系统关系图谱", "CONF_JSON": "{\"useRltName\":\"S3-F.B.AA.调用\",\"belRltName\":\"S3-F.B.AA.属于\",\"mapRltName\":\"S3-F.B.AA.映射\",\"serviceCiName\":\"S3-F.B.AA.接口服务\",\"serviceTypeName\":\"服务类型\",\"serviceType1\":\"原生服务\",\"serviceType2\":\"映射服务\"}", "所属用户": "ch"}, {"CONF_TYPE": "RULE_CLASS_CONFIG", "CONF_NAME": "步骤带出规则", "CONF_JSON": "S-F.B.业务规则", "所属用户": "ch"}, {"CONF_TYPE": "SPECIFIC_ANALYSIS_SHOW_TYPE", "CONF_NAME": "", "CONF_JSON": "1", "所属用户": "admin"}, {"CONF_TYPE": "IS_APPROVE_KEY", "CONF_NAME": "视图审批流程开关 true-开启审批 其他或没有-不开启审批", "CONF_JSON": "true", "所属用户": "admin"}, {"CONF_TYPE": "BUS_TREE_NAME", "CONF_NAME": "", "CONF_JSON": "S2-F.B.业务能力", "所属用户": "w<PERSON><PERSON><PERSON>"}, {"CONF_TYPE": "assetManageShowType", "CONF_NAME": "", "CONF_JSON": "1", "所属用户": "张苗"}, {"CONF_TYPE": "DEFAULT_IMG", "CONF_NAME": "制品类型管理-新建制品类型功能默认图资源", "CONF_JSON": "[{\"id\": \"8516103577855292\",\"name\": \"活动主视图.png\"},\n{\"id\": \"8516103577855427\",\"name\": \"价值链全景视图.png\"\n}, {\"id\": \"8516103577855445\",\"name\": \"价值链主视图.png\"},\n{\"id\": \"8516103577855469\",\"name\": \"价值流主视图.png\"},\n{\"id\": \"8516103577855477\",\"name\": \"任务主视图.png\"},\n{\"id\": \"8520046433700191\",\"name\": \"上下文关系图.png\"},{\n\"id\": \"8520046433700212\",\"name\": \"业务领域主视图.png\"},{\n\"id\": \"8529286005706019\",\"name\": \"技术架构图.png\"},\n{\"id\": \"8529286005706022\",\"name\": \"功能架构图.png\"}]", "所属用户": "admin"}]