package com.uinnova.product.eam.web.mix.diagram.v2.web;

import com.binary.framework.web.ErrorCode;
import com.binary.framework.web.RemoteResult;
import com.uinnova.product.eam.base.diagram.mix.model.ConvertTemplateDTO;
import com.uinnova.product.eam.base.diagram.mix.model.TemplateDir;
import com.uinnova.product.eam.base.diagram.mix.model.TemplateDirQueryBean;
import com.uinnova.product.eam.service.diagram.ESTemplateDirSvc;
import com.uinnova.product.eam.service.diagram.IEamTemplateDirSvc;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 模板相关接口
 */
@RestController
@RequestMapping("/eam/template")
public class ESTemplateMvc {

    @Autowired
    private IEamTemplateDirSvc eamTemplateDirSvc;

    @Autowired
    private ESTemplateDirSvc esTemplateDirSvc;

    @PostMapping("/convertDiagramAndTemplate")
    @ModDesc(desc = "视图与模板相互转换", pType = List.class, pcType = Long.class, pDesc = "", rDesc = "", rType = Long.class)
    public RemoteResult convertDiagramAndTemplate(@RequestBody ConvertTemplateDTO convertTemplateBean) {
        Integer result = esTemplateDirSvc.convertDiagramAndTemplate(convertTemplateBean);
        //在进行模板转换时，模板类型被删除，返回报错信息
        if(result != null && result == 1) {
            return new RemoteResult(false, ErrorCode.SERVER_ERROR.getCode(), "模板转换异常，模板分类已被删除", null);
        }
        return new RemoteResult("");
    }

    @PostMapping("/convertDiagramToTemplate")
    @ModDesc(desc = "视图转换成模板，保留原来视图新生成一个模板", pType = List.class, pcType = Long.class, pDesc = "", rDesc = "", rType = Long.class)
    public RemoteResult convertDiagramToTemplate(@RequestBody ConvertTemplateDTO convertTemplateBean) {
        Integer result = esTemplateDirSvc.convertDiagramToTemplate(convertTemplateBean);
        //在进行模板转换时，模板类型被删除，返回报错信息
        if(result != null && result == 1) {
            return new RemoteResult(false, ErrorCode.SERVER_ERROR.getCode(), "模板转换异常，模板分类已被删除", null);
        }
        return new RemoteResult(result);
    }

    @GetMapping("/queryTemplateDirs")
    @ModDesc(desc = "查询模板目录信息", pType = List.class, pcType = Long.class, pDesc = "", rDesc = "", rType = Long.class)
    public RemoteResult queryTemplateDir(@RequestParam(defaultValue = "true") Boolean all) {
        return new RemoteResult(esTemplateDirSvc.queryTemplateDirs(all));
    }

    @PostMapping("/queryDiagramByTemplateDirId")
    @ModDesc(desc = "通过模板目录id查询视图", pType = List.class, pcType = Long.class, pDesc = "", rDesc = "", rType = Long.class)
    public RemoteResult queryDiagramByTemplateDirId(@RequestBody TemplateDirQueryBean queryBean) {
        return new RemoteResult(esTemplateDirSvc.queryDiagramByTemplateDirId(queryBean));
    }

    @PostMapping("/queryTemplateDiagrams")
    @ModDesc(desc = "模糊查询模板视图", pType = List.class, pcType = Long.class, pDesc = "", rDesc = "", rType = Long.class)
    public RemoteResult queryTemplateDiagrams(@RequestBody TemplateDirQueryBean queryBean) {
        return new RemoteResult(esTemplateDirSvc.queryDiagramTemplateByLike(queryBean));
    }


    @GetMapping("/deleteTemplateDir")
    @ModDesc(desc = "删除模板目录信息", pType = List.class, pcType = Long.class, pDesc = "视图目录信息", rDesc = "", rType = Long.class)
    public RemoteResult deleteTemplateDir(@RequestParam Long dirId) {
        esTemplateDirSvc.removeTemDirById(dirId);
        return new RemoteResult("");
    }

    @GetMapping("getPublicDirAndTemplate")
    @ModDesc(desc = "查询公共模板目录信息", pType = String.class, pDesc = "", rDesc = "", rType = RemoteResult.class)
    public RemoteResult getPublicDirAndTemplate(@RequestParam String like) {
        List<TemplateDir> templateDirs = esTemplateDirSvc.getPublicDirAndTemplate(like);
        return new RemoteResult(templateDirs);
    }


    @PostMapping("/saveOrUpdateTemplateDirs")
    @ModDesc(desc = "保存模板目录信息", pType = List.class, pcType = Long.class, pDesc = "视图目录信息", rDesc = "", rType = Long.class)
    public RemoteResult saveOrUpdateTemplateDirs(@RequestBody TemplateDir templateDir) {

        return new RemoteResult(eamTemplateDirSvc.saveOrUpdate(templateDir));
    }


}
