package com.uinnova.product.eam.model;

import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;

import java.util.List;

public class CiUpAndDownCdt implements Condition{
	private static final long serialVersionUID = 1L;
	@Comment("起点CI")
	private Long sCiId;
	@Comment("向上层数")
	private Integer up;
	@Comment("向下层数")
	private Integer down;
	@Comment("目标ci分类Ids")
	private List<Long> ciClassIds;
	@Comment(" 关系分类的Ids")
	private List<Long> rltClassIds;
	
	@Comment("起点ciCode")
	private String ciCode;
	
	@Comment("环图类型，1=单环图，2=全环图")
	private Integer type;
	
	public String getCiCode() {
		return ciCode;
	}

	public void setCiCode(String ciCode) {
		this.ciCode = ciCode;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}


	public Long getsCiId() {
		return sCiId;
	}

	public void setsCiId(Long sCiId) {
		this.sCiId = sCiId;
	}

	public Integer getUp() {
		return up;
	}

	public void setUp(Integer up) {
		this.up = up;
	}

	public Integer getDown() {
		return down;
	}

	public void setDown(Integer down) {
		this.down = down;
	}

	public List<Long> getCiClassIds() {
		return ciClassIds;
	}

	public void setCiClassIds(List<Long> ciClassIds) {
		this.ciClassIds = ciClassIds;
	}

	public List<Long> getRltClassIds() {
		return rltClassIds;
	}

	public void setRltClassIds(List<Long> rltClassIds) {
		this.rltClassIds = rltClassIds;
	}


}
