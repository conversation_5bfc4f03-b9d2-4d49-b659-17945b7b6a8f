package com.uino.service.permission.microservice;

import java.util.List;

import com.uino.bean.permission.base.OauthRefreshTokenDetail;
import com.uino.bean.permission.base.OauthTokenDetail;

/**
 * token服务
 * 
 * <AUTHOR>
 *
 */
public interface IOAuthTokenSvc {

    /**
     * 持久化
     * 
     * @param tokenDetail
     * @return
     */
    public Long persistence(OauthTokenDetail tokenDetail);

    /**
     * 根据刷新token获取accesstoken
     * 
     * @param reTokenCode
     * @return
     */
    public OauthTokenDetail getTokenDetailByReTokenCode(String reTokenCode);

    /**
     * 获取token
     *
     * @param tokenCode
     * @return
     */
    public OauthTokenDetail getTokenDetailByCode(String tokenCode);

    /**
     * 获取token
     * 
     * @param authId
     * @return
     */
    public OauthTokenDetail getTokenDetailByAuthId(String authId);

    /**
     * 获取token
     * 
     * @param clientId
     * @param userLoginName
     * @return
     */
    public List<OauthTokenDetail> getTokenDetail(String clientId, String userLoginName);

    /**
     * 根据tokenCode删除token
     * 
     * @param tokenCode
     */
    public void delByCode(String tokenCode);

    /**
     * 根据刷新tokencode删除accesstoken
     * 
     * @param reTokenCode
     */
    public void delByRefreshTokenCode(String reTokenCode);

    // 刷新token相关-----------分割线----------------------
    /**
     * 持久化刷新token
     * 
     * @param reRokenDetail
     * @return
     */
    public Long persistenceRefreshToken(OauthRefreshTokenDetail reRokenDetail);

    /**
     * 根据tokencode获取刷新token
     * 
     * @param tokenCode
     * @return
     */
    public OauthRefreshTokenDetail getRefreshTokenDetail(String tokenCode);

    /**
     * 根据tokencode删除刷新token
     * 
     * @param tokenCode
     */
    public void delRefreshTokenByCode(String tokenCode);
}
