package com.uinnova.product.eam.web.eam.mvc;

import com.binary.framework.util.ControllerUtils;
import com.binary.framework.web.RemoteResult;
import com.binary.jdbc.Page;
import com.binary.json.JSON;
import com.uinnova.product.eam.comm.bean.EamFeedback;
import com.uinnova.product.eam.service.IEamFeedbackSvc;
import com.uinnova.product.vmdb.comm.bean.QueryPageCondition;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uinnova.product.vmdb.comm.util.RestTypeUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 意见反馈接口
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/eam/feedback")
public class EamFeedbackMvc {

    @Autowired
    private IEamFeedbackSvc feedbackSvc;

    /**
     * 意见反馈信息保存
     *
     */
    @PostMapping("/saveFeedback")
    public RemoteResult saveFeedback(@RequestBody String body) {
        EamFeedback feedback = JSON.toObject(body, EamFeedback.class);
        Long result = feedbackSvc.saveFeedback(feedback);
        return new RemoteResult(result);
    }

    @RequestMapping("/queryInfoPage")
    @ModDesc(desc = "分页查询意见反馈数据", pDesc = "条件查询", pType = QueryPageCondition.class, pcType = EamFeedback.class, rDesc = "反馈意见数据", rType = Page.class)
    public void queryInfoPage(HttpServletRequest request, HttpServletResponse response, @RequestBody String body) {
        QueryPageCondition<EamFeedback> pageCdt = RestTypeUtil.toPageCondition(body, EamFeedback.class);
        Page<EamFeedback> result = feedbackSvc.queryInfoPage(pageCdt.getPageNum(), pageCdt.getPageSize(), pageCdt.getCdt(), pageCdt.getOrders());
        ControllerUtils.returnJson(request, response, result);
    }

}
