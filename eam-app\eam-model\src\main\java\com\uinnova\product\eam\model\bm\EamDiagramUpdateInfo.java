package com.uinnova.product.eam.model.bm;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.util.List;

/**
 * 视图更新信息
 * <AUTHOR>
 */
@Data
public class EamDiagramUpdateInfo {
    @Comment("视图名称")
    private String diagramName;
    @Comment("是否新增视图")
    private Boolean newDiagram = false;
    @Comment("新增")
    private Boolean add = false;
    @Comment("修改")
    private Boolean update = false;
    @Comment("删除")
    private Boolean delete = false;
    @Comment("数据更新信息")
    List<EamDataUpdateInfo> dataInfo;

}
