package com.uinnova.product.vmdb.comm.model.ci;




import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;


@Comment("CI分类表[CC_CI_CLASS]")
public class CCcCiClass implements Condition {
    private static final long serialVersionUID = 1L;


    @Comment("ID[ID] operate-Equal[=]")
    private Long id;


    @Comment("ID[ID] operate-In[in]")
    private Long[] ids;


    @Comment("ID[ID] operate-GTEqual[>=]")
    private Long startId;

    @Comment("ID[ID] operate-LTEqual[<=]")
    private Long endId;


    @Comment("分类代码[CLASS_CODE] operate-Like[like]")
    private String classCode;


    @Comment("分类代码[CLASS_CODE] operate-Equal[=]")
    private String classCodeEqual;


    @Comment("分类代码[CLASS_CODE] operate-In[in]")
    private String[] classCodes;


    @Comment("分类名称[CLASS_NAME] operate-Like[like]")
    private String className;


    @Comment("分类名称[CLASS_NAME] operate-Equal[=]")
    private String classNameEqual;


    @Comment("分类名称[CLASS_NAME] operate-In[in]")
    private String[] classNames;


    @Comment("分类代码标准值[CLASS_STD_CODE] operate-Like[like]")
    private String classStdCode;


    @Comment("分类代码标准值[CLASS_STD_CODE] operate-Equal[=]")
    private String classStdCodeEqual;


    @Comment("分类代码标准值[CLASS_STD_CODE] operate-In[in]")
    private String[] classStdCodes;


    @Comment("所属目录[DIR_ID] operate-Equal[=]")
    private Long dirId;


    @Comment("所属目录[DIR_ID] operate-In[in]")
    private Long[] dirIds;


    @Comment("所属目录[DIR_ID] operate-GTEqual[>=]")
    private Long startDirId;

    @Comment("所属目录[DIR_ID] operate-LTEqual[<=]")
    private Long endDirId;


    @Comment("分类类型[CI_TYPE] operate-Equal[=]    分类类型:1=基础CI 2=关系CI")
    private Integer ciType;


    @Comment("分类类型[CI_TYPE] operate-In[in]    分类类型:1=基础CI 2=关系CI")
    private Integer[] ciTypes;


    @Comment("分类类型[CI_TYPE] operate-GTEqual[>=]    分类类型:1=基础CI 2=关系CI")
    private Integer startCiType;

    @Comment("分类类型[CI_TYPE] operate-LTEqual[<=]    分类类型:1=基础CI 2=关系CI")
    private Integer endCiType;


    @Comment("分类类别[CLASS_KIND] operate-Equal[=]    分类类别:1=显示 2=不显示")
    private Integer classKind;


    @Comment("分类类别[CLASS_KIND] operate-In[in]    分类类别:1=显示 2=不显示")
    private Integer[] classKinds;


    @Comment("分类类别[CLASS_KIND] operate-GTEqual[>=]    分类类别:1=显示 2=不显示")
    private Integer startClassKind;

    @Comment("分类类别[CLASS_KIND] operate-LTEqual[<=]    分类类别:1=显示 2=不显示")
    private Integer endClassKind;


    @Comment("容器类型[CONTAINER_TYPE] operate-Equal[=]    分类下的CI是否是容器: 0=不是容器 1=默认容器")
    private Integer containerType;


    @Comment("容器类型[CONTAINER_TYPE] operate-In[in]    分类下的CI是否是容器: 0=不是容器 1=默认容器")
    private Integer[] containerTypes;


    @Comment("容器类型[CONTAINER_TYPE] operate-GTEqual[>=]    分类下的CI是否是容器: 0=不是容器 1=默认容器")
    private Integer startContainerType;

    @Comment("容器类型[CONTAINER_TYPE] operate-LTEqual[<=]    分类下的CI是否是容器: 0=不是容器 1=默认容器")
    private Integer endContainerType;


    @Comment("上级分类ID[PARENT_ID] operate-Equal[=]")
    private Long parentId;


    @Comment("上级分类ID[PARENT_ID] operate-In[in]")
    private Long[] parentIds;


    @Comment("上级分类ID[PARENT_ID] operate-GTEqual[>=]")
    private Long startParentId;

    @Comment("上级分类ID[PARENT_ID] operate-LTEqual[<=]")
    private Long endParentId;


    @Comment("分类层级级别[CLASS_LVL] operate-Equal[=]")
    private Integer classLvl;


    @Comment("分类层级级别[CLASS_LVL] operate-In[in]")
    private Integer[] classLvls;


    @Comment("分类层级级别[CLASS_LVL] operate-GTEqual[>=]")
    private Integer startClassLvl;

    @Comment("分类层级级别[CLASS_LVL] operate-LTEqual[<=]")
    private Integer endClassLvl;


    @Comment("分类层级路径[CLASS_PATH] operate-Like[like]    分类层级路径:例：#1#2#7#")
    private String classPath;


    @Comment("显示排序[ORDER_NO] operate-Equal[=]")
    private Integer orderNo;


    @Comment("显示排序[ORDER_NO] operate-In[in]")
    private Integer[] orderNos;


    @Comment("显示排序[ORDER_NO] operate-GTEqual[>=]")
    private Integer startOrderNo;

    @Comment("显示排序[ORDER_NO] operate-LTEqual[<=]")
    private Integer endOrderNo;


    @Comment("是否末级[IS_LEAF] operate-Equal[=]    是否末级:1=是 0=否")
    private Integer isLeaf;


    @Comment("是否末级[IS_LEAF] operate-In[in]    是否末级:1=是 0=否")
    private Integer[] isLeafs;


    @Comment("是否末级[IS_LEAF] operate-GTEqual[>=]    是否末级:1=是 0=否")
    private Integer startIsLeaf;

    @Comment("是否末级[IS_LEAF] operate-LTEqual[<=]    是否末级:1=是 0=否")
    private Integer endIsLeaf;


    @Comment("分类图标[ICON] operate-Like[like]")
    private String icon;


    @Comment("分类图标[ICON] operate-Equal[=]")
    private String iconEqual;


    @Comment("分类图标[ICON] operate-In[in]")
    private String[] icons;


    @Comment("分类图形[SHAPE] operate-Like[like]    DMV中分类对应的形状")
    private String shape;


    @Comment("分类图形[SHAPE] operate-Equal[=]    DMV中分类对应的形状")
    private String shapeEqual;


    @Comment("分类图形[SHAPE] operate-In[in]    DMV中分类对应的形状")
    private String[] shapes;


    @Comment("分类描述[CLASS_DESC] operate-Like[like]")
    private String classDesc;


    @Comment("分类颜色[CLASS_COLOR] operate-Like[like]")
    private String classColor;


    @Comment("分类颜色[CLASS_COLOR] operate-Equal[=]")
    private String classColorEqual;


    @Comment("分类颜色[CLASS_COLOR] operate-In[in]")
    private String[] classColors;


    @Comment("显示字段ID[DISP_FIELD_IDS] operate-Like[like]    显示字段ID:多个以逗号分隔")
    private String dispFieldIds;


    @Comment("显示字段ID[DISP_FIELD_IDS] operate-Equal[=]    显示字段ID:多个以逗号分隔")
    private String dispFieldIdsEqual;


    @Comment("显示字段ID[DISP_FIELD_IDS] operate-In[in]    显示字段ID:多个以逗号分隔")
    private String[] dispFieldIdss;


    @Comment("显示字段名称[DISP_FIELD_NAMES] operate-Like[like]    显示字段名称:多个以逗号分隔")
    private String dispFieldNames;


    @Comment("是否归集[COST_TYPE] operate-Equal[=]    是否归集:1=不归集 2=归集(源->目标) 3=归集(目标->源)")
    private Integer costType;


    @Comment("是否归集[COST_TYPE] operate-In[in]    是否归集:1=不归集 2=归集(源->目标) 3=归集(目标->源)")
    private Integer[] costTypes;


    @Comment("是否归集[COST_TYPE] operate-GTEqual[>=]    是否归集:1=不归集 2=归集(源->目标) 3=归集(目标->源)")
    private Integer startCostType;

    @Comment("是否归集[COST_TYPE] operate-LTEqual[<=]    是否归集:1=不归集 2=归集(源->目标) 3=归集(目标->源)")
    private Integer endCostType;


    @Comment("关系样式[LINE_TYPE] operate-Like[like]")
    private String lineType;


    @Comment("关系样式[LINE_TYPE] operate-Equal[=]")
    private String lineTypeEqual;


    @Comment("关系样式[LINE_TYPE] operate-In[in]")
    private String[] lineTypes;


    @Comment("关系宽度[LINE_BORDER] operate-Equal[=]")
    private Integer lineBorder;


    @Comment("关系宽度[LINE_BORDER] operate-In[in]")
    private Integer[] lineBorders;


    @Comment("关系宽度[LINE_BORDER] operate-GTEqual[>=]")
    private Integer startLineBorder;

    @Comment("关系宽度[LINE_BORDER] operate-LTEqual[<=]")
    private Integer endLineBorder;


    @Comment("关系颜色[LINE_COLOR] operate-Like[like]")
    private String lineColor;


    @Comment("关系颜色[LINE_COLOR] operate-Equal[=]")
    private String lineColorEqual;


    @Comment("关系颜色[LINE_COLOR] operate-In[in]")
    private String[] lineColors;


    @Comment("关系EA样式[LINE_EA_STYLE] operate-Like[like]    DMV画线使用的EA类型样式名称")
    private String lineEaStyle;


    @Comment("关系EA样式[LINE_EA_STYLE] operate-Equal[=]    DMV画线使用的EA类型样式名称")
    private String lineEaStyleEqual;


    @Comment("关系EA样式[LINE_EA_STYLE] operate-In[in]    DMV画线使用的EA类型样式名称")
    private String[] lineEaStyles;


    @Comment("关系箭头[LINE_DIRECT] operate-Like[like]")
    private String lineDirect;


    @Comment("关系箭头[LINE_DIRECT] operate-Equal[=]")
    private String lineDirectEqual;


    @Comment("关系箭头[LINE_DIRECT] operate-In[in]")
    private String[] lineDirects;


    @Comment("关系动态效果[LINE_ANIME] operate-Equal[=]    关系动态效果:1=是 0=否")
    private Integer lineAnime;


    @Comment("关系动态效果[LINE_ANIME] operate-In[in]    关系动态效果:1=是 0=否")
    private Integer[] lineAnimes;


    @Comment("关系动态效果[LINE_ANIME] operate-GTEqual[>=]    关系动态效果:1=是 0=否")
    private Integer startLineAnime;

    @Comment("关系动态效果[LINE_ANIME] operate-LTEqual[<=]    关系动态效果:1=是 0=否")
    private Integer endLineAnime;


    @Comment("关系显示类型[LINE_DISP_TYPE] operate-Equal[=]    关系显示类型:1=普通类型 2=容器类型")
    private Integer lineDispType;


    @Comment("关系显示类型[LINE_DISP_TYPE] operate-In[in]    关系显示类型:1=普通类型 2=容器类型")
    private Integer[] lineDispTypes;


    @Comment("关系显示类型[LINE_DISP_TYPE] operate-GTEqual[>=]    关系显示类型:1=普通类型 2=容器类型")
    private Integer startLineDispType;

    @Comment("关系显示类型[LINE_DISP_TYPE] operate-LTEqual[<=]    关系显示类型:1=普通类型 2=容器类型")
    private Integer endLineDispType;


    @Comment("关系标签位置[LINE_LABEL_ALIGN] operate-Equal[=]    关系标签位置:1=偏向源 2=偏向目标 3=居中")
    private Integer lineLabelAlign;


    @Comment("关系标签位置[LINE_LABEL_ALIGN] operate-In[in]    关系标签位置:1=偏向源 2=偏向目标 3=居中")
    private Integer[] lineLabelAligns;


    @Comment("关系标签位置[LINE_LABEL_ALIGN] operate-GTEqual[>=]    关系标签位置:1=偏向源 2=偏向目标 3=居中")
    private Integer startLineLabelAlign;

    @Comment("关系标签位置[LINE_LABEL_ALIGN] operate-LTEqual[<=]    关系标签位置:1=偏向源 2=偏向目标 3=居中")
    private Integer endLineLabelAlign;


    @Comment("所属域[DOMAIN_ID] operate-Equal[=]")
    private Long domainId;


    @Comment("所属域[DOMAIN_ID] operate-In[in]")
    private Long[] domainIds;


    @Comment("所属域[DOMAIN_ID] operate-GTEqual[>=]")
    private Long startDomainId;

    @Comment("所属域[DOMAIN_ID] operate-LTEqual[<=]")
    private Long endDomainId;


    @Comment("数据状态[DATA_STATUS] operate-Equal[=]    数据状态:0=删除，1=正常")
    private Integer dataStatus;


    @Comment("数据状态[DATA_STATUS] operate-In[in]    数据状态:0=删除，1=正常")
    private Integer[] dataStatuss;


    @Comment("数据状态[DATA_STATUS] operate-GTEqual[>=]    数据状态:0=删除，1=正常")
    private Integer startDataStatus;

    @Comment("数据状态[DATA_STATUS] operate-LTEqual[<=]    数据状态:0=删除，1=正常")
    private Integer endDataStatus;


    @Comment("创建人[CREATOR] operate-Like[like]")
    private String creator;


    @Comment("创建人[CREATOR] operate-Equal[=]")
    private String creatorEqual;


    @Comment("创建人[CREATOR] operate-In[in]")
    private String[] creators;


    @Comment("修改人[MODIFIER] operate-Like[like]")
    private String modifier;


    @Comment("修改人[MODIFIER] operate-Equal[=]")
    private String modifierEqual;


    @Comment("修改人[MODIFIER] operate-In[in]")
    private String[] modifiers;


    @Comment("创建时间[CREATE_TIME] operate-Equal[=]    创建时间:yyyyMMddHHmmss")
    private Long createTime;


    @Comment("创建时间[CREATE_TIME] operate-In[in]    创建时间:yyyyMMddHHmmss")
    private Long[] createTimes;


    @Comment("创建时间[CREATE_TIME] operate-GTEqual[>=]    创建时间:yyyyMMddHHmmss")
    private Long startCreateTime;

    @Comment("创建时间[CREATE_TIME] operate-LTEqual[<=]    创建时间:yyyyMMddHHmmss")
    private Long endCreateTime;


    @Comment("更新时间[MODIFY_TIME] operate-Equal[=]    更新时间:yyyyMMddHHmmss")
    private Long modifyTime;


    @Comment("更新时间[MODIFY_TIME] operate-In[in]    更新时间:yyyyMMddHHmmss")
    private Long[] modifyTimes;


    @Comment("更新时间[MODIFY_TIME] operate-GTEqual[>=]    更新时间:yyyyMMddHHmmss")
    private Long startModifyTime;

    @Comment("更新时间[MODIFY_TIME] operate-LTEqual[<=]    更新时间:yyyyMMddHHmmss")
    private Long endModifyTime;




    public Long getId() {
        return this.id;
    }
    public void setId(Long id) {
        this.id = id;
    }


    public Long[] getIds() {
        return this.ids;
    }
    public void setIds(Long[] ids) {
        this.ids = ids;
    }


    public Long getStartId() {
        return this.startId;
    }
    public void setStartId(Long startId) {
        this.startId = startId;
    }


    public Long getEndId() {
        return this.endId;
    }
    public void setEndId(Long endId) {
        this.endId = endId;
    }


    public String getClassCode() {
        return this.classCode;
    }
    public void setClassCode(String classCode) {
        this.classCode = classCode;
    }


    public String getClassCodeEqual() {
        return this.classCodeEqual;
    }
    public void setClassCodeEqual(String classCodeEqual) {
        this.classCodeEqual = classCodeEqual;
    }


    public String[] getClassCodes() {
        return this.classCodes;
    }
    public void setClassCodes(String[] classCodes) {
        this.classCodes = classCodes;
    }


    public String getClassName() {
        return this.className;
    }
    public void setClassName(String className) {
        this.className = className;
    }


    public String getClassNameEqual() {
        return this.classNameEqual;
    }
    public void setClassNameEqual(String classNameEqual) {
        this.classNameEqual = classNameEqual;
    }


    public String[] getClassNames() {
        return this.classNames;
    }
    public void setClassNames(String[] classNames) {
        this.classNames = classNames;
    }


    public String getClassStdCode() {
        return this.classStdCode;
    }
    public void setClassStdCode(String classStdCode) {
        this.classStdCode = classStdCode;
    }


    public String getClassStdCodeEqual() {
        return this.classStdCodeEqual;
    }
    public void setClassStdCodeEqual(String classStdCodeEqual) {
        this.classStdCodeEqual = classStdCodeEqual;
    }


    public String[] getClassStdCodes() {
        return this.classStdCodes;
    }
    public void setClassStdCodes(String[] classStdCodes) {
        this.classStdCodes = classStdCodes;
    }


    public Long getDirId() {
        return this.dirId;
    }
    public void setDirId(Long dirId) {
        this.dirId = dirId;
    }


    public Long[] getDirIds() {
        return this.dirIds;
    }
    public void setDirIds(Long[] dirIds) {
        this.dirIds = dirIds;
    }


    public Long getStartDirId() {
        return this.startDirId;
    }
    public void setStartDirId(Long startDirId) {
        this.startDirId = startDirId;
    }


    public Long getEndDirId() {
        return this.endDirId;
    }
    public void setEndDirId(Long endDirId) {
        this.endDirId = endDirId;
    }


    public Integer getCiType() {
        return this.ciType;
    }
    public void setCiType(Integer ciType) {
        this.ciType = ciType;
    }


    public Integer[] getCiTypes() {
        return this.ciTypes;
    }
    public void setCiTypes(Integer[] ciTypes) {
        this.ciTypes = ciTypes;
    }


    public Integer getStartCiType() {
        return this.startCiType;
    }
    public void setStartCiType(Integer startCiType) {
        this.startCiType = startCiType;
    }


    public Integer getEndCiType() {
        return this.endCiType;
    }
    public void setEndCiType(Integer endCiType) {
        this.endCiType = endCiType;
    }


    public Integer getClassKind() {
        return this.classKind;
    }
    public void setClassKind(Integer classKind) {
        this.classKind = classKind;
    }


    public Integer[] getClassKinds() {
        return this.classKinds;
    }
    public void setClassKinds(Integer[] classKinds) {
        this.classKinds = classKinds;
    }


    public Integer getStartClassKind() {
        return this.startClassKind;
    }
    public void setStartClassKind(Integer startClassKind) {
        this.startClassKind = startClassKind;
    }


    public Integer getEndClassKind() {
        return this.endClassKind;
    }
    public void setEndClassKind(Integer endClassKind) {
        this.endClassKind = endClassKind;
    }


    public Integer getContainerType() {
        return this.containerType;
    }
    public void setContainerType(Integer containerType) {
        this.containerType = containerType;
    }


    public Integer[] getContainerTypes() {
        return this.containerTypes;
    }
    public void setContainerTypes(Integer[] containerTypes) {
        this.containerTypes = containerTypes;
    }


    public Integer getStartContainerType() {
        return this.startContainerType;
    }
    public void setStartContainerType(Integer startContainerType) {
        this.startContainerType = startContainerType;
    }


    public Integer getEndContainerType() {
        return this.endContainerType;
    }
    public void setEndContainerType(Integer endContainerType) {
        this.endContainerType = endContainerType;
    }


    public Long getParentId() {
        return this.parentId;
    }
    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }


    public Long[] getParentIds() {
        return this.parentIds;
    }
    public void setParentIds(Long[] parentIds) {
        this.parentIds = parentIds;
    }


    public Long getStartParentId() {
        return this.startParentId;
    }
    public void setStartParentId(Long startParentId) {
        this.startParentId = startParentId;
    }


    public Long getEndParentId() {
        return this.endParentId;
    }
    public void setEndParentId(Long endParentId) {
        this.endParentId = endParentId;
    }


    public Integer getClassLvl() {
        return this.classLvl;
    }
    public void setClassLvl(Integer classLvl) {
        this.classLvl = classLvl;
    }


    public Integer[] getClassLvls() {
        return this.classLvls;
    }
    public void setClassLvls(Integer[] classLvls) {
        this.classLvls = classLvls;
    }


    public Integer getStartClassLvl() {
        return this.startClassLvl;
    }
    public void setStartClassLvl(Integer startClassLvl) {
        this.startClassLvl = startClassLvl;
    }


    public Integer getEndClassLvl() {
        return this.endClassLvl;
    }
    public void setEndClassLvl(Integer endClassLvl) {
        this.endClassLvl = endClassLvl;
    }


    public String getClassPath() {
        return this.classPath;
    }
    public void setClassPath(String classPath) {
        this.classPath = classPath;
    }


    public Integer getOrderNo() {
        return this.orderNo;
    }
    public void setOrderNo(Integer orderNo) {
        this.orderNo = orderNo;
    }


    public Integer[] getOrderNos() {
        return this.orderNos;
    }
    public void setOrderNos(Integer[] orderNos) {
        this.orderNos = orderNos;
    }


    public Integer getStartOrderNo() {
        return this.startOrderNo;
    }
    public void setStartOrderNo(Integer startOrderNo) {
        this.startOrderNo = startOrderNo;
    }


    public Integer getEndOrderNo() {
        return this.endOrderNo;
    }
    public void setEndOrderNo(Integer endOrderNo) {
        this.endOrderNo = endOrderNo;
    }


    public Integer getIsLeaf() {
        return this.isLeaf;
    }
    public void setIsLeaf(Integer isLeaf) {
        this.isLeaf = isLeaf;
    }


    public Integer[] getIsLeafs() {
        return this.isLeafs;
    }
    public void setIsLeafs(Integer[] isLeafs) {
        this.isLeafs = isLeafs;
    }


    public Integer getStartIsLeaf() {
        return this.startIsLeaf;
    }
    public void setStartIsLeaf(Integer startIsLeaf) {
        this.startIsLeaf = startIsLeaf;
    }


    public Integer getEndIsLeaf() {
        return this.endIsLeaf;
    }
    public void setEndIsLeaf(Integer endIsLeaf) {
        this.endIsLeaf = endIsLeaf;
    }


    public String getIcon() {
        return this.icon;
    }
    public void setIcon(String icon) {
        this.icon = icon;
    }


    public String getIconEqual() {
        return this.iconEqual;
    }
    public void setIconEqual(String iconEqual) {
        this.iconEqual = iconEqual;
    }


    public String[] getIcons() {
        return this.icons;
    }
    public void setIcons(String[] icons) {
        this.icons = icons;
    }


    public String getShape() {
        return this.shape;
    }
    public void setShape(String shape) {
        this.shape = shape;
    }


    public String getShapeEqual() {
        return this.shapeEqual;
    }
    public void setShapeEqual(String shapeEqual) {
        this.shapeEqual = shapeEqual;
    }


    public String[] getShapes() {
        return this.shapes;
    }
    public void setShapes(String[] shapes) {
        this.shapes = shapes;
    }


    public String getClassDesc() {
        return this.classDesc;
    }
    public void setClassDesc(String classDesc) {
        this.classDesc = classDesc;
    }


    public String getClassColor() {
        return this.classColor;
    }
    public void setClassColor(String classColor) {
        this.classColor = classColor;
    }


    public String getClassColorEqual() {
        return this.classColorEqual;
    }
    public void setClassColorEqual(String classColorEqual) {
        this.classColorEqual = classColorEqual;
    }


    public String[] getClassColors() {
        return this.classColors;
    }
    public void setClassColors(String[] classColors) {
        this.classColors = classColors;
    }


    public String getDispFieldIds() {
        return this.dispFieldIds;
    }
    public void setDispFieldIds(String dispFieldIds) {
        this.dispFieldIds = dispFieldIds;
    }


    public String getDispFieldIdsEqual() {
        return this.dispFieldIdsEqual;
    }
    public void setDispFieldIdsEqual(String dispFieldIdsEqual) {
        this.dispFieldIdsEqual = dispFieldIdsEqual;
    }


    public String[] getDispFieldIdss() {
        return this.dispFieldIdss;
    }
    public void setDispFieldIdss(String[] dispFieldIdss) {
        this.dispFieldIdss = dispFieldIdss;
    }


    public String getDispFieldNames() {
        return this.dispFieldNames;
    }
    public void setDispFieldNames(String dispFieldNames) {
        this.dispFieldNames = dispFieldNames;
    }


    public Integer getCostType() {
        return this.costType;
    }
    public void setCostType(Integer costType) {
        this.costType = costType;
    }


    public Integer[] getCostTypes() {
        return this.costTypes;
    }
    public void setCostTypes(Integer[] costTypes) {
        this.costTypes = costTypes;
    }


    public Integer getStartCostType() {
        return this.startCostType;
    }
    public void setStartCostType(Integer startCostType) {
        this.startCostType = startCostType;
    }


    public Integer getEndCostType() {
        return this.endCostType;
    }
    public void setEndCostType(Integer endCostType) {
        this.endCostType = endCostType;
    }


    public String getLineType() {
        return this.lineType;
    }
    public void setLineType(String lineType) {
        this.lineType = lineType;
    }


    public String getLineTypeEqual() {
        return this.lineTypeEqual;
    }
    public void setLineTypeEqual(String lineTypeEqual) {
        this.lineTypeEqual = lineTypeEqual;
    }


    public String[] getLineTypes() {
        return this.lineTypes;
    }
    public void setLineTypes(String[] lineTypes) {
        this.lineTypes = lineTypes;
    }


    public Integer getLineBorder() {
        return this.lineBorder;
    }
    public void setLineBorder(Integer lineBorder) {
        this.lineBorder = lineBorder;
    }


    public Integer[] getLineBorders() {
        return this.lineBorders;
    }
    public void setLineBorders(Integer[] lineBorders) {
        this.lineBorders = lineBorders;
    }


    public Integer getStartLineBorder() {
        return this.startLineBorder;
    }
    public void setStartLineBorder(Integer startLineBorder) {
        this.startLineBorder = startLineBorder;
    }


    public Integer getEndLineBorder() {
        return this.endLineBorder;
    }
    public void setEndLineBorder(Integer endLineBorder) {
        this.endLineBorder = endLineBorder;
    }


    public String getLineColor() {
        return this.lineColor;
    }
    public void setLineColor(String lineColor) {
        this.lineColor = lineColor;
    }


    public String getLineColorEqual() {
        return this.lineColorEqual;
    }
    public void setLineColorEqual(String lineColorEqual) {
        this.lineColorEqual = lineColorEqual;
    }


    public String[] getLineColors() {
        return this.lineColors;
    }
    public void setLineColors(String[] lineColors) {
        this.lineColors = lineColors;
    }


    public String getLineEaStyle() {
        return this.lineEaStyle;
    }
    public void setLineEaStyle(String lineEaStyle) {
        this.lineEaStyle = lineEaStyle;
    }


    public String getLineEaStyleEqual() {
        return this.lineEaStyleEqual;
    }
    public void setLineEaStyleEqual(String lineEaStyleEqual) {
        this.lineEaStyleEqual = lineEaStyleEqual;
    }


    public String[] getLineEaStyles() {
        return this.lineEaStyles;
    }
    public void setLineEaStyles(String[] lineEaStyles) {
        this.lineEaStyles = lineEaStyles;
    }


    public String getLineDirect() {
        return this.lineDirect;
    }
    public void setLineDirect(String lineDirect) {
        this.lineDirect = lineDirect;
    }


    public String getLineDirectEqual() {
        return this.lineDirectEqual;
    }
    public void setLineDirectEqual(String lineDirectEqual) {
        this.lineDirectEqual = lineDirectEqual;
    }


    public String[] getLineDirects() {
        return this.lineDirects;
    }
    public void setLineDirects(String[] lineDirects) {
        this.lineDirects = lineDirects;
    }


    public Integer getLineAnime() {
        return this.lineAnime;
    }
    public void setLineAnime(Integer lineAnime) {
        this.lineAnime = lineAnime;
    }


    public Integer[] getLineAnimes() {
        return this.lineAnimes;
    }
    public void setLineAnimes(Integer[] lineAnimes) {
        this.lineAnimes = lineAnimes;
    }


    public Integer getStartLineAnime() {
        return this.startLineAnime;
    }
    public void setStartLineAnime(Integer startLineAnime) {
        this.startLineAnime = startLineAnime;
    }


    public Integer getEndLineAnime() {
        return this.endLineAnime;
    }
    public void setEndLineAnime(Integer endLineAnime) {
        this.endLineAnime = endLineAnime;
    }


    public Integer getLineDispType() {
        return this.lineDispType;
    }
    public void setLineDispType(Integer lineDispType) {
        this.lineDispType = lineDispType;
    }


    public Integer[] getLineDispTypes() {
        return this.lineDispTypes;
    }
    public void setLineDispTypes(Integer[] lineDispTypes) {
        this.lineDispTypes = lineDispTypes;
    }


    public Integer getStartLineDispType() {
        return this.startLineDispType;
    }
    public void setStartLineDispType(Integer startLineDispType) {
        this.startLineDispType = startLineDispType;
    }


    public Integer getEndLineDispType() {
        return this.endLineDispType;
    }
    public void setEndLineDispType(Integer endLineDispType) {
        this.endLineDispType = endLineDispType;
    }


    public Integer getLineLabelAlign() {
        return this.lineLabelAlign;
    }
    public void setLineLabelAlign(Integer lineLabelAlign) {
        this.lineLabelAlign = lineLabelAlign;
    }


    public Integer[] getLineLabelAligns() {
        return this.lineLabelAligns;
    }
    public void setLineLabelAligns(Integer[] lineLabelAligns) {
        this.lineLabelAligns = lineLabelAligns;
    }


    public Integer getStartLineLabelAlign() {
        return this.startLineLabelAlign;
    }
    public void setStartLineLabelAlign(Integer startLineLabelAlign) {
        this.startLineLabelAlign = startLineLabelAlign;
    }


    public Integer getEndLineLabelAlign() {
        return this.endLineLabelAlign;
    }
    public void setEndLineLabelAlign(Integer endLineLabelAlign) {
        this.endLineLabelAlign = endLineLabelAlign;
    }


    public Long getDomainId() {
        return this.domainId;
    }
    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }


    public Long[] getDomainIds() {
        return this.domainIds;
    }
    public void setDomainIds(Long[] domainIds) {
        this.domainIds = domainIds;
    }


    public Long getStartDomainId() {
        return this.startDomainId;
    }
    public void setStartDomainId(Long startDomainId) {
        this.startDomainId = startDomainId;
    }


    public Long getEndDomainId() {
        return this.endDomainId;
    }
    public void setEndDomainId(Long endDomainId) {
        this.endDomainId = endDomainId;
    }


    public Integer getDataStatus() {
        return this.dataStatus;
    }
    public void setDataStatus(Integer dataStatus) {
        this.dataStatus = dataStatus;
    }


    public Integer[] getDataStatuss() {
        return this.dataStatuss;
    }
    public void setDataStatuss(Integer[] dataStatuss) {
        this.dataStatuss = dataStatuss;
    }


    public Integer getStartDataStatus() {
        return this.startDataStatus;
    }
    public void setStartDataStatus(Integer startDataStatus) {
        this.startDataStatus = startDataStatus;
    }


    public Integer getEndDataStatus() {
        return this.endDataStatus;
    }
    public void setEndDataStatus(Integer endDataStatus) {
        this.endDataStatus = endDataStatus;
    }


    public String getCreator() {
        return this.creator;
    }
    public void setCreator(String creator) {
        this.creator = creator;
    }


    public String getCreatorEqual() {
        return this.creatorEqual;
    }
    public void setCreatorEqual(String creatorEqual) {
        this.creatorEqual = creatorEqual;
    }


    public String[] getCreators() {
        return this.creators;
    }
    public void setCreators(String[] creators) {
        this.creators = creators;
    }


    public String getModifier() {
        return this.modifier;
    }
    public void setModifier(String modifier) {
        this.modifier = modifier;
    }


    public String getModifierEqual() {
        return this.modifierEqual;
    }
    public void setModifierEqual(String modifierEqual) {
        this.modifierEqual = modifierEqual;
    }


    public String[] getModifiers() {
        return this.modifiers;
    }
    public void setModifiers(String[] modifiers) {
        this.modifiers = modifiers;
    }


    public Long getCreateTime() {
        return this.createTime;
    }
    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }


    public Long[] getCreateTimes() {
        return this.createTimes;
    }
    public void setCreateTimes(Long[] createTimes) {
        this.createTimes = createTimes;
    }


    public Long getStartCreateTime() {
        return this.startCreateTime;
    }
    public void setStartCreateTime(Long startCreateTime) {
        this.startCreateTime = startCreateTime;
    }


    public Long getEndCreateTime() {
        return this.endCreateTime;
    }
    public void setEndCreateTime(Long endCreateTime) {
        this.endCreateTime = endCreateTime;
    }


    public Long getModifyTime() {
        return this.modifyTime;
    }
    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }


    public Long[] getModifyTimes() {
        return this.modifyTimes;
    }
    public void setModifyTimes(Long[] modifyTimes) {
        this.modifyTimes = modifyTimes;
    }


    public Long getStartModifyTime() {
        return this.startModifyTime;
    }
    public void setStartModifyTime(Long startModifyTime) {
        this.startModifyTime = startModifyTime;
    }


    public Long getEndModifyTime() {
        return this.endModifyTime;
    }
    public void setEndModifyTime(Long endModifyTime) {
        this.endModifyTime = endModifyTime;
    }


}


