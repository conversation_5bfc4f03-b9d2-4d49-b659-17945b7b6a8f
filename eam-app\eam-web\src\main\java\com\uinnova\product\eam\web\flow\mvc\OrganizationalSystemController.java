package com.uinnova.product.eam.web.flow.mvc;

import com.binary.framework.util.ControllerUtils;
import com.binary.framework.web.RemoteResult;
import com.binary.jdbc.Page;
import com.uinnova.product.eam.base.cj.ResultMsg;
import com.uinnova.product.eam.model.DepartmentDto;
import com.uinnova.product.eam.model.ObjectMovingDto;
import com.uinnova.product.eam.model.vo.OrganizationalSystemParamVo;
import com.uinnova.product.eam.model.vo.OrganizationalSystemVo;
import com.uinnova.product.eam.service.OrganizationalSystemSvc;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.business.ImportExcelMessage;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 组织体系
 *
 * <AUTHOR>
 * @since 2024/7/9 上午10:10
 */
@RequestMapping("/flowManager/organizationalSystem")
@RestController
public class OrganizationalSystemController {

    @Resource
    private OrganizationalSystemSvc organizationalSystemSvc;

    @GetMapping("/getPositionName")
    public RemoteResult getPositionName() {
        String name = organizationalSystemSvc.getPositionName();
        return new RemoteResult(name);
    }

    @PostMapping("/saveOrUpdate")
    public RemoteResult saveOrUpdate(@RequestBody OrganizationalSystemVo organizationalSystemVo){
        Boolean result = organizationalSystemSvc.saveOrUpdate(organizationalSystemVo);
        return new RemoteResult(result);
    }

    @PostMapping("/remove")
    public RemoteResult remove(@RequestBody OrganizationalSystemVo organizationalSystemVo){
        Boolean result = organizationalSystemSvc.remove(organizationalSystemVo.getCiCode());
        return new RemoteResult(result);
    }

    @PostMapping("/findOrganizationalSystemList")
    public RemoteResult findOrganizationalSystemList(@RequestBody OrganizationalSystemParamVo paramVo){
        Page<ESCIInfo> result = organizationalSystemSvc.findOrganizationalSystemList(paramVo);
        return new RemoteResult(result);
    }

    @PostMapping("/findOrganizationalSystemListNew")
    public RemoteResult findOrganizationalSystemListNew(@RequestBody OrganizationalSystemParamVo paramVo){
        Page<ESCIInfo> result = organizationalSystemSvc.findOrganizationalSystemListNew(paramVo);
        return new RemoteResult(result);
    }

    //移动岗位
    @PostMapping("/movePosition")
    public RemoteResult movePosition(@RequestBody ObjectMovingDto objectMovingDto){
        Boolean result = organizationalSystemSvc.movePosition(objectMovingDto);
        return new RemoteResult(result);
    }
    //查看当前岗位关联信息
    @GetMapping("/getDetail")
    public RemoteResult getDetail(@RequestParam String ciCode){
        List<Map<String, String>> result = organizationalSystemSvc.getDetail(ciCode);
        return new RemoteResult(result);
    }
    //导出当前岗位信息
    @GetMapping("/exportPosition")
    public ResultMsg exportPlan(HttpServletResponse response, @RequestParam String ciCode) {
        String path = organizationalSystemSvc.exportPosition(response, ciCode);
        return new ResultMsg("导出成功",path);
    }
    //获取部门树
    @GetMapping("/getOrganizationalSystemTree")
    public RemoteResult getOrganizationalSystemTree(){
        List<DepartmentDto> result = organizationalSystemSvc.getOrganizationalSystemTree();
        return new RemoteResult(result);
    }

    @RequestMapping(value = "/importPostExcelBatch", method = RequestMethod.POST)
    @ModDesc(desc = "一键导入解析岗位Excel文件", pDesc = "XLS或者XLSX文件", pType = MultipartFile.class, rDesc = "文件解析内容", rType = ImportExcelMessage.class)
    public void importPostExcelBatch(HttpServletRequest request, HttpServletResponse response,
                                   @RequestParam("file") MultipartFile file,
                                   @RequestParam Long id
    ) {
        ImportExcelMessage result = organizationalSystemSvc.importPostExcelBatch(file,id);
        ControllerUtils.returnJson(request, response, result);
    }
}
