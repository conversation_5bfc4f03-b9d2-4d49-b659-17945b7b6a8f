package com.uino.bean.monitor.buiness.unit;

import java.io.Serializable;

/**
 * 单位转换模型项
 * 
 * <AUTHOR>
 *
 */
public class UnitModelItem implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 所属模型id
     */
    private Long modelId;

    /**
     * 当前单位等级
     */
    private Integer level;

    /**
     * 单位名称
     */
    private String unitName;

    public UnitModelItem() {
        super();
    }

    public UnitModelItem(Long modelId, Integer level, String unitName) {
        super();
        this.modelId = modelId;
        this.level = level;
        this.unitName = unitName;
    }

    public Long getModelId() {
        return modelId;
    }

    public void setModelId(Long modelId) {
        this.modelId = modelId;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

}
