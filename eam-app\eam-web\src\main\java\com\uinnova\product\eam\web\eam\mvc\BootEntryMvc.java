package com.uinnova.product.eam.web.eam.mvc;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.binary.framework.web.RemoteResult;
import com.uinnova.product.eam.comm.model.es.BootEntryUserConfig;
import com.uinnova.product.eam.comm.model.es.BootPageProgress;
import com.uinnova.product.eam.model.BootEntryBo;
import com.uinnova.product.eam.model.vo.BootEntryVo;
import com.uinnova.product.eam.service.BootEntrySvc;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/eam/bootEntry")
public class BootEntryMvc {

    @Autowired
    private BootEntrySvc bootEntrySvc;

    @PostMapping("/saveOrUpdateEntry")
    public RemoteResult saveOrUpdateEntry(@RequestBody BootEntryBo bootEntryBo) {
        Long result = bootEntrySvc.saveOrUpdateEntry(bootEntryBo);
        return new RemoteResult(result);
    }

    @GetMapping("/getBootEntryList")
    public RemoteResult getBootEntryList() {
        List<BootEntryVo> result = bootEntrySvc.getBootEntryList();
        return new RemoteResult(result);
    }

    @GetMapping("/deleteBootEntry")
    public RemoteResult deleteBootEntry(@RequestParam Long bootEntryId, @RequestParam Long userRoleId) {
        int result = bootEntrySvc.deleteBootEntry(bootEntryId, userRoleId);
        return new RemoteResult(result);
    }


    @PostMapping("/saveBootPageProgress")
    public RemoteResult saveBootPageProgress(@RequestBody BootPageProgress bootPageProgress) {
        Long result = bootEntrySvc.saveBootPageProgress(bootPageProgress);
        return new RemoteResult(result);
    }

    @GetMapping("/getUserRoleProgress")
    public RemoteResult getUserRoleProgress(@RequestParam Long userRoleId) {
        List<BootPageProgress> result = bootEntrySvc.getUserRoleProgress(userRoleId);
        return new RemoteResult(result);
    }


    @GetMapping("/showButton")
    public RemoteResult showButton() {
        Boolean result = bootEntrySvc.showButton();
        return new  RemoteResult(result);
    }

    @GetMapping("/getModuleById")
    public RemoteResult getModuleById(@RequestParam Long moduleId) {
        List<Long> parentModuleIds = bootEntrySvc.getModuleById(moduleId);
        return new RemoteResult(parentModuleIds);
    }

    @PostMapping("addBootPageUser")
    public RemoteResult addBootPageUser(@RequestBody BootEntryUserConfig bootEntryUserConfig) {
        Long result = bootEntrySvc.addBootPageUser(bootEntryUserConfig);
        return new RemoteResult(result);
    }

    @GetMapping("/getBootPageUsers")
    public RemoteResult getBootPageUsers() {
        List<BootEntryUserConfig> result = bootEntrySvc.getBootPageUsers();
        return new RemoteResult(result);
    }

    @GetMapping("/brushStockRoles")
    public void brushStockRoles() {
        bootEntrySvc.brushStockRoles();
    }

    @GetMapping("/exportUserInitData")
    public ResponseEntity<byte[]> exportConfigInitData() {
        return bootEntrySvc.exportConfigInitData();
    }

    @GetMapping("/exportBootEntryInitData")
    public ResponseEntity<byte[]> exportBootEntryInitData() {
        return bootEntrySvc.exportBootEntryInitData();
    }

    @GetMapping("/updateRoleField")
    public RemoteResult updateRoleField() {
        Long result = bootEntrySvc.updateRoleField();
        return new RemoteResult(result);
    }

    @PostMapping("/saveBootTrialPageProgress")
    public RemoteResult saveBootTrialPageProgress(@RequestBody String requestJson){
        JSONObject paramJson = JSON.parseObject(requestJson);
        String bootTrialPageCode = (String) paramJson.get("bootTrialPageCode");
        Long result = bootEntrySvc.saveBootTrialPageProgress(bootTrialPageCode);
        return new RemoteResult(result);
    }

    @GetMapping("/getUserBootTrialPageProcess")
    public RemoteResult getUserBootTrialPageProcess(@RequestParam(required = false) String bootTrialPageCode){
        Boolean result = bootEntrySvc.getUserBootTrialPageProcess(bootTrialPageCode);
        return new RemoteResult(result);
    }
}
