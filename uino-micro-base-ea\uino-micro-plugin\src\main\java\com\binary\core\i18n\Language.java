package com.binary.core.i18n;

import com.binary.core.exception.BinaryException;

public enum Language {
	
	
	/** 简体中文 **/
	ZHC(11),
	
	
	/** 繁体中文 **/
	ZHT(12),
	
	
	/** 英国 **/
	EN(13),
	
	
	/** 法国 **/
	FR(14),
	
	
	/** 德国 **/
	DE(15),
	
	
	/** 意大利 **/
	IT(16),
	
	
	/** 日本 **/
	JA(17),
	
	
	/** 韩国 **/
	KO(18),
	
	
	/** 南非 **/
	AF(19),
	
	
	/** 阿拉伯 **/
	AR(20),
	
	
	/** 阿塞拜疆 **/
	AZ(21),
	
	
	/** 白俄罗斯 **/
	BE(22),
	
	
	/** 保加利亚 **/
	BG(23),
	
	
	/** 加泰罗尼亚 **/
	CA(24),
	
	
	/** 捷克 **/
	CS(25),
	
	
	/** 丹麦 **/
	DA(26),
	
	
	/** 希腊 **/
	EL(27),
	
	
	/** 西班牙 **/
	ES(28),
	
	
	/** 波斯语 **/
	FA(29),
	
	
	/** 芬兰 **/
	FI(30),
	
	
	/** 希伯来 **/
	HE(31),
	
	
	/** 克罗地亚 **/
	HR(32),
	
	
	/** 匈牙利 **/
	HU(33),
	
	
	/** 亚美尼亚 **/
	HY(34),
	
	
	/** 印尼 **/
	ID(35),
	
	
	/** 冰岛 **/
	IS(36),
	
	
	/** 格鲁吉亚 **/
	KA(37),
	
	
	/** 哈萨克斯坦 **/
	KK(38),
	
	
	/** 立陶宛 **/
	LT(39),
	
	
	/** 拉脱维亚 **/
	LV(40),
	
	
	/** 毛利 **/
	MI(41),
	
	
	/** 蒙古 **/
	MN(42),
	
	
	/** 马来西亚 **/
	MS(43),
	
	
	/** 挪威 **/
	NB(44),
	
	
	/** 荷兰 **/
	NL(45),
	
	
	/** 波兰 **/
	PL(46),
	
	
	/** 葡萄牙 **/
	PT(47),
	
	
	/** 罗马尼亚 **/
	RO(48),
	
	
	/** 俄罗斯 **/
	RU(49),
	
	
	/** 梵文 **/
	SA(50),
	
	
	/** 斯洛伐克 **/
	SK(51),
	
	
	/** 斯洛维尼亚 **/
	SL(52),
	
	
	/** 阿尔巴尼亚 **/
	SQ(53),
	
	
	/** 瑞典 **/
	SV(54),
	
	
	/** 斯瓦希里 **/
	SW(55),
	
	
	/** 叙利亚 **/
	SYR(56),
	
	
	/** 泰国 **/
	TH(57),
	
	
	/** 土耳其 **/
	TR(58),
	
	
	/** 乌克兰 **/
	UK(59),
	
	
	/** 乌兹别克 **/
	UZ(60),
	
	
	/** 越南 **/
	VI(61);
	
	
	
	
	private int value;
	
	
	
	private Language(int value) {
		this.value = value;
	}
	
	
	
	public int getValue() {
		return this.value;
	}
	
	
	
	
	public String getZhName() {
		switch(this) {
			case ZHC: return "简体中文";
			case ZHT: return "繁体中文";
			case EN: return "英国";
			case FR: return "法国";
			case DE: return "德国";
			case IT: return "意大利";
			case JA: return "日本";
			case KO: return "韩国";
			case AF: return "南非";
			case AR: return "阿拉伯";
			case AZ: return "阿塞拜疆";
			case BE: return "白俄罗斯";
			case BG: return "保加利亚";
			case CA: return "加泰罗尼亚";
			case CS: return "捷克";
			case DA: return "丹麦";
			case EL: return "希腊";
			case ES: return "西班牙";
			case FA: return "波斯";
			case FI: return "芬兰";
			case HE: return "希伯来";
			case HR: return "克罗地亚";
			case HU: return "匈牙利";
			case HY: return "亚美尼亚";
			case ID: return "印尼";
			case IS: return "冰岛";
			case KA: return "格鲁吉亚";
			case KK: return "哈萨克斯坦";
			case LT: return "立陶宛";
			case LV: return "拉脱维亚";
			case MI: return "毛利";
			case MN: return "蒙古";
			case MS: return "马来西亚";
			case NB: return "挪威";
			case NL: return "荷兰";
			case PL: return "波兰";
			case PT: return "葡萄牙";
			case RO: return "罗马尼亚";
			case RU: return "俄罗斯";
			case SA: return "梵文";
			case SK: return "斯洛伐克";
			case SL: return "斯洛维尼亚";
			case SQ: return "阿尔巴尼亚";
			case SV: return "瑞典";
			case SW: return "斯瓦希里";
			case SYR: return "叙利亚";
			case TH: return "泰国";
			case TR: return "土耳其";
			case UK: return "乌克兰";
			case UZ: return "乌兹别克";
			case VI: return "越南";
			default : throw new BinaryException(" is wrong '"+this+"'! ");
		}
	}
	
	
	
	
	
	public static Language valueOf(int v) {
		switch(v) {
			case 11: return ZHC;
			case 12: return ZHT;
			case 13: return EN;
			case 14: return FR;
			case 15: return DE;
			case 16: return IT;
			case 17: return JA;
			case 18: return KO;
			case 19: return AF;
			case 20: return AR;
			case 21: return AZ;
			case 22: return BE;
			case 23: return BG;
			case 24: return CA;
			case 25: return CS;
			case 26: return DA;
			case 27: return EL;
			case 28: return ES;
			case 29: return FA;
			case 30: return FI;
			case 31: return HE;
			case 32: return HR;
			case 33: return HU;
			case 34: return HY;
			case 35: return ID;
			case 36: return IS;
			case 37: return KA;
			case 38: return KK;
			case 39: return LT;
			case 40: return LV;
			case 41: return MI;
			case 42: return MN;
			case 43: return MS;
			case 44: return NB;
			case 45: return NL;
			case 46: return PL;
			case 47: return PT;
			case 48: return RO;
			case 49: return RU;
			case 50: return SA;
			case 51: return SK;
			case 52: return SL;
			case 53: return SQ;
			case 54: return SV;
			case 55: return SW;
			case 56: return SYR;
			case 57: return TH;
			case 58: return TR;
			case 59: return UK;
			case 60: return UZ;
			case 61: return VI;
			default : throw new BinaryException(" is wrong value '"+v+"'! ");
		}
	}
	
	

}
