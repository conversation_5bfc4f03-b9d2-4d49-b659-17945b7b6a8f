package com.uinnova.product.eam.web.bm.peer;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.util.ListUtils;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.binary.core.exception.BinaryException;
import com.binary.core.exception.MessageException;
import com.binary.core.util.BinaryUtils;
import com.uinnova.product.eam.base.diagram.model.*;
import com.uinnova.product.eam.base.exception.ServerException;
import com.uinnova.product.eam.base.util.CustomCellWriteHandler;
import com.uinnova.product.eam.base.util.EamUtil;
import com.uinnova.product.eam.db.VcDiagramVersionDao;
import com.uinnova.product.eam.db.diagram.es.ESDiagramDao;
import com.uinnova.product.eam.db.diagram.es.ESDiagramLinkDao;
import com.uinnova.product.eam.db.diagram.es.ESDiagramNodeDao;
import com.uinnova.product.eam.model.asset.AssetChangeAttrVO;
import com.uinnova.product.eam.model.asset.CiContrastVO;
import com.uinnova.product.eam.model.asset.DiagramContrastParam;
import com.uinnova.product.eam.model.asset.DiagramContrastVO;
import com.uinnova.product.eam.model.bm.DiagramEnum;
import com.uinnova.product.eam.model.dto.DiagramContrastDataDto;
import com.uinnova.product.eam.service.ICISwitchSvc;
import com.uinnova.product.eam.service.diagram.ESDiagramSvc;
import com.uinnova.product.eam.service.es.IamsESCIDesignSvc;
import com.uinnova.product.eam.service.es.IamsESCIHistoryPrivateSvc;
import com.uinnova.product.eam.service.es.IamsESCIRltInfoHistoryDesignSvc;
import com.uinnova.product.eam.service.es.IamsESCIRltInfoHistoryPrivateSvc;
import com.uinnova.product.eam.service.impl.IamsCIRltSwitchSvc;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClass;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.comm.model.ci.CcCiClass;
import com.uinnova.product.vmdb.comm.util.CommUtil;
import com.uino.api.client.permission.IUserApiSvc;
import com.uino.bean.cmdb.base.*;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.permission.query.CSysUser;
import com.uino.dao.cmdb.ESCIClassSvc;
import com.uino.dao.cmdb.ESRltClassSvc;
import com.uino.service.util.FileUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.net.URLEncoder;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DiagramContrastPeer {

    @Resource
    ICISwitchSvc ciSwitchSvc;

    @Resource
    IamsCIRltSwitchSvc ciRltSwitchSvc;

    @Resource
    ESCIClassSvc esciClassSvc;

    @Resource
    ESRltClassSvc esRltClassSvc;

    @Resource
    ESDiagramDao esDiagramDao;

    @Resource
    ESDiagramNodeDao esDiagramNodeDao;

    @Resource
    ESDiagramLinkDao esDiagramLinkDao;

    @Resource
    IamsESCIHistoryPrivateSvc ciPrivateHistorySvc;

    @Resource
    IamsESCIDesignSvc ciDesignHistorySvc;

    @Resource
    IamsESCIRltInfoHistoryDesignSvc rltDesignHistorySvc;

    @Resource
    IamsESCIRltInfoHistoryPrivateSvc rltPrivateHistorySvc;

    @Resource
    ESDiagramSvc esDiagramSvc;

    @Resource
    VcDiagramVersionDao vcDiagramVersionDao;

    @Resource
    IUserApiSvc iUserApiSvc;


    public DiagramContrastVO diagramContras(DiagramContrastParam param) {
        /*
          1.查询视图信息
          2.获取视图中ci 及关系
          3.对比俩张图
         */
        DiagramContrastVO result = new DiagramContrastVO();
        // 源端视图获取信息
        ESDiagram sourceDiagramInfo = queryDiagramInfo(param.getSourceDiagramId());
        DiagramContrastDataDto sourceInfo = queryCiAndRltInfoByDiagram(sourceDiagramInfo, param.getSourceSheetId());
        // 目标端视图获取信息
        ESDiagram targetDiagramInfo = queryDiagramInfo(param.getTargetDiagramId());
        DiagramContrastDataDto targetInfo = queryCiAndRltInfoByDiagram(targetDiagramInfo, param.getTargetSheetId());

        List<CiContrastVO> ciContrastVOList = new ArrayList<>();
        result.setCiContrastVOList(ciContrastVOList);
        // 对比数据
        contrastCI(sourceInfo, targetInfo, ciContrastVOList);
        // 对比关系 -新增、删除
        contrastRlt(sourceInfo, targetInfo, ciContrastVOList);
        result.setFirstDiagramInfo(sourceInfo.getEsDiagramDTO());
        result.setSecondDiagramInfo(targetInfo.getEsDiagramDTO());
        return result;
    }

    /**
     *  根据视图信息 获取对应的图内 CI / RLT 信息
     *  数据获取逻辑：
     *      1.资产仓库视图 - 获取 CI / RLT ***设计库或设计库历史库***数据
     *      2.资产仓库历史版本视图 - 获取 CI / RLT 设计库历史库数据
     *      3.设计空间主视图 - 获取 CI / RLT 私有库数据
     *      4.设计空间快照/历史版本视图 - 获取 CI / RLT 私有库历史库数据
     * @param diagramInfo 视图信息
     * @param sheetId sheet页信息 为空是默认查询视图内所有
     * @return
     */
    private DiagramContrastDataDto queryCiAndRltInfoByDiagram(ESDiagram diagramInfo, String sheetId) {
        DiagramContrastDataDto contrastData = new DiagramContrastDataDto();
        // 获取当前视图类型
        DiagramEnum diagramEnum = getDiagramType(diagramInfo);
        // 获取视图 node/link 信息
        String diagramId = diagramInfo.getDEnergy();

        // 获取当前视图完整信息
        Long id = diagramInfo.getId();
        ESDiagramDTO diagramDTO = queryFullDiagramInfo(id, diagramEnum);

        /*// 查询视图上的 CI / RLT 数据 (私有库)
        Set<String> privateCICodes = DataModelDiagramUtil.getDiagramCiList(Collections.singletonList(diagramDTO.getDiagram()));
        Set<String> privateRltCodes = DataModelDiagramUtil.getDiagramRltList(Collections.singletonList(diagramDTO.getDiagram()));
        //添加对ER图的特殊关系处理
        privateRltCodes.addAll(flowModelMergePreProcessor.queryRltCode(Collections.singletonList(diagramDTO)));*/

        List<ESDiagramNode> nodeList = getDiagramNodeList(diagramId, sheetId);
        List<String> items = new ArrayList<>();
        Map<String, ESCIInfo> historyItemsMap = new HashMap<>();
        for (ESDiagramNode node : nodeList) {
            JSONObject jsonObject = JSON.parseObject(node.getNodeJson());
            if (BinaryUtils.isEmpty(jsonObject.get("items"))) {
                continue;
            }
            List<ESCIInfo> esciInfos = JSONObject.parseArray(jsonObject.get("items").toString(), ESCIInfo.class);
            for (ESCIInfo item : esciInfos) {
                String ciCode = item.getCiCode();
                if (BinaryUtils.isEmpty(ciCode)) {
                    continue;
                }
                items.add(ciCode);
                historyItemsMap.put(ciCode, item);
            }
        }

        List<ESDiagramLink> linkList = getDiagramLinkList(diagramId, sheetId);
        // 根据node/link获取CI/RLT信息
        String ownerCode = diagramInfo.getOwnerCode();
        List<ESCIInfo> ciList = getCIByNodeInfo(nodeList, items, diagramEnum, ownerCode);
        List<ESCIRltInfo> rltList = getRltByLinkInfo(linkList, diagramEnum, ownerCode);
        // 重组历史实体属性信息
        if (!BinaryUtils.isEmpty(historyItemsMap) && !CollectionUtils.isEmpty(ciList)) {
            for (ESCIInfo ci : ciList) {
                String ciCode = ci.getCiCode();
                if (BinaryUtils.isEmpty(historyItemsMap.get(ciCode))) {
                    continue;
                }
                Map<String, Object> attrs = historyItemsMap.get(ciCode).getAttrs();
                ci.setAttrs(attrs);
            }
        }

        contrastData.setEsDiagramDTO(diagramDTO);
        contrastData.setCiList(ciList);
        contrastData.setCiMap(ciList.stream().collect(Collectors.toMap(ESCIInfo::getCiPrimaryKey, ci->ci, (k1, k2)->k2)));
        contrastData.setCiAndLocMap(nodeList.stream().collect(Collectors.toMap(ESDiagramNode::getCiCode, ESDiagramNode::getKey, (k1, k2) -> k2)));
        contrastData.setRltAndLocMap(linkList.stream().collect(Collectors.toMap(ESDiagramLink::getUniqueCode, ESDiagramLink::getKey, (k1, k2) -> k2)));
        contrastData.setRltList(rltList);
        return contrastData;
    }

    /**
     *  获取视图完整信息
     * @param diagramId
     * @param diagramEnum
     * @return
     */
    private ESDiagramDTO queryFullDiagramInfo(Long diagramId, DiagramEnum diagramEnum) {
        String type = diagramEnum.equals(DiagramEnum.SNAPSHOT_DIAGRAM) ? "SNAPSHOT" :
                diagramEnum.equals(DiagramEnum.HISTORY_DIAGRAM) ? "FX_D_HISTORY" : "";
        List<ESDiagramDTO> diagramDTOS = esDiagramSvc.queryDiagramInfoByIds(new Long[]{diagramId}, type, Boolean.FALSE, Boolean.FALSE);
        if (BinaryUtils.isEmpty(diagramDTOS)) {
            throw new BinaryException("视图数据获取异常");
        }
        ESDiagramDTO esDiagramDTO = diagramDTOS.get(0);

        switch (diagramEnum) {
            case HISTORY_DIAGRAM:
                return esDiagramDTO;
            case PRIVATE_DIAGRAM:
                return queryPrivateDiagramInfo(esDiagramDTO);
            case RELEASE_DIARGAM:
                return esDiagramDTO;
            case SNAPSHOT_DIAGRAM:
                return querySnapshotDiagramInfo(esDiagramDTO);
            default: {
                throw new ServerException("视图类型未定义");
            }
        }
    }

    private ESDiagramDTO queryPrivateDiagramInfo(ESDiagramDTO esDiagramDTO) {
        Integer flowStatus = esDiagramDTO.getDiagram().getFlowStatus();
        if (!BinaryUtils.isEmpty(flowStatus) && flowStatus == 2) {
            String diagramName = esDiagramDTO.getDiagram().getName();
            esDiagramDTO.getDiagram().setName("审批版本-"+diagramName);
        }
        return esDiagramDTO;
    }

    /**
     *  查询快照视图的创建人
     * @param esDiagramDTO
     * @return
     */
    private ESDiagramDTO querySnapshotDiagramInfo(ESDiagramDTO esDiagramDTO) {
        // 根据视图历史版本关联表视图创建人信息
        VcDiagramVersion vcDiagramVersion = vcDiagramVersionDao.selectById(esDiagramDTO.getDiagram().getId());
        if (BinaryUtils.isEmpty(vcDiagramVersion)) {
            return esDiagramDTO;
        }
        String creator = vcDiagramVersion.getCreator();
        if (!BinaryUtils.isEmpty(creator)) {
            int indexOf = creator.lastIndexOf("[");
            if(indexOf != -1) {
                creator = creator.substring(indexOf+1, creator.length()-1);
            }
            CSysUser cSysUser = new CSysUser();
            cSysUser.setLoginCodeEqual(creator);
            cSysUser.setDomainId(1L);
            cSysUser.setSuperUserFlags(new Integer[]{0,1});
            List<SysUser> sysUsers = iUserApiSvc.getSysUserByCdt(cSysUser);
            if (!BinaryUtils.isEmpty(sysUsers)) {
                SysUser realUser = sysUsers.get(0);
                esDiagramDTO.setCreator(realUser);
                esDiagramDTO.getDiagram().setOwnerCode(realUser.getLoginCode());
            }
        }
        return esDiagramDTO;
    }

    /*private List<ESCIInfo> getPrivateDiagramItemsInfo(List<String> ciCodes, String ownerCode) {
        if (CollectionUtils.isEmpty(ciCodes)) {
            return new ArrayList<>();
        }
        return ciSwitchSvc.getCiByCodes(ciCodes, ownerCode, LibType.PRIVATE);
    }

    private List<ESCIInfo> getReleaseDiagramItemsInfo(List<String> ciCodes) {
        if (CollectionUtils.isEmpty(ciCodes)) {
            return new ArrayList<>();
        }
        return ciSwitchSvc.getCiByCodes(ciCodes, null, LibType.DESIGN);
    }

    *//**
     *  获取数据建模实体属性CI
     * @param items
     * @param diagramEnum
     * @param ownerCode
     * @return
     *//*
    private List<ESCIInfo> getItemCI(List<String> items, DiagramEnum diagramEnum, String ownerCode) {
        if (BinaryUtils.isEmpty(items)) {
            return new ArrayList<>();
        }
        switch (diagramEnum) {
            case HISTORY_DIAGRAM:
                return getPrivateDiagramItemsInfo(items, ownerCode);
            case PRIVATE_DIAGRAM:
                return getPrivateDiagramItemsInfo(items, ownerCode);
            case RELEASE_DIARGAM:
                return getReleaseDiagramItemsInfo(items);
            case SNAPSHOT_DIAGRAM:
                return getReleaseDiagramItemsInfo(items);
            default: {
                throw new ServerException("视图类型未定义");
            }
        }
    }*/

    /**
     *  根据视图类型和图内node节点信息获取CI的信息
     * @param nodeList
     * @param diagramEnum
     * @param ownerCode
     * @return
     */
    private List<ESCIInfo> getCIByNodeInfo(List<ESDiagramNode> nodeList, List<String> items, DiagramEnum diagramEnum, String ownerCode) {
        List<String> allCiCodeList = new ArrayList<>();
        Map<String, ESDiagramNode> hasVersionNodeMap = new HashMap<>();
        List<String> noVersionNodeList = new ArrayList<>();
        for (ESDiagramNode node : nodeList) {
            String ciCode = node.getCiCode();
            Long version = node.getVersion();
            if (BinaryUtils.isEmpty(version)) {
                noVersionNodeList.add(ciCode);
            } else {
                hasVersionNodeMap.put(ciCode, node);
            }
            allCiCodeList.add(ciCode);
        }
        noVersionNodeList.addAll(items);
        switch (diagramEnum) {
            case HISTORY_DIAGRAM:
                return getHistoryDiagramCiInfo(hasVersionNodeMap, noVersionNodeList, Boolean.TRUE);
            case PRIVATE_DIAGRAM:
                return getPrivateDiagramCiInfo(allCiCodeList, items, ownerCode);
            case RELEASE_DIARGAM:
                return getReleaseDiagramCiInfo(allCiCodeList);
            case SNAPSHOT_DIAGRAM:
                return getSnapshotDiagramCiInfo(hasVersionNodeMap, noVersionNodeList, ownerCode, Boolean.TRUE);
            default: {
                throw new ServerException("视图类型未定义");
            }
        }
    }

    /**
     *  根据视图类型和图内link节点信息获取Rlt的信息
     * @param linkList
     * @param diagramEnum
     * @param ownerCode
     * @return
     */
    private List<ESCIRltInfo> getRltByLinkInfo(List<ESDiagramLink> linkList, DiagramEnum diagramEnum, String ownerCode) {
        Set<String> allRltCodeList = new HashSet<>();
        Map<String, ESDiagramLink> hasVersionLinkMap = new HashMap<>();
        Set<String> noVersionLinkList = new HashSet<>();
        for (ESDiagramLink link : linkList) {
            String uniqueCode = link.getUniqueCode();
            Long version = link.getVersion();
            if (BinaryUtils.isEmpty(version)) {
                noVersionLinkList.add(uniqueCode);
            } else {
                hasVersionLinkMap.put(uniqueCode, link);
            }
            allRltCodeList.add(uniqueCode);
        }

        switch (diagramEnum) {
            case HISTORY_DIAGRAM:
                return getHistoryDiagramRltInfo(hasVersionLinkMap, noVersionLinkList, Boolean.TRUE);
            case PRIVATE_DIAGRAM:
                return getPrivateDiagramRltInfo(allRltCodeList, ownerCode);
            case RELEASE_DIARGAM:
                return getReleaseDiagramRltInfo(allRltCodeList);
            case SNAPSHOT_DIAGRAM:
                return getSnapshotDiagramRltInfo(hasVersionLinkMap, noVersionLinkList, ownerCode, Boolean.TRUE);
            default: {
                throw new ServerException("视图类型未定义");
            }
        }
    }

    /**
     *  根据ciCode/uniqueCode获取私有库ci/rlt信息
     * @param ciCodeList
     * @return
     */
    private List<ESCIInfo> getPrivateDiagramCiInfo(List<String> ciCodeList, List<String> items, String ownerCode) {
        if (CollectionUtils.isEmpty(ciCodeList) && CollectionUtils.isEmpty(items)) {
            return new ArrayList<>();
        }
        ciCodeList.addAll(items);
        return ciSwitchSvc.getCiByCodes(ciCodeList, ownerCode, LibType.PRIVATE);
    }

    private List<ESCIRltInfo> getPrivateDiagramRltInfo(Set<String> allRltCodeList, String ownerCode) {
        if (CollectionUtils.isEmpty(allRltCodeList)) {
            return new ArrayList<>();
        }
        return ciRltSwitchSvc.getRltByUniqueCodes(allRltCodeList, ownerCode, LibType.PRIVATE);
    }

    /**
     *  根据ciCode/uniqueCode获取设计库ci/rlt信息
     * @param ciCodeList
     * @return
     */
    private List<ESCIInfo> getReleaseDiagramCiInfo(List<String> ciCodeList) {
        if (CollectionUtils.isEmpty(ciCodeList)) {
            return new ArrayList<>();
        }
        return ciSwitchSvc.getCiByCodes(ciCodeList, null, LibType.DESIGN);
    }

    private List<ESCIRltInfo> getReleaseDiagramRltInfo(Set<String> allRltCodeList) {
        if (CollectionUtils.isEmpty(allRltCodeList)) {
            return new ArrayList<>();
        }
        return ciRltSwitchSvc.getRltByUniqueCodes(allRltCodeList, null, LibType.DESIGN);
    }

    /**
     *  获取设计库历史库ci/rlt信息
     * @param hasVersionNodeMap
     * @param noVersionNodeList
     * @param repair 是否兼容node节点没有version的数据 兼容的情况根据ciCode查询设计库实时的数据
     * @return
     */
    private List<ESCIInfo> getHistoryDiagramCiInfo(Map<String, ESDiagramNode> hasVersionNodeMap, List<String> noVersionNodeList, Boolean repair) {

        List<ESCIInfo> historyDiagramCiList = new ArrayList<>();
        List<String> historyDiagramCiCodeList = new ArrayList<>();

        if (MapUtils.isNotEmpty(hasVersionNodeMap)) {
            BoolQueryBuilder ciHistoryQuery = QueryBuilders.boolQuery();
            for (Map.Entry<String, ESDiagramNode> entry : hasVersionNodeMap.entrySet()) {
                String nodeCode = entry.getKey();
                ESDiagramNode node = entry.getValue();
                BoolQueryBuilder oneQuery = QueryBuilders.boolQuery();
                oneQuery.must(QueryBuilders.termQuery("version", node.getVersion()));
                oneQuery.must(QueryBuilders.termQuery("ciCode.keyword", nodeCode));
                ciHistoryQuery.should(oneQuery);
            }
            historyDiagramCiList = ciDesignHistorySvc.getListByQuery(ciHistoryQuery);
            historyDiagramCiCodeList = historyDiagramCiList.stream().map(ESCIInfo::getCiCode).collect(Collectors.toList());
        }


        for (Map.Entry<String, ESDiagramNode> entry : hasVersionNodeMap.entrySet()) {
            String key = entry.getKey();
            if (!historyDiagramCiCodeList.contains(key)) {
                noVersionNodeList.add(key);
            }
        }

        if (repair && !CollectionUtils.isEmpty(noVersionNodeList)) {
            log.info("query history diagram ci no version list ciCodes:{}", JSONObject.toJSONString(noVersionNodeList));
            List<ESCIInfo> repairCiList = ciSwitchSvc.getCiByCodes(noVersionNodeList, null, LibType.DESIGN);
            if (!CollectionUtils.isEmpty(repairCiList)) {
                historyDiagramCiList.addAll(repairCiList);
            }
        }
        return historyDiagramCiList;
    }

    private List<ESCIRltInfo> getHistoryDiagramRltInfo(Map<String, ESDiagramLink> hasVersionLinkMap, Set<String> noVersionLinkList, Boolean repair) {

        List<ESCIRltInfo> historyDiagramRltList = new ArrayList<>();
        List<String> historyDiagramRltCodeList = new ArrayList<>();

        if (MapUtils.isNotEmpty(hasVersionLinkMap)) {
            BoolQueryBuilder queryOrAttrs = QueryBuilders.boolQuery();
            for (Map.Entry<String, ESDiagramLink> entry : hasVersionLinkMap.entrySet()) {
                String uniqueCode = entry.getKey();
                ESDiagramLink link = entry.getValue();
                BoolQueryBuilder oneQuery = QueryBuilders.boolQuery();
                oneQuery.must(QueryBuilders.termQuery("version", link.getVersion()));
                oneQuery.must(QueryBuilders.termQuery("uniqueCode.keyword", uniqueCode));
                queryOrAttrs.should(oneQuery);
            }
            List<ESCIRltInfoHistory> rltDesignHistoryList = rltDesignHistorySvc.getListByQuery(queryOrAttrs);
            rltDesignHistoryList = rltDesignHistoryList.stream().
                    collect(Collectors.toMap(ESCIRltInfoHistory::getUniqueCode,
                            Function.identity(),
                            (before, after) -> before,
                            LinkedHashMap::new)).
                    values().
                    stream().
                    collect(Collectors.toList());
            historyDiagramRltList = EamUtil.copy(rltDesignHistoryList, ESCIRltInfo.class);
            historyDiagramRltCodeList = historyDiagramRltList.stream().map(ESCIRltInfo::getUniqueCode).collect(Collectors.toList());
        }

        for (Map.Entry<String, ESDiagramLink> entry : hasVersionLinkMap.entrySet()) {
            String key = entry.getKey();
            if (!historyDiagramRltCodeList.contains(key)) {
                noVersionLinkList.add(key);
            }
        }

        if (repair && !CollectionUtils.isEmpty(noVersionLinkList)) {
            log.info("query history diagram rlt no version list ciCodes:{}", JSONObject.toJSONString(noVersionLinkList));
            List<ESCIRltInfo> repairRltList = ciRltSwitchSvc.getRltByUniqueCodes(noVersionLinkList, null, LibType.DESIGN);
            if (!CollectionUtils.isEmpty(repairRltList)) {
                historyDiagramRltList.addAll(repairRltList);
            }
        }
        return historyDiagramRltList;
    }



    /**
     *  获取私有库历史库ci/rlt信息
     * @param hasVersionNodeMap
     * @param noVersionNodeList
     * @param ownerCode
     * @param repair 是否兼容node节点没有version的数据 兼容的情况根据ciCode查询私有库实时的数据
     * @return
     */
    private List<ESCIInfo> getSnapshotDiagramCiInfo(Map<String, ESDiagramNode> hasVersionNodeMap, List<String> noVersionNodeList, String ownerCode, Boolean repair) {

        List<ESCIInfo> snapshotDiagramCiList = new ArrayList<>();

        if (!MapUtils.isEmpty(hasVersionNodeMap)) {
            BoolQueryBuilder ciHistoryQuery = QueryBuilders.boolQuery();
            for (Map.Entry<String, ESDiagramNode> entry : hasVersionNodeMap.entrySet()) {
                String ciCode = entry.getKey();
                ESDiagramNode node = entry.getValue();
                BoolQueryBuilder oneQuery = QueryBuilders.boolQuery();
                oneQuery.must(QueryBuilders.termQuery("version", node.getVersion()));
                oneQuery.must(QueryBuilders.termQuery("ciCode.keyword", ciCode));
                oneQuery.must(QueryBuilders.termQuery("ownerCode.keyword", ownerCode));
                ciHistoryQuery.should(oneQuery);
            }
            List<ESCIHistoryInfo> ciPrivateHistoryList = ciPrivateHistorySvc.getListByQuery(ciHistoryQuery);
            snapshotDiagramCiList = EamUtil.copy(ciPrivateHistoryList, ESCIInfo.class);
        }

        if (repair && !CollectionUtils.isEmpty(noVersionNodeList)) {
            log.info("query snapshot diagram ci no version list ciCodes:{}", JSONObject.toJSONString(noVersionNodeList));
            List<ESCIInfo> repairCiList = ciSwitchSvc.getCiByCodes(noVersionNodeList, ownerCode, LibType.PRIVATE);
            if (!CollectionUtils.isEmpty(repairCiList)) {
                snapshotDiagramCiList.addAll(repairCiList);
            }
        }
        return snapshotDiagramCiList;
    }

    private List<ESCIRltInfo> getSnapshotDiagramRltInfo(Map<String, ESDiagramLink> hasVersionLinkMap, Set<String> noVersionLinkList, String ownerCode, Boolean repair) {

        List<ESCIRltInfo> snapshotDiagramRltList = new ArrayList<>();

        if (!MapUtils.isEmpty(hasVersionLinkMap)) {

            BoolQueryBuilder rltHistoryQuery = QueryBuilders.boolQuery();
            for (Map.Entry<String, ESDiagramLink> entry : hasVersionLinkMap.entrySet()) {
                String uniqueCode = entry.getKey();
                ESDiagramLink link = entry.getValue();
                BoolQueryBuilder oneQuery = QueryBuilders.boolQuery();
                oneQuery.must(QueryBuilders.termQuery("version", link.getVersion()));
                oneQuery.must(QueryBuilders.termQuery("uniqueCode.keyword", uniqueCode));
                oneQuery.must(QueryBuilders.termQuery("ownerCode.keyword", ownerCode));
                rltHistoryQuery.should(oneQuery);
            }
            List<ESCIRltInfoHistory> rltPrivateHistoryList = rltPrivateHistorySvc.getListByQuery(rltHistoryQuery);
            snapshotDiagramRltList = EamUtil.copy(rltPrivateHistoryList, ESCIRltInfo.class);
        }

        if (repair && !CollectionUtils.isEmpty(noVersionLinkList)) {
            log.info("query snapshot diagram rlt no version list ciCodes:{}", JSONObject.toJSONString(noVersionLinkList));
            List<ESCIRltInfo> repairRltList = ciRltSwitchSvc.getRltByUniqueCodes(noVersionLinkList, ownerCode, LibType.PRIVATE);
            if (!CollectionUtils.isEmpty(repairRltList)) {
                snapshotDiagramRltList.addAll(repairRltList);
            }
        }
        return snapshotDiagramRltList;
    }

    /**
     *  根据视图ID和sheet页ID 获取内部的link节点
     * @param diagramId
     * @param sheetId 非必填
     * @return
     */
    private List<ESDiagramLink> getDiagramLinkList(String diagramId, String sheetId) {
        if (BinaryUtils.isEmpty(diagramId)) {
            throw new ServerException("视图标识参数缺失,无法获取到图link内节点信息");
        }
        BoolQueryBuilder linkBool = QueryBuilders.boolQuery();
        linkBool.must(QueryBuilders.termQuery("dEnergy.keyword", diagramId));
        if (!BinaryUtils.isEmpty(sheetId)) {
            linkBool.must(QueryBuilders.termQuery("sheetId.keyword", sheetId));
        }
        linkBool.must(QueryBuilders.existsQuery("uniqueCode.keyword"));
        return esDiagramLinkDao.getListByQuery(linkBool);
    }

    /**
     *  根据视图ID和sheet页ID 获取内部的node节点
     * @param diagramId
     * @param sheetId 非必填
     * @return
     */
    private List<ESDiagramNode> getDiagramNodeList(String diagramId, String sheetId) {
        if (BinaryUtils.isEmpty(diagramId)) {
            throw new ServerException("视图标识参数缺失,无法获取到图node内节点信息");
            // return new ArrayList<>();
        }
        BoolQueryBuilder nodeBool = QueryBuilders.boolQuery();
        nodeBool.must(QueryBuilders.termQuery("dEnergy.keyword", diagramId));
        if (!BinaryUtils.isEmpty(sheetId)) {
            nodeBool.must(QueryBuilders.termQuery("sheetId.keyword", sheetId));
        }
        nodeBool.must(QueryBuilders.existsQuery("ciCode.keyword"));
        return esDiagramNodeDao.getListByQuery(nodeBool);
    }

    /**
     *  根据视图信息解析视图类型
     * @param diagramInfo
     * @return
     */
    private DiagramEnum getDiagramType(ESDiagram diagramInfo) {
        if (BinaryUtils.isEmpty(diagramInfo.getIsOpen()) || BinaryUtils.isEmpty(diagramInfo.getHistoryVersionFlag())) {
            throw new ServerException("视图【"+diagramInfo.getDEnergy()+"】内缺少判断类型的标识信息");
        }
        Boolean isRelease = diagramInfo.getIsOpen() == 0 ? Boolean.FALSE : Boolean.TRUE;
        Boolean isHistory = diagramInfo.getHistoryVersionFlag() == 1 ? Boolean.FALSE : Boolean.TRUE;

        if (isRelease) {
            if (isHistory) {
                // 仓库历史版本
                return DiagramEnum.HISTORY_DIAGRAM;
            } else {
                // 仓库视图
                // return DiagramEnum.RELEASE_DIARGAM;
                return DiagramEnum.HISTORY_DIAGRAM;
            }
        } else {
            if (isHistory) {
                // 设计空间快照
                return DiagramEnum.SNAPSHOT_DIAGRAM;
            } else {
                // 设计空间视图
                return DiagramEnum.PRIVATE_DIAGRAM;
            }
        }
    }

    /**
     *  获取视图信息
     * @param diagramId
     * @return
     */
    private ESDiagram queryDiagramInfo(String diagramId) {
        List<ESDiagram> listByQuery = esDiagramDao.getListByQuery(QueryBuilders.termQuery("dEnergy.keyword", diagramId));
        if (BinaryUtils.isEmpty(listByQuery)) {
            throw new ServerException("视图标识查询异常：diagramId【" + diagramId + "】");
        }
        return listByQuery.get(0);
    }

    private void contrastCI(DiagramContrastDataDto sourceInfo, DiagramContrastDataDto targetInfo, List<CiContrastVO> ciContrastVOList) {
        // 查询分类信息
        Set<Long> classIds = new HashSet<>();
        sourceInfo.getCiList().forEach(ci -> classIds.add(ci.getClassId()));
        targetInfo.getCiList().forEach(ci -> classIds.add(ci.getClassId()));
        CCcCiClass obj = new CCcCiClass();
        obj.setIds(classIds.toArray(new Long[0]));
        List<ESCIClassInfo> classInfoList = esciClassSvc.getListByCdt(obj);
        Map<Long, ESCIClassInfo> classInfoMap = classInfoList.stream().collect(Collectors.toMap(ESCIClassInfo::getId, e -> e, (k1, k2) -> k1));

        Map<String, String> sourceCiAndLocMap = sourceInfo.getCiAndLocMap();
        Map<String, String> targetCiAndLocMap = targetInfo.getCiAndLocMap();
        for (ESCIInfo sourceCiInfo : sourceInfo.getCiList()) {
            Map<String, ESCIInfo> ciMap = targetInfo.getCiMap();
            ESCIInfo targetCiInfo = ciMap.get(sourceCiInfo.getCiPrimaryKey());
            ESCIClassInfo esciClassInfo = classInfoMap.get(sourceCiInfo.getClassId());
            if (BinaryUtils.isEmpty(targetCiInfo)) {
                // 新增
                createAttrChange(ciContrastVOList, sourceCiInfo, esciClassInfo, null, 1, sourceCiAndLocMap.get(sourceCiInfo.getCiCode()), null);
            } else {
                // 对比属性
                Map<String, Object> attrs = sourceCiInfo.getAttrs();
                Map<String, Object> targetCiAttr = targetCiInfo.getAttrs();
                Map<String, ESCIAttrDefInfo> attrDefMap = Collections.emptyMap();
                if (!BinaryUtils.isEmpty(esciClassInfo)) {
                    List<ESCIAttrDefInfo> attrDefs = esciClassInfo.getAttrDefs();
                    attrDefMap = attrDefs.stream().collect(Collectors.toMap(CcCiAttrDef::getProStdName, e -> e, (k1, k2) -> k1));
                }
                List<AssetChangeAttrVO> changeAttrVOS = new ArrayList<>();
                contrastAttr(attrs, targetCiAttr, attrDefMap, changeAttrVOS);
                if (!CollectionUtils.isEmpty(changeAttrVOS)) {
                    createAttrChange(ciContrastVOList, sourceCiInfo, esciClassInfo, changeAttrVOS, 2, sourceCiAndLocMap.get(sourceCiInfo.getCiCode()), targetCiAndLocMap.get(targetCiInfo.getCiCode()));
                }
            }
        }

        for (ESCIInfo targetCiInfo : targetInfo.getCiList()) {
            if (!sourceInfo.getCiMap().containsKey(targetCiInfo.getCiPrimaryKey())) {
                // 删除
                ESCIClassInfo esciClassInfo = classInfoMap.get(targetCiInfo.getClassId());
                CiContrastVO ciContrastVO = new CiContrastVO();
                ciContrastVO.setOperate(3);
                ciContrastVO.setClassType(1);
                ciContrastVO.setClassInfo(esciClassInfo);
                ciContrastVO.setCiInfo(targetCiInfo);
                ciContrastVO.setName(BinaryUtils.isEmpty(targetCiInfo.getCiLabel()) ? "" : targetCiInfo.getCiLabel().replace("[", "").replace("]", "").replaceAll("\"", ""));
                ciContrastVO.setFirstKey(null);
                ciContrastVO.setSecondKey(targetCiAndLocMap.get(targetCiInfo.getCiCode()));
                ciContrastVOList.add(ciContrastVO);
            }
        }
    }

    private void contrastRlt(DiagramContrastDataDto sourceCiAndRltInfo, DiagramContrastDataDto targetCiAndRltInfo, List<CiContrastVO> ciContrastVOList) {
        List<ESCIRltInfo> sourceRltList = sourceCiAndRltInfo.getRltList();
        List<ESCIRltInfo> targetRltList = targetCiAndRltInfo.getRltList();
        List<ESCIInfo> sourceCiList = sourceCiAndRltInfo.getCiList();
        List<ESCIInfo> targetCiList = targetCiAndRltInfo.getCiList();
        Map<String, String> sourceRltCodeAndKeyMap = sourceCiAndRltInfo.getRltAndLocMap();
        Map<String, String> targetRltCodeAndKeyMap = targetCiAndRltInfo.getRltAndLocMap();

        Set<Long> rltClassIds = new HashSet<>();
        sourceRltList.forEach(e -> rltClassIds.add(e.getClassId()));
        targetRltList.forEach(e -> rltClassIds.add(e.getClassId()));
        Map<Long, ESCIClassInfo> rltClassMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(rltClassIds)) {
            CCcCiClass cdt = new CCcCiClass();
            cdt.setIds(rltClassIds.toArray(new Long[0]));
            List<ESCIClassInfo> rltClassList = esRltClassSvc.getListByQuery(QueryBuilders.termsQuery("id", rltClassIds));
            rltClassMap = rltClassList.stream().collect(Collectors.toMap(CcCiClass::getId, e -> e, (k1, k2) -> k1));
        }

        Map<String, ESCIInfo> sourceCIMap = sourceCiList.stream().
                collect(Collectors.toMap(ESCIInfo::getCiCode, source -> source, (k1, k2) -> k1));
        Map<String, ESCIInfo> targetCiMap = targetCiList.stream().
                collect(Collectors.toMap(ESCIInfo::getCiCode, target -> target, (k1, k2) -> k1));

        // 拼装目标端map
        Map<String, ESCIRltInfo> targetRltMap = new HashMap<>();
        for (ESCIRltInfo targetRlt : targetRltList) {
            String rltSourceCiCode = targetRlt.getSourceCiCode();
            String rltTargetCiCode = targetRlt.getTargetCiCode();
            if (BinaryUtils.isEmpty(rltSourceCiCode) || BinaryUtils.isEmpty(rltTargetCiCode)) {
                log.error("########## 对比关系数据目标端标识拼装异常：【" + targetRlt.getId() + "】");
            } else {
                String key = targetCiMap.get(rltSourceCiCode).getCiPrimaryKey() + "-" + targetRlt.getClassId() + "-" + targetCiMap.get(rltTargetCiCode).getCiPrimaryKey();
                targetRltMap.put(key, targetRlt);
            }
        }

        for (ESCIRltInfo sourceRltInfo : sourceRltList) {
            ESCIClassInfo esciClassInfo = rltClassMap.get(sourceRltInfo.getClassId());
            String key = sourceCIMap.get(sourceRltInfo.getSourceCiCode()).getCiPrimaryKey() + "-"+sourceRltInfo.getClassId() + "-" + sourceCIMap.get(sourceRltInfo.getTargetCiCode()).getCiPrimaryKey();

            String sourceCiCode = sourceRltInfo.getSourceCiCode();
            ESCIInfo sourceCiInfo = sourceCIMap.get(sourceCiCode);
            //  String sourceCiLabel = sourceCiInfo.getCiLabel();
            List<String> sourceCiLabel = JSONArray.parseArray(BinaryUtils.isEmpty(sourceCiInfo.getCiLabel())?sourceCiInfo.getCiPrimaryKey():sourceCiInfo.getCiLabel(), String.class);
            String targetCiCode = sourceRltInfo.getTargetCiCode();
            ESCIInfo targetCiInfo = sourceCIMap.get(targetCiCode);
            // String targetCiLabel = targetCiInfo.getCiLabel();
            List<String> targetCiLabel = JSONArray.parseArray(BinaryUtils.isEmpty(targetCiInfo.getCiLabel())?targetCiInfo.getCiPrimaryKey():targetCiInfo.getCiLabel(), String.class);

            if (!targetRltMap.containsKey(key)) {
                CiContrastVO ciContrastVO = new CiContrastVO();
                ciContrastVO.setOperate(1);
                ciContrastVO.setClassType(2);
                ciContrastVO.setClassInfo(esciClassInfo);
                ciContrastVO.setRltInfo(sourceRltInfo);
                ciContrastVO.setFirstKey(sourceRltCodeAndKeyMap.get(sourceRltInfo.getUniqueCode()));
                ciContrastVO.setSecondKey(null);
                try {
                    /*String sourceCiSearchValues = sourceRltInfo.getSourceCiSearchValues();
                    String targetCiSearchValues = sourceRltInfo.getTargetCiSearchValues();
                    String[] sourceValues = sourceCiSearchValues.split("_");
                    String[] targetValues = targetCiSearchValues.split("_");*/
                    ciContrastVO.setName(StringUtils.join(sourceCiLabel,",") + "->" + StringUtils.join(targetCiLabel,","));
                } catch (Exception e) {
                    ciContrastVO.setName("-");
                    log.error("=====关系名称获取失败，关系数据信息 ：" + JSONObject.toJSONString(sourceRltInfo));
                }
                ciContrastVOList.add(ciContrastVO);
            } else {
                ESCIRltInfo esciRltInfoHistory = targetRltMap.get(key);
                Map<String, String> sourceAttrs = sourceRltInfo.getAttrs();
                Map<String, String> targetAttrs = esciRltInfoHistory.getAttrs();
                if (CollectionUtils.isEmpty(sourceAttrs) && CollectionUtils.isEmpty(targetAttrs)) {
                    continue;
                }
                Map<String, ESCIAttrDefInfo> attrDefMap = Collections.emptyMap();
                if (!BinaryUtils.isEmpty(esciClassInfo)) {
                    List<ESCIAttrDefInfo> attrDefs = esciClassInfo.getAttrDefs();
                    attrDefMap = attrDefs.stream().collect(Collectors.toMap(CcCiAttrDef::getProStdName, e -> e, (k1, k2) -> k1));
                }
                List<AssetChangeAttrVO> changeAttrVOS = new ArrayList<>();
                contrastRltAttr(sourceAttrs, targetAttrs, attrDefMap, changeAttrVOS);
                if (!CollectionUtils.isEmpty(changeAttrVOS)) {
                    CiContrastVO ciContrastVO = new CiContrastVO();
                    ciContrastVO.setOperate(2);
                    ciContrastVO.setClassType(2);
                    ciContrastVO.setClassInfo(esciClassInfo);
                    ciContrastVO.setRltInfo(sourceRltInfo);
                    ciContrastVO.setChangeAttr(changeAttrVOS);
                    ciContrastVO.setFirstKey(sourceRltCodeAndKeyMap.get(sourceRltInfo.getUniqueCode()));
                    ciContrastVO.setSecondKey(targetRltCodeAndKeyMap.get(esciRltInfoHistory.getUniqueCode()));
                    try {
                        /*String sourceCiSearchValues = sourceRltInfo.getSourceCiSearchValues();
                        String targetCiSearchValues = sourceRltInfo.getTargetCiSearchValues();
                        String[] sourceValues = sourceCiSearchValues.split("_");
                        String[] targetValues = targetCiSearchValues.split("_");*/
                        ciContrastVO.setName(StringUtils.join(sourceCiLabel,",") + "->" + StringUtils.join(targetCiLabel,","));
                    } catch (Exception e) {
                        ciContrastVO.setName("-");
                        log.error("=====关系名称获取失败，关系数据信息 ：" + JSONObject.toJSONString(sourceRltInfo));
                    }
                    ciContrastVOList.add(ciContrastVO);
                }

            }
        }
        Map<String, ESCIRltInfo> privateRltMap = sourceRltList.stream()
                .filter(e -> !BinaryUtils.isEmpty(sourceCIMap.get(e.getSourceCiCode())) && !BinaryUtils.isEmpty(sourceCIMap.get(e.getTargetCiCode())))
                .collect(Collectors.toMap(e -> sourceCIMap.get(e.getSourceCiCode()).getCiPrimaryKey() + "-" + e.getClassId() + "-" + sourceCIMap.get(e.getTargetCiCode()).getCiPrimaryKey(),
                        e -> e, (k1, k2) -> k1));
        for (ESCIRltInfo targetRlt : targetRltList) {
            String key = targetCiMap.get(targetRlt.getSourceCiCode()).getCiPrimaryKey()+"-"+targetRlt.getClassId()+"-"+targetCiMap.get(targetRlt.getTargetCiCode()).getCiPrimaryKey();
            if (!privateRltMap.containsKey(key)) {
                ESCIClassInfo esciClassInfo = rltClassMap.get(targetRlt.getClassId());
                CiContrastVO ciContrastVO = new CiContrastVO();
                ciContrastVO.setOperate(3);
                ciContrastVO.setClassType(2);
                ciContrastVO.setClassInfo(esciClassInfo);
                ciContrastVO.setRltInfo(targetRlt);
                ciContrastVO.setFirstKey(null);
                ciContrastVO.setSecondKey(targetRltCodeAndKeyMap.get(targetRlt.getUniqueCode()));
                try {
                    ESCIInfo sourceCiInRlt = targetCiMap.get(targetRlt.getSourceCiCode());
                    ESCIInfo targetCiInRlt = targetCiMap.get(targetRlt.getTargetCiCode());
                    List<String> sourceCiLabel = JSONArray.parseArray(sourceCiInRlt.getCiLabel(), String.class);
                    List<String> targetCiLabel = JSONArray.parseArray(targetCiInRlt.getCiLabel(), String.class);
                    ciContrastVO.setName(StringUtils.join(sourceCiLabel,",") + "->" + StringUtils.join(targetCiLabel,","));
                } catch (Exception e) {
                    ciContrastVO.setName("-");
                    log.error("=====关系名称获取失败，关系数据信息 ：" + JSONObject.toJSONString(targetRlt));
                }
                ciContrastVOList.add(ciContrastVO);
            }
        }
    }

    private void getDiagramCiAndRltInfo(ESDiagramDTO secondDiagramInfo, Map<String, Long> ciCodeVersionMap, Map<String, Long> rltUniqueCodeMap, Map<String, String> ciCodeMapKey, Map<String, String> rltCodeMapKey) {
        for (ESDiagramModel model : secondDiagramInfo.getDiagram().getModelList()) {
            if (!BinaryUtils.isEmpty(model.getNodeDataArray())) {
                model.getNodeDataArray().stream().forEach(e -> {
                    if (!BinaryUtils.isEmpty(e.getCiCode())&&!BinaryUtils.isEmpty(e.getKey())) {
                        ciCodeVersionMap.put(e.getCiCode(), e.getVersion());
                        ciCodeMapKey.put(e.getCiCode(), e.getKey());
                    }
                });
            }
            if (!BinaryUtils.isEmpty(model.getLinkDataArray())) {
                model.getLinkDataArray().stream().forEach(e->{
                    if (!BinaryUtils.isEmpty(e.getUniqueCode())&&!BinaryUtils.isEmpty(e.getKey())) {
                        rltUniqueCodeMap.put(e.getUniqueCode(), e.getVersion());
                        rltCodeMapKey.put(e.getUniqueCode(), e.getKey());
                    }
                });
            }
        }
    }

    private void contrastRltAttr(Map<String, String> attrs, Map<String, String> historyAttrs, Map<String, ESCIAttrDefInfo> attrDefMap, List<AssetChangeAttrVO> changeAttrVOS) {
        for (Map.Entry<String, String> entry : attrs.entrySet()) {
            String key = entry.getKey().trim().toUpperCase();
//                    if (IGNORE_ATTR.contains(key)) {
//                        designCiAttr.remove(key);
//                        continue;
//                    }
            AssetChangeAttrVO attrVO = new AssetChangeAttrVO();
            ESCIAttrDefInfo ccCiAttrDef = attrDefMap.get(key);
            if (!BinaryUtils.isEmpty(ccCiAttrDef)) {
                attrVO.setProType(ccCiAttrDef.getProType());
            }
            attrVO.setChangeAttr(key);
            String value = BinaryUtils.isEmpty(entry.getValue()) ? "" : entry.getValue();
            if (!historyAttrs.containsKey(key)) {
                // 删除属性
                attrVO.setAfterVal(BinaryUtils.isEmpty(value) ? "-" : value);
                attrVO.setBeforeVal("-");
                changeAttrVOS.add(attrVO);
                continue;
            }
            if (historyAttrs.containsKey(key) && !value.equals(historyAttrs.get(key))) {
                attrVO.setAfterVal(BinaryUtils.isEmpty(value) ? "-" : value);
                attrVO.setBeforeVal(BinaryUtils.isEmpty(historyAttrs.get(key)) ? "-" : historyAttrs.get(key));
                changeAttrVOS.add(attrVO);
                historyAttrs.remove(key);
                continue;
            }
            if (historyAttrs.containsKey(key) && value.equals(historyAttrs.get(key))) {
                historyAttrs.remove(key);
            }
        }
        for (Map.Entry<String, String> releaseAttr : historyAttrs.entrySet()) {
            if (BinaryUtils.isEmpty(releaseAttr
                    .getValue())) {
                continue;
            }
            AssetChangeAttrVO attrVO = new AssetChangeAttrVO();
            changeAttrVOS.add(attrVO);
            attrVO.setChangeAttr(releaseAttr.getKey());
            attrVO.setBeforeVal(BinaryUtils.isEmpty(releaseAttr.getValue()) ? "-" : releaseAttr.getValue());
            CcCiAttrDef ccCiAttrDef = attrDefMap.get(releaseAttr.getKey());
            if (!BinaryUtils.isEmpty(ccCiAttrDef)) {
                attrVO.setProType(ccCiAttrDef.getProType());
            }
        }

    }

    private void createAttrChange(List<CiContrastVO> ciContrastVOList, ESCIInfo esciInfo, ESCIClassInfo esciClassInfo, List<AssetChangeAttrVO> changeAttrVOS, Integer type,String firstKey,String secondKey) {
        CiContrastVO ciContrastVO = new CiContrastVO();
        ciContrastVO.setOperate(type);
        ciContrastVO.setCiInfo(esciInfo);
        ciContrastVO.setClassType(1);
        ciContrastVO.setClassInfo(esciClassInfo);
        ciContrastVO.setChangeAttr(changeAttrVOS);
        ciContrastVO.setName(BinaryUtils.isEmpty(esciInfo.getCiLabel()) ? "" : esciInfo.getCiLabel().replace("[", "").replace("]", "").replaceAll("\"", ""));
        ciContrastVO.setFirstKey(firstKey);
        ciContrastVO.setSecondKey(secondKey);
        ciContrastVOList.add(ciContrastVO);
    }

    private void contrastAttr(Map<String, Object> attrs, Map<String, Object> designCiAttr, Map<String, ESCIAttrDefInfo> attrDefMap, List<AssetChangeAttrVO> changeAttrVOS) {
        for (Map.Entry<String, Object> entry : attrs.entrySet()) {
            String key = entry.getKey().trim().toUpperCase();
//                    if (IGNORE_ATTR.contains(key)) {
//                        designCiAttr.remove(key);
//                        continue;
//                    }
            AssetChangeAttrVO attrVO = new AssetChangeAttrVO();
            ESCIAttrDefInfo ccCiAttrDef = attrDefMap.get(key);
            if (!BinaryUtils.isEmpty(ccCiAttrDef)) {
                attrVO.setProType(ccCiAttrDef.getProType());
            }
            attrVO.setChangeAttr(key);
            String value = BinaryUtils.isEmpty(entry.getValue()) ? "" : entry.getValue().toString();
            if (!designCiAttr.containsKey(key)) {
                // 删除属性
                attrVO.setAfterVal(BinaryUtils.isEmpty(value) ? "-" : value);
                changeAttrVOS.add(attrVO);
                continue;
            }
            String targetValue = BinaryUtils.isEmpty(designCiAttr.get(key)) ? "" : designCiAttr.get(key).toString();
            if (designCiAttr.containsKey(key) && !value.equals(targetValue)) {
                attrVO.setAfterVal(BinaryUtils.isEmpty(value) ? "-" : value);
                attrVO.setBeforeVal(BinaryUtils.isEmpty(targetValue) ? "-" : targetValue.toString());
                changeAttrVOS.add(attrVO);
                designCiAttr.remove(key);
                continue;
            }
            if (designCiAttr.containsKey(key) && value.equals(targetValue)) {
                designCiAttr.remove(key);
            }
        }
        for (Map.Entry<String, Object> releaseAttr : designCiAttr.entrySet()) {
            if (BinaryUtils.isEmpty(releaseAttr
                    .getValue())) {
                continue;
            }
            AssetChangeAttrVO attrVO = new AssetChangeAttrVO();
            changeAttrVOS.add(attrVO);
            attrVO.setChangeAttr(releaseAttr.getKey());
            attrVO.setBeforeVal(BinaryUtils.isEmpty(releaseAttr.getValue()) ? "" : releaseAttr.getValue().toString());
            CcCiAttrDef ccCiAttrDef = attrDefMap.get(releaseAttr.getKey());
            if (!BinaryUtils.isEmpty(ccCiAttrDef)) {
                attrVO.setProType(ccCiAttrDef.getProType());
            }
        }
    }

    private List<List<String>> head(DiagramContrastVO data) {
        List<List<String>> list = ListUtils.newArrayList();
        List<String> head0 = ListUtils.newArrayList();
        head0.add("对比结果");
        List<String> head1 = ListUtils.newArrayList();
        head1.add("类型");
        List<String> head2 = ListUtils.newArrayList();
        head2.add("名称");
        List<String> head3 = ListUtils.newArrayList();
        head3.add(data.getFirstDiagramInfo().getDiagram().getName());
        List<String> head4 = ListUtils.newArrayList();
        head4.add(data.getSecondDiagramInfo().getDiagram().getName()+"-基准");
        list.add(head0);
        list.add(head1);
        list.add(head2);
        list.add(head3);
        list.add(head4);
        return list;
    }

    private List<List<Object>> dataList(DiagramContrastVO diagramContrastVO) {

        List<List<Object>> list = ListUtils.newArrayList();
        for (CiContrastVO ciContrastVO : diagramContrastVO.getCiContrastVOList()) {
            List<Object> data = ListUtils.newArrayList();
            data.add(getOperateType(ciContrastVO.getOperate()));
            data.add(ciContrastVO.getClassInfo().getClassName());
            data.add(ciContrastVO.getName());
            if (ciContrastVO.getOperate().equals(1)) {
                data.add("新增");
            } else {
                data.add("");
            }
            if (ciContrastVO.getOperate().equals(3)) {
                data.add("删除");
            } else {
                data.add("");
            }
            list.add(data);
            if (!CollectionUtils.isEmpty(ciContrastVO.getChangeAttr())) {
                for (AssetChangeAttrVO assetChangeAttrVO : ciContrastVO.getChangeAttr()) {
                    List<Object> subData = ListUtils.newArrayList();
                    subData.add("");
                    subData.add("");
                    subData.add(assetChangeAttrVO.getChangeAttr());
                    subData.add(assetChangeAttrVO.getAfterVal());
                    subData.add(assetChangeAttrVO.getBeforeVal());
                    list.add(subData);
                }
            }
        }
        return list;
    }

    private Object getOperateType(Integer operate) {
        String operateStr = null;
        switch (operate) {
            case 1:
                operateStr = "新增";
                break;
            case 2:
                operateStr = "修改";
                break;
            case 3:
                operateStr = "删除";
                break;
            default:
                operateStr = "";
        }
        return operateStr;
    }


    public ResponseEntity<byte[]> export(DiagramContrastParam param) {
        DiagramContrastVO data = this.diagramContras(param);
        String fileName = FileUtil.ExcelUtil.getExportFileName("【" + data.getFirstDiagramInfo().getDiagram().getName() + "】-【"
                + data.getSecondDiagramInfo().getDiagram().getName() + "】视图对比", CommUtil.EXCEL07_XLSX_EXTENSION, true);
        ResponseEntity<byte[]> responseEntity = null;
        try (ByteArrayOutputStream out = new ByteArrayOutputStream()){
            ExcelWriterBuilder write = EasyExcel.write(out);
            ExcelWriter build = write.build();
            write.head(head(data)).sheet("视图对比").registerWriteHandler(new CustomCellWriteHandler()).doWrite(dataList(data));
//            write.head(head(data)).sheet("视图对比").doWrite(dataList(data));
            responseEntity = this.returnRes(out.toByteArray(), fileName);

            build.finish();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return responseEntity;
    }

    private ResponseEntity<byte[]> returnRes(byte[] bytes, String fileName) {
        HttpHeaders headers = new HttpHeaders();
        ResponseEntity<byte[]> entity = null;
        try {
            headers.add("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            entity = new ResponseEntity<byte[]>(bytes, headers, HttpStatus.OK);
        } catch (Exception e) {
            throw new MessageException(e.getMessage());
        }
        return entity;
    }
}
