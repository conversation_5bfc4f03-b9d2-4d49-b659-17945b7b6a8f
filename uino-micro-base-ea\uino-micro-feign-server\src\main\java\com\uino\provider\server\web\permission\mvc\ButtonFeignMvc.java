package com.uino.provider.server.web.permission.mvc;

import com.uino.service.permission.microservice.IButtonSvc;
import com.uino.bean.permission.base.SysModule;
import com.uino.bean.permission.query.SysModuleCheck;
import com.uino.provider.feign.permission.ButtonFeign;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * @Title: ButtonFeignMvc
 * @Description: ButtonFeignMvc
 * @Author: YGQ
 * @Create: 2021-07-05 15:20
 **/
@RestController
@RequestMapping("feign/button")
public class ButtonFeignMvc implements ButtonFeign {

    private final IButtonSvc iButtonSvc;

    @Autowired
    public ButtonFeignMvc(IButtonSvc iButtonSvc) {
        this.iButtonSvc = iButtonSvc;
    }

    @Override
    public SysModule saveButton(SysModule sysButton) {
        return iButtonSvc.saveButton(sysButton);
    }

    @Override
    public void deleteButton(Long buttonId) {
        iButtonSvc.deleteButton(buttonId);
    }

    @Override
    public void saveButtonSort(Map<Long, Integer> orderDict) {
        iButtonSvc.saveButtonSort(orderDict);
    }

    @Override
    public SysModuleCheck checkModuleSign(SysModule sysButton) {
        return iButtonSvc.checkModuleSign(sysButton);
    }
}
