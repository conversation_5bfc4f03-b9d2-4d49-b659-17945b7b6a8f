package com.uinnova.product.eam.comm.model;




import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;


@Comment("标签表[VC_TAG]")
public class CVcTag implements Condition {
	private static final long serialVersionUID = 1L;


	@Comment("ID[ID] operate-Equal[=]")
	private Long id;


	@Comment("ID[ID] operate-In[in]")
	private Long[] ids;


	@Comment("ID[ID] operate-GTEqual[>=]")
	private Long startId;

	@Comment("ID[ID] operate-LTEqual[<=]")
	private Long endId;


	@Comment("标签名称[TAG_NAME] operate-Like[like]")
	private String tagName;


	@Comment("标签名称[TAG_NAME] operate-Equal[=]")
	private String tagNameEqual;


	@Comment("标签名称[TAG_NAME] operate-In[in]")
	private String[] tagNames;


	@Comment("标签类型[TAG_TYPE] operate-Equal[=]    标签类型:1=视图标签")
	private Integer tagType;


	@Comment("标签类型[TAG_TYPE] operate-In[in]    标签类型:1=视图标签")
	private Integer[] tagTypes;


	@Comment("标签类型[TAG_TYPE] operate-GTEqual[>=]    标签类型:1=视图标签")
	private Integer startTagType;

	@Comment("标签类型[TAG_TYPE] operate-LTEqual[<=]    标签类型:1=视图标签")
	private Integer endTagType;


	@Comment("定义描述[DEF_DESC] operate-Like[like]")
	private String defDesc;


	@Comment("父标签id[PARENT_ID] operate-Equal[=]")
	private Long parentId;


	@Comment("父标签id[PARENT_ID] operate-In[in]")
	private Long[] parentIds;


	@Comment("父标签id[PARENT_ID] operate-GTEqual[>=]")
	private Long startParentId;

	@Comment("父标签id[PARENT_ID] operate-LTEqual[<=]")
	private Long endParentId;


	@Comment("标签属性[TAG_ATTR] operate-Equal[=]    0=无,1=不能删除,2=不能删除且为团队")
	private Integer tagAttr;


	@Comment("标签属性[TAG_ATTR] operate-In[in]    0=无,1=不能删除,2=不能删除且为团队")
	private Integer[] tagAttrs;


	@Comment("标签属性[TAG_ATTR] operate-GTEqual[>=]    0=无,1=不能删除,2=不能删除且为团队")
	private Integer startTagAttr;

	@Comment("标签属性[TAG_ATTR] operate-LTEqual[<=]    0=无,1=不能删除,2=不能删除且为团队")
	private Integer endTagAttr;


	@Comment("所属域[DOMAIN_ID] operate-Equal[=]")
	private Long domainId;


	@Comment("所属域[DOMAIN_ID] operate-In[in]")
	private Long[] domainIds;


	@Comment("所属域[DOMAIN_ID] operate-GTEqual[>=]")
	private Long startDomainId;

	@Comment("所属域[DOMAIN_ID] operate-LTEqual[<=]")
	private Long endDomainId;


	@Comment("数据状态[DATA_STATUS] operate-Equal[=]    数据状态:数据状态：1-正常 0-删除")
	private Integer dataStatus;


	@Comment("数据状态[DATA_STATUS] operate-In[in]    数据状态:数据状态：1-正常 0-删除")
	private Integer[] dataStatuss;


	@Comment("数据状态[DATA_STATUS] operate-GTEqual[>=]    数据状态:数据状态：1-正常 0-删除")
	private Integer startDataStatus;

	@Comment("数据状态[DATA_STATUS] operate-LTEqual[<=]    数据状态:数据状态：1-正常 0-删除")
	private Integer endDataStatus;


	@Comment("创建人[CREATOR] operate-Like[like]")
	private String creator;


	@Comment("创建人[CREATOR] operate-Equal[=]")
	private String creatorEqual;


	@Comment("创建人[CREATOR] operate-In[in]")
	private String[] creators;


	@Comment("修改人[MODIFIER] operate-Like[like]")
	private String modifier;


	@Comment("修改人[MODIFIER] operate-Equal[=]")
	private String modifierEqual;


	@Comment("修改人[MODIFIER] operate-In[in]")
	private String[] modifiers;


	@Comment("创建时间[CREATE_TIME] operate-Equal[=]    创建时间:yyyyMMddHHmmss")
	private Long createTime;


	@Comment("创建时间[CREATE_TIME] operate-In[in]    创建时间:yyyyMMddHHmmss")
	private Long[] createTimes;


	@Comment("创建时间[CREATE_TIME] operate-GTEqual[>=]    创建时间:yyyyMMddHHmmss")
	private Long startCreateTime;

	@Comment("创建时间[CREATE_TIME] operate-LTEqual[<=]    创建时间:yyyyMMddHHmmss")
	private Long endCreateTime;


	@Comment("修改时间[MODIFY_TIME] operate-Equal[=]    修改时间:yyyyMMddHHmmss")
	private Long modifyTime;


	@Comment("修改时间[MODIFY_TIME] operate-In[in]    修改时间:yyyyMMddHHmmss")
	private Long[] modifyTimes;


	@Comment("修改时间[MODIFY_TIME] operate-GTEqual[>=]    修改时间:yyyyMMddHHmmss")
	private Long startModifyTime;

	@Comment("修改时间[MODIFY_TIME] operate-LTEqual[<=]    修改时间:yyyyMMddHHmmss")
	private Long endModifyTime;




	public Long getId() {
		return this.id;
	}
	public void setId(Long id) {
		this.id = id;
	}


	public Long[] getIds() {
		return this.ids;
	}
	public void setIds(Long[] ids) {
		this.ids = ids;
	}


	public Long getStartId() {
		return this.startId;
	}
	public void setStartId(Long startId) {
		this.startId = startId;
	}


	public Long getEndId() {
		return this.endId;
	}
	public void setEndId(Long endId) {
		this.endId = endId;
	}


	public String getTagName() {
		return this.tagName;
	}
	public void setTagName(String tagName) {
		this.tagName = tagName;
	}


	public String getTagNameEqual() {
		return this.tagNameEqual;
	}
	public void setTagNameEqual(String tagNameEqual) {
		this.tagNameEqual = tagNameEqual;
	}


	public String[] getTagNames() {
		return this.tagNames;
	}
	public void setTagNames(String[] tagNames) {
		this.tagNames = tagNames;
	}


	public Integer getTagType() {
		return this.tagType;
	}
	public void setTagType(Integer tagType) {
		this.tagType = tagType;
	}


	public Integer[] getTagTypes() {
		return this.tagTypes;
	}
	public void setTagTypes(Integer[] tagTypes) {
		this.tagTypes = tagTypes;
	}


	public Integer getStartTagType() {
		return this.startTagType;
	}
	public void setStartTagType(Integer startTagType) {
		this.startTagType = startTagType;
	}


	public Integer getEndTagType() {
		return this.endTagType;
	}
	public void setEndTagType(Integer endTagType) {
		this.endTagType = endTagType;
	}


	public String getDefDesc() {
		return this.defDesc;
	}
	public void setDefDesc(String defDesc) {
		this.defDesc = defDesc;
	}


	public Long getParentId() {
		return this.parentId;
	}
	public void setParentId(Long parentId) {
		this.parentId = parentId;
	}


	public Long[] getParentIds() {
		return this.parentIds;
	}
	public void setParentIds(Long[] parentIds) {
		this.parentIds = parentIds;
	}


	public Long getStartParentId() {
		return this.startParentId;
	}
	public void setStartParentId(Long startParentId) {
		this.startParentId = startParentId;
	}


	public Long getEndParentId() {
		return this.endParentId;
	}
	public void setEndParentId(Long endParentId) {
		this.endParentId = endParentId;
	}


	public Integer getTagAttr() {
		return this.tagAttr;
	}
	public void setTagAttr(Integer tagAttr) {
		this.tagAttr = tagAttr;
	}


	public Integer[] getTagAttrs() {
		return this.tagAttrs;
	}
	public void setTagAttrs(Integer[] tagAttrs) {
		this.tagAttrs = tagAttrs;
	}


	public Integer getStartTagAttr() {
		return this.startTagAttr;
	}
	public void setStartTagAttr(Integer startTagAttr) {
		this.startTagAttr = startTagAttr;
	}


	public Integer getEndTagAttr() {
		return this.endTagAttr;
	}
	public void setEndTagAttr(Integer endTagAttr) {
		this.endTagAttr = endTagAttr;
	}


	public Long getDomainId() {
		return this.domainId;
	}
	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}


	public Long[] getDomainIds() {
		return this.domainIds;
	}
	public void setDomainIds(Long[] domainIds) {
		this.domainIds = domainIds;
	}


	public Long getStartDomainId() {
		return this.startDomainId;
	}
	public void setStartDomainId(Long startDomainId) {
		this.startDomainId = startDomainId;
	}


	public Long getEndDomainId() {
		return this.endDomainId;
	}
	public void setEndDomainId(Long endDomainId) {
		this.endDomainId = endDomainId;
	}


	public Integer getDataStatus() {
		return this.dataStatus;
	}
	public void setDataStatus(Integer dataStatus) {
		this.dataStatus = dataStatus;
	}


	public Integer[] getDataStatuss() {
		return this.dataStatuss;
	}
	public void setDataStatuss(Integer[] dataStatuss) {
		this.dataStatuss = dataStatuss;
	}


	public Integer getStartDataStatus() {
		return this.startDataStatus;
	}
	public void setStartDataStatus(Integer startDataStatus) {
		this.startDataStatus = startDataStatus;
	}


	public Integer getEndDataStatus() {
		return this.endDataStatus;
	}
	public void setEndDataStatus(Integer endDataStatus) {
		this.endDataStatus = endDataStatus;
	}


	public String getCreator() {
		return this.creator;
	}
	public void setCreator(String creator) {
		this.creator = creator;
	}


	public String getCreatorEqual() {
		return this.creatorEqual;
	}
	public void setCreatorEqual(String creatorEqual) {
		this.creatorEqual = creatorEqual;
	}


	public String[] getCreators() {
		return this.creators;
	}
	public void setCreators(String[] creators) {
		this.creators = creators;
	}


	public String getModifier() {
		return this.modifier;
	}
	public void setModifier(String modifier) {
		this.modifier = modifier;
	}


	public String getModifierEqual() {
		return this.modifierEqual;
	}
	public void setModifierEqual(String modifierEqual) {
		this.modifierEqual = modifierEqual;
	}


	public String[] getModifiers() {
		return this.modifiers;
	}
	public void setModifiers(String[] modifiers) {
		this.modifiers = modifiers;
	}


	public Long getCreateTime() {
		return this.createTime;
	}
	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}


	public Long[] getCreateTimes() {
		return this.createTimes;
	}
	public void setCreateTimes(Long[] createTimes) {
		this.createTimes = createTimes;
	}


	public Long getStartCreateTime() {
		return this.startCreateTime;
	}
	public void setStartCreateTime(Long startCreateTime) {
		this.startCreateTime = startCreateTime;
	}


	public Long getEndCreateTime() {
		return this.endCreateTime;
	}
	public void setEndCreateTime(Long endCreateTime) {
		this.endCreateTime = endCreateTime;
	}


	public Long getModifyTime() {
		return this.modifyTime;
	}
	public void setModifyTime(Long modifyTime) {
		this.modifyTime = modifyTime;
	}


	public Long[] getModifyTimes() {
		return this.modifyTimes;
	}
	public void setModifyTimes(Long[] modifyTimes) {
		this.modifyTimes = modifyTimes;
	}


	public Long getStartModifyTime() {
		return this.startModifyTime;
	}
	public void setStartModifyTime(Long startModifyTime) {
		this.startModifyTime = startModifyTime;
	}


	public Long getEndModifyTime() {
		return this.endModifyTime;
	}
	public void setEndModifyTime(Long endModifyTime) {
		this.endModifyTime = endModifyTime;
	}


}


