package com.uinnova.product.eam.db.impl;


import com.uinnova.product.eam.comm.model.CVcComment;
import com.uinnova.product.eam.comm.model.VcComment;
import com.uinnova.product.eam.db.VcCommentDao;
import com.uinnova.product.eam.db.support.dao.mybatis.ComMyBatisBinaryDaoImpl;


/**
 * 视图评论表[VC_COMMENT]数据访问对象实现
 */
public class VcCommentDaoImpl extends ComMyBatisBinaryDaoImpl<VcComment, CVcComment> implements VcCommentDao {

//    @Override
//    public String getTableName() {
//        return "IAMS." + getDaoDefinition().getTableName();
//    }
}


