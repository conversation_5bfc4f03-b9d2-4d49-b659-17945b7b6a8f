package com.uinnova.product.eam.model.dto;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.comm.model.ci.CcCiClass;
import lombok.Data;

import java.util.List;

/**
 * 数据超市+对象分类返回实体类
 * <AUTHOR>
 * @date 2022/3/7
 */
@Data
public class EamDataSetClassDto {
    @Comment("数据超市id")
    private Long id;

    @Comment("数据超市名称")
    private String name;

    @Comment("权限级别")
    private Integer permissionLevel;

    @Comment("根节点分类id")
    private Long classId;

    @Comment("CI分类")
    private CcCiClass ciClass;

    @Comment("属性定义")
    private List<CcCiAttrDef> attrDefs;

    @Comment("根节点分类ci对象数量")
    private Long ciCount;

    @Comment("数据超市包含的所有ci分类")
    private List<CcCiClass> ciClassList;

}
