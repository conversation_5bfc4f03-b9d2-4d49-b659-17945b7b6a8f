package com.uinnova.product.eam.workable.model;

import lombok.Data;

import jakarta.persistence.*;
import java.sql.Date;
import java.sql.Timestamp;

/**
 * 可以直接操作flowable的任务表
 *
 * <AUTHOR>
 * @since 2024/7/22 上午9:57
 */
@Data
@Entity
@Table(name="act_ru_task")
public class ActRuTask {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID_")
    private String id;

    @Column(name = "REV_")
    private int rev;

    @Column(name = "EXECUTION_ID_")
    private String executionId;

    @Column(name = "PROC_INST_ID_")
    private String procInstId;

    @Column(name = "PROC_DEF_ID_")
    private String procDefId;

    @Column(name = "TASK_DEF_ID_")
    private String taskDefId;

    @Column(name = "SCOPE_ID_")
    private String scopeId;

    @Column(name = "SUB_SCOPE_ID_")
    private String subScopeId;

    @Column(name = "SCOPE_TYPE_")
    private String scopeType;

    @Column(name = "SCOPE_DEFINITION_ID_")
    private String scopeDefinitionId;

    @Column(name = "NAME_")
    private String name;

    @Column(name = "PARENT_TASK_ID_")
    private String parentTaskId;

    @Column(name = "DESCRIPTION_")
    private String description;

    @Column(name = "TASK_DEF_KEY_")
    private String taskDefKey;

    @Column(name = "OWNER_")
    private String owner;

    @Column(name = "ASSIGNEE_")
    private String assignee;

    @Column(name = "DELEGATION_")
    private String delegation;

    @Column(name = "PRIORITY_")
    private int priority;

    @Column(name = "CREATE_TIME_")
    private Timestamp createTime;

    @Column(name = "DUE_DATE_")
    private Date dueDate;

    @Column(name = "CATEGORY_")
    private String category;

    @Column(name = "SUSPENSION_STATE_")
    private int suspensionState;

    @Column(name = "TENANT_ID_")
    private String tenantId;

    @Column(name = "FORM_KEY_")
    private String formKey;

    @Column(name = "CLAIM_TIME_")
    private Date claimTime;

    @Column(name = "IS_COUNT_ENABLED_")
    private Boolean isCountEnabled;

    @Column(name = "VAR_COUNT_")
    private int varCount;

    @Column(name = "ID_LINK_COUNT_")
    private int idLinkCount;

    @Column(name = "SUB_TASK_COUNT_")
    private int subTaskCount;

}
