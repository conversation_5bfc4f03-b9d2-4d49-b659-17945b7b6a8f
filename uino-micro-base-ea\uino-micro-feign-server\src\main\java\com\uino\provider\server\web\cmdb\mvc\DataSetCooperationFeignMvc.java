package com.uino.provider.server.web.cmdb.mvc;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.uino.service.cmdb.dataset.microservice.IDataSetCooperationSvc;
import com.uino.provider.feign.cmdb.DataSetCooperationFeign;

/**
 * 
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("feign/cmdb/datasetCooperation")
public class DataSetCooperationFeignMvc implements DataSetCooperationFeign {

	Log logger = LogFactory.getLog(DataSetCooperationFeignMvc.class);
	
	@Autowired
	private IDataSetCooperationSvc datasetCooperationSvc;
	
	@Override
	public List<JSONObject> findByDataSetId(Long dataSetId) {
		logger.info("findByDataSetId param: "+dataSetId);
		Long now = System.currentTimeMillis();
		List<JSONObject> ret = datasetCooperationSvc.findByDataSetId(dataSetId);
		logger.info("findByDataSetId time: "+(System.currentTimeMillis()-now));
		return ret;
	}

	@Override
	public Boolean updateCooperation(JSONObject body) {
		logger.info("updateCooperation param: "+body.toString());
		Long now = System.currentTimeMillis();
		List<JSONObject> coordinationUserCodeList = new ArrayList<JSONObject>();
		if (body.containsKey("coordinationUserCodeList") && body.getJSONArray("coordinationUserCodeList")!=null) {
			JSONArray arr = body.getJSONArray("coordinationUserCodeList");
			for (int i=0;i<arr.size();i++) {
				coordinationUserCodeList.add(arr.getJSONObject(i));
			}
		}
		boolean ret = datasetCooperationSvc.updateCooperation(body.getLong("dataSetId"), coordinationUserCodeList);
		logger.info("updateCooperation time: "+(System.currentTimeMillis()-now));
		return ret;
	}

}
