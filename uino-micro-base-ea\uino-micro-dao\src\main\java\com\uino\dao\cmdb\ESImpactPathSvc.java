package com.uino.dao.cmdb;

import jakarta.annotation.PostConstruct;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.stereotype.Service;

import com.uino.dao.AbstractESBaseDao;
import com.uino.dao.ESConst;
import com.uino.bean.cmdb.base.ESImpactPath;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
public class ESImpactPathSvc extends AbstractESBaseDao<ESImpactPath, ESImpactPath> {

	Log logger = LogFactory.getLog(ESImpactPathSvc.class);
	
	@Override
	public String getIndex() {
		return ESConst.INDEX_CMDB_IMPACTPATH;
	}

	@Override
	public String getType() {
		return "impactpath";
	}
	
	@PostConstruct
    public void init() {
        super.initIndex();
    }

}
