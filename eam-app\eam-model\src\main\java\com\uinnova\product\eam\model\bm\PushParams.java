package com.uinnova.product.eam.model.bm;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.base.diagram.model.ESDiagram;
import com.uinnova.product.eam.base.diagram.model.ESDiagramDTO;
import com.uinnova.product.eam.comm.model.es.EamCategory;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Accessors(chain = true)
@Data
public class PushParams {
    @Comment("发布视图id集合")
    private List<String> diagramIds;
    @Comment("所属用户code")
    private String ownerCode;
    @Comment("模型树id")
    private Long modelId;
    @Comment("视图详细信息")
    private List<ESDiagram> diagramList;
    @Comment("视图是否已发布状态")
    private List<Integer> isOpens;

    /**
     * 以下为 新发布校验逻辑流转的参数
     */
    @Comment("模型视图ID集合")
    private List<String> modelDiagramIds;
    @Comment("模型视图信息集合")
    private List<ESDiagramDTO> modelDiagramList;
    @Comment("拥有者 -> 拥有者名下的模型视图信息")
    private Map<String, List<ESDiagramDTO>> modelDiagramMap;

    @Comment("普通视图ID集合")
    private List<String> commonDiagramIds;
    @Comment("普通视图信息集合")
    private List<ESDiagramDTO> commonDiagramList;
    @Comment("拥有者 -> 拥有者名下的普通视图信息")
    private Map<String, List<ESDiagramDTO>> commonDiagramMap;

    @Comment("模型目录ID集合")
    private List<Long> modelDirIds;
    @Comment("模型目录信息集合")
    private List<EamCategory> modelCategoryList;
    @Comment("拥有者 -> 拥有者名下的模型目录信息")
    private Map<String, List<EamCategory>> modelCategoryMap;

    @Comment("模型视图/普通视图ID集合")
    private List<String> allProcessDiagramIds;
    @Comment("模型视图/普通视图信息集合")
    private List<ESDiagramDTO> allProcessDiagramInfo;
    @Comment("拥有者 -> 拥有者名下的模型视图/普通视图信息集合")
    private Map<String, List<ESDiagramDTO>> allProcessDiagramMap;

    @Comment("模型树发布或模型视图发布")
    private Boolean isModelTree;

    @Comment("我的空间目录信息id-info")
    private Map<Long, EamCategory> privateCategoryIdMap;
    @Comment("资产仓库目录信息modelId -> ciCode-info")
    private Map<Long, Map<String, EamCategory>> designCategoryCiCodeModelIdMap;
    @Comment("我的空间目录信息userCode -> modelIds")
    private Map<String, List<Long>> userCodesModelIdMap;
    @Comment("我的空间目录信息userCode -> modelIds")
    private Map<String, List<EamCategory>> privateModelCategoryUserCodeMap;
    @Comment("我的空间目录信息userCode -> modelId-info")
    private Map<String, Map<Long, List<EamCategory>>> privateModelCategoryModelIdUserCodeMap;


}
