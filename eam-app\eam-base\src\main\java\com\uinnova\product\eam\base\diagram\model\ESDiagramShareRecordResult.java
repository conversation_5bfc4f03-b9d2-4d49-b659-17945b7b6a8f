package com.uinnova.product.eam.base.diagram.model;

import cn.hutool.crypto.SecureUtil;
import com.binary.framework.bean.annotation.Comment;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.uino.bean.permission.base.SysUser;
import lombok.Data;

@Data
public class ESDiagramShareRecordResult {

    private Long id;

    @JsonIgnore
    private Long diagramId;

    @JsonProperty("diagramId")
    private String dEnergy;

    @Comment("制品类型分类")
    private Integer type;

    private Long ownerId;

    private SysUser ownerSysUser;

    private Long sharedUserId;

    private SysUser sharedSysUser;

    private Integer permission;

    private ESDiagram esDiagram;

    public String getdEnergy() {
        return SecureUtil.md5(String.valueOf(diagramId)).substring(8, 24);
    }

    public void setdEnergy(String dEnergy) {
        this.dEnergy = dEnergy;
    }

    @Comment("记录创建时间[CREATE_TIME]    创建时间:yyyyMMddHHmmss")
    private Long diagramModifyTime;

    @Comment("记录更新时间[MODIFY_TIME]    更新时间:yyyyMMddHHmmss")
    private Long modifyTime;
}
