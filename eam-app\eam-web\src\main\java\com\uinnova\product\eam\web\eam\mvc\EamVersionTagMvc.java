package com.uinnova.product.eam.web.eam.mvc;

import com.binary.core.util.BinaryUtils;
import com.binary.framework.web.RemoteResult;
import com.uinnova.product.eam.comm.model.es.EamVersionTag;
import com.uinnova.product.eam.model.dto.EamVersionTagDto;
import com.uinnova.product.eam.service.asset.EamVersionTagSvc;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 版本标签
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/version")
public class EamVersionTagMvc {

    @Resource
    private EamVersionTagSvc versionTagSvc;

    @PostMapping("/createFlowModelTag")
    @ModDesc(desc = "创建版本标签", pDesc = "标签信息", rDesc = "创建结果", rType = RemoteResult.class)
    public RemoteResult createFlowModelTag(@RequestBody EamVersionTagDto dto) {
        Integer result = versionTagSvc.createFlowModelTag(dto);
        return new RemoteResult(result);
    }

    @PostMapping("/deleteById")
    @ModDesc(desc = "删除版本标签", pDesc = "标签信息id", rDesc = "删除结果", rType = RemoteResult.class)
    public RemoteResult deleteById(@RequestBody EamVersionTagDto dto) {
        BinaryUtils.checkEmpty(dto.getId(), "id");
        Integer result = versionTagSvc.deleteById(dto);
        return new RemoteResult(result);
    }

    @GetMapping("/getHistoryVersion")
    @ModDesc(desc = "获取历史版本标签", pDesc = "分支id", rDesc = "标签信息", rType = RemoteResult.class)
    public RemoteResult getHistoryVersion(@RequestParam Long branchId) {
        List<EamVersionTag> result = versionTagSvc.getHistoryVersion(branchId);
        return new RemoteResult(result);
    }

    @GetMapping("/getVersionById")
    @ModDesc(desc = "获取版本标签详情", pDesc = "版本标签id", rDesc = "标签信息", rType = RemoteResult.class)
    public RemoteResult getVersionById(@RequestParam Long id) {
        EamVersionTag result = versionTagSvc.getVersionById(id);
        return new RemoteResult(result);
    }

}
