package com.uino.web.cmdb.mvc;

import java.util.ArrayList;
import java.util.List;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import com.uino.init.api.ApiResult;
import com.uino.init.api.ApiVersion;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uino.bean.cmdb.base.dataset.DataSetMallApiRelationRuleExternal;
import com.uino.bean.cmdb.base.dataset.relation.RelationRuleAttrCdtExternal;
import com.uino.bean.cmdb.base.dataset.relation.RelationRuleLineExternal;
import com.uino.bean.cmdb.base.dataset.relation.RelationRuleNodeExternal;
import com.uino.bean.cmdb.business.dataset.FriendInfo;
import com.uino.bean.cmdb.business.dataset.UpDownAttrCdt;
import com.uino.api.client.cmdb.IGraphAnalysisApiSvc;
import com.uino.web.BaseMvc;

/**
 * <AUTHOR>
 * @data 2019/8/7 11:34.
 */
@ApiVersion(1)
@Api(value = "查询数据", tags = {"数据集"})
@RestController
@RequestMapping(value = "/cmdb/graphAnaly")
public class GraphAnalysisMvc extends BaseMvc {

    @Autowired
    private IGraphAnalysisApiSvc graphAnalysisApiSvc;

    @ApiOperation("通过CiIds查询上下多少层")
    @PostMapping("queryCiUpDownByCiIds")
    @ModDesc(desc = "查询上下多少层", pDesc = "条件", pType = JSONObject.class, rDesc = "上下多少层关系数据", rType = FriendInfo.class)
    public ApiResult<FriendInfo> queryCiUpDownByCiIds(HttpServletRequest request, HttpServletResponse response, @RequestBody JSONObject body) {
        Long domainId = null;
        if (body.containsKey("domainId")) {
            domainId = body.getLong("domainId");
        }
        List<Long> startCiIds;
        if (body.containsKey("startCiIds")) {
            startCiIds = JSONArray.parseArray(body.getString("startCiIds"), Long.class);

        } else {
            throw new RuntimeException("参数错误");
        }
        List<UpDownAttrCdt> ciConditions = null;
        if (body.containsKey("ciConditions")) {
            ciConditions = body.getJSONArray("ciConditions").toJavaList(UpDownAttrCdt.class);
        }
        List<UpDownAttrCdt> rltConditions = null;
        if (body.containsKey("rltConditions")) {
            rltConditions = body.getJSONArray("rltConditions").toJavaList(UpDownAttrCdt.class);
        }
        List<Long> rltLvls;
        if (body.containsKey("rltLvls")) {
            rltLvls = JSONArray.parseArray(body.getString("rltLvls"), Long.class);
        } else {
            throw new RuntimeException("参数错误");
        }
        Integer upLevel;
        if (body.containsKey("upLevel")) {
            upLevel = body.getInteger("upLevel");
        } else {
            throw new RuntimeException("参数错误");
        }
        Integer downLevel;
        if (body.containsKey("downLevel")) {
            downLevel = body.getInteger("downLevel");
        } else {
            throw new RuntimeException("参数错误");
        }
        Boolean hasAttr = false;
        if (body.containsKey("hasAttr")) {
            hasAttr = body.getBoolean("hasAttr");
        }
        FriendInfo friendInfo = graphAnalysisApiSvc.queryCiUpDownByCiIds(domainId, startCiIds, ciConditions, rltConditions, rltLvls, upLevel, downLevel, hasAttr);
		return ApiResult.ok(this).data(friendInfo);
    }

    @ApiOperation("通过CiId查询上下多少层")
    @PostMapping("queryCiUpDownByCiId")
    @ModDesc(desc = "查询上下多少层", pDesc = "条件", pType = JSONObject.class, rDesc = "上下多少层关系数据", rType = FriendInfo.class)
    public ApiResult<FriendInfo> queryCiUpDownByCiId(HttpServletRequest request, HttpServletResponse response, @RequestBody JSONObject body) {
        Long domainId = null;
        if (body.containsKey("domainId")) {
            domainId = body.getLong("domainId");
        }
        Long startCiId;
        if (body.containsKey("startCiId")) {
            startCiId = body.getLong("startCiId");

        } else {
            throw new RuntimeException("参数错误");
        }
        List<UpDownAttrCdt> ciConditions = null;
        if (body.containsKey("ciConditions")) {
            ciConditions = body.getJSONArray("ciConditions").toJavaList(UpDownAttrCdt.class);
        }
        List<UpDownAttrCdt> rltConditions = null;
        if (body.containsKey("rltConditions")) {
            rltConditions = body.getJSONArray("rltConditions").toJavaList(UpDownAttrCdt.class);
        }
        List<Long> rltLvls;
        if (body.containsKey("rltLvls")) {
            rltLvls = JSONArray.parseArray(body.getString("rltLvls"), Long.class);
        } else {
            throw new RuntimeException("参数错误");
        }
        Integer upLevel;
        if (body.containsKey("upLevel")) {
            upLevel = body.getInteger("upLevel");
        } else {
            throw new RuntimeException("参数错误");
        }
        Integer downLevel;
        if (body.containsKey("downLevel")) {
            downLevel = body.getInteger("downLevel");
        } else {
            throw new RuntimeException("参数错误");
        }
        Boolean hasAttr = false;
        if (body.containsKey("hasAttr")) {
            hasAttr = body.getBoolean("hasAttr");
        }
        FriendInfo friendInfo = graphAnalysisApiSvc.queryCiUpDownByCiId(domainId, startCiId, ciConditions, rltConditions, rltLvls, upLevel, downLevel, hasAttr);

		return ApiResult.ok(this).data(friendInfo);
    }

    @ApiOperation("通过关系遍历规则查询数据")
    @PostMapping("queryFriendByCiUsingRule")
    @ModDesc(desc = "通过关系遍历规则查询数据", pDesc = "条件", pType = JSONObject.class, rDesc = "通过关系遍历规则查询数据", rType = FriendInfo.class)
    public ApiResult<FriendInfo> queryFriendByCiUsingRule(HttpServletRequest request, HttpServletResponse response, @RequestBody JSONObject body) {
        List<String> enter = new ArrayList<String>();
        if (body.containsKey("enter") && body.getJSONArray("enter")!=null && body.getJSONArray("enter").size()>0) {
            JSONArray arr = body.getJSONArray("enter");
            for (int i=0;i<arr.size();i++) {
            	enter.add(arr.getString(i));
            }
        } else {
            throw new RuntimeException("参数错误");
        }
        
        DataSetMallApiRelationRuleExternal rule = new DataSetMallApiRelationRuleExternal();
        if (body.containsKey("rule") && body.getJSONObject("rule")!=null) {
        	JSONObject jsObj = body.getJSONObject("rule");
        	if (jsObj.containsKey("pageNodeId") && jsObj.getLong("pageNodeId")!=null) {
        		rule.setPageNodeId(jsObj.getLong("pageNodeId"));
        	} else {
        		throw new RuntimeException("参数错误");
        	}
        	List<RelationRuleNodeExternal> nodes = new ArrayList<RelationRuleNodeExternal>();
        	if (jsObj.containsKey("nodes") && jsObj.getJSONArray("nodes")!=null && jsObj.getJSONArray("nodes").size()>0) {
        		JSONArray nodeArr = jsObj.getJSONArray("nodes");
        		for (int i=0;i<nodeArr.size();i++) {
        			JSONObject nodeObj = nodeArr.getJSONObject(i);
        			RelationRuleNodeExternal node = new RelationRuleNodeExternal();
        			if (nodeObj.containsKey("className") && nodeObj.getString("className")!=null && !"".equals(nodeObj.getString("className").trim())) {
        				node.setClassName(nodeObj.getString("className").trim());
        			} else {
        				throw new RuntimeException("参数错误");
        			}
        			if (nodeObj.containsKey("pageNodeId") && nodeObj.getLong("pageNodeId")!=null) {
        				node.setPageNodeId(nodeObj.getLong("pageNodeId"));
        			} else {
        				throw new RuntimeException("参数错误");
        			}
        			List<RelationRuleAttrCdtExternal> cdts = new ArrayList<RelationRuleAttrCdtExternal>();
        			if (nodeObj.containsKey("cdts") && nodeObj.getJSONArray("cdts")!=null) {
        				JSONArray cdtArr = nodeObj.getJSONArray("cdts");
        				for (int j=0;j<cdtArr.size();j++) {
        					JSONObject cdtObj = cdtArr.getJSONObject(j);
        					RelationRuleAttrCdtExternal cdt = new RelationRuleAttrCdtExternal();
        					if (cdtObj.containsKey("attrName") && cdtObj.getString("attrName")!=null && !"".equals(cdtObj.getString("attrName").trim())) {
        						cdt.setAttrName(cdtObj.getString("attrName").trim());
        					} else {
        						throw new RuntimeException("参数错误");
        					}
        					if (cdtObj.containsKey("op") && cdtObj.getString("op")!=null && !"".equals(cdtObj.getString("op").trim())) {
        						cdt.setOp(cdtObj.getString("op").trim());
        					} else {
        						throw new RuntimeException("参数错误");
        					}
        					if (cdtObj.containsKey("value") && cdtObj.getString("value")!=null && !"".equals(cdtObj.getString("value").trim())) {
        						cdt.setValue(cdtObj.getString("value").trim());
        					} else {
        						throw new RuntimeException("参数错误");
        					}
        					cdts.add(cdt);
        				}
        			}
        			node.setCdts(cdts);
        			nodes.add(node);
        		}
        	} else {
        		throw new RuntimeException("参数错误");
        	}
        	rule.setNodes(nodes);
        	List<RelationRuleLineExternal> lines = new ArrayList<RelationRuleLineExternal>();
        	if (jsObj.containsKey("lines") && jsObj.getJSONArray("lines")!=null) {
        		JSONArray lineArr = jsObj.getJSONArray("lines");
        		for (int i=0;i<lineArr.size();i++) {
        			JSONObject lineObj = lineArr.getJSONObject(i);
        			RelationRuleLineExternal line = new RelationRuleLineExternal();
        			if (lineObj.containsKey("className") && lineObj.getString("className")!=null && !"".equals(lineObj.getString("className").trim())) {
        				line.setClassName(lineObj.getString("className").trim());
        			} else {
        				throw new RuntimeException("参数错误");
        			}
        			if (lineObj.containsKey("nodeStartId") && lineObj.getLong("nodeStartId")!=null) {
        				line.setNodeStartId(lineObj.getLong("nodeStartId"));
        			} else {
        				throw new RuntimeException("参数错误");
        			}
        			if (lineObj.containsKey("nodeEndId") && lineObj.getLong("nodeEndId")!=null) {
        				line.setNodeEndId(lineObj.getLong("nodeEndId"));
        			} else {
        				throw new RuntimeException("参数错误");
        			}
        			if (lineObj.containsKey("direction") && lineObj.getBoolean("direction")!=null) {
        				line.setDirection(lineObj.getBoolean("direction"));
        			} else {
        				throw new RuntimeException("参数错误");
        			}
        			List<RelationRuleAttrCdtExternal> cdts = new ArrayList<RelationRuleAttrCdtExternal>();
        			if (lineObj.containsKey("cdts") && lineObj.getJSONArray("cdts")!=null) {
        				JSONArray cdtArr = lineObj.getJSONArray("cdts");
        				for (int j=0;j<cdtArr.size();j++) {
        					JSONObject cdtObj = cdtArr.getJSONObject(j);
        					RelationRuleAttrCdtExternal cdt = new RelationRuleAttrCdtExternal();
        					if (cdtObj.containsKey("attrName") && cdtObj.getString("attrName")!=null && !"".equals(cdtObj.getString("attrName").trim())) {
        						cdt.setAttrName(cdtObj.getString("attrName").trim());
        					} else {
        						throw new RuntimeException("参数错误");
        					}
        					if (cdtObj.containsKey("op") && cdtObj.getString("op")!=null && !"".equals(cdtObj.getString("op").trim())) {
        						cdt.setOp(cdtObj.getString("op").trim());
        					} else {
        						throw new RuntimeException("参数错误");
        					}
        					if (cdtObj.containsKey("value") && cdtObj.getString("value")!=null && !"".equals(cdtObj.getString("value").trim())) {
        						cdt.setValue(cdtObj.getString("value").trim());
        					} else {
        						throw new RuntimeException("参数错误");
        					}
        					cdts.add(cdt);
        				}
        			}
        			line.setCdts(cdts);
        			if (lineObj.containsKey("lineOp") && lineObj.getString("lineOp")!=null && !"".equals(lineObj.getString("lineOp").trim())) {
        				line.setLineOp(lineObj.getString("lineOp").trim());
        			} else {
        				throw new RuntimeException("参数错误");
        			}
        			if (lineObj.containsKey("lineOpValue") && lineObj.getString("lineOpValue")!=null && !"".equals(lineObj.getString("lineOpValue").trim())) {
        				line.setLineOpValue(lineObj.getString("lineOpValue").trim());
        			} else {
        				throw new RuntimeException("参数错误");
        			}
        			lines.add(line);
        		}
        	}
        	rule.setLines(lines);
        } else {
            throw new RuntimeException("参数错误");
        }
        
        FriendInfo friendInfo = graphAnalysisApiSvc.queryFriendByCiUsingRule(enter, rule);
		return ApiResult.ok(this).data(friendInfo);
    }

}
