FROM corvis/java8:latest
MAINTAINER uino
USER root
ADD application-local.properties /usr/local/
ADD EAM-deploy.tar /usr/local/
ADD docker_start.sh /usr/local/
RUN rm -rf /etc/localtime && ln -s /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
RUN localedef -c -f UTF-8 -i zh_CN zh_CN.utf8
ENV LC_ALL zh_CN.utf8
RUN rm -f /usr/local/EAM/conf/application-local.properties \
	&& mv /usr/local/docker_start.sh /usr/local/EAM/bin/ \
	&& mv /usr/local/application-local.properties /usr/local/EAM/conf/ \
	&& chmod -R 777 /usr/local/EAM/*

CMD /usr/local/EAM/bin/docker_start.sh