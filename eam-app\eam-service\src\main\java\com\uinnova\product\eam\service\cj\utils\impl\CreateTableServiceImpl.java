package com.uinnova.product.eam.service.cj.utils.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.binary.core.util.BinaryUtils;
import com.uinnova.product.eam.model.cj.domain.ContextModule;
import com.uinnova.product.eam.model.cj.domain.PlanDesignInstance;
import com.uinnova.product.eam.model.cj.domain.PlanTemplateChapterData;
import com.uinnova.product.eam.model.cj.enums.TableContentTypeEnum;
import com.uinnova.product.eam.model.cj.vo.PlanChapterVO;
import com.uinnova.product.eam.model.cj.vo.RowTableContentVo;
import com.uinnova.product.eam.service.ICISwitchSvc;
import com.uinnova.product.eam.service.cj.dao.PlanDesignInstanceDao;
import com.uinnova.product.eam.service.cj.utils.CreateTableService;
import com.uinnova.product.vmdb.comm.model.ci.CCcCi;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CiGroupPage;
import com.uino.bean.cmdb.base.ESCIAttrDefInfo;
import com.uino.bean.cmdb.base.ESCIClassInfo;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.base.LibType;
import com.uino.bean.cmdb.query.ESCISearchBean;
import com.uino.service.cmdb.microservice.impl.CIClassSvc;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.apache.poi.xwpf.usermodel.XWPFTableCell;
import org.apache.poi.xwpf.usermodel.XWPFTableRow;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTc;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTcPr;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STJc;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STVerticalJc;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 方案创建表格服务实现类
 * 用于处理不同类型表格的生成和数据填充
 */
@Slf4j
@Service
public class CreateTableServiceImpl implements CreateTableService {

    @Resource
    private ICISwitchSvc ciSwitchSvc;
    @Resource
    private PlanDesignInstanceDao planDesignInstanceDao;
    @Resource
    private CIClassSvc ciClassSvc;

    // A4纸页面可用宽度（twips单位，16厘米 = 9072 twips）
    private static final int PAGE_AVAILABLE_WIDTH = 9072;
    // 表格单元格表头背景色
    private static final String HEADER_CELL_COLOR = "D9D9D9";
    // 默认首列宽度（像素）
    private static final int DEFAULT_FIRST_COLUMN_WIDTH = 120;
    // 默认列宽（像素）
    private static final int DEFAULT_COLUMN_WIDTH = 120;
    // twips到像素的转换比例（1 twip ≈ 1/15 像素）
    private static final int TWIPS_TO_PIXELS_RATIO = 15;
    // CI类型常量
    private static final int CI_TYPE = 11;
    // 日期类型常量
    private static final int DATE_TYPE = 9;
    // 资产绑定类型常量
    private static final int ASSET_BINDING_TYPE = 10;

    /**
     * 设置单元格样式为居中对齐并设置背景色
     *
     * @param cell 表格单元格
     * @param isHeader 是否为表头单元格
     */
    private void setCellStyle(XWPFTableCell cell, boolean isHeader) {
        CTTc cttc = cell.getCTTc();
        CTTcPr ctPr = cttc.addNewTcPr();
        ctPr.addNewVAlign().setVal(STVerticalJc.CENTER);
        cttc.getPList().get(0).addNewPPr().addNewJc().setVal(STJc.CENTER);

        if (isHeader) {
            cell.setColor(HEADER_CELL_COLOR);
        }
    }

    /**
     * 从JSON数据中解析表格数据
     *
     * @param context 上下文模块
     * @return 解析后的JSON数组，如果解析失败或数据为空则返回null
     */
    private JSONArray parseTableData(ContextModule context) {
        if (context == null || StringUtils.isEmpty(context.getData())) {
            return null;
        }

        try {
            JSONObject jsonObject = JSONObject.parseObject(context.getData());
            String data = jsonObject.getString("data");
            if (StringUtils.isEmpty(data)) {
                return null;
            }

            JSONArray jsonArray = JSONObject.parseArray(data);
            return CollectionUtils.isEmpty(jsonArray) ? null : jsonArray;
        } catch (Exception e) {
            log.error("解析表格数据异常: {}", e.getMessage());
            return null;
        }
    }


    @Override
    public void getMatrixTable(PlanTemplateChapterData definition, XWPFTableRow comTableRowOne,
                                ContextModule context, XWPFTable ComTable) {
        List<RowTableContentVo> matrixRowList = definition.getMatrixRow();
        List<RowTableContentVo> matrixColList = definition.getMatrixCol();

        // 获取首列宽
        int firstColumnWidth = getFirstColumnWidth(definition);

        // 初始化矩阵列宽
        initColumnWidth(matrixRowList, firstColumnWidth);

        // 计算总宽度
        int totalColumnWidth = calculateCommonTotalColumnWidth(matrixRowList) + firstColumnWidth;

        // 处理表头行
        if (!CollectionUtils.isEmpty(matrixRowList)) {
            for (int i = 0; i < matrixRowList.size() + 1; i++) {
                if (i < matrixRowList.size()) {
                    comTableRowOne.addNewTableCell().setText(matrixRowList.get(i).getName());
                }

                // 设置单元格样式
                XWPFTableCell cell = comTableRowOne.getTableCells().get(i);
                setCellStyle(cell, true);
                if (i == 0) {
                    // 计算首列宽度占比
                    double firstColumnRatio = (double) firstColumnWidth / totalColumnWidth;

                    // 设置首列宽度
                    setColumnWidth(ComTable, 0, firstColumnRatio, PAGE_AVAILABLE_WIDTH);
                } else {
                    // 设置列宽
                    RowTableContentVo contentVo = matrixRowList.get(i-1);
                    Integer columnWidth = contentVo != null ? contentVo.getColumnWidthValue() : null;

                    // 计算宽度比例
                    double widthRatio;
                    if (columnWidth != null && columnWidth > 0) {
                        widthRatio = (double) columnWidth / totalColumnWidth;
                    } else {
                        // 如果没有设置宽度，使用默认比例
                        widthRatio = 1.0 / totalColumnWidth;
                    }
                    // 设置列宽
                    setColumnWidth(ComTable, i, widthRatio, PAGE_AVAILABLE_WIDTH);
                }
            }
        }

        // 处理表头列
        if (!CollectionUtils.isEmpty(matrixColList)) {
            for (int i = 0; i < matrixColList.size(); i++) {
                XWPFTableRow comTableRowTwo = ComTable.createRow();
                comTableRowTwo.getCell(0).setText(matrixColList.get(i).getName());

                // 设置单元格样式
                setCellStyle(comTableRowTwo.getCell(0), true);

            }
        }

        // 填充表格数据
        JSONArray jsonArray = parseTableData(context);
        if (jsonArray != null && !CollectionUtils.isEmpty(matrixRowList)) {
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject tableData = JSONObject.parseObject(jsonArray.get(i).toString());
                XWPFTableRow comTableRowTwo = ComTable.getRow(i + 1);

                for (int j = 0; j < matrixRowList.size(); j++) {
                    String content = tableData.getString(matrixRowList.get(j).getName());

                    if (Objects.equals(matrixRowList.get(j).getType(), TableContentTypeEnum.ENUM.getType())) {
                        exportTableEnum(matrixRowList, j, comTableRowTwo, content, definition.getDataTableForm());
                    } else {
                        comTableRowTwo.getCell(j + 1).setText(content != null ? content : "");
                    }

                    // 设置单元格样式
                    setCellStyle(comTableRowTwo.getCell(j + 1), false);
                }
            }
        }
    }

    /**
     * 获取CI信息映射
     *
     * @param ciCodes CI代码集合
     * @return CI信息映射
     */
    private Map<String, ESCIInfo> getCiInfoMap(Set<String> ciCodes) {
        if (CollectionUtils.isEmpty(ciCodes)) {
            return Collections.emptyMap();
        }

        List<ESCIInfo> ciList = ciSwitchSvc.getCiByCodes(new ArrayList<>(ciCodes), null, LibType.DESIGN);
        if (CollectionUtils.isEmpty(ciList)) {
            return Collections.emptyMap();
        }

        return ciList.stream()
                .collect(Collectors.toMap(ESCIInfo::getCiCode, Function.identity(), (v1, v2) -> v1));
    }

    /**
     * 收集表格中的CI代码
     *
     * @param jsonArray 表格数据JSON数组
     * @param dataTableContent 表格内容定义
     * @return CI代码集合
     */
    private Set<String> collectCiCodes(JSONArray jsonArray, List<RowTableContentVo> dataTableContent) {
        Set<String> ciCodeSet = new HashSet<>();

        if (CollectionUtils.isEmpty(jsonArray) || CollectionUtils.isEmpty(dataTableContent)) {
            return ciCodeSet;
        }

        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject tableData = JSONObject.parseObject(jsonArray.get(i).toString());
            for (RowTableContentVo contentVo : dataTableContent) {
                if (Objects.equals(contentVo.getType(), CI_TYPE)) {
                    String ciCode = tableData.getString(contentVo.getName());
                    if (StringUtils.isNotEmpty(ciCode)) {
                        ciCodeSet.add(ciCode);
                    }
                }
            }
        }

        return ciCodeSet;
    }

    /**
     * 处理日期类型数据
     *
     * @param dateJsonArray 日期JSON数组
     * @return 格式化后的日期字符串
     */
    private String formatDateArray(JSONArray dateJsonArray) {
        if (CollectionUtils.isEmpty(dateJsonArray)) {
            return "";
        }

        try {
            List<String> dateList = dateJsonArray.toJavaList(String.class);
            return StringUtils.join(dateList, " - ");
        } catch (Exception e) {
            log.error("格式化日期数组异常: {}", e.getMessage());
            return "";
        }
    }

    /**
     * 处理CI类型数据
     *
     * @param esCi CI信息
     * @param attrName 属性名
     * @return 属性值字符串
     */
    private String getCiAttributeValue(ESCIInfo esCi, String attrName) {
        if (esCi == null) {
            return "";
        }

        Map<String, Object> attrs = esCi.getAttrs();
        if (CollectionUtils.isEmpty(attrs) || attrs.get(attrName) == null) {
            return "";
        }

        return String.valueOf(attrs.get(attrName));
    }




    @Override
    public void getVerticalTable(PlanTemplateChapterData definition, XWPFTableRow comTableRowOne,
                                  ContextModule context, XWPFTable ComTable) {
        List<RowTableContentVo> dataTableContent = definition.getDataTableContent();
        if (CollectionUtils.isEmpty(dataTableContent)) {
            return;
        }

        // 获取首列宽度，默认为120像素
        Integer firstColumnWidth = getFirstColumnWidth(definition);

        // 计算总宽度
        int totalColumnWidth = calculateVerticalTotalColumnWidth(context) +
                              (firstColumnWidth == 0 ? DEFAULT_FIRST_COLUMN_WIDTH : firstColumnWidth);

        // 创建表头
        for (int i = 0; i < dataTableContent.size(); i++) {
            XWPFTableCell headerCell;
            if (i == 0) {
                headerCell = comTableRowOne.getCell(0);
                headerCell.setText(dataTableContent.get(i).getName());
            } else {
                XWPFTableRow row = ComTable.createRow();
                headerCell = row.getCell(0);
                headerCell.setText(dataTableContent.get(i).getName());
            }
            setCellStyle(headerCell, true);
        }

        // 解析表格数据
        JSONArray jsonArray = parseTableData(context);
        if (jsonArray == null) {
            // 如果没有数据，添加空单元格
            for (int i = 0; i < dataTableContent.size(); i++) {
                XWPFTableRow row = ComTable.getRow(i);
                XWPFTableCell cell = row.addNewTableCell();
                cell.setText("");
                setCellStyle(cell, false);
            }

            // 计算剩余宽度
            int remainingWidth = totalColumnWidth - firstColumnWidth;

            // 计算首列宽度占比
            double firstColumnRatio = (double) firstColumnWidth / totalColumnWidth;

            // 计算数据列宽度占比
            double dataColumnRatio = (double) remainingWidth / totalColumnWidth;

            // 设置首列宽度
            setColumnWidth(ComTable, 0, firstColumnRatio, PAGE_AVAILABLE_WIDTH);

            // 设置数据列宽度
            setColumnWidth(ComTable, 1, dataColumnRatio, PAGE_AVAILABLE_WIDTH);
            return;
        }

        // 收集CI代码并获取CI信息映射
        Set<String> ciCodeSet = collectCiCodes(jsonArray, dataTableContent);
        Map<String, ESCIInfo> esCiMap = getCiInfoMap(ciCodeSet);

        // 填充表格数据
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject tableData = JSONObject.parseObject(jsonArray.get(i).toString());

            for (int j = 0; j < dataTableContent.size(); j++) {
                XWPFTableRow row = ComTable.getRow(j);
                RowTableContentVo contentVo = dataTableContent.get(j);
                String content = tableData.getString(contentVo.getName());
                XWPFTableCell cell = row.addNewTableCell();

                if (StringUtils.isNotEmpty(content)) {
                    if (Objects.equals(contentVo.getType(), CI_TYPE)) {
                        // CI类型
                        ESCIInfo esCi = esCiMap.get(content);
                        cell.setText(getCiAttributeValue(esCi, contentVo.getName()));
                    } else if (Objects.equals(contentVo.getType(), DATE_TYPE)) {
                        // 日期类型
                        JSONArray dateJsonArray = tableData.getJSONArray(contentVo.getName());
                        cell.setText(formatDateArray(dateJsonArray));
                    } else if (Objects.equals(contentVo.getType(), TableContentTypeEnum.ENUM.getType())) {
                        // 枚举类型
                        exportTableEnum(dataTableContent, j, row, content, definition.getDataTableForm());
                    } else {
                        // 普通文本
                        cell.setText(content);
                    }
                } else {
                    cell.setText("");
                }

                // 设置单元格样式
                setCellStyle(cell, false);
            }
        }

        // 计算列宽比例
        int dataColumnCount = jsonArray.size();
        if (dataColumnCount > 0) {

            // 计算剩余宽度
            int remainingWidth = totalColumnWidth - firstColumnWidth;

            // 计算首列宽度占比
            double firstColumnRatio = (double) firstColumnWidth / totalColumnWidth;

            // 设置首列宽度
            setColumnWidth(ComTable, 0, firstColumnRatio, PAGE_AVAILABLE_WIDTH);

            // 计算每个数据列的宽度
            int dataColumnWidth = remainingWidth / dataColumnCount;

            // 计算每个数据列的宽度占比
            double dataColumnRatio = (double) dataColumnWidth / totalColumnWidth;

            // 设置每列的宽度
            for (int i = 0; i < dataColumnCount; i++) {
                setColumnWidth(ComTable, i + 1, dataColumnRatio, PAGE_AVAILABLE_WIDTH);
            }
        }
    }

    /**
     * 获取首列宽度
     *
     * @param definition 表格定义
     * @return 首列宽度（像素）
     */
    private int getFirstColumnWidth(PlanTemplateChapterData definition) {
        if (definition == null) {
            return DEFAULT_FIRST_COLUMN_WIDTH;
        }

        Integer firstColumnWidth = definition.getFirstColumnWidth();

        // 如果首列宽度未设置或为自定义模式(-1)，则使用自定义宽度值
        if (BinaryUtils.isEmpty(firstColumnWidth) || firstColumnWidth == -1) {
            return BinaryUtils.isEmpty(definition.getFirstColumnWidthValue()) ?
                   DEFAULT_FIRST_COLUMN_WIDTH : definition.getFirstColumnWidthValue();
        }

        // 如果首列宽度为自适应模式(0)
        if (firstColumnWidth == 0) {
            return 0;
        }

        // 使用指定的首列宽度
        return firstColumnWidth;
    }

    /**
     * 计算横向表格所有列的宽度总和
     *
     * @param dataTableContent 表格内容定义
     * @return 所有列的宽度总和
     */
    private int calculateCommonTotalColumnWidth(List<RowTableContentVo> dataTableContent) {
        if (CollectionUtils.isEmpty(dataTableContent)) {
            return 0;
        }

        int totalWidth = 0;
        for (RowTableContentVo contentVo : dataTableContent) {
            Integer columnWidth = contentVo.getColumnWidthValue();
            if (columnWidth != null && columnWidth > 0) {
                totalWidth += columnWidth;
            } else {
                // 如果某列没有设置宽度，使用默认列宽
                totalWidth += DEFAULT_COLUMN_WIDTH;
            }
        }

        // 确保总宽度至少为1
        return Math.max(totalWidth, 1);
    }

    /**
     * 计算纵向表格所有列的宽度总和
     *
     * @param context 表格内容定义
     * @return 所有列的宽度总和
     */
    private int calculateVerticalTotalColumnWidth(ContextModule context) {
        if (BinaryUtils.isEmpty(context) || BinaryUtils.isEmpty(context.getData())) {
            return 0;
        }

        JSONArray jsonArray = parseTableData(context);
        if (jsonArray == null) {
            return 0;
        }

        // 每列使用默认列宽
        return jsonArray.size() * DEFAULT_COLUMN_WIDTH;
    }



    /**
     * 设置表格列宽（按比例）
     *
     * @param table 表格对象
     * @param columnIndex 列索引
     * @param widthRatio 宽度比例（0-1之间的小数）
     * @param totalTableWidthInTwips 表格总宽度（twips单位）
     */
    private void setColumnWidth(XWPFTable table, int columnIndex, double widthRatio, int totalTableWidthInTwips) {
        if (widthRatio <= 0 || table == null) {
            return;
        }

        try {
            // 根据比例计算列宽（确保至少有最小宽度）
            int widthInTwips = Math.max((int)(totalTableWidthInTwips * widthRatio), 200);

            // 设置列宽
            setColumnWidthInTwips(table, columnIndex, widthInTwips);
        } catch (Exception e) {
            log.error("设置表格列宽异常: columnIndex={}, widthRatio={}, totalWidth={}, error={}",
                    columnIndex, widthRatio, totalTableWidthInTwips, e.getMessage());
        }
    }





    /**
     * 设置表格列宽（按twips值）
     *
     * @param table 表格对象
     * @param columnIndex 列索引
     * @param widthInTwips 宽度值（单位：twips）
     */
    private void setColumnWidthInTwips(XWPFTable table, int columnIndex, int widthInTwips) {
        if (table == null || columnIndex < 0 || widthInTwips <= 0) {
            return;
        }

        try {
            // 创建BigInteger对象
            java.math.BigInteger width = java.math.BigInteger.valueOf(widthInTwips);

            // 设置表格网格
            if (table.getCTTbl().getTblGrid() == null) {
                table.getCTTbl().addNewTblGrid();
            }

            // 确保有足够的列
            while (table.getCTTbl().getTblGrid().getGridColList().size() <= columnIndex) {
                table.getCTTbl().getTblGrid().addNewGridCol();
            }

            // 设置指定列的宽度
            table.getCTTbl().getTblGrid().getGridColArray(columnIndex).setW(width);

            // 设置单元格宽度
            for (XWPFTableRow row : table.getRows()) {
                if (row == null) {
                    continue;
                }

                if (row.getTableCells().size() > columnIndex) {
                    XWPFTableCell cell = row.getCell(columnIndex);
                    if (cell != null) {
                        // 设置单元格宽度属性
                        if (cell.getCTTc().getTcPr() == null) {
                            cell.getCTTc().addNewTcPr();
                        }
                        if (cell.getCTTc().getTcPr().getTcW() == null) {
                            cell.getCTTc().getTcPr().addNewTcW();
                        }

                        // 设置宽度值和类型
                        cell.getCTTc().getTcPr().getTcW().setW(width);
                        cell.getCTTc().getTcPr().getTcW().setType(
                            org.openxmlformats.schemas.wordprocessingml.x2006.main.STTblWidth.DXA);
                    }
                }
            }

            // 设置表格整体宽度属性（可选）
            if (table.getCTTbl().getTblPr() == null) {
                table.getCTTbl().addNewTblPr();
            }
            if (table.getCTTbl().getTblPr().getTblW() == null) {
                table.getCTTbl().getTblPr().addNewTblW();
            }

        } catch (Exception e) {
            log.error("设置表格列宽异常: columnIndex={}, widthInTwips={}, error={}",
                    columnIndex, widthInTwips, e.getMessage());
        }
    }

    @Override
    public void getHorizontalTable(PlanTemplateChapterData definition, XWPFTableRow comTableRowOne,
                                    ContextModule context, XWPFTable ComTable, PlanChapterVO planChapterVO) {
        List<RowTableContentVo> dataTableContent = definition.getDataTableContent();
        if (CollectionUtils.isEmpty(dataTableContent)) {
            return;
        }

        // 初始化列宽值
        initColumnWidth(dataTableContent, null);

        // 计算总宽度
        int totalColumnWidth = calculateCommonTotalColumnWidth(dataTableContent);

        // 创建表头
        for (int i = 0; i < dataTableContent.size(); i++) {
            XWPFTableCell headerCell;
            if (i == 0) {
                headerCell = comTableRowOne.getCell(0);
            } else {
                headerCell = comTableRowOne.addNewTableCell();
            }
            headerCell.setText(dataTableContent.get(i).getName());
            setCellStyle(headerCell, true);

            // 设置列宽
            RowTableContentVo contentVo = dataTableContent.get(i);
            Integer columnWidth = contentVo != null ? contentVo.getColumnWidthValue() : null;

            // 计算宽度比例
            double widthRatio;
            if (columnWidth != null && columnWidth > 0) {
                widthRatio = (double) columnWidth / totalColumnWidth;
            } else {
                // 如果没有设置宽度，使用默认比例
                widthRatio = 1.0 / totalColumnWidth;
            }

            // 设置列宽
            setColumnWidth(ComTable, i, widthRatio, PAGE_AVAILABLE_WIDTH);
        }

        // 解析表格数据
        JSONArray jsonArray = parseTableData(context);
        if (jsonArray == null) {
            // 如果没有数据，添加一行空数据
            XWPFTableRow emptyRow = ComTable.createRow();
            for (int i = 0; i < dataTableContent.size(); i++) {
                emptyRow.getCell(i).setText("");
                setCellStyle(emptyRow.getCell(i), false);
            }
            return;
        }

        // 收集CI代码并获取CI信息映射
        Set<String> ciCodeSet = collectCiCodes(jsonArray, dataTableContent);
        Map<String, ESCIInfo> esCiMap = getCiInfoMap(ciCodeSet);

        // 填充表格数据
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject tableData = JSONObject.parseObject(jsonArray.get(i).toString());
            XWPFTableRow dataRow = ComTable.createRow();

            for (int j = 0; j < dataTableContent.size(); j++) {
                RowTableContentVo contentVo = dataTableContent.get(j);
                String content = tableData.getString(contentVo.getName());
                XWPFTableCell cell = dataRow.getCell(j);

                if (StringUtils.isNotEmpty(content)) {
                    if (Objects.equals(contentVo.getType(), CI_TYPE)) {
                        // CI类型
                        ESCIInfo esCi = esCiMap.get(content);
                        cell.setText(getCiAttributeValue(esCi, contentVo.getName()));
                    } else if (Objects.equals(contentVo.getType(), DATE_TYPE)) {
                        // 日期类型
                        JSONArray dateJsonArray = tableData.getJSONArray(contentVo.getName());
                        cell.setText(formatDateArray(dateJsonArray));
                    } else if (Objects.equals(contentVo.getType(), TableContentTypeEnum.ENUM.getType())) {
                        // 枚举类型
                        exportTableEnum(dataTableContent, j, dataRow, content, definition.getDataTableForm());
                    } else {
                        // 普通文本
                        cell.setText(content);
                    }
                } else if (Objects.equals(contentVo.getType(), ASSET_BINDING_TYPE)) {
                    // 资产绑定类型
                    handleTableBindAsset(contentVo, planChapterVO.getPlanId(), content, j, dataRow);
                } else {
                    cell.setText("");
                }

                // 设置单元格样式
                setCellStyle(cell, false);
            }
        }
    }

    /**
     * 初始化列宽值
     *
     * @param dataTableContent 表格内容定义
     * @param firstColumnWidth 首列宽度，可为null
     */
    private void initColumnWidth(List<RowTableContentVo> dataTableContent, Integer firstColumnWidth) {
        if (CollectionUtils.isEmpty(dataTableContent)) {
            return;
        }

        // 自适应列数
        int selfAdaptionNum = 0;
        // 自定义列宽之和
        int customizeTotalWidthValue = 0;
        // 自适应列宽大小
        int selfAdaptionWidthValue = 0;

        // 统计自定义列宽和自适应列数
        for (RowTableContentVo content : dataTableContent) {
            if (!BinaryUtils.isEmpty(content.getColumnWidth()) && content.getColumnWidth() != 0) {
                // 处理自定义列宽
                if (content.getColumnWidth() == -1) {
                    // 自定义宽度模式，使用columnWidthValue
                    customizeTotalWidthValue += content.getColumnWidthValue();
                } else {
                    // 使用预设宽度
                    customizeTotalWidthValue += content.getColumnWidth();
                    content.setColumnWidthValue(content.getColumnWidth());
                }
            } else {
                // 自适应列宽
                selfAdaptionNum++;
            }
        }

        // 处理首列宽度
        if (firstColumnWidth != null) {
            if (firstColumnWidth == 0) {
                // 首列自适应
                selfAdaptionNum++;
            } else {
                // 首列固定宽度
                customizeTotalWidthValue += firstColumnWidth;
            }
        }

        // 计算自适应列宽
        if (selfAdaptionNum > 0) {
            // 页面可用宽度（像素）
            int pageWidthInPixels = PAGE_AVAILABLE_WIDTH / TWIPS_TO_PIXELS_RATIO;

            if (customizeTotalWidthValue > pageWidthInPixels) {
                // 自定义列宽总和已超过页面宽度，使用默认列宽
                selfAdaptionWidthValue = DEFAULT_COLUMN_WIDTH;
            } else {
                // 计算剩余宽度平均值
                selfAdaptionWidthValue = (pageWidthInPixels - customizeTotalWidthValue) / selfAdaptionNum;

                // 确保最小宽度
                selfAdaptionWidthValue = Math.max(selfAdaptionWidthValue, 50);
            }

            // 更新首列宽度（如果是自适应的）
            if (firstColumnWidth != null && firstColumnWidth == 0) {
                firstColumnWidth = selfAdaptionWidthValue;
            }
        }

        // 设置自适应列的宽度值
        for (RowTableContentVo content : dataTableContent) {
            if (content.getColumnWidth() == 0) {
                content.setColumnWidthValue(selfAdaptionWidthValue);
            }
        }
    }

    /**
     * 处理表格枚举类型数据
     *
     * @param dataTableContents 表格内容定义
     * @param index 当前处理的索引
     * @param tableRow 表格行
     * @param content 内容
     * @param dataTableForm 表格形式
     */
    private void exportTableEnum(List<RowTableContentVo> dataTableContents, int index, XWPFTableRow tableRow,
                                String content, Integer dataTableForm) {
        if (content == null) {
            return;
        }

        RowTableContentVo contentVo = dataTableContents.get(index);
        String data = content;

        // 处理多选枚举
        if (Boolean.TRUE.equals(contentVo.getMultiple())) {
            try {
                JSONArray jsonArray = JSON.parseArray(content);
                if (!CollectionUtils.isEmpty(jsonArray)) {
                    List<String> dataList = jsonArray.toJavaList(String.class);
                    data = String.join(", ", dataList);
                }
            } catch (Exception e) {
                log.error("解析表格多选枚举类型值异常: content={}, error={}", content, e.getMessage());
            }
        }

        // 根据表格形式设置单元格内容
        XWPFTableCell targetCell;
        switch (dataTableForm) {
            case 1:
                targetCell = tableRow.getCell(index);
                break;
            case 2:
                targetCell = tableRow.addNewTableCell();
                break;
            case 3:
                targetCell = tableRow.getCell(index + 1);
                break;
            default:
                log.warn("未知的表格形式: {}", dataTableForm);
                return;
        }

        targetCell.setText(data);
        setCellStyle(targetCell, false);
    }

    /**
     * 处理表格资产绑定
     *
     * @param dataTableContent 表格内容定义
     * @param planId 计划ID
     * @param value 值
     * @param cellIndex 单元格索引
     * @param tableRow 表格行
     */
    private void handleTableBindAsset(RowTableContentVo dataTableContent, Long planId, String value,
                                     int cellIndex, XWPFTableRow tableRow) {
        // 验证输入参数
        if (StringUtils.isNotBlank(value) || planId == null) {
            tableRow.getCell(cellIndex).setText("");
            return;
        }

        // 获取计划设计实例
        PlanDesignInstance planDesignInstance = planDesignInstanceDao.getById(planId);
        if (planDesignInstance == null || planDesignInstance.getDefaultSystemClassId() == null) {
            tableRow.getCell(cellIndex).setText("");
            return;
        }

        // 获取系统分类
        Long classId = planDesignInstance.getDefaultSystemClassId();
        ESCIClassInfo esciClassInfo = ciClassSvc.queryESClassInfoById(classId);
        if (esciClassInfo == null || CollectionUtils.isEmpty(esciClassInfo.getAttrDefs())) {
            tableRow.getCell(cellIndex).setText("");
            return;
        }

        // 获取属性定义列表
        List<ESCIAttrDefInfo> attrDefList = esciClassInfo.getAttrDefs();
        Map<Long, String> attrMap = attrDefList.stream()
                .collect(Collectors.toMap(ESCIAttrDefInfo::getId, ESCIAttrDefInfo::getProName));

        // 处理约束条件
        String val = "";
        if (StringUtils.isNotEmpty(dataTableContent.getCopyConstraint())) {
            try {
                JSONObject jsonObject = JSONObject.parseObject(dataTableContent.getCopyConstraint());
                if (jsonObject != null && StringUtils.isNotEmpty(jsonObject.getString("ids"))) {
                    Long attId = Long.valueOf(jsonObject.getString("ids"));
                    if (attrMap.containsKey(attId)) {
                        String preName = attrMap.get(attId);

                        // 查询CI信息
                        ESCISearchBean bean = new ESCISearchBean();
                        CCcCi cdt = new CCcCi();
                        cdt.setCiCode(planDesignInstance.getDefaultSystemCiCode());
                        bean.setCdt(cdt);
                        CiGroupPage ciGroupPage = ciSwitchSvc.queryPageBySearchBean(bean, false, LibType.DESIGN);

                        if (ciGroupPage != null && !CollectionUtils.isEmpty(ciGroupPage.getData())) {
                            CcCiInfo ci = ciGroupPage.getData().get(0);
                            if (ci != null && ci.getAttrs() != null) {
                                val = ci.getAttrs().getOrDefault(preName, "");
                            }
                        }
                    }
                }
            } catch (Exception e) {
                log.error("处理表格资产绑定异常: {}", e.getMessage());
            }
        }

        tableRow.getCell(cellIndex).setText(val);
    }
}
