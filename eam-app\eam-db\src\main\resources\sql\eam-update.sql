/*2021-09-30 Monet视图版本表添加关联发布信息*/
ALTER TABLE `db_vmdb`.`vc_diagram_version`
ADD COLUMN `RELEASE_ID` decimal(16, 0) NULL COMMENT '关联视图发布id' AFTER `MODIFY_TIME`;

ALTER TABLE `db_vmdb`.`vc_diagram_version`
ADD COLUMN `RELEASE_VERSION` decimal(16, 0) NULL COMMENT '已发布视图版本' AFTER `RELEASE_ID`;

/*2022-01-20 lucong 添加系统文件夹标识*/
ALTER TABLE `db_vmdb`.`VC_DIAGRAM_DIR` ADD COLUMN `SYS_DIR` tinyint(1) NULL COMMENT '是否为系统文件夹 0:否，1:是' AFTER `SYS_TYPE`;

/*2022-03-16 yangbingbing 修改字段类型*/
ALTER TABLE `db_vmdb`.`vc_diagram_dir` MODIFY COLUMN `ES_SYS_ID` varchar(100) NULL DEFAULT NULL COMMENT '关联es系统ciCode' AFTER `DIR_INIT`;

/*2022-03-17 changhu 修改数据库 vc_diagram_dir 字段 DIR_TYPE字段长度*/
ALTER TABLE `db_vmdb`.`vc_diagram_dir` MODIFY COLUMN `DIR_TYPE` decimal(16, 0) NULL DEFAULT NULL COMMENT '目录类型' AFTER `DIR_NAME`;

/*2022-04-25 zhangqiang 添加初始数据 vc_diagram_dir*/
INSERT INTO `db_vmdb`.`vc_diagram_dir` (`ID`, `DIR_NAME`, `DIR_TYPE`, `SUBJECT_ID`, `PARENT_ID`, `USER_ID`, `DIR_LVL`, `DIR_PATH`, `ORDER_NO`, `IS_LEAF`, `ICON`, `DIR_DESC`, `DOMAIN_ID`, `DATA_STATUS`, `CREATOR`, `MODIFIER`, `CREATE_TIME`, `MODIFY_TIME`, `OLD_PARENT_ID`, `DIR_INIT`, `ES_SYS_ID`, `SYS_TYPE`, `SYS_DIR`, `STATE`) VALUES (5, '技术架构设计空间', 111, 0, -111, 1, 1, '#5#', NULL, 1, NULL, NULL, 1, 1, 'admin', 'admin', 20220412153335, 20220412155707, NULL, 0, NULL, NULL, 0, 2);
INSERT INTO `db_vmdb`.`vc_diagram_dir` (`ID`, `DIR_NAME`, `DIR_TYPE`, `SUBJECT_ID`, `PARENT_ID`, `USER_ID`, `DIR_LVL`, `DIR_PATH`, `ORDER_NO`, `IS_LEAF`, `ICON`, `DIR_DESC`, `DOMAIN_ID`, `DATA_STATUS`, `CREATOR`, `MODIFIER`, `CREATE_TIME`, `MODIFY_TIME`, `OLD_PARENT_ID`, `DIR_INIT`, `ES_SYS_ID`, `SYS_TYPE`, `SYS_DIR`, `STATE`) VALUES (4, '数据架构设计空间', 101, 0, -101, 1, 1, '#4#', NULL, 1, NULL, NULL, 1, 1, 'admin', 'admin', 20220412153335, 20220412155707, NULL, 0, NULL, NULL, 0, 2);
INSERT INTO `db_vmdb`.`vc_diagram_dir` (`ID`, `DIR_NAME`, `DIR_TYPE`, `SUBJECT_ID`, `PARENT_ID`, `USER_ID`, `DIR_LVL`, `DIR_PATH`, `ORDER_NO`, `IS_LEAF`, `ICON`, `DIR_DESC`, `DOMAIN_ID`, `DATA_STATUS`, `CREATOR`, `MODIFIER`, `CREATE_TIME`, `MODIFY_TIME`, `OLD_PARENT_ID`, `DIR_INIT`, `ES_SYS_ID`, `SYS_TYPE`, `SYS_DIR`, `STATE`) VALUES (1, '业务组件建模空间', 100, 0, -100, 1, 1, '#1#', NULL, 1, NULL, NULL, 1, 1, 'admin', 'admin', 20220412153335, 20220412155707, NULL, 0, NULL, NULL, 0, 2);
INSERT INTO `db_vmdb`.`vc_diagram_dir` (`ID`, `DIR_NAME`, `DIR_TYPE`, `SUBJECT_ID`, `PARENT_ID`, `USER_ID`, `DIR_LVL`, `DIR_PATH`, `ORDER_NO`, `IS_LEAF`, `ICON`, `DIR_DESC`, `DOMAIN_ID`, `DATA_STATUS`, `CREATOR`, `MODIFIER`, `CREATE_TIME`, `MODIFY_TIME`, `OLD_PARENT_ID`, `DIR_INIT`, `ES_SYS_ID`, `SYS_TYPE`, `SYS_DIR`, `STATE`) VALUES (3, '应用架构设计空间', 11, 0, -11, 1, 1, '#3#', NULL, 1, NULL, NULL, 1, 1, 'admin', 'admin', 20220412153335, 20220412155707, NULL, 0, NULL, NULL, 0, 2);
INSERT INTO `db_vmdb`.`vc_diagram_dir` (`ID`, `DIR_NAME`, `DIR_TYPE`, `SUBJECT_ID`, `PARENT_ID`, `USER_ID`, `DIR_LVL`, `DIR_PATH`, `ORDER_NO`, `IS_LEAF`, `ICON`, `DIR_DESC`, `DOMAIN_ID`, `DATA_STATUS`, `CREATOR`, `MODIFIER`, `CREATE_TIME`, `MODIFY_TIME`, `OLD_PARENT_ID`, `DIR_INIT`, `ES_SYS_ID`, `SYS_TYPE`, `SYS_DIR`, `STATE`) VALUES (2, '其他业务架构设计空间', 1, 0, -1, 1, 1, '#2#', NULL, 1, NULL, NULL, 1, 1, 'admin', 'admin', 20220412153335, 20220412155707, NULL, 0, NULL, NULL, 0, 2);

/*2023-09-08 zhangqiang 新增字段autoCreat vc_diagram_version*/
ALTER TABLE `db_vmdb`.`vc_diagram_version` ADD COLUMN `AUTO_CREAT` decimal(16, 0) NULL COMMENT '是否为自动创建 是：1/null 否：2' AFTER `RELEASE_VERSION`;