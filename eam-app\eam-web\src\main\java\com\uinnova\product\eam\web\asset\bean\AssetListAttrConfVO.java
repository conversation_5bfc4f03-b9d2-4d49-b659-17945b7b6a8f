package com.uinnova.product.eam.web.asset.bean;

import com.alibaba.fastjson.JSONObject;
import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 应用广场配置资产信息VO
 *
 * <AUTHOR>
 */
@Data
public class AssetListAttrConfVO {

    @Comment("主键id")
    private Long id;
    @Comment("广场资产配置ID")
    @NotNull(message = "广场卡片ID不可为空")
    private Long appSquareConfId;
    @Comment("资产分类标识")
    @NotNull(message = "资产分类不可为空")
    private String classCode;
    @Comment("类型 1:卡片，2:表单")
    @NotNull(message = "类型不可为空")
    private Integer type;
    @Comment("展示字段")
    /**
     * 存放展示字段属性
     * [
     *  {}
     *  {}
     *  {}
     * ]
     */
    private List<JSONObject> showAttrs;
    /**
     * [
     * {
     *     colour:#aabbcc
     *     attr:{}
     * }
     * ]
     */
    @Comment("标签展示")
    private List<JSONObject> tagAttrs;

    @Comment("领域id")
    private Long domainId;
    @Comment("创建人")
    private String creator;
    @Comment("创建时间")
    private Long createTime;

    @Comment("定制字段-统计数据开关")
    private Boolean countButton;
}
