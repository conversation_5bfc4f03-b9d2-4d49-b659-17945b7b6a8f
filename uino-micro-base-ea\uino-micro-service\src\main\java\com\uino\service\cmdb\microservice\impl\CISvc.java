package com.uino.service.cmdb.microservice.impl;

import com.alibaba.fastjson.TypeReference;
import com.binary.core.exception.BinaryException;
import com.binary.core.exception.MessageException;
import com.binary.core.i18n.LanguageResolver;
import com.binary.core.io.Compression;
import com.binary.core.util.BinaryUtils;
import com.binary.framework.exception.ServiceException;
import com.binary.jdbc.Page;
import com.binary.json.JSON;
import com.google.common.collect.Lists;
import com.uinnova.product.vmdb.comm.bean.CIState;
import com.uinnova.product.vmdb.comm.model.ci.CCcCi;
import com.uinnova.product.vmdb.comm.model.ci.CcCi;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.comm.util.CiExcelUtil;
import com.uinnova.product.vmdb.comm.util.CommUtil;
import com.uinnova.product.vmdb.provider.ci.bean.*;
import com.uinnova.product.vmdb.provider.search.bean.CcCiSearchPage;
import com.uino.bean.cmdb.base.*;
import com.uino.bean.cmdb.business.*;
import com.uino.bean.cmdb.enums.AttrNameKeyEnum;
import com.uino.bean.cmdb.query.ESAttrAggBean;
import com.uino.bean.cmdb.query.ESCISearchBean;
import com.uino.bean.permission.base.SysRole;
import com.uino.bean.permission.base.SysRoleDataModuleRlt;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.permission.business.CIExportLabel;
import com.uino.bean.permission.query.CSysRoleDataModuleRlt;
import com.uino.bean.sys.base.ESCIOperateLog;
import com.uino.dao.BaseConst;
import com.uino.dao.cmdb.*;
import com.uino.dao.sys.ESCIOperateLogSvc;
import com.uino.dao.util.ESUtil;
import com.uino.service.cmdb.microservice.ICISvc;
import com.uino.service.permission.microservice.IUserSvc;
import com.uino.service.permission.microservice.impl.RoleSvc;
import com.uino.service.sys.microservice.ICIOperateLogSvc;
import com.uino.service.sys.microservice.IDictionarySvc;
import com.uino.service.sys.microservice.IResourceSvc;
import com.uino.service.util.FileUtil;
import com.uino.util.excel.EasyExcelUtil;
import com.uino.util.excel.EasyExcelUtil.SheetData;
import com.uino.util.rsm.RsmUtils;
import com.uino.util.sys.CheckAttrUtil;
import com.uino.util.sys.LibTypeUtil;
import com.uino.util.sys.SysUtil;
import com.uino.util.sys.ValidDtoUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.sort.SortBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Primary;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.ResourceUtils;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;
import java.io.*;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.*;
import java.util.Map.Entry;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Primary
@Slf4j
@RefreshScope
public class CISvc implements ICISvc {

    @Autowired
    private ESCISvc esCiSvc;

    @Autowired
    private ESImageSvc esImageSvc;

    @Autowired
    private ESCIClassSvc esClsSvc;

    @Autowired
    private CIClassSvc clsSvc;

    @Autowired
    private CIRltSvc ciRltSvc;

    @Autowired
    @Lazy
    private ESCmdbCommSvc commSvc;

    @Autowired
    private IResourceSvc resourceSvc;

    @Autowired
    private ICIOperateLogSvc logSvc;

    @Autowired
    private ESCIHistorySvc esHistorySvc;

    @Autowired
    private ESCIAttrTransConfigSvc attrConfigSvc;

    @Autowired
    private IDictionarySvc dictSvc;

    @Autowired
    private ESCIAttrTransConfigSvc attrTransConfigSvc;

    @Autowired
    private ESCIHistorySvc ciHistorySvc;

    @Autowired
    private RoleSvc roleSvc;

    @Autowired
    private RsmUtils rsmUtils;

    @Resource
    IUserSvc userSvc;

    @Value("${is.show.3d.attribute:false}")
    private Boolean isShow3dAttribute;

    @Value("${http.resource.space:}")
    private String urlPath;

    @Value("${http.resource.sync:}")
    private String syncHttpPath;

    @Value("${local.resource.space:}")
    private String localPath;

    // private List<Map<String, Object>> saveMessage;
    @Value("${uino.base.ci.primarykey.maxcount:5}")
    private Integer primaryKeyCount;

    @Override
    public Long saveOrUpdate(CcCiInfo ciInfo) {
        return this.saveOrUpdateMethod(ciInfo, null);
    }

    @Override
    public Long saveOrUpdate(CcCiInfo ciInfo, SaveType saveType) {
        return this.saveOrUpdateMethod(ciInfo, saveType);
    }

    @Override
    public Long saveOrUpdateExtra(CcCiInfo ciInfo) {
        CcCi ci = ciInfo.getCi();
        if (ci.getDomainId() == null) {
            ci.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
        }
        Long classId = ciInfo.getCi().getClassId();
        String ciCode = ciInfo.getCi().getCiCode();
        Map<String, String> attrs = ciInfo.getAttrs();
        Assert.notNull(ci, "X_PARAM_NOT_NULL${name:ci}");
        Assert.notNull(classId, "X_PARAM_NOT_NULL${name:classId}");
        SysUser loginUser = null;
        try {
            loginUser = SysUtil.getCurrentUserInfo();
        } catch (Exception e) {
            log.error("获取登陆用户失败，所有值取默认值");
        }
        boolean isAdd = BinaryUtils.isEmpty(ci.getId());
        // 分类是否存在
        ESCIClassInfo ciClass = esClsSvc.getById(classId);
        if (ciClass == null) {
            throw MessageException.i18n("BS_MNAME_CLASS_NOT_EXSIT");
        }
        // 校验CI属性-当前及父类属性
        List<CcCiAttrDef> attrDefs = esClsSvc.getAllDefsByClassId(ci.getDomainId(), classId);

        // When the 3D model is opened, save the CI and add the path conversion
        this.removeModelIconPath(attrDefs, attrs);

        Map<String, Integer> checkResult = commSvc.validAttrs(attrDefs, attrs, true);
        if (!BinaryUtils.isEmpty(checkResult)) {
            for (String result : checkResult.keySet()) {
                throw new MessageException(result);
            }
        }

        Map<String, String> stdMap = toStdMap(attrs);
        // 校验数据字典类型
        Map<String, List<String>> dictValuesMap = this.getExterDictValues(ciInfo.getCi().getDomainId(), attrDefs);
        if (!BinaryUtils.isEmpty(dictValuesMap)) {
            for (Entry<String, List<String>> next : dictValuesMap.entrySet()) {
                String key = next.getKey();
                List<String> values = next.getValue();

                String val = stdMap.get(key);
                Assert.isTrue(!isAdd || BinaryUtils.isEmpty(val) || values.contains(val), "属性[" + key + "]引用值[" + val + "]不存在");
            }
        }
        String classCode = ciClass.getClassCode();
        // 校验重复
        List<String> ciPKAttrDefNames = CommUtil.getCiPKAttrDefNames(attrDefs);
        Integer hashCode = CommUtil.getCiMajorHashCodeExtra(attrs, ciPKAttrDefNames,classCode);
        List<String> ciPrimaryKeys = CommUtil.getCiPrimaryKeys(classCode, attrs, ciPKAttrDefNames);
        ciPrimaryKeys.add(0,classCode);
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.termQuery("domainId", ci.getDomainId()));
        boolQueryBuilder.filter(QueryBuilders.termQuery("hashCode", hashCode));
        List<CcCiInfo> liveCis = esCiSvc.getCIInfoPageByQuery(1, 3000, boolQueryBuilder, false).getData();
        for (CcCiInfo cInfo : liveCis) {
            if (compareCiPrimaryKeys(ciPrimaryKeys, JSON.toList(cInfo.getCi().getCiPrimaryKey(), String.class))) {
                // 同分类下ciCode或id相同，更新，否则删除
                // 有id时优先按id更新，兼容仅有ciCode时更新的情况
                boolean idEqual = ci.getId() != null && ci.getId().longValue() == cInfo.getCi().getId().longValue();
                boolean codeEqual = ciCode != null && ciCode.equals(cInfo.getCi().getCiCode());
                boolean classIdEqual = ci.getClassId().longValue() == cInfo.getCi().getClassId().longValue();
                boolean isUpdate = (codeEqual || idEqual) && classIdEqual;
                if (isUpdate) {
                    if (ci.getId() != null && ciCode != null) {
                        Assert.isTrue(codeEqual && idEqual, "BS_CI_NO_EXIST");
                    }
                    ci.setId(cInfo.getCi().getId());
                    isAdd = false;
                } else {
                    throw MessageException.i18n("BS_MNAME_RECORD_CONTAINS");
                }
            }
        }

        if (isAdd && !BinaryUtils.isEmpty(ciCode)) {
            BoolQueryBuilder codeQuery = QueryBuilders.boolQuery();
            codeQuery.must(QueryBuilders.termQuery("ciCode.keyword", ciCode));
            codeQuery.must(boolQueryBuilder.filter(QueryBuilders.termQuery("domainId", ci.getDomainId())));
            List<ESCIInfo> ciCodeQuery = esCiSvc.getListByQuery(codeQuery);
            if (!BinaryUtils.isEmpty(ciCodeQuery)) {
                ci.setId(ciCodeQuery.get(0).getId());
                isAdd = false;
            }
        }
        // 属性过滤，只保存定义过的属性
        Iterator<String> itAttr = stdMap.keySet().iterator();
        List<String> defNames = attrDefs.stream().map(CcCiAttrDef::getProStdName).collect(Collectors.toList());
        while (itAttr.hasNext()) {
            String attrName = itAttr.next();
            // 属性不包含在定义的属性，并且不是下划线开头结尾的字段，去掉
            if (!defNames.contains(attrName) && !attrName.matches("_([\\d\\D]*?)_")) {
                itAttr.remove();
            }
        }
        ciInfo.setAttrs(stdMap);
        Map<String, String> oldAttrs = null;
        // 组装CI
        if (isAdd) {
            long uuid = ESUtil.getUUID();
            ci.setId(uuid);
            ci.setCiCode(BinaryUtils.isEmpty(ciCode) ? String.valueOf(uuid) : ciCode);
            ci.setCreator(loginUser == null ? "system" : loginUser.getLoginCode());
            ci.setCreateTime(ESUtil.getNumberDateTime());
            ci.setCiVersion(String.valueOf(1));
        } else {
            CcCiInfo dbCIInfo = esCiSvc.getCiInfoById(ci.getId());
            Assert.notNull(dbCIInfo, "BS_CI_NO_EXIST");
            oldAttrs = dbCIInfo.getAttrs();
            // When the 3D model is opened, save the CI and add the path conversion
            this.removeModelIconPath(attrDefs, oldAttrs);
            ci.setCiCode(dbCIInfo.getCi().getCiCode());
            ci.setCreateTime(dbCIInfo.getCi().getCreateTime());
            ci.setCiVersion(dbCIInfo.getCi().getCiVersion());
            if (!CheckAttrUtil.checkAttrMapEqual(stdMap, oldAttrs)) {
                ci.setCiVersion(String.valueOf(Long.parseLong(dbCIInfo.getCi().getCiVersion()) + 1));
            }
        }
        ci.setClassId(classId);
        ci.setDataStatus(1);
        ci.setState(CIState.CREATE_COMPLETE.val());
        ci.setHashCode(hashCode);
        ci.setCiPrimaryKey(JSON.toString(ciPrimaryKeys));
        Long id = esCiSvc.saveOrUpdateCI(ciInfo);
        if (isAdd) {
            this.saveCIOperateLog(null, SysUtil.StaticUtil.LOG_DYNAMIC_INSERT, attrDefs, null, stdMap, ciClass.getClassName(), ci);
        } else {
            this.saveCIOperateLog(null, SysUtil.StaticUtil.LOG_DYNAMIC_UPDATE, attrDefs, oldAttrs, stdMap, ciClass.getClassName(), ci);
        }
        return id;
    }

    private Map<String,String> coverToAttrs(Map<String,Object> attrs){
        Map<String,String> newAttrs = new HashMap<>();
        if(BinaryUtils.isEmpty(attrs)){
            return newAttrs;
        }
        attrs.forEach((k,v) -> {
            if(!BinaryUtils.isEmpty(v)){
                newAttrs.put(k,String.valueOf(v));
            }
        });
        return newAttrs;
    }

    @Override
    public Map<String, SaveBatchCIContext> saveOrUpdateBatchCI(List<ESCIInfo> ciList, List<Long> classIds, String ownerCode, String loginCode) {
        Assert.notNull(ciList, "X_PARAM_NOT_NULL${name:ciList}");
        Assert.notNull(classIds, "X_PARAM_NOT_NULL${name:classIds}");
        Assert.notNull(ownerCode, "X_PARAM_NOT_NULL${name:ownerCode}");
        Assert.notNull(loginCode, "X_PARAM_NOT_NULL${name:loginCode}");
        Map<Long, ESCIClassInfo> classMap = esClsSvc.selectMapByQuery(1, classIds.size(), QueryBuilders.termsQuery("id", classIds));
        Map<String, SaveBatchCIContext> paramsContext = new HashMap<>();
        for (ESCIInfo item : ciList) {
            ESCIInfo ci = item;
            Assert.notNull(ci, "X_PARAM_NOT_NULL${name:ci}");
            Long classId = item.getClassId();
            Assert.notNull(classId, "X_PARAM_NOT_NULL${name:classId}");
            String ciCode = item.getCiCode();
            Assert.notNull(ciCode, "X_PARAM_NOT_NULL${name:ciCode}");
            SaveBatchCIContext context = paramsContext.computeIfAbsent(item.getCiCode(), k -> new SaveBatchCIContext(ciCode, classId, item));
            Map<String, Object> attrs = item.getAttrs();
            context.setId(ci.getId());
            context.setAttrsObj(attrs);
            context.setAttrsStr(coverToAttrs(attrs));
            boolean isAdd = BinaryUtils.isEmpty(context.getId()) ? true : false;
            String ciOwnerCode = BinaryUtils.isEmpty(ci.getOwnerCode()) ? ownerCode : ci.getOwnerCode();
            context.setOwnerCode(ciOwnerCode);
            context.setAdd(isAdd);
            ESCIClassInfo ciClass = classMap.get(classId);
            if (ciClass == null) {
                throw MessageException.i18n("BS_MNAME_CLASS_NOT_EXSIT");
            }
            context.setClassStdCode(ciClass.getClassStdCode());
            context.setClassName(ciClass.getClassName());
            // 校验CI属性-当前及父类属性
            List<CcCiAttrDef> attrDefs = esClsSvc.getAllDefsByClassId(SysUtil.getCurrentUserInfo().getDomainId(), classId);
            // When the 3D model is opened, save the CI and add the path conversion
            removeModelIconPathObject(attrDefs, attrs);
            Map<String, Integer> checkResult = commSvc.validAttrs(attrDefs, context.getAttrsStr(), true);
            if (!BinaryUtils.isEmpty(checkResult)) {
                for (String result : checkResult.keySet()) {
                    throw new MessageException(result);
                }
            }
            Map<String, String> stdMap = CheckAttrUtil.toStdMap(context.getAttrsStr());
            context.setStdMap(stdMap);
            context.setAttrDefs(attrDefs);
            // 校验重复
            List<String> ciPKAttrDefNames = CommUtil.getCiPKAttrDefNames(attrDefs);
            Integer hashCode = CommUtil.getCiMajorHashCode(context.getAttrsStr(), ciPKAttrDefNames, context.getClassStdCode());
            List<String> ciPrimaryKeys = CommUtil.getCiPrimaryKeys(context.getClassStdCode(), context.getAttrsStr(), ciPKAttrDefNames);
            context.setPrimaryKeys(ciPrimaryKeys);
            context.setHashCode(hashCode);
        }
        Map<Integer,List<String>> hashCodes = new HashMap<>(paramsContext.size());
        for (SaveBatchCIContext ctx : paramsContext.values()) {
            boolean contains = hashCodes.containsKey(ctx.getHashCode());
            List<String> ciCodes = hashCodes.computeIfAbsent(ctx.getHashCode(), k -> Lists.newArrayList(ctx.getCiCode()));
            if(contains){
                for (String ciCode : ciCodes) {
                    SaveBatchCIContext repeatCtx = paramsContext.get(ciCode);
                    if(compareCiPrimaryKeys(repeatCtx.getPrimaryKeys(), repeatCtx.getPrimaryKeys())){
                        throw MessageException.i18n("BS_MNAME_RECORD_CONTAINS");
                    }
                }
                ciCodes.add(ctx.getCiCode());
            }
        }
        List<CcCiInfo> liveCis = esCiSvc.getCIInfoPageByQuery(1, 3000, QueryBuilders.termsQuery("hashCode", hashCodes.keySet()), false).getData();
        for (CcCiInfo cInfo : liveCis) {
            List<String> ciCodes = hashCodes.get(cInfo.getCi().getHashCode());
            if(BinaryUtils.isEmpty(ciCodes)){
                continue;
            }
            for (String ciCode : ciCodes) {
                SaveBatchCIContext repeatCtx = paramsContext.get(ciCode);
                if(compareCiPrimaryKeys(repeatCtx.getPrimaryKeys(), JSON.toList(cInfo.getCi().getCiPrimaryKey(), String.class))){
                    boolean idEqual = repeatCtx.getId() != null && repeatCtx.getId().longValue() == cInfo.getCi().getId().longValue();
                    boolean isUpdate = idEqual;
                    if(isUpdate){
                        if (repeatCtx.getId() != null) {
                            Assert.isTrue(idEqual, "BS_CI_NO_EXIST");
                        }
                        repeatCtx.setId(cInfo.getCi().getId());
                        repeatCtx.setAdd(false);
                    }else {
                        throw MessageException.i18n("BS_MNAME_RECORD_CONTAINS");
                    }
                }
            }
            /*if(!ciCodes.contains(dbCiCode)){
                ciCodes.add(dbCiCode);
            }*/
        }
        List<SaveBatchCIContext> addContext = paramsContext.values().stream().filter(ctx -> ctx.isAdd()).collect(Collectors.toList());
        if(!BinaryUtils.isEmpty(addContext)){
            List<String> ciCodes = addContext.stream().map(SaveBatchCIContext::getCiCode).collect(Collectors.toList());
            List<ESCIInfo> ciCodeQuery = esCiSvc.getListByQuery(QueryBuilders.termsQuery("ciCode.keyword", ciCodes));
            if (!BinaryUtils.isEmpty(ciCodeQuery)) {
                for (ESCIInfo dbCi : ciCodeQuery) {
                    SaveBatchCIContext saveBatchCIContext = paramsContext.get(dbCi.getCiCode());
                    saveBatchCIContext.setId(dbCi.getId());
                    saveBatchCIContext.setAdd(false);
                }
            }
        }
        List<SaveBatchCIContext> updateContext = paramsContext.values().stream().filter(ctx -> !ctx.isAdd()).collect(Collectors.toList());
        Map<Long,ESCIInfo> ciDbMap = new HashMap<>();
        if(!BinaryUtils.isEmpty(updateContext)){
            List<Long> ciIds = updateContext.stream().map(SaveBatchCIContext::getId).collect(Collectors.toList());
            List<ESCIInfo> ciCodeQuery = esCiSvc.getListByQuery(QueryBuilders.termsQuery("id", ciIds));
            if (!BinaryUtils.isEmpty(ciCodeQuery)) {
                for (ESCIInfo esciInfo : ciCodeQuery) {
                    ciDbMap.put(esciInfo.getId(),esciInfo);
                }
            }
        }

        for (SaveBatchCIContext ctx : paramsContext.values()) {
            // 属性过滤，只保存定义过的属性
            Iterator<String> itAttr = ctx.getStdMap().keySet().iterator();
            List<String> defNames = ctx.getAttrDefs().stream().map(CcCiAttrDef::getProStdName).collect(Collectors.toList());
            while (itAttr.hasNext()) {
                String attrName = itAttr.next();
                // 属性不包含在定义的属性，并且不是下划线开头结尾的字段，去掉
                if (!defNames.contains(attrName) && !attrName.matches("_([\\d\\D]*?)_")) {
                    itAttr.remove();
                }
            }
            Map<String, Object> oldAttrsObj = null;
            // 组装CI
            if (ctx.isAdd()) {
                long uuid = ESUtil.getUUID();
                ctx.getEsCi().setId(uuid);
                ctx.getEsCi().setCiCode(ctx.getCiCode());
                ctx.getEsCi().setCreator(loginCode);
                ctx.getEsCi().setCreateTime(ESUtil.getNumberDateTime());
                ctx.getEsCi().setCiVersion(String.valueOf(1));
                ctx.getEsCi().setOwnerCode(ownerCode);
            } else {
                ESCIInfo dbCIInfo = ciDbMap.get(ctx.getId());
                Assert.notNull(dbCIInfo, "BS_CI_NO_EXIST");
                oldAttrsObj = dbCIInfo.getAttrs();
                ctx.getEsCi().setId(dbCIInfo.getId());
                ctx.getEsCi().setCiCode(dbCIInfo.getCiCode());
                ctx.getEsCi().setCreateTime(dbCIInfo.getCreateTime());
                ctx.getEsCi().setCiVersion(dbCIInfo.getCiVersion());
                ctx.setOldAttrs(coverToAttrs(oldAttrsObj));
                if (!CheckAttrUtil.checkAttrMapEqual(ctx.getStdMap(), ctx.getOldAttrs())) {
                    ctx.getEsCi().setCiVersion(String.valueOf(Long.parseLong(dbCIInfo.getCiVersion()) + 1));
                }
                ctx.getEsCi().setOwnerCode(dbCIInfo.getOwnerCode());
            }
            ctx.getEsCi().setClassId(ctx.getClassId());
            ctx.getEsCi().setDataStatus(1);
            ctx.getEsCi().setState(CIState.CREATE_COMPLETE.val());
            ctx.getEsCi().setHashCode(ctx.getHashCode());
            ctx.getEsCi().setCiPrimaryKey(JSON.toString(ctx.getPrimaryKeys()));
        }
        List<ESCIInfo> esCiList = paramsContext.values().stream().map(SaveBatchCIContext::getEsCi).collect(Collectors.toList());
        esCiSvc.saveOrUpdateCiList(esCiList);
        esCiSvc.transCIAttrs(esCiList,true);
        for (SaveBatchCIContext ctx : paramsContext.values()) {
            if (ctx.isAdd()) {
                this.saveCIOperateLog(null, SysUtil.StaticUtil.LOG_DYNAMIC_INSERT, ctx.getAttrDefs(), null, ctx.getStdMap(), ctx.getClassName(), ctx.getEsCi());
            } else {
                this.saveCIOperateLog(null, SysUtil.StaticUtil.LOG_DYNAMIC_UPDATE, ctx.getAttrDefs(), ctx.getOldAttrs(), ctx.getStdMap(), ctx.getClassName(), ctx.getEsCi());
            }
        }
        return paramsContext;
    }

    @Override
    public Map<String, ? extends SaveBatchCIContext> copyCiListByIds(List<ESCIInfo> ciList, String ownerCode) {
        return null;
    }

    @Override
    public ImportSheetMessage saveOrUpdateCiBath(Long domainId, CiClassSaveInfo saveInfo) {
        BinaryUtils.checkEmpty(saveInfo, "saveInfo");
        BinaryUtils.checkEmpty(saveInfo.getClassId(), "classId");
        BinaryUtils.checkEmpty(saveInfo.getClassStdCode(), "classStdCode");
        final String classStdCode = saveInfo.getClassStdCode();
        SysUser loginUser = null;
        try {
            loginUser = SysUtil.getCurrentUserInfo();
        } catch (Exception e) {
            log.error("获取登陆用户失败，所有值取默认值");
        }
        saveInfo.setOwnerCode(saveInfo.getOwnerCode() == null ? (loginUser == null ? "system" : loginUser.getLoginCode()) : saveInfo.getOwnerCode());
        ImportSheetMessage detailedResult = ImportSheetMessage.builder().build();
        List<ImportRowMessage> rowMessages = new ArrayList<>();
        // 统计信息
        int successCount = 0;
        int failCount = 0;
        int totals = 0;
        int ignores = 0;
        int inserts = 0;
        int updates = 0;
        List<String> sucessCIPKs = Lists.newArrayList();
        List<CcCiRecord> saveCiRecords = saveInfo.getRecords();
        // 保存CI
        if (!BinaryUtils.isEmpty(saveCiRecords)) {
            totals = saveCiRecords.size();
            Long classId = saveInfo.getClassId();
            Long sourceId = BinaryUtils.isEmpty(saveInfo.getSourceId()) ? 1L : saveInfo.getSourceId();
            String ownerCode = saveInfo.getOwnerCode();
            // 获取当前及父级分类属性
            List<CcCiAttrDef> attrDefs = esClsSvc.getAllDefsByClassId(domainId, classId);
            List<String> defNames = attrDefs.stream().map(CcCiAttrDef::getProStdName).collect(Collectors.toList());
            ESCIClassInfo classInfo = esClsSvc.getById(classId);
            // 查询要更新的数据
            Map<String, CcCiInfo> ciCodeMap = new HashMap<>();
            Map<Long, String> idToCode = new HashMap<>();
            Set<String> ciCodeSet = saveCiRecords.stream().map(CcCiRecord::getCiCode).filter(code -> !BinaryUtils.isEmpty(code)).collect(Collectors.toSet());
            Set<Long> ciIds = saveCiRecords.stream().map(CcCiRecord::getCiId).filter(id -> !BinaryUtils.isEmpty(id)).collect(Collectors.toSet());
            if (!(BinaryUtils.isEmpty(ciCodeSet) && BinaryUtils.isEmpty(ciIds))) {
                BoolQueryBuilder query = QueryBuilders.boolQuery();
                query.must(QueryBuilders.termQuery("domainId", domainId));
                if (!BinaryUtils.isEmpty(ciCodeSet)) {
                    query.should(QueryBuilders.termsQuery("ciCode.keyword", ciCodeSet));
                }
                if (!BinaryUtils.isEmpty(ciIds)) {
                    query.should(QueryBuilders.termsQuery("id", ciIds));
                }
                List<CcCiInfo> esInfos = esCiSvc.getCIInfoPageByQuery(1, saveCiRecords.size(), query, false).getData();
                if (!BinaryUtils.isEmpty(esInfos)) {
                    for (CcCiInfo esCiInfo : esInfos) {
                        ciCodeMap.put(esCiInfo.getCi().getCiCode(), esCiInfo);
                        idToCode.put(esCiInfo.getCi().getId(), esCiInfo.getCi().getCiCode());
                    }
                }
            }
            // 获取数据字典值
            Map<String, List<String>> dictValuesMap = this.getExterDictValues(domainId, attrDefs);

            List<CcCiInfo> ciInfosList = new ArrayList<>();
            List<Integer> hashCodes = new ArrayList<>();
            Map<String, Integer> keyToIndex = new HashMap<>();
            List<String> saveCodes = new ArrayList<>();
            List<String> savePrimaryKeys = new ArrayList<>();
            Iterator<CcCiRecord> it = saveCiRecords.iterator();
            Map<String, ESCIOperateLog> updateLogMap = new HashMap<>();
            recordLoop:
            while (it.hasNext()) {
                boolean isUpdate = false;
                CcCiRecord record = it.next();
                Map<String, String> attrs = record.getAttrs();
                // 校验属性
                Map<String, Integer> errMsg = commSvc.validAttrs(attrDefs, attrs, true);
                if (!BinaryUtils.isEmpty(errMsg)) {
                    List<ImportCellMessage> cellMessages = new ArrayList<>();
                    errMsg.forEach((msg, errType) -> {
                        ImportCellMessage cellMessage = new ImportCellMessage();
                        cellMessage.setErrorType(errType);
                        cellMessage.setErrorDesc(msg);
                        cellMessages.add(cellMessage);
                    });
                    ImportRowMessage rowMessage = new ImportRowMessage();
                    rowMessage.setRowNum(record.getIndex());
                    rowMessage.setMessageItems(cellMessages);
                    rowMessages.add(rowMessage);
                    failCount++;
                    it.remove();
                    continue;
                }
                Map<String, String> stdMap = toStdMap(attrs);
                // 校验数据字典类型
                if (!BinaryUtils.isEmpty(dictValuesMap)) {
                    Iterator<Entry<String, List<String>>> dictIt = dictValuesMap.entrySet().iterator();
                    while (dictIt.hasNext()) {
                        Entry<String, List<String>> next = dictIt.next();
                        String key = next.getKey();
                        List<String> values = next.getValue();
                        String val = stdMap.get(key);
                        if (record.getCiId() == null && !BinaryUtils.isEmpty(val) && !values.contains(val)) {
                            ImportRowMessage importRowMessage = this.buildRowMessage(record.getIndex(), CheckAttrUtil.FAILURE, "属性[" + key + "]引用值[" + val + "]不存在");
                            importRowMessage.setAttrs(attrs);
                            rowMessages.add(importRowMessage);
                            ignores++;
                            it.remove();
                            continue recordLoop;
                        }
                    }
                }
                String ciCode = record.getCiCode();
                // 兼容无ciCode时更新的情况
                if (BinaryUtils.isEmpty(ciCode) && record.getCiId() != null) {
                    ciCode = idToCode.get(record.getCiId());
                }
                // 校验ciCode，同一分类下，ciCode相同更新
                if (saveCodes.contains(ciCode)) {
                    ImportRowMessage importRowMessage = this.buildRowMessage(record.getIndex(), CheckAttrUtil.EXIST, "ciCode【" + ciCode + "】重复");
                    importRowMessage.setAttrs(attrs);
                    rowMessages.add(importRowMessage);
                    ignores++;
                    it.remove();
                    continue;
                } else if (ciCodeMap.containsKey(ciCode)) {
                    CcCiInfo ciInfo = ciCodeMap.get(ciCode);
                    if (ciInfo.getCi().getClassId().longValue() == classId.longValue()) {
                        record.setCiId(ciInfo.getCi().getId());
                        updates++;
                        isUpdate = true;
                    } else {
                        ImportRowMessage importRowMessage = this.buildRowMessage(record.getIndex(), CheckAttrUtil.EXIST, "ciCode 【" + ciCode + "】重复");
                        importRowMessage.setAttrs(attrs);
                        rowMessages.add(importRowMessage);
                        ignores++;
                        it.remove();
                        continue;
                    }
                }
                if (!BinaryUtils.isEmpty(ciCode)) {
                    saveCodes.add(ciCode);
                }
                List<String> ciPKAttrDefNames = CommUtil.getCiPKAttrDefNames(attrDefs);
                // 获取所属分类下的业务主键值的hashCode
                Integer hashCode;
                try {
                    hashCode = CommUtil.getCiMajorHashCode(attrs, ciPKAttrDefNames, classStdCode);
                } catch (Exception e) {
                    failCount++;
                    ImportRowMessage importRowMessage = this.buildRowMessage(record.getIndex(), CheckAttrUtil.FAILURE, "业务主键生成失败");
                    importRowMessage.setAttrs(attrs);
                    rowMessages.add(importRowMessage);
                    continue;
                }
                // 获取当前CI的业务主键值
                List<String> ciPrimaryKeys = CommUtil.getCiPrimaryKeys(classStdCode, attrs, ciPKAttrDefNames);
                String primaryKey = JSON.toString(ciPrimaryKeys);
                if (savePrimaryKeys.contains(primaryKey)) {
                    ignores++;
                    ImportRowMessage importRowMessage = this.buildRowMessage(record.getIndex(), CheckAttrUtil.EXIST, "业务主键【" + ciPrimaryKeys + "】重复");
                    importRowMessage.setAttrs(attrs);
                    rowMessages.add(importRowMessage);
                    continue;
                } else {
                    savePrimaryKeys.add(primaryKey);
                }
                CcCiInfo ciInfo = new CcCiInfo();
                // 属性过滤，只保存定义过的属性
                // 属性不包含在定义的属性，并且不是下划线开头结尾的字段，去掉
                stdMap.keySet().removeIf(attrName -> !defNames.contains(attrName) && !attrName.matches("_([\\d\\D]*?)_"));
                // CI 属性大小写转换
                ciInfo.setAttrs(stdMap);
                CcCi ci = new CcCi();
                if (isUpdate) {
                    CcCiInfo oldCi = ciCodeMap.get(ciCode);
                    ci.setCreateTime(oldCi.getCi().getCreateTime());
                    ci.setCiVersion(oldCi.getCi().getCiVersion());
                    // 属性合并
                    Map<String, String> combineAttrs = new HashMap<>();
                    combineAttrs.putAll(oldCi.getAttrs());
                    combineAttrs.putAll(stdMap);
                    ciInfo.setAttrs(combineAttrs);
                    if (!CheckAttrUtil.checkAttrMapEqual(combineAttrs, oldCi.getAttrs())) {
                        ci.setCiVersion(String.valueOf(Long.parseLong(oldCi.getCi().getCiVersion()) + 1));
                    }
                    ci.setOwnerCode(ownerCode);
                } else {
                    ci.setCreator(loginUser == null ? "system" : loginUser.getLoginCode());
                    ci.setCreateTime(ESUtil.getNumberDateTime());
                    ci.setOwnerCode(ci.getCreator());
                }
                ci.setId(record.getCiId() == null ? ESUtil.getUUID() : record.getCiId());
                ci.setCiCode(ciCode);
                ci.setClassId(classId);
                ci.setSourceId(sourceId);
                ci.setDataStatus(1);
                ci.setHashCode(hashCode);
                ci.setCiPrimaryKey(primaryKey);
                ci.setCreateTime(ESUtil.getNumberDateTime());
                ci.setDomainId(domainId == null ? BaseConst.DEFAULT_DOMAIN_ID : domainId);
                ciInfo.setCi(ci);
                ciInfosList.add(ciInfo);
                hashCodes.add(hashCode);
                keyToIndex.put(ci.getCiPrimaryKey(), record.getIndex());
                if (isUpdate) {
                    ESCIOperateLog ciLog = ESCIOperateLogSvc.buildLogRecord(sourceId,
                            SysUtil.StaticUtil.LOG_DYNAMIC_UPDATE, attrDefs, ciCodeMap.get(ciCode).getAttrs(), stdMap,
                            classInfo.getClassName(), ci);
                    updateLogMap.put(ciCode, ciLog);
                }
            }
            List<String> repeatKeys = new ArrayList<>();
            // 校验hashCode是否重复
            Map<String, Integer> res = this.checkRecordsByHashCode(domainId, ciInfosList, repeatKeys, updateLogMap);
            ignores += res.get("ignore") == null ? 0 : res.get("ignore");
            updates += res.get("update") == null ? 0 : res.get("update");
            if (!BinaryUtils.isEmpty(repeatKeys)) {
                for (String str : repeatKeys) {
                    rowMessages.add(this.buildRowMessage(keyToIndex.get(str), CheckAttrUtil.EXIST, "业务主键【" + str + "】重复"));
                }
            }
            // 保存CI操作日志
            List<ESCIOperateLog> ciLogs = new ArrayList<>(updateLogMap.values());
            for (ESCIOperateLog ciLog : ciLogs) {
                ciLog.setSourceId(sourceId);
                ciLog.setCiClassName(classInfo.getClassName());
                ciLog.setProNames(attrDefs.stream().map(CcCiAttrDef::getProStdName).collect(Collectors.toList()));
                ciLog.setOperator(loginUser == null ? "system" : loginUser.getLoginCode());
            }
            Set<String> keySet = updateLogMap.keySet();
            if (ciInfosList.size() > keySet.size()) {
                ciInfosList.forEach(ciInfo -> {
                    if (BinaryUtils.isEmpty(ciInfo.getCi().getCiCode())) {
                        ciInfo.getCi().setCiCode(String.valueOf(ciInfo.getCi().getId()));
                    }
                    String ciCode = ciInfo.getCi().getCiCode();
                    if (!keySet.contains(ciCode)) {
                        ciLogs.add(ESCIOperateLogSvc.buildLogRecord(sourceId, SysUtil.StaticUtil.LOG_DYNAMIC_INSERT,
                                attrDefs, null, ciInfo.getAttrs(), classInfo.getClassName(), ciInfo.getCi()));
                    }
                });
            }
            Map<String, Object> saveOrUpdateMsg = esCiSvc.saveOrUpdateCIBatch(ciInfosList, false);
            ciInfosList.forEach(ci -> {
                sucessCIPKs.add(ci.getCi().getCiPrimaryKey());
            });
            if (!BinaryUtils.isEmpty(saveOrUpdateMsg.get("failCount"))) {
                failCount += (Integer) saveOrUpdateMsg.get("failCount");
            }
            inserts = (totals - failCount - updates - ignores);
            successCount = inserts + updates;
            if (!BinaryUtils.isEmpty(saveOrUpdateMsg.get("errMessge"))) {
                String errStr = JSON.toString(saveOrUpdateMsg.get("errMessge"));
                // 入库失败
                rowMessages.add(this.buildRowMessage(1, CheckAttrUtil.FAILURE, "入库失败：" + errStr));
            }
            this.saveCIOperateLogBatch(ciLogs);
        }
        // 汇总整理结果
        detailedResult.setSuccessNum(successCount);
        detailedResult.setFailNum(failCount);
        detailedResult.setInsertNum(inserts);
        detailedResult.setUpdateNum(updates);
        detailedResult.setIgnoreNum(ignores);
        detailedResult.setTotalNum(totals);
        detailedResult.setSucessCIPks(sucessCIPKs);
        if (!BinaryUtils.isEmpty(rowMessages)) {
            rowMessages.sort(FileUtil.ExcelUtil.getRowMessageComparator(rowMessages));
            detailedResult.setRowMessages(rowMessages);
        }
        return detailedResult;
    }

    @Override
    public CcCiInfo getCiInfoById(Long id) {
        return esCiSvc.getCiInfoById(id);
    }

    @Override
    public CcCiInfo getCiInfoByCiCode(String ciCode,String ownerCode) {
        //运行库暂时不区分ownerCode
        return esCiSvc.getCiInfoByCiCode(ciCode);
    }

    @Override
    public CiGroupPage queryPageByIndex(Long domainId, Integer pageNum, Integer pageSize, CiQueryCdt cdt, Boolean hasClass) {
        domainId = domainId == null ? BaseConst.DEFAULT_DOMAIN_ID : domainId;
        return esCiSvc.queryPageByIndex(domainId, pageNum, pageSize, cdt, hasClass);
    }

    @Override
    public CiGroupPage queryPageBySearchBean(ESCISearchBean bean, Boolean hasClass) {
        if (bean.getDomainId() == null) {
            bean.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
        }
        CiGroupPage ciGroupPage = esCiSvc.queryPageBySearchBean(bean, hasClass);
        // The current 3D scene, open the 3D model path to add
        this.addModelIconPath(bean, ciGroupPage);
        return ciGroupPage;
    }

    @Override
    public List<CcCi> queryCiList(Long domainId, CCcCi cdt, String orders, Boolean isAsc) {
        domainId = domainId == null ? BaseConst.DEFAULT_DOMAIN_ID : domainId;
        List<CcCi> ciList = new ArrayList<>();
        if (cdt == null) {
            cdt = new CCcCi();
        }
        cdt.setDomainId(domainId);
        orders = BinaryUtils.isEmpty(orders) ? "modifyTime" : orders;
        List<CcCiInfo> ciInfos = esCiSvc.queryCiInfoList(cdt, orders, isAsc, false);
        for (CcCiInfo ciInfo : ciInfos) {
            ciList.add(ciInfo.getCi());
        }
        return ciList;
    }

    @Override
    public List<ESCIInfo> queryESCIInfoList(Long domainId, CCcCi cdt, String orders, Boolean isAsc) {
        domainId = domainId == null ? BaseConst.DEFAULT_DOMAIN_ID : domainId;
        if (cdt == null) {
            cdt = new CCcCi();
        }
        cdt.setDomainId(domainId);
        orders = BinaryUtils.isEmpty(orders) ? "modifyTime" : orders;
        return esCiSvc.queryESCiInfoList(cdt, orders, isAsc);
    }

    @Override
    public List<CcCiInfo> queryCiInfoList(Long domainId, CCcCi cdt, String orders, Boolean isAsc, Boolean hasClass) {
        if (cdt == null) {
            cdt = new CCcCi();
        }
        cdt.setDomainId(domainId);
        orders = BinaryUtils.isEmpty(orders) ? "modifyTime" : orders;
        return esCiSvc.queryCiInfoList(cdt, orders, isAsc, hasClass);
    }

    @Override
    public List<ESCIInfo> getESCIInfoListByCIPrimaryKeys(Long domainId, List<List<String>> ciPrimaryKeys) {
        domainId = domainId == null ? BaseConst.DEFAULT_DOMAIN_ID : domainId;
        if (BinaryUtils.isEmpty(ciPrimaryKeys)) {
            return Collections.emptyList();
        }
        return esCiSvc.getCIInfoListByCIPrimaryKeys(domainId, ciPrimaryKeys);
    }

    @Override
    public List<CcCiInfo> getCIInfoListByCIPrimaryKeys(Long domainId, List<List<String>> ciPrimaryKeys) {
        domainId = domainId == null ? BaseConst.DEFAULT_DOMAIN_ID : domainId;
        List<ESCIInfo> esciInfos = this.getESCIInfoListByCIPrimaryKeys(domainId, ciPrimaryKeys);
        return commSvc.transEsInfoList(esciInfos, false);
    }

    @Override
    public Page<CcCiInfo> queryCiInfoPage(Long domainId, Integer pageNum, Integer pageSize, CCcCi cdt, String orders, Boolean isAsc, Boolean hasClass) {
        if (cdt == null) {
            cdt = new CCcCi();
        }
        cdt.setDomainId(domainId);
        return esCiSvc.queryCiInfoPage(pageNum, pageSize, cdt, orders, isAsc, hasClass);
    }

    @Override
    public CcCiSearchPage searchCIByCdt(int pageNum, int pageSize, CCcCi bean) {
        if (BinaryUtils.isEmpty(bean)) {
            bean = new CCcCi();
        }
        if (bean.getDomainId() == null) {
            bean.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
        }
        return esCiSvc.searchCIByCdt(pageNum, pageSize, bean);
    }

    @Override
    public Page<ESCIInfo> searchESCIByBean(ESCISearchBean bean) {
        if (bean.getDomainId() == null) {
            bean.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
        }
        return esCiSvc.searchESCIByBean(bean);
    }

    @Override
    public CcCiSearchPage searchCIByBean(ESCISearchBean bean) {
        if (BinaryUtils.isEmpty(bean)) {
            bean = new ESCISearchBean();
            bean.setPageNum(1);
            bean.setPageSize(30);
        }
        if (bean.getDomainId() == null) {
            bean.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
        }
        return esCiSvc.searchCIByBean(bean);
    }

    @Override
    public Integer updateESCIInfoBatch(List<ESCIInfo> esCiInfoList) {
        return esCiSvc.updateESCIInfoBatch(esCiInfoList);
    }

    @Override
    public ImportResultMessage importCiByCiClsIds(MultipartFile file, Long classId) {
        // 校验分类
        Assert.notNull(classId, "X_PARAM_NOT_NULL${name:classId}");
        ESCIClassInfo classInfo = esClsSvc.getById(classId);
        Assert.notNull(classInfo, "BS_MNAME_CLASS_NOT_EXSIT");
        String className = classInfo.getClassName();
        ImportExcelMessage imResult = this.importCiExcel(file);
        String sheetName = null;
        for (String imSheetName : imResult.getSheetNames()) {
            if (className.equals(imSheetName)) {
                // sheet未填写领域的情况
                sheetName = imSheetName;
            }
        }
        if (sheetName == null) {
            throw MessageException.i18n("BS_MNAME_CLASS_DATA_ERROR", "{\"field\":" + className + "}");
        }
        CiExcelInfoDto infoDto = CiExcelInfoDto.builder().fileName(imResult.getFileName()).dirId(classInfo.getDirId()).sheetNames(Collections.singletonList(sheetName)).build();
        return this.importCiByClassBatch(classInfo.getDomainId(), infoDto, false);
    }

    @Override
    public ResponseEntity<byte[]> exportCiOrClass(ExportCiDto exportDto) {
        ValidDtoUtil.valid(exportDto);
        ResponseEntity<byte[]> res;
        if (exportDto == null) {
            exportDto = new ExportCiDto();
        }
        if (exportDto.isDataMode()) {
            // 处理准确数据模式前置动作
            Set<Long> ciIds = exportDto.getCiIds();
            Page<ESCIInfo> cis = esCiSvc.getListByQuery(1, ciIds.size(), QueryBuilders.termsQuery("id", ciIds));
            Assert.isTrue(cis.getData().size() == ciIds.size(), "指定的ciIds[" + com.alibaba.fastjson.JSON.toJSONString(ciIds) + "]有不存在数据");
            Set<Long> clsIds = cis.getData().stream().collect(Collectors.groupingBy(ESCIInfo::getClassId)).keySet();
            exportDto.setCiClassIds(clsIds);
        }
        Set<Long> classIds = exportDto.getCiClassIds();
        // 默认不导出类定义
        Integer hasClsDef = exportDto.getHasClsDef();
        // 默认只导出分类
        Integer hasData = exportDto.getHasData();
        // 默认导出全部类定义
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("domainId", exportDto.getDomainId() == null ? BaseConst.DEFAULT_DOMAIN_ID : exportDto.getDomainId()));
        if (!BinaryUtils.isEmpty(classIds)) {
            query.must(QueryBuilders.termsQuery("id", classIds));
        }
        List<ESCIClassInfo> ciClassInfos = esClsSvc.getListByQuery(query);
        // 导出类定义
        String fileName = FileUtil.ExcelUtil.getExportFileName(new CIExportLabel().getCIFileName(true), CommUtil.EXCEL07_XLSX_EXTENSION, false);
        File export = null;
        Workbook swb = null;
        try {
            String path = URLDecoder.decode(ResourceUtils.getURL("classpath:").getPath(), "utf-8");
            File exportDir = new File(path + "/static/download");
            if (!exportDir.exists()) {
                exportDir.mkdirs();
            }
            export = new File(exportDir, "CI-" + CommUtil.EXPORT_TIME_FORMAT.format(new Date()));
            export.mkdir();
            InputStream is = this.getClass().getResourceAsStream("/static_res/ci_new_data_template.xlsx");
            swb = new SXSSFWorkbook(new XSSFWorkbook(is));
        } catch (Exception e) {
            log.error("生成excel失败：",e);
            throw new BinaryException("ci导出报错");
        }
        // 导出类定义
        if (hasClsDef == 1) {
            clsSvc.exportCIClassDef(swb, ciClassInfos);
        }
        // 导出类数据，只导出勾选的分类
        boolean dataNull = true;
        Integer dataCount = 0;
        Integer excelCount = 0;
        if (!(BinaryUtils.isEmpty(classIds) || BinaryUtils.isEmpty(ciClassInfos))) {
            // 标记CI数据是否为空，只要查到数据则设为false
            // 导出数据
            if (hasData == 1) {
                fileName = FileUtil.ExcelUtil.getExportFileName(new CIExportLabel().getCIFileName(false), CommUtil.EXCEL07_XLSX_EXTENSION, true);
                for (ESCIClassInfo classInfo : ciClassInfos) {
                    List<CcCiAttrDef> attrDefs = esClsSvc.getAllDefsByClassId(classInfo.getDomainId(), classInfo.getId());
                    if (BinaryUtils.isEmpty(attrDefs)) {
                        continue;
                    }
                    // 定义集合有序保存列头值
                    List<String> titleCellValues = new ArrayList<String>();
                    Set<String> reqCellValues = new HashSet<String>();
                    // 获取业务主键属性定义
                    Set<String> pkbCellValues = new HashSet<String>();
                    // 指定ciCode列
                    String majorCellValue = SysUtil.StaticUtil.CICODE_LABEL;
                    // 默认第一列为ciCode列
                    titleCellValues.add(majorCellValue);
                    for (CcCiAttrDef attrDef : attrDefs) {
                        if (attrDef.getIsMajor() == 1) {
                            pkbCellValues.add(attrDef.getProName());
                            reqCellValues.add(attrDef.getProName());
                        } else if (attrDef.getIsRequired() == 1) {
                            reqCellValues.add(attrDef.getProName());
                        }
                        // 3D模型属性校验
                        int proType = attrDef.getProType();
                        if (!isShow3dAttribute && proType == AttrNameKeyEnum.MODEL.getType()) {
                            continue;
                        }
                        titleCellValues.add(attrDef.getProName());

                    }
                    if (pkbCellValues.isEmpty()) {
                        continue;
                    }
                    String sheetName = CiExcelUtil.convertSheetNameSpecialChar(classInfo.getClassName());
                    // 创建Sheet并设置标题行
                    Sheet sheet = FileUtil.ExcelUtil.createExcelSheetAndTitle(swb, sheetName, titleCellValues, reqCellValues, pkbCellValues, majorCellValue);
                    // if (hasData == 1) {
                    // 兼容指定数据模式，只把指定的id填入到query中当筛选，简单处理沿用游标不做切换(照理说指定数据的就不应该这里再查了因为已经有了)
                    Map<String, Page<ESCIInfo>> rs = null;
                    BoolQueryBuilder exportQuery = QueryBuilders.boolQuery();
                    if (exportDto.isDataMode()) {
                        exportQuery.must(QueryBuilders.termsQuery("id", exportDto.getCiIds()));
                    }
                    exportQuery.must(QueryBuilders.termQuery("classId", classInfo.getId()));
                    rs = esCiSvc.getScrollByQuery(1, 2000, exportQuery, "id", false);
                    if (rs != null) {
                        String scrollId = rs.keySet().iterator().next();
                        Page<ESCIInfo> page = rs.get(scrollId);
                        long ciCount = 0;
                        long total = page.getTotalRows();
                        int rowNum = 1;
                        List<ESCIInfo> ciInfos = new ArrayList<>(page.getData());
                        while (ciCount < total) {
                            List<ESCIInfo> secList = esCiSvc.getListByScroll(scrollId);
                            ciInfos.addAll(secList);
                            dataNull = false;
                            // 提取正文值map
                            List<Map<String, String>> commentValues = new ArrayList<Map<String, String>>();
                            for (ESCIInfo info : ciInfos) {
                                Map<String, Object> attrs = info.getAttrs();
                                if (attrs == null || attrs.isEmpty()) {
                                    continue;
                                }
                                attrs.put(majorCellValue, info.getCiCode());

                                // 导出时, 3D模型属性路径移除
                                this.ciExportImgAttrPathCheck(attrDefs, attrs);
                                Map<String, String> attrStr = com.alibaba.fastjson.JSON.parseObject(com.alibaba.fastjson.JSON.toJSONString(attrs), new TypeReference<Map<String, String>>() {
                                });
                                commentValues.add(attrStr);
                            }
                            // 持续写入正文
                            if (commentValues.size() > 0) {
                                FileUtil.ExcelUtil.writeExcelComment(swb, sheet, rowNum, titleCellValues, null, null, commentValues);
                            }
                            ciCount += ciInfos.size();
                            dataCount += ciInfos.size();
                            rowNum += ciInfos.size();
                            ciInfos.clear();
                            // 数据量达到了单个Excel最大量,且不是最后一页,则需要写完当前Excel再创建新的Excel来存储
                            if (dataCount >= CommUtil.EXCEL_MAX_DATA_COUNT) {
                                excelCount++;
                                try {
                                    String tempFileName = FileUtil.ExcelUtil.getExportFileName(new CIExportLabel().getCIFileName(false), CommUtil.EXCEL07_XLSX_EXTENSION, true);
                                    File output = new File(export, tempFileName);
                                    FileOutputStream fileOutputStream = new FileOutputStream(output);
                                    swb.write(fileOutputStream);
                                    swb.close();
                                    fileOutputStream.close();
                                } catch (Exception e) {
                                    throw BinaryUtils.transException(e, ServiceException.class);
                                }
                                dataCount = 0;
                                rowNum = 1;
                                swb = new SXSSFWorkbook();
                                sheet = FileUtil.ExcelUtil.createExcelSheetAndTitle(swb, sheetName, titleCellValues, reqCellValues, pkbCellValues, majorCellValue);
                            }
                        }
                        esCiSvc.clearScroll(scrollId);
                    }
                    // }
                }
            }
        }
        File firstFile = null;
        // 末尾数据保存为一个excel
        if (dataCount > 0 || hasData != 1 || dataNull) {
            excelCount++;
            if (excelCount == 1) {
                firstFile = new File(export, fileName);
            }
            try {
                File output = new File(export, fileName);
                FileOutputStream fileOutputStream = new FileOutputStream(output);
                swb.write(fileOutputStream);
                swb.close();
                fileOutputStream.close();
            } catch (Exception e) {
                throw BinaryUtils.transException(e, ServiceException.class);
            }
        }
        if (excelCount > 1) {
            Compression.compressZip(new File(export.getPath()), new File(export.getPath() + ".zip"));
            res = this.returnRes(new File(export.getPath() + ".zip"));
        } else {
            res = this.returnRes(firstFile);
        }
        return res;
    }

    @Override
    public ImportExcelMessage importCiExcel(MultipartFile file) {
        // 校验文件
        boolean isXlsx = FileUtil.ExcelUtil.validExcelImportFile(file);
        ImportExcelMessage message = new ImportExcelMessage();
        message.setOriginalFileName(file.getOriginalFilename());
        // 上传文件存入资源池
        Long dateTimeFolder = ESUtil.getNumberDate();
        String dirPath = localPath + "/" + dateTimeFolder;
        File destFolder = new File(dirPath);
        if (!destFolder.exists()) {
            destFolder.mkdirs();
        }
        File excelFile = null;
        try {
            String fileName = CommUtil.getExportFileName("CI", isXlsx ? CommUtil.EXCEL07_XLSX_EXTENSION : CommUtil.EXCEL03_XLS_EXTENSION);
            excelFile = new File(dirPath + File.separator + fileName);
            FileUtil.writeFile(excelFile, file.getBytes());
            message.setFileName("/" + dateTimeFolder + "/" + fileName);
        } catch (Exception e) {
            log.error("文件上传异常");
            throw new MessageException(e.getMessage());
        }
        String clsDefKey = LanguageResolver.trans("BS_CI_CLASS_EXPORT_CLASS_DEFINITION");
        if (clsDefKey == null) {
            clsDefKey = "对象定义";
        }
        String importNotice = LanguageResolver.trans("BS_MNAME_IMPORT_NOTICE");
        if (importNotice == null) {
            importNotice = SysUtil.StaticUtil.README_SHEETNAME;
        }
        InputStream is = null;
        Workbook wb = null;
        Map<String, String> sheetNameMap = new LinkedHashMap<>();
        List<String> sheetNames = new ArrayList<>();
        List<String[]> sheetVal = null;
        try {
            if (isXlsx) {
                sheetNames = EasyExcelUtil.getExcelAllSheetNames(excelFile);
                if (sheetNames.contains(clsDefKey)) {
                    SheetData sheetData = EasyExcelUtil.readSheet(clsDefKey, null, excelFile);
                    sheetVal = sheetData.getRows();
                }
            } else {
                is = file.getInputStream();
                wb = new HSSFWorkbook(is);
                for (int i = 0; i < wb.getNumberOfSheets(); i++) {
                    Sheet sheet = wb.getSheetAt(i);
                    String sheetName = sheet.getSheetName();
                    if (clsDefKey.equalsIgnoreCase(sheetName)) {
                        sheetVal = FileUtil.ExcelUtil.getSheetDatasArraybyPage(1, sheet.getLastRowNum(), sheet, null);
                    }
                    sheetNames.add(sheetName);
                }
            }
            if (!BinaryUtils.isEmpty(sheetVal)) {
                for (String[] vals : sheetVal) {
                    if (!BinaryUtils.isEmpty(vals[1]) && !sheetNameMap.containsKey(vals[1].toUpperCase())) {
                        sheetNameMap.put(vals[1].toUpperCase(), vals[1]);
                    }
                }
            }
            if (!BinaryUtils.isEmpty(sheetNames)) {
                sheetNames.forEach(name -> {
                    if (!sheetNameMap.containsKey(name.toUpperCase())) {
                        sheetNameMap.put(name.toUpperCase(), name);
                    }
                });
            }
        } catch (Exception e) {
            log.error("文件操作失败", e);
        } finally {
            try {
                is.close();
                wb.close();
            } catch (Exception e) {
            }
        }
        sheetNameMap.remove(importNotice);
        sheetNameMap.remove(clsDefKey);
        Assert.notEmpty(sheetNameMap.values(), "数据为空，不可导入！");
        message.setSheetNames(new ArrayList<>(sheetNameMap.values()));
        FileUtil.ExcelUtil.setImportExcelData(message, excelFile);
        return message;
    }

    @Override
    public ImportResultMessage importCiByClassBatch(Long domainId, CiExcelInfoDto excelInfoDto, boolean addAttr) {
        // 校验必要参数
        Assert.notNull(excelInfoDto.getFileName(), "X_PARAM_NOT_NULL${name:fileName}");
        Assert.notNull(excelInfoDto.getSheetNames(), "X_PARAM_NOT_NULL${name:sheetNames}");
        String clsDefKey = LanguageResolver.trans("BS_CI_CLASS_EXPORT_CLASS_DEFINITION");
        if (clsDefKey == null) {
            clsDefKey = "对象定义";
        }
        List<ImportSheetMessage> sheetMessages = new ArrayList<>();
        SysUser loginUser = SysUtil.getCurrentUserInfo();

        // 用于存储异步响应结果
        // saveMessage = new ArrayList<>();
        FileInputStream is = null;
        HSSFWorkbook workbook = null;
        // List<Long> saveClsIds = new ArrayList<>();

        // 从对象存储获取文件并更新本地资源
        rsmUtils.downloadRsmAndUpdateLocalRsm(excelInfoDto.getFileName());

        // 读取本地文件
        File file = new File(localPath + excelInfoDto.getFileName());
        Assert.isTrue(file.exists(), "系统找不到指定的文件");
        List<String> sheetNames = excelInfoDto.getSheetNames().stream()/*.map(String::toUpperCase)*/.collect(Collectors.toList());
        // 判断excel文件版本，03和07版本读取方式不同
        String excelType = CommUtil.getImportExcelType(file.getName());
        boolean isXlsx = true;
        if (excelType.toUpperCase().equals(CommUtil.EXCEL07_XLSX_EXTENSION.toUpperCase())) {
            isXlsx = true;
        } else if (excelType.toUpperCase().equals(CommUtil.EXCEL03_XLS_EXTENSION.toUpperCase())) {
            isXlsx = false;
        } else {
            throw MessageException.i18n("BS_MNAME_NOT_SUPPORT_FILETYPE");
        }
        try {
            // 数据sheet集合
            List<String> fileSheetNames = new ArrayList<>();
            // 分类定义内容
            List<String[]> clsSheetVal = null;
            if (isXlsx) {
                fileSheetNames = EasyExcelUtil.getExcelAllSheetNames(file);
                if (fileSheetNames.contains(clsDefKey)) {
                    SheetData sheetData = EasyExcelUtil.readSheet(clsDefKey, null, file);
                    clsSheetVal = sheetData.getRows();
                }
            } else {
                is = new FileInputStream(file);
                workbook = new HSSFWorkbook(is);
                for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
                    Sheet sheet = workbook.getSheetAt(i);
                    if (sheet.getSheetName().equalsIgnoreCase(clsDefKey)) {
                        clsSheetVal = FileUtil.ExcelUtil.getSheetDatasArraybyPage(1, sheet.getLastRowNum(), sheet, null);
                    }
                    fileSheetNames.add(sheet.getSheetName());
                }
            }
            //原有数据权限中不包含admin的分类权限
            Map<String, SysRoleDataModuleRlt> oldNoAdminRoleDataModuleRltMap = new HashMap<>();
            if (excelInfoDto.isOverwriteData()) {
                //清空数据
                BoolQueryBuilder ciClassQuery = QueryBuilders.boolQuery();
                ciClassQuery.must(QueryBuilders.termQuery("domainId", domainId));
                ciClassQuery.must(QueryBuilders.termsQuery("className.keyword", sheetNames));
                List<ESCIClassInfo> ciClassInfos = esClsSvc.getListByQuery(ciClassQuery);
                if (!ciClassInfos.isEmpty()) {
                    List<Long> existClassIdList = new ArrayList<>();
                    Map<Long, ESCIClassInfo> oldClassMap = new HashMap<>();
                    ciClassInfos.forEach(esciClassInfo -> {
                        existClassIdList.add(esciClassInfo.getId());
                        oldClassMap.put(esciClassInfo.getId(), esciClassInfo);
                    });
                    //获取子分类
                    List<ESCIClassInfo> childs = esClsSvc.getListByQuery(QueryBuilders.boolQuery().must(QueryBuilders.termsQuery("parentId", existClassIdList)));
                    childs.forEach(esciClassInfo -> {
                        existClassIdList.add(esciClassInfo.getId());
                        oldClassMap.put(esciClassInfo.getId(), esciClassInfo);
                    });
                    attrTransConfigSvc.deleteByQuery(QueryBuilders.termsQuery("classId", existClassIdList), true);
                    ciHistorySvc.deleteByQuery(QueryBuilders.termsQuery("classId", existClassIdList), true);
                    esClsSvc.deleteByIds(existClassIdList);
                    esCiSvc.deleteByQuery(QueryBuilders.termsQuery("classId",existClassIdList),true);
                    ciRltSvc.delRlts(null, existClassIdList, existClassIdList);

                    //删除分类授权
                    CSysRoleDataModuleRlt cdt = new CSysRoleDataModuleRlt();
                    String[] dataValues = new String[existClassIdList.size()];
                    for (int i = 0; i < existClassIdList.size(); i++) {
                        dataValues[i] = existClassIdList.get(i) + "";
                    }
                    cdt.setDataValues(dataValues);
                    cdt.setDomainId(domainId);
                    List<SysRoleDataModuleRlt> oldRoleDataModuleRlts = roleSvc.getRoleDataModuleRltByCdt(cdt);

                    //获取admin角色
                    BoolQueryBuilder roleQuery = QueryBuilders.boolQuery();
                    roleQuery.must(QueryBuilders.termQuery("domainId", domainId));
                    roleQuery.must(QueryBuilders.termQuery("roleName.keyword", "admin"));
                    List<SysRole> admin = roleSvc.getRolesByQuery(roleQuery);
                    if (admin.isEmpty()) {
                        throw new MessageException("缺少admin角色");
                    }
                    Long adminRoleId = admin.get(0).getId();
                    for (SysRoleDataModuleRlt oldRoleDataModuleRlt : oldRoleDataModuleRlts) {
                        if (!oldRoleDataModuleRlt.getRoleId().equals(adminRoleId)) {
                            Long oldClassId = Long.parseLong(oldRoleDataModuleRlt.getDataValue());
                            ESCIClassInfo esciClassInfo = oldClassMap.get(oldClassId);
                            oldNoAdminRoleDataModuleRltMap.put(esciClassInfo.getClassName(), oldRoleDataModuleRlt);
                        }
                    }
                    roleSvc.deleteRoleDataModuleRlt(cdt);
                }
            }
            // 先保存分类
            if (!BinaryUtils.isEmpty(clsSheetVal)) {
                ImportSheetMessage clsSheetMessage = clsSvc.importDirAndCIClass(domainId, clsSheetVal, sheetNames, addAttr);
                clsSheetMessage.setSheetName(clsDefKey);
                sheetMessages.add(clsSheetMessage);
            }
            // 查询分类数据
            BoolQueryBuilder ciClassQuery = QueryBuilders.boolQuery();
            ciClassQuery.must(QueryBuilders.termQuery("domainId", domainId));
            ciClassQuery.must(QueryBuilders.termsQuery("className.keyword", sheetNames));
            List<ESCIClassInfo> ciClassInfos = esClsSvc.getListByQuery(ciClassQuery);
            Map<String, ESCIClassInfo> clsMap = new TreeMap<>(String.CASE_INSENSITIVE_ORDER);
            clsMap.putAll(BinaryUtils.toObjectMap(ciClassInfos, "className"));
            //保存原有分类权限
            if (!oldNoAdminRoleDataModuleRltMap.isEmpty()) {
                List<SysRoleDataModuleRlt> updateRoleDataModuleRlts = new ArrayList<>();
                for (Entry<String, SysRoleDataModuleRlt> entry : oldNoAdminRoleDataModuleRltMap.entrySet()) {
                    String className = entry.getKey();
                    SysRoleDataModuleRlt sysRoleDataModuleRlt = entry.getValue();
                    ESCIClassInfo esciClassInfo = clsMap.get(className);
                    if (esciClassInfo != null) {
                        Long id = esciClassInfo.getId();
                        sysRoleDataModuleRlt.setUid(id + "");
                        sysRoleDataModuleRlt.setDataValue(id + "");
                        updateRoleDataModuleRlts.add(sysRoleDataModuleRlt);
                    }
                }
                if (!updateRoleDataModuleRlts.isEmpty()) {
                    roleSvc.addRoleDataModuleRlt(domainId, updateRoleDataModuleRlts, false);
                }
            }
            // 循环保存数据
            for (String sheetName : fileSheetNames) {
                if (sheetNames.contains(sheetName)) {
                    List<String> titles;
                    List<String[]> sheetVal;
                    if (isXlsx) {
                        SheetData sheetData = EasyExcelUtil.readSheet(sheetName, null, file);
                        // sheetVal =
                        // FileUtil.ExcelUtil.XLSXCovertCSVReader.readerExcelByFile(file,
                        // sheetName, 0);
                        titles = sheetData.getTitles();
                        sheetVal = sheetData.getRows();
                        if (BinaryUtils.isEmpty(sheetVal)) {
                            continue;
                        }
                        // Collections.addAll(titles, sheetVal.remove(0));
                    } else {
                        HSSFSheet sheet = workbook.getSheet(sheetName);
                        titles = new ArrayList<>(FileUtil.ExcelUtil.getSheetTitles(sheet).values());
                        sheetVal = FileUtil.ExcelUtil.getSheetDatasArraybyPage(1, sheet.getLastRowNum(), sheet, null);
                    }
                    ImportSheetMessage sheetMessage = ImportSheetMessage.builder().sheetName(sheetName).className(sheetName).build();
                    sheetMessages.add(sheetMessage);
                    if (titles == null || titles.size() <= 1) {
                        sheetMessage.setTotalNum(sheetVal.size());
                        sheetMessage.setFailNum(sheetVal.size());
                        sheetMessage.setErrMsg(LanguageResolver.trans("BS_MNAME_CLASS_DATA_ERROR", "{\"field\":" + sheetName + "}"));
                        continue;
                    }
                    Map<String, List<String>> attrMark = FileUtil.ExcelUtil.getAttrMarkByTitles(titles);
                    titles = attrMark.get("titles");
                    // 填充读取的原始titles
                    sheetMessage.setTitles(titles);
                    if (!clsMap.containsKey(sheetName)) {
                        sheetMessage.setTotalNum(sheetVal.size());
                        sheetMessage.setFailNum(sheetVal.size());
                        sheetMessage.setErrMsg(LanguageResolver.trans("BS_MNAME_CLASS_NOT_EXSIT"));
                        continue;
                    }
                    ESCIClassInfo ciClassInfo = clsMap.get(sheetName);
                    // CcCiClassInfo ciClassInfo = ciClassInfos.get(0);
                    // saveClsIds.add(ciClassInfo.getId());
                    if (ciClassInfo.getParentId() != 0) {
                        List<CcCiAttrDef> allDefss = esClsSvc.getAllDefsByClassId(ciClassInfo.getDomainId(), ciClassInfo.getId());
                        ciClassInfo.setCcAttrDefs(allDefss);
                    }
                    List<CcCiAttrDef> attrDefs = ciClassInfo.getCcAttrDefs();
                    // CI属性定义不存在！ 主要防止数据不正常现象
                    if (BinaryUtils.isEmpty(attrDefs)) {
                        sheetMessage.setTotalNum(sheetVal.size());
                        sheetMessage.setFailNum(sheetVal.size());
                        sheetMessage.setErrMsg(LanguageResolver.trans("BS_MNAME_NOT_CI_ATTR_DEF"));
                        continue;
                    }
                    Long classId = ciClassInfo.getId();
                    final String classStdCode = ciClassInfo.getClassStdCode();
                    String className = ciClassInfo.getClassName();
                    int rIdx = 1; // 数据行号，用于标识错误信息
                    List<CcCiRecord> ciRecords = new ArrayList<>();
                    for (String[] rows : sheetVal) {
                        // 取第一列的值
                        String oneCellValue = rows[0];
                        // 转成map
                        Map<String, String> map = new HashMap<String, String>();
                        for (int j = 1; j < titles.size(); j++) {
                            String val = rows[j] == null ? rows[j] : rows[j].trim();
                            map.put(titles.get(j), val);
                        }
                        rIdx++;
                        CcCiRecord record = new CcCiRecord();
                        record.setIndex(rIdx);
                        record.setAttrs(map);
                        // 有ciCode且值不为空则填充ciCode
                        if (!BinaryUtils.isEmpty(oneCellValue)) {
                            record.setCiCode(oneCellValue);
                        }
                        ciRecords.add(record);
                    }
                    ImportSheetMessage classDetailedResult = new ImportSheetMessage(className);
                    String ownerCode = BinaryUtils.isEmpty(excelInfoDto.getOwnerCode()) ? loginUser.getLoginCode() : excelInfoDto.getOwnerCode();
                    if (!ciRecords.isEmpty()) {
                        while (ciRecords.size() > 0) {
                            List<CcCiRecord> subList = ciRecords.subList(0, Math.min(ciRecords.size(), 3000));
                            CiClassSaveInfo saveInfo = new CiClassSaveInfo(classId, classStdCode, loginUser.getId(),ownerCode,  subList, null);
                            classDetailedResult = this.saveOrUpdateCiBatchForExcelImport(domainId, saveInfo, ciClassInfo);
                            // 累计当前分类的结果后对当前分类的详情重新赋值
                            sheetMessage.setTotalNum(classDetailedResult.getTotalNum() + sheetMessage.getTotalNum());
                            sheetMessage.setSuccessNum(classDetailedResult.getSuccessNum() + sheetMessage.getSuccessNum());
                            sheetMessage.setFailNum(classDetailedResult.getFailNum() + sheetMessage.getFailNum());
                            sheetMessage.setIgnoreNum(classDetailedResult.getIgnoreNum() + sheetMessage.getIgnoreNum());
                            sheetMessage.setInsertNum(classDetailedResult.getInsertNum() + sheetMessage.getInsertNum());
                            sheetMessage.setUpdateNum(classDetailedResult.getUpdateNum() + sheetMessage.getUpdateNum());
                            if (!BinaryUtils.isEmpty(classDetailedResult.getRowMessages())) {
                                sheetMessage.getRowMessages().addAll(classDetailedResult.getRowMessages());
                            }
                            ciRecords.removeAll(subList);
                        }
                    }
                    // 等待每个分类落盘完成，防止不同分类间业务主键重复
                }
            }
        } catch (IllegalArgumentException e) {
            throw new MessageException(e.getMessage());
        } catch (MessageException e) {
            throw new MessageException(e.getMessage());
        } catch (Exception e) {
            throw new MessageException(e.getMessage());
        } finally {
            if (is != null) {
                try {
                    is.close();
                } catch (IOException e) {
                }
            }
            if (workbook != null) {
                try {
                    workbook.close();
                } catch (IOException e) {
                }
            }
        }
        ImportResultMessage result = FileUtil.ExcelUtil.writeSheetMessageToFile(sheetMessages, "CI导入明细", clsDefKey);
        // 统计分类、CI导入详情
        for (ImportSheetMessage sheetMessage : sheetMessages) {
            if (clsDefKey.equalsIgnoreCase(sheetMessage.getSheetName())) {
                result.setDefTotalNum(sheetMessage.getTotalNum());
                result.setDefSuccessNum(sheetMessage.getSuccessNum());
                result.setDefFailNum(sheetMessage.getFailNum());
            }
        }
        file.delete();

        rsmUtils.deleteRsm(excelInfoDto.getFileName());

        // 记录文件操作
        resourceSvc.saveSyncResourceInfo(excelInfoDto.getFileName(), syncHttpPath + excelInfoDto.getFileName(), false, 1);
        // long time1 = System.currentTimeMillis();
        // long insertNum = 0;
        // for (ImportSheetMessage sheetMessage : sheetMessages) {
        // if (!clsDefKey.equals(sheetMessage.getSheetName())) {
        // insertNum += sheetMessage.getInsertNum();
        // }
        // }
        // long dbInsert = 0;
        // long errNum = 0;
        // while (insertNum - errNum > dbInsert) {
        // try {
        // Thread.sleep(2000);
        // } catch (InterruptedException e) {
        // }
        // dbInsert = 0;
        // Map<String, Long> clsCiCountMap =
        // esCiSvc.groupByCountField("classId", QueryBuilders.matchAllQuery());
        // for (Long clsId : saveClsIds) {
        // Long now = clsCiCountMap.get(String.valueOf(clsId)) == null ? 0L :
        // clsCiCountMap.get(String.valueOf(clsId));
        // Long old = dbCiCountMap.get(String.valueOf(clsId)) == null ? 0L :
        // dbCiCountMap.get(String.valueOf(clsId));
        // dbInsert += now - old;
        // }
        // errNum = 0;
        // for (Map<String, Object> saveMap : saveMessage) {
        // errNum += (Integer) saveMap.get("failCount") == null ? 0 : (Integer)
        // saveMap.get("failCount");
        // }
        // }
        // long time2 = System.currentTimeMillis();
        // log.info("wait for data save : " + (time2 - time1) + "ms");
        return result;
    }

    @Override
    public Integer deleteById(Long id, Long sourceId) {
        ciRltSvc.delRltByCiId(id);
        ESCIInfo esciInfo = esCiSvc.getById(id);
        if (esciInfo != null) {
            CcCiInfo ciInfo = commSvc.tranCcCiInfo(esciInfo, true);
            // CcCiInfo ciInfo = getCiInfoById(id);
            // 记录CI操作日志
            this.saveCIOperateLog(sourceId, SysUtil.StaticUtil.LOG_DYNAMIC_DELETE, ciInfo.getAttrDefs(), ciInfo.getAttrs(), null, ciInfo.getCiClass().getClassName(), ciInfo.getCi());
            // 记录CI历史版本
            esCiSvc.transCIAttrs(Collections.singletonList(esciInfo), false);
            esHistorySvc.saveOrUpdateHistoryInfo(esciInfo, ESCIHistoryInfo.ActionType.DELETE);
            return esCiSvc.deleteById(id);
        } else {
            return -1;
        }
    }

    @Override
    public Integer removeByIds(List<Long> ciIds, Long sourceId) {
        if (BinaryUtils.isEmpty(ciIds)) {
            return 1;
        }
        CCcCi cdt = new CCcCi();
        cdt.setIds(ciIds.toArray(new Long[]{}));

        List<ESCIInfo> esciInfos = esCiSvc.getSortListByCdt(1, ciIds.size(), cdt, null, true).getData();
        if (!esciInfos.isEmpty()) {
            List<CcCiInfo> ciInfos = commSvc.transEsInfoList(esciInfos, true);
            List<ESCIOperateLog> ciLogs = new ArrayList<>();
            ciInfos.forEach(ciInfo -> {
                ciLogs.add(ESCIOperateLogSvc.buildLogRecord(sourceId, SysUtil.StaticUtil.LOG_DYNAMIC_DELETE,
                        ciInfo.getAttrDefs(), ciInfo.getAttrs(), null, ciInfo.getCiClass().getClassName(), ciInfo.getCi()));
            });
            this.saveCIOperateLogBatch(ciLogs);
            esCiSvc.transCIAttrs(esciInfos, false);
            esHistorySvc.saveOrUpdateHistoryInfosBatch(esciInfos, ESCIHistoryInfo.ActionType.DELETE);
            ciRltSvc.delRltByCiIds(ciIds);
            return esCiSvc.deleteByIds(ciIds);
        } else {
            return -1;
        }


    }

    @Override
    public Integer removeByPrimaryKeys(Long domainId, List<String> ciPrimaryKeys, Long sourceId) {
        Assert.isTrue(!BinaryUtils.isEmpty(ciPrimaryKeys), "X_PARAM_NOT_NULL${name:ciPrimaryKeys}");
        domainId = domainId == null ? BaseConst.DEFAULT_DOMAIN_ID : domainId;
        ESCISearchBean bean = new ESCISearchBean();
        bean.setDomainId(domainId);
        bean.setCiPrimaryKeys(ciPrimaryKeys);
        bean.setPageNum(1);
        bean.setPageSize(ciPrimaryKeys.size());
        Page<ESCIInfo> page = searchESCIByBean(bean);
        Page<CcCiInfo> ciInfos = commSvc.transEsInfoPage(page, true);
        List<ESCIOperateLog> ciLogs = new ArrayList<>();
        List<Long> ciIds = new ArrayList<>();
        ciInfos.getData().forEach(ciInfo -> {
            ciIds.add(ciInfo.getCi().getId());
            ciLogs.add(ESCIOperateLogSvc.buildLogRecord(sourceId, SysUtil.StaticUtil.LOG_DYNAMIC_DELETE,
                    ciInfo.getAttrDefs(), ciInfo.getAttrs(), null, ciInfo.getCiClass().getClassName(), ciInfo.getCi()));
        });
        this.saveCIOperateLogBatch(ciLogs);
        esCiSvc.transCIAttrs(page.getData(), false);
        esHistorySvc.saveOrUpdateHistoryInfosBatch(page.getData(), ESCIHistoryInfo.ActionType.DELETE);
        ciRltSvc.delRltByCiIds(ciIds);
        return esCiSvc.deleteByIds(ciIds);
    }

    @Override
    public Integer removeByClassId(Long classId, Long sourceId) {
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("classId", classId));
        long count = esCiSvc.countByCondition(query);
        if (count > 0) {
            long totalPages = count % 3000 == 0 ? count / 3000 : count / 3000 + 1;
            int pageNum = 0;
            while (pageNum < totalPages) {
                List<ESCIInfo> esciInfos = esCiSvc.getListByQuery(++pageNum, 3000, query).getData();
                List<CcCiInfo> ciInfos = commSvc.transEsInfoList(esciInfos, true);
                // List<CcCiInfo> ciInfos =
                // esCiSvc.getCIInfoPageByQuery(pageNum++, 3000, query,
                // true).getData();
                List<ESCIOperateLog> ciLogs = new ArrayList<>();
                ciInfos.forEach(ciInfo -> {
                    ciLogs.add(ESCIOperateLogSvc.buildLogRecord(sourceId, SysUtil.StaticUtil.LOG_DYNAMIC_DELETE,
                            ciInfo.getAttrDefs(), ciInfo.getAttrs(), null, ciInfo.getCiClass().getClassName(),
                            ciInfo.getCi()));
                });
                this.saveCIOperateLogBatch(ciLogs);
                esCiSvc.transCIAttrs(esciInfos, false);
                esHistorySvc.saveOrUpdateHistoryInfosBatch(esciInfos, ESCIHistoryInfo.ActionType.DELETE);
            }
            ciRltSvc.delRltByCiClassId(classId);
            esCiSvc.removeByClassId(classId);
        }
        return 1;
    }

    /**
     * 专为一键导入提供的批量保存接口
     *
     * @param saveInfo    要保存的CI列表
     * @param ciClassInfo 分类信息，子分类需要携带父级属性
     * @return
     */
    private ImportSheetMessage saveOrUpdateCiBatchForExcelImport(Long domainId, CiClassSaveInfo saveInfo, ESCIClassInfo ciClassInfo) {
        BinaryUtils.checkEmpty(saveInfo, "saveInfo");
        BinaryUtils.checkEmpty(ciClassInfo, "classInfo");
        BinaryUtils.checkEmpty(ciClassInfo.getId(), "classId");
        BinaryUtils.checkEmpty(ciClassInfo.getCcAttrDefs(), "attrDefs");
        BinaryUtils.checkEmpty(ciClassInfo.getClassStdCode(), "classStdCode");
        ImportSheetMessage detailedResult = ImportSheetMessage.builder().className(ciClassInfo.getClassName()).build();
        // 待保存数据为空，直接返回
        List<CcCiRecord> records = saveInfo.getRecords();
        if (BinaryUtils.isEmpty(records)) {
            return detailedResult;
        }
        SysUser loginUser = null;
        try {
            loginUser = SysUtil.getCurrentUserInfo();
        } catch (Exception e) {
            log.error("获取登陆用户失败，所有值取默认值");
        }
        Long sourceId = saveInfo.getSourceId() == null ? 1L : saveInfo.getSourceId();
        Long classId = saveInfo.getClassId() == null ? ciClassInfo.getId() : saveInfo.getClassId();
        String classStdCode = BinaryUtils.isEmpty(saveInfo.getClassStdCode()) ? ciClassInfo.getClassStdCode() : saveInfo.getClassStdCode();
        List<ImportRowMessage> rowMessages = new ArrayList<>();
        // 统计信息
        int successCount = 0;
        int failCount = 0;
        int totals = 0;
        int ignores = 0;
        int inserts = 0;
        int updates = 0;
        // 保存CI
        if (!BinaryUtils.isEmpty(records)) {
            totals = records.size();
            List<CcCiAttrDef> attrDefs = ciClassInfo.getCcAttrDefs();

            // When the 3D model switch is turned on, get whether the 3D classification is included in the attribute, and get the image path
            String attrNameFor3dModel = "";
            Set<String> imagesPath = new HashSet<>(128);
            if (isShow3dAttribute) {
                attrNameFor3dModel = this.getModelProStdName(attrDefs);
                List<CcImage> images = esImageSvc.getListByQuery(1, 9999, QueryBuilders.termQuery("domainId", domainId)).getData();
                if (images.size() > 0) {
                    imagesPath = images.stream().map(CcImage::getImgPath).filter(path -> path != null && !"".equals(path.trim())).collect(Collectors.toSet());
                }
            }

            List<String> defNames = attrDefs.stream().map(CcCiAttrDef::getProStdName).collect(Collectors.toList());
            // 查询要更新的数据
            Map<String, CcCiInfo> ciCodeMap = new HashMap<>();
            // Map<Long, String> idToCode = new HashMap<>();
            Set<String> ciCodeSet = records.stream().map(CcCiRecord::getCiCode).filter(code -> !BinaryUtils.isEmpty(code)).collect(Collectors.toSet());
            if (!BinaryUtils.isEmpty(ciCodeSet)) {
                BoolQueryBuilder query = QueryBuilders.boolQuery();
                query.must(QueryBuilders.termQuery("domainId", domainId));
                query.must(QueryBuilders.termsQuery("ciCode.keyword", ciCodeSet));
                List<CcCiInfo> esInfos = esCiSvc.getCIInfoPageByQuery(1, 5000, query, false).getData();
                if (!BinaryUtils.isEmpty(esInfos)) {
                    for (CcCiInfo esCiInfo : esInfos) {
                        ciCodeMap.put(esCiInfo.getCi().getCiCode(), esCiInfo);
                        // idToCode.put(esCiInfo.getId(), esCiInfo.getCiCode());
                    }
                }
            }
            List<CcCiInfo> ciInfosList = new ArrayList<CcCiInfo>();
            List<Integer> hashCodes = new ArrayList<>();
            Map<String, Integer> keyToIndex = new HashMap<>();
            List<String> saveCodes = new ArrayList<>();
            List<String> savePrimaryKeys = new ArrayList<>();
            // CI校验-属性
            // 获取数据字典值
            Map<String, List<String>> dictValuesMap = this.getExterDictValues(domainId, attrDefs);
            Iterator<CcCiRecord> it = records.iterator();
            Map<String, ESCIOperateLog> updateLogMap = new HashMap<>();
            recordLoop:
            while (it.hasNext()) {
                boolean isUpdate = false;
                CcCiRecord record = it.next();
                Map<String, String> attrs = record.getAttrs();
                /**
                 * 3D model attribute picture path verification verification.
                 * When the 3D model switch is turned on,
                 * and the attributes include 3D type attributes,
                 * */
                if (isShow3dAttribute && !"".equals(attrNameFor3dModel)) {
                    this.attributeImgPathVerificationFor3dModel(attrNameFor3dModel, attrs, imagesPath);
                }

                // 校验属性
                Map<String, Integer> errMsg = commSvc.validAttrs(attrDefs, attrs, true);
                if (!BinaryUtils.isEmpty(errMsg)) {
                    List<ImportCellMessage> cellMessages = new ArrayList<>();
                    errMsg.forEach((msg, errType) -> {
                        ImportCellMessage cellMessage = new ImportCellMessage();
                        cellMessage.setErrorType(errType);
                        cellMessage.setErrorDesc(msg);
                        cellMessages.add(cellMessage);
                    });
                    ImportRowMessage rowMessage = new ImportRowMessage();
                    rowMessage.setRowNum(record.getIndex());
                    rowMessage.setMessageItems(cellMessages);
                    rowMessages.add(rowMessage);
                    failCount++;
                    it.remove();
                    continue;
                }
                Map<String, String> stdMap = toStdMap(attrs);
                // 校验数据字典类型
                if (!BinaryUtils.isEmpty(dictValuesMap)) {
                    Iterator<Entry<String, List<String>>> dictIt = dictValuesMap.entrySet().iterator();
                    while (dictIt.hasNext()) {
                        Entry<String, List<String>> next = dictIt.next();
                        String key = next.getKey();
                        List<String> values = next.getValue();
                        String val = stdMap.get(key);
                        if (record.getCiId() == null && !BinaryUtils.isEmpty(val) && !values.contains(val)) {
                            rowMessages.add(this.buildRowMessage(record.getIndex(), CheckAttrUtil.FAILURE, "属性[" + key + "]引用值[" + val + "]不存在"));
                            ignores++;
                            it.remove();
                            continue recordLoop;
                        }
                    }
                }
                // 校验ciCode，同一分类下，ciCode相同更新
                String ciCode = record.getCiCode();
                if (!BinaryUtils.isEmpty(ciCode)) {
                    if (saveCodes.contains(ciCode)) {
                        rowMessages.add(this.buildRowMessage(record.getIndex(), CheckAttrUtil.EXIST, "ciCode【" + ciCode + "】重复"));
                        ignores++;
                        it.remove();
                        continue;
                    } else if (ciCodeMap.containsKey(ciCode)) {
                        CcCiInfo esciInfo = ciCodeMap.get(ciCode);
                        // 不同分类下ciCode相同，视为重复，同分类下则更新
                        if (esciInfo.getCi().getClassId().longValue() == classId.longValue()) {
                            record.setCiId(esciInfo.getCi().getId());
                            updates++;
                            isUpdate = true;
                        } else {
                            rowMessages.add(this.buildRowMessage(record.getIndex(), CheckAttrUtil.EXIST, "ciCode【" + ciCode + "】重复"));
                            ignores++;
                            it.remove();
                            continue;
                        }
                    }
                    saveCodes.add(ciCode);
                }
                List<String> ciPKAttrDefNames = CommUtil.getCiPKAttrDefNames(attrDefs);
                // 获取所属分类下的业务主键值的hashCode
                Integer hashCode;
                try {
                    hashCode = CommUtil.getCiMajorHashCode(attrs, ciPKAttrDefNames, classStdCode);
                } catch (Exception e) {
                    failCount++;
                    rowMessages.add(this.buildRowMessage(record.getIndex(), CheckAttrUtil.FAILURE, "业务主键生成失败"));
                    continue;
                }
                // 获取当前CI的业务主键值
                List<String> ciPrimaryKeys = CommUtil.getCiPrimaryKeys(classStdCode, attrs, ciPKAttrDefNames);
                String primaryKey = JSON.toString(ciPrimaryKeys);
                if (savePrimaryKeys.contains(primaryKey)) {
                    ignores++;
                    rowMessages.add(this.buildRowMessage(record.getIndex(), CheckAttrUtil.EXIST, "业务主键【" + ciPrimaryKeys + "】重复"));
                    continue;
                } else {
                    savePrimaryKeys.add(primaryKey);
                }
                CcCiInfo ciInfo = new CcCiInfo();
                // 属性过滤，只保存定义过的属性
                // 属性不包含在定义的属性，并且不是下划线开头结尾的字段，去掉
                stdMap.keySet().removeIf(attrName -> !defNames.contains(attrName) && !attrName.matches("_([\\d\\D]*?)_"));
                // CI 属性大小写转换
                ciInfo.setAttrs(stdMap);
                CcCi ci = new CcCi();
                if (isUpdate) {
                    CcCiInfo oldCi = ciCodeMap.get(ciCode);
                    ci.setCreateTime(oldCi.getCi().getCreateTime());
                    ci.setCiVersion(oldCi.getCi().getCiVersion());
                    // 属性合并
                    Map<String, String> combineAttrs = new HashMap<>();
                    combineAttrs.putAll(oldCi.getAttrs());
                    combineAttrs.putAll(stdMap);
                    ciInfo.setAttrs(combineAttrs);
                    if (!CheckAttrUtil.checkAttrMapEqual(combineAttrs, oldCi.getAttrs())) {
                        ci.setCiVersion(String.valueOf(Long.parseLong(oldCi.getCi().getCiVersion()) + 1));
                    }
                    ci.setOwnerCode(oldCi.getCi().getOwnerCode());
                } else {
                    ci.setCiVersion(String.valueOf(1));
                    ci.setCreator(loginUser == null ? "system" : loginUser.getLoginCode());
                    ci.setCreateTime(ESUtil.getNumberDateTime());
                    ci.setOwnerCode(ci.getCreator());
                }
                ci.setId(record.getCiId() == null ? ESUtil.getUUID() : record.getCiId());
                ci.setCiCode(ciCode);
                ci.setClassId(classId);
                ci.setSourceId(sourceId);
                ci.setDataStatus(1);
                ci.setState(CIState.CREATE_COMPLETE.val());
                ci.setHashCode(hashCode);
                ci.setCiPrimaryKey(primaryKey);
                ci.setCreateTime(ESUtil.getNumberDateTime());
                ci.setDomainId(domainId);
                ci.setModifyTime(ESUtil.getNumberDateTime());
                ciInfo.setCi(ci);
                ciInfosList.add(ciInfo);
                hashCodes.add(hashCode);
                keyToIndex.put(ci.getCiPrimaryKey(), record.getIndex());
                if (isUpdate) {
                    ESCIOperateLog ciLog = ESCIOperateLogSvc.buildLogRecord(sourceId,
                            SysUtil.StaticUtil.LOG_DYNAMIC_UPDATE, attrDefs, ciCodeMap.get(ciCode).getAttrs(), stdMap,
                            ciClassInfo.getClassName(), ci);
                    updateLogMap.put(ciCode, ciLog);
                }
            }
            List<String> repeatKeys = new ArrayList<>();
            // 校验hashCode是否重复
            Map<String, Integer> res = this.checkRecordsByHashCode(domainId, ciInfosList, repeatKeys, updateLogMap);
            ignores += res.get("ignore") == null ? 0 : res.get("ignore");
            updates += res.get("update") == null ? 0 : res.get("update");
            if (!BinaryUtils.isEmpty(repeatKeys)) {
                for (String str : repeatKeys) {
                    rowMessages.add(this.buildRowMessage(keyToIndex.get(str), CheckAttrUtil.EXIST, "业务主键【" + str + "】重复"));
                }
            }
            // 保存CI操作日志
            List<ESCIOperateLog> ciLogs = new ArrayList<>(updateLogMap.values());
            for (ESCIOperateLog ciLog : ciLogs) {
                ciLog.setSourceId(sourceId);
                ciLog.setCiClassName(ciClassInfo.getClassName());
                ciLog.setProNames(attrDefs.stream().map(CcCiAttrDef::getProName).collect(Collectors.toList()));
                ciLog.setOperator(loginUser == null ? "system" : loginUser.getLoginCode());
            }
            Set<String> keySet = updateLogMap.keySet();
            if (ciInfosList.size() > keySet.size()) {
                ciInfosList.forEach(ciInfo -> {
                    if (BinaryUtils.isEmpty(ciInfo.getCi().getCiCode())) {
                        ciInfo.getCi().setCiCode(String.valueOf(ciInfo.getCi().getId()));
                    }
                    String ciCode = ciInfo.getCi().getCiCode();
                    if (!keySet.contains(ciCode)) {
                        ciLogs.add(ESCIOperateLogSvc.buildLogRecord(sourceId, SysUtil.StaticUtil.LOG_DYNAMIC_INSERT,
                                attrDefs, null, ciInfo.getAttrs(), ciClassInfo.getClassName(), ciInfo.getCi()));
                    }
                });
            }
            Map<String, Object> saveOrUpdateMsg = esCiSvc.saveOrUpdateCIBatch(ciInfosList, false);
            // if (!BinaryUtils.isEmpty(saveOrUpdateMsg.get("failCount"))) {
            // failCount += (Integer) saveOrUpdateMsg.get("failCount");
            // }
            inserts = (totals - failCount - updates - ignores);
            successCount = inserts + updates;
            if (!BinaryUtils.isEmpty(saveOrUpdateMsg.get("errMessge"))) {
                String errStr = JSON.toString(saveOrUpdateMsg.get("errMessge"));
                // 入库失败
                rowMessages.add(this.buildRowMessage(1, CheckAttrUtil.FAILURE, "入库失败：" + errStr));
            }
            this.saveCIOperateLogBatch(ciLogs);
            // saveMessage.add(saveOrUpdateMsg);
        }
        // 汇总整理结果
        detailedResult.setSuccessNum(successCount);
        detailedResult.setFailNum(failCount);
        detailedResult.setInsertNum(inserts);
        detailedResult.setUpdateNum(updates);
        detailedResult.setIgnoreNum(ignores);
        detailedResult.setTotalNum(totals);
        detailedResult.setRowMessages(rowMessages);
        return detailedResult;
    }

    @SuppressWarnings("unchecked")
    public Map<String, List<String>> getExterDictValues(Long domainId, List<CcCiAttrDef> attrDefs) {
        Assert.isTrue(!BinaryUtils.isEmpty(attrDefs), "X_PARAM_NOT_NULL${name:attrDefs}");
        Map<String, List<String>> dictValMap = new HashMap<>();
        List<CcCiAttrDef> dictDefs =
                attrDefs.stream().filter(def -> def.getProType() == AttrNameKeyEnum.DICT.getType() && !BinaryUtils.isEmpty(def.getProDropSourceDef())).collect(Collectors.toList());
        for (CcCiAttrDef def : dictDefs) {
            try {
                Long dictClassId = CiClassProDropSourceDefHelper.getCiClassProDropSourceClassId(def.getProDropSourceDef().trim());
                Long[] dictDefIds = CiClassProDropSourceDefHelper.getCiClassProDropSourceDefIds(def.getProDropSourceDef().trim());
                List<String> dictValues = dictSvc.getExteralDictValues(domainId, dictClassId, dictDefIds);
                dictValMap.put(def.getProName().toUpperCase(), dictValues);
            } catch (Exception e) {
                Assert.isTrue(false, "属性[" + def.getProName().toUpperCase() + "]引用不合法");
            }
        }
        return dictValMap;
    }

    /**
     * icon path conversion
     *
     * @param attrDefs attributes def
     * @param attrs    ci attributes
     */
    private void removeModelIconPath(List<CcCiAttrDef> attrDefs, Map<String, String> attrs) {
        for (CcCiAttrDef def : attrDefs) {
            int attrType = def.getProType();
            if (AttrNameKeyEnum.PICTURE.getType() == attrType || AttrNameKeyEnum.MODEL.getType() == attrType || AttrNameKeyEnum.DOCUMENT.getType() == attrType) {
                this.convertModelPath(attrs, def.getProStdName(), false);
            }
        }
    }


    private void removeModelIconPathObject(List<CcCiAttrDef> attrDefs, Map<String, Object> attrs) {
        if (isShow3dAttribute) {
            String getProStdName = this.getModelProStdName(attrDefs);
            if (!"".equals(getProStdName)) {
                this.convertModelPathObject(attrs, getProStdName, false);
            }
        }
    }

    /**
     * Get the attribute name corresponding to the 3D model
     *
     * @param bean        ESCISearchBean
     * @param ciGroupPage ciGroupPage
     */
    private void addModelIconPath(ESCISearchBean bean, CiGroupPage ciGroupPage) {
        if (isShow3dAttribute) {
            CCcCi cCcCi = bean.getCdt();
            if (null != cCcCi) {
                long classId = cCcCi.getClassId();
                ESCIClassInfo esciClassInfo = esClsSvc.getById(classId);
                String modelAttrDef = this.getModelProStdName(esciClassInfo.getCcAttrDefs());

                List<CcCiInfo> ccCiInfos = ciGroupPage.getData();
                if (!"".equals(modelAttrDef)) {
                    ccCiInfos.forEach(ciInfo -> {
                        Map<String, String> dbCiAttrs = ciInfo.getAttrs();
                        if (dbCiAttrs != null && !dbCiAttrs.isEmpty()) {
                            this.convertModelPath(dbCiAttrs, modelAttrDef, true);
                        }
                    });
                }
            }
        }
    }


    /**
     * Get the attribute name corresponding to the 3D model
     *
     * @param attrDefs CcCiAttrDef
     */
    public String getModelProStdName(List<CcCiAttrDef> attrDefs) {
        String modelAttrDef = "";
        List<String> modelAttrDefNames = new ArrayList<>(1);

        attrDefs.forEach(attr -> {
            int attrType = attr.getProType();
            if (attrType == AttrNameKeyEnum.MODEL.getType()) {
                modelAttrDefNames.add(attr.getProStdName());
            }
        });
        if (modelAttrDefNames.size() > 0) {
            modelAttrDef = modelAttrDefNames.get(0);
        }
        return modelAttrDef;
    }


    /**
     * Get the attribute name corresponding to the 3D model
     *
     * @param attrs      attributes definition
     * @param proStdName 3D model attribute name
     * @param isAddPath  add url path / remove url path
     */
    private void convertModelPath(Map<String, String> attrs, String proStdName, Boolean isAddPath) {
        String modelVal = attrs.get(proStdName);
        if (modelVal != null) {
            if (modelVal.startsWith(this.urlPath)) {
                modelVal = modelVal.replaceAll(this.urlPath, "");
            }
            if (isAddPath) {
                modelVal = this.urlPath + modelVal;
            }
            attrs.put(proStdName, modelVal);
        }
    }

    private void convertModelPathObject(Map<String, Object> attrs, String proStdName, Boolean isAddPath) {
        Object modelVal = attrs.get(proStdName);
        if (modelVal != null) {
            String modelValStr = modelVal.toString();
            if (modelValStr.startsWith(this.urlPath)) {
                modelValStr = modelValStr.replaceAll(this.urlPath, "");
            }
            if (isAddPath) {
                modelValStr = this.urlPath + modelValStr;
            }
            attrs.put(proStdName, modelValStr);
        }
    }


    /**
     * When one-click import, check the path of the image containing the 3D model
     *
     * @param attrNameFor3dModel 3D type attribute name
     * @param attrs              With check attribute collection
     * @param imagesPath         Existing image path
     */
    public void attributeImgPathVerificationFor3dModel(String attrNameFor3dModel, Map<String, String> attrs, Set<String> imagesPath) {
        String iconPath = attrs.get(attrNameFor3dModel);
        if (iconPath != null) {
            if (!imagesPath.contains(iconPath)) {
                String defaultPath = esClsSvc.getDefault3dModel();
                attrs.put(attrNameFor3dModel, defaultPath);
            }
        }
    }


    /**
     * When exporting CI, check and remove the path
     *
     * @param attrDefs attrDefs
     * @param attrs    With check attribute collection
     */
    public void ciExportImgAttrPathCheck(List<CcCiAttrDef> attrDefs, Map<String, Object> attrs) {
        for (CcCiAttrDef def : attrDefs) {
            int attrType = def.getProType();
            String proStdName = def.getProStdName();
            if (attrType == AttrNameKeyEnum.PICTURE.getType()
                    || (isShow3dAttribute && attrType == AttrNameKeyEnum.MODEL.getType() || attrType == AttrNameKeyEnum.DOCUMENT.getType())) {
                Object attrValueForImg = attrs.get(proStdName);
                if (null != attrValueForImg) {
                    String attrValueStrForImg = attrValueForImg.toString();
                    if (attrValueStrForImg.startsWith(this.urlPath)) {
                        attrValueStrForImg = attrValueStrForImg.replaceAll(this.urlPath, "");
                    }
                    attrs.put(proStdName, attrValueStrForImg);
                }
            }
        }
    }

    /**
     * KEY转换成大写
     *
     * @param map
     * @return
     */
    public Map<String, String> toStdMap(Map<String, String> map) {
        Map<String, String> ret = new HashMap<String, String>();
        if (map == null) {
            return ret;
        }
        Set<String> keySet = map.keySet();
        for (String key : keySet) {
            if (key != null) {
                ret.put(key.toUpperCase(), map.get(key));
            } else {
                ret.put(null, map.get(key));
            }
        }
        return ret;
    }

    public ImportRowMessage buildRowMessage(Integer rowNum, Integer errorType, String message) {
        ImportRowMessage rowMessage = new ImportRowMessage();
        rowMessage.setRowNum(rowNum);
        ImportCellMessage cellMessage = new ImportCellMessage();
        cellMessage.setErrorType(errorType);
        cellMessage.setErrorDesc(message);
        rowMessage.setMessageItems(Collections.singletonList(cellMessage));
        return rowMessage;
    }

    public boolean compareCiPrimaryKeys(List<String> ciPks1, List<String> ciPks2) {
        try {
            return CommUtil.compareToCiPks(ciPks1, ciPks2);
        } catch (Exception e) {
            log.error("do compareToCiPks 【" + JSON.toString(ciPks1) + "】 and 【 " + JSON.toString(ciPks2) + "】 err!");
        }
        return false;
    }

    public ResponseEntity<byte[]> returnRes(File file) {
        HttpHeaders headers = new HttpHeaders();
        ResponseEntity<byte[]> entity = null;
        InputStream in = null;
        try {
            in = new FileInputStream(file);
            byte[] bytes = new byte[in.available()];
            in.read(bytes);
            headers.add("Content-Disposition", "attachment;filename=" + URLEncoder.encode(file.getName(), "UTF-8"));
            entity = new ResponseEntity<byte[]>(bytes, headers, HttpStatus.OK);
        } catch (Exception e) {
            throw new MessageException(e.getMessage());
        } finally {
            if (in != null) {
                try {
                    in.close();
                } catch (Exception e) {
                    throw new MessageException(e.getMessage());
                }
            }
        }
        return entity;
    }

    protected Map<String, Integer> checkRecordsByHashCode(Long domainId, List<CcCiInfo> ciInfosList, List<String> repeatKeys, Map<String, ESCIOperateLog> updateLogMap) {
        // updateLogMap用于判断根据ciCode更新的CI，避免更新操作重复记录
        Map<String, Integer> res = new HashMap<>();
        int ignore = 0;
        int update = 0;
        Set<Integer> hashCodes = new HashSet<>();
        ciInfosList.forEach(ciInfo -> hashCodes.add(ciInfo.getCi().getHashCode()));
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termsQuery("hashCode", hashCodes));
        query.must(QueryBuilders.termQuery("domainId", domainId));
        long liveCisCount = esCiSvc.countByCondition(query);
        if (liveCisCount <= 0) {
            return res;
        }
        List<CcCiInfo> liveCis = esCiSvc.getCIInfoPageByQuery(1, new BigDecimal(liveCisCount).intValue(), query, false).getData();
        Map<Integer, List<CcCiInfo>> dbCIs = new HashMap<>();
        liveCis.forEach(ciInfo -> {
            CcCi ci = ciInfo.getCi();
            dbCIs.computeIfAbsent(ci.getHashCode(), k -> new LinkedList<>());
            dbCIs.get(ci.getHashCode()).add(ciInfo);
        });
        Iterator<CcCiInfo> iterator = ciInfosList.iterator();
        while (iterator.hasNext()) {
            CcCiInfo ciInfo = iterator.next();
            // 校验是否与库中的CI重复
            if (dbCIs.containsKey(ciInfo.getCi().getHashCode())) {
                List<CcCiInfo> esInfos = dbCIs.get(ciInfo.getCi().getHashCode());
                for (CcCiInfo esInfo : esInfos) {
                    CcCi ci = esInfo.getCi();
                    if (compareCiPrimaryKeys(JSON.toList(ciInfo.getCi().getCiPrimaryKey(), String.class), JSON.toList(ci.getCiPrimaryKey(), String.class))) {
                        // 同分类下业务主键重复，更新
                        boolean isUpdate = ci.getClassId().longValue() == ciInfo.getCi().getClassId().longValue()
                                && (BinaryUtils.isEmpty(ciInfo.getCi().getCiCode()) || ci.getCiCode().equals(ciInfo.getCi().getCiCode()));
                        if (isUpdate) {
                            if (!updateLogMap.containsKey(ci.getCiCode())) {
                                update++;
                                ESCIOperateLog ciLog = ESCIOperateLog.builder().ciId(ci.getId()).ciCode(ci.getCiCode()).ciPrimaryKey(ciInfo.getCi().getCiPrimaryKey())
                                        .dynamic(SysUtil.StaticUtil.LOG_DYNAMIC_UPDATE).newAttrs(ciInfo.getAttrs()).oldAttrs(esInfo.getAttrs()).libaryId(1).build();
                                updateLogMap.put(ci.getCiCode(), ciLog);
                            }
                            ciInfo.getCi().setCiVersion(esInfo.getCi().getCiVersion());
                            // 属性合并
                            Map<String, String> combineAttrs = new HashMap<>();
                            combineAttrs.putAll(esInfo.getAttrs());
                            combineAttrs.putAll(ciInfo.getAttrs());
                            ciInfo.setAttrs(combineAttrs);
                            if (!CheckAttrUtil.checkAttrMapEqual(combineAttrs, esInfo.getAttrs())) {
                                ciInfo.getCi().setCiVersion(String.valueOf(Long.parseLong(esInfo.getCi().getCiVersion()) + 1));
                            }
                            ciInfo.getCi().setId(ci.getId());
                            ciInfo.getCi().setCiCode(ci.getCiCode());
                            ciInfo.getCi().setCreateTime(ci.getCreateTime());
                            ciInfo.getCi().setCreator(ci.getCreator());
                        } else {
                            if (updateLogMap.containsKey(ciInfo.getCi().getCiCode())) {
                                update--;
                                updateLogMap.remove(ciInfo.getCi().getCiCode());
                            }
                            ignore++;
                            repeatKeys.add(ciInfo.getCi().getCiPrimaryKey());
                            iterator.remove();
                            break;
                        }
                    }
                }
            }
            if (BinaryUtils.isEmpty(ciInfo.getCi().getCiCode())) {
                ciInfo.getCi().setCiCode(String.valueOf(ciInfo.getCi().getId()));
            }
        }
        res.put("ignore", ignore);
        res.put("update", update);
        return res;
    }

    @Override
    public Map<Long, Long> countCiNumGroupClsByQuery(ESCISearchBean bean) {
        Map<Long, Long> clsIdCiNumMap = new HashMap<>();
        bean = bean == null ? new ESCISearchBean() : bean;
        if (bean.getDomainId() == null) {
            bean.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
        }
        QueryBuilder query = commSvc.getCIQueryBuilderByBean(bean);
        Map<String, Long> countRes = esCiSvc.groupByCountField("classId", query);
        countRes.forEach((clsId, ciNum) -> clsIdCiNumMap.put(Long.valueOf(clsId), ciNum));
        return clsIdCiNumMap;
    }

    private Long saveCIOperateLog(Long sourceId, Integer dynamic, List<CcCiAttrDef> attrDefs, Map<String, String> oldAttrs, Map<String, String> newAttrs, String className, CcCi ci) {
        ESCIOperateLog log = ESCIOperateLogSvc.buildLogRecord(sourceId, dynamic, attrDefs, oldAttrs, newAttrs,
                className, ci);
        return logSvc.saveOrUpdate(log);
    }

    public Integer saveCIOperateLogBatch(List<ESCIOperateLog> logs) {
        return logSvc.saveOrUpdateBatch(logs);
    }


    @Override
    public Long countByQuery(ESCISearchBean bean) {
        bean = bean == null ? new ESCISearchBean() : bean;
        if (bean.getDomainId() == null) {
            bean.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
        }
        QueryBuilder query = commSvc.getCIQueryBuilderByBean(bean);
        return esCiSvc.countByCondition(query);
    }

    // /**
    // * 校验属性是否变化
    // *
    // * @param newAttrs
    // * @param oldAttrs
    // * @return
    // */
    // private boolean checkAttrChange(Map<String, String> newAttrs, Map<String,
    // String> oldAttrs) {
    // Map<String, String> checkNewAttrs = newAttrs.entrySet().stream()
    // .filter((e) -> !BinaryUtils.isEmpty(e.getValue()))
    // .collect(Collectors.toMap((e) -> e.getKey(), (e) -> e.getValue()));
    // Map<String, String> checkOldAttrs = oldAttrs.entrySet().stream()
    // .filter((e) -> !BinaryUtils.isEmpty(e.getValue()))
    // .collect(Collectors.toMap((e) -> e.getKey(), (e) -> e.getValue()));
    // Iterator<Map.Entry<String, String>> it =
    // checkOldAttrs.entrySet().iterator();
    // while (it.hasNext()) {
    // String key = it.next().getKey();
    // boolean isDuplicate = (checkOldAttrs.get(key) == null &&
    // checkNewAttrs.get(key) == null)
    // || (checkOldAttrs.get(key) != null && checkNewAttrs.get(key) != null
    // && checkOldAttrs.get(key).equals(checkNewAttrs.get(key)));
    // if (isDuplicate) {
    // it.remove();
    // checkNewAttrs.remove(key);
    // }
    // }
    // return checkNewAttrs.size() > 0 || checkOldAttrs.size() > 0 ? true :
    // false;
    // }
    @Override
    public Page<String> getAttrValuesBySearchBean(ESAttrAggBean searchBean) {
        Long classId = searchBean.getClassId();
        Long attrDefId = searchBean.getAttrDefId();
        Long[] attrDefIds = searchBean.getAttrDefIds();
        Assert.notNull(classId, "X_PARAM_NOT_NULL${name:classId}");
        if (BinaryUtils.isEmpty(attrDefIds)) {
            Assert.notNull(attrDefId, "X_PARAM_NOT_NULL${name:attrDefId}");
            attrDefIds = new Long[]{attrDefId};
        }

        //根据属性Id属性信息得到属性名称
        CcCiClassInfo ccCiClassInfo = esClsSvc.queryClassInfoById(classId);
        Assert.notNull(ccCiClassInfo, "分类不存在");

        List<String> attrNames = new ArrayList<>();
        Map<Long, CcCiAttrDef> attrDefMap = ccCiClassInfo.getAttrDefs().stream().collect(Collectors.toMap(CcCiAttrDef::getId, e -> e));
        for (Long defId : attrDefIds) {
            if (!attrDefMap.containsKey(defId)) {
                log.error("属性不存在");
                return new Page<>(searchBean.getPageNum(), searchBean.getPageSize(), 0, 0, new ArrayList<>());
            }
            attrNames.add(attrDefMap.get(defId).getProName());
        }
        //属性转换表
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("classId", classId));
        query.must(QueryBuilders.termsQuery("defId", attrDefIds));
        List<ESCIAttrTransConfig> attrConfigs = attrConfigSvc.getListByQuery(query);
        if (!BinaryUtils.isEmpty(attrConfigs) && attrConfigs.size() > 0) {
            for (ESCIAttrTransConfig attrConfig : attrConfigs) {
                if (!attrNames.contains(attrConfig.getShowName())) {
                    continue;
                }
                String attrName = "";
                if (attrConfig.getUpType() != null && attrConfig.getUpType().longValue() > 1) {
                    attrName = attrConfig.getTargetAttrName();
                } else {
                    attrName = attrConfig.getSourceAttrName();
                }
                int index = attrNames.indexOf(attrConfig.getShowName());
                attrNames.add(index, attrName);
                attrNames.remove(attrConfig.getShowName());
            }
        }
        Long domainId = ccCiClassInfo.getCiClass().getDomainId();
        if (BinaryUtils.isEmpty(attrDefIds)) {
            searchBean.setAttrName(attrNames.get(0));
            return esCiSvc.queryAttrVal(ccCiClassInfo.getCiClass().getDomainId(), searchBean);
        }
        return esCiSvc.queryAttrVal(domainId, classId, attrNames, true, searchBean);
    }

    @Override
    public Page<ESCIInfo> getESCIInfoPageByQuery(Long domainId, int pageNum, int pageSize, QueryBuilder query, List<SortBuilder<?>> sorts, Boolean isHighLight) {
        domainId = domainId == null ? BaseConst.DEFAULT_DOMAIN_ID : domainId;
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.termQuery("domainId", domainId));
        boolQueryBuilder.filter(query);
        if (isHighLight == null) {
            isHighLight = false;
        }
        if (isHighLight) {
            return esCiSvc.getSortListByHighLightQuery(pageNum, pageSize, boolQueryBuilder, sorts, null);
        } else {
            return esCiSvc.getSortListByQuery(pageNum, pageSize, boolQueryBuilder, sorts);
        }
    }


    public boolean updateAttrValueBatch(CIAttrValueUpdateDto dto) {
        dto.valid();
        String proName = dto.getProName();
        String value = dto.getValue();
        boolean flag = dto.isAll();
        List<Long> unCiIds = dto.getUnCiIds();
        // 获取属性信息
        ESCIClassInfo classInfo = esClsSvc.getById(dto.getClassId());
        Optional<ESCIAttrDefInfo> findFirst = classInfo.getAttrDefs().stream()
                .filter(def -> def.getProName().equals(proName)).findFirst();
        Assert.isTrue(findFirst.isPresent(), "属性[" + proName + "]不存在");
        ESCIAttrDefInfo def = findFirst.get();
        Assert.isTrue(def.getIsMajor().intValue() == 0, "不可批量修改主键值");
        Assert.isTrue(def.getIsRequired().intValue() == 0 || !BinaryUtils.isEmpty(value), "必填属性值不可为空");
        String attrKey = def.getProStdName();
        // 校验属性值格式
        Integer checkResult = CheckAttrUtil.validateAttrValType(def, value);
        Assert.isTrue(CheckAttrUtil.SUCCESS == checkResult, "属性值错误");

        BoolQueryBuilder query = QueryBuilders.boolQuery();
        //选择全部数据
        if (flag) {
            query.must(QueryBuilders.termQuery("classId", dto.getClassId()));
            if (!BinaryUtils.isEmpty(unCiIds)) {
                query.mustNot(QueryBuilders.termsQuery("id", unCiIds));
            }
        } else {
            query.must(QueryBuilders.termsQuery("id", dto.getCiIds()));
        }

        // 获取关联的CI，直接批量修改属性值，CI操作日志和历史版本不好处理，取出来再存
        Page<ESCIInfo> esciInfoss = esCiSvc
                .getListByQuery(1, 99999, query);
        List<ESCIInfo> esciInfos = esciInfoss.getData();
        List<ESCIOperateLog> logs = new ArrayList<>();
        // 构建CI操作日志
        for (ESCIInfo esciInfo : esciInfos) {
            CcCiInfo ciInfo = commSvc.tranCcCiInfo(esciInfo, false);
            Map<String, String> attrs = ciInfo.getAttrs();
            Map<String, String> newAttrs = new HashMap<>(attrs);
            if (attrs.containsKey(attrKey)) {
                newAttrs.put(attrKey, value);
                ESCIOperateLog log = ESCIOperateLogSvc.buildLogRecord(null, SysUtil.StaticUtil.LOG_DYNAMIC_UPDATE,
                        classInfo.getCcAttrDefs(), attrs, newAttrs, classInfo.getClassName(), ciInfo.getCi());
                logs.add(log);
                esciInfo.getAttrs().put(attrKey, value);
            }
            if (!CheckAttrUtil.checkAttrMapEqual(newAttrs, attrs)) {
                esciInfo.setVersion(esciInfo.getVersion() + 1);
            }
        }
        this.saveCIOperateLogBatch(logs);
        Integer res = esCiSvc.saveOrUpdateBatch(esciInfos);
        return res != 0;
    }

    @Override
    public Map<String, Long> queryCiCountByClassId() {
        // 根据分类id查询ci的个数
        Map<String, Long> clsCiCountMap = esCiSvc.groupByCountField("classId", QueryBuilders.matchAllQuery());
        return clsCiCountMap;
    }

    @Override
    public Page<ESCIInfo> queryCiInfoPage(int pageNum, int pageSize, QueryBuilder query, String sortField, boolean isAsc) {
        return esCiSvc.getSortListByQuery(pageNum, pageSize, query, sortField, isAsc);
    }

    @Override
    public Integer removeByOwnerCodeAndClassId(Long classId, String ownerCode) {
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("classId", classId));
        if(!BinaryUtils.isEmpty(ownerCode)){
            query.must(QueryBuilders.termQuery("loginCode", ownerCode));
        }
        long count = esCiSvc.countByCondition(query);
        if (count > 0) {
            long totalPages = count % 3000 == 0 ? count / 3000 : count / 3000 + 1;
            int pageNum = 0;
            while (pageNum < totalPages) {
                List<ESCIInfo> esciInfos = esCiSvc.getListByQuery(++pageNum, 3000, query).getData();
                List<CcCiInfo> ciInfos = commSvc.transEsInfoList(esciInfos, true);
                // List<CcCiInfo> ciInfos =
                // esCiSvc.getCIInfoPageByQuery(pageNum++, 3000, query,
                // true).getData();
                List<ESCIOperateLog> ciLogs = new ArrayList<>();
                this.saveCIOperateLogBatch(ciLogs);
                esCiSvc.transCIAttrs(esciInfos, false);
                esHistorySvc.saveOrUpdateHistoryInfosBatch(esciInfos, ESCIHistoryInfo.ActionType.DELETE);
            }
            ciRltSvc.delRltByCiClassId(classId);
            esCiSvc.removeByClassId(classId);
        }
        return 1;
    }

    @Override
    public Integer removeAllCI(Long domainId, QueryBuilder query) {
        domainId = domainId == null ? BaseConst.DEFAULT_DOMAIN_ID : domainId;
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.termQuery("domainId", domainId));
        return esCiSvc.removeAllCI(boolQueryBuilder);
    }


    @Override
    public boolean modifyAttrValueBatch(CIAttrValueUpdateDto dto) {
        return this.updateAttrValueBatch(dto);
    }

    @Override
    public Integer removeCiBatch(CIRemoveBatchDto dto) {
        dto.valid();
        Long classId = dto.getClassId();
        List<Long> ciIds = dto.getCiIds();
        boolean flag = dto.isAll();
        List<Long> unCiIds = dto.getUnCiIds();
        if (flag) {
            BoolQueryBuilder query = QueryBuilders.boolQuery();
            query.must(QueryBuilders.termQuery("classId", classId));
            if (!BinaryUtils.isEmpty(unCiIds)) {
                query.mustNot(QueryBuilders.termsQuery("id", unCiIds));
            }

            return esCiSvc.deleteByQuery(query, true);
        }
        //否则,批量删除已选的数据
        return this.removeByIds(ciIds, 1L);
    }

    public Long saveOrUpdateMethod (CcCiInfo ciInfo, SaveType saveType) {
        CcCi ci = ciInfo.getCi();
        if (ci.getDomainId() == null) {
            ci.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
        }
        Long classId = ciInfo.getCi().getClassId();
        String ciCode = ciInfo.getCi().getCiCode();
        Map<String, String> attrs = ciInfo.getAttrs();
        Assert.notNull(ci, "X_PARAM_NOT_NULL${name:ci}");
        Assert.notNull(classId, "X_PARAM_NOT_NULL${name:classId}");
        SysUser loginUser = null;
        try {
            loginUser = SysUtil.getCurrentUserInfo();
        } catch (Exception e) {
            log.error("获取登陆用户失败，所有值取默认值");
        }
        boolean isAdd = BinaryUtils.isEmpty(ci.getId()) ? true : false;
        String ownerCode = BinaryUtils.isEmpty(ci.getOwnerCode()) ? (loginUser == null ? "system" : loginUser.getLoginCode()) : ci.getOwnerCode();
        // 分类是否存在
        ESCIClassInfo ciClass = esClsSvc.getById(classId);
        if (ciClass == null) {
            throw MessageException.i18n("BS_MNAME_CLASS_NOT_EXSIT");
        }
        final String classStdCode = ciClass.getClassStdCode();
        // 校验CI属性-当前及父类属性
        List<CcCiAttrDef> attrDefs = esClsSvc.getAllDefsByClassId(ci.getDomainId(), classId);

        // When the 3D model is opened, save the CI and add the path conversion
        this.removeModelIconPath(attrDefs, attrs);

        Map<String, Integer> checkResult = commSvc.validAttrs(attrDefs, attrs, true);
        if (!BinaryUtils.isEmpty(checkResult)) {
            for (String result : checkResult.keySet()) {
                throw new MessageException(result);
            }
        }

        Map<String, String> stdMap = toStdMap(attrs);
        // 校验数据字典类型
        Map<String, List<String>> dictValuesMap = this.getExterDictValues(ciInfo.getCi().getDomainId(), attrDefs);
        if (!BinaryUtils.isEmpty(dictValuesMap)) {
            for (Entry<String, List<String>> next : dictValuesMap.entrySet()) {
                String key = next.getKey();
                List<String> values = next.getValue();

                String val = stdMap.get(key);
                Assert.isTrue(!isAdd || BinaryUtils.isEmpty(val) || values.contains(val), "属性[" + key + "]引用值[" + val + "]不存在");
            }
        }
        // 校验重复
        List<String> ciPKAttrDefNames = CommUtil.getCiPKAttrDefNames(attrDefs);
        Integer hashCode = 0;
        boolean isPrivate = LibTypeUtil.isPrivate();
        if(isPrivate){
            hashCode = CommUtil.getCiMajorHashCode(attrs, ciPKAttrDefNames, classStdCode, ownerCode);
        }else {
            hashCode = CommUtil.getCiMajorHashCode(attrs, ciPKAttrDefNames, classStdCode);
        }
        List<String> ciPrimaryKeys = CommUtil.getCiPrimaryKeys(classStdCode, attrs, ciPKAttrDefNames);
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.termQuery("domainId", ci.getDomainId()));
        boolQueryBuilder.filter(QueryBuilders.termQuery("hashCode", hashCode));
        if(isPrivate){
            boolQueryBuilder.filter(QueryBuilders.termQuery("ownerCode.keyword", ownerCode));
        }
        List<CcCiInfo> liveCis = esCiSvc.getCIInfoPageByQuery(1, 3000, boolQueryBuilder, false).getData();
        for (CcCiInfo cInfo : liveCis) {
            if (compareCiPrimaryKeys(ciPrimaryKeys, JSON.toList(cInfo.getCi().getCiPrimaryKey(), String.class))) {
                // 同分类下ciCode或id相同，更新，否则删除
                // 有id时优先按id更新，兼容仅有ciCode时更新的情况
                boolean idEqual = ci.getId() != null && ci.getId().longValue() == cInfo.getCi().getId().longValue();
                boolean codeEqual = ciCode != null && ciCode.equals(cInfo.getCi().getCiCode());
                boolean classIdEqual = ci.getClassId().longValue() == cInfo.getCi().getClassId().longValue();
                boolean isUpdate = (codeEqual || idEqual) && classIdEqual;
                if (isUpdate) {
                    if (ci.getId() != null && ciCode != null) {
                        Assert.isTrue(codeEqual && idEqual, "BS_CI_NO_EXIST");
                    }
                    ci.setId(cInfo.getCi().getId());
                    isAdd = false;
                } else {
                    throw MessageException.i18n("BS_MNAME_RECORD_CONTAINS");
                }
            }
        }

        if (isAdd && !BinaryUtils.isEmpty(ciCode)) {
            BoolQueryBuilder codeQuery = QueryBuilders.boolQuery();
            codeQuery.must(QueryBuilders.termQuery("ciCode.keyword", ciCode));
            if(isPrivate){
                boolQueryBuilder.filter(QueryBuilders.termQuery("ownerCode.keyword", ownerCode));
            }
            codeQuery.must(boolQueryBuilder.filter(QueryBuilders.termQuery("domainId", ci.getDomainId())));
            List<ESCIInfo> ciCodeQuery = esCiSvc.getListByQuery(codeQuery);
            if (!BinaryUtils.isEmpty(ciCodeQuery)) {
                ci.setId(ciCodeQuery.get(0).getId());
                isAdd = false;
            }
        }
        // 属性过滤，只保存定义过的属性
        Iterator<String> itAttr = stdMap.keySet().iterator();
        List<String> defNames = attrDefs.stream().map(CcCiAttrDef::getProStdName).collect(Collectors.toList());
        while (itAttr.hasNext()) {
            String attrName = itAttr.next();
            // 属性不包含在定义的属性，并且不是下划线开头结尾的字段，去掉
            if (!defNames.contains(attrName) && !attrName.matches("_([\\d\\D]*?)_")) {
                itAttr.remove();
            }
        }
        ciInfo.setAttrs(stdMap);
        Map<String, String> oldAttrs = null;
        // 组装CI
        if (isAdd) {
            long uuid = ESUtil.getUUID();
            ci.setId(uuid);
            ci.setCiCode(BinaryUtils.isEmpty(ciCode) ? String.valueOf(uuid) : ciCode);
            ci.setCreator(loginUser == null ? "system" : loginUser.getLoginCode());
            ci.setCreateTime(ESUtil.getNumberDateTime());
            ci.setCiVersion(String.valueOf(1));
            ci.setOwnerCode(ownerCode);
        } else {
            CcCiInfo dbCIInfo = esCiSvc.getCiInfoById(ci.getId());
            Assert.notNull(dbCIInfo, "BS_CI_NO_EXIST");
            oldAttrs = dbCIInfo.getAttrs();
            // When the 3D model is opened, save the CI and add the path conversion
            this.removeModelIconPath(attrDefs, oldAttrs);
            ci.setCiCode(dbCIInfo.getCi().getCiCode());
            ci.setCreateTime(dbCIInfo.getCi().getCreateTime());
            ci.setCiVersion(dbCIInfo.getCi().getCiVersion());
            if (!CheckAttrUtil.checkAttrMapEqual(stdMap, oldAttrs)) {
                ci.setCiVersion(String.valueOf(Long.parseLong(dbCIInfo.getCi().getCiVersion()) + 1));
            }
            ci.setOwnerCode(dbCIInfo.getCi().getOwnerCode());
        }
        ci.setClassId(classId);
        ci.setDataStatus(1);
        ci.setHashCode(hashCode);
        ci.setCiPrimaryKey(JSON.toString(ciPrimaryKeys));
        Long id = esCiSvc.saveOrUpdateCI(ciInfo);
        if(!isPrivate){
            if (isAdd) {
                this.saveCIOperateLog(null, SysUtil.StaticUtil.LOG_DYNAMIC_INSERT, attrDefs, null, stdMap, ciClass.getClassName(), ci);
            } else {
                this.saveCIOperateLog(null, SysUtil.StaticUtil.LOG_DYNAMIC_UPDATE, attrDefs, oldAttrs, stdMap, ciClass.getClassName(), ci);
            }
        }
        return id;
    }
}