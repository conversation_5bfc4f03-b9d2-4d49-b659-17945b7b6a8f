package com.uinnova.product.eam.model.dmv;

import lombok.Data;

import java.io.Serializable;

/**
 * @description:
 * @author: Lc
 * @create: 2022-04-21 13:51
 */
@Data
public class DiagramNode implements Serializable {

    private String id;

    private Integer width;

    private Integer height;

    private Double x;

    private Double y;

    private String img;

    private String name;

    private Integer zOrder;

    private String image;

    private Long ciId;

    private String ciCode;

    private Long classId;

    private String className;

    // 1：包含对象  2：被包含对象
    private Integer sign;

}
