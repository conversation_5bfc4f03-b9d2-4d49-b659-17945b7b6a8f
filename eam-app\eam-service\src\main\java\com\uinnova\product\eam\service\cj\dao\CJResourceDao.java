package com.uinnova.product.eam.service.cj.dao;

import com.uinnova.product.eam.model.cj.domain.CJResource;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;

@Component
public class CJResourceDao extends AbstractESBaseDao<CJResource,CJResource> {
    @Override
    public String getIndex() {
        return "uino_cj_resource";
    }

    @Override
    public String getType() {
        return getIndex();
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }
}
