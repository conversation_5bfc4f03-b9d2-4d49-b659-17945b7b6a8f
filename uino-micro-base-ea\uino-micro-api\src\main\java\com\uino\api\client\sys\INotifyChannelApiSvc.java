package com.uino.api.client.sys;

import java.util.Collection;
import java.util.List;

import com.uino.bean.sys.base.NotifyChannel;
import com.uino.bean.sys.business.NotifyChannelReqDto;
import com.uino.bean.sys.business.NotifyData;

/**
 * 通知渠道api
 * 
 * <AUTHOR>
 *
 */
public interface INotifyChannelApiSvc {

    /**
     * 保存
     * 
     * @param saveInfo
     * @return
     */
    public NotifyChannel save(NotifyChannel saveInfo);

    /**
     * 根据ids删除
     * 
     * @param ids
     */
    public void delete(Collection<Long> ids);

    /**
     * 查询
     * 
     * @param searchDto
     * @return
     */
    public List<NotifyChannel> search(NotifyChannelReqDto searchDto);

    /**
     * 发送通知
     * 
     * @param notifyData
     * @return
     */
    public boolean sendNotify(NotifyData notifyData);
}
