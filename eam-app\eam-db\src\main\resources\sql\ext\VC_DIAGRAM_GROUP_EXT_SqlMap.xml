<?xml version="1.0" encoding="UTF-8"?><!--Converted at: Sat Dec 08 16:24:26 CST 2018-->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="VC_DIAGRAM_GROUP">
	
	
	<resultMap id="mapCount" type="com.uinnova.product.eam.db.bean.UserDiagramInfoCount">
		<result property="id" column="ID" jdbcType="BIGINT"/>
		<result property="shareCount" column="SHARE_COUNT" jdbcType="BIGINT"/>
		<result property="diagramId" column="DIAGRAM_ID" jdbcType="BIGINT"/>
		<result property="diagramType" column="DIAGRAM_TYPE" jdbcType="BIGINT"/>
	</resultMap>

	<resultMap id="teamDiagrams" type="com.uinnova.product.eam.db.bean.TeamDiagrams">
		<result property="diagramId" column="ID" jdbcType="BIGINT"/>
		<result property="diagramName" column="NAME" jdbcType="VARCHAR"/>
	</resultMap>

	<resultMap id="groupReadCount" type="com.uinnova.product.eam.db.bean.GroupReadCount">
		<result property="groupId" column="GROUP_ID" jdbcType="BIGINT"/>
		<result property="readCount" column="READ_COUNT" jdbcType="BIGINT"/>
	</resultMap>

	<select id="countDiagramByDeployUserId" parameterType="java.util.Map" resultMap="mapCount">
		SELECT result.ID, result.SHARE_COUNT,result.DIAGRAM_ID, result.DIAGRAM_TYPE FROM
			(SELECT a.DEPLOY_USER_ID ID, COUNT(a.id) SHARE_COUNT,c.ID AS DIAGRAM_ID, c.DIAGRAM_TYPE FROM vc_diagram_group a
			INNER JOIN vc_diagram c on c.id = a.DIAGRAM_ID and c.status = 1 and c.data_status = 1
			WHERE a.DOMAIN_ID = #{domainId:BIGINT} and a.DEPLOY_USER_ID in (SELECT ID FROM sys_op WHERE DATA_STATUS = 1)
			<if test="startDate != null ">and
				a.CREATE_TIME &gt;= #{startDate:BIGINT}
			</if>
			<if test="endDate != null ">and
				a.CREATE_TIME &lt;= #{endDate:BIGINT}
			</if>
			GROUP BY c.ID,a.DEPLOY_USER_ID,c.DIAGRAM_TYPE
			) result
	</select>

	<select id="selectAllTeamDiagramByUserId" parameterType="java.util.Map" resultMap="teamDiagrams">
		SELECT a.DIAGRAM_ID AS ID, c.NAME AS NAME FROM vc_diagram_group a
			INNER JOIN VC_GROUP_USER b ON a.GROUP_ID = b.GROUP_ID
			INNER JOIN vc_diagram c ON a.DIAGRAM_ID = c.ID
			WHERE b.USER_ID = #{userId:BIGINT}
	</select>

	<select id="countReadByGroupId" parameterType="java.util.Map" resultMap="groupReadCount">
		select a.GROUP_ID, count(b.id) READ_COUNT from vc_diagram_group a
		LEFT JOIN vc_diagram_operation_record b on a.DIAGRAM_ID = b.DIAGRAM_ID and b.OPERATION_TYPE = 1
		<if test="startDate != null ">and
			b.CREATE_TIME &gt;= #{startDate:BIGINT}
		</if>
		<if test="endDate != null ">and
			b.CREATE_TIME &lt;= #{endDate:BIGINT}
		</if>
		GROUP BY a.GROUP_ID
	</select>
</mapper>