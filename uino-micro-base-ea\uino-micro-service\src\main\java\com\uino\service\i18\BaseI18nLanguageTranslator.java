package com.uino.service.i18;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;

import com.binary.core.i18n.Language;
import com.binary.core.i18n.LanguageResolver;
import com.binary.core.i18n.LanguageTranslator;
import com.binary.core.lang.StringLinker;
import com.binary.core.util.BinaryUtils;
import com.binary.json.JSON;
import com.uinnova.product.devcloud.i18n.client.I18nClient;
import com.uinnova.product.devcloud.i18n.client.trans.LanguageGetter;
import com.uino.I18n;

public class BaseI18nLanguageTranslator implements LanguageTranslator {

    private static final Pattern EXP_REGEX = Pattern.compile(".*[$][\\s]*[{].*");

    /** 默认语言 **/
    private Language defaultLanguage = Language.ZHC;

    /** 获取语言, 如果没有设置则选用默认语言 **/
    private LanguageGetter languageGetter;

    private I18nClient i18nClient;

    public I18nClient getI18nClient() {
        return i18nClient;
    }

    public void setI18nClient(I18nClient i18nClient) {
        this.i18nClient = i18nClient;
    }

    public BaseI18nLanguageTranslator() {
        LanguageResolver.setTranslator(this);
    }

    public Language getDefaultLanguage() {
        return defaultLanguage;
    }

    public void setDefaultLanguage(Language defaultLanguage) {
        if (defaultLanguage == null)
            throw new IllegalArgumentException(" the 'defaultLanguage' is null argument! ");
        this.defaultLanguage = defaultLanguage;
    }

    public LanguageGetter getLanguageGetter() {
        return languageGetter;
    }

    public void setLanguageGetter(LanguageGetter languageGetter) {
        if (languageGetter == null)
            throw new IllegalArgumentException(" the 'languageGetter' is null argument! ");
        this.languageGetter = languageGetter;
    }

    private Language getLanguage() {
        if (this.languageGetter != null) {
            Language lan = this.languageGetter.getLanguage();
            if (lan != null)
                return lan;
        }
        return defaultLanguage;
    }

    @Override
    public String trans(String languageCode) {
        return trans(languageCode, (Object) null);
    }

    @Override
    public String trans(String languageCode, String jsonParams) {
        Map<String, Object> params = null;
        if (!BinaryUtils.isEmpty(jsonParams)) {
            jsonParams = jsonParams.trim();
            if (jsonParams.charAt(0) != '{'
                    || jsonParams.charAt(jsonParams.length() - 1) != '}') { throw new IllegalArgumentException(
                    " the 'jsonParams' is wrong argument! "); }
            params = (Map) JSON.toObject(jsonParams);
        }
        return trans(languageCode, params);
    }

    @Override
    public String trans(String languageCode, Object params) {
        if (languageCode == null || (languageCode = languageCode.trim())
                .length() == 0) { throw new IllegalArgumentException(" the 'languageCode' is empty argument! "); }
        String data = getLanguageValue(Language.ZHC, params, languageCode);
        if (BinaryUtils.isEmpty(data) || !EXP_REGEX.matcher(data).matches()) { return data; }
        StringLinker sl = BinaryUtils.parseExpression(data);
        StringBuilder sb = sl.link(params);
        return sb.toString();
    }

    @Override
    public Map<String, String> getLanguageDictionary() {
        return getLanguageDictionary(null);
    }

    @Override
    public Map<String, String> getLanguageDictionary(Language language) {
        if (language == null)
            language = getLanguage();
        return i18nClient.getLanguagePackage(language);
    }

    @SuppressWarnings("unchecked")
    private String getLanguageValue(Language language, Object param, String code) {
        String data = i18nClient.getLanguageValue(language, code);
        if (data == null || data.equals("")) {
            // 取新国际化平台的国际化
            try {
                if (param != null) {
                    data = I18n.get(code, (HashMap<String, String>) param, languageConvert(language));
                } else {
                    data = I18n.get(code, languageConvert(language));
                }
            } catch (Exception e) {
                data = null;
            }
        }
        return data;
    }

    private I18n.Language languageConvert(Language language) {
        if (Language.ZHC.equals(language)) {
            return I18n.Language.zh;
        } else if (Language.EN.equals(language)) {
            return I18n.Language.en;
        } else {
            return languageConvert(defaultLanguage);
        }
    }
}