package com.uino.dao.cmdb;

import com.uino.bean.cmdb.base.ESVisualModelHistory;
import com.uino.dao.AbstractESBaseDao;
import com.uino.dao.ESConst;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;

/**
 * description
 *
 * <AUTHOR>
 * @since 2025/4/14 10:26
 */
@Service
public class ESVisualModelHistorySvc extends AbstractESBaseDao<ESVisualModelHistory, ESVisualModelHistory> {


    @Override
    public String getIndex() {
        return ESConst.INDEX_CMDB_VISUALMODEL+"_history";
    }

    @Override
    public String getType() {
        return "_doc";
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }
}
