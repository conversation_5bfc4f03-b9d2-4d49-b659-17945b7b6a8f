package com.uinnova.product.eam.service.cj.service;

import com.uinnova.product.eam.model.cj.domain.PlanChapterCollaborate;
import com.uinnova.product.eam.model.cj.vo.CollaborateVO;
import com.uinnova.product.eam.model.cj.vo.PlanChapterCollaborateVO;

import java.util.Map;

public interface PlanChapterCollaborateService {

    /**
     * 新增或修改方案章节协作
     * @param planChapterCollaborateVO
     */
    void saveOrUpdatePlanChapterCollaborate(PlanChapterCollaborateVO planChapterCollaborateVO);

    /**
     * 完成协作
     * @param planChapterCollaborateVO
     */
    Boolean completeCollaborate(PlanChapterCollaborateVO planChapterCollaborateVO);

    /**
     * 查询方案章节是否已完成
     * @param planChapterCollaborateVO
     * @return
     */
    CollaborateVO getUsersAndComplete(PlanChapterCollaborateVO planChapterCollaborateVO);

    /**
     * 获取用户协作信息
     * @param planChapterCollaborate
     * @return
     */
    PlanChapterCollaborate getPlanChapterCollaborate(PlanChapterCollaborate planChapterCollaborate);

    /**
     * 删除方案章节协作用户
     * @param planChapterCollaborateVO
     */
    void deletePlanChapterCollaborateUser(PlanChapterCollaborateVO planChapterCollaborateVO);

    /**
     * 获取方案所有章节协作信息
     * @param planId 方案id
     * @return 章节id->协作信息 映射
     */
    Map<Long, PlanChapterCollaborate> getByPlanId(Long planId);
}
