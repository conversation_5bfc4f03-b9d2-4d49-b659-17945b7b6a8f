package com.uino.bean.permission.base;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * mapping-table: 角色模块关联表[SYS_ROLE_MODULE_RLT]
 * 
 * <AUTHOR>
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
@ApiModel(value="角色模块关联表",description = "角色模块关联表")
public class SysRoleModuleRlt implements Serializable {

    private static final long serialVersionUID = 1L;

    /** ID */
    @ApiModelProperty(value="id",example = "7405537284900747")
    private Long id;

    /** 模块ID */
    @ApiModelProperty(value="模块id",example = "7405537284900747")
    private Long moduleId;

    /** 角色ID */
    @ApiModelProperty(value="角色id",example = "7405537284900747")
    private Long roleId;

    /** 所属域 */
    @ApiModelProperty(value="所属域id",example = "7405537284900747")
    private Long domainId;

    /** 创建时间 */
    @ApiModelProperty(value="创建时间")
    private Long createTime;

    /** 修改时间 */
    @ApiModelProperty(value="修改时间")
    private Long modifyTime;
}
