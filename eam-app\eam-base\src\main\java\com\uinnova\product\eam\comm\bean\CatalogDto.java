package com.uinnova.product.eam.comm.bean;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Classname CatalogDto
 * @Date 2022/7/1 12:36
 */
@Data
public class CatalogDto implements Serializable {
    private static final long serialVersionUID = 1905122041950251207L;

    @Comment("配置页id")
    private Long configId;

    @Comment("菜单id")
    private Long catalogId;

    @Comment("菜单名称")
    private String catalogName;

    @Comment("视图id/数据集id")
    private String diagramId;

    @Comment("数据集id")
    private boolean dataSet;

    @Comment("自动成图CI排序属性配置，JSON")
    private String ciSortConfig;

    @Comment("容器数量配置")
    private String ciNumConfig;
}
