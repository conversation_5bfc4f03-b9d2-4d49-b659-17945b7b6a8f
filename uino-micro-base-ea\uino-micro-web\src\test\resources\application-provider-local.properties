# ---------------------------------------\u57FA\u7840\u914D\u7F6E start---------------------------
# \u57FA\u7840\u6A21\u5757\u670D\u52A1\u52A0\u8F7D\u65B9\u5F0F\uFF0C\u652F\u6301local/rpc
base.load-type=local
#??????
spring.main.allow-circular-references=true
# \u7AEF\u53E3
server.port=1536
# \u670D\u52A1\u540D\u79F0
spring.application.name=common-web
# \u670D\u52A1\u524D\u7F6E\u5730\u5740
server.servlet.context-path=/tarsier-comm
# \u6743\u9650\u6A21\u5757http server path
permission.http.prefix=http://**************/tarsier-comm
# \u672C\u5730\u8D44\u6E90http\u670D\u52A1\u5730\u5740
http.resource.space=http://**************/rsm
#\u5404\u8282\u70B9\u4E4B\u95F4\u540C\u6B65\u8D44\u6E90\u8BBF\u95EE\u7684url\uFF08\u5F53\u524D\u670D\u52A1\u5730\u5740\uFF09
http.resource.sync=http://*************:1536/tarsier-comm
#\u9759\u6001\u8D44\u6E90\u8DEF\u5F84
spring.resources.static-locations=file:/home/<USER>/rsm
# \u672C\u5730\u8D44\u6E90\u5B58\u50A8\u5730\u5740
local.resource.space=/usr/local/uino_data
# ---------------------------------------\u57FA\u7840\u914D\u7F6E end----------------------------

#-------------------------------------------------------------------------------------------------
#redis
# Redis\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\u05B7
spring.data.redis.host=**************
# Redis\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\u04F6\u02FF\uFFFD
spring.data.redis.port=6379
#spring.data.redis.cluster.nodes=*************:6379,*************:6380,**************:6379,**************:6380,**************:6379,**************:6380
# Redis\uFFFD\uFFFD\uFFFD\u077F\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\u012C\uFFFD\uFFFD\u03AA0\uFFFD\uFFFD
spring.data.redis.database=0
# Redis\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uB8E8\u012C\uFFFD\uFFFD\u03AA\uFFFD\u0563\uFFFD
#spring.data.redis.password=Uinnova@123
#-------------------------------------------------------------------------------------------------
# -----------------------------------\u8BA4\u8BC1\u76F8\u5173\u914D\u7F6E start---------------------------
# \u662F\u5426\u5F00\u542Ftrue/false
oauth.open=true
# oauth\u5BA2\u6237\u7AEFid/\u8D44\u6E90\u7AEFid
oauth.client.id=tarsier-comm
# \u5BA2\u6237\u7AEF\u5BC6\u94A5
oauth.client.secret=tarsier-comm
# \u8BF7\u6C42\u6388\u6743\u65B9\u5F0F
oauth.client.grant_type=authorization_code
# \u8BA4\u8BC1\u4E2D\u5FC3\u5730\u5740
oauth.server.url=http://**************/oauth
# \u8BA4\u8BC1\u4E2D\u5FC3\u5185\u7F51\u5730\u5740
oauth.server.in_url=http://**************/oauth
# code\u6362token\u56DE\u8C03\u5730\u5740
oauth.server.token_callback.url=http://**************/tarsier-comm/getTokenByCode
# -----------------------------------\u8BA4\u8BC1\u76F8\u5173\u914D\u7F6E end----------------------------


# -----------------------------------ES\u76F8\u5173\u914D\u7F6E start----------------------------
esIps=**************:9200;
isAuth=true
esUser=uinnova
esPwd=Uinnova@123
# -----------------------------------ES\u76F8\u5173\u914D\u7F6E end------------------------------


# -----------------------------------\u529F\u80FD\u6027\u914D\u7F6E(\u9ED8\u8BA4\u65E0\u987B\u4FEE\u6539) start----------------
# \u521B\u5EFA\u5206\u7C7B\u662F\u5426\u663E\u793A3D\u6A21\u578B\u5C5E\u6027
is.show.3d.attribute=false

# \u521B\u5EFA\u5206\u7C7B\u662F\u5426\u663E\u793A\u6587\u6863\u5C5E\u6027
is.show.document.attribute=false

# \u5BF9\u8C61\u7BA1\u7406\u52FE\u9009\u4E1A\u52A1\u4E3B\u952E\u6570\u91CF\u9650\u5236
uino.base.ci.primarykey.maxcount=5

# \u5173\u7CFB\u904D\u5386\u6279\u5904\u7406
batch.process.relation.rule.cron = 0 0 */1 * * ?


# \u662F\u5426\u6709ep\u6A21\u5757(\u65E0ep\u6A21\u62DF\u544A\u8B66\u76F4\u63A5\u5165\u5E93)
uino.monitor.ep.exist=false
# \u6A21\u62DF\u544A\u8B66\u53D1\u9001\u8C03\u7528\u7684Dix\u63D0\u4F9B\u7684\u53D1\u9001\u544A\u8B66\u670D\u52A1\u7684URL
uino.monitor.event.send.url=http://0.0.0.0:0000/http/rest/event/mq/import


# \u662F\u5426\u52A0\u8F7Dtp\u6A21\u5757(\u65E0tp\u6A21\u62DF\u6027\u80FD\u76F4\u63A5\u5165\u5E93)
base.load-tp=false
# dix\u63A5\u6536\u6027\u80FD\u6570\u636E\u63A5\u53E3
base.perf.dix-url=http://0.0.0.0:0000/http/rest/tp/perf
kpi.units=\u5EA6,\u65A4


# \u5FFD\u7565\u6388\u6743\u8BA4\u8BC1\u7684\u5730\u5740
license.ignore.filter.pattern=**.mp3;**.mp4;**.wav;**.js;**.css;**.jpg;**.jpeg;**.bmp;**.gif;**.png;**.ico;**.swf;**.eot;**.svg;**.ttf;**.woff;**.woff2;**.htm;**.html;**.txt;**.xml;**.json;**.map;/license/auth/**;/redirectAuth;/getTokenByCode;/permission/user/getCurrentUser;/permission/module/getModuleTree;/sys/getLogos;/sys/tenantDomain/multiTenantStatus;/**;
# \u6388\u6743\u8DF3\u8F6C\u5730\u5740
project.license.register.url=http://0.0.0.0:0000/examples/#/license
#\u666E\u901A\u7528\u6237\u6388\u6743\u5931\u8D25\u540E\u8DF3\u8F6C\u9875\u9762\uFF0C\u9700\u8981\u5728\u591A\u79DF\u6237\u4E0B\u751F\u6548
project.license.ordinary.register.url=http://0.0.0.0:0000/examples/#/license-due
# -----------------------------------\u529F\u80FD\u6027\u914D\u7F6E(\u9ED8\u8BA4\u65E0\u987B\u4FEE\u6539) end-------------------


# \u9ED8\u8BA4\u914D\u7F6E\u9879, \u65E0\u7279\u5B9A\u9700\u6C42\u65E0\u987B\u4FEE\u6539
spring.servlet.multipart.max-file-size=500MB
spring.servlet.multipart.max-request-size=500MB
server.tomcat.basedir=.
spring.mvc.async.request-timeout=1000000
#\u65E5\u5FD7\u4FDD\u7559\u65F6\u957F\uFF0C\u5355\u4F4D\uFF1A\u5929
uino.log.clear.duration=7
#\u53D7\u4FE1\u4EFB\u7684\u7684Referer\u5217\u8868\uFF0C\u591A\u4E2A\u7528;\u9694\u5F00\uFF0C\u4E3A\u7A7A\u4EE3\u8868\u4E0D\u505A\u8BE5\u5B89\u5168\u9A8C\u8BC1
safe.trust.referers=
# \u8986\u76D6\u540C\u540Dbean
spring.main.allow-bean-definition-overriding=true
spring.main.lazy-initialization=true

#\u6027\u80FD\u544A\u8B66\u6570\u636E\u7EA6\u675F\u662F\u5426\u5305\u542BdomainId
monitor.select.include.domainId=true

#\u591A\u79DF\u6237\u57DF
uino.tenantDomain=false

#\u6A21\u62DF\u6570\u636E\u5B9A\u65F6\u4EFB\u52A1\u5F00\u5173
base.simulation.rule.auto.excute= false


spring.cloud.obs.endpoint=obs.cn-north-4.myhuaweicloud.com
spring.cloud.obs.access-key=IUSWCJWLVTIRKDQR9RJW
spring.cloud.obs.secret-key=HiZQUzYi6wDCeN45yVW9h3gowrVTazwS0LYHKxVt

spring.cloud.obs.bucketName=quickea
spring.cloud.obs.urlExpireSeconds=3600
spring.cloud.obs.region=cn-beijing
spring.cloud.obs.isHttps=N
rsm.util.sdkType=xinwang
obs.use=false


# \u662F\u5426\u5F00\u542F\u914D\u7F6E\u4E2D\u5FC3
spring.cloud.nacos.config.enabled=false
# \u662F\u5426\u5F00\u542F\u670D\u52A1\u6CE8\u518C\u8C03\u7528
spring.cloud.nacos.discovery.enabled=false
# \u6CE8\u518C\u4E2D\u5FC3\u5730\u5740
spring.cloud.nacos.config.server-addr=192.168.21.223:8848
# \u662F\u5426\u5F00\u542F\u6D41\u91CF\u76D1\u63A7
spring.cloud.sentinel.enabled=false