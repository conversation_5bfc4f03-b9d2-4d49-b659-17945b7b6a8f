package com.uinnova.product.eam.comm.model.es;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

/**
 * 矩阵制品属性保存对象
 */
@Data
public class EamMatrixStencilAttr {

    @Comment("属性定义名称")
    private String name;

    @Comment("属性定义类型")
    private Integer type;

    @Comment("外部属性[关联资产18/关联属性11/数据字典8字段用值]")
    private String proDrop;

    @Comment("约束规则[计算16]")
    private String rule;

    public EamMatrixStencilAttr(String name, Integer type, String proDrop, String rule) {
        this.name = name;
        this.type = type;
        this.proDrop = proDrop;
        this.rule = rule;
    }
}
