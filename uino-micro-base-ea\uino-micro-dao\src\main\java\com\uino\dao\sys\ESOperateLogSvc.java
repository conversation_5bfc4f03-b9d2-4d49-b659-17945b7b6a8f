package com.uino.dao.sys;

import jakarta.annotation.PostConstruct;

import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.uino.dao.AbstractESBaseDao;
import com.uino.dao.ESConst;
import com.uino.bean.sys.base.ESOperateLog;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
public class ESOperateLogSvc extends AbstractESBaseDao<ESOperateLog, JSONObject> {

    @Override
    public String getIndex() {
        return ESConst.INDEX_OPERATE_LOG;
    }

    @Override
    public String getType() {
        return ESConst.INDEX_OPERATE_LOG;
    }

    @PostConstruct
    public void init() {
        super.initIndex(5);
    }
}
