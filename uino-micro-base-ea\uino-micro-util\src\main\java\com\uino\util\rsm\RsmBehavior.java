package com.uino.util.rsm;

import java.io.File;
import java.io.InputStream;

/**
 * 封装对象存储相关的行为
 */
public interface RsmBehavior {

    /**
     * 初始化client方法
     */
    public void initClient();

    /**
     * 关闭Client方法
     */
    public void closeClient();

    /**
     * 上传资源文件-通过本地文件进行上传
     * @param objectKey
     * @param file
     * @return
     */
    public boolean uploadRsm(String objectKey, File file);

    /**
     * 下载资源
     * @param objectKey
     * @return
     */
    public InputStream downloadRsm(String objectKey);

    /**
     * 获得资源的临时访问链接
     * @param objectKey
     * @return
     */
    public String getRsmUrlWithAccess(String objectKey);

    /**
     * 删除资源
     * @param objectKey
     * @return
     */
    public boolean deleteRsm(String objectKey);

}
