package com.uinnova.product.eam.model;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.comm.diagram.CheckType;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.base.ESCIRltInfo;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 视图元素更新结果
 * <AUTHOR>
 * @date 2021/10/29
 */
@Data
public class EamElementUpdateInfo {

    public EamElementUpdateInfo(String key, CheckType state) {
        this.key = key;
        this.state = state;
    }

    @Comment("视图节点key")
    private String key;

    @Comment("映射设计库code")
    private String designCode;
    @Comment("视图节点更新结果")
    private CheckType state;

    @Comment("ci信息")
    private ESCIInfo ciInfo;

    @Comment("rlt信息")
    private ESCIRltInfo rltInfo;

    @Comment("实体属性更新信息(只存在于实体节点)")
    private List<EamElementUpdateInfo> attrUpdateInfo = new ArrayList<>();
}
