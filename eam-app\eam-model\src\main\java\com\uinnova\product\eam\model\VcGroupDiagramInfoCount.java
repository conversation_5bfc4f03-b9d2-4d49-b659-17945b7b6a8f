package com.uinnova.product.eam.model;

import com.binary.framework.bean.annotation.Comment;

public class VcGroupDiagramInfoCount {
	
	@Comment("小组id")
	private Long id;

	@Comment("小组名称")
	private String groupName;
	
	@Comment("小组视图数")
	private Integer diagramCount;
	
	@Comment("小组视图被查看总数")
	private Long diagramReadCount;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getGroupName() {
		return groupName;
	}

	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}

	public Integer getDiagramCount() {
		return diagramCount;
	}

	public void setDiagramCount(Integer diagramCount) {
		this.diagramCount = diagramCount;
	}

	public Long getDiagramReadCount() {
		return diagramReadCount;
	}

	public void setDiagramReadCount(Long diagramReadCount) {
		this.diagramReadCount = diagramReadCount;
	}
	
	
}
