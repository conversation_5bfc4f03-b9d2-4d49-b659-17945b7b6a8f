package com.uinnova.product.eam.model;

import com.uinnova.product.vmdb.comm.model.ci.CcCiClass;
import com.uino.bean.cmdb.base.ESCIClassInfo;
import lombok.Data;

import java.util.List;

/**
 * description
 *
 * <AUTHOR>
 * @since 2024/11/6 16:32
 */
@Data
public class FlowWholeSceneCountFlowProcessLevelVo {

    private String key;

    private String className;

    private CcCiClass ciClass;

    private Integer count;

    private List<FlowWholeSceneCountFlowProcessCiVo> children;

}
