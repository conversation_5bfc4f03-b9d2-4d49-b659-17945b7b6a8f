package com.uinnova.product.eam.model.vo;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.comm.model.es.EamDiagramDir;
import lombok.Data;

@Data
public class EamDiagramDirVo extends EamDiagramDir {

    @Comment("所在文件夹")
    private String relationLocation;
    @Comment("所属系统")
    private String relationSystem;
    @Comment("所属系统分类")
    private String SystemType;
    @Comment("是否已关注 0：未关注  1：已关注")
    private Integer isAttention;
    @Comment("所属系统标识")
    private Integer relationSysSign;
    @Comment("用户名")
    private String userName;
}
