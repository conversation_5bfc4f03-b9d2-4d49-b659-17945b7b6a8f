package com.uinnova.product.eam.model.cj.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * @description:
 * @author: Lc
 * @create: 2022-03-02 14:04
 */
@Getter
public enum QuestionStatusEnum {

    NOT_RECTIFIED(0, "未整改"),
    IS_RECTIFIED(1, "已整改"),
    NO_REQUIRED_RECTIFIED(2, "无需整改"),
    EXCEPTION_QUESTION(3, "例外问题");

    private Integer state;

    private String desc;

    QuestionStatusEnum(Integer state, String desc) {
        this.state = state;
        this.desc = desc;
    }

    public static String getQuestionDesc(Integer state) {
        for (QuestionStatusEnum statusEnum : QuestionStatusEnum.values()) {
            if (Objects.equals(state, statusEnum.getState())) {
                return statusEnum.desc;
            }
        }
        return null;
    }
}
