//package com.uinnova.product.eam.web.xinwang.cas;
//
//import lombok.extern.slf4j.Slf4j;
//import org.jasig.cas.client.authentication.AuthenticationFilter;
//import org.jasig.cas.client.configuration.ConfigurationKeys;
//import org.jasig.cas.client.session.SingleSignOutFilter;
//import org.jasig.cas.client.session.SingleSignOutHttpSessionListener;
//import org.jasig.cas.client.util.HttpServletRequestWrapperFilter;
//import org.jasig.cas.client.validation.Cas30ProxyReceivingTicketValidationFilter;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
//import org.springframework.boot.context.properties.EnableConfigurationProperties;
//import org.springframework.boot.web.servlet.FilterRegistrationBean;
//import org.springframework.boot.web.servlet.ServletListenerRegistrationBean;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//
//import jakarta.servlet.http.HttpSessionListener;
//
///**
// * description
// *
// * <AUTHOR>
// * @since 2022/12/29 18:45
// */
//@Slf4j
//@Configuration
//@ConditionalOnProperty(prefix = "monet.login", name = "loginMethod", havingValue = "xinwang")
//@EnableConfigurationProperties(CasConfigurationProperties.class)
//public class CasConfiguration {
//    @Autowired
//    private CasConfigurationProperties autoconfig;
//
//    {
//        log.info("开启Cas sso");
//    }
//
//    @Bean
//    public ServletListenerRegistrationBean<HttpSessionListener>
//    httpSessionListener() {
//
//        ServletListenerRegistrationBean<HttpSessionListener> listener = new ServletListenerRegistrationBean<>();
//        listener.setListener(new SingleSignOutHttpSessionListener());
//        return listener;
//    }
//
//    /**
//     * 该过滤器用于实现单点登出功能，单点退出配置，一定要放在其他filter之前
//     */
//    @Bean
//    public FilterRegistrationBean singleSignOutFilter() {
//        FilterRegistrationBean
//                filterRegistration = new FilterRegistrationBean();
//        filterRegistration.setFilter(new SingleSignOutFilter());
//        filterRegistration.addUrlPatterns("/*");
//
//        filterRegistration.addInitParameter(ConfigurationKeys.CAS_SERVER_URL_PREFIX.getName(), autoconfig.getCasServerUrlPrefix());
//        filterRegistration.addInitParameter(ConfigurationKeys.SERVER_NAME.getName(), autoconfig.getServerName());
//        filterRegistration.setOrder(1);
//        return filterRegistration;
//    }
//
//    /**
//     * 该过滤器负责用户的认证工作
//     */
//    @Bean
//    public FilterRegistrationBean authenticationFilter() {
//        FilterRegistrationBean
//                filterRegistration = new FilterRegistrationBean();
//        filterRegistration.setFilter(new AuthenticationFilter());
//        filterRegistration.addUrlPatterns("/*");
//
//        filterRegistration.addInitParameter(ConfigurationKeys.CAS_SERVER_LOGIN_URL.getName(), autoconfig.getCasServerLoginUrl());
//        filterRegistration.addInitParameter(ConfigurationKeys.SERVER_NAME.getName(), autoconfig.getServerName());
//        filterRegistration.addInitParameter(ConfigurationKeys.IGNORE_URL_PATTERN_TYPE.getName(), autoconfig.getIgnoreUrlPatternType());
//        filterRegistration.addInitParameter(ConfigurationKeys.IGNORE_PATTERN.getName(), autoconfig.getIgnorePattern());
//        filterRegistration.addInitParameter(ConfigurationKeys.AUTHENTICATION_REDIRECT_STRATEGY_CLASS.getName(), ManageAuthenticationRedirectstrategy.class.getName());
//        filterRegistration.setOrder(2);
//        return filterRegistration;
//    }
//
//    /**
//     * 该过滤器负责对Ticket的校验工作
//     */
//    @Bean
//    public FilterRegistrationBean cas30ProxyReceivingTicketValidationFilter() {
//        FilterRegistrationBean
//                filterRegistration = new FilterRegistrationBean();
//        filterRegistration.setFilter(new Cas30ProxyReceivingTicketValidationFilter());
//        filterRegistration.addUrlPatterns("/*");
//        filterRegistration.addInitParameter(ConfigurationKeys.CAS_SERVER_URL_PREFIX.getName(), autoconfig.getCasServerUrlPrefix());
//        filterRegistration.addInitParameter(ConfigurationKeys.SERVER_NAME.getName(), autoconfig.getServerName());
//        filterRegistration.addInitParameter(ConfigurationKeys.REDIRECT_AFTER_VALIDATION.getName(), String.valueOf(autoconfig.isRedirectAfterValidation()));
//        filterRegistration.addInitParameter(ConfigurationKeys.USE_SESSION.getName(), String.valueOf(autoconfig.isUseSession()));
//        filterRegistration.addInitParameter(ConfigurationKeys.ENCODING.getName(), autoconfig.getEncoding());
//        filterRegistration.setOrder(3);
//        return filterRegistration;
//    }
//
//    /**
//     * 该过滤器对HttpServletRequest请求包装，
//     * 可通过HttpServletRequest的getRemoteUser()方法获得登录用户的登录名
//     */
//    @Bean
//    public FilterRegistrationBean httpServletRequestWrapperFilter() {
//        FilterRegistrationBean filterRegistration = new FilterRegistrationBean();
//        filterRegistration.setFilter(new HttpServletRequestWrapperFilter());
//        filterRegistration.addUrlPatterns("/*");
//        filterRegistration.setOrder(4);
//        return filterRegistration;
//    }
//}
//
