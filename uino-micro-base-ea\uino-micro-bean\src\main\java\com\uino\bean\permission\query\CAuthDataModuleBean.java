package com.uino.bean.permission.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;


@Getter
@Setter
public class CAuthDataModuleBean{

    @ApiModelProperty(value="用户id")
    private Long userId;

    @ApiModelProperty(value="数据模块码, 对应 ci classId")
    private List<Long> moduleIds;

    @ApiModelProperty(value="所属域id")
    private Long domainId;
}
