package com.uino.web.sys.mvc;

import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uinnova.product.vmdb.comm.doc.annotation.MvcDesc;
import com.uino.api.client.sys.IOperateLogApiSvc;
import com.uino.api.client.sys.IOperateLogModuleApiSvc;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.sys.base.ESOperateLog;
import com.uino.bean.sys.base.ESOperateLogModule;
import com.uino.bean.sys.query.ESOperateLogSearchBean;
import com.uino.init.api.ApiResult;
import com.uino.init.api.ApiVersion;
import com.uino.util.log.OperateLogModuleInfo;
import com.uino.util.sys.SysUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.List;

@ApiVersion(1)
@Api(value = "日志管理-操作日志", tags = {"日志管理"})
@RestController
@RequestMapping("/sys/opLog")
@MvcDesc(author = "zmj", desc = "日志管理-操作日志")
public class SysOperateLogMvc {

    @Autowired
    IOperateLogApiSvc logSvc;

    @Autowired
    IOperateLogModuleApiSvc logModuleSvc;

    @ApiOperation("条件查询操作日志")
    @RequestMapping(value="/searchPageByCdt",method = RequestMethod.POST)
    @ModDesc(desc = "条件查询操作日志", pDesc = "查询条件", pType = ESOperateLogSearchBean.class, rDesc = "查询结果", rType = Page.class, rcType = ESOperateLog.class)
    public ApiResult<Page<ESOperateLog>> searchPageByCdt(HttpServletRequest request, HttpServletResponse response, @RequestBody ESOperateLogSearchBean bean) {
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        bean.setDomainId(currentUserInfo.getDomainId());
        Page<ESOperateLog> rs = logSvc.getOperateLogPageByCdt(bean);
        return ApiResult.ok(this).data(rs);
    }

    @ApiOperation("查询操作日志模块")
    @RequestMapping(value="/getModules",method = RequestMethod.POST)
    @ModDesc(desc = "查询操作日志模块", pDesc = "", rDesc = "查询结果", rType = List.class, rcType = OperateLogModuleInfo.class)
    public ApiResult<List<ESOperateLogModule>> getModules() {
        return ApiResult.ok(this).data(logModuleSvc.getAll());
    }
}
