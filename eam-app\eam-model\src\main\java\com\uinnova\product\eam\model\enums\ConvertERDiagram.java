package com.uinnova.product.eam.model.enums;

/**
 * <AUTHOR>
 */

public enum ConvertERDiagram {

    COVER(1,"覆盖"),
    INCREMENT(2,"增量");

    private final String cardName;
    private final int i;

    ConvertERDiagram(int i, String cardName) {
        this.i = i;
        this.cardName = cardName;
    }

    public String getCardName() {
        return cardName;
    }

    public int getI() {
        return i;
    }
}
