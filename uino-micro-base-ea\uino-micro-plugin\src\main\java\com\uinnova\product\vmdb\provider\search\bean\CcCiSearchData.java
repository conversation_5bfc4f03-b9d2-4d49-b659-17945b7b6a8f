package com.uinnova.product.vmdb.provider.search.bean;

import com.binary.framework.bean.annotation.Comment;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

public class CcCiSearchData implements Serializable {
	private static final long serialVersionUID = 1L;

	
	@Comment("CI记录")
	private List<CcCiObj> records;
	
	
	@Comment("CI分类集合, key=classId")
	private Map<Long, CcCiClassObj> classMp;
	
	
	@Comment("统计满足条件的所有数据总数")
	private Long totalCount;
	
	@Comment("是否有下一页, 0=无    1=有")
	private Integer hasNext;
	
	@Comment("查询条件名称")
	private String name;

	public List<CcCiObj> getRecords() {
		return records;
	}


	public void setRecords(List<CcCiObj> records) {
		this.records = records;
	}


	public Map<Long, CcCiClassObj> getClassMp() {
		return classMp;
	}


	public void setClassMp(Map<Long, CcCiClassObj> classMp) {
		this.classMp = classMp;
	}


	public Long getTotalCount() {
		return totalCount;
	}


	public void setTotalCount(Long totalCount) {
		this.totalCount = totalCount;
	}


	public Integer getHasNext() {
		return hasNext;
	}


	public void setHasNext(Integer hasNext) {
		this.hasNext = hasNext;
	}


	public String getName() {
		return name;
	}


	public void setName(String name) {
		this.name = name;
	}


	
	
	
}
