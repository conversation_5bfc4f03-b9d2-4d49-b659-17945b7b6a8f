 package com.uino.bean.cmdb.query;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import lombok.Getter;
import lombok.Setter;

/**
 * 
 * <AUTHOR>
 *
 */
@Getter
@Setter
public class ESOrRuleItem implements Serializable {

    /**
     *
     */
    private static final long serialVersionUID = 2985853832416158790L;

    /**
     * attrRules 用户选择的属性和值组成的且关系集合
     */
    private List<ESAndAttrRule> attrRules = new ArrayList<ESAndAttrRule>();
}
