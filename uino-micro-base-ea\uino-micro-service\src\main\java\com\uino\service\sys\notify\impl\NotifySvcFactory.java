package com.uino.service.sys.notify.impl;


import java.util.Objects;

import org.springframework.util.Assert;

import com.uino.service.sys.notify.INotifySvc;
import com.uino.util.sys.SpringUtil;
import com.uino.bean.sys.business.NotifyData;

/**
 * 通知类工厂
 * @author: weixuesong
 * @create: 2020/07/02 11:04
 **/
public class NotifySvcFactory {

    /**
     * 通过通知类型返回相应bean
     * @author: weixuesong
     * @date: 2020/7/2 11:51
     * @param notifyType
     * @return: com.uino.middleplatform.es.notify.NotifySvc
     */
    public static INotifySvc getByType(String notifyType) {
        Objects.requireNonNull(notifyType);
        INotifySvc notifySvc = SpringUtil.getBean(notifyType, INotifySvc.class);
        Assert.notNull(notifySvc, "不支持的渠道类型");
        return notifySvc;
    }

    /**
     * 直接发送
     * @author: weixuesong
     * @date: 2020/7/2 11:52
     * @param notifyData
     * @return: boolean
     */
    public static boolean sendNotify(NotifyData notifyData) {
        Objects.requireNonNull(notifyData);
        Objects.requireNonNull(notifyData.getType());
        return getByType(notifyData.getType()).sendNotify(notifyData);
    }

}
