package com.uinnova.product.eam.web.bm.mvc;

import com.alibaba.fastjson.JSONObject;
import com.binary.framework.web.RemoteResult;
import com.binary.jdbc.Page;
import com.uinnova.product.eam.base.enums.ResultCodeEnum;
import com.uinnova.product.eam.comm.model.es.EamMultiModelHierarchy;
import com.uinnova.product.eam.comm.model.es.EamMultiModelType;
import com.uinnova.product.eam.model.dto.EamMultiModelHierarchyDto;
import com.uinnova.product.eam.service.exception.BusinessException;
import com.uinnova.product.eam.web.bm.peer.BmMultiModelHierarchyPeer;
import com.uino.api.client.permission.IUserApiSvc;
import com.uino.web.auth.VerifyAuthUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 多模型层级
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/bm/multiModel")
public class BmMultiModelHierarchyMvc {

    public static final String SYS_MODULE_SIGN = "建模工艺管理";
    @Resource
    BmMultiModelHierarchyPeer modelHierarchyPeer;

    @Autowired
    private VerifyAuthUtil verifyAuth;

    @PostMapping("/queryList")
    public RemoteResult queryList(@RequestBody JSONObject query) {
        verifyAuth.verifyAuth(SYS_MODULE_SIGN);
        //是否发布   0=未发布（默认），1=已发布
        Integer releaseState = query.getInteger("releaseState");
        Integer modelType = query.getInteger("modelType");
        String like = query.getString("like");
        Integer pageNum = query.getInteger("pageNum");
        Integer pageSize = query.getInteger("pageSize");
        Page<EamMultiModelHierarchy> page = modelHierarchyPeer.queryList(pageNum, pageSize, like,releaseState, modelType);
        return new RemoteResult(page);
    }

    @PostMapping("saveOrUpdate")
    public RemoteResult saveOrUpdate(@RequestBody EamMultiModelHierarchyDto multiModelHierarchy) {
        verifyAuth.verifyAuth(SYS_MODULE_SIGN);
        Long id = modelHierarchyPeer.saveOrUpdate(multiModelHierarchy);
        return new RemoteResult(id);
    }

    @GetMapping("queryModelTypeList")
    public RemoteResult queryModelTypeList() {
        verifyAuth.verifyAuth(SYS_MODULE_SIGN);
        Collection<EamMultiModelType> list = modelHierarchyPeer.queryModelTypeList();
        return new RemoteResult(list);
    }

    @PostMapping("saveModelType")
    public RemoteResult saveModelType(@RequestBody List<EamMultiModelType> modelTypeList) {
        verifyAuth.verifyAuth(SYS_MODULE_SIGN);
        Integer id = modelHierarchyPeer.saveModelType(modelTypeList);
        return new RemoteResult(id);
    }

    @GetMapping("deleteModelType")
    public RemoteResult deleteModeType(@RequestParam Long id) {
        verifyAuth.verifyAuth(SYS_MODULE_SIGN);
        Integer flag = modelHierarchyPeer.deleteModelType(id);
        return new RemoteResult(flag);
    }

    @GetMapping("releaseModel")
    public RemoteResult releaseMode(@RequestParam Long modelId) {
        verifyAuth.verifyAuth(SYS_MODULE_SIGN);
        Long id = modelHierarchyPeer.releaseModel(modelId);
        return new RemoteResult(id);
    }

    @GetMapping("cancelReleaseModel")
    public RemoteResult cancelReleaseMode(@RequestParam Long modelId) {
        verifyAuth.verifyAuth(SYS_MODULE_SIGN);
        Long id = modelHierarchyPeer.cancelReleaseModel(modelId);
        return new RemoteResult(id);
    }

    @GetMapping("deleteModel")
    public RemoteResult deleteModel(@RequestParam Long modelId) {
        verifyAuth.verifyAuth(SYS_MODULE_SIGN);
        Integer flag = modelHierarchyPeer.deleteModel(modelId);
        return new RemoteResult(flag);
    }

    @GetMapping("getModelById")
    public RemoteResult getModelById(@RequestParam Long id) {
        verifyAuth.verifyAuth(SYS_MODULE_SIGN);
        EamMultiModelHierarchy modelHierarchy = modelHierarchyPeer.getModelById(id);
        return new RemoteResult(modelHierarchy);
    }

    @GetMapping("copeModel")
    public RemoteResult copeModel(@RequestParam Long id) {
        verifyAuth.verifyAuth(SYS_MODULE_SIGN);
        Integer result = modelHierarchyPeer.copeModel(id);
        return new RemoteResult(result);
    }

    @GetMapping("flashModuleData")
    public RemoteResult flashModuleData() {
        return new RemoteResult(modelHierarchyPeer.flashModuleData());
    }

    @PostMapping("uploadPictures")
    public RemoteResult uploadPictures(@RequestParam(name = "file") MultipartFile file) {
        boolean matches = false;
        try {
            matches = file.getOriginalFilename().matches(".*[.](?i)(jpg|jpeg|png|bmp)");
        } catch (Exception e) {
            throw new BusinessException("上传的图片名称不能为空");
        }
        if (!matches) {
            throw new BusinessException(ResultCodeEnum.FILE_VALID_ERROR);
        }
        modelHierarchyPeer.uploadPictures(file);
        return new RemoteResult("上传成功");
    }

    @GetMapping("pictureList")
    public RemoteResult pictureList() {
        Map<String, Object> result = modelHierarchyPeer.pictureList();
        return new RemoteResult(result);
    }

    @GetMapping("removePicture")
    public RemoteResult removePicture(String name) {
        modelHierarchyPeer.removePicture(name);
        return new RemoteResult("");
    }
}
