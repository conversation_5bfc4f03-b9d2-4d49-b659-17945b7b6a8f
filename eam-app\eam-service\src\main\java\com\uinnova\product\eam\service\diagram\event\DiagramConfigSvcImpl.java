package com.uinnova.product.eam.service.diagram.event;

import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClass;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.base.LibType;
import com.uino.bean.cmdb.query.ESCISearchBean;
import com.uino.service.cmdb.microservice.ICISvc;
import com.uino.service.cmdb.microservice.impl.CIClassSvc;
import com.uino.util.sys.LibTypeUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class DiagramConfigSvcImpl implements DiagramConfigSvc {

    @Autowired
    CIClassSvc clsSvc;

    @Autowired
    ICISvc ciSvc;

    @Override
    public String findConfigType(AttrBeanSvc svc) {
        CCcCiClass cCcCiClass = new CCcCiClass();
        cCcCiClass.setClassNameEqual("视图配置");
        List<CcCiClassInfo> ccCiClassInfos = clsSvc.queryClassByCdt(cCcCiClass);
        Long classId = ccCiClassInfos.get(0).getCiClass().getId();
        ESCISearchBean bean = new ESCISearchBean();
        bean.setPageNum(1);
        bean.setPageSize(1);
        bean.setClassIds(Collections.singletonList(classId));
        bean.setAndAttrs(svc.get());
        Page<ESCIInfo> page = LibTypeUtil.execute(()-> ciSvc.searchESCIByBean(bean),LibType.DESIGN);
        if (!CollectionUtils.isEmpty(page.getData())) {
            return page.getData().get(0).getAttrs().get("CONF_JSON").toString();
        }
        return "";
    }

}
