package com.uinnova.product.eam.model.dto;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.io.Serializable;

/**
 * 模型导航条返回体
 * <AUTHOR>
 */
@Data
public class ModelNavigateDTO implements Serializable {
    @Comment("目录id")
    private Long dirCode;
    @Comment("目录名称")
    private String dirName;
    @Comment("目录绑定的ciCode")
    private String assetCode;
    @Comment("目录绑定的ciId")
    private Long attachCiId;
    @Comment("目录绑定的流程视图id")
    private String diagramId;
    @Comment("分类名称")
    private String className;
    @Comment("父目录id")
    private Long parentDirCode;
    @Comment("目录层级")
    private Integer dirLvl;
    @Comment("子级")
    private ModelNavigateDTO child;
}
