package com.uinnova.product.eam.comm.model.es;

import com.alibaba.fastjson.JSONArray;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.Map;

/**
 * description
 *
 * <AUTHOR>
 * @since 2024/5/28 13:46
 */
@Getter
@Setter
@RequiredArgsConstructor
@ToString
public class FlowSystemApproveData {

    private Long id;

    private String ciCode;

    private String processInstanceId;

    private String diagramEnergy;

    private String integrationDiagramId;

    private String c2cFlowDiagram;

    //当前审批流程的审批状态
    private int sourceProcessApprovalStatus;

    private Map<String, Object> autoDiagramData;

    private JSONArray allFileList;

    private CcCiInfo ciInfo;

    private String creator;

    private Long createTime;

    private Long appSquareConfId;

    private Integer approveSuccessVersion;

    private FlowSnapTreeDto flowSnapTreeDto;

    private Map<String, Object> linkCiMap;

}
