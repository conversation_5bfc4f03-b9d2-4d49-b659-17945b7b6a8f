package com.uino.api.client.cmdb.rpc;

import com.uino.bean.cmdb.base.dataset.batch.DataSetExeResultSheet;
import com.uino.provider.feign.cmdb.DataSetExeResultFegin;
import com.uino.api.client.cmdb.IDataSetExeResultApiSvc;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Title: DataSetExeResultApiSvcRpc
 * @Description:
 * @Author: YGQ
 * @Create: 2021-05-31 16:35
 **/
@Service
public class DataSetExeResultApiSvcRpc implements IDataSetExeResultApiSvc {
    private final DataSetExeResultFegin dataSetExeResultFegin;

    @Autowired
    public DataSetExeResultApiSvcRpc(DataSetExeResultFegin dataSetExeResultFegin) {
        this.dataSetExeResultFegin = dataSetExeResultFegin;
    }

    @Override
    public void saveOrUpdateBatch(List<DataSetExeResultSheet> dataSetExeResultSheets) {
        dataSetExeResultFegin.saveOrUpdateBatch(dataSetExeResultSheets);
    }

    @Override
    public void deleteByDataSetId(Long dataSetId) {
        dataSetExeResultFegin.deleteByDataSetId(dataSetId);
    }


}
