package com.uinnova.product.eam.base.model;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class AttrDefV2Info implements Serializable {

    @Comment("所属分类ID - 可能会变")
    private Long classId;

    @Comment("所属分类CLASS_CODE 唯一")
    private String classCode = "标准规范";

    @Comment("字段定义")
    private List<CcCiAttrDefExtend> attrDefs;

}
