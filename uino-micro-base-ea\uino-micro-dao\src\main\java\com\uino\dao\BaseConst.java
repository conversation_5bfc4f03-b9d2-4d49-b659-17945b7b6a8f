package com.uino.dao;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 常量值
 */
@Component
public class BaseConst {
    /**
     * 默认所属域
     */
    public static Long DEFAULT_DOMAIN_ID = 1L;

    /**
     * 字典项告警定级名称
     * 使用场景：告警查询时使用
     */
    public static final String _MONSYS_SEVERITY_NAME = "告警定级";

    /**
     * 根组织code值
     * 组织code不会变，根据组织code可以找到对应id
     */
    public static final String _ORG_CODE = "组织名称";

    /**
     * 超级管理员loginCode
     */
    public static final String _SUPER_ADMIN_LOGIN_CODE = "superadmin";


}
