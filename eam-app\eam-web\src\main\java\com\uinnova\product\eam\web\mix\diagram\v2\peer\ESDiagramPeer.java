package com.uinnova.product.eam.web.mix.diagram.v2.peer;

import com.alibaba.fastjson.JSONObject;
import com.binary.core.util.BinaryUtils;
import com.binary.jdbc.Page;
import com.uinnova.product.eam.api.diagram.ESDiagramApiClient;
import com.uinnova.product.eam.base.diagram.mix.model.CVcDiagramDir;
import com.uinnova.product.eam.base.diagram.mix.model.VcDiagramDir;
import com.uinnova.product.eam.base.diagram.mix.model.VcDiagramDirInfo;
import com.uinnova.product.eam.base.diagram.model.*;
import com.uinnova.product.eam.model.diagram.*;
import com.uinnova.product.eam.model.diagram.event.RuleParams;
import com.uinnova.product.eam.web.util.DmvFileUtil;
import com.uinnova.product.vmdb.comm.i18n.MessageUtil;
import com.uinnova.product.vmdb.comm.i18n.VerifyType;
import com.uino.bean.cmdb.base.LibType;
import com.uino.util.sys.SysUtil;
import com.uino.bean.permission.base.SysUser;
import lombok.Getter;
import lombok.Setter;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.MultiMatchQueryBuilder;
import org.elasticsearch.index.query.Operator;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;


@Service
public class ESDiagramPeer {

    @Value("${http.resource.space}")
    private String httpResouceUrl;

//    @Autowired
//    private VcDiagramApiClient diagramSvc;

    @Resource
    private DmvFileUtil dmvFileUtil;

    @Resource
    private ESDiagramApiClient diagramApiClient;

//    @Autowired
//    private IDiagramCallBackSvc diagramCallBackSvc;

    /**
     * RSM-SLAVE访问入口
     **/
    @Getter
    @Setter
    private String rsmSlaveRoot;

    /**
     * RSM分类代码
     **/
    @Getter
    @Setter
    private String rsmClassCode;

    private static Map<Long, String> currentMap = new ConcurrentHashMap<>();

    @Resource
    private ESDiagramApiClient esDiagramApiClient;

    /**
     * @return com.uinnova.project.model.diagram.DiagramDirInfo
     * <AUTHOR>
     * @Description 工作台查询指定文件夹下所有子文件夹和视图基础信息
     * @Date 10:21 2021/6/24
     * @Param [pageNum, pageSize, pId（目录id）, tagId, like, type(筛选类型[0:初始化,1:单图,2:组合视图,3:文件夹,4:我的模版]),
     * dirType(目录类型), queryType(查询类型：0:名称,1:作者,2:标签,3:CI), isOpen, isConcert(是否是我协作的查询), dataStatus, order]
     **/
    public ESDiagramDirInfo queryMyDirInfoPageByParentId(Integer pageNum, Integer pageSize, Long pId,
                                                         String like, Integer type, Integer dirType, Integer queryType, Integer dataStatus, String order, LibType libType) {
        SysUser loginUser = SysUtil.getCurrentUserInfo();
        Long userId = loginUser.getId();
        Long domainId = loginUser.getDomainId();
//        dirType = 1;
        Number dirId = pId;
        pId = pId == null ? 0 : pId;

        // 标记是否是superadmin用户
        boolean isSuperAdmin = loginUser.getLoginCode().equals("superadmin");
        VcDiagramDir dir = null;
        if (pId != 0) {
            //通过目录id查询目录信息
            // dir = diagramSvc.queryDiagramDirById(domainId, pId);
            dir = null;
            if (dir == null) {
                MessageUtil.throwVerify(VerifyType.NOT_EXIST, "record", dir);
            }
        }
        subDirCreatorAndModifier(dir);

        //最后返回的封装类，封装了目录和视图信息
        ESDiagramDirInfo result = new ESDiagramDirInfo();
        if (!isSuperAdmin && !type.equals(6) && BinaryUtils.isEmpty(like, true)) {
            // result.setChildrenDirs(queryAndFillDiagramDirList(domainId, pId, like, userId, dataStatus, order,dirType));
        }

        if (!isSuperAdmin && !type.equals(6) && !BinaryUtils.isEmpty(like, true) && pId == 0L) {
            // result.setChildrenDirs(queryAndFillDiagramDirList(domainId, null, like, userId, dataStatus, order, dirType));
        }
        if (!isSuperAdmin && !type.equals(6) && !BinaryUtils.isEmpty(like, true) && pId != 0L) {
            // result.setChildrenDirs(queryAndFillDiagramDirList(domainId, pId, like, userId, dataStatus, order, dirType));
        }

        if (!BinaryUtils.isEmpty(type) && type == 3) {
            // 文件夹
        } else {
            CVcDiagram dCdt = new CVcDiagram();
            dCdt.setDomainId(domainId);

            if (!isSuperAdmin) {
                dCdt.setDirId(pId);
                dCdt.setUserId(userId);
                dCdt.setDirType(dirType);
            }
            if (pId.intValue() == 0 && !BinaryUtils.isEmpty(like)) {
                dCdt.setDirId(null);
            }
            dCdt.setStatus(1);

            // 查询全部视图类型diagramType 1--普通视图，3--公共模板， 4--个人模板
            dCdt.setDiagramTypes(new Integer[]{1, 2, 3, 4});
            // 查询类型
            if (BinaryUtils.isEmpty(queryType)) {
                queryType = 0;
            }
            // 按照关键字模糊查询
            if (queryType == 0) {
                if (!BinaryUtils.isEmpty(like)) {
                    dCdt.setName(like);
                }
            }
            // 如果用户在根目录并且有搜索条件则不对文件夹进行限定
            if (dCdt.getDirId() != null && dCdt.getDirId() == 0 && !BinaryUtils.isEmpty(like)) {
                dCdt.setDirId(null);
            }
            if (dirId == null) {
                dCdt.setDirId(null);
            }
            if (libType == LibType.PRIVATE) {
                dCdt.setIsOpen(0);
            } else {
                dCdt.setUserId(null);
                dCdt.setIsOpen(1);
            }
            Page<ESSimpleDiagramDTO> page = esDiagramApiClient.queryESDiagramInfoPage(domainId, pageNum, pageSize, dCdt, "modifyTime");
            List<ESSimpleDiagramDTO> esSimpleDiagramList = page.getData();

            result.setDiagramInfoPage(new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalRows(),
                    page.getTotalPages(), esSimpleDiagramList));
        }
        // List<VcDiagramDir> childrenDirs = result.getChildrenDirs();
        List<VcDiagramDir> childrenDirs = new ArrayList<>();
        if (!BinaryUtils.isEmpty(childrenDirs)) {
            Set<Long> dirIds = new HashSet<Long>();
            for (VcDiagramDir childrenDir : childrenDirs) {
                dirIds.add(childrenDir.getId());
            }
            result.setDirDiagramCountMap(queryDirDiagramCount(domainId, dirIds.toArray(new Long[0]), userId));
        }
        // result.setDiagramDir(dir);
        result.setDiagramDir(null);
        // 處理文件夾下是否有節點map
        result.setDirHasNodeMap(new HashMap<>());
        if (childrenDirs != null && childrenDirs.size() > 0) {
            Map<Long, VcDiagramDir> childDirIdObjMap = BinaryUtils.toObjectMap(childrenDirs, "id");
            CVcDiagramDir queryDir = new CVcDiagramDir();
            queryDir.setStartDataStatus(1);
            queryDir.setParentIds(childDirIdObjMap.keySet().toArray(new Long[childrenDirs.size()]));
            // List<VcDiagramDir> childs = diagramSvc.queryDiagramDirList(domainId, queryDir, "");
            List<VcDiagramDir> childs = new ArrayList<>();
            Map<Long, List<VcDiagramDir>> parentDirIdChildsMap = new HashMap<>();
            if (!BinaryUtils.isEmpty(childs)) {
                parentDirIdChildsMap = BinaryUtils.toObjectGroupMap(childs, "parentId");
            }
            for (VcDiagramDir childDir : childrenDirs) {
                Long childDirId = childDir.getId();
                result.getDirHasNodeMap().put(childDirId, false);
                if (parentDirIdChildsMap.get(childDirId) != null && parentDirIdChildsMap.get(childDirId).size() > 0) {
                    result.getDirHasNodeMap().put(childDirId, true);
                } else if (result.getDirDiagramCountMap() != null && result.getDirDiagramCountMap().get(childDirId) != null
                        && result.getDirDiagramCountMap().get(childDirId) > 0) {
                    result.getDirHasNodeMap().put(childDirId, true);
                }
            }
        }
        return result;
    }

    //查询指定文件夹的子文件夹
    private List<VcDiagramDir> queryAndFillDiagramDirList(Long domainId, Long currDirId, String like, Long userId, Integer dataStatus, String order, Integer dirType) {
        CVcDiagramDir cdt = new CVcDiagramDir();
        cdt.setParentId(currDirId);
        cdt.setUserId(userId);
        cdt.setDataStatus(dataStatus);
        //模糊查询
        if (like != null) {
            cdt.setDirName("%" + like.trim() + "%");
        }
        cdt.setDomainId(domainId);
        cdt.setDirType(dirType);
        // List<VcDiagramDir> queryDiagramDirList = diagramSvc.queryDiagramDirList(domainId, cdt, order);
        List<VcDiagramDir> queryDiagramDirList = new ArrayList<>();

        subListDirCreatorAndModifier(queryDiagramDirList);
        return queryDiagramDirList;
    }

    private Map<Long, Integer> queryDirDiagramCount(Long domainId, Long[] dirIds, Long userId) {
        Map<Long, Integer> map = new HashMap<Long, Integer>();
        // List<VcDiagramDirInfo> list = diagramSvc.queryDirDiagramCountList(domainId, dirIds, userId);
        List<VcDiagramDirInfo> list = new ArrayList<>();
        if (BinaryUtils.isEmpty(list)) {
            return map;
        }

        for (VcDiagramDirInfo dirInfo : list) {
            map.put(dirInfo.getDirId(), dirInfo.getDiagramCount() == null ? 0 : dirInfo.getDiagramCount());
        }
        return map;
    }

    public Integer moveDirAndDiagram(MoveDirAndDiagramCdt move) {
        return esDiagramApiClient.moveDirAndDiagram(move);
    }

    public List<ESDiagramDTO> queryDiagramInfoByIds(Long[] diagramIds, String type, Boolean needAuth, Boolean asset) {
        return diagramApiClient.queryDiagramInfoByIds(diagramIds, type, needAuth, asset);
    }

    public Long saveOrUpdateDir(VcDiagramDir dir) {
        SysUser loginUser = SysUtil.getCurrentUserInfo();
        Long userId = loginUser.getId();
        Long domainId = loginUser.getDomainId();
        if (BinaryUtils.isEmpty(dir.getUserId())) {
            dir.setUserId(userId);
        }
        dir.setDomainId(domainId);
        // return diagramSvc.saveOrUpdateDiagramDir(domainId, dir);
        return null;
    }

    public Long saveOrUpdateDiagram(VcDiagramInfo diagramInfo, String token) {
        // 视图名称为空校验
        String diagramName = diagramInfo.getDiagram().getName();
        if (StringUtils.isEmpty(diagramName)) {
            MessageUtil.throwVerify(VerifyType.EMPTY, "diagramName", diagramName);
        }

        SysUser loginUser = SysUtil.getCurrentUserInfo();
        Long domainId = loginUser.getDomainId();
        Long userId = loginUser.getId();
        VcDiagram diagram = diagramInfo.getDiagram();

        // 视图具体内容
        String json = diagramInfo.getJson();
        // 缩略图base64编码
        String thumbnail = diagramInfo.getThumbnail();

        // 视图json信息存储地址
        String jsonPath = diagram.getDiagramJson();
        if (!StringUtils.isEmpty(jsonPath)) {
            jsonPath = jsonPath.replaceFirst(httpResouceUrl, "");
        }
        String iconPath = diagram.getIcon1();
        if (!StringUtils.isEmpty(iconPath)) {
            iconPath = iconPath.replaceFirst(httpResouceUrl, "");
        }

        // 是否新建
        boolean isAdd = (diagram.getId() == null);

        // 判断是新增还是修改，同时保存各种实体文件，将字符串转为文件保存
        if (StringUtils.isEmpty(diagram.getId())) {
            // 保存视图信息实体文件
            if (!StringUtils.isEmpty(json)) {
                try {
                    jsonPath = dmvFileUtil.saveOrUpdateResource(UUID.randomUUID().toString() + ".json", json.getBytes(), true);
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }

            if (!StringUtils.isEmpty(thumbnail)) {
                try {
                    iconPath = dmvFileUtil.saveOrUpdatePng(UUID.randomUUID().toString() + ".png",
                            thumbnail, true);
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        } else {
            try {
                jsonPath = dmvFileUtil.saveOrUpdateResource(jsonPath, json.getBytes(), false);
            } catch (IOException e) {
                e.printStackTrace();
            }

            boolean pngCreation = false;

            if (StringUtils.isEmpty(iconPath) && !StringUtils.isEmpty(thumbnail)) {
                pngCreation = true;
                iconPath = UUID.randomUUID().toString() + ".png";
            }

            try {
                iconPath = dmvFileUtil.saveOrUpdatePng(iconPath, thumbnail, pngCreation);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        //将保存的文件路径存储到指定字段中
        if (!StringUtils.isEmpty(iconPath)) {
            diagram.setIcon1(iconPath);
        }
        if (!StringUtils.isEmpty(jsonPath)) {
            diagram.setDiagramJson(jsonPath);
        }
        diagram.setDiagramBgImg(subRsmSlaveRoot(diagram.getDiagramBgImg()));

        //新增操作，需要手动设置用户id
        if (isAdd) {
            diagram.setUserId(userId);
        }
        diagram.setDomainId(domainId);
        if (BinaryUtils.isEmpty(diagram.getDirId())) {
            diagram.setDirId(0L);
        }
        diagram.setCreator(null);
        diagram.setModifier(null);
        if (StringUtils.isEmpty(diagram.getIsOpen())) {
            diagram.setIsOpen(0);
        }
        if (StringUtils.isEmpty(diagram.getStatus())) {
            diagram.setStatus(1);
        }

        //如果是修改操作，判断内容是否有更新，如果没有更新，则不需要进行更新操作
        Long diagramId = diagram.getId();
        if (!isAdd) {
            String diaJson = JSONObject.toJSONString(diagram);
            if (currentMap.containsKey(diagramId)) {
                // 如果diagram对象没有变化，则不更新数据
                if (!currentMap.get(diagramId).equals(diaJson)) {
                    // diagramId = diagramSvc.saveOrUpdateDiagram(domainId, diagramInfo);
                }
            } else {
                currentMap.put(diagramId, diaJson);
                // diagramId = diagramSvc.saveOrUpdateDiagram(domainId, diagramInfo);
            }
            if (currentMap.size() > 300) {

            }
        } else {
            // diagramId = diagramSvc.saveOrUpdateDiagram(domainId, diagramInfo);
        }

        return diagramId;
    }

    /**
     * 修整视图信息
     *
     * @param diagramInfo
     * @param token
     */
    private void trimDiagram(VcDiagramInfo diagramInfo, String token) {
        if (diagramInfo == null) {
            return;
        }
        fillPath(diagramInfo);
        subCreatorAndModifier(diagramInfo);
    }

    private void fillPath(VcDiagramInfo diagramInfo) {
        if (diagramInfo == null) {
            return;
        }
        VcDiagram diagram = diagramInfo.getDiagram();
        if (diagram == null) {
            return;
        }
        // 缩略图
        String icon1 = diagram.getIcon1();
        if (icon1 != null && !icon1.startsWith(httpResouceUrl)) {
            icon1 = httpResouceUrl + icon1;
            diagram.setIcon1(icon1);
        }
        String diagramBgImg = diagram.getDiagramBgImg();
        if (diagramBgImg != null && !BinaryUtils.isEmpty(diagramBgImg.trim())
                && !diagramBgImg.startsWith(httpResouceUrl)) {
            diagramBgImg = httpResouceUrl + diagramBgImg;
            diagram.setDiagramBgImg(diagramBgImg);
        }
        // 修整用户头像信息
        SysUser creator = diagramInfo.getCreator();
        if (creator != null) {
            String icon = creator.getIcon();
            // 这儿判断是因为当前用户是同一个对象，所以再修改会无限拼接，勿删！！！！！！
            if (icon != null && !icon.startsWith(this.httpResouceUrl)) {
                creator.setIcon(httpResouceUrl + icon);
            }
        }
    }

    /**
     * 将创建者和修改者名字剪切掉 aa[bb] --> aa
     *
     * @param diagramInfo 视图信息
     */
    private void subCreatorAndModifier(VcDiagramInfo diagramInfo) {
        if (diagramInfo != null) {
            VcDiagram diagram = diagramInfo.getDiagram();
            if (diagram != null) {
                diagram.setCreator(subName(diagram.getCreator()));
                diagram.setModifier(subName(diagram.getModifier()));
            }
        }
    }

    private void subListDirCreatorAndModifier(List<VcDiagramDir> diagramDirs) {
        if (diagramDirs != null) {
            for (VcDiagramDir diagramDir : diagramDirs) {
                subDirCreatorAndModifier(diagramDir);
            }
        }
    }

    private void subDirCreatorAndModifier(VcDiagramDir diagramDir) {
        if (diagramDir != null) {
            diagramDir.setCreator(subName(diagramDir.getCreator()));
            diagramDir.setModifier(subName(diagramDir.getModifier()));
        }
    }

    private String subName(String name) {
        if (name != null) {
            int lastIndexOf = name.lastIndexOf("[");
            if (lastIndexOf > 0) {
                return name.substring(0, lastIndexOf);
            }
        }
        return name;
    }

    private String subRsmSlaveRoot(String url) {
        if (!BinaryUtils.isEmpty(url) && url.startsWith(httpResouceUrl)) {
            return url.substring(httpResouceUrl.length());
        }
        return url;
    }

    public List<Long> saveESDiagramBatch(List<ESDiagramInfoDTO> esDiagramInfoList) {
        return esDiagramApiClient.saveESDiagramBatch(esDiagramInfoList, null, null, null);
    }

    public Map<String, String> saveESDiagram(ESDiagramInfoDTO esDiagramInfo) {
        return  diagramApiClient.saveESDiagram(esDiagramInfo);
    }

    public Map<String, List<ESResponseStruct>> saveOrUpdateDiagramComponent(String jsonStr) {
        return diagramApiClient.saveOrUpdateDiagramComponent(jsonStr);
    }

    public ESDiagramDTO queryESDiagramInfoById(Long diagramId, String type, Boolean versionFlag) {
        return diagramApiClient.queryESDiagramInfoById(diagramId, type, versionFlag);
    }

    public void processBatchThumbnail(ThumbnailBatch thumbnailBatch) {
        diagramApiClient.processBatchThumbnail(thumbnailBatch);
    }

    /**
     * @return java.util.List<com.uinnova.project.base.diagram.comm.model.ESDiagramDTO>
     * <AUTHOR>
     * @Description //待确定需求：工作台查询视图基础信息、激活sheet以及其包含的node、link
     * @Date 14:36 2021/7/22
     * @Param [diagramIds, type]
     **/
    public List<ESDiagramDTO> querySimpleDiagramInfoByIds(Long[] diagramIds, String type) {
        return null;
    }

    public ESDiagram querySimpleDiagramInfoById(Long diagramId) {
        return diagramApiClient.querySimpleDiagramInfoById(diagramId);
    }

    public Long[] queryDiagramInfoBydEnergy(String[] diagramIds) {

        return diagramApiClient.queryDiagramInfoBydEnergy(diagramIds);
    }

    public Long queryDiagramInfoByEnergy(String diagramId) {
        return diagramApiClient.queryDiagramInfoByEnergy(diagramId);
    }

    public int createHiddenLink(RuleParams params) {
        return diagramApiClient.createHiddenLink(params);
    }

    /**
     * 根据视图id返现下钻标识
     * @param data
     * @return
     */
    public Map<String, Boolean> getSkipStatusByDiagramIds(Map<String, List<String>> data) {
        // 返回值
        Map<String, Boolean> result = new HashMap<>();
        Set<String> diagramIds = new HashSet<>();
        for (String key : data.keySet()) {
            diagramIds.addAll(data.get(key));
        }
        // 获取前端传递的所有视图信息 进行筛选 (直接查询ES数据库)
        List<ESDiagram> esDiagrams = this.queryDBDiagramInfoByIds(diagramIds.toArray(new String[diagramIds.size()]));
        // 组合 视图ID 与 发布状态 的 map
        Map<String, Boolean> idAndOpenMap = new HashMap<>();
        esDiagrams.forEach(e -> {
            idAndOpenMap.put(e.getDEnergy(), BinaryUtils.isEmpty(e.getReleaseDiagramId()) ? false : true);
        });
        // 遍历传参 当前视图如果删除 即判断为 false
        for (String key : data.keySet()) {
            List<String> eIds = data.get(key);
            for (String eId : eIds) {
                // 每个跳转图标下关联多张视图 只要存在一张发布 就返回true
                if (idAndOpenMap.containsKey(eId) && idAndOpenMap.get(eId)) {
                    result.put(key, true);
                    break;
                }
                // 所有视图均未发布 就返回false 覆盖前一次结果
                result.put(key, false);
            }
        }
        return result;
    }

    public Long[] queryDBDiagramInfoBydEnergy(String[] diagramIds) {
        return diagramApiClient.queryDBDiagramInfoBydEnergy(diagramIds);
    }

    public List<ESDiagram> queryDBDiagramInfoByIds(String[] eId) {
        return diagramApiClient.queryDBDiagramInfoByIds(eId);
    }

    public Map<String, Set<DiagramRelationInfo>> getRelateInfoByDiagramIds(String diagramId, Integer browseStatus) {
        return diagramApiClient.getRelateInfoByDiagramIds(diagramId, browseStatus);
    }

    public List<ESDiagram> getReleaseDiagramList(String like) {
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("isOpen", 1));
        query.must(QueryBuilders.termQuery("status", 1));
        query.must(QueryBuilders.termQuery("dataStatus", 1));
        query.must(QueryBuilders.termQuery("historyVersionFlag", 1));
        query.must(QueryBuilders.termQuery("domainId", SysUtil.getCurrentUserInfo().getDomainId()));
        if (!BinaryUtils.isEmpty(like)) {
            query.must(QueryBuilders.multiMatchQuery(like, "name").operator(Operator.AND).type(MultiMatchQueryBuilder.Type.PHRASE_PREFIX).lenient(true));
        }
        ArrayList<ESDiagram> releaseDiagramList = new ArrayList<>();
        Page<ESDiagram> esDiagramPage = diagramApiClient.selectListByQuery(1, 3000, query);
        releaseDiagramList.addAll(esDiagramPage.getData());
        if (esDiagramPage.getTotalRows() > 3000) {
            for (int i = 2; i <esDiagramPage.getTotalPages()+1; i++) {
                Page<ESDiagram> esDiagramPage2 = diagramApiClient.selectListByQuery(i, 3000, query);
                releaseDiagramList.addAll(esDiagramPage2.getData());
            }
        }
        return releaseDiagramList;
    }


    public Integer queryFlowStatusById(String diagramId) {
        return diagramApiClient.queryFlowStatusById(diagramId);
    }
}
