package com.uinnova.product.eam.model;

import com.binary.framework.bean.annotation.Comment;

public class VcDiagramInfoCount {
	
	@Comment("视图id")
	private Long id;
	
	@Comment("视图名称")
	private String diagramName;

	@Comment("最后修改时间")
	private Long modifyTime;
	
	@Comment("查看次数")
	private Integer readCount;
	
	@Comment("收藏次数")
	private Integer enshCount;

	public String getDiagramName() {
		return diagramName;
	}

	public void setDiagramName(String diagramName) {
		this.diagramName = diagramName;
	}

	public Long getModifyTime() {
		return modifyTime;
	}

	public void setModifyTime(Long modifyTime) {
		this.modifyTime = modifyTime;
	}

	public Integer getReadCount() {
		return readCount;
	}

	public void setReadCount(Integer readCount) {
		this.readCount = readCount;
	}

	public Integer getEnshCount() {
		return enshCount;
	}

	public void setEnshCount(Integer enshCount) {
		this.enshCount = enshCount;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}
	
	
	
}
