package com.uinnova.product.eam.base.model;

import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class SpecificationInfo {
    private String domainName;
    private List<CcCiInfo> moduleInfos;
    private List<CcCiInfo> taskInfos;
    private List<SpecificationAppendix> appendices;
    private List<Map<String, String>> appendicesHeader;
}
