package com.uino.monitor.tp.metric;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.binary.core.exception.MessageException;
import com.uino.bean.tp.base.FinalPerformanceDTO;
import com.uino.dao.AbstractESBaseDao;
import com.uino.dao.ESConst;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.core.TimeValue;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Map;

/**
 * 同一ciCode和metric的最新性能数据
 * @author: weixuesong
 * @create: 2020/07/13 16:41
 **/
@Service
@Slf4j
public class LastMetricDataSvc extends AbstractESBaseDao<FinalPerformanceDTO, Object> {

    @Override
    public String getIndex() {
        return ESConst.INDEX_LAST_METRIC_DATA;
    }

    @Override
    public String getType() {
        return ESConst.INDEX_LAST_METRIC_DATA;
    }

    @Value("${base.performance.number.of.shards:10}")
    private Integer numberOfShards;

//    @PostConstruct
    public void init() {
        this.initIndex();
    }

    /**
     * 同步批量保存或更新
     *
     * @param datas 对象组
     */
    @Override
    public boolean syncSaveOrUpdateBatch(Map<String, Object> datas) {
        BulkRequest bulkRequest = new BulkRequest();
        bulkRequest.timeout(TimeValue.timeValueMinutes(2));
        boolean flag = true;
        if (!datas.isEmpty()) {
            for (Map.Entry<String, Object> entry : datas.entrySet()) {
                JSONObject documentObj = JSON.parseObject(JSON.toJSONString(entry.getValue()));
                JSONObject otherFiledMap = documentObj.getJSONObject("otherFiledMap");

                if (null == otherFiledMap || otherFiledMap.isEmpty()) {
                    documentObj.remove("otherFiledMap");
                }

                JSONObject metricAttrs = documentObj.getJSONObject("metricAttrs");
                if (null == metricAttrs || metricAttrs.isEmpty()) {
                    documentObj.remove("metricAttrs");
                }

				String unit = documentObj.getString("unit");
                if (null == unit) {
                    documentObj.remove("unit");
                }
                UpdateRequest uRequest = this.getUpdateRequest(entry.getKey());
                uRequest.doc(documentObj);
                uRequest.docAsUpsert(true);
                bulkRequest.add(uRequest);
            }

            try {
                BulkResponse bulkResponse = getClient().bulk(bulkRequest, RequestOptions.DEFAULT);
                if (bulkResponse.hasFailures()) {
                    flag = false;
                    log.error(bulkResponse.buildFailureMessage());
                }
            } catch (IOException e) {
                log.error(e.getMessage(), e);
                throw new MessageException(e.getMessage());
            }
        }

        return flag;
    }
}
