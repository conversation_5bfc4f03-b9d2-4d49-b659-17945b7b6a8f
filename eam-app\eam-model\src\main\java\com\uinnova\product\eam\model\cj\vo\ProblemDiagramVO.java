package com.uinnova.product.eam.model.cj.vo;

import com.uinnova.product.eam.base.diagram.model.ESDiagram;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 审批 - 校验方案
 * @author: Lc
 * @create: 2022-04-13 15:39
 */
@Data
public class ProblemDiagramVO implements Serializable{

    /** 模块id */
    private Long moduleId;

    /** 视图信息 */
    private ESDiagram esDiagram;

    /** 制品问题描述 */
    private List<String> diagramProblemDesc;
}
