package com.uino.dao.permission;

import java.net.URL;
import java.util.*;
import java.util.stream.Collectors;

import jakarta.annotation.PostConstruct;

import com.alibaba.fastjson.JSONObject;
import com.binary.framework.exception.ServiceException;
import com.uino.bean.permission.base.*;
import com.uino.bean.permission.business.request.BusinessConfigDto;
import com.uino.dao.util.TrialSaaSUtil;
import com.uino.util.digest.impl.type.Base64Util;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.admin.indices.create.CreateIndexRequest;
import org.elasticsearch.action.admin.indices.get.GetIndexRequest;
import org.elasticsearch.action.admin.indices.settings.put.UpdateSettingsRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.binary.core.util.BinaryUtils;
import com.binary.jdbc.Page;
import com.uino.dao.AbstractESBaseDao;
import com.uino.dao.ESConst;
import com.uino.dao.permission.rlt.ESRoleModuleRltSvc;
import com.uino.dao.permission.rlt.ESUserModuleEnshrineRltSvc;
import com.uino.dao.permission.rlt.ESUserModuleRltSvc;
import com.uino.util.sys.BeanUtil;
import com.uino.util.sys.CommonFileUtil;
import com.uino.bean.permission.business.ModuleNodeInfo;
import com.uino.bean.permission.query.CSysModule;
import com.uino.bean.permission.query.CSysUserModuleEnshrineRlt;
import org.springframework.util.CollectionUtils;

/**
 * <b>系统模块
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class ESModuleSvc extends AbstractESBaseDao<SysModule, CSysModule> {
    @Autowired
    ESUserModuleRltSvc userModuleRltSvc;

    @Autowired
    ESRoleModuleRltSvc roleModuleRltSvc;

    @Autowired
    private ESRoleSvc roleSvc;

    @Value("${http.resource.space}")
    private  String httpResouceUrl;


    @Autowired
    ESUserModuleEnshrineRltSvc enshrineSvc;

    @Autowired
    private TrialSaaSUtil trialSaaSUtil;

    @Override
    public String getIndex() {
        return ESConst.INDEX_SYS_MODULE;
    }

    @Override
    public String getType() {
        return ESConst.INDEX_SYS_MODULE;
    }

    @Override
    protected void savePreOptionCore(SysModule t) {
        if (t.getLabel() == null) {
            t.setLabel(t.getModuleName());
        }
        if (t.getParentId() == null) {
            t.setParentId(0L);
        }
    }

    @PostConstruct
    public void init() {
        List<SysModule> datas = getInitDatas();

        List<SysModule> newData = datas;
        // 判断文件是否存在
        URL resource = this.getClass().getClassLoader().getResource("./initdata/uino_business_config.json");
        if (resource != null) {
            List<String> businessConfigList = CommonFileUtil.getData("/initdata/uino_business_config.json", String.class);
            // 存在就过滤可用配置
            if (!CollectionUtils.isEmpty(businessConfigList)) {
                String result = businessConfigList.get(0);
                String content = Base64Util.base64dec(result);
                BusinessConfigDto businessConfigDto = JSONObject.parseObject(content, BusinessConfigDto.class);
                if (businessConfigDto != null && !CollectionUtils.isEmpty(businessConfigDto.getSysConfig())) {
                    // 先删除初始化进去的系统模块数据
                    BoolQueryBuilder builder = QueryBuilders.boolQuery();
                    builder.must(QueryBuilders.termQuery("isInit", true));
                    deleteByQuery(builder, true);
                    // 从功能配置读取数据
                    List<Long> configIdList = businessConfigDto.getSysConfig();
                    datas = datas.stream().filter(sysModule -> configIdList.contains(sysModule.getId())).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(datas)) {
                        datas = newData;
                    }
                }
            }
        }

        // super.initIndex(datas);
        try {
            RestHighLevelClient clientc = getClient();
            GetIndexRequest getRequest = new GetIndexRequest();
            String fullIndexName = getFullIndexName();
            getRequest.indices(fullIndexName);
            boolean existIndex = clientc.indices().exists(getRequest, RequestOptions.DEFAULT);
            if (!existIndex) {
                super.initIndex(datas);
            } else {
                // 数据库中存在初始化数据 以数据库版本为主 不进行更新
                List<SysModule> listByQuery = getListByQuery(null);
                List<Long> existId = listByQuery.stream().map(SysModule::getId).collect(Collectors.toList());
                datas = datas.stream().filter(e -> !existId.contains(e.getId())).collect(Collectors.toList());
                saveOrUpdateBatch(datas);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 获取初始化数据
     * 
     * @return
     */
    public List<SysModule> getInitDatas() {
		List<SysModule> datas = CommonFileUtil.getData("/initdata/uino_sys_module.json", SysModule.class);
		datas = trialSaaSUtil.appSquareModuleRenameAndOnTop(datas);
        int orderNum = 0;
        for (SysModule module : datas) {
            module.setOrderNo(++orderNum);
            // 菜单初始化不强制设置为true
            // module.setIsInit(true);
        }
        return datas;
    }

    /**
     * 获取全部系统模块
     * 
     * @return
     */
    public List<SysModule> getAll(Long domainId) {
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("domainId",domainId));
        Page<SysModule> sysModulePage = super.getSortListByQuery(1, 3000, query, Collections.singletonList(SortBuilders.fieldSort("orderNo").order(SortOrder.ASC)));
        return sysModulePage.getData();
    }

    /**
     * 获取全部系统模块
     *
     * @return
     */
    public List<SysModule> getSysModuleIdList(List<Integer> businessGroupList, List<Integer> businessModuleList) {
        if (CollectionUtils.isEmpty(businessGroupList) && CollectionUtils.isEmpty(businessModuleList)) {
            throw new ServiceException("业务分组模块不可为空！");
        }
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        if (!CollectionUtils.isEmpty(businessGroupList)) {
            query.must(QueryBuilders.termsQuery("businessGroup", businessGroupList));
        }
        if (!CollectionUtils.isEmpty(businessModuleList)) {
            query.must(QueryBuilders.termsQuery("businessModule", businessModuleList));
        }
        List<SysModule> sysModuleList = super.getListByQuery(query);
        if (!CollectionUtils.isEmpty(sysModuleList)) {
            return sysModuleList;
        }
        return Collections.emptyList();
    }

    /**
     * 根据用户Id 获取系统模块
     * 
     * @param userId
     * @return
     */
    public List<SysModule> getListByUserId(Long userId) {
        List<SysUserModuleRlt> rlts = userModuleRltSvc.getListByQuery(1, 1000, QueryBuilders.termQuery("userId", userId)).getData();
        Set<Long> ids = new HashSet<Long>();
        for (SysUserModuleRlt rlt : rlts) {
            ids.add(rlt.getModuleId());
        }
        return super.getListByQuery(1, 1000, QueryBuilders.termsQuery("id", ids)).getData();
    }

    /**
     * 根据角色Id 获取系统模块
     * 
     * @param roleIds
     * @return
     */
    public List<SysModule> getListByRoleIds(Collection<Long> roleIds) {
        List<SysRoleModuleRlt> rlts = roleModuleRltSvc.getListByQuery(1, 1000, QueryBuilders.termsQuery("roleId", roleIds)).getData();
        Set<Long> ids = new HashSet<Long>();
        for (SysRoleModuleRlt rlt : rlts) {
            ids.add(rlt.getModuleId());
        }
        return super.getListByQuery(1, 1000, QueryBuilders.termsQuery("id", ids)).getData();
    }

    /**
     * 获取整颗模块树
     * 
     * @return
     */
    public ModuleNodeInfo getModuleTree(Long domainId) {
        return this.getModuleTree(domainId,null);
    }

    /**
     * 获取模块树
     * 
     * @param userId null表示获取整棵树结构，not null表示获取该用户有权限的树
     * @return
     */
    public ModuleNodeInfo getModuleTree(Long domainId,Long userId) {
        List<SysModule> modules = null;
        if (userId == null) {
            // 获取整个树结构
            modules = this.getAll(domainId);
        } else {
            // 只获取这个用户的
            // 获取权限
            List<SysModule> hasAuthModules = this.getModuleAuths(userId);
            List<Long> hasAuthModuleIds = hasAuthModules.stream().map(SysModule::getId).collect(Collectors.toList());
            // 获取所有模块列表-带权限
            Page<SysModule> modulePage =
                super.getSortListByQuery(1, 3000, QueryBuilders.termsQuery("id", hasAuthModuleIds), Collections.singletonList(SortBuilders.fieldSort("orderNo").order(SortOrder.ASC)));
            modules = modulePage.getData();
        }
        // 没有label的将name填奥label
        modules.stream().filter(module -> (module.getLabel() == null || "".equals(module.getLabel()))).forEach(module -> module.setLabel(module.getModuleName()));
        CSysUserModuleEnshrineRlt cdt = new CSysUserModuleEnshrineRlt();
        cdt.setUserId(userId);
        List<SysUserModuleEnshrineRlt> sysUserModuleEnshrineRltList = enshrineSvc.getListByCdt(cdt);
        List<Long> enshrineModeulIds = new ArrayList<Long>();
        if (sysUserModuleEnshrineRltList != null && sysUserModuleEnshrineRltList.size() > 0) {
            for (SysUserModuleEnshrineRlt rlt : sysUserModuleEnshrineRltList) {
                enshrineModeulIds.add(rlt.getModuleId());
            }
        }
        // 列表结构转树形结构
        return listToTree(modules, enshrineModeulIds);
    }

    /**
     * 获取指定用户模块权限
     * 
     * @param userId
     * @return
     */
    public List<SysModule> getModuleAuths(Long userId) {
        List<SysModule> results = new ArrayList<SysModule>();
        // 获取用户有权限的模块
        List<SysModule> userModules = this.getListByUserId(userId);
        if (!BinaryUtils.isEmpty(userModules)) {
            results.addAll(userModules);
        }
        // 获取用户所属角色有权限的模块
        List<SysRole> roles = roleSvc.getListByUserId(userId);
        if (!BinaryUtils.isEmpty(roles)) {
            // 找出这些角色有的模块权限加进去
            Map<Long, SysRole> idRoleDict = BinaryUtils.toObjectMap(roles, "id");
            List<SysModule> roleModules = this.getListByRoleIds(idRoleDict.keySet());
            if (!BinaryUtils.isEmpty(roleModules)) {
                results.addAll(roleModules);
            }
        }
        return results.stream().distinct().collect(Collectors.toList());
    }

    /**
     * 列表结构转树结构 ！目前该转换只从根转换，若有需求需要不从根获取则将rootNodeId抹除，加入node信息参数，然后rootNodeid获取该nodeid即可; 或者将listToTreeCore设置为公开自己调度
     * 
     * @param modules
     */
    public  ModuleNodeInfo listToTree(List<SysModule> modules, List<Long> enshrineModeulIds) {
        // 模块树，先虚拟一个根出来
        long rootNodeId = 0L;
        ModuleNodeInfo tree = new ModuleNodeInfo();
        tree.setId(rootNodeId);
        tree.setModuleName("北京优锘科技有限公司");
        if (BinaryUtils.isEmpty(modules)) {
            return tree;
        }
        // parentId:[childInfos]字典
        Map<Long, Set<SysModule>> parentIdChildsDict = new LinkedHashMap<>();
        for (SysModule module : modules) {
            if (parentIdChildsDict.get(module.getParentId()) == null) {
                parentIdChildsDict.put(module.getParentId(), new LinkedHashSet<>());
            }
            if(!BinaryUtils.isEmpty(module.getModulePic())){
                module.setModulePic(httpResouceUrl+"/"+module.getModulePic());
            }
            parentIdChildsDict.get(module.getParentId()).add(module);
        }
        // 平铺列表转换tree结构
        listToTreeCore(parentIdChildsDict, tree, enshrineModeulIds);
        return tree;
    }

    /**
     * 列表结构转树形结构核心
     * 
     * @param parentIdChildsDict
     * @param node
     */
    private static void listToTreeCore(Map<Long, Set<SysModule>> parentIdChildsDict, ModuleNodeInfo node, List<Long> enshrineModeulIds) {
        Long parentNodeId = node.getId();
        Set<SysModule> childs = parentIdChildsDict.get(parentNodeId);
        if (!BinaryUtils.isEmpty(childs)) {
            node.setChildren(BeanUtil.converBean(childs, ModuleNodeInfo.class));
            node.getChildren().forEach(nextNode -> {
                Long id = nextNode.getId();
                if (enshrineModeulIds.contains(id)) {
                    nextNode.setIsFavorite(1);
                }
                listToTreeCore(parentIdChildsDict, nextNode, enshrineModeulIds);
            });
        }
    }
    public ModuleNodeInfo getModuleTreeBySign(Long domainId, Long userId, String moduleSign) {
        List<SysModule> modules = null;
        if (userId == null) {
            // 获取整个树结构
            modules = this.getAll(domainId);
        } else {
            // 只获取这个用户的
            // 获取权限
            List<SysModule> hasAuthModules = this.getModuleAuths(userId);
            List<Long> hasAuthModuleIds = hasAuthModules.stream().map(SysModule::getId).collect(Collectors.toList());
            // 获取所有模块列表-带权限
            Page<SysModule> modulePage =
                    super.getSortListByQuery(1, 3000, QueryBuilders.termsQuery("id", hasAuthModuleIds), Collections.singletonList(SortBuilders.fieldSort("orderNo").order(SortOrder.ASC)));
            modules = modulePage.getData();
        }
        // 没有label的将name填奥label
        modules.stream().filter(module -> (module.getLabel() == null || "".equals(module.getLabel()))).forEach(module -> module.setLabel(module.getModuleName()));
        CSysUserModuleEnshrineRlt cdt = new CSysUserModuleEnshrineRlt();
        cdt.setUserId(userId);
        List<SysUserModuleEnshrineRlt> sysUserModuleEnshrineRltList = enshrineSvc.getListByCdt(cdt);
        List<Long> enshrineModeulIds = new ArrayList<Long>();
        if (sysUserModuleEnshrineRltList != null && sysUserModuleEnshrineRltList.size() > 0) {
            for (SysUserModuleEnshrineRlt rlt : sysUserModuleEnshrineRltList) {
                enshrineModeulIds.add(rlt.getModuleId());
            }
        }
        // 根据搜索标识为根节点转树形结构
        return listToTreeBySignRootNode(modules, enshrineModeulIds,moduleSign);
    }
    public  ModuleNodeInfo listToTreeBySignRootNode(List<SysModule> modules, List<Long> enshrineModeulIds,String moduleSign) {
        Optional<SysModule> first = modules.stream().filter(module -> !BinaryUtils.isEmpty(module.getModuleSign()) && module.getModuleSign().equals(moduleSign)).findFirst();
        if (!first.isPresent()) {
            throw new ServiceException("【"+moduleSign+"】该模块标识不存在");
        }
        SysModule sysModule = first.get();
        // 模块树，先虚拟一个根出来
        ModuleNodeInfo tree = BeanUtil.converBean(sysModule, ModuleNodeInfo.class);
        // parentId:[childInfos]字典
        Map<Long, Set<SysModule>> parentIdChildsDict = new LinkedHashMap<>();
        for (SysModule module : modules) {
            parentIdChildsDict.computeIfAbsent(module.getParentId(),e-> new LinkedHashSet<>());
            if (parentIdChildsDict.get(module.getParentId()) == null) {
                parentIdChildsDict.put(module.getParentId(), new LinkedHashSet<>());
            }
            if(!BinaryUtils.isEmpty(module.getModulePic())){
                module.setModulePic(httpResouceUrl+"/"+module.getModulePic());
            }
            parentIdChildsDict.get(module.getParentId()).add(module);
        }
        // 平铺列表转换tree结构
        listToTreeCore(parentIdChildsDict, tree, enshrineModeulIds);
        return tree;
    }
}
