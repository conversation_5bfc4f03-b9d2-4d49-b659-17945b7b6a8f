package com.uinnova.product.eam.service.cj.service;

import com.uinnova.product.eam.model.cj.request.PlanDiagramDirBatchCopyParam;
import com.uinnova.product.eam.model.cj.request.PlanDiagramDirBatchMoveParam;

/**
 * 方案、视图、文件夹批量操作  service
 *
 * <AUTHOR>
 * @since 2022-3-9 19:49:22
 */
public interface PlanDiagramDirBatchService {

    /**
     * 根目录Id
     */
    long ROOT_DIR_ID = 0L;

    long OTHER_DIR_ID = 0l;

    /**
     * 批量移动
     *
     * @param request {@link PlanDiagramDirBatchMoveParam}
     */
    @Deprecated
    void moveBatch(PlanDiagramDirBatchMoveParam request);

    /**
     * 批量复制
     *
     * @param request {@link PlanDiagramDirBatchCopyParam}
     */
    @Deprecated
    void copyBatch(PlanDiagramDirBatchCopyParam request);
}
