package com.uinnova.product.eam.service.diagram;

import com.binary.framework.web.RemoteResult;
import com.binary.jdbc.Page;
import com.uinnova.product.eam.base.diagram.model.*;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClassDir;
import com.uinnova.product.vmdb.comm.model.image.CcImage;
import com.uino.bean.cmdb.business.ImageCount;
import com.uino.bean.cmdb.business.ImportDirMessage;
import com.uino.bean.cmdb.business.ImportResultMessage;
import com.uino.bean.cmdb.query.ESSearchImageBean;
import com.uino.bean.permission.query.SearchKeywordBean;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.zip.ZipEntry;

/**
 * <AUTHOR>
 */
public interface ESShapeSvc {
    /**
     * <AUTHOR>
     * @Description 添加图形到ES
     * @Date 16:06 2021/8/25
     * @Param [ESUserShapeDTO]
     * @return RemoteResult
     **/
    RemoteResult saveShape(ESUserShapeDTO esUserShape);

    /**
     * <AUTHOR>
     * @Description 查询用户的图形库
     * @Date 16:06 2021/8/25
     * @Param
     * @return java.util.List
     **/
    List<ESUserShape> queryMyShapes();

    StringBuilder addDenergy();

    StringBuilder nodeToEnergy(Long key);

    List<ESUserShape> queryImagePage(Integer pageNum, Integer pageSize, Long dirId, String keyword);

    ImportResultMessage importZipImage(Integer sourceType, MultipartFile file);

    List<ImageCount> queryImageDirList(CCcCiClassDir cdt);

    Page<ESUserShape> queryImagePage(ESImageBean bean);

    List<CcImage> queryTopImage(SearchKeywordBean bean);

    boolean importImage(Long dirId, MultipartFile file);

    ImportDirMessage importImages(Long dirId, MultipartFile... files);

    boolean replaceImage(Long imageId, MultipartFile file);

    boolean deleteImage(CcImage image);


    long deleteDirImage(Long dirId);

    ResponseEntity<byte[]> exportImageZipByDirIds(Set<Long> dirIds);

    ImportDirMessage import3DZipImage(MultipartFile file, Long dirId, Map<ZipEntry, byte[]> zipEntriesMap, boolean isCover);

    Long updateImageRlt(ESUserShape image);

    boolean replace3DImage(Long imageId, MultipartFile file);

    boolean delete3DImage(CcImage image);

    CcImage queryImageById(Long id);

    Long countBySearchBean(ESSearchImageBean bean);

    ResponseEntity<byte[]> downloadImageResource(List<Long> ids);

    boolean sort(ESUserShapeQuery cdt);

    StringBuilder updateHttp(String userName,String ip);

    List<ESUserShapeResult> queryAllImage(ESImageBean bean);
}
