package com.uino.util.cache.impl;

import com.binary.core.util.BinaryUtils;
import com.uino.util.cache.ICacheService;
import org.redisson.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * redisson实现缓存
 */
@Service
public class RedissonImpl implements ICacheService {

    @Autowired(required = false)
    private RedissonClient redissonClient;

    @Override
    public void setCache(String key, Object value) {
        redissonClient.getBucket(key).set(value);
    }

    @Override
    public void setCacheBatch(Map<String, Object> params) {
        //通过 RBatch 对象可以将多个命令汇总到一个网络调用中一次性发送并执行
        RBatch batch = redissonClient.createBatch(BatchOptions.defaults());
        for (String key: params.keySet()) {
            batch.getBucket(key).setAsync(params.get(key));
        }
        batch.execute();
    }

    @Override
    public void setCache(String key, Object value, long milliseconds) {
        redissonClient.getBucket(key).set(value, milliseconds, TimeUnit.MILLISECONDS);
    }

    @Override
    public Object getCache(String key) {
        return redissonClient.getBucket(key).get();
    }

    @Override
    public <T> T getCacheByType(String key, Class<T> clazz) {
        return (T) redissonClient.getBucket(key).get();
    }

    @Override
    public void delKey(String key) {
        redissonClient.getBucket(key).delete();
    }

    @Override
    public void delKeys(String... keys) {
        RKeys allKeys = redissonClient.getKeys();
        allKeys.delete(keys);
    }

    @Override
    public <T> List<T> listByPattern(String patternKey, Class<T> clazz) {
        //获取所有keys
        RKeys keys = redissonClient.getKeys();
        if (BinaryUtils.isEmpty(keys.count())) {
            return null;
        }
        //匹配key
        Iterable<String> foundedKeys = keys.getKeysByPattern(patternKey);
        List<String> patternKeys = new ArrayList<>();
        foundedKeys.forEach(patternKeys::add);
        if (BinaryUtils.isEmpty(patternKeys)) {
            return null;
        }
        return listByKeys(patternKeys.toArray(new String[patternKeys.size()]), clazz);
    }

    @Override
    public <T> List<T> listByKeys(String[] keys, Class<T> clazz) {
        if (BinaryUtils.isEmpty(keys)) {
            return null;
        }
        List<T> result = new ArrayList<>();
        Map<String, T> resultMap = redissonClient.getBuckets().get(keys);
        for(String temp : resultMap.keySet()){
            result.add(resultMap.get(temp));
        }
        return result;
    }

    @Override
    public Set<String> getKeysByPattern(String key) {
        Set<String> keys = new HashSet<>();
        RKeys allKeys = redissonClient.getKeys();
        if (BinaryUtils.isEmpty(allKeys.count())) {
            return keys;
        }
        Iterable<String> patternKeys = allKeys.getKeysByPattern(key);
        patternKeys.forEach(keys::add);
        return keys;
    }

    @Override
    public Set<String> getAllKeys() {
        Set<String> keys = new HashSet<>();
        RKeys allKeys = redissonClient.getKeys();
        if (BinaryUtils.isEmpty(allKeys.count())) {
            return keys;
        }
        allKeys.getKeys().forEach(keys::add);
        return keys;
    }

    @Override
    public void setAddValue(String key, Object value) {
        redissonClient.getSet(key).add(value);
    }

    @Override
    public void setAddValues(String key, Set<Object> values) {
        redissonClient.getSet(key).addAll(values);
    }

    @Override
    public void setRemoveValue(String key, Object value) {
        redissonClient.getSet(key).remove(value);
    }

    @Override
    public <T> Set<T> setByKey(String key) {
        return (Set<T>) redissonClient.getSet(key).readAll();
    }

    @Override
    public Long atomicGet(String key){
        RAtomicLong atomic = redissonClient.getAtomicLong(key);
        return atomic.get();
    }

    @Override
    public Long getAndSet(String key, Long value){
        RAtomicLong atomic = redissonClient.getAtomicLong(key);
        return atomic.getAndSet(value);
    }

    @Override
    public Long incrementAndGet(String key) {
        RAtomicLong atomic = redissonClient.getAtomicLong(key);
        return atomic.incrementAndGet();
    }

    @Override
    public void setTTL(String key, long milliseconds) {
        redissonClient.getBucket(key).expire(Duration.ofSeconds(milliseconds));
    }
}
