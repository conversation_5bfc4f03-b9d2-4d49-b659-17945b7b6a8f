package com.uino.dao.simulation;

import jakarta.annotation.PostConstruct;

import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.uino.bean.monitor.base.SimulationRuleInfo;
import com.uino.dao.AbstractESBaseDao;
import com.uino.dao.ESConst;

@Service
public class ESSimulationRuleSvc extends AbstractESBaseDao<SimulationRuleInfo, JSONObject> {

	@Override
	public String getIndex() {
		return ESConst.INDEX_UINO_SIMULATION_RULE;
	}

	@Override
	public String getType() {
		return ESConst.INDEX_UINO_SIMULATION_RULE;
	}

	@PostConstruct
	public void init() {
		super.initIndex(5);
	}
}
