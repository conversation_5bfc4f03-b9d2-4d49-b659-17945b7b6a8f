package com.uinnova.product.eam.service.impl;

import com.alibaba.fastjson.JSON;
import com.binary.jdbc.Page;
import com.uinnova.product.eam.base.diagram.model.ESDiagram;
import com.uinnova.product.eam.comm.model.es.*;
import com.uinnova.product.eam.model.FlowProcessSystemPublishHistoryDto;
import com.uinnova.product.eam.model.ObjectMovingDto;
import com.uinnova.product.eam.model.asset.EamReleaseHistoryDTO;
import com.uinnova.product.eam.model.enums.ProcessRootDirectory;
import com.uinnova.product.eam.model.vo.C2CTreeNodeDto;
import com.uinnova.product.eam.service.*;
import com.uinnova.product.eam.service.asset.AssetContent;
import com.uinnova.product.eam.service.diagram.ESDiagramSvc;
import com.uinnova.product.eam.service.es.*;
import com.uinnova.product.eam.service.fx.GeneralPushSvc;
import com.uinnova.product.vmdb.comm.model.ci.CCcCi;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uino.bean.cmdb.base.ESCIHistoryInfo;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.base.LibType;
import com.uino.bean.cmdb.query.ESCISearchBean;
import com.uino.dao.util.ESUtil;
import com.uino.service.cmdb.microservice.ICIClassSvc;
import com.uino.util.sys.SysUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 流程管理/端到端服务层实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class C2CProcessServiceImpl implements C2CProcessService {

    @Resource
    private InstitutionSystemService institutionSystemService;

    @Resource
    private FlowProcessSystemService flowProcessSystemService;

    @Resource
    ICISwitchSvc ciSwitchSvc;

    @Resource
    private FlowSystemApproveDataDao flowSystemApproveDataDao;

    @Resource
    private GeneralPushSvc generalPushSvc;

    @Resource
    private ESDiagramSvc diagramApiClient;

    @Resource
    private EamDiagramRelationSysService eamDiagramRelationSysService;

    @Resource
    private EamDiagramRelationSysDao eamDiagramRelationSysDao;

    @Resource
    private BmDiagramSvc bmDiagramSvc;

    @Resource
    private IamsESCIHistoryDesignSvc ciHistorySvc;

    @Resource
    private FlowProcessSystemPublishHistoryDao flowProcessSystemPublishHistoryDao;

    @Resource
    private EamCategoryDesignDao categoryDesignDao;

    @Resource
    private IamsCIDesignSvc iamsCiDesignSvc;

    @Resource
    private ICIClassSvc iciClassSvc;

    @Autowired
    @Lazy
    IamsESCmdbCommDesignSvc commSvc;

    @Autowired
    private EamDirectoryObjectAssociationDao eamDirectory0bjectAssociationDao;

    @Override
    public void delete( Long id) {
        institutionSystemService.delete(id);
    }

    @Override
    public List<C2CTreeNodeDto> getC2CTree(Long id) {
        List<EamCategory> listByQuery = categoryDesignDao.getListByQuery(QueryBuilders.termQuery("parentId",id));
        ProcessRootDirectory processRootDirectory = ProcessRootDirectory.getByHandleType(id);
        CcCiClassInfo c2cFlowClass = iciClassSvc.getCiClassByClassCode(processRootDirectory.getHandleDesc());
        CCcCi cCcCi = new CCcCi();
        cCcCi.setClassId(c2cFlowClass.getCiClass().getId());
        List<CcCiInfo> ccCiInfos = iamsCiDesignSvc.queryCiInfoList(null, cCcCi, null, false, false);
        Map<String, Long> dirMap = eamDirectory0bjectAssociationDao.getListByCdt(new EamDirectoryObjectAssociation()).stream().collect(Collectors.toMap(EamDirectoryObjectAssociation::getCiCode, EamDirectoryObjectAssociation::getDirectoryId, (k1, k2) -> k1));
        List<CcCiInfo> noGroupList = new ArrayList<>();
        Map<Long, List<CcCiInfo>> ciMap = ccCiInfos.stream().map(e -> {
            CcCiInfo _e = e;
            if (Objects.nonNull(_e.getCi())) {
                Long l = dirMap.get(_e.getCi().getCiCode());
                if(l!=null){
                    _e.setDirId(l);
                }else {
                    noGroupList.add(e);
                }
            }
            return _e;
        }).filter(e -> Objects.nonNull(e.getDirId())).collect(Collectors.groupingBy(CcCiInfo::getDirId));
        List<C2CTreeNodeDto> c2CTreeNodeDtos = buildTree(listByQuery, ciMap);
        C2CTreeNodeDto noGroupNode=null;
        for (C2CTreeNodeDto c2CTreeNodeDto : c2CTreeNodeDtos) {
            if(c2CTreeNodeDto.getName().equalsIgnoreCase("未分组")){
                noGroupNode = c2CTreeNodeDto;
            }
        }
        if(noGroupNode!=null&&!CollectionUtils.isEmpty(noGroupList)){
            List<C2CTreeNodeDto> noGroupCi = new ArrayList<>();
            for (CcCiInfo ciInfo : noGroupList) {
                C2CTreeNodeDto dto = new C2CTreeNodeDto();
                if (Objects.isNull(ciInfo.getCi())) {
                    continue;
                }
                String ciPrimaryKey = ciInfo.getCi().getCiPrimaryKey();
                List<String> objects = JSON.parseArray(ciPrimaryKey,String.class);
                objects.remove(0);
                dto.setId(ciInfo.getCi().getId());
                dto.setName(StringUtils.join(objects," "));
                dto.setCiCode(ciInfo.getCi().getCiCode());
                dto.setCiInfo(ciInfo);
                dto.setType("ci");
                Map<String, String> attrs = ciInfo.getAttrs();
                Map<String, String> listMap = new HashMap<>();
                listMap.put("编写人",attrs.get("编写人"));
                listMap.put("责任人",attrs.get("责任人"));
                dto.setListMap(listMap);
                noGroupCi.add(dto);
            }
            noGroupNode.setChildren(noGroupCi);
            noGroupNode.setCiCount(noGroupCi.size());
        }
        return c2CTreeNodeDtos;
    }

    private List<C2CTreeNodeDto> buildTree(List<EamCategory> categories,Map<Long, List<CcCiInfo>> ciMap) {
        if (categories == null || categories.isEmpty()) {
            return Collections.emptyList();
        }

        List<C2CTreeNodeDto> treeDto = new ArrayList<>();
        for (EamCategory category : categories) {
            C2CTreeNodeDto treeDto1 = new C2CTreeNodeDto();
            treeDto1.setName(category.getDirName());
            treeDto1.setId(category.getId());
            treeDto1.setType("dir");
            // 查询子目录
            List<C2CTreeNodeDto> children = buildTree(categoryDesignDao.getListByQuery(QueryBuilders.termsQuery("parentId", (Object) category.getId())), ciMap);
            if (CollectionUtils.isEmpty(children)) {
                children = new ArrayList<>();
            }

            // 获取当前目录下的CI列表
            List<CcCiInfo> ciList = ciMap.getOrDefault(category.getId(), Collections.emptyList());

            // 统计当前目录及其子目录下的所有CI数量
            int totalCiCount = ciList.size();
            for (C2CTreeNodeDto child : children) {
                if ("dir".equals(child.getType())) {
                    totalCiCount += child.getCiCount() != null ? child.getCiCount() : 0;
                }
            }
            treeDto1.setCiCount(totalCiCount);
            // 添加CI节点
            for (CcCiInfo ciInfo : ciList) {
                C2CTreeNodeDto dto = new C2CTreeNodeDto();
                if (Objects.isNull(ciInfo.getCi())) {
                    continue;
                }
                String ciPrimaryKey = ciInfo.getCi().getCiPrimaryKey();
                List<String> objects = JSON.parseArray(ciPrimaryKey,String.class);
                objects.remove(0);
                dto.setId(ciInfo.getCi().getId());
                dto.setName(StringUtils.join(objects," "));
                dto.setCiCode(ciInfo.getCi().getCiCode());
                dto.setType("ci");
                dto.setCiInfo(ciInfo);
                Map<String, String> attrs = ciInfo.getAttrs();
                Map<String, String> listMap = new HashMap<>();
                listMap.put("编写人",attrs.get("编写人"));
                listMap.put("责任人",attrs.get("责任人"));
                dto.setListMap(listMap);
                children.add(dto);
            }
            // CI信息 目录信息
            if (!children.isEmpty()) {
                // 对子节点进行排序
                Collections.sort(children, (o1, o2) -> {
                    String type = o1.getType();
                    String type1 = o2.getType();
                    if(!type.equalsIgnoreCase(type1)){
                        return o2.getType().compareTo(o1.getType());
                    }
                    if(type.equalsIgnoreCase("ci")){
                        try {
                            String[] split = o1.getName().split(" ");
                            String[] c2cCodeArr = split[0].split("\\.");
                            String[] split1 = o2.getName().split(" ");
                            String[] c2cCodeArr2 = split1[0].split("\\.");
                            int maxLength = Math.max(c2cCodeArr.length, c2cCodeArr2.length);
                            for (int i = 0; i < maxLength; i++) {
                                int part1 = i < c2cCodeArr.length ? Integer.parseInt(c2cCodeArr[i]) : 0;
                                int part2 = i < c2cCodeArr2.length ? Integer.parseInt(c2cCodeArr2[i]) : 0;
                                if (part1 > part2) {
                                    return 1;
                                } else if (part1 < part2) {
                                    return -1;
                                }
                            }
                            return 0;
                        }catch (Exception e){
                            if(o1.getName().compareTo(o2.getName())!=0){
                                return o1.getName().compareTo(o2.getName());
                            }else {
                                return -1;
                            }
                        }
                    }else {
                        return o1.getId().compareTo(o2.getId());
                    }
                });
                treeDto1.setChildren(children);
            }
            treeDto.add(treeDto1);
        }
        // 对当前层级的节点进行排序
        Collections.sort(treeDto, Comparator.comparing(C2CTreeNodeDto::getId));
        return treeDto;
    }

    @Override
    public Page<CcCiInfo> queryChildInfoPageById(Long id, Integer pageNum, Integer pageSize, String words) {
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("dataStatus", 1));
        query.must(QueryBuilders.wildcardQuery("dirPath.keyword", "*#" + id + "#*"));
        List<EamCategory> categories = categoryDesignDao.getListByQuery(query);
        List<Long> dirIds = categories.stream().map(EamCategory::getId).collect(Collectors.toList());
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.termsQuery("directoryId", dirIds));
        List<EamDirectoryObjectAssociation> listByQueryScroll = eamDirectory0bjectAssociationDao.getListByQueryScroll(boolQueryBuilder);
        if (CollectionUtils.isEmpty(listByQueryScroll)) {
            return new Page<>(pageNum, pageSize, 0, 0, new ArrayList<>());
        }
        Set<String> allCiCodes = listByQueryScroll.stream().map(EamDirectoryObjectAssociation::getCiCode).collect(Collectors.toSet());
        ESCISearchBean esciSearchBean = new ESCISearchBean();
        esciSearchBean.setCiCodes(new ArrayList<>(allCiCodes));
        esciSearchBean.setPageNum(pageNum);
        esciSearchBean.setPageSize(pageSize);
        if (StringUtils.isNotBlank(words)) {
            esciSearchBean.setWords(Collections.singletonList(words));
        }
        Page<ESCIInfo> esciInfoPage = iamsCiDesignSvc.searchESCIByBean(esciSearchBean);
        List<ESCIInfo> data = esciInfoPage.getData();
        List<CcCiInfo> ccCiInfos = commSvc.transEsInfoList(data, false);

        if(!CollectionUtils.isEmpty(ccCiInfos)) {
            Collections.sort(ccCiInfos, (c1, c2) -> {
                String s1 = c1.getAttrs().get("端到端流程编码");
                String s2 = c2.getAttrs().get("端到端流程编码");
                try {
                    String[] c2cCodeArr = s1.split("\\.");
                    String[] c2cCodeArr2 = s2.split("\\.");
                    int maxLength = Math.max(c2cCodeArr.length, c2cCodeArr2.length);
                    for (int i = 0; i < maxLength; i++) {
                        int part1 = i < c2cCodeArr.length ? Integer.parseInt(c2cCodeArr[i]) : 0;
                        int part2 = i < c2cCodeArr2.length ? Integer.parseInt(c2cCodeArr2[i]) : 0;
                        if (part1 > part2) {
                            return 1;
                        } else if (part1 < part2) {
                            return -1;
                        }
                    }
                    return 0;
                } catch (Exception e) {
                    if (s1.compareTo(s2) != 0) {
                        return s1.compareTo(s2);
                    } else {
                        return -1;
                    }
                }
            });
        }

        Page<CcCiInfo> ccCiInfoPage = new Page<>();
        ccCiInfoPage.setPageNum(esciInfoPage.getPageNum());
        ccCiInfoPage.setPageSize(esciInfoPage.getPageSize());
        ccCiInfoPage.setTotalRows(esciInfoPage.getTotalRows());
        ccCiInfoPage.setTotalPages(esciInfoPage.getTotalPages());
        ccCiInfoPage.setData(ccCiInfos);
        return ccCiInfoPage;
    }

    @Override
    public Long saveOrUpdateCategory(EamCategory vo) {
        return institutionSystemService.saveOrUpdateCategory(vo);
    }

    @Override
    public Integer moveCi(ObjectMovingDto objectMovingDto) {
        return institutionSystemService.moveCi(objectMovingDto);
    }

    @Override
    public void publishC2CFlowProcessSystem(String ciCode,String loginCode,String publishType) {
        List<String> changeModel = new ArrayList<>();
        FlowProcessSystemPublishHistory flowProcessSystemPublishHistory = new FlowProcessSystemPublishHistory();
        CcCiInfo privateCiInfo = ciSwitchSvc.getCiByCode(ciCode, loginCode, LibType.PRIVATE);
        CcCiInfo designCiInfo = ciSwitchSvc.getCiByCode(ciCode, null, LibType.DESIGN);
        if(privateCiInfo==null){
            flowProcessSystemPublishHistory.setFlowName(designCiInfo.getAttrs().get("端到端流程编码") + " " + designCiInfo.getAttrs().get("端到端流程名称"));
        }else {
            flowProcessSystemPublishHistory.setFlowName(privateCiInfo.getAttrs().get("端到端流程编码") + " " + privateCiInfo.getAttrs().get("端到端流程名称"));
        }
        //1、发布视图
        String publishedDiagramId = publishDiagram(ciCode, "提交", "c2cFlowDiagram", loginCode);
        if (publishedDiagramId != null) {
            changeModel.add("流程图");
            flowProcessSystemPublishHistory.setC2cFlowDiagram(publishedDiagramId);
        }

        //2、发布ci
        Map<String, Long> stringLongMap = publishCiCode(ciCode, "", loginCode);
        if (stringLongMap != null) {
            if (stringLongMap.get("change") == 1L) {
                changeModel.add("流程属性");
            }
            flowProcessSystemPublishHistory.setCiHistoryId(stringLongMap.get("versionId"));
        }

        flowProcessSystemPublishHistory.setCiCode(ciCode);
        flowProcessSystemPublishHistory.setPublishReason("");
        flowProcessSystemPublishHistory.setCheckUser(SysUtil.getCurrentUserInfo().getLoginCode());
        flowProcessSystemPublishHistory.setCreator(SysUtil.getCurrentUserInfo().getLoginCode());
        flowProcessSystemPublishHistory.setPublishType(publishType);
        if (CollectionUtils.isEmpty(changeModel)) {
            changeModel.add("-");
        }
        if ("submit".equalsIgnoreCase(publishType)) {
            flowProcessSystemPublishHistory.setChangeData("修改并提交" + flowProcessSystemPublishHistory.getFlowName() + "【" + org.apache.commons.lang3.StringUtils.join(changeModel, "、") + "】");
            //生成下快照数据
            FlowSystemApproveData flowSystemApproveData = new FlowSystemApproveData();
            if (publishedDiagramId == null) {
                //视图没有更新过，查询最新绑定的视图
                List<EamDiagramRelationSys> diagramRelationSysList = eamDiagramRelationSysService.findFlowSystemDiagramRelation(ciCode, "c2cFlowDiagram");
                if (!CollectionUtils.isEmpty(diagramRelationSysList)) {
                    EamDiagramRelationSys eamDiagramRelationSys = diagramRelationSysList.get(0);
                    flowProcessSystemPublishHistory.setC2cFlowDiagram(eamDiagramRelationSys.getDiagramEnergy());
                }
            } else {
                //视图有更新，查询视图的发布历史，刷新端到端的快照数据
                flowProcessSystemPublishHistory.setC2cFlowDiagram(publishedDiagramId);
                List<FlowProcessSystemPublishHistoryDto> flowSystemPublishHistory2 = flowProcessSystemService.getFlowSystemPublishHistory(ciCode, null);
                if(!CollectionUtils.isEmpty(flowSystemPublishHistory2)) {
                    BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
                    boolQueryBuilder.filter(QueryBuilders.termQuery("ciCode.keyword",ciCode));
                    boolQueryBuilder.filter(QueryBuilders.termQuery("c2cFlowDiagram.keyword",publishedDiagramId));
                    List<FlowSystemApproveData> listByQueryScroll = flowSystemApproveDataDao.getListByQueryScroll(boolQueryBuilder);
                    if(!CollectionUtils.isEmpty(listByQueryScroll)){
                        List<EamReleaseHistoryDTO> eamReleaseHistoryDTOS = bmDiagramSvc.queryReleaseHistory(publishedDiagramId, false);
                        if (!CollectionUtils.isEmpty(eamReleaseHistoryDTOS) && eamReleaseHistoryDTOS.size() > 1) {
                            EamReleaseHistoryDTO eamReleaseHistoryDTO = eamReleaseHistoryDTOS.get(eamReleaseHistoryDTOS.size() - 2);
                            for (FlowSystemApproveData systemApproveData : listByQueryScroll) {
                                systemApproveData.setC2cFlowDiagram(eamReleaseHistoryDTO.getReleaseInfo().getDEnergy());
                            }
                            flowSystemApproveDataDao.saveOrUpdateBatch(listByQueryScroll);
                        }
                    }
                }
            }

            flowSystemApproveData.setC2cFlowDiagram(flowProcessSystemPublishHistory.getC2cFlowDiagram());
            flowSystemApproveData.setCiCode(ciCode);
            CcCiInfo newDesignCiInfo = ciSwitchSvc.getCiByCode(ciCode, null, LibType.DESIGN);
            flowSystemApproveData.setCiInfo(newDesignCiInfo);
            Long l = flowSystemApproveDataDao.saveOrUpdate(flowSystemApproveData);
            flowProcessSystemPublishHistory.setFlowSystemApproveDataId(l);
        } else {
            flowProcessSystemPublishHistory.setChangeData("修改并审批" + flowProcessSystemPublishHistory.getFlowName() + "【" + org.apache.commons.lang3.StringUtils.join(changeModel, "、") + "】");
        }
        flowProcessSystemPublishHistoryDao.saveOrUpdate(flowProcessSystemPublishHistory);
    }


    private String publishDiagram(String ciCode, String publishReason, String diagramClassType, String loginCode) {
        //发布完视图将关联表中的视图id替换掉
        List<EamDiagramRelationSys> diagramRelationSysList = eamDiagramRelationSysService.findFlowSystemDiagramRelation(ciCode, diagramClassType);
        List<EamDiagramRelationSys> diagramRelationSysPirvateList = eamDiagramRelationSysService
                .findFlowSystemDiagramRelationPrivateByOwnerCode(ciCode, loginCode, diagramClassType);
        if(CollectionUtils.isEmpty(diagramRelationSysPirvateList)){
            return null;
        }
        String diagramEnergy = diagramRelationSysPirvateList.get(0).getDiagramEnergy();
        ESDiagram esDiagram = diagramApiClient.getEsDiagram(diagramEnergy, 0);
        if (esDiagram.getLocalVersion() != 0) {
            EamDiagramRelationSys eamDiagramRelationSys;
            if (!CollectionUtils.isEmpty(diagramRelationSysList)) {
                eamDiagramRelationSys = diagramRelationSysList.get(0);
            } else {
                eamDiagramRelationSys = new EamDiagramRelationSys();
                eamDiagramRelationSys.setId(ESUtil.getUUID());
                eamDiagramRelationSys.setEsSysId(ciCode);
                eamDiagramRelationSys.setDiagramClassType(diagramClassType);
                eamDiagramRelationSys.setDelFlag(false);
                eamDiagramRelationSys.setCreator(SysUtil.getCurrentUserInfo().getLoginCode());
                eamDiagramRelationSys.setDirId(-100L);
            }
            String publishDiagramEnergy = generalPushSvc.publishDiagram(diagramRelationSysPirvateList.get(0).getDiagramEnergy(), publishReason, -100L, null, false);
            eamDiagramRelationSys.setDiagramEnergy(publishDiagramEnergy);
            eamDiagramRelationSysDao.saveOrUpdate(eamDiagramRelationSys);
            return publishDiagramEnergy;
        } else {
            // 视图没有被修改过就不更新
            return null;
        }
    }

    private Map<String, Long> publishCiCode(String ciCode, String publishReason, String loginCode) {
        HashMap<String, Long> stringObjectHashMap = new HashMap<>();
        stringObjectHashMap.put("change", 0L);
        CcCiInfo privateCiInfo = ciSwitchSvc.getCiByCode(ciCode, loginCode, LibType.PRIVATE);
        if (privateCiInfo != null) {
            CcCiInfo designCiInfo = ciSwitchSvc.getCiByCode(ciCode, null, LibType.DESIGN);
            String dateTime = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(LocalDateTime.now());
            if (designCiInfo == null) {
                privateCiInfo.getCi().setId(null);
                privateCiInfo.getAttrs().put(AssetContent.MODIFI_TIME, dateTime);
                privateCiInfo.getAttrs().put(AssetContent.RELEASE_TIME, dateTime);
                privateCiInfo.getAttrs().put(AssetContent.RELEASE_STATE, AssetContent.RELEASE);
                privateCiInfo.getAttrs().put(AssetContent.CREATION_TIME, dateTime);
                privateCiInfo.getAttrs().put(AssetContent.CHANGE_RECORD, publishReason);
                ciSwitchSvc.saveOrUpdateCI(privateCiInfo, LibType.DESIGN);
            } else {
                Map<String, String> designAttrs = designCiInfo.getAttrs();
                Map<String, String> privateCiInfoAttrs = privateCiInfo.getAttrs();
                for (String s : AssetContent.IGNORE_ATTR) {
                    designAttrs.remove(s);
                    privateCiInfoAttrs.remove(s);
                }
                boolean equals = designAttrs.equals(privateCiInfoAttrs);
                if (!equals) {
                    stringObjectHashMap.put("change", 1L);
                    privateCiInfo.getAttrs().put(AssetContent.MODIFI_TIME, dateTime);
                    privateCiInfo.getAttrs().put(AssetContent.RELEASE_TIME, dateTime);
                    privateCiInfo.getAttrs().put(AssetContent.RELEASE_STATE, AssetContent.RELEASE);
                    privateCiInfo.getAttrs().put(AssetContent.CREATION_TIME, dateTime);
                    privateCiInfo.getAttrs().put(AssetContent.CHANGE_RECORD, publishReason);
                    designCiInfo.setAttrs(privateCiInfo.getAttrs());
                    designCiInfo.getCi().setOwnerCode(loginCode);
                    ciSwitchSvc.saveOrUpdateCI(designCiInfo, LibType.DESIGN);
                    privateCiInfo.getCi().setPublicVersion(designCiInfo.getCi().getPublicVersion());
                    ciSwitchSvc.saveOrUpdateCI(privateCiInfo, LibType.PRIVATE);
                }
            }
        }

        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.termQuery("ciCode.keyword", ciCode));
        List<ESCIHistoryInfo> version = ciHistorySvc.getSortListByQueryScroll(boolQueryBuilder, "version", false);
        if (CollectionUtils.isEmpty(version)) {
            return null;
        }
        Long id = version.get(0).getId();
        stringObjectHashMap.put("versionId", id);
        return stringObjectHashMap;
    }

}
