package com.uino.web.cmdb.mvc;

import java.io.IOException;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import com.uino.init.api.ApiResult;
import com.uino.init.api.ApiVersion;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.http.HttpStatus;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.binary.core.exception.MessageException;
import com.binary.framework.util.ControllerUtils;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uino.web.BaseMvc;

/**
 * 接口超市
 *
 * <AUTHOR>
 * @data 2019/8/19 16:16.
 */
@ApiVersion(1)
@Api(value = "接口超市", tags = {"接口超市"})
@RestController
@RequestMapping("cmdb/postman")
@RefreshScope
public class PostmanMvc extends BaseMvc {

    @Value("${permission.http.prefix:}")
    private String httpPrefixUrl;

    /**
     * 获取HTTP PREFIX
     *
     * @param
     */
    @ApiOperation("获取公共组件访问前缀")
    @RequestMapping(value="getHttpPrefix",method= RequestMethod.POST)
    @ModDesc(desc = "获取公共组件访问前缀", pDesc = "无", pType = JSONObject.class, rDesc = "公共组件访问前缀", rType = String.class)
    public ApiResult<String> getHttpPrefix(HttpServletRequest request, HttpServletResponse response, @RequestBody JSONObject json) {
        return ApiResult.ok(this).data(httpPrefixUrl);
    }

    /**
     * 接口超市
     *
     * @param json
     */
    @RequestMapping("getCode")
    public void getCode(HttpServletRequest request, HttpServletResponse response, @RequestBody JSONObject json) {
        String uri;
        if (json.containsKey("uri")) {
            uri = json.getString("uri");
        } else {
            throw MessageException.i18n("BS_MVTYPE_ARG_ERROR");
        }
        int type;
        if (json.containsKey("type")) {
            type = json.getInteger("type");
        } else {
            throw MessageException.i18n("BS_MVTYPE_ARG_ERROR");
        }
        JSONObject param = null;
        if (json.containsKey("param")) {
            param = json.getJSONObject("param");
        }
        String token = getTokenByLogin(httpPrefixUrl, JSONObject.parseObject("{\"loginCode\":\"admin\",\"password\":\"4O4HHUgnvTPTF8Gu9uvSVw==\"}"));
        String s = jointCode(httpPrefixUrl + uri, type, param, token);
        System.out.println(s);
        ControllerUtils.returnJson(request, response, s);

    }

    private String jointCode(String url, int type, JSONObject param, String token) {
        String s = null;
        if (type == PostmanEnum.CURL.getType()) {
            s = getCurlCode(url, param, token);
        }
        if (type == PostmanEnum.JAVASCRIPT.getType()) {
            s = getJavascriptCode(url, param, token);
        }
        if (type == PostmanEnum.JAVA.getType()) {
            s = getJavaCode(url, param, token);
        }
        if (type == PostmanEnum.GO.getType()) {
            s = getGolangCode(url, param, token);
        }
        return s;
    }

    /**
     * 格式化json
     *
     * @param object object
     * @return
     */
    private String jsonFormat(JSONObject object) {
        String jsonString = JSON.toJSONString(object, SerializerFeature.PrettyFormat, SerializerFeature.WriteMapNullValue, SerializerFeature.WriteDateUseDateFormat);
        return jsonString;
    }

    /**
     * curl格式
     *
     * @param url    地址
     * @param params 参数
     * @return 结果
     */
    private String getCurlCode(String url, JSONObject params, String token) {
        StringBuilder code = new StringBuilder();
        code.append("curl -X POST \\\n");
        code.append("    " + url + " \\\n");
        code.append("    -H 'Content-Type:application/json' \\\n");
        code.append("    -H 'REQUEST_HEADER:binary-http-client-header' \\\n");
        code.append("    -H 'token:" + token + "' \\\n");
        code.append("    -H 'tk:" + token + "' \\\n");
        code.append("    -d '");
        String s = jsonFormat(params);
        s = s.replaceAll("\t", "\t\t");
        s = s.replace("}", "\t}");
        code.append(s);
        code.append("'");
        return code.toString();
    }

    /**
     * js格式
     *
     * @param url
     * @param params
     * @return
     */
    private String getJavascriptCode(String url, JSONObject params, String token) {
        StringBuilder code = new StringBuilder();
        code.append("var settings = {\n");
        code.append("    \"async\": true,\n");
        code.append("    \"crossDomain\": true,\n");
        code.append("    \"url\": \"" + url + "\",\n");
        code.append("    \"method\": \"POST\",\n");
        code.append("    \"headers\": {\n");
        code.append("        \"Content-Type\": \"application/json\",\n");
        code.append("        \"REQUEST_HEADER\": \"binary-http-client-header\",\n");
        code.append("        \"token\": \"" + token + "\"\n");
        code.append("        \"tk\": \"" + token + "\"\n");
        code.append("    },\n");
        code.append("    \"data\":\"");
        String s = jsonFormat(params);
        s = s.replaceAll("\\\\\"", "\\\\\\\\\"");
        s = s.replaceAll("\"", "\\\\\"");
        s = s.replaceAll("\n", "\" +\n\t\t\t\"");   //
        code.append(s);
        code.append("\"\n");
        code.append("}\n");
        code.append("$.ajax(settings).done(function (response) {\n");
        code.append("    console.log(response);\n");
        code.append("});");
        return code.toString();
    }

    /**
     * Java格式
     *
     * @param url
     * @param params
     * @return
     */
    private String getJavaCode(String url, JSONObject params, String token) {
        StringBuilder code = new StringBuilder();
        code.append("CloseableHttpClient httpClient = HttpClients.createDefault();\n");
        code.append("CloseableHttpResponse response = null;\n");
        code.append("String result = \"\";\n");
        code.append("try {\n");
        code.append("    HttpPost httpPost = new HttpPost(\"" + url + "\");\n");
        code.append("    httpPost.setHeader(\"Content-Type\", \"application/json\");\n");
        code.append("    httpPost.setHeader(\"REQUEST_HEADER\", \"binary-http-client-header\");\n");
        code.append("    httpPost.setHeader(\"token\", \"" + token + "\");\n");
        code.append("    httpPost.setHeader(\"tk\", \"" + token + "\");\n");
        code.append("    StringEntity entity = new StringEntity(\"");
        String s = jsonFormat(params);
        s = s.replaceAll("\\\\\"", "\\\\\\\\\"");
        s = s.replaceAll("\"", "\\\\\"");
        s = s.replaceAll("\n", "\" +\n\t\t\t\"");
        code.append(s);
        code.append("\", \"utf-8\");\n");
        code.append("    httpPost.setEntity(entity);\n");
        code.append("    response = httpClient.execute(httpPost);\n");
        code.append("    int responseCode = response.getStatusLine().getStatusCode();\n");
        code.append("    if (responseCode == 200) {\n");
        code.append("        String respBody = EntityUtils.toString(response.getEntity(), \"utf-8\");\n");
        code.append("        System.out.println(respBody);\n");
        code.append("    }\n");
        code.append("} catch (Exception e) {\n");
        code.append("    e.printStackTrace();\n");
        code.append("} finally {\n");
        code.append("    try {\n");
        code.append("        if (response != null) {\n");
        code.append("            response.close();\n");
        code.append("        }\n");
        code.append("        response = null;\n");
        code.append("        if (httpClient != null) {\n");
        code.append("            httpClient.close();\n");
        code.append("        }\n");
        code.append("        httpClient = null;\n");
        code.append("    } catch (IOException e) {\n");
        code.append("        e.printStackTrace();\n");
        code.append("    }\n");
        code.append("}");
        return code.toString();
    }

    /**
     * go格式
     *
     * @param url
     * @param params
     * @return
     */
    private String getGolangCode(String url, JSONObject params, String token) {
        StringBuilder code = new StringBuilder();
        code.append("package main\n");
        code.append("\n");
        code.append("import (\n");
        code.append("    \"fmt\"\n");
        code.append("    \"strings\"\n");
        code.append("    \"net/http\"\n");
        code.append("    \"io/ioutil\"\n");
        code.append(")\n");
        code.append("\n");
        code.append("func main() {\n");
        code.append("    url := \"" + url + "\"\n");
        code.append("    payload := strings.NewReader(`");
        String s = jsonFormat(params);
        code.append(s);
        code.append("`)\n");
        code.append("    req, _ := http.NewRequest(\"POST\", url, payload)\n");
        code.append("    req.Header.Add(\"Content-Type\", \"application/json\")\n");
        code.append("    req.Header.Add(\"REQUEST_HEADER\", \"binary-http-client-header\")\n");
        code.append("    req.Header.Add(\"token\", \"" + token + "\")\n");
        code.append("    req.Header.Add(\"tk\", \"" + token + "\")\n");
        code.append("    res, _ := http.DefaultClient.Do(req)\n");
        code.append("    defer res.Body.Close()\n");
        code.append("    body, _ := ioutil.ReadAll(res.Body)\n");
        code.append("    fmt.Println(res)\n");
        code.append("    fmt.Println(string(body))\n");
        code.append("}");
        return code.toString();
    }


    public String getTokenByLogin(String url, JSONObject jsonParam) {
        // post请求返回结果
        CloseableHttpClient httpClient = HttpClients.createDefault();
        JSONObject jsonResult = null;
        String token = null;
        HttpPost httpPost = new HttpPost(url + "/dcv/user/oauth/login");
        // 设置请求和传输超时时间
        httpPost.setConfig(RequestConfig.custom().setSocketTimeout(2000).setConnectTimeout(2000).build());
        try {
            if (null != jsonParam) {
                // 解决中文乱码问题
                StringEntity entity = new StringEntity(jsonParam.toString(), "utf-8");
                entity.setContentEncoding("UTF-8");
                entity.setContentType("application/json");
                httpPost.setEntity(entity);
            }
            CloseableHttpResponse result = httpClient.execute(httpPost);
            // 请求发送成功，并得到响应
            if (result.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                String str = "";
                try {
                    // 读取服务器返回过来的json字符串数据
                    str = EntityUtils.toString(result.getEntity(), "utf-8");
                    // 把json字符串转换成json对象
                    jsonResult = JSONObject.parseObject(str);
                    String tk = jsonResult.getJSONObject("data").getString("token");
                    if (tk == null || "".equals(tk)) {
                        tk = jsonResult.getJSONObject("data").getString("tk");
                    }
                    return tk;

                } catch (Exception e) {
					throw new MessageException(e.getMessage());
                }
            }
        } catch (IOException e) {
			throw new MessageException(e.getMessage());
        } finally {
            httpPost.releaseConnection();
        }
        return token;
    }

    public enum PostmanEnum {
        CURL(1),
        JAVASCRIPT(2),
        JAVA(3),
        GO(4);

        private int type;

        PostmanEnum(int type) {
            this.type = type;
        }

        public int getType() {
            return type;
        }

        public void setType(int type) {
            this.type = type;
        }
    }
}
