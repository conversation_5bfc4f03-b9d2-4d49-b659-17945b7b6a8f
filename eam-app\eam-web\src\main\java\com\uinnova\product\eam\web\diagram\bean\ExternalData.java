package com.uinnova.product.eam.web.diagram.bean;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

import com.binary.framework.bean.annotation.Comment;

public class ExternalData implements Serializable {
	private static final long serialVersionUID = 2661305243035519642L;

	@Comment("标题")
	private String title;
	
	@Comment("外接数据键值")
	private List<Map<String,Object>> data;
	
	@Comment("外接数据面键显示值")
	private Map<String,Object> modal;
	
	@Comment("外接数据类型，来源于配置文件project.diagram.externalData")
	private String dataType;
	
	@Comment("外接数据气泡显示数量")
	private Integer count;
	
	@Comment("所属ciCode")
	private String ciCode;
	
	public String getCiCode() {
		return ciCode;
	}

	public void setCiCode(String ciCode) {
		this.ciCode = ciCode;
	}

	public Integer getCount() {
		return count;
	}

	public void setCount(Integer count) {
		this.count = count;
	}

	public String getDataType() {
		return dataType;
	}

	public void setDataType(String dataType) {
		this.dataType = dataType;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public List<Map<String, Object>> getData() {
		return data;
	}

	public void setData(List<Map<String, Object>> data) {
		this.data = data;
	}

	public Map<String, Object> getModal() {
		return modal;
	}

	public void setModal(Map<String, Object> modal) {
		this.modal = modal;
	}
	
}
