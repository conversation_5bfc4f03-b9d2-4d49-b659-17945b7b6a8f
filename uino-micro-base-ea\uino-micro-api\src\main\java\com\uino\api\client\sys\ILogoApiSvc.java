package com.uino.api.client.sys;

import java.util.Map;

import org.springframework.web.multipart.MultipartFile;

import com.uino.bean.sys.base.Logo;

/**
 * 门户图标操作
 * 
 * <AUTHOR>
 */
public interface ILogoApiSvc {

    /**
     * 获取logo信息
     * 
     * @return {logoType:{logoObj}}
     */
    Map<String, Logo> getLogos();


    /**
     * 修改logo
     * 
     * @param logoType
     *            要修改的logo类型
     * @param file
     *            新logo文件
     * @return 修改后的logo信息
     */
    Map<String, Logo> updateLogo(String logoType, MultipartFile file);

    /**
     * 删除logo
     * 
     * @param logoType
     * @return
     */
    Map<String, Logo> deleteLogo(String logoType);

    /**
     * 通过图片地址更新系统Logo
     * @param logoType 类型
     * @param path 图片地址
     * @param fileId 文件id
     * @return logo信息
     */
    Map<String, Logo> updateLogoByPath(String logoType, String path, Long fileId);
}
