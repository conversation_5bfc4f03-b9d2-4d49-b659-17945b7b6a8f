package com.uino.bean.cmdb.base.dataset.query;

import com.uino.bean.cmdb.base.dataset.DataSetMallApi;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @Title: DataSetMallApiDto
 * @Description: DataSetMallApiDto
 * @Author: YGQ
 * @Create: 2021-05-31 14:00
 **/
@Getter
@Setter
@ApiModel(value = "数据超市", description = "数据超市")
public class DataSetMallApiDto {

    @ApiModelProperty(value = "数据集")
    private DataSetMallApi dataSetMallApi;

    @ApiModelProperty(value = "用户标识")
    private String userCode;
}
