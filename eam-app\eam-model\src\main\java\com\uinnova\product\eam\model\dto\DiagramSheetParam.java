package com.uinnova.product.eam.model.dto;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.base.diagram.model.ESDiagramLink;
import com.uinnova.product.eam.base.diagram.model.ESDiagramNode;
import com.uinnova.product.eam.base.diagram.model.ESDiagramSheetDTO;
import com.uinnova.product.eam.model.CiInfoExtend;

import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.base.ESCIRltInfo;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *  视图sheet相关参数
 */
@Data
public class DiagramSheetParam {

    @Comment("视图ID")
    private String diagramId;

    @Comment("视图long的id")
    private Long id;

    @Comment("视图sheet页ID")
    private String sheetId;

    @Comment("视图ownerCode")
    private String ownerCode;

    @Comment("sheet页内是否含有对象数据 true-有 false-没有")
    private Boolean hasCIData = Boolean.FALSE;

    @Comment("源sheet页内的CI数据集合")
    private List<ESCIInfo> sourceCIList;

    @Comment("源sheet页内的RLT数据集合")
    private List<ESCIRltInfo> sourceRltList;

    @Comment("源CI标识与副本CI数据的map")
    private Map<String, CiInfoExtend> sourceCiCodeAndCopyCIMap = new HashMap<>();

    @Comment("源RLT标识(带UK_的)与副本RLT数据的map")
    private Map<String, ESCIRltInfo> sourceRltCodeAndCopyRltMap = new HashMap<>();

    @Comment("源sheet页内全量node")
    private List<ESDiagramNode> allNodeList = new ArrayList<>();
    @Comment("源sheet页内数据node")
    private List<ESDiagramNode> dataNodeList = new ArrayList<>();

    @Comment("源sheet页内全量link")
    private List<ESDiagramLink> allLinkList = new ArrayList<>();
    @Comment("源sheet页内数据link")
    private List<ESDiagramLink> dataLinkList = new ArrayList<>();

    @Comment("副本sheet页信息")
    private ESDiagramSheetDTO copySheetInfo;

    @Comment("副本sheet页node/link信息")
    private ExtendSheetInfo copySheetDataInfo;

}

