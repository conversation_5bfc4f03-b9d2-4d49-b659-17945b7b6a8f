package com.uino.util.encrypt.impl;


import com.uino.util.encrypt.Encrypt;
import com.uino.util.encrypt.EncryptType;
import com.uino.util.encrypt.impl.type.AesUtil;
import com.uino.util.digest.impl.type.Base64Util;
import com.uino.util.encrypt.impl.type.DesUtil;
import com.uino.util.encrypt.impl.type.JasyptUtil;
import com.uino.util.encrypt.impl.type.RsaUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.stereotype.Service;

/**
 * @Title: Encrypt
 * @Author: YGQ
 * @Create: 2021-08-08 22:34
 **/
@Service
@ComponentScan(basePackages = {"com.uino.util.encrypt"})
public class EncryptImpl implements Encrypt {
    private final JasyptUtil jasyptUtil;

    @Override
    public String encryptOrDecrypt(EncryptType type, String str, String... publicKey) {
        String ret = null;
        switch (type) {
            case RsaEncrypt:
                // need public key
                ret = RsaUtil.encrypt(RsaUtil.getPublicKey(checkPublicKey(publicKey)), str);
                break;
            case RsaDecrypt:
                // need public key
                ret = RsaUtil.decrypt(RsaUtil.getPrivateKey(checkPublicKey(publicKey)), str);
                break;
            case JasyptEncrypt:
                // need public key
                ret = jasyptUtil.stringEncryptor(checkPublicKey(publicKey), str, true);
                break;
            case JasyptDecrypt:
                // need public key
                ret = jasyptUtil.stringEncryptor(checkPublicKey(publicKey), str, false);
                break;
            case DesEncrypt:
                // public key needs 8 bits
                ret = DesUtil.encrypt(checkPublicKey(publicKey), str);
                break;
            case DesDecrypt:
                // public key needs 8 bits
                ret = DesUtil.decrypt(checkPublicKey(publicKey), str);
                break;
            case AesEncrypt:
                // public key requires 16 bits
                ret = AesUtil.encrypt(checkPublicKey(publicKey), str);
                break;
            case AesDecrypt:
                // public key requires 16 bits
                ret = AesUtil.decrypt(checkPublicKey(publicKey), str);
                break;
            case Base64Encrypt:
                ret = Base64Util.base64(str);
                break;
            case Base64Decrypt:
                ret = Base64Util.base64dec(str);
                break;
            default:
                break;
        }
        return ret;
    }


    @Autowired
    public EncryptImpl(JasyptUtil jasyptUtil) {
        this.jasyptUtil = jasyptUtil;
    }

    /**
     * public key detection
     *
     * @param publicKey public key
     */
    private String checkPublicKey(String... publicKey) {
        if (null != publicKey && publicKey.length > 0) {
            return publicKey[0];
        } else {
            throw new IllegalArgumentException("public is not null");
        }
    }
}
