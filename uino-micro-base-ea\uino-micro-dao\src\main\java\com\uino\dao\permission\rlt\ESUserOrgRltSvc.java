package com.uino.dao.permission.rlt;

import java.util.ArrayList;
import java.util.List;

import jakarta.annotation.PostConstruct;

import org.elasticsearch.index.query.BoolQueryBuilder;
import org.springframework.stereotype.Service;

import com.uino.dao.AbstractESBaseDao;
import com.uino.dao.ESConst;
import com.uino.dao.util.ESUtil;
import com.uino.util.sys.CommonFileUtil;
import com.uino.bean.permission.base.SysUserOrgRlt;
import com.uino.bean.permission.query.CSysUserOrgRlt;

/**
 * <b>用户-组织关系
 * 
 * <AUTHOR>
 */
@Service
public class ESUserOrgRltSvc extends AbstractESBaseDao<SysUserOrgRlt, CSysUserOrgRlt> {

    @Override
    public String getIndex() {
        return ESConst.INDEX_SYS_USER_ORG_RLT;
    }

    @Override
    public String getType() {
        return ESConst.INDEX_SYS_USER_ORG_RLT;
    }

    @PostConstruct
    public void init() {
		List<SysUserOrgRlt> data = CommonFileUtil.getData("/initdata/uino_sys_user_org_rlt.json", SysUserOrgRlt.class);
        super.initIndex(data);
    }

    public Integer removeRltByCdt(CSysUserOrgRlt cdt) {
        BoolQueryBuilder query = ESUtil.cdtToBuilder(cdt);
        return deleteByQuery(query, true);
    }

    public Integer saveOrUpdateRlt(Long userId, Long[] orgIds) {
        List<SysUserOrgRlt> records = new ArrayList<>();
        for (Long orgId : orgIds) {
            SysUserOrgRlt userOrgRlt = new SysUserOrgRlt();
            userOrgRlt.setUserId(userId);
            userOrgRlt.setOrgId(orgId);
            records.add(userOrgRlt);
        }
        return saveOrUpdateBatch(records);
    }
}
