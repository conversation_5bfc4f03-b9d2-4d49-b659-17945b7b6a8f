package com.uino.bean.cmdb.business;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import com.uino.bean.cmdb.base.CcCiClassDir;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 
 * <AUTHOR>
 *
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value="图标文件夹",description = "图标文件夹")
public class ImageCount implements Serializable {

    private static final long serialVersionUID = 2983786972222108370L;

    @ApiModelProperty(value="ci分类目录")
    private CcCiClassDir dir;

    private List<ImageCount> children = new ArrayList<>();

    /** 文件夹下图片数量 **/
    @ApiModelProperty(value="文件夹下图片的数量",example = "1")
    private Long imageCount;

    /** 是否系统目录 1:是;0:否 */
    @ApiModelProperty(value="是否是系统目录,1:是;0:否",example = "1")
    private Integer isDefault;

    /** 目录是否不可执行删改操作 1:是;0:否 */
    @ApiModelProperty(value="目录是否不可执行删改操作,1:是;0:否",example = "1")
    private Integer isNotOperate;

    /** 是否3D目录 1:是;0:否 */
    @ApiModelProperty(value="是否是3D目录,1:是;0:否",example = "1")
    private Integer is3DImageDir;

    /** DMV的3D目录还是DCV的3D目录 1:DMV;2:DCV */
    @ApiModelProperty(value="DMV的3D目录还是DCV的3D目录,1:DMV;2:DCV",example = "1")
    private Integer dmvOrDcvDir;

}
