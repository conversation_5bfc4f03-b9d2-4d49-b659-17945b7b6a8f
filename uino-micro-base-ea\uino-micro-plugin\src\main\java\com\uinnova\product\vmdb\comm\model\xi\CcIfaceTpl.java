package com.uinnova.product.vmdb.comm.model.xi;

import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;

/**
 * 
 * <AUTHOR>
 *
 */
@Comment("接口模版表[CC_IFACE_TPL]")
public class CcIfaceTpl implements EntityBean {
    private static final long serialVersionUID = 1L;

    @Comment("ID[ID]")
    private Long id;

    @Comment("模版代码[TPL_CODE]")
    private String tplCode;

    @Comment("模版名称[TPL_NAME]")
    private String tplName;

    @Comment("模版描述[TPL_DESC]")
    private String tplDesc;

    @Comment("模版LOGO[TPL_LOGO]")
    private String tplLogo;

    @Comment("模版文档_1[TPL_DOC_1]")
    private String tplDoc1;

    @Comment("模版文档_2[TPL_DOC_2]")
    private String tplDoc2;

    @Comment("模版文档_3[TPL_DOC_3]")
    private String tplDoc3;

    @Comment("模版文档_4[TPL_DOC_4]")
    private String tplDoc4;

    @Comment("模版文档_5[TPL_DOC_5]")
    private String tplDoc5;

    @Comment("备用_1[CUSTOM_1]")
    private String custom1;

    @Comment("备用_2[CUSTOM_2]")
    private String custom2;

    @Comment("备用_3[CUSTOM_3]")
    private String custom3;

    @Comment("备用_4[CUSTOM_4]")
    private String custom4;

    @Comment("备用_5[CUSTOM_5]")
    private String custom5;

    @Comment("备用_6[CUSTOM_6]")
    private String custom6;

    @Comment("所属域[DOMAIN_ID]")
    private Long domainId;

    @Comment("数据状态[DATA_STATUS]    数据状态:数据状态：1-正常 0-删除")
    private Integer dataStatus;

    @Comment("创建时间[CREATE_TIME]")
    private Long createTime;

    @Comment("修改时间[MODIFY_TIME]")
    private Long modifyTime;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTplCode() {
        return this.tplCode;
    }

    public void setTplCode(String tplCode) {
        this.tplCode = tplCode;
    }

    public String getTplName() {
        return this.tplName;
    }

    public void setTplName(String tplName) {
        this.tplName = tplName;
    }

    public String getTplDesc() {
        return this.tplDesc;
    }

    public void setTplDesc(String tplDesc) {
        this.tplDesc = tplDesc;
    }

    public String getTplLogo() {
        return this.tplLogo;
    }

    public void setTplLogo(String tplLogo) {
        this.tplLogo = tplLogo;
    }

    public String getTplDoc1() {
        return this.tplDoc1;
    }

    public void setTplDoc1(String tplDoc1) {
        this.tplDoc1 = tplDoc1;
    }

    public String getTplDoc2() {
        return this.tplDoc2;
    }

    public void setTplDoc2(String tplDoc2) {
        this.tplDoc2 = tplDoc2;
    }

    public String getTplDoc3() {
        return this.tplDoc3;
    }

    public void setTplDoc3(String tplDoc3) {
        this.tplDoc3 = tplDoc3;
    }

    public String getTplDoc4() {
        return this.tplDoc4;
    }

    public void setTplDoc4(String tplDoc4) {
        this.tplDoc4 = tplDoc4;
    }

    public String getTplDoc5() {
        return this.tplDoc5;
    }

    public void setTplDoc5(String tplDoc5) {
        this.tplDoc5 = tplDoc5;
    }

    public String getCustom1() {
        return this.custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }

    public String getCustom2() {
        return this.custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }

    public String getCustom3() {
        return this.custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }

    public String getCustom4() {
        return this.custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }

    public String getCustom5() {
        return this.custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }

    public String getCustom6() {
        return this.custom6;
    }

    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }

    public Long getDomainId() {
        return this.domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }

    public Integer getDataStatus() {
        return this.dataStatus;
    }

    public void setDataStatus(Integer dataStatus) {
        this.dataStatus = dataStatus;
    }

    public Long getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getModifyTime() {
        return this.modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }

}
