package com.uinnova.product.eam.config;


import com.uinnova.product.eam.base.diagram.utils.CommonUtil;
import com.uino.service.permission.microservice.impl.UserSvc;
import com.uino.service.util.FileUtil;
import org.springframework.beans.BeansException;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Configuration;

@Configuration
public class ApplicationContextBean implements ApplicationContextAware, ApplicationRunner {

    private static ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    public static <T> T getBean(String name, Class<T> clazz) {
        return applicationContext.getBean(name, clazz);
    }

    public static <T> T getBean(Class<T> clazz) {
        return applicationContext.getBean(clazz);
    }

    @Override
    public void run(ApplicationArguments args) throws Exception {
        System.out.println("init ApplicationContextBean");
        //实例化一下含有静态属性的bean
        applicationContext.getBean(FileUtil.class);
        applicationContext.getBean(CommonUtil.class);
        applicationContext.getBean(UserSvc.class);
    }
}
