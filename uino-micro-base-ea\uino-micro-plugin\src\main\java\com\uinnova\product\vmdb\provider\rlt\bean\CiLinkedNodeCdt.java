package com.uinnova.product.vmdb.provider.rlt.bean;

import com.binary.framework.bean.annotation.Comment;

import java.io.Serializable;
import java.util.List;

@Comment("一条线的朋友圈查询")
public class CiLinkedNodeCdt implements Serializable{

	private static final long serialVersionUID = 1L;
	
	@Comment("节点的ID在linked中节点ID应该是唯一的")
	private Long nodeId;
	
	@Comment("ci分类ID")
	private Long ciClassId;
	
	@Comment("分类属性的查询关系多个是AND关系")
	private List<CiLinkedAttrCdt> cdts;

	public Long getNodeId() {
		return nodeId;
	}

	public void setNodeId(Long nodeId) {
		this.nodeId = nodeId;
	}

	public Long getCiClassId() {
		return ciClassId;
	}

	public void setCiClassId(Long ciClassId) {
		this.ciClassId = ciClassId;
	}

	public List<CiLinkedAttrCdt> getCdts() {
		return cdts;
	}

	public void setCdts(List<CiLinkedAttrCdt> cdts) {
		this.cdts = cdts;
	}
	
	
	
}
