package com.uinnova.product.eam.service.cj.service;

import com.uinnova.product.eam.model.cj.domain.PlanDesignInstance;
import com.uinnova.product.eam.model.cj.vo.DlvrTemplateVO;

/**
 * 方案操作扩展接口
 * <AUTHOR>
 */
public interface PlanInstanceServiceExtend {

    /**
     * 设置方案发布路径
     * @param plan 方案
     */
    void getPlanReleaseDirInfo(PlanDesignInstance plan);

    /**
     * 设置方案发布路径
     * @param plan 方案
     */
    void getPlanReleaseDirInfo(PlanDesignInstance plan, DlvrTemplateVO template);
}
