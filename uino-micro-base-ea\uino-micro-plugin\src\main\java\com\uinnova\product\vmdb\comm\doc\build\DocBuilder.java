package com.uinnova.product.vmdb.comm.doc.build;

import com.binary.core.http.HttpClient;
import com.binary.core.io.FileSystem;
import com.binary.core.io.SerializationUtils;
import com.binary.core.util.BinaryUtils;
import com.binary.framework.util.ControllerUtils;
import com.binary.json.JSON;
import com.uinnova.product.vmdb.comm.doc.api.ApiDesc;
import com.uinnova.product.vmdb.comm.doc.api.MvcApi;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 
 * <AUTHOR>
 *
 */
public abstract class DocBuilder {
    private static final Logger logger = LoggerFactory.getLogger(DocBuilder.class);

    public static List<MvcApi> getMvcApi(String location) {
        logger.info(" start build mvc doc '{}'... ", location);

        List<MvcApi> apils = new ArrayList<MvcApi>();
        List<Class<?>> ls = MvcScanner.scanMvc(location);

        List<Class<?>> errList = new ArrayList<Class<?>>();

        for (Class<?> mvcClass : ls) {
            try {
                MvcApi mvcApi = MvcDocBuilder.buildMvcApi(mvcClass);
                if (!BinaryUtils.isEmpty(mvcApi)) {
                    apils.add(mvcApi);
                }
            } catch (Exception e) {
                errList.add(mvcClass);
                logger.error(e.getMessage());
            }
        }

        for (Class<?> mvcClass : errList) {
            logger.error("failed mvc ====>" + mvcClass.getName());
        }

        logger.info(" build mvc doc successful. ");
        return apils;
    }

    /**
     * 保存文件
     * 
     * @param fileName
     * @param apis
     * @param docDir
     */
    private static void updateMvcApi2File(String fileName, List<MvcApi> apis) {
        logger.info(" start upload mvc doc '" + fileName + "'... ");

        File file = new File(fileName);
        file.getParentFile().mkdirs();

        FileSystem.write(file, JSON.toString(apis), "utf-8");

    }

    private static void updateMvcApi(String webName, List<MvcApi> apis, String docServer) {
        logger.info(" start upload mvc doc '" + docServer + "'... ");
        byte[] bytes = SerializationUtils.serialize(apis);
        ByteArrayInputStream is = new ByteArrayInputStream(bytes);

        docServer = ControllerUtils.formatContextPath(docServer).substring(1);

        HttpClient client = HttpClient.getInstance(docServer);
        client.addRequestProperty("REQUEST_HEADER", "binary-http-client-header");

        Map<String, Object> form = new HashMap<String, Object>();
        form.put("apiFile", is);
        form.put("webName", webName);
        String result = client.request("/api/upload", form);
        ControllerUtils.toRemoteJsonObject(result, Boolean.class);

        logger.info(" upload mvc doc successful. ");
    }

    private static void updateMvcApiRest(String webName, String projectHttpRoot, List<MvcApi> apis, String docServer) {
        logger.info(" start upload mvc doc '" + docServer + "'... ");

        ApiDesc apiDesc = new ApiDesc();
        apiDesc.setApiDoc(JSON.toString(apis));
        apiDesc.setProjectName(webName);
        apiDesc.setProjectHttpRoot(projectHttpRoot);

        docServer = ControllerUtils.formatContextPath(docServer).substring(1);

        HttpClient client = HttpClient.getInstance(docServer);
        client.addRequestProperty("REQUEST_HEADER", "binary-http-client-header");

        String body = JSON.toString(apiDesc);

        String result = client.rest("/api/upload", body);
        ControllerUtils.toRemoteJsonObject(result, Boolean.class);

        logger.info(" upload mvc doc successful. ");
    }

    public static void buildMvcDoc(String fileName, String scanPath) {
        List<MvcApi> apis = getMvcApi(scanPath);
        // updateMvcApi(webName, apis, docServer);
        updateMvcApi2File(fileName, apis);
    }

    public static void buildMvcDoc(String webName, String scanPath, String docServer) {
        List<MvcApi> apis = getMvcApi(scanPath);
        updateMvcApi(webName, apis, docServer);
        // updateMvcApi2File(fileName, apis);
    }

    /**
     * 
     * @param httpRoot
     * @param projectName
     *            当前项目名称
     * @param scanPath
     *            要遍历的包
     */
    public static void buildMvcDoc2SimpleMvc(String docServer, String projectName, String projectHttpRoot, String scanPath) {
        List<MvcApi> apis = getMvcApi(scanPath);
        updateMvcApiRest(projectName, projectHttpRoot, apis, docServer);
    }

}
