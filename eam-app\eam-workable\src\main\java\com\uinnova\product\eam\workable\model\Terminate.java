package com.uinnova.product.eam.workable.model;

import lombok.Data;

import  jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

@Data
@Entity
@Table(name="flowable_eam_terminate")
public class Terminate {

    @Id
    private String id;

    @Column(name="businessKey")
    private String businessKey ;

    @Column(name="processDefinitionKey")
    private String processDefinitionKey;

    @Column(name="domainId")
    private Integer domainId;

    @Column(name="createTime")
    private Long createTime;

    @Column(name="modifyTime")
    private Long modifyTime;
}
