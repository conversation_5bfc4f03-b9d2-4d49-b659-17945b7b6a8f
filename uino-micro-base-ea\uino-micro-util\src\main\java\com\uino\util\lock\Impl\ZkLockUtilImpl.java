package com.uino.util.lock.Impl;

import com.uino.util.lock.LockUtil;
import com.uino.util.lock.condition.EnableZkCondition;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.curator.framework.CuratorFramework;
import org.apache.curator.framework.recipes.locks.InterProcessLock;
import org.apache.curator.framework.recipes.locks.InterProcessMutex;
import org.apache.curator.framework.recipes.locks.InterProcessReadWriteLock;
import org.apache.curator.framework.recipes.locks.InterProcessSemaphoreMutex;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * @Description 分布式锁实现类（zookeeper）
 * <AUTHOR>
 * @Date 2021/7/14
 **/
@Slf4j
@Primary
@Component
@Conditional(EnableZkCondition.class)
public class ZkLockUtilImpl implements LockUtil {

    // ZooKeeper 锁节点路径, 分布式锁的相关操作都是在这个根节点上进行
    private static final String LOCK_PATH = "/distributed-lock/";

    public static Map<String, InterProcessLock> LOCK = new ConcurrentHashMap<>(32);

    @Value("${spring.application.name:name}")
    private String applicationName;

    @Autowired(required = false)
    private CuratorFramework client;

    @Override
    public Boolean tryLock(String lockName, long time, TimeUnit unit, Boolean... wait) {
        boolean isLock = false;
        InterProcessLock lock = getInterProcessMutexLock(lockName);
        try {
            if (wait.length > 0 && wait[0]) {
                isLock = lock.acquire(time, unit);
            } else {
                if (!isLocked(lockName)) {
                    isLock = lock.acquire(time, unit);
                }
            }
        } catch (Exception e) {
            log.error(">>> try lock [ {} ] error: {}", lockName, e.getMessage());
        }
        return isLock;
    }

    @Override
    public Boolean tryLock(String lockName, Boolean... wait) {
        boolean isLock = false;
        InterProcessLock lock = getInterProcessMutexLock(lockName);
        try {
            if (wait.length > 0 && wait[0]) {
                lock.acquire();
                isLock = true;
            } else {
                if (!isLocked(lockName)) {
                    lock.acquire();
                    isLock = true;
                }
            }
        } catch (Exception e) {
            log.error(">>> try lock [ {} ] error: {}", lockName, e.getMessage());
        }
        return isLock;
    }

    @SneakyThrows
    @Override
    public Boolean unLock(String lockName) {
        InterProcessLock lock = getInterProcessMutexLock(lockName);
        try {
            lock.release();
            return true;
        } catch (Exception e) {
            log.error(">>> {} releases lock [ {} ] error: {}", Thread.currentThread().getName(), lockName, e.getMessage());
        } finally {
            client.delete().guaranteed().forPath(LOCK_PATH + applicationName + "/" + lockName);
        }
        return false;
    }

    @Override
    public Boolean forceUnlock(String lockName) {
        return null;
    }

    @Override
    public Boolean isLocked(String lockName) {
        return getInterProcessMutexLock(lockName).isAcquiredInThisProcess();
    }

    private synchronized InterProcessLock getInterProcessMutexLock(String name) {
        InterProcessLock lock = LOCK.get(name);
        if (null == lock) {
            lock = new InterProcessMutex(client, LOCK_PATH + applicationName + "/" + name);
            LOCK.put(name, lock);
            return lock;
        } else {
            return lock;
        }
    }

    //共享锁，不可重入
    public synchronized InterProcessLock getInterProcessSemaphoreMutexLock(String name) {
        InterProcessLock lock = LOCK.get(name);
        if (null == lock) {
            lock = new InterProcessSemaphoreMutex(client, LOCK_PATH + applicationName + "/" + name);
            LOCK.put(name, lock);
            return lock;
        } else {
            return lock;
        }
    }

    //共享可重入读锁（读锁和读锁不互斥）
    public synchronized InterProcessLock getInterProcessReadLock(String name) {
        InterProcessLock lock = LOCK.get(name);
        if (null == lock) {
            lock = new InterProcessReadWriteLock(client, LOCK_PATH + applicationName + "/" + name).readLock();
            LOCK.put(name, lock);
            return lock;
        } else {
            return lock;
        }
    }

    //写锁（写锁互斥）
    public synchronized InterProcessLock getInterProcessWriteLock(String name) {
        InterProcessLock lock = LOCK.get(name);
        if (null == lock) {
            lock = new InterProcessReadWriteLock(client, LOCK_PATH + applicationName + "/" + name).writeLock();
            LOCK.put(name, lock);
            return lock;
        } else {
            return lock;
        }
    }
}
