package com.uinnova.product.eam.db.impl;


import com.uinnova.product.eam.comm.model.CVcDiagramNotify;
import com.uinnova.product.eam.comm.model.VcDiagramNotify;
import com.uinnova.product.eam.db.VcDiagramNotifyDao;
import com.uinnova.product.eam.db.support.dao.mybatis.ComMyBatisBinaryDaoImpl;


/**
 * 视图通知表[VC_DIAGRAM_NOTIFY]数据访问对象实现
 */
public class VcDiagramNotifyDaoImpl extends ComMyBatisBinaryDaoImpl<VcDiagramNotify, CVcDiagramNotify> implements VcDiagramNotifyDao {

//    @Override
//    public String getTableName() {
//        return "IAMS." + getDaoDefinition().getTableName();
//    }
}


