package com.uinnova.product.eam.model.vo;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;
import java.io.Serializable;
import java.util.List;

/**
 * 通用返回实体
 */
@Data
public class EamGeneralResult implements Serializable {

    @Comment("主键")
    private Long id;

    @Comment("标识")
    private String code;

    @Comment("名称")
    private String name;

    @Comment("子集")
    private List<EamGeneralResult> children;

}
