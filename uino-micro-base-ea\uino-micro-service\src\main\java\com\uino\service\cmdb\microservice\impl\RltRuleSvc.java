package com.uino.service.cmdb.microservice.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.MultiMatchQueryBuilder;
import org.elasticsearch.index.query.Operator;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.sort.SortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONArray;
import com.binary.core.exception.MessageException;
import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClass;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uino.dao.cmdb.ESCIClassSvc;
import com.uino.dao.cmdb.ESRltClassSvc;
import com.uino.dao.cmdb.ESRltRuleSvc;
import com.uino.service.cmdb.microservice.IRltRuleSvc;
import com.uino.bean.cmdb.base.ESCIClassInfo;
import com.uino.bean.cmdb.base.ESRltRuleInfo;

/**
 * <AUTHOR>
 * @data 2019/7/31 15:55.
 */
@Service
@RefreshScope
public class RltRuleSvc implements IRltRuleSvc {

    @Value("${http.resource.space:}")
    private String rsmSlaveRoot;

    @Autowired
    private ESRltRuleSvc esRltRuleSvc;

    @Autowired
    private ESCIClassSvc esciClassSvc;

    @Autowired
    private ESRltClassSvc esRltClassSvc;

    @Override
    public Long saveOrUpdate(ESRltRuleInfo rltRuleInfo) {
        verifyrltRuleInfo(rltRuleInfo);
        return esRltRuleSvc.saveOrUpdate(rltRuleInfo);
    }

    /**
     * 校验规则
     *
     * @param rltRuleInfo
     */
    private void verifyrltRuleInfo(ESRltRuleInfo rltRuleInfo) {
        if (rltRuleInfo.getDomainId() == null) {
            throw MessageException.i18n("BS_VISUALMODEL_LACK_DOMAINID");
        }
        if (rltRuleInfo.getId() != null) {
            ESRltRuleInfo esRltRuleInfo = esRltRuleSvc.getById(rltRuleInfo.getId());
            if (esRltRuleInfo == null) {
                throw MessageException.i18n("BS_RLT_RULE_NO_EXIST");
            }
        }
        // todo:可视化建模校验
        String name = rltRuleInfo.getName();
        if (name == null) {
            throw MessageException.i18n("BS_MVTYPE_ARG_ERROR");
        }
        // 校验重名，此处有漏洞（默认查3000条，不过规则一般不会有这么多）
        List<ESRltRuleInfo> rltRuleInfos = queryInfo(rltRuleInfo.getDomainId());
        for (ESRltRuleInfo esRltRuleInfo : rltRuleInfos) {
            if (esRltRuleInfo.getName().equals(name) && !esRltRuleInfo.getId().equals(rltRuleInfo.getId())) {
                throw MessageException.i18n("BS_RLT_RULE_NAME_REPETITION");
            }
        }

        // 校验所有关系分类是否存在
        List<ESRltRuleInfo.LineInfo> lineInfos = rltRuleInfo.getLines();
        if (lineInfos != null && !lineInfos.isEmpty()) {
            Set<Long> rltClassIds = new HashSet<>();
            for (ESRltRuleInfo.LineInfo lineInfo : lineInfos) {
                if (lineInfo.getLine().getLineType() == 1) {
                    rltClassIds.add(lineInfo.getLine().getClsRltId());
                }
            }
            CCcCiClass cdt = new CCcCiClass();
            cdt.setIds(rltClassIds.toArray(new Long[0]));
            List<ESCIClassInfo> rltClasses = esRltClassSvc.getListByCdt(cdt);
            if (rltClasses == null || rltClassIds.size() != rltClasses.size()) {
                throw MessageException.i18n("BS_RLT_RULE_LACK");
            }
        }

        // 校验所有ci分类是否存在
        Set<Long> ciClassIds = new HashSet<>();
        List<ESCIClassInfo> ciClasses = getCiClasses(rltRuleInfo, ciClassIds);
        if (ciClasses == null || ciClasses.size() != ciClassIds.size()) {
            throw MessageException.i18n("BS_RLT_RULE_LACK");
        }
        // 校验节点显示属性是否是当前分类属性
        Map<Long, Set<String>> clsMap = new HashMap<Long, Set<String>>();
        for (ESCIClassInfo esciClassInfo : ciClasses) {
            Set<String> attrs = new HashSet<String>();
            for (CcCiAttrDef attrDef : esciClassInfo.getCcAttrDefs()) {
                attrs.add(attrDef.getProStdName());
            }
            clsMap.put(esciClassInfo.getId(), attrs);
        }
        for (ESRltRuleInfo.NodeInfo nodeInfo : rltRuleInfo.getNodes()) {
            if (nodeInfo.getNode().getNodeType() == 1) {
                JSONArray attrArr = JSONArray.parseArray(nodeInfo.getNode().getReturns());
                if (attrArr != null) {
                    for (int i = 0; i < attrArr.size(); i++) {
                        String retName = attrArr.getJSONObject(i).getString("proStdName");
                        Set<String> attrs = clsMap.get(nodeInfo.getNode().getClassId());
                        if (attrs != null) {
                            if (!attrs.contains(retName)) {
                                throw MessageException.i18n("BS_RLT_RULE_LACK");
                            }
                        } else {
                            throw MessageException.i18n("BS_RLT_RULE_LACK");
                        }
                    }
                }
            }
        }
        //置空关系分类属性
        if (rltRuleInfo.getLines()!=null) {
            for (ESRltRuleInfo.LineInfo line : rltRuleInfo.getLines()) {
                if (line.getLine().getLineType() == 1) {
                    line.setClassInfo(null);
                }
            }
        }


    }

    @Override
    public Integer deleteById(Long id) {
        return esRltRuleSvc.deleteById(id);
    }

    @Override
    public ESRltRuleInfo queryInfoById(Long id) {
        ESRltRuleInfo esRltRuleInfo = getEsRltRuleInfo(id);
        List<ESCIClassInfo> ciClasses = getCiClasses(esRltRuleInfo, null);
        Map<Long, ESCIClassInfo> classInfoMap = new HashMap<>();
        for (ESCIClassInfo classInfo : ciClasses) {
            String icon = classInfo.getIcon();
            classInfo.setIcon(rsmSlaveRoot + icon);
            classInfoMap.put(classInfo.getId(), classInfo);
        }
        // 为分类属性赋值
        for (ESRltRuleInfo.NodeInfo nodeInfo : esRltRuleInfo.getNodes()) {
            if (nodeInfo.getNode().getNodeType() == 1) {
                nodeInfo.setClassInfo(classInfoMap.get(nodeInfo.getNode().getClassId()));
            }
        }
        //todo:此处在正式版本在做优化，包括规则校验
        for (ESRltRuleInfo.LineInfo line : esRltRuleInfo.getLines()) {
            if (line.getLine().getLineType() == 1) {
                ESCIClassInfo rltClassSvcById = esRltClassSvc.getById(line.getLine().getClsRltId());
                line.setClassInfo(rltClassSvcById);
            }
        }
        return esRltRuleInfo;
    }

    private ESRltRuleInfo getEsRltRuleInfo(Long id) {
        if (id == null) {
            throw MessageException.i18n("BS_MVTYPE_ARG_ERROR");
        }
        ESRltRuleInfo esRltRuleInfo = esRltRuleSvc.getById(id);
        if (esRltRuleInfo == null) {
            // 规则不存在
            throw MessageException.i18n("BS_RLT_RULE_NO_EXIST");
        }
        return esRltRuleInfo;
    }

    /**
     * 获取规则中所有ci分类
     *
     * @param esRltRuleInfo 规则
     * @param ciClassIds    ci分类id
     * @return
     */
    private List<ESCIClassInfo> getCiClasses(ESRltRuleInfo esRltRuleInfo, Set<Long> ciClassIds) {
        if (ciClassIds == null) {
            ciClassIds = new HashSet<>();
        }
        List<ESRltRuleInfo.NodeInfo> nodeInfos = esRltRuleInfo.getNodes();
        for (ESRltRuleInfo.NodeInfo nodeInfo : nodeInfos) {
            if (nodeInfo.getNode().getNodeType() == 1) {
                ciClassIds.add(nodeInfo.getNode().getClassId());
            }
        }
        CCcCiClass ciClassBean = new CCcCiClass();
        ciClassBean.setIds(ciClassIds.toArray(new Long[ciClassIds.size()]));
        List<ESCIClassInfo> ciClasses = esciClassSvc.getListByCdt(ciClassBean);
        return ciClasses;
    }

    @Override
    public List<ESRltRuleInfo> queryInfo(Long domainId) {
        return esRltRuleSvc.getListByQuery(QueryBuilders.termQuery("domainId", domainId));
    }

    @Override
    public Page<ESRltRuleInfo> queryInfoPage(Integer pageNum, Integer pageSize, String name, Long domainId) {
        return queryPage(pageNum, pageSize, name, domainId, null, false);
    }

    @Override
    public Page<ESRltRuleInfo> queryInfoPage(Integer pageNum, Integer pageSize, Long domainId) {
        return queryPage(pageNum, pageSize, null, domainId, null, false);
    }

    @Override
    public Page<ESRltRuleInfo> queryInfoPage(Integer pageNum, Integer pageSize, Long domainId, String orders, boolean isAsc) {
        return queryPage(pageNum, pageSize, null, domainId, orders, isAsc);
    }

    @Override
    public Page<ESRltRuleInfo> queryInfoPage(Integer pageNum, Integer pageSize, String name, Long domainId, String orders, boolean isAsc) {
        return queryPage(pageNum, pageSize, name, domainId, orders, isAsc);
    }

    private Page<ESRltRuleInfo> queryPage(Integer pageNum, Integer pageSize, String name, Long domainId, String orders, boolean isAsc) {
        BoolQueryBuilder querybuilder = QueryBuilders.boolQuery();
        querybuilder.filter(QueryBuilders.termQuery("domainId", domainId));
        if (name != null) {
            querybuilder.filter(QueryBuilders.multiMatchQuery(name, "name").operator(Operator.AND)
                    .type(MultiMatchQueryBuilder.Type.PHRASE_PREFIX).lenient(true));
        }
        List<SortBuilder<?>> sorts = new ArrayList<SortBuilder<?>>();
        if (orders != null) {
            SortBuilder<?> sort = SortBuilders.fieldSort(orders).order(isAsc ? SortOrder.ASC : SortOrder.DESC);
            sorts.add(sort);
        } else {
            SortBuilder<?> sort = SortBuilders.fieldSort("modifyTime").order(SortOrder.ASC);
            sorts.add(sort);
        }

        Page<ESRltRuleInfo> sortListByQuery = esRltRuleSvc.getSortListByQuery(pageNum, pageSize, querybuilder, sorts);
        return sortListByQuery;
    }

}
