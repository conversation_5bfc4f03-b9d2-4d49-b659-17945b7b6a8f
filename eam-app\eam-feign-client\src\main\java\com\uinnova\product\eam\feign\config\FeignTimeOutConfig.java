package com.uinnova.product.eam.feign.config;

import feign.Request;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @description:
 * @author: Lc
 * @create: 2022-07-21 10:11
 */
@Configuration
public class FeignTimeOutConfig {

    @Bean
    Request.Options feignOptions() {
        return new Request.Options(10 * 1000, 10 * 1000);
    }
}
