package com.uino.util.digest.impl.type;


import java.util.Base64;


/**
 * @Title Base64 Util
 * @Author: YGQ
 * @Create: 2021-08-08 13:50
 **/
public class Base64Util {

    /**
     * base64 encryption
     *
     * @param str characters to be encrypted
     */
    public static String base64(String str) {
        byte[] bytes = str.getBytes();
        return Base64.getEncoder().encodeToString(bytes);
    }

    /**
     * Base64 decrypted
     *
     * @param str characters to be decrypted
     */

    public static String base64dec(String str) {
        byte[] decoded = Base64.getDecoder().decode(str);
        return new String(decoded);
    }

    /**
     * BASE64 decryption
     *
     * @param key key to be decrypted
     * @throws Exception exception
     */


    /**
     * BASE64 encryption
     *
     * @param key key to be encrypted
     */

    /**
     * Invoke the sample
     */
    public static void main(String[] args) {
        String str = "helloWorld";
        System.out.println(base64(str));
        System.out.println(base64dec(base64(str)));
    }
}