package com.uinnova.product.eam.model.enums;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/*
* 流程管理相关页面根目录id
* */
public enum ProcessRootDirectory {
    SYSTEM(100l,"制度"),
    STANDARD(110l,"标准"),
    ORGANIZATION(120l,"风险"),
    FORMLIBRARY(130l,"表单"),
    TERMINOLOGYLIBRARY(140l,"术语"),
    RoleLibrary(150l,"岗位角色"),
    C2C(160l,"端到端流程"),
    ELEMENTS(170l,"要素");

    private Long handleType;

    private String handleDesc;

    ProcessRootDirectory(Long handleType, String handleDesc) {
        this.handleType = handleType;
        this.handleDesc = handleDesc;
    }

    public Long getHandleType() {
        return handleType;
    }

    public String getHandleDesc() {
        return handleDesc;
    }

    /**
     * 返回包含所有 handleType 值的集合
     * @return 包含所有 handleType 值的集合
     */
    public static List<Long> getAllHandleTypes() {
        return Arrays.stream(values())
                .map(ProcessRootDirectory::getHandleType)
                .collect(Collectors.toList());
    }
    public static List<String> getHandleDescs() {
        return Arrays.stream(values())
                .map(ProcessRootDirectory::getHandleDesc)
                .collect(Collectors.toList());
    }

    public static ProcessRootDirectory getByHandleType(Long handleType) {
        for (ProcessRootDirectory type : ProcessRootDirectory.values()) {
            if (type.getHandleType().equals(handleType)) {
                return type;
            }
        }
        return null;
    }

}
