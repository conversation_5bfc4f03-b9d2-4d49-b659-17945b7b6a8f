package com.uinnova.product.vmdb.provider.ci.bean;

import com.uinnova.product.vmdb.comm.err.ErrorBean;

import java.util.List;
import java.util.Map;

/**
 * 保存具体到行记录(每条CI)的错误信息<br/>
 * 当前错误级别为CI级错误<br/>
 * 本类的属性较多,请自行选择使用,除注明注明是CI是使用的其它属性均作为通用属性,忽略注释的部分内容
 * 
 * <AUTHOR>
 *
 */
public class DetailedErrInfo extends ErrorBean {
	private static final long serialVersionUID = 1L;

	/**
	 * 索引,行号等
	 */
	private Integer index;

	/**
	 * ciCode值
	 */
	private String ciCode;

	/**
	 * 记录ciCode值的原始列头(ciCode列头不一定叫ciCode)(保存CI使用)
	 */
	private String ciCodeTitle;

	/**
	 * 业务主键属性名(如果业务主键有错误则拼接一下业务主键值以便使用)(保存CI使用)
	 */
	private List<String> pkProNames;

	/**
	 * 当前行处理有错误内容(保存CI使用)
	 */
	private Map<String, String> errAttrs;

	/**
	 * 当前错误内容(自行转换)
	 */
	private Object errComment;

	/**
	 * CI属性值错误信息列表
	 */
	private List<ErrorBean> errs;

	public Integer getIndex() {
		return index;
	}

	public void setIndex(Integer index) {
		this.index = index;
	}

	public String getCiCode() {
		return ciCode;
	}

	public void setCiCode(String ciCode) {
		this.ciCode = ciCode;
	}

	public String getCiCodeTitle() {
		return ciCodeTitle;
	}

	public void setCiCodeTitle(String ciCodeTitle) {
		this.ciCodeTitle = ciCodeTitle;
	}

	public List<String> getPkProNames() {
		return pkProNames;
	}

	public void setPkProNames(List<String> pkProNames) {
		this.pkProNames = pkProNames;
	}

	public Map<String, String> getErrAttrs() {
		return errAttrs;
	}

	public void setErrAttrs(Map<String, String> errAttrs) {
		this.errAttrs = errAttrs;
	}

	public Object getErrComment() {
		return errComment;
	}

	public void setErrComment(Object errComment) {
		this.errComment = errComment;
	}

	public List<ErrorBean> getErrs() {
		return errs;
	}

	public void setErrs(List<ErrorBean> errs) {
		this.errs = errs;
	}

}
