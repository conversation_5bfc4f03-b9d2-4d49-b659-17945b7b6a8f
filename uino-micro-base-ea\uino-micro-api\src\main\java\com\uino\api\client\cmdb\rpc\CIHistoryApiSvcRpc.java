package com.uino.api.client.cmdb.rpc;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import com.uino.bean.cmdb.base.ESCIHistoryInfo;
import com.uino.provider.feign.cmdb.CIHistoryFeign;
import com.uino.api.client.cmdb.ICIHistoryApiSvc;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
@Primary
public class CIHistoryApiSvcRpc implements ICIHistoryApiSvc {

    @Autowired
    private CIHistoryFeign ciHistoryFeign;

    @Override
    public void delAll(List<Long> classIds) {
        ciHistoryFeign.delAll(classIds);
    }

    @Override
    public List<String> getCIVersionList(String ciCode, Long classId) {
        return ciHistoryFeign.getCIVersionList(ciCode, classId);
    }

    @Override
    public ESCIHistoryInfo getCIInfoHistoryByCIVersion(String ciCode, Long classId, Long version) {
        return ciHistoryFeign.getCIInfoHistoryByCIVersion(ciCode, classId, version);
    }

    @Override
    public List<ESCIHistoryInfo> bathGetCIInfoHistoryByCIVersion(Map<String, Long> ciCodeVersionMap) {
        return null;
    }
}
