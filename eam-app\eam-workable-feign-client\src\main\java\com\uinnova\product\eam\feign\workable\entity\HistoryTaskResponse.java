package com.uinnova.product.eam.feign.workable.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.Map;

/**
 * 历史task响应实体
 *
 * <AUTHOR>
 * @since 2022/2/28 15:19
 */
@Data
public class HistoryTaskResponse {

    private String processDefinitionKey;

    private String taskId;

    private String taskName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date commitTime;

    private String userId;

    private String startUserId;

    private String processInstanceName;

    private Map<String, Object> variables;
}
