package com.uinnova.product.vmdb.comm.model.pv;




import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;


@Comment("用户流量统计表[CC_USER_PV_COUNT]")
public class CCcUserPvCount implements Condition {
	private static final long serialVersionUID = 1L;


	@Comment("ID[ID] operate-Equal[=]")
	private Long id;


	@Comment("ID[ID] operate-In[in]")
	private Long[] ids;


	@Comment("ID[ID] operate-GTEqual[>=]")
	private Long startId;

	@Comment("ID[ID] operate-LTEqual[<=]")
	private Long endId;


	@Comment("DOMAINID[DOMAIN_ID] operate-Equal[=]    DOMAIN_ID")
	private Long domainId;


	@Comment("DOMAINID[DOMAIN_ID] operate-In[in]    DOMAIN_ID")
	private Long[] domainIds;


	@Comment("DOMAINID[DOMAIN_ID] operate-GTEqual[>=]    DOMAIN_ID")
	private Long startDomainId;

	@Comment("DOMAINID[DOMAIN_ID] operate-LTEqual[<=]    DOMAIN_ID")
	private Long endDomainId;


	@Comment("用户ID[USER_ID] operate-Equal[=]")
	private Long userId;


	@Comment("用户ID[USER_ID] operate-In[in]")
	private Long[] userIds;


	@Comment("用户ID[USER_ID] operate-GTEqual[>=]")
	private Long startUserId;

	@Comment("用户ID[USER_ID] operate-LTEqual[<=]")
	private Long endUserId;


	@Comment("对象标识[TARGET_CODE] operate-Like[like]")
	private String targetCode;


	@Comment("对象标识[TARGET_CODE] operate-Equal[=]")
	private String targetCodeEqual;


	@Comment("对象标识[TARGET_CODE] operate-In[in]")
	private String[] targetCodes;


	@Comment("对象描述[TARGET_DESC] operate-Like[like]")
	private String targetDesc;


	@Comment("对象描述[TARGET_DESC] operate-Equal[=]")
	private String targetDescEqual;


	@Comment("对象描述[TARGET_DESC] operate-In[in]")
	private String[] targetDescs;


	@Comment("点击次数[PV_COUNT] operate-Equal[=]")
	private Long pvCount;


	@Comment("点击次数[PV_COUNT] operate-In[in]")
	private Long[] pvCounts;


	@Comment("点击次数[PV_COUNT] operate-GTEqual[>=]")
	private Long startPvCount;

	@Comment("点击次数[PV_COUNT] operate-LTEqual[<=]")
	private Long endPvCount;


	@Comment("数据状态[DATA_STATUS] operate-Equal[=]    数据状态:数据状态：1-正常 0-删除")
	private Integer dataStatus;


	@Comment("数据状态[DATA_STATUS] operate-In[in]    数据状态:数据状态：1-正常 0-删除")
	private Integer[] dataStatuss;


	@Comment("数据状态[DATA_STATUS] operate-GTEqual[>=]    数据状态:数据状态：1-正常 0-删除")
	private Integer startDataStatus;

	@Comment("数据状态[DATA_STATUS] operate-LTEqual[<=]    数据状态:数据状态：1-正常 0-删除")
	private Integer endDataStatus;


	@Comment("创建时间[CREATE_TIME] operate-Equal[=]    创建时间:yyyyMMddHHmmss")
	private Long createTime;


	@Comment("创建时间[CREATE_TIME] operate-In[in]    创建时间:yyyyMMddHHmmss")
	private Long[] createTimes;


	@Comment("创建时间[CREATE_TIME] operate-GTEqual[>=]    创建时间:yyyyMMddHHmmss")
	private Long startCreateTime;

	@Comment("创建时间[CREATE_TIME] operate-LTEqual[<=]    创建时间:yyyyMMddHHmmss")
	private Long endCreateTime;


	@Comment("修改时间[MODIFY_TIME] operate-Equal[=]    修改时间:yyyyMMddHHmmss")
	private Long modifyTime;


	@Comment("修改时间[MODIFY_TIME] operate-In[in]    修改时间:yyyyMMddHHmmss")
	private Long[] modifyTimes;


	@Comment("修改时间[MODIFY_TIME] operate-GTEqual[>=]    修改时间:yyyyMMddHHmmss")
	private Long startModifyTime;

	@Comment("修改时间[MODIFY_TIME] operate-LTEqual[<=]    修改时间:yyyyMMddHHmmss")
	private Long endModifyTime;




	public Long getId() {
		return this.id;
	}
	public void setId(Long id) {
		this.id = id;
	}


	public Long[] getIds() {
		return this.ids;
	}
	public void setIds(Long[] ids) {
		this.ids = ids;
	}


	public Long getStartId() {
		return this.startId;
	}
	public void setStartId(Long startId) {
		this.startId = startId;
	}


	public Long getEndId() {
		return this.endId;
	}
	public void setEndId(Long endId) {
		this.endId = endId;
	}


	public Long getDomainId() {
		return this.domainId;
	}
	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}


	public Long[] getDomainIds() {
		return this.domainIds;
	}
	public void setDomainIds(Long[] domainIds) {
		this.domainIds = domainIds;
	}


	public Long getStartDomainId() {
		return this.startDomainId;
	}
	public void setStartDomainId(Long startDomainId) {
		this.startDomainId = startDomainId;
	}


	public Long getEndDomainId() {
		return this.endDomainId;
	}
	public void setEndDomainId(Long endDomainId) {
		this.endDomainId = endDomainId;
	}


	public Long getUserId() {
		return this.userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}


	public Long[] getUserIds() {
		return this.userIds;
	}
	public void setUserIds(Long[] userIds) {
		this.userIds = userIds;
	}


	public Long getStartUserId() {
		return this.startUserId;
	}
	public void setStartUserId(Long startUserId) {
		this.startUserId = startUserId;
	}


	public Long getEndUserId() {
		return this.endUserId;
	}
	public void setEndUserId(Long endUserId) {
		this.endUserId = endUserId;
	}


	public String getTargetCode() {
		return this.targetCode;
	}
	public void setTargetCode(String targetCode) {
		this.targetCode = targetCode;
	}


	public String getTargetCodeEqual() {
		return this.targetCodeEqual;
	}
	public void setTargetCodeEqual(String targetCodeEqual) {
		this.targetCodeEqual = targetCodeEqual;
	}


	public String[] getTargetCodes() {
		return this.targetCodes;
	}
	public void setTargetCodes(String[] targetCodes) {
		this.targetCodes = targetCodes;
	}


	public String getTargetDesc() {
		return this.targetDesc;
	}
	public void setTargetDesc(String targetDesc) {
		this.targetDesc = targetDesc;
	}


	public String getTargetDescEqual() {
		return this.targetDescEqual;
	}
	public void setTargetDescEqual(String targetDescEqual) {
		this.targetDescEqual = targetDescEqual;
	}


	public String[] getTargetDescs() {
		return this.targetDescs;
	}
	public void setTargetDescs(String[] targetDescs) {
		this.targetDescs = targetDescs;
	}


	public Long getPvCount() {
		return this.pvCount;
	}
	public void setPvCount(Long pvCount) {
		this.pvCount = pvCount;
	}


	public Long[] getPvCounts() {
		return this.pvCounts;
	}
	public void setPvCounts(Long[] pvCounts) {
		this.pvCounts = pvCounts;
	}


	public Long getStartPvCount() {
		return this.startPvCount;
	}
	public void setStartPvCount(Long startPvCount) {
		this.startPvCount = startPvCount;
	}


	public Long getEndPvCount() {
		return this.endPvCount;
	}
	public void setEndPvCount(Long endPvCount) {
		this.endPvCount = endPvCount;
	}


	public Integer getDataStatus() {
		return this.dataStatus;
	}
	public void setDataStatus(Integer dataStatus) {
		this.dataStatus = dataStatus;
	}


	public Integer[] getDataStatuss() {
		return this.dataStatuss;
	}
	public void setDataStatuss(Integer[] dataStatuss) {
		this.dataStatuss = dataStatuss;
	}


	public Integer getStartDataStatus() {
		return this.startDataStatus;
	}
	public void setStartDataStatus(Integer startDataStatus) {
		this.startDataStatus = startDataStatus;
	}


	public Integer getEndDataStatus() {
		return this.endDataStatus;
	}
	public void setEndDataStatus(Integer endDataStatus) {
		this.endDataStatus = endDataStatus;
	}


	public Long getCreateTime() {
		return this.createTime;
	}
	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}


	public Long[] getCreateTimes() {
		return this.createTimes;
	}
	public void setCreateTimes(Long[] createTimes) {
		this.createTimes = createTimes;
	}


	public Long getStartCreateTime() {
		return this.startCreateTime;
	}
	public void setStartCreateTime(Long startCreateTime) {
		this.startCreateTime = startCreateTime;
	}


	public Long getEndCreateTime() {
		return this.endCreateTime;
	}
	public void setEndCreateTime(Long endCreateTime) {
		this.endCreateTime = endCreateTime;
	}


	public Long getModifyTime() {
		return this.modifyTime;
	}
	public void setModifyTime(Long modifyTime) {
		this.modifyTime = modifyTime;
	}


	public Long[] getModifyTimes() {
		return this.modifyTimes;
	}
	public void setModifyTimes(Long[] modifyTimes) {
		this.modifyTimes = modifyTimes;
	}


	public Long getStartModifyTime() {
		return this.startModifyTime;
	}
	public void setStartModifyTime(Long startModifyTime) {
		this.startModifyTime = startModifyTime;
	}


	public Long getEndModifyTime() {
		return this.endModifyTime;
	}
	public void setEndModifyTime(Long endModifyTime) {
		this.endModifyTime = endModifyTime;
	}


}


