package com.uinnova.product.eam.web.eam.mvc;

import com.binary.framework.web.RemoteResult;
import com.binary.jdbc.Page;
import com.uinnova.product.eam.model.vo.GlobalSearchQueryVo;
import com.uinnova.product.eam.model.vo.GlobalSearchResVo;
import com.uinnova.product.eam.service.IGlobalSearchService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 工作台-消息&公告
 */
@RestController
@RequestMapping("/global/search")
public class GlobalSearchMvc {

    @Autowired
    private IGlobalSearchService globalSearchService;


    @PostMapping("/list")
    public RemoteResult workflowMsgSave(@RequestBody GlobalSearchQueryVo params) {
        Page<GlobalSearchResVo> result = globalSearchService.search(params);
        return new RemoteResult(result);
    }
}
