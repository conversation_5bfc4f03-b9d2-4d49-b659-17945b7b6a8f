package com.uinnova.product.eam.service.cj.dao;

import com.uinnova.product.eam.model.cj.domain.ChapterInstance;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;


/**
 * 方案章节实例Dao
 * <AUTHOR>
 */
@Component
public class PlanChapterInstanceDao extends AbstractESBaseDao<ChapterInstance, ChapterInstance> {
    @Override
    public String getIndex() {
        return "uino_cj_plan_chapter_instance";
    }

    @Override
    public String getType() {
        return getIndex();
    }

    @PostConstruct
    public void init() {
        initIndex();
    }
}
