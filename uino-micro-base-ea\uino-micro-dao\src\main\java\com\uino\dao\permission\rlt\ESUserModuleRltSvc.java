package com.uino.dao.permission.rlt;

import java.util.List;

import jakarta.annotation.PostConstruct;

import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.stereotype.Service;

import com.uino.dao.AbstractESBaseDao;
import com.uino.dao.ESConst;
import com.uino.bean.permission.base.SysUserModuleRlt;
import com.uino.bean.permission.query.CSysUserModuleRlt;

/**
 * <b>用户-功能模块关系
 * 
 * <AUTHOR>
 */
@Service
public class ESUserModuleRltSvc extends AbstractESBaseDao<SysUserModuleRlt, CSysUserModuleRlt> {

    @Override
    public String getIndex() {
        return ESConst.INDEX_SYS_USER_MODULE_RLT;
    }

    @Override
    public String getType() {
        return ESConst.INDEX_SYS_USER_MODULE_RLT;
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }

    /**
     * 获取用户下的拥有的功能模块关系
     * 
     * @param userId
     * @return
     */
    public List<SysUserModuleRlt> getListByUserId(Long userId) {
        return super.getListByQuery(1, 2000, QueryBuilders.termQuery("userId", userId)).getData();
    }

}
