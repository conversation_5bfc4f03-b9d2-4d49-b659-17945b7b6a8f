package com.uinnova.product.eam.service;

import com.uinnova.product.eam.model.dto.MatrixStencilSortInfoDTO;
import com.uinnova.product.eam.model.dto.SysArtifactVo;
import com.uinnova.product.eam.model.dto.SysModelVo;
import com.uinnova.product.eam.model.dto.SysTemplateDto;
import com.uino.bean.permission.base.SysModule;

import java.util.List;

public interface IModuleSvc {

    /**
     * 保存模块
     */
    SysModule eamSaveModule(SysModule saveDto);

    List<SysArtifactVo>   queryDiagramType();

    List<SysModelVo> queryModuleType();

    List<SysTemplateDto> querySchemeType();

    Boolean deleteAssertDir(Long assertDirId);

    /**
     * 查询矩阵的分类信息
     * @return List<MatrixStencilSortInfoDTO>
     */
    List<MatrixStencilSortInfoDTO> queryMatrixType();
}
