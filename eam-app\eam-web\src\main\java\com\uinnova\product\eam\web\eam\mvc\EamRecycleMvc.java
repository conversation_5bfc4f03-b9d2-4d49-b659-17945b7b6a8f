package com.uinnova.product.eam.web.eam.mvc;

import com.binary.core.util.BinaryUtils;
import com.binary.framework.web.RemoteResult;
import com.uinnova.product.eam.model.EamCategoryCdt;
import com.uinnova.product.eam.model.diagram.SpaceResourceResultInfo;
import com.uinnova.product.eam.service.EamModelSvc;
import com.uinnova.product.eam.service.EamRecycleSvc;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;

/**
 * 回收站相关接口
 * <AUTHOR>
 */
@RestController
@RequestMapping("/eam/recycle")
public class EamRecycleMvc {
    @Resource
    private EamRecycleSvc recycleSvc;
    @Resource
    private EamModelSvc modelSvc;

    @GetMapping("/list")
    @ModDesc(desc = "通过目录id获取视图、系统、方案等资源信息", pDesc = "文件夹id", rDesc = "视图、系统、方案信息", rType = RemoteResult.class)
    public RemoteResult queryResource(@RequestParam Long dirId, @RequestParam String like) {
        BinaryUtils.checkEmpty(dirId, "回收站目录id不可为空!");
        SpaceResourceResultInfo result = recycleSvc.queryResource(dirId, like);
        return new RemoteResult(result);
    }

    @PostMapping("/restoreCheck")
    @ModDesc(desc = "校验还原文件夹是否已删除", pDesc = "文件夹id或视图id或方案id", rDesc = "还原结果", rType = RemoteResult.class)
    public RemoteResult restoreCheck(@RequestBody EamCategoryCdt cdt) {
        String result = recycleSvc.restoreCheck(cdt);
        return new RemoteResult(true, result == null?1:2, result);
    }

    @PostMapping("/restoreBatch")
    @ModDesc(desc = "回收站文件夹还原", pDesc = "文件夹id或视图id或方案id", rDesc = "还原结果", rType = RemoteResult.class)
    public RemoteResult restore(@RequestBody EamCategoryCdt cdt) {
        Integer result = recycleSvc.restoreBatch(cdt);
        return new RemoteResult(result);
    }

    @PostMapping("/deleteBatch")
    @ModDesc(desc = "回收站文件夹清除", pDesc = "文件夹id或视图id或方案id", rDesc = "还原结果", rType = RemoteResult.class)
    public RemoteResult deleteBatch(@RequestBody EamCategoryCdt cdt) {
        Integer result = recycleSvc.deleteBatch(cdt);
        return new RemoteResult(result);
    }

}
