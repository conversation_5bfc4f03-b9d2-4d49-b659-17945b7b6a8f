package com.uino.bean.tp.base;

import java.math.BigDecimal;
import java.util.List;

import com.uino.bean.tp.enums.ExtractMetricTypeEnum;
import com.uino.bean.tp.enums.MergeMetricTypeEnum;
import com.uino.bean.tp.enums.NewMetricTypeEnum;
import com.uino.bean.tp.enums.TpRuleStatusEnum;
import com.uino.bean.tp.enums.TpRuleTypeEnum;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * tp规则
 * @author: weixuesong
 * @create: 2020/06/09 14:45
 **/
@Data
@ApiModel(value = "tp规则", description = "tp规则")
public class TpRuleDTO implements Comparable<TpRuleDTO>{

    private Long id;
    /**
     * 规则状态
     */
    @ApiModelProperty(value = "规则状态")
    private TpRuleStatusEnum dataStatus;
    /**
     * 规则类型
     */
    @ApiModelProperty(value = "规则类型")
    private TpRuleTypeEnum ruleType;
    /**
     * 规则名
     */
    @ApiModelProperty(value = "规则名")
    private String ruleName;
    /**
     * 规则描述
     */
    @ApiModelProperty(value = "规则描述")
    private String description;
    /**
     * 权重 指标丰富时必填
     */
    @ApiModelProperty(value = "权重 指标丰富时必填")
    private BigDecimal weight = BigDecimal.ZERO;

    @ApiModelProperty(value = "分类名称")
    private String className;

    @ApiModelProperty(value = "分类id")
    private Long classId;
    /**
     * 标签映射信息 指标丰富时必填
     */
    @ApiModelProperty(value = "标签映射信息 指标丰富时必填")
    private List<LabelMappingDTO> labelMappings;
    /**
     * 指标名 设置阈值时必填
     */
    @ApiModelProperty(value = "指标名 设置阈值时必填")
    private String metric;
    /**
     * 阈值筛选范围 设置阈值时必填，目前只有一个
     */
    @ApiModelProperty(value = "阈值筛选范围 设置阈值时必填，目前只有一个")
    private List<ThresholdMapping> thresholdMappings;
    /**
     * 阈值信息 设置阈值时必填，目前只有一个
     */
    @ApiModelProperty(value = "阈值信息 设置阈值时必填，目前只有一个")
    private List<ThresholdInfo> thresholdInfos;

    /**
     * 阈值通知配置,目前只有一个
     */
    @ApiModelProperty(value = "阈值通知配置,目前只有一个")
    private List<NotifyConfigDTO> notifyConfigs;

    /**
     * 衍生阈值信息，设置衍生指标时必填
     */
    @ApiModelProperty(value = "衍生阈值信息，设置衍生指标时必填")
    private NewMetricDTO newMetricInfo;
    /**
     * 指标合并信息，指标合并规则必填
     */
    @ApiModelProperty(value = "指标合并信息，指标合并规则必填")
    private MergeMetricDTO mergeMetricInfo;
    /**
     * 指标萃取信息，指标萃取规则必填
     */
    @ApiModelProperty(value = "指标萃取信息，指标萃取规则必填")
    private ExtractMetricDTO extractMetricInfo;
    /**
     * 规则生效开始时间，格式HH:mm:ss，暂定为每天
     */
    @ApiModelProperty(value = "规则生效开始时间，格式HH:mm:ss，暂定为每天")
    private String startTime;
    /**
     * 规则生效结束时间，格式HH:mm:ss，暂定为每天
     */
    @ApiModelProperty(value = "规则生效结束时间，格式HH:mm:ss，暂定为每天")
    private String stopTime;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "修改人")
    private String modifier;

    @ApiModelProperty(value = "创建时间")
    private Long createTime;

    @ApiModelProperty(value = "修改时间")
    private Long modifyTime;
    /**
     * 标签扩展规则必填
     */
    @ApiModelProperty(value = "标签扩展规则必填")
    private MetricExtendDTO metricExtendInfo;

    @Override
    public int compareTo(TpRuleDTO o) {
        // 先比较权重，权重一样比较修改时间
        try {
            if (o.getWeight().compareTo(this.weight) > 0) {
                return 1;
            } else if (o.getWeight().compareTo(this.weight) < 0) {
                return -1;
            } else if (o.getModifyTime().compareTo(this.modifyTime) > 0) {
                return 1;
            } else if (o.getModifyTime().compareTo(this.modifyTime) < 0) {
                return -1;
            } else {
                return 0;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }

    @Data
    @ApiModel(value = "阈值信息", description = "阈值信息")
    public static class ThresholdInfo {
        private Long id;
        /**
         * 连续几个周期
         */
        @ApiModelProperty(value = "连续几个周期")
        private Integer period;
        /**
         * 计算符号
         */
        @ApiModelProperty(value = "计算符号")
        private String expression;
        /**
         * 阈值
         */
        @ApiModelProperty(value = "阈值")
        private String value;
        /**
         * 计算符号为between时的最小值
         */
        @ApiModelProperty(value = "计算符号为between时的最小值")
        private String minValue;
        /**
         * 计算符号为between时的最大值
         */
        @ApiModelProperty(value = "计算符号为between时的最大值")
        private String maxValue;
        /**
         * 告警级别id
         */
        @ApiModelProperty(value = "告警级别id")
        private Long severityId;

        /**
         * 告警级别
         */
        @ApiModelProperty(value = "告警级别")
        private Integer severityLevel;
    }

    @Data
    @ApiModel(value = "阈值筛选范围", description = "阈值筛选范围")
    public static class ThresholdMapping {
        /**
         * 属性名
         */
        @ApiModelProperty(value = "属性名")
        private String attrName;
        /**
         * 计算符号
         */
        @ApiModelProperty(value = "计算符号")
        private String expression;
        /**
         * 属性值，设置为“全部”时传空
         */
        @ApiModelProperty(value = "属性值，设置为“全部”时传空")
        private String value;
        /**
         * 计算符号为between时的最小值
         */
        @ApiModelProperty(value = "计算符号为between时的最小值")
        private String minValue;
        /**
         * 计算符号为between时的最大值
         */
        @ApiModelProperty(value = "计算符号为between时的最大值")
        private String maxValue;
    }

    @Data
    @ApiModel(value = "标签映射信息", description = "标签映射信息")
    public static class LabelMappingDTO {
        /**
         * 源名称
         */
        @ApiModelProperty(value = "源名称")
        private String originName;
        /**
         * 映射名称
         */
        @ApiModelProperty(value = "映射名称")
        private String targetName;
    }

    @Data
    @ApiModel(value = "衍生阈值信息", description = "衍生阈值信息")
    public static class NewMetricDTO {
        /**
         * 衍生指标名 衍生指标规则必填
         */
        @ApiModelProperty(value = "衍生指标名 衍生指标规则必填")
        private String newMetric;
        /**
         * 计算符号
         */
        @ApiModelProperty(value = "计算符号")
        private String expression;
        /**
         * 计算方式
         */
        @ApiModelProperty(value = "计算方式")
        private NewMetricTypeEnum type;
        /**
         * 参与计算的标签名
         */
        @ApiModelProperty(value = "参与计算的标签名")
        private String attrKey;

    }

    @Data
    @ApiModel(value = "指标合并信息", description = "指标合并信息")
    public static class MergeMetricDTO {
        @ApiModelProperty(value = "指标合并类型")
        private MergeMetricTypeEnum type;
        @ApiModelProperty(value = "指标合并信息")
        private List<MergeCalcDTO> mergeInfos;
        /**
         * 合并后的指标名
         */
        @ApiModelProperty(value = "合并后的指标名")
        private String mergeMetricName;
        /**
         * 计算间隔
         */
        @ApiModelProperty(value = "计算间隔")
        private Integer interval;
        /**
         * 属性名
         */
        @ApiModelProperty(value = "属性名")
        private String attrKey;
        /**
         * 属性值
         */
        @ApiModelProperty(value = "属性值")
        private String attrValue;
    }

    @Data
    @ApiModel(value = "指标合并信息", description = "指标合并信息")
    public static class MergeCalcDTO {
        /**
         * 计算符号
         */
        @ApiModelProperty(value = "计算符号")
        private String expression;
        /**
         * 指标名
         */
        @ApiModelProperty(value = "指标名")
        private String metric;
    }

    @Data
    @ApiModel(value = "指标萃取信息", description = "指标萃取信息")
    public static class ExtractMetricDTO {
        private ExtractMetricTypeEnum type;
        /**
         * 记录索引名
         */
        @ApiModelProperty(value = "记录索引名")
        private String indexName;
        /**
         * es索引
         */
        @ApiModelProperty(value = "es索引")
        private String index;
        /**
         * 时间字段
         */
        @ApiModelProperty(value = "时间字段")
        private String timeField;
        /**
         * 聚合维度
         */
        @ApiModelProperty(value = "聚合维度")
        private String aggField;
        /**
         * 萃取指标名
         */
        @ApiModelProperty(value = "萃取指标名")
        private String extractMetricName;
        /**
         * 计算间隔
         */
        @ApiModelProperty(value = "计算间隔")
        private Integer interval;
        /**
         * 查询dsl
         */
        @ApiModelProperty(value = "查询dsl")
        private String queryDsl;
    }

    @Data
    @ApiModel(value = "阈值通知配置", description = "阈值通知配置")
    public static class NotifyConfigDTO {

        private Long channelId;

        private String type;

        private List<Long> orgIds;

        private List<Long> roleIds;
    }

    @Data
    public static class MetricExtendDTO {
        private List<String> metricAttrs;
    }
}
