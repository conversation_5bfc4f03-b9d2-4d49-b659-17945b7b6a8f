package com.uinnova.product.eam.model;

import com.binary.framework.bean.annotation.Comment;

import java.io.Serializable;

@Comment("用户信息")
public class SysOpInfo implements Serializable{

	private static final long serialVersionUID = 1L;
	
	@Comment("ID[ID]")
	private Long opId;
	
	@Comment("操作员编码[OP_CODE]")
	private String opCode;


	@Comment("操作员姓名[OP_NAME]")
	private String opName;
	
	@Comment("查看的权限")
	private Integer seeAuth;
	
	@Comment("编辑的权限")
	private Integer editAuth;

	@Comment("用户头像")
	private String custom3;

	public String getCustom3() {
		return custom3;
	}

	public void setCustom3(String custom3) {
		this.custom3 = custom3;
	}

	public Long getOpId() {
		return opId;
	}

	public void setOpId(Long opId) {
		this.opId = opId;
	}

	public String getOpCode() {
		return opCode;
	}

	public void setOpCode(String opCode) {
		this.opCode = opCode;
	}

	public String getOpName() {
		return opName;
	}

	public void setOpName(String opName) {
		this.opName = opName;
	}

	public Integer getSeeAuth() {
		return seeAuth;
	}

	public void setSeeAuth(Integer seeAuth) {
		this.seeAuth = seeAuth;
	}

	public Integer getEditAuth() {
		return editAuth;
	}

	public void setEditAuth(Integer editAuth) {
		this.editAuth = editAuth;
	}
	
	
	
	

}
