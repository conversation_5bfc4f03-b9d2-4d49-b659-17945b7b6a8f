package com.uinnova.product.eam.model;

import com.binary.framework.bean.annotation.Comment;

import java.io.Serializable;

public class EamDirInfoDelCdt implements Serializable {
	private static final long serialVersionUID = 1L;

	@Comment("目录ID数组")
	private Long[] dirIds;
	
	@Comment("视图ID数组")
	private Long[] diagramIds;

	public Long[] getDirIds() {
		return dirIds;
	}

	public void setDirIds(Long[] dirIds) {
		this.dirIds = dirIds;
	}

	public Long[] getDiagramIds() {
		return diagramIds;
	}

	public void setDiagramIds(Long[] diagramIds) {
		this.diagramIds = diagramIds;
	}

}
