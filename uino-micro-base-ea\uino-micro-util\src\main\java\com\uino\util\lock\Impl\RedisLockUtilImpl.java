package com.uino.util.lock.Impl;

import com.uino.util.lock.LockUtil;
import com.uino.util.lock.condition.EnableRedisCondition;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * @Title: RedisLockUtilImpl
 * @Description: distributed lock redis implementation
 * @Author: YGQ
 * @Create: 2021-07-14 13:02
 **/
@Slf4j
@Component
@Conditional(EnableRedisCondition.class)
public class RedisLockUtilImpl implements LockUtil {

    private final RedissonClient redissonClient;

    public RedisLockUtilImpl(@Autowired(required = false) RedissonClient redissonClient) {
        this.redissonClient = redissonClient;
    }

    @Override
    public Boolean tryLock(String lockName, Boolean... wait) {
        boolean isLock = false;
        try {
            RLock rlock = redissonClient.getLock(lockName);
            if (wait.length > 0 && wait[0]) {
                isLock = rlock.tryLock();
            } else {
                if (!rlock.isLocked()) {
                    isLock = rlock.tryLock();
                }
            }
        } catch (Exception e) {
            log.error(">>> try lock [ {} ] error: {}", lockName, e.getMessage());
        }
        return isLock;
    }

    @Override
    public Boolean tryLock(String lockName, long time, TimeUnit unit, Boolean... wait) {
        boolean isLock = false;
        try {
            RLock rlock = redissonClient.getLock(lockName);
            if (wait.length > 0 && wait[0]) {
                isLock = rlock.tryLock(time, unit);
            } else {
                if (!rlock.isLocked()) {
                    isLock = rlock.tryLock(time, unit);
                }
            }
        } catch (Exception e) {
            log.error(">>> try lock [ {} ] error: {}", lockName, e.getMessage());
        }
        return isLock;
    }

    @Override
    public Boolean unLock(String lockName) {
        boolean isUnlock = false;
        try {
            redissonClient.getLock(lockName).unlock();
            isUnlock = true;
        } catch (Exception e) {
            log.error(">>> releases the lock [ {} ] error: {}", lockName, e.getMessage());
        }
        return isUnlock;
    }

    @Override
    public Boolean forceUnlock(String lockName) {
        return redissonClient.getLock(lockName).forceUnlock();
    }

    @Override
    public Boolean isLocked(String lockName) {
        return redissonClient.getLock(lockName).isLocked();
    }
}
