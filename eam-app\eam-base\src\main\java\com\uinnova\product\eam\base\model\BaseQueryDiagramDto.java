package com.uinnova.product.eam.base.model;

import com.binary.framework.bean.annotation.Comment;
import com.uino.bean.cmdb.base.LibType;
import lombok.Data;

import java.util.List;

/**
 * 视图查询参数接收实体
 * <AUTHOR>
 */
@Data
public class BaseQueryDiagramDto {

    @Comment("页数")
    private int pageNum = 1;

    @Comment("条数")
    private int pageSize = 200;

    @Comment("文件夹Id,历史版本时传入历史目录id")
    private Long id;

    @Comment("模型树id")
    private Long modelId;

    @Comment("视图加密id")
    private String diagramId;

    @Comment("视图加密id")
    private List<String> diagramIds;

    private LibType libType = LibType.PRIVATE;

    @Comment("用户code")
    private String ownerCode;

    @Comment("模糊查询字段信息")
    private String like;

    @Comment("查询类型：0:名称,1:作者,2:标签,3:CI")
    private Integer status;

    @Comment("保留字段,前端过滤已发布1或未发布0")
    private Integer releaseStatus;

    @Comment("是否包含与我协作的查询")
    private boolean isCooperate = false;

    @Comment("是否历史版本查询")
    private boolean historyFlag = false;

    @Comment("筛选类型[0:初始化,1:单图,2:组合视图,3:文件夹,4:我的模版]")
    private Integer diagramType;

    @Comment("查询类型：0:名称,1:作者,2:标签,3:CI")
    private Integer queryType;

    @Comment("按照字段排序")
    private String orders;

    @Comment("模型版本id")
    private Long versionId;

    @Comment("流程实例id")
    private String processInstanceId;
}
