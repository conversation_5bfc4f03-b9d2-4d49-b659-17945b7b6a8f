package com.binary.framework.util;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.PrintWriter;
import java.util.List;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.springframework.web.servlet.ModelAndView;

import com.binary.core.http.HttpUtils;
import com.binary.core.http.URLResolver;
import com.binary.core.io.FileSystem;
import com.binary.core.io.Resource;
import com.binary.core.util.BinaryUtils;
import com.binary.framework.Local;
import com.binary.framework.exception.ControllerException;
import com.binary.framework.exception.ServiceException;
import com.binary.framework.web.RemoteResult;
import com.binary.json.JSON;

public abstract class ControllerUtils {


	private static AjaxResultListener ajaxResultListener;



	public static AjaxResultListener getAjaxResultListener() {
		return ajaxResultListener;
	}

	public static void setAjaxResultListener(AjaxResultListener ajaxResultListener) {
		ControllerUtils.ajaxResultListener = ajaxResultListener;
	}



	public static ModelAndView returnJson(HttpServletRequest request, HttpServletResponse response, Object result) {
		response.setCharacterEncoding("utf-8");
//		response.setContentType("text/html");
		response.setContentType("application/json");
		if(ajaxResultListener != null) {
			boolean ba = ajaxResultListener.beforeWrite(request, response, result);
			if(!ba) return null;
		}

		RemoteResult jr = new RemoteResult(result);
		try {
			PrintWriter pw = response.getWriter();
			pw.write(JSON.toString(jr));
			pw.flush();

			if(ajaxResultListener != null) {
				ajaxResultListener.afterWrite(request, response, result);
			}
		} catch (IOException e) {
			throw new ControllerException(e);
		}
		return null;
	}



	public static ModelAndView returnSimpleJson(HttpServletRequest request, HttpServletResponse response, Object result) {
		response.setCharacterEncoding("utf-8");
//		response.setContentType("text/html");
		response.setContentType("application/json");
		if(ajaxResultListener != null) {
			boolean ba = ajaxResultListener.beforeWrite(request, response, result);
			if(!ba) return null;
		}

		if(result != null) {
			try {
				PrintWriter pw = response.getWriter();
				pw.write(JSON.toString(result));
				pw.flush();

				if(ajaxResultListener != null) {
					ajaxResultListener.afterWrite(request, response, result);
				}
			} catch (IOException e) {
				throw new ControllerException(e);
			}
		}

		return null;
	}




	public static ModelAndView returnOriginalJson(HttpServletRequest request, HttpServletResponse response, Object result) {
		if(ajaxResultListener != null) {
			boolean ba = ajaxResultListener.beforeWrite(request, response, result);
			if(!ba) return null;
		}

		if(result != null) {
			try {
				PrintWriter pw = response.getWriter();
				pw.write(JSON.toString(result));
				pw.flush();

				if(ajaxResultListener != null) {
					ajaxResultListener.afterWrite(request, response, result);
				}
			} catch (IOException e) {
				throw new ControllerException(e);
			}
		}

		return null;
	}





	public static ModelAndView returnText(HttpServletRequest request, HttpServletResponse response, String text) {
		response.setCharacterEncoding("utf-8");
		response.setContentType("text/html");
		if(ajaxResultListener != null) {
			boolean ba = ajaxResultListener.beforeWrite(request, response, text);
			if(!ba) return null;
		}

		if(text != null) {
			try {
				PrintWriter pw = response.getWriter();
				pw.write(text);
				pw.flush();

				if(ajaxResultListener != null) {
					ajaxResultListener.afterWrite(request, response, text);
				}
			} catch (IOException e) {
				throw new ControllerException(e);
			}
		}

		return null;
	}




	public static ModelAndView returnOriginalText(HttpServletRequest request, HttpServletResponse response, String text) {
		if(ajaxResultListener != null) {
			boolean ba = ajaxResultListener.beforeWrite(request, response, text);
			if(!ba) return null;
		}

		if(text != null) {
			try {
				PrintWriter pw = response.getWriter();
				pw.write(text);
				pw.flush();

				if(ajaxResultListener != null) {
					ajaxResultListener.afterWrite(request, response, text);
				}
			} catch (IOException e) {
				throw new ControllerException(e);
			}
		}

		return null;
	}





	public static String[] splitStringPattern(String strPattern) {
		if(strPattern.indexOf(";") > 0) {
			return strPattern.split("[;]");
		}else {
			return new String[]{strPattern};
		}
	}



	public static ModelAndView returnResource(HttpServletRequest request, HttpServletResponse response, Resource resource) {
		return returnResource(request, response, resource, null, false, null);
	}



	public static ModelAndView returnResource(HttpServletRequest request, HttpServletResponse response, Resource resource, String contentType) {
		return returnResource(request, response, resource, contentType, false, null);
	}



	/**
	 * 下载文件
	 * @param request
	 * @param response
	 * @param resource
	 * @param contentType
	 * @param openWindow : 是否弹出"另存为"窗口
	 * @param fileName : 在"另存为"窗口中显示的名字
	 * @return
	 */
	public static ModelAndView returnResource(HttpServletRequest request, HttpServletResponse response, Resource resource, String contentType, boolean openWindow, String fileName) {
		if(resource==null || !resource.exists()) {
			throw new ControllerException(" is not found resource '"+resource+"'! ");
		}
		if(BinaryUtils.isEmpty(contentType)) {
			contentType = "application/octet-stream";
		}
		if(BinaryUtils.isEmpty(fileName)) {
			fileName = resource.getName();
		}
		response.setContentType(contentType);
		if(!BinaryUtils.isEmpty(fileName)) {
			response.addHeader("Content-Disposition",(openWindow?"attachment;":"")+"filename="+URLResolver.encode(fileName, Local.getCharset()));
		}


		try {
			OutputStream os = response.getOutputStream();
			InputStream is = resource.getInputStream();
			try {
				FileSystem.copy(is, os);
				os.flush();
			}finally {
				if(is != null) is.close();
			}
		} catch (IOException e) {
			throw new ControllerException(e);
		}

		return null;
	}






	/**
	 * 转换远程返回的统一对象
	 * @param jsonstring
	 * @param clazz
	 * @return
	 */
	public static <T> T toRemoteJsonObject(String jsonstring, Class<T> clazz) {
		return JSON.toRemoteObject(jsonstring, clazz);
	}


	/**
	 * 转换远程返回的统一数组对象
	 * @param jsonstring
	 * @param clazz
	 * @return
	 */
	public static <T> List<T> toRemoteJsonList(String jsonstring, Class<T> clazz) {
		return JSON.toRemoteList(jsonstring, clazz);
	}





	/**
	 * 获取客户端IP
	 * @param request
	 * @return
	 */
	public static String getClientIp(HttpServletRequest request) {
		String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknow".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }

        if(BinaryUtils.isEmpty(ip)) {
        	throw new ControllerException(" is unknown client ip! ");
        }
        return ip;
	}




	/**
	 * 格式化ContextPath
	 * @param contextPath
	 * @return
	 */
	public static String formatContextPath(String contextPath) {
		return HttpUtils.formatContextPath(contextPath);
	}





	/**
	 * 页面跳转
	 * @param request
	 * @param response
	 * @param url
	 */
	public static void sendRedirect(HttpServletRequest request, HttpServletResponse response, String url) {
		BinaryUtils.checkEmpty(url, "url");
		url = url.trim();
		String ContextPath = request.getContextPath();

		String nginxHost = request.getHeader("host");

		if(url.charAt(0) == '/') {
			if(!url.startsWith(ContextPath)) {
				url = ContextPath + url;
			}
		}else {
			if(!url.startsWith("http://") && !url.startsWith("https://")) {
				url = formatContextPath(url);
				if(!url.startsWith(ContextPath)) {
					url = ContextPath + url;
				}
			}
		}

		try {
			String nginxUrl = getNginxUrl(url, nginxHost);
			response.sendRedirect(nginxUrl);
		} catch (IOException e) {
			throw new ServiceException(e);
		}
	}

	private static String getNginxUrl(String url, String nginxHost) {

		if (BinaryUtils.isEmpty(url) || BinaryUtils.isEmpty(nginxHost)) {
			return url;
		} else {
			if (url.startsWith("/")) {
				return "http://" + nginxHost + url;
			} else {
				StringBuilder sb = new StringBuilder();
				String[] split = url.split("/");
				sb.append(split[0]).append("//").append(nginxHost);
				for (int i = 3; i < split.length; i++) {
					sb.append("/").append(split[i]);
				}
				return sb.toString();
			}
		}
	}


}
