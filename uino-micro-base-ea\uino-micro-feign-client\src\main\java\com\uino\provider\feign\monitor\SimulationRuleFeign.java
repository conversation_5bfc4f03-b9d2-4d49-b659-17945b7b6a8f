package com.uino.provider.feign.monitor;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.binary.jdbc.Page;
import com.uino.bean.monitor.base.SimulationRuleInfo;
import com.uino.bean.monitor.query.SimulationRuleSearchBean;
import com.uino.provider.feign.config.BaseFeignConfig;

@Component
@FeignClient(name = "${feign-server-name:tarsier-platform}", path = "${feign-server-path:tarsier-platform}/feign/simulationRule", configuration = {
		BaseFeignConfig.class })
public interface SimulationRuleFeign {

	/**
	 * 分页查询模拟规则
	 * 
	 * @param bean
	 * @return
	 */
	@PostMapping("querySimulationRuleInfoPage")
	Page<SimulationRuleInfo> querySimulationRuleInfoPage(@RequestBody SimulationRuleSearchBean bean);

	/**
	 * 查询模拟规则列表
	 * 
	 * @param bean
	 * @return
	 */
	@PostMapping("querySimulationRuleInfoList")
	List<SimulationRuleInfo> querySimulationRuleInfoList(@RequestBody SimulationRuleSearchBean bean);

	/**
	 * 保存数据模拟规则
	 * 
	 * @param ruleInfo
	 * @return
	 */
	@PostMapping("saveOrUpdateSimulationRule")
	Long saveOrUpdateSimulationRule(@RequestBody SimulationRuleInfo ruleInfo);

	/**
	 * 根据id删除数据模拟规则
	 * 
	 * @param id
	 * @return
	 */
	@PostMapping("deleteSimulationRuleById")
	Integer deleteSimulationRuleById(@RequestBody Long id);
}
