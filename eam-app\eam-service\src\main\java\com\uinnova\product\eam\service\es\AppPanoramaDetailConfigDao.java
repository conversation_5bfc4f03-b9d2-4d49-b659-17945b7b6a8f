package com.uinnova.product.eam.service.es;

import com.uinnova.product.eam.comm.model.es.AppPanoramaDetailConfig;
import com.uinnova.product.eam.model.AppPanoramaDetailConfigVo;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;

/**
 * 全景配置
 * <AUTHOR>
 */
@Service
public class AppPanoramaDetailConfigDao extends AbstractESBaseDao<AppPanoramaDetailConfig, AppPanoramaDetailConfig> {
    @Override
    public String getIndex() {
        return "uino_eam_asset_panorama_detail_conf";
    }

    @Override
    public String getType() {
        return "_doc";
    }
    @PostConstruct
    public void init() {
        super.initIndex();
    }
}
