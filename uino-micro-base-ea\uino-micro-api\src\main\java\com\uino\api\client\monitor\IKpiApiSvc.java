package com.uino.api.client.monitor;

import java.util.Collection;
import java.util.List;

import org.springframework.web.multipart.MultipartFile;

import com.binary.core.io.Resource;
import com.binary.jdbc.Page;
import com.uino.bean.cmdb.business.ImportResultMessage;
import com.uino.bean.cmdb.business.ImportSheetMessage;
import com.uino.bean.monitor.base.ESKpiInfo;
import com.uino.bean.monitor.buiness.KpiRltBindDto;
import com.uino.bean.monitor.buiness.SearchKpiBean;

/**
 * kpi相关api服务
 * 
 * <AUTHOR>
 */
public interface IKpiApiSvc {

	/**
	 * 根据id获取指标数据
	 * 
	 * @param id
	 * @return
	 */
	public ESKpiInfo getKpiInfoById(Long id);
	
	/**
     * 根据id获取指标数据
     * 
     * @param ids
     * @return
     */
	public List<ESKpiInfo> getKpiInfoByIds(Collection<Long> ids);

    /**
     * 根据kpiSearchDto查询kpi分页列表
     * 
     * @param searchDto
     * @return
     */
    public Page<ESKpiInfo> queryKpiInfoPage(SearchKpiBean searchDto);

    /**
     * 持久化kpi
     * 
     * @param saveDto
     * @return
     */
    public Long saveOrUpdate(ESKpiInfo saveDto);

	/**
	 * 批量保存指标信息
	 * 
	 * @description
	 * @author: ZMJ
	 * @param kpiInfos
	 * @return
	 * @example
	 */
	public ImportSheetMessage saveOrUpdateBatch(List<ESKpiInfo> kpiInfos);
	public ImportSheetMessage saveOrUpdateBatch(Long domainId, List<ESKpiInfo> kpiInfos);

    /**
     * 根据kpiIds删除kpi相关信息
     * 
     * @param kpiIds
     */
    public void deleteByKpiIds(Collection<Long> kpiIds);

    /**
     * 导出指标数据
     * 
     * @param isTpl
     * @return
     */
    public Resource exportKpiInfos(Boolean isTpl);
    public Resource exportKpiInfos(Long domainId, Boolean isTpl);

    /**
     * 导入指标数据
     * 
     * @param file
     * @return
     */
    public ImportResultMessage importKpiInfos(MultipartFile file);
    public ImportResultMessage importKpiInfos(Long domainId, MultipartFile file);

    /**
     * 绑定分类与KPI关联关系
     * 
     * @param dto
     */
    public void bindCiClassRltToKpiInfo(KpiRltBindDto dto);

    /**
     * 解除分类与KPI关联关系
     * 
     * @param dto
     */
    public void delCiClassRltToKpiInfo(KpiRltBindDto dto);
}
