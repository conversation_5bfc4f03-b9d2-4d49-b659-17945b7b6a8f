package com.uinnova.product.eam.service.es;

import com.uinnova.product.vmdb.comm.model.rlt.CCcCiRlt;
import com.uino.bean.cmdb.base.ESCIRltInfo;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;

@Service
public class IamsCIRltDesignNonComplianceDao extends AbstractESBaseDao<ESCIRltInfo, CCcCiRlt>{
    @Override
    public String getIndex() {
        return "uino_cmdb_no_compliance_design_rlt";
    }

    @Override
    public String getType() {
        return this.getIndex();
    }
    @PostConstruct
    public void init() {
        super.initIndex();
    }
}
