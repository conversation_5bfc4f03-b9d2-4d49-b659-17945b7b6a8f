package com.uinnova.product.vmdb.comm.model.rule;

import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;

/**
 * 
 * <AUTHOR>
 *
 */
@Comment("关系节点条件表[CC_RLT_NODE_CDT]")
public class CcRltNodeCdt implements EntityBean {
    private static final long serialVersionUID = 1L;

    @Comment("ID[ID]")
    private Long id;

    @Comment("所属节点[NODE_ID]")
    private Long nodeId;

    @Comment("条件属性[ATTR_ID]")
    private Long attrId;

    @Comment("运算符[CDT_OP]")
    private Integer cdtOp;

    @Comment("条件值[CDT_VAL]")
    private String cdtVal;

    @Comment("非运算[IS_NOT]")
    private Integer isNot;

    @Comment("排列顺序[ORDER_NO]")
    private Integer orderNo;

    @Comment("所属域[DOMAIN_ID]")
    private Long domainId;

    @Comment("数据状态[DATA_STATUS]    数据状态：1-正常 0-删除")
    private Integer dataStatus;

    @Comment("创建时间[CREATE_TIME]    yyyyMMddHHmmss")
    private Long createTime;

    @Comment("修改时间[MODIFY_TIME]    yyyyMMddHHmmss")
    private Long modifyTime;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getNodeId() {
        return this.nodeId;
    }

    public void setNodeId(Long nodeId) {
        this.nodeId = nodeId;
    }

    public Long getAttrId() {
        return this.attrId;
    }

    public void setAttrId(Long attrId) {
        this.attrId = attrId;
    }

    public Integer getCdtOp() {
        return this.cdtOp;
    }

    public void setCdtOp(Integer cdtOp) {
        this.cdtOp = cdtOp;
    }

    public String getCdtVal() {
        return this.cdtVal;
    }

    public void setCdtVal(String cdtVal) {
        this.cdtVal = cdtVal;
    }

    public Integer getIsNot() {
        return this.isNot;
    }

    public void setIsNot(Integer isNot) {
        this.isNot = isNot;
    }

    public Integer getOrderNo() {
        return this.orderNo;
    }

    public void setOrderNo(Integer orderNo) {
        this.orderNo = orderNo;
    }

    public Long getDomainId() {
        return this.domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }

    public Integer getDataStatus() {
        return this.dataStatus;
    }

    public void setDataStatus(Integer dataStatus) {
        this.dataStatus = dataStatus;
    }

    public Long getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getModifyTime() {
        return this.modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }

}
