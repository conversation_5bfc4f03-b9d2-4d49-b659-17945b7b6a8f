package com.uino.bean.monitor.buiness.unit;

import java.io.Serializable;
import java.util.List;

/**
 * 
 * <AUTHOR>
 *
 */
public class UnitConverModelInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private UnitConverModel unitConverModel;

    private List<UnitModelItem> items;

    public UnitConverModelInfo(Long id, UnitConverModel unitConverModel, List<UnitModelItem> items) {
        super();
        this.id = id;
        this.unitConverModel = unitConverModel;
        this.items = items;
    }

    public UnitConverModelInfo() {
        super();
    }
    
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

    public UnitConverModel getUnitConverModel() {
        return unitConverModel;
    }

    public void setUnitConverModel(UnitConverModel unitConverModel) {
        this.unitConverModel = unitConverModel;
    }

    public List<UnitModelItem> getItems() {
        return items;
    }

    public void setItems(List<UnitModelItem> items) {
        this.items = items;
    }



}
