package com.uinnova.product.vmdb.comm.util;

import com.binary.core.exception.MessageException;
import com.binary.core.util.BinaryUtils;
import com.uinnova.product.vmdb.comm.i18n.MessageUtil;
import org.apache.poi.openxml4j.exceptions.OpenXML4JException;
import org.xml.sax.SAXException;

import javax.xml.parsers.ParserConfigurationException;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 兼容读取2003和2007版Excel的辅助类[将Excel转为CSV解析读取]
 * 
 * <AUTHOR>
 *
 */
public class ExcelCovertCSVReaderUtil {

    /**
     * 将读取到的List<String[]>格式的行数据第一行 结果转为List<String>
     * 
     * @param titles
     * @return
     */
    public static List<String> getTitlesBySheetDatas(List<String[]> datas) {
        if (datas == null || datas.size() == 0) {
            return null;
        }
        String[] titles = datas.get(0);
        List<String> sheetTitles = new ArrayList<String>();
        if (titles != null && titles.length > 0) {
            // 第一行列头不计入正文读取,但要保持索引和值至MAP
            for (int j = 0; j < titles.length; j++) {
                String t = titles[j];
                if (BinaryUtils.isEmpty(t)) {
                    t = "";
                }
                sheetTitles.add(t);
            }
        }
        return sheetTitles;
    }

    /**
     * 将读取到的List<String[]>格式的行数据 结果转为List<Map<String, String>>
     * 
     * @param datas
     *            String[]格式的行数据集合
     * @param isStdKey
     *            是否需要将结果中Map的Key值(列头值)标准化(转成大写)[true Or false]
     * @return List<Map<String, String>>: Map中Key为列头值;Value列头对应的行坐标的正文列值
     */
    public static List<Map<String, String>> arrayConvertMapsBySheetDatas(List<String[]> datas, boolean isStdKey) {
        if (datas == null || datas.size() == 0) {
            return null;
        }
        List<Map<String, String>> maps = new ArrayList<Map<String, String>>();

        // 遍历行值
        // 记录标题行类容: key:列索引; Value:列值
        Map<Integer, String> titleIdxStdValMap = new HashMap<Integer, String>();
        for (int i = 0; i < datas.size(); i++) {
            String[] rowDatas = datas.get(i);

            if (i == 0) {
                // 列头行值为空直接终止
                if (rowDatas == null || rowDatas.length == 0) {
                    break;
                }

                // 第一行列头不计入正文读取,但要保持索引和值至MAP
                for (int j = 0; j < rowDatas.length; j++) {
                    if (BinaryUtils.isEmpty(rowDatas[j])) {
                        continue;
                    }
                    String titleCellVal = rowDatas[j];
                    // 是否需要标准化
                    if (isStdKey) {
                        titleCellVal = titleCellVal.toUpperCase();
                    }
                    if (!BinaryUtils.isEmpty(titleCellVal)) {
                        titleIdxStdValMap.put(j, titleCellVal.trim());
                    }
                }
            }

            if (i > 0) {
                if (rowDatas == null || rowDatas.length == 0) {
                    continue;
                }
                // 遍历每行取出列值并存入Map： key:属性标准名; Value:对应的列值
                Map<String, String> commMap = new HashMap<String, String>();
                for (int j = 0; j < rowDatas.length; j++) {
                    String val = "";
                    if (!BinaryUtils.isEmpty(rowDatas[j])) {
                        val = rowDatas[j].trim();
                    }
                    // 存入map:key:列头值; Value:对应的正文列值
                    if (!BinaryUtils.isEmpty(titleIdxStdValMap.get(j))) {
                        commMap.put(titleIdxStdValMap.get(j), val);
                    }
                }

                if (!commMap.isEmpty()) {
                    maps.add(commMap);
                }
            }
        }

        return maps;
    }

    /**
     * 兼容读取2003和2007版Excel指定的Sheet的内容
     * 
     * @param filePath
     *            文件绝对路径
     * @param sheetName
     *            指定Sheet名字
     * @return List<String[]> 格式行数据集合
     * @throws FileNotFoundException
     * @throws IOException
     * @throws OpenXML4JException
     * @throws ParserConfigurationException
     * @throws SAXException
     */
    public static List<String[]> readerExcelByPath(String filePath, String sheetName) throws FileNotFoundException, IOException, OpenXML4JException, ParserConfigurationException, SAXException {
        MessageUtil.checkEmpty(filePath, "filePath");
        if (BinaryUtils.isEmpty(sheetName)) {
            return new ArrayList<String[]>();
        }

        List<String[]> list = null;
        if (filePath.toLowerCase().endsWith(CommUtil.EXCEL03_XLS_EXTENSION)) {
            list = XLSCovertCSVReader.readerExcelByPath(filePath, sheetName, -1);
        } else if (filePath.toLowerCase().endsWith(CommUtil.EXCEL07_XLSX_EXTENSION)) {
            list = XLSXCovertCSVReader.readerExcel(filePath, sheetName, -1);
        }
        return list == null ? new ArrayList<String[]>() : list;
    }

    /**
     * 兼容读取2003和2007版Excel指定的Sheet的内容
     * 
     * @param file
     * @param sheetName
     * @return List<String[]> 格式行数据集合
     * @throws FileNotFoundException
     * @throws IOException
     * @throws OpenXML4JException
     * @throws ParserConfigurationException
     * @throws SAXException
     */
    public static List<String[]> readerExcelByFile(File file, String sheetName) throws FileNotFoundException, IOException, OpenXML4JException, ParserConfigurationException, SAXException {
        if (!file.exists()) {
            throw MessageException.i18n("BS_MDOMAIN_NOTFOUNDFILE");
        }
        if (BinaryUtils.isEmpty(sheetName)) {
            return new ArrayList<String[]>();
        }

        List<String[]> list = null;
        String filePath = file.getPath().toLowerCase();
        if (filePath.endsWith(CommUtil.EXCEL03_XLS_EXTENSION)) {
            list = XLSCovertCSVReader.readerExcelByFile(file, sheetName, -1);
        } else if (filePath.endsWith(CommUtil.EXCEL07_XLSX_EXTENSION)) {
            list = XLSXCovertCSVReader.readerExcelByFile(file, sheetName, -1);
        }
        return list == null ? new ArrayList<String[]>() : list;
    }

    /**
     * 兼容读取2003和2007版Excel指定的Sheet的内容且返回结果是Map集合
     * 
     * @param filePath
     *            文件绝对路径
     * @param sheetName
     *            sheet名称(不传则不读)
     * @param isStdKey
     *            是否需要将结果中Map的Key值(列头值)标准化(转成大写)[true Or false]
     * @return List<Map<String, String>>: Map中Key为列头值;Value列头对应的行坐标的正文列值
     * @throws FileNotFoundException
     * @throws IOException
     * @throws SAXException
     * @throws ParserConfigurationException
     * @throws OpenXML4JException
     */
    public static List<Map<String, String>> readerExcelByPathToMaps(String filePath, String sheetName, boolean isStdKey) throws FileNotFoundException, IOException, OpenXML4JException, ParserConfigurationException, SAXException {
        MessageUtil.checkEmpty(filePath, "filePath");
        if (BinaryUtils.isEmpty(sheetName)) {
            return new ArrayList<Map<String, String>>();
        }

        List<String[]> list = readerExcelByPath(filePath, sheetName);
        return arrayConvertMapsBySheetDatas(list, isStdKey);
    }

}
