package com.uinnova.product.eam.model.asset;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.base.model.AttrDefInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

@Data
public class SoftwareMasterEleDTO implements Serializable {

    @Comment("主信息元素定义")
    private AttrDefInfo software;

    @Comment("版本信息元素定义")
    private AttrDefInfo softwareEdition;

    @Comment("分类")
    private Map fieldAttrs;
}
