package com.uinnova.product.eam.model.vo;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.io.Serializable;

/**
 * 精简rlt关系vo
 * <AUTHOR>
 */
@Data
public class RltSimpleInfoVo implements Serializable {

    @Comment("ID")
    private Long id;

    @Comment("名称")
    private String name;

    @Comment("标识")
    private String ciCode;

    @Comment("标识")
    private String uniqueCode;

    @Comment("分类id")
    private Long classId;

    @Comment("分类名称")
    private String className;

    @Comment("源端ci名称")
    private String sourceCiName;

    @Comment("源端ci标识")
    private String sourceCiCode;

    @Comment("目标端ci名称")
    private String targetCiName;

    @Comment("目标端ci标识")
    private String targetCiCode;

    @Comment("是否包含")
    private Boolean include = false;

    public RltSimpleInfoVo() {
    }

    public RltSimpleInfoVo(String sourceCiCode, String targetCiCode) {
        this.sourceCiCode = sourceCiCode;
        this.targetCiCode = targetCiCode;
    }
}
