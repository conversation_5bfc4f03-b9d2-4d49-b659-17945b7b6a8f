package com.uino.provider.server.web.cmdb.mvc;

import com.alibaba.fastjson.JSONObject;
import com.uino.service.cmdb.dataset.microservice.IRelationRuleAnalysisSvc;
import com.uino.bean.cmdb.base.dataset.DataSetMallApiRelationRule;
import com.uino.bean.cmdb.base.dataset.query.QueryCiFriendDto;
import com.uino.bean.cmdb.business.dataset.FriendInfo;
import com.uino.bean.cmdb.business.dataset.QueryCondition;
import com.uino.bean.cmdb.business.dataset.RltRuleTableData;
import com.uino.provider.feign.cmdb.RelationRuleAnalysisFegin;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * @Title: RelationRuleAnalysisMvc
 * @Description: RelationRuleAnalysisMvc
 * @Author: YGQ
 * @Create: 2021-05-31 14:47
 **/
@RestController
@RequestMapping("feign/cmdb/dataset/analysis")
public class RelationRuleAnalysisMvc implements RelationRuleAnalysisFegin {

    private final IRelationRuleAnalysisSvc relationRuleAnalysisSvc;

    @Autowired
    public RelationRuleAnalysisMvc(IRelationRuleAnalysisSvc relationRuleAnalysisSvc) {
        this.relationRuleAnalysisSvc = relationRuleAnalysisSvc;
    }

    @Override
    public Map<Long, List<QueryCondition>> findTravalTree(DataSetMallApiRelationRule relationRule) {
        return relationRuleAnalysisSvc.findTravalTree(relationRule);
    }

    @Override
    public FriendInfo queryCiFriendByCiId(QueryCiFriendDto queryCiFriendDto) {
        return relationRuleAnalysisSvc.queryCiFriendByCiId(queryCiFriendDto.getSCi(), queryCiFriendDto.getDataSetId());
    }

    @Override
    public FriendInfo queryCiFriendByCiIdAndRule(QueryCiFriendDto queryCiFriendDto) {
        return relationRuleAnalysisSvc.queryCiFriendByCiIdAndRule(queryCiFriendDto.getSCi(), queryCiFriendDto.getRelationRule());
    }

    @Override
    public Map<Long, FriendInfo> queryCiFriendByCiIds(QueryCiFriendDto queryCiFriendDto) {
        return relationRuleAnalysisSvc.queryCiFriendByCiIds(queryCiFriendDto.getSCis(), queryCiFriendDto.getDataSetId(), queryCiFriendDto.getIsIncludeAllStartCI());
    }

    @Override
    public Map<Long, FriendInfo> queryCiFriendByCiIdsAndRule(QueryCiFriendDto queryCiFriendDto) {
        return relationRuleAnalysisSvc.queryCiFriendByCiIdsAndRule(queryCiFriendDto.getSCis(), queryCiFriendDto.getRelationRule(), queryCiFriendDto.getIsIncludeAllStartCI());
    }

    @Override
    public Map<Long, FriendInfo> queryCiFriendByRule(DataSetMallApiRelationRule relationRule) {
        return relationRuleAnalysisSvc.queryCiFriendByRule(relationRule);
    }

    @Override
    public Map<Long, FriendInfo> queryCiFriendByRuleWithLimit(QueryCiFriendDto queryCiFriendDto) {
        return relationRuleAnalysisSvc.queryCiFriendByRuleWithLimit(queryCiFriendDto.getRelationRule(), queryCiFriendDto.getLimit());
    }

    @Override
    public FriendInfo mergeFriendInfoMap(Map<Long, FriendInfo> friendInfoMap) {
        return relationRuleAnalysisSvc.mergeFriendInfoMap(friendInfoMap);
    }

    @Override
    public RltRuleTableData disassembleFriendInfoDataByPath(QueryCiFriendDto queryCiFriendDto) {
        return relationRuleAnalysisSvc.disassembleFriendInfoDataByPath(queryCiFriendDto.getRelationRule(), queryCiFriendDto.getCiIds(), queryCiFriendDto.getFriendInfo());
    }

    @Override
    public RltRuleTableData disassembleFriendInfoDataByCisAndRule(QueryCiFriendDto queryCiFriendDto) {
        return relationRuleAnalysisSvc.disassembleFriendInfoDataByPath(queryCiFriendDto.getSCis(), queryCiFriendDto.getRelationRule());
    }

    @Override
    public JSONObject countStatistics(QueryCiFriendDto queryCiFriendDto) {
        return relationRuleAnalysisSvc.countStatistics(queryCiFriendDto.getRelationRule(), queryCiFriendDto.getSimpleFriendInfoMap(), queryCiFriendDto.getChart());
    }
}
