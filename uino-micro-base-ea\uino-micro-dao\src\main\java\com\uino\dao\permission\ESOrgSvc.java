package com.uino.dao.permission;

import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import jakarta.annotation.PostConstruct;

import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import com.uino.dao.AbstractESBaseDao;
import com.uino.dao.ESConst;
import com.uino.dao.permission.rlt.ESPerssionCommSvc;
import com.uino.util.sys.CommonFileUtil;
import com.uino.bean.permission.base.SysOrg;
import com.uino.bean.permission.base.SysOrgRoleRlt;
import com.uino.bean.permission.base.SysUserOrgRlt;
import com.uino.bean.permission.query.CSysOrg;

/**
 * <b>组织服务
 * 
 * <AUTHOR>
 */
@Service
public class ESOrgSvc extends AbstractESBaseDao<SysOrg, CSysOrg> {

	@Override
	public String getIndex() {
		return ESConst.INDEX_SYS_ORG;
	}

	@Override
	public String getType() {
		return ESConst.INDEX_SYS_ORG;
	}

	@PostConstruct
	public void init() {
		List<SysOrg> datas = CommonFileUtil.getData("/initdata/uino_sys_org.json", SysOrg.class);
		super.initIndex(datas);
	}

	@Autowired
    ESPerssionCommSvc commSvc;

	/**
	 * 获取用户所在的组织
	 * 
	 * @param userId
	 *            用户ID
	 * @return
	 */
	public List<SysOrg> getListByUserId(Long userId) {
        List<SysUserOrgRlt> rlts = commSvc.getUserOrgRltByUserIds(Collections.singleton(userId));
		Set<Long> ids = new HashSet<Long>();
		for (SysUserOrgRlt rlt : rlts) {
			ids.add(rlt.getOrgId());
		}
		return super.getListByQuery(1, 1000, QueryBuilders.termsQuery("id", ids)).getData();
	}

	/**
	 * 获取角色对应的组织
	 * 
	 * @param roleId
	 *            角色ID
	 * @return
	 */
	public List<SysOrg> getListByRoleId(Long roleId) {
        List<SysOrgRoleRlt> rlts = commSvc.getOrgRoleRltByRoleIds(Collections.singleton(roleId));
		Set<Long> ids = new HashSet<Long>();
		for (SysOrgRoleRlt rlt : rlts) {
			ids.add(rlt.getOrgId());
		}
		return super.getListByQuery(1, 1000, QueryBuilders.termsQuery("id", ids)).getData();
	}

	/**
	 * 获取在指定组织下的用户
	 * 
	 * @param orgId
	 * @return
	 */
	public Set<Long> getInOrgUserIds(Long orgId) {
		Set<Long> userIds = new HashSet<>();
        List<SysUserOrgRlt> results = commSvc.getUserOrgRltByOrgIds(Collections.singleton(orgId));
        results.forEach(mappingObj -> {
			userIds.add(mappingObj.getUserId());
		});
		return userIds;
	}

	/**
	 * 获取指定组织绑定的角色
	 * 
	 * @param orgId
	 * @return
	 */
	public Set<Long> getInOrgRoleIds(Long orgId) {
		Set<Long> roleIds = new HashSet<>();
        List<SysOrgRoleRlt> results = commSvc.getOrgRoleRltByOrgIds(Collections.singleton(orgId));
        results.forEach(mappingObj -> {
			roleIds.add(mappingObj.getRoleId());
		});
		return roleIds;
	}

	public Map<Long, String> getOrgIdAllNameMap(Long domainId, Collection<Long> orgIds) {
		Assert.notEmpty(orgIds, "param not empty");
		Map<Long, String> res = new HashMap<>();
		Map<Long, SysOrg> orgMap = new HashMap<>();
		List<SysOrg> orgs = this.getListByQuery(1, 99999, QueryBuilders.termQuery("domainId", domainId)).getData();
		orgs.forEach(org -> orgMap.put(org.getId(), org));
		orgIds.forEach(orgId -> {
			res.put(orgId, SysOrg.getAllName(orgId, orgMap));
		});
		return res;
	}
}
