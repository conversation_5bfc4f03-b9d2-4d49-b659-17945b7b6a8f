package com.uinnova.product.eam.model.dto;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.comm.model.es.AssetWarehouseDir;
import com.uinnova.product.eam.comm.model.es.EamVersionTag;
import com.uinnova.product.eam.comm.model.es.FolderPermissionManager;
import com.uinnova.product.eam.model.enums.AssetType;
import lombok.Data;
import org.springframework.data.annotation.Transient;

import java.io.Serializable;
import java.util.List;

/**
 * 目录&文件夹信息返回体
 * <AUTHOR>
 */
@Data
public class EamCategoryDTO implements Serializable {

    @Comment("主键")
    private Long id;
    @Comment("版本id")
    private Long tagId;
    @Comment("视图版本id")
    private Long versionId;
    @Comment("目录名称")
    private String dirName;
    @Comment("领域id")
    private Long domainId;
    @Comment("目录绑定的ciCode,模型目录及系统目录有值,普通目录为空")
    private String ciCode;
    @Comment("父级目录id")
    private Long parentId;
    @Comment("目录层级")
    private Integer dirLvl;
    @Comment("目录层级路径:例：#1#2#7#")
    private String dirPath;
    @Comment("目录绑定的流程视图id")
    private String diagramId;
    @Comment("模型树Id")
    private Long modelId;
    @Comment("顶级目录=1、普通文件夹=2、系统文件夹=3、模型树=4、模型文件夹=5、分类文件夹=6(原使用sysType、sysDir字段的判断逻辑使用该字段调整)")
    private Integer type;
    @Comment("创建人")
    private String creator;
    @Comment("创建时间")
    private Long createTime;
    @Comment("修改人")
    private String modifier;
    @Comment("修改时间")
    private Long modifyTime;
    @Comment("是否历史版本")
    private Boolean historyFlag = false;
    @Comment("历史版本标签集合")
    @Transient
    private List<EamVersionTag> historyList;
    @Comment("权限")
    private FolderPermissionManager folderPermissionManager;
    @Comment("Label")
    private String label;
    @Comment("序号")
    private String sortNum;
    @Comment("用作前端区分资源类型")
    private AssetType assetType;
    @Comment("映射菜单")
    private AssetWarehouseDir sysModulel;
    @Comment("是否被关注：0未关注 1关注")
    private Integer isAttention = 0;
    @Comment("数据模型标识")
    private boolean dataModelFlag = false;
}
