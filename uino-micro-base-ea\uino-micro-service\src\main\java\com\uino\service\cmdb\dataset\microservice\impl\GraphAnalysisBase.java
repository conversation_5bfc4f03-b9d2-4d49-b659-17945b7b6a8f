package com.uino.service.cmdb.dataset.microservice.impl;

import com.alibaba.fastjson.JSONArray;
import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClass;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uinnova.product.vmdb.provider.search.bean.CcCiObj;
import com.uinnova.product.vmdb.provider.search.bean.CcCiSearchPage;
import com.uino.service.cmdb.dataset.microservice.IGraphAnalysisBase;
import com.uino.service.cmdb.microservice.ICIClassSvc;
import com.uino.service.cmdb.microservice.ICIRltSvc;
import com.uino.service.cmdb.microservice.ICISvc;
import com.uino.bean.cmdb.base.ESCIRltInfo;
import com.uino.bean.cmdb.business.dataset.FriendInfo;
import com.uino.bean.cmdb.business.dataset.UpDownAttrCdt;
import com.uino.bean.cmdb.query.ESAttrBean;
import com.uino.bean.cmdb.query.ESCISearchBean;
import com.uino.bean.cmdb.query.ESRltSearchBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
@Slf4j
@RefreshScope
public class GraphAnalysisBase implements IGraphAnalysisBase {

    @Value("${selfProtection.ci.num:5000}")
    private int selfProtectionCiNum = 5000;

    @Autowired
    private ICIClassSvc iciClassSvc;

    private interface NextFinder {

        Long nextCiId(ESCIRltInfo esRltInfo);

        void setCiIds(ESRltSearchBean bean, List<Long> ciIds);

    }

    private class UpFinder implements NextFinder {
        @Override
        public Long nextCiId(ESCIRltInfo esRltInfo) {
            return esRltInfo.getSourceCiId();
        }

        @Override
        public void setCiIds(ESRltSearchBean bean, List<Long> ciIds) {
            bean.setTargetCiIds(ciIds);
        }
    }

    private class DownFinder implements NextFinder {
        @Override
        public Long nextCiId(ESCIRltInfo esRltInfo) {
            return esRltInfo.getTargetCiId();
        }

        @Override
        public void setCiIds(ESRltSearchBean bean, List<Long> ciIds) {
            bean.setSourceCiIds(ciIds);
        }
    }

    private class Condition {
        private Set<Long> classIds;
        private Map<Long, List<ESAttrBean>> onlyOrCdts;
        private List<UpDownAttrCdt> andOrCdts;

        public Set<Long> getClassIds() {
            return classIds;
        }

        public void setClassIds(Set<Long> classIds) {
            this.classIds = classIds;
        }

        public Map<Long, List<ESAttrBean>> getOnlyOrCdts() {
            return onlyOrCdts;
        }

        public void setOnlyOrCdts(Map<Long, List<ESAttrBean>> onlyOrCdts) {
            this.onlyOrCdts = onlyOrCdts;
        }

        public List<UpDownAttrCdt> getAndOrCdts() {
            return andOrCdts;
        }

        public void setAndOrCdts(List<UpDownAttrCdt> andOrCdts) {
            this.andOrCdts = andOrCdts;
        }
    }

    @Override
    public FriendInfo queryCiUpDownByCiIds(Long domainId, List<Long> startCiIds, List<UpDownAttrCdt> ciConditions,
                                           List<UpDownAttrCdt> rltConditions, List<Long> rltLvls, Integer upLevel, Integer downLevel,
                                           Boolean hasAttr, ICISvc iciSvc, ICIRltSvc iciRltSvc) {
        FriendInfo ret = new FriendInfo();
        if (domainId == null) {
            domainId = 1L;
        }
        if (upLevel == null) {
            upLevel = 0;
        }
        if (downLevel == null) {
            downLevel = 0;
        }
        if (hasAttr == null) {
            hasAttr = false;
        }

        if (startCiIds != null && startCiIds.size() > 0) {
            Condition ciCdt = resolveConditions(ciConditions);
            Condition rltCdt = resolveConditions(rltConditions);
            Map<Long, CcCiInfo> ciInfoMap = new HashMap<>();
            Map<Long, ESCIRltInfo> esRltInfoMap = new HashMap<>();
            if (traversal(domainId, startCiIds, ciCdt, rltCdt, rltLvls, upLevel, new UpFinder(), ciInfoMap, esRltInfoMap, iciSvc, iciRltSvc)) {
                traversal(domainId, startCiIds, ciCdt, rltCdt, rltLvls, downLevel, new DownFinder(), ciInfoMap, esRltInfoMap, iciSvc, iciRltSvc);
            }
            ESCISearchBean bean = initCiSearchBean(domainId, startCiIds);
            for (CcCiInfo ciInfo : queryCi(bean, iciSvc)) {
                ciInfoMap.put(ciInfo.getCi().getId(), ciInfo);
            }
            CCcCiClass cdt = new CCcCiClass();
            cdt.setDomainId(domainId);
            Map<Long, CcCiClassInfo> classInfoMap = new HashMap<Long, CcCiClassInfo>();
            for (CcCiClassInfo classInfo : iciClassSvc.queryCiClassInfoList(domainId, cdt, null, true)) {
                classInfoMap.put(classInfo.getCiClass().getId(), classInfo);
            }

            List<CcCiInfo> ciNodes = new ArrayList<>();
            for (Long ciId : ciInfoMap.keySet()) {
                CcCiInfo ciInfo = ciInfoMap.get(ciId);
                JSONArray labelJson = new JSONArray();
                if (classInfoMap.containsKey(ciInfo.getCi().getClassId())) {
                    for (CcCiAttrDef attrDef : classInfoMap.get(ciInfo.getCi().getClassId()).getAttrDefs()) {
                        if (attrDef.getIsCiDisp() > 0) {
                            labelJson.add(ciInfo.getAttrs().get(attrDef.getProStdName()));
                        }
                    }
                }
                ciInfo.getCi().setCiLabel(labelJson.toString());
                if (!hasAttr) {
                    ciInfo.setAttrs(null);
                }
                ciNodes.add(ciInfo);
            }
            List<ESCIRltInfo> ciRltLines = new ArrayList<>();
            for (Long rltId : esRltInfoMap.keySet()) {
                ESCIRltInfo rltInfo = esRltInfoMap.get(rltId);
                if (!hasAttr) {
                    rltInfo.setAttrs(null);
                }
                ciRltLines.add(rltInfo);
            }
            ret.setCiNodes(ciNodes);
            ret.setCiRltLines(ciRltLines);
        }
        return ret;
    }

    @Override
    public FriendInfo queryCiUpDownByCiId(Long domainId, Long startCiId, List<UpDownAttrCdt> ciConditions,
                                          List<UpDownAttrCdt> rltConditions, List<Long> rltLvls, Integer upLevel, Integer downLevel,
                                          Boolean hasAttr, ICISvc iciSvc, ICIRltSvc iciRltSvc) {
        List<Long> startCiIds = new ArrayList<Long>();
        startCiIds.add(startCiId);
        return queryCiUpDownByCiIds(domainId, startCiIds, ciConditions, rltConditions, rltLvls, upLevel, downLevel,
                hasAttr, iciSvc, iciRltSvc);
    }

    private Condition resolveConditions(List<UpDownAttrCdt> conditions) {
        Set<Long> classIds = new HashSet<>();
        Map<Long, List<ESAttrBean>> onlyOrCdts = new HashMap<>();
        List<UpDownAttrCdt> andOrCdts = new ArrayList<>();
        if (conditions != null) {
            for (UpDownAttrCdt condition : conditions) {
                if (condition.getClassId() != null) {
                    if ((condition.getAndAttrs() == null || condition.getAndAttrs().size() == 0) &&
                            (condition.getOrAttrs() == null || condition.getOrAttrs().size() == 0)) {
                        classIds.add(condition.getClassId());
                    } else if ((condition.getAndAttrs() == null || condition.getAndAttrs().size() == 0) &&
                            condition.getOrAttrs() != null && condition.getOrAttrs().size() > 0) {
                        List<ESAttrBean> eSAttrBeans = onlyOrCdts.computeIfAbsent(condition.getClassId(), k -> new ArrayList<>());
                        eSAttrBeans.addAll(condition.getOrAttrs());
                    } else {
                        andOrCdts.add(condition);
                    }
                }
            }
        }
        Condition condition = new Condition();
        condition.setClassIds(classIds);
        condition.setOnlyOrCdts(onlyOrCdts);
        condition.setAndOrCdts(andOrCdts);
        return condition;
    }

    private boolean traversal(Long domainId, List<Long> startCiIds, Condition ciCdt, Condition rltCdt,
                              List<Long> rltLvls, Integer level, NextFinder nextFinder, Map<Long, CcCiInfo> ciInfoMap, Map<Long, ESCIRltInfo> esRltInfoMap, ICISvc iciSvc, ICIRltSvc iciRltSvc) {

        Set<Long> ciIdSet = new HashSet<>(startCiIds);
        for (int i = 0; i < level; i++) {
            Set<Long> newCiIdSet = new HashSet<>();

            List<ESCIRltInfo> esRltInfoList = queryRlt(domainId, ciIdSet, rltCdt, rltLvls, nextFinder, iciRltSvc);
            Map<Long, List<ESCIRltInfo>> nextCiIdMap = new HashMap<>();
            for (ESCIRltInfo esRltInfo : esRltInfoList) {
                Long nextCiId = nextFinder.nextCiId(esRltInfo);
                List<ESCIRltInfo> rltInfoList = nextCiIdMap.computeIfAbsent(nextCiId, k -> new ArrayList<>());
                rltInfoList.add(esRltInfo);
            }

            List<CcCiInfo> ciInfoList = queryCi(domainId, nextCiIdMap.keySet(), ciCdt, iciSvc);
            for (CcCiInfo ciInfo : ciInfoList) {
                newCiIdSet.add(ciInfo.getCi().getId());
                ciInfoMap.put(ciInfo.getCi().getId(), ciInfo);
                for (ESCIRltInfo esRltInfo : nextCiIdMap.get(ciInfo.getCi().getId())) {
                    esRltInfoMap.put(esRltInfo.getId(), esRltInfo);
                }
            }

            if (newCiIdSet.size() > 0 && ciInfoMap.size() < selfProtectionCiNum) {
                ciIdSet = newCiIdSet;
            } else {
                break;
            }
        }
        return ciInfoMap.size() < selfProtectionCiNum;
    }

    private List<ESCIRltInfo> queryRlt(Long domainId, Set<Long> ciIdSet, Condition rltCondition, List<Long> rltLvls,
                                       NextFinder nextFinder, ICIRltSvc iciRltSvc) {
        List<ESCIRltInfo> ret = new ArrayList<>();

        List<Long> ciIdList = new ArrayList<>();
        for (Long ciId : ciIdSet) {
            ciIdList.add(ciId);
            if (ciIdList.size() == 200) {
                ret.addAll(queryRlt(domainId, ciIdList, rltCondition, rltLvls, nextFinder, iciRltSvc));
                ciIdList = new ArrayList<>();
            }
        }
        if (ciIdList.size() > 0) {
            ret.addAll(queryRlt(domainId, ciIdList, rltCondition, rltLvls, nextFinder, iciRltSvc));
        }

        return ret;
    }

    private List<ESCIRltInfo> queryRlt(Long domainId, List<Long> ciIdList, Condition condition, List<Long> rltLvls,
                                       NextFinder nextFinder, ICIRltSvc iciRltSvc) {
        List<ESCIRltInfo> ret = new ArrayList<>();

        if (condition.getClassIds().size() == 0 && condition.getOnlyOrCdts().size() == 0 && condition.getAndOrCdts().size() == 0) {
            ESRltSearchBean bean = initRltSearchBean(domainId, ciIdList, rltLvls, nextFinder);
            ret.addAll(queryRlt(bean, iciRltSvc));
        } else {
            if (condition.getClassIds().size() > 0) {
                ESRltSearchBean bean = initRltSearchBean(domainId, ciIdList, rltLvls, nextFinder);
                List<Long> rltClassIdList = new ArrayList<>(condition.getClassIds());
                bean.setRltClassIds(rltClassIdList);
                ret.addAll(queryRlt(bean, iciRltSvc));
            }
            for (Long rltClassId : condition.getOnlyOrCdts().keySet()) {
                ESRltSearchBean bean = initRltSearchBean(domainId, ciIdList, rltLvls, nextFinder);
                List<Long> rltClassIdList = new ArrayList<Long>();
                rltClassIdList.add(rltClassId);
                bean.setRltClassIds(rltClassIdList);
                bean.setOrAttrs(condition.getOnlyOrCdts().get(rltClassId));
                ret.addAll(queryRlt(bean, iciRltSvc));
            }
            for (UpDownAttrCdt cdt : condition.getAndOrCdts()) {
                ESRltSearchBean bean = initRltSearchBean(domainId, ciIdList, rltLvls, nextFinder);
                List<Long> rltClassIdList = new ArrayList<Long>();
                rltClassIdList.add(cdt.getClassId());
                bean.setRltClassIds(rltClassIdList);
                bean.setAndAttrs(cdt.getAndAttrs());
                bean.setOrAttrs(cdt.getOrAttrs());
                ret.addAll(queryRlt(bean, iciRltSvc));
            }
        }

        return ret;
    }

    private ESRltSearchBean initRltSearchBean(Long domainId, List<Long> ciIdList, List<Long> rltLvls, NextFinder nextFinder) {
        ESRltSearchBean bean = new ESRltSearchBean();
        bean.setDomainId(domainId);
        nextFinder.setCiIds(bean, ciIdList);
        bean.setCiRltLvl(rltLvls == null ? new ArrayList<>() : rltLvls);
        return bean;
    }

    private List<ESCIRltInfo> queryRlt(ESRltSearchBean bean, ICIRltSvc iciRltSvc) {
        List<ESCIRltInfo> ret = new ArrayList<ESCIRltInfo>();
        bean.setPageSize(1000);
        int pageNum = 1;
        while (true) {
            bean.setPageNum(pageNum);
            Page<ESCIRltInfo> page = iciRltSvc.searchRlt(bean);
            ret.addAll(page.getData());
            if (pageNum >= page.getTotalPages()) {
                break;
            } else {
                pageNum++;
            }
        }
        return ret;
    }

    /**
     * 根据条件过滤ci
     *
     * @param domainId
     * @param ciIdSet
     * @param ciCondition
     * @param iciSvc
     * @return
     */
    private List<CcCiInfo> queryCi(Long domainId, Set<Long> ciIdSet, Condition ciCondition, ICISvc iciSvc) {
        List<CcCiInfo> ret = new ArrayList<>();
        List<Long> ciIdList = new ArrayList<>();
        for (Long ciId : ciIdSet) {
            ciIdList.add(ciId);
            if (ciIdList.size() == 200) {
                ret.addAll(queryCi(domainId, ciIdList, ciCondition, iciSvc));
                ciIdList = new ArrayList<>();
            }
        }
        if (ciIdList.size() > 0) {
            ret.addAll(queryCi(domainId, ciIdList, ciCondition, iciSvc));
        }
        return ret;
    }

    private List<CcCiInfo> queryCi(Long domainId, List<Long> ciIds, Condition ciCondition, ICISvc iciSvc) {
        List<CcCiInfo> ret = new ArrayList<CcCiInfo>();

        if (ciCondition.getClassIds().size() == 0 && ciCondition.getOnlyOrCdts().size() == 0 && ciCondition.getAndOrCdts().size() == 0) {
            ESCISearchBean bean = initCiSearchBean(domainId, ciIds);
            ret.addAll(queryCi(bean, iciSvc));
        } else {
            if (ciCondition.getClassIds().size() > 0) {
                ESCISearchBean bean = initCiSearchBean(domainId, ciIds);
                List<Long> ciClassIdList = new ArrayList<Long>();
                ciClassIdList.addAll(ciCondition.getClassIds());
                bean.setClassIds(ciClassIdList);
                ret.addAll(queryCi(bean, iciSvc));
            }
            for (Long ciClassId : ciCondition.getOnlyOrCdts().keySet()) {
                ESCISearchBean bean = initCiSearchBean(domainId, ciIds);
                List<Long> ciClassIdList = new ArrayList<Long>();
                ciClassIdList.add(ciClassId);
                bean.setClassIds(ciClassIdList);
                bean.setOrAttrs(ciCondition.getOnlyOrCdts().get(ciClassId));
                ret.addAll(queryCi(bean, iciSvc));
            }
            for (UpDownAttrCdt cdt : ciCondition.getAndOrCdts()) {
                ESCISearchBean bean = initCiSearchBean(domainId, ciIds);
                List<Long> ciClassIdList = new ArrayList<Long>();
                ciClassIdList.add(cdt.getClassId());
                bean.setClassIds(ciClassIdList);
                bean.setAndAttrs(cdt.getAndAttrs());
                bean.setOrAttrs(cdt.getOrAttrs());
                ret.addAll(queryCi(bean, iciSvc));
            }
        }

        return ret;
    }

    private ESCISearchBean initCiSearchBean(Long domainId, List<Long> ciIds) {
        ESCISearchBean bean = new ESCISearchBean();
        bean.setDomainId(domainId);
        bean.setIds(ciIds);
        return bean;
    }

    private List<CcCiInfo> queryCi(ESCISearchBean bean, ICISvc iciSvc) {
        List<CcCiInfo> ret = new ArrayList<CcCiInfo>();
        bean.setPageSize(1000);
        int pageNum = 1;
        while (true) {
            bean.setPageNum(pageNum);
            CcCiSearchPage page = iciSvc.searchCIByBean(bean);
            for (CcCiObj ciObj : page.getData().getRecords()) {
                CcCiInfo ciInfo = new CcCiInfo();
                ciInfo.setCi(ciObj.getCi());
                ciInfo.setAttrs(ciObj.getAttrs());
                ret.add(ciInfo);
            }
            if (pageNum >= page.getTotalPages()) {
                break;
            } else {
                pageNum++;
            }
        }
        return ret;
    }
}
