package com.uinnova.product.eam.comm.model;




import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;
import lombok.Data;


@Comment("基础配置表[VC_BASE_CONFIG]")
@Data
public class VcBaseConfig implements EntityBean {
	private static final long serialVersionUID = 1L;


	@Comment("ID[ID]")
	private Long id;


	@Comment("配置代码[CFG_CODE]")
	private String cfgCode;


	@Comment("配置内容[CFG_CONTENT]")
	private String cfgContent;


	@Comment("所属域[DOMAIN_ID]")
	private Long domainId;


	@Comment("数据状态[DATA_STATUS]    数据状态:数据状态：1-正常 0-删除")
	private Integer dataStatus;

	@Comment("被选中的/有效的")
	private Boolean selected;

	@Comment("创建人[CREATOR]")
	private String creator;

	@Comment("修改人[MODIFIER]")
	private String modifier;

	@Comment("创建时间[CREATE_TIME]    创建时间:yyyyMMddHHmmss")
	private Long createTime;

	@Comment("修改时间[MODIFY_TIME]    修改时间:yyyyMMddHHmmss")
	private Long modifyTime;
}


