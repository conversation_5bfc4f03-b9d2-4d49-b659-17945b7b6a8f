package com.uinnova.product.eam.web.asset.mvc;

import com.alibaba.fastjson.JSONObject;
import com.binary.core.util.BinaryUtils;
import com.binary.framework.web.RemoteResult;
import com.binary.jdbc.Page;
import com.uinnova.product.eam.model.cj.dto.PlanDesignInstanceDTO;
import com.uinnova.product.eam.model.VcCiClassInfoDto;
import com.uinnova.product.eam.model.asset.AppSystemInfoResponse;
import com.uinnova.product.eam.model.dto.EamAppConfigDto;
import com.uinnova.product.eam.model.vo.*;
import com.uinnova.product.eam.service.asset.AppSystemDetailSvc;
import com.uinnova.product.eam.web.asset.bean.HtAppSystemRltVo;
import com.uinnova.product.eam.web.asset.bean.HtAppSystemTaskVo;
import com.uinnova.product.eam.web.asset.peer.AppSystemPeer;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uino.bean.cmdb.base.ESCIAttrDefInfo;
import com.uino.bean.cmdb.base.ESCIClassInfo;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.base.LibType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 应用系统管理相关接口
 */
@RestController
@RequestMapping("/asset/appSystem")
public class AppSystemMvc {

    @Resource
    AppSystemPeer appSystemPeer;
    @Resource
    private AppSystemDetailSvc appSystemDetailSvc;

    /**
     * 分页查询应用系统列表
     *
     * @param queryVo
     * @return
     */
    @PostMapping("/queryAppSystemList")
    public RemoteResult queryAppSystemList(@RequestBody AppSystemQueryVo queryVo) {
        Page<ESCIInfo> htAppSystemVoPage = appSystemPeer.queryAppSystemList(queryVo);
        return new RemoteResult(htAppSystemVoPage);
    }

    @PostMapping("/queryAppSystemListNew")
    public RemoteResult queryAppSystemListNew(@RequestParam(defaultValue = "DESIGN") LibType libType, @RequestBody AppSystemQueryVo queryVo) {
        AppSysQueryVo htAppSystemVo = appSystemPeer.queryAppSystemListNew(queryVo, libType);
        return new RemoteResult(htAppSystemVo);
    }

    @GetMapping("/queryAppSystemInfo")
    @ModDesc(desc = "查询系统详情", pDesc = "系统ciCode", rDesc = "系统信息", rType = AppSystemInfoResponse.class)
    public RemoteResult queryAppSystemInfo(@RequestParam String ciCode) {
        BinaryUtils.checkEmpty(ciCode, "应用系统code");
        AppSystemInfoResponse result = appSystemDetailSvc.queryAppSystemInfo(ciCode);
        return new RemoteResult(result);
    }

    /**
     * 获取应用系统的对象定义
     *
     * @return
     */
    @RequestMapping("/getAppSystemClassInfo")
    public RemoteResult getAppSystemClassInfo() {
        ESCIClassInfo appSystemClassInfo = appSystemPeer.getAppSystemClassInfo();
        return new RemoteResult(appSystemClassInfo);
    }

    /**
     * 获取应用系统卡片配置
     *
     * @return
     */
    @RequestMapping("/getCardConfiguration")
    public RemoteResult getCardConfiguration() {
        String cardConfiguration = appSystemPeer.getCardConfiguration();
        return new RemoteResult(cardConfiguration);
    }

    /**
     * 保存应用系统卡片配置
     *
     * @return
     */
    @RequestMapping("/saveCardConfiguration")
    public RemoteResult saveCardConfiguration(@RequestBody String body) {
        JSONObject jsonObject = JSONObject.parseObject(body);
        BinaryUtils.checkEmpty(jsonObject.getString("data"), "data");
        String data = jsonObject.getString("data");
        appSystemPeer.saveCardConfiguration(data);
        return new RemoteResult(true);
    }

    /**
     * 上下文关系图获取中心分类信息
     *
     * @param htAppSystemTaskVo
     * @return
     */
    @RequestMapping("/queryAppSystemClassInfo")
    public RemoteResult queryAppSystemClassInfo(@RequestBody HtAppSystemTaskVo htAppSystemTaskVo) {
        VcCiClassInfoDto result = appSystemPeer.queryAppSystemClassInfo(htAppSystemTaskVo.getClassId(), htAppSystemTaskVo.getLike());
        return new RemoteResult(result);
    }

    /**
     * 查询上下游信息
     *
     * @param htAppSystemTaskVo
     * @return
     */
    @PostMapping("/queryUpAndDownRlt")
    public RemoteResult queryUpAndDownRlt(@RequestBody HtAppSystemTaskVo htAppSystemTaskVo) {
        Page<HtAppSystemRltVo> data = appSystemPeer.queryUpAndDownRlt(htAppSystemTaskVo.getCiCode(),
                htAppSystemTaskVo.getPageNum(),
                htAppSystemTaskVo.getPageSize());
        return new RemoteResult(data);
    }

    /**
     * 获取应用系统列表模式左侧筛选条件
     *
     * @return
     */
    @GetMapping("/getQueryConditions")
    public RemoteResult getQueryConditions() {
        List<AppSystemQueryConVo> conditions = appSystemPeer.getQueryConditions();
        return new RemoteResult(conditions);
    }

    /**
     * 获取应用架构全景显示配置
     *
     * @return
     */
    @GetMapping("/getAppArchWallConfig")
    public RemoteResult getAppArchWallConfig() {
        AppArchWallConfigVo archWallConfigVo = appSystemPeer.getAppArchWallConfig();
        return new RemoteResult(archWallConfigVo);
    }

    @GetMapping("/getSystemArchitectureDiagram")
    @ModDesc(desc = "查询系统相关架构视图", pDesc = "系统ciCode", rDesc = "视图加密id", rType = List.class)
    public RemoteResult getSystemArchitectureDiagram(@RequestParam String ciCode) {
        BinaryUtils.checkEmpty(ciCode, "应用系统code");
        List<Map<String, String>> result = appSystemDetailSvc.getSystemArchitectureDiagram(ciCode);
        return new RemoteResult(result);
    }

    /**
     * 查询应用系统关联资产
     *
     * @return
     */
    @GetMapping("/getSystemAssets")
    public RemoteResult getSystemAssets(@RequestParam String ciCode) {
        BinaryUtils.checkEmpty(ciCode, "应用系统code");
        List<AppSystemAssetsVo> systemAssetsVos = appSystemPeer.getSystemAssets(ciCode);
        return new RemoteResult(systemAssetsVos);
    }

    @GetMapping("/plan")
    @ModDesc(desc = "查询应用系统关联架构方案", pDesc = "系统ciCode", rDesc = "方案", rType = List.class)
    public RemoteResult getSystemPlan(@RequestParam(defaultValue = "1") Integer pageNum, @RequestParam(defaultValue = "20") Integer pageSize, @RequestParam String ciCode) {
        BinaryUtils.checkEmpty(ciCode, "应用系统code");
        Page<PlanDesignInstanceDTO> result = appSystemPeer.getSystemPlan(pageNum, pageSize, ciCode);
        return new RemoteResult(result);
    }

    /**
     * 保存筛选配置
     *
     * @param appSysConfig 保存参数
     * @return 返回值
     */
    @PostMapping("/saveFilterConfig")
    public RemoteResult saveFilterConfig(@RequestBody EamAppConfigDto dto) {
        Long id = appSystemPeer.saveFilterConfig(dto);
        return new RemoteResult(id);
    }

    /**
     * 查询筛选配置接口
     *
     * @param id 配置id
     * @return
     */
    @GetMapping("/queryFilterConfig")
    public RemoteResult queryFilterConfig(@RequestParam String classCode, @RequestParam Long cardId) {
        EamAppConfigVo vo = appSystemPeer.queryFilterConfig(classCode, cardId);
        return new RemoteResult(vo);
    }

    /**
     * 查询分类属性及属性的枚举值等
     *
     * @param classCode 分类classCode
     * @return 返回值
     */
    @GetMapping("/getDefInfoByClassCode")
    public RemoteResult getDefInfoByClassCode(@RequestParam String classCode) {
        List<ESCIAttrDefInfo> attrList = appSystemPeer.getDefInfoByClassCode(classCode);
        return new RemoteResult(attrList);
    }

    @PostMapping("exportBySearch")
    public ResponseEntity<byte[]> exportBySearch(@RequestParam(defaultValue = "DESIGN") LibType libType, @RequestBody AppSystemQueryVo queryVo) {
        return appSystemPeer.exportBySearch(queryVo, libType);
    }



}
