package com.uinnova.product.vmdb.comm.doc.api;

import java.io.Serializable;

/**
 * 文档基本描述
 * 
 * <AUTHOR>
 *
 */
public class ApiDesc implements Serializable {

    private static final long serialVersionUID = 1L;

    private String projectName;

    private String projectHttpRoot;

    private String apiDoc;

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getProjectHttpRoot() {
        return projectHttpRoot;
    }

    public void setProjectHttpRoot(String projectHttpRoot) {
        this.projectHttpRoot = projectHttpRoot;
    }

    public String getApiDoc() {
        return apiDoc;
    }

    public void setApiDoc(String apiDoc) {
        this.apiDoc = apiDoc;
    }

}
