package com.uino.service.cmdb.dataset.microservice.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.handler.context.CellWriteHandlerContext;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.binary.core.exception.MessageException;
import com.binary.core.io.Compression;
import com.binary.core.io.Resource;
import com.binary.core.io.support.FileResource;
import com.binary.core.util.BinaryUtils;
import com.binary.framework.exception.ServiceException;
import com.binary.jdbc.Page;
import com.google.common.collect.Lists;
import com.uinnova.product.vmdb.comm.model.ci.CCcCi;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClass;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.comm.model.ci.CcCiClass;
import com.uinnova.product.vmdb.comm.util.CommUtil;
import com.uinnova.product.vmdb.comm.util.PropertyType;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uino.bean.cmdb.base.ESCIAttrTransConfig;
import com.uino.bean.cmdb.base.ESCIRltInfo;
import com.uino.bean.cmdb.base.LibType;
import com.uino.bean.cmdb.base.dataset.*;
import com.uino.bean.cmdb.base.dataset.batch.DataSetExeResult;
import com.uino.bean.cmdb.base.dataset.batch.DataSetExeResultSheet;
import com.uino.bean.cmdb.base.dataset.batch.DataSetTableResult;
import com.uino.bean.cmdb.base.dataset.batch.SimpleFriendInfo;
import com.uino.bean.cmdb.base.dataset.chart.Chart;
import com.uino.bean.cmdb.base.dataset.log.DataSetMallApiLog;
import com.uino.bean.cmdb.base.dataset.relation.RelationRuleLine;
import com.uino.bean.cmdb.base.dataset.relation.RelationRuleNode;
import com.uino.bean.cmdb.base.dataset.style.DataSetPriorityDisplay;
import com.uino.bean.cmdb.base.dataset.style.DataSetTop;
import com.uino.bean.cmdb.base.dataset.style.DisplayType;
import com.uino.bean.cmdb.business.dataset.*;
import com.uino.bean.dataset.base.DataSetPathInfoVo;
import com.uino.bean.dataset.base.DataSetThumbnailDTO;
import com.uino.bean.permission.base.*;
import com.uino.bean.permission.business.UserInfo;
import com.uino.bean.permission.query.CSysUser;
import com.uino.bean.tp.base.TpRuleDTO;
import com.uino.bean.tp.base.TpRuleDTO.LabelMappingDTO;
import com.uino.bean.tp.enums.TpRuleTypeEnum;
import com.uino.bean.tp.query.MetricAttrValQueryDTO;
import com.uino.bean.tp.query.QueryDataTableDTO;
import com.uino.bean.tp.query.TpRuleReqDTO;
import com.uino.dao.BaseConst;
import com.uino.dao.cmdb.ESCIAttrTransConfigSvc;
import com.uino.dao.cmdb.dataset.*;
import com.uino.dao.util.ESUtil;
import com.uino.monitor.tp.metric.MetricDataSvc;
import com.uino.monitor.tp.metric.TpRuleSvc;
import com.uino.service.cmdb.dataset.microservice.*;
import com.uino.service.cmdb.microservice.ICIClassSvc;
import com.uino.service.cmdb.microservice.ICISvc;
import com.uino.service.cmdb.microservice.ITaskLockSvc;
import com.uino.service.permission.microservice.impl.UserSvc;
import com.uino.service.util.FileUtil;
import com.uino.util.exception.LoginException;
import com.uino.util.sys.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFCell;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.elasticsearch.index.query.*;

import org.elasticsearch.search.sort.SortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * @Classname DataSetMallApi
 * @Description TODO
 * @Date 2020/3/20 10:22
 * @Created by sh
 */
@Service
@Slf4j
public class DataSetSvc implements IDataSetSvc {

    //表格查询支持类型
    private static final List<Integer> _CONDITION_TYPE_LIST = Arrays.asList(PropertyType.ENUM.getValue(), PropertyType.INTEGER.getValue(), PropertyType.DOUBLE.getValue(), PropertyType.DATE.getValue());

    @Autowired
    private ESDataSetSvc esDataSetSvc;

    @Autowired
    private ESDataSetCoordinationSvc esDataSetCoordinationSvc;

    @Autowired
    private ESDataSetTopSvc esDataSetTopSvc;

    @Autowired
    private ESDataSetPriorityDisplaySvc esDataSetPriorityDisplaySvc;

    @Autowired
    private ESDataSetMallApiLogSvc esDataSetMallApiLogSvc;

    @Autowired
    private ESDataSetExeResultSvc esDataSetExeResultSvc;

    @Autowired
    private ESDataSetExeResultSheetSvc esDataSetExeResultSheetSvc;

    @Autowired
    private ICIClassSvc iciClassSvc;

    @Autowired
    private ICISvc iciSvc;

    @Autowired
    private IRelationRuleAnalysisSvc relationRuleAnalysisSvc;

    @Autowired
    private ITaskLockSvc taskLockSvc;

    @Autowired
    private IBatchProcessSvc batchProcessSvc;

    @Autowired
    private MetricDataSvc metricDataSvc;

    @Autowired
    private IDigitalTwinServicePortalBaseSvc digitalTwinServicePortalSvc;

    @Autowired
    private TpRuleSvc tpRuleSvc;

    @Autowired
    private UserSvc userSvc;

    @Autowired
    private ESCIAttrTransConfigSvc esciAttrTransConfigSvc;

    @Value("${http.resource.space}")
    private  String httpResourceUrl;

    @Value("${uino.cmdb.libtype:BASELINE}")
    private String libType;

    private final static String ROLE_ADMIN = "admin";

    private IMallApiSvc getMallApi(Integer type) {
        if (DataSetMallApiType.RelationRule.getCode().equals(type)) {
            return SpringUtil.getBean(MallApiRelationRuleSvc.class);
        } else if (DataSetMallApiType.CiClass.getCode().equals(type)) {
            return SpringUtil.getBean(MallApiClassSvc.class);
        } else if (DataSetMallApiType.RelClass.getCode().equals(type)) {
            return SpringUtil.getBean(MallApiRelSvc.class);
        } else if (DataSetMallApiType.UpDownNFloor.getCode().equals(type)) {
            return SpringUtil.getBean(MallApiUpDownNFloorSvc.class);
        } else if (DataSetMallApiType.Metrics.getCode().equals(type)) {
            return SpringUtil.getBean(MallApiMertricsSvc.class);
        } else if (DataSetMallApiType.Statistic.getCode().equals(type)) {
            return SpringUtil.getBean(MallApiStatisticSvc.class);
        } else {
            throw new RuntimeException("规则类型不存在");
        }
    }

    private DataSetMallApi getDataSetMallApi(JSONObject json) {
        int type = json.getIntValue("type");
        if (DataSetMallApiType.RelationRule.getCode().equals(type)) {
            return new DataSetMallApiRelationRule(json);
        } else if (DataSetMallApiType.CiClass.getCode().equals(type)) {
            return new DataSetMallApiCiClass(json);
        } else if (DataSetMallApiType.RelClass.getCode().equals(type)) {
            return new DataSetMallApiRelClass(json);
        } else if (DataSetMallApiType.UpDownNFloor.getCode().equals(type)) {
            return new DataSetMallApiUpDownNFloor(json);
        } else if (DataSetMallApiType.Metrics.getCode().equals(type)) {
            return new DataSetMallApiMetrics(json);
        } else if (DataSetMallApiType.Statistic.getCode().equals(type)) {
            return new DataSetMallApiStatistic(json);
        } else {
            throw new RuntimeException("规则类型不存在");
        }
    }

    @Override
    public Long saveOrUpdateDataSet(JSONObject json, Boolean ifExeBatchProcess) {
        Boolean needCheck = json.getBoolean("needCheck") == null || json.getBoolean("needCheck");
        Long id = json.getLong("id");
        needCheck = BinaryUtils.isEmpty(id) ? Boolean.TRUE : needCheck;
        return saveOrUpdateDataSetExtend(json, ifExeBatchProcess, needCheck);
    }

    public Long saveOrUpdateDataSetExtend(JSONObject json, Boolean ifExeBatchProcess, Boolean needCheck) {
        SysUser user = SysUtil.getCurrentUserInfo();
        int type = json.getIntValue("type");
        IMallApiSvc mallApi = getMallApi(type);
        DataSetMallApi dataSetMallApi;
        if (needCheck) {
            dataSetMallApi = mallApi.checkCharacteristic(user, json);
        } else {
            dataSetMallApi = new DataSetMallApiMetrics(json);
        }
        if (dataSetMallApi.getId() == null) {
            dataSetMallApi.setId(ESUtil.getUUID());
            dataSetMallApi.setShareLevel(OperateType.Invisible.getCode());
            dataSetMallApi.setCreater(user.getLoginCode());
            dataSetMallApi.setCreateTime(System.currentTimeMillis());
            dataSetMallApi.setModifyTime(System.currentTimeMillis());
        } else {
            //更新进行权限校验
            checkOperate(user.getLoginCode(), dataSetMallApi);
            dataSetMallApi.setModifier(user.getLoginCode());
            dataSetMallApi.setModifyTime(System.currentTimeMillis());
        }
        if(!BinaryUtils.isEmpty(dataSetMallApi.getThumbnail())){
            dataSetMallApi.setThumbnail(dataSetMallApi.getThumbnail().replace(httpResourceUrl, ""));
        }
        //判断重名
        List<JSONObject> datasetAll = esDataSetSvc.getListByQuery(QueryBuilders.termQuery("domainId", user.getDomainId()));
        if (datasetAll != null) {
            for (JSONObject jsonObject : datasetAll) {
                DataSetMallApi dataSet = getDataSetMallApi(jsonObject);
                if (dataSet.getName().equalsIgnoreCase(dataSetMallApi.getName()) && !dataSet.getId().equals(dataSetMallApi.getId())) {
                    //规则名称已存在
                    throw MessageException.i18n("DCV_BS_OBJ_RULE_EXISTS");
                }
                if(dataSet.getId().equals(dataSetMallApi.getId()) && BinaryUtils.isEmpty(dataSetMallApi.getThumbnail())){
                    dataSetMallApi.setThumbnail(dataSet.getThumbnail());
                }
            }
        }
        Long id = esDataSetSvc.saveOrUpdate(dataSetMallApi.toJson(), true);
        if (null != id) {
            digitalTwinServicePortalSvc.saveOrUpdate(dataSetMallApi, id);
        }
        // 如果是关系遍历类型的数据集，保存数据集后调用批处理接口将API的执行结果保存至ES中
        if (ifExeBatchProcess && dataSetMallApi.getType() == DataSetMallApiType.RelationRule.getCode()) {
            // 异步调用批处理的方法
            CompletableFuture.runAsync(() ->{
                LibTypeUtil.execute(() -> batchProcessSvc.processDataSetRule((DataSetMallApiRelationRule) dataSetMallApi), LibType.valueOf(libType));
            });
        }
        log.debug("#########################Return save/update result");
        return id;
    }


    @Override
    public boolean delete(Long id) {
        SysUser user = SysUtil.getCurrentUserInfo();
        JSONObject dataSet = esDataSetSvc.getById(id);
        if (ObjectUtils.isEmpty(dataSet)) {
            return true;
        }
        DataSetMallApi dataSetMallApi = getDataSetMallApi(dataSet);
        checkOperate(user.getLoginCode(), dataSetMallApi);
        Integer var1 = esDataSetSvc.deleteById(dataSetMallApi.getId());
        //删除协作
        BoolQueryBuilder deleteQuery = QueryBuilders.boolQuery();
        deleteQuery.must(QueryBuilders.termQuery("dataSetMallApiId.keyword", dataSetMallApi.getId()));
        Integer var2 = esDataSetCoordinationSvc.deleteByQuery(deleteQuery, true);
        //删除置顶
        Integer var3 = esDataSetTopSvc.deleteByQuery(deleteQuery, true);
        digitalTwinServicePortalSvc.delete(id);
        return var1 == 1 && var2 == 1 && var3 == 1;
    }

    @Override
    public List<JSONObject> findDataSet(String name, boolean isMyself, String url, List<DataSetMallApiType> typeList) {
        SysUser user = SysUtil.getCurrentUserInfo();
        Long domainId = user.getDomainId();
        Boolean isAdmin = isAdmin(user.getLoginCode());
        //根据权限表查询用户具有读写权限的规则
        BoolQueryBuilder coordinationQuery = QueryBuilders.boolQuery();
        coordinationQuery.must(QueryBuilders.termQuery("coordinationUserCode.keyword", user.getLoginCode()));
        coordinationQuery.must(QueryBuilders.termQuery("domainId", domainId));
        List<DataSetCoordination> coordinationList = esDataSetCoordinationSvc.getListByQuery(coordinationQuery);
        List<Long> coordinationDataSetIds = new ArrayList<>();
        if (coordinationList != null && !isAdmin) {
            coordinationList.forEach(cordination -> coordinationDataSetIds.add(cordination.getDataSetMallApiId()));
        }
        //查询所有规则
        BoolQueryBuilder dataSetMallApiQuery = QueryBuilders.boolQuery();
        dataSetMallApiQuery.must(QueryBuilders.termQuery("domainId", domainId));
        if (name != null && !"".equals(name)) {
            dataSetMallApiQuery.must(QueryBuilders.multiMatchQuery(name.trim(), "name").operator(Operator.AND).type(MultiMatchQueryBuilder.Type.PHRASE_PREFIX).lenient(true));
        }
        if (isMyself) {
            // 自己或自己有协作权限的
            BoolQueryBuilder coordiantionShould = QueryBuilders.boolQuery();
            coordiantionShould.must(QueryBuilders.termQuery("domainId", domainId));
            coordiantionShould.should(QueryBuilders.termsQuery("id.keyword", coordinationDataSetIds));
            coordiantionShould.should(QueryBuilders.termQuery("creater.keyword", user.getLoginCode()));
            dataSetMallApiQuery.must(coordiantionShould);
        }
        if (typeList != null && !typeList.isEmpty()) {
            List<Integer> types = new ArrayList<>();
            typeList.forEach(dataSetMallApiType -> types.add(dataSetMallApiType.getCode()));
            dataSetMallApiQuery.must(QueryBuilders.termsQuery("type", types));
        }

        SortBuilder<?> sort = SortBuilders.fieldSort("modifyTime").order(SortOrder.DESC);
        List<SortBuilder<?>> sorts = new ArrayList<>();
        sorts.add(sort);
        List<JSONObject> dataSetMallApiAll = esDataSetSvc.getSortListByQuery(dataSetMallApiQuery, sorts);
        //过滤规则并加操作等级（权限）字段（留下分享规则、自己创建的规则、有协作权限的规则）
        Set<Long> classIdList = new HashSet<>();
        Map<Long, JSONObject> legitimateDataSetMap = new HashMap<>();
        //默认顺序
        List<Long> dataSetOrderIds = new ArrayList<>();
        String realTimeUrl = url + "/cmdb/dataSet/realTimeExecute";
        String batchUrl = url + "/cmdb/dataSet/execute";
        for (JSONObject dataset : dataSetMallApiAll) {
            Long id = dataset.getLong("id");
            // 数据集API URL地址
            dataset.put("realTimeUrl", realTimeUrl);
            dataset.put("batchUrl", batchUrl);
            String thumbnail = dataset.getString("thumbnail");
            if(!BinaryUtils.isEmpty(thumbnail)){
                dataset.put("thumbnail", httpResourceUrl + thumbnail);
            }
            if (coordinationDataSetIds.contains(id) || user.getLoginCode().equals(dataset.getString("creater")) || isAdmin) {
                dataSetOrderIds.add(id);
                Integer type = dataset.getInteger("type");
                if (DataSetMallApiType.RelationRule.getCode().equals(type)) {
                    classIdList.add(dataset.getLongValue("classId"));
                }
                //操作等级
                dataset.put("permissionLevel", OperateType.Write.getCode());
                legitimateDataSetMap.put(id, dataset);
            } else if (dataset.getInteger("shareLevel") > OperateType.Invisible.getCode()) {
                dataSetOrderIds.add(id);
                Integer type = dataset.getInteger("type");
                if (DataSetMallApiType.RelationRule.getCode().equals(type)) {
                    classIdList.add(dataset.getLongValue("classId"));
                }
                dataset.put("permissionLevel", dataset.getInteger("shareLevel"));
                legitimateDataSetMap.put(id, dataset);
            }
        }

        //首选显示
        BoolQueryBuilder dataSetPriorityDisplayQuery = QueryBuilders.boolQuery();
        dataSetPriorityDisplayQuery.must(QueryBuilders.termQuery("domainId", domainId));
        dataSetPriorityDisplayQuery.must(QueryBuilders.termQuery("userCode.keyword", user.getLoginCode()));

        List<DataSetPriorityDisplay> priorityDisplayList = esDataSetPriorityDisplaySvc.getListByQuery(dataSetPriorityDisplayQuery);
        Map<Long, Integer> priorityDisplayMap = new HashMap<>();
        if (priorityDisplayList != null) {
            priorityDisplayList.forEach(priorityDisplay -> {
                priorityDisplayMap.put(priorityDisplay.getDataSetMallApiId(), priorityDisplay.getDisplay());
            });
        }
        CCcCiClass ciClassCdt = new CCcCiClass();
        ciClassCdt.setDomainId(domainId);
        ciClassCdt.setIds(classIdList.toArray(new Long[classIdList.size()]));
        List<CcCiClassInfo> classList = iciClassSvc.queryClassByCdt(ciClassCdt);
        //分类图标
        Map<Long, String> iconMap = new HashMap<>();
        classList.forEach(ccCiClass -> iconMap.put(ccCiClass.getCiClass().getId(), ccCiClass.getCiClass().getIcon()));

        //根据日志计算请求次数、和请求时长(此处计算一个月的)
        Map<Long, Map<String, Object>> statisticsLogMap = statisticsLog(dataSetOrderIds, 30 * 24 * 3600 * 1000L);
        //查询置顶
        BoolQueryBuilder dataSetTopQuery = QueryBuilders.boolQuery();
        dataSetTopQuery.must(QueryBuilders.termQuery("domainId", domainId));
        dataSetTopQuery.must(QueryBuilders.termQuery("userCode.keyword", user.getLoginCode()));
        SortBuilder<?> topSort = SortBuilders.fieldSort("createTime").order(SortOrder.DESC);
        List<SortBuilder<?>> topSorts = new ArrayList<SortBuilder<?>>();
        topSorts.add(topSort);
        List<DataSetTop> dataSetTopList = esDataSetTopSvc.getSortListByQuery(dataSetTopQuery, topSorts);

        //排序重组
        List<JSONObject> result = new ArrayList<>();
        //置顶排序
        if (dataSetTopList != null) {
            dataSetTopList.forEach(dataSetTop -> {
                Long dataSetMallApiId = dataSetTop.getDataSetMallApiId();
                dataSetOrderIds.remove(dataSetMallApiId);
                JSONObject dataset = legitimateDataSetMap.get(dataSetMallApiId);
                //置顶后也可能被人取消查看或操作权限，故而判断
                if (dataset != null) {
                    result.add(this.wapperDataSet(dataSetMallApiId, dataset, statisticsLogMap, true, priorityDisplayMap, iconMap));
                }
            });
        }
        //其他排序
        dataSetOrderIds.forEach(id ->
            result.add(this.wapperDataSet(id, legitimateDataSetMap.get(id), statisticsLogMap, false, priorityDisplayMap, iconMap)));

        fillUserInfo(result);
        return result;
    }

    /**
     *
     * @param dataSetMallApiId 数据集id
     * @param dataset 数据集
     * @param statisticsLogMap 数据集对应请求次数、请求时长（最近一个月）
     * @param isTop 是否置顶
     * @param priorityDisplayMap 优先显示
     * @param iconMap 分类图标
     * @return
     */
    private JSONObject wapperDataSet(Long dataSetMallApiId, JSONObject dataset, Map<Long, Map<String, Object>> statisticsLogMap,
                               Boolean isTop, Map<Long, Integer> priorityDisplayMap, Map<Long, String> iconMap) {
        dataset.putAll(statisticsLogMap.get(dataSetMallApiId));
        dataset.put("isTop", isTop);
        Integer type = dataset.getInteger("type");
        if (DataSetMallApiType.CiClass.getCode().equals(type)
                || DataSetMallApiType.RelClass.getCode().equals(type)
                || DataSetMallApiType.UpDownNFloor.getCode().equals(type)
                || DataSetMallApiType.Metrics.getCode().equals(type)) {
            dataset.put("priorityDisplay", DisplayType.Api.getCode());
        } else {
            dataset.put("priorityDisplay", priorityDisplayMap.get(dataSetMallApiId) == null ? 1 : priorityDisplayMap.get(dataSetMallApiId));
        }
        if (DataSetMallApiType.RelationRule.getCode().equals(type)) {
            dataset.put("icon", iconMap.get(dataset.getLongValue("classId")));
        }
        return dataset;
    }

    /**
     * 统计日志
     *
     * @param dataSetOrderIds 数据集ID
     * @param time            时长
     * @return 日志
     */
    private Map<Long, Map<String, Object>> statisticsLog(List<Long> dataSetOrderIds, Long time) {
        Map<Long, List<DataSetMallApiLog>> logMap = new HashMap<>();
        dataSetOrderIds.forEach(s -> logMap.put(s, new ArrayList<>()));
        BoolQueryBuilder logQuery = QueryBuilders.boolQuery();
        logQuery.must(QueryBuilders.termsQuery("dataSetMallApiId", dataSetOrderIds));
        Long span = System.currentTimeMillis() - time;
        logQuery.must(QueryBuilders.rangeQuery("createTime").gt(span));
        int pageNum = 1;

        while (true) {
            Page<DataSetMallApiLog> list = esDataSetMallApiLogSvc.getListByQuery(pageNum, 10000, logQuery);
            List<DataSetMallApiLog> data = list.getData();
            for (DataSetMallApiLog dataSetMallApiLog : data) {
                List<DataSetMallApiLog> dataSetMallApiLogs = logMap.get(dataSetMallApiLog.getDataSetMallApiId());
                dataSetMallApiLogs.add(dataSetMallApiLog);
            }
            if (data.size() < 10000) {
                break;
            }
            pageNum++;
        }
        //计算
        Map<Long, Map<String, Object>> result = new HashMap<>();
        logMap.forEach((id, logs) -> {
            Double respDurationAll = logs.stream().mapToDouble(DataSetMallApiLog::getRespDuration).sum();

            Map<String, Object> map = new HashMap<>();
            int reqNumber = logs.size() == 0 ? -1 : logs.size();
            map.put("reqNumber", reqNumber);
            Double respTimeAvg = reqNumber <= 0 ? -1 : respDurationAll / reqNumber;
            map.put("respTimeAvg", respTimeAvg.longValue());
            result.put(id, map);
        });
        return result;
    }


    @Override
    public JSONObject findDataSetById(Long id, boolean isCheck) {
        SysUser user = new UserInfo();
        try {
            user = SysUtil.getCurrentUserInfo();
        } catch (LoginException e) {
            user.setDomainId(1L);
            user.setLoginCode("system");
        }
        Boolean isAdmin = isAdmin(user.getLoginCode());
        Long domainId = user.getDomainId();
        JSONObject dataSet = esDataSetSvc.getById(id);
        Assert.notNull(dataSet, "数据集不存在");
        DataSetMallApi dataSetMallApi = getDataSetMallApi(dataSet);
        String userLoginCode = user.getLoginCode();
        if (null != userLoginCode && (userLoginCode.equals(dataSetMallApi.getCreater()) || userLoginCode.equals("system"))) {
            dataSet.put("permissionLevel", OperateType.Write.getCode());
        } else {
            BoolQueryBuilder coordinationQuery = QueryBuilders.boolQuery();
            coordinationQuery.must(QueryBuilders.termQuery("domainId", domainId));
            coordinationQuery.must(QueryBuilders.termQuery("coordinationUserCode.keyword", user.getLoginCode()));
            coordinationQuery.must(QueryBuilders.termQuery("dataSetMallApiId", dataSetMallApi.getId()));
            List<DataSetCoordination> coordinationList = esDataSetCoordinationSvc.getListByQuery(coordinationQuery);
            if (coordinationList.isEmpty() && dataSetMallApi.getShareLevel() == OperateType.Invisible.getCode() && !isAdmin) {
                String name = "";
                if (!StringUtils.isEmpty(dataSet.getString("name"))) {
                    name = dataSet.getString("name");
                }
                throw new RuntimeException("暂无权限访问"+ name +"数据集");
            } else {
                dataSet.put("permissionLevel", OperateType.Write.getCode());
            }
        }
        String thumbnail = dataSet.getString("thumbnail");
        if(!BinaryUtils.isEmpty(thumbnail)){
            dataSet.put("thumbnail", httpResourceUrl + thumbnail);
        }
        if (isCheck) {
            try {
                IMallApiSvc mallApi = getMallApi(dataSetMallApi.getType());
                mallApi.checkCharacteristic(user, dataSet);
            } catch (Exception e) {
                throw new MessageException(e.getMessage() + "请检查[" + dataSet.getString("name") +"]数据集数据信息！");
            }
        }
        return dataSet;
    }

    public List<JSONObject> findDataSetByIds(List<Long> ids, boolean isCheck) {
        SysUser user = new UserInfo();
        try {
            user = SysUtil.getCurrentUserInfo();
        } catch (LoginException e) {
            user.setDomainId(1L);
            user.setLoginCode("system");
        }
        Long domainId = user.getDomainId();
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termsQuery("id", ids));
        List<JSONObject> dataSetList = esDataSetSvc.getListByQuery(query);

        Assert.notNull(dataSetList, "数据集不存在");
        String userLoginCode = user.getLoginCode();
        BoolQueryBuilder coordinationQuery = QueryBuilders.boolQuery();
        coordinationQuery.must(QueryBuilders.termQuery("domainId", domainId));
        coordinationQuery.must(QueryBuilders.termQuery("coordinationUserCode.keyword", user.getLoginCode()));
        List<DataSetCoordination> coordinationList = esDataSetCoordinationSvc.getListByQuery(coordinationQuery);
        Map<Long, DataSetCoordination> coordinationMap = coordinationList.stream().collect(Collectors.toMap(DataSetCoordination::getDataSetMallApiId, each -> each, (key1, key2) -> key2));
        for (JSONObject dataSet : dataSetList) {
            DataSetMallApi dataSetMallApi = getDataSetMallApi(dataSet);
            if (null != userLoginCode && (userLoginCode.equals(dataSetMallApi.getCreater()) || userLoginCode.equals("system"))) {
                dataSet.put("permissionLevel", OperateType.Write.getCode());
            } else {

                if (BinaryUtils.isEmpty(coordinationMap.get(dataSetMallApi.getId())) && dataSetMallApi.getShareLevel() == OperateType.Invisible.getCode()) {
                    throw new RuntimeException("暂无权限");
                } else {
                    dataSet.put("permissionLevel", OperateType.Write.getCode());
                }
            }
            if (isCheck) {
                IMallApiSvc mallApi = getMallApi(dataSetMallApi.getType());
                mallApi.checkCharacteristic(user, dataSet);
            }
        }
        return dataSetList;
    }

    @Override
    public JSONObject execute(Long domainId, String language, Long id, JSONObject body) {
        JSONObject dataSet = null;
        // 如果ID为空说明是外部请求，通过name或类型匹配
        if (BinaryUtils.isEmpty(id)) {
            BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
            queryBuilder.filter(QueryBuilders.termQuery("domainId", domainId));
            // 如果是关系分类或CI分类通过type查找，如果是遍历通过名称查询
            int dataSetType = body.getInteger("type");
            if (DataSetMallApiType.RelClass.getCode() == dataSetType
                    || DataSetMallApiType.CiClass.getCode() == dataSetType || DataSetMallApiType.UpDownNFloor.getCode() == dataSetType) {
                queryBuilder.filter(QueryBuilders.termQuery("type", dataSetType));
            } else {
                String dataSetName = body.getString("dataSetName");
                Assert.notNull(dataSetName, "X_PARAM_NOT_NULL${name:数据集标识}");
                queryBuilder.filter(QueryBuilders.termQuery("name.keyword", dataSetName));
            }
            List<JSONObject> dataSetList = esDataSetSvc.getListByQuery(queryBuilder);
            if (dataSetList.size() > 0) {
                dataSet = dataSetList.get(0);
            }
        } else {
            dataSet = esDataSetSvc.getById(id);
        }
        if (dataSet == null) {
            throw MessageException.i18n("DCV_BS_OBJ_DATASET_NOT_EXIST");
        }
        DataSetMallApi dataSetMallApi = getDataSetMallApi(dataSet);
        IMallApiSvc mallApi = getMallApi(dataSetMallApi.getType());
        return mallApi.execute(dataSetMallApi, body);
    }

    @Override
    public JSONObject execute(Long domainId, JSONObject body) {
        // 直接根据body参数进行执行
        DataSetMallApi dataSetMallApi = getDataSetMallApi(body);
        IMallApiSvc mallApi = getMallApi(dataSetMallApi.getType());
        return mallApi.realTimeExecute(dataSetMallApi, body);
    }

    @Override
    public JSONObject realTimeExecute(Long domainId, String language, Long id, JSONObject body) {
        JSONObject dataSet = null;
        // 如果ID为空说明是外部请求，通过name或类型匹配
        if (BinaryUtils.isEmpty(id)) {
            BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
            queryBuilder.filter(QueryBuilders.termQuery("domainId", domainId));
            String dataSetName = body.getString("dataSetName");
            if (!BinaryUtils.isEmpty(dataSetName)) {
                queryBuilder.filter(QueryBuilders.termQuery("name.keyword", dataSetName));
                List<JSONObject> dataSetList = esDataSetSvc.getListByQuery(queryBuilder);
                if (dataSetList.size() > 0) {
                    dataSet = dataSetList.get(0);
                }
            }
        } else {
            dataSet = esDataSetSvc.getById(id);
        }
        if (dataSet == null) {
            //数据集不存在
            JSONObject param = body.getJSONObject("param");
            if (BinaryUtils.isEmpty(param)) {
                throw MessageException.i18n("DCV_BS_OBJ_DATASET_NOT_EXIST");
            } else {
                dataSet = param;
                body = body.getJSONObject("param");
            }
        }
        DataSetMallApi dataSetMallApi = getDataSetMallApi(dataSet);
        IMallApiSvc mallApi = getMallApi(dataSetMallApi.getType());
        return mallApi.realTimeExecute(dataSetMallApi, body);
    }

    @Override
    public boolean shareDataSet(Long id, int shareLevel) {
        JSONObject dataSet = esDataSetSvc.getById(id);
        if (dataSet.isEmpty()) {
            throw MessageException.i18n("DCV_BS_OBJ_DATASET_NOT_EXIST");
        }
        dataSet.put("shareLevel", shareLevel);
        Boolean needCheckRule = Boolean.FALSE;
        if (shareLevel == 1) {
            // shareLevel = 1 表示启用 需要校验规则
            needCheckRule = Boolean.TRUE;
        }
        saveOrUpdateDataSetExtend(dataSet, false, needCheckRule);
        return true;
    }

    @Override
    public List<DataSetMallApiRelationRule> findAllRelationDateSet(Long domainId) {
        List<DataSetMallApiRelationRule> retList = new ArrayList<>();

        BoolQueryBuilder dataSetMallApiQuery = QueryBuilders.boolQuery();
        dataSetMallApiQuery.must(QueryBuilders.termQuery("domainId", domainId));
        dataSetMallApiQuery.must(QueryBuilders.termQuery("type", DataSetMallApiType.RelationRule.getCode()));
        SortBuilder<?> sort = SortBuilders.fieldSort("modifyTime").order(SortOrder.DESC);
        List<SortBuilder<?>> sorts = new ArrayList<SortBuilder<?>>();
        sorts.add(sort);
        List<JSONObject> dataSetMallApiAll = esDataSetSvc.getSortListByQuery(dataSetMallApiQuery, sorts);
        for (JSONObject dataset : dataSetMallApiAll) {
            retList.add(new DataSetMallApiRelationRule(dataset));
        }

        return retList;
    }

    @Override
    public void updateMallApiExeResult(DataSetMallApiRelationRule dataSetRelationRule, Map<Long, FriendInfo> friendInfoMap) {
        try {
            BoolQueryBuilder deleteQuery = QueryBuilders.boolQuery();
            deleteQuery.must(QueryBuilders.termQuery("dataSetId", dataSetRelationRule.getId()));
            esDataSetExeResultSvc.deleteByQuery(deleteQuery, true);
            List<DataSetExeResult> resultList = new ArrayList<>();

            for (Map.Entry<Long, FriendInfo> itemEntry : friendInfoMap.entrySet()) {
                Long startCiId = itemEntry.getKey();
                FriendInfo friendInfo = itemEntry.getValue();

                // ci id
                List<Long> ciIds = new ArrayList<>();
                List<CcCiInfo> ciNodes = friendInfo.getCiNodes();
                CcCiInfo startNode = null;
                for (CcCiInfo ciNode : ciNodes) {
                    if (ciNode.getCi().getId().equals(startCiId)) {
                        startNode = ciNode;
                    }
                    ciIds.add(ciNode.getCi().getId());
                }
                // ci class ids
                List<Long> ciClassIds = new ArrayList<>();
                List<CcCiClassInfo> ciClassInfos = friendInfo.getCiClassInfos();
                for (CcCiClassInfo ciClassInfo : ciClassInfos) {
                    ciClassIds.add(ciClassInfo.getCiClass().getId());
                }
                // rltLine Id
                List<Long> ciRltLineIds = new ArrayList<>();
                List<ESCIRltInfo> ciRltLines = friendInfo.getCiRltLines();
                for (ESCIRltInfo ciRltLine : ciRltLines) {
                    ciRltLineIds.add(ciRltLine.getId());
                }

                SimpleFriendInfo simpleFriendInfo = new SimpleFriendInfo();
                simpleFriendInfo.setCiIdMap(friendInfo.getCiIdByNodeMap());
                simpleFriendInfo.setCiIds(ciIds);
                simpleFriendInfo.setCiClassIds(ciClassIds);
                simpleFriendInfo.setCiRltLineIds(ciRltLineIds);

                DataSetExeResult exeResult = new DataSetExeResult();
                exeResult.setDomainId(dataSetRelationRule.getDomainId());
                exeResult.setId(ESUtil.getUUID());
                exeResult.setDataSetId(dataSetRelationRule.getId());
                exeResult.setStartCi(startNode);

                exeResult.setSimpleFriendInfo(simpleFriendInfo);
                resultList.add(exeResult);
                if (resultList.size() == 2000) {
                    esDataSetExeResultSvc.saveOrUpdateBatch(resultList);
                    resultList = new ArrayList<>();
                }
            }
            if (!resultList.isEmpty()) {
                esDataSetExeResultSvc.saveOrUpdateBatch(resultList);
                BoolQueryBuilder query = QueryBuilders.boolQuery();
                query.must(QueryBuilders.termQuery("dataSetId.keyword", dataSetRelationRule.getId()));
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            log.error("Get dataSet execute result error", e);
            throw new MessageException(e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> getDataSetSheets(Long dataSetId) {
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("dataSetId", dataSetId));

        Map<String, Long> stringLongMap = esDataSetExeResultSheetSvc.groupByCountField("sheetId.keyword", query);
        List<String> sheetList = new ArrayList<>();
        for (Map.Entry<String, Long> itemEntry : stringLongMap.entrySet()) {
            sheetList.add(itemEntry.getKey());
        }
        try {
            // 截取出Sheet页的index进行排序
            sheetList.sort((o1, o2) -> {
                Integer o1No = Integer.parseInt(o1.replace("Path", ""));
                Integer o2No = Integer.parseInt(o2.replace("Path", ""));
                return o1No - o2No;
            });
        } catch (Exception e) {
            log.error("Sort sheet list error", e);
        }
        // Collections.sort(sheetList);

        //增加路径字段返回
        List<Map<String, Object>> ret = new ArrayList<Map<String, Object>>();
        for (String sheetId : sheetList) {
            BoolQueryBuilder q = QueryBuilders.boolQuery();
            q.must(QueryBuilders.termQuery("dataSetId", dataSetId));
            q.must(QueryBuilders.termQuery("sheetId.keyword", sheetId));
            Page<DataSetExeResultSheet> page = esDataSetExeResultSheetSvc.getListByQuery(1, 1, q);
            List<String> pathList = page.getData().get(0).getClassNamePath();

            Map<String, Object> m = new HashMap<String, Object>();
            m.put("name", sheetId);
            m.put("path", pathList);
            ret.add(m);
        }
        return ret;
    }
    @Override
    public Map<String, Long> getDataSetLineCount(List<String> dataSetIds){
        //创建查询条件
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termsQuery("dataSetId", dataSetIds));
        //查询
        return esDataSetExeResultSheetSvc.groupByCountField("dataSetId", query);
    }

    @Override
    public JSONObject queryRuleLegitimateCi(Integer pageNum, Integer pageSize, Long dataSetId, String name, String like) {
        JSONObject result = new JSONObject();

        BoolQueryBuilder query = QueryBuilders.boolQuery();
        if(BinaryUtils.isEmpty(dataSetId)){
            BoolQueryBuilder queryByName = QueryBuilders.boolQuery();
            queryByName.must(QueryBuilders.termQuery("name.keyword", name));
            List<JSONObject> dataSetList = esDataSetSvc.getListByQuery(queryByName);
            if(BinaryUtils.isEmpty(dataSetList)){
                throw new ServiceException("未获取到'"+ name +"'数据超市");
            }
            dataSetId = dataSetList.get(0).getLong("id");
        }
        query.must(QueryBuilders.termQuery("dataSetId", dataSetId));
        if(!BinaryUtils.isEmpty(like)){
            query.must(QueryBuilders.wildcardQuery("startCi.ci.ciLabel.keyword", "*" + like + "*"));
        }
        Page<DataSetExeResult> listByQuery = esDataSetExeResultSvc.getListByQuery(pageNum, pageSize, query);
        result.put("pageNum", listByQuery.getPageNum());
        result.put("pageSize", listByQuery.getPageSize());
        result.put("totalCount", listByQuery.getTotalPages());
        Map<String, CcCiInfo> data = listByQuery.getData().stream().map(DataSetExeResult::getStartCi).collect(Collectors.toMap(each -> each.getCi().getCiCode(), each -> each, (k1, k2) -> k2));
//        fillCiInfo(data, attrDefs);
        result.put("data", Lists.newArrayList(data.values()));
        result.put("count", data.size());
        result.put("id", dataSetId);
        return result;
    }

    @Override
    public FriendInfo queryFriendByStartCiId(Long dataSetId, Long startCiId) {
        SysUser user = new UserInfo();
        try {
            user = SysUtil.getCurrentUserInfo();
        } catch (LoginException e) {
            user.setDomainId(1L);
            user.setLoginCode("system");
        }
        boolean isSuccess = true;
        long startTime = System.currentTimeMillis();
        int ciSize = 0;
        int rltSize = 0;
        try {
            log.info("开始执行数据集,入参为：dataSetId={} , startCiId={}", dataSetId, startCiId);
            JSONObject dataSet = findDataSetById(dataSetId, true);
            DataSetMallApi dataSetMallApi = getDataSetMallApi(dataSet);
            log.info("第一步获取数据集：dataSet={}", dataSet);
            DataSetMallApiRelationRule dataSetMallRelationApi = (DataSetMallApiRelationRule) dataSetMallApi;
            CCcCi cdt = new CCcCi();
            cdt.setId(startCiId);
            List<CcCiInfo> ciInfos = iciSvc.queryCiInfoList(user.getDomainId(), cdt, null, true, false);
            log.info("第二步获取CI对象：ciInfos={}", ciInfos);
            FriendInfo friendInfo = relationRuleAnalysisSvc.queryCiFriendByCiIdAndRule(ciInfos.get(0), dataSetMallRelationApi);
            log.info("第三步获取结果数据friendInfo：friendInfo={}", friendInfo);
//            fillSearchDataCiInfo(friendInfo);
            ciSize = friendInfo.getCiNodes().size();
            log.info("第四步获取Node的size：nodeSize={}", ciSize);
            rltSize = friendInfo.getCiRltLines().size();
            log.info("第五步获取CiRltLines的size：ciRltLinesSize={}", rltSize);
            return friendInfo;
        } catch (Exception e) {
            isSuccess = false;
            log.error("", e);
            throw new RuntimeException(e.getMessage());
        } finally {
            DataSetMallApiLog dataSetMallApiLog = new DataSetMallApiLog();
            dataSetMallApiLog.setDomainId(user.getDomainId());
            dataSetMallApiLog.setId(ESUtil.getUUID());
            dataSetMallApiLog.setDataSetMallApiId(dataSetId);
            dataSetMallApiLog.setSuccess(isSuccess);
            dataSetMallApiLog.setRespUserCode(user.getLoginCode());
            dataSetMallApiLog.setRespDuration(System.currentTimeMillis() - startTime);
            dataSetMallApiLog.setCreateTime(System.currentTimeMillis());
            dataSetMallApiLog.setCiTotal(ciSize);
            dataSetMallApiLog.setRelTotal(rltSize);
            esDataSetMallApiLogSvc.saveOrUpdate(dataSetMallApiLog);
        }
    }

    @Override
    public FriendInfo queryFriendByStartCiIdAndTargetClass(Long dataSetId, Long startCiId, Set<Long> targetClassIds) {
        SysUser user = new UserInfo();
        try {
            user = SysUtil.getCurrentUserInfo();
        } catch (LoginException e) {
            user.setDomainId(1L);
            user.setLoginCode("system");
        }
        boolean isSuccess = true;
        long startTime = System.currentTimeMillis();
        int ciSize = 0;
        int rltSize = 0;
        try {
            log.info("开始执行数据集,入参为：dataSetId={} , startCiId={}", dataSetId, startCiId);
            JSONObject dataSet = findDataSetById(dataSetId, true);
            DataSetMallApi dataSetMallApi = getDataSetMallApi(dataSet);
            log.info("第一步获取数据集：dataSet={}", dataSet);
            DataSetMallApiRelationRule dataSetMallRelationApi = (DataSetMallApiRelationRule) dataSetMallApi;
            pruneRule(dataSetMallRelationApi, targetClassIds);
            CCcCi cdt = new CCcCi();
            cdt.setId(startCiId);
            List<CcCiInfo> ciInfos = iciSvc.queryCiInfoList(user.getDomainId(), cdt, null, true, false);
            log.info("第二步获取CI对象：ciInfos={}", ciInfos);
            FriendInfo friendInfo = relationRuleAnalysisSvc.queryCiFriendByCiIdAndRule(ciInfos.get(0), dataSetMallRelationApi);
            log.info("第三步获取结果数据friendInfo：friendInfo={}", friendInfo);
//            fillSearchDataCiInfo(friendInfo);
            ciSize = friendInfo.getCiNodes().size();
            log.info("第四步获取Node的size：nodeSize={}", ciSize);
            rltSize = friendInfo.getCiRltLines().size();
            log.info("第五步获取CiRltLines的size：ciRltLinesSize={}", rltSize);
            return friendInfo;
        } catch (Exception e) {
            isSuccess = false;
            log.error("", e);
            throw new RuntimeException(e.getMessage());
        } finally {
            DataSetMallApiLog dataSetMallApiLog = new DataSetMallApiLog();
            dataSetMallApiLog.setDomainId(user.getDomainId());
            dataSetMallApiLog.setId(ESUtil.getUUID());
            dataSetMallApiLog.setDataSetMallApiId(dataSetId);
            dataSetMallApiLog.setSuccess(isSuccess);
            dataSetMallApiLog.setRespUserCode(user.getLoginCode());
            dataSetMallApiLog.setRespDuration(System.currentTimeMillis() - startTime);
            dataSetMallApiLog.setCreateTime(System.currentTimeMillis());
            dataSetMallApiLog.setCiTotal(ciSize);
            dataSetMallApiLog.setRelTotal(rltSize);
            esDataSetMallApiLogSvc.saveOrUpdate(dataSetMallApiLog);
        }
    }

    /**
     * 对规则进行剪枝操作，移除节点中不存在目标分类的分支，移除路径中目标分类节点中不存在目标分类的子节点路径
     *
     * @param rule     规则
     * @param classIds 目标分类id
     */
    private static void pruneRule(DataSetMallApiRelationRule rule, Set<Long> classIds) {
        Map<Long, RelationRuleNode> idNodeMap = new HashMap<>();
        rule.getNodes().forEach(node -> idNodeMap.put(node.getPageNodeId(), node));
        Map<Long, List<RelationRuleLine>> startIdLineMap = new HashMap<>();
        rule.getLines().forEach(line -> {
            if (!startIdLineMap.containsKey(line.getNodeStartId())) {
                startIdLineMap.put(line.getNodeStartId(), new ArrayList<>());
            }
            startIdLineMap.get(line.getNodeStartId()).add(line);
        });
        boolean hasTarget = hasTargetInChildren(rule.getPageNodeId(), idNodeMap, startIdLineMap, classIds);
        if (!hasTarget || CollectionUtils.isEmpty(rule.getNodes())) {
            throw new ServiceException("规则中未找到目标分类");
        } else {
            rule.setNodes(new ArrayList<>(idNodeMap.values()));
            List<RelationRuleLine> lines = new ArrayList<>();
            startIdLineMap.forEach((startId, lineList) -> lines.addAll(lineList));
            rule.setLines(lines);
        }
    }

    /**
     * 判断子节点中是否存在目标分类
     * 如果子节点中不存在目标分类则删除子节点及当前节点与子节点的连线
     *
     * @param nodeId         当前要判断的节点id
     * @param idNodeMap      当前所有节点以节点id为key的map
     * @param startIdLineMap 当前所有线以开始节点id为key的map
     * @param targetClassIds 目标分类
     * @return 当前节点下的子节点中是否存在目标分类
     */
    private static boolean hasTargetInChildren(Long nodeId, Map<Long, RelationRuleNode> idNodeMap, Map<Long, List<RelationRuleLine>> startIdLineMap, Set<Long> targetClassIds) {
        List<RelationRuleLine> lines = startIdLineMap.get(nodeId);
        boolean hasTargetInChildren = false;
        if (!CollectionUtils.isEmpty(lines)) {
            // 有子节点
            Iterator<RelationRuleLine> it = lines.iterator();
            while (it.hasNext()) {
                RelationRuleLine line = it.next();
                Long childNodeId = line.getNodeEndId();
                // 在n条路径收束时，子节点有可能在其他路径的判断中被删除
                if (idNodeMap.containsKey(childNodeId)) {
                    boolean childHasTarget = hasTargetInChildren(childNodeId, idNodeMap, startIdLineMap, targetClassIds);
                    if (!childHasTarget) {
                        idNodeMap.remove(childNodeId);
                        it.remove();
                    } else {
                        hasTargetInChildren = true;
                    }
                } else {
                    it.remove();
                }
            }
        }
        if (!hasTargetInChildren) {
            return targetClassIds.contains(idNodeMap.get(nodeId).getClassId());
        } else {
            return true;
        }
    }


    @Override
    public Map<Long, Integer> queryRuleNodeCiNum(Long dataSetId) {
        JSONObject dataSet = findDataSetById(dataSetId, true);
        if (dataSet == null) {
            throw new RuntimeException("规则不存在");
        }
        Map<Long, Integer> nodeCiSize = new HashMap<>();
        DataSetMallApiRelationRule dataSetMallApi = (DataSetMallApiRelationRule) getDataSetMallApi(dataSet);
        List<RelationRuleNode> nodes = dataSetMallApi.getNodes();
        nodes.forEach(relationRuleNode -> nodeCiSize.put(relationRuleNode.getPageNodeId(), 0));

        Map<Long, Set<Long>> ciIdByNodeMap = new HashMap<>();
        if (0 == isTaskRunning(dataSetId)) {
            BoolQueryBuilder query = QueryBuilders.boolQuery();
            query.must(QueryBuilders.termQuery("dataSetId", dataSetId));
            int pageNum = 1;
            while (true) {
                Page<DataSetExeResult> page = esDataSetExeResultSvc.getListByQuery(pageNum, 1000, query);
                for (DataSetExeResult result : page.getData()) {
                    SimpleFriendInfo simpleFriendInfo = result.getSimpleFriendInfo();
                    Map<Long, Set<Long>> ciIdMap = simpleFriendInfo.getCiIdMap();
                    ciIdMap.forEach((k, v) -> {
                        Set<Long> set = ciIdByNodeMap.computeIfAbsent(k, k1 -> new HashSet<>());
                        set.addAll(v);
                    });
                }
                if (pageNum >= page.getTotalPages()) {
                    break;
                } else {
                    pageNum++;
                }
            }
        } else {
            throw new RuntimeException("数据正在构建中，请稍候");
        }

        ciIdByNodeMap.forEach((k, v) -> {
            nodeCiSize.put(k, v.size());
        });
        return nodeCiSize;
    }

    @Override
    public RltRuleTableData queryDisassembleFriendInfoDataByPath(Long dataSetId, Long startCiId) {
        SysUser user = SysUtil.getCurrentUserInfo();
        boolean isSuccess = true;
        long startTime = System.currentTimeMillis();
        int ciSize = 0;
        int rltSize = 0;
        try {
            JSONObject dataSet = findDataSetById(dataSetId, true);
            DataSetMallApi dataSetMallApi = getDataSetMallApi(dataSet);

            DataSetMallApiRelationRule dataSetMallRelationApi = (DataSetMallApiRelationRule) dataSetMallApi;
            CCcCi cdt = new CCcCi();
            cdt.setId(startCiId);
            List<CcCiInfo> ciInfos = iciSvc.queryCiInfoList(user.getDomainId(), cdt, null, true, false);
            FriendInfo friendInfo = relationRuleAnalysisSvc.queryCiFriendByCiIdAndRule(ciInfos.get(0), dataSetMallRelationApi);
            RltRuleTableData rltRuleTableData = relationRuleAnalysisSvc.disassembleFriendInfoDataByPath(dataSetMallRelationApi, Collections.singletonList(ciInfos.get(0).getCi().getId()), friendInfo);
            ciSize = friendInfo.getCiNodes().size();
            rltSize = friendInfo.getCiRltLines().size();
            return rltRuleTableData;
        } catch (Exception e) {
            isSuccess = false;
            throw new RuntimeException(e.getMessage());
        } finally {
            DataSetMallApiLog dataSetMallApiLog = new DataSetMallApiLog();
            dataSetMallApiLog.setDomainId(user.getDomainId());
            dataSetMallApiLog.setId(ESUtil.getUUID());
            dataSetMallApiLog.setDataSetMallApiId(dataSetId);
            dataSetMallApiLog.setSuccess(isSuccess);
            dataSetMallApiLog.setRespUserCode(user.getLoginCode());
            dataSetMallApiLog.setRespDuration(System.currentTimeMillis() - startTime);
            dataSetMallApiLog.setCreateTime(System.currentTimeMillis());
            dataSetMallApiLog.setCiTotal(ciSize);
            dataSetMallApiLog.setRelTotal(rltSize);
            esDataSetMallApiLogSvc.saveOrUpdate(dataSetMallApiLog);
        }
    }

    @Override
    public JSONObject countStatistics(Long dataSetId, Chart chart) {
        SysUser user = SysUtil.getCurrentUserInfo();
        long startTime = System.currentTimeMillis();
        boolean isSuccess = true;
        AtomicInteger ciSize = new AtomicInteger();
        AtomicInteger rltSize = new AtomicInteger();
        try {
            JSONObject dataSet = findDataSetById(dataSetId, true);
            DataSetMallApi dataSetMallApi = getDataSetMallApi(dataSet);
            DataSetMallApiRelationRule dataSetMallRelationApi = (DataSetMallApiRelationRule) dataSetMallApi;
            if (0 == isTaskRunning(dataSetId)) {
                Map<Long, SimpleFriendInfo> simpleFriendInfoMap = new HashMap<>();
                BoolQueryBuilder query = QueryBuilders.boolQuery();
                query.must(QueryBuilders.termQuery("dataSetId", dataSetId));
                int pageNum = 1;
                while (true) {
                    Page<DataSetExeResult> page = esDataSetExeResultSvc.getListByQuery(pageNum, 1000, query);
                    for (DataSetExeResult result : page.getData()) {
                        SimpleFriendInfo simpleFriendInfo = result.getSimpleFriendInfo();
                        if (simpleFriendInfo.getCiIds() != null) {
                            ciSize.addAndGet(simpleFriendInfo.getCiIds().size());
                        }
                        if (simpleFriendInfo.getCiRltLineIds() != null) {
                            rltSize.addAndGet(simpleFriendInfo.getCiRltLineIds().size());
                        }
                        simpleFriendInfoMap.put(result.getStartCi().getCi().getId(), simpleFriendInfo);
                    }
                    if (pageNum >= page.getTotalPages()) {
                        break;
                    } else {
                        pageNum++;
                    }
                }
                JSONObject result = relationRuleAnalysisSvc.countStatistics(dataSetMallRelationApi, simpleFriendInfoMap, chart);
				// JSONObject retJson = new JSONObject();
				// retJson.put("xData", result.get("xData"));
				// retJson.put("yData", "");
				// retJson.put("data", result.get("data"));
				return result;
            } else {
                throw new RuntimeException("数据正在构建中，请稍候");
            }
        } catch (Exception e) {
            isSuccess = false;
            throw e;
        } finally {
            DataSetMallApiLog dataSetMallApiLog = new DataSetMallApiLog();
            dataSetMallApiLog.setDomainId(user.getDomainId());
            dataSetMallApiLog.setId(ESUtil.getUUID());
            dataSetMallApiLog.setDataSetMallApiId(dataSetId);
            dataSetMallApiLog.setSuccess(isSuccess);
            dataSetMallApiLog.setRespUserCode(user.getLoginCode());
            dataSetMallApiLog.setRespDuration(System.currentTimeMillis() - startTime);
            dataSetMallApiLog.setCreateTime(System.currentTimeMillis());
            dataSetMallApiLog.setCiTotal(ciSize.get());
            dataSetMallApiLog.setRelTotal(rltSize.get());
            esDataSetMallApiLogSvc.saveOrUpdate(dataSetMallApiLog);
        }
    }

    @Override
    public List<JSONObject> getQueryCondition(Long dataSetId, String sheetId) {
        // 默认是第一个Sheet
        if (BinaryUtils.isEmpty(sheetId)) {
            sheetId = "Path1";
        }
        JSONObject dataSetJson = esDataSetSvc.getById(dataSetId);
        DataSetMallApiRelationRule dataSetMallApiRelationRule = new DataSetMallApiRelationRule(dataSetJson);
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("dataSetId", dataSetId));
        query.must(QueryBuilders.termQuery("sheetId.keyword", sheetId));
        Page<DataSetExeResultSheet> exeResultSheetPage = esDataSetExeResultSheetSvc.getListByQuery(1, 10, query);

        List<JSONObject> conditionList = new ArrayList<>();
        if (exeResultSheetPage.getData().size() > 0) {
            // 取得遍历结果中包含的分类
            DataSetExeResultSheet exeResultSheet = exeResultSheetPage.getData().get(0);
            List<Long> classIdsInSheetResult = exeResultSheet.getClassIds();

            // 获取到完整的分类信息
            Long[] classIds = new Long[classIdsInSheetResult.size()];
            for (int i = 0; i < classIdsInSheetResult.size(); i++) {
                classIds[i] = classIdsInSheetResult.get(i);
            }
            CCcCiClass cdt = new CCcCiClass();
            cdt.setIds(classIds);
            cdt.setDomainId(dataSetMallApiRelationRule.getDomainId());
            List<CcCiClassInfo> classList = iciClassSvc.queryClassByCdt(cdt);
            Map<Long, String> classNameMap = new HashMap<>();
            //Map<classId_属性名, 枚举值数组>
            Map<String, JSONArray> attrNameValuesMap = new HashMap<>();
            for (CcCiClassInfo ccCiClass : classList) {
                classNameMap.put(ccCiClass.getCiClass().getId(), ccCiClass.getCiClass().getClassName());
                Long classId = ccCiClass.getCiClass().getId();
                List<CcCiAttrDef> attrDefList = ccCiClass.getAttrDefs();
                for (CcCiAttrDef attrDef : attrDefList) {
                    // 如果是枚举类型的放入Map
                    if (_CONDITION_TYPE_LIST.contains(attrDef.getProType())) {
                        attrNameValuesMap.put(classId + "_" + attrDef.getProName(),
                                JSONArray.parseArray(attrDef.getEnumValues()));

                    }
                }
            }

            List<RelationRuleNode> nodes = dataSetMallApiRelationRule.getNodes();
            for (RelationRuleNode node : nodes) {
                // 只取遍历结果之中包含的分类
                if (classIdsInSheetResult.contains(node.getClassId())) {
                    JSONArray nodeReturns = JSON.parseArray(node.getNodeReturns());
                    for (int i = 0; i < nodeReturns.size(); i++) {
                        JSONObject nodeAttr = nodeReturns.getJSONObject(i);
                        String attrName = nodeAttr.getString("proName");
                        // 如果是条件支持的属性类型
                        if (attrNameValuesMap.containsKey(node.getClassId() + "_" + attrName)) {
                            JSONObject conditionJson = new JSONObject();
                            // 查询条件的属性名
                            conditionJson.put("type", nodeAttr.getInteger("proType"));
                            conditionJson.put("classId", node.getClassId());
                            conditionJson.put("nodeId", node.getPageNodeId());
                            conditionJson.put("attrName", attrName);
                            conditionJson.put("attrKey", node.getPageNodeId() + "_" + classNameMap.get(node.getClassId()) + "_" + attrName);
                            //枚举取值特殊处理
                            if (nodeAttr.getInteger("proType") == PropertyType.ENUM.getValue()) {
                                // 从node中直接取的，当分类属性值变化时此处不会同步变化
                                JSONArray attrValues = attrNameValuesMap.get(node.getClassId() + "_" + attrName);
                                // 组装查询条件中属性的枚举值，为每个值填充上classId
                                JSONArray attrEnumValues = new JSONArray();
                                for (Object attrValue : attrValues) {
                                    JSONObject valueJson = new JSONObject();
                                    valueJson.put("classId", node.getClassId());
                                    valueJson.put("value", attrValue);
                                    attrEnumValues.add(valueJson);
                                }
                                conditionJson.put("attrEnumValues", attrEnumValues);
                            }
                            conditionList.add(conditionJson);
                        }

                    }
                }
            }
        }
        return conditionList;
    }

    @Override
    public DataSetExeResultSheetPage queryDataSetResultBySheet(Long domainId, Long dataSetId, String sheetId, int pageNum, int pageSize, String sortCol, boolean isDesc, JSONArray condition, String userCode) {
        long startTime = System.currentTimeMillis();
        // 如果有任务锁，说明批处理任务还未结束
        if (taskLockSvc.isLocked("DataSetTask_" + dataSetId)) {
            throw new RuntimeException("数据正在构建中，请稍候");
        }

        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("dataSetId", dataSetId));
        // 如果不指定SheetId
        if (!BinaryUtils.isEmpty(sheetId)) {
            query.must(QueryBuilders.termQuery("sheetId.keyword", sheetId));
        }

        // 解析查询条件condition
        for (int i = 0; i < condition.size(); i++) {
            JSONObject itemCond = condition.getJSONObject(i);
            // 通过属性查询
            if (itemCond.getInteger("type") == PropertyType.ENUM.getValue()) {
                String attrKey = itemCond.getString("attrKey");
                JSONArray attrValues = itemCond.getJSONArray("attrEnumValues");
                BoolQueryBuilder shouldQuery = QueryBuilders.boolQuery();
                for (int j = 0; j < attrValues.size(); j++) {
                    JSONObject itemValue = attrValues.getJSONObject(j);
                    //分类ID
                    shouldQuery.should(QueryBuilders.termQuery("attrs." + attrKey + ".keyword",
                            itemValue.getString("value")));
                }
                query.must(shouldQuery);
            } else if (itemCond.getInteger("type") == PropertyType.DATE.getValue()) {
                String attrKey = itemCond.getString("attrKey");
                if (!itemCond.containsKey("from")) {
                    Long to = itemCond.getLong("to");
                    //小于等于
                    QueryBuilder rangeQuery = QueryBuilders.rangeQuery("attrs." + attrKey).lte(to);
                    query.must(rangeQuery);
                } else if (!itemCond.containsKey("to")) {
                    Long from = itemCond.getLong("from");
                    //大于等于
                    QueryBuilder rangeQuery = QueryBuilders.rangeQuery("attrs." + attrKey).gte(from);
                    query.must(rangeQuery);
                } else {
                    Long from = itemCond.getLong("from");
                    Long to = itemCond.getLong("to");
                    RangeQueryBuilder rangeQuery = QueryBuilders.rangeQuery("attrs." + attrKey).from(from).to(to);
                    query.must(rangeQuery);
                }

            } else if (itemCond.getInteger("type") == PropertyType.DOUBLE.getValue()) {
                String attrKey = itemCond.getString("attrKey");
                if (!itemCond.containsKey("from")) {
                    double to = itemCond.getDouble("to");
                    //小于等于
                    QueryBuilder rangeQuery = QueryBuilders.rangeQuery("attrs." + attrKey).lte(to);
                    query.must(rangeQuery);
                } else if (!itemCond.containsKey("to")) {
                    double from = itemCond.getDouble("from");
                    //大于等于
                    QueryBuilder rangeQuery = QueryBuilders.rangeQuery("attrs." + attrKey).gte(from);
                    query.must(rangeQuery);
                } else {
                    double from = itemCond.getDouble("from");
                    double to = itemCond.getDouble("to");
                    RangeQueryBuilder rangeQuery = QueryBuilders.rangeQuery("attrs." + attrKey).from(from).to(to);
                    query.must(rangeQuery);
                }
            } else if (itemCond.getInteger("type") == PropertyType.INTEGER.getValue()) {
                String attrKey = itemCond.getString("attrKey");
                if (!itemCond.containsKey("from")) {
                    int to = itemCond.getInteger("to");
                    //小于等于
                    QueryBuilder rangeQuery = QueryBuilders.rangeQuery("attrs." + attrKey).lte(to);
                    query.must(rangeQuery);
                } else if (!itemCond.containsKey("to")) {
                    int from = itemCond.getInteger("from");
                    //大于等于
                    QueryBuilder rangeQuery = QueryBuilders.rangeQuery("attrs." + attrKey).gte(from);
                    query.must(rangeQuery);
                } else {
                    int from = itemCond.getInteger("from");
                    int to = itemCond.getInteger("to");
                    RangeQueryBuilder rangeQuery = QueryBuilders.rangeQuery("attrs." + attrKey).from(from).to(to);
                    query.must(rangeQuery);
                }
            } else {
                //全文检索
                query.must(QueryBuilders.multiMatchQuery(itemCond.getString("value"), "attrs.*")
                        .operator(Operator.AND).type("phrase_prefix").lenient(true));

            }
        }
        // 排序
        List<SortBuilder<?>> sorts = new ArrayList<>();
        String sortStr = "startCiId";
        if (!BinaryUtils.isEmpty(sortCol)) {
            sortStr = "attrs." + sortCol + ".keyword";
        }
        SortBuilder<?> sort = SortBuilders.fieldSort(sortStr).order(isDesc ? SortOrder.DESC : SortOrder.ASC);
        sorts.add(sort);

        Page<DataSetExeResultSheet> exeResultSheetPage = esDataSetExeResultSheetSvc.getSortListByQuery(pageNum, pageSize, query, sorts);
        DataSetExeResultSheetPage retPage = new DataSetExeResultSheetPage();
        if (exeResultSheetPage.getData().size() > 0) {
            retPage = setExeResultByData(dataSetId, exeResultSheetPage.getData());
            retPage.setTotalCount(exeResultSheetPage.getTotalRows());
        }
        retPage.setPageNum(new Long(exeResultSheetPage.getPageNum()).intValue());
        retPage.setPageSize(new Long(exeResultSheetPage.getPageSize()).intValue());
        if (userCode != null) {
            // 保存日志
            DataSetMallApiLog dataSetMallApiLog = new DataSetMallApiLog();
            dataSetMallApiLog.setDomainId(domainId);
            dataSetMallApiLog.setId(ESUtil.getUUID());
            dataSetMallApiLog.setDataSetMallApiId(dataSetId);
            dataSetMallApiLog.setDataSetType(DataSetMallApiType.RelationRule.getCode());
            dataSetMallApiLog.setSuccess(true);
            dataSetMallApiLog.setRespUserCode(userCode);
            dataSetMallApiLog.setRespDuration(System.currentTimeMillis() - startTime);
            dataSetMallApiLog.setCreateTime(System.currentTimeMillis());
            dataSetMallApiLog.setCiTotal(retPage.getData().size());
            dataSetMallApiLog.setRelTotal(retPage.getData().size());
            esDataSetMallApiLogSvc.saveOrUpdate(dataSetMallApiLog);
        }

        return retPage;
    }

    @Override
    public List<DataSetExeResultSheetPage> queryDataSetResultList(List<Long> dataSetIds, String sheetId, String sortCol, boolean isDesc) {
        // 如果有任务锁，说明批处理任务还未结束
        for(Long dataSetId : dataSetIds){
            if (taskLockSvc.isLocked("DataSetTask_" + dataSetId)) {
                throw new RuntimeException("数据正在构建中,请稍候");
            }
        }
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termsQuery("dataSetId", dataSetIds));
        // 如果不指定SheetId
        if (!BinaryUtils.isEmpty(sheetId)) {
            query.must(QueryBuilders.termQuery("sheetId.keyword", sheetId));
        }
        // 排序
        List<SortBuilder<?>> sorts = new ArrayList<>();
        String sortStr = "startCiId";
        if (!BinaryUtils.isEmpty(sortCol)) {
            sortStr = "attrs." + sortCol + ".keyword";
        }
        SortBuilder<?> sort = SortBuilders.fieldSort(sortStr).order(isDesc ? SortOrder.DESC : SortOrder.ASC);
        sorts.add(sort);

        Page<DataSetExeResultSheet> exeResultSheetPage = esDataSetExeResultSheetSvc.getSortListByQuery(1, 3000, query, sorts);
        List<DataSetExeResultSheetPage> result = new ArrayList<>();
        if (!BinaryUtils.isEmpty(exeResultSheetPage.getData())) {
            Map<Long, List<DataSetExeResultSheet>> exeResultMap = exeResultSheetPage.getData().stream().collect(Collectors.groupingBy(DataSetExeResultSheet::getDataSetId));
            exeResultMap.forEach((id, exeResult) -> {
                DataSetExeResultSheetPage retPage = setExeResultByData(id, exeResult);
                retPage.setTotalCount(exeResultSheetPage.getTotalRows());
                result.add(retPage);
            });
        }
        return result;
    }

    private DataSetExeResultSheetPage setExeResultByData(Long dataSetId, List<DataSetExeResultSheet> exeResult){
        List<Map<String, String>> headers = new ArrayList<>();
        for (DataSetExeResultSheet dataSetExeResultSheet : exeResult) {
            List<Map<String, String>> tmpHeaders = dataSetExeResultSheet.getHeaders();
            for (Map<String, String> header : tmpHeaders) {
                if (!headers.contains(header)) {
                    headers.add(header);
                }
            }
        }
        // List<Map<String, String>> headers = exeResult.get(0).getHeaders();
        List<Map<String, Object>> dataList = new ArrayList<>();
        try {
            for (DataSetExeResultSheet dataSetExeResultSheet : exeResult) {
                Map<String, Object> attrMap = dataSetExeResultSheet.getAttrs();
                for (Map<String, String> header : dataSetExeResultSheet.getHeaders()) {
                    PropertyType proType = PropertyType.valueOf(Integer.parseInt(header.get("proType")));
                    if (!proType.equals(PropertyType.DATE)) {
                        continue;
                    }
                    String constraintRule = header.get("constraintRule");
                    List<String> sdfList = new ArrayList<String>();
                    if (constraintRule != null && !"".equals(constraintRule.trim())) {
                        sdfList.add(constraintRule.trim());
                    } else {
                        sdfList.addAll(CheckAttrUtil.getDefaultRuleByType(PropertyType.DATE.getValue()));
                    }
                    String val = null;
                    for (String sdfFormat : sdfList) {
                            SimpleDateFormat sdf = new SimpleDateFormat(sdfFormat);
                            val = sdf.format(new Date((Long) attrMap.get(header.get("attrKey"))));
                            break;
                    }
                    attrMap.put(header.get("attrKey"), val);
                }
                dataList.add(attrMap);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        DataSetExeResultSheetPage retPage = new DataSetExeResultSheetPage();
        retPage.setTotalCount(new Long(exeResult.size()));
        retPage.setHeaders(headers);
        retPage.setData(dataList);
        retPage.setDataSetId(dataSetId);
        return retPage;
    }

    @Override
    public List<DataSetExeResultSheetPage> getResultUsingRule(JSONObject rule, Integer limit) {
        DataSetMallApiRelationRule ruleBean = new DataSetMallApiRelationRule(rule);
        Map<Long, FriendInfo> friendInfoMap = relationRuleAnalysisSvc.queryCiFriendByRuleWithLimit(ruleBean, limit);

        //<NodePath,sheet页ID>
        Map<String, Integer> nodePathSheetMap = new HashMap<>();
        //<NodePath,Header>
        Map<String, List<Map<String, String>>> nodePathHeaderMap = new HashMap<>();
        // 将关系遍历结果按路径拆分Sheet并存取
        Map<String, List<DataSetExeResultSheet>> resultSheetDataListMap = new HashMap<>();
        Long now = System.currentTimeMillis();
        Long nano = System.nanoTime();
        Map<Integer, List<String>> pathHeadersMap = new HashMap<Integer, List<String>>();
        for (Map.Entry<Long, FriendInfo> friendInfoMapEntry : friendInfoMap.entrySet()) {
            // 获取有效返回FriendInfo结果的入口
            RltRuleTableData rltRuleTableData = relationRuleAnalysisSvc.disassembleFriendInfoDataByPath(ruleBean, Arrays.asList(friendInfoMapEntry.getKey()), friendInfoMapEntry.getValue());
            List<RltRulePathData> rltRulePathDateList = rltRuleTableData.getSheetList();
            List<Map<String, String>> headerList = null;
            List<Long> classIds = null;
            // 按路径进行Sheet页的拆分
            for (int j = 0; j < rltRulePathDateList.size(); j++) {
                RltRulePathData rltRulePathDate = rltRulePathDateList.get(j);
                List<List<Object>> ruleDatas = rltRulePathDate.getData();
                classIds = new ArrayList<>();
                headerList = new ArrayList<>();
                List<String> classHeaderList = new ArrayList<String>();
                // 表头信息
                Map<String, String> headMap = null;
                String nodePath = rltRulePathDate.getNodePath();
                String[] pageNodeArray = nodePath.split("-");
                List<Map<String, Object>> headers = rltRulePathDate.getHeaders();
                for (int i = 0; i < headers.size(); i++) {
                    Map<String, Object> header = headers.get(i);
                    Long classId = (Long) header.get("classId");
                    String className = (String) header.get("className");
                    List<String> labels = (List<String>) header.get("labels");
                    List<Integer> proTypes = (List<Integer>) header.get("proTypes");
                    List<String> constraintRules = (List<String>) header.get("constraintRules");
                    for (int k = 0; k < labels.size(); k++) {
                        String label = labels.get(k);
                        headMap = new HashMap<>();
                        headMap.put("className", className);
                        headMap.put("classId", classId + "");
                        headMap.put("attrKey", pageNodeArray[i] + "_" + className + "_" + label);
                        headMap.put("attrName", label);
                        headMap.put("proType", proTypes.get(k) + "");
                        headMap.put("constraintRule", constraintRules.get(k));
                        headerList.add(headMap);
                    }
                    classIds.add(classId);
                    classHeaderList.add(className);
                }

                // 如果此路径(NodePath)还没有对应的Sheet页
                if (!nodePathSheetMap.containsKey(rltRulePathDate.getNodePath())) {
                    Integer index = nodePathSheetMap.size() + 1;
                    nodePathSheetMap.put(rltRulePathDate.getNodePath(), index);
                    pathHeadersMap.put(index, classHeaderList);
                }

                // 如果此路径(NodePath)还没有对应的Sheet页
                if (!nodePathHeaderMap.containsKey(rltRulePathDate.getNodePath())) {
                    nodePathHeaderMap.put(rltRulePathDate.getNodePath(), headerList);
                }

                for (List<Object> ruleData : ruleDatas) {
                    Map<String, Object> attrsMap = new HashMap<>();
                    for (int i = 0; i < ruleData.size(); i++) {
                        String attrKey = headerList.get(i).get("attrKey");
                        Integer proTypeValue = Integer.parseInt(headerList.get(i).get("proType"));
                        PropertyType proType = PropertyType.valueOf(proTypeValue);
                        if (PropertyType.DATE.equals(proType)) { //日期
                            String constraintRule = headerList.get(i).get("constraintRule");
                            List<String> sdfList = new ArrayList<String>();
                            if (constraintRule == null || "".equals(constraintRule.trim())) {
                                sdfList.addAll(CheckAttrUtil.getDefaultRuleByType(PropertyType.DATE.getValue()));
                            } else {
                                sdfList.add(constraintRule.trim());
                            }
                            String val = null;
                            for (String sdfFormat : sdfList) {
                                try {
                                    SimpleDateFormat sdf = new SimpleDateFormat(sdfFormat);
                                    val = sdf.format(new Date((Long) ruleData.get(i)));
                                    break;
                                } catch (Exception e) {
                                }
                            }
                            attrsMap.put(attrKey, val);
                        } else { //整数、小数、string
                            attrsMap.put(attrKey, ruleData.get(i));
                        }
                    }
                    DataSetExeResultSheet retSheet = new DataSetExeResultSheet();
                    Long differ = System.nanoTime() - nano;
                    Long mills = differ / 1000 / 1000;
                    String nn = (differ % (1000 * 1000)) + "";
                    while (nn.length() < 6) {
                        nn = "0" + nn;
                    }
                    retSheet.setId(((now + mills) + "") + nn);
                    retSheet.setDataSetId(1L);
                    retSheet.setStartCiId(friendInfoMapEntry.getKey());
                    retSheet.setAttrs(attrsMap);
                    retSheet.setSheetName(rltRulePathDate.getNodePath());
                    retSheet.setSheetId("Path" + (nodePathSheetMap.get(rltRulePathDate.getNodePath())));
                    retSheet.setClassIds(classIds);
                    retSheet.setCreateTime(0L);
                    retSheet.setHeaders(nodePathHeaderMap.get(rltRulePathDate.getNodePath()));

                    List<DataSetExeResultSheet> resultSheetDataList = resultSheetDataListMap.get(retSheet.getSheetId());
                    if (resultSheetDataList == null) {
                        resultSheetDataList = new ArrayList<>();
                        resultSheetDataListMap.put(retSheet.getSheetId(), resultSheetDataList);
                    }
                    resultSheetDataList.add(retSheet);
                }
            }
        }

        List<DataSetExeResultSheetPage> ret = new ArrayList<>();
        for (int i = 1; i <= nodePathSheetMap.size(); i++) {
            String name = "Path" + i;
            if (resultSheetDataListMap.containsKey(name)) {
                List<Map<String, String>> headers = new ArrayList<>();
                List<Map<String, Object>> dataList = new ArrayList<>();
                List<DataSetExeResultSheet> resultSheetDataList = resultSheetDataListMap.get(name);
                if (resultSheetDataList.size() > 0) {
                    headers = resultSheetDataList.get(0).getHeaders();
                    List<String> pathList = pathHeadersMap.get(i);
                    resultSheetDataList.forEach(dataSetExeResultSheet -> dataList.add(dataSetExeResultSheet.getAttrs()));
                    DataSetExeResultSheetPage sheet = new DataSetExeResultSheetPage();
                    sheet.setName(name);
                    sheet.setPath(pathList);
                    sheet.setHeaders(headers);
                    sheet.setData(dataList);
                    ret.add(sheet);
                }
            }
        }
        return ret;
    }

    @Override
    public List<DataSetExeResultSheetPage> findDataSetRuleList(JSONObject rule, Integer limit) {
        Long dataSetId = rule.getLong("dataSetId");
        Long domainId = rule.getLong("domainId");
        String loginCode = rule.getString("loginCode");
        List<Map<String, Object>> dataSetSheets = getDataSetSheets(dataSetId);
        if (!CollectionUtils.isEmpty(dataSetSheets)) {
            List<DataSetExeResultSheetPage> dataSetList = new ArrayList<>();
            for (int i = 0; i < dataSetSheets.size(); i++) {
                Map<String, Object> dataSetMap = dataSetSheets.get(i);
                if (dataSetMap != null && !StringUtils.isEmpty(String.valueOf(dataSetMap.get("name")))) {
                    String path = String.valueOf(dataSetMap.get("name"));
                    DataSetExeResultSheetPage dataSetExeResultSheetPage = queryDataSetResultBySheet(domainId, dataSetId, path, 1, limit, null, false, new JSONArray(), loginCode);
                    if (dataSetExeResultSheetPage != null) {
                        List<String> paths = (List<String>) dataSetMap.get("path");
                        if (!CollectionUtils.isEmpty(paths)) {
                            dataSetExeResultSheetPage.setPath(paths);
                        }
                        dataSetList.add(dataSetExeResultSheetPage);
                    }
                }
            }
            return dataSetList;
        }
        return Collections.emptyList();
    }

    @Override
    public Resource downloadSheetData(Long domainId, Long dataSetId, String sheetId, String sortCol, boolean isDesc, JSONArray condition) {
        int pageSize = 20000;
        DataSetExeResultSheetPage retPage = queryDataSetResultBySheet(domainId, dataSetId, sheetId, 1, pageSize,
                sortCol, isDesc, condition, null);

        //todo：没有临时文件目录
        File dir = new File("downloadTmp");
        log.info("文件路径为：{}", dir.getPath());
        dir.mkdirs();
        File export = new File(dir, System.currentTimeMillis() + "" + new Double(Math.random() * 10000).intValue());
        export.mkdirs();
        writeExcel(export, retPage, 1);
        // 分批查询并导出
        if (retPage.getTotalCount() > pageSize) {
            long totalPage = retPage.getTotalCount() / pageSize;
            for (int pageNum = 2; pageNum <= totalPage + 1; pageNum++) {
                DataSetExeResultSheetPage exeResultSheetPage = queryDataSetResultBySheet(domainId, dataSetId, sheetId, pageNum, pageSize,
                        sortCol, isDesc, condition, null);
                if (exeResultSheetPage.getData().size() > 0) {
                    writeExcel(export, exeResultSheetPage, pageNum);
                }
            }

        }

        if (export.isDirectory() && export.listFiles().length == 1) {
            return new FileResource(export.listFiles()[0]);
        } else if (export.listFiles().length == 0) {
//            Relational data does not exist
            throw MessageException.i18n("BS_RULE_DATA_NOTEXIST");
        } else {
            Compression.compressZip(new File(export.getPath()), new File(export.getPath() + ".zip"));
            return new FileResource(new File(export.getPath() + ".zip"));
        }
    }

    /**
     * 数据写入Excel文件
     *
     * @param export    父目录
     * @param retPage   需要下载的数据
     * @param fileIndex 文件名
     */
    private void writeExcel(File export, DataSetExeResultSheetPage retPage, int fileIndex) {
        FileOutputStream fos = null;
        SXSSFWorkbook swb = new SXSSFWorkbook(300);
        CellStyle cellStyle = swb.createCellStyle();
        cellStyle.setWrapText(true);
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        try {
            Map<Integer, Integer> headerMaxWidth = new HashMap<>();
            File file = new File(export, "report" + fileIndex + CommUtil.EXCEL07_XLSX_EXTENSION);
            fos = new FileOutputStream(file);
            List<Map<String, String>> headers = retPage.getHeaders();
            SXSSFSheet sheet = swb.createSheet("sheet1");

            SXSSFRow labelRow = sheet.createRow(0);
            int columnIndex = 0;
            // 表头
            for (int i = 0; i < headers.size(); i++) {
                Map<String, String> header = retPage.getHeaders().get(i);
                SXSSFCell labelCell = labelRow.createCell(columnIndex);
                labelCell.setCellStyle(cellStyle);
                labelCell.setCellValue(header.get("attrName"));
                headerMaxWidth.put(i, header.get("attrName").getBytes().length);
                columnIndex++;
            }

            //从第二行开始写入数据
            int rowIndex = 1;
            List<Map<String, Object>> dataList = retPage.getData();

            for (Map<String, Object> itemData : dataList) {
                SXSSFRow row = sheet.createRow(rowIndex);
                for (int j = 0; j < headers.size(); j++) {
                    SXSSFCell cell = row.createCell(j);
                    cell.setCellStyle(cellStyle);
                    Object value = itemData.get(headers.get(j).get("attrKey"));
                    // 取到表头对应的数据
                    int length = 0;
                    if (value != null) {
                        cell.setCellValue(value.toString());
                        length = value.toString().getBytes().length;
                    }

                    if (headerMaxWidth.get(j) < length) {
                        headerMaxWidth.put(j, length);
                    }
                }
                rowIndex++;
            }

            for (int j = 0; j < headerMaxWidth.size(); j++) {
                if (headerMaxWidth.get(j) < 80) {
                    sheet.setColumnWidth(j, headerMaxWidth.get(j) * 256 + 256);
                } else {
                    sheet.setColumnWidth(j, 80 * 256 + 256);
                }
            }

        } catch (Exception e) {
            log.error("", e);
            throw BinaryUtils.transException(e, ServiceException.class);
        } finally {
            if (swb != null) {
                try {
                    swb.write(fos);
                    swb.close();
                } catch (IOException e) {
                    throw BinaryUtils.transException(e, ServiceException.class);
                }
            }
            if (fos != null) {
                try {
                    fos.close();
                } catch (IOException e) {
                    throw BinaryUtils.transException(e, ServiceException.class);
                }
            }
        }
    }

    @Override
    public int isTaskRunning(Long dataSetId) {
        // 如果有任务锁，说明批处理任务还未结束
        if (taskLockSvc.isLocked("DataSetTask_" + dataSetId)) {
            return 1;
        } else {
            return 0;
        }
    }

    @Override
    public String getCode(String jsonStr) throws Exception {
        try {
            JSONObject json = JSONObject.parseObject(jsonStr);
            Integer codeLang = null;
            if (json.containsKey("codeLang") && json.getInteger("codeLang") != null) {
                codeLang = json.getInteger("codeLang");
                if (codeLang < 0 || codeLang > 3) {
                    throw new RuntimeException("参数有误");
                }
            } else {
                throw new RuntimeException("参数有误");
            }
            String url = null;
            if (json.containsKey("url") && json.getString("url") != null && !json.getString("url").trim().equals("")) {
                url = json.getString("url").trim();
            } else {
                throw new RuntimeException("参数有误");
            }
            if (json.containsKey("type") && json.getInteger("type") != null) {
                int type = json.getInteger("type");

                JSONObject params = new JSONObject();
                params.put("username", json.getString("username") == null ? "请输入用户名" : json.getString("username"));
                params.put("password", json.getString("password") == null ? "请输入密码" : json.getString("password"));

                // 规则类型
                params.put("type", type);

                if (DataSetMallApiType.RelationRule.getCode().equals(type)) {
                    if (url.contains("realTimeExecute")) {
                        params.put("enter", Arrays.asList("ci1业务主键", "ci2业务主键"));
                    } else {
                        params.put("pageNum", json.getString("pageNum") == null ? 1 : json.getString("pageNum"));
                        params.put("pageSize", json.getString("pageSize") == null ? 10 : json.getString("pageSize"));
                    }
                    // 数据集名称
                    params.put("dataSetName", json.getString("dataSetName"));
                } else if (DataSetMallApiType.CiClass.getCode().equals(type)) {
                    if (url.contains("realTimeExecute")) {
                        params.put("ci", Arrays.asList("ci1业务主键", "ci2业务主键"));
                    } else {
                        params.put("pageNum", json.getString("pageNum") == null ? 1 : json.getString("pageNum"));
                        params.put("pageSize", json.getString("pageSize") == null ? 10 : json.getString("pageSize"));
                        params.put("ciClassNames", Collections.singletonList("请输入分类名称"));
                    }
                } else if (DataSetMallApiType.RelClass.getCode().equals(type)) {
                    if (url.contains("realTimeExecute")) {
                        params.put("source", Collections.singletonList("请输入源CI业务主键"));
                        params.put("target", Collections.singletonList("请输入目标CI业务主键"));
                    } else {
                        params.put("pageNum", json.getString("pageNum") == null ? 1 : json.getString("pageNum"));
                        params.put("pageSize", json.getString("pageSize") == null ? 10 : json.getString("pageSize"));
                        params.put("relClassNames", json.getString("relClassNames") == null
                                ? Arrays.asList("请输入分类名称1", "请输入分类名称2") : json.getJSONArray("relClassNames"));
                    }
                } else if (DataSetMallApiType.UpDownNFloor.getCode().equals(type)) {
                    if (url.contains("realTimeExecute")) {
                        params.put("upLevel", json.getString("upLevel") == null ? 1 : json.getInteger("upLevel"));
                        params.put("downLevel", json.getString("downLevel") == null ? 1 : json.getInteger("downLevel"));
                        params.put("startCiId", json.getLong("startCiId"));
                    } else {
                        params.put("upLevel", json.getString("upLevel") == null ? 1 : json.getInteger("upLevel"));
                        params.put("downLevel", json.getString("downLevel") == null ? 1 : json.getInteger("downLevel"));
                        params.put("startCiId", json.getLong("startCiId"));
                    }
                } else if (DataSetMallApiType.Metrics.getCode().equals(type)) {
                    params.put("metricTagName", json.getString("metricTagName") == null ? "请输入指标标签名称" : json.getString("metricTagName"));
                    params.put("metricTagVals", json.getJSONArray("metricTagVals") == null ? JSONArray.parse("[\"请输入指标标签值\"]") : json.getJSONArray("metricTagVals"));
                    params.put("timeFlag", json.getString("timeFlag") == null ? "请输入时间范围" : json.getString("timeFlag"));
                    params.put("aggFlag", json.getString("aggFlag") == null ? "请输入聚合方式" : json.getString("aggFlag"));
                    params.put("interval", json.getString("interval") == null ? "请输入聚合间隔" : json.getString("interval"));
                }
                if (codeLang == 0) {
                    return AppMallCode.getCurlCode(url, params.toJSONString(), null);
                } else if (codeLang == 1) {
                    return AppMallCode.getJavascriptCode(url, params.toJSONString(), null);
                } else if (codeLang == 2) {
                    return AppMallCode.getJavaCode(url, params.toJSONString(), null);
                } else {
                    return AppMallCode.getGolangCode(url, params.toJSONString(), null);
                }
            } else {
                throw new RuntimeException("CMV_APPMALLAPI_TYPE_WRONG");
            }
        } catch (RuntimeException e) {
            log.error("", e);
            throw e;
        }
    }


    @Override
    public void checkOperate(String userCode, DataSetMallApi dataSetMallApi) {
        JSONObject dataSet = esDataSetSvc.getById(dataSetMallApi.getId());
        Assert.notNull(dataSet, "DCV_BS_OBJ_DATASET_NOT_EXIST");
        dataSetMallApi.setCreater(dataSet.getString("creater"));
        dataSetMallApi.setCreateTime(dataSet.getLong("createTime"));
        if (!userCode.equals(dataSetMallApi.getCreater())) {
            //不是本人创建，判断是否又协作权限
            BoolQueryBuilder coordinationQuery = QueryBuilders.boolQuery();
            coordinationQuery.must(QueryBuilders.termQuery("coordinationUserCode.keyword", userCode));
            coordinationQuery.must(QueryBuilders.termQuery("dataSetMallApiId.keyword", dataSetMallApi.getId()));
            long count = esDataSetCoordinationSvc.countByCondition(coordinationQuery);
            if (count <= 0) {
                // 非管理员 且 没有协作权限 则无权操作
                if (!isAdmin(SysUtil.getCurrentUserInfo().getLoginCode())) {
                    throw new RuntimeException("暂无权限");
                }
            }
        }
    }

    /**
     *  判断登录用户是否为admin角色
     * @return
     */
    private Boolean isAdmin(String loginCode) {
        if (BinaryUtils.isEmpty(loginCode)) {
            loginCode = SysUtil.getCurrentUserInfo().getLoginCode();
        }
        UserInfo userInfo = userSvc.getUserInfoByLoginCode(loginCode);
        List<String> roleList = userInfo.getRoles().stream().map(SysRole::getRoleName).collect(Collectors.toList());
        if (!roleList.contains(ROLE_ADMIN)) {
            return Boolean.FALSE;
        } else {
            return Boolean.TRUE;
        }
    }

    /**
     *  补充用户信息
     * @param data
     * @return
     */
    private void fillUserInfo(List<JSONObject> data) {
        List<String> createrCodeList = data.stream()
                .filter(e -> !BinaryUtils.isEmpty(e.getString("creater")))
                .map(e -> e.getString("creater"))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(createrCodeList)) {
            return;
        }
        CSysUser cdt = new CSysUser();
        cdt.setLoginCodes(createrCodeList.toArray(new String[0]));
        List<SysUser> userInfoList = userSvc.getSysUserByCdt(cdt);
        Map<String, SysUser> userMap = userInfoList.stream().collect(Collectors.toMap(SysUser::getLoginCode, e -> e, (k1, v2) -> k1));
        for (JSONObject dataSet : data) {
            String creater = dataSet.getString("creater");
            if (BinaryUtils.isEmpty(creater)) {
                dataSet.put("createrName", "-");
                continue;
            }
            SysUser user = userMap.get(creater);
            if (user == null) {
                dataSet.put("createrName", "-");
                continue;
            }
            dataSet.put("createrName", user.getUserName());
        }
    }

    @Override
    public List<Map<String, Object>> groupDataSetMallApiLogCount(Long domainId) {
        List<JSONObject> dataSetList = esDataSetSvc.getListByQuery(QueryBuilders.termQuery("domainId", domainId));
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        Long span = System.currentTimeMillis() - 86400000L * 30L;
        query.must(QueryBuilders.rangeQuery("createTime").gt(span));
        Map<String, Long> statistics = esDataSetMallApiLogSvc.groupByCountField("dataSetMallApiId", query);
        List<Map<String, Object>> ret = new ArrayList<Map<String, Object>>();
        for (JSONObject dataSetObj : dataSetList) {
            Map<String, Object> obj = new HashMap<String, Object>();
            obj.put("id", dataSetObj.getLong("id"));
            obj.put("name", dataSetObj.getString("name"));
            Long count = 0L;
            if (statistics.get(dataSetObj.getLong("id") + "") != null) {
                count = statistics.get(dataSetObj.getLong("id") + "");
            }
            obj.put("count", count);
            ret.add(obj);
        }
        while (true) {
            boolean breakable = true;
            for (int i = 0; i < ret.size() - 1; i++) {
                Map<String, Object> less = ret.get(i);
                Map<String, Object> more = ret.get(i + 1);
                if ((Long) less.get("count") < (Long) more.get("count")) {
                    ret.set(i, more);
                    ret.set(i + 1, less);
                    breakable = false;
                }
            }
            if (breakable) {
                break;
            }
        }
        return ret;
    }

    @Override
    public List<String> getTpMetricLabelDTOList(TpRuleReqDTO ruleReqDTO) {
        Assert.notNull(ruleReqDTO.getClassId(), "X_PARAM_NOT_NULL${name:classId}");
        ruleReqDTO.setRuleType(TpRuleTypeEnum.LABEL_RICH);
        List<TpRuleDTO> tpRules = tpRuleSvc.getTpRuleList(ruleReqDTO);
        return tpRules.stream().distinct().filter(rule -> !BinaryUtils.isEmpty(rule.getLabelMappings())).flatMap(rule -> rule.getLabelMappings().stream().map(LabelMappingDTO::getTargetName))
                .collect(Collectors.toList());
    }

    @Override
    public List<String> queryMetricAttrValue(MetricAttrValQueryDTO query) {
        return metricDataSvc.queryAttrValue(query);
    }

    @Override
    public FriendBatchInfo queryFriendByStartCiIds(List<FriendInfoRequestDto> body) {
        SysUser user = new UserInfo();
        try {
            user = SysUtil.getCurrentUserInfo();
        } catch (LoginException e) {
            user.setDomainId(1L);
            user.setLoginCode("system");
        }
        FriendBatchInfo result = new FriendBatchInfo();
        List<Long> dataSetIds = body.stream().map(FriendInfoRequestDto::getId).distinct().collect(Collectors.toList());
        List<Long> startCiIds = body.stream().map(FriendInfoRequestDto::getStartCiId).distinct().collect(Collectors.toList());
        if(BinaryUtils.isEmpty(dataSetIds) || BinaryUtils.isEmpty(startCiIds)){
            return result;
        }
        CCcCi cdt = new CCcCi();
        cdt.setIds(startCiIds.toArray(new Long[]{}));
        List<CcCiInfo> ciInfos = iciSvc.queryCiInfoList(user.getDomainId(), cdt, null, true, false);
        Map<Long, Set<Long>> dataSetIdGroup = body.stream().collect(Collectors.groupingBy(FriendInfoRequestDto::getId, Collectors.mapping(FriendInfoRequestDto::getStartCiId, Collectors.toSet())));
        try {
            List<FriendInfo> friendInfos = new ArrayList<>();
            log.info("开始执行数据集,入参为：dataSetId={} , startCiId={}", dataSetIds, startCiIds);
            List<JSONObject> dataSetList = findDataSetByIds(dataSetIds, true);
            //数据超市id与ci对象id集合map
            Map<Long, List<Long>> rootIdCiIdMap = new TreeMap<>(new Comparator<Long>() {
                @Override
                public int compare(Long o1, Long o2) {
                    if(startCiIds.indexOf(o1)>startCiIds.indexOf(o2)){
                        return 1;
                    }
                    return -1;
                }
            });
            for (JSONObject dataSet : dataSetList) {
                DataSetMallApi dataSetMallApi = getDataSetMallApi(dataSet);
                log.info("第一步获取数据集：dataSet={}", dataSet);
                DataSetMallApiRelationRule dataSetMallRelationApi = (DataSetMallApiRelationRule) dataSetMallApi;
                Set<Long> rootCiIds = dataSetIdGroup.get(dataSetMallRelationApi.getId());
                List<CcCiInfo> currentList = ciInfos.stream().filter(each -> rootCiIds.contains(each.getCi().getId())).collect(Collectors.toList());
                log.info("第二步获取CI对象：ciInfos={}", ciInfos);
                Map<Long, FriendInfo> friendInfoMap = relationRuleAnalysisSvc.queryCiFriendByCiIdsAndRule(currentList, dataSetMallRelationApi, true);
                log.info("第三步获取结果数据friendInfo：friendInfo={}", friendInfoMap);
                friendInfoMap.forEach((rootId, friendInfo) -> {
                    friendInfos.add(friendInfo);
                    List<Long> ids = friendInfo.getCiNodes().stream().map(each -> each.getCi().getId()).collect(Collectors.toList());
                    rootIdCiIdMap.put(rootId, ids);
                });
            }
            //去重ciClass
            Map<Long, CcCiClassInfo> distinctClassMap = new HashMap<>();
            List<CcCiClassInfo> ciClassList = friendInfos.stream().map(FriendInfo::getCiClassInfos).flatMap(u -> u.stream())
                                                .filter(a -> distinctClassMap.put(a.getCiClass().getId(), a)==null).collect(Collectors.toList());
            result.setCiClassInfos(ciClassList);

            List<CcCiInfo> ciNodes = friendInfos.stream().map(FriendInfo::getCiNodes).flatMap(u -> u.stream()).collect(Collectors.toList());
            //去重Node节点
            Map<Long, CcCiFriendInfo> distinctCiMap = new HashMap<>();
            for (CcCiInfo ciNode : ciNodes) {
                Long id = ciNode.getCi().getId();
                CcCiFriendInfo copy = CommUtil.copy(ciNode, CcCiFriendInfo.class);
                copy.setRepeat(false);
                if(distinctCiMap.get(id) != null){
                    //重复的打标记
                    copy.setRepeat(true);
                }
                rootIdCiIdMap.forEach((rootId, ciIds) -> {
                    if(ciIds.contains(id)){
                        copy.getStartCiIds().add(rootId);
                    }
                });
                distinctCiMap.put(id, copy);
            }
            result.setCiNodes(new ArrayList<>(distinctCiMap.values()));

            //去重关系线
            List<ESCIRltInfo> rltLines = friendInfos.stream().map(FriendInfo::getCiRltLines).flatMap(u -> u.stream()).collect(Collectors.toList());
            Map<Long, CiRltFriendInfo> distinctRltMap = new HashMap<>();
            for (ESCIRltInfo rltLine : rltLines) {
                Long id = rltLine.getId();
                CiRltFriendInfo copy = CommUtil.copy(rltLine, CiRltFriendInfo.class);
                copy.setRepeat(false);
                if(distinctRltMap.get(id) != null){
                    //重复的打标记
                    copy.setRepeat(true);
                }
                distinctRltMap.put(id, copy);
            }
            result.setCiRltLines(new ArrayList<>(distinctRltMap.values()));
            //将ciClass放入对应ci对象中
            Map<Long, CcCiClass> classMap = result.getCiClassInfos().stream().map(CcCiClassInfo::getCiClass).collect(Collectors.toMap(CcCiClass::getId, each -> each, (key1, key2) -> key2));
            for(CcCiInfo ciInfo : result.getCiNodes()){
                Long classId = ciInfo.getCi().getClassId();
                CcCiClass ciClass = classMap.get(classId);
                if(!BinaryUtils.isEmpty(ciClass)){
                    ciInfo.setCiClass(ciClass);
                }
            }
        } catch (Exception e) {
            log.error("", e);
            throw new RuntimeException(e.getMessage());
        }
        return result;
    }

    @Override
    public String updateThumbnail(DataSetThumbnailDTO body) {
        JSONObject dataSetObj = esDataSetSvc.getById(body.getDataSetId());
        Assert.notNull(dataSetObj, "数据集不存在");
        DataSetMallApi dataSet = getDataSetMallApi(dataSetObj);
        String result;
        if(BinaryUtils.isEmpty(dataSet.getThumbnail())){
            result = FileUtil.decodeBase64(UUID.randomUUID() + ".png", body.getContent(), true);
            dataSet.setThumbnail(result);
            esDataSetSvc.saveOrUpdate(dataSet.toJson());
        }else{
            result = FileUtil.decodeBase64(dataSet.getThumbnail(), body.getContent(), false);
        }
        return httpResourceUrl + result;
    }

    @Override
    public List<DataSetPathInfoVo> getPathInfo(List<Long> dataSetIds) {
        List<JSONObject> dataSetObjList = esDataSetSvc.getListByQuery(QueryBuilders.termsQuery("id", dataSetIds));
        Assert.notNull(dataSetObjList, "数据集不存在");
        List<DataSetPathInfoVo> result = new ArrayList<>();
        for (JSONObject each : dataSetObjList) {
            DataSetMallApi dataSet = getDataSetMallApi(each);
            DataSetPathInfoVo info = new DataSetPathInfoVo();
            info.setDataSetId(dataSet.getId());
            if(!BinaryUtils.isEmpty(dataSet.getThumbnail())){
                info.setIcon(httpResourceUrl + dataSet.getThumbnail());
            }
            info.setName(dataSet.getName());
            List<Map<String, Object>> pathList = this.getDataSetSheets(dataSet.getId());
            if(!CollectionUtils.isEmpty(pathList)){
                for (int i = 0; i < pathList.size(); i++) {
                    Map<String, Object> path = pathList.get(i);
                    path.put("name", "路径" + (i + 1));
                }
            }
            info.setPathInfo(pathList);
            result.add(info);
        }
        return result;
    }

    @Override
    public void updateDataSetById(Long id) {
        JSONObject byId = esDataSetSvc.getById(id);
        LibTypeUtil.execute(() -> batchProcessSvc.processDataSetRule(new DataSetMallApiRelationRule(byId)), LibType.valueOf(libType));
    }

    @Override
    public List<DataSetTableResult> queryCiTableList(QueryDataTableDTO queryDataTableDTO) {
        if (BinaryUtils.isEmpty(queryDataTableDTO.getDataSetId())) {
            throw new ServiceException("数据集不能为空！");
        }
        if (BinaryUtils.isEmpty(queryDataTableDTO.getAttrType())) {
            throw new ServiceException("属性值不能为空！");
        }
        if (BinaryUtils.isEmpty(queryDataTableDTO.getHeadList())) {
            throw new ServiceException("表头不能为空！");
        }
        if (BinaryUtils.isEmpty(queryDataTableDTO.getClassId())) {
            throw new ServiceException("第一个子节点不能为空！");
        }
        final String attrType = queryDataTableDTO.getAttrType();
        final List<String> headList = queryDataTableDTO.getHeadList();
        final Long classId = queryDataTableDTO.getClassId();
        List<String> contentList = queryDataTableDTO.getContentList();

        // 查询全部数据
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("dataSetId", queryDataTableDTO.getDataSetId()));

        // 查询全部
        List<DataSetExeResult> data = new ArrayList<>();
        int pageNum = 1;
        while (true) {
            Page<DataSetExeResult> page = esDataSetExeResultSvc.getListByQuery(pageNum, 1000, query);
            data.addAll(page.getData());
            if (pageNum >= page.getTotalPages()) {
                break;
            } else {
                pageNum++;
            }
        }
        List<ESCIAttrTransConfig> attrConfigs = esciAttrTransConfigSvc.getListByQuery(QueryBuilders.termQuery("classId", classId));
        List<ESCIAttrTransConfig> collect = attrConfigs.stream()
                .filter(m -> m.getSourceAttrName().equals(attrType))
                .collect(Collectors.toList());
        String attrKey = !BinaryUtils.isEmpty(collect) ? collect.get(0).getTargetAttrName() : "";
        List<DataSetTableResult> resultList = new ArrayList<>();
        data.forEach(dataSetExeResult -> {
            DataSetTableResult dataSetTableResult = new DataSetTableResult();
            Map<Long, Set<Long>> ciIdMap = dataSetExeResult.getSimpleFriendInfo().getCiIdMap();
            Map.Entry<Long, Set<Long>> firstEntry = ciIdMap.entrySet().stream().skip(1).findFirst().orElse(null);
            // 第一个子节点没有值则跳过
            if (BinaryUtils.isEmpty(firstEntry) || BinaryUtils.isEmpty(firstEntry.getValue())) {
                return;
            }
            Set<Long> longs = firstEntry.getValue();
            CCcCi ciQuery = new CCcCi();
            ciQuery.setIds(longs.toArray(new Long[0]));
            List<CcCiInfo> ccCiInfos = iciSvc.queryCiInfoList(BaseConst.DEFAULT_DOMAIN_ID, ciQuery, null, true, false);
            List<CcCiInfo> collectCiList = ccCiInfos.stream()
                    .filter(ci -> ci.getCi().getClassId().equals(classId))
                    .collect(Collectors.toList());
            List<CcCiInfo> ciInfoList = new ArrayList<>();
            Map<String, String> attrValueMap = new HashMap<>();
            for (CcCiInfo ccCiInfo : collectCiList) {
                Map<String, String> attrs = ccCiInfo.getAttrs();
                String value = BinaryUtils.isEmpty(attrs.get(attrKey)) ? attrs.get(attrType) : attrs.get(attrKey);
                if (headList.contains(value)) {
                    ciInfoList.add(ccCiInfo);
                    attrValueMap.put(value, value);
                }
            }
            String ciLabel = dataSetExeResult.getStartCi().getCi().getCiLabel();
            dataSetTableResult.setStartCiName(JSONObject.parseArray(ciLabel, String.class).get(0));
            HashMap<String, String> map = new HashMap<>();
            for (String head : headList) {
                if (attrValueMap.containsKey(head)) {
                    StringBuilder stringBuilder = new StringBuilder();
                    ciInfoList.forEach(ciInfo -> {
                        Map<String, String> attrs = ciInfo.getAttrs();
                        String value = BinaryUtils.isEmpty(attrs.get(attrKey)) ? attrs.get(attrType) : attrs.get(attrKey);
                        if (value.equals(head)) {
                            List<String> valueList = contentList.stream()
                                    .filter(content -> !BinaryUtils.isEmpty(ciInfo.getAttrs().get(content)))
                                    .map(content -> ciInfo.getAttrs().get(content))
                                    .collect(Collectors.toList());
                            String join = String.join(" ", valueList);
                            stringBuilder.append(join).append(",");
                        }
                    });
                    String value = stringBuilder.length() > 0 ? stringBuilder.substring(0, stringBuilder.length() - 1) : "";
                    map.put(head, value);
                }
            }
            dataSetTableResult.setDataMap(map);
            synchronized (resultList) {
                resultList.add(dataSetTableResult);
            }
        });
        return resultList;
    }
    private CCcCi createCiQuery(Set<Long> allCiIds) {
        CCcCi ciQuery = new CCcCi();
        ciQuery.setIds(allCiIds.toArray(new Long[0]));
        return ciQuery;
    }
    @Override
    public ResponseEntity<byte[]> ciTableListExport(List<DataSetTableResult> ret, QueryDataTableDTO body) {//
        // 创建临时文件
        File tempFile = null;
        ResponseEntity<byte[]> responseEntity = null;
        try {
            String cardName = body.getCardName();
            List<String> headList = body.getHeadList();
            tempFile = new File(cardName + ".xlsx");
            ExcelWriter excelWriter = EasyExcel.write(tempFile)
                    .build();
            List<List<Object>> dataRows = new ArrayList<>();
            for (DataSetTableResult result : ret) {
                List<Object> dataRow = new ArrayList<>();
                Map<String, String> dataMap = result.getDataMap();
                dataRow.add(result.getStartCiName());
                headList.forEach(head -> dataRow.add(BinaryUtils.isEmpty(dataMap.get(head)) ? "/" : dataMap.get(head)));
                dataRows.add(dataRow);
            }
            headList.add(0, body.getRootName());
            List<List<String>> columnHead = new ArrayList<>();
            for (int i = 0; i < headList.size(); i++) {
                List<String> ss = new ArrayList<>();
                String s = headList.get(i);
                if (i == 0) {
                    ss.add(cardName + "相关信息数据表");
                    ss.add(s);
                } else {
                    ss.add(" ");
                    ss.add(s);
                }
                columnHead.add(ss);
            }
            WriteSheet writeSheet = EasyExcel.writerSheet().sheetName("sheet1")
                    .head(columnHead)
                    .registerWriteHandler(new TitleMergeStrategy(headList.size()))
                    .build();
            excelWriter.write(dataRows, writeSheet);
            excelWriter.finish();
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            if (tempFile != null && tempFile.exists()) {
                try {
                    // 删除临时文件
                    responseEntity = returnRes(tempFile);
                    Files.delete(tempFile.toPath());
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }
        }
        return responseEntity;
    }
    private  ResponseEntity<byte[]> returnRes(File file) {
        HttpHeaders headers = new HttpHeaders();
        InputStream inputStream = null;
        try {
            inputStream = Files.newInputStream(file.toPath());
            byte[] bytes = new byte[inputStream.available()];
            int read = inputStream.read(bytes);
            headers.add("Content-Disposition", "attachment;filename=" + URLEncoder.encode(file.getName(), "UTF-8"));
            return new ResponseEntity<>(bytes, headers, HttpStatus.OK);
        } catch (Exception e) {
            throw new MessageException(e.getMessage());
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (Exception e) {
                    log.error(e.getMessage(),e);
                }
            }
        }
    }
    public static class TitleMergeStrategy implements CellWriteHandler {
        private final int colSpan;
        public TitleMergeStrategy(int colSpan) {
            this.colSpan = colSpan;
        }
        @Override
        public void afterCellDataConverted(CellWriteHandlerContext context) {
        }
        @Override
        public void afterCellDispose(CellWriteHandlerContext context) {
            WriteCellData<?> cellData = context.getFirstCellData();
            Sheet sheet = context.getWriteSheetHolder().getSheet();
            Cell cell = context.getCell();
            Workbook workbook = sheet.getWorkbook();
            // 处理首行
            if (cell.getRowIndex() == 0 || cell.getRowIndex() == 1) {
                CellStyle writeCellStyle = workbook.createCellStyle();
                // 字体
                Font font = workbook.createFont();
                // 背景颜色
                writeCellStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
                // 字体大小
                font.setFontHeightInPoints((short) 16);
                // 字体加粗
                font.setBold(true);
                writeCellStyle.setFont(font);
                // 列宽，行高
                sheet.setColumnWidth(cell.getColumnIndex(), 4000);
                sheet.setDefaultRowHeight((short) 400);
                // 应用样式到当前单元格
                cell.setCellStyle(writeCellStyle);
                if (cell.getRowIndex() == 0) {
                    sheet.addMergedRegionUnsafe(new CellRangeAddress(0, 0, 0, colSpan - 1));
                    // 设置左对齐
                    Font font1 = workbook.createFont();
                    // 字体大小
                    font1.setFontHeightInPoints((short) 16);
                    // 字体加粗
                    font1.setBold(true);
                    CellStyle style = workbook.createCellStyle();
                    style.setFont(font1);
                    style.setAlignment(HorizontalAlignment.LEFT);
                    style.setFillForegroundColor(IndexedColors.WHITE.getIndex());
                    sheet.setColumnWidth(cell.getColumnIndex(), 4000);
                    // 应用样式到合并后的所有单元格
                    for (int i = 0; i <= colSpan - 1; i++) {
                        Cell mergedCell = sheet.getRow(0).getCell(i);
                        if (mergedCell != null) {
                            mergedCell.setCellStyle(style);
                        }
                    }
                }
            }
        }
    }

}
