package com.uinnova.product.vmdb.comm.util;

import com.binary.framework.exception.ServiceException;

/**
 * 
 * <AUTHOR>
 *
 */
public enum RuleOp {

    /**
     * 等于
     */
    Equal(1),

    /**
     * 不等于
     */
    NotEqual(2),

    /**
     * 小于
     */
    LT(3),

    /**
     * 小于或等于
     */
    LTEqual(4),

    /**
     * 大于
     */
    GT(5),

    /**
     * 大于或等于
     */
    GTEqual(6),

    /**
     * 模糊匹配...
     */
    Like(7),

    /**
     * 非模糊匹配...
     */
    NotLike(8),

    /**
     * 包含...
     */
    In(9),

    /**
     * 不包含...
     */
    NotIn(10);

    private final int value;

    RuleOp(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public String getOperator() {
        switch (this) {
        case Equal:
            return "=";
        case NotEqual:
            return "<>";
        case LT:
            return "<";
        case LTEqual:
            return "<=";
        case GT:
            return ">";
        case GTEqual:
            return ">=";
        case Like:
            return " like ";
        case NotLike:
            return " not like ";
        case In:
            return " in ";
        case NotIn:
            return " not in ";
        default:
            throw new ServiceException(" is wrong type:'" + this + "'! ");
        }
    }

    public String parseExpValue(String v, PropertyType type) {
        switch (this) {
        case Equal:
        case NotEqual:
        case LT:
        case LTEqual:
        case GT:
        case GTEqual: {
            if (type == PropertyType.INTEGER || type == PropertyType.DOUBLE) {
                return v;
            } else {
                return "'" + v + "'";
            }
        }
        case Like:
        case NotLike: {
            if (type == PropertyType.INTEGER || type == PropertyType.DOUBLE) {
                throw new ServiceException(" the property's type [" + type.name() + "] not support '" + this.name() + "'! ");
            }
            return "'" + v + "'";
        }
        case In:
        case NotIn: {
            String s = "(";
            String[] arr = v.split("[,]");
            for (int i = 0; i < arr.length; i++) {
                if ((arr[i] = arr[i].trim()).length() == 0) {
                    continue;
                }
                if (i > 0) {
                    s += ",";
                }
                s += ((type == PropertyType.INTEGER || type == PropertyType.DOUBLE) ? arr[i] : ("'" + arr[i] + "'"));
            }
            s += ")";
            return s;
        }
        default:
            throw new ServiceException(" is wrong type:'" + this + "'! ");
        }
    }

    public static RuleOp valueOf(int v) {
        switch (v) {
        case 1:
            return Equal;
        case 2:
            return NotEqual;
        case 3:
            return LT;
        case 4:
            return LTEqual;
        case 5:
            return GT;
        case 6:
            return GTEqual;
        case 7:
            return Like;
        case 8:
            return NotLike;
        case 9:
            return In;
        case 10:
            return NotIn;
        default:
            throw new ServiceException(" is wrong value:'" + v + "'! ");
        }
    }

}
