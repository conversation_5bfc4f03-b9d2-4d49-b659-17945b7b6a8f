package com.uino.service.util.sync;

import java.io.File;
import java.io.FileOutputStream;
import java.util.HashMap;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.logging.Logger;

import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;

import com.binary.core.io.Compression;
import com.uino.service.util.FileUtil;
import com.uino.bean.cmdb.base.ESResource;

import lombok.extern.slf4j.Slf4j;

/**
 * 资源同步处理器
 * <p>
 * 有三个核心对象1处理资源线程池2已处理资源指针3同步资源核心
 * <p>
 * 线程池使用了带阻塞方案的拒绝策略来处理
 * <p>
 * 资源指针只允许前进不允许后退，没有进行持久化使用了内存，若重启并且重复下发了同步任务thread会校对文件保证尽量不重复拉取浪费资源,
 * 现在不再去校验文件内容，只是新增任务时发现本地已经有就不再远程拉取，否则每次对比都需要拉取完文件信息才可以拉取，只降低了磁盘io并降低不了网络io;
 * 指针逻辑也对应修改，只为外部拉取数据使用，同步器内做完任务会向后指，但不会接到任务发现index小于指针就忽略任务使其走文件校对逻辑
 * <p>
 * 同步资源核心(thread)每成功一条任务会刷新指针，若失败则重新入队
 * 
 * <AUTHOR>
 *
 */
@Slf4j
public class SyncProcess {

    /**
     * 已同步的资源位置指针
     */
    private static Long syncCreateTime;

    /**
     * 已同步资源位置锁
     */
    private static Lock syncIndexLock = new ReentrantLock();

    /**
     * 发送请求util
     */
    private static RestTemplate restTemplate = new RestTemplate();

    /**
     * 本机资源根路径
     */
    private static String rootPath = null;

    /**
     * 同步资源方法
     * 
     * @param syncResource
     *            待同步资源信息
     * 
     * @param rootPath
     *            本地资源根目录eg: /usr/local/uino_data
     * 
     * @see ESResource
     */
    public static void sync(ESResource syncResource, String rootPath) {
        if (SyncProcess.rootPath == null) {
            synchronized (SyncProcess.class) {
                if (SyncProcess.rootPath == null) {
                    SyncProcess.rootPath = rootPath;
                }
            }
        }
        new SyncCore(syncResource).run();
    }

    /**
     * 获取指针
     * 
     * @return
     */
    public static Long getSyncCreateTime() {
        syncIndexLock.lock();
        try {
            return SyncProcess.syncCreateTime;
        } catch (Exception e) {
            log.error("获取同步索引位置异常");
            throw new RuntimeException("获取同步索引位置异常");
        } finally {
            syncIndexLock.unlock();
        }
    }

    /**
     * 设置指针位置，只能前进设置
     * 
     * @param createTime
     */
    public static void setSyncCreateTime(long createTime) {
        syncIndexLock.lock();
        try {
            if (SyncProcess.syncCreateTime != null && SyncProcess.syncCreateTime >= createTime) {
                return;
            } else {
                SyncProcess.syncCreateTime = createTime;
            }
        } catch (Exception e) {
            log.error("获取同步索引位置异常");
            throw new RuntimeException("获取同步索引位置异常");
        } finally {
            syncIndexLock.unlock();
        }
    }

    /**
     * 同步资源核心
     * 
     * <AUTHOR>
     *
     */
    public static class SyncCore {

        // 该条任务同步资源信息
        private ESResource syncResource;

        // 是否为重试任务
        private boolean reTry = false;

        // 重试次数
        private int tryNumber = 0;

        public SyncCore(ESResource syncResource) {
            SyncProcess.setSyncCreateTime(syncResource.getCreateTime());
            this.syncResource = syncResource;
        }

        public SyncCore(ESResource syncResource, boolean reTry, int currentTryNumber) {
            SyncProcess.setSyncCreateTime(syncResource.getCreateTime());
            this.syncResource = syncResource;
            this.reTry = reTry;
            this.tryNumber = ++currentTryNumber;
        }

        public void run() {
            // 同步资源id
            Long dataId = syncResource.getId();
            // 资源在本地需操作的真实路径
            String path = SyncProcess.rootPath + syncResource.getPath();
            // 操作文件的输出流
            FileOutputStream fileOs = null;
            try {
                // 待同步资源可访问地址
                String publicUrl = syncResource.getPublicUrl();
                // 同步操作动作类型
                Integer optionType = syncResource.getOptionType();
                // 走到这里代表确实需要传输，这个if是新增和修改动作(都视为写动作)区别只是新增动作本地已有就不覆盖
                if (optionType.intValue() == 0 || optionType.intValue() == 2) {
                    File writeFile = new File(path);
                    if (!writeFile.getParentFile().exists()) {
                        writeFile.getParentFile().mkdirs();
                    }
                    // 非重试任务新建文件动作然后发现本地对应位置已经有了就不处理了，其他的不管直接覆盖，虽然可能不一致，但是可以保证最终一致
                    if (!this.reTry && optionType.intValue() == 0 && writeFile.exists()) { return; }
                    try {
                        ResponseEntity<byte[]> res = SyncProcess.restTemplate.getForEntity(publicUrl, byte[].class,
                                new HashMap<>());
                        byte[] fileBody = res.getBody();
                        if (fileBody == null || fileBody.length <= 0) {
                            log.info("同步文件获取到了空文件【{}】【{}】", dataId, publicUrl);
                            return;
                        }
                        fileOs = new FileOutputStream(writeFile);
                        fileOs.write(fileBody);
                        // 需要解压资源操作-上文已写入单文件，根据要求去解压
                        if (syncResource.isUnzip()) {
                            File zip = new File(path);
                            File output = null;
                            // 解压到当前文件夹下zipname下(会先递归删除对应文件夹)
                            if (!syncResource.isCurrentDir()) {
                                output = new File(zip.getParent() + "/"
                                        + zip.getName().substring(0, zip.getName().indexOf(".")) + "/");
                                if (output.isDirectory()) {
                                    FileUtil.delDirAndChildFiles(output);
                                }
                                output.mkdirs();
                            } else {
                                // 解压至当前文件夹
                                output = new File(zip.getParent() + "/");
                            }
                            // 目录在上分支判断结束这里向文件夹解压文件
                            try {
                                Compression.uncompressZip(zip, output);
                            } catch (Exception e) {
                                log.error("解压文件异常【{}】【{}】", path, e);
                            }
                        }
                    } catch (HttpClientErrorException.NotFound e) {
                        log.error("待同步文件【{}】【{}】【{}】已在源服务器不存在或不可访问，不再重试处理,如发现丢失请管理员检查日志", dataId,
                                syncResource.getPath(), publicUrl);
                    }
                } else if (optionType.intValue() == 1) {
                    // 删除动作
                    try {
                        File zipOrFile = new File(path);
                        if (zipOrFile.exists() && zipOrFile.isDirectory()) {
                            // 是文件夹路径则递归删除
                            FileUtil.delDirAndChildFiles(zipOrFile);
                        } else if (syncResource.isUnzip() && !syncResource.isCurrentDir()) {
                            // 需要解压的资源并且不是解压到当前目录的资源则先删除zipname文件夹再删除zip
                            File unZipDir = new File(zipOrFile.getParent() + "/"
                                    + zipOrFile.getName().substring(0, zipOrFile.getName().indexOf(".")) + "/");
                            try {
                                FileUtil.delDirAndChildFiles(unZipDir);
                            } catch (Exception e) {
                                log.error("删除zip【{}】对应解压文件夹【{}】异常，删除任务失败不再重试处理", dataId, unZipDir.getPath());
                            }
                            zipOrFile.delete();
                        } else {
                            // 单文件删除
                            zipOrFile.delete();
                        }
                    } catch (Exception e) {
                        log.error("删除文件异常【{}】【{}】，删除任务失败不再重试处理", path, dataId);
                    }
                }
            } catch (Exception e) {
                if (this.tryNumber < 5) {
                    log.error("同步传输文件异常[" + dataId + "]，重新加入队列尝试", e);
                    new SyncCore(syncResource, true, tryNumber).run();
                } else {
                    log.error("同步传输文件异常[" + dataId + "]，已尝试传输5次，不再重试处理该任务，如需要请管理员检查日志手动同步", e);
                }
            } finally {
                try {
                    if (fileOs != null) {
                        fileOs.close();
                    }
                } catch (Exception e2) {
                    // TODO: handle exception
                }
            }
        }
    }
}
