package com.uino.api.client.cmdb.rpc;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.uino.provider.feign.cmdb.DataSetCooperationFeign;
import com.uino.api.client.cmdb.IDataSetCooperationApiSvc;

@Service
public class DataSetCooperationApiSvcRpc implements IDataSetCooperationApiSvc {

	@Autowired
	private DataSetCooperationFeign dataSetCooperationFeign;
	
	@Override
	public List<JSONObject> findByDataSetId(Long dataSetId) {
		return dataSetCooperationFeign.findByDataSetId(dataSetId);
	}

	@Override
	public boolean updateCooperation(Long dataSetId, List<JSONObject> coordinationUserCodeList) {
		JSONObject body = new JSONObject();
		body.put("dataSetId", dataSetId);
		body.put("coordinationUserCodeList", coordinationUserCodeList);
		return dataSetCooperationFeign.updateCooperation(body);
	}

}
