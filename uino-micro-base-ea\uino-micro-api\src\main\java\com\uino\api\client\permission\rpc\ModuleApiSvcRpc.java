package com.uino.api.client.permission.rpc;

import java.util.List;
import java.util.Map;
import java.util.Set;

import com.uino.dao.BaseConst;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import com.uino.bean.permission.base.SysModule;
import com.uino.bean.permission.business.ModuleNodeInfo;
import com.uino.bean.permission.query.CAuthModuleBean;
import com.uino.bean.permission.query.CSysModule;
import com.uino.provider.feign.permission.ModuleFeign;
import com.uino.api.client.permission.IModuleApiSvc;
import org.springframework.web.multipart.MultipartFile;

@Service
@Primary
public class ModuleApiSvcRpc implements IModuleApiSvc {

    @Autowired
    ModuleFeign moduleFeign;

    @Override
    public ModuleNodeInfo getModuleTree(Long userId) {
        return moduleFeign.getModuleTree(BaseConst.DEFAULT_DOMAIN_ID,userId);
    }

    @Override
    public ModuleNodeInfo getModuleTree(Long domainId, Long userId) {
        return moduleFeign.getModuleTree(domainId,userId);
    }

    @Override
    public SysModule saveModule(SysModule saveDto) {
        return moduleFeign.saveModule(saveDto);
    }

    @Override
    public void delModule(Long id) {
        moduleFeign.delModule(id);
    }

    @Override
    public void saveOrder(Map<Long, Integer> orderDict) {
        moduleFeign.saveOrder(orderDict);
    }

    @Override
    public Map<Long, SysModule> recoverModules(Set<Long> moduleIds) {
        return moduleFeign.recoverModules(moduleIds);
    }

    @Override
    public List<SysModule> getModulesByCdt(CSysModule cdt) {
        return moduleFeign.getModulesByCdt(cdt);
    }

    @Override
    public List<SysModule> getAuthModulesBySearchBean(CAuthModuleBean bean) {
        return moduleFeign.getAuthModulesBySearchBean(bean);
    }

    @Override
    public ResponseEntity<byte[]> exportModules() {
        return null;
    }

    @Override
    public void importModules(MultipartFile file) {
    }

    @Override
    public ModuleNodeInfo getModuleTreeBySign(Long domainId, Long userId, String moduleSign) {
        return null;
    }
}
