package com.uino.bean.permission.business.request;

import java.util.Set;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.util.Assert;

import com.uino.bean.permission.business.IValidDto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 向组织添加用户
 * 
 * <AUTHOR>
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value="向组织添加用户",description = "向组织添加用户")
public class AddOrRemoveUserToOrgRequestDto implements IValidDto {

	/**
	 * 用户ids
	 */
	@ApiModelProperty(value="用户id集合")
	private Set<Long> userIds;
	/**
	 * 组织id
	 */
	@ApiModelProperty(value="组织id",example = "123")
	private Long orgId;

	@ApiModelProperty(value="所属域id")
	private Long domainId;

	@Override
	public void valid() {
		Assert.notNull(orgId, "ORG_NOT_NULL");
		Assert.notEmpty(userIds, "USER_NOT_NULL");
	}

}
