package com.uinnova.product.eam.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class PlanDiagramRequest implements Serializable {

    private String diagramName;
    private String viewType;
    private Integer pageNum;
    private Integer pageSize;
    private Integer dirType;
    @JsonProperty("systemList")
    private List<String> sysCiCodeList;
    private List<String> viewTypes;
    /** 筛选条件创建者 1：我创建的 2：与我协作 3：其他 */
    private Integer owner;
    /** 筛选条件所在位置 0：设计空间  1：资产仓库 */
    private Integer location;
    /** 当前用户登录人 */
    private String currentLoginCode;
    /** 当前用户id */
    private Long currentUserId;
}
