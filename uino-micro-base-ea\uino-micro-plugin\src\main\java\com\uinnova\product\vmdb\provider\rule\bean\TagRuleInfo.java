package com.uinnova.product.vmdb.provider.rule.bean;

import com.uinnova.product.vmdb.comm.model.rule.CcCiTagRule;
import com.uinnova.product.vmdb.comm.model.rule.CcCiTagRuleItem;

import java.io.Serializable;
import java.util.List;

public class TagRuleInfo implements Serializable {
	private static final long serialVersionUID = 1L;
	
	
	
	/** 规则 **/
	private CcCiTagRule rule;
	
	
	
	/** 规则条目 **/
	private List<CcCiTagRuleItem> items;


	
	

	public CcCiTagRule getRule() {
		return rule;
	}



	public void setRule(CcCiTagRule rule) {
		this.rule = rule;
	}



	public List<CcCiTagRuleItem> getItems() {
		return items;
	}



	public void setItems(List<CcCiTagRuleItem> items) {
		this.items = items;
	}
	
	
	
	
	
	
	

}
