package com.uinnova.product.eam.base.diagram.utils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Filename: FileFilterUtil
 * @Author: WangBaoDe
 * @Data:2024/3/11 14:10
 */
public class FileFilterUtil {

    /**
     * 对文件访问路径做过滤，针对 ../ or ~/ 这种路径做替换处理
     * @param filePath
     * @return
     */
    public static String parseFilePath(String filePath){
        String regex = "[\\.]{2}/|~[/]?";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(filePath);
        filePath =matcher.replaceAll("/");
        return filePath;
    }
}
