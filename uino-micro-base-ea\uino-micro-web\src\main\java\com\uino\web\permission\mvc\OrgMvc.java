package com.uino.web.permission.mvc;

import java.util.List;
import java.util.Set;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import com.uino.bean.permission.base.SysUser;
import com.uino.bean.permission.query.CSysOrg;
import com.uino.util.sys.SysUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uino.api.client.permission.IOrgApiSvc;
import com.uino.bean.permission.base.SysOrg;
import com.uino.bean.permission.business.OrgNodeInfo;
import com.uino.bean.permission.business.request.AddOrRemoveRoleToOrgRequestDto;
import com.uino.bean.permission.business.request.AddOrRemoveUserToOrgRequestDto;
import com.uino.bean.permission.business.request.InterchangeOrgNoRequestDto;
import com.uino.bean.permission.business.request.SaveOrgRequestDto;
import com.uino.bean.permission.business.request.SetUsersOrgsRequestDto;
import com.uino.init.api.ApiResult;
import com.uino.init.api.ApiVersion;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 组织相关控制层
 * 
 * <AUTHOR>
 *
 */
@ApiVersion(1)
@Api(value = "组织管理", tags = {"组织管理"})
@RestController
@RequestMapping(value = "/permission/org")
public class OrgMvc {
	@Autowired
	private IOrgApiSvc orgService;

	/**
	 * 获取组织树
	 * 
	 * @param request
	 * @param response
	 */
	@ApiOperation("获取组织树")
	@PostMapping("getTree")
    @ModDesc(desc = "获取组织树", pDesc = "无", rDesc = "组织树结构", rType = OrgNodeInfo.class)
	public ApiResult<OrgNodeInfo> getOrgTree(HttpServletRequest request, HttpServletResponse response) {
		SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
		OrgNodeInfo tree = orgService.getOrgTree(currentUserInfo.getDomainId());
		return ApiResult.ok(this).data(tree);
	}
	
	/**
	 * 获取组织树
	 * 
	 * @param request
	 * @param response
	 */
	@ApiOperation("动态获取组织树")
	@PostMapping("getTreeDynamic")
    @ModDesc(desc = "动态获取组织树", pDesc = "父级组织id，可为空", rDesc = "组织树结构", rType = OrgNodeInfo.class)
	public ApiResult<OrgNodeInfo> getTreeDynamic(HttpServletRequest request, HttpServletResponse response,@RequestBody(required=false) Long orgId) {
		SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
		OrgNodeInfo tree = orgService.getOrgTreeV2(currentUserInfo.getDomainId(),orgId);
		return ApiResult.ok(this).data(tree);
	}

	/**
	 * 获取组织树
	 */
	@ApiOperation("获取组织树-不包含用户")
	@GetMapping("getTreeV3")
	public ApiResult<OrgNodeInfo> getTreeV3() {
		SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
		OrgNodeInfo tree = orgService.getOrgTreeV3(currentUserInfo.getDomainId());
		return ApiResult.ok(this).data(tree);
	}

	/**
	 * 持久化组织
	 * 
	 * @param request
	 * @param response
	 */
	@ApiOperation("保存或更新组织")
	@PostMapping("saveOrUpdate")
    @ModDesc(desc = "保存或更新组织", pDesc = "组织对象", pType = SaveOrgRequestDto.class, rDesc = "组织id", rType = Long.class)
	public ApiResult<Long> saveOrUpdate(@RequestBody SaveOrgRequestDto requestDto, HttpServletRequest request,
			HttpServletResponse response) {
		SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
		requestDto.setDomainId(currentUserInfo.getDomainId());
		Long result = orgService.saveOrUpdateOrg(requestDto);
		return ApiResult.ok(this).data(result);
	}

	/**
	 * 删除组织
	 * 
	 * @param removeOrgIds
	 * @param request
	 * @param response
	 */
	@ApiOperation("根据id批量删除组织")
	@PostMapping("deleteByIds")
    @ModDesc(desc = "根据id批量删除组织", pDesc = "组织id集合", pType = Set.class, rDesc = "操作是否成功", rType = Boolean.class)
	public ApiResult<Boolean> deleteByIds(@RequestBody Set<Long> removeOrgIds, HttpServletRequest request,
			HttpServletResponse response) {
		orgService.deleteOrg(removeOrgIds);
		return ApiResult.ok(this).data(true);
	}

	/**
	 * 向组织添加用户
	 * 
	 * @param requestDto
	 * @param request
	 * @param response
	 */
	@ApiOperation("组织添加用户")
	@PostMapping("addUsers")
    @ModDesc(desc = "组织添加用户", pDesc = "组织用户数据对象", pType = AddOrRemoveUserToOrgRequestDto.class, rDesc = "操作是否成功", rType = Boolean.class)
	public ApiResult<Boolean> addUsers(@RequestBody AddOrRemoveUserToOrgRequestDto requestDto, HttpServletRequest request,
			HttpServletResponse response) {
		SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
		requestDto.setDomainId(currentUserInfo.getDomainId());
		orgService.addUserForOrg(requestDto);
		return ApiResult.ok(this).data(true);
	}

	/**
	 * 将用户从组织移除
	 * 
	 * @param requestDto
	 * @param request
	 * @param response
	 */
	@ApiOperation("组织移除用户")
	@PostMapping("removeUsers")
    @ModDesc(desc = "组织移除用户", pDesc = "组织用户数据对象", pType = AddOrRemoveUserToOrgRequestDto.class, rDesc = "操作是否成功", rType = Boolean.class)
	public ApiResult<Boolean> removeUsers(@RequestBody AddOrRemoveUserToOrgRequestDto requestDto, HttpServletRequest request,
			HttpServletResponse response) {
		SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
		requestDto.setDomainId(currentUserInfo.getDomainId());
		orgService.removeUserForOrg(requestDto);
		return ApiResult.ok(this).data(true);
	}

	/**
	 * 设置用户s所属组织s
	 * 
	 * @param reqDto
	 * @param request
	 * @param response
	 */
	@ApiOperation("设置用户所属组织")
	@PostMapping("setUsersOrgs")
    @ModDesc(desc = "设置用户所属组织", pDesc = "用户组织数据对象", pType = SetUsersOrgsRequestDto.class, rDesc = "操作是否成功", rType = Boolean.class)
	public ApiResult<Boolean> setUsersOrgs(@RequestBody SetUsersOrgsRequestDto reqDto, HttpServletRequest request,
			HttpServletResponse response) {
		SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
		reqDto.setDomainId(currentUserInfo.getDomainId());
		orgService.setUsersOrgs(reqDto);
		return ApiResult.ok(this).data(true);
	}

	/**
	 * 向组织绑定角色
	 * 
	 * @param requestDto
	 * @param request
	 * @param response
	 */
	@ApiOperation("为组织绑定角色")
	@PostMapping("addRoles")
    @ModDesc(desc = "为组织绑定角色", pDesc = "组织角色数据对象", pType = AddOrRemoveRoleToOrgRequestDto.class, rDesc = "操作是否成功", rType = Boolean.class)
	public ApiResult<Boolean> addRoles(@RequestBody AddOrRemoveRoleToOrgRequestDto requestDto, HttpServletRequest request,
			HttpServletResponse response) {
		SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
		requestDto.setDomainId(currentUserInfo.getDomainId());
		orgService.addRoleForOrg(requestDto);
		return ApiResult.ok(this).data(true);
	}

	/**
	 * 将角色移除组织
	 * 
	 * @param requestDto
	 * @param request
	 * @param response
	 */
	@ApiOperation("组织解绑角色")
	@PostMapping("removeRoles")
    @ModDesc(desc = "组织解绑角色", pDesc = "组织角色数据对象", pType = AddOrRemoveRoleToOrgRequestDto.class, rDesc = "操作是否成功", rType = Boolean.class)
	public ApiResult<Boolean> removeRoles(@RequestBody AddOrRemoveRoleToOrgRequestDto requestDto, HttpServletRequest request,
			HttpServletResponse response) {
		SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
		requestDto.setDomainId(currentUserInfo.getDomainId());
		orgService.removeRoleForOrg(requestDto);
		return ApiResult.ok(this).data(true);
	}

	/**
	 * 获取指定组织下用户
	 * 
	 * @param orgId
	 * @param request
	 * @param response
	 */
	@ApiOperation("获取指定组织下用户")
	@PostMapping("getUserIds")
    @ModDesc(desc = "获取指定组织下用户", pDesc = "组织id", pType = Long.class, rDesc = "组织下用户id的集合", rType = Set.class, rcType = Long.class)
	public ApiResult<Set<Long>> getUserIds(@RequestBody Long orgId, HttpServletRequest request, HttpServletResponse response) {
		Set<Long> userIds = orgService.getUserIds(orgId);
		return ApiResult.ok(this).data(userIds);
	}

	/**
	 * 获取指定组织下角色
	 * 
	 * @param orgId
	 * @param request
	 * @param response
	 */
	@ApiOperation("获取指定组织下角色")
	@PostMapping("getRoleIds")
    @ModDesc(desc = "获取指定组织下角色", pDesc = "组织id", pType = Long.class, rDesc = "组织绑定的角色id集合", rType = Set.class, rcType = Long.class)
	public ApiResult<Set<Long>> getRoleIds(@RequestBody Long orgId, HttpServletRequest request, HttpServletResponse response) {
		Set<Long> roleIds = orgService.getRoleIds(orgId);
		return ApiResult.ok(this).data(roleIds);
	}

    /**
     * 获取指定组织下角色
     * 
     * @param orgId
     * @param request
     * @param response
     */
    @ApiOperation("获取角色绑定的组织")
    @PostMapping("getOrgByRoleId")
    @ModDesc(desc = "获取角色绑定的组织", pDesc = "角色id", pType = Long.class, rDesc = "角色绑定的组织数据集合", rType = List.class, rcType = SysOrg.class)
    public ApiResult<List<SysOrg>> getOrgByRoleId(@RequestBody Long roleId, HttpServletRequest request, HttpServletResponse response) {
        List<SysOrg> res = orgService.getOrgByRoleId(roleId);
		return ApiResult.ok(this).data(res);
    }

	/**
	 * 交换组织顺序
	 * 
	 * @param reqDto
	 * @param request
	 * @param response
	 */
	@ApiOperation("交换组织顺序")
	@PostMapping("interchangeOrgNo")
    @ModDesc(desc = "交换组织顺序", pDesc = "组织顺序交换数据传输类", pType = InterchangeOrgNoRequestDto.class, rDesc = "操作是否成功", rType = Boolean.class)
	public ApiResult<Boolean> interchangeOrgNo(@RequestBody InterchangeOrgNoRequestDto reqDto, HttpServletRequest request,
			HttpServletResponse response) {
		orgService.interchangeOrgNo(reqDto);
		return ApiResult.ok(this).data(true);
	}

	@ApiOperation("查询全部组织信息")
	@PostMapping("queryAllOrgs")
	public ApiResult<List<SysOrg>> queryAllOrgs(HttpServletRequest request,
			HttpServletResponse response) {
		SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
		CSysOrg cSysOrg = new CSysOrg();
		cSysOrg.setDomainId(currentUserInfo.getDomainId());
		List<SysOrg> orgs = orgService.queryPageByCdt(1, 100000, cSysOrg).getData();
		return ApiResult.ok(this).data(orgs);
	}
}
