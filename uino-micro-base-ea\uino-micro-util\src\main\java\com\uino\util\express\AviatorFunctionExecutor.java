package com.uino.util.express;

import com.alibaba.fastjson.JSON;
import com.binary.framework.exception.ServiceException;
import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.aviator.Expression;
import com.googlecode.aviator.exception.FunctionNotFoundException;
import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.type.AviatorObject;
import com.uino.bean.express.ExpressDTO;
import com.uino.bean.express.ExpressExecuteResult;
import com.uino.plugin.bean.OperatePluginDetails;
import com.uino.plugin.classloader.ClassLoaderAware;
import com.uino.util.express.annotation.UinoAviatorFunction;
import com.uino.util.express.common.FunctionCollectionDesc;
import com.uino.util.express.common.FunctionDescription;
import com.uino.util.express.common.UinoAbstractAviatorFunction;
import com.uino.util.sys.SysUtil;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Aviator公式计算
 * 实现{@link ClassLoaderAware}，插件加载或卸载成功后重新加载计算公式
 * 实现{@link ApplicationRunner}，启动成功后加载计算公式
 */
@Component
@Log4j2
public class AviatorFunctionExecutor implements ClassLoaderAware, ApplicationRunner {

    @Autowired
    private ApplicationContext applicationContext;
    /**
     * all function map
     * <p>
     * {@code key: function name}
     * {@code value: function descriptions {@link List<FunctionDescription>}}
     */
    public static Map<String, FunctionCollectionDesc> loaderFunctionMap = new ConcurrentHashMap<>();

    public void reloadFunctions() {
        Set<String> functionNames = new HashSet<>(AviatorFunctionExecutor.loaderFunctionMap.keySet());
        Map<String, Object> beanMap = applicationContext.getBeansWithAnnotation(UinoAviatorFunction.class);
        beanMap.forEach((k, v) -> {
            UinoAbstractAviatorFunction function = (UinoAbstractAviatorFunction) v;
            FunctionCollectionDesc functionInfos = function.getFunctionInfos();
            String funcName = function.getName();
            if (functionInfos != null) {
                try {
                    // add function
                    AviatorEvaluator.addFunction(function);
                    // add function description
                    AviatorFunctionExecutor.loaderFunctionMap.put(funcName, functionInfos);
                    functionNames.remove(funcName);
                } catch (Exception e) {
                    log.error("Aviator function add error! Function name is [${}]", funcName);
                }
            } else {
                log.error("Empty function name of : ${}", funcName);
            }
        });
        if (!CollectionUtils.isEmpty(functionNames)) {
            functionNames.forEach(functionName -> {
                AviatorFunctionExecutor.loaderFunctionMap.remove(functionName);
                AviatorEvaluator.removeFunction(functionName);
            });
        }
    }

    /**
     * 获取所有已加载公式及描述，包含重载公式
     */
    public static List<FunctionCollectionDesc> getAllLoadedFunctions() {
        return new ArrayList<>(AviatorFunctionExecutor.loaderFunctionMap.values());
    }

    /**
     * 获取所有已加载公式及描述，包含重载公式
     */
    public static List<FunctionCollectionDesc> getLoadedFunctionsByTags(List<String> tags) {
        if (CollectionUtils.isEmpty(tags)) {
            return getAllLoadedFunctions();
        } else {
            List<FunctionCollectionDesc> functionList = new ArrayList<>();
            AviatorFunctionExecutor.loaderFunctionMap.forEach((name, desc) -> {
                List<String> funcTags = desc.getTags();
                boolean hasTag = false;
                if (!CollectionUtils.isEmpty(funcTags)) {
                    for (String tag : tags) {
                        if (funcTags.contains(tag)) {
                            hasTag = true;
                            break;
                        }
                    }
                }
                if (hasTag) {
                    functionList.add(desc);
                }
            });
            return functionList;
        }
    }

    /**
     * 获取所有已加载的公式及描述map
     * <p>
     * {@code key: function name}
     * {@code value: function collection descriptions {@link FunctionCollectionDesc }}
     */
    public static Map<String, FunctionCollectionDesc> getAllLoadedFunctionMap() {
        return AviatorFunctionExecutor.loaderFunctionMap;
    }

    /**
     * 寻找函数类中的call方法
     * 递归寻找父类中的方法，当前class为{@link AbstractFunction} 时退出递归
     * @param clazz 类定义
     * @param methodList 方法列表
     */
    public static void findCallMethodsInClass(Class<?> clazz, List<Method> methodList) {
        if (methodList == null) {
            methodList = new ArrayList<>();
        }
        if (!clazz.equals(AbstractFunction.class)) {
            for (Method method : clazz.getDeclaredMethods()) {
                if (method.getName().equals("call")) {
                    methodList.add(method);
                }
            }
            findCallMethodsInClass(clazz.getSuperclass(), methodList);
        }
    }

    /**
     * 验证表达式格式
     * 验证表达式中公式是否在当前系统中存在
     * 验证表达式中的参数列表是否与定义相符
     * 验证表达式中参数是否在参数列表中
     *
     * @param originExpress 表达式
     */
    public static void validateExpress(String originExpress, Set<String> paramNames) {
        String express = originExpress;
        // 1.验证表达式格式并解析出表达式中的变量参数
        List<String> variableNames = null;
        try {
            Expression expression = AviatorEvaluator.compile(express);
            variableNames = expression.getVariableNames();
        } catch (Exception e) {
            throw new ServiceException("表达式格式错误");
        }
        // 2.移除表达式中的字符串参数和空格
        express = express.replaceAll("\"([^\"]*)\"", "#");
        express = express.replace(" ", "");
        // 3.所有变量参数替换成#并验证参数名在参数列表中是否存在
        for (String varName : variableNames) {
            if (paramNames != null && !paramNames.contains(varName)) {
                String msg = String.format("表达式[%s]参数[%s]在参数列表中不存在", originExpress, varName);
                throw new ServiceException(msg);
            }
            int index = 0;
            while (express.substring(index).contains(varName)) {
                int argStartIndex = express.substring(index).indexOf(varName) + index;
                int argEndIndex = argStartIndex + varName.length() - 1;
                if ("+-*/,(".contains(String.valueOf(express.charAt(argStartIndex - 1))) &&
                        "+-*/,)".contains(String.valueOf(express.charAt(argEndIndex + 1)))) {
                    express = express.substring(0, argStartIndex) + "#" + express.substring(argEndIndex + 1);
                } else {
                    index = argEndIndex + 1;
                }
            }
        }
        // 4.所有方法名替换成#，并通过反射验证参数数量
        Map<String, Object> funcMap = AviatorEvaluator.getInstance().getFuncMap();
        for (String funcName : funcMap.keySet()) {
            int index = 0;
            while (express.substring(index).contains(funcName)) {
                int funcStartIndex = express.substring(index).indexOf(funcName) + index;
                int funcEndIndex = funcStartIndex + funcName.length() - 1;
                if ((funcStartIndex == 0 || ",(+-*/".contains(String.valueOf(express.charAt(funcStartIndex - 1)))) &&
                        "(".contains(String.valueOf(express.charAt(funcEndIndex + 1)))) {
                    String stringFromParam = express.substring(funcEndIndex + 1);
                    int paramCount = 1;
                    if (stringFromParam.indexOf(')') != 1) {
                        int bracketsGroupCount = 0;
                        int charIndex = 0;
                        // 括号组数量为0时方法参数遍历结束
                        do {
                            char ch = stringFromParam.charAt(charIndex);
                            if (ch =='(') {
                                bracketsGroupCount++;
                            }
                            if (ch == ')') {
                                bracketsGroupCount--;
                            }
                            // 括号数量不为1时，说明遍历到作为参数的方法内的参数列表
                            if (ch == ',' && bracketsGroupCount == 1) {
                                paramCount++;
                            }
                            charIndex++;
                        } while (bracketsGroupCount != 0);
                    } else {
                        paramCount = 0;
                    }
                    Object funcObj = funcMap.get(funcName);
                    Class<?> funcClazz = funcObj.getClass();
                    List<Integer> paramCountList = new ArrayList<>();
                    List<Method> callMethodList = new ArrayList<>();
                    findCallMethodsInClass(funcClazz, callMethodList);
                    for (Method method : callMethodList) {
                        if (method.getName().equals("call")) {
                            int paramCountInClazz = 0;
                            for (Parameter parameter : method.getParameters()) {
                                if (parameter.getType().equals(AviatorObject.class)) {
                                    paramCountInClazz++;
                                }
                            }
                            paramCountList.add(paramCountInClazz);
                        }
                    }
                    if (!paramCountList.contains(paramCount)) {
                        String msg = String.format("表达式[%s]中方法[%s]参数列表与定义不符", originExpress, funcName + "()");
                        throw new ServiceException(msg);
                    }
                    express = express.substring(0, funcStartIndex) + "#" + express.substring(funcEndIndex + 1);
                } else {
                    index = funcEndIndex + 1;
                }
            }
        }
        // 5.保留残留的字符串为系统中不存在的方法名
        String[] split = express.split("[#+\\-*/(),]");
        StringBuilder sb = new StringBuilder();
        sb.append("[");
        for (String str : split) {
            if (SysUtil.StringUtil.isNotBack(str)) {
                try {
                    Long.valueOf(str);
                } catch (NumberFormatException e) {
                    try {
                        Double.valueOf(str);
                    } catch (NumberFormatException e1) {
                        sb.append(str).append("(), ");
                    }
                }
            }
        }
        // 6.移除表达式中的运算符、逗号、句号、括号、数字后字符串不为空则表达式中包含当前系统中不存在的方法
        express = express.replaceAll("[#+\\-*/(),.0-9]", "");
        if (SysUtil.StringUtil.isNotBack(express)) {
            String notExists = sb.substring(0, sb.lastIndexOf(", ")) + "]";
            String msg = String.format("表达式[%s]中方法%s在当前系统中不存在", originExpress, notExists);
            throw new ServiceException(msg);
        }
    }

    public static ExpressExecuteResult calculate(ExpressDTO expressDTO) {
        Assert.isTrue(!StringUtils.isEmpty(expressDTO.getExpress()), "错误：必须通过express键定义表达式!");
        validateExpress(expressDTO.getExpress(), expressDTO.getParam().keySet());
        Expression compiledExp = AviatorEvaluator.compile(expressDTO.getExpress());
        Object result = null;
        boolean success = true;
        String cause = null;
        try {
            result = compiledExp.execute(expressDTO.getParam());
        } catch (FunctionNotFoundException e) {
            success = false;
            cause = String.format("公式[%s]未定义", expressDTO.getExpress());
            log.debug(cause, e);
        } catch (IllegalArgumentException e) {
            success = false;
            cause = String.format("公式[%s]参数列表与定义不符", expressDTO.getExpress());
            log.debug(cause, e);
        } catch (NullPointerException e) {
            success = false;
            cause = String.format("公式[%s]中参数[%s}]不完整", expressDTO.getExpress(), JSON.toJSONString(expressDTO.getParam()));
            log.debug(cause, e);
        } catch (Exception e) {
            success = false;
            cause = String.format("公式[%s]，参数[%s]，未知计算错误，错误原因[%s]，详细错误信息请查看后台错误日志", expressDTO.getExpress(), JSON.toJSONString(expressDTO.getParam()), e.getMessage());
            log.error(cause, e);
        }
        return ExpressExecuteResult.builder().success(success).failureReason(cause).value(result).build();
    }

    @Override
    public void afterLoaderSuccess(OperatePluginDetails operatePluginDetails) {
        this.reloadFunctions();
    }

    @Override
    public void afterUnLoaderSuccess(OperatePluginDetails operatePluginDetails) {
        this.reloadFunctions();
    }

    @Override
    public void run(ApplicationArguments args) {
        this.reloadFunctions();
    }
}
