package com.uinnova.product.eam.service.impl;

import com.uinnova.product.eam.comm.model.es.ModelPanorama3D;
import com.uinnova.product.eam.service.IModelPanorama3DSvc;
import com.uinnova.product.eam.service.es.ModelPanorama3DDao;
import com.uino.dao.util.ESUtil;
import com.uino.util.sys.SysUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.TermsQueryBuilder;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 元模型3D全景配置服务实现
 * <AUTHOR>
 */
@Slf4j
@Service
public class ModelPanorama3DSvcImpl implements IModelPanorama3DSvc {

    @Resource
    private ModelPanorama3DDao modelPanorama3DDao;

    @Override
    public Long saveModelPanorama3D(ModelPanorama3D modelPanorama3D) {
        // 先查询是否已存在该元模型的配置
        ModelPanorama3D existConfig = modelPanorama3DDao.selectOne(QueryBuilders.termQuery("modelId", modelPanorama3D.getModelId()));

        if (existConfig == null) {
            // 新增
            modelPanorama3D.setId(ESUtil.getUUID());
            modelPanorama3D.setCreator(SysUtil.getCurrentUserInfo().getLoginCode());
            modelPanorama3D.setCreateTime(ESUtil.getNumberDateTime());
            modelPanorama3D.setDomainId(SysUtil.getCurrentUserInfo().getDomainId());
        } else {
            // 更新
            modelPanorama3D.setId(existConfig.getId());
            modelPanorama3D.setCreator(existConfig.getCreator());
            modelPanorama3D.setCreateTime(existConfig.getCreateTime());
            modelPanorama3D.setDomainId(existConfig.getDomainId());
        }

        // 设置修改信息
        modelPanorama3D.setModifier(SysUtil.getCurrentUserInfo().getLoginCode());
        modelPanorama3D.setModifyTime(ESUtil.getNumberDateTime());

        return modelPanorama3DDao.saveOrUpdate(modelPanorama3D);
    }

    @Override
    public ModelPanorama3D queryByModelId(Long modelId) {
        return modelPanorama3DDao.selectOne(QueryBuilders.termQuery("modelId", modelId));
    }

    @Override
    public Map<Long, ModelPanorama3D> queryByModelIds(Collection<Long> modelIds) {
        Map<Long, ModelPanorama3D> result = new HashMap<>();

        if (CollectionUtils.isEmpty(modelIds)) {
            return result;
        }

        // 使用terms查询多个modelId
        TermsQueryBuilder queryBuilder = QueryBuilders.termsQuery("modelId", modelIds);
        List<ModelPanorama3D> panoramaList = modelPanorama3DDao.getListByQuery(queryBuilder);

        if (!CollectionUtils.isEmpty(panoramaList)) {
            // 将查询结果转换为Map，以modelId为key
            for (ModelPanorama3D panorama : panoramaList) {
                result.put(panorama.getModelId(), panorama);
            }
        }

        return result;
    }
}
