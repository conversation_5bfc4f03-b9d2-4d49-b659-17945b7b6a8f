package com.uinnova.product.eam.service.diagram;

import com.uinnova.product.eam.base.diagram.mix.model.ConvertTemplateBean;
import com.uinnova.product.eam.base.diagram.mix.model.TemplateDir;

import java.util.List;

public interface IEamTemplateDirSvc {

    Integer convertDiagramAndTemplate(ConvertTemplateBean convertTemplateBean);

    /**
     * 保存或更新模板目录
     * @param templateDir
     * @return
     */
    TemplateDir saveOrUpdate(TemplateDir templateDir);

    /**
     * 查询所有的模板目录信息
     * @return
     */
    List<TemplateDir> queryTemplateDirs(Boolean all);

}
