package com.uino.web.auth;

import com.binary.core.exception.MessageException;
import com.binary.core.util.BinaryUtils;
import com.uino.bean.permission.base.SysModule;
import com.uino.bean.permission.base.SysRole;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.permission.business.CurrentUserInfo;
import com.uino.dao.permission.ESModuleSvc;
import com.uino.dao.permission.ESRoleSvc;
import com.uino.dao.permission.ESUserSvc;
import com.uino.util.sys.SysUtil;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class VerifyAuthUtil {

    @Autowired
    private ESUserSvc userSvc;

    @Autowired
    private ESRoleSvc roleSvc;

    @Autowired
    private ESModuleSvc esModuleSvc;

    public void verifyAuth(String moduleSign) {
        CurrentUserInfo currentUser = this.getCurrentUser();
        // 获取用户所属角色有权限的模块
        List<SysRole> roles = roleSvc.getListByUserId(currentUser.getId());
        if (!BinaryUtils.isEmpty(roles)) {
            // 找出这些角色有的模块权限加进去f
            Set<Long> roleIds = roles.stream().map(SysRole::getId).collect(Collectors.toSet());
            List<SysModule> roleModules = esModuleSvc.getListByRoleIds(roleIds);
            Optional<SysModule> module = roleModules.stream().filter(e -> moduleSign.equals(e.getModuleSign())).findFirst();
            if (module.isPresent()) {
                return;
            }
        }
        throw MessageException.i18n("BS_NOT_AUTH", new HashMap<String, String>());
    }
    public void verifyAuth(List<String> moduleSign) {
        CurrentUserInfo currentUser = this.getCurrentUser();
        // 获取用户所属角色有权限的模块
        List<SysRole> roles = roleSvc.getListByUserId(currentUser.getId());
        if (!BinaryUtils.isEmpty(roles)) {
            // 找出这些角色有的模块权限加进去f
            Set<Long> roleIds = roles.stream().map(SysRole::getId).collect(Collectors.toSet());
            List<SysModule> roleModules = esModuleSvc.getListByRoleIds(roleIds);
            List<String> intersection  = roleModules.stream().map(SysModule::getModuleSign).filter(moduleSign::contains).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(intersection)) {
                return;
            }
        }
        throw MessageException.i18n("BS_NOT_AUTH", new HashMap<String, String>());
    }

    public CurrentUserInfo getCurrentUser() {
        SysUser currentUser = SysUtil.getCurrentUserInfo();
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("loginCode.keyword", currentUser.getLoginCode()));
        query.must(QueryBuilders.termQuery("domainId", currentUser.getDomainId()));
        List<SysUser> users = userSvc.getListByQuery(query);
        Assert.isTrue(!BinaryUtils.isEmpty(users), "用户[" + currentUser.getLoginCode() + "]不存在");
        SysUser user = users.get(0);
        return CurrentUserInfo.builder().id(user.getId())
                .domainId(user.getDomainId()).loginCode(user.getLoginCode()).userCode(user.getLoginCode())
                .userName(user.getUserName())
                .lastLoginTime(user.getLastLoginTime()).isUpdatePwd(user.getIsUpdatePwd()).build();
    }
}
