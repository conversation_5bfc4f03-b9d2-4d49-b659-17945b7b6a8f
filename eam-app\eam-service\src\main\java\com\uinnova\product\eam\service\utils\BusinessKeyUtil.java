package com.uinnova.product.eam.service.utils;

import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSONObject;
import com.binary.core.util.BinaryUtils;
import com.uinnova.product.eam.model.cj.domain.PlanDesignInstance;
import com.uinnova.product.eam.model.cj.domain.TemplateType;
import com.uinnova.product.eam.model.cj.vo.BindAssetVo;
import com.uinnova.product.eam.model.cj.vo.DlvrTemplateVO;
import com.uinnova.product.eam.service.ICISwitchSvc;
import com.uinnova.product.eam.service.cj.dao.TemplateTypeDao;
import com.uinnova.product.eam.service.cj.service.DeliverableTemplateService;
import com.uinnova.product.vmdb.comm.model.ci.CcCi;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CiGroupPage;
import com.uino.bean.cmdb.base.LibType;
import com.uino.bean.cmdb.query.ESCISearchBean;
import com.uino.bean.permission.base.SysUser;
import com.uino.util.sys.SysUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class BusinessKeyUtil {

    @Autowired
    private TemplateTypeDao templateTypeDao;

    @Autowired
    private DeliverableTemplateService deliverableTemplateService;

    @Autowired
    private ICISwitchSvc ciSwitchSvc;

    public String createBusinessKey(PlanDesignInstance planDesignInstance) {
        StringBuilder businessStr = new StringBuilder();

        // 根据 typeId 获取模板类型名称
        TemplateType templateType = templateTypeDao.getById(planDesignInstance.getTypeId());
        if (templateType != null && !StringUtils.isEmpty(templateType.getTypeName())) {
            businessStr.append(templateType.getTypeName());
        }

        // 检查templateId是否存在，并获取模板名称和分类名
        String className = "";
        Long templateId = planDesignInstance.getTemplateId();
        if (templateId != null) {
            DlvrTemplateVO dlvrTemplateById = deliverableTemplateService.getDlvrTemplateById(templateId);
            if (dlvrTemplateById != null) {
                businessStr.append(dlvrTemplateById.getTemplateName());
            }
            if (dlvrTemplateById != null && !CollectionUtils.isEmpty(dlvrTemplateById.getBindAssetList())) {
                Map<Long, String> bindAssetMap = dlvrTemplateById.getBindAssetList().stream()
                        .collect(Collectors.toMap(BindAssetVo::getClassId, BindAssetVo::getClassName));
               className = bindAssetMap.get(planDesignInstance.getDefaultSystemClassId());
            }
        }

        //拼接分类名
        if (!BinaryUtils.isEmpty(className)) {
            businessStr.append(className);
        }

        // 设置查询条件并查询CI信息
       if(!BinaryUtils.isEmpty(planDesignInstance.getDefaultSystemCiCode()) &&
               !BinaryUtils.isEmpty(planDesignInstance.getDefaultSystemClassId())){
           ESCISearchBean ciSearchBean = setupCiSearchBean(planDesignInstance);
           CiGroupPage ciGroupPage = ciSwitchSvc.queryPageBySearchBean(ciSearchBean, false, LibType.DESIGN);
           // 处理查询结果
           if (!CollectionUtils.isEmpty(ciGroupPage.getData())) {
               processCiGroupPageData(businessStr, ciGroupPage);
           }
       }

        // 添加实例名称并返回MD5加密后的业务键
        businessStr.append(planDesignInstance.getName());
        return SecureUtil.md5(businessStr.toString());
    }

    private ESCISearchBean setupCiSearchBean(PlanDesignInstance planDesignInstance) {
        ESCISearchBean ciSearchBean = new ESCISearchBean();
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        ciSearchBean.setDomainId(currentUserInfo.getDomainId());
        List<String> ciCodes = new ArrayList<>();
        ciCodes.add(planDesignInstance.getDefaultSystemCiCode());
        ciSearchBean.setCiCodes(ciCodes);
        List<Long> classList = new ArrayList<>();
        classList.add(planDesignInstance.getDefaultSystemClassId());
        ciSearchBean.setClassIds(classList);
        return ciSearchBean;
    }

    private void processCiGroupPageData(StringBuilder businessStr, CiGroupPage ciGroupPage) {
        CcCiInfo ccCiInfo = ciGroupPage.getData().get(0);
        String systemName = getSysName(ccCiInfo.getCi());
//        String systemName = BinaryUtils.isEmpty(ccCiInfo.getAttrs().get("系统名称")) ?
//                ccCiInfo.getAttrs().get("子系统名称") :
//                ccCiInfo.getAttrs().get("系统名称");
        if (!BinaryUtils.isEmpty(systemName)) {
            businessStr.append(systemName);
        }
    }

    private String getSysName(CcCi ci) {
        String ciLabel = ci.getCiLabel();
        List<String> datas = JSONObject.parseArray(ciLabel, String.class);
        if (!BinaryUtils.isEmpty(ciLabel) && !org.springframework.util.CollectionUtils.isEmpty(datas)) {
            String[] strings1 = datas.toArray(new String[0]);
            return StringUtils.join(strings1, ",");
        }

        return formatSysName(ci.getCiPrimaryKey());
    }

    private String formatSysName(String ciPrimaryKey) {
        List<String> strings = JSONObject.parseArray(ciPrimaryKey, String.class);
        if (!org.springframework.util.CollectionUtils.isEmpty(strings)) {
            strings.remove(0);
            String[] strings1 = strings.toArray(new String[0]);
            return StringUtils.join(strings1, ",");
        }
        return null;
    }
}

