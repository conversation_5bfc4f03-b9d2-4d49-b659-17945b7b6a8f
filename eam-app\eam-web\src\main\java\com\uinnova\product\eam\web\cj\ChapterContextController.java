package com.uinnova.product.eam.web.cj;

import com.uinnova.product.eam.base.cj.ResultMsg;
import com.uinnova.product.eam.base.enums.ResultCodeEnum;
import com.uinnova.product.eam.model.cj.domain.ChapterContext;
import com.uinnova.product.eam.model.cj.request.ChapterContextRequest;
import com.uinnova.product.eam.model.cj.request.DeleteModulesRequest;
import com.uinnova.product.eam.model.cj.request.UpdateVersionRequest;
import com.uinnova.product.eam.model.cj.vo.ChapterContextVO;
import com.uinnova.product.eam.service.cj.service.ChapterContextService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.Objects;


/**
 * 方案章节详情 controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/chapter/context")
@Slf4j
public class ChapterContextController {

    @Resource
    private ChapterContextService chapterContextService;


    /**
     * 方案章节新增
     */
    @PostMapping("save")
    public ResultMsg save(@RequestBody @Validated ChapterContextRequest request) {
        Long result = chapterContextService.save(request);
        if (Objects.equals(result, -1L)) {
            return new ResultMsg(false, ResultCodeEnum.APPROVE_ERROR.getCode(), "该方案已提交审批，请刷新后重试!");
        } else if (Objects.equals(result, -2L)){
            return new ResultMsg(false, 404, "暂无权限!");
        } else {
            return new ResultMsg(result);
        }
    }

    /**
     * 查找
     *
     * @param chapterId 章节id
     */
    @GetMapping
    public ResultMsg findById(Long chapterId) {

        ChapterContextVO byId = chapterContextService.findById(chapterId);
        return new ResultMsg(byId);
    }

    @GetMapping("/findIntroduceContextById")
    public ResultMsg findIntroduceContextById(Long introduceChapterId) {
        ChapterContext context = chapterContextService.findIntroduceContextById(introduceChapterId);
        return new ResultMsg(context);
    }


    /**
     * 批量删除模块
     *
     * @param request {@link DeleteModulesRequest}
     * @return ResultMsg
     */
    @PostMapping("/module/delete")
    public ResultMsg delModule(@RequestBody @Validated DeleteModulesRequest request) {
        chapterContextService.delModule(request);
        return ResultMsg.ok();
    }

    /**
     * 全量更新方案内容框视图信息版本为最新版本
     */
    @GetMapping("/updateAllContextDiagramVersion")
    public ResultMsg updateAllContextDiagramVersion(@RequestParam Long planId) {
        chapterContextService.updateAllContextDiagramVersion(planId);
        return ResultMsg.ok();
    }

    /**
     * 更新单个内容块视图最新版本 在画布里面
     */
    @PostMapping("/updateSigleDiagramVersion")
    public ResultMsg updateSingleDiagramVersion(@RequestBody UpdateVersionRequest updateVersionRequest) {
        chapterContextService.updateSingleDiagramVersion(updateVersionRequest);
        return ResultMsg.ok();
    }
}

