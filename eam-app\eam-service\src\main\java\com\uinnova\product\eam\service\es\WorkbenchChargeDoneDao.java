package com.uinnova.product.eam.service.es;

import com.uinnova.product.eam.comm.model.es.WorkbenchChargeDone;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;

@Service
public class WorkbenchChargeDoneDao extends AbstractESBaseDao<WorkbenchChargeDone, WorkbenchChargeDone> {
    @Override
    public String getIndex() {
        return "uino_eam_workbench_charge_done";
    }

    @Override
    public String getType() {
        return getIndex();
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }
}
