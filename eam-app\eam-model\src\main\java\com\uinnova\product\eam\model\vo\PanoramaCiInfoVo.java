package com.uinnova.product.eam.model.vo;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PanoramaCiInfoVo extends CiSimpleInfoVo{

    @Comment("子集")
    private List<PanoramaCiInfoVo> children = new ArrayList<>();

    public PanoramaCiInfoVo() {
    }

    public PanoramaCiInfoVo(Long id, String name, String ciCode, Long classId, String className) {
        super.setId(id);
        super.setName(name);
        super.setCiCode(ciCode);
        super.setClassId(classId);
        super.setClassName(className);
    }
}


