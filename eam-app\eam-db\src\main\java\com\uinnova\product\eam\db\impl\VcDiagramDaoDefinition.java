package com.uinnova.product.eam.db.impl;


import com.binary.framework.dao.DaoDefinition;

import com.uinnova.product.eam.comm.model.CVcDiagram;
import com.uinnova.product.eam.comm.model.VcDiagram;


/**
 * 视图设计表[VC_DIAGRAM]数据访问对象定义实现
 */
public class VcDiagramDaoDefinition implements DaoDefinition<VcDiagram, CVcDiagram> {


	@Override
	public Class<VcDiagram> getEntityClass() {
		return VcDiagram.class;
	}


	@Override
	public Class<CVcDiagram> getConditionClass() {
		return CVcDiagram.class;
	}


	@Override
	public String getTableName() {
		return "VC_DIAGRAM";
	}


	@Override
	public boolean hasDataStatusField() {
		return true;
	}


	@Override
	public void setDataStatusValue(VcDiagram record, int status) {
		record.setDataStatus(status);
	}


	@Override
	public void setDataStatusValue(CVcDiagram cdt, int status) {
		cdt.setDataStatus(status);
	}


	@Override
	public void setCreatorValue(VcDiagram record, String creator) {
		record.setCreator(creator);
	}


	@Override
	public void setModifierValue(VcDiagram record, String modifier) {
		record.setModifier(modifier);
	}


}


