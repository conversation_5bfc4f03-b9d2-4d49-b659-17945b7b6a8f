package com.uino.web.sys.mvc;

import com.alibaba.fastjson.JSONObject;
import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uino.api.client.sys.ITenantDomainApiSvc;
import com.uino.bean.permission.business.SysUserPwdResetParam;
import com.uino.bean.plugin.query.CPluginInfo;
import com.uino.bean.sys.base.TenantDomain;
import com.uino.init.api.ApiResult;
import com.uino.init.api.ApiVersion;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.List;

@ApiVersion(1)
@Api(value = "租户域", tags = {"租户域"})
@RestController
@RequestMapping("/sys/tenantDomain")
public class TenantDomainMvc {

    @Value("${uino.tenantDomain:false}")
    private boolean isOpenTenantDomain;

    @Autowired
    private ITenantDomainApiSvc tenantDomainApiSvc;

    @ApiOperation("保存或更新租户域")
    @PostMapping("/saveOrUpdate")
    public ApiResult<Long> saveOrUpdate(@RequestBody TenantDomain tenantDomain) {
        return ApiResult.ok(this).data(tenantDomainApiSvc.saveOrUpdate(tenantDomain));
    }


    @ApiOperation("根据名称查询租户域信息")
    @PostMapping("/queryPage")
    public ApiResult<Page<TenantDomain>> queryPage(@RequestBody String body) {
        JSONObject jsonObject = JSONObject.parseObject(body);
        int pageNum = jsonObject.getInteger("pageNum");
        int pageSize = jsonObject.getInteger("pageSize");
        String name = jsonObject.getString("name");
        Page<TenantDomain> page = tenantDomainApiSvc.queryPage(pageNum, pageSize, name);
        return ApiResult.ok(this).data(page);
    }

    @ApiOperation("根据id删除租户域")
    @PostMapping("/delete")
    public ApiResult<Boolean> deleteById(@RequestBody Long id) {
        tenantDomainApiSvc.deleteById(id);
        return ApiResult.ok(this).data(true);
    }

    @ApiOperation("启用或停用租户域")
    @PostMapping("/startOrStop")
    public ApiResult<Long> startOrStop(@RequestBody Long id) {
        return ApiResult.ok(this).data(tenantDomainApiSvc.startOrStop(id));
    }

    @ApiOperation("重置域管理员密码")
    @RequestMapping(value = "/resetPasswdByAdmin", method = RequestMethod.POST)
    @ModDesc(desc = "管理员更新密码", pDesc = "封装对象", pType = Long.class, rDesc = "是否成功", rType = Boolean.class)
    public ApiResult<Boolean> resetPasswdByAdmin(HttpServletRequest request, HttpServletResponse response, @RequestBody Long id) {
        tenantDomainApiSvc.resetPasswdByAdmin(id);
        return ApiResult.ok(this).data(true);
    }

    @ApiOperation("是否开启多租户")
    @PostMapping("/multiTenantStatus")
    public ApiResult<Boolean> multiTenantStatus() {
        return ApiResult.ok(this).data(isOpenTenantDomain);
    }

}
