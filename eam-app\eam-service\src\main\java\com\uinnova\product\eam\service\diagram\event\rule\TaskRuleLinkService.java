package com.uinnova.product.eam.service.diagram.event.rule;

import com.alibaba.fastjson.JSONObject;
import com.binary.core.util.BinaryUtils;
import com.binary.jdbc.Page;
import com.uinnova.product.eam.base.diagram.model.ESDiagramLink;
import com.uinnova.product.eam.base.diagram.model.ESDiagramNode;
import com.uinnova.product.eam.db.diagram.es.ESDiagramLinkDao;
import com.uinnova.product.eam.db.diagram.es.ESDiagramNodeDao;
import com.uinnova.product.eam.model.diagram.event.DiagramRuleEnum;
import com.uinnova.product.eam.model.diagram.event.RuleParams;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClass;
import com.uinnova.product.vmdb.comm.model.ci.CcCiClass;
import com.uinnova.product.vmdb.comm.model.rlt.CCcCiRlt;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.base.ESCIRltInfo;
import com.uino.bean.cmdb.base.LibType;
import com.uino.bean.cmdb.business.BindCiRltRequestDto;
import com.uino.bean.cmdb.query.ESCISearchBean;
import com.uino.bean.cmdb.query.ESRltSearchBean;
import com.uino.dao.util.ESUtil;
import com.uino.service.cmdb.microservice.IRltClassSvc;
import com.uino.service.cmdb.microservice.impl.CIClassSvc;
import com.uino.service.cmdb.microservice.impl.CIRltSvc;
import com.uino.service.cmdb.microservice.impl.CISvc;
import com.uino.util.sys.LibTypeUtil;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class TaskRuleLinkService implements IRuleLinkService {

    @Autowired
    CISvc ciSvc;

    @Autowired
    CIRltSvc ciRltSvc;

    @Autowired
    IRltClassSvc rltClassSvc;

    @Autowired
    CIClassSvc ciClassSvc;

    @Autowired
    ESDiagramNodeDao diagramNodeDao;

    @Autowired
    ESDiagramLinkDao diagramLinkDao;

    @Override
    public DiagramRuleEnum getRuleType() {
        return DiagramRuleEnum.TASK_DIAGRAM;
    }

    @Override
    public void process(RuleParams params) {
        if(getRuleType() != params.getRuleType()){
            return;
        }
        String entityClass = params.getConfig().getString("entityClass");
        String stepClass = params.getConfig().getString("stepClass");
        String makeHiddenLinkClass = params.getConfig().getString("makeHiddenLinkClass");

        BoolQueryBuilder nodeQuery = QueryBuilders.boolQuery();
        nodeQuery.must(QueryBuilders.termQuery("diagramId", params.getDiagramId()));
        nodeQuery.must(QueryBuilders.termQuery("sheetId.keyword", params.getSheetId()));
        nodeQuery.must(QueryBuilders.termQuery("domainId", params.getDomainId()));
        nodeQuery.must(QueryBuilders.existsQuery("ciCode"));
        List<ESDiagramNode> esDiagramNodes = diagramNodeDao.selectListByQuery(1, 3000, nodeQuery);

        BoolQueryBuilder linkQuery = QueryBuilders.boolQuery();
        linkQuery.must(QueryBuilders.termQuery("diagramId", params.getDiagramId()));
        linkQuery.must(QueryBuilders.termQuery("sheetId.keyword", params.getSheetId()));
        linkQuery.must(QueryBuilders.termQuery("domainId", params.getDomainId()));
        if(!BinaryUtils.isEmpty(params.getExpectKeys())){
            linkQuery.mustNot(QueryBuilders.termsQuery("key.keyword", params.getExpectKeys()));
        }
        linkQuery.must(QueryBuilders.existsQuery("uniqueCode"));
        List<ESDiagramLink> esDiagramLinks = diagramLinkDao.selectListByQuery(1, 3000, linkQuery);
        if(BinaryUtils.isEmpty(esDiagramNodes)){
            return;
        }
        if(BinaryUtils.isEmpty(esDiagramLinks)){
            return;
        }
        List<String> ciCodes = esDiagramNodes.stream().filter(each -> !BinaryUtils.isEmpty(each.getCiCode()))
                .map(ESDiagramNode::getCiCode).collect(Collectors.toList());
        if(BinaryUtils.isEmpty(ciCodes)){
            return;
        }
        Map<String,String> ciCodeKeysMap = esDiagramNodes.stream().filter(each -> !BinaryUtils.isEmpty(each.getCiCode()))
                .collect(Collectors.toMap(ESDiagramNode::getCiCode, each-> each.getKey(),(k1,k2)->k1));

        CCcCiClass cdt = new CCcCiClass();
        cdt.setDomainId(params.getDomainId());
        cdt.setClassCodes(new String[]{stepClass, entityClass, makeHiddenLinkClass});
        List<CcCiClassInfo> ciClassList = ciClassSvc.queryClassByCdt(cdt);
        if(BinaryUtils.isEmpty(ciClassList)){
            return;
        }
        ESCISearchBean singleBean = new ESCISearchBean();
        singleBean.setPageSize(1);
        singleBean.setPageNum(1);
        singleBean.setOwnerCode(params.getOwnerCode());
        singleBean.setDomainId(params.getDomainId());
        singleBean.setCiCodes(Collections.singletonList(params.getCiCode()));
        Page<ESCIInfo> singleCi = LibTypeUtil.execute(()->ciSvc.searchESCIByBean(singleBean),LibType.PRIVATE);
        if(BinaryUtils.isEmpty(singleCi.getData())){
            return;
        }
        ESCIInfo taskCi = singleCi.getData().get(0);

        Map<String,Long> classIdMap = ciClassList.stream().map(CcCiClassInfo::getCiClass).collect(Collectors.toMap(CcCiClass::getClassCode, CcCiClass::getId));
        List<Long> classIds = new ArrayList<>(classIdMap.values());
        ESCISearchBean bean = new ESCISearchBean();
        bean.setPageNum(1);
        bean.setPageSize(ciCodes.size());
        bean.setCiCodes(ciCodes);
        bean.setClassIds(classIds);
        bean.setOwnerCode(params.getOwnerCode());
        bean.setDomainId(params.getDomainId());
        Page<ESCIInfo> ciPage = LibTypeUtil.execute(()->ciSvc.searchESCIByBean(bean),LibType.PRIVATE);
        if(BinaryUtils.isEmpty(ciPage.getData())){
            return;
        }
        Set<String> uniqueCodes = esDiagramLinks.stream().filter(each -> !BinaryUtils.isEmpty(each.getUniqueCode()))
                .map(ESDiagramLink::getUniqueCode).collect(Collectors.toSet());
        if(BinaryUtils.isEmpty(uniqueCodes)){
            return;
        }
        long sourceClassId = classIdMap.get(stepClass);
        long targetClassId = classIdMap.get(entityClass);
        ESRltSearchBean searchBean = new ESRltSearchBean();
        searchBean.setPageNum(1);
        searchBean.setPageSize(uniqueCodes.size());
        searchBean.setRltUniqueCodes(uniqueCodes);
        searchBean.setSourceClassIds(Collections.singletonList(sourceClassId));
        searchBean.setTargetClassIds(Collections.singletonList(targetClassId));
        searchBean.setOwnerCode(params.getOwnerCode());
        searchBean.setDomainId(params.getDomainId());
        Page<ESCIRltInfo> rltPage = LibTypeUtil.execute(()->ciRltSvc.searchRlt(searchBean),LibType.PRIVATE);
        if(BinaryUtils.isEmpty(rltPage.getData())){
            return;
        }
        List<Long> rltIds = new ArrayList<>();
        for (ESCIRltInfo rlt : rltPage.getData()) {
            BindCiRltRequestDto requestDto = new BindCiRltRequestDto();
            requestDto.setOwnerCode(rlt.getOwnerCode());
            requestDto.setAttrs(rlt.getAttrs());
            requestDto.setRepetitionError(false);
            requestDto.setRltClassId(rlt.getClassId());
            requestDto.setSourceCiId(taskCi.getId());
            requestDto.setTargetCiId(rlt.getTargetCiId());
            Long id = LibTypeUtil.execute(()->ciRltSvc.bindCiRlt(requestDto),LibType.PRIVATE);
            rltIds.add(id);
        }
        ESRltSearchBean rltSearchBean = new ESRltSearchBean();
        CCcCiRlt rltCdt = new CCcCiRlt();
        rltCdt.setIds(rltIds.toArray(new Long[rltIds.size()]));
        rltSearchBean.setCdt(rltCdt);
        Page<ESCIRltInfo> rltDiagramPage  = LibTypeUtil.execute(()->ciRltSvc.searchRlt(rltSearchBean),LibType.PRIVATE);

        List<CcCiClassInfo> rltClassList = rltClassSvc.queryAllClasses(params.getDomainId());
        Map<Long,String> clsMap = rltClassList.stream().map(CcCiClassInfo::getCiClass).collect(Collectors.toMap(CcCiClass::getId,CcCiClass::getClassName,(k1,k2)->k1));
        List<ESDiagramLink> createLinks = new ArrayList<>();
        for (ESCIRltInfo rlt : rltDiagramPage.getData()) {
            ESDiagramLink link = new ESDiagramLink();
            createLinks.add(link);
            link.setDiagramId(params.getDiagramId());
            link.setSheetId(params.getSheetId());
            link.setUniqueCode(rlt.getUniqueCode());
            link.setId(ESUtil.getUUID());
            link.setVisible(0);
            link.setKey(UUID.randomUUID().toString());
            JSONObject linkJson = new JSONObject();
            linkJson.put("key", link.getKey());
            linkJson.put("rltId", rlt.getId());
            linkJson.put("rltCode", rlt.getUniqueCode());
            linkJson.put("classId", rlt.getClassId());
            String className = clsMap.get(rlt.getClassId());
            linkJson.put("className", className);
            linkJson.put("label", className);
            linkJson.put("visible", false);

            linkJson.put("from", ciCodeKeysMap.get(rlt.getSourceCiCode()));
            linkJson.put("to", ciCodeKeysMap.get(rlt.getTargetCiCode()));
            link.setLinkJson(linkJson.toJSONString());
        }
        diagramLinkDao.saveOrUpdateBatch(createLinks);
    }
}
