package com.uinnova.product.eam.service.asset;

import com.uinnova.product.eam.comm.model.es.EamVersionTag;
import com.uinnova.product.eam.model.dto.EamVersionTagDto;

import java.util.List;

/**
 * 版本标签业务层接口
 * <AUTHOR>
 */
public interface EamVersionTagSvc {

    /**
     * 创建流程建模版本标签(目前适用于业务建模及数据建模)
     * @param dto 版本信息
     * @return 创建结果
     */
    Integer createFlowModelTag(EamVersionTagDto dto);

    /**
     * 查询历史版本标签信息
     * @param branchId 分支id
     * @return 历史版本标签集合
     */
    List<EamVersionTag> getHistoryVersion(Long branchId);

    /**
     * 获取版本标签详情
     * @param id 标签id
     * @return 版本标签详情
     */
    EamVersionTag getVersionById(Long id);

    /**
     * 逻辑删除版本标签
     * @param dto
     * @return
     */
    Integer deleteById(EamVersionTagDto dto);
}
