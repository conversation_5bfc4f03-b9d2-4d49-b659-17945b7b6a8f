package com.uino.bean.cmdb.business;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 
 * <AUTHOR>
 *
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value="导入图标信息类",description = "导入图标信息")
public class ImportImageMessage implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /** 图片的地址*/
    @ApiModelProperty(value="图片的地址")
    private String imagePath;

    @ApiModelProperty(value="图片id",example = "123")
    private Long imageId;

    @ApiModelProperty(value="图片名称")
    private String imageName;

    @ApiModelProperty(value="图标信息描述")
    private String message;

}
