package com.uinnova.product.eam.comm.model;




import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;


@Comment("视图操作表[VC_DIAGRAM_OP]")
public class VcDiagramOp implements EntityBean {
	private static final long serialVersionUID = 1L;


	@Comment("ID[ID]")
	private Long id;


	@Comment("视图ID[DIAGRAM_ID]")
	private Long diagramId;


	@Comment("来源D[SOURCE_ID]")
	private Long sourceId;


	@Comment("来源类型[SOURCE_TYPE]    1=用户，2=小组")
	private Integer sourceType;


	@Comment("操作类型[OP_TYPE]    1=移动到网络运维")
	private Integer opType;


	@Comment("操作描述[OP_DESC]")
	private String opDesc;


	@Comment("所属域[DOMAIN_ID]")
	private Long domainId;


	@Comment("创建时间[CREATE_TIME]")
	private Long createTime;


	@Comment("修改时间[MODIFY_TIME]")
	private Long modifyTime;




	public Long getId() {
		return this.id;
	}
	public void setId(Long id) {
		this.id = id;
	}


	public Long getDiagramId() {
		return this.diagramId;
	}
	public void setDiagramId(Long diagramId) {
		this.diagramId = diagramId;
	}


	public Long getSourceId() {
		return this.sourceId;
	}
	public void setSourceId(Long sourceId) {
		this.sourceId = sourceId;
	}


	public Integer getSourceType() {
		return this.sourceType;
	}
	public void setSourceType(Integer sourceType) {
		this.sourceType = sourceType;
	}


	public Integer getOpType() {
		return this.opType;
	}
	public void setOpType(Integer opType) {
		this.opType = opType;
	}


	public String getOpDesc() {
		return this.opDesc;
	}
	public void setOpDesc(String opDesc) {
		this.opDesc = opDesc;
	}


	public Long getDomainId() {
		return this.domainId;
	}
	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}


	public Long getCreateTime() {
		return this.createTime;
	}
	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}


	public Long getModifyTime() {
		return this.modifyTime;
	}
	public void setModifyTime(Long modifyTime) {
		this.modifyTime = modifyTime;
	}


}


