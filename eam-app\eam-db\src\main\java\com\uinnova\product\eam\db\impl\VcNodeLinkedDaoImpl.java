package com.uinnova.product.eam.db.impl;


import com.uinnova.product.eam.comm.model.CVcNodeLinked;
import com.uinnova.product.eam.comm.model.VcNodeLinked;
import com.uinnova.product.eam.db.VcNodeLinkedDao;
import com.uinnova.product.eam.db.support.dao.mybatis.ComMyBatisBinaryDaoImpl;


/**
 * 节点链路表[VC_NODE_LINKED]数据访问对象实现
 */
public class VcNodeLinkedDaoImpl extends ComMyBatisBinaryDaoImpl<VcNodeLinked, CVcNodeLinked> implements VcNodeLinkedDao {

//    @Override
//    public String getTableName() {
//        return "IAMS." + getDaoDefinition().getTableName();
//    }
}


