package com.uino.bean.ap.rule;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.quartz.CronExpression;

import java.text.ParseException;
import java.util.Date;
import java.util.Map;

@Data
@ApiModel(value = "AP计算规则", description = "AP计算规则")
public class APBaseRule {

	@ApiModelProperty(value = "规则ID")
	private Long id;
	@ApiModelProperty(value = "规则名称")
	private String name;
	@ApiModelProperty(value = "规则描述")
	private String description;
	@ApiModelProperty(value = "是否激活（0：未激活；1；激活）")
	private Integer active;
	@ApiModelProperty(value = "定时执行Cron表达式")
	private String strCronExpression;
	@ApiModelProperty(value = "规则创建者")
	private String creator;
	@ApiModelProperty(value = "规则修改者")
	private String modifier;
	@ApiModelProperty(value = "规则创建时间")
	private Long createTime;
	@ApiModelProperty(value = "规则修改时间")
	private Long modifyTime;

	@ApiModelProperty(value="计算参数")
	private Map<String,Object> param;
	@ApiModelProperty(value="表达式公式")
	private String express;
	
	private CronExpression cronExpression = null;

	@ApiModelProperty(value="任务下次执行时间")
	private Date nextValidTime;

	@ApiModelProperty(value="任务执行时间")
	private Date executeTime;

	/**更新下次执行时间
	 * @throws ParseException
	 */
	protected void updateNext() throws ParseException {
		if(cronExpression == null) {
			cronExpression = new CronExpression(strCronExpression);
		}
		nextValidTime = cronExpression.getNextValidTimeAfter(new Date());
	}
	
	/**检查该规则是否到执行时间了
	 * @return 是否需要执行
	 * @throws ParseException
	 */
	public boolean doExecuteCheck() throws ParseException {
		//判断规则是否激活
		if (active == 0) {
			return false;
		}
		Date now = new Date();
		//如果下次执行时间为空, 初始化一次
		if(nextValidTime == null) {
			updateNext();
		}
		//判断是否已经到时间
		if(nextValidTime.getTime()<=now.getTime()) {
			executeTime = nextValidTime;
			updateNext();
			return true;
		}
		return false;
	}

}
