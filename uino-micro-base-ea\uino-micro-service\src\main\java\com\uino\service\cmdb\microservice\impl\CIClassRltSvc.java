package com.uino.service.cmdb.microservice.impl;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeSet;
import java.util.stream.Collectors;

import com.uino.dao.BaseConst;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import com.binary.core.util.BinaryUtils;
import com.binary.jdbc.Page;
import com.uino.dao.cmdb.ESCIClassSvc;
import com.uino.dao.cmdb.ESCISvc;
import com.uino.dao.cmdb.ESCiClassRltSvc;
import com.uino.service.cmdb.microservice.ICIClassRltSvc;
import com.uino.dao.util.ESUtil;
import com.uino.bean.cmdb.base.ESCIClassInfo;
import com.uino.bean.cmdb.base.ESCiClassRlt;
import com.uino.bean.cmdb.business.BindCIClassRltDto;
import com.uino.bean.cmdb.business.ClassRltQueryDto;

@Service
public class CIClassRltSvc implements ICIClassRltSvc {

    @Autowired
    private ESCiClassRltSvc classRltSvc;

    @Autowired
    private ESCIClassSvc esCIClassSvc;

    @Override
    public Page<ESCiClassRlt> queryClassRltPage(ClassRltQueryDto queryDto) {
        if (queryDto == null) {
            queryDto = new ClassRltQueryDto();
        }
        if (queryDto.getDomainId() == null) {
            queryDto.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
        }
        BoolQueryBuilder query = this.queryDtoToESQuery(queryDto);
        return classRltSvc.getSortListByQuery(queryDto.getPageNum(), queryDto.getPageSize(), query, queryDto.getOrder(),
                queryDto.isAsc());
    }

    @Override
    public List<ESCiClassRlt> queryClassRlt(ClassRltQueryDto queryDto) {
        queryDto.setPageSize(3000);
        queryDto.setPageNum(1);
        Page<ESCiClassRlt> pageRes = this.queryClassRltPage(queryDto);
        Assert.isTrue(pageRes.getTotalRows() <= 3000, "不分页查询超过最大3000限制，请更换分页查询方式实现");
        List<ESCiClassRlt> res = pageRes.getData();
        this.fullChildRlt(res);
        return res;
    }

    /**
     * 补充子类关系
     * 
     * @param res
     */
    private void fullChildRlt(List<ESCiClassRlt> res) {
        if (res == null || res.size() <= 0) { return; }
        Set<Long> clsIds = new HashSet<>();
        res.forEach(clsRlt -> {
            clsIds.add(clsRlt.getSourceClassId());
            clsIds.add(clsRlt.getTargetClassId());
        });
        List<ESCIClassInfo> childClss = esCIClassSvc.getListByQuery(QueryBuilders.termsQuery("parentId", clsIds));
        if (childClss == null || childClss.size() <= 0) { return; }
        Map<Long, List<ESCIClassInfo>> clsIdChildsMap = childClss.stream()
                .collect(Collectors.groupingBy(ESCIClassInfo::getParentId));
        List<ESCiClassRlt> addRes = new LinkedList<>();
        res.forEach(clsRlt -> {
            Long sourceClsId = clsRlt.getSourceClassId();
            Long targetClsId = clsRlt.getTargetClassId();
            Long classId = clsRlt.getClassId();
            if (clsIdChildsMap.get(sourceClsId) == null && clsIdChildsMap.get(targetClsId) == null) { return; }
            // 自连接则需要全排列子类
            if (sourceClsId.equals(targetClsId)) {
                List<ESCIClassInfo> childs = clsIdChildsMap.get(targetClsId);
                childs.forEach(source -> {
                    childs.forEach(target -> addRes.add(ESCiClassRlt.builder().classId(classId)
                            .sourceClassId(source.getId()).targetClassId(target.getId()).build()));
                    addRes.add(ESCiClassRlt.builder().classId(classId).sourceClassId(source.getId())
                            .targetClassId(targetClsId).build());
                    addRes.add(ESCiClassRlt.builder().classId(classId).sourceClassId(targetClsId)
                            .targetClassId(source.getId()).build());
                });
            } else {
                List<ESCIClassInfo> sourceChilds = clsIdChildsMap.get(sourceClsId);
                List<ESCIClassInfo> targetChilds = clsIdChildsMap.get(targetClsId);
                // 满足sourcecls有子类就连接target本身
                if (sourceChilds != null && sourceChilds.size() > 0) {
                    sourceChilds.forEach(sourceChild -> addRes.add(ESCiClassRlt.builder().classId(classId)
                            .sourceClassId(sourceChild.getId()).targetClassId(targetClsId).build()));
                }
                // 满足targetcls有子类就连接source-targetchild
                if (targetChilds != null && targetChilds.size() > 0) {
                    targetChilds.forEach(targetChild -> addRes.add(ESCiClassRlt.builder().classId(classId)
                            .sourceClassId(sourceClsId).targetClassId(targetChild.getId()).build()));
                }
                // 满足sourcecls有子类targetcls也有子类，就将sourcecls子类连接所有targetcls子类
                if (sourceChilds != null && sourceChilds.size() > 0 && targetChilds != null
                        && targetChilds.size() > 0) {
                    sourceChilds.forEach(sourceChild -> {
                        targetChilds.forEach(targetChild -> {
                            addRes.add(ESCiClassRlt.builder().classId(classId).sourceClassId(sourceChild.getId())
                                    .targetClassId(targetChild.getId()).build());
                        });
                    });
                }
            }
        });
        if (addRes != null && addRes.size() > 0) {
            res.addAll(addRes);
        }
    }

    /**
     * 查询条件转换esquery
     * 
     * @param queryDto
     * @return
     */
    private BoolQueryBuilder queryDtoToESQuery(ClassRltQueryDto queryDto) {
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("domainId", queryDto.getDomainId()));
		if (!BinaryUtils.isEmpty(queryDto.getRltClassId())) {
			query.must(QueryBuilders.termQuery("classId", queryDto.getRltClassId()));
		}
        if (!BinaryUtils.isEmpty(queryDto.getSourceClassId())) {
            query.must(QueryBuilders.termQuery("sourceClassId", queryDto.getSourceClassId()));
        }
        if (!BinaryUtils.isEmpty(queryDto.getTargetClassId())) {
            query.must(QueryBuilders.termQuery("targetClassId", queryDto.getTargetClassId()));
        }
		if (!BinaryUtils.isEmpty(queryDto.getCiClassId())) {
			query.must(QueryBuilders.multiMatchQuery(queryDto.getCiClassId(), "sourceClassId", "targetClassId"));
		}
		if(!BinaryUtils.isEmpty(queryDto.getTargetClassIds())){
		    query.must(QueryBuilders.termsQuery("targetClassId",queryDto.getTargetClassIds()));
        }
		if(!BinaryUtils.isEmpty(queryDto.getRltClassIds())){
		    query.must(QueryBuilders.termsQuery("classId",queryDto.getRltClassIds()));
        }
		if(!BinaryUtils.isEmpty(queryDto.getSourceClassIds())){
		    query.must(QueryBuilders.termsQuery("sourceClassId",queryDto.getSourceClassIds()));
        }
        return query;
    }

    @Override
    public boolean existRlt(long sourceClsId, long targetClsId, long clsId, boolean onlyCurrentModel) {
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("classId", clsId));
        query.must(QueryBuilders.termQuery("sourceClassId", sourceClsId));
        query.must(QueryBuilders.termQuery("targetClassId", targetClsId));
        List<ESCiClassRlt> rltList = classRltSvc.getListByQuery(query);
        return !rltList.isEmpty();
    }

	@Override
	public Integer saveRlts(Long domainId, List<ESCiClassRlt> classRlts) {
		if (BinaryUtils.isEmpty(classRlts)) {
			return 1;
		}
		domainId = domainId == null ? BaseConst.DEFAULT_DOMAIN_ID : domainId;
		ClassRltQueryDto dto = new ClassRltQueryDto();
		dto.setDomainId(domainId);
		dto.setPageNum(1);
		dto.setPageSize(99999);
		List<ESCiClassRlt> data = this.queryClassRltPage(dto).getData();
		classRlts = classRlts.stream()
				.filter(rlt -> (rlt.getClassId() != null && rlt.getSourceClassId() != null
						&& rlt.getTargetClassId() != null) && !data.contains(rlt))
				.collect(Collectors.collectingAndThen(
						Collectors.toCollection(
								() -> new TreeSet<>(
										Comparator.comparing(rlt -> rlt.getClassId() + ";" + rlt.getSourceClassId()
												+ ";" + rlt.getTargetClassId()))),
						ArrayList::new));
		return classRltSvc.saveOrUpdateBatch(classRlts);
	}

	@Override
	public Integer saveRltsForClass(BindCIClassRltDto dto) {
		dto.valid();
		if (dto.getSourceClassId() != null) {
			classRltSvc.deleteByQuery(QueryBuilders.termQuery("sourceClassId", dto.getSourceClassId()), true);
			dto.getClassRlts().forEach(rlt -> rlt.setSourceClassId(dto.getSourceClassId()));
		} else if (dto.getTargetClassId() != null) {
			classRltSvc.deleteByQuery(QueryBuilders.termQuery("targetClassId", dto.getTargetClassId()), true);
			dto.getClassRlts().forEach(rlt -> rlt.setTargetClassId(dto.getTargetClassId()));
		} else {
			Assert.isTrue(false, "请指定源或目标分类id");
		}
		return this.saveRlts(dto.getDomainId(), dto.getClassRlts());
	}

	@Override
	public Integer deleteRltsByClassId(Long classId) {
		return classRltSvc.deleteByQuery(
				QueryBuilders.multiMatchQuery(classId, "classId", "sourceClassId", "targetClassId"), true);
	}

	@Override
	public Integer deleteClassRlt(ESCiClassRlt rlt) {
		Assert.notNull(rlt, "BS_FIELD_EMPTY_VAL${field:rlt}");
        if (rlt.getDomainId() == null) {
            rlt.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
        }
		BoolQueryBuilder query = ESUtil.cdtToBuilder(rlt);
		return classRltSvc.deleteByQuery(query, true);
	}
}
