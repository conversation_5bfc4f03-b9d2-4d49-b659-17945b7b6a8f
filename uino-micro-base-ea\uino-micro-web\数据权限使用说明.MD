1、实现一个对应数据权限的数据接口（返回树形结构或列表结构）
	树形结构：
		{
			"uid":"唯一标示（方便前端操作无实际意义，与datavalue保持一致即可）",
			"name":"显示名称",
			"dataValue":"该权限的唯一标识",
			"children":[{结构体与该对象一致}]
		}
	列表结构：
		[{
			"uid":"唯一标示（方便前端操作无实际意义，与datavalue保持一致即可）",
			"name":"显示名称",
			"dataValue":"该权限的唯一标识"
		}]
2、将该数据权限写入到初始化数据中uino_sys_data_module.json
	[{
		"dataModuleName": "数据权限tab名称",
		"dataSourceUrl": "1步骤接口的uri",
		"orderNo": 2,
		"modifyTime": 20190715164749,
		"createTime": 20190710172054,
		"modifier": "admin[init]",
		//数据模块唯一code
		"dataModuleCode": "code",
		"id": 2,
		"domainId": 1,
		//是否展示新增checkbox
		"iscreate": 1,
		//展示模式1:列表2:树形
		"displayType": 1,
		//是否展示查看checkbox
		"issee": 1,
		//是否展示删除checkbox
		"isdelete": 1,
		//是否展示修改checkbox
		"isupdate": 1,
		//是否启用
		"isenable": 1
	}]
3、1/2都完成后在页面上就能在数据权限页面中看到对应tab页了，之后页面操作即可
4、通过接口permission/role/getAuthDataRoleByRoleId可获取指定角色的数据权限
   或者通过sdk  IRoleApiSvc#getAuthDataRoleByRoleId获取指定角色的数据权限
