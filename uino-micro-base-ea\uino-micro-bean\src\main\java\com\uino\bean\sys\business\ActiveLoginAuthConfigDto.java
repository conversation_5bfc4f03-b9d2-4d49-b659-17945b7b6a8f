package com.uino.bean.sys.business;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.util.Assert;

import com.binary.framework.bean.annotation.Comment;
import com.uino.bean.permission.business.IValidDto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Comment("用于激活集成登录配置信息")
@ApiModel(value="用于激活集成登录配置信息",description = "用于激活集成登录配置信息")
public class ActiveLoginAuthConfigDto implements Serializable, IValidDto {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value="配置ID",example="123")
    @Comment("配置ID")
    private Long id;

    @ApiModelProperty(value="是否激活该配置",example = "true")
    @Comment("是否激活该配置,true:激活,false不激活")
    private boolean active;

    @Override
    public void valid() {
        Assert.notNull(getId(), "X_PARAM_NOT_NULL${name:id}");
    }
}
