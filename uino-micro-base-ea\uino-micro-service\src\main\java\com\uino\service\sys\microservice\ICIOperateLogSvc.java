package com.uino.service.sys.microservice;

import java.util.List;

import com.binary.jdbc.Page;
import com.uino.bean.sys.base.ESCIOperateLog;
import com.uino.bean.sys.query.ESCIOperateLogSearchBean;

/**
 * 
 * <AUTHOR>
 *
 */
public interface ICIOperateLogSvc {

    /**
     * 条件查询CI配置日志
     * 
     * @param bean
     * @return
     */
    public Page<ESCIOperateLog> getCIOperateLogPageByCdt(ESCIOperateLogSearchBean bean);

    /**
     * 保存CI配置日志
     * 
     * @param log
     * @return
     */
    public Long saveOrUpdate(ESCIOperateLog log);

    /**
     * 批量保存CI配置日志
     * 
     * @param logs
     * @return
     */
    public Integer saveOrUpdateBatch(List<ESCIOperateLog> logs);

    /**
     * 按保留时长清除CI配置日志
     * 
     * @param durationDay
     * @return
     */
    public Integer clearCIOperateLogByDuration(Integer durationDay);

}
