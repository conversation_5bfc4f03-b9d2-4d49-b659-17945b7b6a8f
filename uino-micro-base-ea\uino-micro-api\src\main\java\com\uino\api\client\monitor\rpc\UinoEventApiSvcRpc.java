package com.uino.api.client.monitor.rpc;

import com.binary.jdbc.Page;
import com.uino.api.client.monitor.IUinoEventApiSvc;
import com.uino.bean.event.EventCiKpiCount;
import com.uino.bean.event.modeling.EventModel;
import com.uino.bean.event.param.EventCiKpiCountParam;
import com.uino.bean.event.param.EventCiSeverityQueryParam;
import com.uino.bean.monitor.base.ESAlarm;
import com.uino.bean.monitor.base.ESMonEapEvent;
import com.uino.bean.monitor.base.ESMonSysSeverityInfo;
import com.uino.bean.monitor.buiness.CurrentEventQueryDto;
import com.uino.bean.monitor.buiness.EventQueryDto;
import com.uino.dao.BaseConst;
import com.uino.provider.feign.monitor.UinoEventFeign;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

@Service
public class UinoEventApiSvcRpc implements IUinoEventApiSvc {

	@Autowired
	private UinoEventFeign eventFeign;

	@Override
	public EventModel getEventModel() {
		return eventFeign.getEventModel();
	}

	@Override
	public Page<ESMonEapEvent> searchEventPage(EventQueryDto queryDto) {
		return eventFeign.searchEventPage(queryDto);
	}

	@Override
	public void saveAlarm(ESAlarm saveBean) {
		eventFeign.saveAlarm(saveBean);
	}

	@Override
	public void saveCurrentEvent(ESMonEapEvent fmEvent) {
		eventFeign.saveCurrentEvent(fmEvent);
	}

	@Override
	public List<ESMonEapEvent> listCurrentEventByParam(CurrentEventQueryDto queryDto) {
		return eventFeign.listCurrentEventByParam(queryDto);
	}

	@Override
	public List<ESMonEapEvent> listCurrentEventByParam(List<CurrentEventQueryDto> queryDtos) {
		return eventFeign.listCurrentEventByParam(queryDtos);
	}

	@Override
	public void delCurrentEvent(ESMonEapEvent fmEvent) {
		eventFeign.delCurrentEvent(fmEvent);
	}

	@Override
	public List<ESMonEapEvent> listCurrentEventByIds(Set<String> ids) {
		return eventFeign.listCurrentEventByIds(ids);
	}

	@Override
	public List<ESMonEapEvent> listCurrentEventByCiIds(List<Long> ciIds) {
		return eventFeign.listCurrentEventByCiIds(ciIds);
	}

	@Override
	public ESMonEapEvent currentEventById(String id) {
		return eventFeign.currentEventById(id);
	}

	@Override
	public void clearCurrentEvent() {
		eventFeign.clearCurrentEvent(BaseConst.DEFAULT_DOMAIN_ID);
	}

	@Override
	public void clearCurrentEvent(Long domainId) {
		eventFeign.clearCurrentEvent(domainId);
	}

	@Override
	public ESMonEapEvent saveEventHis(ESMonEapEvent esMonEapEvent) {
		return eventFeign.saveEventHis(esMonEapEvent);
	}

	@Override
	public ESMonEapEvent hisEventById(String id) {
		return eventFeign.hisEventById(id);
	}

	@Override
	public List<ESMonEapEvent> listCurrentEventByCiCodes(List<String> ciCodes) {
		return eventFeign.listCurrentEventByCiCodes(BaseConst.DEFAULT_DOMAIN_ID, ciCodes);
	}

	@Override
	public List<ESMonEapEvent> listCurrentEventByCiCodes(Long domainId, List<String> ciCodes) {
		return eventFeign.listCurrentEventByCiCodes(domainId, ciCodes);
	}

	@Override
	public List<ESMonSysSeverityInfo> getAllEventSeverity() {
		return eventFeign.getAllEventSeverity(BaseConst.DEFAULT_DOMAIN_ID);
	}

	@Override
	public List<ESMonSysSeverityInfo> getAllEventSeverity(Long domainId) {
		return eventFeign.getAllEventSeverity(domainId);
	}

	@Override
	public List<EventCiKpiCount> getEventCountByCiCodeAndKpiCodes(EventCiKpiCountParam param) {
		return eventFeign.getEventCountByCiCodeAndKpiCodes(BaseConst.DEFAULT_DOMAIN_ID, param);
	}

	@Override
	public List<EventCiKpiCount> getEventCountByCiCodeAndKpiCodes(Long domainId, EventCiKpiCountParam param) {
		return eventFeign.getEventCountByCiCodeAndKpiCodes(domainId, param);
	}

	@Override
	public boolean saveEventBatch(List<ESMonEapEvent> events) {
		return eventFeign.saveEventBatch(events);
	}

	@Override
	public List<ESMonEapEvent> queryCurrentEventsByCiCodesAndServeritys(EventCiSeverityQueryParam queryParam) {
		return eventFeign.queryCurrentEventsByCiCodesAndServeritys(BaseConst.DEFAULT_DOMAIN_ID, queryParam);
	}

	@Override
	public List<ESMonEapEvent> queryCurrentEventsByCiCodesAndServeritys(Long domainId, EventCiSeverityQueryParam queryParam) {
		return eventFeign.queryCurrentEventsByCiCodesAndServeritys(domainId, queryParam);
	}
}
