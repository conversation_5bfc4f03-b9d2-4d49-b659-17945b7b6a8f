package com.uino.util.message.queue.service.producer;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.uino.util.message.queue.MessageQueueProducer;
import com.uino.util.message.queue.config.KafkaProp;
import com.uino.util.message.queue.tools.EnableMessageQueue;
import com.uino.util.message.queue.tools.SnowFlakeFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Conditional;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;

/**
 * KafkaProducerService
 *
 * @Author: YGQ
 * @Create: 2021-05-24 13:14
 **/
@Service
@Slf4j
@Conditional(EnableMessageQueue.class)
public class MessageQueueProducerKafkaImpl implements MessageQueueProducer {
    private final int batchSize = KafkaProp.batchSize;
    private final KafkaTemplate<String, String> kafkaTemplate;
    private final SnowFlakeFactory snowFlakeFactory;
    private static final Gson GSON = new GsonBuilder().create();

    @Autowired
    public MessageQueueProducerKafkaImpl(KafkaTemplate<String, String> kafkaTemplate, SnowFlakeFactory snowFlakeFactory, KafkaProp kafkaProp) {
        this.kafkaTemplate = kafkaTemplate;
        this.snowFlakeFactory = snowFlakeFactory;
    }

    /**
     * Send messages in batches to Kafka.
     * This method is preferred in most cases.
     *
     * @param topic    kafka topic
     * @param listData data
     */
    @Override
    public void sendBatchMessage(String topic, List<Map<String, Object>> listData, String... key) {
        if (listData.size() > batchSize) {
            List<Map<String, Object>> batchData = new ArrayList<>(batchSize);
            listData.forEach(data -> {
                batchData.add(data);
                if (batchData.size() >= batchSize) {
                    this.doSend(topic, batchData, key);
                }
            });
            if (batchData.size() > 0) {
                this.doSend(topic, batchData, key);
            }
        } else {
            this.doSend(topic, listData, key);
        }
    }

    /**
     * Send a single piece of data to kafka.
     * This method is preferred in most cases.
     *
     * @param topic   topic
     * @param mapData data
     */
    @Override
    public void sendMessage(String topic, Map<String, Object> mapData, String... key) {
        String messageKey = this.getRandomKey(key);
        kafkaTemplate.send(topic, messageKey, GSON.toJson(mapData));
    }

    /**
     * Send a single piece of data to kafka.
     * This method is preferred in most cases.
     *
     * @param topic topic
     * @param data  data
     */
    @Override
    public void sendMessage(String topic, String data, String... key) {
        String messageKey = this.getRandomKey(key);
        kafkaTemplate.send(topic, messageKey, data);
    }

    /**
     * Synchronously send data to kafka in batches.
     *
     * @param topic    topic
     * @param listData data
     */
    @Override
    public void sendBatchMessageSync(String topic, List<Map<String, Object>> listData, String... key) {
        if (listData.size() > batchSize) {
            List<Map<String, Object>> batchData = new ArrayList<>(batchSize);
            listData.forEach(data -> {
                batchData.add(data);
                if (batchData.size() >= batchSize) {
                    this.doSendSync(topic, batchData, key);
                }
            });
            if (batchData.size() > 0) {
                this.doSendSync(topic, batchData, key);
            }
        } else {
            this.doSendSync(topic, listData, key);
        }
    }


    /**
     * Synchronously send a single piece of data to kafka.
     * Use this method most of the time.
     *
     * @param topic   topic
     * @param mapData data
     */
    @Override
    public void sendMessageSync(String topic, Map<String, Object> mapData, String... key) {
        try {
            String messageKey = this.getRandomKey(key);
            kafkaTemplate.send(topic, messageKey, GSON.toJson(mapData)).get();
        } catch (InterruptedException | ExecutionException e) {
            log.error("send kafka error, topic: {}, cause: {}", topic, e.getMessage());
            throw new RuntimeException(e.getMessage());
        }
    }

    /**
     * Synchronously send a single piece of data to kafka.
     * Use this method most of the time.
     *
     * @param topic topic
     * @param data  data
     */
    @Override
    public void sendMessageSync(String topic, String data, String... key) {
        try {
            String messageKey = this.getRandomKey(key);
            kafkaTemplate.send(topic, messageKey, data).get();
        } catch (InterruptedException | ExecutionException e) {
            log.error("send kafka error, topic: {}, cause: {}", topic, e.getMessage());
            throw new RuntimeException(e.getMessage());
        }
    }

    /**
     * Asynchronously send data to kafka in batches
     *
     * @param topic    topic
     * @param listData data
     */
    @Override
    public void sendBatchMessageAsync(String topic, List<Map<String, Object>> listData, String... key) {
        if (listData.size() > batchSize) {
            List<Map<String, Object>> batchData = new ArrayList<>(batchSize);
            listData.forEach(data -> {
                batchData.add(data);
                if (batchData.size() >= batchSize) {
                    this.doSendAsync(topic, batchData, key);
                }
            });
            if (batchData.size() > 0) {
                this.doSendAsync(topic, batchData, key);
            }
        } else {
            this.doSendAsync(topic, listData, key);
        }
    }


    /**
     * Asynchronously send a single piece of data to kafka.
     * Use this method most of the time.
     *
     * @param topic   topic
     * @param mapData data
     */
    @Override
    public void sendMessageAsync(String topic, Map<String, Object> mapData, String... key) {
        String messageKey = this.getRandomKey(key);
        kafkaTemplate.send(topic, messageKey, GSON.toJson(mapData))
            .thenAccept(success -> {
                log.debug("send message to kafka success, topic: {}, message: {}", topic, mapData);
            })
            .exceptionally(error -> {
                log.error("send message to kafka fail, topic: {}, message: {}", topic, mapData);
                return null;
            });
    }

    private void doSend(String topic, List<Map<String, Object>> message, String[] key) {
        try {
            String messageKey = this.getRandomKey(key);
            kafkaTemplate.send(topic, messageKey, GSON.toJson(message));
        } finally {
            message.clear();
        }
    }

    private void doSendSync(String topic, List<Map<String, Object>> message, String[] key) {
        try {
            String messageKey = this.getRandomKey(key);
            kafkaTemplate.send(topic, messageKey, GSON.toJson(message)).get();
        } catch (InterruptedException | ExecutionException e) {
            log.error("send kafka error, topic: {}, cause: {}", topic, e.getMessage());
            throw new RuntimeException(e.getMessage());
        } finally {
            message.clear();
        }
    }

    private void doSendAsync(String topic, List<Map<String, Object>> message, String[] key) {
        try {
            String messageKey = this.getRandomKey(key);
            kafkaTemplate.send(topic, messageKey, GSON.toJson(message))
                .thenAccept(success -> {
                    log.debug("send message to kafka success, topic: {}, message: {}", topic, message.get(0));
                })
                .exceptionally(error -> {
                    log.error("send message to kafka fail, topic: {}, message: {}", topic, message);
                    return null;
                });
        } finally {
            message.clear();
        }
    }

    private String getRandomKey(String[] key) {
        String messageKey;
        if (null != key && key.length > 0) {
            messageKey = key[0];
        } else {
            messageKey = snowFlakeFactory.nextId();
        }
        return messageKey;
    }
}
