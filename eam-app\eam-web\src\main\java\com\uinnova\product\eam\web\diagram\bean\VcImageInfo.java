package com.uinnova.product.eam.web.diagram.bean;

import java.io.Serializable;
import java.util.List;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.vmdb.comm.model.ci.CcCiClass;
import com.uinnova.product.vmdb.comm.model.dir.CcGeneralDir;
import com.uinnova.product.vmdb.comm.model.image.CcImage;

public class VcImageInfo implements Serializable {
	private static final long serialVersionUID = 1L;

	
	@Comment("图标目录")
	private CcGeneralDir dir;
	
	@Comment("目录下的图标")
	private List<CcImage> images;
	
	@Comment("自定义形状xml信息")
	private String imageXml;
	
	@Comment("自定义形状名称")
	private String imageName;
	
	@Comment("自定义形状信息")
	private CcImage image;
	
	@Comment("使用该图标的CI分类信息")
	private CcCiClass ciClass;
	
	@Comment("分类ID")
	private Long classId;
	
	public CcGeneralDir getDir() {
		return dir;
	}

	public void setDir(CcGeneralDir dir) {
		this.dir = dir;
	}

	public List<CcImage> getImages() {
		return images;
	}

	public void setImages(List<CcImage> images) {
		this.images = images;
	}

	public String getImageXml() {
		return imageXml;
	}

	public void setImageXml(String imageXml) {
		this.imageXml = imageXml;
	}

	public String getImageName() {
		return imageName;
	}

	public void setImageName(String imageName) {
		this.imageName = imageName;
	}

	public CcImage getImage() {
		return image;
	}

	public void setImage(CcImage image) {
		this.image = image;
	}

	public CcCiClass getCiClass() {
		return ciClass;
	}

	public void setCiClass(CcCiClass ciClass) {
		this.ciClass = ciClass;
	}

	public Long getClassId() {
		return classId;
	}

	public void setClassId(Long classId) {
		this.classId = classId;
	}
	

	
	
}
