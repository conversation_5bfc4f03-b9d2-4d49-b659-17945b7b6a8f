package com.uino.provider.server.init;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Set;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.binary.core.i18n.Language;
import com.binary.framework.util.FrameworkProperties;
import com.uino.util.exception.LoginException;
import com.uino.util.sys.CurrentUserGetter;
import com.uino.util.sys.SysUtil;
import com.uino.bean.permission.base.SysUser;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Configuration
@Slf4j
public class InitBeans {

    static {
        log.info("初始化 micro feign server!");
    }

    @Value("${i18n.lang.default:ZHC}")
    private Language defaultLanguage;

    @Value("${local.resource.space:}")
    private String projectLocalSpace;

    @Bean
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }

    @Bean
    @ConditionalOnProperty(name = "eam.user.getter", havingValue = "true")
    public CurrentUserGetter currentUserGetter() {
        return new CurrentUserGetter() {

            @Override
            public SysUser getCurrentUserInfo() {
                String token = getCurrentToken();
                return getCurrentUserInfo(token);
            }

            @Override
            public SysUser getCurrentUserInfo(String token) {
                if (StringUtils.isBlank(token)) {
                    throw new LoginException("USER_DONOT_LOGIN");
                }
                SysUser currentUser = null;
                try {
                    Long userId = Long.valueOf(token.split("\\|")[0]);
                    String fiterColumnVal = token.split("\\|")[1];
                    Long domainId = Long.valueOf(token.split("\\|")[2]);
                    String loginCode = token.split("\\|")[3];
                    currentUser = SysUser.builder().id(userId).domainId(domainId).loginCode(loginCode).build();

                } catch (Exception e) {
                    throw new LoginException("异常格式凭据");
                }
                return currentUser;
            }

            @Override
            public String getCurrentToken() {
                try {
                    String token = null;
                    ServletRequestAttributes attrs = (ServletRequestAttributes) RequestContextHolder
                            .getRequestAttributes();
                    token = attrs.getRequest().getHeader("Authorization");
                    if (token != null) {
                        token = token.split(" ")[1];
                        token= SysUtil.StringUtil.Base64.decode(token);
                    }
                    return token;
                } catch (Exception e) {
                    throw new LoginException("USER_DONOT_LOGIN");
                }

            }
        };
    }

    private static final Map<String, Language> httpHeaderLanguageDict = new LinkedHashMap<>();

    static {
        httpHeaderLanguageDict.put("zh-cn", Language.ZHC);
        httpHeaderLanguageDict.put("zh", Language.ZHC);
        httpHeaderLanguageDict.put("en-US", Language.EN);
        httpHeaderLanguageDict.put("en", Language.EN);
    }

    @Bean
    @ConditionalOnMissingBean
    public FrameworkProperties FrameworkProperties() {
        Map<String, Object> properties = new HashMap<>();
        properties.put("charset", "UTF-8");
        properties.put("Project_Local_Space", projectLocalSpace);
        FrameworkProperties frameworkProperties = new FrameworkProperties(properties);
        log.info("FrameworkProperties注册成功");
        return frameworkProperties;
    }
}
