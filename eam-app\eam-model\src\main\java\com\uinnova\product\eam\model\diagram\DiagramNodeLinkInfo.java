package com.uinnova.product.eam.model.diagram;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.io.Serializable;

/**
 * 节点关系线标识vo
 * <AUTHOR>
 */
@Data
public class DiagramNodeLinkInfo implements Serializable {

    @Comment("源端标识")
    private String sourceKey;

    @Comment("源端id")
    private Long sourceId;

    @Comment("目标端标识")
    private String targetKey;

    @Comment("目标端id")
    private Long targetId;

    @Comment("线标识")
    private String linkKey;

    @Comment("线id")
    private Long linkId;

    public DiagramNodeLinkInfo() {
    }

    public DiagramNodeLinkInfo(Long sourceId, Long targetId, Long linkId) {
        this.sourceId = sourceId;
        this.targetId = targetId;
        this.linkId = linkId;
        this.linkKey = sourceId + "_" + linkId + "_" + targetId;
    }
}
