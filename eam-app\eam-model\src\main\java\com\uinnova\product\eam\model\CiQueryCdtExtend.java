package com.uinnova.product.eam.model;

import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.vmdb.comm.model.ci.CCcCi;
import com.uinnova.product.vmdb.comm.model.rule.CcCiTagRuleItem;
import com.uinnova.product.vmdb.provider.ci.bean.CiQueryCdt;
import com.uino.bean.cmdb.base.LibType;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class CiQueryCdtExtend extends CiQueryCdt implements Condition {
	private static final long serialVersionUID = 1L;
	
	@Comment("模糊收索字段")
	private String like;
	
	@Comment("上一页最后一行记录ciCode")
	private String lastCiCode;
	
	@Comment("ID")
	private Long tagId;

	@Comment("IDS")
	private Long[] tagIds;

	@Comment("ci分类Id")
	private Long classId;
	
	@Comment("分类ID")
	private Long[] classIds;
	
	@Comment("是否刷新分类的数据0不刷新,1刷新")
	private Integer queryClass;
	
	@Comment("搜索关键字")
	private String[] words;
	
	@Comment("是否搜索更多0只查前三条1分页")
	private Integer isMore;
	
	@Comment("枚举条件值数组")
	private String[] ciQs;
	
	@Comment("要过滤去除CI分类的文件夹名称")
	private String[] classDirNames;
	
	@Comment("筛选条件")
	private List<CcCiTagRuleItem> ruleItems;
	
	@Comment("ci条件")
	private CCcCi ciCdt;
	
	@Comment("查询条件数组")
	private List<CiQueryCdtExtend> queryCdts;
	
	@Comment("根据标签规则查询条件数组")
	private List<TagRuleItemCdt> tagRuleCdts;
	
	@Comment("树的id")
	private Long treeId;
	
	@Comment("搜索关键字")
	private String keyword;

	@Comment("视图类型")
	private String viewType;

	@Comment("视图类型")
	private LibType libType;

	@Comment("当前视图所属用户id")
	private String ownerCode;

	@Comment("制品类型ID")
	private String productTypeId;

	@Comment("应用广场卡片id")
	private Long cardId;

	@Comment("是否使用权限控制")
	private Boolean permission = false;

	@Comment(value = "关键字是否只查label字段")
	private Boolean wordLabel = false;

	@Comment("AI助手开关 false:关闭 true:开启（默认）")
	private Boolean aiAssistant = Boolean.TRUE;
}
