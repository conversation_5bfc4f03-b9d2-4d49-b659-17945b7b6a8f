package com.uinnova.product.eam.service.dm;

import com.binary.jdbc.Page;
import com.uinnova.product.eam.model.dm.bean.AttrAndCiDto;
import com.uinnova.product.eam.model.dm.bean.DataStandardTypeDto;
import com.uinnova.product.eam.model.dm.bean.EntityParamDto;
import com.uino.bean.cmdb.base.LibType;

import java.util.List;

/**
 * <AUTHOR>
 * 数据建模查询接口类
 */
public interface DataModelQuerySvc {



    /**
     * 查询实体信息列表接口
     * @param paramDto 分类名称，和所属人；
     * @return 实体信息集合
     */
    Page<AttrAndCiDto> selectEntityAttrList(EntityParamDto paramDto);


    /***
     * 查询数据标准信息列表/查询值域信息列表接口
     * @param （搜索功能暂定前端实现）ident用来区分是查询数据标准还是值域
     * @param libType 数据库标识DESIGN
     * @return
     */
    List<DataStandardTypeDto> selectDataStandardList(String ident, LibType libType);


}
