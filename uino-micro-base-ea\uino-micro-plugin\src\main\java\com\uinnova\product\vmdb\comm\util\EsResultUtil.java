package com.uinnova.product.vmdb.comm.util;

import com.binary.core.lang.Conver;
import com.binary.json.JSON;
import com.uinnova.product.vmdb.comm.i18n.MessageUtil;
import com.uinnova.product.vmdb.comm.i18n.VerifyType;

import java.util.Map;

/**
 * 
 * <AUTHOR>
 *
 */
public final class EsResultUtil {
    private EsResultUtil() {
    }

    /**
     * 将条件对象转换为MAP
     * 
     * @param jsonString
     * @return
     */
    @SuppressWarnings({ "unchecked", "rawtypes" })
    public static Map<String, Object> toConditionMap(String jsonString) {
        MessageUtil.checkEmpty(jsonString, "jsonString");
        jsonString = jsonString.trim();
        Map<String, Object> map = (Map) JSON.toObject(jsonString);

        return map;
    }

    @SuppressWarnings("unused")
    public static <T> T getResult(String data, Class<T> clazz) {
        Map<String, Object> map = toConditionMap(data);
        Object object = map.get("data");

        T cdt = Conver.mapping(clazz, map.get("data"));
        try {
            cdt = cdt == null ? clazz.newInstance() : cdt;
        } catch (Exception e) {
            MessageUtil.throwVerify(VerifyType.EMPTY, "CDT", "");
        }
        return cdt;
    }

}
