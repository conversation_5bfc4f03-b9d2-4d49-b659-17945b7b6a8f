package com.uino.api.client.cmdb.local;

import java.util.List;

import com.uino.dao.BaseConst;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.uino.service.cmdb.microservice.ICITreeSvc;
import com.uino.bean.cmdb.base.ESCITreeConfigInfo;
import com.uino.bean.cmdb.business.CITreeNode;
import com.uino.api.client.cmdb.ICITreeApiSvc;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
public class CITreeApiSvcLocal implements ICITreeApiSvc {
	@Autowired
	private ICITreeSvc ciTreeSvc;

	@Override
	public List<ESCITreeConfigInfo> getCITreeConfigs() {
		return ciTreeSvc.getCITreeConfigs(BaseConst.DEFAULT_DOMAIN_ID);
	}

	@Override
	public List<ESCITreeConfigInfo> getCITreeConfigs(Long domainId) {
		return ciTreeSvc.getCITreeConfigs(domainId);
	}

	@Override
	public ESCITreeConfigInfo getCITreeConfig(Long id) {
		// TODO Auto-generated method stub
		return ciTreeSvc.getCITreeConfig(id);
	}

	@Override
	public ESCITreeConfigInfo save(ESCITreeConfigInfo saveInfo) {
		// TODO Auto-generated method stub
		return ciTreeSvc.save(saveInfo);
	}

	@Override
	public List<ESCITreeConfigInfo> delete(Long id) {
		return ciTreeSvc.delete(BaseConst.DEFAULT_DOMAIN_ID, id);
	}

	@Override
	public List<ESCITreeConfigInfo> delete(Long domainId, Long id) {
		return ciTreeSvc.delete(domainId, id);
	}

	@Override
	public CITreeNode getCITree(ESCITreeConfigInfo config, boolean hasNullNode, boolean returnAllCI) {
		// TODO Auto-generated method stub
		return ciTreeSvc.getCITree(config, hasNullNode, returnAllCI);
	}

}
