package com.uinnova.product.eam.db.impl;


import com.binary.framework.dao.DaoDefinition;

import com.uinnova.product.eam.comm.model.CVcDiagramLog;
import com.uinnova.product.eam.comm.model.VcDiagramLog;


/**
 * 视图操作记录表[VC_DIAGRAM_LOG]数据访问对象定义实现
 */
public class VcDiagramLogDaoDefinition implements DaoDefinition<VcDiagramLog, CVcDiagramLog> {


	@Override
	public Class<VcDiagramLog> getEntityClass() {
		return VcDiagramLog.class;
	}


	@Override
	public Class<CVcDiagramLog> getConditionClass() {
		return CVcDiagramLog.class;
	}


	@Override
	public String getTableName() {
		return "VC_DIAGRAM_LOG";
	}


	@Override
	public boolean hasDataStatusField() {
		return false;
	}


	@Override
	public void setDataStatusValue(VcDiagramLog record, int status) {
	}


	@Override
	public void setDataStatusValue(CVcDiagramLog cdt, int status) {
	}


	@Override
	public void setCreatorValue(VcDiagramLog record, String creator) {
	}


	@Override
	public void setModifierValue(VcDiagramLog record, String modifier) {
	}


}


