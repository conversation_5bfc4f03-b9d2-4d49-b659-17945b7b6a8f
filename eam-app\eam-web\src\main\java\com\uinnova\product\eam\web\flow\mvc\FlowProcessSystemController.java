package com.uinnova.product.eam.web.flow.mvc;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.binary.core.exception.BinaryException;
import com.binary.framework.util.ControllerUtils;
import com.binary.framework.web.RemoteResult;
import com.uinnova.product.eam.base.cj.ResultMsg;
import com.uinnova.product.eam.base.util.EamUtil;
import com.uinnova.product.eam.base.util.ExcelUtil;
import com.uinnova.product.eam.comm.model.es.FlowSnapTreeDto;
import com.uinnova.product.eam.comm.model.es.FlowSystemApproveData;
import com.uinnova.product.eam.comm.model.es.IndicatorDetectionInformationAssociation;
import com.uinnova.product.eam.feign.workable.entity.ProcessRequest;
import com.uinnova.product.eam.feign.workable.entity.TaskResponse;
import com.uinnova.product.eam.model.*;
import com.uinnova.product.eam.model.dto.FlowSystemAssociatedFeaturesDto;
import com.uinnova.product.eam.model.dto.FlowSystemFileDto;
import com.uinnova.product.eam.model.enums.ProcessRoles;
import com.uinnova.product.eam.model.vo.FileDocVo;
import com.uinnova.product.eam.model.vo.KcpInfoVo;
import com.uinnova.product.eam.service.FlowProcessSystemService;
import com.uinnova.product.eam.service.ICISwitchSvc;
import com.uinnova.product.eam.service.asset.BmConfigSvc;
import com.uinnova.product.eam.service.es.IndicatorDetectionInformationAssociationDao;
import com.uinnova.product.eam.service.impl.IamsCIDesignSvc;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uino.api.client.permission.IRoleApiSvc;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.base.LibType;
import com.uino.bean.cmdb.business.CiExcelInfoDto;
import com.uino.bean.cmdb.business.ImportResultMessage;
import com.uino.bean.permission.base.SysRole;
import com.uino.bean.permission.base.SysRoleDataModuleRlt;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.permission.query.CAuthBean;
import com.uino.dao.permission.ESRoleSvc;
import com.uino.util.sys.SysUtil;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.File;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 流程管理/流程体系相关接口
 *
 * <AUTHOR>
 * @since 2024/5/24 11:20
 */
@RestController
@RequestMapping("/flowManager/flowProcessSystem/")
public class FlowProcessSystemController {

    @Resource
    FlowProcessSystemService flowProcessSystemService;

    @Resource
    private BmConfigSvc bmConfigSvc;

    @Resource
    private IamsCIDesignSvc ciDesignSvc;

    @Resource
    ICISwitchSvc ciSwitchSvc;

    @Resource
    ESRoleSvc esRoleSvc;

    @Resource
    IRoleApiSvc iRoleApiSvc;

    @Resource
    IndicatorDetectionInformationAssociationDao informationAssociationDao;

    /**
     * 新建流程
     * @param flowProcessSystemDto
     * @return
     */
    @PostMapping("createFlowSystem")
    public RemoteResult createFlowSystem(@RequestBody FlowProcessSystemDto flowProcessSystemDto) {
        return new RemoteResult(flowProcessSystemService.createFlowSystem(flowProcessSystemDto));
    }

    @GetMapping("/getSingleTreeByProcessCiId")
    public RemoteResult getSingleTreeByProcessCiId(Long ciId){
        FlowSnapTreeDto singleTreeByProcessCiId = flowProcessSystemService.getSingleTreeByProcessCiId(ciId);
        return new RemoteResult(singleTreeByProcessCiId);
    }

    /**
     * 新版流程树
     * 是否需要ciInfo,是否需要末级流程
     * @return
     */
    @GetMapping("getFlowSystemTreeNew")
    public RemoteResult getFlowSystemTreeNew(){
        return new RemoteResult(flowProcessSystemService.getFlowSystemTreeNew(Boolean.FALSE,Boolean.TRUE));
    }

    @GetMapping("getRedirectDiagramId")
    public RemoteResult getRedirectDiagramId(String diagramId,@RequestParam(value ="diagramClassType",defaultValue = "flowDiagram") String diagramClassType){
        //查询当前私有库的视图id
        return new RemoteResult(flowProcessSystemService.getRedirectDiagramId(diagramId,diagramClassType,false));
    }

    @GetMapping("getFlowDiagramArtifactId")
    public RemoteResult getFlowDiagramArtifactId(String flowDiagramType){

        String showType = bmConfigSvc.getConfigType("FLOW_DIAGRAM_ARTIFACT_CONFIG");
        if(StringUtils.isEmpty(showType)){
            return new RemoteResult(true,-1,"视图未配置");
        }
        JSONObject jsonObject = JSON.parseObject(showType);
        return new RemoteResult(jsonObject.getLong(flowDiagramType));
    }

    /**
     * 添加关联要素（ciCode和另外8个分类的关联关系）
     * @return
     */
    @PostMapping("addFlowSystemAssociatedFeatures")
    public RemoteResult addFlowSystemAssociatedFeatures(@RequestBody FlowSystemAssociatedFeaturesDto flowProcessSystemDto){
        return new RemoteResult(flowProcessSystemService.addFlowSystemAssociatedFeatures(flowProcessSystemDto));
    }

    /**
     * 添加关联要素（ciCode和另外8个分类的关联关系）
     * @return
     */
    @PostMapping("batchAddFlowSystemAssociatedFeatures")
    public RemoteResult batchAddFlowSystemAssociatedFeatures(@RequestBody FlowSystemAssociatedFeaturesDto flowProcessSystemDto){
        flowProcessSystemService.batchAddFlowSystemAssociatedFeatures(flowProcessSystemDto);
        return new RemoteResult("success");
    }

    /**
     * 根据ciCode和分类code查询绑定的ci列表
     * @param ciCode
     * @param classCode
     * @return
     */
    @GetMapping("getFlowSystemAssociatedFeatures")
    public RemoteResult getFlowSystemAssociatedFeatures(String ciCode,String classCode,
                                                        @RequestParam(defaultValue = "DESIGN") LibType libType){
        return new RemoteResult(flowProcessSystemService.getFlowSystemAssociatedFeatures(ciCode,classCode,libType));
    }

    /**
     * 批量查询关联要素数据
     * @param ciCode
     * @param classCodes
     * @return
     */
    @GetMapping("getBatchFlowSystemAssociatedFeatures")
    public RemoteResult getBatchFlowSystemAssociatedFeatures(String ciCode,String classCodes,@RequestParam(defaultValue = "DESIGN") LibType libType){
        Assert.notNull(ciCode,"ciCode不能为空");
        Assert.notNull(classCodes,"classCodes不能为空");
        HashMap<String, Object> resultMap = new HashMap<>();
        //先用循环查询
        String[] classCodeArr = classCodes.split(",");
        for (String classCode : classCodeArr) {
            Map<String, Object> flowSystemAssociatedFeatures = flowProcessSystemService.getFlowSystemAssociatedFeatures(ciCode, classCode,libType);
            resultMap.put(classCode,flowSystemAssociatedFeatures);
        }
        return new RemoteResult(resultMap);
    }

    /**
     * 根据关联要素id删除关联关系
     * @param flowProcessSystemDto
     * @return
     */
    @PostMapping("deleteFlowSystemAssociatedFeatures")
    public RemoteResult deleteFlowSystemAssociatedFeatures(@RequestBody FlowSystemAssociatedFeaturesDto flowProcessSystemDto){
        Assert.notNull(flowProcessSystemDto.getId(),"需要删除的id不能为空");
        flowProcessSystemService.deleteFlowSystemAssociatedFeatures(flowProcessSystemDto.getId());
        return new RemoteResult("success");
    }

    /**
     * 绑定流程文件
     * @param flowSystemFileDto
     * @return
     */
    @PostMapping("saveOrUpdateFlowSystemFile")
    public RemoteResult saveOrUpdateFlowSystemFile(@RequestBody FlowSystemFileDto flowSystemFileDto){
        return new RemoteResult(flowProcessSystemService.saveOrUpdateFlowSystemFile(flowSystemFileDto));
    }

    /**
     * 获取ciCode绑定的流程文件数据
     * @param ciCode
     * @return
     */
    @GetMapping("getFlowSystemFile")
    public RemoteResult getFlowSystemFile(String ciCode,LibType libType){
        return new RemoteResult(flowProcessSystemService.getFlowSystemFile(ciCode,libType));
    }

    /**
     * 检验
     * @return
     */
    @GetMapping("checkFlowProcessSystem")
    public RemoteResult checkFlowProcessSystem(String ciCode){
        Map<String, Object> stringObjectHashMap = flowProcessSystemService.checkFlowProcessSystem(ciCode);
        if(!stringObjectHashMap.keySet().isEmpty()){
            return new RemoteResult(false,200,"校验未通过",stringObjectHashMap);
        }
        return new RemoteResult("success");
    }

    /**
     *  流程体系发布
     * @param flowProcessPublishDto 需要发布得流程ciCode
     * @return
     */
    @PostMapping("publishFlowProcessSystem")
    public RemoteResult publishFlowProcessSystem(@RequestBody FlowProcessPublishDto flowProcessPublishDto){
        Assert.notNull(flowProcessPublishDto.getCiCode(),"资产标识ciCode不能为空");
        Map<String, Object> errorMap = flowProcessSystemService.checkFlowProcessSystem(flowProcessPublishDto.getCiCode());
        if(!errorMap.keySet().isEmpty()){
            return new RemoteResult(false,200,"校验未通过",errorMap);
        }
        String loginCode = SysUtil.getCurrentUserInfo().getLoginCode();
        flowProcessSystemService.publishFlowProcessSystem(flowProcessPublishDto.getCiCode(),loginCode
                ,flowProcessPublishDto.getPublishReason(),"submit",null);
        return new RemoteResult("success");
    }

    /**
     * 新建/修改流程绩效数据
     * @return
     */
    @PostMapping("createProcessPerformance")
    public RemoteResult createProcessPerformance(@RequestBody ProcessPerformanceDto flowProcessSystemDto){
        CcCiInfo processPerformance = flowProcessSystemService.createProcessPerformance(flowProcessSystemDto,flowProcessSystemDto.getClassCode());
        return new RemoteResult(processPerformance);
    }

    /**
     * 新建/修改流程表单数据
     * @return
     */
    @PostMapping("createProcessTable")
    public RemoteResult createProcessTable(@RequestBody ProcessPerformanceDto flowProcessSystemDto){
        CcCiInfo processPerformance = flowProcessSystemService.createProcessPerformance(flowProcessSystemDto,"表单");
        return new RemoteResult(processPerformance);
    }

    @GetMapping("getFlowSystemPublishHistory")
    public RemoteResult getFlowSystemPublishHistory(String ciCode,String publishType) {
        Assert.notNull(ciCode, "ciCode不能为空");
        if(publishType==null){
            publishType = "submit";
        }
        return new RemoteResult(flowProcessSystemService.getFlowSystemPublishHistory(ciCode,publishType));
    }

    @GetMapping("/findKcpInfoList")
    public RemoteResult findKcpInfoList(String ciCode) {
        List<KcpInfoVo> kcpInfoList = flowProcessSystemService.findKcpInfoList(ciCode, 2,LibType.DESIGN);
        return new RemoteResult(kcpInfoList);
    }

    /**
     * 文件清单列表
     * @param ciCode
     * @return
     */
    @GetMapping("/findFileDocList")
    public RemoteResult findFileDocList(String ciCode, String publishType) {
        if(publishType==null){
            publishType = "submit";
        }
        List<FileDocVo> kcpInfoList = flowProcessSystemService.findFileDocList(ciCode,publishType);
        return new RemoteResult(kcpInfoList);
    }

    /**
     * 版本管理和操作记录列表
     * @param ciCode
     * @return
     */
    @GetMapping("/findOperationList")
    public RemoteResult findOperationList(String ciCode, String publishType) {
        List<FlowProcessSystemPublishHistoryDto> versionManageVoList = flowProcessSystemService.findOperationList(ciCode, publishType);
        return new RemoteResult(versionManageVoList);
    }


    @GetMapping("getAutoDiagramByParentCiCode")
    public RemoteResult getAutoDiagramByParentCiCode(Long parentCiId){
        Assert.notNull(parentCiId,"父ciId不能为空");
        return new RemoteResult(flowProcessSystemService.getAutoDiagramByParentCiCode(parentCiId));
    }

    @PostMapping("delFlowProcessSystem")
    public RemoteResult delFlowProcessSystem(@RequestBody FlowProcessPublishDto flowProcessPublishDto){
        flowProcessSystemService.delFlowProcessSystem(flowProcessPublishDto.getCiCode());
        return new RemoteResult("success");
    }

    @GetMapping("checkFlowDiagramVersion")
    public RemoteResult checkFlowDiagramVersion(String ciCode,String diagramEnergy,@RequestParam(value ="diagramClassType",defaultValue = "flowDiagram") String diagramClassType){

        return new RemoteResult(flowProcessSystemService.checkFlowDiagramVersion(ciCode,diagramEnergy,diagramClassType));
    }

    @PostMapping("mergePullFlowDiagram")
    public RemoteResult mergePullFlowDiagram(@RequestBody FlowProcessMergePushDto flowProcessMergePushDto){
        Assert.notNull(flowProcessMergePushDto.getCiCode(),"ciCode不能为空");
        Assert.notNull(flowProcessMergePushDto.getDiagramClassType(),"视图类型不能为空");
        return new RemoteResult(flowProcessSystemService.mergePullFlowDiagram(flowProcessMergePushDto.getCiCode()
                ,flowProcessMergePushDto.getDiagramClassType()));
    }


    @GetMapping("checkFlowCiVersion")
    public RemoteResult checkFlowCiVersion(String ciCode){
        return new RemoteResult(flowProcessSystemService.checkFlowCiVersion(ciCode));
    }

    @PostMapping("mergePullFlowCi")
    public RemoteResult mergePullFlowCi(@RequestBody FlowProcessMergePushDto flowProcessMergePushDto){
        Assert.notNull(flowProcessMergePushDto.getCiCode(),"ciCode不能为空");
        return new RemoteResult(flowProcessSystemService.mergePullFlowCi(flowProcessMergePushDto.getCiCode(),false));
    }

    /**
     * 获取末级流程表单数据
     * @return
     */
    @GetMapping("getEndProcessTable")
    public RemoteResult getEndProcessTable(String ciCode,@RequestParam(defaultValue = "PRIVATE")LibType libType) {
        Assert.notNull(ciCode,"资产标识ciCode不能为空");
        return new RemoteResult(flowProcessSystemService.getEndProcessTable(ciCode,libType));
    }
    /**
     * 批量保存或更新末级流程活动数据视图节点信息
     * @param flowProcessTableDto 视图节点id,节点排序字段
     * @return 结果
     */
    @PostMapping("batchSaveOrUpdateFlowDiagramNode")
    public RemoteResult batchSaveOrUpdateFlowDiagramNode(@RequestBody List<FlowProcessTableDto> flowProcessTableDto) {
        Assert.notNull(flowProcessTableDto,"参数不能为空");
        flowProcessSystemService.batchSaveOrUpdateFlowDiagramNode(flowProcessTableDto);
        return new RemoteResult("success");
    }

    @RequestMapping(value = "/importFlowSystemByExcel", method = RequestMethod.POST)
    @ModDesc(desc = "一键导入批量导入分类数据", pDesc = "导入对象", pType = CiExcelInfoDto.class, rDesc = "导入明细", rType = ImportResultMessage.class)
    public void importFlowSystemByExcel(HttpServletRequest request, HttpServletResponse response,
                                     @RequestBody CiExcelInfoDto excelInfoDto) {
        ImportResultMessage importResultMessage = ciDesignSvc.importFlowSystemBatch(1L, excelInfoDto);
        ControllerUtils.returnJson(request, response, importResultMessage);
    }

    /**
     * 权限管理当前选中层级的下一层级全量流程条目
     * @return
     */
    @PostMapping("/getLevelFormsList")
    public RemoteResult getLevelFormsList(@RequestBody LevelFormsDto bean){
        Assert.notNull(bean,"参数不能为空");
        Map<String,Object> map= flowProcessSystemService.getLevelFormsList(bean);
        return new RemoteResult(map);
    }

    @PostMapping("/saveOrUpdate")
    public RemoteResult saveOrUpdate( @RequestBody List<CcCiInfo> ciInfos, @RequestParam(defaultValue = "DESIGN") LibType libType){
        if (ciInfos.size() == 0){
            return new RemoteResult("success");
        }
        String loginCode = SysUtil.getCurrentUserInfo().getLoginCode();
        List<Long> collect = ciInfos.stream().map(c -> c.getCi().getClassId()).collect(Collectors.toList());
        List<ESCIInfo> coverCiList = EamUtil.coverCiInfoList(ciInfos);
        ciSwitchSvc.saveOrUpdateBatchCI(coverCiList,collect,loginCode,loginCode,libType);
        return new RemoteResult("success");
    }
    @GetMapping("/getMoveFlowSystem")
    public RemoteResult getMoveFlowSystem(String ciCode){
        return new RemoteResult(flowProcessSystemService.getMoveFlowSystem(ciCode));
    }

    @GetMapping("/getMoveFlowSystemNew")
    public RemoteResult getMoveFlowSystemNew(String ciCode){
        return new RemoteResult(flowProcessSystemService.getMoveFlowSystemNew(ciCode));
    }

    @PostMapping("/moveFlowProcess")
    public RemoteResult moveFlowProcess(@RequestBody FlowProcessSystemDto flowProcessSystemDto){
        flowProcessSystemService.moveFlowProcess(flowProcessSystemDto);
        return new RemoteResult("success");
    }

    @PostMapping("/renameFlowProcess")
    public RemoteResult renameFlowProcess(@RequestBody FlowReNameDto flowReNameDto) {
        Assert.notNull(flowReNameDto.getCiId(), "ciId不能为空");
        if (StringUtils.isBlank(flowReNameDto.getFlowSystemName())) {
            throw new BinaryException("流程/流程组名称不能为空");
        }
        if (StringUtils.isBlank(flowReNameDto.getFlowCode())) {
            throw new BinaryException("流程/流程组编码不能为空");
        }
        flowProcessSystemService.renameFlowProcess(flowReNameDto);
        return new RemoteResult("success");
    }

    @GetMapping("/getFlowRoleTree")
    public RemoteResult getFlowRoleTree(String roleName){
        return new RemoteResult(flowProcessSystemService.getFlowRoleTree(roleName));
    }

    @GetMapping("getFlowDiagramLocalActiveList")
    public RemoteResult getFlowDiagramLocalActiveList(String ciCode,@RequestParam(defaultValue = "PRIVATE")LibType libType){
        return new RemoteResult(flowProcessSystemService.getFlowDiagramLocalActiveList(ciCode,libType));
    }

    /**
     * 批量保存场景和活动的关联关系
     * @param sceneActiveRltDto
     * @return
     */
    @PostMapping("/batchSaveSceneActiveRlt")
    public RemoteResult batchSaveSceneActiveRlt(@RequestBody SceneActiveRltDto sceneActiveRltDto){
        return new RemoteResult(flowProcessSystemService.batchSaveSceneActiveRlt(sceneActiveRltDto));
    }

    @GetMapping("/getSceneActiveRltRltByFlowCiCodeWithLibType")
    public RemoteResult getSceneActiveRltRltByFlowCiCodeWithLibType(String ciCode,@RequestParam(defaultValue = "PRIVATE")LibType libType){
        return new RemoteResult(flowProcessSystemService.getSceneActiveRltRltByFlowCiCodeWithLibType(ciCode,libType));
    }

    /**
     * 根据流程ciCode，查询出来关联的场景以及场景关联的活动数据
     */
    @GetMapping("/getSceneActiveRltByFlowCiCode")
    public RemoteResult getSceneActiveRltByFlowCiCode(String ciCode,@RequestParam(defaultValue = "PRIVATE")LibType libType){
        return new RemoteResult(flowProcessSystemService.getSceneActiveRltByFlowCiCode(ciCode,libType));
    }

    /**
     *  获取角色为流程所有者,流程责任人,流程编写人的数据权限
     */
    @GetMapping("/obtainDataPermissionsForProcessRole")
    public RemoteResult obtainDataPermissionsForProcessRole(){
        HashMap<String, List<SysRoleDataModuleRlt>> map = new HashMap<>();
        String[] flowRoleNames = {ProcessRoles.Process_OWNER.getProcessRolesName(), ProcessRoles.Process_RESPONSIBLE.getProcessRolesName(), ProcessRoles.Process_WRITER.getProcessRolesName()};
        for (String flowRoleName : flowRoleNames) {
            SysRole sysRole = esRoleSvc.getByName(1l, flowRoleName);
            CAuthBean cAuthBean = new CAuthBean();
            cAuthBean.setRoleId(sysRole.getId());
            cAuthBean.setDataModuleCode("BUTTON");
            List<SysRoleDataModuleRlt> authDataRoleByRoleId = iRoleApiSvc.getAuthDataRoleByRoleId(cAuthBean);
            map.put(flowRoleName, authDataRoleByRoleId);
        }
        return new RemoteResult(map);
    }


    /**
     * 末级流程发起审批
     */
    @ApiOperation(value = "末级流程发起审批")
    @PostMapping("/processLaunch")
    public RemoteResult processLaunch(@RequestBody ProcessLaunchDto processApprovalDto){
        return new RemoteResult(flowProcessSystemService.processLaunch(processApprovalDto));
    }

    /**
     * 末级流程审批
     */
    @ApiOperation(value = "末级流程审批")
    @PostMapping("/processApproval")
    public RemoteResult processApproval(@RequestBody ProcessApprovalDto processApprovalDto){
        flowProcessSystemService.processApproval(processApprovalDto);
        return new RemoteResult("success");
    }

    /**
     * 流程升版接口
     * @param processApprovalChangeDto
     * @return
     */
    @PostMapping("/changeFlowStatus")
    public RemoteResult changeFlowStatus(@RequestBody ProcessApprovalChangeDto processApprovalChangeDto){
        flowProcessSystemService.changeFlowStatus(processApprovalChangeDto);
        return new RemoteResult("success");
    }

    /**
     * 流程废止
     * @param processApprovalChangeDto
     * @return
     */
    @PostMapping("/abolishFlow")
    public RemoteResult abolishFlow(@RequestBody ProcessApprovalChangeDto processApprovalChangeDto) {
        String ciCode = processApprovalChangeDto.getCiCode();
        Boolean upVersion = processApprovalChangeDto.getUpVersion();
        if (upVersion == null) {
            upVersion = Boolean.TRUE;
        }
        flowProcessSystemService.abolishFlow(ciCode, upVersion);
        return new RemoteResult("success");
    }

    /**
     * 查询业务域下需要签发的流程
     * @return
     */
    @GetMapping("/findSingFlowList")
    public RemoteResult findSingFlowList(Long ciId,Integer pageNum,Integer pageSize,String word){
        if(pageNum==null||pageNum<1){
            pageNum = 1;
        }
        if(pageSize==null){
            pageSize = 3;
        }
        return new RemoteResult(flowProcessSystemService.findSingFlowList(ciId, pageNum, pageSize,word));
    }

    /**
     * 流程审批终止
     * @param processRequest
     * @return
     */
    @PostMapping("/stopFlowSystemApprove")
    public RemoteResult stopFlowSystemApprove(@RequestBody ProcessRequest processRequest) {
        flowProcessSystemService.stopFlowSystemApprove(processRequest.getProcessInstanceId()
                , processRequest.getDeleteReason());
        return new RemoteResult("success");
    }

    @GetMapping("/reSubmitRejectApproveFlow")
    public RemoteResult reSubmitRejectApproveFlow(String businessKey){
        return new RemoteResult(flowProcessSystemService.reSubmitRejectApproveFlow(businessKey));
    }

    /**
     * 流程撤回
     * @param processRequest
     * @return
     */
    @PostMapping("/withdrawFlowSystemApprove")
    public RemoteResult withdrawFlowSystemApprove(@RequestBody ProcessRequest processRequest) {

        String businessKey = processRequest.getBusinessKey();
        flowProcessSystemService.withdrawFlowSystemApprove(businessKey);
        return new RemoteResult("success");
    }

    /**
     * 获取流程绑定的末级流程ciinfo信息
     * @param processInstanceId
     * @return
     */
    @GetMapping("/getApproveCiInfoByProcessesInstanceId")
    public RemoteResult getApproveCiInfoByProcessesInstanceId(String processInstanceId){
        CcCiInfo ccCiInfo = flowProcessSystemService.getApproveCiInfoByProcessesInstanceId(processInstanceId);
        return new RemoteResult(ccCiInfo);
    }

    /**
     * 末级流程发起签发
     * @return
     */
    @PostMapping("/flowSingApproval")
    public RemoteResult flowSingApproval(@RequestBody FlowSignProcessVo flowSignPricessVo){
        TaskResponse taskResponse = flowProcessSystemService.flowSingApproval(flowSignPricessVo);
        return new RemoteResult(taskResponse);
    }

    /**
     * 获取当前审批签发列表
     */
    @GetMapping("/getFlowSignCiList")
    public RemoteResult getFlowSignCiList(String businessKey){
        List<FlowSystemProcessSingDataVo> flowSignCiList = flowProcessSystemService.getFlowSignCiList(businessKey);
        return new RemoteResult(flowSignCiList);
    }

    /**
     * 执行流程签发签发
     * @return
     */
    @PostMapping("/carryOutFlow")
    public RemoteResult carryOutFlow(@RequestBody SignFlowActionVo signFlowActionVo){
        Assert.notNull(signFlowActionVo.getSignIds(),"签发id不能为空");
        Assert.notNull(signFlowActionVo.getApprovalStatus(),"签发状态不能为空");
        Assert.notNull(signFlowActionVo.getTaskId(),"任务id不能为空");
        Boolean b = flowProcessSystemService.carryOutFlow(signFlowActionVo);
        if(b){
            //流程执行完毕返回end
            return new RemoteResult("end");
        }
        return new RemoteResult("success");
    }

    @GetMapping("/getFlowSystemApproveDataByProcessInstanceIdId")
    public RemoteResult getFlowSystemApproveDataByProcessInstanceIdId(String processInstanceId){
        FlowSystemApproveData flowSystemApproveDataByProcessInstanceIdId = flowProcessSystemService.getFlowSystemApproveDataByProcessInstanceIdId(processInstanceId);
        return new RemoteResult(flowSystemApproveDataByProcessInstanceIdId);
    }

    /**
     * 根据流程快照id获取末级流程快照数据
     * @param id
     * @return
     */
    @GetMapping("/getFlowSystemApproveDataById")
    public RemoteResult getFlowSystemApproveDataById(Long id){
        FlowSystemApproveData flowSystemApproveDataByProcessInstanceIdId = flowProcessSystemService.getFlowSystemApproveDataById(id);
        return new RemoteResult(flowSystemApproveDataByProcessInstanceIdId);
    }

    /**
     * 获取当前用户在流程体系的岗位和角色信息
     * @return
     */
    @GetMapping("/getFlowUserRoleAndPosition")
    public RemoteResult getFlowUserRoleAndPosition(){
        Map<String, List<CcCiInfo>> flowUserRoleAndPosition = flowProcessSystemService.getFlowUserRoleAndPosition();
        return new RemoteResult(flowUserRoleAndPosition);
    }

    /**
     * 获取流程体系资产统计
     * @return
     */
    @GetMapping("/getFlowAssertCount")
    public RemoteResult getFlowAssertCount(){
        Map<String, Collection<CcCiInfo>> flowAssertCount = flowProcessSystemService.getFlowAssertCount();
        return new RemoteResult(flowAssertCount);
    }

    @GetMapping("/getFlowWholeSceneCountInfo")
    public RemoteResult getFlowWholeSceneCountInfo(){
        FlowWholeSceneCountVo flowWholeSceneCountInfo = flowProcessSystemService.getFlowWholeSceneCountInfo();
        return new RemoteResult(flowWholeSceneCountInfo);
    }

    /**
     * 获取所有无流程图的流程
     * @return
     */
    @GetMapping("/getAllFlowWhereNoDiagram")
    public RemoteResult getAllFlowWhereNoDiagram(){
        return new RemoteResult(flowProcessSystemService.getAllFlowWhereNoDiagram());
    }

    /**
     * 复用末级流程图
     * @return
     */
    @PostMapping("/copyDiagramByFlowChart")
    public RemoteResult copyDiagramByFlowChart(@RequestBody FlowDiagramReuseDto flowDiagramReuseDto){

        return new RemoteResult(flowProcessSystemService.copyDiagramByFlowChart(flowDiagramReuseDto));
    }
    /**
     * 根据ciCode和流程图id查询绑定的关系列表
     * @param ciCode
     * @param diagramIds
     * @param libType
     * @return
     */
    @GetMapping("getFlowSystemAssociatedFeaturesNew")
    public RemoteResult getFlowSystemAssociatedFeaturesNew(String diagramIds,String ciCode, @RequestParam(defaultValue = "DESIGN") LibType libType){
        return new RemoteResult(flowProcessSystemService.getFlowSystemAssociatedFeaturesNew(diagramIds,ciCode,libType));
    }

    /**
    * 档案管理table
    * @Param: ciCode
    * @return:
    */
    @GetMapping("obtainTheProcessFile")
    public RemoteResult obtainTheProcessFile(String ciCode, @RequestParam(defaultValue = "PRIVATE") LibType libType){
         Assert.notNull(ciCode,"ciCode不能为空");
        return new RemoteResult(flowProcessSystemService.obtainTheProcessFile(ciCode,libType));
    }

    /**
     * 权限流程树
     * @return
     */
    @GetMapping("getPermissionsProcessTree")
    public RemoteResult getPermissionsProcessTree(){
        Collection<FlowProcessSystemTreeDto> flowSystemTreeNew = flowProcessSystemService.getFlowSystemTreeNew(Boolean.FALSE,Boolean.FALSE);
        FlowProcessSystemTreeDto flowProcessSystemTreeDto = new FlowProcessSystemTreeDto();
        flowProcessSystemTreeDto.setFlowSystemName("流程目录");
        flowProcessSystemTreeDto.setCiCode("流程目录");
        flowProcessSystemTreeDto.setChild(flowSystemTreeNew);
        return new RemoteResult(flowProcessSystemTreeDto);
    }

    /**
     * 根据ciCode和流程图id查询绑定的关系列表
     * @param ciCode
     * @param diagramIds
     * @param libType
     * @return
     */
    @GetMapping("getFlowFileNew")
    public RemoteResult getFlowFileNew(String diagramIds,String ciCode, @RequestParam(defaultValue = "DESIGN") LibType libType){
        return new RemoteResult(flowProcessSystemService.getFlowFileNew(diagramIds,ciCode,libType));
    }

    /**
    * @Description: 导出流程文件word
    * @Param: ciCode
    * @Param: diagramIds
    * @Param: id   流程快照id
    * @Param: libType
    */
    @GetMapping("/exportProcessFile")
    public ResultMsg exportProcessFile(HttpServletResponse response, @RequestParam String ciCode,
                String diagramIds,@RequestParam(defaultValue = "DESIGN") LibType libType , String id ) {
        String path = flowProcessSystemService.exportProcessFile(response, ciCode, diagramIds,id, libType);
        return new ResultMsg("导出成功",path);
    }

    /**
     * @Description: 确认档案,指标,风险数据是否变更
     * @Param: ciCode
     * @Param: classCode
     */
    @GetMapping("/whetherDataIsChanged")
    public RemoteResult whetherDataIsChanged(String ciCode, String classCode) {
        return new RemoteResult(flowProcessSystemService.whetherDataIsChanged(ciCode, classCode));
    }

    @PostMapping("mergePullAssociationCi")
    public RemoteResult  mergePullAssociationCi(@RequestParam String ciCode,String classCode){
        Assert.notNull(ciCode,"ciCode不能为空");
        return new RemoteResult(flowProcessSystemService.mergePullAssociationCi(ciCode,classCode));
    }

    /**
     * 获取所有顶级流程组
     * @return
     */
    @GetMapping("getTopTierProcesses")
    public RemoteResult getTopTierProcesses() {

        return new RemoteResult(flowProcessSystemService.getTopTierProcesses());
    }

    /**
     * 导出流程地图
     * @return
     */
    @PostMapping("exportFlowSystemExcel")
    public ResponseEntity<byte[]> exportFlowSystemExcel(@RequestBody Map<String,List<String>> map) {
        File file = flowProcessSystemService.exportFlowSystemExcel(map.get("targetCiCode"));
        return ExcelUtil.returnResAndeDelFile(file, "流程地图.xlsx");
    }

    /**
     * 添加指标监测信息
     * @return
     */
    @PostMapping("addMetricMonitoringInformation")
    public RemoteResult  addMetricMonitoringInformation(@RequestBody List<IndicatorDetectionInformationAssociation> dto){
        return new RemoteResult(flowProcessSystemService.addMetricMonitoringInformation(dto));
    }

    /**
     * 查询指标监测信息
     * @return
     */
    @GetMapping("queryIndicatorMonitoringInformation")
    public RemoteResult  queryIndicatorMonitoringInformation(@RequestParam String ciCode){
        Assert.notNull(ciCode,"ciCode不能为空");
        return new RemoteResult(flowProcessSystemService.queryIndicatorMonitoringInformation(ciCode));
    }
    /**
     * 删除指标监测信息
     * @return
     */
    @GetMapping("deleteIndicatorMonitoringInformation")
    public RemoteResult deleteIndicatorMonitoringInformation(@RequestParam Long id) {
        Assert.notNull(id, "id不能为空");
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        // 构建删除条件：同时匹配id和创建人
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery()
                .filter(QueryBuilders.termQuery("id", id))
                .filter(QueryBuilders.termQuery("creator.keyword", currentUserInfo.getLoginCode()));
        // 执行删除操作
        informationAssociationDao.deleteByQuery(queryBuilder, true);
        return new RemoteResult(1);
    }
    /**
     * 导出业务域手册
     * @param response
     * @param map
     * @return
     */
    @PostMapping("exportFlowManual")
    public ResultMsg  exportFlowManual(HttpServletResponse response, @RequestBody  Map<String,String> map){
    	// 从map中提取数据
        String ciCode = map.get("ciCode");
        String base64File = map.get("base64File");
    	Assert.notNull(ciCode,"ciCode不能为空");
        String path = flowProcessSystemService.exportFlowManual(response,ciCode,base64File);
        return new ResultMsg("导出成功",path);

    }

/*
* 查看历史版本信息时更新快照信息中的流程文件信息
* */
    @GetMapping("processFileAddData")
    public RemoteResult processFileAddData(Long id){
        Assert.notNull(id,"快照id不能为空");
        return new RemoteResult(flowProcessSystemService.processFileAddData(id));
    }

    /**
    * @Description: 查询流程树上流程组和流程数量
    * @Param:
    * @return:
    */
    @GetMapping("queryProcessQuantity")
    public RemoteResult queryProcessQuantity(){
        return new RemoteResult(flowProcessSystemService.queryProcessQuantity());
    }
    /**
     * 获取所有运行情况的流程
     * @return
     */
    @GetMapping("getAllFlowWhereRunSituation")
    public RemoteResult getAllFlowWhereRunSituation(String sourceId){
        Assert.notNull(sourceId,"系统来源id不能为空");
        return new RemoteResult(flowProcessSystemService.getAllFlowWhereRunSituation(sourceId));
    }
}
