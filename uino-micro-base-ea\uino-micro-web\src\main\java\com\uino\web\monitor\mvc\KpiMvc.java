package com.uino.web.monitor.mvc;

import java.io.File;
import java.util.Set;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import com.uino.bean.permission.base.SysUser;
import com.uino.util.sys.SysUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.binary.framework.util.ControllerUtils;
import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uinnova.product.vmdb.provider.kpi.bean.CcKpiInfo;
import com.uino.bean.cmdb.business.ImportResultMessage;
import com.uino.bean.monitor.base.ESKpiInfo;
import com.uino.bean.monitor.buiness.KpiRltBindDto;
import com.uino.bean.monitor.buiness.SearchKpiBean;
import com.uino.bean.sys.query.ExportDictionaryDto;
import com.uino.api.client.monitor.IKpiApiSvc;

/**
 * kpi指标相关
 * 
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping(value = "/monitor/kpi")
public class KpiMvc {

    @Autowired
    private IKpiApiSvc kpiApiSvc;

    /**
     * 分页查询kpi信息
     * 
     * @param reqDto
     * @param request
     * @param response
     */
    @PostMapping("queryKpiInfoPage")
    @ModDesc(desc = "分页查询指标数据", pDesc = "查询条件", pType = SearchKpiBean.class, rDesc = "指标分页数据", rType = Page.class, rcType = CcKpiInfo.class)
    public void queryKpiInfoPage(@RequestBody SearchKpiBean reqDto, HttpServletRequest request,
            HttpServletResponse response) {
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        reqDto.setDomainId(currentUserInfo.getDomainId());
        ControllerUtils.returnJson(request, response, kpiApiSvc.queryKpiInfoPage(reqDto));
    }

    /**
     * 持久化kpi信息
     * 
     * @param reqDto
     * @param request
     * @param response
     */
    @PostMapping("saveOrUpdate")
    @ModDesc(desc = "保存或更新指标数据", pDesc = "指标对象", pType = CcKpiInfo.class, rDesc = "指标数据id", rType = Long.class)
    public void saveOrUpdate(@RequestBody ESKpiInfo reqDto, HttpServletRequest request, HttpServletResponse response) {
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        reqDto.setDomainId(currentUserInfo.getDomainId());
        Long retult = kpiApiSvc.saveOrUpdate(reqDto);
        ControllerUtils.returnJson(request, response, retult);
    }

    /**
     * 删除kpi信息
     * 
     * @param kpiIds
     * @param request
     * @param response
     */
    @PostMapping("deleteByKpiIds")
    @ModDesc(desc = "根据id批量删除指标", pDesc = "指标id集合", pType = Set.class, pcType = Long.class, rDesc = "操作是否成功", rType = Boolean.class)
    public void deleteByKpiIds(@RequestBody Set<Long> kpiIds, HttpServletRequest request,
            HttpServletResponse response) {
        kpiApiSvc.deleteByKpiIds(kpiIds);
        ControllerUtils.returnJson(request, response, true);
    }

    @PostMapping("/exportKpiInfos")
    @ModDesc(desc = "导出指标数据", pDesc = "是否导出模板", pcType = Long.class, rDesc = "导出Excel文件", rType = File.class)
    public void exportKpiInfos(HttpServletRequest request, HttpServletResponse response, @RequestBody ExportDictionaryDto dto) {
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        ControllerUtils.returnResource(request, response, kpiApiSvc.exportKpiInfos(currentUserInfo.getDomainId(),dto.getIsTpl()));
    }

    @PostMapping("/importKpiInfos")
    @ModDesc(desc = "导入指标数据", pDesc = "导入文件", rDesc = "导入明细", rType = ImportResultMessage.class)
    public void importKpiInfos(HttpServletRequest request, HttpServletResponse response, @RequestParam("file") MultipartFile file) {
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        ControllerUtils.returnJson(request, response, kpiApiSvc.importKpiInfos(currentUserInfo.getDomainId(), file));
    }

    @PostMapping("/bindCiClassRltToKpiInfo")
    @ModDesc(desc = "绑定指标与分类关系", pDesc = "关系绑定对象", rDesc = "是否绑定成功", rType = Boolean.class)
    public void bindCiClassRltToKpiInfo(HttpServletRequest request, HttpServletResponse response, @RequestBody KpiRltBindDto dto) {
        kpiApiSvc.bindCiClassRltToKpiInfo(dto);
        ControllerUtils.returnJson(request, response, true);
    }

    @PostMapping("/delCiClassRltToKpiInfo")
    @ModDesc(desc = "解除指标与分类关系", pDesc = "关系绑定对象", rDesc = "是否绑定成功", rType = Boolean.class)
    public void delCiClassRltToKpiInfo(HttpServletRequest request, HttpServletResponse response, @RequestBody KpiRltBindDto dto) {
        kpiApiSvc.delCiClassRltToKpiInfo(dto);
        ControllerUtils.returnJson(request, response, true);
    }

}
