<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	   xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">


	<bean class="com.uinnova.product.eam.db.impl.VcDiagramDirDaoDefinition" />
	<bean class="com.uinnova.product.eam.db.impl.VcDiagramDaoDefinition" />
	<bean class="com.uinnova.product.eam.db.impl.VcDiagramEleDaoDefinition" />
	<bean class="com.uinnova.product.eam.db.impl.VcDiagramCiAttrDispDaoDefinition" />
	<bean class="com.uinnova.product.eam.db.impl.VcDiagramEleVersionDaoDefinition" />
	<bean class="com.uinnova.product.eam.db.impl.VcDiagramCiAttrVersionDaoDefinition" />
	<bean class="com.uinnova.product.eam.db.impl.VcDiagramGroupDaoDefinition" />
	<bean class="com.uinnova.product.eam.db.impl.DcCombDiagramDaoDefinition" />

	<bean class="com.uinnova.product.eam.db.impl.VcDiagramItemOpteDaoDefinition" />
	<bean class="com.uinnova.product.eam.db.impl.VcCommentDaoDefinition" />
	<bean class="com.uinnova.product.eam.db.impl.VcBaseConfigDaoDefinition"></bean>
	<bean class="com.uinnova.product.eam.db.impl.VcDiagramEnshDaoDefinition"></bean>
	<bean class="com.uinnova.product.eam.db.impl.VcCiLinkedDaoDefinition"></bean>
	<bean class="com.uinnova.product.eam.db.impl.VcDiagramLogDaoDefinition"></bean>
	<bean class="com.uinnova.product.eam.db.impl.VcDiagramOpDaoDefinition"></bean>
	<bean class="com.uinnova.product.eam.db.impl.VcNodeLinkedDaoDefinition"></bean>
	<bean class="com.uinnova.product.eam.db.impl.VcNodeLinkedPathDaoDefinition"></bean>
	
	<bean class="com.uinnova.product.eam.db.impl.VcClassAddAttrDaoDefinition"></bean>

	<bean class="com.uinnova.product.eam.db.impl.VcMetaModelDiagramDaoDefinition"></bean>
	<bean class="com.uinnova.product.eam.db.impl.VcDiagramDirRelationDaoDefinition"></bean>

	<bean class="com.uinnova.product.eam.db.impl.VcDiagramVersionDaoDefinition" />

	<bean class="com.uinnova.product.eam.db.impl.VcDiagramVersionDaoImpl" parent="dao.parent.eam" >
		<property name="daoDefinition" ref="com.uinnova.product.eam.db.impl.VcDiagramVersionDaoDefinition" />
	</bean>

	<bean class="com.uinnova.product.eam.db.impl.VcDiagramDirDaoImpl" parent="dao.parent.eam" >
		<property name="daoDefinition" ref="com.uinnova.product.eam.db.impl.VcDiagramDirDaoDefinition" />
	</bean>
	<bean class="com.uinnova.product.eam.db.impl.VcDiagramDaoImpl" parent="dao.parent.eam" >
		<property name="daoDefinition" ref="com.uinnova.product.eam.db.impl.VcDiagramDaoDefinition" />
	</bean>
	<bean class="com.uinnova.product.eam.db.impl.VcDiagramEleDaoImpl" parent="dao.parent.eam" >
		<property name="daoDefinition" ref="com.uinnova.product.eam.db.impl.VcDiagramEleDaoDefinition" />
	</bean>
	<bean class="com.uinnova.product.eam.db.impl.VcDiagramCiAttrDispDaoImpl" parent="dao.parent.eam" >
		<property name="daoDefinition" ref="com.uinnova.product.eam.db.impl.VcDiagramCiAttrDispDaoDefinition" />
	</bean>
	<bean class="com.uinnova.product.eam.db.impl.VcDiagramEleVersionDaoImpl" parent="dao.parent.eam" >
		<property name="daoDefinition" ref="com.uinnova.product.eam.db.impl.VcDiagramEleVersionDaoDefinition" />
	</bean>
	<bean class="com.uinnova.product.eam.db.impl.VcDiagramCiAttrVersionDaoImpl" parent="dao.parent.eam" >
		<property name="daoDefinition" ref="com.uinnova.product.eam.db.impl.VcDiagramCiAttrVersionDaoDefinition" />
	</bean>
	<bean class="com.uinnova.product.eam.db.impl.DcCombDiagramDaoImpl" parent="dao.parent.eam" >
		<property name="daoDefinition" ref="com.uinnova.product.eam.db.impl.DcCombDiagramDaoDefinition" />
	</bean>

	<bean class="com.uinnova.product.eam.db.impl.VcDiagramItemOpteDaoImpl" parent="dao.parent.eam" >
		<property name="daoDefinition" ref="com.uinnova.product.eam.db.impl.VcDiagramItemOpteDaoDefinition" />
	</bean>
	<bean class="com.uinnova.product.eam.db.impl.VcCommentDaoImpl" parent="dao.parent.eam" >
		<property name="daoDefinition" ref="com.uinnova.product.eam.db.impl.VcCommentDaoDefinition" />
	</bean>
	<bean class="com.uinnova.product.eam.db.impl.VcBaseConfigDaoImpl" parent="dao.parent.eam">
		<property name="daoDefinition" ref="com.uinnova.product.eam.db.impl.VcBaseConfigDaoDefinition"></property>
	</bean>
	<bean class="com.uinnova.product.eam.db.impl.VcDiagramEnshDaoImpl" parent="dao.parent.eam">
		<property name="daoDefinition" ref="com.uinnova.product.eam.db.impl.VcDiagramEnshDaoDefinition"></property>
	</bean>
	<bean class="com.uinnova.product.eam.db.impl.VcCiLinkedDaoImpl" parent="dao.parent.eam">
		<property name="daoDefinition" ref="com.uinnova.product.eam.db.impl.VcCiLinkedDaoDefinition"></property>
	</bean>
	<bean class="com.uinnova.product.eam.db.impl.VcDiagramLogDaoImpl" parent="dao.parent.eam">
		<property name="daoDefinition" ref="com.uinnova.product.eam.db.impl.VcDiagramLogDaoDefinition"></property>
	</bean>
	<bean class="com.uinnova.product.eam.db.impl.VcDiagramOpDaoImpl" parent="dao.parent.eam">
		<property name="daoDefinition" ref="com.uinnova.product.eam.db.impl.VcDiagramOpDaoDefinition"></property>
	</bean>
	<bean class="com.uinnova.product.eam.db.impl.VcNodeLinkedDaoImpl" parent="dao.parent.eam">
		<property name="daoDefinition" ref="com.uinnova.product.eam.db.impl.VcNodeLinkedDaoDefinition"></property>
	</bean>
	<bean class="com.uinnova.product.eam.db.impl.VcNodeLinkedPathDaoImpl" parent="dao.parent.eam">
		<property name="daoDefinition" ref="com.uinnova.product.eam.db.impl.VcNodeLinkedPathDaoDefinition"></property>
	</bean>

	<bean class="com.uinnova.product.eam.db.impl.VcClassAddAttrDaoImpl" parent="dao.parent.eam">
		<property name="daoDefinition" ref="com.uinnova.product.eam.db.impl.VcClassAddAttrDaoDefinition"></property>
	</bean>
	
	<bean class="com.uinnova.product.eam.db.impl.VcDiagramDirRelationDaoImpl" parent="dao.parent.eam">
		<property name="daoDefinition" ref="com.uinnova.product.eam.db.impl.VcDiagramDirRelationDaoDefinition"></property>
	</bean>
	
	<bean class="com.uinnova.product.eam.db.impl.VcMetaModelDiagramDaoImpl" parent="dao.parent.eam">
		<property name="daoDefinition" ref="com.uinnova.product.eam.db.impl.VcMetaModelDiagramDaoDefinition"></property>
	</bean>


	<bean class="com.uinnova.product.eam.db.impl.VcDiagramNotifyDaoDefinition" />
	<bean class="com.uinnova.product.eam.db.impl.VcDiagramNotifyDaoImpl" parent="dao.parent.eam" >
		<property name="daoDefinition" ref="com.uinnova.product.eam.db.impl.VcDiagramNotifyDaoDefinition" />
	</bean>

	<bean class="com.uinnova.product.eam.db.impl.VcDiagramOperationRecordDaoDefinition" />
	<bean class="com.uinnova.product.eam.db.impl.VcDiagramOperationRecordDaoImpl" parent="dao.parent.eam" >
		<property name="daoDefinition" ref="com.uinnova.product.eam.db.impl.VcDiagramOperationRecordDaoDefinition" />
	</bean>
</beans>

