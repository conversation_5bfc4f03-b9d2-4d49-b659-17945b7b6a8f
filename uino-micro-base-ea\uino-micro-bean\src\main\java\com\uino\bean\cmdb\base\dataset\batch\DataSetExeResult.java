package com.uino.bean.cmdb.base.dataset.batch;

import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;

import java.io.Serializable;

/**
 * 数据集执行结果
 *
 * <AUTHOR>
 * @version 2020-4-24
 */
public class DataSetExeResult implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    private Long id;

    /**
     * 数据集ID
     */
    private Long dataSetId;


    private CcCiInfo startCi;

    private Long domainId;


    /**
     * 数据集执行结果
     */
    private SimpleFriendInfo simpleFriendInfo;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getDataSetId() {
        return dataSetId;
    }

    public void setDataSetId(Long dataSetId) {
        this.dataSetId = dataSetId;
    }

    public CcCiInfo getStartCi() {
        return startCi;
    }

    public void setStartCi(CcCiInfo startCi) {
        this.startCi = startCi;
    }

    public SimpleFriendInfo getSimpleFriendInfo() {
        return simpleFriendInfo;
    }

    public void setSimpleFriendInfo(SimpleFriendInfo simpleFriendInfo) {
        this.simpleFriendInfo = simpleFriendInfo;
    }

    public Long getDomainId() {
        return domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }

    public DataSetExeResult() {
    }
}