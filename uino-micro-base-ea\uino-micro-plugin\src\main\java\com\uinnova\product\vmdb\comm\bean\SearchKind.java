package com.uinnova.product.vmdb.comm.bean;

import java.io.Serializable;

/**
 * 
 * <AUTHOR>
 *
 */
public class SearchKind implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 搜索ID **/
    private Long id;

    /** 搜索名称 **/
    private String name;

    /** 类型 1=标签 2=分类 **/
    private Integer type;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

}
