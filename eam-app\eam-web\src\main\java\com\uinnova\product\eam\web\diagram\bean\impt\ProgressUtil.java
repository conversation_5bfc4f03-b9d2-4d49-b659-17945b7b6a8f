package com.uinnova.product.eam.web.diagram.bean.impt;

import com.binary.core.util.BinaryUtils;

public class ProgressUtil {

    /**
     * 标记是否有程序正在导入CI
     */
    public static boolean ciImporting = false;

    /**
     * 创建一个进度并添加到进度map中
     * 
     * @param clazz
     *            具体使用的进度类型
     * @param key
     * @param totalRecords
     * @return
     */
    public static ProgressBean createProgress(String key, int totalRecords) {
        ProgressBean progressBean = new ProgressBean();
        if (!BinaryUtils.isEmpty(key)) {
            progressBean.setKey(key);
            progressBean.setTotalRecords(totalRecords);
            ProgressCatch.putProgress(progressBean);
        }
        return progressBean;
    }

    /**
     * 创建一个进度并添加到进度map中
     * 
     * @param clazz
     * @param key
     * @return
     */
    public static ProgressBean createProgress(String key) {
        return createProgress(key, 0);
    }

    /**
     * 更新进度状态
     * 
     * @param status
     */
    public static void changeProgressBeanStatus(int status) {
        Thread currentThread = Thread.currentThread();
        if (currentThread instanceof ProgressThread) {
            ProgressThread progressThread = (ProgressThread) currentThread;
            progressThread.changeProgressBeanStatus(status);
        }
    }

    /**
     * 更新结果
     * 
     * @param resultData
     */
    public static void changeProgressResultData(Object resultData) {
        Thread currentThread = Thread.currentThread();
        if (currentThread instanceof ProgressThread) {
            ProgressThread progressThread = (ProgressThread) currentThread;
            progressThread.changeProgressResultData(resultData);
        }
    }

}
