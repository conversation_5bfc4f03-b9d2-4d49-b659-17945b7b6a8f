package com.uino.provider.feign.cmdb;

import com.uino.bean.cmdb.base.dataset.DataSetMallApiRelationRuleExternal;
import com.uino.bean.cmdb.business.dataset.FriendInfo;
import com.uino.bean.cmdb.business.dataset.UpDownAttrCdt;
import com.uino.provider.feign.config.BaseFeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @data 2019/8/7 16:30.
 */
@Component
@FeignClient(name = "${feign-server-name:tarsier-platform}", path = "${feign-server-path:tarsier-platform}/feign/graphAnaly", configuration = {BaseFeignConfig.class})
public interface GraphAnalysisFeign {

    /**
     * 上下N层(多入口CI)
     *
     * @param domainId      domainId
     * @param startCiIds    起始ciIds
     * @param ciConditions  CI过滤条件
     * @param rltConditions 关系过滤条件
     * @param rltLvls       关系子CODE过滤条件
     * @param upLevel       上几层
     * @param downLevel     下几层
     * @param hasAttr       是否需要属性
     * @return FriendInfo
     */
    @PostMapping("queryCiUpDownByCiIds")
    public FriendInfo queryCiUpDownByCiIds(@RequestParam(value = "domainId", required = false) Long domainId, @RequestParam(value = "startCiIds", required = false) List<Long> startCiIds, @RequestParam(value = "ciConditions", required = false) List<UpDownAttrCdt> ciConditions, @RequestParam(value = "rltConditions", required = false) List<UpDownAttrCdt> rltConditions,
                                           @RequestParam(value = "rltLvls", required = false) List<Long> rltLvls, @RequestParam(value = "upLevel", required = false) Integer upLevel, @RequestParam(value = "downLevel", required = false) Integer downLevel, @RequestParam(value = "hasAttr", required = false) Boolean hasAttr);

    /**
     * 上下N层(单入口CI)
     *
     * @param domainId      domainId
     * @param startCiId     起始ciId
     * @param ciConditions  CI过滤条件
     * @param rltConditions 关系过滤条件
     * @param rltLvls       关系子CODE过滤条件
     * @param upLevel       上几层
     * @param downLevel     下几层
     * @param hasAttr       是否需要属性
     * @return FriendInfo
     */
    @PostMapping("queryCiUpDownByCiId")
    public FriendInfo queryCiUpDownByCiId(@RequestParam(value = "domainId", required = false) Long domainId, @RequestParam(value = "startCiId", required = false) Long startCiId, @RequestParam(value = "ciConditions", required = false) List<UpDownAttrCdt> ciConditions, @RequestParam(value = "rltConditions", required = false) List<UpDownAttrCdt> rltConditions,
                                          @RequestParam(value = "rltLvls", required = false) List<Long> rltLvls, @RequestParam(value = "upLevel", required = false) Integer upLevel, @RequestParam(value = "downLevel", required = false) Integer downLevel, @RequestParam(value = "hasAttr", required = false) Boolean hasAttr);
    
    /**
     * 通过关系遍历规则查询数据
     *
     * @param enter     起始ci
     * @param rule  	关系遍历规则
     * @return FriendInfo
     */
    @PostMapping("queryFriendByCiUsingRule")
    public FriendInfo queryFriendByCiUsingRule(@RequestParam(value = "enter", required = true) List<String> enter, @RequestParam(value = "rule", required = true) DataSetMallApiRelationRuleExternal rule);

}
