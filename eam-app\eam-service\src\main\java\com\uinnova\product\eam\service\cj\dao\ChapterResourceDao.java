package com.uinnova.product.eam.service.cj.dao;

import com.uinnova.product.eam.model.cj.domain.ChapterResource;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;

/**
 * <AUTHOR>
 * @Classname ChapterResourceDao
 * @Date 2022/6/21 16:00
 */
@Component
public class ChapterResourceDao extends AbstractESBaseDao<ChapterResource,ChapterResource> {
    @Override
    public String getIndex() {
        return "uino_cj_chapter_resource";
    }

    @Override
    public String getType() {
        return getIndex();
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }
}
