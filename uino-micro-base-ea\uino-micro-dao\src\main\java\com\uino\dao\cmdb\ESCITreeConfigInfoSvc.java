package com.uino.dao.cmdb;

import jakarta.annotation.PostConstruct;

import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.uino.dao.AbstractESBaseDao;
import com.uino.dao.ESConst;
import com.uino.bean.cmdb.base.ESCITreeConfigInfo;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
public class ESCITreeConfigInfoSvc extends AbstractESBaseDao<ESCITreeConfigInfo, JSONObject> {

	@Override
	public String getIndex() {
		// TODO Auto-generated method stub
		return ESConst.INDEX_CMDB_CI_TREE_CONFIG;
	}

	@Override
	public String getType() {
		// TODO Auto-generated method stub
		return ESConst.INDEX_CMDB_CI_TREE_CONFIG;
	}

	@PostConstruct
	public void init() {
		super.initIndex();
	}

}
