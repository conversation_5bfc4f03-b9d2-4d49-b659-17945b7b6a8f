package com.uino.role.svc;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.elasticsearch.index.query.QueryBuilders;
import org.junit.Assert;
import org.junit.Before;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import com.binary.jdbc.Page;
import com.uino.dao.permission.ESRoleSvc;
import com.uino.service.permission.microservice.impl.RoleSvc;
import com.uino.dao.permission.rlt.ESOrgRoleRltSvc;
import com.uino.dao.permission.rlt.ESPerssionCommSvc;
import com.uino.bean.permission.base.SysOrgRoleRlt;
import com.uino.bean.permission.base.SysRole;

@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("provider-local")
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class GetRolesByOrgIdTest {
	@Autowired
	private RoleSvc testSvc;
	@MockBean
	private ESOrgRoleRltSvc esOrgRoleRltSvc;
	@MockBean
	private ESRoleSvc esRoleSvc;
    @MockBean
    private ESPerssionCommSvc commSvc;

	@Before
	public void before() {
		Page<SysOrgRoleRlt> maxUp = new Page<>();
		maxUp.setTotalRows(1111111L);
		Mockito.when(esOrgRoleRltSvc.getListByQuery(Mockito.anyInt(), Mockito.anyInt(),
				Mockito.eq(QueryBuilders.termQuery("orgId", 9L)))).thenReturn(maxUp);
		Page<SysOrgRoleRlt> nullPage = new Page<>();
		Mockito.when(esOrgRoleRltSvc.getListByQuery(Mockito.anyInt(), Mockito.anyInt(),
				Mockito.eq(QueryBuilders.termQuery("orgId", 8L)))).thenReturn(nullPage);

		Page<SysOrgRoleRlt> legalPage = new Page<>(1, 10, 1, 1,
				Collections.singletonList(SysOrgRoleRlt.builder().roleId(1L).build()));
		Mockito.when(esOrgRoleRltSvc.getListByQuery(Mockito.anyInt(), Mockito.anyInt(),
				Mockito.eq(QueryBuilders.termQuery("orgId", 1L)))).thenReturn(legalPage);

		Page<SysOrgRoleRlt> noLegalPage = new Page<>(1, 10, 1, 1,
				Collections.singletonList(SysOrgRoleRlt.builder().roleId(2L).build()));
		Mockito.when(esOrgRoleRltSvc.getListByQuery(Mockito.anyInt(), Mockito.anyInt(),
				Mockito.eq(QueryBuilders.termQuery("orgId", 2L)))).thenReturn(noLegalPage);
		Set<Long> roleIds = new HashSet<>();
		roleIds.add(1L);
		Page<SysRole> roles = new Page<>(1, 1, 1, 1, Collections.singletonList(new SysRole()));

		Mockito.when(esRoleSvc.getListByQuery(Mockito.anyInt(), Mockito.anyInt(),
				Mockito.eq(QueryBuilders.termsQuery("id", roleIds)))).thenReturn(roles);

		Set<Long> nullRoleIds = new HashSet<>();
        nullRoleIds.add(2L);
		Page<SysRole> nullRoles = new Page<>(1, 1, 0, 1, new ArrayList<>());
		Mockito.when(esRoleSvc.getListByQuery(Mockito.anyInt(), Mockito.anyInt(),
            Mockito.eq(QueryBuilders.termsQuery("id", nullRoleIds)))).thenReturn(nullRoles);

        Mockito.when(commSvc.getOrgRoleRltByOrgIds(Mockito.any())).thenReturn(noLegalPage.getData());
	}

	@Test(expected = IllegalArgumentException.class)
	public void test01() {
		testSvc.getRolesByOrgId(null);
	}

    @Test
	public void test02() {
		testSvc.getRolesByOrgId(9L);
	}

	@Test
	public void test03() {
		List<SysRole> rep = testSvc.getRolesByOrgId(8L);
		Assert.assertTrue(rep == null || rep.size() <= 0);
	}

	@Test
	public void test04() {
		testSvc.getRolesByOrgId(1L);
	}

	@Test
	public void test05() {
		Page<SysRole> nullRoles = new Page<>(1, 1, 0, 1, new ArrayList<>());
		Mockito.when(esRoleSvc.getListByQuery(Mockito.anyInt(), Mockito.anyInt(), Mockito.any())).thenReturn(nullRoles);
		testSvc.getRolesByOrgId(2L);
	}
}
