package com.uino.bean.cmdb.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Title: ESShow3dAttributeDto
 * @Description:
 * @Author: YGQ
 * @Create: 2021-04-28 14:37
 **/
@ApiModel(value = "创建分类是否增加3D属性", description = "创建分类是否增加3D属性")
public class ESShow3dAttributeDto {

    @ApiModelProperty(value = "创建分类是否增加3D属性", example = "false")
    private Boolean isShow3dAttribute = false;

    @ApiModelProperty(value = "默认3D图标路径", example = "/122/default_3d.jpg")
    private String defaultIcon = "/122/default_3d.jpg";

    public void setShow3dAttribute(Boolean show3dAttribute) {
        isShow3dAttribute = show3dAttribute;
    }

    public void setDefaultIcon(String rsmSlaveRoot) {
        this.defaultIcon = rsmSlaveRoot + this.defaultIcon;
    }

    public Boolean getShow3dAttribute() {
        return isShow3dAttribute;
    }

    public String getDefaultIcon() {
        return defaultIcon;
    }
}
