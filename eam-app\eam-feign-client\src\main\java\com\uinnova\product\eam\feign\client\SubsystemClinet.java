package com.uinnova.product.eam.feign.client;

import com.binary.jdbc.Page;
import com.uinnova.product.eam.comm.bean.SubsystemEditData;
import com.uinnova.product.eam.comm.bean.SubsystemTemporary;
import com.uinnova.product.eam.feign.FeignConst;
import com.uinnova.product.eam.model.SubsystemEditDataDto;
import com.uinnova.product.eam.model.asset.SubsystemDTO;
import com.uino.bean.cmdb.base.ESCIInfo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.util.List;
import java.util.Map;

@Component
@FeignClient(name = FeignConst.APPLICATION,path = FeignConst.SUBSYSTEM_PATH)
public interface SubsystemClinet {

    SubsystemDTO getSubsystemByCiCode(String ciCode);

    Object createSubsystem(SubsystemDTO subsystemDTO);

    Page<ESCIInfo> searchSubsystemByCiCode(Long[] ciClassIds, String[] datas, Integer pageSize, Integer pageNum,Boolean recent, Boolean mostClick);

    SubsystemDTO getSubsystemByCiId(Long ciId);

    List<SubsystemTemporary> getTemporaryStorageSubsystem(String userId);

    Long temporaryStorageSubsystem(SubsystemTemporary subsystemTemporary);

    void deleteTemporaryStorageSubsystem(Long tempId);

    SubsystemTemporary getTemporaryStorageSubsystemById(Long tempId);

    Boolean subsystemCodeisExist(Long ciClassId, String subsystemCode);

    Long getTotalNum(String subSystemName);

    List<ESCIInfo> getSubsystemByLoginCode(String loginCode);

    Map getChartData(String className) throws ParseException;

    Long getSubsystemTotalNumber();

    Boolean subsystemNameisExist(Long ciClassId, String subsystemName);

    Map<String, Object> subsystemEditApprove(SubsystemEditData subsystemEditData);

    SubsystemEditDataDto getEditApprove(Long id);
}
