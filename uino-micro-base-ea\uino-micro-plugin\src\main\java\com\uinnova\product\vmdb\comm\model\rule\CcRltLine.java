package com.uinnova.product.vmdb.comm.model.rule;

import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;

/**
 * 
 * <AUTHOR>
 *
 */
@Comment("关系线表[CC_RLT_LINE]")
public class CcRltLine implements EntityBean {
    private static final long serialVersionUID = 1L;

    @Comment("ID[ID]")
    private Long id;

    @Comment("所属定义[DEF_ID]")
    private Long defId;

    @Comment("定义类型[DEF_TYPE]    定义类型:1=朋友圈")
    private Integer defType;

    @Comment("参照分类[CLASS_ID]")
    private Long classId;

    @Comment("起始分类ID[CLS_START_ID]")
    private Long clsStartId;

    @Comment("结束分类ID[CLS_END_ID]")
    private Long clsEndId;

    @Comment("关系类型ID[CLS_RLT_TYPE_ID]    关系类型ID:依赖、调用、等等")
    private Long clsRltTypeId;

    @Comment("起始节点ID[NODE_START_ID]    起始节点ID:供页面用")
    private Long nodeStartId;

    @Comment("结束节点ID[NODE_END_ID]    结束节点ID:供页面用")
    private Long nodeEndId;

    @Comment("关系线类型[LINE_TYPE]    关系线类型:1=规则关系、2=empty")
    private Integer lineType;

    @Comment("所属域[DOMAIN_ID]")
    private Long domainId;

    @Comment("数据状态[DATA_STATUS]    数据状态:数据状态：1-正常 0-删除")
    private Integer dataStatus;

    @Comment("创建时间[CREATE_TIME]    创建时间:yyyyMMddHHmmss")
    private Long createTime;

    @Comment("修改时间[MODIFY_TIME]    修改时间:yyyyMMddHHmmss")
    private Long modifyTime;

    @Comment("运算符[LINE_OP]    运算符:1=between、2=大于等于、3=小于等于")
    private Integer lineOp;

    @Comment("条件值[LINE_VAL]    条件值为两个值时以逗号隔开")
    private String lineVal;

    @Comment("关系线条件方向[LINE_DIRECT]    关系线条件方向：1=线起始，2=线终止")
    private Integer lineDirect;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getDefId() {
        return this.defId;
    }

    public void setDefId(Long defId) {
        this.defId = defId;
    }

    public Integer getDefType() {
        return this.defType;
    }

    public void setDefType(Integer defType) {
        this.defType = defType;
    }

    public Long getClassId() {
        return this.classId;
    }

    public void setClassId(Long classId) {
        this.classId = classId;
    }

    public Long getClsStartId() {
        return this.clsStartId;
    }

    public void setClsStartId(Long clsStartId) {
        this.clsStartId = clsStartId;
    }

    public Long getClsEndId() {
        return this.clsEndId;
    }

    public void setClsEndId(Long clsEndId) {
        this.clsEndId = clsEndId;
    }

    public Long getClsRltTypeId() {
        return this.clsRltTypeId;
    }

    public void setClsRltTypeId(Long clsRltTypeId) {
        this.clsRltTypeId = clsRltTypeId;
    }

    public Long getNodeStartId() {
        return this.nodeStartId;
    }

    public void setNodeStartId(Long nodeStartId) {
        this.nodeStartId = nodeStartId;
    }

    public Long getNodeEndId() {
        return this.nodeEndId;
    }

    public void setNodeEndId(Long nodeEndId) {
        this.nodeEndId = nodeEndId;
    }

    public Integer getLineType() {
        return this.lineType;
    }

    public void setLineType(Integer lineType) {
        this.lineType = lineType;
    }

    public Long getDomainId() {
        return this.domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }

    public Integer getDataStatus() {
        return this.dataStatus;
    }

    public void setDataStatus(Integer dataStatus) {
        this.dataStatus = dataStatus;
    }

    public Long getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getModifyTime() {
        return this.modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Integer getLineOp() {
        return this.lineOp;
    }

    public void setLineOp(Integer lineOp) {
        this.lineOp = lineOp;
    }

    public String getLineVal() {
        return this.lineVal;
    }

    public void setLineVal(String lineVal) {
        this.lineVal = lineVal;
    }

    public Integer getLineDirect() {
        return this.lineDirect;
    }

    public void setLineDirect(Integer lineDirect) {
        this.lineDirect = lineDirect;
    }

}
