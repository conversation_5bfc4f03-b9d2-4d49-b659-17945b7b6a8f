package com.uino.comm.feign.exception;

import com.uino.comm.exception.UinoBasicException;

/**
 * 将远程错误转换为本地错误.
 * 
 * <AUTHOR>
 * @since 2020年6月30日下午2:58:47
 *
 */
public class FeignInterfaceException extends UinoBasicException {

    public static final long serialVersionUID = 1;

    public FeignInterfaceException() {
        super();
    }

    public FeignInterfaceException(String message) {
        super(message);
    }

    public FeignInterfaceException(String message, Throwable cause) {
        super(message, cause);
    }

    public FeignInterfaceException(Throwable cause) {
        super(cause);
    }
}
