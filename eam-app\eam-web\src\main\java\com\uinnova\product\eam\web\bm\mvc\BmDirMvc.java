package com.uinnova.product.eam.web.bm.mvc;

import com.binary.framework.web.RemoteResult;
import com.uinnova.product.eam.base.model.PageDTO;
import com.uinnova.product.eam.model.diagram.DiagramDirResultInfo;
import com.uinnova.product.eam.web.bm.bean.QueryDir;
import com.uinnova.product.eam.web.bm.peer.BmDirPeer;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;

/**
 * 文件夹管理
 * <AUTHOR>
 */
@Deprecated
@RestController
@RequestMapping("/bm/dir")
public class BmDirMvc {

    @Resource
    private BmDirPeer bmDirPeer;
    /**
     * 查询私有库业务组件建模视图信息
     */
    @PostMapping("getModuleDiagramPage")
    public RemoteResult getModuleDiagramPage(@RequestBody PageDTO<QueryDir> pageDTO) {
        DiagramDirResultInfo result = bmDirPeer.getModuleDiagramPage(pageDTO);
        return new RemoteResult(result);
    }




}
