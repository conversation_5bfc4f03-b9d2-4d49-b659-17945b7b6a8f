package com.uinnova.product.eam.service.es;


import com.uinnova.product.eam.comm.model.es.EamDiagramRelationSys;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;


/**
 * 视图关联系统表[UINO_EAM_DIAGRAM_RELATION_SYS]数据访问对象
 */
@Service
public class EamDiagramRelationSysDao extends AbstractESBaseDao<EamDiagramRelationSys, EamDiagramRelationSys> {

    @Override
    public String getIndex() {
        return "uino_eam_diagram_relation_sys";
    }

    @Override
    public String getType() {
        return getIndex();
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }
}


