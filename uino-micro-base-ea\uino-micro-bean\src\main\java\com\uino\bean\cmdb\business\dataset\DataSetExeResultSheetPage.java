package com.uino.bean.cmdb.business.dataset;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 数据集执行结果按路径拆分为Sheet的分页
 *
 * <AUTHOR>
 * @version 2020-5-7
 */
@ApiModel(value="数据集执行结果按路径拆分为Sheet的分页",description = "数据集执行结果按路径拆分为Sheet的分页")
public class DataSetExeResultSheetPage implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value="")
    private Integer count = 0;

    @ApiModelProperty(value="页码",example = "1")
    private Integer pageNum = 1;

    @ApiModelProperty(value="每页记录数",example = "20")
    private Integer pageSize = 20;

    @ApiModelProperty(value="记录总数",example = "0")
    private Long totalCount = 0L;

    @ApiModelProperty(value="数据超市id",example = "1")
    private Long dataSetId;

    @ApiModelProperty(value="根节点ciId",example = "1")
    private Long startCiId;

    @ApiModelProperty(value="Sheet分页名",example = "sh1")
    private String name;

    @ApiModelProperty(value="路径")
    private List<String> path;


    /**
     * 表头
     */
    private List<Map<String, String>>  headers = new ArrayList<>();

    /**
     * 列表内容
     */
    private List<Map<String, Object>> data = new ArrayList<>();

    public Long getDataSetId() {
        return dataSetId;
    }

    public void setDataSetId(Long dataSetId) {
        this.dataSetId = dataSetId;
    }

    public Long getStartCiId() {
        return startCiId;
    }

    public void setStartCiId(Long startCiId) {
        this.startCiId = startCiId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<String> getPath() {
        return path;
    }

    public void setPath(List<String> path) {
        this.path = path;
    }

    public List<Map<String, String>> getHeaders() {
        return headers;
    }

    public void setHeaders(List<Map<String, String>> headers) {
        this.headers = headers;
    }

    public List<Map<String, Object>> getData() {
        return data;
    }

    public void setData(List<Map<String, Object>> data) {
        this.data = data;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Long getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Long totalCount) {
        this.totalCount = totalCount;
    }

    public JSONObject toJson() {
        JSONObject json = new JSONObject();
        json.put("count", count);
        json.put("pageNum", pageNum);
        json.put("pageSize", pageSize);
        json.put("totalCount", totalCount);
        json.put("data", data);
        json.put("headers", headers);
        return json;
    }
}