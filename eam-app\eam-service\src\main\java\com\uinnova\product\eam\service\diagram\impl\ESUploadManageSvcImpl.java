package com.uinnova.product.eam.service.diagram.impl;


import com.binary.core.exception.MessageException;
import com.binary.jdbc.Page;
import com.uinnova.product.eam.base.diagram.model.ESUploadManage;
import com.uinnova.product.eam.base.diagram.utils.FileFilterUtil;
import com.uinnova.product.eam.db.diagram.es.ESUploadManageDao;
import com.uinnova.product.eam.service.diagram.ESUploadManageSvc;
import com.uinnova.product.vmdb.comm.i18n.MessageUtil;
import com.uinnova.product.vmdb.comm.model.image.CcImage;
import com.uino.util.rsm.RsmUtils;
import com.uino.util.sys.SysUtil;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.sort.SortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.util.LinkedList;
import java.util.List;

@Service
public class ESUploadManageSvcImpl implements ESUploadManageSvc {

    @Value("${http.resource.space}")
    private String httpResourcePath;

    @Value("${local.resource.space}")
    private String localResourcePath;

    @Autowired
    private ESUploadManageDao eSUploadManageDao;

    @Autowired
    private RsmUtils rsmUtils;


    @Override
    public ESUploadManage upload(String url, Long size) {
        // 1.上传图片
        Long userId = SysUtil.getCurrentUserInfo().getId();
        ESUploadManage model = new ESUploadManage();
        model.setFileUrl(url);
        model.setFileSize(size);
        model.setStatus(1L);
        model.setFileType(1L);
        model.setUserId(userId);
        Long id = eSUploadManageDao.saveOrUpdate(model);
        model.setId(id);
        model.setFileUrl(httpResourcePath + url);
        return model;
    }

    @Override
    public Page<ESUploadManage> queryData(int pageNum, int pageSize, String orders, BoolQueryBuilder bQuery) {
        bQuery.must(QueryBuilders.termQuery("userId", SysUtil.getCurrentUserInfo().getId()));
        bQuery.must(QueryBuilders.termQuery("status", 1L));
//        bQuery.must(QueryBuilders.termQuery("fileType", 1L));
        List<SortBuilder<?>> sorts = new LinkedList();
        sorts.add(SortBuilders.fieldSort("modifyTime").order(SortOrder.DESC));
        Page<ESUploadManage> sortListByCdt = eSUploadManageDao.getSortListByQuery(pageNum, pageSize, bQuery, sorts);
        List<ESUploadManage> data = sortListByCdt.getData();
        if (!CollectionUtils.isEmpty(data)) {
            data.forEach((model) -> model.setFileUrl(httpResourcePath + model.getFileUrl()));
        }
        sortListByCdt.setData(data);
        return sortListByCdt;
    }

    @Override
    public Long deleteImage(CcImage image) {
        Long id = image.getId();
        MessageUtil.checkEmpty(id, "id");
        ESUploadManage model = eSUploadManageDao.getById(id);
        Long userId = SysUtil.getCurrentUserInfo().getId();
        if (model != null && userId.equals(model.getUserId())) {
//            model.setStatus(0L);
//            eSUploadManageDao.saveOrUpdate(model);
            // 删除数据库
            eSUploadManageDao.deleteById(model.getId());
            // 删除服务器资源
            String url = localResourcePath + model.getFileUrl();
            File file = new File(FileFilterUtil.parseFilePath(url));
            if (file.exists()) {
                file.delete();
            }
            rsmUtils.deleteRsm(url);
            url = url.replace(url.substring(url.lastIndexOf("/")), "");
            file = new File(FileFilterUtil.parseFilePath(url));
            System.out.println(url);
            if (file.exists()) {
                file.delete();
            }
            rsmUtils.deleteRsm(url);
        } else {
            throw new MessageException("需要删除的图片，已删除或没有权限");
        }
        return id;
    }

}
