package com.uinnova.product.eam.db.impl;


import com.binary.framework.dao.DaoDefinition;

import com.uinnova.product.eam.comm.model.CDcCombDiagram;
import com.uinnova.product.eam.comm.model.DcCombDiagram;


/**
 * 组合视图表[DC_COMB_DIAGRAM]数据访问对象定义实现
 */
public class DcCombDiagramDaoDefinition implements DaoDefinition<DcCombDiagram, CDcCombDiagram> {


	@Override
	public Class<DcCombDiagram> getEntityClass() {
		return DcCombDiagram.class;
	}


	@Override
	public Class<CDcCombDiagram> getConditionClass() {
		return CDcCombDiagram.class;
	}


	@Override
	public String getTableName() {
		return "DC_COMB_DIAGRAM";
	}


	@Override
	public boolean hasDataStatusField() {
		return true;
	}


	@Override
	public void setDataStatusValue(DcCombDiagram record, int status) {
		record.setDataStatus(status);
	}


	@Override
	public void setDataStatusValue(CDcCombDiagram cdt, int status) {
		cdt.setDataStatus(status);
	}


	@Override
	public void setCreatorValue(DcCombDiagram record, String creator) {
	}


	@Override
	public void setModifierValue(DcCombDiagram record, String modifier) {
	}


}


