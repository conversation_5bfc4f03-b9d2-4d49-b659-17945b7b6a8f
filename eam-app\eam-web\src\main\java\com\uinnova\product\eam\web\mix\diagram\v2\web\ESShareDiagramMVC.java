package com.uinnova.product.eam.web.mix.diagram.v2.web;

import cn.hutool.crypto.SecureUtil;
import com.binary.core.exception.BinaryException;
import com.binary.core.util.BinaryUtils;
import com.binary.framework.util.ControllerUtils;
import com.binary.framework.web.RemoteResult;
import com.binary.jdbc.Page;
import com.uinnova.product.eam.base.diagram.model.*;
import com.uinnova.product.eam.service.diagram.ESShareDiagramSvc;
import com.uinnova.product.eam.web.mix.diagram.v2.peer.ESDiagramPeer;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uinnova.product.vmdb.comm.doc.annotation.MvcDesc;
import com.uinnova.product.vmdb.comm.i18n.MessageUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/eam/shareDiagram")
@MvcDesc(author = "wang", desc = "提供视图历史增删改查接口")
public class ESShareDiagramMVC {

    @Autowired
    private ESShareDiagramSvc esShareDiagramSvc;

    @Autowired
    private ESDiagramPeer esDiagramPeer;

    @PostMapping("/addRecord")
    @ModDesc(desc = "新增分享信息", pDesc = "", rDesc = "", rType = List.class)
    public RemoteResult addRecord(@RequestBody List<DiagramShareRecordDTO> shareRecords) {
        List<ESDiagramShareRecordResult> resultList = esShareDiagramSvc.saveOrUpdateShareRecord(shareRecords);
        if (BinaryUtils.isEmpty(resultList)) {
            return new RemoteResult(false, 404, "分享视图已被删除");
        } else {
            return new RemoteResult(resultList);
        }
    }

    @PostMapping("/querySharedDiagramPage")
    @ModDesc(desc = "分页查询分享给我的视图", pDesc = "", rDesc = "", rType = List.class)
    public RemoteResult querySharedDiagramPage(@RequestBody ShareRecordQueryBean queryBean) {
        Page<ESDiagramShareRecordResult> resultPage = esShareDiagramSvc.queryShareRecordPage(queryBean);
        return new RemoteResult(resultPage);
    }

    @PostMapping("/queryDiagramShareRecords")
    @ModDesc(desc = "查询视图分享记录", pDesc = "", rDesc = "", rType = List.class)
    public RemoteResult queryDiagramShareRecords(@RequestBody ShareRecordQueryBean shareRecordQueryBean) {
		MessageUtil.checkEmpty(shareRecordQueryBean, "shareRecordQueryBean");
    	String diagramId = shareRecordQueryBean.getDiagramId();
        if (StringUtils.isEmpty(diagramId)) {
            throw new BinaryException("传参错误，视图id不能为空");
        }
        Long aLong = esDiagramPeer.queryDiagramInfoByEnergy(diagramId);
        Map<Long, List<ESDiagramShareRecordResult>> resultMap = esShareDiagramSvc.queryDiagramShareRecords(new Long[]{aLong}, true);
        Map<String, List<ESDiagramShareRecordResult>> map = new HashMap<>();
        resultMap.forEach((key, value) -> {
            map.put(SecureUtil.md5(String.valueOf(key)).substring(8, 24), value);
        });
        resultMap = null;
        return new RemoteResult(map);
    }

	@GetMapping("/removeRecord")
	@ModDesc(desc = "删除分享信息", pDesc = "", rDesc = "", rType = List.class)
	public void removeRecord(HttpServletRequest request, HttpServletResponse response,
							 @RequestParam Long id) {
		Integer result = esShareDiagramSvc.removeShareRecord(id);
		ControllerUtils.returnJson(request, response, result);
	}

	@GetMapping("/addUserByLink")
	@ModDesc(desc = "链接授权用户视图权限", pDesc = "", rDesc = "", rType = List.class)
	public RemoteResult addUserByLink(@RequestParam String id) {
		String result = esShareDiagramSvc.addUserByLink(id);
		return new RemoteResult(result);
	}

	@PostMapping("/updateShareLink")
	@ModDesc(desc = "链接授权用户视图权限", pDesc = "", rDesc = "", rType = List.class)
	public RemoteResult updateShareLink(@RequestBody DiagramShareLink updateLink) {
		Long id = updateLink.getId();
		if (id == null) {
			throw new BinaryException("id不能为空");
		}
		Integer permission = updateLink.getPermission();
		if (permission == null) {
			throw new BinaryException("permission不能为空");
		}
		Long result = esShareDiagramSvc.updateShareLink(id, permission);
		return new RemoteResult(result);
	}

	@PostMapping("/addShareLink")
	@ModDesc(desc = "新增分享链接", pDesc = "", rDesc = "", rType = List.class)
	public RemoteResult addShareLink(@RequestBody DiagramShareLink shareBean) {
		if (BinaryUtils.isEmpty(shareBean.getDiagramId())) {
			throw new BinaryException("diagramId不能为空");
		}
		if (BinaryUtils.isEmpty(shareBean.getImageName())) {
			throw new BinaryException("imageName不能为空");
		}
		if (BinaryUtils.isEmpty(shareBean.getLinkType())) {
			throw new BinaryException("linkType不能为空");
		}
		DiagramShareLink result = esShareDiagramSvc.addShareLink(shareBean);
		return new RemoteResult(result);
	}

	@GetMapping("/getDiagramByLinkKey")
	@ModDesc(desc = "新增分享链接", pDesc = "", rDesc = "", rType = List.class)
	public RemoteResult getDiagramByLinkKey(@RequestParam String id) {
		ESDiagramInfoDTO result = esShareDiagramSvc.getDiagramByLinkKey(id);
		return new RemoteResult(result);
	}

	@GetMapping("/deleteShareLink")
	@ModDesc(desc = "删除分享的视图链接", pDesc = "", rDesc = "", rType = List.class)
	public RemoteResult deleteShareLink(@RequestParam Long id) {
		if (BinaryUtils.isEmpty(id)) {
			throw new BinaryException("id不能为空");
		}
		Integer result = esShareDiagramSvc.deleteShareLink(id);
		return new RemoteResult(result);
	}

	@PostMapping("/addSharePic")
	@ModDesc(desc = "新增分享的图片链接", pDesc = "", rDesc = "", rType = List.class)
	public RemoteResult addSharePic(@RequestBody DiagramShareLink shareBean) {
		if (BinaryUtils.isEmpty(shareBean.getContent())) {
			throw new BinaryException("content不能为空");
		}
		if (BinaryUtils.isEmpty(shareBean.getDiagramId())) {
			throw new BinaryException("diagramId不能为空");
		}
		if (BinaryUtils.isEmpty(shareBean.getImageName())) {
			throw new BinaryException("imageName不能为空");
		}
		if (BinaryUtils.isEmpty(shareBean.getSheetId())) {
			throw new BinaryException("sheetId不能为空");
		}
		DiagramShareLink result = esShareDiagramSvc.addSharePic(shareBean);
		return new RemoteResult(result);
	}

	@GetMapping("/deleteSharePic")
	@ModDesc(desc = "删除分享的图片链接", pDesc = "", rDesc = "", rType = List.class)
	public RemoteResult deleteSharePic(@RequestParam Long id) {
		if (BinaryUtils.isEmpty(id)) {
			throw new BinaryException("id不能为空");
		}
		Integer result = esShareDiagramSvc.deleteSharePic(id);
		return new RemoteResult(result);
	}

	@PostMapping("/querySharePic")
	public RemoteResult querySharePic(@RequestBody DiagramShareLinkQuery shareLink) {
		String diagramId = shareLink.getDiagramId();
		if (StringUtils.isEmpty(diagramId)) {
			throw new BinaryException("id不能为空");
		}
		Integer[] linkTypes = shareLink.getLinkTypes();
		if (linkTypes == null && linkTypes.length <= 0) {
			throw new BinaryException("type不能为空");
		}
		List<DiagramShareLink> result = esShareDiagramSvc.querySharePic(diagramId, linkTypes);
		return new RemoteResult(result);
	}
}
