//package com.uinnova.product.eam.web.xinwang.cas;
//
//import lombok.Data;
//import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
//import org.springframework.boot.context.properties.ConfigurationProperties;
//
///**
// * description
// *
// * <AUTHOR>
// * @since 2022/12/29 18:43
// */
//@Data
//@ConditionalOnProperty(prefix = "monet.login", name = "loginMethod", havingValue = "xinwang")
//@ConfigurationProperties(prefix = "spring.cas")
//public class CasConfigurationProperties {
//
//    private String casServerUrlPrefix;
//    private String casServerLoginUrl;
//    private String casServerLogoutUrl;
//    private String serverName;
//    private boolean useSession = true;
//    private boolean redirectAfterValidation = true;
//    private String ignorePattern = "\\.(js|img|css)(\\?.*)?$";
//    private String ignoreUrlPatternType = "REGEX";
//    private String encoding = "UTF-8";
//
//}
