package com.uinnova.product.eam.service.sys;

import com.uinnova.product.eam.comm.bean.ApplicationSysFigureRlt;

import java.util.Map;

/**
 * 应用系统的图关系接口
 *
 * <AUTHOR>
 */
public interface IEamApplicationSysFigureRltSvc {
    /**
     * 根据应用系统的CiCode查询对应的图
     *
     * @param ciCode 应用系统ciCode
     * @return 应用系统的图关系信息
     */
    Map<String, Object> getByCiCode(String ciCode);

    /**
     * 保存或更新应用系统图关系
     *
     * @param applicationSysFigureRlt 实体类
     * @return 应用系统图关系id
     */
    Long addFigureRlt(ApplicationSysFigureRlt applicationSysFigureRlt);
}
