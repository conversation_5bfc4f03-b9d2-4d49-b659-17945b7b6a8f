package com.uino.plugin.classloader;

import com.uino.plugin.bean.OperatePluginDetails;
import com.uino.plugin.client.init.SpringContextUtil;
import com.uino.plugin.client.init.UinoBaseRequestMappingHandlerMapping;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.beans.factory.support.BeanDefinitionBuilder;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Modifier;
import java.net.URL;
import java.net.URLClassLoader;
import java.util.*;
import java.util.jar.JarEntry;
import java.util.jar.JarFile;


/**
 * 自定义类加载器
 * 动态加载外部jar包的自定义类加载器
 */
@Slf4j
public class PluginClassLoader extends URLClassLoader {

    // 保存已经加载过的Class对象
    private static Map<String, Class> cacheClassMap = new HashMap<>();

    //jar包中有的类
    private static Map<String, List<String>> classByJarMap = new HashMap<>();

    // 需要注册的spring bean的name集合
    private static List<String> registeredBean = new ArrayList<>();


    /**
     * 预加载插件地址
     */
    public static final String _PRELOADING_PLUGIN_PATH = System.getProperty("user.dir") + "/plugins/preloading/";
    /**
     * 加载插件地址
     */
    public static final String _LOADING_PLUGIN_PATH = System.getProperty("user.dir") + "/plugins/loading/";
//    public static final String PLUGIN_PATH = "PLUGIN_PATH";

    // 构造
    public PluginClassLoader(ClassLoader parent) {
        super(new URL[0], parent);
    }

    private List<ClassLoaderAware> classLoaderAwareList = new ArrayList<>();

    private List<ClassLoaderAware> getClassLoaderAwareList() {
        if (CollectionUtils.isEmpty(this.classLoaderAwareList)) {
            Map<String, ClassLoaderAware> listenerMap = SpringContextUtil.getBeanFactory().getBeansOfType(ClassLoaderAware.class);
            if (!CollectionUtils.isEmpty(listenerMap)) {
                for (Map.Entry<String, ClassLoaderAware> entry : listenerMap.entrySet()) {
                    ClassLoaderAware aware = entry.getValue();
                    this.classLoaderAwareList.add(aware);
                }
            }
        }
        return this.classLoaderAwareList;
    }

    private void afterLoaderSuccess(OperatePluginDetails operatePluginDetails) {
        List<ClassLoaderAware> classLoaderAwareList = this.getClassLoaderAwareList();
        for (ClassLoaderAware aware : classLoaderAwareList) {
            aware.afterLoaderSuccess(operatePluginDetails);
        }
    }

    private void afterLoaderFailed(OperatePluginDetails operatePluginDetails) {
        List<ClassLoaderAware> classLoaderAwareList = this.getClassLoaderAwareList();
        for (ClassLoaderAware aware : classLoaderAwareList) {
            aware.afterLoaderFailed(operatePluginDetails);
        }
    }

    private void afterUnLoaderSuccess(OperatePluginDetails operatePluginDetails) {
        List<ClassLoaderAware> classLoaderAwareList = this.getClassLoaderAwareList();
        for (ClassLoaderAware aware : classLoaderAwareList) {
            aware.afterUnLoaderSuccess(operatePluginDetails);
        }
    }

    private void afterUnLoaderFailed(OperatePluginDetails operatePluginDetails) {
        List<ClassLoaderAware> classLoaderAwareList = this.getClassLoaderAwareList();
        for (ClassLoaderAware aware : classLoaderAwareList) {
            aware.afterUnLoaderFailed(operatePluginDetails);
        }
    }


    /**
     * 加载Jar，单实例用
     *
     * @param jarPath
     */
    public boolean loadJar(String jarName) {
        JarFile jarFile = null;
        try {
            String jarPath;
            if (jarName.contains(_LOADING_PLUGIN_PATH)) {
                jarPath = jarName;
            }else {
                jarPath = _LOADING_PLUGIN_PATH + jarName;
            }

            File file = new File(jarPath);
            if (!file.exists()) {
                afterLoaderFailed(new OperatePluginDetails(jarName, null, null, false, "插件不存在"));
                return false;
            }
            jarFile = new JarFile(jarPath);
            URL url = file.toURI().toURL();
            super.addURL(url);

            // 初始化类加载器执行类加载
            List<String> list = classByJarMap.computeIfAbsent(jarName, k -> new ArrayList<>());

            // 初始化Spring Bean
            try {
                addJar(list, jarFile);
                List<String> beanNameList = addBeans(list);
                for (String beanName : beanNameList) {
                    try {
                        // 插件中有扫包相关操作时无法初始化，同时也不需要扫包
                        SpringContextUtil.getBeanFactory().applyBeanPostProcessorsAfterInitialization(SpringContextUtil.getBean(beanName), beanName);
                    } catch (Exception e) {
                        log.debug("bean init error");
                    }

                }
            }catch (Exception e){
                //出错后直接卸载
                unloadJar(jarName);
                afterLoaderFailed(new OperatePluginDetails(jarName, null, null, false, ExceptionInfoUtil.getExceptionAllInfo(e)));
                return false;
            }
            afterLoaderSuccess(new OperatePluginDetails(jarName, null, null, true, null));
            return true;
        } catch (IOException e) {
            afterLoaderFailed(new OperatePluginDetails(jarName, null, null, false, ExceptionInfoUtil.getExceptionAllInfo(e)));
            log.error("loadJar", e);
            return false;
        } finally {
            if (jarFile != null) {
                try {
                    jarFile.close();
                } catch (IOException e) {
                    log.error("loadJar IOException", e);
                }
            }
        }


    }

    public boolean unloadJar(String jarName) {
        JarFile jarFile = null;
        try {
            String jarPath;
            if (jarName.contains(_LOADING_PLUGIN_PATH)) {
                jarPath = jarName;
            }else {
                jarPath = _LOADING_PLUGIN_PATH + jarName;
            }

            File file = new File(jarPath);
            if (!file.exists()) {
                afterUnLoaderFailed(new OperatePluginDetails(jarName, null, null, false, "插件不存在"));
                return false;
            }
            jarFile = new JarFile(jarPath);

            // 卸载类
//            List<String> listClassNames = removeJar(jarFile);
            List<String> list = classByJarMap.get(jarName);
            if (list != null) {
                // 卸载Spring Bean
                removeBeans(list);
                // 全部卸载完后移除cache
                removeClassCaches(list);
                classByJarMap.remove(jarName);
            }

            afterUnLoaderSuccess(new OperatePluginDetails(jarName, null, null, true, null));
            return true;
        } catch (IOException e) {
            afterUnLoaderFailed(new OperatePluginDetails(jarName, null, null, false, ExceptionInfoUtil.getExceptionAllInfo(e)));
            log.error("unloadJar IOException", e);
        } finally {
            if (jarFile != null) {
                try {
                    jarFile.close();
                    System.gc();
                } catch (IOException e) {
                    log.error("unloadJar IOException", e);
                }
            }
        }

        return false;
    }

    // 重写loadClass方法
    // 改写loadClass方式
    @Override
    public Class<?> loadClass(String name) throws ClassNotFoundException {
        if (cacheClassMap.containsKey(name)) {
            return cacheClassMap.get(name);
        } else {
            return super.loadClass(name);
        }
    }

    /**
     * 方法描述 初始化类加载器，保存字节码
     *
     * @method init
     */
    private void addJar(List<String> list, JarFile jarFile) {
        // 解析jar包每一项
        Enumeration<JarEntry> en = jarFile.entries();
        while (en.hasMoreElements()) {
            JarEntry je = en.nextElement();
            String name = je.getName();
            if (name.endsWith(".class")) {
                String className = name.replace(".class", "").replaceAll("/", ".");
                Class<?> aClass = null;
                InputStream inputClass = null;
                try {

                    // 考虑更新加载的问题,每次更新类定义
                    if (cacheClassMap.containsKey(className)) {
                        // 如果已存在，卸载重新加载
                        cacheClassMap.remove(className);
                        try {
                            // 相同的类加载器，不能重复加载相同的类
                            super.findClass(className);
                        } catch (Throwable err) {
                            log.error("addJar Throwable", err);
                        }
                    }
                    aClass = loadClass(className, true);
                } catch (ClassNotFoundException e) {
                    log.error("addJar ClassNotFoundException", e);
                } finally {
                    if (inputClass != null) {
                        try {
                            inputClass.close();
                        } catch (IOException e) {
                            log.error("addJar IOException", e);
                        }
                    }
                }
                if (aClass != null) {
                    cacheClassMap.put(className, aClass);
                    list.add(className);
                }
            }
        }
    }

    private List<String> removeJar(JarFile jarFile) {
        // 解析jar包每一项
        Enumeration<JarEntry> en = jarFile.entries();
        List<String> listClassNames = new ArrayList<>();

        while (en.hasMoreElements()) {
            JarEntry je = en.nextElement();
            String name = je.getName();
            if (name.endsWith(".class")) {
                String className = name.replace(".class", "").replaceAll("/", ".");
                if (cacheClassMap.containsKey(className)) {
                    listClassNames.add(className);
                }
            }
        }
        return listClassNames;
    }

    /**
     * 方法描述 初始化spring bean
     *
     * @method initBean
     */
    private List<String> addBeans(List<String> listClassNames) {
        List<String> beanNameList = new ArrayList<>();
        for (String className : listClassNames) {
            Class<?> cla = cacheClassMap.get(className);
            String beanName = className;
            if (isSpringBeanClass(cla)) {
                BeanDefinitionBuilder beanDefinitionBuilder = BeanDefinitionBuilder.genericBeanDefinition(cla);
                BeanDefinition beanDefinition = beanDefinitionBuilder.getRawBeanDefinition();
                // 设置当前bean定义对象是单例的
                beanDefinition.setScope("singleton");

                // 将变量首字母置小写
//				String beanName = StringUtils.uncapitalize(className);

//				beanName = beanName.substring(beanName.lastIndexOf(".") + 1);
//				beanName = StringUtils.uncapitalize(beanName);
                if (registeredBean.contains(beanName)) {
                    // 如已存在更新注册
                    SpringContextUtil.getBeanFactory().removeBeanDefinition(beanName);
                    registeredBean.remove(beanName);
                }

                SpringContextUtil.getBeanFactory().registerBeanDefinition(beanName, beanDefinition);
                beanNameList.add(beanName);
                //解决依赖
                //SpringContextUtil.getBeanFactory().autowireBean(SpringContextUtil.getBeanFactory().getBean(beanName));
                registeredBean.add(beanName);
                log.info("注册bean:" + beanName);
            }

            // 判断是否Controller
            if (isControllerClass(cla)) {
                // 注册RequestMapping
                UinoBaseRequestMappingHandlerMapping requestMappingHandlerMapping = SpringContextUtil
                        .getBean(UinoBaseRequestMappingHandlerMapping.class);
                requestMappingHandlerMapping.registerMapping(beanName, cla);
            }
        }
        return beanNameList;
//		//解决依赖问题
//		SpringContextUtil.getBeanFactory().autowireBean(SpringContextUtil.getBeanFactory().getBean("TestVersioningController-v5"));
    }


    private void removeClassCaches(List<String> listClassNames) {
        for (String className : listClassNames) {
            cacheClassMap.remove(className);
        }
    }

    private void removeBeans(List<String> listClassNames) {
        for (String className : listClassNames) {
            Class<?> cla = cacheClassMap.get(className);
            // 判断是否Controller
            if (isControllerClass(cla)) {
                // 注销RequestMapping
                UinoBaseRequestMappingHandlerMapping requestMappingHandlerMapping = SpringContextUtil
                        .getBean(UinoBaseRequestMappingHandlerMapping.class);
                requestMappingHandlerMapping.unregisterMapping(className);
            }

            if (registeredBean.contains(className)) {
                SpringContextUtil.getBeanFactory().removeBeanDefinition(className);
                registeredBean.remove(className);
                log.info("删除bean:" + className);
            }
        }

//		//解决依赖问题
//		SpringContextUtil.getBeanFactory().autowireBean(SpringContextUtil.getBeanFactory().getBean("TestVersioningController-v5"));
    }

    /**
     * 方法描述 判断class对象是否带有spring的注解
     *
     * @param cla jar中的每一个class
     * @return true 是spring bean false 不是spring bean
     * @method isSpringBeanClass
     */
    public boolean isSpringBeanClass(Class<?> cla) {
        if (cla == null) {
            return false;
        }
        // 是否是接口
        if (cla.isInterface()) {
            return false;
        }
        // 是否是抽象类
        if (Modifier.isAbstract(cla.getModifiers())) {
            return false;
        } else {
            return AnnotationUtils.findAnnotation(cla, Component.class) != null;
        }
    }

    /**
     * 方法描述 判断class对象是否带有spring的注解
     *
     * @param cla jar中的每一个class
     * @return true 是spring bean false 不是spring bean
     * @method isSpringBeanClass
     */
    public boolean isControllerClass(Class<?> cla) {
        if (cla == null) {
            return false;
        }
        // 是否是接口
        if (cla.isInterface()) {
            return false;
        }
        // 是否是抽象类
        if (Modifier.isAbstract(cla.getModifiers())) {
            return false;
        } else {
            return AnnotationUtils.findAnnotation(cla, Controller.class) != null;
        }
    }
}
