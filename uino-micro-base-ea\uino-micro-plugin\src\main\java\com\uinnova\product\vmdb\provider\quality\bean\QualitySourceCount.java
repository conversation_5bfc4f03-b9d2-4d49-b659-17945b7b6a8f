package com.uinnova.product.vmdb.provider.quality.bean;


public class QualitySourceCount {

    private static final long serialVersionUID = 1L;

    /**
     * 来源
     */
    private String source;

    /**
     * 数量
     */
    private Integer count;

    /**
     * 类型[0:普通,1:无数据来源,2:全部]
     */
    private Integer type;

    public static enum SourceType {

        DEFAULT(0),
        EMPTY(1),
        ALL(2);

        private Integer code;

        private SourceType(Integer code) {
            this.code = code;
        }

        public Integer getType() {
            return code;
        }
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }
}
