package com.uino.util.judge;

import com.uino.util.judge.exception.JudgeValidException;

import lombok.Setter;

/**
 * 判断链 链式判断，再xml或@bean实例链，调用链头的valid开始判断，直到判断完成整个链完成判定 eg: <bean id="endNode" class="***.package.EndNode"> </bean>
 * <bean id="secondNode" class="***.package.SecondNode"> <property name= "nextValidNode" ref="endNode" /> </bean>
 * <bean id="firstNode" class="***.package.FirstNode"> <property name= "nextValidNode" ref="secondNode" /> </bean>
 * 上述举例描述了： 若执行firstNode校验然后执行secondNode校验最后执行endNode校验 若执行secondNode校验则之后执行endNode校验
 * 实际就是从校验的头节点视为起点不停的寻找其中nextValidNode实例，直到最后不再有nextValidNode 异常消息说明： 若判断不通过时有两种处理策略
 * 1、不声明exceptionMessage，并且不重写exceptionMessage()/重写exceptionMessage()但返回空， 此时判断链会返回false
 * 2、声明exceptionMessage/重写exceptionMessage()并且带有返回消息, exceptionMessage优先级比exceptionMessage()更高
 * 
 * <AUTHOR>
 *
 */
public abstract class BaseJudgeProcess {

    /**
     * 下一个判断节点
     */
    @Setter
    protected BaseJudgeProcess nextValidNode;
    /**
     * 判断失败异常信息，最高优先级
     */
    @Setter
    protected String exceptionMessage;

    /**
     * 判断失败异常消息
     * 
     * @return
     */
    protected String exceptionMessage() {
        return null;
    };

    /**
     * 验证核心，子类必须实现
     * 
     * @param validObj 验证对象
     * @return
     */
    protected abstract boolean validCore(Object validObj);

    /**
     * 验证
     * 
     * @param validObj 验证的对象
     * @return
     */
    public final boolean valid(Object validObj) {
        boolean validResults = validCore(validObj);
        if (!validResults) {
            String errorMsg = exceptionMessage;
            if (errorMsg != null && !"".equals(errorMsg.trim())) {
                throw new JudgeValidException(errorMsg);
            }
            errorMsg = exceptionMessage();
            if (errorMsg != null && !"".equals(errorMsg.trim())) {
                throw new JudgeValidException(errorMsg);
            }
            return validResults;
        } else if (nextValidNode != null) {
            return nextValidNode.valid(validObj);
        } else {
            return validResults;
        }
    }
}
