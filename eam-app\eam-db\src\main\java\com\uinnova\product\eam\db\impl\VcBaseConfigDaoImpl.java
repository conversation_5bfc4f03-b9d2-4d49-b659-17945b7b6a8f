package com.uinnova.product.eam.db.impl;



import com.uinnova.product.eam.comm.model.CVcBaseConfig;
import com.uinnova.product.eam.comm.model.VcBaseConfig;
import com.uinnova.product.eam.db.VcBaseConfigDao;
import com.uinnova.product.eam.db.support.dao.mybatis.ComMyBatisBinaryDaoImpl;


/**
 * 基础配置表[VC_BASE_CONFIG]数据访问对象实现
 */
public class VcBaseConfigDaoImpl extends ComMyBatisBinaryDaoImpl<VcBaseConfig, CVcBaseConfig> implements VcBaseConfigDao {

//    @Override
//    public String getTableName() {
//        return "IAMS." + getDaoDefinition().getTableName();
//    }
}


