package com.uinnova.product.eam.service;

import com.binary.framework.web.RemoteResult;
import com.binary.jdbc.Page;
import com.uinnova.product.eam.comm.model.es.EamNotice;
import com.uinnova.product.eam.model.EamNoticeTransVo;
import com.uinnova.product.eam.model.EamNoticeVo;
import com.uinnova.product.eam.model.asset.ShareMsgDTO;
import com.uinnova.product.eam.model.vo.CIRltDelMsgVo;
import com.uinnova.product.eam.model.vo.DeliTemplateMsgSaveVo;
import com.uinnova.product.eam.model.vo.NoticeVo;
import com.uinnova.product.eam.model.vo.SharePublishAssertMsgVo;

import java.util.List;

public interface IEamNoticeService {

    /**
     * 方案关联视图更新时，消息保存
     * @param dEnergies
     */
    void diagramUpdateMsgSave(List<String> dEnergies);

    /**
     * 审核流程结束时，消息保存
     * @param result 审批结果
     * @param sourceType 类型(plan在线方案)
     * @param sourceId 业务主键
     * @param startUserId 流程发起人
     */
    void workflowMsgSave(String result, String sourceType, String sourceId, String startUserId);

    /**
     * 更新消息/公告已读
     * @param noticeType
     */
    void updateRead(String noticeType);

    /**
     * 消息/公告统计数
     * @return
     */
    NoticeVo noticeNum();

    /**
     * 消息/公告列表
     * @param noticeType
     * @param pageSize
     * @param pageNum
     * @return
     */
    Page<EamNoticeVo> list(String noticeType, Integer pageSize, Integer pageNum);

    /**
     * 获取真实消息数据
     * @param noticeTransVo
     * @return
     */
    RemoteResult transform(EamNoticeTransVo noticeTransVo);

    /**
     *  与我协作消息保存
     * @param shareMsgSaveVos
     */
    void shareMsgSave(List<ShareMsgDTO> shareMsgSaveVos);

    /**
     * 交付物模板变更消息保存
     * @param saveVo
     */
    void deliverableTemplataMsgSave(DeliTemplateMsgSaveVo saveVo);

    /**
     *  交付物模板变更消息同步协作者
     * @param saveVo
     */
    void deliverableTemplataMsgSyncShare(DeliTemplateMsgSaveVo saveVo);

    /**
     * 新增批量通知
     * @param noticeList
     */
    void batchSaveNotice(List<EamNotice> noticeList);

    /**
     * 方案/视图协作着发布消息
     * @param sharePublishAssertMsgVo
     */
    void sharePublishAssertMsgSave(SharePublishAssertMsgVo sharePublishAssertMsgVo);

    /**
     * CI关系删除消息
     * @param msgVos
     */
    void ciRltDelMsgSave(List<CIRltDelMsgVo> msgVos);
}
