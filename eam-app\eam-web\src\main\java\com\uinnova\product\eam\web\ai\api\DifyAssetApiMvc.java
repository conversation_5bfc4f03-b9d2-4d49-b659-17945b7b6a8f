package com.uinnova.product.eam.web.ai.api;

import com.binary.core.util.BinaryUtils;
import com.binary.framework.web.RemoteResult;
import com.uinnova.product.eam.comm.model.es.AssetChangeRecord;
import com.uinnova.product.eam.model.vo.ESCISearchBeanVO;
import com.uinnova.product.eam.service.ai.api.IDifyAssetApiService;
import com.uinnova.product.eam.web.asset.peer.ArchitectureAssetPeer;
import com.uinnova.product.vmdb.comm.bean.QueryPageCondition;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uinnova.product.vmdb.provider.ci.bean.CiGroupPage;
import com.uino.bean.cmdb.base.LibType;
import com.uino.bean.cmdb.query.ESCISearchBean;
import lombok.extern.log4j.Log4j2;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/ai/api/asset")
@Log4j2
public class DifyAssetApiMvc {

    @Resource
    IDifyAssetApiService difyAssetApiService;
    @Resource
    ArchitectureAssetPeer assetPeer;

    @RequestMapping("/ci/queryPageBySearchBean")
    @ModDesc(desc = "分页查询对象数据-支持属性排序", pDesc = "条件查询", pType = QueryPageCondition.class, pcType = ESCISearchBean.class, rDesc = "对象数据", rType = CiGroupPage.class)
    public RemoteResult queryPageBySearchBean(@RequestParam(defaultValue = "DESIGN") LibType libType, @RequestBody ESCISearchBeanVO bean) {
        CiGroupPage result = difyAssetApiService.queryPageBySearchBean(bean, libType);
        return new RemoteResult(result);
    }

    @GetMapping("/appSystem/getSystemArchitectureDiagram")
    @ModDesc(desc = "查询系统相关架构视图", pDesc = "系统ciCode", rDesc = "视图加密id", rType = List.class)
    public RemoteResult getSystemArchitectureDiagram(@RequestParam String ciCode) {
        BinaryUtils.checkEmpty(ciCode, "应用系统code");
        List<Map<String, String>> result = difyAssetApiService.getSystemArchitectureDiagram(ciCode);
        return new RemoteResult(result);
    }

    @GetMapping("/ci/getChangeRecordInfo")
    @ModDesc(desc = "获取ci变更信息", pDesc = "ciCode", rDesc = "变更记录", rType = List.class)
    public RemoteResult getChangeRecordInfo(@RequestParam String ciCode) {
        List<AssetChangeRecord> result = assetPeer.getChangeRecordInfo(ciCode);
        return new RemoteResult(result);
    }

    @PostMapping("/auto/data/analysis")
    @ModDesc(desc = "ai绘图-视图数据解析", pDesc = "ciCode", rDesc = "ai绘图-视图数据解析", rType = List.class)
    public RemoteResult autoDataAnalysis(@RequestBody String body) {
        return difyAssetApiService.autoDataAnalysis(body);
    }
}