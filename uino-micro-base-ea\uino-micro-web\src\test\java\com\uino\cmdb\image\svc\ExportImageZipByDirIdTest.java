package com.uino.cmdb.image.svc;

import static org.junit.Assert.assertEquals;

import java.util.Collections;
import java.util.List;

import org.elasticsearch.index.query.QueryBuilders;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.http.ResponseEntity;
import org.springframework.test.util.ReflectionTestUtils;

import com.uino.dao.cmdb.ESDirSvc;
import com.uino.dao.cmdb.ESImageSvc;
import com.uino.service.cmdb.microservice.impl.ImageSvc;
import com.uino.bean.cmdb.base.CcCiClassDir;
import com.uino.bean.cmdb.base.CcImage;

public class ExportImageZipByDirIdTest {
	@InjectMocks
	private ImageSvc imageSvc;
	private ESImageSvc svc;
	private ESDirSvc dirSvc;
	private String localPath;

	@Before
	public void before() {
		MockitoAnnotations.initMocks(this);
		svc = Mockito.mock(ESImageSvc.class);
		dirSvc = Mockito.mock(ESDirSvc.class);
		localPath = "./src/test/resources";
		ReflectionTestUtils.setField(imageSvc, "svc", svc);
		ReflectionTestUtils.setField(imageSvc, "dirSvc", dirSvc);
		ReflectionTestUtils.setField(imageSvc, "localPath", localPath);
		CcCiClassDir dir = new CcCiClassDir();
		dir.setId(1L);
		dir.setDirName("图片文件夹01");
		Mockito.when(dirSvc.getById(Mockito.eq(1L))).thenReturn(dir);
		CcImage img = new CcImage();
		img.setImgPath("/testdata/10006.png");
		img.setImgName("导出图片测试01");
		List<CcImage> exportImgs = Collections.singletonList(img);
		Mockito.when(svc.getListByQuery(Mockito.eq(QueryBuilders.termQuery("dirId", 1L)))).thenReturn(exportImgs);

	}

	@Test
	public void test01() {
        try {
            ResponseEntity<byte[]> re = imageSvc.exportImageZipByDirIds(Collections.singleton(1L));
            Assert.assertNotNull(re);
        } catch (Exception e) {
            assertEquals("导出图标为空", e.getMessage());
        }
	}
}
