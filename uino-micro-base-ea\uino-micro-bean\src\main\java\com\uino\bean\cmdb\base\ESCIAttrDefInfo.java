package com.uino.bean.cmdb.base;

import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@ApiModel(value = "CI属性定义", description = "CI属性定义")
@Getter
@Setter
public class ESCIAttrDefInfo extends CcCiAttrDef {

    /**
     *
     */
    private static final long serialVersionUID = 1L;

    // private ESDictionaryItemSearchBean dictSearchBean;

    /**
     * 属性组
     */
    @ApiModelProperty(value="属性组")
    private List<String> group;

    @ApiModelProperty(value="扩展属性")
    private Map<String, Object> extendAttrDefMap;

    private boolean forbidden;


}
