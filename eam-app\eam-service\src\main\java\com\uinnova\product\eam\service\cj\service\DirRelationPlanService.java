package com.uinnova.product.eam.service.cj.service;

import com.uinnova.product.eam.model.cj.domain.DirRelationPlan;

import java.util.List;

public interface DirRelationPlanService {

    /**
     * 新增或修改资产文件夹关联方案信息
     * @param dirRelationPlan
     */
    void saveOrUpdate(DirRelationPlan dirRelationPlan);

    /**
     * 通过文件夹id获取文件夹关联方案
     * @param childDirIds
     * @return
     */
    List<DirRelationPlan> findDirRelationPlanList(List<Long> childDirIds);

    /**
     * 用户发布列表
     * @param loginCode
     * @return
     */
    List<DirRelationPlan> findUserPublishList(String loginCode);

    /**
     * 删除文件夹关联方案数据
     * @param planId
     */
    void deleteDirRelationPlan(Long planId);
}
