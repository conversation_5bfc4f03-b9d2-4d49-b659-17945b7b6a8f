package com.uino.bean.cmdb.base;

import com.binary.core.util.BinaryUtils;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.comm.model.rlt.CcCiRlt;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * ci关系
 * 
 * <AUTHOR>
 *
 */
@ApiModel(value="ci关系",description = "ci关系信息")
public class ESCIRltInfo extends CcCiRlt implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * <b>属性
     */
    @ApiModelProperty(value="属性")
    private Map<String, String> attrs;

    /**
     * 根据关系以及子code拼接成的唯一标识 ${rltCiCode}-?-?-? ?代表按顺序的子code值，未填写会填入NULL
     */
    @ApiModelProperty(value="根据关系以及子code拼接成的唯一标识,未填写会填入NULL")
    private String uniqueCode;

    /**
     * 前端源端ci节点标识key
     */
    private String sourceKey;

    /**
     * 前端目标端ci节点标识key
     */
    private String targetKey;

    private String sourceCiSearchValues;

    private String targetCiSearchValues;

    /**
     * 源关系标识
     */
    private String originCode;

    public String getOriginCode() {
        return originCode;
    }

    public void setOriginCode(String originCode) {
        this.originCode = originCode;
    }

    public String getSourceCiSearchValues() {
        return sourceCiSearchValues;
    }

    public void setSourceCiSearchValues(String sourceCiSearchValues) {
        this.sourceCiSearchValues = sourceCiSearchValues;
    }

    public String getTargetCiSearchValues() {
        return targetCiSearchValues;
    }

    public void setTargetCiSearchValues(String targetCiSearchValues) {
        this.targetCiSearchValues = targetCiSearchValues;
    }

    public String getSourceKey() {
        return sourceKey;
    }

    public void setSourceKey(String sourceKey) {
        this.sourceKey = sourceKey;
    }

    public String getTargetKey() {
        return targetKey;
    }

    public void setTargetKey(String targetKey) {
        this.targetKey = targetKey;
    }

    public String getUniqueCode() {
        return uniqueCode;
    }

    public void setUniqueCode(String uniqueCode) {
        this.uniqueCode = uniqueCode;
    }

    public Map<String, String> getAttrs() {
        return attrs;
    }

    public void setAttrs(Map<String, String> attrs) {
        Map<String, String> realAttrMap = new HashMap<>();
        if (attrs != null && attrs.size() > 0) {
            attrs.forEach((key, val) -> {
                realAttrMap.put(key.toUpperCase(), val);
            });
        }
        this.attrs = realAttrMap;
    }

    /**
     * 根据源和目标ci信息设置关系信息（不提供任何验证，使用自己注意）
     * 
     * @param sCi
     * @param tCi
     */
    public void setRltCiInfo(ESCIInfo sCi, ESCIInfo tCi,List<String> sourceSearchFiles,List<String> targetSearchFiles) {
        // 设置源信息
        this.setSourceCiId(sCi.getId());
        this.setSourceCiCode(sCi.getCiCode());
        this.setSourceClassId(sCi.getClassId());
        this.setSourceCiSearchValues(getSearchFileValues(sCi,sourceSearchFiles));
        // 设置目标信息
        this.setTargetCiId(tCi.getId());
        this.setTargetCiCode(tCi.getCiCode());
        this.setTargetClassId(tCi.getClassId());
        this.setTargetCiSearchValues(getSearchFileValues(tCi,targetSearchFiles));
        // 根据源和目标和自身已有关系分类信息计算该关系cicode
        // 继承之前规则，ci关系的ciCode拼装规则为：${sCiCode}_${rltClassId}_${tCiCode}
        String rltCiCode = new StringBuilder().append(sCi.getCiCode()).append("_").append(this.getClassId()).append("_")
                .append(tCi.getCiCode()).toString();
        this.setCiCode(rltCiCode);
    }

    private String getSearchFileValues(ESCIInfo esciInfo,List<String> files) {
        List<String> collect = files.stream().map(s ->
                esciInfo.getAttrs().get(s) == null ? "" : esciInfo.getAttrs().get(s).toString())
                .collect(Collectors.toList());
        return StringUtils.join(collect, "_");
    }

    /**
     * 替换ciCode
     * @param assetMapper
     */
    public boolean replaceRltCiInfo(Map<String,String> assetMapper) {
        String newSourceCiCode = assetMapper.get(getSourceCiCode());
        String newTargetCiCode = assetMapper.get(getTargetCiCode());
        if(BinaryUtils.isEmpty(newSourceCiCode) && BinaryUtils.isEmpty(newTargetCiCode)){
            return false;
        }
        if(!BinaryUtils.isEmpty(newSourceCiCode)){
            setSourceCiCode(newSourceCiCode);
        }
        if(!BinaryUtils.isEmpty(newTargetCiCode)){
            setTargetCiCode(newTargetCiCode);
        }
        // 继承之前规则，ci关系的ciCode拼装规则为：${sCiCode}_${rltClassId}_${tCiCode}
        String rltCiCode = new StringBuilder().append(getSourceCiCode()).append("_").append(this.getClassId()).append("_")
                .append(getTargetCiCode()).toString();
        this.setCiCode(rltCiCode);
        return true;
    }

    /**
     * 根据关系分类属性模板以及对象已存在的cicode以及属性信息拼装唯一标识
     * 
     * @param attrDefs
     */
    public void setUniqueCode(List<CcCiAttrDef> attrDefs) {
        this.uniqueCode = getUniqueCode(attrDefs, this.getAttrs(), this.getCiCode());
    }

    /**
     * 根据关系分类属性模板以及rltcode以及属性信息拼装唯一标识
     * 
     * @param attrDefs
     * @param attrs
     * @param rltCode
     * @return
     */
    public static String getUniqueCode(List<CcCiAttrDef> attrDefs, Map<String, String> attrs, String rltCode) {
        String appendCode = "";
        if (attrDefs != null && attrDefs.size() > 0) {
            List<CcCiAttrDef> isMajorDefs = attrDefs.stream()
                    .filter(def -> def.getIsMajor() != null && def.getIsMajor().intValue() == 1)
                    .collect(Collectors.toList());
            Collections.sort(isMajorDefs, new Comparator<CcCiAttrDef>() {

                @Override
                public int compare(CcCiAttrDef o1, CcCiAttrDef o2) {
                    int o1HashCode = o1.getProStdName().hashCode();
                    int o2HashCode = o2.getProStdName().hashCode();
                    if (o1HashCode == o2HashCode) {
                        return 0;
                    } else if (o1HashCode < o2HashCode) {
                        return -1;
                    } else {
                        return 1;
                    }
                }
            });
            if (isMajorDefs != null && isMajorDefs.size() > 0) {
                attrs = attrs == null ? new HashMap<>() : attrs;
                for (CcCiAttrDef def : isMajorDefs) {
                    String defKey = def.getProStdName();
                    String attrVal = attrs.get(defKey);
                    attrVal = (attrVal == null || "".equals(attrVal.trim())) ? "NULL" : attrVal;
                    appendCode += "-" + attrVal;
                }
            }
        }
        // appendCode = appendCode != null && !"".equals(appendCode) ?
        // appendCode.toUpperCase() : appendCode;
        return "".equals(appendCode) ? rltCode : rltCode + appendCode;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null || !(obj instanceof ESCIRltInfo)) { return false; }
        ESCIRltInfo rltEq = (ESCIRltInfo) obj;
        String rltCICode = this.getCiCode();
        String rltEqCICode = rltEq.getCiCode();
        if ((rltCICode == null && rltEqCICode != null) || (rltCICode != null && rltEqCICode == null)) { return false; }
        boolean rltCICodeEq = rltCICode.equals(rltEqCICode);
        if (!rltCICodeEq) { return false; }
        Map<String, String> rltAttrs = this.getAttrs();
        Map<String, String> rltEqAttrs = rltEq.getAttrs();
        if ((rltAttrs == null && rltEqAttrs != null) || (rltAttrs != null && rltEqAttrs == null)) { return false; }
        return rltAttrs.equals(rltEqAttrs);
    }
}
