package com.uinnova.product.eam.service.cj.dao;

import com.uinnova.product.eam.model.cj.domain.PlanDesignShareRecord;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
@Component
public class PlanDesignShareRecordDao extends AbstractESBaseDao<PlanDesignShareRecord,PlanDesignShareRecord> {
    @Override
    public String getIndex() {
        return "uino_cj_plan_design_share_record";
    }

    @Override
    public String getType() {
        return getIndex();
    }
    @PostConstruct
    public void init() {
        super.initIndex();
    }

}
