package com.uino.dao.cmdb;

import com.binary.core.lang.Conver;

public class CiClassProDropSourceDefHelper {

    private static final String CLASS_DEF_SEPARATOR = ":";
    public static final String DEF_IDS_SEPARATOR = ",";
    public static final String DEF_PRONAME_SEPARATOR = "/";
    public static final String IMPORT_DEF_SEPARATOR = ",";
    public static final String EXPORT_DEF_SEPARATOR = ",";

    public static Long getCiClassProDropSourceClassId(String ciClassProDropSourceDef) {
        String[] arry = getCiClassProDropSourceDefArry(ciClassProDropSourceDef);
        return Conver.to(arry[0], Long.class);
    }

    public static Long[] getCiClassProDropSourceDefIds(String ciClassProDropSourceDef) {
        String[] arry = getCiClassProDropSourceDefArry(ciClassProDropSourceDef);
        String[] dictDefIdStr = arry[1].split(DEF_IDS_SEPARATOR);
        return Conver.to(dictDefIdStr, Long.class);
    }

    private static String[] getCiClassProDropSourceDefArry(String ciClassProDropSourceDef) {
        return ciClassProDropSourceDef.replace("{", "").replace("}", "").split(CLASS_DEF_SEPARATOR);
    }
}
