package com.uino.provider.feign.cmdb;

import com.binary.jdbc.Page;
import com.uino.bean.cmdb.base.ESCiRltAutoBuild;
import com.uino.bean.cmdb.query.ESCiRltAutoBuildSearchBean;
import com.uino.provider.feign.config.BaseFeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @Classname CiRltAutoBuildFeign
 * @Description TODO
 * @Date 2020/6/30 15:31
 * <AUTHOR> sh
 */
@Component
@FeignClient(name = "${feign-server-name:tarsier-platform}", path = "${feign-server-path:tarsier-platform}/feign/ciRltAutoBuild", configuration = {BaseFeignConfig.class})
public interface CiRltAutoBuildFeign {
    /**
     * 添加默认构建规则
     *
     * @param sourceCiClassId
     * @param targetCiClassId
     * @param rltClassId
     * @param visualModelId
     */
    @PostMapping("saveDefault")
    ESCiRltAutoBuild saveDefault(@RequestParam(value = "domainId") Long domainId, @RequestParam(value = "sourceCiClassId") Long sourceCiClassId, @RequestParam(value = "targetCiClassId") Long targetCiClassId, @RequestParam(value = "rltClassId") Long rltClassId, @RequestParam(value = "visualModelId") Long visualModelId);

    /**
     * 修改构建规则
     *
     * @param esCiRltAutoBuild
     */
    @PostMapping("updateCiRltAutoBuild")
    Long updateCiRltAutoBuild(@RequestBody ESCiRltAutoBuild esCiRltAutoBuild);

    /**
     * 查看构建规则
     *
     * @param sourceCiClassId
     * @param targetCiClassId
     * @param rltClassId
     * @param visualModelId
     * @return
     */
    @PostMapping("findCiRltAutoBuild")
    ESCiRltAutoBuild findCiRltAutoBuild(@RequestParam(value = "domainId") Long domainId, @RequestParam(value = "sourceCiClassId") Long sourceCiClassId, @RequestParam(value = "targetCiClassId") Long targetCiClassId, @RequestParam(value = "rltClassId") Long rltClassId, @RequestParam(value = "visualModelId") Long visualModelId);

    /**
     * 删除构建规则
     *
     * @param sourceCiClassId
     * @param targetCiClassId
     * @param rltClassId
     * @param visualModelId
     */
    @PostMapping("deleteCiRltAutoBuild")
    void deleteCiRltAutoBuild(@RequestParam(value = "sourceCiClassId") Long sourceCiClassId, @RequestParam(value = "targetCiClassId") Long targetCiClassId, @RequestParam(value = "rltClassId") Long rltClassId, @RequestParam(value = "visualModelId") Long visualModelId);

    /**
     * 执行关系自动构建
     *
     * @param
     */
    @PostMapping("runCiRltAutoBuildAll")
    void runCiRltAutoBuildAll(@RequestBody Long domainId);

    @PostMapping("runCiRltAutoBuildById")
    boolean runCiRltAutoBuildById(@RequestParam(value = "id") Long id);

    /**
     * 保存构建规则
     * @param esCiRltAutoBuild
     * @return
     */
    @PostMapping("/saveCiRltAutoBuild")
    Long saveCiRltAutoBuild(@RequestBody ESCiRltAutoBuild esCiRltAutoBuild);

    /**
     * 分页查询
     * @param searchBean
     * @return
     */
    @PostMapping("/queryCiRltAutoBuildPage")
    Page<ESCiRltAutoBuild> queryCiRltAutoBuildPage(@RequestBody ESCiRltAutoBuildSearchBean searchBean);

    /**
     * 根据主键删除
     * @param id
     * @return
     */
    @PostMapping("deleteById")
    Integer deleteById(@RequestParam(value = "id") Long id);

    /**
     * 直接保存构建规则
     * @param esCiRltAutoBuild
     * @return
     */
    @PostMapping("saveOrUpdate")
    Long saveOrUpdate(@RequestBody ESCiRltAutoBuild esCiRltAutoBuild);

}
