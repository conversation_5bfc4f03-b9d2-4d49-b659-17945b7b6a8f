package com.uinnova.product.eam.service.diagram;


import com.uinnova.product.eam.base.diagram.enums.DiagramCopyEnum;
import com.uinnova.product.eam.base.diagram.model.ESDiagram;
import com.uino.bean.cmdb.base.LibType;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 视图扩展接口
 * <AUTHOR>
 */
public interface ESDiagramExtendSvc {
    /**
     * 批量更新视图内容
     * @param updateIdMap 更新视图集合<需要被更新内容的视图id，获取更新内容的视图id>
     * @param libType 我的空间视图更新到资产仓库视图->DESIGN(发布);资产仓库视图更新到我的空间视图->PRIVATE(检出)
     * @return 更新成功的<被更新内容的视图id，获取更新内容的视图id>
     */
    Map<String,String> updateDiagramBatch(Map<String, String> updateIdMap, LibType libType);

    /**
     * 批量更新视图内容
     * @param updateIdMap 更新视图集合<需要被更新内容的视图id，获取更新内容的视图id>
     * @param diagramList 视图信息
     * @param libType 我的空间视图更新到资产仓库视图->DESIGN(发布);资产仓库视图更新到我的空间视图->PRIVATE(检出)
     * @return 更新成功的<被更新内容的视图id，获取更新内容的视图id>
     */
    Map<String,String> updateDiagramBatch(Map<String, String> updateIdMap, List<ESDiagram> diagramList, LibType libType);

    /**
     * 批量创建视图历史版本
     * @param diagramList 需要创建历史版本的视图信息
     */
    void createDiagramHistoryBatch(List<ESDiagram> diagramList);

    /**
     * 批量copy视图
     * @param diagramDirIdMap <被copy视图加密id, copy视图至目录id>
     * @param diagramList 被copy视图集合
     * @param type 我的空间视图copy到资产仓库视图->PUSH(发布);资产仓库视图copy到我的空间视图->PULL(检出)
     * @return copy的<被copy的视图加密id，copy出的新视图加密id>
     */
    Map<String, String> copyDiagramBatch(Map<String, Long> diagramDirIdMap, List<ESDiagram> diagramList, DiagramCopyEnum type);

    /**
     * 批量更新视图所在文件夹id
     * @param diagramDirIdMap <视图加密id, 视图更新至目录id>
     */
    void updateDirIdBatch(Map<String, Long> diagramDirIdMap);

    /**
     * 更新视图缩略图
     * @param updateIcon 更新视图路径
     * @param sourceIcon 源视图路径
     */
    void updateIcon(String updateIcon, String sourceIcon);

    /**
     * 批量更新视图缩略图
     * @param updateIconMap <更新视图路径, 源视图路径>
     */
    void updateIconBatch(Map<String, String> updateIconMap);

    /**
     * 删除视图分享记录
     * @param diagramIds 视图id
     */
    void deleteShareLink(Collection<String> diagramIds);

    /**
     * 通过视图发布id查询视图
     * @param diagramIds 视图加密id
     * @param ownerCode 用户标识
     * @param open 发布状态（0未发布，1已发布）
     * @return 视图
     */
    List<ESDiagram> queryByReleaseDiagramId(Collection<String> diagramIds, String ownerCode, Integer open);

    /**
     * 更新视图发布版本(将源视图发布版本更新至被更新视图)
     * @param diagramIdMap 视图id<被更新视图加密id, 源视图加密id>
     */
    void updateDiagramPublicVersion(Map<String, String> diagramIdMap);

    /**
     *  回滚视图 将快照信息回滚至关联的主版本
     * @param snapshotInfo 快照视图信息
     * @param mainInfo 快照关联的主视图信息
     */
    void rollBackDiagramById(ESDiagram snapshotInfo, ESDiagram mainInfo);
}
