package com.uino.bean.monitor.query;

import com.uino.bean.cmdb.query.ESSearchBase;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel(value = "模拟规则查询类")
public class SimulationRuleSearchBean extends ESSearchBase {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "搜索关键字", example = "模拟性能")
	private String keyword;
	
	@ApiModelProperty(value = "启用状态：0=关闭;1=开启", example = "0")
	private Integer ruleStatus;

	@ApiModelProperty(value = "规则类型，1=性能，2=告警", example = "1")
	private Integer ruleType;

	@ApiModelProperty(value = "所属域", example = "1")
	private Long domainId;
}
