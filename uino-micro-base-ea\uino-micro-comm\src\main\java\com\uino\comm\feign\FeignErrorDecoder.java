package com.uino.comm.feign;

import org.apache.commons.io.IOUtils;

import com.alibaba.fastjson.JSONObject;
import com.binary.framework.web.RemoteResult;
import com.uino.comm.feign.exception.FeignInterfaceException;

import feign.Response;
import feign.codec.ErrorDecoder;

//@Slf4j
public class FeignErrorDecoder implements ErrorDecoder {

    @Override
    public Exception decode(String methodKey, Response response) {
        if (response.body() != null) {
            try {
                String string = IOUtils.toString(response.body().asInputStream(), "UTF-8");
                RemoteResult remoteResult = JSONObject.parseObject(string, RemoteResult.class);
                if (remoteResult != null) { return new FeignInterfaceException(remoteResult.getMessage()); }
            } catch (Exception e) {
                return e;
            }
        }
        return new FeignInterfaceException(methodKey);
    }
}
