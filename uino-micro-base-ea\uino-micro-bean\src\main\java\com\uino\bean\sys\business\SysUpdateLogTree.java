package com.uino.bean.sys.business;

import java.util.LinkedList;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 
 * <AUTHOR>
 *
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value="系统更新日志树",description = "系统更新日志树信息")
public class SysUpdateLogTree {

	/**
	 * 标题
	 */
	@ApiModelProperty(value="标题",example = "updatelog")
	private String title;
	/**
	 * 版本
	 */
	@ApiModelProperty(value="版本",example = "xxx1")
	private String version;

	/**
	 * 修改时间20101203-第一层按照修改时间倒叙
	 */
	@ApiModelProperty(value="修改时间,第一层按照修改时间倒序",example = "20210604")
	private Long updateTime;

	/**
	 * 修改时间-字符串
	 */
	@ApiModelProperty(value="修改时间-字符串")
	private String updateTimeStr;

	/**
	 * 排序序号-第二层按照该字段升序
	 */
	@ApiModelProperty(value="排序序号,第二层按照该字段升序")
	private int orderNo;

	/**
	 * 子集
	 */
	@ApiModelProperty(value="子集")
	@Builder.Default
	private List<SysUpdateLogTree> children = new LinkedList<>();

	/**
	 * 修改记录-只有第二级下有
	 */
	@ApiModelProperty(value="修改记录，只有第二级下有")
	@Builder.Default
	private List<SysUpdateLogDto> records = new LinkedList<>();
}
