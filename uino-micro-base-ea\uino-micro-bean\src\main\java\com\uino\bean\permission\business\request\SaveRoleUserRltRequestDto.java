package com.uino.bean.permission.business.request;

import java.util.Set;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.util.Assert;

import com.uino.bean.permission.business.IValidDto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 
 * <AUTHOR>
 *
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value="角色用户关联信息",description = "角色用户关联信息")
public class SaveRoleUserRltRequestDto implements IValidDto {

    /**
     * 角色id
     */
    @ApiModelProperty(value="角色id",example = "123")
    private Long roleId;
    /**
     * 拥有该角色的用户id
     */
    @ApiModelProperty(value="拥有该角色的用户id")
    private Set<Long> userIds;

    @ApiModelProperty(value="所属域id")
    private  Long  domainId;

    @Override
    public void valid() {
        Assert.notNull(roleId, "roleId is null");
    }
}
