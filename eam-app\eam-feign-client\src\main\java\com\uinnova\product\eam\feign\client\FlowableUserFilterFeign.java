package com.uinnova.product.eam.feign.client;

import com.uinnova.product.eam.comm.model.es.FlowableUser;
import com.uino.provider.feign.config.BaseFeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

@FeignClient(name = "${fuxi-feign-server-name:eam-fuxi}"
        , path = "${fuxi-feign-server-path:tarsier-eam}/flowable",
        configuration = {BaseFeignConfig.class})
public interface FlowableUserFilterFeign {

    /**
     * 添加任务审批同意的人
     * @param request
     * @param response
     * @param flowableUser
     */
    @PostMapping("/addAgreeUser")
    public void addAgreeUser(HttpServletRequest request, HttpServletResponse response, @RequestBody FlowableUser flowableUser);

    /**
     * 获取审批同意人列表
     * @param request
     * @param response
     * @param flowableUser
     */
    @PostMapping("/getAgreeUserList")
    public void getAgreeUserList(HttpServletRequest request, HttpServletResponse response, @RequestBody FlowableUser flowableUser);
}
