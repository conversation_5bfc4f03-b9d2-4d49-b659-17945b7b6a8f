package com.uinnova.product.eam.service.manage;

import com.binary.jdbc.Page;
import com.uinnova.product.eam.comm.dto.EamMatrixQueryVO;
import com.uinnova.product.eam.comm.model.es.EamMatrixStencil;

import java.util.List;
import java.util.Set;

public interface EamMatrixStencilSvc {

    /**
     * 保存或更新
     */
    Long saveOrUpdate(EamMatrixStencil vo);

    /**
     * 保存或更新详细信息
     */
    Long saveOrUpdateDetail(EamMatrixStencil vo);

    /**
     * 查询矩阵制品列表
     */
    Page<EamMatrixStencil> queryList(EamMatrixQueryVO dto);

    /**
     * 更新发布状态
     * @param id 制品id
     * @param published 0 未发布，1 已发布
     * @return 制品id
     */
    Long updatePublished(Long id, Integer published);

    /**
     * 删除
     * @param id 制品id
     * @return 结果
     */
    Integer deleteById(Long id);

    /**
     * 根据id查询
     * @param id 制品id
     * @return 制品
     */
    EamMatrixStencil getById(Long id);

    /**
     * 查询历史版本
     * @param id 制品id
     * @param version 制品版本
     * @return 制品
     */
    EamMatrixStencil getHistoryById(Long id, Integer version);

    /**
     * 根据矩阵制品类型查询制品信息
     *
     * @param typerIds 制品类型分类
     */
    List<EamMatrixStencil> queryMatrixStencilByType(Set<Long> typerIds);

    /**
     * 根据矩阵表格id查询矩阵分类名称
     * @param matrixStencilId  矩阵制品ID
     * @return String
     */
    String queryStencilTypeNameByMatrixId(Long matrixStencilId);
}
