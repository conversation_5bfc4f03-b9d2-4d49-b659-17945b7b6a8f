package com.uinnova.product.eam.web.diagram.bean;

import java.util.List;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.comm.model.VcDiagram;
import com.uinnova.product.eam.comm.model.VcDiagramEle;

@Comment("视图自动保存使用接口")
public class AutoSaveDiagram {

	@Comment("视图ID")
	private Long id;

	@Comment("视图xml信息")
	private String xml;

	@Comment("视图SVG信息")
	private String svg;
	
	@Comment("缩略图base64编码")
	private String thumbnail;
	
	@Comment("视图坐标")
	private String ci3dPoint;

	@Comment("ci 3d DMV坐标信息")
	private String ci3dPoint2;
	
	@Comment("视图背景图片")
	private String diagramBgImg;

	@Comment("视图背景颜色")
	private String diagramBgCss;
	
	@Comment("视图关联元素信息")
	private List<VcDiagramEle> diagramEles;
	
	@Comment("视图信息")
	private VcDiagram diagram;
	
	@Comment("视图json信息")
	private String json;
	
	public String getJson() {
		return json;
	}

	public void setJson(String json) {
		this.json = json;
	}

	public String getThumbnail() {
		return thumbnail;
	}

	public void setThumbnail(String thumbnail) {
		this.thumbnail = thumbnail;
	}

	public String getCi3dPoint2() {
		return ci3dPoint2;
	}

	public void setCi3dPoint2(String ci3dPoint2) {
		this.ci3dPoint2 = ci3dPoint2;
	}

	public VcDiagram getDiagram() {
		return diagram;
	}

	public void setDiagram(VcDiagram diagram) {
		this.diagram = diagram;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getXml() {
		return xml;
	}

	public void setXml(String xml) {
		this.xml = xml;
	}

	public String getSvg() {
		return svg;
	}

	public void setSvg(String svg) {
		this.svg = svg;
	}

	public List<VcDiagramEle> getDiagramEles() {
		return diagramEles;
	}

	public void setDiagramEles(List<VcDiagramEle> diagramEles) {
		this.diagramEles = diagramEles;
	}

	public String getCi3dPoint() {
		return ci3dPoint;
	}

	public void setCi3dPoint(String ci3dPoint) {
		this.ci3dPoint = ci3dPoint;
	}

	public String getDiagramBgImg() {
		return diagramBgImg;
	}

	public void setDiagramBgImg(String diagramBgImg) {
		this.diagramBgImg = diagramBgImg;
	}

	public String getDiagramBgCss() {
		return diagramBgCss;
	}

	public void setDiagramBgCss(String diagramBgCss) {
		this.diagramBgCss = diagramBgCss;
	}
	
	

}
