package com.uinnova.product.vmdb.provider.ci.bean;

import com.binary.framework.bean.annotation.Comment;

import java.io.Serializable;
import java.util.Map;

/**
 * 
 * <AUTHOR>
 *
 */
public class CcCiRecord implements Serializable {
    private static final long serialVersionUID = 1L;

    @Comment("导入时用于记录排序")
    private Integer index;

    @Comment("CI_ID")
    private Long ciId;

    @Comment("CI_CODE")
    private String ciCode;

    @Comment("CI属性")
    private Map<String, String> attrs;

    public Long getCiId() {
        return ciId;
    }

    public void setCiId(Long ciId) {
        this.ciId = ciId;
    }

    public String getCiCode() {
        return ciCode;
    }

    public void setCiCode(String ciCode) {
        this.ciCode = ciCode;
    }

    public Map<String, String> getAttrs() {
        return attrs;
    }

    public void setAttrs(Map<String, String> attrs) {
        this.attrs = attrs;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

}
