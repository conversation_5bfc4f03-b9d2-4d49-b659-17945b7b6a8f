package com.uinnova.product.eam.model.dm.bean;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.model.vo.EntityAndAttributeVo;
import com.uinnova.product.vmdb.comm.model.ci.CcCi;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uino.bean.cmdb.base.ESCIInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description 实体和属性返回结构
 */
@Data
public class AttrAndCiDto {


    @Comment("分类属性定义")
    private List<CcCiAttrDef> entityAttrDefs;

    @ApiModelProperty(value="ci实体信息")
    private CcCi entityCi;

    @ApiModelProperty(value="ci属性信息")
    private List<ESCIInfo> attrCiList;

    @Comment("分类属性定义")
    private List<CcCiAttrDef> attrAttrDefs;

    @Comment("实体和属性信息")
    List<EntityAndAttributeVo> entityAndAttributeVos;

    @Comment("实体下的属性个数")
    private int count =0;




}
