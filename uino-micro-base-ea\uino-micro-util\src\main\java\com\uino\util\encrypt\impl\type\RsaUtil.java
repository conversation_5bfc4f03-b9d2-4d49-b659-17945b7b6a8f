package com.uino.util.encrypt.impl.type;

import lombok.extern.log4j.Log4j2;
import org.apache.commons.codec.binary.Base64;

import javax.crypto.Cipher;
import java.io.ByteArrayOutputStream;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;


/**
 * @Title: RSAUtil
 * @Author: YGQ
 * @Create: 2021-08-08 13:16
 **/
@Log4j2
public class RsaUtil {
    /**
     * rsa maximum encrypted plaintext size
     */
    private static final int MAX_ENCRYPT_BLOCK = 117;

    /**
     * rsa maximum decrypted ciphertext size
     */
    private static final int MAX_DECRYPT_BLOCK = 128;

    /**
     * get the key pair
     *
     * @return key pair
     */
    public static KeyPair getKeyPair() throws Exception {
        KeyPairGenerator generator = KeyPairGenerator.getInstance("RSA");
        generator.initialize(1024);
        return generator.generateKeyPair();
    }

    /**
     * get private key
     *
     * @param privateKey private key string
     */
    public static PrivateKey getPrivateKey(String privateKey) {
        PrivateKey result = null;
        try {
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            byte[] decodedKey = Base64.decodeBase64(privateKey.getBytes());
            PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(decodedKey);
            result = keyFactory.generatePrivate(keySpec);
        } catch (Exception e) {
            log.error(">>> ", e);
        }
        return result;
    }

    /**
     * get public key
     *
     * @param publicKey public key string
     */
    public static PublicKey getPublicKey(String publicKey) {
        PublicKey result = null;
        try {
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            byte[] decodedKey = Base64.decodeBase64(publicKey.getBytes());
            X509EncodedKeySpec keySpec = new X509EncodedKeySpec(decodedKey);
            result = keyFactory.generatePublic(keySpec);
        } catch (Exception e) {
            log.error(">>> ", e);
        }
        return result;
    }

    /**
     * rsa encryption
     *
     * @param publicKey public key
     * @param data      data to be encrypted
     */
    public static String encrypt(PublicKey publicKey, String data) {
        String result = null;
        try {
            Cipher cipher = Cipher.getInstance("RSA");
            cipher.init(Cipher.ENCRYPT_MODE, publicKey);
            int inputLen = data.getBytes().length;
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            int offset = 0;
            byte[] cache;
            int i = 0;
            while (inputLen - offset > 0) {
                if (inputLen - offset > MAX_ENCRYPT_BLOCK) {
                    cache = cipher.doFinal(data.getBytes(), offset, MAX_ENCRYPT_BLOCK);
                } else {
                    cache = cipher.doFinal(data.getBytes(), offset, inputLen - offset);
                }
                out.write(cache, 0, cache.length);
                i++;
                offset = i * MAX_ENCRYPT_BLOCK;
            }
            byte[] encryptedData = out.toByteArray();
            out.close();
            result = Base64.encodeBase64String(encryptedData);
        } catch (Exception e) {
            log.error(">>> ", e);
        }
        return result;
    }

    /**
     * RSA解密
     *
     * @param privateKey 私钥
     * @param data       待解密数据
     */
    public static String decrypt(PrivateKey privateKey, String data) {
        String result = null;
        try {
            Cipher cipher = Cipher.getInstance("RSA");
            cipher.init(Cipher.DECRYPT_MODE, privateKey);
            byte[] dataBytes = Base64.decodeBase64(data);
            int inputLen = dataBytes.length;
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            int offset = 0;
            byte[] cache;
            int i = 0;
            while (inputLen - offset > 0) {
                if (inputLen - offset > MAX_DECRYPT_BLOCK) {
                    cache = cipher.doFinal(dataBytes, offset, MAX_DECRYPT_BLOCK);
                } else {
                    cache = cipher.doFinal(dataBytes, offset, inputLen - offset);
                }
                out.write(cache, 0, cache.length);
                i++;
                offset = i * MAX_DECRYPT_BLOCK;
            }
            byte[] decryptedData = out.toByteArray();
            out.close();
            result = new String(decryptedData, StandardCharsets.UTF_8);
        }  catch (Exception e) {
            log.error(">>> ", e);
        }
        return result;
    }

    /**
     * signature
     *
     * @param data       data to be signed
     * @param privateKey private key
     * @return signature
     */
    public static String sign(String data, PrivateKey privateKey) throws Exception {
        byte[] keyBytes = privateKey.getEncoded();
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PrivateKey key = keyFactory.generatePrivate(keySpec);
        Signature signature = Signature.getInstance("MD5withRSA");
        signature.initSign(key);
        signature.update(data.getBytes());
        return new String(Base64.encodeBase64(signature.sign()));
    }

    /**
     * verification
     *
     * @param srcData   raw string
     * @param publicKey public key
     * @param sign      signature
     * @return whether to pass the verification
     */
    public static boolean verify(String srcData, PublicKey publicKey, String sign) throws Exception {
        byte[] keyBytes = publicKey.getEncoded();
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PublicKey key = keyFactory.generatePublic(keySpec);
        Signature signature = Signature.getInstance("MD5withRSA");
        signature.initVerify(key);
        signature.update(srcData.getBytes());
        return signature.verify(Base64.decodeBase64(sign.getBytes()));
    }
}
