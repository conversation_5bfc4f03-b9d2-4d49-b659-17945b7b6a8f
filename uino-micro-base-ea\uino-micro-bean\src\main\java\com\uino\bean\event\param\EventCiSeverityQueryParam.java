package com.uino.bean.event.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * 封装请求参数
 */
@Data
public class EventCiSeverityQueryParam implements Serializable {

    @ApiModelProperty("ciCode集合")
    private Set<String> ciCodes;

    @ApiModelProperty("severity集合")
    private List<Integer> severitys;

    @ApiModelProperty("status集合")
    private List<Integer> status;
}
