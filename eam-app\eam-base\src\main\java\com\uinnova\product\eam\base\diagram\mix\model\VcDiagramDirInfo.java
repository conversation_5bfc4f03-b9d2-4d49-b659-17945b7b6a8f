package com.uinnova.product.eam.base.diagram.mix.model;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.io.Serializable;

@Data
@Comment("视图目录信息")
public class VcDiagramDirInfo implements Serializable{
	private static final long serialVersionUID = 1L;
	
	@Comment("目录id")
	private Long dirId;
	
	@Comment("目录下视图数量")
	private Integer diagramCount;
	
	@Comment("目录名称")
	private String dirName;
	
	@Comment("父级id")
	private Long parentId;
	
	@Comment("用户id")
	private Long userId;
	
	@Comment("目录类型")
	private Integer dirType;

}
