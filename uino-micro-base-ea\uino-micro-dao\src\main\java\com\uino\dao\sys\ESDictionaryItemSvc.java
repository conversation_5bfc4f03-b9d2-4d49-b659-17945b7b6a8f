package com.uino.dao.sys;

import com.alibaba.fastjson.JSONObject;
import com.binary.core.util.BinaryUtils;
import com.uino.dao.AbstractESBaseDao;
import com.uino.dao.ESConst;
import com.uino.dao.cmdb.CiClassProDropSourceDefHelper;
import com.uino.util.sys.CommonFileUtil;
import com.uino.bean.cmdb.query.ESAttrBean;
import com.uino.bean.sys.base.ESDictionaryAttrDef;
import com.uino.bean.sys.base.ESDictionaryClassInfo;
import com.uino.bean.sys.base.ESDictionaryItemInfo;
import com.uino.bean.sys.enums.DictionaryOptionEnum;
import com.uino.bean.sys.query.ESDictionaryItemSearchBean;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.PostConstruct;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
public class ESDictionaryItemSvc extends AbstractESBaseDao<ESDictionaryItemInfo, JSONObject> {

	@Autowired
	private ESDictionaryClassSvc dictClsSvc;

    @Override
    public String getIndex() {
        return ESConst.INDEX_SYS_DICTIONARY_ITEM;
    }

    @Override
    public String getType() {
        return ESConst.INDEX_SYS_DICTIONARY_ITEM;
    }

    @PostConstruct
    public void init() {
		List<ESDictionaryItemInfo> datas = CommonFileUtil.getData("/initdata/uino_sys_dictionary_item.json",
				ESDictionaryItemInfo.class);
        for (ESDictionaryItemInfo item : datas) {
            if (BinaryUtils.isEmpty(item.getOption())) {
                item.setOption(DictionaryOptionEnum.READ);
            }
        }
        super.initIndex(datas);
    }

	public List<String> getAttrValues(Long dictClassId, Long dictDefId) {
		List<String> res = new ArrayList<String>();
		ESDictionaryClassInfo dictClassInfo = dictClsSvc.getById(dictClassId);
		Assert.notNull(dictClassInfo, "字典表不存在");
		Map<Long, ESDictionaryAttrDef> defMap = BinaryUtils.toObjectMap(dictClassInfo.getDictAttrDefs(), "id");
		Assert.isTrue(defMap.containsKey(dictDefId), "字典属性不存在");
		long count = this.countByCondition(QueryBuilders.termQuery("dictClassId", dictClassId));
		ESDictionaryAttrDef dictDef = defMap.get(dictDefId);
		List<ESDictionaryItemInfo> items = this.getSortListByQuery(1, new BigDecimal(count).intValue(),
				QueryBuilders.termQuery("dictClassId", dictClassId), "id", true).getData();
		for (ESDictionaryItemInfo item : items) {
			Map<String, String> attrs = item.getAttrs();
			if (attrs.containsKey(dictDef.getProName())) {
				res.add(attrs.get(dictDef.getProName()));
			}
		}
		return res;
	}

	public List<String> getAttrValues(Long dictClassId, Long[] dictDefIds) {
		List<String> res = new ArrayList<String>();
		ESDictionaryClassInfo dictClassInfo = dictClsSvc.getById(dictClassId);
		Assert.notNull(dictClassInfo, "字典表不存在");
		Map<Long, ESDictionaryAttrDef> defMap = BinaryUtils.toObjectMap(dictClassInfo.getDictAttrDefs(), "id");
		Set<String> dictDefProNameSet = new LinkedHashSet<>();
		for (Long dictDefId : dictDefIds) {
			Assert.isTrue(defMap.containsKey(dictDefId), "字典属性不存在");
			dictDefProNameSet.add(defMap.get(dictDefId).getProName());
		}
		long count = this.countByCondition(QueryBuilders.termQuery("dictClassId", dictClassId));
		List<ESDictionaryItemInfo> items = this.getSortListByQuery(1, new BigDecimal(count).intValue(),
				QueryBuilders.termQuery("dictClassId", dictClassId), "id", true).getData();

		for (ESDictionaryItemInfo item : items) {
			this.addAttrValue(res, dictDefProNameSet, item.getAttrs());
		}
		return res;
	}

	private void addAttrValue(List<String> res, Set<String> dictDefProNameSet, Map<String, String> attrs) {
		List<String> attrValues = new ArrayList<>();
		for (String dictDefProName : dictDefProNameSet) {
			String value =  attrs.get(dictDefProName);
			if (StringUtils.isBlank(value)) {
				continue;
			}
			attrValues.add(value);
		}
		if (!CollectionUtils.isEmpty(attrValues)) {
			res.add(String.join(CiClassProDropSourceDefHelper.DEF_PRONAME_SEPARATOR, attrValues));
		}
	}

	/**
	 * <b>根据查询Bean 获取字典查询query
	 *
	 * @param bean
	 * @return
	 */
	public QueryBuilder getDictQueryBuilderByBean(ESDictionaryItemSearchBean bean) {
		BoolQueryBuilder query = QueryBuilders.boolQuery();
		query.must(QueryBuilders.termQuery("domainId",bean.getDomainId()));
		BoolQueryBuilder clsQuery = this.getDictClassQueryBuilderByBean(bean);
		List<ESDictionaryClassInfo> dictClassInfos = dictClsSvc.getListByQuery(clsQuery);
		if (BinaryUtils.isEmpty(dictClassInfos)) {
			return null;
		}
		List<Long> dictClassIds = dictClassInfos.stream().map(ESDictionaryClassInfo::getId).collect(Collectors.toList());
		query.must(QueryBuilders.termsQuery("dictClassId",dictClassIds));
		if (!BinaryUtils.isEmpty(bean.getKeyword())) {
			query.must(QueryBuilders.multiMatchQuery(bean.getKeyword(), "attrs.*").operator(Operator.AND).type(MultiMatchQueryBuilder.Type.PHRASE_PREFIX).lenient(true));
		}
		
        if (!BinaryUtils.isEmpty(bean.getKeyCodes())) {
            query.must(QueryBuilders.termsQuery("keyCode.keyword", bean.getKeyCodes()));
        }
		
		// 属性查询 And 关系
		List<ESAttrBean> andAttrs = bean.getAndAttrs();
		if (!BinaryUtils.isEmpty(andAttrs)) {
			BoolQueryBuilder queryAndAttrs = QueryBuilders.boolQuery();
			for (ESAttrBean attr : andAttrs) {
				String key = attr.getKey();
				Object value = attr.getValue();
				int opType = attr.getOptType();
				String str = "attrs." + key;
				// opType=1表示绝对等于,opType=2表示全文检索
				if (opType == 1) {
					queryAndAttrs.must(QueryBuilders.termQuery(str + ".keyword", value));
				} else if (opType == 2) {
					queryAndAttrs.must(QueryBuilders.multiMatchQuery(value, str).operator(Operator.AND).type(MultiMatchQueryBuilder.Type.PHRASE_PREFIX).lenient(true));
				}
			}
			if (andAttrs.size() > 0) {
				query.must(queryAndAttrs);
			}
		}
		// 属性查询or关系
		List<ESAttrBean> orAttrs = bean.getOrAttrs();
		if (!BinaryUtils.isEmpty(orAttrs)) {
			BoolQueryBuilder queryOrAttrs = QueryBuilders.boolQuery();
			for (ESAttrBean attrBean : orAttrs) {
				String key = attrBean.getKey();
				Object value = attrBean.getValue();
				int opType = attrBean.getOptType();
				String str = "attrs." + key;
				// opType=1表示绝对等于,opType=2表示全文检索
				if (opType == 1) {
					queryOrAttrs.should(QueryBuilders.termQuery(str + ".keyword", value));
				} else if (opType == 2) {
					queryOrAttrs.should(QueryBuilders.multiMatchQuery(value, str).operator(Operator.AND).type(MultiMatchQueryBuilder.Type.PHRASE_PREFIX).lenient(true));
				}
			}
			if (orAttrs.size() > 0) {
				query.must(queryOrAttrs);
			}
		}
		return query;
	}


	/**
	 * <b>根据查询Bean 获取字典查询query
	 *
	 * @param bean
	 * @return
	 */
	public QueryBuilder getDictQueryBuilderByIds(ESDictionaryItemSearchBean bean) {
		BoolQueryBuilder query = QueryBuilders.boolQuery();
		query.must(QueryBuilders.termQuery("domainId",bean.getDomainId()));
		BoolQueryBuilder clsQuery = this.getDictClassQueryBuilderByBean(bean);
		List<ESDictionaryClassInfo> dictClassInfos = dictClsSvc.getListByQuery(clsQuery);
		if (BinaryUtils.isEmpty(dictClassInfos)) {
			return null;
		}
		//查出多条数据
		Long[] dictClassIds = dictClassInfos.stream().map(ESDictionaryClassInfo :: getId).collect(Collectors.toList()).toArray(new Long[dictClassInfos.size()]);
		if (!BinaryUtils.isEmpty(dictClassIds)) {
			query.must(QueryBuilders.termsQuery("dictClassId", dictClassIds));
		}

		if (!BinaryUtils.isEmpty(bean.getKeyword())) {
			query.must(QueryBuilders.multiMatchQuery(bean.getKeyword(), "attrs.*").operator(Operator.AND).type(MultiMatchQueryBuilder.Type.PHRASE_PREFIX).lenient(true));
		}

		if (!BinaryUtils.isEmpty(bean.getKeyCodes())) {
			query.must(QueryBuilders.termsQuery("keyCode.keyword", bean.getKeyCodes()));
		}

		// 属性查询 And 关系
		List<ESAttrBean> andAttrs = bean.getAndAttrs();
		if (!BinaryUtils.isEmpty(andAttrs)) {
			BoolQueryBuilder queryAndAttrs = QueryBuilders.boolQuery();
			for (ESAttrBean attr : andAttrs) {
				String key = attr.getKey();
				Object value = attr.getValue();
				int opType = attr.getOptType();
				String str = "attrs." + key;
				// opType=1表示绝对等于,opType=2表示全文检索
				if (opType == 1) {
					queryAndAttrs.must(QueryBuilders.termQuery(str + ".keyword", value));
				} else if (opType == 2) {
					queryAndAttrs.must(QueryBuilders.multiMatchQuery(value, str).operator(Operator.AND).type(MultiMatchQueryBuilder.Type.PHRASE_PREFIX).lenient(true));
				}
			}
			if (andAttrs.size() > 0) {
				query.must(queryAndAttrs);
			}
		}
		// 属性查询or关系
		List<ESAttrBean> orAttrs = bean.getOrAttrs();
		if (!BinaryUtils.isEmpty(orAttrs)) {
			BoolQueryBuilder queryOrAttrs = QueryBuilders.boolQuery();
			for (ESAttrBean attrBean : orAttrs) {
				String key = attrBean.getKey();
				Object value = attrBean.getValue();
				int opType = attrBean.getOptType();
				String str = "attrs." + key;
				// opType=1表示绝对等于,opType=2表示全文检索
				if (opType == 1) {
					queryOrAttrs.should(QueryBuilders.termQuery(str + ".keyword", value));
				} else if (opType == 2) {
					queryOrAttrs.should(QueryBuilders.multiMatchQuery(value, str).operator(Operator.AND).type(MultiMatchQueryBuilder.Type.PHRASE_PREFIX).lenient(true));
				}
			}
			if (orAttrs.size() > 0) {
				query.must(queryOrAttrs);
			}
		}
		return query;
	}

	/**
	 * <b>根据查询Bean 获取字典查询query
	 *
	 * @param bean
	 * @return
	 */
	private BoolQueryBuilder getDictClassQueryBuilderByBean(ESDictionaryItemSearchBean bean) {
		Long dictClassId = bean.getDictClassId();
		String dictCode = bean.getDictCode();
		String dictName = bean.getDictName();
		Long[] dictClassIds = bean.getDictClassIds();
		Assert.isTrue(dictClassIds !=null || dictClassId != null || dictCode != null || dictName != null, "X_PARAM_NOT_NULL${name:引用字典定义}");
		BoolQueryBuilder clsQuery = QueryBuilders.boolQuery();
		clsQuery.must(QueryBuilders.termQuery("domainId",bean.getDomainId()));
		if (!BinaryUtils.isEmpty(dictClassId)) {
			clsQuery.must(QueryBuilders.termQuery("id", dictClassId));
		}
		if (!BinaryUtils.isEmpty(dictClassIds)) {
			clsQuery.must(QueryBuilders.termsQuery("id", dictClassIds));
		}
		if (!BinaryUtils.isEmpty(dictCode)) {
			clsQuery.must(QueryBuilders.termQuery("dictCode.keyword", dictCode));
		}
		if (!BinaryUtils.isEmpty(dictName)) {
			clsQuery.must(QueryBuilders.termQuery("dictName.keyword", dictName));
		}
		return clsQuery;
	}
}
