package com.uino.bean.monitor.buiness;

import java.util.List;

import com.uino.bean.monitor.base.ESAlarm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel(value = "模拟告警数据类")
public class SimulationAlarmBean extends ESAlarm {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "孪生体的id集合", example = "[123,456]")
	private List<Long> ciIds;

}
