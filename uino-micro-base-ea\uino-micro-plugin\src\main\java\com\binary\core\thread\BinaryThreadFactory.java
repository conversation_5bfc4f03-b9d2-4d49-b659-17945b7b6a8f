package com.binary.core.thread;


import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicInteger;



public class BinaryThreadFactory implements ThreadFactory {
	private static final AtomicInteger poolId = new AtomicInteger();
	
	private final AtomicInteger nextId = new AtomicInteger();
    
    private final String prefix;
    private boolean daemon;
    private final int priority;
    private final ThreadGroup threadGroup;
    
    
    
    
    public BinaryThreadFactory() {
    	this(null, true, 0, null);
    }
    
    public BinaryThreadFactory(String poolName) {
        this(poolName, true, Thread.NORM_PRIORITY, null);
    }

    public BinaryThreadFactory(String poolName, boolean daemon) {
        this(poolName, daemon, Thread.NORM_PRIORITY, null);
    }

    public BinaryThreadFactory(String poolName, int priority) {
        this(poolName, true, priority, null);
    }
    
    
    
    public BinaryThreadFactory(String poolName, boolean daemon, int priority) {
        this(poolName, daemon, priority, null);
    }
    
    

    public BinaryThreadFactory(String poolName, boolean daemon, int priority, ThreadGroup threadGroup) {
        if(poolName==null || (poolName=poolName.trim()).length()==0) {
        	poolName = "binary-pool";
        }
        if (priority<Thread.MIN_PRIORITY || priority>Thread.MAX_PRIORITY) {
            throw new IllegalArgumentException("priority: " + priority + " (expected: Thread.MIN_PRIORITY <= priority <= Thread.MAX_PRIORITY)");
        }
        if(threadGroup == null) {
        	SecurityManager s = System.getSecurityManager();
        	threadGroup = (s != null)? s.getThreadGroup() : Thread.currentThread().getThreadGroup();
        }

        prefix = poolName + '-' + poolId.incrementAndGet() + '-';
        this.daemon = daemon;
        this.priority = priority;
        this.threadGroup = threadGroup;
    }

    
    
    
    
   
    

    @Override
    public Thread newThread(Runnable r) {
        Thread t = newThread(r, prefix + nextId.incrementAndGet());
        t.setDaemon(this.daemon);
        if(t.getPriority() != priority) {
            t.setPriority(priority);
        }
        return t;
    }
    

    
    
    protected Thread newThread(Runnable r, String name) {
        return new Thread(threadGroup, r, name);
    }
    

}
