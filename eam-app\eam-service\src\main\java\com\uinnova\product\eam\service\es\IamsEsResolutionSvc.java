package com.uinnova.product.eam.service.es;

import com.uinnova.product.eam.comm.bean.Resolution;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Repository;

import jakarta.annotation.PostConstruct;

/**
 * 架构决议操作es的数据接口层
 * <AUTHOR>
 */
@Repository
public class IamsEsResolutionSvc extends AbstractESBaseDao<Resolution, Resolution> {
    @Override
    public String getIndex() {
        return "uino_eam_resolution";
    }

    @Override
    public String getType() {
        return "_doc";
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }
}
