
EAM（企业架构管理） 后端工程
=====

[![pipeline status](https://git.uinnova.com/eam/eam/badges/develop/pipeline.svg)](https://git.uinnova.com/eam/eam/-/commits/develop)
[![coverage report](https://git.uinnova.com/eam/eam/badges/develop/coverage.svg)](https://git.uinnova.com/eam/eam/-/commits/develop)
-------------------------------------------

仓库地址说明
------------


Gitlab `https://git.uinnova.com/eam/eam.git`
		
SSH `*******************:eam/eam.git`
 
 

----------------------------------------------

如何获取代码？
-------------

### 1、Git方式

安装git命令，然后使用git命令把项目克隆到本机

* 克隆：
```bash
<NAME_EMAIL>:eam/eam.git
```

*如果以上操作提示需要输入密码，请到自己的个人页面生成`sshkey`上传后再操作即可*

### 2、HTTP方式

直接在项目页面的右上角点 `Download.zip` 按钮打包下载


问题或BUG提交
-------------



-------------------------------------------------------

CHANGELOG
---------
