package com.uinnova.product.eam.model;

import java.io.Serializable;

import com.binary.framework.bean.annotation.Comment;

@Comment("视图目录信息")
public class VcDiagramDirInfo  implements Serializable{
	private static final long serialVersionUID = 1L;
	
	@Comment("目录id")
	private Long dirId;
	
	@Comment("目录下视图数量")
	private Integer diagramCount;
	
	@Comment("目录名称")
	private String dirName;
	
	@Comment("父级id")
	private Long parentId;
	
	@Comment("用户id")
	private Long userId;
	
	@Comment("目录类型")
	private Integer dirType;

	public Long getDirId() {
		return dirId;
	}

	public void setDirId(Long dirId) {
		this.dirId = dirId;
	}

	public Integer getDiagramCount() {
		return diagramCount;
	}

	public void setDiagramCount(Integer diagramCount) {
		this.diagramCount = diagramCount;
	}

	public String getDirName() {
		return dirName;
	}

	public void setDirName(String dirName) {
		this.dirName = dirName;
	}

	public Long getParentId() {
		return parentId;
	}

	public void setParentId(Long parentId) {
		this.parentId = parentId;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public Integer getDirType() {
		return dirType;
	}

	public void setDirType(Integer dirType) {
		this.dirType = dirType;
	}
	
}
