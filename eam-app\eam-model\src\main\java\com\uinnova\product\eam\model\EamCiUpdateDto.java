package com.uinnova.product.eam.model;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 视图Ci对象更新dto
 * <AUTHOR>
 * @date 2021/10/28
 */
@Data
public class EamCiUpdateDto implements Serializable {
    private static final long serialVersionUID = 1L;
    @Comment("视图ID")
    private String diagramId;

    @Comment("视图所属人")
    private String ownerCode;

    @Comment("视图sheet页id")
    private String sheetId;

    @Comment("画布对象元素信息更新结果")
    private transient List<EamElementUpdateInfo> ciList;

    @Comment("画布关系元素信息更新结果")
    private transient List<EamElementUpdateInfo> rltList;
}
