package com.uino.bean.permission.business;

import java.util.Set;

import com.uino.bean.permission.base.SysOrg;
import com.uino.bean.permission.base.SysRole;
import com.uino.bean.permission.base.SysUser;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 用户信息表
 * 
 * <AUTHOR>
 */
@ApiModel(value="用户信息表",description = "用户信息表")
public class UserInfo extends SysUser {

    private static final long serialVersionUID = 376390396704613405L;

    @ApiModelProperty(value="索引",example = "1")
    private Integer index;

    /**
     * 角色列表
     */
    @ApiModelProperty(value="角色列表")
    private Set<SysRole> roles;
    /**
     * 组织列表
     */
    @ApiModelProperty(value="组织列表")
    private Set<SysOrg> orgs;

    private boolean online;

    public static void filterSensitiveInfo(UserInfo userInfo) {
        userInfo.setEmailAdress(null);
        userInfo.setImsAdress(null);
    }

    public Set<SysRole> getRoles() {
        return roles;
    }

    public void setRoles(Set<SysRole> roles) {
        this.roles = roles;
    }

    public Set<SysOrg> getOrgs() {
        return orgs;
    }

    public void setOrgs(Set<SysOrg> orgs) {
        this.orgs = orgs;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public boolean isOnline() {
        return online;
    }

    public void setOnline(boolean online) {
        this.online = online;
    }
}
