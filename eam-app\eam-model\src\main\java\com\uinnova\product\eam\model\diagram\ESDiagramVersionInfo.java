package com.uinnova.product.eam.model.diagram;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.base.diagram.model.ESDiagramDTO;
import com.uinnova.product.eam.base.diagram.model.VcDiagramVersion;
import lombok.Data;

import java.io.Serializable;

@Data
public class ESDiagramVersionInfo implements Serializable {
	private static final long serialVersionUID = 1L;

	@Comment("历史视图")
	private VcDiagramVersion diagramVersion;

	@Comment("视图全量信息")
	private ESDiagramDTO esDiagram;

}
