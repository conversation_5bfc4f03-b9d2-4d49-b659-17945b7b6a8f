package com.uino.service.simulation;

import java.util.List;

import org.springframework.web.multipart.MultipartFile;

import com.binary.core.io.Resource;
import com.binary.jdbc.Page;
import com.uino.bean.chart.bean.UinoChartDataBean;
import com.uino.bean.monitor.base.ESPerformance;
import com.uino.bean.monitor.base.SimulationRuleInfo;
import com.uino.bean.monitor.buiness.ImportPerformanceReqDto;
import com.uino.bean.monitor.buiness.PerformanceQueryDto;
import com.uino.bean.tp.base.FinalPerformanceDTO;

/**
 * 性能
 * 
 * <AUTHOR>
 *
 */
public interface IPerformanceSvc {

    /**
     * 导入性能数据
     * 
     * @param excelFile
     *            性能数据excel
     * @param objId
     *            性能数据导入对象id {@link ESPerformance#getObjId()}
     * @param objType
     *            性能数据导入对象类型{@link ESPerformance#getObjType()}
     */
	public void importPerformance(MultipartFile excelFile, ImportPerformanceReqDto importDto);

    /**
     * 导入性能数据
     * 
     * @see IPerformanceSvc#importPerformance(MultipartFile, Long, Integer)
     * @param importDto
     *            导入信息
     */
    public void importPerformance(ImportPerformanceReqDto importDto);

    /**
     * 导出性能对象的性能模板
     * 
     * @param objId
     *            性能数据导入对象id {@link ESPerformance#getObjId()}
     * @param objType
     *            性能数据导入对象类型{@link ESPerformance#getObjType()}
     * @return file bytes
     */
    public Resource exportPerformanceTemplate(Long domainId,Long objId, Integer objType);

    /**
     * 分页查询性能-
     * 
     * @param queryDto
     * @return
     */
	public Page<FinalPerformanceDTO> searchPerformance(PerformanceQueryDto queryDto);

	/**
	 * 
	 * @description 查询性能数据历史曲线图
	 * @author: ZMJ
	 * @param queryDto
	 * @return
	 * @example
	 */
	public UinoChartDataBean<List<Double>> searchPerformanceGraph(PerformanceQueryDto queryDto);

    /**
     * 查询未关联ci的性能数据
     * @author: weixuesong
     * @date: 2021/1/29 14:25
     * @param queryDto
     * @return: com.binary.jdbc.Page<com.alibaba.fastjson.JSONObject>
     */
	Page<FinalPerformanceDTO> searchNoCiPerformance(PerformanceQueryDto queryDto);

    /**
     * 查询性能数据标签
     * @author: weixuesong
     * @date: 2021/2/24 17:33
     * @param classId
     * @return: java.util.List<java.lang.String>
     */
    List<String> getPerfDataLabel(Long classId);

	/**
	 * 模拟性能数据
	 * 
	 * @param bean
	 */
	void simulationPerformance(SimulationRuleInfo bean);
}
