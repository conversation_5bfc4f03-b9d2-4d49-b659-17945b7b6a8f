package com.uino.bean.permission.query;

import java.util.Set;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 
 * <AUTHOR>
 *
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value="关键字查询类",description = "关键字查询信息")
public class SearchKeywordBean extends SearchBase {

	private static final long serialVersionUID = 6084952400504270350L;

	/**
	 * 搜索关键字
	 */
	@ApiModelProperty(value="搜索关键字",example = "key")
	private String keyword;

	/**
	 * 目录ID
	 */
	@ApiModelProperty(value="目录ID",example = "123")
	private Long dirId;

	/**
	 * 忽略数据
	 */
	@ApiModelProperty(value="忽略数据")
	private Set<Long> ignoreDatas;

	/**
	 * 所属域id
	 */
     @ApiModelProperty(value="所属域id")
	private Long domainId;
}
