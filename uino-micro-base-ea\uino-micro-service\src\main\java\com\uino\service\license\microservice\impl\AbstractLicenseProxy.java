package com.uino.service.license.microservice.impl;

import com.uino.license.sdk.license.License;
import com.uino.service.license.microservice.ILicenseAuthSvc;
import com.uino.service.license.microservice.LicenseProxy;
import org.springframework.beans.factory.annotation.Autowired;

import com.binary.core.i18n.Language;
import com.binary.core.util.BinaryUtils;
import com.binary.framework.Local;
import com.binary.framework.bean.SimpleUser;
import com.binary.framework.bean.User;
import com.binary.framework.exception.ServiceException;
import com.uinnova.product.vmdb.comm.i18n.MessageUtil;


public abstract class AbstractLicenseProxy implements LicenseProxy {

	
	@Autowired
	private ILicenseAuthSvc licenseAuthSvc;
	
	
	private String licenseToken = "b0769e203e017171faea86baa58bc92e";
	private User taskUser;
	
	
	
	protected AbstractLicenseProxy() {
		SimpleUser user = new SimpleUser();
		user.setId(0l);
		user.setUserCode("system");
		user.setUserName("系统");
		user.setKind(2);
		user.setLanguage(Language.ZHC);
		user.setLoginCode("system");
		user.setDomainId(1l);
		this.taskUser = user;
	}
	
	
	@Override
	public License getLicense() {
		License license = null;
		try {
			boolean open = Local.isOpen();
			try {
				if(!open) {
					Local.open(this.taskUser);
					Local.getCriticalObject().setUserObject(licenseToken);
				}
				license = licenseAuthSvc.getRealLicense();
				if(!open) Local.commit();
			}catch(Throwable t) {
				if(!open) Local.rollback();
				throw t;
			}finally {
				if(!open) Local.close();
			}
		}catch(Throwable t) {
			throw BinaryUtils.transException(t, ServiceException.class);
		}
		return license;
	}
	
	
	
	
    public String getLicenseToken() {
		return licenseToken;
	}
	public void setLicenseToken(String licenseToken) {
		MessageUtil.checkEmpty(licenseToken, "licenseToken");
		this.licenseToken = licenseToken;
	}
	public User getTaskUser() {
		return taskUser;
	}
	public void setTaskUser(User taskUser) {
		MessageUtil.checkEmpty(taskUser, "taskUser");
		this.taskUser = taskUser;
	}
	
	
}
