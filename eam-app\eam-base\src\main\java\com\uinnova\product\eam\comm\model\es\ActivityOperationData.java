package com.uinnova.product.eam.comm.model.es;

import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.ToString;

/**
 * description
 *
 * <AUTHOR>
 * @since 2024/12/30 10:46
 */
@Data
@RequiredArgsConstructor
@ToString
public class ActivityOperationData {

    private Long id;

    private String sourceId;

    private String processId;

    private String processName;

    private String processTypeId;

    private String processTypeName;

    private String activityId;

    private String activityName;

    private Double duration;

    private Long activityStateTime;

    private Long activityEndTime;

}
