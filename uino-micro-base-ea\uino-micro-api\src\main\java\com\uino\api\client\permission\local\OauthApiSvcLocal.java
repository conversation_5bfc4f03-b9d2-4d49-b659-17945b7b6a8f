package com.uino.api.client.permission.local;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.uino.service.permission.microservice.IOAuthTokenSvc;
import com.uino.service.permission.microservice.IOauthResourceSvc;
import com.uino.service.permission.microservice.impl.OAuthClientSvc;
import com.uino.bean.permission.base.OAuthClientDetail;
import com.uino.bean.permission.base.OauthRefreshTokenDetail;
import com.uino.bean.permission.base.OauthResourceDetail;
import com.uino.bean.permission.base.OauthTokenDetail;
import com.uino.bean.permission.business.request.RegisterClientReq;
import com.uino.api.client.permission.IOauthApiSvc;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
public class OauthApiSvcLocal implements IOauthApiSvc {

    @Autowired
    private OAuthClientSvc clientSvc;

    /**
     * ！！不可以直接在此层调用essvc，先这样写
     */
    @Autowired
    private IOauthResourceSvc resourceSvc;

    @Autowired
    private IOAuthTokenSvc tokenSvc;

    @Override
    public OAuthClientDetail getClientInfoByCode(String clientCode) {
        // TODO Auto-generated method stub
        return clientSvc.getClientInfoByCode(clientCode);
    }

    @Override
    public void register(RegisterClientReq req) {
        // TODO Auto-generated method stub
        clientSvc.register(req);
    }

    @Override
    public void removeClientInfoByCode(String clientCode) {
        // TODO Auto-generated method stub
        clientSvc.removeClientInfoByCode(clientCode);
    }

    @Override
    public List<OAuthClientDetail> getAllClents() {
        // TODO Auto-generated method stub
        return clientSvc.getAllClient();
    }

    @Override
    public OauthResourceDetail getResourceDetail(String name) {
    	return resourceSvc.getDetail(name);
    }

    @Override
    public Long persistenceToken(OauthTokenDetail tokenDetail) {
        // TODO Auto-generated method stub
        return tokenSvc.persistence(tokenDetail);
    }

    @Override
    public OauthTokenDetail getTokenDetailByCode(String tokenCode) {
        // TODO Auto-generated method stub
        return tokenSvc.getTokenDetailByCode(tokenCode);
    }

    @Override
    public OauthTokenDetail getTokenDetailByAuthId(String authId) {
        // TODO Auto-generated method stub
        return tokenSvc.getTokenDetailByAuthId(authId);
    }

    @Override
    public List<OauthTokenDetail> getTokenDetail(String clientId, String userLoginName) {
        // TODO Auto-generated method stub
        return tokenSvc.getTokenDetail(clientId, userLoginName);
    }

    @Override
    public void delByCode(String tokenCode) {
        // TODO Auto-generated method stub
        tokenSvc.delByCode(tokenCode);
    }

    @Override
    public void delByRefreshTokenCode(String reTokenCode) {
        // TODO Auto-generated method stub
        tokenSvc.delByRefreshTokenCode(reTokenCode);
    }

    @Override
    public Long persistenceRefreshToken(OauthRefreshTokenDetail reRokenDetail) {
        // TODO Auto-generated method stub
        return tokenSvc.persistenceRefreshToken(reRokenDetail);
    }

    @Override
    public OauthRefreshTokenDetail getRefreshTokenDetail(String tokenCode) {
        // TODO Auto-generated method stub
        return tokenSvc.getRefreshTokenDetail(tokenCode);
    }

    @Override
    public void delRefreshTokenByCode(String tokenCode) {
        // TODO Auto-generated method stub
        tokenSvc.delRefreshTokenByCode(tokenCode);
    }

    @Override
    public OauthTokenDetail getTokenDetailByReTokenCode(String reTokenCode) {
        // TODO Auto-generated method stub
        return tokenSvc.getTokenDetailByReTokenCode(reTokenCode);
    }

	@Override
	public boolean save(OauthResourceDetail saveDto) {
		// TODO Auto-generated method stub
		return resourceSvc.save(saveDto);
	}

	@Override
	public List<OauthResourceDetail> list() {
		// TODO Auto-generated method stub
		return resourceSvc.list();
	}
}
