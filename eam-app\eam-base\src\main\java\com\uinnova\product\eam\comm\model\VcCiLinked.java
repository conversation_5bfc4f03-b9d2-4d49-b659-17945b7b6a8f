package com.uinnova.product.eam.comm.model;




import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;


@Comment("CI链路表[VC_CI_LINKED]")
public class VcCiLinked implements EntityBean {
	private static final long serialVersionUID = 1L;


	@Comment("ID[ID]")
	private Long id;


	@Comment("链路名称[LINKED_NAME]")
	private String linkedName;


	@Comment("来源类型[SOURCE_TYPE]    1=视图")
	private Integer sourceType;


	@Comment("是否显示[IS_DISPLAY]    是否显示当前链路到视图(1=显示,0不显示)")
	private Integer isDisplay;


	@Comment("来源ID[SOURCE_ID]")
	private Long sourceId;


	@Comment("CI代码列表[CI_CODES]    逗号分隔")
	private String ciCodes;


	@Comment("链路颜色[LINKED_COLOR]")
	private String linkedColor;


	@Comment("备用_1[CUSTOM_1]")
	private String custom1;


	@Comment("备用_2[CUSTOM_2]")
	private String custom2;


	@Comment("备用_3[CUSTOM_3]")
	private String custom3;


	@Comment("备用_4[CUSTOM_4]")
	private String custom4;


	@Comment("备用_5[CUSTOM_5]")
	private String custom5;


	@Comment("备用_6[CUSTOM_6]")
	private String custom6;


	@Comment("所属域[DOMAIN_ID]")
	private Long domainId;


	@Comment("数据状态[DATA_STATUS]    数据状态:数据状态：1-正常 0-删除")
	private Integer dataStatus;


	@Comment("创建人[CREATOR]")
	private String creator;


	@Comment("修改人[MODIFIER]")
	private String modifier;


	@Comment("创建时间[CREATE_TIME]    创建时间:yyyyMMddHHmmss")
	private Long createTime;


	@Comment("修改时间[MODIFY_TIME]    修改时间:yyyyMMddHHmmss")
	private Long modifyTime;




	public Long getId() {
		return this.id;
	}
	public void setId(Long id) {
		this.id = id;
	}


	public String getLinkedName() {
		return this.linkedName;
	}
	public void setLinkedName(String linkedName) {
		this.linkedName = linkedName;
	}


	public Integer getSourceType() {
		return this.sourceType;
	}
	public void setSourceType(Integer sourceType) {
		this.sourceType = sourceType;
	}


	public Integer getIsDisplay() {
		return this.isDisplay;
	}
	public void setIsDisplay(Integer isDisplay) {
		this.isDisplay = isDisplay;
	}


	public Long getSourceId() {
		return this.sourceId;
	}
	public void setSourceId(Long sourceId) {
		this.sourceId = sourceId;
	}


	public String getCiCodes() {
		return this.ciCodes;
	}
	public void setCiCodes(String ciCodes) {
		this.ciCodes = ciCodes;
	}


	public String getLinkedColor() {
		return this.linkedColor;
	}
	public void setLinkedColor(String linkedColor) {
		this.linkedColor = linkedColor;
	}


	public String getCustom1() {
		return this.custom1;
	}
	public void setCustom1(String custom1) {
		this.custom1 = custom1;
	}


	public String getCustom2() {
		return this.custom2;
	}
	public void setCustom2(String custom2) {
		this.custom2 = custom2;
	}


	public String getCustom3() {
		return this.custom3;
	}
	public void setCustom3(String custom3) {
		this.custom3 = custom3;
	}


	public String getCustom4() {
		return this.custom4;
	}
	public void setCustom4(String custom4) {
		this.custom4 = custom4;
	}


	public String getCustom5() {
		return this.custom5;
	}
	public void setCustom5(String custom5) {
		this.custom5 = custom5;
	}


	public String getCustom6() {
		return this.custom6;
	}
	public void setCustom6(String custom6) {
		this.custom6 = custom6;
	}


	public Long getDomainId() {
		return this.domainId;
	}
	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}


	public Integer getDataStatus() {
		return this.dataStatus;
	}
	public void setDataStatus(Integer dataStatus) {
		this.dataStatus = dataStatus;
	}


	public String getCreator() {
		return this.creator;
	}
	public void setCreator(String creator) {
		this.creator = creator;
	}


	public String getModifier() {
		return this.modifier;
	}
	public void setModifier(String modifier) {
		this.modifier = modifier;
	}


	public Long getCreateTime() {
		return this.createTime;
	}
	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}


	public Long getModifyTime() {
		return this.modifyTime;
	}
	public void setModifyTime(Long modifyTime) {
		this.modifyTime = modifyTime;
	}


}


