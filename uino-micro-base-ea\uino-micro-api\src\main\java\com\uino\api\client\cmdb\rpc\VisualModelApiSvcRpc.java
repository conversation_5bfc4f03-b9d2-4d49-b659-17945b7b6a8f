package com.uino.api.client.cmdb.rpc;

import java.util.Collections;
import java.util.List;

import com.uino.bean.cmdb.base.ESVisualModelVo;
import com.uino.bean.cmdb.base.LibType;
import org.elasticsearch.index.query.QueryBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.uino.bean.cmdb.base.ESVisualModel;
import com.uino.provider.feign.cmdb.VisualModelFeign;
import com.uino.api.client.cmdb.IVisualModelApiSvc;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
public class VisualModelApiSvcRpc implements IVisualModelApiSvc {

    @Autowired
    private VisualModelFeign feign;

    @Override
    public List<ESVisualModel> queryVisualModels(Long domainId) {
        return feign.queryVisualModels(domainId);
    }

    @Override
    public Long saveVisualModel(ESVisualModel model) {
        return feign.saveVisualModel(model);
    }

    @Override
    public Long saveVisualModelPrivate(ESVisualModel model) {
        return 0L;
    }

    @Override
    public Boolean flushAllEnableVisualModelCiRlt() {
        return null;
    }

    @Override
    public List<ESVisualModel> getVisualModelByQuery(QueryBuilder query, LibType libType) {
        return Collections.emptyList();
    }

    @Override
    public List<ESVisualModelVo> queryVisualModels(Long domainId, LibType libType, String loginCode) {
        return Collections.emptyList();
    }

    @Override
    public List<ESVisualModelVo> queryVisualModelsNoChickExit(Long domainId, LibType libType, String loginCode) {
        return Collections.emptyList();
    }

    @Override
    public void updateVisualModelName(ESVisualModel model) {
    	feign.updateVisualModelName(model);
    }

    @Override
    public void deleteVisualModel(Long id) {
    	feign.deleteVisualModel(id);
    }

    @Override
    public void delVMThumbnailBySheetId(Long id, Long sheetId) {

    }

    @Override
    public ESVisualModel queryVisualModelById(Long id, Long domainId) {
        return null;
    }

    @Override
    public ESVisualModel queryPrivateVisualModelById(Long domainId, Long visId) {
        return null;
    }

    @Override
    public void delValidVisData() {
        return;
    }

}
