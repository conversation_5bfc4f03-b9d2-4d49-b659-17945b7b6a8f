package com.uino.service.permission.microservice.impl;

import java.io.*;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSON;
import com.binary.core.exception.MessageException;
import com.uino.bean.permission.base.SysRoleModuleRlt;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.permission.query.CSysRoleModuleRlt;
import com.uino.dao.BaseConst;
import com.uino.dao.permission.rlt.ESRoleModuleRltSvc;
import com.uino.dao.permission.rlt.ESUserModuleEnshrineRltSvc;
import com.uino.dao.permission.rlt.ESUserModuleRltSvc;
import com.uino.util.sys.CommonFileUtil;
import com.uino.util.sys.SysUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import com.binary.core.util.BinaryUtils;
import com.uino.dao.permission.ESModuleSvc;
import com.uino.service.permission.microservice.IModuleSvc;
import com.uino.service.permission.microservice.IUserSvc;
import com.uino.dao.util.ESUtil;
import com.uino.util.sys.BeanUtil;
import com.uino.bean.permission.base.SysModule;
import com.uino.bean.permission.business.ModuleNodeInfo;
import com.uino.bean.permission.query.CAuthModuleBean;
import com.uino.bean.permission.query.CSysModule;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ModuleSvc implements IModuleSvc {

    private static final String _DEFAULT_BUTTON = "页面查看";
    @Autowired
    private ESModuleSvc esModuleSvc;
    @Autowired
    private IUserSvc userSvc;
    @Autowired
    ESRoleModuleRltSvc roleModuleRltSvc;
    @Autowired
    ESUserModuleRltSvc userModuleRltSvc;
    @Autowired
    private ESUserModuleEnshrineRltSvc userModuleEnshrineRltSvc;

    @Value("${http.resource.space}")
    private String rsmSlaveRoot;
    @Override
    public ModuleNodeInfo getModuleTree(Long domainId, Long userId) {
        return userSvc.getModuleTree(domainId, userId);
    }

    @Override
    public SysModule saveModule(SysModule saveDto) {
        Assert.notNull(saveDto, "param notnull");
        Assert.notNull(saveDto.getModuleName(), "模块名称不可为空");
        Assert.isTrue(!"".equals(saveDto.getModuleName().trim()), "模块名称不可为空");
        if (BinaryUtils.isEmpty(saveDto.getDomainId())) {
            saveDto.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
        }
        BoolQueryBuilder validNameQuery = QueryBuilders.boolQuery();
        BoolQueryBuilder validLabelQuery = QueryBuilders.boolQuery();
        Long parentId = saveDto.getParentId();
        parentId = parentId == null ? 0 : parentId;
        saveDto.setParentId(parentId);
        String label = saveDto.getLabel();
        label = label != null && !"".equals(label) ? label : saveDto.getModuleName();
        saveDto.setLabel(label);
        SysModule parent = null;
        if (parentId != 0) {
            parent = esModuleSvc.getById(parentId);
            Assert.notNull(parent, "指定父级不存在");
        }
        if (saveDto.getId() == null) {
            BoolQueryBuilder query = QueryBuilders.boolQuery();
            query.must(QueryBuilders.termQuery("domainId", saveDto.getDomainId()));
            query.must(QueryBuilders.termQuery("parentId", parentId));
            List<SysModule> modules = esModuleSvc.getSortListByQuery(1, 1, query, "orderNo", false).getData();
            Integer sameLevelMaxOrder = null;
            try {
                sameLevelMaxOrder = modules.get(0).getOrderNo();
            } catch (NullPointerException e) {
            } catch (IndexOutOfBoundsException e) {
            }
            if ((saveDto.getModuleType() == 1) && sameLevelMaxOrder == null) {
                //如果是按钮并且该页面下没有按钮则添加查看按钮
                sameLevelMaxOrder = 0;
                SysModule seeButton = new SysModule();
                seeButton.setModuleName(_DEFAULT_BUTTON);
                if (parent.getModuleSign() != null) {
                    seeButton.setModuleSign(parent.getModuleSign() + "_see");
                }
                seeButton.setModuleType(saveDto.getModuleType());
                seeButton.setParentId(parentId);
                seeButton.setIsInit(true);
                seeButton.setCustomColor("customColor");
                seeButton.setModuleImg("ts ts-envamables");
                seeButton.setOrderNo(0);
                seeButton.setIsDisable(true);
                if (BinaryUtils.isEmpty(saveDto.getModulePic())) {
                    seeButton.setModulePic(saveDto.getModulePic());
                }
                Long seeButtonId = esModuleSvc.saveOrUpdate(seeButton);
                //如果菜单勾选，创建按钮时默认赋予查看权限
                CSysRoleModuleRlt rltCdt = new CSysRoleModuleRlt();
                rltCdt.setDomainId(saveDto.getDomainId());
                rltCdt.setModuleId(parentId);
                List<SysRoleModuleRlt> listByCdt = roleModuleRltSvc.getListByCdt(rltCdt);
                if (!listByCdt.isEmpty()) {
                    List<SysRoleModuleRlt> seeRoleModuleRltList = new ArrayList<>();
                    for (SysRoleModuleRlt roleModuleRlt : listByCdt) {
                        SysRoleModuleRlt moduleRlt = SysRoleModuleRlt.builder().moduleId(seeButtonId)
                                .domainId(saveDto.getDomainId())
                                .roleId(roleModuleRlt.getRoleId()).build();
                        seeRoleModuleRltList.add(moduleRlt);
                    }
                    roleModuleRltSvc.saveOrUpdateBatch(seeRoleModuleRltList);
                }
            }
            Integer orderNo = sameLevelMaxOrder == null ? 0 : ++sameLevelMaxOrder;
            saveDto.setParentId(parentId);
            saveDto.setOrderNo(orderNo);
            saveDto.setModuleCode(String.valueOf(ESUtil.getUUID()));
            String moduleSign = saveDto.getModuleSign();
            if (null == moduleSign || "".equals(moduleSign.trim())) {
                moduleSign = saveDto.getModuleName();
            }
            saveDto.setModuleSign(moduleSign);
            saveDto.setIsInit(false);
        } else {
            SysModule module = esModuleSvc.getById(saveDto.getId());
            Assert.notNull(module, "待修改模块不存在");
            saveDto.setOrderNo(module.getOrderNo());
            //修改的数据，判断是否更新图片
            if (!BinaryUtils.isEmpty(saveDto.getModulePic()) && saveDto.getModulePic().startsWith(rsmSlaveRoot)) {
                saveDto.setModulePic(saveDto.getModulePic().replaceAll(rsmSlaveRoot, ""));
            }
            saveDto.setModuleCode(module.getModuleCode());
            saveDto.setIsInit(module.getIsInit());
            saveDto.setParentId(parentId);
            saveDto.setDomainId(module.getDomainId());
            validNameQuery.mustNot(QueryBuilders.termQuery("id", saveDto.getId()));
            validLabelQuery.mustNot(QueryBuilders.termQuery("id", saveDto.getId()));
        }
        if(saveDto.getModuleType() == 3){
            int code = ("QUICK_EA" + saveDto.getModuleName()).hashCode() & Integer.MAX_VALUE;
            String moduleUrl = saveDto.getModuleUrl();
            String substring = moduleUrl.substring(0, moduleUrl.indexOf("assets/") + 7);
            saveDto.setModuleUrl(substring+"{"+code+"}"+"?id="+code);
        }
        // Verify the module name when it is a menu
        if (saveDto.getModuleType() == 0 || saveDto.getModuleType() == 2) {
            validNameQuery.must(QueryBuilders.termQuery("moduleName.keyword", saveDto.getModuleName()));
            validNameQuery.must(QueryBuilders.termQuery("parentId", parentId));
            validNameQuery.must(QueryBuilders.termQuery("domainId", saveDto.getDomainId()));
            long validNameRes = esModuleSvc.countByCondition(validNameQuery);
            Assert.isTrue(validNameRes <= 0, "模块名称重复");

            validLabelQuery.must(QueryBuilders.termQuery("label.keyword", saveDto.getLabel()));
            validLabelQuery.must(QueryBuilders.termQuery("parentId", parentId));
            validLabelQuery.must(QueryBuilders.termQuery("domainId", saveDto.getDomainId()));
            long validLabelRes = esModuleSvc.countByCondition(validLabelQuery);
            Assert.isTrue(validLabelRes <= 0, "模块显示名称重复");
        }
        // 验证通过
        Long dataId = esModuleSvc.saveOrUpdate(saveDto);
        SysModule module = esModuleSvc.getById(dataId);
        module.fullData();
        return module;
    }

    @Override
    public void delModule(Long id) {
        SysModule delModule = esModuleSvc.getById(id);
        boolean isInit = delModule.getIsInit();
        Assert.isTrue(!isInit, "The initialization menu cannot be deleted");
        //如果是按钮，判断是不是该页面的最后一个按钮，如果是则删除查看按钮
        Long seeButtonId = null;
        if (delModule.getModuleType() == 1 || delModule.getModuleType() == 3) {
            List<SysModule> list = esModuleSvc.getListByQuery(QueryBuilders.termQuery("parentId", delModule.getParentId()));
            if (list.size() == 2) {
                for (SysModule sysModule : list) {
                    if (_DEFAULT_BUTTON.equals(sysModule.getModuleName())) {
                        seeButtonId = sysModule.getId();
                    }
                }
            }

        }

        List<SysModule> allModules = esModuleSvc.getAll(delModule.getDomainId());
        Map<Long, List<SysModule>> allModulesGroupByParentId = allModules.stream().collect(Collectors.groupingBy(SysModule::getParentId));
        List<Long> moduleIdsToBeDeleted = Collections.synchronizedList(new ArrayList<>(16));
        this.getModuleIdsToBeDeleted(allModulesGroupByParentId, delModule, moduleIdsToBeDeleted);
        if (moduleIdsToBeDeleted.size() > 0) {
            if (seeButtonId != null) {
                moduleIdsToBeDeleted.add(seeButtonId);
            }
            esModuleSvc.deleteByIds(moduleIdsToBeDeleted);
            //级联删除模块相关的内容
            QueryBuilder query = QueryBuilders.termsQuery("moduleId", moduleIdsToBeDeleted);
            esModuleSvc.deleteByIds(moduleIdsToBeDeleted);
            roleModuleRltSvc.deleteByQuery(query, true);
            userModuleRltSvc.deleteByQuery(query, true);
            userModuleEnshrineRltSvc.deleteByQuery(query, true);
        }
    }

    @Override
    public void saveOrder(Map<Long, Integer> orderDict) {
        if (orderDict == null || orderDict.size() <= 0) {
            return;
        }
        Set<Long> moduleIds = orderDict.keySet();
        List<SysModule> modules = esModuleSvc.getListByQuery(QueryBuilders.termsQuery("id", moduleIds));
        Assert.isTrue(moduleIds.size() == modules.size(), "传入菜单有非法数据(不存在菜单)");
        modules.forEach(module -> module.setOrderNo(orderDict.get(module.getId())));
        esModuleSvc.saveOrUpdateBatch(modules);
    }

    @Override
    public Map<Long, SysModule> recoverModules(Set<Long> moduleIds) {

        BoolQueryBuilder moduleQuery = QueryBuilders.boolQuery();
        if (moduleIds != null && moduleIds.size() > 0) {
            moduleQuery.must(QueryBuilders.termsQuery("id", moduleIds));
        }
        moduleQuery.must(QueryBuilders.termQuery("isInit", true));
        if (moduleIds != null && moduleIds.size() > 0) {
            long validCount = esModuleSvc.countByCondition(moduleQuery);
            Assert.isTrue(new BigDecimal(moduleIds.size()).longValue() == validCount, "传入模块有非法模块");
        }
        List<SysModule> nowModules = esModuleSvc.getListByQuery(moduleQuery);

        List<SysModule> initDatas = esModuleSvc.getInitDatas();
        Map<String, SysModule> initDataMap = new HashMap<>();
        initDatas.forEach(initData -> initDataMap.put(initData.getModuleCode(), initData));

        List<SysModule> saveDatas = new LinkedList<>();
        Map<Long, SysModule> res = new HashMap<>();
        for (SysModule nowData : nowModules) {
            Long recoverId = nowData.getId();
            SysModule saveData = BeanUtil.converBean(initDataMap.get(nowData.getModuleCode()), SysModule.class);
            if (saveData == null) {
                continue;
            }
            saveData.setParentId(nowData.getParentId());
            saveData.setId(recoverId);
            saveData.setParentId(nowData.getParentId());
            saveData.setOrderNo(nowData.getOrderNo());
            saveData.setDomainId(nowData.getDomainId());
            saveDatas.add(saveData);
            saveData.fullData();
            res.put(recoverId, saveData);
        }
        esModuleSvc.saveOrUpdateBatch(saveDatas);

        return res;
    }

    @Override
    public List<SysModule> getModulesByCdt(CSysModule cdt) {
        if (cdt == null) {
            cdt = new CSysModule();
        }
        if (BinaryUtils.isEmpty(cdt.getDomainId())) {
            cdt.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
        }
        return esModuleSvc.getListByCdt(cdt);
    }

    @Override
    public List<SysModule> getAuthModulesBySearchBean(CAuthModuleBean bean) {
        Assert.notNull(bean, "X_PARAM_NOT_NULL${name:searchBean}");
        List<SysModule> result = new ArrayList<SysModule>();
        Long userId = bean.getUserId();
        List<Long> roleIds = bean.getRoleIds();
        if (userId != null) {
            //超级管理员
            if (userId == 0L) {
                return CommonFileUtil.getData("/initdata/uino_sys_superadmin_module.json", SysModule.class);
            }
            // 获取用户有权限的模块
            List<SysModule> userModules = esModuleSvc.getModuleAuths(userId);
            result.addAll(userModules);
        }
        if (!BinaryUtils.isEmpty(roleIds)) {
            List<SysModule> roleModules = esModuleSvc.getListByRoleIds(roleIds);
            result.addAll(roleModules);
        }
        return result.stream().distinct().collect(Collectors.toList());
    }

    @Override
    public ResponseEntity<byte[]> exportModules() {
        //查询所有的菜单树，并把每个菜单的isInit 改成false
        SysUser sys = SysUtil.getCurrentUserInfo();
        List<SysModule> sysModules = esModuleSvc.getAll(sys.getDomainId());
        List<SysModule> lists = new ArrayList<>(sysModules.size());
        for (SysModule module : sysModules) {
            if (!BinaryUtils.isEmpty(module.getIsInit()) && module.getIsInit().equals(true)) {
                module.setIsInit(false);
            }
            lists.add(module);
        }
        String s = JSON.toJSONString(lists);
        String exportName = "系统模块数据";
        String time = DateTimeFormatter.ofPattern("yyyyMMddhhmmss").format(LocalDateTime.now());
        File file = new File(exportName + time + ".json");
        try (FileOutputStream fileOutputStream = new FileOutputStream(file)) {
            IOUtils.write(s, fileOutputStream, String.valueOf(StandardCharsets.UTF_8));
        } catch (IOException e) {
            log.error("数据文件生成失败", e);
        }
        ResponseEntity<byte[]> responseEntity = returnRes(file);
        FileUtils.deleteQuietly(file);
        return responseEntity;
    }

    @Override
    public void importModules(MultipartFile file) {
        log.info("导入菜单模块数据");
        //导入的 图片要处理下路径
        String s = null;
        try {
            s = IOUtils.toString(file.getInputStream(), String.valueOf(StandardCharsets.UTF_8));
            List<SysModule> list = JSON.parseArray(s, SysModule.class);
            esModuleSvc.saveOrUpdateBatch(list);
            log.info("批量保存数据成功");
        } catch (IOException e) {
            log.error("导入数据失败", e);
            e.printStackTrace();
        }
    }

    @Override
    public List<SysModule> getSysModuleIdList(List<Integer> businessGroupList, List<Integer> businessModuleList) {
        return esModuleSvc.getSysModuleIdList(businessGroupList, businessModuleList);
    }

    private ResponseEntity<byte[]> returnRes(File file) {
        HttpHeaders headers = new HttpHeaders();
        ResponseEntity<byte[]> entity = null;
        InputStream in = null;
        try {
            in = new FileInputStream(file);
            byte[] bytes = new byte[in.available()];
            in.read(bytes);
            headers.add("Content-Disposition", "attachment;filename=" + URLEncoder.encode(file.getName(), "UTF-8"));
            entity = new ResponseEntity<byte[]>(bytes, headers, HttpStatus.OK);
        } catch (Exception e) {
            throw new MessageException(e.getMessage());
        } finally {
            if (in != null) {
                try {
                    in.close();
                } catch (Exception e) {
                    throw new MessageException(e.getMessage());
                }
            }
        }
        return entity;
    }

    /**
     * Gets the ID of the module to be deleted
     *
     * @param moduleMap       module map group by parent id
     * @param sysModule       current module
     * @param deleteModuleIds module ID to be deleted
     */
    private void getModuleIdsToBeDeleted(Map<Long, List<SysModule>> moduleMap, SysModule sysModule, List<Long> deleteModuleIds) {
        if (sysModule != null) {
            Long moduleId = sysModule.getId();
            if (!deleteModuleIds.contains(moduleId)) {
                deleteModuleIds.add(moduleId);
            }

            List<SysModule> childModuleList = moduleMap.get(moduleId);
            if (null != childModuleList && childModuleList.size() > 0) {
                childModuleList.forEach(childSysModule -> {
                    getModuleIdsToBeDeleted(moduleMap, childSysModule, deleteModuleIds);
                });
            }
        }
    }

    @Override
    public ModuleNodeInfo getModuleTreeBySign(Long domainId, Long userId, String moduleSign) {
        return userSvc.getModuleTreeBySign(domainId, userId,moduleSign);
    }
}
