package com.uino.cmdb.ci;

import static org.junit.Assert.assertEquals;

import java.io.File;
import java.io.FileInputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.binary.json.JSON;
import com.uinnova.product.vmdb.comm.bean.QueryPageCondition;
import com.uinnova.product.vmdb.comm.model.ci.CcCi;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.comm.model.ci.CcCiClass;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CiGroupPage;
import com.uinnova.product.vmdb.provider.ci.bean.CiQueryCdt;
import com.uino.StartBaseWebAppliaction;
import com.uino.bean.cmdb.business.CiExcelInfoDto;
import com.uino.bean.cmdb.business.ExportCiDto;
import com.uino.bean.cmdb.business.ImportExcelMessage;
import com.uino.bean.cmdb.business.ImportSheetMessage;

@RunWith(SpringRunner.class)
@WebAppConfiguration
@SpringBootTest(classes = {StartBaseWebAppliaction.class})
@ActiveProfiles("provider-local")
@AutoConfigureMockMvc(addFilters = false)
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class CiMvcTest {

    @Autowired
    private MockMvc mockMvc;

    private static String token = "ca14ed042fe7912426b484f6ea5c754b4fb51ae4a72037e9127da51743e0d1f9650e6e78ed4466ac970acb6a0b2f46fd26958c4ab2d115cc71b34ca8a02db24c";

    private static List<Long> ciIds = new ArrayList<>();

    private static Long classId = 0L;

    private static Map<String, String> importBatchMap = new HashMap<>();

    @Test
    public void atestSaveOrUpdateCiClass() throws Exception {
        CcCiClassInfo param = new CcCiClassInfo();
        CcCiClass ciClass = new CcCiClass();
        ciClass.setClassCode("test-test");
        ciClass.setClassName("test-test");
        ciClass.setDirId(1L);
        ciClass.setParentId(0L);
        ciClass.setCiType(1);
        List<CcCiAttrDef> attrDefs = new ArrayList<>();
        CcCiAttrDef def = new CcCiAttrDef();
        def.setProName("业务主键");
        def.setCiType(1);
        def.setIsMajor(1);
        def.setIsRequired(1);
        def.setProType(3);
        attrDefs.add(def);
        CcCiAttrDef def1 = new CcCiAttrDef();
        def1.setProName("编号");
        def1.setCiType(1);
        def1.setIsMajor(0);
        def1.setIsRequired(0);
        def1.setProType(3);
        attrDefs.add(def1);
        CcCiAttrDef def2 = new CcCiAttrDef();
        def2.setProName("名称");
        def2.setCiType(1);
        def2.setIsMajor(0);
        def2.setIsRequired(0);
        def2.setProType(3);
        attrDefs.add(def2);
        CcCiAttrDef def3 = new CcCiAttrDef();
        def3.setProName("所属");
        def3.setCiType(1);
        def3.setIsMajor(0);
        def3.setIsRequired(0);
        def3.setProType(3);
        attrDefs.add(def3);
        param.setCiClass(ciClass);
        param.setAttrDefs(attrDefs);
        String requestJson = JSONObject.toJSONString(param);
        ResultActions reaction =
            this.mockMvc.perform(MockMvcRequestBuilders.post("/cmdb/ciClass/saveOrUpdate").contentType(MediaType.APPLICATION_JSON).content(requestJson).accept(MediaType.APPLICATION_JSON)// 返回值接收json
                .header("Content-Type", "application/json").header("token", token));
        MvcResult mvcResult = reaction.andReturn();
        String resStr = mvcResult.getResponse().getContentAsString();
        String dirIdStr = JSON.toString(JSON.toObject(resStr, Map.class).get("data"));
        classId = JSON.toObject(dirIdStr, Long.class);
        int status = mvcResult.getResponse().getStatus();
        assertEquals(200, status);
    }

    @Test
    public void btestSaveOrUpdate() throws Exception {
        CcCiInfo param = new CcCiInfo();
        CcCi ci = new CcCi();
        ci.setClassId(classId);
        Map<String, String> attrs = new HashMap<String, String>();
        attrs.put("业务主键", "业务主键属性值");
        attrs.put("编号", "优锘-001");
        attrs.put("名称", "优锘");
        attrs.put("所属", "优锘");
        param.setCi(ci);
        param.setAttrs(attrs);
        String requestJson = JSONObject.toJSONString(param);
        ResultActions reaction =
            this.mockMvc.perform(MockMvcRequestBuilders.post("/cmdb/ci/saveOrUpdate").contentType(MediaType.APPLICATION_JSON).content(requestJson).accept(MediaType.APPLICATION_JSON)// 返回值接收json
                .header("Content-Type", "application/json").header("token", token));
        MvcResult mvcResult = reaction.andReturn();
        String resStr = mvcResult.getResponse().getContentAsString();
        String ciIdStr = JSON.toString(JSON.toObject(resStr, Map.class).get("data"));
        Long ciId = JSON.toObject(ciIdStr, Long.class);
        ciIds.add(ciId);
        int status = mvcResult.getResponse().getStatus();
        assertEquals(200, status);
    }

    @Test
    public void ctestQueryById() throws Exception {
        String requestJson = JSONObject.toJSONString(ciIds.get(0));
        ResultActions reaction = this.mockMvc.perform(MockMvcRequestBuilders.post("/cmdb/ci/queryById").contentType(MediaType.APPLICATION_JSON).content(requestJson).accept(MediaType.APPLICATION_JSON)// 返回值接收json
            .header("Content-Type", "application/json").header("token", token));
        MvcResult mvcResult = reaction.andReturn();
        int status = mvcResult.getResponse().getStatus();
        String resStr = mvcResult.getResponse().getContentAsString();
        String ciInfoStr = JSON.toString(JSON.toObject(resStr, Map.class).get("data"));
        CcCiInfo ciInfo = JSON.toObject(ciInfoStr, CcCiInfo.class);
        assertEquals("业务主键属性值", ciInfo.getAttrs().get("业务主键"));
        assertEquals(200, status);
    }

    @Test
    public void dtestAsyncImportCi() throws Exception {
        File file = new File("./src/test/resources/testdata/ci.xlsx");
        System.out.println(file.getAbsolutePath());
        MockMultipartFile firstFile = new MockMultipartFile("file", "ci.xlsx", MediaType.TEXT_PLAIN_VALUE, new FileInputStream(file));
        ResultActions reaction = mockMvc.perform(MockMvcRequestBuilders.multipart("/cmdb/ci/importCi").file(firstFile)).andExpect(MockMvcResultMatchers.status().isOk());
        String resStr = reaction.andReturn().getResponse().getContentAsString();
        String resultStr = JSONArray.toJSONString(JSON.toObject(resStr, Map.class).get("data"));
        ImportSheetMessage result = JSON.toObject(resultStr, ImportSheetMessage.class);
        int status = reaction.andReturn().getResponse().getStatus();
        assertEquals(200, status);
    }

    @Test
    public void etestExportAllCi() throws Exception {
        ExportCiDto param = new ExportCiDto();
        param.setHasData(0);
        param.setHasClsDef(1);
        String requestJson = JSONObject.toJSONString(param);
        ResultActions reaction =
            this.mockMvc.perform(MockMvcRequestBuilders.post("/cmdb/ci/exportCi").contentType(MediaType.APPLICATION_JSON).content(requestJson).accept(MediaType.MULTIPART_FORM_DATA)// 返回值接收文件
            .header("Content-Type", "application/json").header("token", token));
        MvcResult mvcResult = reaction.andReturn();
        int status = mvcResult.getResponse().getStatus();
        assertEquals(200, status);
    }

    @Test
    public void ftestQueryPageByIndex() throws Exception {
        QueryPageCondition<CiQueryCdt> param = new QueryPageCondition<>();
        CiQueryCdt cdt = new CiQueryCdt();
        cdt.setClassId(classId);
        cdt.setLike("业务");
        param.setPageNum(1);
        param.setPageSize(30);
        param.setCdt(cdt);
        String requestJson = JSONObject.toJSONString(param);
        ResultActions reaction =
            this.mockMvc.perform(MockMvcRequestBuilders.post("/cmdb/ci/queryPageByIndex").contentType(MediaType.APPLICATION_JSON).content(requestJson).accept(MediaType.APPLICATION_JSON)// 返回值接收json
                .header("Content-Type", "application/json").header("token", token));
        MvcResult mvcResult = reaction.andReturn();
        int status = mvcResult.getResponse().getStatus();
        String resStr = mvcResult.getResponse().getContentAsString();
        String ciInfoStr = JSON.toString(JSON.toObject(resStr, Map.class).get("data"));
        CiGroupPage groupPage = JSON.toObject(ciInfoStr, CiGroupPage.class);
        groupPage.getData().forEach(ciInfo -> {
            Long ciId = ciInfo.getCi().getId();
            ciIds.add(ciId);
        });
        assertEquals(classId, groupPage.getData().get(0).getCi().getClassId());
        assertEquals(200, status);
    }

    @Test
    public void gtestImportCiExcelBatch() throws Exception {
        File file = new File("./src/test/resources/testdata/ci.xlsx");
        System.out.println(file.getAbsolutePath());
        MockMultipartFile firstFile = new MockMultipartFile("file", "ci.xlsx", MediaType.TEXT_PLAIN_VALUE, new FileInputStream(file));
        List<MockMultipartFile> files = new ArrayList<>();
        files.add(firstFile);
        ResultActions reaction = mockMvc.perform(MockMvcRequestBuilders.multipart("/cmdb/ci/importCiExcelBatch").file(firstFile)).andExpect(MockMvcResultMatchers.status().isOk());
        String resStr = reaction.andReturn().getResponse().getContentAsString();
        String resultStr = JSONArray.toJSONString(JSON.toObject(resStr, Map.class).get("data"));
        ImportExcelMessage excelMessage = JSON.toObject(resultStr, ImportExcelMessage.class);
        importBatchMap.put("dirName", excelMessage.getDirName());
        importBatchMap.put("fileName", excelMessage.getFileName());
        importBatchMap.put("sheetName", excelMessage.getSheetNames().get(0));
        int status = reaction.andReturn().getResponse().getStatus();
        assertEquals(200, status);
    }

    @Test
    public void htestImportCiBySheetName() throws Exception {
        CiExcelInfoDto param = new CiExcelInfoDto();
        param.setFileName(importBatchMap.get("fileName"));
        List<String> sheetNames = new ArrayList<>();
        sheetNames.add(importBatchMap.get("sheetName"));
         param.setSheetNames(sheetNames);
        String requestJson = JSONObject.toJSONString(param);
        ResultActions reaction =
            mockMvc.perform(MockMvcRequestBuilders.multipart("/cmdb/ci/importCiByClassBatch").contentType(MediaType.APPLICATION_JSON).content(requestJson).accept(MediaType.APPLICATION_JSON)// 返回值接收json
                .header("Content-Type", "application/json").header("token", token));
        String resStr = reaction.andReturn().getResponse().getContentAsString();
        String resultStr = JSONArray.toJSONString(JSON.toObject(resStr, Map.class).get("data"));
        ImportSheetMessage result = JSON.toObject(resultStr, ImportSheetMessage.class);
        int status = reaction.andReturn().getResponse().getStatus();
        assertEquals(200, status);
    }

    @Test
    public void z1testRemoveById() throws Exception {
        String requestJson = JSONObject.toJSONString(ciIds.get(0));
        ResultActions reaction =
            this.mockMvc.perform(MockMvcRequestBuilders.post("/cmdb/ci/removeById").contentType(MediaType.APPLICATION_JSON).content(requestJson).accept(MediaType.APPLICATION_JSON)// 返回值接收json
                .header("Content-Type", "application/json").header("token", token));
        MvcResult mvcResult = reaction.andReturn();
        int status = mvcResult.getResponse().getStatus();
        assertEquals(200, status);
    }

    @Test
    public void z2testRemoveByIds() throws Exception {
        String requestJson = JSONObject.toJSONString(ciIds);
        ResultActions reaction =
            this.mockMvc.perform(MockMvcRequestBuilders.post("/cmdb/ci/removeByIds").contentType(MediaType.APPLICATION_JSON).content(requestJson).accept(MediaType.APPLICATION_JSON)// 返回值接收json
                .header("Content-Type", "application/json").header("token", token));
        MvcResult mvcResult = reaction.andReturn();
        int status = mvcResult.getResponse().getStatus();
        assertEquals(200, status);
    }

    @Test
    public void z3testRemoveByClassId() throws Exception {
        String requestJson = JSONObject.toJSONString(classId);
        ResultActions reaction =
            this.mockMvc.perform(MockMvcRequestBuilders.post("/cmdb/ci/removeByClassId").contentType(MediaType.APPLICATION_JSON).content(requestJson).accept(MediaType.APPLICATION_JSON)// 返回值接收json
                .header("Content-Type", "application/json").header("token", token));
        MvcResult mvcResult = reaction.andReturn();
        int status = mvcResult.getResponse().getStatus();
        assertEquals(200, status);
    }

    @Test
    public void z4testRemoveClassById() throws Exception {
        String requestJson = JSONObject.toJSONString(classId);
        ResultActions reaction =
            this.mockMvc.perform(MockMvcRequestBuilders.post("/cmdb/ciClass/removeById").contentType(MediaType.APPLICATION_JSON).content(requestJson).accept(MediaType.APPLICATION_JSON)// 返回值接收json
                .header("Content-Type", "application/json").header("token", token));
        MvcResult mvcResult = reaction.andReturn();
        int status = mvcResult.getResponse().getStatus();
        assertEquals(200, status);
    }
}
