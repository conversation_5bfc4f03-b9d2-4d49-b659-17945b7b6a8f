package com.uinnova.product.eam.web.dmv;

import com.binary.core.util.BinaryUtils;
import com.binary.framework.util.ControllerUtils;
import com.binary.framework.web.RemoteResult;
import com.binary.json.JSON;
import com.uinnova.product.eam.comm.exception.BusinessException;
import com.uinnova.product.eam.comm.model.VcBaseConfig;
import com.uinnova.product.eam.model.VcCiRltInfo;
import com.uinnova.product.eam.model.dmv.*;
import com.uinnova.product.eam.service.dmv.DmvAppWallService;
import com.uinnova.product.eam.web.util.SqlInjectUtil;
import com.uino.bean.permission.base.SysUser;
import com.uino.util.sys.SysUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * dmv全景应用墙
 */
@Controller
@RequestMapping("/appWall")
@Api("提供CI管理,查询接口")
public class DmvAppWallController {

    @Resource
    private DmvAppWallService dmvAppWallService;

    @PostMapping("/findAppWallList")
    @ResponseBody
    public void findAppWallList(HttpServletRequest request, HttpServletResponse response) {
        SysUser sysUser = SysUtil.getCurrentUserInfo();
        if (sysUser == null) {
            throw new BusinessException("获取当前用户错误!");
        }
        List<CiInfoResponse> appWallList = dmvAppWallService.findAppWallList(sysUser);
        ControllerUtils.returnJson(request, response, appWallList);
    }

    @PostMapping("/saveAppSortConfig")
    @ResponseBody
    public void saveAppSortConfig(HttpServletRequest request, HttpServletResponse response,
                                  @RequestBody String body) {
        VcBaseConfig record = JSON.toObject(body, VcBaseConfig.class);
        Long result = dmvAppWallService.saveAppSortConfig(record);
        ControllerUtils.returnJson(request, response, result);
    }


    /**
     * 获取视图信息
     * @param request
     * @param response
     * @param esSysId
     * @param type 1:全景墙配置视图  2:分层分域配置视图
     */
    @GetMapping("/findAppDiagramList")
    @ResponseBody
    public void findAppDiagramList(HttpServletRequest request, HttpServletResponse response,
                                   @RequestParam("esSysId") String esSysId,
                                   @RequestParam("type") Integer type) {
        List<VcDiagramInfo> appDiagramList = dmvAppWallService.findAppDiagramList(esSysId, type);
        ControllerUtils.returnJson(request, response, appDiagramList);
    }

    @ApiOperation(value = "通过ciIds查询他们之间的关系", httpMethod = "POST")
    @ApiResponses( {@ApiResponse(response = ResultWrapper.class, code = 200, message = "关系信息,包含相关的CI信息")})
    @RequestMapping("/queryCiBetweenRlt")
    @ResponseBody
    public ResultWrapper<List<?>> queryCiBetweenRlt(HttpServletRequest request, HttpServletResponse response, @RequestBody CiBetweenRltCdt cdt) {
        com.uinnova.product.eam.model.dmv.VcCiRltQ[] ciRltQs = cdt.getCiRltQs();
        if (BinaryUtils.isEmpty(ciRltQs)) {
            ciRltQs = new com.uinnova.product.eam.model.dmv.VcCiRltQ[] {};
        }
        List<VcCiRltInfo> vcCiRltInfos = dmvAppWallService.queryCiBetweenRlt(cdt.getIds(), cdt.getCiCodes(), cdt.getClassIds(), cdt.getTypes(), ciRltQs);
        return new ResultWrapper(vcCiRltInfos);
    }

    /**
     * 查询关联层域的应用列表
     * @param request
     * @param response
     */
    @PostMapping("/findLayerDomainCiList")
    @ResponseBody
    public RemoteResult findLayerDomainCiList(HttpServletRequest request, HttpServletResponse response) {
        SysUser sysUser = SysUtil.getCurrentUserInfo();
        if (sysUser == null) {
            throw new BusinessException("获取当前用户错误!");
        }
        List<Layer> layerDomainCiList = dmvAppWallService.findLayerDomainCiList(sysUser);
        return new RemoteResult(layerDomainCiList);
    }


    @GetMapping("/getCardInfos")
    @ResponseBody
    public RemoteResult getCardInfos(@RequestParam String ciCode , @RequestParam String cfgCode ) {
        Assert.notNull(ciCode,"查询的资产ciCode不能为空");
        Assert.notNull(cfgCode,"全景配置code不能为空");
        Object result = dmvAppWallService.getCardInfos(ciCode,cfgCode);
        return new RemoteResult(result);
    }
}