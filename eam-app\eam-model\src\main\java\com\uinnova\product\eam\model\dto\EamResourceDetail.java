package com.uinnova.product.eam.model.dto;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.model.enums.AssetType;
import com.uinnova.product.vmdb.comm.model.ci.CcCi;
import lombok.Data;

/**
 * 文件夹、视图or方案详情返回体
 * <AUTHOR>
 */
@Data
public class EamResourceDetail {
    @Comment("资源类型")
    private AssetType assetType;
    @Comment("名称")
    private String name;
    @Comment("视图id")
    private String diagramId;
    @Comment("是否协作")
    private boolean share = false;
    @Comment("缩略图")
    private String icon;
    @Comment("制品数量")
    private Integer artifactNum;
    @Comment("使用制品类型名称")
    private String artifactName;
    @Comment("关联方案个数")
    private Integer planNum;
    @Comment("所在文件夹名称")
    private String dirName;
    @Comment("方案类型")
    private String planType;
    @Comment("方案模版名称")
    private String planTemplate;
    @Comment("方案关联名称")
    private String assetName;
    @Comment("最新版本")
    private String newestVersion;
    @Comment("说明")
    private String remark;
    @Comment("关联资产信息")
    private String ciName;
    @Comment("创建者")
    private String creator;
    @Comment("创建时间")
    private Long createTime;
    @Comment("最近更新时间")
    private Long modifyTime;

}
