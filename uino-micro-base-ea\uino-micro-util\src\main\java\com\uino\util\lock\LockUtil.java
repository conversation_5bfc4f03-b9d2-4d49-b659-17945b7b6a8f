package com.uino.util.lock;

import java.util.concurrent.TimeUnit;

/**
 * @Title: LockUtil
 * @Description: distributed lock
 * @Author: YGQ
 * @Create: 2021-07-14 13:00
 **/
public interface LockUtil {

    /**
     * If the lock is available this method returns immediately
     * with the value {@code true}.
     * <p>
     * If the lock is not available then
     * the current thread becomes disabled for thread scheduling
     * purposes and lies dormant until one of three things happens:
     * <p>
     * The lock is acquired by the current thread;
     * <p>
     * Some other thread {@linkplain Thread#interrupt interrupts} the
     * current thread, and interruption of lock acquisition is supported; or
     * <p>
     * The specified waiting time elapses
     *
     * @param lockName name of lock
     * @param time     the maximum time to wait for the lock
     * @param unit     the time unit of the {@code time} argument, {@link TimeUnit}
     * @param wait     Whether to wait for the lock to be acquired, if the lock is occupied, wait for the lock to be released
     * @return {@code true} if the lock was acquired and {@code false}
     * if the waiting time elapsed before the lock was acquired
     */
    Boolean tryLock(String lockName, long time, TimeUnit unit, Boolean... wait);

    /**
     * Acquires the lock if it is available and returns immediately
     * <p>
     * with the value {@code true}.
     * If the lock is not available then this method will return
     * immediately with the value {@code false}.
     * <p>
     * After acquiring the lock, you need to actively release the lock
     * try {
     * // ...
     * } finally {
     * unLock();
     * }
     *
     * @param lockName name of lock
     * @param wait     Whether to wait for the lock to be acquired, if the lock is occupied, wait for the lock to be released
     * @return {@code true} or {@code false}
     */
    Boolean tryLock(String lockName, Boolean... wait);

    /**
     * Releases the lock.
     *
     * <b>Implementation Considerations</b>
     *
     * <p>
     * A {@code Lock} implementation will usually impose
     * restrictions on which thread can release a lock (typically only the
     * holder of the lock can release it)
     *
     * @param lockName name of lock
     * @return {@code true} or {@code false}
     */
    Boolean unLock(String lockName);

    /**
     * Unlocks lock independently of state
     *
     * @param lockName name of lock
     * @return <code>true</code> if unlocked otherwise <code>false</code>
     */
    Boolean forceUnlock(String lockName);

    /**
     * Checks if this lock locked by any thread
     *
     * @param lockName name of lock
     * @return <code>true</code> if locked otherwise <code>false</code>
     */
    Boolean isLocked(String lockName);
}
