package com.uinnova.product.eam.comm.dto;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class DataSetSheetInfo {

    @Comment("数据集链路/路径名称信息")
    private List<String> namePath;

    @Comment("数据集链路/路径id信息")
    private List<Long> idPath;

    @Comment("sheet页信息")
    private String sheetId;

    @Comment("数据集表头信息")
    private List<Map<String, String>> headers;

    @Comment("数据集列表内容")
    private List<Map<String, Object>> data;

}
