package com.uino.api.client.cmdb.local;

import com.uino.service.cmdb.dataset.microservice.IDataSetExeResultSvc;
import com.uino.bean.cmdb.base.dataset.batch.DataSetExeResultSheet;
import com.uino.api.client.cmdb.IDataSetExeResultApiSvc;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Title: DataSetExeResultApiSvcLocal
 * @Description: DataSetExeResultApiSvcLocal
 * @Author: YGQ
 * @Create: 2021-05-31 15:58
 **/
@Service
public class DataSetExeResultApiSvcLocal implements IDataSetExeResultApiSvc {

    private final IDataSetExeResultSvc iDataSetExeResultSvc;

    @Autowired
    public DataSetExeResultApiSvcLocal(IDataSetExeResultSvc iDataSetExeResultSvc) {
        this.iDataSetExeResultSvc = iDataSetExeResultSvc;
    }

    @Override
    public void saveOrUpdateBatch(List<DataSetExeResultSheet> dataSetExeResultSheets) {
        iDataSetExeResultSvc.saveOrUpdateBatch(dataSetExeResultSheets);
    }

    @Override
    public void deleteByDataSetId(Long id) {
        iDataSetExeResultSvc.deleteByQuery(id);
    }
}
