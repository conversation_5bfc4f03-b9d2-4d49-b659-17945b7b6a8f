package com.uino.provider.feign.cmdb;


import com.uino.provider.feign.config.BaseFeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 任务锁的相关服务接口
 *
 * <AUTHOR>
 * @version 2020-4-24
 */
@Component
@FeignClient(name = "${feign-server-name:tarsier-platform}", path = "${feign-server-path:tarsier-platform}/feign/taskLocal", configuration = {
        BaseFeignConfig.class })
public interface TaskLockFeign {

    /**
     * 获取任务锁，分布式部署时避免定时任务重复执行，只有成功拿到任务锁才允许任务执行
     *
     * @param taskName 任务名称
     * @param taskExecuteTime 任务执行时长
     * @return 是否成功拿到锁
     */
    @PostMapping("getLock")
    boolean getLock(@RequestParam(value = "taskName")String taskName,@RequestParam(value = "taskExecuteTime") long taskExecuteTime);

    /**
     * 解除指定任务锁
     *
     * @param taskName  任务锁名称
     */
    @PostMapping("breakLock")
    void breakLock(@RequestParam(value = "taskName")String taskName);

    /**
     * 增加任务锁
     *
     * @param taskName 任务名称
     * @return 新增:true,更新:false
     */
    @PostMapping("addLock")
    boolean addLock(@RequestParam(value = "taskName")String taskName);

    /**
     * 判断某个任务是否处于锁定中
     * @param taskName 任务名称
     * @return true: 锁定, false: 未锁定
     */
    @PostMapping("isLocked")
    boolean isLocked(@RequestParam(value = "taskName")String taskName);

}
