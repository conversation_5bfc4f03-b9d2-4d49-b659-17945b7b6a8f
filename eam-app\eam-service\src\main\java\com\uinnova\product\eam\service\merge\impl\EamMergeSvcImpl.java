package com.uinnova.product.eam.service.merge.impl;

import com.binary.core.util.BinaryUtils;
import com.uinnova.product.eam.base.diagram.model.ESDiagram;
import com.uinnova.product.eam.model.bm.EamMergeParams;
import com.uinnova.product.eam.service.IEamNoticeService;
import com.uinnova.product.eam.service.merge.EamMergeSvc;
import com.uino.bean.cmdb.base.LibType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 流程建模发布/检出接口实现
 * <AUTHOR>
 */
@Slf4j
@Service
public class EamMergeSvcImpl implements EamMergeSvc {

    @Autowired
    private EamMergePreProcessor preProcessor;

    @Autowired
    private EamCiRltMerge ciRltMerge;

    @Autowired
    private EamCategoryMerge catalogMerge;

    @Autowired
    private EamDiagramMerge diagramMerge;

    @Autowired
    private EamMatrixMerge matrixMerge;

    @Autowired
    private EamMergePostProcessor postProcessor;

    @Autowired
    @Lazy
    private IEamNoticeService eamNoticeService;

    @Override
    public void modelPush(List<Long> dirIds, String diagramId, String desc, Long parentId) {
        EamMergeParams params = preProcessor.pushBaseQuery(dirIds, Collections.singletonList(diagramId), parentId);

        params.setDesc(BinaryUtils.isEmpty(desc) ? "" : desc);
        preProcessor.sync(params, LibType.PRIVATE);
//        ciRltMerge.pushCheck(params);
//        catalogMerge.pushCheck(params);
//        diagramMerge.pushCheck(params);

        ciRltMerge.push(params);
        catalogMerge.push(params);
        diagramMerge.push(params);
        postProcessor.refreshCategory(params, LibType.DESIGN);
        postProcessor.execute(params);
        //推送工作台-消息
        List<String> msgDiagramSet = params.getMergeDiagramList().stream().map(ESDiagram::getDEnergy).collect(Collectors.toList());
        msgDiagramSet.addAll(params.getDiagramIds());
        eamNoticeService.diagramUpdateMsgSave(msgDiagramSet);
    }

    @Override
    public void modelPull(Long dirId, String diagramId, Long targetDirId, LibType libType) {
        long start = System.currentTimeMillis();
        ciRltMerge.pullCheck(dirId);
        long checkEnd = System.currentTimeMillis();
        log.info("校验时间：{}", checkEnd-start);
        EamMergeParams params = preProcessor.pullBaseQuery(dirId, Collections.singletonList(diagramId), libType);
        long queryEnd = System.currentTimeMillis();
        log.info("查询时间：{}", queryEnd-checkEnd);
        params.setTargetDirId(targetDirId);
        preProcessor.sync(params, LibType.DESIGN);
        long syncEnd = System.currentTimeMillis();
        log.info("同步时间：{}", syncEnd-queryEnd);
        // ciRltMerge.pullCheck(params, dirId, diagramId);
        catalogMerge.pullCheck(params);
        diagramMerge.pullCheck(params);
        catalogMerge.pull(params, libType);
        long categoryEnd = System.currentTimeMillis();
        log.info("目录时间：{}", categoryEnd-syncEnd);
        ciRltMerge.pull(params);
        long pullStart = System.currentTimeMillis();
        log.info("ciRlt时间：{}", pullStart-categoryEnd);
        diagramMerge.pull(params);
        log.info("视图检出时间：{}", System.currentTimeMillis() - pullStart);
    }

    @Override
    public void matrixPush(Long id, Long dirId, String description) {
        EamMergeParams params = preProcessor.pushBaseQuery(id, dirId);
        log.info("矩阵{}发布基础查询结束", id);
        params.setTargetDirId(dirId);
        preProcessor.sync(params, LibType.PRIVATE);
        log.info("同步设计库数据结束");
        ciRltMerge.push(params);
        log.info("对象及关系发布结束");
        matrixMerge.push(params, description);
        log.info("矩阵发布结束");
    }

    @Override
    public void matrixPull(Long id, Long dirId) {
        EamMergeParams params = preProcessor.pullBaseQuery(id, dirId);
        log.info("矩阵{}检出基础查询结束", id);
        params.setTargetDirId(dirId);
        preProcessor.sync(params, LibType.DESIGN);
        log.info("同步数据结束");
        ciRltMerge.pull(params);
        log.info("对象及关系检出结束");
        matrixMerge.pull(params);
        log.info("矩阵检出结束");
    }
}
