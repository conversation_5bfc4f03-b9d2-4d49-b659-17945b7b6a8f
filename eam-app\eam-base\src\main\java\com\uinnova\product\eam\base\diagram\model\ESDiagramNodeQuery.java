package com.uinnova.product.eam.base.diagram.model;


import com.alibaba.fastjson.annotation.JSONField;
import com.binary.framework.bean.EntityBean;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 视图节点dto
 * @Date 17:14 2021/6/3
 * @Param
 * @return
 **/
@Data
public class ESDiagramNodeQuery implements EntityBean {

	private static final long serialVersionUID = 1L;

	private Long id;
	private Long[] ids;
	private String category;
	private Integer childShapeId;
	private String figure;
	private String fill;
	private Boolean flowchart;
	private Integer height;
	private String initialShapeName;
	private Boolean isLockNodeRatio;
	private String key;
	private String label;
	private String layerName;
	private String loc;
	private String shapeName;
	private String shapeParentName;
	private String stroke;
	private Integer strokeWidth;
	private Integer width;
	private Integer zOrder;
	private Integer textOriginalHeight;
	private String size;
	private Boolean attatch;
	private Boolean lock;
	private String fontColor;
	private String fontFamily;
	private Integer fontSize;
	private String fontStyle;
	private String fontWeight;
	private String textAlign;
	private Boolean isUnderline;
	private Double attrHeight;
	private String attrLabel;
	private String attrTextAlign;
	private Integer attrFontSize;
	private String attrFontStyle;
	private String attrFontWeight;
	private Boolean attrUnderline;
	private String funcFontColor;
	private Integer funcFontSize;
	private String funcFontStyle;
	private String funcFontWeight;
	private Double funcHeight;
	private String funcLabel;
	private String funcTextAlign;
	private Double titleHeight;
	private String titleLabel;
	private String titleFontColor;
	private Integer titleFontSize;
	private String titleFontStyle;
	private String titleFontWeight;
	private Boolean titleUnderline;
	private Boolean textSpot;
	private Boolean interrupting;
	private Boolean throwing;
	private Boolean noNeedMakeUpPoint;
	private Integer parameter1;
	private Integer headerHeight;
	private Boolean textOrientation;
	private Boolean textEditable;
	private Boolean disabledFill;
	private Boolean isGroup;
	private String group;
	private Boolean aroundLock;
	private List<?> portArray;
	private List<Integer> strokeDashArray;
	private TextOptionDTO textOption;
	private BadgeDTO badge;
	private RelationInfoDTO relationInfo;
	private List<ItemsDTO> items;
	private List<List<Double>> predefinedPortArr;
	private Long diagramId;
	private Long[] diagramIds;
	private Long sheetId;
	private Long[] sheetIds;

	private Long createTime;

	private Long modifyTime;

	public Boolean getIsGroup() {
		return isGroup;
	}

	public void setIsGroup(Boolean isGroup) {
		this.isGroup = isGroup;
	}

	public String getGroup() {
		return group;
	}

	public void setGroup(String group) {
		this.group = group;
	}

	public static class TextOptionDTO {
		@JSONField(name = "after")
		private Integer after;
		@JSONField(name = "before")
		private Integer before;
		@JSONField(name = "deleteStroke")
		private Boolean deleteStroke;
		@JSONField(name = "disabled")
		private Boolean disabled;
		@JSONField(name = "leftIndent")
		private Integer leftIndent;
		@JSONField(name = "lineHeight")
		private Integer lineHeight;
		@JSONField(name = "padding")
		private Integer padding;
		@JSONField(name = "rightIndent")
		private Integer rightIndent;
		@JSONField(name = "rotate")
		private Integer rotate;

		public Integer getAfter() {
			return after;
		}

		public void setAfter(Integer after) {
			this.after = after;
		}

		public Integer getBefore() {
			return before;
		}

		public void setBefore(Integer before) {
			this.before = before;
		}

		public Boolean getDeleteStroke() {
			return deleteStroke;
		}

		public void setDeleteStroke(Boolean deleteStroke) {
			this.deleteStroke = deleteStroke;
		}

		public Boolean getDisabled() {
			return disabled;
		}

		public void setDisabled(Boolean disabled) {
			this.disabled = disabled;
		}

		public Integer getLeftIndent() {
			return leftIndent;
		}

		public void setLeftIndent(Integer leftIndent) {
			this.leftIndent = leftIndent;
		}

		public Integer getLineHeight() {
			return lineHeight;
		}

		public void setLineHeight(Integer lineHeight) {
			this.lineHeight = lineHeight;
		}

		public Integer getPadding() {
			return padding;
		}

		public void setPadding(Integer padding) {
			this.padding = padding;
		}

		public Integer getRightIndent() {
			return rightIndent;
		}

		public void setRightIndent(Integer rightIndent) {
			this.rightIndent = rightIndent;
		}

		public Integer getRotate() {
			return rotate;
		}

		public void setRotate(Integer rotate) {
			this.rotate = rotate;
		}
	}

	public static class BadgeDTO {
		@JSONField(name = "alignment")
		private String alignment;
		@JSONField(name = "alignmentFocus")
		private String alignmentFocus;
		@JSONField(name = "color")
		private String color;
		@JSONField(name = "figure")
		private String figure;
		@JSONField(name = "icon")
		private String icon;
		@JSONField(name = "image")
		private String image;

		public String getAlignment() {
			return alignment;
		}

		public void setAlignment(String alignment) {
			this.alignment = alignment;
		}

		public String getAlignmentFocus() {
			return alignmentFocus;
		}

		public void setAlignmentFocus(String alignmentFocus) {
			this.alignmentFocus = alignmentFocus;
		}

		public String getColor() {
			return color;
		}

		public void setColor(String color) {
			this.color = color;
		}

		public String getFigure() {
			return figure;
		}

		public void setFigure(String figure) {
			this.figure = figure;
		}

		public String getIcon() {
			return icon;
		}

		public void setIcon(String icon) {
			this.icon = icon;
		}

		public String getImage() {
			return image;
		}

		public void setImage(String image) {
			this.image = image;
		}
	}

	public static class RelationInfoDTO {
		@JSONField(name = "isNewWindowOpen")
		private Boolean isNewWindowOpen;
		@JSONField(name = "name")
		private String name;
		@JSONField(name = "page")
		private String page;
		@JSONField(name = "type")
		private String type;
		@JSONField(name = "value")
		private Long value;

		public Boolean getIsNewWindowOpen() {
			return isNewWindowOpen;
		}

		public void setIsNewWindowOpen(Boolean isNewWindowOpen) {
			this.isNewWindowOpen = isNewWindowOpen;
		}

		public String getName() {
			return name;
		}

		public void setName(String name) {
			this.name = name;
		}

		public String getPage() {
			return page;
		}

		public void setPage(String page) {
			this.page = page;
		}

		public String getType() {
			return type;
		}

		public void setType(String type) {
			this.type = type;
		}

		public Long getValue() {
			return value;
		}

		public void setValue(Long value) {
			this.value = value;
		}
	}

	public static class ItemsDTO {
		@JSONField(name = "emptyHeight")
		private Double emptyHeight;
		@JSONField(name = "fill")
		private String fill;
		@JSONField(name = "label")
		private String label;
		@JSONField(name = "partUUID")
		private String partUUID;
		@JSONField(name = "textAlign")
		private String textAlign;
		@JSONField(name = "width")
		private Integer width;

		public Double getEmptyHeight() {
			return emptyHeight;
		}

		public void setEmptyHeight(Double emptyHeight) {
			this.emptyHeight = emptyHeight;
		}

		public String getFill() {
			return fill;
		}

		public void setFill(String fill) {
			this.fill = fill;
		}

		public String getLabel() {
			return label;
		}

		public void setLabel(String label) {
			this.label = label;
		}

		public String getPartUUID() {
			return partUUID;
		}

		public void setPartUUID(String partUUID) {
			this.partUUID = partUUID;
		}

		public String getTextAlign() {
			return textAlign;
		}

		public void setTextAlign(String textAlign) {
			this.textAlign = textAlign;
		}

		public Integer getWidth() {
			return width;
		}

		public void setWidth(Integer width) {
			this.width = width;
		}

		@Override
		public String toString() {
			return "ItemsDTO{" +
					"emptyHeight=" + emptyHeight +
					", fill='" + fill + '\'' +
					", label='" + label + '\'' +
					", partUUID='" + partUUID + '\'' +
					", textAlign='" + textAlign + '\'' +
					", width=" + width +
					'}';
		}
	}
}


