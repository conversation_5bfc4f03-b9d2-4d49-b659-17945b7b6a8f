package com.uino.bean.cmdb.business;

import java.io.Serializable;
import java.util.LinkedList;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 
 * <AUTHOR>
 *
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value="分类树结构节点信息类",description = "分类树结构的节点信息")
public class ClassNodeInfo implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value="分类id",example = "0",required = true)
    private Long id;
    @ApiModelProperty(value="分类名称",example="nan")
    private String name;
    @ApiModelProperty(value="分类类型",example="test")
    private String type;
    @ApiModelProperty(value="分类个数",example = "6")
    private Long count;
    @ApiModelProperty(value="分类图标",example = "defaulticon")
    private String icon;
    @ApiModelProperty(value="分类图形",example="circle")
    private String shape;
    @ApiModelProperty(value="分类序号",example="1")
    private Integer orderNo;
    @ApiModelProperty(value="分类code",example = "test")
    private String classCode;
    @ApiModelProperty(value="子类标识",example = "test")
    private Boolean isSubClass;

    /**
     * 孩子节点
     */
    @Builder.Default
    private List<ClassNodeInfo> children = new LinkedList<>();
}
