package com.uinnova.product.eam.model.vo;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

/**
 * 视图发布校验返回体
 */
@Data
public class PushCheckVO {
    @Comment("id")
    private String id;
    @Comment("视图名称")
    private String name;
    @Comment("校验信息")
    private Object data;

    public PushCheckVO() {
    }

    public PushCheckVO(String id, String name) {
        this.id = id;
        this.name = name;
    }

    public PushCheckVO(String id, String name, Object data) {
        this.id = id;
        this.name = name;
        this.data = data;
    }
}
