package com.uinnova.product.eam.web.cj;

import com.binary.jdbc.Page;
import com.uinnova.product.eam.base.cj.ResultMsg;
import com.uinnova.product.eam.model.cj.domain.PlanChapterQuestion;
import com.uinnova.product.eam.model.cj.request.PlanChapterQuestionAnswerRequest;
import com.uinnova.product.eam.model.cj.request.PlanChapterQuestionRequest;
import com.uinnova.product.eam.model.cj.request.PlanModuleAnnotationRequest;
import com.uinnova.product.eam.model.cj.vo.PlanChapterQuestionVO;
import com.uinnova.product.eam.model.cj.vo.QuestionAnswerVO;
import com.uinnova.product.eam.service.cj.service.PlanChapterQuestionService;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * @description:
 * @author: Lc
 * @create: 2022-03-01 19:56
 */
@RestController
@RequestMapping("/question")
public class PlanChapterQuestionController {

    @Resource
    private PlanChapterQuestionService planChapterQuestionService;

    /**
     * 新增问题并且新增批注
     * @param question
     * @return
     */
    @PostMapping("/saveOrUpdate")
    public ResultMsg saveOrUpdate(@RequestBody PlanChapterQuestion question) {
        Long result = planChapterQuestionService.saveOrUpdate(question);
        return new ResultMsg(result);
    }

    /**
     * 将批注归档成问题
     * @param question
     * @return
     */
    @PostMapping("/saveArchivingQuestion")
    public ResultMsg saveArchivingQuestion(@RequestBody PlanChapterQuestion question) {
        Long result = planChapterQuestionService.saveArchivingQuestion(question);
        return new ResultMsg(result);
    }

    @PostMapping("/saveAnswer")
    public ResultMsg saveAnswer(@RequestBody PlanChapterQuestionAnswerRequest request) {
        Long id = planChapterQuestionService.saveAnswer(request);
        return new ResultMsg(id);
    }

    @PostMapping("/find")
    public ResultMsg findQuestionList(@RequestBody PlanChapterQuestionRequest request) {
        Page<PlanChapterQuestionVO> questionList = planChapterQuestionService.findQuestionList(request);
        return new ResultMsg(questionList);
    }

    @GetMapping("/findAnswer/{id}")
    public ResultMsg findAnswerList(@PathVariable("id") Long id) {
        List<QuestionAnswerVO> answerList = planChapterQuestionService.findAnswerList(id);
        return new ResultMsg(answerList);
    }

    /**
     * 删除问题和批注
     **/
    @GetMapping("/delete/{annotationId}")
    public ResultMsg deleteQuestionAndAnnotation(@PathVariable("annotationId") Long annotationId) {
        Boolean result = planChapterQuestionService.deleteQuestionAndAnnotation(annotationId);
        return new ResultMsg(result);
    }

    /**
     * 修改问题和批注
     **/
    @PostMapping("/update")
    public ResultMsg updateQuestionAndAnnotation(@RequestBody PlanModuleAnnotationRequest request) {
        Boolean result = planChapterQuestionService.updateQuestionAndAnnotation(request);
        return new ResultMsg(result);
    }

    /**
     * 问题或批注取消归档
     **/
    @GetMapping("/cancel/{annotationId}")
    public ResultMsg cancelArchiving(@PathVariable("annotationId") Long annotationId) {
        Boolean result = planChapterQuestionService.cancelArchiving(annotationId);
        return new ResultMsg(result);
    }
}
