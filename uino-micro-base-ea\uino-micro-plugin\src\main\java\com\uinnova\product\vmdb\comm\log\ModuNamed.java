package com.uinnova.product.vmdb.comm.log;

/**
 * 
 * <AUTHOR>
 *
 */
public enum ModuNamed {

    /**
     * CMV模块名称
     */
    CMV,

    /**
     * DMV模块名称
     */
    DMV,

    /**
     * DCV模块名称
     */
    DCV,

    /**
     * EMV模块名称
     */
    EMV,

    /**
     * PMV模块名称
     */
    PMV,

    /**
     * SMV模块名称
     */
    SMV,

    /**
     * MOM模块名称
     */
    MOM;

    public static ModuNamed valueOfMvc(String mvc) {
        if (mvc == null || (mvc = mvc.trim()).length() == 0) {
            return null;
        }
        String cmvPackageStart = "com.uinnova.product.vmdb.web";
        String dmvPackageStart = "com.uinnova.product.diagram.web";
        String dcvPackageStart = "com.uinnova.product.dcv.web";
        String emvPackageStart = "com.uinnova.product.monitor.web";
        String pmvPackageStart = "com.uinnova.product.pmv.web";
        String momPackageStart = "com.uinnova.product.mom.web";
        if (mvc.startsWith(cmvPackageStart)) {
            return CMV;
        } else if (mvc.startsWith(dmvPackageStart)) {
            return DMV;
        } else if (mvc.startsWith(dcvPackageStart)) {
            return DCV;
        } else if (mvc.startsWith(emvPackageStart)) {
            return EMV;
        } else if (mvc.startsWith(pmvPackageStart)) {
            return PMV;
        } else if (mvc.startsWith(momPackageStart)) {
            return MOM;
        }
        return null;
    }

}
