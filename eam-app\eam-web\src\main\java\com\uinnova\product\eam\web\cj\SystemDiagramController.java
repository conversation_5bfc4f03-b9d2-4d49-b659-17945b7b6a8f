package com.uinnova.product.eam.web.cj;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.binary.core.exception.BinaryException;
import com.binary.core.exception.MessageException;
import com.binary.framework.web.RemoteResult;
import com.binary.jdbc.Page;
import com.uinnova.product.eam.base.diagram.model.CVcDiagram;
import com.uinnova.product.eam.base.diagram.model.ESDiagram;
import com.uinnova.product.eam.base.diagram.model.ESSimpleDiagramDTO;
import com.uinnova.product.eam.comm.model.CVcDiagramDir;
import com.uinnova.product.eam.comm.model.VcDiagramDir;
import com.uinnova.product.eam.comm.model.es.*;
import com.uinnova.product.eam.feign.cj.SystemDiagramFeign;
import com.uinnova.product.eam.model.*;
import com.uinnova.product.eam.model.asset.EamReleaseHistoryDTO;
import com.uinnova.product.eam.model.bm.PushParams;
import com.uinnova.product.eam.model.bm.ReleaseValidResponse;
import com.uinnova.product.eam.model.dmv.ClassDefinitionVO;
import com.uinnova.product.eam.model.dto.AttentionDto;
import com.uinnova.product.eam.model.dto.DiagramParamDto;
import com.uinnova.product.eam.model.dto.ElementDto;
import com.uinnova.product.eam.model.dto.ReleaseDiagramDTO;
import com.uinnova.product.eam.model.enums.DiagramFieldEnum;
import com.uinnova.product.eam.model.enums.HandleTypeEnum;
import com.uinnova.product.eam.model.vo.*;
import com.uinnova.product.eam.service.*;
import com.uinnova.product.eam.service.asset.IAttentionSvc;
import com.uinnova.product.eam.service.asset.IRecentlyViewSvc;
import com.uinnova.product.eam.service.diagram.ESDiagramSvc;
import com.uinnova.product.eam.service.diagram.EsDiagramSvcV2;
import com.uinnova.product.eam.service.merge.EamMergeSvc;
import com.uinnova.product.eam.service.handler.PushFacadeService;
import com.uinnova.product.eam.web.bm.bean.ReleaseModuleDiagramDTO;
import com.uinnova.product.eam.web.bm.peer.BmDiagramPeer;
import com.uinnova.product.eam.web.diagram.peer.SystemDiagramPeer;
import com.uinnova.product.vmdb.comm.i18n.MessageUtil;
import com.uinnova.product.vmdb.comm.model.ci.CcCi;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CiGroupPage;
import com.uino.api.client.cmdb.IDataSetApiSvc;
import com.uino.bean.cmdb.base.ESCIAttrDefInfo;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.base.LibType;
import com.uino.bean.cmdb.base.dataset.OperateType;
import com.uino.bean.cmdb.business.dataset.DataSetExeResultSheetPage;
import com.uino.bean.permission.base.SysUser;
import com.uino.service.cmdb.microservice.impl.CIClassSvc;
import com.uino.util.sys.SysUtil;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 系统级列表
 * <AUTHOR>
 */
@RestController
@RequestMapping("/cj/system/diagram")
public class SystemDiagramController implements SystemDiagramFeign{

    @Value("${http.resource.space}")
    private String httpResouceUrl;

    @Autowired
    private IEamDiagramSvc diagramSvc;

    @Autowired
    private SystemDiagramPeer systemDiagramPeer;

    @Resource
    private EamDiagramRelationSysService eamDiagramRelationSysService;

    @Autowired
    private IOperateSettingService operateSettingService;

    @Resource
    private BmDiagramPeer bmDiagramPeer;

    @Autowired
    private IRecentlyViewSvc recentlyViewSvc;

    @Resource
    private PushFacadeService pushFacadeService;

    @Autowired
    private ESDiagramSvc esDiagramSvc;
    @Resource
    private com.uinnova.product.eam.service.diagram.ESDiagramSvc diagramApiClient;
    @Resource
    private EsDiagramSvcV2 esDiagramSvcV2;

    @Autowired
    private IAttentionSvc attentionSvc;

    @Autowired
    private FxDiagramSvc fxDiagramSvc;

    @Autowired
    private IEamArtifactColumnSvc iEamArtifactColumnSvc;

    @Autowired
    ICISwitchSvc ciSwitchSvc;

    @Resource
    CIClassSvc clsSvc;

    @Resource
    private BmModuleSvc bmModuleSvc;

    @Resource
    private EamMergeSvc eamMergeSvc;

    @Autowired
    private WorkbenchChargeDoneSvc workbenchChargeDoneSvc;

    @Autowired
    private IDataSetApiSvc dataSetApiSvc;

    @Resource
    private EamCategorySvc categorySvc;

    @Override
    @PostMapping("/getCiDirInfo")
    public RemoteResult getCiDirInfo(@RequestBody String body) {
        JSONObject jsonObject = JSON.parseObject(body);
        Long dirId = jsonObject.getLong("dirId");
        CiDirVo ciInfoVo = systemDiagramPeer.getCiDirInfo(dirId);
        return new RemoteResult(ciInfoVo);
    }

    @Override
    @GetMapping("/getCiByCiCode")
    public CcCiInfo getCiByCiCode(@RequestParam(value = "ciCode") String ciCode) {
        return systemDiagramPeer.getCiByCiCode(ciCode);
    }
    @Override
    @PostMapping("/getBathCi")
    public List<CcCi> getBathCi(@RequestBody EchoCiVO echoCiVO) {
        return systemDiagramPeer.getBathCi(echoCiVO.getCiCodes());
    }

    @Override
    @PostMapping("/findCiSysList")
    public RemoteResult findCiSysList(@RequestBody String body) {
        JSONObject jsonObject = JSON.parseObject(body);
        // type取值 1：子系统  2：应用系统
        Integer type = jsonObject.getInteger("type");
        String name = jsonObject.getString("name");
        List list = diagramSvc.findCiSysList(type, name);
        return new RemoteResult(list);
    }

    @GetMapping("/findDesignCiSysList")
    public CiGroupPage findDesignCiSysList() {
        return diagramSvc.findDesignCiSysList();
    }

    @Override
    @PostMapping("/insertCiSysDesign")
    public RemoteResult insertCiSysDesign(@RequestBody String body) {
        JSONObject jsonObject = JSON.parseObject(body);
        // type取值 1：子系统  2：应用系统
        Integer type = jsonObject.getInteger("type");
        Long ciId = jsonObject.getLong("id");
        Long result = diagramSvc.insertCiSysDesign(type, ciId);
        return new RemoteResult(result);
    }

    @Override
    @PostMapping("/addSysDir")
    public RemoteResult addSysDir(@RequestBody SysDirVO sysDirVO) {
        if (sysDirVO.getCiName().length() > 100) {
            throw new BinaryException("文件夹名称长度超过100,创建失败");
        }
        Long result = diagramSvc.addSysDir(sysDirVO);
        return new RemoteResult(result);
    }

    @Override
    @PostMapping("/saveDiagramRelationSys")
    public RemoteResult saveDiagramRelationSys(@RequestBody EamDiagramRelationSysCdt eamDiagramRelationSysCdt,@RequestParam(defaultValue = "DESIGN")LibType libType) {
        boolean result = eamDiagramRelationSysService.saveDiagramRelationSys(eamDiagramRelationSysCdt,eamDiagramRelationSysCdt.getLibType());
        return new RemoteResult(result);
    }

    @Override
    @GetMapping("/getEamDiagramRelationSys/{diagramEnergy}")
    public RemoteResult getEamDiagramRelationSys(@PathVariable("diagramEnergy") String diagramEnergy) {
        EamDiagramRelationSys eamDiagramRelationSys = eamDiagramRelationSysService.getEamDiagramRelationSys(diagramEnergy);
        return new RemoteResult(eamDiagramRelationSys);
    }

    @Override
    @GetMapping("/getDiagramSysCiCode")
    public EamDiagramRelationSys getDiagramSysCiCode(@RequestParam("diagramEnergy") String diagramEnergy) {
        return eamDiagramRelationSysService.getEamDiagramRelationSys(diagramEnergy);
    }

    @GetMapping("/getDiagramViewBySysCiCode")
    @Override
    public RemoteResult getDiagramViewBySysCiCode(String ciCode,@RequestParam(value ="diagramClassType",defaultValue = "flowDiagram") String diagramClassType) {
        List<EamDiagramRelationSys> diagramRelationSysPriavteList = eamDiagramRelationSysService.findFlowSystemDiagramRelationPrivate(ciCode,diagramClassType);
        if(CollectionUtils.isEmpty(diagramRelationSysPriavteList)){
            List<EamDiagramRelationSys> flowSystemDiagramRelation = eamDiagramRelationSysService.findFlowSystemDiagramRelation(ciCode,diagramClassType);
            if(CollectionUtils.isEmpty(flowSystemDiagramRelation)) {
                return new RemoteResult(new EamDiagramRelationSys());
            }else {
                return new RemoteResult(flowSystemDiagramRelation.get(0));
            }
        }
        return new RemoteResult(diagramRelationSysPriavteList.get(0));
    }

    /**
     * 名称模糊分页查询已发布的视图
     */
    @Override
    @PostMapping("findDiagramLikeName")
    public RemoteResult findDiagramLikeName (@RequestBody PlanDiagramRequest request) {
        return new RemoteResult(systemDiagramPeer.findDiagramLikeName (request));
    }

    /**
     * 通过筛选条件
     * 获取视图列表
     */
    @PostMapping("/findDiagramList")
    public RemoteResult findDiagramList (@RequestBody PlanDiagramRequest request) {
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        request.setCurrentLoginCode(currentUserInfo.getLoginCode());
        request.setCurrentUserId(currentUserInfo.getId());
        return new RemoteResult(systemDiagramPeer.findDiagramList(request));
    }

    /**
     * 根据条件查询本地视图信息
     * @param request
     * @return
     */
    @Override
    @PostMapping("findDiagramByCdt")
    public List<Map<String,Object>> findDiagramByCdt (@RequestBody PlanDiagramRequest request) {
        return systemDiagramPeer.findDiagramByCdt (request);
    }

    @PostMapping("/findSimpleDiagramByCdt")
    public List<ESDiagram> findSimpleDiagramByCdt (@RequestBody PlanDiagramRequest request) {
        return systemDiagramPeer.findSimpleDiagramByCdt (request);
    }

    @Override
    @RequestMapping(value = "/queryAllColumns")
    public List<EamArtifactElementVo> queryAllColumns(@RequestBody ElementDto elementDto) {
        List<EamArtifactElementVo> columns = iEamArtifactColumnSvc.queryAllColumns(elementDto);
        return columns;
    }

    @PostMapping("/batchQueryAllColumns")
    public Map<Long,List<EamArtifactElementVo>> batchQueryAllColumns(@RequestBody List<Long> artifactIds) {
        Map<Long, List<EamArtifactElementVo>> longListMap = iEamArtifactColumnSvc.queryAllColumnsByIds(artifactIds);
        return longListMap;
    }

    @Override
    @PostMapping("/releasePlan")
    public RemoteResult releasePlan(@RequestBody String body) {
        List<Long> sysIdList = JSONObject.parseArray(body, Long.class);
        boolean result = systemDiagramPeer.releasePlan(sysIdList);
        return new RemoteResult(result);
    }

    @GetMapping("/isMkClassDir")
    public RemoteResult isMkClassDir() {
        List<ClassSettingDTO> result = operateSettingService.isMkClassDir();
        return new RemoteResult(result);
    }

    /**
     * 发布视图
     * @param diagramDTO
     * @return
     */
    @Override
    @PostMapping("/publishDiagram")
    public String publishDiagram(@RequestBody ReleaseDiagramDTO diagramDTO) {
        return bmDiagramPeer.publishDiagram(diagramDTO.getDiagramId(), diagramDTO.getReleaseDesc(), diagramDTO.getDirId(), diagramDTO.getReleaseDiagramId());
    }

    @Override
    @PostMapping("/checkDiagram")
    public ReleaseValidResponse.ValidType checkDiagram(@RequestBody DiagramParamDto diagramParamDto) {
        PushParams params = new PushParams();
        BeanUtils.copyProperties(diagramParamDto, params);
        ReleaseValidResponse releaseValidResponse = pushFacadeService.checkCjDiagram(params);
        return releaseValidResponse.getValidType();
    }

    @Override
    @GetMapping("/getReleaseDiagramVersion")
    public ESDiagram getReleaseDiagramVersion(@RequestParam("diagramId")String diagramId) {
        BoolQueryBuilder diagramQuery = QueryBuilders.boolQuery();
        diagramQuery.must(QueryBuilders.termQuery("status", 1));
        diagramQuery.must(QueryBuilders.termQuery("dataStatus", 1));
        diagramQuery.must(QueryBuilders.termQuery("isOpen", 1));
        diagramQuery.must(QueryBuilders.termQuery("prepareDiagramId.keyword", diagramId));
        Page<ESDiagram> page = diagramApiClient.selectListByQuery(1, 1, diagramQuery);
        if(CollectionUtils.isEmpty(page.getData())){
            return null;
        }
        return page.getData().get(0);
    }

    @GetMapping("/getAllRecentlyDiagram")
    public AttentionVo getAllRecentlyDiagram() {
        RecentlyViewBo recentlyViewList = recentlyViewSvc.getAllRecentlyDiagram();
        AttentionVo attentionVo = new AttentionVo();
        attentionVo.setRecentlyViewBo(recentlyViewList);
        return attentionVo;
    }

    @GetMapping("/getDesignRecentlyDiagram")
    public AttentionVo getDesignRecentlyDiagram() {
        RecentlyViewBo recentlyViewList = recentlyViewSvc.getDesignRecentlyDiagram();
        AttentionVo attentionVo = new AttentionVo();
        attentionVo.setRecentlyViewBo(recentlyViewList);
        return attentionVo;
    }


    @PostMapping("/getMineFocus")
    public AttentionVo getMineFocus() {
        AttentionDto attentionDto = new AttentionDto();
        attentionDto.setUserId(SysUtil.getCurrentUserInfo().getId());
        AttentionVo attentionList = attentionSvc.getMineFocus(1,2000,attentionDto);
        return attentionList;
    }

    @Override
    @PostMapping("/getBuildAssets")
    @Deprecated
    public AttentionVo getBuildAssets(@RequestBody MineAssetsVo mineAssetsVo) {
        MessageUtil.checkEmpty(mineAssetsVo, "mineAssetsVo");
        MessageUtil.checkEmpty(mineAssetsVo.getHandleType(), "handleType");

        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        if (HandleTypeEnum.MINE_PUBLISH.getHandleType().equals(mineAssetsVo.getHandleType())) {
            BoolQueryBuilder diagramQuery = QueryBuilders.boolQuery();
            diagramQuery.must(QueryBuilders.termQuery("isOpen", 1));
            diagramQuery.must(QueryBuilders.termQuery("creator.keyword", currentUserInfo.getLoginCode()));
            diagramQuery.must(QueryBuilders.termQuery("historyVersionFlag", 1));
            Page<ESDiagram> diagramPage = diagramApiClient.selectListByQuery(1, 1000, diagramQuery);
            AttentionVo attentionVo = new AttentionVo();
            attentionVo.setDiagramList(diagramPage.getData());
            return attentionVo;
        }else if (HandleTypeEnum.MINE_ATTENTION.getHandleType().equals(mineAssetsVo.getHandleType())) {
            AttentionDto attentionDto = new AttentionDto();
            attentionDto.setUserId(currentUserInfo.getId());
            return attentionSvc.findAttentionList(1, 500, attentionDto);
        } else if (HandleTypeEnum.RECENTLY_VIEW.getHandleType().equals(mineAssetsVo.getHandleType())) {
            EamRecentlyView eamRecentlyView = new EamRecentlyView();
            eamRecentlyView.setUserId(currentUserInfo.getId());
            List<ESDiagram> recentlyViewList = recentlyViewSvc.findRecentlyViewList(eamRecentlyView);
            AttentionVo attentionVo = new AttentionVo();
            attentionVo.setDiagramList(recentlyViewList);
            return attentionVo;
        }
        return null;
    }

    @Override
    @GetMapping("/getMyAttentionPlans")
    public List<Long> getMyAttentionPlans() {
        List<Long> result = attentionSvc.getMyAttentionPlans();
        return result;
    }

    @GetMapping("/getMyAttentionDiagramIds")
    public List<String> getMyAttentionDiagramIds() {
        List<Long> myAttentionDiagramIds = attentionSvc.getMyAttentionDiagramIds();
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.must(QueryBuilders.termsQuery("id",myAttentionDiagramIds));
        Page<ESDiagram> esDiagramPage = esDiagramSvc.selectListByQuery(1, 2000, boolQuery);
        List<ESDiagram> data = esDiagramPage.getData();
        if (data == null || data.size() < 1) {
            return new ArrayList<>();
        }
        List<String> result = data.stream().map(item -> item.getDEnergy()).collect(Collectors.toList());
        return result;
    }

    @Override
    @GetMapping("/getRecentlyView")
    public List<EamRecentlyView> getRecentlyView(@RequestParam(required = false) Integer buildType) {
        List<EamRecentlyView> result = recentlyViewSvc.getRecentlyView(buildType);
        return result;
    }

    /**
     * 我发布的视图列表
     * @return
     */
    @GetMapping("/findMyPublishDiagramList")
    public List<ESSimpleDiagramDTO> findMyPublishDiagramList() {
        SysUser sysUser = SysUtil.getCurrentUserInfo();
        return systemDiagramPeer.findMyPublishDiagramList(sysUser);
    }

    @Override
    @PostMapping("/changeFlowByDiagramIds")
    public Boolean changeFlowByDiagramIds(List<String> eIds, Integer flowStatus) {
        return fxDiagramSvc.changeFlowByDiagramIds(eIds, flowStatus);
    }

    @Override
    @PostMapping("/getDirName")
    public String getDirNameById(@RequestBody SysDirVO sysDirVO) {
        EamCategory category = categorySvc.getById(sysDirVO.getDirId(), LibType.DESIGN);
        return category.getDirName();
    }

    @GetMapping("/getDiagramInfo")
    public ESDiagram getDiagramInfo(@RequestParam("energy") String energy) {
        if (StringUtils.isEmpty(energy)) {
            return null;
        }
        Long diagramId = diagramApiClient.queryDiagramInfoByEnergy(energy);
        if (diagramId == null) {
            return null;
        }
        ESDiagram esDiagram = diagramApiClient.querySimpleDiagramInfoById(diagramId);
        if (esDiagram != null) {
            // 缩略图
            String icon1 = esDiagram.getIcon1();
            if (icon1 != null && !icon1.startsWith(httpResouceUrl)) {
                icon1 = httpResouceUrl + icon1;
                esDiagram.setIcon1(icon1);
            }
        }
        return esDiagram;
    }

    @GetMapping("/getClassInfoByCiCode")
    public ESCIInfo  getClassInfoByCiCode(@RequestParam("ciCode")String ciCode) {
        List<ESCIInfo> classInfo = ciSwitchSvc.getCiByCodes(Collections.singletonList(ciCode), null, LibType.DESIGN);
        if (classInfo == null || classInfo.size() == 0) {
            return new ESCIInfo();
        }
        return classInfo.get(0);
    }

    @GetMapping("/getClassInfoById")
    public CcCiClassInfo  getClassInfoById(@RequestParam("id")Long id) {
        CcCiClassInfo ccCiClassInfo = clsSvc.queryClassInfoById(id);
        return ccCiClassInfo;
    }

    @Override
    @GetMapping("/publishBusinessDiagram")
    public String publishBusinessDiagram(@RequestParam("diagramId")String diagramId, @RequestParam(value = "dirId", required = false) Long dirId,
                                          @RequestParam("dirType")Integer dirType) {
        if (StringUtils.isEmpty(diagramId) || dirType == null) {
            return null;
        }
        if (Objects.equals(dirType, Integer.valueOf(DiagramFieldEnum.COMPONENT_MODEL.getSign()))) {
            return bmModuleSvc.releaseModuleDiagram(diagramId, null, dirId, null);
        } else if (Objects.equals(dirType, Integer.valueOf(DiagramFieldEnum.PROCESS_MODEL.getSign()))
                || Objects.equals(dirType, Integer.valueOf(DiagramFieldEnum.DATA_BUILD.getSign()))) {
            eamMergeSvc.modelPush(null, diagramId, null, null);
            return null;
        }
        return null;
    }

    @Override
    @PostMapping("/findDiagramListByDirIds")
    public List<ESDiagram> findDiagramListByDirIds(@RequestParam("dirIds") Set<Long> dirIds) {
        SysUser sysUser = SysUtil.getCurrentUserInfo();
        if (sysUser == null) {
            return null;
        }
        CVcDiagram cVcDiagram = new CVcDiagram();
        cVcDiagram.setDirIds(dirIds.toArray(new Long[0]));
        cVcDiagram.setOwnerCodeEqual(sysUser.getLoginCode());
        cVcDiagram.setStatus(1);
        cVcDiagram.setDataStatus(1);
        return diagramApiClient.queryESDiagramList(sysUser.getDomainId(), cVcDiagram, "");
    }

    @GetMapping("/findAttrDefList")
    public List<ESCIAttrDefInfo> findAttrDefList(@RequestParam("classId") Long classId) {
        return systemDiagramPeer.findAttrDefList(classId);
    }

    @PostMapping("/checkDiagramConflictByDIds")
    public Map<Integer, Object> checkDiagramConflictByDIds(@RequestBody List<String> diagramIds) {
        return bmDiagramPeer.checkDiagramConflictByDIds(diagramIds);
    }

    @PostMapping("/batchPublishDiagram")
    public Map<String, String> batchPublishDiagram(@RequestBody Map<String, Long> publishDirSite) {
        ReleaseModuleDiagramDTO releaseModule = new ReleaseModuleDiagramDTO();
        releaseModule.setPuiblishDirSite(publishDirSite);
        return bmDiagramPeer.batchPublishDiagram(releaseModule);
    }

    /**
     * 获取分类对应的对象定义
     */
    @GetMapping("/findClassDefinitionList")
    public List<ClassDefinitionVO> findClassDefinitionList() {
        return operateSettingService.findClassDefinitionList();
    }

    @GetMapping("/findDiagramFieldList")
    public RemoteResult findDiagramFieldList() {
        List<DiagramFieldVo> diagramFieldList = systemDiagramPeer.findDiagramFieldList();
        return new RemoteResult(diagramFieldList);
    }

    @RequestMapping("/queryHistoryDiagramByIds")
    public Map<String, List<EamReleaseHistoryDTO>> queryHistoryDiagramByIds(@RequestBody List<ReleaseModuleDiagramVO> releaseModuleDiagramVOs) {
        Map<String, List<EamReleaseHistoryDTO>> result = bmDiagramPeer.queryHistoryDiagramByIds(releaseModuleDiagramVOs);
        return result;
    }

    @PostMapping("/findDiagramListByDiagramIds")
    public List<ESDiagram> findDiagramListByDiagramIds(@RequestBody List<String> diagramIds) {
        return bmDiagramPeer.findDiagramListByDiagramIds(diagramIds);
    }

    @PostMapping("/deleteWorkbenchChargeDone")
    public void deleteWorkbenchChargeDone(@RequestBody WorkbenchChargeDoneVO workbenchChargeDoneVO) {
        workbenchChargeDoneSvc.deleteWorkbenchChargeDone(workbenchChargeDoneVO);
    }

    @PostMapping("/findDiagramDirList")
    public List<VcDiagramDir> findDiagramDirList(@RequestBody CVcDiagramDir cdt) {
        SysUser sysUser = SysUtil.getCurrentUserInfo();
        return systemDiagramPeer.findDiagramDirList(cdt, sysUser);
    }

    @PostMapping("/cancelAttention")
    public Integer cancelAttention(@RequestBody EamAttention eamAttention) {
        Integer result = attentionSvc.cancelAttention(eamAttention);
        return result;
    }

    @PostMapping("/cancelRecentlyView")
    public void cancelRecentlyView(@RequestBody EamRecentlyView eamRecentlyView) {
        Long result = recentlyViewSvc.cancelRecentlyView(eamRecentlyView);
    }

    @GetMapping("/findDataSetById")
    public JSONObject findDataSetById(@RequestParam("dataSetId") Long dataSetId) {
        return dataSetApiSvc.findDataSetById(dataSetId);
    }

    @GetMapping("/getResultUsingRuleByDataSetId")
    public List<DataSetExeResultSheetPage> getResultUsingRuleByDataSetId(@RequestParam("dataSetId") Long dataSetId) {
        // 通过id获取数据集
        JSONObject ruleJson = dataSetApiSvc.findDataSetById(dataSetId);
        if(!ruleJson.getInteger("shareLevel").equals(OperateType.Invisible.getCode())){
            throw MessageException.i18n("DCV_BS_OBJ_DATASET_NO_PERMISSION");
        }
        // 通过数据集获取数据
        if (ruleJson != null) {
            Integer limit = 200;
            SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
            ruleJson.put("domainId", currentUserInfo.getDomainId());
            return dataSetApiSvc.getResultUsingRule(ruleJson, limit);
        }
        return Collections.emptyList();
    }

    @GetMapping("/deletePublishMessage")
    public void deletePublishMessage(@RequestParam String businessKey,@RequestParam Integer dirType) {
        workbenchChargeDoneSvc.deleteWorkbenchPublishedChargeDone(businessKey,dirType);
    }

    @Override
    @PostMapping("/batchCancelAttention")
    public Integer batchCancelAttention(@RequestBody CEamAttention eamAttention) {
        return attentionSvc.batchCancelAttention(eamAttention);
    }

    @Override
    @PostMapping("/batchCancelRecentlyView")
    public void batchCancelRecentlyView(CEamRecentlyView eamRecentlyView) {
        recentlyViewSvc.batchCancelRecentlyView(eamRecentlyView);
    }

    /**
     * 查询引用视图（方案中引用视图、视图中关联视图）
     */
    @PostMapping("/queryRelateDiagramList")
    public RemoteResult queryRelateDiagramList(@RequestBody RelateionDiagramVo params) {
        Page<RelateDiagram> page = systemDiagramPeer.queryRelateDiagramList(params);
        return new RemoteResult(page);
    }
}
