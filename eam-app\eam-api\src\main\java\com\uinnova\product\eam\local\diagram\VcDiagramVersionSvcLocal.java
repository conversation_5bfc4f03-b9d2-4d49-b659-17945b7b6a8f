package com.uinnova.product.eam.local.diagram;

import com.uinnova.product.eam.api.diagram.VcDiagramVersionApiClient;
import com.uinnova.product.eam.base.diagram.mix.enums.DiagramQ;
import com.uinnova.product.eam.base.diagram.mix.model.VcDiagramVersionInfo;
import com.uinnova.product.eam.base.diagram.model.CVcDiagramVersion;
import com.uinnova.product.eam.base.diagram.model.VcDiagramVersion;
import com.uinnova.product.eam.model.diagram.VcDiagramInfo;
import com.uinnova.product.eam.service.diagram.VcDiagramVersionSvc;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class VcDiagramVersionSvcLocal implements VcDiagramVersionApiClient {

    @Autowired
    private VcDiagramVersionSvc vcDiagramVersionSvc;

    @Override
    public VcDiagramVersionInfo queryDiagramVersionInfoById(Long domainId, Long id, DiagramQ[] diagramQs) {
        return vcDiagramVersionSvc.queryDiagramVersionInfoById(domainId, id, diagramQs);
    }

    @Override
    public List<VcDiagramVersion> queryVcDiagramVersionList(Long domainId, CVcDiagramVersion cdt, String orders) {
        return vcDiagramVersionSvc.queryVcDiagramVersionList(domainId, cdt, orders);
    }

    @Override
    public Long saveDiagramVersionByDiagramInfo(Long domainId, Long diagramId, VcDiagramInfo record) {
        return vcDiagramVersionSvc.saveDiagramVersionByDiagramInfo(domainId, diagramId, record);
    }

    @Override
    public Integer removeDiagramVersionById(Long domainId, Long id) {
        return vcDiagramVersionSvc.removeDiagramVersionById(domainId, id);
    }

    @Override
    public void saveOrUpdateDiagramVersion(Long domainId, VcDiagramVersion version) {
        vcDiagramVersionSvc.saveOrUpdateDiagramVersion(domainId, version);
    }

    @Override
    public void updateDiagramVersionDescAndVersionNo(Long domainId, Long id, String versionDesc, String versionNo) {
        vcDiagramVersionSvc.updateDiagramVersionDescAndVersionNo(domainId, id, versionDesc, versionNo);
    }
}
