package com.uino.provider.server.web.cmdb.mvc;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.uinnova.product.vmdb.comm.model.ci.CCcCiClassDir;
import com.uino.service.cmdb.microservice.IDirSvc;
import com.uino.bean.cmdb.base.CcCiClassDir;
import com.uino.provider.feign.cmdb.DirFeign;

/**
 * 
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("feign/dir")
public class DirFeignMvc implements DirFeign {

    @Autowired
    private IDirSvc dirSvc;

    @Override
    public Long saveOrUpdateDir(CcCiClassDir dir) {
        return dirSvc.saveOrUpdate(dir);
    }

    @Override
    public Integer removeDirById(Long id) {
        return dirSvc.deleteById(id);
    }

    @Override
    public List<CcCiClassDir> queryDirList(CCcCiClassDir cdt, String orders, Boolean isAsc) {
        return dirSvc.queryDirList(cdt, orders, isAsc);
    }

}
