package com.uinnova.product.eam.base.diagram.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * @Classname
 * @Description 视图的水印信息
 * <AUTHOR>
 * @Date 2021-06-04-14:15
 */
@Data
public class ESWaterMarkInfo {

    @JSONField(name = "text")
    private String text;

    @JSONField(name = "stroke")
    private String stroke;

    @JSONField(name = "translucent")
    private Boolean translucent;

    @JSONField(name = "fontSize")
    private String fontSize;

    @JSONField(name = "fontFamily")
    private String fontFamily;

    @JSONField(name = "deg")
    private Integer deg;

    @JSONField(name = "showWatermark")
    private String showWatermark;
}
