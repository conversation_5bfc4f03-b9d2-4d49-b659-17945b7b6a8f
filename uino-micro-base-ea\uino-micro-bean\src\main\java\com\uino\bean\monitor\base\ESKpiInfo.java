package com.uino.bean.monitor.base;

import java.io.Serializable;
import java.util.List;

import com.binary.framework.bean.annotation.Comment;
import com.uino.bean.sys.enums.DictionaryOptionEnum;

import lombok.Getter;
import lombok.Setter;

/**
 * kpi
 * 
 * <AUTHOR>
 *
 */
@Getter
@Setter

public class ESKpiInfo implements Serializable {
    /**
     * 
     */
    private static final long serialVersionUID = 1L;

    @Comment("ID[ID]")
    private Long id;

    @Comment("KPI名称[KPI_CODE]")
    private String kpiCode;

    @Comment("KPI别名[KPI_NAME]")
    private String kpiName;

    @Comment("服务ID,每一个kpi都不允许重复[SERVICE_ID]")
	private String serviceId;

    @Comment("字典分类id")
    private Long dictClassId;
    //
    // @Comment("所属分类[CLASS_ID]")
    // private Long classId;

    @Comment("关联分类[CLASS_IDS]")
    List<Long> classIds;

    @Comment("数值类型[VAL_TYPE]    数值类型:1=数值 2=字符 3=枚举")
    private Integer valType;

    @Comment("约束[constraint] `0-自动 ; 1-制冷 ; 2-制热 ; 3-除湿 ; 4-送风`的枚举值映射语句，有严格格式要求：<原始值> + \"-\" + <翻译值>，以 \";\" 分隔。")
    private String constraint;

    @Comment("单位ID[UNIT_ID]")
    private Long unitId;

    @Comment("单位名称[UNIT_NAME]")
    private String unitName;

    @Comment("数据阈值[VAL_EXP]")
    private String valExp;

    @Comment("来源[SOURCE_ID]")
    private Long sourceId;

    @Comment("所有者[OWNER_CODE]")
    private String ownerCode;

    @Comment("KPI描述[KPI_DESC]")
    private String kpiDesc;

    @Comment("搜索字段[SEARCH_VALUE]")
    private String searchValue;

	@Comment("KPI扩展属性[KPI_EXT_PROS]    KPI扩展属性 [{val:'',img:''}]")
    private String kpiExtPros;

    @Comment("指标单位转换模板ID[UINIT_CONVERT_MODEL_ID]")
    private Long uinitConvertModelId;

    @Comment("监控系统[MONITOR_SYSTEM]")
    private String monitorSystem;

    @Comment("指标类型[KPI_TYPE] 指标类型:1=数值 2=字符 3=枚举")
    private String kpiType;

    @Comment("指标大类[KPI_CATEGORY]")
    private String kpiCategory;

    @Comment("采集周期[COLLECTION_CYCLE]")
    private String collectionCycle;

    @Comment("采集周期单位: SECOND, MINUTE")
    private String collectionCycleUnit;

    @Comment("阈值条件[EXP_CONDITION]")
    private String expCondition;

    @Comment("操作类型：READ:只读，WRITE:读写")
    private DictionaryOptionEnum option;

    @Comment("所属域[DOMAIN_ID]")
    private Long domainId;

    @Comment("创建人[CREATOR]")
    private String creator;

    @Comment("修改人[MODIFIER]")
    private String modifier;

    @Comment("创建时间[CREATE_TIME]    创建时间:yyyyMMddHHmmss")
    private Long createTime;

    @Comment("修改时间[MODIFY_TIME]    修改时间:yyyyMMddHHmmss")
    private Long modifyTime;

}
