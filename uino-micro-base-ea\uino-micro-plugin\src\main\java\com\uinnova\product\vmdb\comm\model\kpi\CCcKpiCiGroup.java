package com.uinnova.product.vmdb.comm.model.kpi;

import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;

/**
 * 
 * <AUTHOR>
 *
 */
@Comment("KPI对象组关系表[CC_KPI_CI_GROUP]")
public class CCcKpiCiGroup implements Condition {
    private static final long serialVersionUID = 1L;

    @Comment("ID[ID] operate-Equal[=]")
    private Long id;

    @Comment("ID[ID] operate-In[in]")
    private Long[] ids;

    @Comment("ID[ID] operate-GTEqual[>=]")
    private Long startId;

    @Comment("ID[ID] operate-LTEqual[<=]")
    private Long endId;

    @Comment("KPI_ID[KPI_ID] operate-Equal[=]")
    private Long kpiId;

    @Comment("KPI_ID[KPI_ID] operate-In[in]")
    private Long[] kpiIds;

    @Comment("KPI_ID[KPI_ID] operate-GTEqual[>=]")
    private Long startKpiId;

    @Comment("KPI_ID[KPI_ID] operate-LTEqual[<=]")
    private Long endKpiId;

    @Comment("对象组类型[OBJ_GROUP_TYPE] operate-Equal[=]    1=标签 2=CI分类")
    private Integer objGroupType;

    @Comment("对象组类型[OBJ_GROUP_TYPE] operate-In[in]    1=标签 2=CI分类")
    private Integer[] objGroupTypes;

    @Comment("对象组类型[OBJ_GROUP_TYPE] operate-GTEqual[>=]    1=标签 2=CI分类")
    private Integer startObjGroupType;

    @Comment("对象组类型[OBJ_GROUP_TYPE] operate-LTEqual[<=]    1=标签 2=CI分类")
    private Integer endObjGroupType;

    @Comment("对象组ID[OBJ_GROUP_ID] operate-Equal[=]")
    private Long objGroupId;

    @Comment("对象组ID[OBJ_GROUP_ID] operate-In[in]")
    private Long[] objGroupIds;

    @Comment("对象组ID[OBJ_GROUP_ID] operate-GTEqual[>=]")
    private Long startObjGroupId;

    @Comment("对象组ID[OBJ_GROUP_ID] operate-LTEqual[<=]")
    private Long endObjGroupId;

    @Comment("所属域[DOMAIN_ID] operate-Equal[=]")
    private Long domainId;

    @Comment("所属域[DOMAIN_ID] operate-In[in]")
    private Long[] domainIds;

    @Comment("所属域[DOMAIN_ID] operate-GTEqual[>=]")
    private Long startDomainId;

    @Comment("所属域[DOMAIN_ID] operate-LTEqual[<=]")
    private Long endDomainId;

    @Comment("数据状态[DATA_STATUS] operate-Equal[=]    0=删除，1=正常")
    private Integer dataStatus;

    @Comment("数据状态[DATA_STATUS] operate-In[in]    0=删除，1=正常")
    private Integer[] dataStatuss;

    @Comment("数据状态[DATA_STATUS] operate-GTEqual[>=]    0=删除，1=正常")
    private Integer startDataStatus;

    @Comment("数据状态[DATA_STATUS] operate-LTEqual[<=]    0=删除，1=正常")
    private Integer endDataStatus;

    @Comment("创建时间[CREATE_TIME] operate-Equal[=]    yyyyMMddHHmmss")
    private Long createTime;

    @Comment("创建时间[CREATE_TIME] operate-In[in]    yyyyMMddHHmmss")
    private Long[] createTimes;

    @Comment("创建时间[CREATE_TIME] operate-GTEqual[>=]    yyyyMMddHHmmss")
    private Long startCreateTime;

    @Comment("创建时间[CREATE_TIME] operate-LTEqual[<=]    yyyyMMddHHmmss")
    private Long endCreateTime;

    @Comment("更新时间[MODIFY_TIME] operate-Equal[=]    yyyyMMddHHmmss")
    private Long modifyTime;

    @Comment("更新时间[MODIFY_TIME] operate-In[in]    yyyyMMddHHmmss")
    private Long[] modifyTimes;

    @Comment("更新时间[MODIFY_TIME] operate-GTEqual[>=]    yyyyMMddHHmmss")
    private Long startModifyTime;

    @Comment("更新时间[MODIFY_TIME] operate-LTEqual[<=]    yyyyMMddHHmmss")
    private Long endModifyTime;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long[] getIds() {
        return this.ids;
    }

    public void setIds(Long[] ids) {
        this.ids = ids;
    }

    public Long getStartId() {
        return this.startId;
    }

    public void setStartId(Long startId) {
        this.startId = startId;
    }

    public Long getEndId() {
        return this.endId;
    }

    public void setEndId(Long endId) {
        this.endId = endId;
    }

    public Long getKpiId() {
        return this.kpiId;
    }

    public void setKpiId(Long kpiId) {
        this.kpiId = kpiId;
    }

    public Long[] getKpiIds() {
        return this.kpiIds;
    }

    public void setKpiIds(Long[] kpiIds) {
        this.kpiIds = kpiIds;
    }

    public Long getStartKpiId() {
        return this.startKpiId;
    }

    public void setStartKpiId(Long startKpiId) {
        this.startKpiId = startKpiId;
    }

    public Long getEndKpiId() {
        return this.endKpiId;
    }

    public void setEndKpiId(Long endKpiId) {
        this.endKpiId = endKpiId;
    }

    public Integer getObjGroupType() {
        return this.objGroupType;
    }

    public void setObjGroupType(Integer objGroupType) {
        this.objGroupType = objGroupType;
    }

    public Integer[] getObjGroupTypes() {
        return this.objGroupTypes;
    }

    public void setObjGroupTypes(Integer[] objGroupTypes) {
        this.objGroupTypes = objGroupTypes;
    }

    public Integer getStartObjGroupType() {
        return this.startObjGroupType;
    }

    public void setStartObjGroupType(Integer startObjGroupType) {
        this.startObjGroupType = startObjGroupType;
    }

    public Integer getEndObjGroupType() {
        return this.endObjGroupType;
    }

    public void setEndObjGroupType(Integer endObjGroupType) {
        this.endObjGroupType = endObjGroupType;
    }

    public Long getObjGroupId() {
        return this.objGroupId;
    }

    public void setObjGroupId(Long objGroupId) {
        this.objGroupId = objGroupId;
    }

    public Long[] getObjGroupIds() {
        return this.objGroupIds;
    }

    public void setObjGroupIds(Long[] objGroupIds) {
        this.objGroupIds = objGroupIds;
    }

    public Long getStartObjGroupId() {
        return this.startObjGroupId;
    }

    public void setStartObjGroupId(Long startObjGroupId) {
        this.startObjGroupId = startObjGroupId;
    }

    public Long getEndObjGroupId() {
        return this.endObjGroupId;
    }

    public void setEndObjGroupId(Long endObjGroupId) {
        this.endObjGroupId = endObjGroupId;
    }

    public Long getDomainId() {
        return this.domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }

    public Long[] getDomainIds() {
        return this.domainIds;
    }

    public void setDomainIds(Long[] domainIds) {
        this.domainIds = domainIds;
    }

    public Long getStartDomainId() {
        return this.startDomainId;
    }

    public void setStartDomainId(Long startDomainId) {
        this.startDomainId = startDomainId;
    }

    public Long getEndDomainId() {
        return this.endDomainId;
    }

    public void setEndDomainId(Long endDomainId) {
        this.endDomainId = endDomainId;
    }

    public Integer getDataStatus() {
        return this.dataStatus;
    }

    public void setDataStatus(Integer dataStatus) {
        this.dataStatus = dataStatus;
    }

    public Integer[] getDataStatuss() {
        return this.dataStatuss;
    }

    public void setDataStatuss(Integer[] dataStatuss) {
        this.dataStatuss = dataStatuss;
    }

    public Integer getStartDataStatus() {
        return this.startDataStatus;
    }

    public void setStartDataStatus(Integer startDataStatus) {
        this.startDataStatus = startDataStatus;
    }

    public Integer getEndDataStatus() {
        return this.endDataStatus;
    }

    public void setEndDataStatus(Integer endDataStatus) {
        this.endDataStatus = endDataStatus;
    }

    public Long getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long[] getCreateTimes() {
        return this.createTimes;
    }

    public void setCreateTimes(Long[] createTimes) {
        this.createTimes = createTimes;
    }

    public Long getStartCreateTime() {
        return this.startCreateTime;
    }

    public void setStartCreateTime(Long startCreateTime) {
        this.startCreateTime = startCreateTime;
    }

    public Long getEndCreateTime() {
        return this.endCreateTime;
    }

    public void setEndCreateTime(Long endCreateTime) {
        this.endCreateTime = endCreateTime;
    }

    public Long getModifyTime() {
        return this.modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Long[] getModifyTimes() {
        return this.modifyTimes;
    }

    public void setModifyTimes(Long[] modifyTimes) {
        this.modifyTimes = modifyTimes;
    }

    public Long getStartModifyTime() {
        return this.startModifyTime;
    }

    public void setStartModifyTime(Long startModifyTime) {
        this.startModifyTime = startModifyTime;
    }

    public Long getEndModifyTime() {
        return this.endModifyTime;
    }

    public void setEndModifyTime(Long endModifyTime) {
        this.endModifyTime = endModifyTime;
    }

}
