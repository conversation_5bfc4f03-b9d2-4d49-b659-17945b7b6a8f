package com.uino.monitor.performance.impl;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.uino.bean.monitor.buiness.PerformanceLabelQueryDto;
import com.uino.dao.BaseConst;
import org.apache.lucene.queryparser.classic.QueryParser;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.ConstantScoreQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.binary.core.util.BinaryUtils;
import com.binary.jdbc.Page;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.monitor.buiness.PerformanceQueryDto;
import com.uino.bean.tp.base.FinalPerformanceDTO;
import com.uino.dao.cmdb.ESCISvc;
import com.uino.monitor.performance.IUinoPerformanceSvc;
import com.uino.monitor.tp.metric.LastMetricDataSvc;
import com.uino.monitor.tp.metric.MetricDataSvc;
import com.uino.util.cache.ICacheService;

import lombok.extern.log4j.Log4j2;
import org.springframework.util.CollectionUtils;

@Log4j2
@Service
@RefreshScope
public class UinoPerformanceSvc implements IUinoPerformanceSvc {

    @Value("${monitor.select.include.domainId:true}")
    private boolean includeDomainId;
    private static final Gson GSON = new GsonBuilder().create();
    private final ESCISvc esciSvc;
    private final MetricDataSvc metricDataSvc;
    private final LastMetricDataSvc lastMetricDataSvc;
    private final ICacheService iCacheService;
    /**
     * If there is redis, the current performance data will be queried redis, default false
     * <p>
     * Configuration items: {@code redis.enable={@link Boolean}}
     */
    private final Boolean enableRedis;
    /**
     * Maximum number of performance data pages to be queried. The default value is 9999.
     * This parameter can be customized
     * <p>
     * Configuration items: {@code performance.max.page.size={@link Integer}}
     */
    private final Integer maxPageSize;
    /**
     * The performance data is saved in batches. The default value is 200.
     * You can modify the batch quantity by using the following configuration items.
     * <p>
     * Configuration items: {@code performance.save.batch.size={@link Integer}}
     */
    private final Integer batchSize;

    /**
     * Performance data storage method, synchronous or asynchronous, synchronous by default
     * Configuration items: {@code performance.save.method={@link String}}
     */
    private final String saveMethod;

    @Override
    public Boolean saveOrUpdateBatch(List<FinalPerformanceDTO> perfs) {
        boolean res = true;
        try {
            // save history performance
            List<FinalPerformanceDTO> savePerformances = new ArrayList<>(256);
            if (null != perfs && !perfs.isEmpty()) {
                perfs.forEach(perf -> {
                    if (perf.getDomainId() == null) {
                        perf.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
                    }
                    savePerformances.add(perf);
                    if (savePerformances.size() >= batchSize) {
                        // save history performance
                        metricDataSvc.saveMetricDataList(savePerformances);
                        // save current performance
                        this.saveCurrentPerformance(savePerformances);
                        savePerformances.clear();
                    }
                });
            }

            if (!savePerformances.isEmpty()) {
                // save history performance
                metricDataSvc.saveMetricDataList(savePerformances);
                // save current performance
                this.saveCurrentPerformance(savePerformances);
            }
        } catch (Exception e) {
            res = false;
            log.error("save performance error: ", e);
        }
        return res;
    }

    /**
     * save current performance data
     *
     * @param perfList {@link List<FinalPerformanceDTO>}
     */
    private void saveCurrentPerformance(List<FinalPerformanceDTO> perfList) {
        Map<String, Object> saveCurrentPerfMap = new HashMap<>(256);
        perfList.forEach(perf -> {
            String identify = perf.getMetric() + "_" + perf.getCiCode();
            if (!saveCurrentPerfMap.containsKey(identify)) {
                // perf.setId(identify);
                saveCurrentPerfMap.put(identify, perf);
            } else {
                // Duplicate performance data in the same batch, keep the latest data
                FinalPerformanceDTO lastPerformance = (FinalPerformanceDTO) saveCurrentPerfMap.get(identify);
                long lastPerformanceTime = lastPerformance.getTime();
                long currentPerformanceTime = perf.getTime();
                if (currentPerformanceTime > lastPerformanceTime) {
                    saveCurrentPerfMap.put(identify, perf);
                }
            }
        });
        if (!saveCurrentPerfMap.isEmpty()) {
            // save current performance to redis when redis enable
            if (enableRedis) {
                iCacheService.setCacheBatch(saveCurrentPerfMap);
            }

            if ("synchronous".equals(saveMethod)) {
                // synchronization save or update to es
                lastMetricDataSvc.syncSaveOrUpdateBatch(saveCurrentPerfMap);
            } else {
                // asynchronous save or update to es
                lastMetricDataSvc.asyncSaveOrUpdateBatch(saveCurrentPerfMap);
            }
        }
    }

    @Override
    public Boolean saveOrUpdatePerformance(FinalPerformanceDTO perf) {
        return this.saveOrUpdateBatch(Collections.singletonList(perf));
    }

    @Override
    public List<FinalPerformanceDTO> getCurrentPerformanceByCiCodes(Long domainId, List<String> ciCodes) {
        Assert.isTrue(!BinaryUtils.isEmpty(ciCodes), "X_PARAM_NOT_NULL${name:ciCodes}");
        List<FinalPerformanceDTO> perfList;
        if (includeDomainId && domainId == null) {
            domainId = BaseConst.DEFAULT_DOMAIN_ID;
        }
        BoolQueryBuilder perfQuery = QueryBuilders.boolQuery();
        BoolQueryBuilder ciCodeQuery = QueryBuilders.boolQuery();
        ciCodeQuery.should(QueryBuilders.termsQuery("ciCode", ciCodes));
        ciCodeQuery.should(QueryBuilders.termsQuery("ciCode.keyword", ciCodes));
        ciCodeQuery.should(QueryBuilders.termsQuery("ci_name", ciCodes));
        ciCodeQuery.should(QueryBuilders.termsQuery("ci_name.keyword", ciCodes));
        perfQuery.must(ciCodeQuery);
        perfQuery.must(QueryBuilders.termQuery("domainId", domainId));

        perfList = this.getLastPerfListByQuery(domainId, 1, maxPageSize, perfQuery, "time", false).getData();
        return perfList;
    }

    @Override
    public List<FinalPerformanceDTO> getCurrentPerformance(Long domainId, List<String> ciCodes, List<String> kpiCodes) {
        Assert.isTrue(!BinaryUtils.isEmpty(ciCodes), "X_PARAM_NOT_NULL${name:ciCodes}");
        Assert.isTrue(!BinaryUtils.isEmpty(kpiCodes), "X_PARAM_NOT_NULL${name:kpiCodes}");
        List<FinalPerformanceDTO> perfList;
        if (includeDomainId && domainId == null) {
            domainId = BaseConst.DEFAULT_DOMAIN_ID;
        }
        if (enableRedis) {
            List<String> keys = kpiCodes.stream()
                    .flatMap(kpiCode -> ciCodes.stream()
                            .map(ciCode -> kpiCode + "_" + ciCode))
                    .collect(Collectors.toList());
            perfList = iCacheService.listByKeys(keys.toArray(new String[]{}), FinalPerformanceDTO.class);
        } else {
            BoolQueryBuilder query = QueryBuilders.boolQuery();
            query.must(QueryBuilders.termQuery("domainId", domainId));
            BoolQueryBuilder ciCodeQuery = QueryBuilders.boolQuery();
            ciCodeQuery.should(QueryBuilders.termsQuery("ciCode", ciCodes));
            ciCodeQuery.should(QueryBuilders.termsQuery("ciCode.keyword", ciCodes));
            ciCodeQuery.should(QueryBuilders.termsQuery("ci_name", ciCodes));
            ciCodeQuery.should(QueryBuilders.termsQuery("ci_name.keyword", ciCodes));
            query.must(ciCodeQuery);

            BoolQueryBuilder metricQuery = QueryBuilders.boolQuery();
            metricQuery.should(QueryBuilders.termsQuery("metric", kpiCodes));
            metricQuery.should(QueryBuilders.termsQuery("metric.keyword", kpiCodes));
            metricQuery.should(QueryBuilders.termsQuery("kpi_name", kpiCodes));
            metricQuery.should(QueryBuilders.termsQuery("kpi_name.keyword", kpiCodes));
            query.must(metricQuery);

            perfList = this.getLastPerfListByQuery(domainId, 1, maxPageSize, query, "time", false).getData();
        }
        return perfList;
    }

    @Override
    public List<FinalPerformanceDTO> getPerformanceByConditional(Long domainId, String ciCode, String kpiCode, Long startTime, Long endTime) {
        Assert.isTrue(!BinaryUtils.isEmpty(ciCode), "X_PARAM_NOT_NULL${name:ciCode}");
        Assert.isTrue(!BinaryUtils.isEmpty(kpiCode), "X_PARAM_NOT_NULL${name:kpiCode}");
        if (includeDomainId && domainId == null) {
            domainId = BaseConst.DEFAULT_DOMAIN_ID;
        }
        if (startTime == null && endTime == null) {
            return this.getCurrentPerformance(domainId, Collections.singletonList(ciCode), Collections.singletonList(kpiCode));
        } else {
            PerformanceQueryDto queryDto = new PerformanceQueryDto();
            queryDto.setCiCode(ciCode);
            queryDto.setMetric(kpiCode);
            queryDto.setTimeStart(startTime);
            queryDto.setTimeEnd(endTime);
            queryDto.setPageSize(maxPageSize);
            queryDto.setDomainId(domainId);
            return this.queryPerformancePage(queryDto).getData();
        }
    }

    @Override
    public Page<FinalPerformanceDTO> queryPerformancePage(PerformanceQueryDto queryDto) {
        Page<FinalPerformanceDTO> res;
        if (includeDomainId && BinaryUtils.isEmpty(queryDto.getDomainId())) {
            queryDto.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
        }
        int pageNum = queryDto.getPageNum(), pageSize = queryDto.getPageSize();

        BoolQueryBuilder query = QueryBuilders.boolQuery();
        ConstantScoreQueryBuilder constantScoreQuery = QueryBuilders.constantScoreQuery(query);
        // class id
        if (queryDto.getClassId() != null) {
            query.must(QueryBuilders.termQuery("classId", queryDto.getClassId()));
        }
        query.must(QueryBuilders.termQuery("domainId", queryDto.getDomainId()));
        // ci id
        // if (queryDto.getCiId() != null) {
        // query.must(QueryBuilders.termQuery("ciId", queryDto.getCiId()));
        // }

        // ci code
        String ciCode = queryDto.getCiCode();
        if (!BinaryUtils.isEmpty(ciCode)) {
            BoolQueryBuilder ciCodeQuery = QueryBuilders.boolQuery();
            ciCodeQuery.should(QueryBuilders.termQuery("ciCode", ciCode));
            ciCodeQuery.should(QueryBuilders.termQuery("ci_name.keyword", ciCode));
            query.must(ciCodeQuery);
        }

        // time query, By default, the data within one year is queried
        Long timeStart = queryDto.getTimeStart();
        if (timeStart == null) {
            timeStart = LocalDateTime.now().plusYears(-1).toInstant(ZoneOffset.of("+8")).toEpochMilli();
        }

        Long timeEnd = queryDto.getTimeEnd();
        if (timeEnd == null) {
            timeEnd = System.currentTimeMillis();
        }
        query.must(QueryBuilders.rangeQuery("time").gte(timeStart).lte(timeEnd));

        if (!BinaryUtils.isEmpty(queryDto.getKeyword()) && !BinaryUtils.isEmpty(queryDto.getSearchFields())) {
            String words = "*" + QueryParser.escape(queryDto.getKeyword()) + "*";
            BoolQueryBuilder wildQuery = QueryBuilders.boolQuery();
            for (String searchField : queryDto.getSearchFields()) {
                wildQuery.should(QueryBuilders.wildcardQuery(searchField, words));
            }
            query.must(wildQuery);
        }

        // Default sort field
        String order = queryDto.getOrder();
        if (order == null) {
            order = "time";
        }

        // metric query
        if (!BinaryUtils.isEmpty(queryDto.getMetric())) {
            String metric = queryDto.getMetric();
            BoolQueryBuilder metricQuery = QueryBuilders.boolQuery();
            metricQuery.should(QueryBuilders.termQuery("metric", metric));
            metricQuery.should(QueryBuilders.termQuery("kpi_name.keyword", metric));
            query.must(metricQuery);
        }

        // ciPrimaryKey query
        if (!BinaryUtils.isEmpty(queryDto.getCiPrimaryKey())) {
            query.must(QueryBuilders.termQuery("ciPrimaryKey.keyword", queryDto.getCiPrimaryKey()));
        }

        // performance label query (and)
        List<PerformanceLabelQueryDto> andQueryLabels = queryDto.getAndLabels();
        if (!CollectionUtils.isEmpty(andQueryLabels)) {
            BoolQueryBuilder andLabelQueryBuilder = this.performanceLabelQueryBuilder(andQueryLabels, true);
            query.must(andLabelQueryBuilder);
        }

        // performance label query (or)
        List<PerformanceLabelQueryDto> orQueryLabels = queryDto.getOrLabels();
        if (!CollectionUtils.isEmpty(orQueryLabels)) {
            BoolQueryBuilder orLabelQueryBuilder = this.performanceLabelQueryBuilder(orQueryLabels, false);
            query.must(orLabelQueryBuilder);
        }

        // query the current performance data or historical performance data
        if (Boolean.TRUE.equals(queryDto.getLast())) {
            // query current performance
            res = this.queryLastPerformance(queryDto, pageNum, pageSize, order, constantScoreQuery, queryDto.isAsc());
        } else {
            // query history performance
            res = this.getSortListByQuery(queryDto.getDomainId(), pageNum, pageSize, constantScoreQuery, order, timeStart, timeEnd, queryDto.isAsc());
        }
        return res;
    }

    @Override
    public Page<FinalPerformanceDTO> getSortListByQuery(Long domainId, int pageNum, int pageSize, QueryBuilder query, String order, Long timeStart, Long timeEnd, boolean asc) {
        if (includeDomainId && domainId == null) {
            domainId = BaseConst.DEFAULT_DOMAIN_ID;
        }
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(query);
        boolQueryBuilder.filter(QueryBuilders.termQuery("domainId", domainId));
        Page<JSONObject> jsonPage = metricDataSvc.getSortListByQuery(pageNum, pageSize, boolQueryBuilder, BinaryUtils.isEmpty(order) ? "time" : order, timeStart, timeEnd, asc);
        List<FinalPerformanceDTO> performanceList = JSON.parseArray(JSON.toJSONString(jsonPage.getData()), FinalPerformanceDTO.class);
        return new Page<>(jsonPage.getPageNum(), jsonPage.getPageSize(), jsonPage.getTotalRows(), jsonPage.getTotalPages(), performanceList);
    }

    @Override
    public Page<FinalPerformanceDTO> getLastPerfListByQuery(Long domainId, int pageNum, int pageSize, QueryBuilder query, String order, boolean asc) {
        if (includeDomainId && domainId == null) {
            domainId = BaseConst.DEFAULT_DOMAIN_ID;
        }
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.must(QueryBuilders.termQuery("domainId", domainId));
        boolQuery.must(query);
        return lastMetricDataSvc.getSortListByQuery(pageNum, pageSize, boolQuery, order, asc);
    }

    /**
     * query current performance data
     *
     * @param queryDto queryDto
     * @param pageNum  pageNum
     * @param pageSize pageSize
     * @param order    order
     * @param query    queryBuilder
     * @param asc      asc
     */
    private Page<FinalPerformanceDTO> queryLastPerformance(PerformanceQueryDto queryDto, int pageNum, int pageSize, String order, QueryBuilder query, boolean asc) {
        if (BinaryUtils.isEmpty(queryDto.getDomainId())) {
            queryDto.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
        }
        String cacheKey = "";
        if (enableRedis) {
            cacheKey = this.getCacheKey(queryDto);
        }

        if ("".equals(cacheKey)) {
            // from es
            return this.getLastPerfListByQuery(queryDto.getDomainId(), pageNum, pageSize, query, order, asc);
        } else {
            // from redis
            return this.queryLastPerformanceFromCache(cacheKey);
        }
    }

    /**
     * query current performance data from redis
     *
     * @param cacheKey cacheKey
     */
    private Page<FinalPerformanceDTO> queryLastPerformanceFromCache(String cacheKey) {
        ArrayList<FinalPerformanceDTO> finalPerformances = new ArrayList<>(1);
        FinalPerformanceDTO finalPerformance = iCacheService.getCacheByType(cacheKey, FinalPerformanceDTO.class);
        if (null != finalPerformance) {
            finalPerformances.add(finalPerformance);
        }

        Page<FinalPerformanceDTO> performancePage = new Page<>();
        performancePage.setTotalRows(1);
        performancePage.setTotalPages(1);
        performancePage.setPageNum(1);
        performancePage.setPageSize(1);
        performancePage.setData(finalPerformances);
        return performancePage;
    }

    /**
     * generate cache key according to query dto
     *
     * @param queryDto queryDto
     */
    private String getCacheKey(PerformanceQueryDto queryDto) {
        String metric = queryDto.getMetric();
        if (null == metric) {
            return "";
        }
        StringBuilder cacheKey = new StringBuilder(metric).append("_");

        String ciCode = queryDto.getCiCode();
        if (null != ciCode) {
            cacheKey.append(ciCode);
            return cacheKey.toString();
        }

        String ciPrimaryKey = queryDto.getCiPrimaryKey();
        if (null != ciPrimaryKey) {
            List<List<String>> ciPrimaryKeyLists;
            try {
                List<String> ciPrimaryKeyList = GSON.fromJson(ciPrimaryKey, new TypeToken<List<String>>() {
                }.getType());
                ciPrimaryKeyLists = new ArrayList<List<String>>(1) {{
                    add(ciPrimaryKeyList);
                }};
            } catch (Exception e) {
                ciPrimaryKeyLists = GSON.fromJson(ciPrimaryKey, new TypeToken<List<List<String>>>() {
                }.getType());
            }
            List<ESCIInfo> esCiInfos = esciSvc.getCIInfoListByCIPrimaryKeys(queryDto.getDomainId(), ciPrimaryKeyLists);
            if (null != esCiInfos && esCiInfos.size() > 0) {
                ESCIInfo esCiInfo = esCiInfos.get(0);
                String esCiCode = esCiInfo.getCiCode();
                cacheKey.append(esCiCode);
                return cacheKey.toString();
            }
        }
        return "";
    }

    /**
     * performance label query builder rich
     *
     * @param queryLabels  query labels condition
     * @param isAnd condition relation: {@code true is and} / {@code false is or}
     * @return {@link BoolQueryBuilder}
     * */
    private BoolQueryBuilder performanceLabelQueryBuilder( List<PerformanceLabelQueryDto> queryLabels, boolean isAnd) {
        BoolQueryBuilder queryLabelBuilder = QueryBuilders.boolQuery();
        queryLabels.forEach(performanceLabelQueryDto -> {
            String labelKey = performanceLabelQueryDto.getLabelKey();
            String labelValue = performanceLabelQueryDto.getLabelValue();
            Integer operationType = performanceLabelQueryDto.getOperationType();
            if (null != labelKey && null != labelValue && null != operationType) {
                String queryLabelKey = "otherFiledMap." + labelKey.trim();
                String queryLabelValue = labelValue.trim();

                // equals
                if (operationType.equals(1)) {
                    if (isAnd) {
                        queryLabelBuilder.must(QueryBuilders.termQuery(queryLabelKey, queryLabelValue));
                    } else {
                        queryLabelBuilder.should(QueryBuilders.termQuery(queryLabelKey, queryLabelValue));
                    }
                }

                // like
                if (operationType.equals(2)) {
                    queryLabelValue = "*" + queryLabelValue + "*";
                    if (isAnd) {
                        queryLabelBuilder.must(QueryBuilders.wildcardQuery(queryLabelKey, queryLabelValue));
                    } else {
                        queryLabelBuilder.should(QueryBuilders.wildcardQuery(queryLabelKey, queryLabelValue));
                    }
                }
            }
        });
        return queryLabelBuilder;
    }

    @Autowired
    public UinoPerformanceSvc(MetricDataSvc metricDataSvc, LastMetricDataSvc lastMetricDataSvc, ESCISvc esciSvc, @Autowired(required = false) ICacheService iCacheService, @Value("${redis.enable:false}") Boolean enableRedis, @Value("${performance.max.page.size:9999}") Integer maxPageSize, @Value("${performance.save.batch.size:200}") Integer batchSize, @Value("${performance.save.method:synchronous}") String saveMethod) {
        this.metricDataSvc = metricDataSvc;
        this.lastMetricDataSvc = lastMetricDataSvc;
        this.esciSvc = esciSvc;
        this.iCacheService = iCacheService;
        this.enableRedis = enableRedis;
        this.maxPageSize = maxPageSize;
        this.batchSize = batchSize;
        this.saveMethod = saveMethod;
    }
}
