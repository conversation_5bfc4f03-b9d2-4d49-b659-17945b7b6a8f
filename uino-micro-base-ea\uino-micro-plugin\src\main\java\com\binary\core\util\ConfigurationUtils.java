package com.binary.core.util;

import java.io.IOException;
import java.io.InputStream;
import java.util.Iterator;
import java.util.Map.Entry;
import java.util.Properties;

import com.binary.core.bean.BMProxy;
import com.binary.core.exception.ConfigurationException;
import com.binary.core.io.Resource;
import com.binary.core.io.ResourceResolver;

public abstract class ConfigurationUtils {

	

	/**
	 * 加载properties文件, 将配置属性映射到对象上
	 * @param obj 对象
	 * @param prefix 去掉属性前缀, 为实际属性名
	 * @param configPath
	 */
	public static void parseProperties(Object obj, String prefix, String configPath) {
		BinaryUtils.checkEmpty(obj, "obj");
		BinaryUtils.checkEmpty(configPath, "configPath");
		parseProperties(obj, prefix, ResourceResolver.getResource(configPath));
	}
	
	
	
	
	/**
	 * 加载properties文件, 将配置属性映射到对象上
	 * @param obj 对象
	 * @param prefix 去掉属性前缀, 为实际属性名
	 * @param resource
	 */
	public static void parseProperties(Object obj, String prefix, Resource resource) {
		BinaryUtils.checkEmpty(obj, "obj");
		BinaryUtils.checkEmpty(resource, "resource");
		if(prefix!=null) prefix = prefix.trim();
		
		if(resource==null || !resource.exists()) throw new ConfigurationException(" the resource '"+resource.getPath()+"' is not exists! ");
		
		InputStream is = null;
		try {
			is = resource.getInputStream();
			Properties pro = new Properties();
			pro.load(is);
			
			String prefixName = BinaryUtils.isEmpty(prefix) ? "" : (prefix + ".");
			boolean needPrefix = prefixName.length()>0;
			
			BMProxy<?> proxy = BMProxy.getInstance(obj);
			Iterator<Entry<Object, Object>> itor = pro.entrySet().iterator();
			while(itor.hasNext()) {
				Entry<Object, Object> e = itor.next();
				String key = (String)e.getKey();
				Object value = e.getValue();
				
				if((needPrefix && !key.startsWith(prefixName)) || BinaryUtils.isEmpty(value)) continue ;
				key = key.substring(key.indexOf('.')+1);
				if(key.indexOf('.') > 0) {
					key = key.replaceAll("[.]", "");
				}
				
				if(proxy.containsKey(key)) {
					proxy.set(key, value);
				}
			}
		}catch(Exception e) {
			throw new ConfigurationException(e);
		}finally {
			try {
				if(is != null) is.close();
			} catch (IOException e) {
				throw new ConfigurationException(e);
			}
		}	
	}
	
	
	
}
