package com.uino.web.cmdb.mvc;

import com.alibaba.fastjson.JSONObject;
import com.binary.framework.web.RemoteResult;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uinnova.product.vmdb.comm.doc.annotation.MvcDesc;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uino.api.client.cmdb.ICiRltAutoBuildApiSvc;
import com.uino.bean.cmdb.RltAutoBuildDTO;
import com.uino.bean.cmdb.base.ESCiRltAutoBuild;
import com.uino.bean.permission.base.SysUser;
import com.uino.init.api.ApiResult;
import com.uino.init.api.ApiVersion;
import com.uino.util.sys.SysUtil;
import com.uino.web.BaseMvc;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Classname CiRltAutoBuildMvc
 * @Description 关系自动构建规则
 * @Date 2020/6/29 16:24
 * <AUTHOR> sh
 */
@ApiVersion(1)
@Api(value = "关系自动构建", tags = {"关系管理"})
@RestController
@RequestMapping("/cmdb/ciRltAutoBuild")
@MvcDesc(author = "sh", desc = "关系自动构建")
public class CiRltAutoBuildMvc extends BaseMvc {

    @Autowired
    private ICiRltAutoBuildApiSvc iCiRltAutoBuildApiSvc;

    @ApiOperation(value="添加默认规则")
    @ModDesc(desc = "添加默认规则", pDesc = "", pType = JSONObject.class, rDesc = "", rType = CcCiInfo.class)
    @RequestMapping(value="/saveDefault",method= RequestMethod.POST)
    public ApiResult<Long> saveDefault(@RequestBody JSONObject body) {
        Long sourceCiClassId = body.getLong("sourceCiClassId");
        Long targetCiClassId = body.getLong("targetCiClassId");
        Long rltClassId = body.getLong("rltClassId");
        Long visualModelId = body.getLong("visualModelId");
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        ESCiRltAutoBuild esCiRltAutoBuild = iCiRltAutoBuildApiSvc.saveDefault(currentUserInfo.getDomainId(), sourceCiClassId, targetCiClassId, rltClassId, visualModelId);
        Long id=esCiRltAutoBuild.getId();
        return ApiResult.ok(this).data(id);

    }

    @ApiOperation(value="修改规则")
    @ModDesc(desc = "修改规则", pDesc = "", pType = JSONObject.class, rDesc = "", rType = CcCiInfo.class)
    @RequestMapping(value="/updateCiRltAutoBuild",method=RequestMethod.POST)
    public ApiResult<Long> updateCiRltAutoBuild(@RequestBody JSONObject body) {
        ESCiRltAutoBuild esCiRltAutoBuild = new ESCiRltAutoBuild(body);
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        esCiRltAutoBuild.setDomainId(currentUserInfo.getDomainId());
        Long id = iCiRltAutoBuildApiSvc.updateCiRltAutoBuild(esCiRltAutoBuild);
        return ApiResult.ok(this).data(id);
    }

    /**
     * 根据关系id查询当前关系的构建规则列表
     * @param body
     * @return
     */
    @ApiOperation(value="根据关系id查询当前关系的构建规则列表")
    @GetMapping("/getAutoBuildListByRltId")
    public RemoteResult getAutoBuildListByRltId(Long rltId){
        Assert.notNull(rltId, "关系id不能为空");
        return new RemoteResult(iCiRltAutoBuildApiSvc.getAutoBuildListByRltId(rltId));
    }
    
    /**
     * 新增或保存关系规则
     * @param esCiRltAutoBuild
     * @return
     */
    @ApiOperation(value="新增或保存关系规则")
    @PostMapping("/saveCiRltAutoBuild")
    public ApiResult<Long> saveOrUpdateCiRltAutoBuild(@RequestBody ESCiRltAutoBuild esCiRltAutoBuild) {
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        esCiRltAutoBuild.setDomainId(currentUserInfo.getDomainId());
        Long id = null;
        if(esCiRltAutoBuild.getId()!=null){
            id = iCiRltAutoBuildApiSvc.updateCiRltAutoBuild(esCiRltAutoBuild);
        }else{
            id = iCiRltAutoBuildApiSvc.saveCiRltAutoBuild(esCiRltAutoBuild);
        }
        return ApiResult.ok(this).data(id);
    }

    @ApiOperation(value="查看规则")
    @ModDesc(desc = "查看规则", pDesc = "", pType = JSONObject.class, rDesc = "", rType = CcCiInfo.class)
    @RequestMapping(value="/findCiRltAutoBuild",method=RequestMethod.POST)
    public ApiResult<ESCiRltAutoBuild> findCiRltAutoBuild(@RequestBody JSONObject body) {
        Long sourceCiClassId = body.getLong("sourceCiClassId");
        Long targetCiClassId = body.getLong("targetCiClassId");
        Long rltClassId = body.getLong("rltClassId");
        Long visualModelId = body.getLong("visualModelId");
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        ESCiRltAutoBuild ciRltAutoBuild = iCiRltAutoBuildApiSvc.findCiRltAutoBuild(currentUserInfo.getDomainId(), sourceCiClassId, targetCiClassId, rltClassId, visualModelId);
        return ApiResult.ok(this).data(ciRltAutoBuild);

    }

    @ApiOperation(value="删除规则")
    @ModDesc(desc = "删除规则", pDesc = "", pType = JSONObject.class, rDesc = "", rType = CcCiInfo.class)
    @RequestMapping(value="/deleteCiRltAutoBuild",method = RequestMethod.POST)
    public ApiResult<Boolean> deleteCiRltAutoBuild(@RequestBody JSONObject body) {
        Long sourceCiClassId = body.getLong("sourceCiClassId");
        Long targetCiClassId = body.getLong("targetCiClassId");
        Long rltClassId = body.getLong("rltClassId");
        Long visualModelId = body.getLong("visualModelId");
        iCiRltAutoBuildApiSvc.deleteCiRltAutoBuild(sourceCiClassId, targetCiClassId, rltClassId, visualModelId);
        return ApiResult.ok(this).data(true);
    }
    @ApiOperation(value="根据对象分类批量删除构建关系")
    @PostMapping("/deleteCiRltAutoBuildBatchByCiClassIds")
    public ApiResult<Boolean> deleteCiRltAutoBuildBatchByCiClassIds(@RequestBody RltAutoBuildDTO rltAutoBuildDTO){
        iCiRltAutoBuildApiSvc.deleteCiRltAutoBuildBatchByCiClassIds(rltAutoBuildDTO.getCiClassIds(),rltAutoBuildDTO.getVisualModelId());
        return ApiResult.ok(this).data(true);
    }

    @ApiOperation(value="删除规则")
    @ModDesc(desc = "删除规则", pDesc = "", pType = JSONObject.class, rDesc = "", rType = CcCiInfo.class)
    @RequestMapping(value="/deleteCiRltAutoBuildById",method = RequestMethod.POST)
    public ApiResult<Boolean> deleteCiRltAutoBuild(@RequestBody ESCiRltAutoBuild esCiRltAutoBuild) {
        if (esCiRltAutoBuild.getId() != null) {
            iCiRltAutoBuildApiSvc.deleteById(esCiRltAutoBuild.getId());
        }
        return ApiResult.ok(this).data(true);
    }

    @ApiOperation(value="执行所有")
    @ModDesc(desc = "执行所有", pDesc = "", pType = JSONObject.class, rDesc = "", rType = CcCiInfo.class)
    @RequestMapping(value="/runCiRltAutoBuildAll",method = RequestMethod.POST)
    public ApiResult<Boolean> runCiRltAutoBuildAll() {
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        iCiRltAutoBuildApiSvc.runCiRltAutoBuildAll(currentUserInfo.getDomainId());
        return ApiResult.ok(this).data(true);
    }

    @ApiOperation(value="根据构建规则id构建数据")
    @ModDesc(desc = "根据构建规则id构建数据", pDesc = "规则id", pType = JSONObject.class, rDesc = "true:成功", rType = Boolean.class)
    @RequestMapping(value="/runCiRltAutoBuildById",method = RequestMethod.POST)
    public ApiResult<Boolean> runCiRltAutoBuildById(@RequestBody JSONObject body) {
        Long id = body.getLong("id");
        boolean b = iCiRltAutoBuildApiSvc.runCiRltAutoBuildById(id);
        return ApiResult.ok(this).data(b);
    }

    @ApiOperation(value="根据关系id查询构建规则列表")
    @PostMapping("/getAutoBuildListByRltClassId")
    public RemoteResult getAutoBuildListByRltClassId(@RequestBody List<Long> rltClassIds){
        if(CollectionUtils.isEmpty(rltClassIds)){
            return new RemoteResult("查询关系id不能为空");
        }
        List<ESCiRltAutoBuild> autoBuildList = iCiRltAutoBuildApiSvc.getAutoBuildListByRltClassId(rltClassIds);
        return new RemoteResult(autoBuildList);
    }

    @ApiOperation(value="根据关系id查询构建规则列表")
    @PostMapping("/runCiRltAutoBuildByIds")
    public RemoteResult runCiRltAutoBuildByIds(@RequestBody List<Long> ids){
        iCiRltAutoBuildApiSvc.runCiRltAutoBuildByIds(ids);
        return new RemoteResult("构建完毕");
    }

}
