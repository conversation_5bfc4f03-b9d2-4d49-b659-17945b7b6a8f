package com.uinnova.product.eam.service.cj.service;


import com.uinnova.product.eam.model.cj.domain.CJResource;
import com.uinnova.product.eam.model.cj.domain.ChapterResource;
import com.uinnova.product.eam.model.cj.vo.ContextFileVo;
import com.uino.bean.permission.base.SysUser;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface CJResourceDaoService {
    List<CJResource> upload(MultipartFile[] files, SysUser currentUserInfo);

    List<ChapterResource> uploadChapterFile(MultipartFile[] files, SysUser currentUserInfo, Long templateId, Long chapterId);

    Boolean checkUploadFileCount(Long templateId, Long chapterId);

    void deleteChapterFile(Long id);

    List<ChapterResource> getPlanResource(Long templateId, Long chapterTemplateId);

    ContextFileVo uploadContextFile(MultipartFile file);

    void ossTest(MultipartFile file, String objectKey);
}
