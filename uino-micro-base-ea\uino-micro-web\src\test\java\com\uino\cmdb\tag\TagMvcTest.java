package com.uino.cmdb.tag;

import static org.junit.Assert.assertEquals;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import com.alibaba.fastjson.JSONObject;
import com.binary.json.JSON;
import com.uino.StartBaseWebAppliaction;
import com.uino.bean.cmdb.base.ESCITagInfo;
import com.uino.bean.cmdb.base.ESTagRuleInfo;
import com.uino.bean.cmdb.base.ESTagRuleItem;
import com.uino.bean.cmdb.base.ESTagRuleItemGroup;
import com.uino.bean.cmdb.query.ESTagSearchBean;

@RunWith(SpringRunner.class)
@WebAppConfiguration
@SpringBootTest(classes = {StartBaseWebAppliaction.class})
@ActiveProfiles("provider-local")
@AutoConfigureMockMvc(addFilters = false)
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class TagMvcTest {

    @Autowired
    private MockMvc mockMvc;

    private String token = "ca14ed042fe7912426b484f6ea5c754b4fb51ae4a72037e9127da51743e0d1f9650e6e78ed4466ac970acb6a0b2f46fd26958c4ab2d115cc71b34ca8a02db24c";

    private static Long tagId;

    @Test
    public void atestGetTagTree() throws Exception {
        ResultActions reaction = this.mockMvc.perform(MockMvcRequestBuilders.post("/cmdb/tag/getTagTree").contentType(MediaType.APPLICATION_JSON).accept(MediaType.APPLICATION_JSON)// 返回值接收json
            .header("Content-Type", "application/json").header("token", token));
        MvcResult mvcResult = reaction.andReturn();
        int status = mvcResult.getResponse().getStatus();
        assertEquals(200, status);
    }

    @Test
    public void btestSaveOrUpdate() throws Exception {
        List<ESTagRuleItem> items = new ArrayList<>();
        items.add(ESTagRuleItem.builder().classAttrId(123L).ruleOp(1).ruleVal("test").build());

        List<ESTagRuleItemGroup> itemGroups = new ArrayList<>();
        itemGroups.add(ESTagRuleItemGroup.builder().logicOp(1).items(items).build());

        List<ESTagRuleInfo> rules = new ArrayList<ESTagRuleInfo>();
        rules.add(ESTagRuleInfo.builder().classId(1L).itemGroups(itemGroups).build());

        ESCITagInfo param = new ESCITagInfo();
        param.setTagName("tagName");
        param.setDirId(1L);
        param.setRules(rules);

        // param.setRules(rules);
        String requestJson = JSONObject.toJSONString(param);
        ResultActions reaction =
            this.mockMvc.perform(MockMvcRequestBuilders.post("/cmdb/tag/saveOrUpdate").contentType(MediaType.APPLICATION_JSON).content(requestJson).accept(MediaType.APPLICATION_JSON)// 返回值接收json
                .header("Content-Type", "application/json").header("token", token));
        MvcResult mvcResult = reaction.andReturn();
        String resStr = mvcResult.getResponse().getContentAsString();
        String tagIdStr = JSON.toString(JSON.toObject(resStr, Map.class).get("data"));
        tagId = JSON.toObject(tagIdStr, Long.class);
        int status = mvcResult.getResponse().getStatus();
        assertEquals(200, status);
    }

    @Test
    public void ctestGetTagRuleById() throws Exception {
        String requestJson = JSONObject.toJSONString(tagId == null ? 1L : null);
        ResultActions reaction =
            this.mockMvc.perform(MockMvcRequestBuilders.post("/cmdb/tag/getTagRuleById").contentType(MediaType.APPLICATION_JSON).content(requestJson).accept(MediaType.APPLICATION_JSON)// 返回值接收json
            .header("Content-Type", "application/json").header("token", token));
        MvcResult mvcResult = reaction.andReturn();
        int status = mvcResult.getResponse().getStatus();
        assertEquals(200, status);
    }

    @Test
    public void dtestGetCIInfoListByTagId() throws Exception {
        ESCITagInfo tag = new ESCITagInfo();
        List<ESTagRuleItem> items = new ArrayList<>();
        items.add(ESTagRuleItem.builder().classAttrId(123L).ruleOp(1).ruleVal("test").build());

        List<ESTagRuleItemGroup> itemGroups = new ArrayList<>();
        itemGroups.add(ESTagRuleItemGroup.builder().logicOp(1).items(items).build());

        List<ESTagRuleInfo> rules = new ArrayList<ESTagRuleInfo>();
        rules.add(ESTagRuleInfo.builder().classId(1L).itemGroups(itemGroups).build());
        tag.setRules(rules);
        ESTagSearchBean bean = new ESTagSearchBean();
        bean.setTagInfo(tag);
        String requestJson = JSONObject.toJSONString(bean);
        ResultActions reaction =
            this.mockMvc.perform(MockMvcRequestBuilders.post("/cmdb/tag/getCIInfoListByTag").contentType(MediaType.APPLICATION_JSON).content(requestJson).accept(MediaType.APPLICATION_JSON)// 返回值接收json
                .header("Content-Type", "application/json").header("token", token));
        MvcResult mvcResult = reaction.andReturn();
        int status = mvcResult.getResponse().getStatus();
        assertEquals(200, status);
    }

    @Test
    public void etestDeleteById() throws Exception {
        String requestJson = JSONObject.toJSONString(tagId == null ? 1L : tagId);
        ResultActions reaction =
            this.mockMvc.perform(MockMvcRequestBuilders.post("/cmdb/tag/deleteById").contentType(MediaType.APPLICATION_JSON).content(requestJson).accept(MediaType.APPLICATION_JSON)// 返回值接收json
                .header("Content-Type", "application/json").header("token", token));
        MvcResult mvcResult = reaction.andReturn();
        String resStr = mvcResult.getResponse().getContentAsString();
        String reStr = JSON.toString(JSON.toObject(resStr, Map.class).get("data"));
        Integer res = JSON.toObject(reStr, Integer.class);
        assertEquals(1, res.intValue());
        int status = mvcResult.getResponse().getStatus();
        assertEquals(200, status);
    }

}
