package com.uinnova.product.eam.comm.model.es;

import com.binary.framework.bean.Condition;
import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;
import lombok.Data;


@Comment("视图关联系统表[UINO_EAM_DIAGRAM_RELATION_SYS]")
@Data
public class EamDiagramRelationSys implements EntityBean,Condition {
	private static final long serialVersionUID = 1L;


	@Comment("ID[ID]")
	private Long id;


	@Comment("视图编码")
	private String diagramEnergy;


	@Comment("文件夹id")
	private Long dirId;


	@Comment("系统id")
	private String esSysId;

	@Comment("删除标志 true:删除 false:未删除")
	private Boolean delFlag;

	@Comment("创建人[CREATOR]")
	private String creator;


	@Comment("修改人[MODIFIER]")
	private String modifier;


	@Comment("创建时间[CREATE_TIME]    创建时间:yyyyMMddHHmmss")
	private Long createTime;


	@Comment("更新时间[MODIFY_TIME]    更新时间:yyyyMMddHHmmss")
	private Long modifyTime;

	@Comment("视图类型[MODIFY_TIME] ")
	private String diagramClassType;

	@Override
	public Long getId() {
		return id;
	}

	@Override
	public void setId(Long id) {
		this.id = id;
	}

	public String getCreator() {
		return this.creator;
	}
	public void setCreator(String creator) {
		this.creator = creator;
	}


	public String getModifier() {
		return this.modifier;
	}
	public void setModifier(String modifier) {
		this.modifier = modifier;
	}


	public Long getCreateTime() {
		return this.createTime;
	}
	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}


	public Long getModifyTime() {
		return this.modifyTime;
	}
	public void setModifyTime(Long modifyTime) {
		this.modifyTime = modifyTime;
	}

	public String getEsSysId() {
		return esSysId;
	}

	public void setEsSysId(String esSysId) {
		this.esSysId = esSysId;
	}

}


