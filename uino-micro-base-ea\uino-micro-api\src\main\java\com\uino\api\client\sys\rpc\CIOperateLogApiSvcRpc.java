package com.uino.api.client.sys.rpc;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.binary.jdbc.Page;
import com.uino.bean.sys.base.ESCIOperateLog;
import com.uino.bean.sys.query.ESCIOperateLogSearchBean;
import com.uino.provider.feign.sys.CIOperateLogFeign;
import com.uino.api.client.sys.ICIOperateLogApiSvc;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
public class CIOperateLogApiSvcRpc implements ICIOperateLogApiSvc {

    @Autowired
    private CIOperateLogFeign ciLogFeign;
    @Override
    public Page<ESCIOperateLog> getCIOperateLogPageByCdt(ESCIOperateLogSearchBean bean) {
        return ciLogFeign.getCIOperateLogPageByCdt(bean);
    }

    @Override
    public Integer clearCIOperateLogByDuration(Integer clearLogDuration) {
        return ciLogFeign.clearCIOperateLogByDuration(clearLogDuration);
    }

}
