package com.uinnova.product.eam.web.bm.mvc;


import com.binary.framework.web.RemoteResult;
import com.uinnova.product.eam.model.asset.DiagramContrastParam;
import com.uinnova.product.eam.model.asset.DiagramContrastVO;
import com.uinnova.product.eam.web.bm.peer.DiagramContrastPeer;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.util.Asserts;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;

@RestController
@RequestMapping("/diagram/contra")
@Slf4j
public class DiagramContrastMvc {

    @Resource
    DiagramContrastPeer diagramContrastPeer;


    @PostMapping("ciAndRlt")
    public RemoteResult diagramContras(@RequestBody DiagramContrastParam param) {
        Asserts.notNull(param.getSourceDiagramId(), "源端视图标识不能为空");
        Asserts.notNull(param.getTargetDiagramId(), "目标端视图标识不能为空");
        DiagramContrastVO result = diagramContrastPeer.diagramContras(param);
        return new RemoteResult(result);
    }

    @PostMapping("export")
    public ResponseEntity<byte[]> export(@RequestBody DiagramContrastParam param) {
        Asserts.notNull(param.getSourceDiagramId(), "源端视图标识不能为空");
        Asserts.notNull(param.getTargetDiagramId(), "目标端视图标识不能为空");
        return diagramContrastPeer.export(param);
    }

}



