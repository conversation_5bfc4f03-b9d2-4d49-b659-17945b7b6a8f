package com.uino.provider.server.web.cmdb.mvc;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.uino.service.cmdb.microservice.IVisualModelSvc;
import com.uino.bean.cmdb.base.ESVisualModel;
import com.uino.provider.feign.cmdb.VisualModelFeign;

/**
 * 
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("feign/visualModel")
public class VisualModelFeignMvc implements VisualModelFeign {

	@Autowired
	private IVisualModelSvc svc;
	
	@Override
	public List<ESVisualModel> queryVisualModels(Long domainId) {
		return svc.queryVisualModels(domainId);
	}

	@Override
	public Long saveVisualModel(ESVisualModel model) {
		return svc.saveVisualModel(model);
	}
	
	@Override
	public void updateVisualModelName(ESVisualModel model) {
		svc.updateVisualModelName(model);
	}

	@Override
	public void deleteVisualModel(Long id) {
		svc.deleteVisualModel(id);
	}

}
