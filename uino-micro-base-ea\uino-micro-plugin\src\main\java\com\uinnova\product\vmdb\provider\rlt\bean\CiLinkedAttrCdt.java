package com.uinnova.product.vmdb.provider.rlt.bean;

import com.binary.framework.bean.annotation.Comment;

import java.io.Serializable;

@Comment("简单的关系分类或CI分类属性判断条件")
public class CiLinkedAttrCdt implements Serializable{

	private static final long serialVersionUID = 1L;
	
	@Comment("分类id")
	private Long classId;
	
	@Comment("分类属性的id")
	private Long attrId;
	
	@Comment("操作符代码 comm下RuleOp类")
	private Integer cdtOp;
		
	@Comment("属性值")
	private String cdtVal;

	public Long getClassId() {
		return classId;
	}

	public void setClassId(Long classId) {
		this.classId = classId;
	}

	public Long getAttrId() {
		return attrId;
	}

	public void setAttrId(Long attrId) {
		this.attrId = attrId;
	}

	public Integer getCdtOp() {
		return cdtOp;
	}

	public void setCdtOp(Integer cdtOp) {
		this.cdtOp = cdtOp;
	}

	public String getCdtVal() {
		return cdtVal;
	}

	public void setCdtVal(String cdtVal) {
		this.cdtVal = cdtVal;
	}
 
	
}
