package com.uinnova.product.vmdb.comm.model.quality;

import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;

/**
 * 
 * <AUTHOR>
 *
 */
@Comment("CI数据质量规则表[CC_CI_QUALITY_RULE]")
public class CcCiQualityRule implements EntityBean {
    private static final long serialVersionUID = 1L;

    @Comment("ID[ID]")
    private Long id;

    @Comment("规则名称[RULE_NAME]")
    private String ruleName;

    @Comment("规则描述[RULE_DESC]")
    private String ruleDesc;

    @Comment("规则类型[RULE_TYPE]    规则类型:10=完整性 11=合规性 12=准确性")
    private Integer ruleType;

    @Comment("规则子类型[RULE_SUB_TYPE]    规则子类型:1001=完整性 1101=合规性 1201=准确性过期 1202=准确性孤儿")
    private Integer ruleSubType;

    @Comment("筛选标签[TAG_ID]")
    private Long tagId;

    @Comment("激活状态[STATUS]    激活状态:0=未激活 1=已激活")
    private Integer status;

    @Comment("有效状态[USE_STATUS]    有效状态:0=无效 1=有效")
    private Integer useStatus;

    @Comment("构建状态[BUILD_STATUS]    构建状态:1=待构建 2=构建中")
    private Integer buildStatus;

    @Comment("最后构建时间[LAST_BUILD_TIME]")
    private Long lastBuildTime;

    @Comment("无效说明[VALID_ERR_MSG]")
    private String validErrMsg;

    @Comment("所属域[DOMAIN_ID]")
    private Long domainId;

    @Comment("数据状态[DATA_STATUS]    数据状态:0=删除，1=正常")
    private Integer dataStatus;

    @Comment("创建人[CREATOR]")
    private String creator;

    @Comment("修改人[MODIFIER]")
    private String modifier;

    @Comment("创建时间[CREATE_TIME]    创建时间:yyyyMMddHHmmss")
    private Long createTime;

    @Comment("修改时间[MODIFY_TIME]    修改时间:yyyyMMddHHmmss")
    private Long modifyTime;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getRuleName() {
        return this.ruleName;
    }

    public void setRuleName(String ruleName) {
        this.ruleName = ruleName;
    }

    public String getRuleDesc() {
        return this.ruleDesc;
    }

    public void setRuleDesc(String ruleDesc) {
        this.ruleDesc = ruleDesc;
    }

    public Integer getRuleType() {
        return this.ruleType;
    }

    public void setRuleType(Integer ruleType) {
        this.ruleType = ruleType;
    }

    public Integer getRuleSubType() {
        return this.ruleSubType;
    }

    public void setRuleSubType(Integer ruleSubType) {
        this.ruleSubType = ruleSubType;
    }

    public Long getTagId() {
        return this.tagId;
    }

    public void setTagId(Long tagId) {
        this.tagId = tagId;
    }

    public Integer getStatus() {
        return this.status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getUseStatus() {
        return this.useStatus;
    }

    public void setUseStatus(Integer useStatus) {
        this.useStatus = useStatus;
    }

    public Integer getBuildStatus() {
        return this.buildStatus;
    }

    public void setBuildStatus(Integer buildStatus) {
        this.buildStatus = buildStatus;
    }

    public Long getLastBuildTime() {
        return this.lastBuildTime;
    }

    public void setLastBuildTime(Long lastBuildTime) {
        this.lastBuildTime = lastBuildTime;
    }

    public String getValidErrMsg() {
        return this.validErrMsg;
    }

    public void setValidErrMsg(String validErrMsg) {
        this.validErrMsg = validErrMsg;
    }

    public Long getDomainId() {
        return this.domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }

    public Integer getDataStatus() {
        return this.dataStatus;
    }

    public void setDataStatus(Integer dataStatus) {
        this.dataStatus = dataStatus;
    }

    public String getCreator() {
        return this.creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getModifier() {
        return this.modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public Long getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getModifyTime() {
        return this.modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }

}
