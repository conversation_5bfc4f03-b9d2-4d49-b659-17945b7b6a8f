package com.uino.api.client.monitor;

import java.util.List;

import org.elasticsearch.index.query.QueryBuilder;

import com.binary.jdbc.Page;
import com.uino.bean.monitor.buiness.PerformanceQueryDto;
import com.uino.bean.tp.base.FinalPerformanceDTO;

/**
 * 公共组件-通用指标数据操作接口
 * 
 * <AUTHOR>
 * @Date 2021-07-01
 */
public interface IUinoPerformanceApiSvc {
	/**
	 * 保存指标数据<br>
	 * 直接执行入库操作，不再校验指标各项属性信息
	 * 
	 * @param perf
	 * @return
	 */
	public Boolean saveOrUpdatePerformance(FinalPerformanceDTO perf);

	/**
	 * 分页查询指标数据
	 * 
	 * @param queryDto
	 * @return
	 */
	public Page<FinalPerformanceDTO> queryPerformancePage(PerformanceQueryDto queryDto);

	/**
	 * 批量保存指标数据<br>
	 * 直接执行入库操作，不再校验指标各项属性信息
	 * 
	 * @param perfs
	 * @return
	 */
	public Boolean saveOrUpdateBatch(List<FinalPerformanceDTO> perfs);

	/**
	 * 条件查询指标数据
	 * 
	 * @param pageNum
	 * @param pageSize
	 * @param query
	 * @param order
	 * @param timeStart
	 * @param timeEnd
	 * @param asc
	 * @return
	 */
	public Page<FinalPerformanceDTO> getSortListByQuery(int pageNum, int pageSize, QueryBuilder query, String order,
			Long timeStart, Long timeEnd, boolean asc);
	public Page<FinalPerformanceDTO> getSortListByQuery(Long domainId ,int pageNum, int pageSize,QueryBuilder query, String order,
														Long timeStart, Long timeEnd, boolean asc);


	/**
	 * 获取当前最新一条指标数据
	 * 
	 * @param pageNum
	 * @param pageSize
	 * @param query
	 * @param order
	 * @param asc
	 * @return
	 */
	public Page<FinalPerformanceDTO> getLastPerfListByQuery(int pageNum, int pageSize, QueryBuilder query, String order,
			boolean asc);

	public Page<FinalPerformanceDTO> getLastPerfListByQuery(Long domainId,int pageNum, int pageSize, QueryBuilder query, String order,
															boolean asc);

	/**
	 * Get current performance data
	 *
	 * @param ciCodes
	 *            ciCodes
	 * @param kpiCodes
	 *            kpiCodes
	 * @return {@link List<FinalPerformanceDTO>}
	 */
	List<FinalPerformanceDTO> getCurrentPerformance(List<String> ciCodes, List<String> kpiCodes);
	List<FinalPerformanceDTO> getCurrentPerformance(Long domainId,List<String> ciCodes, List<String> kpiCodes);

	/**
	 * Get current performance data,
	 * This method can only query real-time performance data from {@code ES}
	 *
	 * @param ciCodes  ciCodes
	 * @return {@link List<FinalPerformanceDTO>}
	 */
	List<FinalPerformanceDTO> getCurrentPerformanceByCiCodes(List<String> ciCodes);
	List<FinalPerformanceDTO> getCurrentPerformanceByCiCodes(Long domainId,List<String> ciCodes);

	/**
	 * Get current performance data
	 *
	 * @param ciCode
	 *            ciCode
	 * @param kpiCode
	 *            kpiCode
	 * @param startTime
	 *            start time
	 * @param endTime
	 *            end time
	 * @return {@link List<FinalPerformanceDTO>}
	 */
	List<FinalPerformanceDTO> getPerformanceByConditional(String ciCode, String kpiCode, Long startTime, Long endTime);

	List<FinalPerformanceDTO> getPerformanceByConditional(Long domainId,String ciCode, String kpiCode, Long startTime, Long endTime);
}
