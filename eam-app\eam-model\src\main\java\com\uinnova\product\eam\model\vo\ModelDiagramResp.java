package com.uinnova.product.eam.model.vo;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

/**
 * 创建模型视图返回
 * <AUTHOR>
 */
@Data
public class ModelDiagramResp {
    @Comment("视图id")
    private String diagramId;
    @Comment("父文件夹id")
    private Long parentDirCode;
    @Comment("父级视图id")
    private String parentDiagramId;
    @Comment("ciId")
    private long attachCiId;
    @Comment("ciCode")
    private String attachCiCode;
    @Comment("异常信息提示")
    private String message;
}
