package com.uinnova.product.eam.web.config.mvc;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import jakarta.servlet.*;
import jakarta.servlet.annotation.WebFilter;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * description
 *
 * <AUTHOR>
 * @since 2023/3/3 9:25
 */
@WebFilter(urlPatterns = "/*")
@Component
@Order(1000)
@Slf4j
@ConditionalOnProperty(name = "monet.login.loginMethod", havingValue = "xinwang")
public class XinwangRedirectFilter implements Filter {
    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        HttpServletRequest request = (HttpServletRequest) servletRequest;
        HttpServletResponse response = (HttpServletResponse) servletResponse;
        String quickeaHeader = request.getHeader("REQUEST_HEADER");
        if(StringUtils.isBlank(quickeaHeader)){
            //重定向到项目首页
            response.sendRedirect("/application-square");
            log.info("重定向");
        }else {
            filterChain.doFilter(servletRequest,servletResponse);
        }
    }
}
