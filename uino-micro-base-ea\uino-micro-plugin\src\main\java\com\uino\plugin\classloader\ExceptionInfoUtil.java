package com.uino.plugin.classloader;

import java.io.ByteArrayOutputStream;
import java.io.PrintStream;

/**
 * <AUTHOR>
 * @Description
 * @Date 2021/9/27
 * @Version 1.0
 */
public class ExceptionInfoUtil {

    public static void main(String[] args) {
        try {
            throw new RuntimeException("自定义");
        }catch (Exception e){
            System.out.println(getExceptionAllInfo(e));
        }
    }
    /**
     * 获取异常信息
     *
     * @param ex
     * @return
     */
    public static String getExceptionAllInfo(Throwable ex) {
        ByteArrayOutputStream out = null;
        PrintStream pout = null;
        String ret = "";
        try {
            out = new ByteArrayOutputStream();
            pout = new PrintStream(out);
            ex.printStackTrace(pout);
            ret = out.toString();
            out.close();
        } catch (Exception e) {
            return ex.getMessage();
        } finally {
            if (pout != null) {
                pout.close();
            }
        }
        return ret;
    }
}
