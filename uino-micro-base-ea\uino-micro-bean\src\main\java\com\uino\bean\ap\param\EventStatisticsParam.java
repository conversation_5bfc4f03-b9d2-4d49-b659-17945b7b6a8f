package com.uino.bean.ap.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 告警统计参数
 */
@Getter
@Setter
@ApiModel(value = "告警统计参数", description = "告警统计参数")
public class EventStatisticsParam implements Serializable {

    @ApiModelProperty(value = "分组字段")
    private String groupField;

    @ApiModelProperty(value = "统计维度字段")
    private String analysisField;

    @ApiModelProperty(value = "输出记录数")
    private int builderSize;

    @ApiModelProperty(value = "开始时间")
    private Long startTime;

    @ApiModelProperty(value = "结束时间")
    private Long endTime;
}
