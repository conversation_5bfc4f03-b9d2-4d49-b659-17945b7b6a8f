package com.uinnova.product.eam.web.dm.mvc;

import com.binary.framework.web.RemoteResult;
import com.uinnova.product.eam.base.diagram.model.ESDiagram;
import com.uinnova.product.eam.model.dm.bean.AttrParamDto;
import com.uinnova.product.eam.model.enums.ArtifactEnum;
import com.uinnova.product.eam.service.dm.DataModelCatalogSvc;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uinnova.product.vmdb.comm.doc.annotation.MvcDesc;
import com.uino.bean.cmdb.base.LibType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 数据建模视图逻辑业务处理相关接口
 *
 * <AUTHOR>
 * @date 2022/8/15
 */
@Slf4j
@RestController
@RequestMapping("/dataModel/catalog")
@MvcDesc(author = "wcl", desc = "数据建模业务相关接口")
public class DataModelCatalogMvc {

    @Resource
    private DataModelCatalogSvc catalogSvc;


    @GetMapping("/convertConceptToLogic")
    @ModDesc(desc = "概念图->逻辑图->c`图->物理图的转换", pDesc = "视图加密id", rDesc = "响应成功标识", rType = RemoteResult.class)
    public RemoteResult convertConceptToLogic(@RequestParam(required = false) Long dirId,
                                              @RequestParam(required = false) String dEnergy,
                                              @RequestParam Integer convertType,
                                              @RequestParam(required = false) Integer operation) {
        //校验
        List<ESDiagram> conceptDiagrams = catalogSvc.filterDiagramByType(dirId, dEnergy,convertType);
        Boolean aBoolean = catalogSvc.convertDiagrams(conceptDiagrams,convertType,operation);
        String sMessage = null;
        String eMessage = null;
        if(ArtifactEnum.CONCEPTUAL_ENTITY.getArtifactType().intValue() == convertType){
            sMessage = "逻辑模型";
            eMessage = "概念模型";
        }else if(ArtifactEnum.RELATION_ENTITY.getArtifactType().intValue() == convertType){
            sMessage = "系统级逻辑实体";
            eMessage = "逻辑模型";
        }else if(ArtifactEnum.SYS_LOGICAL_ENTITY.getArtifactType().intValue() == convertType){
            sMessage = "物理模型";
            eMessage = "系统级逻辑实体";
        }
        if (aBoolean) {

            String successMessage = "已转化" + conceptDiagrams.size() + "张视图，请进入"+sMessage+"查看！";
            return new RemoteResult(true, -1, successMessage);
        } else {
            String errorMessage = "当前模型目录，不存在"+eMessage+"ER图，无法转化！";
            return new RemoteResult(true, -2, errorMessage);
        }
    }

    @GetMapping("/convert")
    @ModDesc(desc = "ER图转换", pDesc = "视图加密id", rDesc = "响应成功标识", rType = RemoteResult.class)
    public RemoteResult convert(@RequestParam(required = false) Long dirId, @RequestParam(required = false) String dEnergy,
                                              @RequestParam Integer convertType, @RequestParam(required = false) Integer operation) {
        Integer result = catalogSvc.convert(dirId, dEnergy, convertType, operation);
        String message = result.equals(-1)?"转换成功!":"转换失败!";
        return new RemoteResult(true, result, message);
    }

    @GetMapping("/checkVariation")
    @ModDesc(desc = "校验实体和属性是否发生变化", pDesc = "私有库", rDesc = "视图", rType = RemoteResult.class)
    public RemoteResult checkVariation(@RequestParam(required = false) Long dirId,
                                       @RequestParam(required = false) String dEnergy,
                                       @RequestParam Integer convertType) {
        return new RemoteResult(catalogSvc.checkVariation(dirId,dEnergy,convertType));
    }

    @GetMapping("/queryDataType")
    @ModDesc(desc = "获取数据类型", pDesc = "私有库/设计库", rDesc = "类型名称列表", rType = RemoteResult.class)
    public RemoteResult queryDataType(@RequestParam LibType libType) {
        return new RemoteResult(catalogSvc.queryDataType(libType));
    }

    @PostMapping("/getAttrInfoBiCiCode")
    @ModDesc(desc = "根据属性ciCode查询属性信息", pDesc = "私有库/设计库，属性的ciCode", rDesc = "属性具体信息", rType = RemoteResult.class)
    public RemoteResult getAttrInfoBiCiCode(@RequestBody @Validated AttrParamDto paramDto) {
        return new RemoteResult(catalogSvc.getAttrInfoBiCiCode(paramDto));
    }
}
