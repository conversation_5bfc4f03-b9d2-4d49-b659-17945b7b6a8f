package com.uino.api.client.sys.rpc;

import com.uino.dao.BaseConst;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.uino.bean.cmdb.base.ESTableDataConfigInfo;
import com.uino.provider.feign.sys.TableDataConfigFeign;
import com.uino.api.client.sys.ITableDataConfigApiSvc;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
public class TableDataConfigApiSvcRpc implements ITableDataConfigApiSvc {

    @Autowired
    private TableDataConfigFeign configFeign;

    @Override
    public ESTableDataConfigInfo getCIDataConfigInfo(String uid) {
        return configFeign.getCIDataConfigInfo(BaseConst.DEFAULT_DOMAIN_ID,uid);
    }

    @Override
    public ESTableDataConfigInfo getCIDataConfigInfo(Long domainId, String uid) {
        return configFeign.getCIDataConfigInfo(domainId,uid);
    }

    @Override
    public Long saveCIDataConfigInfo(ESTableDataConfigInfo configInfo) {
        return configFeign.saveCIDataConfigInfo(configInfo);
    }

}
