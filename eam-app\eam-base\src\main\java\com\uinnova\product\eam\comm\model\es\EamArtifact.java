package com.uinnova.product.eam.comm.model.es;

import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.util.List;

@Data
@Comment("制品基本信息表[UINO_EAM_Artifact]")
public class EamArtifact implements EntityBean {

    @Comment("制品类型id")
    private Long id;

    @Comment("制品类型名称")
    private String artifactName;

    @Comment("制品类型目的")
    private String typePurpose;

    @Comment("制品类型应用范围")
    private String appliedRange;

    @Comment("制品类型描述")
    private String typeSpecification;

    @Comment("制品类型应用场景")
    private String applyScene;

    @Comment("数据状态[DATA_STATUS]   0=删除，1=正常")
    private Integer dataStatus;

    @Comment("上传图片的id")
    private List<Long> fileIds;

    @Comment("是否发布   0=未发布（默认），1=已发布")
    private Integer releaseState;

    @Comment("制品被引用的次数")
    private Integer usageCounter;

    @Comment("制品类型分类 ArtifactEnum.class 1=业务流程建模 2=业务组件建模 3=需求关联分析 4=IT架构设计 5=其他 6=数据建模-概念实体关系图 7=数据建模-逻辑实体关系图")
    private Integer typeClassification;

    @Comment("制品所属组织")
    private Long orgId;

    @Comment("创建人")
    private String creator;

    @Comment("修改人")
    private String modifier;

    @Comment("制品创建时间")
    private Long createTime;

    @Comment("制品更改时间")
    private Long modifyTime;

    @Comment("制品是否支持拖入相同的资产在画布上")
    private Boolean isSupportDragAsset;

    @Comment("制品资产面板配置")
    private String assetPanelConfig;
}
