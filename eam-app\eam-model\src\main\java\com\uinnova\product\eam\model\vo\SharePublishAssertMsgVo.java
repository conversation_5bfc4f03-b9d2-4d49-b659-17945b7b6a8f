package com.uinnova.product.eam.model.vo;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

@Data
public class SharePublishAssertMsgVo {

    @Comment("资产创建人")
    private String assertOwnerCode;
    @Comment("类型")
    private String type;
    @Comment("资产名称")
    private String assertName;
    @Comment("资产id")
    private String assertId;
    @Comment("协作人")
    private Long shareUserId;
    @Comment("协作人名称")
    private String shareUserName;
}
