package com.uinnova.product.eam.service.es;

import com.uinnova.product.eam.comm.model.es.AppSquareConfig;
import com.uinnova.product.eam.comm.model.es.InterfaceParameter;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;

@Service
public class InterfaceParameterDao extends AbstractESBaseDao<InterfaceParameter, InterfaceParameter> {
    @Override
    public String getIndex() {
        return "uino_eam_interface_parameters";
    }

    @Override
    public String getType() {
        return getIndex();
    }
    @PostConstruct
    public void init() {
        super.initIndex();
    }
}
