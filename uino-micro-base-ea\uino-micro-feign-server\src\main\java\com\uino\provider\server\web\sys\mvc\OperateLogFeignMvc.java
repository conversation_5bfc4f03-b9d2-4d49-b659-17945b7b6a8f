package com.uino.provider.server.web.sys.mvc;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.binary.jdbc.Page;
import com.uino.service.sys.microservice.IOperateLogSvc;
import com.uino.bean.sys.base.ESOperateLog;
import com.uino.bean.sys.query.ESOperateLogSearchBean;
import com.uino.provider.feign.sys.OperateLogFeign;

/**
 * 
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("feign/sys/opLog")
public class OperateLogFeignMvc implements OperateLogFeign {

    @Autowired
    private IOperateLogSvc opLogSvc;

    @Override
    public Page<ESOperateLog> getOperateLogPageByCdt(ESOperateLogSearchBean bean) {
        return opLogSvc.getOperateLogPageByCdt(bean);
    }

    @Override
    public Long saveOrUpdate(ESOperateLog log) {
        return opLogSvc.saveOrUpdate(log);
    }

    @Override
    public Integer clearOperateLogByDuration(Integer clearLogDuration) {
        return opLogSvc.clearOperateLogByDuration(clearLogDuration);
    }

}
