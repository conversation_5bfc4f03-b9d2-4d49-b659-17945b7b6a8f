package com.uino.bean.permission.business;

import java.io.Serializable;
import java.util.List;

import com.uino.bean.permission.base.SysOrg;

/**
 * 组织树状结构
 * 
 * <AUTHOR>
 */
public class OrgTreeNode implements Serializable, Comparable<OrgTreeNode> {

    private static final long serialVersionUID = 1L;

    private Long id;
    private String name;
    private SysOrg org;
    private Long parentId;
    private List<OrgTreeNode> children;

    public OrgTreeNode() {
    }

    public OrgTreeNode(SysOrg org) {
        this.id = org.getId();
        this.name = org.getOrgName();
        this.parentId = org.getParentOrgId();
        this.org = org;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public SysOrg getOrg() {
        return org;
    }

    public void setOrg(SysOrg org) {
        this.org = org;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public List<OrgTreeNode> getChildren() {
        return children;
    }

    public void setChildren(List<OrgTreeNode> children) {
        this.children = children;
    }

    @Override
    public int compareTo(OrgTreeNode o) {
        Integer oOrderNo = o.getOrg() == null ? 1000 : o.getOrg().getOrderNo() == null ? 1000 : o.getOrg().getOrderNo();
        Integer sOrderNo = this.org == null ? 1000 : this.org.getOrderNo() == null ? 1000 : this.org.getOrderNo();
        if (sOrderNo - oOrderNo == 0) {
            return this.name.compareTo(o.name);
        }
        return sOrderNo - oOrderNo;
    }

}
