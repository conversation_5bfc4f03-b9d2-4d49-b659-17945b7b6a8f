package com.uino.util.message.queue.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Component;

import com.uino.util.message.queue.tools.EnableMessageQueue;

/**
 * Kafka basic configuration properties
 *
 * @Author: YGQ
 * @Create: 2021-05-24 13:34
 **/
@Component
@Conditional(EnableMessageQueue.class)
public class KafkaProp {

    /**
     * Kafka cluster address
     * <br>
     * eg:(0.0.0.0:0000,*******:0000,*******:0000)
     */
    public static String kafkaServers;

    @Value(value = "${kafka.server}")
    public void setKafkaServers(String kafkaServers) {
        KafkaProp.kafkaServers = kafkaServers;
    }

    /**
     * Number of kafka cluster partitions
     */
    public static Integer numberOfPartitions;

    @Value(value = "${kafka.number-of-partitions:3}")
    public void setNumberOfPartitions(Integer numberOfPartitions) {
        KafkaProp.numberOfPartitions = numberOfPartitions;
    }

    /**
     * Number of kafka cluster copies
     */
    public static Short numberOfCopies;

    @Value(value = "${kafka.number-of-copies:1}")
    public void setNumberOfCopies(Short numberOfCopies) {
        KafkaProp.numberOfCopies = numberOfCopies;
    }

    /**
     * Whether to support idempotence
     */
    public static Boolean enableIdempotence;

    @Value(value = "${kafka.producer.enable-idempotence:false}")
    public void setEnableIdempotence(Boolean enableIdempotence) {
        KafkaProp.enableIdempotence = enableIdempotence;
    }

    /**
     * Number of retries.
     * <br>
     * 0 : means no retry mechanism is enabled.
     * <br>
     * When the idempotent condition is turned on, the number of retries must be greater than 0.
     */
    public static Integer retries;

    @Value(value = "${kafka.producer.retries:3}")
    public void setRetries(Integer retries) {
        KafkaProp.retries = retries;
    }

    /**
     * Producer sends message confirmation strategy.
     * <br>
     * "all", "0", "1"
     */
    public static String sendAckMode;

    @Value(value = "${kafka.producer.ack-mode:all}")
    public void setSendAckMode(String sendAckMode) {
        KafkaProp.sendAckMode = sendAckMode;
    }

    /**
     * When the producer space memory is insufficient, calling the send method is blocked for the maximum time.
     * <br>
     * default is 60000ms.
     */
    public static Long maxBlockMs;

    @Value(value = "${kafka.producer.max-block-ms:60000}")
    public void setMaxBlockMs(Long maxBlockMs) {
        KafkaProp.maxBlockMs = maxBlockMs;
    }

    /**
     * The size of the data sent by the producer in batches.
     * <br>
     * Unit is byte.
     */
    public static Integer batchSize;

    @Value(value = "${kafka.producer.batch-size:40960}")
    public void setBatchSize(Integer batchSize) {
        KafkaProp.batchSize = batchSize;
    }

    /**
     * When sending data in batches, the size of the list
     */
    public static Integer sendBatchListSize;

    @Value(value = "${kafka.producer.batch-size:100}")
    public void setSendBatchListSize(Integer sendBatchListSize) {
        KafkaProp.sendBatchListSize = sendBatchListSize;
    }

    /**
     * The maximum waiting time when the batch-size message is less than the batch-size.
     * <br>
     * Unit is ms
     */
    public static Long lingerMs;

    @Value(value = "${kafka.producer.linger-ms:1}")
    public void setLingerMs(Long lingerMs) {
        KafkaProp.lingerMs = lingerMs;
    }

    /**
     * The size of the buffer that the producer can use when sending data.
     */
    public static Long bufferMemory;

	@Value(value = "${kafka.producer.buffer-memory:5242880}")
    public void setBufferMemory(Long bufferMemory) {
        KafkaProp.bufferMemory = bufferMemory;
    }


    /**
     * The maximum limit of data sent by the producer.
     * <br>
     * Default 1 MB
     */
    public static Integer maxRequestSize;

    @Value(value = "${kafka.producer.max-request-size:1048576}")
    public void setMaxRequestSize(Integer maxRequestSize) {
        KafkaProp.maxRequestSize = maxRequestSize;
    }

    /**
     * Producer message sending compression type.
     * <br>
     * none (default)
     * <br>
     * lz4
     * <br>
     * gzip
     * <br>
     * snappy
     */
    public static String compressionType;

    @Value(value = "${kafka.producer.compression-type:gzip}")
    public void setCompressionType(String compressionType) {
        KafkaProp.compressionType = compressionType;
    }

    /**
     * Concurrent consumption by consumers, The number should be determined according to the kafka partition and the TP or EP deployment instance.
     * <br>
     * When the number of Kafka partitions is 3 and the number of TPs is 1, the configuration here is 3.
     * When the number of Kafka partitions is 3 and the number of TPs is 3, the configuration here is 1.
     */
    public static Integer concurrencyNumber;

    @Value(value = "${kafka.consumer.concurrency-number:3}")
    public void setConcurrencyNumber(Integer concurrencyNumber) {
        KafkaProp.concurrencyNumber = concurrencyNumber;
    }

    /**
     * Consumer pull timeout.
     * <br>
     * Default is 3000 ms
     */
    public static Long pollTimeOut;

    @Value(value = "${kafka.consumer.poll-time-out:3000}")
    public void setPollTimeOut(Long pollTimeOut) {
        KafkaProp.pollTimeOut = pollTimeOut;
    }

    /**
     * Whether to open batch consumption
     */
    public static Boolean enableBatchListener;

    @Value(value = "${kafka.consumer.enable-batch-listener:true}")
    public void setEnableBatchListener(Boolean enableBatchListener) {
        KafkaProp.enableBatchListener = enableBatchListener;
    }

    /**
     * Consumer manual confirmation mode
     * <br>
     * RECORD： submit after each record is processed.
     * BATCH(default)： submit a batch of data after each poll, the frequency depends on the frequency of each poll call.
     * TIME： submit every ack time interval.
     * COUNT： after processing a batch of poll data and the number of records submitted for processing since the last time exceeds the set ack Count, submit it.
     * COUNT_TIME： submit when any one of time and count is satisfied.
     * MANUAL： After manually calling Acknowledgment.acknowledge(), and after processing KafkaProp batch of poll data, submit.
     * MANUAL_IMMEDIATE： Submit immediately after calling Acknowledgment.acknowledge() manually.
     */
    public static String consumerAckMode;

    @Value(value = "${kafka.consumer.ack-mode:MANUAL}")
    public void setConsumerAckMode(String consumerAckMode) {
        KafkaProp.consumerAckMode = consumerAckMode;
    }

    /**
     * Consumer group id.
     */
    public static String consumerGroupId;

    @Value(value = "${kafka.consumer.group-id:defaultGroup}")
    public void setConsumerGroupId(String consumerGroupId) {
        KafkaProp.consumerGroupId = consumerGroupId;
    }

    /**
     * Whether the consumer submits automatically.
     */
    public static Boolean enableAutoCommit;

    @Value(value = "${kafka.consumer.enable-auto-commit:false}")
    public void setEnableAutoCommit(Boolean enableAutoCommit) {
        KafkaProp.enableAutoCommit = enableAutoCommit;
    }

    /**
     * When automatic submission is turned on, the frequency of automatic submission.
     */
    public static Integer autoCommitInterval;

    @Value(value = "${kafka.consumer.auto-commit-interval:5000}")
    public void setAutoCommitInterval(Integer autoCommitInterval) {
        KafkaProp.autoCommitInterval = autoCommitInterval;
    }

    /**
     * Consumer session timeout.
     */
    public static Integer sessionTimeOutMs;

    @Value(value = "${kafka.consumer.session-timeout-ms:6000}")
    public void setSessionTimeOutMs(Integer sessionTimeOutMs) {
        KafkaProp.sessionTimeOutMs = sessionTimeOutMs;
    }

    /**
     * Offset rule setting.
     * <br>
     * earliest：When there is a submitted offset under each partition, start consumption from the submitted offset; when there is no submitted offset, start consumption from the beginning.
     * latest：When there is a submitted offset in each partition, start consumption from the submitted offset; when there is no submitted offset, consume the newly generated data under the partition.
     * none：When there is a submitted offset in each partition of the topic, consumption starts after the offset; as long as there is no submitted offset in one partition, an exception will be thrown.
     */
    public static String autoOffsetReset;

    @Value(value = "${kafka.consumer.auto-offset-reset:earliest}")
    public void setAutoOffsetReset(String autoOffsetReset) {
        KafkaProp.autoOffsetReset = autoOffsetReset;
    }

    /**
     * When consumers consume in batches, the maximum number of pulls each time
     */
    public static Integer maxPollRecords;

    @Value(value = "${kafka.consumer.max-poll-records:100}")
    public void setMaxPollRecords(Integer maxPollRecords) {
        KafkaProp.maxPollRecords = maxPollRecords;
    }

    /**
     * The maximum delay for consumers to consume messages, the default value is 6 hours
     * */
    public static Integer maxPollInterval;

    @Value(value = "${kafka.consumer.max.poll.interval:21600000}")
    public void setMaxPollInterval(Integer maxPollInterval) {
        KafkaProp.maxPollInterval = maxPollInterval;
    }

    /**
     * Global message filter, only accepts messages that contain the content of the filter
     */
    public static String recordFilterStrategyKey;

    @Value(value = "${kafka.consumer.record-filter-strategy-key:record-filter-strategy-key}")
    public void setRecordFilterStrategyKey(String recordFilterStrategyKey) {
        KafkaProp.recordFilterStrategyKey = recordFilterStrategyKey;
    }

    /**
     * Global StringSerializer
     */
    public static String kafkaSerialization;

    @Value(value = "${kafka.serialization:org.apache.kafka.common.serialization.StringSerializer}")
    public void setKafkaSerialization(String kafkaSerialization) {
        KafkaProp.kafkaSerialization = kafkaSerialization;
    }

    /**
     * Global StringDeserializer
     */
    public static String kafkaDeserializer;

    @Value(value = "${kafka.deserializer:org.apache.kafka.common.serialization.StringDeserializer}")
    public void setKafkaDeserializer(String kafkaDeserializer) {
        KafkaProp.kafkaDeserializer = kafkaDeserializer;
    }
}
