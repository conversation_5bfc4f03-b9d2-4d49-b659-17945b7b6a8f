package com.uinnova.product.eam.web.diagram.peer.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSONObject;
import com.binary.core.util.BinaryUtils;
import com.binary.jdbc.Page;
import com.binary.tools.excel.Column;
import com.binary.tools.excel.ExcelStyle;
import com.google.common.collect.Lists;
import com.uinnova.product.eam.base.diagram.model.CVcDiagram;
import com.uinnova.product.eam.base.diagram.model.ESDiagram;
import com.uinnova.product.eam.base.diagram.model.ESSimpleDiagramDTO;
import com.uinnova.product.eam.comm.exception.BusinessException;
import com.uinnova.product.eam.comm.model.CVcDiagramDir;
import com.uinnova.product.eam.comm.model.VcDiagram;
import com.uinnova.product.eam.comm.model.VcDiagramDir;
import com.uinnova.product.eam.comm.model.VcGroup;
import com.uinnova.product.eam.config.Env;
import com.uinnova.product.eam.model.DiagramBo;
import com.uinnova.product.eam.model.PlanDiagramRequest;
import com.uinnova.product.eam.model.VcDiagramInfo;
import com.uinnova.product.eam.model.diagram.SystemDiagramDirResultInfo;
import com.uinnova.product.eam.model.enums.DiagramFieldEnum;
import com.uinnova.product.eam.model.vo.*;
import com.uinnova.product.eam.service.ESDiagramSvc;
import com.uinnova.product.eam.service.EamDiagramRelationSysService;
import com.uinnova.product.eam.service.ICISwitchSvc;
import com.uinnova.product.eam.service.VcDiagramSvc;
import com.uinnova.product.eam.service.impl.IamsCIDesignSvc;
import com.uinnova.product.eam.web.diagram.constant.CjConstant;
import com.uinnova.product.eam.web.diagram.peer.SystemDiagramPeer;
import com.uinnova.product.eam.web.util.DmvFileUtil;
import com.uinnova.product.vmdb.comm.i18n.MessageUtil;
import com.uinnova.product.vmdb.comm.model.ci.CCcCi;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClass;
import com.uinnova.product.vmdb.comm.model.ci.CcCi;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.comm.util.CiExcelUtil;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CiGroupPage;
import com.uino.api.client.cmdb.ICIClassApiSvc;
import com.uino.api.client.permission.IUserApiSvc;
import com.uino.bean.cmdb.base.ESCIAttrDefInfo;
import com.uino.bean.cmdb.base.ESCIClassInfo;
import com.uino.bean.cmdb.base.LibType;
import com.uino.bean.cmdb.query.ESCISearchBean;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.permission.business.UserInfo;
import com.uino.bean.permission.query.CSysUser;
import com.uino.dao.permission.ESUserSvc;
import com.uino.service.cmdb.microservice.ICIClassSvc;
import com.uino.util.sys.SysUtil;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class SystemDiagramPeerImpl implements SystemDiagramPeer {
    @Value("${http.resource.space}")
    private String httpResouceUrl;

    @Resource
    private ICIClassSvc iciClassSvc;

    @Autowired
    private ESDiagramSvc esDiagramSvc;

    @Autowired
    private com.uinnova.product.eam.service.diagram.ESDiagramSvc diagramApiClient;

    @Autowired
    ICISwitchSvc ciSwitchSvc;

    @Autowired
    private VcDiagramSvc diagramSvc;

    @Autowired
    private DmvFileUtil dmvFileUtil;

    @Autowired
    private IUserApiSvc userApiSvc;

    @Resource
    private ICIClassApiSvc iciClassApiSvc;

    @Autowired
    private IamsCIDesignSvc iamsCiDesignSvc;

    @Autowired
    private EamDiagramRelationSysService eamDiagramRelationSysService;

    @Resource
    private ESUserSvc esUserSvc;


    /**
     * RSM-SLAVE访问入口
     **/
    @Value("${http.resource.space}")
    private String rsmSlaveRoot;

    public String getRsmSlaveRoot() {
        return rsmSlaveRoot;
    }

    public void setRsmSlaveRoot(String rsmSlaveRoot) {
        this.rsmSlaveRoot = rsmSlaveRoot;
    }

    private void diagramSetProperties(List<ESSimpleDiagramDTO> esSimpleDiagramDTOList) {
        if (BinaryUtils.isEmpty(esSimpleDiagramDTOList)) {
            return;
        }

        List<ESDiagram> esDiagramList =
                esSimpleDiagramDTOList.stream().map(ESSimpleDiagramDTO::getDiagram).collect(Collectors.toList());

        if (BinaryUtils.isEmpty(esDiagramList)) {
            return;
        }

        eamDiagramRelationSysService.esDiagramSetRelationProperties(esDiagramList);

        for (ESDiagram esDiagram : esDiagramList) {
            Long diagramDirId = esDiagram.getDirId();
            if (diagramDirId != null) {
                if (diagramDirId == 1L) {
                    esDiagram.setRelationLocation("我的文件");
                } else {
                    VcDiagramDir vcDiagramDir = diagramSvc.queryDiagramDirById(1L, diagramDirId);
                    if (vcDiagramDir != null && !StringUtils.isEmpty(vcDiagramDir.getDirName())) {
                        esDiagram.setRelationLocation(vcDiagramDir.getDirName());
                    }
                }
            }

            if (Objects.equals(esDiagram.getIsOpen(), 0) && !StringUtils.isEmpty(esDiagram.getReleaseDiagramId())) {
                esDiagram.setIsOpen(1);
            }
        }
    }

    private void setChildrenDirs(Long pId, String like, Integer dirType, Integer dataStatus, String order, Long userId,
                                 Long domainId, SystemDiagramDirResultInfo ret) {
        List<VcDiagramDir> dirList = queryAndFillDiagramDirList(domainId, pId, like, userId, dataStatus, order, ret);
        List<VcDiagramDirVo> diagramDirVoList = new ArrayList<>();

        Map<Long, UserInfo> userMap = getUserMap(dirList);

        for (VcDiagramDir diagramDir : dirList) {
            VcDiagramDirVo diagramDirVo = new VcDiagramDirVo();
            BeanUtils.copyProperties(diagramDir, diagramDirVo);
            if (diagramDirVo.getUserId() != null) {
                UserInfo userInfo = userMap.get(diagramDirVo.getUserId());
                if (userInfo != null) {
                    diagramDirVo.setUserName(userInfo.getUserName());
                } else {
                    diagramDirVo.setUserName("admin");
                }
            } else {
                diagramDirVo.setUserName("admin");
            }
            if (diagramDir.getParentId() == 1) {
                diagramDirVo.setRelationLocation("我的文件");
            } else {
                VcDiagramDir parentDir = diagramSvc.queryDiagramDirById(1L, diagramDir.getParentId());
                if (parentDir != null) {
                    diagramDirVo.setRelationLocation(parentDir.getDirName());
                }
            }
            if (!StringUtils.isEmpty(diagramDir.getDirPath())) {
                String sysId = diagramDir.getDirPath().split("#")[1];
                VcDiagramDir sysDir = diagramSvc.queryDiagramDirById(1L, Long.valueOf(sysId));
                if (sysDir != null && sysDir.getEsSysId() != null && diagramDir.getEsSysId() == null) {
                    diagramDirVo.setRelationSystem(sysDir.getDirName());
                    diagramDirVo.setSystemType(sysDir.getSysType());
                }
            }
            diagramDirVoList.add(diagramDirVo);
        }
        ret.setChildrenDirs(diagramDirVoList);
    }

    private Map<Long, UserInfo> getUserMap(List<VcDiagramDir> dirList) {
        Long[] userIds =
                dirList.stream().map(VcDiagramDir::getUserId).distinct().filter(Objects::nonNull).toArray(Long[]::new);

        if (BinaryUtils.isEmpty(userIds)) {
            return Collections.emptyMap();
        }

        CSysUser cSysUser = new CSysUser();
        cSysUser.setIds(userIds);
        List<UserInfo> userInfoList = userApiSvc.getUserInfoByCdt(cSysUser, true);

        return userInfoList.stream().collect(Collectors.toMap(SysUser::getId, u -> u, (k1, k2) -> k1));
    }

    private List<VcDiagramDir> queryAndFillDiagramDirList(Long domainId, Long pId, String like, Long userId, Integer dataStatus, String order, SystemDiagramDirResultInfo ret) {
        CVcDiagramDir cdt = new CVcDiagramDir();
        if (!StringUtils.isEmpty(like)) {
            if (pId != null && pId != 1) {
                cdt.setParentId(pId);
            }
        } else {
            cdt.setParentId(pId);
        }
        cdt.setUserId(userId);
        cdt.setDataStatus(dataStatus);
        if (!StringUtils.isEmpty(like)) {
            cdt.setDirName("%" + like + "%");
        }
        cdt.setDomainId(domainId);
        cdt.setDirType(11);
        List<VcDiagramDir> queryDiagramDirList = diagramSvc.queryDiagramDirList(domainId, cdt, order);

        subListDirCreatorAndModifier(queryDiagramDirList);
        return queryDiagramDirList;
    }

    /**
     * 修整视图信息
     *
     * @param diagramInfo
     * @param token
     */
    private void trimDiagram(VcDiagramInfo diagramInfo, String token) {
        if (diagramInfo == null)
            return;
        fillRsmResource(diagramInfo, token);

        fillPath(diagramInfo);
        subCreatorAndModifier(diagramInfo);

    }

    /**
     * 修整视图信息
     *
     * @param diagramInfos
     * @param token
     */
    private void trimDiagramList(List<VcDiagramInfo> diagramInfos, String token) {
        if (diagramInfos == null)
            return;
        for (VcDiagramInfo vcDiagramInfo : diagramInfos) {
            trimDiagram(vcDiagramInfo, token);
        }
    }

    /**
     * 填充视图的远程资源信息
     *
     * @param diagramInfo
     * @param token
     */
    private void fillRsmResource(VcDiagramInfo diagramInfo, String token) {
        if (diagramInfo == null)
            return;

        VcDiagram diagram = diagramInfo.getDiagram();
        if (diagram == null)
            return;

        String diagramJsonPath = diagram.getDiagramJson();

        if (diagramJsonPath != null && diagramJsonPath.startsWith(httpResouceUrl)) {
            diagramJsonPath = diagramJsonPath.substring(httpResouceUrl.length());
        }

        if (diagramJsonPath != null) {
            try {
                String json = dmvFileUtil.getResourceContent(diagramJsonPath);
                diagramInfo.setJson(json);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    private void fillPath(VcDiagramInfo diagramInfo) {
        if (diagramInfo == null)
            return;
        VcDiagram diagram = diagramInfo.getDiagram();
        if (diagram == null)
            return;

        // 缩略图
        String icon1 = diagram.getIcon1();
        if (icon1 != null && !icon1.startsWith(httpResouceUrl)) {
            icon1 = httpResouceUrl + icon1;
            diagram.setIcon1(icon1);
        }

        String diagramBgImg = diagram.getDiagramBgImg();
        if (diagramBgImg != null && !BinaryUtils.isEmpty(diagramBgImg.trim())
                && !diagramBgImg.startsWith(httpResouceUrl)) {
            diagramBgImg = httpResouceUrl + diagramBgImg;
            diagram.setDiagramBgImg(diagramBgImg);
        }

        List<VcGroup> groups = diagramInfo.getGroups();
        if (!BinaryUtils.isEmpty(groups)) {
            for (VcGroup vcGroup : groups) {
                String groupImage = vcGroup.getGroupImage();
                if (!BinaryUtils.isEmpty(groupImage) && !groupImage.startsWith(httpResouceUrl)) {
                    groupImage = httpResouceUrl + groupImage;
                }
                vcGroup.setGroupImage(groupImage);
                subGroupCreatorAndModifier(vcGroup);
            }
        }
        // 修整用户头像信息
        SysUser creator = diagramInfo.getCreator();
        if (creator != null) {
            String icon = creator.getIcon();
            // 这儿判断是因为当前用户是同一个对象，所以再修改会无限拼接，勿删！！！！！！
            if (icon != null && !icon.startsWith(this.httpResouceUrl)) {
                creator.setIcon(httpResouceUrl + icon);
            }
        }
    }

    /**
     * 将创建者和修改者名字剪切掉 aa[bb] --> aa
     *
     * @param diagramInfo 视图信息
     */
    private void subCreatorAndModifier(VcDiagramInfo diagramInfo) {
        if (diagramInfo != null) {
            VcDiagram diagram = diagramInfo.getDiagram();
            if (diagram != null) {
                diagram.setCreator(subName(diagram.getCreator()));
                diagram.setModifier(subName(diagram.getModifier()));
            }
        }
    }

    private void subListDirCreatorAndModifier(List<VcDiagramDir> diagramDirs) {
        if (diagramDirs != null) {
            for (VcDiagramDir diagramDir : diagramDirs) {
                subDirCreatorAndModifier(diagramDir);
            }
        }
    }

    private void subDirCreatorAndModifier(VcDiagramDir diagramDir) {
        if (diagramDir != null) {
            diagramDir.setCreator(subName(diagramDir.getCreator()));
            diagramDir.setModifier(subName(diagramDir.getModifier()));
        }
    }

    private void subGroupCreatorAndModifier(VcGroup group) {
        if (group != null) {
            group.setModifier(subName(group.getModifier()));
            group.setCreator(subName(group.getCreator()));
        }
    }

    private String subName(String name) {
        if (name != null) {
            int lastIndexOf = name.lastIndexOf("[");
            if (lastIndexOf > 0) {
                return name.substring(0, lastIndexOf);
            }
        }
        return name;
    }

    @Override
    public CiDirVo getCiDirInfo(Long dirId) {
        MessageUtil.checkEmpty(dirId, "dirId");
        ESCISearchBean bean = new ESCISearchBean();
        CCcCi cdt = new CCcCi();
        cdt.setId(dirId);
        bean.setCdt(cdt);
        CiGroupPage ciGroupPage = iamsCiDesignSvc.queryPageBySearchBean(bean, false);
        CcCiInfo ccCiInfo = ciGroupPage.getData().get(0);
        Map<String, String> attrs = ccCiInfo.getAttrs();

        CiDirVo ciDirVo = new CiDirVo();
        CCcCiClass cCcCiClass = new CCcCiClass();
        cCcCiClass.setId(ccCiInfo.getCi().getClassId());

        List<CcCiClassInfo> ccCiClassList = iciClassApiSvc.queryClassByCdt(cCcCiClass);
        String className = ccCiClassList.get(0).getCiClass().getClassName();

        if (Env.APP_SUBSYSTEM.getClassName().equals(className)) {
            String contact = attrs.get("业务联系人及部门");
            if (!StringUtils.isEmpty(contact) && contact.startsWith("[") && contact.endsWith("]")) {
                List<String> list = (List) JSONObject.parseArray(contact);
                StringJoiner stringJoiner = new StringJoiner(", ");
                list.forEach(stringJoiner::add);
                contact = stringJoiner.toString();
            }
            ciDirVo.setSystemName(attrs.get("子系统名称"));
            ciDirVo.setSystemSign(attrs.get("子系统标识"));
            ciDirVo.setSystemType(attrs.get("子系统分类"));
            ciDirVo.setLevel(attrs.get("重要性等级"));
            ciDirVo.setProductStatus(attrs.get("投产状态"));
            ciDirVo.setBelongDepart(attrs.get("归属处室"));
            ciDirVo.setLeader(attrs.get("子系统负责人"));
            ciDirVo.setContacts(contact);
            ciDirVo.setRemark(attrs.get("子系统简介"));
            ciDirVo.setSysSign(1);
        } else if ( Env.APP.getClassName().equals(className)) {
            ciDirVo.setSystemName(attrs.get("应用系统中文名称"));
            ciDirVo.setSystemSign(attrs.get("应用系统编号"));
            ciDirVo.setSystemType(attrs.get("应用系统分类"));
            ciDirVo.setLevel(attrs.get("重要性等级"));
            ciDirVo.setProductStatus(attrs.get("所属业务能力"));
            ciDirVo.setBelongDepart(attrs.get("所属层级"));
            ciDirVo.setLeader(attrs.get("管理员"));
            ciDirVo.setContacts(attrs.get("所属应用域"));
            ciDirVo.setRemark(attrs.get("应用系统简介"));
            ciDirVo.setSysSign(2);
        }
        return ciDirVo;
    }

    /**
     * 名称模糊分页查询已发布的视图
     *
     * @param request {@link PlanDiagramRequest}
     * @return 数据对象 {@link ESDiagram}
     */
    @Override
    public Page<Map<String, Object>> findDiagramLikeName(PlanDiagramRequest request) {
        Page<ESDiagram> esDiagramPage = esDiagramSvc.findDiagramLikeName(request);

        Page<Map<String, Object>> page = new Page<>();
        BeanUtil.copyProperties(esDiagramPage, page, "data");
        if (esDiagramPage.getTotalRows() == 0) {
            page.setData(Collections.emptyList());
        } else {
            List<ESDiagram> esDiagramList = esDiagramPage.getData();
            // 设置所属系统
            eamDiagramRelationSysService.esDiagramSetRelationProperties(esDiagramList);

            List<Map<String, Object>> diagramMapList =
                    esDiagramList.stream().map(this::convertDiagramMap).collect(Collectors.toList());

            // 设置creator信息
            diagramMapPutCreator(diagramMapList, esDiagramList);

            page.setData(diagramMapList);
        }

        return page;
    }

    @Override
    public Page<DiagramBo> findDiagramList(PlanDiagramRequest request) {
        return esDiagramSvc.findDiagramList(request);
    }

    private Map<String, Object> convertDiagramMap(ESDiagram esDiagram) {
        int initialCapacity = ESDiagram.class.getDeclaredFields().length + 1;
        Map<String, Object> diagramMap = MapUtil.newHashMap(initialCapacity);
        BeanUtil.copyProperties(esDiagram, diagramMap, "id", "dEnergy", "isOpen");
        diagramMap.put("id", esDiagram.getDEnergy());
        boolean releaseDiagramIdNotBlank =
                org.apache.commons.lang3.StringUtils.isNotBlank(esDiagram.getReleaseDiagramId());
        boolean isOpen = esDiagram.getIsOpen() != null && esDiagram.getIsOpen() == 0;
        diagramMap.put("isOpen", isOpen && releaseDiagramIdNotBlank ? 1 : 0);
        return diagramMap;
    }

    private void diagramMapPutCreator(List<Map<String, Object>> diagramMapList, List<ESDiagram> esDiagramList) {
        List<Long> creatorIdList =
                esDiagramList.stream().map(ESDiagram::getUserId).distinct().collect(Collectors.toList());

        List<SysUser> userList = esUserSvc.getListByQuery(QueryBuilders.boolQuery().must(QueryBuilders.termsQuery("id",
                creatorIdList)));

        Map<Long, SysUser> userMap = userList.stream().collect(Collectors.toMap(SysUser::getId, Function.identity()));

        for (Map<String, Object> diagramMap : diagramMapList) {
            Long userId = (Long) diagramMap.get("userId");
            SysUser user = userMap.get(userId);
            diagramMap.put("creator", user);
        }
    }

    @Override
    public boolean releasePlan(List<Long> sysIdList) {
        return true;
    }

    @Override
    public CcCiInfo getCiByCiCode(String ciCode) {
        if (StringUtils.isEmpty(ciCode)) {
            throw new BusinessException("ci编码不能为空!");
        }
        ESCISearchBean bean = new ESCISearchBean();
        CCcCi cdt = new CCcCi();
        cdt.setCiCode(ciCode);
        bean.setCdt(cdt);
        CiGroupPage ciGroupPage = iamsCiDesignSvc.queryPageBySearchBean(bean, false);
        if (ciGroupPage != null && !CollectionUtils.isEmpty(ciGroupPage.getData())) {
            return ciGroupPage.getData().get(0);
        }
        return null;
    }

    @Override
    public List<CcCi> getBathCi(String[] ciCodes) {
        ESCISearchBean bean = new ESCISearchBean();
        CCcCi cdt = new CCcCi();
        cdt.setCiCodes(ciCodes);
        List<CcCi> ciList = ciSwitchSvc.queryCiList(SysUtil.getCurrentUserInfo().getDomainId(), cdt, null, true, LibType.DESIGN);
        if (ciList != null && ciList.size() >= 1) {
            return ciList;
        }
        return new ArrayList<>();
    }

    @Override
    public List<ESSimpleDiagramDTO> findMyPublishDiagramList(SysUser sysUser) {
        if (sysUser == null) {
            throw new BusinessException("用户信息不能为空!");
        }
        CVcDiagram cVcDiagram = new CVcDiagram();
        cVcDiagram.setDirType(CjConstant.DIR_TYPE);
        cVcDiagram.setIsOpen(CjConstant.IS_OPEN);
        cVcDiagram.setCreatorEqual(sysUser.getLoginCode());
        Page<ESSimpleDiagramDTO> page = diagramApiClient.queryESDiagramInfoPage(sysUser.getDomainId(), 1, 1000, cVcDiagram, null);
        if (page != null && !CollectionUtils.isEmpty(page.getData())) {
            return page.getData();
        }
        return Collections.emptyList();
    }

    @Override
    public List<Map<String, Object>> findDiagramByCdt(PlanDiagramRequest request) {
        Page<ESDiagram> esDiagramPage = esDiagramSvc.findDiagramLikeName(request);

        Page<Map<String, Object>> page = new Page<>();
        BeanUtil.copyProperties(esDiagramPage, page, "data");
        if (esDiagramPage.getTotalRows() == 0) {
            page.setData(Collections.emptyList());
        } else {
            List<ESDiagram> esDiagramList = esDiagramPage.getData();
            // 设置所属系统
            eamDiagramRelationSysService.esDiagramSetRelationProperties(esDiagramList);

            List<Map<String, Object>> diagramMapList =
                    esDiagramList.stream().map(this::convertDiagramMap).collect(Collectors.toList());

            // 设置creator信息
            diagramMapPutCreator(diagramMapList, esDiagramList);

            page.setData(diagramMapList);
        }

        return page.getData();
    }

    @Override
    public List<ESCIAttrDefInfo> findAttrDefList(Long classId) {
        ESCIClassInfo esciClassInfo = iciClassSvc.queryESClassInfoById(classId);
        if (esciClassInfo != null && !CollectionUtils.isEmpty(esciClassInfo.getAttrDefs())) {
            return esciClassInfo.getAttrDefs();
        }
        return null;
    }

    @Override
    public List<DiagramFieldVo> findDiagramFieldList() {
        return DiagramFieldEnum.getInstance();
    }

    @Override
    public List<VcDiagramDir> findDiagramDirList(CVcDiagramDir cdt, SysUser sysUser) {
        cdt.setCreatorEqual(sysUser.getLoginCode());
        cdt.setDataStatus(1);
        return diagramSvc.queryDiagramDirList(sysUser.getDomainId(), cdt, null);
    }

    @Override
    public List<ESDiagram> findSimpleDiagramByCdt(PlanDiagramRequest request) {
        Page<ESDiagram> esDiagramPage = esDiagramSvc.findDiagramLikeName(request);

        Page<Map<String, Object>> page = new Page<>();
        BeanUtil.copyProperties(esDiagramPage, page, "data");
        if (esDiagramPage.getTotalRows() == 0) {
            return Lists.newArrayList();
        } else {
            List<ESDiagram> esDiagramList = esDiagramPage.getData();
            // 设置所属系统
            eamDiagramRelationSysService.esDiagramSetRelationProperties(esDiagramList);
            return esDiagramList;
        }
    }

    @Override
    public Page<RelateDiagram> queryRelateDiagramList(RelateionDiagramVo params) {
        return esDiagramSvc.queryRelateDiagramList(params);
    }
}
