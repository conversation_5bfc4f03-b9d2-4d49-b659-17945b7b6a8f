package com.uinnova.product.eam.service.impl;

import com.google.common.collect.Lists;
import com.uinnova.product.eam.model.ArchFieldPermissionBO;
import com.uinnova.product.eam.service.ArchFieldPermissionSvc;
import com.uino.api.client.permission.IModuleApiSvc;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.permission.business.ModuleNodeInfo;
import com.uino.util.sys.SysUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
@Deprecated
public class ArchFieldPermissionSvcImpl implements ArchFieldPermissionSvc {

    @Autowired
    private IModuleApiSvc moduleApiSvc;

    private static final String QuickEAModuleCode = "8948830619007584";

    private static final String DesignSpaceModuleCode = "8948830619007619";
    private static final String DesignSpaceModuleName = "我的空间";

    private static final String AssertSpaceModuleCode = "8948830619007632";
    private static final String AssertSpaceModuleName = "资产仓库";

    private static final String BusinessArchModuleCode = "8948830619009281";

    private static final String OtherModuleCode = "8948830619009553";



    private static final List<String> ModuleCodeList = Lists.newArrayList("8948830619008341","8964026895302176","8963553354803586","8963553354803502","1384295824926386");

    private static final List<String> AssertModuleCodeList = Lists.newArrayList("8964026895312390","1406246419201031");

    /*private static final Map<String,Integer> map = ImmutableMap.<String,Integer>builder()
            .put("业务架构设计",1)
            .put("应用架构设计",11)
            .put("数据架构设计",101)
            .put("技术架构设计",111)
            .put("产品方案设计",110)
            .build();



    private static final Map<String,Integer> assertMap = ImmutableMap.<String,Integer>builder()
            .put("业务架构资产",1)
            .put("应用架构资产",11)
            .put("产品方案资产",110)
            .build();*/

    @Override
    public List<ArchFieldPermissionBO> getDesignPermission() {
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        ModuleNodeInfo tree = moduleApiSvc.getModuleTree(currentUserInfo.getDomainId(), currentUserInfo.getId());
        if (tree == null || tree.getChildren() == null || tree.getChildren().size() < 1) {
            return new ArrayList<>();
        }
        Optional<ModuleNodeInfo> quickEAModule = tree.getChildren().stream().filter(item -> item.getModuleCode().equals(QuickEAModuleCode)).findFirst();
        ModuleNodeInfo quickEAModuleNodeInfo = quickEAModule.orElse(null);
        if (null == quickEAModuleNodeInfo || quickEAModuleNodeInfo.getChildren() == null || quickEAModuleNodeInfo.getChildren().size() < 1) {
            return new ArrayList<>();
        }
        Optional<ModuleNodeInfo> designModule = quickEAModuleNodeInfo.getChildren().stream().filter(item -> item.getModuleCode().equals(DesignSpaceModuleName)).findFirst();
        ModuleNodeInfo designModuleNodeInfo = designModule.orElse(null);
        if (designModuleNodeInfo == null || designModuleNodeInfo.getChildren().size() < 1) {
            return new ArrayList<>();
        }
        List<ModuleNodeInfo> children = designModuleNodeInfo.getChildren();
        List<ArchFieldPermissionBO> result = new ArrayList<>();
        for (ModuleNodeInfo moduleNodeInfo : children) {
            ArchFieldPermissionBO archFieldPermissionBO = new ArchFieldPermissionBO();
            archFieldPermissionBO.setModuleName(moduleNodeInfo.getLabel());
            archFieldPermissionBO.setDirType(1);
            result.add(archFieldPermissionBO);
        }
        return result;
    }

    @Override
    public List<ArchFieldPermissionBO> getAssertPermission() {
        List<ArchFieldPermissionBO> result = new ArrayList<>();
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        ModuleNodeInfo tree = moduleApiSvc.getModuleTree(currentUserInfo.getDomainId(), currentUserInfo.getId());
        if (tree == null || tree.getChildren() == null || tree.getChildren().size() < 1) {
            return new ArrayList<>();
        }
        Optional<ModuleNodeInfo> quickEAModule = tree.getChildren().stream().filter(item -> item.getModuleCode().equals(QuickEAModuleCode)).findFirst();
        ModuleNodeInfo quickEAModuleNodeInfo = quickEAModule.orElse(null);
        if (null == quickEAModuleNodeInfo || quickEAModuleNodeInfo.getChildren() == null || quickEAModuleNodeInfo.getChildren().size() < 1) {
            return new ArrayList<>();
        }
        List<ModuleNodeInfo> spaceModuleList = quickEAModuleNodeInfo.getChildren();

        Optional<ModuleNodeInfo> assertOptional = spaceModuleList.stream().filter(item -> item.getModuleName().equals(AssertSpaceModuleName)).findFirst();
        ModuleNodeInfo assertModuleNodeInfo = assertOptional.orElse(null);
        if (assertModuleNodeInfo == null || assertModuleNodeInfo.getChildren() == null || assertModuleNodeInfo.getChildren().size() < 1) {
            return new ArrayList<>();
        }
        List<ModuleNodeInfo>  assertChildModuleNodeInfo = assertModuleNodeInfo.getChildren();
        for (ModuleNodeInfo each : assertChildModuleNodeInfo) {
            //资产仓库
            if(each.getModuleType() == 3){
                ArchFieldPermissionBO archFieldPermissionBO = new ArchFieldPermissionBO();
                archFieldPermissionBO.setModuleName(each.getLabel());
                archFieldPermissionBO.setDirType(1);
                result.add(archFieldPermissionBO);
            }
            if(CollectionUtils.isNotEmpty(each.getChildren())){
                for (ModuleNodeInfo child : each.getChildren()) {
                    ArchFieldPermissionBO archFieldPermissionBO = new ArchFieldPermissionBO();
                    archFieldPermissionBO.setModuleName(child.getLabel());
                    archFieldPermissionBO.setDirType(1);
                    result.add(archFieldPermissionBO);
                }
            }
        }
        return result;
    }
}
