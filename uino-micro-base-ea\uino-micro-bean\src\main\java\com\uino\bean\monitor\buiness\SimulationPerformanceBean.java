package com.uino.bean.monitor.buiness;

import com.uino.bean.monitor.base.ESKpiInfo;
import com.uino.bean.monitor.base.SimulationRuleInfo;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel(value = "模拟性能数据类")
public class SimulationPerformanceBean {
	private SimulationRuleInfo ruleInfo;
	private ESKpiInfo kpiInfo;
	private long time;
	private String val;
}
