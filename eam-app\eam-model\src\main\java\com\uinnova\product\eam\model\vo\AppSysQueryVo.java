package com.uinnova.product.eam.model.vo;

import com.binary.framework.bean.annotation.Comment;
import com.uino.bean.cmdb.base.ESCIAttrDefInfo;
import com.uino.bean.cmdb.base.ESCIInfo;
import lombok.Data;

import java.util.List;

@Data
public class AppSysQueryVo {

    @Comment("分类属性信息")
    private List<ESCIAttrDefInfo> attrDefs;

    @Comment("分类查询的ci信息")
    private List<AppSysCIVo> esciInfo;

    private long pageNum;

    private long pageSize;

    private long totalRows;

    private long totalPages;
}
