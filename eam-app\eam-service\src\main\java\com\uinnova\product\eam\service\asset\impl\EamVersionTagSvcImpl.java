package com.uinnova.product.eam.service.asset.impl;

import com.binary.core.util.BinaryUtils;
import com.google.common.collect.Lists;
import com.uinnova.product.eam.base.diagram.model.ESDiagram;
import com.uinnova.product.eam.base.util.EamUtil;
import com.uinnova.product.eam.comm.model.es.*;
import com.uinnova.product.eam.model.dto.EamVersionTagDto;
import com.uinnova.product.eam.model.enums.AssetType;
import com.uinnova.product.eam.model.enums.OperatorType;
import com.uinnova.product.eam.service.EamCategorySvc;
import com.uinnova.product.eam.service.IBmMultiModelHierarchySvc;
import com.uinnova.product.eam.service.IVersionTagSvc;
import com.uinnova.product.eam.service.asset.EamVersionTagSvc;
import com.uinnova.product.eam.service.bm.IEamFlowModelSvc;
import com.uinnova.product.eam.service.diagram.ESDiagramSvc;
import com.uino.bean.cmdb.base.LibType;
import com.uino.bean.permission.base.SysUser;
import com.uino.dao.util.ESUtil;
import com.uino.util.sys.SysUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 版本标签业务层接口实现
 * <AUTHOR>
 */
@Service
public class EamVersionTagSvcImpl implements EamVersionTagSvc {

    @Autowired
    private IVersionTagSvc versionTagSvc;
    @Autowired
    private ESDiagramSvc diagramApiClient;
    @Resource
    private EamCategorySvc categorySvc;
    @Autowired
    private IEamFlowModelSvc flowModelSvc;
    @Autowired
    private IBmMultiModelHierarchySvc modelHierarchySvc;

    @Override
    public Integer createFlowModelTag(EamVersionTagDto dto) {
        SysUser user = SysUtil.getCurrentUserInfo();
        EamCategory modelRoot = categorySvc.getModelRoot(dto.getBranchId(), null, LibType.DESIGN);
        if (modelRoot != null) {
            //校验模型版本创建权限
            categorySvc.checkOperatorPermission(Arrays.asList(modelRoot.getId()),
                    AssetType.MODEL, user.getLoginCode(), LibType.DESIGN, OperatorType.VERSION_TAG);
        }
        EamMultiModelHierarchy model = modelHierarchySvc.getModelById(dto.getBranchId());
        String branchName = BinaryUtils.isEmpty(model)?dto.getTagName():model.getName();
        EamVersionTag tag = EamUtil.copy(dto, EamVersionTag.class);
        tag.setTagTime(ESUtil.getNumberDateTime(dto.getDate()));
        tag.setCreator(user.getLoginCode());
        tag.setDomainId(user.getDomainId());
        tag.setBranchName(branchName);
        tag.setDataStatus(1);
        Long tagId = versionTagSvc.saveOrUpdateTag(tag);
        //目录copy一份
        List<EamCategory> categoryList = categorySvc.selectByModelId(dto.getBranchId(), LibType.DESIGN, user.getLoginCode());
        List<Long> dirIds = categoryList.stream().map(EamCategory::getId).collect(Collectors.toList());
        List<ESDiagram> diagramList = diagramApiClient.selectByDirIds(dirIds, null, null, Lists.newArrayList(1));
        Map<Long, List<ESDiagram>> diagramDirGroup = diagramList.stream().collect(Collectors.groupingBy(ESDiagram::getDirId));
        Map<String, ESDiagram> diagramMap = diagramList.stream().collect(Collectors.toMap(ESDiagram::getDEnergy, each -> each, (k1, k2) -> k2));
        List<EamVersionDir> tagDirList = new ArrayList<>();
        for (EamCategory category : categoryList) {
            EamVersionDir tagDir = new EamVersionDir();
            tagDir.setTagId(tagId);
            tagDir.setDirId(category.getId());
            tagDir.setDirName(category.getDirName());
            tagDir.setDirLvl(category.getDirLvl());
            tagDir.setCiCode(category.getCiCode());
            tagDir.setDiagramId(category.getDiagramId());
            List<EamVersionDiagram> versionDiagramList = new ArrayList<>();
            List<ESDiagram> diagrams = diagramDirGroup.get(category.getId());
            if(!BinaryUtils.isEmpty(diagrams)){
                diagrams.forEach(each -> versionDiagramList.add(new EamVersionDiagram(each.getDEnergy(), each.getReleaseVersion())));
            }
            tagDir.setDiagramList(versionDiagramList);
            tagDir.setParentDirId(category.getParentId());
            tagDirList.add(tagDir);
        }
        return versionTagSvc.saveVersionDirBatch(tagDirList);
    }

    @Override
    public List<EamVersionTag> getHistoryVersion(Long branchId){
        List<EamVersionTag> versionTagList = versionTagSvc.getVersionTagByBranchId(branchId);
        versionTagList.sort(Comparator.comparing(EamVersionTag::getCreateTime));
        return versionTagList;
    }

    @Override
    public EamVersionTag getVersionById(Long id) {
        return versionTagSvc.getByTagId(id);
    }

    @Override
    public Integer deleteById(EamVersionTagDto dto) {
        EamVersionTag tag = versionTagSvc.getByTagId(dto.getId());
        if(BinaryUtils.isEmpty(tag)){
            return 1;
        }
        tag.setDataStatus(0);
        versionTagSvc.saveOrUpdateTag(tag);
        return 1;
    }
}
