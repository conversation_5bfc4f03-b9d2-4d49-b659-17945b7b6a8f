package com.uinnova.product.vmdb.provider.ci.bean;

import java.io.Serializable;
import java.util.List;

public class CiGroupPage implements Serializable {
	private static final long serialVersionUID = 1L;

	
	
	private long pageNum;
	private long pageSize;
	
	private long totalRows;
	private long totalPages;
	private long totalCiCount;
	private List<CcCiClassInfo> classInfos;
	private List<CcCiInfo> data;
	
	
	
	public CiGroupPage() {
	}



	public CiGroupPage(long pageNum, long pageSize, long totalRows, long totalPages, List<CcCiInfo> data) {
		this.pageNum = pageNum;
		this.pageSize = pageSize;
		this.totalRows = totalRows;
		this.totalPages = totalPages;
		this.data = data;
	}
	
	



	public long getPageNum() {
		return pageNum;
	}



	public void setPageNum(long pageNum) {
		this.pageNum = pageNum;
	}



	public long getPageSize() {
		return pageSize;
	}



	public void setPageSize(long pageSize) {
		this.pageSize = pageSize;
	}



	public long getTotalRows() {
		return totalRows;
	}



	public void setTotalRows(long totalRows) {
		this.totalRows = totalRows;
	}



	public long getTotalPages() {
		return totalPages;
	}



	public void setTotalPages(long totalPages) {
		this.totalPages = totalPages;
	}



	public List<CcCiClassInfo> getClassInfos() {
		return classInfos;
	}



	public void setClassInfos(List<CcCiClassInfo> classInfos) {
		this.classInfos = classInfos;
	}



	public List<CcCiInfo> getData() {
		return data;
	}



	public void setData(List<CcCiInfo> data) {
		this.data = data;
	}



	public long getTotalCiCount() {
		return totalCiCount;
	}



	public void setTotalCiCount(long totalCiCount) {
		this.totalCiCount = totalCiCount;
	}


	
	
	
	
	
	
	
	
}
