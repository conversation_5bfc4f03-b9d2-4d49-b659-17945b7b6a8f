package com.uinnova.product.eam.model;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.comm.model.DcCombDiagram;
import com.uinnova.product.eam.comm.model.VcDiagram;
import com.uinnova.product.eam.comm.model.VcDiagramCiAttrDisp;
import com.uinnova.product.eam.comm.model.VcDiagramEle;
import com.uinnova.product.eam.comm.model.VcDiagramGroup;
import com.uinnova.product.eam.comm.model.VcDiagramTag;
import com.uinnova.product.eam.comm.model.VcGroup;
import com.uinnova.product.eam.comm.model.VcTag;
import com.uino.bean.permission.base.SysUser;

public class VcDiagramInfo implements Serializable{
	private static final long serialVersionUID = 1L;
	
	@Comment("自动生成视图名字")
	private Boolean autoName;
	
	@Comment("视图基本信息")
	private VcDiagram diagram;

	@Comment("组合视图的信息")
	private List<DcCombDiagram> combDiagrams;
	
	@Comment("组合视图下单图的详细信息")
	private List<VcDiagramInfo> combDiagramInfos;
	
	@Comment("视图svg内容信息,用于生产缩略图")
	private String svg;
	
	@Comment("缩略图base64编码")
	private String thumbnail;

	@Comment("视图具体内容")
	private String xml;
	
	@Comment("视图json内容")
	private String json;

	@Comment("版本描述")
	private String versionDesc;
	
	@Comment("自定义版本号")
	private String versionNo;
	
	@Comment("视图关联元素信息")
	private List<VcDiagramEle> diagramEles;

	@Comment("视图自定义分类labels")
	private List<VcDiagramCiAttrDisp> ciAttrDisps;

	@Comment("视图标签关系")
	private List<VcDiagramTag> diagramTags;
	
	@Comment("标签")
	private List<VcTag> tags;
	
	@Comment("视图组信息")
	private List<VcDiagramGroup> diagramGroups;

	@Comment("视图组信息")
	private List<VcGroup> groups;

	@Comment("小组成员权限信息")
	private Map<Long,List<SysOpInfo>> groupUsers;
	
	@Comment("保存时默认放到小组的位置")
	private Long createGroupId;
	
	@Comment("视图作者信息")
	private SysUser creator;
	
	@Comment("版本更新来源  1：历史版本来源   2：自动更新来源")
	private Integer updateType;
	
	@Comment("更新的关系数据")
	private List<String> upRelations;
	
	@Comment("视图规则信息")
	private VcDiagramEle rltRule;
	
	@Comment("视图规则所使用的模版信息")
	private VcDiagramEle template;
	
	@Comment("是否是我收藏的视图：1=是  0：否")
	private Integer isCollection;
	
	@Comment("操作代码,当前用户对当前视图的操作类型")
	private Integer[] opTypes;
	
	@Comment("版本描述路径信息")
	private String versionDescPath;
	
	private Boolean seeAuth;
	
	private Boolean editAuth;

	public Map<Long, List<SysOpInfo>> getGroupUsers() {
		return groupUsers;
	}

	public void setGroupUsers(Map<Long, List<SysOpInfo>> groupUsers) {
		this.groupUsers = groupUsers;
	}

	public String getVersionDescPath() {
		return versionDescPath;
	}

	public void setVersionDescPath(String versionDescPath) {
		this.versionDescPath = versionDescPath;
	}

	public Integer[] getOpTypes() {
		return opTypes;
	}

	public void setOpTypes(Integer[] opTypes) {
		this.opTypes = opTypes;
	}

	public String getJson() {
		return json;
	}

	public void setJson(String json) {
		this.json = json;
	}

	public String getVersionNo() {
		return versionNo;
	}

	public void setVersionNo(String versionNo) {
		this.versionNo = versionNo;
	}

	public String getThumbnail() {
		return thumbnail;
	}

	public void setThumbnail(String thumbnail) {
		this.thumbnail = thumbnail;
	}
	
	public Integer getIsCollection() {
		return isCollection;
	}

	public void setIsCollection(Integer isCollection) {
		this.isCollection = isCollection;
	}

	public VcDiagramEle getTemplate() {
		return template;
	}

	public void setTemplate(VcDiagramEle template) {
		this.template = template;
	}

	public VcDiagramEle getRltRule() {
		return rltRule;
	}

	public void setRltRule(VcDiagramEle rltRule) {
		this.rltRule = rltRule;
	}

	public Boolean getAutoName() {
		return autoName;
	}

	public void setAutoName(Boolean autoName) {
		this.autoName = autoName;
	}

	public VcDiagram getDiagram() {
		return diagram;
	}

	public void setDiagram(VcDiagram diagram) {
		this.diagram = diagram;
	}

	public List<DcCombDiagram> getCombDiagrams() {
		return combDiagrams;
	}

	public void setCombDiagrams(List<DcCombDiagram> combDiagrams) {
		this.combDiagrams = combDiagrams;
	}

	public List<VcDiagramInfo> getCombDiagramInfos() {
		return combDiagramInfos;
	}

	public void setCombDiagramInfos(List<VcDiagramInfo> combDiagramInfos) {
		this.combDiagramInfos = combDiagramInfos;
	}

	public String getSvg() {
		return svg;
	}

	public void setSvg(String svg) {
		this.svg = svg;
	}

	public String getXml() {
		return xml;
	}

	public void setXml(String xml) {
		this.xml = xml;
	}

	public String getVersionDesc() {
		return versionDesc;
	}

	public void setVersionDesc(String versionDesc) {
		this.versionDesc = versionDesc;
	}

	public List<VcDiagramEle> getDiagramEles() {
		return diagramEles;
	}

	public void setDiagramEles(List<VcDiagramEle> diagramEles) {
		this.diagramEles = diagramEles;
	}

	public List<VcDiagramCiAttrDisp> getCiAttrDisps() {
		return ciAttrDisps;
	}

	public void setCiAttrDisps(List<VcDiagramCiAttrDisp> ciAttrDisps) {
		this.ciAttrDisps = ciAttrDisps;
	}

	public List<VcDiagramTag> getDiagramTags() {
		return diagramTags;
	}

	public void setDiagramTags(List<VcDiagramTag> diagramTags) {
		this.diagramTags = diagramTags;
	}

	public List<VcTag> getTags() {
		return tags;
	}

	public void setTags(List<VcTag> tags) {
		this.tags = tags;
	}

	public List<VcDiagramGroup> getDiagramGroups() {
		return diagramGroups;
	}

	public void setDiagramGroups(List<VcDiagramGroup> diagramGroups) {
		this.diagramGroups = diagramGroups;
	}

	public List<VcGroup> getGroups() {
		return groups;
	}

	public void setGroups(List<VcGroup> groups) {
		this.groups = groups;
	}

	public Long getCreateGroupId() {
		return createGroupId;
	}

	public void setCreateGroupId(Long createGroupId) {
		this.createGroupId = createGroupId;
	}

	public SysUser getCreator() {
		return creator;
	}

	public void setCreator(SysUser creator) {
		this.creator = creator;
	}

	public Integer getUpdateType() {
		return updateType;
	}

	public void setUpdateType(Integer updateType) {
		this.updateType = updateType;
	}

	public List<String> getUpRelations() {
		return upRelations;
	}

	public void setUpRelations(List<String> upRelations) {
		this.upRelations = upRelations;
	}

	public Boolean getSeeAuth() {
		return seeAuth;
	}

	public void setSeeAuth(Boolean seeAuth) {
		this.seeAuth = seeAuth;
	}

	public Boolean getEditAuth() {
		return editAuth;
	}

	public void setEditAuth(Boolean editAuth) {
		this.editAuth = editAuth;
	}
	
}
