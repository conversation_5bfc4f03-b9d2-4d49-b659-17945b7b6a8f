package com.uino.bean.cmdb.base;

import com.binary.framework.bean.annotation.Comment;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 *
 * <AUTHOR>
 *
 */
@Data
@ApiModel(value="可视化模型类",description = "可视化模型信息")
public class ESVisualModelVo extends ESVisualModel {

	private LibType libType;

	//是否是低版本
	private Boolean oldVersion = Boolean.FALSE;

	@Comment("3D全景配置Id")
	private Long modelPanorama3DId;
}
