package com.uino.dao.sys;

import jakarta.annotation.PostConstruct;

import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.uino.dao.AbstractESBaseDao;
import com.uino.dao.ESConst;
import com.uino.bean.sys.base.NotifyChannel;

/**
 * 通知渠道es访问层
 * 
 * <AUTHOR>
 *
 */
@Service
public class ESNotifyChannelSvc extends AbstractESBaseDao<NotifyChannel, JSONObject> {

    @Override
    public String getIndex() {
        // TODO Auto-generated method stub
        return ESConst.INDEX_SYS_NOTIFY_CHANNEL;
    }

    @Override
    public String getType() {
        // TODO Auto-generated method stub
        return ESConst.INDEX_SYS_NOTIFY_CHANNEL;
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }
}
