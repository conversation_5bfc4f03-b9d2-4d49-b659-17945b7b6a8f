package com.uino.bean.cmdb.base.dataset.query;

import com.uino.bean.cmdb.base.dataset.DataSetMallApiRelationRule;
import com.uino.bean.cmdb.business.dataset.FriendInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

/**
 * @Title: DataSetMallApiRelationRuleDto
 * @Description:
 * @Author: YGQ
 * @Create: 2021-05-31 13:57
 **/
@Getter
@Setter
@ApiModel(value = "数据超市", description = "数据超市")
public class DataSetMallApiRelationRuleDto {

    @ApiModelProperty(value = "数据超市关联规则")
    private DataSetMallApiRelationRule dataSetRelationRule;

    @ApiModelProperty(value = "朋友圈数据")
    private Map<Long, FriendInfo> friendInfoMap;
}
