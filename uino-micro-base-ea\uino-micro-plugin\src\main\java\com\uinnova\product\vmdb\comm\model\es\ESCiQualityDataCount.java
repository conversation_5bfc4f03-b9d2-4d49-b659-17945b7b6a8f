package com.uinnova.product.vmdb.comm.model.es;

import java.util.List;
import java.util.Map;

public class ESCiQualityDataCount {

    private static final long serialVersionUID = 1L;

    private String id;

    /**
     * key=来源；value=数量
     */
    private Map<String, Integer> problemCounts;

    private Map<String, List<String>> problemTypes;

    private Map<String, List<String>> classNames;

    private Map<String, List<String>> rltClassNames;

    private Map<String, List<String>> oppositeClassNames;

    private Map<String, Double> originalOrphanHealth;

    private Map<String, Double> originalValidityHealth;

    private Map<String, Double> originalCompletenessHealth;

    private Long time;

    private Long domainId;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Map<String, Integer> getProblemCounts() {
        return problemCounts;
    }

    public void setProblemCounts(Map<String, Integer> problemCounts) {
        this.problemCounts = problemCounts;
    }

    public Map<String, List<String>> getProblemTypes() {
        return problemTypes;
    }

    public void setProblemTypes(Map<String, List<String>> problemTypes) {
        this.problemTypes = problemTypes;
    }

    public Map<String, List<String>> getClassNames() {
        return classNames;
    }

    public void setClassNames(Map<String, List<String>> classNames) {
        this.classNames = classNames;
    }

    public Map<String, List<String>> getRltClassNames() {
        return rltClassNames;
    }

    public void setRltClassNames(Map<String, List<String>> rltClassNames) {
        this.rltClassNames = rltClassNames;
    }

    public Map<String, List<String>> getOppositeClassNames() {
        return oppositeClassNames;
    }

    public void setOppositeClassNames(Map<String, List<String>> oppositeClassNames) {
        this.oppositeClassNames = oppositeClassNames;
    }

    public Map<String, Double> getOriginalOrphanHealth() {
        return originalOrphanHealth;
    }

    public void setOriginalOrphanHealth(Map<String, Double> originalOrphanHealth) {
        this.originalOrphanHealth = originalOrphanHealth;
    }

    public Map<String, Double> getOriginalValidityHealth() {
        return originalValidityHealth;
    }

    public void setOriginalValidityHealth(Map<String, Double> originalValidityHealth) {
        this.originalValidityHealth = originalValidityHealth;
    }

    public Map<String, Double> getOriginalCompletenessHealth() {
        return originalCompletenessHealth;
    }

    public void setOriginalCompletenessHealth(Map<String, Double> originalCompletenessHealth) {
        this.originalCompletenessHealth = originalCompletenessHealth;
    }

    public Long getTime() {
        return time;
    }

    public void setTime(Long time) {
        this.time = time;
    }

    public Long getDomainId() {
        return domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }
}
