package com.uino.api.client.cmdb;


/**
 * 任务锁的相关服务接口
 *
 * <AUTHOR>
 * @version 2020-4-24
 */
public interface ITaskLockApiSvc {

    /**
     * 获取任务锁，分布式部署时避免定时任务重复执行，只有成功拿到任务锁才允许任务执行
     *
     * @param taskName 任务名称
     * @param taskExecuteTime 任务执行时长
     * @return 是否成功拿到锁
     */
    boolean getLock(String taskName, long taskExecuteTime);

    /**
     * 解除指定任务锁
     *
     * @param taskName  任务锁名称
     */
    void breakLock(String taskName);

    /**
     * 增加任务锁
     *
     * @param taskName 任务名称
     * @return 新增:true,更新:false
     */
    boolean addLock(String taskName);

    /**
     * 判断某个任务是否处于锁定中
     * @param taskName 任务名称
     * @return true: 锁定, false: 未锁定
     */
    boolean isLocked(String taskName);

}
