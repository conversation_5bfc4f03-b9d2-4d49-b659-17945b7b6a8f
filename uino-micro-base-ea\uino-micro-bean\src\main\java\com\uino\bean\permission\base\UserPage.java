package com.uino.bean.permission.base;

import com.binary.jdbc.Page;

import java.util.List;

public class UserPage<E> extends Page<E> {

    public UserPage(long pageNum, long pageSize, long totalRows, long totalPages, List<E> data){
        super(pageNum, pageSize,  totalRows,  totalPages,  data);
    }

    private Integer totalOnlineNum = 0;

    public Integer getTotalOnlineNum() {
        return totalOnlineNum;
    }

    public void setTotalOnlineNum(Integer totalOnlineNum) {
        this.totalOnlineNum = totalOnlineNum;
    }
}
