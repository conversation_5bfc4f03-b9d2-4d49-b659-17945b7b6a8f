package com.uinnova.product.eam.service;

import com.uinnova.product.eam.model.vo.ESCISearchBeanVO;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CiGroupPage;

/**
 * 流程表单
 *
 * <AUTHOR>
 * @since 2024/6/26 下午1:49
 */
public interface FlowFormService {

    CcCiClassInfo queryCIClassByCode(String classCode);

    CiGroupPage queryFlowFormList(ESCISearchBeanVO esciSearchBeanVO);
}
