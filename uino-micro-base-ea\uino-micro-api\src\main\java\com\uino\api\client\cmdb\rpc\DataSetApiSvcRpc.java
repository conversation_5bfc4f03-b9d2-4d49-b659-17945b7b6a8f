package com.uino.api.client.cmdb.rpc;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.binary.core.io.Resource;
import com.uino.api.client.cmdb.IDataSetApiSvc;
import com.uino.bean.cmdb.base.dataset.DataSetMallApi;
import com.uino.bean.cmdb.base.dataset.DataSetMallApiRelationRule;
import com.uino.bean.cmdb.base.dataset.DataSetMallApiType;
import com.uino.bean.cmdb.base.dataset.batch.DataSetTableResult;
import com.uino.bean.cmdb.base.dataset.chart.Chart;
import com.uino.bean.cmdb.base.dataset.query.DataSetMallApiDto;
import com.uino.bean.cmdb.base.dataset.query.DataSetMallApiRelationRuleDto;
import com.uino.bean.cmdb.business.dataset.*;
import com.uino.bean.dataset.base.DataSetPathInfoVo;
import com.uino.bean.dataset.base.DataSetThumbnailDTO;
import com.uino.bean.tp.query.MetricAttrValQueryDTO;
import com.uino.bean.tp.query.QueryDataTableDTO;
import com.uino.bean.tp.query.TpRuleReqDTO;
import com.uino.dao.BaseConst;
import com.uino.provider.feign.cmdb.DataSetFeign;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Service
public class DataSetApiSvcRpc implements IDataSetApiSvc {

    @Autowired
    private DataSetFeign datasetFeign;

    @Override
    public Long saveOrUpdateDataSet(JSONObject json, Boolean ifExeBatchProcess) {
        JSONObject body = new JSONObject();
        body.put("json", json);
        body.put("ifExeBatchProcess", ifExeBatchProcess);
        return datasetFeign.saveOrUpdateDataSet(body);
    }

    @Override
    public boolean delete(Long id) {
        return datasetFeign.delete(id);
    }

    @Override
    public List<JSONObject> findDataSet(String name, boolean isMyself, String url, List<DataSetMallApiType> typeList) {
        JSONObject body = new JSONObject();
        body.put("name", name);
        body.put("isMyself", isMyself);
        body.put("url", url);
        body.put("typeList",typeList);
        return datasetFeign.findDataSet(body);
    }


    @Override
    public JSONObject findDataSetById(Long id) {
        JSONObject body = new JSONObject();
        body.put("id", id);
        body.put("isCheck", true);
        return datasetFeign.findDataSetById(body);
    }

    @Override
    public JSONObject findDataSetById(Long id, boolean isCheck) {
        JSONObject body = new JSONObject();
        body.put("id", id);
        body.put("isCheck", isCheck);
        return datasetFeign.findDataSetById(body);
    }

    @Override
    public JSONObject execute(String language, Long id, JSONObject body) {
        JSONObject b = new JSONObject();
        b.put("domainId", BaseConst.DEFAULT_DOMAIN_ID);
        b.put("language", language);
        b.put("id", id);
        b.put("body", body);
        return datasetFeign.execute(b);
    }

    @Override
    public JSONObject execute(Long domainId, String language, Long id, JSONObject body) {
        JSONObject b = new JSONObject();
        b.put("domainId", domainId);
        b.put("language", language);
        b.put("id", id);
        b.put("body", body);
        return datasetFeign.execute(b);
    }

    @Override
    public JSONObject execute(JSONObject body) {
        JSONObject b = new JSONObject();
        b.put("domainId", BaseConst.DEFAULT_DOMAIN_ID);
        b.put("body", body);
        return datasetFeign.execute(b);
    }

    @Override
    public JSONObject realTimeExecute(String language, Long id, JSONObject body) {
        JSONObject b = new JSONObject();
        b.put("domainId", BaseConst.DEFAULT_DOMAIN_ID);
        b.put("language", language);
        b.put("id", id);
        b.put("body", body);
        return datasetFeign.realTimeExecute(b);
    }

    @Override
    public JSONObject realTimeExecute(Long domainId, String language, Long id, JSONObject body) {
        JSONObject b = new JSONObject();
        b.put("domainId", domainId);
        b.put("language", language);
        b.put("id", id);
        b.put("body", body);
        return datasetFeign.realTimeExecute(b);
    }

    @Override
    public boolean shareDataSet(Long id, int shareLevel) {
        JSONObject body = new JSONObject();
        body.put("id", id);
        body.put("shareLevel", shareLevel);
        return datasetFeign.shareDataSet(body);
    }

    @Override
    public List<DataSetMallApiRelationRule> findAllRelationDateSet() {
        return datasetFeign.findAllRelationDateSet(BaseConst.DEFAULT_DOMAIN_ID);
    }

    @Override
    public List<DataSetMallApiRelationRule> findAllRelationDateSet(Long domainId) {
        return datasetFeign.findAllRelationDateSet(domainId);
    }

    @Override
    public void updateMallApiExeResult(DataSetMallApiRelationRule dataSetRelationRule, Map<Long, FriendInfo> friendInfoMap) {
        DataSetMallApiRelationRuleDto dataSetMallApiRelationRuleDto = new DataSetMallApiRelationRuleDto();
        dataSetMallApiRelationRuleDto.setDataSetRelationRule(dataSetRelationRule);
        dataSetMallApiRelationRuleDto.setFriendInfoMap(friendInfoMap);
        datasetFeign.updateMallApiExeResult(dataSetMallApiRelationRuleDto);
    }

    @Override
    public List<Map<String, Object>> getDataSetSheets(Long dataSetId) {
        return datasetFeign.getDataSetSheets(dataSetId);
    }

    @Override
    public Map<String, Long> getDataSetLineCount(List<String> dataSetIds) {
        //暂时用不到
        return null;
    }

    @Override
    public JSONObject queryRuleLegitimateCi(Integer pageNum, Integer pageSize, Long dataSetId, String name, String like) {
        JSONObject body = new JSONObject();
        body.put("pageNum", pageNum);
        body.put("pageSize", pageSize);
        body.put("dataSetId", dataSetId);
        body.put("name", name);
        body.put("like", like);
        return datasetFeign.queryRuleLegitimateCi(body);
    }

    @Override
    public FriendInfo queryFriendByStartCiId(Long dataSetId, Long startCiId) {
        JSONObject body = new JSONObject();
        body.put("dataSetId", dataSetId);
        body.put("startCiId", startCiId);
        return datasetFeign.queryFriendByStartCiId(body);
    }

    @Override
    public FriendInfo queryFriendByStartCiIdAndTargetClass(Long dataSetId, Long startCiId, Set<Long> targetClassIds) {
        JSONObject body = new JSONObject();
        body.put("dataSetId", dataSetId);
        body.put("startCiId", startCiId);
        body.put("targetClassIds", targetClassIds);
        return datasetFeign.queryFriendByStartCiIdAndTargetClass(body);
    }

    @Override
    public Map<Long,Integer> queryRuleNodeCiNum(Long dataSetId) {
        JSONObject body = new JSONObject();
        body.put("dataSetId", dataSetId);
        return datasetFeign.queryRuleNodeCiNum(body);
    }

    @Override
    public RltRuleTableData queryDisassembleFriendInfoDataByPath(Long dataSetId, Long startCiId) {
        JSONObject body = new JSONObject();
        body.put("dataSetId", dataSetId);
        body.put("startCiId", startCiId);
        return datasetFeign.queryDisassembleFriendInfoDataByPath(body);
    }

    @Override
    public JSONObject countStatistics(Long dataSetId, Chart chart) {
        JSONObject body = new JSONObject();
        body.put("dataSetId", dataSetId);
        body.put("chart", chart);
        return datasetFeign.countStatistics(body);
    }

    @Override
    public List<JSONObject> getQueryCondition(Long dataSetId, String sheetId) {
        JSONObject body = new JSONObject();
        body.put("dataSetId", dataSetId);
        body.put("sheetId", sheetId);
        return datasetFeign.getQueryCondition(body);
    }

    @Override
    public DataSetExeResultSheetPage queryDataSetResultBySheet(Long dataSetId, String sheetId, int pageNum,
                                                               int pageSize, String sortCol, boolean isDesc, JSONArray condition, String userCode) {
        JSONObject body = new JSONObject();
        body.put("domainId", BaseConst.DEFAULT_DOMAIN_ID);
        body.put("dataSetId", dataSetId);
        body.put("sheetId", sheetId);
        body.put("pageNum", pageNum);
        body.put("pageSize", pageSize);
        body.put("sortCol", sortCol);
        body.put("isDesc", isDesc);
        body.put("condition", condition);
        body.put("userCode", userCode);
        return datasetFeign.queryDataSetResultBySheet(body);
    }

    @Override
    public List<DataSetExeResultSheetPage> queryDataSetResultList(List<Long> dataSetIds, String sheetId, String sortCol, boolean isDesc) {
        JSONObject body = new JSONObject();
        body.put("dataSetIds", dataSetIds);
        body.put("sheetId", sheetId);
        body.put("sortCol", sortCol);
        body.put("isDesc", isDesc);
        return datasetFeign.queryDataSetResultList(body);
    }

    @Override
    public DataSetExeResultSheetPage queryDataSetResultBySheet(Long domainId, Long dataSetId, String sheetId, int pageNum, int pageSize, String sortCol, boolean isDesc, JSONArray condition, String userCode) {
        JSONObject body = new JSONObject();
        body.put("domainId", domainId);
        body.put("dataSetId", dataSetId);
        body.put("sheetId", sheetId);
        body.put("pageNum", pageNum);
        body.put("pageSize", pageSize);
        body.put("sortCol", sortCol);
        body.put("isDesc", isDesc);
        body.put("condition", condition);
        body.put("userCode", userCode);
        return datasetFeign.queryDataSetResultBySheet(body);
    }

    @Override
    public List<DataSetExeResultSheetPage> getResultUsingRule(JSONObject rule, Integer limit) {
        JSONObject body = new JSONObject();
        body.put("rule", rule);
        body.put("limit", limit);
        return datasetFeign.getResultUsingRule(body);
    }

    @Override
    public Resource downloadSheetData(Long dataSetId, String sheetId, String sortCol, boolean isDesc,
                                      JSONArray condition) {
        JSONObject body = new JSONObject();
        body.put("domainId", BaseConst.DEFAULT_DOMAIN_ID);
        body.put("dataSetId", dataSetId);
        body.put("sheetId", sheetId);
        body.put("sortCol", sortCol);
        body.put("isDesc", isDesc);
        body.put("condition", condition);
        return datasetFeign.downloadSheetData(body);
    }

    @Override
    public Resource downloadSheetData(Long domainId, Long dataSetId, String sheetId, String sortCol, boolean isDesc, JSONArray condition) {
        JSONObject body = new JSONObject();
        body.put("domainId", domainId);
        body.put("dataSetId", dataSetId);
        body.put("sheetId", sheetId);
        body.put("sortCol", sortCol);
        body.put("isDesc", isDesc);
        body.put("condition", condition);
        return datasetFeign.downloadSheetData(body);
    }

    @Override
    public int isTaskRunning(Long dataSetId) {
        return datasetFeign.isTaskRunning(dataSetId);
    }

    @Override
    public String getCode(String jsonStr) throws Exception {
        return datasetFeign.getCode(jsonStr);
    }

    @Override
    public void checkOperate(String userCode, DataSetMallApi dataSetMallApi) {
        DataSetMallApiDto dataSetMallApiDto = new DataSetMallApiDto();
        dataSetMallApiDto.setUserCode(userCode);
        dataSetMallApiDto.setDataSetMallApi(dataSetMallApi);
        datasetFeign.checkOperate(dataSetMallApiDto);
    }

    @Override
    public List<Map<String, Object>> groupDataSetMallApiLogCount() {
        return datasetFeign.groupDataSetMallApiLogCount(BaseConst.DEFAULT_DOMAIN_ID);
    }

    @Override
    public List<Map<String, Object>> groupDataSetMallApiLogCount(Long domainId) {
        return datasetFeign.groupDataSetMallApiLogCount(domainId);
    }

    @Override
    public List<String> getTpMetricLabelDTOList(TpRuleReqDTO ruleReqDTO) {
        return datasetFeign.getTpMetricLabelDTOList(ruleReqDTO);
    }

    @Override
    public List<String> queryMetricAttrValue(MetricAttrValQueryDTO query) {
        return datasetFeign.queryMetricAttrValue(query);
    }

    @Override
    public FriendBatchInfo queryFriendByStartCiIds(List<FriendInfoRequestDto> body) {
        return datasetFeign.queryFriendByStartCiIds(body);
    }

    @Override
    public List<DataSetExeResultSheetPage> findDataSetRuleList(JSONObject rule, Integer limit) {
        JSONObject body = new JSONObject();
        body.put("rule", rule);
        body.put("limit", limit);
        return datasetFeign.findPathList(body);
    }

    @Override
    public String updateThumbnail(DataSetThumbnailDTO body) {
        return datasetFeign.updateThumbnail(body);
    }

    @Override
    public List<DataSetPathInfoVo> getPathInfo(List<Long> dataSetId) {
        return datasetFeign.getPathInfo(dataSetId);
    }

    @Override
    public void updateDataSetById(Long id) {
        datasetFeign.updateDataSetById(id);
    }

    @Override
    public List<DataSetTableResult> queryCiTableList(QueryDataTableDTO queryDataTableDTO) {
        return datasetFeign.queryCiTableList(queryDataTableDTO);
    }

    @Override
    public ResponseEntity<byte[]> ciTableListExport(List<DataSetTableResult> ret, QueryDataTableDTO body) {
        return datasetFeign.ciTableListExport(ret, body);
    }
}
