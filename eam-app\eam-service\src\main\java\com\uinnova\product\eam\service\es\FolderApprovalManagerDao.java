package com.uinnova.product.eam.service.es;

import com.uinnova.product.eam.comm.model.es.FolderApprovalManager;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;

/**
 * 文件夹审批用户配置
 * <AUTHOR>
 */
@Service
public class FolderApprovalManagerDao extends AbstractESBaseDao<FolderApprovalManager, FolderApprovalManager> {
    @Override
    public String getIndex() {
        return "uino_eam_folder_approval_manager";
    }

    @Override
    public String getType() {
        return "_doc";
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }
}
