<?xml version="1.0" encoding="UTF-8"?><!--Converted at: Sat Dec 08 16:22:33 CST 2018-->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="VC_DIAGRAM_CI_ATTR_DISP">


	<resultMap id="queryResult" type="com.uinnova.product.eam.comm.model.VcDiagramCiAttrDisp">
		<result property="id" column="ID" jdbcType="BIGINT"/>	<!-- ID -->
		<result property="diagramId" column="DIAGRAM_ID" jdbcType="BIGINT"/>	<!-- 视图ID -->
		<result property="objType" column="OBJ_TYPE" jdbcType="INTEGER"/>	<!-- 配置类型 -->
		<result property="objId" column="OBJ_ID" jdbcType="VARCHAR"/>	<!-- 配置对象 -->
		<result property="attrDefId" column="ATTR_DEF_ID" jdbcType="BIGINT"/>	<!-- 属性定义ID -->
		<result property="domainId" column="DOMAIN_ID" jdbcType="BIGINT"/>	<!-- 所属域 -->
		<result property="createTime" column="CREATE_TIME" jdbcType="BIGINT"/>	<!-- 创建时间 -->
		<result property="modifyTime" column="MODIFY_TIME" jdbcType="BIGINT"/>	<!-- 修改时间 -->
	</resultMap>
	

	<sql id="sql_query_where">
		<if test="cdt != null and cdt.id != null">and
			ID = #{cdt.id:BIGINT}
		</if>
		<if test="ids != null and ids != ''">and
			ID in (${ids})
		</if>
		<if test="cdt != null and cdt.startId != null">and
			 ID &gt;= #{cdt.startId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endId != null">and
			 ID &lt;= #{cdt.endId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.diagramId != null">and
			DIAGRAM_ID = #{cdt.diagramId:BIGINT}
		</if>
		<if test="diagramIds != null and diagramIds != ''">and
			DIAGRAM_ID in (${diagramIds})
		</if>
		<if test="cdt != null and cdt.startDiagramId != null">and
			 DIAGRAM_ID &gt;= #{cdt.startDiagramId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endDiagramId != null">and
			 DIAGRAM_ID &lt;= #{cdt.endDiagramId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.objType != null">and
			OBJ_TYPE = #{cdt.objType:INTEGER}
		</if>
		<if test="objTypes != null and objTypes != ''">and
			OBJ_TYPE in (${objTypes})
		</if>
		<if test="cdt != null and cdt.startObjType != null">and
			 OBJ_TYPE &gt;= #{cdt.startObjType:INTEGER} 
		</if>
		<if test="cdt != null and cdt.endObjType != null">and
			 OBJ_TYPE &lt;= #{cdt.endObjType:INTEGER} 
		</if>
		<if test="cdt != null and cdt.objId != null and cdt.objId != ''">and
			OBJ_ID like #{cdt.objId,jdbcType=VARCHAR} 
		</if>
		<if test="cdt != null and cdt.objIdEqual != null and cdt.objIdEqual != ''">and
			OBJ_ID = #{cdt.objIdEqual,jdbcType=VARCHAR}
		</if>
		<if test="objIds != null and objIds != ''">and
			OBJ_ID in (${objIds})
		</if>
		<if test="cdt != null and cdt.attrDefId != null">and
			ATTR_DEF_ID = #{cdt.attrDefId:BIGINT}
		</if>
		<if test="attrDefIds != null and attrDefIds != ''">and
			ATTR_DEF_ID in (${attrDefIds})
		</if>
		<if test="cdt != null and cdt.startAttrDefId != null">and
			 ATTR_DEF_ID &gt;= #{cdt.startAttrDefId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endAttrDefId != null">and
			 ATTR_DEF_ID &lt;= #{cdt.endAttrDefId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.domainId != null">and
			DOMAIN_ID = #{cdt.domainId:BIGINT}
		</if>
		<if test="domainIds != null and domainIds != ''">and
			DOMAIN_ID in (${domainIds})
		</if>
		<if test="cdt != null and cdt.startDomainId != null">and
			 DOMAIN_ID &gt;= #{cdt.startDomainId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endDomainId != null">and
			 DOMAIN_ID &lt;= #{cdt.endDomainId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.createTime != null">and
			CREATE_TIME = #{cdt.createTime:BIGINT}
		</if>
		<if test="createTimes != null and createTimes != ''">and
			CREATE_TIME in (${createTimes})
		</if>
		<if test="cdt != null and cdt.startCreateTime != null">and
			 CREATE_TIME &gt;= #{cdt.startCreateTime:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endCreateTime != null">and
			 CREATE_TIME &lt;= #{cdt.endCreateTime:BIGINT} 
		</if>
		<if test="cdt != null and cdt.modifyTime != null">and
			MODIFY_TIME = #{cdt.modifyTime:BIGINT}
		</if>
		<if test="modifyTimes != null and modifyTimes != ''">and
			MODIFY_TIME in (${modifyTimes})
		</if>
		<if test="cdt != null and cdt.startModifyTime != null">and
			 MODIFY_TIME &gt;= #{cdt.startModifyTime:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endModifyTime != null">and
			 MODIFY_TIME &lt;= #{cdt.endModifyTime:BIGINT} 
		</if>
	</sql>
	

	<sql id="sql_update_columns">
		<if test="record != null and record.id != null"> 
			ID = #{record.id:BIGINT}
		,</if>
		<if test="record != null and record.diagramId != null"> 
			DIAGRAM_ID = #{record.diagramId:BIGINT}
		,</if>
		<if test="record != null and record.objType != null"> 
			OBJ_TYPE = #{record.objType:INTEGER}
		,</if>
		<if test="record != null and record.objId != null"> 
			OBJ_ID = #{record.objId,jdbcType=VARCHAR}
		,</if>
		<if test="record != null and record.attrDefId != null"> 
			ATTR_DEF_ID = #{record.attrDefId:BIGINT}
		,</if>
		<if test="record != null and record.domainId != null"> 
			DOMAIN_ID = #{record.domainId:BIGINT}
		,</if>
		<if test="record != null and record.createTime != null"> 
			CREATE_TIME = #{record.createTime:BIGINT}
		,</if>
		<if test="record != null and record.modifyTime != null"> 
			MODIFY_TIME = #{record.modifyTime:BIGINT}
		,</if>
	</sql>
	

	<sql id="sql_query_columns">
		ID, DIAGRAM_ID, OBJ_TYPE, OBJ_ID, ATTR_DEF_ID, DOMAIN_ID, 
		CREATE_TIME, MODIFY_TIME
	</sql>
	

	

	<select id="selectList" parameterType="java.util.Map" resultMap="queryResult">
		select 
			<include refid="VC_DIAGRAM_CI_ATTR_DISP.sql_query_columns"/>
		from VC_DIAGRAM_CI_ATTR_DISP 
			<where>
				<include refid="VC_DIAGRAM_CI_ATTR_DISP.sql_query_where"/>
			</where>
		order by 
			<if test="orders != null and orders != ''">
				${orders}
			</if>
			<if test="orders == null or orders == ''">
				ID
			</if>
	</select>
	<select id="selectCount" parameterType="java.util.Map" resultType="java.lang.Long">
		select count(1) from VC_DIAGRAM_CI_ATTR_DISP 
			<where>
				<include refid="VC_DIAGRAM_CI_ATTR_DISP.sql_query_where"/>
			</where>
	</select>
	<select id="selectById" parameterType="java.util.Map" resultMap="queryResult">
		select 
			<include refid="VC_DIAGRAM_CI_ATTR_DISP.sql_query_columns"/>
		from VC_DIAGRAM_CI_ATTR_DISP where ID=#{id:BIGINT} 
	</select>
	

	

	<insert id="insert" parameterType="java.util.Map">
		insert into VC_DIAGRAM_CI_ATTR_DISP(
			ID, DIAGRAM_ID, OBJ_TYPE, OBJ_ID, ATTR_DEF_ID, 
			DOMAIN_ID, CREATE_TIME, MODIFY_TIME)
		values (
			#{record.id:BIGINT}, #{record.diagramId:BIGINT}, #{record.objType:INTEGER}, #{record.objId,jdbcType=VARCHAR}, #{record.attrDefId:BIGINT}, 
			#{record.domainId:BIGINT}, #{record.createTime:BIGINT}, #{record.modifyTime:BIGINT})
	</insert>
	

	

	<update id="updateById" parameterType="java.util.Map">
		update VC_DIAGRAM_CI_ATTR_DISP
			<set> 
				<include refid="VC_DIAGRAM_CI_ATTR_DISP.sql_update_columns"/> 
			</set>
		where ID = #{id:BIGINT}
	</update>
	<update id="updateByCdt" parameterType="java.util.Map">
		update VC_DIAGRAM_CI_ATTR_DISP
			<set> 
				<include refid="VC_DIAGRAM_CI_ATTR_DISP.sql_update_columns"/> 
			</set>
			<where> 
				<include refid="VC_DIAGRAM_CI_ATTR_DISP.sql_query_where"/> 
			</where>
	</update>
	
	

	

	<delete id="deleteById" parameterType="java.util.Map">
		delete from VC_DIAGRAM_CI_ATTR_DISP where ID = #{id:BIGINT}
	</delete>
	<delete id="deleteByCdt" parameterType="java.util.Map">
		delete from VC_DIAGRAM_CI_ATTR_DISP
			<where> 
				<include refid="VC_DIAGRAM_CI_ATTR_DISP.sql_query_where"/> 
			</where>
	</delete>
	



</mapper>