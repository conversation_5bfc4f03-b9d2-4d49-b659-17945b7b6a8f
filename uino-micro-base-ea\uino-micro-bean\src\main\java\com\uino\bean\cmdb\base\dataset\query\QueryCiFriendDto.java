package com.uino.bean.cmdb.base.dataset.query;

import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uino.bean.cmdb.base.dataset.DataSetMallApiRelationRule;
import com.uino.bean.cmdb.base.dataset.batch.SimpleFriendInfo;
import com.uino.bean.cmdb.base.dataset.chart.Chart;
import com.uino.bean.cmdb.business.dataset.FriendInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * @Title: QueryCiFriendDto
 * @Description: QueryCiFriendDto
 * @Author: YGQ
 * @Create: 2021-05-31 14:36
 **/
@Getter
@Setter
@Builder
@ApiModel(value = "数据超市", description = "数据超市")
public class QueryCiFriendDto {

    @ApiModelProperty(value = "数据集关联API")
    private DataSetMallApiRelationRule relationRule;

    @ApiModelProperty(value = "CI信息")
    private CcCiInfo sCi;

    @ApiModelProperty(value = "数据集ID")
    private Long dataSetId;

    @ApiModelProperty(value = "CI信息集合")
    private List<CcCiInfo> sCis;

    @ApiModelProperty(value = "是否包括全部源CI")
    private Boolean isIncludeAllStartCI;

    @ApiModelProperty(value = "限制")
    private Integer limit;

    @ApiModelProperty(value = "朋友圈信息集合")
    private Map<Long, FriendInfo> friendInfoMap;

    @ApiModelProperty(value = "Ci标识集合")
    private List<Long> ciIds;

    @ApiModelProperty(value = "朋友圈信息")
    private FriendInfo friendInfo;

    @ApiModelProperty(value = "简略朋友圈信息")
    private Map<Long, SimpleFriendInfo> simpleFriendInfoMap;

    @ApiModelProperty(value = "图表")
    private Chart chart;






}
