package com.uinnova.product.eam.service;

import com.uinnova.product.eam.model.AppStrategyDto;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uino.bean.cmdb.base.LibType;

import java.util.HashMap;
import java.util.List;

/**
 * 应用架构服务的接口类
 *
 * <AUTHOR>
 * @version 2020/7/28
 */
public interface IAppArchitectureSvc {

    /**
     * 获取应用的整体策略
     *
     * @param libType 对象库类型
     * @return 应用策略信息列表
     */
    List<AppStrategyDto> getAppStrategy(LibType libType);

    /**
     * 业务能力/人员矩阵
     *
     * @param libType 库类型
     * @return 执行结果
     */
    HashMap<String, Object> getStaffMatrixView(LibType libType);

    /**
     * 业务能力/策略矩阵
     *
     * @param libType 仓库类型
     * @return 业务能力/策略矩阵的图数据
     */
    HashMap<String, Object> getStrategyMatrixView(LibType libType);

    /**
     * 获取时间轴线图
     * @param libType
     * @return
     */
    List<CcCiInfo> getTimeAxisView(LibType libType);

    /**
     * 保存或更新应用系统信息,并创建对应架构设计文件夹
     * @param ciInfo
     * @param libType
     * @return
     */
    Long saveOrUpdateAppInfo(CcCiInfo ciInfo);
}
