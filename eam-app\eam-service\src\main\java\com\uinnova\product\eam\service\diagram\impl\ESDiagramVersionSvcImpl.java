package com.uinnova.product.eam.service.diagram.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.crypto.SecureUtil;
import com.binary.core.exception.BinaryException;
import com.binary.core.util.BinaryUtils;
import com.binary.jdbc.Page;
import com.uinnova.product.eam.base.diagram.exception.UnAuthorizedException;
import com.uinnova.product.eam.base.diagram.model.*;
import com.uinnova.product.eam.base.diagram.utils.FileFilterUtil;
import com.uinnova.product.eam.db.VcDiagramVersionDao;
import com.uinnova.product.eam.db.diagram.es.*;
import com.uinnova.product.eam.service.diagram.ESDiagramSvc;
import com.uinnova.product.eam.service.diagram.ESDiagramVersionSvc;
import com.uinnova.product.eam.service.diagram.IEamShareDiagramSvc;
import com.uinnova.product.vmdb.comm.i18n.MessageUtil;
import com.uinnova.product.vmdb.comm.i18n.VerifyType;
import com.uino.api.client.permission.IUserApiSvc;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.permission.business.UserInfo;
import com.uino.bean.permission.query.CSysUser;
import com.uino.dao.util.ESUtil;
import com.uino.service.sys.microservice.IResourceSvc;
import com.uino.service.util.FileUtil;
import com.uino.tarsier.tarsiercom.util.IdGenerator;
import com.uino.util.rsm.RsmUtils;
import com.uino.util.sys.SysUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.TermsQueryBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.File;
import java.io.IOException;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

@Deprecated
@Service
@Slf4j
public class ESDiagramVersionSvcImpl implements ESDiagramVersionSvc {

    private static final Long SYS_DOMAIN_ID = 1L;
    @Autowired
    private VcDiagramVersionDao diagramVersionDao;

    @Autowired
    private ESDiagramDao esDiagramDao;

    @Autowired
    private ESDiagramSvc esDiagramSvc;

    @Autowired
    private ESDiagramSheetDao esDiagramSheetDao;

    @Autowired
    private ESDiagramNodeDao esDiagramNodeDao;

    @Autowired
    private ESDiagramLinkDao esDiagramLinkDao;

    @Autowired
    private IEamShareDiagramSvc eamShareDiagramSvc;

    @Autowired
    private IUserApiSvc userApiSvc;

    @Autowired
    private ESShareLinkDao shareLinkDao;

    @Autowired(required = false)
    private IResourceSvc resourceSvc;

    @Autowired
    private RsmUtils rsmUtils;

    @Value("${local.resource.space}")
    private String localPath;

    private static final Logger logger = LoggerFactory.getLogger(ESDiagramSvcImpl.class);

    private final static long MAX_QUERY_COUNT = 30000;

    private final static long PAGE_COUNT = 3000;

    public static final String FILE_SEPARATOR = System.getProperty("file.separator");
    @Value("${http.resource.space}")
    private String httpResouceUrl;

    public static ExecutorService executor = Executors.newFixedThreadPool(10);

    /**
     * <AUTHOR>
     * @Description 生成视图的历史记录
     **/

    private Long saveDiagramVersionESRecord(ESDiagram currDiagram, Long newDiagramId, SysUser currUser, SysUser lastModifier) {
        return saveDiagramVersionESRecord(currDiagram, newDiagramId, currUser, lastModifier, Boolean.TRUE);
    }

    private Long saveDiagramVersionESRecord(ESDiagram currDiagram, Long newDiagramId, SysUser currUser, SysUser lastModifier, Boolean isAuto) {
        //1.填充新建历史视图的版本信息
        Long currDiagramId = currDiagram.getId();
        VcDiagramVersion diagramVersion = new VcDiagramVersion();
        if (!isAuto) {
            diagramVersion.setAutoCreat(2);
        }
        BeanUtils.copyProperties(currDiagram, diagramVersion);
        diagramVersion.setId(newDiagramId);
        diagramVersion.setDiagramId(currDiagramId);
        diagramVersion.setDomainId(SYS_DOMAIN_ID);
        diagramVersion.setVersionTime(BinaryUtils.getNumberDateTime());
        //2.更新当前视图的版本信息
        VcDiagramVersion currDiagramVersion = new VcDiagramVersion();
        VcDiagramVersion currDiagramOldVersion = diagramVersionDao.selectById(currDiagramId);
        //如果当前视图的版本名称不为空，将名称赋给新建历史视图的版本，当前版本名称置空
        if (currDiagramOldVersion != null) {
            String versionName = currDiagramOldVersion.getVersionName();
            if (!StringUtils.isEmpty(versionName)) {
                diagramVersion.setVersionName(versionName);
                currDiagramVersion.setVersionName("");
            }
        }
        diagramVersion.setCreator(lastModifier.getUserName() + "[" + lastModifier.getLoginCode() + "]");
        //新建历史视图的版本记录，更新当前视图的版本记录
        long resultId = diagramVersionDao.insertAsync(diagramVersion, lastModifier);
        if (!BinaryUtils.isEmpty(currDiagramVersion)) {
            diagramVersionDao.updateByIdAsync(currDiagramVersion, currDiagramId, currUser.getLoginCode());
        }

        //检查如果自动创建(名称为空)的历史视图超过20个，则删除最早的那个视图
        CVcDiagramVersion cVcDiagramVersion = new CVcDiagramVersion();
        cVcDiagramVersion.setDiagramId(currDiagramId);
        cVcDiagramVersion.setAutoCreat(1);
        // todo 加个条件 这里不查手动创建的版本 手动创建的版本也不会自动删除
        List<VcDiagramVersion> tmpVersionList = diagramVersionDao.selectList(cVcDiagramVersion, "VERSION_TIME DESC");
        //去除当前版本
        List<VcDiagramVersion> diagramVersionList = tmpVersionList
                .stream()
                .filter(version -> !version.getId().equals(version.getDiagramId()))
                .collect(Collectors.toList());

        if (diagramVersionList.size() > 20) {
            int count = 0;
            for (int i = 0; i < diagramVersionList.size(); i++) {
                VcDiagramVersion version = diagramVersionList.get(i);
                Long id = version.getId();
                if (StringUtils.isEmpty(version.getVersionName()) && !id.equals(version.getDiagramId())) {
                    if (count > 19) {
                        diagramVersionDao.deleteByIdAsync(id, currUser.getLoginCode());
                        //暂时注掉物理删除的逻辑
                        //esDiagramSvc.deleteDiagramByIds(new Long[]{versionId});
                    }
                    count++;
                }
            }
        }

        return resultId;
    }

    /**
     1.为之前的版本新建历史记录
     2.更新当前版本的版本时间和版本名称
     */
    /**
     * <AUTHOR>
     * @Description 1.为之前的版本新建历史记录
     * 2.更新当前版本的版本时间和版本名称
     * @Date 14:28 2021/8/16
     * @Param [currDiagramId, newDiagramId, historyVersionId, modifyTime]
     * @Return
     **/
    public void saveVersionRecord(Long currDiagramId, Long newDiagramId, Long historyVersionId, Long modifyTime, Long oldModifyTime) {
        //新建的历史视图
        ESDiagram newDiagram = judgeSingleDiagramAuth(newDiagramId, "version", false, null);
        //根据当前视图id查出其历史版本信息
        VcDiagramVersion currVersion = diagramVersionDao.selectById(currDiagramId);
        VcDiagramVersion historyVersion = diagramVersionDao.selectById(historyVersionId);
        //一是根据oldVersion新建历史版本，二是更新oldVersion
        VcDiagramVersion newVersion = new VcDiagramVersion();
        BeanUtils.copyProperties(currVersion, newVersion);
        newVersion.setId(newDiagramId);
        newVersion.setDiagramId(currDiagramId);
        newVersion.setVersionTime(oldModifyTime);
        newVersion.setCreator(buildCreator(SysUtil.getCurrentUserInfo()));
        diagramVersionDao.insertAsync(newVersion, SysUtil.getCurrentUserInfo());

        Long historyVersionTime = historyVersion.getVersionTime();
        String historyVersionName = historyVersion.getVersionName();
        VcDiagramVersion updateCurrVersion = new VcDiagramVersion();
        updateCurrVersion.setVersionTime(modifyTime);
        if (!StringUtils.isEmpty(historyVersionName)) {
            updateCurrVersion.setVersionName("恢复自：" + historyVersionName);
        } else {
            updateCurrVersion.setVersionName("恢复自：" + DateUtil.format(BinaryUtils.toDateTime(historyVersionTime), "yyyy/MM/dd hh:mm"));
        }
        updateCurrVersion.setCreator(buildCreator(SysUtil.getCurrentUserInfo()));
        diagramVersionDao.updateById(updateCurrVersion, currDiagramId);
    }

    @Override
    public VcDiagramVersion queryDiagramVersionById(Long id) {
        MessageUtil.checkEmpty(id, "id");
        VcDiagramVersion diagramVersion = diagramVersionDao.selectById(id);
        if (diagramVersion == null) {
            MessageUtil.throwVerify(VerifyType.NOT_EXIST, "record", "");
        }
        return diagramVersion;
    }


    @Override
    public Boolean saveESDiagramVersion(ESSimpleDiagram esSimpleDiagram, SysUser sysUser, SysUser lastModifier) {
        ESDiagram currDiagram = esSimpleDiagram.getEsDiagram();
        ESDiagram historyDiagram = new ESDiagram();
        BeanUtils.copyProperties(currDiagram, historyDiagram);
        List<ESDiagramSheetDTO> sheetList = esSimpleDiagram.getSheetList();
        List<ESDiagramLink> linkList = esSimpleDiagram.getLinkList();
        List<ESDiagramNode> nodeList = esSimpleDiagram.getNodeList();
        MessageUtil.checkEmpty(sheetList, "视图包含sheet为空，逻辑错误");
        Map<String, List<ESDiagramNode>> sheetNodeMap = new HashMap<>(16);
        if (!BinaryUtils.isEmpty(nodeList)) {
            sheetNodeMap = nodeList.stream().collect(Collectors.groupingBy(ESDiagramNode::getSheetId));
        }
        Map<String, List<ESDiagramLink>> sheetLinkMap = new HashMap<>();
        if (!BinaryUtils.isEmpty(linkList)) {
            sheetLinkMap = linkList.stream().collect(Collectors.groupingBy(ESDiagramLink::getSheetId));
        }

        //根据当前视图信息生成历史版本，不用存分享记录
        List<ESDiagramSheetDTO> copySheetList = new ArrayList<>();
        List<ESDiagramNode> copyNodeList = new ArrayList<>();
        List<ESDiagramLink> copyLinkList = new ArrayList<>();
        //复制视图
        Long newDiagramId = IdGenerator.createGenerator().getID();
        historyDiagram.setId(newDiagramId);
        historyDiagram.setDEnergy(SecureUtil.md5(String.valueOf(newDiagramId)).substring(8,24));
        historyDiagram.setHistoryVersionFlag(0);
        historyDiagram.setIcon1(null);
        for (ESDiagramSheetDTO sheetDTO : sheetList) {
            String oldSheetId = sheetDTO.getSheetId();
            Long newSheetId = IdGenerator.createGenerator().getID();
            sheetDTO.setId(newSheetId);
            sheetDTO.setDiagramId(newDiagramId);
            copySheetList.add(sheetDTO);

            //根据sheet和node的关系获取所有node
            if (!BinaryUtils.isEmpty(sheetNodeMap)) {
                List<ESDiagramNode> nodeDTOList = sheetNodeMap.get(oldSheetId);
                if (!BinaryUtils.isEmpty(nodeDTOList)) {
                    for (ESDiagramNode node : nodeDTOList) {
                        Long newNodeId = IdGenerator.createGenerator().getID();
                        node.setId(newNodeId);
                        node.setSheetId(oldSheetId);
                        node.setDiagramId(newDiagramId);
                        copyNodeList.add(node);
                    }
                }
            }
            if (!BinaryUtils.isEmpty(sheetLinkMap)) {
                List<ESDiagramLink> linkDTOList = sheetLinkMap.get(oldSheetId);
                if (!BinaryUtils.isEmpty(linkDTOList)) {
                    for (ESDiagramLink link : linkDTOList) {
                        Long newLinkId = IdGenerator.createGenerator().getID();
                        link.setId(newLinkId);
                        link.setSheetId(oldSheetId);
                        link.setDiagramId(newDiagramId);
                        copyLinkList.add(link);
                    }
                }
            }
        }
        //批量保存
        esDiagramDao.saveOrUpdate(historyDiagram);
        esDiagramSheetDao.saveOrUpdateBatch(copySheetList);
        if (!BinaryUtils.isEmpty(copyNodeList)) {
            esDiagramNodeDao.saveOrUpdateBatch(copyNodeList);
        }
        if (!BinaryUtils.isEmpty(copyLinkList)) {
            esDiagramLinkDao.saveOrUpdateBatch(copyLinkList);
        }

        saveDiagramVersionESRecord(currDiagram, newDiagramId, sysUser, lastModifier);
        //返回新生成的视图历史版本id
        return true;
    }

    /**
     * <AUTHOR>
     * @Description 根据历史视图替代当前视图
     * @Date 18:20 2021/8/26
     * @Param [currDiagramId, historyDiagram]
     * @Return 修改时间
     **/
    public Long restoreByHistoryDiagram(Long currDiagramId, ESDiagram historyDiagram) {
        //1.获取历史视图的部分基础信息，以此修改当前视图的基础信息
        Long historyDiagramId = historyDiagram.getId();
        ESDiagram newESDiagram = historyDiagram;
        newESDiagram.setId(currDiagramId);
        newESDiagram.setHistoryVersionFlag(1);
        newESDiagram.setName(historyDiagram.getName());
        ESWaterMarkInfo watermarkInfo = historyDiagram.getWatermarkInfo();
        newESDiagram.setWatermarkInfo(watermarkInfo);
        newESDiagram.setDiagramSetting(historyDiagram.getDiagramSetting() == null ? "" : historyDiagram.getDiagramSetting());
        String icon1 = historyDiagram.getIcon1();
        String iconPath = "";
        try {
            if (!StringUtils.isEmpty(icon1)) {
                rsmUtils.downloadRsmAndUpdateLocalRsm(icon1);
                StringBuilder stringBuilder = new StringBuilder();
                stringBuilder.append(localPath);
                stringBuilder.append(icon1);
                byte[] iconBytes = FileUtils.readFileToByteArray(new File(FileFilterUtil.parseFilePath(stringBuilder.toString())));
                iconPath = this.saveOrUpdateResource(new StringBuilder(UUID.randomUUID().toString()).append(".png").toString(), iconBytes, true);
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.info("iconPath:{}", iconPath);
        }
        newESDiagram.setIcon1(iconPath);

        //2.获取历史视图包含的sheet、node、link
        ESDiagramSheetQuery sheetQuery = new ESDiagramSheetQuery();
        sheetQuery.setDiagramId(historyDiagramId);
        List<ESDiagramSheetDTO> sheetList = esDiagramSheetDao.getListByCdt(sheetQuery);
        if (BinaryUtils.isEmpty(sheetList)) {
            throw new BinaryException("视图状态有误，无法进行操作");
        }
        String[] sheetIdArr = sheetList
                .stream()
                .map(ESDiagramSheetDTO::getSheetId)
                .distinct()
                .toArray(String[]::new);

        ESDiagramNodeQueryBean nodeQuery = new ESDiagramNodeQueryBean();
        nodeQuery.setDiagramId(historyDiagramId);
        nodeQuery.setSheetIds(sheetIdArr);
        List<ESDiagramNode> nodeList = new ArrayList<>();
        nodeList = getAllNode(nodeQuery, nodeList);
        Map<String, List<ESDiagramNode>> sheetNodeMap = new HashMap<>();
        if (!BinaryUtils.isEmpty(nodeList)) {
            sheetNodeMap = nodeList
                    .stream()
                    .collect(Collectors.groupingBy(ESDiagramNode::getSheetId));
        }

        ESDiagramLinkQueryBean linkQuery = new ESDiagramLinkQueryBean();
        linkQuery.setDiagramId(historyDiagramId);
        linkQuery.setSheetIds(sheetIdArr);
        List<ESDiagramLink> linkList = new ArrayList<>();
        linkList = getAllLink(linkQuery, linkList);
        Map<String, List<ESDiagramLink>> sheetLinkMap = new HashMap<>();
        if (!BinaryUtils.isEmpty(linkList)) {
            sheetLinkMap = linkList
                    .stream()
                    .collect(Collectors.groupingBy(ESDiagramLink::getSheetId));
        }

        //3.复制历史视图的组件信息，一是为其生成新id，二是diagramId统一设置为当前视图id
        List<ESDiagramSheetDTO> copySheetList = new ArrayList<>();
        List<ESDiagramNode> copyNodeList = new ArrayList<>();
        List<ESDiagramLink> copyLinkList = new ArrayList<>();
        for (ESDiagramSheetDTO sheetDTO : sheetList) {
            String oldSheetId = sheetDTO.getSheetId();
            sheetDTO.setId(IdGenerator.createGenerator().getID());
            sheetDTO.setDiagramId(currDiagramId);
            copySheetList.add(sheetDTO);
            if (!BinaryUtils.isEmpty(sheetNodeMap)) {
                List<ESDiagramNode> nodeDTOList = sheetNodeMap.get(oldSheetId);
                if (!BinaryUtils.isEmpty(nodeDTOList)) {
                    for (ESDiagramNode nodeDTO : nodeDTOList) {
                        nodeDTO.setId(IdGenerator.createGenerator().getID());
                        nodeDTO.setSheetId(oldSheetId);
                        nodeDTO.setDiagramId(currDiagramId);
                        copyNodeList.add(nodeDTO);
                    }
                }
            }
            if (!BinaryUtils.isEmpty(sheetLinkMap)) {
                List<ESDiagramLink> linkDTOList = sheetLinkMap.get(oldSheetId);
                if (!BinaryUtils.isEmpty(linkDTOList)) {
                    for (ESDiagramLink linkDTO : linkDTOList) {
                        linkDTO.setId(IdGenerator.createGenerator().getID());
                        linkDTO.setSheetId(oldSheetId);
                        linkDTO.setDiagramId(currDiagramId);
                        copyLinkList.add(linkDTO);
                    }
                }
            }
        }
        //批量保存
        Long currTime = BinaryUtils.getNumberDateTime();
        newESDiagram.setModifyTime(currTime);
        esDiagramDao.saveOrUpdateWithOption(newESDiagram, true);
        esDiagramSheetDao.saveOrUpdateBatch(copySheetList);
        if (!BinaryUtils.isEmpty(copyNodeList)) {
            esDiagramNodeDao.saveOrUpdateBatch(copyNodeList);
        }
        if (!BinaryUtils.isEmpty(copyLinkList)) {
            esDiagramLinkDao.saveOrUpdateBatch(copyLinkList);
        }
        if (watermarkInfo == null) {
            //手动更新
            BoolQueryBuilder query = QueryBuilders.boolQuery();
            query.should(QueryBuilders.termQuery("id", currDiagramId));
            String scriptStr = "ctx._source.watermarkInfo=null";
            esDiagramDao.updateByQuery(query, scriptStr, true);
        }
        //异步删除该视图包含的所有分享图片链接(视图内容更新，导致分享图片内容过期)
        executor.submit(() ->
                {
                    try {
                        //删除历史记录
                        DiagramShareLinkQuery shareLinkQuery = new DiagramShareLinkQuery();
                        shareLinkQuery.setDiagramIdEqual(SecureUtil.md5(String.valueOf(currDiagramId)).substring(8, 24));
                        shareLinkQuery.setLinkType(0);
                        List<DiagramShareLink> deleteShareLinkList = shareLinkDao.getListByCdt(shareLinkQuery);
                        if (!BinaryUtils.isEmpty(deleteShareLinkList)) {
                            //删除链接
                            Set<Long> linkIdSet = deleteShareLinkList
                                    .stream()
                                    .map(DiagramShareLink::getId)
                                    .collect(Collectors.toSet());
                            shareLinkDao.deleteByIds(linkIdSet);
                            //删除文件
                            for (DiagramShareLink shareLink : deleteShareLinkList) {
                                if (!BinaryUtils.isEmpty(shareLink)) {
                                    String fileUrl = shareLink.getStorePath();
                                    if (!BinaryUtils.isEmpty(fileUrl)) {
                                        localPath = localPath.replace("/", FILE_SEPARATOR).replace("\\", FILE_SEPARATOR);
                                        fileUrl = fileUrl.startsWith(localPath) ? fileUrl : localPath + fileUrl;
                                        File delFile = new File(FileFilterUtil.parseFilePath(fileUrl));
                                        if (delFile.exists()) {
                                            delFile.delete();
                                            resourceSvc.saveSyncResourceInfo(fileUrl, httpResouceUrl + fileUrl, false, 1);
                                            logger.info("文件删除成功");
                                        }
                                    } else {
                                        logger.info("fileUrl为空，无法删除服务器中文件，ids={}", shareLink.getId());
                                    }
                                }
                            }
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                        logger.info("删除分享图片失败，视图id={}", currDiagramId);
                    }
                }
        );
        return currTime;
    }

    @Override
    public Long restoreDiagramByVersionId(Long currDiagramId, Long historyVersionId) {
		/*
			一是将当前视图变为历史视图(生成新id，版本标识置为0)，其包含的组件也要变(修改所属的diagramId)，同时给其新增一条历史记录
			二是将查出要还原的历史视图的的信息和包含组件，复制一份，更新其信息(视图id设置为currDiagramId，组件所属的diagramId也设置为currDiagramId)
		 * */
        //1.确定当前视图和指定的历史视图是否被删除
        ESDiagram esDiagram = judgeSingleDiagramAuth(currDiagramId, "", true, null);
        ESDiagram historyDiagram = judgeSingleDiagramAuth(historyVersionId, "version", false, null);
        Long oldModifyTime = esDiagram.getModifyTime();
        //2.复制当前视图信息，更改版本标识为历史版本，新建一条视图信息；
        Long newDiagramId = IdGenerator.createGenerator().getID();
        esDiagram.setId(newDiagramId);
        esDiagram.setHistoryVersionFlag(0);
        esDiagramDao.saveOrUpdateWithOption(esDiagram, true);
        //查出其包含的所有组件，更改组件的diagramId；生成其新的历史记录，因为他也是一个历史版本
        ESDiagramSheetQuery sheetQuery = new ESDiagramSheetQuery();
        sheetQuery.setDiagramId(currDiagramId);
        List<ESDiagramSheetDTO> oldSheetList = esDiagramSheetDao.getListByCdt(sheetQuery);
        ESDiagramNodeQueryBean nodeQuery = new ESDiagramNodeQueryBean();
        nodeQuery.setDiagramId(currDiagramId);
        List<ESDiagramNode> oldNodeList = new ArrayList<>();
        oldNodeList = getAllNode(nodeQuery, oldNodeList);
        ESDiagramLinkQueryBean linkQuery = new ESDiagramLinkQueryBean();
        linkQuery.setDiagramId(currDiagramId);
        List<ESDiagramLink> oldLinkList = new ArrayList<>();
        oldLinkList = getAllLink(linkQuery, oldLinkList);
        //3.根据历史视图新建最新视图
        Long modifyTime = restoreByHistoryDiagram(currDiagramId, historyDiagram);
        //4.修改之前视图组件的diagramId信息，并新建一条之前视图的历史记录
        if (!BinaryUtils.isEmpty(oldSheetList)) {
            List<Long> oldSheetIdList = oldSheetList
                                            .stream()
                                            .map(ESDiagramSheetDTO::getId)
                                            .filter(Objects::nonNull)
                                            .distinct()
                                            .collect(Collectors.toList());
            if (!BinaryUtils.isEmpty(oldSheetIdList)) {
                TermsQueryBuilder termsQueryBuilder = QueryBuilders.termsQuery("id", oldSheetIdList);
                String scriptStr = "ctx._source.diagramId=params.diagramId";
                Map<String, Object> paramsMap = new HashMap<>(16);
                paramsMap.put("diagramId", newDiagramId);
                esDiagramSheetDao.updateByQuery(termsQueryBuilder, scriptStr, paramsMap, true);
            }
        }
        if (!BinaryUtils.isEmpty(oldNodeList)) {
            List<Long> oldNodeIdList = oldNodeList
                                            .stream()
                                            .map(ESDiagramNode::getId)
                                            .filter(Objects::nonNull)
                                            .distinct()
                                            .collect(Collectors.toList());
            if (!BinaryUtils.isEmpty(oldNodeIdList)) {
                TermsQueryBuilder termsQueryBuilder = QueryBuilders.termsQuery("id", oldNodeIdList);
                String scriptStr = "ctx._source.diagramId=params.diagramId";
                Map<String, Object> paramsMap = new HashMap<>(16);
                paramsMap.put("diagramId", newDiagramId);
                esDiagramNodeDao.updateByQuery(termsQueryBuilder, scriptStr, paramsMap, true);
            }
        }
        if (!BinaryUtils.isEmpty(oldLinkList)) {
            List<Long> oldLinkIdList = oldLinkList
                    .stream()
                    .map(ESDiagramLink::getId)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());
            if (!BinaryUtils.isEmpty(oldLinkIdList)) {
                TermsQueryBuilder termsQueryBuilder = QueryBuilders.termsQuery("id", oldLinkIdList);
                String scriptStr = "ctx._source.diagramId=params.diagramId";
                Map<String, Object> paramsMap = new HashMap<>(16);
                paramsMap.put("diagramId", newDiagramId);
                esDiagramLinkDao.updateByQuery(termsQueryBuilder, scriptStr, paramsMap, true);
            }
        }

        //为之前的视图新建历史记录,更新最新的历史记录
        saveVersionRecord(currDiagramId, newDiagramId, historyVersionId, modifyTime, oldModifyTime);
        return currDiagramId;
    }

    @Override
    public Long createDiagramByCurrVersion(Long diagramId, boolean versionFlag) {
        //根据版本视图id查询历史视图详情
        ESDiagramDTO esDiagramDTO = new ESDiagramDTO();
        if (versionFlag) {
            //以最新版本新建，最新版本指向的是主视图，所以查询时需要设置historyVersionFlag=1
            esDiagramDTO = esDiagramSvc.queryESDiagramInfoById(diagramId, "copy", false);
        } else {
            esDiagramDTO = esDiagramSvc.queryESDiagramInfoById(diagramId, "copy", true);
        }
        ESDiagramInfoDTO esDiagramInfo = esDiagramDTO.getDiagram();
        esDiagramInfo.setTransFlag(null);
        // 将releaseDiagramId置为null
        esDiagramInfo.setReleaseDiagramId(null);
        List<ESDiagramInfoDTO> esDiagramInfoDTOList = new ArrayList<>();
        esDiagramInfoDTOList.add(esDiagramInfo);
        //根据历史视图详情生成新的视图
        Long newDiagramId = esDiagramSvc.saveESDiagramBatch(esDiagramInfoDTOList, null, null, "").get(0);

        createESDiagramVersionRecord(newDiagramId);
        return newDiagramId;
    }

    @Override
    public void createSnapshotByCurrVersion(Map<Long, Long> presetIdMap, Map<String, String> idAndNameMap, SysUser lastModifier, Boolean isAuto) {
        // 根据版本视图id查询视图详情
        List<ESDiagramDTO> esDiagramDTOs = esDiagramSvc.queryDiagramInfosById(presetIdMap.keySet().toArray(new Long[0]), "copy", Boolean.FALSE, Boolean.FALSE);
        List<ESDiagramInfoDTO> esDiagramInfoDTOList = new ArrayList<>();
        for (ESDiagramDTO esDiagramDTO : esDiagramDTOs) {
            ESDiagramInfoDTO esDiagramInfo = esDiagramDTO.getDiagram();
            // 设置快照视图ID
            Long presetId = presetIdMap.get(esDiagramDTO.getDiagram().getId());
            esDiagramInfo.setNewCopyId(presetId);
            if (!BinaryUtils.isEmpty(idAndNameMap.get(esDiagramInfo.getDEnergy()))) {
                esDiagramInfo.setName(idAndNameMap.get(esDiagramInfo.getDEnergy()));
            }
            esDiagramInfo.setTransFlag(null);
            esDiagramInfoDTOList.add(esDiagramInfo);
        }
        // 生成新的视图 新加个方法吧 之前的方法引用的地方太多了 怕改出问题
        esDiagramSvc.creatSnapshotBatch(esDiagramInfoDTOList);
        log.info("##############快照批量创建成功");

        // todo 这里可能要传视图的用户信息
        List<String> userCodes = esDiagramDTOs.stream().map(e->e.getDiagram().getOwnerCode()).collect(Collectors.toList());
        CSysUser cdt = new CSysUser();
        cdt.setLoginCodes(userCodes.toArray(new String[0]));
        List<SysUser> userList = userApiSvc.getSysUserByCdt(cdt);
        Map<String, SysUser> userMap = userList.stream().collect(Collectors.toMap(SysUser::getLoginCode, e->e, (k1, k2)->k1));
        for (ESDiagramDTO esDiagramDTO : esDiagramDTOs) {
            Long curId = esDiagramDTO.getDiagram().getId();
            SysUser user = userMap.get(esDiagramDTO.getDiagram().getOwnerCode());
            if (BinaryUtils.isEmpty(user)) {
                // 用户信息异常 不创建
                continue;
            }
            ESDiagram esDiagram = new ESDiagram();
            BeanUtils.copyProperties(esDiagramDTO.getDiagram(), esDiagram);
            saveDiagramVersionESRecord(esDiagram, presetIdMap.get(curId), user, lastModifier, isAuto);
            log.info("##############视图id【{}】快照关联成功", curId);
        }
        // createESDiagramVersionRecords(presetIdMap.values(), Boolean.FALSE);
    }

    @Override
    public List<VcDiagramVersion> queryDiagramVersionByMainId(Long mainDiagramId) {
        CVcDiagramVersion version = new CVcDiagramVersion();
        version.setDiagramId(mainDiagramId);
        List<VcDiagramVersion> diagramVersions = diagramVersionDao.selectList(version, "VERSION_TIME ASC");
        return diagramVersions;
    }

    /**
     * 组件创建者 格式：用户名[用户code]
     *
     * @return
     */
    private String buildCreator(SysUser loginUser) {
        String creator = null;
        //查询当前用户最新的信息
        UserInfo op = userApiSvc.getUserInfoById(loginUser.getId());
        if (!BinaryUtils.isEmpty(op)) {
            creator = op.getUserName() + "[" + op.getLoginCode() + "]";
        } else {
            creator = loginUser.getUserName() + "[" + loginUser.getLoginCode() + "]";
        }
        return creator;
    }

    /**
     * <AUTHOR>
     * @Description 判断用户是否具有当前视图的权限
     * 1.不限制userId进行查询
     * 查不出---视图被删除
     * 查出来---判断视图的userId是否等于当前用户
     * 等于---有权限
     * 不等于---查询该用户是否被分享了该视图
     * 分享了，有两种权限，一种是查看，一种是编辑
     * 没分享，无权限，报错
     **/
    private ESDiagram judgeSingleDiagramAuth(Long diagramId, String type, Boolean needAuth, SysUser sysUser) {
        //异步处理
        if (BinaryUtils.isEmpty(sysUser)) {
            sysUser = SysUtil.getCurrentUserInfo();
        }
        Long userId = sysUser.getId();
        Long domainId = sysUser.getDomainId();

        //1.不限制userId进行查询
        EamDiagramQuery diagramQuery = new EamDiagramQuery();
        diagramQuery.setId(diagramId);
        diagramQuery.setStatus(1);
        diagramQuery.setDataStatus(1);
        diagramQuery.setDomainId(domainId);
        if (!BinaryUtils.isEmpty(type) && "version".equals(type)) {
            diagramQuery.setHistoryVersionFlag(0);
        } else {
            diagramQuery.setHistoryVersionFlag(1);
        }
        List<ESDiagram> diagramList = esDiagramDao.getListByCdt(diagramQuery);
        //2.查不出，视图不存在，报错
        if (BinaryUtils.isEmpty(diagramList)) {
            throw new BinaryException("操作视图不存在，请核对视图当前信息");
        }
        //3.查出来，判断其是否具有视图权限
        ESDiagram esDiagram = diagramList.get(0);
        if (needAuth) {
            //模板不需要校验权限
            if (esDiagram.getDiagramType().equals(3)) {
                return esDiagram;
            }
            if (esDiagram.getUserId().equals(userId)) {
                //视图属于当前用户
                return esDiagram;
            } else {
                //视图不属于当前用户，判断该视图是否分享给了该用户
                Map<Long, Set<Long>> diagramIdShareRecordMap = eamShareDiagramSvc.queryDiagramSharedUserIds(new Long[]{diagramId});
                Set<Long> shareUserIdSet = diagramIdShareRecordMap.get(diagramId);
                if (!BinaryUtils.isEmpty(shareUserIdSet) && shareUserIdSet.contains(userId)) {
                    //视图被分享给了当前用户
                    return esDiagram;
                } else {
                    throw new UnAuthorizedException("用户无当前视图权限");
                }
            }
        }
        return esDiagram;
    }

    /**
     * 将字符串保存/更新 为文件，自动加上日期前缀
     *
     * @param filePath    文件名或文件相对路径
     * @param fileContent 文件内容
     * @return 文件名或文件相对路径
     * @throws IOException
     */
    public String saveOrUpdateResource(String filePath, byte[] fileContent, boolean create) {
        if (create) {
            filePath = Paths.get("/" + LocalDate.now(), filePath).toString();
        }

        try {
            FileUtil.writeFile(filePath, fileContent);
        } catch (IOException e) {
            logger.warn("写入文件：{} 错误！", filePath, e);
        }
        return filePath;
    }

    private void createESDiagramVersionRecord(Long diagramId) {
        VcDiagramVersion diagramVersion = new VcDiagramVersion();
        diagramVersion.setId(diagramId);
        diagramVersion.setStatus(1);
        diagramVersion.setDiagramId(diagramId);
        diagramVersion.setDomainId(SYS_DOMAIN_ID);
        diagramVersion.setVersionTime(BinaryUtils.getNumberDateTime());
        SysUser currUser = SysUtil.getCurrentUserInfo();
        diagramVersion.setCreator(buildCreator(currUser));
        diagramVersionDao.insertAsync(diagramVersion, currUser);
    }

    private void createESDiagramVersionRecords(Collection<Long> diagramIds, Boolean isAuto) {
        List<VcDiagramVersion> diagramVersionList = new ArrayList<>();
        for (Long diagramId : diagramIds) {
            VcDiagramVersion diagramVersion = new VcDiagramVersion();
            diagramVersion.setId(diagramId);
            diagramVersion.setStatus(1);
            diagramVersion.setDiagramId(diagramId);
            diagramVersion.setDomainId(SYS_DOMAIN_ID);
            diagramVersion.setAutoCreat(2);
            diagramVersion.setVersionTime(BinaryUtils.getNumberDateTime());
            SysUser currUser = SysUtil.getCurrentUserInfo();
            diagramVersion.setCreator(buildCreator(currUser));
            diagramVersionList.add(diagramVersion);
        }
        diagramVersionDao.insertBatch(diagramVersionList);
    }

    public List<ESDiagramNode> getAllNode(ESDiagramNodeQueryBean nodeQuery, List<ESDiagramNode> nodeList) {
        long totalNodeCount = esDiagramNodeDao.countByCondition(ESUtil.cdtToBuilder(nodeQuery));
        if (totalNodeCount > MAX_QUERY_COUNT) {
            throw new BinaryException("一次查询数据不能超过30000条");
        }
        if (totalNodeCount > PAGE_COUNT) {
            long loopCount = totalNodeCount / PAGE_COUNT + 1;
            for (int start = 1; start <= loopCount; ++start) {
                //分页查询
                Page<ESDiagramNode> tmpList = esDiagramNodeDao.getListByCdt(start, 3000, nodeQuery);
                nodeList.addAll(tmpList.getData());
            }
        } else {
            nodeList = esDiagramNodeDao.getListByCdt(nodeQuery);
        }
        return nodeList;
    }

    public List<ESDiagramLink> getAllLink(ESDiagramLinkQueryBean linkQuery, List<ESDiagramLink> linkList) {
        long totalLinkCount = esDiagramLinkDao.countByCondition(ESUtil.cdtToBuilder(linkQuery));
        if (totalLinkCount > MAX_QUERY_COUNT) {
            throw new BinaryException("一次查询数据不能超过30000条");
        }
        if (totalLinkCount > PAGE_COUNT) {
            long loopCount = totalLinkCount / PAGE_COUNT + 1;
            for (int start = 1; start <= loopCount; ++start) {
                //分页查询
                Page<ESDiagramLink> tmpList = esDiagramLinkDao.getListByCdt(start, 3000, linkQuery);
                linkList.addAll(tmpList.getData());
            }
        } else {
            linkList = esDiagramLinkDao.getListByCdt(linkQuery);
        }
        return linkList;
    }

}
