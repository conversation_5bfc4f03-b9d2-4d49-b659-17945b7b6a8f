package com.uinnova.product.eam.db.diagram.es;

import com.uinnova.product.eam.base.diagram.model.ESEnterpriseSysUser;
import com.uinnova.product.eam.base.diagram.model.ESEnterpriseSysUserQuery;
import com.uino.dao.AbstractESBaseDao;
import com.uino.service.util.FileUtil;
import org.springframework.stereotype.Repository;

import jakarta.annotation.PostConstruct;
import java.util.List;

/**
 * @Classname 系统用户，集成企业内部产品用户ID，mmd_id
 * @Description 该类用来操作各产品ID和系统用户关联关系
 * <AUTHOR>
 * @Date 2021-10-9-15:10
 */
@Repository
public class ESEnterpriseSysUserDao extends AbstractESBaseDao<ESEnterpriseSysUser, ESEnterpriseSysUserQuery> {
    @Override
    public String getIndex() {
        return "uino_sys_user_enterprise";
    }

    @Override
    public String getType() {
        return "_doc";
    }

    @PostConstruct
    public void init() {
        List<ESEnterpriseSysUser> list = FileUtil.getData("/initdata/uino_sys_user_enterprise.json", ESEnterpriseSysUser.class);
        super.initIndex(list);
    }


}
