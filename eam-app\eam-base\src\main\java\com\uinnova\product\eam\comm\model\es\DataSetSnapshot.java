package com.uinnova.product.eam.comm.model.es;

import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.comm.dto.DataSetSheetInfo;
import lombok.Data;

import java.util.List;

@Data
@Comment("数据集临时数据表[uino_eam_dataset_snapshot]")
public class DataSetSnapshot implements Condition {

    @Comment("主键")
    private Long id;

    @Comment("临时数据集所关联的资产业务主键")
    private String assetKey;

    @Comment("临时数据集所关联的资产类型：方案-3")
    private Integer assetType;

    @Comment("数据集id")
    private Long dataSetId;

    @Comment("数据集名称")
    private String dataSetName;

    @Comment("数据集描述")
    private String description;

    @Comment("数据集根节点分类标识")
    private Long rootClassId;

    @Comment("数据集路径缩略图-需要新生成")
    private String thumbnail;

    @Comment("数据集创建人")
    private String datasetCreator;

    @Comment("数据集创建时间")
    private Long datasetCreateTime;

    @Comment("数据集内容详情列表")
    private List<DataSetSheetInfo> sheetList;

}

