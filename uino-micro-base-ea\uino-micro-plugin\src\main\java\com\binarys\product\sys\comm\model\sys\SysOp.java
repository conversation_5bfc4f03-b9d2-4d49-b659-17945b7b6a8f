package com.binarys.product.sys.comm.model.sys;




import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;


@Comment("操作员表[SYS_OP]")
public class SysOp implements EntityBean {
	private static final long serialVersionUID = 1L;


	@Comment("ID[ID]")
	private Long id;


	@Comment("操作员编码[OP_CODE]")
	private String opCode;


	@Comment("操作员姓名[OP_NAME]")
	private String opName;


	@Comment("操作员类别[OP_KIND]    操作员类别:0-超级管理员 1-管理员 2-普通用户")
	private Integer opKind;


	@Comment("手机号[MOBILE_NO]")
	private String mobileNo;


	@Comment("电子邮件地址[EMAIL_ADRESS]")
	private String emailAdress;


	@Comment("登录代码[LOGIN_CODE]")
	private String loginCode;


	@Comment("登录密码[LOGIN_PASSWD]")
	private String loginPasswd;


	@Comment("操作员简称[SHORT_NAME]")
	private String shortName;


	@Comment("备注[NOTES]")
	private String notes;


	@Comment("是否允许修改密码[ALLOW_CHANGE_PASSWD]")
	private Integer allowChangePasswd;


	@Comment("最后一次登录日志[LAST_LOGIN_LOG_ID]")
	private Long lastLoginLogId;


	@Comment("最后一次登录时间[LAST_LOGIN_TIME]")
	private Long lastLoginTime;


	@Comment("登录尝试次数[TRY_TIMES]")
	private Integer tryTimes;


	@Comment("登录标志[LOGIN_FLAG]    登录标志:0-未登录 1-已登录")
	private Integer loginFlag;


	@Comment("超级用户标志[SUPER_USER_FLAG]    超级用户标志:0-否 1-是")
	private Integer superUserFlag;


	@Comment("密码有效期[PASSWD_VALID_DAYS]    密码有效期:单位：天")
	private Long passwdValidDays;


	@Comment("开始锁定的时间[LOCKED_TIME]    用户被锁定的时间")
	private Long lockedTime;


	@Comment("锁定标志[LOCK_FLAG]    锁定标志:0-未锁定 1-锁定")
	private Integer lockFlag;


	@Comment("是否需要修改密码[IS_UPDATE_PWD]    是否需要修改密码:1=是 0=否")
	private Integer isUpdatePwd;


	@Comment("登录认证代码[LOGIN_AUTH_CODE]")
	private String loginAuthCode;

	@Comment("即时通讯[IM_ACCOUNT]")
	private String imAccount;

	@Comment("备用_1[CUSTOM_1]")
	private String custom1;


	@Comment("备用_2[CUSTOM_2]")
	private String custom2;


	@Comment("备用_3[CUSTOM_3]    备用_3:第三方用户ID")
	private String custom3;


	@Comment("备用_4[CUSTOM_4]")
	private Long custom4;


	@Comment("备用_5[CUSTOM_5]")
	private Long custom5;


	@Comment("备用_6[CUSTOM_6]")
	private Long custom6;


	@Comment("状态[STATUS]    状态:1-正常 0-停用")
	private Integer status;


	@Comment("所属域[DOMAIN_ID]")
	private Long domainId;


	@Comment("所属用户域[USER_DOMAIN_ID]")
	private Long userDomainId;


	@Comment("数据状态[DATA_STATUS]    数据状态:1-正常 0-删除")
	private Integer dataStatus;


	@Comment("创建人[CREATOR]")
	private String creator;


	@Comment("修改人[MODIFIER]")
	private String modifier;


	@Comment("创建时间[CREATE_TIME]    创建时间:yyyyMMddHHmmss")
	private Long createTime;


	@Comment("修改时间[MODIFY_TIME]    修改时间:yyyyMMddHHmmss")
	private Long modifyTime;




	public Long getId() {
		return this.id;
	}
	public void setId(Long id) {
		this.id = id;
	}


	public String getOpCode() {
		return this.opCode;
	}
	public void setOpCode(String opCode) {
		this.opCode = opCode;
	}


	public String getOpName() {
		return this.opName;
	}
	public void setOpName(String opName) {
		this.opName = opName;
	}


	public Integer getOpKind() {
		return this.opKind;
	}
	public void setOpKind(Integer opKind) {
		this.opKind = opKind;
	}


	public String getMobileNo() {
		return this.mobileNo;
	}
	public void setMobileNo(String mobileNo) {
		this.mobileNo = mobileNo;
	}


	public String getEmailAdress() {
		return this.emailAdress;
	}
	public void setEmailAdress(String emailAdress) {
		this.emailAdress = emailAdress;
	}


	public String getLoginCode() {
		return this.loginCode;
	}
	public void setLoginCode(String loginCode) {
		this.loginCode = loginCode;
	}


	public String getLoginPasswd() {
		return this.loginPasswd;
	}
	public void setLoginPasswd(String loginPasswd) {
		this.loginPasswd = loginPasswd;
	}


	public String getShortName() {
		return this.shortName;
	}
	public void setShortName(String shortName) {
		this.shortName = shortName;
	}


	public String getNotes() {
		return this.notes;
	}
	public void setNotes(String notes) {
		this.notes = notes;
	}


	public Integer getAllowChangePasswd() {
		return this.allowChangePasswd;
	}
	public void setAllowChangePasswd(Integer allowChangePasswd) {
		this.allowChangePasswd = allowChangePasswd;
	}


	public Long getLastLoginLogId() {
		return this.lastLoginLogId;
	}
	public void setLastLoginLogId(Long lastLoginLogId) {
		this.lastLoginLogId = lastLoginLogId;
	}


	public Long getLastLoginTime() {
		return this.lastLoginTime;
	}
	public void setLastLoginTime(Long lastLoginTime) {
		this.lastLoginTime = lastLoginTime;
	}


	public Integer getTryTimes() {
		return this.tryTimes;
	}
	public void setTryTimes(Integer tryTimes) {
		this.tryTimes = tryTimes;
	}


	public Integer getLoginFlag() {
		return this.loginFlag;
	}
	public void setLoginFlag(Integer loginFlag) {
		this.loginFlag = loginFlag;
	}


	public Integer getSuperUserFlag() {
		return this.superUserFlag;
	}
	public void setSuperUserFlag(Integer superUserFlag) {
		this.superUserFlag = superUserFlag;
	}


	public Long getPasswdValidDays() {
		return this.passwdValidDays;
	}
	public void setPasswdValidDays(Long passwdValidDays) {
		this.passwdValidDays = passwdValidDays;
	}


	public Long getLockedTime() {
		return this.lockedTime;
	}
	public void setLockedTime(Long lockedTime) {
		this.lockedTime = lockedTime;
	}


	public Integer getLockFlag() {
		return this.lockFlag;
	}
	public void setLockFlag(Integer lockFlag) {
		this.lockFlag = lockFlag;
	}


	public Integer getIsUpdatePwd() {
		return this.isUpdatePwd;
	}
	public void setIsUpdatePwd(Integer isUpdatePwd) {
		this.isUpdatePwd = isUpdatePwd;
	}


	public String getLoginAuthCode() {
		return this.loginAuthCode;
	}
	public void setLoginAuthCode(String loginAuthCode) {
		this.loginAuthCode = loginAuthCode;
	}

	public String getImAccount() {
		return imAccount;
	}

	public void setImAccount(String imAccount) {
		this.imAccount = imAccount;
	}

	public String getCustom1() {
		return this.custom1;
	}
	public void setCustom1(String custom1) {
		this.custom1 = custom1;
	}


	public String getCustom2() {
		return this.custom2;
	}
	public void setCustom2(String custom2) {
		this.custom2 = custom2;
	}


	public String getCustom3() {
		return this.custom3;
	}
	public void setCustom3(String custom3) {
		this.custom3 = custom3;
	}


	public Long getCustom4() {
		return this.custom4;
	}
	public void setCustom4(Long custom4) {
		this.custom4 = custom4;
	}


	public Long getCustom5() {
		return this.custom5;
	}
	public void setCustom5(Long custom5) {
		this.custom5 = custom5;
	}


	public Long getCustom6() {
		return this.custom6;
	}
	public void setCustom6(Long custom6) {
		this.custom6 = custom6;
	}


	public Integer getStatus() {
		return this.status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}


	public Long getDomainId() {
		return this.domainId;
	}
	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}


	public Long getUserDomainId() {
		return this.userDomainId;
	}
	public void setUserDomainId(Long userDomainId) {
		this.userDomainId = userDomainId;
	}


	public Integer getDataStatus() {
		return this.dataStatus;
	}
	public void setDataStatus(Integer dataStatus) {
		this.dataStatus = dataStatus;
	}


	public String getCreator() {
		return this.creator;
	}
	public void setCreator(String creator) {
		this.creator = creator;
	}


	public String getModifier() {
		return this.modifier;
	}
	public void setModifier(String modifier) {
		this.modifier = modifier;
	}


	public Long getCreateTime() {
		return this.createTime;
	}
	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}


	public Long getModifyTime() {
		return this.modifyTime;
	}
	public void setModifyTime(Long modifyTime) {
		this.modifyTime = modifyTime;
	}


}


