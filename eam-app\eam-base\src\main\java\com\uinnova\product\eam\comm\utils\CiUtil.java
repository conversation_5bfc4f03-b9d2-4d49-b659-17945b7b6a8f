package com.uinnova.product.eam.comm.utils;

import com.alibaba.fastjson.JSONObject;
import com.binary.core.util.BinaryUtils;
import com.uinnova.product.vmdb.comm.model.ci.CcCi;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * ci工具类
 *
 * <AUTHOR>
 */
public class CiUtil {

    /**
     * 解析CiLabel
     *
     * @param ci {@link CcCi}
     * @return 将ciLabel解析成字符串 , 逗号分割, CiLabel不存在时分割CiPrimaryKey
     */
    public static String parseCiLabel(CcCi ci) {
        return parseCiLabel(ci, ",");
    }

    /**
     * 解析CiLabel
     *
     * @param ci        {@link CcCi}
     * @param separator 分隔符
     * @return 将ciLabel解析成字符串 , 根据参数separator分割, CiLabel不存在时分割CiPrimaryKey
     */
    public static String parseCiLabel(CcCi ci, String separator) {
        String ciLabel = ci.getCiLabel();
        String labelString = BinaryUtils.isEmpty(ciLabel) ? ci.getCiPrimaryKey() : ciLabel;
        return formatSysName(labelString, separator);
    }

    private static String formatSysName(String ciLabel, String separator) {
        List<String> strings = JSONObject.parseArray(ciLabel, String.class);
        String[] strings1 = strings.toArray(new String[0]);
        return StringUtils.join(strings1, separator);
    }
}
