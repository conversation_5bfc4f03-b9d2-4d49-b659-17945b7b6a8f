package com.uinnova.product.eam.service.es;

import com.uinnova.product.eam.comm.model.es.EamAppConfig;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
@Service
public class EamConfigDao extends AbstractESBaseDao<EamAppConfig, EamAppConfig> {
    @Override
    public String getIndex() {
        return "uino_eam_screen_config";
    }

    @Override
    public String getType() {
        return "_doc";
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }
}
