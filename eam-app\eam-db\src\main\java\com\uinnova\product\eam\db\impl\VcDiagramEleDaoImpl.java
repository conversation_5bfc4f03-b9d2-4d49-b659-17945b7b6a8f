package com.uinnova.product.eam.db.impl;


import com.uinnova.product.eam.comm.model.CVcDiagramEle;
import com.uinnova.product.eam.comm.model.VcDiagramEle;
import com.uinnova.product.eam.db.VcDiagramEleDao;
import com.uinnova.product.eam.db.support.dao.mybatis.ComMyBatisBinaryDaoImpl;


/**
 * 视图元素表[VC_DIAGRAM_ELE]数据访问对象实现
 */
public class VcDiagramEleDaoImpl extends ComMyBatisBinaryDaoImpl<VcDiagramEle, CVcDiagramEle> implements VcDiagramEleDao {

//    @Override
//    public String getTableName() {
//        return "IAMS." + getDaoDefinition().getTableName();
//    }
}


