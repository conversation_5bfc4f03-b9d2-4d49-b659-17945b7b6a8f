package com.uinnova.product.eam.workable.listener;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.uinnova.product.eam.workable.model.FilterUser;
import com.uinnova.product.eam.workable.service.FilterUserService;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.ExecutionListener;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 将上个节点发过来的审批人记录一下
 *
 * <AUTHOR>
 * @since 2024/11/12 上午10:34
 */
@Slf4j
@Service("recordProcessUsersListener")
public class RecordProcessUsersListener implements ExecutionListener {


    @Resource
    private FilterUserService filterUserService;

    @Override
    public void notify(DelegateExecution execution) {
        String currentActivityId = execution.getCurrentActivityId();
        log.info("执行器id{}", execution.getCurrentActivityId());
        String processInstanceId = execution.getProcessInstanceId();
        List<String> filterUser = getFilterUser(processInstanceId, currentActivityId);
        Object assigneeList = execution.getVariable("assigneeList");
        Set<String> strings = new HashSet<>();
        if(!CollectionUtils.isEmpty(filterUser)){
            JSONArray objects = JSON.parseArray(JSON.toJSONString(assigneeList));
            for (int i = 0;i<objects.size();i++){
                if(!filterUser.contains(objects.getString(i))){
                    strings.add(objects.getString(i));
                }
            }
            assigneeList = strings;
            execution.setVariable("assigneeList",assigneeList);
        }
        execution.setVariable("processAssigneeList",assigneeList);
    }


    private List<String> getFilterUser(String prcessInstanceId, String flowSequenceId){
        List<FilterUser> currentTaskAgreeUser = filterUserService.getCurrentTaskAgreeUser(prcessInstanceId, flowSequenceId);
        if(CollectionUtils.isEmpty(currentTaskAgreeUser)){
            return new ArrayList<>(0);
        }
        List<String> collect = currentTaskAgreeUser.stream().map(FilterUser::getAgreeUserCode).collect(Collectors.toList());
        return collect;
    }

}
