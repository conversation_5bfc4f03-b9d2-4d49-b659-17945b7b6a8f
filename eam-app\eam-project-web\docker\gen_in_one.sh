#!/usr/bin/env bash

BIN_DIR=`pwd`
TMP_DIR=$BIN_DIR/appData

#将文件夹数据清空
if [[ ! -d "$TMP_DIR" ]]; then
 mkdir $TMP_DIR
else
 rm -rf $TMP_DIR
 mkdir $TMP_DIR
fi

tar -xf workflow/eam-workable-deploy.tar -C $TMP_DIR
tar -xf diagram/diagram-deploy.tar -C $TMP_DIR
tar -xf cangjie/cj-deploy.tar -C $TMP_DIR
tar -xf eam/eam-deploy.tar -C $TMP_DIR
tar -xf oauth/oauth.tar -C $TMP_DIR


dos2unix ./*/*.sh
#workflow
cp workflow/bootstrap.yaml $TMP_DIR/eam-workable/
cp workflow/start.sh $TMP_DIR/eam-workable/
#cangjie
cp cangjie/application.properties $TMP_DIR/cj/conf/
cp cangjie/bootstrap.properties $TMP_DIR/cj/conf/
cp cangjie/docker_start.sh $TMP_DIR/cj/bin/

#diagram
cp diagram/application-local.properties $TMP_DIR/diagram/conf/
cp diagram/bootstrap.properties $TMP_DIR/diagram/conf/
cp diagram/docker_start.sh $TMP_DIR/diagram/bin/

#eam
cp eam/application-local.properties $TMP_DIR/eam/conf/
cp eam/bootstrap.properties $TMP_DIR/eam/conf/
cp eam/docker_start.sh $TMP_DIR/eam/bin/

#oauth
cp oauth/application.properties $TMP_DIR/oauth/conf/
cp oauth/docker_start.sh $TMP_DIR/oauth/bin/

docker build -t dk.uino.cn/eam_backend/ea-app:1.0.2 .