package com.uinnova.product.eam.model;

import com.uinnova.product.eam.base.diagram.model.ESDiagram;
import com.uino.bean.permission.base.SysUser;
import lombok.Data;

import java.util.List;

@Data
public class DiagramBo {
    private ESDiagram diagram;
    private SysUser creator;
    private long pageNum;
    private long pageSize;
    private long totalRows;
    private long totalPages;
    private List<ESDiagram> diagramList;
}
