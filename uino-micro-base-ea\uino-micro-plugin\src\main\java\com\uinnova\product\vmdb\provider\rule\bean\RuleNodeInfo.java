package com.uinnova.product.vmdb.provider.rule.bean;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class RuleNodeInfo {
	private CcRltNodeInfo nodeInfo;

	private Set<Long> ciIds = new HashSet<Long>();

	private List<RuleLineInfo> sourceLines;

	private List<RuleLineInfo> targetLines;

	public RuleNodeInfo(CcRltNodeInfo nodeInfo) {
		this.nodeInfo = nodeInfo;
	}

	public CcRltNodeInfo getNodeInfo() {
		return nodeInfo;
	}

	public void setNodeInfo(CcRltNodeInfo nodeInfo) {
		this.nodeInfo = nodeInfo;
	}

	public List<RuleLineInfo> getSourceLines() {
		return sourceLines;
	}

	public void setSourceLines(List<RuleLineInfo> sourceLines) {
		this.sourceLines = sourceLines;
	}

	public List<RuleLineInfo> getTargetLines() {
		return targetLines;
	}

	public void setTargetLines(List<RuleLineInfo> targetLines) {
		this.targetLines = targetLines;
	}

	public Set<Long> getCiIds() {
		return ciIds;
	}

	public void setCiIds(Set<Long> ciIds) {
		this.ciIds = ciIds;
	}
}
