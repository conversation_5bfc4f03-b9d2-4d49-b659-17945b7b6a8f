package com.uinnova.product.eam.base.enums;

/**
 * @description: 返回参数枚举
 * @author: Lc
 * @create: 2021-12-09 16:16
 */
public enum  ResultCodeEnum {

    PARAMS_ERROR(10001, "参数错误"),

    ADD_FAIL(20001, "新增失败"),
    UPDATE_FAIL(20002, "修改失败"),
    DELETE_FAIL(20003, "删除失败"),
    SELECT_FAIL(20004, "查询失败"),
    DUPLICATE_NAME(20005,"名称重复"),

    PROCESS_ERROR(30000, "处理异常"),
    SERVICE_ERROR(30001, "服务异常"),
    FILE_VALID_ERROR(30002, "文件格式不正确"),
    PLAN_REPEAT_PUB(30003,"方案不能重复发布"),
    DLVR_TYPE_REPEAT(30004,"模板类型名称不能重复"),
    FILE_UPLOAD_COUNT_ERROR(30006, "超过文件上传数量"),

    FOLDER_ERROR(40000, "文件夹不存在"),
    APPROVE_ERROR(40001, "方案提交审批失败"),
    DELETE_ASSETS_DIR_ERROR(40002, "方案发布位置错误!"),

    NO_PERMISSION(50001, "暂无权限"),

    TERMINATE_PROCESS(60001, "发布位置已删除，当前审批流程将自动关闭，需提交人选择发布位置后重新提交审批"),
    REPEAT_OPERATE(60003, "当前流程节点已完成，页面将自动跳转至工作台"),
    TASK_FAILURE(60005, "当前流程节点已完成，页面将自动跳转至工作台"),
    MATRIX_NOT_FOUND(60006, "未找到相关数据"),
    MATRIX_RENAME_FAIL(60007, "矩阵名称重复"),
    ;

    private final int code;

    private final String message;

    ResultCodeEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
