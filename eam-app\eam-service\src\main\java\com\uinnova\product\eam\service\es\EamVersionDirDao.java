package com.uinnova.product.eam.service.es;

import com.uinnova.product.eam.comm.model.es.EamVersionDir;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;

/**
 * 版本目录
 * <AUTHOR>
 */
@Service
public class EamVersionDirDao extends AbstractESBaseDao<EamVersionDir, EamVersionDir> {
    @Override
    public String getIndex() {
        return "uino_eam_version_dir";
    }

    @Override
    public String getType() {
        return "_doc";
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }
}
