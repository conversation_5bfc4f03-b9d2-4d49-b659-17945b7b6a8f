package com.uinnova.product.eam.base.model;

import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import lombok.Data;

@Data
public class CcCiAttrDefExtend extends CcCiAttrDef {
      //100-数据字典; 101-调用接口查询
    //private Integer proType;

    private String dictType;

//    private List<DictInfo> dictInfos;

    private String callType;

    private boolean hidden;

    private String parentAttr;

}
