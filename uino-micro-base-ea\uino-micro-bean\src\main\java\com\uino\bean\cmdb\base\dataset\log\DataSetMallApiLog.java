package com.uino.bean.cmdb.base.dataset.log;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Classname DataSetMallApiLog
 * @Description 数据超市请求历史
 * @Date 2020/3/18 17:05
 * @Created by sh
 */
@ApiModel(value="数据超市请求历史",description = "数据超市请求历史信息")
public class DataSetMallApiLog {
    //    type（关系遍历、ci、关系），数据集id，是否成功，请求用户，请求时间，请求时常，ci个数，关系个数
    @ApiModelProperty(value="id",example = "123")
    private Long id;

    @ApiModelProperty(value="",example = "")
    private int dataSetType;

    @ApiModelProperty(value="数据集id",example = "123")
    private Long dataSetMallApiId;

    @ApiModelProperty(value="是否成功",example = "true")
    private boolean isSuccess;

    @ApiModelProperty(value="请求用户userCode")
    private String respUserCode;

    @ApiModelProperty(value="请求时长",example = "123")
    //请求时长
    private Long respDuration;

    @ApiModelProperty(value="请求时间",example = "3500")
    private Long createTime;

    @ApiModelProperty(value="ci个数",example = "1")
    private int ciTotal;

    @ApiModelProperty(value="关系个数",example = "1")
    private int relTotal;

    @ApiModelProperty(value="所属域",example = "1")
    private Long domainId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getDataSetMallApiId() {
        return dataSetMallApiId;
    }

    public void setDataSetMallApiId(Long dataSetMallApiId) {
        this.dataSetMallApiId = dataSetMallApiId;
    }

    public int getDataSetType() {
        return dataSetType;
    }

    public void setDataSetType(int dataSetType) {
        this.dataSetType = dataSetType;
    }

    public boolean isSuccess() {
        return isSuccess;
    }

    public void setSuccess(boolean success) {
        isSuccess = success;
    }

    public String getRespUserCode() {
        return respUserCode;
    }

    public void setRespUserCode(String respUserCode) {
        this.respUserCode = respUserCode;
    }

    public Long getRespDuration() {
        return respDuration;
    }

    public void setRespDuration(Long respDuration) {
        this.respDuration = respDuration;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public int getCiTotal() {
        return ciTotal;
    }

    public void setCiTotal(int ciTotal) {
        this.ciTotal = ciTotal;
    }

    public int getRelTotal() {
        return relTotal;
    }

    public void setRelTotal(int relTotal) {
        this.relTotal = relTotal;
    }

    public Long getDomainId() {
        return domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }

    public DataSetMallApiLog() {
    }

    public DataSetMallApiLog(JSONObject json) {
        if (json.containsKey("id")) {
            this.id = json.getLong("id");
        }
        if (json.containsKey("domainId")) {
            this.domainId = json.getLong("domainId");
        }
        if (json.containsKey("dataSetType")) {
                this.dataSetType = json.getInteger("dataSetType");
        }
        if (json.containsKey("dataSetMallApiId")) {
            this.dataSetMallApiId = json.getLong("dataSetMallApiId");
        }
        if (json.containsKey("isSuccess")) {
            this.isSuccess = json.getBooleanValue("isSuccess");
        }
        if (json.containsKey("respUserCode")) {
            this.respUserCode = json.getString("respUserCode");
        }
        //请求时长
        if (json.containsKey("respDuration")) {
            this.respDuration = json.getLong("respDuration");
        }
        if (json.containsKey("createTime")) {
            this.createTime = json.getLong("createTime");
        }
        if (json.containsKey("ciTotal")) {
            this.ciTotal = json.getIntValue("ciTotal");
        }
        if (json.containsKey("relTotal")) {
            this.relTotal = json.getIntValue("relTotal");
        }
    }

    public JSONObject toJson() {
        JSONObject json = new JSONObject();
        json.put("id", id);
        json.put("domainId", domainId);
        json.put("dataSetType", dataSetType);
        json.put("dataSetMallApiId", dataSetMallApiId);
        json.put("isSuccess", isSuccess);
        json.put("respUserCode", respUserCode);
        json.put("respDuration", respDuration);
        json.put("createTime", createTime);
        json.put("ciTotal", ciTotal);
        json.put("relTotal", relTotal);

        return json;
    }

}
