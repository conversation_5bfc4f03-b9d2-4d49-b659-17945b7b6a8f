package com.uino.provider.feign.cmdb;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import com.uino.bean.cmdb.base.ESCITreeConfigInfo;
import com.uino.bean.cmdb.business.CITreeNode;
import com.uino.provider.feign.config.BaseFeignConfig;

/**
 * ci关系feign定义
 * 
 * <AUTHOR>
 *
 */
@Component
@FeignClient(name = "${feign-server-name:tarsier-platform}", path = "${feign-server-path:tarsier-platform}/feign/ciTree", configuration = {
        BaseFeignConfig.class})
public interface CITreeFeign {

    /**
     * 获取ci tree配置
     * 
     * @return
     */
    @PostMapping(value = "getCITreeConfigs")
    public List<ESCITreeConfigInfo> getCITreeConfigs(@RequestBody Long domainId);

    /**
     * 获取ci tree配置by id
     * 
     * @param id
     * @return
     */
    @PostMapping(value = "getCITreeConfig")
    public ESCITreeConfigInfo getCITreeConfig(@RequestParam(value = "id", required = false) Long id);

    /**
     * 保存ci tree配置
     * 
     * @param saveInfo
     * @return
     */
    @PostMapping(value = "save")
    public ESCITreeConfigInfo save(@RequestBody(required = false) ESCITreeConfigInfo saveInfo);

    /**
     * 删除ci tree配置
     * 
     * @param id
     * @return
     */
    @PostMapping(value = "delete")
    public List<ESCITreeConfigInfo> delete(@RequestParam(value = "domainId") Long domainId, @RequestParam(value = "id", required = false) Long id);

    /**
     * 获取ci树
     * 
     * @param config
     *            ciTree配置信息
     * @param hasNullNode
     *            返回信息是否包含空节点true:包含 false:不包含
     * @param returnAllCI
     *            是否返回所有ci true:返回所有匹配上的ci false:每个节点最多只挂载60条ci，其余的自己条件查询翻页
     * @return 符合条件的ci树
     */
    @PostMapping(value = "getCITree")
    public CITreeNode getCITree(@RequestBody(required = false) ESCITreeConfigInfo config,
            @RequestHeader(required = false, value = "hasNullNode") boolean hasNullNode,
            @RequestHeader(required = false, value = "returnAllCI") boolean returnAllCI);
}
