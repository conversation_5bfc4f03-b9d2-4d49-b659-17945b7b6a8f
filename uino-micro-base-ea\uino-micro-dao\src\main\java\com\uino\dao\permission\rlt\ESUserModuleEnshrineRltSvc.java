package com.uino.dao.permission.rlt;

import jakarta.annotation.PostConstruct;

import org.springframework.stereotype.Service;

import com.uino.dao.AbstractESBaseDao;
import com.uino.dao.ESConst;
import com.uino.bean.permission.base.SysUserModuleEnshrineRlt;
import com.uino.bean.permission.query.CSysUserModuleEnshrineRlt;

/**
 * <b>用户-菜单收藏关系
 * 
 * <AUTHOR>
 */
@Service
public class ESUserModuleEnshrineRltSvc extends AbstractESBaseDao<SysUserModuleEnshrineRlt, CSysUserModuleEnshrineRlt> {

    @Override
    public String getIndex() {
        return ESConst.INDEX_SYS_USER_MODULE_ENSHRINE_RLT;
    }

    @Override
    public String getType() {
        return ESConst.INDEX_SYS_USER_MODULE_ENSHRINE_RLT;
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }

}
