package com.uinnova.product.eam.model.dmv;

import com.uinnova.product.eam.comm.model.es.ClassSetting;
import com.uino.bean.cmdb.base.ESCIAttrDefInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 分类定义
 * @author: Lc
 * @create: 2022-05-30 15:44
 */
@Data
public class ClassDefinitionVO implements Serializable {
    /** 分类  */
    private ClassSetting classSet;
    /** 分类定义 */
    private List<ESCIAttrDefInfo> attrDefList;
}
