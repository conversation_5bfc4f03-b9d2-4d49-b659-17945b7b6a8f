package com.uino.dao.sys;

import com.alibaba.fastjson.JSONObject;
import com.uino.bean.sys.base.ESOperateLogModule;
import com.uino.dao.AbstractESBaseDao;
import com.uino.dao.ESConst;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
public class ESOperateLogModuleSvc extends AbstractESBaseDao<ESOperateLogModule, JSONObject> {

	@Override
	public String getIndex() {
		return ESConst.INDEX_OPERATE_LOG_MODULE;
	}

	@Override
	public String getType() {
		return ESConst.INDEX_OPERATE_LOG_MODULE;
	}

	@PostConstruct
	public void init() {
		super.initIndex(5);
	}


}
