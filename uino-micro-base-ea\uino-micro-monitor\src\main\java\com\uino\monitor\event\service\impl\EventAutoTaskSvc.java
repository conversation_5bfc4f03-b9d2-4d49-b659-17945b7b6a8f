package com.uino.monitor.event.service.impl;

import com.uino.dao.ESConst;
import com.uino.dao.event.EventHistoryDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;


/**
 * 告警定时任务
 */
@Service
@RefreshScope
@ConditionalOnProperty(name = "event.history.auto.clear", havingValue = "true")
public class EventAutoTaskSvc {

    @Autowired
    private EventHistoryDao eventHistoryDao;

    //告警历史索引清除日期，默认清除365天之前的索引
    @Value("${event.history.save.days:365}")
    private Long eventHistorySaveDays;

    /**
     * 定时删除告警历史索引（每月1日0点执行）
     */
//    @Scheduled(cron = "0 0 0 1 1/1 ? ")
    public void deleteEventHistory() {
        eventHistoryDao.deleteIndexCheck(ESConst.INDEX_METRIC_DATA_PREFIX, eventHistorySaveDays);
    }

}
