package com.uinnova.product.eam.service.cj.service.impl;

import com.uinnova.product.eam.model.cj.domain.PlanDesignInstance;
import com.uinnova.product.eam.model.cj.domain.PlanDesignShareRecord;
import com.uinnova.product.eam.model.cj.domain.PlanModuleDelete;
import com.uinnova.product.eam.service.cj.dao.PlanModuleDeleteDao;
import com.uinnova.product.eam.service.cj.service.PlanDesignInstanceService;
import com.uinnova.product.eam.service.cj.service.PlanModuleDeleteService;
import com.uinnova.product.eam.service.cj.service.ShareService;
import com.uinnova.product.eam.service.exception.BusinessException;
import com.uino.bean.permission.base.SysUser;
import com.uino.dao.util.ESUtil;
import com.uino.util.sys.SysUtil;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @description: 记录方案删除的模块
 * @author: Lc
 * @create: 2023-03-08 13:49
 */
@Service
public class PlanModuleDeleteServiceImpl implements PlanModuleDeleteService {

    @Resource
    private PlanModuleDeleteDao planModuleDeleteDao;

    @Resource
    private ShareService shareService;

    @Resource
    private PlanDesignInstanceService planDesignInstanceService;

    @Override
    public Long addPlanModule(PlanModuleDelete planModuleDelete, SysUser sysUser) {

        if (planModuleDelete == null) {
            throw new BusinessException("参数不能为空!");
        }
        if (planModuleDelete.getPlanId() == null) {
            throw new BusinessException("方案主键不能为空!");
        }

        PlanDesignInstance planDesignInstance = planDesignInstanceService.getPlanDesignInstance(planModuleDelete.getPlanId());
        if (planDesignInstance == null) {
            throw new BusinessException("获取方案错误");
        }
        if (!Objects.equals(sysUser.getLoginCode(), planDesignInstance.getCreatorCode())) {
            List<PlanDesignShareRecord> shareRecordList = shareService.getByPlanId(planDesignInstance.getId());
            if (!CollectionUtils.isEmpty(shareRecordList)) {
                Map<String, Integer> shareRecordMap = shareRecordList.stream().collect(Collectors.toMap(PlanDesignShareRecord::getSharedLoginCode, PlanDesignShareRecord::getPermission));
                if (shareRecordMap == null || shareRecordMap.get(sysUser.getLoginCode()) == null
                        || (!Objects.equals(shareRecordMap.get(sysUser.getLoginCode()), 1)
                        && !Objects.equals(shareRecordMap.get(sysUser.getLoginCode()), 2)
                        && !Objects.equals(shareRecordMap.get(sysUser.getLoginCode()), 4))) {
                    throw new BusinessException("暂无权限操作方案!");
                }
            } else {
                throw new BusinessException("暂无权限操作方案!");
            }
        }

        List<Long> collect = new ArrayList<>();
        if (planModuleDelete.getTemplateChapterId() != null) {
            collect = Stream.of(planModuleDelete.getTemplateChapterId()).collect(Collectors.toList());
        }
        List<PlanModuleDelete> planModuleDeleteList = findPlanModuleDeleteList(planModuleDelete.getPlanId(), collect, planModuleDelete.getModuleId(), sysUser.getLoginCode());
        if (!CollectionUtils.isEmpty(planModuleDeleteList)) {
            return planModuleDeleteList.get(0).getId();
        }
        planModuleDelete.setId(ESUtil.getUUID());
        planModuleDelete.setCreatorCode(sysUser.getLoginCode());
        planModuleDelete.setCreatorName(sysUser.getUserName());
        planModuleDelete.setCreateTime(ESUtil.getNumberDateTime());
        planModuleDelete.setModifierCode(sysUser.getLoginCode());
        planModuleDelete.setModifyTime(ESUtil.getNumberDateTime());
        planModuleDelete.setModifierName(sysUser.getUserName());
        planModuleDelete.setDomainId(sysUser.getDomainId());
        return planModuleDeleteDao.saveOrUpdate(planModuleDelete);
    }

    @Override
    public List<PlanModuleDelete> findPlanModuleDeleteList(Long planId, List<Long> templateChapterIds, Long moduleId, String loginCode) {
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.must(QueryBuilders.termQuery("planId", planId));
        if (moduleId != null) {
            queryBuilder.must(QueryBuilders.termQuery("moduleId", moduleId));
        }
        if (!CollectionUtils.isEmpty(templateChapterIds)) {
            queryBuilder.must(QueryBuilders.termsQuery("templateChapterId", templateChapterIds));
        }
        queryBuilder.must(QueryBuilders.termQuery("creatorCode.keyword", loginCode));
        return planModuleDeleteDao.getListByQuery(queryBuilder);
    }

    @Override
    public PlanModuleDelete getPlanModuleDelete(PlanModuleDelete planModule, SysUser sysUser) {
        if (planModule == null) {
            throw new BusinessException("参数不能为空!");
        }
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.must(QueryBuilders.termQuery("planId", planModule.getPlanId()));
        if (planModule.getModuleId() != null) {
            queryBuilder.must(QueryBuilders.termQuery("moduleId", planModule.getModuleId()));
        }
        if (planModule.getTemplateChapterId() != null) {
            queryBuilder.must(QueryBuilders.termQuery("templateChapterId", planModule.getTemplateChapterId()));
        }
        queryBuilder.must(QueryBuilders.termQuery("creatorCode.keyword", sysUser.getLoginCode()));
        List<PlanModuleDelete> listByQuery = planModuleDeleteDao.getListByQuery(queryBuilder);
        if (!CollectionUtils.isEmpty(listByQuery)) {
            return listByQuery.get(0);
        }
        return null;
    }
}
