package com.uino.cmdb.image.svc;

import java.io.File;
import java.io.FileInputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;

import org.elasticsearch.index.query.QueryBuilders;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.util.ReflectionTestUtils;

import com.binary.core.exception.MessageException;
import com.binary.jdbc.Page;
import com.uino.dao.cmdb.ESDirSvc;
import com.uino.dao.cmdb.ESImageSvc;
import com.uino.service.cmdb.microservice.impl.ImageSvc;
import com.uino.service.sys.microservice.impl.ResourceSvc;
import com.uino.bean.cmdb.base.CcCiClassDir;
import com.uino.bean.cmdb.base.CcImage;

public class ImportZipImageTest {
	@InjectMocks
	private ImageSvc testSvc;
	private ESImageSvc esImageSvc;
	private ESDirSvc esDirSvc;
    private ResourceSvc resourceSvc;
	private String localPath;

	@Before
	public void before() {
		MockitoAnnotations.initMocks(this);
		esImageSvc = Mockito.mock(ESImageSvc.class);
		esDirSvc = Mockito.mock(ESDirSvc.class);
        resourceSvc = Mockito.mock(ResourceSvc.class);
		localPath = "./src/test/resources" + UUID.randomUUID().toString();
		ReflectionTestUtils.setField(testSvc, "localPath", localPath);
		ReflectionTestUtils.setField(testSvc, "svc", esImageSvc);
		ReflectionTestUtils.setField(testSvc, "dirSvc", esDirSvc);
        ReflectionTestUtils.setField(testSvc, "resourceSvc", resourceSvc);

		Mockito.when(esDirSvc.getListByQuery(QueryBuilders.termQuery("dirName.keyword", "img_dir_01")))
				.thenReturn(Collections.emptyList());
		Mockito.when(esDirSvc.saveOrUpdate(Mockito.any())).thenReturn(1L);

		Mockito.when(esImageSvc.getListByQuery(QueryBuilders.termQuery("dirId", 1L))).thenReturn(new ArrayList<>());

		Mockito.when(esImageSvc.saveOrUpdateBatch(Mockito.anyList())).thenReturn(1);

        Page<CcImage> page = new Page<>();
		CcImage oldImg = new CcImage();
		oldImg.setId(1L);
		oldImg.setImgName("oldImg");
        List<CcImage> data = Collections.singletonList(oldImg);
        page.setData(data);
        Mockito.when(esImageSvc.getListByQuery(Mockito.anyInt(), Mockito.anyInt(), Mockito.any())).thenReturn(page);

        Mockito.doNothing().when(resourceSvc).saveSyncResourceInfo(Mockito.anyList());
	}

	@After
	public void after() {
		try {
			delFile(new File(localPath));
		} catch (Exception e) {
			throw new MessageException(e.getMessage());
		}
	}

	@Test(expected = MessageException.class)
	public void test01() {
		MockMultipartFile imgPngZip = new MockMultipartFile("img_dir_01", "img_dir_01.zip", "application/json",
				new byte[0]);
		testSvc.importZipImage(1L,3, imgPngZip);
	}

	@Test
	public void test02() {
		try {
			MockMultipartFile imgPngZip = new MockMultipartFile("img_dir_01", "img_dir_01.zip", "application/zip",
					new FileInputStream("./src/test/resources/testdata/img_dir_01.zip"));
			testSvc.importZipImage(1L,3, imgPngZip);
		} catch (Exception e) {
            Assert.fail();
		}
	}

	@Test
	public void test03() {
		CcCiClassDir dir = new CcCiClassDir();
		dir.setId(2L);
		Mockito.when(esDirSvc.getListByQuery(QueryBuilders.termQuery("dirName.keyword", "img_dir_01")))
				.thenReturn(Collections.singletonList(dir));

		try {
			MockMultipartFile imgPngZip = new MockMultipartFile("img_dir_01", "img_dir_01.zip", "application/zip",
					new FileInputStream("./src/test/resources/testdata/img_dir_01.zip"));
			testSvc.importZipImage(1L,3, imgPngZip);
		} catch (Exception e) {
			Assert.fail();
		}
	}

	@Test
	public void test04() {
		CcImage oldImg = new CcImage();
		oldImg.setId(1L);
		oldImg.setImgName("10003.png");
		Mockito.when(esImageSvc.getListByQuery(QueryBuilders.termQuery("dirId", 2L)))
				.thenReturn(Collections.singletonList(oldImg));

		CcCiClassDir dir = new CcCiClassDir();
		dir.setId(2L);
		Mockito.when(esDirSvc.getListByQuery(QueryBuilders.termQuery("dirName.keyword", "img_dir_01")))
				.thenReturn(Collections.singletonList(dir));

		try {
			MockMultipartFile imgPngZip = new MockMultipartFile("img_dir_01", "img_dir_01.zip", "application/zip",
					new FileInputStream("./src/test/resources/testdata/img_dir_01.zip"));
			testSvc.importZipImage(1L,3, imgPngZip);
		} catch (Exception e) {
			Assert.fail();
		}
	}

	private void delFile(File file) {
		if (!file.exists()) {
			return;
		}

		if (file.isDirectory()) {
			File[] files = file.listFiles();
			for (File f : files) {
				delFile(f);
			}
		}
		file.delete();
	}

}
