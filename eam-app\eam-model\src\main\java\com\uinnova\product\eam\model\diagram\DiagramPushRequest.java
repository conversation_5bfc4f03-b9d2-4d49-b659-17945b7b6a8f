package com.uinnova.product.eam.model.diagram;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 视图发布请求体
 * <AUTHOR>
 */
@Data
public class DiagramPushRequest {

    @Comment("所要发布视图的id")
    private List<String> diagramIds;

    @Comment("创建视图对应的dirId   [privateDiagramId->dirId]")
    private Map<String,Long> diagramIdDirIdMap;

    @Comment("处理尚未绑定但能找到对应的diagramId,虚拟去写releaseDiagramId")
    private Map<String,String> writeReleaseDiagramIdMap;

    @Comment("发布描述")
    private String releaseDesc;

    @Comment("用户标识")
    private String ownerCode;
}
