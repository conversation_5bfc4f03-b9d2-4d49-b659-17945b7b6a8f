package com.uino.bean.cmdb.business.dataset;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @date
 */
@ApiModel(value="关系规则信息",description = "关系规则信息")
public class RltRuleTableData implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value="页码",example = "1")
    private Integer pageNum;

    @ApiModelProperty(value="每页记录数",example = "20")
    private Integer pageSize;

    @ApiModelProperty(value="总页数",example = "2")
    private Integer totalPages;

    @ApiModelProperty(value="表头集合")
    private List<Map<String, Object>> headers;

    @ApiModelProperty(value="列表内容")
    private List<List<String>> data;

    @ApiModelProperty(value="Sheet分页集")
    private List<RltRulePathData> sheetList;

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getTotalPages() {
        return totalPages;
    }

    public void setTotalPages(Integer totalPages) {
        this.totalPages = totalPages;
    }

    public List<Map<String, Object>> getHeaders() {
        return headers;
    }

    public void setHeaders(List<Map<String, Object>> headers) {
        this.headers = headers;
    }

    public List<List<String>> getData() {
        return data;
    }

    public void setData(List<List<String>> data) {
        this.data = data;
    }

    public List<RltRulePathData> getSheetList() {
        return sheetList;
    }

    public void setSheetList(List<RltRulePathData> sheetList) {
        this.sheetList = sheetList;
    }
}
