package com.uino.util.express.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @Title: FunctionDescription
 * @Description: function description
 * @Author: YGQ
 * @Create: 2021-08-26 14:55
 **/
@Data
@ApiModel("方法描述")
public class FunctionDescription {

    @ApiModelProperty("方法名")
    private String functionName;

    @ApiModelProperty("方法描述")
    private String functionDescription;

    @ApiModelProperty("参数描述")
    private List<FunctionParamDescription> paramsDescription;

    @ApiModelProperty("返回类型")
    private Class<?> returnType;

}
