package com.uinnova.product.eam.base.diagram.model;

import cn.hutool.crypto.SecureUtil;
import com.binary.framework.bean.annotation.Comment;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * @Classname
 * @Description 视图model信息集合
 * <AUTHOR>
 * @Date 2021-06-04-11:55
 */
@Data
public class ESDiagramModel {
    @Comment("视图id")
    @JsonIgnore
    private Long diagramId;

    @Comment("视图类别")
    private String diagramClass;

    @Comment("线条起始点")
    private String  linkFromPortIdProperty;

    @Comment("线条到达点")
    private String linkToPortIdProperty;

    @Comment("模型数据")
    private String modelData;

    @Comment("节点数据集合")
    private List<ESDiagramNode> nodeDataArray;

    @Comment("线条数据集合")
    private List<ESDiagramLink> linkDataArray;

    @Comment("视图ID加密字段")
//    @JsonIgnore
    @JsonProperty("diagramId")
    private String dEnergy;

    public String getdEnergy() {
        return SecureUtil.md5(String.valueOf(diagramId)).substring(8,24);
    }

    @Comment("前端需要的sheet标识")
    private String sheetId;

    @Comment("老视图的sheet标识")
    private String sheetSign;
}
