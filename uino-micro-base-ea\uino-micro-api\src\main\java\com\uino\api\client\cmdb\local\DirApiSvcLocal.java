package com.uino.api.client.cmdb.local;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.uinnova.product.vmdb.comm.model.ci.CCcCiClassDir;
import com.uino.service.cmdb.microservice.IDirSvc;
import com.uino.bean.cmdb.base.CcCiClassDir;
import com.uino.api.client.cmdb.IDirApiSvc;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
public class DirApiSvcLocal implements IDirApiSvc {

	@Autowired
	private IDirSvc dirSvc;

	@Override
	public Long saveOrUpdateDir(CcCiClassDir dir) {
		return dirSvc.saveOrUpdate(dir);
	}

	@Override
	public Integer removeDirById(Long id) {
		return dirSvc.deleteById(id);
	}

    @Override
    public List<CcCiClassDir> queryDirList(CCcCiClassDir cdt, String orders, Boolean isAsc) {
        return dirSvc.queryDirList(cdt, orders, isAsc);
    }

}
