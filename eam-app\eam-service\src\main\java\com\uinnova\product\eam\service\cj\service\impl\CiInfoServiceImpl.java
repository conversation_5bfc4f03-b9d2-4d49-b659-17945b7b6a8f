package com.uinnova.product.eam.service.cj.service.impl;

import com.uinnova.product.eam.service.ICISwitchSvc;
import com.uinnova.product.eam.service.cj.service.CiInfoService;
import com.uinnova.product.vmdb.provider.ci.bean.CiGroupPage;
import com.uino.bean.cmdb.base.LibType;
import com.uino.bean.cmdb.query.ESCISearchBean;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

/**
 * ci service 实现类
 *
 * <AUTHOR>
 */
@Service
public class CiInfoServiceImpl implements CiInfoService {

    @Resource
    private ICISwitchSvc ciSwitchSvc;

    /**
     * 根据class查询ci列表
     *
     * @param libType 库类型
     * @return ci列表
     */
    @Override
    public CiGroupPage getCiList(LibType libType, ESCISearchBean bean) {

        Integer totalRows = getCiTotalRows(libType, bean);
        if (totalRows > 0) {
            bean.setPageSize(totalRows);
            return ciSwitchSvc.queryPageBySearchBean(bean, false, libType);
        } else {
            CiGroupPage ciGroupPage = new CiGroupPage();
            ciGroupPage.setPageNum(bean.getPageNum());
            ciGroupPage.setPageSize(bean.getPageSize());
            return ciGroupPage;
        }
    }

    /**
     * @param libType 库类型
     * @param bean    查询条件
     * @return CI个数
     */
    @Override
    public Integer getCiTotalRows(LibType libType, ESCISearchBean bean) {
        // 备份 pageSize 和 pageNum
        int pageNum = bean.getPageNum();
        int pageSize = bean.getPageSize();

        bean.setPageNum(1);
        bean.setPageSize(1);
        CiGroupPage ciGroupPage = ciSwitchSvc.queryPageBySearchBean(bean, false, libType);

        bean.setPageNum(pageNum);
        bean.setPageSize(pageSize);

        return (int) ciGroupPage.getTotalRows();
    }
}
