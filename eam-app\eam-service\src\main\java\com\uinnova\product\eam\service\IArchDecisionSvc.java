package com.uinnova.product.eam.service;

import com.binary.jdbc.Page;
import com.uinnova.product.eam.comm.model.ArchDecisionDto;
import com.uinnova.product.eam.comm.model.ArchDecisionListVo;
import com.uinnova.product.eam.comm.model.ArchDecisionTeamResponse;
import com.uinnova.product.eam.comm.model.DecisionParam;
import com.uinnova.product.eam.comm.model.es.ArchDecision;
import com.uinnova.product.eam.model.ArchDecisionResponse;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.permission.base.SysUser;

import java.util.List;

/**
 * 架构决策接口
 * <AUTHOR>
 */
public interface IArchDecisionSvc {

    /**
     * 新建架构决策
     * @param dto 决策信息
     * @return 新建结果
     */
    Long submitDecision(ArchDecisionDto dto);

    ArchDecisionResponse getDecisionById(Long id);

    List<ArchDecision> getDecisionByIds(List<Long> ids);

    /**
     * 决策列表信息
     * @param decisionParam 查询列表参数
     * @return 决策列表信息
     */
    Page<ArchDecisionListVo> queryDecisionList(DecisionParam decisionParam);

    /***
     * 查询所属产品分类信息
     * @return
     */
    List<ESCIInfo> getProductsInvolved();

    /**
     * 查询架构决策产品及团队
     * @return
     */
    List<ArchDecisionTeamResponse> getCenterAndTeam();

    /**
     * 已阅
     * @param taskId 任务id
     */
    void itemDone(String taskId);

    List<SysUser> getUserByRole();

    Page<ArchDecisionListVo> queryDecisionBySystemId(Integer pageNum, Integer pageSize, String systemId);

    String getApproverByTaskId(String taskId);
}
