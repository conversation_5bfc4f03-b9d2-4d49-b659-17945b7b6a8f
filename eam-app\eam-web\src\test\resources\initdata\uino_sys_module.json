[{"creator": "system", "moduleCode": "8948830619007584", "orderNo": 18, "moduleType": 0, "modifier": "system", "moduleName": "QuickEa", "moduleSign": "QuickEa", "label": "QuickEa", "domainId": 1, "parentId": 0, "isDisable": false, "modifyTime": 20220329095530, "createTime": 20220329095530, "id": 8948830619007585, "isInit": false, "status": 1}, {"id": 1963657779852713, "moduleCode": "1963657779852712", "moduleName": "架构设计", "moduleSign": "架构设计", "label": "架构设计", "parentId": 8948830619007585, "orderNo": 27, "moduleUrl": "/framework-design#/", "status": 1, "isDisable": false, "moduleType": 0, "isInit": false, "domainId": 1, "creator": "system", "modifier": "system", "createTime": 20230320142731, "modifyTime": 20230605145050, "moduleImg": "uino-iconfont icon-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "customColor": "#E9C690"}, {"id": 8964026895312496, "moduleCode": "8964026895312495", "moduleName": "资产管理", "moduleSign": "资产管理", "label": "资产管理", "parentId": 8948830619007585, "orderNo": 28, "status": 1, "isDisable": false, "moduleType": 0, "isInit": false, "domainId": 1, "creator": "system", "modifier": "system", "createTime": 20220330184748, "modifyTime": 20230605145050, "moduleImg": "ea-iconfont yingyongguangchang1", "modulePic": "", "customColor": "#F5E304"}, {"id": 2371005538954991, "moduleCode": "2371005538954989", "moduleName": "架构应用", "moduleSign": "架构应用", "label": "架构应用", "parentId": 8948830619007585, "orderNo": 29, "status": 1, "isDisable": false, "moduleType": 0, "isInit": false, "domainId": 1, "creator": "admin", "modifier": "system", "createTime": 20230531140548, "modifyTime": 20230605145050, "moduleImg": "ts ts-envamables", "customColor": "#D90000"}, {"id": 8948830619007658, "moduleCode": "8948830619007657", "moduleName": "架构治理", "moduleSign": "架构治理", "label": "架构治理", "parentId": 8948830619007585, "orderNo": 30, "status": 1, "isDisable": false, "moduleType": 0, "isInit": false, "domainId": 1, "creator": "system", "modifier": "system", "createTime": 20220329095750, "modifyTime": 20230605145050, "moduleImg": "ea-iconfont jiagou1", "modulePic": "", "customColor": "#E7B6AC"}, {"id": 2388712285072927, "moduleCode": "2388712285072926", "moduleName": "设置", "moduleSign": "设置", "label": "设置", "parentId": 8948830619007585, "orderNo": 31, "status": 1, "isDisable": false, "moduleType": 0, "isInit": false, "domainId": 1, "creator": "admin", "modifier": "system", "createTime": 20230605104452, "modifyTime": 20230605145050, "moduleImg": "ts ts-envamables", "customColor": "#206000"}, {"creator": "admin", "moduleCode": "3123516895479211", "orderNo": 0, "moduleType": 0, "modifier": "admin", "moduleName": "信息资产管理", "moduleSign": "信息资产管理", "label": "信息资产管理", "moduleImg": "ts ts-envamables", "domainId": 1, "parentId": 8964026895312496, "isDisable": false, "customColor": "#2FBC57", "modifyTime": 20231013102521, "createTime": 20231013102228, "id": 3123516895479212, "isInit": false, "moduleUrl": "/architecture-assets#/", "status": 1}, {"creator": "system", "moduleCode": "2515931059703022", "orderNo": 1, "moduleType": 0, "modifier": "system", "moduleName": "设计资产管理", "moduleSign": "设计资产管理", "label": "设计资产管理", "moduleImg": "ts ts-envamables", "domainId": 1, "parentId": 8964026895312496, "isDisable": false, "customColor": "#FF7718", "modifyTime": 20230626142049, "createTime": 20230626142013, "id": 8948830619007633, "isInit": false, "moduleUrl": "/framework-assets#/", "status": 1}, {"id": 2371005539013566, "moduleCode": "2371005539013565", "moduleName": "架构地图", "moduleSign": "架构地图", "label": "架构地图", "parentId": 2371005538954991, "orderNo": 33, "moduleUrl": "/application-square#/map", "status": 1, "isDisable": false, "moduleType": 0, "isInit": false, "domainId": 1, "creator": "admin", "modifier": "system", "createTime": 20230602113348, "modifyTime": 20230605145050, "moduleImg": "ts ts-envamables", "customColor": "#2FBC57"}, {"id": 2371005539013572, "moduleCode": "2371005539013571", "moduleName": "专题分析", "moduleSign": "专题分析", "label": "专题分析", "parentId": 2371005538954991, "orderNo": 34, "moduleUrl": "/eam#/analysis", "status": 1, "isDisable": false, "moduleType": 0, "isInit": false, "domainId": 1, "creator": "admin", "modifier": "system", "createTime": 20230602113420, "modifyTime": 20230605145050, "moduleImg": "ts ts-envamables", "customColor": "#2FBC57"}, {"id": 2388712285072533, "moduleCode": "2388712285072532", "moduleName": "数据超市", "moduleSign": "数据超市", "label": "数据超市", "parentId": 2371005538954991, "orderNo": 35, "moduleUrl": "/system-setting#/dataStore", "status": 1, "isDisable": false, "moduleType": 0, "isInit": false, "domainId": 1, "creator": "admin", "modifier": "system", "createTime": 20230605102403, "modifyTime": 20230605145050, "moduleImg": "ts ts-envamables", "customColor": "#206000"}, {"id": 8948830619009861, "moduleCode": "8948830619009860", "moduleName": "元模型管理", "moduleSign": "元模型管理新版", "label": "元模型管理", "parentId": 8948830619007658, "orderNo": 56, "moduleUrl": "/visual-model#/preview", "status": 1, "isDisable": false, "moduleType": 0, "isInit": false, "domainId": 1, "creator": "system", "modifier": "system", "createTime": 20220329103752, "modifyTime": 20230605145050, "moduleImg": "ts ts-envamables", "modulePic": "", "customColor": "#E9C690", "businessGroup": 702, "businessModule": 703}, {"id": 8948830619009964, "moduleCode": "8948830619009963", "moduleName": "制品类型管理", "moduleSign": "制品类型管理新版", "label": "制品类型管理", "parentId": 8948830619007658, "orderNo": 57, "moduleUrl": "/eam#/unit-modal/product", "status": 1, "isDisable": false, "moduleType": 0, "isInit": false, "domainId": 1, "creator": "system", "modifier": "system", "createTime": 20220329103920, "modifyTime": 20230605145050, "moduleImg": "ts ts-envamables", "customColor": "#E9C690", "businessGroup": 702, "businessModule": 706}, {"id": 8948830619010032, "moduleCode": "8948830619010031", "moduleName": "交付物模版管理", "moduleSign": "交付物模版管理", "label": "交付物模板管理", "parentId": 8948830619007658, "orderNo": 58, "moduleUrl": "/deliverable#/template", "status": 1, "isDisable": false, "moduleType": 0, "isInit": false, "domainId": 1, "creator": "system", "modifier": "system", "createTime": 20220329103956, "modifyTime": 20230605145050, "moduleImg": "ts ts-envamables", "customColor": "#F5E304", "businessGroup": 702, "businessModule": 707}, {"id": 44528121803658, "moduleCode": "44528121803657", "moduleName": "建模工艺管理", "moduleSign": "建模工艺管理", "label": "建模工艺管理", "parentId": 8948830619007658, "orderNo": 59, "moduleUrl": "/eam#/unit-modal/level-setting", "status": 1, "isDisable": false, "moduleType": 0, "isInit": false, "domainId": 1, "creator": "system", "modifier": "system", "createTime": 20220413143409, "modifyTime": 20230605145050, "moduleImg": "ts ts-envamables", "customColor": "#E9C690", "businessGroup": 715, "businessModule": 716}, {"creator": "admin", "moduleCode": "5552442069933229", "orderNo": 17, "moduleType": 0, "modifier": "admin", "moduleName": "矩阵制品类型管理", "moduleSign": "矩阵制品类型管理", "label": "矩阵制品类型管理", "moduleImg": "ts ts-envamables", "domainId": 1, "parentId": 8948830619007658, "isDisable": false, "customColor": "#A3E558", "modifyTime": 20241230102810, "createTime": 20241230102810, "id": 5552442069933230, "isInit": false, "moduleUrl": "/matrix#/template", "status": 1}, {"id": 2388712285072948, "moduleCode": "2388712285072947", "moduleName": "我的数据", "moduleSign": "我的数据", "label": "我的数据", "parentId": 2388712285072927, "orderNo": 0, "moduleUrl": "/system-setting#/model-asset/newci", "status": 1, "isDisable": false, "moduleType": 0, "isInit": false, "domainId": 1, "creator": "admin", "modifier": "admin", "createTime": 20230605104510, "modifyTime": 20230613150052, "moduleImg": "ts ts-envamables", "customColor": "#2FBC57"}, {"id": 2388712285073226, "moduleCode": "2388712285073225", "moduleName": "对象管理", "moduleSign": "对象管理", "label": "对象管理", "parentId": 2388712285072927, "orderNo": 2, "moduleUrl": "/visual-model#/ci", "status": 1, "isDisable": false, "moduleType": 0, "isInit": false, "domainId": 1, "creator": "admin", "modifier": "admin", "createTime": 20230605105401, "modifyTime": 20230613150052, "moduleImg": "ts ts-envamables", "customColor": "#2FBC57"}, {"id": 2388712285073288, "moduleCode": "2388712285073287", "moduleName": "关系管理", "moduleSign": "关系管理", "label": "关系管理", "parentId": 2388712285072927, "orderNo": 3, "moduleUrl": "/visual-model#/relation", "status": 1, "isDisable": false, "moduleType": 0, "isInit": false, "domainId": 1, "creator": "admin", "modifier": "admin", "createTime": 20230605105440, "modifyTime": 20230613150052, "moduleImg": "ts ts-envamables", "customColor": "#2FBC57"}, {"creator": "admin", "moduleCode": "2663631426553401", "orderNo": 4, "moduleType": 0, "modifier": "admin", "moduleName": "菜单管理", "moduleSign": "菜单管理", "label": "菜单管理", "moduleImg": "ts ts-envamables", "domainId": 1, "parentId": 2388712285072927, "isDisable": false, "customColor": "#FF7718", "modifyTime": 20230724165022, "createTime": 20230720113342, "id": 2663631426553402, "isInit": false, "moduleUrl": "/system-setting#/menu", "status": 1}, {"creator": "caimengyao", "moduleCode": "2639049917149734", "orderNo": 6, "moduleType": 0, "modifier": "admin", "moduleName": "品牌管理", "moduleSign": "品牌管理", "label": "品牌管理", "moduleImg": "ts ts-envamables", "domainId": 1, "parentId": 2388712285072927, "isDisable": false, "customColor": "#FF7718", "modifyTime": 20230724165022, "createTime": 20230720093031, "id": 2639049917149735, "isInit": false, "moduleUrl": "/system-setting#/Logo", "status": 1}, {"creator": "admin", "moduleCode": "3711961215503605", "orderNo": 5, "moduleType": 0, "modifier": "admin", "moduleName": "标签管理", "moduleSign": "标签管理", "label": "标签管理", "moduleImg": "ts ts-envamables", "domainId": 1, "parentId": 2388712285072927, "isDisable": false, "customColor": "#FF7718", "modifyTime": 20230724165022, "createTime": 20230720093031, "id": 3711961215503606, "isInit": false, "moduleUrl": "/tag", "status": 1}, {"id": 2400164805001884, "moduleCode": "2400164805001883", "moduleName": "权限管理", "moduleSign": "权限管理", "label": "权限管理", "parentId": 2388712285072927, "orderNo": 7, "moduleUrl": "/system-setting#/authority_new", "status": 1, "isDisable": false, "moduleType": 0, "isInit": false, "domainId": 1, "creator": "admin", "modifier": "admin", "createTime": 20230607104147, "modifyTime": 20230607104147, "moduleImg": "ts ts-envamables", "customColor": "#FF7718"}, {"id": 1095263230002295, "moduleName": "页面查看", "label": "页面查看", "parentId": 2400164805001884, "orderNo": 0, "status": 1, "isDisable": true, "moduleType": 1, "isInit": false, "domainId": 1, "creator": "admin", "modifier": "admin", "createTime": 20221017210205, "modifyTime": 20221017210205, "moduleImg": "ts ts-envamables", "customColor": "customColor"}, {"id": 1101799087475416, "moduleCode": "1101799087475415", "moduleName": "菜单权限", "moduleSign": "functionPerm", "label": "菜单权限", "parentId": 2400164805001884, "orderNo": 1, "status": 0, "isDisable": false, "moduleType": 1, "isInit": false, "domainId": 1, "creator": "lichong", "modifier": "lichong", "createTime": 20221018201706, "modifyTime": 20221018201706, "moduleImg": "ts ts-envamables", "customColor": "#068AF2"}, {"id": 1101799087475424, "moduleCode": "1101799087475423", "moduleName": "数据权限", "moduleSign": "datafunction", "label": "数据权限", "parentId": 2400164805001884, "orderNo": 2, "status": 0, "isDisable": false, "moduleType": 1, "isInit": false, "domainId": 1, "creator": "lichong", "modifier": "lichong", "createTime": 20221018201725, "modifyTime": 20221018201725, "moduleImg": "ts ts-envamables", "customColor": "#068AF2"}, {"id": 2400164805001890, "moduleCode": "2400164805001889", "moduleName": "组织管理", "moduleSign": "组织管理", "label": "组织管理", "parentId": 2388712285072927, "orderNo": 8, "moduleUrl": "/system-setting#/org", "status": 1, "isDisable": false, "moduleType": 0, "isInit": false, "domainId": 1, "creator": "admin", "modifier": "admin", "createTime": 20230607104211, "modifyTime": 20230607104211, "moduleImg": "ts ts-envamables", "customColor": "#FF7718"}, {"id": 2400164805001877, "moduleCode": "2400164805001876", "moduleName": "用户管理", "moduleSign": "用户管理", "label": "用户管理", "parentId": 2388712285072927, "orderNo": 9, "moduleUrl": "/system-setting#/user", "status": 1, "isDisable": false, "moduleType": 0, "isInit": false, "domainId": 1, "creator": "admin", "modifier": "admin", "createTime": 20230607104123, "modifyTime": 20230607104123, "moduleImg": "ts ts-envamables", "customColor": "#FF7718"}, {"id": 2400164805001871, "moduleCode": "2400164805001870", "moduleName": "图标管理", "moduleSign": "图标管理", "label": "图标管理", "parentId": 2388712285072927, "orderNo": 10, "moduleUrl": "/system-setting#/icon", "status": 1, "isDisable": false, "moduleType": 0, "isInit": false, "domainId": 1, "creator": "admin", "modifier": "admin", "createTime": 20230607104104, "modifyTime": 20230607104104, "moduleImg": "ts ts-envamables", "customColor": "#FF7718"}, {"creator": "admin", "moduleCode": "2677925441010978", "orderNo": 11, "moduleType": 0, "modifier": "admin", "moduleName": "字典表管理", "moduleSign": "字典表管理", "label": "字典表管理", "moduleImg": "ts ts-envamables", "domainId": 1, "parentId": 2388712285072927, "isDisable": false, "customColor": "#FF7718", "modifyTime": 20230724165022, "createTime": 20230724165012, "id": 2677925441010979, "isInit": false, "moduleUrl": "/system-setting#/dict", "status": 1}, {"id": 2400164805001903, "moduleCode": "2400164805001902", "moduleName": "日志管理", "moduleSign": "日志管理", "label": "日志管理", "parentId": 2388712285072927, "orderNo": 12, "moduleUrl": "/system-setting#/log", "status": 1, "isDisable": false, "moduleType": 0, "isInit": false, "domainId": 1, "creator": "admin", "modifier": "admin", "createTime": 20230607104306, "modifyTime": 20230607104306, "moduleImg": "ts ts-envamables", "customColor": "#FF7718"}, {"id": 2400164805001916, "moduleCode": "2400164805001915", "moduleName": "授权管理", "moduleSign": "授权管理", "label": "授权管理", "parentId": 2388712285072927, "orderNo": 13, "moduleUrl": "/system-setting#/license", "status": 1, "isDisable": false, "moduleType": 0, "isInit": false, "domainId": 1, "creator": "admin", "modifier": "admin", "createTime": 20230607104421, "modifyTime": 20230607104421, "moduleImg": "ts ts-envamables", "customColor": "#FF7718"}, {"id": 2400164805001910, "moduleCode": "2400164805001909", "moduleName": "LDAP集成", "moduleSign": "LDAP集成", "label": "LDAP集成", "parentId": 2388712285072927, "orderNo": 14, "moduleUrl": "/system-setting#/ldap", "status": 1, "isDisable": false, "moduleType": 0, "isInit": false, "domainId": 1, "creator": "admin", "modifier": "admin", "createTime": 20230607104358, "modifyTime": 20230607104358, "moduleImg": "ts ts-envamables", "customColor": "#FF7718"}, {"id": 4677865170000100, "moduleCode": "6015643927702208", "moduleName": "流程管理", "moduleSign": "流程管理", "label": "流程管理", "parentId": 8948830619007585, "orderNo": 7, "moduleUrl": "/flow", "status": 1, "isDisable": false, "moduleType": 0, "isInit": false, "domainId": 1, "creator": "admin", "modifier": "admin", "createTime": 20250304165622, "modifyTime": 20250304165622, "moduleImg": "ts ts-envamables", "customColor": "#FF7718"}]