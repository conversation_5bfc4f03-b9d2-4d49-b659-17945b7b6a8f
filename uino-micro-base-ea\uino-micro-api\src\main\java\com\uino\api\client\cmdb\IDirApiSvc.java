package com.uino.api.client.cmdb;

import java.util.List;

import com.uinnova.product.vmdb.comm.model.ci.CCcCiClassDir;
import com.uino.bean.cmdb.base.CcCiClassDir;

/**
 * 目录操作
 * 
 * <AUTHOR>
 */
public interface IDirApiSvc {

    /**
     * 保存或更新目录信息
     * 
     * @param dir
     * @return
     */
    Long saveOrUpdateDir(CcCiClassDir dir);

    /**
     * 根据id删除目录
     * 
     * @param id
     * @return
     */
    Integer removeDirById(Long id);

    /**
     * 条件查询文件夹信息
     * 
     * @param cdt ciType=1,查询分类文件夹;=3查询图标文件夹
     * @param orders
     * @param isAsc
     * @return
     */
    public List<CcCiClassDir> queryDirList(CCcCiClassDir cdt, String orders, Boolean isAsc);

}
