package com.uino.bean.cmdb.business.dataset;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 数据超市拓扑图对象类
 * <AUTHOR>
 * @date 2022/3/18
 */
@Data
public class CcCiFriendInfo extends CcCiInfo {
    @Comment("是否重叠节点")
    private Boolean repeat;
    @Comment("根节点ciId")
    private List<Long> startCiIds = new ArrayList<>();
}
