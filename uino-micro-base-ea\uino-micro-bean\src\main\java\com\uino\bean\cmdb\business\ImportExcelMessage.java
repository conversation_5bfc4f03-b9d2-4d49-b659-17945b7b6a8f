package com.uino.bean.cmdb.business;

import com.binary.framework.bean.annotation.Comment;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * 
 * <AUTHOR>
 *
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value="导入Exce文件详情",description = "导入Excel文件详情")
public class ImportExcelMessage {

	@ApiModelProperty(value="临时文件名",example = "temp")
	@Comment("临时文件名")
	private String fileName;

	@ApiModelProperty(value="源文件名",example = "source")
	@Comment("源文件名")
	private String originalFileName;

	@ApiModelProperty(value = "目录名",example = "dir")
	@Comment("临时文件夹名")
	private String dirName;

	@ApiModelProperty(value="sheet页名列表")
	@Comment("sheet页名列表")
	@Builder.Default
    private List<String> sheetNames = new LinkedList<>();

	@ApiModelProperty(value="当前sheet页名称",example = "sheet1")
	@Comment("当前sheet页")
	private String curSheetName;

	@ApiModelProperty(value="页号",example = "1")
	@Comment("页号")
	private Long pageNum;

	@ApiModelProperty(value="页大小",example = "10")
	@Comment("页大小")
	private Long pageSize;

	@ApiModelProperty(value="总记录",example = "10")
	@Comment("总记录")
	private Long totalRows;

	@ApiModelProperty(value="分类属性名")
	@Comment("分类属性名")
	private List<String> fieldNames;

	@ApiModelProperty(value="页标题")
	@Comment("页标题")
	private List<Map<String, Object>> sheetTitles;

	@ApiModelProperty(value="内容")
	@Comment("内容")
	private List<Map<Integer, String>> datas;

	@ApiModelProperty(value="页标题map")
	@Comment("页标题map")
	private Map<String, List<Map<String, String>>> titleMap;

	@ApiModelProperty(value="页数据map")
	@Comment("页数据map")
	private Map<String, List<Map<String, String>>> dataMap;
}
