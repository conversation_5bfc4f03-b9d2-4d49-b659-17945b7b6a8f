package com.uinnova.product.eam.web.resource.mvc;

import com.binary.core.util.BinaryUtils;
import com.binary.framework.util.ControllerUtils;
import com.binary.framework.web.RemoteResult;
import com.uinnova.product.eam.base.model.FileResourceMeta;
import com.uinnova.product.eam.base.model.ResourceInfo;
import com.uinnova.product.eam.base.util.ResourceUtil;
import com.uinnova.product.eam.model.vo.DefaultFileVo;
import com.uinnova.product.eam.web.resource.peer.ResourcePeer;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uinnova.product.vmdb.comm.doc.annotation.MvcDesc;
import com.uino.util.sys.SysUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.ValidationException;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * 标准规范相关接口
 */
@RestController
@RequestMapping("/eam/resource")
@MvcDesc(author="Shaolong.fan",desc="标准规范类")
public class ResourceMvc {

	@Resource
	private ResourcePeer resourcePeer;

	@Value("${uino.eam.word_name_regex}")
	private String resNameRegex;

	@PostMapping("/uploadFile")
	public void uploadFile(HttpServletRequest request, HttpServletResponse response, @RequestParam("files") MultipartFile[] files) throws ValidationException {

		Optional<MultipartFile> optional = Arrays.stream(files).filter(file -> !file.getOriginalFilename().matches(resNameRegex)).findAny();
		if (optional.isPresent()) {
			throw new ValidationException(optional.get().getOriginalFilename() + "文件格式不正确");
		}
		List<ResourceInfo> resources = resourcePeer.upload(files, SysUtil.getCurrentUserInfo());
		ControllerUtils.returnJson(request, response, resources);
	}

	@PostMapping("/uploadFileByType")
	@ModDesc(desc = "上传文件区分业务类型", pDesc = "", rDesc = "", rType = ResourceInfo.class)
	public RemoteResult uploadFileByType(@RequestParam("files") MultipartFile[] files, @RequestParam("type") Integer type) {

		Optional<MultipartFile> optional = Arrays.stream(files).filter(file -> !file.getOriginalFilename().matches(resNameRegex)).findAny();
		if (optional.isPresent()) {
			throw new ValidationException(optional.get().getOriginalFilename() + "文件格式不正确");
		}
		List<ResourceInfo> resources = resourcePeer.uploadFileByType(files, type);
		return new RemoteResult(resources);
	}

	@GetMapping("/deleteResource")
	@ModDesc(desc = "删除资源", pDesc = "", rDesc = "", rType = ResourceInfo.class)
	public RemoteResult deleteResource(@RequestParam Long id) {
		BinaryUtils.checkEmpty(id, "id");
		Integer result = resourcePeer.deleteResource(id);
		return new RemoteResult(result);
	}

	@GetMapping("/downFileByResId/{id}/{ciCode}")
	public ModelAndView downFileByResId(HttpServletRequest request, HttpServletResponse response, @PathVariable("id") Long id, @PathVariable("ciCode") String ciCode) throws ValidationException {
		List<FileResourceMeta> resources = resourcePeer.download(Collections.singletonList(id));
		return ControllerUtils.returnResource(request, response, ResourceUtil.installFile(resources.get(0)), null,
				false, resources.get(0).getName());
	}

	@GetMapping("/downFileByResId/{id}")
	public ModelAndView downFileByResId(HttpServletRequest request, HttpServletResponse response, @PathVariable("id") Long id) throws ValidationException {
		List<FileResourceMeta> resources = resourcePeer.download(Collections.singletonList(id));
		return ControllerUtils.returnResource(request, response, ResourceUtil.installFile(resources.get(0)), null,
				false, resources.get(0).getName());
	}

	@GetMapping("/images")
	@ModDesc(desc = "查询示例图", pDesc = "业务类型", rDesc = "", rType = DefaultFileVo.class)
	public RemoteResult getImages(@RequestParam Integer type) {
		List<DefaultFileVo> vo = resourcePeer.getImages(type);
		return new RemoteResult(vo);
	}

}
