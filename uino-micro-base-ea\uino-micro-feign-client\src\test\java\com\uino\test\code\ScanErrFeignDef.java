package com.uino.test.code;

import java.io.InputStream;
import java.lang.annotation.Annotation;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

import org.springframework.core.io.InputStreamSource;
import org.springframework.core.io.Resource;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import com.binary.core.util.BinaryUtils;

public class ScanErrFeignDef {

    public static Set<String> rBodyErr = new HashSet<String>();

    public static Set<String> annType = new HashSet<String>(
            Arrays.asList("org.springframework.web.bind.annotation.RequestBody",
                    //
                    "org.springframework.web.bind.annotation.RequestParam",
                    "org.springframework.web.bind.annotation.PathVariable",
                    "org.springframework.web.bind.annotation.RequestHeader",
                    "org.springframework.web.bind.annotation.RequestPart"));

    public static Set<String> methodAnnTypes = new HashSet<String>(
            Arrays.asList("org.springframework.web.bind.annotation.PostMapping",
                    //
                    "org.springframework.web.bind.annotation.GetMapping"
            // ,"org.springframework.web.bind.annotation.PathVariable"
            // ,"org.springframework.web.bind.annotation.RequestHeader"
            // , "org.springframework.web.bind.annotation.RequestPart"
            ));

    private static Set<String> ppname = new HashSet<String>();

    public static StringBuffer sbuf = new StringBuffer();

    public static StringBuffer pbuf = new StringBuffer();

    public static void main(String[] args) throws Exception {
        String packageStr = "com.uino.provider.feign.*.*Feign*";
        Resource[] resources = ClsUtils.findResource(packageStr);
        Class<?>[] classes = ClsUtils.findClass(resources);
        for (Class<?> clazz : classes) {
            aa(clazz);
        }
        Thread.sleep(100);
        System.out.println(rBodyErr.size());
        System.out.println(rBodyErr);
    }

    public static void aa(Class<?> clazz) throws Exception {
        if (!clazz.isInterface()) { return; }
        clear(sbuf);
        // sbuf.append(clazz.getName()).append("\n");
        int ii = sbuf.length();
        Method[] declaredMethods = clazz.getDeclaredMethods();
        for (Method method : declaredMethods) {
            clear(pbuf);
            ppname.clear();
            Annotation[] mas = method.getAnnotations();
            if (mas == null || mas.length == 0) {
                pbuf.append("            未定义方法的注解\n");
            } else {
                for (int i = 0; i < mas.length; i++) {
                    Annotation ma = mas[i];
                    if (!methodAnnTypes.contains(ma.annotationType().getName())) {
                        pbuf.append("           未知注解" + ma.annotationType().getName() + "\n");
                    }
                }
            }
            Parameter[] parameters = method.getParameters();
            if (parameters.length <= 0) {
                continue;
            }
            for (int i = 0; i < parameters.length; i++) {
                Parameter p = parameters[i];
                if (p.getAnnotations().length == 0) {
                    pbuf.append("        第[").append(i + 1).append("]个参数").append(p.getName()).append(",没有添加注解请补上\n");
                } else {
                    if (InputStreamSource.class.isAssignableFrom(p.getType())
                            || InputStream.class.isAssignableFrom(p.getType())) {
                        // System.out.println("啊啊啊");
                        RequestPart a = p.getAnnotation(RequestPart.class);
                        if (a == null) {
                            pbuf.append("        [").append(i).append("]").append(p.getName()).append("的注解类型应该是 ")
                                    .append(RequestPart.class.getName()).append("\n");
                        }
                    }
                    Annotation[] annotations = p.getAnnotations();
                    for (Annotation ann : annotations) {
                        Class<? extends Annotation> at = ann.annotationType();
                        // System.out.println(annotationType);
                        if (annType.contains(at.getName())) {
                            String name = null;
                            boolean flag = true;
                            if (at.getName().equals(RequestParam.class.getName())) {
                                RequestParam t = (RequestParam) ann;
                                name = BinaryUtils.isEmpty(t.name()) ? t.value() : t.name();
                            } else if (at.getName().equals(RequestPart.class.getName())) {
                                RequestPart t = (RequestPart) ann;
                                name = BinaryUtils.isEmpty(t.name()) ? t.value() : t.name();
                            } else if (at.getName().equals(PathVariable.class.getName())) {
                                PathVariable t = (PathVariable) ann;
                                name = BinaryUtils.isEmpty(t.name()) ? t.value() : t.name();
                            } else if (at.getName().equals(RequestHeader.class.getName())) {
                                RequestHeader t = (RequestHeader) ann;
                                name = BinaryUtils.isEmpty(t.name()) ? t.value() : t.name();
                            } else {
                                flag = false;
                            }
                            if (flag) {
                                if (BinaryUtils.isEmpty(name)
                                // || !name.equals(p.getName())
                                ) {
                                    pbuf.append("        [").append(i).append("]").append(p.getName())
                                            .append("的name(value)不存在,或者和参数名称不一样,请注意修改").append("\n");
                                } else if (contains(name)) {
                                    pbuf.append("        [").append(i).append("]").append(p.getName())
                                            .append("的name(value)").append(name).append("重复!\n");
                                }
                            }
                            if (at.getName().equals(RequestBody.class.getName())) {
                                RequestBody t = (RequestBody) ann;
                                if (!t.required()) {
                                    pbuf.append("        [").append(i).append("]").append(p.getName())
                                            .append(" RequestBody 不能为空");
                                }
                                rBodyErr.add(clazz.getName() + "." + method.getName() + "." + p.getName());
                            }
                        } else {
                            pbuf.append("        [").append(i).append("]").append(p.getName()).append("使用了")
                                    .append(at.getName()).append(" 类型\n");
                        }
                    }
                }
                if (pbuf.length() > 0) {
                    pbuf.append("\n");
                }
            }
            if (pbuf.length() > 0) {
                sbuf.append("    ").append(method.getName()).append("\n").append(pbuf);
            }
        }
        if (sbuf.length() != ii) {
            System.out.println(clazz.getName());
            Thread.sleep(40L);
            System.err.println(sbuf.toString());
        }
    }

    private static boolean contains(String name) {
        if (ppname.contains(name)) { return true; }
        if (!BinaryUtils.isEmpty(name)) {
            ppname.add(name);
        }
        return false;
    }

    private static void clear(StringBuffer sb) {
        sb.delete(0, sb.length());
    }
}
