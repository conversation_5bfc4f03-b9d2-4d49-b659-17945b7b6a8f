package com.uino.api.client.license.rpc;

import java.util.List;

import com.uino.bean.license.BaseLicenseAuthInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.binary.core.io.Resource;
import com.uinnova.product.vmdb.comm.model.license.CcLicenseAuthServer;
import com.uino.provider.feign.license.LicenseAuthFeign;
import com.uino.api.client.license.ILicenseAuthApiSvc;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
public class LicenseAuthApiSvcRpc implements ILicenseAuthApiSvc {

    @Autowired
    private LicenseAuthFeign licenseFeign;
    @Override
    public BaseLicenseAuthInfo queryLicenseAuthInfo() {
        return licenseFeign.queryLicenseAuthInfo();
    }

    @Override
    public List<CcLicenseAuthServer> queryServerList() {
        return licenseFeign.queryServerList();
    }

    @Override
    public Integer removeServer(Long serverId) {
        return licenseFeign.removeServer(serverId);
    }

    @Override
    public String createClientCode() {
        return licenseFeign.createClientCode();
    }

    @Override
    public void registerLicense(String authCode, String authUser) {
        licenseFeign.registerLicense(authCode, authUser);
    }

    @Override
    public void registerLicenseByDb() {
        licenseFeign.registerLicenseByDb();
    }

    @Override
    public Resource getQRCodeImage(Integer width, Integer height) {
        return licenseFeign.getQRCodeImage(width, height);
    }
}
