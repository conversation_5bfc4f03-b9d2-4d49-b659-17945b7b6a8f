package com.uino.cmdb.ci_rlt.mvc;

import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import com.alibaba.fastjson.JSON;
import com.binary.framework.web.RemoteResult;
import com.uino.StartBaseWebAppliaction;
import com.uino.dao.cmdb.ESCIRltSvc;
import com.uino.dao.cmdb.ESCISvc;
import com.uino.dao.cmdb.ESRltClassSvc;
import com.uino.bean.cmdb.base.ESCIClassInfo;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.business.BindCiRltRequestDto;

import lombok.extern.slf4j.Slf4j;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = { StartBaseWebAppliaction.class }, webEnvironment = WebEnvironment.RANDOM_PORT)
@Slf4j
@ActiveProfiles("provider-local")
public class BindCiRltTest {
    @Autowired
    private TestRestTemplate restTemplate;
    @MockBean
    private ESRltClassSvc esRltClassSvc;
    @MockBean
    private ESCISvc esCiSvc;
    @MockBean
    private ESCIRltSvc esCiRltSvc;
    private static String testUrl;

    @BeforeClass
    public static void initCls() {
        BindCiRltTest.testUrl = "/cmdb/ciRlt/bindCiRlt";
    }

    @Before
    public void start() {
        ESCIInfo sCi = new ESCIInfo();
        sCi.setId(111L);
        sCi.setCiCode("111cicode");
        sCi.setClassId(999L);
        Mockito.when(esCiSvc.getById(Mockito.eq(111L))).thenReturn(sCi);
        ESCIInfo tCi = new ESCIInfo();
        tCi.setId(222L);
        tCi.setCiCode("222cicode");
        tCi.setClassId(999L);
        Mockito.when(esCiSvc.getById(Mockito.eq(222L))).thenReturn(sCi);
        ESCIClassInfo rltCls = new ESCIClassInfo();
        rltCls.setId(888L);
        Mockito.when(esRltClassSvc.getById(888L)).thenReturn(rltCls);
        Mockito.when(esCiRltSvc.saveOrUpdate(Mockito.any())).thenReturn(1L);
    }

    @Test
    public void test01() {
        BindCiRltRequestDto reqBean = BindCiRltRequestDto.builder().rltClassId(888L).sourceCiId(111L).targetCiId(222L).repetitionError(true)
                .build();
        String responseBodyStr = restTemplate.postForObject(testUrl, reqBean, String.class);
        RemoteResult responseBody = JSON.parseObject(responseBodyStr, RemoteResult.class);
        Assert.assertTrue(responseBody.isSuccess());

    }
}
