package com.uinnova.product.eam.web.asset.mvc;

import com.binary.framework.web.RemoteResult;
import com.uinnova.product.eam.model.AssetWarehouseDirVo;
import com.uinnova.product.eam.web.asset.peer.AssetWarehouseDirPeer;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 资产仓库目录文件夹
 */
@RestController
@RequestMapping("/eam/assetWarehouse")
public class AssetWarehouseDirMvc {

    public static final String ASSET_WAREHOUSE_DIR_EDIT = "ASSET_WAREHOUSE_DIR_EDIT";
    @Resource
    AssetWarehouseDirPeer assetWarehouseDirPeer;

    @Resource
    StringRedisTemplate stringRedisTemplate;

    @GetMapping("openLock")
    public RemoteResult openLock() {
        Boolean ifAbsent = stringRedisTemplate.opsForValue().setIfAbsent(ASSET_WAREHOUSE_DIR_EDIT, "update");
        return new RemoteResult(ifAbsent);
    }

    @GetMapping("closeLock")
    public RemoteResult closeLock() {
        Boolean delete = stringRedisTemplate.delete(ASSET_WAREHOUSE_DIR_EDIT);
        return new RemoteResult(delete);
    }



    /**
     * 保存修改资产仓库目录
     *
     * @return
     */
    @PostMapping("saveOrUpdate")
    public RemoteResult saveOrUpdate(@RequestBody List<AssetWarehouseDirVo> dirVos) {
        Integer num = assetWarehouseDirPeer.saveOrUpdate(dirVos);
        return new RemoteResult(num);
    }

    @GetMapping("delModule")
    public RemoteResult delModule(@RequestParam Long id) {
        Integer num = assetWarehouseDirPeer.delete(id);
        return new RemoteResult(num);
    }

    @GetMapping("getTree")
    public RemoteResult getTree(@RequestParam(required = false) Long userId) {
        List<AssetWarehouseDirVo> result = assetWarehouseDirPeer.getTree(userId);
        return new RemoteResult(result);
    }


    @GetMapping("getConfigTree")
    public RemoteResult getConfigTree() {
        List<AssetWarehouseDirVo> result = assetWarehouseDirPeer.getConfigTree();
        return new RemoteResult(result);
    }

    @GetMapping("/migrationAssetModule")
    public RemoteResult migrationAssetModule() {
        assetWarehouseDirPeer.migrationAssetModule();
        return new RemoteResult(1);
    }


}
