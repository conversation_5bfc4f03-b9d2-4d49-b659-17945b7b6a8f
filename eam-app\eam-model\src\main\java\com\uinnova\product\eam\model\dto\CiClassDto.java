package com.uinnova.product.eam.model.dto;

import com.uino.bean.cmdb.base.LibType;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class CiClassDto implements Serializable{
    private String like;

    private Integer pageNum;

    private Integer pageSize;

    private LibType libType;

    private String viewType;

    // 分类ID
    private Long id;

    // 分类IDS
    private List<Long> ids;
}
