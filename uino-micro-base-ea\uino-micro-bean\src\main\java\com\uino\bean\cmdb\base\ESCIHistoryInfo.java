package com.uino.bean.cmdb.base;

import java.io.Serializable;
import java.util.Map;

import com.uinnova.product.vmdb.comm.model.ci.CcCi;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * ES-CI历史
 * 
 * <AUTHOR>
 *
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value="ES-CI历史",description = "ES-CI历史信息")
public class ESCIHistoryInfo extends CcCi implements Serializable {
    /**
     * 
     */
    private static final long serialVersionUID = 6905266482135014905L;

    /**
     * CI历史版本唯一标识
     */
    @ApiModelProperty(value="CI历史版本唯一标识")
    private String uniqueCode;

    /**
     * CI属性
     */
    @ApiModelProperty(value="CI属性")
    private Map<String, Object> attrs;

    /**
     * DCV特有属性
     */
    @ApiModelProperty(value="DCV特有属性")
    private Map<String, String> dcvExtAttrs;

    /**
     * CI版本号
     */
    @ApiModelProperty(value="CI版本号")
    private Long version;

    /**
     * CI操作，0=新增或修改，1=删除
     */
    @ApiModelProperty(value="CI操作,0=新增或修改,1=删除",example = "0")
    @Builder.Default
    private Integer action = 0;

    /**
     * 动作类型
     * 
     * <AUTHOR>
     *
     */
    public static enum ActionType {
        SAVE_OR_UPDATE(0), DELETE(1),CLEAR(2);

        private int value;

        private ActionType(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }

        @SuppressWarnings("unused")
        public void setValue(int value) {
            this.value = value;
        }
    }
}
