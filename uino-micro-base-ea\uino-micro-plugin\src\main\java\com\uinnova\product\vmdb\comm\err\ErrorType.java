package com.uinnova.product.vmdb.comm.err;

import com.binary.core.i18n.LanguageResolver;
import com.binary.core.util.BinaryUtils;

/**
 * 根据错误类型国际化的错误信息
 * 
 * <AUTHOR>
 *
 */
public enum ErrorType {

	/**
	 * 
	 */
	ERR_1(1, "BS_MNAME_SUCCESSFUL", "成功"),
	/**
	 * 
	 */
	ERR_2(2, "BS_MNAME_FAILURE", "失败"),
	/**
	 * 
	 */
	ERR_3(3, "BS_MNAME_FREEZE_DATA", "当前系统为非正式产生系统，请购买授权许可证。"),
	/**
	 * 
	 */
	ERR_4(4, "BS_MNAME_CLASSNOTEXSIT", "分类不存在！"),
	/**
	 * 
	 */
	ERR_5(5, "BS_CC_CLASS_DELETING", "分类被删除中，请稍后再试！"),
	/**
	 * 
	 */
	ERR_6(6, "BS_CC_CLASS_UPDATING", "分类更新中，请稍后再试！"),
	/**
	 * 
	 */
	ERR_7(7, "BS_CC_CLASS_IMPORTING_CI", "分类数据导入中，请稍后再试！"),
	/**
	 * 
	 */
	ERR_8(8, "BS_CC_CLASS_EXPORTING_CI", "分类数据导出中，请稍后再试！"),
	/**
	 * 
	 */
	ERR_9(9, "BS_CC_CLASS_DELETING_CI", "分类数据清空中，请稍后再试！"),
	/**
	 * 
	 */
	ERR_10(10, "BS_CC_ATTR_VALUE_REQUIRED_LOST", "必填项不能为空"),
	/**
	 * 
	 */
	ERR_11(11, "BS_CC_ATTR_VALUE_NOTCOMPLIANCE", "属性值不符合约束规则！"),
	/**
	 * ciCode专属
	 */
	ERR_12(12, "BS_MNAME_RECORD_CONTAINS", "该CI已存在"),
	/**
	 * ciCode专属
	 */
	ERR_13(13, "BS_MNAME_CI_EXIST_OTHERCLASS", "CI已在其他分类中存在"),
	/**
	 * 
	 */
	ERR_14(14, "BS_MVTYPE_NOT_EXIST", "不存在"),
	/**
	 * 
	 */
	ERR_15(15, "BS_MVTYPE_EMPTY", "不可为空"),
	/**
	 * 
	 */
	ERR_16(16, "BS_CC_ATTR_VALUE_TYPENOTCONFORM", "属性值与类型不符！"),
	/**
	 * ciCode和关系code专属
	 */
	ERR_17(17, "BS_MNAME_SAVE_CONTAINS", "该批次中存在相同的CI！"),
	/**
	 * 业务主键专属
	 */
	ERR_18(18, "BS_MNAME_SAVE_CONTAINS", "该批次中存在相同的CI！"),
	/**
	 * 
	 */
	ERR_19(19, "BS_MNAME_STORAGEFAIL", "入库失败!"),
	/**
	 * 
	 */
	ERR_20(20, "BS_MNAME_SAVECIRLT_FAIL", "保存关系数据失败！"),
	/**
	 * 
	 */
	ERR_21(21, "BS_MDOMAIN_IMPORTFAIL", "导入失败！"),
	/**
	 * 
	 */
	ERR_22(22, "BS_MVTYPE_OVERLENGTH", "超长"),
	/**
	 * 业务主键专属
	 */
	ERR_23(23, "BS_MNAME_RECORD_CONTAINS", "该CI已存在"),
	/**
	 * 业务主键专属
	 */
	ERR_24(24, "BS_MNAME_CI_EXIST_OTHERCLASS", "CI已在其他分类中存在"),
	/**
	 * ATTRS全部为空专用
	 */
	ERR_25(25, "BS_MVTYPE_EMPTY", "不可为空"),
	/**
	 * 
	 */
	ERR_26(26, "BS_MNAME_CIPRIMARYKEY_ERRO", "业务主键值异常!"),

	/**
	 * 非法字符
	 */
	ERR_27(27, "BS_MNAME_ATTR_ILLEGAL", "属性值含有非法字符！");

	/**
	 * 错误类型
	 */
	private Integer type;
	/**
	 * 错误消息国际化代码
	 */
	private String value;
	/**
	 * 错误预留中文提示信息
	 */
	private String desc;
	
	private ErrorType(Integer type, String value, String desc) {
		this.type = type;
		this.value = value;
		this.desc = desc;
	}

	public Integer getType() {
		return type;
	}

	public String getValue() {
		return value;
	}

	public String getDesc() {
		return desc;
	}

	/**
	 * 根据错误类型获取错误信息
	 * 
	 * @param errType 错误
	 * @return 错误消息
	 */
	public static String getErrMsg(Integer errType) {
		return getErrMsg(errType, null);
	}

	/**
	 * 根据错误类型获取错误信息
	 * 
	 * @param errType   错误
	 * @param fieldName 验证字段名,与后台校验国际化方式一致[可选]
	 * @return 错误消息
	 */
	public static String getErrMsg(Integer errType, String fieldName) {
		ErrorType[] values = values();
		String message = null;
		for (ErrorType errorZhcType : values) {
			if (errorZhcType.getType().equals(errType)) {
				String languageCode = errorZhcType.getValue();
				try {
					message = LanguageResolver.trans(languageCode);
					if (!BinaryUtils.isEmpty(message) && message.startsWith("BS_")) {
					    message = errorZhcType.getDesc();
					}
					String bsMname = "BS_MNAME_";
					if (fieldName != null && fieldName != "") {
					    String nFieldName = "";
						if (!fieldName.startsWith(bsMname)) {
						    nFieldName = "BS_MNAME_" + fieldName.trim().toUpperCase();
						}
						String name = LanguageResolver.trans(nFieldName);
						if(name.equals(nFieldName)) {
						    name = fieldName;
						}
						message = "[" + name + "]" + message;
					}
				} catch (Exception e) {
					// 获取国际化信息异常
					message = errorZhcType.getDesc();
				}
				break;
			}
		}
		return message;
	}
	
}
