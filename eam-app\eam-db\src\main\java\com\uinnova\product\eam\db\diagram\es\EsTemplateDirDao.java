package com.uinnova.product.eam.db.diagram.es;

import com.uinnova.product.eam.base.diagram.mix.model.TemplateDir;
import com.uinnova.product.eam.base.diagram.mix.model.TemplateDirQuery;
import com.uino.dao.AbstractESBaseDao;
import com.uino.service.util.FileUtil;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.admin.indices.create.CreateIndexRequest;
import org.elasticsearch.action.admin.indices.get.GetIndexRequest;
import org.elasticsearch.action.admin.indices.settings.put.UpdateSettingsRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.stereotype.Repository;

import jakarta.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;

@Repository
@Slf4j
public class EsTemplateDirDao extends AbstractESBaseDao<TemplateDir, TemplateDirQuery> {

    @Override
    public String getIndex() {
        return "uino_eam_template_dir";
    }

    @Override
    public String getType() {
        return "_doc";
    }

    @PostConstruct
    public void init() {
        // 初始化加载模板数据
        try {
            RestHighLevelClient clientc = getClient();
            GetIndexRequest getRequest = new GetIndexRequest();
            getRequest.indices(getFullIndexName());
            boolean existIndex = clientc.indices().exists(getRequest, RequestOptions.DEFAULT);
            if (!existIndex) {
                List<TemplateDir> data = FileUtil.getData("/initdata/uino_eam_template_dir.json", TemplateDir.class);
                super.initIndex(data);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

}
