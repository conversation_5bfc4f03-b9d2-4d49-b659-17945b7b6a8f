package com.uino.bean.permission.query;

import java.io.Serializable;

/**
 * mapping-table: 用户菜单收藏关联表[SYS_USER_MODULE_ENSHRINE_RLT]
 * 
 * <AUTHOR>
 */
public class CSysUserModuleEnshrineRlt implements Serializable {

    /**
     * 
     */
    private static final long serialVersionUID = 1L;
    /**
     * condition-field: ID[ID] operate-Equal[=]
     */
    private Long id;

    /**
     * condition-field: ID[ID] operate-In[in]
     */
    private Long[] ids;

    /**
     * condition-field: ID[ID] operate-GTEqual[>=]
     */
    private Long startId;

    /**
     * condition-field: ID[ID] operate-LTEqual[<=]
     */
    private Long endId;

    /**
     * condition-field: 模块ID[MODULE_ID] operate-Equal[=]
     */
    private Long moduleId;

    /**
     * condition-field: 模块ID[MODULE_ID] operate-In[in]
     */
    private Long[] moduleIds;

    /**
     * condition-field: 模块ID[MODULE_ID] operate-GTEqual[>=]
     */
    private Long startModuleId;

    /**
     * condition-field: 模块ID[MODULE_ID] operate-LTEqual[<=]
     */
    private Long endModuleId;

    /**
     * condition-field: 用户ID[USER_ID] operate-Equal[=] 角色ID
     */
    private Long userId;

    /**
     * condition-field: 用户ID[USER_ID] operate-In[in] 角色ID
     */
    private Long[] userIds;

    /**
     * condition-field: 用户ID[USER_ID] operate-GTEqual[>=] 角色ID
     */
    private Long startUserId;

    /**
     * condition-field: 用户ID[USER_ID] operate-LTEqual[<=] 角色ID
     */
    private Long endUserId;

    /**
     * condition-field: 所属域[DOMAIN_ID] operate-Equal[=]
     */
    private Long domainId;

    /**
     * condition-field: 所属域[DOMAIN_ID] operate-In[in]
     */
    private Long[] domainIds;

    /**
     * condition-field: 所属域[DOMAIN_ID] operate-GTEqual[>=]
     */
    private Long startDomainId;

    /**
     * condition-field: 所属域[DOMAIN_ID] operate-LTEqual[<=]
     */
    private Long endDomainId;

    /**
     * condition-field: 创建时间[CREATE_TIME] operate-Equal[=] 创建时间:yyyyMMddHHmmss
     */
    private Long createTime;

    /**
     * condition-field: 创建时间[CREATE_TIME] operate-In[in] 创建时间:yyyyMMddHHmmss
     */
    private Long[] createTimes;

    /**
     * condition-field: 创建时间[CREATE_TIME] operate-GTEqual[>=] 创建时间:yyyyMMddHHmmss
     */
    private Long startCreateTime;

    /**
     * condition-field: 创建时间[CREATE_TIME] operate-LTEqual[<=] 创建时间:yyyyMMddHHmmss
     */
    private Long endCreateTime;

    /**
     * condition-field: 修改时间[MODIFY_TIME] operate-Equal[=] 修改时间:yyyyMMddHHmmss
     */
    private Long modifyTime;

    /**
     * condition-field: 修改时间[MODIFY_TIME] operate-In[in] 修改时间:yyyyMMddHHmmss
     */
    private Long[] modifyTimes;

    /**
     * condition-field: 修改时间[MODIFY_TIME] operate-GTEqual[>=] 修改时间:yyyyMMddHHmmss
     */
    private Long startModifyTime;

    /**
     * condition-field: 修改时间[MODIFY_TIME] operate-LTEqual[<=] 修改时间:yyyyMMddHHmmss
     */
    private Long endModifyTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long[] getIds() {
        return ids;
    }

    public void setIds(Long[] ids) {
        this.ids = ids;
    }

    public Long getStartId() {
        return startId;
    }

    public void setStartId(Long startId) {
        this.startId = startId;
    }

    public Long getEndId() {
        return endId;
    }

    public void setEndId(Long endId) {
        this.endId = endId;
    }

    public Long getModuleId() {
        return moduleId;
    }

    public void setModuleId(Long moduleId) {
        this.moduleId = moduleId;
    }

    public Long[] getModuleIds() {
        return moduleIds;
    }

    public void setModuleIds(Long[] moduleIds) {
        this.moduleIds = moduleIds;
    }

    public Long getStartModuleId() {
        return startModuleId;
    }

    public void setStartModuleId(Long startModuleId) {
        this.startModuleId = startModuleId;
    }

    public Long getEndModuleId() {
        return endModuleId;
    }

    public void setEndModuleId(Long endModuleId) {
        this.endModuleId = endModuleId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long[] getUserIds() {
        return userIds;
    }

    public void setUserIds(Long[] userIds) {
        this.userIds = userIds;
    }

    public Long getStartUserId() {
        return startUserId;
    }

    public void setStartUserId(Long startUserId) {
        this.startUserId = startUserId;
    }

    public Long getEndUserId() {
        return endUserId;
    }

    public void setEndUserId(Long endUserId) {
        this.endUserId = endUserId;
    }

    public Long getDomainId() {
        return domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }

    public Long[] getDomainIds() {
        return domainIds;
    }

    public void setDomainIds(Long[] domainIds) {
        this.domainIds = domainIds;
    }

    public Long getStartDomainId() {
        return startDomainId;
    }

    public void setStartDomainId(Long startDomainId) {
        this.startDomainId = startDomainId;
    }

    public Long getEndDomainId() {
        return endDomainId;
    }

    public void setEndDomainId(Long endDomainId) {
        this.endDomainId = endDomainId;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long[] getCreateTimes() {
        return createTimes;
    }

    public void setCreateTimes(Long[] createTimes) {
        this.createTimes = createTimes;
    }

    public Long getStartCreateTime() {
        return startCreateTime;
    }

    public void setStartCreateTime(Long startCreateTime) {
        this.startCreateTime = startCreateTime;
    }

    public Long getEndCreateTime() {
        return endCreateTime;
    }

    public void setEndCreateTime(Long endCreateTime) {
        this.endCreateTime = endCreateTime;
    }

    public Long getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Long[] getModifyTimes() {
        return modifyTimes;
    }

    public void setModifyTimes(Long[] modifyTimes) {
        this.modifyTimes = modifyTimes;
    }

    public Long getStartModifyTime() {
        return startModifyTime;
    }

    public void setStartModifyTime(Long startModifyTime) {
        this.startModifyTime = startModifyTime;
    }

    public Long getEndModifyTime() {
        return endModifyTime;
    }

    public void setEndModifyTime(Long endModifyTime) {
        this.endModifyTime = endModifyTime;
    }

}
