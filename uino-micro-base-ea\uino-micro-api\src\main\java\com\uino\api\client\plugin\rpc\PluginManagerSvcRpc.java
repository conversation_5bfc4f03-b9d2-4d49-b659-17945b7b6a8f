package com.uino.api.client.plugin.rpc;

import com.binary.core.io.Resource;
import com.binary.jdbc.Page;
import com.uino.api.client.plugin.IPluginManageSvc;
import com.uino.bean.plugin.base.ESPluginInfo;
import com.uino.bean.plugin.query.CPluginInfo;
import com.uino.provider.feign.plugin.PluginFeign;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import java.util.List;

@Service
public class PluginManagerSvcRpc implements IPluginManageSvc {

    @Autowired
    private PluginFeign pluginFeign;

    @Override
    public List<String> getServiceList() {
        return pluginFeign.getServiceList();
    }

    @Override
    public Long saveOrUpdate(ESPluginInfo pluginInfo) {
        return pluginFeign.saveOrUpdate(pluginInfo);
    }

    @Override
    public String uploadPlugin(Long id, MultipartFile file) {
        return pluginFeign.uploadPlugin(id,file);
    }

    @Override
    public boolean syncPlugin(MultipartFile file) {
        return pluginFeign.syncPlugin(file);
    }

    @Override
    public Resource downloadPlugin(Long id) {
        return pluginFeign.downloadPlugin(id);
    }

    @Override
    public Page<ESPluginInfo> queryList(int pageNum, int pageSize, CPluginInfo cPluginInfo) {
        return pluginFeign.queryList(pageNum,pageSize,cPluginInfo);
    }

    @Override
    public boolean loadOrUnloadPlugin(Long id) {
        return pluginFeign.loadOrUnloadPlugin(id);
    }

    @Override
    public boolean deletePlugin(Long id) {
        return pluginFeign.deletePlugin(id);
    }

    @Override
    public List<String> queryListByOpenServer(String serverName) {
        return pluginFeign.queryListByOpenServer(serverName);
    }
}
