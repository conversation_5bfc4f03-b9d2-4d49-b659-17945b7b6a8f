package com.uino.service.cmdb.microservice;

import com.uino.bean.cmdb.base.ESCiClassEncode;
import com.uino.bean.cmdb.base.LibType;

import java.util.List;

/**
 * 分类编码字段处理接口
 * <AUTHOR>
 */
public interface ICIClassEncodeSvc {

    /**
     * 获取分类最大编码
     * @param classCode 分类标识
     * @param proName 字段名称
     * @param domainId 领域id
     * @param libType 分库：私有库&设计库->DESIGN,运行库->BASELINE
     * @return 当前分类最大编码
     */
    Long getMaxNum(String classCode, String proName, Long attrDefId, Long domainId, LibType libType);

    /**
     * 获取分类最大编码
     * @param classCode 分类标识
     * @param proName 字段名称
     * @param domainId 领域id
     * @param libType 分库：私有库&设计库->DESIGN,运行库->BASELINE
     * @return 当前分类最大编码
     */
    Long getMaxNumByES(String classCode, String proName, Long attrDefId, Long domainId, LibType libType);

    /**
     * 保存或更新分类最大编码
     * @param maxNum 最大编码
     * @param classCode 分类标识
     * @param proName 字段名称
     * @param domainId 领域id
     * @param libType 分库：私有库&设计库->DESIGN,运行库->BASELINE
     * @return 当前分类最大编码
     */
    Long saveOrUpdate(Long maxNum, String classCode, String proName, Long domainId, LibType libType);

    /**
     * 清空编码存储记录(ES+Redis)
     * @param classIds 分类id
     */
    void clearCacheEncode(List<Long> classIds);
}
