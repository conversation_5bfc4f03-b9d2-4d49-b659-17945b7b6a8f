FROM corvis/java8:latest
MAINTAINER uino
USER root
ADD application-local.properties /usr/local/
ADD bootstrap.properties /usr/local/
ADD eam-deploy.tar /usr/local/
ADD docker_start.sh /usr/local/
RUN rm -rf /etc/localtime && ln -s /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
RUN localedef -c -f UTF-8 -i zh_CN zh_CN.utf8
ENV LC_ALL zh_CN.utf8
RUN rm -f /usr/local/eam/conf/application-local.properties \
    && rm -f /usr/local/eam/conf/bootstrap.properties \
	&& mv /usr/local/docker_start.sh /usr/local/eam/bin/ \
	&& mv /usr/local/application-local.properties /usr/local/eam/conf/ \
    && mv /usr/local/bootstrap.properties /usr/local/eam/conf/ \
	&& chmod -R 777 /usr/local/eam/*

CMD /usr/local/eam/bin/docker_start.sh