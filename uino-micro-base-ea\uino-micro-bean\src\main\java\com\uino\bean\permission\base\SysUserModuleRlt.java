package com.uino.bean.permission.base;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

/**
 * mapping-table: 用户模块关联表[SYS_USER_MODULE_RLT]
 * 
 * <AUTHOR>
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value="用户模块关联表",description = "用户模块关联表")
public class SysUserModuleRlt implements Serializable {
	private static final long serialVersionUID = 1L;

	/** ID */
	@ApiModelProperty(value="id",example = "123")
	private Long id;

	/** 模块ID */
	@ApiModelProperty(value="模块id",example = "123")
	private Long moduleId;

	/** 用户ID */
	@ApiModelProperty(value="用户id",example = "123")
	private Long userId;

	/** 所属域 */
	@ApiModelProperty(value="所属域")
	private Long domainId;

	/** 创建时间 */
	@ApiModelProperty(value="创建时间")
	private Long createTime;

	/** 修改时间 */
	@ApiModelProperty(value="修改时间")
	private Long modifyTime;

	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getModuleId() {
		return this.moduleId;
	}

	public void setModuleId(Long moduleId) {
		this.moduleId = moduleId;
	}

	public Long getUserId() {
		return this.userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public Long getDomainId() {
		return this.domainId;
	}

	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}

	public Long getCreateTime() {
		return this.createTime;
	}

	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}

	public Long getModifyTime() {
		return this.modifyTime;
	}

	public void setModifyTime(Long modifyTime) {
		this.modifyTime = modifyTime;
	}

}
