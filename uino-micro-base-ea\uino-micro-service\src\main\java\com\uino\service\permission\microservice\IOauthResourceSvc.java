package com.uino.service.permission.microservice;

import java.util.List;

import com.uino.bean.permission.base.OauthResourceDetail;

/**
 * oauth资源端
 * <AUTHOR>
 *
 */
public interface IOauthResourceSvc {

	/**
	 * 保存oauth资源段
	 * @param saveDto
	 * @return
	 */
	public boolean save(OauthResourceDetail saveDto);

	/**
	 * 获取所有oauth资源端列表
	 * @return
	 */
	public List<OauthResourceDetail> list();
	
	/**
	 * 根据资源端名称获取资源端详情
	 * @param name
	 * @return
	 */
	public OauthResourceDetail getDetail(String name);
	
}
