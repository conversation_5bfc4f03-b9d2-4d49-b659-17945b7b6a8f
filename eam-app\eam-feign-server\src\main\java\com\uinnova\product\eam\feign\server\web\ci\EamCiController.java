package com.uinnova.product.eam.feign.server.web.ci;

import com.uinnova.product.eam.feign.client.IEamCiFeign;
import com.uinnova.product.eam.model.CiQueryCdtExtend;
import com.uinnova.product.eam.model.VcCiClassInfoDto;
import com.uinnova.product.eam.service.IEamCiSvc;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uino.bean.cmdb.base.LibType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/feign/ci")
public class EamCiController implements IEamCiFeign {

    @Resource
    private IEamCiSvc iEamCiSvc;
    @Override
    public List<VcCiClassInfoDto> queryCiCountByClass(@RequestBody CiQueryCdtExtend cdt, @RequestParam("libType") LibType libType) {
        return iEamCiSvc.queryCiCountByClass(cdt,libType);
    }

    @Override
    public List<CcCiInfo> getCiInfoListByClassName(@RequestParam("ciClassName") String ciClassName, @RequestParam("libType") LibType libType) {
        return iEamCiSvc.getCiInfoListByClassName(ciClassName,libType);
    }
}
