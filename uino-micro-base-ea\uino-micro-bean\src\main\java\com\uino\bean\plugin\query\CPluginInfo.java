package com.uino.bean.plugin.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description
 * @Date 2021/9/27
 * @Version 1.0
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "插件信息", description = "插件信息")
public class CPluginInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value="id",example = "123")
    private Long id;

    @ApiModelProperty(value="登录代码")
    private String name;

    @ApiModelProperty(value="名称")
    private String nameEqual;

    @ApiModelProperty(value="名称")
    private String[] names;

    @ApiModelProperty(value="加载状态")
    private Integer loadStatus;


}
