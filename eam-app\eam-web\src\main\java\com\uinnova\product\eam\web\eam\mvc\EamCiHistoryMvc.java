package com.uinnova.product.eam.web.eam.mvc;

import java.util.List;
import java.util.Map;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import com.uino.bean.cmdb.base.LibType;
import com.uino.bean.cmdb.base.LibType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import com.binary.framework.util.ControllerUtils;
import com.uinnova.product.eam.service.ICIHistorySwitchSvc;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uinnova.product.vmdb.comm.doc.annotation.MvcDesc;
import com.uino.bean.cmdb.base.ESCIHistoryInfo;
import com.uino.bean.cmdb.query.ESCIHistorySearchBean;

@Controller
@RequestMapping("/eam/ciHistory")
@MvcDesc(author = "zmj", desc = "对象管理-CI历史版本")
public class EamCiHistoryMvc {
    @Autowired
    private ICIHistorySwitchSvc ciHistorySvc;

    @RequestMapping("/getCIVersionList")
    @ModDesc(desc = "根据CI id查询历史版本号列表", pDesc = "CI id", pType = Long.class, rDesc = "版本号列表", rType = Map.class)
    public void getCIVersionList(HttpServletRequest request, HttpServletResponse response, @RequestParam(defaultValue = "BASELINE") LibType libType, @RequestBody ESCIHistorySearchBean bean) {
        List<String> res = ciHistorySvc.getCIVersionList(bean.getCiCode(), bean.getClassId(), libType);
        ControllerUtils.returnJson(request, response, res);
    }

    @RequestMapping("/getCIInfoHistoryByCIVersion")
    @ModDesc(desc = "查询指定CI版本信息", pDesc = "查询条件", pType = ESCIHistorySearchBean.class, rDesc = "历史版本数据", rType = ESCIHistoryInfo.class)
    public void getCIInfoHistoryByCIVersion(HttpServletRequest request, HttpServletResponse response, @RequestParam(defaultValue = "BASELINE") LibType libType, @RequestBody ESCIHistorySearchBean bean) {
        ESCIHistoryInfo res = ciHistorySvc.getCIInfoHistoryByCIVersion(bean.getCiCode(), bean.getClassId(), bean.getVersion(), libType);
        ControllerUtils.returnJson(request, response, res);
    }
}
