package com.uino.bean.permission.base;




import java.io.Serializable;

import lombok.Builder;
import lombok.experimental.Tolerate;


/**
 * mapping-table: 用户组织关联表[SYS_USER_ORG_RLT]
 * 
 * <AUTHOR>
 */
@Builder
public class SysUserOrgRlt implements Serializable {
	private static final long serialVersionUID = 1L;
	@Tolerate
	public SysUserOrgRlt(){
		
	}

	 /**ID*/
	private Long id;


	 /**用户ID*/
	private Long userId;


	 /**组织ID*/
	private Long orgId;


	 /**所属域*/
	private Long domainId;


	 /**创建人*/
	private String creator;


	 /**修改人*/
	private String modifier;


	 /**创建时间*/
	private Long createTime;


	 /**修改时间*/
	private Long modifyTime;




	public Long getId() {
		return this.id;
	}
	public void setId(Long id) {
		this.id = id;
	}


	public Long getUserId() {
		return this.userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}


	public Long getOrgId() {
		return this.orgId;
	}
	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}


	public Long getDomainId() {
		return this.domainId;
	}
	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}


	public String getCreator() {
		return this.creator;
	}
	public void setCreator(String creator) {
		this.creator = creator;
	}


	public String getModifier() {
		return this.modifier;
	}
	public void setModifier(String modifier) {
		this.modifier = modifier;
	}


	public Long getCreateTime() {
		return this.createTime;
	}
	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}


	public Long getModifyTime() {
		return this.modifyTime;
	}
	public void setModifyTime(Long modifyTime) {
		this.modifyTime = modifyTime;
	}


}


