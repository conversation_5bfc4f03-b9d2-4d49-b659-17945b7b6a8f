package com.uinnova.product.eam.service.impl;

import com.binary.core.exception.BinaryException;
import com.binary.core.util.BinaryUtils;
import com.uinnova.product.eam.comm.model.es.AssetAttrDataScreen;
import com.uinnova.product.eam.service.AssetAttrDataScreenSvc;
import com.uinnova.product.eam.service.es.AssetAttrDataScreenDao;
import com.uino.bean.permission.base.SysUser;
import com.uino.util.sys.SysUtil;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

@Service
public class AssetAttrDataScreenSvcImpl implements AssetAttrDataScreenSvc {


    @Resource
    AssetAttrDataScreenDao dataScreenDao;

    @Override
    public Long saveOrUpdate(AssetAttrDataScreen assetAttrDataScreen) {
        BinaryUtils.checkEmpty(assetAttrDataScreen, "筛选信息");
        BinaryUtils.checkEmpty(assetAttrDataScreen.getClassId(),"分类Id");
        BinaryUtils.checkEmpty(assetAttrDataScreen.getBindId(),"筛选项Id");

        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("bindId", assetAttrDataScreen.getBindId()));
        query.must(QueryBuilders.termQuery("classId", assetAttrDataScreen.getClassId()));
        AssetAttrDataScreen queryAssetDataScreen = dataScreenDao.selectOne(query);
        if (!BinaryUtils.isEmpty(queryAssetDataScreen)) {
            assetAttrDataScreen.setId(queryAssetDataScreen.getId());
        }

        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        long timeMillis = System.currentTimeMillis();
        if (BinaryUtils.isEmpty(assetAttrDataScreen.getId())) {
            assetAttrDataScreen.setCreator(currentUserInfo.getLoginCode());
            assetAttrDataScreen.setCreateTime(timeMillis);
        }
        assetAttrDataScreen.setModifier(currentUserInfo.getLoginCode());
        assetAttrDataScreen.setModifyTime(timeMillis);
        return dataScreenDao.saveOrUpdate(assetAttrDataScreen);
    }

    @Override
    public AssetAttrDataScreen getDateScreenInfo(Long bindId, Long classId) {
        BinaryUtils.checkEmpty(bindId,"筛选项Id");
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("bindId", bindId));
        query.must(QueryBuilders.termQuery("classId", classId));
        return dataScreenDao.selectOne(query);
    }

    @Override
    public Integer removeDateScreenInfo(Long bindId, Long classId) {
        BinaryUtils.checkEmpty(bindId,"筛选项Id");
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("bindId", bindId));
        query.must(QueryBuilders.termQuery("classId",classId));
        AssetAttrDataScreen assetAttrDataScreen = dataScreenDao.selectOne(query);
        if (BinaryUtils.isEmpty(assetAttrDataScreen)) {
            throw new BinaryException("数据筛选信息不存在");
        }
        return dataScreenDao.deleteById(assetAttrDataScreen.getId());
    }
}
