package com.uinnova.product.vmdb.provider.sys.bean;

import com.binary.framework.bean.annotation.Comment;
import com.binarys.product.sys.comm.model.sys.SysOrg;
import com.binarys.product.sys.comm.model.sys.SysRole;

import java.io.Serializable;
import java.util.List;

@Comment("组织信息和组织对应的角色信息(包含继承于父亲的角色)")
public class SysOrgInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @Comment("组织信息")
    private SysOrg sysOrg;

    @Comment("组织对应的角色信息")
    private List<SysRole> sysRoles;

    @Comment("组织层级路径")
    private List<String> orgPath;

    public SysOrg getSysOrg() {
        return sysOrg;
    }

    public void setSysOrg(SysOrg sysOrg) {
        this.sysOrg = sysOrg;
    }

    public List<SysRole> getSysRoles() {
        return sysRoles;
    }

    public void setSysRoles(List<SysRole> sysRoles) {
        this.sysRoles = sysRoles;
    }

    public List<String> getOrgPath() {
        return orgPath;
    }

    public void setOrgPath(List<String> orgPath) {
        this.orgPath = orgPath;
    }

}
