package com.uinnova.product.eam.service.system.impl;

import com.binary.core.util.BinaryUtils;
import com.binary.jdbc.Page;
import com.uinnova.product.eam.base.model.DictInfo;
import com.uinnova.product.eam.service.system.IDictSvc;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClass;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.query.ESAttrBean;
import com.uino.bean.cmdb.query.ESCISearchBean;
import com.uino.api.client.cmdb.ICIApiSvc;
import com.uino.api.client.cmdb.ICIClassApiSvc;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@Service
public class DictSvcImpl implements IDictSvc {

    @Resource
    private ICIApiSvc ciApiSvc;

    @Resource
    private ICIClassApiSvc ciClassApiSvc;

    @Override
    public Map<String, String> getAllInfo(List<String> codeTypes,String className) {
        Map<String,String> map = new HashMap<>();
        CCcCiClass cCcCiClass = new CCcCiClass();
        cCcCiClass.setClassNameEqual(className);
        List<CcCiClassInfo> ccCiClassInfos = ciClassApiSvc.queryClassByCdt(cCcCiClass);
        ESCISearchBean searchBean = new ESCISearchBean();
        searchBean.setClassIds(Collections.singletonList(ccCiClassInfos.get(0).getCiClass().getId()));
        List<ESAttrBean> attrBeans = new ArrayList<>(codeTypes.size() + 1);
        Supplier<ESAttrBean> supplier = ESAttrBean::new;
        searchBean.setPageNum(1);
        searchBean.setPageSize(200);
        codeTypes.forEach(each->{
            ESAttrBean attrBean = supplier.get();
            attrBean.setOptType(1);
            attrBean.setValue(each);
            attrBean.setKey("CODE_TYPE");
            attrBeans.add(attrBean);
        });
        if(codeTypes.size() == 1){
            searchBean.setAndAttrs(attrBeans);
        }else {
            searchBean.setOrAttrs(attrBeans);
        }
        Page<ESCIInfo> page =  ciApiSvc.searchESCIByBean(searchBean);
        List<DictInfo> list = page.getData().stream().map(DictInfo::new).collect(Collectors.toList());
        list.forEach(each -> {
            map.put(each.getCodeCode(),each.getCodeName());
        });
        return map;
    }

    @Override
    public List<DictInfo> selectListByType(List<String> codeTypes, String parentCode, String className) {
        CCcCiClass cCcCiClass = new CCcCiClass();
        cCcCiClass.setClassNameEqual(className);
        List<CcCiClassInfo> ccCiClassInfos = ciClassApiSvc.queryClassByCdt(cCcCiClass);

        ESCISearchBean searchBean = new ESCISearchBean();
        searchBean.setClassIds(Collections.singletonList(ccCiClassInfos.get(0).getCiClass().getId()));
        List<ESAttrBean> attrBeans = new ArrayList<>(codeTypes.size() + 1);
        Supplier<ESAttrBean> supplier = ESAttrBean::new;
        codeTypes.forEach(each->{
            ESAttrBean attrBean = supplier.get();
            attrBean.setOptType(1);
            attrBean.setValue(each);
            attrBean.setKey("CODE_TYPE");
            attrBeans.add(attrBean);
        });
        if(!BinaryUtils.isEmpty(parentCode)){
            ESAttrBean attrBean = new ESAttrBean();
            attrBean.setOptType(1);
            attrBean.setValue(parentCode);
            attrBean.setKey("PARENT_CODE");
            attrBeans.add(attrBean);
        }
        if(codeTypes.size() == 1){
            searchBean.setAndAttrs(attrBeans);
        }else {
            searchBean.setOrAttrs(attrBeans);
        }
        searchBean.setSortField("attrs.ID");
        searchBean.setAsc(true);
        searchBean.setPageNum(1);
        searchBean.setPageSize(500);
        Page<ESCIInfo> page =  ciApiSvc.searchESCIByBean(searchBean);
        if(CollectionUtils.isEmpty(page.getData())){
            return Collections.emptyList();
        }
        return  page.getData().stream().map(DictInfo::new).collect(Collectors.toList());
    }

    @Override
    public Map<String, List<DictInfo>> selectGroupList(List<String> codeTypes, String className) {
        List<DictInfo> list = selectListByType(codeTypes, null, className);
        return list.stream().collect(Collectors.groupingBy(DictInfo::getCodeType));
    }
}
