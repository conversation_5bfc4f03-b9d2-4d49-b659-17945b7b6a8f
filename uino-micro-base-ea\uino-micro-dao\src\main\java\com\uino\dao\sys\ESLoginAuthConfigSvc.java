package com.uino.dao.sys;

import java.util.HashMap;
import java.util.Map;

import jakarta.annotation.PostConstruct;

import org.springframework.stereotype.Repository;

import com.alibaba.fastjson.JSONObject;
import com.uino.dao.AbstractESBaseDao;
import com.uino.dao.ESConst;
import com.uino.bean.sys.base.LoginAuthConfig;

@Repository
public class ESLoginAuthConfigSvc extends AbstractESBaseDao<LoginAuthConfig, JSONObject> {

    @Override
    public String getIndex() {
        return ESConst.INDEX_SYS_LOGIN_AUTH_INTEGRATION;
    }

    @Override
    public String getType() {
        return ESConst.INDEX_SYS_LOGIN_AUTH_INTEGRATION;
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }
    
    @Override
    protected Map<String, Class<?>> addInitProperties() {
        Map<String, Class<?>> r = new HashMap<String, Class<?>>();
        r.put("protoStatus", Long.class);
        r.put("protoName", String.class);
        return r;
    }

}
