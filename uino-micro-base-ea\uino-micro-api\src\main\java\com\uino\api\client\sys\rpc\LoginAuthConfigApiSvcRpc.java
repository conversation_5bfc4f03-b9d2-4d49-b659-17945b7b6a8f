package com.uino.api.client.sys.rpc;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.binary.jdbc.Page;
import com.uino.bean.sys.base.LdapUserMapping;
import com.uino.bean.sys.base.LoginAuthConfig;
import com.uino.bean.sys.business.ActiveLoginAuthConfigDto;
import com.uino.bean.sys.business.LoginAuthConfigDto;
import com.uino.provider.feign.sys.LoginAuthConfigFeign;
import com.uino.api.client.sys.ILoginAuthConfigApiSvc;

@Service
public class LoginAuthConfigApiSvcRpc implements ILoginAuthConfigApiSvc {

    @Autowired
    LoginAuthConfigFeign loginAuthConfigFegin;

    @Override
    public LoginAuthConfig queryById(Long id) {
        return loginAuthConfigFegin.queryById(id);
    }

    @Override
    public Page<LoginAuthConfig> queryPage(LoginAuthConfigDto authConfigCdt) {
        return loginAuthConfigFegin.queryPage(authConfigCdt);
    }

    @Override
    public Long saveOrUpdate(LoginAuthConfig authConfig) {
        return loginAuthConfigFegin.saveOrUpdate(authConfig);
    }

    @Override
    public void activeConfig(ActiveLoginAuthConfigDto active) {
        loginAuthConfigFegin.activeConfig(active);
    }

    @Override
    public boolean testConnection(LoginAuthConfig authConfig) {
        return loginAuthConfigFegin.testConnection(authConfig);
    }

    @Override
    public LdapUserMapping testConnectionAndMappingUser(LoginAuthConfig authConfig) {
        return loginAuthConfigFegin.testConnectionAndMappingUser(authConfig);
    }

    @Override
    public void removeById(Long id) {
        loginAuthConfigFegin.removeById(id);
    }
}
