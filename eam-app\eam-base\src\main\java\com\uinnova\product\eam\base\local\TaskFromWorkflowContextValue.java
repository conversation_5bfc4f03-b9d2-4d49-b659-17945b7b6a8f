package com.uinnova.product.eam.base.local;

import lombok.Data;

@Data
public class TaskFromWorkflowContextValue {

    public TaskFromWorkflowContextValue(Boolean fromWorkflow, String startUserLoginCode) {
        this.fromWorkflow = fromWorkflow;
        this.startUserLoginCode = startUserLoginCode;
    }

    private Boolean fromWorkflow;
    private String startUserLoginCode;
    private String processInstanceId;
}
