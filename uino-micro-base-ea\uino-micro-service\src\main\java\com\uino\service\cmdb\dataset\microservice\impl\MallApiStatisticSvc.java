package com.uino.service.cmdb.dataset.microservice.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.binary.core.exception.MessageException;
import com.binary.core.util.BinaryUtils;
import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClass;
import com.uinnova.product.vmdb.comm.model.es.ESCiClassAttrDef;
import com.uino.bean.cmdb.base.ESCIAttrDefInfo;
import com.uino.bean.cmdb.base.ESCIClassInfo;
import com.uino.bean.cmdb.base.dataset.DataSetMallApi;
import com.uino.bean.cmdb.base.dataset.DataSetMallApiRelationRule;
import com.uino.bean.cmdb.base.dataset.DataSetMallApiStatistic;
import com.uino.bean.cmdb.base.dataset.batch.DataSetExeResult;
import com.uino.bean.cmdb.base.dataset.batch.SimpleFriendInfo;
import com.uino.bean.cmdb.base.dataset.chart.Chart;
import com.uino.bean.cmdb.base.dataset.chart.ChartEnum;
import com.uino.bean.cmdb.base.dataset.log.DataSetMallApiLog;
import com.uino.bean.cmdb.base.dataset.relation.RelationRuleNode;
import com.uino.bean.cmdb.query.ESCIClassSearchBean;
import com.uino.bean.cmdb.query.ESCISearchBean;
import com.uino.bean.permission.base.SysUser;
import com.uino.dao.cmdb.dataset.ESDataSetExeResultSvc;
import com.uino.dao.cmdb.dataset.ESDataSetMallApiLogSvc;
import com.uino.dao.cmdb.dataset.ESDataSetSvc;
import com.uino.dao.util.ESUtil;
import com.uino.service.cmdb.dataset.microservice.IDataSetSvc;
import com.uino.service.cmdb.dataset.microservice.IMallApiSvc;
import com.uino.service.cmdb.dataset.microservice.IRelationRuleAnalysisSvc;
import com.uino.service.cmdb.microservice.ICIClassSvc;
import com.uino.service.cmdb.microservice.ICISvc;
import com.uino.util.sys.SysUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * 统计数据集
 *
 * @Date 2021/01/12 15:46
 * <AUTHOR> zmj
 */
@Service
@Slf4j
public class MallApiStatisticSvc implements IMallApiSvc {

    @Autowired
    private ICIClassSvc iciClassSvc;
    @Autowired
    private ICISvc iciSvc;

    @Autowired
    private IDataSetSvc dataSvc;

    @Autowired
    private ESDataSetSvc esDataSvc;

    @Autowired
    private ESDataSetExeResultSvc esDataSetExeResultSvc;

    @Autowired
    private IRelationRuleAnalysisSvc relationRuleAnalysisSvc;

    @Autowired
    private ESDataSetMallApiLogSvc esDataSetMallApiLogSvc;

    @Override
    public DataSetMallApi checkCharacteristic(SysUser user, JSONObject jsonObject) {
        DataSetMallApiStatistic dataSetMetrics = new DataSetMallApiStatistic(jsonObject);
        dataSetMetrics.valid();
        validStatistic(dataSetMetrics);
        return dataSetMetrics;
    }

    @Override
    public JSONObject execute(DataSetMallApi dataSetMallApi, JSONObject jsonObject) {
        long curTime = System.currentTimeMillis();
        Long domainId = dataSetMallApi.getDomainId();
        String username = "system";
        try {
            username = SysUtil.getCurrentUserInfo().getLoginCode();
        } catch (Exception e) {
            log.error("获取登陆用户失败，所有值取默认值");
        }
        boolean isSuccess = true;
        try {
            DataSetMallApiStatistic dataSetStatistic = (DataSetMallApiStatistic) dataSetMallApi;
            Integer dataSourceType = jsonObject.containsKey("dataSourceType") ? jsonObject.getInteger("dataSourceType") : dataSetStatistic.getDataSourceType();
            Long dataSourceId = jsonObject.containsKey("dataSourceId") ? jsonObject.getLong("dataSourceId") : dataSetStatistic.getDataSourceId();
            List<Long> dimensionClassIds =
                    jsonObject.containsKey("dimensionClassIds") ? JSON.parseArray(JSON.toJSONString(jsonObject.get("dimensionClassIds")), Long.class) : dataSetStatistic.getDimensionClassIds();
            Long dimensionDefId = jsonObject.containsKey("dimensionDefId") ? jsonObject.getLong("dimensionDefId") : dataSetStatistic.getDimensionDefId();
            Long statisticClassId = jsonObject.containsKey("statisticClassId") ? jsonObject.getLong("statisticClassId") : dataSetStatistic.getStatisticClassId();
            Long statisticDefId = jsonObject.containsKey("statisticDefId") ? jsonObject.getLong("statisticDefId") : dataSetStatistic.getStatisticDefId();
            Integer statisticalType = jsonObject.containsKey("statisticalType") ? jsonObject.getInteger("statisticalType") : dataSetStatistic.getStatisticalType();
            Integer chartType = dataSetStatistic.getChartType();

            Assert.notNull(dataSourceType, "X_PARAM_NOT_NULL${name:dataSourceType}");
            Assert.isTrue(!BinaryUtils.isEmpty(dimensionClassIds), "X_PARAM_NOT_NULL${name:dimensionClassIds}");
            Assert.notNull(statisticalType, "X_PARAM_NOT_NULL${name:statisticalType}");
            JSONObject retJson = new JSONObject();
            // 配置数据集
            if (dataSourceType == 1) {
                Assert.notNull(dataSourceId, "X_PARAM_NOT_NULL${name:dataSourceId}");
                Assert.isTrue(dimensionClassIds.size() == 1, "BS_MVTYPE_ARG_ERROR");
                Assert.notNull(dimensionDefId, "X_PARAM_NOT_NULL${name:dimensionDefId}");
                Assert.notNull(statisticClassId, "X_PARAM_NOT_NULL${name:statisticClassId}");
                Assert.notNull(statisticDefId, "X_PARAM_NOT_NULL${name:statisticDefId}");
                JSONObject sourceJsonObject = esDataSvc.getById(dataSourceId);
                Assert.notNull(sourceJsonObject, "UINO_BS_OBJ_RELATION_RULE_DATASET#{field:BS_MVTYPE_NOT_EXIST}");
                Long dimensionClassId = dimensionClassIds.get(0);
                ESCIClassInfo dimensionClassInfo = iciClassSvc.queryESClassInfoById(dimensionClassId);
                Assert.notNull(dimensionClassInfo, "维度分类不存在");
                List<String> dimensionAttrNames =
                        dimensionClassInfo.getAttrDefs().stream().filter(def -> def.getId().equals(dimensionDefId)).map(ESCIAttrDefInfo::getProName).collect(Collectors.toList());
                Assert.isTrue(!BinaryUtils.isEmpty(dimensionAttrNames), "维度字段不存在");
                ESCIClassInfo statisticClassInfo = iciClassSvc.queryESClassInfoById(statisticClassId);
                Assert.notNull(statisticClassInfo, "度量分类不存在");
                String statisticAttrName = null;
                for (ESCIAttrDefInfo def : statisticClassInfo.getAttrDefs()) {
                    if (def.getId().equals(statisticDefId)) {
                        statisticAttrName = def.getProName();
                        Assert.isTrue(statisticalType != 1 || def.getProType() <= 2, "属性[" + def.getProName() + "]类型不支持该度量方式");
                    }
                }
                Assert.notNull(statisticAttrName, "度量字段不存在");

                DataSetMallApiRelationRule sourceDataSet = new DataSetMallApiRelationRule(sourceJsonObject);
                String seriesPageNodeId = "";
                String valuePageNodeId = "";
                for (RelationRuleNode node : sourceDataSet.getNodes()) {
                    List<ESCiClassAttrDef> defs = JSONArray.parseArray(node.getNodeReturns(), ESCiClassAttrDef.class);
                    for (ESCiClassAttrDef def : defs) {
                        if (dimensionDefId.equals(def.getId())) {
                            seriesPageNodeId = node.getPageNodeId().toString();
                        }
                        if (statisticDefId.equals(def.getId())) {
                            valuePageNodeId = node.getPageNodeId().toString();
                        }
                    }
                }

                Chart chart = new Chart();
                chart.setChartType(chartType == 3 ? ChartEnum.HISTOGRAM.getValue() : chartType);
                chart.setStatisticalType(statisticalType);
                chart.setSeriesAttrName(dimensionAttrNames.get(0));
                chart.setSeriesPageNodeId(seriesPageNodeId);
                chart.setValueAttrName(statisticAttrName);
                chart.setValuePageNodeId(valuePageNodeId);
                JSONObject countStatistics = this.countStatistics(dataSourceId, chart);
                retJson.put("xData", countStatistics.get("xData"));
                retJson.put("yData", "");
                retJson.put("data", countStatistics.get("data"));
            } else if (dataSourceType == 2) {
                // 配置数据
                ESCIClassSearchBean clsBean = new ESCIClassSearchBean();
                CCcCiClass cdt = new CCcCiClass();
                cdt.setDomainId(domainId);
                cdt.setIds(dimensionClassIds.toArray(new Long[]{}));
                clsBean.setCdt(cdt);
                List<ESCIClassInfo> classInfos = iciClassSvc.queryESCiClassInfoListBySearchBean(clsBean);
                ESCISearchBean bean = new ESCISearchBean();
                bean.setDomainId(domainId);
                bean.setClassIds(dimensionClassIds);
                Map<Long, Long> ciGroupByClsMap = iciSvc.countCiNumGroupClsByQuery(bean);
                List<String> xData = new ArrayList<String>();
                List<Long> chartsData = new ArrayList<>();
                for (ESCIClassInfo esciClassInfo : classInfos) {
                    xData.add(esciClassInfo.getClassName());
                    Long count = ciGroupByClsMap.get(esciClassInfo.getId());
                    chartsData.add(count == null ? 0 : count);
                }
                retJson.put("xData", xData);
                retJson.put("yData", "");
                retJson.put("data", chartsData);

                // retJson.put("dimension", xData);
                // List<Object> data = new ArrayList<>();
                // JSONObject jsonData = new JSONObject();
                // jsonData.put("name", "");
                // jsonData.put("data", chartsData);
                // data.add(jsonData);
                // retJson.put("data", data);
            }

            return retJson;
        } catch (MessageException e) {
            log.error("", e);
            isSuccess = false;
            throw e;
        } finally {
            if (dataSetMallApi.getId() != null) {
                // 保存日志
                DataSetMallApiLog dataSetMallApiLog = new DataSetMallApiLog();
                dataSetMallApiLog.setId(ESUtil.getUUID());
                dataSetMallApiLog.setDomainId(domainId);
                dataSetMallApiLog.setDataSetMallApiId(dataSetMallApi.getId());
                dataSetMallApiLog.setDataSetType(dataSetMallApi.getType());
                dataSetMallApiLog.setSuccess(isSuccess);
                dataSetMallApiLog.setRespUserCode(username);
                dataSetMallApiLog.setRespDuration(System.currentTimeMillis() - curTime);
                dataSetMallApiLog.setCreateTime(System.currentTimeMillis());
                esDataSetMallApiLogSvc.saveOrUpdate(dataSetMallApiLog.toJson(), true);
            }
        }
    }

    @Override
    public JSONObject realTimeExecute(DataSetMallApi dataSetMallApi, JSONObject jsonObject) {
        return execute(dataSetMallApi, jsonObject);
    }

    private void validStatistic(DataSetMallApiStatistic dataSetStatistic) {
        Integer dataSourceType = dataSetStatistic.getDataSourceType();
        // 配置数据集
        if (dataSourceType.intValue() == 1) {
            Assert.notNull(esDataSvc.getById(dataSetStatistic.getDataSourceId()), "UINO_BS_OBJ_RELATION_RULE_DATASET#{field:BS_MVTYPE_NOT_EXIST}");
            Long dimensionClassId = dataSetStatistic.getDimensionClassIds().get(0);
            ESCIClassInfo dimensionClassInfo = iciClassSvc.queryESClassInfoById(dimensionClassId);
            Assert.notNull(dimensionClassInfo, "维度分类不存在");
            List<String> dimensionAttrNames =
                    dimensionClassInfo.getAttrDefs().stream().filter(def -> def.getId().equals(dataSetStatistic.getDimensionDefId())).map(ESCIAttrDefInfo::getProName).collect(Collectors.toList());
            Assert.isTrue(!BinaryUtils.isEmpty(dimensionAttrNames), "维度字段不存在");
            ESCIClassInfo statisticClassInfo = iciClassSvc.queryESClassInfoById(dataSetStatistic.getStatisticClassId());
            Assert.notNull(statisticClassInfo, "度量分类不存在");
            String statisticAttrName = null;
            for (ESCIAttrDefInfo def : statisticClassInfo.getAttrDefs()) {
                if (def.getId().equals(dataSetStatistic.getStatisticDefId())) {
                    statisticAttrName = def.getProName();
                    Assert.isTrue(dataSetStatistic.getStatisticalType().intValue() != 1 || def.getProType() <= 2, "属性[" + def.getProName() + "]类型不支持该度量方式");
                }
            }
            Assert.notNull(statisticAttrName, "度量字段不存在");
        } else if (dataSourceType.intValue() == 2) {
            // 配置数据
            dataSetStatistic.setDataSourceId(null);
            dataSetStatistic.setDimensionDefId(null);
            dataSetStatistic.setStatisticClassId(null);
            dataSetStatistic.setStatisticDefId(null);
        }
    }

    public JSONObject countStatistics(Long dataSetId, Chart chart) {
        long startTime = System.currentTimeMillis();
        boolean isSuccess = true;
        AtomicInteger ciSize = new AtomicInteger();
        AtomicInteger rltSize = new AtomicInteger();
        try {
            JSONObject dataSet = esDataSvc.getById(dataSetId);
            Assert.notNull(dataSet, "UINO_BS_OBJ_RELATION_RULE_DATASET#{field:BS_MVTYPE_NOT_EXIST}");
            DataSetMallApiRelationRule dataSetMallRelationApi = new DataSetMallApiRelationRule(dataSet);
            if (0 == dataSvc.isTaskRunning(dataSetId)) {
                Map<Long, SimpleFriendInfo> simpleFriendInfoMap = new HashMap<>();
                BoolQueryBuilder query = QueryBuilders.boolQuery();
                query.must(QueryBuilders.termQuery("dataSetId", dataSetId));
                int pageNum = 1;
                while (true) {
                    Page<DataSetExeResult> page = esDataSetExeResultSvc.getListByQuery(pageNum, 1000, query);
                    for (DataSetExeResult result : page.getData()) {
                        SimpleFriendInfo simpleFriendInfo = result.getSimpleFriendInfo();
                        if (simpleFriendInfo.getCiIds() != null) {
                            ciSize.addAndGet(simpleFriendInfo.getCiIds().size());
                        }
                        if (simpleFriendInfo.getCiRltLineIds() != null) {
                            rltSize.addAndGet(simpleFriendInfo.getCiRltLineIds().size());
                        }
                        simpleFriendInfoMap.put(result.getStartCi().getCi().getId(), simpleFriendInfo);
                    }
                    if (pageNum >= page.getTotalPages()) {
                        break;
                    } else {
                        pageNum++;
                    }
                }
                JSONObject result = relationRuleAnalysisSvc.countStatistics(dataSetMallRelationApi, simpleFriendInfoMap, chart);
                return result;
            } else {
                throw new RuntimeException("数据正在构建中，请稍候");
            }
        } catch (Exception e) {
            isSuccess = false;
            throw e;
        } finally {
            DataSetMallApiLog dataSetMallApiLog = new DataSetMallApiLog();
            dataSetMallApiLog.setId(ESUtil.getUUID());
            dataSetMallApiLog.setDataSetMallApiId(dataSetId);
            dataSetMallApiLog.setSuccess(isSuccess);
            dataSetMallApiLog.setRespUserCode("system");
            dataSetMallApiLog.setRespDuration(System.currentTimeMillis() - startTime);
            dataSetMallApiLog.setCreateTime(System.currentTimeMillis());
            dataSetMallApiLog.setCiTotal(ciSize.get());
            dataSetMallApiLog.setRelTotal(rltSize.get());
            esDataSetMallApiLogSvc.saveOrUpdate(dataSetMallApiLog);
        }
    }

}
