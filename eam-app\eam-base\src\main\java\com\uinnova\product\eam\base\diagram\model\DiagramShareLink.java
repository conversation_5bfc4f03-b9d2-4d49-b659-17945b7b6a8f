package com.uinnova.product.eam.base.diagram.model;

import com.binary.framework.bean.annotation.Comment;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import org.springframework.stereotype.Component;

/**
 * @Description 视图分享类
 * <AUTHOR>
 * @Date 2021-09-06-16:03
 * @version 1.0
 */
@Component
@Data
public class DiagramShareLink {

    @Comment("主键")
    private Long id;

    @Comment("所属视图id")
    private String diagramId;

    @Comment("图片名称，即sheet名称")
    private String imageName;

    @Comment("资源路径")
    @JsonIgnore
    private String storePath;

    @Comment("生成的链接")
    private String link;

    @Comment("生成的链接密码")
    private String linkPass;

    @Comment("分享链接的key")
    private String linkKey;

    @Comment("分享图片对应的sheetId")
    private String sheetId;

    @Comment("分享链接的权限")
    private Integer permission;

    @Comment("分享链接的类型， 0-分享图片, 1-分享链接, 2-协作链接")
    private Integer linkType;

    @Comment("生成的链接有效期--废弃")
    private Long linkExpiration;

    @Comment("生成的链接有效期")
    private String linkExpireTime;

    @Comment("分享图片链接的base64字符串")
    private String content;

    @Comment("创建时间[CREATE_TIME]    创建时间:yyyyMMddHHmmss")
    private Long createTime;

    @Comment("更新时间[MODIFY_TIME]    更新时间:yyyyMMddHHmmss")
    private Long modifyTime;

    @Comment("创建人[CREATOR]")
    private String creator;

    @Comment("修改人[MODIFIER]")
    private String modifier;

}
