package com.uinnova.product.vmdb.comm.model.classTpl;

import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;

/**
 * 
 * <AUTHOR>
 *
 */
@Comment("CI分类模版表[CC_CI_CLASS_TPL]")
public class CcCiClassTpl implements EntityBean {
    private static final long serialVersionUID = 1L;

    @Comment("ID[ID]")
    private Long id;

    @Comment("模版ID[TPL_ID]")
    private Long tplId;

    @Comment("分类代码[CLASS_CODE]")
    private String classCode;

    @Comment("分类名称[CLASS_NAME]")
    private String className;

    @Comment("分类代码标准值[CLASS_STD_CODE]")
    private String classStdCode;

    @Comment("所属目录[DIR_ID]")
    private Long dirId;

    @Comment("分类类型[CI_TYPE]    分类类型:1=基础CI 2=关系CI")
    private Integer ciType;

    @Comment("分类类别[CLASS_KIND]    分类类别:1=显示 2=不显示")
    private Integer classKind;

    @Comment("上级分类ID[PARENT_ID]")
    private Long parentId;

    @Comment("分类层级级别[CLASS_LVL]")
    private Integer classLvl;

    @Comment("分类层级路径[CLASS_PATH]    分类层级路径:例：#1#2#7#")
    private String classPath;

    @Comment("显示排序[ORDER_NO]")
    private Integer orderNo;

    @Comment("是否末级[IS_LEAF]    是否末级:1=是 0=否")
    private Integer isLeaf;

    @Comment("分类图标[ICON]")
    private String icon;

    @Comment("分类描述[CLASS_DESC]")
    private String classDesc;

    @Comment("分类颜色[CLASS_COLOR]")
    private String classColor;

    @Comment("显示字段ID[DISP_FIELD_IDS]    显示字段ID:多个以逗号分隔")
    private String dispFieldIds;

    @Comment("显示字段名称[DISP_FIELD_NAMES]    显示字段名称:多个以逗号分隔")
    private String dispFieldNames;

    @Comment("是否归集[COST_TYPE]    是否归集:1=不归集 2=归集(源->目标) 3=归集(目标->源)")
    private Integer costType;

    @Comment("关系样式[LINE_TYPE]")
    private String lineType;

    @Comment("关系宽度[LINE_BORDER]")
    private Integer lineBorder;

    @Comment("关系颜色[LINE_COLOR]")
    private String lineColor;

    @Comment("关系箭头[LINE_DIRECT]")
    private String lineDirect;

    @Comment("关系动态效果[LINE_ANIME]    关系动态效果:1=是 0=否")
    private Integer lineAnime;

    @Comment("所属域[DOMAIN_ID]")
    private Long domainId;

    @Comment("数据状态[DATA_STATUS]    数据状态:0=删除，1=正常")
    private Integer dataStatus;

    @Comment("创建人[CREATOR]")
    private String creator;

    @Comment("修改人[MODIFIER]")
    private String modifier;

    @Comment("创建时间[CREATE_TIME]    创建时间:yyyyMMddHHmmss")
    private Long createTime;

    @Comment("更新时间[MODIFY_TIME]    更新时间:yyyyMMddHHmmss")
    private Long modifyTime;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTplId() {
        return this.tplId;
    }

    public void setTplId(Long tplId) {
        this.tplId = tplId;
    }

    public String getClassCode() {
        return this.classCode;
    }

    public void setClassCode(String classCode) {
        this.classCode = classCode;
    }

    public String getClassName() {
        return this.className;
    }

    public void setClassName(String className) {
        this.className = className;
    }

    public String getClassStdCode() {
        return this.classStdCode;
    }

    public void setClassStdCode(String classStdCode) {
        this.classStdCode = classStdCode;
    }

    public Long getDirId() {
        return this.dirId;
    }

    public void setDirId(Long dirId) {
        this.dirId = dirId;
    }

    public Integer getCiType() {
        return this.ciType;
    }

    public void setCiType(Integer ciType) {
        this.ciType = ciType;
    }

    public Integer getClassKind() {
        return this.classKind;
    }

    public void setClassKind(Integer classKind) {
        this.classKind = classKind;
    }

    public Long getParentId() {
        return this.parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public Integer getClassLvl() {
        return this.classLvl;
    }

    public void setClassLvl(Integer classLvl) {
        this.classLvl = classLvl;
    }

    public String getClassPath() {
        return this.classPath;
    }

    public void setClassPath(String classPath) {
        this.classPath = classPath;
    }

    public Integer getOrderNo() {
        return this.orderNo;
    }

    public void setOrderNo(Integer orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getIsLeaf() {
        return this.isLeaf;
    }

    public void setIsLeaf(Integer isLeaf) {
        this.isLeaf = isLeaf;
    }

    public String getIcon() {
        return this.icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getClassDesc() {
        return this.classDesc;
    }

    public void setClassDesc(String classDesc) {
        this.classDesc = classDesc;
    }

    public String getClassColor() {
        return this.classColor;
    }

    public void setClassColor(String classColor) {
        this.classColor = classColor;
    }

    public String getDispFieldIds() {
        return this.dispFieldIds;
    }

    public void setDispFieldIds(String dispFieldIds) {
        this.dispFieldIds = dispFieldIds;
    }

    public String getDispFieldNames() {
        return this.dispFieldNames;
    }

    public void setDispFieldNames(String dispFieldNames) {
        this.dispFieldNames = dispFieldNames;
    }

    public Integer getCostType() {
        return this.costType;
    }

    public void setCostType(Integer costType) {
        this.costType = costType;
    }

    public String getLineType() {
        return this.lineType;
    }

    public void setLineType(String lineType) {
        this.lineType = lineType;
    }

    public Integer getLineBorder() {
        return this.lineBorder;
    }

    public void setLineBorder(Integer lineBorder) {
        this.lineBorder = lineBorder;
    }

    public String getLineColor() {
        return this.lineColor;
    }

    public void setLineColor(String lineColor) {
        this.lineColor = lineColor;
    }

    public String getLineDirect() {
        return this.lineDirect;
    }

    public void setLineDirect(String lineDirect) {
        this.lineDirect = lineDirect;
    }

    public Integer getLineAnime() {
        return this.lineAnime;
    }

    public void setLineAnime(Integer lineAnime) {
        this.lineAnime = lineAnime;
    }

    public Long getDomainId() {
        return this.domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }

    public Integer getDataStatus() {
        return this.dataStatus;
    }

    public void setDataStatus(Integer dataStatus) {
        this.dataStatus = dataStatus;
    }

    public String getCreator() {
        return this.creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getModifier() {
        return this.modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public Long getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getModifyTime() {
        return this.modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }

}
