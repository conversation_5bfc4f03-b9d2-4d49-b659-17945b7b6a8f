package com.uino.dao.cmdb.microservice.impl;

import static org.junit.Assert.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import java.util.ArrayList;
import java.util.List;

import com.uino.service.cmdb.microservice.impl.VisualModelSvc;
import org.elasticsearch.index.query.QueryBuilder;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.util.ReflectionTestUtils;

import com.binary.core.exception.MessageException;
import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClass;
import com.uino.dao.cmdb.ESCIClassSvc;
import com.uino.dao.cmdb.ESCiClassRltSvc;
import com.uino.dao.cmdb.ESImpactPathSvc;
import com.uino.dao.cmdb.ESRltClassSvc;
import com.uino.dao.cmdb.ESVisualModelSvc;
import com.uino.bean.cmdb.base.ESCIClassInfo;
import com.uino.bean.cmdb.base.ESVisualModel;
import com.uino.bean.cmdb.query.ESVisualModelSearchBean;

import net.minidev.json.JSONArray;

/**
 * 
 * <AUTHOR>
 *
 */
@RunWith(SpringJUnit4ClassRunner.class)
public class VisualModelSvcTest {

	@InjectMocks
	private VisualModelSvc svc;
	
	private ESVisualModelSvc visualModelSvc;
	
	private ESCiClassRltSvc ciClassRltSvc;
	
	private ESImpactPathSvc impactPathSvc;
	
	private ESCIClassSvc ciClassSvc;
	
	private ESRltClassSvc rltClassSvc;
	
	@Before
	public void setUp() {
		MockitoAnnotations.initMocks(this);
		
		visualModelSvc = mock(ESVisualModelSvc.class);
		ReflectionTestUtils.setField(svc, "visualModelSvc", visualModelSvc);
		ciClassRltSvc = mock(ESCiClassRltSvc.class);
		ReflectionTestUtils.setField(svc, "ciClassRltSvc", ciClassRltSvc);
		impactPathSvc = mock(ESImpactPathSvc.class);
		ReflectionTestUtils.setField(svc, "impactPathSvc", impactPathSvc);
		ciClassSvc = mock(ESCIClassSvc.class);
		ReflectionTestUtils.setField(svc, "ciClassSvc", ciClassSvc);
		rltClassSvc = mock(ESRltClassSvc.class);
		ReflectionTestUtils.setField(svc, "rltClassSvc", rltClassSvc);
	}
	
	@Test
	public void queryVisualModels() {
		List<ESCIClassInfo> ciClassList = new ArrayList<ESCIClassInfo>();
		//A
		ESCIClassInfo aClass = new ESCIClassInfo();
		aClass.setId(1L);
		aClass.setClassName("A");
		aClass.setIcon("a.png");
		ciClassList.add(aClass);
		when(ciClassSvc.getListByCdt(any(CCcCiClass.class))).thenReturn(ciClassList);
		
		List<ESCIClassInfo> rltClassMap = new ArrayList<ESCIClassInfo>();
		//A-B
		ESCIClassInfo abClass = new ESCIClassInfo();
		abClass.setId(1L);
		abClass.setClassName("A-B");
		rltClassMap.add(abClass);
		when(rltClassSvc.getListByCdt(any(CCcCiClass.class))).thenReturn(rltClassMap);
		
		List<ESVisualModel> models = new ArrayList<ESVisualModel>();
		ESVisualModel model = new ESVisualModel();
		model.setId(1L);
		model.setName("model1");
		model.setJson("{\"nodeDataArray\":[{\"classId\":1},{\"classId\":2}], \"linkDataArray\":[{\"classId\":1},{\"classId\":2}]}");
		List<ESVisualModel.ImpactPath> impactPaths = new ArrayList<ESVisualModel.ImpactPath>();
		ESVisualModel.ImpactPath impactPath1 = new ESVisualModel.ImpactPath();
		impactPath1.setFocusClassId(1L);
		impactPath1.setSourceClassId(1L);
		impactPath1.setTargetClassId(1L);
		impactPath1.setRltClassId(1L);
		impactPaths.add(impactPath1);
		ESVisualModel.ImpactPath impactPath2 = new ESVisualModel.ImpactPath();
		impactPath2.setFocusClassId(1L);
		impactPath2.setSourceClassId(1L);
		impactPath2.setTargetClassId(2L);
		impactPath2.setRltClassId(3L);
		impactPaths.add(impactPath2);
		model.setImpactPaths(impactPaths);
		model.setEnable(true);
		model.setDomainId(1L);
		models.add(model);
		when(visualModelSvc.getSortListByCdt(any(ESVisualModelSearchBean.class), any(List.class))).thenReturn(models);
		
		checkQueryVisualModels(1L);
	}
	
	private void checkQueryVisualModels(Long domainId) {
		List<ESVisualModel> ret = null;
		try {
			ret = svc.queryVisualModels(domainId);
		} catch (Exception e) {
//			e.printStackTrace();
		}
		String expect = "[{\"thumbnail\":null,\"modifyTime\":null,\"createTime\":null,\"enable\":true,\"name\":\"model1\",\"json\":\"{\\\"nodeDataArray\\\":[{\\\"image\\\":\\\"nulla.png\\\",\\\"classId\\\":1,\\\"label\\\":\\\"A\\\"},{}],\\\"linkDataArray\\\":[{\\\"classId\\\":1,\\\"label\\\":\\\"A-B\\\"},{}]}\",\"id\":1,\"impactPaths\":[{\"focusClassId\":1,\"targetClassId\":1,\"sourceClassId\":1,\"rltClassId\":1}],\"domainId\":1}]";
		Assert.assertEquals("", expect, JSONArray.toJSONString(ret));
	}
	
	@Test
	public void saveVisualModel() {
		when(visualModelSvc.saveOrUpdate(any(ESVisualModel.class))).thenReturn(1L);
		
		//缺少domainId
		ESVisualModel model = new ESVisualModel();
		checkSaveVisualModelException(model);
		//缺少模型名称
		model.setDomainId(1L);
		checkSaveVisualModelException(model);
		//缺少模型内容
		model.setName("model1");
		checkSaveVisualModelException(model);
		//模型内容不是JSON
		model.setJson("{");
		checkSaveVisualModelException(model);
		//影响模型参数不合法
		model.setJson("{\"nodeDataArray\":[{\"key\":1,\"classId\":1},{\"key\":2,\"classId\":2}], \"linkDataArray\":[{\"from\":1,\"to\":2,\"classId\":1}]}");
		List<ESVisualModel.ImpactPath> impactPaths = new ArrayList<ESVisualModel.ImpactPath>();
		ESVisualModel.ImpactPath impactPath = new ESVisualModel.ImpactPath();
		impactPath.setFocusClassId(null);
		impactPath.setSourceClassId(1L);
		impactPath.setTargetClassId(1L);
		impactPath.setRltClassId(1L);
		impactPaths.add(impactPath);
		model.setImpactPaths(impactPaths);
		checkSaveVisualModelException(model);
		impactPath.setFocusClassId(1L);
		impactPath.setSourceClassId(null);
		model.setImpactPaths(impactPaths);
		checkSaveVisualModelException(model);
		impactPath.setSourceClassId(1L);
		impactPath.setTargetClassId(null);
		model.setImpactPaths(impactPaths);
		checkSaveVisualModelException(model);
		impactPath.setTargetClassId(1L);
		impactPath.setRltClassId(null);
		model.setImpactPaths(impactPaths);
		checkSaveVisualModelException(model);
		//正常
		impactPath.setRltClassId(1L);
		model.setImpactPaths(impactPaths);
		checkSaveVisualModel(model);
	}
	
	private void checkSaveVisualModelException(ESVisualModel model) {
		try {
			svc.saveVisualModel(model);
			fail("");
		} catch (Exception e) {
//			e.printStackTrace();
		}
	}
	
	private void checkSaveVisualModel(ESVisualModel model) {
		Long ret = null;
		try {
			ret = svc.saveVisualModel(model);
		} catch (Exception e) {
//			e.printStackTrace();
		}
		String expect = "1";
		Assert.assertEquals("", expect, ret+"");
	}
	
	@Test
	public void updateVisualModelName() {
		when(visualModelSvc.getById(eq(1L))).thenReturn(null);
		when(visualModelSvc.getById(eq(2L))).thenReturn(new ESVisualModel());
		when(visualModelSvc.updateByQuery(any(QueryBuilder.class), any(String.class), any(Boolean.class))).thenReturn(true);
		
		//缺少模型ID
		ESVisualModel model = new ESVisualModel();
		checkUpdateVisualModelNameException(model);
		//缺少模型名称
		model.setId(1L);
		checkUpdateVisualModelNameException(model);
		model.setName("");
		checkUpdateVisualModelNameException(model);
		//模型不存在
		model.setName("model1");
		checkUpdateVisualModelNameException(model);
		//正常
		model.setId(2L);
		checkUpdateVisualModelName(model);
	}
	
	private void checkUpdateVisualModelNameException(ESVisualModel model) {
		try {
			svc.updateVisualModelName(model);
			fail("");
		} catch (Exception e) {
			throw new MessageException(e.getMessage());
		}
	}
	
	private void checkUpdateVisualModelName(ESVisualModel model) {
		try {
			svc.updateVisualModelName(model);
		} catch (Exception e) {
			throw new MessageException(e.getMessage());
		}
	}
	
	@Test
	public void deleteVisualModel() {
		ESVisualModel modelTrue = new ESVisualModel();
		modelTrue.setEnable(true);
		modelTrue.setDomainId(1L);
		when(visualModelSvc.getById(eq(1L))).thenReturn(modelTrue);
		ESVisualModel modelFalse = new ESVisualModel();
		modelFalse.setEnable(false);
		modelFalse.setDomainId(1L);
		when(visualModelSvc.getById(eq(2L))).thenReturn(modelFalse);
		when(visualModelSvc.getById(eq(3L))).thenReturn(null);
		
		//单个模型
		Page<ESVisualModel> page1 = new Page<ESVisualModel>();
		page1.setTotalRows(1);
		//多个模型
		Page<ESVisualModel> page2 = new Page<ESVisualModel>();
		page2.setTotalRows(2);
		when(visualModelSvc.getListByQuery(eq(1), eq(1), any())).thenReturn(page2, page2, page1);
		
		//缺少模型ID
		checkDeleteVisualModelException(null);
		//模型不存在
		checkDeleteVisualModelException(3L);
		//多个模型,要删true的
		checkDeleteVisualModelException(1L);
		//多个模型,要删false的
		checkDeleteVisualModel(2L);
		//单个模型
		checkDeleteVisualModel(1L);
	}
	
	private void checkDeleteVisualModelException(Long id) {
		try {
			svc.deleteVisualModel(id);
			fail("");
		} catch (Exception e) {
			throw new MessageException(e.getMessage());
		}
	}
	
	private void checkDeleteVisualModel(Long id) {
		try {
			svc.deleteVisualModel(id);
		} catch (Exception e) {
			throw new MessageException(e.getMessage());
		}
	}
}
