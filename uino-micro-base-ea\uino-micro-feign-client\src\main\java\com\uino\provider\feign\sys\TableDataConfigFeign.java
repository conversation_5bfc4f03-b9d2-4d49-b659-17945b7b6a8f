package com.uino.provider.feign.sys;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.uino.bean.cmdb.base.ESTableDataConfigInfo;
import com.uino.provider.feign.config.BaseFeignConfig;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 
 * <AUTHOR>
 *
 */
@Component
@FeignClient(name = "${feign-server-name:tarsier-platform}", path = "${feign-server-path:tarsier-platform}/feign/sys/table", configuration = {BaseFeignConfig.class})
public interface TableDataConfigFeign {

    /**
     * 查询CI数据配置
     * 
     * @param uid
     * @return
     */
    @PostMapping("getCIDataConfigInfo")
    ESTableDataConfigInfo getCIDataConfigInfo(@RequestParam(value="domainId") Long domainId, @RequestBody String uid);

    /**
     * 保存CI数据配置
     * 
     * @param configInfo
     * @return
     */
    @PostMapping("saveCIDataConfigInfo")
    Long saveCIDataConfigInfo(@RequestBody ESTableDataConfigInfo configInfo);
}
