package com.uinnova.product.eam.service.cj.dao;

import com.uinnova.product.eam.model.cj.domain.PlanSystemRelation;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;

/**方
 * 案与系统关联dao
 * <AUTHOR>
 */
@Component
public class PlanSystemRelationDao extends AbstractESBaseDao<PlanSystemRelation, PlanSystemRelation> {

    @Override
    public String getIndex() {
        return "uino_cj_plan_system_relation";
    }

    @Override
    public String getType() {
        return getIndex();
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }

}
