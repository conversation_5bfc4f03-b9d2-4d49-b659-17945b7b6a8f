package com.uino.bean.permission.base;

import java.util.List;
import java.util.Set;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 资源保护配置
 * 
 * <AUTHOR>
 *
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value="资源保护配置",description = "资源保护配置")
public class OauthResourceDetail {
	/** id */
	@ApiModelProperty(value="id",example = "123")
	private Long id;
	/**
	 * 资源端名称
	 */
	@ApiModelProperty(value="资源端名称")
	private String name;

	/**
	 * 不受保护的urls
	 */
	@ApiModelProperty(value="不受保护的urls")
	private List<String> permitAllUrls;

	/**
	 * 受保护url安全配置组
	 * <p>
	 * 注意:
	 * <p>
	 * 1、顺序很重要，与springmvc中messageConverter类似，多个都可匹配上先匹配上的策略生效
	 */
	@ApiModelProperty(value="受保护url安全配置组")
	private List<UrlSecureConfig> urlSecureConfigs;

	/** 所属域 */
	@ApiModelProperty(value="所属域",example = "123")
	private Long domainId;

	/** 创建人 */
	@ApiModelProperty(value="创建人",example = "mike")
	private String creator;

	/** 修改人 */
	@ApiModelProperty(value = "修改人",example = "mike")
	private String modifier;

	/** 创建时间 */
	@ApiModelProperty(value="创建时间")
	private Long createTime;

	/** 修改时间 */
	@ApiModelProperty(value="修改时间")
	private Long modifyTime;

	/**
	 * url安全配置
	 * 
	 * <AUTHOR>
	 *
	 */
	@Data
	@Builder
	@NoArgsConstructor
	@AllArgsConstructor
	@ApiModel(value="受保护url安全配置",description = "受保护url安全配置")
	public static class UrlSecureConfig {
		/**
		 * 该组配置的url
		 */
		@ApiModelProperty(value="该组配置的url")
		private List<String> urls;
		/**
		 * 需要的授权范围
		 */
		@ApiModelProperty(value="需要的授权范围")
		private String scope;
		/**
		 * 可访问该组资源的角色
		 */
		@ApiModelProperty(value="可访问该组资源的角色")
		private Set<String> roles;
		/**
		 * 可访问该组资源的模块权限code
		 */
		@ApiModelProperty(value="可访问该组资源的模块权限code")
		private Set<String> moduleCodes;

		/**
		 * 获取该保护信息对应的springSecure需要的认证字符串
		 * 
		 * @return
		 */
		public String buildAccess() {
			String access = "";
			if (this.scope != null && !"".equals(this.scope.trim())) {
				access = "#oauth2.hasScope('" + this.scope.trim() + "')";
			}
			if (this.roles != null && this.roles.size() > 0) {
				access = "".equals(access) ? access : access + " and";
				for (String role : this.roles) {
					access += " hasRole('ROLE_" + role + "') and";
				}
				access = access.substring(0, access.lastIndexOf("and"));
			}
			if (this.moduleCodes != null && this.moduleCodes.size() > 0) {
				access = "".equals(access) ? access : access + " and";
				for (String moduleCode : this.moduleCodes) {
					access += " hasAuthority('MODULECODE_" + moduleCode + "') and";
				}
				access = access.substring(0, access.lastIndexOf("and"));
			}
			return access.trim();
		}

	}

}
