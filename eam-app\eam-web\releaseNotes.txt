日期: 2022-1-18
版本: V1.0
提交人: 管玉号
影响功能:【业务建模配置变更】
更新内容:
修改"对象管理-->业务建模-->业务建模全景图"分类定义，删除TABLE_NAME及UINO_UUID属性
============================================================================
日期: 2022-1-18
版本: V1.0
提交人: uino
影响功能:【制品类型管理配置变更】
更新内容:
修改"对象管理-->业务建模-->视图配置"分类定义，增加DEFAULT_IMG属性
============================================================================
日期: 2022-3-9
版本: V1.0
提交人: 周鼎海
影响功能:【视图查询类CVcDiagram】
更新内容: 在CVcDiagram类下增加diagramSubType字段，进行区分视图类型
============================================================================
日期: 2022-3-9
版本: V1.0
提交人: 周鼎海
影响功能:【业务流程建模，业务组件】
更新内容:
修改"对象管理-->业务建模-->视图配置"分类定义，增加BUS_TASK_TAG属性,控制任务显示关联活动按钮 ，
    增加BUS_COMPONENT_CONFIG属性，控制业务组件使用对象分类，制品类型等
============================================================================
日期: 2022-3-9
版本: V1.0
提交人: wcl
影响功能:【二级菜单更换图标】
更新内容:
修改"系统设置-->菜单配置，增加图片列，需要初始化默认图
#二级菜单默认图相对路径
local.resource.pic.url =/122/defaultIcon/default_menu_pic.png
============================================================================
日期: 2022-3-14
版本: V1.0
提交人: 张强
影响功能:【数据字典】
更新内容: 服务器执行命令 删除原索引 之后重启项目生成新索引 命令如下：
curl 'http://***************:9200/uino_sys_dictionary_item' \
  -X 'DELETE' \
  -H 'Accept: application/json, text/javascript, */*; q=0.01' \
  -H 'Referer: ' \
  -H 'Authorization: Basic dWlubm92YTpVaW5ub3ZhQDEyMw==' \
  -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.82 Safari/537.36' \
  -H 'Content-Type: application/json' \
  --compressed

curl 'http://***************:9200/uino_sys_dictionary_class' \
  -X 'DELETE' \
  -H 'Accept: application/json, text/javascript, */*; q=0.01' \
  -H 'Referer: ' \
  -H 'Authorization: Basic dWlubm92YTpVaW5ub3ZhQDEyMw==' \
  -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.82 Safari/537.36' \
  -H 'Content-Type: application/json' \
  --compressed
============================================================================
日期: 2022-3-14
版本: V1.0
提交人: 张强
影响功能:【架构设计】
更新内容: 架构设计添加初始化根数据 命令如下：
curl 'http://1eam_diagram_dir/uino_eam_diagram_dir/1'   -X 'PUT'   -H 'Accept: application/json, text/javascript, */*; q=0.01'   -H 'Referer: '   -H 'Authorization: Basic dWlubm92YTpVaW5ub3ZhQDEyMw=='   -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.82 Safari/537.36'   -H 'Content-Type: application/json'   --data '{"modifier":"system","dataStatus":1,"dirType":-1,"isLeaf":0,"userId":1,"domainId":1,"parentId":-1,"modifyTime":20220226181440,"dirLvl":1,"createTime":20210901010000,"dirPath":"#-1#","id":1,"dirName":"架构资产"}'   --compressed


日期: 2022-3-15
版本: V1.0
提交人: 杨兵兵
影响功能:【架构设计】
更新内容:
修改数据库 vc_diagram_dir 字段 ES_SYS_ID变成字符串类型保存的资产ciCode,执行语句在sql做了记录


日期: 2022-3-16
版本: V1.0
提交人: 杨兵兵
影响功能:【架构资产】
更新内容:
修改es索引 uino_eam_diagram_dir 字段 esSysId变成字符串类型保存的资产ciCode,数据使用的是临时索引备份，再倒入。

============================================================================
日期: 2022-3-17
版本: V1.0
提交人: 常虎
影响功能:【业务组件建模目录】
更新内容:
修改数据库 vc_diagram_dir 字段 DIR_TYPE字段长度
ALTER TABLE `db_vmdb`.`vc_diagram_dir` MODIFY COLUMN `DIR_TYPE` decimal(16, 0) NULL DEFAULT NULL COMMENT '目录类型[DIR_TYPE]    1=我的，私有库，2=设计库，3=基线库' AFTER `DIR_NAME`;
============================================================================
日期: 2022-3-23
版本: V1.0
提交人: 李崇
影响功能:【伏羲/盘古log4j日志文件优化】
更新内容:
修改启动脚本，脚本Console日志直接丢弃，使用log4j2提供的文件日志进行记录，升级需替换log4j2.xml文件和start_local.sh脚本
============================================================================
日期: 2022-3-24
版本: V1.0
提交人: 范少龙/常虎
影响功能:【EAM/DIAGRAM】
更新内容:
业务建模->视图配置,设计库中增加配置
CONF_TYPE:BM_BELONG_ZJ
CONF_JSON:{"rltClass": "归属于","sourceClass": "DTEA-业务组件","targetClass": "DTEA-业务领域","stepClass": "DTEA-步骤","entityClass": "DTEA-实体","makeHiddenLinkClass": "DTEA-任务","domainAttrKey":"所属领域"}
CONF_NAME:业务组件与领域关系图配置
============================================================================
日期: 2022-4-8
提交人: 张强
影响功能:【资产仓库根目录】
更新内容:
新版本EAM的 业务架构 / 数据架构 / 技术架构资产仓库插入初始化数据 执行下面脚本 插入三条初始化数据
curl 'http://【部署环境地址】:9200/uino_eam_diagram_dir/uino_eam_diagram_dir' \
  -H 'Accept: application/json, text/javascript, */*; q=0.01' \
  -H 'Referer: ' \
  -H 'Authorization: Basic dWlubm92YTpVaW5ub3ZhQDEyMw==' \
  -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.84 Safari/537.36' \
  -H 'Content-Type: application/json' \
  --data '{"modifier":"system","dataStatus":1,"dirType":-2,"isLeaf":0,"userId":1,"domainId":1,"parentId":-1,"modifyTime":20220226181440,"dirLvl":1,"createTime":20210901010000,"dirPath":"#-1#","id":-2,"dirName":"资产仓库"}' \
  --compressed

curl 'http://【部署环境地址】:9200/uino_eam_diagram_dir/uino_eam_diagram_dir' \
  -H 'Accept: application/json, text/javascript, */*; q=0.01' \
  -H 'Referer: ' \
  -H 'Authorization: Basic dWlubm92YTpVaW5ub3ZhQDEyMw==' \
  -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.84 Safari/537.36' \
  -H 'Content-Type: application/json' \
  --data '{"modifier":"system","dataStatus":1,"dirType":-3,"isLeaf":0,"userId":1,"domainId":1,"parentId":-1,"modifyTime":20220226181440,"dirLvl":1,"createTime":20210901010000,"dirPath":"#-1#","id":-3,"dirName":"资产仓库"}' \
  --compressed

curl 'http://【部署环境地址】:9200/uino_eam_diagram_dir/uino_eam_diagram_dir' \
  -H 'Accept: application/json, text/javascript, */*; q=0.01' \
  -H 'Referer: ' \
  -H 'Authorization: Basic dWlubm92YTpVaW5ub3ZhQDEyMw==' \
  -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.84 Safari/537.36' \
  -H 'Content-Type: application/json' \
  --data '{"modifier":"system","dataStatus":1,"dirType":-4,"isLeaf":0,"userId":1,"domainId":1,"parentId":-1,"modifyTime":20220226181440,"dirLvl":1,"createTime":20210901010000,"dirPath":"#-1#","id":-4,"dirName":"资产仓库"}' \
  --compressed
============================================================================
日期: 2022-4-19
提交人: 周鼎海
影响功能:【系统设置，图片显示】
更新内容:
更新系统图片路径，服务器中添加/rsm/system文件夹，保存图片

============================================================================
日期: 2022-4-20
版本: V1.0
提交人: 常虎
影响功能:【业务流程建模顶级视图绑定ci】
更新内容:
系统设置->对象管理->业务建模->业务建模全景图，将该分类ID属性勾选为主键
============================================================================
日期: 2022-4-25
版本: V1.0
提交人: 张强
影响功能:【设计空间根目录查询修改】
更新内容:
执行SQL 初始化五条设计空间根目录数据 执行语句记录在eam-update.sql
============================================================================
日期: 2022-4-27
版本: V1.0
提交人: 常虎
影响功能:【应用全景服务分析(新疆农信)】
更新内容:
业务建模->视图配置,设计库中增加配置
CONF_TYPE:NX_SERVICE_CONFIG
CONF_JSON:{"useRltName":"调用","belRltName":"属于","mapRltName":"映射","serviceCiName":"接口服务","serviceTypeName":"服务类型","serviceType1":"原生服务","serviceType2":"映射服务"}
CONF_NAME:新疆农信定制服务分析配置项

============================================================================
日期: 2022-4-29
提交人: 周鼎海
影响功能:【IT架构操作设置】
更新内容:
1.更改IT架构操作设置架构设置页面为小组;
2.添加子级配置页面： 关联资产分类配置 /eam#/operate-settings ,应用全景基础设置 /app-wall#/setting
3.重新配置权限
curl --location --request POST 'http://**************:9200/uino_sys_module/uino_sys_module/_update_by_query' \
--header 'Authorization: Basic dWlubm92YTpVaW5ub3ZhQDEyMw==' \
--header 'Content-Type: application/json' \
--data-raw '{
  "query": {
    "bool": {
      "must": [
        {
          "match": {
            "moduleName.keyword": "IT架构操作设置"
          }
        }
      ],
      "must_not": [],
      "should": []
    }
  },
   "script":{
    "inline": "ctx._source['\''moduleUrl'\'']='\'''\''"
  }
}'
============================================================================
日期: 2022-4-29
提交人: 周鼎海
影响功能:【业务流程建模】
更新内容:
添加模型树初始化文件，在首次增加多模型服务时，服务器中添加uino_eam_multi_model_hierarchy.json uino_eam_multi_model_type.json文件
同时对流程建模目录历史数据进行刷新，添加modelId

1.层级配置老数据添加modelId字段=1
curl 'http://ip:9200/uino_eam_hierarchy/_doc/_update_by_query' \
  -H 'Accept: application/json, text/javascript, */*; q=0.01' \
  -H 'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8' \
  -H 'Authorization: Basic dWlubm92YTpVaW5ub3ZhQDEyMw==' \
  -H 'Connection: keep-alive' \
  -H 'Content-Type: application/json' \
  -H 'Origin: chrome-extension://pkmihhfgehkjjojkpkdppdahkfeadahe' \
  -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.88 Safari/537.36' \
  --data-raw $'{"script":{"source":"ctx._source[\'modelId\']=1"},"query":{"bool":{"must_not":[{"exists":{"field":"modelId"}}]}}}' \
  --compressed \
  --insecure

2.私有库的catalog表存量数据添加modelId字段

curl 'http://ip:9200/uino_eam_diagram_catalog_private/uino_eam_diagram_catalog_private/_update_by_query' \
  -H 'Accept: application/json, text/javascript, */*; q=0.01' \
  -H 'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8' \
  -H 'Authorization: Basic dWlubm92YTpVaW5ub3ZhQDEyMw==' \
  -H 'Connection: keep-alive' \
  -H 'Content-Type: application/json' \
  -H 'Origin: chrome-extension://pkmihhfgehkjjojkpkdppdahkfeadahe' \
  -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.88 Safari/537.36' \
  --data-raw $'{"script":{"source":"ctx._source[\'modelId\']=1"},"query":{"bool":{"must_not":[{"exists":{"field":"modelId"}}]}}}' \
  --compressed \
  --insecure

3.设计库的catalog表存量数据添加modelId字段

curl 'http://ip:9200/uino_eam_diagram_catalog_design/uino_eam_diagram_catalog_design/_update_by_query' \
  -H 'Accept: application/json, text/javascript, */*; q=0.01' \
  -H 'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8' \
  -H 'Authorization: Basic dWlubm92YTpVaW5ub3ZhQDEyMw==' \
  -H 'Connection: keep-alive' \
  -H 'Content-Type: application/json' \
  -H 'Origin: chrome-extension://pkmihhfgehkjjojkpkdppdahkfeadahe' \
  -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.88 Safari/537.36' \
  --data-raw $'{"script":{"source":"ctx._source[\'modelId\']=1"},"query":{"bool":{"must_not":[{"exists":{"field":"modelId"}}]}}}' \
  --compressed \
  --insecure


============================================================================
日期: 2022-05-09
提交人: 张强
新增功能:【后台获取token,对其他功能无影响】
更新内容: 初始化文件uino_oauth_resource.json中的tarsier-eam模块追加不受保护的url内容 "/eam/oauth/getTokenByLoginInfo"
升级时需要更新es中的 uino_oauth_resource索引数据 脚本如下：

curl 'http://ip:9200/uino_oauth_resource/uino_oauth_resource/【环境数据对应的ID】' \
  -X 'PUT' \
  -H 'Accept: application/json, text/javascript, */*; q=0.01' \
  -H 'Referer: ' \
  -H 'Authorization: Basic dWlubm92YTpVaW5ub3ZhQDEyMw==' \
  -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.127 Safari/537.36' \
  -H 'Content-Type: application/json' \
  --data '{"permitAllUrls":["/cj/system/diagram/changeFlowByDiagramIds","/planDesign/updatePlanDiagramIsFlow","/redirectAuth","/getTokenByCode","/refreshToken","/cmdb/dataSet/execute","/eam/user/getUserByRoleName","/cmdb/dataSet/realTimeExecute","/websocket/*/*","/eam/oauth/getTokenByLoginInfo"],"creator":"system","modifyTime":20220414152224,"createTime":20220414152224,"urlSecureConfigs":[{"urls":["/permission/user/getAuth","/permission/user/getMenuTree","/permission/user/getAuth"]},{"urls":["/**"],"scope":"tarsier"}],"modifier":"system","name":"tarsier-eam","id":39763273700000,"domainId":1}' \
  --compressed

==================================================================================
日期: 2022-05-19
提交人: 李崇
新增功能:【diagram中添加redisson配置】
更新内容: 因开启用户在线检测的拦截器故需要再diagram的配置文件application-local.properties添加如下内容
redis.enable=true
redis.address=redis://${spring.redis.host}:${spring.redis.port}
#支持single、master、sentinel、cluster模式，默认single
redis.type=single
#redis.password=${spring.redis.password}
==================================================================================
日期: 2022-05-24
提交人: 李崇
新增功能:【diagram和eam启动脚本修改】
支持新版license类库需要修改脚本
eam修改为：
nohup java $JAVA_OPTS $JAVA_MEM_OPTS $JAVA_DEBUG_OPTS $JAVA_JMX_OPTS -classpath $CONF_DIR:$LIB_DIR:$LIB_JARS $MAINCLASS $PARAMTER >> /dev/null 2>&1 &
diagram修改为：
nohup java $JAVA_OPTS $JAVA_MEM_OPTS $JAVA_DEBUG_OPTS $JAVA_JMX_OPTS -classpath $CONF_DIR:$LIB_DIR:$LIB_JARS $MAINCLASS $PARAMTER > /dev/null 2>&1 &
==================================================================================
日期: 2022-06-06
提交人: 张强
新增功能:QuickEA菜单数据更新
删除es中的uino_sys_module索引 之后替换eam后台conf/initdata目录下的uino_sys_module.json文件，重启项目
==================================================================================

==================================================================================

日期: 2022-06-09
提交人: 李崇
新增功能:仓颉、伏羲、莫奈支持uino_sso登录，对比application-local文件，缺少的添加，已存在的配置改成如下配置
-------------------------->>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>
#默认分配的角色
uino.user.rolename=admin

#数字空间权限相关配置
wiki.oauth.client.id=d5fqDTxwkchgD4EPxnyKMw1KK9OhRYo94jbOjWHxh1jje7FxuCOWN8Kc6tEhqwdE
wiki.oauth.client.secret=Dw7lGst3QLDHhSPBOTyr0VncTK9xTX9pMfnXfOFOpfjpk9Z5eXBzCe4jF69s2RxA
wiki.oauth.client.grant_type=authorization_code
wiki.oauth.client.user_agent=eam-79 (Internal App)
wiki.oauth.server.url=https://sso.uino.com
wiki.oauth.server.token_callback.url=http://10.100.31.79/tarsier-eam/wiki/getTokenByCode

#数字空间本地权限相关配置
local.oauth.client.id=d5fqDTxwkchgD4EPxnyKMw1KK9OhRYo94jbOjWHxh1jje7FxuCOWN8Kc6tEhqwdE
local.oauth.client.secret=Dw7lGst3QLDHhSPBOTyr0VncTK9xTX9pMfnXfOFOpfjpk9Z5eXBzCe4jF69s2RxA
local.oauth.client.grant_type=authorization_code
local.oauth.client.user_agent=eam-79 (Internal App)
local.oauth.server.url=https://sso.uino.com
local.oauth.server.token_callback.url=http://10.100.31.79/tarsier-eam/wiki/getTokenByCode

#登录方式：sso-扫码、短信、thingjs, thingjs-只支持thingjs登录， oauth-用户密码方式登录
monet.login.loginMethod = oauth

#关闭springsecurity
#spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration, org.springframework.boot.actuate.autoconfigure.security.servlet.ManagementWebSecurityAutoConfiguration
--------------------------<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
更新uino_oauth_resource.json文件，删除es中uino_oauth_resource索引重建数据
===================================================================================
===================================================================================
日期: 2022-06-10
提交人: 杨兵兵
新增功能:应用广场卡片初始化json文件
删除索引uino_eam_square_config，之后替换eam后台conf/initdata目录下的uino_eam_square_config.json文件,重启项目
==================================================================================

===================================================================================
日期: 2022-06-23
提交人: 李崇
新增功能:项目重启时修改oauth认证信息
删除索引uino_oauth_client，之后替换eam和仓颉后台conf/initdata目录下的uino_oauth_client.json文件,重启项目
==================================================================================
日期: 2022-06-27
提交人: 李崇
新增功能:关系管理支持分类名称和分类lable搜索
调用/tarsier-eam/flushData/flushRltSearchData?libType=DESIGN接口将关系数据重建，libType有PRIVATE,DESIGN,BASELINE
==================================================================================

日期: 2022-06-28
提交人: 常虎
更新功能: 更新系统设置中业务建模相关功能的名称
删除索引uino_sys_module，之后替换eam后台conf/initdata目录下的uino_sys_module.json文件,重启项目
==================================================================================
日期: 2022-07-13
提交人: 王春蕾
更新功能: 更新流程模型树管理所属分类
删除索引uino_eam_multi_model_type,之后替换eam后台conf/initdata目录下的uino_eam_multi_model_type.json文件,重启项目
==================================================================================
日期: 2022-07-22
提交人: 李崇
更新功能: 初始化权限由eam项目管理
将仓颉和diagramconf/initdata目录下uino_oauth_resource.json改为空数组，初始化权限由eam统一管理
==================================================================================
日期: 2022-08-03
提交人: 杨兵兵
更新功能: 引导项初始化配置文件添加
将eam下面的initdata目录下面的uino_eam_boot_entry_config.json和uino_eam_boot_user_config.json初始化配置文件放到对应的升级环境
==================================================================================
日期: 2022-08-09
提交人: 杨兵兵
更新功能: base项目初始化文件添加默认角色字段改变\uino-micro-service\src\main\resources\initdata\uino_sys_role.json
        如果已部署的项目需要接口更新admin角色下的字段 接口/eam/bootEntry/updateRoleField
部署的时候更新一下base的maven
==================================================================================
日期: 2022-08-16
提交人: 杨兵兵
更新功能: 仓颉项目application.properties配置文件添加配置，限制上传类型
uino.eam.word.chapter_name_regex=.*[.](doc|docx|xls|xlsx|ppt|pptx|pdf|jpg|jpeg|png)
==================================================================================
日期: 2022-08-17
提交人: 杨兵兵
更新功能: diagram项目 application-local.properties配置文件添加配置项
#本地资源前端静态资源 实际地址为服务器前端文件quickea的路径
local.web.resource.space = /home/<USER>/quickea/
==================================================================================
日期: 2022-08-17
提交人: 常虎
更新功能: eam项目 asset.yml 文件更新
dataModel: 后的参数
==================================================================================
日期: 2022-08-17
提交人: 卢聪
更新功能: diagram项目application.properties配置文件添加配置，限制上传总量大小
total.file.size=524288000
==================================================================================
=======
日期: 2022-08-22
提交人: 李崇
更新功能: eam项目 application-local.properties配置文件添加配置项（选择性添加当参数为true时，需要注册license；为false时不需要注册license，不加默认为开启）
uino.license=false
=======
日期: 2022-08-29
提交人: 李崇
更新功能: eam项目application-local.properties配置文件添加配置项
init.data.action=true
==================================================================================
日期: 2022-09-04
提交人: 卢聪
更新功能: cj项目application.properties配置文件添加配置，添加redis配置(目前的版本配置文件没加的，需要加上)
# Redis服务器地址
spring.redis.host=**************(修改为本机地址)
# Redis服务器连接端口
spring.redis.port=6379
# Redis数据库索引（默认为0）
spring.redis.database=0
# Redis服务器连接密码（默认为空）
#spring.redis.password=Uinnova@123
# 连接超时时间（毫秒）
spring.redis.timeout=10000
# Lettuce
# 连接池最大连接数（使用负值表示没有限制）
spring.redis.lettuce.pool.max-active=8
# 连接池最大阻塞等待时间（使用负值表示没有限制）
spring.redis.lettuce.pool.max-wait=10000
# 连接池中的最大空闲连接
spring.redis.lettuce.pool.max-idle=8
# 连接池中的最小空闲连接
spring.redis.lettuce.pool.min-idle=0
==================================================================================
日期: 2022-09-19
提交人: 王春蕾
更新功能: eam项目 asset.yml 文件更新
dataModel: 系统逻辑实体 和 物理实体，值域，域组等参数

============================================================================
日期: 2022-09-21
提交人: 常虎
更新内容: 流程模型树增加逻辑删除字段,对流程模型树存量数据'dataStatus'字段为空的数据做处理,升级时需要执行如下请求:

curl 'http://ip:9200/uino_eam_multi_model_hierarchy/_doc/_update_by_query' \
  -H 'Accept: application/json, text/javascript, */*; q=0.01' \
  -H 'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8' \
  -H 'Authorization: Basic dWlubm92YTpVaW5ub3ZhQDEyMw==' \
  -H 'Connection: keep-alive' \
  -H 'Content-Type: application/json' \
  -H 'Origin: chrome-extension://pkmihhfgehkjjojkpkdppdahkfeadahe' \
  -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  --data-raw $'{"script":{"source":"ctx._source[\'dataStatus\']=1"},"query":{"bool":{"must_not":[{"exists":{"field":"dataStatus"}}]}}}' \
  --compressed \
  --insecure
============================================================================
日期: 2022-09-26
提交人: 张强
更新内容: 数据建模功能改版， 需要重新修改菜单索引的 地址链接字段

修改设计空间中的数据架构设计： workbench改为bm-workbench 命令如下 （请求路径中的ID为对应升级的环境中的数据）
curl --location --request POST 'http://ip:9200/uino_sys_module/uino_sys_module/8963553354803587' \
--header 'Accept: application/json, text/javascript, */*; q=0.01' \
--header 'Accept-Language: zh-CN,zh;q=0.9' \
--header 'Authorization: Basic dWlubm92YTpVaW5ub3ZhQDEyMw==' \
--header 'Cache-Control: no-cache' \
--header 'Connection: keep-alive' \
--header 'Content-Type: application/json' \
--header 'Origin: chrome-extension://ffmkiejjmecolpfloofpjologoblkegm' \
--header 'Pragma: no-cache' \
--header 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
--data '{"creator":"system","modulePic":"/20220331/79c27983-bb55-4283-8758-0352be2657ad.png","moduleCode":"8963553354803586","orderNo":49,"moduleType":0,"modifier":"system","moduleName":"数据架构设计","moduleSign":"数据架构设计","label":"数据架构设计","moduleImg":"ts ts-envamables","domainId":1,"parentId":8948830619007620,"isDisable":false,"customColor":"#E9C690","modifyTime":20220830101543,"createTime":20220330145444,"id":8963553354803587,"isInit":true,"moduleUrl":"/eam#/bm-workbench/documents/{0}?dirType=101&type=data","status":1}'

修改资产仓库中的数据架构资产： workasset/documents改为bm-model-assets/flowModel 命令如下 （请求路径中的ID为对应升级的环境中的数据）
curl 'http://ip:9200/uino_sys_module/uino_sys_module/8961279551503418' \
  -H 'Accept: application/json, text/javascript, */*; q=0.01' \
  -H 'Accept-Language: zh-CN,zh;q=0.9' \
  -H 'Authorization: Basic dWlubm92YTpVaW5ub3ZhQDEyMw==' \
  -H 'Cache-Control: no-cache' \
  -H 'Connection: keep-alive' \
  -H 'Content-Type: application/json' \
  -H 'Origin: chrome-extension://ffmkiejjmecolpfloofpjologoblkegm' \
  -H 'Pragma: no-cache' \
  -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  --data '{"creator":"system","modulePic":"/20220331/79c27983-bb55-4283-8758-0352be2657ad.png","moduleCode":"8961279551503417","orderNo":57,"moduleType":0,"modifier":"system","moduleName":"数据架构设计资产","moduleSign":"数据架构设计资产","label":"数据架构资产","moduleImg":"ts ts-envamables","domainId":1,"parentId":8948830619007633,"isDisable":false,"customColor":"#FF7718","modifyTime":20220830101543,"createTime":20220331171923,"id":8961279551503418,"isInit":true,"moduleUrl":"/eam#/bm-model-assets/flowModel/{0}?dirType=101&type=data","status":0}' \
  --compressed \
  --insecure
==================================================================================
日期: 2022-10-09
提交人: 张龙
更新功能：
(1).application-local.properties配置文件添加配置项：
#自动成图-部署架构图排序(DMZ-WEB-APP-DB)
auto.deployment.arch.diagram.sort=0-1-2-3
(2).application-minor.properties配置文件追加license免校验接口配置项：
license.ignore.filter.pattern追加/permission/oauth/resource/cleanOnlineUser;/trial/saas/login/check;
(3)./initdata/uino_oauth_resource.json初始化文件追加saas登录拦截免登录接口：
tarsier-eam--permitAllUrls追加"/trial/saas/login/check"
===================================================================================
日期: 2022-10-13
提交人: 张龙
(1).更新功能：调整应用广场卡片顺序号sort（开通新环境）uino_eam_square_config.json：
业务架构全景视图2.0 ==>1.0
应用架构全景视图3.0 ==>2.0
数据架构全景视图1.0 ==>3.0
技术架构全景视图4.0 ==>4.0
(2).新增功能：维护系统类型配置：
无索引时加载./initdata/uino_sys_type_config.json数据并初始化，默认类型为2，即标准产品。
如有需要可直接操作es数据或调用get,/tarsier-eam/trial/saas/change/systype/{类型：1SaaS试用、2标准产品、3SaaS正式产品}来修改系统类型
==================================================================================
日期: 2022-10-13
提交人: 王春蕾
更新内容: 模型树索引增加一个是否发布过的字段 :【releaseFlag=true是发布过,releaseFlag=false是未发布过；以此字段限定模型树类型是否可更改】
在es-head中执行请求，刷新数据
http://ip:端口/uino_eam_multi_model_hierarchy/_doc/_update_by_query/
{
  "script": {
    "source": "ctx._source['type.id']='643122257769207';ctx._source['type.code']='businessModel';ctx._source['type.name']='业务建模'"
  },
  "query": {
    "bool": {
      "must_not": [
        {
          "term": {
            "type.name.keyword": "业务建模"
          }
        },
        {
          "term": {
            "type.name.keyword": "数据建模"
          }
        }
      ]
    }
  }
}
==================================================================================
日期: 2022-10-18
提交人: 张强
更新内容: 新增自动成图缓存时间配置，缓存时间参数不配置的情况 默认为60s
uino.auto.draw.cache.timeout=60000
==================================================================================
日期: 2022-10-20
提交人: 张龙
更新内容:
(1).application-minor.properties配置文件追加license免校验接口配置项：
license.ignore.filter.pattern追加/login/getLoginMethod;
(2).init_eam.sql增加示例数据初始化数据：
table：vc_diagram_dir
id：1087638491356966 - 1109771240702387
==================================================================================
日期: 2022-10-26
提交人: 张强
更新内容:
系统菜单配置项顺序调整，需要删除 【uino_sys_module】 索引， 替换initdate目录下的uino_sys_module.json文件 重新启动项目加载初始化菜单数据 记得来个备份 :)
==================================================================================
日期: 2022-10-28
版本: V1.0
提交人: 常虎
影响功能:【业务架构全景视图-业务能力全景图】
更新内容:
修改"对象管理-->业务建模-->视图配置"分类定义，增加BUS_TREE_NAME属性,CONF_TYPE=BUS_TREE_NAME,CONF_JSON=EBPM-业务能力
==================================================================================
日期: 2022-11-16
版本: V1.0
提交人: 李崇
影响功能:【新用户初始化数据】
更新内容:
若要开启新用户数据初始化需要如下修改
1、oauth新版本包，需要在oauth项目develop-ea分支打包（对应ldap自动增加用户功能）
同时增加如下配置：
oauth.client.id=tarsier-comm
spring.main.allow-bean-definition-overriding=true
#是否开启新用户初始化数据
uino.user.init.data.enable=true
#新用户同步的分类code，多个用逗号隔开
uino.user.init.data.ciclasscode=sync_class,sync_class_222,同步分类中文
注意：默认同步admin用户的初始化数据，如果想修改，则增加如下配置
uino.user.init.data.source.user=other_login_code
2、eam项目开启（对应用户管理新增用户）
eam增加如下配置
uino.user.init.data.enable=true
#新用户同步的分类code，多个用逗号隔开
uino.user.init.data.ciclasscode=sync_class,sync_class_222,同步分类中文
注意：默认同步admin用户的初始化数据，如果想修改，则增加如下配置
uino.user.init.data.source.user=other_login_code
==================================================================================
日期: 2022-11-25
提交人: 王春蕾
影响功能:【数据建模er图】
更新内容:
对象管理dataModel领域，‘系统级逻辑实体’分类已删掉，对应asset.yml文件要覆盖更新；
==================================================================================
日期: 2022-11-29
提交人: 张龙
更新内容:
(1).uino_oauth_resource.json文件eam项目追加个人工作台审批流消息推送及方案获取免登录接口：
tarsier-eam => permitAllUrls =>  "/eam/notice/workflow/msg/save","/planDesign/getPlanForFeign","/planDesign/findRenewVersionPlanList"
注意：部署时替换uino_oauth_resource.json文件并重启eam,cj项目生效
(2).eam-workable配置文件application.properties添加审批流结果推送接口：
eam.push.plan.notice=http://【部署环境地址】/tarsier-eam/eam/notice/workflow/msg/save
==================================================================================
日期: 2022-12-07
提交人: 常虎
影响功能:【数据建模】
更新内容:
对象管理DataModel领域，‘实体属性’分类及‘数据标准’分类的"精度"字段类型改为"整数"
==================================================================================
日期: 2022-12-08
提交人: 卢聪
更新内容:
在uino_sys_module.json中添加“架构方案设计”和“架构方案资产”菜单。
==================================================================================
日期: 2022-12-19
提交人: 张强
更新内容:
设计空间新增架构方案设计 执行sql命令 插入数据 eam-221219.sql
==================================================================================
日期: 2023-01-03
提交人: 杨兵兵
更新内容:
菜单更新菜单名称架构方案设计和架构方案资产变更成 产品方案设计 产品方案资产 修改uino_sys_module.json初始化文件 需要删除索引uino_sys_module重启项目
==================================================================================
日期: 2023-02-15
提交人: 张龙
更新内容:
1.uino_eam_square_config.json添加“应用系统信息管理”卡片；
2.asset.yml维护“应用系统信息”。
=====================================================================================
日期: 2023-02-20
提交人: 王春蕾
更新内容: 新增字段：模型树层级配置----是否校验活动图拖拽开关，刷存量数据
GET请求：无参
http://localhost:1515/tarsier-eam/eam/hierarchy/crushDataActiveFlag
====================================================================================
日期: 2023-02-24
提交人: 王春蕾
更新内容: 制品分栏 新增classCode字段，刷新存量数据接口
FET请求：无参
http://localhost:1515/tarsier-eam/eam/artifact/crushArtifactColumns
====================================================================================
日期: 2023-03-02
提交人: 常虎
更新内容:
更新asset.yml文件,删除bm的配置内容
====================================================================================
日期: 2023-03-02
提交人: 张龙
更新内容:
1.ES和Mysql用户名、密码配置从application-minor.properties迁移至application-local.properties
涉及项目：eam、diagram：
更新方案：已有环境需要在application-local.properties维护以下配置，具体的用户名密码以实际情况为准：

#ElasticSearch是否需要认证
isAuth=true
#ElasticSearch用户名
esUser=uinnova
#ElasticSearch密码
esPwd=ENC(p9nZzfKfCVu9bDHgBRy/dGo7L8OKQXP+)

#Mysql用户名密码
ds.jdbc.vmdb.user=uinnova
ds.jdbc.vmdb.passwd=ENC(JiIIQjp80jzaI3qohk9rzGuQBv5mBFiW)
2.diagram项目application-local.properties维护visio服务访问地址：
uino.diagram.visio.external.url=http://pangu.quickea.com/restvisio/visio/visioToGojsJson
注：依据实际情况做配置
====================================================================================
日期: 2023-03-09
提交人: 王春蕾
更新内容: 将制品分栏架构元素数据刷入classCode,部署时是否执行，待（号哥）定
GET请求，无参
http://localhost:1515/tarsier-eam/eam/artifact/crushArtifactColumns
====================================================================================
日期: 2023-03-10
提交人: 常虎
更新内容:
数据建模C'实体发布后推送应用子系统相关数据到DIX(默认false不开启推送),以及dix接收数据接口,application-local.properties添加如下内容：
uino.eam.sync.system.open=true
uino.eam.sync.dix.url=http://ip/dix/sync
====================================================================================
日期: 2023-03-14
提交人: 卢聪
更新内容: eam-web/bootstrap.properties 添加仓颉配置项
process.definition.key=cj_technical_scheme_approve
uino.eam.word.chapter_name_regex=.*[.](doc|docx|xls|xlsx|ppt|pptx|pdf|jpg|jpeg|png)
====================================================================================
日期: 2023-03-15
提交人: 卢聪
更新内容: eam-workable/bootstrap.xml 修改工作流配置
eam:
  push:
    plan:
      notice: ${quickea.server.eam}/tarsier-eam/eam/notice/workflow/msg/save
cj:
  update:
    diagram:
      status: ${quickea.server.cangjie}/tarsier-eam/planDesign/updatePlanDiagramIsFlow
=========================================================================================
日期: 2023-03-27
提交人: 王春蕾
更新内容: 系统设置-字典表--制品类型分类和模型工艺类型
影响：新建制品类型/新建模型层级 时下拉菜单-查询类型数据来源为此；这两个字典表数据导出再导入新环境即可；
=======================================================================================
日期: 2023-04-06
提交人: 王春蕾
更新内容: base项目中uino_sys_dictionary_item.json和uino_sys_dictionary_class.json 两个文件有更新，需替换
影响：制品类型数据和模型工艺类型数据；
=====================================================================================
日期: 2023-04-07
提交人: 王春蕾
更新内容: 刷新模型层级旧数据 新增modelType字段
影响：系统设置查询模型工艺类型配置数据；
http://ip/tarsier-eam/flushData/crushModelType
GET请求无参;

=======================================================================================
日期: 2023-04-13
提交人: 常虎
更新内容: 新版目录改造->兼容存量视图、文件夹、方案数据接口
影响：更新新版目录后必刷，影响我的空间及资产仓库
POST请求
http://ip/tarsier-eam/flushData/newMenu/flushAll
=====================================================================================
=======================================================================================
日期: 2023-05-17
提交人: 杨兵兵
更新内容: 修改应用广场业务架构全景视图链接地址addressLink为/diagram#/fullview
影响：需要替换初始化文件uino_eam_square_config.json之后重启eam项目
=====================================================================================
日期: 2023-05-25
提交人: 张龙
更新内容: 新版目录改造->修复创建目录时未依据父目录权限同步创建权限接口
影响：影响资产仓库父目录配置了目录权限且权限作用范围包含子目录及文件的目录
GET请求
http://ip/tarsier-eam/flushData/category/permission/fix
=====================================================================================
日期: 2023-05-30
提交人: 杨兵兵
更新内容: 更好引导页初始化配置文件uino_eam_boot_entry_config.json
影响：替换文件之后重启eam
=====================================================================================

日期: 2023-05-30
提交人: 杨兵兵
更新内容: 工作流调用eam获取审批人、审批驳回或最后一节点更改视图/方案状态 接口需要加入白名单
影响：\resources\initdata\uino_oauth_resource.json 修改name=tarsier-eam 在permitAllUrls添加"/flowable/getApprovalUser","/flowable/approval/task"
=====================================================================================
日期: 2023-05-30
提交人: 杨兵兵
更新内容: 工作流配置文件bootstrap.yaml文件添加配置如下 需要修改白名单
generic:
  user:
    url: http://************/tarsier-eam/flowable/getApprovalUser
approval:
  task:
    url: http://************/tarsier-eam/flowable/approval/task
batch:
  modify:
    workbench:
      task: http://************/tarsier-eam/flowable/batchModifyWorkbenchTask
=====================================================================================
日期: 2023-06-01
提交人: 张龙
更新内容: 模型权限->刷空的模型权限数据
影响：影响资产仓库配置目录权限的数据
GET请求
http://ip/tarsier-eam/flushData/model/permission/flush
=====================================================================================
日期: 2023-06-06
提交人: 王晓蓉
更新内容：eam-web/application-local.properties和diagram/application-local.properties中增加对象存储相关的配置
spring.cloud.huawei.obs.endpoint=obs.cn-north-4.myhuaweicloud.com
spring.cloud.huawei.obs.access-key=IUSWCJWLVTIRKDQR9RJW
spring.cloud.huawei.obs.secret-key=HiZQUzYi6wDCeN45yVW9h3gowrVTazwS0LYHKxVt
spring.cloud.huawei.obs.bucketName=quickea
spring.cloud.huawei.obs.urlExpireSeconds=3600
rsm.util.sdkType=xinwang
obs.use=true
obs.rsm.url.prefix=/tarsier-eam/rsm
=====================================================================================
日期: 2023-06-06
提交人: 王晓蓉
更新内容：工作流配置文件bootstrap.yaml文件添加对象存储相关的配置如下
spring：
    cloud：
        huawei:
             obs:
               endpoint: obs.cn-north-4.myhuaweicloud.com
               access-key: IUSWCJWLVTIRKDQR9RJW
               secret-key: HiZQUzYi6wDCeN45yVW9h3gowrVTazwS0LYHKxVt
               bucketName: quickea
               urlExpireSeconds: 3600
rsm:
  util:
    sdkType: xinwang
obs:
  use: true
=====================================================================================
日期: 2023-06-29
提交人: 张龙
更新内容: 模型历史版本目录->刷目录改造前有问题的模型版本目录（补充模型历史版本根目录及修改L0层级历史版本目录的parentId）
影响：影响资产仓库模型切换版本
GET请求
http://ip/tarsier-eam/flushData/model/version/tagDir
=====================================================================================
日期: 2023-07-17
提交人: 常虎
更新内容: 系统设置->替换Logo默认图
影响：系统默认logo及背景图

使用resources/images/system下的图片替换服务器rsm/122/logos下的同名图片
=====================================================================================
日期: 2023-7-31
版本: V5.0
提交人: 周鼎海
影响功能:【资产仓库】
根据菜单配置-资产仓库子集，迁移生成资产仓库目录树，解耦菜单配置和资产仓库，调用接口
GET请求
http://ip/tarsier-eam/eam/assetWarehouse/migrationAssetModule
============================================================================
日期: 2023-7-31
版本: V5.0
提交人: 周鼎海
影响功能:【我的收藏】
对象管理-视图配置中添加我的收藏下拉筛选项（不添加返回默认值）
CONF_TYPE:ATTENTION_SOURCE
CONF_JSON:[
              {
                  "sourceTag": 0,
                  "showName": "架构设计"
              },
              {
                  "sourceTag": 1,
                  "showName": "资产仓库"
              }
          ]
=====================================================================================
日期: 2023-12-04
版本: V5.5.0
提交人: 周鼎海
影响功能: 品牌管理系统主题
更新内容: 对象管理->系统设置->添加系统主题颜色配置(不添加返回默认值)
CONF_TYPE:SystemColour
CONF_JSON:light
=====================================================================================
日期: 2023-12-04
版本: V5.5.0
提交人: 周鼎海
影响功能: 专题分析展示样式
更新内容: 对象管理->系统设置->样式配置(不添加返回默认值)
CONF_TYPE:assetManageShowType
CONF_JSON:1
=====================================================================================
日期: 2023-12-04
版本: V5.5.0
提交人: 张强
影响功能: 视图审批流程开关 true-开启审批 其他或没有-不开启审批
更新内容: 对象管理->系统设置->审批开关配置(不添加默认关闭)
CONF_TYPE:IS_APPROVE_KEY
CONF_JSON:true
=====================================================================================
日期: 2023-12-04
版本: V5.5.0
提交人: 常虎
影响功能: 视图中对象库的分类展示,配置需要在画布中隐藏的领域
更新内容: 对象管理->系统设置->需要在画布中隐藏的领域配置(不添加默认配置数据字典)
CONF_TYPE:DISPLAY_CONFIG
CONF_JSON:["数据字典"]
=====================================================================================
日期: 2023-12-04
版本: V5.5.0
提交人: 常虎
影响功能: 步骤自动带出规则分类
更新内容: 对象管理->系统设置->带出的规则分类名(不添加走默认配置)
CONF_TYPE:RULE_CLASS_CONFIG
CONF_JSON:DTEA-业务规则
=====================================================================================
日期: 2024-03-12
提交人: 张龙
影响功能: 资产仓库视图绑定资产
新增功能: 刷资产库历史视图绑定资产（刷数接口根据资产仓库视图反查私有库视图，通过私有库视图绑定资产信息，反推出资产仓库视图绑定资产并维护关联记录）
接口: get请求，/tarsier-eam/flushData/design/diagram/relate/system
=====================================================================================
日期: 2024-06-05
提交人: 卢聪
影响功能: 修改数据vc_diagram_version表字段CREATOR的长度
更新内容:
ALTER TABLE db_vmdb_test0824.vc_diagram_version MODIFY COLUMN CREATOR varchar(80) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '创建人';
=====================================================================================
日期: 2025-02-13
提交人: 张龙
影响功能: 视图发布&&资产库视图删除时的涉及资产库关系的删除
更新内容: eam-web/application-local.properties新增配置:
#视图发布&&资产库视图删除场景下：默认不对设计库关系做删除操作，配置为true时会对满足条件的设计库关系进行删除
rlt.del.open=false
#关系删除不生效操作：默认元模型关系自动构建以及外部系统接口数据同步
rlt.del.ignore.options=[VIS_MODEL_BUILD_AOTO,INTERFACE_SYNC]
=====================================================================================