package com.uinnova.product.eam.web.mix.diagram.v2.web;

import com.binary.framework.util.ControllerUtils;
import com.uinnova.product.eam.base.diagram.mix.model.VcDiagramDir;
import com.uinnova.product.eam.base.diagram.model.ESShapeDir;
import com.uinnova.product.eam.web.mix.diagram.v2.peer.ESShapePeer;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

/**
 * @Classname
 * @Description ES我的形状类
 * <AUTHOR>
 * @Date 2021-08-23-16:56
 */
@RestController
@RequestMapping("/eam/shapeDir")
public class EsShapeDirMvc {

    @Autowired
    private ESShapePeer svc;

    private static final Logger logger = LoggerFactory.getLogger(ESShapesMVC.class);

    @RequestMapping("/saveOrUpdate")
    @ModDesc(desc = "保存或更新我的形状目录,ID存在更新", pDesc = "图形目录信息", pType = VcDiagramDir.class, rDesc = "目录ID", rType = Long.class)
    public void saveOrUpdateDir(HttpServletRequest request, HttpServletResponse response, @RequestBody ESShapeDir dir) {
        Long id = this.svc.saveOrUpdate(dir);
        ControllerUtils.returnJson(request, response, id);
    }
}
