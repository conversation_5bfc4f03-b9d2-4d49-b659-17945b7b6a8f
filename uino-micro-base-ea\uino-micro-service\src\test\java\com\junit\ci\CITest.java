package com.junit.ci;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotEquals;
import static org.junit.Assert.assertTrue;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.sort.SortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.cloud.autoconfigure.RefreshAutoConfiguration;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import com.alibaba.fastjson.JSON;
import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.comm.model.ci.CCcCi;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CiQueryCdt;
import com.uinnova.product.vmdb.provider.search.bean.CcCiSearchPage;
import com.uino.dao.cmdb.ESCIAttrTransConfigSvc;
import com.uino.dao.cmdb.ESCIClassSvc;
import com.uino.dao.cmdb.ESCIHistorySvc;
import com.uino.dao.cmdb.ESCIRltSvc;
import com.uino.dao.cmdb.ESCISvc;
import com.uino.dao.cmdb.ESCmdbCommSvc;
import com.uino.dao.cmdb.ESDirSvc;
import com.uino.dao.cmdb.ESImageSvc;
import com.uino.dao.cmdb.ESTagSvc;
import com.uino.dao.sys.ESCIOperateLogSvc;
import com.uino.dao.sys.ESDictionaryClassSvc;
import com.uino.dao.sys.ESDictionaryItemSvc;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.query.ESAttrAggBean;
import com.uino.bean.cmdb.query.ESCISearchBean;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = { CITest.class, ESCIClassSvc.class, ESCmdbCommSvc.class, ESCISvc.class, ESTagSvc.class,
		ESCIRltSvc.class, ESImageSvc.class, ESDirSvc.class, ESCIHistorySvc.class,
		ESCIAttrTransConfigSvc.class, ESCIOperateLogSvc.class, ESDictionaryItemSvc.class, ESDictionaryClassSvc.class })
@ActiveProfiles("providerDev")
@ImportAutoConfiguration(RefreshAutoConfiguration.class)
@Slf4j
public class CITest {

	@Autowired
	ESCISvc ciSvc;

	@Autowired
	ESCmdbCommSvc commSvc;

	@Autowired
	ESCIRltSvc rltSvc;

	@Autowired
	ESCIClassSvc classSvc;

	@Autowired
	ESImageSvc esImgSvc;

	@Autowired
	ESDirSvc dirSvc;

	@Test
	public void asaveCI() {
		ESCIInfo ci = new ESCIInfo();
        ci.setId(1L);
		ci.setCiCode("test");
        ci.setClassId(100000000052508L);
		ci.setCiLabel("test");
		ci.setCiDesc("描述");
		ci.setCreator("admin");
		ci.setCiPrimaryKey("[\"test\"]");
        Map<String, Object> attrs = new HashMap<String, Object>();
		attrs.put("部署单元名称", "短信平台web管理控制台-异地");
		attrs.put("网络区域", "内网");
		attrs.put("计算规格", "4c8g");
		attrs.put("部署单元类型", "集群多活");
		attrs.put("SRCID", "test");
		attrs.put("虚IP", "");
		attrs.put("计算资源类型", "X86-KVM虚拟机");
		attrs.put("是否被监控", "Y");
		attrs.put("部署实例数量", "1");
		attrs.put("存储规格", "金");
		ci.setAttrs(attrs);
		ciSvc.saveOrUpdate(JSON.parseObject(JSON.toJSONString(ci)), true);

	}

	@Test
	public void bgetById() {
        CcCiInfo ci = ciSvc.getCiInfoById(1L);
        assertEquals(1L, ci.getCi().getId().longValue());
		System.out.println(JSON.toJSONString(ci));
	}

	@Test
	public void cgetListByQuery() {
		Page<ESCIInfo> page = ciSvc.getListByQuery(1, 20, QueryBuilders.matchAllQuery());
		CcCiSearchPage rs = commSvc.tranCcCiSearchPage(page);
        assertNotEquals(0L, rs.getTotalRows());
		System.out.println(JSON.toJSONString(rs));
	}

	@Test
	public void csearchCiByCdt() {
		ciSvc.updateByQuery(QueryBuilders.termQuery("ciCode.keyword", "test"), "ctx._source.attrs['部署实例数量'] = 2", true);
		CCcCi bean = new CCcCi();
		// String[] ciCodes = new String[] { "test" };
		// bean.setCiCodes(ciCodes);
		bean.setCiCode("591fe");
		bean.setStartClassId(1L);
        bean.setEndClassId(200000000052508L);
		CcCiSearchPage rs = ciSvc.searchCIByCdt(1, 10, bean);
		System.out.println(JSON.toJSONString(rs));

	}

	@Test
	public void deleteById() {
		Integer flag = ciSvc.deleteById(1L);
		assertEquals(1, flag.intValue());
	}

	@Test
	public void echeckCI() {
		ciSvc.getById(1L);
		CiQueryCdt cdt = new CiQueryCdt();
        ciSvc.queryPageByIndex(1L, 1, 10, cdt, false);
	}

	@Test
	public void fgetCIByTagId() {
		ESCISearchBean bean = new ESCISearchBean();
		List<Long> tagIds = new ArrayList<Long>();
        tagIds.add(100000000010004L);
		bean.setTagIds(tagIds);
		List<String> words = new ArrayList<String>();
		words.add("网络");
		bean.setWords(words);
		CcCiSearchPage rs = ciSvc.searchCIByBean(bean);
		System.out.println(JSON.toJSONString(rs));
	}

	@Test
	public void getCIByClassID() {
		ciSvc.searchCIByBean(new ESCISearchBean());

		ESCISearchBean bean = new ESCISearchBean();
		List<Long> classIds = new ArrayList<Long>();
        classIds.add(100000000052508L);
		bean.setClassIds(classIds);
		CcCiSearchPage rs = ciSvc.searchCIByBean(bean);
		System.out.println(JSON.toJSONString(rs));
	}

	@Test
	public void fgetCIByClassIDSort() {
		List<SortBuilder<?>> sorts = new ArrayList<SortBuilder<?>>();
		sorts.add(SortBuilders.scoreSort().order(SortOrder.DESC));
		sorts.add(SortBuilders.fieldSort("ciCode.keyword").order(SortOrder.DESC));
        Page<ESCIInfo> rs = ciSvc.getSortListByQuery(1, 5, QueryBuilders.termQuery("classId", 100000000052508L), sorts);
		System.out.println(JSON.toJSONString(rs));

	}

	@Test
	public void hqueryAttrVal() {
		ESAttrAggBean bean = new ESAttrAggBean();
		bean.setAttrName("attrs.网络区域");
        bean.setClassId(100000000052508L);
		bean.setPageNum(1);
		bean.setPageSize(20);
		bean.setLike("网");
		Page<String> rs = ciSvc.queryAttrVal(1L,bean);
		System.out.println(JSON.toJSONString(rs));
	}

	@Test
	public void igroupByCountField() {
		Map<String, Long> rs = ciSvc.groupByCountField("attrs.网络区域.keyword",
            QueryBuilders.termQuery("classId", 100000000052508L));
		System.out.println(JSON.toJSONString(rs));
	}

	@Test
	public void jgroupByCountField() {
		ESCIInfo ci = new ESCIInfo();
        ci.setId(2L);
		ci.setCiCode("test11");
        ci.setClassId(100000000052508L);
		ci.setCiLabel("test");
		ci.setCiDesc("描述");
		ci.setCreator("admin");
		ci.setCiPrimaryKey("[\"test\"]");
        Map<String, Object> attrs = new HashMap<String, Object>();
		attrs.put("部署单元名称", "短信平台web管理控制台-异地");
		attrs.put("网络区域", "内网");
		attrs.put("计算规格", "4c8g");
		attrs.put("部署单元类型", "集群多活");
		attrs.put("SRCID", "test");
		attrs.put("虚IP", "");
		attrs.put("计算资源类型", "X86-KVM虚拟机");
		attrs.put("是否被监控", "Y");
		attrs.put("部署实例数量", "1");
		attrs.put("存储规格", "金");
		ci.setAttrs(attrs);
        ciSvc.saveOrUpdateCI(commSvc.tranCcCiInfo(ci, false));
	}

	@Test
	public void lgroupByCountField() {
		ciSvc.deleteByQuery(QueryBuilders.termQuery("id", 2), true);
	}

	@Test
	public void mgetById() {
        CcCiInfo ci = ciSvc.getCiInfoById(2L);
		System.out.println(JSON.toJSONString(ci));
		assertTrue(ci == null);
	}

	@Test
	public void nsaveOrUpdateCIBatch() {
		ESCIInfo ci = new ESCIInfo();
		ci.setCiCode("test");
        ci.setClassId(100000000052508L);
		ci.setCiLabel("test");
		ci.setCiDesc("描述");
		ci.setCreator("admin");
		ci.setCiPrimaryKey("[\"test\"]");
        Map<String, Object> attrs = new HashMap<String, Object>();
		attrs.put("部署单元名称", "短信平台web管理控制台-异地");
		attrs.put("网络区域", "内网");
		attrs.put("计算规格", "4c8g");
		attrs.put("部署单元类型", "集群多活");
		attrs.put("SRCID", "test");
		attrs.put("虚IP", "");
		attrs.put("计算资源类型", "X86-KVM虚拟机");
		attrs.put("是否被监控", "Y");
		attrs.put("部署实例数量", "1");
		attrs.put("存储规格", "金");
		ci.setAttrs(attrs);
		List<CcCiInfo> list = new ArrayList<CcCiInfo>();
        list.add(commSvc.tranCcCiInfo(ci, false));
        ciSvc.saveOrUpdateCIBatch(list, false);
	}

	@Test
	public void oremoveByIds() {
		List<Long> list = new ArrayList<Long>();
        list.add(1L);
        list.add(2L);
		ciSvc.deleteByIds(list);
		ciSvc.deleteByQuery(QueryBuilders.termQuery("ciCode.keyword", "test"), true);
	}

	@Test
	public void pqueryPageByIndex() {
		CiQueryCdt cdt = new CiQueryCdt();
        cdt.setClassId(100000000052508L);
		cdt.setLike("网络");
        System.out.println(JSON.toJSONString(ciSvc.queryPageByIndex(1L, 1, 10, cdt, false)));
	}

	@Test
	public void qcountCIByClassId() {
        System.out.println(JSON.toJSONString(ciSvc.countCIByQuery(QueryBuilders.matchAllQuery())));
	}

	@Test
	public void rgetAllClassId() {
		List<CcCiClassInfo> rs = classSvc.queryAllClasses();
		log.info(JSON.toJSONString(commSvc.tranESCIClassInfo(rs.get(0))));
	}

	@Test
	public void getAllCIByScroll() {
		// QueryBuilders.termQuery("classId", 100000000000001l)
		// Map<String, Page<ESCIInfo>> rs = ciSvc.getScrollByQuery(1, 1000,
		// QueryBuilders.matchAllQuery(), "id", false);
		Map<String, Page<ESCIInfo>> rs = ciSvc.getScrollByQuery(1, 1000,
            QueryBuilders.termQuery("classId", 100000000000002L), "id", false);
		List<ESCIInfo> list = new ArrayList<ESCIInfo>();
		long s = System.currentTimeMillis();

		if (rs != null) {
			String scrollId = rs.keySet().iterator().next();
			Page<ESCIInfo> page = rs.get(scrollId);
			long total = page.getTotalRows();
			list.addAll(page.getData());
			long length = page.getData().size();
			while (length < total) {
				List<ESCIInfo> secList = ciSvc.getListByScroll(scrollId);
				/*
				 * for (ESCIInfo e : secList) { System.out.println(e.getId()); }
				 */
				// list.addAll(secList);
				length += secList.size();
			}
			ciSvc.clearScroll(scrollId);
			System.out.println(total + "    " + scrollId);

			System.out.println(System.currentTimeMillis() - s);
		}
	}

}
