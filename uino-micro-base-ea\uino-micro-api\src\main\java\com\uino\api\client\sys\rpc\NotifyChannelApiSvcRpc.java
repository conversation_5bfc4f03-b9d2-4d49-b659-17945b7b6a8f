package com.uino.api.client.sys.rpc;

import java.util.Collection;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import com.uino.bean.sys.base.NotifyChannel;
import com.uino.bean.sys.business.NotifyChannelReqDto;
import com.uino.bean.sys.business.NotifyData;
import com.uino.provider.feign.sys.NotifyChannelFeign;
import com.uino.api.client.sys.INotifyChannelApiSvc;

@Service
@Primary
public class NotifyChannelApiSvcRpc implements INotifyChannelApiSvc {

    @Autowired
    private NotifyChannelFeign notifyChannelFeign;

    @Override
    public NotifyChannel save(NotifyChannel saveInfo) {
        // TODO Auto-generated method stub
        return notifyChannelFeign.save(saveInfo);
    }

    @Override
    public void delete(Collection<Long> ids) {
        // TODO Auto-generated method stub
        notifyChannelFeign.delete(ids);
    }

    @Override
    public List<NotifyChannel> search(NotifyChannelReqDto searchDto) {
        // TODO Auto-generated method stub
        return notifyChannelFeign.search(searchDto);
    }

    @Override
    public boolean sendNotify(NotifyData notifyData) {
        // TODO Auto-generated method stub
        return notifyChannelFeign.sendNotify(notifyData);
    }
}
