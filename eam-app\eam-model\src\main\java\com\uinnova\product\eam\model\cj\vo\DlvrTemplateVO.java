package com.uinnova.product.eam.model.cj.vo;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class DlvrTemplateVO {
    private Long id;
    private Long businessId;
    private String templateName;
    private Long proposalType;
    private String proposalName;
    private String templateDesc;
    private String remarkInfo;
    private Integer quoteNum;
    private int version;
    private String creator;
    private String modifier;
    private Long createTime;
    private Long modifyTime;
    private Integer status;
    private String icon;
    @Comment("模板绑定的资产Map")
    private Map<String,String> bindAssetMap;
    @Comment("模板绑定的资产List")
    private List<BindAssetVo> bindAssetList;

    @Comment("方案发布资产-目录名称")
    private String echoDirName;

    @Comment("方案发布资产-目录id")
    private Long assetsDirId;

    @Comment("方案发布资产-目录类型")
    private Integer dirType;

    @Comment("创建者名称")
    private String creatorName;

    @Comment("方案审批类型")
    private Integer approvalType;

    @Comment("分类目录id")
    private Long domainDirId;

}
