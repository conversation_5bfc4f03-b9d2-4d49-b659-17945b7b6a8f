package com.uinnova.product.eam.model;

import java.io.Serializable;
import java.util.List;

import com.uinnova.product.eam.comm.model.VcTheme;
import com.uinnova.product.eam.comm.model.VcThemeDiagram;
import com.uinnova.product.eam.comm.model.VcThemeField;

public class VcThemeInfo implements Serializable {
	private static final long serialVersionUID = 1L;
	
	
	/**
	 * 主题字段对象
	 */
	private List<VcThemeField> themeField;
	
	
	/**
	 * 主题视图关联对象
	 */
	private List<VcThemeDiagram> themeDiagrams;
	
	
	/**
	 * 主题对象
	 */
	private VcTheme theme;


	public List<VcThemeField> getThemeField() {
		return themeField;
	}


	public void setThemeField(List<VcThemeField> themeField) {
		this.themeField = themeField;
	}


	public List<VcThemeDiagram> getThemeDiagrams() {
		return themeDiagrams;
	}


	public void setThemeDiagrams(List<VcThemeDiagram> themeDiagrams) {
		this.themeDiagrams = themeDiagrams;
	}


	public VcTheme getTheme() {
		return theme;
	}


	public void setTheme(VcTheme theme) {
		this.theme = theme;
	}





}
