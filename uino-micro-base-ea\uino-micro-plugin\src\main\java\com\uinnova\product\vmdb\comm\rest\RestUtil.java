package com.uinnova.product.vmdb.comm.rest;

import com.binary.core.lang.Conver;
import com.binary.framework.exception.ServiceException;
import com.binary.jdbc.Page;
import com.binary.json.JSON;
import com.binary.json.JSONException;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 
 * <AUTHOR>
 *
 */
public abstract class RestUtil {

    /**
     * 转换远程返回的统一数组对象
     * 
     * @param jsonstring
     * @param clazz
     * @return
     */
    @SuppressWarnings({ "unchecked", "rawtypes" })
    public static <T> Page<T> toRemotePage(String jsonstring, Class<T> clazz) {
        Map<String, Object> obj = (Map) JSON.toObject(jsonstring);
        Object data = obj.get("data");
        boolean success = Conver.to(obj.get("success"), Boolean.class);
        String message = (String) obj.get("message");

        if (success) {
            Map<?, ?> pm = (Map<?, ?>) data;

            if (pm != null) {
                Page<T> page = new Page<T>();
                Object pageNum = pm.get("pageNum");
                Object pageSize = pm.get("pageSize");
                Object totalRows = pm.get("totalRows");
                Object totalPages = pm.get("totalPages");
                List<?> rows = (List<?>) pm.get("data");

                if (pageNum != null) {
                    page.setPageNum(Conver.to(pageNum, long.class));
                } else {
                    page.setPageNum(1);
                }
                if (pageSize != null) {
                    page.setPageNum(Conver.to(pageSize, long.class));
                }
                if (totalRows != null) {
                    page.setPageNum(Conver.to(totalRows, long.class));
                }
                if (totalPages != null) {
                    page.setPageNum(Conver.to(totalPages, long.class));
                }

                List<T> rs = new ArrayList<T>();
                if (rows != null && rows.size() > 0) {
                    for (int i = 0; i < rows.size(); i++) {
                        Object o = rows.get(i);
                        rs.add(Conver.mapping(clazz, o));
                    }
                }
                page.setData(rs);

                return page;
            } else {
                return null;
            }
        } else {
            throw new JSONException(" call remote error! " + message);
        }
    }

    @SuppressWarnings({ "rawtypes", "unchecked" })
    public static Object parseResult(String jsonstring, Class<?> resultType, Type resultGenericType) {
        String jsonData = JSON.toRemoteObject(jsonstring, String.class);
        if (jsonData == null) {
            return null;
        }

        Object obj = JSON.toObject(jsonData);
        if (Page.class.isAssignableFrom(resultType)) {
            if (!(obj instanceof Map)) {
                throw new ServiceException(" is wrong json-result '" + jsonData + "'! ");
            }
            Map map = (Map) obj;

            ParameterizedType pt = (ParameterizedType) resultGenericType;
            Type[] actualTypes = pt.getActualTypeArguments();
            if (actualTypes == null || actualTypes.length == 0) {
                throw new ServiceException(" not setting actualType at result-page type! ");
            }
            if (!(actualTypes[0] instanceof Class)) {
                throw new ServiceException(" is wrong actualType '" + actualTypes[0] + "' at result-page type! ");
            }
            Class componentType = (Class) actualTypes[0];

            Page page = new Page();
            page.setPageNum(Conver.to(map.get("pageNum"), Long.class));
            page.setPageSize(Conver.to(map.get("pageSize"), Long.class));
            page.setTotalRows(Conver.to(map.get("totalRows"), Long.class));
            page.setTotalPages(Conver.to(map.get("totalPages"), Long.class));
            page.setData(toList((List) map.get("data"), componentType));
            return page;
        } else {
            return Conver.mapping(resultGenericType, obj);
        }
    }

    private static <T> List<T> toList(List<?> list, Class<T> clazz) {
        List<T> array = new ArrayList<T>();
        if (list != null) {
            for (int i = 0; i < list.size(); i++) {
                Object item = list.get(i);
                array.add(Conver.mapping(clazz, item));
            }
        }
        return array;
    }

}
