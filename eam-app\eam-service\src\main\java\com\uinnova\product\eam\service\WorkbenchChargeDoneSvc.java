package com.uinnova.product.eam.service;

import com.binary.framework.web.RemoteResult;
import com.binary.jdbc.Page;
import com.uinnova.product.eam.comm.model.es.WorkbenchChargeDone;
import com.uinnova.product.eam.model.WorkbenchChargeDoneDto;
import com.uinnova.product.eam.model.vo.WorkbenchBO;
import com.uinnova.product.eam.model.vo.WorkbenchChargeDoneVO;

import java.util.List;
import java.util.Map;

public interface WorkbenchChargeDoneSvc {
    Boolean saveOrUpdate(List<WorkbenchChargeDone> workbenchChargeDoneList);

    boolean changeAction(String businessId);

    Page<WorkbenchChargeDone> pageQueryList(WorkbenchChargeDoneDto doneDto);

    RemoteResult transform(String businessId,String transId,Integer dirType);

    WorkbenchBO getCount();

    boolean updateInfoStatus(Long planId, String diagramId, Integer type);

    void deleteWorkbenchChargeDone(WorkbenchChargeDoneVO workbenchChargeDoneVO);

    void deleteWorkbenchPublishedChargeDone(String businessKey,Integer dirType);

    boolean deleteByCondition(WorkbenchChargeDone workbenchChargeDone);

    Boolean batchModifyWorkbenchTask(String processInstanceId,String businessKey);

    Map<String, Object> todoCount(String loginCode);

}
