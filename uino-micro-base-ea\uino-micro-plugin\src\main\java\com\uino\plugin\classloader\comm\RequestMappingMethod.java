package com.uino.plugin.classloader.comm;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.lang.reflect.Method;

/**
 * MVC mapping的方法、类信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RequestMappingMethod {

    /**
     * 类信息
     */
    private Class<?> clazz;

    /**
     * 方法信息
     */
    private Method method;

    /**
     * 映射路径
     */
    private String mapping;

}
