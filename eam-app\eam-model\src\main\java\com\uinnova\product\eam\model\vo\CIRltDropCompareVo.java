package com.uinnova.product.eam.model.vo;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.model.enums.CIRltDelType;
import lombok.Data;

@Comment("待发布视图关系删除对比参数")
@Data
public class CIRltDropCompareVo {

    @Comment("删除类型")
    private CIRltDelType delType;
    @Comment("待发布视图id")
    private String privateDiagramId;
    @Comment("待发布矩阵id")
    private Long privateMatrixId;
    @Comment("待删除的资产库视图id")
    private String designDiagramId;
    @Comment("待删除的资产库矩阵id")
    private Long designMatrixId;
    @Comment("第几页")
    private Integer pageNum = 1;
    @Comment("每页显示记录数")
    private Integer pageSize = 20;
    @Comment("流程实例id")
    private String processInstanceId;
}
