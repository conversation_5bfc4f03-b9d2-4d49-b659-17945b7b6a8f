package com.uinnova.product.eam.service.asset;

import com.google.common.collect.Lists;

import java.util.List;

/**
 * 资产常量
 * <AUTHOR>
 */
public interface AssetContent {
    /**
     * 属性字段
     */
    public static final String CREATION_TIME = "创建时间";
    public static final String MODIFI_TIME = "修改时间";
    public static final String RELEASE_TIME = "发布时间";
    public static final String RELEASE_STATE = "资产状态";
    public static final List<String> ATTR_DEFAULT_LIST = Lists.newArrayList(CREATION_TIME, MODIFI_TIME, RELEASE_TIME, RELEASE_STATE);


    /**
     * 资产状态
     */
    public static final String RELEASE = "已发布";
    public static final String DRAFT = "草稿";
    public static final String CHANGE = "变更";
    public static final String CREATE = "创建";
    public static final String RELEASING = "发布";
    public static final String CANCELLATION = "作废";
    public static final String CANCELLED = "已作废";
    public static final List<String> IGNORE_ATTR = Lists.newArrayList("所属用户", "变更原因", "修改时间", "发布时间","创建时间",RELEASE_STATE);
    public static final String CHANGE_RECORD = "变更原因";


    /**
     * 标签tag
     */
    public static String CHECKBOX = "多选";

    /**
     * 数据字典 关联属性 枚举 多选分隔符
     */
    public static final String MULT_SYMBOL = "@@";

}
