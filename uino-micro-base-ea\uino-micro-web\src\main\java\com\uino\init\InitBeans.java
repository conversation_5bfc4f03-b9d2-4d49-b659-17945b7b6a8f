package com.uino.init;

import com.binary.core.i18n.Language;
import com.binary.core.i18n.LanguageTranslator;
import com.uinnova.product.devcloud.i18n.client.I18nClient;
import com.uinnova.product.devcloud.i18n.client.trans.LanguageGetter;
import com.uino.comm.feign.FeignErrorDecoder;
import com.uino.service.i18.BaseI18Client;
import com.uino.service.i18.BaseI18nLanguageTranslator;
import com.uino.util.log.OperateLogModuleInfo;
import lombok.extern.slf4j.Slf4j;
import net.dreamlu.mica.xss.core.XssCleaner;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Entities;
import org.jsoup.safety.Safelist;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Primary;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Set;

@Configuration
@Slf4j
public class InitBeans {

    @Autowired(required = false)
    private I18nClient i18Client;

    @Autowired(required = false)
    private LanguageGetter languageGetter;

    @Value("${i18n.lang.default:ZHC}")
    private Language defaultLanguage;

    public static final Safelist WHITE_LIST = Safelist.basicWithImages();

    @Bean
    public XssCleaner xssCleaner() {
        return (name, html, type) -> {
            Document.OutputSettings settings = new Document.OutputSettings()
                    // 1. 转义用最少的规则，没找到关闭的方法
                    .escapeMode(Entities.EscapeMode.xhtml)
                    // 2. 保留换行
                    .prettyPrint(false);
            // 注意会被转义
            return Jsoup.clean(html, "",WHITE_LIST, settings);
        };
    }

    @Bean(name = "comm")
    public OperateLogModuleInfo logModuleInfo() {
        return new OperateLogModuleInfo() {
            @Override
            public String getModuleCode() {
                return "COMM";
            }

            @Override
            public String getModuleName() {
                return "公共组件";
            }

            @Override
            public String getModuleMvcPackagePrefix() {
                return "com.uino.web";
            }
        };
    }

    @Bean
    @ConditionalOnMissingBean
    public RestTemplate restTemplate(RestTemplateBuilder builder) {
        log.info("注册restTemplate成功");

        // 使用RestTemplateBuilder创建RestTemplate
        return builder
                .requestFactory(() -> {
                    org.springframework.http.client.SimpleClientHttpRequestFactory factory = new org.springframework.http.client.SimpleClientHttpRequestFactory();
                    factory.setConnectTimeout(30000);
                    factory.setReadTimeout(30000);
                    return factory;
                })
                .build();
    }

    @Bean
    @ConditionalOnMissingBean
    public I18nClient I18nClient(@Value("#{'${i18n.files:i18-base.json}'.split(',')}") Set<String> i18FileNames) {
        I18nClient client = null;
        log.info("开始注册i18-client，修改i18n.files可修改对应国际化文件读取，支持逗号隔开字符串");
        client = new BaseI18Client(i18FileNames);
        log.info("注册i18-client成功");
        return client;
    }

    private static final Map<String, Language> httpHeaderLanguageDict = new LinkedHashMap<>();
    static {
        httpHeaderLanguageDict.put("zh-cn", Language.ZHC);
        httpHeaderLanguageDict.put("zh", Language.ZHC);
        httpHeaderLanguageDict.put("en-US", Language.EN);
        httpHeaderLanguageDict.put("en", Language.EN);
    }

    @Bean
    @ConditionalOnMissingBean
    public LanguageGetter languageGetter() {
        return () -> {
            try {
                ServletRequestAttributes attrs = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
                String Language = attrs.getRequest().getLocale().getLanguage();
                log.info("语言获取器注册成功");
                return httpHeaderLanguageDict.get(Language);
            } catch (Exception e) {
                log.debug("获取语言失败");
            }
            return null;
        };
    }

    @Bean
    @Lazy(false)
    @ConditionalOnMissingBean
    public LanguageTranslator I18nLanguageTranslator() throws Exception {
        BaseI18nLanguageTranslator i18nLanguageTranslator = new BaseI18nLanguageTranslator();
        i18nLanguageTranslator.setI18nClient(i18Client);
        i18nLanguageTranslator.setDefaultLanguage(defaultLanguage);
        i18nLanguageTranslator.setLanguageGetter(languageGetter);
        log.info("国际化默认语言为【{}】，可通过修改i18n.lang.default修改默认语言，属性范围参考class:Language", defaultLanguage);
        return i18nLanguageTranslator;
    }

    @Bean
    @ConditionalOnProperty(name = "run-env", havingValue = "cluster", matchIfMissing = false)
    public SyncResourceJob syncResourceJob() {
        log.info("文件同步器注册成功");
        return new SyncResourceJob();
    }

    @Bean
    @Primary
    @ConditionalOnMissingBean
    @ConditionalOnProperty(prefix = "base", name = "load-type", havingValue = "rpc")
    public FeignErrorDecoder initFeignErrorDecoder() {
        return new FeignErrorDecoder();
    }
}
