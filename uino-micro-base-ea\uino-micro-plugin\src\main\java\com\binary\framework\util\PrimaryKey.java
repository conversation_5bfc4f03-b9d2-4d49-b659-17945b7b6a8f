package com.binary.framework.util;

import java.util.HashMap;
import java.util.Map;

import com.binary.core.http.HttpClient;
import com.binary.core.http.HttpUtils;
import com.binary.core.lang.Conver;
import com.binary.core.util.BinaryUtils;
import com.binary.framework.exception.PrimaryKeyException;


/**
 * 主键获取对象
 */
public class PrimaryKey {
	
	
	/** 缺省主键名 **/
	public static final String PUBLIC = "|PUBLIC|";
	
	/** 缺省批次 **/
	public static final int DEFAULT_BATCH = 500;
	
	
	/**
	 * 获取远程Key值url, 需在Frame环境变量中配置
	 */
	private static final String Single_Web_Root = "Single_Web_Root";
	private static final String Primary_Key_Batch = "Primary_Key_Batch";
	
	
	
	
	/**
	 * 主键加载器 
	 * <AUTHOR>
	 */
	public interface PrimaryKeyLoader {
		
		//默认优先级
		public int priority = -1;
		
		public int getPriority();
		/**
		 * 加载主建
		 * @param name 主键名
		 * @param batch 批次
		 * @return start-end
		 */
		public String load(String name, int batch);
	}
	
	
	
	
	public static class DefaultRemotePrimaryKeyLoader implements PrimaryKeyLoader {

		private HttpClient httpClient;
		
		public DefaultRemotePrimaryKeyLoader() {
			FrameworkProperties properties = FrameworkProperties.getInstance();
			String url = properties.getString(Single_Web_Root);
			if(url==null || (url=url.trim()).length()==0) throw new PrimaryKeyException(" is not setting "+Single_Web_Root+" at framework-config! ");
			url = HttpUtils.formatContextPath(url).substring(1);
			url += "/keys/getKey";
			this.httpClient = HttpClient.getInstance(url);
		}
		
		
		@Override
		public String load(String name, int batch) {
			Map<String,Object> form = new HashMap<String,Object>();
			form.put("name", name.trim());
			form.put("batch", batch);
			String s = this.httpClient.request(form);
					
			if(s==null || (s=s.trim()).length()==0) {
				throw new PrimaryKeyException(" connect remote data: the return value is empty! ");
			}
			
			String data = ControllerUtils.toRemoteJsonObject(s, String.class);
			return data;
		}


		@Override
		public int getPriority() {
			// TODO Auto-generated method stub
			return PrimaryKeyLoader.priority;
		}
		
	}
	
	
	private static PrimaryKeyLoader DEFAULT_LOADER;
	private static int FRAME_BATCH = -1;
	
	private static final Object syncobj = new Object();
	private static final Map<String, PrimaryKey> keys = new HashMap<String, PrimaryKey>();
	
	
	private String name;
	private int batch;
	
	private long curr;
	private long max;
	
	private PrimaryKeyLoader primaryKeyLoader;
	
	
	
	protected PrimaryKey(String name, int batch, PrimaryKeyLoader primaryKeyLoader) {
		this.name = name.trim().toUpperCase();
		this.batch = batch;
		this.primaryKeyLoader = primaryKeyLoader;
		this.curr = 0;
		this.max = 0;
	}
	
	
	
	
	
	public static PrimaryKeyLoader getDefaultPrimaryKeyLoader() {
		if(DEFAULT_LOADER == null) {
			synchronized (syncobj) {
				if(DEFAULT_LOADER == null) {
					DEFAULT_LOADER = new DefaultRemotePrimaryKeyLoader();
				}
			}
		}
		return DEFAULT_LOADER;
	}
	
	public static void setDefaultPrimaryKeyLoader(PrimaryKeyLoader primaryKeyLoader) {
		BinaryUtils.checkEmpty(primaryKeyLoader, "primaryKeyLoader");
		synchronized (syncobj) {
			if(DEFAULT_LOADER == null) {
				DEFAULT_LOADER = primaryKeyLoader;				
			}else if(DEFAULT_LOADER != null && DEFAULT_LOADER.getPriority() > primaryKeyLoader.getPriority()) {	
				//如果既有加载器优先级更高,则优先采用既有加载器
			} else {
				DEFAULT_LOADER = primaryKeyLoader;
			}
			
		}
	}
	


	public static int getDefaultBatch() {
		if(FRAME_BATCH < 0) {
			synchronized(syncobj) {
				if(FRAME_BATCH < 0) {
					FrameworkProperties properties = FrameworkProperties.getInstance();
					String fbobj = properties.getString(Primary_Key_Batch);
					if(fbobj==null || (fbobj=fbobj.trim()).length()==0) {
						FRAME_BATCH = DEFAULT_BATCH;
					}else {
						int fb = Conver.to(fbobj, int.class);
						if(fb <= 0) throw new PrimaryKeyException(" the framework-property setting "+Primary_Key_Batch+" is wrong '"+fb+"'! ");
						FRAME_BATCH = fb;
					}
				}
			}
			
			if(FRAME_BATCH <= 0) throw new PrimaryKeyException(" the batch '"+FRAME_BATCH+"' must >0! ");
		}
		return FRAME_BATCH;
	}
	
	
	
	
	public synchronized long next() {
		if(this.curr >= this.max) {
			try {
				String data = primaryKeyLoader.load(name, batch);
				
				int index = data.indexOf("-");
				if(index <= 0) throw new PrimaryKeyException(" connect remote data: the return value is wrong '"+data+"'! ");
				
				long d1 = Long.parseLong(data.substring(0, index));
				long d2 = Long.parseLong(data.substring(index+1));
				
				if(d2 < d1) {
					throw new PrimaryKeyException(" connect remote data: is wrong data '"+data+"'! ");
				}
				
				this.curr = d1;
				this.max = d2;
				
				return this.curr;
			}catch(Exception e) {
				throw new PrimaryKeyException("load data error!", e);
			}
		}
		this.curr ++ ;
		return this.curr;
	}
	
	
	
	
	
	
	public static PrimaryKey getInstance() {
		return getInstance(null, null, null);
	}
	
	public static PrimaryKey getInstance(String name) {
		return getInstance(name, null, null);
	}
	
	
	public static PrimaryKey getInstance(String name, Integer batch) {
		return getInstance(name, batch, null);
	}
	
	public static PrimaryKey getInstance(String name, Integer batch, PrimaryKeyLoader loader) {
		if(name==null || (name=name.trim()).length()==0) name = PUBLIC;
		if(batch==null || batch.intValue()<=0) batch = getDefaultBatch();
		if(loader == null) loader = getDefaultPrimaryKeyLoader();
		
		name = name.toUpperCase();
		
		PrimaryKey key = keys.get(name);
		if(key == null) {
			synchronized(syncobj) {
				key = keys.get(name);
				if(key == null) {
					key = new PrimaryKey(name, batch, loader);
					keys.put(name, key);
				}
			}
		}
		return key;
	}
	
	
	
}
