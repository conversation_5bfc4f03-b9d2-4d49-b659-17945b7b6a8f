package com.uinnova.product.eam.service.impl;

import com.uinnova.product.eam.dao.HealthyDao;
import com.uinnova.product.eam.domain.Healthy;
import com.uinnova.product.eam.service.HealthyService;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * description
 *
 * <AUTHOR>
 * @since 2023/7/24 16:58
 */
@Service
public class HealthyServiceImpl implements HealthyService {

    @Resource
    private HealthyDao healthyDao;


    @Override
    public Boolean check() {
        List<Healthy> listByQueryScroll = healthyDao.getListByQueryScroll(QueryBuilders.matchAllQuery());
        if(CollectionUtils.isEmpty(listByQueryScroll)){
            return false;
        }else {
            return listByQueryScroll.get(0).isHealthy();
        }
    }
}
