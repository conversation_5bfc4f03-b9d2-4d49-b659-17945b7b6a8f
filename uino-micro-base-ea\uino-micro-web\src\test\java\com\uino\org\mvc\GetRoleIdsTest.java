package com.uino.org.mvc;

import java.util.HashSet;

import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.mockito.Mockito;
import org.mockito.internal.util.collections.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import com.alibaba.fastjson.JSON;
import com.binary.framework.web.RemoteResult;
import com.uino.StartBaseWebAppliaction;
import com.uino.dao.permission.ESOrgSvc;

import lombok.extern.slf4j.Slf4j;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = { StartBaseWebAppliaction.class }, webEnvironment = WebEnvironment.RANDOM_PORT)
@Slf4j
@ActiveProfiles("provider-local")
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class GetRoleIdsTest {
	@Autowired
	private TestRestTemplate restTemplate;
	@MockBean
	private ESOrgSvc esOrgSvc;
	private static String testUrl;

	@BeforeClass
	public static void initCls() {
		GetRoleIdsTest.testUrl = "/permission/org/getRoleIds";
	}

	@Before
	public void start() {
		Mockito.when(esOrgSvc.getInOrgRoleIds(1L)).thenReturn(Sets.newSet(1L, 2L, 3L));
		Mockito.when(esOrgSvc.getInOrgRoleIds(2L)).thenReturn(new HashSet<>());
	}

	@Test
	public void test01() {
		Long requestBody = 1L;
		String responseBodyStr = restTemplate.postForObject(testUrl, requestBody, String.class);
		RemoteResult responseBody = JSON.parseObject(responseBodyStr, RemoteResult.class);
		Assert.assertTrue(responseBody.isSuccess());
	}

	@Test
	public void test02() {
		Long requestBody = 2L;
		String responseBodyStr = restTemplate.postForObject(testUrl, requestBody, String.class);
		RemoteResult responseBody = JSON.parseObject(responseBodyStr, RemoteResult.class);
		Assert.assertTrue(responseBody.isSuccess());
	}
}
