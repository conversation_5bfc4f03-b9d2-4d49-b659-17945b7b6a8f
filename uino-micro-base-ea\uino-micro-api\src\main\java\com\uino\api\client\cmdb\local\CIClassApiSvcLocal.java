package com.uino.api.client.cmdb.local;

import java.util.Collection;
import java.util.List;
import java.util.Set;

import com.uino.dao.BaseConst;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClass;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uino.service.cmdb.microservice.ICIClassSvc;
import com.uino.bean.cmdb.base.ESCIClassInfo;
import com.uino.bean.cmdb.business.ClassNodeInfo;
import com.uino.bean.cmdb.business.ClassReOrderDTO;
import com.uino.bean.cmdb.business.ImportResultMessage;
import com.uino.bean.cmdb.query.ESCIClassSearchBean;
import com.uino.api.client.cmdb.ICIClassApiSvc;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
public class CIClassApiSvcLocal implements ICIClassApiSvc {

	@Autowired
	private ICIClassSvc classSvc;



	@Override
    public List<ClassNodeInfo> getClassTree() {
        return classSvc.getClassTree(BaseConst.DEFAULT_DOMAIN_ID);
	}

	@Override
	public List<ClassNodeInfo> getClassTree(Long domainId) {
		return classSvc.getClassTree(domainId);
	}

	@Override
    public ESCIClassInfo queryESClassInfoById(Long id) {
        return classSvc.queryESClassInfoById(id);
    }

    @Override
	public CcCiClassInfo queryClassInfoById(Long id) {
		return classSvc.queryClassInfoById(id);
	}

	@Override
    public List<CcCiClassInfo> queryCiClassInfoListBySearchBean(ESCIClassSearchBean bean) {
        return classSvc.queryCiClassInfoListBySearchBean(bean);
    }

    @Override
    public List<ESCIClassInfo> queryESCiClassInfoListBySearchBean(ESCIClassSearchBean bean) {
        return classSvc.queryESCiClassInfoListBySearchBean(bean);
    }

    @Override
	public Page<ESCIClassInfo> queryESCiClassInfoPageBySearchBean(ESCIClassSearchBean bean) {
		return classSvc.queryESCiClassInfoPageBySearchBean(bean);
	}

	@Override
	public Long saveOrUpdateCIClass(CcCiClassInfo record) {
		return classSvc.saveOrUpdate(record);
	}

	@Override
    public Long saveOrUpdateESCIClass(ESCIClassInfo record) {
        return classSvc.saveOrUpdate(record);
    }


	@Override
	public Long saveOrUpdateESCIClassExtra(ESCIClassInfo record) {
		return classSvc.saveOrUpdateExtra(record);
	}

	@Override
	public Integer saveOrUpdateBatch(Long domainId,List<CcCiClassInfo> clsInfos) {
		return classSvc.saveOrUpdateBatch(domainId,clsInfos);
	}

	@Override
	public Integer removeCIClassById(Long id) {
		return classSvc.deleteById(id);
	}

	@Override
	public ImportResultMessage importCiClassAttr(MultipartFile file, Long classId) {
		return classSvc.importCiClassAttr(file, classId);
	}

	@Override
	public List<CcCiClassInfo> queryClassByCdt(CCcCiClass cdt) {
		return classSvc.queryClassByCdt(cdt);
	}

	@Override
    public ResponseEntity<byte[]> exportClassAttrExcel(Set<Long> clsIds, Boolean isBatchTpl) {
        return classSvc.exportClassAttrExcel(clsIds, isBatchTpl);
	}

	@Override
	public boolean reOrder(ClassReOrderDTO reOrderDTO) {
		return classSvc.reOrder(reOrderDTO);
	}

	@Override
	public boolean initAllOrderNo() {
		return classSvc.initAllOrderNo(BaseConst.DEFAULT_DOMAIN_ID);
	}

	@Override
	public boolean initAllOrderNo(Long domainId) {
		return classSvc.initAllOrderNo(domainId);
	}

	@Override
	public List<CcCiClassInfo> queryCiClassInfoList(Long domainId, CCcCiClass cdt, String orders, Boolean isAsc) {
		return classSvc.queryCiClassInfoList(domainId, cdt, orders, isAsc);
	}

    @Override
    public List<ESCIClassInfo> getTargetAttrDefsByClassIds(Collection<Long> classIds) {
        return classSvc.getTargetAttrDefsByClassIds(BaseConst.DEFAULT_DOMAIN_ID, classIds);
    }

	@Override
	public List<ESCIClassInfo> getTargetAttrDefsByClassIds(Long domainId, Collection<Long> classIds) {
		return classSvc.getTargetAttrDefsByClassIds(domainId, classIds);
	}

	@Override
	public List<String> queryAttrDefGroupList() {
		return classSvc.queryAttrDefGroupList(BaseConst.DEFAULT_DOMAIN_ID);
	}

	@Override
	public List<String> queryAttrDefGroupList(Long domainId) {
		return classSvc.queryAttrDefGroupList(domainId);
	}

	@Override
	public String getHttpResourceSpace() {
		return classSvc.getHttpResourceSpace();
	}

	@Override
	public Boolean isShow3dAttribute() {
		return classSvc.isShow3dAttribute();
	}

	@Override
	public List<ESCIClassInfo> queryESClassInfoByIds(List<Long> classIds) {
		return classSvc.queryESClassInfoByIds(classIds);
	}

	@Override
	public List<ESCIClassInfo> queryEsClassInfoByDtClassIds(List<String> dtClassIds, Long domainId) {
		return classSvc.queryEsClassInfoByDtClassIds(dtClassIds, domainId);
	}
}
