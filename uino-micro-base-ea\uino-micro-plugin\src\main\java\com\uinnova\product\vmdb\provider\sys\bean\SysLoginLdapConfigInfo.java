package com.uinnova.product.vmdb.provider.sys.bean;

import com.uinnova.product.vmdb.comm.model.sys.SysLoginAuthConfig;

public class SysLoginLdapConfigInfo {

    private SysLoginAuthConfig record;

    private SysLoginLdapPropBean ldapProp;

    private SysLoginLdapPropDefs protoDefs;

    private String testUserName;

    private String testPassword;

    /**
     * 0表示测试连接,1表示测试用户DN,2表示测试Ldap户属性映射成用户属性
     * 
     * @since v5.19.0
     */
    private Integer testStep;

    public SysLoginAuthConfig getRecord() {
        return record;
    }

    public void setRecord(SysLoginAuthConfig record) {
        this.record = record;
    }

    public SysLoginLdapPropBean getLdapProp() {
        return ldapProp;
    }

    public void setLdapProp(SysLoginLdapPropBean ldapProp) {
        this.ldapProp = ldapProp;
    }

    public String getTestUserName() {
        return testUserName;
    }

    public void setTestUserName(String testUserName) {
        this.testUserName = testUserName;
    }

    public String getTestPassword() {
        return testPassword;
    }

    public void setTestPassword(String testPassword) {
        this.testPassword = testPassword;
    }

    public SysLoginLdapPropDefs getProtoDefs() {
        return protoDefs;
    }

    public void setProtoDefs(SysLoginLdapPropDefs protoDefs) {
        this.protoDefs = protoDefs;
    }

    public Integer getTestStep() {
        return testStep;
    }

    public void setTestStep(Integer testStep) {
        this.testStep = testStep;
    }
}
