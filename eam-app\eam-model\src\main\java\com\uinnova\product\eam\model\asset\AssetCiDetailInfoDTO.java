package com.uinnova.product.eam.model.asset;

import com.binary.framework.bean.annotation.Comment;
import com.uino.bean.cmdb.base.ESCIInfo;
import lombok.Data;

@Data
public class AssetCiDetailInfoDTO {
    @Comment("资产ci")
    private ESCIInfo esciInfo;
    @Comment("卡片id")
    private Long appSquareId;
    @Comment("分类标识")
    private String classCode;
    @Comment("卡片名称")
    private String cardName;
    private Integer source;

}
