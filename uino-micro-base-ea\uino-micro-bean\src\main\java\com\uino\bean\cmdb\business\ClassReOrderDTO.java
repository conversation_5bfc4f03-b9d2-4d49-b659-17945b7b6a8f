package com.uino.bean.cmdb.business;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * class拖动排序请求参数
 * @author: weixuesong
 * @create: 2020/08/05 14:21
 **/

@ApiModel(value="顺序类",description = "顺序信息")
@Data
public class ClassReOrderDTO {
    @ApiModelProperty(value="目录ID",example="11")
    private Long dirId;

    @ApiModelProperty(value="分类id",example="001")
    private Long classId;

    @ApiModelProperty(value="父类节点id",example="001")
    private Long parentId;
    /**
     * 原顺序
     */
    @ApiModelProperty(value="初始顺序",example="1")
    private int originOrder;
    /**
     * 新顺序
     */
    @ApiModelProperty(value = "新顺序",example = "2")
    private int newOrder;

    @ApiModelProperty(value = "所属域",example = "2")
    private Long domainId;
}
