package com.uino.bean.cmdb.business;

import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 
 * <AUTHOR>
 *
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value="导入目录信息类",description = "导入目录信息")
public class ImportDirMessage implements Serializable {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value="目录ID",example = "123")
	private Long dirId;

	@ApiModelProperty(value="目录名称",example = "fruit")
	private String dirName;

	@ApiModelProperty(value="导入数据总量",example = "0")
	@Builder.Default
	private Integer totalCount = 0;

	@ApiModelProperty(value="成功导入数据数量",example = "1")
	@Builder.Default
	private Integer successCount = 0;

	@Builder.Default
	@ApiModelProperty(value="忽略数据数量",example = "0")
	private Integer ignoreCount = 0;

	@ApiModelProperty(value="失败数据数量",example = "0")
	@Builder.Default
	private Integer failCount = 0;

	@ApiModelProperty(value="图标信息集合")
	private List<ImportImageMessage> imageMessages;

}
