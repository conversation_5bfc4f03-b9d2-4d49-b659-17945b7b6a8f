package com.uino.monitor.tp.alarm;

import jakarta.annotation.PostConstruct;

import com.uino.dao.AbstractESBaseDao;
import com.uino.bean.tp.base.ThresholdCountDTO;

/**
 * 该方法已迁移Redis
 * 记录阈值连续触发次数
 * @author: we<PERSON><PERSON><PERSON>
 * @create: 2020/06/15 15:51
 **/
//@Service
@Deprecated
public class ThresholdCountService extends AbstractESBaseDao<ThresholdCountDTO, Object> {
    @Override
    public String getIndex() {
		return "uino_threshold_count";
    }

    @Override
    public String getType() {
		return "uino_threshold_count";
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }
}
