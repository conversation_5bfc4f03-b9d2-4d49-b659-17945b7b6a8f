package com.uinnova.product.eam.db.impl;


import com.binary.framework.dao.DaoDefinition;

import com.uinnova.product.eam.comm.model.CVcDiagramEleVersion;
import com.uinnova.product.eam.comm.model.VcDiagramEleVersion;


/**
 * 视图元素版本表[VC_DIAGRAM_ELE_VERSION]数据访问对象定义实现
 */
public class VcDiagramEleVersionDaoDefinition implements DaoDefinition<VcDiagramEleVersion, CVcDiagramEleVersion> {


	@Override
	public Class<VcDiagramEleVersion> getEntityClass() {
		return VcDiagramEleVersion.class;
	}


	@Override
	public Class<CVcDiagramEleVersion> getConditionClass() {
		return CVcDiagramEleVersion.class;
	}


	@Override
	public String getTableName() {
		return "VC_DIAGRAM_ELE_VERSION";
	}


	@Override
	public boolean hasDataStatusField() {
		return false;
	}


	@Override
	public void setDataStatusValue(VcDiagramEleVersion record, int status) {
	}


	@Override
	public void setDataStatusValue(CVcDiagramEleVersion cdt, int status) {
	}


	@Override
	public void setCreatorValue(VcDiagramEleVersion record, String creator) {
	}


	@Override
	public void setModifierValue(VcDiagramEleVersion record, String modifier) {
	}


}


