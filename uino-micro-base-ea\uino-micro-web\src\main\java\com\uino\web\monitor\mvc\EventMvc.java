package com.uino.web.monitor.mvc;

import com.binary.jdbc.Page;
import com.uino.bean.monitor.base.ESMonEapEvent;
import com.uino.bean.monitor.buiness.CurrentEventQueryDto;
import com.uino.bean.monitor.buiness.EventQueryDto;
import com.uino.bean.permission.base.SysUser;
import com.uino.init.api.ApiResult;
import com.uino.init.api.ApiVersion;
import com.uino.monitor.event.service.IEventService;
import com.uino.util.sys.SysUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 告警mvc
 */
@ApiVersion(1)
@Api(value = "孪生画像-告警", tags = {"孪生全景"})
@RestController
@RequestMapping(value = "/monitor/event")
public class EventMvc {

    @Autowired
    private IEventService eventService;

    /**
     * 分页查询告警
     *
     * @param reqDto req
     */
    @PostMapping("searchEventPage")
    @ApiOperation(value = "分页查询告警", notes="- 根据传入条件, 分页查询告警数据, 数据孪生模式下 digitalTwinId 为必填字段")
    public ApiResult<Page<ESMonEapEvent>> searchEventPage(@RequestBody EventQueryDto reqDto) {
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        reqDto.setDomainId(currentUserInfo.getDomainId());
        Page<ESMonEapEvent> rep = eventService.searchEventPage(reqDto);
        return ApiResult.ok(this).data(rep);
    }

    @PostMapping("queryEventById")
    @ApiOperation(value = "查询一条告警", notes="参数为告警id")
    public ApiResult<ESMonEapEvent> queryEventById(@RequestBody String id) {
        return ApiResult.ok(this).data(eventService.hisEventById(id));
    }

    @PostMapping("searchCurrentEvent")
    @ApiOperation(value = "查询当前告警列表", notes="- 根据传入条件, 分页查询告警数据, 数据孪生模式下 digitalTwinId 为必填字段")
    public ApiResult<List<ESMonEapEvent>> searchCurrentEvent(@RequestBody CurrentEventQueryDto queryDto) {
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        queryDto.setDomainId(currentUserInfo.getDomainId());
        List<ESMonEapEvent> rep = eventService.listCurrentEventByParam(queryDto);
        return ApiResult.ok(this).data(rep);
    }
}
