package com.uinnova.product.eam.web.asset.bean;

import com.alibaba.fastjson.JSONObject;
import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.comm.bean.CcCiAttrDefTapGroupConfVO;
import lombok.Data;

import java.util.List;

/**
 * 资产详情属性配置
 * <AUTHOR>
 */
@Data
public class AssetDetailAttrConfVO {

    @Comment("主键id")
    private Long id;
    @Comment("")

    private Long appSquareConfId;
    @Comment("资产分类")
    private String ClassCode;
    @Comment("展示形式 1:文本，2:Tab页")
    private Integer type;
    @Comment("展示设置")
    private List<CcCiAttrDefTapGroupConfVO> showAttrs;
    @Comment("领域id")
    private Long domainId;
    @Comment("创建人")
    private String creator;
    @Comment("创建时间")
    private Long createTime;

}
