package com.uino.api.client.monitor.rpc;

import java.util.List;

import com.uino.dao.BaseConst;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.binary.core.io.Resource;
import com.binary.core.io.support.ByteArrayResource;
import com.binary.jdbc.Page;
import com.uino.api.client.monitor.IPerformanceApiSvc;
import com.uino.bean.chart.bean.UinoChartDataBean;
import com.uino.bean.monitor.base.SimulationRuleInfo;
import com.uino.bean.monitor.buiness.ImportPerformanceReqDto;
import com.uino.bean.monitor.buiness.PerformanceQueryDto;
import com.uino.bean.tp.base.FinalPerformanceDTO;
import com.uino.provider.feign.monitor.PerformanceFeign;

@Service
public class PerformanceSvcRpc implements IPerformanceApiSvc {

    @Autowired
    private PerformanceFeign feign;

    @Override
	public void importPerformance(MultipartFile excelFile, ImportPerformanceReqDto importDto) {
		feign.importPerformance(excelFile, importDto);
    }

    @Override
    public void importPerformance(ImportPerformanceReqDto importDto) {
        feign.importPerformance(importDto);
    }

    @Override
    public Resource exportPerformanceTemplate(Long objId, Integer objType) {
        ResponseEntity<byte[]> httpResponse = feign.exportPerformanceTemplate(BaseConst.DEFAULT_DOMAIN_ID,objId, objType);
        Resource returnVal = new ByteArrayResource(httpResponse.getBody(),
                httpResponse.getHeaders().getFirst("fileName"));
        return returnVal;
    }

    @Override
    public Resource exportPerformanceTemplate(Long domainId, Long objId, Integer objType) {
        ResponseEntity<byte[]> httpResponse = feign.exportPerformanceTemplate(domainId,objId, objType);
        Resource returnVal = new ByteArrayResource(httpResponse.getBody(),
                httpResponse.getHeaders().getFirst("fileName"));
        return returnVal;
    }

    @Override
	public Page<FinalPerformanceDTO> searchPerformance(PerformanceQueryDto queryDto) {
        return feign.searchPerformance(queryDto);
    }

    @Override
	public UinoChartDataBean<List<Double>> searchPerformanceGraph(PerformanceQueryDto queryDto) {
		return feign.searchPerformanceGraph(queryDto);
	}

	@Override
	public Page<FinalPerformanceDTO> searchNoCiPerformance(PerformanceQueryDto queryDto) {
        return feign.searchNoCiPerformance(queryDto);
    }

    @Override
    public List<String> getPerfDataLabel(Long classId) {
        return feign.getPerfDataLabel(classId);
    }

	@Override
	public void simulationPerformance(SimulationRuleInfo bean) {
		feign.simulationPerformance(bean);
	}
}
