package com.uino.provider.server.web.sys.mvc;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.binary.core.io.Resource;
import com.uino.service.sys.microservice.ISysSvc;
import com.uino.provider.feign.sys.SysFeign;

@RestController
@RequestMapping("feign/sys")
public class SysFeignMvc implements SysFeign {

    @Autowired
    private ISysSvc svc;

    @Override
    public String uploadFile(MultipartFile file) {
        // TODO Auto-generated method stub
        return svc.uploadFile(file);
    }

    @Override
    public Resource downloadFile(String filePath) {
        return svc.downloadFile(filePath);
    }

    @Override
    public String uploadFile(byte[] fileBytes, String fileName) {
        // TODO Auto-generated method stub
        return svc.uploadFile(fileBytes, fileName);
    }
}
