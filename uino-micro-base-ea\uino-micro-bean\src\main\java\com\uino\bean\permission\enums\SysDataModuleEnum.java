package com.uino.bean.permission.enums;

import com.binary.framework.bean.annotation.Comment;
import com.binary.framework.exception.ServiceException;

/**
 *  权限管理 - 数据权限枚举项
 */
public enum SysDataModuleEnum {

    CI_CLASS("CICLASS", "对象分类"),
    CI_TAG("CITAG", "标签"),
    A_BUTTON("BUTTON", "按钮");

    @Comment("数据权限模块标识")
    private final String dataModuleCode;

    @Comment("数据权限模块名称")
    private final String dataModuleName;

    SysDataModuleEnum(String dataModuleCode, String dataModuleName) {
        this.dataModuleCode = dataModuleCode;
        this.dataModuleName = dataModuleName;
    }

    public String getDataModuleCode() {
        return dataModuleCode;
    }

    public String getDataModuleName() {
        return dataModuleName;
    }

    public static SysDataModuleEnum getType(String code) {
        switch (code) {
            case "CICLASS":
                return CI_CLASS;
            case "CITAG":
                return CI_TAG;
            case "BUTTON":
                return A_BUTTON;
            default:
                throw new ServiceException("数据模块【" + code + "】功能未实现");
        }
    }

}
