package com.uino.cmdb.ci_rlt.mvc;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.elasticsearch.index.query.QueryBuilders;
import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uino.StartBaseWebAppliaction;
import com.uino.dao.cmdb.ESCIClassSvc;
import com.uino.dao.cmdb.ESCIRltSvc;
import com.uino.dao.cmdb.ESCISvc;
import com.uino.dao.cmdb.ESRltClassSvc;
import com.uino.bean.cmdb.base.ESCIClassInfo;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.base.ESCIRltInfo;

import lombok.extern.slf4j.Slf4j;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = { StartBaseWebAppliaction.class }, webEnvironment = WebEnvironment.RANDOM_PORT)
@Slf4j
@ActiveProfiles("provider-local")
public class ExportCiRltTest {
	@Autowired
	private TestRestTemplate restTemplate;
	@MockBean
	private ESRltClassSvc esRltClassSvc;
	@MockBean
	private ESCIRltSvc esCiRltSvc;
	@MockBean
	private ESCIClassSvc esClassSvc;
    @MockBean
    private ESCISvc esCiSvc;
	private static String testUrl;

	@BeforeClass
	public static void initCls() {
		ExportCiRltTest.testUrl = "/cmdb/ciRlt/exportCiRlt";
	}

	@Before
	public void before() {
		Mockito.when(esCiRltSvc.countByCondition(Mockito.any())).thenReturn(10000L);

		Set<Long> rltClsIds = Collections.singleton(1L);
		List<ESCIClassInfo> rltClassInfos = new LinkedList<>();
		ESCIClassInfo rltClsInfo = new ESCIClassInfo();
		rltClassInfos.add(rltClsInfo);
		CcCiAttrDef def = new CcCiAttrDef();
		def.setProName("属性a");
		def.setProStdName("属性A");
        rltClsInfo.setCcAttrDefs(Collections.singletonList(def));
		rltClsInfo.setId(1L);
		rltClsInfo.setClassCode("rltClsCode");
		rltClsInfo.setClassName("rltClsName");
		rltClsInfo.setClassStdCode("RLTCLSCODE");
		Mockito.when(esRltClassSvc.getListByQuery(Mockito.eq(QueryBuilders.termsQuery("id", rltClsIds))))
				.thenReturn(rltClassInfos);

		String scrollId = "scrollId";
		Map<String, Page<ESCIRltInfo>> resultMap = new HashMap<>();
		ESCIRltInfo rlt = new ESCIRltInfo();
		rlt.setSourceCiId(1L);
		rlt.setCiCode("sCiCode");
		rlt.setSourceClassId(1L);
		rlt.setTargetCiId(2L);
		rlt.setTargetCiCode("tCiCode");
		rlt.setTargetClassId(2L);
		rlt.setAttrs(Collections.singletonMap("属性A", "我是属性A啊~"));
		Page<ESCIRltInfo> result = new Page<>(1, 1, 1, 1, Collections.singletonList(rlt));
		resultMap.put(scrollId, result);
		Mockito.when(esCiRltSvc.getScrollByQuery(Mockito.anyInt(), Mockito.anyInt(), Mockito.any(), Mockito.anyString(),
				Mockito.anyBoolean())).thenReturn(resultMap);

		List<ESCIClassInfo> clsInfos = new LinkedList<>();
		ESCIClassInfo sCls = new ESCIClassInfo();
		clsInfos.add(sCls);
		sCls.setId(1L);
		sCls.setClassCode("sClsCode");
		sCls.setClassStdCode("SCLSCODE");
		sCls.setClassName("sClsName");
		ESCIClassInfo tCls = new ESCIClassInfo();
		clsInfos.add(tCls);
		tCls.setId(2L);
		tCls.setClassCode("tClsCode");
		tCls.setClassStdCode("TCLSCODE");
		tCls.setClassName("tClsName");

		Page<ESCIClassInfo> clsPage = new Page<>(1, 99999, 2, 1, clsInfos);
		Mockito.when(esClassSvc.getListByQuery(Mockito.anyInt(), Mockito.eq(99999), Mockito.any())).thenReturn(clsPage);
		Mockito.when(esClassSvc.getListByQuery(Mockito.any())).thenReturn(clsPage.getData());

		Mockito.when(esCiRltSvc.getListByScroll(Mockito.eq(scrollId))).thenReturn(null);
		Mockito.when(esCiRltSvc.clearScroll(Mockito.eq(scrollId))).thenReturn(1);
		
        Page<ESCIInfo> ciPage = new Page<>();
        List<ESCIInfo> data = new ArrayList<ESCIInfo>();
        ESCIInfo esciInfo = new ESCIInfo();
        esciInfo.setId(1L);
        esciInfo.setCiPrimaryKey("[\"sourcePK\"]");
        ESCIInfo esciInfo2 = new ESCIInfo();
        esciInfo2.setId(2L);
        esciInfo2.setCiPrimaryKey("[\"sourcePK2\"]");
        data.add(esciInfo);
        data.add(esciInfo2);
        ciPage.setData(data);
        Mockito.when(esCiSvc.getListByQuery(Mockito.anyInt(), Mockito.anyInt(), Mockito.any())).thenReturn(ciPage);
	}

	@Test
	public void test01() {
		Set<Long> reqBean = Collections.singleton(1L);
		ResponseEntity<byte[]> response = restTemplate.postForEntity(testUrl, reqBean, byte[].class, new Object[] {});
		Assert.assertTrue(response.getStatusCodeValue() == 200);
	}

	@Test
	public void test02() {
		Mockito.when(esCiRltSvc.countByCondition(Mockito.any())).thenReturn(1L);
		Set<Long> reqBean = Collections.singleton(1L);
		ResponseEntity<byte[]> response = restTemplate.postForEntity(testUrl, reqBean, byte[].class, new Object[] {});
		Assert.assertTrue(response.getStatusCodeValue() == 200);
	}
}
