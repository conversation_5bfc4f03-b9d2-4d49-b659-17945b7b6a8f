package com.uinnova.product.vmdb.comm.model.rlt;

import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;

/**
 * 
 * <AUTHOR>
 *
 */
@Comment("CI关系表[CC_CI_RLT]")
public class CcCiRlt implements EntityBean {
    private static final long serialVersionUID = 1L;

    @Comment("ID[ID]")
    private Long id;

    @Comment("CI代码[CI_CODE]")
    private String ciCode;

    @Comment("CI描述[CI_DESC]")
    private String ciDesc;

    @Comment("所属分类[CLASS_ID]")
    private Long classId;

    @Comment("关系级别[CI_RLT_LVL]    关系级别:1=1级 2=2级 3=3级 ... n=n级")
    private Integer ciRltLvl;

    @Comment("来源[SOURCE_ID]")
    private Long sourceId;

    @Comment("所有者ID[OWNER_ID]")
    private String ownerCode;

    @Comment("所属组织[ORG_ID]")
    private Long orgId;

    @Comment("源CI_ID[SOURCE_CI_ID]")
    private Long sourceCiId;

    @Comment("源CI代码[SOURCE_CI_CODE]")
    private String sourceCiCode;

    @Comment("源CI分类[SOURCE_CLASS_ID]")
    private Long sourceClassId;

    @Comment("目标CI_ID[TARGET_CI_ID]")
    private Long targetCiId;

    @Comment("目标CI代码[TARGET_CI_CODE]")
    private String targetCiCode;

    @Comment("目标CI分类[TARGET_CLASS_ID]")
    private Long targetClassId;

    @Comment("备用_1[CUSTOM_1]")
    private String custom1;

    @Comment("备用_2[CUSTOM_2]")
    private String custom2;

    @Comment("备用_3[CUSTOM_3]")
    private String custom3;

    @Comment("备用_4[CUSTOM_4]")
    private String custom4;

    @Comment("备用_5[CUSTOM_5]")
    private String custom5;

    @Comment("备用_6[CUSTOM_6]")
    private String custom6;

    @Comment("CI关系属性[ATTRS_STR]")
    private String attrsStr;

    @Comment("所属域[DOMAIN_ID]")
    private Long domainId;

    @Comment("数据状态[DATA_STATUS]    数据状态:1-正常 0-删除")
    private Integer dataStatus;

    @Comment("创建人[CREATOR]")
    private String creator;

    @Comment("修改人[MODIFIER]")
    private String modifier;

    @Comment("创建时间[CREATE_TIME]    创建时间:yyyyMMddHHmmss")
    private Long createTime;

    @Comment("修改时间[MODIFY_TIME]    修改时间:yyyyMMddHHmmss")
    private Long modifyTime;

    @Comment("所属视图ID[DIAGRAM_ID]")
    private String diagramId;

    @Comment("所属视图下SHEET_ID[SHEET_ID]")
    private String sheetId;

    @Comment("运行库版本[PUBLIC_VERSION]")
    private Long publicVersion;

    @Comment("私有库版本[LOCAL_VERSION]")
    private Long localVersion;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCiCode() {
        return this.ciCode;
    }

    public void setCiCode(String ciCode) {
        this.ciCode = ciCode;
    }

    public String getCiDesc() {
        return this.ciDesc;
    }

    public void setCiDesc(String ciDesc) {
        this.ciDesc = ciDesc;
    }

    public Long getClassId() {
        return this.classId;
    }

    public void setClassId(Long classId) {
        this.classId = classId;
    }

    public Integer getCiRltLvl() {
        return this.ciRltLvl;
    }

    public void setCiRltLvl(Integer ciRltLvl) {
        this.ciRltLvl = ciRltLvl;
    }

    public Long getSourceId() {
        return this.sourceId;
    }

    public void setSourceId(Long sourceId) {
        this.sourceId = sourceId;
    }

    public String getOwnerCode() {
        return ownerCode;
    }

    public void setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
    }

    public Long getOrgId() {
        return this.orgId;
    }

    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    public Long getSourceCiId() {
        return this.sourceCiId;
    }

    public void setSourceCiId(Long sourceCiId) {
        this.sourceCiId = sourceCiId;
    }

    public String getSourceCiCode() {
        return this.sourceCiCode;
    }

    public void setSourceCiCode(String sourceCiCode) {
        this.sourceCiCode = sourceCiCode;
    }

    public Long getSourceClassId() {
        return this.sourceClassId;
    }

    public void setSourceClassId(Long sourceClassId) {
        this.sourceClassId = sourceClassId;
    }

    public Long getTargetCiId() {
        return this.targetCiId;
    }

    public void setTargetCiId(Long targetCiId) {
        this.targetCiId = targetCiId;
    }

    public String getTargetCiCode() {
        return this.targetCiCode;
    }

    public void setTargetCiCode(String targetCiCode) {
        this.targetCiCode = targetCiCode;
    }

    public Long getTargetClassId() {
        return this.targetClassId;
    }

    public void setTargetClassId(Long targetClassId) {
        this.targetClassId = targetClassId;
    }

    public String getCustom1() {
        return this.custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }

    public String getCustom2() {
        return this.custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }

    public String getCustom3() {
        return this.custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }

    public String getCustom4() {
        return this.custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }

    public String getCustom5() {
        return this.custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }

    public String getCustom6() {
        return this.custom6;
    }

    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }

    public String getAttrsStr() {
        return attrsStr;
    }

    public void setAttrsStr(String attrsStr) {
        this.attrsStr = attrsStr;
    }

    public Long getDomainId() {
        return this.domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }

    public Integer getDataStatus() {
        return this.dataStatus;
    }

    public void setDataStatus(Integer dataStatus) {
        this.dataStatus = dataStatus;
    }

    public String getCreator() {
        return this.creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getModifier() {
        return this.modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public Long getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getModifyTime() {
        return this.modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getDiagramId() {
        return diagramId;
    }

    public void setDiagramId(String diagramId) {
        this.diagramId = diagramId;
    }

    public String getSheetId() {
        return sheetId;
    }

    public void setSheetId(String sheetId) {
        this.sheetId = sheetId;
    }

    public Long getPublicVersion() {
        return publicVersion;
    }

    public void setPublicVersion(Long publicVersion) {
        this.publicVersion = publicVersion;
    }

    public Long getLocalVersion() {
        return localVersion;
    }

    public void setLocalVersion(Long localVersion) {
        this.localVersion = localVersion;
    }
}
