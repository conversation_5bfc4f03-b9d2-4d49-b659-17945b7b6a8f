package com.uinnova.product.eam.comm.model.es;

import com.alibaba.fastjson.JSONObject;
import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.util.List;

/**
 * 数据筛选
 */
@Data
public class AssetAttrDataScreen {

    @Comment("主键id")
    private Long id;


    @Comment("分类id")
    private Long classId;

    @Comment("关联绑定项id")
    private Long bindId;

    @Comment("筛选信息")
    private List<List<JSONObject>> screenInfo;

    @Comment("领域id")
    private Long domainId;

    @Comment("创建人")
    private String creator;

    @Comment("创建时间")
    private Long createTime;

    @Comment("修改人")
    private String modifier;

    @Comment("修改时间")
    private Long modifyTime;

}
