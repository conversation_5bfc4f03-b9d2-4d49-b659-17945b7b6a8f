package com.uino.provider.feign.sys;

import java.util.Map;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import com.uino.bean.sys.base.Logo;
import com.uino.provider.feign.config.BaseFeignConfig;

@Component
@FeignClient(name = "${feign-server-name:tarsier-platform}", path = "${feign-server-path:tarsier-platform}/feign/sys/logo", configuration = {
        BaseFeignConfig.class})
public interface LogoFeign {

    /**
     * 获取logo信息
     * 
     * @return {logoType:{logoObj}}
     */
    @PostMapping("getLogos")
    Map<String, Logo> getLogos();

    /**
     * 修改logo
     * 
     * @param logoType
     *            要修改的logo类型
     * @param file
     *            新logo文件
     * @return 修改后的logo信息
     */
    @PostMapping("updateLogo")
    Map<String, Logo> updateLogo(@RequestParam(name = "logoType") String logoType,
                                 @RequestPart(name = "file") MultipartFile file);

    /**
     * 删除logo
     * 
     * @param logoType
     * @return
     */
    @PostMapping("deleteLogo")
    Map<String, Logo> deleteLogo( @RequestParam(name = "logoType") String logoType);

    /**
     * 通过图片地址更新系统Logo
     * @param logoType 类型
     * @param path 路径
     * @return logo信息
     */
    @PostMapping("updateLogoByPath")
    Map<String, Logo> updateLogoByPath(@RequestParam(name = "logoType") String logoType, @RequestParam(name = "path") String path, @RequestParam(name = "fileId") Long fileId);
}
