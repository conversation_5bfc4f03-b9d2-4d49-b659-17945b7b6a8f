package com.uinnova.product.eam.web.integration.bean;

import com.binary.framework.bean.annotation.Comment;
import com.binary.jdbc.Page;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
public class VcCiTableInfo implements Serializable {

	private static final long serialVersionUID = 1L;

	@Comment("CI分类集合")
	private List<VcCiClassTabs> ciClassTabs;

	@Comment("标题")
	private List<String> ciAttrHeads;

	@Comment("CI属性集合")
	private Page<Map<String,String>> attrValuePage;

	@Data
	public static class VcCiClassTabs{
		@Comment("ci分类id")
		private Long classId;

		@Comment("ci分类名称")
		private String className;

		@Comment("被选中")
		private boolean selectEnable;

	}
}
