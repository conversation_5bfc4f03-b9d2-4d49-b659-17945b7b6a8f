package com.uinnova.product.eam.db.impl;


import com.binary.framework.dao.DaoDefinition;

import com.uinnova.product.eam.comm.model.CVcDiagramEle;
import com.uinnova.product.eam.comm.model.VcDiagramEle;


/**
 * 视图元素表[VC_DIAGRAM_ELE]数据访问对象定义实现
 */
public class VcDiagramEleDaoDefinition implements DaoDefinition<VcDiagramEle, CVcDiagramEle> {


	@Override
	public Class<VcDiagramEle> getEntityClass() {
		return VcDiagramEle.class;
	}


	@Override
	public Class<CVcDiagramEle> getConditionClass() {
		return CVcDiagramEle.class;
	}


	@Override
	public String getTableName() {
		return "VC_DIAGRAM_ELE";
	}


	@Override
	public boolean hasDataStatusField() {
		return false;
	}


	@Override
	public void setDataStatusValue(VcDiagramEle record, int status) {
	}


	@Override
	public void setDataStatusValue(CVcDiagramEle cdt, int status) {
	}


	@Override
	public void setCreatorValue(VcDiagramEle record, String creator) {
	}


	@Override
	public void setModifierValue(VcDiagramEle record, String modifier) {
	}


}


