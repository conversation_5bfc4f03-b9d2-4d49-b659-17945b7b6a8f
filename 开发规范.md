# EAM企业架构管理系统 - 开发规范

## 1. 概述

本文档规定了EAM（企业架构管理）系统的开发规范，旨在确保代码质量、提高开发效率、保证系统的可维护性和可扩展性。本系统基于最新的Spring Boot 3.x技术栈，支持AI智能分析、专题分析、数据建模等先进功能。

## 2. 技术栈规范

### 2.1 核心技术栈
- **Java版本**: JDK 17
- **Spring Boot**: 3.4.5
- **Spring Cloud**: 2024.0.1
- **数据库**: MySQL 8.0.33
- **搜索引擎**: Elasticsearch 7.17.14/7.17.28
- **工作流引擎**: Flowable
- **构建工具**: Maven 3.x
- **容器化**: Docker
- **服务注册**: Nacos 2023.0.3.2

### 2.2 关键依赖版本
- **Lombok**: 1.18.30
- **MyBatis**: 3.5.13
- **MyBatis Spring**: 3.0.3
- **Tomcat**: 11.0.6
- **Log4j2**: 2.24.0
- **OpenFeign**: 4.2.1
- **Netty**: 4.1.121.Final
- **Guava**: 33.4.8-jre
- **Jakarta Validation**: 3.0.2
- **Jakarta Servlet**: 6.1.0

### 2.3 依赖管理原则
- 使用Maven进行依赖管理
- 统一在父POM中管理版本号
- 禁止在子模块中重复定义已在父POM中管理的依赖版本
- 使用Spring Cloud依赖管理BOM统一版本控制
- 优先使用Jakarta EE规范替代Java EE

## 3. 项目结构规范

### 3.1 模块划分原则
```
eam-app/                    # 主应用模块
├── eam-api/               # API接口定义
├── eam-base/              # 基础组件和配置
├── eam-db/                # 数据库相关
├── eam-feign-client/      # Feign客户端
├── eam-feign-server/      # Feign服务端
├── eam-model/             # 数据模型和DTO
├── eam-service/           # 业务服务层
├── eam-web/               # Web控制层
├── eam-workable/          # 工作流模块
└── eam-workable-feign-client/ # 工作流Feign客户端

uino-micro-base-ea/        # 微服务基础框架
├── uino-micro-api/        # 外部调用API
├── uino-micro-bean/       # Bean定义
├── uino-micro-comm/       # 通用组件
├── uino-micro-dao/        # 数据访问层
├── uino-micro-ed/         # 扩展组件
├── uino-micro-feign-client/ # Feign客户端
├── uino-micro-feign-server/ # Feign服务端
├── uino-micro-monitor/    # 监控组件
├── uino-micro-plugin/     # 插件组件
├── uino-micro-service/    # 服务实现
├── uino-micro-util/       # 工具类
└── uino-micro-web/        # Web服务
```

### 3.2 包命名规范
- 基础包名：`com.uinnova.product.eam`
- 控制层：`com.uinnova.product.eam.web`
- 服务层：`com.uinnova.product.eam.service`
- 数据访问层：`com.uinnova.product.eam.dao`
- 模型层：`com.uinnova.product.eam.model`
- AI服务：`com.uinnova.product.eam.service.ai`
- 工作流：`com.uinnova.product.eam.workable`

## 4. 代码规范

### 4.1 命名规范

#### 4.1.1 类命名
- 类名使用大驼峰命名法（PascalCase）
- 控制器类以`Mvc`或`Controller`结尾：`DifyAssetApiMvc`
- 服务接口以`Svc`或`Service`结尾：`IDifyAssetApiService`
- 服务实现类以`SvcImpl`或`ServiceImpl`结尾：`DifyAssetService`
- 数据传输对象以`Dto`结尾：`ArchitecturalDTO`
- 值对象以`Vo`结尾：`AIDrawVo`
- 配置类以`Config`结尾：`EnvConfig`

#### 4.1.2 方法命名
- 方法名使用小驼峰命名法（camelCase）
- 查询方法以`get`、`find`、`query`开头
- 保存方法以`save`、`create`开头
- 更新方法以`update`、`modify`开头
- 删除方法以`delete`、`remove`开头
- AI相关方法以`auto`开头：`autoDataAnalysis`

#### 4.1.3 变量命名
- 变量名使用小驼峰命名法
- 常量使用全大写，单词间用下划线分隔
- 布尔类型变量以`is`、`has`、`can`开头

### 4.2 注释规范

#### 4.2.1 类注释
```java
/**
 * AI EA资产相关api接口
 * 
 * <AUTHOR>
 * @version 版本号
 * @since 创建日期
 */
public interface IDifyAssetApiService {
}
```

#### 4.2.2 方法注释
```java
/**
 * AI绘图-视图数据解析
 * 
 * @param param 输入参数
 * @return 解析结果
 */
RemoteResult autoDataAnalysis(String param);
```

### 4.3 代码质量规范

#### 4.3.1 基本要求
- 每行代码不超过140个字符
- 方法长度不超过50行
- 方法参数不超过5个
- 类文件长度不超过1500行
- 禁止使用`System.out.println`进行日志输出
- 使用`@Slf4j`注解进行日志记录

#### 4.3.2 异常处理
- 使用具体的异常类型，避免使用`Exception`
- 异常信息要明确，便于问题定位
- 不允许捕获异常后不处理
- AI服务调用异常要有明确的错误处理机制

#### 4.3.3 日志规范
- 使用SLF4J + Log4j2进行日志记录
- 日志级别：ERROR、WARN、INFO、DEBUG
- 关键业务操作必须记录日志
- AI模型调用要记录请求和响应日志

## 5. 开发流程规范

### 5.1 分支管理
- `master`：主分支，用于生产环境
- `develop`：开发分支，用于集成测试
- `feature/*`：功能分支，用于新功能开发
- `hotfix/*`：热修复分支，用于紧急修复

### 5.2 代码提交规范
- 提交信息格式：`[类型] 简短描述`
- 类型：feat（新功能）、fix（修复）、docs（文档）、style（格式）、refactor（重构）、test（测试）、ai（AI功能）

### 5.3 代码审查
- 所有代码必须经过Code Review
- 使用Checkstyle进行代码规范检查
- 单元测试覆盖率不低于70%
- AI相关功能需要额外的安全性审查

## 6. 数据库规范

### 6.1 表命名规范
- 表名使用小写字母，单词间用下划线分隔
- 表名要有明确的业务含义
- 系统表以`sys_`开头
- 业务表以业务模块缩写开头
- AI相关表以`ai_`开头

### 6.2 字段命名规范
- 字段名使用小写字母，单词间用下划线分隔
- 主键字段统一命名为`id`
- 创建时间字段命名为`create_time`
- 更新时间字段命名为`update_time`
- 删除标记字段命名为`is_deleted`

## 7. 接口规范

### 7.1 RESTful API规范
- 使用HTTP动词表示操作：GET（查询）、POST（创建）、PUT（更新）、DELETE（删除）
- URL使用名词，避免使用动词
- 使用统一的响应格式
- AI接口路径以`/ai/api/`开头

### 7.2 响应格式
```json
{
  "success": true,
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": 1234567890
}
```

### 7.3 AI接口特殊规范
- AI接口需要支持异步调用
- 大模型调用需要设置合理的超时时间
- AI接口响应需要包含置信度信息
- 支持流式响应处理

## 8. 测试规范

### 8.1 单元测试
- 使用JUnit进行单元测试
- 测试类命名：被测试类名 + Test
- 测试方法命名：test + 被测试方法名 + 测试场景
- AI功能需要模拟测试数据

### 8.2 集成测试
- 使用Spring Boot Test进行集成测试
- 测试数据库使用H2内存数据库
- 测试完成后清理测试数据
- AI服务集成测试需要使用Mock服务

## 9. 部署规范

### 9.1 环境配置
- 开发环境：dev
- 测试环境：test
- 生产环境：prod

### 9.2 配置管理
- 使用Nacos进行配置管理
- 敏感信息使用加密存储
- 不同环境使用不同的配置文件
- AI模型配置独立管理

## 10. 安全规范

### 10.1 认证授权
- 使用OAuth2进行认证
- 接口权限控制使用注解方式
- 敏感操作需要二次验证
- AI接口需要特殊的访问控制

### 10.2 数据安全
- 数据库连接使用加密传输
- 敏感数据存储加密
- 定期备份重要数据
- AI训练数据需要脱敏处理

## 11. 性能规范

### 11.1 数据库优化
- 合理使用索引
- 避免N+1查询问题
- 大数据量查询使用分页
- Elasticsearch查询优化

### 11.2 缓存策略
- 使用Redis进行数据缓存
- 合理设置缓存过期时间
- 避免缓存雪崩和穿透
- AI模型结果缓存策略

## 12. 监控规范

### 12.1 应用监控
- 使用Spring Boot Actuator进行健康检查
- 关键业务指标监控
- 异常告警机制
- AI服务调用监控

### 12.2 日志监控
- 集中化日志管理
- 错误日志实时告警
- 性能日志分析
- AI模型调用链路追踪

---

**注意**: 本规范为强制性规范，所有开发人员必须严格遵守。如有疑问或建议，请及时反馈给技术负责人。
