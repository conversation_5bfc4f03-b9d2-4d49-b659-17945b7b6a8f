package com.uino.dao.permission;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import jakarta.annotation.PostConstruct;

import com.uino.dao.util.ESUtil;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.uino.dao.AbstractESBaseDao;
import com.uino.dao.ESConst;
import com.uino.util.sys.CommonFileUtil;
import com.uino.bean.permission.base.OauthResourceDetail;

import lombok.extern.slf4j.Slf4j;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
@Slf4j
public class ESOauthResourceSvc extends AbstractESBaseDao<OauthResourceDetail, JSONObject> {
	@Override
	public String getIndex() {
		// TODO Auto-generated method stub
		return ESConst.INDEX_OAUTH_RESOURCE;
	}

	@Override
	public String getType() {
		// TODO Auto-generated method stub
		return ESConst.INDEX_OAUTH_RESOURCE;
	}

	@PostConstruct
	public void init() {
		List<OauthResourceDetail> datas = CommonFileUtil.getData("/initdata/uino_oauth_resource.json",
				OauthResourceDetail.class);
		super.initIndex(datas);
		List<OauthResourceDetail> listByQueryScroll = getListByQueryScroll(QueryBuilders.matchAllQuery());
		Map<String, Long> resourceIdMap = listByQueryScroll.stream().collect(Collectors.toMap(OauthResourceDetail::getName, OauthResourceDetail::getId));
		for (OauthResourceDetail data : datas) {
			Long id = resourceIdMap.get(data.getName());
			if(id!=null){
				data.setId(id);
			}else {
				data.setId(ESUtil.getUUID());
			}
		}
		saveOrUpdateBatch(datas);
	}

}
