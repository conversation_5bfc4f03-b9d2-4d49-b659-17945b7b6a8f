package com.uino.service.permission.data;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.binary.core.util.BinaryUtils;
import com.uino.bean.cmdb.base.ESCIClassInfo;
import com.uino.bean.permission.base.SysRoleDataModuleRlt;
import com.uino.bean.permission.business.UserInfo;
import com.uino.bean.permission.enums.PermissionOperateEnum;
import com.uino.dao.cmdb.ESCIClassSvc;
import com.uino.service.permission.microservice.IRoleSvc;
import com.uino.service.permission.microservice.IUserSvc;
import com.uino.util.sys.SysUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.lang.annotation.Annotation;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 数据权限控制
 */
@Aspect
@Slf4j
@Component
public class DataPermissionAspect {

    @Resource
    private IRoleSvc roleSvc;

    @Resource
    private ESCIClassSvc classSvc;

    @Resource
    private TagPermissionProcessor tagPermissionProcessor;

    @Resource
    private IUserSvc userSvc;

    private static final Integer CHECK = 1;

    @Around("execution(* *.*(.., @CIViewPermission (*), ..))")
    public Object viewDataPermission(ProceedingJoinPoint joinPoint) throws Throwable {
        // 遍历参数注解数组，寻找带有 @CIViewPermission 注解的参数
        CIViewPermission permission = getPermissionParam(joinPoint, CIViewPermission.class);
        Object[] args = joinPoint.getArgs();
        // 执行目标方法，并获取返回值
        if(permission == null){
            return joinPoint.proceed(args);
        }
        JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(args[0]));
        Boolean usePermission = jsonObject.getBoolean("permission");
        if(usePermission == null || !usePermission){
            return joinPoint.proceed(args);
        }
        String ownerCode = jsonObject.getString("ownerCode");
        Long userId;
        if(BinaryUtils.isEmpty(ownerCode)){
            userId = SysUtil.getCurrentUserInfo().getId();
        }else{
            UserInfo user = userSvc.getUserInfoByLoginCode(ownerCode);
            userId = user.getId();
        }
        List<SysRoleDataModuleRlt> dataModules = roleSvc.getRoleByUserAndCode(userId, "CICLASS");
        List<Long> classIds = dataModules.stream().filter(e -> CHECK.equals(e.getIssee())).map(e -> Long.parseLong(e.getDataValue())).distinct().collect(Collectors.toList());
//        List<ESTagRuleInfo> ruleList = tagPermissionProcessor.getTagRulePermission(classIds, PermissionOperateEnum.VIEW);
        List<Long> tagIds = tagPermissionProcessor.getTagIds(classIds, userId, PermissionOperateEnum.VIEW);
        if(permission.object()){
            List<Long> classParams = JSON.parseArray(jsonObject.getString(permission.classFieldName()), Long.class);
            if(!CollectionUtils.isEmpty(classParams)){
                //取交集
                classIds = classIds.stream().filter(classParams::contains).collect(Collectors.toList());
            }
            if(!CollectionUtils.isEmpty(tagIds) && !CollectionUtils.isEmpty(classIds)){
//                Set<String> tagCiCodes = tagPermissionProcessor.getCIInfoListByTag(ruleList, new ArrayList<>(classIds), permission.libType());
//                List<String> codeParams = JSON.parseArray(jsonObject.getString(permission.ciFieldName()), String.class);
//                if(!CollectionUtils.isEmpty(codeParams)){
//                    tagCiCodes = tagCiCodes.stream().filter(codeParams::contains).collect(Collectors.toSet());
//                }
                jsonObject.put(permission.tagFieldName(), tagIds);
            }
            if(CollectionUtils.isEmpty(classIds)){
                classIds.add(-9999999999L);
            }
            jsonObject.put(permission.classFieldName(), classIds);
            args[0] = JSON.parseObject(JSON.toJSONString(jsonObject), args[0].getClass());
        }else{
            List<Long> params = JSON.parseArray(args[0].toString(), Long.class);
            params.removeAll(classIds);
            if(CollectionUtils.isEmpty(params)){
                params = classIds;
            }
            args[0] = params;
        }
        return joinPoint.proceed(args);
    }

    /**
     * 获取方法参数注解
     */
    private <T extends Annotation> T getPermissionParam(ProceedingJoinPoint joinPoint, Class<T> annotationClass){
        // 获取方法签名
        Signature signature = joinPoint.getSignature();
        // 强制转换为 MethodSignature
        MethodSignature methodSignature = (MethodSignature) signature;
        // 获取方法参数上的所有注解数组
        Annotation[][] parameterAnnotations = methodSignature.getMethod().getParameterAnnotations();
        // 遍历参数注解数组，寻找带有 @CIViewPermission 注解的参数
        for (Annotation[] parameterAnnotation : parameterAnnotations) {
            for (Annotation annotation : parameterAnnotation) {
                if (annotationClass.isInstance(annotation)) {
                    // 找到带有 @CIViewPermission 注解的参数
                    return annotationClass.cast(annotation);
                }
            }
        }
        return null;
    }

    @Around("execution(* *.*(.., @CIEditPermission (*), ..))")
    public Object editDataPermission(ProceedingJoinPoint joinPoint) throws Throwable {
        // 遍历参数注解数组，寻找带有 @CIEditPermission 注解的参数
        CIEditPermission permission = getPermissionParam(joinPoint, CIEditPermission.class);
        Object[] args = joinPoint.getArgs();
        // 执行目标方法，并获取返回值
        if(permission == null){
            return joinPoint.proceed(args);
        }
        Long userId = SysUtil.getCurrentUserInfo().getId();
        List<SysRoleDataModuleRlt> dataModules = roleSvc.getRoleByUserAndCode(userId, "CICLASS");

        JSONObject ciInfo = JSON.parseObject(JSON.toJSONString(args[0]));
        JSONObject attrs = ciInfo.getJSONObject(permission.attrName());
        //兼容有查看权限时可从库中拖入到画布情况
        Boolean edit = ciInfo.getBoolean("edit");
        if(permission.object()){
            ciInfo = ciInfo.getJSONObject(permission.ciName());
        }
        String ciCode = ciInfo.getString("ciCode");
        Long classId = ciInfo.getLong("classId");
        if(classId == null || (edit != null && edit)){
            return joinPoint.proceed(args);
        }
        ESCIClassInfo classInfo = classSvc.getById(classId);
        if(BinaryUtils.isEmpty(ciCode)){
            List<Long> createClassIds = dataModules.stream().filter(e -> CHECK.equals(e.getIscreate())).map(e -> Long.parseLong(e.getDataValue())).distinct().collect(Collectors.toList());
            //校验新增
            if(BinaryUtils.isEmpty(classId) || !createClassIds.contains(classId)){
                throw new RuntimeException("无新建分类【"+classInfo.getClassName()+"】数据权限!");
            }
        }else{
            List<Long> updateClassIds = dataModules.stream().filter(e -> CHECK.equals(e.getIsupdate())).map(e -> Long.parseLong(e.getDataValue())).distinct().collect(Collectors.toList());
            //校验编辑
            if(BinaryUtils.isEmpty(classId) || !updateClassIds.contains(classId)){
                throw new RuntimeException("无修改分类【"+classInfo.getClassName()+"】数据权限!");
            }
            //校验attr是否符合规则
            boolean check = tagPermissionProcessor.checkEditPermission(attrs, classInfo);
            if(!check){
                throw new RuntimeException("无修改当前属性数据权限!");
            }
        }
        return joinPoint.proceed(args);
    }

    @Around("@annotation(permissionFunction)")
    public Object viewClassPermission(ProceedingJoinPoint joinPoint, ClassViewPermission permissionFunction) throws Throwable {
        // 获取方法参数
        Object[] args = joinPoint.getArgs();
        Object result = joinPoint.proceed(args);
        //当前用户拥有的
        Long userId = SysUtil.getCurrentUserInfo().getId();
        List<SysRoleDataModuleRlt> dataModules = roleSvc.getRoleByUserAndCode(userId, "CICLASS");
        List<Long> classIds = dataModules.stream().filter(e -> CHECK.equals(e.getIssee())).map(e -> Long.parseLong(e.getDataValue())).distinct().collect(Collectors.toList());
        List<?> arrayList = (List<?>)result;
        if(permissionFunction.subset()){
            result = filterBySubset(arrayList, permissionFunction.field(), classIds);
        }else if(permissionFunction.child()){
            result = filterByChild(arrayList, permissionFunction.childName(), permissionFunction.field(), classIds);
        }else{
            result = filterByField(arrayList, permissionFunction.field(), classIds);
        }
        return result;
    }

    /**
     * 通过子对象字段过滤
     * @param array 原数据
     * @param field 字段
     * @param classIds 分类id
     * @return 过滤后数据
     */
    private List<Object> filterByChild(List<?> array, String childName, String field, List<Long> classIds){
        List<Object> result = new ArrayList<>();
        for (Object each : array) {
            JSONObject json = JSON.parseObject(JSON.toJSONString(each));
            String childrenObj = json.getString(childName);
            if(childrenObj == null){
                continue;
            }
            JSONObject child = JSON.parseObject(childrenObj);
            Long id = child.getLong(field);
            if(id != null && classIds.contains(id)){
                result.add(each);
            }
        }
        return result;
    }

    /**
     * 通过子集字段过滤
     * @param array 原数据
     * @param field 字段
     * @param classIds 分类id
     * @return 过滤后数据
     */
    private List<Object> filterBySubset(List<?> array, String field, List<Long> classIds){
        List<Object> resultList = new ArrayList<>();
        for (Object each : array) {
            Class<?> clazz = each.getClass();
            JSONObject json = JSON.parseObject(JSON.toJSONString(each));
            String childrenObj = json.getString("children");
            if(childrenObj == null){
                continue;
            }
            List<JSONObject> children = JSON.parseArray(childrenObj, JSONObject.class);
            List<JSONObject> newChild = new ArrayList<>();
            for (JSONObject child : children) {
                Long id = child.getLong(field);
                if(id != null && classIds.contains(id)){
                    newChild.add(child);
                }
            }
            json.put("children", newChild);
            resultList.add(json.toJavaObject(clazz));
        }
        return resultList;
    }

    /**
     * 通过字段过滤
     * @param array 原数据
     * @param field 字段
     * @param classIds 分类id
     * @return 过滤后数据
     */
    private List<Object> filterByField(List<?> array, String field, List<Long> classIds){
        List<Object> resultList = new ArrayList<>();
        for (Object each : array) {
            JSONObject json = JSON.parseObject(JSON.toJSONString(each));
            Long id = json.getLong(field);
            if(id != null && classIds.contains(id)){
                resultList.add(each);
            }
        }
        return resultList;
    }

}
