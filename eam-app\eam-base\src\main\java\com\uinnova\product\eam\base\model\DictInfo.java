package com.uinnova.product.eam.base.model;

import com.uino.bean.cmdb.base.ESCIInfo;
import lombok.Data;

import java.util.Map;

@Data
public class DictInfo {

    private Long id;

    private String codeCode;

    private String codeValue;

    private String codeName;

    private String codeType;

    private String parentCode;

    private String isMajor;

    private String description;

    public DictInfo(){}

    public DictInfo(ESCIInfo ci){
        Map<String, Object> attrs = ci.getAttrs();
        this.id = Long.valueOf(String.valueOf(attrs.get("ID")));
        this.codeCode = String.valueOf(attrs.get("CODE_CODE"));
        this.codeValue = String.valueOf(attrs.get("CODE_VALUE"));
        this.codeName = String.valueOf(attrs.get("CODE_NAME"));
        this.codeType = String.valueOf(attrs.get("CODE_TYPE"));
        Object parent = attrs.get("PARENT_CODE");
        this.parentCode = parent == null ? "" : String.valueOf(parent);
        this.isMajor = attrs.get("IS_MAJOR") == null ? "0" : String.valueOf(attrs.get("IS_MAJOR"));
        this.description = attrs.get("DESCRIPTION") == null ? "" : String.valueOf(attrs.get("DESCRIPTION"));
    }

}
