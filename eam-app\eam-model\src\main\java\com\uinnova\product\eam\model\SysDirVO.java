package com.uinnova.product.eam.model;

import com.binary.framework.bean.annotation.Comment;
import com.uino.bean.cmdb.base.LibType;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class SysDirVO {

        private Long dirId;

        //文件夹类型 11表示仓颉使用的文件夹
        private Integer dirType;

        private String ciCode;

        private String ciName;

        private LibType libType;

        @Comment("系统文件夹还是普通文件夹")
        private Boolean isSysDir;

        private Long parentId;

        private String dirName;

        @Comment("0表示普通文件夹，1表示分类实例文件夹，2表示分类文件夹 3表示系统文件夹下面的文件夹 4表示设计方案特殊文件夹 5.表示列外目录")
        private Integer sysDir;

    @Comment("分类classId")
    private Long classId;


}
