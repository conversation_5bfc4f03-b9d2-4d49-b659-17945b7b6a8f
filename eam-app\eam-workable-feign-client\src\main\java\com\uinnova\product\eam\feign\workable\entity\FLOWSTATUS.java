package com.uinnova.product.eam.feign.workable.entity;

/**
 * description
 *
 * <AUTHOR>
 * @since 2022/11/28 12:28
 */
public enum FLOWSTATUS {
    ACTIVE(0,"审批中"),SUCCESS_END(1,"审批成功"),ERROR_END(2,"审批异常"),WITHDRAWAL(3,"审批取消");

    private Integer status;

    private String name;

    private FLOWSTATUS(Integer status,String name){
        this.status = status;
        this.name = name;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
