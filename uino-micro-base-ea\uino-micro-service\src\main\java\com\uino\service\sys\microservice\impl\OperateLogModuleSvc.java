package com.uino.service.sys.microservice.impl;

import com.uino.bean.sys.base.ESOperateLogModule;
import com.uino.dao.sys.ESOperateLogModuleSvc;
import com.uino.dao.util.ESUtil;
import com.uino.plugin.classloader.annotation.PluginOperateLogModule;
import com.uino.service.sys.microservice.IOperateLogModuleSvc;
import com.uino.util.log.OperateLogModuleInfo;
import com.uino.util.sys.BeanUtil;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.ApplicationContext;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class OperateLogModuleSvc implements ApplicationRunner, IOperateLogModuleSvc {

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private ESOperateLogModuleSvc operateModuleSvc;

    private final Map<String, ESOperateLogModule> allModuleMap = new HashMap<>();

    /**
     * 初始化操作日志模块
     */
    @Override
    public void run(ApplicationArguments args) throws Exception {
        reloadAllModules();
    }

    @Override
    public List<ESOperateLogModule> getAll() {
        reloadAllModules();
        return new ArrayList<>(allModuleMap.values());
    }

    @Override
    public ESOperateLogModule getModuleInfoByMvc(String mvcPath) {
        ESOperateLogModule result = null;
        reloadModulesFormAnnotation();
        for (ESOperateLogModule moduleInfo : this.allModuleMap.values()) {
            String prefix = moduleInfo.getModuleMvcPackagePrefix();
            if (mvcPath.startsWith(prefix) && (result == null || prefix.length() > result.getModuleMvcPackagePrefix().length())) {
                result = moduleInfo;
            }
        }
        if (result == null) {
            result = ESOperateLogModule.builder().moduleName("DEFAULT").moduleCode("DEFAULT").build();
        }
        return result;
    }

    /**
     * 从注解中加载所有模块信息到内存和ES
     * 不重新加载ES中数据到内存
     */
    private void reloadModulesFormAnnotation() {
        List<ESOperateLogModule> saveList = new ArrayList<>();
        // 获取注解中的模块信息
        List<ESOperateLogModule> moduleListFromAnnotation = getEditModuleListFromAnnotation();
        if (!CollectionUtils.isEmpty(moduleListFromAnnotation)) {
            saveList.addAll(moduleListFromAnnotation);
        }

        if (!CollectionUtils.isEmpty(saveList)) {
            operateModuleSvc.saveOrUpdateBatch(saveList);
        }
    }

    /**
     * 重新加载所有模块到内存和ES
     */
    private void reloadAllModules() {
        // 模块不会超过十个，直接全部查询
        List<ESOperateLogModule> allModules = operateModuleSvc.getListByQuery(new BoolQueryBuilder());
        if (!CollectionUtils.isEmpty(allModules)) {
            allModules.forEach(module -> this.allModuleMap.put(module.getModuleCode(), module));
        }

        List<ESOperateLogModule> saveList = new ArrayList<>();
        // 获取配置中的模块信息
        List<ESOperateLogModule> moduleListFromInterface = getEditModuleListFromInterface();
        if (!CollectionUtils.isEmpty(moduleListFromInterface)) {
            saveList.addAll(moduleListFromInterface);
        }
        // 获取注解中的模块信息
        List<ESOperateLogModule> moduleListFromAnnotation = getEditModuleListFromAnnotation();
        if (!CollectionUtils.isEmpty(moduleListFromAnnotation)) {
            saveList.addAll(moduleListFromAnnotation);
        }

        if (!CollectionUtils.isEmpty(saveList)) {
            operateModuleSvc.saveOrUpdateBatch(saveList);
        }
    }

    private List<ESOperateLogModule> getEditModuleListFromAnnotation() {
        List<ESOperateLogModule> moduleList = new ArrayList<>();
        Map<String, Object> beanMap = applicationContext.getBeansWithAnnotation(PluginOperateLogModule.class);
        if (!CollectionUtils.isEmpty(beanMap)) {
            beanMap.forEach((k, v) -> {
                PluginOperateLogModule moduleAnnotation = AnnotationUtils.findAnnotation(v.getClass(), PluginOperateLogModule.class);
                if (moduleAnnotation != null) {
                    String moduleCode = moduleAnnotation.code();
                    if (this.allModuleMap.containsKey(moduleCode) && !moduleInfoEquals(this.allModuleMap.get(moduleCode), moduleAnnotation)) {
                        this.allModuleMap.get(moduleCode).setModuleName(moduleAnnotation.name());
                        this.allModuleMap.get(moduleCode).setModuleMvcPackagePrefix(moduleAnnotation.mvcPackagePrefix());
                        moduleList.add(this.allModuleMap.get(moduleCode));
                    } else if (!this.allModuleMap.containsKey(moduleCode)) {
                        ESOperateLogModule module = ESOperateLogModule.builder()
                                .moduleCode(moduleAnnotation.code())
                                .moduleName(moduleAnnotation.name())
                                .moduleMvcPackagePrefix(moduleAnnotation.mvcPackagePrefix())
                                .id(ESUtil.getUUID()).build();
                        this.allModuleMap.put(module.getModuleCode(), module);
                        moduleList.add(module);
                    }
                }
            });
        }
        return moduleList;
    }

    public List<ESOperateLogModule> getEditModuleListFromInterface() {
        List<ESOperateLogModule> moduleList = new ArrayList<>();
        // 获取配置中的模块信息
        Map<String, OperateLogModuleInfo> beanMap = applicationContext.getBeansOfType(OperateLogModuleInfo.class);
        beanMap.forEach((k, moduleInfo) -> {
            if (moduleInfo.getModuleMvcPackagePrefix() != null && moduleInfo.getModuleMvcPackagePrefix().startsWith("com.uino")) {
                String moduleCode = moduleInfo.getModuleCode();
                if (allModuleMap.containsKey(moduleCode) && !moduleInfoEquals(this.allModuleMap.get(moduleCode), moduleInfo)) {
                    this.allModuleMap.get(moduleCode).setModuleName(moduleInfo.getModuleName());
                    this.allModuleMap.get(moduleCode).setModuleMvcPackagePrefix(moduleInfo.getModuleMvcPackagePrefix());
                    moduleList.add(this.allModuleMap.get(moduleCode));
                } else if (!this.allModuleMap.containsKey(moduleCode)) {
                    ESOperateLogModule module = new ESOperateLogModule();
                    BeanUtil.copyProperties(moduleInfo, module);
                    module.setId(ESUtil.getUUID());
                    this.allModuleMap.put(module.getModuleCode(), module);
                    moduleList.add(module);
                }
            }
        });
        return moduleList;
    }

    private boolean moduleInfoEquals(ESOperateLogModule esModule, OperateLogModuleInfo beanModule) {
        if (esModule == null || beanModule == null) {
            return false;
        } else {
            return esModule.getModuleName().equals(beanModule.getModuleName())
                    && esModule.getModuleCode().equals(beanModule.getModuleCode())
                    && esModule.getModuleMvcPackagePrefix().equals(beanModule.getModuleMvcPackagePrefix());
        }
    }

    private boolean moduleInfoEquals(ESOperateLogModule esModule, PluginOperateLogModule beanModule) {
        if (esModule == null || beanModule == null) {
            return false;
        } else {
            return esModule.getModuleName().equals(beanModule.name())
                    && esModule.getModuleCode().equals(beanModule.code())
                    && esModule.getModuleMvcPackagePrefix().equals(beanModule.mvcPackagePrefix());
        }
    }
}
