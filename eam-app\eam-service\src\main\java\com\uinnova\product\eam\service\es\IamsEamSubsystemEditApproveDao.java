package com.uinnova.product.eam.service.es;

import com.uinnova.product.eam.comm.bean.SubsystemEditData;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;

@Service
public class IamsEamSubsystemEditApproveDao extends AbstractESBaseDao<SubsystemEditData,SubsystemEditData> {



    @Override
    public String getIndex() {
        return "uino_eam_subsystem_approve_edit";
    }

    @Override
    public String getType() {
        return "_doc";
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }
}
