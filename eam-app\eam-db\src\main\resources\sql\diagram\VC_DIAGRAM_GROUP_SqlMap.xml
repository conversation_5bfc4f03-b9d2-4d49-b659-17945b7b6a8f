<?xml version="1.0" encoding="UTF-8"?><!--Converted at: Sat Dec 08 16:22:36 CST 2018-->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="VC_DIAGRAM_GROUP">


	<resultMap id="queryResult" type="com.uinnova.product.eam.comm.model.VcDiagramGroup">
		<result property="id" column="ID" jdbcType="BIGINT"/>	<!-- ID -->
		<result property="diagramId" column="DIAGRAM_ID" jdbcType="BIGINT"/>	<!-- 视图ID -->
		<result property="groupId" column="GROUP_ID" jdbcType="BIGINT"/>	<!-- 组ID -->
		<result property="deployUserId" column="DEPLOY_USER_ID" jdbcType="BIGINT"/>	<!-- 发布人 -->
		<result property="deployTime" column="DEPLOY_TIME" jdbcType="BIGINT"/>	<!-- 发布时间 -->
		<result property="domainId" column="DOMAIN_ID" jdbcType="BIGINT"/>	<!-- 所属域 -->
		<result property="createTime" column="CREATE_TIME" jdbcType="BIGINT"/>	<!-- 创建时间 -->
		<result property="modifyTime" column="MODIFY_TIME" jdbcType="BIGINT"/>	<!-- 修改时间 -->
	</resultMap>
	

	<sql id="sql_query_where">
		<if test="cdt != null and cdt.id != null">and
			ID = #{cdt.id:BIGINT}
		</if>
		<if test="ids != null and ids != ''">and
			ID in (${ids})
		</if>
		<if test="cdt != null and cdt.startId != null">and
			 ID &gt;= #{cdt.startId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endId != null">and
			 ID &lt;= #{cdt.endId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.diagramId != null">and
			DIAGRAM_ID = #{cdt.diagramId:BIGINT}
		</if>
		<if test="diagramIds != null and diagramIds != ''">and
			DIAGRAM_ID in (${diagramIds})
		</if>
		<if test="cdt != null and cdt.startDiagramId != null">and
			 DIAGRAM_ID &gt;= #{cdt.startDiagramId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endDiagramId != null">and
			 DIAGRAM_ID &lt;= #{cdt.endDiagramId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.groupId != null">and
			GROUP_ID = #{cdt.groupId:BIGINT}
		</if>
		<if test="groupIds != null and groupIds != ''">and
			GROUP_ID in (${groupIds})
		</if>
		<if test="cdt != null and cdt.startGroupId != null">and
			 GROUP_ID &gt;= #{cdt.startGroupId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endGroupId != null">and
			 GROUP_ID &lt;= #{cdt.endGroupId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.deployUserId != null">and
			DEPLOY_USER_ID = #{cdt.deployUserId:BIGINT}
		</if>
		<if test="deployUserIds != null and deployUserIds != ''">and
			DEPLOY_USER_ID in (${deployUserIds})
		</if>
		<if test="cdt != null and cdt.startDeployUserId != null">and
			 DEPLOY_USER_ID &gt;= #{cdt.startDeployUserId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endDeployUserId != null">and
			 DEPLOY_USER_ID &lt;= #{cdt.endDeployUserId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.deployTime != null">and
			DEPLOY_TIME = #{cdt.deployTime:BIGINT}
		</if>
		<if test="deployTimes != null and deployTimes != ''">and
			DEPLOY_TIME in (${deployTimes})
		</if>
		<if test="cdt != null and cdt.startDeployTime != null">and
			 DEPLOY_TIME &gt;= #{cdt.startDeployTime:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endDeployTime != null">and
			 DEPLOY_TIME &lt;= #{cdt.endDeployTime:BIGINT} 
		</if>
		<if test="cdt != null and cdt.domainId != null">and
			DOMAIN_ID = #{cdt.domainId:BIGINT}
		</if>
		<if test="domainIds != null and domainIds != ''">and
			DOMAIN_ID in (${domainIds})
		</if>
		<if test="cdt != null and cdt.startDomainId != null">and
			 DOMAIN_ID &gt;= #{cdt.startDomainId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endDomainId != null">and
			 DOMAIN_ID &lt;= #{cdt.endDomainId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.createTime != null">and
			CREATE_TIME = #{cdt.createTime:BIGINT}
		</if>
		<if test="createTimes != null and createTimes != ''">and
			CREATE_TIME in (${createTimes})
		</if>
		<if test="cdt != null and cdt.startCreateTime != null">and
			 CREATE_TIME &gt;= #{cdt.startCreateTime:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endCreateTime != null">and
			 CREATE_TIME &lt;= #{cdt.endCreateTime:BIGINT} 
		</if>
		<if test="cdt != null and cdt.modifyTime != null">and
			MODIFY_TIME = #{cdt.modifyTime:BIGINT}
		</if>
		<if test="modifyTimes != null and modifyTimes != ''">and
			MODIFY_TIME in (${modifyTimes})
		</if>
		<if test="cdt != null and cdt.startModifyTime != null">and
			 MODIFY_TIME &gt;= #{cdt.startModifyTime:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endModifyTime != null">and
			 MODIFY_TIME &lt;= #{cdt.endModifyTime:BIGINT} 
		</if>
	</sql>
	

	<sql id="sql_update_columns">
		<if test="record != null and record.id != null"> 
			ID = #{record.id:BIGINT}
		,</if>
		<if test="record != null and record.diagramId != null"> 
			DIAGRAM_ID = #{record.diagramId:BIGINT}
		,</if>
		<if test="record != null and record.groupId != null"> 
			GROUP_ID = #{record.groupId:BIGINT}
		,</if>
		<if test="record != null and record.deployUserId != null"> 
			DEPLOY_USER_ID = #{record.deployUserId:BIGINT}
		,</if>
		<if test="record != null and record.deployTime != null"> 
			DEPLOY_TIME = #{record.deployTime:BIGINT}
		,</if>
		<if test="record != null and record.domainId != null"> 
			DOMAIN_ID = #{record.domainId:BIGINT}
		,</if>
		<if test="record != null and record.createTime != null"> 
			CREATE_TIME = #{record.createTime:BIGINT}
		,</if>
		<if test="record != null and record.modifyTime != null"> 
			MODIFY_TIME = #{record.modifyTime:BIGINT}
		,</if>
	</sql>
	

	<sql id="sql_query_columns">
		ID, DIAGRAM_ID, GROUP_ID, DEPLOY_USER_ID, DEPLOY_TIME, DOMAIN_ID, 
		CREATE_TIME, MODIFY_TIME
	</sql>
	

	

	<select id="selectList" parameterType="java.util.Map" resultMap="queryResult">
		select 
			<include refid="VC_DIAGRAM_GROUP.sql_query_columns"/>
		from VC_DIAGRAM_GROUP 
			<where>
				<include refid="VC_DIAGRAM_GROUP.sql_query_where"/>
			</where>
		order by 
			<if test="orders != null and orders != ''">
				${orders}
			</if>
			<if test="orders == null or orders == ''">
				ID
			</if>
	</select>
	<select id="selectCount" parameterType="java.util.Map" resultType="java.lang.Long">
		select count(1) from VC_DIAGRAM_GROUP 
			<where>
				<include refid="VC_DIAGRAM_GROUP.sql_query_where"/>
			</where>
	</select>
	<select id="selectById" parameterType="java.util.Map" resultMap="queryResult">
		select 
			<include refid="VC_DIAGRAM_GROUP.sql_query_columns"/>
		from VC_DIAGRAM_GROUP where ID=#{id:BIGINT} 
	</select>
	

	

	<insert id="insert" parameterType="java.util.Map">
		insert into VC_DIAGRAM_GROUP(
			ID, DIAGRAM_ID, GROUP_ID, DEPLOY_USER_ID, DEPLOY_TIME, 
			DOMAIN_ID, CREATE_TIME, MODIFY_TIME)
		values (
			#{record.id:BIGINT}, #{record.diagramId:BIGINT}, #{record.groupId:BIGINT}, #{record.deployUserId:BIGINT}, #{record.deployTime:BIGINT}, 
			#{record.domainId:BIGINT}, #{record.createTime:BIGINT}, #{record.modifyTime:BIGINT})
	</insert>
	

	

	<update id="updateById" parameterType="java.util.Map">
		update VC_DIAGRAM_GROUP
			<set> 
				<include refid="VC_DIAGRAM_GROUP.sql_update_columns"/> 
			</set>
		where ID = #{id:BIGINT}
	</update>
	<update id="updateByCdt" parameterType="java.util.Map">
		update VC_DIAGRAM_GROUP
			<set> 
				<include refid="VC_DIAGRAM_GROUP.sql_update_columns"/> 
			</set>
			<where> 
				<include refid="VC_DIAGRAM_GROUP.sql_query_where"/> 
			</where>
	</update>
	
	

	

	<delete id="deleteById" parameterType="java.util.Map">
		delete from VC_DIAGRAM_GROUP where ID = #{id:BIGINT}
	</delete>
	<delete id="deleteByCdt" parameterType="java.util.Map">
		delete from VC_DIAGRAM_GROUP
			<where> 
				<include refid="VC_DIAGRAM_GROUP.sql_query_where"/> 
			</where>
	</delete>
	



</mapper>