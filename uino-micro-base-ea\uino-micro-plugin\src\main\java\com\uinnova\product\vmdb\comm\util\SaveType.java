package com.uinnova.product.vmdb.comm.util;

/**
 * 保存类型
 * 
 * <AUTHOR>
 */
public enum SaveType {

    /**
     * 覆盖, 以导入数据为准 1. 导入数据在数据库中不存在, 则新增 2. 导入数据在数据库中已存在, 则更新 3. 数据库数据在导入数据中不存在, 则删除
     */
    OVERRIDE,

    /**
     * 更新, 取合集 1. 导入数据在数据库中不存在, 则新增 2. 导入数据在数据库中已存在, 则更新 3. 数据库数据在导入数据中不存在, 则不变
     */
    UPDATE,

    /**
     * 忽略, 取合集 1. 导入数据在数据库中不存在, 则新增 2. 导入数据在数据库中已存在, 则不更新(忽略) 3. 数据库数据在导入数据中不存在, 则不变
     */
    IGNORE

}
