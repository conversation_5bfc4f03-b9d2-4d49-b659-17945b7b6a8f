package com.uinnova.product.eam.service.es;

import com.uinnova.product.eam.comm.model.es.EamNotice;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;

@Service
public class EamNoticeDao extends AbstractESBaseDao<EamNotice, EamNotice> {

    @Override
    public String getIndex() {
        return "uino_eam_notice";
    }

    @Override
    public String getType() {
        return "_doc";
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }
}
