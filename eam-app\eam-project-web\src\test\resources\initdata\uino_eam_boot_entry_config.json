[{"bootEntryName": "test开始吧", "createTime": 20220721182014, "id": 604653030300000, "modifyTime": 20220721182049, "sort": 0, "userRoleId": 8964026895324295}, {"bootButtonName": "查看应用架构3D全景", "bootEntryName": "我想向公司高层、同业嘉宾介绍企业架构情况", "bootIntroduce": "提供企业级IT架构三维展示能力，多种模式灵活切换，实现架构立体效果呈现和与众不同的观感体验", "bootType": 2, "createTime": 20220724144806, "firstMenuPath": "/application-square#/", "functionId": "8964026895312496", "id": 3, "isFromStandard": true, "menuPath": "/app-wall#/?isShowBack=true", "modifyTime": 20220724144806, "resourcePath": "/rsm/bootEntrySource/3.png", "secondBootButtonName": "去试试", "sort": 3, "title": "查看应用架构3D全景", "titleIntroduce": "点击应用架构3D全景卡片，三维展示企业级IT应用架构全景", "userRoleId": 1}, {"bootButtonName": "搭建架构元模型", "bootEntryName": "我想规范我们企业的架构概念和基本术语", "bootIntroduce": "提供企业架构元模型定义能力，规范企业架构概念与术语，实现沟通同频、效率提升", "bootType": 1, "createTime": 20220724145949, "firstMenuPath": "/system-setting#/unit", "functionId": "8948830619009861", "id": 5, "isFromStandard": true, "isJump": true, "menuPath": "", "modifyTime": 20220724145949, "resourcePath": "/rsm/bootEntrySource/5.png", "secondBootButtonName": "知道了", "sort": 2, "title": "搭建架构元模型", "titleIntroduce": "拖拽图标和分类可视化搭建架构元模型，一站式设置架构元素和关系的图例、属性和信息模型", "userRoleId": 2}, {"bootButtonName": "查看业务架构全景", "bootEntryName": "我想了解企业业务架构的全貌", "bootIntroduce": "提供业务架构的全景展现能力，一键纵览流程图谱，快速了解当前业务现状，全面把握企业核心业务能力", "bootType": 2, "createTime": 20220724144253, "firstMenuPath": "/application-square#/", "functionId": "8964026895312496", "id": 1, "isFromStandard": true, "menuPath": "/diagram#/fullview?id=1", "modifyTime": 20220724144253, "resourcePath": "/rsm/bootEntrySource/1.png", "secondBootButtonName": "去试试", "sort": 1, "title": "查看业务架构全景", "titleIntroduce": "点击业务架构全景卡片，快速了解企业当前业务架构全貌", "userRoleId": 1}, {"bootButtonName": "配置架构制图规范", "bootEntryName": "我想制定我们企业的架构制图规范", "bootIntroduce": "提供架构制品类型定制能力，将架构规范融入制图过程，实现规范化制图，提升制图质量", "bootType": 1, "createTime": 20220724151419, "firstMenuPath": "/eam#/unit-modal/product", "functionId": "8948830619009964", "id": 6, "isFromStandard": true, "isJump": true, "menuPath": "", "modifyTime": 20220724151419, "resourcePath": "/rsm/bootEntrySource/6.png", "secondBootButtonName": "知道了", "sort": 3, "title": "配置架构制图规范", "titleIntroduce": "通过配置架构制品类型，规范架构制图的图例形状、关联资产数据、视图模板等要素", "userRoleId": 2}, {"bootButtonName": "查看应用架构全景图", "bootEntryName": "我想了解企业IT架构的全貌", "bootIntroduce": "提供IT架构的全景展现能力，一图贯穿整个架构，实现“一图在手，架构全有”的全新认知体验", "bootType": 2, "createTime": 20220724144442, "firstMenuPath": "/application-square#/", "functionId": "8964026895312496", "id": 2, "isFromStandard": true, "menuPath": "/diagram#/fullview?id=3", "modifyTime": 20220724144442, "resourcePath": "/rsm/bootEntrySource/2.png", "secondBootButtonName": "去试试", "sort": 2, "title": "查看应用架构全景", "titleIntroduce": "点击应用架构全景卡片，快速了解企业IT架构的全貌", "userRoleId": 1}, {"bootButtonName": "查看应用系统关系图谱", "bootEntryName": "我想了解企业所有应用系统之间的关联关系", "bootIntroduce": "提供应用系统间访问关系分析能力，基于数字资产实现可视化自动分析，轻松厘清系统间的复杂访问关系", "bootType": 2, "createTime": 20220724154015, "firstMenuPath": "/application-square#/", "functionId": "8964026895312496", "id": 9, "isFromStandard": true, "menuPath": "/eam#/theme/DataStoreFull?id=7", "modifyTime": 20220724154015, "resourcePath": "/rsm/bootEntrySource/9.png", "secondBootButtonName": "去试试", "sort": 2, "title": "查看应用系统关系图谱", "titleIntroduce": "点击应用系统关系图谱卡片，查看各个系统间的访问关系及接口细节", "userRoleId": 3}, {"bootButtonName": "绘制应用系统架构视图", "bootEntryName": "我想快速绘制一个系统的架构视图", "bootIntroduce": "提供系统架构制图能力，基于架构制品类型，规范高效地绘制不同视点的应用架构视图", "bootType": 2, "createTime": 20220724154830, "firstMenuPath": "/framework-design#/1", "functionId": "8964026895302177", "id": 12, "isFromStandard": true, "menuActionType": "newBuild", "menuPath": "", "modifyTime": 20220724154830, "resourcePath": "/rsm/bootEntrySource/12.png", "secondBootButtonName": "知道了", "sort": 5, "title": "绘制应用系统架构视图", "titleIntroduce": "点击新建按钮，选择基于制品类型或自由视图创建应用架构视图", "userRoleId": 3}, {"bootButtonName": "设计应用架构蓝图", "bootEntryName": "我想设计企业的应用架构蓝图", "bootIntroduce": "提供企业级应用架构设计能力，全面支持企业应用架构蓝图的规划设计，助力企业IT战略目标的达成", "bootType": 2, "createTime": 20220724154249, "firstMenuPath": "/framework-design#/1", "functionId": "8964026895302177", "id": 10, "isFromStandard": true, "menuActionType": "newBuild", "menuPath": "", "modifyTime": 20220724154249, "resourcePath": "/rsm/bootEntrySource/10.png", "secondBootButtonName": "知道了", "sort": 3, "title": "设计应用架构蓝图", "titleIntroduce": "点击新建按钮，选择基于制品类型或自由视图创建应用架构蓝图", "userRoleId": 3}, {"bootButtonName": "导入业务架构资产数据", "bootEntryName": "我想导入存量业务架构资产数据", "bootIntroduce": "提供业务数据的导入能力，便捷导入业务架构资产数据，迅速盘活企业存量业务架构资产。", "bootType": 2, "createTime": 20220724160312, "firstMenuPath": "/system-setting#/model-asset/newci", "functionId": "menu_data", "id": 16, "isFromStandard": true, "menuActionType": "objectManagement", "menuPath": "", "modifyTime": 20220724160312, "resourcePath": "/rsm/bootEntrySource/16.png", "secondBootButtonName": "知道了", "sort": 3, "title": "导入业务架构资产数据", "titleIntroduce": "下载资产数据导入模板，一键导入存量业务架构资产数据", "userRoleId": 4}, {"bootButtonName": "查看业务架构全景视图", "bootEntryName": "我想了解企业业务架构的全貌", "bootIntroduce": "提供业务架构的全景展现能力，一键纵览流程图谱，快速了解当前业务现状，全面把握企业核心业务能力", "bootType": 2, "createTime": 20220724155622, "firstMenuPath": "/application-square#/", "functionId": "8964026895312496", "id": 14, "isFromStandard": true, "menuPath": "/diagram#/fullview?id=1", "modifyTime": 20220724155622, "resourcePath": "/rsm/bootEntrySource/14.png", "secondBootButtonName": "去试试", "sort": 1, "title": "查看业务架构全景视图", "titleIntroduce": "点击业务架构全景视图，快速了解企业当前业务架构全貌", "userRoleId": 4}, {"bootButtonName": "查看业务流程参考模型", "bootEntryName": "我想了解业务流程的参考模型", "bootIntroduce": "提供业务流程模型参考，详细展示业务流程模型搭建方式，快速提升业务流程建模能力", "bootType": 2, "createTime": 20220724155904, "firstMenuPath": "/eam#/bm-model-assets/flowModel/0", "functionId": "8948830619009330", "id": 15, "isFromStandard": true, "menuActionType": "box", "menuPath": "", "modifyTime": 20220724155904, "resourcePath": "/rsm/bootEntrySource/15.png", "secondBootButtonName": "知道了", "sort": 2, "title": "查看业务流程参考模型", "titleIntroduce": "点击业务流程参考模型，快速了解业务流程模型的分层分级框架和细节", "userRoleId": 4}, {"bootButtonName": "查看应用系统架构资产", "bootEntryName": "我想完整了解一个系统的架构情况", "bootIntroduce": "提供系统架构多维展示能力，多视角展示架构视图，清晰呈现应用系统架构全貌", "bootType": 2, "createTime": 20220724154512, "firstMenuPath": "/framework-assets#/assets/1", "functionId": "8964026895312391", "id": 11, "isFromStandard": true, "menuActionType": "search", "menuPath": "", "modifyTime": 20220724154512, "resourcePath": "/rsm/bootEntrySource/11.png", "secondBootButtonName": "知道了", "sort": 4, "title": "查看应用系统架构资产", "titleIntroduce": "输入应用系统名称，搜索并查看应用系统的架构资产", "userRoleId": 3}, {"bootButtonName": "查看应用架构全景墙", "bootEntryName": "我想了解企业当前的（应用）架构全貌", "bootIntroduce": "提供企业级IT架构的全景展现能力，一图贯穿整个架构，实现“一图在手，架构全有”的全新认知体验", "bootType": 2, "createTime": 20220724153442, "firstMenuPath": "/application-square#/", "functionId": "8964026895312496", "id": 8, "isFromStandard": true, "menuPath": "/app-wall#/?isShowBack=true", "modifyTime": 20220724153442, "resourcePath": "/rsm/bootEntrySource/8.png", "secondBootButtonName": "去试试", "sort": 1, "title": "查看应用架构全景墙", "titleIntroduce": "点击应用架构全景墙，三维展示企业级IT架构情况，切换不同模式快速了解企业架构全貌", "userRoleId": 3}, {"bootButtonName": "搭建业务流程模型", "bootEntryName": "我想搭建业务流程模型", "bootIntroduce": "提供业务流程建模能力，快速搭建可视化、结构化、数字化的业务模型，助力业务到IT的快速平滑传导", "bootType": 2, "createTime": 20220724160516, "firstMenuPath": "/eam#/bm-workbench/documents/0", "functionId": "8948830619008488", "id": 17, "isFromStandard": true, "menuActionType": "newBuild", "menuPath": "", "modifyTime": 20220724160516, "resourcePath": "/rsm/bootEntrySource/17.png", "secondBootButtonName": "知道了", "sort": 4, "title": "搭建业务流程模型", "titleIntroduce": "点击新建按钮，创建业务流程模型", "userRoleId": 4}, {"bootButtonName": "分析业务与IT关联性", "bootEntryName": "我想查看业务需求对IT建设的影响", "bootIntroduce": "提供业务与IT关联分析能力，可视化展示业务与IT系统关系，准确识别业务需求对于IT系统建设的实际影响", "bootType": 2, "createTime": 20220724160726, "firstMenuPath": "/application-square#/", "functionId": "8964026895312496", "id": 18, "isFromStandard": true, "menuPath": "/eam#/theme/TreeFriendView?id=12", "modifyTime": 20220724160726, "resourcePath": "/rsm/bootEntrySource/18.png", "secondBootButtonName": "去试试", "sort": 5, "title": "分析业务与IT关联性", "titleIntroduce": "点击资产关联分析卡片，通过逐层展开或选择分析路径查看业务与IT关联关系", "userRoleId": 4}, {"bootButtonName": "查看业务架构全景视图", "bootEntryName": "我想了解企业当前业务架构全貌", "bootIntroduce": "提供企业级业务架构的全景展现能力，快速了解当前业务现状，把握企业核心业务能力", "bootType": 2, "createTime": 20220724173406, "firstMenuPath": "/application-square#/", "functionId": "8964026895312496", "id": 19, "isFromStandard": true, "menuPath": "/diagram#/fullview?id=1", "modifyTime": 20220724173406, "resourcePath": "/rsm/bootEntrySource/19.png", "secondBootButtonName": "去试试", "sort": 1, "title": "查看业务架构全景视图", "titleIntroduce": "点击业务架构全景视图，快速了解企业当前业务架构全貌", "userRoleId": 5}, {"bootButtonName": "查看业务流程模型资产", "bootEntryName": "我想了解某个业务条线的业务流程", "bootIntroduce": "提供业务流程的便捷展示能力，一键纵览业务条线流程图谱，快速了解业务条线关键流程", "bootType": 2, "createTime": 20220724173703, "firstMenuPath": "/eam#/bm-model-assets/flowModel/0", "functionId": "8948830619009282", "id": 20, "isFromStandard": true, "menuActionType": "box", "menuPath": "", "modifyTime": 20220724173703, "resourcePath": "/rsm/bootEntrySource/20.png", "secondBootButtonName": "知道了", "sort": 2, "title": "查看业务流程模型资产", "titleIntroduce": "点击业务流程模型，快速了解关注的业务条线相关流程", "userRoleId": 5}, {"bootButtonName": "分析系统对业务的支撑关系", "bootEntryName": "我想了解哪些系统在支撑我负责的业务条线", "bootIntroduce": "提供业务与IT关联分析能力，可视化展示业务与IT系统关系，准确把握自身业务对IT系统建设的影响", "bootType": 2, "createTime": 20220724173837, "firstMenuPath": "/application-square#/", "functionId": "8964026895312496", "id": 21, "isFromStandard": true, "menuPath": "/eam#/theme/TreeFriendView?id=12", "modifyTime": 20220724173837, "resourcePath": "/rsm/bootEntrySource/21.png", "secondBootButtonName": "去试试", "sort": 3, "title": "分析系统对业务的支撑关系", "titleIntroduce": "点击资产关联分析卡片，通过逐层展开或选择分析路径查看所关注业务的支撑系统", "userRoleId": 5}, {"bootButtonName": "应用架构全景视图", "bootEntryName": "我想了解企业当前IT架构全貌", "bootIntroduce": "提供企业级IT架构的全景展现能力，一图贯穿整个架构，实现“一图在手，架构全有”的全新认知体验", "bootType": 2, "createTime": 20220724174652, "firstMenuPath": "/application-square#/", "functionId": "8964026895312496", "id": 22, "isFromStandard": true, "menuPath": "/diagram#/fullview?id=3", "modifyTime": 20220724174652, "resourcePath": "/rsm/bootEntrySource/22.png", "secondBootButtonName": "去试试", "sort": 4, "title": "应用架构全景视图", "titleIntroduce": "点击应用架构全景视图，快速了解企业IT架构的全貌", "userRoleId": 5}, {"bootButtonName": "查看应用系统架构视图", "bootEntryName": "我想看看我参与建设的应用系统架构图", "bootIntroduce": "提供系统架构多维展示能力，多视角展示架构视图，清晰呈现所参与应用系统的架构全貌", "bootType": 2, "createTime": 20220724174956, "firstMenuPath": "/framework-assets#/assets/1", "functionId": "8964026895312391", "id": 23, "isFromStandard": true, "menuActionType": "view", "menuPath": "", "modifyTime": 20220724174956, "resourcePath": "/rsm/bootEntrySource/23.png", "secondBootButtonName": "知道了", "sort": 5, "title": "查看应用系统架构视图", "titleIntroduce": "按应用系统名称搜索后点击视图标签，查看应用系统的架构视图", "userRoleId": 5}, {"bootButtonName": "查看架构管控流程模型", "bootEntryName": "我想了解架构管控的参考流程和工艺", "bootIntroduce": "提供行业实践参考框架，快速了解架构管控的工艺与流程，助力企业架构管控的优化提升", "bootType": 2, "createTime": 20220724145552, "firstMenuPath": "/eam#/bm-workbench/documents/460053856560478", "functionId": "8948830619008488", "id": 4, "isFromStandard": true, "menuActionType": "newBuild", "menuPath": "", "modifyTime": 20220724145552, "resourcePath": "/rsm/bootEntrySource/4.png", "secondBootButtonName": "知道了", "sort": 1, "title": "查看架构管控流程模型", "titleIntroduce": "点击架构管控流程模型，快速了解架构管控的工艺与流程", "userRoleId": 2}, {"bootButtonName": "查看企业当前IT架构现状", "bootEntryName": "我想了解如何有效复用存量架构资产数据", "bootIntroduce": "提供IT架构的自动成图能力，自动生成五张架构视图，快速把握企业IT架构现状，快速盘活企业存量的架构资产数据。", "bootType": 2, "createTime": 20220724144806, "firstMenuPath": "/application-square#/", "functionId": "8964026895312496", "id": 24, "isFromStandard": true, "menuPath": "/eam#/it-appWall?id=13", "modifyTime": 20220724144806, "resourcePath": "/rsm/bootEntrySource/25.png", "imageOrVideoUrl": "https://cdn.uino.cn/quickea/rsm/guide.gif", "secondBootButtonName": "去试试", "sort": 4, "title": "查看企业当前IT架构现状", "titleIntroduce": "点击存量数据自动成图卡片，快速了解企业当前IT架构现状", "userRoleId": 1}, {"bootButtonName": "查看企业当前IT架构现状", "bootEntryName": "我想了解如何有效复用存量架构资产数据", "bootIntroduce": "提供IT架构的自动成图能力，自动生成五张架构视图，快速把握企业IT架构现状，快速盘活企业存量的架构资产数据。", "bootType": 2, "createTime": 20220724144806, "firstMenuPath": "/application-square#/", "functionId": "8964026895312496", "id": 25, "isFromStandard": true, "menuPath": "/eam#/it-appWall?id=13", "modifyTime": 20220724144806, "resourcePath": "/rsm/bootEntrySource/25.png", "imageOrVideoUrl": "https://cdn.uino.cn/quickea/rsm/guide.gif", "secondBootButtonName": "去试试", "sort": 4, "title": "查看企业当前IT架构现状", "titleIntroduce": "点击存量数据自动成图卡片，快速了解企业当前IT架构现状", "userRoleId": 2}, {"bootButtonName": "查看系统的架构现状", "bootEntryName": "我想了解企业某个存量系统的架构现状", "bootIntroduce": "提供IT架构的自动成图能力，自动生成五张架构视图，快速了解系统的架构现状，充分发掘存量架构资产的数据价值。", "bootType": 2, "createTime": 20220724144806, "firstMenuPath": "/application-square#/", "functionId": "8964026895312496", "id": 26, "isFromStandard": true, "menuPath": "/eam#/it-appWall?id=13", "modifyTime": 20220724144806, "resourcePath": "/rsm/bootEntrySource/25.png", "imageOrVideoUrl": "https://cdn.uino.cn/quickea/rsm/guide.gif", "secondBootButtonName": "去试试", "sort": 6, "title": "查看系统的架构现状", "titleIntroduce": "点击存量数据自动成图卡片，快速了解关注系统的架构现状", "userRoleId": 4}, {"bootButtonName": "查看系统的架构现状", "bootEntryName": "我想了解企业某个存量系统的架构现状", "bootIntroduce": "提供IT架构的自动成图能力，自动生成五张架构视图，快速了解系统的架构现状，充分发掘存量架构资产的数据价值。", "bootType": 2, "createTime": 20220724144806, "firstMenuPath": "/application-square#/", "functionId": "8964026895312496", "id": 27, "isFromStandard": true, "menuPath": "/eam#/it-appWall?id=13", "modifyTime": 20220724144806, "resourcePath": "/rsm/bootEntrySource/25.png", "imageOrVideoUrl": "https://cdn.uino.cn/quickea/rsm/guide.gif", "secondBootButtonName": "去试试", "sort": 6, "title": "查看系统的架构现状", "titleIntroduce": "点击存量数据自动成图卡片，快速了解关注系统的架构现状", "userRoleId": 5}]