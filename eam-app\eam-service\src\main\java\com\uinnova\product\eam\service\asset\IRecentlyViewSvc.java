package com.uinnova.product.eam.service.asset;

import com.uinnova.product.eam.base.diagram.model.ESDiagram;
import com.uinnova.product.eam.comm.model.es.CEamRecentlyView;
import com.uinnova.product.eam.comm.model.es.EamRecentlyView;
import com.uinnova.product.eam.model.RecentlyViewBo;

import java.util.List;

public interface IRecentlyViewSvc {

    /**
     * 添加或修改我的最近查看记录
     * @param ceamRecentlyView
     * @return
     */
    Long saveOrUpdateRecentlyView(CEamRecentlyView ceamRecentlyView);

    /**
     * 获取最近常看列表
     * @param eamRecentlyView
     * @return
     */
    List<ESDiagram> findRecentlyViewList(EamRecentlyView eamRecentlyView);

    /**
     * 删除最近查看记录
     * @param eamRecentlyView
     */
    Long cancelRecentlyView(EamRecentlyView eamRecentlyView);

    void cancelRecentlyViewBatch(List<Long> diagramIds, List<Long> planIds, List<Long> matrixIds);

    void cancelRecentlyViewMatrix(List<Long> matrixIds);

    /**
     * 查看最近查看的20条记录信息
     * @return
     */
    List<EamRecentlyView> getRecentlyView(Integer buildType);

    RecentlyViewBo getAllRecentlyDiagram();

    RecentlyViewBo getDesignRecentlyDiagram();

    /**
     * 批量取消最近常看
     * @param eamRecentlyView
     */
    void batchCancelRecentlyView(CEamRecentlyView eamRecentlyView);

    /**
     * 查看资产库最近查看的20条记录信息
     * @return
     */
    List<EamRecentlyView> getDesignRecentlyView();

    List<EamRecentlyView> getAllRecentlyList();

}
