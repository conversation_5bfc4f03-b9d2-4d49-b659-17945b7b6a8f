package com.uino.service.cmdb.dataset.microservice.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.binary.core.exception.MessageException;
import com.binary.core.util.BinaryUtils;
import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClass;
import com.uinnova.product.vmdb.comm.model.rlt.CCcCiRlt;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uinnova.product.vmdb.provider.rlt.bean.CcCiRltInfo;
import com.uino.bean.cmdb.base.ESCIRltInfo;
import com.uino.bean.cmdb.base.dataset.DataSetMallApi;
import com.uino.bean.cmdb.base.dataset.log.DataSetMallApiLog;
import com.uino.bean.cmdb.query.ESRltSearchBean;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.permission.query.CSysUser;
import com.uino.dao.cmdb.dataset.ESDataSetMallApiLogSvc;
import com.uino.dao.util.ESUtil;
import com.uino.service.cmdb.dataset.microservice.IMallApiSvc;
import com.uino.service.cmdb.microservice.ICIRltSvc;
import com.uino.service.cmdb.microservice.IRltClassSvc;
import com.uino.service.permission.microservice.IUserSvc;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 关系
 *
 * @Date 2020/4/7 10:16
 * @Created by sh
 */
@Service
@Slf4j
public class MallApiRelSvc implements IMallApiSvc {


    @Autowired
    private ICIRltSvc iciRltSvc;
    @Autowired
    private IRltClassSvc iRltClassSvc;

    @Autowired
    private ESDataSetMallApiLogSvc esDataSetMallApiLogSvc;

    @Autowired
    private IUserSvc userSvc;

    @Override
    public DataSetMallApi checkCharacteristic(SysUser user, JSONObject jsonObject) {
        return null;
    }

    @Override
    public JSONObject execute(DataSetMallApi dataSetMallApi, JSONObject jsonObject) {
        long startTime = System.currentTimeMillis();
        Long domainId = dataSetMallApi.getDomainId();

        //2021-08-18 增加apikey校验
        String authentication = jsonObject.getString("authentication");
        String oauthAppName = jsonObject.getString("oauthAppName");

        String username = jsonObject.getString("username");
        String password = jsonObject.getString("password");
        //考虑code代码的查询，所以此处用了name
        List<String> relClassNames = jsonObject.getJSONArray("relClassNames").toJavaList(String.class);
        Integer pageNum = jsonObject.getInteger("pageNum");
        Integer pageSize = jsonObject.getInteger("pageSize");
        int relTotal = 0;
        boolean isSuccess = true;

        try {
            //2021-08-18 增加apikey校验 记录调用系统名称
            if (!BinaryUtils.isEmpty(authentication)) {
                username = oauthAppName;
            } else {
                // Base64解密
                // Base64解密
                password = new String(Base64.decodeBase64(password.getBytes(StandardCharsets.UTF_8)), StandardCharsets.UTF_8);
                password = DigestUtils.sha256Hex(password);
                //todo: 用户名密码直接查询判断，没有通过登陆
                CSysUser cSysUser = new CSysUser();
                cSysUser.setDomainId(domainId);
                cSysUser.setLoginCodeEqual(username);
                List<SysUser> listByCdt = userSvc.getSysUserByCdt(cSysUser);
                if (listByCdt == null || listByCdt.isEmpty()) {
                    throw MessageException.i18n("DCV_USERNAME_ERROR");
                } else if (!listByCdt.get(0).getLoginPasswd().equals(password)) {
                    throw MessageException.i18n("DCV_PASSWORD_ERROR");

                }
            }

            CCcCiClass cCcCiClass = new CCcCiClass();
            cCcCiClass.setDomainId(domainId);
            cCcCiClass.setClassNames(relClassNames.toArray(new String[0]));
            List<CcCiClassInfo> classInfos = iRltClassSvc.getRltClassByCdt(cCcCiClass);
            List<Long> classIds = new ArrayList<>();
            if (classInfos != null && !classInfos.isEmpty()) {
                classInfos.forEach(ccCiClassInfo -> classIds.add(ccCiClassInfo.getCiClass().getId()));
            }
            if (!classIds.isEmpty()) {
                CCcCiRlt cdt = new CCcCiRlt();
                cdt.setDomainId(domainId);
                cdt.setClassIds(classIds.toArray(new Long[0]));
                ESRltSearchBean bean = new ESRltSearchBean();
                bean.setDomainId(domainId);
                bean.setPageNum(pageNum);
                bean.setPageSize(pageSize);
                bean.setCdt(cdt);
                Page<ESCIRltInfo> page = iciRltSvc.searchRlt(bean);
                relTotal = page.getData().size();
                return JSONObject.parseObject(JSONObject.toJSONString(page));
            } else {
                throw MessageException.i18n("BS_MNAME_CLASS_NOT_EXSIT");
            }

        } catch (MessageException e) {
            log.error("", e);
            isSuccess = false;
            throw e;
        } finally {
            //保存日志
            DataSetMallApiLog dataSetMallApiLog = new DataSetMallApiLog();
            dataSetMallApiLog.setId(ESUtil.getUUID());
            dataSetMallApiLog.setDomainId(domainId);
            dataSetMallApiLog.setDataSetMallApiId(dataSetMallApi.getId());
            dataSetMallApiLog.setDataSetType(dataSetMallApi.getType());
            dataSetMallApiLog.setSuccess(isSuccess);
            dataSetMallApiLog.setRespUserCode(username);
            dataSetMallApiLog.setRespDuration(System.currentTimeMillis() - startTime);
            dataSetMallApiLog.setCreateTime(System.currentTimeMillis());
            dataSetMallApiLog.setCiTotal(0);
            dataSetMallApiLog.setRelTotal(relTotal);
            esDataSetMallApiLogSvc.saveOrUpdate(dataSetMallApiLog.toJson(), true);
        }
    }

    @Override
    public JSONObject realTimeExecute(DataSetMallApi dataSetMallApi, JSONObject jsonObject) {
        long startTime = System.currentTimeMillis();
        Long domainId = dataSetMallApi.getDomainId();
        String username = jsonObject.getString("username");
        String password = jsonObject.getString("password");
        JSONArray source = jsonObject.getJSONArray("source");
        JSONArray target = jsonObject.getJSONArray("target");
        int relTotal = 0;
        boolean isSuccess = true;

        try {
            // Base64解密
            // Base64解密
            password = new String(Base64.decodeBase64(password.getBytes("utf-8")), "utf-8");
            password = DigestUtils.sha256Hex(password);
            //todo: 用户名密码直接查询判断，没有通过登陆
            CSysUser cSysUser = new CSysUser();
            cSysUser.setDomainId(domainId);
            cSysUser.setLoginCodeEqual(username);
            List<SysUser> listByCdt = userSvc.getSysUserByCdt(cSysUser);
            if (listByCdt == null || listByCdt.isEmpty()) {
                throw MessageException.i18n("DCV_USERNAME_ERROR");
            } else if (!listByCdt.get(0).getLoginPasswd().equals(password)) {
                throw MessageException.i18n("DCV_PASSWORD_ERROR");
            }

            ESRltSearchBean bean = new ESRltSearchBean();
            bean.setDomainId(domainId);
            Set<String> sourceCiPrimaryKeys = new HashSet<String>();
            sourceCiPrimaryKeys.add(source.toString());
            bean.setSourceCiPrimaryKeys(sourceCiPrimaryKeys);
            Set<String> targetCiPrimaryKeys = new HashSet<String>();
            targetCiPrimaryKeys.add(target.toString());
            bean.setTargetCiPrimaryKeys(targetCiPrimaryKeys);
            Page<CcCiRltInfo> page = iciRltSvc.searchRltByBean(bean);
            relTotal = page.getData().size();
            return JSONObject.parseObject(JSONObject.toJSONString(page));
        } catch (Exception e) {
            isSuccess = false;
            throw new RuntimeException(e.getMessage());
        } finally {
            //保存日志
            DataSetMallApiLog dataSetMallApiLog = new DataSetMallApiLog();
            dataSetMallApiLog.setId(ESUtil.getUUID());
            dataSetMallApiLog.setDomainId(domainId);
            dataSetMallApiLog.setDataSetMallApiId(dataSetMallApi.getId());
            dataSetMallApiLog.setDataSetType(dataSetMallApi.getType());
            dataSetMallApiLog.setSuccess(isSuccess);
            dataSetMallApiLog.setRespUserCode(username);
            dataSetMallApiLog.setRespDuration(System.currentTimeMillis() - startTime);
            dataSetMallApiLog.setCreateTime(System.currentTimeMillis());
            dataSetMallApiLog.setCiTotal(0);
            dataSetMallApiLog.setRelTotal(relTotal);
            esDataSetMallApiLogSvc.saveOrUpdate(dataSetMallApiLog.toJson(), true);
        }
    }
}
