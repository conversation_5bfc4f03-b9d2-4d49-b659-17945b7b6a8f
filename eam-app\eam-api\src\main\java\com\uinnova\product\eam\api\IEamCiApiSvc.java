package com.uinnova.product.eam.api;

import com.uinnova.product.eam.model.CiQueryCdtExtend;
import com.uinnova.product.eam.model.VcCiClassInfoDto;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uino.bean.cmdb.base.LibType;

import java.util.List;

public interface IEamCiApiSvc {

    /**
     * 按指定条件查询CI，并按分类进行汇总
     *
     * @param cdt 查询条件
     * @param libType 库类型    // 私有库 PRIVATE, 设计库 DESIGN, 运行库/基线库 BASELINE
     * @return 包含CI数量的分类列表信息
     */
    List<VcCiClassInfoDto> queryCiCountByClass(CiQueryCdtExtend cdt, LibType libType);


    /**
     * 根据ci分类名称获取全部的ci信息
     *
     * @param ciClassName ci类型名称
     * @param libType     库类型
     * @return ci信息列表
     */
    List<CcCiInfo> getCiInfoListByClassName(String ciClassName, LibType libType);
}
