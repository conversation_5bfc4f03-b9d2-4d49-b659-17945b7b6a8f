package com.uinnova.product.vmdb.comm.doc.build;

import com.binary.core.bean.Bean;
import com.binary.core.bean.BeanManager;
import com.binary.core.bean.Property;
import com.binary.core.lang.Types;
import com.binary.core.util.BinaryUtils;
import com.binary.framework.exception.ServiceException;
import com.binary.framework.util.ControllerUtils;
import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uinnova.product.vmdb.comm.doc.annotation.MvcDesc;
import com.uinnova.product.vmdb.comm.doc.api.FieldDesc;
import com.uinnova.product.vmdb.comm.doc.api.MvcApi;
import com.uinnova.product.vmdb.comm.doc.api.MvcModApi;
import com.uinnova.product.vmdb.comm.i18n.MessageUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;

import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.lang.reflect.TypeVariable;
import java.util.*;

/**
 * 
 * <AUTHOR>
 *
 */
public abstract class MvcDocBuilder {
    private static final Logger logger = LoggerFactory.getLogger(MvcDocBuilder.class);

    /**
     * 跟据MVC类型生成api
     * 
     * @param mvcClass
     * @return
     */
    public static MvcApi buildMvcApi(Class<?> mvcClass) {
        logger.info(" start build mvc api '" + mvcClass.getName() + "'... ");

        RequestMapping mvcmp = mvcClass.getAnnotation(RequestMapping.class);
        MessageUtil.checkEmpty(mvcmp, "requestMapping");
        MessageUtil.checkEmpty(mvcmp.value(), "requestMapping.value");
        MessageUtil.checkEmpty(mvcmp.value()[0], "requestMapping.value");

        MvcDesc mvcDesc = mvcClass.getAnnotation(MvcDesc.class);
        if (mvcDesc == null) {
            return null;
        }
        MessageUtil.checkEmpty(mvcDesc, "mvcDesc");
        MessageUtil.checkEmpty(mvcDesc.author(), "mvcDesc.author");
        MessageUtil.checkEmpty(mvcDesc.desc(), "mvcDesc.desc");

        MvcApi mvcApi = new MvcApi();
        mvcApi.setMvc(mvcClass.getName());
        String name = mvcClass.getName();
        name = name.substring(name.lastIndexOf(".") + 1);
        mvcApi.setClassName(name);
        mvcApi.setPath(ControllerUtils.formatContextPath(mvcmp.value()[0]));
        mvcApi.setAuthor(mvcDesc.author());
        mvcApi.setDesc(mvcDesc.desc());

        List<MvcModApi> modApis = new ArrayList<MvcModApi>();
        mvcApi.setModApis(modApis);

        Method[] mods = mvcClass.getMethods();
        if (mods != null && mods.length > 0) {
            for (int i = 0; i < mods.length; i++) {
                MvcModApi modApi = buildMvcModApi(mvcApi, mods[i]);
                if (modApi != null) {
                    modApis.add(modApi);
                }
            }
        }

        return mvcApi;
    }

    private static MvcModApi buildMvcModApi(MvcApi mvcApi, Method mod) {
        RequestMapping rm = mod.getAnnotation(RequestMapping.class);
        if (rm == null) {
            return null;
        }

        ModDesc md = mod.getAnnotation(ModDesc.class);

        if (BinaryUtils.isEmpty(rm.value()) || BinaryUtils.isEmpty(rm.value()[0])) {
            throw new ServiceException(" not setting method annotation requestMapping '" + mvcApi.getMvc() + "." + mod.getName() + "()'! ");
        }
        if (md == null) {
            return null;
        }

        MessageUtil.checkEmpty(md.desc(), " modDesc '" + mvcApi.getMvc() + "." + mod.getName() + "() - desc'");
        MessageUtil.checkEmpty(md.rType(), " modDesc '" + mvcApi.getMvc() + "." + mod.getName() + "() - rType'");

        MvcModApi modApi = new MvcModApi();
        modApi.setModName(mod.getName());
        modApi.setPath(ControllerUtils.formatContextPath(rm.value()[0]));
        modApi.setFullPath(mvcApi.getPath() + ControllerUtils.formatContextPath(rm.value()[0]));
        modApi.setDesc(md.desc());

        Set<Class<?>> beanSet = new HashSet<Class<?>>();

        if (md.pType() != null && md.pType() != void.class) {
            FieldDesc paramDesc = parseApiField(beanSet, mvcApi, modApi, md.pType(), md.pcType(), md.pDesc());
            modApi.setParameterDesc(paramDesc);
        }

        beanSet.clear();
        FieldDesc resultDesc = parseApiField(beanSet, mvcApi, modApi, md.rType(), md.rcType(), md.rDesc());
        modApi.setResultDesc(resultDesc);

        return modApi;
    }

    private static FieldDesc parseApiField(Set<Class<?>> beanSet, MvcApi mvcApi, MvcModApi modApi, Class<?> type, Class<?> componentType, String desc) {
        if (type == null || type == void.class) {
            return null;
        }

        FieldDesc fd = new FieldDesc();
        fd.setDesc(desc);
        String typeName = type.getSimpleName();

        if (componentType == null || componentType == void.class) {
            boolean isarray = BinaryUtils.isArray(type);
            String endStr = "[]";
            if (isarray && !typeName.endsWith(endStr)) {
                typeName += "[]";
            }
            fd.setType(typeName);

            Class<?> beanType = type;
            if (type.isArray()) {
                beanType = type.getComponentType();
            }

            if (Types.isBean(beanType)) {
                fd.setDtype(isarray ? 4 : 3);
                fd.setChilds(parseBeanField(beanSet, mvcApi, modApi, beanType, null));
            } else {
                fd.setDtype(isarray ? 2 : 1);
            }
        } else {
            if (type.isArray()) {
                throw new ServiceException(" is wrong field type '" + mvcApi.getMvc() + "." + modApi.getModName() + "() - " + type.getSimpleName() + "[]<" + componentType.getSimpleName() + ">'! ");
            }
            if (List.class.isAssignableFrom(type)) {
                typeName = componentType.getSimpleName() + "[]";
                fd.setType(typeName);
                if (Types.isBean(componentType)) {
                    fd.setDtype(4);
                    fd.setChilds(parseBeanField(beanSet, mvcApi, modApi, componentType, null));
                } else {
                    fd.setDtype(2);
                }
            } else {
                fd.setType(type.getSimpleName() + "<" + componentType.getSimpleName() + ">");
                if (Types.isBean(type) || Types.isBean(componentType)) {
                    fd.setDtype(3);
                    fd.setChilds(parseBeanField(beanSet, mvcApi, modApi, type, componentType));
                }
            }
        }

        return fd;
    }

    // private static File getJavaFile(File root, Class<?> type) {
    // String name = type.getName();
    // String itvopPrefix = "com.uinnova.project.itvop.";
    // if(!name.startsWith(itvopPrefix)) {
    // throw new ServiceException(" not found java file '"+type+"'! ");
    // }
    //
    // String sub = name.substring(itvopPrefix.length()-6);
    // String proname = "";
    //
    // if(sub.startsWith("itvop.comm.")) {
    // proname = "itvop-comm";
    // }else if(sub.startsWith("itvop.web.")) {
    // sub = sub.substring("itvop.web.".length());
    // if(sub.startsWith(".portal")) {
    // proname = "itvop-web";
    // }else {
    // proname = "itvop-web-"+sub.substring(0, sub.indexOf('.'));
    // }
    // }else if(sub.startsWith("itvop.provider.")) {
    // sub = sub.substring("itvop.provider.".length());
    // proname = "itvop-provider-"+sub.substring(0, sub.indexOf('.'));
    // }
    //
    // File file = new File(root, proname+"/src/main/java/"+name.replace('.', '/')+".java");
    // if(!file.isFile()) {
    // throw new ServiceException(" not found java-file '"+file.getPath()+"'! ");
    // }
    // return file;
    //
    // }

    private static List<FieldDesc> parseBeanField(Set<Class<?>> beanSet, MvcApi mvcApi, MvcModApi modApi, Class<?> type, Class<?> componentType) {
        List<FieldDesc> fields = new ArrayList<FieldDesc>();

        if (type == Page.class) {
            fields.add(new FieldDesc("pageNum", "Long", 1, "页码"));
            fields.add(new FieldDesc("pageSize", "Long", 1, "每页记录数"));
            fields.add(new FieldDesc("totalRows", "Long", 1, "总记录数"));
            fields.add(new FieldDesc("totalPages", "Long", 1, "总页数"));

            if (componentType != null) {
                FieldDesc field = new FieldDesc("data", componentType.getSimpleName() + "[]", 4, "数据");
                List<FieldDesc> childs = parseBeanField(beanSet, mvcApi, modApi, componentType, null);
                field.setChilds(childs);
                fields.add(field);
            }
        } else {
            if (beanSet.contains(type)) {
                return fields;
            }
            beanSet.add(type);

            Map<String, String> map = JavaBeanParser.getFieldDesc(type);

            Bean<?> bean = BeanManager.getBean(type);
            Iterator<Property> itor = bean.getPropertyIterator();
            while (itor.hasNext()) {
                Property pro = itor.next();
                Class<?> pt = pro.getType();
                Type pgt = pro.getGenericType();
                String name = pro.getName();
                String desc = map.get(name);

                boolean isarr = BinaryUtils.isArray(pt);
                boolean isbean = false;

                if (isarr) {
                    if (List.class.isAssignableFrom(pt)) {
                        if (!(pgt instanceof ParameterizedType)) {
                            throw new ServiceException(" is wrong type by property at '" + type.getName() + "." + name + "'! ");
                        }
                        Type t = Types.getGenericType((ParameterizedType) pgt);
                        if (!(t instanceof Class)) {
                            continue;
                        }
                        pt = (Class<?>) t;
                    } else {
                        pt = pt.getComponentType();
                    }
                }

                if (pgt instanceof TypeVariable) {
                    pt = componentType;
                }

                isbean = Types.isBean(pt);

                String fn = pt.getSimpleName();
                if (isarr && !fn.endsWith("[]")) {
                    fn += "[]";
                }
                FieldDesc fd = new FieldDesc(name, fn, (isarr ? (isbean ? 4 : 2) : (isbean ? 3 : 1)), desc);
                fields.add(fd);

                if (isbean) {
                    fd.setChilds(parseBeanField(beanSet, mvcApi, modApi, pt, null));
                }
            }
        }

        return fields;
    }

}
