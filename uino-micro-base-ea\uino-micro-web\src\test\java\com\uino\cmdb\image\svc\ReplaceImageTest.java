package com.uino.cmdb.image.svc;

import java.io.File;
import java.io.FileInputStream;
import java.util.Collections;

import org.elasticsearch.index.query.QueryBuilders;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.util.ReflectionTestUtils;

import com.binary.core.exception.MessageException;
import com.uino.dao.cmdb.ESImageSvc;
import com.uino.service.cmdb.microservice.impl.ImageSvc;
import com.uino.service.sys.microservice.impl.ResourceSvc;
import com.uino.bean.cmdb.base.CcImage;

public class ReplaceImageTest {
	@InjectMocks
	private ImageSvc testSvc;
	private ESImageSvc esImageSvc;
	private String localPath;
    private ResourceSvc resourceSvc;

	@Before
	public void before() {
		MockitoAnnotations.initMocks(this);
		esImageSvc = Mockito.mock(ESImageSvc.class);
        resourceSvc = Mockito.mock(ResourceSvc.class);
		localPath = "./src/test/resources";
		ReflectionTestUtils.setField(testSvc, "localPath", localPath);
		ReflectionTestUtils.setField(testSvc, "svc", esImageSvc);
        ReflectionTestUtils.setField(testSvc, "resourceSvc", resourceSvc);

		Mockito.when(esImageSvc.getListByQuery(Mockito.eq(QueryBuilders.termQuery("id", 2L))))
				.thenReturn(Collections.emptyList());

		CcImage img = new CcImage();
		img.setId(1L);
		img.setImgPath("/testdata/10006old.png");
		Mockito.when(esImageSvc.getListByQuery(Mockito.eq(QueryBuilders.termQuery("id", 1L))))
				.thenReturn(Collections.singletonList(img));

		Mockito.when(esImageSvc.saveOrUpdate(Mockito.any())).thenReturn(1L);
        Mockito.when(resourceSvc.saveSyncResourceInfo(Mockito.anyString(), Mockito.anyString(), Mockito.anyBoolean(), Mockito.anyInt())).thenReturn("resourcePath");
	}

	@Test(expected = IllegalArgumentException.class)
	public void test01() {
		testSvc.replaceImage(null, null);
	}

	@Test
	public void test02() {

		try {
			FileInputStream infs = new FileInputStream(localPath + "/testdata/10007.error");
			MockMultipartFile imgPng = new MockMultipartFile("10007", "10007.error", "", infs);
			boolean res = testSvc.replaceImage(2L, imgPng);
			Assert.assertFalse(res);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			throw new MessageException(e.getMessage());
		}

	}

	@Test
	public void test03() {
		try {
			File oldF = new File(localPath + "/testdata/10006old.png");
			oldF.createNewFile();
			FileInputStream infs = new FileInputStream(localPath + "/testdata/10006.png");
			MockMultipartFile imgPng = new MockMultipartFile("10006", "10006.png", "", infs);
			boolean res = testSvc.replaceImage(1L, imgPng);
			oldF.delete();
			Assert.assertTrue(res);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			throw new MessageException(e.getMessage());
		}
	}

}
