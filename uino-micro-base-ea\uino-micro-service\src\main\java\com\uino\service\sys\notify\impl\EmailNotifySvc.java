package com.uino.service.sys.notify.impl;

import java.io.File;
import java.util.Iterator;
import java.util.Properties;
import java.util.Set;
import java.util.regex.Pattern;

import jakarta.mail.internet.MimeMessage;
import jakarta.mail.internet.MimeUtility;

import com.uino.service.sys.microservice.INotifyChannelSvc;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.MailAuthenticationException;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import com.binary.core.util.BinaryUtils;
import com.uino.service.sys.notify.INotifySvc;
import com.uino.bean.sys.base.NotifyChannel;
import com.uino.bean.sys.base.NotifyChannel.EmailChannelInfo;
import com.uino.bean.sys.base.NotifyTypeConstants;
import com.uino.bean.sys.business.NotifyData;

import lombok.extern.slf4j.Slf4j;

/**
 * 邮件通知
 *
 * @author: weixuesong
 * @create: 2020/07/02 10:48
 **/
@Service(value = NotifyTypeConstants.EMAIL)
@Slf4j
public class EmailNotifySvc implements INotifySvc {

	@Autowired
	private INotifyChannelSvc channelSvc;

	@Value("${mail.smtp.ssl.enable : false}")
	private boolean sslEnable;

	@Override
	public boolean sendNotify(NotifyData notifyData) {
		NotifyChannel notifyChannel = channelSvc.getNotifyChannelById(notifyData.getChannelId());
		EmailChannelInfo emailChannel = notifyChannel.getEmailChannelInfo();
		log.info("send email notify======>>>>" + notifyData.getTitle());
		// 过滤邮箱地址
		Set<String> toAddress = notifyData.getNotifyAddress();
		String regexEmail = "\\w[-\\w.+]*@([a-z0-9A-Z]+(-[a-z0-9A-Z]+)?\\.)+[a-zA-Z]{2,}$";
		Iterator<String> it = toAddress.iterator();
		while (it.hasNext()) {
			String emailAdress = it.next();
			if (!Pattern.matches(regexEmail, emailAdress)) {
				log.info("邮箱[" + emailAdress + "]格式错误，已丢弃！");
				it.remove();
			}
		}
		if (BinaryUtils.isEmpty(toAddress)) {
			return true;
		}
		// 创建邮件发送服务器
		JavaMailSenderImpl mailSender = new JavaMailSenderImpl();
		mailSender.setHost(emailChannel.getSmtpHost());
		mailSender.setPort(Integer.parseInt(emailChannel.getSmtpPort()));
		mailSender.setUsername(emailChannel.getSmtpUser());
		mailSender.setPassword(emailChannel.getSmtpPwd());
		// 加认证机制
		Properties javaMailProperties = new Properties();
		javaMailProperties.put("mail.smtp.auth", false);
		javaMailProperties.put("mail.smtp.starttls.enable", true);
		javaMailProperties.put("mail.smtp.timeout", 20000);
		if (sslEnable) {
			javaMailProperties.put("mail.smtp.ssl.enable", true);
			javaMailProperties.put("mail.smtp.socketFactory.class", "javax.net.ssl.SSLSocketFactory");
			javaMailProperties.put("mail.smtp.socketFactory.port", Integer.parseInt(emailChannel.getSmtpPort()));
		}
		mailSender.setJavaMailProperties(javaMailProperties);
		// 创建邮件内容
		try {
			MimeMessage mailMessage = mailSender.createMimeMessage();
			MimeMessageHelper messageHelper = new MimeMessageHelper(mailMessage, true, "utf-8");
			messageHelper.setFrom(emailChannel.getSmtpUser());
			messageHelper.setTo(toAddress.toArray(new String[] {}));
			messageHelper.setSubject(notifyData.getTitle());
			messageHelper.setText(notifyData.getContent());
			// 添加附件
			if (!BinaryUtils.isEmpty(notifyData.getMailAttachments())) {
				for (String key : notifyData.getMailAttachments().keySet()) {
					String path = notifyData.getMailAttachments().get(key);
					File file = new File(path);
					if (file.exists()) {
						messageHelper.addAttachment(MimeUtility.encodeWord(key), file);
					}else {
						log.error("附件["+path+"]异常，已丢弃！");
					}
				}
			}
			// 发送邮件
			mailSender.send(mailMessage);
		} catch (MailAuthenticationException e) {
			Assert.isTrue(false, "邮件认证失败，请检查渠道密码配置！");
		} catch (Exception e) {
			log.error("Send email error", e);
			Assert.isTrue(false, "邮件发送失败，请检查渠道配置！");
		}
		log.info("send email notify successfully!");
		return true;
	}

}
