package com.uinnova.product.eam.model.diagram;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.model.cj.vo.DiagramPlanVO;
import com.uinnova.product.eam.model.dto.EamCategoryDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
@Comment("我的空间及资产仓库资源信息返回体")
public class SpaceResourceResultInfo implements Serializable {
	private static final long serialVersionUID = 1L;

	@Comment("当前文件夹信息")
	private EamCategoryDTO category;

	@Comment("子文件夹信息")
	private List<EamCategoryDTO> childrenDirs = new ArrayList<>();

	@Comment("视图信息及方案信息")
	private List<DiagramPlanVO> diagramPlanList = new ArrayList<>();

	@Comment("数据模型标识")
	private boolean dataModelFlag = false;

}
