package com.uinnova.product.eam.service.impl;

import com.uinnova.product.eam.model.TrialSaasTagInfo;
import com.uinnova.product.eam.model.TrialSaasVo;
import com.uinnova.product.eam.service.TrialSaasSvc;
import com.uino.bean.permission.SysTypeConfig;
import com.uino.dao.permission.ESSysTypeConfigSvc;
import com.uino.dao.util.ESUtil;
import com.uino.license.sdk.license.License;
import com.uino.service.license.microservice.ILicenseAuthSvc;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;

@Slf4j
@Service
public class TrialSaasSvcImpl implements TrialSaasSvc {

    private static final String LICENSE_UN_VALID_MSG = "已过14天试用期，如有需求，请联系商务400-051-0152";

    @Autowired
    private ILicenseAuthSvc licenseAuthSvc;
    @Autowired
    private ESSysTypeConfigSvc sysTypeConfigSvc;

    @Override
    public TrialSaasVo loginCheck(String loginCode) {
        SysTypeConfig sysTypeConfig = this.getSysTypeConfig();
        TrialSaasVo result = new TrialSaasVo();
        result.setType(sysTypeConfig.getType());
        //是否Saas试用版
        if (!sysTypeConfig.getType().equals(1)) {
            return result;
        }
        //未开通license或是superadmin，不禁用登录，方便许可证申请
        if ("superadmin".equals(loginCode)) {
            result.setValid(true);
            return result;
        }
        //是否有效
        License license = licenseAuthSvc.getRealLicense();
        if (license == null) {
            result.setValid(false);
            return result;
        }
        result.setValid(license.getValid());
        if (Boolean.FALSE.equals(license.getValid())) {
            result.setMessage(LICENSE_UN_VALID_MSG);
        }
        return result;
    }

    @Override
    public TrialSaasTagInfo tagInfo() {
        SysTypeConfig sysTypeConfig = this.getSysTypeConfig();
        TrialSaasTagInfo tagInfo = new TrialSaasTagInfo();
        tagInfo.setType(sysTypeConfig.getType());
        //是否Saas试用版
        if (!sysTypeConfig.getType().equals(1)) {
            return tagInfo;
        }
        //是否有效
        License license = licenseAuthSvc.getRealLicense();
        if (license == null) {
            tagInfo.setValid(false);
            return tagInfo;
        }
        tagInfo.setValid(license.getValid());
        if (Boolean.TRUE.equals(tagInfo.getValid())) {
            //过期天数（过期日期计算所得）
            int expiredDay = daysBetween(ESUtil.getNumberDate() + "", license.getAuthEndDate() + "");
            //剩余使用天数
            int remainingUseDay =  (int)(license.getAuthEndDate() - (license.getOnlineTime() / 60 / 24));
            tagInfo.setLicenseValidTime(Math.min(expiredDay, remainingUseDay));
        }
        return tagInfo;
    }


    private int daysBetween(String currentDate, String endDate) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        Calendar cal = Calendar.getInstance();
        int betweenDays = -1;
        try {
            cal.setTime(sdf.parse(currentDate));
            long currentTime = cal.getTimeInMillis();

            cal.setTime(sdf.parse(endDate));
            cal.add(Calendar.DAY_OF_MONTH, 1);
            long endTime = cal.getTimeInMillis();
            long day = (endTime - currentTime) / (1000 * 3600 * 24);
            if (day <= 0L) {
                return betweenDays;
            }else {
                return Integer.parseInt(String.valueOf(day));
            }

        } catch (ParseException e) {
            log.error("授权日期有误", e);
        }
        return betweenDays;
    }

    private SysTypeConfig getSysTypeConfig() {
        return sysTypeConfigSvc.getListByCdt(new SysTypeConfig()).get(0);
    }

    @Override
    public void changeSysType(Integer type) {
        SysTypeConfig sysTypeConfig = this.getSysTypeConfig();
        sysTypeConfig.setType(type);
        sysTypeConfigSvc.saveOrUpdate(sysTypeConfig);
    }
}
