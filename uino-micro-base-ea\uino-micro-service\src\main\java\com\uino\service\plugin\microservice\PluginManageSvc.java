package com.uino.service.plugin.microservice;

import com.binary.core.io.Resource;
import com.binary.jdbc.Page;
import com.uino.bean.plugin.base.ESPluginInfo;
import com.uino.bean.plugin.query.CPluginInfo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 插件管理
 * @Date 2021/9/27
 * @Version 1.0
 */
public interface PluginManageSvc {

    /**
     * 获取所有服务
     *
     * @return
     */
    List<String> getServiceList();

    /**
     * 添加插件
     *
     * @param pluginInfo
     * @return
     */
    Long saveOrUpdate(ESPluginInfo pluginInfo);

    /**
     * 上传插件
     *
     * @param file
     * @return
     */
    String uploadPlugin(Long id, MultipartFile file);

    /**
     * 同步插件
     *
     * @param file
     * @return
     */
    boolean syncPlugin(MultipartFile file);

    /**
     * 下载插件
     *
     * @param id
     * @return
     */
    Resource downloadPlugin(Long id);


    Page<ESPluginInfo> queryList(int pageNum, int pageSize, CPluginInfo cPluginInfo);

    /**
     * 加载卸载插件
     *
     * @param id
     * @return
     */
    boolean loadOrUnloadPlugin(Long id);


    /**
     * 删除插件
     *
     * @param id
     * @return
     */
    boolean deletePlugin(Long id);

    /**
     * 查询服务所要加载的插件名称集合
     *
     * @param serverName
     * @return
     */
    List<String> queryListByOpenServer(String serverName);
}
