package com.uino.api.client.monitor.rpc;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.binary.jdbc.Page;
import com.uino.api.client.monitor.ISimulationRuleApiSvc;
import com.uino.bean.monitor.base.SimulationRuleInfo;
import com.uino.bean.monitor.query.SimulationRuleSearchBean;
import com.uino.provider.feign.monitor.SimulationRuleFeign;

@Service
public class SimulationRuleApiSvcRpc implements ISimulationRuleApiSvc {

	@Autowired
	private SimulationRuleFeign ruleFeign;

	@Override public Page<SimulationRuleInfo> querySimulationRuleInfoPage(SimulationRuleSearchBean bean) {
		return ruleFeign.querySimulationRuleInfoPage(bean);
	}

	@Override
	public List<SimulationRuleInfo> querySimulationRuleInfoList(SimulationRuleSearchBean bean) {
		return ruleFeign.querySimulationRuleInfoList(bean);
	}

	@Override
	public Long saveOrUpdateSimulationRule(SimulationRuleInfo ruleInfo) {
		return ruleFeign.saveOrUpdateSimulationRule(ruleInfo);
	}

	@Override
	public Integer deleteSimulationRuleById(Long id) {
		return ruleFeign.deleteSimulationRuleById(id);
	}

}
