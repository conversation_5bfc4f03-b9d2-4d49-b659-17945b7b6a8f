package com.uinnova.product.eam.model.cj.domain;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.base.cj.BaseEntity;
import lombok.Data;

/**
 * <AUTHOR>
 * @Classname ChapterResource
 * @Date 2022/6/21 15:55
 */
@Data
@Comment("交付物模板章节资源表[UINO_CJ_CHAPTER_RESOURCE]")
public class ChapterResource extends BaseEntity {
    private Long id;

    private Long templateId;

    private Long chapterId;

    private String resourceName;

    private String resourceType;

    private String resourcePath;

    private String operator;
}
