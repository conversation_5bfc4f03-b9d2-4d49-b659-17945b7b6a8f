package com.uino.bean.permission.business;

import java.util.List;

import com.uino.bean.permission.base.SysModule;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 模块节点信息
 * 
 * <AUTHOR>
 */
@Getter
@Setter
@ApiModel(value="模块节点信息",description = "模块节点信息")
public class ModuleNodeInfo extends SysModule {

	/**
	 *
	 */
	private static final long serialVersionUID = 1L;

    @ApiModelProperty(value="孩子节点")
	private List<ModuleNodeInfo> children;;

	/** 是否收藏,1 收藏, 0 未收藏 */
	@ApiModelProperty(value="是否收藏，1 收藏, 0 未收藏",example = "1")
	private Integer isFavorite;

	/** 是否展开,1 展开,0 收起 */
	@ApiModelProperty(value="是否展开,1 展开,0 收起",example = "1")
	private Integer isOpen;

}
