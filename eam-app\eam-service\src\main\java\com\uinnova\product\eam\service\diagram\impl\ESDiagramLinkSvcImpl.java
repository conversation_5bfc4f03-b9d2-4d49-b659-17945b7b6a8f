package com.uinnova.product.eam.service.diagram.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.binary.core.util.BinaryUtils;
import com.uinnova.product.eam.base.diagram.model.ESDiagramLink;
import com.uinnova.product.eam.base.diagram.model.ESResponseStruct;
import com.uinnova.product.eam.db.diagram.es.ESDiagramLinkDao;
import com.uinnova.product.eam.service.diagram.ESDiagramLinkSvc;
import com.uinnova.product.vmdb.comm.i18n.MessageUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 视图线接口业务实现
 * <AUTHOR>
 */
@Service
@Slf4j
public class ESDiagramLinkSvcImpl implements ESDiagramLinkSvc {
    @Autowired
    private ESDiagramLinkDao diagramLinkDao;

    @Override
    public List<ESDiagramLink> getLinkByDiagram(Collection<Long> diagramIds, Collection<String> sheetIds) {
        Assert.notEmpty(diagramIds, "视图id");
        BoolQueryBuilder nodeQuery = QueryBuilders.boolQuery();
        nodeQuery.must(QueryBuilders.termsQuery("diagramId", diagramIds));
        if(CollectionUtils.isNotEmpty(sheetIds)){
            nodeQuery.must(QueryBuilders.termsQuery("sheetId.keyword", sheetIds));
        }
        List<ESDiagramLink> linkList = diagramLinkDao.getListByQueryScroll(nodeQuery);
        if(CollectionUtils.isEmpty(linkList)){
            return Collections.emptyList();
        }
        return linkList;
    }

    @Override
    public List<ESDiagramLink> getDiagramLink(Long diagramId, Collection<String> sheetIds, Collection<String> keys) {
        Assert.notNull(diagramId, "视图id");
        BoolQueryBuilder nodeQuery = QueryBuilders.boolQuery();
        nodeQuery.must(QueryBuilders.termQuery("diagramId", diagramId));
        if(CollectionUtils.isNotEmpty(sheetIds)){
            nodeQuery.must(QueryBuilders.termsQuery("sheetId.keyword", sheetIds));
        }
        if(CollectionUtils.isNotEmpty(keys)){
            nodeQuery.must(QueryBuilders.termsQuery("key.keyword", keys));
        }
        List<ESDiagramLink> linkList = diagramLinkDao.getListByQueryScroll(nodeQuery);
        if(CollectionUtils.isEmpty(linkList)){
            return Collections.emptyList();
        }
        return linkList;
    }

    @Override
    public Integer saveOrUpdateBatch(List<ESDiagramLink> links) {
        return diagramLinkDao.saveOrUpdateBatch(links);
    }

    @Override
    public void deleteByDiagramIds(Collection<Long> diagramIds){
        if(CollectionUtils.isEmpty(diagramIds)){
            return;
        }
        diagramLinkDao.deleteByQuery(QueryBuilders.termsQuery("diagramId", diagramIds), true);
    }

    @Override
    public List<ESResponseStruct> saveOrUpdateLink(Long diagramId, String sheetId, String opList, Boolean flag) {
        MessageUtil.checkEmpty(diagramId, "保存或更新link时diagramId不能为空");
        MessageUtil.checkEmpty(sheetId, "保存或更新link时sheetId不能为空");
        List<ESDiagramLink> saveOrUpdateLinkList = JSONUtil.toList(opList, ESDiagramLink.class);

        for (ESDiagramLink eachLink : saveOrUpdateLinkList) {
            eachLink.setSheetId(sheetId);
            eachLink.setDiagramId(diagramId);
            eachLink.setUniqueCode(null);
            if (!BinaryUtils.isEmpty(eachLink.getLinkJson())) {
                JSONObject linkJson = JSONObject.parseObject(eachLink.getLinkJson());
                String rltCode = linkJson.getString("rltCode");
                if (BinaryUtils.isEmpty(rltCode)) {
                    continue;
                }
                eachLink.setUniqueCode(rltCode);
            }
        }
        List<ESResponseStruct> structList = new ArrayList<>();
        //更新操作
        if (flag) {
            //获取所有的sheetId，并根据sheetId查出其对应的sheet对象
            List<String> linkKeys = saveOrUpdateLinkList.stream().map(ESDiagramLink::getKey).distinct().collect(Collectors.toList());
            if (BinaryUtils.isEmpty(linkKeys)) {
                ESResponseStruct struct = new ESResponseStruct();
                struct.setErrorMessage("更新link时需要设置linkKey");
                structList.add(struct);
                return structList;
            }
            List<ESDiagramLink> originLinkList = this.getDiagramLink(diagramId, Collections.singleton(sheetId), linkKeys);
            //建立sheetId和originId之间的映射关系
            Map<String, Long> linkIdMap = new HashMap<>();
            if (!BinaryUtils.isEmpty(originLinkList)) {
                for (ESDiagramLink originLink : originLinkList) {
                    linkIdMap.put(originLink.getKey(), originLink.getId());
                }
            } else {
                return structList;
            }
            if (BinaryUtils.isEmpty(linkIdMap)) {
                return structList;
            }
            List<ESDiagramLink> skipLinkList = new ArrayList<>();
            for (ESDiagramLink updateLink : saveOrUpdateLinkList) {
                String linkKey = updateLink.getKey();
                Long linkLogicId = linkIdMap.get(linkKey);
                if (!BinaryUtils.isEmpty(linkLogicId)) {
                    updateLink.setId(linkLogicId);
                } else {
                    skipLinkList.add(updateLink);
                }
            }
            if (!BinaryUtils.isEmpty(skipLinkList)) {
                saveOrUpdateLinkList.removeAll(skipLinkList);
            }
            if (!BinaryUtils.isEmpty(saveOrUpdateLinkList)) {
                diagramLinkDao.saveOrUpdateBatch(saveOrUpdateLinkList);
            }
        } else {
            //新增操作，需要设置diagramId
            diagramLinkDao.saveOrUpdateBatch(saveOrUpdateLinkList);
        }
        return structList;
    }
}
