package com.uinnova.product.eam.db.impl;


import com.binary.framework.dao.DaoDefinition;

import com.uinnova.product.eam.comm.model.CVcCiLinked;
import com.uinnova.product.eam.comm.model.VcCiLinked;


/**
 * CI链路表[VC_CI_LINKED]数据访问对象定义实现
 */
public class VcCiLinkedDaoDefinition implements DaoDefinition<VcCiLinked, CVcCiLinked> {


	@Override
	public Class<VcCiLinked> getEntityClass() {
		return VcCiLinked.class;
	}


	@Override
	public Class<CVcCiLinked> getConditionClass() {
		return CVcCiLinked.class;
	}


	@Override
	public String getTableName() {
		return "VC_CI_LINKED";
	}


	@Override
	public boolean hasDataStatusField() {
		return true;
	}


	@Override
	public void setDataStatusValue(VcCiLinked record, int status) {
		record.setDataStatus(status);
	}


	@Override
	public void setDataStatusValue(CVcCiLinked cdt, int status) {
		cdt.setDataStatus(status);
	}


	@Override
	public void setCreatorValue(VcCiLinked record, String creator) {
		record.setCreator(creator);
	}


	@Override
	public void setModifierValue(VcCiLinked record, String modifier) {
		record.setModifier(modifier);
	}


}


