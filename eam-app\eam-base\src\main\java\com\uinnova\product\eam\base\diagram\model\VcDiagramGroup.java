package com.uinnova.product.eam.base.diagram.model;




import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;


@Comment("视图组表[VC_DIAGRAM_GROUP]")
public class VcDiagramGroup implements EntityBean {
	private static final long serialVersionUID = 1L;


	@Comment("ID[ID]")
	private Long id;


	@Comment("视图ID[DIAGRAM_ID]")
	private Long diagramId;


	@Comment("组ID[GROUP_ID]")
	private Long groupId;


	@Comment("发布人[DEPLOY_USER_ID]")
	private Long deployUserId;


	@Comment("发布时间[DEPLOY_TIME]")
	private Long deployTime;


	@Comment("所属域[DOMAIN_ID]")
	private Long domainId;


	@Comment("创建时间[CREATE_TIME]    yyyyMMddHHmmss")
	private Long createTime;


	@Comment("修改时间[MODIFY_TIME]    yyyyMMddHHmmss")
	private Long modifyTime;




	public Long getId() {
		return this.id;
	}
	public void setId(Long id) {
		this.id = id;
	}


	public Long getDiagramId() {
		return this.diagramId;
	}
	public void setDiagramId(Long diagramId) {
		this.diagramId = diagramId;
	}


	public Long getGroupId() {
		return this.groupId;
	}
	public void setGroupId(Long groupId) {
		this.groupId = groupId;
	}


	public Long getDeployUserId() {
		return this.deployUserId;
	}
	public void setDeployUserId(Long deployUserId) {
		this.deployUserId = deployUserId;
	}


	public Long getDeployTime() {
		return this.deployTime;
	}
	public void setDeployTime(Long deployTime) {
		this.deployTime = deployTime;
	}


	public Long getDomainId() {
		return this.domainId;
	}
	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}


	public Long getCreateTime() {
		return this.createTime;
	}
	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}


	public Long getModifyTime() {
		return this.modifyTime;
	}
	public void setModifyTime(Long modifyTime) {
		this.modifyTime = modifyTime;
	}


}


