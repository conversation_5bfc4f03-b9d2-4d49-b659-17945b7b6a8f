package com.uinnova.product.eam.service;

import com.uinnova.product.eam.model.CiQueryCdtExtend;
import com.uinnova.product.eam.model.VcCiClassInfoDto;
import com.uinnova.product.eam.model.vo.EamAnalyseCiVo;
import com.uinnova.product.eam.model.dto.AnalysePathDto;
import com.uinnova.product.eam.model.dto.DataAnalyzeBatch;
import com.uinnova.product.eam.model.enums.AnalyseLeaf;
import com.uinnova.product.eam.model.vo.EamAnalyseTableVo;
import org.springframework.http.ResponseEntity;

import java.util.List;
import java.util.Map;

/**
 * 数据分析
 * <AUTHOR>
 */
public interface IBmDataAnalyzeSvc {
    /**
     * 查询元模型中所有分类
     * @return
     * @param cdt
     */
    List<VcCiClassInfoDto> queryClassInfoByMetaModel(CiQueryCdtExtend cdt);

    /**
     * 获取ci对象的管理数据
     * @param ciCode
     * @param position
     * @return
     */
    Map<String, Object> queryByCiCode(String ciCode, Integer position);

    /**
     * 通过根节点查询路径图
     * @param ciCode
     * @return
     */
    AnalysePathDto queryPath(String ciCode);

    /**
     * 通过路径或层级查询多节点关联分析数据
     * @param ciCode
     * @param leaf
     * @param path
     * @return
     */
    List<DataAnalyzeBatch> queryByPath(String ciCode, AnalyseLeaf leaf, List<String> path);

    /**
     * 查询ci并获取源端及目标端关联ci数量
     * @param ciCodes
     * @return
     */
    List<EamAnalyseCiVo> queryByCiCodes(List<String> ciCodes);


    /**
     * 由根节点查询关联分析全部上游或者全部上游节点关系数据
     * @param ciCode 根节点ciCode
     * @param leaf 上游（leaf=from）/下游（leaf=to）参数
     * @return 所有层级节点及关系数据
     */
    List<DataAnalyzeBatch> queryPathOneSide(String ciCode,AnalyseLeaf leaf,String rootCiCode);

    /**
     * 关联分析表格导出
     * @param root 根节点ciCode
     * @param ciCodeList 对象标识集合
     * @param rltCodeList 关系标识集合
     * @return excel
     */
    ResponseEntity<byte[]> export(String root, List<String> ciCodeList, List<String> rltCodeList);

    /**
     * 关联分析表格导出
     * @param root 根节点ciCode
     * @param ciCodeList 对象标识集合
     * @param rltCodeList 关系标识集合
     * @return 表格数据
     */
    List<EamAnalyseTableVo> getTable(String root, List<String> ciCodeList, List<String> rltCodeList);
}
