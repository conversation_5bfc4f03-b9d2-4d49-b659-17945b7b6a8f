package com.uinnova.product.eam.feign.workable.entity;

import lombok.Data;

import java.util.Date;
import java.util.Map;

/**
 * description
 *
 * <AUTHOR>
 * @since 2022/2/24 14:31
 */
@Data
public class PorcessResponse {

    private String processInstanceId;

    private String taskId;

    private String processDefinitionKey;

    private String businessKey;

    private String processInstanceName;

    private Date startTime;

    private Date endTime;

    private String processStartUserId;

    private String processStartUserName;

    private FLOWSTATUS status;

    /**
     * 路由使用的参数(流程全局变量)
     */
    private Map<String, Object> routerVariables;
}
