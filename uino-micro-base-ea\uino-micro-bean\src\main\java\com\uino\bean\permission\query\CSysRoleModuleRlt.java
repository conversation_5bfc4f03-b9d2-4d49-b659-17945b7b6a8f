package com.uino.bean.permission.query;




import java.io.Serializable;


/**
 * condition-table: 角色模块关联表[SYS_ROLE_MODULE_RLT]
 * 
 * <AUTHOR>
 */
public class CSysRoleModuleRlt implements Serializable {
	private static final long serialVersionUID = 1L;


	/**
	 * condition-field: ID[ID] operate-Equal[=]
	 */
	private Long id;


	/**
	 * condition-field: ID[ID] operate-In[in]
	 */
	private Long[] ids;


	/**
	 * condition-field: ID[ID] operate-GTEqual[>=]
	 */
	private Long startId;

	/**
	 * condition-field: ID[ID] operate-LTEqual[<=]
	 */
	private Long endId;


	/**
	 * condition-field: 模块ID[MODULE_ID] operate-Equal[=]
	 */
	private Long moduleId;


	/**
	 * condition-field: 模块ID[MODULE_ID] operate-In[in]
	 */
	private Long[] moduleIds;


	/**
	 * condition-field: 模块ID[MODULE_ID] operate-GTEqual[>=]
	 */
	private Long startModuleId;

	/**
	 * condition-field: 模块ID[MODULE_ID] operate-LTEqual[<=]
	 */
	private Long endModuleId;


	/**
	 * condition-field: 角色ID[ROLE_ID] operate-Equal[=]
	 */
	private Long roleId;


	/**
	 * condition-field: 角色ID[ROLE_ID] operate-In[in]
	 */
	private Long[] roleIds;


	/**
	 * condition-field: 角色ID[ROLE_ID] operate-GTEqual[>=]
	 */
	private Long startRoleId;

	/**
	 * condition-field: 角色ID[ROLE_ID] operate-LTEqual[<=]
	 */
	private Long endRoleId;


	/**
	 * condition-field: 所属域[DOMAIN_ID] operate-Equal[=]
	 */
	private Long domainId;


	/**
	 * condition-field: 所属域[DOMAIN_ID] operate-In[in]
	 */
	private Long[] domainIds;


	/**
	 * condition-field: 所属域[DOMAIN_ID] operate-GTEqual[>=]
	 */
	private Long startDomainId;

	/**
	 * condition-field: 所属域[DOMAIN_ID] operate-LTEqual[<=]
	 */
	private Long endDomainId;


	/**
	 * condition-field: 创建时间[CREATE_TIME] operate-Equal[=]
	 * 创建时间:yyyyMMddHHmmss
	 */
	private Long createTime;


	/**
	 * condition-field: 创建时间[CREATE_TIME] operate-In[in]
	 * 创建时间:yyyyMMddHHmmss
	 */
	private Long[] createTimes;


	/**
	 * condition-field: 创建时间[CREATE_TIME] operate-GTEqual[>=]
	 * 创建时间:yyyyMMddHHmmss
	 */
	private Long startCreateTime;

	/**
	 * condition-field: 创建时间[CREATE_TIME] operate-LTEqual[<=]
	 * 创建时间:yyyyMMddHHmmss
	 */
	private Long endCreateTime;


	/**
	 * condition-field: 修改时间[MODIFY_TIME] operate-Equal[=]
	 * 修改时间:yyyyMMddHHmmss
	 */
	private Long modifyTime;


	/**
	 * condition-field: 修改时间[MODIFY_TIME] operate-In[in]
	 * 修改时间:yyyyMMddHHmmss
	 */
	private Long[] modifyTimes;


	/**
	 * condition-field: 修改时间[MODIFY_TIME] operate-GTEqual[>=]
	 * 修改时间:yyyyMMddHHmmss
	 */
	private Long startModifyTime;

	/**
	 * condition-field: 修改时间[MODIFY_TIME] operate-LTEqual[<=]
	 * 修改时间:yyyyMMddHHmmss
	 */
	private Long endModifyTime;




	public Long getId() {
		return this.id;
	}
	public void setId(Long id) {
		this.id = id;
	}


	public Long[] getIds() {
		return this.ids;
	}
	public void setIds(Long[] ids) {
		this.ids = ids;
	}


	public Long getStartId() {
		return this.startId;
	}
	public void setStartId(Long startId) {
		this.startId = startId;
	}


	public Long getEndId() {
		return this.endId;
	}
	public void setEndId(Long endId) {
		this.endId = endId;
	}


	public Long getModuleId() {
		return this.moduleId;
	}
	public void setModuleId(Long moduleId) {
		this.moduleId = moduleId;
	}


	public Long[] getModuleIds() {
		return this.moduleIds;
	}
	public void setModuleIds(Long[] moduleIds) {
		this.moduleIds = moduleIds;
	}


	public Long getStartModuleId() {
		return this.startModuleId;
	}
	public void setStartModuleId(Long startModuleId) {
		this.startModuleId = startModuleId;
	}


	public Long getEndModuleId() {
		return this.endModuleId;
	}
	public void setEndModuleId(Long endModuleId) {
		this.endModuleId = endModuleId;
	}


	public Long getRoleId() {
		return this.roleId;
	}
	public void setRoleId(Long roleId) {
		this.roleId = roleId;
	}


	public Long[] getRoleIds() {
		return this.roleIds;
	}
	public void setRoleIds(Long[] roleIds) {
		this.roleIds = roleIds;
	}


	public Long getStartRoleId() {
		return this.startRoleId;
	}
	public void setStartRoleId(Long startRoleId) {
		this.startRoleId = startRoleId;
	}


	public Long getEndRoleId() {
		return this.endRoleId;
	}
	public void setEndRoleId(Long endRoleId) {
		this.endRoleId = endRoleId;
	}


	public Long getDomainId() {
		return this.domainId;
	}
	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}


	public Long[] getDomainIds() {
		return this.domainIds;
	}
	public void setDomainIds(Long[] domainIds) {
		this.domainIds = domainIds;
	}


	public Long getStartDomainId() {
		return this.startDomainId;
	}
	public void setStartDomainId(Long startDomainId) {
		this.startDomainId = startDomainId;
	}


	public Long getEndDomainId() {
		return this.endDomainId;
	}
	public void setEndDomainId(Long endDomainId) {
		this.endDomainId = endDomainId;
	}


	public Long getCreateTime() {
		return this.createTime;
	}
	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}


	public Long[] getCreateTimes() {
		return this.createTimes;
	}
	public void setCreateTimes(Long[] createTimes) {
		this.createTimes = createTimes;
	}


	public Long getStartCreateTime() {
		return this.startCreateTime;
	}
	public void setStartCreateTime(Long startCreateTime) {
		this.startCreateTime = startCreateTime;
	}


	public Long getEndCreateTime() {
		return this.endCreateTime;
	}
	public void setEndCreateTime(Long endCreateTime) {
		this.endCreateTime = endCreateTime;
	}


	public Long getModifyTime() {
		return this.modifyTime;
	}
	public void setModifyTime(Long modifyTime) {
		this.modifyTime = modifyTime;
	}


	public Long[] getModifyTimes() {
		return this.modifyTimes;
	}
	public void setModifyTimes(Long[] modifyTimes) {
		this.modifyTimes = modifyTimes;
	}


	public Long getStartModifyTime() {
		return this.startModifyTime;
	}
	public void setStartModifyTime(Long startModifyTime) {
		this.startModifyTime = startModifyTime;
	}


	public Long getEndModifyTime() {
		return this.endModifyTime;
	}
	public void setEndModifyTime(Long endModifyTime) {
		this.endModifyTime = endModifyTime;
	}


}


