package com.uinnova.product.eam.web.integration.bean;

import java.io.Serializable;
import java.util.Map;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.vmdb.comm.model.rlt.CcCiRlt;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;

public class VcCiRltInfo implements Serializable{

	private static final long serialVersionUID = 1L;

	@Comment("CI关系")
	private CcCiRlt ciRlt;

	@Comment("Rlt属性")
	private Map<String, String> attrs;
	
	@Comment("关系分类信息")
	private VcCiClassInfo rltClassInfo;

	@Comment("源Ci信息")
	private CcCiInfo sourceCiInfo;

	@Comment("目标Ci信息")
	private CcCiInfo targetCiInfo;

	
	public VcCiClassInfo getRltClassInfo() {
		return rltClassInfo;
	}

	public void setRltClassInfo(VcCiClassInfo rltClassInfo) {
		this.rltClassInfo = rltClassInfo;
	}

	public CcCiRlt getCiRlt() {
		return ciRlt;
	}

	public void setCiRlt(CcCiRlt ciRlt) {
		this.ciRlt = ciRlt;
	}

	public Map<String, String> getAttrs() {
		return attrs;
	}

	public void setAttrs(Map<String, String> attrs) {
		this.attrs = attrs;
	}

	public CcCiInfo getSourceCiInfo() {
		return sourceCiInfo;
	}

	public void setSourceCiInfo(CcCiInfo sourceCiInfo) {
		this.sourceCiInfo = sourceCiInfo;
	}

	public CcCiInfo getTargetCiInfo() {
		return targetCiInfo;
	}

	public void setTargetCiInfo(CcCiInfo targetCiInfo) {
		this.targetCiInfo = targetCiInfo;
	}

}