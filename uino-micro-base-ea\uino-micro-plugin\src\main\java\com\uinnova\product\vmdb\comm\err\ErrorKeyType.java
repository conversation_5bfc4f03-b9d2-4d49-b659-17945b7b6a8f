package com.uinnova.product.vmdb.comm.err;

import com.binary.core.i18n.LanguageResolver;
import com.binary.core.util.BinaryUtils;

/**
 * 根据错误原因类型国际化错误原因
 * 
 * <AUTHOR>
 *
 */
public enum ErrorKeyType {

	/**
	 * 
	 */
	ERR_KEY_1(1, "BS_MNAME_ATTRS", "CI属性"),
	/**
	 * 
	 */
	ERR_KEY_2(2, "BS_MNAME_CIPRIMARYKEY", "业务主键"),
	/**
	 * 
	 */
	ERR_KEY_3(3, "BS_MNAME_CCCIRLT_SOURCECICODE", "源CI"),
	/**
	 * 
	 */
	ERR_KEY_4(4, "BS_MNAME_CCCIRLT_TARGETCICODE", "目标CI"),
	/**
	 * 
	 */
	ERR_KEY_5(5, "BS_MNAME_RLTCLASS", "关系");

	/**
	 * 错误原因类型
	 */
	private Integer type;
	/**
	 * 错误原因国际化代码
	 */
	private String value;
	/**
	 * 错误原因预留中文信息
	 */
	private String desc;

	private ErrorKeyType(Integer type, String value, String desc) {
		this.type = type;
		this.value = value;
		this.desc = desc;
	}

	public Integer getType() {
		return type;
	}

	public String getValue() {
		return value;
	}

	public String getDesc() {
		return desc;
	}

	/**
	 * 根据错误云因类型翻译获取原因
	 * 
	 * @param errKeyType 错误
	 * @return 错误消息
	 */
	public static String getErrKey(Integer errKeyType) {
		ErrorKeyType[] values = values();
		String message = null;
		for (ErrorKeyType errorZhcType : values) {
			if (errorZhcType.getType().equals(errKeyType)) {
				// 默认获取中文
				message = errorZhcType.getDesc();
				try {
					String languageCode = errorZhcType.getValue();
					message = LanguageResolver.trans(languageCode);
					if (!BinaryUtils.isEmpty(message) && message.startsWith("BS_")) {
						message = errorZhcType.getDesc();
					}
				} catch (Exception e) {
					// 获取国际化信息异常,不处理
				}
				break;
			}
		}
		return message;
	}

}
