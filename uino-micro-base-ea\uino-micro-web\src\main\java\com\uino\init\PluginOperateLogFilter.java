package com.uino.init;

import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uino.api.client.sys.IOperateLogApiSvc;
import com.uino.api.client.sys.IOperateLogModuleApiSvc;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.sys.base.ESOperateLog;
import com.uino.bean.sys.base.ESOperateLogModule;
import com.uino.dao.BaseConst;
import com.uino.init.http.request.CustomHttpServletRequestWrapper;
import com.uino.init.http.response.CustomHttpServletResponseWrapper;
import com.uino.plugin.classloader.PluginOperateLogUtil;
import com.uino.plugin.classloader.comm.RequestMappingMethod;
import com.uino.util.exception.LoginException;
import com.uino.util.sys.SysUtil;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;

import jakarta.servlet.Filter;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Date;

/**
 * 拦截插件接口，记录日志
 */
@Slf4j
public class PluginOperateLogFilter implements Filter {


    /**
     * 打印请求参数最大大小，超过则打印为debug日志
     */
    private int printReqParamMaxSize = 1024 * 100;

    /**
     * 返回数据过大告警阈值
     */
    private int warnSize = 1024 * 1024 * 3;

    /**
     * 日志记录返回值最大长度
     */
    private int returnSize = 2000;

    /**
     * 与前端约定的请求唯一标识字段
     */
    private String requestIdHeaderName = "requestId";

    private ApplicationContext applicationContext;

    public PluginOperateLogFilter(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        Date startTime = new Date();

        // 通过request获取信息
        HttpServletRequest httpServletRequest = (HttpServletRequest) request;
        String ip = SysUtil.getIpAddress(httpServletRequest);
        String uri = httpServletRequest.getRequestURI();
        String requestId = httpServletRequest.getHeader(requestIdHeaderName);
        String contextPath = httpServletRequest.getContextPath();
        String path = uri.substring(contextPath.length());
        log.info("接收到来自【{}】请求，uri为【{}】,请求标识为【{}】", ip, uri, requestId);

        // 获取插件类和方法信息
        RequestMappingMethod mappingMethod = PluginOperateLogUtil.mappingMethodMap.getOrDefault(path, null);
        if (mappingMethod == null) {
            log.debug("通过url找不到方法信息");
            chain.doFilter(request, response);
        } else {
            log.debug("路由到类【{}】方法【{}】", mappingMethod.getClazz().getName(), mappingMethod.getMethod().getName());
            CustomHttpServletResponseWrapper responseWrapper = null;
            try {
                responseWrapper = (CustomHttpServletResponseWrapper) response;
            } catch (Exception e) {
                log.error("获取请求自定义response失败，可能由于未注册自定义CustomHttpServletResponseWrapper或正确配置请求拦截");
            }
            CustomHttpServletRequestWrapper requestWrapper = new CustomHttpServletRequestWrapper((HttpServletRequest) request);
            String requestBody = requestWrapper.getBody();
            if (printReqParamMaxSize < 0L || requestBody.length() <= printReqParamMaxSize) {
                log.info("参数为【{}】", requestBody);
            } else {
                log.info("参数值空间占用过大，打印到了打印到了更低级别debug日志中");
                log.debug("参数为【{}】", requestBody);
            }

            // 记录接口访问日志
            SysUser currentUser = null;
            try {
                currentUser = SysUtil.getCurrentUserInfo();
            } catch (LoginException e) {
                log.debug("无法获取当前登陆用户，该持久化信息可能会缺失[创建/修改]人信息和domain域信息不对的问题");
            }
            ESOperateLog opLog = null;
            try {
                // 获取类和方法注解信息
                ModDesc methodAnnotation = mappingMethod.getMethod().getAnnotation(ModDesc.class);
                ApiOperation apiOperation = mappingMethod.getMethod().getAnnotation(ApiOperation.class);
                ESOperateLogModule moduleInfo = applicationContext.getBean(IOperateLogModuleApiSvc.class).getModuleInfoByMvc(mappingMethod.getClazz().getName());
                opLog = ESOperateLog.builder().moduleName(SysUtil.StringUtil.isNotBack(moduleInfo.getModuleCode()) ? moduleInfo.getModuleCode() : "")
                        .mvcFullName(mappingMethod.getClazz().getName()).mvcModName(mappingMethod.getMethod().getName())
                        .opDesc(methodAnnotation != null ? methodAnnotation.desc()
                                : (apiOperation != null ? apiOperation.value() : ""))
                        .opPath(uri)
                        .opStatus(responseWrapper == null ? 200 : responseWrapper.getStatus())
//                        .opParam(requestBody)
                        .userId(currentUser == null ? 1L : currentUser.getId()).ipAddress(ip)
                        .domainId((currentUser == null || currentUser.getDomainId() == null) ? BaseConst.DEFAULT_DOMAIN_ID : currentUser.getDomainId())
                        .userName(currentUser == null ? "system" : currentUser.getUserName())
                        .userCode(currentUser == null ? "system" : currentUser.getLoginCode()).build();
            } catch (Exception e) {
                log.warn("构建接口日志异常", e);
            }

            chain.doFilter(requestWrapper, response);
            Date endTime = new Date();
            log.info("本次请求【{}】共处理【{}】毫秒", requestId, (endTime.getTime() - startTime.getTime()));

            // 通过response获取接口返回信息
            String data = "";
            if (responseWrapper != null) {
                String responseContentType = responseWrapper.getContentType();
                if (responseContentType != null) {
                    String responseContentTypeLowerCase = responseContentType.toLowerCase();
                    if (!responseContentTypeLowerCase.contains("stream") && !responseContentTypeLowerCase.contains("image/x-png")) {
                        data = new String(responseWrapper.getBody());
                        log.debug("返回消息为【{}】", data);
                        if (responseWrapper.getBody().length >= this.warnSize) {
                            log.warn("类【{}】方法【{}】在本次处理返回数据大小大于阈值【{}】b,请考虑优化返回数据", mappingMethod.getClazz().getName(), mappingMethod.getMethod().getName(), this.warnSize);
                        }
                    } else {
                        data = "File";
                    }
                }
            } else {
                log.error("无法获取返回信息");
            }
            if (opLog != null) {
                opLog.setOpResult(data.substring(0, Math.min(data.length(), returnSize)));
                applicationContext.getBean(IOperateLogApiSvc.class).saveOrUpdate(opLog);
            }
        }
    }
}
