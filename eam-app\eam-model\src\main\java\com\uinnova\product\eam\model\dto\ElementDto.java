package com.uinnova.product.eam.model.dto;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

/**
 * <AUTHOR> wangchun<PERSON>i
 * @Date :
 * @Description : 查询制品分栏信息参数
 */
@Data
public class ElementDto {

    @Comment("制品id")
    private Long artifactId;

    @Comment("制品分栏信息类型")
    private Integer type;

    public ElementDto() {
    }

    public ElementDto(Long artifactId, Integer type){
        this.artifactId = artifactId;
        this.type = type;
    }
}
