package com.uinnova.product.eam.model.diagram;

import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

@Data
public class MoveDirAndDiagramCdt implements Condition {

	private static final long serialVersionUID = 1L;

	@Comment("要移动到的目录id")
	private Long targetDirId;
	@Comment("要移动的目录")
	private Long[] dirIds;
	@Comment("要移动的视图")
	private String[] diagramIds;
	@Comment("要移动到的SubjectId")
	private Long targetSubjectId;
	@Comment("要移动的Subject")
	private Long[] SubjectIds;
	@Comment("文件夹类型")
	private Integer dirType;
}
