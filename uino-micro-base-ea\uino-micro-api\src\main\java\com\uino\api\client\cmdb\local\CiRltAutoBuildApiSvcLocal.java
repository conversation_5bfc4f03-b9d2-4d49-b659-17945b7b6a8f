package com.uino.api.client.cmdb.local;

import com.binary.jdbc.Page;
import com.uino.dao.BaseConst;
import com.uino.service.cmdb.microservice.ICiRltAutoBuildSvc;
import com.uino.bean.cmdb.base.ESCiRltAutoBuild;
import com.uino.bean.cmdb.business.ClassRltListDto;
import com.uino.bean.cmdb.query.ESCiRltAutoBuildSearchBean;
import com.uino.api.client.cmdb.ICiRltAutoBuildApiSvc;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Classname CiRltAutoBuildApiSvcLocal
 * @Description TODO
 * @Date 2020/6/30 15:23
 * <AUTHOR> sh
 */
@Service
public class CiRltAutoBuildApiSvcLocal implements ICiRltAutoBuildApiSvc {

    @Autowired
    private ICiRltAutoBuildSvc iCiRltAutoBuildSvc;

    @Override
    public ESCiRltAutoBuild saveDefault(Long sourceCiClassId, Long targetCiClassId, Long rltClassId, Long visualModelId) {
        return iCiRltAutoBuildSvc.saveDefault(BaseConst.DEFAULT_DOMAIN_ID, sourceCiClassId, targetCiClassId, rltClassId, visualModelId);
    }

    @Override
    public ESCiRltAutoBuild saveDefault(Long domainId, Long sourceCiClassId, Long targetCiClassId, Long rltClassId, Long visualModelId) {
        return iCiRltAutoBuildSvc.saveDefault(domainId, sourceCiClassId, targetCiClassId, rltClassId, visualModelId);
    }

    @Override
    public Long updateCiRltAutoBuild(ESCiRltAutoBuild esCiRltAutoBuild) {
        return iCiRltAutoBuildSvc.updateCiRltAutoBuild(esCiRltAutoBuild);
    }

    @Override
    public List<ClassRltListDto> getAutoBuildListByRltId(Long rltId) {
        return iCiRltAutoBuildSvc.getAutoBuildListByRltId(rltId);
    }

    @Override
    public ESCiRltAutoBuild findCiRltAutoBuild(Long sourceCiClassId, Long targetCiClassId, Long rltClassId, Long visualModelId) {
        return iCiRltAutoBuildSvc.findCiRltAutoBuild(BaseConst.DEFAULT_DOMAIN_ID, sourceCiClassId, targetCiClassId, rltClassId, visualModelId);
    }

    @Override
    public ESCiRltAutoBuild findCiRltAutoBuild(Long domainId, Long sourceCiClassId, Long targetCiClassId, Long rltClassId, Long visualModelId) {
        return iCiRltAutoBuildSvc.findCiRltAutoBuild(domainId, sourceCiClassId, targetCiClassId, rltClassId, visualModelId);
    }

    @Override
    public void deleteCiRltAutoBuild(Long sourceCiClassId, Long targetCiClassId, Long rltClassId, Long visualModelId) {
        iCiRltAutoBuildSvc.deleteCiRltAutoBuild(sourceCiClassId, targetCiClassId, rltClassId, visualModelId);
    }

    @Override
    public void runCiRltAutoBuildAll() {
        iCiRltAutoBuildSvc.runCiRltAutoBuildAll(BaseConst.DEFAULT_DOMAIN_ID);
    }

    @Override
    public void runCiRltAutoBuildAll(Long domainId) {
        iCiRltAutoBuildSvc.runCiRltAutoBuildAll(domainId);
    }

    @Override
    public boolean runCiRltAutoBuildById(Long id) {
        return iCiRltAutoBuildSvc.runCiRltAutoBuildById(id);
    }

    @Override
    public Long saveCiRltAutoBuild(ESCiRltAutoBuild esCiRltAutoBuild) {
        return iCiRltAutoBuildSvc.saveCiRltAutoBuild(esCiRltAutoBuild);
    }

    @Override
    public Page<ESCiRltAutoBuild> queryCiRltAutoBuildPage(ESCiRltAutoBuildSearchBean searchBean) {
        return iCiRltAutoBuildSvc.queryCiRltAutoBuildPage(searchBean);
    }

    @Override
    public Integer deleteById(Long id) {
        return iCiRltAutoBuildSvc.deleteById(id);
    }

    @Override
    public Long saveOrUpdate(ESCiRltAutoBuild esCiRltAutoBuild) {
        return iCiRltAutoBuildSvc.saveOrUpdate(esCiRltAutoBuild);
    }

    @Override
    public List<ESCiRltAutoBuild> getAutoBuildListByRltClassId(List<Long> rltClassIds) {
        return iCiRltAutoBuildSvc.getAutoBuildListByRltClassId(rltClassIds);
    }

    @Override
    public void runCiRltAutoBuildByIds(List<Long> ids) {
        iCiRltAutoBuildSvc.runCiRltAutoBuildByIds(ids);
    }

    @Override
    public void deleteCiRltAutoBuildBatchByCiClassIds(List<Long> ciClassIds, Long visualModelId) {
        iCiRltAutoBuildSvc.deleteCiRltAutoBuildBatchByCiClassIds(ciClassIds,visualModelId);
    }
}
