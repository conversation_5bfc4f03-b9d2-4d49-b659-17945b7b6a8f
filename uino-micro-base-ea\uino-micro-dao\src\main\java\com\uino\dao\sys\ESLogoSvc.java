package com.uino.dao.sys;

import java.util.List;

import jakarta.annotation.PostConstruct;

import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.uino.dao.AbstractESBaseDao;
import com.uino.dao.ESConst;
import com.uino.util.sys.CommonFileUtil;
import com.uino.bean.sys.base.Logo;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
public class ESLogoSvc extends AbstractESBaseDao<Logo, JSONObject> {

	@Override
	public String getIndex() {
		// TODO Auto-generated method stub
		return ESConst.INDEX_SYS_LOGO;
	}

	@Override
	public String getType() {
		// TODO Auto-generated method stub
		return ESConst.INDEX_SYS_LOGO;
	}

	@PostConstruct
	public void init() {
		List<Logo> datas = CommonFileUtil.getData("/initdata/uino_sys_logo.json", Logo.class);
		super.initIndex(datas);
	}
}
