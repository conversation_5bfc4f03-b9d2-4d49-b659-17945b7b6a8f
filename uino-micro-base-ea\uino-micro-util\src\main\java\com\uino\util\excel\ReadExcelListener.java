package com.uino.util.excel;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map.Entry;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.uino.util.excel.callBack.IReadExcelCallBack;

import lombok.extern.slf4j.Slf4j;

/**
 * excel读取监听
 *
 * <pre>
 * 采用阻塞式处理
 * 想获取sheet下所有数据，可直接callBackExecImpl传递null&&batchSize传递-1
 * 然后直接获取本对象的rows和titles
 *
 * 建议自己实现回调接口IReadExcelCallBack然后按照批次处理
 * </pre>
 *
 * <AUTHOR>
 */
@Slf4j
public class ReadExcelListener extends AnalysisEventListener<LinkedHashMap<Integer, Object>> {

	/**
	 * @param callBackExecImpl
	 *            回调处理实现实例
	 * @param batchSize
	 *            批次大小,<=0则代表全部读取完毕再处理
	 */
	public ReadExcelListener(IReadExcelCallBack callBackExecImpl, int batchSize) {
		this.callBackExecImpl = callBackExecImpl;
		this.batchSize = batchSize;
	}

	/**
	 * 批次大小,<=0则代表全部读取完毕再处理
	 */
	private int batchSize = -1;

	private IReadExcelCallBack callBackExecImpl;

	private List<String[]> rows = new LinkedList<>();

	private int titleSize = -1;

	private int currentRowIndex = -1;

	private List<String> titles = new ArrayList<>();

	/**
	 * 获取所有数据行-只阅读有效标题列
	 *
	 * @return
	 */
	public List<String[]> getRows() {
		return rows;
	}

	/**
	 * 获取标题
	 *
	 * @return
	 */
	public List<String> getTitles() {
		return titles;
	}

	/**
	 * 所有数据读取完毕会调用
	 */
	@Override
	public void doAfterAllAnalysed(AnalysisContext context) {

		if (this.getRows().size() > 0 && callBackExecImpl != null) {
			callBackExecImpl.execCacheDatas(this.getTitles(), getRows(), context.readSheetHolder().getSheetName());
		}
	}

	/**
	 * 每一行都会调用
	 */
	@SuppressWarnings("deprecation")
	@Override
	public void invoke(LinkedHashMap<Integer, Object> rowMap, AnalysisContext context) {
		if (context.getCurrentRowNum() - currentRowIndex > 1) {
			// 空行截断
			return;
		}
		if (context.getCurrentRowNum() == 0) {
			for (Entry<Integer, Object> entry : rowMap.entrySet()) {
				Integer index = entry.getKey();
				Object val = entry.getValue();
				if (val == null) {
					// 配合空列截断处理
					continue;
				}
				if (index - titleSize <= 1) {
					titleSize = index;
					titles.add(String.valueOf(val));
				}
			}
			currentRowIndex = context.getCurrentRowNum();
		} else {
			String[] rowData = new String[titles.size()];
			boolean valNotEmbty = false;
			for (Entry<Integer, Object> entry : rowMap.entrySet()) {
				Integer index = entry.getKey();
				Object val = entry.getValue();
				if (index < rowData.length) {
					rowData[index] = val == null ? null : String.valueOf(val);
					if (val != null) {
						valNotEmbty = true;
					}
				}
			}
			if (valNotEmbty) {
				rows.add(rowData);
				currentRowIndex = context.getCurrentRowNum();
			}
		}
		/**
		 * 制定了批次大小并且缓存中数据已经达到批次大小，执行回调处理函数
		 */
		if (this.batchSize > 0 && getRows().size() >= this.batchSize && callBackExecImpl != null) {
			callBackExecImpl.execCacheDatas(this.getTitles(), getRows(), context.readSheetHolder().getSheetName());
			// 处理完毕清除缓存中数据
			getRows().clear();
		}
	}
}
