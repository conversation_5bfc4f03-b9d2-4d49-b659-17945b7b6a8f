package com.uinnova.product.vmdb.comm.model.es;

import com.uinnova.product.vmdb.comm.model.rule.CcCiTagDef;

import java.util.List;

/**
 * 
 * <AUTHOR>
 *
 */
public class EsTagInfo extends CcCiTagDef {

    private static final long serialVersionUID = 1L;

    List<EsTagRuleInfo> rules;

    public List<EsTagRuleInfo> getRules() {
        return rules;
    }

    public void setRules(List<EsTagRuleInfo> rules) {
        this.rules = rules;
    }

}
