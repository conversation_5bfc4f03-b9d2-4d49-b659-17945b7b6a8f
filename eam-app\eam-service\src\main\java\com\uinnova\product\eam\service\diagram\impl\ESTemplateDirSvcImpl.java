package com.uinnova.product.eam.service.diagram.impl;

import com.binary.core.exception.BinaryException;
import com.binary.core.util.BinaryUtils;
import com.binary.jdbc.Page;
import com.uinnova.product.eam.base.diagram.mix.model.ConvertTemplateDTO;
import com.uinnova.product.eam.base.diagram.mix.model.TemplateDir;
import com.uinnova.product.eam.base.diagram.mix.model.TemplateDirQuery;
import com.uinnova.product.eam.base.diagram.mix.model.TemplateDirQueryBean;
import com.uinnova.product.eam.base.diagram.model.*;
import com.uinnova.product.eam.db.diagram.es.ESDiagramDao;
import com.uinnova.product.eam.db.diagram.es.EsTemDiaRelationDao;
import com.uinnova.product.eam.db.diagram.es.EsTemplateDirDao;
import com.uinnova.product.eam.service.diagram.ESDiagramSvc;
import com.uinnova.product.eam.service.diagram.ESTemplateDirSvc;
import com.uinnova.product.vmdb.comm.i18n.MessageUtil;
import com.uino.api.client.permission.IUserApiSvc;
import com.uino.bean.permission.base.SysRole;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.permission.business.UserInfo;
import com.uino.dao.util.ESUtil;
import com.uino.util.sys.SysUtil;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.security.SecureRandom;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class ESTemplateDirSvcImpl implements ESTemplateDirSvc {

    @Autowired
    private EsTemplateDirDao templateDirDao;

    @Autowired
    private EsTemDiaRelationDao esTemDiaRelationDao;

    @Value("${http.resource.space}")
    private String httpResouceUrl;

    @Autowired
    private IUserApiSvc userApiSvc;

    @Autowired
    private ESDiagramSvc esDiagramSvc;

    @Autowired
    private ESDiagramDao esDiagramDao;

    @Override
    public Integer convertDiagramAndTemplate(ConvertTemplateDTO cBean) {
        Integer opType = cBean.getType();
        String dId = cBean.getDiagramId();
        Long diagramId = esDiagramSvc.queryDiagramInfoByEnergy(dId);
        Long templateDirId = cBean.getTemplateDirId();
        if (opType == 1) { //视图转模板
            //在进行模板转换时，首先判断模板分类是否还存在
            TemplateDir templateDir = templateDirDao.getById(templateDirId);
            if (BinaryUtils.isEmpty(templateDir)) {
                //模板分类已被删除
                return 1;
            }
            // 视图与目录关联，先查询视图是否与目录已有关联
            TemDiaRelationQuery temDiaRelation = new TemDiaRelationQuery();
            temDiaRelation.setDiagramId(diagramId);
            List<TemDiaRelation> temDiaRelations = esTemDiaRelationDao.getListByCdt(temDiaRelation);
            if (CollectionUtils.isEmpty(temDiaRelations)) {
                TemDiaRelation newRelation = new TemDiaRelation();
                newRelation.setId(ESUtil.getUUID());
                newRelation.setDiagramId(diagramId);
                newRelation.setTemplateDirId(templateDirId);
                esTemDiaRelationDao.saveOrUpdate(newRelation);

                // 将视图的类型改为3
                ESDiagram esDiagram = esDiagramDao.getById(diagramId);
                if (this.getCurrentUserType() == 1) {
                    esDiagram.setDiagramType(3);
                } else {
                    esDiagram.setDiagramType(4);
                }
                esDiagram.setTemConvertTime(BinaryUtils.getNumberDateTime());
                esDiagramDao.saveOrUpdateWithOptionExtra(esDiagram, true);
            } else {
                // 将视图的模板信息更新
                TemDiaRelation diaRelation = temDiaRelations.get(0);
                diaRelation.setTemplateDirId(templateDirId);
                esTemDiaRelationDao.saveOrUpdate(diaRelation);
            }
        } else if (opType == 2) { //模板转视图
            TemDiaRelationQuery temDiaRelation = new TemDiaRelationQuery();
            temDiaRelation.setDiagramId(diagramId);
            List<TemDiaRelation> temDiaRelations = esTemDiaRelationDao.getListByCdt(temDiaRelation);
            if (!CollectionUtils.isEmpty(temDiaRelations)) {
                Set<Long> temDiaRelationIdSet = new HashSet<>();
                for (TemDiaRelation temDiagramRelation : temDiaRelations) {
                    temDiaRelationIdSet.add(temDiagramRelation.getId());
                }
                esTemDiaRelationDao.deleteByIds(temDiaRelationIdSet);
            }

            // 将视图类型改为1
            ESDiagram esDiagram = esDiagramDao.getById(diagramId);
            esDiagram.setDiagramType(1);
            esDiagramDao.saveOrUpdateWithOptionExtra(esDiagram, true);
        }
        return 0;
    }

    @Override
    public TemplateDir saveOrUpdate(TemplateDir templateDir) {
        if (StringUtils.isEmpty(templateDir.getId())) {
            templateDir.setId(ESUtil.getUUID());
//            templateDir.setViewType(RandomStringUtils.randomAlphanumeric(9));
            templateDir.setViewType(generateRandomString(9));
        }
        templateDirDao.saveOrUpdate(templateDir);

        return templateDir;
    }

    private String generateRandomString(int length) {
        SecureRandom random = new SecureRandom();
        StringBuilder sb = new StringBuilder(length);

        String ALLOWED_CHARACTERS = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
        for (int i = 0; i < length; i++) {
            sb.append(ALLOWED_CHARACTERS.charAt(random.nextInt(ALLOWED_CHARACTERS.length())));
        }

        return sb.toString();
    }

    @Override
    public List<TemplateDir> queryTemplateDirs(Boolean queryFlag) {
        // 判断当前登录用户是不是管理员，如果是管理员，则查询类型为1的目录，其他用户查询类型为2的目录
        Integer opKind = this.getCurrentUserType();
        TemplateDirQuery query = new TemplateDirQuery();
        //queryFlag=false，表示是转换模板是查询模板目录信息，=true表示是模板工作台查询，不限制type
        if (!queryFlag) {
            // 如果为管理员用户，则查询标准模板目录
            if (opKind == 1) {
                query.setType(1);
            } else {
                //否则查询个人模板目录
                query.setType(2);
            }
        }

        return templateDirDao.getSortListByCdt(query, Collections.singletonList(SortBuilders.fieldSort("order").order(SortOrder.ASC)));
    }

    @Override
    public Page<ESDiagram> queryDiagramByTemplateDirId(TemplateDirQueryBean queryBean) {
        Page<VcDiagram> vcDiagrams = new Page<>();
        Page<ESDiagram> esDiagramPage = new Page<>();
        Set<Long> diagramIds = new HashSet<>();
        //获取目录包含的所有视图id
        if (queryBean.getDirId() != null) {
            this.getAllDiagramIdByDirId(Collections.singleton(queryBean.getDirId()), diagramIds);
        } else {
            List<TemplateDir> templateDirs = queryTemplateDirs(true);
            if (!CollectionUtils.isEmpty(templateDirs)) {
                //获取目录包含的所有视图id
                Set<Long> sets = templateDirs.stream().map(TemplateDir::getId).filter(Objects::nonNull).collect(Collectors.toSet());
                this.getAllDiagramIdByDirId(sets, diagramIds);
            }
        }
        if (!CollectionUtils.isEmpty(diagramIds)) {
            Long userId = SysUtil.getCurrentUserInfo().getId();
            CVcDiagram cVcDiagram = new CVcDiagram();
            cVcDiagram.setIds(diagramIds.toArray(new Long[0]));
            cVcDiagram.setStatus(1);
            cVcDiagram.setDataStatus(1);
            if (!StringUtils.isEmpty(queryBean.getLike())) {
                cVcDiagram.setName(queryBean.getLike());
            }
            if (queryBean.getDirId() != null) {
                TemplateDir templateDir = templateDirDao.getById(queryBean.getDirId());
                //如果目录类型是1，即个人模板中的目录，则限制视图作者为自己，即只查询个人视图
                if (templateDir != null && templateDir.getType() == 2) {
                    cVcDiagram.setUserId(userId);
                    //diagramType=4，表明是个人模板
                    cVcDiagram.setDiagramType(4);
                } else {
                    Integer[] integers = {3};
                    cVcDiagram.setDiagramTypes(integers);
                    setUserIds(userId, integers, cVcDiagram);
                }
            }else {
                setUserIds(userId, queryBean.getDiagramTypes(), cVcDiagram);
            }
            esDiagramPage = esDiagramSvc.queryESDiagramPage(1L, queryBean.getPageNum(), queryBean.getPageSize(), cVcDiagram, "temConvertTime");
        }

        List<ESDiagram> esDiagramList = esDiagramPage.getData();
        if (!CollectionUtils.isEmpty(vcDiagrams.getData())) {
            vcDiagrams.getData().forEach(diagram -> {
                // 缩略图
                String icon1 = diagram.getIcon1();
                if (icon1 != null && !icon1.startsWith(httpResouceUrl)) {
                    diagram.setIcon1(new StringBuilder(httpResouceUrl).append(icon1).toString());
                }
            });
        }

        this.relationTemDirByESDiagramId(esDiagramList);
        return esDiagramPage;
    }

    private void getAllDiagramIdByDirId(Set<Long> dirIds, Set<Long> diagramIds) {
        TemDiaRelationQuery temDiaRelation = new TemDiaRelationQuery();
        temDiaRelation.setTemplateDirIds(dirIds.toArray(new Long[0]));
        List<TemDiaRelation> diaRelations = esTemDiaRelationDao.getSortListByCdt(
                temDiaRelation, Collections.singletonList(SortBuilders.fieldSort("modifyTime").order(SortOrder.DESC)));

        if (!CollectionUtils.isEmpty(diaRelations)) {
            for (TemDiaRelation diaRelation : diaRelations) {
                diagramIds.add(diaRelation.getDiagramId());
            }
        }

        // 查询子目录
        Set<Long> dirs = new HashSet<>();
        TemplateDirQuery templateDirQuery = new TemplateDirQuery();
        templateDirQuery.setParentIds(dirIds.toArray(new Long[0]));
        List<TemplateDir> listByCdt = templateDirDao.getListByCdt(templateDirQuery);
        if (!CollectionUtils.isEmpty(listByCdt)) {
            for (TemplateDir templateDir : listByCdt) {
                dirs.add(templateDir.getId());
            }
            this.getAllDiagramIdByDirId(dirs, diagramIds);
        }
    }

    @Override
    public Page<ESDiagram> queryDiagramTemplateByLike(TemplateDirQueryBean queryBean) {
        String like = queryBean.getLike();
        Integer[] diagramTypes = queryBean.getDiagramTypes();
        MessageUtil.checkEmpty(diagramTypes, "视图类型不能为空");
        SysUser currUser = SysUtil.getCurrentUserInfo();
        Long userId = currUser.getId();
        Long domainId = currUser.getDomainId();

        // 查询模板视图
        CVcDiagram cVcDiagram = new CVcDiagram();
        if (!StringUtils.isEmpty(like)) {
            cVcDiagram.setName(like);
        }
        cVcDiagram.setDiagramTypes(diagramTypes);
        List<TemplateDir> templateDirs = queryTemplateDirs(true);
        if (!CollectionUtils.isEmpty(templateDirs)) {
            Set<Long> diagramIds = new HashSet<>();
            //获取目录包含的所有视图id
            Set<Long> sets = templateDirs.stream().map(TemplateDir::getId).filter(Objects::nonNull).collect(Collectors.toSet());
            this.getAllDiagramIdByDirId(sets, diagramIds);
            cVcDiagram.setIds(diagramIds.toArray(new Long[0]));
        }
        setUserIds(userId, diagramTypes, cVcDiagram);
        Page<ESDiagram> esDiagramPage = esDiagramSvc.queryESDiagramPage(domainId, queryBean.getPageNum(), queryBean.getPageSize(), cVcDiagram, "temConvertTime");
        //关联模板目录信息
        List<ESDiagram> esDiagramList = esDiagramPage.getData();
//        for (ESDiagram esDiagram :
//                esDiagramList) {
//            esDiagram.setDEnergy(SecureUtil.md5(String.valueOf(esDiagram.getId())).substring(8, 24));
//        }
//        Integer integer = esDiagramDao.saveOrUpdateBatchWithOption(esDiagramList);
//        System.out.println(integer+"修改的模版数量");
        if (!BinaryUtils.isEmpty(esDiagramList)) {
            this.relationTemDirByESDiagramId(esDiagramList);
        }
        return esDiagramPage;
    }

    private void setUserIds(Long userId, Integer[] diagramTypes, CVcDiagram cVcDiagram) {
        // 如果查询视图的模板是个人(4)的，那么查询条件过滤为个人的
        Set<Long> creators = new HashSet<>();
        //查询标识，false-管理员，true-个人
        boolean queryFlag1 = false;
        boolean queryFlag2 = false;
        for (Integer diagramType : diagramTypes) {
            if (diagramType.equals(4)) {
                queryFlag1 = true;
            }
            if (diagramType.equals(3)) {
                queryFlag2 = true;
            }
        }
        if (queryFlag1) {
            creators.add(userId);
            if (queryFlag2) {
                List<SysUser> sysUserList = userApiSvc.getUserByRoleId(1L);
                sysUserList.forEach(user -> creators.add(user.getId()));
            }
        } else {
            List<SysUser> sysUserList = userApiSvc.getUserByRoleId(1L);
            sysUserList.forEach(user -> creators.add(user.getId()));
        }
        cVcDiagram.setUserIds(creators.toArray(new Long[0]));
    }

    /**
     * 获取当前用户的类型
     *
     * @return
     */
    private Integer getCurrentUserType() {
        Long loginId = SysUtil.getCurrentUserInfo().getId();
        UserInfo userInfo = userApiSvc.getUserInfoById(loginId);
        Set<SysRole> roles = userInfo.getRoles();
        int opKind = 2;
        if (!CollectionUtils.isEmpty(roles)) {
            for (SysRole role : roles) {
                String roleName = role.getRoleName();
                if (roleName.equals("admin")) {
                    opKind = 1;
                    break;
                }
            }
        }
        return opKind;
    }

    //根据视图id将模板信息关联上
    public void relationTemDirByESDiagramId(List<ESDiagram> esDiagramList) {
        if (!CollectionUtils.isEmpty(esDiagramList)) {
            Set<Long> diagramIds = esDiagramList.stream().map(ESDiagram::getId).collect(Collectors.toSet());
            if (!CollectionUtils.isEmpty(diagramIds)) {
                TemDiaRelationQuery temDiaRelation = new TemDiaRelationQuery();
                temDiaRelation.setDiagramIds(diagramIds.toArray(new Long[0]));
                List<TemDiaRelation> temDiaRelations = esTemDiaRelationDao.getListByCdt(temDiaRelation);
                if (!CollectionUtils.isEmpty(temDiaRelations)) {
                    // key：视图id value：模板目录id
                    Map<Long, Long> diagramIdTemMap = new HashMap<>();
                    for (TemDiaRelation diaRelation : temDiaRelations) {
                        diagramIdTemMap.put(diaRelation.getDiagramId(), diaRelation.getTemplateDirId());
                    }

                    Collection<Long> templateIds = diagramIdTemMap.values();
                    if (!CollectionUtils.isEmpty(templateIds)) {
                        Map<Long, TemplateDir> templateDirMap = new HashMap<>();
                        // 查询所有模板信息
                        TemplateDirQuery templateDir = new TemplateDirQuery();
                        templateDir.setIds(templateIds.toArray(new Long[0]));
                        List<TemplateDir> templateDirs = templateDirDao.getListByCdt(templateDir);
                        for (TemplateDir dir : templateDirs) {
                            templateDirMap.put(dir.getId(), dir);
                        }
                        for (ESDiagram esDiagram : esDiagramList) {
                            Long id = esDiagram.getId();
                            Integer diagramType = esDiagram.getDiagramType();

                            // 将视图类型为3和4的模板信息补充
                            if (diagramType == 3 || diagramType == 4) {
                                Long dirId = diagramIdTemMap.get(id);
                                TemplateDir temDir = templateDirMap.get(dirId);
                                if (temDir != null) {
                                    esDiagram.setTemDirId(dirId);
                                    esDiagram.setTemDirName(temDir.getDirName());
                                    esDiagram.setTemType(temDir.getType());
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    @Override
    public void removeTemDirById(Long dirId) {
        TemplateDir templateDir = templateDirDao.getById(dirId);
        Set<Long> diagramIds = new HashSet<>();
        this.getAllDiagramIdByDirId(Collections.singleton(templateDir.getId()), diagramIds);
        if (!CollectionUtils.isEmpty(diagramIds)) {
            CVcDiagram cVcDiagram = new CVcDiagram();
            cVcDiagram.setIds(diagramIds.toArray(new Long[0]));
            cVcDiagram.setStatus(1);
            cVcDiagram.setDataStatus(1);
            //设置视图类型为模板，3--公共模板，4--个人模板
            cVcDiagram.setDiagramTypes(new Integer[]{3, 4});
            List<ESDiagram> esDiagramList = esDiagramSvc.queryESDiagramList(1L, cVcDiagram, "");
            if (!CollectionUtils.isEmpty(esDiagramList)) {
                throw new BinaryException("当前目录存在模板，不能删除！");
            } else {
                templateDirDao.deleteById(dirId);
            }
        } else {
            templateDirDao.deleteById(dirId);
        }
    }

    @Override
    public void relationTemDirByDiagramId(List<ESDiagram> esDiagramList) {
        if (!CollectionUtils.isEmpty(esDiagramList)) {
            Set<Long> diagramIdSet = esDiagramList.stream().map(ESDiagram::getId).collect(Collectors.toSet());
            if (!CollectionUtils.isEmpty(diagramIdSet)) {
                TemDiaRelationQuery temDiaRelation = new TemDiaRelationQuery();
                temDiaRelation.setDiagramIds(diagramIdSet.toArray(new Long[0]));
                List<TemDiaRelation> temDiaRelations = esTemDiaRelationDao.getListByCdt(temDiaRelation);
                if (!CollectionUtils.isEmpty(temDiaRelations)) {
                    // key：视图id value：模板目录id
                    Map<Long, Long> diaIdTemMap = new HashMap<>();
                    for (TemDiaRelation diaRelation : temDiaRelations) {
                        diaIdTemMap.put(diaRelation.getDiagramId(), diaRelation.getTemplateDirId());
                    }
                    if (!CollectionUtils.isEmpty(diaIdTemMap.values())) {
                        Map<Long, TemplateDir> templateDirMap = new HashMap<>();
                        // 查询所有模板信息
                        TemplateDirQuery templateDir = new TemplateDirQuery();
                        templateDir.setIds(diaIdTemMap.values().toArray(new Long[0]));
                        List<TemplateDir> templateDirs = templateDirDao.getListByCdt(templateDir);
                        for (TemplateDir dir : templateDirs) {
                            templateDirMap.put(dir.getId(), dir);
                        }
                        for (ESDiagram esDiagram : esDiagramList) {
                            Long id = esDiagram.getId();
                            Integer diagramType = esDiagram.getDiagramType();
                            // 将视图类型为3和4的模板信息补充
                            if (diagramType == 3 || diagramType == 4) {
                                Long dirId = diaIdTemMap.get(id);
                                TemplateDir temDir = templateDirMap.get(dirId);
                                if (temDir != null) {
                                    esDiagram.setTemDirId(dirId);
                                    esDiagram.setTemDirName(temDir.getDirName());
                                    esDiagram.setTemType(temDir.getType());
                                }
                            }
                        }
                    }
                }
            }
        }
    }


    @Override
    public List<TemplateDir> getPublicDirAndTemplate(String like) {
        //目录信息
        List<TemplateDir> templateDirs = queryTemplateDirs(true);
        if (CollectionUtils.isEmpty(templateDirs)) {
            return templateDirs;
        }
        Map<Long, TemplateDir> templateDirMap = templateDirs.stream().filter(each ->each.getId()!=null).collect(Collectors.toMap(TemplateDir::getId, templateDir -> templateDir, (k1, k2) -> k1));
        TemplateDirQueryBean templateDirQueryBean = new TemplateDirQueryBean();
        templateDirQueryBean.setLike(like);
        templateDirQueryBean.setPageNum(1);
        templateDirQueryBean.setPageSize(3000);
        templateDirQueryBean.setDiagramTypes(new Integer[]{3});
        Page<ESDiagram> diagramPage = queryDiagramTemplateByLike(templateDirQueryBean);
        if (CollectionUtils.isEmpty(diagramPage.getData())) {
            return templateDirs;
        }
        for (ESDiagram diagram : diagramPage.getData()) {
            if (BinaryUtils.isEmpty(diagram.getDirId())) {
                continue;
            }
            TemplateDir templateDir = templateDirMap.get(diagram.getTemDirId());
            if (BinaryUtils.isEmpty(templateDir)) {
                continue;
            }
            List<ESDiagram> diagramList = templateDir.getDiagramList();
            if (CollectionUtils.isEmpty(diagramList)) {
                diagramList= new ArrayList<>();
            }
            diagramList.add(diagram);
            templateDir.setDiagramList(diagramList);
        }
        //将子目录放入父目录中

        for (TemplateDir templateDir : templateDirs) {
            Long parentId = templateDir.getParentId();
            TemplateDir parentDir = templateDirMap.get(parentId);
            if (BinaryUtils.isEmpty(parentDir)) {
                continue;
            }

            List<TemplateDir> subTemplateDirs = parentDir.getSubTemplateDirs();
            if (CollectionUtils.isEmpty(subTemplateDirs)) {
                subTemplateDirs = new ArrayList<>();
            }
            subTemplateDirs.add(templateDir);
            parentDir.setSubTemplateDirs(subTemplateDirs);
        }
        templateDirs= templateDirs.stream().filter(templateDir -> templateDir.getParentId() == 0).collect(Collectors.toList());
        return templateDirs;
    }

    @Override
    public Integer convertDiagramToTemplate(ConvertTemplateDTO cBean) {
        List<ESDiagram> esDiagrams = esDiagramSvc.queryDBDiagramInfoByIds(new String[]{cBean.getDiagramId()});
        if (null == esDiagrams || esDiagrams.size() < 1) {
            throw new BinaryException("视图已删除,请刷新页面在转换");
        }
        ESDiagram sourceDiagram = esDiagrams.get(0);
        Integer opType = cBean.getType();
        Long templateDirId = cBean.getTemplateDirId();
        List<TemDiaRelation> temDiaRelations = new ArrayList<>();
        // 视图与目录关联，先查询视图是否与目录已有关联
        if (sourceDiagram.getFirstCopyDiagramId() != null) {

            TemDiaRelationQuery temDiaRelation = new TemDiaRelationQuery();
            temDiaRelation.setDiagramId(sourceDiagram.getFirstCopyDiagramId());
            temDiaRelations = esTemDiaRelationDao.getListByCdt(temDiaRelation);
            if (!CollectionUtils.isEmpty(temDiaRelations)) {
                // 将视图的模板信息更新
                TemDiaRelation diaRelation = temDiaRelations.get(0);
                //diaRelation.setTemplateDirId(templateDirId);
                esTemDiaRelationDao.deleteById(diaRelation.getId());
                //更新转换描述信息
              /*  ESDiagram esDiagram = esDiagramDao.getById(sourceDiagram.getFirstCopyDiagramId());
                esDiagram.setDescribeInfo(cBean.getDescribeInfo());*/
                esDiagramDao.deleteById(sourceDiagram.getFirstCopyDiagramId());
                //return 0;
            }
        }
        if (opType == 1) { //视图转模板
            ESDiagramMoveCdt esDiagramMoveCdt = new ESDiagramMoveCdt();
            esDiagramMoveCdt.setDiagramId(sourceDiagram.getDEnergy());
            esDiagramMoveCdt.setNewDirId(cBean.getTemplateDirId());
            String copyDiagramById = esDiagramSvc.copyDiagramById(esDiagramMoveCdt);
            cBean.setDiagramId(copyDiagramById);

            List<ESDiagram> diagramCopyInfos = esDiagramSvc.queryDBDiagramInfoByIds(new String[]{cBean.getDiagramId()});
            ESDiagram diagramCopyInfo = diagramCopyInfos.get(0);
            sourceDiagram.setFirstCopyDiagramId(diagramCopyInfo.getId());
            esDiagramDao.saveOrUpdate(sourceDiagram);

            //在进行模板转换时，首先判断模板分类是否还存在
            TemplateDir templateDir = templateDirDao.getById(templateDirId);
            if (BinaryUtils.isEmpty(templateDir)) {
                //模板分类已被删除
                return 1;
            }

            TemDiaRelation newRelation = new TemDiaRelation();
            newRelation.setId(ESUtil.getUUID());
            newRelation.setDiagramId(diagramCopyInfo.getId());
            newRelation.setTemplateDirId(templateDirId);
            esTemDiaRelationDao.saveOrUpdate(newRelation);

            // 将视图的类型改为3
            ESDiagram esDiagram = esDiagramDao.getById(diagramCopyInfo.getId());
            if (this.getCurrentUserType() == 1) {
                esDiagram.setDiagramType(3);
            } else {
                esDiagram.setDiagramType(4);
            }
            esDiagram.setTemConvertTime(BinaryUtils.getNumberDateTime());
            esDiagram.setDescribeInfo(cBean.getDescribeInfo());
            esDiagramDao.saveOrUpdateWithOptionExtra(esDiagram, true);
        }
        return 0;
    }
}
