package com.uino.service.cmdb.dataset.microservice;

import com.alibaba.fastjson.JSONObject;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uino.service.cmdb.microservice.ICIRltSvc;
import com.uino.service.cmdb.microservice.ICISvc;
import com.uino.bean.cmdb.base.dataset.DataSetMallApiRelationRule;
import com.uino.bean.cmdb.base.dataset.batch.SimpleFriendInfo;
import com.uino.bean.cmdb.base.dataset.chart.Chart;
import com.uino.bean.cmdb.business.dataset.FriendInfo;
import com.uino.bean.cmdb.business.dataset.QueryCondition;
import com.uino.bean.cmdb.business.dataset.RltRuleTableData;

import java.util.List;
import java.util.Map;

public interface IRelationRuleAnalysisBase {

    Map<Long, List<QueryCondition>> findTravalTree(DataSetMallApiRelationRule relationRule);

    /**
     * 指定起始CI查询朋友圈数据
     *
     * @param sCis                起始CIs
     * @param relationRule        关系遍历规则
     * @param isIncludeAllStartCI 是否过滤scis中不符合规则要求的
     * @return
     */
    Map<Long, FriendInfo> queryCiFriendByCiIdsAndRule(List<CcCiInfo> sCis, DataSetMallApiRelationRule relationRule, boolean isIncludeAllStartCI,
                                                      ICISvc iciSvc, ICIRltSvc iciRltSvc);

    /**
     * 根据规则查询所有数据,区分入口返回
     *
     * @param relationRule 规则
     * @return 规则结果
     */
    Map<Long, FriendInfo> queryCiFriendByRule(DataSetMallApiRelationRule relationRule, ICISvc iciSvc, ICIRltSvc iciRltSvc);

    Map<Long, FriendInfo> queryCiFriendByRuleWithLimit(DataSetMallApiRelationRule relationRule, Integer limit, ICISvc iciSvc, ICIRltSvc iciRltSvc);

    /**
     * 合并数据
     *
     * @param friendInfoMap
     * @return
     */
    FriendInfo mergeFriendInfoMap(Map<Long, FriendInfo> friendInfoMap);

    /**
     * 拼接数据结果
     *
     * @param sCis 入口ciId
     * @param
     * @return
     */
    RltRuleTableData disassembleFriendInfoDataByPath(List<CcCiInfo> sCis, DataSetMallApiRelationRule relationRule, ICISvc iciSvc, ICIRltSvc iciRltSvc);

    /**
     * 拼接数据结果
     *
     * @param ciIds      入口ciId
     * @param friendInfo
     * @return
     */
    RltRuleTableData disassembleFriendInfoDataByPath(DataSetMallApiRelationRule relationRule, List<Long> ciIds, FriendInfo friendInfo);

    /**
     * 统计
     *
     * @return
     */
    JSONObject countStatistics(DataSetMallApiRelationRule dataSetMallRelationApi, Map<Long, SimpleFriendInfo> simpleFriendInfoMap, Chart chart, ICISvc iciSvc, ICIRltSvc iciRltSvc);


}
