package com.uinnova.product.eam.model;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.comm.model.ci.CcCiClass;

import java.io.Serializable;
import java.util.List;

/**
 * 存放CI分类信息，及分类下CI数据的对象
 *
 * <AUTHOR>
 * @version 2020/8/10
 */
public class VcCiClassInfoDto implements Serializable {
    private static final long serialVersionUID = 1L;

    @Comment("CI分类")
    private CcCiClass ciClass;

    @Comment("属性定义")
    private List<CcCiAttrDef> attrDefs;

    @Comment("分类下CI的数量")
    private Long ciCount;

    public CcCiClass getCiClass() {
        return ciClass;
    }

    public void setCiClass(CcCiClass ciClass) {
        this.ciClass = ciClass;
    }

    public List<CcCiAttrDef> getAttrDefs() {
        return attrDefs;
    }

    public void setAttrDefs(List<CcCiAttrDef> attrDefs) {
        this.attrDefs = attrDefs;
    }

    public Long getCiCount() {
        return ciCount;
    }

    public void setCiCount(Long ciCount) {
        this.ciCount = ciCount;
    }

}
