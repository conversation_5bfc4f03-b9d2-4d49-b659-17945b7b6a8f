package com.uino.provider.server.web.cmdb.mvc;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.binary.jdbc.Page;
import com.uino.bean.cmdb.base.dataset.log.DataSetMallApiLog;
import com.uino.service.cmdb.dataset.microservice.IDataSetMallApiLogSvc;
import com.uino.provider.feign.cmdb.DataSetMallApiLogFeign;

/**
 * 
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("feign/cmdb/datasetMallApiLog")
public class DataSetMallApiLogFeignMvc implements DataSetMallApiLogFeign {

	Log logger = LogFactory.getLog(DataSetMallApiLogFeignMvc.class);
	
	@Autowired
	private IDataSetMallApiLogSvc dataSetMallApiLogSvc;

	@Override
	public Page<DataSetMallApiLog> findPage(JSONObject body) {
		logger.info("findPage param: "+body.toString());
		Long now = System.currentTimeMillis();
		Page<DataSetMallApiLog> ret = dataSetMallApiLogSvc.findPage(body.getString("dataSetId"), body.getInteger("pageNum"), body.getInteger("pageSize"), 
				body.getString("respUserName"), body.getString("respUserCode"));
		logger.info("findPage time: "+(System.currentTimeMillis()-now));
		return ret;
	}

}
