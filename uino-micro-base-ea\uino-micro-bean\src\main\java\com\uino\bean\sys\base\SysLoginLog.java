package com.uino.bean.sys.base;

import com.binary.framework.bean.annotation.Comment;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 
 * <AUTHOR>
 *
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value="登录日志信息",description = "登录日志信息")
public class SysLoginLog {

	@ApiModelProperty(value="id",example = "123")
	private Long id;

	@ApiModelProperty(value="用户id",example = "123")
	private Long userId;

	@ApiModelProperty(value="userCode")
	private String userCode;

	@ApiModelProperty(value="用户名",example = "user")
	private String userName;

	/** 所属域 */
	@ApiModelProperty(value="所属域id",example = "123")
	private Long domainId;

	/** 创建人 */
	@ApiModelProperty(value="创建人",example = "mike")
	private String creator;

	/** 修改人 */
	@ApiModelProperty(value="修改人",example = "mike")
	private String modifier;

	/** 创建时间 */
	@ApiModelProperty(value="创建时间")
	private Long createTime;

	/** 修改时间 */
	@ApiModelProperty(value="修改时间")
	private Long modifyTime;

	@Comment("发起请求的IP地址")
	private String ipAddress;
}
