package com.uinnova.product.eam.service.es;

import com.uinnova.product.eam.comm.model.es.BootEntryUserConfig;
import com.uino.dao.AbstractESBaseDao;
import com.uino.service.util.FileUtil;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.util.List;

@Service
public class BootEntryUserDao extends AbstractESBaseDao<BootEntryUserConfig, BootEntryUserConfig> {
    @Override
    public String getIndex() {
        return "uino_eam_boot_user_config";
    }

    @Override
    public String getType() {
        return getIndex();
    }

    @PostConstruct
    public void init() {
        List<BootEntryUserConfig> list = FileUtil.getData("/initdata/uino_eam_boot_user_config.json", BootEntryUserConfig.class);
        super.initIndex(list);
    }
}
