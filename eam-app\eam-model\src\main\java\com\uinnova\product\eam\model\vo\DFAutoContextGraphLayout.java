package com.uinnova.product.eam.model.vo;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

@Data
@Comment("自动成图-上下文关系图布局")
public class DFAutoContextGraphLayout {

    @Comment("布局：1=有向无环图，2=环形布局")
    private Integer layoutType;
    @Comment("布局方向：leftToRight=从左到右布局，topToBottom=从上到下的布局")
    private String layoutDirection;
    @Comment("横向间距")
    private Integer columnSpacing;
    @Comment("纵向间距")
    private Integer layerSpacing;
}
