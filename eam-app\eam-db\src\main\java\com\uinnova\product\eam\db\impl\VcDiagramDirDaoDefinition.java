package com.uinnova.product.eam.db.impl;


import com.binary.framework.dao.DaoDefinition;

import com.uinnova.product.eam.comm.model.CVcDiagramDir;
import com.uinnova.product.eam.comm.model.VcDiagramDir;


/**
 * 视图目录表[VC_DIAGRAM_DIR]数据访问对象定义实现
 */
public class VcDiagramDirDaoDefinition implements DaoDefinition<VcDiagramDir, CVcDiagramDir> {


	@Override
	public Class<VcDiagramDir> getEntityClass() {
		return VcDiagramDir.class;
	}


	@Override
	public Class<CVcDiagramDir> getConditionClass() {
		return CVcDiagramDir.class;
	}


	@Override
	public String getTableName() {
		return "VC_DIAGRAM_DIR";
	}


	@Override
	public boolean hasDataStatusField() {
		return true;
	}


	@Override
	public void setDataStatusValue(VcDiagramDir record, int status) {
		record.setDataStatus(status);
	}


	@Override
	public void setDataStatusValue(CVcDiagramDir cdt, int status) {
		cdt.setDataStatus(status);
	}


	@Override
	public void setCreatorValue(VcDiagramDir record, String creator) {
		record.setCreator(creator);
	}


	@Override
	public void setModifierValue(VcDiagramDir record, String modifier) {
		record.setModifier(modifier);
	}


}


