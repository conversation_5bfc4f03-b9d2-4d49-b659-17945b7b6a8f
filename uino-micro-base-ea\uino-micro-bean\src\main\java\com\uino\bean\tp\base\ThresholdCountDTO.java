package com.uino.bean.tp.base;

import com.uino.bean.tp.enums.TpRuleTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 记录触发阈值的次数
 *
 * <AUTHOR>
 * @data 2020年2月4日
 */
@Getter
@Setter
public class ThresholdCountDTO {
    private Long id;
    /**
     * cache key
     * <p>
     * {@code TP_+_serverId(ruleId)_+_ruleType_+_metric_+_classId_+_ciId}
     */
    private String cacheKey;
    /**
     * threshold type
     */
    private TpRuleTypeEnum ruleType;
    /**
     * metric name
     */
    private String metric;
    /**
     * ci class id
     */
    private Long ciClassId;
    /**
     * ci code
     */
    private String ciCode;
    /**
     * rule id or service id
     * <p>
     * ruleId for tp trigger
     * serviceId for twin middle taiwan linkage control
     */
    private Long ruleOrServiceId;
    /**
     * threshold id for tp trigger
     */
    private Long thresholdId;
    /**
     * accumulated times exceeding the threshold
     */
    private Integer numberOfTriggers;
    /**
     * maximum number of thresholds set
     */
    private Integer period;

    private String creator;

    private String modifier;

    private Long createTime;

    private Long modifyTime;
}
