package com.uino.bean.monitor.buiness;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@ApiModel(value = "事件选择器信息")
@Data
public class SelectorList implements Serializable {

    private static final long serialVersionUID = -5524866821598534339L;

    @ApiModelProperty(value = "事件选择器列表")
    private List<Selector> selectors = new ArrayList<>();
}
