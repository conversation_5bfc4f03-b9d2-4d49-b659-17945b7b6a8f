package com.uino.ed.eventdrive.event;

/**
 * 任务状态枚举
 */
public enum EventStatusType {

    INIT(0,"初始状态"),
    RUNNING(1,"执行中"),
    SUCCESS(2,"执行成功"),
    FAILURE(9,"执行失败");

    private Integer code;
    private String value;

    EventStatusType(Integer code, String value){
        this.code = code;
        this.value = value;
    }

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
