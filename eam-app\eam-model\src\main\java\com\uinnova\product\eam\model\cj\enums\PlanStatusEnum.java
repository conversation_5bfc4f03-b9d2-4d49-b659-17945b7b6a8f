package com.uinnova.product.eam.model.cj.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 方案状态枚举类
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum PlanStatusEnum {

    /**
     * 删除
     */
    deleted("删除"),

    /**
     * 发布
     */
    published("发布"),

    /**
     * 草稿
     */
    draft("草稿"),

    /**
     * 修改中
     */
    changing("变更中"),

    /**
     * 审批中-当前流程在审批人处
     */
    under_approver("审批中-当前流程在审批人处"),

    /**
     * 审批中-当前流程在提交人处
     */
    under_submitter("审批中-当前流程在提交人处"),

    /**
     * 审批中-当前流程在提交人处
     */
    history("历史版本");

    /**
     * 中文名称
     */
    private String showName;

    public static String queryShowName (String name) {
        for (PlanStatusEnum planStatusEnum : PlanStatusEnum.values()) {
            if (planStatusEnum.name().equals(name)) {
                return planStatusEnum.showName;
            }
        }

        return null;
    }
}
