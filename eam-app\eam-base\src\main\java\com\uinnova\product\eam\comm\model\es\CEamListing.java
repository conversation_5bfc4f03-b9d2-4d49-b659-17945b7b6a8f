package com.uinnova.product.eam.comm.model.es;

import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.comm.dto.ListClassConfig;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class CEamListing implements Condition {

    @Comment("id")
    private Long id;

    @Comment("清单名称")
    @NotNull(message = "清单名称不能为空")
    private String name;

    @Comment("清单类型")
    @NotNull(message = "清单类型不能为空")
    private String listType;

    @Comment("清单描述")
    private String description;

    @Comment("数据集--字段值是数据超市列表的数据集名称（唯一）")
    @NotNull(message = "数据集不能为空")
    private String dataSetName;

    @Comment("数据集--字段值是数据超市列表的数据集ID")
    @NotNull(message = "数据集不能为空")
    private Long dataSetId;

    @Comment("清单表头配置(包括排序，搜索，筛选)")
    private transient Map<String, Map<String, ListClassConfig>> headConfig;

    @Comment("是否发布   0=未发布，1=已发布")
    private Integer releaseState;

    @Comment("数据状态[DATA_STATUS]   0=删除，1=正常")
    private Integer dataStatus;

    @Comment("创建时间")
    private Long createTime;

    @Comment("更改时间")
    private Long modifyTime;


}
