package com.uinnova.product.eam.model.cj.domain;

import com.binary.framework.bean.Condition;
import com.uinnova.product.eam.base.cj.BaseEntity;
import lombok.Data;

/**
 * @description: 方案下的制品
 * @author: Lc
 * @create: 2022-06-14 15:09
 */
@Data
public class PlanArtifact extends BaseEntity implements Condition {

    private Long planId;

    private Long chapterId;

    private Long moduleId;

    /** 方案中视图关联制品，多个 */
    private String diagramProductType;

    /** 0: 删除 1: 在用 */
    private Integer status;

    /** 方案中选中的视图id */
    private String diagramId;

    /** 方案中选中的视图对应的版本 */
    private Integer version;

    /** 发布的视图id */
    private String releaseDiagramId;
}
