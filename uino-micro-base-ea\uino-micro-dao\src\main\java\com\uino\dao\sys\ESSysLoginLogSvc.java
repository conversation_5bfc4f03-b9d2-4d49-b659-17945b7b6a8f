package com.uino.dao.sys;

import jakarta.annotation.PostConstruct;

import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.uino.dao.AbstractESBaseDao;
import com.uino.dao.ESConst;
import com.uino.bean.sys.base.SysLoginLog;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
public class ESSysLoginLogSvc extends AbstractESBaseDao<SysLoginLog, JSONObject> {

	@Override
	public String getIndex() {
		// TODO Auto-generated method stub
		return ESConst.INDEX_SYS_LOGIN_LOG;
	}

	@Override
	public String getType() {
		// TODO Auto-generated method stub
		return ESConst.INDEX_SYS_LOGIN_LOG;
	}

	@PostConstruct
	public void init() {
		super.initIndex();
	}
}
