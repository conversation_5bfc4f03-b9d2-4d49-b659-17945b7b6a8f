package com.binary.core.lang;

import java.io.BufferedReader;
import java.io.Reader;
import java.sql.Clob;

import com.binary.core.exception.CoreException;

public abstract class CLOBUtils {

    public static Object toAny(Clob v, Class<?> toclass) {

        if (v == null || toclass == null)
            return null;

        if (toclass == String.class) {
            return toString(v);
        } else {
            throw new CoreException(" is not support toclass:'" + toclass.getName() + "'! ");
        }
    }

    public static String toString(Clob clob) {
        String reString = "";
        Reader is = null;
        try {
            is = clob.getCharacterStream();// 得到流

            BufferedReader br = new BufferedReader(is);
            String s = br.readLine();
            StringBuffer sb = new StringBuffer();
            while (s != null) {// 执行循环将字符串全部取出付值给StringBuffer由StringBuffer转成STRING
                sb.append(s);
                s = br.readLine();
            }
            reString = sb.toString();
        } catch (Exception e) {
            throw new CoreException("", e);
        } finally {
            try {
                if (is != null) {
                    is.close();
                }
            } catch (Exception e2) {
                // nothing to do
            }
        }
        return reString;
    }
}
