package com.uinnova.product.eam.comm.bean;

import java.util.Objects;

public class SubsystemTemporary {

    private Long id;

    private String userId;

    private String subsystemName;

    private String processType;

    private String tempData;

    private String step;

    private String editTime;

    private String processId;

    private Integer processState;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getSubsystemName() {
        return subsystemName;
    }

    public void setSubsystemName(String subsystemName) {
        this.subsystemName = subsystemName;
    }

    public String getProcessType() {
        return processType;
    }

    public void setProcessType(String processType) {
        this.processType = processType;
    }

    public String getTempData() {
        return tempData;
    }

    public void setTempData(String tempData) {
        this.tempData = tempData;
    }

    public String getStep() {
        return step;
    }

    public void setStep(String step) {
        this.step = step;
    }

    public String getEditTime() {
        return editTime;
    }

    public void setEditTime(String editTime) {
        this.editTime = editTime;
    }

    public String getProcessId() {
        return processId;
    }

    public void setProcessId(String processId) {
        this.processId = processId;
    }

    public Integer getProcessState() {
        return processState;
    }

    public void setProcessState(Integer processState) {
        this.processState = processState;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        SubsystemTemporary that = (SubsystemTemporary) o;
        return Objects.equals(id, that.id) &&
                Objects.equals(userId, that.userId) &&
                Objects.equals(subsystemName, that.subsystemName) &&
                Objects.equals(processType, that.processType) &&
                Objects.equals(tempData, that.tempData) &&
                Objects.equals(step, that.step) &&
                Objects.equals(editTime, that.editTime) &&
                Objects.equals(processId, that.processId) &&
                Objects.equals(processState, that.processState);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, userId, subsystemName, processType, tempData, step, editTime, processId, processState);
    }
}
