package com.uinnova.product.eam.model.vo;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;

/**
 * 数据超市ci返回实体
 * <AUTHOR>
 * @date 2022/6/14
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class EamAnalyseCiVo extends CcCiInfo {
    @Comment("源端ci数量")
    private Long sourceCount = 0L;

    @Comment("目标端ci数量")
    private Long targetCount = 0L;

    @Comment("实体属性")
    private List<CcCiInfo> attrList = new ArrayList<>();
}
