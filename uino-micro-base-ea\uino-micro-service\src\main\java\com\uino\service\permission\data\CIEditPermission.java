package com.uino.service.permission.data;

import com.uino.bean.cmdb.base.LibType;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 对象数据新增、编辑权限控制注解
 * <AUTHOR>
 */

@Target({ElementType.PARAMETER, ElementType.LOCAL_VARIABLE})
@Retention(RetentionPolicy.RUNTIME)
public @interface CIEditPermission {
    //处理CcCiInfo对象true,处理ESCIInfo对象false
    boolean object() default true;

    String ciName() default "ci";

    //属性map字段名
    String attrName() default "attrs";

}
