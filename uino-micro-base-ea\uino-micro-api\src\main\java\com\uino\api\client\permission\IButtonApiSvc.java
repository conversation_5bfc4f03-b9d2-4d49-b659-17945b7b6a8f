package com.uino.api.client.permission;

import com.uino.bean.permission.base.SysModule;
import com.uino.bean.permission.query.SysModuleCheck;

import java.util.Map;

/**
 * @Title: IButtonApiSvc
 * @Description: IButtonApiSvc
 * @Author: YGQ
 * @Create: 2021-06-28 09:41
 **/
public interface IButtonApiSvc {
    /**
     * Save button
     *
     * @param buttonDto save dto
     * @return {@link SysModule}
     */
    SysModule saveButton(SysModule buttonDto);

    /**
     * Delete button
     *
     * @param buttonId button id
     */
    void deleteButton(Long buttonId);

    /**
     * Save button sort
     *
     * @param orderDict order dict
     */
    void saveButtonSort(Map<Long, Integer> orderDict);

    /**
     * check module sign
     *
     * @param sysButton save dto
     * @return {@code true/false}
     */
    SysModuleCheck checkModuleSign(SysModule sysButton);
}
