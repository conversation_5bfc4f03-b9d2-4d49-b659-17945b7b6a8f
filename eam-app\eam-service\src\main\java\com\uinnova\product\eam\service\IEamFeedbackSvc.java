package com.uinnova.product.eam.service;

import com.binary.jdbc.Page;
import com.uinnova.product.eam.comm.bean.EamFeedback;
import com.uinnova.product.eam.comm.bean.WordDoc;
import org.springframework.web.multipart.MultipartFile;

/**
 * 意见反馈服务层
 *
 * <AUTHOR>
 */
public interface IEamFeedbackSvc {

    /**
     * 保存意见反馈信息
     * @param feedback
     * @return
     */
    Long saveFeedback(EamFeedback feedback);

    /**
     * 分页查询反馈意见数据
     * @param pageNum
     * @param pageSize
     * @param cdt
     * @param orders
     * @return
     */
    Page<EamFeedback> queryInfoPage(Integer pageNum, Integer pageSize, EamFeedback cdt, String orders);
}
