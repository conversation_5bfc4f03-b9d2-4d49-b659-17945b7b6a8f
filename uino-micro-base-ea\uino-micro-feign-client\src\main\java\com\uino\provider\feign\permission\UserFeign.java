package com.uino.provider.feign.permission;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import com.uino.bean.permission.base.SysModuleOutSide;
import com.uino.bean.permission.query.CAuthDataModuleBean;
import org.elasticsearch.index.query.QueryBuilder;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import com.binary.jdbc.Page;
import com.uino.bean.cmdb.business.ImportResultMessage;
import com.uino.bean.cmdb.business.ImportSheetMessage;
import com.uino.bean.permission.base.SysRoleDataModuleRlt;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.permission.business.CurrentUserInfo;
import com.uino.bean.permission.business.ModuleNodeInfo;
import com.uino.bean.permission.business.SysUserPwdResetParam;
import com.uino.bean.permission.business.UserInfo;
import com.uino.bean.permission.business.request.SaveUserRoleRltRequestDto;
import com.uino.bean.permission.query.CSysUser;
import com.uino.bean.permission.query.UserSearchBeanExtend;
import com.uino.provider.feign.config.BaseFeignConfig;

/**
 * <AUTHOR>
 */
@Component
@FeignClient(name = "${feign-server-name:tarsier-platform}", path = "${feign-server-path:tarsier-platform}/feign/user", configuration = {
        BaseFeignConfig.class})
public interface UserFeign {

    /**
     * 保存用户
     *
     * @param userInfo
     * @return 0表示失败，其它表示成功
     */
    @PostMapping("saveOrUpdate")
    public Long saveOrUpdate(@RequestBody UserInfo userInfo);

    /**
     * 批量保存用户信息
     *
     * @param userInfos
     * @return
     */
    @PostMapping("saveOrUpdateUserInfoBatch")
    public ImportSheetMessage saveOrUpdateUserInfoBatch(@RequestParam(value="domainId")Long domainId,@RequestParam(value="isUpdatePassword")Boolean isUpdatePassword, @RequestBody List<UserInfo> userInfos);

    /**
     * 批量保存用户
     *
     * @param userInfos
     * @return
     */
    @PostMapping("syncUserBatch")
    public ImportSheetMessage syncUserBatch(@RequestParam(value="domainId")Long domainId,@RequestBody List<UserInfo> userInfos);

    /**
     * 获取用户
     *
     * @param userId
     * @return
     */
    @PostMapping("getUserInfoById")
    public UserInfo getUserInfoById(@RequestBody Long userId);

    @PostMapping("getUserInfoByLoginCode")
    UserInfo getUserInfoByLoginCode(String loginCode);

    /**
     * 条件查询用户信息
     *
     * @return
     */
    @PostMapping("getSysUserByCdtWithoutBody")
    public List<SysUser> getSysUserByCdtWithoutBody();

    /**
     * 条件查询用户信息
     *
     * @param cdt
     * @return
     */
    @PostMapping("getSysUserByCdt")
    public List<SysUser> getSysUserByCdt(@RequestBody CSysUser cdt);

    /**
     * 条件查询用户信息-包含角色
     *
     * @param rmSensitiveData 是否过滤敏感信息
     * @return
     */
    @PostMapping("getUserInfoByCdtWithoutBody")
    public List<UserInfo> getUserInfoByCdtWithoutBody(@RequestParam(value = "rmSensitiveData") boolean rmSensitiveData);

    /**
     * 条件查询用户信息-包含角色
     *
     * @param cdt
     * @param rmSensitiveData 是否过滤敏感信息
     * @return
     */
    @PostMapping("getUserInfoByCdt")
    public List<UserInfo> getUserInfoByCdt(@RequestBody CSysUser cdt,
                                           @RequestParam(value = "rmSensitiveData") boolean rmSensitiveData);

    /**
     * 全文检索--分页
     *
     * @param bean
     * @return
     */
    @PostMapping("getListByQuery")
    public Page<UserInfo> getListByQuery(@RequestBody UserSearchBeanExtend bean);

    /**
     * 获取角色下的用户
     *
     * @param roleId
     * @return
     */
    @PostMapping("getUserByRoleId")
    public List<SysUser> getUserByRoleId(@RequestBody Long roleId);

    /**
     * 更新当前用户信息
     *
     * @param user
     * @param file
     * @return
     */
    @PostMapping(value = "updateCurUser", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Long updateCurUser(@RequestParam(value = "user") SysUser user,
                              @RequestPart(value = "file", required = false) MultipartFile file);

    /**
     * 用户更新个人密码
     *
     * @param pwdParam
     * @return
     */
    @PostMapping("resetPwd")
    public Long resetPwd(@RequestParam(value="domainId")Long domainId,@RequestBody SysUserPwdResetParam pwdParam);

    /**
     * 管理员更新密码
     *
     * @param pwdParam
     * @return
     */
    @PostMapping("resetPwdByAdmin")
    public Long resetPwdByAdmin(@RequestBody SysUserPwdResetParam pwdParam);

    /**
     * 导入用户
     *
     * @param file
     * @return
     */
    @PostMapping(value = "importUserByExcel", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ImportResultMessage importUserByExcel(@RequestParam(value="domainId")Long domainId,@RequestPart(value = "file") MultipartFile file);

    /**
     * 导出用户到Excel文件
     *
     * @param isTpl
     * @return
     */
    @PostMapping(value = "exportUserToExcelWithoutBody")
    public ResponseEntity<byte[]> exportUserToExcelWithoutBody(@RequestParam(value = "isTpl") Boolean isTpl);

    /**
     * 导出用户到Excel文件
     *
     * @param isTpl
     * @param cdt
     * @return
     */
    @PostMapping(value = "exportUserToExcel")
    public ResponseEntity<byte[]> exportUserToExcel(@RequestParam(value = "isTpl") Boolean isTpl,
                                                    @RequestBody CSysUser cdt);

    /**
     * 根据id删除用户及其关联数据
     *
     * @param userId
     * @return
     */
    @PostMapping("deleteById")
    public Integer deleteById(@RequestBody Long userId);

    /**
     * 根据登录代码LoginCode批量删除用户
     *
     * @param loginCodes
     * @return
     */
    @PostMapping("deleteSysUserByLoginCodeBatch")
    public Integer deleteSysUserByLoginCodeBatch(@RequestParam(value = "domainId")Long domainId,@RequestBody List<String> loginCodes);

    /**
     * 绑定用户角色关系
     *
     * @param bean
     * @return
     */
    @PostMapping("saveUserRoleRlt")
    Integer saveUserRoleRlt(@RequestBody SaveUserRoleRltRequestDto bean);

    /**
     * 获取菜单tree结构
     *
     * @param userId 为空则获取所有tree；不为空则获取该用户有权限的菜单tree
     * @return
     */
    @PostMapping("getModuleTree")
    ModuleNodeInfo getModuleTree(@RequestParam(value = "domainId", required = false) Long domainId,
            @RequestParam(value = "userId", required = false) Long userId);

    /**
     * 获取指定用户拥有的数据权限字典
     *
     * @param userId
     * @param moduleCodes
     * @return
     */
    @PostMapping("getDataModule")
    public Map<String, List<SysRoleDataModuleRlt>> getDataModule(@RequestParam(value = "userId") Long userId,
                                                                 @RequestBody List<String> moduleCodes);

    /**
     * 收藏菜单
     *
     * @param userId
     * @param moduleId
     * @return
     */
    @PostMapping("enshrineModule")
    Long enshrineModule(@RequestParam(value="domainId") Long domainId,@RequestBody Long userId, @RequestParam(value = "moduleId") Long moduleId);

    /**
     * 取消菜单收藏
     *
     * @param userId
     * @param moduleId
     * @return
     */
    @PostMapping("unenshrineModule")
    Integer unenshrineModule(@RequestParam(value="domainId")Long domainId,@RequestBody Long userId, @RequestParam(value = "moduleId") Long moduleId);

    /**
     * 统计符合条件的用户
     *
     * @param query
     * @return
     */
    @PostMapping("countByCondition")
    long countByCondition(@RequestParam(value="domainId")Long domainId,@RequestBody QueryBuilder query);

    /**
     * 记录用户登陆失败次数
     *
     * @param loginName
     * @param success
     * @return
     */
    @PostMapping("recordLoginFailNum")
    int recordLoginFailNum(@RequestParam(value = "domainId") Long domainId,@RequestBody String loginName, @RequestParam(value = "success") boolean success);

    /**
     * 获取当前登陆用户
     *
     * @return
     */
    @PostMapping("getCurrentUser")
    CurrentUserInfo getCurrentUser();

    /**
     * 根据锁定时长解锁用户
     *
     * @param durationSecond
     * @return 解锁的用户数量
     */
    @PostMapping("unLockUsersByDuration")
    int unLockUsersByDuration(@RequestBody Long durationSecond);

    /**
     * 获取登陆失败次数锁定
     *
     * @return
     */
    @PostMapping("getLoginFailLockNum")
    public int getLoginFailLockNum();

    /**
     * 获取用户锁定时长
     *
     * @return
     */
    @PostMapping("getUserLockDuration")
    public long getUserLockDuration();

    /**
     * 校验用户权限
     */
    @PostMapping("verifyAuth")
    public void verifyAuth();

    /**
     * 根据登录名查询用户权限
     *
     * @param loginCode login code
     * @return {@link List<SysModuleOutSide>}
     */
    @PostMapping("getModulesByLoginCode")
    List<SysModuleOutSide> getModulesByLoginCode(@RequestParam(value="domainId") Long domainId,@RequestParam(value = "loginCode") String loginCode);

    /**
     * Query the data module permissions corresponding to the user
     *
     * @param cAuthDataModuleBean cAuthDataModuleBean
     * @return {@link  Map<String, Integer>}
     * */
    @PostMapping("getDataPermissionByUser")
    Map<Long, Map<String, Integer>> getDataPermissionByUser(@RequestBody CAuthDataModuleBean cAuthDataModuleBean);

    /**
     * 根据角色名称查询下面的人员
     * @param roleName
     * @return
     */
    @GetMapping("/getUserByRoleName")
    List<SysUser> getUserByRoleName(String roleName);

    /**
     * 根据用户名称模糊查询
     * @param userName
     * @return
     */
    @PostMapping("/getUserInfoByName")
    List<SysUser> getUserInfoByName(String userName);

    /**
     * 模糊搜索用户分页展示
     * @param userName
     * @return
     */
    @GetMapping("/findUserInfoByNameForPage")
    Page<SysUser> findUserInfoByNameForPage(Integer pageNum, Integer pageSize, String userName);

    @GetMapping("/getNameByCodes")
    Map<String, String> getNameByCodes(Collection<String> loginCodes);
}
