package com.uino.dao.cmdb;

import com.uino.dao.AbstractESBaseDao;
import com.uino.dao.ESConst;
import com.uino.bean.cmdb.base.ESCiRltAutoBuild;
import org.springframework.stereotype.Repository;

import jakarta.annotation.PostConstruct;

/**
 * @Classname ESCiRltAutoBuildSvc
 * @Description TODO
 * @Date 2020/6/29 10:06
 * <AUTHOR> sh
 */
@Repository
public class ESCiRltAutoBuildSvc extends AbstractESBaseDao<ESCiRltAutoBuild, ESCiRltAutoBuild> {

    @Override
    public String getIndex() {
        return ESConst.INDEX_CMDB_CIRLT_AUTO_BUILD;
    }

    @Override
    public String getType() {
        return ESConst.INDEX_CMDB_CIRLT_AUTO_BUILD;
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }
}
