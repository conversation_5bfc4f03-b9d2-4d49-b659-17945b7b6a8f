package com.uino.cmdb.dir;

import static org.junit.Assert.assertEquals;

import java.util.Map;

import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import com.alibaba.fastjson.JSONObject;
import com.binary.json.JSON;
import com.uino.StartBaseWebAppliaction;
import com.uino.bean.cmdb.base.CcCiClassDir;

@RunWith(SpringRunner.class)
@WebAppConfiguration
@SpringBootTest(classes = {StartBaseWebAppliaction.class})
@ActiveProfiles("provider-local")
@AutoConfigureMockMvc(addFilters = false)
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class DirMvcTest {

    @Autowired
    private MockMvc mockMvc;

    private String token = "ca14ed042fe7912426b484f6ea5c754b4fb51ae4a72037e9127da51743e0d1f9650e6e78ed4466ac970acb6a0b2f46fd26958c4ab2d115cc71b34ca8a02db24c";

    private static Long dirId = 0L;
    
    @Test
    public void atestSaveOrUpdateDir() throws Exception {
        CcCiClassDir param = new CcCiClassDir();
        param.setDirName("test-test");
        param.setParentId(0L);
        param.setCiType(1);
        String requestJson = JSONObject.toJSONString(param);
        ResultActions reaction =
            this.mockMvc.perform(MockMvcRequestBuilders.post("/cmdb/dir/saveOrUpdateDir").contentType(MediaType.APPLICATION_JSON).content(requestJson).accept(MediaType.APPLICATION_JSON)// 返回值接收json
                .header("Content-Type", "application/json").header("token", token));
        MvcResult mvcResult = reaction.andReturn();
        String resStr = mvcResult.getResponse().getContentAsString();
        String dirIdStr = JSON.toString(JSON.toObject(resStr, Map.class).get("data"));
        dirId = JSON.toObject(dirIdStr, Long.class);
        int status = mvcResult.getResponse().getStatus();
        assertEquals(200, status);
    }

    @Test
    public void ztestRemoveDirById() throws Exception {
        String requestJson = JSONObject.toJSONString(dirId);
        ResultActions reaction =
            this.mockMvc.perform(MockMvcRequestBuilders.post("/cmdb/dir/removeDirById").contentType(MediaType.APPLICATION_JSON).content(requestJson).accept(MediaType.APPLICATION_JSON)// 返回值接收json
                .header("Content-Type", "application/json").header("token", token));
        MvcResult mvcResult = reaction.andReturn();
        int status = mvcResult.getResponse().getStatus();
        assertEquals(200, status);
    }

}
