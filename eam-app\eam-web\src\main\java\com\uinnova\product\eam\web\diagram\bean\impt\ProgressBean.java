package com.uinnova.product.eam.web.diagram.bean.impt;

import com.binary.core.exception.MessageException;
import com.binary.framework.bean.annotation.Comment;

public class ProgressBean {

    @Comment("进度状态:准备")
    public static final int PREPARATION = 0;
    @Comment("进度状态:完成")
    public static final int COMPLETED = -1;
    @Comment("进度状态:异常")
    public static final int EXCEPTION = -2;

    @SuppressWarnings("unused")
    private String threadName;
    @Comment("从系统中查询进度信息的key")
    private String key;
    @Comment("进度状态,0表示未完成,-1表示完成,-2表示出现异常")
    private int status = PREPARATION;
    @Comment("总记录数")
    private int totalRecords;
    @Comment("已处理记录数")
    private int processedRecords;
    @Comment("已处理百分比")
    private int percentage;
    @Comment("处理完成后的结果信息,格式可以自定义")
    private Object resultData;

    public ProgressBean() {
    }

    /**
     * 
     * @param key
     */
    public ProgressBean(String key) {
        this.key = key;
    }

    /**
     * 
     * @param key
     * @param totalRecords
     */
    public ProgressBean(String key, int totalRecords) {
        this.key = key;
        this.totalRecords = totalRecords;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getTotalRecords() {
        return totalRecords;
    }

    public void setTotalRecords(int totalRecords) {
        this.totalRecords = totalRecords;
    }

    public int getProcessedRecords() {
        return processedRecords;
    }

    public void setProcessedRecords(int processedRecords) {
        this.processedRecords = processedRecords;
    }

    public int getPercentage() {
        return percentage;
    }

    public void setPercentage(int percentage) {
        this.percentage = percentage;
    }

    public Object getResultData() {
        return resultData;
    }

    public void setResultData(Object resultData) {
        this.resultData = resultData;
    }

    /**
     * 更新进度
     * 
     * @param num
     */
    public void progressing(int num) {
        if (0 == totalRecords)
            throw MessageException.i18n("BS_MDOMAIN_TOTALRECORDSIS0");

        synchronized (key) {
            this.processedRecords += num;
            this.percentage = processedRecords * 100 / totalRecords;
        }
        if (processedRecords > totalRecords)
            throw MessageException.i18n("BS_MDOMAIN_PROCESSED_OUT_OF_BOUNDS");

    }

    /**
     * 重置更新进度
     * 
     * @param num
     */
    public void resetProgressing(int totalRecords) {
        if (0 == totalRecords)
            throw MessageException.i18n("BS_MDOMAIN_TOTALRECORDSIS0");

        synchronized (key) {
            this.totalRecords = totalRecords;
            this.processedRecords = 0;
            this.percentage = 0;
        }

    }

    /**
     * 完成进度,延时清理
     */
    public void complete() {
        ProgressCatch.endProgress(this);
        this.status = COMPLETED;
    }

    /**
     * 完成进度,延时清理
     * 
     * @param timeOut
     */
    public void complete(Long timeOut) {
        this.status = COMPLETED;
        ProgressCatch.endProgress(this, timeOut);
    }

    public void err() {
        this.status = EXCEPTION;
        ProgressCatch.endProgress(this);
    }
}
