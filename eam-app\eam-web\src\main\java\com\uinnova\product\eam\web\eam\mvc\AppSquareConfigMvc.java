package com.uinnova.product.eam.web.eam.mvc;

import com.alibaba.fastjson.JSONObject;
import com.binary.core.exception.BinaryException;
import com.binary.core.util.BinaryUtils;
import com.binary.framework.web.RemoteResult;
import com.google.common.collect.Lists;
import com.uinnova.product.eam.comm.bean.CatalogDto;
import com.uinnova.product.eam.comm.model.es.AppSquareConfig;
import com.uinnova.product.eam.model.AppSquareConfigBo;
import com.uinnova.product.eam.model.dto.AppSquareConfigVo;
import com.uinnova.product.eam.service.AppSquareConfigSvc;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uino.api.client.cmdb.ICIClassApiSvc;
import com.uino.web.auth.VerifyAuthUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Classname AppSquareConfigMvc
 * @Date 2022/5/25 14:40
 */
@RestController
@RequestMapping("/eam/appSquareConfig")
public class AppSquareConfigMvc {
    public static final List<String> SYS_MODULE_SIGN = Lists.newArrayList("资产管理", "架构地图", "专题分析");


    @Resource
    VerifyAuthUtil verifyAuth;

    @Autowired
    private AppSquareConfigSvc basicOperationSettingSvc;

    @Autowired
    private ICIClassApiSvc ciClassApiSvc;

    @PostMapping("check")
    public RemoteResult check(@RequestBody AppSquareConfigBo basicSettingBo) {
        String result = basicOperationSettingSvc.check(basicSettingBo);
        return new RemoteResult(result);
    }

    @PostMapping("/saveOrUpdate")
    public RemoteResult saveOrUpdate(@RequestBody AppSquareConfigBo basicSettingBo) {
        verifyAuth.verifyAuth(SYS_MODULE_SIGN);
        Long id = basicOperationSettingSvc.saveOrUpdate(basicSettingBo);
        return new RemoteResult(id);
    }

    @GetMapping("/getInfoById")
    public RemoteResult getInfoById(@RequestParam Long id) {
        verifyAuth.verifyAuth(SYS_MODULE_SIGN);
        AppSquareConfig basicVo = basicOperationSettingSvc.getInfoById(id);
        return new RemoteResult(basicVo);
    }

    @GetMapping("/deleteInfoById")
    public RemoteResult deleteInfoById(@RequestParam Long id) {
        verifyAuth.verifyAuth(SYS_MODULE_SIGN);
        int count = basicOperationSettingSvc.deleteInfoById(id);
        return new RemoteResult(count);
    }

    @PostMapping("/getListInfo")
    public RemoteResult getListInfo(@RequestBody AppSquareConfigBo basicSettingBo) {
        verifyAuth.verifyAuth(SYS_MODULE_SIGN);
        List<AppSquareConfig> result = basicOperationSettingSvc.getListInfo(basicSettingBo);
        return new RemoteResult(result);
    }


    @PostMapping("/batchSaveInfos")
    public RemoteResult batchSaveInfos(@RequestBody List<AppSquareConfigBo> list) {
        Integer result = basicOperationSettingSvc.batchSaveInfos(list);
        return new RemoteResult(result);
    }

    @GetMapping("/exportConfigInitData")
    public ResponseEntity<byte[]> exportConfigInitData() {
        return basicOperationSettingSvc.exportConfigInitData();
    }

    @GetMapping("/reductInitData")
    public RemoteResult reductInitData(@RequestParam Long cardId) {
        Boolean result = basicOperationSettingSvc.reductInitData(cardId);
        return new RemoteResult(result);
    }


    @GetMapping("/getDataByCardId")
    public RemoteResult getDataByCardId(@RequestParam Long cardId) {
        AppSquareConfigVo result = basicOperationSettingSvc.getDataByCardId(cardId);
        return new RemoteResult(result);
    }


    @GetMapping("/getFilterDataSet")
    public RemoteResult getFilterDataSet() {
        List<JSONObject> dataSet = basicOperationSettingSvc.getFilterDataSet();
        return new RemoteResult(dataSet);
    }

    @GetMapping("dragSort")
    public RemoteResult dragSort(@RequestParam Long cardId, @RequestParam Integer sortNum, @RequestParam(defaultValue = "") String type) {
        Assert.notNull(cardId, "拖拽卡片不能为空");
        Assert.notNull(sortNum, "要插入的位置不能为空");
        boolean result;
        if (BinaryUtils.isEmpty(type)) {
            // 平铺排序
            result = basicOperationSettingSvc.dragSort(cardId, sortNum);
        } else {
            // 分组排序
            result = basicOperationSettingSvc.groupDragSort(cardId, sortNum);
        }
        return new RemoteResult(result);
    }

    @GetMapping("/refreshData")
    public void refreshData() {
        basicOperationSettingSvc.refreshData();
    }

    @GetMapping("getAppSquareConfigByClassCode")
    public RemoteResult getAppSquareConfigByClassCode(@RequestParam String classCode,
                                                      @RequestParam(defaultValue = "3") Integer assetType,
                                                      @RequestParam(defaultValue = "6") String classification) {
        verifyAuth.verifyAuth(SYS_MODULE_SIGN);
        AppSquareConfig basicVo = basicOperationSettingSvc.getAppSquareConfigByClassCode(classCode, assetType, classification);
        return new RemoteResult(basicVo);
    }

    @GetMapping("getAppSquareConfigByClassId")
    public RemoteResult getAppSquareConfigByClassCode(@RequestParam Long classId,
                                                      @RequestParam(defaultValue = "3") Integer assetType,
                                                      @RequestParam(defaultValue = "6") String classification) {
        verifyAuth.verifyAuth(SYS_MODULE_SIGN);
        CcCiClassInfo ccCiClassInfo = ciClassApiSvc.queryClassInfoById(classId);
        if (BinaryUtils.isEmpty(ccCiClassInfo)) {
            throw new BinaryException("找不到对应的分类信息");

        }
        String classCode = ccCiClassInfo.getCiClass().getClassCode();
        AppSquareConfig basicVo = basicOperationSettingSvc.getAppSquareConfigByClassCode(classCode, assetType, classification);
        return new RemoteResult(basicVo);
    }

    @GetMapping("/findPlanDataSetList")
    public RemoteResult findPlanDataSetList() {
        List<JSONObject> dataSet = basicOperationSettingSvc.findPlanDataSetList();
        return new RemoteResult(dataSet);
    }

    @GetMapping("getExistsClassByClassification")
    public RemoteResult getExistsClassByClassification(String classification) {
        List<String> classCodes = basicOperationSettingSvc.getExistsClassByClassification(classification);
        return new RemoteResult(classCodes);
    }

    @PostMapping("/update/business")
    @ModDesc(desc = "更换业务全景图视图或数据集", pDesc = "", pType = Long.class, rDesc = "矩阵表格数据", rType = Object.class)
    public RemoteResult updateBusinessDiagram(@RequestBody CatalogDto dto) {
        Long result = basicOperationSettingSvc.updateBusinessDiagram(dto);
        return new RemoteResult(result);
    }
}
