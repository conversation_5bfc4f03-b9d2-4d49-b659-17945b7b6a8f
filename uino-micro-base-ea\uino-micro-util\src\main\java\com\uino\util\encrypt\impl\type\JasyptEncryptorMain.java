package com.uino.util.encrypt.impl.type;

import org.jasypt.encryption.StringEncryptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;

@ComponentScan({"com.ulisesbocchio.jasyptspringboot.*.**" })
public class JasyptEncryptorMain {

    private final StringEncryptor stringEncryptor;

	@Autowired
	public JasyptEncryptorMain(StringEncryptor stringEncryptor) {
		this.stringEncryptor = stringEncryptor;
	}

	@Bean
	private JasyptEncryptorMain getJasyptEncryptorMain(){
		return new JasyptEncryptorMain(stringEncryptor);
	}
	
	public String encrypt(String message){
		return stringEncryptor.encrypt(message);
	}
	
	public String decrypt(String message){
		return stringEncryptor.decrypt(message);
	}
}
