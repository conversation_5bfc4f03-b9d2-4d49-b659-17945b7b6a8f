package com.uinnova.product.vmdb.comm.doc.api;

import java.io.Serializable;
import java.util.List;

/**
 * 
 * <AUTHOR>
 *
 */
public class MvcApi implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 类名 **/
    private String className;

    /** mvc名称(全限定类名) **/
    private String mvc;

    /** RequestMapping **/
    private String path;

    /** 责任人 **/
    private String author;

    /** 描述 **/
    private String desc;

    /** 方法API **/
    private List<MvcModApi> modApis;

    public String getClassName() {
        return className;
    }

    public void setClassName(String className) {
        this.className = className;
    }

    public String getMvc() {
        return mvc;
    }

    public void setMvc(String mvc) {
        this.mvc = mvc;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getAuthor() {
        return author;
    }

    public void setAuthor(String author) {
        this.author = author;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public List<MvcModApi> getModApis() {
        return modApis;
    }

    public void setModApis(List<MvcModApi> modApis) {
        this.modApis = modApis;
    }

}
