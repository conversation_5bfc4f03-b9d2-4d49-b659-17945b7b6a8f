package com.binary.jdbc.adapter.support.kingbase;

import java.math.BigDecimal;
import java.sql.Blob;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Time;
import java.sql.Timestamp;
import java.util.HashMap;
import java.util.Map;

import com.binary.jdbc.adapter.FieldMapping;
import com.binary.jdbc.adapter.support.AbstractJdbcAdapter;
import com.binary.jdbc.exception.JdbcException;

public abstract class AbstractKingbaseAdapter  extends AbstractJdbcAdapter {
	
	
	protected Map<String,FieldMapping> newFieldMappings() {
		Map<String,FieldMapping> mapping = new HashMap<String,FieldMapping>();
		mapping.put("BIGINT", new FieldMapping("BIGINT", Long.class, "getLong"));
		mapping.put("BIT", new FieldMapping("BIT", String.class, "getString"));
		mapping.put("BIT_VARYING", new FieldMapping("BIT_VARYING", String.class, "getString"));
		mapping.put("BLOB", new FieldMapping("BLOB", Blob.class, "getBlob"));
		mapping.put("BOOLEAN", new FieldMapping("BOOLEAN", Boolean.class, "getBoolean"));
		mapping.put("BYTEA", new FieldMapping("BYTEA", byte[].class, "getBytes"));
		mapping.put("CHAR", new FieldMapping("CHAR", String.class, "getString"));
		mapping.put("CLOB", new FieldMapping("CLOB", String.class, "getString"));
		mapping.put("TIMESTAMP", new FieldMapping("TIMESTAMP", Timestamp.class, "getTimestamp"));
		mapping.put("NUMERIC", new FieldMapping("NUMERIC", BigDecimal.class, "getBigDecimal"));
		mapping.put("DOUBLE", new FieldMapping("DOUBLE", Double.class, "getDouble"));
		mapping.put("REAL", new FieldMapping("REAL", Float.class, "getFloat"));
		mapping.put("INTEGER", new FieldMapping("INTEGER", Integer.class, "getInt"));
		mapping.put("INTERVAL_DAY", new FieldMapping("INTERVAL_DAY", String.class, "getString"));
		mapping.put("INTERVAL_DAY_TO_SECOND", new FieldMapping("INTERVAL_DAY_TO_SECOND", String.class, "getString"));
		mapping.put("INTERVAL_HOUR", new FieldMapping("INTERVAL_HOUR", String.class, "getString"));
		mapping.put("INTERVAL_MINUTE", new FieldMapping("INTERVAL_MINUTE", String.class, "getString"));
		mapping.put("INTERVAL_MONTH", new FieldMapping("INTERVAL_MONTH", String.class, "getString"));
		mapping.put("INTERVAL_SECOND", new FieldMapping("INTERVAL_SECOND", String.class, "getString"));
		mapping.put("INTERVAL_YEAR", new FieldMapping("INTERVAL_YEAR", String.class, "getString"));
		mapping.put("INTERVAL_YEAR_TO_MONTH", new FieldMapping("INTERVAL_YEAR_TO_MONTH", String.class, "getString"));
		mapping.put("SMALLINT", new FieldMapping("SMALLINT", Short.class, "getShort"));
		mapping.put("TEXT", new FieldMapping("TEXT", String.class, "getString"));
		mapping.put("TIME", new FieldMapping("TIME", Time.class, "getTime"));
		mapping.put("TIME_WITH_TIME_ZONE", new FieldMapping("TIME_WITH_TIME_ZONE", Time.class, "getTime"));
		mapping.put("TIMESTAMP_WITH_TIME_ZONE", new FieldMapping("TIMESTAMP_WITH_TIME_ZONE", Timestamp.class, "getTimestamp"));
		mapping.put("TINYINT", new FieldMapping("TINYINT", Byte.class, "getByte"));
		mapping.put("TSQUERY", new FieldMapping("TSQUERY", String.class, "getString"));
		mapping.put("TSVECTOR", new FieldMapping("TSVECTOR", String.class, "getString"));
		mapping.put("VARCHAR", new FieldMapping("VARCHAR", String.class, "getString"));
		mapping.put("XML", new FieldMapping("XML", String.class, "getString"));
		
		return mapping;
	}
	
	
	
	@Override
	public String getSchema(Connection conn, String userName) {
		if(userName != null) userName = userName.toUpperCase(); 
		return userName;
	}
	
	
	@Override
	public PreparedStatement prepareUpdateStatement(Connection conn, String sql) {
		try {
			return conn.prepareStatement(sql, ResultSet.TYPE_SCROLL_INSENSITIVE, ResultSet.CONCUR_UPDATABLE);
		} catch (SQLException e) {
			throw new JdbcException(e);
		}
	}
	
	
	
	

}
