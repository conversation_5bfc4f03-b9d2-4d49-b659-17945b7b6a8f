package com.uinnova.product.vmdb.provider.sys.bean;

public enum PasswordComplicationType {

	LOWER_LETTER(0),
	UPPER_LETTER(1),
	NUMBER(2),
	SPECIAL_LETTER(3);
	
	private Integer code;

    private PasswordComplicationType(Integer code) {
        this.code = code;
    }

    public Integer getType() {
        return code;
    }

    public static PasswordComplicationType valueOf(Integer code) {
        if (LOWER_LETTER.code.equals(code)) {
            return LOWER_LETTER;
        } else if (UPPER_LETTER.code.equals(code)) {
            return UPPER_LETTER;
        } else if (NUMBER.code.equals(code)) {
            return NUMBER;
        } else if (SPECIAL_LETTER.code.equals(code)) {
            return SPECIAL_LETTER;
        } else {
            return null;
        }
    }

}
