package com.uino.bean.permission.query;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 数据模块查询对象
 * 
 * <AUTHOR>
 */
@Setter
@Getter
@ApiModel(value="数据模块查询对象信息",description = "数据模块查询对象信息")
public class CAuthBean implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value="用户id",example = "123")
    private Long userId;

    @ApiModelProperty(value="角色id",example = "123")
    private Long roleId;

    @ApiModelProperty(value="数据模块码")
    private String dataModuleCode;
}
