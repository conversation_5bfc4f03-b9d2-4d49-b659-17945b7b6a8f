package com.uinnova.product.vmdb.comm.model.ci;

import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;

/**
 * 
 * <AUTHOR>
 *
 */
@Comment("CI分类表单配置表[CC_CI_CLASS_FORM]")
public class CcCiClassForm implements EntityBean {
    private static final long serialVersionUID = 1L;

    @Comment("ID[ID]    同ClassId")
    private Long id;

    @Comment("所属分类[CLASS_ID]")
    private Long classId;

    @Comment("标题名称[TITLE]")
    private String title;

    @Comment("总行数[FORM_ROWS]")
    private Integer formRows;

    @Comment("总列表[FORM_COLS]")
    private Integer formCols;

    @Comment("排序顺序[ORDER_NO]")
    private Integer orderNo;

    @Comment("备用_1[NM_CUSTOM_1]")
    private String nmCustom1;

    @Comment("备用_2[NM_CUSTOM_2]")
    private String nmCustom2;

    @Comment("备用_3[NM_CUSTOM_3]")
    private String nmCustom3;

    @Comment("备用_4[NM_CUSTOM_4]")
    private String nmCustom4;

    @Comment("备用_5[NM_CUSTOM_5]")
    private String nmCustom5;

    @Comment("备用_6[NM_CUSTOM_6]")
    private String nmCustom6;

    @Comment("所属域[DOMAIN_ID]")
    private Long domainId;

    @Comment("数据状态[DATA_STATUS]    0=删除，1=正常")
    private Integer dataStatus;

    @Comment("创建人[CREATOR]")
    private String creator;

    @Comment("修改人[MODIFIER]")
    private String modifier;

    @Comment("创建时间[CREATE_TIME]    yyyyMMddHHmmss")
    private Long createTime;

    @Comment("更新时间[MODIFY_TIME]    yyyyMMddHHmmss")
    private Long modifyTime;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getClassId() {
        return this.classId;
    }

    public void setClassId(Long classId) {
        this.classId = classId;
    }

    public String getTitle() {
        return this.title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Integer getFormRows() {
        return this.formRows;
    }

    public void setFormRows(Integer formRows) {
        this.formRows = formRows;
    }

    public Integer getFormCols() {
        return this.formCols;
    }

    public void setFormCols(Integer formCols) {
        this.formCols = formCols;
    }

    public Integer getOrderNo() {
        return this.orderNo;
    }

    public void setOrderNo(Integer orderNo) {
        this.orderNo = orderNo;
    }

    public String getNmCustom1() {
        return this.nmCustom1;
    }

    public void setNmCustom1(String nmCustom1) {
        this.nmCustom1 = nmCustom1;
    }

    public String getNmCustom2() {
        return this.nmCustom2;
    }

    public void setNmCustom2(String nmCustom2) {
        this.nmCustom2 = nmCustom2;
    }

    public String getNmCustom3() {
        return this.nmCustom3;
    }

    public void setNmCustom3(String nmCustom3) {
        this.nmCustom3 = nmCustom3;
    }

    public String getNmCustom4() {
        return this.nmCustom4;
    }

    public void setNmCustom4(String nmCustom4) {
        this.nmCustom4 = nmCustom4;
    }

    public String getNmCustom5() {
        return this.nmCustom5;
    }

    public void setNmCustom5(String nmCustom5) {
        this.nmCustom5 = nmCustom5;
    }

    public String getNmCustom6() {
        return this.nmCustom6;
    }

    public void setNmCustom6(String nmCustom6) {
        this.nmCustom6 = nmCustom6;
    }

    public Long getDomainId() {
        return this.domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }

    public Integer getDataStatus() {
        return this.dataStatus;
    }

    public void setDataStatus(Integer dataStatus) {
        this.dataStatus = dataStatus;
    }

    public String getCreator() {
        return this.creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getModifier() {
        return this.modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public Long getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getModifyTime() {
        return this.modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }

}
