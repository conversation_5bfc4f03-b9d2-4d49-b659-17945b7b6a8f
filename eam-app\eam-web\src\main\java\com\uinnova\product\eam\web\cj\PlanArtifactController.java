package com.uinnova.product.eam.web.cj;

import com.alibaba.excel.util.StringUtils;
import com.uinnova.product.eam.model.cj.dto.PlanArtifactDTO;
import com.uinnova.product.eam.model.cj.domain.PlanArtifact;
import com.uinnova.product.eam.service.cj.service.PlanArtifactService;
import com.uinnova.product.eam.service.exception.BusinessException;
import org.springframework.util.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @description:
 * @author: Lc
 * @create: 2022-06-14 17:53
 */
@RestController
@RequestMapping("/planArtifact")
public class PlanArtifactController {

    @Resource
    private PlanArtifactService planArtifactService;

    @GetMapping("/checkPlanArtifact")
    public boolean checkPlanArtifact(@RequestParam("diagramProductType") String diagramProductType) {
        if (StringUtils.isEmpty(diagramProductType)) {
            throw new BusinessException("制品类型不能为空!");
        }
        PlanArtifact planArtifact = new PlanArtifact();
        planArtifact.setDiagramProductType(diagramProductType);
        List<PlanArtifact> planArtifactList = planArtifactService.findPlanArtifactList(planArtifact);
        if (CollectionUtils.isEmpty(planArtifactList)) {
            return true;
        }
        return false;
    }

    @PostMapping("/checkPlanRelationDiagram")
    public boolean checkPlanRelationDiagram(@RequestBody List<String> diagramIdList) {
        List<PlanArtifact> planDiagramList = planArtifactService.findPlanDiagramList(diagramIdList);
        if (!CollectionUtils.isEmpty(planDiagramList)) {
            return true;
        }
        return false;
    }

    @PostMapping("/findByDiagramIds")
    public List<PlanArtifactDTO> findByDiagramIds(@RequestBody List<String> diagramIdList) {
        List<PlanArtifact> planArtifacts = planArtifactService.findPlanDiagramList(diagramIdList);
        if (CollectionUtils.isEmpty(planArtifacts)) {
            return new ArrayList<>();
        }
        List<PlanArtifactDTO> result = new ArrayList<>();
        PlanArtifactDTO dto;
        for (PlanArtifact planArtifact : planArtifacts) {
            dto = new PlanArtifactDTO();
            BeanUtils.copyProperties(planArtifact, dto);
            result.add(dto);
        }
        return result;
    }

}
