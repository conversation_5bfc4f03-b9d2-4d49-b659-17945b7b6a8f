package com.uino.bean.event;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

/**
 * 告警事件发送kafka DTO
 */
@Data
public class Event2KafkaDTO {

    private int Status;
    private String SourceAlertKey;
    private String SourceSeverity;
    private int SourceID;
    private String SourceCIName;
    private String SourceIdentifier;
    private String Summary;
    private int Severity;
    private String SourceEventID;
    private String LastOccurrence;
    private String timestamp;
    private CIObjectDTO CIObject;

    @Data
    public static class CIObjectDTO {
        private Long classId;
        private String ciCode;
        private Long hashCode;
        private String ciPrimaryKey;
        private String className;
        private Long id;
        private Long time;
        private JSONObject object;
    }
}
