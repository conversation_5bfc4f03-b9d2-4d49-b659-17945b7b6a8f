package com.uino.monitor.event.service;

import com.binary.jdbc.Page;
import com.uino.bean.event.EventCiKpiCount;
import com.uino.bean.event.modeling.EventModel;
import com.uino.bean.monitor.base.ESAlarm;
import com.uino.bean.monitor.base.ESMonEapEvent;
import com.uino.bean.monitor.base.ESMonSysSeverityInfo;
import com.uino.bean.monitor.buiness.CurrentEventQueryDto;
import com.uino.bean.monitor.buiness.EventQueryDto;
import org.elasticsearch.index.query.QueryBuilder;

import java.util.List;
import java.util.Set;

/**
 * 告警事件接口
 */
public interface IEventService {

    String SERVICE_NAME = "eventService";

    EventModel getEventModel();

    /**
     * 分页查询告警信息
     * @param queryDto
     * @return
     */
    Page<ESMonEapEvent> searchEventPage(EventQueryDto queryDto);

    /**
     * 分页查询告警信息，提供自定义builder
     * @param queryDto
     * @param mustQuery
     * @param shouldQuery
     * @return
     */
    Page<ESMonEapEvent> searchEventPage(EventQueryDto queryDto, QueryBuilder mustQuery, QueryBuilder shouldQuery);

    /**
     * 保存告警-模拟告警
     * @param saveBean
     */
    void saveAlarm( ESAlarm saveBean);

    /**
     * 批量保存告警-模拟告警
     * @param saveBeans
     */
    void saveAlarmBatch(List<ESAlarm> saveBeans);

    /**
     * 保存当前未关闭告警信息
     * @param fmEvent
     */
    void saveCurrentEvent(ESMonEapEvent fmEvent);

    /**
     * 查询当前未关闭告警信息
     * @param queryDto
     * @return
     */
    List<ESMonEapEvent> listCurrentEventByParam(CurrentEventQueryDto queryDto);

    /**
     * EP查询当前未关闭告警信息(条件数组之间关系为或)
     * @param queryDtos
     * @return
     */
    List<ESMonEapEvent> listCurrentEventByParam(List<CurrentEventQueryDto> queryDtos);

    /**
     * 删除当前告警信息
     * @param fmEvent
     */
    void delCurrentEvent(ESMonEapEvent fmEvent);

    /**
     * 根据id查询列表
     * @param ids
     * @return
     */
    List<ESMonEapEvent> listCurrentEventByIds(Set<String> ids);

    /**
     * 根据id查询当前告警信息
     * @param id
     * @return
     */
    ESMonEapEvent currentEventById(String id);

    /**
     * 清除当前告警信息
     */
    void clearCurrentEvent(Long domainId);

    /**
     * 保存历史告警信息
     * @param esMonEapEvent
     * @return
     */
    ESMonEapEvent saveEventHis(ESMonEapEvent esMonEapEvent);

    /**
     * 根据id查询历史告警信息
     * @param id
     * @return
     */
    ESMonEapEvent hisEventById(String id);

    /**
     * 获取ciCode集合下的告警信息
     * @param ciCodes
     * @return
     */
    List<ESMonEapEvent> listCurrentEventByCiCodes(Long domainId, List<String> ciCodes);

    /**
     * 获取ciId集合下的告警信息
     * @param ciIds
     * @return
     */
    List<ESMonEapEvent> listCurrentEventByCiIds(List<Long> ciIds);

    /**
     * 查询告警级别列表
     * @return
     */
    List<ESMonSysSeverityInfo> getAllEventSeverity(Long domainId);

    /**
     * 告警级别
     * @param ciCodes
     * @param kpiCodes
     * @return ciCode及对应告警的最严重级别、数量、颜色的信息
     */
    List<EventCiKpiCount> getEventCountByCiCodeAndKpiCodes(Long domainId, Set<String> ciCodes, Set<String> kpiCodes);

    /**
     * 批量保存告警信息
     * @param events
     */
    boolean saveEventBatch(List<ESMonEapEvent> events);


    /**
     * 根据ciCode及告警等级查询当前告警信息（备注：告警状态参数只针对ES中event索引查询，redis查询不生效）
     * @param ciCodes ciCode集合
     * @param severitys 告警等级集合
     * @param status 告警状态
     * @return
     */
    List<ESMonEapEvent> queryCurrentEventsByCiCodesAndServeritys(Long domainId, Set<String> ciCodes, List<Integer> severitys, List<Integer> status);

}
