package com.uinnova.product.vmdb.comm.license;

import java.io.Serializable;

/**
 * 
 * <AUTHOR>
 *
 */
public class IpAddress implements Serializable {
    private static final long serialVersionUID = 1L;

    /** ip地址 **/
    private String ip;

    /** mac地址 **/
    private String mac;

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getMac() {
        return mac;
    }

    public void setMac(String mac) {
        this.mac = mac;
    }

}
