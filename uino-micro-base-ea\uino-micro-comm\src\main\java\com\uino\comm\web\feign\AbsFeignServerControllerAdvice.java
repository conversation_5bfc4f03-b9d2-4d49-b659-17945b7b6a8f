package com.uino.comm.web.feign;

import jakarta.servlet.http.HttpServletResponse;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import com.binary.framework.web.ErrorCode;
import com.binary.framework.web.RemoteResult;

/**
 * <li>集成基本异常处理
 * 
 * <AUTHOR>
 * @since 2020年7月1日上午11:16:20
 *
 */
public abstract class AbsFeignServerControllerAdvice {

    @ResponseBody
    @ExceptionHandler(value = Exception.class)
    public RemoteResult errorHandler(HttpServletResponse response, Exception ex) {
        RemoteResult result = null;
        result = new RemoteResult(false, ErrorCode.SERVER_ERROR.getCode(), ex.getMessage());
        response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
        return result;
    }
}
