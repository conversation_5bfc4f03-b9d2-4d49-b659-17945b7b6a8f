package com.uinnova.product.eam.service.impl;

import java.util.List;

import com.uinnova.product.eam.service.VcBaseConfigSvc;
import org.springframework.beans.factory.annotation.Autowired;

import com.binary.core.util.BinaryUtils;
import com.binary.framework.Local;
import com.binary.jdbc.Page;
import com.uinnova.product.eam.comm.model.CVcBaseConfig;
import com.uinnova.product.eam.comm.model.VcBaseConfig;
import com.uinnova.product.eam.db.VcBaseConfigDao;
import com.uinnova.product.vmdb.comm.i18n.MessageUtil;

@Deprecated
public class VcBaseConfigSvcImpl implements VcBaseConfigSvc {

	@Autowired
	private VcBaseConfigDao configDao;
	
	@Override
	public List<VcBaseConfig> queryBaseConfigList(Long domainId, CVcBaseConfig cdt,String orders) {
		MessageUtil.checkEmpty(domainId, "domainId");
		if(BinaryUtils.isEmpty(cdt)) cdt = new CVcBaseConfig();
		cdt.setDomainId(domainId);

		return configDao.selectList(cdt, orders);
	}
	
	@Override
	public String queryCfgContentByCode(Long domainId , String cfgCode) {
		MessageUtil.checkEmpty(domainId, "domainId");
		MessageUtil.checkEmpty(cfgCode, "cfgCode");
		CVcBaseConfig cdt = new CVcBaseConfig();
		cdt.setCfgCode(cfgCode);
		cdt.setDomainId(domainId);
		List<VcBaseConfig> list = configDao.selectList(cdt, null);
		if(BinaryUtils.isEmpty(list)) return null;
		return list.get(0).getCfgContent();
	}

	@Override
	public Page<VcBaseConfig> queryBaseConfigPage(Long domainId, Integer pageNum, Integer pageSize, CVcBaseConfig cdt, String orders) {
		MessageUtil.checkEmpty(domainId, "domainId");
		MessageUtil.checkEmpty(pageNum,"pageNum");
		MessageUtil.checkEmpty(pageSize,"pageSize");
		if(BinaryUtils.isEmpty(cdt)) cdt = new CVcBaseConfig();
		cdt.setDomainId(domainId);
		return configDao.selectPage(pageNum, pageSize, cdt, orders);
	}
	
	@Override
	public VcBaseConfig queryConfigById(Long id) {
		MessageUtil.checkEmpty(id, "id");

		return configDao.selectById(id);
	}


	@Override
	public Long saveOrUpdateBaseConfig(Long domainId, VcBaseConfig record) {
		MessageUtil.checkEmpty(domainId, "domainId");
		MessageUtil.checkEmpty(record, "record");
		MessageUtil.checkEmpty(record.getCfgCode(), "cfgCode");
		record.setDomainId(domainId);
	
		long id = configDao.save(record);
		Local.commit();
		return id;
	}
	
	@Override
	public Integer saveOrUpdateBaseConfigBatch(Long domainId, List<VcBaseConfig> records){
		MessageUtil.checkEmpty(domainId, "domainId");
		MessageUtil.checkEmpty(records, "records");
		
		for(VcBaseConfig record : records){
			MessageUtil.checkEmpty(record.getCfgCode(), "cfgCode");
			record.setDomainId(domainId);
		}
		configDao.saveBatch(records);
		Local.commit();
		return 1;
	}

	@Override
	public Integer removeBaseConfigByCdt(Long domainId, CVcBaseConfig cdt) {
		MessageUtil.checkEmpty(domainId, "domainId");
		if(BinaryUtils.isEmpty(cdt)) cdt = new CVcBaseConfig();
		cdt.setDomainId(domainId);
		int res = 0;
		try {
			res = configDao.deleteByCdt(cdt);
		} catch (Exception e) {
			e.printStackTrace();
		}
		Local.commit();
		return res;
	}

	@Override
	public Integer removeBaseConfigById(Long id) {
		MessageUtil.checkEmpty(id, "id");
		int result = configDao.deleteById(id);
		Local.commit();
		return result;
	}

}
