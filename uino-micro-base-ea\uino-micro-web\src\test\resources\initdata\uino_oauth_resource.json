[{"name": "tarsier-comm", "permitAllUrls": ["/doc.html", "/license/auth/**", "/redirectAuth", "/getTokenByCode", "/refreshToken", "/cmdb/dataSet/execute", "/cmdb/dataSet/realTimeExecute", "/cmdb/graphAnaly/queryFriendByCiUsingRule", "/permission/oauth/**", "/plugin/syncPlugin", "/currentServer/plugin/uploadPlugin", "/currentServer/plugin/loadPlugin", "/currentServer/plugin/unloadAndDeletePlugin", "/rsm/**"], "urlSecureConfigs": [{"urls": ["/permission/user/getAuth", "/permission/user/getMenuTree", "/permission/user/getAuth"]}, {"urls": ["/**"], "scope": "tarsier"}]}]