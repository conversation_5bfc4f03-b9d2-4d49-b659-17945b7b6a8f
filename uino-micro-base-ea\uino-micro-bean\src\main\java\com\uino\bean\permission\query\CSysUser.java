package com.uino.bean.permission.query;




import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * condition-table: 用户表[SYS_USER]
 * 
 * <AUTHOR>
 */
@ApiModel(value="用户表",description = "用户表")
public class CSysUser implements Serializable {
	private static final long serialVersionUID = 1L;


	/**
	 * condition-field: ID[ID] operate-Equal[=]
	 */
	@ApiModelProperty(value="id",example = "123")
	private Long id;


	/**
	 * condition-field: ID[ID] operate-In[in]
	 */
	@ApiModelProperty(value="批量id数组")
	private Long[] ids;


	/**
	 * condition-field: ID[ID] operate-GTEqual[>=]
	 */
	@ApiModelProperty(value="批量查询起始id（operate-GTEqual[>=]）")
	private Long startId;

	/**
	 * condition-field: ID[ID] operate-LTEqual[<=]
	 */
	@ApiModelProperty(value="批量查询结束id(operate-LTEqual[<=])")
	private Long endId;


	/**
	 * condition-field: 登录代码[LOGIN_CODE] operate-Like[like]
	 */
	@ApiModelProperty(value="登录代码")
	private String loginCode;


	/**
	 * condition-field: 登录代码[LOGIN_CODE] operate-Equal[=]
	 */
	@ApiModelProperty(value="登录代码Equal全等于判断")
	private String loginCodeEqual;


	/**
	 * condition-field: 登录代码[LOGIN_CODE] operate-In[in]
	 */
	@ApiModelProperty(value="登录代码数组")
	private String[] loginCodes;


	/**
	 * condition-field: 登录密码[LOGIN_PASSWD] operate-Like[like]
	 */
	@ApiModelProperty(value="登录密码")
	private String loginPasswd;

	/**
	 * condition-field: 登录密码[LOGIN_PASSWD] operate-Like[like]
	 */
	@ApiModelProperty(value = "登录密码")
	private String loginPasswdEqual;


	/**
	 * condition-field: 显示名[USER_NAME] operate-Like[like]
	 * 操作员姓名
	 */
	@ApiModelProperty(value="操作员姓名")
	private String userName;


	/**
	 * condition-field: 用户头像[ICON] operate-Like[like]
	 * 备用_1
	 */
	@ApiModelProperty(value="用户头像")
	private String icon;


	/**
	 * condition-field: 手机号[MOBILE_NO] operate-Like[like]
	 */
	@ApiModelProperty(value="手机号",example = "12345678")
	private String mobileNo;


	/**
	 * condition-field: 手机号[MOBILE_NO] operate-Equal[=]
	 */
	@ApiModelProperty(value="手机号Equal全等于判断")
	private String mobileNoEqual;


	/**
	 * condition-field: 手机号[MOBILE_NO] operate-In[in]
	 */
	@ApiModelProperty(value="手机号集合")
	private String[] mobileNos;


	/**
	 * condition-field: 电子邮件地址[EMAIL_ADRESS] operate-Like[like]
	 */
	@ApiModelProperty(value="电子邮件地址")
	private String emailAdress;


	/**
	 * condition-field: 电子邮件地址[EMAIL_ADRESS] operate-Equal[=]
	 */
	@ApiModelProperty(value="电子邮件地址Equal全等于判断")
	private String emailAdressEqual;


	/**
	 * condition-field: 电子邮件地址[EMAIL_ADRESS] operate-In[in]
	 */
	@ApiModelProperty(value="电子邮件地址集合")
	private String[] emailAdresss;


	/**
	 * condition-field: 备注[NOTES] operate-Like[like]
	 */
	@ApiModelProperty(value="备注")
	private String notes;


	/**
	 * condition-field: 是否允许修改密码[ALLOW_CHANGE_PASSWD] operate-Equal[=]
	 */
	@ApiModelProperty(value="是否允许修改密码")
	private Integer allowChangePasswd;


	/**
	 * condition-field: 是否允许修改密码[ALLOW_CHANGE_PASSWD] operate-In[in]
	 */
	@ApiModelProperty(value="是否允许修改密码数组")
	private Integer[] allowChangePasswds;


	/**
	 * condition-field: 是否允许修改密码[ALLOW_CHANGE_PASSWD] operate-GTEqual[>=]
	 */
	@ApiModelProperty(value="是否允许修改密码,operate-GTEqual[>=]")
	private Integer startAllowChangePasswd;

	/**
	 * condition-field: 是否允许修改密码[ALLOW_CHANGE_PASSWD] operate-LTEqual[<=]
	 */
	@ApiModelProperty(value="是否允许修改密码,operate-GTEqual[<=]")
	private Integer endAllowChangePasswd;


	/**
	 * condition-field: 最后一次登录日志[LAST_LOGIN_LOG_ID] operate-Equal[=]
	 */
	@ApiModelProperty(value="最后一次登录日志")
	private Long lastLoginLogId;


	/**
	 * condition-field: 最后一次登录日志[LAST_LOGIN_LOG_ID] operate-In[in]
	 */
	@ApiModelProperty(value="最后一次登录日志数组")
	private Long[] lastLoginLogIds;


	/**
	 * condition-field: 最后一次登录日志[LAST_LOGIN_LOG_ID] operate-GTEqual[>=]
	 */
	@ApiModelProperty(value="最后一次登录日志,operate-GTEqual[>=]")
	private Long startLastLoginLogId;

	/**
	 * condition-field: 最后一次登录日志[LAST_LOGIN_LOG_ID] operate-LTEqual[<=]
	 */
	@ApiModelProperty(value="最后一次登录日志,operate-GTEqual[<=]")
	private Long endLastLoginLogId;


	/**
	 * condition-field: 最后一次登录时间[LAST_LOGIN_TIME] operate-Equal[=]
	 */
	@ApiModelProperty(value="最后一次登录时间")
	private Long lastLoginTime;


	/**
	 * condition-field: 最后一次登录时间[LAST_LOGIN_TIME] operate-In[in]
	 */
	@ApiModelProperty(value="最后一次登录时间数组")
	private Long[] lastLoginTimes;


	/**
	 * condition-field: 最后一次登录时间[LAST_LOGIN_TIME] operate-GTEqual[>=]
	 */
	@ApiModelProperty(value="最后一次登录时间(operate-GTEqual[>=])")
	private Long startLastLoginTime;

	/**
	 * condition-field: 最后一次登录时间[LAST_LOGIN_TIME] operate-LTEqual[<=]
	 */
	@ApiModelProperty(value="最后一次登录时间(operate-GTEqual[<=])")
	private Long endLastLoginTime;


	/**
	 * condition-field: 登录尝试次数[TRY_TIMES] operate-Equal[=]
	 */
	@ApiModelProperty(value="登录尝试次数")
	private Integer tryTimes;


	/**
	 * condition-field: 登录尝试次数[TRY_TIMES] operate-In[in]
	 */
	@ApiModelProperty(value="登录尝试次数数组")
	private Integer[] tryTimess;


	/**
	 * condition-field: 登录尝试次数[TRY_TIMES] operate-GTEqual[>=]
	 */
	@ApiModelProperty(value="登录尝试次数(operate-GTEqual[>=])")
	private Integer startTryTimes;

	/**
	 * condition-field: 登录尝试次数[TRY_TIMES] operate-LTEqual[<=]
	 */
	@ApiModelProperty(value="登录尝试次数(operate-GTEqual[<=])")
	private Integer endTryTimes;


	/**
	 * condition-field: 登录标志[LOGIN_FLAG] operate-Equal[=]
	 * 登录标志:0-未登录 1-已登录
	 */
	@ApiModelProperty(value="登录标志，0-未登录 1-已登录")
	private Integer loginFlag;


	/**
	 * condition-field: 登录标志[LOGIN_FLAG] operate-In[in]
	 * 登录标志:0-未登录 1-已登录
	 */
	@ApiModelProperty(value="登录标志数组,0-未登录 1-已登录")
	private Integer[] loginFlags;


	/**
	 * condition-field: 登录标志[LOGIN_FLAG] operate-GTEqual[>=]
	 * 登录标志:0-未登录 1-已登录
	 */
	@ApiModelProperty(value="登录标志，0-未登录 1-已登录,operate-GTEqual[>=]")
	private Integer startLoginFlag;

	/**
	 * condition-field: 登录标志[LOGIN_FLAG] operate-LTEqual[<=]
	 * 登录标志:0-未登录 1-已登录
	 */
	@ApiModelProperty(value="登录标志，0-未登录 1-已登录,operate-GTEqual[<=]")
	private Integer endLoginFlag;


	/**
	 * condition-field: 超级用户标志[SUPER_USER_FLAG] operate-Equal[=]
	 * 超级用户标志:0-否 1-是
	 */
	@ApiModelProperty(value="超级用户标志,0-否 1-是")
	private Integer superUserFlag;


	/**
	 * condition-field: 超级用户标志[SUPER_USER_FLAG] operate-In[in]
	 * 超级用户标志:0-否 1-是
	 */
	@ApiModelProperty(value="超级用户标志数组")
	private Integer[] superUserFlags;


	/**
	 * condition-field: 超级用户标志[SUPER_USER_FLAG] operate-GTEqual[>=]
	 * 超级用户标志:0-否 1-是
	 */
	@ApiModelProperty(value="超级用户标志operate-GTEqual[>=]")
	private Integer startSuperUserFlag;

	/**
	 * condition-field: 超级用户标志[SUPER_USER_FLAG] operate-LTEqual[<=]
	 * 超级用户标志:0-否 1-是
	 */
	@ApiModelProperty(value="超级用户标志operate-GTEqual[<=]")
	private Integer endSuperUserFlag;


	/**
	 * condition-field: 密码有效期[PASSWD_VALID_DAYS] operate-Equal[=]
	 * 密码有效期:单位：天
	 */
	@ApiModelProperty(value="密码有效期,单位：天")
	private Long passwdValidDays;


	/**
	 * condition-field: 密码有效期[PASSWD_VALID_DAYS] operate-In[in]
	 * 密码有效期:单位：天
	 */
	@ApiModelProperty(value="密码有效期数组")
	private Long[] passwdValidDayss;


	/**
	 * condition-field: 密码有效期[PASSWD_VALID_DAYS] operate-GTEqual[>=]
	 * 密码有效期:单位：天
	 */
	@ApiModelProperty(value="密码有效期,operate-LTEqual[>=]")
	private Long startPasswdValidDays;

	/**
	 * condition-field: 密码有效期[PASSWD_VALID_DAYS] operate-LTEqual[<=]
	 * 密码有效期:单位：天
	 */
	@ApiModelProperty(value="密码有效期,operate-LTEqual[<=]")
	private Long endPasswdValidDays;


	/**
	 * condition-field: 开始锁定的时间[LOCKED_TIME] operate-Equal[=]
	 * 用户被锁定的时间
	 */
	@ApiModelProperty(value="用户被锁定的时间")
	private Long lockedTime;


	/**
	 * condition-field: 开始锁定的时间[LOCKED_TIME] operate-In[in]
	 * 用户被锁定的时间
	 */
	@ApiModelProperty(value="用户被锁定的时间数组")
	private Long[] lockedTimes;


	/**
	 * condition-field: 开始锁定的时间[LOCKED_TIME] operate-GTEqual[>=]
	 * 用户被锁定的时间
	 */
	@ApiModelProperty(value="用户被锁定的时间,operate-LTEqual[>=]")
	private Long startLockedTime;

	/**
	 * condition-field: 开始锁定的时间[LOCKED_TIME] operate-LTEqual[<=]
	 * 用户被锁定的时间
	 */
	@ApiModelProperty(value="用户被锁定的时间,operate-LTEqual[<=]")
	private Long endLockedTime;


	/**
	 * condition-field: 锁定标志[LOCK_FLAG] operate-Equal[=]
	 * 锁定标志:0-未锁定 1-锁定
	 */
	@ApiModelProperty(value="锁定标志,0-未锁定 1-锁定",example = "1")
	private Integer lockFlag;


	/**
	 * condition-field: 锁定标志[LOCK_FLAG] operate-In[in]
	 * 锁定标志:0-未锁定 1-锁定
	 */
	@ApiModelProperty(value="锁定标志数组,0-未锁定 1-锁定")
	private Integer[] lockFlags;


	/**
	 * condition-field: 锁定标志[LOCK_FLAG] operate-GTEqual[>=]
	 * 锁定标志:0-未锁定 1-锁定
	 */
	@ApiModelProperty(value="锁定标志,0-未锁定 1-锁定,operate-LTEqual[>=]")
	private Integer startLockFlag;

	/**
	 * condition-field: 锁定标志[LOCK_FLAG] operate-LTEqual[<=]
	 * 锁定标志:0-未锁定 1-锁定
	 */
	@ApiModelProperty(value="锁定标志,0-未锁定 1-锁定,operate-LTEqual[<=]")
	private Integer endLockFlag;


	/**
	 * condition-field: 是否需要修改密码[IS_UPDATE_PWD] operate-Equal[=]
	 * 是否需要修改密码:1=是 0=否
	 */
	@ApiModelProperty(value="是否需要修改密码,1=是 0=否")
	private Integer isUpdatePwd;


	/**
	 * condition-field: 是否需要修改密码[IS_UPDATE_PWD] operate-In[in]
	 * 是否需要修改密码:1=是 0=否
	 */
	@ApiModelProperty(value="是否需要修改密码,1=是 0=否")
	private Integer[] isUpdatePwds;


	/**
	 * condition-field: 是否需要修改密码[IS_UPDATE_PWD] operate-GTEqual[>=]
	 * 是否需要修改密码:1=是 0=否
	 */
	@ApiModelProperty(value="是否需要修改密码,1=是 0=否,operate-LTEqual[>=]")
	private Integer startIsUpdatePwd;

	/**
	 * condition-field: 是否需要修改密码[IS_UPDATE_PWD] operate-LTEqual[<=]
	 * 是否需要修改密码:1=是 0=否
	 */
	@ApiModelProperty(value="是否需要修改密码,1=是 0=否,operate-LTEqual[<=]")
	private Integer endIsUpdatePwd;


	/**
	 * condition-field: 登录认证代码[LOGIN_AUTH_CODE] operate-Like[like]
	 */
	@ApiModelProperty(value="登录认证代码")
	private String loginAuthCode;


	/**
	 * condition-field: 登录认证代码[LOGIN_AUTH_CODE] operate-Equal[=]
	 */
	@ApiModelProperty(value="登录认证代码Equal全等于判断")
	private String loginAuthCodeEqual;


	/**
	 * condition-field: 登录认证代码[LOGIN_AUTH_CODE] operate-In[in]
	 */
	@ApiModelProperty(value="登录认证代码数组")
	private String[] loginAuthCodes;


	/**
	 * condition-field: 用户状态[STATUS] operate-Equal[=]
	 * 状态:1-正常 0-停用
	 */
	@ApiModelProperty(value="用户状态,1-正常 0-停用")
	private Integer status;


	/**
	 * condition-field: 用户状态[STATUS] operate-In[in]
	 * 状态:1-正常 0-停用
	 */
	@ApiModelProperty(value="用户状态数组,1-正常 0-停用")
	private Integer[] statuss;


	/**
	 * condition-field: 用户状态[STATUS] operate-GTEqual[>=]
	 * 状态:1-正常 0-停用
	 */
	@ApiModelProperty(value="用户状态1-正常 0-停用，operate-LTEqual[>=]")
	private Integer startStatus;

	/**
	 * condition-field: 用户状态[STATUS] operate-LTEqual[<=]
	 * 状态:1-正常 0-停用
	 */
	@ApiModelProperty(value="用户状态1-正常 0-停用，operate-LTEqual[<=]")
	private Integer endStatus;


	/**
	 * condition-field: 所属域[DOMAIN_ID] operate-Equal[=]
	 */
	@ApiModelProperty(value="所属域id")
	private Long domainId;


	/**
	 * condition-field: 所属域[DOMAIN_ID] operate-In[in]
	 */
	@ApiModelProperty(value="所属域id数组")
	private Long[] domainIds;


	/**
	 * condition-field: 所属域[DOMAIN_ID] operate-GTEqual[>=]
	 */
	@ApiModelProperty(value="所属域operate-LTEqual[>=]")
	private Long startDomainId;

	/**
	 * condition-field: 所属域[DOMAIN_ID] operate-LTEqual[<=]
	 */
	@ApiModelProperty(value="所属域operate-LTEqual[<=]")
	private Long endDomainId;


	/**
	 * condition-field: 创建人[CREATOR] operate-Like[like]
	 */
	@ApiModelProperty(value="创建人")
	private String creator;


	/**
	 * condition-field: 创建人[CREATOR] operate-Equal[=]
	 */
	@ApiModelProperty(value="创建人的Equal全等于判断")
	private String creatorEqual;


	/**
	 * condition-field: 创建人[CREATOR] operate-In[in]
	 */
	@ApiModelProperty(value="创建人数组")
	private String[] creators;


	/**
	 * condition-field: 修改人[MODIFIER] operate-Like[like]
	 */
	@ApiModelProperty(value="修改人",example = "mike")
	private String modifier;


	/**
	 * condition-field: 修改人[MODIFIER] operate-Equal[=]
	 */
	@ApiModelProperty(value="修改人Equal全等于判断")
	private String modifierEqual;


	/**
	 * condition-field: 修改人[MODIFIER] operate-In[in]
	 */
	@ApiModelProperty(value="修改人数组")
	private String[] modifiers;


	/**
	 * condition-field: 创建时间[CREATE_TIME] operate-Equal[=]
	 * 创建时间:yyyyMMddHHmmss
	 */
	@ApiModelProperty(value="创建时间")
	private Long createTime;


	/**
	 * condition-field: 创建时间[CREATE_TIME] operate-In[in]
	 * 创建时间:yyyyMMddHHmmss
	 */
	@ApiModelProperty(value="创建时间数组")
	private Long[] createTimes;


	/**
	 * condition-field: 创建时间[CREATE_TIME] operate-GTEqual[>=]
	 * 创建时间:yyyyMMddHHmmss
	 */
	@ApiModelProperty(value="创建时间operate-GTEqual[>=]")
	private Long startCreateTime;

	/**
	 * condition-field: 创建时间[CREATE_TIME] operate-LTEqual[<=]
	 * 创建时间:yyyyMMddHHmmss
	 */
	@ApiModelProperty(value="创建时间operate-GTEqual[<=]")
	private Long endCreateTime;


	/**
	 * condition-field: 修改时间[MODIFY_TIME] operate-Equal[=]
	 * 修改时间:yyyyMMddHHmmss
	 */
	@ApiModelProperty(value="修改时间")
	private Long modifyTime;


	/**
	 * condition-field: 修改时间[MODIFY_TIME] operate-In[in]
	 * 修改时间:yyyyMMddHHmmss
	 */
	@ApiModelProperty(value="修改时间数组")
	private Long[] modifyTimes;


	/**
	 * condition-field: 修改时间[MODIFY_TIME] operate-GTEqual[>=]
	 * 修改时间:yyyyMMddHHmmss
	 */
	@ApiModelProperty(value="修改时间operate-LTEqual[>=]")
	private Long startModifyTime;

	/**
	 * condition-field: 修改时间[MODIFY_TIME] operate-LTEqual[<=]
	 * 修改时间:yyyyMMddHHmmss
	 */
	@ApiModelProperty(value="修改时间operate-LTEqual[<=]")
	private Long endModifyTime;




	public Long getId() {
		return this.id;
	}
	public void setId(Long id) {
		this.id = id;
	}


	public Long[] getIds() {
		return this.ids;
	}
	public void setIds(Long[] ids) {
		this.ids = ids;
	}


	public Long getStartId() {
		return this.startId;
	}
	public void setStartId(Long startId) {
		this.startId = startId;
	}


	public Long getEndId() {
		return this.endId;
	}
	public void setEndId(Long endId) {
		this.endId = endId;
	}


	public String getLoginCode() {
		return this.loginCode;
	}
	public void setLoginCode(String loginCode) {
		this.loginCode = loginCode;
	}


	public String getLoginCodeEqual() {
		return this.loginCodeEqual;
	}
	public void setLoginCodeEqual(String loginCodeEqual) {
		this.loginCodeEqual = loginCodeEqual;
	}


	public String[] getLoginCodes() {
		return this.loginCodes;
	}
	public void setLoginCodes(String[] loginCodes) {
		this.loginCodes = loginCodes;
	}


	public String getLoginPasswd() {
		return this.loginPasswd;
	}
	public void setLoginPasswd(String loginPasswd) {
		this.loginPasswd = loginPasswd;
	}

	public String getLoginPasswdEqual() {
		return loginPasswdEqual;
	}

	public void setLoginPasswdEqual(String loginPasswdEqual) {
		this.loginPasswdEqual = loginPasswdEqual;
	}
	public String getUserName() {
		return this.userName;
	}
	public void setUserName(String userName) {
		this.userName = userName;
	}


	public String getIcon() {
		return this.icon;
	}
	public void setIcon(String icon) {
		this.icon = icon;
	}


	public String getMobileNo() {
		return this.mobileNo;
	}
	public void setMobileNo(String mobileNo) {
		this.mobileNo = mobileNo;
	}


	public String getMobileNoEqual() {
		return this.mobileNoEqual;
	}
	public void setMobileNoEqual(String mobileNoEqual) {
		this.mobileNoEqual = mobileNoEqual;
	}


	public String[] getMobileNos() {
		return this.mobileNos;
	}
	public void setMobileNos(String[] mobileNos) {
		this.mobileNos = mobileNos;
	}


	public String getEmailAdress() {
		return this.emailAdress;
	}
	public void setEmailAdress(String emailAdress) {
		this.emailAdress = emailAdress;
	}


	public String getEmailAdressEqual() {
		return this.emailAdressEqual;
	}
	public void setEmailAdressEqual(String emailAdressEqual) {
		this.emailAdressEqual = emailAdressEqual;
	}


	public String[] getEmailAdresss() {
		return this.emailAdresss;
	}
	public void setEmailAdresss(String[] emailAdresss) {
		this.emailAdresss = emailAdresss;
	}


	public String getNotes() {
		return this.notes;
	}
	public void setNotes(String notes) {
		this.notes = notes;
	}


	public Integer getAllowChangePasswd() {
		return this.allowChangePasswd;
	}
	public void setAllowChangePasswd(Integer allowChangePasswd) {
		this.allowChangePasswd = allowChangePasswd;
	}


	public Integer[] getAllowChangePasswds() {
		return this.allowChangePasswds;
	}
	public void setAllowChangePasswds(Integer[] allowChangePasswds) {
		this.allowChangePasswds = allowChangePasswds;
	}


	public Integer getStartAllowChangePasswd() {
		return this.startAllowChangePasswd;
	}
	public void setStartAllowChangePasswd(Integer startAllowChangePasswd) {
		this.startAllowChangePasswd = startAllowChangePasswd;
	}


	public Integer getEndAllowChangePasswd() {
		return this.endAllowChangePasswd;
	}
	public void setEndAllowChangePasswd(Integer endAllowChangePasswd) {
		this.endAllowChangePasswd = endAllowChangePasswd;
	}


	public Long getLastLoginLogId() {
		return this.lastLoginLogId;
	}
	public void setLastLoginLogId(Long lastLoginLogId) {
		this.lastLoginLogId = lastLoginLogId;
	}


	public Long[] getLastLoginLogIds() {
		return this.lastLoginLogIds;
	}
	public void setLastLoginLogIds(Long[] lastLoginLogIds) {
		this.lastLoginLogIds = lastLoginLogIds;
	}


	public Long getStartLastLoginLogId() {
		return this.startLastLoginLogId;
	}
	public void setStartLastLoginLogId(Long startLastLoginLogId) {
		this.startLastLoginLogId = startLastLoginLogId;
	}


	public Long getEndLastLoginLogId() {
		return this.endLastLoginLogId;
	}
	public void setEndLastLoginLogId(Long endLastLoginLogId) {
		this.endLastLoginLogId = endLastLoginLogId;
	}


	public Long getLastLoginTime() {
		return this.lastLoginTime;
	}
	public void setLastLoginTime(Long lastLoginTime) {
		this.lastLoginTime = lastLoginTime;
	}


	public Long[] getLastLoginTimes() {
		return this.lastLoginTimes;
	}
	public void setLastLoginTimes(Long[] lastLoginTimes) {
		this.lastLoginTimes = lastLoginTimes;
	}


	public Long getStartLastLoginTime() {
		return this.startLastLoginTime;
	}
	public void setStartLastLoginTime(Long startLastLoginTime) {
		this.startLastLoginTime = startLastLoginTime;
	}


	public Long getEndLastLoginTime() {
		return this.endLastLoginTime;
	}
	public void setEndLastLoginTime(Long endLastLoginTime) {
		this.endLastLoginTime = endLastLoginTime;
	}


	public Integer getTryTimes() {
		return this.tryTimes;
	}
	public void setTryTimes(Integer tryTimes) {
		this.tryTimes = tryTimes;
	}


	public Integer[] getTryTimess() {
		return this.tryTimess;
	}
	public void setTryTimess(Integer[] tryTimess) {
		this.tryTimess = tryTimess;
	}


	public Integer getStartTryTimes() {
		return this.startTryTimes;
	}
	public void setStartTryTimes(Integer startTryTimes) {
		this.startTryTimes = startTryTimes;
	}


	public Integer getEndTryTimes() {
		return this.endTryTimes;
	}
	public void setEndTryTimes(Integer endTryTimes) {
		this.endTryTimes = endTryTimes;
	}


	public Integer getLoginFlag() {
		return this.loginFlag;
	}
	public void setLoginFlag(Integer loginFlag) {
		this.loginFlag = loginFlag;
	}


	public Integer[] getLoginFlags() {
		return this.loginFlags;
	}
	public void setLoginFlags(Integer[] loginFlags) {
		this.loginFlags = loginFlags;
	}


	public Integer getStartLoginFlag() {
		return this.startLoginFlag;
	}
	public void setStartLoginFlag(Integer startLoginFlag) {
		this.startLoginFlag = startLoginFlag;
	}


	public Integer getEndLoginFlag() {
		return this.endLoginFlag;
	}
	public void setEndLoginFlag(Integer endLoginFlag) {
		this.endLoginFlag = endLoginFlag;
	}


	public Integer getSuperUserFlag() {
		return this.superUserFlag;
	}
	public void setSuperUserFlag(Integer superUserFlag) {
		this.superUserFlag = superUserFlag;
	}


	public Integer[] getSuperUserFlags() {
		return this.superUserFlags;
	}
	public void setSuperUserFlags(Integer[] superUserFlags) {
		this.superUserFlags = superUserFlags;
	}


	public Integer getStartSuperUserFlag() {
		return this.startSuperUserFlag;
	}
	public void setStartSuperUserFlag(Integer startSuperUserFlag) {
		this.startSuperUserFlag = startSuperUserFlag;
	}


	public Integer getEndSuperUserFlag() {
		return this.endSuperUserFlag;
	}
	public void setEndSuperUserFlag(Integer endSuperUserFlag) {
		this.endSuperUserFlag = endSuperUserFlag;
	}


	public Long getPasswdValidDays() {
		return this.passwdValidDays;
	}
	public void setPasswdValidDays(Long passwdValidDays) {
		this.passwdValidDays = passwdValidDays;
	}


	public Long[] getPasswdValidDayss() {
		return this.passwdValidDayss;
	}
	public void setPasswdValidDayss(Long[] passwdValidDayss) {
		this.passwdValidDayss = passwdValidDayss;
	}


	public Long getStartPasswdValidDays() {
		return this.startPasswdValidDays;
	}
	public void setStartPasswdValidDays(Long startPasswdValidDays) {
		this.startPasswdValidDays = startPasswdValidDays;
	}


	public Long getEndPasswdValidDays() {
		return this.endPasswdValidDays;
	}
	public void setEndPasswdValidDays(Long endPasswdValidDays) {
		this.endPasswdValidDays = endPasswdValidDays;
	}


	public Long getLockedTime() {
		return this.lockedTime;
	}
	public void setLockedTime(Long lockedTime) {
		this.lockedTime = lockedTime;
	}


	public Long[] getLockedTimes() {
		return this.lockedTimes;
	}
	public void setLockedTimes(Long[] lockedTimes) {
		this.lockedTimes = lockedTimes;
	}


	public Long getStartLockedTime() {
		return this.startLockedTime;
	}
	public void setStartLockedTime(Long startLockedTime) {
		this.startLockedTime = startLockedTime;
	}


	public Long getEndLockedTime() {
		return this.endLockedTime;
	}
	public void setEndLockedTime(Long endLockedTime) {
		this.endLockedTime = endLockedTime;
	}


	public Integer getLockFlag() {
		return this.lockFlag;
	}
	public void setLockFlag(Integer lockFlag) {
		this.lockFlag = lockFlag;
	}


	public Integer[] getLockFlags() {
		return this.lockFlags;
	}
	public void setLockFlags(Integer[] lockFlags) {
		this.lockFlags = lockFlags;
	}


	public Integer getStartLockFlag() {
		return this.startLockFlag;
	}
	public void setStartLockFlag(Integer startLockFlag) {
		this.startLockFlag = startLockFlag;
	}


	public Integer getEndLockFlag() {
		return this.endLockFlag;
	}
	public void setEndLockFlag(Integer endLockFlag) {
		this.endLockFlag = endLockFlag;
	}


	public Integer getIsUpdatePwd() {
		return this.isUpdatePwd;
	}
	public void setIsUpdatePwd(Integer isUpdatePwd) {
		this.isUpdatePwd = isUpdatePwd;
	}


	public Integer[] getIsUpdatePwds() {
		return this.isUpdatePwds;
	}
	public void setIsUpdatePwds(Integer[] isUpdatePwds) {
		this.isUpdatePwds = isUpdatePwds;
	}


	public Integer getStartIsUpdatePwd() {
		return this.startIsUpdatePwd;
	}
	public void setStartIsUpdatePwd(Integer startIsUpdatePwd) {
		this.startIsUpdatePwd = startIsUpdatePwd;
	}


	public Integer getEndIsUpdatePwd() {
		return this.endIsUpdatePwd;
	}
	public void setEndIsUpdatePwd(Integer endIsUpdatePwd) {
		this.endIsUpdatePwd = endIsUpdatePwd;
	}


	public String getLoginAuthCode() {
		return this.loginAuthCode;
	}
	public void setLoginAuthCode(String loginAuthCode) {
		this.loginAuthCode = loginAuthCode;
	}


	public String getLoginAuthCodeEqual() {
		return this.loginAuthCodeEqual;
	}
	public void setLoginAuthCodeEqual(String loginAuthCodeEqual) {
		this.loginAuthCodeEqual = loginAuthCodeEqual;
	}


	public String[] getLoginAuthCodes() {
		return this.loginAuthCodes;
	}
	public void setLoginAuthCodes(String[] loginAuthCodes) {
		this.loginAuthCodes = loginAuthCodes;
	}


	public Integer getStatus() {
		return this.status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}


	public Integer[] getStatuss() {
		return this.statuss;
	}
	public void setStatuss(Integer[] statuss) {
		this.statuss = statuss;
	}


	public Integer getStartStatus() {
		return this.startStatus;
	}
	public void setStartStatus(Integer startStatus) {
		this.startStatus = startStatus;
	}


	public Integer getEndStatus() {
		return this.endStatus;
	}
	public void setEndStatus(Integer endStatus) {
		this.endStatus = endStatus;
	}


	public Long getDomainId() {
		return this.domainId;
	}
	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}


	public Long[] getDomainIds() {
		return this.domainIds;
	}
	public void setDomainIds(Long[] domainIds) {
		this.domainIds = domainIds;
	}


	public Long getStartDomainId() {
		return this.startDomainId;
	}
	public void setStartDomainId(Long startDomainId) {
		this.startDomainId = startDomainId;
	}


	public Long getEndDomainId() {
		return this.endDomainId;
	}
	public void setEndDomainId(Long endDomainId) {
		this.endDomainId = endDomainId;
	}


	public String getCreator() {
		return this.creator;
	}
	public void setCreator(String creator) {
		this.creator = creator;
	}


	public String getCreatorEqual() {
		return this.creatorEqual;
	}
	public void setCreatorEqual(String creatorEqual) {
		this.creatorEqual = creatorEqual;
	}


	public String[] getCreators() {
		return this.creators;
	}
	public void setCreators(String[] creators) {
		this.creators = creators;
	}


	public String getModifier() {
		return this.modifier;
	}
	public void setModifier(String modifier) {
		this.modifier = modifier;
	}


	public String getModifierEqual() {
		return this.modifierEqual;
	}
	public void setModifierEqual(String modifierEqual) {
		this.modifierEqual = modifierEqual;
	}


	public String[] getModifiers() {
		return this.modifiers;
	}
	public void setModifiers(String[] modifiers) {
		this.modifiers = modifiers;
	}


	public Long getCreateTime() {
		return this.createTime;
	}
	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}


	public Long[] getCreateTimes() {
		return this.createTimes;
	}
	public void setCreateTimes(Long[] createTimes) {
		this.createTimes = createTimes;
	}


	public Long getStartCreateTime() {
		return this.startCreateTime;
	}
	public void setStartCreateTime(Long startCreateTime) {
		this.startCreateTime = startCreateTime;
	}


	public Long getEndCreateTime() {
		return this.endCreateTime;
	}
	public void setEndCreateTime(Long endCreateTime) {
		this.endCreateTime = endCreateTime;
	}


	public Long getModifyTime() {
		return this.modifyTime;
	}
	public void setModifyTime(Long modifyTime) {
		this.modifyTime = modifyTime;
	}


	public Long[] getModifyTimes() {
		return this.modifyTimes;
	}
	public void setModifyTimes(Long[] modifyTimes) {
		this.modifyTimes = modifyTimes;
	}


	public Long getStartModifyTime() {
		return this.startModifyTime;
	}
	public void setStartModifyTime(Long startModifyTime) {
		this.startModifyTime = startModifyTime;
	}


	public Long getEndModifyTime() {
		return this.endModifyTime;
	}
	public void setEndModifyTime(Long endModifyTime) {
		this.endModifyTime = endModifyTime;
	}


}


