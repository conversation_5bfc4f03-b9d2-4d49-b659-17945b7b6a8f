package com.uinnova.product.eam.db.impl;


import com.binary.framework.dao.DaoDefinition;

import com.uinnova.product.eam.comm.model.CVcNodeLinkedPath;
import com.uinnova.product.eam.comm.model.VcNodeLinkedPath;


/**
 * 节点路径详情表[VC_NODE_LINKED_PATH]数据访问对象定义实现
 */
public class VcNodeLinkedPathDaoDefinition implements DaoDefinition<VcNodeLinkedPath, CVcNodeLinkedPath> {


	@Override
	public Class<VcNodeLinkedPath> getEntityClass() {
		return VcNodeLinkedPath.class;
	}


	@Override
	public Class<CVcNodeLinkedPath> getConditionClass() {
		return CVcNodeLinkedPath.class;
	}


	@Override
	public String getTableName() {
		return "VC_NODE_LINKED_PATH";
	}


	@Override
	public boolean hasDataStatusField() {
		return false;
	}


	@Override
	public void setDataStatusValue(VcNodeLinkedPath record, int status) {
	}


	@Override
	public void setDataStatusValue(CVcNodeLinkedPath cdt, int status) {
	}


	@Override
	public void setCreatorValue(VcNodeLinkedPath record, String creator) {
	}


	@Override
	public void setModifierValue(VcNodeLinkedPath record, String modifier) {
	}


}


