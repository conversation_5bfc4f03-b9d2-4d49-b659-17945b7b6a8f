package com.uinnova.product.vmdb.comm.model.es;

import java.util.Map;

/**
 * CI质量数据
 */
public class ESCiQualityData {

    private static final long serialVersionUID = 1L;

    public ESCiQualityData() {
        this.proName = "-";
        this.proValue = "-";
        this.problemReason = "-";
        this.rltClassName = "-";
        this.oppositeClassName = "-";
    }

    private String id;

    private Long ciId;

    /**
     * 问题类型，取自枚举值
     */
    private String problemType;

    private String className;

    /**
     * key={0,1,2,3}
     */
    private Map<String, String> ciPrimaryKey;

    /**
     * 用于显示
     */
    private String primaryKey;

    /**
     * CI属性名
     */
    private String proName;

    /**
     * CI属性值
     */
    private String proValue;

    /**
     * 问题原因
     */
    private String problemReason;

    /**
     * 关系名称
     */
    private String rltClassName;

    /**
     * 对端分类名
     */
    private String oppositeClassName;

    /**
     * 检查时间
     */
    private Long time;

    /**
     * CI数据来源
     */
    private String source;

    private Long domainId;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Long getCiId() {
        return ciId;
    }

    public void setCiId(Long ciId) {
        this.ciId = ciId;
    }

    public String getProblemType() {
        return problemType;
    }

    public void setProblemType(String problemType) {
        this.problemType = problemType;
    }

    public String getClassName() {
        return className;
    }

    public void setClassName(String className) {
        this.className = className;
    }

    public Map<String, String> getCiPrimaryKey() {
        return ciPrimaryKey;
    }

    public void setCiPrimaryKey(Map<String, String> ciPrimaryKey) {
        int index = 0;
        StringBuilder sb = new StringBuilder();
        while (ciPrimaryKey.containsKey(String.valueOf(index))) {
            String pk = ciPrimaryKey.get(String.valueOf(index));
            if (index != 0) {
                sb.append(",");
            }
            sb.append(pk);
            index++;
        }
        this.primaryKey = sb.toString();
        this.ciPrimaryKey = ciPrimaryKey;
    }

    public String getProName() {
        return proName;
    }

    public void setProName(String proName) {
        this.proName = proName;
    }

    public String getProValue() {
        return proValue;
    }

    public void setProValue(String proValue) {
        if (proValue != null && !proValue.equals("")) {
            this.proValue = proValue;
        }
    }

    public String getProblemReason() {
        return problemReason;
    }

    public void setProblemReason(String problemReason) {
        this.problemReason = problemReason;
    }

    public String getRltClassName() {
        return rltClassName;
    }

    public void setRltClassName(String rltClassName) {
        this.rltClassName = rltClassName;
    }

    public String getOppositeClassName() {
        return oppositeClassName;
    }

    public void setOppositeClassName(String oppositeClassName) {
        this.oppositeClassName = oppositeClassName;
    }

    public Long getTime() {
        return time;
    }

    public void setTime(Long time) {
        this.time = time;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public Long getDomainId() {
        return domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }

    public String getPrimaryKey() {
        return primaryKey;
    }

    public void setPrimaryKey(String primaryKey) {
        this.primaryKey = primaryKey;
    }
}
