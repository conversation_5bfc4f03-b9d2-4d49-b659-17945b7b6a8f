package com.uino.service.cmdb.microservice.impl;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import com.uino.dao.BaseConst;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

import com.binary.core.io.Resource;
import com.binary.core.io.support.ByteArrayResource;
import com.binary.core.util.BinaryUtils;
import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClass;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uino.dao.cmdb.ESCIRltSvc;
import com.uino.dao.cmdb.ESCmdbCommSvc;
import com.uino.dao.cmdb.ESRltClassSvc;
import com.uino.service.cmdb.microservice.IRltClassSvc;
import com.uino.dao.util.ESUtil;
import com.uino.service.util.FileUtil;
import com.uino.util.sys.CheckAttrUtil;
import com.uino.util.sys.SysUtil;
import com.uino.util.sys.ValidDtoUtil;
import com.uino.bean.cmdb.base.ESCIClassInfo;
import com.uino.bean.cmdb.base.ESCIRltInfo;
import com.uino.bean.cmdb.business.ClassReOrderDTO;

import lombok.extern.slf4j.Slf4j;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
@Slf4j
public class RltClassSvc implements IRltClassSvc {

    @Autowired
    private ESRltClassSvc esRltClassSvc;

    @Autowired
    private ESCIRltSvc esCiRltSvc;

    @Autowired
    @Lazy
    private ESCmdbCommSvc commSvc;

	@Autowired
	private CIClassRltSvc classRltSvc;

    @Override
    public Long saveOrUpdate(ESCIClassInfo rltClass) {
        if (rltClass.getDomainId() == null) {
            rltClass.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
        }
        validSaveOrUpdate(rltClass.getDomainId(), rltClass);
        // 验证通过，处理属性
        if (rltClass.getCcAttrDefs() != null) {
            for (CcCiAttrDef attrDef : rltClass.getCcAttrDefs()) {
                attrDef.setProStdName(attrDef.getProName() != null ? attrDef.getProName().toUpperCase() : null);
                if (attrDef.getIsRequired() == null) {
                    attrDef.setIsRequired(0);
                }
                if (attrDef.getId() == null) {
                    attrDef.setId(ESUtil.getUUID());
                }
            }
        }
        return esRltClassSvc.saveOrUpdate(rltClass);
    }

    /**
     * 验证持久化关系分类
     *
     * @param rltClass
     */
    private void validSaveOrUpdate(Long domainId, ESCIClassInfo rltClass) {
        ValidDtoUtil.valid(rltClass);
        String classCode = rltClass.getClassCode();
        String classStdCode = classCode.toUpperCase();
        BoolQueryBuilder validQuery = QueryBuilders.boolQuery();
        validQuery.must(QueryBuilders.termQuery("domainId", domainId));
        validQuery.must(QueryBuilders.termQuery("classStdCode.keyword", classStdCode));
        if (rltClass.getId() != null) {
            validQuery.mustNot(QueryBuilders.termQuery("id", rltClass.getId()));
        }
        List<ESCIClassInfo> validDatas = esRltClassSvc.getListByQuery(validQuery);
        Assert.isTrue(validDatas == null || validDatas.size() <= 0, "关系分类已存在");
        // 校验属性重复名称
        if (rltClass.getCcAttrDefs() != null && rltClass.getCcAttrDefs().size() > 0) {
            Set<String> defNames = new HashSet<>();
            rltClass.getCcAttrDefs().forEach(def -> {
                Assert.isTrue(!BinaryUtils.isEmpty(def.getProName().trim()), "属性名称不得为空");
                Assert.isTrue(
                        def.getProName().trim()
                                .matches("[A-Za-z0-9 \\\\\\_\\&\\(\\)\\（\\）\\|\\/\\'\\-\\@\\u4e00-\\u9fa5]+"),
                        "属性[" + def.getProName() + "]包含非法字符，仅支持 _&()（）|/\'-@");
                Assert.isTrue(!defNames.contains(def.getProName().toUpperCase()), "属性名称重复[" + def.getProName() + "]");
                defNames.add(def.getProName().toUpperCase());
            });
        }
        // 若关系分类下已有关系，禁止修改已存在任何属性
        if (rltClass.getId() != null) {
            long hasRltNumber = esCiRltSvc.countByCondition(QueryBuilders.termQuery("classId", rltClass.getId()));
            if (hasRltNumber > 0) {
                ESCIClassInfo oldCls = esRltClassSvc.getById(rltClass.getId());
                // 新属性模板信息
                List<CcCiAttrDef> newDefs = rltClass.getCcAttrDefs();
                newDefs = newDefs == null ? new LinkedList<>() : newDefs;
                Map<Long, CcCiAttrDef> newDefMap = new HashMap<>();
                newDefs.forEach(def -> newDefMap.put(def.getId(), def));
                // 旧的属性模板相关信息
                List<CcCiAttrDef> oldDefs = oldCls.getCcAttrDefs();
                oldDefs = oldDefs == null ? new LinkedList<>() : oldDefs;
                oldDefs.forEach(def -> {
                    Assert.isTrue(newDefMap.get(def.getId()) != null, "关系分类下已有数据不允许删除属性");
                    Assert.isTrue(CheckAttrUtil.equalsAttrDef(newDefMap.get(def.getId()), def), "关系分类下已有数据不允许修改属性");
                });
                // 新属性不可有子code和必填
                List<CcCiAttrDef> validList = newDefs.stream()
                        .filter(def -> def.getId() == null
                                && ((def.getIsMajor() != null && def.getIsMajor() == 1)
                                        || (def.getIsRequired() != null && def.getIsRequired() == 1)))
                        .collect(Collectors.toList());
                Assert.isTrue(validList.size() <= 0, "关系分类下已有数据不允许新增子CODE以及必填属性");
            }
        }
    }

    @Override
    public Integer deleteByIds(Collection<Long> rltClassIds) {
        // 先验证指定关系分类下是否有关系，若有关系阻止删除
        Page<ESCIRltInfo> validCiResult = esCiRltSvc.getListByQuery(1, 1,
                QueryBuilders.termsQuery("classId", rltClassIds));
        Assert.isTrue(validCiResult.getTotalRows() <= 0, "删除的关系分类下有存在关系不得删除，请先清除分类下关系");
		for (Long classId : rltClassIds) {
			classRltSvc.deleteRltsByClassId(classId);
		}
        return esRltClassSvc.deleteByIds(new ArrayList<>(rltClassIds));
    }

    @Override
    public List<CcCiClassInfo> queryAllClasses(Long domainId) {
        List<CcCiClassInfo> rltClss = esRltClassSvc.queryAllClasses(domainId);
        if (rltClss != null && rltClss.size() > 0) {
            // 填补分类下关系数量信息
            Set<Long> clsIds = new HashSet<>();
            rltClss.forEach(cls -> clsIds.add(cls.getCiClass().getId()));
            Map<String, Long> countRes = esCiRltSvc.groupByCountField("classId",
                    QueryBuilders.termsQuery("classId", clsIds));
            rltClss.forEach(cls -> {
                Long count = countRes.get(cls.getCiClass().getId().toString());
                count = count == null ? 0L : count;
                cls.setCiCount(count);
            });
        }
        return rltClss;
    }

    @Override
    public CcCiClassInfo getRltClassById(Long rltClassId) {
        return esRltClassSvc.queryClassById(rltClassId);
    }

    @Override
    public CcCiClassInfo getRltClassByName(Long domainId, String className) {
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("domainId", domainId));
        query.must(QueryBuilders.termQuery("className.keyword", className));
        List<ESCIClassInfo> listByQuery = esRltClassSvc.getListByQuery(query);
        if(BinaryUtils.isEmpty(listByQuery)){
            return null;
        }
        return commSvc.tranCcCiClassInfo(listByQuery.get(0));
    }

    @Override
    public List<CcCiClassInfo> getRltClassByCdt(CCcCiClass cdt) {
        if (cdt == null) {
            cdt = new CCcCiClass();
        }
        if (cdt.getDomainId() == null) {
            cdt.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
        }
        BoolQueryBuilder query = ESUtil.cdtToBuilder(cdt);
        long count = esRltClassSvc.countByCondition(query);
        if (count <= 0) { return new ArrayList<>(); }
        return esRltClassSvc.queryClassByCdt(1, new BigDecimal(count).intValue(), cdt);
    }

    @Override
    public Resource exportAttrDefs(Set<Long> clsIds) {
        Resource resource = null;
        String markName = SysUtil.StaticUtil.RLTCLS_MAJOR_MARK;
        Workbook workBook = null;
        InputStream temIs = null;
        temIs = this.getClass().getResourceAsStream("/static_res/rlt_attr_template.xlsx");
        try {
            workBook = new XSSFWorkbook(temIs);
        } catch (IOException e) {
            Assert.isTrue(false, "读取模板文件异常");
        }
        String exportFileName = null;
        if (clsIds != null && clsIds.size() > 0) {
            exportFileName = FileUtil.ExcelUtil.getExportFileName("关系属性", ".xlsx", true);
            List<ESCIClassInfo> rltClsInfos = esRltClassSvc.getListByQuery(QueryBuilders.termsQuery("id", clsIds));
            Assert.isTrue(rltClsInfos.size() == clsIds.size(), "指定关系分类有失效数据");
            for (ESCIClassInfo rltCls : rltClsInfos) {
                Sheet sheet = workBook.createSheet(rltCls.getClassCode());
                Row titleRow = sheet.createRow(0);
                if (rltCls.getCcAttrDefs() != null && rltCls.getCcAttrDefs().size() > 0) {
                    for (int titIndex = 0; titIndex < rltCls.getCcAttrDefs().size(); titIndex++) {
                        CcCiAttrDef attrDef = rltCls.getCcAttrDefs().get(titIndex);
                        Cell titleCell = titleRow.createCell(titIndex);
                        titleCell.setCellValue(attrDef.getProName());
                        boolean major = (attrDef.getIsMajor() != null && attrDef.getIsMajor() == 1);
                        boolean required = (attrDef.getIsRequired() != null && attrDef.getIsRequired() == 1);
                        titleCell = FileUtil.ExcelUtil.setCellMark(workBook, titleCell, markName, major, required);
                    }
                }
                FileUtil.ExcelUtil.setAutoColumnSizeByRow(sheet, titleRow);
            }
        } else {
            exportFileName = "关系属性模板.xlsx";
            Sheet sheet = workBook.createSheet("关系分类名称");
            Row titleRow = sheet.createRow(0);
            Cell titleCell = titleRow.createCell(0);
            titleCell.setCellValue("属性01");
            titleCell = FileUtil.ExcelUtil.setCellMark(workBook, titleCell, markName, false, false);
            titleCell = titleRow.createCell(1);
            titleCell.setCellValue("属性02");
            titleCell = FileUtil.ExcelUtil.setCellMark(workBook, titleCell, markName, true, false);
            FileUtil.ExcelUtil.setAutoColumnSizeByRow(sheet, titleRow);
        }
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        try {
            workBook.write(os);
            resource = new ByteArrayResource(os.toByteArray(), exportFileName);
        } catch (IOException e) {
            log.error("导出ci关系写入输出流异常", e);
        } finally {
            try {
                if (os != null) {
                    os.close();
                }
                if (temIs != null) {
                    temIs.close();
                }
                if (workBook != null) {
                    workBook.close();
                }
            } catch (IOException e) {
                log.error("导出ci关系关闭输出流异常", e);
            }
        }
        return resource;
    }

    @Override
    public ESCIClassInfo importAttrDefs(MultipartFile excelFile, Long clsId) {
        // 简单校验文件合法性
        Assert.notNull(excelFile, "导入文件不得为空");
        Assert.notNull(clsId, "clsId notnull");
        String fileName = excelFile.getOriginalFilename();
        Assert.isTrue(fileName.toLowerCase().endsWith(".xls") || fileName.toLowerCase().endsWith(".xlsx"),
                "导入文件后缀不正确（支持.xls/.xlsx）");
        // 获取工作簿对象
        Workbook workbook = getWorkBookByImportCiRltFile(excelFile);
        ESCIClassInfo rltCls = esRltClassSvc.getById(clsId);
        try {
            Assert.notNull(rltCls, "指定关系分类不存在");
            boolean hasData = esCiRltSvc.countByCondition(QueryBuilders.termQuery("classId", clsId)) > 0;
            // Assert.isTrue(validCount <= 0, "该关系分类下已有数据不允许导入属性");
            Sheet sheet = workbook.getSheet(rltCls.getClassCode());
            Assert.notNull(sheet, "excel中没有对应分类sheet页");
            List<CcCiAttrDef> defs = getDefsBySheet(sheet, 0);
            // 验证sheet中表头重复
            if (defs.size() > 0) {
                Set<String> stdNames = new HashSet<>();
                defs.forEach(def -> stdNames.add(def.getProStdName()));
                Assert.isTrue(defs.size() == stdNames.size(), "表头有重复属性");
            }
            Set<String> existDefStdCodes = new HashSet<>();
            if (rltCls.getCcAttrDefs() != null) {
                rltCls.getCcAttrDefs().forEach(def -> existDefStdCodes.add(def.getProStdName()));
            }
            List<CcCiAttrDef> importDefs = new LinkedList<>();
            for (CcCiAttrDef def : defs) {
                if (existDefStdCodes.contains(def.getProStdName())) {
                    // 待导入属性已存在忽略
                } else if (hasData && def.getIsMajor() != null && def.getIsMajor() == 1) {
                    // 有数据，待导入子code忽略
                } else if (hasData && def.getIsRequired() != null && def.getIsRequired() == 1) {
                    // 有数据待导入必填忽略
                } else if (!def.getProStdName()
                        .matches("[A-Za-z0-9 \\_\\&\\(\\)\\（\\）\\|\\/\\'\\-\\@\\u4e00-\\u9fa5]+")) {
                    // 属性包含非法字符忽略
                } else {
                    importDefs.add(def);
                }
            }
            rltCls.setCcAttrDefs(rltCls.getCcAttrDefs() == null ? new LinkedList<>() : rltCls.getCcAttrDefs());
            if (importDefs.size() > 0) {
                rltCls.getCcAttrDefs().addAll(importDefs);
                esRltClassSvc.saveOrUpdate(rltCls);
            }
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        } finally {
            try {
                workbook.close();
            } catch (IOException e) {
            }
        }
        return rltCls;
    }

    @Override
    public boolean reOrder(ClassReOrderDTO reOrderDTO) {
        if (reOrderDTO.getOriginOrder() == reOrderDTO.getNewOrder()) { return true; }
        boolean flag;
        Long domainId = reOrderDTO.getDomainId() == null ? BaseConst.DEFAULT_DOMAIN_ID : reOrderDTO.getDomainId();
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("domainId", domainId));
        if (reOrderDTO.getOriginOrder() > reOrderDTO.getNewOrder()) {
            // 向上移动
            query.must(QueryBuilders.rangeQuery("orderNo").lt(reOrderDTO.getOriginOrder()).gte(reOrderDTO.getNewOrder()));
            flag = esRltClassSvc.updateByQuery(QueryBuilders.constantScoreQuery(query), "ctx._source.orderNo+=1", true);
        } else {
            // 向下移动
            query.must(QueryBuilders.rangeQuery("orderNo").lte(reOrderDTO.getNewOrder()).gt(reOrderDTO.getOriginOrder()));
            flag = esRltClassSvc.updateByQuery(QueryBuilders.constantScoreQuery(query), "ctx._source.orderNo-=1", true);
        }
        // 最后更新传入的节点
        if (flag) { return esRltClassSvc.updateByQuery(QueryBuilders.termQuery("id", reOrderDTO.getClassId()),
                "ctx._source.orderNo=" + reOrderDTO.getNewOrder(), true); }
        return false;
    }

    @Override
    public boolean initAllOrderNo(Long domainId) {
        return esRltClassSvc.initAllOrderNo(domainId);
    }

    /**
     * 根据sheet头构建属性模板s-关系分类修改为全属性非必填
     *
     * @param sheet
     * @return
     */
    public static List<CcCiAttrDef> getDefsBySheet(Sheet sheet, int startIndex) {
        String markName = SysUtil.StaticUtil.RLTCLS_MAJOR_MARK;
        List<CcCiAttrDef> defs = new LinkedList<>();
        if (sheet.getRow(0).getLastCellNum() > startIndex) {
            for (int index = startIndex; index < sheet.getRow(0).getLastCellNum(); index++) {
                Cell cell = sheet.getRow(0).getCell(index);
                String title = FileUtil.ExcelUtil.getCellTitleString(cell);
                if (title == null || "".equals(title.trim())) {
                    continue;
                }
                title = title.trim();
                CcCiAttrDef def = new CcCiAttrDef();
                defs.add(def);
                def.setId(ESUtil.getUUID());
                def.setProName(title);
                def.setProStdName(def.getProName().toUpperCase());
                def.setIsMajor(FileUtil.ExcelUtil.isMajorCell(cell, markName) ? 1 : 0);
                // 关系分类修改为全属性非必填
                // def.setIsRequired(FileUtil.ExcelUtil.isRequireCell(cell) ? 1
                // : 0);
                def.setIsRequired(0);
                def.setProType(3);
            }
        }
        return defs;
    }

    /**
     * 根据导入ci关系excel获取工作簿
     *
     * @param excelFile
     * @return
     */
    private Workbook getWorkBookByImportCiRltFile(MultipartFile excelFile) {
        // 待返回工作簿
        Workbook workbook = null;
        // 获取文件名，区分两种文件以不同方式获取工作簿
        String fileNameLow = excelFile.getOriginalFilename().toLowerCase().trim();
        try {
            InputStream inStream = excelFile.getInputStream();
            if (fileNameLow.endsWith(".xls")) {
                workbook = new HSSFWorkbook(inStream);
            } else if (fileNameLow.endsWith(".xlsx")) {
                workbook = new XSSFWorkbook(inStream);
            }
        } catch (Exception e) {
            log.error("读取导入文件/从文件读取到工作簿异常【{}】", e);
            throw new RuntimeException(e.getMessage(), e);
        }
        return workbook;
    }
}
