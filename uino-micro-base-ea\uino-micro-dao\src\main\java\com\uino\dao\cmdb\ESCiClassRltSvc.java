package com.uino.dao.cmdb;

import jakarta.annotation.PostConstruct;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.stereotype.Service;

import com.uino.dao.AbstractESBaseDao;
import com.uino.dao.ESConst;
import com.uino.bean.cmdb.base.ESCiClassRlt;
import com.uino.bean.cmdb.query.ESCiClassRltSearchBean;

/**
 * ci分类关系es dao
 * 
 * <AUTHOR>
 *
 */
@Service
public class ESCiClassRltSvc extends AbstractESBaseDao<ESCiClassRlt, ESCiClassRltSearchBean> {

    Log logger = LogFactory.getLog(ESCiClassRltSvc.class);

    @Override
    public String getIndex() {
        return ESConst.INDEX_CMDB_CICLASSRLT;
    }

    @Override
    public String getType() {
        return ESConst.INDEX_CMDB_CICLASSRLT;
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }
}
