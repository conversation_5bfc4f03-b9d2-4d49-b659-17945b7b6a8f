package com.uino.bean.monitor.base;

import java.sql.Timestamp;
import java.util.List;

import com.binary.framework.bean.annotation.Comment;
import org.springframework.util.Assert;

import com.binary.core.util.BinaryUtils;
import com.uino.bean.permission.business.IValidDto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Builder.Default;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "性能数据模拟规则定义")
public  class SimulationRuleInfo implements IValidDto {

    @ApiModelProperty(value = "规则id")
    public  Long id;

    @ApiModelProperty(value = "规则名称", example = "性能数据模拟")
    public String ruleName;

    @ApiModelProperty(value = "孪生体模型的id", example = "7490904610300711")
    public Long classId;

    @ApiModelProperty(value = "孪生体的id集合", example = "[7511312077260842,7544064097150987]")
    public List<Long> ciIds;

    @ApiModelProperty(value = "规则类型，1=性能，2=告警", example = "1")
    public Integer ruleType;

    @ApiModelProperty(value = "指标名称", example = "温度")
    public String metric;

    @ApiModelProperty(value = "发送频率(传参以h,m,s结尾，分别代表时、分、秒)", example = "60s")
    private String sendCycle;

    @ApiModelProperty(value = "数据类型，1=随机，2=序列", example = "1")
    private Integer dataType;

    @ApiModelProperty(value = "约束(随机类型：数值间用\"-\"分隔；序列类型：数值间用\",\"分隔)", example = "10-30")
    private String constraint;

    @ApiModelProperty(value = "发送次数(单位次)", example = "10")
    private Integer sendTimes;

    @ApiModelProperty(value = "是否循环发送，默认false", example = "true/false")
    @Builder.Default
    private Boolean isCycle = false;

    @ApiModelProperty(value = "开始时间", example = "1626237072000")
    public Long startTime;

    @ApiModelProperty(value = "结束时间", example = "1626237072526")
    public Long endTime;


    @ApiModelProperty(value = "规则状态：0=停用，1=启用", example = "0")
    public Integer ruleStatus;

    @ApiModelProperty(value = "用于记录规则发送次数或序列下标(后端使用)")
    @Default
    public Integer index = 0;

    /**
     * 所属域
     */
    @ApiModelProperty(value="所属域")
    public Long domainId;

    /**
     * 创建人
     */
    @ApiModelProperty(value="创建人")
    public String creator;

    /**
     * 修改人
     */
    @ApiModelProperty(value="修改人")
    public String modifier;

    /**
     * 由于无法保证递增id，该创建时间同时会被各个server当作指针起点使用
     */
    @ApiModelProperty(value="创建时间")
    public Long createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty(value="修改时间")
    public Long modifyTime;

    /**
     * 唯一key
     * <p>
     * ${objId}_${kpiName}
     */
    @ApiModelProperty(value="唯一key")
    private String uniqueKey;

    /**
     * 告警编号
     */
    @ApiModelProperty(value="告警编号")
    private Long alarmCode;

    /**
     * 告警标志
     */
    @ApiModelProperty(value="告警标志")
    private String alarmMark;

    /**
     * 告警对象类型0:对象1:关系
     */
    @ApiModelProperty(value="告警对象类型0:对象1:关系")
    private int alarmObjType;

    /**
     * 告警对象id alarmObjType为对象时为对象id，为关系时为关系id
     */
    @ApiModelProperty(value="告警对象id alarmObjType为对象时为对象id，为关系时为关系id")
    private Long alarmObjId;

    /**
     * 告警级别id
     */
    @ApiModelProperty(value="告警级别id")
    private Long severityId;

    /**
     * 告警级别
     */
    @ApiModelProperty(value="告警级别")
    private Integer severityLevel;

    /**
     * 详情
     */
    @ApiModelProperty(value="告警详情")
    private String desc;

    /**
     * 来源
     */
    @ApiModelProperty(value="来源")
    private String source;

    /**
     * 来源id
     */
    @ApiModelProperty(value="来源id")
    private Integer sourceId;

    /**
     * 告警产生时间毫秒值
     */
    @ApiModelProperty(value="告警产生时间毫秒值")
    private Long alarmTime;


    /**
     * 告警状态0:开始1关闭2未确认3已确认4未派单5已派单6已通知7未通知
     */
    @ApiModelProperty(value="告警状态0:开始1关闭2未确认3已确认4未派单5已派单6已通知7未通知")
    private int status;

    /**
     * 是否为模拟数据
     */
    @ApiModelProperty(value="是否为模拟数据")
    private boolean mock;

    /**
     * 告警最后一次发生时间
     */
    @ApiModelProperty(value="告警最后一次发生时间")
    private Timestamp lastOccurrence;

    /**
     * 告警首次 发生时间
     */
    @ApiModelProperty(value="告警首次 发生时间")
    private Timestamp firstOccurrence;

    @ApiModelProperty(value="自定义字段")
    @Comment("自定义字段")
    private String customize;

    @Override
    public void valid() {
        Assert.isTrue(classId != null || !BinaryUtils.isEmpty(ciIds), "X_PARAM_NOT_NULL${name:classId/ciIds}");
        Assert.isTrue(ruleType != null, "X_PARAM_NOT_NULL${name:ruleType}");
        Assert.isTrue(!BinaryUtils.isEmpty(metric), "X_PARAM_NOT_NULL${name:metric}");
        Assert.isTrue(dataType != null, "X_PARAM_NOT_NULL${name:dataType}");
        Assert.isTrue(!BinaryUtils.isEmpty(sendCycle), "X_PARAM_NOT_NULL${name:sendCycle}");
        Assert.isTrue(!BinaryUtils.isEmpty(constraint), "X_PARAM_NOT_NULL${name:constraint}");
    }
}
