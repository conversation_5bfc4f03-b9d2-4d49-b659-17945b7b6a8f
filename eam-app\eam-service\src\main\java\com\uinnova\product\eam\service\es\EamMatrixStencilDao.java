package com.uinnova.product.eam.service.es;

import com.uinnova.product.eam.comm.model.es.EamMatrixStencil;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;

/**
 * 矩阵制品管理
 * <AUTHOR>
 */
@Service
public class EamMatrixStencilDao extends AbstractESBaseDao<EamMatrixStencil, EamMatrixStencil> {

    @Override
    public String getIndex() {
        return "uino_eam_matrix_stencil";
    }

    @Override
    public String getType() {
        return "_doc";
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }

}
