package com.uino.api.client.cmdb;

import com.binary.core.io.Resource;
import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uinnova.product.vmdb.provider.rlt.bean.CcCiRltInfo;
import com.uino.bean.cmdb.base.ESCIRltInfo;
import com.uino.bean.cmdb.base.ESCIRltInfoHistory;
import com.uino.bean.cmdb.base.LibType;
import com.uino.bean.cmdb.business.BindCiRltRequestDto;
import com.uino.bean.cmdb.business.DataModuleRltClassDto;
import com.uino.bean.cmdb.business.ImportExcelMessage;
import com.uino.bean.cmdb.business.ImportResultMessage;
import com.uino.bean.cmdb.query.ESAttrAggBean;
import com.uino.bean.cmdb.query.ESRltSearchBean;
import com.uino.service.cmdb.microservice.ICIRltSvc;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * ci关系相关服务类
 * 
 * <AUTHOR>
 */
public interface ICIRltApiSvc {

    /**
     *  绑定ci关系
     * @param bindCiRltRequestDto
     * @return
     */
    Long bindCiRlt(BindCiRltRequestDto bindCiRltRequestDto);

    /**
     * @see ICIRltSvc#bindCiRlts(Set, boolean, Map, Map)
     * @param bindRltDtos
     * @param repeat
     * @return
     */
    ImportResultMessage bindCiRlts(Set<BindCiRltRequestDto> bindRltDtos, boolean repeat);
    ImportResultMessage bindCiRlts(Long domainId, Set<BindCiRltRequestDto> bindRltDtos, boolean repeat);

    /**
     * 批量绑定ci关系
     * 
     * @param bindRltDtos
     * @return
     */
    ImportResultMessage bindCiRlts(Set<BindCiRltRequestDto> bindRltDtos);
    ImportResultMessage bindCiRlts(Long domainId, Set<BindCiRltRequestDto> bindRltDtos);

    /**
     * 根据ciId解除ci关系（无论是目标还是源都解除）
     * 
     * @param ciId
     *            源/目标ciId
     * @return
     */
    Integer delRltByCiId(Long ciId);

    /**
     * 根据【关系ids OR 关系codes】解除ci关系
     * 
     * @param rltIds
     * @param rltCodes
     * @return
     */
    Integer delRltByIdsOrRltCodes(Set<Long> rltIds, Set<String> rltCodes);

    /**
     * 修改ci关系信息(只能修改属性信息)
     * 
     * @param ciRltId
     *            ci关系Id
     * @param attrs
     *            属性信息
     * @return
     */
    Long updateCiRltAttr(Long ciRltId, Map<String, String> attrs);

    /**
     * 分页查询ci关系（带条件）
     * 
     * @param bean
     * @return
     */
    Page<CcCiRltInfo> searchRltByBean(ESRltSearchBean bean);

    /**
     * 根据ids查询ci关系
     * 
     * @param ids
     * @return
     */
    List<CcCiRltInfo> searchRltByIds(Set<Long> ids);

    /**
     * 清除某个关系分类下所有ci关系
     * 
     * @param rltClassId
     * @return
     */
    Integer clearRltByClassId(Long rltClassId);

    /**
     * {@link #exportCiRlt(Set, Set)}
     * 
     * @param rltClassIds
     * @return
     */
    default Resource exportCiRlt(Set<Long> rltClassIds) {
        ESRltSearchBean bean = new ESRltSearchBean();
        bean.setRltClassIds(new ArrayList<>(rltClassIds));
        return exportCiRlt(bean);
    }

    /**
     * 导出ci关系
     * 
     * @return
     */
    Resource exportCiRlt(ESRltSearchBean bean);

    /**
     * 导入ci关系
     * 
     * @param excelFilePath
     * @param excelFile
     * @param rltClsCodes
     * @return
     */
    ImportResultMessage importCiRlt(String excelFilePath, MultipartFile excelFile, Set<String> rltClsCodes);
    ImportResultMessage importCiRlt(Long domainId, String excelFilePath, MultipartFile excelFile, Set<String> rltClsCodes);

    /**
     * 解析关系excel
     * 
     * @param excelFile
     *            待解析excel
     * @return {关系分类code:是否已存在}
     */
    Map<String, Boolean> comprehendRltExcel(MultipartFile excelFile);

    /**
     * 解析关系excel
     * 
     * @param excelFilePath
     * @param excelFile
     * @return
     */
    ImportExcelMessage parseRltExcel(String excelFilePath, MultipartFile excelFile);

    /**
     * 分页查询ci关系（带条件）
     * 
     * @param bean
     * @return
     */
    public Page<ESCIRltInfo> searchRlt(ESRltSearchBean bean);

    /**
     * 类sql: select fieldName from table group by fieldName where *****
     * 
     * @param req
     *            条件
     * @return
     */
    public Page<String> groupByField(ESAttrAggBean req);
    public Page<String> groupByField(Long domainId, ESAttrAggBean req);

    /**
     * 根据分类筛选条件获取分类关系map
     * 
     * @param clsIds
     *            ci分类ids
     * @param rltClsIds
     *            关系分类ids
     * @return {源分类id:{关系分类id:[目标分类ids]}}
     */
    public Map<Long, Map<Long, Set<Long>>> getClassRltMapByClsQuery(Set<Long> clsIds, Set<Long> rltClsIds);

    /**
     * 根据分类筛选条件获取分类关系map
     *
     * @param classIds ci分类ids
     * @param rltClsIds 关系分类ids
     * @param libType 库类型（私有/设计/运行）
     * @return 元模型上使用包含起始分类和目标分类的关系对象信息
     */
    List<DataModuleRltClassDto> getClassRltList(Set<Long> classIds, Set<Long> rltClsIds);

    /**
     * 获取{ciRltId:对应历史版本信息s}字典
     *
     * @param rltIds
     *            需组装得关系历史字典得关系ids
     * @param hasCurrent
     *            是否需包含当前版本
     * @return {ciRltId:对应历史版本信息s}字典
     */
    Map<Long, List<ESCIRltInfoHistory>> getRltsHistrysDict(Set<Long> rltIds, boolean hasCurrent);

    /**
     * 获取{rltId：maxVersion}键值对
     *
     * @param rltIds
     *            if null:获取全部rltid：maxversion键值对 ，if notnull:仅获取指定rltid
     * @return {rltid：maxVersion}
     */
    Map<Long, Long> getRltIdMaxVersion(Set<Long> rltIds);

    /**
     * 获取所有关系分类
     *
     * @param
     * @param
     */
    List<CcCiClassInfo> queryAllClasses(Long domainId);
}
