package com.uinnova.product.vmdb.comm.model.kpi;




import com.binary.core.util.BinaryUtils;
import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.vmdb.comm.util.CommUtil;


@Comment("KPI表[CC_KPI]")
public class CCcKpi implements Condition {
	private static final long serialVersionUID = 1L;


	@Comment("ID[ID] operate-Equal[=]")
	private Long id;


	@Comment("ID[ID] operate-In[in]")
	private Long[] ids;


	@Comment("ID[ID] operate-GTEqual[>=]")
	private Long startId;

	@Comment("ID[ID] operate-LTEqual[<=]")
	private Long endId;


	@Comment("KPI名称[KPI_CODE] operate-Like[like]")
	private String kpiCode;


	@Comment("KPI名称[KPI_CODE] operate-Equal[=]")
	private String kpiCodeEqual;


	@Comment("KPI名称[KPI_CODE] operate-In[in]")
	private String[] kpiCodes;


	@Comment("KPI别名[KPI_NAME] operate-Like[like]")
	private String kpiName;


	@Comment("KPI别名[KPI_NAME] operate-Equal[=]")
	private String kpiNameEqual;


	@Comment("KPI别名[KPI_NAME] operate-In[in]")
	private String[] kpiNames;


	@Comment("所属分类[CLASS_ID] operate-Equal[=]")
	private Long classId;


	@Comment("所属分类[CLASS_ID] operate-In[in]")
	private Long[] classIds;


	@Comment("所属分类[CLASS_ID] operate-GTEqual[>=]")
	private Long startClassId;

	@Comment("所属分类[CLASS_ID] operate-LTEqual[<=]")
	private Long endClassId;


	@Comment("数值类型[VAL_TYPE] operate-Equal[=]    数值类型:1=数值 2=字符")
	private Integer valType;


	@Comment("数值类型[VAL_TYPE] operate-In[in]    数值类型:1=数值 2=字符")
	private Integer[] valTypes;


	@Comment("数值类型[VAL_TYPE] operate-GTEqual[>=]    数值类型:1=数值 2=字符")
	private Integer startValType;

	@Comment("数值类型[VAL_TYPE] operate-LTEqual[<=]    数值类型:1=数值 2=字符")
	private Integer endValType;


	@Comment("单位ID[UNIT_ID] operate-Equal[=]")
	private Long unitId;


	@Comment("单位ID[UNIT_ID] operate-In[in]")
	private Long[] unitIds;


	@Comment("单位ID[UNIT_ID] operate-GTEqual[>=]")
	private Long startUnitId;

	@Comment("单位ID[UNIT_ID] operate-LTEqual[<=]")
	private Long endUnitId;


	@Comment("单位名称[UNIT_NAME] operate-Like[like]")
	private String unitName;


	@Comment("单位名称[UNIT_NAME] operate-Equal[=]")
	private String unitNameEqual;


	@Comment("单位名称[UNIT_NAME] operate-In[in]")
	private String[] unitNames;


	@Comment("数据阈值[VAL_EXP] operate-Like[like]")
	private String valExp;


	@Comment("来源[SOURCE_ID] operate-Equal[=]")
	private Long sourceId;


	@Comment("来源[SOURCE_ID] operate-In[in]")
	private Long[] sourceIds;


	@Comment("来源[SOURCE_ID] operate-GTEqual[>=]")
	private Long startSourceId;

	@Comment("来源[SOURCE_ID] operate-LTEqual[<=]")
	private Long endSourceId;

	@Comment("所有者[OWNER_ID] operate-Equal[=]")
	private String ownerCodeEqual;

	@Comment("所有者[OWNER_ID] operate-In[in]")
	private String[] ownerCodes;

	@Comment("KPI描述[KPI_DESC] operate-Like[like]")
	private String kpiDesc;


	@Comment("搜索字段[SEARCH_VALUE] operate-Like[like]")
	private String searchValue;


	@Comment("备用_1[CUSTOM_1] operate-Equal[=]")
	private Long custom1;


	@Comment("备用_1[CUSTOM_1] operate-In[in]")
	private Long[] custom1s;


	@Comment("备用_1[CUSTOM_1] operate-GTEqual[>=]")
	private Long startCustom1;

	@Comment("备用_1[CUSTOM_1] operate-LTEqual[<=]")
	private Long endCustom1;


	@Comment("备用_2[CUSTOM_2] operate-Equal[=]")
	private Long custom2;


	@Comment("备用_2[CUSTOM_2] operate-In[in]")
	private Long[] custom2s;


	@Comment("备用_2[CUSTOM_2] operate-GTEqual[>=]")
	private Long startCustom2;

	@Comment("备用_2[CUSTOM_2] operate-LTEqual[<=]")
	private Long endCustom2;


	@Comment("备用_3[CUSTOM_3] operate-Equal[=]")
	private Long custom3;


	@Comment("备用_3[CUSTOM_3] operate-In[in]")
	private Long[] custom3s;


	@Comment("备用_3[CUSTOM_3] operate-GTEqual[>=]")
	private Long startCustom3;

	@Comment("备用_3[CUSTOM_3] operate-LTEqual[<=]")
	private Long endCustom3;


	@Comment("备用_4[CUSTOM_4] operate-Like[like]")
	private String custom4;


	@Comment("备用_4[CUSTOM_4] operate-Equal[=]")
	private String custom4Equal;


	@Comment("备用_4[CUSTOM_4] operate-In[in]")
	private String[] custom4s;


	@Comment("备用_5[CUSTOM_5] operate-Like[like]")
	private String custom5;


	@Comment("备用_5[CUSTOM_5] operate-Equal[=]")
	private String custom5Equal;


	@Comment("备用_5[CUSTOM_5] operate-In[in]")
	private String[] custom5s;


	@Comment("备用_6[CUSTOM_6] operate-Like[like]")
	private String custom6;


	@Comment("备用_6[CUSTOM_6] operate-Equal[=]")
	private String custom6Equal;


	@Comment("备用_6[CUSTOM_6] operate-In[in]")
	private String[] custom6s;


	@Comment("KPI扩展属性[KPI_EXT_PROS] operate-Like[like]    KPI扩展属性 [{val:'',img:''}]")
	private String kpiExtPros;


	@Comment("所属域[DOMAIN_ID] operate-Equal[=]")
	private Long domainId;


	@Comment("所属域[DOMAIN_ID] operate-In[in]")
	private Long[] domainIds;


	@Comment("所属域[DOMAIN_ID] operate-GTEqual[>=]")
	private Long startDomainId;

	@Comment("所属域[DOMAIN_ID] operate-LTEqual[<=]")
	private Long endDomainId;


	@Comment("数据状态[DATA_STATUS] operate-Equal[=]    数据状态:1-正常 0-删除")
	private Integer dataStatus;


	@Comment("数据状态[DATA_STATUS] operate-In[in]    数据状态:1-正常 0-删除")
	private Integer[] dataStatuss;


	@Comment("数据状态[DATA_STATUS] operate-GTEqual[>=]    数据状态:1-正常 0-删除")
	private Integer startDataStatus;

	@Comment("数据状态[DATA_STATUS] operate-LTEqual[<=]    数据状态:1-正常 0-删除")
	private Integer endDataStatus;


	@Comment("创建人[CREATOR] operate-Like[like]")
	private String creator;


	@Comment("创建人[CREATOR] operate-Equal[=]")
	private String creatorEqual;


	@Comment("创建人[CREATOR] operate-In[in]")
	private String[] creators;


	@Comment("修改人[MODIFIER] operate-Like[like]")
	private String modifier;


	@Comment("修改人[MODIFIER] operate-Equal[=]")
	private String modifierEqual;


	@Comment("修改人[MODIFIER] operate-In[in]")
	private String[] modifiers;


	@Comment("创建时间[CREATE_TIME] operate-Equal[=]    创建时间:yyyyMMddHHmmss")
	private Long createTime;


	@Comment("创建时间[CREATE_TIME] operate-In[in]    创建时间:yyyyMMddHHmmss")
	private Long[] createTimes;


	@Comment("创建时间[CREATE_TIME] operate-GTEqual[>=]    创建时间:yyyyMMddHHmmss")
	private Long startCreateTime;

	@Comment("创建时间[CREATE_TIME] operate-LTEqual[<=]    创建时间:yyyyMMddHHmmss")
	private Long endCreateTime;


	@Comment("修改时间[MODIFY_TIME] operate-Equal[=]    修改时间:yyyyMMddHHmmss")
	private Long modifyTime;


	@Comment("修改时间[MODIFY_TIME] operate-In[in]    修改时间:yyyyMMddHHmmss")
	private Long[] modifyTimes;


	@Comment("修改时间[MODIFY_TIME] operate-GTEqual[>=]    修改时间:yyyyMMddHHmmss")
	private Long startModifyTime;

	@Comment("修改时间[MODIFY_TIME] operate-LTEqual[<=]    修改时间:yyyyMMddHHmmss")
	private Long endModifyTime;


	@Comment("指标单位转换模板ID[UINIT_CONVERT_MODEL_ID] operate-Equal[=]")
	private Long uinitConvertModelId;


	@Comment("指标单位转换模板ID[UINIT_CONVERT_MODEL_ID] operate-In[in]")
	private Long[] uinitConvertModelIds;


	@Comment("指标单位转换模板ID[UINIT_CONVERT_MODEL_ID] operate-GTEqual[>=]")
	private Long startUinitConvertModelId;

	@Comment("指标单位转换模板ID[UINIT_CONVERT_MODEL_ID] operate-LTEqual[<=]")
	private Long endUinitConvertModelId;


	@Comment("监控系统[MONITOR_SYSTEM] operate-Like[like]")
	private String monitorSystem;


	@Comment("指标类型[KPI_TYPE] operate-Like[like]")
	private String kpiType;


	@Comment("指标类型[KPI_TYPE] operate-Equal[=]")
	private String kpiTypeEqual;


	@Comment("指标类型[KPI_TYPE] operate-In[in]")
	private String[] kpiTypes;


	@Comment("指标大类[KPI_CATEGORY] operate-Like[like]")
	private String kpiCategory;


	@Comment("指标大类[KPI_CATEGORY] operate-Equal[=]")
	private String kpiCategoryEqual;


	@Comment("指标大类[KPI_CATEGORY] operate-In[in]")
	private String[] kpiCategorys;


	@Comment("采集周期[COLLECTION_CYCLE] operate-Like[like]")
	private String collectionCycle;


	@Comment("采集周期[COLLECTION_CYCLE] operate-Equal[=]")
	private String collectionCycleEqual;


	@Comment("采集周期[COLLECTION_CYCLE] operate-In[in]")
	private String[] collectionCycles;




	public Long getId() {
		return this.id;
	}
	public void setId(Long id) {
		this.id = id;
	}


	public Long[] getIds() {
		return this.ids;
	}
	public void setIds(Long[] ids) {
		this.ids = ids;
	}


	public Long getStartId() {
		return this.startId;
	}
	public void setStartId(Long startId) {
		this.startId = startId;
	}


	public Long getEndId() {
		return this.endId;
	}
	public void setEndId(Long endId) {
		this.endId = endId;
	}


	public String getKpiCode() {
		return this.kpiCode;
	}
	public void setKpiCode(String kpiCode) {
		if (!BinaryUtils.isEmpty(kpiCode)) {
			kpiCode = CommUtil.replaceForKpi(kpiCode);
		}
		this.kpiCode = kpiCode;
	}


	public String getKpiCodeEqual() {
		return this.kpiCodeEqual;
	}
	public void setKpiCodeEqual(String kpiCodeEqual) {

		if (!BinaryUtils.isEmpty(kpiCodeEqual)) {
			kpiCodeEqual = CommUtil.replaceForKpi(kpiCodeEqual);
		}
		this.kpiCodeEqual = kpiCodeEqual;
	}


	public String[] getKpiCodes() {
		return this.kpiCodes;
	}
	public void setKpiCodes(String[] kpiCodes) {
		if (kpiCodes != null && kpiCodes.length != 0) {
			for	(int i = 0; i < kpiCodes.length; i++) {
				String kpiCode = kpiCodes[i];
				if (!BinaryUtils.isEmpty(kpiCode)) {
					kpiCodes[i] = CommUtil.replaceForKpi(kpiCode);
				}
			}
		}
		this.kpiCodes = kpiCodes;
	}


	public String getKpiName() {
		return this.kpiName;
	}
	public void setKpiName(String kpiName) {
		if (!BinaryUtils.isEmpty(kpiName)) {
			kpiName = CommUtil.replaceForKpi(kpiName);
		}
		this.kpiName = kpiName;
	}


	public String getKpiNameEqual() {
		return this.kpiNameEqual;
	}
	public void setKpiNameEqual(String kpiNameEqual) {
		if (!BinaryUtils.isEmpty(kpiNameEqual)) {
			kpiNameEqual = CommUtil.replaceForKpi(kpiNameEqual);
		}
		this.kpiNameEqual = kpiNameEqual;
	}


	public String[] getKpiNames() {
		return this.kpiNames;
	}
	public void setKpiNames(String[] kpiNames) {
		if (kpiNames != null && kpiNames.length != 0) {
			for	(int i = 0; i < kpiNames.length; i++) {
				String kpiName = kpiNames[i];
				if (!BinaryUtils.isEmpty(kpiCode)) {
					kpiNames[i] = CommUtil.replaceForKpi(kpiName);
				}
			}
		}
		this.kpiNames = kpiNames;
	}


	public Long getClassId() {
		return this.classId;
	}
	public void setClassId(Long classId) {
		this.classId = classId;
	}


	public Long[] getClassIds() {
		return this.classIds;
	}
	public void setClassIds(Long[] classIds) {
		this.classIds = classIds;
	}


	public Long getStartClassId() {
		return this.startClassId;
	}
	public void setStartClassId(Long startClassId) {
		this.startClassId = startClassId;
	}


	public Long getEndClassId() {
		return this.endClassId;
	}
	public void setEndClassId(Long endClassId) {
		this.endClassId = endClassId;
	}


	public Integer getValType() {
		return this.valType;
	}
	public void setValType(Integer valType) {
		this.valType = valType;
	}


	public Integer[] getValTypes() {
		return this.valTypes;
	}
	public void setValTypes(Integer[] valTypes) {
		this.valTypes = valTypes;
	}


	public Integer getStartValType() {
		return this.startValType;
	}
	public void setStartValType(Integer startValType) {
		this.startValType = startValType;
	}


	public Integer getEndValType() {
		return this.endValType;
	}
	public void setEndValType(Integer endValType) {
		this.endValType = endValType;
	}


	public Long getUnitId() {
		return this.unitId;
	}
	public void setUnitId(Long unitId) {
		this.unitId = unitId;
	}


	public Long[] getUnitIds() {
		return this.unitIds;
	}
	public void setUnitIds(Long[] unitIds) {
		this.unitIds = unitIds;
	}


	public Long getStartUnitId() {
		return this.startUnitId;
	}
	public void setStartUnitId(Long startUnitId) {
		this.startUnitId = startUnitId;
	}


	public Long getEndUnitId() {
		return this.endUnitId;
	}
	public void setEndUnitId(Long endUnitId) {
		this.endUnitId = endUnitId;
	}


	public String getUnitName() {
		return this.unitName;
	}
	public void setUnitName(String unitName) {
		this.unitName = unitName;
	}


	public String getUnitNameEqual() {
		return this.unitNameEqual;
	}
	public void setUnitNameEqual(String unitNameEqual) {
		this.unitNameEqual = unitNameEqual;
	}


	public String[] getUnitNames() {
		return this.unitNames;
	}
	public void setUnitNames(String[] unitNames) {
		this.unitNames = unitNames;
	}


	public String getValExp() {
		return this.valExp;
	}
	public void setValExp(String valExp) {
		this.valExp = valExp;
	}


	public Long getSourceId() {
		return this.sourceId;
	}
	public void setSourceId(Long sourceId) {
		this.sourceId = sourceId;
	}


	public Long[] getSourceIds() {
		return this.sourceIds;
	}
	public void setSourceIds(Long[] sourceIds) {
		this.sourceIds = sourceIds;
	}


	public Long getStartSourceId() {
		return this.startSourceId;
	}
	public void setStartSourceId(Long startSourceId) {
		this.startSourceId = startSourceId;
	}


	public Long getEndSourceId() {
		return this.endSourceId;
	}
	public void setEndSourceId(Long endSourceId) {
		this.endSourceId = endSourceId;
	}


	public String getOwnerCodeEqual() {
		return this.ownerCodeEqual;
	}
	public void setOwnerCodeEqual(String ownerCodeEqual) {
		this.ownerCodeEqual = ownerCodeEqual;
	}


	public String[] getOwnerCodes() {
		return this.ownerCodes;
	}
	public void setOwnerCodes(String[] ownerCodes) {
		this.ownerCodes = ownerCodes;
	}


	public String getKpiDesc() {
		return this.kpiDesc;
	}
	public void setKpiDesc(String kpiDesc) {
		this.kpiDesc = kpiDesc;
	}


	public String getSearchValue() {
		return this.searchValue;
	}
	public void setSearchValue(String searchValue) {
		this.searchValue = searchValue;
	}


	public Long getCustom1() {
		return this.custom1;
	}
	public void setCustom1(Long custom1) {
		this.custom1 = custom1;
	}


	public Long[] getCustom1s() {
		return this.custom1s;
	}
	public void setCustom1s(Long[] custom1s) {
		this.custom1s = custom1s;
	}


	public Long getStartCustom1() {
		return this.startCustom1;
	}
	public void setStartCustom1(Long startCustom1) {
		this.startCustom1 = startCustom1;
	}


	public Long getEndCustom1() {
		return this.endCustom1;
	}
	public void setEndCustom1(Long endCustom1) {
		this.endCustom1 = endCustom1;
	}


	public Long getCustom2() {
		return this.custom2;
	}
	public void setCustom2(Long custom2) {
		this.custom2 = custom2;
	}


	public Long[] getCustom2s() {
		return this.custom2s;
	}
	public void setCustom2s(Long[] custom2s) {
		this.custom2s = custom2s;
	}


	public Long getStartCustom2() {
		return this.startCustom2;
	}
	public void setStartCustom2(Long startCustom2) {
		this.startCustom2 = startCustom2;
	}


	public Long getEndCustom2() {
		return this.endCustom2;
	}
	public void setEndCustom2(Long endCustom2) {
		this.endCustom2 = endCustom2;
	}


	public Long getCustom3() {
		return this.custom3;
	}
	public void setCustom3(Long custom3) {
		this.custom3 = custom3;
	}


	public Long[] getCustom3s() {
		return this.custom3s;
	}
	public void setCustom3s(Long[] custom3s) {
		this.custom3s = custom3s;
	}


	public Long getStartCustom3() {
		return this.startCustom3;
	}
	public void setStartCustom3(Long startCustom3) {
		this.startCustom3 = startCustom3;
	}


	public Long getEndCustom3() {
		return this.endCustom3;
	}
	public void setEndCustom3(Long endCustom3) {
		this.endCustom3 = endCustom3;
	}


	public String getCustom4() {
		return this.custom4;
	}
	public void setCustom4(String custom4) {
		this.custom4 = custom4;
	}


	public String getCustom4Equal() {
		return this.custom4Equal;
	}
	public void setCustom4Equal(String custom4Equal) {
		this.custom4Equal = custom4Equal;
	}


	public String[] getCustom4s() {
		return this.custom4s;
	}
	public void setCustom4s(String[] custom4s) {
		this.custom4s = custom4s;
	}


	public String getCustom5() {
		return this.custom5;
	}
	public void setCustom5(String custom5) {
		this.custom5 = custom5;
	}


	public String getCustom5Equal() {
		return this.custom5Equal;
	}
	public void setCustom5Equal(String custom5Equal) {
		this.custom5Equal = custom5Equal;
	}


	public String[] getCustom5s() {
		return this.custom5s;
	}
	public void setCustom5s(String[] custom5s) {
		this.custom5s = custom5s;
	}


	public String getCustom6() {
		return this.custom6;
	}
	public void setCustom6(String custom6) {
		this.custom6 = custom6;
	}


	public String getCustom6Equal() {
		return this.custom6Equal;
	}
	public void setCustom6Equal(String custom6Equal) {
		this.custom6Equal = custom6Equal;
	}


	public String[] getCustom6s() {
		return this.custom6s;
	}
	public void setCustom6s(String[] custom6s) {
		this.custom6s = custom6s;
	}


	public String getKpiExtPros() {
		return this.kpiExtPros;
	}
	public void setKpiExtPros(String kpiExtPros) {
		this.kpiExtPros = kpiExtPros;
	}


	public Long getDomainId() {
		return this.domainId;
	}
	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}


	public Long[] getDomainIds() {
		return this.domainIds;
	}
	public void setDomainIds(Long[] domainIds) {
		this.domainIds = domainIds;
	}


	public Long getStartDomainId() {
		return this.startDomainId;
	}
	public void setStartDomainId(Long startDomainId) {
		this.startDomainId = startDomainId;
	}


	public Long getEndDomainId() {
		return this.endDomainId;
	}
	public void setEndDomainId(Long endDomainId) {
		this.endDomainId = endDomainId;
	}


	public Integer getDataStatus() {
		return this.dataStatus;
	}
	public void setDataStatus(Integer dataStatus) {
		this.dataStatus = dataStatus;
	}


	public Integer[] getDataStatuss() {
		return this.dataStatuss;
	}
	public void setDataStatuss(Integer[] dataStatuss) {
		this.dataStatuss = dataStatuss;
	}


	public Integer getStartDataStatus() {
		return this.startDataStatus;
	}
	public void setStartDataStatus(Integer startDataStatus) {
		this.startDataStatus = startDataStatus;
	}


	public Integer getEndDataStatus() {
		return this.endDataStatus;
	}
	public void setEndDataStatus(Integer endDataStatus) {
		this.endDataStatus = endDataStatus;
	}


	public String getCreator() {
		return this.creator;
	}
	public void setCreator(String creator) {
		this.creator = creator;
	}


	public String getCreatorEqual() {
		return this.creatorEqual;
	}
	public void setCreatorEqual(String creatorEqual) {
		this.creatorEqual = creatorEqual;
	}


	public String[] getCreators() {
		return this.creators;
	}
	public void setCreators(String[] creators) {
		this.creators = creators;
	}


	public String getModifier() {
		return this.modifier;
	}
	public void setModifier(String modifier) {
		this.modifier = modifier;
	}


	public String getModifierEqual() {
		return this.modifierEqual;
	}
	public void setModifierEqual(String modifierEqual) {
		this.modifierEqual = modifierEqual;
	}


	public String[] getModifiers() {
		return this.modifiers;
	}
	public void setModifiers(String[] modifiers) {
		this.modifiers = modifiers;
	}


	public Long getCreateTime() {
		return this.createTime;
	}
	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}


	public Long[] getCreateTimes() {
		return this.createTimes;
	}
	public void setCreateTimes(Long[] createTimes) {
		this.createTimes = createTimes;
	}


	public Long getStartCreateTime() {
		return this.startCreateTime;
	}
	public void setStartCreateTime(Long startCreateTime) {
		this.startCreateTime = startCreateTime;
	}


	public Long getEndCreateTime() {
		return this.endCreateTime;
	}
	public void setEndCreateTime(Long endCreateTime) {
		this.endCreateTime = endCreateTime;
	}


	public Long getModifyTime() {
		return this.modifyTime;
	}
	public void setModifyTime(Long modifyTime) {
		this.modifyTime = modifyTime;
	}


	public Long[] getModifyTimes() {
		return this.modifyTimes;
	}
	public void setModifyTimes(Long[] modifyTimes) {
		this.modifyTimes = modifyTimes;
	}


	public Long getStartModifyTime() {
		return this.startModifyTime;
	}
	public void setStartModifyTime(Long startModifyTime) {
		this.startModifyTime = startModifyTime;
	}


	public Long getEndModifyTime() {
		return this.endModifyTime;
	}
	public void setEndModifyTime(Long endModifyTime) {
		this.endModifyTime = endModifyTime;
	}


	public Long getUinitConvertModelId() {
		return this.uinitConvertModelId;
	}
	public void setUinitConvertModelId(Long uinitConvertModelId) {
		this.uinitConvertModelId = uinitConvertModelId;
	}


	public Long[] getUinitConvertModelIds() {
		return this.uinitConvertModelIds;
	}
	public void setUinitConvertModelIds(Long[] uinitConvertModelIds) {
		this.uinitConvertModelIds = uinitConvertModelIds;
	}


	public Long getStartUinitConvertModelId() {
		return this.startUinitConvertModelId;
	}
	public void setStartUinitConvertModelId(Long startUinitConvertModelId) {
		this.startUinitConvertModelId = startUinitConvertModelId;
	}


	public Long getEndUinitConvertModelId() {
		return this.endUinitConvertModelId;
	}
	public void setEndUinitConvertModelId(Long endUinitConvertModelId) {
		this.endUinitConvertModelId = endUinitConvertModelId;
	}


	public String getMonitorSystem() {
		return this.monitorSystem;
	}
	public void setMonitorSystem(String monitorSystem) {
		this.monitorSystem = monitorSystem;
	}


	public String getKpiType() {
		return this.kpiType;
	}
	public void setKpiType(String kpiType) {
		this.kpiType = kpiType;
	}


	public String getKpiTypeEqual() {
		return this.kpiTypeEqual;
	}
	public void setKpiTypeEqual(String kpiTypeEqual) {
		this.kpiTypeEqual = kpiTypeEqual;
	}


	public String[] getKpiTypes() {
		return this.kpiTypes;
	}
	public void setKpiTypes(String[] kpiTypes) {
		this.kpiTypes = kpiTypes;
	}


	public String getKpiCategory() {
		return this.kpiCategory;
	}
	public void setKpiCategory(String kpiCategory) {
		this.kpiCategory = kpiCategory;
	}


	public String getKpiCategoryEqual() {
		return this.kpiCategoryEqual;
	}
	public void setKpiCategoryEqual(String kpiCategoryEqual) {
		this.kpiCategoryEqual = kpiCategoryEqual;
	}


	public String[] getKpiCategorys() {
		return this.kpiCategorys;
	}
	public void setKpiCategorys(String[] kpiCategorys) {
		this.kpiCategorys = kpiCategorys;
	}


	public String getCollectionCycle() {
		return this.collectionCycle;
	}
	public void setCollectionCycle(String collectionCycle) {
		this.collectionCycle = collectionCycle;
	}


	public String getCollectionCycleEqual() {
		return this.collectionCycleEqual;
	}
	public void setCollectionCycleEqual(String collectionCycleEqual) {
		this.collectionCycleEqual = collectionCycleEqual;
	}


	public String[] getCollectionCycles() {
		return this.collectionCycles;
	}
	public void setCollectionCycles(String[] collectionCycles) {
		this.collectionCycles = collectionCycles;
	}


}


