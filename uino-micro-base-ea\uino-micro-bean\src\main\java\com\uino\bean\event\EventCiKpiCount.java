package com.uino.bean.event;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class EventCiKpiCount implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("CI-CODE")
    private String ciCode;

    @ApiModelProperty("kpi-CODE")
    private String kpiCode;

    @ApiModelProperty("告警数量")
    private Integer count;

    @ApiModelProperty("告警等级")
    private Integer severity;

    @ApiModelProperty("最严重告警的颜色")
    private String color;

    @ApiModelProperty("压缩次数")
    private Integer tally;

}
