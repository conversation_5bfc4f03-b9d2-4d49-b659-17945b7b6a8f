package com.uinnova.product.eam.local.bm;

import com.uinnova.product.eam.api.IBmDiagramApiClient;
import com.uinnova.product.eam.model.asset.EamReleaseHistoryDTO;
import com.uinnova.product.eam.model.vo.CheckBatchArtifactRuleVo;
import com.uinnova.product.eam.service.BmDiagramSvc;
import com.uinnova.product.vmdb.provider.rlt.bean.CcCiRltInfo;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class BmDiagramSvcLocal implements IBmDiagramApiClient {

    @Resource
    private BmDiagramSvc bmDiagramSvc;

    @Override
    public List<EamReleaseHistoryDTO> queryReleaseHistory(String id, Boolean historyFlag) {
        return bmDiagramSvc.queryReleaseHistory(id, historyFlag);
    }

    @Override
    public boolean handlerRelease(String diagramId) {
        return bmDiagramSvc.handlerRelease(diagramId);
    }

    @Override
    public List<CcCiRltInfo> getActivityListByTaskRlt(String diagramId, String ciCode, String libType) {
       return bmDiagramSvc.getActivityListByTaskRlt(diagramId, ciCode, libType);
    }

    @Override
    public List<CheckBatchArtifactRuleVo> categoryEleNumCheckBatch(List<String> diagramIds) {
        return bmDiagramSvc.categoryEleNumCheckBatch(diagramIds);
    }

}
