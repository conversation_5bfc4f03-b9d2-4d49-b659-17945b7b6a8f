package com.uino.bean.dataset.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
@ApiModel(value="数据超市缩略图参数")
public class DataSetPathInfoVo implements Serializable {

    @ApiModelProperty(value="数据超市id",example = "123")
    private Long dataSetId;

    @ApiModelProperty(value="数据超市名称")
    private String name;

    @ApiModelProperty(value="缩略图")
    private String icon;

    @ApiModelProperty(value="路径信息")
    private List<Map<String, Object>> pathInfo;

}
