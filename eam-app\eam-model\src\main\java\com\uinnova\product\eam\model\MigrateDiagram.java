package com.uinnova.product.eam.model;

import java.io.Serializable;
import java.util.List;

import com.uinnova.product.eam.comm.model.DcCombDiagram;
import com.uinnova.product.eam.comm.model.VcDiagram;
import com.uinnova.product.eam.comm.model.VcDiagramDir;
import com.uinnova.product.eam.comm.model.VcDiagramGroup;
import com.uinnova.product.eam.comm.model.VcDiagramTag;
import com.uinnova.product.eam.comm.model.VcGroup;
import com.uinnova.product.eam.comm.model.VcGroupUser;
import com.uinnova.product.eam.comm.model.VcTag;

public class MigrateDiagram implements Serializable{
	
	private static final long serialVersionUID = 1L;

	private List<VcDiagram> diagrams;
	
	private List<MigrateDiagramEleWarp> diagramEles;

	private List<DcCombDiagram> combDiagrams;
	
	private List<VcDiagramDir> diagramDirs;
	
	private List<VcGroup> groups;
	
	private List<VcGroupUser> groupUsers;
	
	private List<VcDiagramGroup> diagramGroups;
	
	private List<VcTag> tags;
	
	private List<VcDiagramTag> diagramTags;
	
	public List<VcTag> getTags() {
		return tags;
	}

	public void setTags(List<VcTag> tags) {
		this.tags = tags;
	}

	public List<VcDiagramTag> getDiagramTags() {
		return diagramTags;
	}

	public void setDiagramTags(List<VcDiagramTag> diagramTags) {
		this.diagramTags = diagramTags;
	}

	public List<VcGroup> getGroups() {
		return groups;
	}

	public void setGroups(List<VcGroup> groups) {
		this.groups = groups;
	}

	public List<VcGroupUser> getGroupUsers() {
		return groupUsers;
	}

	public void setGroupUsers(List<VcGroupUser> groupUsers) {
		this.groupUsers = groupUsers;
	}

	public List<VcDiagramGroup> getDiagramGroups() {
		return diagramGroups;
	}

	public void setDiagramGroups(List<VcDiagramGroup> diagramGroups) {
		this.diagramGroups = diagramGroups;
	}

	public List<VcDiagram> getDiagrams() {
		return diagrams;
	}

	public void setDiagrams(List<VcDiagram> diagrams) {
		this.diagrams = diagrams;
	}
	
	public List<VcDiagramDir> getDiagramDirs() {
		return diagramDirs;
	}

	public void setDiagramDirs(List<VcDiagramDir> diagramDirs) {
		this.diagramDirs = diagramDirs;
	}

	public List<MigrateDiagramEleWarp> getDiagramEles() {
		return diagramEles;
	}

	public void setDiagramEles(List<MigrateDiagramEleWarp> diagramEles) {
		this.diagramEles = diagramEles;
	}

	public List<DcCombDiagram> getCombDiagrams() {
		return combDiagrams;
	}

	public void setCombDiagrams(List<DcCombDiagram> combDiagrams) {
		this.combDiagrams = combDiagrams;
	}

	
}
