<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:flowable="http://flowable.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://www.flowable.org/processdef">
  <process id="xw_it_technical_approve" name="新网it方案审批" isExecutable="true">
    <documentation>新网it方案审批</documentation>
    <startEvent id="startEvent1" flowable:formFieldValidation="true"></startEvent>
    <userTask id="sid-F4312AC7-C91A-4DB6-A816-0DC1BB3FB66A" name="提交人（处理）" flowable:assignee="$INITIATOR" flowable:category="submit" flowable:formFieldValidation="true">
      <documentation>rectification</documentation>
      <extensionElements>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[true]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <userTask id="OrganizationalStructureReviewTask" name="组织架构（审批）" flowable:assignee="${assignee}" flowable:formFieldValidation="true">
      <extensionElements>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
      <multiInstanceLoopCharacteristics isSequential="false" flowable:collection="assigneeList" flowable:elementVariable="assignee">
        <completionCondition>${multiInstanceCompleteExecution.executeByOneUserConditionImmediately(execution)}</completionCondition>
      </multiInstanceLoopCharacteristics>
    </userTask>
    <exclusiveGateway id="sid-D77B83C6-FBA4-44DA-B95F-0D366F41C68C"></exclusiveGateway>
    <userTask id="PublishLocationDirectoryTask" name="发布位置目录

owner（审批）" flowable:assignee="${assignee}" flowable:formFieldValidation="true">
      <extensionElements>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
      <multiInstanceLoopCharacteristics isSequential="false" flowable:collection="assigneeList" flowable:elementVariable="assignee">
        <completionCondition>${multiInstanceCompleteExecution.executeByOneUserConditionImmediately(execution)}</completionCondition>
      </multiInstanceLoopCharacteristics>
    </userTask>
    <endEvent id="sid-8FE8A585-4DE3-4937-884E-23F8949FE37D"></endEvent>
    <exclusiveGateway id="sid-FD9ED80B-1E5C-4CCA-AE0B-C27ED93A63DA"></exclusiveGateway>
    <sequenceFlow id="sid-995B7955-5CC4-4AD1-9B98-4C48136790BD" sourceRef="PublishLocationDirectoryTask" targetRef="sid-FD9ED80B-1E5C-4CCA-AE0B-C27ED93A63DA"></sequenceFlow>
    <serviceTask id="sid-23AB9801-0C09-4F63-89B6-BD2B24BE634F" name="相关评审者通过通知" flowable:class="com.uinnova.product.eam.workable.config.CallExternalSystemDelegate"></serviceTask>
    <sequenceFlow id="sid-7295F973-9C96-4A1F-B6CA-70E318420C3E" sourceRef="sid-23AB9801-0C09-4F63-89B6-BD2B24BE634F" targetRef="sid-8FE8A585-4DE3-4937-884E-23F8949FE37D"></sequenceFlow>
    <sequenceFlow id="sid-3D87F241-4A5A-4547-9EC4-8FE44EDE1DFC" sourceRef="startEvent1" targetRef="sid-F4312AC7-C91A-4DB6-A816-0DC1BB3FB66A"></sequenceFlow>
    <exclusiveGateway id="sid-293BF6ED-1891-44C9-A44C-F59122559765"></exclusiveGateway>
    <sequenceFlow id="sid-811039FE-4D2A-4AE7-AE0E-95FBB4DE6FFD" sourceRef="sid-F4312AC7-C91A-4DB6-A816-0DC1BB3FB66A" targetRef="sid-293BF6ED-1891-44C9-A44C-F59122559765"></sequenceFlow>
    <sequenceFlow id="sid-1F377F3D-230B-4697-803F-F18EF4122D66" sourceRef="OrganizationalStructureReviewTask" targetRef="sid-D77B83C6-FBA4-44DA-B95F-0D366F41C68C"></sequenceFlow>
    <sequenceFlow id="sid-BECA36B7-5260-41B2-A2BC-0A55FDE843C5" sourceRef="sid-D77B83C6-FBA4-44DA-B95F-0D366F41C68C" targetRef="PublishLocationDirectoryTask">
      <documentation>PublishLocationDirectoryTask</documentation>
      <extensionElements>
        <flowable:executionListener event="end" delegateExpression="${publicAccessUserInfoListener}"></flowable:executionListener>
      </extensionElements>
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${goOut=='pass'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-DC81EB6D-EB03-4D04-8F43-3D6AEF6ACEF9" sourceRef="sid-293BF6ED-1891-44C9-A44C-F59122559765" targetRef="OrganizationalStructureReviewTask">
      <documentation>OrganizationalStructureReviewTask</documentation>
      <extensionElements>
        <flowable:executionListener event="end" delegateExpression="${publicAccessUserInfoListener}"></flowable:executionListener>
      </extensionElements>
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${pass=='pass'}]]></conditionExpression>
    </sequenceFlow>
    <endEvent id="sid-6F325672-50F7-4F18-9121-EEAD7C650F60">
      <terminateEventDefinition></terminateEventDefinition>
    </endEvent>
    <sequenceFlow id="sid-5F7F391C-81B5-40E7-9B24-2B02D2565D78" sourceRef="sid-D77B83C6-FBA4-44DA-B95F-0D366F41C68C" targetRef="sid-F4312AC7-C91A-4DB6-A816-0DC1BB3FB66A">
      <documentation>plan</documentation>
      <extensionElements>
        <flowable:executionListener event="end" delegateExpression="${approvalTaskListener}"></flowable:executionListener>
        <flowable:executionListener event="end" delegateExpression="${pushPlanNoticeListener}"></flowable:executionListener>
      </extensionElements>
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${goOut=='noPass'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-63380960-39C2-45B4-B100-999036A3B761" name="取消" sourceRef="sid-293BF6ED-1891-44C9-A44C-F59122559765" targetRef="sid-6F325672-50F7-4F18-9121-EEAD7C650F60">
      <documentation>plan</documentation>
      <extensionElements>
        <flowable:executionListener event="end" delegateExpression="${approvalTaskListener}"></flowable:executionListener>
        <flowable:executionListener event="end" delegateExpression="${pushPlanNoticeListener}"></flowable:executionListener>
      </extensionElements>
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${pass=='cancel'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-ED725FA2-CF5B-41B0-B330-D41B9E4C71C8" name="取消" sourceRef="sid-D77B83C6-FBA4-44DA-B95F-0D366F41C68C" targetRef="sid-6F325672-50F7-4F18-9121-EEAD7C650F60">
      <documentation>plan</documentation>
      <extensionElements>
        <flowable:executionListener event="end" delegateExpression="${approvalTaskListener}"></flowable:executionListener>
        <flowable:executionListener event="end" delegateExpression="${pushPlanNoticeListener}"></flowable:executionListener>
      </extensionElements>
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${pass=='cancel'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-CAE715DA-20B5-48AE-9B17-F243EF530D6C" name="取消" sourceRef="sid-FD9ED80B-1E5C-4CCA-AE0B-C27ED93A63DA" targetRef="sid-6F325672-50F7-4F18-9121-EEAD7C650F60">
      <documentation>plan</documentation>
      <extensionElements>
        <flowable:executionListener event="end" delegateExpression="${approvalTaskListener}"></flowable:executionListener>
        <flowable:executionListener event="end" delegateExpression="${pushPlanNoticeListener}"></flowable:executionListener>
      </extensionElements>
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${pass=='cancel'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-76D11121-C804-4284-B3EF-E0E845E4FD75" sourceRef="sid-FD9ED80B-1E5C-4CCA-AE0B-C27ED93A63DA" targetRef="sid-23AB9801-0C09-4F63-89B6-BD2B24BE634F">
      <documentation>plan</documentation>
      <extensionElements>
        <flowable:executionListener event="end" delegateExpression="${approvalTaskListener}"></flowable:executionListener>
        <flowable:executionListener event="end" delegateExpression="${pushPlanNoticeListener}"></flowable:executionListener>
      </extensionElements>
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${goOut=='pass'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-9DF17D70-8501-46E1-9DE9-FDA4C9FDF38A" name="审批未通过" sourceRef="sid-FD9ED80B-1E5C-4CCA-AE0B-C27ED93A63DA" targetRef="sid-F4312AC7-C91A-4DB6-A816-0DC1BB3FB66A">
      <documentation>plan</documentation>
      <extensionElements>
        <flowable:executionListener event="end" delegateExpression="${approvalTaskListener}"></flowable:executionListener>
        <flowable:executionListener event="end" delegateExpression="${pushPlanNoticeListener}"></flowable:executionListener>
      </extensionElements>
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${goOut=='noPass'}]]></conditionExpression>
    </sequenceFlow>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_xw_it_technical_approve">
    <bpmndi:BPMNPlane bpmnElement="xw_it_technical_approve" id="BPMNPlane_xw_it_technical_approve">
      <bpmndi:BPMNShape bpmnElement="startEvent1" id="BPMNShape_startEvent1">
        <omgdc:Bounds height="30.0" width="30.0" x="14.999999552965178" y="162.9999951422216"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-F4312AC7-C91A-4DB6-A816-0DC1BB3FB66A" id="BPMNShape_sid-F4312AC7-C91A-4DB6-A816-0DC1BB3FB66A">
        <omgdc:Bounds height="80.0" width="100.0" x="89.99999731779107" y="137.99999588727962"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="OrganizationalStructureReviewTask" id="BPMNShape_OrganizationalStructureReviewTask">
        <omgdc:Bounds height="80.0" width="100.0" x="329.99997049570254" y="137.99999588727962"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-D77B83C6-FBA4-44DA-B95F-0D366F41C68C" id="BPMNShape_sid-D77B83C6-FBA4-44DA-B95F-0D366F41C68C">
        <omgdc:Bounds height="40.0" width="40.0" x="510.0" y="158.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="PublishLocationDirectoryTask" id="BPMNShape_PublishLocationDirectoryTask">
        <omgdc:Bounds height="80.0" width="100.0" x="629.9999624490755" y="137.99999588727962"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-8FE8A585-4DE3-4937-884E-23F8949FE37D" id="BPMNShape_sid-8FE8A585-4DE3-4937-884E-23F8949FE37D">
        <omgdc:Bounds height="28.0" width="28.0" x="1004.9999700486669" y="163.99999511241927"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-FD9ED80B-1E5C-4CCA-AE0B-C27ED93A63DA" id="BPMNShape_sid-FD9ED80B-1E5C-4CCA-AE0B-C27ED93A63DA">
        <omgdc:Bounds height="40.0" width="40.0" x="775.0" y="158.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-23AB9801-0C09-4F63-89B6-BD2B24BE634F" id="BPMNShape_sid-23AB9801-0C09-4F63-89B6-BD2B24BE634F">
        <omgdc:Bounds height="80.0" width="100.0" x="859.666552523781" y="137.99999588727962"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-293BF6ED-1891-44C9-A44C-F59122559765" id="BPMNShape_sid-293BF6ED-1891-44C9-A44C-F59122559765">
        <omgdc:Bounds height="40.0" width="40.00000000000003" x="239.99999284744283" y="157.9999952912332"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-6F325672-50F7-4F18-9121-EEAD7C650F60" id="BPMNShape_sid-6F325672-50F7-4F18-9121-EEAD7C650F60">
        <omgdc:Bounds height="27.999999999999996" width="28.0" x="515.9999846220021" y="9.999998688697904"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="sid-3D87F241-4A5A-4547-9EC4-8FE44EDE1DFC" id="BPMNEdge_sid-3D87F241-4A5A-4547-9EC4-8FE44EDE1DFC">
        <omgdi:waypoint x="44.949994646096464" y="177.9999953248219"></omgdi:waypoint>
        <omgdi:waypoint x="89.99999559967253" y="177.99999587506554"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-9DF17D70-8501-46E1-9DE9-FDA4C9FDF38A" id="BPMNEdge_sid-9DF17D70-8501-46E1-9DE9-FDA4C9FDF38A">
        <omgdi:waypoint x="795.5" y="197.44058151093444"></omgdi:waypoint>
        <omgdi:waypoint x="795.5" y="279.0"></omgdi:waypoint>
        <omgdi:waypoint x="116.66665613651278" y="279.0"></omgdi:waypoint>
        <omgdi:waypoint x="115.0255348302549" y="217.94999588727964"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-CAE715DA-20B5-48AE-9B17-F243EF530D6C" id="BPMNEdge_sid-CAE715DA-20B5-48AE-9B17-F243EF530D6C">
        <omgdi:waypoint x="795.5" y="158.5"></omgdi:waypoint>
        <omgdi:waypoint x="795.5" y="23.9999986886979"></omgdi:waypoint>
        <omgdi:waypoint x="543.9499053249788" y="23.9999986886979"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-7295F973-9C96-4A1F-B6CA-70E318420C3E" id="BPMNEdge_sid-7295F973-9C96-4A1F-B6CA-70E318420C3E">
        <omgdi:waypoint x="959.6165464368261" y="177.99999553292304"></omgdi:waypoint>
        <omgdi:waypoint x="1004.9999700486669" y="177.99999521128476"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-995B7955-5CC4-4AD1-9B98-4C48136790BD" id="BPMNEdge_sid-995B7955-5CC4-4AD1-9B98-4C48136790BD">
        <omgdi:waypoint x="729.949962449073" y="178.21623136182976"></omgdi:waypoint>
        <omgdi:waypoint x="775.4130427882876" y="178.41304278828943"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-1F377F3D-230B-4697-803F-F18EF4122D66" id="BPMNEdge_sid-1F377F3D-230B-4697-803F-F18EF4122D66">
        <omgdi:waypoint x="429.9499704956992" y="178.16594406358658"></omgdi:waypoint>
        <omgdi:waypoint x="510.43333279625585" y="178.43333279625588"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-76D11121-C804-4284-B3EF-E0E845E4FD75" id="BPMNEdge_sid-76D11121-C804-4284-B3EF-E0E845E4FD75">
        <omgdi:waypoint x="814.5249568133617" y="178.41642151297376"></omgdi:waypoint>
        <omgdi:waypoint x="859.666552523781" y="178.21875702947298"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-BECA36B7-5260-41B2-A2BC-0A55FDE843C5" id="BPMNEdge_sid-BECA36B7-5260-41B2-A2BC-0A55FDE843C5">
        <omgdi:waypoint x="549.5071936976456" y="178.436241068469"></omgdi:waypoint>
        <omgdi:waypoint x="629.9999624490691" y="178.16705415954434"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-DC81EB6D-EB03-4D04-8F43-3D6AEF6ACEF9" id="BPMNEdge_sid-DC81EB6D-EB03-4D04-8F43-3D6AEF6ACEF9">
        <omgdi:waypoint x="279.94887770399936" y="177.99999588727962"></omgdi:waypoint>
        <omgdi:waypoint x="329.9999704956781" y="177.99999588727962"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-5F7F391C-81B5-40E7-9B24-2B02D2565D78" id="BPMNEdge_sid-5F7F391C-81B5-40E7-9B24-2B02D2565D78">
        <omgdi:waypoint x="530.5" y="197.43786811779773"></omgdi:waypoint>
        <omgdi:waypoint x="530.5" y="256.5"></omgdi:waypoint>
        <omgdi:waypoint x="139.99999731779107" y="256.5"></omgdi:waypoint>
        <omgdi:waypoint x="139.99999731779107" y="217.94999588727964"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-811039FE-4D2A-4AE7-AE0E-95FBB4DE6FFD" id="BPMNEdge_sid-811039FE-4D2A-4AE7-AE0E-95FBB4DE6FFD">
        <omgdi:waypoint x="189.94999669440108" y="177.99999563892692"></omgdi:waypoint>
        <omgdi:waypoint x="239.9999929467839" y="177.99999539032586"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-ED725FA2-CF5B-41B0-B330-D41B9E4C71C8" id="BPMNEdge_sid-ED725FA2-CF5B-41B0-B330-D41B9E4C71C8">
        <omgdi:waypoint x="530.4350629319908" y="158.43506293199087"></omgdi:waypoint>
        <omgdi:waypoint x="530.0451313567676" y="37.94971129272772"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-63380960-39C2-45B4-B100-999036A3B761" id="BPMNEdge_sid-63380960-39C2-45B4-B100-999036A3B761">
        <omgdi:waypoint x="260.49999284744285" y="158.4999952912332"></omgdi:waypoint>
        <omgdi:waypoint x="260.49999284744285" y="23.9999986886979"></omgdi:waypoint>
        <omgdi:waypoint x="515.9999846220021" y="23.9999986886979"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>