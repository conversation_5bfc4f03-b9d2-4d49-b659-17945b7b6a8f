package com.uino.provider.feign.sys;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.binary.jdbc.Page;
import com.uino.bean.sys.base.ESOperateLog;
import com.uino.bean.sys.query.ESOperateLogSearchBean;
import com.uino.provider.feign.config.BaseFeignConfig;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 
 * <AUTHOR>
 *
 */
@Component
@FeignClient(name = "${feign-server-name:tarsier-platform}", path = "${feign-server-path:tarsier-platform}/feign/sys/opLog", configuration = {BaseFeignConfig.class})
public interface OperateLogFeign {

    /**
     * 条件查询系统操作日志
     * 
     * @param bean
     * @return
     */
    @PostMapping("getOperateLogPageByCdt")
    Page<ESOperateLog> getOperateLogPageByCdt(@RequestBody ESOperateLogSearchBean bean);

    /**
     * 保存系统操作日志
     * 
     * @param log
     * @return
     */
    @PostMapping("saveOrUpdate")
    Long saveOrUpdate(@RequestBody ESOperateLog log);

    /**
     * 按保留时长清除系统操作日志
     * 
     * @param clearLogDuration
     * @return
     */
    @PostMapping("clearOperateLogByDuration")
    Integer clearOperateLogByDuration(@RequestBody Integer clearLogDuration);
}
