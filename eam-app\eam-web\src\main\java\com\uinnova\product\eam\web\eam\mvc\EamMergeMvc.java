package com.uinnova.product.eam.web.eam.mvc;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.binary.core.util.BinaryUtils;
import com.binary.framework.web.RemoteResult;
import com.uinnova.product.eam.base.exception.ServerException;
import com.uinnova.product.eam.comm.model.es.EamMatrixInstance;
import com.uinnova.product.eam.model.EamCategoryCdt;
import com.uinnova.product.eam.model.diagram.EamPathResultDTO;
import com.uinnova.product.eam.model.enums.AssetType;
import com.uinnova.product.eam.service.EamCategorySvc;
import com.uinnova.product.eam.service.merge.EamMergeSvc;
import com.uinnova.product.eam.service.fx.GeneralPullSvc;
import com.uinnova.product.eam.service.fx.GeneralPushSvc;
import com.uinnova.product.eam.web.bm.bean.ReleaseModuleDiagramDTO;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uino.bean.cmdb.base.LibType;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 资产仓库目录&文件夹
 * <AUTHOR>
 */
@RestController
@RequestMapping("/merge")
public class EamMergeMvc {

    @Resource
    private EamMergeSvc mergeSvc;

    @Resource
    private EamCategorySvc categorySvc;

    @Resource
    private GeneralPushSvc generalPushSvc;

    @Resource
    private GeneralPullSvc generalPullSvc;

    @PostMapping("/push")
    @ModDesc(desc = "业务建模目录/视图发布", pDesc = "目录id/视图id", rDesc = "发布结果", rType = RemoteResult.class)
    public RemoteResult push(@RequestBody JSONObject body) {
        List<Long> dirIds = null;
        JSONArray dirIdJson = body.getJSONArray("dirIds");
        if(!BinaryUtils.isEmpty(dirIdJson)){
            dirIds = dirIdJson.toJavaList(Long.class);
        }
        String diagramId = body.getString("diagramId");
        if(BinaryUtils.isEmpty(dirIds) && BinaryUtils.isEmpty(diagramId)){
            throw new ServerException("参数不可为空!");
        }
        Long parentId = body.getLong("parentId");
        if (BinaryUtils.isEmpty(parentId)) {
            throw new ServerException("发布位置参数不能为空!");
        }
        String desc = body.getString("desc");
        mergeSvc.modelPush(dirIds, diagramId, desc, parentId);
        return new RemoteResult(1);
    }

    @PostMapping("/pull")
    @ModDesc(desc = "业务建模目录/视图检出", pDesc = "目录id/视图id", rDesc = "检出结果", rType = RemoteResult.class)
    public RemoteResult pull(@RequestBody ReleaseModuleDiagramDTO diagramDTO) {
        Long dirId = diagramDTO.getDirId();
        String diagramId = diagramDTO.getDiagramId();
        Long targetDirId = diagramDTO.getTargetDirId();
        Integer type = diagramDTO.getType();
        Integer actionType = diagramDTO.getActionType();

        if (BinaryUtils.isEmpty(type)) {
            throw new ServerException("检出类型参数不能为空!");
        }
        if (BinaryUtils.isEmpty(targetDirId)) {
            throw new ServerException("发布位置不能为空!");
        }
        if(BinaryUtils.isEmpty(dirId) && BinaryUtils.isEmpty(diagramId)){
            throw new ServerException("参数不可为空!");
        }
        if (type == AssetType.DIAGRAM.assetType) {
            if (BinaryUtils.isEmpty(actionType)) {
                throw new ServerException("视图检出方式不能为空!");
            }
            String diagramName = diagramDTO.getDiagramName();
            String localDiagramId = generalPullSvc.generalCheckOutDiagram(diagramId, targetDirId, actionType, diagramName);
            return new RemoteResult(localDiagramId);
        } else {
            mergeSvc.modelPull(dirId, diagramId, targetDirId, LibType.DESIGN);
            return new RemoteResult(1);
        }
    }

    @PostMapping("/queryPushPath")
    @ModDesc(desc = "查询已发布的视图or模型位置", pDesc = "模型id/视图id", rDesc = "发布位置", rType = RemoteResult.class)
    public RemoteResult queryPushPath(@RequestBody EamCategoryCdt cdt) {
        EamPathResultDTO result = categorySvc.queryPushPath(cdt);
        return new RemoteResult(result);
    }

    @PostMapping("/queryPullPath")
    @ModDesc(desc = "查询检出矩阵位置", pDesc = "矩阵id/视图id", rDesc = "发布位置", rType = RemoteResult.class)
    public RemoteResult queryPullPath(@RequestBody EamCategoryCdt cdt) {
        EamPathResultDTO result = categorySvc.queryPullPath(cdt);
        return new RemoteResult(result);
    }

    @PostMapping("/pushCkeck")
    @ModDesc(desc = "视图模型发布校验冲突", pDesc = "模型ids/视图ids", rDesc = "校验结果", rType = RemoteResult.class)
    public RemoteResult pushCkeck(@RequestBody EamCategoryCdt cdt) {
        Map<Integer, Object> result = generalPushSvc.pushCkeck(cdt.getDirIds(), cdt.getDiagramIds());
        return new RemoteResult(result);
    }

    @PostMapping("/matrix/push")
    @ModDesc(desc = "矩阵表格发布", pDesc = "矩阵表格id", rDesc = "发布结果", rType = RemoteResult.class)
    public RemoteResult matrixPush(@RequestBody EamMatrixInstance vo) {
        BinaryUtils.checkEmpty(vo.getId(), "矩阵表格id");
        BinaryUtils.checkEmpty(vo.getDirId(), "发布位置");
        BinaryUtils.checkEmpty(vo.getDescription(), "发布说明");
        mergeSvc.matrixPush(vo.getId(), vo.getDirId(), vo.getDescription());
        return new RemoteResult(1);
    }

    @PostMapping("/matrix/pull")
    @ModDesc(desc = "矩阵表格检出", pDesc = "矩阵表格id", rDesc = "检出结果", rType = RemoteResult.class)
    public RemoteResult matrixPull(@RequestBody EamMatrixInstance vo) {
        BinaryUtils.checkEmpty(vo.getId(), "矩阵表格id");
        BinaryUtils.checkEmpty(vo.getDirId(), "检出位置");
        mergeSvc.matrixPull(vo.getId(), vo.getDirId());
        return new RemoteResult(1);
    }

    @PostMapping("/checkByType")
    @ModDesc(desc = "根据类型校验视图信息", pDesc = "模型ids/视图ids", rDesc = "校验结果", rType = RemoteResult.class)
    public RemoteResult checkByType(@RequestBody EamCategoryCdt cdt) {
        Map<Integer, Object> result = generalPushSvc.checkByType(cdt.getDirIds(), cdt.getDiagramIds(), cdt.getCheckType());
        return new RemoteResult(result);
    }

}
