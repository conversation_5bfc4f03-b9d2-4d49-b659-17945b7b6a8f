package com.uinnova.product.eam.model.cj.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * 发布视图请求参数
 *
 * <AUTHOR>
 */
@Data
public class DiagramReleaseRequest {

    /**
     * 视图加密Id
     */
    @NotBlank(message = "视图Id不能为空")
    @JsonProperty("diagramId")
    private String dEnergy;

    /**
     * 视图加密Id
     */
    @NotBlank(message = "发布说明不能为空")
    private String releaseDesc;

    /**
     * 视图加密Id
     */
    private Long dirId;
}
