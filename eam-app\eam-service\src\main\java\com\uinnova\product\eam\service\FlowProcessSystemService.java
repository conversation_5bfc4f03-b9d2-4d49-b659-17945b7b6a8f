package com.uinnova.product.eam.service;

import com.binary.jdbc.Page;
import com.uinnova.product.eam.comm.model.es.*;
import com.uinnova.product.eam.feign.workable.entity.TaskResponse;
import com.uinnova.product.eam.model.*;
import com.uinnova.product.eam.model.dto.FlowSystemAssociatedFeaturesDto;
import com.uinnova.product.eam.model.dto.FlowSystemFileDto;
import com.uinnova.product.eam.model.vo.FileDocVo;
import com.uinnova.product.eam.model.vo.KcpInfoVo;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.base.LibType;

import jakarta.servlet.http.HttpServletResponse;
import java.io.File;
import java.util.*;

/**
 * 流程管理/流程体系服务层
 *
 * <AUTHOR>
 * @since 2024/5/24 11:28
 */
public interface FlowProcessSystemService {

    Long createFlowSystem(FlowProcessSystemDto flowProcessSystemDto);

    Collection<FlowProcessSystemTreeDto> getFlowSystemTreeNew(Boolean needCiInfo,Boolean needFlow);

    FlowSnapTreeDto getSingleTreeByProcessCiId(Long ciId);

    Map<String, Object> getFlowSystemAssociatedFeatures(String ciCode, String classCode, LibType libType);

    void deleteFlowSystemAssociatedFeatures(Long associatedFeatureId);

    FlowSystemAssociatedFeaturesDto addFlowSystemAssociatedFeatures(FlowSystemAssociatedFeatures flowProcessSystem);

    void batchAddFlowSystemAssociatedFeatures(FlowSystemAssociatedFeaturesDto flowProcessSystemDto);

    Long saveOrUpdateFlowSystemFile(FlowSystemFileDto flowSystemFileDto);

    FlowSystemFile getFlowSystemFile(String ciCode, LibType libType);

    HashMap<String, Object> checkFlowProcessSystem(String ciCode);

    /**
     * @param ciCode
     * @param loginCode
     * @param publishReason
     * @param publishType
     * @param flowSystemApproveDataId 来源是发布的时候不能为空
     * @return
     */
    Long publishFlowProcessSystem(String ciCode, String loginCode, String publishReason, String publishType, Long flowSystemApproveDataId);


    Map<String, Object> getRedirectDiagramId(String diagramId, String diagramClassType, Boolean alwaysCheck);

    List<FlowProcessSystemPublishHistoryDto> getFlowSystemPublishHistory(String ciCode, String publishType);

    /**
     * 查询kcp
     *
     * @param ciCode
     * @param sign   1:本层级查询kcp  2:L1,L2,L3查询kcp
     * @return
     */
    List<KcpInfoVo> findKcpInfoList(String ciCode, Integer sign, LibType libType);

    /**
     * 文件清单列表
     *
     * @param ciCode
     * @return
     */
    List<FileDocVo> findFileDocList(String ciCode, String publishType);

    /**
     * 查询版本管理列表
     *
     * @param ciCode
     * @return
     */
    List<FlowProcessSystemPublishHistoryDto> findOperationList(String ciCode, String publishType);

    CcCiInfo createProcessPerformance(ProcessPerformanceDto ccCiInfo, String classCode);

    HashMap<String, Object> getAutoDiagramByParentCiCode(Long parentCiId);

    void delFlowProcessSystem(String ciCode);

    Map<String, Object> checkFlowDiagramVersion(String ciCode, String diagramEnergy, String diagramClassType);

    Map<String, Object> mergePullFlowDiagram(String ciCode, String diagramClassType);

    Map<String, Object> checkFlowCiVersion(String ciCode);

    String mergePullFlowCi(String ciCode, Boolean allUser);


    List<Map<String, Object>> getEndProcessTable(String ciCode, LibType libType);

    void batchSaveOrUpdateFlowDiagramNode(List<FlowProcessTableDto> flowProcessTableDtoList);

    Map<String, Object> getLevelFormsList(LevelFormsDto bean);

    void moveFlowProcess(FlowProcessSystemDto flowProcessSystemDto);

    void renameFlowProcess(FlowReNameDto flowReNameDto);

    Set<FlowProcessSystemTreeDto> getMoveFlowSystem(String ciCode);

    Collection<FlowProcessSystemTreeDto> getMoveFlowSystemNew(String ciCode);

    List<Map<String, Object>> getFlowRoleTree(String roleName);

    List<String> batchSaveSceneActiveRlt(SceneActiveRltDto sceneActiveRltDto);

    Map<String, Object> getSceneActiveRltRltByFlowCiCodeWithLibType(String ciCode, LibType libType);

    List<Map<String, Object>> getSceneActiveRltByFlowCiCode(String ciCode, LibType libType);

    List<ESCIInfo> getFlowDiagramLocalActiveList(String ciCode, LibType libType);

    /**
     * 流程体系末级流程发布
     *
     * @param processApprovalDto
     */
    TaskResponse processLaunch(ProcessLaunchDto processApprovalDto);

    /**
     * 流程体系末级流程审批
     *
     * @param processApprovalDto
     */
    void processApproval(ProcessApprovalDto processApprovalDto);

    Page<CcCiInfo> findSingFlowList(Long ciId, Integer pageNum, Integer pageSize, String word);

    Map<String, List<CcCiInfo>> getFlowUserRoleAndPosition();

    Map<String, Collection<CcCiInfo>> getFlowAssertCount();

    FlowWholeSceneCountVo getFlowWholeSceneCountInfo();

    /**
     * 获取所有无流程图的末级流程
     *
     * @return
     */
    Collection<FlowProcessSystemTreeDto> getAllFlowWhereNoDiagram();

    /**
     * 复用末级流程图
     *
     * @return
     */
    Map<String, String> copyDiagramByFlowChart(FlowDiagramReuseDto flowDiagramReuseDto);

    /**
     * 获取顶级流程组
     * @return
     */
    Collection<FlowProcessSystemTreeDto> getTopTierProcesses();

    TaskResponse flowSingApproval(FlowSignProcessVo flowSignPricessVo);

    List<FlowSystemProcessSingDataVo> getFlowSignCiList(String businessKey);

    FlowSystemApproveData getFlowSystemApproveDataByProcessInstanceIdId(String processInstanceId);

    FlowSystemApproveData getFlowSystemApproveDataById(Long flowSystemApproveId);

    Boolean carryOutFlow(SignFlowActionVo signFlowActionVo);

    void changeFlowStatus(ProcessApprovalChangeDto processApprovalChangeDto);

    CcCiInfo getApproveCiInfoByProcessesInstanceId(String processInstanceId);

    TaskResponse reSubmitRejectApproveFlow(String businessKey);

    void abolishFlow(String ciCode, Boolean upVersion);

    Boolean stopFlowSystemApprove(String processInstanceId, String deleteReason);

    void withdrawFlowSystemApprove(String businessKey);

    Map<String, Map> getFlowSystemAssociatedFeaturesNew(String diagramIds, String ciCode, LibType libType);

    Map<String, Map> getFlowFileNew(String diagramIds, String ciCode, LibType libType);

    Map<String,Object> obtainTheProcessFile(String ciCode, LibType libType);

    String exportProcessFile(HttpServletResponse response, String ciCode, String diagramIds,String id, LibType libType);

    Set<String> whetherDataIsChanged(String ciCode, String classCode);

    Long mergePullAssociationCi(String ciCode,String classCode);

    Long addMetricMonitoringInformation(List<IndicatorDetectionInformationAssociation> dto);

    Map<String, Object> queryIndicatorMonitoringInformation(String ciCode);

    File exportFlowSystemExcel(List<String> targetCiCode);

	String exportFlowManual(HttpServletResponse response, String ciCode,String base64File);

	List<FlowProcessSystemTreeDto> getAllFlowWhereRunSituation(String sourceId);

    Long processFileAddData(Long id);

    Map<String,Integer> queryProcessQuantity();
}
