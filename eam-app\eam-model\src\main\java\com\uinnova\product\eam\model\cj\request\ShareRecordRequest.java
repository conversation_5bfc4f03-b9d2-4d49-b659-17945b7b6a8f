package com.uinnova.product.eam.model.cj.request;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.base.diagram.model.ShareRecordQueryBean;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 分享查询请求参数
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ShareRecordRequest extends ShareRecordQueryBean {

    @Comment("是否查询视图")
    private Boolean queryDiagram;

    @Comment("是否查询方案")
    private Boolean queryPlan;
}
