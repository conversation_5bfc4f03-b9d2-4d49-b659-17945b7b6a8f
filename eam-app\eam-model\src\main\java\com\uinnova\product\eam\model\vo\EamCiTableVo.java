package com.uinnova.product.eam.model.vo;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.io.Serializable;

/**
 * ci表格vo
 * <AUTHOR>
 */
@Data
public class EamCiTableVo implements Serializable {

    @Comment("属性字段名")
    private String proName;

    @Comment("字段标识")
    private String attrKey;

    @Comment("header列名")
    private String attrName;

    @Comment("分类id")
    private Long classId;

    @Comment("分类名称")
    private String className;

    @Comment("列是否可合并")
    private Boolean merge = false;

    public EamCiTableVo() {
    }

    public EamCiTableVo(String proName, String attrName, Long classId, String className) {
        this.proName = proName;
        if(classId == null){
            this.attrKey = proName;
        }else{
            this.attrKey = proName + "_" + classId;
        }
        this.attrName = attrName;
        this.classId = classId;
        this.className = className;
    }

    public EamCiTableVo(String proName, String attrName, Long classId, String className, Boolean merge) {
        this.proName = proName;
        if(classId == null){
            this.attrKey = proName;
        }else{
            this.attrKey = proName + "_" + classId;
        }
        this.attrName = attrName;
        this.classId = classId;
        this.className = className;
        this.merge = merge;
    }
}
