package com.uinnova.product.eam.db.impl;


import com.binary.framework.dao.DaoDefinition;

import com.uinnova.product.eam.comm.model.CVcDiagramOperationRecord;
import com.uinnova.product.eam.comm.model.VcDiagramOperationRecord;


/**
 * 视图相关操作记录表[VC_DIAGRAM_OPERATION_RECORD]数据访问对象定义实现
 */
public class VcDiagramOperationRecordDaoDefinition implements DaoDefinition<VcDiagramOperationRecord, CVcDiagramOperationRecord> {


	@Override
	public Class<VcDiagramOperationRecord> getEntityClass() {
		return VcDiagramOperationRecord.class;
	}


	@Override
	public Class<CVcDiagramOperationRecord> getConditionClass() {
		return CVcDiagramOperationRecord.class;
	}


	@Override
	public String getTableName() {
		return "VC_DIAGRAM_OPERATION_RECORD";
	}


	@Override
	public boolean hasDataStatusField() {
		return false;
	}


	@Override
	public void setDataStatusValue(VcDiagramOperationRecord record, int status) {
	}


	@Override
	public void setDataStatusValue(CVcDiagramOperationRecord cdt, int status) {
	}


	@Override
	public void setCreatorValue(VcDiagramOperationRecord record, String creator) {
		record.setCreator(creator);
	}


	@Override
	public void setModifierValue(VcDiagramOperationRecord record, String modifier) {
		record.setModifier(modifier);
	}


}


