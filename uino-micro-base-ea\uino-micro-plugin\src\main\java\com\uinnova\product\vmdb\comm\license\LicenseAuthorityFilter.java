package com.uinnova.product.vmdb.comm.license;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

import jakarta.servlet.Filter;
import jakarta.servlet.FilterChain;
import jakarta.servlet.FilterConfig;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import com.binary.core.io.Resource;
import com.binary.core.io.ResourceResolver;
import com.binary.core.util.BinaryUtils;
import com.binary.core.util.Properties;
import com.binary.core.util.WildcardPatternBuilder;
import com.binary.framework.exception.ServiceException;
import com.binary.framework.util.ControllerUtils;
import com.binary.framework.web.RemoteResult;
import com.uinnova.product.vmdb.comm.integration.SpringContextAware;
import com.uinnova.product.vmdb.comm.util.SystemUtil;

/**
 *
 * <AUTHOR>
 *
 */
public class LicenseAuthorityFilter implements Filter {

    /**
     * 不拦截的路径正则表达式, 多个值以分号[;]分隔 , 不需要包含Context
     */
    private Pattern ignoreFilterPattern;

    protected static final String SERVER_STATUS="ServerStatus";

    /** license过期状态码 **/
    public static final int licenseInvalidCode = 402;

    private LicenseProxy licenseProxy;

    /** 注册license地址 **/
    private String registerLicenseUrl;

    private String[] splitStringPattern(String strFilterPattern) {
        char m = ';';
        if (strFilterPattern.indexOf(m) > 0) {
            String[] arr = strFilterPattern.split("[;]");
            List<String> ls = new ArrayList<String>();
            for (int i = 0; i < arr.length; i++) {
                String s = arr[i].trim();
                if (s.length() > 0) {
                    ls.add(s);
                }
            }
            return ls.toArray(new String[0]);
        } else {
            return new String[] {strFilterPattern };
        }
    }

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        String strIgnoreFilterPattern = filterConfig.getInitParameter("ignore-filter-pattern");
        if (!BinaryUtils.isEmpty(strIgnoreFilterPattern)) {
            this.ignoreFilterPattern = WildcardPatternBuilder.build(splitStringPattern(strIgnoreFilterPattern.toUpperCase()));
        }

        String registerLicenseUrl = filterConfig.getInitParameter("registerLicenseUrl");
        String registerLicensePropertyName = filterConfig.getInitParameter("registerLicensePropertyName");

        if (BinaryUtils.isEmpty(registerLicenseUrl)) {
            if (!BinaryUtils.isEmpty(registerLicensePropertyName)) {
                Resource res = ResourceResolver.getResource("classpath:application-minor.properties");
                Properties props = new Properties(res);
                String s = props.get(registerLicensePropertyName.trim());
                if (s != null) {
                    this.registerLicenseUrl = s.trim();
                }
            }
        } else {
            this.registerLicenseUrl = registerLicenseUrl.trim();
        }

        if (!BinaryUtils.isEmpty(this.registerLicenseUrl)) {
            String http = "http://";
            String https = "https://";
            if (!this.registerLicenseUrl.startsWith(http) && !this.registerLicenseUrl.startsWith(https)) {
                this.registerLicenseUrl = ControllerUtils.formatContextPath(this.registerLicenseUrl);
            }
        }
    }

    protected LicenseProxy getLicenseProxy() {
        if (this.licenseProxy == null) {
            this.licenseProxy = SpringContextAware.getSpringContext().getBean(LicenseProxy.class);
        }
        return this.licenseProxy;
    }

    protected License getLicense() {
        return getLicenseProxy().getLicense();
    }

    public boolean isIgnoreRequest(HttpServletRequest request) {
        String contextpath = request.getContextPath();
        String url = request.getRequestURI();
        String path = url.substring(contextpath.length()).toUpperCase();
        if (path.length() == 0) {
            path = "/";
        }

        if (this.ignoreFilterPattern != null && ignoreFilterPattern.matcher(path).matches()) {
            return true;
        }
        return false;
    }

    @Override
    public void doFilter(ServletRequest req, ServletResponse resp, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest request = (HttpServletRequest) req;
        HttpServletResponse response = (HttpServletResponse) resp;

        request.setAttribute(LicenseAuthorityFilter.class.getName(), this);

        // 如里不需要过滤
        if (isIgnoreRequest(request)) {
            chain.doFilter(req, resp);
            return;
        }

        License license = getLicense();
        if (!SystemUtil.isLicenseValid(license)) {
            boolean isajax = request.getHeader("REQUEST_HEADER") != null;
            if (isajax) {
                processError4Ajax(request, response);
            } else {
                processError4Forward(request, response);
            }
            return;
        }
        chain.doFilter(request, response);
    }

    /**
     * 处理forward异常
     *
     * @param request
     * @param response
     */
    protected void processError4Forward(HttpServletRequest request, HttpServletResponse response) {
        if (BinaryUtils.isEmpty(this.registerLicenseUrl)) {
            throw new ServiceException(" not setting register license url! ");
        }
        char c = '/';
        if (this.registerLicenseUrl.charAt(0) == c && !this.registerLicenseUrl.startsWith(request.getContextPath())) {
            this.registerLicenseUrl = request.getContextPath() + this.registerLicenseUrl;
        }
        try {
            String url = this.registerLicenseUrl;
            response.sendRedirect(url);
        } catch (IOException e) {
            // do nothing
        }
    }

    /**
     * 处理ajax异常
     *
     * @param request
     * @param response
     */
    protected void processError4Ajax(HttpServletRequest request, HttpServletResponse response) {
    	response.setHeader(SERVER_STATUS, String.valueOf(licenseInvalidCode));
        //response.setStatus(licenseInvalidCode);
        RemoteResult rs = new RemoteResult(false, licenseInvalidCode, "Authorized license is invalid!");
        ControllerUtils.returnSimpleJson(request, response, rs);
    }

    @Override
    public void destroy() {
    }

}
