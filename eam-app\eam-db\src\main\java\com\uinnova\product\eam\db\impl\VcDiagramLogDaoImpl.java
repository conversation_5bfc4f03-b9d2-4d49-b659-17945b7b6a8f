package com.uinnova.product.eam.db.impl;


import com.uinnova.product.eam.comm.model.CVcDiagramLog;
import com.uinnova.product.eam.comm.model.VcDiagramLog;
import com.uinnova.product.eam.db.VcDiagramLogDao;
import com.uinnova.product.eam.db.support.dao.mybatis.ComMyBatisBinaryDaoImpl;


/**
 * 视图操作记录表[VC_DIAGRAM_LOG]数据访问对象实现
 */
public class VcDiagramLogDaoImpl extends ComMyBatisBinaryDaoImpl<VcDiagramLog, CVcDiagramLog> implements VcDiagramLogDao {

//    @Override
//    public String getTableName() {
//        return "IAMS." + getDaoDefinition().getTableName();
//    }
}


