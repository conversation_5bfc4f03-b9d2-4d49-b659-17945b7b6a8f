package com.uinnova.product.eam.web.cj;

import com.uinnova.product.eam.base.cj.ResultMsg;
import com.uinnova.product.eam.model.cj.request.PlanDiagramDirBatchCopyParam;
import com.uinnova.product.eam.model.cj.request.PlanDiagramDirBatchMoveParam;
import com.uinnova.product.eam.service.cj.service.PlanDiagramDirBatchService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;


/**
 * 方案、视图、文件夹批量操作  controller
 *
 * <AUTHOR>
 * @since 2022-3-1 20:40:43
 */
@RestController
@RequestMapping("planDiagramDir/batch")
@Slf4j
public class PlanDiagramDirBatchController {

    @Resource
    private PlanDiagramDirBatchService planDiagramDirBatchService;


    /**
     * 批量移动
     */
    @PostMapping("move")
    public ResultMsg move(@RequestBody PlanDiagramDirBatchMoveParam request) {
        planDiagramDirBatchService.moveBatch(request);
        return ResultMsg.ok();
    }

    /**
     * 批量复制
     **/
    @PostMapping("copy")
    public ResultMsg copy(@RequestBody PlanDiagramDirBatchCopyParam request) {

        planDiagramDirBatchService.copyBatch(request);
        return ResultMsg.ok();
    }

}

