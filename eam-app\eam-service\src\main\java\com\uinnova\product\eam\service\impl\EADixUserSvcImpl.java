package com.uinnova.product.eam.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.binary.core.lang.StringUtils;
import com.uinnova.product.eam.model.dto.UserInfoDto;
import com.uinnova.product.eam.service.EADixUserSvc;
import com.uinnova.product.eam.service.ICISwitchSvc;
import com.uinnova.product.eam.service.exception.BusinessException;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.base.LibType;
import com.uino.bean.cmdb.business.ImportSheetMessage;
import com.uino.bean.permission.base.SysOrg;
import com.uino.bean.permission.base.SysRole;
import com.uino.bean.permission.base.SysUserOrgRlt;
import com.uino.bean.permission.business.UserInfo;
import com.uino.dao.BaseConst;
import com.uino.dao.permission.ESOrgSvc;
import com.uino.dao.permission.ESRoleSvc;
import com.uino.dao.permission.rlt.ESUserOrgRltSvc;
import com.uino.dao.util.ESUtil;
import com.uino.service.cmdb.microservice.ICIClassSvc;
import com.uino.service.permission.microservice.IUserSvc;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class EADixUserSvcImpl implements EADixUserSvc {

    @Autowired
    private ESRoleSvc esRoleSvc;

    @Autowired
    private ESOrgSvc esOrgSvc;

    @Autowired
    private IUserSvc userSvc;

    @Autowired
    private ESUserOrgRltSvc esUserOrgRltSvc;

    @Autowired
    private ICISwitchSvc iCISwitchSvc;

    @Autowired
    private ICIClassSvc iCIClassSvc;



    public Integer saveBatchOrg(List<SysOrg> orgList) {
        if (!CollectionUtils.isEmpty(orgList)) {
            for (SysOrg sysOrg : orgList) {
                sysOrg.setDomainId(1L);
                if (StringUtils.isBlank(sysOrg.getOrgCode())) {
                    sysOrg.setOrgCode(String.valueOf(ESUtil.getUUID()));
                }
            }
            return esOrgSvc.saveOrUpdateBatch(orgList);
        }
        return 0;
    }

    @Override
    public ImportSheetMessage saveBatchUser(List<UserInfoDto> userInfoDto) {
        // 获取角色详情
        List<Long> roleIds = new ArrayList<>();
        for (UserInfoDto user : userInfoDto) {
            roleIds.addAll(user.getRoleIds());
        }
        Map<Long, SysRole> roleInfoMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(roleIds)) {
            List<SysRole> roleInfoList = esRoleSvc.getListByQuery(QueryBuilders.termsQuery("id", roleIds));
            if (!CollectionUtils.isEmpty(roleInfoList)) {
                for (SysRole role : roleInfoList) {
                    roleInfoMap.put(role.getId(), role);
                }
            }
        }
        this.setRoleInfo(userInfoDto, roleInfoMap);
        // 获取组织详情
        List<Long> orgIds = new ArrayList<>();
        for (UserInfoDto user : userInfoDto) {
            orgIds.addAll(user.getOrgIds());
        }
        Map<Long, SysOrg> orgInfoMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(orgIds)) {
            List<SysOrg> orgInfoList = esOrgSvc.getListByQuery(QueryBuilders.termsQuery("id", orgIds));
            if (!CollectionUtils.isEmpty(orgInfoList)) {
                for (SysOrg org : orgInfoList) {
                    orgInfoMap.put(org.getId(), org);
                }
            }
        }
        this.setOrgInfo(userInfoDto, orgInfoMap);
        List<UserInfo> userInfos = JSONObject.parseArray(JSONObject.toJSONString(userInfoDto), UserInfo.class);
        log.info("传参数据转化userInfos：{}", JSONObject.toJSONString(userInfos));
        return userSvc.syncUserBatch(BaseConst.DEFAULT_DOMAIN_ID,userInfos);
    }

    /**
     *  将传参用roleId信息解析成roleInfo信息塞入
     * @param userInfos
     * @param roleInfoMap
     */
    private void setRoleInfo(List<UserInfoDto> userInfos, Map<Long, SysRole> roleInfoMap) {
        for (UserInfoDto userInfo : userInfos) {
            List<Long> roleIds = userInfo.getRoleIds();
            Set<SysRole> roles = new HashSet<>();
            for (Long roleId : roleIds) {
                if (roleInfoMap.containsKey(roleId)) roles.add(roleInfoMap.get(roleId));
            }
            userInfo.setRoles(roles);
        }
    }

    private void setOrgInfo(List<UserInfoDto> userInfos, Map<Long, SysOrg> orgInfoMap) {
        for (UserInfoDto userInfo : userInfos) {
            List<Long> orgIds = userInfo.getOrgIds();
            Set<SysOrg> orgs = new HashSet<>();
            for (Long orgId : orgIds) {
                if (orgInfoMap.containsKey(orgId)) orgs.add(orgInfoMap.get(orgId));
            }
            userInfo.setOrgs(orgs);
        }
    }

    @Override
    public Integer saveBatchOrgUserRlt(List<SysUserOrgRlt> rltList) {
        return esUserOrgRltSvc.saveOrUpdateBatch(rltList);
    }

    @Override
    public Integer saveBatchDepartment() {
        try {
            //  获取现有组织数据并建立ID到主键的映射
            List<SysOrg> sysOrgList = esOrgSvc.getListByQuery(QueryBuilders.matchAllQuery());
            if (CollectionUtils.isEmpty(sysOrgList)) {
                log.warn("在系统中找不到组织数据");
                return 0;
            }
            // 建立组织ID到主键的映射
            Map<Long, String> orgIdToPrimaryKeyMap = sysOrgList.stream()
                    .filter(org -> org.getId() != null && org.getOrgCode() != null)
                    .collect(Collectors.toMap(SysOrg::getId, SysOrg::getOrgCode,
                            (existing, replacement) -> existing
                    ));

            //  获取CI中的部门数据
            CcCiClassInfo classInfo = iCIClassSvc.getCiClassByClassCode("部门");
            List<ESCIInfo> existingDepts = iCISwitchSvc.getCiByClassIds(Collections.singletonList(classInfo.getCiClass().getId()), null, LibType.DESIGN);
            //existingDepts中有数据不在sysOrgList里的时候将数据删除
            //  构建现有部门的索引maps
            Map<String, ESCIInfo> deptKeyMap = new HashMap<>();
            Map<String, Set<String>> parentDeptNameMap = new HashMap<>();

            for (ESCIInfo dept : existingDepts) {
                Map<String, Object> attrs = dept.getAttrs();
                String deptKey = String.valueOf(attrs.get("部门主键"));
                deptKeyMap.put(deptKey, dept);
                // 使用部门主键作为父部门标识
                String parentDeptKey = String.valueOf(attrs.get("上级部门"));
                parentDeptNameMap.computeIfAbsent(parentDeptKey, k -> new HashSet<>())
                        .add(String.valueOf(attrs.get("部门名称")));
            }

            // 收集需要删除的部门
            List<ESCIInfo> toDelete = existingDepts.stream()
                    .filter(dept -> {
                        String deptKey = String.valueOf(dept.getAttrs().get("部门主键"));
                        return !orgIdToPrimaryKeyMap.values().contains(deptKey);
                    })
                    .collect(Collectors.toList());

            //  处理每个组织数据
            List<ESCIInfo> toUpdate = new ArrayList<>();
            List<ESCIInfo> toCreate = new ArrayList<>();
            for (SysOrg org : sysOrgList) {
                ESCIInfo ciInfo = deptKeyMap.get(org.getOrgCode());
                // 获取父组织的主键，顶级组织的parentOrgPrimaryKey为null
                String parentOrgPrimaryKey = null;
                if (org.getParentOrgId() != null && org.getParentOrgId() != 0) {
                    parentOrgPrimaryKey = orgIdToPrimaryKeyMap.get(org.getParentOrgId());
                    // 只有在非顶级组织且找不到父组织主键时才跳过
                    if (parentOrgPrimaryKey == null) {
                        log.warn("找不到上级组织主键, 组织ID: {}, 组织编码: {}",
                                org.getParentOrgId(), org.getOrgCode());
                        continue;
                    }
                }

                // 检查同级部门是否有重名（对于顶级部门，parentOrgPrimaryKey为null）
                if (parentDeptNameMap.getOrDefault(parentOrgPrimaryKey, new HashSet<>()).contains(org.getOrgName())) {
                    log.error("部门编码或名称在同级重复: orgCode={}, orgName={}", org.getOrgCode(), org.getOrgName());
                    continue;
                }

                if (ciInfo != null) {
                    // 更新现有部门
                    updateDepartment(ciInfo, org, parentOrgPrimaryKey);
                    toUpdate.add(ciInfo);
                } else {
                    // 创建新部门
                    ESCIInfo newDept = createNewDepartment(org, classInfo.getCiClass().getId(),
                            parentOrgPrimaryKey);
                    toCreate.add(newDept);
                }
            }
            log.info("部门数据同步完成: 更新{}条, 新增{}条", toUpdate.size(), toCreate.size());
            toUpdate.addAll(toCreate);
            if (!toUpdate.isEmpty()) {
                iCISwitchSvc.saveOrUpdateBatchCI(toUpdate, Collections.singletonList(classInfo.getCiClass().getId()), "admin", "admin", LibType.DESIGN);
            }

            // 处理删除
            if (!toDelete.isEmpty()) {
                List<Long> deleteIds = toDelete.stream()
                        .map(ESCIInfo::getId)
                        .collect(Collectors.toList());
                iCISwitchSvc.removeByIds(deleteIds, 1l, LibType.DESIGN);
            }
        } catch (Exception e) {
            log.error("部门数据同步失败", e);
            throw new BusinessException("部门数据同步失败: " + e.getMessage());
        }
        return 1;
    }

    private void updateDepartment(ESCIInfo existingDept, SysOrg org, String parentOrgPrimaryKey) {
        Map<String, Object> attrs = existingDept.getAttrs();
        attrs.put("部门主键", org.getOrgCode());
        attrs.put("部门名称", org.getOrgName());
        attrs.put("上级部门", parentOrgPrimaryKey);
        existingDept.setAttrs(attrs);
    }

    private ESCIInfo createNewDepartment(SysOrg org, Long classId, String parentOrgPrimaryKey) {
        ESCIInfo newDept = new ESCIInfo();
        newDept.setClassId(classId);
        Map<String, Object> attrs = new HashMap<>();
        attrs.put("部门主键", org.getOrgCode());
        attrs.put("部门名称", org.getOrgName());
        attrs.put("上级部门", parentOrgPrimaryKey);
        newDept.setAttrs(attrs);
        newDept.setCiCode(String.valueOf(ESUtil.getUUID()));
        newDept.setClassId(classId);
        return newDept;
    }

}
