package com.uino.monitor;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.binary.jdbc.Page;
import com.uino.StartBaseWebAppliaction;
import com.uino.api.client.monitor.IUinoEventApiSvc;
import com.uino.bean.ap.param.EventStatisticsParam;
import com.uino.bean.event.param.EventCiSeverityQueryParam;
import com.uino.bean.monitor.base.ESMonEapEvent;
import com.uino.dao.event.EventHistoryDao;
import com.uino.dao.util.ESUtil;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import java.util.*;

@RunWith(SpringRunner.class)
@WebAppConfiguration
@SpringBootTest(classes = {StartBaseWebAppliaction.class})
@ActiveProfiles("provider-local-dev")
@AutoConfigureMockMvc(addFilters = false)
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class EventTest {

    @Autowired
    private IUinoEventApiSvc eventApiSvc;
    @Autowired
    private EventHistoryDao eventHistoryDao;

    @Test
    public void queryCurrentEventsByCiCodesAndServeritys() {
        Set<String> ciCodes = new HashSet<>();
        ciCodes.add("7493653406669135");

        List<Integer> severity = new ArrayList<>();
        severity.add(1);
        EventCiSeverityQueryParam queryParam = new EventCiSeverityQueryParam();
        queryParam.setCiCodes(ciCodes);
//        queryParam.setSeveritys(severity);
        List<ESMonEapEvent>  events = eventApiSvc.queryCurrentEventsByCiCodesAndServeritys(queryParam);
        System.out.println("----" + JSON.toJSONString(events));
    }

    @Test
    public void saveEventHis() {
        ESMonEapEvent event = new ESMonEapEvent();
        event.setId(String.valueOf(ESUtil.getUUID()));
        event.setCiName("测试");
        event.setFirstOccurrence(new Date());
        eventApiSvc.saveEventHis(event);
        System.out.println("----" + JSON.toJSONString(event));
    }

    @Test
    public void testTermsCount() throws Exception{

        EventStatisticsParam param = new EventStatisticsParam();
        param.setAnalysisField("grade");
        param.setGroupField("DTOwnedSpace.keyword");
        param.setBuilderSize(10000);

        Map<String, Map<String, Long>> result = eventHistoryDao.getTermsFieldGroupCount(QueryBuilders.boolQuery(), param);
        System.out.println("-------------" + JSON.toJSONString(result));
    }

    @Test
    public void testCount() throws Exception{

        EventStatisticsParam param = new EventStatisticsParam();
        param.setAnalysisField("DTOwnedSpace.keyword");
        param.setBuilderSize(100);

        Map<String, Long> result = eventHistoryDao.getTermsFieldCount(QueryBuilders.boolQuery(), param);
        System.out.println("-------------" + JSON.toJSONString(result));
    }

    @Test
    public void testGroupByCountField() throws Exception{

        EventStatisticsParam param = new EventStatisticsParam();
        param.setAnalysisField("kpiName.keyword");
        param.setBuilderSize(100);

        List<Map<String, Long>> result = eventHistoryDao.groupByCountField("DTOwnedSpace.keyword",QueryBuilders.boolQuery());
        System.out.println("-------------" + JSON.toJSONString(result));
    }
    
    @Test
    public void testQuery() {
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
//        queryBuilder.must(QueryBuilders.rangeQuery("lastOccurrence").format("yyyy-MM-dd HH:mm:ss").from("2021-10-09 15:00:00").to("2021-10-09 17:00:00"));
        queryBuilder.must(QueryBuilders.rangeQuery("lastOccurrence").gt("1633762800000").lt("1633770000000"));
        Page<JSONObject> list = eventHistoryDao.getListByQuery(1, 300, queryBuilder);
        System.out.println(list);
    }

}
