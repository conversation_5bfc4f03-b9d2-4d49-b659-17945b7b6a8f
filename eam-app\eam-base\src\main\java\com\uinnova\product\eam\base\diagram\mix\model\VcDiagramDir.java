package com.uinnova.product.eam.base.diagram.mix.model;




import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;

@Deprecated
@Comment("视图目录表[VC_DIAGRAM_DIR]")
public class VcDiagramDir implements EntityBean {
	private static final long serialVersionUID = 1L;


	@Comment("ID[ID]")
	private Long id;


	@Comment("目录名称[DIR_NAME]")
	private String dirName;


	@Comment("目录类型[DIR_TYPE]   ")
	private Integer dirType;


	@Comment("上级目录ID[PARENT_ID]")
	private Long parentId;


	@Comment("所属用户ID[USER_ID]")
	private Long userId;


	@Comment("目录层级级别[DIR_LVL]")
	private Integer dirLvl;


	@Comment("目录层级路径[DIR_PATH]    目录层级路径:例：#1#2#7#")
	private String dirPath;


	@Comment("显示排序[ORDER_NO]")
	private Integer orderNo;


	@Comment("是否末级[IS_LEAF]    是否末级:1=是 0=否")
	private Integer isLeaf;


	@Comment("目录图标[ICON]")
	private String icon;


	@Comment("目录描述[DIR_DESC]")
	private String dirDesc;


	@Comment("所属域[DOMAIN_ID]")
	private Long domainId;


	@Comment("数据状态[DATA_STATUS]    数据状态:0=删除，1=正常")
	private Integer dataStatus;


	@Comment("创建人[CREATOR]")
	private String creator;


	@Comment("修改人[MODIFIER]")
	private String modifier;


	@Comment("创建时间[CREATE_TIME]    创建时间:yyyyMMddHHmmss")
	private Long createTime;


	@Comment("更新时间[MODIFY_TIME]    更新时间:yyyyMMddHHmmss")
	private Long modifyTime;

	@Comment("主题目录")
	private Long subjectId;

	private Long oldDirId;

	private Long oldParentId;

	@Comment("是否初始目录  1=是，0=否")
	private Integer dirInit;

	@Comment("关联es系统id")
	private String esSysId;

	@Comment("系统类型")
	private String sysType;

	@Comment("是否是系统文件夹，0:否，1:是")
	private Integer sysDir;

	public Long getId() {
		return this.id;
	}
	public void setId(Long id) {
		this.id = id;
	}


	public String getDirName() {
		return this.dirName;
	}
	public void setDirName(String dirName) {
		this.dirName = dirName;
	}


	public Integer getDirType() {
		return this.dirType;
	}
	public void setDirType(Integer dirType) {
		this.dirType = dirType;
	}


	public Long getParentId() {
		return this.parentId;
	}
	public void setParentId(Long parentId) {
		this.parentId = parentId;
	}


	public Long getUserId() {
		return this.userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}


	public Integer getDirLvl() {
		return this.dirLvl;
	}
	public void setDirLvl(Integer dirLvl) {
		this.dirLvl = dirLvl;
	}


	public String getDirPath() {
		return this.dirPath;
	}
	public void setDirPath(String dirPath) {
		this.dirPath = dirPath;
	}


	public Integer getOrderNo() {
		return this.orderNo;
	}
	public void setOrderNo(Integer orderNo) {
		this.orderNo = orderNo;
	}


	public Integer getIsLeaf() {
		return this.isLeaf;
	}
	public void setIsLeaf(Integer isLeaf) {
		this.isLeaf = isLeaf;
	}


	public String getIcon() {
		return this.icon;
	}
	public void setIcon(String icon) {
		this.icon = icon;
	}


	public String getDirDesc() {
		return this.dirDesc;
	}
	public void setDirDesc(String dirDesc) {
		this.dirDesc = dirDesc;
	}


	public Long getDomainId() {
		return this.domainId;
	}
	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}


	public Integer getDataStatus() {
		return this.dataStatus;
	}
	public void setDataStatus(Integer dataStatus) {
		this.dataStatus = dataStatus;
	}


	public String getCreator() {
		return this.creator;
	}
	public void setCreator(String creator) {
		this.creator = creator;
	}


	public String getModifier() {
		return this.modifier;
	}
	public void setModifier(String modifier) {
		this.modifier = modifier;
	}


	public Long getCreateTime() {
		return this.createTime;
	}
	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}


	public Long getModifyTime() {
		return this.modifyTime;
	}
	public void setModifyTime(Long modifyTime) {
		this.modifyTime = modifyTime;
	}

	public Long getSubjectId() {
		return subjectId;
	}

	public void setSubjectId(Long subjectId) {
		this.subjectId = subjectId;
	}

	public Long getOldDirId() {
		return oldDirId;
	}

	public void setOldDirId(Long oldDirId) {
		this.oldDirId = oldDirId;
	}

	public Long getOldParentId() {
		return oldParentId;
	}

	public void setOldParentId(Long oldParentId) {
		this.oldParentId = oldParentId;
	}

	public Integer getDirInit() {
		return dirInit;
	}

	public void setDirInit(Integer dirInit) {
		this.dirInit = dirInit;
	}

	public String getEsSysId() {
		return esSysId;
	}

	public void setEsSysId(String esSysId) {
		this.esSysId = esSysId;
	}

	public String getSysType() {
		return sysType;
	}

	public void setSysType(String sysType) {
		this.sysType = sysType;
	}

	public Integer getSysDir() {
		return sysDir;
	}

	public void setSysDir(Integer sysDir) {
		this.sysDir = sysDir;
	}
}


