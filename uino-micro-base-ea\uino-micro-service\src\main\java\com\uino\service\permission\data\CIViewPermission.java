package com.uino.service.permission.data;

import com.uino.bean.cmdb.base.LibType;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 对象数据查看数据权限控制注解
 * <AUTHOR>
 */

@Target({ElementType.PARAMETER, ElementType.LOCAL_VARIABLE})
@Retention(RetentionPolicy.RUNTIME)
public @interface CIViewPermission {
    //处理对象true,处理当前参数false
    boolean object() default true;

    LibType libType() default LibType.DESIGN;

    //如果是处理对象,需要获取被处理对象中的分类字段名
    String classFieldName() default "classIds";

    //如果是处理对象,需要获取被处理对象中的分类字段名
    String ciFieldName() default "ciCodes";

    //标签参数字段名
    String tagFieldName() default "tagIds";
}
