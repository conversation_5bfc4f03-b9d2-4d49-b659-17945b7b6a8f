package com.uino.web.exter.mvc;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import com.uinnova.product.vmdb.comm.util.CommUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.binary.core.util.BinaryUtils;
import com.binary.framework.util.ControllerUtils;
import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.comm.bean.QueryPageCondition;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uinnova.product.vmdb.comm.doc.annotation.MvcDesc;
import com.uinnova.product.vmdb.comm.model.ci.CCcCi;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClass;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClassDir;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.comm.model.ci.CcCiClass;
import com.uinnova.product.vmdb.comm.util.RestTypeUtil;
import com.uinnova.product.vmdb.comm.util.SaveType;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiRecord;
import com.uinnova.product.vmdb.provider.ci.bean.CiClassSaveInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CiGroupPage;
import com.uinnova.product.vmdb.provider.ci.bean.CiQueryCdt;
import com.uinnova.product.vmdb.provider.search.bean.CcCiObj;
import com.uinnova.product.vmdb.provider.search.bean.CcCiSearchPage;
import com.uino.util.sys.SysUtil.StringUtil;
import com.uino.bean.cmdb.base.CcCiClassDir;
import com.uino.bean.cmdb.business.ImportRowMessage;
import com.uino.bean.cmdb.business.ImportSheetMessage;
import com.uino.bean.cmdb.query.ESAttrBean;
import com.uino.bean.cmdb.query.ESCIClassSearchBean;
import com.uino.bean.cmdb.query.ESCISearchBean;
import com.uino.bean.dix.Record;
import com.uino.api.client.cmdb.ICIApiSvc;
import com.uino.api.client.cmdb.ICIClassApiSvc;
import com.uino.api.client.cmdb.IDirApiSvc;

import lombok.extern.slf4j.Slf4j;

/**
 * 用于Dix查询Ci相关信息
 *
 * <AUTHOR>
 * @version 2020/1/8
 */
@Slf4j
@Controller
@RequestMapping("/dix")
@MvcDesc(author = "zhengchunyun", desc = "提供CI管理,查询接口")
public class DixCiMvc {

    private Logger logger = LoggerFactory.getLogger(DixCiMvc.class);

    private static final String CI_LIST = "cilist";

    private static final String CLASS_NAME = "className";

    private static final String PAGE_NUM = "pageNum";

    private static final String PAGE_SIZE = "pageSize";

    @Autowired
    ICIApiSvc ciInterface;

    @Autowired
    ICIClassApiSvc ciClassInterface;

    @Autowired
    private IDirApiSvc dirApiSvc;

    /**
     * 通过单个属性查询Ci信息
     */
    @RequestMapping("/getByAttr")
    public void getByAttr(HttpServletRequest request, HttpServletResponse response, @RequestBody String body) {
        logger.info("access getByAttr, params: {}", body);
        JSONObject paramObj = JSON.parseObject(body);
        String attrName = paramObj.getString("attrName");
        String attrValue = paramObj.getString("attrValue");
        String className = paramObj.getString(CLASS_NAME);
        BinaryUtils.checkEmpty(attrName, "attrName");
        BinaryUtils.checkEmpty(attrValue, "attrValue");
        List<ESAttrBean> attrsCdt = new ArrayList<>();
        ESAttrBean attrBean = new ESAttrBean();
        attrBean.setKey(attrName);
        attrBean.setOptType(1);
        attrBean.setValue(attrValue);
        attrsCdt.add(attrBean);
        ESCISearchBean queryBean = new ESCISearchBean();
        if (!BinaryUtils.isEmpty(className)) {
            CCcCiClass classCdt = new CCcCiClass();
            classCdt.setClassNameEqual(className);
            List<Long> classIds = new ArrayList<>();
            List<CcCiClassInfo> clsInfoList = ciClassInterface.queryClassByCdt(classCdt);
            CcCiClassInfo ciClassInfo = null;
            if (clsInfoList != null) {
                ciClassInfo = clsInfoList.get(0);
                classIds.add(ciClassInfo.getCiClass().getId());
            }
            queryBean.setClassIds(classIds);
        }
        queryBean.setAndAttrs(attrsCdt);
        queryBean.setPageNum(1);
        queryBean.setPageSize(1);
        queryBean.setDomainId(1L);
        CcCiSearchPage searchPage = ciInterface.searchCIByBean(queryBean);
        List<CcCiObj> records = searchPage.getData().getRecords();
        if (!BinaryUtils.isEmpty(records)) {
            CcCiObj ccCiObj = records.get(0);
            ControllerUtils.returnJson(request, response, transToRecord(ccCiObj));
        }
    }

    /**
     * 通过多属性属性查询CI分类
     */
    @RequestMapping("/getByAttrs")
    public void getByAttrs(HttpServletRequest request, HttpServletResponse response, @RequestBody String body) {
        logger.info("access getByAttrs, params: {}", body);
        JSONObject paramObj = JSON.parseObject(body);
        int pageNum = paramObj.containsKey(PAGE_NUM) ? paramObj.getIntValue(PAGE_NUM) : 1;
        int pageSize = paramObj.containsKey(PAGE_SIZE) ? paramObj.getIntValue(PAGE_SIZE) : 10;
        JSONArray attrArray = paramObj.getJSONArray("attrCdts");
        List<ESAttrBean> attrCdts = attrArray.toJavaList(ESAttrBean.class);
        BinaryUtils.checkEmpty(attrCdts, "attrCdts");
        List<String> classNames = (List<String>) paramObj.get("classNames");
        Map<Long, CcCiClassInfo> clsMap = new HashMap<>();
        List<Long> classIds = new ArrayList<>();
        CCcCiClass classCdt = new CCcCiClass();
        if (!BinaryUtils.isEmpty(classNames)) {
            classCdt.setClassNames(classNames.toArray(new String[] {}));
        }
        List<CcCiClassInfo> clsInfoList = ciClassInterface.queryClassByCdt(classCdt);
        if (clsInfoList != null) {
            for (CcCiClassInfo clsInfo : clsInfoList) {
                classIds.add(clsInfo.getCiClass().getId());
                clsMap.put(clsInfo.getCiClass().getId(), clsInfo);
            }
        }
        ESCISearchBean queryBean = new ESCISearchBean();
        queryBean.setClassIds(classIds);
        queryBean.setAndAttrs(attrCdts);
        queryBean.setPageNum(pageNum);
        queryBean.setPageSize(pageSize);
        queryBean.setDomainId(1L);
        CcCiSearchPage searchPage = ciInterface.searchCIByBean(queryBean);
        List<CcCiObj> records = searchPage.getData().getRecords();
        List<Record> ret = new ArrayList<>();
        if (!BinaryUtils.isEmpty(records)) {
            /*
             * if (clsMap.size() == 0) { clsInfoList = new ArrayList<>(); for
             * (CcCiObj ccCiObj : records) {
             * clsInfoList.add(ciInterface.queryCiClassInfoById(ccCiObj.getCi().
             * getClassId())); } for (CcCiClassInfo ciClassInfo : clsInfoList) {
             * clsMap.put(ciClassInfo.getCiClass().getId(), ciClassInfo); } }
             */
            for (CcCiObj ccCiObj : records) {
                CcCiClassInfo ciClassInfo = clsMap.get(ccCiObj.getCi().getClassId());
                Record record = transToRecords(ciClassInfo, ccCiObj);
                if (record != null) {
                    ret.add(record);
                }
            }
        }
        ControllerUtils.returnJson(request, response, ret);
    }

    /**
     * 批量更新或保存Ci信息
     */
    @RequestMapping("/saveOrUpdateCiBatch")
    public void saveOrUpdateCiBatch(HttpServletRequest request, HttpServletResponse response,
            @RequestBody String body) {
        logger.info("access saveOrUpdateCiBatch, params: {}", body);
        String className = "";
        Long sourceId = 0L;
        List<Map<String, String>> datas = null;
        JSONObject paramObj = JSON.parseObject(body);
        Boolean returnPks = paramObj.getBoolean("returnSucessPks");
        returnPks = returnPks == null ? false : returnPks;
        if (paramObj.containsKey(CLASS_NAME)) {
            className = paramObj.getString(CLASS_NAME);
        }
        if (paramObj.containsKey("sourceId")) {
            sourceId = paramObj.getLong("sourceId");
        } else {
            sourceId = 8L;
        }
        if (paramObj.containsKey(CI_LIST)) {
            datas = (List<Map<String, String>>) paramObj.get(CI_LIST);
        }
        BinaryUtils.checkEmpty(className, CLASS_NAME);
        BinaryUtils.checkEmpty(datas, CI_LIST);
        Object returnVal = saveOrUpdateCiBatch(className, datas, sourceId, returnPks);
        ControllerUtils.returnJson(request, response, returnVal);
    }

    /**
     * 批量更新或保存Ci信息
     */
    @RequestMapping("/saveOrUpdateCis")
    public void saveOrUpdateCis(HttpServletRequest request, HttpServletResponse response, @RequestBody String body) {
        logger.info("access saveOrUpdateCiBatch, params: {}", body);
        String className = "";
        Long sourceId = 0L;
        List<Map<String, String>> datas = null;
        JSONObject paramObj = JSON.parseObject(body);
        if (paramObj.containsKey(CLASS_NAME)) {
            className = paramObj.getString(CLASS_NAME);
        }
        if (paramObj.containsKey("sourceId")) {
            sourceId = paramObj.getLong("sourceId");
        } else {
            sourceId = 8L;
        }
        if (paramObj.containsKey("datas")) {
            datas = (List<Map<String, String>>) paramObj.get("datas");
        }
        BinaryUtils.checkEmpty(className, CLASS_NAME);
        BinaryUtils.checkEmpty(datas, "datas");
        List<String> returnVal = (List<String>) saveOrUpdateCiBatch(className, datas, sourceId, true);
        JSONArray retJson = new JSONArray();
        if (returnVal != null) {
            for (String pkStr : returnVal) {
                retJson.add(JSONArray.parseArray(pkStr));
            }
        }
        JSONObject ret = new JSONObject();
        ret.put("create", retJson);
        ControllerUtils.returnJson(request, response, ret);
    }

    private Object saveOrUpdateCiBatch(String className, List<Map<String, String>> datas, Long sourceId,
            Boolean returnPks) {
        Long domainId = 1L;
        CCcCiClass clsCdt = new CCcCiClass();
        clsCdt.setDomainId(domainId);
        clsCdt.setCiType(1);
        clsCdt.setClassNameEqual(className);
        List<CcCiClassInfo> ciClass = ciClassInterface.queryClassByCdt(clsCdt);
        BinaryUtils.checkEmpty(ciClass, "ciClass");
        List<CcCiRecord> ciRecords = new ArrayList<>();
        for (Map<String, String> map : datas) {
            CcCiRecord record = new CcCiRecord();
            record.setAttrs(map);
            ciRecords.add(record);
        }
        CiClassSaveInfo saveInfo = new CiClassSaveInfo(ciClass.get(0).getCiClass().getId(), ciClass.get(0).getCiClass().getClassStdCode(), sourceId, "system", ciRecords,
                SaveType.UPDATE);
        ImportSheetMessage saveResult = null;
        try {
            saveResult = ciInterface.saveOrUpdateCiBatch(domainId, saveInfo);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        if (saveResult == null) {
            saveResult = new ImportSheetMessage();
            saveResult.setTotalNum(ciRecords.size());
        }
        Integer totalCount = saveResult.getTotalNum();
        Integer successCount = saveResult.getSuccessNum();
        Integer failCount = saveResult.getFailNum();
        Integer ignoreCount = saveResult.getIgnoreNum();
        Integer insertCount = saveResult.getInsertNum();
        Integer updateCount = saveResult.getUpdateNum();
        List<ImportRowMessage> errInfos = saveResult.getRowMessages();
        StringBuffer mes = new StringBuffer();
        mes.append("本次保存分类【").append(className).append("】下CI数据,").append("总数【").append(totalCount).append("】；成功【")
                .append(successCount).append("】,失败【").append(failCount).append("】，忽略【").append(ignoreCount)
                .append("】；新增【").append(insertCount).append("】，更新【").append(updateCount).append("】。");
        if (!BinaryUtils.isEmpty(saveResult.getDetailUrl())) {
            mes.append("明细结果文件地址(服务器)【").append(saveResult.getDetailUrl()).append("】。");
        }
        if (!CollectionUtils.isEmpty(errInfos)) {
            String str = JSON.toJSONString(errInfos);
            mes.append("错误信息明细：").append(str);
            logger.error("dix保存ci错误信息明细如下：====================={} ", mes);
        }
        Object returnVal = mes.toString();
        if (returnPks) {
            returnVal = saveResult.getSucessCIPks();
        }
        return returnVal;
    }

    @RequestMapping("/getCiPageByClassName")
    public void getCiPageByClassName(HttpServletRequest request, HttpServletResponse response,
            @RequestBody String body) {
        logger.info("access getCiPageByClassName,params:{} ", body);
        JSONObject paramObj = JSON.parseObject(body);
        Integer pageNum = paramObj.containsKey(PAGE_NUM) ? paramObj.getIntValue(PAGE_NUM) : 1;
        Integer pageSize = paramObj.containsKey(PAGE_SIZE) ? paramObj.getIntValue(PAGE_SIZE) : 10;
        String className = paramObj.getString(CLASS_NAME);
        CCcCiClass clsCdt = new CCcCiClass();
        clsCdt.setClassName(className);
        List<CcCiClassInfo> cls = ciClassInterface.queryClassByCdt(clsCdt);
        Set<Long> classIds = new HashSet<>();
        cls.forEach(c -> {
            Long clsId = c.getCiClass().getId();
            classIds.add(clsId);
        });
        CCcCi cdt = new CCcCi();
        cdt.setClassIds(classIds.toArray(new Long[] {}));
		Page<CcCiInfo> ciPage = ciInterface.queryCiInfoPage(1L, pageNum, pageSize, cdt, null, true, true);
        Page<Record> result = this.transToRecords(ciPage);
        ControllerUtils.returnJson(request, response, result);
    }

    @RequestMapping("/getAllCiClass")
    public void getAllCiClass(HttpServletRequest request, HttpServletResponse response) {
        List<CcCiClassInfo> classInfoList = ciClassInterface.queryClassByCdt(new CCcCiClass());
        List<CcCiClass> ciClass = new ArrayList<CcCiClass>();
        if (classInfoList != null && classInfoList.size() > 0) {
            for (CcCiClassInfo ciClsInfo : classInfoList) {
                ciClass.add(ciClsInfo.getCiClass());
            }
        }
        ControllerUtils.returnJson(request, response, ciClass);
    }

    @RequestMapping("/getClass")
    public void getClass(HttpServletRequest request, HttpServletResponse response, @RequestBody String clsName) {
        CCcCiClass query = new CCcCiClass();
        query.setClassNameEqual(clsName);
        List<CcCiClassInfo> classInfoList = ciClassInterface.queryClassByCdt(query);
        List<CcCiClass> ciClass = new ArrayList<CcCiClass>();
        if (classInfoList != null && classInfoList.size() > 0) {
            for (CcCiClassInfo ciClsInfo : classInfoList) {
                ciClass.add(ciClsInfo.getCiClass());
            }
        }
        ControllerUtils.returnJson(request, response, ciClass != null && ciClass.size() > 0 ? ciClass.get(0) : null);
    }

    @RequestMapping("/getClassByClassName")
    public void getClassByClassName(HttpServletRequest request, HttpServletResponse response, @RequestBody String param) {
        JSONObject queryJson = JSONObject.parseObject(param);
        String className = queryJson.getString(CLASS_NAME);

        if(StringUtils.isBlank(className)){
            ControllerUtils.returnJson(request, response, null);
        }

        CCcCiClass query = new CCcCiClass();
        query.setClassNameEqual(className);
        List<CcCiClassInfo> classInfoList = ciClassInterface.queryClassByCdt(query);
        List<CcCiClass> ciClass = new ArrayList<CcCiClass>();
        if (classInfoList != null && classInfoList.size() > 0) {
            for (CcCiClassInfo ciClsInfo : classInfoList) {
                ciClass.add(ciClsInfo.getCiClass());
            }
        }
        ControllerUtils.returnJson(request, response, ciClass != null && ciClass.size() > 0 ? ciClass.get(0) : null);
    }

    @RequestMapping("/saveCiClass")
    public void saveCiClass(HttpServletRequest request, HttpServletResponse response, @RequestBody DixCIClass saveDto) {
        // 将dirname转dirid
        String dirName = saveDto.getDirName();
        Assert.isTrue(StringUtil.isNotBack(dirName), "dirName not null");
        CCcCiClassDir dirQuery = new CCcCiClassDir();
        dirQuery.setDirNameEqual(dirName);
        dirQuery.setCiType(1);
        List<CcCiClassDir> dirs = dirApiSvc.queryDirList(dirQuery, "id", true);
        Assert.isTrue(dirs.size() > 0, "dir inexistence");
        Long dirId = dirs.get(0).getId();
        saveDto.getCiClass().setDirId(dirId);
        // 获取分类名称填补id，使其可以触发更新
        if (StringUtil.isNotBack(saveDto.getCiClass().getClassName())) {
            ESCIClassSearchBean clsQuery = new ESCIClassSearchBean();
            clsQuery.setCdt(new CCcCiClass());
            clsQuery.getCdt().setClassNameEqual(saveDto.getCiClass().getClassName());
            List<CcCiClassInfo> clss = ciClassInterface.queryCiClassInfoListBySearchBean(clsQuery);
            if (clss != null && clss.size() > 0) {
                Long clsId = clss.get(0).getCiClass().getId();
                saveDto.getCiClass().setId(clsId);
            }
        }
        ControllerUtils.returnJson(request, response, ciClassInterface.saveOrUpdateCIClass(saveDto));
    }

    /**
     * 删除ci分类
     *
     * @param request
     * @param response
     * @param className
     */
    @RequestMapping("/deleteCIClass")
    public void deleteCIClass(HttpServletRequest request, HttpServletResponse response, @RequestBody String className) {
        CCcCiClass clsQuery = new CCcCiClass();
        clsQuery.setClassNameEqual(className);
        List<CcCiClassInfo> clss = ciClassInterface.queryClassByCdt(clsQuery);
        Assert.notEmpty(clss, "CI分类不存在");
        ciClassInterface.removeCIClassById(clss.get(0).getCiClass().getId());
        ControllerUtils.returnJson(request, response,
                ciClassInterface.removeCIClassById(clss.get(0).getCiClass().getId()));
    }

    @RequestMapping("/getAllCiClassInfo")
    public void getAllCiClassInfo(HttpServletRequest request, HttpServletResponse response) {
        List<CcCiClassInfo> classInfoList = ciClassInterface.queryClassByCdt(new CCcCiClass());
        ControllerUtils.returnJson(request, response, classInfoList);
    }

    @RequestMapping("/getCiClassInfo")
    public void getCiClassInfo(HttpServletRequest request, HttpServletResponse response, @RequestBody JSONObject body) throws Exception {
    	if (body.containsKey("name") && body.getString("name")!=null && !"".equals(body.getString("name").trim())) {
    		CCcCiClass cdt = new CCcCiClass();
    		cdt.setClassNameEqual(body.getString("name").trim());
    		List<CcCiClassInfo> classInfoList = ciClassInterface.queryClassByCdt(cdt);
    		if (classInfoList!=null && classInfoList.size()>0) {
    			ControllerUtils.returnJson(request, response, classInfoList.get(0));
    		} else {
    			throw new Exception("ci class is not exist");
    		}
    	} else {
    		throw new Exception("lack of name");
    	}
    }

    @RequestMapping("/saveOrUpdateCiClassInfo")
    public void saveOrUpdateCiClassInfo(HttpServletRequest request, HttpServletResponse response, @RequestBody JSONObject body) throws Exception {
    	String className = null;
    	if (body.containsKey("className") && body.getString("className")!=null && !"".equals(body.getString("className").trim())) {
    		className = body.getString("className").trim();
    	} else {
    		throw new Exception("lack of className");
    	}
    	List<CcCiAttrDef> attrDefs = new ArrayList<CcCiAttrDef>();
    	boolean hasIsMajor = false;
    	if (body.containsKey("attrDefs") && body.getJSONArray("attrDefs")!=null && body.getJSONArray("attrDefs").size()>0) {
    		for (int i=0;i<body.getJSONArray("attrDefs").size();i++) {
    			JSONObject attrDefObj = body.getJSONArray("attrDefs").getJSONObject(i);
    			CcCiAttrDef attrDef = new CcCiAttrDef();
    			if (attrDefObj.containsKey("proName") && attrDefObj.getString("proName")!=null && !"".equals(attrDefObj.getString("proName").trim())) {
    				attrDef.setProName(attrDefObj.getString("proName").trim());
    			} else {
    				throw new Exception("attrDef is lack of proName");
    			}
    			if (attrDefObj.containsKey("proType") && attrDefObj.getInteger("proType")!=null) {
    				attrDef.setProType(attrDefObj.getInteger("proType"));
    			} else {
    				throw new Exception("attrDef is lack of proType");
    			}
    			if (attrDefObj.containsKey("isMajor") && attrDefObj.getBoolean("isMajor")!=null) {
    				if (attrDefObj.getBoolean("isMajor")) {
    					attrDef.setIsMajor(1);
    					hasIsMajor = true;
    				} else {
    					attrDef.setIsMajor(0);
    				}
    			} else {
    				throw new Exception("attrDef is lack of isMajor");
    			}
    			if (attrDefObj.containsKey("isRequired") && attrDefObj.getBoolean("isRequired")!=null) {
    				if (attrDefObj.getBoolean("isRequired")) {
    					attrDef.setIsRequired(1);
    				} else {
    					attrDef.setIsRequired(0);
    				}
    			} else {
    				throw new Exception("attrDef is lack of isRequired");
    			}
    			attrDef.setIsCiDisp(0);
    			attrDef.setDefVal("");
    			attrDefs.add(attrDef);
    		}
    	} else {
    		throw new Exception("lack of attrDefs");
    	}
    	if (hasIsMajor) {
    		CcCiClass ciClass = null;
    		CCcCiClass cdt = new CCcCiClass();
    		cdt.setClassNameEqual(className);
			List<CcCiClassInfo> classInfos = ciClassInterface.queryClassByCdt(cdt);
			if (classInfos!=null && classInfos.size()>0) {
				ciClass = classInfos.get(0).getCiClass();
				Map<String, CcCiAttrDef> oldAttrDefMap = new HashMap<String, CcCiAttrDef>();
				for (CcCiAttrDef oldAttrDef:classInfos.get(0).getAttrDefs()) {
					oldAttrDefMap.put(oldAttrDef.getProName(), oldAttrDef);
				}
				List<CcCiAttrDef> newAttrDefs = new ArrayList<CcCiAttrDef>();
				for (CcCiAttrDef attrDef:attrDefs) {
					CcCiAttrDef oldAttrDef = oldAttrDefMap.get(attrDef.getProName());
					if (oldAttrDef!=null) {
						oldAttrDef.setProName(attrDef.getProName());
						oldAttrDef.setProType(attrDef.getProType());
						oldAttrDef.setIsMajor(attrDef.getIsMajor());
						oldAttrDef.setIsRequired(attrDef.getIsRequired());
						newAttrDefs.add(oldAttrDef);
					} else {
						newAttrDefs.add(attrDef);
					}
				}
				attrDefs = newAttrDefs;
			} else {
				ciClass = new CcCiClass();
				ciClass.setClassName(className);
				ciClass.setClassCode(className);
				ciClass.setCiType(1);
				ciClass.setParentId(0L);
				ciClass.setIcon(ciClassInterface.getHttpResourceSpace() + "/122/default_icon.png");
				CCcCiClassDir dirCdt = new CCcCiClassDir();
				String dirName = "图森破";
				dirCdt.setDirNameEqual(dirName);
				List<CcCiClassDir> dirs = dirApiSvc.queryDirList(dirCdt, null, true);
				if (dirs!=null && dirs.size()>0) {
					ciClass.setDirId(dirs.get(0).getId());
				} else {
					CcCiClassDir dir = new CcCiClassDir();
					dir.setDirName(dirName);
					dir.setCiType(1);
					dir.setDirDesc("");
					dir.setParentId(0L);
					ciClass.setDirId(dirApiSvc.saveOrUpdateDir(dir));
				}
			}

			CcCiClassInfo classInfo = new CcCiClassInfo();
			classInfo.setCiClass(ciClass);
			classInfo.setAttrDefs(attrDefs);
			ciClassInterface.saveOrUpdateCIClass(classInfo);
			ControllerUtils.returnJson(request, response, true);
    	} else {
    		throw new Exception("lack of isMajor");
    	}
    }

    @RequestMapping("/deleteCiClassInfo")
    public void deleteCiClassInfo(HttpServletRequest request, HttpServletResponse response, @RequestBody JSONObject body) throws Exception {
    	if (body.containsKey("className") && body.getString("className")!=null && !"".equals(body.getString("className").trim())) {
    		CCcCiClass cdt = new CCcCiClass();
    		cdt.setClassNameEqual(body.getString("className").trim());
			List<CcCiClassInfo> classInfos = ciClassInterface.queryCiClassInfoList(1L, cdt, "", true);
			if (classInfos!=null && classInfos.size()>0) {
				Long classId = classInfos.get(0).getCiClass().getId();
				if (ciClassInterface.removeCIClassById(classId)>0) {
					ControllerUtils.returnJson(request, response, true);
				} else {
					throw new Exception("fail to delete ci class");
				}
			} else {
				throw new Exception("ci class is not exist");
			}
    	} else {
    		throw new Exception("lack of className");
    	}
    }

    public static class DixCIClass extends CcCiClassInfo {

        private static final long serialVersionUID = 1L;

        private String dirName;

        public String getDirName() {
            return dirName;
        }

        public void setDirName(String dirName) {
            this.dirName = dirName;
        }
    }

    private Record transToRecord(CcCiObj ccCiObj) {
        Record result = new Record();
        result.setId(ccCiObj.getCi().getId());
        result.setClassId(ccCiObj.getCi().getClassId());
        result.setCiCode(ccCiObj.getCi().getCiCode());
        result.setCiPrimaryKey(ccCiObj.getCi().getCiPrimaryKey());
        result.setHashCode(ccCiObj.getCi().getHashCode());
        result.setObject(ccCiObj.getAttrs());
        result.setTime(ccCiObj.getCi().getModifyTime());
        return result;
    }

    private Record transToRecords(CcCiClassInfo ciClassInfo, CcCiObj ccCiObj) {
        if (ciClassInfo == null) { return null; }
        Record result = new Record();
        result.setId(ccCiObj.getCi().getId());
        result.setClassId(ccCiObj.getCi().getClassId());
        result.setClassName(ciClassInfo.getCiClass().getClassName());
        result.setCiCode(ccCiObj.getCi().getCiCode());
        result.setCiPrimaryKey(ccCiObj.getCi().getCiPrimaryKey());
        result.setHashCode(ccCiObj.getCi().getHashCode());
        result.setObject(ccCiObj.getAttrs());
        result.setTime(ccCiObj.getCi().getModifyTime());
        return result;
    }

    private Page<Record> transToRecords(Page<CcCiInfo> ciPage) {
        List<CcCiInfo> data = ciPage.getData();
        List<Record> records = new ArrayList<>();
        data.forEach(n -> {
            Record record = new Record();
            record.setId(n.getCi().getId());
            record.setClassId(n.getCi().getClassId());
//            record.setClassName(n.getCiClass().getClassName());
            record.setCiCode(n.getCi().getCiCode());
            record.setCiPrimaryKey(n.getCi().getCiPrimaryKey());
            record.setHashCode(n.getCi().getHashCode());
            record.setObject(n.getAttrs());
            record.setTime(n.getCi().getModifyTime());
            records.add(record);
        });
        return new Page<>(ciPage.getPageNum(), ciPage.getPageSize(), ciPage.getTotalRows(), ciPage.getTotalPages(),
                records);
    }

    @RequestMapping("/queryPageByIndex")
    @ModDesc(desc = "分页查询对象数据", pDesc = "条件查询", pType = QueryPageCondition.class, pcType = CiQueryCdt.class, rDesc = "对象数据", rType = CiGroupPage.class)
    public void queryPageByIndex(HttpServletRequest request, HttpServletResponse response, @RequestBody String body) {
        QueryPageCondition<CiQueryCdt> pageCdt = RestTypeUtil.toPageCondition(body, CiQueryCdt.class);
        CiGroupPage result = ciInterface.queryPageByIndex(pageCdt.getPageNum(), pageCdt.getPageSize(), pageCdt.getCdt(),
                false);
        ControllerUtils.returnJson(request, response, result);
    }

    @RequestMapping("/removeByPrimaryKeys")
    @ModDesc(desc = "通过业务主键批量删除对象", pDesc = "业务主键", pType = List.class, pcType = Long.class, rDesc = "1成功0失败", rType = Integer.class)
    public void removeByIds(HttpServletRequest request, HttpServletResponse response,
            @RequestBody List<String> ciPrimaryKeys) {
        Integer result = ciInterface.removeByPrimaryKeys(ciPrimaryKeys, 3L);
        ControllerUtils.returnJson(request, response, result);
    }

    @RequestMapping("/deleteCis")
    @ModDesc(desc = "通过业务主键批量删除对象", pDesc = "业务主键", pType = List.class, pcType = Long.class, rDesc = "1成功0失败", rType = Integer.class)
    public void deleteCis(HttpServletRequest request, HttpServletResponse response,
            @RequestBody List<List<String>> ciPks) {
        List<String> ciPrimaryKeys = new ArrayList<String>();
        for (List<String> pk : ciPks) {
            JSONArray pkJson = new JSONArray();
            for (String p : pk) {
                pkJson.add(p);
            }
            ciPrimaryKeys.add(pkJson.toString());
        }
        Integer result = ciInterface.removeByPrimaryKeys(ciPrimaryKeys, 3L);
        JSONObject ret = new JSONObject();
        if (result > 0) {
            ret.put("delete", ciPks);
        } else {
            ret.put("delete", new JSONArray());
        }
        ControllerUtils.returnJson(request, response, ret);
    }

    @RequestMapping("/deleteCiByClassNameAndMajorAttr")
    @ModDesc(desc = "通过分类名称和主键属性删除ci数据", pDesc = "业务主键", pType = List.class, pcType = Long.class, rDesc = "1成功0失败", rType = Integer.class)
    public void deleteCiByClassNameAndMajorAttr(HttpServletRequest request, HttpServletResponse response,
                          @RequestBody String body) {
        JSONObject paramObj = JSON.parseObject(body);
        String className = paramObj.getString(CLASS_NAME);
        BinaryUtils.checkEmpty(className, CLASS_NAME);
        List<Map<String, String>> datas  = (List<Map<String, String>>) paramObj.get(CI_LIST);
        BinaryUtils.checkEmpty(datas, CI_LIST);
        CCcCiClass classCdt = new CCcCiClass();
        classCdt.setClassNameEqual(className);
        List<CcCiClassInfo> clsInfoList = ciClassInterface.queryClassByCdt(classCdt);
        if(CollectionUtils.isEmpty(clsInfoList)){
            throw new RuntimeException("请检查分类名是否正确："+className);
        }
        CcCiClassInfo ccCiClassInfo = clsInfoList.get(0);
        List<CcCiAttrDef> attrDefs = ccCiClassInfo.getAttrDefs();

        List<String> ciPKAttrDefNames = CommUtil.getCiPKAttrDefNames(attrDefs);
        String classCode = ccCiClassInfo.getCiClass().getClassCode();
        List<List<String>> ciPks = new ArrayList<>();
        for (Map<String, String> attrs : datas) {
            List<String> ciPrimaryKeys = CommUtil.getCiPrimaryKeys(classCode, attrs, ciPKAttrDefNames);
            ciPks.add(ciPrimaryKeys);
        }
        deleteCis(request,response,ciPks);
    }


    @RequestMapping("/searchCIByBean")
    @ModDesc(desc = "条件搜索对象数据", pDesc = "查询条件", pType = ESCISearchBean.class, rDesc = "对象数据", rType = CcCiSearchPage.class)
    public void searchCIByBean(@RequestBody ESCISearchBean bean, HttpServletRequest request,
            HttpServletResponse response) {
        CcCiSearchPage res = ciInterface.searchCIByBean(bean);
        ControllerUtils.returnJson(request, response, res);
    }

    @RequestMapping("/searchCI")
    @ModDesc(desc = "条件搜索对象数据", pDesc = "查询条件", pType = ESCISearchBean.class, rDesc = "对象数据", rType = CcCiSearchPage.class)
    public void searchCI(@RequestBody JSONObject body, HttpServletRequest request, HttpServletResponse response) {
        String className = body.getString("className");
        ESCISearchBean bean = body.getObject("bean", ESCISearchBean.class);
        if (className != null) {
            CCcCiClass cdt = new CCcCiClass();
            cdt.setClassName(className);
            List<CcCiClassInfo> ccCiClassInfos = ciClassInterface.queryClassByCdt(cdt);
            if (ccCiClassInfos == null || ccCiClassInfos.isEmpty()) { throw new RuntimeException("分类不存在"); }
            bean.getClassIds().add(ccCiClassInfos.get(0).getCiClass().getId());
        }
        CcCiSearchPage res = ciInterface.searchCIByBean(bean);
        ControllerUtils.returnJson(request, response, res);
    }
}
