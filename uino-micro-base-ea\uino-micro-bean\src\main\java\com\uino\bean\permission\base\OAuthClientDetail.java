package com.uino.bean.permission.base;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Set;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 客户端详情
 * 
 * <AUTHOR>
 *
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OAuthClientDetail implements Serializable {
	private static final long serialVersionUID = 1L;
	/**
	 * 主键
	 */
	private Long id;
	/**
	 * 客户端code
	 */
	private String clientCode;

	/**
	 * 是否验证该客户端密钥凭据
	 */
	@Builder.Default
	private boolean secretRequired = true;

	/**
	 * 客户端密钥-该客户端请求认证时所需携带密钥，存储加密后的
	 */
	private String clientSecret;

	/**
	 * 客户端code换token-url地址
	 */
	private Set<String> codeToTokenUrl;

	/**
	 * 刷新token有效期时间
	 */
	private Long refreshTokenValiditySeconds;

	/**
	 * token有效期
	 */
	private Long accessTokenValiditySeconds;

	/**
	 * 是否验证客户端可用范围
	 */
	@Builder.Default
	private boolean scoped = true;

	/**
	 * 该客户端可用范围
	 */
	private Set<String> scopes;

	/**
	 * 是否自动批准授权范围
	 */
	@Builder.Default
	private boolean autoApprove = true;

	/**
	 * 额外属性
	 */
	private Map<String, Object> attrs;

	/**
	 * 客户端可访问资源
	 */
	private Set<String> resourceIds;

	/**
	 * 该客户端所持有的权限
	 */
	private List<String> athorities;

	/**
	 * 客户端可用授权类型
	 */
	private Set<String> authorizedGrantTypes;

	/** 所属域 */
	private Long domainId;

	/** 创建人 */
	private String creator;

	/** 修改人 */
	private String modifier;

	/** 创建时间 */
	private Long createTime;

	/** 修改时间 */
	private Long modifyTime;
}
