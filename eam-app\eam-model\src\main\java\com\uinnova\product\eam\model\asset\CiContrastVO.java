package com.uinnova.product.eam.model.asset;

import com.uinnova.product.vmdb.comm.model.ci.CcCi;
import com.uinnova.product.vmdb.comm.model.rlt.CcCiRlt;
import com.uino.bean.cmdb.base.ESCIClassInfo;
import lombok.Data;

import java.util.List;

@Data
public class CiContrastVO {
    /**
     * 类型 1：新增 2：修改 3：删除
     */
    private Integer operate;

    /**
     * 1: ci 2：rlt
     */
    private Integer classType;

    /**
     * ci分类信息
     */
    private ESCIClassInfo classInfo;

    private String name;

    private CcCi ciInfo;
    private CcCiRlt rltInfo;

    /**
     *
     *     changeAttr:[{
     *          :"名称"
     *         before: 1,
     *         after:2
     *     }]
     */
    private List<AssetChangeAttrVO> changeAttr;

    private String firstKey;
    private String secondKey;


}
