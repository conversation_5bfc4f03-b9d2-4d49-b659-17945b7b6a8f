package com.uinnova.product.eam.service.utils;

import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSONObject;
import com.binary.core.util.BinaryUtils;
import com.uinnova.product.eam.model.cj.domain.PlanDesignInstance;
import com.uinnova.product.eam.model.cj.domain.TemplateType;
import com.uinnova.product.eam.model.cj.vo.BindAssetVo;
import com.uinnova.product.eam.model.cj.vo.DlvrTemplateVO;
import com.uinnova.product.eam.service.ICISwitchSvc;
import com.uinnova.product.eam.service.cj.dao.PlanDesignInstanceDao;
import com.uinnova.product.eam.service.cj.dao.TemplateTypeDao;
import com.uinnova.product.eam.service.cj.service.DeliverableTemplateService;
import com.uinnova.product.vmdb.comm.model.ci.CcCi;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CiGroupPage;
import com.uino.bean.cmdb.base.LibType;
import com.uino.bean.cmdb.query.ESCISearchBean;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Component
@Slf4j
public class ConvertBusinessKeyUtil implements ApplicationRunner {


    @Resource
    private PlanDesignInstanceDao planDesignInstanceDao;

    @Autowired
    private TemplateTypeDao templateTypeDao;

    @Autowired
    private DeliverableTemplateService deliverableTemplateService;

    @Autowired
    private ICISwitchSvc ciSwitchSvc;

    private ConcurrentHashMap<Long, TemplateType> templateTypeMap = new ConcurrentHashMap<>();

    private ConcurrentHashMap<Long, DlvrTemplateVO> templateMap = new ConcurrentHashMap<>();

    private ConcurrentHashMap<String,CiGroupPage> ciGroupPageConcurrentHashMap = new ConcurrentHashMap<>();

    // 是否启动时刷新方案的businessKey
    @Value("${plan.businesskey.flush:false}")
    private String flush;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("转换businessKey: " + flush);
        if(Boolean.parseBoolean(flush)){
            convert();
        }
    }

    private void convert() {
        List<PlanDesignInstance> listByQuery = planDesignInstanceDao.getListByQuery(null);
        if (!CollectionUtils.isEmpty(listByQuery)) {
            listByQuery.forEach(planDesignInstance -> {
                BusinessKey businessKeyMsg = new BusinessKey();
                businessKeyMsg.setPlanId(planDesignInstance.getId());
                businessKeyMsg.setBusinessKey(planDesignInstance.getBusinessKey());
                String businessKey = createBusinessKey(planDesignInstance);
                businessKeyMsg.setBusinessKeyMd5(businessKey);
                planDesignInstance.setBusinessKey(businessKey);
                log.info("【BusinessKey】 转换前后：【" + businessKeyMsg.toString() + "】");
            });
            planDesignInstanceDao.saveOrUpdateBatch(listByQuery);
        }
    }

    static class BusinessKey{

        private Long planId;
        private String businessKey;
        private String businessKeyMd5;

        public Long getPlanId() {
            return planId;
        }

        public void setPlanId(Long planId) {
            this.planId = planId;
        }
        public String getBusinessKey() {
            return businessKey;
        }

        public void setBusinessKey(String businessKey) {
            this.businessKey = businessKey;
        }

        public String getBusinessKeyMd5() {
            return businessKeyMd5;
        }

        public void setBusinessKeyMd5(String businessKeyMd5) {
            this.businessKeyMd5 = businessKeyMd5;
        }

        @Override
        public String toString() {
            return "BusinessKey{" +
                    "planId=" + planId + '\'' +
                    ", businessKey='" + businessKey + '\'' +
                    ", businessKeyMd5='" + businessKeyMd5 + '\'' +
                    '}';
        }
    }

    private String createBusinessKey(PlanDesignInstance planDesignInstance) {
        StringBuilder businessStr = new StringBuilder();

        // 根据 typeId 获取模板类型名称
        TemplateType templateType = getTemplateType(planDesignInstance.getTypeId());
        if (templateType != null && !StringUtils.isEmpty(templateType.getTypeName())) {
            businessStr.append(templateType.getTypeName());
        }

        // 检查templateId是否存在，并获取模板名称和分类名
        String className = "";
        Long templateId = planDesignInstance.getTemplateId();
        if (templateId != null) {
            DlvrTemplateVO dlvrTemplateById = getDlvrTemplateById(templateId);
            if (dlvrTemplateById != null) {
                businessStr.append(dlvrTemplateById.getTemplateName());
                if (!org.apache.commons.collections4.CollectionUtils.isEmpty(dlvrTemplateById.getBindAssetList())) {
                    Map<Long, String> bindAssetMap = dlvrTemplateById.getBindAssetList().stream()
                            .collect(Collectors.toMap(BindAssetVo::getClassId, BindAssetVo::getClassName));
                    className = bindAssetMap.get(planDesignInstance.getDefaultSystemClassId());
                }
            }
        }

        //拼接分类名
        if (!BinaryUtils.isEmpty(className)) {
            businessStr.append(className);
        }

        if(!BinaryUtils.isEmpty(planDesignInstance.getDefaultSystemCiCode()) &&
                !BinaryUtils.isEmpty(planDesignInstance.getDefaultSystemClassId())){
            ESCISearchBean ciSearchBean = setupCiSearchBean(planDesignInstance);
            CiGroupPage ciGroupPage = getCiGroupPage(ciSearchBean, planDesignInstance.getDefaultSystemClassId(), planDesignInstance.getDefaultSystemCiCode());
            // 处理查询结果
            if (!org.apache.commons.collections4.CollectionUtils.isEmpty(ciGroupPage.getData())) {
                processCiGroupPageData(businessStr, ciGroupPage);
            }
        }

        // 添加实例名称并返回MD5加密后的业务键
        businessStr.append(planDesignInstance.getName());
        System.out.println("【businessStr】：" + businessStr);
        return SecureUtil.md5(businessStr.toString());
    }

    private ESCISearchBean setupCiSearchBean(PlanDesignInstance planDesignInstance) {
        ESCISearchBean ciSearchBean = new ESCISearchBean();
        ciSearchBean.setDomainId(1L);
        List<String> ciCodes = new ArrayList<>();
        ciCodes.add(planDesignInstance.getDefaultSystemCiCode());
        ciSearchBean.setCiCodes(ciCodes);
        List<Long> classList = new ArrayList<>();
        classList.add(planDesignInstance.getDefaultSystemClassId());
        ciSearchBean.setClassIds(classList);
        return ciSearchBean;
    }

    private TemplateType getTemplateType(Long typeId) {
        if(BinaryUtils.isEmpty(templateTypeMap.get(typeId))){
            TemplateType templateType = templateTypeDao.getById(typeId);
            templateTypeMap.put(typeId, templateType);
            return templateType;
        }
        return templateTypeMap.get(typeId);
    }

    private DlvrTemplateVO getDlvrTemplateById(Long templateId) {
        if(BinaryUtils.isEmpty(templateMap.get(templateId))){
            DlvrTemplateVO dlvrTemplateById = deliverableTemplateService.getDlvrTemplateById(templateId);
            templateMap.put(templateId, dlvrTemplateById);
            return dlvrTemplateById;

        }
        return templateMap.get(templateId);
    }

    private CiGroupPage getCiGroupPage(ESCISearchBean ciSearchBean, Long classId, String ciCode) {
        if(BinaryUtils.isEmpty(ciGroupPageConcurrentHashMap.get(classId + "-" + ciCode))){
            CiGroupPage ciGroupPage = ciSwitchSvc.queryPageBySearchBean(ciSearchBean, false, LibType.DESIGN);
            if(ciGroupPage != null){
                ciGroupPageConcurrentHashMap.put(classId + "-" + ciCode, ciGroupPage);
            }
            return ciGroupPage;
        }
        return ciGroupPageConcurrentHashMap.get(classId + "-" + ciCode);
    }

    private void processCiGroupPageData(StringBuilder businessStr, CiGroupPage ciGroupPage) {
        CcCiInfo ccCiInfo = ciGroupPage.getData().get(0);
        String systemName = getSysName(ccCiInfo.getCi());
        if (!BinaryUtils.isEmpty(systemName)) {
            businessStr.append(systemName);
        }
    }

    private String getSysName(CcCi ci) {
        String ciLabel = ci.getCiLabel();
        List<String> datas = JSONObject.parseArray(ciLabel, String.class);
        if (!BinaryUtils.isEmpty(ciLabel) && !CollectionUtils.isEmpty(datas)) {
            String[] strings1 = datas.toArray(new String[0]);
            return StringUtils.join(strings1, ",");
        }

        return formatSysName(ci.getCiPrimaryKey());
    }

    private String formatSysName(String ciPrimaryKey) {
        List<String> strings = JSONObject.parseArray(ciPrimaryKey, String.class);
        if (!CollectionUtils.isEmpty(strings)) {
            strings.remove(0);
            String[] strings1 = strings.toArray(new String[0]);
            return StringUtils.join(strings1, ",");
        }
        return null;
    }
}
