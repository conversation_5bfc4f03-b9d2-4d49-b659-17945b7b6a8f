package com.uino.bean.cmdb.base;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.io.Serializable;

/**
 * CI分类编码字段类型vo
 * <AUTHOR>
 */
@Data
public class ESCiClassEncode implements Serializable {

    @Comment("ID")
    private Long id;

    @Comment("所属域id")
    private Long domainId;

    @Comment("分类标识")
    private String classCode;

    @Comment("属性名称")
    private String proName;

    @Comment("最大值")
    private Long maxNum;

    @Comment("分库:私有库设计库用同一个,运行库另算")
    private String libType;

    @Comment("创建时间")
    private Long createTime;

    @Comment("修改时间")
    private Long modifyTime;
}
