package com.uinnova.product.eam.comm.model;




import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;


@Comment("节点路径详情表[VC_NODE_LINKED_PATH]")
public class VcNodeLinkedPath implements EntityBean {
	private static final long serialVersionUID = 1L;


	@Comment("ID[ID]")
	private Long id;


	@Comment("节点路径ID[LINKED_ID]")
	private Long linkedId;


	@Comment("节点代码列表[NODE_CODES]    节点代码列表用'隔开")
	private String nodeCodes;


	@Comment("节点列表序号[PATH_ORDER]")
	private Integer pathOrder;


	@Comment("所属域[DOMAIN_ID]")
	private Long domainId;


	@Comment("创建时间[CREATE_TIME]    创建时间:yyyyMMddHHmmss")
	private Long createTime;


	@Comment("修改时间[MODIFY_TIME]    修改时间:yyyyMMddHHmmss")
	private Long modifyTime;




	public Long getId() {
		return this.id;
	}
	public void setId(Long id) {
		this.id = id;
	}


	public Long getLinkedId() {
		return this.linkedId;
	}
	public void setLinkedId(Long linkedId) {
		this.linkedId = linkedId;
	}


	public String getNodeCodes() {
		return this.nodeCodes;
	}
	public void setNodeCodes(String nodeCodes) {
		this.nodeCodes = nodeCodes;
	}


	public Integer getPathOrder() {
		return this.pathOrder;
	}
	public void setPathOrder(Integer pathOrder) {
		this.pathOrder = pathOrder;
	}


	public Long getDomainId() {
		return this.domainId;
	}
	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}


	public Long getCreateTime() {
		return this.createTime;
	}
	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}


	public Long getModifyTime() {
		return this.modifyTime;
	}
	public void setModifyTime(Long modifyTime) {
		this.modifyTime = modifyTime;
	}


}


