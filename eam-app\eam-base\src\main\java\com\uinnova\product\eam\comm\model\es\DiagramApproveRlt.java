package com.uinnova.product.eam.comm.model.es;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.util.List;

@Data
@Comment("视图审批关联表[uino_diagram_approve_rlt]")
public class DiagramApproveRlt {
    @Comment("业务主键id")
    private Long id;
    @Comment("数据状态 0=流程结束，1=驳回，2=流程中")
    private Integer flowStatus;
    @Comment("模型树ID")
    private Long modelId;
    @Comment("模型工艺类型 1=数据建模 2=业务建模")
    private Integer modelType;
    @Comment("提交人标识")
    private String ownerCode;
    @Comment("模型审批顶级目录")
    private Long approveRootDirId;
    @Comment("提交审批的模型目录信息集合")
    private List<Long> approvelDirList;
    @Comment("提交审批的视图加密信息集合")
    private List<String> dEnergysList;
    @Comment("提交审批的视图信息集合")
    private List<Long> diagramIdList;
    @Comment("发布位置")
    private Long targetDirId;
    @Comment("发布描述")
    private String releaseDesc;
    @Comment("是否为模型的单图提交 true=是 false=否")
    private Boolean isSingle;
    @Comment("模型单图审批视图ID")
    private String approveDiagramId;
}


