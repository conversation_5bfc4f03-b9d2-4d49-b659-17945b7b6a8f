package com.uinnova.product.eam.web.config.bean;

import java.util.Collections;
import java.util.LinkedList;
import java.util.List;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

/**
 * it世界地图配置传输类
 * <AUTHOR>
 *
 */
public class ItMapConfigDto {
	/**
	 * 地图视图id
	 */
	private Long diagramId;
	/**
	 * 地图层级
	 */
	private List<MapLevel> levels;
	
	
	
	public Long getDiagramId() {
		return diagramId;
	}



	public void setDiagramId(Long diagramId) {
		this.diagramId = diagramId;
	}



	public List<MapLevel> getLevels() {
		return levels;
	}



	public void setLevels(List<MapLevel> levels) {
		this.levels = levels;
	}
	
	public void setLevels(JSONArray levels) {
		List<MapLevel> newLevels=new LinkedList<>();
		for(int index=0;index<levels.size();index++){
			MapLevel newObj=new MapLevel();
			JSONObject levelObj= levels.getJSONObject(index);
			newObj.setLevel(levelObj.getIntValue("level"));
			newObj.setClassName(levelObj.getString("className"));
			newObj.setRltName(levelObj.getString("rltName"));
			newLevels.add(newObj);
		}
		Collections.sort(newLevels);
		this.levels = newLevels;
	}



	public class MapLevel implements Comparable<MapLevel>{
		private int level;
		private String className;
		private String rltName;
		
		public int getLevel() {
			return level;
		}

		public void setLevel(int level) {
			this.level = level;
		}

		public String getClassName() {
			return className;
		}

		public void setClassName(String className) {
			this.className = className;
		}

		public String getRltName() {
			return rltName;
		}

		public void setRltName(String rltName) {
			this.rltName = rltName;
		}

		@Override
		public int compareTo(MapLevel o) {
			if(level<o.getLevel())return -1;
			if(level>o.getLevel())return 1;
			return 0;
		}
		
		
	}
}
