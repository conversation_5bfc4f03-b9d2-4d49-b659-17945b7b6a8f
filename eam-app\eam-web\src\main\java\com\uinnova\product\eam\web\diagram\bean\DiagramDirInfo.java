package com.uinnova.product.eam.web.diagram.bean;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

import com.binary.framework.bean.annotation.Comment;
import com.binary.jdbc.Page;
import com.uinnova.product.eam.base.diagram.model.ESSimpleDiagramDTO;
import com.uinnova.product.eam.comm.model.VcDiagramDir;
import com.uinnova.product.eam.model.VcDiagramInfo;
import com.uinnova.product.eam.model.vo.VcDiagramDirVo;
import com.uinnova.product.vmdb.provider.ci.bean.CiGroupPage;

@Comment("查询文件夹和视图信息")
public class DiagramDirInfo implements Serializable {
	private static final long serialVersionUID = 1L;

	@Comment("当前文件夹信息")
	private VcDiagramDir diagramDir;
	
	@Comment("子文件夹信息")
	private List<VcDiagramDir> childrenDirs;
	
	@Comment("子文件夹下是否有节点")
	private Map<Long,Boolean> dirHasNodeMap;
	
	@Comment("子文件夹视图数量信息")
	private Map<Long,Integer> dirDiagramCountMap;

	@Comment("视图信息，不分页时使用")
	private List<VcDiagramInfo> diagramInfos;
	
	@Comment("视图信息，分页时使用")
	private Page<VcDiagramInfo> diagramInfoPage;
	
	@Comment("我提交的视图")
	private List<VcDiagramInfo> myCommits;
	
	@Comment("我需要操作的视图")
	private List<VcDiagramInfo> needMeOpDiagrams;
	
	@Comment("我已经操作过的视图，比如我审批过的")
	private List<VcDiagramInfo> myOpDiagramsNotMine;

	@Comment("子文件夹信息")
	private List<VcDiagramDirVo> childrenDirList;

	@Comment("系统详情")
	private CiGroupPage ciGroupPage;

	private Page<ESSimpleDiagramDTO> esDiagramPage;

	public List<VcDiagramInfo> getMyOpDiagramsNotMine() {
		return myOpDiagramsNotMine;
	}

	public void setMyOpDiagramsNotMine(List<VcDiagramInfo> myOpDiagramsNotMine) {
		this.myOpDiagramsNotMine = myOpDiagramsNotMine;
	}

	public List<VcDiagramInfo> getMyCommits() {
		return myCommits;
	}

	public void setMyCommits(List<VcDiagramInfo> myCommits) {
		this.myCommits = myCommits;
	}

	public List<VcDiagramInfo> getNeedMeOpDiagrams() {
		return needMeOpDiagrams;
	}

	public void setNeedMeOpDiagrams(List<VcDiagramInfo> needMeOpDiagrams) {
		this.needMeOpDiagrams = needMeOpDiagrams;
	}

	public Map<Long, Integer> getDirDiagramCountMap() {
		return dirDiagramCountMap;
	}

	public void setDirDiagramCountMap(Map<Long, Integer> dirDiagramCountMap) {
		this.dirDiagramCountMap = dirDiagramCountMap;
	}

	public VcDiagramDir getDiagramDir() {
		return diagramDir;
	}

	public void setDiagramDir(VcDiagramDir diagramDir) {
		this.diagramDir = diagramDir;
	}

	public List<VcDiagramDir> getChildrenDirs() {
		return childrenDirs;
	}

	public void setChildrenDirs(List<VcDiagramDir> childrenDirs) {
		this.childrenDirs = childrenDirs;
	}

	public List<VcDiagramInfo> getDiagramInfos() {
		return diagramInfos;
	}

	public void setDiagramInfos(List<VcDiagramInfo> diagramInfos) {
		this.diagramInfos = diagramInfos;
	}

	public Page<VcDiagramInfo> getDiagramInfoPage() {
		return diagramInfoPage;
	}

	public void setDiagramInfoPage(Page<VcDiagramInfo> diagramInfoPage) {
		this.diagramInfoPage = diagramInfoPage;
	}

	public Map<Long, Boolean> getDirHasNodeMap() {
		return dirHasNodeMap;
	}

	public void setDirHasNodeMap(Map<Long, Boolean> dirHasNodeMap) {
		this.dirHasNodeMap = dirHasNodeMap;
	}


	public List<VcDiagramDirVo> getChildrenDirList() {
		return childrenDirList;
	}

	public void setChildrenDirList(List<VcDiagramDirVo> childrenDirList) {
		this.childrenDirList = childrenDirList;
	}

	public CiGroupPage getCiGroupPage() {
		return ciGroupPage;
	}

	public void setCiGroupPage(CiGroupPage ciGroupPage) {
		this.ciGroupPage = ciGroupPage;
	}

	public Page<ESSimpleDiagramDTO> getEsDiagramPage() {
		return esDiagramPage;
	}

	public void setEsDiagramPage(Page<ESSimpleDiagramDTO> esDiagramPage) {
		this.esDiagramPage = esDiagramPage;
	}

}
