package com.uinnova.product.eam.model;


import com.binary.framework.bean.annotation.Comment;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 *
 * 末级流程图复用
 * description
 *
 * <AUTHOR>
 * @since 2024/12/03 15:50
 */
@Data
public class WorkbenchChargeDoneDto {

    @ApiModelProperty(value = "页码",required = true)
    private Integer pageNum;

    @ApiModelProperty(value = "页面数量",required = true)
    private Integer pageSize;

    @ApiModelProperty(value = "操作(1、待办，2、已办)",required = true)
    private Integer action;

    @ApiModelProperty(value = "标题")
    private String title;

    @Comment("1:表示工作流(方案审批) 2：表示消息(视图更新) 3：表示视图审批 4:模型审批 5:表示填写流程任务  6:流程审批  7：流程签发")
    @ApiModelProperty(value = "类型")
    private String types;
}
