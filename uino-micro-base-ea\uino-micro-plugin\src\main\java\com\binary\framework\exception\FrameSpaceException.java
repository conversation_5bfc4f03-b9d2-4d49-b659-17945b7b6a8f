package com.binary.framework.exception;


public class FrameSpaceException extends FrameworkException {
	private static final long serialVersionUID = 1L;

	public FrameSpaceException() {
		super();
	}
	
	public FrameSpaceException(String message) {
		super(message);
	}
	
	public FrameSpaceException(Throwable cause) {
		super(cause);
	}
	
	public FrameSpaceException(String message, Throwable cause) {
		super(message, cause);
	}
	
	
}


