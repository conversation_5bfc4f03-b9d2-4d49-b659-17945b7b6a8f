package com.uinnova.product.eam.model.dm.bean;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uino.bean.cmdb.base.ESCIInfo;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description 数据标准列表返回值
 */
@Data
public class DataStandardTypeDto extends ESCIInfo {

    @Comment("ci名称")
    private String standClassName;

    @Comment("所束父级coCode")
    private String parentCode;

    @Comment("该层级下数据标准的数据个数")
    private Integer count = 0;

    @Comment("所在层级")
    private Integer lvl;

    @Comment("是否是文件夹")
    private Boolean isDir = false;

    @Comment("分类属性定义")
    private List<CcCiAttrDef> attrDefs;

    @Comment("分类的shape")
    private String shape;

    @Comment("子层级")
    private List<DataStandardTypeDto> childList = new ArrayList<>();

}
