package com.uinnova.product.eam.service;

import com.uinnova.product.eam.comm.model.es.ModelPanorama3D;

import java.util.Collection;
import java.util.Map;

/**
 * 元模型3D全景配置服务接口
 * <AUTHOR>
 */
public interface IModelPanorama3DSvc {

    /**
     * 保存元模型3D全景配置
     * @param modelPanorama3D 元模型3D全景配置
     * @return 保存结果ID
     */
    Long saveModelPanorama3D(ModelPanorama3D modelPanorama3D);

    /**
     * 根据元模型ID查询3D全景配置
     * @param modelId 元模型ID
     * @return 元模型3D全景配置
     */
    ModelPanorama3D queryByModelId(Long modelId);

    /**
     * 根据多个元模型ID查询3D全景配置
     * @param modelIds 元模型ID列表
     * @return 元模型ID与3D全景配置的映射
     */
    Map<Long, ModelPanorama3D> queryByModelIds(Collection<Long> modelIds);
}
