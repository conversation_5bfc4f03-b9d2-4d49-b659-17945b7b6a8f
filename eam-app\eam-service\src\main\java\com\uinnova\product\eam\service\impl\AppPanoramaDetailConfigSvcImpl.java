package com.uinnova.product.eam.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.binary.core.exception.BinaryException;
import com.binary.core.util.BinaryUtils;
import com.uinnova.product.eam.comm.bean.CcCiAttrDefTapGroupConfVO;
import com.uinnova.product.eam.comm.bean.CcCiClassInfoConfVO;
import com.uinnova.product.eam.comm.model.es.AppPanoramaDetailConfig;
import com.uinnova.product.eam.comm.model.es.AppPanoramaInfo;
import com.uinnova.product.eam.comm.model.es.AssetDetailAttrConf;
import com.uinnova.product.eam.model.AppPanoramaDetailConfigVo;
import com.uinnova.product.eam.service.AppPanoramaSvc;
import com.uinnova.product.eam.service.asset.AssetDetailAttrConfSvc;
import com.uinnova.product.eam.service.es.AppPanoramaDetailConfigDao;
import com.uinnova.product.eam.service.es.AppPanoramaInfoDao;
import com.uino.bean.permission.base.SysUser;
import com.uino.util.sys.BeanUtil;
import com.uino.util.sys.SysUtil;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class AppPanoramaDetailConfigSvcImpl implements AppPanoramaSvc {

    @Resource
    AssetDetailAttrConfSvc assetDetailAttrConfSvc;

    @Resource
    AppPanoramaDetailConfigDao panoramaDetailConfigDao;

    @Resource
    AppPanoramaInfoDao panoramaInfoDao;

    @Override
    public Long saveOrUpdateDetailConf(AppPanoramaDetailConfigVo panoramaDetailConfigVo) {
        if (BinaryUtils.isEmpty(panoramaDetailConfigVo.getClassCode())) {
            throw new BinaryException("分类标识不可为空");
        }
        if (BinaryUtils.isEmpty(panoramaDetailConfigVo.getAppSquareConfId())) {
            throw new BinaryException("广场卡片ID不可为空");
        }
        // 后端手动过滤tagAttrs中的无用信息
        List<JSONObject> realJS = panoramaDetailConfigVo.getTagAttrs().stream().filter(ta -> !BinaryUtils.isEmpty(ta.get("id"))).collect(Collectors.toList());
        panoramaDetailConfigVo.setTagAttrs(realJS);
        SysUser userInfo = SysUtil.getCurrentUserInfo();
        String loginCode = userInfo.getLoginCode();
        Long domainId = userInfo.getDomainId();
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("appSquareConfId", panoramaDetailConfigVo.getAppSquareConfId()));
        query.must(QueryBuilders.termQuery("domainId", domainId));
        if (!BinaryUtils.isEmpty(panoramaDetailConfigVo.getId())) {
            query.mustNot(QueryBuilders.termQuery("id", panoramaDetailConfigVo.getId()));
        }
        AppPanoramaDetailConfig panoramaDetailConfig1 = panoramaDetailConfigDao.selectOne(query);
        if (!BinaryUtils.isEmpty(panoramaDetailConfig1)) {
            throw new BinaryException("该详情配置已存在,请进行修改");
        }

        long timeMillis = System.currentTimeMillis();
        AppPanoramaDetailConfig panoramaDetailConfig = new AppPanoramaDetailConfig();
        BeanUtil.copyProperties(panoramaDetailConfigVo, panoramaDetailConfig);
        if (BinaryUtils.isEmpty(panoramaDetailConfigVo.getId())) {
            // 新增
            panoramaDetailConfig.setCreator(loginCode);
            panoramaDetailConfig.setModifier(loginCode);
            panoramaDetailConfig.setCreateTime(timeMillis);
            panoramaDetailConfig.setModifyTime(timeMillis);
            panoramaDetailConfig.setDomainId(domainId);
        } else {
            // 修改
            panoramaDetailConfig.setModifier(loginCode);
            panoramaDetailConfig.setModifyTime(timeMillis);
            panoramaDetailConfig.setDomainId(domainId);
        }
        // 属性信息配置
        Long appSquareConfId = panoramaDetailConfigVo.getAppSquareConfId();
        AssetDetailAttrConf detailAttr = assetDetailAttrConfSvc.getDetailAttr(appSquareConfId);
        List<CcCiAttrDefTapGroupConfVO> defTapGroupConfVOList = panoramaDetailConfigVo.getDetailAttrConfVO();
        if (BinaryUtils.isEmpty(defTapGroupConfVOList)) {
            defTapGroupConfVOList = Collections.emptyList();
        }
        if (BinaryUtils.isEmpty(detailAttr)) {
            detailAttr = new AssetDetailAttrConf();
            detailAttr.setShowAttrs(defTapGroupConfVOList);
            detailAttr.setAppSquareConfId(panoramaDetailConfigVo.getAppSquareConfId());
            detailAttr.setClassCode(panoramaDetailConfigVo.getClassCode());
            detailAttr.setCreator(loginCode);
            detailAttr.setCreateTime(timeMillis);
        } else {
            detailAttr.setShowAttrs(defTapGroupConfVOList);
            detailAttr.setAppSquareConfId(panoramaDetailConfigVo.getAppSquareConfId());
            detailAttr.setClassCode(panoramaDetailConfigVo.getClassCode());
        }
        detailAttr.setDomainId(domainId);
        detailAttr.setModifier(loginCode);
        detailAttr.setModifyTime(timeMillis);
        Long detailAttrId = assetDetailAttrConfSvc.saveOrUpdateDetailAttr(detailAttr);
        panoramaDetailConfig.setDetailAttrConfId(detailAttrId);
        Long id = panoramaDetailConfigDao.saveOrUpdate(panoramaDetailConfig);
        return id;
    }

    @Override
    public AppPanoramaDetailConfigVo getDetailConf(Long appSquareConfId) {
        AppPanoramaDetailConfigVo result = new AppPanoramaDetailConfigVo();
        if (BinaryUtils.isEmpty(appSquareConfId)) {
            throw new BinaryException("资产卡片配置ID不可为空");
        }
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        Long domainId = currentUserInfo.getDomainId();
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("appSquareConfId", appSquareConfId));
        query.must(QueryBuilders.termQuery("domainId", domainId));
        AppPanoramaDetailConfig panoramaDetailConfig = panoramaDetailConfigDao.selectOne(query);
        if (BinaryUtils.isEmpty(panoramaDetailConfig)) {
            return null;
        }
        BeanUtil.copyProperties(panoramaDetailConfig, result);
        CcCiClassInfoConfVO classInfoDetailAttr = assetDetailAttrConfSvc.getClassInfoDetailAttr(appSquareConfId);
        result.setDetailAttrConfVO(classInfoDetailAttr.getShowDetailCIAttrInfo());
        return result;
    }

    @Override
    public Long saveOrUpdatePanorama(AppPanoramaInfo panoramaInfo) {
        // 校验唯一性
        if (BinaryUtils.isEmpty(panoramaInfo.getCiCode())) {
            throw new BinaryException("CiCode不可为空");
        }
        SysUser userInfo = SysUtil.getCurrentUserInfo();
        String loginCode = userInfo.getLoginCode();
        Long domainId = userInfo.getDomainId();
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("ciCode.keyword", panoramaInfo.getCiCode()));
        query.must(QueryBuilders.termQuery("domainId", domainId));
        AppPanoramaInfo appPanoramaInfo = panoramaInfoDao.selectOne(query);
        if (!BinaryUtils.isEmpty(appPanoramaInfo)) {
            panoramaInfo.setId(appPanoramaInfo.getId());
        }
        long timeMillis = System.currentTimeMillis();
        if (BinaryUtils.isEmpty(panoramaInfo.getId())) {
            // 新增
            panoramaInfo.setCreator(loginCode);
            panoramaInfo.setModifier(loginCode);
            panoramaInfo.setCreateTime(timeMillis);
        } else {
            // 修改
            panoramaInfo.setModifier(loginCode);
        }
        panoramaInfo.setModifyTime(timeMillis);
        panoramaInfo.setDomainId(domainId);
        return panoramaInfoDao.saveOrUpdate(panoramaInfo);
    }

    @Override
    public Long change(AppPanoramaInfo panoramaInfo) {
        AppPanoramaInfo appPanoramaInfo = panoramaInfoDao.getById(panoramaInfo.getId());
        if (BinaryUtils.isEmpty(appPanoramaInfo)) {
            throw new BinaryException("全景信息不存在");
        }
        SysUser userInfo = SysUtil.getCurrentUserInfo();
        String loginCode = userInfo.getLoginCode();
        long timeMillis = System.currentTimeMillis();
        // 修改
        panoramaInfo.setModifier(loginCode);
        panoramaInfo.setModifyTime(timeMillis);
        return panoramaInfoDao.saveOrUpdate(panoramaInfo);
    }

    @Override
    public AppPanoramaInfo getPanoramaInfoByCiCode(String ciCode) {
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("ciCode.keyword", ciCode));
//        query.must(QueryBuilders.termQuery("state", state));
        query.must(QueryBuilders.termQuery("domainId", SysUtil.getCurrentUserInfoNotThrow().getDomainId()));
        return panoramaInfoDao.selectOne(query);
    }
}
