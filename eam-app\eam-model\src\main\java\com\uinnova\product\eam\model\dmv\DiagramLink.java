package com.uinnova.product.eam.model.dmv;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @description:
 * @author: Lc
 * @create: 2022-04-21 13:51
 */
@Data
public class DiagramLink implements Serializable {

    private String id;

    private Integer strokeWidth;

    private String text;

    private String sourceId;

    private List<DiagramLinkLoc> points;

    private String sourceName;

    private String targetId;

    private String targetName;

    private String strokeColor;

    private Integer zOrder;

}
