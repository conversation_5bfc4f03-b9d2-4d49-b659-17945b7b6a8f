/* ====================================================================
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
==================================================================== */

package com.uinnova.product.vmdb.comm.util;

import com.binary.core.util.BinaryUtils;
import org.apache.poi.hssf.eventusermodel.EventWorkbookBuilder.SheetRecordCollectingListener;
import org.apache.poi.hssf.eventusermodel.*;
import org.apache.poi.hssf.eventusermodel.dummyrecord.LastCellOfRowDummyRecord;
import org.apache.poi.hssf.eventusermodel.dummyrecord.MissingCellDummyRecord;
import org.apache.poi.hssf.model.HSSFFormulaParser;
import org.apache.poi.hssf.record.*;
import org.apache.poi.hssf.record.Record;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.poifs.filesystem.POIFSFileSystem;

import java.io.*;
import java.util.ArrayList;
import java.util.List;

/**
 * A XLS -> CSV processor, that uses the MissingRecordAware EventModel code to ensure it outputs all columns and rows.
 * 
 * <AUTHOR> Burch
 */
public class XLSCovertCSVReader implements HSSFListener {
    private int minColumns;
    private POIFSFileSystem fs;
    private PrintStream output;

    private int lastRowNumber;

    private int lastColumnNumber;

    /** Should we output the formula, or the value it has? */
    private boolean outputFormulaValues = true;

    /** For parsing Formulas */
    private SheetRecordCollectingListener workbookBuildingListener;
    private HSSFWorkbook stubWorkbook;

    /**
     * Records we pick up as we process
     */
    private SSTRecord sstRecord;
    private FormatTrackingHSSFListener formatListener;

    /** So we known which sheet we're on */
    private BoundSheetRecord[] orderedBSRs;
    private List<BoundSheetRecord> boundSheetRecords = new ArrayList<BoundSheetRecord>();

    /**
     * For handling formulas with string results
     */
    private int nextRow;
    private int nextColumn;
    private boolean outputNextStringRecord;

    @SuppressWarnings("unused")
    private String sheetName;
    private String[] records;
    private List<String> titleRecords = new ArrayList<String>();
    private List<String[]> rows = new ArrayList<String[]>();

    public List<String[]> getRows() {
        return rows;
    }

    public void setRows(List<String[]> rows) {
        this.rows = rows;
    }

    /**
     * Creates a new XLS -> CSV converter
     * 
     * @param fs
     *            The POIFSFileSystem to process
     * @param output
     *            The PrintStream to output the CSV to
     * @param minColumns
     *            The minimum number of columns to output, or -1 for no minimum
     */
    public XLSCovertCSVReader(POIFSFileSystem fs, String sheetName, PrintStream output, int minColumns) {
        this.fs = fs;
        this.sheetName = sheetName;
        this.output = output;
        this.minColumns = minColumns;
        this.rows.clear();
    }

    /**
     * Creates a new XLS -> CSV converter
     * 
     * @param filename
     *            The file to process
     * @param minColumns
     *            The minimum number of columns to output, or -1 for no minimum
     * @throws IOException
     * @throws FileNotFoundException
     */
    public XLSCovertCSVReader(String filename, String sheetName, int minColumns) throws IOException, FileNotFoundException {
        this(new POIFSFileSystem(new FileInputStream(filename)), sheetName, System.out, minColumns);
    }

    /**
     * Initiates the processing of the XLS file to CSV
     */
    public void process() throws IOException {
        MissingRecordAwareHSSFListener listener = new MissingRecordAwareHSSFListener(this);
        formatListener = new FormatTrackingHSSFListener(listener);

        HSSFEventFactory factory = new HSSFEventFactory();
        HSSFRequest request = new HSSFRequest();

        if (outputFormulaValues) {
            request.addListenerForAllRecords(formatListener);
        } else {
            workbookBuildingListener = new SheetRecordCollectingListener(formatListener);
            request.addListenerForAllRecords(workbookBuildingListener);
        }

        factory.processWorkbookEvents(request, fs);
    }

    /**
     * Main HSSFListener method, processes events, and outputs the CSV as the file is processed.
     */
    @Override
    public void processRecord(Record record) {
        int thisRow = -1;
        int thisColumn = -1;
        String thisStr = null;

        switch (record.getSid()) {
        case BoundSheetRecord.sid:
            BoundSheetRecord sheetRecord = (BoundSheetRecord) record;
            boundSheetRecords.add(sheetRecord);
            break;
        case BOFRecord.sid:
            BOFRecord br = (BOFRecord) record;
            if (br.getType() == BOFRecord.TYPE_WORKSHEET) {
                // Create sub workbook if required
                if (workbookBuildingListener != null && stubWorkbook == null) {
                    stubWorkbook = workbookBuildingListener.getStubHSSFWorkbook();
                }

                // Output the worksheet name
                // Works by ordering the BSRs by the location of
                // their BOFRecords, and then knowing that we
                // process BOFRecords in byte offset order

                if (orderedBSRs == null && boundSheetRecords.size() > 0) {
                    orderedBSRs = BoundSheetRecord.orderByBofPosition(boundSheetRecords);
                }
            }
            break;

        case SSTRecord.sid:
            sstRecord = (SSTRecord) record;
            break;

        case BlankRecord.sid:
            BlankRecord brec = (BlankRecord) record;

            thisRow = brec.getRow();
            thisColumn = brec.getColumn();
            thisStr = "";
            break;
        case BoolErrRecord.sid:
            BoolErrRecord berec = (BoolErrRecord) record;

            thisRow = berec.getRow();
            thisColumn = berec.getColumn();
            thisStr = "";
            break;

        case FormulaRecord.sid:
            FormulaRecord frec = (FormulaRecord) record;

            thisRow = frec.getRow();
            thisColumn = frec.getColumn();

            if (outputFormulaValues) {
                if (Double.isNaN(frec.getValue())) {
                    // Formula result is a string
                    // This is stored in the next record
                    outputNextStringRecord = true;
                    nextRow = frec.getRow();
                    nextColumn = frec.getColumn();
                } else {
                    thisStr = formatListener.formatNumberDateCell(frec);
                }
            } else {
                thisStr = HSSFFormulaParser.toFormulaString(stubWorkbook, frec.getParsedExpression());
            }
            break;
        case StringRecord.sid:
            if (outputNextStringRecord) {
                // String for formula
                StringRecord srec = (StringRecord) record;
                thisStr = srec.getString();
                thisRow = nextRow;
                thisColumn = nextColumn;
                outputNextStringRecord = false;
            }
            break;

        case LabelRecord.sid:
            LabelRecord lrec = (LabelRecord) record;

            thisRow = lrec.getRow();
            thisColumn = lrec.getColumn();
            thisStr = lrec.getValue();
            break;
        case LabelSSTRecord.sid:
            LabelSSTRecord lsrec = (LabelSSTRecord) record;

            thisRow = lsrec.getRow();
            thisColumn = lsrec.getColumn();
            if (sstRecord == null) {
                thisStr = "(No SST Record, can't identify string)";
            } else {
                thisStr = sstRecord.getString(lsrec.getSSTIndex()).toString();
            }
            break;
        case NoteRecord.sid:
            NoteRecord nrec = (NoteRecord) record;

            thisRow = nrec.getRow();
            thisColumn = nrec.getColumn();
            // TODO: Find object to match nrec.getShapeId()
            thisStr = "(TODO)";
            break;
        case NumberRecord.sid:
            NumberRecord numrec = (NumberRecord) record;

            thisRow = numrec.getRow();
            thisColumn = numrec.getColumn();

            // Format
            thisStr = formatListener.formatNumberDateCell(numrec);
            break;
        case RKRecord.sid:
            RKRecord rkrec = (RKRecord) record;

            thisRow = rkrec.getRow();
            thisColumn = rkrec.getColumn();
            thisStr = "(TODO)";
            break;
        default:
            break;
        }

        // Handle new row
        if (thisRow != -1 && thisRow != lastRowNumber) {
            lastColumnNumber = -1;
        }

        // Handle missing column
        if (record instanceof MissingCellDummyRecord) {
            MissingCellDummyRecord mc = (MissingCellDummyRecord) record;
            thisRow = mc.getRow();
            thisColumn = mc.getColumn();
            thisStr = "";
        }

        // If we got something to print out, do so
        if (thisStr == null) {
            thisStr = "";
        }

        // Update column and row count
        if (thisRow > -1) {
            lastRowNumber = thisRow;
        }

        if (thisColumn > -1) {

            if (minColumns > -1) {
                records[thisColumn] = thisStr;
            } else if (minColumns == -1) {

                if (records != null && records.length > 0 && thisColumn < records.length) {
                    records[thisColumn] = thisStr;
                } else {
                    // 没有指定读取列数则先保存列头值
                    titleRecords.add(thisStr.trim());
                }
            }

            lastColumnNumber = thisColumn;
        } else {
            lastColumnNumber = -1;
        }

        // Handle end of row
        if (record instanceof LastCellOfRowDummyRecord) {
            if (minColumns > 0 && lastColumnNumber == -1) {
                lastColumnNumber = 0;
            }

            if (lastRowNumber > -1) {

                if (minColumns > -1 && records != null && records.length > 0) {
                    rows.add(records.clone());
                    for (int i = 0; i < records.length; i++) {
                        records[i] = null;
                    }

                } else if (minColumns == -1) {
                    if (lastRowNumber == 1 && titleRecords.size() > 0) {
                        records = new String[titleRecords.size()];

                        String[] strs = new String[titleRecords.size()];
                        for (int i = 0; i < strs.length; i++) {
                            if (BinaryUtils.isEmpty(titleRecords.get(i))) {
                                continue;
                            }
                            strs[i] = titleRecords.get(i);
                        }
                        rows.add(strs);
                        titleRecords.clear();
                    } else if (records != null && records.length > 0) {
                        rows.add(records.clone());

                        for (int i = 0; i < records.length; i++) {
                            records[i] = null;
                        }
                    }

                }
            }

            // We're onto a new row
            lastColumnNumber = -1;

            output.println();

        }

    }

    /**
     * 读取指定Sheet的内容
     * 
     * @param filePath
     * @param sheetName
     * @param minColumns
     *            读取最小列数,默认-1(有值的全读)
     * @return List<String[]> 结合
     * @throws FileNotFoundException
     * @throws IOException
     */
    public static List<String[]> readerExcelByPath(String filePath, String sheetName, int minColumns) throws FileNotFoundException, IOException {
        XLSCovertCSVReader reader = new XLSCovertCSVReader(filePath, sheetName, minColumns);
        reader.process();
        return reader.getRows();
    }

    /**
     * 读取指定Sheet的内容
     * 
     * @param file
     * @param sheetName
     * @param minColumns
     *            读取最小列数,默认-1(有值的全读)
     * @return List<String[]> 结合
     * @throws FileNotFoundException
     * @throws IOException
     */
    public static List<String[]> readerExcelByFile(File file, String sheetName, int minColumns) throws FileNotFoundException, IOException {
        POIFSFileSystem fs = new POIFSFileSystem(file);
        XLSCovertCSVReader reader = new XLSCovertCSVReader(fs, sheetName, System.out, minColumns);
        reader.process();
        return reader.getRows();
    }

}
