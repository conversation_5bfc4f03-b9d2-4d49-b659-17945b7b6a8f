package com.uinnova.product.eam.model.bm;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uino.bean.cmdb.base.ESCIRltInfo;

import java.util.List;
import java.util.Set;

@Comment("视图私有库/设计库要素信息")
public class DiagramPrivateAndDesginData {
	/**
	 * 集合信息
	 */
	@Comment("私有库 CI 信息")
	private List<CcCiInfo> privateCiInfos;
	
	@Comment("设计库 CI 信息")
	private List<CcCiInfo> desginCiInfos;
	
	@Comment("私有库 RLT 信息")
	private List<ESCIRltInfo> privateRltInfos;
	
	@Comment("设计库 RLT 信息")
	private List<ESCIRltInfo> desginRltInfos;

	private Set<String> needDelRltSet;


	public List<CcCiInfo> getPrivateCiInfos() {
		return privateCiInfos;
	}

	public void setPrivateCiInfos(List<CcCiInfo> privateCiInfos) {
		this.privateCiInfos = privateCiInfos;
	}

	public List<CcCiInfo> getDesginCiInfos() {
		return desginCiInfos;
	}

	public void setDesginCiInfos(List<CcCiInfo> desginCiInfos) {
		this.desginCiInfos = desginCiInfos;
	}

	public List<ESCIRltInfo> getPrivateRltInfos() {
		return privateRltInfos;
	}

	public void setPrivateRltInfos(List<ESCIRltInfo> privateRltInfos) {
		this.privateRltInfos = privateRltInfos;
	}

	public List<ESCIRltInfo> getDesginRltInfos() {
		return desginRltInfos;
	}

	public void setDesginRltInfos(List<ESCIRltInfo> desginRltInfos) {
		this.desginRltInfos = desginRltInfos;
	}

	public Set<String> getNeedDelRltSet() {
		return needDelRltSet;
	}

	public void setNeedDelRltSet(Set<String> needDelRltSet) {
		this.needDelRltSet = needDelRltSet;
	}
}
