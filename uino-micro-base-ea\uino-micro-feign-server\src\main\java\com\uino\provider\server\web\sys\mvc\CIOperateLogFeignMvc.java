package com.uino.provider.server.web.sys.mvc;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.binary.jdbc.Page;
import com.uino.service.sys.microservice.ICIOperateLogSvc;
import com.uino.bean.sys.base.ESCIOperateLog;
import com.uino.bean.sys.query.ESCIOperateLogSearchBean;
import com.uino.provider.feign.sys.CIOperateLogFeign;

/**
 * 
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("feign/sys/ciLog")
public class CIOperateLogFeignMvc implements CIOperateLogFeign {

    @Autowired
    private ICIOperateLogSvc ciOperateLog;

    @Override
    public Page<ESCIOperateLog> getCIOperateLogPageByCdt(ESCIOperateLogSearchBean bean) {
        return ciOperateLog.getCIOperateLogPageByCdt(bean);
    }

    @Override
    public Integer clearCIOperateLogByDuration(Integer clearLogDuration) {
        return ciOperateLog.clearCIOperateLogByDuration(clearLogDuration);
    }

}
