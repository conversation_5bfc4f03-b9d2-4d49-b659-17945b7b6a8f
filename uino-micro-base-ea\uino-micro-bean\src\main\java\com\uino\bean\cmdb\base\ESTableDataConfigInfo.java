package com.uino.bean.cmdb.base;

import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.util.Assert;

import com.uino.bean.permission.business.IValidDto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 
 * <AUTHOR>
 *
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value="Table配置信息",description = "Table配置信息")
public class ESTableDataConfigInfo implements Serializable, IValidDto {

    private static final long serialVersionUID = -6931837717815904293L;

    @ApiModelProperty(value="id",example = "123")
    Long id;

    @ApiModelProperty(value="uid",example = "123")
    String uid;

    @ApiModelProperty(value="配置")
    List<TableDataConfig> config;

    /** 所属域 */
    @ApiModelProperty(value="所属域id",example = "123")
    private Long domainId;

    /** 创建人 */
    @ApiModelProperty(value="创建人",example = "mike")
    private String creator;

    /** 修改人 */
    @ApiModelProperty(value = "修改人",example = "mike")
    private String modifier;

    /** 创建时间 */
    @ApiModelProperty(value="创建时间")
    private Long createTime;

    /** 修改时间 */
    @ApiModelProperty(value="修改时间")
    private Long modifyTime;

    @Setter
    @Getter
    @ApiModel(value="表格数据配置",description = "表格数据配置信息")
    public static class TableDataConfig {
        @ApiModelProperty(value="列ID")
        String colId;

        @ApiModelProperty(value="列名称")
        String colName;

        @ApiModelProperty(value="列宽度")
        Integer colWidth;

        @ApiModelProperty(value="是否展示")
        Integer isShow;
    }

    @Override
    public void valid() {
        Assert.notNull(uid, "uid not null");
    }
}
