package com.uino.dao.event;

import com.alibaba.fastjson.JSONObject;
import com.uino.bean.monitor.base.ESAlarm;
import com.uino.dao.AbstractESBaseDao;
import com.uino.dao.ESConst;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;

/**
 * 模拟告警推送记录
 * 
 * <AUTHOR>
 *
 */
@Service
public class ESAlarmSvc extends AbstractESBaseDao<ESAlarm, JSONObject> {

    @Override
    public String getIndex() {
        return ESConst.INDEX_SIMULATION_RECORD_EVENT;
    }

    @Override
    public String getType() {
        return ESConst.INDEX_SIMULATION_RECORD_EVENT;
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }
}
