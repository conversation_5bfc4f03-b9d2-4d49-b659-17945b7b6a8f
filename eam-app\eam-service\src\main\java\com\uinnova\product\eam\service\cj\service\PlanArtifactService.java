package com.uinnova.product.eam.service.cj.service;

import com.uinnova.product.eam.model.cj.domain.PlanArtifact;

import java.util.List;

public interface PlanArtifactService {

    /**
     * 新增方案关联制品信息
     * @param planArtifactList
     */
    void savePlanArtifact(List<PlanArtifact> planArtifactList);

    /**
     * 修改方案关联制品信息
     * @param planArtifact
     */
    void updatePlanArtifact(PlanArtifact planArtifact);

    /**
     * 删除方案关联制品信息
     * @param planArtifact
     */
    void removePlanArtifact(PlanArtifact planArtifact);

    /**
     * 通过方案主键批量移除
     * @param planIdList
     */
    void handlePlanArtifact(List<Long> planIdList, Integer status);

    /**
     * 复制方案关联制品
     * @param sourcePlanId
     */
    void copyPlanArtifact(Long sourcePlanId, Long sourceChapterId, Long newPlanId, Long newChapterId);

    /**
     * 获取方案下制品列表
     * @return
     */
    List<PlanArtifact> findPlanArtifactList(PlanArtifact planArtifact);

    /**
     * 通过视图id获取方案和视图信息
     * @param diagramIdList
     * @return
     */
    List<PlanArtifact> findPlanDiagramList(List<String> diagramIdList);

    /**
     * 删除方案关联制品
     * @param id
     */
    void deletePlanArtifact(Long id);

    /**
     * 删除方案关联制品
     */
    void deletePlanArtifactByPlanIds(List<Long> planIds);

    /**
     * 通过资产库视图id查关联的资产库视图的方案列表
     * @param privateDiagramIds 不能关联的设计库视图ids
     * @param releaseDiagramIds 需要关联的资产库视图ids
     * @return
     */
    List<PlanArtifact> findPlanReleaseDiagramList(List<String> privateDiagramIds, List<String> releaseDiagramIds);
}
