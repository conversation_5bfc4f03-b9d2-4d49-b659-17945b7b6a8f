package com.uino.bean.cmdb.business;

import com.binary.core.util.BinaryUtils;
import com.uino.bean.permission.business.IValidDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.springframework.util.Assert;

import java.io.Serializable;
import java.util.List;

/**
 * 批量删除CI对象
 * <AUTHOR>
 */
@ApiModel(value = "批量删除ci对象传输类", description = "批量删除对象参数体")
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CIRemoveBatchDto implements Serializable, IValidDto {


    @ApiModelProperty(value = "分类ID", example = "27393335556218", required = true)
    private Long classId;

    @ApiModelProperty(value = "对象id集合", required = true)
    private List<Long> ciIds;

    @Builder.Default
    @ApiModelProperty(value="是否选择全部数据",example="false",required = true)
    private boolean all=false;

    @ApiModelProperty(value="排除的数据集合")
    private List<Long> unCiIds;
    @Override
    public void valid() {
        Assert.notNull(classId, "X_PARAM_NOT_NULL${name:classId}");
        Assert.isTrue(!BinaryUtils.isEmpty(ciIds), "X_PARAM_NOT_NULL${name:ciIds}");
        Assert.notNull(all,"请选择是否全选数据");
    }

}
