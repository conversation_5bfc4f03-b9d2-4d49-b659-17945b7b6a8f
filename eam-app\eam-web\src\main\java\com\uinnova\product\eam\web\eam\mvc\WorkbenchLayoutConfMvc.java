package com.uinnova.product.eam.web.eam.mvc;

import com.binary.framework.web.RemoteResult;
import com.uinnova.product.eam.comm.model.es.WorkbenchLayoutConf;
import com.uinnova.product.eam.service.WorkbenchLayoutConfSvc;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;

/**
 * 工作台布局配置
 */
@RestController
@RequestMapping("/eam/workbenchConf")
public class WorkbenchLayoutConfMvc {
    /*{
      x: x坐标，y: y坐标，w: 宽度， h:高度， widgets： [{
        key: 功能块key
      }]
    }*/
    @Resource
    WorkbenchLayoutConfSvc workbenchLayoutConfSvc;

    @PostMapping("saveOrUpdate")
    public RemoteResult saveOrUpdate(@RequestBody WorkbenchLayoutConf workbenchLayoutConf) {
        Long id = workbenchLayoutConfSvc.saveOrUpdate(workbenchLayoutConf);
        return new RemoteResult(id);
    }

    @GetMapping("getLayout")
    public RemoteResult getLayout() {
        WorkbenchLayoutConf result = workbenchLayoutConfSvc.getLayout();
        return new RemoteResult(result);
    }
}
