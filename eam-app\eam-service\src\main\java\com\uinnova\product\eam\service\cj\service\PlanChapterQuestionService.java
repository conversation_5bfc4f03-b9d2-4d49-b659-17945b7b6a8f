package com.uinnova.product.eam.service.cj.service;

import com.binary.jdbc.Page;
import com.uinnova.product.eam.model.cj.domain.PlanChapterQuestion;
import com.uinnova.product.eam.model.cj.request.PlanChapterQuestionAnswerRequest;
import com.uinnova.product.eam.model.cj.request.PlanChapterQuestionRequest;
import com.uinnova.product.eam.model.cj.request.PlanModuleAnnotationRequest;
import com.uinnova.product.eam.model.cj.vo.PlanChapterQuestionVO;
import com.uinnova.product.eam.model.cj.vo.QuestionAnswerVO;

import java.util.List;

public interface PlanChapterQuestionService {

    /**
     * 添加或修改问题
     * @param question
     */
    Long saveOrUpdate(PlanChapterQuestion question);

    /**
     * 新增回复
     * @param request
     * @return
     */
    Long saveAnswer(PlanChapterQuestionAnswerRequest request);

    /**
     * 获取问题列表
     * @param request
     * @return
     */
    Page<PlanChapterQuestionVO> findQuestionList(PlanChapterQuestionRequest request);

    /**
     * 获取问题回复列表
     * @param id
     * @return
     */
    List<QuestionAnswerVO> findAnswerList(Long id);

    /**
     * 编辑问题
     * @param request
     * @return
     */
    Boolean updateQuestionAndAnnotation(PlanModuleAnnotationRequest request);

    /**
     * 编辑问题
     * @param annotationId
     * @return
     */
    Boolean deleteQuestionAndAnnotation(Long annotationId);

    /**
     * 取消归档
     * @param annotationId
     * @return
     */
    Boolean cancelArchiving(Long annotationId);

    /**
     * 统计不通过的数量
     * @param question
     * @return
     */
    Long countQuestion(PlanChapterQuestion question);

    /**
     * 获取章节问题列表
     * @param question
     * @return
     */
    List<PlanChapterQuestion> findPlanChapterQuestionList(PlanChapterQuestion question);

    /**
     * 新增问题
     * @param question
     * @return
     */
    Long saveArchivingQuestion(PlanChapterQuestion question);

    /**
     * 删除问题和批注
     * @param planId
     * @return
     */
    Boolean deleteQuestionAndAnnotationByPlanId(Long planId);
}
