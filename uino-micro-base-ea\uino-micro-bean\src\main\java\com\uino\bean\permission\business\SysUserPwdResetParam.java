package com.uino.bean.permission.business;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

/**
 * 
 * <AUTHOR>
 *
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value="用户密码重置信息",description = "用户密码重置信息")
public class SysUserPwdResetParam implements Serializable {

	private static final long serialVersionUID = 1L;

	/** 用户id **/
	@ApiModelProperty(value="用户id",example = "123")
	private Long userId;

	@ApiModelProperty(value = "登录名", example = "admin")
	private String loginCode;

	@ApiModelProperty(value="域",example = "123")
	private Long domainId;

	/** 旧密码 **/
	@ApiModelProperty(value="旧密码")
	private String oldPasswd;

	/** 新密码 **/
	@ApiModelProperty(value="新密码")
	private String newPasswd;

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public String getOldPasswd() {
		return oldPasswd;
	}

	public void setOldPasswd(String oldPasswd) {
		this.oldPasswd = oldPasswd;
	}

	public String getNewPasswd() {
		return newPasswd;
	}

	public void setNewPasswd(String newPasswd) {
		this.newPasswd = newPasswd;
	}

	public String getLoginCode() {
		return loginCode;
	}

	public void setLoginCode(String loginCode) {
		this.loginCode = loginCode;
	}

	public Long getDomainId() {
		return domainId;
	}

	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}
}
