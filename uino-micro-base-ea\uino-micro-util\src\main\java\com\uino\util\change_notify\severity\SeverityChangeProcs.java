package com.uino.util.change_notify.severity;

import java.util.ArrayList;
import java.util.List;

import com.uino.util.change_notify.IChangeChain;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.uino.util.change_notify.AbstractChangeProcs;
import com.uino.bean.monitor.base.ESMonSysSeverityInfo;

@Component
public class SeverityChangeProcs extends AbstractChangeProcs<ESMonSysSeverityInfo> {

    @Autowired(required = false)
    private List<ISeverityChangeChain> chains;

    @Override
    protected List<IChangeChain<ESMonSysSeverityInfo>> getChainsCore() {
        List<IChangeChain<ESMonSysSeverityInfo>> res = new ArrayList<>();
        if (chains != null && chains.size() > 0) {
            chains.forEach(chain -> res.add(chain));
        }
        return res;
    }
}
