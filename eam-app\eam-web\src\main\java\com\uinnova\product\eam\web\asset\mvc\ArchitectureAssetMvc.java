package com.uinnova.product.eam.web.asset.mvc;

import com.binary.framework.util.ControllerUtils;
import com.binary.framework.web.RemoteResult;
import com.uinnova.product.eam.comm.bean.CcAssetCiInfo;
import com.uinnova.product.eam.comm.model.es.AssetChangeRecord;
import com.uinnova.product.eam.model.vo.AppSystemQueryVoV2;
import com.uinnova.product.eam.service.ICISwitchSvc;
import com.uinnova.product.eam.web.asset.bean.AssetLisConfSearchParam;
import com.uinnova.product.eam.web.asset.peer.ArchitectureAssetPeer;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uino.bean.cmdb.base.LibType;
import com.uino.util.sys.SysUtil;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 架构资产控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/eam/asset")
public class ArchitectureAssetMvc {

    @Resource
    ArchitectureAssetPeer assetPeer;

    @Resource
    ICISwitchSvc ciSwitchSvc;

    @PostMapping("/saveOrUpdate")
    public void saveOrUpdate(@RequestParam(defaultValue = "DESIGN") LibType libType,
                             HttpServletRequest request, HttpServletResponse response, @RequestBody CcCiInfo ciInfo) {
        /**
         * 1.保存草稿（私有库保存）
         * 2. todo 发起审批（变更私有库数据-发布状态：审批中）
         * 3.审批通过（设计库保存数据，删除私有库数据）
         * 4. todo 变更记录列表
         * 5. todo 获取变更信息
         */
        CcCiInfo result = assetPeer.saveOrUpdate(ciInfo, libType);
        ControllerUtils.returnJson(request, response, result);
    }


    @ModDesc(desc = "通过ID查询对象信息", pDesc = "对象的id", pType = Long.class, rDesc = "对象信息", rType = CcCiInfo.class)
    @GetMapping("/queryById")
    public void queryById(@RequestParam(defaultValue = "DESIGN") LibType libType,
                          HttpServletRequest request, HttpServletResponse response, @RequestParam Long id) {
        CcCiInfo result = ciSwitchSvc.getCiInfoById(id, libType);
        ControllerUtils.returnJson(request, response, result);
    }

    @ModDesc(desc = "通过ID查询对象信息", pDesc = "对象的id", pType = Long.class, rDesc = "对象信息", rType = CcCiInfo.class)
    @GetMapping("/queryByCiCode")
    public void queryByCiCode(@RequestParam(defaultValue = "DESIGN") LibType libType,
                          HttpServletRequest request, HttpServletResponse response, @RequestParam String ciCode) {
        CcCiInfo result = ciSwitchSvc.getCiByCode(ciCode, SysUtil.getCurrentUserInfo().getLoginCode(), libType);
        ControllerUtils.returnJson(request, response, result);
    }

    @PostMapping("/change")
    public void change(HttpServletRequest request, HttpServletResponse response, @RequestBody CcCiInfo ciInfo) {
        CcCiInfo result =   assetPeer.change(ciInfo);
        ControllerUtils.returnJson(request, response, result);
    }

    @PostMapping("/cancellation")
    public void cancellation(HttpServletRequest request, HttpServletResponse response, @RequestBody CcCiInfo ciInfo) {
        Long id =   assetPeer.cancellation(ciInfo);
        ControllerUtils.returnJson(request, response, id);
    }

    @GetMapping("getChangeRecordInfo")
    public RemoteResult getChangeRecordInfo(@RequestParam String ciCode) {
        List<AssetChangeRecord> result = assetPeer.getChangeRecordInfo(ciCode);
        return new RemoteResult(result);
    }

    @GetMapping("queryAssetInfoByCiCode")
    public RemoteResult queryAssetInfoByCiCode(@RequestParam(defaultValue = "DESIGN") LibType libType, @RequestParam String ciCode, @RequestParam(defaultValue = "0") String isContrastReleaseInfo) {
        CcAssetCiInfo result = assetPeer.queryCiAndChangeAttrById(libType, ciCode, isContrastReleaseInfo);
        return new RemoteResult(result);
    }

    @PostMapping("exportBySearch")
    public ResponseEntity<byte[]> exportBySearch(@RequestParam(defaultValue = "DESIGN") LibType libType,
                                                 @RequestBody AssetLisConfSearchParam param) {
        return assetPeer.exportBySearch(param, libType);
    }

    /**
     * 处理数据
     * @param classIds
     * @return
     */
    @PostMapping("/classAndCiProcessingByClassIds")
    public RemoteResult classAndCiProcessingByClassIds(@RequestBody List<Long> classIds) {
        Object result = assetPeer.classAndCiProcessingByClassId(classIds);
        return new RemoteResult(1);
    }

    @GetMapping("/checkNoRepeatCiField")
    public RemoteResult checkNoRepeatCiField(Long classId,String ciCode,String attrName,String attrValue, LibType libType) {
        return new RemoteResult(assetPeer.checkNoRepeatCiField(classId, ciCode, attrName, attrValue,libType));
    }

    @GetMapping("/getAttrDefRestrainByClassId")
    public RemoteResult getAttrDefRestrainByClassId(Long classId,Long attrDefId) {
        return new RemoteResult(assetPeer.getAttrDefRestrainByClassId(classId, attrDefId));
    }

}
