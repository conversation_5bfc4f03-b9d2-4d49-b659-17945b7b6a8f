package com.uinnova.product.vmdb.comm.bean;

import com.binary.framework.bean.annotation.Comment;

import java.io.Serializable;

/**
 * 
 * <AUTHOR>
 *
 */
public class KeyPair implements Serializable {
    private static final long serialVersionUID = 1L;

    @Comment("键名")
    private String key;

    @Comment("键值")
    private String value;

    public KeyPair() {
    }

    public KeyPair(String key, String value) {
        this.key = key;
        this.value = value;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

}
