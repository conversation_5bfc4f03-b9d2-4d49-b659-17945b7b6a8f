package com.uinnova.product.vmdb.comm.model.rule;

import com.binary.core.util.BinaryUtils;
import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;
import com.binary.framework.exception.ServiceException;

import java.util.ArrayList;
import java.util.List;

/**
 * 
 * <AUTHOR>
 *
 */
@Comment("朋友圈实例[CC_RLT_RULE_INST]")
public class CcRltRuleInst implements EntityBean {
    private static final long serialVersionUID = 1L;

    @Comment("ID[ID]")
    private Long id;

    @Comment("所属朋友圈规则[RULE_DEF_ID]")
    private Long ruleDefId;

    @Comment("实例代码[INST_CODE]")
    private String instCode;

    @Comment("实例名称[INST_NAME]")
    private String instName;

    @Comment("实例描述[INST_DESC]")
    private String instDesc;

    @Comment("所属目录[DIR_ID]    备用")
    private Long dirId;

    @Comment("创建用户ID[USER_ID]")
    private Long userId;

    @Comment("创建用户姓名[USER_NAME]")
    private String userName;

    @Comment("实例类型[INST_TYPE]    1=通用")
    private Integer instType;

    @Comment("存放SVG[SVG_PATH]    运算符")
    private String svgPath;

    @Comment("存放XML[XML_PATH]    条件值")
    private String xmlPath;

    @Comment("背景图[BG_PATH]    非运算")
    private String bgPath;

    @Comment("背景样式[BG_CSS]    排列顺序")
    private String bgCss;

    @Comment("实例图标_1[ICON_1]")
    private String icon1;

    @Comment("实例图标_2[ICON_2]")
    private String icon2;

    @Comment("实例图标_3[ICON_3]")
    private String icon3;

    @Comment("实例图标_4[ICON_4]")
    private String icon4;

    @Comment("实例图标_5[ICON_5]")
    private String icon5;

    @Comment("入口CI例表[CI_IDS]    以逗号分隔, 前后也需有逗号")
    private String ciIds;

    @Comment("搜索字段[SEARCH_FIELD]")
    private String searchField;

    @Comment("状态[STATUS]    1=有效 0=无效")
    private Integer status;

    @Comment("所属域[DOMAIN_ID]")
    private Long domainId;

    @Comment("数据状态[DATA_STATUS]    数据状态:数据状态：1-正常 0-删除")
    private Integer dataStatus;

    @Comment("创建时间[CREATE_TIME]    创建时间:yyyyMMddHHmmss")
    private Long createTime;

    @Comment("修改时间[MODIFY_TIME]    修改时间:yyyyMMddHHmmss")
    private Long modifyTime;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getRuleDefId() {
        return this.ruleDefId;
    }

    public void setRuleDefId(Long ruleDefId) {
        this.ruleDefId = ruleDefId;
    }

    public String getInstCode() {
        return this.instCode;
    }

    public void setInstCode(String instCode) {
        this.instCode = instCode;
    }

    public String getInstName() {
        return this.instName;
    }

    public void setInstName(String instName) {
        this.instName = instName;
    }

    public String getInstDesc() {
        return this.instDesc;
    }

    public void setInstDesc(String instDesc) {
        this.instDesc = instDesc;
    }

    public Long getDirId() {
        return this.dirId;
    }

    public void setDirId(Long dirId) {
        this.dirId = dirId;
    }

    public Long getUserId() {
        return this.userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return this.userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Integer getInstType() {
        return this.instType;
    }

    public void setInstType(Integer instType) {
        this.instType = instType;
    }

    public String getSvgPath() {
        return this.svgPath;
    }

    public void setSvgPath(String svgPath) {
        this.svgPath = svgPath;
    }

    public String getXmlPath() {
        return this.xmlPath;
    }

    public void setXmlPath(String xmlPath) {
        this.xmlPath = xmlPath;
    }

    public String getBgPath() {
        return this.bgPath;
    }

    public void setBgPath(String bgPath) {
        this.bgPath = bgPath;
    }

    public String getBgCss() {
        return this.bgCss;
    }

    public void setBgCss(String bgCss) {
        this.bgCss = bgCss;
    }

    public String getIcon1() {
        return this.icon1;
    }

    public void setIcon1(String icon1) {
        this.icon1 = icon1;
    }

    public String getIcon2() {
        return this.icon2;
    }

    public void setIcon2(String icon2) {
        this.icon2 = icon2;
    }

    public String getIcon3() {
        return this.icon3;
    }

    public void setIcon3(String icon3) {
        this.icon3 = icon3;
    }

    public String getIcon4() {
        return this.icon4;
    }

    public void setIcon4(String icon4) {
        this.icon4 = icon4;
    }

    public String getIcon5() {
        return this.icon5;
    }

    public void setIcon5(String icon5) {
        this.icon5 = icon5;
    }

    public String getCiIds() {
        return this.ciIds;
    }

    public void setCiIds(String ciIds) {
        // 筛选格式
        if (null != ciIds) {
            String[] split = ciIds.split(",");
            List<Long> idList = new ArrayList<Long>(split.length);
            for (String string : split) {
                if (BinaryUtils.isEmpty(string, true)) {
                    continue;
                }
                Long id;
                try {
                    id = Long.parseLong(string.trim());
                } catch (NumberFormatException e) {
                    throw new ServiceException("入口CI_ID列表字符串解析错误");
                }
                idList.add(id);
            }
            ciIds = null;
            if (!BinaryUtils.isEmpty(idList)) {
                ciIds = ",";
                for (Long id : idList) {
                    ciIds += id + ",";
                }
            }
        }
        this.ciIds = ciIds;
    }

    public String getSearchField() {
        return this.searchField;
    }

    public void setSearchField(String searchField) {
        this.searchField = searchField;
    }

    public Integer getStatus() {
        return this.status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Long getDomainId() {
        return this.domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }

    public Integer getDataStatus() {
        return this.dataStatus;
    }

    public void setDataStatus(Integer dataStatus) {
        this.dataStatus = dataStatus;
    }

    public Long getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getModifyTime() {
        return this.modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }

}
