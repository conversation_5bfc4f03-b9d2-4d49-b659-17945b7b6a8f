package com.uinnova.product.vmdb.provider.kpiTpl.bean;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.comm.model.ci.CcCiClass;

import java.io.Serializable;
import java.util.List;

public class CiClassKpiTplInfo implements Serializable {
	private static final long serialVersionUID = 1L;
	
	@Comment("分类对象")
	private CcCiClass ciClass;
	
	@Comment("分类对象属性定义")
	private List<CcCiAttrDef> attrDefs;
	
	@Comment("当前分类下的KPI模板信息以及模板下的指标信息")
	private List<CcKpiTplInfo> kpiTplInfos;

	public CcCiClass getCiClass() {
		return ciClass;
	}

	public void setCiClass(CcCiClass ciClass) {
		this.ciClass = ciClass;
	}

	public List<CcCiAttrDef> getAttrDefs() {
		return attrDefs;
	}

	public void setAttrDefs(List<CcCiAttrDef> attrDefs) {
		this.attrDefs = attrDefs;
	}

	public List<CcKpiTplInfo> getKpiTplInfos() {
		return kpiTplInfos;
	}

	public void setKpiTplInfos(List<CcKpiTplInfo> kpiTplInfos) {
		this.kpiTplInfos = kpiTplInfos;
	}

	
	

}
