package com.uino.api.client.monitor.local;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.binary.jdbc.Page;
import com.uino.api.client.monitor.ISimulationRuleApiSvc;
import com.uino.bean.monitor.base.SimulationRuleInfo;
import com.uino.bean.monitor.query.SimulationRuleSearchBean;
import com.uino.service.simulation.ISimulationRuleSvc;

@Service
public class SimulationRuleApiSvcLocal implements ISimulationRuleApiSvc {

	@Autowired
	private ISimulationRuleSvc ruleSvc;

	@Override
	public Page<SimulationRuleInfo> querySimulationRuleInfoPage(SimulationRuleSearchBean bean) {
		return ruleSvc.querySimulationRuleInfoPage(bean);
	}

	@Override
	public Long saveOrUpdateSimulationRule(SimulationRuleInfo ruleInfo) {
		return ruleSvc.saveOrUpdateSimulationRule(ruleInfo);
	}

	@Override
	public Integer deleteSimulationRuleById(Long id) {
		return ruleSvc.deleteSimulationRuleById(id);
	}

	@Override
	public List<SimulationRuleInfo> querySimulationRuleInfoList(SimulationRuleSearchBean bean) {
		return ruleSvc.querySimulationRuleInfoList(bean);
	}

}
