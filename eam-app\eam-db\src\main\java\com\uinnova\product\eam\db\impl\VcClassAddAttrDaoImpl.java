package com.uinnova.product.eam.db.impl;


import com.uinnova.product.eam.comm.model.CVcClassAddAttr;
import com.uinnova.product.eam.comm.model.VcClassAddAttr;
import com.uinnova.product.eam.db.VcClassAddAttrDao;
import com.uinnova.product.eam.db.support.dao.mybatis.ComMyBatisBinaryDaoImpl;


/**
 * 分类附加属性表[VC_CLASS_ADD_ATTR]数据访问对象实现
 */
public class VcClassAddAttrDaoImpl extends ComMyBatisBinaryDaoImpl<VcClassAddAttr, CVcClassAddAttr> implements VcClassAddAttrDao {

//    @Override
//    public String getTableName() {
//        return "IAMS." + getDaoDefinition().getTableName();
//    }
}


