package com.binary.core.io;

import java.io.Serializable;

import com.binary.core.util.BinaryUtils;
import com.binary.core.util.ConfigurationUtils;

public abstract class Configuration implements Serializable {
	private static final long serialVersionUID = 1L;

	
	private String name;
	
	
	protected Configuration(String name) {
		BinaryUtils.checkEmpty(name, "name");
		this.name = name.trim();
	}
	
	
	protected Configuration(String name, String resourcePath) {
		BinaryUtils.checkEmpty(name, "name");
		BinaryUtils.checkEmpty(resourcePath, "resourcePath");
		this.name = name.trim();
		this.loadResource(ResourceResolver.getResource(resourcePath));
	}
	
	
	
	public void loadResource(Resource rs) {
		ConfigurationUtils.parseProperties(this, this.name, rs);
	}
	
	
}
