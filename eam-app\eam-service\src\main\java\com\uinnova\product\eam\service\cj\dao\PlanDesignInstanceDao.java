package com.uinnova.product.eam.service.cj.dao;

import com.uinnova.product.eam.model.cj.domain.PlanDesignInstance;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;


/**
 * 方案设计实例Dao
 * <AUTHOR>
 */
@Component
public class PlanDesignInstanceDao extends AbstractESBaseDao<PlanDesignInstance, PlanDesignInstance> {
    @Override
    public String getIndex() {
        return "uino_cj_plan_design_instance";
    }

    @Override
    public String getType() {
        return getIndex();
    }

    @PostConstruct
    public void init() {
        initIndex();
    }
}
