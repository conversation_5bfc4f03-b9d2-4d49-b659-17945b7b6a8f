package com.uino.web.cmdb.mvc;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import com.uino.bean.permission.base.SysUser;
import com.uino.init.api.ApiResult;
import com.uino.init.api.ApiVersion;
import com.uino.util.sys.SysUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import com.binary.core.io.Resource;
import com.binary.framework.util.ControllerUtils;
import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uinnova.product.vmdb.provider.rlt.bean.CcCiRltInfo;
import com.uino.bean.cmdb.business.BindCiRltRequestDto;
import com.uino.bean.cmdb.business.DataModuleRltClassDto;
import com.uino.bean.cmdb.business.DelRltReqDto;
import com.uino.bean.cmdb.business.ImportExcelMessage;
import com.uino.bean.cmdb.business.ImportResultMessage;
import com.uino.bean.cmdb.business.ImportSheetMessage;
import com.uino.bean.cmdb.business.RltExcelInfoDto;
import com.uino.bean.cmdb.business.UpdateCiRltRequestDto;
import com.uino.bean.cmdb.query.ESCiClassRltSearchBean;
import com.uino.bean.cmdb.query.ESRltSearchBean;
import com.uino.api.client.cmdb.ICIRltApiSvc;

/**
 * ci关系相关控制层
 *
 * <AUTHOR>
 */
@ApiVersion(1)
@Api(value = "ci关系相关控制层", tags = {"对象管理"})
@RestController
@RequestMapping(value = "/cmdb/ciRlt")
public class CiRltMvc {
    @Autowired
    private ICIRltApiSvc ciRltApiSvc;

    /**
     * 创建ci关系
     *
     * @param reqBean
     * @param request
     * @param response
     */
    @ApiOperation(value = "保存关系数据")
    @PostMapping("bindCiRlt")
    @ModDesc(desc = "保存关系数据", pDesc = "关系数据传输对象", pType = BindCiRltRequestDto.class, rDesc = "操作是否成功", rType = Boolean.class)
	public ApiResult<Boolean> bindCiRlt(@RequestBody BindCiRltRequestDto reqBean, HttpServletRequest request,
			HttpServletResponse response) {
		ciRltApiSvc.bindCiRlt(reqBean);
		return ApiResult.ok(this).data(true);
	}

    /**
     * 根据ciId解除ci关系（无论是目标还是源都解除）
     *
     * @param ciId 源/目标ciId
     * @return
     */
    @ApiOperation(value = "根据对象id清除相关关系数据(无论是目标还是源都解除)")
    @PostMapping("delRltByCiId")
    @ModDesc(desc = "根据对象id清楚相关关系数据(无论是目标还是源都解除)", pDesc = "对象id", pType = Long.class, rDesc = "操作是否成功", rType = Boolean.class)
    public ApiResult<Boolean> delRltByCiId(@RequestBody Long ciId, HttpServletRequest request, HttpServletResponse response) {
        ciRltApiSvc.delRltByCiId(ciId);

        return ApiResult.ok(this).data(true);

    }

    /**
     * 修改ci关系属性
     *
     * @param reqBean
     * @param request
     * @param response
     */
    @ApiOperation(value = "修改ci关系属性")
    @PostMapping("updateCiRltAttr")
    @ModDesc(desc = "更改关系属性", pDesc = "要修改的关系属性", pType = UpdateCiRltRequestDto.class, rDesc = "操作是否成功", rType = Boolean.class)
    public ApiResult<Boolean> updateCiRltAttr(@RequestBody UpdateCiRltRequestDto reqBean, HttpServletRequest request,
                                              HttpServletResponse response) {
        ciRltApiSvc.updateCiRltAttr(reqBean.getCiRltId(), reqBean.getAttrs());

        return ApiResult.ok(this).data(true);
    }

    /**
     * 查询ci关系-根据查询bean
     *
     * @param reqBean
     * @param request
     * @param response
     */
    @ApiOperation(value = "查询ci关系-根据查询bean")
    @PostMapping("searchRltByBean")
    @ModDesc(desc = "条件查询关系数据", pDesc = "查询条件", pType = ESRltSearchBean.class, rDesc = "关系数据分页结果", rType = Page.class, rcType = CcCiRltInfo.class)
    public ApiResult<Page<CcCiRltInfo>> searchRltByBean(@RequestBody ESRltSearchBean reqBean, HttpServletRequest request,
                                                        HttpServletResponse response) {
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        reqBean.setDomainId(currentUserInfo.getDomainId());
        Page<CcCiRltInfo> results = ciRltApiSvc.searchRltByBean(reqBean);

        return ApiResult.ok(this).data(results);
    }

    /**
     * 清除指定分类下所有ci关系
     *
     * @param rltClassId
     * @param request
     * @param response
     */
    @ApiOperation(value = "清楚指定分类下所有ci关系")
    @PostMapping("clearRltByClassId")
    @ModDesc(desc = "清除指定分类下关系数据", pDesc = "关系分类id", pType = Long.class, rDesc = "操作是否成功", rType = Boolean.class)
    public ApiResult<Boolean> clearRltByClassId(@RequestBody Long rltClassId, HttpServletRequest request,
                                                HttpServletResponse response) {
        ciRltApiSvc.clearRltByClassId(rltClassId);
        return ApiResult.ok(this).data(true);
    }

    /**
     * 导出ci关系
     *
     * @param rltClassIds
     * @param request
     * @param response
     */
    @ApiOperation(value="导出ci关系",notes="- 导出关系数据post请求 \r\n - 是否导出数据,1=是,0=否 \r\n - 传入参数为关系分类id集合 \r\n - 输出关系数据文件")
    @PostMapping("exportCiRlt")
    @ModDesc(desc = "导出关系数据(exportData:是否导出数据1=是，0=否;rltClassIds:关系分类id集合)", pDesc = "关系分类id集合", pType = Set.class, pcType = Long.class, rDesc = "关系数据文件", rType = Resource.class)
    public void exportCiRlt(@RequestBody(required = false) Set<Long> rltClassIds, HttpServletRequest request,
                            HttpServletResponse response) {
        Resource file = ciRltApiSvc.exportCiRlt(rltClassIds);
        ControllerUtils.returnResource(request, response, file);
    }

    /**
     * 导出ci关系
     *
     * @param rltClassIds
     * @param request
     * @param response
     */
    @ApiOperation(value="导出ci关系",notes="- 导出关系数据get请求 \r\n - 是否导出数据,1=是,0=否 \r\n -传入参数为关系分类id集合 \r\n - 输出关系数据文件 " )
    @GetMapping("exportCiRlt")
    @ModDesc(desc = "导出关系数据get请求(exportData:是否导出数据1=是，0=否;rltClassIds:关系分类id集合)", pDesc = "?exportData=1&rltClassIds=1,2", pType = String.class, rDesc = "关系数据文件", rType = Resource.class)
    public void exportCiRltGet(@RequestParam(required = false) Set<Long> rltClassIds, HttpServletRequest request,
                               HttpServletResponse response) {
        Resource file = ciRltApiSvc.exportCiRlt(rltClassIds);
        ControllerUtils.returnResource(request, response, file);
    }

    /**
     * 导入ci关系
     *
     * @param file
     * @param request
     * @param response
     */
    @ApiOperation(value = "导入ci关系")
    @RequestMapping(value="importCiRlt",method = RequestMethod.POST)
    @ModDesc(desc = "一键导入关系数据", pDesc = "导入文件及要导入的关系分类信息", rDesc = "导入明细", rType = ImportSheetMessage.class)
    public ApiResult<ImportResultMessage> importCiRlt(@RequestBody RltExcelInfoDto rltExcelInfoDto,
                            @RequestParam(required = false, value = "file") MultipartFile file, HttpServletRequest request,
                            HttpServletResponse response) {
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        ImportResultMessage result = ciRltApiSvc.importCiRlt(currentUserInfo.getDomainId(), rltExcelInfoDto.getExcelFilePath(), file, rltExcelInfoDto.getRltClsCodes());
        return ApiResult.ok(this).data(result);
    }

    /**
     * 根据【关系ids OR 关系codes】解除ci关系
     *
     * @param request
     * @param response
     */
    @ApiOperation(value="根据关系ids或codes解除关系")
    @PostMapping("delRltByIdsOrRltCodes")
    @ModDesc(desc = "根据关系ids或codes解除关系", pDesc = "解除关系请求传输对象", pType = DelRltReqDto.class, rDesc = "操作是否成功,1=成功，0=失败", rType = Integer.class)
    public ApiResult<Integer> delRltByIdsOrRltCodes(@RequestBody DelRltReqDto reqDto, HttpServletRequest request,
                                      HttpServletResponse response) {
        Integer re = ciRltApiSvc.delRltByIdsOrRltCodes(reqDto.getRltIds(), reqDto.getRltCodes());
        return ApiResult.ok(this).data(re);
    }

    /**
     * 解析关系excel
     *
     * @param excelFilePath
     * @param excelFile
     * @param request
     * @param response
     */
    @ApiOperation(value="一键导入解析文件")
    @RequestMapping(value="parseRltExcel",method = RequestMethod.POST)
    @ModDesc(desc = "一键导入解析文件", pDesc = "关系Excel文件或文件路径", rDesc = "Excel解析结果", rType = ImportExcelMessage.class)
    public ApiResult<ImportExcelMessage> parseRltExcel(@RequestParam(required = false, value = "excelFilePath") String excelFilePath,
                              @RequestParam(value = "file", required = false) MultipartFile excelFile, HttpServletRequest request,
                              HttpServletResponse response) {
        ImportExcelMessage res = ciRltApiSvc.parseRltExcel(excelFilePath, excelFile);
        return ApiResult.ok(this).data(res);
    }

    @ApiOperation(value="查询CI分类之间的关系信息")
    @PostMapping("queryClassRltList")
    @ModDesc(desc = "查询CI分类之间的关系信息", pDesc = "查询条件", rDesc = "关系分类列表", rType = List.class)
    public ApiResult<List<DataModuleRltClassDto>> queryClassRltList(@RequestBody ESCiClassRltSearchBean bean, HttpServletRequest request, HttpServletResponse response) {
        // ci分类的ID
        Long[] ciClassIds = bean.getIds();
        HashSet<Long> ciClassIdSet = new HashSet<>(Arrays.asList(ciClassIds));
        List<DataModuleRltClassDto> ccCiRltInfos = ciRltApiSvc.getClassRltList(ciClassIdSet, new HashSet<>());
        return ApiResult.ok(this).data(ccCiRltInfos);
    }
}
