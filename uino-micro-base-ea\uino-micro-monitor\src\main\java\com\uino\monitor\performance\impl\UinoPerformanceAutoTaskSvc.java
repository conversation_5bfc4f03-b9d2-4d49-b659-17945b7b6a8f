package com.uino.monitor.performance.impl;

import com.uino.dao.ESConst;
import com.uino.monitor.tp.metric.MetricDataSvc;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

/**
 * @Title: UinoPerformanceAutoTaskSvc
 * @Description: Periodically delete historical performance data
 * @Author: YGQ
 * @Create: 2021-08-04 18:18
 **/
@Service
@RefreshScope
//@ConditionalOnProperty(name = "base.performance.history.auto.clear", havingValue = "true")
public class UinoPerformanceAutoTaskSvc {

    private final MetricDataSvc metricDataSvc;
    /**
     * Performance data is stored every day. By default, data is retained for {@since 30 days}.
     * In this method, the performance index is deleted periodically
     * <p>
     * You can customize the performance data retention period
     * Configuration items: {@code performance.history.save.days={@link Integer}}
     */
    private final Long performanceHistorySaveDays;

    /**
     * Whether to enable regular cleaning of historical performance data, it is {@code enabled} by default
     *
     * <p>
     * It can be closed through the following configuration items
     * Configuration items: {@code base.performance.history.auto.clear={@link Boolean}}
     */
    @Scheduled(cron = "${delete.history.performance.cron:0 0 */12 * * ?}")
    public void deleteOldPerformances() {
        metricDataSvc.deleteIndexCheck(ESConst.INDEX_METRIC_DATA_PREFIX, performanceHistorySaveDays);
    }

    @Autowired
    public UinoPerformanceAutoTaskSvc(MetricDataSvc metricDataSvc, @Value("${performance.history.save.days:30}") Long performanceHistorySaveDays) {
        this.metricDataSvc = metricDataSvc;
        this.performanceHistorySaveDays = performanceHistorySaveDays;
    }
}
