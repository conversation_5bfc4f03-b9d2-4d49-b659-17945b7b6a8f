package com.uino.service.sys.microservice.impl;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import com.uino.dao.BaseConst;
import com.uino.service.permission.microservice.IRoleSvc;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.MultiMatchQueryBuilder.Type;
import org.elasticsearch.index.query.Operator;
import org.elasticsearch.index.query.QueryBuilders;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import com.binary.core.util.BinaryUtils;
import com.binary.jdbc.Page;
import com.uino.dao.permission.ESModuleSvc;
import com.uino.dao.permission.ESUserSvc;
import com.uino.dao.sys.ESOperateLogSvc;
import com.uino.service.sys.microservice.IOperateLogSvc;
import com.uino.dao.util.ESUtil;
import com.uino.bean.permission.base.SysModule;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.permission.query.CSysUser;
import com.uino.bean.sys.base.ESOperateLog;
import com.uino.bean.sys.query.ESOperateLogSearchBean;

/**
 *
 * <AUTHOR>
 *
 */
@Service
public class OperateLogSvc implements IOperateLogSvc {

    @Autowired
    private ESOperateLogSvc logSvc;

    @Autowired
    private ESUserSvc esUserSvc;

    @Autowired
    private ESModuleSvc esModuleSvc;

    @Autowired
    private OperateLogWriter operateLogWriter;

    @Autowired
    private IRoleSvc iRoleSvc;

    @Override
    public Page<ESOperateLog> getOperateLogPageByCdt(ESOperateLogSearchBean bean) {
        if(BinaryUtils.isEmpty(bean.getDomainId())){
            bean.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
        }
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("domainId",bean.getDomainId()));
        if (bean.getStartTime() != null) {
            query.must(QueryBuilders.rangeQuery("createTime").from(bean.getStartTime()));
        }
        if (bean.getEndTime() != null) {
            query.must(QueryBuilders.rangeQuery("createTime").to(bean.getEndTime()));
        }
        // 若查询条件中包含用户名，则丰富日志的用户信息时无需再查询用户
        List<SysUser> users = null;
        if (!BinaryUtils.isEmpty(bean.getUserName())) {
            users = esUserSvc.getListByQuery(QueryBuilders.boolQuery()
                    .must(QueryBuilders.multiMatchQuery(bean.getUserName(), "userName", "loginCode")
                            .operator(Operator.AND).type(Type.PHRASE_PREFIX).lenient(true)).must(QueryBuilders.termQuery("domainId",bean.getDomainId())));
            Set<Long> tempIds = users.stream().map(SysUser::getId).collect(Collectors.toSet());
            if (BinaryUtils.isEmpty(tempIds)) {
                return new Page<>(bean.getPageNum(), bean.getPageSize(), 0, 0, new ArrayList<>());
            } else {
                query.must(QueryBuilders.termsQuery("userId", tempIds));
            }
        }
        if (!BinaryUtils.isEmpty(bean.getModuleNames())) {
            query.must(QueryBuilders.termsQuery("moduleName.keyword", bean.getModuleNames()));
        }
        if (!BinaryUtils.isEmpty(bean.getOpDesc())) {
            query.must(QueryBuilders.multiMatchQuery(bean.getOpDesc(), "opDesc").operator(Operator.AND)
                    .type(Type.PHRASE_PREFIX).lenient(true));
        }
        if (!BinaryUtils.isEmpty(bean.getKeyword())) {
            String keyword = bean.getKeyword();
            BoolQueryBuilder keyQuery = QueryBuilders.boolQuery();
            BoolQueryBuilder queryUser = new BoolQueryBuilder();
            queryUser.must(QueryBuilders.multiMatchQuery(keyword, "userName", "loginCode").operator(Operator.AND)
                    .type(Type.PHRASE_PREFIX).lenient(true)).must(QueryBuilders.termQuery("domainId",bean.getDomainId()));
            List<SysUser> tempUsers = esUserSvc.getListByQuery(queryUser);
            Set<Long> userIdSet = tempUsers.stream().map(SysUser::getId).collect(Collectors.toSet());
            if (!BinaryUtils.isEmpty(userIdSet)) {
                keyQuery.should(QueryBuilders.termsQuery("userId", userIdSet));
            }
            BoolQueryBuilder queryModule = new BoolQueryBuilder();
            queryModule.must(QueryBuilders.multiMatchQuery(keyword, "moduleName", "label").operator(Operator.AND)
                    .type(Type.PHRASE_PREFIX).lenient(true)).must(QueryBuilders.termQuery("domainId",bean.getDomainId()));
            List<SysModule> modules = esModuleSvc.getListByQuery(queryModule);
            Set<String> moduleSignSet = modules.stream().filter(module -> !BinaryUtils.isEmpty(module.getModuleSign()))
                    .map(SysModule::getModuleSign).collect(Collectors.toSet());
            if (!BinaryUtils.isEmpty(moduleSignSet)) {
                keyQuery.should(QueryBuilders.termsQuery("moduleName.keyword", moduleSignSet));
            }
            keyQuery.should(QueryBuilders.multiMatchQuery(keyword, "opPath", "opDesc").operator(Operator.AND)
                    .type(Type.PHRASE_PREFIX).lenient(true));
            query.must(keyQuery);
        }
        //三员管理查询条件
        iRoleSvc.sanyuanCondition(query,"operate");
        // 填充用户信息
        Page<ESOperateLog> page = logSvc.getSortListByQuery(bean.getPageNum(), bean.getPageSize(), query, "createTime",
                false);
        if (users == null) {
            Set<Long> userIds = page.getData().stream().filter(x -> x.getUserId() != null).map(ESOperateLog::getUserId)
                    .collect(Collectors.toSet());
            CSysUser cdt = new CSysUser();
            cdt.setIds(userIds.toArray(new Long[userIds.size()]));
            users = esUserSvc.getListByCdt(cdt);
        }
        Map<Long, SysUser> userMap = BinaryUtils.toObjectMap(users, "id");
        page.getData().forEach(log -> {
            SysUser sysUser = userMap.get(log.getUserId());
            log.setUserCode(sysUser == null ? "system" : sysUser.getLoginCode());
            log.setUserName(sysUser == null ? "system" : sysUser.getUserName());
        });
        return page;
    }

    @Override
    public Long saveOrUpdate(ESOperateLog log) {
        Assert.notNull(log, "log not null");
        operateLogWriter.writeLog(log);
        return 1L;
    }

    @Override
	public Integer saveOrUpdateBatch(List<ESOperateLog> logs) {
		operateLogWriter.writeLog(logs.toArray(new ESOperateLog[] {}));
		return 1;
	}

	@Override
    public Integer clearOperateLogByDuration(Integer durationDay) {
        if (durationDay == null) {
            durationDay = 7;
        }
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DATE, -durationDay);
        query.must(QueryBuilders.rangeQuery("createTime").lte(ESUtil.getNumberDateTime(cal.getTime())));
        return logSvc.deleteByQuery(query, true);
    }
}
