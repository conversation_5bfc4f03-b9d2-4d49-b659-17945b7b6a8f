package com.uino.api.client.cmdb.rpc;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.uino.api.client.cmdb.IDataSetTopApiSvc;
import com.uino.provider.feign.cmdb.DataSetTopFeign;

@Service
public class DataSetTopApiSvcRpc implements IDataSetTopApiSvc {

	@Autowired
	private DataSetTopFeign dataSetTopFeign;

	@Override
	public void collectDataSet(Long dataSetId) {
		dataSetTopFeign.collectDataSet(dataSetId);
	}

	@Override
	public void deleteDataSetTop(Long dataSetId) {
		dataSetTopFeign.deleteDataSetTop(dataSetId);
	}
	

}
