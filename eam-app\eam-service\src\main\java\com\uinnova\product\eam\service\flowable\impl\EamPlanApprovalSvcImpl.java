package com.uinnova.product.eam.service.flowable.impl;

import com.uinnova.product.eam.base.local.TaskFromWorkflowContext;
import com.uinnova.product.eam.base.local.TaskFromWorkflowContextValue;
import com.uinnova.product.eam.model.cj.domain.PlanDesignInstance;
import com.uinnova.product.eam.model.constants.FlowableConstant;
import com.uinnova.product.eam.service.cj.service.PlanDesignInstanceService;
import com.uinnova.product.eam.service.exception.BusinessException;
import com.uinnova.product.eam.service.flowable.FlowableApprovalSvc;
import com.uinnova.product.eam.service.flowable.FlowableApprovalUserSvc;
import com.uino.api.client.permission.IUserApiSvc;
import com.uino.bean.permission.business.UserInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.List;

@Slf4j
@Component(value = FlowableConstant.PLAN_DEFINITION_KEY2)
public class EamPlanApprovalSvcImpl implements FlowableApprovalSvc {

    @Resource
    private PlanDesignInstanceService planDesignInstanceService;
    @Resource
    private FlowableApprovalUserSvc approvalUserSvc;
    @Resource
    private IUserApiSvc userApiSvc;

    @Override
    public List<String> getApprovalUser(String businessKey, String taskKey) {
        return approvalUserSvc.getApprovalUser(FlowableConstant.PLAN_DEFINITION_KEY2, businessKey, taskKey);
    }

    @Override
    public void reject(String businessKey) {
        /*PlanDesignInstance planDesignInstance = planDesignInstanceService.getPlanDesignInstance(Long.valueOf(businessKey));
        // 先判断是否已开启流程任务
        TaskResponse taskInfo = flowableFeign.getCurrentUserTask(String.valueOf(businessKey), FlowableConstant.PLAN_DEFINITION_KEY2, planDesignInstance.getCreatorCode());
        if (taskInfo == null || StringUtils.isEmpty(taskInfo.getTaskId())) {
            throw new BusinessException("方案驳回流程错误!");
        }
        // 将方案改为非流程中状态
        planDesignInstance.setProcessApproval(false);
        planDesignInstance.setTaskId(taskInfo.getTaskId());
        planDesignInstanceService.saveOrUpdate(planDesignInstance);
        // 修改方案中视图状态
        planDesignInstanceService.updateDiagramState(Long.valueOf(businessKey), 0);*/
    }

    @Override
    public void pass(String businessKey) {
        PlanDesignInstance planDesignInstance = planDesignInstanceService.getPlanDesignInstance(Long.valueOf(businessKey));
        TaskFromWorkflowContextValue contextValue = TaskFromWorkflowContext.getContext();
        String userLoginCode = planDesignInstance.getCreatorCode();
        if (contextValue != null && contextValue.getFromWorkflow()
                && StringUtils.isNotBlank(contextValue.getStartUserLoginCode())) {
            userLoginCode = contextValue.getStartUserLoginCode();
        }
        UserInfo userInfo = userApiSvc.getUserInfoByLoginCode(userLoginCode);
        if (userInfo == null) {
            throw new BusinessException("获取用户信息错误!");
        }
        planDesignInstanceService.passPlan(planDesignInstance, userInfo);
    }

    @Override
    public void cancel(String businessKey) {
        PlanDesignInstance planDesignInstance = planDesignInstanceService.getPlanDesignInstance(Long.valueOf(businessKey));
        planDesignInstanceService.cancelPlan(planDesignInstance);
    }
}
