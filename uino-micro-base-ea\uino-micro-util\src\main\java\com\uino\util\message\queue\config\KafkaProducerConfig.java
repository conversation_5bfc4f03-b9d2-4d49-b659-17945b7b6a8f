package com.uino.util.message.queue.config;

import com.uino.util.message.queue.tools.EnableMessageQueue;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.admin.AdminClient;
import org.apache.kafka.clients.admin.AdminClientConfig;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaAdmin;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * Kafka producer configuration
 *
 * @Author: YGQ
 * @Create: 2021-05-24 13:05
 **/
@Slf4j
@Configuration
@EnableKafka
@Conditional(EnableMessageQueue.class)
@ComponentScan(basePackages = {"com.uino.common.util.message.queue.config"})
public class KafkaProducerConfig {

    private final KafkaProp kafkaProp;

    @Autowired
    public KafkaProducerConfig(KafkaProp kafkaProp) {
        this.kafkaProp = kafkaProp;
    }

    /**
     * Producer Template configuration
     */
    @Bean(name = "kafkaTemplate")
    public KafkaTemplate<String, String> kafkaTemplate() {
        return new KafkaTemplate<>(producerFactory());
    }

    /**
     * Producer factory configuration
     */
    @Bean
    public ProducerFactory<String, String> producerFactory() {
        return new DefaultKafkaProducerFactory<>(producerConfigs());
    }

    public Map<String, Object> producerConfigs() {
        Map<String, Object> props = new HashMap<>(12);
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, KafkaProp.kafkaServers);
        props.put(ProducerConfig.ENABLE_IDEMPOTENCE_CONFIG, KafkaProp.enableIdempotence);
        props.put(ProducerConfig.RETRIES_CONFIG, KafkaProp.retries);
        props.put(ProducerConfig.ACKS_CONFIG, KafkaProp.sendAckMode);
        props.put(ProducerConfig.MAX_BLOCK_MS_CONFIG, KafkaProp.maxBlockMs);
        props.put(ProducerConfig.BATCH_SIZE_CONFIG, KafkaProp.batchSize);
        props.put(ProducerConfig.LINGER_MS_CONFIG, KafkaProp.lingerMs);
        props.put(ProducerConfig.BUFFER_MEMORY_CONFIG, KafkaProp.bufferMemory);
        props.put(ProducerConfig.MAX_REQUEST_SIZE_CONFIG, KafkaProp.maxRequestSize);
        props.put(ProducerConfig.COMPRESSION_TYPE_CONFIG, KafkaProp.compressionType);
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, KafkaProp.kafkaSerialization);
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, KafkaProp.kafkaSerialization);
        return props;
    }

    /**
     * Kafka Admin bean can automatically detect whether there is a topic in the cluster, and create it if it does not exist
     */
    @Bean
    public KafkaAdmin kafkaAdmin() {
        Map<String, Object> configs = new HashMap<>(1);
        configs.put(AdminClientConfig.BOOTSTRAP_SERVERS_CONFIG, KafkaProp.kafkaServers);
        return new KafkaAdmin(configs);
    }

    @Bean
    @Order(1)
    public AdminClient adminClient() {
        return AdminClient.create(kafkaAdmin().getConfigurationProperties());
    }

}
