package com.uinnova.product.eam.init;

import com.uinnova.product.vmdb.comm.dao.CommDao;
import com.uinnova.product.vmdb.comm.dao.impl.CommDaoImpl;
import org.mybatis.spring.SqlSessionTemplate;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.*;

/**
 * 本地化部署扫描类
 * <AUTHOR>
 */
@Configuration
@ImportResource(locations = {"classpath:spring-dbs.xml","spring/spring-impl-iams.xml"})
@ComponentScan(basePackages = {
        "com.uinnova.product.eam.service",
        "com.uinnova.product.eam.api",
        "com.uinnova.product.eam.local" })
@ConditionalOnProperty(prefix = "eam", name = "load-type", havingValue = "local", matchIfMissing = true)
public class EamLocalRunConfig implements ApplicationContextAware {
    {
        LoggerFactory.getLogger(getClass()).info("发现spring-boot配置为本地加载");
    }

    private static ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        EamLocalRunConfig.applicationContext = applicationContext;
    }

    @Bean
    @Primary
    @ConditionalOnMissingBean
    public CommDao getCommDaoImpl(){
        CommDaoImpl commDao = new CommDaoImpl();
        SqlSessionTemplate sqlSessionTemplate = applicationContext.getBean(SqlSessionTemplate.class);
        commDao.setSqlSessionTemplate(sqlSessionTemplate);
        return commDao;
    }


}
