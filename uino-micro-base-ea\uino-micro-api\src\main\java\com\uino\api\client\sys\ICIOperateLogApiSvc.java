package com.uino.api.client.sys;

import com.binary.jdbc.Page;
import com.uino.bean.sys.base.ESCIOperateLog;
import com.uino.bean.sys.query.ESCIOperateLogSearchBean;

/**
 * CI操作日志
 * 
 * <AUTHOR>
 */
public interface ICIOperateLogApiSvc {
    /**
     * 条件查询CI配置日志
     * 
     * @param bean
     * @return
     */
    Page<ESCIOperateLog> getCIOperateLogPageByCdt(ESCIOperateLogSearchBean bean);

    /**
     * 按保留时长清除CI配置日志
     * 
     * @param clearLogDuration
     * @return
     */
    Integer clearCIOperateLogByDuration(Integer clearLogDuration);
}
