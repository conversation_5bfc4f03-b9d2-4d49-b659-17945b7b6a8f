//package com.uinnova.product.eam.web.xinwang.cas;
//
//import com.alibaba.fastjson.JSON;
//import com.binary.framework.web.RemoteResult;
//import org.jasig.cas.client.authentication.AuthenticationRedirectStrategy;
//
//import jakarta.servlet.http.HttpServletRequest;
//import jakarta.servlet.http.HttpServletResponse;
//import java.io.IOException;
//import java.io.PrintWriter;
//import java.util.HashMap;
//
///**
// * description
// *
// * <AUTHOR>
// * @since 2023/3/1 17:54
// */
//public class ManageAuthenticationRedirectstrategy implements AuthenticationRedirectStrategy {
//
//    @Override
//    public void redirect(HttpServletRequest request, HttpServletResponse response, String potentialRedirecturl) throws IOException {
//        if (request.getHeader("accept").contains("application/json")) {
//            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
//            response.setCharacterEncoding("UTF-8");
//            response.setContentType("application/json;charset-utf-8");
//            PrintWriter writer = response.getWriter();
//            HashMap<String, String> result  = new HashMap<>();
//            result.put("status","401");
//            result.put("msg","请登录");
//            result.put("redirecturl",potentialRedirecturl);
//            RemoteResult remoteResult = new RemoteResult(result);
//            String s = JSON.toJSONString(remoteResult);
//            writer.write(s);
//            writer.flush();
//        }else {
//            response.sendRedirect(potentialRedirecturl);
//        }
//    }
//}
