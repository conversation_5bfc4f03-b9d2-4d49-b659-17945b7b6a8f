 package com.uino.bean.cmdb.query;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 
 * <AUTHOR>
 *
 */
@Getter
@Setter
@ApiModel(value="and规则",description = "and规则")
public class ESAndAttrRule implements Serializable {

    /**
     *
     */
    private static final long serialVersionUID = 1662865722877874452L;

    /**
     * key=属性
     */
    @ApiModelProperty(value="属性")
    private String key;

    /**
     * value=属性值
     */
    @ApiModelProperty(value="属性值")
    private String value;

    /**
     * 1：绝对等于,2：不等于,3:小于，4：小于等于， 5：大于，6：大于等于，7： 模糊匹配，8：模糊匹配取反， 9：包含，10：不包含
     */
    @ApiModelProperty(value="选择类型",example = "1：绝对等于,2：不等于,3:小于，4：小于等于， 5：大于，6：大于等于，7： 模糊匹配，8：模糊匹配取反， 9：包含，10：不包含")
    private int optType;
}
