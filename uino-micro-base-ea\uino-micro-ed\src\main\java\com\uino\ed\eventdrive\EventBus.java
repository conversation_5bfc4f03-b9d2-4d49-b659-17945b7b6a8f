package com.uino.ed.eventdrive;

import com.uino.ed.eventdrive.event.Event;
import com.uino.ed.eventdrive.event.EventHandleParam;
import com.uino.ed.eventdrive.executor.*;
import com.uino.ed.eventdrive.handle.EventHandle;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.util.Map;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.LinkedBlockingQueue;

/**
 * 事件驱动总线
 * 根据事件类型，事件名称注册事件处理器
 */
@Service
@Slf4j
public class EventBus extends Thread{

    @Autowired
    private SyncExecutor syncExecutor;//同步执行
    @Autowired
    private AsynExecutor asynExecutor;//异步执行
    @Autowired
    private ConcExecutor concExecutor;//并发执行
    @Autowired
    private AscoExecutor ascoExecutor;//异步并发执行

    //根据事件名称缓存事件处理器
    @Autowired
    private Map<String, EventHandle> mapEventHandle = new ConcurrentHashMap<>();

    //异步事件队列
    //线程安全
    private int MAX_QUEUE_SIZE = 10000;
    private BlockingQueue<Event> eventQueue = new LinkedBlockingQueue<>(MAX_QUEUE_SIZE);

    /**
     * @param event
     */
    public void trigger(Event event) throws InterruptedException {
        eventQueue.put(event);
    }


    @PostConstruct
    public  void active(){
        this.start();
    }

    @Override
    public void run() {
        while (true){
            try {
                Event event = eventQueue.take();
                this.dispatch(event);
            } catch (Exception exp) {
                log.error("EventBus Run Exception", exp);
            }
        }
    }

    private void dispatch(Event event) throws InterruptedException {
        String eventName = event.getEventName();
        if (mapEventHandle.containsKey(eventName)){
            EventHandle handle = mapEventHandle.get(eventName);
            ExecuteType executeType = event.getExecuteType();
            if(executeType == null){
                concExecutor.execute(handle, event);//并发执行
            } else if (executeType == ExecuteType.RM_SYNC) {
                syncExecutor.execute(handle, event);//同步执行
            } else if (executeType == ExecuteType.RM_ASYN) {
                EventHandleParam eventHandleParam = new EventHandleParam();
                eventHandleParam.setHandle(handle);
                eventHandleParam.setEvent(event);
                asynExecutor.execute(eventHandleParam);//异步执行
            } else if (executeType == ExecuteType.RM_CONC) {
                concExecutor.execute(handle, event);//并发执行
            } else if (executeType == ExecuteType.RM_ASCO) {
                EventHandleParam eventHandleParam = new EventHandleParam();
                eventHandleParam.setHandle(handle);
                eventHandleParam.setEvent(event);
                ascoExecutor.execute(eventHandleParam);//异步并发执行
            } else {
                concExecutor.execute(handle, event);//并发执行
            }
        }
    }
}
