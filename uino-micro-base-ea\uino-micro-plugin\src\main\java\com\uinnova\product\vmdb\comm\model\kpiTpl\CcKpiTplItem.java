package com.uinnova.product.vmdb.comm.model.kpiTpl;

import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;

/**
 * 
 * <AUTHOR>
 *
 */
@Comment("指标模版项目表[CC_KPI_TPL_ITEM]")
public class CcKpiTplItem implements EntityBean {
    private static final long serialVersionUID = 1L;

    @Comment("ID[ID]")
    private Long id;

    @Comment("模板ID[TPL_ID]")
    private Long tplId;

    @Comment("关联对象类型[RLT_OBJ_TYPE]    关联对象类型:1=指标 2=CI分类 3=标签")
    private Integer rltObjType;

    @Comment("关联对象ID[RLT_OBJ_ID]")
    private Long rltObjId;

    @Comment("关联对象名称[RLT_OBJ_NAME]")
    private String rltObjName;

    @Comment("指标应用类型[KPI_USE_TYPE]    指标应用类型:1=应该，2=可能")
    private Integer kpiUseType;

    @Comment("备用_1[CUSTOM_1]")
    private Long custom1;

    @Comment("备用_2[CUSTOM_2]")
    private String custom2;

    @Comment("备用_3[CUSTOM_3]")
    private String custom3;

    @Comment("所属域[DOMAIN_ID]")
    private Long domainId;

    @Comment("数据状态[DATA_STATUS]    数据状态:0=删除，1=正常")
    private Integer dataStatus;

    @Comment("创建时间[CREATE_TIME]    创建时间:yyyyMMddHHmmss")
    private Long createTime;

    @Comment("更新时间[MODIFY_TIME]    更新时间:yyyyMMddHHmmss")
    private Long modifyTime;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTplId() {
        return this.tplId;
    }

    public void setTplId(Long tplId) {
        this.tplId = tplId;
    }

    public Integer getRltObjType() {
        return this.rltObjType;
    }

    public void setRltObjType(Integer rltObjType) {
        this.rltObjType = rltObjType;
    }

    public Long getRltObjId() {
        return this.rltObjId;
    }

    public void setRltObjId(Long rltObjId) {
        this.rltObjId = rltObjId;
    }

    public String getRltObjName() {
        return this.rltObjName;
    }

    public void setRltObjName(String rltObjName) {
        this.rltObjName = rltObjName;
    }

    public Integer getKpiUseType() {
        return this.kpiUseType;
    }

    public void setKpiUseType(Integer kpiUseType) {
        this.kpiUseType = kpiUseType;
    }

    public Long getCustom1() {
        return this.custom1;
    }

    public void setCustom1(Long custom1) {
        this.custom1 = custom1;
    }

    public String getCustom2() {
        return this.custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }

    public String getCustom3() {
        return this.custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }

    public Long getDomainId() {
        return this.domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }

    public Integer getDataStatus() {
        return this.dataStatus;
    }

    public void setDataStatus(Integer dataStatus) {
        this.dataStatus = dataStatus;
    }

    public Long getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getModifyTime() {
        return this.modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }

}
