package com.uino.bean.tp.base;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

/**
 * 性能数据
 * @author: weix<PERSON>ong
 * @create: 2020/07/10 13:48
 **/
@Data
public class PerformanceDTO {
    private String value;
    /**
     * 标准指标
     */
    private String metric;
    /**
     * 原始指标
     */
    private String originMetric;
    private String ciCode;
    private Long timestamp;
    private String kpiClass;
    private String instance;
    private Long ciId;
    private Long classId;
    private String className;
    private String ciPrimaryKey;
    private String originCiName;
    private CIObjectDTO CIObject;
    private JSONObject metricAttrs;

    @Data
    public static class CIObjectDTO {
        private Long classId;
        private String ciCode;
        private Long hashCode;
        private String ciPrimaryKey;
        private String className;
        private Long id;
        private Long time;
        private JSONObject object;
    }

}
