package com.uinnova.product.eam.model.diagram;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

/**
 * 流程建模视图上下级绑定
 * <AUTHOR>
 */
@Data
public class DiagramBindLogo {
    @Comment("对象id")
    private Long ciId;
    @Comment("对象code")
    private String ciCode;
    @Comment("层级")
    private int level;
    @Comment("当前ci绑定视图id")
    private String currentDiagramId;
    @Comment("父级对象id")
    private Long preAssetId;
    @Comment("父级对象code")
    private String preAssetCode;
    @Comment("父级视图id")
    private String preDiagramId;

    public DiagramBindLogo(Long ciId, String ciCode, int level, String currentDiagramId, Long preAssetId, String preAssetCode, String preDiagramId) {
        this.ciId = ciId;
        this.ciCode = ciCode;
        this.level = level;
        this.currentDiagramId = currentDiagramId;
        this.preAssetId = preAssetId;
        this.preAssetCode = preAssetCode;
        this.preDiagramId = preDiagramId;
    }
}
