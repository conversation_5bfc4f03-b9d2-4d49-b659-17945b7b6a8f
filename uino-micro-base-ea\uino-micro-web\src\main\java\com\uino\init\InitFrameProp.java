package com.uino.init;

import com.binary.framework.util.FrameworkProperties;
import com.uino.api.client.cmdb.ICIClassApiSvc;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;

import java.util.HashMap;
import java.util.Map;

/**
 * @Title: InitFrameProp
 * @Description:
 * @Author: YGQ
 * @Create: 2021-05-31 11:44
 **/
@Configuration
@Slf4j
public class InitFrameProp {

    @Autowired(required = false)
    private ICIClassApiSvc iciClassApiSvc;

    @Bean
    @Lazy(false)
    @ConditionalOnMissingBean
    public FrameworkProperties FrameworkProperties() {
        Map<String, Object> properties = new HashMap<>();
        String projectLocalSpace = "";
        try {
            if (iciClassApiSvc != null) {
                projectLocalSpace = iciClassApiSvc.getHttpResourceSpace();
            } else {
                projectLocalSpace = "http://localhost";
            }
        } catch (Exception e) {
            log.error("获取HttpResourceSpace失败", e);
            projectLocalSpace = "http://localhost";
        }
        properties.put("charset", "UTF-8");
        properties.put("Project_Local_Space", projectLocalSpace);
        FrameworkProperties frameworkProperties = new FrameworkProperties(properties);
        log.info("FrameworkProperties注册成功, Local Space {}", projectLocalSpace);
        return frameworkProperties;
    }

}
