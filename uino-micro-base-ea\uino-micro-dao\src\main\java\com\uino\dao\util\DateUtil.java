package com.uino.dao.util;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2021/11/26
 * @Version 1.0
 */
public class DateUtil {
    private static final DateFormat _FORMAT = new SimpleDateFormat("yyyyMMddHHmmss");

    /**
     * 获取精确到秒的时间戳
     *
     * @return
     */
    public static int getSecondTimestamp(Date date) {
        if (null == date) {
            return 0;
        }
        String timestamp = String.valueOf(date.getTime());
        int length = timestamp.length();
        if (length > 3) {
            return Integer.parseInt(timestamp.substring(0, length - 3));
        } else {
            return 0;
        }
    }

    /**
     * 获取分秒
     *
     * @param date
     * @return
     */
    public static String getmmss(Date date) {
        return _FORMAT.format(date).substring(10);
    }

    /**
     * 获取时分秒
     *
     * @param date
     * @return
     */
    public static String getHHmmss(Date date) {
        return _FORMAT.format(date).substring(8);
    }


}
