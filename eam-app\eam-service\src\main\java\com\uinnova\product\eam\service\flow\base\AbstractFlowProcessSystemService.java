package com.uinnova.product.eam.service.flow.base;

import com.uinnova.product.eam.feign.workable.FlowableFeign;
import com.uinnova.product.eam.service.AppSquareConfigSvc;
import com.uinnova.product.eam.service.EamDiagramRelationSysService;
import com.uinnova.product.eam.service.ICIRltSwitchSvc;
import com.uinnova.product.eam.service.ICISwitchSvc;
import com.uinnova.product.eam.service.asset.BmConfigSvc;
import com.uinnova.product.eam.service.diagram.ESDiagramSvc;
import com.uinnova.product.eam.service.diagram.EsDiagramSvcV2;
import com.uinnova.product.eam.service.es.*;
import com.uinnova.product.eam.service.fx.GeneralPushSvc;
import com.uino.api.client.permission.IUserApiSvc;
import com.uino.bean.cmdb.query.ESAttrBean;
import com.uino.service.cmdb.microservice.ICIClassSvc;
import com.uino.service.cmdb.microservice.ICISvc;
import com.uino.service.cmdb.microservice.IRltClassSvc;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;

import jakarta.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 流程管理服务抽象基类
 * 提供公共依赖和通用方法
 */
@Slf4j
public abstract class AbstractFlowProcessSystemService {

    @Resource
    protected FlowSystemApproveDataDao flowSystemApproveDataDao;

    @Resource
    protected ICISvc iamsCIDesignSvc;

    @Resource
    protected IUserApiSvc userApiSvc;

    @Resource
    protected ICISwitchSvc ciSwitchSvc;

    @Resource
    protected ICIClassSvc iciClassSvc;

    @Resource
    protected IRltClassSvc iRltClassSvc;

    @Resource
    protected ICIRltSwitchSvc iciRltSwitchSvc;

    @Resource
    protected ESDiagramSvc diagramApiClient;

    @Resource
    protected EsDiagramSvcV2 diagramSvcV2;

    @Resource
    protected GeneralPushSvc generalPushSvc;

    @Resource
    protected EamDiagramRelationSysService eamDiagramRelationSysService;

    @Resource
    protected EamDiagramRelationSysDao eamDiagramRelationSysDao;

    @Resource
    protected AppSquareConfigSvc appSquareConfigSvc;

    @Resource
    protected IUserApiSvc iUserApiSvc;

    @Resource
    protected FlowProcessSystemPublishHistoryDao flowProcessSystemPublishHistoryDao;

    @Resource
    protected FlowSystemAssociatedFeaturesDao flowSystemAssociatedFeaturesDao;

    @Resource
    protected FlowSystemAssociatedFeaturesPrivateDao flowSystemAssociatedFeaturesPrivateDao;

    @Resource
    protected FlowSystemFileDao flowSystemFileDao;

    @Resource
    protected FlowSystemFilePrivateDao flowSystemFilePrivateDao;

    @Resource
    protected FlowSystemProcessSignDataDao flowSystemProcessSignDataDao;

    @Autowired
    @Lazy
    protected IamsESCmdbCommDesignSvc commSvc;

    @Resource
    protected BmConfigSvc bmConfigSvc;

    @Resource
    protected FlowableFeign flowableFeign;

    @Resource
    protected ICISwitchSvc iciSwitchSvc;

    @Resource
    protected IndicatorDetectionInformationAssociationDao informationAssociationDao;

    /**
     * 格式化时间戳为字符串
     */
    protected String formatDateTime(Long timestamp) {
        if (timestamp == null) {
            return "";
        }
        return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date(timestamp));
    }

    /**
     * 创建ES属性查询对象
     */
    protected ESAttrBean createSearchAttr(String key, String value) {
        ESAttrBean esAttrBean = new ESAttrBean();
        esAttrBean.setKey(key);
        esAttrBean.setValue(value);
        return esAttrBean;
    }

    protected Map<String, String> safeGetAttrs(Map<String, String> attrs) {
        Map<String, String> safeAttrs = new LinkedHashMap<>();
        for (Map.Entry<String, String> entry : attrs.entrySet()) {
            safeAttrs.put(entry.getKey(), entry.getValue() != null ? entry.getValue() : "");
        }
        return safeAttrs;
    }

}