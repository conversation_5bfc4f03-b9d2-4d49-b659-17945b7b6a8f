package com.uinnova.product.eam.model.enums;

import lombok.Getter;

@Getter
public enum HandleTypeEnum {
    MINE_PUBLISH(1, "我的发布"),
    MINE_ATTENTION(2, "我的关注"),
    RECENTLY_VIEW(3, "最近常看");

    private Integer handleType;

    private String handleDesc;

    HandleTypeEnum(Integer handleType, String handleDesc) {
        this.handleType = handleType;
        this.handleDesc = handleDesc;
    }
}
