package com.uinnova.product.eam.rpc.bm;

import com.uinnova.product.eam.api.IBmDiagramApiClient;
import com.uinnova.product.eam.model.asset.EamReleaseHistoryDTO;
import com.uinnova.product.eam.model.vo.CheckBatchArtifactRuleVo;
import com.uinnova.product.vmdb.provider.rlt.bean.CcCiRltInfo;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
public class BmDiagramSvcRpc implements IBmDiagramApiClient {

    @Override
    public List<EamReleaseHistoryDTO> queryReleaseHistory(String id, Boolean historyFlag) {
        return Collections.emptyList();
    }

    @Override
    public boolean handlerRelease(String diagramId) {
        return false;
    }

    @Override
    public List<CcCiRltInfo> getActivityListByTaskRlt(String diagramId, String ciCode, String libType) {
        return Collections.emptyList();
    }

    @Override
    public List<CheckBatchArtifactRuleVo> categoryEleNumCheckBatch(List<String> diagramIds) {
        return Collections.emptyList();
    }
}
