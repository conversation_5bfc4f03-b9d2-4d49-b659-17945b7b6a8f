package com.uino.api.client.monitor.rpc;

import java.util.Collection;
import java.util.List;

import com.uino.dao.BaseConst;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.binary.core.io.Resource;
import com.binary.jdbc.Page;
import com.uino.api.client.monitor.IKpiApiSvc;
import com.uino.bean.cmdb.business.ImportResultMessage;
import com.uino.bean.cmdb.business.ImportSheetMessage;
import com.uino.bean.monitor.base.ESKpiInfo;
import com.uino.bean.monitor.buiness.KpiRltBindDto;
import com.uino.bean.monitor.buiness.SearchKpiBean;
import com.uino.provider.feign.monitor.KpiFeign;

@Service
public class KpiApiSvcRpc implements IKpiApiSvc {

    @Autowired
    private KpiFeign feign;

    @Override
	public ESKpiInfo getKpiInfoById(Long id) {
		return feign.getKpiInfoById(id);
	}
    
    @Override
    public List<ESKpiInfo> getKpiInfoByIds(Collection<Long> ids) {
        return feign.getKpiInfoByIds(ids);
    }

	@Override
    public Page<ESKpiInfo> queryKpiInfoPage(SearchKpiBean searchDto) {
        return feign.queryKpiInfoPage(searchDto);
    }

    @Override
    public Long saveOrUpdate(ESKpiInfo saveDto) {
        return feign.saveOrUpdate(saveDto);
    }

    @Override
    public void deleteByKpiIds(Collection<Long> kpiIds) {
        feign.deleteByKpiIds(kpiIds);
    }

    @Override
    public Resource exportKpiInfos(Boolean isTpl) {
        return feign.exportKpiInfos(BaseConst.DEFAULT_DOMAIN_ID, isTpl);
    }

    @Override
    public Resource exportKpiInfos(Long domainId, Boolean isTpl) {
        return feign.exportKpiInfos(domainId, isTpl);
    }

    @Override
    public ImportResultMessage importKpiInfos(MultipartFile file) {
        return feign.importKpiInfos(BaseConst.DEFAULT_DOMAIN_ID, file);
    }

    @Override
    public ImportResultMessage importKpiInfos(Long domainId, MultipartFile file) {
        return feign.importKpiInfos(domainId, file);
    }

    @Override
    public void bindCiClassRltToKpiInfo(KpiRltBindDto dto) {
        feign.bindCiClassRltToKpiInfo(dto);
    }

    @Override
    public void delCiClassRltToKpiInfo(KpiRltBindDto dto) {
        feign.delCiClassRltToKpiInfo(dto);
    }

	@Override
	public ImportSheetMessage saveOrUpdateBatch(List<ESKpiInfo> kpiInfos) {
		return feign.saveOrUpdateBatch(BaseConst.DEFAULT_DOMAIN_ID, kpiInfos);
	}

    @Override
    public ImportSheetMessage saveOrUpdateBatch(Long domainId, List<ESKpiInfo> kpiInfos) {
        return feign.saveOrUpdateBatch(domainId, kpiInfos);
    }

}
