package com.uino.util.express.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 公式参数描述
 */
@Target(ElementType.PARAMETER)
@Retention(RetentionPolicy.RUNTIME)
public @interface UinoAviatorParam {

    /**
     * 参数名，为空时取真实参数名
     */
    String name() default "";

    /**
     * 参数描述
     */
    String desc();

    /**
     * 参数类型
     */
    Class<?> type();
}
