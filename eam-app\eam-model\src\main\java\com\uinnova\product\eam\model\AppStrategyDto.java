package com.uinnova.product.eam.model;

import com.uinnova.product.vmdb.comm.model.ci.CcCi;

/**
 * 应用系统整体策略气泡图的数据对象
 *
 * <AUTHOR>
 * @version 2020/7/28
 */
public class AppStrategyDto {
    /**
     * 技术适配度
     */
    public String technicalFit;

    /**
     * 策略适配度
     */
    public String businessFit;

    /**
     * 关联应用的数量
     */
    public Integer rltCount;

    /**
     * 应用CI的基本信息
     */
    public CcCi ci;

    public String getTechnicalFit() {
        return technicalFit;
    }

    public void setTechnicalFit(String technicalFit) {
        this.technicalFit = technicalFit;
    }

    public String getBusinessFit() {
        return businessFit;
    }

    public void setBusinessFit(String businessFit) {
        this.businessFit = businessFit;
    }

    public Integer getRltCount() {
        return rltCount;
    }

    public void setRltCount(Integer rltCount) {
        this.rltCount = rltCount;
    }

    public CcCi getCi() {
        return ci;
    }

    public void setCi(CcCi ci) {
        this.ci = ci;
    }
}
