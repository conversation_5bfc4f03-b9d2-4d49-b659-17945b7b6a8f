package com.uinnova.product.eam.service.diagram.event.rule;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.binary.core.exception.BinaryException;
import com.binary.core.util.BinaryUtils;
import com.binary.jdbc.Page;
import com.google.common.collect.Lists;
import com.uinnova.product.eam.base.diagram.model.ESDiagramLink;
import com.uinnova.product.eam.base.diagram.model.ESDiagramNode;
import com.uinnova.product.eam.db.diagram.es.ESDiagramLinkDao;
import com.uinnova.product.eam.db.diagram.es.ESDiagramNodeDao;
import com.uinnova.product.eam.model.diagram.event.DiagramRuleEnum;
import com.uinnova.product.eam.model.diagram.event.RuleParams;
import com.uinnova.product.vmdb.comm.model.ci.CcCi;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.comm.model.ci.CcCiClass;
import com.uinnova.product.vmdb.comm.model.rlt.CCcCiRlt;
import com.uinnova.product.vmdb.comm.util.CommUtil;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.base.ESCIRltInfo;
import com.uino.bean.cmdb.base.LibType;
import com.uino.bean.cmdb.business.BindCiRltRequestDto;
import com.uino.bean.cmdb.query.ESCISearchBean;
import com.uino.bean.cmdb.query.ESRltSearchBean;
import com.uino.dao.util.ESUtil;
import com.uino.service.cmdb.microservice.IRltClassSvc;
import com.uino.service.cmdb.microservice.impl.CIClassSvc;
import com.uino.service.cmdb.microservice.impl.CIRltSvc;
import com.uino.service.cmdb.microservice.impl.CISvc;
import com.uino.util.sys.LibTypeUtil;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class ComponentRuleLinkService implements IRuleLinkService {

    @Autowired
    CISvc ciSvc;

    @Autowired
    CIRltSvc ciRltSvc;

    @Autowired
    CIClassSvc ciClassSvc;

    @Autowired
    IRltClassSvc rltClassSvc;

    @Autowired
    ESDiagramLinkDao diagramLinkDao;

    @Autowired
    ESDiagramNodeDao diagramNodeDao;

    @Override
    public DiagramRuleEnum getRuleType() {
        return DiagramRuleEnum.COMPONENT_DIAGRAM;
    }

    @Override
    public void process(RuleParams params) {
        if(getRuleType() != params.getRuleType()){
            return;
        }
        String rltClassName = params.getConfig().getString("rltClass");
        if(BinaryUtils.isEmpty(rltClassName)){
            throw new BinaryException("缺少配置");
        }

        List<CcCiClassInfo> ciClassList = rltClassSvc.queryAllClasses(params.getDomainId());
        if(BinaryUtils.isEmpty(ciClassList)){
            return;
        }
        Optional<CcCiClassInfo> any = ciClassList.stream().filter(each -> each.getCiClass().getClassName().equals(rltClassName)).findAny();
        if(!any.isPresent()){
            throw new BinaryException("缺少[" + rltClassName + "]关系类");
        }
        Long rltClassId = any.get().getCiClass().getId();


        ESCISearchBean bean = new ESCISearchBean();
        bean.setPageNum(1);
        bean.setPageSize(2);
        bean.setCiCodes(Lists.newArrayList(params.getSrcCiCode(), params.getTargetCiCode()));
        bean.setOwnerCode(params.getOwnerCode());
        bean.setDomainId(params.getDomainId());
        Page<ESCIInfo> ciPage = LibTypeUtil.execute(()-> ciSvc.searchESCIByBean(bean),LibType.PRIVATE);
        if(BinaryUtils.isEmpty(ciPage.getData())){
            return;
        }
        Optional<ESCIInfo> present = ciPage.getData().stream().filter(each -> each.getCiCode().equals(params.getTargetCiCode())).findAny();
        ESCIInfo targetCi = null;
        if(present.isPresent()){
            targetCi = present.get();
        }
        ESCIInfo srcCi = ciPage.getData().stream().filter(each -> each.getCiCode().equals(params.getSrcCiCode())).findAny().get();
        if(ciPage.getData().size() == 1){
            ESCISearchBean design = new ESCISearchBean();
            design.setPageNum(1);
            design.setPageSize(1);
            design.setCiCodes(Lists.newArrayList(params.getTargetCiCode()));
            design.setDomainId(params.getDomainId());
            Page<ESCIInfo> designPage = LibTypeUtil.execute(()-> ciSvc.searchESCIByBean(design), LibType.DESIGN);
            if(BinaryUtils.isEmpty(designPage.getData())){
                return;
            }
            final ESCIInfo _targetCi = designPage.getData().get(0);
            _targetCi.setId(null);
            _targetCi.setOwnerCode(params.getOwnerCode());
            long id = LibTypeUtil.execute(()->ciSvc.saveOrUpdate(tranCcCiInfo(_targetCi,null,null)),LibType.PRIVATE);
            _targetCi.setId(id);
            targetCi = _targetCi;
            ciPage.getData().add(targetCi);
        }
        String targetKey = createHiddenNode(targetCi, params, srcCi);
        Map<String, Long> dict = ciPage.getData().stream().collect(Collectors.toMap(ESCIInfo::getCiCode, ESCIInfo::getId, (k1, k2) -> k1));
        BindCiRltRequestDto requestDto = new BindCiRltRequestDto();
        requestDto.setOwnerCode(params.getOwnerCode());
        requestDto.setAttrs(Collections.emptyMap());
        requestDto.setRepetitionError(false);
        requestDto.setRltClassId(rltClassId);
        requestDto.setSourceCiId(dict.get(params.getSrcCiCode()));
        requestDto.setTargetCiId(dict.get(params.getTargetCiCode()));
        Long id = LibTypeUtil.execute(()-> ciRltSvc.bindCiRlt(requestDto), LibType.PRIVATE);
        ESRltSearchBean rltSearchBean = new ESRltSearchBean();
        CCcCiRlt rltCdt = new CCcCiRlt();
        rltCdt.setId(id);
        rltSearchBean.setCdt(rltCdt);
        Page<ESCIRltInfo> rltPage = LibTypeUtil.execute(()-> ciRltSvc.searchRlt(rltSearchBean),LibType.PRIVATE);
        List<ESDiagramLink> createLinks = new ArrayList<>();
        BoolQueryBuilder nodeQuery = QueryBuilders.boolQuery();
        nodeQuery.must(QueryBuilders.termQuery("diagramId", params.getDiagramId()));
        nodeQuery.must(QueryBuilders.termQuery("sheetId.keyword", params.getSheetId()));
        nodeQuery.must(QueryBuilders.termQuery("domainId", params.getDomainId()));
        nodeQuery.must(QueryBuilders.existsQuery("ciCode"));
        List<ESDiagramNode> esDiagramNodes = diagramNodeDao.selectListByQuery(1, 3000, nodeQuery);

        Map<String,String> ciCodeKeysMap = esDiagramNodes.stream().filter(each -> !BinaryUtils.isEmpty(each.getCiCode()))
                .collect(Collectors.toMap(ESDiagramNode::getCiCode, each-> each.getKey(),(k1,k2)->k1));

        for (ESCIRltInfo rlt : rltPage.getData()) {
            ESDiagramLink link = new ESDiagramLink();
            createLinks.add(link);
            link.setDiagramId(params.getDiagramId());
            link.setSheetId(params.getSheetId());
            link.setUniqueCode(rlt.getUniqueCode());
            link.setId(ESUtil.getUUID());
            link.setVisible(0);
            link.setKey(UUID.randomUUID().toString());
            JSONObject linkJson = new JSONObject();
            linkJson.put("key", link.getKey());
            linkJson.put("rltId", rlt.getId());
            linkJson.put("rltCode", rlt.getUniqueCode());
            linkJson.put("classId", rlt.getClassId());
            linkJson.put("className", rltClassName);
            linkJson.put("label", rltClassName);
            linkJson.put("visible", false);
            linkJson.put("from", ciCodeKeysMap.get(rlt.getSourceCiCode()));
            linkJson.put("to", targetKey);

            link.setLinkJson(linkJson.toJSONString());
        }
        diagramLinkDao.saveOrUpdateBatch(createLinks);
    }

    private String createHiddenNode(ESCIInfo targetCi, RuleParams params, ESCIInfo srcCi){
        if(null == targetCi){
            return null;
        }
        String domainAttrKey = params.getConfig().getString("domainAttrKey");
        CcCiClassInfo ccCiClassInfo = ciClassSvc.queryClassInfoById(targetCi.getClassId());

        ESDiagramNode node = new ESDiagramNode();
        node.setDiagramId(params.getDiagramId());
        node.setSheetId(params.getSheetId());
        node.setCiCode(targetCi.getCiCode());
        node.setId(ESUtil.getUUID());
        node.setVisible(0);
        node.setKey(UUID.randomUUID().toString());

        JSONObject nodeJson = new JSONObject();
        nodeJson.put("width", 50);
        nodeJson.put("zOrder", 2);
        nodeJson.put("shapeName", "entity");
        nodeJson.put("height", 50);
        nodeJson.put("category", "shape");
        nodeJson.put("loc", "200 200");

        nodeJson.put("key", node.getKey());
        nodeJson.put("ciId", targetCi.getId());
        nodeJson.put("ciCode", targetCi.getCiCode());
        nodeJson.put("classId", targetCi.getClassId());
        nodeJson.put("className", ccCiClassInfo.getCiClass().getClassName());
        String label = getLabel(ccCiClassInfo.getAttrDefs(), targetCi.getAttrs(), ccCiClassInfo.getCiClass().getClassName());
        nodeJson.put("label", label);
        nodeJson.put("visible", false);
        nodeJson.put("ciPrimaryKey", com.binary.json.JSON.toList(targetCi.getCiPrimaryKey(), String.class).stream().collect(Collectors.joining(",")));
        node.setNodeJson(nodeJson.toJSONString());
        diagramNodeDao.saveOrUpdate(node);
        srcCi.getAttrs().put(domainAttrKey, label);
        LibTypeUtil.execute(()-> ciSvc.saveOrUpdate(tranCcCiInfo(srcCi,null,null)),LibType.PRIVATE);
        return node.getKey();
    }
    
    private String getLabel(List<CcCiAttrDef> attrDefs, Map<String, Object> attrs, String clsName){
        Map<Integer, List<String>> ciPKAndLabels = CommUtil.getCiPKAndLabels(attrDefs, coverToAttrs(attrs));
        for(Integer item : Arrays.asList(2,0,1)){
            List<String> labels = ciPKAndLabels.get(item);
            if(BinaryUtils.isEmpty(labels)){
                if(item == 0){
                    return clsName;
                }
                continue;
            }
            return labels.stream().collect(Collectors.joining(","));
        }
        return clsName;
    }

    public static Map<String,String> coverToAttrs(Map<String,Object> attrs){
        Map<String,String> newAttrs = new HashMap<>();
        if(BinaryUtils.isEmpty(attrs)){
            return newAttrs;
        }
        attrs.forEach((k,v) -> {
            if(v != null){
                newAttrs.put(k,String.valueOf(v));
            } else {
                newAttrs.put(k,"");
            }
        });
        return newAttrs;
    }


    public static CcCiInfo tranCcCiInfo(ESCIInfo esCI, List<CcCiAttrDef> attrDefs, CcCiClass ciClass) {
        if (esCI == null) { return null; }
        String jsonStr = JSON.toJSONString(esCI, SerializerFeature.WriteMapNullValue).replaceAll("null", "''");
        JSONObject json = JSON.parseObject(jsonStr);
        CcCiInfo ciInfo = JSON.toJavaObject(json, CcCiInfo.class);
        CcCi ci = JSON.toJavaObject(json, CcCi.class);
        // CcCiInfo添加版本标识
        Long version = esCI.getVersion() == null ? 0 : esCI.getVersion();
        ci.setCiVersion(String.valueOf(version));
        ciInfo.setCi(ci);
        if (!BinaryUtils.isEmpty(ciClass)) {
            ciInfo.setCiClass(ciClass);
        }
        if(!BinaryUtils.isEmpty(attrDefs)){
            ciInfo.setAttrDefs(attrDefs);
        }
        return ciInfo;
    }
}
