package com.uino.util.message.queue;

import com.uino.util.message.queue.service.consumer.KafkaMessageQueueConsumerImpl;
import com.uino.util.message.queue.service.consumer.MessageQueueConsumerInterface;
import com.uino.util.message.queue.service.consumer.MessageQueueHandler;
import com.uino.util.message.queue.tools.ListenMode;
import com.uino.util.message.queue.tools.MessageQueueType;
import lombok.Setter;

/**
 * Message service consumer startup class, the supported types are {@link MessageQueueType},
 * the supported sending methods are {@link ListenMode}
 *
 * @see <a href="http://*************:8080/common/messageQueue/">http://*************:8080/common/messageQueue/</a>
 *
 * @Author: YGQ
 * @Create: 2021-05-25 10:53
 **/
@Setter
public class MessageQueueConsumer {
    private MessageQueueConsumerInterface messageQueueConsumerInterface;

    /**
     * @param messageQueueType    message queue type {@link MessageQueueType}
     * @param topics              topics, separate multiple with commas
     * @param groupId             group id
     * @param messageQueueHandler message monitoring service processor {@link MessageQueueHandler}
     */
    public MessageQueueConsumer(MessageQueueType messageQueueType, String topics, String groupId, MessageQueueHandler messageQueueHandler) {
        if (messageQueueType == MessageQueueType.KAFKA) {
            messageQueueConsumerInterface = new KafkaMessageQueueConsumerImpl(topics, groupId, null, null, messageQueueHandler);
        }
    }

    /**
     * @param messageQueueType    message queue type {@link MessageQueueType}
     * @param topics              topics, separate multiple with commas
     * @param groupId             group id
     * @param listenMode          monitor mode {@link ListenMode}
     * @param messageQueueHandler message monitoring service processor {@link MessageQueueHandler}
     */
    public MessageQueueConsumer(MessageQueueType messageQueueType, String topics, String groupId, ListenMode listenMode, MessageQueueHandler messageQueueHandler) {
        if (messageQueueType == MessageQueueType.KAFKA) {
            messageQueueConsumerInterface = new KafkaMessageQueueConsumerImpl(topics, groupId, listenMode, null, messageQueueHandler);
        }
    }

    /**
     * @param messageQueueType        message queue type {@link MessageQueueType}
     * @param topics                  topics, separate multiple with commas
     * @param groupId                 group id
     * @param listenMode              monitor mode {@link ListenMode}
     * @param customConcurrencyNumber concurrent consumption
     * @param messageQueueHandler     message monitoring service processor {@link MessageQueueHandler}
     */
    public MessageQueueConsumer(MessageQueueType messageQueueType, String topics, String groupId, ListenMode listenMode, Integer customConcurrencyNumber, MessageQueueHandler messageQueueHandler) {
        if (messageQueueType == MessageQueueType.KAFKA) {
            messageQueueConsumerInterface = new KafkaMessageQueueConsumerImpl(topics, groupId, listenMode, customConcurrencyNumber, messageQueueHandler);
        }
    }

    /**
     * Turn on monitoring
     */
    public void listen() {
        messageQueueConsumerInterface.listen();
    }
}
