package com.uinnova.product.eam.model.enums;

import lombok.Getter;

@Getter
public enum ConflictEnum {

    // 视图
    PRODUCT_NUM_CHECK(1, "视图内CI数量与制品约定数量冲突校验"),
    REQUIRED_FIELD_CHECK(2, "视图内CI必填项缺失校验"),
    PRIMARY_KEY_CHECK(3, "视图内CI主键冲突校验"),
    DIAGRAM_VERSION_CHECK(4, "视图数据版本冲突校验"),
    CI_VERSION_CHECK(5, "视图内CI数据版本冲突校验"),
    // 模型及模型视图
    MODEL_P_LEVEL_EXIST_CHECK(6, "模型目录发布上级完整性校验"),
    MODEL_DIAGRAM_LOCATION_CHECK(7, "单图发布校验模型视图所在的仓库目录存在"),
    MODEL_HIERARCHY_COMPLETE_CHECK(8, "业务架构模型树配置完整性校验"),
    LOCATION_RIGTH_CHECK(9, "发布默认回显位置合法性校验"),
    CI_RLT_DEL_CHECK(13, "RLT和CI是否被删除校验同时返回");

    private Integer conflictType;

    private String conflictDesc;

    ConflictEnum(Integer conflictType, String conflictDesc) {
        this.conflictType = conflictType;
        this.conflictDesc = conflictDesc;
    }
}
