package com.uino.dao.cmdb.dataset;

import com.alibaba.fastjson.JSONObject;
import com.uino.dao.AbstractESBaseDao;
import com.uino.bean.cmdb.base.dataset.batch.DataSetExeResultSheet;
import com.uino.dao.ESConst;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.stereotype.Repository;

import jakarta.annotation.PostConstruct;

/**
 * 数据集执行结果按路径拆分为Sheet
 *
 * <AUTHOR>
 * @version 2020-4-28
 */
@Repository
public class ESDataSetExeResultSheetSvc extends AbstractESBaseDao<DataSetExeResultSheet, JSONObject> {

    Log logger = LogFactory.getLog(ESDataSetExeResultSheetSvc.class);

    @Override
    public String getIndex() {
        return ESConst.INDEX_CMDB_DATASET_EXE_RESULT_SHEET;
    }

    @Override
    public String getType() {
        return ESConst.INDEX_CMDB_DATASET_EXE_RESULT_SHEET;
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }
}

