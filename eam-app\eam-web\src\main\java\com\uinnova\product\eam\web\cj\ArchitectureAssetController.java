package com.uinnova.product.eam.web.cj;

import com.binary.jdbc.Page;
import com.uinnova.product.eam.base.cj.ResultMsg;
import com.uinnova.product.eam.model.cj.domain.PlanDesignInstance;
import com.uinnova.product.eam.model.cj.vo.SystemPlanVo;
import com.uinnova.product.eam.service.cj.service.ArchitectureAssetService;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @description: 架构资产相关接口
 * @author: Lc
 * @create: 2022-01-12 11:26
 */
@RestController
@RequestMapping("/architectureAsset")
public class ArchitectureAssetController {

    @Resource
    private ArchitectureAssetService architectureAssetService;

    @PostMapping("/findPlanList")
    public ResultMsg findPlanList(@RequestBody SystemPlanVo systemPlanVo) {
        Page<PlanDesignInstance> planDesignList = architectureAssetService.findPlanList(systemPlanVo);
        return new ResultMsg(planDesignList);
    }

}
