package com.uinnova.product.eam.web.diagram.bean;

import java.io.Serializable;

import com.binary.framework.bean.annotation.Comment;

public class ExcelDataMessage implements Serializable{

	
	private static final long serialVersionUID = 1L;
	
	@Comment("ID")
	private Long id;
	
	@Comment("所属excel")
	private Long excelId;
	
	@Comment("所属sheet名称")
	private String sheetName;
	
	@Comment("所在行")
	private Integer rowNum;
	
	@Comment("所在列")
	private Integer colNum;
	
	@Comment("数据值")
	private String value;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getExcelId() {
		return excelId;
	}

	public void setExcelId(Long excelId) {
		this.excelId = excelId;
	}

	public String getSheetName() {
		return sheetName;
	}

	public void setSheetName(String sheetName) {
		this.sheetName = sheetName;
	}

	public Integer getRowNum() {
		return rowNum;
	}

	public void setRowNum(Integer rowNum) {
		this.rowNum = rowNum;
	}

	public Integer getColNum() {
		return colNum;
	}

	public void setColNum(Integer colNum) {
		this.colNum = colNum;
	}

	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}
	
	
	
	
}
