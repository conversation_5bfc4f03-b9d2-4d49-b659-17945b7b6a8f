package com.uinnova.product.eam.comm.bean;

import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

@Data
@Comment("意见反馈表")
public class EamFeedback implements Condition {
    @Comment("Id")
    private Long id;
    @Comment("序号")
    private Integer no;
    @Comment("满意度")
    private String satisfaction;
    @Comment("反馈功能模块")
    private String function;
    @Comment("反馈描述")
    private String describe;
    @Comment("提交人姓名")
    private String submitUser;
    @Comment("所属企业")
    private String company;
    @Comment("联系方式")
    private String phone;
    @Comment("邮箱")
    private String email;
    @Comment("登录帐号")
    private Long userId;
    @Comment("登录帐号姓名")
    private String userName;
    @Comment("创建时间 :yyyyMMddHHmmss")
    private Long createTime;
}
