package com.uinnova.product.eam.service.ai.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.binary.core.util.BinaryUtils;
import com.binary.jdbc.Page;
import com.uinnova.product.eam.model.AIDrawVo;
import com.uinnova.product.eam.model.vo.DifyDiagram;
import com.uinnova.product.eam.model.AutoGraphVo;
import com.uinnova.product.eam.service.ICISwitchSvc;
import com.uinnova.product.eam.service.ai.IDifyService;
import com.uinnova.product.eam.service.impl.IamsCIRltPrivateSvc;
import com.uinnova.product.eam.service.impl.IamsCIRltSwitchSvc;
import com.uino.bean.cmdb.base.ESCIClassInfo;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.base.ESCIRltInfo;
import com.uino.bean.cmdb.base.LibType;
import com.uino.bean.cmdb.business.BindCiRltRequestDto;
import com.uino.bean.cmdb.query.ESCISearchBean;
import com.uino.bean.cmdb.query.ESRltSearchBean;
import com.uino.dao.cmdb.ESCIClassSvc;
import com.uino.dao.cmdb.ESRltClassSvc;
import com.uino.util.sys.SysUtil;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;


import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DifyService implements IDifyService {

    @Autowired
    private RestTemplate restTemplate;

    // 请求dify工作流API的URL
    @Value("${dify.diagram.api.url:http://127.0.0.1/v1/workflows/run}")
    private String difyWorkflowUrl;

    // 请求dify工作流API的URL
    @Value("${dify.diagram.api.key:testKey001}")
    private String difyWorkflowAPIKey;

    @Value("${dify.diagram.api.user:ea}")
    private String difyWorkflowAPIUser;

    @Autowired
    ESCIClassSvc esciClassSvc;

    @Autowired
    ESRltClassSvc esRltClassSvc;

    @Autowired
    ICISwitchSvc ciSwitchSvc;

    @Autowired
    IamsCIRltSwitchSvc rltSwitchSvc;

    @Autowired
    IamsCIRltPrivateSvc iamsCiRltPrivateSvc;

    public Object createDiagram(DifyDiagram difyDiagram) {
        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Authorization", "Bearer " + difyWorkflowAPIKey);

        // 构建请求体
        Map<String, Object> requestBody = new HashMap<>();
        Map<String, Object> inputsBody = new HashMap<>();
        inputsBody.put("input", difyDiagram.getDiagramContent());
        inputsBody.put("drawType", difyDiagram.getDrawType());
        inputsBody.put("diagramType", difyDiagram.getDiagramType());
        requestBody.put("inputs", inputsBody);
        requestBody.put("response_mode", "blocking");
        requestBody.put("user", difyWorkflowAPIUser);

        log.info("调用Dify工作流请求参数: {}", JSONObject.toJSONString(requestBody));
        // 发起REST请求
        HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);
        try {
            ResponseEntity<JSONObject> responseEntity =
                    restTemplate.postForEntity(difyWorkflowUrl, requestEntity, JSONObject.class);
            log.info("调用Dify工作流返回: {}", JSONObject.toJSONString(responseEntity));
            if (responseEntity.getStatusCode() == HttpStatus.OK) {
                JSONObject responseBody = responseEntity.getBody();
                if (responseBody != null) {
                    JSONObject data = responseBody.getJSONObject("data");
                    if (data != null) {
                        Map<String, Object> outputs = data.getJSONObject("outputs");
                        if (outputs != null) {
                            log.info("调用Dify工作流返回outputs内容: {}", JSONObject.toJSONString(outputs));
                            if (difyDiagram.getDrawType().equalsIgnoreCase("quickea")) {
                                return parseResponseToAIDrawVo((String) outputs.get("graph_result"));
                            } else if(difyDiagram.getDrawType().equalsIgnoreCase("auto")) {
                                return parseResponseToAutoGraphVo((String) outputs.get("graph_result"));
                            } else {
                                return outputs.get("graph_result");
                            }
                        }
                    }
                }
            }else {
                log.error("请求Dify工作流API接口返回异常");
                throw new RuntimeException("调用AI模型发生异常");
            }
        }catch (HttpClientErrorException ce) {
            log.error("调用Dify工作流发生客户端异常", ce);
            handleClientError(ce.getMessage());
        }catch (Exception e) {
            log.error("调用Dify模型发生异常", e);
            throw new RuntimeException("调用AI模型发生异常", e);
        }
        throw new RuntimeException("无法从AI模型库获取执行结果");
    }

    /**
     * 处理客户端错误
     * 当客户端请求导致错误时，此方法用于构造错误响应并返回给客户端
     *
     * @param errorResponse 错误信息，用于向客户端报告错误详情
     */
    private void handleClientError(String errorResponse) {
        if (errorResponse != null) {
            if (errorResponse.contains("invalid_param")) {
                throw new IllegalArgumentException("AI模型传入参数异常");
            } else if (errorResponse.contains("app_unavailable")) {
                throw new RuntimeException("App 配置不可用");
            } else if (errorResponse.contains("provider_not_initialize")) {
                throw new RuntimeException("AI模型无可用模型凭据配置");
            } else if (errorResponse.contains("provider_quota_exceeded")) {
                throw new RuntimeException("AI模型调用额度不足");
            } else if (errorResponse.contains("model_currently_not_support")) {
                throw new RuntimeException("当前AI模型不可用");
            } else if (errorResponse.contains("workflow_request_error")) {
                throw new RuntimeException("AI workflow 执行失败");
            } else {
                throw new RuntimeException("调用AI模型错误");
            }
        }
        throw new RuntimeException("调用AI模型错误");
    }


    /**
     * 将Dify获取的返回JSON字符串转换为对象和关系数据
     *
     * @param answer Dify获取的返回JSON字符串
     */
    public AIDrawVo parseResponseToAIDrawVo(String answer){
        /* 制图问题示例：  设计一个系统间的上下文关系图，以国际结算系统为中心与代销理财系统、微信小程序系统、远程视频银行系统建立联机访问关系，
        以国际结算系统为中心与远程视频银行系统、代销基金系统、STM智慧柜台建立批量访问关系。*/
        // 接收到的AI返回数据格式示例:
        /*
         {
            "elements": [
                {
                    "name": "国际结算系统",
                    "type": "S-F.B.应用系统"
                },
                {
                    "name": "代销理财系统",
                    "type": "S-F.B.应用系统"
                },
                {
                    "name": "人像比对",
                    "type": "S-F.B.应用系统"
                }
            ],
            "relations": [
                {
                    "desc": "国际结算系统与代销理财系统的联机访问关系",
                    "name": "国际结算系统-联机访问-代销理财系统",
                    "source": "国际结算系统",
                    "target": "代销理财系统",
                    "type": "S-F.B.联机访问"
                },
                {
                    "desc": "国际结算系统与人像比对的联机访问关系",
                    "name": "国际结算系统-联机访问-人像比对",
                    "source": "国际结算系统",
                    "target": "人像比对",
                    "type": "S-F.B.联机访问"
                }
            ]
        }
        */

        // 解析 JSON 数据
        JSONObject jsonObject = JSON.parseObject(answer);

        // 解析 "elements" 数组
        JSONArray elementsArray = jsonObject.getJSONArray("elements");
        Set<String> classNameSet = new HashSet<>();
        for (int i = 0; i < elementsArray.size(); i++) {
            JSONObject element = ((JSONArray) elementsArray).getJSONObject(i);
            String type = element.getString("type");
            classNameSet.add(type);
        }
        // 批量查询涉及到的分类名称
        Map<String,Long> classNameIdMap = getClassIdsByName(classNameSet);
        List<ESCIInfo> esCiInfos = new ArrayList<>();
        Map<String, ESCIInfo> ciNameInfoMap = new HashMap<>();
        Map<String, ESCIInfo> ciCodeInfoMap = new HashMap<>();
        /* List<String> ciSearchWord = new ArrayList<>();
        List<Long> classIdList = new ArrayList<>();
        // 解析json中的关键字和分类名
        for (int i = 0; i < elementsArray.size(); i++) {
            JSONObject element = elementsArray.getJSONObject(i);
            String ciWord = element.getString("name");
            String className = element.getString("type");
            ciSearchWord.add(ciWord);
            if(classNameIdMap.containsKey(className)){
                classIdList.add(classNameIdMap.get(className));
            }
        }

        ESCISearchBean ciSearchBean = new ESCISearchBean();
        ciSearchBean.setPageNum(1);
        ciSearchBean.setPageSize(1);
        ciSearchBean.setWords(ciSearchWord);
        ciSearchBean.setWordLabel(true);
        if(!BinaryUtils.isEmpty(classIdList)){
            ciSearchBean.setClassIds(classIdList);
        }
        // 批量查询关键字和实际CI可能出现映射不准确的问题
        Page<ESCIInfo> page = ciSwitchSvc.searchESCIByBean(ciSearchBean, LibType.PRIVATE);
        if(!BinaryUtils.isEmpty(page.getData())){
            esCiInfos.add(page.getData().get(0));
            ciNameInfoMap.put(ciWord, page.getData().get(0));
        }*/

        for (int i = 0; i < elementsArray.size(); i++) {
            JSONObject element = elementsArray.getJSONObject(i);
            String ciWord = element.getString("name");
            String className = element.getString("type");

            List<String> ciSearchWord = new ArrayList<>();
            ciSearchWord.add(ciWord);
            ESCISearchBean ciSearchBean = new ESCISearchBean();
            ciSearchBean.setPageNum(1);
            ciSearchBean.setPageSize(1);
            ciSearchBean.setWords(ciSearchWord);
            ciSearchBean.setWordLabel(true);
            if(classNameIdMap.containsKey(className)){
                List<Long> classIdList = new ArrayList<>();
                classIdList.add(classNameIdMap.get(className));
                ciSearchBean.setClassIds(classIdList);
            }
            // TODO 修改为批量查询
            Page<ESCIInfo> page = ciSwitchSvc.searchESCIByBean(ciSearchBean, LibType.PRIVATE);
            if(!BinaryUtils.isEmpty(page.getData())){
                esCiInfos.add(page.getData().get(0));
                ciNameInfoMap.put(ciWord, page.getData().get(0));
                ciCodeInfoMap.put(page.getData().get(0).getCiCode(),page.getData().get(0));
            }
        }

        // 解析 "relations" 数组
        JSONArray relationsArray = jsonObject.getJSONArray("relations");
        Set<String> rltClassNameSet = new HashSet<>();
        for (int i = 0; i < relationsArray.size(); i++) {
            JSONObject relation = relationsArray.getJSONObject(i);
            String type = relation.getString("type");
            rltClassNameSet.add(type);
        }

        // 批量查询涉及到的分类名称
        Map<String,Long> rltClassNameIdMap = getRltClassIdsByName(rltClassNameSet);
        // 解析JSON并查询出实际已存在的关联关系
        Set<String> rltCodes = new HashSet<>();
        Map<String,Long> rltCodeClassIdMap = new HashMap<>();
        for (int i = 0; i < relationsArray.size(); i++) {
            JSONObject relation = relationsArray.getJSONObject(i);
            String type = relation.getString("type");
            String source = relation.getString("source");
            String target = relation.getString("target");
            if(ciNameInfoMap.containsKey(source) && ciNameInfoMap.containsKey(target)
                    && rltClassNameIdMap.containsKey(type)){
                String sourceCiCode = ciNameInfoMap.get(source).getCiCode();
                String targetCiCode = ciNameInfoMap.get(target).getCiCode();
                Long rltClassId = rltClassNameIdMap.get(type);
                rltCodes.add(sourceCiCode+"_"+rltClassId+"_"+targetCiCode);
                rltCodeClassIdMap.put(sourceCiCode+"_"+rltClassId+"_"+targetCiCode,rltClassNameIdMap.get(type));
            }
        }
        List<ESCIRltInfo> retRlt = new ArrayList<>();
        if(!BinaryUtils.isEmpty(rltCodes)){
            ESRltSearchBean rltSearchBean = new ESRltSearchBean();
            rltSearchBean.setOwnerCode(SysUtil.getCurrentUserInfo().getLoginCode());
            rltSearchBean.setRltCodes(rltCodes);
            rltSearchBean.setPageSize(1000);
            Page<ESCIRltInfo> rltInfoPage = rltSwitchSvc.searchRlt(rltSearchBean, LibType.PRIVATE);
            retRlt = rltInfoPage.getData();
        }

        // 过滤出不存在的关系
        List<String> retRltCiCodes = retRlt.stream()
                .map(ESCIRltInfo::getCiCode)
                .collect(Collectors.toList());
        List<String> filteredRltCodes = rltCodes.stream()
                .filter(code -> !retRltCiCodes.contains(code))
                .collect(Collectors.toList());
        // 将不存在的关系进行批量创建
        List<BindCiRltRequestDto> bindList = new ArrayList<>();
        for ( String rltCode : filteredRltCodes){
            String[] parts = rltCode.split("_");
            String sourceCode = parts[0];
            String targetCode = parts[2];
            Long rltClassId = rltCodeClassIdMap.get(rltCode);
            Long sourceId = ciCodeInfoMap.get(sourceCode).getId();
            Long targetId = ciCodeInfoMap.get(targetCode).getId();
            BindCiRltRequestDto bindRlt = BindCiRltRequestDto.builder()
                    .ownerCode(SysUtil.getCurrentUserInfo().getLoginCode())
                    .repetitionError(false)
                    .rltClassId(rltClassId).custom1("3")
                    .sourceCiId(sourceId)
                    .targetCiId(targetId).build();

            bindList.add(bindRlt);
        }
        List<ESCIRltInfo> newRlt = iamsCiRltPrivateSvc.bindBatchCiRltNoFilter(bindList);
        if(!BinaryUtils.isEmpty(newRlt)) {
            retRlt.addAll(newRlt);
        }

        AIDrawVo aiDrawVo = new AIDrawVo();
        aiDrawVo.setCi(esCiInfos);
        aiDrawVo.setRlt(retRlt);
        return aiDrawVo;
    }


    /**
     * 将Dify获取的返回JSON字符串转换为自动成图需要的数据结构
     *
     * @return AutoGraphVo 自动成图结果
     */
    public AutoGraphVo parseResponseToAutoGraphVo(String answer) {
        /*
          接收到的AI返回数据格式示例：
            {
              "centerElement": "QuickEA",
              "relations": ["联机访问", "批量访问"],
              "layoutType": 1,
              "graphType": "CONTEXT_GRAPH"
            }
         */
        // 解析 JSON 数据
        JSONObject answerObject = JSON.parseObject(answer);
        String centerElement = answerObject.getString("centerElement");
        // List<String> relations = answerObject.getJSONArray("relations").toJavaList(String.class);
        int layoutType = answerObject.getIntValue("layoutType");
        String graphType = answerObject.getString("graphType");

        // 构建返回的JSON数据，
        AutoGraphVo.LayoutScheme layoutScheme = new AutoGraphVo.LayoutScheme();
        layoutScheme.setColumnSpacing(100);
        layoutScheme.setLayoutType(layoutType);
        layoutScheme.setLayoutDirection("topToBottom");
        layoutScheme.setLayerSpacing(200);

        AutoGraphVo.DataScheme dataScheme = new AutoGraphVo.DataScheme();
        // 关系数据暂不处理
        // dataScheme.setRelatedRltIds(Arrays.asList(3175780809191824L, 3175780809192772L, 3175780809192451L, 3175780809153828L));
        // dataScheme.setRelatedClassIds(Collections.singletonList(3181127342665088L));

        // 查询中心元素的CI数据
        List<String> ciSearchWord = new ArrayList<>();
        ciSearchWord.add(centerElement);
        ESCISearchBean ciSearchBean = new ESCISearchBean();
        ciSearchBean.setPageNum(1);
        ciSearchBean.setPageSize(1);
        ciSearchBean.setWords(ciSearchWord);
        ciSearchBean.setWordLabel(true);
        Page<ESCIInfo> page = ciSwitchSvc.searchESCIByBean(ciSearchBean, LibType.DESIGN);
        if(!BinaryUtils.isEmpty(page.getData())){
            ESCIInfo ciObj = page.getData().get(0);
            dataScheme.setCoreCiCode(ciObj.getCiCode());
            dataScheme.setCiClass(ciObj.getClassId());
            log.info("查询到的中心元素主键：{}", ciObj.getCiPrimaryKey());
        }
        AutoGraphVo.ArtifactScheme artifactScheme = new AutoGraphVo.ArtifactScheme();
        // artifactScheme.setArtifactId(3181127342708098L);

        AutoGraphVo autoGraphVo = new AutoGraphVo();
        AutoGraphVo.Style style = new AutoGraphVo.Style();
        autoGraphVo.setValue(new AutoGraphVo.Value(layoutScheme, dataScheme, artifactScheme, style));
        autoGraphVo.setKey(graphType);

        return autoGraphVo;
    }

    /**
     * 根据分类名称获取分类信息
     * @return Map<分类名称, 分类ID>
     */
    private Map<String, Long> getClassIdsByName(Set<String> className) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termsQuery("className.keyword", className));
        List<ESCIClassInfo> listByQuery = esciClassSvc.getListByQuery(boolQueryBuilder);
        Map<String, Long> classNameIdMap = new HashMap<>();
        for(ESCIClassInfo classInfo : listByQuery){
            classNameIdMap.put(classInfo.getClassName(), classInfo.getId());
        }
        return classNameIdMap;
    }

    /**
     * 根据关系分类名称获取关系分类信息
     * @return Map<分类名称, 分类ID>
     */
    private Map<String, Long> getRltClassIdsByName(Set<String> className) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termsQuery("className.keyword", className));
        List<ESCIClassInfo> listByQuery = esRltClassSvc.getListByQuery(boolQueryBuilder);
        Map<String, Long> classNameIdMap = new HashMap<>();
        for(ESCIClassInfo classInfo : listByQuery){
            classNameIdMap.put(classInfo.getClassName(), classInfo.getId());
        }
        return classNameIdMap;
    }

}
