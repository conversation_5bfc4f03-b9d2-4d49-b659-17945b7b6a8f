package com.binary.framework.spring;

import java.util.Map;

import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.springframework.web.servlet.View;
import org.springframework.web.servlet.view.RedirectView;

import com.binary.core.util.BinaryUtils;

public class BinaryRedirectView implements View {

	private static final Cookie fraudulentCookie = new Cookie("jsessionid", BinaryUtils.getUUID());

	static {
		fraudulentCookie.setMaxAge(Integer.MAX_VALUE);
	}



	private RedirectView view;


	public BinaryRedirectView(RedirectView view) {
		this.view = view;
	}



	@Override
	public String getContentType() {
		return this.view.getContentType();
	}




	@Override
	public void render(Map<String, ?> model, HttpServletRequest request, HttpServletResponse response) throws Exception {
		if(request.isRequestedSessionIdFromCookie()) {
			this.view.render(model, request, response);
		}else {
			response.sendRedirect(this.view.getUrl());
		}
	}

}
