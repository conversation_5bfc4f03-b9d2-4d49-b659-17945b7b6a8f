package com.uinnova.product.eam.service.es;

import com.uinnova.product.eam.comm.model.es.AssetWarehouseDir;
import com.uino.dao.AbstractESBaseDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import jakarta.annotation.PostConstruct;

@Repository
@Slf4j
public class AssetWarehouseDirDao extends AbstractESBaseDao<AssetWarehouseDir, AssetWarehouseDir> {


    @Override
    public String getIndex() {
        return "uino_eam_asset_warehouse_dir";
    }

    @Override
    public String getType() {
        return "_doc";
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }

}
