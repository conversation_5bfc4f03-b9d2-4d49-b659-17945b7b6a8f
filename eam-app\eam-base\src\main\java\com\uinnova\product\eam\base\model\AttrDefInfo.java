package com.uinnova.product.eam.base.model;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
public class AttrDefInfo implements Serializable {

    @Comment("所属分类ID - 可能会变")
    private Long classId;

    @Comment("所属分类CLASS_CODE 唯一")
    private String classCode = "标准规范";

    @Comment("字段定义")
    private List<CcCiAttrDef> attrDefs;

    @Comment("隐藏字段")
    private List<String> hiddenAttrs;

    @Comment("下拉菜单属性")
    private List<String> selectableAttrs;

    @Comment("下拉菜单接口调用类型")
    private Map<String, String> callbackType;

    @Comment("下拉菜单数据字典调用类型")
    private Map<String, AttrDictInfo> dictType;

    @Comment("单选框属性")
    private List<String> radioAttrs;

    @Comment("多选框属性")
    private List<String> checkBoxAttrs;

    @Comment("适用场景子属性")
    private List<String> subScenarioAttrs;


}
