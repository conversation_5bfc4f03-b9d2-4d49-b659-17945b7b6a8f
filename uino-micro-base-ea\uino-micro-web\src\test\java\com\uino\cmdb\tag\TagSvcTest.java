package com.uino.cmdb.tag;

import static org.junit.Assert.assertEquals;

import java.util.ArrayList;
import java.util.List;

import jakarta.servlet.http.Cookie;

import com.uino.dao.BaseConst;
import org.elasticsearch.index.query.QueryBuilders;
import org.junit.After;
import org.junit.AfterClass;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uino.dao.cmdb.ESCIClassSvc;
import com.uino.dao.cmdb.ESCISvc;
import com.uino.dao.cmdb.ESCmdbCommSvc;
import com.uino.dao.cmdb.ESDirSvc;
import com.uino.dao.cmdb.ESTagSvc;
import com.uino.service.cmdb.microservice.impl.TagSvc;
import com.uino.bean.cmdb.base.CcCiClassDir;
import com.uino.bean.cmdb.base.ESCIClassInfo;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.base.ESCITagInfo;
import com.uino.bean.cmdb.base.ESTagRuleInfo;
import com.uino.bean.cmdb.base.ESTagRuleItem;
import com.uino.bean.cmdb.base.ESTagRuleItemGroup;
import com.uino.bean.cmdb.business.ClassNodeInfo;
import com.uino.bean.cmdb.query.ESAttrAggBean;
import com.uino.bean.cmdb.query.ESTagSearchBean;

@RunWith(SpringJUnit4ClassRunner.class)
public class TagSvcTest {

    @InjectMocks
    TagSvc tagSvc;

    private ESTagSvc esTagSvc;

    private ESDirSvc dirSvc;

    private ESCIClassSvc classSvc;

    private ESCISvc ciSvc;

    private ESCmdbCommSvc commSvc;

    @BeforeClass
    public static void setUpBeforeClass() throws Exception {
        MockHttpServletRequest request = new MockHttpServletRequest();
        Cookie cookie = new Cookie("token", "ca14ed042fe7912426b484f6ea5c754b4fb51ae4a72037e9127da51743e0d1f9650e6e78ed4466ac970acb6a0b2f46fd26958c4ab2d115cc71b34ca8a02db24c");
        request.setCookies(cookie);
        ServletRequestAttributes attributes = new ServletRequestAttributes(request);
        RequestContextHolder.setRequestAttributes(attributes);
    }

    @AfterClass
    public static void tearDownAfterClass() throws Exception {}

    @Before
    public void setUp() throws Exception {
        esTagSvc = Mockito.mock(ESTagSvc.class);
        ReflectionTestUtils.setField(tagSvc, "tagSvc", esTagSvc);

        dirSvc = Mockito.mock(ESDirSvc.class);
        ReflectionTestUtils.setField(tagSvc, "dirSvc", dirSvc);

        classSvc = Mockito.mock(ESCIClassSvc.class);
        ReflectionTestUtils.setField(tagSvc, "classSvc", classSvc);

        ciSvc = Mockito.mock(ESCISvc.class);
        ReflectionTestUtils.setField(tagSvc, "ciSvc", ciSvc);

        commSvc = Mockito.mock(ESCmdbCommSvc.class);
        ReflectionTestUtils.setField(tagSvc, "commSvc", commSvc);
    }

    @After
    public void tearDown() throws Exception {}

    @Test
    public void testSaveOrUpdateCITagRule() {
        List<ESTagRuleItem> items = new ArrayList<>();
        items.add(ESTagRuleItem.builder().classAttrId(123L).ruleOp(1).ruleVal("test").build());

        List<ESTagRuleItemGroup> itemGroups = new ArrayList<>();
        itemGroups.add(ESTagRuleItemGroup.builder().logicOp(1).items(items).build());

        List<ESTagRuleInfo> rules=new ArrayList<ESTagRuleInfo>();
        rules.add(ESTagRuleInfo.builder().classId(1L).itemGroups(itemGroups).build());

        ESCITagInfo tagInfo = new ESCITagInfo();
        tagInfo.setTagName("tagName");
        tagInfo.setDirId(1L);
        tagInfo.setRules(rules);

        Mockito.when(esTagSvc.getListByQuery(Mockito.any())).thenReturn(null);
        Mockito.when(esTagSvc.saveOrUpdate(Mockito.any(ESCITagInfo.class))).thenReturn(123L);

        List<CcCiAttrDef> attrDefs = new ArrayList<>();
        CcCiAttrDef def = new CcCiAttrDef();
        def.setId(123L);
        def.setProType(3);
        attrDefs.add(def);
        List<ESCIClassInfo> cls = new ArrayList<>();
        ESCIClassInfo ciClass = new ESCIClassInfo();
        ciClass.setCcAttrDefs(attrDefs);
        cls.add(ciClass);
        Mockito.when(classSvc.getListByQuery(Mockito.any())).thenReturn(cls);

        Long id = tagSvc.saveOrUpdateCITagRule(tagInfo);
        assertEquals(123, id.longValue());
    }

    @Test
    public void testGetCITagRuleById() {
        ESCITagInfo tag = new ESCITagInfo();
        tag.setTagName("test");
        Mockito.when(esTagSvc.getById(Mockito.anyLong())).thenReturn(tag);

        ESCITagInfo res = tagSvc.getCITagRuleById(123L);
        assertEquals("test", res.getTagName());
    }

    @Test
    public void testGetTagTree() {
        List<CcCiClassDir> dirs = new ArrayList<>();
        CcCiClassDir dir = new CcCiClassDir();
        dir.setId(123L);
        dir.setDirName("dirName");
        dirs.add(dir);
        Mockito.when(dirSvc.getSortListByQuery(Mockito.anyInt(), Mockito.anyInt(), Mockito.any(), Mockito.anyString(), Mockito.anyBoolean())).thenReturn(new Page<>(1, 1, 1, 1, dirs));

        List<ESCITagInfo> tags = new ArrayList<>();
        ESCITagInfo tag = new ESCITagInfo();
        tag.setDirId(123L);
        tag.setTagName("tagName");
        tag.setRules(new ArrayList<>());
        tags.add(tag);
        Mockito.when(esTagSvc.getSortListByQuery(Mockito.anyInt(), Mockito.anyInt(), Mockito.any(), Mockito.anyString(), Mockito.anyBoolean())).thenReturn(new Page<>(1, 1, 1, 1, tags));
        
        List<ClassNodeInfo> tagTree = tagSvc.getTagTree(BaseConst.DEFAULT_DOMAIN_ID);
        assertEquals("tagName", tagTree.get(0).getChildren().get(0).getName());
    }

    @Test
    public void testGetCIInfoListByTag() {
        ESCITagInfo tag = new ESCITagInfo();
        List<ESTagRuleItem> items = new ArrayList<>();
        items.add(ESTagRuleItem.builder().classAttrId(123L).ruleOp(1).ruleVal("test").build());

        List<ESTagRuleItemGroup> itemGroups = new ArrayList<>();
        itemGroups.add(ESTagRuleItemGroup.builder().logicOp(1).items(items).build());

        List<ESTagRuleInfo> rules=new ArrayList<ESTagRuleInfo>();
        rules.add(ESTagRuleInfo.builder().classId(1L).itemGroups(itemGroups).build());
        tag.setRules(rules);

        Mockito.when(esTagSvc.getQueryByTag(Mockito.any(ESCITagInfo.class))).thenReturn(QueryBuilders.boolQuery());

        Mockito.when(ciSvc.countByCondition(Mockito.any())).thenReturn(10L);

        Page<ESCIInfo> page = new Page<>(1, 10, 1, 1, new ArrayList<>());
        Mockito.when(ciSvc.getSortListByQuery(Mockito.anyInt(), Mockito.anyInt(), Mockito.any(), Mockito.anyString(), Mockito.anyBoolean())).thenReturn(page);

        List<CcCiInfo> cis = new ArrayList<>();
        CcCiInfo ciInfo = new CcCiInfo();
        cis.add(ciInfo);
        Mockito.when(commSvc.transEsInfoPage(Mockito.any(), Mockito.anyBoolean())).thenReturn(new Page<>(1, 1, 1, 1, cis));

        ESTagSearchBean bean = new ESTagSearchBean();
        bean.setTagInfo(tag);
        // 分类不存在
        Mockito.when(classSvc.getListByQuery(Mockito.any())).thenReturn(new ArrayList<>());
        try {
            tagSvc.getCIInfoListByTag(bean);
        } catch (Exception e) {
            assertEquals("BS_MNAME_CLASS_NOT_EXSIT", e.getMessage());
        }

        List<ESCIClassInfo> clsList = new ArrayList<>();
        ESCIClassInfo cls = new ESCIClassInfo();
        cls.setId(1L);
        List<CcCiAttrDef> attrDefs = new ArrayList<>();
        CcCiAttrDef def = new CcCiAttrDef();
        def.setId(123L);
        def.setProType(3);
        attrDefs.add(def);
        cls.setCcAttrDefs(attrDefs);
        clsList.add(cls);
        Mockito.when(classSvc.getListByQuery(Mockito.any())).thenReturn(clsList);
        // bean.setTagIds(Collections.singletonList(123L));
        Page<CcCiInfo> res = tagSvc.getCIInfoListByTag(bean);
        assertEquals(1, res.getData().size());

        Mockito.when(ciSvc.countByCondition(Mockito.any())).thenReturn(0L);
        Page<CcCiInfo> res2 = tagSvc.getCIInfoListByTag(bean);
        assertEquals(1, res2.getData().size());

        // 标签规则为空
        Mockito.when(esTagSvc.getQueryByTag(Mockito.any(ESCITagInfo.class))).thenReturn(null);
        Page<CcCiInfo> res1 = tagSvc.getCIInfoListByTag(bean);
        assertEquals(0, res1.getData().size());
    }

    @Test
    public void testDeleteById() {
        Mockito.when(esTagSvc.deleteById(Mockito.anyLong())).thenReturn(1);

        Integer res = tagSvc.deleteById(123L);
        assertEquals(1, res.intValue());
    }

    @Test
    public void testGetAttrValuesBySearchBean() {
        Mockito.when(ciSvc.queryAttrVal(1L, Mockito.any(ESAttrAggBean.class))).thenReturn(new Page<>());

        ESAttrAggBean searchBean = new ESAttrAggBean();
        searchBean.setClassId(123L);
        searchBean.setAttrName("attrName");
        tagSvc.getAttrValuesBySearchBean(1L, searchBean);
    }

    @Test
    public void testChangeTagDir() {
        Mockito.when(esTagSvc.updateByQuery(Mockito.any(), Mockito.anyString(), Mockito.anyBoolean())).thenReturn(true);

        ESCITagInfo tagInfo = new ESCITagInfo();
        tagInfo.setId(22L);
        tagInfo.setDirId(123L);
        Boolean res = tagSvc.changeTagDir(tagInfo);
        assertEquals(true, res);
    }
}
