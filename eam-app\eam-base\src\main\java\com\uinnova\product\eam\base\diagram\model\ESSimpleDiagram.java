package com.uinnova.product.eam.base.diagram.model;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Classname 中间类，存储视图基础信息和其包含组件信息(sheet、node、link)
 * @Description TODO
 * <AUTHOR>
 * @Date 2021-06-16-15:58
 */
@Data
public class ESSimpleDiagram implements Serializable {

    private static final long serialVersionUID = 1L;

    @Comment("视图基础信息")
    private ESDiagram esDiagram;

    @Comment("视图包含的所有sheet信息")
    private List<ESDiagramSheetDTO> sheetList;

    @Comment("视图包含的所有node信息")
    private List<ESDiagramNode> nodeList;

    @Comment("视图包含的所有link信息")
    private List<ESDiagramLink> linkList;

    public ESSimpleDiagram() {
    }

    public ESSimpleDiagram(ESDiagram esDiagram, List<ESDiagramSheetDTO> sheetList, List<ESDiagramNode> nodeList, List<ESDiagramLink> linkList) {
        this.esDiagram = esDiagram;
        this.sheetList = sheetList;
        this.nodeList = nodeList;
        this.linkList = linkList;
    }
}
