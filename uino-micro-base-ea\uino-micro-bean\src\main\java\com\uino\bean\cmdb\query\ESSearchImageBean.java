package com.uino.bean.cmdb.query;

import java.util.Set;

import com.uinnova.product.vmdb.comm.model.image.CCcImage;
import com.uino.bean.permission.query.SearchKeywordBean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 
 * <AUTHOR>
 *
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value="查询图标类",description = "查询图标信息")
public class ESSearchImageBean extends SearchKeywordBean {
	private static final long serialVersionUID = 1L;

	/**
	 * 查询条件
	 */
	@ApiModelProperty(value="查询条件")
	private CCcImage cdt;

	/**
	 * 图标名称s
	 */
	@ApiModelProperty(value="图标名称集合")
	private Set<String> imgNames;
	/**
	 * 图标全名称s
	 */
	@ApiModelProperty(value="图标全名称集合")
	private Set<String> imgFullNames;

	/**
	 * 图像路径
	 */
	@ApiModelProperty(value="图像路径集合")
	private Set<String> imgPaths;
}
