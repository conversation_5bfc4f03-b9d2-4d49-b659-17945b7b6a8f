package com.uinnova.product.eam.workable.listener;

import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.TaskService;
import org.flowable.engine.delegate.TaskListener;
import org.flowable.task.service.delegate.DelegateTask;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.Map;

/**
 * 将主流程分配的变量绑定到子流程中
 *
 * <AUTHOR>
 * @since 2023/6/15 17:09
 */
@Slf4j
@Service("bindTaskVariableListener")
public class BindTaskVariableListener implements TaskListener {

    @Resource
    private TaskService taskService;

    @Override
    public void notify(DelegateTask delegateTask) {
        Map<String, Object> variables = delegateTask.getVariables();
        Object childVariable = variables.get("childVariable");
        Map<String, Object> variablesLocal = taskService.getVariablesLocal(delegateTask.getId());
        variablesLocal.put("childVariable",childVariable);
        taskService.setVariablesLocal(delegateTask.getId(),variablesLocal);
        log.info("执行本地变量绑定监听器");
    }
}
