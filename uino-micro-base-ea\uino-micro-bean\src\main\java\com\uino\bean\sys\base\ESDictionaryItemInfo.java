package com.uino.bean.sys.base;

import java.io.Serializable;
import java.util.Map;

import com.binary.framework.bean.annotation.Comment;
import com.uino.bean.sys.enums.DictionaryOptionEnum;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 
 * <AUTHOR>
 *
 */
@Comment("字典表属性定义")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value="字典表属性定义",description = "字典表属性定义")
public class ESDictionaryItemInfo implements Serializable {

    /**
     * 
     */
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value="字典项ID",example = "123")
    @Comment("字典项ID[ID]")
    private Long id;

    @ApiModelProperty(value="所属字典表ID",example = "123")
    @Comment("所属字典表ID")
    private Long dictClassId;

    @ApiModelProperty(value="唯一标识")
    @Comment("唯一标识")
    private String keyCode;

    @ApiModelProperty(value="属性值")
    @Comment("属性值")
    private Map<String, String> attrs;

	@ApiModelProperty(value = "扩展属性")
	private Map<String, Object> extendAttrs;

    @ApiModelProperty(value="操作类型,READ:只读，WRITE:读写",example = "READ")
    @Comment("操作类型：READ:只读，WRITE:读写")
    private DictionaryOptionEnum option;

    @ApiModelProperty(value="所属域id",example = "123")
    @Comment("所属域[DOMAIN_ID]")
    private Long domainId;

    @ApiModelProperty(value = "创建人",example = "mike")
    @Comment("创建人[CREATOR]")
    private String creator;

    @ApiModelProperty(value = "修改人",example = "mike")
    @Comment("修改人[MODIFIER]")
    private String modifier;

    @ApiModelProperty(value="创建时间")
    @Comment("创建时间[CREATE_TIME]    创建时间:yyyyMMddHHmmss")
    private Long createTime;

    @ApiModelProperty(value="更新时间")
    @Comment("更新时间[MODIFY_TIME]    更新时间:yyyyMMddHHmmss")
    private Long modifyTime;

    @ApiModelProperty(value="是否初始字典项",example = "0")
    @Comment("是否初始字典[IS_INIT]")
    @Builder.Default
    private Integer isInit = 0;

    /**
     * 用于批量保存时记录序号-入库时会移除
     */
    @ApiModelProperty(value="用于批量保存时记录序号-入库时会移除",example = "1")
    private Integer index;
}
