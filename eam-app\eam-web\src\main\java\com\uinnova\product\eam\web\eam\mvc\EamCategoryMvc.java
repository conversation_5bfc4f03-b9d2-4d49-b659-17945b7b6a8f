package com.uinnova.product.eam.web.eam.mvc;

import com.alibaba.fastjson.JSONObject;
import com.binary.core.util.BinaryUtils;
import com.binary.framework.web.RemoteResult;
import com.uinnova.product.eam.base.model.BaseQueryDiagramDto;
import com.uinnova.product.eam.model.EamCategoryCdt;
import com.uinnova.product.eam.model.ModuleInfo;
import com.uinnova.product.eam.model.bm.CategoryNode;
import com.uinnova.product.eam.model.diagram.DiagramBindLogo;
import com.uinnova.product.eam.model.dto.EamCategoryDTO;
import com.uinnova.product.eam.model.dto.ModelNavigateDTO;
import com.uinnova.product.eam.model.enums.AssetType;
import com.uinnova.product.eam.model.enums.OperatorType;
import com.uinnova.product.eam.model.vo.EamCategoryVO;
import com.uinnova.product.eam.model.vo.ModelDiagramResp;
import com.uinnova.product.eam.service.EamCategorySvc;
import com.uinnova.product.eam.service.EamModelSvc;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uino.bean.cmdb.base.LibType;
import com.uino.util.sys.SysUtil;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * 资产仓库目录&文件夹
 * <AUTHOR>
 */
@RestController
@RequestMapping("/eam")
public class EamCategoryMvc {
    @Resource
    private EamCategorySvc categorySvc;
    @Resource
    private EamModelSvc modelSvc;

    @PostMapping("/category/saveOrUpdate")
    @ModDesc(desc = "保存或更新文件夹信息", pDesc = "文件夹信息", rDesc = "文件夹id", rType = RemoteResult.class)
    public RemoteResult saveOrUpdate(@RequestBody EamCategoryVO vo) {
        BinaryUtils.checkEmpty(vo, "vo");
        BinaryUtils.checkEmpty(vo.getDirName(),"dirName");
        String loginCode = SysUtil.getCurrentUserInfo().getLoginCode();
        //资产库新建文件夹需要判断下父目录有无创建权限
        if (vo.getId() == null) {
            categorySvc.checkOperatorPermission(Arrays.asList(vo.getParentId()), AssetType.FOLDER, loginCode, vo.getLibType(), OperatorType.CREATE);
        } else {
            //校验资产库目录是否有重命名权限
            categorySvc.checkOperatorPermission(Arrays.asList(vo.getId()), AssetType.FOLDER, loginCode, vo.getLibType(), OperatorType.RENAME);
        }
        Long id = modelSvc.saveOrUpdateCategory(vo, vo.getLibType());
        return new RemoteResult(id);
    }

    @PostMapping("/category/deleteBatch")
    @ModDesc(desc = "批量删除文件夹或视图或方案", pDesc = "文件夹id/视图id/方案id", rDesc = "删除的文件夹id集合", rType = RemoteResult.class)
    public RemoteResult deleteBatch(@RequestBody EamCategoryCdt cdt) {
        cdt.setOwnerCode(SysUtil.getCurrentUserInfo().getLoginCode());
        Integer result = modelSvc.deleteBatch(cdt);
        return new RemoteResult(result);
    }

    @GetMapping("/category/queryList")
    @ModDesc(desc = "全量查询文件夹", pDesc = "顶级目录id", rDesc = "文件夹信息", rType = RemoteResult.class)
    public RemoteResult queryList(@RequestParam(defaultValue = "PRIVATE") LibType libType, @RequestParam Long rootId) {
        List<EamCategoryDTO> result = categorySvc.queryList(libType, rootId);
        return new RemoteResult(result);
    }

    @GetMapping("/category/queryListByParentId")
    @ModDesc(desc = "通过父级id查询文件夹", pDesc = "", rDesc = "文件夹信息", rType = RemoteResult.class)
    public RemoteResult queryListByParentId(@RequestParam(defaultValue = "PRIVATE") LibType libType, @RequestParam Long parentId) {
        BinaryUtils.checkEmpty(parentId, "parentId");
        List<EamCategoryDTO> result = categorySvc.queryListByParentId(parentId, null, false, libType);
        return new RemoteResult(result);
    }

    @PostMapping("/category/copyBatch")
    @ModDesc(desc = "批量复制文件夹、视图OR方案", pDesc = "文件夹、视图OR方案id集合,目标文件夹id", rDesc = "复制结果", rType = RemoteResult.class)
    public RemoteResult copyBatch(@RequestBody EamCategoryCdt cdt) {
        BinaryUtils.checkEmpty(cdt.getTargetId(), "targetId");
        Integer result = modelSvc.copyBatch(cdt);
        return new RemoteResult(result);
    }

    @PostMapping("/category/moveBatch")
    @ModDesc(desc = "批量移动文件夹", pDesc = "文件夹id集合,目标文件夹id", rDesc = "复制结果", rType = RemoteResult.class)
    public RemoteResult moveBatch(@RequestBody EamCategoryCdt cdt) {
        Integer result = categorySvc.moveBatch(cdt);
        return new RemoteResult(result);
    }

    @GetMapping("/category/createModel/{modelId}/{parentId}")
    @ModDesc(desc = "创建模型", pDesc = "建模工艺id,目录id", rDesc = "模型id", rType = RemoteResult.class)
    public RemoteResult createModel(@PathVariable Long modelId, @PathVariable Long parentId, @RequestParam String modelName) {
        BinaryUtils.checkEmpty(modelId, "modelId");
        BinaryUtils.checkEmpty(parentId, "parentId");
        String msg = modelSvc.createModel(modelId, parentId, modelName);
        return new RemoteResult(true, BinaryUtils.isEmpty(msg) ? 1 : 2, msg);
    }

    @PostMapping("/diagram/createModelDiagram")
    @ModDesc(desc = "创建模型视图", pDesc = "模型目录id或视图id+ciCode", rDesc = "视图加密id", rType = RemoteResult.class)
    public RemoteResult createModelDiagram(@RequestBody EamCategoryCdt cdt) {
        ModelDiagramResp resp = modelSvc.createModelDiagram(cdt);
        return new RemoteResult(resp);
    }

    @PostMapping("/model/getModelNavigate")
    @ModDesc(desc = "获取模型导航条", pDesc = "视图id", rDesc = "导航条", rType = RemoteResult.class)
    public RemoteResult getModelNavigate(@RequestBody BaseQueryDiagramDto dto) {
        BinaryUtils.checkEmpty(dto.getDiagramId(), "diagramId");
        BinaryUtils.checkEmpty(dto.getOwnerCode(), "ownerCode");
        ModelNavigateDTO result = modelSvc.getModelNavigate(dto);
        return new RemoteResult(result);
    }

    @PostMapping("/model/getDiagramBindLogo")
    @ModDesc(desc = "获取模型视图下钻icon", pDesc = "视图id", rDesc = "导航条", rType = RemoteResult.class)
    public RemoteResult getDiagramBindLogo(@RequestBody BaseQueryDiagramDto dto) {
        List<DiagramBindLogo> result = modelSvc.getDiagramBindLogo(dto);
        return new RemoteResult(result);
    }

    @GetMapping("/category/published/library")
    @ModDesc(desc = "获取资产发布分库", pDesc = "", rDesc = "分库列表", rType = RemoteResult.class)
    public RemoteResult getPublishedLibrary(@RequestParam AssetType assetType, @RequestParam String assetId) {
        List<EamCategoryDTO> result = categorySvc.getPublishedLibrary(assetType, assetId);
        return new RemoteResult(result);
    }

    @GetMapping("/category/published")
    @ModDesc(desc = "获取资产发布目录, 可以按名称模糊查询", pDesc = "", rDesc = "发布目录", rType = RemoteResult.class)
    public RemoteResult getPublishedCategory(@RequestParam AssetType assetType, @RequestParam(required = false) Long rootId, @RequestParam String assetId, @RequestParam(required = false) String queryDirName) {
        List<EamCategoryDTO> result = categorySvc.getPublishedCategory(assetType, rootId, assetId, queryDirName);
        return new RemoteResult(result);
    }


    @GetMapping("/category/model/version/checkout")
    @ModDesc(desc = "模型历史版本切换目录列表", pDesc = "", rDesc = "模型历史版本切换目录列表", rType = RemoteResult.class)
    public RemoteResult modelVersionCheckOut(@RequestParam Long modelId, @RequestParam Long tagId) {
        List<EamCategoryDTO> result = categorySvc.modelVersionCheckOut(modelId, tagId);
        return new RemoteResult(result);
    }

    @GetMapping("/model/selectNodeTree")
    @ModDesc(desc = "查询批量发布列表", pDesc = "目录id,私有库or设计库", rDesc = "目录信息", rType = RemoteResult.class)
    public RemoteResult selectNodeTree(@RequestParam Long dirId, @RequestParam LibType libType) {
        List<CategoryNode> nodes = modelSvc.selectNodeTree(dirId, libType);
        return new RemoteResult(nodes);
    }

    @GetMapping("/model/queryModuleTree")
    @ModDesc(desc = "查询已发布的建模层级详细信息", pDesc = "", rDesc = "模型工艺信息", rType = RemoteResult.class)
    public RemoteResult queryModuleTree() {
        List<ModuleInfo> result = modelSvc.queryModuleInfo();
        return new RemoteResult(result);
    }

    @GetMapping("/category/design/permission/library")
    @ModDesc(desc = "当前用户权限资产分库目录", pDesc = "", rDesc = "当前用户权限资产分库目录", rType = RemoteResult.class)
    public RemoteResult queryDesignPermissionRootList() {
        List<EamCategoryDTO> result = categorySvc.queryDesignPermissionRootList(SysUtil.getCurrentUserInfo().getLoginCode());
        return new RemoteResult(result);
    }

    @PostMapping("/category/moveCheckout/list")
    @ModDesc(desc = "获取复制/移动/检出文件夹", pDesc = "", rDesc = "获取复制/移动/检出文件夹", rType = RemoteResult.class)
    public RemoteResult getMoveCheckoutCategory(@RequestBody JSONObject jsonObject) {
        AssetType assetType = jsonObject.getObject("assetType", AssetType.class);
        Long modelId = jsonObject.getLong("modelId");
        Long rootId = jsonObject.getLong("rootId");
        LibType libType = jsonObject.getObject("libType", LibType.class);
        List<EamCategoryDTO> result = categorySvc.getMoveCheckoutCategory(assetType, modelId, rootId, libType);
        return new RemoteResult(result);
    }

    @GetMapping("/category/workbench/list")
    @ModDesc(desc = "工作台-快捷入口获取目录列表", pDesc = "", rDesc = "工作台-快捷入口获取目录列表", rType = RemoteResult.class)
    public RemoteResult getWorkbenchCategory(@RequestParam AssetType assetType) {
        List<EamCategoryDTO> result = categorySvc.getWorkbenchCategory(assetType);
        return new RemoteResult(result);
    }
}
