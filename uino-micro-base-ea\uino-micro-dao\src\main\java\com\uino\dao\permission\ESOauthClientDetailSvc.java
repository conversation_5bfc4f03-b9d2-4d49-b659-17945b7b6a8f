package com.uino.dao.permission;

import java.util.*;

import jakarta.annotation.PostConstruct;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.binary.core.encrypt.Encrypt;
import com.uino.dao.AbstractESBaseDao;
import com.uino.dao.ESConst;
import com.uino.util.sys.CommonFileUtil;
import com.uino.bean.permission.base.OAuthClientDetail;

import lombok.extern.slf4j.Slf4j;

/**
 *
 * <AUTHOR>
 *
 */
@Service
@Slf4j
public class ESOauthClientDetailSvc extends AbstractESBaseDao<OAuthClientDetail, JSONObject> {


	@Value("${oauth.client.id:}")
	private String clientId;

	@Value("${oauth.server.token_callback.url:}")
	private String tokenCallbaclUrl;

	@Override
	public String getIndex() {
		// TODO Auto-generated method stub
		return ESConst.INDEX_OAUTH_CLIENT;
	}

	@Override
	public String getType() {
		// TODO Auto-generated method stub
		return ESConst.INDEX_OAUTH_CLIENT;

	}

	@PostConstruct
	public void init() {
		List<OAuthClientDetail> datas = CommonFileUtil.getData("/initdata/uino_oauth_client.json",
				OAuthClientDetail.class);
        OAuthClientDetail currentOAuthClientDetail = null;
		if (datas != null) {
            for (OAuthClientDetail data : datas) {
                if (data.getClientCode().equals(clientId)) {
                    currentOAuthClientDetail = data;
                }
                data.setClientSecret(Encrypt.encrypt(data.getClientSecret()));
            }
        }
		super.initIndex(datas);
		if(currentOAuthClientDetail!=null){
			Map<String, Object> attrs = currentOAuthClientDetail.getAttrs();
			if(attrs==null){
				attrs = new HashMap<>();
			}
			if (StringUtils.isNotBlank(tokenCallbaclUrl)) {
				String[] split = tokenCallbaclUrl.split("/");
				String urlPrefix = split[0]+split[1]+"//"+split[2];
				attrs.put("clientIndexUrl",urlPrefix+"/home#/");
				currentOAuthClientDetail.setAttrs(attrs);
			}
			currentOAuthClientDetail.setCodeToTokenUrl(Collections.singleton(tokenCallbaclUrl));
		}
		saveOrUpdate(currentOAuthClientDetail);
	}

}
