package com.uinnova.product.eam.model.cj.domain;

import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.model.cj.vo.BindAssetVo;
import lombok.Data;
import java.util.List;
import java.util.Map;

@Data
@Comment("交付物模板基本信息表[UINO_CJ_Deliverable_Template]")
public class DeliverableTemplate implements EntityBean {

    @Comment("主键")
    private Long id;
    @Comment("业务主键")
    private Long businessId;
    @Comment("模板名称")
    private String templateName;
    @Comment("模板绑定的资产Map")
    private Map<String,String> bindAssetMap;
    @Comment("模板绑定的资产List")
    private List<BindAssetVo> bindAssetList;
    @Comment("技术方案类型（数据字典ID）")
    private Long proposalType;
    @Comment("模板说明")
    private String templateDesc;
    @Comment("备注信息")
    private String remarkInfo;
    @Comment("引用数量")
    private Integer quoteNum;
    @Comment("版本")
    private String version;
    @Comment("创建人")
    private String creator;
    @Comment("修改人")
    private String modifier;
    @Comment("创建时间")
    private Long createTime;
    @Comment("修改时间")
    private Long modifyTime;
    @Comment("模板状态 0:禁用 1:设计中 变更 2:设计中 草稿 3:再用 已发布 4:再用 已屏蔽 5:停用 已发布")
    private Integer status;
    @Comment("模板章节基本信息")
    private PlanTemplateChapter planTemplateChapter;
    @Comment("模板章节数据")
    private List<PlanTemplateChapterData> chapterDataList;

    @Comment("方案发布资产-目录名称")
    private String echoDirName;

    @Comment("方案发布资产-目录id")
    private Long assetsDirId;

    @Comment("方案发布资产-目录类型")
    private Integer dirType;

    @Comment("方案审批类型")
    private Integer approvalType;

    @Comment("分类目录id")
    private Long domainDirId;
}
