package com.uinnova.product.eam.service;


import com.binary.jdbc.Page;
import com.uinnova.product.eam.base.diagram.model.DiagramShareRecord;
import com.uinnova.product.eam.base.diagram.model.ESDiagramShareRecordResult;
import com.uinnova.product.eam.base.diagram.model.ShareRecordQueryBean;

import java.util.List;

@Deprecated
public interface ShareDiagramSvc {

    Page<ESDiagramShareRecordResult> queryShareDirgram(ShareRecordQueryBean queryBean);

    DiagramShareRecord queryShare(Long diagramId, Long sharedUserId);

}
