package com.uino.util.message.queue.tools;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.LockSupport;

/**
 * ID generation factory
 *
 * @Author: YGQ
 * @Create: 2021-05-24 12:32
 **/
public class SnowFlakeFactory {

    /**
     * Starting timestamp
     * 2000-01-01 00:00:00
     */
    private final static long START_STMP = 1577808000000L;
    private final static long SEQUENCE_BIT = 12;
    private final static long MACHINE_BIT = 5;
    private final static long DATACENTER_BIT = 5;

    /**
     * Maximum value of each part
     * MAX_DATACENTER_NUM = 31
     * MAX_MACHINE_NUM = 31
     * MAX_SEQUENCE = 4095
     */
    private final static long MAX_DATACENTER_NUM = ~(-1L << DATACENTER_BIT);
    private final static long MAX_MACHINE_NUM = ~(-1L << MACHINE_BIT);
    private final static long MAX_SEQUENCE = ~(-1L << SEQUENCE_BIT);

    private final static long MACHINE_LEFT = SEQUENCE_BIT;
    private final static long DATACENTER_LEFT = SEQUENCE_BIT + MACHINE_BIT;
    private final static long TIMESTMP_LEFT = DATACENTER_LEFT + DATACENTER_BIT;

    private long datacenterId = 0L;
    private long machineId = 0L;
    private long sequence = 0L;
    private long lastStmp = -1L;

    /**
     * The maximum tolerance time, in milliseconds, that is, if the clock just dials back the time specified by the variable, then wait for the corresponding time;
     * considering the high performance of the sequence service, this value is not easy to be too large
     */
    private static final long MAX_BACKWARD_MS = 5L;
    private long maxExtension = 2L;
    /**
     * Keep machineId and lastTimestamp, as well as backup machineId and its corresponding lastTimestamp
     */
    private static Map<Long, Long> machineIdLastTimeMap = new ConcurrentHashMap<>();

    /**
     * Initialize the data center location and machine identification
     * 0 < datacenterId < MAX_DATACENTER_NUM 31
     * 0 < machineId < MAX_MACHINE_NUM 31
     *
     * @param datacenterId datacenterId
     * @param machineId    machineId
     */
    public SnowFlakeFactory(long datacenterId, long machineId) {
        if (datacenterId > MAX_DATACENTER_NUM || datacenterId < 0) {
            throw new IllegalArgumentException(" datacenterId Must be between[0,31] ");
        }
        if (machineId > MAX_MACHINE_NUM || machineId < 0) {
            throw new IllegalArgumentException(" machineId Must be between[0,31] ");
        }
        this.datacenterId = datacenterId;
        this.machineId = machineId;
        machineIdLastTimeMap.put(machineId, getNewstmp());
    }

    /**
     * Generate the next ID
     *
     * @return id
     */
    public synchronized String nextId() {
        long extension = 0L;
        // Get the current time in milliseconds
        long currStmp = getNewstmp();
        if (currStmp < lastStmp) {
            long offset = lastStmp - currStmp;
            if (offset <= MAX_BACKWARD_MS) {
                try {
                    LockSupport.parkNanos(TimeUnit.MILLISECONDS.toNanos(offset));
                    currStmp = getNewstmp();
                    if (currStmp < lastStmp) {
                        extension += 1;
                        if (extension > maxExtension) {
                            throw new RuntimeException(String.format("The clock moves backward. Reject the generated id %d milliseconds", lastStmp - currStmp));
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            } else {
                extension += 1;
                if (extension > maxExtension) {
                    throw new RuntimeException(String.format("The clock moves backward, beyond the extension bit, rejects the generated id %d milliseconds", lastStmp - currStmp));
                }
                tryGenerateKeyOnBackup(currStmp);
            }
        }

        if (currStmp == lastStmp) {
            sequence = (sequence + 1) & MAX_SEQUENCE;
            if (sequence == 0L) {
                currStmp = getNextMill();
            }
        } else {
            sequence = 0L;
        }

        lastStmp = currStmp;

        /*
         * Timestamp part
         * Data center part
         * Machine identification part
         * Serial number part
         * */
        long id = (currStmp - START_STMP) << (TIMESTMP_LEFT - extension)
                | datacenterId << DATACENTER_LEFT
                | machineId << MACHINE_LEFT
                | sequence;
        return String.valueOf(id);
    }

    /**
     * Spin lock gets the current timestamp
     *
     * @return long
     */
    private long getNextMill() {
        long mill = getNewstmp();
        while (mill <= lastStmp) {
            mill = getNewstmp();
        }
        return mill;
    }

    /**
     * Get the current time in milliseconds
     *
     * @return long
     */
    private long getNewstmp() {
        return System.currentTimeMillis();
    }

    /**
     * Try to generate the core optimization code on the backup machineId of the machineId.
     * In the method tryGenerateKeyOnBackup(), BACKUP_COUNT means that the more the number of backup machineIds,
     * the stronger the ability of the sequence service to avoid the impact of clock callback,
     * but the fewer the sequence services that can be deployed, the BACKUP_COUNT is set to 3.
     * Up to 1024 (3+1) or 256 sequence services can be deployed, which is completely sufficient,
     * and the ability to resist the impact of clock callback is also greatly guaranteed.
     *
     * @param currentMillis current time
     */
    private long tryGenerateKeyOnBackup(long currentMillis) {
        for (Map.Entry<Long, Long> entry : machineIdLastTimeMap.entrySet()) {
            this.machineId = entry.getKey();
            Long tempLastTime = entry.getValue();
            lastStmp = tempLastTime == null ? 0L : tempLastTime;
            if (lastStmp <= currentMillis) {
                return lastStmp;
            }
        }
        throw new IllegalStateException("The clock is moving backwards, the current time is " + currentMillis + "Milliseconds, machine Id mapping = " + machineIdLastTimeMap);
    }

}