package com.uinnova.product.eam.base.diagram.model;

import com.binary.framework.bean.annotation.Comment;
import com.uino.bean.permission.base.SysUser;

import java.io.Serializable;
import java.util.List;

/**
 * @Classname
 * @Description TODO
 * <AUTHOR>
 * @Date 2021-06-18-14:21
 */
public class ESSimpleDiagramDTO implements Serializable {
    @Comment("视图基础信息")
    private ESDiagram diagram;

    @Comment("视图作者信息")
    private SysUser creator;

    @Comment("视图分享记录")
    private List<ESDiagramShareRecordResult> shareRecords;

    public ESDiagram getDiagram() {
        return diagram;
    }

    public void setDiagram(ESDiagram diagram) {
        this.diagram = diagram;
    }

    public SysUser getCreator() {
        return creator;
    }

    public void setCreator(SysUser creator) {
        this.creator = creator;
    }

    public List<ESDiagramShareRecordResult> getShareRecords() {

        return shareRecords;
    }

    public void setShareRecords(List<ESDiagramShareRecordResult> shareRecords) {
        this.shareRecords = shareRecords;
    }

    @Override
    public String toString() {
        return "ESSimpleDiagramDTO{" +
                "diagram=" + diagram +
                ", creator=" + creator +
                ", shareRecords=" + shareRecords +
                '}';
    }
}
