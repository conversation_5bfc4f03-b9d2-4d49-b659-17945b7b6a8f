package com.uino.bean.cmdb.base.dataset.batch;

import com.alibaba.fastjson.JSONObject;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 数据集执行结果按路径拆分为Sheet的Bean
 *
 * <AUTHOR>
 * @version 2020-4-28
 */
public class DataSetExeResultSheet implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    private String id;

    /**
     * 数据集ID
     */
    private Long dataSetId;

    /**
     * 根据路径拆分为Sheet的Id
     */
    private String sheetId;

    /**
     * 根据路径拆分为Sheet的名称
     */
    private String sheetName;

    /**
     * 起始CIID
     */
    private Long startCiId;

    /**
     * 遍历结果中的CI属性及属性值Map
     */
    private Map<String, Object> attrs;

    /**
     * 遍历结果中包含的CI分类集合
     */
    private List<Long> classIds;

    private List<String> classNamePath;

    /**
     * 数据生成时间
     */
    private Long createTime;

    /**
     * 数据的表头
     */
    private List<Map<String, String>> headers;

    private Long domainId;

    public List<String> getClassNamePath() {
        return classNamePath;
    }

    public void setClassNamePath(List<String> classNamePath) {
        this.classNamePath = classNamePath;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Long getDataSetId() {
        return dataSetId;
    }

    public void setDataSetId(Long dataSetId) {
        this.dataSetId = dataSetId;
    }

    public String getSheetId() {
        return sheetId;
    }

    public void setSheetId(String sheetId) {
        this.sheetId = sheetId;
    }

    public String getSheetName() {
        return sheetName;
    }

    public void setSheetName(String sheetName) {
        this.sheetName = sheetName;
    }

    public Long getStartCiId() {
        return startCiId;
    }

    public void setStartCiId(Long startCiId) {
        this.startCiId = startCiId;
    }

    public Map<String, Object> getAttrs() {
        return attrs;
    }

    public void setAttrs(Map<String, Object> attrs) {
        this.attrs = attrs;
    }

    public List<Long> getClassIds() {
        return classIds;
    }

    public void setClassIds(List<Long> classIds) {
        this.classIds = classIds;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public List<Map<String, String>> getHeaders() {
        return headers;
    }

    public void setHeaders(List<Map<String, String>> headers) {
        this.headers = headers;
    }

    public Long getDomainId() {
        return domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }

    public JSONObject toJson() {
        JSONObject json = new JSONObject();
        json.put("id", id);
        json.put("domainId", domainId);
        json.put("dataSetId", dataSetId);
        json.put("sheetId", sheetId);
        json.put("sheetName", sheetName);
        json.put("attrs", attrs);
        json.put("classIds", classIds);
        json.put("createTime", createTime);
        json.put("headers", headers);
        return json;
    }
}