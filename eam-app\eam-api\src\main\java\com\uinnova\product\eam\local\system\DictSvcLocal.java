package com.uinnova.product.eam.local.system;

import com.uinnova.product.eam.api.IDictAPIClient;
import com.uinnova.product.eam.base.model.DictInfo;
import com.uinnova.product.eam.service.system.IDictSvc;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Service
public class DictSvcLocal implements IDictAPIClient {

    @Resource
    private IDictSvc dictSvc;

    @Override
    public List<DictInfo> selectListByType(String codeType, String parentCode, String className) {
        return dictSvc.selectListByType(Collections.singletonList(codeType), parentCode, className);
    }

    @Override
    public Map<String, List<DictInfo>> selectGroupList(List<String> codeTypes, String className) {
        return dictSvc.selectGroupList(codeTypes, className);
    }

    @Override
    public Map<String, String> getAllInfo(List<String> codeTypes,String className) {
        return dictSvc.getAllInfo(codeTypes,className);
    }
}
