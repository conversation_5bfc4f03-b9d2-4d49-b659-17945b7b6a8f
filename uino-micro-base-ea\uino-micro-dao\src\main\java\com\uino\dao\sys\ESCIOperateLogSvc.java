package com.uino.dao.sys;

import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import jakarta.annotation.PostConstruct;

import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.binary.core.util.BinaryUtils;
import com.uinnova.product.vmdb.comm.model.ci.CcCi;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uino.bean.sys.base.ESCIOperateLog;
import com.uino.dao.AbstractESBaseDao;
import com.uino.dao.ESConst;
import com.uino.util.sys.SysUtil;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
public class ESCIOperateLogSvc extends AbstractESBaseDao<ESCIOperateLog, JSONObject> {

	@Override
	public String getIndex() {
		return ESConst.INDEX_CI_OPERATE_LOG;
	}

	@Override
	public String getType() {
		return ESConst.INDEX_CI_OPERATE_LOG;
	}

	@PostConstruct
	public void init() {
		super.initIndex(5);
	}

	@Override
	public Integer saveOrUpdateBatch(List<ESCIOperateLog> logs) {
		if (BinaryUtils.isEmpty(logs)) {
			return 1;
		}
		String ipAddress = SysUtil.getIpAddress(null);
		Integer pageSize = 3000;
		while (logs.size() > 0) {
			List<ESCIOperateLog> temList = logs.subList(0, logs.size() > pageSize ? pageSize : logs.size());
			JSONArray list = new JSONArray();
			temList.forEach(temp -> {
				duplicateAttrFilter(temp);
				if (!(temp.getDynamic().equals(SysUtil.StaticUtil.LOG_DYNAMIC_UPDATE) && temp.getOldAttrs().size() == 0
						&& temp.getNewAttrs().size() == 0)) {
					temp.setIpAddress(ipAddress);
					String jsonStr = JSON.toJSONString(temp);
					JSONObject json = JSON.parseObject(jsonStr);
					list.add(json);
				}
			});
			this.saveOrUpdateBatchMessage(list, true);
			logs.removeAll(temList);
		}
		return 1;
	}

	/**
	 * 过滤重复字段
	 * 
	 * @param ciLog
	 */
	public void duplicateAttrFilter(ESCIOperateLog ciLog) {
		if (ciLog != null && ciLog.getDynamic().equals(SysUtil.StaticUtil.LOG_DYNAMIC_UPDATE)) {
			Map<String, String> newAttrs = ciLog.getNewAttrs();
			Map<String, String> oldAttrs = ciLog.getOldAttrs();
			Iterator<Map.Entry<String, String>> it = oldAttrs.entrySet().iterator();
			while (it.hasNext()) {
				String key = it.next().getKey();
				boolean isDuplicate = (oldAttrs.get(key) == null && newAttrs.get(key) == null)
						|| (oldAttrs.get(key) != null && newAttrs.get(key) != null
								&& oldAttrs.get(key).equals(newAttrs.get(key)));
				if (isDuplicate) {
					it.remove();
					newAttrs.remove(key);
				}
			}
		}
	}

	/**
	 * 构建CI操作日志实例
	 *
	 * @param sourceId
	 *            来源ID 1：页面；2：配置处理；3：DIX
	 * @param dynamic
	 *            动态 1：添加；2：删除；3修改
	 * @param oldAttrs
	 *            旧属性值
	 * @param newAttrs
	 *            新属性值
	 * @param className
	 *            分类名， 可不传
	 * @param ci
	 *            CiId必填
	 * @return
	 */
	public static ESCIOperateLog buildLogRecord(Long sourceId, Integer dynamic, List<CcCiAttrDef> attrDefs,
			Map<String, String> oldAttrs, Map<String, String> newAttrs, String className, CcCi ci) {
		Assert.notNull(dynamic, "X_PARAM_NOT_NULL${name:dynamic}");
		Assert.notNull(className, "X_PARAM_NOT_NULL${name:className}");
		Assert.notNull(ci, "X_PARAM_NOT_NULL${name:ci}");
		Assert.notNull(ci.getCiCode(), "X_PARAM_NOT_NULL${name:ciCode}");
		Assert.notNull(ci.getCiPrimaryKey(), "X_PARAM_NOT_NULL${name:ciPrimaryKey}");
		Assert.notEmpty(attrDefs, "X_PARAM_NOT_NULL${name:attrDefs}");
		Map<String, String> oldCiAttrs = new HashMap<String, String>();
		if (!BinaryUtils.isEmpty(oldAttrs)) {
			oldCiAttrs = oldAttrs.entrySet().stream().filter((e) -> !BinaryUtils.isEmpty(e.getValue()))
					.collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
		}
		Map<String, String> newCiAttrs = new HashMap<>();
		if (!BinaryUtils.isEmpty(newAttrs)) {
			newCiAttrs = newAttrs.entrySet().stream().filter((e) -> !BinaryUtils.isEmpty(e.getValue()))
					.collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
		}

		// getRealAttrs(newAttrs, attrDefs);
		// getRealAttrs(oldAttrs, attrDefs);
		ESCIOperateLog record = new ESCIOperateLog();
		if (dynamic.equals(SysUtil.StaticUtil.LOG_DYNAMIC_INSERT)) {
			Assert.notEmpty(newCiAttrs, "X_PARAM_NOT_NULL${name:newAttrs}");
			record.setNewAttrs(newCiAttrs);
		} else if (dynamic.equals(SysUtil.StaticUtil.LOG_DYNAMIC_DELETE)) {
			Assert.notEmpty(oldCiAttrs, "X_PARAM_NOT_NULL${name:oldAttrs}");
			record.setOldAttrs(oldCiAttrs);
		} else if (dynamic.equals(SysUtil.StaticUtil.LOG_DYNAMIC_UPDATE)) {
			Assert.notEmpty(newCiAttrs, "X_PARAM_NOT_NULL${name:newAttrs}");
			Assert.notEmpty(oldCiAttrs, "X_PARAM_NOT_NULL${name:oldAttrs}");
			record.setNewAttrs(newCiAttrs);
			record.setOldAttrs(oldCiAttrs);
		}
		if (sourceId == null) {
			sourceId = 1L;
		}
		record.setSourceId(sourceId);
		if (sourceId == 1L) {
			try {
				record.setOperator(SysUtil.getCurrentUserInfo().getLoginCode());
			} catch (Exception e) {
				record.setOperator("system");
			}
		} else if (sourceId == 2L) {
			record.setOperator("CP");
		} else if (sourceId == 3L) {
			record.setOperator("DIX");
		} else {
			record.setOperator("system");
		}
		record.setCiId(ci.getId());
		record.setCiCode(ci.getCiCode());
		record.setCiPrimaryKey(ci.getCiPrimaryKey());
		record.setCiClassName(className);
		record.setDynamic(dynamic);
		record.setLibaryId(1);
		record.setDomainId(ci.getDomainId());
		record.setProNames(attrDefs.stream().map(CcCiAttrDef::getProName).collect(Collectors.toList()));
		return record;
	}
}
