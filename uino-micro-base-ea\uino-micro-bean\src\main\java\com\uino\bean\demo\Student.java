package com.uino.bean.demo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @ApiModel 作用于类上, 主要生成对于类的描述.
 * @ApiModelProperty 作用属性字段上, 主要生成对于该字段的描述.
 **/
@ApiModel(value = "学生类", description = "学生信息")
@Getter
@Setter
public class Student {

    @ApiModelProperty(value = "学号", example = "27393335556218",  required = true)
    private Long id;

    @ApiModelProperty(value = "年龄", example = "12")
    private Integer age;

    @ApiModelProperty(value = "姓名", example = "张三")
    private String name;

    @ApiModelProperty(value = "性别", example = "0: 男 / 1: 女")
    private Integer sex;
}
