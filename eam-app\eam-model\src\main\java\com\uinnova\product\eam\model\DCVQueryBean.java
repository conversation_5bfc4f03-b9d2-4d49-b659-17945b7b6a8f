package com.uinnova.product.eam.model;

import java.util.ArrayList;
import java.util.List;

public class DCVQueryBean {
	private int pageSize = 1;

	private int pageNum = 20;

	private String keyword = null;

	private Long domainId = 1L;

	private Long dcId = 0L;

	private List<DCVAttrBean> attrs = new ArrayList<DCVAttrBean>();

	private List<Long> tagIds = new ArrayList<Long>();

	private List<Long> classIds = new ArrayList<Long>();

	public int getPageSize() {
		return pageSize;
	}

	public void setPageSize(int pageSize) {
		this.pageSize = pageSize;
	}

	public int getPageNum() {
		return pageNum;
	}

	public void setPageNum(int pageNum) {
		this.pageNum = pageNum;
	}

	public String getKeyword() {
		return keyword;
	}

	public void setKeyword(String keyword) {
		this.keyword = keyword;
	}

	public Long getDomainId() {
		return domainId;
	}

	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}

	public Long getDcId() {
		return dcId;
	}

	public void setDcId(Long dcId) {
		this.dcId = dcId;
	}

	public List<DCVAttrBean> getAttrs() {
		return attrs;
	}

	public void setAttrs(List<DCVAttrBean> attrs) {
		this.attrs = attrs;
	}

	public List<Long> getTagIds() {
		return tagIds;
	}

	public void setTagIds(List<Long> tagIds) {
		this.tagIds = tagIds;
	}

	public List<Long> getClassIds() {
		return classIds;
	}

	public void setClassIds(List<Long> classIds) {
		this.classIds = classIds;
	}

}
