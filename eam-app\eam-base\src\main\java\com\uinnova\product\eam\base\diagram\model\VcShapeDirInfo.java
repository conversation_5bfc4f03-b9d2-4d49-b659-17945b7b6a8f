package com.uinnova.product.eam.base.diagram.model;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.io.Serializable;

@Data
public class VcShapeDirInfo implements Serializable {

	private static final long serialVersionUID = 1L;

	@Comment("文件夹信息")
	private VcShapeDir dir;
	
	@Comment("文件夹下图形数量")
	private Long imageCount;
	
	@Comment("是否默认显示1=是;0否")
	private Long isShow;

}
