package com.uinnova.product.eam.service.dm;

import com.uinnova.product.eam.model.dm.bean.AttrAndCiDto;
import com.uinnova.product.eam.model.enums.DataBaseType;
import com.uino.bean.cmdb.base.LibType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

/**
 * 数据建模sql处理相关接口
 * <AUTHOR>
 */
public interface DataModelSqlSvc {
    /**
     * 导出建表DDL
     * @param diagramId 视图id
     * @param libType 库
     * @param dataBase 数据库类型
     * @return 导出文件
     */
    ResponseEntity<byte[]> exportCreateTable(String diagramId, LibType libType, DataBaseType dataBase);

    /**
     * 导入ddl文件，解析为视图
     * @param file ddl文件
     * @param diagramId 视图id
     * @param dataBase 数据库类型
     * @return 实体及实体属性
     */
    AttrAndCiDto importDDL(MultipartFile file, String diagramId, DataBaseType dataBase);
}
