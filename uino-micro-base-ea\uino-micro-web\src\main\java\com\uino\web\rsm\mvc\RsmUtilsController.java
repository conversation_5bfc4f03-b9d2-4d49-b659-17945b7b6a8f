package com.uino.web.rsm.mvc;

import com.uino.init.api.ApiResult;
import com.uino.util.rsm.RsmUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.File;

@Api(value = "对象存储", tags = {"对象存储"})
@RestController
@RequestMapping(value = "/obs")
public class RsmUtilsController {

    @Autowired
    private RsmUtils rsmUtils;

    @ApiOperation("下载资源并更新到本地资源文件")
    @RequestMapping(value="/downloadRsmAndUpdateLocalRsm",method = RequestMethod.POST)
    public ApiResult<Boolean> downloadRsmAndUpdateLocalRsm(HttpServletRequest request, HttpServletResponse response, @RequestParam(name="objectKey") String objectKey){
        Boolean result = rsmUtils.downloadRsmAndUpdateLocalRsm(objectKey);
        return ApiResult.ok(this).data(result);
    }

    @ApiOperation("删除指定的资源")
    @RequestMapping(value="/deleteRsm",method = RequestMethod.POST)
    public ApiResult<Boolean> deleteRsm(HttpServletRequest request, HttpServletResponse response, @RequestParam(name="objectKey") String objectKey){
        Boolean result = rsmUtils.deleteRsm(objectKey);
        return ApiResult.ok(this).data(result);
    }

    @ApiOperation("删除指定文件资源")
    @RequestMapping(value="/deleteRsmByFile",method = RequestMethod.POST)
    public ApiResult<Boolean> deleteRsmByFile(HttpServletRequest request, HttpServletResponse response, @RequestParam(name="filePath") String filePath){
        File file = new File(filePath);
        Boolean result = rsmUtils.deleteRsmByFile(file);
        return ApiResult.ok(this).data(result);
    }

    @ApiOperation("删除指定文件夹下的资源")
    @RequestMapping(value="/deleteRsmByFolder",method = RequestMethod.POST)
    public ApiResult<Boolean> deleteRsmByFolder(HttpServletRequest request, HttpServletResponse response, @RequestParam(name="folderPath") String folderPath){
        File folder = new File(folderPath);
        Boolean result = rsmUtils.deleteRsmByFolder(folder);
        return ApiResult.ok(this).data(result);
    }

    @ApiOperation("上传指定文件到指定的objectKey")
    @RequestMapping(value="/uploadRsm",method = RequestMethod.POST)
    public ApiResult<Boolean> uploadRsm(HttpServletRequest request, HttpServletResponse response, @RequestParam(name="objectKey") String objectKey, @RequestParam(name="filePath") String filePath){
        File file = new File(filePath);
        Boolean result = rsmUtils.uploadRsm(objectKey,file);
        return ApiResult.ok(this).data(result);
    }

    @ApiOperation("上传指定文件资源到对象存储")
    @RequestMapping(value="/uploadRsmFromFile",method = RequestMethod.POST)
    public ApiResult<Boolean> uploadRsmFromFile(HttpServletRequest request, HttpServletResponse response, @RequestParam(name="filePath") String filePath){
        File file = new File(filePath);
        Boolean result = rsmUtils.uploadRsmFromFile(file);
        return ApiResult.ok(this).data(result);
    }

    @ApiOperation("上传指定文件夹下的所有资源到对象存储")
    @RequestMapping(value="/uploadRsmFromFolder",method = RequestMethod.POST)
    public ApiResult<Boolean> uploadRsmFromFolder(HttpServletRequest request, HttpServletResponse response, @RequestParam(name="folderPath") String folderPath){
        File folder = new File(folderPath);
        Boolean result = rsmUtils.uploadRsmFromFolder(folder);
        return ApiResult.ok(this).data(result);
    }
}
