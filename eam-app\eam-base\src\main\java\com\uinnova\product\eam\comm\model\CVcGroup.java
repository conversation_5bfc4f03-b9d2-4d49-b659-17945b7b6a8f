package com.uinnova.product.eam.comm.model;




import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;


@Comment("组表[VC_GROUP]")
public class CVcGroup implements Condition {
	private static final long serialVersionUID = 1L;


	@Comment("ID[ID] operate-Equal[=]")
	private Long id;


	@Comment("ID[ID] operate-In[in]")
	private Long[] ids;


	@Comment("ID[ID] operate-GTEqual[>=]")
	private Long startId;

	@Comment("ID[ID] operate-LTEqual[<=]")
	private Long endId;


	@Comment("组名称[GROUP_NAME] operate-Like[like]")
	private String groupName;


	@Comment("组名称[GROUP_NAME] operate-Equal[=]")
	private String groupNameEqual;


	@Comment("组名称[GROUP_NAME] operate-In[in]")
	private String[] groupNames;


	@Comment("组描述[GROUP_DESC] operate-Like[like]")
	private String groupDesc;


	@Comment("权限范围[AUTH_REGION] operate-Equal[=]    1=全部")
	private Integer authRegion;


	@Comment("权限范围[AUTH_REGION] operate-In[in]    1=全部")
	private Integer[] authRegions;


	@Comment("权限范围[AUTH_REGION] operate-GTEqual[>=]    1=全部")
	private Integer startAuthRegion;

	@Comment("权限范围[AUTH_REGION] operate-LTEqual[<=]    1=全部")
	private Integer endAuthRegion;


	@Comment("组图标[GROUP_IMAGE] operate-Like[like]")
	private String groupImage;


	@Comment("组图标[GROUP_IMAGE] operate-Equal[=]")
	private String groupImageEqual;


	@Comment("组图标[GROUP_IMAGE] operate-In[in]")
	private String[] groupImages;


	@Comment("所属域[DOMAIN_ID] operate-Equal[=]")
	private Long domainId;


	@Comment("所属域[DOMAIN_ID] operate-In[in]")
	private Long[] domainIds;


	@Comment("所属域[DOMAIN_ID] operate-GTEqual[>=]")
	private Long startDomainId;

	@Comment("所属域[DOMAIN_ID] operate-LTEqual[<=]")
	private Long endDomainId;


	@Comment("数据状态[DATA_STATUS] operate-Equal[=]    数据状态：1-正常 0-删除")
	private Integer dataStatus;


	@Comment("数据状态[DATA_STATUS] operate-In[in]    数据状态：1-正常 0-删除")
	private Integer[] dataStatuss;


	@Comment("数据状态[DATA_STATUS] operate-GTEqual[>=]    数据状态：1-正常 0-删除")
	private Integer startDataStatus;

	@Comment("数据状态[DATA_STATUS] operate-LTEqual[<=]    数据状态：1-正常 0-删除")
	private Integer endDataStatus;


	@Comment("创建人[CREATOR] operate-Like[like]")
	private String creator;


	@Comment("创建人[CREATOR] operate-Equal[=]")
	private String creatorEqual;


	@Comment("创建人[CREATOR] operate-In[in]")
	private String[] creators;


	@Comment("修改人[MODIFIER] operate-Like[like]")
	private String modifier;


	@Comment("修改人[MODIFIER] operate-Equal[=]")
	private String modifierEqual;


	@Comment("修改人[MODIFIER] operate-In[in]")
	private String[] modifiers;


	@Comment("创建时间[CREATE_TIME] operate-Equal[=]    yyyyMMddHHmmss")
	private Long createTime;


	@Comment("创建时间[CREATE_TIME] operate-In[in]    yyyyMMddHHmmss")
	private Long[] createTimes;


	@Comment("创建时间[CREATE_TIME] operate-GTEqual[>=]    yyyyMMddHHmmss")
	private Long startCreateTime;

	@Comment("创建时间[CREATE_TIME] operate-LTEqual[<=]    yyyyMMddHHmmss")
	private Long endCreateTime;


	@Comment("修改时间[MODIFY_TIME] operate-Equal[=]    yyyyMMddHHmmss")
	private Long modifyTime;


	@Comment("修改时间[MODIFY_TIME] operate-In[in]    yyyyMMddHHmmss")
	private Long[] modifyTimes;


	@Comment("修改时间[MODIFY_TIME] operate-GTEqual[>=]    yyyyMMddHHmmss")
	private Long startModifyTime;

	@Comment("修改时间[MODIFY_TIME] operate-LTEqual[<=]    yyyyMMddHHmmss")
	private Long endModifyTime;




	public Long getId() {
		return this.id;
	}
	public void setId(Long id) {
		this.id = id;
	}


	public Long[] getIds() {
		return this.ids;
	}
	public void setIds(Long[] ids) {
		this.ids = ids;
	}


	public Long getStartId() {
		return this.startId;
	}
	public void setStartId(Long startId) {
		this.startId = startId;
	}


	public Long getEndId() {
		return this.endId;
	}
	public void setEndId(Long endId) {
		this.endId = endId;
	}


	public String getGroupName() {
		return this.groupName;
	}
	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}


	public String getGroupNameEqual() {
		return this.groupNameEqual;
	}
	public void setGroupNameEqual(String groupNameEqual) {
		this.groupNameEqual = groupNameEqual;
	}


	public String[] getGroupNames() {
		return this.groupNames;
	}
	public void setGroupNames(String[] groupNames) {
		this.groupNames = groupNames;
	}


	public String getGroupDesc() {
		return this.groupDesc;
	}
	public void setGroupDesc(String groupDesc) {
		this.groupDesc = groupDesc;
	}


	public Integer getAuthRegion() {
		return this.authRegion;
	}
	public void setAuthRegion(Integer authRegion) {
		this.authRegion = authRegion;
	}


	public Integer[] getAuthRegions() {
		return this.authRegions;
	}
	public void setAuthRegions(Integer[] authRegions) {
		this.authRegions = authRegions;
	}


	public Integer getStartAuthRegion() {
		return this.startAuthRegion;
	}
	public void setStartAuthRegion(Integer startAuthRegion) {
		this.startAuthRegion = startAuthRegion;
	}


	public Integer getEndAuthRegion() {
		return this.endAuthRegion;
	}
	public void setEndAuthRegion(Integer endAuthRegion) {
		this.endAuthRegion = endAuthRegion;
	}


	public String getGroupImage() {
		return this.groupImage;
	}
	public void setGroupImage(String groupImage) {
		this.groupImage = groupImage;
	}


	public String getGroupImageEqual() {
		return this.groupImageEqual;
	}
	public void setGroupImageEqual(String groupImageEqual) {
		this.groupImageEqual = groupImageEqual;
	}


	public String[] getGroupImages() {
		return this.groupImages;
	}
	public void setGroupImages(String[] groupImages) {
		this.groupImages = groupImages;
	}


	public Long getDomainId() {
		return this.domainId;
	}
	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}


	public Long[] getDomainIds() {
		return this.domainIds;
	}
	public void setDomainIds(Long[] domainIds) {
		this.domainIds = domainIds;
	}


	public Long getStartDomainId() {
		return this.startDomainId;
	}
	public void setStartDomainId(Long startDomainId) {
		this.startDomainId = startDomainId;
	}


	public Long getEndDomainId() {
		return this.endDomainId;
	}
	public void setEndDomainId(Long endDomainId) {
		this.endDomainId = endDomainId;
	}


	public Integer getDataStatus() {
		return this.dataStatus;
	}
	public void setDataStatus(Integer dataStatus) {
		this.dataStatus = dataStatus;
	}


	public Integer[] getDataStatuss() {
		return this.dataStatuss;
	}
	public void setDataStatuss(Integer[] dataStatuss) {
		this.dataStatuss = dataStatuss;
	}


	public Integer getStartDataStatus() {
		return this.startDataStatus;
	}
	public void setStartDataStatus(Integer startDataStatus) {
		this.startDataStatus = startDataStatus;
	}


	public Integer getEndDataStatus() {
		return this.endDataStatus;
	}
	public void setEndDataStatus(Integer endDataStatus) {
		this.endDataStatus = endDataStatus;
	}


	public String getCreator() {
		return this.creator;
	}
	public void setCreator(String creator) {
		this.creator = creator;
	}


	public String getCreatorEqual() {
		return this.creatorEqual;
	}
	public void setCreatorEqual(String creatorEqual) {
		this.creatorEqual = creatorEqual;
	}


	public String[] getCreators() {
		return this.creators;
	}
	public void setCreators(String[] creators) {
		this.creators = creators;
	}


	public String getModifier() {
		return this.modifier;
	}
	public void setModifier(String modifier) {
		this.modifier = modifier;
	}


	public String getModifierEqual() {
		return this.modifierEqual;
	}
	public void setModifierEqual(String modifierEqual) {
		this.modifierEqual = modifierEqual;
	}


	public String[] getModifiers() {
		return this.modifiers;
	}
	public void setModifiers(String[] modifiers) {
		this.modifiers = modifiers;
	}


	public Long getCreateTime() {
		return this.createTime;
	}
	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}


	public Long[] getCreateTimes() {
		return this.createTimes;
	}
	public void setCreateTimes(Long[] createTimes) {
		this.createTimes = createTimes;
	}


	public Long getStartCreateTime() {
		return this.startCreateTime;
	}
	public void setStartCreateTime(Long startCreateTime) {
		this.startCreateTime = startCreateTime;
	}


	public Long getEndCreateTime() {
		return this.endCreateTime;
	}
	public void setEndCreateTime(Long endCreateTime) {
		this.endCreateTime = endCreateTime;
	}


	public Long getModifyTime() {
		return this.modifyTime;
	}
	public void setModifyTime(Long modifyTime) {
		this.modifyTime = modifyTime;
	}


	public Long[] getModifyTimes() {
		return this.modifyTimes;
	}
	public void setModifyTimes(Long[] modifyTimes) {
		this.modifyTimes = modifyTimes;
	}


	public Long getStartModifyTime() {
		return this.startModifyTime;
	}
	public void setStartModifyTime(Long startModifyTime) {
		this.startModifyTime = startModifyTime;
	}


	public Long getEndModifyTime() {
		return this.endModifyTime;
	}
	public void setEndModifyTime(Long endModifyTime) {
		this.endModifyTime = endModifyTime;
	}


}


