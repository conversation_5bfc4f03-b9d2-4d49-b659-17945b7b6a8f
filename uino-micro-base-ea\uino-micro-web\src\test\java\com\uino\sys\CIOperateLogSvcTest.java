package com.uino.sys;

import static org.junit.Assert.assertEquals;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import jakarta.servlet.http.Cookie;

import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.binary.jdbc.Page;
import com.uino.dao.sys.ESCIOperateLogSvc;
import com.uino.service.sys.microservice.impl.CIOperateLogSvc;
import com.uino.bean.sys.base.ESCIOperateLog;
import com.uino.bean.sys.query.ESCIOperateLogSearchBean;

@RunWith(SpringJUnit4ClassRunner.class)
public class CIOperateLogSvcTest {

    @InjectMocks
    private CIOperateLogSvc svc;

    private ESCIOperateLogSvc logSvc;

    @BeforeClass
    public static void setUpBeforeClass() throws Exception {
        MockHttpServletRequest request = new MockHttpServletRequest();
        Cookie cookie = new Cookie("token", "ca14ed042fe7912426b484f6ea5c754b4fb51ae4a72037e9127da51743e0d1f9650e6e78ed4466ac970acb6a0b2f46fd26958c4ab2d115cc71b34ca8a02db24c");
        request.setCookies(cookie);
        ServletRequestAttributes attributes = new ServletRequestAttributes(request);
        RequestContextHolder.setRequestAttributes(attributes);
    }

    @Before
    public void setUp() throws Exception {
        logSvc = Mockito.mock(ESCIOperateLogSvc.class);
        ReflectionTestUtils.setField(svc, "logSvc", logSvc);

    }

    @Test
    public void testGetCIOperateLogPageByCdt() {
        Mockito.when(logSvc.getSortListByQuery(Mockito.anyInt(), Mockito.anyInt(), Mockito.any(), Mockito.anyString(), Mockito.anyBoolean())).thenReturn(new Page<>(1, 30, 2, 1, new ArrayList<>()));

        ESCIOperateLogSearchBean bean = new ESCIOperateLogSearchBean();
        bean.setPageNum(1);
        bean.setPageSize(30);
        Page<ESCIOperateLog> rs = svc.getCIOperateLogPageByCdt(bean);
        assertEquals(2, rs.getTotalRows());
    }

    @Test
    public void testSaveOrUpdate() {
        Mockito.when(logSvc.saveOrUpdate(Mockito.any(ESCIOperateLog.class))).thenReturn(123L);

        Map<String, String> oldAttrs = new HashMap<>();
        Map<String, String> newAttrs = new HashMap<>();
        newAttrs.put("new", "value");
        ESCIOperateLog ciLog = ESCIOperateLog.builder().ciClassName("test-test").ciCode("ciCode").ciId(123L).ciPrimaryKey("ciPrimaryKey").domainId(1L).dynamic(2).operator("operator")
            .oldAttrs(oldAttrs).newAttrs(newAttrs)
            .build();
        Long rs = svc.saveOrUpdate(ciLog);
        assertEquals(123L, rs.longValue());
    }

    @Test
    public void testSaveOrUpdateBatch() {
        Mockito.when(logSvc.saveOrUpdateBatch(Mockito.anyList())).thenReturn(1);

        List<ESCIOperateLog> logs = new ArrayList<>();
        ESCIOperateLog ciLog = ESCIOperateLog.builder().ciClassName("test-test").ciCode("ciCode").ciId(123L).ciPrimaryKey("ciPrimaryKey").domainId(1L).dynamic(2).operator("operator")
            .oldAttrs(new HashMap<>()).newAttrs(new HashMap<>()).build();
        logs.add(ciLog);

        Integer rs = svc.saveOrUpdateBatch(logs);
        assertEquals(1, rs.intValue());
    }

    @Test
    public void testClearCIOperateLogByDuration() {
        Mockito.when(logSvc.deleteByQuery(Mockito.any(), Mockito.anyBoolean())).thenReturn(1);

        Integer rs = svc.clearCIOperateLogByDuration(1);
        assertEquals(1, rs.intValue());
    }

}
