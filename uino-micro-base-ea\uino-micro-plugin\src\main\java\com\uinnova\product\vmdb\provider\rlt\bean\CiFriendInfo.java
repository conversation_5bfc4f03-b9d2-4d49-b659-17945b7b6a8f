package com.uinnova.product.vmdb.provider.rlt.bean;

import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;

import java.io.Serializable;
import java.util.List;

public class CiFriendInfo implements Serializable {

	private static final long serialVersionUID = 1L;
	private List<CcCiClassInfo> ciClassInfos;

	private List<CiNode> ciNodes;

	private List<CiRltLine> ciRltLines;

	private List<CcCiInfo> ciInfos;

	private List<CcCiRltInfo> ciRlts;

	public List<CcCiClassInfo> getCiClassInfos() {
		return ciClassInfos;
	}

	public void setCiClassInfos(List<CcCiClassInfo> ciClassInfos) {
		this.ciClassInfos = ciClassInfos;
	}

	public List<CcCiInfo> getCiInfos() {
		return ciInfos;
	}

	public void setCiInfos(List<CcCiInfo> ciInfos) {
		this.ciInfos = ciInfos;
	}

	public List<CcCiRltInfo> getCiRlts() {
		return ciRlts;
	}

	public void setCiRlts(List<CcCiRltInfo> ciRlts) {
		this.ciRlts = ciRlts;
	}

	public List<CiNode> getCiNodes() {
		return ciNodes;
	}

	public void setCiNodes(List<CiNode> ciNodes) {
		this.ciNodes = ciNodes;
	}

	public List<CiRltLine> getCiRltLines() {
		return ciRltLines;
	}

	public void setCiRltLines(List<CiRltLine> ciRltLines) {
		this.ciRltLines = ciRltLines;
	}

}
