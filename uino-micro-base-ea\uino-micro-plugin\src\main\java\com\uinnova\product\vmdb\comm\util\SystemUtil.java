package com.uinnova.product.vmdb.comm.util;

import com.binary.core.os.OperateSystem;
import com.binary.core.os.OperateSystemFactory;
import com.binary.core.os.OperateSystemType;
import com.binary.core.util.BinaryUtils;
import com.binary.framework.Local;
import com.binary.framework.bean.User;
import com.binary.framework.exception.ServiceException;
import com.github.pagehelper.dialect.helper.HsqldbDialect;
import com.github.pagehelper.page.PageAutoDialect;
import com.uinnova.product.vmdb.comm.license.License;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import jakarta.servlet.http.HttpServletRequest;
import java.io.File;
import java.net.InetAddress;
import java.net.InterfaceAddress;
import java.net.NetworkInterface;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;
import java.util.regex.Pattern;

/**
 *
 * <AUTHOR>
 *
 */
public abstract class SystemUtil {
    private static final Logger logger = LoggerFactory.getLogger(SystemUtil.class);

    public static final Pattern IP_REGEX = Pattern.compile("[0-9]{1,3}[.][0-9]{1,3}[.][0-9]{1,3}[.][0-9]{1,3}");

    static {
        logger.info("init DB Dialect Mapping");
        logger.info("add kingbase8 mapping to " + HsqldbDialect.class);
        PageAutoDialect.registerDialectAlias("kingbase8", HsqldbDialect.class);
        logger.info("add KINGBASE8 mapping to " + HsqldbDialect.class);
        PageAutoDialect.registerDialectAlias("KINGBASE8", HsqldbDialect.class);
    }

    /**
     * 获取登录用户, 如果没有登录则会抛出#ServiceException
     *
     * @return
     */
    public static User getLoginUser() {
        User user = Local.getUser();
        if (user == null) {
            throw new ServiceException(" not found login user! ");
        }
        return user;
    }

    /**
     * 获取用户域ID
     *
     * @return
     */
    public static Long getUserDomainId() {
        return getLoginUser().getDomainId();
    }

    /**
     * 获取当前用户token
     *
     * @param request
     * @return
     */
    public static String getToken(HttpServletRequest request) {
        return "";
    }

    public static boolean isLicenseValid(License license) {
        // 分钟
        long onlineTime = license.getOnlineTime();
        return license.getValid().booleanValue() && (onlineTime / 60 / 24 < license.getLimitUseTime());
    }

//    /**
//     * 获取Cookie中的值
//     *
//     * @param request
//     * @param name
//     * @return
//     */
//    public static String getCookie(HttpServletRequest request, String name) {
//        MessageUtil.checkEmpty(name, "name");
//        name = name.trim();
//
//        Cookie[] cs = request.getCookies();
//        String value = null;
//        if (cs != null && cs.length > 0) {
//            for (int i = 0; i < cs.length; i++) {
//                Cookie c = cs[i];
//                if (name.equals(c.getName())) {
//                    value = c.getValue();
//                    if (value != null) {
//                        value = value.trim();
//                    }
//                    break;
//                }
//            }
//        }
//        return value;
//    }

    /**
     * 获取上一页路径
     *
     * @param request
     * @return
     */
    public static String getBeforeUrl(HttpServletRequest request) {
        StringBuffer beforeUrl = request.getRequestURL();
        if (beforeUrl == null || beforeUrl.length() == 0) {
            return null;
        }

        String qs = request.getQueryString();
        if (!BinaryUtils.isEmpty(qs)) {
            beforeUrl.append("?").append(qs);
        }
        return beforeUrl.toString();
    }

    /**
     * 判断是否是有效的IP
     *
     * @param ip
     * @return
     */
    public static boolean isEffectiveIp(String ip) {
        if (!IP_REGEX.matcher(ip).matches()) {
            return false;
        }
        String ipStr = "127.0.0.1";
        String ipnStr = "0.0.0.0";
        if (ipStr.equals(ip) || ip.equals(ipnStr)) {
            return false;
        }
        char d = '.';
        if (Integer.parseInt(ip.substring(0, ip.indexOf(d))) == 0) {
            return false;
        }
        return true;
    }

    /**
     * 获取服务器所有网卡IP
     *
     * @return
     */
    public static String[] getIps() {
        List<String> ls = new ArrayList<String>();
        try {
            Enumeration<NetworkInterface> e = NetworkInterface.getNetworkInterfaces();
            while (e.hasMoreElements()) {
                NetworkInterface net = e.nextElement();
                List<InterfaceAddress> interfaceAddrs = net.getInterfaceAddresses();
                if (BinaryUtils.isEmpty(interfaceAddrs)) {
                    continue;
                }

                for (int i = 0; i < interfaceAddrs.size(); i++) {
                    InterfaceAddress interfaceAddr = interfaceAddrs.get(i);
                    if (interfaceAddr == null) {
                        continue;
                    }

                    InetAddress addr = interfaceAddr.getAddress();
                    if (addr == null) {
                        continue;
                    }

                    String host = addr.getHostAddress();

                    if (host != null && isEffectiveIp(host)) {
                        ls.add(host);
                    }
                }
            }
        } catch (Exception e) {
            throw BinaryUtils.transException(e, ServiceException.class);
        }

        return ls.toArray(new String[0]);
    }

    /**
     * 加载动态链接库 格式必须是：libname-1.0.dll、libname-1.0.so
     */
    public static void loadLibrary(String libaryName) {
        OperateSystem os = OperateSystemFactory.getOperateSystem();
        OperateSystemType ostype = os.getType();

        String simpleName = libaryName.substring(0, libaryName.lastIndexOf('.'));
        String version = simpleName.substring(simpleName.lastIndexOf('-') + 1);

        try {
            try {
                logger.info(" load library '" + simpleName + "' ... ");
                System.loadLibrary(simpleName);
            } catch (UnsatisfiedLinkError e) {
                logger.info(" load library '" + simpleName.substring(3) + "' ... ");
                System.loadLibrary(simpleName.substring(3));
            }

        } catch (UnsatisfiedLinkError e) {
            String classpath = System.getProperty("java.class.path");
            char space = ostype == OperateSystemType.WINDOWS ? ';' : ':';

            int idx = classpath.indexOf("binary-core-1.0.0-");
            String libaryPath = null;
            logger.info(" load library by classpath idx[" + idx + "] ... ");

            if (idx > 0) {
                String path = classpath.substring(0, idx - 1);
                path = path.substring(path.lastIndexOf(space) + 1);

                String dir = path.substring(path.lastIndexOf(File.separatorChar) + 1);
                logger.info(" load library by directory [" + dir + "]... ");

                String dirStd = "1.0.0-SNAPSHOT";
                String libDir = "lib";
                String binDir = "bin";
                if (dir.equals(dirStd)) {
                    String libaryDir = path.substring(0, path.lastIndexOf("com"));
                    File libaryFile = new File(libaryDir, "com/uinnova/product/vmdb/" + simpleName.substring(0, simpleName.lastIndexOf('-')) + "/" + version + "/" + libaryName);
                    logger.info(" load library by maven repository file '" + libaryFile.getAbsolutePath() + "' - [" + libaryFile.isFile() + "] ... ");
                    if (libaryFile.isFile()) {
                        libaryPath = libaryFile.getAbsolutePath();
                    }
                } else if (dir.equals(libDir)) {
                    String currPath = new File(".").getAbsolutePath();
                    currPath = currPath.substring(0, currPath.length() - 2);
                    if (currPath.endsWith(File.separator + binDir)) {
                        currPath = currPath.substring(0, currPath.length() - 3);
                    }
                    File libaryFile = new File(currPath, "lib/" + libaryName);
                    logger.info(" load library by lib file '" + libaryFile.getAbsolutePath() + "' - [" + libaryFile.isFile() + "] ... ");
                    if (libaryFile.isFile()) {
                        libaryPath = libaryFile.getAbsolutePath();
                    }
                }
            }

            if (BinaryUtils.isEmpty(libaryPath)) {
                throw new ServiceException(" not found libary '" + libaryName + "'! ");
            }

            System.load(libaryPath);
        }
    }
}
