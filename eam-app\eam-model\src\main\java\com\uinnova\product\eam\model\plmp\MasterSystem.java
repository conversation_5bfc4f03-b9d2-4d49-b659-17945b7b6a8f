package com.uinnova.product.eam.model.plmp;

public class MasterSystem {

    private String systNam;

    private Code systTyp;

    private String systFuncDesc;

    private Code systImpnLevl;

    private Code systSafePrteLevl;

    public String getSystNam() {
        return systNam;
    }

    public void setSystNam(String systNam) {
        this.systNam = systNam;
    }



    public String getSystFuncDesc() {
        return systFuncDesc;
    }

    public void setSystFuncDesc(String systFuncDesc) {
        this.systFuncDesc = systFuncDesc;
    }

    public Code getSystTyp() {
        return systTyp;
    }

    public void setSystTyp(Code systTyp) {
        this.systTyp = systTyp;
    }

    public Code getSystImpnLevl() {
        return systImpnLevl;
    }

    public void setSystImpnLevl(Code systImpnLevl) {
        this.systImpnLevl = systImpnLevl;
    }

    public Code getSystSafePrteLevl() {
        return systSafePrteLevl;
    }

    public void setSystSafePrteLevl(Code systSafePrteLevl) {
        this.systSafePrteLevl = systSafePrteLevl;
    }
}
