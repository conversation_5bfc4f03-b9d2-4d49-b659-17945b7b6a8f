package com.uinnova.product.eam.web.eam.mvc.system;

import com.binary.framework.web.RemoteResult;
import com.binary.jdbc.Page;
import com.uinnova.product.eam.model.permissionV2.*;
import com.uinnova.product.eam.service.UserRoleRltV2Svc;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/permission/v2")
public class UserRoleRltV2Mvc {

    @Autowired
    private UserRoleRltV2Svc userRoleRltV2Svc;

    @PostMapping("/user/page")
    public RemoteResult userPage(@RequestBody UserQueryReq req) {
        Page<UserQueryRes> result = userRoleRltV2Svc.userPage(req);
        return new RemoteResult(result);
    }

    @PostMapping("/user/role/rlt")
    public RemoteResult userRoleRlt(@RequestBody UserRoleRltReq req) {
        Page<UserRoleRltRes> result = userRoleRltV2Svc.userRoleRlt(req);
        return new RemoteResult(result);
    }

    @PostMapping("/user/role/rlt/op")
    public RemoteResult userRoleRltOp(@RequestBody UserRoleOpReq req) {
        userRoleRltV2Svc.userRoleRltOp(req);
        return new RemoteResult("success");
    }
}
