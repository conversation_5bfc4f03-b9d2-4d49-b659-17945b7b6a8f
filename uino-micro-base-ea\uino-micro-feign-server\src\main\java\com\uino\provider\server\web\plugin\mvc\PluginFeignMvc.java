package com.uino.provider.server.web.plugin.mvc;

import com.binary.core.io.Resource;
import com.binary.jdbc.Page;
import com.uino.bean.plugin.base.ESPluginInfo;
import com.uino.bean.plugin.query.CPluginInfo;
import com.uino.provider.feign.plugin.PluginFeign;
import com.uino.service.plugin.microservice.PluginManageSvc;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import java.util.List;

@RestController
@RequestMapping("feign/plugin")
public class PluginFeignMvc implements PluginFeign {

    @Autowired
    private PluginManageSvc svc;

    @Override
    public List<String> getServiceList() {
        return svc.getServiceList();
    }

    @Override
    public Long saveOrUpdate(ESPluginInfo pluginInfo) {
        return svc.saveOrUpdate(pluginInfo);
    }


    @Override
    public String uploadPlugin(Long id, MultipartFile file) {
        return svc.uploadPlugin(id,file);
    }

    @Override
    public boolean syncPlugin(MultipartFile file) {
        return svc.syncPlugin(file);
    }

    @Override
    public Resource downloadPlugin(Long id) {
        return svc.downloadPlugin(id);
    }

    @Override
    public Page<ESPluginInfo> queryList(int pageNum, int pageSize, CPluginInfo cPluginInfo) {
        return svc.queryList(pageNum,pageSize,cPluginInfo);
    }

    @Override
    public boolean loadOrUnloadPlugin(Long id) {
        return svc.loadOrUnloadPlugin(id);
    }

    @Override
    public boolean deletePlugin(Long id) {
        return svc.deletePlugin(id);
    }

    @Override
    public List<String> queryListByOpenServer(String serverName) {
        return svc.queryListByOpenServer(serverName);
    }
}
