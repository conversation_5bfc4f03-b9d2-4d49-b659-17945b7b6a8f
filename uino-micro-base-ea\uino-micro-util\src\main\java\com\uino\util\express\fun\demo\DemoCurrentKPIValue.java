package com.uino.util.express.fun.demo;


import com.googlecode.aviator.runtime.function.FunctionUtils;
import com.googlecode.aviator.runtime.type.AviatorDouble;
import com.googlecode.aviator.runtime.type.AviatorObject;
import com.uino.util.express.annotation.UinoAviatorFunction;
import com.uino.util.express.annotation.UinoAviatorParam;
import com.uino.util.express.common.FunctionTagConst;
import com.uino.util.express.common.UinoAbstractAviatorFunction;

import java.util.List;
import java.util.Map;

@UinoAviatorFunction(name = "inner.demo.CurrentKPIValue", desc = "当前KPI值计算demo", returnType = List.class, tags = FunctionTagConst.DEMO)
public class DemoCurrentKPIValue extends UinoAbstractAviatorFunction {

    // 可以使用依赖注入获取svc查询性能数据
//    @Autowired
//    IUinoPerformanceSvc svc;

//    @Override
//    public String getName() {
//        return "inner.demo.CurrentKPIValue";
//    }

    @Override
    public AviatorObject call(final Map<String, Object> env, @UinoAviatorParam(desc = "分类名", type = String.class) AviatorObject className,
                              @UinoAviatorParam(desc = "CI业务主键", type = String.class) AviatorObject ciPk,
                              @UinoAviatorParam(desc = "感知服务", type = String.class) AviatorObject kpiName) {
        String strclassName = FunctionUtils.getStringValue(className, env);
        String strciPk = FunctionUtils.getStringValue(ciPk, env);
        String strkpiName = FunctionUtils.getStringValue(kpiName, env);
        System.out.println("className:" + strclassName + ",ciPk:" + strciPk + ",kpiName:" + strkpiName);

        return new AviatorDouble(Math.random() * 100.0);
    }
}
