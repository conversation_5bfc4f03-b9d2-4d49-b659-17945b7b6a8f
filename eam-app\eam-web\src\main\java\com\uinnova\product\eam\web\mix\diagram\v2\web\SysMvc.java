package com.uinnova.product.eam.web.mix.diagram.v2.web;

import com.binary.framework.util.ControllerUtils;
import com.binary.framework.web.RemoteResult;
import com.binary.jdbc.Page;
import com.uinnova.product.eam.base.diagram.mix.model.TingJsDiagramCdt;
import com.uinnova.product.eam.base.diagram.model.ESUploadManage;
import com.uinnova.product.eam.web.mix.diagram.v2.peer.SysPeer;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uinnova.product.vmdb.comm.model.image.CcImage;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

@RestController
@RequestMapping("/eam/word")
public class SysMvc {

    @Resource
    private SysPeer sysPeer;

    @RequestMapping("/uploadFile")
    @ModDesc(desc = "上传文件", pDesc = "待上传文件", rDesc = "文件地址", rType = String.class)
    public void uploadFile(@RequestParam(name = "file") MultipartFile file, HttpServletRequest request,
                           HttpServletResponse response) {
//        file.getSize() 50MB=52428800字节
        String path = sysPeer.uploadFile(file);
        ControllerUtils.returnJson(request, response, path);
    }

    @RequestMapping("/upload")
    @ModDesc(desc = "上传文件", pDesc = "待上传文件", rDesc = "文件地址", rType = String.class)
    public void upload(@RequestParam(name = "file") MultipartFile file, HttpServletRequest request,
                       HttpServletResponse response) {
        ESUploadManage model = sysPeer.upload(file);
        ControllerUtils.returnJson(request, response, model);
    }

    @RequestMapping("/queryImages")
    @ModDesc(desc = "工作台-查询用户上传图片列表",
            pDesc = "查询参数",
            pType = TingJsDiagramCdt.class,
            rDesc = "查询用户上传图片列表",
            rType = Page.class)
    public RemoteResult queryImages(@RequestBody TingJsDiagramCdt cdt) {
        String orders = cdt.getOrders();
        Page<ESUploadManage> result = sysPeer.queryData(cdt.getPageNum(), cdt.getPageSize(), orders);
        return new RemoteResult(result);
    }

    @PostMapping({"/deleteImage"})
    @ModDesc(
            desc = "删除图片",
            pDesc = "要删除的图片信息",
            pType = CcImage.class,
            rDesc = "根据ID删除图片",
            rType = Boolean.class
    )
    public void deleteImage(@RequestBody CcImage image, HttpServletRequest request, HttpServletResponse response) {
        ControllerUtils.returnJson(request, response, sysPeer.deleteImage(image));
    }

}
