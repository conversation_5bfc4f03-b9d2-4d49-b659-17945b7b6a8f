package com.uinnova.product.eam.model;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/3/4
 */
@Data
public class EamArtifactCiVo {

    @Comment("分类id")
    private String id;

    @Comment("分类名称")
    private String name;

    @Comment("分类标识")
    private String classCode;

    @Comment("是否启用")
    private Boolean viewFlag;

    @Comment("显示名称")
    private String viewName;

    @Comment("制品配置图例")
    private String imgFullName;

    @Comment("架构元素默认图例")
    private String shape;
    private Integer orderNo;
    private String icon;
    private Integer viewNumber;
    private String type;
    private Boolean isChecked;
    private String relation;
    private String unique;
    private Boolean assetsFlag;
    private String viewFullName;
    private String viewIcon;

    @Comment("过滤条件")
    private String filter;

    private Integer count;
    private String root;
    private boolean exist;
    private boolean isSubClass;
}
