package com.uinnova.product.eam.model.plmp;

import java.util.List;

public class BascInfo {

    private String ssysNam;
    private String ssysEngFullNam;
    private Code ssysTyp;
    private Code ssysImpnLevl;
    private String ssysFuncDesc;
    private Code ssysDvlpOrgn;
    private String ssysFrstRegrTime;
    private String ssysInfoUpdtTime;
    private String ssysSrveTime;
    private Code ssysSafePrteLevl;
    private String ssysUseDate;
    private List<String> ssysBsinAdmtDept;
    private String ssysBlngSecdDept;
    private String ssysDvlpRespPesn;
    private String ssysBlngTeam;
    private String ifNeedSafeTest;

    public String getSsysNam() {
        return ssysNam;
    }

    public void setSsysNam(String ssysNam) {
        this.ssysNam = ssysNam;
    }

    public String getSsysEngFullNam() {
        return ssysEngFullNam;
    }

    public void setSsysEngFullNam(String ssysEngFullNam) {
        this.ssysEngFullNam = ssysEngFullNam;
    }

    public Code getSsysTyp() {
        return ssysTyp;
    }

    public void setSsysTyp(Code ssysTyp) {
        this.ssysTyp = ssysTyp;
    }

    public Code getSsysImpnLevl() {
        return ssysImpnLevl;
    }

    public void setSsysImpnLevl(Code ssysImpnLevl) {
        this.ssysImpnLevl = ssysImpnLevl;
    }

    public String getSsysFuncDesc() {
        return ssysFuncDesc;
    }

    public void setSsysFuncDesc(String ssysFuncDesc) {
        this.ssysFuncDesc = ssysFuncDesc;
    }

    public Code getSsysDvlpOrgn() {
        return ssysDvlpOrgn;
    }

    public void setSsysDvlpOrgn(Code ssysDvlpOrgn) {
        this.ssysDvlpOrgn = ssysDvlpOrgn;
    }

    public String getSsysFrstRegrTime() {
        return ssysFrstRegrTime;
    }

    public void setSsysFrstRegrTime(String ssysFrstRegrTime) {
        this.ssysFrstRegrTime = ssysFrstRegrTime;
    }

    public String getSsysInfoUpdtTime() {
        return ssysInfoUpdtTime;
    }

    public void setSsysInfoUpdtTime(String ssysInfoUpdtTime) {
        this.ssysInfoUpdtTime = ssysInfoUpdtTime;
    }

    public String getSsysSrveTime() {
        return ssysSrveTime;
    }

    public void setSsysSrveTime(String ssysSrveTime) {
        this.ssysSrveTime = ssysSrveTime;
    }

    public Code getSsysSafePrteLevl() {
        return ssysSafePrteLevl;
    }

    public void setSsysSafePrteLevl(Code ssysSafePrteLevl) {
        this.ssysSafePrteLevl = ssysSafePrteLevl;
    }

    public String getSsysUseDate() {
        return ssysUseDate;
    }

    public void setSsysUseDate(String ssysUseDate) {
        this.ssysUseDate = ssysUseDate;
    }

    public List<String> getSsysBsinAdmtDept() {
        return ssysBsinAdmtDept;
    }

    public void setSsysBsinAdmtDept(List<String> ssysBsinAdmtDept) {
        this.ssysBsinAdmtDept = ssysBsinAdmtDept;
    }

    public String getSsysBlngSecdDept() {
        return ssysBlngSecdDept;
    }

    public void setSsysBlngSecdDept(String ssysBlngSecdDept) {
        this.ssysBlngSecdDept = ssysBlngSecdDept;
    }

    public String getSsysDvlpRespPesn() {
        return ssysDvlpRespPesn;
    }

    public void setSsysDvlpRespPesn(String ssysDvlpRespPesn) {
        this.ssysDvlpRespPesn = ssysDvlpRespPesn;
    }

    public String getSsysBlngTeam() {
        return ssysBlngTeam;
    }

    public void setSsysBlngTeam(String ssysBlngTeam) {
        this.ssysBlngTeam = ssysBlngTeam;
    }

    public String getIfNeedSafeTest() {
        return ifNeedSafeTest;
    }

    public void setIfNeedSafeTest(String ifNeedSafeTest) {
        this.ifNeedSafeTest = ifNeedSafeTest;
    }
}
