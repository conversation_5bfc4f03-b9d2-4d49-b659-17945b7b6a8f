package com.uino.util.lock.zk.config;

import com.uino.util.lock.condition.EnableZkCondition;
import lombok.extern.slf4j.Slf4j;
import org.apache.curator.RetryPolicy;
import org.apache.curator.framework.CuratorFramework;
import org.apache.curator.framework.CuratorFrameworkFactory;
import org.apache.curator.retry.ExponentialBackoffRetry;
import org.apache.curator.utils.CloseableUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Configuration;

import jakarta.annotation.PreDestroy;

/**
 * @Description zk curator配置类
 * <AUTHOR>
 * @Date 2021/7/14
 **/
@Slf4j
@Configuration
@Conditional(EnableZkCondition.class)
public class ZkConfiguration {

    private static CuratorFramework client;

    /**
     * ZooKeeper 服务地址, 单机格式为:(127.0.0.1:2181)
     * <br>
     * 集群格式为:(127.0.0.1:2181,127.0.0.1:2182,127.0.0.1:2183)
     */
    @Value("${zookeeper.server}")
    private String zookeeperServer;

    /**
     * 初始休眠时间
     */
    @Value("${zookeeper.curator.baseSleepTimeMs:1000}")
    private int baseSleepTimeMs;

    /**
     * 最大重试次数
     */
    @Value("${zookeeper.curator.maxRetries:3}")
    private int maxRetries;

    /**
     * session 超时时间
     */
    @Value("${zookeeper.curator.sessionTimeoutMs:60000}")
    private int sessionTimeoutMs;

    /**
     * 链接超时时间
     */
    @Value("${zookeeper.curator.connectionTimeoutMs:15000}")
    private int connectionTimeoutMs;


    /**
     * 初始化资源
     *
     * @return {@link CuratorFramework}
     */
    @Bean
    public CuratorFramework init() {
        // Curator 客户端重试策略
        RetryPolicy retry = new ExponentialBackoffRetry(baseSleepTimeMs, maxRetries);
        // 创建Curator客户端对象
        client = CuratorFrameworkFactory.newClient(zookeeperServer, sessionTimeoutMs, connectionTimeoutMs, retry);
        // 创建会话
        client.start();
        log.debug("CuratorFramework init success!");
        log.debug("zookeeper.server:" + zookeeperServer);
        return client;
    }

    /**
     * 释放资源
     */
    @PreDestroy
    public void close() {
        CloseableUtils.closeQuietly(client);
    }
}
