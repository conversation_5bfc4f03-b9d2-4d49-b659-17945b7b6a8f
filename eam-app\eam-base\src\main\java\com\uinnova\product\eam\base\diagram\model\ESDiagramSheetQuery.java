package com.uinnova.product.eam.base.diagram.model;

import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

/**
 * @Classname
 * @Description 视图sheet实体类
 * <AUTHOR>
 * @Date 2021-06-03-16:31
 */
@Data
public class ESDiagramSheetQuery implements EntityBean {

    private Long id;

    private Long[] ids;

    private String sheetName;

    private Long diagramId;

    private Boolean active;

    private String sheetIdEqual;

    private String[] sheetIds;

    private Long[] diagramIds;

    private Long createTime;

    private Long modifyTime;

    @Comment("转换老视图标识，0-老视图")
    private Integer transFlag;

    private String reserved1;

    private String reserved2;

    private String reserved3;
}
