package com.uinnova.product.eam.comm.bean;

import java.util.Objects;

public class ResolutionDoc {

    private Long id;
    private Long resolutionId;
    private String name;
    private String realPath;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getResolutionId() {
        return resolutionId;
    }

    public void setResolutionId(Long resolutionId) {
        this.resolutionId = resolutionId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getRealPath() {
        return realPath;
    }

    public void setRealPath(String realPath) {
        this.realPath = realPath;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof ResolutionDoc)) return false;
        ResolutionDoc that = (ResolutionDoc) o;
        return Objects.equals(id, that.id) &&
                Objects.equals(resolutionId, that.resolutionId) &&
                Objects.equals(name, that.name) &&
                Objects.equals(realPath, that.realPath);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, resolutionId, name, realPath);
    }
}
