package com.uino.bean.sys.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Builder.Default;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 
 * <AUTHOR>
 *
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value="导出字典数据",description = "导出字典数据")
public class ExportDictionaryDto {

    @ApiModelProperty(value="字典分类id",example = "123")
    private Long dictClassId;

    @Default
    @ApiModelProperty(value="是否下载模板",example = "true/false")
    private Boolean isTpl = false;
}
