package com.uinnova.product.eam.comm.model.es;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Comment("目录操作配置表[UINO_EAM_DIR_SETTING]")
public class DirSetting {
    @Comment("目录操作设置ID")
    private Long id;
    @Comment("分类ID")
    private Long classId;
    @Comment("配置的目录名称")
    private String dirName;
    @Comment("配置的目录的层级父目录id")
    private Long parentId;
    @Comment("排序字段")
    private Integer order;
    @Comment("创建人")
    private String createUser;
    @Comment("修改人")
    private String modifyUser;
    @Comment("创建时间")
    private Long createTime;
    @Comment("修改时间")
    private Long modifyTime;
}
