package com.uino.web.cmdb.mvc;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import com.uino.init.api.ApiResult;
import com.uino.init.api.ApiVersion;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.uino.api.client.cmdb.IDataSetPriorityDisplayApiSvc;
import com.uino.web.BaseMvc;

import io.swagger.annotations.ApiOperation;

/**
 * @Classname DataSetPriorityDisplayController
 * @Description 优先显示
 * @Date 2020/3/23 8:50
 * @Created by sh
 */
@ApiVersion(1)
@Api(value = "优先显示", tags = {"数据集"})
@RestController
@RequestMapping("cmdb/dataSet")
public class DataSetPriorityDisplayMvc extends BaseMvc {

    @Autowired
    private IDataSetPriorityDisplayApiSvc dataSetPriorityDisplayApiSvc;

    @RequestMapping(value = {"/priorityDisplay"}, method = RequestMethod.POST)
    @ApiOperation("设置优先显示")
    public ApiResult<Boolean> priorityDisplay(HttpServletRequest request, HttpServletResponse response, @RequestBody String body) {
        JSONObject jsonObject = JSONObject.parseObject(body);
        Long dataSetId = jsonObject.getLong("id");
        int displayType = jsonObject.getIntValue("displayType");
        dataSetPriorityDisplayApiSvc.priorityDisplay(dataSetId, displayType);
        return ApiResult.ok(this).data(true);
    }
}
