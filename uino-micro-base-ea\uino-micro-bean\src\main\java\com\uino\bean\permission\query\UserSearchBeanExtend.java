package com.uino.bean.permission.query;

import java.util.Set;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 *
 * <AUTHOR>
 *
 */
@Getter
@Setter
@ApiModel(value="用户信息查询类",description ="用户信息查询" )
public class UserSearchBeanExtend extends SearchKeywordBean {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value="角色id",example = "123")
    private Long roleId;

    @ApiModelProperty(value="角色id集合")
    private Set<Long> roleIds;

    @ApiModelProperty(value="组织id",example = "123")
    private Long orgId;

    @ApiModelProperty(value="组织id集合")
    private Set<Long> orgIds;

    @ApiModelProperty(value="所属组织")
    private boolean onlyCurrentOrg;

    @ApiModelProperty(value = "所属域")
    private Long domainId;

    @ApiModelProperty(value = "在线状态（0，全部，1、在线 2、离线）")
    private Integer onlineStatus;
}
