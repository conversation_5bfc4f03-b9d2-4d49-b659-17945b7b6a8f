package com.uinnova.product.eam.service.sys.impl;

import cn.hutool.http.ContentType;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONObject;
import com.binary.core.exception.MessageException;
import com.binary.core.util.BinaryUtils;
import com.binary.jdbc.Page;
import com.uinnova.product.eam.base.diagram.model.*;
import com.uinnova.product.eam.base.diagram.utils.CommonUtil;
import com.uinnova.product.eam.db.diagram.es.ESEnterpriseSysUserDao;
import com.uinnova.product.eam.db.diagram.es.ESNewUserDao;
import com.uinnova.product.eam.model.EamCurrentUserInfo;
import com.uinnova.product.eam.service.diagram.ESUploadManageSvc;
import com.uinnova.product.eam.service.sys.IEamSysSvc;
import com.uinnova.product.vmdb.comm.model.image.CcImage;
import com.uino.api.client.permission.IRoleApiSvc;
import com.uino.api.client.permission.IUserApiSvc;
import com.uino.api.client.sys.ISysApiSvc;
import com.uino.bean.permission.base.SysRole;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.permission.business.CurrentUserInfo;
import com.uino.bean.permission.business.UserInfo;
import com.uino.bean.permission.query.CSysUser;
import com.uino.util.sys.SysUtil;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 系统相关服务
 *
 * <AUTHOR>
 * @version 2020/7/28
 */
@Slf4j
@Service
public class IEamSvcImpl implements IEamSysSvc {

    //private static Logger logger = LoggerFactory.getLogger(IEamSvcImpl.class);

    @Autowired
    private ISysApiSvc sysApiSvcSvc;

    @Value("${http.resource.space}")
    private String httpResourcePath;

    @Autowired
    private IUserApiSvc userSvc;

    @Autowired
    private IRoleApiSvc roleApiSvc;

    @Value("${total.file.size}")
    private Long totalFileSize;

    @Autowired
    private ESNewUserDao eamIsOldUser;

    @Autowired
    private ESEnterpriseSysUserDao enterpriseSysUserDao;

    @Autowired
    private IUserApiSvc userApiSvc;

    @Autowired
    private ESUploadManageSvc svc;

    @Value("${wiki.oauth.server.url}")
    private String oauthServerUrl;

    private static final String USER_IMAGE_REGEX = ".*[.](?i)(jpg|jpeg|png|svg)";


    @Override
    public String uploadFile(MultipartFile file) {
        return httpResourcePath + sysApiSvcSvc.uploadFile(file);
    }

    @Override
    public EamCurrentUserInfo getCurrentUser() {
        SysUser currentUser = SysUtil.getCurrentUserInfo();
        CurrentUserInfo user = userSvc.getCurrentUser();
        List<SysRole> roles = roleApiSvc.getRolesByUserId(user.getId());
        EamCurrentUserInfo eamCurrentUserInfo = new EamCurrentUserInfo();
        for (SysRole role:roles) {
            eamCurrentUserInfo.setId(user.getId());
            eamCurrentUserInfo.setDomainId(user.getDomainId());
            eamCurrentUserInfo.setIcon(user.getIcon());
            eamCurrentUserInfo.setLanguage(user.getLanguage());
            eamCurrentUserInfo.setUserName(user.getUserName());
            eamCurrentUserInfo.setUserCode(user.getLoginCode());
            eamCurrentUserInfo.setLastLoginTime(user.getLastLoginTime());
            eamCurrentUserInfo.setRoleName(role.getRoleName());
        }
        return eamCurrentUserInfo;
    }

    @Override
    public List<NewUserRecord> getIsOlderUser(Long userId) {
        NewUserQuery query = new NewUserQuery();
        query.setUserId(userId);
        return eamIsOldUser.getListByCdt(query);
    }

    @Override
    public Long setIsOlderUser(Long userId, JSONObject object) {
        NewUserQuery query = new NewUserQuery();
        query.setUserId(userId);
        List<NewUserRecord> records = eamIsOldUser.getListByCdt(query);
        if (records.size() > 0) {
            NewUserRecord record = records.get(0);
            if (object.containsKey("old")) {
                record.setOldUser(object.getBool("old"));
            }
            if (object.containsKey("editorOld")) {
                record.setEditorOldUser(object.getBool("editorOld"));
            }
            return eamIsOldUser.saveOrUpdate(record);
        } else {
            NewUserRecord record = new NewUserRecord();
            record.setUserId(userId);
            if (object.containsKey("old")) {
                record.setOldUser(object.getBool("old"));
            }
            if (object.containsKey("editorOld")) {
                record.setEditorOldUser(object.getBool("editorOld"));
            }
            return eamIsOldUser.saveOrUpdate(record);
        }
    }

    @Override
    public SysUser getUserIdByMmdId(ESEnterpriseSysUserQuery userQuery) {
        SysUser user = new SysUser();
        List<ESEnterpriseSysUser> listByCdt = enterpriseSysUserDao.getListByCdt(userQuery);
        if (!CollectionUtils.isEmpty(listByCdt)) {
            ESEnterpriseSysUser enterpriseSysUser = listByCdt.get(0);
            CSysUser query = new CSysUser();
            query.setId(enterpriseSysUser.getUserId());
            List<UserInfo> userInfoByCdt = userApiSvc.getUserInfoByCdt(query, true);
            if (!BinaryUtils.isEmpty(userInfoByCdt)) {
                UserInfo userInfo = userInfoByCdt.get(0);
                BeanUtils.copyProperties(userInfo, user);
            }
        }
        return user;
    }

    @Override
    public Long getMmdIdByUserId(Long userId) {
        Long mmdId = null;
        ESEnterpriseSysUserQuery query = new ESEnterpriseSysUserQuery();
        query.setUserId(userId);
        List<ESEnterpriseSysUser> listByCdt = enterpriseSysUserDao.getListByCdt(query);
        if (!CollectionUtils.isEmpty(listByCdt)) {
            ESEnterpriseSysUser enterpriseSysUser = listByCdt.get(0);
            mmdId = enterpriseSysUser.getMmdId();
        }
        if (mmdId == null) {
            RequestHeadParam requestHeadParam = CommonUtil.getRequestHeadParam();
            // 调用数字空间接口获取用户信息
            HttpResponse mmdIdUser = HttpRequest
                    .get(this.oauthServerUrl + "/api/user/thingjs/get_mmd_id")
                    .contentType(ContentType.JSON.getValue())
                    .auth(requestHeadParam.getToken())
                    .header(Header.USER_AGENT, requestHeadParam.getUserAgent())
                    .execute();
            if (!mmdIdUser.isOk()) {
                log.info("获取当前用户MMD_ID失败, 报错如下：" + mmdIdUser.body());
            } else {
                JSONObject userJson = new JSONObject(mmdIdUser.body());
                ESEnterpriseSysUser esEnterpriseSysUser = new ESEnterpriseSysUser();
                esEnterpriseSysUser.setUserId(userId);
                esEnterpriseSysUser.setMmdId(mmdId = userJson.getLong("mmd_id"));
                this.saveOrUpdate(esEnterpriseSysUser);
            }
        }
        return mmdId;
    }

    @Override
    public Long saveOrUpdate(ESEnterpriseSysUser esEnterpriseSysUser) {
        return enterpriseSysUserDao.saveOrUpdate(esEnterpriseSysUser);
    }

    @Override
    public ESUploadManage upload(MultipartFile file) {
        // 1.判断上传的图片是否超过50MB
        BoolQueryBuilder bQuery = QueryBuilders.boolQuery();
        bQuery.must(QueryBuilders.termQuery("fileType", 1L));
        Page<ESUploadManage> queryData = this.svc.queryData(1, 3000, "createTime", bQuery);
        List<ESUploadManage> data = queryData.getData();
        AtomicReference<Long> size = new AtomicReference<>(0L);
        if (!BinaryUtils.isEmpty(data)) {
            data.forEach(em -> {
                if (!BinaryUtils.isEmpty(em.getFileSize())) {
                    size.updateAndGet(v -> v + em.getFileSize());
                    if (size.get() > totalFileSize) {
                        throw new MessageException("您的存储空间已达上限，请释放部分空间后再次上传！");
                    }
                }
            });

        }
        if (size.get() + file.getSize() > totalFileSize) {
            throw new MessageException("您的存储空间已达上限，请释放部分空间后再次上传！");
        }
        // 2.上传图片
        String url = sysApiSvcSvc.uploadFile(file);
        return svc.upload(url, file.getSize());
    }

    @Override
    public Page<ESUploadManage> queryData(int pageNum, int pageSize, String orders) {
        BoolQueryBuilder bQuery = QueryBuilders.boolQuery();
        bQuery.must(QueryBuilders.termQuery("fileType", 1L));
        return this.svc.queryData(pageNum, pageSize, orders, bQuery);
    }

    @Override
    public Long deleteImage(CcImage image) {
        return this.svc.deleteImage(image);
    }

}
