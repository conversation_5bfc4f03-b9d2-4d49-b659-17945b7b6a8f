package com.uino.bean.permission.business.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.util.Assert;

import com.uino.bean.permission.business.IValidDto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 交换组织顺序请求数据传输类
 * 
 * <AUTHOR>
 *
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value="交换组织顺序请求数据传输类",description = "交换组织顺序请求数据传输类")
public class InterchangeOrgNoRequestDto implements IValidDto {

	/**
	 * 交换源组织id
	 */
	@ApiModelProperty(value="交换源组织id",example = "123")
	private Long sourceOrgId;
	/**
	 * 交换目标组织id
	 */
	@ApiModelProperty(value="交换目标组织id",example = "123")
	private Long targetOrgId;


	@Override
	public void valid() {
		// TODO Auto-generated method stub
		Assert.notNull(sourceOrgId, "X_PARAM_NOT_NULL${name:sourceOrgId}");
		Assert.notNull(targetOrgId, "X_PARAM_NOT_NULL${name:targetOrgId}");
	}
}
