package com.uinnova.product.eam.rpc.diagram;

import cn.hutool.json.JSONObject;
import com.binary.jdbc.Page;
import com.uinnova.product.eam.api.diagram.SysApiClient;
import com.uinnova.product.eam.base.diagram.model.ESUploadManage;
import com.uinnova.product.eam.base.diagram.model.NewUserRecord;
import com.uinnova.product.eam.service.sys.IEamSysSvc;
import com.uinnova.product.vmdb.comm.model.image.CcImage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Service
public class SysSvcRpc implements SysApiClient {
    @Override
    public String uploadFile(MultipartFile file) {
        return null;
    }
    @Autowired
    IEamSysSvc eamSysSvc;
    @Override
    public List<NewUserRecord> getIsOlderUser(Long userId) {
        return eamSysSvc.getIsOlderUser(userId);
    };

    @Override
    public  Long setIsOlderUser(Long userId, JSONObject Object) {
        return eamSysSvc.setIsOlderUser(userId, Object);
    };

    @Override
    public ESUploadManage upload(MultipartFile file) {
        return eamSysSvc.upload(file);
    }

    @Override
    public Page<ESUploadManage> queryData(int pageNum, int pageSize, String orders) {
        return eamSysSvc.queryData(pageNum,pageSize,orders);
    }

    @Override
    public Long deleteImage(CcImage image) {
        return eamSysSvc.deleteImage(image);
    }
}
