package com.uinnova.product.eam.base.diagram.model;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

@Data
public class NewUserRecord {

    private Long id;

    private Long userId;

    private boolean isOldUser;

    private boolean isEditorOldUser;

    @Comment("创建时间[CREATE_TIME]    创建时间:yyyyMMddHHmmss")
    private Long createTime;

    @Comment("更新时间[MODIFY_TIME]    更新时间:yyyyMMddHHmmss")
    private Long modifyTime;
}
