package com.uinnova.product.eam.model.cj.request;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.model.cj.vo.MoveDirAndDiagramCdtVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 视图、方案、文件夹批量复制参数
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PlanDiagramDirBatchCopyParam extends MoveDirAndDiagramCdtVO {
    @Comment("要复制的方案")
    private List<Long> planIdList;
}
