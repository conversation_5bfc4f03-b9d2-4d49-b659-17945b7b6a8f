package com.uinnova.product.eam.model.vo;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

@Data
public class AppSystemQueryConParamVo {

    public AppSystemQueryConParamVo(String paramName, String paramColor) {
        this.paramName = paramName;
        this.paramColor = paramColor;
    }

    @Comment("参数名称")
    private String paramName;

    @Comment("参数颜色")
    private String paramColor;
}
