package com.uinnova.product.vmdb.comm.model.license;

import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;

/**
 * 
 * <AUTHOR>
 *
 */
@Comment("受权服务器表[CC_LICENSE_AUTH_SERVER]")
public class CcLicenseAuthServer implements EntityBean {
    private static final long serialVersionUID = 1L;

    @Comment("ID[ID]")
    private Long id;

    @Comment("授权ID[AUTH_ID]")
    private Long authId;

    @Comment("服务器IP[SERVER_IP]")
    private String serverIp;

    @Comment("服务器编码[SERVER_CODE]")
    private String serverCode;

    @Comment("服务器描述[SERVER_DESC]")
    private String serverDesc;

    @Comment("创建时间[CREATE_TIME]    yyyyMMddHHmmss")
    private Long createTime;

    @Comment("修改时间[MODIFY_TIME]    yyyyMMddHHmmss")
    private Long modifyTime;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getAuthId() {
        return this.authId;
    }

    public void setAuthId(Long authId) {
        this.authId = authId;
    }

    public String getServerIp() {
        return this.serverIp;
    }

    public void setServerIp(String serverIp) {
        this.serverIp = serverIp;
    }

    public String getServerCode() {
        return this.serverCode;
    }

    public void setServerCode(String serverCode) {
        this.serverCode = serverCode;
    }

    public String getServerDesc() {
        return this.serverDesc;
    }

    public void setServerDesc(String serverDesc) {
        this.serverDesc = serverDesc;
    }

    public Long getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getModifyTime() {
        return this.modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }

}
