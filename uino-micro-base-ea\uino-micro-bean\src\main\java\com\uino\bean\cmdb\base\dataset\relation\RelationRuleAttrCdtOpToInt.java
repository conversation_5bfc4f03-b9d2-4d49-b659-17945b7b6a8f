package com.uino.bean.cmdb.base.dataset.relation;


import com.alibaba.fastjson.parser.DefaultJSONParser;
import com.alibaba.fastjson.parser.deserializer.ObjectDeserializer;

import java.lang.reflect.Type;

public class RelationRuleAttrCdtOpToInt implements ObjectDeserializer {


	@Override
	public <T> T deserialze(DefaultJSONParser parser, Type type, Object o) {
		int code = parser.parseObject(int.class);
		return (T) RelationRuleAttrCdtOp.valueOf(code);
	}

	@Override
	public int getFastMatchToken() {
		return 0;
	}
}