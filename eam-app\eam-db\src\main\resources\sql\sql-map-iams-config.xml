<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMapConfig PUBLIC "-//ibatis.apache.org//DTD SQL Map Config 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-config-2.dtd">

<sqlMapConfig>

	<settings  statementCachingEnabled="false" cacheModelsEnabled="true" lazyLoadingEnabled="false" useStatementNamespaces="true" enhancementEnabled="true" />
 
	<!-- diagram -->
	<sqlMap resource="sql/diagram/VC_DIAGRAM_DIR_SqlMap.xml" />
	<sqlMap resource="sql/diagram/VC_DIAGRAM_ELE_SqlMap.xml" />
	<sqlMap resource="sql/diagram/VC_DIAGRAM_ELE_VERSION_SqlMap.xml" />
	<sqlMap resource="sql/diagram/VC_DIAGRAM_GROUP_SqlMap.xml" />
	<sqlMap resource="sql/diagram/VC_DIAGRAM_SqlMap.xml" />
	<sqlMap resource="sql/diagram/VC_DIAGRAM_TAG_SqlMap.xml" />
	<sqlMap resource="sql/diagram/VC_DIAGRAM_VERSION_SqlMap.xml" />
	<sqlMap resource="sql/diagram/VC_GROUP_SqlMap.xml" />
	<sqlMap resource="sql/diagram/VC_GROUP_USER_SqlMap.xml" />
	<sqlMap resource="sql/diagram/VC_TAG_SqlMap.xml" />
	<sqlMap resource="sql/diagram/VC_DIAGRAM_CI_ATTR_DISP_SqlMap.xml" />
	<sqlMap resource="sql/diagram/VC_DIAGRAM_CI_ATTR_VERSION_SqlMap.xml" />
	<sqlMap resource="sql/diagram/DC_COMB_DIAGRAM_SqlMap.xml" />
	<sqlMap resource="sql/diagram/VC_THEME_SqlMap.xml" />
	<sqlMap resource="sql/diagram/VC_THEME_FIELD_SqlMap.xml" />
	<sqlMap resource="sql/diagram/VC_THEME_DIAGRAM_SqlMap.xml" />
	<sqlMap resource="sql/diagram/VC_DIAGRAM_ITEM_OPTE_SqlMap.xml" />
	<sqlMap resource="sql/diagram/VC_COMMENT_SqlMap.xml" />
	<sqlMap resource="sql/diagram/VC_GROUP_LOG_SqlMap.xml"/>
	<sqlMap resource="sql/diagram/VC_DIAGRAM_ENSH_SqlMap.xml"/>
	<sqlMap resource="sql/diagram/VC_BASE_CONFIG_SqlMap.xml"/>
	<sqlMap resource="sql/diagram/VC_CI_LINKED_SqlMap.xml"/>
	<sqlMap resource="sql/diagram/VC_FEEDBACK_SqlMap.xml"/>
	<sqlMap resource="sql/diagram/VC_DIAGRAM_LOG_SqlMap.xml"/>
	<sqlMap resource="sql/diagram/VC_DIAGRAM_OP_SqlMap.xml"/>
	<sqlMap resource="sql/diagram/VC_NODE_LINKED_SqlMap.xml"/>
	<sqlMap resource="sql/diagram/VC_NODE_LINKED_PATH_SqlMap.xml"/>
	<sqlMap resource="sql/diagram/VC_DIAGRAM_VERSION_SqlMap.xml"/>

	<!-- ext -->
	<sqlMap resource="sql/ext/VC_DIAGRAM_EXT_SqlMap.xml" />
	<sqlMap resource="sql/ext/VC_DIAGRAM_GROUP_EXT_SqlMap.xml" />
	<sqlMap resource="sql/ext/VC_DIAGRAM_VERSION_EXT_SqlMap.xml" />
	<sqlMap resource="sql/ext/VC_TAG_EXT_SqlMap.xml" />
	<sqlMap resource="sql/ext/VC_USER_PV_COUNT_EXT_SqlMap.xml"/>
	
	<sqlMap resource="sql/diagram/VC_PROCESS_SqlMap.xml" />
	<sqlMap resource="sql/diagram/VC_PROCESS_NODE_SqlMap.xml" />
	<sqlMap resource="sql/diagram/VC_PROCESS_DEF_SqlMap.xml" />
	<sqlMap resource="sql/diagram/VC_PROCESS_NODE_DIAGRAM_SqlMap.xml" />
	<sqlMap resource="sql/diagram/VC_PROCESS_NODE_USER_SqlMap.xml" />
	<sqlMap resource="sql/diagram/VC_CLASS_ADD_ATTR_SqlMap.xml"/>
	
	
	<sqlMap resource="sql/diagram/VC_DIAGRAM_DIR_RELATION_SqlMap.xml"/>
	<sqlMap resource="sql/diagram/VC_PROCESS_OP_LOG_SqlMap.xml"/>
	<sqlMap resource="sql/diagram/VC_CI_PROCESS_STATUS_SqlMap.xml"/>
	<sqlMap	resource="sql/diagram/VC_ROLE_ORG_SqlMap.xml" />
	<sqlMap	resource="sql/diagram/VC_USER_PV_COUNT_SqlMap.xml" />
</sqlMapConfig>


