package com.uinnova.product.vmdb.comm.model.sys;

import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;

/**
 * 
 * <AUTHOR>
 *
 */
@Comment("个性化模块定制[CC_CUSTOM_MODULE]")
public class CcCustomModule implements EntityBean {
    private static final long serialVersionUID = 1L;

    @Comment("ID[ID]")
    private Long id;

    @Comment("模块编码[MODULE_CODE]    模块默认CODE")
    private String moduleCode;

    @Comment("模块名称[MODULE_NAME]    模块默认名称")
    private String moduleName;

    @Comment("语言[LANGUAGE_CODE]")
    private String languageCode;

    @Comment("菜单定制别名[CUSTOM_NAME]    定制别名")
    private String customName;

    @Comment("菜单图标[CUSTOM_IMG]    未选中状态模块图标")
    private String customImg;

    @Comment("选中菜单图标[CUSTOM_SELECTED_IMG]    选中状态模块图标")
    private String customSelectedImg;

    @Comment("显示排序[CUSTOM_ORDER_NO]    定制显示排序")
    private Integer customOrderNo;

    @Comment("定义描述[CUSTOM_DESC]")
    private String customDesc;

    @Comment("所属域[DOMAIN_ID]")
    private Long domainId;

    @Comment("数据状态[DATA_STATUS]    数据状态:数据状态：1-正常 0-删除")
    private Integer dataStatus;

    @Comment("创建人[CREATOR]")
    private String creator;

    @Comment("修改人[MODIFIER]")
    private String modifier;

    @Comment("创建时间[CREATE_TIME]    创建时间:yyyyMMddHHmmss")
    private Long createTime;

    @Comment("修改时间[MODIFY_TIME]    修改时间:yyyyMMddHHmmss")
    private Long modifyTime;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getModuleCode() {
        return this.moduleCode;
    }

    public void setModuleCode(String moduleCode) {
        this.moduleCode = moduleCode;
    }

    public String getModuleName() {
        return this.moduleName;
    }

    public void setModuleName(String moduleName) {
        this.moduleName = moduleName;
    }

    public String getLanguageCode() {
        return this.languageCode;
    }

    public void setLanguageCode(String languageCode) {
        this.languageCode = languageCode;
    }

    public String getCustomName() {
        return this.customName;
    }

    public void setCustomName(String customName) {
        this.customName = customName;
    }

    public String getCustomImg() {
        return this.customImg;
    }

    public void setCustomImg(String customImg) {
        this.customImg = customImg;
    }

    public String getCustomSelectedImg() {
        return this.customSelectedImg;
    }

    public void setCustomSelectedImg(String customSelectedImg) {
        this.customSelectedImg = customSelectedImg;
    }

    public Integer getCustomOrderNo() {
        return this.customOrderNo;
    }

    public void setCustomOrderNo(Integer customOrderNo) {
        this.customOrderNo = customOrderNo;
    }

    public String getCustomDesc() {
        return this.customDesc;
    }

    public void setCustomDesc(String customDesc) {
        this.customDesc = customDesc;
    }

    public Long getDomainId() {
        return this.domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }

    public Integer getDataStatus() {
        return this.dataStatus;
    }

    public void setDataStatus(Integer dataStatus) {
        this.dataStatus = dataStatus;
    }

    public String getCreator() {
        return this.creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getModifier() {
        return this.modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public Long getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getModifyTime() {
        return this.modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }

}
