package com.uino.provider.server.web.monitor.mvc;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.binary.jdbc.Page;
import com.uino.bean.monitor.base.SimulationRuleInfo;
import com.uino.bean.monitor.query.SimulationRuleSearchBean;
import com.uino.provider.feign.monitor.SimulationRuleFeign;
import com.uino.service.simulation.ISimulationRuleSvc;

@RestController
@RequestMapping("feign/simulationRule")
public class SimulationRuleFeignMvc implements SimulationRuleFeign {

	@Autowired
	private ISimulationRuleSvc ruleSvc;

	@Override
	public Page<SimulationRuleInfo> querySimulationRuleInfoPage(SimulationRuleSearchBean bean) {
		return ruleSvc.querySimulationRuleInfoPage(bean);
	}

	@Override
	public Long saveOrUpdateSimulationRule(SimulationRuleInfo ruleInfo) {
		return ruleSvc.saveOrUpdateSimulationRule(ruleInfo);
	}

	@Override
	public Integer deleteSimulationRuleById(Long id) {
		return ruleSvc.deleteSimulationRuleById(id);
	}

	@Override
	public List<SimulationRuleInfo> querySimulationRuleInfoList(SimulationRuleSearchBean bean) {
		return ruleSvc.querySimulationRuleInfoList(bean);
	}

}
