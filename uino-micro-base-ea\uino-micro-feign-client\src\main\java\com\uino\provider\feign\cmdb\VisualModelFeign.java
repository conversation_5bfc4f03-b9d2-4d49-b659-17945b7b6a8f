package com.uino.provider.feign.cmdb;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.uino.bean.cmdb.base.ESVisualModel;
import com.uino.provider.feign.config.BaseFeignConfig;

/**
 * 
 * <AUTHOR>
 *
 */
@Component
@FeignClient(name = "${feign-server-name:tarsier-platform}", path = "${feign-server-path:tarsier-platform}/feign/visualModel", configuration = {BaseFeignConfig.class})
public interface VisualModelFeign {

    /**
     * 查询可视化模型列表
     * 
     * @param domainId
     * @return
     */
    @PostMapping("queryVisualModels")
    public List<ESVisualModel> queryVisualModels(@RequestParam(required = false, value = "domainId") Long domainId);

    /**
     * 保存可视化模型
     * 
     * @param model
     * @return
     */
    @PostMapping("saveVisualModel")
    public Long saveVisualModel(@RequestBody(required = true) ESVisualModel model);

    /**
     * 修改可视化模型名称
     * 
     * @param model
     * @return
     */
    @PostMapping("updateVisualModelName")
    public void updateVisualModelName(@RequestBody(required = true) ESVisualModel model);

    /**
     * 删除可视化模型
     * 
     * @param id
     * @return
     */
    @PostMapping("deleteVisualModel")
    public void deleteVisualModel(@RequestParam(value = "id", required = true) Long id);
}
