package com.uinnova.product.eam.service.es;

import com.uinnova.product.eam.comm.model.es.EamVersionTag;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;

/**
 * 版本标签
 * <AUTHOR>
 */
@Service
public class EamVersionTagDao extends AbstractESBaseDao<EamVersionTag, EamVersionTag> {
    @Override
    public String getIndex() {
        return "uino_eam_version_tag";
    }

    @Override
    public String getType() {
        return "_doc";
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }
}
