package com.uino.bean.cmdb.business;

import java.io.Serializable;
import java.util.List;

import org.springframework.util.Assert;

import com.binary.core.util.BinaryUtils;
import com.uino.bean.permission.business.IValidDto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@ApiModel(value = "对象属性批量修改参数体", description = "对象属性批量修改参数体")
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CIAttrValueUpdateDto implements Serializable, IValidDto {

	/**
	 * 
	 */
	private static final long serialVersionUID = -3525816290637161320L;

	@ApiModelProperty(value = "分类ID", example = "27393335556218", required = true)
	private Long classId;

	@ApiModelProperty(value = "对象id集合", required = true)
	private List<Long> ciIds;

	@ApiModelProperty(value = "属性名称", example = "IP地址", required = true)
	private String proName;

	@ApiModelProperty(value = "属性值", required = true)
	private String value;

    @Builder.Default
	@ApiModelProperty(value="是否选择全部数据",example="false",required = true)
	private boolean all=false;


	@ApiModelProperty(value="排除的id集合")
	private List<Long> unCiIds;
	@Override
	public void valid() {
		Assert.notNull(classId, "X_PARAM_NOT_NULL${name:classId}");
		Assert.isTrue(!BinaryUtils.isEmpty(ciIds), "X_PARAM_NOT_NULL${name:ciIds}");
		Assert.isTrue(ciIds.size() <= 3000, "单次批量修改对象属性值不可超过3000条");
		Assert.notNull(proName, "X_PARAM_NOT_NULL${name:proName}");
		Assert.notNull(value, "X_PARAM_NOT_NULL${name:value}");
		Assert.notNull(all,"请选择是否全选数据");
	}

}
