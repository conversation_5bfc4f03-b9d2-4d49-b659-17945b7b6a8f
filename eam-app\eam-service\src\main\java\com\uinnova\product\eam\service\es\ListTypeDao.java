package com.uinnova.product.eam.service.es;

import com.uinnova.product.eam.comm.model.CListType;
import com.uinnova.product.eam.comm.model.ListType;
import com.uinnova.product.eam.comm.model.es.CEamListing;
import com.uinnova.product.eam.comm.model.es.EamListing;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;

/**
 * <AUTHOR>
 */
@Service
public class ListTypeDao extends AbstractESBaseDao<ListType, CListType> {
    @Override
    public String getIndex() {
        return "uino_eam_listing_type";
    }

    @Override
    public String getType() {
        return getIndex();
    }
    @PostConstruct
    public void init() {
        super.initIndex();
    }
}
