package com.uinnova.product.devcloud.i18n.client.support;

import lombok.Data;

/**
 * description
 *
 * <AUTHOR>
 * @since 2024/5/16 13:58
 */
@Data
public class I18nTransData {

    private static final long serialVersionUID = 1L;
    private String code;
    private String classCode;
    private Long createTime;
    private Long modifyTime;
    private String lanZhc;
    private String lanZht;
    private String lanEn;
    private String lanFr;
    private String lanDe;
    private String lanIt;
    private String lanJa;
    private String lanKo;
    private String lanAf;
    private String lanAr;
    private String lanAz;
    private String lanBe;
    private String lanBg;
    private String lanCa;
    private String lanCs;
    private String lanDa;
    private String lanEl;
    private String lanEs;
    private String lanFa;
    private String lanFi;
    private String lanHe;
    private String lanHr;
    private String lanHu;
    private String lanHy;
    private String lanId;
    private String lanIs;
    private String lanKa;
    private String lanKk;
    private String lanLt;
    private String lanLv;
    private String lanMi;
    private String lanMn;
    private String lanMs;
    private String lanNb;
    private String lanNl;
    private String lanPl;
    private String lanPt;
    private String lanRo;
    private String lanRu;
    private String lanSa;
    private String lanSk;
    private String lanSl;
    private String lanSq;
    private String lanSv;
    private String lanSw;
    private String lanSyr;
    private String lanTh;
    private String lanTr;
    private String lanUk;
    private String lanUz;
    private String lanVi;
}
