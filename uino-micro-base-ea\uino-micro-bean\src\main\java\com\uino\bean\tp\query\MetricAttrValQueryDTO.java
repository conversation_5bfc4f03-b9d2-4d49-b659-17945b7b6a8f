package com.uino.bean.tp.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: weixuesong
 * @date: 2020/10/22 14:36
 **/
@Data
@ApiModel(value="指标标签值查询条件类",description = "指标标签值查询条件")
public class MetricAttrValQueryDTO {
    @ApiModelProperty(value="分类id",example = "123")
    private Long classId;

    @ApiModelProperty(value="检索值")
    private String word;

    @ApiModelProperty(value="检索属性名称")
    private String attrKey;

    @ApiModelProperty(value="指标")
    private String metric;
}
