package com.uino.dao.permission.rlt;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

import jakarta.annotation.PostConstruct;

import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.stereotype.Service;

import com.uino.dao.AbstractESBaseDao;
import com.uino.dao.ESConst;
import com.uino.bean.permission.base.SysRoleDataModuleRlt;
import com.uino.bean.permission.query.CSysRoleDataModuleRlt;

/**
 * <b>角色-数据模块关系
 * 
 * <AUTHOR>
 */
@Service
public class ESRoleDataModuleRltSvc extends AbstractESBaseDao<SysRoleDataModuleRlt, CSysRoleDataModuleRlt> {

    @Override
    public String getIndex() {
        return ESConst.INDEX_SYS_ROLE_DATAMODULE_RLT;
    }

    @Override
    public String getType() {
        return ESConst.INDEX_SYS_ROLE_DATAMODULE_RLT;
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }

    /**
     * 获取数据权限
     * 
     * @param roleIds 角色id
     * @param moduleCodes 模块代码
     * @return
     */
    public List<SysRoleDataModuleRlt> getListByRoleIds(Set<Long> roleIds, List<String> moduleCodes) {
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termsQuery("roleId", roleIds));
        if (moduleCodes != null && !moduleCodes.isEmpty()) {
            query.must(QueryBuilders.termsQuery("dataModuleCode.keyword", moduleCodes));
        }
        long count = countByCondition(query);
        return super.getListByQuery(1, new BigDecimal(count).intValue(), query).getData();
    }

    /**
     * 获取角色下的所有模块数据权限
     * 
     * @param roleIds 角色id
     * @return
     */
    public List<SysRoleDataModuleRlt> getListByRoleIds(Set<Long> roleIds) {
        return super.getListByQuery(1, 5000, QueryBuilders.termsQuery("roleId", roleIds)).getData();
    }

}
