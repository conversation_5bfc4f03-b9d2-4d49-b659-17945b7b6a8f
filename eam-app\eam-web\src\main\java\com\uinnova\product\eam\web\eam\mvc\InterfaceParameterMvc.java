package com.uinnova.product.eam.web.eam.mvc;

import com.binary.framework.web.RemoteResult;
import com.uinnova.product.eam.comm.model.es.InterfaceParameter;
import com.uinnova.product.eam.service.InterfaceParameterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/eam/InterfaceParameter")
public class InterfaceParameterMvc {

    @Autowired
    private InterfaceParameterService interfaceParameterService;

    @PostMapping("/save")
    public RemoteResult save(@RequestBody List<InterfaceParameter> interfaceParameterList) {
        Integer result = interfaceParameterService.save(interfaceParameterList);
        return new RemoteResult(result);
    }

    @GetMapping("/getList")
    public RemoteResult getList(@RequestParam Integer parameterType,@RequestParam(name="requestMethod" , required = false)Integer requestMethod) {
        Map<String,List<InterfaceParameter>> result = interfaceParameterService.getList(parameterType,requestMethod);
        return new RemoteResult(result);
    }

    @PostMapping("/getChildListByCode")
    public RemoteResult getChildListByCode(@RequestParam String parentCode) {
        List<InterfaceParameter> result = interfaceParameterService.getChildListByCode(parentCode);
        return new RemoteResult(result);
    }

    @GetMapping("deleteById")
    public RemoteResult deleteById(@RequestParam Long id) {
        Integer result = interfaceParameterService.deleteById(id);
        return new RemoteResult(result);
    }
}
