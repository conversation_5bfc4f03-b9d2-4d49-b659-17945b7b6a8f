package com.uino.bean.cmdb.base;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;
import org.springframework.util.Assert;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.binary.core.util.BinaryUtils;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.comm.model.ci.CcCiClass;
import com.uino.bean.permission.business.IValidDto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * ES对应的分类
 * 
 * <AUTHOR>
 */
@ApiModel(value="ES对应的分类",description = "ES对应的分类")
public class ESCIClassInfo extends CcCiClass implements Serializable, IValidDto {

	private static final long serialVersionUID = 6949844723148728871L;

	/**
	 * 孪生模型id
	 */
	@ApiModelProperty(value = "孪生模型id", example = "abc001", required = true)
	private String dtClassId;
    /**
     * 分类类型
     */
	@ApiModelProperty(value = "分类类型:1=设备，2=设施，3=人员，4=空间", example = "1", required = true)
	private Integer clsType;

	/**
	 * 3d图标
	 */
	@ApiModelProperty(value="3d图标",example = "icon_apple")
	private String icon3d;

	/**
	 * 3d图标对应3D模型属性默认图标
	 */
	@ApiModelProperty(value="3d图标对应3D模型属性默认图标",example = "icon_apple")
	private String modelIcon3D;

	/**
	 * 属性定义
	 */
	@ApiModelProperty(value="属性定义",example="color")
    private List<ESCIAttrDefInfo> attrDefs;


	@JsonIgnore
	@JSONField(serialize = false, deserialize = false)
    public List<CcCiAttrDef> getCcAttrDefs() {
        List<CcCiAttrDef> res = new ArrayList<>();
        if (!BinaryUtils.isEmpty(attrDefs)) {
            for (ESCIAttrDefInfo attrDef : attrDefs) {
                res.add(attrDef);
            }
        }
        return res;
    }

	@JsonIgnore
	@JSONField(serialize = false, deserialize = false)
    public void setCcAttrDefs(List<CcCiAttrDef> ccAttrDefs) {
		if (BinaryUtils.isEmpty(this.attrDefs)) {
			this.attrDefs = JSON.parseArray(JSON.toJSONString(ccAttrDefs), ESCIAttrDefInfo.class);
		} else {
			Map<String, ESCIAttrDefInfo> defMap = this.attrDefs.stream()
					.collect(Collectors.toMap(ESCIAttrDefInfo::getProName, def -> def));
			this.attrDefs = ccAttrDefs.stream().map(ccDef -> {
				ESCIAttrDefInfo def = defMap.containsKey(ccDef.getProName()) ? defMap.get(ccDef.getProName())
						: new ESCIAttrDefInfo();
				BeanUtils.copyProperties(ccDef, def);
					return def;
			}).collect(Collectors.toList());
		}
    }

	public List<ESCIAttrDefInfo> getAttrDefs() {
		return attrDefs;
	}

	public void setAttrDefs(List<ESCIAttrDefInfo> attrDefs) {
		this.attrDefs = attrDefs;
	}

	public String getDtClassId() {
		return dtClassId;
	}

	public void setDtClassId(String dtClassId) {
		this.dtClassId = dtClassId;
	}

	public Integer getClsType() {
		return clsType;
	}

	public void setClsType(Integer clsType) {
		this.clsType = clsType;
	}

	public String getIcon3d() {
		return icon3d;
	}

	public void setIcon3d(String icon3d) {
		this.icon3d = icon3d;
	}

	@Override
	public void valid() {
		Assert.notNull(this.getClassCode(), "关系分类code不得为空");
        Assert.notNull(this.getClassName(), "关系分类name不得为空");
	}

	public String getModelIcon3D() {
		return modelIcon3D;
	}

	public void setModelIcon3D(String modelIcon3D) {
		this.modelIcon3D = modelIcon3D;
	}


}
