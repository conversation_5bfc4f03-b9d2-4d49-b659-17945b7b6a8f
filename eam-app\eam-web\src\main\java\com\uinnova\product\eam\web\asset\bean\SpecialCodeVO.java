package com.uinnova.product.eam.web.asset.bean;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.io.Serializable;

@Data
public class SpecialCodeVO implements Serializable {

    @Comment("标准规范对应ciCode")
    private String ciCode;

    @Comment("标准规范名称")
    private String specificationName;

    @Comment("创建人")
    private String creator;

    @Comment("创建时间")
    private Long createTime;

}
