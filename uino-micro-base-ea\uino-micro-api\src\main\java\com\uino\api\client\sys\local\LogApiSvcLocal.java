package com.uino.api.client.sys.local;

import com.uino.dao.BaseConst;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.binary.jdbc.Page;
import com.uino.service.sys.microservice.ILogSvc;
import com.uino.bean.sys.base.SysLoginLog;
import com.uino.bean.sys.business.QueryLoginLogRequestDto;
import com.uino.api.client.sys.ILogApiSvc;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
public class LogApiSvcLocal implements ILogApiSvc {

	@Autowired
	private ILogSvc svc;

	@Override
	public SysLoginLog addLoginLog(Long userId) {
		return svc.addLoginLog(userId);
	}

	@Override
	public Page<SysLoginLog> queryLoginLog(QueryLoginLogRequestDto query) {
		return svc.queryLoginLog(query);
	}

	@Override
	public SysLoginLog addLoginLog(String userCode) {
		return svc.addLoginLog(BaseConst.DEFAULT_DOMAIN_ID, userCode);
	}

	@Override
	public SysLoginLog addLoginLog(Long domainId, String userCode) {
		return svc.addLoginLog(domainId, userCode);
	}

}
