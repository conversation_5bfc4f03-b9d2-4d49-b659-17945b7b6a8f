package com.uinnova.product.eam.web.cj;

import com.uinnova.product.eam.base.cj.ResultMsg;
import com.uinnova.product.eam.model.cj.group.AddGroup;
import com.uinnova.product.eam.model.cj.group.ModifyGroup;
import com.uinnova.product.eam.model.cj.group.QueryGroup;
import com.uinnova.product.eam.model.cj.vo.CollaborateVO;
import com.uinnova.product.eam.model.cj.vo.PlanChapterCollaborateVO;
import com.uinnova.product.eam.service.cj.service.PlanChapterCollaborateService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;

@RestController
@RequestMapping("/plan/chapter/share")
public class PlanChapterShareController {

    @Resource
    private PlanChapterCollaborateService planChapterShareService;

    @PostMapping("/saveOrUpdate")
    public ResultMsg saveOrUpdate(@RequestBody @Validated(AddGroup.class) PlanChapterCollaborateVO planChapterCollaborateVO) {
        planChapterShareService.saveOrUpdatePlanChapterCollaborate(planChapterCollaborateVO);
        return new ResultMsg(true);
    }

    @PostMapping("/completeCollaborate")
    public ResultMsg completeCollaborate(@RequestBody @Validated(ModifyGroup.class) PlanChapterCollaborateVO planChapterCollaborateVO) {
        Boolean result = planChapterShareService.completeCollaborate(planChapterCollaborateVO);
        return new ResultMsg(result);
    }

    /**
     * 获取方案章节协作用户和完成状态
     * @param planChapterCollaborateVO
     * @return
     */
    @PostMapping("/getUsersAndComplete")
    public ResultMsg getUsersAndComplete(@RequestBody @Validated(QueryGroup.class) PlanChapterCollaborateVO planChapterCollaborateVO) {
        CollaborateVO result = planChapterShareService.getUsersAndComplete(planChapterCollaborateVO);
        return new ResultMsg(result);
    }
}
