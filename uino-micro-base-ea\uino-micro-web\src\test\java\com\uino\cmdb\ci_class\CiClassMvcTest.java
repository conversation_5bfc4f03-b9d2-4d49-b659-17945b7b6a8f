package com.uino.cmdb.ci_class;

import static org.junit.Assert.assertEquals;

import java.io.File;
import java.io.FileInputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import com.alibaba.fastjson.JSONObject;
import com.binary.json.JSON;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.comm.model.ci.CcCiClass;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uino.StartBaseWebAppliaction;
import com.uino.bean.cmdb.business.ImportSheetMessage;

@RunWith(SpringRunner.class)
@WebAppConfiguration
@SpringBootTest(classes = {StartBaseWebAppliaction.class})
@ActiveProfiles("provider-local")
@AutoConfigureMockMvc(addFilters = false)
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class CiClassMvcTest {

    @Autowired
    private MockMvc mockMvc;

    private String token = "ca14ed042fe7912426b484f6ea5c754b4fb51ae4a72037e9127da51743e0d1f9650e6e78ed4466ac970acb6a0b2f46fd26958c4ab2d115cc71b34ca8a02db24c";

    private static Long classId = 0L;

    @Test
    public void atestSaveOrUpdate() throws Exception {
        CcCiClassInfo param = new CcCiClassInfo();
        CcCiClass ciClass=new CcCiClass();
        ciClass.setClassCode("test-test");
        ciClass.setClassName("test-test");
        ciClass.setDirId(1L);
        ciClass.setParentId(0L);
        ciClass.setCiType(1);
        List<CcCiAttrDef> attrDefs=new ArrayList<>();
        CcCiAttrDef def=new CcCiAttrDef();
        def.setProName("业务主键");
        def.setCiType(1);
        def.setIsMajor(1);
        def.setIsRequired(1);
        def.setProType(3);
        attrDefs.add(def);
        param.setCiClass(ciClass);
        param.setAttrDefs(attrDefs);
        String requestJson = JSONObject.toJSONString(param);
        ResultActions reaction =
            this.mockMvc.perform(MockMvcRequestBuilders.post("/cmdb/ciClass/saveOrUpdate").contentType(MediaType.APPLICATION_JSON).content(requestJson).accept(MediaType.APPLICATION_JSON)// 返回值接收json
                .header("Content-Type", "application/json").header("token", token));
        MvcResult mvcResult = reaction.andReturn();
        String resStr = mvcResult.getResponse().getContentAsString();
        String dirIdStr = JSON.toString(JSON.toObject(resStr, Map.class).get("data"));
        classId = JSON.toObject(dirIdStr, Long.class);
        int status = mvcResult.getResponse().getStatus();
        assertEquals(200, status);
    }

    @Test
    public void btestQueryById() throws Exception {
        String requestJson = JSONObject.toJSONString(classId);
        ResultActions reaction =
            this.mockMvc.perform(MockMvcRequestBuilders.post("/cmdb/ciClass/queryById").contentType(MediaType.APPLICATION_JSON).content(requestJson).accept(MediaType.APPLICATION_JSON)// 返回值接收json
                .header("Content-Type", "application/json").header("token", token));
        MvcResult mvcResult = reaction.andReturn();
        int status = mvcResult.getResponse().getStatus();
        String resStr = mvcResult.getResponse().getContentAsString();
        String classInfoStr = JSON.toString(JSON.toObject(resStr, Map.class).get("data"));
        CcCiClassInfo classInfo = JSON.toObject(classInfoStr, CcCiClassInfo.class);
        assertEquals("test-test", classInfo.getCiClass().getClassName());
        assertEquals(200, status);
    }

    @Test
    public void ctestImportClassAttr() throws Exception {
        File file = new File("./src/test/resources/testdata/ci.xlsx");
        System.out.println(file.getAbsolutePath());
        MockMultipartFile firstFile = new MockMultipartFile("file", "ci.xlsx", MediaType.TEXT_PLAIN_VALUE, new FileInputStream(file));
        ResultActions reaction =
            mockMvc.perform(MockMvcRequestBuilders.multipart("/cmdb/ciClass/importClassAttr?ciClassIds=" + classId).file(firstFile)).andExpect(MockMvcResultMatchers.status().isOk());
        String resStr = reaction.andReturn().getResponse().getContentAsString();
        // String resultStr = JSONArray.toJSONString(JSON.toObject(resStr, Map.class).get("data"));
        ImportSheetMessage result = JSON.toObject(resStr, ImportSheetMessage.class);
        int status = reaction.andReturn().getResponse().getStatus();
        assertEquals(200, status);
    }

    @Test
    public void dtestGetClassTree() throws Exception {
        ResultActions reaction = this.mockMvc.perform(MockMvcRequestBuilders.post("/cmdb/ciClass/getClassTree").accept(MediaType.MULTIPART_FORM_DATA)// 返回值接收文件
            .header("Content-Type", "application/json").header("token", token));
        MvcResult mvcResult = reaction.andReturn();
        int status = mvcResult.getResponse().getStatus();
        assertEquals(200, status);
    }

    @Test
    public void EtestExportClassAttrExcel() throws Exception {
        ResultActions reaction = this.mockMvc.perform(MockMvcRequestBuilders.post("/cmdb/ciClass/exportClassAttrExcel").accept(MediaType.MULTIPART_FORM_DATA)// 返回值接收文件
            .header("Content-Type", "application/json").header("token", token));
        MvcResult mvcResult = reaction.andReturn();
        int status = mvcResult.getResponse().getStatus();
        assertEquals(200, status);
    }

    @Test
    public void ztestRemoveById() throws Exception {
        String requestJson = JSONObject.toJSONString(classId);
        ResultActions reaction =
            this.mockMvc.perform(MockMvcRequestBuilders.post("/cmdb/ciClass/removeById").contentType(MediaType.APPLICATION_JSON).content(requestJson).accept(MediaType.APPLICATION_JSON)// 返回值接收json
                .header("Content-Type", "application/json").header("token", token));
        MvcResult mvcResult = reaction.andReturn();
        int status = mvcResult.getResponse().getStatus();
        assertEquals(200, status);
    }
}
