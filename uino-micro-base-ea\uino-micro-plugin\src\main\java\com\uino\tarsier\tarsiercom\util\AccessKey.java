package com.uino.tarsier.tarsiercom.util;

import java.text.SimpleDateFormat;
import java.util.Date;

import org.jasypt.encryption.pbe.PooledPBEStringEncryptor;
import org.jasypt.encryption.pbe.config.SimpleStringPBEConfig;


public class AccessKey {
	private static PooledPBEStringEncryptor encryptor = null; 
	private static SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	
	public static String getTimeKey() {
		if(encryptor==null) {
			encryptor = createDefault();
		}
		
		Date nowDate = new Date();
		String formatDate = formatter.format(nowDate);
		
		String encryptDate = encryptor.encrypt(formatDate);
		return encryptDate;
	}

	public static String getTimeKey(Date date) {
		if(encryptor==null) {
			encryptor = createDefault();
		}
		
		String formatDate = formatter.format(date);
		String encryptDate = encryptor.encrypt(formatDate);
		return encryptDate;		
	}
	
	public static Date getDatebyKey(String accessKey) throws Exception {
		if(encryptor==null) {
			encryptor = createDefault();
		}
		
		String formatDate = encryptor.decrypt(accessKey);
	    Date accessDate = formatter.parse(formatDate);
	    
		return accessDate;
	}
	
    private static PooledPBEStringEncryptor createDefault() {
        PooledPBEStringEncryptor encryptor = new PooledPBEStringEncryptor();
        SimpleStringPBEConfig config = new SimpleStringPBEConfig();
        config.setPassword("dix@Uinnova");
        config.setAlgorithm("PBEWithMD5AndDES");
        config.setKeyObtentionIterations("1000");
        config.setPoolSize("1");
        config.setSaltGeneratorClassName("org.jasypt.salt.RandomSaltGenerator");
        config.setStringOutputType("base64");
        encryptor.setConfig(config);
        return encryptor;
    }
}
