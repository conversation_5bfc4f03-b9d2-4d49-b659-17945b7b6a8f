package com.uinnova.product.eam.base.diagram.model;

import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.vmdb.comm.model.image.CcImage;
import lombok.Data;

/**
 * @Classname
 * @Description 我的图形
 * <AUTHOR>
 * @Date 2021-08-24-10:21
 */
@Data
public class ESUserShape extends CcImage implements EntityBean {

    private static final Long serialVersionUID = 1L;

    @Comment("key")
    private String key;

    @Comment("userId")
    private Long userId;

    @Comment("shapeJson")
    private String shapeJson;

}
