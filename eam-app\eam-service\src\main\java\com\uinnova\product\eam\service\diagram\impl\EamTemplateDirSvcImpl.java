package com.uinnova.product.eam.service.diagram.impl;

import com.binary.core.util.BinaryUtils;
import com.uinnova.product.eam.base.diagram.mix.model.ConvertTemplateBean;
import com.uinnova.product.eam.base.diagram.mix.model.TemplateDir;
import com.uinnova.product.eam.base.diagram.mix.model.TemplateDirQuery;
import com.uinnova.product.eam.base.diagram.model.TemDiaRelation;
import com.uinnova.product.eam.base.diagram.model.TemDiaRelationQuery;
import com.uinnova.product.eam.db.diagram.es.EsTemDiaRelationDao;
import com.uinnova.product.eam.db.diagram.es.EsTemplateDirDao;
import com.uinnova.product.eam.service.diagram.IEamTemplateDirSvc;
import com.uino.api.client.permission.IUserApiSvc;
import com.uino.bean.permission.base.SysRole;
import com.uino.bean.permission.business.UserInfo;
import com.uino.dao.util.ESUtil;
import com.uino.util.sys.SysUtil;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.security.SecureRandom;
import java.util.*;

@Deprecated
@Service
public class EamTemplateDirSvcImpl implements IEamTemplateDirSvc {

    @Autowired
    private EsTemplateDirDao templateDirDao;

    @Autowired
    private EsTemDiaRelationDao esTemDiaRelationDao;

    @Value("${http.resource.space}")
    private String httpResouceUrl;

    @Autowired
    private IUserApiSvc userApiSvc;

    @Override
    public Integer convertDiagramAndTemplate(ConvertTemplateBean cBean) {

        if(cBean.getType() == 1) {
            //在进行模板转换时，首先判断模板分类是否还存在
            TemplateDir templateDir = templateDirDao.getById(cBean.getTemplateDirId());
            if(BinaryUtils.isEmpty(templateDir)) {
                //该模板分类已被删除，转换报错
                return 1;
            }

            // 视图与目录关联，先查询视图是否与目录已有关联
            TemDiaRelationQuery temDiaRelation = new TemDiaRelationQuery();
            temDiaRelation.setDiagramId(cBean.getDiagramId());

            List<TemDiaRelation> temDiaRelations = esTemDiaRelationDao.getListByCdt(temDiaRelation);

            if (CollectionUtils.isEmpty(temDiaRelations)) {
                TemDiaRelation newRelation = new TemDiaRelation();
                newRelation.setId(ESUtil.getUUID());
                newRelation.setDiagramId(cBean.getDiagramId());
                newRelation.setTemplateDirId(cBean.getTemplateDirId());

                esTemDiaRelationDao.saveOrUpdate(newRelation);

                /*// 将视图的类型改为3
                VcDiagram vcDiagram = vcDiagramSvc.queryDiagramById(cBean.getDiagramId());
                // 普通用户的视图模板类型为4，管理员为3
                if (this.getCurrentUserType() == 1) {
                    vcDiagram.setDiagramType(3);
                } else {
                    vcDiagram.setDiagramType(4);
                }

                vcDiagram.setTemConvertTime(System.currentTimeMillis());
                vcDiagramSvc.saveOrUpdateDiagramBaseInfo(1L, Collections.singletonList(vcDiagram));
*/
            } else {
                // 将视图的模板信息更新
                TemDiaRelation diaRelation = temDiaRelations.get(0);
                diaRelation.setTemplateDirId(cBean.getTemplateDirId());

                esTemDiaRelationDao.saveOrUpdate(diaRelation);
            }
        } else if (cBean.getType() == 2) {

            TemDiaRelationQuery temDiaRelation = new TemDiaRelationQuery();
            temDiaRelation.setDiagramId(cBean.getDiagramId());

            List<TemDiaRelation> temDiaRelations = esTemDiaRelationDao.getListByCdt(temDiaRelation);

            if (!CollectionUtils.isEmpty(temDiaRelations)) {
                Set<Long> diaIds = new HashSet<>();
                for (TemDiaRelation diaRelation : temDiaRelations) {
                    diaIds.add(diaRelation.getId());
                }

                esTemDiaRelationDao.deleteByIds(diaIds);
            }

            /*// 将视图的类型改为1
            VcDiagram vcDiagram = vcDiagramSvc.queryDiagramById(cBean.getDiagramId());
            vcDiagram.setDiagramType(1);
            vcDiagramSvc.saveOrUpdateDiagramBaseInfo(1L, Collections.singletonList(vcDiagram));*/
        }
        return 0;
    }

    @Override
    public TemplateDir saveOrUpdate(TemplateDir templateDir) {
        if (StringUtils.isEmpty(templateDir.getId())) {
            templateDir.setId(ESUtil.getUUID());
//            templateDir.setViewType(RandomStringUtils.randomAlphanumeric(9));
            templateDir.setViewType(generateRandomString(9));
        }
        templateDirDao.saveOrUpdate(templateDir);
        return templateDir;
    }

    private String generateRandomString(int length) {
        SecureRandom random = new SecureRandom();
        StringBuilder sb = new StringBuilder(length);

        String ALLOWED_CHARACTERS = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
        for (int i = 0; i < length; i++) {
            sb.append(ALLOWED_CHARACTERS.charAt(random.nextInt(ALLOWED_CHARACTERS.length())));
        }

        return sb.toString();
    }

    @Override
    public List<TemplateDir> queryTemplateDirs(Boolean all) {

        // 判断当前登录用户是不是管理员，如果是管理员，则查询类型为1的目录，其他用户查询类型为2的目录

        // Integer opKind = this.getCurrentUserType();

        // TemplateDirQuery query = new TemplateDirQuery();

//        if (!all) {
//            // 如果为管理员用户，则查询管理员模板目录
//            if (opKind == 1) {
//                query.setType(1);
//            } else {
//                query.setType(2);
//            }
//        }

        // 临时去除模板查询逻辑，当前系统所有用户均可查看全部，后期需要产品提供具体原型

        TemplateDirQuery query = new TemplateDirQuery();
        query.setDomainId(SysUtil.getCurrentUserInfo().getDomainId());

        return templateDirDao.getSortListByCdt(query, Collections.singletonList(SortBuilders.fieldSort("modifyTime").order(SortOrder.DESC)));
    }


    private void getAllDiagramIdByDirId(Set<Long> dirIds, Set<Long> diagramIds) {

        TemDiaRelationQuery temDiaRelation = new TemDiaRelationQuery();
        temDiaRelation.setTemplateDirIds(dirIds.toArray(new Long[0]));
        List<TemDiaRelation> diaRelations = esTemDiaRelationDao.getSortListByCdt(
                temDiaRelation, Collections.singletonList(SortBuilders.fieldSort("modifyTime").order(SortOrder.DESC)));

        if (!CollectionUtils.isEmpty(diaRelations)) {
            for (TemDiaRelation diaRelation : diaRelations) {
                diagramIds.add(diaRelation.getDiagramId());
            }
        }

        // 查询子目录
        Set<Long> dirs = new HashSet<>();

        TemplateDirQuery templateDirQuery = new TemplateDirQuery();
        templateDirQuery.setParentIds(dirIds.toArray(new Long[0]));

        List<TemplateDir> listByCdt = templateDirDao.getListByCdt(templateDirQuery);
        if (!CollectionUtils.isEmpty(listByCdt)) {
            for (TemplateDir templateDir : listByCdt) {
                dirs.add(templateDir.getId());
            }
            this.getAllDiagramIdByDirId(dirs, diagramIds);
        }
    }


    /**
     * 获取当前用户的类型
     * @return
     */
    private Integer getCurrentUserType() {

        Long loginId = SysUtil.getCurrentUserInfo().getId();

        UserInfo userInfo = userApiSvc.getUserInfoById(loginId);

        Set<SysRole> roles = userInfo.getRoles();

        int opKind = 2;

        if (!CollectionUtils.isEmpty(roles)) {
            for (SysRole role : roles) {
                String roleName = role.getRoleName();
                if (roleName.equals("admin")) {
                    opKind = 1;
                    break;
                }
            }
        }

        return opKind;
    }

}
