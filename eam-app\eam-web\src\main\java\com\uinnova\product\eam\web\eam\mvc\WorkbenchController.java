package com.uinnova.product.eam.web.eam.mvc;

import com.binary.framework.web.RemoteResult;
import com.uinnova.product.eam.model.ArchitectureCount;
import com.uinnova.product.eam.model.UserArchitectureTreeBO;
import com.uinnova.product.eam.service.WorkbenchSvc;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/eam/workbench")
public class WorkbenchController {

    @Autowired
    private WorkbenchSvc workbenchSvc;

    /**
     * @param type  1表示方视图创建位置 2表示方案创建位置
     * @return
     */
    @GetMapping("/getUserArchitectureTree")
    public RemoteResult getUserArchitectureTree(@RequestParam Integer type) {
        List<UserArchitectureTreeBO> result = workbenchSvc.getUserArchitectureTree(type);
        return new RemoteResult(result);
    }

    /**
     * 获取架构视图，方案统计信息
     * @return
     */
    @GetMapping("/getArchitectureCount")
    public RemoteResult getArchitectureCount() {
        ArchitectureCount architectureCount = workbenchSvc.getArchitectureCount();
        return new RemoteResult(architectureCount);
    }

}
