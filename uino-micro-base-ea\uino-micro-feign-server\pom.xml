<?xml version="1.0"?>
<project
		xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd"
		xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.uino</groupId>
		<artifactId>eam-base</artifactId>
		<version>1.0.0-SNAPSHOT</version>
	</parent>
	<artifactId>uino-eam-micro-feign-server</artifactId>
	<name>uino-eam-micro-feign-server</name>
	<url>http://maven.apache.org</url>
	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
	</properties>

	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-starter-logging</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.yaml</groupId>
					<artifactId>snakeyaml</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.uino</groupId>
			<artifactId>uino-eam-micro-comm</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>com.uino</groupId>
			<artifactId>uino-eam-micro-feign-client</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>com.uino</groupId>
			<artifactId>uino-eam-micro-service</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>com.uino</groupId>
			<artifactId>uino-eam-micro-dao</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>com.uino</groupId>
			<artifactId>uino-eam-micro-monitor</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>com.uino</groupId>
			<artifactId>uino-eam-micro-util</artifactId>
			<version>${project.version}</version>
		</dependency>
	</dependencies>

	<build>
		<finalName>${project.artifactId}</finalName>
		<plugins>
			<plugin>
				<groupId>org.jacoco</groupId>
				<artifactId>jacoco-maven-plugin</artifactId>
				<version>0.8.2</version>
				<executions>
					<execution>
						<id>prepare-agent</id>
						<goals>
							<goal>prepare-agent</goal>
						</goals>
					</execution>
					<execution>
						<id>report</id>
						<phase>prepare-package</phase>
						<goals>
							<goal>report</goal>
						</goals>
					</execution>
					<execution>
						<id>post-unit-test</id>
						<phase>test</phase>
						<goals>
							<goal>report</goal>
						</goals>
						<configuration>
							<outputDirectory>target/coverage</outputDirectory>
						</configuration>
					</execution>
				</executions>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<version>2.22.2</version><!--$NO-MVN-MAN-VER$ -->
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<source>17</source>
					<target>17</target>
					<encoding>UTF-8</encoding>
					<compilerArguments>
						<extdirs>${project.basedir}/lib</extdirs>
					</compilerArguments>
				</configuration>
			</plugin>

<!--			<plugin>-->
<!--				<artifactId>maven-assembly-plugin</artifactId>-->
<!--				<executions>-->
<!--					<execution>-->
<!--						<id>pack-center</id>-->
<!--						<phase>package</phase>-->
<!--						<goals>-->
<!--							<goal>single</goal>-->
<!--						</goals>-->
<!--						<configuration>-->
<!--							<descriptors>-->
<!--								<descriptor>/pack-war2tar.xml</descriptor>-->
<!--							</descriptors>-->
<!--						</configuration>-->
<!--					</execution>-->
<!--				</executions>-->
<!--			</plugin>-->
		</plugins>
	</build>
</project>
