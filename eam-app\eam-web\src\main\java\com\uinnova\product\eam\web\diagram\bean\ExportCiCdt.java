package com.uinnova.product.eam.web.diagram.bean;

import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;

public class ExportCiCdt implements Condition {

	private static final long serialVersionUID = 1L;
	
	@Comment("下载指定的分类数据,与ciIds不应该同时出现.")
	private Long[] ciClassIds;
	
	@Comment("与分类ID配套使用,0时只下载模板,1时下载数据")
	private Integer hasData;

	@Comment("下载指定的CI,使用该参数,ciClassIds和hashData 都无效")
	private Long[] ciIds;
	
	public Long[] getCiClassIds() {
		return ciClassIds;
	}

	public void setCiClassIds(Long[] ciClassIds) {
		this.ciClassIds = ciClassIds;
	}

	public Long[] getCiIds() {
		return ciIds;
	}

	public void setCiIds(Long[] ciIds) {
		this.ciIds = ciIds;
	}

	public Integer getHasData() {
		return hasData;
	}

	public void setHasData(Integer hasData) {
		this.hasData = hasData;
	}

 
 
}
