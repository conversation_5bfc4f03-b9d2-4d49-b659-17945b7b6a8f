package com.uino.provider.feign.cmdb;

import com.alibaba.fastjson.JSONObject;
import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.provider.rlt.bean.CcCiRltInfo;
import com.uino.bean.cmdb.base.ESCIRltInfo;
import com.uino.bean.cmdb.base.ESCIRltInfoHistory;
import com.uino.bean.cmdb.business.BindCiRltRequestDto;
import com.uino.bean.cmdb.business.DataModuleRltClassDto;
import com.uino.bean.cmdb.business.ImportExcelMessage;
import com.uino.bean.cmdb.business.ImportResultMessage;
import com.uino.bean.cmdb.query.ESAttrAggBean;
import com.uino.bean.cmdb.query.ESRltSearchBean;
import com.uino.provider.feign.config.BaseFeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * ci关系feign定义
 * 
 * <AUTHOR>
 *
 */
@Component
@FeignClient(name = "${feign-server-name:tarsier-platform}", path = "${feign-server-path:tarsier-platform}/feign/ciRlt", configuration = {
        BaseFeignConfig.class})
public interface CIRltFeign {

    /**
     * 绑定ci关系
     * @param bindCiRltRequestDto
     * @return
     */
    @PostMapping("bindCiRlt")
    public Long bindCiRlt(@RequestBody BindCiRltRequestDto bindCiRltRequestDto);

    @PostMapping("bindCiRltsReqJson")
    public ImportResultMessage bindCiRlts(@RequestParam(value = "domainId") Long domainId,@RequestBody JSONObject jsonObject);

    /**
     * 根据ciId解除ci关系（无论是目标还是源都解除）
     * 
     * @param ciId
     *            源/目标ciId
     * @return
     */
    @PostMapping("delRltByCiId")
    public Integer delRltByCiId(@RequestBody(required = false) Long ciId);

    /**
     * 修改ci关系信息(只能修改属性信息)
     * 
     * @param ciRltId
     *            ci关系Id
     * @param attrs
     *            属性信息
     * @return
     */
    @PostMapping("updateCiRltAttr")
    public Long updateCiRltAttr(@RequestParam(value = "ciRltId", required = false) Long ciRltId,
            @RequestBody(required = false) Map<String, String> attrs);

    /**
     * 分页查询ci关系（带条件）
     * 
     * @param bean
     * @return
     */
    @PostMapping("searchRltByBean")
    public Page<CcCiRltInfo> searchRltByBean(@RequestBody(required = false) ESRltSearchBean bean);

    /**
     * 清除某个关系分类下所有ci关系
     * 
     * @param rltClassId
     * @return
     */
    @PostMapping("clearRltByClassId")
    public Integer clearRltByClassId(@RequestBody(required = false) Long rltClassId);

    /**
     * 导入ci关系
     * 
     * @param excelFilePath
     * @param excelFile
     * @param rltClsCodes
     * @return
     */
    @PostMapping("importCiRlt")
    public ImportResultMessage importCiRlt(@RequestParam(value = "domainId", required = false) Long domainId,
            @RequestParam(value = "excelFilePath", required = false) String excelFilePath,
            @RequestParam(value = "file") MultipartFile excelFile, @RequestBody Set<String> rltClsCodes);

    /**
     * 导出指定关系分类下关系
     * 
     * @param
     * @param
     * @return
     */
    @PostMapping("exportCiRlt")
    public ResponseEntity<byte[]> exportCiRlt(@RequestBody(required = false) String req);

    /**
     * 根据【关系ids OR 关系codes】解除ci关系
     * 
     * @param rltIds
     * @param rltCodes
     * @return
     */
    @GetMapping("delRltByIdsOrRltCodes")
    public Integer delRltByIdsOrRltCodes(@RequestParam(required = false, value = "rltIds") Set<Long> rltIds,
            @RequestParam(required = false, value = "rltCodes") Set<String> rltCodes);

    /**
     * 解释关系excel
     * 
     * @param excelFile
     * @return
     */
    @PostMapping("comprehendRltExcel")
    public Map<String, Boolean> comprehendRltExcel(@RequestParam(value = "file") MultipartFile excelFile);

    /**
     * 解析关系excel
     * 
     * @param excelFilePath
     * @param excelFile
     * @return
     */
    @PostMapping("parseRltExcel")
    public ImportExcelMessage parseRltExcel(
            @RequestParam(value = "excelFilePath", required = false) String excelFilePath,
            @RequestPart(value = "file") MultipartFile excelFile);

    @PostMapping("bindCiRlts")
    public ImportResultMessage bindCiRlts(@RequestParam(value = "domainId") Long domainId, @RequestBody(required = false) Set<BindCiRltRequestDto> bindRltDtos);

    @PostMapping("searchRltByIds")
    public List<CcCiRltInfo> searchRltByIds(@RequestBody(required = false) Set<Long> ids);

    @PostMapping("searchRlt")
    public Page<ESCIRltInfo> searchRlt(@RequestBody(required = false) ESRltSearchBean bean);

    @PostMapping("groupByField")
    public Page<String> groupByField(@RequestParam(value = "domainId") Long domainId, @RequestBody(required = false) ESAttrAggBean req);

    /**
     * 根据分类筛选条件获取分类关系map
     * 
     * @param clsIds
     *            ci分类ids
     * @param rltClsIds
     *            关系分类ids
     * @return {源分类id:{关系分类id:[目标分类ids]}}
     */
    @PostMapping("getClassRltMapByClsQuery")
    public Map<Long, Map<Long, Set<Long>>> getClassRltMapByClsQuery(@RequestBody JSONObject req);
    // public Map<Long, Map<Long, Set<Long>>> getClassRltMapByClsQuery(Set<Long>
    // clsIds, Set<Long> rltClsIds);

    /**
     * 根据分类筛选条件获取分类关系map
     *
     * @param classIds ci分类ids
     * @param rltClsIds 关系分类ids
     * @param libType 库类型（私有/设计/运行）
     * @return 元模型上使用包含起始分类和目标分类的关系对象信息
     */
    @PostMapping("getClassRltList")
    List<DataModuleRltClassDto> getClassRltList(@RequestBody Set<Long> classIds, @RequestParam(value = "rltClsIds", required = false) Set<Long> rltClsIds);

    /**
     * 获取关系的历史信息
     *
     * @param rltIds 关系id
     * @param hasCurrent 是否包含当前版本
     * @return 关系历史信息
     */
    @PostMapping("getRltsHistrysDict")
    Map<Long, List<ESCIRltInfoHistory>> getRltsHistrysDict(@RequestParam(required = false, value = "rltIds") Set<Long> rltIds,
                                                           @RequestParam(required = false, value = "hasCurrent") boolean hasCurrent
    );

    /**
     * 获取{rltId：maxVersion}键值对
     *
     * @param rltIds
     *            if null:获取全部rltid：maxversion键值对 ，if notnull:仅获取指定rltid
     * @return {rltid：maxVersion}
     */
    @PostMapping("getRltIdMaxVersion")
    Map<Long, Long> getRltIdMaxVersion(@RequestParam(required = false, value = "rltIds") Set<Long> rltIds);



}
