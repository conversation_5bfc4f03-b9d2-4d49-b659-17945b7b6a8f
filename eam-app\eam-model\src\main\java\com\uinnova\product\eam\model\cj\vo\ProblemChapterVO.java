package com.uinnova.product.eam.model.cj.vo;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 审批 - 校验方案
 * @author: Lc
 * @create: 2022-04-13 15:39
 */
@Data
public class ProblemChapterVO implements Serializable{

    /** 方案id */
    private Long planId;

    /** 章节id */
    private Long chapterId;
    /** 章节名称 */
    private String chapterName;

    /** 章节序号 */
    private String serialNum;

    /**  章节层级 */
    private Integer level;

    /** 序号 */
    private Double orderId;

    /** 问题模块列表 */
    private List<ProblemModuleVO> problemModuleList;

    /** 问题视图列表 */
    private List<ProblemDiagramVO> problemDiagramList;
}
