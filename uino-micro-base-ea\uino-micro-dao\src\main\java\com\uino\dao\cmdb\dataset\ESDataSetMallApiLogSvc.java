package com.uino.dao.cmdb.dataset;

import com.alibaba.fastjson.JSONObject;
import com.uino.dao.AbstractESBaseDao;
import com.uino.bean.cmdb.base.dataset.log.DataSetMallApiLog;
import com.uino.dao.ESConst;
import org.springframework.stereotype.Repository;

import jakarta.annotation.PostConstruct;

/**
 * @Classname DataSetMallApiLogDao
 * @Description TODO
 * @Date 2020/3/19 10:07
 * @Created by sh
 */
@Repository
public class ESDataSetMallApiLogSvc extends AbstractESBaseDao<DataSetMallApiLog, JSONObject> {

    @Override
    public String getIndex() {
        return ESConst.INDEX_CMDB_DATASET_MALLAPI_LOG;
    }

    @Override
    public String getType() {
        return ESConst.INDEX_CMDB_DATASET_MALLAPI_LOG;
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }
}

