package com.uinnova.product.eam.feign.client;

import com.binary.jdbc.Page;
import com.uinnova.product.eam.base.model.PageDTO;
import com.uinnova.product.eam.base.model.PaySoftwareMeta;
import com.uinnova.product.eam.feign.FeignConst;
import com.uinnova.product.eam.model.asset.*;
import com.uino.bean.cmdb.base.ESCIInfo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@FeignClient(name = FeignConst.APPLICATION,path = FeignConst.SOFTWARE_PATH)
public interface IEamSoftwareClient {
    List<SoftwareMasterDTO> select(SearchSoftwareDTO searchSoftwareDTO);

    Long saveSoftware(SoftwareMasterDTO copy);

    Boolean checkSoftwareName(SoftwareCheckDTO softwareCheckDTO);

    SoftwareMasterDTO selectById(SoftwarePrimaryKeyDTO keyDTO);

    SoftwareMasterDTO selectSoftwareEditionById(SoftwarePrimaryKeyDTO keyDTO);

    Page<SoftwareMasterDTO> selectPage(PageDTO<SearchSoftwareDTO> page);

    boolean softwareNameisExist(Long ciClassId, String systemName);

    Long getTotalNum(SearchSoftwareDTO searchSoftwareDTO);

    SoftwareMasterEleDTO selectAttrConf(PaySoftwareMeta paySoftwareMeta);

    List<ESCIInfo> getSoftwareByLoginCode(String loginCode);
}
