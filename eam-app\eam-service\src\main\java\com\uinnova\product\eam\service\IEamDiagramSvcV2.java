package com.uinnova.product.eam.service;

import com.uinnova.product.eam.base.diagram.model.ESDiagram;
import com.uinnova.product.eam.base.diagram.model.ESDiagramInfoDTO;
import com.uinnova.product.eam.base.model.BaseQueryDiagramDto;
import com.uinnova.product.eam.model.DiagramBo;
import com.uinnova.product.eam.model.EamCategoryCdt;
import com.uinnova.product.eam.model.diagram.SpaceResourceResultInfo;
import com.uinnova.product.eam.model.dto.EamResourceDetail;

import java.util.List;
import java.util.Map;

/**
 * eam视图相关接口
 */
public interface IEamDiagramSvcV2 {
    /**
     * 通过目录id获取视图、系统、方案等资源信息
     * @param dto 查询参数
     * @return 视图、系统、方案信息
     */
    SpaceResourceResultInfo getDiagramByDirId(BaseQueryDiagramDto dto);

    /**
     * 删除画布ci
     * @param cdt ciCode、视图id
     * @return 删除结果
     */
    Integer removeCiInDiagram(EamCategoryCdt cdt);

    /**
     * 文件夹、视图or方案详情
     * @param cdt 文件夹、视图or方案id
     * @return 详情信息
     */
    EamResourceDetail detail(EamCategoryCdt cdt);

    /**
     * 新建空白视图
     * @param diagramDto 视图信息
     * @return 视图id
     */
    Map<String, String> createDiagram(ESDiagramInfoDTO diagramDto);

    /**
     * 新建视图-带关系、节点
     * @param diagramDto 视图信息
     * @return 视图id
     */
    Map<String, String> createDiagramWithData(ESDiagramInfoDTO diagramDto);

    /**
     * 右键打开下级模型视图
     * @param params 视图id，node节点ciCode，分库信息
     * @return 视图全量信息
     */
    ESDiagramInfoDTO openNextDiagram(EamCategoryCdt params);

    /**
     * 校验文件夹中是否存在视图
     * @param categoryIdList
     * @return
     */
    boolean checkDirExistData(List<Long> categoryIdList);

    /**
     * 全景墙获取发布视图
     * <AUTHOR>
     * @return
     */
    DiagramBo getPublishDiagramList(String like , Integer pageNum, Integer pageSize);

    List<ESDiagram> getSysLinkDigramByCiCode(String ciCode);
}
