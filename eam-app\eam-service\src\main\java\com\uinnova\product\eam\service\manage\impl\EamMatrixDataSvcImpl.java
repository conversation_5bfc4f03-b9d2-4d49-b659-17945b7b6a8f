package com.uinnova.product.eam.service.manage.impl;

import com.binary.core.util.BinaryUtils;
import com.binary.jdbc.Page;
import com.uinnova.product.eam.comm.dto.EamMatrixQueryVO;
import com.uinnova.product.eam.comm.model.es.EamMatrixInstanceData;
import com.uinnova.product.eam.comm.model.es.EamMatrixTableCase;
import com.uinnova.product.eam.comm.model.es.EamMatrixInstance;
import com.uinnova.product.eam.comm.model.es.EamMatrixStencil;
import com.uinnova.product.eam.model.bm.EamMergeParams;
import com.uinnova.product.eam.service.ICISwitchSvc;
import com.uinnova.product.eam.service.IEamCIClassApiSvc;
import com.uinnova.product.eam.service.es.EamMatrixDataDesignDao;
import com.uinnova.product.eam.service.es.EamMatrixDataPrivateDao;
import com.uinnova.product.eam.service.impl.IamsCIRltSwitchSvc;
import com.uinnova.product.eam.service.manage.EamMatrixDataSvc;
import com.uinnova.product.eam.service.manage.EamMatrixInstanceSvc;
import com.uinnova.product.eam.service.manage.EamMatrixStencilSvc;
import com.uinnova.product.eam.service.merge.impl.EamCiRltMerge;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.comm.model.rlt.CcCiRlt;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uino.api.client.cmdb.IRltClassApiSvc;
import com.uino.bean.cmdb.base.ESCIClassInfo;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.base.ESCIRltInfo;
import com.uino.bean.cmdb.base.LibType;
import com.uino.bean.cmdb.business.BindCiRltRequestDto;
import com.uino.dao.AbstractESBaseDao;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 矩阵表格数据接口实现
 * <AUTHOR>
 */
@Slf4j
@Service
public class EamMatrixDataSvcImpl implements EamMatrixDataSvc {

    @Autowired
    private EamMatrixDataPrivateDao dataPrivateDao;

    @Autowired
    private EamMatrixDataDesignDao dataDesignDao;

    @Autowired
    private EamMatrixStencilSvc stencilSvc;

    @Autowired
    private EamMatrixInstanceSvc instanceSvc;

    @Resource
    private ICISwitchSvc ciSwitchSvc;

    @Resource
    private IamsCIRltSwitchSvc rltSwitchSvc;

    @Resource
    private IEamCIClassApiSvc ciClassApiSvc;

    @Resource
    private IRltClassApiSvc rltClassApiSvc;

    @Autowired
    private EamCiRltMerge ciRltMerge;

    @Override
    public EamMatrixInstanceData getById(Long id, LibType libType) {
        return getPriOrDesDao(libType).getById(id);
    }

    @Override
    public Integer deleteByTableId(Long tableId, LibType libType) {
        if(LibType.PRIVATE.equals(libType)){
            return dataPrivateDao.deleteByQuery(QueryBuilders.termQuery("tableId", tableId), true);
        } else {
            return dataDesignDao.deleteByQuery(QueryBuilders.termQuery("tableId", tableId), true);
        }
    }

    @Override
    public void updateTableInfo(EamMatrixInstance instance, List<EamMatrixInstanceData> table, EamMatrixStencil stencil, String ownerCode){
        if (CollectionUtils.isEmpty(table)) {
            this.deleteByTableId(instance.getId(), LibType.PRIVATE);
            return;
        }
        Set<String> sourceCodes = new HashSet<>();
        Set<String> targetCodes = new HashSet<>();
        Set<String> ciCodes = new HashSet<>();
        Set<String> rltCodes = new HashSet<>();
        for (EamMatrixInstanceData each : table) {
            if(!BinaryUtils.isEmpty(each.getSourceCiCode())){
                ciCodes.add(each.getSourceCiCode());
                sourceCodes.add(each.getSourceCiCode());
            }
            if(!BinaryUtils.isEmpty(each.getTargetCiCode())){
                ciCodes.add(each.getTargetCiCode());
                targetCodes.add(each.getTargetCiCode());
            }
            if(!BinaryUtils.isEmpty(each.getRltCode())){
                rltCodes.add(each.getSourceCiCode() + "_" + stencil.getRltClass() + "_" + each.getTargetCiCode());
            }
        }
        //检出ci及关系
        this.pullCiRlt(ciCodes, rltCodes, ownerCode);
        List<ESCIInfo> sourceCiList = ciSwitchSvc.getCiByCodes(new ArrayList<>(sourceCodes), ownerCode, LibType.PRIVATE);
        Assert.notEmpty(sourceCiList, "未查询到源端ci数据!");
        Map<String, ESCIInfo> sourceCiMap = sourceCiList.stream().collect(Collectors.toMap(ESCIInfo::getCiCode, e -> e, (k1, k2) -> k2));

        List<ESCIInfo> targetCiList = ciSwitchSvc.getCiByCodes(new ArrayList<>(targetCodes), ownerCode, LibType.PRIVATE);
        Assert.notEmpty(targetCiList, "未查询到目标端端ci数据!");
        Map<String, ESCIInfo> targetCiMap = targetCiList.stream().collect(Collectors.toMap(ESCIInfo::getCiCode, e -> e, (k1, k2) -> k2));

        List<ESCIRltInfo> rltList = rltSwitchSvc.getRltByRltCodes(rltCodes, ownerCode, LibType.PRIVATE);
        Map<String, ESCIRltInfo> rltMap = rltList.stream().collect(Collectors.toMap(CcCiRlt::getCiCode, e -> e, (k1, k2) -> k2));
        CcCiClassInfo rltClass = rltClassApiSvc.getRltClassById(stencil.getRltClass());
        Map<Long, String> defMap = rltClass.getAttrDefs().stream().collect(Collectors.toMap(CcCiAttrDef::getId, CcCiAttrDef::getProName, (v1, v2) -> v2));

        List<BindCiRltRequestDto> bindList = new ArrayList<>();
        Long rltClassId = stencil.getRltClass();
        for (EamMatrixInstanceData each : table) {
            each.setTableId(instance.getId());
            ESCIInfo sourceCi = sourceCiMap.get(each.getSourceCiCode());
            Assert.notNull(sourceCi, "未查询到ci数据:" + each.getSourceCiCode());
            ESCIInfo targetCi = targetCiMap.get(each.getTargetCiCode());
            Assert.notNull(targetCi, "未查询到ci数据:" + each.getTargetCiCode());

            ESCIRltInfo rltInfo = rltMap.get(each.getRltCode());
            Map<String, String> rltAttrs = this.updateAttrs(rltInfo, each.getData(), defMap);
            each.setRltCode(each.getSourceCiCode() + "_" + rltClassId + "_" + each.getTargetCiCode());
            BindCiRltRequestDto bindRlt = BindCiRltRequestDto.builder().ownerCode(ownerCode).repetitionError(false)
                    .rltClassId(rltClassId).sourceCiId(sourceCi.getId()).targetCiId(targetCi.getId()).attrs(rltAttrs).build();
            bindList.add(bindRlt);
            //设计阶段不保存具体属性数据
            each.setData(null);
        }
        rltSwitchSvc.bindCiRltBatch(bindList, LibType.PRIVATE);
        this.deleteByTableId(instance.getId(), LibType.PRIVATE);
        dataPrivateDao.saveOrUpdateBatch(table);
    }

    /**
     * 检出ci及关系
     */
    private void pullCiRlt(Set<String> ciCodes, Set<String> rltCodes, String ownerCode) {
        if(CollectionUtils.isEmpty(ciCodes)){
            return;
        }
        List<ESCIInfo> ciList = ciSwitchSvc.getCiByCodes(new ArrayList<>(ciCodes), null, LibType.DESIGN);
        if(CollectionUtils.isEmpty(ciList)){
            return;
        }
        EamMergeParams merge = new EamMergeParams();
        merge.setMergeCiList(ciList);
        merge.setOwnerCode(ownerCode);
        if(!CollectionUtils.isEmpty(rltCodes)){
            List<ESCIRltInfo> rltList = rltSwitchSvc.getRltByRltCodes(rltCodes, null, LibType.DESIGN);
            merge.setMergeRltList(rltList);
        }
        ciRltMerge.pull(merge);
    }

    @Override
    public List<EamMatrixInstanceData> selectByTableIds(List<Long> tableIds, LibType libType) {
        return getPriOrDesDao(libType).getListByQueryScroll(QueryBuilders.termsQuery("tableId", tableIds));
    }

    @Override
    public Integer saveOrUpdateMatrixTable(List<EamMatrixInstanceData> matrixTable, LibType libType) {
        return getPriOrDesDao(libType).saveOrUpdateBatch(matrixTable);
    }

    @Override
    public void deleteBatchTableDataByIds(List<Long> matrixTableIds, LibType libType) {
        getPriOrDesDao(libType).deleteByIds(matrixTableIds);
    }

    /**
     * 根据表格数据更新rlt属性
     */
    private Map<String, String> updateAttrs(ESCIRltInfo rlt, List<EamMatrixTableCase> data, Map<Long, String> defMap){
        Map<String, String> attrs;
        if(rlt == null || CollectionUtils.isEmpty(rlt.getAttrs())){
            attrs = new HashMap<>();
        }else{
            attrs = rlt.getAttrs();
        }
        if(CollectionUtils.isEmpty(data)){
            return attrs;
        }
        for (EamMatrixTableCase each : data) {
            String proName = defMap.get(each.getId());
            if(BinaryUtils.isEmpty(proName)){
                log.error("未查询到对象属性信息'{}'!", each.getId());
                continue;
            }
            attrs.put(proName, each.getValue());
        }
        return attrs;
    }

    @Override
    public List<EamMatrixInstanceData> getTableData(Long tableId, Set<String> ciCodes, LibType libType) {
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        if(!BinaryUtils.isEmpty(tableId)){
            query.must(QueryBuilders.termQuery("tableId", tableId));
        }
        if(!CollectionUtils.isEmpty(ciCodes)){
            BoolQueryBuilder should = QueryBuilders.boolQuery();
            should.should(QueryBuilders.termsQuery("sourceCiCode.keyword", ciCodes));
            should.should(QueryBuilders.termsQuery("targetCiCode.keyword", ciCodes));
            query.must(should);
        }
        List<EamMatrixInstanceData> result = getPriOrDesDao(libType).getListByQueryScroll(query);
        if(CollectionUtils.isEmpty(result)){
            return new ArrayList<>();
        }
        return result;
    }

    @Override
    public List<EamMatrixInstanceData> getTableData(Long tableId, LibType libType) {
        List<EamMatrixInstanceData> result;
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("tableId", tableId));
        if(LibType.PRIVATE.equals(libType)){
            result = dataPrivateDao.getListByQueryScroll(query);
        }else{
            result = dataDesignDao.getListByQueryScroll(query);
        }
        if(CollectionUtils.isEmpty(result)){
            return new ArrayList<>();
        }
        if(LibType.PRIVATE.equals(libType)){
            EamMatrixInstance instance = instanceSvc.getBaseInfoById(tableId, LibType.PRIVATE);
            Assert.notNull(instance, "未查询到矩阵表格："+tableId);
            this.setTableData(result, instance.getMatrixId(), instance.getOwnerCode(), LibType.PRIVATE);
        }
        return result;
    }

    @Override
    public Page<EamMatrixInstanceData> getTableData(EamMatrixQueryVO queryVO) {
        Page<EamMatrixInstanceData> result;
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("tableId", queryVO.getId()));
        if(LibType.PRIVATE.equals(queryVO.getLibType())){
            result = dataPrivateDao.getSortListByQuery(queryVO.getPageNum(), queryVO.getPageSize(), query, "num", true);
        }else{
            result = dataDesignDao.getSortListByQuery(queryVO.getPageNum(), queryVO.getPageSize(), query, "num", true);
        }
        if(CollectionUtils.isEmpty(result.getData())){
            return result;
        }
        if(LibType.PRIVATE.equals(queryVO.getLibType())){
            EamMatrixInstance instance = instanceSvc.getBaseInfoById(queryVO.getId(), LibType.PRIVATE);
            Assert.notNull(instance, "未查询到矩阵表格："+queryVO.getId());
            this.setTableData(result.getData(), instance.getMatrixId(), instance.getOwnerCode(), LibType.PRIVATE);
        }
        return result;
    }

    @Override
    public void setTableData(List<EamMatrixInstanceData> tableData, Long matrixId, String ownerCode, LibType libType){
        EamMatrixStencil stencil = stencilSvc.getById(matrixId);
        Assert.notNull(stencil, "未查询到矩阵：" + matrixId);

        Set<String> sourceCodes = new HashSet<>();
        Set<String> targetCodes = new HashSet<>();
        Set<String> rltCodes = new HashSet<>();
        for (EamMatrixInstanceData each : tableData) {
            sourceCodes.add(each.getSourceCiCode());
            targetCodes.add(each.getTargetCiCode());
            rltCodes.add(each.getRltCode());
        }
        List<ESCIInfo> sourceCiList = ciSwitchSvc.getCiByCodes(new ArrayList<>(sourceCodes), ownerCode, libType);
        Map<String, ESCIInfo> sourceCiMap = sourceCiList.stream().collect(Collectors.toMap(ESCIInfo::getCiCode, e -> e, (k1, k2) -> k2));
        List<ESCIInfo> targetCiList = ciSwitchSvc.getCiByCodes(new ArrayList<>(targetCodes), ownerCode, libType);
        Map<String, ESCIInfo> targetCiMap = targetCiList.stream().collect(Collectors.toMap(ESCIInfo::getCiCode, e -> e, (k1, k2) -> k2));

        ESCIClassInfo sourceClass = ciClassApiSvc.queryClassById(stencil.getSourceClass());
        Assert.notNull(stencil, "未查询到分类：" + stencil.getSourceClass());
        Map<Long, String> sourceDefMap = sourceClass.getAttrDefs().stream().collect(Collectors.toMap(CcCiAttrDef::getId, CcCiAttrDef::getProName, (v1, v2) -> v2));
        ESCIClassInfo targetClass = ciClassApiSvc.queryClassById(stencil.getTargetClass());
        Assert.notNull(stencil, "未查询到分类：" + stencil.getTargetClass());
        Map<Long, String> targetDefMap = targetClass.getAttrDefs().stream().collect(Collectors.toMap(CcCiAttrDef::getId, CcCiAttrDef::getProName, (v1, v2) -> v2));

        List<ESCIRltInfo> rltList = rltSwitchSvc.getRltByRltCodes(rltCodes, ownerCode, libType);
        Map<String, ESCIRltInfo> rltMap = rltList.stream().collect(Collectors.toMap(CcCiRlt::getCiCode, e -> e, (k1, k2) -> k2));
        CcCiClassInfo rltClass = rltClassApiSvc.getRltClassById(stencil.getRltClass());
        Assert.notNull(stencil, "未查询到关系：" + stencil.getRltClass());
        Map<Long, String>  rltDefMap = rltClass.getAttrDefs().stream().collect(Collectors.toMap(CcCiAttrDef::getId, CcCiAttrDef::getProName, (v1, v2) -> v2));
//        EamMatrixTableCase rltCase = new EamMatrixTableCase(rltClass.getCiClass().getId(), null, rltClass.getCiClass().getClassName(), rltClass.getCiClass().getId());
        for (EamMatrixInstanceData each : tableData) {
            List<EamMatrixTableCase> data = new ArrayList<>();
            ESCIInfo sourceCi = sourceCiMap.get(each.getSourceCiCode());
            if(sourceCi != null){
                for (Long defId : stencil.getSourceAttrs()) {
                    String proName = sourceDefMap.get(defId);
                    Object value = sourceCi.getAttrs().get(proName);
                    String defValue = value == null ? "" : value.toString();
                    data.add(new EamMatrixTableCase(defId, each.getSourceCiCode(), defValue, sourceCi.getClassId()));
                }
                each.setSourceVersion(sourceCi.getPublicVersion());
            }
            ESCIInfo targetCi = targetCiMap.get(each.getTargetCiCode());
            if(targetCi != null){
                for (Long defId : stencil.getTargetAttrs()) {
                    String proName = targetDefMap.get(defId);
                    Object value = targetCi.getAttrs().get(proName);
                    String defValue = value == null ? "" : value.toString();
                    data.add(new EamMatrixTableCase(defId, each.getTargetCiCode(), defValue, targetCi.getClassId()));
                }
                each.setTargetVersion(targetCi.getPublicVersion());
            }
            //添加关系分类名展示
//            data.add(rltCase);
            ESCIRltInfo rltInfo = rltMap.get(each.getRltCode());
            if(rltInfo != null){
                for (Long defId : stencil.getRltAttrs()) {
                    String proName = rltDefMap.get(defId);
                    Object value = rltInfo.getAttrs().get(proName);
                    String defValue = value == null ? "" : value.toString();
                    data.add(new EamMatrixTableCase(defId, each.getRltCode(), defValue, rltInfo.getClassId()));
                }
                each.setRltVersion(rltInfo.getPublicVersion());
            }
            each.setData(data);
        }
    }
    private AbstractESBaseDao<EamMatrixInstanceData, EamMatrixInstanceData> getPriOrDesDao(LibType libType){
        if (LibType.PRIVATE.equals(libType)){
            return dataPrivateDao;
        }else {
            return dataDesignDao;
        }
    }


}
