package com.uinnova.product.eam.base.diagram.utils;

import com.binary.core.bean.BMProxy;
import com.binary.jdbc.Page;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.function.BiConsumer;
import java.util.function.Consumer;

public abstract class DiagramUtil {

	
	/**
	 * 复制对象
	 * 
	 * @param obj
	 *            待复制对象
	 * @param toType
	 *            目标类型
	 * @return
	 */
	public static <T> T copy(Object obj, Class<T> toType) {
		BMProxy<T> proxy = BMProxy.getInstance(toType);
		T t = proxy.newInstance();
		proxy.copyFrom(obj);
		return t;
	}
	
	/**
	 * 复制列表信息 
	 * @param datas
	 * @param toType
	 * @return
	 */
	public static <T> List<T> copy(List<?> datas, Class<T> toType) {
		if (datas == null) {
			return Collections.emptyList();
		}

		List<T> retData = new ArrayList<T>();
		BMProxy<T> proxy = BMProxy.getInstance(toType);
		for (Object obj : datas) {
			T t = proxy.newInstance();
			proxy.copyFrom(obj);
			retData.add(t);
		}
		return retData;
	}

	/**
	 * 复制列表信息
	 * @param datas
	 * @param toType
	 * @return
	 */
	public static <T> List<T> copy(List<?> datas, Class<T> toType, Consumer<T> function) {
		if (datas == null) {
			return Collections.emptyList();
		}

		List<T> retData = new ArrayList<T>();
		BMProxy<T> proxy = BMProxy.getInstance(toType);
		for (Object obj : datas) {
			T t = proxy.newInstance();
			proxy.copyFrom(obj);
			function.accept(t);
			retData.add(t);
		}
		return retData;
	}

	/**
	 * 复制列表信息
	 * @param datas
	 * @param toType
	 * @return
	 */
	public static <E,T> List<T> copy(List<E> datas, Class<T> toType, BiConsumer<? super E, ? super T> action) {
		if (datas == null) {
			return Collections.emptyList();
		}

		List<T> retData = new ArrayList<T>();
		BMProxy<T> proxy = BMProxy.getInstance(toType);
		for (E obj : datas) {
			T t = proxy.newInstance();
			proxy.copyFrom(obj);
			action.accept(obj, t);
			retData.add(t);
		}
		return retData;
	}


	
	/**
	 * 复制分页信息
	 * @param pageData
	 * @param toType
	 * @return
	 */
	public static <T> Page<T> copy(Page<?> pageData, Class<T> toType) {
		if (pageData == null) {
			return null;
		}

		Page<T> ret = new Page<>();
		List<?> data = pageData.getData();
		
		if(data != null){
			List<T> retData = new ArrayList<T>();
			BMProxy<T> proxy = BMProxy.getInstance(toType);
			for (Object obj : data) {
				T t = proxy.newInstance();
				proxy.copyFrom(obj);
				retData.add(t);
			}
			ret.setData(retData);
		}

		ret.setPageNum(pageData.getPageNum());
		ret.setPageSize(pageData.getPageSize());
		ret.setTotalPages(pageData.getTotalPages());
		ret.setTotalRows(pageData.getTotalRows());
		return ret;
	}

	
	
	
}
