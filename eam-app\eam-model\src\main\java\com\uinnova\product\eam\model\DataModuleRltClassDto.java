package com.uinnova.product.eam.model;

import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;

/**
 * 元模型上使用包含起始分类和目标分类的关系对象信息
 *
 * <AUTHOR>
 * @version 2020-8-13
 */
public class DataModuleRltClassDto {
    /**
     * 起始分类ID
     */
    public Long sourceClassId;

    /**
     * 目标分类ID
     */
    public Long targetClassId;

    /**
     * 关系分类信息
     */
    public CcCiClassInfo rltClassInfo;


    public Long getSourceClassId() {
        return sourceClassId;
    }

    public void setSourceClassId(Long sourceClassId) {
        this.sourceClassId = sourceClassId;
    }

    public Long getTargetClassId() {
        return targetClassId;
    }

    public void setTargetClassId(Long targetClassId) {
        this.targetClassId = targetClassId;
    }

    public CcCiClassInfo getRltClassInfo() {
        return rltClassInfo;
    }

    public void setRltClassInfo(CcCiClassInfo rltClassInfo) {
        this.rltClassInfo = rltClassInfo;
    }
}
