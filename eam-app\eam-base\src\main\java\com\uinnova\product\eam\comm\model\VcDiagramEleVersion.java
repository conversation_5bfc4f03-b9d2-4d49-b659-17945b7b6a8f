package com.uinnova.product.eam.comm.model;




import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;


@Comment("视图元素版本表[VC_DIAGRAM_ELE_VERSION]")
public class VcDiagramEleVersion implements EntityBean {
	private static final long serialVersionUID = 1L;


	@Comment("ID[ID]")
	private Long id;


	@Comment("视图历史ID[DIAGRAM_ID]")
	private Long diagramId;


	@Comment("元素类型[ELE_TYPE]")
	private Integer eleType;


	@Comment("元素ID[ELE_ID]")
	private String eleId;

	@Comment("元素ciCode[ELE_CODE]")
	private String eleCode;

	@Comment("所属域[DOMAIN_ID]")
	private Long domainId;


	@Comment("创建时间[CREATE_TIME]    yyyyMMddHHmmss")
	private Long createTime;


	@Comment("修改时间[MODIFY_TIME]    yyyyMMddHHmmss")
	private Long modifyTime;

	@Override
	public Long getId() {
		return id;
	}

	@Override
	public void setId(Long id) {
		this.id = id;
	}

	public Long getDiagramId() {
		return diagramId;
	}

	public void setDiagramId(Long diagramId) {
		this.diagramId = diagramId;
	}

	public Integer getEleType() {
		return eleType;
	}

	public void setEleType(Integer eleType) {
		this.eleType = eleType;
	}

	public String getEleId() {
		return eleId;
	}

	public void setEleId(String eleId) {
		this.eleId = eleId;
	}

	public Long getDomainId() {
		return domainId;
	}

	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}

	@Override
	public Long getCreateTime() {
		return createTime;
	}

	@Override
	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}

	@Override
	public Long getModifyTime() {
		return modifyTime;
	}

	@Override
	public void setModifyTime(Long modifyTime) {
		this.modifyTime = modifyTime;
	}

	public String getEleCode() {
		return eleCode;
	}

	public void setEleCode(String eleCode) {
		this.eleCode = eleCode;
	}
}


