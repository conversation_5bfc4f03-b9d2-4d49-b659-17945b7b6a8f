server:
  port: 1518
  servlet:
    context-path: ${CONTEXTPATH}
spring:
  mvc:
    pathmatch:
      matching-strategy: ANT_PATH_MATCHER
  application:
    name: eam-flowable
  datasource:
    url: jdbc:mysql://${MYSQL_HOST}:${MYSQL_PORT}/${MYSQL_DB_NAME}?useUnicode=true&characterEncoding=utf-8&useSSL=false&autoReconnect=true&serverTimezone=GMT%2b8&nullCatalogMeansCurrent=true
    username: ${MYSQL_USER}
    password: ${MYSQL_PASSWORD}
  cloud:
    nacos:
      server-addr: ${NACOS_SERVER:NACOS_PORT}
      discovery:
        enabled: true
        #namespace: ${NACOS_NAMESPACE}
        #username: ${NACOS_USER}
        #password: ${NACOS_PASSWORD}
      config:
        enabled: false
    obs:
      endpoint: ${OBS_ENDPOINT}
      access-key: ${OBS_ACCESS_KEY}
      secret-key: ${OBS_SECRET_KEY}
      bucketName: ${OBS_BUCKET_NAME}
      urlExpireSeconds: ${OBS_URL_EXP}
      region: ${OBS_REGION}
      isHttps: ${OBS_IS_HTTPS}
  jpa:
    hibernate:
      ddl-auto: update
    database-platform: org.hibernate.dialect.MySQL8Dialect
    show-sql: true
# flowable 配置
flowable:
  # 关闭异步，不关闭历史数据的插入就是异步的，会在同一个事物里面，无法回滚
  # 开发可开启会提高些效率，上线需要关闭
  async-executor-activate: false
  history-level: full
  db-history-used: true
  database-schema-update: ${FLOWABLE_SCHEMA_UPDATE}

quickea:
  server:
    eam: http://${EAM_SERVICE}:1515

#本地资源http服务地址
http:
  resource:
    space: ${HTTP_RESOURCE}

#本地资源存储地址
local:
  resource:
    space: ${LOCAL_RESOURCE}

eam:
  query:
    user:
      url: ${quickea.server.eam}/tarsier-eam/eam/user/getUserByRoleName
  message:
    save:
      url: ${quickea.server.eam}/tarsier-eam/eam/workbenchChargeDone/saveOrUpdate
    change:
      url: ${quickea.server.eam}/tarsier-eam/eam/workbenchChargeDone/changeAction
  push:
    plan:
      notice: ${quickea.server.eam}/tarsier-eam/eam/notice/workflow/msg/save

cj:
  update:
    diagram:
      status: ${quickea.server.eam}/tarsier-eam/planDesign/updatePlanDiagramIsFlow
generic:
  user:
    url: ${quickea.server.eam}/tarsier-eam/flowable/getApprovalUser
approval:
  task:
    url: ${quickea.server.eam}/tarsier-eam/flowable/approval/task
  bind:
    url: ${quickea.server.eam}/tarsier-eam/flowable/bindTaskToBusiness
rsm:
  util:
    sdkType: ${OBS_SDK_TYPE}
obs:
  use: ${OBS_USE}
  rsm:
    url:
      prefix: ${OBS_RSM_URL_PREFIX}
batch:
  modify:
    workbench:
      task: ${quickea.server.eam}/tarsier-eam/flowable/batchModifyWorkbenchTask