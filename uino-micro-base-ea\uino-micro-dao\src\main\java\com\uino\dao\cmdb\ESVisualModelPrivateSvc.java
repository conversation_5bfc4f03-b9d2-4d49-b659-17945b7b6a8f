package com.uino.dao.cmdb;

import com.uino.bean.cmdb.base.ESVisualModel;
import com.uino.bean.cmdb.query.ESVisualModelSearchBean;
import com.uino.dao.AbstractESBaseDao;
import com.uino.dao.ESConst;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;

/**
 *
 * <AUTHOR>
 *
 */
@Service
public class ESVisualModelPrivateSvc extends AbstractESBaseDao<ESVisualModel, ESVisualModelSearchBean> {

    Log logger = LogFactory.getLog(ESVisualModelPrivateSvc.class);

    @Override
    public String getIndex() {
        return ESConst.INDEX_CMDB_VISUALMODEL+"_private";
    }

    @Override
    public String getType() {
        return "_doc";
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }

}
