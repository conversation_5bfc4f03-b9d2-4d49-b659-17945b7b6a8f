package com.uinnova.product.eam.web.eam.mvc;


import com.binary.framework.web.RemoteResult;
import com.uinnova.product.eam.model.dto.CITableQueryVo;
import com.uinnova.product.eam.service.CITableSnapshotSvc;
import com.uinnova.product.eam.service.DataSetSnapshotSvc;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;

@RestController
@RequestMapping("/snapshot")
public class SnapshotDataMvc {

    @Autowired
    private DataSetSnapshotSvc dataSetSnapshotSvc;

    @Autowired
    private CITableSnapshotSvc ciTableSnapshotSvc;

    @GetMapping("/dataset/createDataSetSnapshot")
    public RemoteResult queryDataSetByKey(@RequestParam String assetKey,
                                          @RequestParam Long dataSetId,
                                          @RequestParam(defaultValue = "3") Integer assetType) {
        return new RemoteResult(dataSetSnapshotSvc.saveorupdateDataSetSnapshot(assetKey, assetType, Collections.singletonList(dataSetId)));
    }

    @GetMapping("/dataset/getDataSetInfoByAssetKey")
    public RemoteResult getDataSetInfoByAssetKey(@RequestParam String assetKey,
                                                 @RequestParam Long dataSetId,
                                                 @RequestParam(defaultValue = "3") Integer assetType) {
        return new RemoteResult(dataSetSnapshotSvc.getDataSetInfoByAssetKey(assetKey, assetType, dataSetId));
    }

    @PostMapping("/ciTable/createCITableSnapshot")
    public RemoteResult createCITableSnapshot(@RequestBody CITableQueryVo ciTableQueryVo) {
        return new RemoteResult(ciTableSnapshotSvc.saveorupdateCiTableSnapshot(ciTableQueryVo.getAssetKey(), 3, ciTableQueryVo.getCiCodeList()));
    }

    @PostMapping("/ciTable/getCITableInfoByAssetKey")
    public RemoteResult getCITableInfoByAssetKey(@RequestBody CITableQueryVo ciTableQueryVo) {
        return new RemoteResult(ciTableSnapshotSvc.getCiTableInfoByAssetKey(ciTableQueryVo.getAssetKey(), 3, ciTableQueryVo.getCiCodeList()));
    }



}
