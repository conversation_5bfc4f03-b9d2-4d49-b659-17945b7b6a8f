package com.uino.bean.cmdb.base.dataset.chart;

/**
 * <AUTHOR>
 * @data 2019/12/26 10:48.
 */
public enum ChartEnum {
    //柱、饼、堆叠
    HISTOGRAM("柱状图", 0),
    PIE("饼图", 1),
    COLUMN_HISTOGRAM("堆叠柱状图", 2);

    ChartEnum(String label, int value) {
        this.label = label;
        this.value = value;
    }

    private String label;
    private int value;

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }}
