package com.uino.provider.feign.monitor;

import java.util.List;
import java.util.Set;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import com.binary.core.io.Resource;
import com.uino.bean.cmdb.business.ImportResultMessage;
import com.uino.bean.monitor.base.ESMonSysSeverityInfo;
import com.uino.provider.feign.config.BaseFeignConfig;

@Component
@FeignClient(name = "${feign-server-name:tarsier-platform}", path = "${feign-server-path:tarsier-platform}/feign/monSeverity", configuration = {BaseFeignConfig.class})
public interface MonSeverityFeign {

    /**
     * 全文检索搜索告警定义
     * 
     * @param searchVal
     * @return
     */
    @PostMapping("querySeverityList")
    List<ESMonSysSeverityInfo> querySeverityList(@RequestParam(value = "domainId")Long domainId, @RequestParam(value = "searchVal", required = false) String searchVal);

    /**
     * 持久化告警定义
     * 
     * @param saveDto
     * @return
     */
    @PostMapping("saveOrUpdateSeverity")
    Long saveOrUpdateSeverity(@RequestBody ESMonSysSeverityInfo saveDto);

    /**
     * 根据告警定义ids删除告警定义
     * 
     * @param delIds
     */
    @PostMapping("deleteServrityByIds")
    void deleteServrityByIds(@RequestBody Set<Long> delIds);

    /**
     * 导出告警定级数据
     * 
     * @param isTpl
     * @return
     */
    @PostMapping("exportServrities")
    public Resource exportSeverityInfos(@RequestParam(value = "domainId")Long domainId, @RequestBody(required = false) Boolean isTpl);

    /**
     * 导入告警定级数据
     * 
     * @param file
     * @return
     */
    @PostMapping(value = "importServrities", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ImportResultMessage importSeverityInfos(@RequestParam(value = "domainId")Long domainId, @RequestPart(value = "file") MultipartFile file);
}
