<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:flowable="http://flowable.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://www.flowable.org/processdef">
  <process id="xw_it_diagram_approve" name="新网it视图审批" isExecutable="true">
    <documentation>新网it视图审批</documentation>
    <startEvent id="startEvent1" flowable:formFieldValidation="true"></startEvent>
    <userTask id="sid-E7D70AF5-7EFE-41BE-96A8-3554A988B131" name="提交人（处理）" flowable:assignee="$INITIATOR" flowable:category="submit" flowable:formFieldValidation="true">
      <documentation>rectification</documentation>
      <extensionElements>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[true]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <userTask id="OrganizationalStructureReviewTask" name="组织架构（审批）" flowable:assignee="${assignee}" flowable:formFieldValidation="true">
      <extensionElements>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
      <multiInstanceLoopCharacteristics isSequential="false" flowable:collection="assigneeList" flowable:elementVariable="assignee">
        <completionCondition>${multiInstanceCompleteExecution.executeByOneUserConditionImmediately(execution)}</completionCondition>
      </multiInstanceLoopCharacteristics>
    </userTask>
    <exclusiveGateway id="sid-6F1333A6-1512-44E7-A4AD-F4B1AB6BCA9A"></exclusiveGateway>
    <userTask id="PublishLocationDirectoryTask" name="发布位置目录owner（审批）" flowable:assignee="${assignee}" flowable:formFieldValidation="true">
      <documentation>IT制品审批流程结束</documentation>
      <extensionElements>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
      <multiInstanceLoopCharacteristics isSequential="false" flowable:collection="assigneeList" flowable:elementVariable="assignee">
        <completionCondition>${multiInstanceCompleteExecution.executeByOneUserConditionImmediately(execution)}</completionCondition>
      </multiInstanceLoopCharacteristics>
    </userTask>
    <exclusiveGateway id="sid-A6466672-B0B6-4405-A2D9-4F862677DCC2"></exclusiveGateway>
    <endEvent id="sid-070FBD58-20A3-4BAB-BC06-A62F174BC424"></endEvent>
    <sequenceFlow id="sid-7FB61133-6CB2-46EE-9ADD-477126E8678D" sourceRef="startEvent1" targetRef="sid-E7D70AF5-7EFE-41BE-96A8-3554A988B131"></sequenceFlow>
    <serviceTask id="sid-844E2405-2FF8-41CE-AC77-B6F59D8B4658" name="相关评审者通过通知" flowable:class="com.uinnova.product.eam.workable.config.CallExternalSystemDelegate"></serviceTask>
    <sequenceFlow id="sid-BA3E2932-64A3-4D79-9D0D-E5934279E98F" sourceRef="PublishLocationDirectoryTask" targetRef="sid-A6466672-B0B6-4405-A2D9-4F862677DCC2"></sequenceFlow>
    <sequenceFlow id="sid-17466C7B-D9E9-4D70-A7E6-01C1CD4DCF3D" sourceRef="OrganizationalStructureReviewTask" targetRef="sid-6F1333A6-1512-44E7-A4AD-F4B1AB6BCA9A"></sequenceFlow>
    <sequenceFlow id="sid-049328C5-4298-4895-944C-68E78F83C221" sourceRef="sid-844E2405-2FF8-41CE-AC77-B6F59D8B4658" targetRef="sid-070FBD58-20A3-4BAB-BC06-A62F174BC424"></sequenceFlow>
    <exclusiveGateway id="sid-890EFD4E-D72A-4F49-806E-B059B6EA1F59"></exclusiveGateway>
    <sequenceFlow id="sid-4D6F24BF-6B6D-4E9F-894F-C6BEBF82CA3B" sourceRef="sid-E7D70AF5-7EFE-41BE-96A8-3554A988B131" targetRef="sid-890EFD4E-D72A-4F49-806E-B059B6EA1F59"></sequenceFlow>
    <endEvent id="sid-CF75C90D-7E23-4B8D-BFA6-3DA5F22F1E63">
      <terminateEventDefinition></terminateEventDefinition>
    </endEvent>
    <sequenceFlow id="sid-5A004FCF-792A-4315-8584-F07762CF25DD" name="同意" sourceRef="sid-6F1333A6-1512-44E7-A4AD-F4B1AB6BCA9A" targetRef="PublishLocationDirectoryTask">
      <documentation>PublishLocationDirectoryTask</documentation>
      <extensionElements>
        <flowable:executionListener event="end" delegateExpression="${publicAccessUserInfoListener}"></flowable:executionListener>
      </extensionElements>
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${goOut=='pass'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-2771DB9D-F507-44CE-98C3-B39D565922E1" name="同意" sourceRef="sid-890EFD4E-D72A-4F49-806E-B059B6EA1F59" targetRef="OrganizationalStructureReviewTask">
      <documentation>OrganizationalStructureReviewTask</documentation>
      <extensionElements>
        <flowable:executionListener event="end" delegateExpression="${publicAccessUserInfoListener}"></flowable:executionListener>
      </extensionElements>
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${pass=='pass'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-8868638B-00A3-4F75-ACE1-BB6522A441B2" name="同意" sourceRef="sid-A6466672-B0B6-4405-A2D9-4F862677DCC2" targetRef="sid-844E2405-2FF8-41CE-AC77-B6F59D8B4658">
      <documentation>diagram</documentation>
      <extensionElements>
        <flowable:executionListener event="end" delegateExpression="${approvalTaskListener}"></flowable:executionListener>
        <flowable:executionListener event="end" delegateExpression="${pushPlanNoticeListener}"></flowable:executionListener>
      </extensionElements>
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${goOut=='pass'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-447201BC-30DD-4B60-88A8-57AB37EB385A" name="驳回" sourceRef="sid-A6466672-B0B6-4405-A2D9-4F862677DCC2" targetRef="sid-E7D70AF5-7EFE-41BE-96A8-3554A988B131">
      <documentation>diagram</documentation>
      <extensionElements>
        <flowable:executionListener event="end" delegateExpression="${approvalTaskListener}"></flowable:executionListener>
        <flowable:executionListener event="end" delegateExpression="${pushPlanNoticeListener}"></flowable:executionListener>
      </extensionElements>
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${goOut=='noPass'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-B85E3C63-C6B1-410C-99CE-5F217AC09DFC" name="驳回" sourceRef="sid-6F1333A6-1512-44E7-A4AD-F4B1AB6BCA9A" targetRef="sid-E7D70AF5-7EFE-41BE-96A8-3554A988B131">
      <documentation>diagram</documentation>
      <extensionElements>
        <flowable:executionListener event="end" delegateExpression="${approvalTaskListener}"></flowable:executionListener>
        <flowable:executionListener event="end" delegateExpression="${pushPlanNoticeListener}"></flowable:executionListener>
      </extensionElements>
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${goOut=='noPass'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-31F8F532-17C5-4AE0-9E0E-E614E346DE57" name="取消" sourceRef="sid-890EFD4E-D72A-4F49-806E-B059B6EA1F59" targetRef="sid-CF75C90D-7E23-4B8D-BFA6-3DA5F22F1E63">
      <documentation>diagram</documentation>
      <extensionElements>
        <flowable:executionListener event="end" delegateExpression="${approvalTaskListener}"></flowable:executionListener>
        <flowable:executionListener event="end" delegateExpression="${pushPlanNoticeListener}"></flowable:executionListener>
      </extensionElements>
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${pass=='cancel'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-BC589900-36A3-4C02-B207-498A8C2BAB48" name="取消" sourceRef="sid-6F1333A6-1512-44E7-A4AD-F4B1AB6BCA9A" targetRef="sid-CF75C90D-7E23-4B8D-BFA6-3DA5F22F1E63">
      <documentation>diagram</documentation>
      <extensionElements>
        <flowable:executionListener event="end" delegateExpression="${approvalTaskListener}"></flowable:executionListener>
        <flowable:executionListener event="end" delegateExpression="${pushPlanNoticeListener}"></flowable:executionListener>
      </extensionElements>
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${pass=='cancel'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-75F691EB-BA06-4AA6-A352-D5A3C958A67E" name="取消" sourceRef="sid-A6466672-B0B6-4405-A2D9-4F862677DCC2" targetRef="sid-CF75C90D-7E23-4B8D-BFA6-3DA5F22F1E63">
      <documentation>diagram</documentation>
      <extensionElements>
        <flowable:executionListener event="end" delegateExpression="${approvalTaskListener}"></flowable:executionListener>
        <flowable:executionListener event="end" delegateExpression="${pushPlanNoticeListener}"></flowable:executionListener>
      </extensionElements>
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${pass=='cancel'}]]></conditionExpression>
    </sequenceFlow>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_xw_it_diagram_approve">
    <bpmndi:BPMNPlane bpmnElement="xw_it_diagram_approve" id="BPMNPlane_xw_it_diagram_approve">
      <bpmndi:BPMNShape bpmnElement="startEvent1" id="BPMNShape_startEvent1">
        <omgdc:Bounds height="30.0" width="30.0" x="60.0" y="240.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-E7D70AF5-7EFE-41BE-96A8-3554A988B131" id="BPMNShape_sid-E7D70AF5-7EFE-41BE-96A8-3554A988B131">
        <omgdc:Bounds height="80.00000000000003" width="100.0" x="149.99999552965178" y="214.99999359250089"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="OrganizationalStructureReviewTask" id="BPMNShape_OrganizationalStructureReviewTask">
        <omgdc:Bounds height="80.00000000000003" width="100.0" x="390.00000895394373" y="214.999996282438"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-6F1333A6-1512-44E7-A4AD-F4B1AB6BCA9A" id="BPMNShape_sid-6F1333A6-1512-44E7-A4AD-F4B1AB6BCA9A">
        <omgdc:Bounds height="40.00000000000003" width="40.0" x="540.0000123977683" y="234.99999674161458"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="PublishLocationDirectoryTask" id="BPMNShape_PublishLocationDirectoryTask">
        <omgdc:Bounds height="80.0" width="100.0" x="630.0000144640629" y="215.0000202037711"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-A6466672-B0B6-4405-A2D9-4F862677DCC2" id="BPMNShape_sid-A6466672-B0B6-4405-A2D9-4F862677DCC2">
        <omgdc:Bounds height="40.00000000000003" width="40.0" x="765.000017563505" y="234.99999674161458"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-070FBD58-20A3-4BAB-BC06-A62F174BC424" id="BPMNShape_sid-070FBD58-20A3-4BAB-BC06-A62F174BC424">
        <omgdc:Bounds height="28.0" width="28.0" x="1004.9999700486669" y="240.99999281764053"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-844E2405-2FF8-41CE-AC77-B6F59D8B4658" id="BPMNShape_sid-844E2405-2FF8-41CE-AC77-B6F59D8B4658">
        <omgdc:Bounds height="80.0" width="100.0" x="855.0000196297997" y="214.99999134628973"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-890EFD4E-D72A-4F49-806E-B059B6EA1F59" id="BPMNShape_sid-890EFD4E-D72A-4F49-806E-B059B6EA1F59">
        <omgdc:Bounds height="40.0" width="40.0" x="295.00000907536145" y="235.00000539532505"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-CF75C90D-7E23-4B8D-BFA6-3DA5F22F1E63" id="BPMNShape_sid-CF75C90D-7E23-4B8D-BFA6-3DA5F22F1E63">
        <omgdc:Bounds height="28.0" width="28.0" x="546.0000249332896" y="135.00000309944207"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="sid-B85E3C63-C6B1-410C-99CE-5F217AC09DFC" id="BPMNEdge_sid-B85E3C63-C6B1-410C-99CE-5F217AC09DFC">
        <omgdi:waypoint x="560.5000123977682" y="274.4380934757703"></omgdi:waypoint>
        <omgdi:waypoint x="560.5000123977682" y="335.0"></omgdi:waypoint>
        <omgdi:waypoint x="199.99999552965178" y="335.0"></omgdi:waypoint>
        <omgdi:waypoint x="199.99999552965178" y="294.94999359250096"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-4D6F24BF-6B6D-4E9F-894F-C6BEBF82CA3B" id="BPMNEdge_sid-4D6F24BF-6B6D-4E9F-894F-C6BEBF82CA3B">
        <omgdi:waypoint x="249.949995529639" y="255.21623243771265"></omgdi:waypoint>
        <omgdi:waypoint x="295.41305050227515" y="255.41304682223878"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-75F691EB-BA06-4AA6-A352-D5A3C958A67E" id="BPMNEdge_sid-75F691EB-BA06-4AA6-A352-D5A3C958A67E">
        <omgdi:waypoint x="785.500017563505" y="235.49999674161458"></omgdi:waypoint>
        <omgdi:waypoint x="785.500017563505" y="149.0000061988842"></omgdi:waypoint>
        <omgdi:waypoint x="573.9499470772275" y="149.0000032911814"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-5A004FCF-792A-4315-8584-F07762CF25DD" id="BPMNEdge_sid-5A004FCF-792A-4315-8584-F07762CF25DD">
        <omgdi:waypoint x="579.5216081455685" y="255.4201685720252"></omgdi:waypoint>
        <omgdi:waypoint x="630.0000144640629" y="255.20900620907196"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-BA3E2932-64A3-4D79-9D0D-E5934279E98F" id="BPMNEdge_sid-BA3E2932-64A3-4D79-9D0D-E5934279E98F">
        <omgdi:waypoint x="729.9500144640629" y="255.23673894625065"></omgdi:waypoint>
        <omgdi:waypoint x="765.4047839613359" y="255.4047631394497"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-049328C5-4298-4895-944C-68E78F83C221" id="BPMNEdge_sid-049328C5-4298-4895-944C-68E78F83C221">
        <omgdi:waypoint x="954.9500196297996" y="254.99999199097402"></omgdi:waypoint>
        <omgdi:waypoint x="1004.9999700486669" y="254.99999263694826"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-BC589900-36A3-4C02-B207-498A8C2BAB48" id="BPMNEdge_sid-BC589900-36A3-4C02-B207-498A8C2BAB48">
        <omgdi:waypoint x="560.4056751458174" y="235.40565948966378"></omgdi:waypoint>
        <omgdi:waypoint x="560.0655154904853" y="162.94958800484622"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-8868638B-00A3-4F75-ACE1-BB6522A441B2" id="BPMNEdge_sid-8868638B-00A3-4F75-ACE1-BB6522A441B2">
        <omgdi:waypoint x="804.5216179201678" y="255.42016394516978"></omgdi:waypoint>
        <omgdi:waypoint x="855.0000196297956" y="255.2089894137761"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-17466C7B-D9E9-4D70-A7E6-01C1CD4DCF3D" id="BPMNEdge_sid-17466C7B-D9E9-4D70-A7E6-01C1CD4DCF3D">
        <omgdi:waypoint x="489.9500089539382" y="255.20725787764215"></omgdi:waypoint>
        <omgdi:waypoint x="540.4166789899782" y="255.41666333382454"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-7FB61133-6CB2-46EE-9ADD-477126E8678D" id="BPMNEdge_sid-7FB61133-6CB2-46EE-9ADD-477126E8678D">
        <omgdi:waypoint x="89.9499988307927" y="254.99999923110008"></omgdi:waypoint>
        <omgdi:waypoint x="149.99999552965178" y="254.99999615293765"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-31F8F532-17C5-4AE0-9E0E-E614E346DE57" id="BPMNEdge_sid-31F8F532-17C5-4AE0-9E0E-E614E346DE57">
        <omgdi:waypoint x="315.5000090753614" y="235.50000539532502"></omgdi:waypoint>
        <omgdi:waypoint x="315.50000907536145" y="149.0000061988842"></omgdi:waypoint>
        <omgdi:waypoint x="546.0000241628185" y="149.00000327628138"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-2771DB9D-F507-44CE-98C3-B39D565922E1" id="BPMNEdge_sid-2771DB9D-F507-44CE-98C3-B39D565922E1">
        <omgdi:waypoint x="334.51872521817984" y="255.42339109006429"></omgdi:waypoint>
        <omgdi:waypoint x="390.0000089539404" y="255.20060234840642"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-447201BC-30DD-4B60-88A8-57AB37EB385A" id="BPMNEdge_sid-447201BC-30DD-4B60-88A8-57AB37EB385A">
        <omgdi:waypoint x="785.500017563505" y="274.44172885354254"></omgdi:waypoint>
        <omgdi:waypoint x="785.500017563505" y="370.0"></omgdi:waypoint>
        <omgdi:waypoint x="174.99999552965178" y="370.0"></omgdi:waypoint>
        <omgdi:waypoint x="174.99999552965178" y="294.94999359250096"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>