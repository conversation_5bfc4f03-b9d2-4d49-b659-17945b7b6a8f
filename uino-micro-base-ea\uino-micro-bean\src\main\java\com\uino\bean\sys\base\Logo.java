package com.uino.bean.sys.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 
 * <AUTHOR>
 *
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value="Logo信息类",description = "Logo信息")
public class Logo {

	@ApiModelProperty(value="id",example = "123")
	private Long id;

	@ApiModelProperty(value="文件id")
	private Long fileId;

	/**
	 * 默认url
	 */
	@ApiModelProperty(value="默认url")
	private String defaultUrl;

	/**
	 * url
	 */
	@ApiModelProperty(value="url")
	private String url;
	/**
	 * 类型
	 */
	@ApiModelProperty(value="类型",example = "system")
	private String type;

	/** 所属域 */
	@ApiModelProperty(value="所属域id",example = "123")
	private Long domainId;

	/** 创建人 */
	@ApiModelProperty(value="创建人",example = "mike")
	private String creator;

	/** 修改人 */
	@ApiModelProperty(value="修改人",example = "mike")
	private String modifier;

	/** 创建时间 */
	@ApiModelProperty(value="创建时间")
	private Long createTime;

	/** 修改时间 */
	@ApiModelProperty(value="修改时间")
	private Long modifyTime;
}
