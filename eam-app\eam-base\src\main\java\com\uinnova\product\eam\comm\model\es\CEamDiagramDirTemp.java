package com.uinnova.product.eam.comm.model.es;

import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

@Data
@Comment("操作计数表[UINO_EAM_DIAGRAM_DIR]")
public class CEamDiagramDirTemp implements Condition {


    @Comment("ID[ID] operate-Equal[=]")
    private Long id;


    @Comment("ID[ID] operate-In[in]")
    private Long[] ids;

    @Comment("ID[ID] operate-GTEqual[>=]")
    private Long startId;

    @Comment("ID[ID] operate-LTEqual[<=]")
    private Long endId;

    @Comment("目录名称[DIR_NAME] operate-Like[like]")
    private String dirName;


    @Comment("目录名称[DIR_NAME] operate-Equal[=]")
    private String dirNameEqual;


    @Comment("目录名称[DIR_NAME] operate-In[in]")
    private String[] dirNames;

    @Comment("目录类型[DIR_TYPE] operate-Equal[=] ")
    private Integer dirType;


    @Comment("目录类型[DIR_TYPE] operate-In[in]  ")
    private Integer[] dirTypes;


    @Comment("目录类型[DIR_TYPE] operate-GTEqual[>=]  ")
    private Integer startDirType;

    @Comment("目录类型[DIR_TYPE] operate-LTEqual[<=]   ")
    private Integer endDirType;

    @Comment("上级目录ID[PARENT_ID] operate-Equal[=]")
    private Long parentId;

    @Comment("上级目录ID[PARENT_ID] operate-In[in]")
    private Long[] parentIds;

    @Comment("上级目录ID[PARENT_ID] operate-GTEqual[>=]")
    private Long startParentId;

    @Comment("上级目录ID[PARENT_ID] operate-LTEqual[<=]")
    private Long endParentId;

    @Comment("所属用户ID[USER_ID] operate-Equal[=]")
    private Long userId;

    @Comment("所属用户ID[USER_ID] operate-In[in]")
    private Long[] userIds;

    @Comment("目录层级级别[DIR_LVL] operate-Equal[=]")
    private Integer dirLvl;

    @Comment("目录层级级别[DIR_LVL] operate-In[in]")
    private Integer[] dirLvls;

    @Comment("目录层级级别[DIR_LVL] operate-GTEqual[>=]")
    private Integer startDirLvl;

    @Comment("目录层级级别[DIR_LVL] operate-LTEqual[<=]")
    private Integer endDirLvl;

    @Comment("目录层级路径[DIR_PATH] operate-Like[like]    目录层级路径:例：#1#2#7#")
    private String dirPath;

    @Comment("显示排序[ORDER_NO] operate-Equal[=]")
    private Integer orderNo;

    @Comment("显示排序[ORDER_NO] operate-In[in]")
    private Integer[] orderNos;

    @Comment("显示排序[ORDER_NO] operate-GTEqual[>=]")
    private Integer startOrderNo;

    @Comment("显示排序[ORDER_NO] operate-LTEqual[<=]")
    private Integer endOrderNo;

    @Comment("是否末级[IS_LEAF] operate-Equal[=]    是否末级:1=是 0=否")
    private Integer isLeaf;

    @Comment("目录图标[ICON] operate-Like[like]")
    private String icon;

    @Comment("目录描述[DIR_DESC] operate-Like[like]")
    private String dirDesc;

    @Comment("所属域[DOMAIN_ID] operate-Equal[=]")
    private Long domainId;

    @Comment("数据状态[DATA_STATUS] operate-Equal[=]    数据状态:0=删除，1=正常")
    private Integer dataStatus;

    @Comment("数据状态[DATA_STATUS] operate-In[in]    数据状态:0=删除，1=正常")
    private Integer[] dataStatuss;

    @Comment("创建人[CREATOR] operate-Like[like]")
    private String creator;

    @Comment("创建人[CREATOR] operate-Equal[=]")
    private String creatorEqual;

    @Comment("创建人[CREATOR] operate-In[in]")
    private String[] creators;

    @Comment("修改人[MODIFIER] operate-Like[like]")
    private String modifier;

    @Comment("修改人[MODIFIER] operate-Equal[=]")
    private String modifierEqual;

    @Comment("修改人[MODIFIER] operate-In[in]")
    private String[] modifiers;

    @Comment("创建时间[CREATE_TIME] operate-Equal[=]    创建时间:yyyyMMddHHmmss")
    private Long createTime;

    @Comment("创建时间[CREATE_TIME] operate-In[in]    创建时间:yyyyMMddHHmmss")
    private Long[] createTimes;

    @Comment("创建时间[CREATE_TIME] operate-GTEqual[>=]    创建时间:yyyyMMddHHmmss")
    private Long startCreateTime;

    @Comment("创建时间[CREATE_TIME] operate-LTEqual[<=]    创建时间:yyyyMMddHHmmss")
    private Long endCreateTime;

    @Comment("更新时间[MODIFY_TIME] operate-Equal[=]    更新时间:yyyyMMddHHmmss")
    private Long modifyTime;

    @Comment("更新时间[MODIFY_TIME] operate-In[in]    更新时间:yyyyMMddHHmmss")
    private Long[] modifyTimes;

    @Comment("更新时间[MODIFY_TIME] operate-GTEqual[>=]    更新时间:yyyyMMddHHmmss")
    private Long startModifyTime;

    @Comment("更新时间[MODIFY_TIME] operate-LTEqual[<=]    更新时间:yyyyMMddHHmmss")
    private Long endModifyTime;

    @Comment("回收站记录原来的父类Id")
    private Long oldParentId;

    @Comment("主题目录")
    private Long subjectId;

    @Comment("是否初始目录  1=是，0=否")
    private Integer dirInit;

    private String esSysId;

    // 关联文件，1：企业级我的文件，2：系统级我的文件
    private Integer relationFile;

    private String sysType;

    // monet应用系统编码
    private String ciCode;
}
