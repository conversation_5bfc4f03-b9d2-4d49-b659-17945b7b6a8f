package com.uino.service.sys.microservice.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.binary.core.io.Resource;
import com.binary.core.io.support.ByteArrayResource;
import com.binary.core.util.BinaryUtils;
import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.comm.model.ci.CcCiClass;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uino.bean.cmdb.base.ESCIClassInfo;
import com.uino.bean.cmdb.business.ImportCellMessage;
import com.uino.bean.cmdb.business.ImportResultMessage;
import com.uino.bean.cmdb.business.ImportRowMessage;
import com.uino.bean.cmdb.business.ImportSheetMessage;
import com.uino.bean.permission.base.SysRole;
import com.uino.bean.sys.base.ESDictionaryAttrDef;
import com.uino.bean.sys.base.ESDictionaryAttrDef.DictProTypeEnum;
import com.uino.bean.sys.base.ESDictionaryClassInfo;
import com.uino.bean.sys.base.ESDictionaryItemInfo;
import com.uino.bean.sys.business.DictionaryInfoDto;
import com.uino.bean.sys.enums.DictionaryOptionEnum;
import com.uino.bean.sys.query.ESDictionaryItemSearchBean;
import com.uino.bean.sys.query.ExportDictionaryDto;
import com.uino.dao.BaseConst;
import com.uino.dao.cmdb.CiClassProDropSourceDefHelper;
import com.uino.dao.cmdb.ESCIClassSvc;
import com.uino.dao.sys.ESDictionaryClassSvc;
import com.uino.dao.sys.ESDictionaryItemSvc;
import com.uino.dao.util.ESUtil;
import com.uino.service.permission.microservice.IRoleSvc;
import com.uino.service.sys.microservice.IDictionarySvc;
import com.uino.service.util.FileUtil;
import com.uino.util.excel.EasyExcelUtil;
import com.uino.util.excel.EasyExcelUtil.SheetData;
import com.uino.util.sys.BeanUtil;
import com.uino.util.sys.CheckAttrUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.Map.Entry;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@RefreshScope
public class DictionarySvc implements IDictionarySvc {

    @Autowired
    private ESDictionaryClassSvc dictClsSvc;

    @Autowired
    private ESDictionaryItemSvc dictItemSvc;

    @Autowired
    private ESCIClassSvc clsSvc;

    @Autowired
    private IRoleSvc roleSvc;

    @Value("${http.resource.space:}")
    private String rsmSlaveRoot;

    @Autowired
    private ESCIClassSvc esciClassSvc;


    @Override
    public Long saveDictionaryClassInfo(ESDictionaryClassInfo dictClassInfo) {
        if (dictClassInfo.getDomainId() == null) {
            dictClassInfo.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
        }
        this.validESDictionaryClassInfo(dictClassInfo);
        Long domainId = dictClassInfo.getDomainId();
        // 校验分类重复
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("domainId", domainId));
        query.must(QueryBuilders.termQuery("dictName.stdkeyword", dictClassInfo.getDictName()));
        Long dictClsId = dictClassInfo.getId();
        if (dictClsId != null) {
            query.mustNot(QueryBuilders.termQuery("id", dictClsId));
        }
        Assert.isTrue(BinaryUtils.isEmpty(dictClsSvc.getListByQuery(query)), "字典定义已存在");
        if (dictClsId == null) {
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
            boolQueryBuilder.filter(QueryBuilders.termQuery("domainId", domainId));
            boolQueryBuilder.filter(QueryBuilders.matchAllQuery());
            List<ESDictionaryClassInfo> maxDictClassInfo = dictClsSvc
                    .getSortListByQuery(1, 1, boolQueryBuilder, "id", false).getData();
            if (!BinaryUtils.isEmpty(maxDictClassInfo)) {
                dictClassInfo.setId(maxDictClassInfo.get(0).getId() + 1);
            }
        }
        return dictClsSvc.saveOrUpdate(dictClassInfo);
    }

    @Override
    public ESDictionaryClassInfo getDictClassInfoById(Long id) {
        Assert.notNull(id, "X_PARAM_NOT_NULL${name:id}");
        return dictClsSvc.getById(id);
    }

    @Override
    public List<DictionaryInfoDto> getDictionaryClassList(Long domainId) {
        List<DictionaryInfoDto> res = new ArrayList<>();
        List<ESDictionaryClassInfo> dictClassInfos = dictClsSvc
                .getSortListByQuery(1, 9999, QueryBuilders.termQuery("domainId", domainId), "id", true).getData();
        // 根据字典表id查询字典的个数
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.termQuery("domainId", domainId));
        boolQueryBuilder.filter(QueryBuilders.matchAllQuery());
        Map<String, Long> dictCountMap = dictItemSvc.groupByCountField("dictClassId", boolQueryBuilder);
        for (ESDictionaryClassInfo dictClassInfo : dictClassInfos) {
            DictionaryInfoDto dto = BeanUtil.converBean(dictClassInfo, DictionaryInfoDto.class);
            // 未置顶排序字段时，默认按主键排序
            if (dictClassInfo.getOrderDefId() == null) {
                List<ESDictionaryAttrDef> majorDefs = dictClassInfo.getDictAttrDefs().stream()
                        .filter(def -> def.getIsMajor() == 1).collect(Collectors.toList());
                dto.setOrderDefId(majorDefs.get(0).getId());
            }
            dto.setDictCount(dictCountMap.get(String.valueOf(dictClassInfo.getId())) == null ? 0L : dictCountMap.get(String.valueOf(dictClassInfo.getId())));
            res.add(dto);
        }
        return res;
    }

    @Override
    public List<ESDictionaryClassInfo> queryDcitClassInfosByBean(ESDictionaryItemSearchBean bean) {
        if (bean == null) {
            return new ArrayList<>();
        }
        if (bean.getDomainId() == null) {
            bean.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
        }
        List<ESDictionaryClassInfo> dictClassInfos = dictClsSvc.queryDcitClassInfosByBean(bean);
        for (ESDictionaryClassInfo dictClassInfo : dictClassInfos) {
            // 未置顶排序字段时，默认按主键排序
            if (dictClassInfo.getOrderDefId() == null) {
                List<ESDictionaryAttrDef> majorDefs = dictClassInfo.getDictAttrDefs().stream()
                        .filter(def -> def.getIsMajor() == 1).collect(Collectors.toList());
                dictClassInfo.setOrderDefId(majorDefs.get(0).getId());
            }
        }
        return dictClassInfos;
    }

    @Override
    public Integer deleteDictClassInfoById(Long dictClassId) {
        ESDictionaryClassInfo dictClassInfo = this.getDictClassInfoById(dictClassId);
        Assert.notNull(dictClassInfo, "字典定义不存在");
        Assert.isTrue(1 != dictClassInfo.getIsInit().intValue(), "系统数据不可删除");
        Assert.isTrue(dictItemSvc.countByCondition(QueryBuilders.termQuery("dictClassId", dictClassId)) == 0,
                "请先删除字典数据");
        return dictClsSvc.deleteById(dictClassId);
    }

    @Override
    public ESDictionaryItemInfo getDictItemInfoById(Long id) {
        return dictItemSvc.getById(id);
    }


    @Override
    public List<ESDictionaryItemInfo> getDictItemInfoByIds(Collection<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return new ArrayList<>(0);
        }
        // 字典表查询不可能大于3000,不考虑分页
        QueryBuilder q = QueryBuilders.termsQuery("id", ids);
        return dictItemSvc.getListByQuery(q);
    }


    @Override
    public Page<ESDictionaryItemInfo> searchDictItemPageByBean(ESDictionaryItemSearchBean bean) {
        if (bean == null) {
            bean = new ESDictionaryItemSearchBean();
        }
        if (bean.getDomainId() == null) {
            bean.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
        }
        Page<ESDictionaryItemInfo> page = new Page<>(bean.getPageNum(), bean.getPageSize(), 0, 0, new ArrayList<>());
        QueryBuilder query = dictItemSvc.getDictQueryBuilderByBean(bean);
        if (query != null) {
            String sortField = BinaryUtils.isEmpty(bean.getSortField()) ? "id" : bean.getSortField();
            if (sortField.startsWith("attrs.")) {
                sortField = sortField + ".keyword";
            }
            page = dictItemSvc.getSortListByQuery(bean.getPageNum(), bean.getPageSize(), query, sortField,
                    bean.getIsAsc());
            // 默认为只读
            page.getData().forEach(item -> {
                if (BinaryUtils.isEmpty(item.getOption())) {
                    item.setOption(DictionaryOptionEnum.READ);
                }
            });
        }
        return page;
    }

    @Override
    public Page<ESDictionaryItemInfo> searchDictItemPageByIds(ESDictionaryItemSearchBean bean) {
        if (bean == null) {
            bean = new ESDictionaryItemSearchBean();
        }
        if (bean.getDomainId() == null) {
            bean.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
        }
        Page<ESDictionaryItemInfo> page = new Page<>(bean.getPageNum(), bean.getPageSize(), 0, 0, new ArrayList<>());
        QueryBuilder query = dictItemSvc.getDictQueryBuilderByIds(bean);
        if (query != null) {
            String sortField = BinaryUtils.isEmpty(bean.getSortField()) ? "id" : bean.getSortField();
            if (sortField.startsWith("attrs.")) {
                sortField = sortField + ".keyword";
            }
            page = dictItemSvc.getSortListByQuery(bean.getPageNum(), bean.getPageSize(), query, sortField,
                    bean.getIsAsc());
            // 默认为只读
            page.getData().forEach(item -> {
                if (BinaryUtils.isEmpty(item.getOption())) {
                    item.setOption(DictionaryOptionEnum.READ);
                }
            });
        }
        return page;
    }

    @Override
    public List<ESDictionaryItemInfo> searchDictItemListByBean(ESDictionaryItemSearchBean bean) {
        bean.setPageNum(1);
        bean.setPageSize(9999);
        return this.searchDictItemPageByBean(bean).getData();
    }

    @Override
    public Long saveOrUpdateDictionaryItem(ESDictionaryItemInfo item) {
        Assert.notNull(item.getDictClassId(), "X_PARAM_NOT_NULL${name:dictClassId}");
        ESDictionaryClassInfo dictClassInfo = dictClsSvc.getById(item.getDictClassId());
        List<ESDictionaryAttrDef> dictAttrDefs = dictClassInfo.getDictAttrDefs();
        // 获取来源及单位引用值
        Map<String, List<String>> sourceValMap = this.getSourceValMap(dictAttrDefs);
        Map<String, String> attrs = this.transToStdMap(item.getAttrs(), dictClassInfo, false);
        item.setAttrs(attrs);
        // 验证属性
        this.validDictionaryAttrs(dictAttrDefs, item, sourceValMap);
        // 验证重复
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("dictClassId", item.getDictClassId()));
        if (!BinaryUtils.isEmpty(item.getId())) {
            query.mustNot(QueryBuilders.termQuery("id", item.getId()));
        }
        BoolQueryBuilder attrQuery = QueryBuilders.boolQuery();
        for (ESDictionaryAttrDef def : dictAttrDefs) {
            if (def.getIsMajor() == 1 || def.getIsUnique() == 1) {
                String majorStr = attrs.get(def.getProStdName());
                Assert.isTrue(!BinaryUtils.isEmpty(majorStr), "X_PARAM_NOT_NULL${name:标识}");
                attrQuery.should(QueryBuilders.termQuery("attrs." + def.getProStdName() + ".keyword", majorStr));
            }
            //加颜色唯一性校验
            if (def.getProType() != null && def.getProType() == 4) {
                String color = attrs.get(def.getProStdName());
                if (color == null) {
                    continue;
                }
                attrQuery.should(QueryBuilders.termQuery("attrs." + def.getProStdName() + ".keyword", color));
            }
        }
        query.must(attrQuery);
        List<ESDictionaryItemInfo> list = dictItemSvc.getListByQuery(query);
        Assert.isTrue(BinaryUtils.isEmpty(list), "字典项主键或颜色有重复！");
        item.setIndex(null);
        if (null != item.getId()) {
            item.setIsInit(null);
        }
        return dictItemSvc.saveOrUpdate(item);
    }

    @Override
    public ImportSheetMessage saveDictionaryItemsBatch(Long dictClassId, List<ESDictionaryItemInfo> items) {
        Assert.notNull(dictClassId, "X_PARAM_NOT_NULL${name:dictClassId}");
        ESDictionaryClassInfo dictClassInfo = dictClsSvc.getById(dictClassId);
        Assert.notNull(dictClassInfo, "字典定义不存在");
        ImportSheetMessage sheetMessage = ImportSheetMessage.builder().className(dictClassInfo.getDictName())
                .sheetName(dictClassInfo.getDictName()).build();
        if (!BinaryUtils.isEmpty(items)) {
            sheetMessage.setTotalNum(items.size());
            List<ESDictionaryAttrDef> dictAttrDefs = dictClassInfo.getDictAttrDefs();
            // 用于引用属性校验
            Map<String, List<String>> sourceValMap = this.getSourceValMap(dictAttrDefs);
            // 统计信息
            int failCount = 0, insertCount = 0, updateCount = 0;
            // 获取存量数据，用于重复项验证
            List<ESDictionaryItemInfo> dbItems = dictItemSvc
                    .getListByQuery(1, 9999, QueryBuilders.termQuery("dictClassId", dictClassId)).getData();
            // 用于判重
            Map<String, ESDictionaryItemInfo> dbItemMap = BinaryUtils.toObjectMap(dbItems, "keyCode");
            // 遍历字典项，校验属性格式及重复项
            List<ImportRowMessage> rowMessages = new ArrayList<>();
            List<ESDictionaryItemInfo> itemInfos = new ArrayList<>();
            List<String> saveKeyCodes = new ArrayList<>();
            Map<String, List<String>> uniqueMap = new HashMap<>();
            for (int i = 0; i < items.size(); i++) {
                ESDictionaryItemInfo item = items.get(i);
                int index = item.getIndex() == null ? i + 2 : item.getIndex();
                // 属性校验
                try {
                    this.validDictionaryAttrs(dictAttrDefs, item, sourceValMap);
                } catch (Exception e) {
                    ImportRowMessage rowMessage = this.buildRowMessage(index, null, e.getMessage());
                    rowMessages.add(rowMessage);
                    failCount++;
                    continue;
                }
                // 重复校验
                String keyCode = item.getKeyCode();
                if (saveKeyCodes.contains(keyCode)) {
                    ImportRowMessage rowMessage = this.buildRowMessage(index, null, "字典项重复");
                    rowMessages.add(rowMessage);
                    failCount++;
                    continue;
                }
                boolean flag = false;
                loop:
                for (ESDictionaryAttrDef attrDef : dictAttrDefs) {
                    if (attrDef.getIsUnique() == 1) {
                        Map<String, String> attrs = item.getAttrs();
                        String uniqueStr = attrs.get(attrDef.getProStdName());
                        if (attrDef.getIsMajor() != 1) {
                            List<String> uniqueList = uniqueMap.get(attrDef.getProStdName());
                            if (CollectionUtils.isEmpty(uniqueList)) {
                                uniqueList = new ArrayList<>();
                                uniqueMap.put(attrDef.getProStdName(), uniqueList);
                            }
                            if (uniqueList.contains(uniqueStr)) {
                                ImportRowMessage rowMessage = this.buildRowMessage(index, null, "字典项重复");
                                rowMessages.add(rowMessage);
                                failCount++;
                                flag = true;
                                break loop;
                            } else {
                                uniqueList.add(uniqueStr);
                            }
                        }
                        for (ESDictionaryItemInfo dbItem : dbItems) {
                            Map<String, String> dbItemAttrs = dbItem.getAttrs();
                            String dbUniqueStr = dbItemAttrs.get(attrDef.getProStdName());
                            if (!dbItemMap.containsKey(keyCode) && uniqueStr.equals(dbUniqueStr)) {
                                ImportRowMessage rowMessage = this.buildRowMessage(index, null, attrDef.getProName() +
                                        "必须唯一,重复值为:" + uniqueStr);
                                rowMessages.add(rowMessage);
                                failCount++;
                                flag = true;
                                break loop;
                            }
                        }
                    }
                }
                if (flag) continue;
                if (dbItemMap.containsKey(keyCode)) {
                    ESDictionaryItemInfo dbItem = dbItemMap.get(keyCode);
                    item.setId(dbItem.getId());
                    item.setIsInit(dbItem.getIsInit());
                    item.setOption(dbItem.getOption());
                    updateCount++;
                } else {
                    insertCount++;
                }
                item.setIndex(null);
                itemInfos.add(item);
                saveKeyCodes.add(keyCode);
            }
            // 批量保存字典项
            if (!BinaryUtils.isEmpty(itemInfos)) {
                dictItemSvc.saveOrUpdateBatch(itemInfos);
            }
            sheetMessage.setSuccessNum(insertCount + updateCount);
            sheetMessage.setFailNum(failCount);
            sheetMessage.setInsertNum(insertCount);
            sheetMessage.setUpdateNum(updateCount);
            // 对错误信息按行号排序
            if (!BinaryUtils.isEmpty(rowMessages)) {
                Collections.sort(rowMessages, FileUtil.ExcelUtil.getRowMessageComparator(rowMessages));
                sheetMessage.setRowMessages(rowMessages);
            }
        }
        return sheetMessage;
    }

    @Override
    public Integer deleteItemByIds(Collection<Long> ids) {
        List<ESDictionaryItemInfo> items = dictItemSvc.getListByQuery(QueryBuilders.termsQuery("id", ids));
        if (!BinaryUtils.isEmpty(items)) {
            List<Long> delIds = items.stream().filter(item -> DictionaryOptionEnum.WRITE == item.getOption())
                    .map(ESDictionaryItemInfo::getId).collect(Collectors.toList());
            if (!BinaryUtils.isEmpty(delIds)) {
                dictItemSvc.deleteByIds(new ArrayList<>(delIds));
            }
            Assert.isTrue(ids.size() == delIds.size(), "BS_NOT_AUTH");
        }
        return 1;
    }

    @Override
    public Resource exportDictionaryItems(ExportDictionaryDto dto) {
        Long dictClassId = dto.getDictClassId();
        Assert.notNull(dictClassId, "X_PARAM_NOT_NULL${name:dictClassId}");
        ESDictionaryClassInfo dictClassInfo = dictClsSvc.getById(dictClassId);
        Assert.notNull(dictClassInfo, "该字典定义不存在");
        List<ESDictionaryAttrDef> dictAttrDefs = dictClassInfo.getDictAttrDefs();
        List<String> titles = new ArrayList<>();
        Set<String> reqCellValues = new HashSet<>();
        List<String> fileDefNames = new ArrayList<>();
        dictAttrDefs.forEach(def -> {
            if (def.getDisplay() == true) {
                titles.add(def.getProName());
            }
            if (def.getIsRequired().intValue() == 1) {
                reqCellValues.add(def.getProName());
            }
            if (def.getProType().intValue() == ESDictionaryAttrDef.DictProTypeEnum.FILE.getType()) {
                fileDefNames.add(def.getProName());
            }
        });
        List<Map<String, String>> datas = new ArrayList<Map<String, String>>();
        if (!dto.getIsTpl()) {
            List<ESDictionaryItemInfo> items = dictItemSvc
                    .getListByQuery(QueryBuilders.termQuery("dictClassId", dictClassId));
            // 转换引用值
            this.transExterDcitIdToName(items, dictClassInfo);
            for (ESDictionaryItemInfo item : items) {
                Map<String, String> attrs = item.getAttrs();
                // 导出时文件属性清空
                for (String filePro : fileDefNames) {
                    attrs.put(filePro, null);
                }
                Map<String, String> stdMap = this.transToStdMap(attrs, dictClassInfo, true);
                datas.add(stdMap);
            }
        }
        return this.writeDataToExcelFile(dictClassInfo.getDictName(), titles, reqCellValues, datas);
    }

    @Override
    public ImportResultMessage importDictionaryItems(Long dictClassId, MultipartFile file) {
        // 校验参数
        Assert.notNull(dictClassId, "X_PARAM_NOT_NULL${name:dictClassId}");
        FileUtil.ExcelUtil.validExcelImportFile(file);
        ESDictionaryClassInfo dictClassInfo = dictClsSvc.getById(dictClassId);
        Assert.notNull(dictClassInfo, "该字典定义不存在");
        List<String> fileProNames = dictClassInfo.getDictAttrDefs().stream()
                .filter(def -> def.getProType().intValue() == ESDictionaryAttrDef.DictProTypeEnum.FILE.getType())
                .map(ESDictionaryAttrDef::getProName).collect(Collectors.toList());
        ImportSheetMessage sheetMessage = ImportSheetMessage.builder().className(dictClassInfo.getDictName())
                .sheetName(dictClassInfo.getDictName()).build();

        InputStream excelIs = null;
        List<ESDictionaryItemInfo> items = new ArrayList<>();
        try {
            excelIs = file.getInputStream();
            SheetData sheetData = EasyExcelUtil.readSheet(dictClassInfo.getDictName(), null, excelIs);
            Assert.isTrue(!BinaryUtils.isEmpty(sheetData.getTitles()), "字典数据不存在");
            List<String> titles = sheetData.getTitles();
            Map<String, List<String>> attrMark = FileUtil.ExcelUtil.getAttrMarkByTitles(titles);
            titles = attrMark.get("titles");
            List<String[]> rows = sheetData.getRows();
            sheetMessage.setTotalNum(rows.size());
            for (int i = 0; i < rows.size(); i++) {
                String[] row = rows.get(i);
                Map<String, String> map = new HashMap<String, String>();
                for (int j = 0; j < titles.size(); j++) {
                    String val = row[j] == null ? row[j] : row[j].trim();
                    // 文件属性不支持导入，清空属性值
                    if (fileProNames.contains(titles.get(j))) {
                        val = null;
                    }
                    map.put(titles.get(j), val);
                }
                /**
                 * 转换为标准属性值
                 */
                Map<String, String> stdMap = this.transToStdMap(map, dictClassInfo, false);
                ESDictionaryItemInfo itemInfo = new ESDictionaryItemInfo();
                itemInfo.setDomainId(dictClassInfo.getDomainId());
                itemInfo.setDictClassId(dictClassId);
                itemInfo.setAttrs(stdMap);
                itemInfo.setIndex(i + 2);
                items.add(itemInfo);
            }
        } catch (IOException e1) {
            log.error("读取excel流异常");
        } finally {
            if (excelIs != null) {
                try {
                    excelIs.close();
                } catch (IOException e) {
                    log.error("关闭excel流异常");
                }
            }
        }
        return this.importDictItemsBatch(dictClassInfo, items, sheetMessage);
    }

    @Override
    public List<String> getExteralDictValues(ESDictionaryItemSearchBean bean) {
        // 校验参数
        Long dictClassId = bean.getDictClassId();
        String dictCode = bean.getDictCode();
        String dictName = bean.getDictName();
        Long dictDefId = bean.getDictDefId();
        String dictProName = bean.getDictProName();
        Assert.isTrue(dictClassId != null || dictCode != null || dictName != null, "X_PARAM_NOT_NULL${name:引用字典定义}");
        /*Assert.isTrue(dictDefId != null || dictProName != null, "X_PARAM_NOT_NULL${name:引用属性}");*/
        List<String> res = new ArrayList<>();
        // 获取字典项
        List<ESDictionaryItemInfo> items = this.searchDictItemListByBean(bean);
        if (BinaryUtils.isEmpty(items)) {
            return res;
        }
        // 判断引用属性是否存在
        ESDictionaryClassInfo dictClassInfo = dictClsSvc.getById(items.get(0).getDictClassId());
        String proName = null;
        for (ESDictionaryAttrDef def : dictClassInfo.getDictAttrDefs()) {
            if (dictDefId != null && dictDefId.longValue() == def.getId().longValue()) {
                proName = def.getProStdName();
            }
            if (!BinaryUtils.isEmpty(dictProName) && dictProName.equals(def.getProName())) {
                proName = def.getProStdName();
            }
        }
        if (proName == null) {
            return res;
        }

        for (ESDictionaryItemInfo item : items) {
            Map<String, String> attrs = item.getAttrs();
            String val = attrs.get(proName);
            if (!BinaryUtils.isEmpty(val) && !res.contains(val)) {
                res.add(val);
            }
        }
        return res;
    }

    @Override
    public List<String> getExteralDictValues(Long domainId, Long dictClassId, Long[] dictDefIds) {
        // 校验参数
        Assert.isTrue(domainId != null || dictClassId != null || dictDefIds != null, "X_PARAM_NOT_NULL${name:引用字典定义}");
        List<String> res = new ArrayList<>();
        // 获取字典项
        ESDictionaryItemSearchBean bean = new ESDictionaryItemSearchBean();
        bean.setDomainId(domainId);
        bean.setDictClassId(dictClassId);
        List<ESDictionaryItemInfo> items = this.searchDictItemListByBean(bean);
        if (BinaryUtils.isEmpty(items)) {
            return res;
        }
        // 判断引用属性是否存在
        ESDictionaryClassInfo dictClassInfo = dictClsSvc.getById(items.get(0).getDictClassId());
        Set<String> proNames = new LinkedHashSet<>();
        Map<Long, ESDictionaryAttrDef> attrDefMap = dictClassInfo.getDictAttrDefs()
                .stream().collect(Collectors.toMap(ESDictionaryAttrDef::getId, e -> e));
        for (Long dictDefId : dictDefIds) {
            ESDictionaryAttrDef attrDef = attrDefMap.get(dictDefId);
            if (attrDef != null) {
                proNames.add(attrDef.getProStdName());
            }
        }
        if (CollectionUtils.isEmpty(proNames)) {
            return res;
        }

        for (ESDictionaryItemInfo item : items) {
            Map<String, String> attrs = item.getAttrs();
            List<String> val = new ArrayList<>();
            for (String proName : proNames) {
                String value = attrs.get(proName);
                if (StringUtils.isBlank(value)) {
                    continue;
                }
                val.add(value);
            }
            if (!CollectionUtils.isEmpty(val)) {
                res.add(String.join(CiClassProDropSourceDefHelper.DEF_PRONAME_SEPARATOR, val));
            }
        }
        return res;
    }

    @Override
    public String checkAttrUsedMethod(Long dictClassId) {
        //查询所有分类属性信息有没有选择当前要删除的字典表
        List<CcCiClassInfo> ccCiClassInfos = esciClassSvc.queryAllClasses();
        Map<Long, String> ciClassMap = ccCiClassInfos.stream().map(CcCiClassInfo::getCiClass).collect(Collectors.toMap(CcCiClass::getId, CcCiClass::getClassName, (k1, k2) -> k2));
        List<CcCiAttrDef> ccCiAttrDefs = ccCiClassInfos.stream()
                .map(CcCiClassInfo::getAttrDefs)
                .flatMap(Collection::stream)
                .filter(each -> each.getProType().equals(8))
                .collect(Collectors.toList());
        Map<String, List<String>> resultMap = new HashMap<>(ccCiAttrDefs.size());
        if (!BinaryUtils.isEmpty(ccCiAttrDefs)) {
            for (CcCiAttrDef ccCiAttrDef : ccCiAttrDefs) {
                String[] split = ccCiAttrDef.getProDropSourceDef().split(":");
                if (split[0].contains(dictClassId.toString())) {
                    String className = ciClassMap.get(ccCiAttrDef.getClassId());
                    if (BinaryUtils.isEmpty(className)) {
                        continue;
                    }
                    List<String> lists = resultMap.computeIfAbsent(className, key -> new ArrayList<>(ccCiAttrDefs.size()));
                    lists.add(ccCiAttrDef.getProName());
                }
            }
        }
        if (!BinaryUtils.isEmpty(resultMap)) {
            StringBuilder stringBuilder = new StringBuilder("当前数据字典被");
            for (String key : resultMap.keySet()) {
                stringBuilder.append("【").append(key).append("】");
            }
            stringBuilder.append("分类使用，请先修改对象分类");
            return stringBuilder.toString();
        }
        return null;
    }

    public List<ImportRowMessage> transExterDcitNameToId(List<ESDictionaryItemInfo> items,
                                                         ESDictionaryClassInfo dictClsInfo) {
        Assert.notNull(dictClsInfo, "X_PARAM_NOT_NULL${name:字典定义}");
        List<ImportRowMessage> rowMessages = new ArrayList<>();
        if (!BinaryUtils.isEmpty(items)) {
            List<ESDictionaryAttrDef> dictAttrDefs = dictClsInfo.getDictAttrDefs();
            Assert.isTrue(!BinaryUtils.isEmpty(dictAttrDefs), "X_PARAM_NOT_NULL${name:字典定义}");
            // 获取引用类型的属性定义
            List<ESDictionaryAttrDef> citeDefs = dictAttrDefs.stream().filter(def -> def.getProType()
                    .intValue() == ESDictionaryAttrDef.DictProTypeEnum.EXTERNAL_DICT.getType()
                    && (def.getCiteType().intValue() == ESDictionaryAttrDef.DictCiteTypeEnum.CLASS.getType()
                    || def.getCiteType().intValue() == ESDictionaryAttrDef.DictCiteTypeEnum.ROLE.getType()))
                    .collect(Collectors.toList());
            if (!BinaryUtils.isEmpty(citeDefs)) {
                // 获取引用类型值
                Map<String, Map<String, String>> exterDictValMap = this.getExterDictValMap(dictAttrDefs);
                Iterator<ESDictionaryItemInfo> it = items.iterator();
                nextItem:
                while (it.hasNext()) {
                    ESDictionaryItemInfo item = it.next();
                    int index = item.getIndex();
                    Map<String, String> attrs = item.getAttrs();
                    for (ESDictionaryAttrDef def : citeDefs) {
                        String val = attrs.get(def.getProStdName());
                        if (!BinaryUtils.isEmpty(val)) {
                            Map<String, String> idToNameMap = exterDictValMap.get(def.getProName());
                            // 转换为id
                            // 复选框
                            if (def.getSelectType().intValue() == 1) {
                                String[] split = val.split(",");
                                List<String> ids = new ArrayList<>();
                                for (String str : split) {
                                    String id = this.getKeyByValue(idToNameMap, str);
                                    if (id != null) {
                                        ids.add(id);
                                    } else {
                                        rowMessages.add(this.buildRowMessage(index, null,
                                                "属性[" + def.getProName() + "]引用值[" + str + "]不存在"));
                                        it.remove();
                                        continue nextItem;
                                    }
                                }
                                attrs.put(def.getProStdName(), JSON.toJSONString(ids));
                            } else {
                                String id = this.getKeyByValue(idToNameMap, val);
                                if (id == null) {
                                    rowMessages.add(this.buildRowMessage(index, null,
                                            "属性[" + def.getProName() + "]引用值[" + val + "]不存在"));
                                    it.remove();
                                    continue nextItem;
                                }
                                attrs.put(def.getProStdName(), id);
                            }
                        }
                    }

                }
            }
        }
        return rowMessages;
    }

    /**
     * 校验字典定义合法性
     *
     * @param dictClassInfo
     */
    private void validESDictionaryClassInfo(ESDictionaryClassInfo dictClassInfo) {
        // 校验必填项
        Assert.notNull(dictClassInfo.getDictName(), "X_PARAM_NOT_NULL${name:字典名称}");
        Assert.notNull(dictClassInfo.getDomainId(), "BS_VISUALMODEL_LACK_DOMAINID");
        Assert.isTrue(dictClassInfo.getDictName().matches("[^\\:\\/\\?\\*\\[\\]\\\\]{1,30}"),
                "字典名称不可超过30位，且不可包含:/\\?*[]等字符");
        Assert.isTrue(!BinaryUtils.isEmpty(dictClassInfo.getDictAttrDefs()), "X_PARAM_NOT_NULL${name:字典定义}");
        boolean isAdd = dictClassInfo.getId() == null;
        if (isAdd) {
            dictClassInfo.setDictCode(String.valueOf(ESUtil.getUUID()));
        }
        // 校验属性重复
        // 校验标识属性类型
        List<ESDictionaryAttrDef> dictAttrDefs = dictClassInfo.getDictAttrDefs();
        List<String> defNames = new ArrayList<>();
        for (ESDictionaryAttrDef def : dictAttrDefs) {
            Assert.notNull(def.getProName(), "X_PARAM_NOT_NULL${name:属性名称}");
            Assert.isTrue(!defNames.contains(def.getProName().toUpperCase()), "属性[" + def.getProName() + "]重复");
            if (def.getIsMajor().longValue() == 1) {
                def.setIsRequired(1);
            }
            this.validDictProTypeDef(def);
            if (def.getId() == null && BinaryUtils.isEmpty(def.getProStdName())) {
                def.setProStdName(def.getProName());
            }
            def.setId(def.getId() == null ? ESUtil.getUUID() : def.getId());
            defNames.add(def.getProName().toUpperCase());
        }
        List<String> majorProNames = dictAttrDefs.stream().filter(def -> def.getIsMajor() == 1)
                .map(ESDictionaryAttrDef::getProName).collect(Collectors.toList());
        Assert.isTrue(majorProNames.size() == 1, "标识属性有且至多有一个");
        // Set<ESDictionaryAttrDef> fileDefs = dictAttrDefs.stream().filter(def ->
        // def.getProType().intValue() ==
        // ESDictionaryAttrDef.DictProTypeEnum.FILE.getType()).collect(Collectors.toSet());
        // Assert.isTrue(fileDefs.size() <= 1, "文件属性至多有一个");
        // 若字典定义下已有数据，禁止修改已存在任何属性
        if (!isAdd) {
            long count = dictItemSvc.countByCondition(QueryBuilders.termQuery("dictClassId", dictClassInfo.getId()));
            if (count > 0) {
                ESDictionaryClassInfo oldCls = dictClsSvc.getById(dictClassInfo.getId());
                // 新属性模板信息
                List<ESDictionaryAttrDef> newDefs = oldCls.getDictAttrDefs();
                newDefs = newDefs == null ? new LinkedList<>() : newDefs;
                Map<Long, ESDictionaryAttrDef> newDefMap = new HashMap<>();
                newDefs.forEach(def -> newDefMap.put(def.getId(), def));
                // 旧的属性模板相关信息
                List<ESDictionaryAttrDef> oldDefs = oldCls.getDictAttrDefs();
                oldDefs = oldDefs == null ? new LinkedList<>() : oldDefs;
                oldDefs.forEach(def -> {
                    Assert.isTrue(newDefMap.get(def.getId()) != null, "字典表下已有数据不允许删除属性");
                    Assert.isTrue(this.equalsAttrDef(newDefMap.get(def.getId()), def), "字典表下已有数据不允许修改属性");
                });
                // 新属性不可设置为必填
                List<ESDictionaryAttrDef> validList = newDefs.stream().filter(
                        def -> def.getId() == null && ((def.getIsMajor() != null && def.getIsMajor().intValue() == 1)
                                || (def.getIsRequired() != null && def.getIsRequired().intValue() == 1)))
                        .collect(Collectors.toList());
                Assert.isTrue(validList == null || validList.size() <= 0, "字典表下已有数据不允许新增必填属性");
            }
            dictAttrDefs.forEach(def -> {
                if (def.getId() != null) {
                    def.setProStdName(def.getProName());
                }
            });
        }
    }

    private boolean equalsAttrDef(ESDictionaryAttrDef attrDef, ESDictionaryAttrDef attrDef2) {
        boolean equalFlag = true;
        if (attrDef == attrDef2) {
            return equalFlag;
        }
        equalFlag = CheckAttrUtil.equalsForModel(attrDef.getId(), attrDef2.getId())
                && CheckAttrUtil.equalsForModel(attrDef.getProName(), attrDef2.getProName())
                && CheckAttrUtil.equalsForModel(attrDef.getProType(), attrDef2.getProType())
                && CheckAttrUtil.equalsForModel(attrDef.getIsMajor(), attrDef2.getIsMajor())
                && CheckAttrUtil.equalsForModel(attrDef.getIsRequired(), attrDef2.getIsRequired())
                && CheckAttrUtil.equalsForModel(attrDef.getEnumValues(), attrDef2.getEnumValues());
        if (attrDef.getProType().intValue() == ESDictionaryAttrDef.DictProTypeEnum.EXTERNAL_DICT.getType()
                || attrDef2.getProType().intValue() == ESDictionaryAttrDef.DictProTypeEnum.EXTERNAL_DICT.getType()) {
            equalFlag = CheckAttrUtil.equalsForModel(attrDef.getCiteType(), attrDef2.getCiteType())
                    && CheckAttrUtil.equalsForModel(attrDef.getSelectType(), attrDef2.getSelectType())
                    && CheckAttrUtil.equalsForModel(attrDef.getSourceDictClassId(), attrDef2.getSourceDictClassId())
                    && CheckAttrUtil.equalsForModel(attrDef.getSourceDictDefId(), attrDef2.getSourceDictDefId());
        }
        return equalFlag;
    }

    /**
     * 校验属性定义合法性
     *
     * @param def
     */
    private void validDictProTypeDef(ESDictionaryAttrDef def) {
        DictProTypeEnum proType = ESDictionaryAttrDef.DictProTypeEnum.valueOf(def.getProType());
        Assert.isTrue(proType != null, "不支持的属性类型");
        String defVal = def.getDefVal();
        switch (proType) {
            case TEXT:
                if (!BinaryUtils.isEmpty(defVal)) {
                    Assert.isTrue(defVal.length() <= 200, "属性[" + def.getProName() + "]默认值超长");
                }
                break;
            case FILE:
                Assert.isTrue(BinaryUtils.isEmpty(defVal), "文件类型不支持指定默认值");
                Assert.isTrue(def.getIsMajor().intValue() != 1, "文件属性[" + def.getProName() + "]不可作为字典标识");
                break;
            case NUMBER:
                if (!BinaryUtils.isEmpty(defVal)) {
                    Assert.isTrue(
                            CheckAttrUtil.INTEGER_REGEX.matcher(defVal).matches()
                                    || CheckAttrUtil.DOUBLE_REGEX2.matcher(defVal).matches(),
                            "属性[" + def.getProName() + "]格式错误");
                }
                break;
            case COLOR:
                if (!BinaryUtils.isEmpty(defVal)) {
                    Pattern pattern = Pattern.compile("^#([0-9a-fA-F]{6}|[0-9a-fA-F]{3})$");
                    Assert.isTrue(pattern.matcher(defVal).matches(), "属性[" + def.getProName() + "]默认值格式错误");
                }
                break;
            case EXTERNAL_DICT:
                Assert.isTrue(BinaryUtils.isEmpty(defVal), "引用类型不支持指定默认值");
                Assert.isTrue(def.getCiteType().intValue() != 0, "未选择引用类型");
                // 引用类型不可作为标识属性
                Assert.isTrue(def.getIsMajor().intValue() != 1, "引用属性[" + def.getProName() + "]不可作为字典标识");
                // 校验引用字典是否存在
                if (def.getCiteType().intValue() == 1) {
                    Assert.notNull(def.getSourceDictClassId(), "X_PARAM_NOT_NULL${name:源字典分类}");
                    Assert.notNull(def.getSourceDictDefId(), "X_PARAM_NOT_NULL${name:源字典属性}");
                    ESDictionaryClassInfo sourceDictClassInfo = dictClsSvc.getById(def.getSourceDictClassId());
                    Assert.notNull(sourceDictClassInfo, "引用字典不存在");
                    Map<Long, ESDictionaryAttrDef> sourceDefsMap = BinaryUtils
                            .toObjectMap(sourceDictClassInfo.getDictAttrDefs(), "id");
                    ESDictionaryAttrDef sourceDictDef = sourceDefsMap.get(def.getSourceDictDefId());
                    Assert.isTrue(sourceDictDef != null, "引用字典属性不存在");
                    // 禁止引用文件、引用类型
                    int dictDefType = sourceDictDef.getProType().intValue();
                    Assert.isTrue(
                            ESDictionaryAttrDef.DictProTypeEnum.FILE.getType() != dictDefType
                                    && ESDictionaryAttrDef.DictProTypeEnum.EXTERNAL_DICT.getType() != dictDefType,
                            "[" + sourceDictDef.getProName() + "]不可引用不支持的属性类型");
                }
                break;
            case ENUM:
                List<String> enumValues = def.getEnumValues();
                Assert.isTrue(!BinaryUtils.isEmpty(enumValues), "X_PARAM_NOT_NULL${name:枚举值}");
                long count = enumValues.stream().distinct().count();
                Assert.isTrue(enumValues.size() == count, "枚举值有重复项");
                if (!BinaryUtils.isEmpty(defVal)) {
                    Assert.isTrue(enumValues.contains(defVal), "属性[" + def.getProName() + "]默认值错误");
                }
                break;
            case LONG_VARCHAR:
                if (!BinaryUtils.isEmpty(defVal)) {
                    Assert.isTrue(defVal.length() <= 1000, "属性[" + def.getProName() + "]默认值超长");
                }
                break;
            case CLOB:
                break;
            default:
                break;
        }
    }

    /**
     * 校验字典项各属性是否规范
     *
     * @param dictAttrDefs
     * @param item
     * @param sourceValMap
     */
    private void validDictionaryAttrs(List<ESDictionaryAttrDef> dictAttrDefs, ESDictionaryItemInfo item,
                                      Map<String, List<String>> sourceValMap) {
        Assert.isTrue(!BinaryUtils.isEmpty(dictAttrDefs), "字典定义不可为空");
        Assert.notNull(item.getDictClassId(), "所属字典id不可为空");
        List<String> majorProNames = dictAttrDefs.stream().filter(def -> def.getIsMajor() == 1)
                .map(ESDictionaryAttrDef::getProName).collect(Collectors.toList());
        Assert.isTrue(majorProNames.size() == 1, "标识属性有且至多有一个");
        Map<String, String> attrs = item.getAttrs();
        Map<String, String> stdAttrs = new HashMap<>();
        for (ESDictionaryAttrDef def : dictAttrDefs) {
            String val = attrs.get(def.getProStdName()) == null ? attrs.get(def.getProName())
                    : attrs.get(def.getProStdName());

            if (BinaryUtils.isEmpty(val)) {
                val = def.getDefVal();
            }
            if (def.getIsRequired().intValue() == 1) {
                Assert.notNull(val, "必填属性[" + def.getProName() + "]不可为空");
            }
            if (def.getIsMajor().intValue() == 1) {
                Assert.notNull(val, "标识属性[" + def.getProName() + "]不可为空");
                item.setKeyCode(val);
            }
            if (!BinaryUtils.isEmpty(val)) {
                DictProTypeEnum proType = ESDictionaryAttrDef.DictProTypeEnum.valueOf(def.getProType());
                switch (proType) {
                    case TEXT:
                        Assert.isTrue(val.length() <= 200, "属性[" + def.getProName() + "]属性值超长");
                        break;
                    case FILE:
                        if (!BinaryUtils.isEmpty(val) && val.startsWith(rsmSlaveRoot)) {
                            val = val.substring(rsmSlaveRoot.length(), val.length());
                        }
                        break;
                    case NUMBER:
                        Assert.isTrue(
                                CheckAttrUtil.INTEGER_REGEX.matcher(val).matches()
                                        || CheckAttrUtil.DOUBLE_REGEX2.matcher(val).matches(),
                                "属性[" + def.getProName() + "]格式错误");
                        break;
                    case COLOR:
                        Pattern pattern = Pattern.compile("^#([0-9a-fA-F]{6}|[0-9a-fA-F]{3})$");
                        Assert.isTrue("none".equals(val) || pattern.matcher(val).matches(),
                                "属性[" + def.getProName() + "]格式错误");
                        break;
                    case EXTERNAL_DICT:
                        List<String> sourceVals = sourceValMap.get(def.getProName());
                        // 复选框
                        if (def.getSelectType().intValue() == 1) {
                            List<String> values = JSONArray.parseArray(val, String.class);
                            if (BinaryUtils.isEmpty(values)) {
                                val = def.getDefVal();
                                values = JSONArray.parseArray(val, String.class);
                            }
                            Assert.isTrue(def.getIsRequired().intValue() != 1 || !BinaryUtils.isEmpty(values),
                                    "必填属性[" + def.getProName() + "]不可为空");
                            if (!BinaryUtils.isEmpty(values)) {
                                for (String value : values) {
                                    Assert.isTrue(
                                            item.getId() != null || BinaryUtils.isEmpty(sourceVals)
                                                    || sourceVals.contains(value),
                                            "属性[" + def.getProName() + "]引用值[" + value + "]不存在");
                                }
                            }
                        } else {
                            Assert.isTrue(
                                    item.getId() != null || BinaryUtils.isEmpty(sourceVals) || sourceVals.contains(val),
                                    "属性[" + def.getProName() + "]引用值[" + val + "]不存在");
                        }
                        break;
                    case ENUM:
                        Assert.isTrue(def.getEnumValues().contains(val), "属性[" + def.getProName() + "]属性值格式错误");
                        break;
                    case LONG_VARCHAR:
                        Assert.isTrue(val.length() <= 1000, "属性[" + def.getProName() + "]属性值超长");
                        break;
                    case CLOB:
                        break;
                    default:
                        break;
                }
            }
            Assert.isTrue(def.getIsRequired().intValue() != 1 || !BinaryUtils.isEmpty(val),
                    "必填属性[" + def.getProName() + "]不可为空");
            stdAttrs.put(def.getProStdName(), val == null ? "" : val);
        }
        // 新增字典项可编辑
        if (item.getId() == null && BinaryUtils.isEmpty(item.getOption())) {
            item.setOption(DictionaryOptionEnum.WRITE);
        }
        item.setAttrs(stdAttrs);
    }

    /**
     * 获取引用字典值(引用字典、引用分类、引用角色)
     *
     * @param dictAttrDefs
     * @return
     */
    private Map<String, List<String>> getSourceValMap(List<ESDictionaryAttrDef> dictAttrDefs) {
        Map<String, List<String>> sourceValMap = new HashMap<>();
        for (ESDictionaryAttrDef def : dictAttrDefs) {
            Long domainId = def.getDoaminId() == null ? BaseConst.DEFAULT_DOMAIN_ID : def.getDoaminId();
            if (def.getCiteType().intValue() == ESDictionaryAttrDef.DictCiteTypeEnum.DICT.getType()) {
                if (def.getSourceDictClassId() != null && def.getSourceDictDefId() != null) {
                    // 获取来源及单位引用值
                    ESDictionaryItemSearchBean bean = ESDictionaryItemSearchBean.builder()
                            .dictClassId(def.getSourceDictClassId()).dictDefId(def.getSourceDictDefId()).build();
                    List<String> values = this.getExteralDictValues(bean);
                    sourceValMap.put(def.getProName(), values);
                }
            } else if (def.getCiteType().intValue() == ESDictionaryAttrDef.DictCiteTypeEnum.CLASS.getType()) {
                List<ESCIClassInfo> list = clsSvc.getListByQuery(QueryBuilders.termQuery("domainId", domainId));
                List<String> ids = list.stream().map(cls -> cls.getId().toString()).collect(Collectors.toList());
                sourceValMap.put(def.getProName(), ids);
            } else if (def.getCiteType().intValue() == ESDictionaryAttrDef.DictCiteTypeEnum.ROLE.getType()) {
                List<SysRole> list = roleSvc.getRolesByQuery(QueryBuilders.termQuery("domainId", domainId));
                List<String> ids = list.stream().map(role -> role.getId().toString()).collect(Collectors.toList());
                sourceValMap.put(def.getProName(), ids);
            } else if (def.getCiteType().intValue() == ESDictionaryAttrDef.DictCiteTypeEnum.CUSTOM.getType()) {
                List<Map<String, String>> selectValues = def.getSelectValues();
                if (!CollectionUtils.isEmpty(selectValues)) {
                    ArrayList<String> values = new ArrayList<>();
                    for (int i = 0; i < selectValues.size(); i++) {
                        Map<String, String> map = selectValues.get(i);
                        Iterator iterator = map.keySet().iterator();
                        while (iterator.hasNext()) {
                            String key = (String) iterator.next();
                            String str = map.get(key);
                            values.add(str);
                        }
                        sourceValMap.put(def.getProName(), values);
                    }
                }
            }
        }
        return sourceValMap;
    }

    /**
     * 构建导出明细中的行明细
     *
     * @param rowNum
     * @param errorType
     * @param message
     * @return
     */
    public ImportRowMessage buildRowMessage(int rowNum, Integer errorType, String message) {
        ImportRowMessage rowMessage = new ImportRowMessage();
        rowMessage.setRowNum(rowNum);
        ImportCellMessage cellMessage = new ImportCellMessage();
        cellMessage.setErrorType(errorType);
        cellMessage.setErrorDesc(message);
        rowMessage.setMessageItems(Collections.singletonList(cellMessage));
        return rowMessage;
    }

    /**
     * 字典项引用值转换(支持id-名称转换，通过toShowVal参数控制)
     *
     * @param items
     * @param dictClsInfo
     * @param toShowVal   是否转换为显示属性
     * @return
     */
    public void transExterDcitIdToName(List<ESDictionaryItemInfo> items, ESDictionaryClassInfo dictClsInfo) {
        Assert.notNull(dictClsInfo, "X_PARAM_NOT_NULL${name:字典定义}");
        if (!BinaryUtils.isEmpty(items)) {
            List<ESDictionaryAttrDef> dictAttrDefs = dictClsInfo.getDictAttrDefs();
            Assert.isTrue(!BinaryUtils.isEmpty(dictAttrDefs), "X_PARAM_NOT_NULL${name:字典定义}");
            // 获取引用类型的属性定义
            List<ESDictionaryAttrDef> citeDefs = dictAttrDefs.stream().filter(def -> def.getProType() == ESDictionaryAttrDef.DictProTypeEnum.EXTERNAL_DICT.getType()
                    && (def.getCiteType() == ESDictionaryAttrDef.DictCiteTypeEnum.CLASS.getType()
                    || def.getCiteType() == ESDictionaryAttrDef.DictCiteTypeEnum.ROLE.getType()))
                    .collect(Collectors.toList());
            if (!BinaryUtils.isEmpty(citeDefs)) {
                // 获取引用类型值
                Map<String, Map<String, String>> exterDictValMap = this.getExterDictValMap(dictAttrDefs);
                for (ESDictionaryItemInfo item : items) {
                    Map<String, String> attrs = item.getAttrs();
                    for (ESDictionaryAttrDef def : citeDefs) {
                        String val = attrs.get(def.getProStdName());
                        if (!BinaryUtils.isEmpty(val)) {
                            Map<String, String> idToNameMap = exterDictValMap.get(def.getProName());
                            // 转换为显示名称
                            List<String> values = new ArrayList<>();
                            // 复选框
                            if (def.getSelectType() == 1) {
                                values = JSONArray.parseArray(val, String.class);
                            } else {
                                values.add(val);
                            }
                            String nameStr = values.stream().map(value -> idToNameMap.get(value))
                                    .filter(name -> !BinaryUtils.isEmpty(name)).collect(Collectors.joining(","));
                            attrs.put(def.getProStdName(), nameStr);
                        }
                    }
                }
            }
        }
    }

    /**
     * 获取引用字典值id-名称对应关系(引用分类、引用角色)
     *
     * @param dictAttrDefs
     * @return
     */
    private Map<String, Map<String, String>> getExterDictValMap(List<ESDictionaryAttrDef> dictAttrDefs) {
        Map<String, Map<String, String>> resMap = new HashMap<>();
        for (ESDictionaryAttrDef def : dictAttrDefs) {
            Long domainId = def.getDoaminId() == null ? BaseConst.DEFAULT_DOMAIN_ID : def.getDoaminId();
            if (def.getProType().intValue() == ESDictionaryAttrDef.DictProTypeEnum.EXTERNAL_DICT.getType()) {
                Map<String, String> valMap = new HashMap<>();
                resMap.put(def.getProName(), valMap);
                if (def.getCiteType().intValue() == ESDictionaryAttrDef.DictCiteTypeEnum.CLASS.getType()) {
                    List<ESCIClassInfo> list = clsSvc.getListByQuery(QueryBuilders.termQuery("domainId", domainId));
                    list.forEach(cls -> {
                        resMap.get(def.getProName()).put(cls.getId().toString(), cls.getClassName());
                    });
                } else if (def.getCiteType().intValue() == ESDictionaryAttrDef.DictCiteTypeEnum.ROLE.getType()) {
                    List<SysRole> list = roleSvc.getRolesByQuery(QueryBuilders.termQuery("domainId", domainId));
                    list.forEach(role -> {
                        resMap.get(def.getProName()).put(role.getId().toString(), role.getRoleName());
                    });
                }
            }
        }
        return resMap;
    }

    /**
     * 根据value值获取对应key
     *
     * @param map
     * @param val
     * @return
     */
    private String getKeyByValue(Map<String, String> map, String val) {
        String key = null;
        if (!BinaryUtils.isEmpty(map) && !BinaryUtils.isEmpty(val)) {
            List<String> res = map.entrySet().stream().filter(kvEntry -> Objects.equals(kvEntry.getValue(), val))
                    .map(Map.Entry::getKey).collect(Collectors.toList());
            if (!BinaryUtils.isEmpty(res)) {
                key = res.get(0);
            }
        }
        return key;
    }

    public Resource writeDataToExcelFile(String dictName, List<String> titles, Set<String> reqCellValues,
                                         List<Map<String, String>> datas) {
        Workbook workBook = new SXSSFWorkbook();
        Resource resource = null;
        Sheet sheet = FileUtil.ExcelUtil.createExcelSheetAndTitle(workBook, dictName, titles, reqCellValues, null,
                null);
        if (!BinaryUtils.isEmpty(datas)) {
            if (!BinaryUtils.isEmpty(datas)) {
                FileUtil.ExcelUtil.writeExcelComment(workBook, sheet, 1, titles, null, null, datas);
            }
        }
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        try {
            workBook.write(os);
            resource = new ByteArrayResource(os.toByteArray(),
                    FileUtil.ExcelUtil.getExportFileName(dictName, ".xlsx", true));
        } catch (IOException e) {
            log.error("导出字典数据写入输出流异常", e);
        } finally {
            try {
                os.close();
                workBook.close();
            } catch (IOException e) {
                log.error("导出字典数据关闭输出流异常", e);
            }
        }
        return resource;
    }

    public ImportResultMessage importDictItemsBatch(ESDictionaryClassInfo dictClassInfo,
                                                    List<ESDictionaryItemInfo> items, ImportSheetMessage sheetMessage) {
        ImportResultMessage result = null;
        // 引用值名称转换为id
        List<ImportRowMessage> exterDictRowMessages = this.transExterDcitNameToId(items, dictClassInfo);
        if (!BinaryUtils.isEmpty(exterDictRowMessages)) {
            sheetMessage.getRowMessages().addAll(exterDictRowMessages);
            sheetMessage.setFailNum(sheetMessage.getFailNum() + exterDictRowMessages.size());
        }
        ImportSheetMessage dictMessage = this.saveDictionaryItemsBatch(dictClassInfo.getId(), items);
        sheetMessage.setFailNum(sheetMessage.getFailNum() + dictMessage.getFailNum());
        sheetMessage.setInsertNum(dictMessage.getInsertNum());
        sheetMessage.setUpdateNum(dictMessage.getUpdateNum());
        sheetMessage.setSuccessNum(dictMessage.getSuccessNum());
        if (sheetMessage.getTotalNum() == 0) {
            sheetMessage.setTotalNum(dictMessage.getTotalNum());
        }
        if (!BinaryUtils.isEmpty(dictMessage.getRowMessages())) {
            sheetMessage.getRowMessages().addAll(dictMessage.getRowMessages());
        }
        sheetMessage.setSheetName(dictClassInfo.getDictName());
        result = FileUtil.ExcelUtil.writeSheetMessageToFile(Collections.singletonList(sheetMessage),
                dictClassInfo.getDictName() + "导入明细");
        return result;
    }

    /**
     * 转换为标准属性值
     */
    public Map<String, String> transToStdMap(Map<String, String> attrs, ESDictionaryClassInfo dictClassInfo,
                                             boolean transToShow) {
        Map<String, String> stdMap = new HashMap<>();
        Map<String, ESDictionaryAttrDef> defMap = null;
        Map<String, ESDictionaryAttrDef> defStdMap = null;
        defStdMap = BinaryUtils.toObjectMap(dictClassInfo.getDictAttrDefs(), "proStdName");
        defMap = BinaryUtils.toObjectMap(dictClassInfo.getDictAttrDefs(), "proName");
        for (Entry<String, String> entry : attrs.entrySet()) {
            String key = entry.getKey();
            if (BinaryUtils.isEmpty(entry.getValue())) {
                continue;
            }
            String value = String.valueOf(entry.getValue());
            ESDictionaryAttrDef def = defMap.containsKey(key) ? defMap.get(key) : defStdMap.get(key);
            if (def != null) {
                if (transToShow) {
                    stdMap.put(def.getProName(), value);
                } else {
                    stdMap.put(def.getProStdName(), value);
                }
            }
        }
        return stdMap;
    }

}
