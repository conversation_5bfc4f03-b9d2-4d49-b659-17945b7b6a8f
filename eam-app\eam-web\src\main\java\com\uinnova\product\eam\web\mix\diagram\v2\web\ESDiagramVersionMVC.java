package com.uinnova.product.eam.web.mix.diagram.v2.web;

import cn.hutool.crypto.SecureUtil;
import com.binary.core.exception.BinaryException;
import com.binary.core.util.BinaryUtils;
import com.binary.framework.util.ControllerUtils;
import com.binary.framework.web.RemoteResult;
import com.binary.json.JSON;
import com.binary.json.JSONObject;
import com.uinnova.product.eam.base.diagram.mix.model.VcDiagramVersionInfo;
import com.uinnova.product.eam.base.diagram.model.VcDiagramVersion;
import com.uinnova.product.eam.model.diagram.ESDiagramVersionInfo;
import com.uinnova.product.eam.web.diagram.bean.DiagramVersionInfo;
import com.uinnova.product.eam.web.mix.diagram.v2.peer.ESDiagramPeer;
import com.uinnova.product.eam.web.mix.diagram.v2.peer.ESDiagramVersionPeer;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uinnova.product.vmdb.comm.doc.annotation.MvcDesc;
import com.uinnova.product.vmdb.comm.util.SystemUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.List;

@RestController
@RequestMapping("/eam/historyVersion")
@MvcDesc(author = "wang", desc = "提供视图历史增删改查接口")
public class ESDiagramVersionMVC {

//    @Autowired
//    private DiagramVersionPeer diagramVersionPeer;

    @Autowired
    private ESDiagramPeer esDiagramPeer;

    @Autowired
    private ESDiagramVersionPeer esDiagramVersionPeer;

    @RequestMapping("/restoreDiagramByVersionId")
    @ModDesc(desc = "根据版本id还原历史视图，覆盖当前视图", pType = Long.class, pDesc = "历史版本id", rDesc = "结果", rType = Integer.class)
    public RemoteResult restoreDiagramByVersionId(@RequestBody String body) {
		/*
		 	根据历史版本替代当前版本
		 	1.更改当前视图的历史版本标识为0，当前历史版本为1
		 	2.将当前视图的分享记录过渡给历史版本
		 	3.为历史版本生成名字
		 	传参：当前视图id，历史版本id
		 * */
        JSONObject jsonObject = new JSONObject(body);
        String currDiagramId = jsonObject.getString("currDiagramId");
        String historyVersionId = jsonObject.getString("historyVersionId");
        if (currDiagramId == null || historyVersionId == null) {
            throw new BinaryException("视图id和或版本id不能为空");
        }
        Long newDiagramId;
        Long aLong = esDiagramPeer.queryDiagramInfoByEnergy(currDiagramId);
        Long versionId = esDiagramPeer.queryDiagramInfoByEnergy(historyVersionId);
        newDiagramId = esDiagramVersionPeer.restoreDiagramByVersionId(aLong, versionId);
        return new RemoteResult(SecureUtil.md5(String.valueOf(newDiagramId)).substring(8, 24));
    }

    @RequestMapping("/createDiagramByCurrVersion")
    @ModDesc(desc = "根据当前版本新建视图", pType = Long.class, pDesc = "历史版本id", rDesc = "结果", rType = Integer.class)
    public RemoteResult createDiagramByCurrVersion(@RequestBody String jsonStr) {
        cn.hutool.json.JSONObject jsonObject = new cn.hutool.json.JSONObject(jsonStr);
        Boolean versionFlag = jsonObject.getBool("versionFlag");
        String versionId1 = jsonObject.getStr("versionId");
        if (versionId1 == null || versionFlag == null) {
            throw new BinaryException("版本标识和版本id均不能为空");
        }
        Long newDiagramId;
        Long versionId = esDiagramPeer.queryDiagramInfoByEnergy(versionId1);
        newDiagramId = esDiagramVersionPeer.createDiagramByCurrVersion(versionId, versionFlag);
        return new RemoteResult(SecureUtil.md5(String.valueOf(newDiagramId)).substring(8, 24));
    }

    @PostMapping("/queryDiagramVersionById")
    @ModDesc(desc = "根据id查询视图历史版本", pType = Long.class, pDesc = "版本id", rDesc = "版本信息", rType = VcDiagramVersionInfo.class)
    public RemoteResult queryDiagramVersionById(HttpServletRequest request, HttpServletResponse response, @RequestBody String body) {
        if(BinaryUtils.isEmpty(body)){
            throw new BinaryException("传参不能为空");
        }
        com.alibaba.fastjson.JSONObject jsonObject = com.alibaba.fastjson.JSONObject.parseObject(body);
        //true--查询的当前版本，false--查询的历史版本
        Boolean versionFlag = jsonObject.getBoolean("versionFlag");
        ESDiagramVersionInfo result;
        String dId = jsonObject.getString("versionId");
        Object param1 = jsonObject.get("hasUserInfo");
        Object param2 = jsonObject.get("needAuth");
        Boolean hasUserInfo = BinaryUtils.isEmpty(param1) ? Boolean.FALSE : (Boolean) param1;
        Boolean needAuth = BinaryUtils.isEmpty(param2) ? Boolean.TRUE : (Boolean) param2;
        Long id = esDiagramPeer.queryDiagramInfoByEnergy(dId);
        result = esDiagramVersionPeer.queryDiagramVersionById(id, versionFlag, hasUserInfo, needAuth);
        return new RemoteResult(result);
    }

    @RequestMapping("/queryDiagramVersionByDiagramId")
    @ModDesc(desc = "根据视图id查询视图历史版本列表", pType = Long.class, pDesc = "视图id", rDesc = "版本列表信息", rType = List.class, rcType = DiagramVersionInfo.class)
    public RemoteResult queryDiagramVersionByDiagramId(HttpServletRequest request, HttpServletResponse response, @RequestBody String body) {
        String diagramId = JSON.toObject(body, String.class);
        if (StringUtils.isEmpty(diagramId)) {
            throw new BinaryException("传参错误，视图id不能为空");
        }
        Long aLong = esDiagramPeer.queryDiagramInfoByEnergy(diagramId);
        List<DiagramVersionInfo> result = esDiagramVersionPeer.queryDiagramVersionByDiagramId(aLong);
        return new RemoteResult(result);
    }

	/*@RequestMapping("/queryDiagramVersionByDiagramId")
	@ModDesc(desc = "根据视图id查询视图历史版本列表", pType=Long.class,pDesc = "视图id", rDesc = "版本列表信息", rType = List.class,rcType= DiagramVersionInfo.class)
	public void queryDiagramVersionByDiagramId(HttpServletRequest request, HttpServletResponse response, @RequestBody String body){
		Long diagramId = JSON.toObject(body, Long.class);
		List<DiagramVersionInfo> result = diagramVersionPeer.queryDiagramVersionByDiagramId(diagramId);
		ControllerUtils.returnJson(request, response, result);
	}*/

    @RequestMapping("/updateDiagramVersionDesc")
    @ModDesc(desc = "根据历史版本id修改版本描述信息", pType = VcDiagramVersion.class, pDesc = "历史版本id和版本描述信息", rDesc = "结果", rType = Integer.class)
    public void updateDiagramVersionDescAndVersionNo(HttpServletRequest request, HttpServletResponse response, @RequestBody String body) {
        VcDiagramVersion record = JSON.toObject(body, VcDiagramVersion.class);

        // Integer result = diagramVersionPeer.updateDiagramVersionDescAndVersionNo(record.getId(), record.getVersionDesc(), record.getVersionNo(), record.getName(), record.getVersionTime());
        ControllerUtils.returnJson(request, response, null);
    }

    @RequestMapping("/removeDiagramVersionById")
    @ModDesc(desc = "根据历史版本id删除历史版本", pType = Long.class, pDesc = "历史版本id", rDesc = "结果", rType = Integer.class)
    public void removeDiagramVersionById(HttpServletRequest request, HttpServletResponse response, @RequestBody String body) {
        Long id = JSON.toObject(body, Long.class);
        String token = SystemUtil.getToken(request);
        // Integer result = diagramVersionPeer.removeDiagramVersionById(id, token);
        ControllerUtils.returnJson(request, response, null);
    }
}
