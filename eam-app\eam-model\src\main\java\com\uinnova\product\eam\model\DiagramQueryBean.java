package com.uinnova.product.eam.model;

import com.uinnova.product.eam.comm.model.CVcDiagram;

public class DiagramQueryBean extends CVcDiagram {

    private Boolean byUser;

    private Integer pageNum;

    private Integer pageSize;

    public Boolean getByUser() {
        return byUser;
    }

    public void setByUser(Boolean byUser) {
        this.byUser = byUser;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}
