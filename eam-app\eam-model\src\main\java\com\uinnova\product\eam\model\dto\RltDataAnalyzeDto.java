package com.uinnova.product.eam.model.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.uinnova.product.eam.model.vo.EamAnalyseCiVo;
import com.uinnova.product.vmdb.comm.model.rlt.CcCiRlt;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import lombok.Data;

import java.io.Serializable;

/**
 *  数据分析Dto
 * <AUTHOR>
 */
@Data
public class RltDataAnalyzeDto implements Serializable {
    @JsonIgnore
    private String rltCode;
    @JsonIgnore
    private String ciCode;
    private Integer sourceCount = 0;
    private Integer targetCount = 0;

    private CcCiRlt rltInfo;
    private CcCiClassInfo rltClassInfo;

    private EamAnalyseCiVo nodeInfo;

}
