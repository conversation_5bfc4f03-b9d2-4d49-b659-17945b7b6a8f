package com.uino.api.client.cmdb.rpc;

import java.util.List;

import com.uino.dao.BaseConst;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.uino.bean.cmdb.base.ESCITreeConfigInfo;
import com.uino.bean.cmdb.business.CITreeNode;
import com.uino.provider.feign.cmdb.CITreeFeign;
import com.uino.api.client.cmdb.ICITreeApiSvc;

@Service
public class CITreeApiSvcRpc implements ICITreeApiSvc {

    @Autowired
    private CITreeFeign ciTreeFeign;

    @Override
    public List<ESCITreeConfigInfo> getCITreeConfigs() {
        return ciTreeFeign.getCITreeConfigs(BaseConst.DEFAULT_DOMAIN_ID);
    }

    @Override
    public List<ESCITreeConfigInfo> getCITreeConfigs(Long domainId) {
        return ciTreeFeign.getCITreeConfigs(domainId);
    }

    @Override
    public ESCITreeConfigInfo getCITreeConfig(Long id) {
        return ciTreeFeign.getCITreeConfig(id);
    }

    @Override
    public ESCITreeConfigInfo save(ESCITreeConfigInfo saveInfo) {
        return ciTreeFeign.save(saveInfo);
    }

    @Override
    public List<ESCITreeConfigInfo> delete(Long id) {
        return ciTreeFeign.delete(BaseConst.DEFAULT_DOMAIN_ID, id);
    }

    @Override
    public List<ESCITreeConfigInfo> delete(Long domainId, Long id) {
        return ciTreeFeign.delete(domainId, id);
    }

    @Override
    public CITreeNode getCITree(ESCITreeConfigInfo config, boolean hasNullNode, boolean returnAllCI) {
        return ciTreeFeign.getCITree(config, hasNullNode, returnAllCI);
    }
}
