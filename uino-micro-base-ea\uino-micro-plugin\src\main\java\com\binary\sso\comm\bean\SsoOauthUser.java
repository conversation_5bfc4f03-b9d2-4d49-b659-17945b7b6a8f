package com.binary.sso.comm.bean;

import com.binary.framework.bean.SimpleUser;

public class SsoOauthUser extends SimpleUser {
	private static final long serialVersionUID = 1L;
		
	
	/** 客户端IP **/
	private String clientIp;
	
	
	/** token创建时间, 单位:毫秒 **/
	private Long tokenCreateTime;
	
	
	/** token有效时间, 单位:分钟 **/
	private Integer tokenValidTime;	
	

	
	@Override
	public String getUserCode() {
		String code = super.getUserCode();
		if(code == null) code = getLoginCode();
		return code;
	}
	

	public String getClientIp() {
		return clientIp;
	}


	public void setClientIp(String clientIp) {
		this.clientIp = clientIp;
	}


	public Long getTokenCreateTime() {
		return tokenCreateTime;
	}


	public void setTokenCreateTime(Long tokenCreateTime) {
		this.tokenCreateTime = tokenCreateTime;
	}


	public Integer getTokenValidTime() {
		return tokenValidTime;
	}


	public void setTokenValidTime(Integer tokenValidTime) {
		this.tokenValidTime = tokenValidTime;
	}


	
	
	

}
