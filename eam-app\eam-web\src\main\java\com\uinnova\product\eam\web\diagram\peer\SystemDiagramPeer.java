package com.uinnova.product.eam.web.diagram.peer;

import com.binary.jdbc.Page;
import com.uinnova.product.eam.base.diagram.model.ESDiagram;
import com.uinnova.product.eam.base.diagram.model.ESSimpleDiagramDTO;
import com.uinnova.product.eam.comm.model.CVcDiagramDir;
import com.uinnova.product.eam.comm.model.VcDiagramDir;
import com.uinnova.product.eam.model.DiagramBo;
import com.uinnova.product.eam.model.PlanDiagramRequest;
import com.uinnova.product.eam.model.vo.CiDirVo;
import com.uinnova.product.eam.model.vo.DiagramFieldVo;
import com.uinnova.product.eam.model.vo.RelateDiagram;
import com.uinnova.product.eam.model.vo.RelateionDiagramVo;
import com.uinnova.product.vmdb.comm.model.ci.CcCi;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uino.bean.cmdb.base.ESCIAttrDefInfo;
import com.uino.bean.permission.base.SysUser;

import java.util.List;
import java.util.Map;


public interface SystemDiagramPeer {

	/**
	 * 系统文件夹详情
	 * @param dirId
	 * @return
	 */
    CiDirVo getCiDirInfo(Long dirId);

	/**
	 * 名称模糊分页查询已发布的视图
	 * @param request {@link PlanDiagramRequest}
	 * @return 基于这个对象 {@link ESDiagram}
	 */
	Page<Map<String, Object>> findDiagramLikeName(PlanDiagramRequest request);

	/**
	 * 通过筛选条件获取视图列表
	 * @param request
	 */
	Page<DiagramBo> findDiagramList(PlanDiagramRequest request);

	/**
	 * 发布方案
	 * @param sysIdList
	 * @return
	 */
	@Deprecated
	boolean releasePlan(List<Long> sysIdList);

    /**
     * 获取ci
     * @param ciCode
     * @return
     */
    CcCiInfo getCiByCiCode(String ciCode);

	List<CcCi> getBathCi(String[] ciCodes);

	/**
	 * 查询我发布的视图列表
	 * @param sysUser
	 * @return
	 */
	List<ESSimpleDiagramDTO> findMyPublishDiagramList(SysUser sysUser);

	List<Map<String, Object>> findDiagramByCdt(PlanDiagramRequest request);

	/**
	 * 查询分类定义列表
	 * @param classId
	 * @return
	 */
	List<ESCIAttrDefInfo> findAttrDefList(Long classId);

	List<ESDiagram> findSimpleDiagramByCdt(PlanDiagramRequest request);

	/**
	 * 视图类型
	 * @return
	 */
    List<DiagramFieldVo> findDiagramFieldList();

	/**
	 * 获取设计库文件夹信息
	 * @param cdt
	 * @return
	 */
	List<VcDiagramDir> findDiagramDirList(CVcDiagramDir cdt, SysUser sysUser);

	/**
	 * 查询引用视图（方案中引用视图、视图中关联视图）
	 * @param params 查询参数
	 * @return
	 */
	Page<RelateDiagram> queryRelateDiagramList(RelateionDiagramVo params);
}
