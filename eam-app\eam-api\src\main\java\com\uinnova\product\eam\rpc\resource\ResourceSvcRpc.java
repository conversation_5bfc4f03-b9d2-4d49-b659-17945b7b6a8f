package com.uinnova.product.eam.rpc.resource;

import com.uinnova.product.eam.api.IResourceAPIClient;
import com.uinnova.product.eam.base.model.FileResourceMeta;
import com.uinnova.product.eam.base.model.ResourceInfo;
import com.uinnova.product.eam.feign.client.EamResourceClient;
import com.uinnova.product.eam.model.vo.DefaultFileVo;
import com.uino.bean.permission.base.SysUser;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;
import java.util.List;

@Service
public class ResourceSvcRpc implements IResourceAPIClient {
    @Resource
    private EamResourceClient eamResourceClient;

    @Override
    public List<ResourceInfo> upload(MultipartFile[] files, SysUser sysUser){
        return eamResourceClient.upload(files, sysUser);
    }

    @Override
    public List<FileResourceMeta> download(List<Long> ids){
        return eamResourceClient.download(ids);
    }

    @Override
    public List<ResourceInfo> uploadFileByType(MultipartFile[] files, Integer type) {
        return eamResourceClient.uploadFileByType(files, type);
    }

    @Override
    public Integer deleteResource(Long id) {
        return eamResourceClient.deleteResource(id);
    }

    @Override
    public List<DefaultFileVo> getImages(Integer type) {
        return eamResourceClient.getImages(type);
    }

}
