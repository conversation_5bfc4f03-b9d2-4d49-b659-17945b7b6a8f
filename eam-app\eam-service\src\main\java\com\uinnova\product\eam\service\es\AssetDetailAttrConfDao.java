package com.uinnova.product.eam.service.es;

import com.uinnova.product.eam.comm.model.es.AssetDetailAttrConf;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;

/**
 * 详情配置
 * <AUTHOR>
 */
@Service
public class AssetDetailAttrConfDao extends AbstractESBaseDao<AssetDetailAttrConf, AssetDetailAttrConf> {
    @Override
    public String getIndex() {
        return "uino_eam_asset_detail_attr_conf";
    }

    @Override
    public String getType() {
        return "uino_eam_asset_detail_attr_conf";
    }
    @PostConstruct
    public void init() {
        super.initIndex();
    }
}
