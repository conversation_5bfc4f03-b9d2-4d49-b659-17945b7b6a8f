package com.uinnova.product.eam.workable.listener;

import com.uinnova.product.eam.workable.util.HttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.ExecutionListener;
import org.flowable.engine.runtime.ProcessInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

/**
 * 修改技术方案视图状态回调监听器
 *
 * <AUTHOR>
 * @since 2022/4/1 10:44u
 */
@Slf4j
@Component("updateDiagramStatusListener")
public class UpdateDiagramStatusListener implements ExecutionListener {

    @Autowired
    private RuntimeService runtimeService;

    @Value("${cj.update.diagram.status}")
    private String changeDiagramUrl;

    @Resource
    private HttpUtil httpUtil;


    @Override
    public void notify(DelegateExecution execution) {
        log.info("执行修改状态执行器");
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                .processInstanceId(execution.getProcessInstanceId()).singleResult();
        if (processInstance != null) {
            log.info("技术方案id：{}", processInstance.getBusinessKey());
            changeDiagramStatus(processInstance.getBusinessKey());
        }
    }

    private String changeDiagramStatus(String businessKey) {
        String requestUrl = changeDiagramUrl + "?planId=" + businessKey + "&status=" + 1;
        log.info("技术方案请求url：{}", requestUrl);
        return httpUtil.get(requestUrl, String.class);
    }
}
