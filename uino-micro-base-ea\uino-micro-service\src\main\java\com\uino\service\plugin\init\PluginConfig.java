package com.uino.service.plugin.init;

import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @Description
 * @Date 2021/9/27
 * @Version 1.0
 */
@Configuration
public class PluginConfig {

    @Bean
    @ConditionalOnMissingBean(PluginDistributionManage.class)
    public PluginDistributionManage pluginManageInterface(){
        return new DefaultPluginDistributionManage();
    }
}
