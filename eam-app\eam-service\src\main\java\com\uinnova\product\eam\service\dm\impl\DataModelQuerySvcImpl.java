package com.uinnova.product.eam.service.dm.impl;

import com.binary.core.util.BinaryUtils;
import com.binary.jdbc.Page;
import com.google.common.collect.Lists;
import com.uinnova.product.eam.base.util.EamUtil;
import com.uinnova.product.eam.config.Env;
import com.uinnova.product.eam.model.dm.DataModelAttribute;
import com.uinnova.product.eam.model.dm.DataModelStaticName;
import com.uinnova.product.eam.model.dm.bean.AttrAndCiDto;
import com.uinnova.product.eam.model.dm.bean.DataStandardTypeDto;
import com.uinnova.product.eam.model.dm.bean.EntityParamDto;
import com.uinnova.product.eam.service.ICISwitchSvc;
import com.uinnova.product.eam.service.IEamArtifactSvc;
import com.uinnova.product.eam.service.IEamCIClassApiSvc;
import com.uinnova.product.eam.service.dm.DataModelQuerySvc;
import com.uinnova.product.eam.service.impl.IamsCIRltSwitchSvc;
import com.uinnova.product.vmdb.comm.model.ci.CcCi;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uinnova.product.vmdb.provider.rlt.bean.CcCiRltInfo;
import com.uino.api.client.cmdb.IRltClassApiSvc;
import com.uino.bean.cmdb.base.ESCIClassInfo;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.base.LibType;
import com.uino.bean.cmdb.query.ESCISearchBean;
import com.uino.bean.cmdb.query.ESRltSearchBean;
import com.uino.util.sys.SysUtil;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */

@Service
public class DataModelQuerySvcImpl implements DataModelQuerySvc {

    @Resource
    private IRltClassApiSvc rltClassApiSvc;

    @Resource
    private IEamCIClassApiSvc ciClassApiSvc;

    @Resource
    private ICISwitchSvc iciSwitchSvc;

    @Resource
    private IamsCIRltSwitchSvc rltSwitchSvc;

    @Resource
    private IEamArtifactSvc artifactSvc;
    private static final String TOP_CODE = "0";
    @Override
    public Page<AttrAndCiDto> selectEntityAttrList(EntityParamDto dto) {
        Long domainId = SysUtil.getCurrentUserInfo().getDomainId();
        String classCode = getEntityCode(dto.getClassName());
        Page<AttrAndCiDto> result = new Page<>(dto.getPageNum(), dto.getPageSize(), 0, 0, new ArrayList<>());
        //根据分类的classCode查到分类信息，进而拿到分类id(classId)
        ESCIClassInfo entityClass = ciClassApiSvc.getCIClassByCode(classCode);
        if (BinaryUtils.isEmpty(entityClass) || BinaryUtils.isEmpty(entityClass.getAttrDefs())) {
            return result;
        }
        ESCIClassInfo attrClass = ciClassApiSvc.getCIClassByCode(Env.ATTRIBUTES.getCode());
        ESCIClassInfo standClass = ciClassApiSvc.getCIClassByCode(Env.STANDARD.getCode());

        Map<String, String> shapeAndCodeMap = new HashMap<>();
        String productId = dto.getProductId();
        if (!BinaryUtils.isEmpty(productId)) {
            shapeAndCodeMap = artifactSvc.getArtifactClassShapeById(Long.valueOf(productId));
        }
        //全量实体信息
        ESCISearchBean bean = EamUtil.copy(dto, ESCISearchBean.class);
        bean.setDomainId(domainId);
        bean.setClassIds(Collections.singletonList(entityClass.getId()));
        if(!BinaryUtils.isEmpty(dto.getWord())){
            bean.setWords(Collections.singletonList(dto.getWord()));
        }
        Page<ESCIInfo> dataPage = iciSwitchSvc.searchESCIByBean(bean, dto.getLibType());
        result.setTotalRows(dataPage.getTotalRows());
        result.setTotalPages(dataPage.getTotalPages());
        List<ESCIInfo> data = dataPage.getData();
        if (BinaryUtils.isEmpty(data)) {
            return result;
        }
        String entityCode = entityClass.getClassCode();
        String entityShape = BinaryUtils.isEmpty(shapeAndCodeMap.get(entityCode)) ? entityClass.getShape() : shapeAndCodeMap.get(entityCode);
        String entityAttrCode = attrClass.getClassCode();
        String entityAttrShape = BinaryUtils.isEmpty(shapeAndCodeMap.get(entityAttrCode)) ? attrClass.getShape() : shapeAndCodeMap.get(entityAttrCode);

        for (ESCIInfo info : data) {
            info.setShape(entityShape);
        }
        //封装返回体
        List<AttrAndCiDto> dtoList = new ArrayList<>();
        //根据实体ciCode 重新组合关系数据成map
        Map<String, List<CcCiInfo>> entityGroup = queryEntityAttrs(data, entityClass.getId(), attrClass.getId(), dto.getOwnerCode(), dto.getLibType());
        List<ESCIInfo> entityData = data.stream().sorted(Comparator.comparing(ESCIInfo::getModifyTime).reversed()).collect(Collectors.toList());
        for (ESCIInfo datum : entityData) {
            String entityCiCode = datum.getCiCode();
            if (BinaryUtils.isEmpty(entityCiCode)) {
                continue;
            }
            //拿实体ciCode匹配
            List<CcCiInfo> attrList = entityGroup.getOrDefault(entityCiCode, Lists.newArrayList());
            AttrAndCiDto ciDto = new AttrAndCiDto();
            ciDto.setEntityCi(datum);
            ciDto.setEntityAttrDefs(entityClass.getCcAttrDefs());
            ciDto.getEntityCi().setShape(entityShape);
            List<ESCIInfo> attrCiList = new ArrayList<>();
            for (CcCiInfo attrCiInfo : attrList) {
                ESCIInfo esciInfo = EamUtil.coverCiInfo(attrCiInfo);
                esciInfo.setShape(entityAttrShape);
                Object standard = esciInfo.getAttrs().get("数据标准");
                if(!BinaryUtils.isEmpty(standard)){
                    esciInfo.setShape(entityAttrShape);
                }
                attrCiList.add(esciInfo);
            }
            ciDto.setAttrAttrDefs(attrClass.getCcAttrDefs());
            ciDto.setAttrCiList(attrCiList);
            ciDto.setCount(attrList.size());
            dtoList.add(ciDto);
        }
        result.setData(dtoList);
        return result;
    }

    /**
     * 获取实体分类标识
     * @param className 实体分类名称
     * @return 分类标识
     */
    private String getEntityCode(String className){
        if (className.equals(Env.CONCEPTION_ENTITY.getName())) {
            return Env.CONCEPTION_ENTITY.getCode();
        }else if (className.equals(Env.LOGIC_ENTITY.getName())) {
            return Env.LOGIC_ENTITY.getCode();
        }else if (className.equals(Env.PHYSICAL_ENTITY.getName())) {
            return Env.PHYSICAL_ENTITY.getCode();
        }
        return null;
    }

    /**
     * 根据关系查询实体包含的属性
     * @param data 实体集合
     * @param entityClassId 实体分类id
     * @param attrClassId 实体属性分类id
     * @return 实体包含实体属性map
     */
    public Map<String, List<CcCiInfo>> queryEntityAttrs(List<ESCIInfo> data, Long entityClassId, Long attrClassId, String ownerCode, LibType libType){
        Long domainId = SysUtil.getCurrentUserInfo().getDomainId();
        Map<String, List<CcCiInfo>> result = new HashMap<>(16);
        //根据源端关系信息查出目标端信息----进行数量展示
        CcCiClassInfo rltClass = rltClassApiSvc.getRltClassByName(domainId, Env.DM_INCLUDE);
        if (BinaryUtils.isEmpty(rltClass)) {
            //没有属性分类的话，直接返回实体数据，属性数量都为0
            return result;
        }
        ESRltSearchBean rltSearchBean = new ESRltSearchBean();
        if (LibType.PRIVATE.equals(libType)) {
            rltSearchBean.setOwnerCode(ownerCode);
        }
        Set<String> sourceCiCodes = data.stream().map(CcCi::getCiCode).collect(Collectors.toSet());
        rltSearchBean.setSourceCiCodes(sourceCiCodes);
        rltSearchBean.setRltClassIds(Lists.newArrayList(rltClass.getCiClass().getId()));
        rltSearchBean.setSourceClassIds(Collections.singletonList(entityClassId));
        rltSearchBean.setTargetClassIds(Collections.singletonList(attrClassId));
        //根据实体ciCode,查出对应得关系数据，取出所有得目标端 属性信息进行组合；
        List<CcCiRltInfo> rltList = rltSwitchSvc.searchRltByScroll(rltSearchBean, libType);
        //当关系数据不存在时，将实体的个数设置成0；
        if (BinaryUtils.isEmpty(rltList)) {
            return result;
        }
        //根据实体ciCode 重新组合关系数据成map
        for (CcCiRltInfo rltInfo : rltList) {
            String entityCode = rltInfo.getCiRlt().getSourceCiCode();
            result.putIfAbsent(entityCode, new ArrayList<>());
            result.get(entityCode).add(rltInfo.getTargetCiInfo());
        }
        return result;
    }

    @Override
    public List<DataStandardTypeDto> selectDataStandardList(String type, LibType libType) {
        String key = "standard".equals(type)?DataModelAttribute.BELONG_CLASS : DataModelAttribute.BELONG_DOMAIN;
        String dirCode = "standard".equals(type)?Env.STANDARD_CLASS.getCode() : Env.DOMAIN_CLASS.getCode();
        String dataCode = "standard".equals(type)?Env.STANDARD.getCode() : Env.DOMAIN.getCode();
        //数据分类
        CcCiClassInfo dirClass = ciClassApiSvc.getCIClassByCodes(dirCode);
        //数据
        CcCiClassInfo dataClass = ciClassApiSvc.getCIClassByCodes(dataCode);
        if(BinaryUtils.isEmpty(dirClass)){
            return Lists.newArrayList();
        }
        List<ESCIInfo> dirList = iciSwitchSvc.getCiByClassIds(Collections.singletonList(dirClass.getCiClass().getId()), null, libType);
        //父级分类为key，把有值的下一级待查的域组查出来
        Map<String, List<ESCIInfo>> dirGroup = new HashMap<>();
        for (ESCIInfo each : dirList) {
            Object parent = each.getAttrs().get(DataModelStaticName.PARENT);
            if(parent == null){
                continue;
            }
            dirGroup.computeIfAbsent(parent.toString(), e -> new ArrayList<>()).add(each);
        }
        List<ESCIInfo> dataList = iciSwitchSvc.getCiByClassIds(Collections.singletonList(dataClass.getCiClass().getId()), null, libType);
        //数据标准:key = 所属父级的  ciCode
        Map<String, List<ESCIInfo>> dataGroup = new HashMap<>();
        for (ESCIInfo each : dataList) {
            Object obj = each.getAttrs().get(key);
            if(obj == null){
                continue;
            }
            dataGroup.computeIfAbsent(obj.toString(), e -> new ArrayList<>()).add(each);
        }
        String className = "standard".equals(type)?DataModelStaticName.CLASS_NAME : DataModelStaticName.CNAME;
        List<ESCIInfo> resultCiList = dirGroup.getOrDefault(TOP_CODE, Lists.newArrayList());
        List<DataStandardTypeDto> result = this.coverToDTO(resultCiList, className);
        getChildType(result, dirGroup, dataGroup, dataClass, className);
        return result;
    }

    /***
     * 获取子级目录及数据集合
     * @param dirList 目录ci集合
     * @param dataGroup 查询的数据标准类型的map信息
     * @param dataClass 数据标准信息
     */
    public void getChildType(List<DataStandardTypeDto> dirList, Map<String, List<ESCIInfo>> dirGroup, Map<String, List<ESCIInfo>> dataGroup, CcCiClassInfo dataClass, String className) {
        if(CollectionUtils.isEmpty(dirList)){
            return;
        }
        List<DataStandardTypeDto> childDirList = new ArrayList<>();
        for (DataStandardTypeDto dir : dirList) {
            List<ESCIInfo> dataCiList = dataGroup.getOrDefault(dir.getCiCode(), Lists.newArrayList());
            dir.setCount(dataCiList.size());
            List<DataStandardTypeDto> dataList = EamUtil.copy(dataCiList, DataStandardTypeDto.class);
            dataList.forEach(each -> {
                each.setAttrDefs(dataClass.getAttrDefs());
                each.setShape(dataClass.getCiClass().getShape());
            });
            List<ESCIInfo> childList = dirGroup.getOrDefault(dir.getCiCode(), Lists.newArrayList());
            List<DataStandardTypeDto> coverList = coverToDTO(childList, className);
            coverList.addAll(dataList);
            dir.setChildList(coverList);
            childDirList.addAll(coverList);
        }
        this.getChildType(childDirList, dirGroup, dataGroup, dataClass, className);
    }

    private List<DataStandardTypeDto> coverToDTO(List<ESCIInfo> ciList, String className){
        List<DataStandardTypeDto> result = new ArrayList<>();
        for (ESCIInfo each : ciList) {
            DataStandardTypeDto dir = new DataStandardTypeDto();
            dir.setIsDir(true);
            dir.setCiCode(each.getCiCode());
            dir.setStandClassName(each.getAttrs().get(className).toString());
            dir.setParentCode(each.getAttrs().get(DataModelStaticName.PARENT).toString());
            dir.setLvl(Integer.parseInt(each.getAttrs().get(DataModelStaticName.LVL).toString()));
            result.add(dir);
        }
        return result;
    }

}
