package com.uinnova.product.vmdb.comm.model.ci;

import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.vmdb.comm.bean.CIState;

/**
 * 
 * <AUTHOR>
 *
 */
@Comment("CI表[CC_CI]")
public class CcCi implements EntityBean {
    private static final long serialVersionUID = 1L;

    @Comment("ID[ID]")
    private Long id;

    @Comment("CI代码[CI_CODE]")
    private String ciCode;

    @Comment("CI描述[CI_DESC]")
    private String ciDesc;

    @Comment("所属分类[CLASS_ID]")
    private Long classId;

    @Comment("来源[SOURCE_ID]")
    private Long sourceId;

    @Comment("所有者代码[OWNER_CODE]")
    private String ownerCode;

    @Comment("所属组织[ORG_ID]")
    private Long orgId;

    @Comment("子类型[SUB_CLASS]")
    private String subClass;

    @Comment("CI版本[CI_VERSION]")
    private String ciVersion;

    @Comment("CI业务主键值的hashcode[HASH_CODE]")
    private Integer hashCode;
    
    @Comment("当前CI所有业务主键值集合[JSON格式字符串][CI_PRIMARY_KEY]")
    private String ciPrimaryKey;

    @Comment("备用_1[CUSTOM_1]")
    private String custom1;

    @Comment("备用_2[CUSTOM_2]")
    private String custom2;

    @Comment("备用_3[CUSTOM_3]")
    private String custom3;

    @Comment("备用_4[CUSTOM_4]")
    private String custom4;

    @Comment("备用_5[CUSTOM_5]")
    private String custom5;

    @Comment("备用_6[CUSTOM_6]")
    private String custom6;

    @Comment("CI属性[ATTRS_STR]")
    private String attrsStr;

    @Comment("所属域[DOMAIN_ID]")
    private Long domainId;

    @Comment("数据状态[DATA_STATUS]    1-正常 0-删除")
    private Integer dataStatus;

    @Comment("创建人[CREATOR]")
    private String creator;

    @Comment("修改人[MODIFIER]")
    private String modifier;

    @Comment("创建时间[CREATE_TIME]    yyyyMMddHHmmss")
    private Long createTime;

    @Comment("修改时间[MODIFY_TIME]    yyyyMMddHHmmss")
    private Long modifyTime;

    @Comment("当前CI所有Label值集合,有序[JSON格式字符串,且与属性定义顺序一致],仅查询展示使用")
    private String ciLabel;

    @Comment("所属视图ID[DIAGRAM_ID]")
    private String diagramId;

    @Comment("所属视图分页ID[SHEET_ID]")
    private String sheetId;

    @Comment("数据状态:默认:0-草稿态;1-审核中;2-生效")
    private int state = CIState.CREATE_COMPLETE.val();

    @Comment("本地版本[LOCAL_VERSION]")
    private long localVersion = 0;

    @Comment("发布后版本[PUBLIC_VERSION]")
    private long publicVersion = 0;

    @Comment("运行库版本[PUBLIC_VERSION]")
    private String releaseCiCode;

    @Comment("ci分类的shape")
    private String shape;

    @Comment("流程组编码")
    private String processCoding;

    @Comment("流程审批状态:未发布:0  审批中:1  待发布:2  已发布:3  升版中:4 升班审批中:5")
    private int processApprovalStatus = 0;

    @Comment("流程能否编辑的状态:可编辑:0  不可编辑:1 ")
    private int updateStatus = 0;

    public int getUpdateStatus() {
        return updateStatus;
    }

    public void setUpdateStatus(int updateStatus) {
        this.updateStatus = updateStatus;
    }

    public int getProcessApprovalStatus() {
        return processApprovalStatus;
    }

    public void setProcessApprovalStatus(int processApprovalStatus) {
        this.processApprovalStatus = processApprovalStatus;
    }

    public String getSheetId() {
        return sheetId;
    }

    public void setSheetId(String sheetId) {
        this.sheetId = sheetId;
    }

    public String getShape() {
        return shape;
    }

    public void setShape(String shape) {
        this.shape = shape;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCiCode() {
        return ciCode;
    }

    public void setCiCode(String ciCode) {
        this.ciCode = ciCode;
    }

    public String getCiDesc() {
        return ciDesc;
    }

    public void setCiDesc(String ciDesc) {
        this.ciDesc = ciDesc;
    }

    public Long getClassId() {
        return classId;
    }

    public void setClassId(Long classId) {
        this.classId = classId;
    }

    public Long getSourceId() {
        return sourceId;
    }

    public void setSourceId(Long sourceId) {
        this.sourceId = sourceId;
    }

    public String getOwnerCode() {
        return ownerCode;
    }

    public void setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
    }

    public Long getOrgId() {
        return orgId;
    }

    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    public String getSubClass() {
        return subClass;
    }

    public void setSubClass(String subClass) {
        this.subClass = subClass;
    }

    public String getCiVersion() {
        return ciVersion;
    }

    public void setCiVersion(String ciVersion) {
        this.ciVersion = ciVersion;
    }

    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }

    public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }

    public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }

    public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }

    public String getCustom5() {
        return custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }

    public String getCustom6() {
        return custom6;
    }

    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }

    public String getAttrsStr() {
        return attrsStr;
    }

    public void setAttrsStr(String attrs) {
        this.attrsStr = attrs;
    }

    public Long getDomainId() {
        return domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }

    public Integer getDataStatus() {
        return dataStatus;
    }

    public void setDataStatus(Integer dataStatus) {
        this.dataStatus = dataStatus;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Integer getHashCode() {
        return hashCode;
    }

    public void setHashCode(Integer hashCode) {
        this.hashCode = hashCode;
    }

    public String getCiPrimaryKey() {
        return ciPrimaryKey;
    }

    public void setCiPrimaryKey(String ciPrimaryKey) {
        this.ciPrimaryKey = ciPrimaryKey;
    }

    public String getCiLabel() {
        return ciLabel;
    }

    public void setCiLabel(String ciLabel) {
        this.ciLabel = ciLabel;
    }

    public String getDiagramId() {
        return diagramId;
    }

    public void setDiagramId(String diagramId) {
        this.diagramId = diagramId;
    }

    public long getLocalVersion() {
        return localVersion;
    }

    public void setLocalVersion(long localVersion) {
        this.localVersion = localVersion;
    }

    public long getPublicVersion() {
        return publicVersion;
    }

    public void setPublicVersion(long publicVersion) {
        this.publicVersion = publicVersion;
    }

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }


    public String getReleaseCiCode() {
        return releaseCiCode;
    }

    public void setReleaseCiCode(String releaseCiCode) {
        this.releaseCiCode = releaseCiCode;
    }
    public String getProcessCoding() {
        return processCoding;
    }
    public void setProcessCoding(String processCoding) {
        this.processCoding = processCoding;
    }
}
