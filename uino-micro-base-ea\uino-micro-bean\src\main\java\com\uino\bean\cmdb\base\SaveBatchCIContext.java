package com.uino.bean.cmdb.base;

import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class SaveBatchCIContext {

    private final String ciCode;

    private final Long classId;

    private String originCiCode;

    private Long originCiId;

    private String className;

    private String classStdCode;

    private final ESCIInfo esCi;

    private Long id;

    private Integer hashCode;

    private List<String> primaryKeys;

    private String ownerCode;

    private boolean isAdd;

    private Map<String,String> stdMap;

    private Map<String,Object> attrsObj;

    private Map<String,String> attrsStr;

    private Map<String,String> oldAttrs;

    private List<CcCiAttrDef> attrDefs;

    private int copyTimes;

    private List<String> copyValues;

    private boolean change = false;

    public SaveBatchCIContext(String ciCode, Long classId, ESCIInfo esCi){
        this.esCi = esCi;
        this.classId = classId;
        this.ciCode = ciCode;
    }

}
