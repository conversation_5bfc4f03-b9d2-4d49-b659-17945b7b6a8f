package com.uinnova.product.eam.service.es;

import com.binary.core.exception.MessageException;
import com.uinnova.product.eam.comm.model.es.FlowProcessSystemPublishHistory;
import com.uino.dao.AbstractESBaseDao;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.TopHits;
import org.elasticsearch.search.aggregations.metrics.TopHitsAggregationBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.io.IOException;
import java.util.*;

/**
 * 流程体系发布记录
 *
 * <AUTHOR>
 * @since 2024/5/30 18:05
 */
@Slf4j
@Service
public class FlowProcessSystemPublishHistoryDao extends AbstractESBaseDao<FlowProcessSystemPublishHistory,FlowProcessSystemPublishHistory> {
    @Override
    public String getIndex() {
        return "uino_flow_system_publish_history";
    }

    @Override
    public String getType() {
        return "_doc";
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }


    /**
     * 根据ciCode获取最新的发布记录,如果是提交的话，SV1，SV2，SV3...
     * 如果是发布的话，V1，V2，V3...
     *
     * @param ciCodes
     * @return
     */
    public Map<String, String> getLatestPublishHistoryByCiCodes(Collection<String> ciCodes) {

        //按照ciCode字段分组，并按modifyTime排序取最大值
        RestHighLevelClient client = getClient();
        // 创建搜索请求
        SearchRequest searchRequest = new SearchRequest(getFullIndexName());
        // 构建搜索条件
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(QueryBuilders.termsQuery("ciCode.keyword", ciCodes));
        // 执行搜索请求
        // 创建分组聚合
        TermsAggregationBuilder termsAggregationBuilder = AggregationBuilders
                .terms("by_ciCode")
                .field("ciCode.keyword")
                .size(10000); // 设置足够大的分组数量

        // 创建TopHits聚合，按modifyTime排序并只取第一条
        TopHitsAggregationBuilder topHitsAggregationBuilder = AggregationBuilders
                .topHits("latest_record")
                .sort("modifyTime", SortOrder.DESC)
                .size(1);
        // 将TopHits聚合添加到Terms聚合中
        termsAggregationBuilder.subAggregation(topHitsAggregationBuilder);
        // 将聚合添加到搜索请求中
        searchSourceBuilder.aggregation(termsAggregationBuilder);
        // 设置查询条件（根据传入的ciCodes进行查询）
        searchSourceBuilder.query(QueryBuilders.termsQuery("ciCode.keyword", ciCodes));
        // 设置不返回原始文档，只返回聚合结果
        searchSourceBuilder.size(0);
        // 将搜索条件设置到请求中
        searchRequest.source(searchSourceBuilder);
        SearchResponse searchResponse;
        Map<String, String> versionMap = new HashMap<>();
        try {
            searchResponse = client.search(searchRequest, RequestOptions.DEFAULT);
            Terms terms = searchResponse.getAggregations().get("by_ciCode");
            HashMap<String, String> stringStringHashMap = new HashMap<>();
            for (Terms.Bucket bucket : terms.getBuckets()) {
                String ciCode = bucket.getKeyAsString();
                long docCount = bucket.getDocCount();
                stringStringHashMap.put("ciCode", ciCode);
                TopHits topHits = bucket.getAggregations().get("latest_record");
                if (topHits.getHits().getHits().length > 0) {
                    Map<String, Object> sourceAsMap = topHits.getHits().getHits()[0].getSourceAsMap();
                    Object publishType = sourceAsMap.get("publishType");
                    if (publishType != null && "submit".equals(publishType.toString())) {
                        versionMap.put(ciCode, "SV" + docCount);
                    } else {
                        versionMap.put(ciCode, "V" + docCount);
                    }
                }
            }
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            throw new MessageException(e.getMessage());
        }
        return versionMap;
    }

}
