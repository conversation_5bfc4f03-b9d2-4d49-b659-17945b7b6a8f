package com.uinnova.product.eam.workable.listener;

import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.ExecutionListener;
import org.springframework.stereotype.Service;

/**
 * 解析审批人
 *
 * <AUTHOR>
 * @since 2024/11/12 上午10:34
 */
@Slf4j
@Service("parseRecordProcessUsersListener")
public class ParseRecordProcessUsersListener implements ExecutionListener {

    @Override
    public void notify(DelegateExecution execution) {
        Object processAssigneeList = execution.getVariable("processAssigneeList");
        execution.setVariable("assigneeList",processAssigneeList);
    }

}
