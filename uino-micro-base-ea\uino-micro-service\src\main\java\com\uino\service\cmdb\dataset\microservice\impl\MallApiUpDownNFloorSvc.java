package com.uino.service.cmdb.dataset.microservice.impl;

import com.alibaba.fastjson.JSONObject;
import com.binary.core.exception.MessageException;
import com.binary.core.util.BinaryUtils;
import com.uino.bean.cmdb.base.dataset.DataSetMallApi;
import com.uino.bean.cmdb.base.dataset.log.DataSetMallApiLog;
import com.uino.bean.cmdb.business.dataset.FriendInfo;
import com.uino.bean.cmdb.business.dataset.UpDownAttrCdt;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.permission.query.CSysUser;
import com.uino.dao.cmdb.dataset.ESDataSetMallApiLogSvc;
import com.uino.dao.util.ESUtil;
import com.uino.service.cmdb.dataset.microservice.IGraphAnalysisSvc;
import com.uino.service.cmdb.dataset.microservice.IMallApiSvc;
import com.uino.service.permission.microservice.IUserSvc;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

/**
 * 上下多少层
 *
 * @Date 2020/8/5 10:35
 * <AUTHOR> sh
 */
@Service
@Slf4j
public class MallApiUpDownNFloorSvc implements IMallApiSvc {
    @Autowired
    private ESDataSetMallApiLogSvc esDataSetMallApiLogSvc;

    @Autowired
    private IUserSvc userSvc;

    @Autowired
    private IGraphAnalysisSvc graphAnalysisSvc;

    @Override
    public DataSetMallApi checkCharacteristic(SysUser user, JSONObject jsonObject) {
        return null;
    }

    @Override
    public JSONObject execute(DataSetMallApi dataSetMallApi, JSONObject jsonObject) {
        long startTime = System.currentTimeMillis();
        Long domainId = dataSetMallApi.getDomainId();

        //2021-08-18 增加apikey校验
        String authentication = jsonObject.getString("authentication");
        String oauthAppName = jsonObject.getString("oauthAppName");

        String username = jsonObject.getString("username");
        String password = jsonObject.getString("password");
        Long startCiId = jsonObject.getLong("startCiId");
        List<UpDownAttrCdt> ciConditions = null;
        List<UpDownAttrCdt> rltConditions = null;
        List<Long> rltLvls = new ArrayList<>();
        Integer upLevel = jsonObject.getInteger("upLevel");
        Integer downLevel = jsonObject.getInteger("downLevel");
        int relTotal = 0;
        int ciTotal = 0;
        boolean isSuccess = true;

        try {
            //2021-08-18 增加apikey校验 记录调用系统名称
            if (!BinaryUtils.isEmpty(authentication)) {
                username = oauthAppName;
            } else {
                // Base64解密
                // Base64解密
                password = new String(Base64.decodeBase64(password.getBytes(StandardCharsets.UTF_8)), StandardCharsets.UTF_8);
                password = DigestUtils.sha256Hex(password);
                //todo: 用户名密码直接查询判断，没有通过登陆
                CSysUser cSysUser = new CSysUser();
                cSysUser.setDomainId(domainId);
                cSysUser.setLoginCodeEqual(username);
                List<SysUser> listByCdt = userSvc.getSysUserByCdt(cSysUser);
                if (listByCdt == null || listByCdt.isEmpty()) {
                    throw MessageException.i18n("DCV_USERNAME_ERROR");
                } else if (!listByCdt.get(0).getLoginPasswd().equals(password)) {
                    throw MessageException.i18n("DCV_PASSWORD_ERROR");
                }
            }

            FriendInfo friendInfo = graphAnalysisSvc.queryCiUpDownByCiId(domainId, startCiId, ciConditions, rltConditions, rltLvls, upLevel, downLevel, true);
            ciTotal = friendInfo.getCiNodes().size();
            relTotal = friendInfo.getCiRltLines().size();
            return JSONObject.parseObject(JSONObject.toJSONString(friendInfo));
        } catch (Exception e) {
            isSuccess = false;
            log.info("", e);
            throw new RuntimeException(e.getMessage());
        } finally {
            //保存日志
            DataSetMallApiLog dataSetMallApiLog = new DataSetMallApiLog();
            dataSetMallApiLog.setId(ESUtil.getUUID());
            dataSetMallApiLog.setDomainId(domainId);
            dataSetMallApiLog.setDataSetMallApiId(dataSetMallApi.getId());
            dataSetMallApiLog.setDataSetType(dataSetMallApi.getType());
            dataSetMallApiLog.setSuccess(isSuccess);
            dataSetMallApiLog.setRespUserCode(username);
            dataSetMallApiLog.setRespDuration(System.currentTimeMillis() - startTime);
            dataSetMallApiLog.setCreateTime(System.currentTimeMillis());
            dataSetMallApiLog.setCiTotal(ciTotal);
            dataSetMallApiLog.setRelTotal(relTotal);
            esDataSetMallApiLogSvc.saveOrUpdate(dataSetMallApiLog.toJson(), true);
        }
    }

    @Override
    public JSONObject realTimeExecute(DataSetMallApi dataSetMallApi, JSONObject jsonObject) {
        return execute(dataSetMallApi, jsonObject);
    }
}
