package com.uino.ed.eventdrive.executor;

import com.uino.ed.eventdrive.event.Event;
import com.uino.ed.eventdrive.handle.EventHandle;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 并发执行器
 */
@Component
public class ConcExecutor {

    private static final int CORE_POOL_SIZE = 5;

    private static final int Max_POOL_SIZE = 10;

    private static final int KEEP_ALIVE_TIME = 30;

    private static final int WORK_QUEUE_SIZE = 30;

    protected long interval=180;
    //控制并发线程池
    private ThreadPoolExecutor executorThreadPool;

    @PostConstruct
    public void active(){
        executorThreadPool = new ThreadPoolExecutor(CORE_POOL_SIZE,Max_POOL_SIZE, KEEP_ALIVE_TIME, TimeUnit.SECONDS,
                new ArrayBlockingQueue<Runnable>(WORK_QUEUE_SIZE),new ThreadPoolExecutor.AbortPolicy());
    }

    public void execute(EventHandle handle, Event event) {
        executorThreadPool.execute(new ConcWorker(handle, event));
    }
}
