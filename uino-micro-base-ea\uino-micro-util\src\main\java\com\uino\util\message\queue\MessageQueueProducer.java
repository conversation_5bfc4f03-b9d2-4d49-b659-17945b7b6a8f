package com.uino.util.message.queue;

import java.util.List;
import java.util.Map;

/**
 * Message service sender
 *
 * @Author: YGQ
 * @Create: 2021-05-24 15:35
 * @see <a href="http://192.168.21.86:8080/common/messageQueue/">http://192.168.21.86:8080/common/messageQueue/</a>
 **/
public interface MessageQueueProducer {

    /**
     * Send a single piece of data.
     * <h4 style="color:yellow">This method is preferred in most cases</h4>
     *
     * @param topic   topic
     * @param mapData data
     * @param key     Specify the key of the message, if not transmitted, it will be randomly generated
     */
    void sendMessage(String topic, Map<String, Object> mapData, String... key);

    /**
     * Send a single piece of data.
     * <h4 style="color:yellow">This method is preferred in most cases</h4>
     *
     * @param topic topic
     * @param data  data
     * @param key   Specify the key of the message, if not transmitted, it will be randomly generated
     */
    void sendMessage(String topic, String data, String... key);

    /**
     * Send messages in batches.
     * <h4 style="color:yellow">This method is preferred in most cases</h4>
     *
     * @param topic    kafka topic
     * @param listData data
     * @param key      Specify the key of the message, if not transmitted, it will be randomly generated
     */
    void sendBatchMessage(String topic, List<Map<String, Object>> listData, String... key);

    /**
     * Synchronously send data in batches.
     *
     * @param topic    topic
     * @param listData data
     * @param key      Specify the key of the message, if not transmitted, it will be randomly generated
     */
    void sendBatchMessageSync(String topic, List<Map<String, Object>> listData, String... key);

    /**
     * Synchronously send a single piece of data.
     *
     * @param topic   topic
     * @param mapData data
     * @param key     Specify the key of the message, if not transmitted, it will be randomly generated
     */
    void sendMessageSync(String topic, Map<String, Object> mapData, String... key);

    /**
     * Synchronously send a single piece of data.
     *
     * @param topic topic
     * @param data  data
     * @param key   Specify the key of the message, if not transmitted, it will be randomly generated
     */
    void sendMessageSync(String topic, String data, String... key);

    /**
     * Asynchronously send data in batches.
     *
     * @param topic    topic
     * @param listData data
     * @param key      Specify the key of the message, if not transmitted, it will be randomly generated
     */
    void sendBatchMessageAsync(String topic, List<Map<String, Object>> listData, String... key);

    /**
     * Asynchronously send a single piece of data.
     *
     * @param topic   topic
     * @param mapData data
     * @param key     Specify the key of the message, if not transmitted, it will be randomly generated
     */
    void sendMessageAsync(String topic, Map<String, Object> mapData, String... key);
}
