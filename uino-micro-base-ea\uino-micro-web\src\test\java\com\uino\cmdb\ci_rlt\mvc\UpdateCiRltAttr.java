package com.uino.cmdb.ci_rlt.mvc;

import java.util.ArrayList;
import java.util.HashMap;

import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import com.alibaba.fastjson.JSON;
import com.binary.framework.web.RemoteResult;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uino.StartBaseWebAppliaction;
import com.uino.dao.cmdb.ESCIRltSvc;
import com.uino.dao.cmdb.ESCISvc;
import com.uino.dao.cmdb.ESRltClassSvc;
import com.uino.bean.cmdb.base.ESCIClassInfo;
import com.uino.bean.cmdb.base.ESCIRltInfo;
import com.uino.bean.cmdb.business.UpdateCiRltRequestDto;

import lombok.extern.slf4j.Slf4j;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = { StartBaseWebAppliaction.class }, webEnvironment = WebEnvironment.RANDOM_PORT)
@Slf4j
@ActiveProfiles("provider-local")
public class UpdateCiRltAttr {
	@Autowired
	private TestRestTemplate restTemplate;
	@MockBean
	private ESRltClassSvc esRltClassSvc;
	@MockBean
	private ESCISvc esCiSvc;
	@MockBean
	private ESCIRltSvc esCiRltSvc;
	private static String testUrl;

	@BeforeClass
	public static void initCls() {
		UpdateCiRltAttr.testUrl = "/cmdb/ciRlt/updateCiRltAttr";
	}

	@Before
	public void start() {
		ESCIRltInfo ciRlt = new ESCIRltInfo();
		ciRlt.setId(1L);
        ciRlt.setClassId(123L);
        ciRlt.setCiCode("ciCode");
		Mockito.when(esCiRltSvc.getById(1L)).thenReturn(ciRlt);
        ESCIClassInfo rltClass = new ESCIClassInfo();
        rltClass.setCcAttrDefs(new ArrayList<CcCiAttrDef>());
        Mockito.when(esRltClassSvc.getById(Mockito.anyLong())).thenReturn(rltClass);
        Mockito.when(esCiRltSvc.countByCondition(Mockito.any())).thenReturn(0L);
		Mockito.when(esCiRltSvc.saveOrUpdate(Mockito.any())).thenReturn(1L);
	}

	@Test
	public void test01() {
		UpdateCiRltRequestDto reqBean = UpdateCiRltRequestDto.builder().ciRltId(1L).attrs(new HashMap<>()).build();
		String responseBodyStr = restTemplate.postForObject(testUrl, reqBean, String.class);
		RemoteResult responseBody = JSON.parseObject(responseBodyStr, RemoteResult.class);
		Assert.assertTrue(responseBody.isSuccess());

	}
}
