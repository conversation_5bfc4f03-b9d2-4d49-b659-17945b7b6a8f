package com.uinnova.product.eam.service.es;

import com.binary.core.lang.StringUtils;
import com.binary.core.util.BinaryUtils;
import com.uinnova.product.eam.comm.model.es.CEamDiagramDir;
import com.uinnova.product.eam.comm.model.es.EamDiagramDir;
import com.uino.dao.AbstractESBaseDao;
import com.uino.service.util.FileUtil;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.admin.indices.create.CreateIndexRequest;
import org.elasticsearch.action.admin.indices.get.GetIndexRequest;
import org.elasticsearch.action.admin.indices.settings.put.UpdateSettingsRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;

@Service
@Slf4j
public class IamsEsDiagramDirDao extends AbstractESBaseDao<EamDiagramDir, CEamDiagramDir> {

    @Override
    public String getIndex() {
        return "uino_eam_diagram_dir";
    }

    @Override
    public String getType() {
        return getIndex();
    }

    @PostConstruct
    public void init() {
        //初始化架构资产目录 设计仓库数据支持变更 不能每次重启都去加载初始化文件
        try {
            RestHighLevelClient clientc = getClient();
            GetIndexRequest getRequest = new GetIndexRequest();
            getRequest.indices(getFullIndexName());
            boolean existIndex = clientc.indices().exists(getRequest, RequestOptions.DEFAULT);
            if (!existIndex) {
                List<EamDiagramDir> data = FileUtil.getData("/initdata/uino_eam_diagram_dir.json", EamDiagramDir.class);
                super.initIndex(data);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    public List<EamDiagramDir> findDiagramDirList(Long dirParentId,Integer dirType, String sysType) {
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        if (BinaryUtils.isEmpty(dirType)) {
            // 单独查询根目录 dirType = -1
            query.must(QueryBuilders.termQuery("dirType", -1));
        }
        query.must(QueryBuilders.termQuery("dirType", dirType));
        if (!StringUtils.isEmpty(sysType)) {
            query.must(QueryBuilders.termQuery("sysType", sysType));
        }
        query.must(QueryBuilders.termQuery("dataStatus", 1));
        if (dirParentId != null) {
            query.must(QueryBuilders.termQuery("parentId", dirParentId));
        }
        query.must(QueryBuilders.termQuery("domainId", 1));
        //List<EamDiagramDir> eamList = esDiagramDirDao.getListByQuery(query);
        List<EamDiagramDir> eamList = this.getSortListByQuery(1, 3000, query, "dirName.keyword", true).getData();
        return eamList;
    }
}
