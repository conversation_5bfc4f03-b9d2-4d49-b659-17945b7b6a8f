package com.uinnova.product.eam.service.asset.impl;

import com.alibaba.fastjson.JSONObject;
import com.binary.core.exception.BinaryException;
import com.binary.core.util.BinaryUtils;
import com.uinnova.product.eam.comm.bean.CcCiAttrDefConfVO;
import com.uinnova.product.eam.comm.bean.CcCiClassInfoConfVO;
import com.uinnova.product.eam.comm.model.es.AssetListAttrConf;
import com.uinnova.product.eam.service.IEamCIClassApiSvc;
import com.uinnova.product.eam.service.asset.AssetListAttrConfSvc;
import com.uinnova.product.eam.service.es.AssetListAttrConfDao;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uino.bean.permission.base.SysUser;
import com.uino.util.sys.BeanUtil;
import com.uino.util.sys.SysUtil;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class AssetListAttrConfSvcImpl implements AssetListAttrConfSvc {

    public static final String APP_SQUARE_CONF_ID = "appSquareConfId";
    public static final String DOMAIN_ID = "domainId";
    @Resource
    AssetListAttrConfDao listAttrConfDao;

    @Resource
    IEamCIClassApiSvc ciClassApiSvc;

    @Override
    public Long saveOrUpdate(AssetListAttrConf attrConf) {
        if (BinaryUtils.isEmpty(attrConf.getClassCode())) {
            throw new BinaryException("分类标识不可为空");
        }
        if (BinaryUtils.isEmpty(attrConf.getAppSquareConfId())) {
            throw new BinaryException("广场卡片ID不可为空");
        }
        if (BinaryUtils.isEmpty(attrConf.getType())) {
            throw new BinaryException("分类不可为空");
        }
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("type", attrConf.getType()));
        query.must(QueryBuilders.termQuery(APP_SQUARE_CONF_ID, attrConf.getAppSquareConfId()));
        if (!BinaryUtils.isEmpty(attrConf.getId())) {
            query.mustNot(QueryBuilders.termQuery("id", attrConf.getId()));
        }
        AssetListAttrConf selectOne = listAttrConfDao.selectOne(query);
        if (!BinaryUtils.isEmpty(selectOne)) {
            throw new BinaryException("该资产列表配置已存在");
        }
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        String loginCode = currentUserInfo.getLoginCode();
        Long domainId = currentUserInfo.getDomainId();
        long timeMillis = System.currentTimeMillis();

        attrConf.setDomainId(domainId);
        if (BinaryUtils.isEmpty(attrConf.getId())) {
            attrConf.setCreator(loginCode);
            attrConf.setCreateTime(timeMillis);
        } else {
            attrConf.setModifier(loginCode);
            attrConf.setModifyTime(timeMillis);
        }

        attrConf.getShowAttrs().forEach(showAttr -> {
            Object id = showAttr.get("id");
            if (id instanceof String) {
                showAttr.put("id", 0);
            }
        });
        return listAttrConfDao.saveOrUpdate(attrConf);
    }

    @Override
    public AssetListAttrConf getListAttr(Long appSquareConfId, Integer type) {
        if (BinaryUtils.isEmpty(appSquareConfId)) {
            throw new BinaryException("资产卡片配置ID不可为空");
        }
        if (BinaryUtils.isEmpty(type)) {
            throw new BinaryException("分类不可为空");
        }
        Long domainId = SysUtil.getCurrentUserInfo().getDomainId();
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("type", type));
        query.must(QueryBuilders.termQuery(APP_SQUARE_CONF_ID, appSquareConfId));
        query.must(QueryBuilders.termQuery(DOMAIN_ID, domainId));
        return listAttrConfDao.selectOne(query);
    }

    @Override
    public Integer deleteListAttrById(Long id) {
        return listAttrConfDao.deleteById(id);
    }

    @Override
    public CcCiClassInfoConfVO getListShowAttrList(Long appSquareConfId, Integer type, boolean oldAttr) {
        CcCiClassInfoConfVO classInfoConf = new CcCiClassInfoConfVO();
        AssetListAttrConf listAttr = getListAttr(appSquareConfId, type);
        if (BinaryUtils.isEmpty(listAttr)) {
            return null;
        }
        CcCiClassInfo classInfo;
        if(oldAttr){
            classInfo = ciClassApiSvc.getCIClassByCodes(listAttr.getClassCode());
        }else{
            classInfo = ciClassApiSvc.queryClassAndAttrMappingByCode(listAttr.getClassCode());
        }
        BeanUtil.copyProperties(classInfo, classInfoConf);
        List<CcCiAttrDef> attrDefs = classInfo.getAttrDefs();
        Map<Long, CcCiAttrDef> attrDefMap = attrDefs.stream().collect(Collectors.toMap(e -> e.getId(), e -> e, (k1, k2) -> k1));

        List<JSONObject> showAttrs = listAttr.getShowAttrs();
        List<CcCiAttrDef> showCIAttrInfo = new ArrayList<>();
        classInfoConf.setShowListCIAttrInfo(showCIAttrInfo);
        for (JSONObject showAttr : showAttrs) {
            Object idObj = showAttr.get("id");
            CcCiAttrDef ccCiAttrDef = attrDefMap.get(idObj);
            if (!BinaryUtils.isEmpty(ccCiAttrDef)) {
                showCIAttrInfo.add(ccCiAttrDef);
            }
        }
        List<JSONObject> tagAttrs = listAttr.getTagAttrs();
        List<CcCiAttrDefConfVO> tagCIAttrInfo = new ArrayList<>();
        classInfoConf.setTagListCIAttrInfo(tagCIAttrInfo);
        for (JSONObject tagAttr : tagAttrs) {
            Object id = tagAttr.get("id");
            if (!BinaryUtils.isEmpty(id)) {
                CcCiAttrDef ccCiAttrDef = attrDefMap.get(id);
                if (!BinaryUtils.isEmpty(ccCiAttrDef)) {
                    CcCiAttrDefConfVO attrDefConfVO = new CcCiAttrDefConfVO();
                    BeanUtil.copyProperties(ccCiAttrDef, attrDefConfVO);
                    Object colour = tagAttr.get("colour");
                    if (!BinaryUtils.isEmpty(colour)) {
                        attrDefConfVO.setColour(colour.toString());
                    }
                    tagCIAttrInfo.add(attrDefConfVO);
                }
            }
        }
        return classInfoConf;
    }

    @Override
    public Integer delListAttrByAppSquareConfId(Long appSquareConfId) {
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery(APP_SQUARE_CONF_ID, appSquareConfId));
        query.must(QueryBuilders.termQuery(DOMAIN_ID, SysUtil.getCurrentUserInfo().getDomainId()));
        return listAttrConfDao.deleteByQuery(query, true);
    }

}
