package com.uino.util.sys;

import java.io.BufferedReader;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.io.FileUtils;

import com.alibaba.fastjson.JSON;
import com.binary.core.exception.MessageException;

import lombok.extern.slf4j.Slf4j;

/**
 * 
 * <AUTHOR>
 *
 */
@Slf4j
public class CommonFileUtil {
	/**
	 * 读取文件
	 * 
	 * @param file
	 * @return
	 */
	public static byte[] readFileToByteArray(File file) {
		try {
			return FileUtils.readFileToByteArray(file);
		} catch (IOException e) {
			throw new RuntimeException(e);
		}
	}

    /**
	 * 读取指定路径文件转换为List<T>
	 * 
	 * @param path
	 * @param obj
	 *            <T>
	 * @return
	 */
	public static <T> List<T> getData(String path, Class<T> obj) {
		InputStream is = null;
		BufferedReader br = null;
		List<T> list = new ArrayList<T>();
		try {
			is = CommonFileUtil.class.getResourceAsStream(path);
			br = new BufferedReader(new InputStreamReader(is, "utf-8"));
			StringBuffer sf = new StringBuffer();
			String line = "";
			while ((line = br.readLine()) != null) {
				sf.append(line.trim());
			}
			list = JSON.parseArray(sf.toString(), obj);
		} catch (Exception e) {
			log.error("读取文件[" + path + "]失败");
		} finally {
			try {
				if (br != null) {
					br.close();
				}
				if (is != null) {
					is.close();
				}
			} catch (Exception e) {
				throw new MessageException(e.getMessage());
			}
        }
		return list;
	}

	/**
	 * 读取输入流返回输入流对应bytes
	 * 
	 * @param inStream
	 * @return
	 * @throws Exception
	 */
	public static byte[] readStream(InputStream inStream, boolean clostInputStream) {
		try {
			ByteArrayOutputStream outSteam = new ByteArrayOutputStream();
			byte[] buffer = new byte[1024];
			int len = -1;
			while ((len = inStream.read(buffer)) != -1) {
				outSteam.write(buffer, 0, len);
			}
			outSteam.close();
			if (clostInputStream) {
				inStream.close();
			}
			return outSteam.toByteArray();
		} catch (Exception e) {
			log.error("读取流异常", e);
			throw new RuntimeException(e);
        }
    }

}
