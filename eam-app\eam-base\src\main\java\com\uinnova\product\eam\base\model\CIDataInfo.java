package com.uinnova.product.eam.base.model;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uino.bean.cmdb.base.ESCIInfo;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Data
public class CIDataInfo {

    private Long classId;
    private Long id;
    private String ciCode;
    private String ciVersion;

    @Comment("当前版本")
    private String editionNo;

    @Comment("发布时间")
    private Long publishTime;

    @Comment("下载量")
    private Long useTimes;

    private Map<String, String> attrs;

    @Comment("代码Keys")
    private List<String> attrCodeKeys;

    @Comment("代码Value")
    private Map<String, String> attrCodeValues;

    @Comment("属性定义有序")
    private List<String> attrKeys;

    private List<ResourceInfo> resources;

    public CIDataInfo(){
    }

    public CIDataInfo(CcCiInfo ci, String editionNo){
        this.attrKeys = ci.getAttrDefs().stream().map(CcCiAttrDef::getProStdName).collect(Collectors.toList());
        this.attrs = ci.getAttrs();
        this.ciCode = ci.getCi().getCiCode();
        this.ciVersion = ci.getCi().getCiVersion();
        this.editionNo = editionNo;
        this.id = ci.getCi().getId();
        this.classId = ci.getCi().getClassId();
    }

    public CIDataInfo(ESCIInfo esciInfo, CcCiClassInfo ccCiClassInfo){
        this.attrKeys = ccCiClassInfo.getAttrDefs().stream().map(CcCiAttrDef::getProStdName).collect(Collectors.toList());
        this.attrs = new HashMap<>();
        esciInfo.getAttrs().forEach((key,value)-> attrs.put(key, value == null ? "" : String.valueOf(value)));
        this.ciCode = esciInfo.getCiCode();
        this.ciVersion = esciInfo.getCiVersion();
        Object editionNo = esciInfo.getAttrs().get("版本");
        if(null != editionNo){
            this.editionNo = String.valueOf(editionNo);
        }else {
            this.editionNo = "0";
        }
        this.id = esciInfo.getId();
        this.classId = esciInfo.getClassId();
    }

}
