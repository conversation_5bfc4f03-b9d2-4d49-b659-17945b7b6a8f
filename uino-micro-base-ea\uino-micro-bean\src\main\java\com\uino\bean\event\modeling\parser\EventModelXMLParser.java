package com.uino.bean.event.modeling.parser;

import com.uino.bean.event.modeling.EventModel;
import com.uino.bean.event.modeling.EventModelAttribute;
import org.jdom2.Document;
import org.jdom2.Element;
import org.jdom2.input.SAXBuilder;

import java.io.InputStream;
import java.util.List;

public class EventModelXMLParser {

	private SAXBuilder builder;

	public EventModelXMLParser() {
		this.builder = new SAXBuilder("org.apache.xerces.parsers.SAXParser");
	}

	public EventModel load(InputStream in) throws RuntimeException {

		try {
			Document doc = builder.build(in);
			Element root = doc.getRootElement();

			String version = root.getAttributeValue("version");
			if (version.equals("1.0")) {
				return parseRootV1(root);
			} else {
				throw new RuntimeException("Can not identify the version of the event model: " + version);
			}
		} catch (RuntimeException e) {
			throw e;
		} catch (Exception e) {
			throw new RuntimeException(e.getMessage(), e);
		}

	}

	/**
	 * 解析本版本的事件模型XML信息。
	 * @param root
	 * @return
	 * @throws Exception
	 */
	private EventModel parseRootV1(Element root) throws Exception {


		EventModel model = new EventModel();
		String desc = root.getAttributeValue("description");
		String table = root.getAttributeValue("table");
		String partitionColumn = root.getAttributeValue("partitionColumn");
		String memoryTable = root.getAttributeValue("memoryTable");
		model.setDesc(desc);
		model.setTable(table);
		model.setPartitionColumn(partitionColumn);
		model.setMemoryTable(memoryTable);

		@SuppressWarnings("rawtypes")
		List attributes = root.getChildren("field");
		for (Object obj : attributes) {
			Element attrEle = (Element) obj;
			EventModelAttribute attr = parseAttr(attrEle);
			model.addAttribute(attr);
		}

		return model;

	}


	/**
	 * 解析每个事件的属性信息节点。
	 * @param attrEle
	 * @return
	 * @throws Exception
	 */
	private EventModelAttribute parseAttr(Element attrEle) throws Exception {
		EventModelAttribute retVal = new EventModelAttribute();

		String name = attrEle.getChildTextTrim("field-name");

		String alias = attrEle.getChildTextTrim("title");
		boolean isNullAble = Boolean.parseBoolean(attrEle
				.getChildTextTrim("required"));
		String dataType = attrEle.getChildTextTrim("data-type");
		String defaultValue = attrEle.getChildTextTrim("default-value");

		boolean isRedefine = Boolean.parseBoolean(attrEle.getChildTextTrim("redefinable"));

		boolean isUpatedOnDeduplicate = Boolean.parseBoolean(attrEle.getChildTextTrim("update-on-deduplicate"));
		//字段的来源信息 Event/KPI/CI
		String sourceType = attrEle.getChildTextTrim("source-type");

		//映射CI和KPI字段的在mmdb中的字段
		String mmdbKey = attrEle.getChildTextTrim("mmdb-key");

		//是否作为缓存查询字段（存储redis时，将查询字段拼接作为key，id集合作为value，实现按照条件检索）
		boolean isRedisSearchKey = Boolean.parseBoolean(attrEle.getChildTextTrim("redis-search-key"));

		retVal.setName(name);
		retVal.setAlias(alias);
		retVal.setNullAble(!isNullAble);
		retVal.setDataType(dataType);

		retVal.setDefaultValue(defaultValue);

		retVal.setRedefine(isRedefine);
		retVal.setUpatedOnDeduplicate(isUpatedOnDeduplicate);

		retVal.setSourceType(sourceType);
		retVal.setMmdbKey(mmdbKey);
		retVal.setRedisSearchKey(isRedisSearchKey);
		return retVal;
	}

}