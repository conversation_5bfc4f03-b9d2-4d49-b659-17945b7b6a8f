package com.uinnova.product.eam.feign.client;

import com.uinnova.product.eam.base.model.SpecialMetaData;
import com.uinnova.product.eam.feign.FeignConst;
import com.uinnova.product.eam.model.asset.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@Component
@FeignClient(name = FeignConst.APPLICATION,path = FeignConst.SPECIFICATION_PATH)
public interface SpecificationClient {

    @GetMapping("selectAttrConf")
    SpecialMasterEleDTO selectAttrConf(@RequestBody SpecialMetaData specialMetaData);

    @PostMapping("selectAttrConf")
    Long save(@RequestBody SpecialMasterDTO record);

    @PostMapping("selectList")
    List<SpecialMasterDTO> selectList(@RequestBody SearchSpeciDTO searchBean);

    @PostMapping("selectTopN")
    List<SpecialSimpleDTO> selectTopN(@RequestParam("specialClassName") String specialClassName, @RequestParam("editionClassName") String editionClassName,@RequestParam("limit") int limit);

    @GetMapping("getResIdsById")
    EditionResourceListDto getResIdsById(@RequestParam("id") Long id, @RequestParam("editionClassName") String editionClassName);
}
