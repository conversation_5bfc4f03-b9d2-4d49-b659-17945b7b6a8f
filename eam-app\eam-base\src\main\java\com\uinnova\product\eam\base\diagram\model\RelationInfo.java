package com.uinnova.product.eam.base.diagram.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

@Data
public class RelationInfo implements Serializable {
    @JSONField(name = "isNewWindowOpen")
    private Boolean isNewWindowOpen;
    @JSONField(name = "name")
    private String name;
    @JSONField(name = "page")
    private String page;
    @JSONField(name = "type")
    private String type;
    @JSONField(name = "value")
    private String value;


}
