package com.uino.cmdb.ci;

import static org.junit.Assert.assertEquals;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

import jakarta.servlet.http.Cookie;

import org.apache.commons.io.FileUtils;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.binary.core.exception.MessageException;
import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.comm.model.ci.CCcCi;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClass;
import com.uinnova.product.vmdb.comm.model.ci.CcCi;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.comm.model.ci.CcCiClass;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiRecord;
import com.uinnova.product.vmdb.provider.ci.bean.CiClassSaveInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CiGroupPage;
import com.uinnova.product.vmdb.provider.ci.bean.CiQueryCdt;
import com.uinnova.product.vmdb.provider.search.bean.CcCiSearchPage;
import com.uino.dao.cmdb.ESCIClassSvc;
import com.uino.dao.cmdb.ESCISvc;
import com.uino.dao.cmdb.ESCmdbCommSvc;
import com.uino.service.cmdb.microservice.impl.CIClassSvc;
import com.uino.service.cmdb.microservice.impl.CIRltSvc;
import com.uino.service.cmdb.microservice.impl.CISvc;
import com.uino.service.sys.microservice.ICIOperateLogSvc;
import com.uino.service.sys.microservice.IResourceSvc;
import com.uino.dao.util.ESUtil;
import com.uino.bean.cmdb.base.ESCIClassInfo;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.business.CiExcelInfoDto;
import com.uino.bean.cmdb.business.ExportCiDto;
import com.uino.bean.cmdb.business.ImportExcelMessage;
import com.uino.bean.cmdb.business.ImportResultMessage;
import com.uino.bean.cmdb.query.ESAttrAggBean;
import com.uino.bean.cmdb.query.ESCISearchBean;
import com.uino.bean.sys.base.ESCIOperateLog;

@RunWith(SpringJUnit4ClassRunner.class)
public class CISvcTest {

    @InjectMocks
    CISvc ciSvc;

    private ESCISvc esCiSvc;

    private ESCIClassSvc esClsSvc;

    private CIClassSvc clsSvc;

    private CIRltSvc ciRltSvc;

    private ESCmdbCommSvc commSvc;

    private IResourceSvc resourceSvc;

    private ICIOperateLogSvc logSvc;

    @BeforeClass
    public static void setUpBeforeClass() throws Exception {
        MockHttpServletRequest request = new MockHttpServletRequest();
        Cookie cookie = new Cookie("token", "ca14ed042fe7912426b484f6ea5c754b4fb51ae4a72037e9127da51743e0d1f9650e6e78ed4466ac970acb6a0b2f46fd26958c4ab2d115cc71b34ca8a02db24c");
        request.setCookies(cookie);
        ServletRequestAttributes attributes = new ServletRequestAttributes(request);
        RequestContextHolder.setRequestAttributes(attributes);
    }

    @Before
    public void setUp() throws Exception {
        esCiSvc = Mockito.mock(ESCISvc.class);
        ReflectionTestUtils.setField(ciSvc, "esCiSvc", esCiSvc);

        esClsSvc = Mockito.mock(ESCIClassSvc.class);
        ReflectionTestUtils.setField(ciSvc, "esClsSvc", esClsSvc);

        clsSvc = Mockito.mock(CIClassSvc.class);
        ReflectionTestUtils.setField(ciSvc, "clsSvc", clsSvc);

        ciRltSvc = Mockito.mock(CIRltSvc.class);
        ReflectionTestUtils.setField(ciSvc, "ciRltSvc", ciRltSvc);

        commSvc = Mockito.mock(ESCmdbCommSvc.class);
        ReflectionTestUtils.setField(ciSvc, "commSvc", commSvc);

        resourceSvc = Mockito.mock(IResourceSvc.class);
        ReflectionTestUtils.setField(ciSvc, "resourceSvc", resourceSvc);

        logSvc = Mockito.mock(ICIOperateLogSvc.class);
        ReflectionTestUtils.setField(ciSvc, "logSvc", logSvc);

        ReflectionTestUtils.setField(ciSvc, "urlPath", "urlPath");
        ReflectionTestUtils.setField(ciSvc, "localPath", "localPath");
        ReflectionTestUtils.setField(ciSvc, "primaryKeyCount", 1);
    }

    @Test
    public void testSaveOrUpdate() {
        ESCIClassInfo ciClass = new ESCIClassInfo();
        ciClass.setClassName("测试");
        Mockito.when(esClsSvc.getById(Mockito.anyLong())).thenReturn(null);

        List<CcCiAttrDef> dbDefs = new ArrayList<>();
        CcCiAttrDef dbDef = new CcCiAttrDef();
        dbDef.setProName("测试key");
        dbDef.setOrderNo(1);
        dbDef.setIsMajor(1);
        dbDef.setIsRequired(1);
        dbDef.setProStdName("测试KEY");
        dbDefs.add(dbDef);
        Mockito.when(esClsSvc.getAllDefsByClassId(1L, Mockito.anyLong())).thenReturn(dbDefs);
        Map<String, Integer> errorMsg = new HashMap<>();
        errorMsg.put("属性校验失败", 0);
        Mockito.when(commSvc.validAttrs(Mockito.anyList(), Mockito.any(), Mockito.anyBoolean())).thenReturn(errorMsg);

        // List<ESCIInfo> liveCis = new ArrayList<>();
        // ESCIInfo esciInfo = new ESCIInfo();
        // esciInfo.setCiCode("测试code");
        // esciInfo.setCiPrimaryKey("[\"测试value\"]");
        // liveCis.add(esciInfo);
        // Mockito.when(esCiSvc.getListByCdt(Mockito.any(CCcCi.class))).thenReturn(liveCis);

        Page<CcCiInfo> page = new Page<>();
        List<CcCiInfo> ciInfos = new ArrayList<>();
        CcCiInfo ciInfo = new CcCiInfo();
        CcCi ci = new CcCi();
        ci.setClassId(123L);
        ci.setCiCode("测试code");
        ci.setHashCode(131487205);
        ci.setCiVersion("1");
        ciInfo.setCi(ci);
        Map<String, String> attrsMap = new HashMap<>();
        attrsMap.put("测试key", "测试value");
        ciInfo.setAttrs(attrsMap);
        ciInfos.add(ciInfo);
        page.setData(ciInfos);
        Mockito.when(esCiSvc.getCIInfoPageByQuery(Mockito.anyInt(), Mockito.anyInt(), Mockito.any(), Mockito.anyBoolean())).thenReturn(page);
        Mockito.when(esCiSvc.getCiInfoById(Mockito.anyLong())).thenReturn(ciInfo);

        Mockito.when(esCiSvc.saveOrUpdateCI(Mockito.any(CcCiInfo.class))).thenReturn(123L);

        // 分类不存在
        CcCi ci2 = new CcCi();
        ci2.setClassId(123L);
        ci2.setId(123L);
        CcCiInfo ciInfo2 = new CcCiInfo();
        ciInfo2.setCi(ci2);
        Map<String, String> attrs = new HashMap<>();
        attrs.put("测试key", "测试value");
        ciInfo2.setAttrs(attrs);
        try {
            ciSvc.saveOrUpdate(ciInfo2);
        } catch (Exception e) {
            // assertEquals("BS_MNAME_CLASS_NOT_EXSIT", e.getMessage());
        }

        // 属性校验错误
        Mockito.when(esClsSvc.getById(Mockito.anyLong())).thenReturn(ciClass);
        try {
            ciSvc.saveOrUpdate(ciInfo2);
        } catch (Exception e) {
            assertEquals("属性校验失败", e.getMessage());
        }

        // CI已存在
        Mockito.when(commSvc.validAttrs(Mockito.anyList(), Mockito.any(), Mockito.anyBoolean())).thenReturn(new HashMap<>());
        try {
            ciSvc.saveOrUpdate(ciInfo2);
        } catch (Exception e) {
            // assertEquals("BS_MNAME_RECORD_CONTAINS", e.getMessage());
        }

        Mockito.when(esCiSvc.getCIInfoPageByQuery(Mockito.anyInt(), Mockito.anyInt(), Mockito.any(), Mockito.anyBoolean())).thenReturn(new Page<>(1, 1, 1, 1, new ArrayList<>()));
        ci2.setCiCode("测试code");
        Long id = ciSvc.saveOrUpdate(ciInfo2);
        Assert.assertEquals(123L, id.longValue());
    }

    @Test
    public void testSaveOrUpdateCiBath() {
        List<CcCiAttrDef> defs = new ArrayList<>();
        CcCiAttrDef def = new CcCiAttrDef();
        def.setProName("测试key");
        def.setOrderNo(1);
        def.setIsMajor(1);
        def.setIsRequired(1);
        def.setProStdName("测试KEY");
        defs.add(def);
        Mockito.when(esClsSvc.getAllDefsByClassId(1L,Mockito.anyLong())).thenReturn(defs);

        ESCIClassInfo clsInfo = new ESCIClassInfo();
        clsInfo.setClassName("测试");
        Mockito.when(esClsSvc.getById(Mockito.anyLong())).thenReturn(clsInfo);

        Page<ESCIInfo> esPage = new Page<>();
        List<ESCIInfo> esInfos = new ArrayList<>();
        ESCIInfo esciInfo = new ESCIInfo();
        Map<String, Object> attrs = new HashMap<>();
        attrs.put("测试key", "测试value");
        esciInfo.setClassId(123L);
        esciInfo.setCiCode("测试code");
        esciInfo.setAttrs(attrs);
        esciInfo.setHashCode(131487205);
        esInfos.add(esciInfo);
        esPage.setData(esInfos);
        Mockito.when(esCiSvc.getListByQuery(Mockito.anyInt(), Mockito.anyInt(), Mockito.any())).thenReturn(esPage);

        Page<CcCiInfo> page = new Page<>();
        List<CcCiInfo> ciInfos = new ArrayList<>();
        CcCiInfo ciInfo = new CcCiInfo();
        CcCi ci = new CcCi();
        ci.setClassId(123L);
        ci.setCiCode("测试code");
        ci.setHashCode(131487205);
        ci.setCiVersion("1");
        ciInfo.setCi(ci);
        Map<String, String> attrsMap = new HashMap<>();
        attrsMap.put("测试key", "测试value");
        ciInfo.setAttrs(attrsMap);
        ciInfos.add(ciInfo);
        page.setData(ciInfos);
        Mockito.when(esCiSvc.getCIInfoPageByQuery(Mockito.anyInt(), Mockito.anyInt(), Mockito.any(), Mockito.anyBoolean())).thenReturn(page);

        Mockito.when(commSvc.validAttrs(Mockito.anyList(), Mockito.any(), Mockito.anyBoolean())).thenReturn(new HashMap<>());

        Mockito.when(esCiSvc.countByCondition(Mockito.any())).thenReturn(1L);
        Mockito.when(esCiSvc.getListByQuery(Mockito.anyInt(), Mockito.anyInt(), Mockito.any())).thenReturn(esPage);

        Map<String, Object> result = new HashMap<>();
        result.put("failCount", 0);
        result.put("errMessge", null);
        Mockito.when(esCiSvc.saveOrUpdateCIBatch(Mockito.anyList(), Mockito.anyBoolean())).thenReturn(result);

        /*CiClassSaveInfo saveInfo = new CiClassSaveInfo();
        List<CcCiRecord> records = new ArrayList<>();
        CcCiRecord record = new CcCiRecord();
        record.setCiCode("测试code");
        Map<String, String> attrs2 = new HashMap<>();
        attrs2.put("测试key", "测试value");
        record.setAttrs(attrs2);
        records.add(record);
        saveInfo.setRecords(records);
        saveInfo.setClassId(123L);
        ciSvc.saveOrUpdateCiBath(1L, saveInfo);*/
    }

    @Test
    public void testGetCiInfoById() {
        CcCiInfo ciInfo = new CcCiInfo();
        CcCi ci = new CcCi();
        ci.setCiCode("测试code");
        ciInfo.setCi(ci);
        Mockito.when(esCiSvc.getCiInfoById(Mockito.anyLong())).thenReturn(ciInfo);

        CcCiInfo rs = ciSvc.getCiInfoById(123L);
        Assert.assertEquals("测试code", rs.getCi().getCiCode());
    }

    @Test
    public void testQueryPageByIndex() {
        CiGroupPage page = new CiGroupPage();
        page.setPageNum(1L);
        Mockito.when(esCiSvc.queryPageByIndex(1L, Mockito.anyInt(), Mockito.anyInt(), Mockito.any(CiQueryCdt.class), Mockito.any())).thenReturn(page);

        CiGroupPage rs = ciSvc.queryPageByIndex(1L, 1, 20, new CiQueryCdt(), false);
        Assert.assertEquals(1L, rs.getPageNum());
    }

    @Test
    public void testQueryCiList() {
        List<CcCiInfo> ciInfos = new ArrayList<>();
        CcCiInfo ciInfo = new CcCiInfo();
        CcCi ci = new CcCi();
        ci.setCiCode("测试code");
        ciInfo.setCi(ci);
        ciInfos.add(ciInfo);
        Mockito.when(esCiSvc.queryCiInfoList(Mockito.any(CCcCi.class), Mockito.anyString(), Mockito.anyBoolean(), Mockito.anyBoolean())).thenReturn(ciInfos);

        List<CcCi> rs = ciSvc.queryCiList(1L, null, null, true);
        Assert.assertEquals("测试code", rs.get(0).getCiCode());
    }

    @Test
    public void testQueryESCIInfoList() {
        List<ESCIInfo> ciInfos = new ArrayList<>();
        ESCIInfo esciInfo = new ESCIInfo();
        esciInfo.setId(123L);
        ciInfos.add(esciInfo);
        Mockito.when(esCiSvc.queryESCiInfoList(Mockito.any(CCcCi.class), Mockito.anyString(), Mockito.any())).thenReturn(ciInfos);

        List<ESCIInfo> res = ciSvc.queryESCIInfoList(null, null, null, null);
        Assert.assertEquals(123L, res.get(0).getId().longValue());
    }

    @Test
    public void testQueryCiInfoList() {
        List<CcCiInfo> ciInfos = new ArrayList<>();
        CcCiInfo ciInfo = new CcCiInfo();
        CcCi ci = new CcCi();
        ci.setCiCode("测试code");
        ciInfo.setCi(ci);
        ciInfos.add(ciInfo);
        Mockito.when(esCiSvc.queryCiInfoList(Mockito.any(CCcCi.class), Mockito.anyString(), Mockito.anyBoolean(), Mockito.anyBoolean())).thenReturn(ciInfos);

        List<CcCiInfo> rs = ciSvc.queryCiInfoList(1L, null, null, true, true);
        Assert.assertEquals("测试code", rs.get(0).getCi().getCiCode());
    }

    @Test
    public void testGetESCIInfoListByCIPrimaryKeys() {
        List<ESCIInfo> ciInfos = new ArrayList<>();
        ESCIInfo esciInfo = new ESCIInfo();
        esciInfo.setId(123L);
        ciInfos.add(esciInfo);
        Mockito.when(esCiSvc.getCIInfoListByCIPrimaryKeys(1L, Mockito.anyList())).thenReturn(ciInfos);

        List<ESCIInfo> res = ciSvc.getESCIInfoListByCIPrimaryKeys(1L, null);
        Assert.assertEquals(0, res.size());

        List<List<String>> ciPrimaryKeys = new ArrayList<>();
        ciPrimaryKeys.add(Arrays.asList("a", "b"));
        List<ESCIInfo> res2 = ciSvc.getESCIInfoListByCIPrimaryKeys(1L, ciPrimaryKeys);
        Assert.assertEquals(123L, res2.get(0).getId().longValue());
    }

    @Test
    public void testGetCIInfoListByCIPrimaryKeys() {
        List<ESCIInfo> ciInfos = new ArrayList<>();
        ESCIInfo esciInfo = new ESCIInfo();
        esciInfo.setCiCode("测试code");
        ciInfos.add(esciInfo);
        Mockito.when(esCiSvc.getCIInfoListByCIPrimaryKeys(1L, Mockito.anyList())).thenReturn(ciInfos);

        List<CcCiInfo> ccCiInfos = new ArrayList<>();
        CcCiInfo ciInfo = new CcCiInfo();
        CcCi ci = new CcCi();
        ci.setId(123L);
        ci.setCiCode("测试code");
        ciInfo.setCi(ci);
        ccCiInfos.add(ciInfo);
        Mockito.when(commSvc.transEsInfoList(Mockito.anyList(), Mockito.anyBoolean())).thenReturn(ccCiInfos);

        List<CcCiInfo> res = ciSvc.getCIInfoListByCIPrimaryKeys(1L, null);
        Assert.assertEquals(1, res.size());

        List<List<String>> ciPrimaryKeys = new ArrayList<>();
        ciPrimaryKeys.add(Arrays.asList("a", "b"));
        List<CcCiInfo> res2 = ciSvc.getCIInfoListByCIPrimaryKeys(1L, ciPrimaryKeys);
        Assert.assertEquals(123L, res2.get(0).getCi().getId().longValue());
    }

    @Test
    public void testQueryCiInfoPage() {
        Page<CcCiInfo> page = new Page<>();
        List<CcCiInfo> ciInfos = new ArrayList<>();
        CcCiInfo ciInfo = new CcCiInfo();
        CcCi ci = new CcCi();
        ci.setCiCode("测试code");
        ciInfo.setCi(ci);
        ciInfos.add(ciInfo);
        page.setData(ciInfos);
        Mockito.when(esCiSvc.queryCiInfoPage(Mockito.anyInt(), Mockito.anyInt(), Mockito.any(CCcCi.class), Mockito.anyString(), Mockito.anyBoolean(), Mockito.anyBoolean())).thenReturn(page);

        Page<CcCiInfo> rs = ciSvc.queryCiInfoPage(1L, 1, 10, null, "createTime", true, true);
        Assert.assertEquals("测试code", rs.getData().get(0).getCi().getCiCode());
    }

    @Test
    public void testSearchCIByCdt() {
        CcCiSearchPage page = new CcCiSearchPage();
        page.setPageNum(1L);
        Mockito.when(esCiSvc.searchCIByCdt(Mockito.anyInt(), Mockito.anyInt(), Mockito.any(CCcCi.class))).thenReturn(page);

        CcCiSearchPage rs = ciSvc.searchCIByCdt(1, 10, null);
        Assert.assertEquals(1L, rs.getPageNum());
    }

    @Test
    public void testSearchESCIByBean() {
        Page<ESCIInfo> page = new Page<>();
        page.setPageNum(1);
        page.setPageSize(10);
        page.setTotalPages(2);
        page.setTotalRows(16);
        List<ESCIInfo> ciInfos = new ArrayList<>();
        ESCIInfo esciInfo = new ESCIInfo();
        esciInfo.setId(123L);
        ciInfos.add(esciInfo);
        page.setData(ciInfos);
        Mockito.when(esCiSvc.searchESCIByBean(Mockito.any())).thenReturn(page);

        Page<ESCIInfo> res = ciSvc.searchESCIByBean(null);
        Assert.assertEquals(123L, res.getData().get(0).getId().longValue());
    }

    @Test
    public void testSearchCIByBean() {
        CcCiSearchPage page = new CcCiSearchPage();
        page.setPageNum(1L);
        Mockito.when(esCiSvc.searchCIByBean(Mockito.any(ESCISearchBean.class))).thenReturn(page);

        CcCiSearchPage rs = ciSvc.searchCIByBean(null);
        Assert.assertEquals(1L, rs.getPageNum());
    }

    @Test
    public void testUpdateESCIInfoBatch() {
        Mockito.when(esCiSvc.updateESCIInfoBatch(Mockito.anyList())).thenReturn(1);

        Integer rs = ciSvc.updateESCIInfoBatch(new ArrayList<>());
        Assert.assertEquals(1, rs.intValue());
    }

     @Test
    public void testImportCiByCiClsIds() throws Exception {
        ESCIClassInfo clsInfo = new ESCIClassInfo();
        clsInfo.setClassName("测试");
        Mockito.when(esClsSvc.getById(Mockito.anyLong())).thenReturn(clsInfo);

        // importCiExcel
        Mockito.when(resourceSvc.saveSyncResourceInfo(Mockito.anyString(), Mockito.anyString(), Mockito.anyBoolean(), Mockito.anyBoolean(), Mockito.anyInt())).thenReturn("test");

        File file = new File("./src/test/resources/testdata/ci.xlsx");
        System.out.println(file.getAbsolutePath());
        MockMultipartFile importFile = new MockMultipartFile("file", "ci.xlsx", MediaType.TEXT_PLAIN_VALUE, new FileInputStream(file));
        try {
            ciSvc.importCiByCiClsIds(importFile, 123L);
        } catch (Exception e) {
            // assertEquals("BS_MNAME_CLASS_DATA_ERROR", e.getMessage());
        }
        // 删除上传的文件
        try {
            File imFile = new File("./localPath/" + ESUtil.getNumberDate());
            FileUtils.deleteDirectory(imFile);
        } catch (Exception e) {
            // TODO: handle exception
        }
    }

    @Test
    public void testExportCiOrClass() {
        List<ESCIClassInfo> ciClassInfos = new ArrayList<>();
        ESCIClassInfo classInfo = new ESCIClassInfo();
        classInfo.setId(123L);
        classInfo.setClassName("test-test");
        ciClassInfos.add(classInfo);
        Mockito.when(esClsSvc.getListByQuery(Mockito.any())).thenReturn(ciClassInfos);

        List<CcCiAttrDef> dbDefs = new ArrayList<>();
        CcCiAttrDef dbDef = new CcCiAttrDef();
        dbDef.setProName("测试key");
        dbDef.setOrderNo(1);
        dbDef.setIsMajor(1);
        dbDef.setIsRequired(1);
        dbDef.setProStdName("测试KEY");
        dbDefs.add(dbDef);
        Mockito.when(esClsSvc.getAllDefsByClassId(1L, Mockito.anyLong())).thenReturn(dbDefs);

        Map<String, Page<ESCIInfo>> ciResult = new HashMap<>();
        Page<ESCIInfo> esPage = new Page<>();
        List<ESCIInfo> esInfos = new ArrayList<>();
        ESCIInfo cInfo = new ESCIInfo();
        Map<String, Object> attrs = new HashMap<>();
        attrs.put("测试key", "测试value");
        cInfo.setAttrs(attrs);
        esInfos.add(cInfo);
        esPage.setData(esInfos);
        esPage.setTotalRows(1);
        ciResult.put("scrollId", esPage);
        Mockito.when(esCiSvc.getScrollByQuery(Mockito.anyInt(), Mockito.anyInt(), Mockito.any(), Mockito.anyString(), Mockito.anyBoolean())).thenReturn(ciResult);

        Mockito.when(esCiSvc.clearScroll(Mockito.anyString())).thenReturn(1);

        ExportCiDto exportDto = new ExportCiDto();
        exportDto.setHasData(1);
        exportDto.setCiClassIds(new HashSet<>(Arrays.asList(123L)));
        ciSvc.exportCiOrClass(exportDto);
    }

    @Test
    public void testImportCiExcel() throws Exception {
        Mockito.when(resourceSvc.saveSyncResourceInfo(Mockito.anyString(), Mockito.anyString(), Mockito.anyBoolean(), Mockito.anyBoolean(), Mockito.anyInt())).thenReturn("test");

        File file = new File("./src/test/resources/testdata/ci.xlsx");
        System.out.println(file.getAbsolutePath());
        MockMultipartFile importFile = new MockMultipartFile("file", "ci.xlsx", MediaType.TEXT_PLAIN_VALUE, new FileInputStream(file));

        ImportExcelMessage rs = ciSvc.importCiExcel(importFile);
        Assert.assertEquals("test-test", rs.getSheetNames().get(0));
        // 删除上传的文件
        try {
            File imFile = new File("./localPath/" + ESUtil.getNumberDate());
            FileUtils.deleteDirectory(imFile);
        } catch (Exception e) {
            // TODO: handle exception
        }
    }

    @Test
    public void testImportCiByClassBatch() {
        ReflectionTestUtils.setField(ciSvc, "localPath", "./src/test/resources/testdata/");

        Map<String, Long> map = new HashMap<>();
        map.put("123", 123L);
        Mockito.when(esCiSvc.groupByCountField(Mockito.anyString(), Mockito.any())).thenReturn(map);

        Mockito.when(clsSvc.saveOrUpdate(Mockito.any(CcCiClassInfo.class))).thenReturn(123L);

        List<CcCiAttrDef> dbDefs = new ArrayList<>();
        CcCiAttrDef dbDef = new CcCiAttrDef();
        dbDef.setProName("测试key");
        dbDef.setOrderNo(1);
        dbDef.setIsMajor(1);
        dbDef.setIsRequired(1);
        dbDef.setProStdName("测试KEY");
        dbDefs.add(dbDef);
        List<CcCiClassInfo> ciClassInfos = new ArrayList<>();
        CcCiClassInfo classInfo = new CcCiClassInfo();
        CcCiClass ccCiClass = new CcCiClass();
        ccCiClass.setId(123L);
        ccCiClass.setParentId(123L);
        ccCiClass.setClassName("test-test");
        classInfo.setCiClass(ccCiClass);
        classInfo.setAttrDefs(dbDefs);
        ciClassInfos.add(classInfo);
        Mockito.when(esClsSvc.queryClassByCdt(Mockito.any(CCcCiClass.class))).thenReturn(ciClassInfos);

        Mockito.when(esClsSvc.getAllDefsByClassId(1L, Mockito.anyLong())).thenReturn(dbDefs);

        // saveOrUpdateCiBatchForExcelImport
        List<CcCiAttrDef> defs = new ArrayList<>();
        CcCiAttrDef def = new CcCiAttrDef();
        def.setProName("测试key");
        def.setOrderNo(1);
        def.setIsMajor(1);
        def.setIsRequired(1);
        def.setProStdName("测试KEY");
        defs.add(def);
//        Mockito.when(clsSvc.getAllDefsByClassId(1L, Mockito.anyLong())).thenReturn(defs);
        Page<ESCIInfo> esPage = new Page<>();
        List<ESCIInfo> esInfos = new ArrayList<>();
        esPage.setData(esInfos);
        Mockito.when(esCiSvc.getListByQuery(Mockito.anyInt(), Mockito.anyInt(), Mockito.any())).thenReturn(esPage);
        Mockito.when(commSvc.validAttrs(Mockito.anyList(), Mockito.any(), Mockito.anyBoolean())).thenReturn(new HashMap<>());
        Map<String, Object> result = new HashMap<>();
        result.put("failCount", 0);
        result.put("errMessge", null);
        Mockito.when(esCiSvc.saveOrUpdateCIBatch(Mockito.anyList(), Mockito.anyBoolean())).thenReturn(result);

        ImportResultMessage rm = new ImportResultMessage();
        rm.setSuccessNum(1);
        Mockito.when(resourceSvc.saveSyncResourceInfo(Mockito.anyString(), Mockito.anyString(), Mockito.anyBoolean(), Mockito.anyBoolean(), Mockito.anyInt())).thenReturn("success");

        File file1 = new File("./src/test/resources/testdata/ci.xlsx");
        File file2 = new File("./src/test/resources/testdata/ci-test.xlsx");
        try {
            FileUtils.copyFile(file1, file2);
        } catch (IOException e) {
			throw new MessageException(e.getMessage());
        }
        CiExcelInfoDto bean = new CiExcelInfoDto();
        bean.setDirId(123L);
        bean.setFileName("ci-test.xlsx");
        List<String> sheetNames = new ArrayList<>();
        sheetNames.add("test-test");
        bean.setSheetNames(sheetNames);
        ImportResultMessage rs = ciSvc.importCiByClassBatch(1L, bean, false);
        Assert.assertEquals(1, rs.getSuccessNum().intValue());
    }

    @Test
    public void testDeleteById() {
        Mockito.when(ciRltSvc.delRltByCiId(Mockito.anyLong())).thenReturn(1);
        CcCiInfo ciInfo = new CcCiInfo();
        CcCi ci = new CcCi();
        ci.setClassId(123L);
        ci.setCiCode("测试code");
        ci.setHashCode(131487205);
        ci.setCiVersion("1");
        ci.setCiPrimaryKey("ciPrimaryKey");
        ciInfo.setCi(ci);
        Map<String, String> attrsMap = new HashMap<>();
        attrsMap.put("测试key", "测试value");
        ciInfo.setAttrs(attrsMap);
        CcCiClass ciClass = new CcCiClass();
        ciClass.setClassName("测试");
        ciInfo.setCiClass(ciClass);
        List<CcCiAttrDef> defs = new ArrayList<>();
        CcCiAttrDef def = new CcCiAttrDef();
        def.setProName("测试key");
        def.setOrderNo(1);
        def.setIsMajor(1);
        def.setIsRequired(1);
        def.setProStdName("测试KEY");
        defs.add(def);
        ciInfo.setAttrDefs(defs);
        Mockito.when(esCiSvc.getCiInfoById(Mockito.anyLong())).thenReturn(ciInfo);
        Mockito.when(logSvc.saveOrUpdate(Mockito.any(ESCIOperateLog.class))).thenReturn(123L);
        Mockito.when(esCiSvc.deleteById(Mockito.anyLong())).thenReturn(1);

        Integer rs = ciSvc.deleteById(123L, 1L);
        Assert.assertEquals(1, rs.intValue());
    }

    @Test
    public void testRemoveByIds() {
        Page<CcCiInfo> page = new Page<>();
        List<CcCiInfo> ciInfos = new ArrayList<>();
        CcCiInfo ciInfo = new CcCiInfo();
        CcCi ci = new CcCi();
        ci.setClassId(123L);
        ci.setCiCode("测试code");
        ci.setHashCode(131487205);
        ci.setCiVersion("1");
        ci.setCiPrimaryKey("ciPrimaryKey");
        ciInfo.setCi(ci);
        Map<String, String> attrsMap = new HashMap<>();
        attrsMap.put("测试key", "测试value");
        ciInfo.setAttrs(attrsMap);
        CcCiClass ciClass = new CcCiClass();
        ciClass.setClassName("测试");
        ciInfo.setCiClass(ciClass);
        List<CcCiAttrDef> defs = new ArrayList<>();
        CcCiAttrDef def = new CcCiAttrDef();
        def.setProName("测试key");
        def.setOrderNo(1);
        def.setIsMajor(1);
        def.setIsRequired(1);
        def.setProStdName("测试KEY");
        defs.add(def);
        ciInfo.setAttrDefs(defs);
        ciInfos.add(ciInfo);
        page.setData(ciInfos);
        Mockito.when(esCiSvc.queryCiInfoPage(Mockito.anyInt(), Mockito.anyInt(), Mockito.any(CCcCi.class), Mockito.any(), Mockito.any(), Mockito.anyBoolean()))
            .thenReturn(page);

        Mockito.when(ciRltSvc.delRltByCiIds(Mockito.any())).thenReturn(1);
        Mockito.when(esCiSvc.deleteByIds(Mockito.any())).thenReturn(1);
        
        Mockito.when(logSvc.saveOrUpdateBatch(Mockito.anyList())).thenReturn(123);

        List<Long> ids = new ArrayList<>();
        ciSvc.removeByIds(ids, 1L);
        
        ids.add(123L);
        Integer rs = ciSvc.removeByIds(ids, 1L);
        Assert.assertEquals(1, rs.intValue());
    }

    @Test
    public void testRemoveByClassId() {
        Mockito.when(esCiSvc.countByCondition(Mockito.any())).thenReturn(100L);
        Page<CcCiInfo> page = new Page<>();
        List<CcCiInfo> ciInfos = new ArrayList<>();
        CcCiInfo ciInfo = new CcCiInfo();
        CcCi ci = new CcCi();
        ci.setClassId(123L);
        ci.setCiCode("测试code");
        ci.setHashCode(131487205);
        ci.setCiVersion("1");
        ci.setCiPrimaryKey("ciPrimaryKey");
        ciInfo.setCi(ci);
        Map<String, String> attrsMap = new HashMap<>();
        attrsMap.put("测试key", "测试value");
        ciInfo.setAttrs(attrsMap);
        CcCiClass ciClass = new CcCiClass();
        ciClass.setClassName("className");
        ciInfo.setCiClass(ciClass);
        List<CcCiAttrDef> attrDefs = new ArrayList<>();
        CcCiAttrDef def = new CcCiAttrDef();
        def.setProName("proName");
        attrDefs.add(def);
        ciInfo.setAttrDefs(attrDefs);
        ciInfos.add(ciInfo);
        page.setData(ciInfos);
        Mockito.when(esCiSvc.getCIInfoPageByQuery(Mockito.anyInt(), Mockito.anyInt(), Mockito.any(), Mockito.anyBoolean())).thenReturn(page);
        Mockito.when(logSvc.saveOrUpdateBatch(Mockito.anyList())).thenReturn(123);
        Mockito.when(ciRltSvc.delRltByCiClassId(Mockito.anyLong())).thenReturn(1);
        Mockito.when(esCiSvc.removeByClassId(Mockito.anyLong())).thenReturn(1);

        Integer rs = ciSvc.removeByClassId(123L, 1L);
        Assert.assertEquals(1, rs.intValue());
    }

    @Test
    public void testToStdMap() {
        Map<String, String> map=new HashMap<>();
        map.put("key", "value");
        map.put(null, "null");
        Map<String, String> stdMap = ciSvc.toStdMap(map);
        Assert.assertEquals("value", stdMap.get("KEY"));
    }

    @Test
    public void testCountCiNumGroupClsByQuery() {
        QueryBuilder query = QueryBuilders.boolQuery();
        Mockito.when(commSvc.getCIQueryBuilderByBean(Mockito.any(ESCISearchBean.class))).thenReturn(query);
        Map<String, Long> map = new HashMap<>();
        map.put("123", 123L);
        Mockito.when(esCiSvc.groupByCountField(Mockito.anyString(), Mockito.any())).thenReturn(map);

        Map<Long, Long> rs = ciSvc.countCiNumGroupClsByQuery(null);
        Assert.assertEquals(123L, rs.get(123L).longValue());
    }

    @Test
    public void testGetAttrValuesBySearchBean() {
        Page<String> page = new Page<>(1, 10, 10, 1, Arrays.asList("a", "b"));
        Mockito.when(esCiSvc.queryAttrVal(1L, Mockito.any(ESAttrAggBean.class))).thenReturn(page);

        ESAttrAggBean searchBean = new ESAttrAggBean();
        searchBean.setClassId(123L);
        searchBean.setAttrName("测试");
        Page<String> res = ciSvc.getAttrValuesBySearchBean(searchBean);
        Assert.assertEquals("a", res.getData().get(0));
    }

    @Test
    public void testCountByQuery() {
        Mockito.when(commSvc.getCIQueryBuilderByBean(Mockito.any(ESCISearchBean.class))).thenReturn(QueryBuilders.boolQuery());
        Mockito.when(esCiSvc.countByCondition(Mockito.any())).thenReturn(123L);

        Long res = ciSvc.countByQuery(null);
        Assert.assertEquals(123L, res.longValue());
    }

    @Test
    public void testRemoveByPrimaryKeys() {
        Page<ESCIInfo> esPage = new Page<>();
        List<ESCIInfo> esciInfos = new ArrayList<>();
        ESCIInfo esciInfo = new ESCIInfo();
        esciInfo.setId(123L);
        esciInfos.add(esciInfo);
        esPage.setData(esciInfos);
        Mockito.when(esCiSvc.searchESCIByBean(Mockito.any())).thenReturn(esPage);

        Page<CcCiInfo> ciPage = new Page<>();
        List<CcCiInfo> ciInfos = new ArrayList<>();
        CcCiInfo ciInfo = new CcCiInfo();
        CcCi ci = new CcCi();
        ci.setId(123L);
        ci.setCiCode("ciCode");
        ci.setCiPrimaryKey("ciPrimaryKey");
        ciInfo.setCi(ci);
        Map<String, String> attrs = new HashMap<>();
        attrs.put("key", "value");
        ciInfo.setAttrs(attrs);
        CcCiClass ciClass = new CcCiClass();
        ciClass.setClassName("className");
        ciInfo.setCiClass(ciClass);
        List<CcCiAttrDef> attrDefs = new ArrayList<>();
        CcCiAttrDef def = new CcCiAttrDef();
        def.setProName("proName");
        attrDefs.add(def);
        ciInfo.setAttrDefs(attrDefs);
        ciInfos.add(ciInfo);
        ciPage.setData(ciInfos);
        Mockito.when(commSvc.transEsInfoPage(Mockito.any(), Mockito.anyBoolean())).thenReturn(ciPage);

        Mockito.when(logSvc.saveOrUpdateBatch(Mockito.anyList())).thenReturn(1);

        Mockito.when(ciRltSvc.delRltByCiIds(Mockito.anyList())).thenReturn(1);
        Mockito.when(esCiSvc.deleteByIds(Mockito.anyList())).thenReturn(1);

        List<String> ciPrimaryKeys = new ArrayList<>();
        ciPrimaryKeys.add("ciPrimaryKey");
        Integer res = ciSvc.removeByPrimaryKeys(1L, ciPrimaryKeys, 1L);
        assertEquals(1, res.intValue());
    }
}
