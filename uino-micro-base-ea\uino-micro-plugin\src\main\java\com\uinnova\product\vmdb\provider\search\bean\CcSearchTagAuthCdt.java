package com.uinnova.product.vmdb.provider.search.bean;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

public class CcSearchTagAuthCdt implements Serializable {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private List<Map<String, String>> attrCdts;
	private Integer auth;

	public CcSearchTagAuthCdt() {
	}

	public CcSearchTagAuthCdt(List<Map<String, String>> attrCdts, Integer auth) {
		super();
		this.attrCdts = attrCdts;
		this.auth = auth;
	}

	public List<Map<String, String>> getAttrCdts() {
		return attrCdts;
	}

	public void setAttrCdts(List<Map<String, String>> attrCdts) {
		this.attrCdts = attrCdts;
	}

	public Integer getAuth() {
		return auth;
	}

	public void setAuth(Integer auth) {
		this.auth = auth;
	}

}
