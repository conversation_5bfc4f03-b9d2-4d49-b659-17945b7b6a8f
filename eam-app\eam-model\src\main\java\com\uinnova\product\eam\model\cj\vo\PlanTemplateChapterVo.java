package com.uinnova.product.eam.model.cj.vo;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.model.cj.domain.PlanTemplateChapter;
import com.uinnova.product.eam.model.cj.domain.PlanTemplateChapterData;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @description:
 * @author: Lc
 * @create: 2022-01-06 09:43
 */
@Data
public class PlanTemplateChapterVo implements Serializable {

    @Comment("类型 1:插入章节 2:向下插入章节 3:插入子章节")
    private Integer type;
    @Comment("相对id，当类型为2时使用")
    private Long relativeId;
    @Comment("模板章节基本信息")
    private PlanTemplateChapter planTemplateChapter;
    @Comment("模板章节数据")
    private List<PlanTemplateChapterData> chapterDataList;
}
