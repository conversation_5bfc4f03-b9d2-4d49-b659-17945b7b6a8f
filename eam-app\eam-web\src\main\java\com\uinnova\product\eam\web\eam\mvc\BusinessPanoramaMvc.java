package com.uinnova.product.eam.web.eam.mvc;

import com.alibaba.fastjson.JSONObject;
import com.binary.core.util.BinaryUtils;
import com.binary.framework.web.RemoteResult;
import com.uinnova.product.eam.base.diagram.model.ESDiagram;
import com.uinnova.product.eam.model.AppStrategyDto;
import com.uinnova.product.eam.model.BizNodeDto;
import com.uinnova.product.eam.model.vo.PanoramaCiInfoVo;
import com.uinnova.product.eam.service.IBusinessPanoramaSvc;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uino.bean.cmdb.base.LibType;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @Classname
 * @Description 业务架构全景
 * <AUTHOR>
 * @Date 2021-10-09
 */
@RestController
@RequestMapping("/eam/busPanorama")
public class BusinessPanoramaMvc {

    @Resource
    IBusinessPanoramaSvc businessPanoramaSvc;

    /**
     * 业务组件全景矩阵图
     *
     * @param libType 仓库类型
     * @return 业务组件全景矩阵图数据
     */
    @GetMapping("/getBusMatrixView")
    @ResponseBody
    public RemoteResult getBusMatrixView(@RequestParam(defaultValue = "DESIGN") LibType libType) {

        Map<String, Object> resultMap = businessPanoramaSvc.getBusMatrixView(libType);
        return new RemoteResult(resultMap);
    }

    @ModDesc(desc = "获取业务能力全景图信息", pDesc = "对象库", pType = String.class, rDesc = "业务能力信息", rType = AppStrategyDto.class)
    @GetMapping("/getBusinessTree")
    public RemoteResult getBusinessTree(@RequestParam(defaultValue = "DESIGN") LibType libType) {
        List<BizNodeDto> result = businessPanoramaSvc.getBusinessTree(libType);
        return new RemoteResult(result);
    }

    @GetMapping("/queryPanoramaId")
    @ModDesc(desc = "查询全景墙视图id", pDesc = "", rDesc = "视图信息", rType = RemoteResult.class)
    public RemoteResult queryPanoramaId(@RequestParam(defaultValue = "DESIGN") LibType libType, @RequestParam String codeName) {
       /* JSONObject jsonObj = JSONObject.parseObject(body);
        String codeName = jsonObj.getString("codeName");*/
        return new RemoteResult(businessPanoramaSvc.queryPanoramaId(codeName, libType));
    }


    @PostMapping("/saveOrUpdatePanoramaDict")
    @ModDesc(desc = "更新全景全在字典里面的diagramId", pDesc = "", rDesc = "全景墙diagramId", rType = RemoteResult.class)
    public RemoteResult saveOrUpdatePanoramaDict(@RequestParam(defaultValue = "DESIGN") LibType libType, @RequestBody String body) {
        JSONObject jsonObj = JSONObject.parseObject(body);
        BinaryUtils.checkEmpty(jsonObj.get("diagramId"),"diagramId");
        BinaryUtils.checkEmpty(jsonObj.get("codeName"),"codeName");
        String diagramId = businessPanoramaSvc.saveOrUpdatePanoramaDict(jsonObj, libType);
        return new RemoteResult(diagramId);
    }


    @PostMapping("/selectPanoramicWallDiagram")
    @ModDesc(desc = "查询全景墙所有发布视图", pDesc = "", rDesc = "视图信息", rType = RemoteResult.class)
    public RemoteResult selectPanoramicWallDiagram(@RequestParam(defaultValue = "DESIGN") LibType libType,@RequestBody String body) {
        List<ESDiagram> result = businessPanoramaSvc.selectPanoramicWallDiagram(libType,body);
        return new RemoteResult(result);
    }

    @ModDesc(desc = "通过数据集获取业务能力全景图信息", pDesc = "数据集id", pType = Long.class, rDesc = "业务能力信息", rType = Object.class)
    @GetMapping("/business/tree")
    public RemoteResult businessTree(@RequestParam Long dataSetId) {
        List<PanoramaCiInfoVo> result = businessPanoramaSvc.businessTree(dataSetId);
        return new RemoteResult(result);
    }

    @ModDesc(desc = "通过数据集获取业务能力全景ci信息", pDesc = "数据集id", pType = Long.class, rDesc = "业务能力ci信息", rType = Object.class)
    @GetMapping("/business/ci")
    public RemoteResult businessCi(@RequestParam Long dataSetId) {
        List<CcCiInfo> result = businessPanoramaSvc.businessCi(dataSetId);
        return new RemoteResult(result);
    }
}
