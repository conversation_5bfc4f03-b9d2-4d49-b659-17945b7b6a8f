package com.uino.bean.cmdb.business;

import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 元模型上使用包含起始分类和目标分类的关系对象信息
 *
 * <AUTHOR>
 * @version 2020-8-13
 */
@ApiModel(value="关系对象信息类",description = "元模型上使用包含起始分类和目标分类的关系对象信息")
public class DataModuleRltClassDto {
    /**
     * 起始分类ID
     */
    @ApiModelProperty(value="起始分类ID",example="23456")
    public Long sourceClassId;

    /**
     * 目标分类ID
     */
    @ApiModelProperty(value="目标分类ID",example = "123456")
    public Long targetClassId;

    /**
     * 关系分类信息
     */
    @ApiModelProperty(value="关系分类信息")
    public CcCiClassInfo rltClassInfo;

    public Long getSourceClassId() {
        return sourceClassId;
    }

    public void setSourceClassId(Long sourceClassId) {
        this.sourceClassId = sourceClassId;
    }

    public Long getTargetClassId() {
        return targetClassId;
    }

    public void setTargetClassId(Long targetClassId) {
        this.targetClassId = targetClassId;
    }

    public CcCiClassInfo getRltClassInfo() {
        return rltClassInfo;
    }

    public void setRltClassInfo(CcCiClassInfo rltClassInfo) {
        this.rltClassInfo = rltClassInfo;
    }

}
