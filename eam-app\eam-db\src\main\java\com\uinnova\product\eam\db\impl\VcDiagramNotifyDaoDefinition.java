package com.uinnova.product.eam.db.impl;


import com.binary.framework.dao.DaoDefinition;

import com.uinnova.product.eam.comm.model.CVcDiagramNotify;
import com.uinnova.product.eam.comm.model.VcDiagramNotify;


/**
 * 视图通知表[VC_DIAGRAM_NOTIFY]数据访问对象定义实现
 */
public class VcDiagramNotifyDaoDefinition implements DaoDefinition<VcDiagramNotify, CVcDiagramNotify> {


	@Override
	public Class<VcDiagramNotify> getEntityClass() {
		return VcDiagramNotify.class;
	}


	@Override
	public Class<CVcDiagramNotify> getConditionClass() {
		return CVcDiagramNotify.class;
	}


	@Override
	public String getTableName() {
		return "VC_DIAGRAM_NOTIFY";
	}


	@Override
	public boolean hasDataStatusField() {
		return false;
	}


	@Override
	public void setDataStatusValue(VcDiagramNotify record, int status) {
	}


	@Override
	public void setDataStatusValue(CVcDiagramNotify cdt, int status) {
	}


	@Override
	public void setCreatorValue(VcDiagramNotify record, String creator) {
		record.setCreator(creator);
	}


	@Override
	public void setModifierValue(VcDiagramNotify record, String modifier) {
		record.setModifier(modifier);
	}

}


