package com.uinnova.product.eam.model;

import java.io.Serializable;

public class VcMetaModelDiagramInfo implements Serializable{

	private static final long serialVersionUID = 1L;
	
	/**
	 * 元模型视图ID
	 */
	private Long id;
	
	/**
	 * 元模型视图名称
	 */
	private String name;
	
	/**
	 * 用户ID
	 */
	private String userId;
	
	/**
	 * 是否发布
	 */
	private Integer isOpen;
	
	/**
	 * 视图JSON
	 */
	private String modelJson;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public Integer getIsOpen() {
		return isOpen;
	}

	public void setIsOpen(Integer isOpen) {
		this.isOpen = isOpen;
	}

	public String getModelJson() {
		return modelJson;
	}

	public void setModelJson(String modelJson) {
		this.modelJson = modelJson;
	}

	
	
}
