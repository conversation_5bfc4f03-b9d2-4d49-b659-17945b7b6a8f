package com.uino.service.sys.microservice;

import com.uino.bean.sys.base.ESOperateLogModule;

import java.util.List;

public interface IOperateLogModuleSvc {

    /**
     * 获取所有操作日志模块
     * @return
     */
    public List<ESOperateLogModule> getAll();

    /**
     * 通过mvc路径获取Module信息
     * 如果有多个匹配，则匹配最长路径
     * @param mvcPath Mvc路径
     * @return 匹配到的Module信息
     */
    public ESOperateLogModule getModuleInfoByMvc(String mvcPath);
}
