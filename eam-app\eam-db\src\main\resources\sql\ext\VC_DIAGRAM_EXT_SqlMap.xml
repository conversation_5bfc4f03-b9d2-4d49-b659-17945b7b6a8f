<?xml version="1.0" encoding="UTF-8"?><!--Converted at: Sat Dec 08 16:24:25 CST 2018-->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="VC_DIAGRAM">

	<resultMap id="queryDiagramTagGroup" type="com.uinnova.product.eam.db.bean.DiagramTagGroup">
		<result property="tagId" column="TAG_ID" jdbcType="BIGINT"/>	<!-- 分类ID -->
		<result property="tagName" column="TAG_NAME" jdbcType="CHAR"/>	<!-- 分类ID -->
		<result property="defDesc" column="DEF_DESC" jdbcType="CHAR"/>	<!-- 标签描述 -->
		<result property="diagramCount" column="COUNT" jdbcType="BIGINT"/>	<!-- 分组值 -->
	
	</resultMap>
	
	<resultMap id="queryDiagramUserGroup" type="com.uinnova.product.eam.db.bean.DiagramUserGroup">
		<result property="userId" column="USER_ID" jdbcType="BIGINT"/>	<!-- 分类ID -->
		<result property="userName" column="USER_NAME" jdbcType="CHAR"/>	
		<result property="diagramCount" column="COUNT" jdbcType="BIGINT"/>	<!-- 分组值 -->
	</resultMap>
	
	<resultMap id="diagramInfoCount" type="com.uinnova.product.eam.db.bean.DiagramInfoCount">
		<result property="id" column="ID" jdbcType="BIGINT"/>
		<result property="diagramName" column="DIAGRAM_NAME" jdbcType="CHAR"/>
		<result property="modifyTime" column="MODIFY_TIME" jdbcType="BIGINT"/>
		<result property="readCount" column="READ_COUNT" jdbcType="BIGINT"/>
		<result property="enshCount" column="ENSH_COUNT" jdbcType="BIGINT"/>
	</resultMap>
	
	<resultMap id="groupDiagramInfoCount" type="com.uinnova.product.eam.db.bean.GroupDiagramInfoCount">
		<result property="id" column="ID" jdbcType="BIGINT"/>
		<result property="groupName" column="GROUP_NAME" jdbcType="CHAR"/>
		<result property="diagramId" column="DIAGRAM_ID" jdbcType="BIGINT"/>
		<result property="diagramReadCount" column="DIAGRAM_READ_COUNT" jdbcType="BIGINT"/>
	</resultMap>
	
	<resultMap id="userDiagramInfoCount" type="com.uinnova.product.eam.db.bean.UserDiagramInfoCount">
		<result property="id" column="ID" jdbcType="BIGINT"/>
		<result property="opCode" column="OP_CODE" jdbcType="CHAR"/>
		<result property="opName" column="OP_NAME" jdbcType="CHAR"/>
		<result property="createCount" column="CREATE_COUNT" jdbcType="BIGINT"/>
		<result property="readCount" column="READ_COUNT" jdbcType="BIGINT"/>
	</resultMap>
	
	<resultMap id="mapCount" type="com.uinnova.product.eam.db.bean.UserDiagramInfoCount">
		<result property="id" column="ID" jdbcType="BIGINT"/>
		<result property="openCount" column="OPEN_COUNT" jdbcType="BIGINT"/>
		<result property="diagramId" column="DIAGRAM_ID" jdbcType="BIGINT"/>
		<result property="diagramType" column="DIAGRAM_TYPE" jdbcType="BIGINT"/>
	</resultMap>
	
	
	
	

	<select id="selectOpenListExt" parameterType="java.util.Map" resultMap="queryResult">
		select 
			<include refid="VC_DIAGRAM.sql_query_columns"/>
		from VC_DIAGRAM where is_open=1 and status = 1
				<include refid="VC_DIAGRAM.sql_query_where"/>
				<if test="likes != null and likes != ''">and
					${likes}
				</if>
				<if test="userIds != null and userIds != ''">and
					user_id in (${userIds})
				</if>
				<if test="groupIds != null and groupIds != ''">and
					id in (select DIAGRAM_ID from VC_DIAGRAM_GROUP where GROUP_ID in (${groupIds})
								<if test="cdt != null and cdt.domainId != null">and
									DOMAIN_ID = #{cdt.domainId:BIGINT}
								</if>
							)
				</if>
				<if test="tagIds != null and tagIds != ''">and
					id in (select DIAGRAM_ID from VC_DIAGRAM_TAG where TAG_ID in (${tagIds})
								<if test="cdt != null and cdt.domainId != null">and
									DOMAIN_ID = #{cdt.domainId:BIGINT}
								</if>
							)
				</if>
				
				<if test="ciTagOrCiLike != null and ciTagOrCiLike != ''">and
					id in (select DIAGRAM_ID from VC_DIAGRAM_CI
						<if test="ciTagIds != null and ciTagIds != ''">
							inner join CC_CI_TAG on VC_DIAGRAM_CI.ci_id=CC_CI_TAG.ci_id and tag_id in (${ciTagIds})
						</if>
						<if test="ciExps != null and ciExps != ''">
							inner join CC_CI_INDEX on VC_DIAGRAM_CI.ci_id=CC_CI_INDEX.id and ${ciExps}
						</if>
					)
				</if>
				
		order by 
			<if test="orders != null and orders != ''">
				${orders}
			</if>
			<if test="orders == null or orders == ''">
				ID
			</if>
	</select>
	
	<select id="selectWorkabilityPage" parameterType="java.util.Map" resultMap="queryResult">
		select
		<include refid="VC_DIAGRAM.sql_query_columns"/>
		from VC_DIAGRAM where (
			ID in (
				select DIAGRAM_ID from VC_DIAGRAM_GROUP where GROUP_ID in (
					select GROUP_ID from VC_GROUP_USER where USER_ID = #{userId:BIGINT}
				)
			)
			or
			USER_ID = #{userId:BIGINT}
			or
			IS_OPEN = 1
		)
		<include refid="VC_DIAGRAM.sql_query_where" />
		order by
		<if test="orders != null and orders != ''">
			${orders}
		</if>
		<if test="orders == null or orders == ''">
			ID
		</if>
	</select>
	
	
	<select id="selectListByCiIdsExt" parameterType="java.util.Map" resultMap="queryResult">
		select
		<include refid="VC_DIAGRAM.sql_query_columns"/>
		from VC_DIAGRAM where (
			ID in (
				select DIAGRAM_ID from VC_DIAGRAM_ELE where (ELE_TYPE = 2 and ELE_ID in (
					select distinct TAG_ID from CC_CI_TAG where DOMAIN_ID =#{cdt.domainId:BIGINT} and CI_ID in (${ciIds})
				) ) OR (ELE_ID in (${ciIds}) and ELE_TYPE = 1 and DOMAIN_ID =#{cdt.domainId:BIGINT})
			)
		)
		
		<include refid="VC_DIAGRAM.sql_query_where" />
		order by
		<if test="orders != null and orders != ''">
			${orders}
		</if>
		<if test="orders == null or orders == ''">
			ID
		</if>
	</select>
	
	<select id="selectOpenDiagramCountByTag" parameterType="java.util.Map" resultMap="queryDiagramTagGroup">
		select t.ID TAG_ID,t.TAG_NAME TAG_NAME,t.DEF_DESC DEF_DESC,count(dt.DIAGRAM_ID) COUNT
		from 
			VC_DIAGRAM_TAG dt
		INNER JOIN VC_TAG t on t.ID = dt.TAG_ID
			and t.TAG_TYPE in (1, 3)

			<if test="tagName != null and tagName != ''">and
				t.TAG_NAME like '${tagName}'
			</if>
		
		INNER JOIN VC_DIAGRAM d on 
			dt.DIAGRAM_ID = d.ID and 
			d.DOMAIN_ID = #{domainId:BIGINT} and d.DATA_STATUS = 1 and 
			d.IS_OPEN = 1 and d.STATUS = 1 
			<if test="userIds != null and userIds != ''">and
				d.USER_ID in (${userIds})
			</if>
		group BY
			t.TAG_NAME,t.ID,t.DEF_DESC
		ORDER BY 
			<if test="orders != null and orders != ''">
				${orders}
			</if>
			<if test="orders == null or orders == ''">
				t.TAG_NAME
			</if>
	</select>
	
	<select id="selectOpenDiagramCountByUser" parameterType="java.util.Map" resultMap="queryDiagramUserGroup">
		SELECT d.user_id USER_ID,o.OP_NAME USER_NAME,COUNT(d.id) COUNT
		from 
			VC_DIAGRAM  d
		INNER JOIN SYS_OP o ON o.ID = d.USER_ID 
			<if test="userIds != null and userIds != ''">and
				o.ID in (${userIds})
			</if>
			<if test="userName != null and userName != ''">and
				o.OP_NAME like '${userName}'
			</if>
		WHERE d.DATA_STATUS = 1 AND d.STATUS = 1 AND d.IS_OPEN = 1 
		AND d.DOMAIN_ID = #{domainId:BIGINT}
		GROUP BY d.USER_ID,o.OP_NAME
		ORDER BY 
			<if test="orders != null and orders != ''">
				${orders}
			</if>
			<if test="orders == null or orders == ''">
				o.OP_NAME
			</if>
	</select>
	
	
	<select id="selectDiagramEditAuth" parameterType="java.util.Map" resultType="java.lang.Long">
		select count(1) from VC_DIAGRAM d
			INNER join VC_DIAGRAM_GROUP dg on d.ID = dg.DIAGRAM_ID 
			inner join VC_GROUP g on dg.GROUP_ID = g.ID
			inner join VC_GROUP_USER gu on gu.GROUP_ID = g.ID and gu.USER_ID = #{userId:BIGINT}  and gu.AUTH_REGION = 1
		where d.ID = #{id:BIGINT} and d.DATA_STATUS = 1 and d.STATUS = 1 and d.DOMAIN_ID =  #{domainId:BIGINT}
	</select>
	
	
	
	
	<!-- 以下是EMV需求：删除组合视图时要删除该视图对应的场景(如果存在场景) -->
	
	<resultMap id="querySceneDiagram" type="com.uinnova.product.eam.comm.bean.SceneDiagram">
		<result property="id" column="ID" jdbcType="BIGINT"/>	<!-- 场景视图关联ID -->
		<result property="sceneId" column="SCENE_ID" jdbcType="BIGINT"/>	<!-- 场景ID -->
		<result property="diagramId" column="DIAGRAM_ID" jdbcType="BIGINT"/>	<!-- 视图ID -->
		<result property="diagramType" column="DIAGRAM_TYPE" jdbcType="INTEGER"/>	<!-- 视图类型 -->
	</resultMap>
	
	
	<select id="selectSceneDiagramListByDids" parameterType="java.util.Map" resultMap="querySceneDiagram">
		select 
			ID, SCENE_ID, DIAGRAM_ID, DIAGRAM_TYPE
		from SC_SCENE_DIAGRAM 
		where DOMAIN_ID =  #{domainId:BIGINT} and DATA_STATUS = 1
		<if test="diagramType != null and diagramType != ''">and
			DIAGRAM_TYPE = ${diagramType}
		</if>
		<if test="idsSql != null and idsSql != ''">and
			DIAGRAM_ID in (${idsSql})
		</if>
	</select>
	
	
	<delete id="deleteSceneDiagramBySceneIds" parameterType="java.util.Map">
		delete from SC_SCENE_DIAGRAM where SCENE_ID in (${idsSql})
	</delete>
	
	
	<delete id="deleteSceneBySceneIds" parameterType="java.util.Map">
		delete from SC_SCENE where ID in (${idsSql})
	</delete>
	
	<update id="updateReadCountById" parameterType="java.util.Map">
		update VC_DIAGRAM set READ_COUNT = COALESCE(READ_COUNT,0)+1 where DOMAIN_ID = #{domainId:BIGINT} and ID = #{id:BIGINT}
	</update>
	
	<select id="countDiagramInfo" parameterType="java.util.Map" resultMap="diagramInfoCount">
		select a.ID ID, a.name DIAGRAM_NAME,a.MODIFY_TIME MODIFY_TIME from VC_DIAGRAM a
		INNER JOIN SYS_OP s ON s.ID = a.USER_ID
				where a.DOMAIN_ID = #{domainId:BIGINT} and a.DATA_STATUS = 1 and a.status = 1 and s.DATA_STATUS = 1
				<if test="startDate != null ">and
					a.MODIFY_TIME &gt;= #{startDate:BIGINT}
				</if>
				<if test="endDate != null ">and
					a.MODIFY_TIME &lt;= #{endDate:BIGINT}
				</if>
					GROUP by a.ID,a.name,a.MODIFY_TIME
					ORDER BY
					<if test="orders != null and orders != ''">
						${orders}
					</if>
					<if test="orders == null or orders == ''">
						MODIFY_TIME DESC
					</if>
	</select>

	<select id="countDiagramInfoReadCount" parameterType="java.util.Map" resultMap="diagramInfoCount">
		select a.DIAGRAM_ID ID,b.NAME DIAGRAM_NAME, b.MODIFY_TIME as MODIFY_TIME, count(a.id) as READ_COUNT from vc_diagram_operation_record a
		LEFT JOIN vc_diagram b on a.DIAGRAM_ID = b.ID
		where a.OPERATION_TYPE = 1 and b.DATA_STATUS =1 and b.STATUS = 1
			<if test="startDate != null ">and
				a.CREATE_TIME &gt;= #{startDate:BIGINT}
			</if>
			<if test="endDate != null ">and
				a.CREATE_TIME &lt;= #{endDate:BIGINT}
			</if>
		GROUP BY a.DIAGRAM_ID, b.NAME
	</select>

	<select id="countDiagramInfoEnshCount" parameterType="java.util.Map" resultMap="diagramInfoCount">
		select a.DIAGRAM_ID ID,b.NAME DIAGRAM_NAME, b.MODIFY_TIME as MODIFY_TIME, count(a.id) as ENSH_COUNT from vc_diagram_ensh a
		LEFT JOIN vc_diagram b on a.DIAGRAM_ID = b.ID
		where a.DATA_STATUS = 1 and b.DATA_STATUS =1 and b.STATUS = 1
			<if test="startDate != null ">and
				a.CREATE_TIME &gt;= #{startDate:BIGINT}
			</if>
			<if test="endDate != null ">and
				a.CREATE_TIME &lt;= #{endDate:BIGINT}
			</if>
		GROUP BY a.DIAGRAM_ID, b.NAME
	</select>
	
	<select id="countOpenDiagramGroupByUser" parameterType="java.util.Map" resultMap="mapCount">
		select 	a.USER_ID ID,
				count( b.DIAGRAM_ID ) OPEN_COUNT,
				a.id DIAGRAM_ID,
				a.diagram_type DIAGRAM_TYPE from vc_diagram a
			LEFT JOIN vc_diagram_operation_record b on b.DIAGRAM_ID = a.ID AND b.OPERATION_TYPE = 2
			<if test="startDate != null ">and
				b.CREATE_TIME &gt;= #{startDate:BIGINT}
			</if>
			<if test="endDate != null ">and
				b.CREATE_TIME &lt;= #{endDate:BIGINT}
			</if>
			where a.DOMAIN_ID = #{domainId:BIGINT} and a.DATA_STATUS = 1 and a.STATUS = 1 and a.IS_OPEN = 1
			GROUP BY a.ID, a.USER_ID, a.DIAGRAM_TYPE
		
	</select>
	
	<select id="countGroupDiagramInfo" parameterType="java.util.Map" resultMap="groupDiagramInfoCount">
		select a.ID ID,a.GROUP_NAME GROUP_NAME,c.id DIAGRAM_ID  from VC_GROUP a
			LEFT JOIN VC_DIAGRAM_GROUP b on a.id = b.group_id and b.DOMAIN_ID = #{domainId:BIGINT}
			<if test="startDate != null ">and
				b.CREATE_TIME &gt;= #{startDate:BIGINT}
			</if>
			<if test="endDate != null ">and
				b.CREATE_TIME &lt;= #{endDate:BIGINT}
			</if>
			LEFT JOIN VC_DIAGRAM c on c.id = b.diagram_id and c.data_status = 1 and c.domain_id =#{domainId:BIGINT} and c.status = 1
			WHERE a.DATA_STATUS = 1 and a.DOMAIN_ID = #{domainId:BIGINT}
	</select>
	
	<select id="countUserDiagramInfo" parameterType="java.util.Map" resultMap="userDiagramInfoCount">
		select a.id ID,a.OP_CODE OP_CODE,a.OP_NAME OP_NAME,count(distinct b.id) CREATE_COUNT from sys_op a
			LEFT JOIN VC_DIAGRAM b on a.id = b.USER_ID and b.DATA_STATUS = 1
			and b.STATUS = 1 and b.DOMAIN_ID = #{domainId:BIGINT} AND b.diagram_type IN (1, 3)
            <if test="startDate != null ">and
                b.CREATE_TIME &gt;= #{startDate:BIGINT}
            </if>
            <if test="endDate != null ">and
                b.CREATE_TIME &lt;= #{endDate:BIGINT}
            </if>
				where a.DATA_STATUS = 1 and a.STATUS = 1 and a.USER_DOMAIN_ID = #{domainId:BIGINT} and a.id != 0
					GROUP BY a.id,a.OP_CODE,a.OP_NAME
						ORDER BY
							<if test="orders != null and orders != ''">
								${orders}
							</if>
							<if test="orders == null or orders == ''">
								CREATE_COUNT DESC
							</if>
	</select>
	
	<select id="countAllDiagramCount" parameterType="java.util.Map" resultType="java.lang.Long">
		SELECT
			count( 1 ) 
		FROM
			VC_DIAGRAM 
		WHERE
			DIAGRAM_TYPE IN ( 1, 3 ) 
			AND DATA_STATUS = 1 
			AND STATUS=1
			AND USER_ID IN ( SELECT ID FROM sys_op WHERE DATA_STATUS = 1 )
			AND DOMAIN_ID = #{domainId:BIGINT}
	</select>
	
	<update id="updateAppRltCiCodeNullByRltCiCode" parameterType="java.util.Map">
		update VC_DIAGRAM set APP_RLT_CI_CODE = null where DOMAIN_ID = #{domainId:BIGINT} and APP_RLT_CI_CODE = #{appRltCiCode,jdbcType=VARCHAR} 
	</update>
	
	<select id="selectListByCdtAndNotInIds" parameterType="java.util.Map" resultMap="queryResult">
		select 
			<include refid="VC_DIAGRAM.sql_query_columns"/>
		from VC_DIAGRAM 
			<where>
				<include refid="VC_DIAGRAM.sql_query_where"/>
			</where>
			<if test="notInIds != null and notInIds != ''">and
				ID not in (${notInIds})
			</if>
		order by 
			<if test="orders != null and orders != ''">
				${orders}
			</if>
			<if test="orders == null or orders == ''">
				ID
			</if>
	</select>
	
	<resultMap id="digramDirCount" type="com.uinnova.product.eam.db.bean.DiagramDirCount">
		<result property="dirId" column="DIR_ID" jdbcType="BIGINT"/>
		<result property="dirName" column="DIR_NAME" jdbcType="CHAR"/>
		<result property="dirType" column="DIR_TYPE" jdbcType="INTEGER"/>
		<result property="parentId" column="PARENT_ID" jdbcType="BIGINT"/>
		<result property="userId" column="USER_ID" jdbcType="BIGINT"/>
		<result property="diagramCount" column="DIAGRAM_COUNT" jdbcType="INTEGER"/>
	</resultMap>
	
	<select id="selectDirDiagramCountList" parameterType="java.util.Map" resultMap="digramDirCount">
		select a.ID DIR_ID,a.DIR_NAME DIR_NAME,a.DIR_TYPE DIR_TYPE,a.PARENT_ID PARENT_ID ,a.USER_ID USER_ID,COUNT(b.ID) DIAGRAM_COUNT 
			from vc_diagram_dir a LEFT JOIN vc_diagram b
				ON b.DIR_ID = a.ID and b.DIR_TYPE = a.DIR_TYPE and b.STATUS = 1 and b.DATA_STATUS = 1 and b.DOMAIN_ID = #{domainId:BIGINT} and b.USER_ID = #{userId:BIGINT}
			where a.DATA_STATUS = 1 and a.DOMAIN_ID = #{domainId:BIGINT} and a.USER_ID =  #{userId:BIGINT} and a.ID in (${dirIds}) 
			 GROUP BY a.ID,a.DIR_NAME,a.DIR_TYPE,a.PARENT_ID ,a.USER_ID
	</select>
	
</mapper>