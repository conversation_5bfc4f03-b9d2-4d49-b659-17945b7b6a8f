package com.uinnova.product.eam.base.diagram.mix.model;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.base.diagram.model.*;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class VcDiagramVersionInfo implements Serializable {
	private static final long serialVersionUID = 1L;

	@Comment("历史视图")
	private VcDiagramVersion diagramVersion;
	
	@Comment("历史视图元素关系")
	private List<VcDiagramEleVersion> diagramEleVersions;
		
	@Comment("历史视图显示属性关系")
	private List<VcDiagramCiAttrVersion> ciAttrVersions;
	
	@Comment("视图标签关系")
	private List<VcDiagramTag> diagramTags;
	
	@Comment("标签")
	private List<VcTag> tags;
	
	@Comment("视图组关系")
	private List<VcDiagramGroup> diagramGroups;

	@Comment("视图组信息")
	private List<VcGroup> groups;
	
	@Comment("xml信息")
	private String xml;
	
	@Comment("svg信息")
	private String svg;

	@Comment("ci3dPoint")
	private String ci3dPoint;
	
	@Comment("ci 3d DMV坐标信息")
	private String ci3dPoint2;
	
	@Comment("更新的关系数据")
	private List<String> upRelations;
	
	@Comment("视图json信息")
	private String json;
	

}
