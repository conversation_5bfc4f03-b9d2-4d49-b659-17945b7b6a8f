package com.uino.dao.sys;

import jakarta.annotation.PostConstruct;

import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.uino.dao.AbstractESBaseDao;
import com.uino.dao.ESConst;
import com.uino.bean.cmdb.base.ESTableDataConfigInfo;

/**
 * ES-CI配置
 * 
 * <AUTHOR>
 */
@Service
public class ESTableDataConfigSvc extends AbstractESBaseDao<ESTableDataConfigInfo, JSONObject> {

    @Override
    public String getIndex() {
        return ESConst.INDEX_TABLE_DATA_CONFIG;
    }

    @Override
    public String getType() {
        return ESConst.INDEX_TABLE_DATA_CONFIG;
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }

}
