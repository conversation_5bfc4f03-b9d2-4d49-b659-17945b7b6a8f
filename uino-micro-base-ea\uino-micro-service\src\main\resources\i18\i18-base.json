[{"lanEn": "${name}${value}${type}", "createTime": 20170328143337, "lanZhc": "${name}${value}${type}", "classCode": "01", "code": "BS_VERIFY_ERROR", "modifyTime": 20181019143710}, {"lanEn": "Parameter error", "createTime": 20181019143558, "lanZhc": "参数错误", "classCode": "01", "code": "BS_MVTYPE_ARG_ERROR", "modifyTime": 20181019144523}, {"lanEn": "Incorrect query condition", "createTime": 20181019143619, "lanZhc": "查询条件错误", "classCode": "01", "code": "BS_MVTYPE_CDT", "modifyTime": 20181019144512}, {"lanEn": "Date type does not match", "createTime": 20181019143636, "lanZhc": "日期类型不匹配", "classCode": "01", "code": "BS_MVTYPE_DATE_MISMATCH", "modifyTime": 20181019144538}, {"lanEn": "Data length or type does not match", "createTime": 20181019143648, "lanZhc": "小数长度或类型不匹配", "classCode": "01", "code": "BS_MVTYPE_DB_MISMATCH", "modifyTime": 20181029190143}, {"lanEn": "repeat", "createTime": 20181019143702, "lanZhc": "重复", "classCode": "01", "code": "BS_MVTYPE_DUPLICATE", "modifyTime": 20181019144600}, {"lanEn": "cannot be empty", "createTime": 20170328143028, "lanZhc": "不可为空", "classCode": "01", "code": "BS_MVTYPE_EMPTY", "modifyTime": 20181101202221}, {"lanEn": "Enumeration type does not match", "createTime": 20181019143743, "lanZhc": "枚举类型不匹配", "classCode": "01", "code": "BS_MVTYPE_ENUM_MISMATCH", "modifyTime": 20181019144626}, {"lanEn": "existed", "createTime": 20170328143106, "lanZhc": "已存在", "classCode": "01", "code": "BS_MVTYPE_EXIST", "modifyTime": 20181019144643}, {"lanEn": "Data length or type does not match", "createTime": 20181019143818, "lanZhc": "整数长度或类型不匹配", "classCode": "01", "code": "BS_MVTYPE_INT_MISMATCH", "modifyTime": 20181029190542}, {"lanEn": "Long text type does not match", "createTime": 20181019143829, "lanZhc": "长文本类型不匹配", "classCode": "01", "code": "BS_MVTYPE_LONGSTR_MISMATCH", "modifyTime": 20181019144706}, {"lanEn": "does not exist", "createTime": 20181019143841, "lanZhc": "不存在", "classCode": "01", "code": "BS_MVTYPE_NOT_EXIST", "modifyTime": 20181019144722}, {"lanEn": "does not null", "createTime": 20181019143851, "lanZhc": "不可为空", "classCode": "01", "code": "BS_MVTYPE_NULL", "modifyTime": 20181019144731}, {"lanEn": "extra long", "createTime": 20181019143902, "lanZhc": "超长", "classCode": "01", "code": "BS_MVTYPE_OVERLENGTH", "modifyTime": 20181019144744}, {"lanEn": "Short text type does not match", "createTime": 20181019143912, "lanZhc": "短文本类型不匹配", "classCode": "01", "code": "BS_MVTYPE_STR_MISMATCH", "modifyTime": 20181019144758}, {"lanEn": "using", "createTime": 20181019143923, "lanZhc": "使用中", "classCode": "01", "code": "BS_MVTYPE_USING", "modifyTime": 20181019144819}, {"lanEn": "userId", "createTime": 20170328143337, "lanZhc": "用戶ID", "classCode": "02", "code": "BS_MNAME_USERID", "modifyTime": 20181019143710}, {"lanEn": "org", "createTime": 20170328143337, "lanZhc": "组织", "classCode": "02", "code": "BS_MNAME_ORG", "modifyTime": 20181019143710}, {"lanEn": "role", "createTime": 20170328143337, "lanZhc": "角色", "classCode": "02", "code": "BS_MNAME_ROLE", "modifyTime": 20181019143710}, {"lanEn": "The current user cannot be deleted", "createTime": 20170328143028, "lanZhc": "不可删除当前用户", "classCode": "04", "code": "BS_USER_DELETE_CUR_ERROR", "modifyTime": 20181101202221}, {"lanEn": "Not authority!", "createTime": 20180104115209, "lanZhc": "没有相应权限!", "classCode": "02", "code": "BS_NOT_AUTH", "modifyTime": 20181015094150}, {"lanEn": "Wrong old password!", "createTime": 20181018182158, "lanZhc": "原密码错误！", "classCode": "04", "code": "BS_OLD_PASSWORD_ERROR", "modifyTime": 20181019145751}, {"lanEn": "User [${loginCode}] does not exist!", "createTime": 20181019101337, "lanZhc": "用户[${loginCode}]不存在！", "classCode": "04", "code": "BS_USER_NOT_EXIST", "modifyTime": 20181019150101}, {"lanEn": "Search character must be a maximum of 200 characters in length.", "createTime": 20181023182057, "lanZhc": "搜索字符不能超过200位", "classCode": "04", "code": "BS_SEARCH_LENGTH_ERR", "modifyTime": 20190313171939}, {"lanEn": "Login name must be a maximum of 50 characters in length.", "createTime": 20181023182057, "lanZhc": "登录名不能超过50位", "classCode": "04", "code": "BS_USER_LOGIN_NAME_LENGTH_ERR", "modifyTime": 20190313171939}, {"lanEn": "Login name only can be letters/numbers/dashes(-)/periods(.)/@ symbol(@)/underscores(_)", "createTime": 20181023182057, "lanZhc": "登录名仅支持字母、数字以及(.-_@)", "classCode": "04", "code": "BS_USER_LOGIN_NAME_FORMAT_ERR", "modifyTime": 20190313171939}, {"lanEn": "Username must be a maximum of 50 characters in length.", "createTime": 20181023182057, "lanZhc": "用户名不能超过50位", "classCode": "04", "code": "BS_USER_USER_NAME_LENGTH_ERR", "modifyTime": 20190313171939}, {"lanEn": "The username format is incorrect!", "createTime": 20181023182057, "lanZhc": "用户名格式不正确", "classCode": "04", "code": "BS_USER_NAME_FORMAT_ERR", "modifyTime": 20190313171939}, {"lanEn": "E-mail must be a maximum of 50 characters in length.", "createTime": 20181023182057, "lanZhc": "邮箱不能超过50位", "classCode": "04", "code": "BS_USER_EMAIL_LENGTH_ERR", "modifyTime": 20190313171939}, {"lanEn": "The mailbox format is incorrect!", "createTime": 20181023182057, "lanZhc": "邮箱格式不正确", "classCode": "04", "code": "BS_USER_EMAIL_FORMAT_ERR", "modifyTime": 20190313171939}, {"lanEn": "Contact must be a maximum of 50 characters in length.", "createTime": 20181023182057, "lanZhc": "联系方式不能超过50位", "classCode": "04", "code": "BS_USER_MOBILE_NO_LENGTH_ERR", "modifyTime": 20190313171939}, {"lanEn": "MSN Address must be a maximum of 50 characters in length.", "createTime": 20181023182057, "lanZhc": "即时通讯不能超过50位", "classCode": "04", "code": "BS_USER_MSN_ADDRESS_LENGTH_ERR", "modifyTime": 20190313171939}, {"lanEn": "The contact format is incorrect!", "createTime": 20181023182057, "lanZhc": "联系方式仅支持数字和“-”", "classCode": "04", "code": "BS_USER_MOBILE_NO_FORMAT_ERR", "modifyTime": 20190313171939}, {"lanEn": "Description must be a maximum of 200 characters in length.", "createTime": 20181023182057, "lanZhc": "描述不能超过200位", "classCode": "04", "code": "BS_USER_NOTE_LENGTH_ERR", "modifyTime": 20190313171939}, {"lanEn": "${field} cannot be empty", "createTime": 20180815125123, "lanZhc": "${field}不可为空", "classCode": "04", "code": "BS_FIELD_EMPTY_VAL", "modifyTime": 20181015094150}, {"lanEn": "Passwords must contain at least 8 characters and no more than 20 characters.", "createTime": 20181023182211, "lanZhc": "密码超出/低于8-20个字符", "classCode": "04", "code": "BS_USER_IMPORT_USER_PASSWORD_LENGTH_ERR", "modifyTime": 20181113142448}, {"lanEn": "Passwords must and only contain all of the following: uppercase letters, lowercase letters, numbers, and symbols.", "createTime": 20181023182249, "lanZhc": "密码必须且只能包含大小写字母，数字及特殊字符", "classCode": "04", "code": "BS_USER_IMPORT_USER_PASSWORD_FORMAT_ERR", "modifyTime": 20181113142443}, {"lanEn": "LoginCode [${loginCode}] is exist!", "createTime": 20181019101337, "lanZhc": "登录名[${loginCode}]已存在！", "classCode": "04", "code": "BS_USER_LOGIN_CODE_EXIST", "modifyTime": 20181019150101}, {"lanEn": "roleName [${roleName}] is exist!", "createTime": 20181019101337, "lanZhc": "角色[${roleName}]已存在！", "classCode": "04", "code": "BS_ROLE_NAME_EXIST", "modifyTime": 20181019150101}, {"lanEn": "parent org is not exist or unspecified parent org!", "createTime": 20181019101337, "lanZhc": "指定父级机构不存在或未指定", "classCode": "04", "code": "SAVE_ORG_PARNETID_NOEXIST", "modifyTime": 20181019150101}, {"lanEn": "don't use the same name in this org!", "createTime": 20181019101337, "lanZhc": "同级下机构不可重名", "classCode": "04", "code": "SAVE_ORG_NAME_REPEAT", "modifyTime": 20181019150101}, {"lanEn": "params don't null!", "createTime": 20181019101337, "lanZhc": "入参不可为空", "classCode": "04", "code": "PARAM_NOT_NULL", "modifyTime": 20181019150101}, {"lanEn": "param[${name}] don't null!", "createTime": 20181019101337, "lanZhc": "[${name}]不可为空", "classCode": "04", "code": "X_PARAM_NOT_NULL", "modifyTime": 20181019150101}, {"lanEn": "org don't null!", "createTime": 20181019101337, "lanZhc": "组织不可为空", "classCode": "04", "code": "ORG_NOT_NULL", "modifyTime": 20181019150101}, {"lanEn": "role don't null!", "createTime": 20181019101337, "lanZhc": "角色不可为空", "classCode": "04", "code": "ROLE_NOT_NULL", "modifyTime": 20181019150101}, {"lanEn": "user don't null!", "createTime": 20181019101337, "lanZhc": "用户不可为空", "classCode": "04", "code": "USER_NOT_NULL", "modifyTime": 20181019150101}, {"lanEn": "org name can't null!", "createTime": 20181019101337, "lanZhc": "组织名称不可为空", "classCode": "04", "code": "ORGNAME_NOT_NULL", "modifyTime": 20181019150101}, {"lanEn": "org orderNo can't null!", "createTime": 20181019101337, "lanZhc": "组织序号不可为空", "classCode": "04", "code": "ORGNO_NOT_NULL", "modifyTime": 20181019150101}, {"lanEn": "domainId can't be null!", "createTime": 20181019101337, "lanZhc": "域ID不能为空", "classCode": "01", "code": "BS_VISUALMODEL_LACK_DOMAINID", "modifyTime": 20181019150101}, {"lanEn": "Name of model can't be null!", "createTime": 20181019101337, "lanZhc": "模型名称不能为空", "classCode": "01", "code": "BS_VISUALMODEL_LACK_NAME", "modifyTime": 20181019150101}, {"lanEn": "Content of model can't be null!", "createTime": 20181019101337, "lanZhc": "模型内容不能为空", "classCode": "01", "code": "BS_VISUALMODEL_LACK_JSON", "modifyTime": 20181019150101}, {"lanEn": "Model is wrong!", "createTime": 20181019101337, "lanZhc": "模型内容错误", "classCode": "01", "code": "BS_VISUALMODEL_WRONG_JSON", "modifyTime": 20181019150101}, {"lanEn": "Id of model can't be null!", "createTime": 20181019101337, "lanZhc": "模型ID不能为空", "classCode": "01", "code": "BS_VISUALMODEL_LACK_ID", "modifyTime": 20181019150101}, {"lanEn": "Model is not exist!", "createTime": 20181019101337, "lanZhc": "模型不存在", "classCode": "01", "code": "BS_VISUALMODEL_NO_EXIST", "modifyTime": 20181019150101}, {"lanEn": "Model is using, and can't be deleted!", "createTime": 20181019101337, "lanZhc": "模型正在使用中, 不能被删除", "classCode": "01", "code": "BS_VISUALMODEL_CANT_BE_DELETE", "modifyTime": 20181019150101}, {"lanEn": "Impact Model is wrong!", "createTime": 20181019101337, "lanZhc": "影响模型错误", "classCode": "01", "code": "BS_IMPACTPATH_WRONG", "modifyTime": 20181019150101}, {"lanEn": "Rule name repetition!", "createTime": 20181019101337, "lanZhc": "规则重名", "classCode": "01", "code": "BS_RLT_RULE_NAME_REPETITION", "modifyTime": 20181019150101}, {"lanEn": "Rule lack!", "createTime": 20181019101337, "lanZhc": "规则残缺", "classCode": "01", "code": "BS_RLT_RULE_LACK", "modifyTime": 20181019150101}, {"lanEn": "Rule no exist!", "createTime": 20181019101337, "lanZhc": "规则不存在", "classCode": "01", "code": "BS_RLT_RULE_NO_EXIST", "modifyTime": 20181019150101}, {"lanEn": "Entry ci is not a rule starting classification!", "createTime": 20181019101337, "lanZhc": "入口ci不是规则起始分类", "classCode": "01", "code": "BS_RLT_RULE_START_CI_ILLEGALITY", "modifyTime": 20181019150101}, {"lanEn": "CI no exist!", "createTime": 20181019101337, "lanZhc": "CI不存在", "classCode": "01", "code": "BS_CI_NO_EXIST", "modifyTime": 20181019150101}, {"lanEn": "Category already exists", "createTime": 20180801155415, "lanZhc": "该分类已存在", "classCode": "04", "code": "BS_CC_CLASS_EXIST", "modifyTime": 20181026155514}, {"lanEn": "Duplicate attribute name[${proName}]", "createTime": 20180801161137, "lanZhc": "属性名称【${proName}】重复", "classCode": "04", "code": "BS_CC_CLASS_DEF_EXIST", "modifyTime": 20181015092727}, {"lanEn": "Please delete the data first!", "createTime": 20180801161137, "lanZhc": "该分类下有数据，请先删除数据！", "classCode": "04", "code": "BS_CC_CLASS_HAS_DATA", "modifyTime": 20181015092727}, {"lanEn": "Class does not exist!", "createTime": 20180907105801, "lanZhc": "分类不存在！", "classCode": "04", "code": "BS_MNAME_CLASS_NOT_EXSIT", "modifyTime": 20181026170354}, {"lanEn": "Unable to operate unsupported file types!", "createTime": 20180802152106, "lanZhc": "无法操作不支持的文件类型！", "classCode": "04", "code": "BS_MNAME_NOT_SUPPORT_FILETYPE", "modifyTime": 20181015093958}, {"lanEn": "CI already exists", "createTime": 20170406142342, "lanZhc": "该对象已存在", "classCode": "04", "code": "BS_MNAME_RECORD_CONTAINS", "modifyTime": 20190510101113}, {"lanEn": "Classification [${field}] data exception!", "createTime": 20180725174659, "lanZhc": "分类【${field}】数据异常！", "classCode": "04", "code": "BS_MNAME_CLASS_DATA_ERROR", "modifyTime": 20181026170354}, {"lanEn": "CI attribute definition does not exist!", "createTime": 20180910160738, "lanZhc": "CI属性定义不存在！", "classCode": "04", "code": "BS_MNAME_NOT_CI_ATTR_DEF", "modifyTime": 20181015093958}, {"lanEn": "The user is not logged in or the login is invalid", "createTime": 20180801165422, "lanZhc": "用户未登陆或登陆已失效", "classCode": "04", "code": "USER_DONOT_LOGIN", "modifyTime": 20181026155514}, {"lanEn": "Lack of required entries!", "createTime": 20170922112430, "lanZhc": "必填属性缺失", "classCode": "04", "code": "BS_REQUIRED_FIELD_MISS", "modifyTime": 20181019180012}, {"lanEn": "Parent class is not exist!", "createTime": 20181019101337, "lanZhc": "指定父级分类不存在", "classCode": "04", "code": "BS_PARNET_CLASS_NOT_EXIST", "modifyTime": 20181019150101}, {"lanEn": "[${field}] content exceeds length limit!", "createTime": 20181019101337, "lanZhc": "[${field}]内容超长", "classCode": "04", "code": "BS_OVER_LENGTH", "modifyTime": 20181019150101}, {"lanEn": "[${field}] format error!", "createTime": 20181019101337, "lanZhc": "${field}格式不正确", "classCode": "04", "code": "BS_FORMAT_ERROR", "modifyTime": 20181019150101}, {"lanEn": "Doesn't conform to the [${filed}] template format!", "createTime": 20181019101337, "lanZhc": "不符合模板格式", "classCode": "04", "code": "BS_TEMPLATE_FORMAT_ERROR", "modifyTime": 20181019150101}, {"lanEn": "Please delete subcategory [${field}] first!", "createTime": 20181019101337, "lanZhc": "请先删除子分类【${field}】", "classCode": "04", "code": "BS_CLASS_DELETE_EXIST_SUB_CLASS", "modifyTime": 20181019150101}, {"lanEn": "image", "createTime": 20170328143337, "lanZhc": "图标", "classCode": "02", "code": "BS_MNAME_IMAGE", "modifyTime": 20181019143710}, {"lanEn": "Prohibit deleting 3D folders!", "createTime": 20170328143337, "lanZhc": "3D文件夹禁止删除", "classCode": "02", "code": "BS_MNAME_IMAGE_DELETE_3D_NOT_ALLOW", "modifyTime": 20181019143710}, {"lanEn": "Folder names only support brackets (), dots., underscores, hyphens -, aiters @, spaces!", "createTime": 20170328143337, "lanZhc": "文件夹名称仅允许输入中文、字母、数字空格以及()._-@符号", "classCode": "02", "code": "BS_MNAME_IMAGE_DIR_NAME_FORMAT", "modifyTime": 20181019143710}, {"lanEn": "File size cannot exceed 256M!", "createTime": 20170328143337, "lanZhc": "文件大小不得超过256M", "classCode": "02", "code": "BS_MNAME_FILE_OVERLENGTH", "modifyTime": 20181019143710}, {"lanEn": "Only ZIP files are supported!", "createTime": 20170328143337, "lanZhc": "仅支持ZIP文件", "classCode": "02", "code": "BS_MDOMAIN_ONLY_ZIP", "modifyTime": 20181019143710}, {"lanEn": "Authorization code error", "createTime": 20181121135915, "lanZhc": "授权码不可用", "classCode": "04", "code": "BS_MNAME_AUTHCODE_ERROR", "modifyTime": 20181121135948}, {"lanEn": "Class Definition", "createTime": 20181121135915, "lanZhc": "填写须知", "classCode": "04", "code": "BS_MNAME_IMPORT_NOTICE", "modifyTime": 20181121135948}, {"lanEn": "Class Definition", "createTime": 20181121135915, "lanZhc": "对象定义", "classCode": "04", "code": "BS_CI_CLASS_EXPORT_CLASS_DEFINITION", "modifyTime": 20181121135948}, {"lanEn": "Dir Name", "createTime": 20181121135915, "lanZhc": "领域名称", "classCode": "04", "code": "BS_CI_CLASS_EXPORT_DIR_NAME", "modifyTime": 20181121135948}, {"lanEn": "Class Name", "createTime": 20181121135915, "lanZhc": "分类名称", "classCode": "04", "code": "BS_CI_CLASS_EXPORT_CLASS_NAME", "modifyTime": 20181121135948}, {"lanEn": "Parent Class Name", "createTime": 20181121135915, "lanZhc": "父类名称", "classCode": "04", "code": "BS_CI_CLASS_EXPORT_PARENT_CLASS_NAME", "modifyTime": 20181121135948}, {"lanEn": "Image Name", "createTime": 20181121135915, "lanZhc": "图标名称", "classCode": "04", "code": "BS_CI_CLASS_EXPORT_CLASS_IMAGE_NAME", "modifyTime": 20181121135948}, {"lanEn": "Attribution Name", "createTime": 20181121135915, "lanZhc": "属性名称", "classCode": "04", "code": "BS_CI_CLASS_EXPORT_ATTR_NAME", "modifyTime": 20181121135948}, {"lanEn": "Attribution Type", "createTime": 20181121135915, "lanZhc": "属性类型", "classCode": "04", "code": "BS_CI_CLASS_EXPORT_ATTR_TYPE", "modifyTime": 20181121135948}, {"lanEn": "Is Required", "createTime": 20181121135915, "lanZhc": "是否必填", "classCode": "04", "code": "BS_CI_CLASS_EXPORT_IS_REQUIRED", "modifyTime": 20181121135948}, {"lanEn": "Is Label", "createTime": 20181121135915, "lanZhc": "是否Label", "classCode": "04", "code": "BS_CI_CLASS_EXPORT_IS_LABEL", "modifyTime": 20181121135948}, {"lanEn": "Is Major", "createTime": 20181121135915, "lanZhc": "是否主键", "classCode": "04", "code": "BS_CI_CLASS_EXPORT_IS_MAJOR", "modifyTime": 20181121135948}, {"lanEn": "Default Value", "createTime": 20181121135915, "lanZhc": "默认值", "classCode": "04", "code": "BS_CI_CLASS_EXPORT_ATTR_DEFVALUE", "modifyTime": 20181121135948}, {"lanEn": "Attribution Type Rule", "createTime": 20181121135915, "lanZhc": "类型约束", "classCode": "04", "code": "BS_CI_CLASS_EXPORT_ATTR_TYPE_RULE", "modifyTime": 20181121135948}, {"lanEn": "Attribution Tag", "createTime": 20181121135915, "lanZhc": "属性tag", "classCode": "04", "code": "BS_CI_CLASS_EXPORT_ATTR_TAG", "modifyTime": 20181121135948}, {"lanEn": "DOMAIN", "createTime": 20180801165422, "lanZhc": "领域", "classCode": "04", "code": "BS_DIR_CLASS_DOMAIN", "modifyTime": 20181026155514}, {"lanEn": "Folder", "createTime": 20180801165422, "lanZhc": "文件夹", "classCode": "04", "code": "BS_DIR_FOLDER", "modifyTime": 20181026155514}, {"lanEn": "Group", "createTime": 20180801165422, "lanZhc": "分组", "classCode": "04", "code": "BS_DIR_GROUP", "modifyTime": 20181026155514}, {"lanEn": "Invalid relationship: node's relationship deleted in Visual modeling.", "createTime": 20180801165422, "lanZhc": "关系规则已残缺：节点关系在元模型中被删除！", "classCode": "05", "code": "CMV_MNAME_RLTRULE_CLASSRLTDELETED", "modifyTime": 20181026155514}, {"lanEn": "Relation not exists", "createTime": 20180801165422, "lanZhc": "关系不存在", "classCode": "05", "code": "CMV_RLT_NOT_EXISTS", "modifyTime": 20181026155514}, {"lanEn": "The rule illegal", "createTime": 20180801165422, "lanZhc": "规则非法", "classCode": "05", "code": "CMV_RULE_ILLEGAL", "modifyTime": 20181026155514}, {"lanEn": "Userna<PERSON> error", "createTime": 20180801165422, "lanZhc": "用户名有误", "classCode": "05", "code": "DCV_USERNAME_ERROR", "modifyTime": 20181026155514}, {"lanEn": "Password error", "createTime": 20180801165422, "lanZhc": "密码有误", "classCode": "05", "code": "DCV_PASSWORD_ERROR", "modifyTime": 20181026155514}, {"lanEn": "The data set does not exist", "createTime": 20180801165422, "lanZhc": "数据集不存在", "classCode": "05", "code": "DCV_BS_OBJ_DATASET_NOT_EXIST", "modifyTime": 20181026155514}, {"lanEn": "The user has no permission to collaborate", "createTime": 20180801165422, "lanZhc": "数据集已停用", "classCode": "05", "code": "DCV_BS_OBJ_DATASET_NO_PERMISSION", "modifyTime": 20181026155514}, {"lanEn": "The data set without node", "createTime": 20180801165422, "lanZhc": "数据集未配置节点", "classCode": "05", "code": "DCV_BS_OBJ_DATASET_WITHOUT_NODE", "modifyTime": 20181026155514}, {"lanEn": "The rule name is existed", "createTime": 20180801165422, "lanZhc": "规则名称已经存在", "classCode": "05", "code": "DCV_BS_OBJ_RULE_EXISTS", "modifyTime": 20181026155514}, {"lanEn": "Relational data does not exist!", "createTime": 20180801165422, "lanZhc": "规则数据不存在", "classCode": "05", "code": "BS_RULE_DATA_NOTEXIST", "modifyTime": 20181026155514}, {"lanEn": "The configuration data set ${field}", "createTime": 20180801165422, "lanZhc": "配置数据集${field}", "classCode": "05", "code": "UINO_BS_OBJ_RELATION_RULE_DATASET", "modifyTime": 20181026155514}, {"lanEn": "model", "createTime": 20180801165422, "lanZhc": "模型", "classCode": "05", "code": "UINO_BS_3D_IMAGE_MODEL", "modifyTime": 20181026155514}, {"lanEn": "modelType", "createTime": 20180801165422, "lanZhc": "型号", "classCode": "05", "code": "UINO_BS_3D_IMAGE_MODEL_TYPE", "modifyTime": 20181026155514}, {"lanEn": "resource does not exist", "createTime": 20180801165422, "lanZhc": "资源不存在", "classCode": "02", "code": "RESOURCE_NOT_EXIST", "modifyTime": 20181026155514}, {"lanEn": "The number of users has exceeded the maximum", "createTime": 20180801165422, "lanZhc": "用户数已超出最大数量", "classCode": "05", "code": "USER_COUNT_BEYOND", "modifyTime": 20181026155514}]