package com.uino.api.client.cmdb;

import com.alibaba.fastjson.JSONObject;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uino.bean.cmdb.base.dataset.DataSetMallApiRelationRule;
import com.uino.bean.cmdb.base.dataset.batch.SimpleFriendInfo;
import com.uino.bean.cmdb.base.dataset.chart.Chart;
import com.uino.bean.cmdb.business.dataset.FriendInfo;
import com.uino.bean.cmdb.business.dataset.QueryCondition;
import com.uino.bean.cmdb.business.dataset.RltRuleTableData;

import java.util.List;
import java.util.Map;

/**
 * 关系遍历规则查数据
 *
 * <AUTHOR>
 * @data 2019/8/7 9:55.
 */
public interface IRelationRuleAnalysisApiSvc {

    /**
     * find traval tree by relation rule
     *
     * @param relationRule relationRule
     * @return map
     */
    Map<Long, List<QueryCondition>> findTravalTree(DataSetMallApiRelationRule relationRule);

    /**
     * query ci friend by ci id
     *
     * @param sCi       ci info
     * @param dataSetId data set id
     * @return friend info
     */
    FriendInfo queryCiFriendByCiId(CcCiInfo sCi, Long dataSetId);

    /**
     * query ci friend by ci id and rule
     *
     * @param sCi          ci info
     * @param relationRule rule
     * @return friend info
     */
    FriendInfo queryCiFriendByCiIdAndRule(CcCiInfo sCi, DataSetMallApiRelationRule relationRule);

    /**
     * query ci friend by ci ids
     *
     * @param sCis                ci info
     * @param dataSetId           data set id
     * @param isIncludeAllStartCI Whether to include all CI
     * @return map
     */
    Map<Long, FriendInfo> queryCiFriendByCiIds(List<CcCiInfo> sCis, Long dataSetId, boolean isIncludeAllStartCI);

    /**
     * 指定起始CI查询朋友圈数据
     *
     * @param sCis                起始CIs
     * @param relationRule        关系遍历规则
     * @param isIncludeAllStartCI 是否过滤scis中不符合规则要求的
     * @return
     */
    Map<Long, FriendInfo> queryCiFriendByCiIdsAndRule(List<CcCiInfo> sCis, DataSetMallApiRelationRule relationRule, boolean isIncludeAllStartCI);

    /**
     * 根据规则查询所有数据,区分入口返回
     *
     * @param relationRule 规则
     * @return 规则结果
     */
    Map<Long, FriendInfo> queryCiFriendByRule(DataSetMallApiRelationRule relationRule);

    /**
     * query ci friend by rule with limit
     *
     * @param relationRule rule
     * @param limit limit
     * @return map
     * */
    Map<Long, FriendInfo> queryCiFriendByRuleWithLimit(DataSetMallApiRelationRule relationRule, Integer limit);


    /**
     * 合并数据
     *
     * @param friendInfoMap
     * @return
     */
    FriendInfo mergeFriendInfoMap(Map<Long, FriendInfo> friendInfoMap);


    /**
     * 拼接数据结果
     *
     * @param ciIds      入口ciId
     * @param friendInfo
     * @return
     */
    RltRuleTableData disassembleFriendInfoDataByPath(DataSetMallApiRelationRule relationRule, List<Long> ciIds, FriendInfo friendInfo);

    /**
     * 根据入口ci查询并拼接数据结果
     *
     * @param sCis 入口ciId
     * @param
     * @return
     */
    RltRuleTableData disassembleFriendInfoDataByPath(List<CcCiInfo> sCis, DataSetMallApiRelationRule relationRule);

    /**
     * 统计
     *
     * @return
     */
    JSONObject countStatistics(DataSetMallApiRelationRule dataSetMallRelationApi, Map<Long, SimpleFriendInfo> simpleFriendInfoMap, Chart chart);
}
