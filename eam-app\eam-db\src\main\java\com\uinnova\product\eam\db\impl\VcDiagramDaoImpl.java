package com.uinnova.product.eam.db.impl;

import com.binary.core.lang.Conver;
import com.binary.core.util.BinaryUtils;
import com.binary.jdbc.Page;
import com.uinnova.product.eam.comm.bean.SceneDiagram;
import com.uinnova.product.eam.comm.model.CVcDiagram;
import com.uinnova.product.eam.comm.model.VcDiagram;
import com.uinnova.product.eam.db.VcDiagramDao;
import com.uinnova.product.eam.db.bean.DiagramDirCount;
import com.uinnova.product.eam.db.bean.DiagramInfoCount;
import com.uinnova.product.eam.db.bean.DiagramTagGroup;
import com.uinnova.product.eam.db.bean.DiagramUserGroup;
import com.uinnova.product.eam.db.bean.GroupDiagramInfoCount;
import com.uinnova.product.eam.db.bean.UserDiagramInfoCount;
import com.uinnova.product.eam.db.support.dao.mybatis.ComMyBatisBinaryDaoImpl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 视图设计表[VC_DIAGRAM]数据访问对象实现
 */
public class VcDiagramDaoImpl extends ComMyBatisBinaryDaoImpl<VcDiagram, CVcDiagram> implements VcDiagramDao {

//	@Override
//	public String getTableName() {
//		return "IAMS." + getDaoDefinition().getTableName();
//	}

	@Override
	public Page<VcDiagram> selectOpenPageExt(long pageNum, long pageSize, String[] likes, Long[] userIds,
			Long[] groupIds, Long[] tagIds, CVcDiagram cdt, String orders) {
		if (cdt == null)
			cdt = new CVcDiagram();
		setDataStatusValue(cdt, 1);
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("cdt", cdt);
		fillCondition(cdt, map);
		map.put("orders", orders);

		if (!BinaryUtils.isEmpty(likes)) {
			StringBuilder sb = new StringBuilder();
			for (int i = 0; i < likes.length; i++) {
				if (i > 0) {
					sb.append(" and ");
				}
				sb.append(" SEARCH_FIELD like '").append(likes[i]).append("' ");
			}
			map.put("likes", sb.toString());
		}
		if (!BinaryUtils.isEmpty(userIds))
			map.put("userIds", Conver.toString(userIds));
		if (!BinaryUtils.isEmpty(groupIds))
			map.put("groupIds", Conver.toString(groupIds));
		if (!BinaryUtils.isEmpty(tagIds))
			map.put("tagIds", Conver.toString(tagIds));

		Page<VcDiagram> page = selectByPage(getTableName() + ".selectOpenListExt", map, pageNum, pageSize);
		return page;
	}

	@Override
	public Page<VcDiagram> selectWorkabilityPage(long pageNum, long pageSize, Long userId, CVcDiagram cdt,
			String orders) {
		if (cdt == null)
			cdt = new CVcDiagram();
		setDataStatusValue(cdt, 1);
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("cdt", cdt);
		fillCondition(cdt, map);
		map.put("userId", userId);
		Page<VcDiagram> page = selectByPage(getTableName() + ".selectWorkabilityPage", map, pageNum, pageSize);
		return page;
	}

	@Override
	public Integer selectEditAuthByDiagramId(Long domainId, Long diagramId, Long userId) {

		Map<String, Object> map = new HashMap<String, Object>();
		map.put("userId", userId);
		map.put("id", diagramId);
		map.put("domainId", domainId);
		Long a = (Long) getSqlSession().selectOne(getTableName() + ".selectDiagramEditAuth", map);
		// Long a =
		// (Long)getSqlMapClientTemplate().queryForObject(getTableName()+".selectDiagramEditAuth",
		// map);
		return a > 0 ? 1 : 0;
	}

	@Override
	public List<VcDiagram> selectListByCiIdsExt(Long[] ciIds, CVcDiagram cdt, String orders) {
//		MessageUtil.checkEmpty(ciIds, "ciIds");
		StringBuffer sb = new StringBuffer(1000);
		for (int i = 0; i < ciIds.length; i++) {
			sb.append("'").append(ciIds[i]).append("',");
		}
		sb.delete(sb.length() - 1, sb.length());

		if (cdt == null)
			cdt = new CVcDiagram();
		setDataStatusValue(cdt, 1);
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("cdt", cdt);
		fillCondition(cdt, map);

		map.put("ciIds", sb.toString());
		map.put("orders", orders);
		List<VcDiagram> queryForList = getSqlSession().selectList(getTableName() + ".selectListByCiIdsExt", map);
		return queryForList;
	}

	@Override
	public List<DiagramTagGroup> selectOpenDiagramCountByTag(Long domainId, String like,Long[] userIds) {
		Map<String, Object> map = new HashMap<String, Object>();
		if(domainId !=null ) map.put("domainId", domainId);
		if(!BinaryUtils.isEmpty(like)) map.put("tagName", like);
		map.put("userIds", array2SqlStr(userIds));
		List<DiagramTagGroup> res = getSqlSession().selectList(getTableName()+".selectOpenDiagramCountByTag", map);
		return res;
	}

	@Override
	public List<DiagramUserGroup> selectOpenDiagramCountByUser(Long domainId, String like, Long[] userIds) {
		Map<String, Object> map = new HashMap<String, Object>();
		if (domainId != null)
			map.put("domainId", domainId);
		if (!BinaryUtils.isEmpty(like))
			map.put("userName", like);
		map.put("userIds", array2SqlStr(userIds));
		List<DiagramUserGroup> res = getSqlSession().selectList(getTableName() + ".selectOpenDiagramCountByUser", map);

		return res;
	}

	private String array2SqlStr(Long[] ids) {
		if (BinaryUtils.isEmpty(ids))
			return "";
		StringBuffer idsSql = new StringBuffer(1000);
		for (int i = 0; i < ids.length; i++) {
			if (i == 0) {
				idsSql.append(ids[i]);
			} else {
				idsSql.append(",").append(ids[i]);
			}
		}
		return idsSql.toString();
	}

	private String array2SqlStr(Integer[] ids) {
		if (BinaryUtils.isEmpty(ids))
			return "";
		StringBuffer idsSql = new StringBuffer(1000);
		for (int i = 0; i < ids.length; i++) {
			if (i == 0) {
				idsSql.append(ids[i]);
			} else {
				idsSql.append(",").append(ids[i]);
			}
		}
		return idsSql.toString();
	}

	@Override
	public Page<VcDiagram> selectOpenDiagramPageByCiIndexExt(long pageNum, long pageSize, String[] likes,
			Long[] userIds, Long[] groupIds, Long[] tagIds, CVcDiagram cdt, String ciIdxExp, Long[] ciTagIds,
			String orders) {

		if (cdt == null)
			cdt = new CVcDiagram();
		setDataStatusValue(cdt, 1);
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("cdt", cdt);
		fillCondition(cdt, map);
		map.put("orders", orders);

		if (!BinaryUtils.isEmpty(likes)) {
			StringBuilder sb = new StringBuilder();
			for (int i = 0; i < likes.length; i++) {
				if (i > 0) {
					sb.append(" and ");
				}
				sb.append(" SEARCH_FIELD like '").append(likes[i]).append("' ");
			}
			map.put("likes", sb.toString());
		}
		if (!BinaryUtils.isEmpty(userIds))
			map.put("userIds", Conver.toString(userIds));
		if (!BinaryUtils.isEmpty(groupIds))
			map.put("groupIds", Conver.toString(groupIds));
		if (!BinaryUtils.isEmpty(tagIds))
			map.put("tagIds", Conver.toString(tagIds));

		if (!BinaryUtils.isEmpty(ciIdxExp) || !BinaryUtils.isEmpty(ciTagIds)) {
			map.put("ciTagOrCiLike", true);

			if (!BinaryUtils.isEmpty(ciIdxExp))
				map.put("ciExps", ciIdxExp);
			if (!BinaryUtils.isEmpty(ciTagIds))
				map.put("ciTagIds", ciTagIds);
		}

		Page<VcDiagram> page = selectByPage(getTableName() + ".selectOpenListExt", map, pageNum, pageSize,
				VcDiagram.class);
		return page;
	}

	@Override
	public List<SceneDiagram> selectSceneDiagramListByDids(Long domainId, Long[] diagramIds, Integer diagramType) {
		BinaryUtils.checkEmpty(domainId, "domainId");
		BinaryUtils.checkEmpty(diagramIds, "ids");
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("domainId", domainId);

		StringBuffer idsSql = new StringBuffer(1000);
		for (int i = 0; i < diagramIds.length; i++) {
			if (i == 0) {
				idsSql.append(diagramIds[i]);
			} else {
				idsSql.append(",").append(diagramIds[i]);
			}
		}
		map.put("idsSql", idsSql.toString());

		if (BinaryUtils.isEmpty(diagramType))
			map.put("diagramType", diagramType);
		List<SceneDiagram> res = getSqlSession().selectList(getTableName() + ".selectSceneDiagramListByDids", map);

		return res;
	}

	@Override
	public Integer deleteSceneDiagramBySceneIds(Long domainId, Long[] sceneIds) {
		BinaryUtils.checkEmpty(domainId, "domainId");
		BinaryUtils.checkEmpty(sceneIds, "ids");

		Map<String, Object> map = new HashMap<String, Object>();
		map.put("domainId", domainId);

		StringBuffer idsSql = new StringBuffer(1000);
		for (int i = 0; i < sceneIds.length; i++) {
			if (i == 0) {
				idsSql.append(sceneIds[i]);
			} else {
				idsSql.append(",").append(sceneIds[i]);
			}
		}
		map.put("idsSql", idsSql.toString());
		return getSqlSession().delete(getTableName() + ".deleteSceneDiagramBySceneIds", map);
	}

	@Override
	public Integer deleteSceneBySceneIds(Long domainId, Long[] sceneIds) {
		BinaryUtils.checkEmpty(domainId, "domainId");
		BinaryUtils.checkEmpty(sceneIds, "ids");

		Map<String, Object> map = new HashMap<String, Object>();
		map.put("domainId", domainId);

		StringBuffer idsSql = new StringBuffer(1000);
		for (int i = 0; i < sceneIds.length; i++) {
			if (i == 0) {
				idsSql.append(sceneIds[i]);
			} else {
				idsSql.append(",").append(sceneIds[i]);
			}
		}
		map.put("idsSql", idsSql.toString());
		return getSqlSession().delete(getTableName() + ".deleteSceneBySceneIds", map);

	}

	@Override
	public Integer updateDiagramReadCountById(Long domainId, Long id) {
		BinaryUtils.checkEmpty(domainId, "domainId");
		BinaryUtils.checkEmpty(id, "id");
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("domainId", domainId);
		map.put("id", id);
		return getSqlSession().update(getTableName() + ".updateReadCountById", map);
	}

	public Integer updateAppRltCiCodeNullByRltCiCode(Long domainId, String appRltCiCode) {
		BinaryUtils.checkEmpty(domainId, "domainId");
		BinaryUtils.checkEmpty(appRltCiCode, "appRltCiCode");
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("domainId", domainId);
		map.put("appRltCiCode", appRltCiCode);
		return getSqlSession().update(getTableName() + ".updateAppRltCiCodeNullByRltCiCode", map);
	}

	@Override
	public List<DiagramInfoCount> countDiagramInfo(Long domainId, String orders, Long startDate, Long endDate) {
		BinaryUtils.checkEmpty(domainId, "domainId");
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("domainId", domainId);
		map.put("orders", orders);
		map.put("startDate", startDate);
		map.put("endDate", endDate);
		return getSqlSession().selectList(getTableName() + ".countDiagramInfo", map);

	}

	public List<DiagramInfoCount> countDiagramInfoRead(Long domainId, Long startDate, Long endDate) {
		BinaryUtils.checkEmpty(domainId, "domainId");
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("domainId", domainId);
		map.put("startDate", startDate);
		map.put("endDate", endDate);
		return getSqlSession().selectList(getTableName() + ".countDiagramInfoReadCount", map);
	}

	public List<DiagramInfoCount> countDiagramInfoEnsh(Long domainId, Long startDate, Long endDate) {
		BinaryUtils.checkEmpty(domainId, "domainId");
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("domainId", domainId);
		map.put("startDate", startDate);
		map.put("endDate", endDate);
		return getSqlSession().selectList(getTableName() + ".countDiagramInfoEnshCount", map);
	}

	@Override
	public List<UserDiagramInfoCount> countOpenDiagramGroupByUser(Long domainId, Long startDate, Long endDate) {
		BinaryUtils.checkEmpty(domainId, "domainId");
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("domainId", domainId);
        map.put("startDate", startDate);
        map.put("endDate", endDate);

		return getSqlSession().selectList(getTableName() + ".countOpenDiagramGroupByUser", map);
	}

	@Override
	public List<GroupDiagramInfoCount> countGroupDiagramInfo(Long domainId, String orders,
															 Long startDate, Long endDate) {
		BinaryUtils.checkEmpty(domainId, "domainId");
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("domainId", domainId);
		map.put("orders", orders);
		map.put("startDate", startDate);
		map.put("endDate", endDate);
		return getSqlSession().selectList(getTableName() + ".countGroupDiagramInfo", map);
	}

	@Override
	public List<UserDiagramInfoCount> countUserDiagramInfo(Long domainId, String orders, Long startDate, Long endDate) {
		BinaryUtils.checkEmpty(domainId, "domainId");
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("domainId", domainId);
		map.put("orders", orders);
        map.put("startDate", startDate);
        map.put("endDate", endDate);
		return getSqlSession().selectList(getTableName() + ".countUserDiagramInfo", map);

	}

	@Override
	public Page<VcDiagram> selectPageByCdtAndNotInIds(long pageNum, long pageSize, Long[] notInIds, CVcDiagram cdt,
			String orders) {
		if (cdt == null)
			cdt = new CVcDiagram();
		setDataStatusValue(cdt, 1);
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("cdt", cdt);
		fillCondition(cdt, map);
		map.put("orders", orders);

		if (!BinaryUtils.isEmpty(notInIds)) {
			StringBuffer idsSql = new StringBuffer(1000);
			for (int i = 0; i < notInIds.length; i++) {
				if (i == 0) {
					idsSql.append(notInIds[i]);
				} else {
					idsSql.append(",").append(notInIds[i]);
				}
			}
			map.put("notInIds", idsSql.toString());
		}
		return selectByPage(getTableName() + ".selectListByCdtAndNotInIds", map, pageNum, pageSize, VcDiagram.class);
		// Page<VcDiagram> page =
		// IBatisUtils.selectPage(getSqlMapClientTemplate(),
		// getTableName()+".selectListByCdtAndNotInIds", map, pageNum, pageSize,
		// true);
		// return page;
	}

	@Override
	public List<DiagramDirCount> selectDirDiagramCountList(Long domainId, Long[] dirIds, Long userId) {
		BinaryUtils.checkEmpty(domainId, "domainId");
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("domainId", domainId);

		StringBuffer idsSql = new StringBuffer(1000);
		for (int i = 0; i < dirIds.length; i++) {
			if (i == 0) {
				idsSql.append(dirIds[i]);
			} else {
				idsSql.append(",").append(dirIds[i]);
			}
		}
		map.put("dirIds", idsSql.toString());

		map.put("userId", userId);
		return getSqlSession().selectList(getTableName() + ".selectDirDiagramCountList", map);
	}

	@Override
	public Long countAllDiagramCount(Long domainId) {
		Map<String, Object> sqlMap = new HashMap<>();
		sqlMap.put("domainId", domainId);
		Long countNumber = getSqlSession().selectOne(getTableName() + ".countAllDiagramCount", sqlMap);
		return countNumber;
	}

}
