package com.uinnova.product.vmdb.comm.exception;

import com.binary.core.exception.BinaryException;

/**
 * 
 * <AUTHOR>
 *
 */
public class ExpressionException extends BinaryException {
    private static final long serialVersionUID = 1L;

    public ExpressionException() {
        super();
    }

    public ExpressionException(String message) {
        super(message);
    }

    public ExpressionException(Throwable cause) {
        super(cause);
    }

    public ExpressionException(String message, Throwable cause) {
        super(message, cause);
    }

}
