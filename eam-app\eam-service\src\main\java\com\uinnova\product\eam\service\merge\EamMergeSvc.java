package com.uinnova.product.eam.service.merge;

import com.uino.bean.cmdb.base.LibType;

import java.util.List;

/**
 * 企业架构资产发布/检出接口
 * <AUTHOR>
 */
public interface EamMergeSvc {
    /**
     * 模型发布
     * @param dirIds 目录id
     * @param diagramId 视图id
     * @param desc 备注
     */
    void modelPush(List<Long> dirIds, String diagramId, String desc, Long parentId);

    /**
     * 模型检出
     * @param dirId 目录id
     * @param diagramId 视图id
     * @param targetDirId 检出目标端id
     * @param libType 检出位置PRIVATE=架构设计/DESIGN=架构资产
     */
    void modelPull(Long dirId, String diagramId, Long targetDirId, LibType libType);

    /**
     * 矩阵表格发布
     * @param id 矩阵表格id
     * @param dirId 发布位置文件夹id
     * @param description 说明
     */
    void matrixPush(Long id, Long dirId, String description);

    /**
     * 矩阵表格检出
     * @param id 矩阵表格id
     * @param dirId 私有库文件夹id
     */
    void matrixPull(Long id, Long dirId);
}
