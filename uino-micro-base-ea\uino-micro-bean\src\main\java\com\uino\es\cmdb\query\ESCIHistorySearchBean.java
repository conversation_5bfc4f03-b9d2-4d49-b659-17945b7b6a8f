package com.uino.es.cmdb.query;

import com.uino.bean.cmdb.query.ESSearchBase;
import lombok.*;

import java.util.Set;

/**
 * CI历史查询类
 * 
 * <AUTHOR>
 *
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ESCIHistorySearchBean extends ESSearchBase {
    /**
     * 
     */
    private static final long serialVersionUID = 1L;

    private Long ciId;

    private Long classId;

    private String ciCode;

    private Long version;

    private Set<Integer> actions;

    /**
     * EA产品字段----视图id
     */
    private String diagramId;
}
