package com.uinnova.product.eam.service.cj.service;

import com.alibaba.fastjson.JSONObject;
import com.binary.jdbc.Page;
import com.uinnova.product.eam.model.bm.DataSetEnum;
import com.uinnova.product.eam.model.cj.dto.PlanDesignInstanceDTO;
import com.uinnova.product.eam.model.cj.dto.PlanDesignShareRecordDTO;
import com.uinnova.product.eam.model.cj.dto.PlanVersionDTO;
import com.uinnova.product.eam.model.cj.domain.PlanDesignInstance;
import com.uinnova.product.eam.model.cj.domain.PlanDesignShareRecord;
import com.uinnova.product.eam.model.cj.domain.TemplateType;
import com.uinnova.product.eam.model.cj.request.PlanDesignInstanceAddRequest;
import com.uinnova.product.eam.model.cj.request.PlanDesignInstanceQueryRequest;
import com.uinnova.product.eam.model.cj.request.PlanDesignInstanceUpdateRequest;
import com.uinnova.product.eam.model.cj.request.ProcessApprovalRequest;
import com.uinnova.product.eam.model.cj.vo.DetectionPlanVo;
import com.uinnova.product.eam.model.cj.vo.PlanDesignInstanceVO;
import com.uinnova.product.eam.model.cj.vo.PlanHistoryVersionVo;
import com.uinnova.product.eam.model.cj.vo.PlanInfoVO;
import com.uinnova.product.eam.model.vo.CiSimpleInfoVo;
import com.uinnova.product.eam.model.vo.MinePlanQueryVo;
import com.uino.bean.cmdb.base.LibType;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.permission.business.UserInfo;
import org.springframework.http.ResponseEntity;

import jakarta.servlet.http.HttpServletResponse;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 方案设计实例service接口
 * <AUTHOR>
 */
public interface PlanDesignInstanceService {

    /**
     * 方案设计新增
     * @param request {@link PlanDesignInstanceAddRequest}
     * @return id
     */
    Long add(PlanDesignInstanceAddRequest request);

    /**
     * 方案设计更新
     * @param request {@link PlanDesignInstanceUpdateRequest}
     */
    Long update(PlanDesignInstanceUpdateRequest request);

    /**
     * 方案设计分页查询
     * @param request {@link PlanDesignInstanceQueryRequest}
     * @return 分页数据
     */
    Page<PlanDesignInstance> list(PlanDesignInstanceQueryRequest request, SysUser sysUser);

    /**
     * 根据id查询
     * @param id 方案设计的id
     * @return {@link PlanDesignInstance}
     */
    PlanDesignInstanceVO getById(Long id);

    /**
     * 根据id删除
     *
     * @param id 方案设计的id
     */
    void deleteToRecycleBin(Long id);

    Long addOrUpdateShareRecord(PlanDesignShareRecord planDesignShareRecord);

    Page<PlanDesignInstance> getMyCoopPlanDesign(PlanDesignInstanceQueryRequest planDesignInstanceQueryRequest);

    Set<String> updatePlanStatus(String status, Long planId);

    List<TemplateType> getReleaseTemplateType();

    Boolean copyPlanInfoById(Long planId, String planName);

    List<PlanDesignInstance> findPlanDesignList(Integer releaseStatus, Long dirId, String name, String creatorCode);

    /**
     * 查找回收站
     *
     * @param pageNum  页码
     * @param pageSize 个数
     * @param dirId    文件夹id
     * @param word     搜索词
     * @return {@link PlanDesignInstanceVO}
     */
    Page<PlanDesignInstanceVO> queryRecycleBin(int pageNum, int pageSize, Long dirId, String word);

    /**
     * 校验方案
     * @param planId
     * @return
     */
    Map<String,Object> checkPlan(Long planId);

    /**
     * 提交审批
     *
     * @param user
     * @return
     */
    Long submitApproval(SysUser user, ProcessApprovalRequest request, PlanDesignInstance plan);

    /**
     * 方案重命名
     *
     * @param request 必填参数是：id、name
     */
    void rename(Map<String, Object> request);

    /**
     * 回收站批量删除 不可恢复
     *
     * @param planIdList 方案集合
     * @param dirIdList  已经删除的文件夹Id
     */
    void removeFromRecycleBinBatch(List<Long> planIdList, List<Long> dirIdList);

    /**
     * 回收站批量恢复
     *  @param planIdList 方案集合
     * @param dirIdSet 文件夹Id
     */
    void recoverBatch(List<Long> planIdList, Set<Long> dirIdSet);

    /**
     * 批量删除到回收站 可恢复
     *
     * @param planIdList 方案集合
     * @param dirIdList  已经删除的文件夹Id
     */
    void deleteToRecycleBinBatch(List<Long> planIdList, List<Long> dirIdList);

    /**
     * 方案批量移动
     *
     * @param targetDirId 目标文件夹
     * @param planIdList  要移动的方案
     */
    void moveBatch(Long targetDirId, List<Long> planIdList);

    /**
     * 批量复制
     *
     * @param planIdList             方案id
     * @param oldDirIdAndNewDirIdMap 旧的id和新的id的映射关系
     * @param targetDirId            目标文件夹
     */
    void copyBatch(List<Long> planIdList, Map<Long, Long> oldDirIdAndNewDirIdMap, Long targetDirId);

    /**
     * 根据classId查询系统列表
     *
     * @param classId class分类id
     * @param libType 库
     * @return list
     */
    List<CiSimpleInfoVo> getSystemList(Long classId, LibType libType);

    List<PlanDesignInstance> findPublishedPlanDesignList(String name);

    List<PlanDesignInstance> getRecyclePlanByDirId(Set<Long> dirIds);

    /**
     * 获取资产仓库方案
     * @param dirIds 资产文件夹id
     * @param name 模糊匹配名称
     * @return 方案
     */
    List<PlanDesignInstance> getPublishedPlan(List<Long> dirIds, String name);

    /**
     * 通过方案主键获取方案
     * @param planIds
     * @param name
     * @return
     */
    List<PlanDesignInstance> findPlanDesignListByPlanIds(Long[] planIds, String name);

    /**
     * 根据方案id物理删除发布的视图并且删除和文件夹的关系表数据
     *
     * @param planId
     * @return
     */
    Boolean deletePublishedPlan(Long planId);

    /**
     * 更新最后修改时间
     *
     * @param planId 方案Id
     */
    void updateModifyTime(Long planId);

    List<PlanDesignInstance> queryMyPublishPlan();

    /**
     * 方案中视图列表
     *
     * @param planId
     * @return
     */
    Boolean updatePlanDiagramIsFlow(Long planId, Integer status);

    /**
     * 方案取消分享
     *
     * @param planDesignShareRecord {@link PlanDesignShareRecord}
     */
    void cancelShareRecord(PlanDesignShareRecord planDesignShareRecord);

    /**
     * 查询方案
     *
     * @param ids id集合
     * @return 方案集合
     */
    List<PlanDesignInstance> getByIds(Collection<Long> ids);

    /**
     * 根据方案id进行批量物理删除
     *
     * @param ids id集合
     * @return 状态值
     */
    Integer deleteByIds(Collection<Long> ids);
    /**
     * 获取所属文件夹的方案列表
     * @param dirIdList
     * @return
     */
    List<PlanDesignInstance> findPlanListByDirIds(Set<Long> dirIdList);

    /**
     * 供性能测试使用，批量发布方案
     * @param planIdList
     */
    @Deprecated
    void batchPublishPlan(List<Long> planIdList);

    /**
     * 批量审批方案
     * @param planIdList
     * @return
     */
    Map<String,Object> batchCheckPlan(List<Long> planIdList);

    /**
     * 修改方案关联系统
     * @param dirId
     */
    @Deprecated
    List<Long> updatePlanRelationSys(Long dirId);

    /**
     * 清洗数据使用，修改方案dirType
     * @param dirType
     * @return
     */
    @Deprecated
    boolean updatePlanDirType(Integer dirType);

    ResponseEntity<byte[]> downLoadAllFile(Long planId);

    /**
     * feign接口获取所有方案列表
     * @param dirType
     */
    List<PlanDesignInstanceDTO> findAllPlanListForFeign(Long dirId, Integer dirType, String name, Integer publishStatus);


    /**
     * 获取所有方案列表
     * @param name 模糊查询方案名称
     * @param publishStatus 发布状态
     */
    List<PlanDesignInstanceDTO> findAllPlanList(String name, Integer publishStatus);
    /**
     * 视图关联方案列表
     * @param diagramId
     * @return
     */
    Page<PlanInfoVO> findDiagramPlanList(Integer pageNum, Integer pageSize, String diagramId, Integer type);

    /**
     * 通过业务id和状态获取列表
     * @param businessKey
     * @param statusList
     * @return
     */
    Page<PlanDesignInstance> findPlanInstanceList(String businessKey, List<String> statusList, Integer dirType);

    /**
     * 获取方案信息
     * @param planId
     * @return
     */
    PlanDesignInstance getPlanDesignInstance(Long planId);

    /**
     * 检出方案
     * @return
     */
    JSONObject detectionPlan(DetectionPlanVo detectionPlanVo, SysUser sysUser);

    /**
     * 查询方案历史版本
     * @param planId
     * @return
     */
    List<PlanHistoryVersionVo> findPlanHistoryVersionList(Long planId);
    /**
     *查询方案历史版本
     * @param planIds
     * @return
     */
    Map<String, List<PlanDesignInstance>> findPlanHistoryVersionListByIds(List<Long> planIds);

    List<PlanDesignInstance> findAllPlanDesignListByPlanIds(Long[] toArray, String name);

    /**
     * 查询最新版本
     * @param planId
     * @return
     */
    PlanHistoryVersionVo getPlanLatestVersion(Long planId, Integer type);

    /**
     * 方案关联用户列表
     * @param planId
     * @return
     */
    List<PlanDesignInstanceDTO> findRenewVersionPlanList(Long planId);

    /**
     * 通过历史版本查询设计库数据
     * @param historyPlanIds
     * @return
     */
    List<PlanDesignInstanceDTO> findDesignHistoryPlanList(List<Long> historyPlanIds);

    /**
     * 获取资产最新版本
     * @param businessKey
     * @param dirType
     * @return
     */
    PlanDesignInstanceDTO getAssetsLatestPlan(String businessKey, Integer dirType);

    /**
     * 资产库最新版本方案列表
     * @param planVersionList
     * @return
     */
    List<PlanDesignInstanceDTO> findAssetsLatestPlanList(List<PlanVersionDTO> planVersionList);

    /**
     * 获取方案分享列表
     * @param planId
     * @return
     */
    List<PlanDesignShareRecordDTO> findShareRecordList(Long planId);

    /**
     * 查询交付物模板被使用的方案列表
     * @param templateId
     * @return
     */
    List<PlanDesignInstanceDTO> findByTemplateId(Long templateId);

    /**
     * 导出方案
     * @param planId
     */
    String exportPlan(HttpServletResponse response, Long planId);

    /**
     * 导出方案为pdf格式
     * @param response
     * @param planId
     * @return
     */
    String exportPlanForPdf(HttpServletResponse response, Long planId);

    List<PlanDesignInstance> findPlanInstanceList(List<MinePlanQueryVo> minePlanQueryVos);

    /**
     * 根据方案id物理删除发布的视图并且删除和文件夹的关系表数据
     *
     * @param planIds
     * @return
     */
    Boolean batchDeletePublishedPlan(List<Long> planIds);

    /**
     * 通过dirId或assetsDirIds获取方案
     * @param dirId
     * @return
     */
    List<PlanDesignInstance> findPlanInstanceListByDirId(Long dirId, List<Long> assetsDirIds);

    /**
     * 批量修改方案
     * @param planDesignInstanceList
     * @return
     */
    void updatePlanBatch(List<PlanDesignInstance> planDesignInstanceList);

    Page<PlanDesignInstanceDTO> getSystemPlan(Integer pageNum, Integer pageSize, String ciCode);
    Page<PlanDesignInstanceDTO> getSystemPlan(Integer pageNum, Integer pageSize, String ciCode,List<Long> relevanceIds);

    /**
     * 保存或修改方案
     * @param planDesignInstance
     */
    void saveOrUpdate(PlanDesignInstance planDesignInstance);

    /**
     * 校验是否存在未回复的问题
     * @param businessKey
     */
    void checkQuestion(String businessKey);

    /**
     * 校验发布位置
     * @param businessKey
     */
    Long checkPublishAssetsDir(String businessKey);

    /**
     * 方案通过
     * @param plan
     * @param userInfo
     */
    Long passPlan(PlanDesignInstance plan, UserInfo userInfo);

    /**
     * 方案终止
     * @param plan
     */
    void cancelPlan(PlanDesignInstance plan);

    void updateDiagramState(Long planId, Integer status);


    /**
     * 获取所属文件夹的方案列表
     * @param dirIdList
     * @return
     */
    List<PlanDesignInstance> findPlanList(Set<Long> dirIdList, LibType libType);

    /**
     *  根据方案状态获取数据集信息来源
     * @param planId
     * @return
     */
    DataSetEnum getDataSetFromByPlanId(Long planId);

    void saveOrUpdateSnapshot(Long planId);
    void deleteSnapshot(Long planId);
}
