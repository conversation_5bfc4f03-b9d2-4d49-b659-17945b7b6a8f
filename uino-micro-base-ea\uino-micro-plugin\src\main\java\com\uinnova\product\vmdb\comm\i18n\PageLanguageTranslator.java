package com.uinnova.product.vmdb.comm.i18n;

import com.binary.core.i18n.Language;
import com.binary.core.i18n.LanguageResolver;
import com.binary.core.i18n.LanguageTranslator;
import com.binary.json.JSON;

import java.util.HashMap;
import java.util.Map;

/**
 * 简单的将错误码和错误参数抛给前台.让前台页面进行国际化处理
 * 
 * <AUTHOR>
 *
 */
public class PageLanguageTranslator implements LanguageTranslator {

    /**
     * 默认设置为全局的语言转化器
     */
    public PageLanguageTranslator() {
        this(true);
    }

    /**
     * 
     * @param globalTranslator
     *            是否设置为全局的转换器
     */
    public PageLanguageTranslator(boolean globalTranslator) {
        if (globalTranslator) {
            LanguageResolver.setTranslator(this);
        }
    }

    @Override
    public String trans(String languageCode) {
        return trans(languageCode, (Object) null);
    }

    @Override
    public String trans(String languageCode, String jsonParams) {
        return trans(languageCode, (Object) null);
    }

    @Override
    public String trans(String languageCode, Object params) {
        Map<String, Object> result = new HashMap<String, Object>();

        result.put("languageCode", languageCode);
        result.put("params", params);

        return JSON.toString(result);
    }

    @Override
    public Map<String, String> getLanguageDictionary() {
        return new HashMap<String, String>();
    }

    @Override
    public Map<String, String> getLanguageDictionary(Language language) {
        return new HashMap<String, String>();
    }

}
