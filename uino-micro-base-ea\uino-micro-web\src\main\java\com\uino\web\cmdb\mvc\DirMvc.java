package com.uino.web.cmdb.mvc;

import java.util.List;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import com.uino.bean.permission.base.SysUser;
import com.uino.dao.util.ESUtil;
import com.uino.bean.cmdb.base.CcCiClassDir;
import com.uino.init.api.ApiResult;
import com.uino.init.api.ApiVersion;
import com.uino.util.sys.SysUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uinnova.product.vmdb.comm.doc.annotation.MvcDesc;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClassDir;
import com.uino.api.client.cmdb.IDirApiSvc;

@ApiVersion(1)
@Api(value = "分类目录", tags = {"分类目录"})
@RestController
@RequestMapping("/cmdb/dir")
@MvcDesc(author = "zmj", desc = "分类目录")
public class DirMvc {

    @Autowired
    private IDirApiSvc dirSvc;

    @ApiOperation("保存或更新目录，ID存在更新")
    @RequestMapping(value = "/saveOrUpdateDir",method = RequestMethod.POST)
    @ModDesc(desc = "保存或更新目录,ID存在更新", pDesc = "目录", pType = CcCiClassDir.class, rDesc = "目录ID", rType = Long.class)
    public ApiResult<Long> saveOrUpdateDir(HttpServletRequest request, HttpServletResponse response, @RequestBody CcCiClassDir dir) {
        dir.setIsLeaf(1);
        if (dir.getDirLvl() == null) {
            dir.setDirLvl(1);
        }
        dir.setDirPath("#" + ESUtil.getUUID() + "#");
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        dir.setDomainId(currentUserInfo.getDomainId());
        Long id = dirSvc.saveOrUpdateDir(dir);

        return ApiResult.ok(this).data(id);
    }

    @ApiOperation("通过目录ID删除目录")
    @RequestMapping(value="/removeDirById",method = RequestMethod.POST)
    @ModDesc(desc = "通过目录ID删除目录", pDesc = "目录ID", pType = Long.class, rDesc = "0失败,1成功", rType = Integer.class)
    public ApiResult<Integer> removeDirById(HttpServletRequest request, HttpServletResponse response, @RequestBody String body) {
        Long id = Long.parseLong(body.trim());
        Integer result = dirSvc.removeDirById(id);
        return ApiResult.ok(this).data(result);
    }

    @ApiOperation("获取分类目录")
    @RequestMapping(value="/queryDirList",method = RequestMethod.POST)
	@ModDesc(desc = "获取分类目录", pDesc = "目录ID", pType = Long.class, rDesc = "0失败,1成功", rType = Integer.class)
	public ApiResult<List<CcCiClassDir>> queryDirList(HttpServletRequest request, HttpServletResponse response, @RequestBody CCcCiClassDir cdt) {
        cdt = cdt == null ? new CCcCiClassDir() : cdt;
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        cdt.setDomainId(currentUserInfo.getDomainId());
        List<CcCiClassDir> result = dirSvc.queryDirList(cdt, "createTime", true);
        return ApiResult.ok(this).data(result);
	}
}
