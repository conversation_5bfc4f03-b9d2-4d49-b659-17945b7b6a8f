package com.uinnova.product.eam.model.vo;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uino.bean.cmdb.base.ESCIClassInfo;
import lombok.Data;

/**
 * <AUTHOR> wangchunlei
 * @Date :
 * @Description : TODO
 */
@Data
public class CiClassRltVo {

    @Comment("源端ci分类信息")
    private ESCIClassInfo sourceCiInfo;

    @Comment("目标端ci分类信息")
    private ESCIClassInfo targetCiInfo;

    @Comment("关系分类信息")
    private CcCiClassInfo rltClassInfo;

    @Comment("是否开启  默认为true 开启")
    private Boolean viewFlag = true;

    @Comment("构建方式 mode=1 连线（默认） mode=2 插入画布 mode=3 拖入形状 mode=4 其他 mode =5 拖入规则容器，mode=6 超文本")
    private Integer mode = 1;

    @Comment("显示名称")
    private String viewName;

}
