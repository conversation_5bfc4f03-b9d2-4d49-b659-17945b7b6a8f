package com.uinnova.product.eam.comm.bean;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.*;

public class Resolution {

    private Long id;
    /**
     * 决议概要
     */
    private String resolutionSummary;
    /**
     * 决议编号
     */
    private String resolutionNo;
    /**
     * 决议名称
     */
    private String resolutionSubject;
    /**
     * 参与部门
     */
    private String participatingDepartment;
    /**
     * ciCode
     */
    private String ciCode;
    /**
     * 决议文档
     */
    private Set<Map<String, Object>> docs = new HashSet<>();
    /**
     * 发布时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date resolutionTime;
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
    /**
     * 备注
     */
    private String remark;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getResolutionSummary() {
        return resolutionSummary;
    }

    public void setResolutionSummary(String resolutionSummary) {
        this.resolutionSummary = resolutionSummary;
    }

    public String getResolutionNo() {
        return resolutionNo;
    }

    public void setResolutionNo(String resolutionNo) {
        this.resolutionNo = resolutionNo;
    }

    public String getResolutionSubject() {
        return resolutionSubject;
    }

    public void setResolutionSubject(String resolutionSubject) {
        this.resolutionSubject = resolutionSubject;
    }

    public String getParticipatingDepartment() {
        return participatingDepartment;
    }

    public void setParticipatingDepartment(String participatingDepartment) {
        this.participatingDepartment = participatingDepartment;
    }

    public String getCiCode() {
        return ciCode;
    }

    public void setCiCode(String ciCode) {
        this.ciCode = ciCode;
    }

    public Set<Map<String, Object>> getDocs() {
        return docs;
    }

    public void setDocs(Set<Map<String, Object>> docs) {
        this.docs = docs;
    }

    public Date getResolutionTime() {
        return resolutionTime;
    }

    public void setResolutionTime(Date resolutionTime) {
        this.resolutionTime = resolutionTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof Resolution)) return false;
        Resolution that = (Resolution) o;
        return Objects.equals(id, that.id) &&
                Objects.equals(resolutionSummary, that.resolutionSummary) &&
                Objects.equals(resolutionNo, that.resolutionNo) &&
                Objects.equals(resolutionSubject, that.resolutionSubject) &&
                Objects.equals(participatingDepartment, that.participatingDepartment) &&
                Objects.equals(ciCode, that.ciCode) &&
                Objects.equals(docs, that.docs) &&
                Objects.equals(resolutionTime, that.resolutionTime) &&
                Objects.equals(updateTime, that.updateTime) &&
                Objects.equals(remark, that.remark);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, resolutionSummary, resolutionNo, resolutionSubject, participatingDepartment, ciCode, docs, resolutionTime, updateTime, remark);
    }
}
