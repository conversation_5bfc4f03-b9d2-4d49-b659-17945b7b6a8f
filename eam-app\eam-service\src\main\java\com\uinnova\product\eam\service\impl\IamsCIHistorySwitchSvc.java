package com.uinnova.product.eam.service.impl;

import com.uinnova.product.eam.service.ICIHistorySwitchSvc;
import com.uino.api.client.cmdb.ICIHistoryApiSvc;
import com.uino.bean.cmdb.base.ESCIHistoryInfo;
import com.uino.bean.cmdb.base.LibType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class IamsCIHistorySwitchSvc implements ICIHistorySwitchSvc {


    @Autowired
    ICIHistoryApiSvc ciHistoryApiSvc;

    @Autowired
    IamsCIHistoryDesignSvc iamsCiHistoryDesignSvc;

    @Autowired
    IamsCIHistoryPrivateSvc iamsCiHistoryPrivateSvc;

    @Override
    public void delAll(List<Long> classIds, LibType libType) {
        getCiHistoryApiSvc(libType).delAll(classIds);
    }

    @Override
    public ICIHistoryApiSvc getCiHistoryApiSvc() {
        return ciHistoryApiSvc;
    }

    @Override
    public ICIHistoryApiSvc getCiHistoryApiSvc(LibType libType) {
        if (LibType.PRIVATE.equals(libType)) {
            return iamsCiHistoryPrivateSvc;
        } else if (LibType.DESIGN.equals(libType)) {
            return iamsCiHistoryDesignSvc;
        } else {
            return ciHistoryApiSvc;
        }
    }

    @Override
    public List<String> getCIVersionList(String ciCode, Long classId, LibType libType) {
        return getCiHistoryApiSvc(libType).getCIVersionList(ciCode, classId);
    }

    @Override
    public ESCIHistoryInfo getCIInfoHistoryByCIVersion(String ciCode, Long classId, Long version, LibType libType) {
        return getCiHistoryApiSvc(libType).getCIInfoHistoryByCIVersion(ciCode, classId, version);
    }
}
