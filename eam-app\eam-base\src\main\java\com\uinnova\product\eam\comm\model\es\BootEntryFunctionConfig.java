package com.uinnova.product.eam.comm.model.es;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.io.Serializable;

@Data
@Comment("引导项菜单配置表[uino_eam_boot_function_config]")
public class BootEntryFunctionConfig implements Serializable {
    private static final long serialVersionUID = 1905122041950251207L;
    @Comment("主键id")
    private Long id;
    @Comment("引导项id")
    private Long bootEntryId;
    @Comment("引导项标题")
    private String title;
    @Comment("引导项功能介绍")
    private String bootIntroduce;
    @Comment("引导项功能按钮名称")
    private String bootButtonName;
    @Comment("二级引导项功能按钮名称")
    private String secondBootButtonName;
    @Comment("页面路径")
    private String menuPath;
    @Comment("菜单id")
    private String functionId;
    @Comment("1表示菜单引导2表示页面引导")
    private Integer bootType;
    @Comment("资源路径")
    private String resourcePath;
    @Comment("排序字段")
    private int sort;
    private Long createTime;
    private Long modifyTime;
}
