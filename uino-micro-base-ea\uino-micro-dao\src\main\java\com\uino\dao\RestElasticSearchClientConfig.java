package com.uino.dao;

import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.impl.nio.reactor.IOReactorConfig;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

/**
 * es配置类
 *
 * <AUTHOR>
 * @since 2022/5/16 22:22
 */
@Configuration
public class RestElasticSearchClientConfig {


    @Value("${isAuth}")
    private boolean isAuth = false;

    @Value("${esIps}")
    private String esIps;

    @Value("${esUser}")
    private String esUser;

    @Value("${esPwd}")
    private String esPwd;

    @Bean
    public RestHighLevelClient getRestHighLevelClient(){
        String[] ips = esIps.split(";");
        HttpHost[] httpHosts = new HttpHost[ips.length];
        for (int i = 0; i < ips.length; i++) {
            String[] ipPorts = ips[i].split("\\:");
            httpHosts[i] = new HttpHost(ipPorts[0], Integer.parseInt(ipPorts[1]), "http");
        }
        RestClientBuilder builder = initBuilder(httpHosts);
        RestHighLevelClient restHighLevelClient = new RestHighLevelClient(builder);
        return restHighLevelClient;
    }

    /**
     * <b>初始化
     *
     * @param httpHosts
     * @return
     */
    public RestClientBuilder initBuilder(HttpHost[] httpHosts) {
        RestClientBuilder restClientBuilder = RestClient.builder(httpHosts);
        restClientBuilder.setRequestConfigCallback(builder -> {
            builder.setConnectTimeout(60000);
            builder.setSocketTimeout(60000);
            builder.setConnectionRequestTimeout(60000);
            return builder;
        });
        restClientBuilder.setHttpClientConfigCallback(httpAsyncClientBuilder -> {
            httpAsyncClientBuilder.setMaxConnTotal(300);
            httpAsyncClientBuilder.setMaxConnPerRoute(300);
            httpAsyncClientBuilder.setDefaultIOReactorConfig(IOReactorConfig.custom().setSoKeepAlive(true)
                    .build());
            httpAsyncClientBuilder.setKeepAliveStrategy((response, context) -> TimeUnit.MINUTES.toMillis(5));
            if (isAuth) {
                CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
                credentialsProvider.setCredentials(AuthScope.ANY, new UsernamePasswordCredentials(esUser, esPwd));
                httpAsyncClientBuilder.setDefaultCredentialsProvider(credentialsProvider);
            }
            return httpAsyncClientBuilder;
        });
        return restClientBuilder;
    }

}
