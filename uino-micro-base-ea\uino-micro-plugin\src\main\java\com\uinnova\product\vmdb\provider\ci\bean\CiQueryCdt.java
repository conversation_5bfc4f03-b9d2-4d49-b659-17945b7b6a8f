package com.uinnova.product.vmdb.provider.ci.bean;

import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;

/**
 * <AUTHOR>
 *
 */
public class CiQueryCdt implements Condition {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	@Comment("模糊收索字段")
	private String like;
	
	@Comment("ID")
	private Long tagId;

	@Comment("IDS")
	private Long[] tagIds;

	@Comment("ci分类Id")
	private Long classId;
	
	@Comment("分类ID")
	private Long[] classIds;
	
	@Comment("是否刷新分类的数据0不刷新,1刷新")
	private Integer queryClass;
	
	@Comment("关系级别")
	private Integer[] ciRltLvls;
	
	

	public String getLike() {
		return like;
	}

	public void setLike(String like) {
		this.like = like;
	}

	public Long getTagId() {
		return tagId;
	}

	public void setTagId(Long tagId) {
		this.tagId = tagId;
	}

	public Long[] getTagIds() {
		return tagIds;
	}

	public void setTagIds(Long[] tagIds) {
		this.tagIds = tagIds;
	}

	public Long getClassId() {
		return classId;
	}

	public void setClassId(Long classId) {
		this.classId = classId;
	}

	public Long[] getClassIds() {
		return classIds;
	}

	public void setClassIds(Long[] classIds) {
		this.classIds = classIds;
	}

	public Integer getQueryClass() {
		return queryClass;
	}

	public void setQueryClass(Integer queryClass) {
		this.queryClass = queryClass;
	}

	public Integer[] getCiRltLvls() {
		return ciRltLvls;
	}

	public void setCiRltLvls(Integer[] ciRltLvls) {
		this.ciRltLvls = ciRltLvls;
	}

	
	
	
}
