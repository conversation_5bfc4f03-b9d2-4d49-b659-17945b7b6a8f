package com.uino.service.sys.notify.impl;

import com.alibaba.fastjson.JSONObject;
import com.uino.service.sys.microservice.INotifyChannelSvc;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import com.binary.core.http.HttpClient;
import com.uino.service.sys.notify.INotifySvc;
import com.uino.bean.sys.base.NotifyChannel;
import com.uino.bean.sys.base.NotifyChannel.HttpChannelInfo;
import com.uino.bean.sys.base.NotifyTypeConstants;
import com.uino.bean.sys.business.NotifyData;

import lombok.extern.slf4j.Slf4j;

/**
 * HTTP通知
 * 
 * @author: zoumengjie
 * @create: 2021/01/28 10:20
 **/
@Service(value = NotifyTypeConstants.HTTP)
@Slf4j
public class HttpNotifySvc implements INotifySvc {

    @Autowired
    private INotifyChannelSvc channelSvc;

    @Override
    public boolean sendNotify(NotifyData notifyData) {
        NotifyChannel notifyChannel = channelSvc.getNotifyChannelById(notifyData.getChannelId());
        HttpChannelInfo httpChannel = notifyChannel.getHttpChannelInfo();
        String url = httpChannel.getDestUrl();
//        String param = httpChannel.getPatam();
        log.info("send http notify======>>>>" + notifyData.getTitle());
        //封装参数
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("config",notifyChannel.getHttpChannelInfo().getParam());
        jsonObject.put("business",notifyData.getNotifyObject());
        boolean isSuccess = false;
        try {
            HttpClient instance = HttpClient.getInstance(url);
            instance.addRequestProperty("Content-Type", "application/json");
            // instance.addRequestProperty("tk", TokenUtil.getCurrentThreadToken());
            // instance.addRequestProperty("Authorization", "Bearer " + TokenUtil.getCurrentThreadToken());
            String res = instance.rest((String) null, jsonObject.toJSONString());
            isSuccess = true;
        } catch (Exception e) {
            Assert.isTrue(false, "接口请求失败，请检查渠道配置！");
            isSuccess = false;
        }
        log.info("send http notify successfully!");
        return isSuccess;
    }

}
