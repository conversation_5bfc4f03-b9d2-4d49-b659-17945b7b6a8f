package com.uino.cmdb.ci_rlt.svc;

import java.util.Collections;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import com.uino.StartBaseWebAppliaction;
import com.uino.dao.cmdb.ESCIRltSvc;
import com.uino.service.cmdb.microservice.ICIRltSvc;

import lombok.extern.slf4j.Slf4j;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = { StartBaseWebAppliaction.class }, webEnvironment = WebEnvironment.RANDOM_PORT)
@Slf4j
@ActiveProfiles("provider-local")
public class DelRltByCiIdsTest {
	@Autowired
	private ICIRltSvc rltSvc;
	@MockBean
	private ESCIRltSvc esCiRltSvc;

	@Before
	public void before() {
		Mockito.when(esCiRltSvc.deleteByQuery(Mockito.any(), Mockito.anyBoolean())).thenReturn(1);
	}

	@Test
	public void test01() {
		Integer re = rltSvc.delRltByCiIds(Collections.singleton(1L));
		Assert.assertTrue(re != null && re.intValue() >= 1);
	}
}
