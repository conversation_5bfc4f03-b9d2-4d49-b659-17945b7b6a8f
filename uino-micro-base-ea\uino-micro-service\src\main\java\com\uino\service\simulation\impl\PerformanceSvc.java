package com.uino.service.simulation.impl;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.uino.dao.BaseConst;
import com.uino.util.rsm.RsmUtils;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.FileItemFactory;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.lucene.queryparser.classic.QueryParser;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.ConstantScoreQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

import com.binary.core.i18n.LanguageResolver;
import com.binary.core.io.Resource;
import com.binary.core.io.support.ByteArrayResource;
import com.binary.core.util.BinaryUtils;
import com.binary.jdbc.Page;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.uino.bean.chart.bean.UinoChartDataBean;
import com.uino.bean.chart.enums.UinoChartType;
import com.uino.bean.cmdb.base.ESCIClassInfo;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.base.ESCIRltInfo;
import com.uino.bean.cmdb.query.ESAttrBean;
import com.uino.bean.monitor.base.ESKpiInfo;
import com.uino.bean.monitor.base.SimulationRuleInfo;
import com.uino.bean.monitor.buiness.ImportPerformanceReqDto;
import com.uino.bean.monitor.buiness.PerformanceQueryDto;
import com.uino.bean.monitor.buiness.SearchKpiBean;
import com.uino.bean.sys.base.ESDictionaryItemInfo;
import com.uino.bean.sys.query.ESDictionaryItemSearchBean;
import com.uino.bean.tp.base.FinalPerformanceDTO;
import com.uino.bean.tp.base.TpRuleDTO;
import com.uino.bean.tp.enums.TpRuleStatusEnum;
import com.uino.bean.tp.enums.TpRuleTypeEnum;
import com.uino.dao.cmdb.ESCIClassSvc;
import com.uino.dao.cmdb.ESCIRltSvc;
import com.uino.dao.cmdb.ESCISvc;
import com.uino.monitor.performance.IUinoPerformanceSvc;
import com.uino.monitor.tp.metric.TpRuleSvc;
import com.uino.service.simulation.IPerformanceSvc;
import com.uino.service.sys.microservice.IDictionarySvc;
import com.uino.service.util.FileUtil;
import com.uino.service.util.FileUtil.ExcelUtil;
import com.uino.util.sys.SysUtil;
import com.uino.util.sys.ValidDtoUtil;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RefreshScope
public class PerformanceSvc implements IPerformanceSvc {

	@Autowired
	private KpiSvc kpiSvc;

	@Autowired
	private ESCISvc esCISvc;

	@Autowired
	private ESCIClassSvc esCIClassSvc;

	@Autowired
	private ESCIRltSvc esCIRltSvc;

	@Value("${local.resource.space:}")
	private String localPath;

	@Value("${base.load-tp:false}")
	private boolean enableTp;

	@Autowired
	private IUinoPerformanceSvc performanceSvc;

	@Autowired
	private TpRuleSvc tpRuleSvc;

	@Autowired
	private IDictionarySvc dictionarySvc;

	@Autowired
	private RsmUtils rsmUtils;

	@Value("${monitor.select.include.domainId:true}")
	private boolean includeDomainId;

	private final static SimpleDateFormat SIMPLE_DATE_FORMAT = new SimpleDateFormat("yyyyMMddHHmmss");
	private final static String DEFAULT_INSTANCE = "_";
	private static final Gson GSON = new GsonBuilder().create();

	@Override
	public void importPerformance(ImportPerformanceReqDto importDto) {
		ValidDtoUtil.valid(importDto);
		String filePath = importDto.getExcelFilePath();

		// 从对象存储获取该文件并更新本地文件
		rsmUtils.downloadRsmAndUpdateLocalRsm(filePath);

		File file = new File(localPath + filePath);
		FileItemFactory factory = new DiskFileItemFactory(16, null);
		FileItem item = factory.createItem("file", "text/html", true, file.getName());
		int bytesRead = 0;
		byte[] buffer = new byte[8192];
		try {
			FileInputStream fis = new FileInputStream(file);
			OutputStream os = item.getOutputStream();
			while ((bytesRead = fis.read(buffer, 0, 8192)) != -1) {
				os.write(buffer, 0, bytesRead);
			}
			os.close();
			fis.close();
		} catch (IOException e) {
			throw new RuntimeException(e);
		}
		// 创建自定义的MultipartFile实现
		MultipartFile multipartFile = new MultipartFile() {
			private final FileItem fileItem;

			{
				this.fileItem = item;
			}

			@Override
			public String getName() {
				return fileItem.getFieldName();
			}

			@Override
			public String getOriginalFilename() {
				return fileItem.getName();
			}

			@Override
			public String getContentType() {
				return fileItem.getContentType();
			}

			@Override
			public boolean isEmpty() {
				return fileItem.getSize() == 0;
			}

			@Override
			public long getSize() {
				return fileItem.getSize();
			}

			@Override
			public byte[] getBytes() throws IOException {
				return fileItem.get();
			}

			@Override
			public InputStream getInputStream() throws IOException {
				return fileItem.getInputStream();
			}

			@Override
			public void transferTo(File dest) throws IOException {
				try {
					fileItem.write(dest);
				} catch (Exception e) {
					throw new IOException("Error writing file", e);
				}
			}
		};
		this.importPerformance(multipartFile, importDto);
	}

	@Override
	public void importPerformance(MultipartFile excelFile, ImportPerformanceReqDto importDto) {
		importDto.valid();
		// 校验导入文件合法性
		Assert.notNull(excelFile, "导入文件不得为空");
		boolean isXlsx = FileUtil.ExcelUtil.validExcelImportFile(excelFile);
		Integer objType = importDto.getObjType();
		Long classId = importDto.getClassId();
		List<Long> ciIds = importDto.getCiIds();
		if(BinaryUtils.isEmpty(importDto.getDomainId())){
			importDto.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
		}
		// 校验导入对象合法性，以及获取导入对象信息
		Assert.isTrue(objType != null && (objType == 0L || objType == 1L), "性能对象类型错误");
		BoolQueryBuilder query = QueryBuilders.boolQuery();
		if (classId != null) {
			query.must(QueryBuilders.termQuery("classId", classId));
		}
		if (!BinaryUtils.isEmpty(ciIds)) {
			query.must(QueryBuilders.termsQuery("id", ciIds));
		}
		query.must(QueryBuilders.termQuery("domainId",importDto.getDomainId()));
		List<ESCIInfo> ciList = esCISvc.getListByQuery(1, 99999, query).getData();
		Assert.isTrue(!BinaryUtils.isEmpty(ciList), "未匹配到孪生体");
		SearchKpiBean searchDto = SearchKpiBean.builder().pageNum(1).pageSize(9999).classId(classId).domainId(importDto.getDomainId()).build();
		Page<ESKpiInfo> listByQuery = kpiSvc.queryKpiInfoPage(searchDto);
		List<ESKpiInfo> kpiInfos = listByQuery.getData();
		Assert.notEmpty(kpiInfos, "分类下没有指标");
		Workbook workBook;
		try {
			if (isXlsx) {
				workBook = new XSSFWorkbook(excelFile.getInputStream());
			} else {
				workBook = new HSSFWorkbook(excelFile.getInputStream());
			}
		} catch (IOException e) {
			log.error("读取性能excel文件异常", e);
			throw new RuntimeException("读取性能excel文件异常");
		}
		// 获取到了工作簿对象开始操作数据
		long nowTime = System.currentTimeMillis();

		// 校验指标是否都存在
		String importNotice = LanguageResolver.trans("BS_MNAME_IMPORT_NOTICE");
		if (importNotice == null) {
			importNotice = SysUtil.StaticUtil.README_SHEETNAME;
		}
		List<String> metrics = new ArrayList<>();
		Iterator<Sheet> sheetIterator = workBook.sheetIterator();
		while (sheetIterator.hasNext()) {
			Sheet sheet = sheetIterator.next();
			String metric = sheet.getSheetName();
			if (!importNotice.equals(metric)) {
				metrics.add(metric);
			}
		}
		metrics.forEach(metric -> Assert.isTrue(
				kpiInfos.stream().anyMatch(metricInfo -> metricInfo.getKpiCode().equals(metric)),
				"指标：" + metric + "不存在"));
		Assert.notEmpty(metrics, "导入失败，性能数据不能为空");
		Map<String, ESKpiInfo> kpiInfoMap = kpiInfos.stream()
				.collect(Collectors.toMap(ESKpiInfo::getKpiCode, a -> a, (k1, k2) -> k1));

		workBook.sheetIterator().forEachRemaining(sheet -> {
			String metric = sheet.getSheetName();
			if (metrics.contains(metric)) {
				List<FinalPerformanceDTO> saveDtos = new LinkedList<>();
				for (int rowNum = 0; rowNum <= sheet.getLastRowNum(); rowNum++) {
					Row row = sheet.getRow(rowNum);
					if (row == null) {
						log.error("指标：{}遇到空行", metric);
						break;
					}
					// 忽略第一行
					if (sheet.getFirstRowNum() == row.getRowNum()) {
						continue;
					}
					// 按位读取时间差与性能值
					String deltaValStr = ExcelUtil.getStringValue(row.getCell(0));
					String performanceValStr = ExcelUtil.getStringValue(row.getCell(1));

					if (deltaValStr.isEmpty() || performanceValStr.isEmpty()) {
						log.error("sheet【{}】row【{}】数据填写不完全", metric, row.getRowNum());
						break;
					}
					// 时间差，单位分钟
					int deltaVal;
					try {
						deltaVal = Math.abs(Integer.parseInt(deltaValStr));
						new BigDecimal(performanceValStr);
					} catch (Exception e) {
						log.error("sheet【{}】row【{}】数据填写不符合格式", metric, row.getRowNum());
						continue;
					}
					long time = System.currentTimeMillis();
					long longTime = Long.parseLong(SIMPLE_DATE_FORMAT.format(time));
					String unit = kpiInfoMap.get(metric).getUnitName();
					for (ESCIInfo ciInfo : ciList) {
						try {
							// 直接入库
							FinalPerformanceDTO saveDTO = new FinalPerformanceDTO();
							saveDTO.setCiCode(ciInfo.getCiCode());
							saveDTO.setClassId(ciInfo.getClassId());
							saveDTO.setInstance(DEFAULT_INSTANCE);
							saveDTO.setMetric(metric);
							saveDTO.setUnit(unit);
							saveDTO.setTime(nowTime - (deltaVal * 60 * 1000L));
							Double value = new BigDecimal(performanceValStr)
									.setScale(2, BigDecimal.ROUND_HALF_UP)
									.doubleValue();
							saveDTO.setValue(value);
							saveDTO.setDomainId(ciInfo.getDomainId());
							// saveDTO.setId(String.valueOf(ESUtil.getUUID()));
							// saveDTO.setPerfIdentify(metric + "_" + ciInfo.getCiCode());
							saveDtos.add(saveDTO);
						} catch (Exception ignored) {}
					}
				}
				// if (enableTp) {
				// Assert.notEmpty(sendDtos, "指标：" + metric + "导入失败，性能数据不能为空");
				// // 数据直接发送至kafka
				// try {
				// MessageQueueProducer messageQueueProducer =
				// SpringUtil.getBean(MessageQueueProducer.class);
				// messageQueueProducer.sendMessage(MessageTopicConst.MIDDLE_TP_PERF,
				// GSON.toJson(sendDtos));
				// } catch (Exception e) {
				// Assert.isTrue(false, "配置异常,数据发送失败");
				// }
				// } else {
					Assert.notEmpty(saveDtos, "指标：" + metric + "导入失败，性能数据不能为空");
					// 保存性能数据
					performanceSvc.saveOrUpdateBatch(saveDtos);
				// }
			}
		});
		try {
			workBook.close();
		} catch (IOException ignored) {
		}
	}

	@Override
	public Resource exportPerformanceTemplate(Long domainId,Long objId, Integer objType) {
		domainId = domainId == null ? BaseConst.DEFAULT_DOMAIN_ID : domainId;
		// 校验数据以及查询对应数据信息
		Resource resource = null;
		Assert.isTrue(objType != null && (objType == 0 || objType == 1), "性能对象类型错误");
		Assert.notNull(objId, "性能对象参数为空");
		Long classId = objId;
		if (objType == 0L) {
			ESCIClassInfo classInfo = esCIClassSvc.getById(objId);
			// ESCIInfo ci = esCISvc.getById(objId);
			Assert.notNull(classInfo, "性能对象不存在");
			// classId = ci.getClassId();
		} else {
			ESCIRltInfo rlt = esCIRltSvc.getById(objId);
			Assert.notNull(rlt, "性能对象不存在");
			classId = rlt.getClassId();
		}
		SearchKpiBean searchDto = SearchKpiBean.builder().pageNum(1).pageSize(9999).classId(classId).domainId(domainId).build();
		List<ESKpiInfo> kpis = kpiSvc.queryKpiInfoPage(searchDto).getData();
		Assert.notEmpty(kpis, "所选对象不存在指标数据");
		// 走到这里需要的数据已经有了，开始构建excel进行导出操作
		XSSFWorkbook workBook = null;
		try {
			String templateName = "/static_res/performance_import_template.xlsx";
			InputStream is = this.getClass().getResourceAsStream(templateName);
			workBook = new XSSFWorkbook(is);
		} catch (IOException e) {
			log.error("模板文件获取失败");
		}
		XSSFCellStyle cellStyle = workBook.createCellStyle();
		XSSFFont cellFont = workBook.createFont();
		cellFont.setFontName("微软雅黑");
		cellFont.setFontHeightInPoints((short) 14);
		cellStyle.setFont(cellFont);
		for (ESKpiInfo kpi : kpis) {
			XSSFSheet sheet = workBook.createSheet(kpi.getKpiCode());
			XSSFRow titleRow = sheet.createRow(0);
			XSSFCell deltaTCell = titleRow.createCell(0);
			deltaTCell.setCellStyle(cellStyle);
			deltaTCell.setCellValue("时间差");
			XSSFCell performanceValCell = titleRow.createCell(1);
			performanceValCell.setCellStyle(cellStyle);
			performanceValCell.setCellValue("性能值");
		}
		// excel数据写完了，构造输出流
		ByteArrayOutputStream os = new ByteArrayOutputStream();
		String exportFileName = "性能模板";
		try {
			workBook.write(os);
			resource = new ByteArrayResource(os.toByteArray(), exportFileName + ".xlsx");
		} catch (IOException e) {
			log.error("导出性能模板流异常", e);
		} finally {
			try {
				os.close();
				workBook.close();
			} catch (IOException e) {
				log.error("导出性能模板关闭流异常", e);
			}
		}
		return resource;
	}

	@Override
	public Page<FinalPerformanceDTO> searchPerformance(PerformanceQueryDto queryDto) {
		Page<FinalPerformanceDTO> res = performanceSvc.queryPerformancePage(queryDto);
		// if (!BinaryUtils.isEmpty(res)) {
		// // 转化枚举值指标
		// tranEnumMetric(res, queryDto.getClassId());
		// }
		return res;
	}

	@Override
	public UinoChartDataBean<List<Double>> searchPerformanceGraph(PerformanceQueryDto queryDto) {
		if(includeDomainId && BinaryUtils.isEmpty(queryDto.getDomainId())){
			queryDto.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
		}
		UinoChartDataBean<List<Double>> lineChart = new UinoChartDataBean<>(UinoChartType.LINECHART);
		List<FinalPerformanceDTO> perfData = this.searchPerformance(queryDto).getData();
		List<Object> xData = new ArrayList<>();
		List<Double> data = new ArrayList<>();
		perfData.stream().sorted((x, y) -> x.getTime().compareTo(y.getTime())).forEach(perf -> {
			xData.add(perf.getTime());
			data.add(perf.getValue());
		});
		lineChart.setXData(xData);
		lineChart.setData(data);
		lineChart.setYData(new ArrayList<>());
		return lineChart;
	}

	/**
	 * 翻译枚举类型的指标
	 *
	 * @param res
	 * @param classId
	 * @author: weixuesong
	 * @date: 2021/3/2 14:52
	 * @return: void
	 */
	private void tranEnumMetric(Page<FinalPerformanceDTO> res, Long classId) {
		if (classId == null) {
			return;
		}
		for (FinalPerformanceDTO perf : res.getData()) {
			ESDictionaryItemSearchBean esDictionaryItemSearchBean = new ESDictionaryItemSearchBean();
			esDictionaryItemSearchBean.setAndAttrs(new ArrayList<ESAttrBean>() {
				{

					ESAttrBean nameBean = new ESAttrBean();
					nameBean.setKey("kpiCode");

					String metric = perf.getMetric();
					nameBean.setValue(metric);
					nameBean.setOptType(1);
					add(nameBean);

					ESAttrBean classBean = new ESAttrBean();
					classBean.setKey("classIds");
					classBean.setValue(classId);
					classBean.setOptType(2);
					add(classBean);

					ESAttrBean typeBean = new ESAttrBean();
					typeBean.setKey("kpiType");
					typeBean.setValue("3");
					typeBean.setOptType(1);
					add(typeBean);
				}
			});
			esDictionaryItemSearchBean.setPageNum(1);
			esDictionaryItemSearchBean.setPageSize(1);
			esDictionaryItemSearchBean.setDictClassId(2L);
			Page<ESDictionaryItemInfo> dicPage = dictionarySvc.searchDictItemPageByBean(esDictionaryItemSearchBean);
			if (dicPage.getData().isEmpty()) {
				continue;
			}
			ESDictionaryItemInfo dicInfo = dicPage.getData().get(0);
			String tranStr = dicInfo.getAttrs().get("constraint");
			if (BinaryUtils.isEmpty(tranStr)) {
				continue;
			}
			List<String> split = Stream.of(tranStr.split(";")).collect(Collectors.toList());
			String replaceStr = perf.getValue() + "-";
			for (String str : split) {
				if (str.contains(replaceStr)) {
					String valueString = str.replace(replaceStr, "");
					Double value = new BigDecimal(valueString)
							.setScale(2, BigDecimal.ROUND_HALF_UP)
							.doubleValue();
					perf.setValue(value);
					break;
				}
			}
		}
	}

	@Override
	public Page<FinalPerformanceDTO> searchNoCiPerformance(PerformanceQueryDto queryDto) {
		if(includeDomainId && BinaryUtils.isEmpty(queryDto.getDomainId())){
			queryDto.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
		}
		BoolQueryBuilder query = QueryBuilders.boolQuery();
		ConstantScoreQueryBuilder constantScoreQuery = QueryBuilders.constantScoreQuery(query);
		query.must(QueryBuilders.termQuery("domainId",queryDto.getDomainId()));
		query.mustNot(QueryBuilders.existsQuery("ciId"));
		if (!BinaryUtils.isEmpty(queryDto.getKeyword())) {
			String words = "*" + QueryParser.escape(queryDto.getKeyword()) + "*";
			BoolQueryBuilder wildQuery = QueryBuilders.boolQuery();
			wildQuery.should(QueryBuilders.wildcardQuery("metric", words));
			wildQuery.should(QueryBuilders.wildcardQuery("kpi_name", words));
			query.must(wildQuery);
		}
		if (Boolean.TRUE.equals(queryDto.getLast())) {
			return performanceSvc.getLastPerfListByQuery(queryDto.getDomainId(),queryDto.getPageNum(), queryDto.getPageSize(),
					constantScoreQuery, queryDto.getOrder(), queryDto.isAsc());
		}
		Long timeStart = queryDto.getTimeStart();
		Long timeEnd = queryDto.getTimeEnd();
		if (timeStart == null) {
			// 默认查询一年内的数据
			timeStart = LocalDateTime.now().plusYears(-1).toInstant(ZoneOffset.of("+8")).toEpochMilli();
		}
		if (timeEnd == null) {
			timeEnd = System.currentTimeMillis();
		}
		if (timeStart > timeEnd) {
			throw new RuntimeException("查询开始时间不能大于结束时间");
		}

		return performanceSvc.getSortListByQuery(queryDto.getDomainId(),queryDto.getPageNum(), queryDto.getPageSize(), constantScoreQuery,
				queryDto.getOrder(), timeStart, timeEnd, queryDto.isAsc());
	}

	@Override
	public List<String> getPerfDataLabel(Long classId) {
		BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
		ConstantScoreQueryBuilder constantScoreQueryBuilder = QueryBuilders.constantScoreQuery(boolQueryBuilder);
		boolQueryBuilder.filter(QueryBuilders.termQuery("dataStatus.keyword", TpRuleStatusEnum.ENABLE));
		boolQueryBuilder.filter(QueryBuilders.termQuery("classId", classId));
		boolQueryBuilder.filter(QueryBuilders.termQuery("ruleType.keyword", TpRuleTypeEnum.LABEL_RICH));
		List<TpRuleDTO> rules = tpRuleSvc.getListByQuery(constantScoreQueryBuilder);
		if (BinaryUtils.isEmpty(rules)) {
			return new ArrayList<>();
		}
		Collections.sort(rules);
		TpRuleDTO tpRuleDTO = rules.get(0);
		return tpRuleDTO.getLabelMappings().stream().map(TpRuleDTO.LabelMappingDTO::getTargetName)
				.collect(Collectors.toList());
	}

	@Override
	public void simulationPerformance(SimulationRuleInfo bean) {
		bean.valid();
		List<FinalPerformanceDTO> saveDtos = new ArrayList<>();
		Long classId = bean.getClassId();
		List<Long> ciIds = bean.getCiIds();
		String metric = bean.getMetric();
		Long startTime = bean.getStartTime();
		Long endTime = bean.getEndTime();
		String sendCycle = bean.getSendCycle();
		Integer dataType = bean.getDataType();
		String constraint = bean.getConstraint();

		Boolean isCycle = bean.getIsCycle();
		if(includeDomainId && BinaryUtils.isEmpty(bean.getDomainId())){
			bean.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
		}
		Assert.isTrue(dataType.intValue() == 1 || dataType.intValue() == 2, "不支持的数据类型");
		long cycle = 60;
		if (sendCycle.endsWith("s")) {
			cycle = Long.valueOf(sendCycle.replace("s", "")) * 1000;
		} else if (sendCycle.endsWith("m")) {
			cycle = Long.valueOf(sendCycle.replace("m", "")) * 60 * 1000;
		} else if (sendCycle.endsWith("h")) {
			cycle = Long.valueOf(sendCycle.replace("h", "")) * 60 * 60 * 1000;
		} else {
			Assert.isTrue(false, "不支持的时间类型");
		}

		List<ESKpiInfo> kpiInfos = kpiSvc
				.queryKpiInfoPage(SearchKpiBean.builder().kpiNames(Collections.singletonList(metric)).domainId(bean.getDomainId()).build())
				.getData();
		Assert.isTrue(!BinaryUtils.isEmpty(kpiInfos), "指标不存在");
		ESKpiInfo kpiInfo = kpiInfos.get(0);
		BoolQueryBuilder query = QueryBuilders.boolQuery();
		if (classId != null) {
			query.must(QueryBuilders.termQuery("classId", classId));
		}
		if (!BinaryUtils.isEmpty(ciIds)) {
			query.must(QueryBuilders.termsQuery("id", ciIds));
		}
		query.must(QueryBuilders.termQuery("domainId",bean.getDomainId()));
		List<ESCIInfo> ciList = esCISvc.getListByQuery(1, 99999, query).getData();
		if (BinaryUtils.isEmpty(ciList)) {
			log.info("未匹配到孪生体，已结束指标[" + metric + "]推送任务！");
			return;
		}

		int num = 0;
		String[] split = dataType.intValue() == 1 ? constraint.split("(?<=\\d)-") : constraint.split(",");
		Assert.isTrue(split.length > 0, "数据范围格式异常");
		for (long time = startTime; time <= endTime; time += cycle) {
			String val = null;
			try {
				if (dataType.intValue() == 1) { // 随机
					if (kpiInfo.getValType() == 1) {
						// 数值型随机，默认生成两位小数
						Double start = Double.valueOf(split[0]);
						Double end = Double.valueOf(split[1]);
						double value = ((double) Math.abs(new Random().nextInt()) % 101) / 100 * (end - start) + start;
						DecimalFormat df = new DecimalFormat("0.##");
						df.setRoundingMode(RoundingMode.DOWN);
						val = df.format(value);
					} else if (kpiInfo.getValType() == 3) {
						// 枚举型随机
						int index = 0;
						if (split.length > 1) {
							index = new Random().nextInt(split.length);
						}
						val = split[index];
					}
				} else if (dataType.intValue() == 2) { // 序列
					if (split.length <= num) {
						if (isCycle == true) {
							num = 0;
						} else {
							break;
						}
					}
					val = split[num];
				}
			} catch (Exception e) {
				log.info("模拟数据规则格式错误：" + constraint + e.getMessage());
				Assert.isTrue(false, "数据格式异常");
			}

			try {
				Double value = new BigDecimal(val).doubleValue();
				for (ESCIInfo ci : ciList) {
					FinalPerformanceDTO saveDTO = new FinalPerformanceDTO();
					saveDTO.setCiCode(ci.getCiCode());
					saveDTO.setClassId(ci.getClassId());
					saveDTO.setInstance(DEFAULT_INSTANCE);
					saveDTO.setMetric(metric);
					saveDTO.setUnit(kpiInfo.getUnitName());
					saveDTO.setTime(time);
					saveDTO.setValue(value);
					saveDTO.setDomainId(ci.getDomainId());
					saveDtos.add(saveDTO);
				}
			} catch (Exception e) {
				log.error("Simulation perf value:{} is not a number", val);
			}

			num++;
		}
		// 保存性能数据
		if (!BinaryUtils.isEmpty(saveDtos)) {
			performanceSvc.saveOrUpdateBatch(saveDtos);
		}
	}
}
