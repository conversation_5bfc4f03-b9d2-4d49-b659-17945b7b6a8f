package com.uinnova.product.eam.web.mix.diagram.v2.peer;

import com.binary.core.exception.BinaryException;
import com.binary.core.util.BinaryUtils;
import com.uinnova.product.eam.api.diagram.ESDiagramVersionApiClient;
import com.uinnova.product.eam.api.diagram.VcDiagramVersionApiClient;
import com.uinnova.product.eam.base.diagram.exception.UnAuthorizedException;
import com.uinnova.product.eam.base.diagram.mix.model.VcDiagramVersionInfo;
import com.uinnova.product.eam.base.diagram.model.*;
import com.uinnova.product.eam.db.diagram.es.ESDiagramDao;
import com.uinnova.product.eam.model.diagram.ESDiagramVersionInfo;
import com.uinnova.product.eam.service.IEamDiagramSvc;
import com.uinnova.product.eam.service.diagram.ESDiagramSvc;
import com.uinnova.product.eam.service.diagram.ESDiagramVersionSvc;
import com.uinnova.product.eam.service.diagram.IEamShareDiagramSvc;
import com.uinnova.product.eam.web.diagram.bean.DiagramVersionInfo;
import com.uinnova.product.vmdb.comm.i18n.MessageUtil;
import com.uino.util.sys.SysUtil;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.permission.query.CSysUser;
import com.uino.api.client.permission.IUserApiSvc;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;


@Service
public class ESDiagramVersionPeer {

	@Autowired
	private VcDiagramVersionApiClient diagramVersionSvc;

	@Autowired
	private ESDiagramVersionApiClient esDiagramVersionApiClient;

	@Autowired
	private IUserApiSvc userApiSvc;

	@Autowired
	private IEamDiagramSvc eamDiagramSvc;

	@Autowired
	private ESDiagramSvc esDiagramSvc;

	@Autowired
	private ESDiagramVersionSvc esDiagramVersionSvc;

	@Autowired
	private IEamShareDiagramSvc eamShareDiagramSvc;

	@Autowired
	private ESDiagramDao esDiagramDao;

	//private RsmClient rsmClient;

	@Value("${http.resource.space}")
	private String httpResouceUrl;

	/** RSM-SLAVE访问入口 **/
	private String rsmSlaveRoot;


	/** RSM分类代码 **/
	private String rsmClassCode;

	public String getRsmSlaveRoot() {
		return rsmSlaveRoot;
	}

	public void setRsmSlaveRoot(String rsmSlaveRoot) {
		this.rsmSlaveRoot = rsmSlaveRoot;
	}

	public String getRsmClassCode() {
		return rsmClassCode;
	}

	public void setRsmClassCode(String rsmClassCode) {
		this.rsmClassCode = rsmClassCode;
	}


	public ESDiagramVersionInfo queryDiagramVersionById(Long versionId, Boolean versionFlag, Boolean hasUserInfo, Boolean needAuth) {
		//1.查询基础的历史版本信息
		VcDiagramVersion vcDiagramVersion = esDiagramVersionSvc.queryDiagramVersionById(versionId);
		//先校验下是否有主视图的权限
		Long diagramId = vcDiagramVersion.getDiagramId();
		// 校验逻辑注释掉
		// udgeSingleDiagramAuth(diagramId, null, needAuth);
		//2.查询视图历史版本全量数据
		ESDiagramDTO esDiagramDTO = new ESDiagramDTO();
		//versionFlag=true 标识是主视图，=false标识是历史视图
		if (versionFlag) {
			esDiagramDTO = esDiagramSvc.queryESDiagramInfoById(versionId, null, false);
		} else {
			esDiagramDTO = esDiagramSvc.queryESDiagramInfoById(versionId, "version", true);
		}
		if (hasUserInfo) {
			String ownerCode = esDiagramDTO.getDiagram().getOwnerCode();
			CSysUser user = new CSysUser();
			user.setLoginCodeEqual(ownerCode);
			List<SysUser> userInfo = userApiSvc.getSysUserByCdt(user);
			esDiagramDTO.setCreator(userInfo.get(0));
			String icon1 = esDiagramDTO.getDiagram().getIcon1();
			esDiagramDTO.getDiagram().setIcon1(httpResouceUrl + icon1);
		}
		//封装返回信息
		ESDiagramVersionInfo esDiagramVersionInfo = new ESDiagramVersionInfo();
		esDiagramVersionInfo.setDiagramVersion(vcDiagramVersion);
		esDiagramVersionInfo.setEsDiagram(esDiagramDTO);
		return esDiagramVersionInfo;
	}

	public Long restoreDiagramByVersionId(Long currDiagramId, Long historyVersionId) {
		return esDiagramVersionApiClient.restoreDiagramByVersionId(currDiagramId, historyVersionId);
	}


	public Integer removeDiagramVersionById(Long id, String token) {
		Long domainId = SysUtil.getCurrentUserInfo().getDomainId();
		//删除资源文件
		VcDiagramVersionInfo versionInfo = diagramVersionSvc.queryDiagramVersionInfoById(domainId, id, null);
		if(BinaryUtils.isEmpty(versionInfo)) {
			return null;
		}
		VcDiagramVersion diagramVersion = versionInfo.getDiagramVersion();

		String ci3dPointPath = diagramVersion.getCi3dPoint();
		String iconPath = diagramVersion.getIcon1();
		String xmlPath = diagramVersion.getDiagramXml();
		String svgPath = diagramVersion.getDiagramSvg();
		String jsonPath = diagramVersion.getDiagramJson();

		//if(!BinaryUtils.isEmpty(ci3dPointPath)) {
		//	rsmClient.removeResource(ci3dPointPath, token);
		//}
		//if(!BinaryUtils.isEmpty(iconPath)) {
		//	rsmClient.removeResource(iconPath, token);
		//}
		//if(!BinaryUtils.isEmpty(xmlPath)) {
		//	rsmClient.removeResource(xmlPath, token);
		//}
		//if(!BinaryUtils.isEmpty(svgPath)) {
		//	rsmClient.removeResource(svgPath, token);
		//}
		//if(!BinaryUtils.isEmpty(jsonPath)) {
		//	rsmClient.removeResource(jsonPath, token);
		//}

		return diagramVersionSvc.removeDiagramVersionById(domainId, id);
	}

	public Long createDiagramByCurrVersion(Long diagramId, boolean versionFlag) {
		return esDiagramVersionApiClient.createDiagramByCurrVersion(diagramId, versionFlag);
	}

	public List<DiagramVersionInfo> queryDiagramVersionByDiagramId(Long diagramId) {
		Long domainId = SysUtil.getCurrentUserInfo().getDomainId();
		MessageUtil.checkEmpty(diagramId, "diagramId");
		ESDiagram esDiagram = judgeSingleDiagramAuth(diagramId, null, true);
		CVcDiagramVersion cdt = new CVcDiagramVersion();
		cdt.setDomainId(domainId);
		cdt.setDiagramId(diagramId);
		// 不查手动创建的数据
		// cdt.setAutoCreat(1);
		List<VcDiagramVersion> diagramVersions = diagramVersionSvc.queryVcDiagramVersionList(domainId, cdt , "VERSION_TIME DESC");
		if(!BinaryUtils.isEmpty(diagramVersions)) {
			for(VcDiagramVersion version : diagramVersions) {
				if (version.getId().equals(version.getDiagramId())) {
					//当前视图的历史版本，需要对时间处理
					String modifier = esDiagram.getModifier();
					CSysUser cSysUser = new CSysUser();
					cSysUser.setLoginCodeEqual(modifier);
					cSysUser.setDomainId(domainId);
					cSysUser.setSuperUserFlags(new Integer[]{0,1});
					List<SysUser> sysUsers = userApiSvc.getSysUserByCdt(cSysUser);
					SysUser sysUser = null;
					if (CollectionUtils.isNotEmpty(sysUsers)) {
						sysUser = sysUsers.get(0);
					}
					if (!BinaryUtils.isEmpty(sysUser)) {
						String creator = new StringBuilder(sysUser.getUserName()).append("[").append(sysUser.getLoginCode()).append("]").toString();
						version.setCreator(creator);
					}
					Long modifyTime = esDiagram.getModifyTime();
					if (version.getVersionTime() <= modifyTime) {
						version.setVersionTime(modifyTime + 1);
					}
					break;
				}
			}
		}
		diagramVersions.stream().sorted(Comparator.comparing(VcDiagramVersion::getVersionTime)).collect(Collectors.toList());
		List<DiagramVersionInfo> result = fillCreator(diagramVersions);
		fillPath(diagramVersions);
		//diagramVersions = diagramVersions.stream().sorted(Comparator.comparing(VcDiagramVersion::getVersionTime).reversed()).collect(Collectors.toList());
		return result;
	}

	private List<DiagramVersionInfo> fillCreator( List<VcDiagramVersion> diagramVersions) {
		Long userDomainId = SysUtil.getCurrentUserInfo().getDomainId();
		Set<String> creatorCodes = new HashSet<String>();
		List<DiagramVersionInfo> result = new ArrayList<DiagramVersionInfo>();
		for(VcDiagramVersion diagramVersion : diagramVersions){
			String creator = diagramVersion.getCreator();
			if(!BinaryUtils.isEmpty(creator)){
				String creatorCode = getOpCode(creator);
				creatorCodes.add(creatorCode);
			}
		}
		Map<String,SysUser> codeOpMap = new HashMap<>();
		if(!BinaryUtils.isEmpty(creatorCodes)){
			CSysUser cSysUser = new CSysUser();
			cSysUser.setLoginCodes(creatorCodes.toArray(new String[0]));
			cSysUser.setDomainId(userDomainId);
			cSysUser.setSuperUserFlags(new Integer[]{0,1});
			List<SysUser> sysUsers = userApiSvc.getSysUserByCdt(cSysUser);
			for(SysUser SysUser : sysUsers){
				StringBuilder sb = new StringBuilder();
				SysUser.clearSensitive();
				codeOpMap.put(SysUser.getLoginCode(), SysUser);
				String icon = SysUser.getIcon();
				sb.append(icon);
				if(!BinaryUtils.isEmpty(icon) && !BinaryUtils.isEmpty(rsmSlaveRoot)) {
					sb.setLength(0);
					sb.append(rsmSlaveRoot);
					sb.append(icon);
				}
				SysUser.setIcon(sb.toString());
			}
		}
		for(VcDiagramVersion diagramVersion : diagramVersions){
			DiagramVersionInfo dVersionInfo = new DiagramVersionInfo();
			dVersionInfo.setDiagramVersion(diagramVersion);
			String creatorCode = getOpCode(diagramVersion.getCreator());
			SysUser op = codeOpMap.get(creatorCode);
			dVersionInfo.setUser(op);
			result.add(dVersionInfo);
		}
		return result;
	}
	private String getOpCode(String str){
		if(BinaryUtils.isEmpty(str)) return null;
		int indexOf = str.lastIndexOf("[");
		if(indexOf != -1) {
			str = str.substring(indexOf+1, str.length()-1);
		}
		return str;
	}

	private void fillVersionDescResource(VcDiagramVersion diagramVersion){
		/*String versionDescPath = diagramVersion.getVersionDescPath();
		if(versionDescPath != null && versionDescPath.startsWith(httpResouceUrl)){
			versionDescPath = versionDescPath.substring(httpResouceUrl.length(), versionDescPath.length());
		}
		if(versionDescPath != null) {
			String resourceContent = eamDiagramSvc.getResourceContent(versionDescPath);
			diagramVersion.setVersionDesc(resourceContent);
		}*/
	}

	private void fillPath(List<VcDiagramVersion> diagramVersions){
		if (diagramVersions != null) {
			for (VcDiagramVersion vcDiagramVersion : diagramVersions) {
				if(vcDiagramVersion != null) {
					vcDiagramVersion.setCreator(subName(vcDiagramVersion.getCreator()));
					vcDiagramVersion.setModifier(subName(vcDiagramVersion.getModifier()));
				}
			}
		}
	}

	private String subName(String name){
		if(name != null){
			int lastIndexOf = name.lastIndexOf("[");
			if(lastIndexOf > 0){
				return	name.substring(0, lastIndexOf);
			}
		}
		return null;
	}

	/**
	 * <AUTHOR>
	 * @Description 判断用户是否具有当前视图的权限
	 * 1.不限制userId进行查询
	 *  查不出---视图被删除
	 *  查出来---判断视图的userId是否等于当前用户
	 * 		等于---有权限
	 * 		不等于---查询该用户是否被分享了该视图
	 * 			分享了，有两种权限，一种是查看，一种是编辑
	 * 			没分享，无权限，报错
	 **/
	private ESDiagram judgeSingleDiagramAuth(Long diagramId, String type, Boolean needAuth) {
		SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
		Long userId = currentUserInfo.getId();
		Long domainId = currentUserInfo.getDomainId();

		//1.不限制userId进行查询
		EamDiagramQuery diagramQuery = new EamDiagramQuery();
		diagramQuery.setId(diagramId);
		diagramQuery.setStatus(1);
		diagramQuery.setDataStatus(1);
		diagramQuery.setDomainId(domainId);
		if(!BinaryUtils.isEmpty(type) && "version".equals(type)) {
			diagramQuery.setHistoryVersionFlag(0);
		} else {
			diagramQuery.setHistoryVersionFlag(1);
		}
		List<ESDiagram> diagramList = esDiagramDao.getListByCdt(diagramQuery);
		//2.查不出，视图不存在，报错
		if(BinaryUtils.isEmpty(diagramList)) {
			throw new BinaryException("操作视图不存在，请核对视图当前信息");
		}
		//3.查出来，判断其是否具有视图权限
		ESDiagram esDiagram = diagramList.get(0);
		if (needAuth) {
			//模板不需要校验权限
			if (esDiagram.getDiagramType().equals(3)) {
				return esDiagram;
			}
			if (esDiagram.getUserId().equals(userId)) {
				//视图属于当前用户
				return esDiagram;
			} else {
				//视图不属于当前用户，判断该视图是否分享给了该用户
				Map<Long, Set<Long>> diagramIdShareRecordMap = eamShareDiagramSvc.queryDiagramSharedUserIds(new Long[]{diagramId});
				Set<Long> shareUserIdSet = diagramIdShareRecordMap.get(diagramId);
				if (!BinaryUtils.isEmpty(shareUserIdSet) && shareUserIdSet.contains(userId)) {
					//视图被分享给了当前用户
					return esDiagram;
				} else {
					throw new UnAuthorizedException("用户无当前视图权限");
				}
			}
		}
		return esDiagram;
	}
}
