package com.uino.monitor;

import com.alibaba.fastjson.JSON;
import com.binary.jdbc.Page;
import com.uino.StartBaseWebAppliaction;
import com.uino.api.client.permission.IUserApiSvc;
import com.uino.bean.monitor.buiness.PerformanceLabelQueryDto;
import com.uino.bean.permission.query.CAuthDataModuleBean;
import com.uino.dao.BaseConst;
import com.uino.monitor.performance.IUinoPerformanceSvc;
import com.uino.bean.monitor.buiness.PerformanceQueryDto;
import com.uino.bean.tp.base.FinalPerformanceDTO;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @Title: Performance
 * @Description:
 * @Author: YGQ
 * @Create: 2021-07-28 21:44
 **/
@RunWith(SpringRunner.class)
@WebAppConfiguration
@SpringBootTest(classes = {StartBaseWebAppliaction.class})
@ActiveProfiles("provider-local-dev")
@AutoConfigureMockMvc(addFilters = false)
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class PerformanceTest {

    @Autowired
    private IUserApiSvc iUserApiSvc;

    @Autowired
    private IUinoPerformanceSvc iUinoPerformanceSvc;

    @Test
    public void queryPerformanceLabelTest () {
        List<PerformanceLabelQueryDto> performanceLabelQueryDtoList = new ArrayList<>();
        PerformanceLabelQueryDto performanceLabelQueryDto = new PerformanceLabelQueryDto();
        performanceLabelQueryDto.setLabelKey("name");
        performanceLabelQueryDto.setLabelValue("1231");
        performanceLabelQueryDto.setOperationType(1);
        performanceLabelQueryDtoList.add(performanceLabelQueryDto);

        PerformanceLabelQueryDto performanceLabelQueryDto1 = new PerformanceLabelQueryDto();
        performanceLabelQueryDto1.setLabelKey("age");
        performanceLabelQueryDto1.setLabelValue("123");
        performanceLabelQueryDtoList.add(performanceLabelQueryDto1);

        PerformanceLabelQueryDto performanceLabelQueryDto2 = new PerformanceLabelQueryDto();
        performanceLabelQueryDto2.setLabelKey("age12");
        performanceLabelQueryDto2.setLabelValue("123");
        performanceLabelQueryDtoList.add(performanceLabelQueryDto2);

        PerformanceQueryDto dto = new PerformanceQueryDto();
        dto.setOrLabels(performanceLabelQueryDtoList);

        Page<FinalPerformanceDTO> queryPerformancePage = iUinoPerformanceSvc.queryPerformancePage(dto);
        System.out.println("----" + JSON.toJSONString(queryPerformancePage.getData().size()));
    }

    @Test
    public void test() {
        CAuthDataModuleBean cAuthDataModuleBean = new CAuthDataModuleBean();
        cAuthDataModuleBean.setUserId(1L);

        List<Long> dataValues = new ArrayList<>();
        dataValues.add(7826716008150028L);
        dataValues.add(7826716008150031L);
        cAuthDataModuleBean.setModuleIds(dataValues);
        Map<Long, Map<String, Integer>>  aa = iUserApiSvc.getDataPermissionByUser(cAuthDataModuleBean);
        System.out.println("");
    }

    @Test
    public void getCurrentPerformanceByCiCodes() {
        List<String> ciCodes = new ArrayList<>();
        ciCodes.add("7569010526650004");
        List<FinalPerformanceDTO> perfs = iUinoPerformanceSvc.getCurrentPerformanceByCiCodes(BaseConst.DEFAULT_DOMAIN_ID,ciCodes);
        System.out.println("----" + JSON.toJSONString(perfs));
    }

    @Test
    public void getCurrentPerformance() {
        List<String> ciCodes = new ArrayList<>();
        ciCodes.add("7569010526650004");

        List<String> kpiCodes = new ArrayList<>();
        kpiCodes.add("ygqkpi");
        List<FinalPerformanceDTO> perfs = iUinoPerformanceSvc.getCurrentPerformance(BaseConst.DEFAULT_DOMAIN_ID,ciCodes, kpiCodes);
        System.out.println("----" + JSON.toJSONString(perfs));
        System.out.println("----" + perfs.size());
    }

    @Test
    public void getPerformanceByConditional() {
        List<FinalPerformanceDTO> perfs = iUinoPerformanceSvc.getPerformanceByConditional(BaseConst.DEFAULT_DOMAIN_ID,"7487477246000115", "CPU使用率", null, null);
        System.out.println("----" + JSON.toJSONString(perfs));
        System.out.println("----" + perfs.size());
    }

    @Test
    public void queryPerformancePage() {
        PerformanceQueryDto dto = new PerformanceQueryDto();
        dto.setCiPrimaryKey("[\"ygq001\"]");
        dto.setCiCode("7487477246000115");
        dto.setLast(false);
        Page<FinalPerformanceDTO> queryPerformancePage = iUinoPerformanceSvc.queryPerformancePage(dto);
        System.out.println("----" + JSON.toJSONString(queryPerformancePage.getData().size()));
    }
}
