package com.uino.bean.cmdb.base;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.comm.model.ci.CcCiClass;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import lombok.Data;

import java.util.List;

/**
 * 元模型发布历史关联的ciInfo信息
 *
 * <AUTHOR>
 * @since 2025/4/14 11:33
 */
@Data
public class ClassInfoHistory {

    private Long id;

    private Long visualPublishVersionId;

    private Long classId;

    private String classCode;

    private CcCiClass ciClass;

    private List<CcCiAttrDef> attrDefs;
}
