package com.uino.service.cmdb.microservice.impl;

import java.util.List;

import com.uino.bean.permission.base.SysUser;
import com.uino.dao.BaseConst;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import com.uino.dao.cmdb.ESSysTopData;
import com.uino.service.cmdb.microservice.ITopDataSvc;
import com.uino.util.sys.SysUtil;
import com.uino.bean.cmdb.SysTopData;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
public class TopDataSvc implements ITopDataSvc {
	@Autowired
	private ESSysTopData esTopDataSvc;

	@Override
	public void unTop(Long domainId,Long topData, Long topDataType) {
		Assert.isTrue(topData != null && topDataType != null, "置顶数据与类型不得为空");
		domainId=domainId==null?BaseConst.DEFAULT_DOMAIN_ID:domainId;
		Long loginUserId = SysUtil.getCurrentUserInfo().getId();
		esTopDataSvc.deleteByQuery(QueryBuilders.boolQuery().must(QueryBuilders.termQuery("topDataId", topData))
				.must(QueryBuilders.termQuery("topDataType", topDataType))
				.must(QueryBuilders.termQuery("userId", loginUserId))
				.must(QueryBuilders.termQuery("domainId",domainId)), true);
	}

	@Override
	public void top(Long domainId,Long topData, Long topDataType) {
		Assert.isTrue(topData != null && topDataType != null, "置顶数据与类型不得为空");
		domainId=domainId==null? BaseConst.DEFAULT_DOMAIN_ID:domainId;
		Long loginUserId = SysUtil.getCurrentUserInfo().getId();
		unTop(domainId,topData, topDataType);
		SysTopData saveData = SysTopData.builder().userId(loginUserId).topDataType(topDataType).topDataId(topData).domainId(domainId)
				.build();
		esTopDataSvc.saveOrUpdate(saveData);
	}

	@Override
	public List<SysTopData> searTopData(Long topDataType) {
		SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
		BoolQueryBuilder query = QueryBuilders.boolQuery();
		query.must(QueryBuilders.termQuery("userId", currentUserInfo.getId()));
		query.must(QueryBuilders.termQuery("domainId",currentUserInfo.getDomainId()));
		if (topDataType != null) {
			query.must(QueryBuilders.termQuery("topDataType", topDataType));
		}
		// List<SysTopData> tops = esTopDataSvc.getListByQuery(query);
		List<SysTopData> tops = esTopDataSvc.getSortListByQuery(1, 5000, query, "createTime", false).getData();
		return tops;
	}

}
