package com.uino.plugin.client.init;

import com.uino.plugin.classloader.ClassloaderRepository;
import com.uino.plugin.classloader.PluginClassLoader;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * 重启服务，插件操作
 *
 * <AUTHOR>
 * @Date 2021/10/11
 * @Version 1.0
 */
@Component
@Slf4j
public class RebootLoadingPluginRunner implements ApplicationRunner {


    @Override
    public void run(ApplicationArguments args) throws Exception {
        loadPlugin();
    }

    public void loadPlugin() {
        String pluginPath = PluginClassLoader._LOADING_PLUGIN_PATH;
        log.info("插件目录：{}" , pluginPath);
        List<File> filesByPath = getJarFilesByPath(pluginPath);
        if (!CollectionUtils.isEmpty(filesByPath)) {
            for (File file : filesByPath) {
                try {
                    ClassloaderRepository.loadJar(file.getPath());
                    log.info("加载插件：{}" , file.getName());
                } catch (Throwable e) {
                    log.error("异常", e);
                    log.error("Jar包加载失败，路径为：{}", file.getPath());
                }
            }
        }
    }

    /**
     * 获取目录下所有可加载文件
     *
     * @param path
     * @return
     */
    public static List<File> getJarFilesByPath(String path) {
        List<File> jarFileList = new ArrayList<>();
        File f = new File(path);
        if (f.exists()) {
            File[] files = f.listFiles();
            if (files != null) {
                for (File file : files) {
//                    if (file.isDirectory()) {
//                        jarFileList.addAll(getJarFilesByPath(file.getPath()));
//                    }
                    if (file.getName().endsWith(".jar") && !file.getName().endsWith("sources.jar")) {
                        jarFileList.add(file);
                    }
                }
            }
        } else {
            f.mkdirs();
        }
        return jarFileList;
    }
}
