package com.uinnova.product.eam.model.dm;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.model.dm.bean.DataModelEntityNodeVo;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * 数据建模实体属性参数传递
 * <AUTHOR>
 */
@Data
public class DataModelAttributeParam {
    @Comment("实体属性名称-node节点Map")
    private Map<String, DataModelEntityNodeVo> nameMap = new HashMap<>();
    @Comment("实体属性继承标识-node节点Map")
    private Map<String, DataModelEntityNodeVo> inheritIdMap = new HashMap<>();
}
