package com.uinnova.product.eam.service.dmv;

import com.uinnova.product.eam.comm.model.VcBaseConfig;
import com.uinnova.product.eam.model.VcCiRltInfo;
import com.uinnova.product.eam.model.dmv.CiInfoResponse;
import com.uinnova.product.eam.model.dmv.Layer;
import com.uinnova.product.eam.model.dmv.VcCiRltQ;
import com.uinnova.product.eam.model.dmv.VcDiagramInfo;
import com.uino.bean.permission.base.SysUser;

import java.util.List;
import java.util.Map;

/**
 * @description: 全景应用墙
 * @author: Lc
 * @create: 2022-04-19 10:51
 */
public interface DmvAppWallService {

    /**
     * 全景应用墙
     * @param sysUser
     */
    List<CiInfoResponse> findAppWallList(SysUser sysUser);

    /**
     * 全景应用墙所有ci直接的关系查询
     * @param ids
     * @param ciCodes
     * @param classIds
     * @param types
     * @param ciRltQs
     */
    List<VcCiRltInfo> queryCiBetweenRlt(Long[] ids, String[] ciCodes, Long[] classIds, Integer[] types, VcCiRltQ[] ciRltQs);

    /**
     * 查询应用视图列表
     * @param esSysId
     * @return
     */
    List<VcDiagramInfo> findAppDiagramList(String esSysId, Integer type);

    /**
     * 新增应用排序配置
     * @param record
     * @return
     */
    Long saveAppSortConfig(VcBaseConfig record);

    /**
     * 查询应用关联层域的列表
     * @param sysUser
     * @return
     */
    List<Layer> findLayerDomainCiList(SysUser sysUser);

    Object getCardInfos(String ciCode, String cfgCode);
}
