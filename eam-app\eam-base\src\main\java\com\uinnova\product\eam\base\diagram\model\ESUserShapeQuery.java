package com.uinnova.product.eam.base.diagram.model;

import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.vmdb.comm.model.image.CcImage;
import lombok.Data;

/**
 * @Classname
 * @Description TODO
 * <AUTHOR>
 * @Date 2021-08-24-10:21
 */
@Data
@Comment("用户图形ES查询类")
public class ESUserShapeQuery extends CcImage implements EntityBean {

    @Comment("id")
    private Long id;

    @Comment("ids")
    private Long[] ids;

    @Comment("imgName")
    private String imgName;

    @Comment("imgNameEqual")
    private String imgNameEqual;

    @Comment("sorts")
    private String sorts;

    @Comment("userId")
    private Long userId;

    @Comment("shapeJson")
    private String shapeJson;

    @Comment("shapeJsonEqual")
    private String shapeJsonEqual;

    @Comment("dirIds")
    private Long[] dirIds;

    @Comment("dirId")
    private Long dirId;

    @Comment("createTime")
    private Long createTime;

    @Comment("modifyTime")
    private Long modifyTime;

}
