package com.uinnova.product.eam.model.vo;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.util.List;

@Data
public class AppSystemQueryConVo {

    public AppSystemQueryConVo(String title, List<AppSystemQueryConParamVo> conditions) {
        this.title = title;
        this.conditions = conditions;
    }

    @Comment("条件名称")
    private String title;

    @Comment("条件内容")
    private List<AppSystemQueryConParamVo> conditions;
}
