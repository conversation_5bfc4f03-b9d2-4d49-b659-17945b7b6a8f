package com.uinnova.product.eam.model.cj.enums;

import com.uinnova.product.eam.model.cj.vo.TableContentTypeVo;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

@Getter
public enum TableContentTypeEnum {

    STRING(1, "字符串", 1),
    INTEGER(2, "整数", 1),
    FLOAT(3, "小数", 1),
    TEXT(4, "文本", 1),
    ARTICLE(5, "文章", 1),
    PERCENTAGE(6, "百分比", 1),
    ENUM(7, "枚举", 1),
    DATE(8, "日期", 1),
    TIME(9, "时间段", 1),
    ASSET(11, "资产信息项", 1),
    BIND_ASSET(10, "绑定资产信息项", 1);

    private Integer type;

    private String desc;

    private Integer status;

    TableContentTypeEnum(Integer type, String desc, Integer status) {
        this.type = type;
        this.desc = desc;
        this.status = status;
    }

    private static List<TableContentTypeVo> typeList = null;

    static{
        typeList = new ArrayList<>();
        for (TableContentTypeEnum typeEnum : TableContentTypeEnum.values()){
            TableContentTypeVo type = new TableContentTypeVo();
            type.setType(typeEnum.getType());
            type.setDesc(typeEnum.getDesc());
            type.setStatus(typeEnum.getStatus());
            typeList.add(type);
        }
    }

    public static List<TableContentTypeVo> getTypeList() {
        return typeList;
    }
}
