package com.uinnova.product.vmdb.comm.expression.support;

import com.uinnova.product.vmdb.comm.exception.ExpressionException;
import com.uinnova.product.vmdb.comm.expression.Field;
import com.uinnova.product.vmdb.comm.expression.OP;

/**
 * 
 * <AUTHOR>
 *
 */
public class ArrayExpression<E> extends AbstractExpression<E> {
    private static final long serialVersionUID = 3724996193290465870L;

    private Field<E> field;
    private OP op;
    private Object[] values;

    public ArrayExpression(Field<E> field, OP op, String[] values) {
        if (field == null) {
            throw new ExpressionException(" the field is NULL argument! ");
        }
        if (op == null) {
            throw new ExpressionException(" the op is NULL argument! ");
        }
        if (values == null || values.length == 0) {
            throw new ExpressionException(" the values is NULL argument! ");
        }
        if (op != OP.In && op != OP.NotIn) {
            throw new ExpressionException(" the op:'" + op + "' is not support operate multiple-values! ");
        }

        this.field = field;
        this.op = op;
        this.values = values;
    }

    @Override
    protected String buildSqlFragment() {
        StringBuilder buff = new StringBuilder();
        buff.append(this.field.getName()).append(this.op.getOperator()).append("(");

        for (int i = 0; i < this.values.length; i++) {
            if (this.values[i] == null) {
                throw new ExpressionException(" the " + this.op + " values[" + i + "] is NULL! ");
            }
            if (i > 0) {
                buff.append(", ");
            }
            buff.append('\'').append(this.values[i]).append('\'');
        }
        buff.append(")");
        return buff.toString();
    }

}
