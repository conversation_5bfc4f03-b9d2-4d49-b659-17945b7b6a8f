package com.uinnova.product.eam.service.es;

import com.uinnova.product.eam.comm.model.es.ModelPanorama3D;
import com.uino.dao.AbstractESBaseDao;
import jakarta.annotation.PostConstruct;
import org.springframework.stereotype.Service;


/**
 * 元模型3D全景配置Dao
 * <AUTHOR>
 */
@Service
public class ModelPanorama3DDao extends AbstractESBaseDao<ModelPanorama3D, ModelPanorama3D> {
    @Override
    public String getIndex() {
        return "uino_eam_model_panorama_3d";
    }

    @Override
    public String getType() {
        return "_doc";
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }
}
