package com.uino.dao;

/**
 * ES共用变量
 * 
 * <AUTHOR>
 */
public class ESConst {

    /**
     * 关系遍历规则
     */
    public static final String INDEX_CMDB_RLT_RULE = "uino_cmdb_rlt_rule";

    /**
     * 分类目录
     */
    public static final String INDEX_CMDB_DIR = "uino_cmdb_dir";

    /**
     * CI类
     */
    public static final String INDEX_CMDB_CICLASS = "uino_cmdb_ciclass";

    /**
     * CI类
     */
    public static final String INDEX_CMDB_CICLASS_ENCODE = "uino_cmdb_ciclass_encode";

    /**
     * CI当前
     */
    public static final String INDEX_CMDB_CI = "uino_cmdb_ci";

    /**
     * CI当前私有库
     */
    public static final String INDEX_CMDB_CI_PRIVATE = "uino_cmdb_ci_private";

    /**
     * CI当前设计库
     */
    public static final String INDEX_CMDB_CI_DESIGN = "uino_cmdb_ci_design";

    /**
     * CI历史
     */
    public static final String INDEX_CMDB_CI_HISTORY = "uino_cmdb_ci_history";

    /**
     * CI历史
     */
    public static final String INDEX_CMDB_CI_HISTORY_PRIVATE = "uino_cmdb_ci_history_private";

    /**
     * CI历史
     */
    public static final String INDEX_CMDB_CI_HISTORY_DESIGN = "uino_cmdb_ci_history_design";

    /**
     * 关系定义
     */
    public static final String INDEX_CMDB_RLTCLASS = "uino_cmdb_rltclass";

    /**
     * 关系对象
     */
    public static final String INDEX_CMDB_RLT = "uino_cmdb_rlt";

    /**
     * 关系对象私有库
     */
    public static final String INDEX_CMDB_RLT_PRIVATE = "uino_cmdb_rlt_private";

    /**
     * 关系对象设计库
     */
    public static final String INDEX_CMDB_RLT_DESIGN = "uino_cmdb_rlt_design";

    /**
     * 可视化模型
     */
    public static final String INDEX_CMDB_VISUALMODEL = "uino_cmdb_visualmodel";

    /**
     * 可视化建模中CI分类间的关系分类约束
     */
    public static final String INDEX_CMDB_CICLASSRLT = "uino_cmdb_ciclassrlt";

    /**
     * 可视化模型中的谁影响我
     */
    public static final String INDEX_CMDB_IMPACTPATH = "uino_cmdb_impactpath";

    /**
     * 标签对象
     */
    public static final String INDEX_CMDB_TAG = "uino_cmdb_tag";

    /**
     * ci树配置
     */
    public static final String INDEX_CMDB_CI_TREE_CONFIG = "uino_cmdb_ci_tree_config";

    /**
     * oauth认证使用的client信息
     */
    public static final String INDEX_OAUTH_CLIENT = "uino_oauth_client";

    /**
     * oauth认证使用的token信息
     */
    public static final String INDEX_OAUTH_TOKEN = "uino_oauth_token";

    /**
     * oauth认证使用的刷新token信息
     */
    public static final String INDEX_OAUTH_REFRESHTOKEN = "uino_oauth_refreshtoken";

    /**
     * oauth认证使用的
     */
    public static final String INDEX_OAUTH_RESOURCE = "uino_oauth_resource";

    /**
     * 角色表
     */
    public static final String INDEX_SYS_ROLE = "uino_sys_role";

    /**
     * 模块
     */
    public static final String INDEX_SYS_MODULE = "uino_sys_module";

    /**
     * 用户
     */
    public static final String INDEX_SYS_USER = "uino_sys_user";

    /**
     * 组织
     */
    public static final String INDEX_SYS_ORG = "uino_sys_org";

    /**
     * 数据模块
     */
    public static final String INDEX_SYS_DATA_MODULE = "uino_sys_data_module";

    /**
     * 用户-角色关系
     */
    public static final String INDEX_SYS_USER_ROLE_RLT = "uino_sys_user_role_rlt";

    /**
     * 角色-模块关系
     */
    public static final String INDEX_SYS_ROLE_MODULE_RLT = "uino_sys_role_module_rlt";

    /**
     * 用户与组织关系
     */
    public static final String INDEX_SYS_USER_ORG_RLT = "uino_sys_user_org_rlt";

    /**
     * 组织与角色关系SYS_ORG_ROLE
     */
    public static final String INDEX_SYS_ORG_ROLE_RLT = "uino_sys_org_role_rlt";

    /**
     * 用户与功能模块
     */
    public static final String INDEX_SYS_USER_MODULE_RLT = "uino_sys_user_module_rlt";

    /**
     * 用户与数据项关系
     */
    public static final String INDEX_SYS_USER_DATAMODULE_RLT = "uino_sys_user_datamodule_rlt";

    /**
     * 角色与数据项关系
     */
    public static final String INDEX_SYS_ROLE_DATAMODULE_RLT = "uino_sys_role_datamodule_rlt";

    /**
     * 图标
     */
    public static final String INDEX_CMDB_IMAGE = "uino_cmdb_image";

    /**
     * 性能
     */
    @Deprecated
    public static final String INDEX_PERFORMANCE = "uino_monitor_performance";

    /**
     * 模拟告警推送记录
     * 告警
     */
    public static final String INDEX_SIMULATION_RECORD_EVENT = "uino_monitor_simulation_record_event";

    /**
     * 告警级别定义
     */
    public static final String INDEX_EVENT_SERVERITY = "uino_monitor_event_severity";

    /**
     * kpi指标
     */
    public static final String INDEX_KPI = "uino_monitor_kpi";

    /**
     * kpi对象组
     */
    public static final String INDEX_KPI_GROUP = "uino_monitor_kpi_group";

    /**
     * 用户与菜单收藏关系
     */
    public static final String INDEX_SYS_USER_MODULE_ENSHRINE_RLT = "uino_sys_user_module_enshrine_rlt";

    /**
     * 置顶数据
     */
    public static final String INDEX_SYS_TOP_DATA = "uino_sys_top";

    /**
     * 资源
     */
    public static final String INDEX_RESOURCE = "uino_resource";

    /**
     * 已同步资源索引位置
     */
    public static final String INDEX_SYNC_RESOURCEINDEX = "uino_sync_resourceindex";

    /**
     * logo配置
     */
    public static final String INDEX_SYS_LOGO = "uino_sys_logo";

    /**
     * 对象管理-对象数据用户配置
     */
    public static final String INDEX_TABLE_DATA_CONFIG = "uino_table_data_config";

    /**
     * 登陆日志
     */
    public static final String INDEX_SYS_LOGIN_LOG = "uino_sys_login_log";

    /**
     * 通知渠道
     */
    public static final String INDEX_SYS_NOTIFY_CHANNEL = "uino_sys_notify_channel";

    /**
     * 日志管理-配置日志
     */
    public static final String INDEX_CI_OPERATE_LOG = "uino_ci_operate_log";

    /**
     * 日志管理-配置日志
     */
    public static final String INDEX_OPERATE_LOG_MODULE = "uino_operate_log_module";

    /**
     * 日志管理-系统日志
     */
    public static final String INDEX_OPERATE_LOG = "uino_operate_log";

    /**
     * 授权码
     */
    public static final String INDEX_LICENSE_AUTH = "uino_license_auth";

    /**
     * 授权服务器配置
     */
    public static final String INDEX_LICENSE_AUTH_SERVER = "uino_license_auth_server";

    /**
     * 属性定义中间表
     */
    public static final String INDEX_CMDB_ATTR_TRANS_CONFIG = "uino_cmdb_attr_trans_config";

    /*
     * 登录集成方式配置
     * 
     * <AUTHOR>
     * 
     */
    public static final String INDEX_SYS_LOGIN_AUTH_INTEGRATION = "uino_sys_login_auth_integration";

    /**
     * ci关系历史版本
     */
    public static final String INDEX_CMDB_CIRLT_HIS = "uino_cmdb_ci_rlt_history";

    /**
     * 分布式锁
     */
    public static final String INDEX_CMDB_DATASET_TASK_LOCK = "uino_cmdb_dataset_task_lock";

    /**
     * 关系自动构建
     */
    public static final String INDEX_CMDB_CIRLT_AUTO_BUILD = "uino_cmdb_cirlt_auto_build";

    /**
     * 数据集协作权限
     */
    public static final String INDEX_CMDB_DATASET_COORDINATION = "uino_cmdb_dataset_coordination";

    /**
     * 关系遍历路径结果集
     */
    public static final String INDEX_CMDB_DATASET_EXE_RESULT_SHEET = "uino_cmdb_dataset_exe_result_sheet";

    /**
     * 关系遍历查询总结果（FriendInfo）
     */
    public static final String INDEX_CMDB_DATASET_EXE_RESULT = "uino_cmdb_dataset_exe_result";

    /**
     * 数据超市用户请求log
     */
    public static final String INDEX_CMDB_DATASET_MALLAPI_LOG = "uino_cmdb_dataset_mallapi_log";

    /**
     * 数据超市规则
     */
    public static final String INDEX_CMDB_DATASET = "uino_cmdb_dataset";

    /**
     * 数据超市优先显示
     */
    public static final String INDEX_CMDB_DATASET_PRIORITY_DISPLAY = "uino_cmdb_dataset_priority_display";

    /**
     * 数据超市置顶
     */
    public static final String INDEX_CMDB_DATASET_TOP = "uino_cmdb_dataset_top";

    /**
     * 历史性能数据索引前缀
     */
    public static final String INDEX_METRIC_DATA_PREFIX = "performance_";

    /**
     * 实时性能数据索引
     */
    public static final String INDEX_LAST_METRIC_DATA = "performance";

    /**
     * 无ep时 直接入库的告警
     */
    public static final String INDEX_MONITOR_MON_EAP_EVENT = "event";

    /**
     * ep 按时间分 全部告警 索引 前缀
     */
    public static final String INDEX_EP_EVENT_HISTORY_PREFIX = "mon_eap_event_all_";

    /**
     * 字典表定义
     */
    public static final String INDEX_SYS_DICTIONARY_CLASS = "uino_sys_dictionary_class";

    /**
     * 字典项
     */
    public static final String INDEX_SYS_DICTIONARY_ITEM = "uino_sys_dictionary_item";

    /**
     * tp 规则
     */
    public static final String INDEX_TP_RULE = "uino_tp_rule";


	/**
	 * tp 格式错误的性能数据
	 */
	@Deprecated
	public static final String ERROR_METRIC_DATA = "uino_tp_error_metric_data";

	/**
	 * tp 指标标签
	 */
	public static final String INDEX_TP_METRIC_LABEL = "uino_tp_metric_label";

    /**
     * 孪生服务（原数据超市）信息
     */
    public static final String INDEX_DIGITAL_TWIN_SERVICE_PORTAL = "uino_digital_twin_service_portal";

    /**
     * 任务执行记录
     */
    public static final String INDEX_TASK_EXECUTE_RECORD = "uino_task_execute_record";

	public static final String INDEX_UINO_SIMULATION_RULE = "uino_simulation_rule";

    /**
     * ap 规则
     */
    public static final String INDEX_AP_RULE = "uino_ap_rule";

    /**
     * ap 计算结果
     */
    public static final String INDEX_AP_EXECUTE_RESULT = "uino_ap_execute_result";

    /**
     * 插件管理
     */
    public static final String INDEX_PLUGIN = "uino_plugin";
    /**
     * 租户域管理
     */
    public static final String TENANT_DOMAIN = "uino_tenant_domain";

}
