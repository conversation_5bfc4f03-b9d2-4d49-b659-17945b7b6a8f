package com.uinnova.product.eam.comm.bean;

import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import lombok.Data;

import java.util.List;

/**
 * 分类属性配置展示
 * <AUTHOR>
 */
@Data
public class CcCiClassInfoConfVO extends CcCiClassInfo {

    private List<CcCiAttrDef> showListCIAttrInfo;

    private List<CcCiAttrDefConfVO> tagListCIAttrInfo;

    private List<CcCiAttrDefTapGroupConfVO> showDetailCIAttrInfo;
}
