package com.uinnova.product.eam.service;

import com.uinnova.product.eam.model.BusinessConfigResponseVo;
import com.uinnova.product.eam.model.BusinessConfigVo;
import org.springframework.http.ResponseEntity;

import java.util.List;

public interface BusinessConfigSvc {
    /**
     * 创建功能配置json文件
     * @param businessConfigVo
     * @return
     */
    ResponseEntity<byte[]> exportConfig(BusinessConfigVo businessConfigVo);

    /**
     * 获取配置接口
     */
    List<BusinessConfigResponseVo> getBusinessConfigList();
}
