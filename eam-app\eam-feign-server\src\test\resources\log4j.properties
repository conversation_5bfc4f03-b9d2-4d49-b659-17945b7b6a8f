### set log levels ###
log4j.rootLogger = INFO,stdout,log 

### 输出到控制台 ###
log4j.appender.stdout = org.apache.log4j.ConsoleAppender  
log4j.appender.stdout.Target = System.out
log4j.appender.stdout.layout = org.apache.log4j.PatternLayout  
log4j.appender.stdout.layout.ConversionPattern =  %d{ABSOLUTE} %5p %c{1}:%L - %m%n  

### 输出到日志文件 ###
## 输出DEBUG级别以上的日志
log4j.appender.log.Threshold = INFO
log4j.appender.log=org.apache.log4j.RollingFileAppender
log4j.appender.log.Encoding=UTF-8
log4j.appender.log.MaxFileSize=20MB
log4j.appender.log.MaxBackupIndex=10
log4j.appender.log.layout=org.apache.log4j.PatternLayout
log4j.appender.log.layout.ConversionPattern=%-d{yyyy-MM-dd HH:mm:ss}  [ %t:%r ] - [ %p ]  %m%n
log4j.appender.log.File=./logs/project.log

## 对spring配置
log4j.logger.org.springframework=INFO