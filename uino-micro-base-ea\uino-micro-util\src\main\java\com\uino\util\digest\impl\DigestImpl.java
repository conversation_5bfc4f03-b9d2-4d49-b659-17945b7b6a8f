package com.uino.util.digest.impl;

import com.uino.util.digest.Digest;
import com.uino.util.digest.DigestType;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.stereotype.Service;

/**
 * @Title: DigestImpl
 * @Author: YGQ
 * @Create: 2021-08-09 11:01
 **/
@Service
public class DigestImpl implements Digest {
    @Override
    public String calculate(DigestType type, String str) {
        String ret = null;
        switch (type) {
            case MD2:
                ret = DigestUtils.md2Hex(str);
                break;
            case MD5:
                ret = DigestUtils.md5Hex(str);
                break;
            case SHA1:
                ret = DigestUtils.sha1Hex(str);
                break;
            case SHA256:
                ret = DigestUtils.sha256Hex(str);
                break;
            case SHA384:
                ret = DigestUtils.sha384Hex(str);
                break;
            case SHA512:
                ret = DigestUtils.sha512Hex(str);
                break;
            default:
                break;
        }
        return ret;
    }
}
