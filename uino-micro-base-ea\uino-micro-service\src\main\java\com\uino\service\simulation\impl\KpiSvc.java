package com.uino.service.simulation.impl;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import com.uino.dao.BaseConst;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.binary.core.io.Resource;
import com.binary.core.util.BinaryUtils;
import com.binary.jdbc.Page;
import com.uino.bean.cmdb.business.ImportResultMessage;
import com.uino.bean.cmdb.business.ImportRowMessage;
import com.uino.bean.cmdb.business.ImportSheetMessage;
import com.uino.bean.cmdb.query.ESAttrBean;
import com.uino.bean.monitor.base.ESKpiInfo;
import com.uino.bean.monitor.buiness.KpiRltBindDto;
import com.uino.bean.monitor.buiness.SearchKpiBean;
import com.uino.bean.monitor.enums.KpiTypeEnum;
import com.uino.bean.sys.base.ESDictionaryAttrDef;
import com.uino.bean.sys.base.ESDictionaryClassInfo;
import com.uino.bean.sys.base.ESDictionaryItemInfo;
import com.uino.bean.sys.query.ESDictionaryItemSearchBean;
import com.uino.service.simulation.IKpiSvc;
import com.uino.service.sys.microservice.impl.DictionarySvc;
import com.uino.service.util.FileUtil;
import com.uino.util.excel.EasyExcelUtil;
import com.uino.util.excel.EasyExcelUtil.SheetData;

import lombok.extern.slf4j.Slf4j;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
@Slf4j
public class KpiSvc implements IKpiSvc {

	@Autowired
	private DictionarySvc dictSvc;

	@Override
	public ESKpiInfo getKpiInfoById(Long id) {
		ESDictionaryItemInfo item = dictSvc.getDictItemInfoById(id);
		if (item != null) {
			List<ESKpiInfo> kpiInfos = this.transDictItemToKpiInfo(Collections.singletonList(item));
			return kpiInfos.get(0);
		}
		return null;
	}
	
    @Override
    public List<ESKpiInfo> getKpiInfoByIds(Collection<Long> ids) {
        List<ESDictionaryItemInfo> kpis = dictSvc.getDictItemInfoByIds(ids);
        return this.transDictItemToKpiInfo(kpis);
    }
	
	@Override
	public Page<ESKpiInfo> queryKpiInfoPage(SearchKpiBean searchDto) {
		if (searchDto.getDomainId() == null) {
			searchDto.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
		}
		ESDictionaryItemSearchBean bean = ESDictionaryItemSearchBean.builder().dictCode("kpiManager")
				.keyword(searchDto.getSearchVal()).sortField(searchDto.getSortField()).isAsc(searchDto.getIsAsc())
				.build();
		bean.setDomainId(searchDto.getDomainId());
		bean.setPageNum(searchDto.getPageNum());
		bean.setPageSize(searchDto.getPageSize());
		List<ESAttrBean> andAttrs = new ArrayList<>();
		if (searchDto.getClassId() != null) {
			ESAttrBean attrBean = new ESAttrBean();
			attrBean.setKey("classIds");
			attrBean.setValue(searchDto.getClassId());
			attrBean.setOptType(2);
			andAttrs.add(attrBean);
		}
		if (!BinaryUtils.isEmpty(searchDto.getServiceId())) {
			ESAttrBean attrBean = new ESAttrBean();
			attrBean.setKey("serviceId");
			attrBean.setValue(searchDto.getServiceId());
			attrBean.setOptType(1);
			andAttrs.add(attrBean);
		}
		if (!BinaryUtils.isEmpty(andAttrs)) {
			bean.setAndAttrs(andAttrs);
		}
		List<ESAttrBean> orAttrs = new ArrayList<>();
		if (!BinaryUtils.isEmpty(searchDto.getKpiNames())) {
			for (String kpiName : searchDto.getKpiNames()) {
				ESAttrBean attrBean = new ESAttrBean();
				attrBean.setKey("kpiCode");
				attrBean.setValue(kpiName);
				attrBean.setOptType(1);
				orAttrs.add(attrBean);
			}
		}
		if (!BinaryUtils.isEmpty(searchDto.getSearchVal())) {
			ESAttrBean attrBean = new ESAttrBean();
			attrBean.setKey("kpiCode");
			attrBean.setValue(searchDto.getSearchVal());
			attrBean.setOptType(2);
			orAttrs.add(attrBean);
			ESAttrBean attrBean2 = new ESAttrBean();
			attrBean2.setKey("serviceId");
			attrBean2.setValue(searchDto.getSearchVal());
			attrBean2.setOptType(2);
			orAttrs.add(attrBean2);
		}
		//新增DMV使用通过code或者name查询
		if (!BinaryUtils.isEmpty(searchDto.getSearchCodeOrName())) {
		    ESAttrBean attrBean = new ESAttrBean();
		    attrBean.setKey("kpiCode");
		    attrBean.setValue(searchDto.getSearchCodeOrName());
		    attrBean.setOptType(2);
		    orAttrs.add(attrBean);
		    ESAttrBean attrBean2 = new ESAttrBean();
		    attrBean2.setKey("kpiName");
		    attrBean2.setValue(searchDto.getSearchCodeOrName());
		    attrBean2.setOptType(2);
		    orAttrs.add(attrBean2);
		}
		
		
		if (!BinaryUtils.isEmpty(orAttrs)) {
			bean.setOrAttrs(orAttrs);
		}
		Page<ESDictionaryItemInfo> itemPage = dictSvc.searchDictItemPageByBean(bean);
		List<ESKpiInfo> kpiInfos = this.transDictItemToKpiInfo(itemPage.getData());
		return new Page<>(itemPage.getPageNum(), itemPage.getPageSize(), itemPage.getTotalRows(),
				itemPage.getTotalPages(), kpiInfos);
	}

	@Override
	public Long saveOrUpdate(ESKpiInfo saveDto) {
		if (saveDto.getDomainId() == null) {
			saveDto.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
		}
		// 约束条件格式判断
		if ("3".equals(saveDto.getKpiType())) {
			String constraint = saveDto.getConstraint();
			try {
				String[] split = constraint.split(";");
				for (String s : split) {
					if (!s.contains("-")) {
						throw new RuntimeException("约束条件格式异常");
					}
				}
			} catch (Exception e) {
				throw new RuntimeException("约束条件格式异常");
			}
		}
		Assert.isTrue(
				BinaryUtils.isEmpty(saveDto.getKpiCode())
						|| saveDto.getKpiCode().matches("[^\\:\\/\\?\\*\\[\\]\\\\]{1,30}"),
				"指标名称不可超过30位，且不可包含:/\\?*[]等字符");
		List<ESDictionaryItemInfo> items = this.transKpiInfoToDictItem(Collections.singletonList(saveDto));
		return dictSvc.saveOrUpdateDictionaryItem(items.get(0));
	}

	@Override
	public void deleteByKpiIds(Collection<Long> kpiIds) {
		dictSvc.deleteItemByIds(kpiIds);
	}

	@Override
	public Resource exportKpiInfos(Long domainId, Boolean isTpl) {
		List<ESDictionaryClassInfo> dictClassInfos = dictSvc
				.queryDcitClassInfosByBean(ESDictionaryItemSearchBean.builder().domainId(domainId).dictCode("kpiManager").build());
		Assert.isTrue(!BinaryUtils.isEmpty(dictClassInfos), "指标字典不存在");
		ESDictionaryClassInfo dictClassInfo = dictClassInfos.get(0);
		List<ESDictionaryAttrDef> dictAttrDefs = dictClassInfo.getDictAttrDefs();
		List<String> titles = new ArrayList<>();
		Set<String> reqCellValues = new HashSet<>();
		List<String> fileDefNames = new ArrayList<>();
		dictAttrDefs.forEach(def -> {
			if (def.getDisplay()) {
				titles.add(def.getProName());
			}
			if (def.getIsRequired() == 1) {
				reqCellValues.add(def.getProName());
			}
			if (def.getProType() == ESDictionaryAttrDef.DictProTypeEnum.FILE.getType()) {
				fileDefNames.add(def.getProName());
			}
		});
		List<Map<String, String>> datas = new ArrayList<Map<String, String>>();
		if (!isTpl) {
			ESDictionaryItemSearchBean bean = ESDictionaryItemSearchBean.builder().domainId(domainId).dictClassId(dictClassInfo.getId())
					.build();
			List<ESDictionaryItemInfo> items = dictSvc.searchDictItemListByBean(bean);
			// 转换引用值
			dictSvc.transExterDcitIdToName(items, dictClassInfo);
			for (ESDictionaryItemInfo item : items) {
				Map<String, String> attrs = item.getAttrs();
				// 导出时文件属性清空
				for (String filePro : fileDefNames) {
					attrs.put(filePro, null);
				}
				// 替换指标类型为中文
				KpiTypeEnum kpiTypeEnum = KpiTypeEnum.valueOfCode(attrs.get("kpiType"));
				attrs.put("kpiType", kpiTypeEnum.getName());
				Map<String, String> stdMap = dictSvc.transToStdMap(attrs, dictClassInfo, true);
				datas.add(stdMap);
			}
		}
		return dictSvc.writeDataToExcelFile(dictClassInfo.getDictName(), titles, reqCellValues, datas);

	}

	@Override
	public ImportResultMessage importKpiInfos(Long domainId, MultipartFile file) {
		// 获取字典定义
		List<ESDictionaryClassInfo> dictClassInfos = dictSvc
				.queryDcitClassInfosByBean(ESDictionaryItemSearchBean.builder().domainId(domainId).dictCode("kpiManager").build());
		Assert.isTrue(!BinaryUtils.isEmpty(dictClassInfos), "指标字典不存在");
		ESDictionaryClassInfo dictClassInfo = dictClassInfos.get(0);
		List<String> fileProNames = dictClassInfo.getDictAttrDefs().stream()
				.filter(def -> def.getProType() == ESDictionaryAttrDef.DictProTypeEnum.FILE.getType())
				.map(ESDictionaryAttrDef::getProName).collect(Collectors.toList());

		InputStream excelIs = null;
		// 读取文件组装数据
		List<ESDictionaryItemInfo> items = new ArrayList<>();
		List<ImportRowMessage> rowMessages = new ArrayList<>();
		ImportSheetMessage sheetMessage = ImportSheetMessage.builder().className(dictClassInfo.getDictName())
				.sheetName(dictClassInfo.getDictName()).rowMessages(rowMessages).build();
		try {
			excelIs = file.getInputStream();
			SheetData sheetData = EasyExcelUtil.readSheet(dictClassInfo.getDictName(), null, excelIs);
			Assert.isTrue(!BinaryUtils.isEmpty(sheetData.getTitles()), "字典数据不存在");
			List<String> titles = sheetData.getTitles();
			Map<String, List<String>> attrMark = FileUtil.ExcelUtil.getAttrMarkByTitles(titles);
			titles = attrMark.get("titles");

			List<String[]> rows = sheetData.getRows();
			sheetMessage.setTotalNum(rows.size());
			// 按行组装数据
			int rowNum = 1;
			Iterator<String[]> it = rows.iterator();
			while (it.hasNext()) {
				rowNum++;
				String[] row = it.next();
				Map<String, String> map = new HashMap<String, String>();
				for (int j = 0; j < titles.size(); j++) {
					String val = row[j] == null ? row[j] : row[j].trim();
					// 文件属性不支持导入，清空属性值
					if (fileProNames.contains(titles.get(j))) {
						val = null;
					}
					map.put(titles.get(j), val);
				}
				/**
				 * 转换为标准属性值
				 */
				Map<String, String> stdMap = dictSvc.transToStdMap(map, dictClassInfo, false);
				// 替换指标类型为类型值
				KpiTypeEnum kpiTypeEnum = KpiTypeEnum.valueOfCode(stdMap.get("kpiType"));
				stdMap.put("kpiType", String.valueOf(kpiTypeEnum.getType()));
				// 校验指标名称长度
				String kpiCode = stdMap.get("kpiCode");
				if (!BinaryUtils.isEmpty(kpiCode) && !kpiCode.matches("[^\\:\\/\\?\\*\\[\\]\\\\]{1,30}")) {
					ImportRowMessage rowMessage = dictSvc.buildRowMessage(rowNum, null, "指标名称不可超过30位，且不可包含:/\\?*[]等字符");
					rowMessages.add(rowMessage);
					sheetMessage.setFailNum(sheetMessage.getFailNum() + 1);
					it.remove();
					continue;
				}
				String collectionCycleUnit = stdMap.get("collectionCycleUnit");
				if(collectionCycleUnit != null){
					boolean flag = collectionCycleUnit.equals("分") || collectionCycleUnit.equals("秒");
					if(!flag){
						ImportRowMessage rowMessage = dictSvc.buildRowMessage(rowNum, null, "采集频率单位不支持["+collectionCycleUnit+"],只支持分或秒");
						rowMessages.add(rowMessage);
						sheetMessage.setFailNum(sheetMessage.getFailNum() + 1);
						it.remove();
						continue;
					}
				}
				// 构建字典项
				ESDictionaryItemInfo itemInfo = new ESDictionaryItemInfo();
				itemInfo.setDomainId(domainId);
				itemInfo.setDictClassId(dictClassInfo.getId());
				itemInfo.setAttrs(stdMap);
				itemInfo.setIndex(rowNum);
				items.add(itemInfo);
			}
		} catch (IOException e1) {
			log.error("读取excel流异常");
		} finally {
			if (excelIs != null) {
				try {
					excelIs.close();
				} catch (IOException e) {
					log.error("关闭excel流异常");
				}
			}
		}
		return dictSvc.importDictItemsBatch(dictClassInfo, items, sheetMessage);
	}

	private List<ESKpiInfo> transDictItemToKpiInfo(List<ESDictionaryItemInfo> items) {
		List<ESKpiInfo> kpiInfos = new ArrayList<>();
		if (!BinaryUtils.isEmpty(items)) {
			for (ESDictionaryItemInfo item : items) {
				Map<String, String> attrs = item.getAttrs();
				ESKpiInfo kpiInfo = new ESKpiInfo();
				kpiInfo.setId(item.getId());
				kpiInfo.setKpiCode(attrs.get("kpiCode"));
				kpiInfo.setKpiName(attrs.get("kpiName"));
				kpiInfo.setKpiType(attrs.get("kpiType"));
				kpiInfo.setConstraint(attrs.get("constraint"));
				kpiInfo.setMonitorSystem(attrs.get("monitorSystem"));
				kpiInfo.setUnitName(attrs.get("unitName"));
				kpiInfo.setCollectionCycle(attrs.get("collectionCycle"));
				kpiInfo.setCollectionCycleUnit(attrs.get("collectionCycleUnit"));
				kpiInfo.setServiceId(attrs.get("serviceId"));
				String classIdStr = attrs.get("classIds");
				try {
					kpiInfo.setClassIds(JSONArray.parseArray(classIdStr, Long.class));
				} catch (Exception e) {
					log.info("指标关联分类id转换失败：" + classIdStr);
				}
				kpiInfo.setKpiDesc(attrs.get("kpiDesc"));
				kpiInfo.setExpCondition(attrs.get("expCondition"));
				kpiInfo.setValExp(attrs.get("valExp"));
				kpiInfo.setSearchValue(attrs.get("searchValue"));
				kpiInfo.setKpiExtPros(attrs.get("kpiExtPros"));
				kpiInfo.setKpiCategory(attrs.get("kpiCategory"));
				String uinitConvertModelId = attrs.get("uinitConvertModelId");
				if (!BinaryUtils.isEmpty(uinitConvertModelId)) {
					kpiInfo.setUinitConvertModelId(Long.parseLong(uinitConvertModelId));
				}
				String valType = attrs.get("kpiType");
				if (!BinaryUtils.isEmpty(valType)) {
					kpiInfo.setValType(Integer.parseInt(valType));
				}
				kpiInfo.setOption(item.getOption());
				kpiInfo.setCreator(item.getCreator());
				kpiInfo.setModifier(item.getModifier());
				kpiInfo.setCreateTime(item.getCreateTime());
				kpiInfo.setModifyTime(item.getModifyTime());
				kpiInfo.setDomainId(item.getDomainId());
				kpiInfos.add(kpiInfo);
			}
		}
		return kpiInfos;
	}

	private List<ESDictionaryItemInfo> transKpiInfoToDictItem(List<ESKpiInfo> kpiInfos) {
		List<ESDictionaryItemInfo> itemInfos = new ArrayList<>();
		if (!BinaryUtils.isEmpty(kpiInfos)) {
			for (ESKpiInfo kpiInfo : kpiInfos) {
				ESDictionaryItemInfo item = new ESDictionaryItemInfo();
				item.setDictClassId(kpiInfo.getDictClassId());
				item.setId(kpiInfo.getId());
				if (BinaryUtils.isEmpty(kpiInfo.getKpiName())) {
					kpiInfo.setKpiName(kpiInfo.getKpiCode());
				}
				Map<String, String> attrs = new HashMap<String, String>();
				attrs.put("kpiCode", kpiInfo.getKpiCode());
				attrs.put("kpiName", kpiInfo.getKpiName());
				attrs.put("kpiType", kpiInfo.getKpiType());
				attrs.put("constraint", kpiInfo.getConstraint());
				attrs.put("monitorSystem", kpiInfo.getMonitorSystem());
				attrs.put("unitName", kpiInfo.getUnitName());
				attrs.put("collectionCycle", kpiInfo.getCollectionCycle());
				attrs.put("collectionCycleUnit", kpiInfo.getCollectionCycleUnit());
				attrs.put("classIds", JSON.toJSONString(kpiInfo.getClassIds()));
				attrs.put("kpiDesc", kpiInfo.getKpiDesc());
				attrs.put("expCondition", kpiInfo.getExpCondition());
				attrs.put("valExp", kpiInfo.getValExp());
				attrs.put("searchValue", kpiInfo.getSearchValue());
				attrs.put("kpiExtPros", kpiInfo.getKpiExtPros());
				attrs.put("kpiCategory", kpiInfo.getKpiCategory());
				attrs.put("serviceId", kpiInfo.getServiceId());
				if (kpiInfo.getValType() != null) {
					attrs.put("valType", JSON.toJSONString(kpiInfo.getValType()));
				}
				if (kpiInfo.getUinitConvertModelId() != null) {
					attrs.put("uinitConvertModelId", JSON.toJSONString(kpiInfo.getUinitConvertModelId()));
				}
				attrs.values().removeIf(val -> BinaryUtils.isEmpty(val));
				item.setAttrs(attrs);
				item.setOption(kpiInfo.getOption());
				item.setCreator(kpiInfo.getCreator());
				item.setModifier(kpiInfo.getModifier());
				item.setCreateTime(kpiInfo.getCreateTime());
				item.setModifyTime(kpiInfo.getModifyTime());
				item.setDomainId(kpiInfo.getDomainId());
				itemInfos.add(item);
			}
		}
		return itemInfos;
	}

	@Override
	public void bindCiClassRltToKpiInfo(KpiRltBindDto dto) {
		Assert.notNull(dto.getKpiId(), "X_PARAM_NOT_NULL${name:kpiId}");
		List<Long> saveClassIds = new ArrayList<>();
		if (dto.getClassId() != null) {
			saveClassIds.add(dto.getClassId());
		} else if (!BinaryUtils.isEmpty(dto.getClassIds())) {
			saveClassIds.addAll(dto.getClassIds());
		} else {
			Assert.isTrue(false, "X_PARAM_NOT_NULL${name:classId}");
		}
		ESDictionaryItemInfo item = dictSvc.getDictItemInfoById(dto.getKpiId());
		Assert.notNull(item, "指标不存在");
		ESKpiInfo kpiInfo = this.transDictItemToKpiInfo(Collections.singletonList(item)).get(0);
		if (kpiInfo.getClassIds() == null) {
			kpiInfo.setClassIds(new ArrayList<>());
		}
		kpiInfo.getClassIds().addAll(saveClassIds);
		List<Long> classIds = kpiInfo.getClassIds().stream().distinct().collect(Collectors.toList());
		kpiInfo.setClassIds(classIds);
		this.saveOrUpdate(kpiInfo);
	}

	@Override
	public void delCiClassRltToKpiInfo(KpiRltBindDto dto) {
		Assert.notNull(dto.getKpiId(), "X_PARAM_NOT_NULL${name:kpiId}");
		List<Long> delClassIds = new ArrayList<>();
		if (dto.getClassId() != null) {
			delClassIds.add(dto.getClassId());
		} else if (!BinaryUtils.isEmpty(dto.getClassIds())) {
			delClassIds.addAll(dto.getClassIds());
		} else {
			Assert.isTrue(false, "X_PARAM_NOT_NULL${name:classId}");
		}
		ESDictionaryItemInfo item = dictSvc.getDictItemInfoById(dto.getKpiId());
		Assert.notNull(item, "指标不存在");
		ESKpiInfo kpiInfo = this.transDictItemToKpiInfo(Collections.singletonList(item)).get(0);
		kpiInfo.getClassIds().removeAll(delClassIds);
		List<Long> classIds = kpiInfo.getClassIds().stream().distinct().collect(Collectors.toList());
		kpiInfo.setClassIds(classIds);
		this.saveOrUpdate(kpiInfo);
	}

	@Override
	public ImportSheetMessage saveOrUpdateBatch(Long domainId, List<ESKpiInfo> kpiInfos) {
		// 获取字典定义
		List<ESDictionaryClassInfo> dictClassInfos = dictSvc
				.queryDcitClassInfosByBean(ESDictionaryItemSearchBean.builder().domainId(domainId).dictCode("kpiManager").build());
		Assert.isTrue(!BinaryUtils.isEmpty(dictClassInfos), "指标字典不存在");
		List<ESDictionaryItemInfo> items = this.transKpiInfoToDictItem(kpiInfos);
		return dictSvc.saveDictionaryItemsBatch(dictClassInfos.get(0).getId(), items);
	}

}
