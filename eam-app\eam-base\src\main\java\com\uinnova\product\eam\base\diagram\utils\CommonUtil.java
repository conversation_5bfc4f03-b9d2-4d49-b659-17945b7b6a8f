package com.uinnova.product.eam.base.diagram.utils;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.*;
import cn.hutool.json.JSONObject;
import com.binary.core.exception.BinaryException;
import com.binary.core.util.BinaryUtils;
import com.binary.framework.web.RemoteResult;
import com.uinnova.product.eam.base.diagram.constant.CommonConst;
import com.uinnova.product.eam.base.diagram.exception.LoginFailException;
import com.uinnova.product.eam.base.diagram.model.RequestHeadParam;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.permission.query.CSysUser;
import com.uino.service.permission.microservice.IUserSvc;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;
import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description 工具类，整合常用公共方法
 * @date 2021-11-26-10:06
 */
@Slf4j
@Component
public class CommonUtil {

    private static RedisUtil redisUtil;

    private static String urlPath;

    private static IUserSvc userSvc;

    private static String localPath;

    private static String fileContent = "iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAAXNSR0IArs4c6QAAACxJREFUOE9j/P///38GKgLGUQMpDs3RMKQ4CBlGw3A0DMkIgdFkQ0agoWkBAE8+T8Xjo4rXAAAAAElFTkSuQmCC";

    @Value("${http.resource.space}")
    public void setUrlPath(String urlPath) {
        CommonUtil.urlPath = urlPath;
    }

    @Value("${local.resource.space}")
    public void setLocalPath(String localPath) {
        CommonUtil.localPath = localPath;
    }

    private static String userAgent;

    private static String localUserAgent;

    @Value("${wiki.oauth.client.user_agent}")
    public void setUserAgent(String userAgent) {
        CommonUtil.userAgent = userAgent;
    }

    @Value("${local.oauth.client.user_agent}")
    public void setLocalUserAgent(String localUserAgent) {
        CommonUtil.localUserAgent = localUserAgent;
    }

    @Autowired
    public void setRedisUtil(RedisUtil redisUtil) {
        CommonUtil.redisUtil = redisUtil;
    }

    @Autowired
    public void setUserSvc(IUserSvc userSvc) {
        CommonUtil.userSvc = userSvc;
    }

    private static String oauthServerUrl;

    @Value("${wiki.oauth.server.url}")
    public void setOauthServerUrl(String oauthServerUrl) {
        CommonUtil.oauthServerUrl = oauthServerUrl;
    }


    /**
     * 对用户头像地址做处理，如果为空，返回默认头像
     *
     * @param icon 待处理的图片地址
     * @return 正确的图片地址
     **/
    public static String getCorrectIcon(String icon) {
        String correctIcon = icon;
        boolean hasFault = false;
        try {
            if (!StringUtils.isEmpty(correctIcon)) {
                if (correctIcon.contains(urlPath)) {
                    correctIcon = correctIcon.replace(urlPath, "");
                }
                if (correctIcon.startsWith(CommonConst.SLASH)) {
                    correctIcon = urlPath + correctIcon;
                }
            } else {
                correctIcon = urlPath + CommonConst.DEFAULT_USER_ICON;
            }
        } catch (Exception e) {
            hasFault = true;
            e.printStackTrace();
        }
        if (hasFault) {
            return icon;
        } else {
            return correctIcon;
        }
    }

    /**
     * 根据用户id集合获取用户id和用户的映射
     **/
    public static Map<Long, SysUser> getUserIdObjMap(Long[] userIdArr) {
        CSysUser userQuery = new CSysUser();
        userQuery.setIds(userIdArr);
        List<SysUser> userList = userSvc.getSysUserByCdt(userQuery);
        Map<Long, SysUser> userIdObjMap = new HashMap<>(CommonConst.DEFAULT_MAP_SIZE);
        userList.forEach(user -> userIdObjMap.put(user.getId(), user));
        return userIdObjMap;
    }


    /**
     * 获取指定日期指定时间类型的开始值
     *
     * @param dateStr 日期字符串
     * @param type    日期类型(年、月、日)
     * @return 开始值
     * <AUTHOR>
     **/
    public String beginOfDate(String dateStr, int type) {
        //1.将dateStr转换为date类型
        DateTime dateTime = DateUtil.parse(dateStr, "yyyy-MM-dd");
        switch (type) {
            case 1:
                return String.valueOf(BinaryUtils.getNumberDate(DateUtil.beginOfYear(dateTime)));
            case 2:
                return String.valueOf(BinaryUtils.getNumberDate(DateUtil.beginOfMonth(dateTime)));
            case 3:
                return String.valueOf(BinaryUtils.getNumberDate(DateUtil.beginOfDay(dateTime)));
            default:
                throw new BinaryException("日期类型不符合规范");
        }
    }

    /**
     * 获取指定日期指定时间类型的结束值
     *
     * @param dateStr 日期字符串
     * @param type    日期类型(年、月、日)
     * @return 结束值
     * <AUTHOR>
     **/
    public String endOfDate(String dateStr, int type) {
        DateTime dateTime = DateUtil.parse(dateStr, "yyyy-MM-dd");
        switch (type) {
            case 1:
                return String.valueOf(BinaryUtils.getNumberDate(DateUtil.endOfYear(dateTime)));
            case 2:
                return String.valueOf(BinaryUtils.getNumberDate(DateUtil.endOfMonth(dateTime)));
            case 3:
                return String.valueOf(BinaryUtils.getNumberDate(DateUtil.endOfDay(dateTime)));
            default:
                throw new BinaryException("日期类型不符合规范");
        }
    }

    /**
     * 根据所有有效用户
     **/
    public static List<SysUser> getAllValidUser() {
        CSysUser userQuery = new CSysUser();
        return userSvc.getSysUserByCdt(userQuery);
    }

    public static String getUniqueFileUrl(String originFileUrl) {
        long currTime = System.currentTimeMillis();
        return "/" + currTime + "/" + originFileUrl;
    }

    public static String getToken() {
        //已校验的用户无需再次校验
        ServletRequestAttributes attrs = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        assert attrs != null;
        HttpServletRequest request;
        String authorization = "";
        try {
            request = attrs.getRequest();
            authorization = request.getHeader(CommonConst.SSO_Auth);
            authorization = StringUtils.isEmpty(authorization) ? request.getHeader(CommonConst.ACC_TOKEN) : authorization;
            authorization = StringUtils.isEmpty(authorization) ? request.getHeader(Header.AUTHORIZATION.getValue()) : authorization;
            Object value = redisUtil.get("REFRESH" + authorization.replace("Bearer", "").trim());
            if (value != null) {
                authorization = (String) value;
            }
        } catch (NullPointerException exception) {
            return null;
        }
        if (StringUtils.isEmpty(authorization)) {
            return null;
        }
        authorization = authorization.startsWith("Bearer") ? authorization : "Bearer " + authorization.trim();
        return authorization;
    }

    public static RequestHeadParam getRequestHeadParam() {
        //已校验的用户无需再次校验
        ServletRequestAttributes attrs = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        assert attrs != null;
        HttpServletRequest request;
        String authorization = "";
        String appName = "";
        String agent = "";
        boolean isLocal = false;
        try {
            request = attrs.getRequest();
            // 获取请求的token，从Authorization、AccToken、SSO-Authorization中获取
            authorization = request.getHeader(Header.AUTHORIZATION.getValue());
            authorization = StringUtils.isEmpty(authorization) ? request.getHeader(CommonConst.ACC_TOKEN) : authorization;
            authorization = StringUtils.isEmpty(authorization) ? request.getHeader(CommonConst.SSO_Auth) : authorization;
            Object value = redisUtil.get("REFRESH" + authorization.replace("Bearer", "").trim());
            if (value != null) {
                authorization = (String) value;
            }
            authorization = authorization.startsWith("Bearer") ? authorization : "Bearer " + authorization.trim();

            // 获取请求的userAgent，从User-Agent、appName中获取
            appName = request.getHeader(CommonConst.APP_NAME);
            agent = request.getHeader(Header.USER_AGENT.getValue());
            if (!StringUtils.isEmpty(agent) && userAgent.contains("localhost")) {
                isLocal = true;
            }
            agent = isLocal ? localUserAgent : userAgent;
            if (!StringUtils.isEmpty(appName)) {
                agent = appName;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        RequestHeadParam param = new RequestHeadParam();
        param.setToken(authorization);
        param.setUserAgent(agent);
        return param;
    }



    /**
     * 转换yyyyMMddHHmmss格式的日期为毫秒值
     *
     * @param date 日期
     * @return 毫秒值
     **/
    public static Long parseDateToTime(Long date) {
        try {
            SimpleDateFormat format = new SimpleDateFormat("yyyyMMddHHmmss");
            return format.parse(String.valueOf(date)).getTime();
        } catch (Exception e) {
            //转换出错返回原值
            log.info("转换日期出错");
            e.printStackTrace();
            return date;
        }
    }


    /**
     * <AUTHOR>
     * @Description 响应成功
     * @Date 14:06 2021/9/6
     * @Param [loginCode]
     **/
    public static RemoteResult success(Object data) {
        return new RemoteResult(true, 200, "操作成功", data);
    }

    /**
     * 判空对象并抛错
     *
     * @param data       对象
     * @param objectName 字段名称
     **/
    public static void checkEmpty(Object data, String objectName) {
        if (ObjectUtil.isEmpty(data)) {
            throw new BinaryException(objectName + "不能为空");
        }
    }

    /**
     * <AUTHOR>
     * @Description 生成随机密码
     * @Date 14:06 2021/9/6
     * @Param [loginCode]
     **/
    public static String randPwd(String loginCode) {
        String a = "ab";
        String b = "cd";
        if (loginCode.length() > 4) {
            a = loginCode.substring(0, 2);
            b = loginCode.substring(2, 4);
        }
        return a + "T_w" + System.currentTimeMillis() % 100 + "er" + b + System.currentTimeMillis() % 1000;
    }

    @Value("${prefix1:https://}")
    private static String prefix1;
    @Value("${prefix2:http://}")
    private static String prefix2;

    /**
     * <AUTHOR>
     * @Description 判断https或http
     * @Date 14:06 2022/3/15
     * @Param [loginCode]
     **/
    public static String useHttps(String path) {
        String rout="";
        path = path.replace(prefix1, "").replace(prefix2, "").replace(" ", "%20");
        try {
            HttpUtil.get(path);
        } catch (Exception e) {
            rout = prefix2 + path;
        }
        return rout;
    }

    /**
     * @Description 获取当天的yyyyMMdd格式
     * @Date 14:06 2022/3/15
     * @Param [loginCode]
     **/
    public static Long getNumberDate() {
        return getNumberDate(new Date());
    }

    /**
     * @Description 获取指定日期的yyyyMMdd格式
     * @Date 14:06 2022/3/15
     * @Param [loginCode]
     **/
    public static Long getNumberDate(Date date) {
        if (date == null) {
            return null;
        }
        return Long.valueOf(DatePattern.PURE_DATE_FORMAT.format(date));
    }

    /**
     * @Description 获取指定日期的yyyyMMddHHmmss格式
     * @Date 14:06 2022/3/15
     * @Param [loginCode]
     **/
    public static Long getDateTime(Date date) {
        if (date == null) {
            return null;
        }
        return Long.valueOf(DatePattern.PURE_DATETIME_FORMAT.format(date));
    }

    /**
     * <AUTHOR>
     * @Description 获取当天日期-yyyyMMdd
     * @Date 11:06 2022/3/25
     **/
    public static Long getTodayYMD() {
        return Long.valueOf(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
    }

    /**
     * <AUTHOR>
     * @Description 获取studio会员信息
     * @Date 15:06 2022/5/19
     **/
    public static JSONObject getStudioVip(Long mmdId) {
        //获取thingjs会员信息
        JSONObject jsonObj;
        HttpResponse userInfoRes = HttpRequest.get("" + mmdId)
                .contentType(ContentType.JSON.getValue())
                .header("client-id", "thingStudio")
                .execute();
        String tokenResBody = userInfoRes.body();
        if (!userInfoRes.isOk()) {
            log.info("获取用户vip信息失败, 报错如下：" + userInfoRes.body());
            throw new LoginFailException("获取用户vip信息失败");
        } else {
            jsonObj = new JSONObject(tokenResBody);
            jsonObj = (JSONObject) jsonObj.get("userInfo");
        }
//        log.info(userInfo.toString());
        return jsonObj;
    }

    /**
     * <AUTHOR>
     * @Description 获取studio是否为会员
     * @Date 18:06 2022/5/19
     **/
    public static boolean getIsVip(Long mmdId) {
        boolean isVip = false;
        JSONObject studioVip = null;
        try {
            studioVip = getStudioVip(mmdId);
        } catch (Exception e) {
            e.printStackTrace();
        }
        // z_role代表零代码会员状态，字段的值说明 free: 普通用户;vip: VIP会员;svip: SVIP会员
        //role代表低代码会员状态，字段的值说明：free: 普通用户;developer: VIP会员;developer_svip: SVIP会员
        String z_role = studioVip.getStr("z_role");
        String role = studioVip.getStr("role");
        if (org.springframework.util.StringUtils.isEmpty(z_role) && org.springframework.util.StringUtils.isEmpty(role)) {
            isVip = false;
        }
        if (((z_role.equals("vip") || z_role.equals("svip")) || (role.equals("developer") || role.equals("developer_svip")))) {
            isVip = true;
        }
        return isVip;
    }


    /**
     * 获得文件编码
     * @param  file file
     * @return
     * @throws Exception
     */
    public static String fileCode(File file){
        String code = "";
        BufferedInputStream bin = null;
        try {
            bin = new BufferedInputStream(new FileInputStream(file));
            int p = (bin.read() << 8) + bin.read();
            bin.close();
            switch (p) {
                case 0xefbb:
                    code = "UTF-8";
                    break;
                case 0xfffe:
                    code = "Unicode";
                    break;
                case 0xfeff:
                    code = "UTF-16BE";
                    break;
                default:
                    code = "GBK";
            }
        } catch (Exception e) {
            return code;
        } finally {
            try {
                bin.close();
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        return code;
    }
}
