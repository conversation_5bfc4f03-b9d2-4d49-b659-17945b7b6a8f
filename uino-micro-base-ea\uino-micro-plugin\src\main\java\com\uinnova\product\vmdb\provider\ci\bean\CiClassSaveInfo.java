package com.uinnova.product.vmdb.provider.ci.bean;

import com.binary.core.util.BinaryUtils;
import com.uinnova.product.vmdb.comm.util.SaveType;

import java.io.Serializable;
import java.util.List;

/**
 * 保存需要批量保存的CI相关信息
 * 
 * <AUTHOR>
 *
 */
public class CiClassSaveInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 所属CI分类ID
     */
    private Long classId;

    private String classStdCode;
    /**
     * 来源ID
     */
    private Long sourceId;
    /**
     * 拥有者ID
     */
    private String ownerCode;
    /**
     * 当前分类下的CI信息
     */
    private List<CcCiRecord> records;

    /**
     * 保存类型
     */
    private SaveType saveType;

    public CiClassSaveInfo(Long classId, String classStdCode, Long sourceId, String ownerCode, List<CcCiRecord> records, SaveType saveType) {
        BinaryUtils.checkEmpty(classId, "classId");
        BinaryUtils.checkEmpty(classStdCode, "classStdCode");
        BinaryUtils.checkEmpty(ownerCode, "ownerCode");
        this.classId = classId;
        this.classStdCode = classStdCode;
        this.sourceId = sourceId;
        this.ownerCode = ownerCode;
        this.records = records;
        this.saveType = saveType;
    }

    public Long getClassId() {
        return classId;
    }

    public void setClassId(Long classId) {
        this.classId = classId;
    }

    public String getClassStdCode() {
        return classStdCode;
    }

    public void setClassStdCode(String classStdCode) {
        this.classStdCode = classStdCode;
    }

    public Long getSourceId() {
        return sourceId;
    }

    public void setSourceId(Long sourceId) {
        this.sourceId = sourceId;
    }

    public String getOwnerCode() {
        return ownerCode;
    }

    public void setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
    }

    public List<CcCiRecord> getRecords() {
        return records;
    }

    public void setRecords(List<CcCiRecord> records) {
        this.records = records;
    }

    public SaveType getSaveType() {
        return saveType;
    }

    public void setSaveType(SaveType saveType) {
        this.saveType = saveType;
    }

}
