package com.uinnova.product.eam.comm.model.es;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

@Data
@Comment("引导项配置表[uino_eam_boot_entry_config]")
public class BootEntryConfig {
    @Comment("主键id")
    private Long id;
    @Comment("角色ID")
    private Long userRoleId;
    @Comment("引导向名称")
    private String bootEntryName;
    @Comment("引导项标题")
    private String title;
    @Comment("引导项标题介绍")
    private String titleIntroduce;
    @Comment("引导项功能介绍")
    private String bootIntroduce;
    @Comment("引导项功能按钮名称")
    private String bootButtonName;
    @Comment("二级引导项功能按钮名称")
    private String secondBootButtonName;
    @Comment("一级页面路径")
    private String firstMenuPath;
    @Comment("页面路径")
    private String menuPath;
    @Comment("菜单id")
    private String functionId;
    @Comment("1表示菜单引导2表示页面引导")
    private Integer bootType;
    @Comment("页面引导操作类型 newBuild search view plan objectManagement")
    private String menuActionType;
    @Comment("资源路径")
    private String resourcePath;
    @Comment("是否是标准数据")
    private Boolean isFromStandard;
    @Comment("排序字段")
    private int sort;
    @Comment("动图/视频url")
    private String imageOrVideoUrl;
    @Comment("创建时间")
    private Long createTime;
    @Comment("更新时间")
    private Long modifyTime;

    private Boolean isJump;
}
