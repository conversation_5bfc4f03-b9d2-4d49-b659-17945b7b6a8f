package com.uinnova.product.eam.model.dm.bean;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.config.Env;
import com.uinnova.product.eam.model.EamArtifactVo;
import com.uinnova.product.eam.model.enums.ArtifactEnum;
import com.uinnova.product.vmdb.comm.model.ci.CcCiClass;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * 模型转换类型传输vo
 * <AUTHOR>
 */
@Data
public class CoverDiagramTypeVO implements Serializable {

    @Comment("当前实体分类标识")
    private String classCode;

    @Comment("转换的实体分类标识")
    private String coverCode;

    @Comment("当前制品类型")
    private Integer type;

    @Comment("转换的制品类型")
    private Integer coverType;

    @Comment("当前视图名称后缀")
    private String suffix;

    @Comment("转换的视图名称后缀")
    private String coverSuffix;

    @Comment("制品")
    private EamArtifactVo artifact;

    @Comment("包含关系分类")
    private CcCiClass includeRlt;

    @Comment("衍生关系分类")
    private CcCiClass deriveRlt;

    @Comment("ci分类集合")
    private Map<String, CcCiClass> classMap;

    @Comment("是否覆盖：1覆盖2增量")
    private Boolean operation;

    @Comment("用户")
    private String ownerCode;

    public CoverDiagramTypeVO() {
    }

    public CoverDiagramTypeVO (Integer type){
        if(ArtifactEnum.CONCEPTUAL_ENTITY.getArtifactType().equals(type)){
            this.classCode = Env.CONCEPTION_ENTITY.getCode();
            this.coverCode = Env.LOGIC_ENTITY.getCode();
            this.suffix = "";
            this.coverSuffix = "-C";
            this.coverType = ArtifactEnum.RELATION_ENTITY.getArtifactType();
        }else if(ArtifactEnum.RELATION_ENTITY.getArtifactType().equals(type)){
            this.classCode = Env.LOGIC_ENTITY.getCode();
            this.coverCode = Env.LOGIC_ENTITY.getCode();
            this.suffix = "-C";
            this.coverSuffix = "-C'";
            this.coverType = ArtifactEnum.SYS_LOGICAL_ENTITY.getArtifactType();
        }else if(ArtifactEnum.SYS_LOGICAL_ENTITY.getArtifactType().equals(type)){
            this.classCode = Env.LOGIC_ENTITY.getCode();
            this.coverCode = Env.PHYSICAL_ENTITY.getCode();
            this.suffix = "-C'";
            this.coverSuffix = "-D";
            this.coverType = ArtifactEnum.PHYSICAL_ENTITY.getArtifactType();
        }
        this.type = type;
    }

    public void setOperation(Integer operation){
        this.operation = operation.equals(1);
    }
}
