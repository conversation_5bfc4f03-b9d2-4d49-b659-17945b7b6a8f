package com.uino.bean.dataset.enums;

/**
 *
 */
public enum DigitalServiceTypeEnum {

    BASE(0 , "基础服务"),
    RELATION_RULE(1 , "关系遍历服务"),
    CI_CLASS(2 , "CI分类"),
    REL_CLASS(3 , "关系分类"),
    UP_DOWN_FLOOR(4 , "上下层关系"),
    METRICS(5 , "指标汇聚类服务"),
    STATISTIC(6 , "统计服务");

    private Integer code;

    private String typeName;

    DigitalServiceTypeEnum(Integer code, String typeName) {
        this.code = code;
        this.typeName = typeName;
    }

    public static String getTypeNameByCode(Integer code){
        if (null != code){
            for (DigitalServiceTypeEnum value : values()) {
                if (value.code.equals(code))
                    return value.typeName;
            }
        }
        return null;
    }
    public Integer getCode() {
        return code;
    }

    public String getTypeName() {
        return typeName;
    }
}
