package com.uino.util.express.common;

import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.type.AviatorObject;
import com.uino.util.express.AviatorFunctionExecutor;
import com.uino.util.express.annotation.UinoAviatorFunction;
import com.uino.util.express.annotation.UinoAviatorParam;
import com.uino.util.sys.SysUtil;
import lombok.extern.log4j.Log4j2;

import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @Title: UinoAbstractAviatorFunction
 * @Description: Gets the detailed function description
 * @Author: ZJM
 * @Create: 2021-09-01 14:03
 **/
@Log4j2
public abstract class UinoAbstractAviatorFunction extends AbstractFunction {

    @Override
    public String getName() {
        Class<? extends UinoAbstractAviatorFunction> clazz = this.getClass();
        UinoAviatorFunction funcAnnotation = clazz.getAnnotation(UinoAviatorFunction.class);
        if (funcAnnotation != null) {
            return funcAnnotation.name();
        } else {
            return null;
        }
    }


    /**
     * 获取方法描述
     * @return 所有重载的方法的描述
     */
    public FunctionCollectionDesc getFunctionInfos() {
        Class<? extends UinoAbstractAviatorFunction> clazz = this.getClass();
        FunctionCollectionDesc funcCollectionDesc = new FunctionCollectionDesc();
        List<FunctionDescription> descriptionList = new ArrayList<>();
        UinoAviatorFunction funcAnnotation = clazz.getAnnotation(UinoAviatorFunction.class);
        if (funcAnnotation != null && this.getName().equals(funcAnnotation.name()) && clazz.getDeclaredMethods().length > 0) {
            List<Method> callMethodList = new ArrayList<>();
            AviatorFunctionExecutor.findCallMethodsInClass(clazz, callMethodList);
            for (Method method : callMethodList) {
                if (method.getName().equals("call")) {
                    List<FunctionParamDescription> paramDescriptionList = new ArrayList<>();
                    for (Parameter parameter : method.getParameters()) {
                        if (parameter.getType().equals(AviatorObject.class)) {
                            UinoAviatorParam paramAnnotation = parameter.getAnnotation(UinoAviatorParam.class);
                            FunctionParamDescription paramDescription = new FunctionParamDescription();
                            paramDescription.setParamDescription(paramAnnotation != null ? paramAnnotation.desc() : "");
                            paramDescription.setParamType(paramAnnotation != null ? paramAnnotation.type() : parameter.getType());
                            paramDescription.setParamName((paramAnnotation != null && !paramAnnotation.name().isEmpty()) ? paramAnnotation.name() : parameter.getName());
                            paramDescriptionList.add(paramDescription);
                        }
                    }
                    FunctionDescription description = new FunctionDescription();
                    description.setFunctionName(funcAnnotation.name());
                    description.setFunctionDescription(funcAnnotation.desc());
                    description.setReturnType(funcAnnotation.returnType());
                    description.setParamsDescription(paramDescriptionList);
                    descriptionList.add(description);
                }
            }
            funcCollectionDesc.setFunctions(descriptionList);
            funcCollectionDesc.setFunctionName(funcAnnotation.name());
            funcCollectionDesc.setFunctionShortName(getShortName(funcAnnotation));
            funcCollectionDesc.setFunctionDescription(funcAnnotation.desc());
            funcCollectionDesc.setReturnType(funcAnnotation.returnType());
            funcCollectionDesc.setTags(Arrays.asList(funcAnnotation.tags()));
            return funcCollectionDesc;
        } else {
            return null;
        }
    }

    private String getShortName(UinoAviatorFunction funcAnnotation) {
        if (SysUtil.StringUtil.isNotBack(funcAnnotation.shortName())) {
            return funcAnnotation.shortName();
        } else {
            String funcName = funcAnnotation.name();
            if (funcName.contains(".")) {
                String[] split = funcName.split("\\.");
                return split[split.length - 1];
            } else {
                return funcName;
            }

        }
    }
}
