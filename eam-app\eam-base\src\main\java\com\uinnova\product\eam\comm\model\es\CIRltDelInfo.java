package com.uinnova.product.eam.comm.model.es;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.base.cj.BaseEntity;
import lombok.Data;

@Data
@Comment("ci关系删除详细")
public class CIRltDelInfo extends BaseEntity {

    @Comment("关系code")
    private String rltCode;
    @Comment("源端分类id")
    private Long sourceCIClassId;
    @Comment("源端分类图标")
    private String sourceCIClassIcon;
    @Comment("源端分类名称")
    private String sourceCIClassName;
    @Comment("源端ciCode")
    private String sourceCICode;
    @Comment("源端ci名称")
    private String sourceCIName;
    @Comment("源端对象展示名称")
    private String sourceName;
    @Comment("关系线样式")
    private String rltLineType;
    @Comment("关系名称")
    private String rltClassName;
    @Comment("目标端分类id")
    private Long targetCIClassId;
    @Comment("目标端分类图标")
    private String targetCIClassIcon;
    @Comment("目标端分类名称")
    private String targetCIClassName;
    @Comment("目标端ciCode")
    private String targetCICode;
    @Comment("目标端ci名称")
    private String targetCIName;
    @Comment("关联视图或矩阵id")
    private String relateAssertId;
    @Comment("关联视图或矩阵名称")
    private String relateAssertName;
    @Comment("关联资产类型")
    private String relateAssertType;
    @Comment("删除类型")
    private String delType;
    @Comment("消息id")
    private Long noticeId;
}
