package com.uino.service.permission.microservice;

import com.uino.bean.permission.base.SysModule;
import com.uino.bean.permission.query.SysModuleCheck;

import java.util.Map;

/**
 * @Title: IButtonSvc
 * @Description:
 * @Author: YGQ
 * @Create: 2021-06-28 10:05
 **/
public interface IButtonSvc {
    /**
     * Save button
     *
     * @param buttonDto save dto
     * @return {@link SysModule}
     */
    SysModule saveButton(SysModule buttonDto);

    /**
     * Delete button
     *
     * @param id button id
     */
    void deleteButton(Long id);

    /**
     * Save button sort
     *
     * @param orderDict order dict
     */
    void saveButtonSort(Map<Long, Integer> orderDict);

    /**
     * check module sign
     *
     * @param sysButton save dto
     * @return {@code true/false}
     */
    SysModuleCheck checkModuleSign(SysModule sysButton);
}
