package com.uinnova.product.eam.web.cj;

import com.uinnova.product.eam.base.cj.ResultMsg;
import com.uinnova.product.eam.model.cj.vo.DirVo;
import com.uinnova.product.eam.model.cj.vo.ESDiagramDirPlanVO;
import com.uinnova.product.eam.model.cj.vo.PublishedDiagramPlanVO;
import com.uinnova.product.eam.model.vo.MineAssetsReq;
import com.uinnova.product.eam.service.cj.service.DirDiagramPlanService;
import com.uinnova.product.eam.service.exception.BusinessException;
import com.uinnova.product.vmdb.comm.model.ci.CcCi;
import com.uino.bean.permission.base.SysUser;
import com.uino.util.sys.SysUtil;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.text.ParseException;
import java.util.List;
import java.util.Map;

/**
 * @description: 文件夹 + 制品 + 方案
 * @author: Lc
 * @create: 2022-02-23 16:49
 */
@RestController
@RequestMapping("/dirDiagramPlan")
public class DirDiagramPlanController {

    @Resource
    private DirDiagramPlanService dirDiagramPlanService;

    @GetMapping("/movePlan")
    public ResultMsg movePlane(@RequestParam Long planId,@RequestParam Long targetDirId) {
        if (planId == null) {
            throw new BusinessException("移动的方案id不能为空!");
        }
        if (targetDirId == null) {
            throw new BusinessException("移动的方案的目标文件夹不能为空!");
        }
        dirDiagramPlanService.movePlane(planId,targetDirId);
        return new ResultMsg(targetDirId);
    }

    @GetMapping("/getPlanAssertName")
    public ResultMsg getPlanAssertName(@RequestParam Long planId) {
        List<CcCi> result = dirDiagramPlanService.getPlanAssertName(planId);
        return new ResultMsg(result);
    }

    @PostMapping("/getBuildAssert")
    public ResultMsg getBuildAssert(@RequestBody MineAssetsReq req) {
        ESDiagramDirPlanVO buildAssert = dirDiagramPlanService.getBuildAssertNew(req);
        return new ResultMsg(buildAssert);
    }

    @GetMapping("/getAllMineAttention")
    public ResultMsg getAllMineAttention() throws ParseException {
        ESDiagramDirPlanVO result = dirDiagramPlanService.getAllMineAttention();
        return new ResultMsg(result);
    }

    @GetMapping("/getAllRecentlyDiagramAndPlan")
    public ResultMsg getAllRecentlyDiagramAndPlan() throws ParseException {
        ESDiagramDirPlanVO buildAssert = dirDiagramPlanService.getAllRecentlyDiagramAndPlan();
        return new ResultMsg(buildAssert);
    }

    @GetMapping("/getDesignDiagramAndPlan")
    public ResultMsg getDesignDiagramAndPlan() throws ParseException {
        ESDiagramDirPlanVO buildAssert = dirDiagramPlanService.getDesignDiagramAndPlan();
        return new ResultMsg(buildAssert);
    }

    @GetMapping("getCount")
    public ResultMsg getCount() throws ParseException {
        Map<String, Long> result = dirDiagramPlanService.getCount();
        return new ResultMsg(result);
    }

    @GetMapping("/findMyPublishList")
    public ResultMsg findMyPublishList() {
        SysUser user = SysUtil.getCurrentUserInfo();
        List<PublishedDiagramPlanVO> dirDiagramPlanList = dirDiagramPlanService.findMyPublishList(user);
        return new ResultMsg(dirDiagramPlanList);
    }

    @PostMapping("/getDirName")
    public ResultMsg getDirName(@RequestBody DirVo dirVo) {
        String dirName = dirDiagramPlanService.getDirName(dirVo);
        return new ResultMsg(dirName);
    }

    @GetMapping("/getHostSystemByPlanId")
    public ResultMsg getHostSystemByPlanId(@RequestParam("planId") Long planId) {
        String hostSystem = dirDiagramPlanService.getHostSystemByPlanId(planId);
        return new ResultMsg(hostSystem);
    }

}
