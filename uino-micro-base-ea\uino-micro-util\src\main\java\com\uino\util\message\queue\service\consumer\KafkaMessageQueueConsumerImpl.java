package com.uino.util.message.queue.service.consumer;

import com.uino.util.message.queue.config.KafkaConsumerConfig;
import com.uino.util.message.queue.config.KafkaProp;
import com.uino.util.message.queue.tools.ListenMode;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.kafka.listener.*;
import org.springframework.kafka.listener.DefaultErrorHandler;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Kafka implementation of message queue,
 * including batch consumption and single consumption
 *
 * @Author: YGQ
 * @Create: 2021-05-24 13:50
 **/
public class KafkaMessageQueueConsumerImpl implements MessageQueueConsumerInterface {
    public static final Logger logger = LoggerFactory.getLogger(KafkaMessageQueueConsumerImpl.class);

    /**
     * Multiple topic names
     */
    private final String topics;
    /**
     * Consumer group ID
     */
    private final String groupId;
    /**
     * listening mode, batch or single
     * <br>
     * ListenMode.SINGLE
     * ListenMode.BATCH (default)
     */
    private final ListenMode listenMode;
    /**
     * The number of concurrent consumption threads,
     * if not specified, it will be loaded according to the default value of the configuration file.
     *
     * <p style="yellow">If you specify the number of threads, the number of threads cannot be greater than the number of partitions</p>
     */
    private final Integer customConcurrencyNumber;
    /**
     * Single listener handler
     */
    private final MessageQueueHandler messageQueueHandler;

    public KafkaMessageQueueConsumerImpl(String topics, String groupId, ListenMode listenMode, Integer customConcurrencyNumber, MessageQueueHandler messageQueueHandler) {
        this.topics = topics;
        this.groupId = groupId;
        this.listenMode = listenMode != null ? listenMode : ListenMode.SINGLE;
        this.customConcurrencyNumber = customConcurrencyNumber;
        this.messageQueueHandler = messageQueueHandler;
    }

    @Override
    public void listen() {
        Assert.notNull(topics, "consumer topics is not null");
        String currentTopics = topics.replaceAll("\\s*", "");
        String[] topicsArray = currentTopics.split(",");
        ContainerProperties containerProperties = new ContainerProperties(topicsArray);
        containerProperties.setGroupId(groupId);
        boolean enableAutoCommit = KafkaProp.enableAutoCommit;

        // Set ack mode when auto commit is false
        if (!enableAutoCommit) {
            containerProperties.setAckMode(ContainerProperties.AckMode.valueOf(KafkaProp.consumerAckMode.trim()));
        }

        if (listenMode == ListenMode.SINGLE) {
            // Set up a single listener
            containerProperties.setMessageListener((AcknowledgingConsumerAwareMessageListener<String, String>) (data, acknowledgment, consumer) -> {
                String receiveValue = data.value();
                Optional<?> receiveValueCheck = Optional.ofNullable(receiveValue);
                if (receiveValueCheck.isPresent()) {
                    // logger.debug("Received Kafka message, topic: {}, key: {}, timestamp: {}, offset: {}, value: {}", data.topic(), data.key(), data.timestamp(), data.offset(), data.value());
                    boolean manualConfirmation = messageQueueHandler.message(receiveValue);
                    if (manualConfirmation || enableAutoCommit) {
                        acknowledgment.acknowledge();
                    }
                } else {
                    acknowledgment.acknowledge();
                }

            });
        } else if (listenMode == ListenMode.BATCH || listenMode == ListenMode.LIST_BATCH) {
            // Set up a batch listener
            containerProperties.setMessageListener((BatchAcknowledgingMessageListener<String, String>) (data, acknowledgment) -> {
                Optional<?> receiveValueCheck = Optional.ofNullable(data);
                if (receiveValueCheck.isPresent()) {
                    ConsumerRecord<String, String> singleConsumerRecord = data.get(0);
                    // logger.debug("Received Kafka message, topic: {}, single key: {}, single ", singleConsumerRecord.topic(), singleConsumerRecord.timestamp());
                    List<String> formatValue = data.stream().map(ConsumerRecord::value).filter(receiveValue -> !receiveValue.isEmpty()).collect(Collectors.toList());
                    boolean manualConfirmation = messageQueueHandler.message(formatValue.toString());
                    if (manualConfirmation || enableAutoCommit) {
                        acknowledgment.acknowledge();
                    }
                } else {
                    acknowledgment.acknowledge();
                }
            });
        }

        // Load configuration properties
        Map<String, Object> configs = KafkaConsumerConfig.Configs.getConfigs();
        if (listenMode == ListenMode.LIST_BATCH) {
            configs.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, 5);
        }

        ConsumerFactory<String, String> consumerFactory = new DefaultKafkaConsumerFactory<>(configs);

        ConcurrentMessageListenerContainer<String, String> messageListenerContainer = new ConcurrentMessageListenerContainer<>(consumerFactory, containerProperties);

        if (listenMode == ListenMode.SINGLE) {
            // Set up single handling exception
            messageListenerContainer.setCommonErrorHandler(new DefaultErrorHandler((record, exception) -> {
                throw new RuntimeException(exception.getMessage());
            }));
        } else if (listenMode == ListenMode.BATCH) {
            // Set up batch handling exception
            messageListenerContainer.setCommonErrorHandler(new DefaultErrorHandler((record, exception) -> {
                throw new RuntimeException(exception.getMessage());
            }));
        }

        // If the number of consumers is not specified, load the default configuration
        if (null != customConcurrencyNumber) {
            messageListenerContainer.setConcurrency(customConcurrencyNumber);
        } else {
            messageListenerContainer.setConcurrency(KafkaProp.concurrencyNumber);
        }

        messageListenerContainer.setBeanName("kafka-client-" + groupId);
        messageListenerContainer.setAutoStartup(true);
        messageListenerContainer.start();
    }
}
