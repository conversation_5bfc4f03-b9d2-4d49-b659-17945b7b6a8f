<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:flowable="http://flowable.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://www.flowable.org/processdef">
  <process id="flow_system_publish_approve" name="流程体系-末级流程发布" isExecutable="true">
    <documentation>末级流程发布</documentation>
    <startEvent id="startEvent1" flowable:formFieldValidation="true"></startEvent>
    <userTask id="sid-1026748D-6967-4AD7-8C63-B592A836CEFE" name="提交审批" flowable:formFieldValidation="true"></userTask>
    <sequenceFlow id="sid-025B79E4-65D0-444E-924F-EA8498BC595A" sourceRef="startEvent1" targetRef="sid-1026748D-6967-4AD7-8C63-B592A836CEFE"></sequenceFlow>
    <userTask id="business_leader" name="业务域负责人审批" flowable:assignee="${assignee}" flowable:formFieldValidation="true">
      <extensionElements>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
      <multiInstanceLoopCharacteristics isSequential="false" flowable:collection="assigneeList" flowable:elementVariable="assignee">
        <completionCondition>${multiInstanceCompleteExecution.executeByOneUserConditionImmediately(execution)}</completionCondition>
      </multiInstanceLoopCharacteristics>
    </userTask>
    <exclusiveGateway id="sid-DFEC839C-5FC0-4874-94F3-8E88384687BA"></exclusiveGateway>
    <userTask id="business_leader_reject" name="提交人修改" flowable:assignee="$INITIATOR" flowable:formFieldValidation="true">
      <extensionElements>
        <modeler:activiti-idm-initiator xmlns:modeler="http://flowable.org/modeler"><![CDATA[true]]></modeler:activiti-idm-initiator>
      </extensionElements>
    </userTask>
    <sequenceFlow id="sid-9FB97B7C-889B-4144-87D6-7C4DA1D52637" sourceRef="business_leader_reject" targetRef="business_leader"></sequenceFlow>
    <userTask id="business_review" name="业务审查人审批" flowable:assignee="${assignee}" flowable:formFieldValidation="true">
      <extensionElements>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
      <multiInstanceLoopCharacteristics isSequential="false" flowable:collection="assigneeList" flowable:elementVariable="assignee">
        <completionCondition>${multiInstanceCompleteExecution.rejectByOneUserConditionImmediately(execution)}</completionCondition>
      </multiInstanceLoopCharacteristics>
    </userTask>
    <exclusiveGateway id="sid-BAFD954C-7C3F-4BAF-902F-F29E2D01DF0F"></exclusiveGateway>
    <userTask id="integrated_reviewer" name="集成审查人审批" flowable:assignee="${assignee}" flowable:formFieldValidation="true">
      <extensionElements>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
      <multiInstanceLoopCharacteristics isSequential="false" flowable:collection="assigneeList" flowable:elementVariable="assignee">
        <completionCondition>${multiInstanceCompleteExecution.rejectByOneUserConditionImmediately(execution)}</completionCondition>
      </multiInstanceLoopCharacteristics>
    </userTask>
    <exclusiveGateway id="sid-3AD37A31-6350-4E8F-AD87-5A9DC554F13B"></exclusiveGateway>
    <sequenceFlow id="sid-35C196CB-BFB5-4312-A8F7-DBD1D22C314A" sourceRef="integrated_reviewer" targetRef="sid-3AD37A31-6350-4E8F-AD87-5A9DC554F13B"></sequenceFlow>
    <endEvent id="sid-3580497E-5BA8-4385-99C0-1416DA248181"></endEvent>
    <userTask id="integrated_reviewer_reject" name="提交人修改" flowable:assignee="$INITIATOR" flowable:formFieldValidation="true">
      <extensionElements>
        <modeler:activiti-idm-initiator xmlns:modeler="http://flowable.org/modeler"><![CDATA[true]]></modeler:activiti-idm-initiator>
      </extensionElements>
    </userTask>
    <userTask id="business_review_reject" name="提交人修改" flowable:assignee="$INITIATOR" flowable:formFieldValidation="true">
      <extensionElements>
        <modeler:activiti-idm-initiator xmlns:modeler="http://flowable.org/modeler"><![CDATA[true]]></modeler:activiti-idm-initiator>
      </extensionElements>
    </userTask>
    <sequenceFlow id="sid-58884371-B70B-44D7-BFB1-8AE2A0C53147" sourceRef="sid-1026748D-6967-4AD7-8C63-B592A836CEFE" targetRef="business_leader"></sequenceFlow>
    <sequenceFlow id="sid-E8F2AB77-08F1-45CF-AB60-A2A4DC11F008" sourceRef="sid-3AD37A31-6350-4E8F-AD87-5A9DC554F13B" targetRef="sid-3580497E-5BA8-4385-99C0-1416DA248181">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${goOut=='pass'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-DD307F56-E6B6-45C3-84A4-185877557175" sourceRef="sid-3AD37A31-6350-4E8F-AD87-5A9DC554F13B" targetRef="integrated_reviewer_reject">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${goOut=='noPass'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-C042E687-D06A-4220-A896-93773CAB57C4" sourceRef="business_leader" targetRef="sid-DFEC839C-5FC0-4874-94F3-8E88384687BA"></sequenceFlow>
    <sequenceFlow id="sid-8EC84DCB-8EE7-41A1-8E53-C24CE5DAD115" name="驳回" sourceRef="sid-DFEC839C-5FC0-4874-94F3-8E88384687BA" targetRef="business_leader_reject">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${goOut=='noPass'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-9953D168-E478-4E7B-B3F5-F0D8EDEFFA92" sourceRef="business_review" targetRef="sid-BAFD954C-7C3F-4BAF-902F-F29E2D01DF0F"></sequenceFlow>
    <sequenceFlow id="sid-DA50B8A4-9090-4B44-AF9E-FCCC228303CE" sourceRef="sid-BAFD954C-7C3F-4BAF-902F-F29E2D01DF0F" targetRef="business_review_reject">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${goOut=='noPass'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-0006DF50-1657-4462-882E-21DF6D91FC1F" sourceRef="sid-DFEC839C-5FC0-4874-94F3-8E88384687BA" targetRef="business_review">
      <extensionElements>
        <flowable:executionListener event="start" delegateExpression="${recordProcessUsersListener}"></flowable:executionListener>
      </extensionElements>
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${goOut=='pass'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-AD06CF6C-330A-4248-B69A-DD59F82FCCAD" sourceRef="sid-BAFD954C-7C3F-4BAF-902F-F29E2D01DF0F" targetRef="integrated_reviewer">
      <extensionElements>
        <flowable:executionListener event="start" delegateExpression="${recordProcessUsersListener}"></flowable:executionListener>
      </extensionElements>
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${goOut=='pass'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-E1BDBDE8-F583-4360-88E2-671ABE936AC5" sourceRef="business_review_reject" targetRef="sid-DFEC839C-5FC0-4874-94F3-8E88384687BA">
      <extensionElements>
        <flowable:executionListener event="start" delegateExpression="${parseRecordProcessUsersListener}"></flowable:executionListener>
      </extensionElements>
    </sequenceFlow>
    <sequenceFlow id="sid-486D219F-32A8-459F-885B-F6F0F886907E" sourceRef="integrated_reviewer_reject" targetRef="sid-BAFD954C-7C3F-4BAF-902F-F29E2D01DF0F">
      <extensionElements>
        <flowable:executionListener event="start" delegateExpression="${parseRecordProcessUsersListener}"></flowable:executionListener>
      </extensionElements>
    </sequenceFlow>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_flow_system_publish_approve">
    <bpmndi:BPMNPlane bpmnElement="flow_system_publish_approve" id="BPMNPlane_flow_system_publish_approve">
      <bpmndi:BPMNShape bpmnElement="startEvent1" id="BPMNShape_startEvent1">
        <omgdc:Bounds height="30.0" width="30.0" x="44.99999928474432" y="239.99999618530302"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-1026748D-6967-4AD7-8C63-B592A836CEFE" id="BPMNShape_sid-1026748D-6967-4AD7-8C63-B592A836CEFE">
        <omgdc:Bounds height="80.0" width="100.0" x="119.99999928474432" y="214.99999618530302"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="business_leader" id="BPMNShape_business_leader">
        <omgdc:Bounds height="79.99999999999989" width="100.0" x="285.0" y="214.9999897778041"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-DFEC839C-5FC0-4874-94F3-8E88384687BA" id="BPMNShape_sid-DFEC839C-5FC0-4874-94F3-8E88384687BA">
        <omgdc:Bounds height="40.0" width="40.00000000000006" x="474.99999475479166" y="234.99999276797035"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="business_leader_reject" id="BPMNShape_business_leader_reject">
        <omgdc:Bounds height="80.00000000000001" width="100.00000000000034" x="444.9999814927586" y="14.99999910593037"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="business_review" id="BPMNShape_business_review">
        <omgdc:Bounds height="80.0" width="100.0" x="570.0" y="214.99999618530302"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-BAFD954C-7C3F-4BAF-902F-F29E2D01DF0F" id="BPMNShape_sid-BAFD954C-7C3F-4BAF-902F-F29E2D01DF0F">
        <omgdc:Bounds height="40.0" width="40.0" x="729.9999512732052" y="234.99998977780405"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="integrated_reviewer" id="BPMNShape_integrated_reviewer">
        <omgdc:Bounds height="80.0" width="100.0" x="825.0" y="214.99999618530302"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-3AD37A31-6350-4E8F-AD87-5A9DC554F13B" id="BPMNShape_sid-3AD37A31-6350-4E8F-AD87-5A9DC554F13B">
        <omgdc:Bounds height="40.0" width="40.0" x="951.6249855216595" y="234.99999298155348"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-3580497E-5BA8-4385-99C0-1416DA248181" id="BPMNShape_sid-3580497E-5BA8-4385-99C0-1416DA248181">
        <omgdc:Bounds height="28.0" width="28.0" x="1036.6249855216595" y="240.99999298155348"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="integrated_reviewer_reject" id="BPMNShape_integrated_reviewer_reject">
        <omgdc:Bounds height="80.0" width="100.0" x="921.6249305885318" y="29.99999731779115"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="business_review_reject" id="BPMNShape_business_review_reject">
        <omgdc:Bounds height="80.0" width="100.0" x="699.9999512732052" y="29.99999731779115"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="sid-58884371-B70B-44D7-BFB1-8AE2A0C53147" id="BPMNEdge_sid-58884371-B70B-44D7-BFB1-8AE2A0C53147">
        <omgdi:waypoint x="219.94999928474326" y="254.99999424363665"></omgdi:waypoint>
        <omgdi:waypoint x="284.9999999998895" y="254.99999171752873"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-9FB97B7C-889B-4144-87D6-7C4DA1D52637" id="BPMNEdge_sid-9FB97B7C-889B-4144-87D6-7C4DA1D52637">
        <omgdi:waypoint x="455.248430992648" y="94.94999910593039"></omgdi:waypoint>
        <omgdi:waypoint x="335.9440993243776" y="214.9999897778041"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-AD06CF6C-330A-4248-B69A-DD59F82FCCAD" id="BPMNEdge_sid-AD06CF6C-330A-4248-B69A-DD59F82FCCAD">
        <omgdi:waypoint x="769.5186650104284" y="255.42337789043654"></omgdi:waypoint>
        <omgdi:waypoint x="824.9999999999907" y="255.20059594571143"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-E1BDBDE8-F583-4360-88E2-671ABE936AC5" id="BPMNEdge_sid-E1BDBDE8-F583-4360-88E2-671ABE936AC5">
        <omgdi:waypoint x="699.9999512732052" y="106.40756649576886"></omgdi:waypoint>
        <omgdi:waypoint x="507.03925540658554" y="247.0681738725256"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-35C196CB-BFB5-4312-A8F7-DBD1D22C314A" id="BPMNEdge_sid-35C196CB-BFB5-4312-A8F7-DBD1D22C314A">
        <omgdi:waypoint x="924.9499999999999" y="255.25713743313509"></omgdi:waypoint>
        <omgdi:waypoint x="952.02149328785" y="255.396500747744"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-DD307F56-E6B6-45C3-84A4-185877557175" id="BPMNEdge_sid-DD307F56-E6B6-45C3-84A4-185877557175">
        <omgdi:waypoint x="972.0709255115694" y="235.44593297146344"></omgdi:waypoint>
        <omgdi:waypoint x="971.7326243623617" y="109.94999731779116"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-8EC84DCB-8EE7-41A1-8E53-C24CE5DAD115" id="BPMNEdge_sid-8EC84DCB-8EE7-41A1-8E53-C24CE5DAD115">
        <omgdi:waypoint x="495.4499934236883" y="235.4499914368669"></omgdi:waypoint>
        <omgdi:waypoint x="495.0996100735551" y="94.94999910593039"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-E8F2AB77-08F1-45CF-AB60-A2A4DC11F008" id="BPMNEdge_sid-E8F2AB77-08F1-45CF-AB60-A2A4DC11F008">
        <omgdi:waypoint x="991.1841724614762" y="255.37819810975859"></omgdi:waypoint>
        <omgdi:waypoint x="1036.6252610741478" y="255.08884486581752"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-025B79E4-65D0-444E-924F-EA8498BC595A" id="BPMNEdge_sid-025B79E4-65D0-444E-924F-EA8498BC595A">
        <omgdi:waypoint x="74.9499977747019" y="254.99999618530302"></omgdi:waypoint>
        <omgdi:waypoint x="119.99999928474432" y="254.99999618530302"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-9953D168-E478-4E7B-B3F5-F0D8EDEFFA92" id="BPMNEdge_sid-9953D168-E478-4E7B-B3F5-F0D8EDEFFA92">
        <omgdi:waypoint x="669.9499999999936" y="255.1913731145797"></omgdi:waypoint>
        <omgdi:waypoint x="730.4230291570105" y="255.4230676616094"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-C042E687-D06A-4220-A896-93773CAB57C4" id="BPMNEdge_sid-C042E687-D06A-4220-A896-93773CAB57C4">
        <omgdi:waypoint x="384.9499999999974" y="255.15559819010943"></omgdi:waypoint>
        <omgdi:waypoint x="475.43749437780394" y="255.4374923909826"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-486D219F-32A8-459F-885B-F6F0F886907E" id="BPMNEdge_sid-486D219F-32A8-459F-885B-F6F0F886907E">
        <omgdi:waypoint x="923.9429924099104" y="109.94999731779116"></omgdi:waypoint>
        <omgdi:waypoint x="761.3488750815001" y="246.37610387133287"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-DA50B8A4-9090-4B44-AF9E-FCCC228303CE" id="BPMNEdge_sid-DA50B8A4-9090-4B44-AF9E-FCCC228303CE">
        <omgdi:waypoint x="750.4458972169481" y="235.44593572154693"></omgdi:waypoint>
        <omgdi:waypoint x="750.1076332182829" y="109.94999731779116"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-0006DF50-1657-4462-882E-21DF6D91FC1F" id="BPMNEdge_sid-0006DF50-1657-4462-882E-21DF6D91FC1F">
        <omgdi:waypoint x="514.5187089743372" y="255.4233803937202"></omgdi:waypoint>
        <omgdi:waypoint x="569.999999999996" y="255.2005972154399"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>