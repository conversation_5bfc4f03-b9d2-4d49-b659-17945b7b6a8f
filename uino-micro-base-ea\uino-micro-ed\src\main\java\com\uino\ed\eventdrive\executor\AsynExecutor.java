package com.uino.ed.eventdrive.executor;

import com.uino.ed.eventdrive.event.Event;
import com.uino.ed.eventdrive.event.EventHandleParam;
import com.uino.ed.eventdrive.handle.EventHandle;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;

/**
 * 异步执行器
 */
@Component
public class AsynExecutor extends Thread {

    //待执行队列
    private BlockingQueue<EventHandleParam> eventHandleQueue =new ArrayBlockingQueue<>(100);

    public void execute(EventHandleParam handle) throws InterruptedException {
        eventHandleQueue.put(handle);
    }

    @PostConstruct
    public  void active(){
        this.start();
    }

    protected void doExecute(EventHandle handle, Event event) throws Exception
    {
        handle.handle(event);
    }

    @Override
    public void run() {
        while (true){
            try {
                EventHandleParam eventHandleParam = eventHandleQueue.take();
                doExecute(eventHandleParam.getHandle(), eventHandleParam.getEvent());
            } catch (Exception exp) {
                //log.error("AsynExecutor Run Exception", exp);
            }
        }
    }
}
