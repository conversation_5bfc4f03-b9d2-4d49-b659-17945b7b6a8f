package com.uino.service.sys.microservice.impl;

import java.util.ArrayList;
import java.util.List;

import com.uino.dao.BaseConst;
import com.uino.service.sys.microservice.ITableDataConfigSvc;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import com.binary.core.util.BinaryUtils;
import com.uino.dao.sys.ESTableDataConfigSvc;
import com.uino.bean.cmdb.base.ESTableDataConfigInfo;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
public class TableDataConfigSvc implements ITableDataConfigSvc {

    @Autowired
    ESTableDataConfigSvc configSvc;

    @Override
    public ESTableDataConfigInfo getCIDataConfigInfo(Long domainId,String uid) {
        Assert.notNull(uid, "param error");
        domainId = domainId == null ? BaseConst.DEFAULT_DOMAIN_ID : domainId;
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("domainId",domainId));
        query.must(QueryBuilders.termQuery("uid.keyword", uid));
        List<ESTableDataConfigInfo> list = configSvc.getListByQuery(query);
        if (!BinaryUtils.isEmpty(list)) {
            return list.get(0);
        }
        return ESTableDataConfigInfo.builder().uid(uid).config(new ArrayList<>()).domainId(domainId).build();
    }

    @Override
    public Long saveCIDataConfigInfo(ESTableDataConfigInfo configInfo) {
        if(BinaryUtils.isEmpty(configInfo.getDomainId())){
            configInfo.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
        }
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("domainId",configInfo.getDomainId()));
        query.must(QueryBuilders.termQuery("uid.keyword", configInfo.getUid()));
        List<ESTableDataConfigInfo> list = configSvc.getListByQuery(query);
        if (!BinaryUtils.isEmpty(list)) {
            configInfo.setId(list.get(0).getId());
            configInfo.setCreateTime(list.get(0).getCreateTime());
        }
        return configSvc.saveOrUpdate(configInfo);
    }
}
