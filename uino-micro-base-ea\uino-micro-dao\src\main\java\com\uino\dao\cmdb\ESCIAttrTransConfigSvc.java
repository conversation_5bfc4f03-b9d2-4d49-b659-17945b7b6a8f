package com.uino.dao.cmdb;

import jakarta.annotation.PostConstruct;

import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.uino.dao.AbstractESBaseDao;
import com.uino.dao.ESConst;
import com.uino.bean.cmdb.base.ESCIAttrTransConfig;

@Service
public class ESCIAttrTransConfigSvc extends AbstractESBaseDao<ESCIAttrTransConfig, JSONObject> {

    @Override
    public String getIndex() {
        return ESConst.INDEX_CMDB_ATTR_TRANS_CONFIG;
    }

    @Override
    public String getType() {
        return ESConst.INDEX_CMDB_ATTR_TRANS_CONFIG;
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }
}
