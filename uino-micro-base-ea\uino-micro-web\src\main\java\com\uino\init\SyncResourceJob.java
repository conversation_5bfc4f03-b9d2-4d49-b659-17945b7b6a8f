package com.uino.init;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.annotation.Scheduled;

import com.uino.service.util.sync.SyncProcess;
import com.uino.bean.cmdb.base.ESResource;
import com.uino.api.client.sys.IResourceApiSvc;

import lombok.extern.slf4j.Slf4j;

/**
 * 同步资源任务调度器
 * <p>
 * 
 * 不断获取最近一天的同步资源信息调度资源同步器进行同步，并且以同步器指针为起点向后偏移尽量保障资源不浪费，若发现资源全部读取完毕下一次扫描将在5S后，
 * 频繁处理时则一直下发
 * <p>
 * 若重写该调度器需要注意做好异常处理！！
 * 
 * <AUTHOR>
 *
 */
@Slf4j
@RefreshScope
public class SyncResourceJob {

    /**
     * 上一秒执行过的同步文件id集合
     */
    private static Set<Long> lastSecondSyncIdList = new HashSet<>();

    @Autowired
    private IResourceApiSvc resourceApi;

    @Value("${local.resource.space:}")
    private String localResourceSpace;

    @Scheduled(fixedDelay = 1000L, initialDelay = 1000L * 10)
    public void scan() {
        try {
            Long createTimestamp = SyncProcess.getSyncCreateTime();
            List<ESResource> syncFiles = null;
            syncFiles = resourceApi.getWaitSyncResources(createTimestamp);
            if (syncFiles != null && syncFiles.size() > 0) {
                Set<Long> idList = new HashSet<>();
                for (ESResource syncFile : syncFiles) {
                    if (!lastSecondSyncIdList.contains(syncFile.getId())) {
                        log.info("开始同步文件，文件为：" + syncFile.getPath());
                        try {
                            SyncProcess.sync(syncFile, localResourceSpace);
                        } catch (Exception e) {
                            log.error("加入同步文件线程异常", e);
                        }
                        idList.add(syncFile.getId());
                        log.info("同步文件成功");
                    }
                }
                if (idList.isEmpty() && createTimestamp != null) {
                    SyncProcess.setSyncCreateTime(createTimestamp + 1L);
                }
                lastSecondSyncIdList = idList;

            }
        } catch (Exception e) {
            log.error("下发同步异常(大概率由于查询出错)", e);
        }
    }

    // @PostConstruct
    public void init() {
        log.info("文件同步器创建成功");
        new Thread(new Runnable() {
            @Override
            public void run() {
                while (true) {
                    Long createTime = SyncProcess.getSyncCreateTime();
                    List<ESResource> syncFiles = null;
                    try {
                        syncFiles = resourceApi.getWaitSyncResources(createTime);
                    } catch (Exception e) {
                        log.error("拉取同步文件列表异常");
                    }

                    if (syncFiles == null || syncFiles.size() <= 0) {
                        try {
                            Thread.sleep(5000L);
                        } catch (InterruptedException e) {
                            log.error("休眠同步器异常", e);
                        }
                    }
                    for (ESResource syncFile : syncFiles) {
                        try {
                            SyncProcess.sync(syncFile, localResourceSpace);
                        } catch (Exception e) {
                            log.error("加入同步文件线程异常", e);
                        }
                    }
                }

            }
        }).start();

    }
}
