package com.uino.ed.eventdrive.event;

import com.uino.ed.eventdrive.executor.ExecuteType;
import lombok.Data;

import java.io.Serializable;

@Data
public class Event<T> implements Serializable {

    private static final long serialVersionUID = 7169544389373052853L;

    /**
     * 事件ID
     */
    private String eventID;

    /**
     * 事件类型
     */
    private String eventType;

    /**
     * 触发方式 1:自动触发, 2:手动触发
     */
    private Integer triggerType;

    /**
     * 事件名称
     */
    private String eventName;

    /**
     * 执行方式
     */
    private ExecuteType executeType;

    /**
     * 事件规则
     */
    private T apRule;

    private long timestamp;
}
