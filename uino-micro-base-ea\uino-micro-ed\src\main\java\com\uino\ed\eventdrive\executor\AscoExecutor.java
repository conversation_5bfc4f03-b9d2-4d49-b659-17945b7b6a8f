package com.uino.ed.eventdrive.executor;

import com.uino.ed.eventdrive.event.Event;
import com.uino.ed.eventdrive.handle.EventHandle;
import org.springframework.stereotype.Component;

/**
 * 异步并发执行
 */
@Component
public class AscoExecutor extends AsynExecutor {

    protected void doExecute(EventHandle handle, Event event) throws Exception
    {
        new ConcWorker(handle, event).start();
    }
}
