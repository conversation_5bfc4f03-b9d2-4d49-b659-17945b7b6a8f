package com.uino.bean.cmdb.base.dataset.chart;

/**
 * <AUTHOR>
 * @data 2019/12/26 10:56.
 */
public enum StatisticalEnum {
    // 统计(去重)、求和、统计(不去重)
    DISTINCT_COUNT(0),
    SUM(1), COUNT(2);

    StatisticalEnum(int value) {
        this.value = value;
    }

    private int value;

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }}
