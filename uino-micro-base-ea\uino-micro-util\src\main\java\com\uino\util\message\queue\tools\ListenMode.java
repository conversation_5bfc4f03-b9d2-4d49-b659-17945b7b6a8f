package com.uino.util.message.queue.tools;

/**
 * listen mode, the default is Single, single consumption
 */
public enum ListenMode {

    /**
     * Single monitor mode.
     */
    SINGLE,

    /**
     * Batch monitoring mode for map, represents the received batch data format is map.
     */
    BATCH,

    /**
     * Batch monitoring mode for list, represents the received data format is list.
     */
    LIST_BATCH,
}