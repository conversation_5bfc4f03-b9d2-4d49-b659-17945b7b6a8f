package com.uino.api.client.cmdb.local;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.binary.core.io.Resource;
import com.uino.api.client.cmdb.IDataSetApiSvc;
import com.uino.bean.cmdb.base.dataset.DataSetMallApi;
import com.uino.bean.cmdb.base.dataset.DataSetMallApiRelationRule;
import com.uino.bean.cmdb.base.dataset.DataSetMallApiType;
import com.uino.bean.cmdb.base.dataset.batch.DataSetTableResult;
import com.uino.bean.cmdb.base.dataset.chart.Chart;
import com.uino.bean.cmdb.business.dataset.*;
import com.uino.bean.dataset.base.DataSetPathInfoVo;
import com.uino.bean.dataset.base.DataSetThumbnailDTO;
import com.uino.bean.tp.query.MetricAttrValQueryDTO;
import com.uino.bean.tp.query.QueryDataTableDTO;
import com.uino.bean.tp.query.TpRuleReqDTO;
import com.uino.dao.BaseConst;
import com.uino.service.cmdb.dataset.microservice.IDataSetSvc;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @Classname DataSetSvcLocal
 * @Description TODO
 * @Date 2020/5/29 10:13
 * @Created by sh
 */
@Service
public class DataSetSvcLocal implements IDataSetApiSvc {

    @Autowired
    private IDataSetSvc dataSetService;

    @Override
    public Long saveOrUpdateDataSet(JSONObject json, Boolean ifExeBatchProcess) {
        return dataSetService.saveOrUpdateDataSet(json, ifExeBatchProcess);
    }

    @Override
    public boolean delete(Long id) {
        return dataSetService.delete(id);
    }

    @Override
    public List<JSONObject> findDataSet(String name, boolean isMyself, String url, List<DataSetMallApiType> typeList) {
        return dataSetService.findDataSet(name, isMyself, url, typeList);
    }

    @Override
    public JSONObject findDataSetById(Long id) {
        return dataSetService.findDataSetById(id, false);
    }

    @Override
    public JSONObject findDataSetById(Long id, boolean isCheck) {
        return dataSetService.findDataSetById(id, isCheck);
    }


    @Override
    public JSONObject execute(String language, Long id, JSONObject body) {
        return dataSetService.execute(BaseConst.DEFAULT_DOMAIN_ID, language, id, body);
    }

    @Override
    public JSONObject execute(JSONObject body) {
        return dataSetService.execute(BaseConst.DEFAULT_DOMAIN_ID, body);
    }

    @Override
    public JSONObject execute(Long domainId, String language, Long id, JSONObject body) {
        return dataSetService.execute(domainId, language, id, body);
    }

    @Override
    public JSONObject realTimeExecute(String language, Long id, JSONObject body) {
        return dataSetService.realTimeExecute(BaseConst.DEFAULT_DOMAIN_ID, language, id, body);
    }

    @Override
    public JSONObject realTimeExecute(Long domainId, String language, Long id, JSONObject body) {
        return dataSetService.realTimeExecute(domainId, language, id, body);
    }

    @Override
    public boolean shareDataSet(Long id, int shareLevel) {
        return dataSetService.shareDataSet(id, shareLevel);
    }

    @Override
    public List<DataSetMallApiRelationRule> findAllRelationDateSet() {
        return dataSetService.findAllRelationDateSet(BaseConst.DEFAULT_DOMAIN_ID);
    }

    @Override
    public List<DataSetMallApiRelationRule> findAllRelationDateSet(Long domainId) {
        return null;
    }

    @Override
    public void updateMallApiExeResult(DataSetMallApiRelationRule dataSetRelationRule, Map<Long, FriendInfo> friendInfoMap) {
        dataSetService.updateMallApiExeResult(dataSetRelationRule, friendInfoMap);
    }

    @Override
    public List<Map<String, Object>> getDataSetSheets(Long dataSetId) {
        return dataSetService.getDataSetSheets(dataSetId);
    }

    @Override
    public Map<String, Long> getDataSetLineCount(List<String> dataSetIds) {
        return dataSetService.getDataSetLineCount(dataSetIds);
    }

    @Override
    public JSONObject queryRuleLegitimateCi(Integer pageNum, Integer pageSize, Long dataSetId,String name, String like) {
        return dataSetService.queryRuleLegitimateCi(pageNum, pageSize, dataSetId, name, like);
    }

    @Override
    public FriendInfo queryFriendByStartCiId(Long dataSetId, Long startCiId) {
        return dataSetService.queryFriendByStartCiId(dataSetId, startCiId);
    }

    @Override
    public FriendInfo queryFriendByStartCiIdAndTargetClass(Long dataSetId, Long startCiId, Set<Long> targetClassIds) {
        return dataSetService.queryFriendByStartCiIdAndTargetClass(dataSetId, startCiId, targetClassIds);
    }

    @Override
    public Map<Long, Integer> queryRuleNodeCiNum(Long dataSetId) {
        return dataSetService.queryRuleNodeCiNum(dataSetId);
    }

    @Override
    public RltRuleTableData queryDisassembleFriendInfoDataByPath(Long dataSetId, Long startCiId) {
        return dataSetService.queryDisassembleFriendInfoDataByPath(dataSetId, startCiId);
    }

    @Override
    public JSONObject countStatistics(Long dataSetId, Chart chart) {
        return dataSetService.countStatistics(dataSetId, chart);
    }

    @Override
    public List<JSONObject> getQueryCondition(Long dataSetId, String sheetId) {
        return dataSetService.getQueryCondition(dataSetId, sheetId);
    }

    @Override
    public DataSetExeResultSheetPage queryDataSetResultBySheet(Long dataSetId, String sheetId, int pageNum, int pageSize, String sortCol, boolean isDesc, JSONArray condition, String userCode) {
        return dataSetService.queryDataSetResultBySheet(BaseConst.DEFAULT_DOMAIN_ID, dataSetId, sheetId, pageNum, pageSize, sortCol, isDesc, condition, userCode);
    }

    @Override
    public List<DataSetExeResultSheetPage> queryDataSetResultList(List<Long> dataSetIds, String sheetId, String sortCol, boolean isDesc) {
        return dataSetService.queryDataSetResultList(dataSetIds, sheetId, sortCol, isDesc);
    }

    @Override
    public DataSetExeResultSheetPage queryDataSetResultBySheet(Long domainId, Long dataSetId, String sheetId, int pageNum, int pageSize, String sortCol, boolean isDesc, JSONArray condition, String userCode) {
        return dataSetService.queryDataSetResultBySheet(domainId, dataSetId, sheetId, pageNum, pageSize, sortCol, isDesc, condition, userCode);
    }

    @Override
    public List<DataSetExeResultSheetPage> getResultUsingRule(JSONObject rule, Integer limit) {
        return dataSetService.getResultUsingRule(rule, limit);
    }

    @Override
    public List<DataSetExeResultSheetPage> findDataSetRuleList(JSONObject rule, Integer limit) {
        return dataSetService.findDataSetRuleList(rule, limit);
    }

    @Override
    public Resource downloadSheetData(Long dataSetId, String sheetId, String sortCol, boolean isDesc, JSONArray condition) {
        return dataSetService.downloadSheetData(BaseConst.DEFAULT_DOMAIN_ID, dataSetId, sheetId, sortCol, isDesc, condition);
    }

    @Override
    public Resource downloadSheetData(Long domainId, Long dataSetId, String sheetId, String sortCol, boolean isDesc, JSONArray condition) {
        return dataSetService.downloadSheetData(domainId, dataSetId, sheetId, sortCol, isDesc, condition);
    }

    @Override
    public int isTaskRunning(Long dataSetId) {
        return dataSetService.isTaskRunning(dataSetId);
    }

    @Override
    public String getCode(String jsonStr) throws Exception {
        return dataSetService.getCode(jsonStr);
    }

    @Override
    public void checkOperate(String userCode, DataSetMallApi dataSetMallApi) {
        dataSetService.checkOperate(userCode, dataSetMallApi);
    }

    @Override
    public List<Map<String, Object>> groupDataSetMallApiLogCount() {
        return dataSetService.groupDataSetMallApiLogCount(BaseConst.DEFAULT_DOMAIN_ID);
    }

    @Override
    public List<Map<String, Object>> groupDataSetMallApiLogCount(Long domainId) {
        return dataSetService.groupDataSetMallApiLogCount(domainId);
    }

    @Override
    public List<String> getTpMetricLabelDTOList(TpRuleReqDTO ruleReqDTO) {
        return dataSetService.getTpMetricLabelDTOList(ruleReqDTO);
    }

    @Override
    public List<String> queryMetricAttrValue(MetricAttrValQueryDTO query) {
        return dataSetService.queryMetricAttrValue(query);
    }

    @Override
    public FriendBatchInfo queryFriendByStartCiIds(List<FriendInfoRequestDto> body) {

        return dataSetService.queryFriendByStartCiIds(body);
    }

    @Override
    public String updateThumbnail(DataSetThumbnailDTO body) {
        return dataSetService.updateThumbnail(body);
    }

    @Override
    public List<DataSetPathInfoVo> getPathInfo(List<Long> dataSetId) {
        return dataSetService.getPathInfo(dataSetId);
    }
    @Override
    public void updateDataSetById(Long id) {
        dataSetService.updateDataSetById(id);
    }

    @Override
    public List<DataSetTableResult> queryCiTableList(QueryDataTableDTO queryDataTableDTO) {
        return dataSetService.queryCiTableList(queryDataTableDTO);
    }

    @Override
    public ResponseEntity<byte[]> ciTableListExport(List<DataSetTableResult> ret, QueryDataTableDTO body) {
        return dataSetService.ciTableListExport(ret, body);
    }
}
