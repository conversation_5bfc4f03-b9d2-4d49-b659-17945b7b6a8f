package com.uinnova.product.eam.workable;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.uinnova.product.eam.feign.workable.entity.*;
import com.uinnova.product.eam.workable.service.FilterUserService;
import com.uinnova.product.eam.workable.service.FlowableService;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.HistoryService;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.flowable.engine.history.HistoricDetail;
import org.flowable.engine.repository.Deployment;
import org.flowable.engine.runtime.ActivityInstance;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.task.api.Task;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.flowable.variable.api.history.HistoricVariableInstance;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.io.ClassPathResource;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.io.FileInputStream;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class EamWorkableApplicationTests {

    @Autowired
    FlowableService flowableService;

    @Autowired
    ObjectMapper objectMapper;

    @Resource
    private TaskService taskService;

    @Resource
    private RuntimeService runtimeService;

    @Resource
    private RepositoryService repositoryService;

    @Resource
    private HistoryService historyService;

    @Resource
    private FilterUserService filterUserService;

    @Test
    public void contextLoads() throws Exception {
        FileInputStream fileInputStream = new FileInputStream("D:\\workspace\\fuxi\\eam\\eam-workable\\src\\main\\resources\\processes\\multipart_demo.bpmn20.xml");
        FileInputStream fileImageInputStream = new FileInputStream("D:\\workspace\\fuxi\\eam\\eam-workable\\src\\main\\resources\\processes\\multipart_demo.png");
        Deployment uinoApprovedeployment = flowableService.deployProcessDefinition("优锘请假流程"
                , "multipart_demo.bpmn20.xml", fileInputStream
                , "multipart_demo.png", fileImageInputStream);
        Assert.assertNotNull(uinoApprovedeployment);
    }

    @Test
    public void starProcess() throws Exception {
        HashMap<String, Object> objectObjectMap = new HashMap<>();
        objectObjectMap.put("name", "lichong");
        ProcessRequest processRequest = new ProcessRequest();
        processRequest.setProcessDefinitionKey("multipart_demo");
        processRequest.setBusinessKey("ownercode001");
        processRequest.setRouterVariables(objectObjectMap);
        processRequest.setUserId("lichong");
        processRequest.setOwner("lichong");
        Task task = flowableService.startProcessBindAssignee(processRequest);
        System.out.println(task.getId());
        Assert.assertNotNull(task);
    }


    @Test
    public void testGuotouApprove() {

        ProcessRequest processRequest = new ProcessRequest();
        processRequest.setProcessDefinitionKey("guotou_technical_scheme_approve_with_sub");
        processRequest.setProcessInstanceName("国投测试009_sub");
        processRequest.setBusinessKey("国投测试buskey_0003_sub");
        processRequest.setUserId("lichong");
        processRequest.setOwner("lichong");
        Task task = flowableService.startProcessBindAssignee(processRequest);

        TaskRequest taskRequest = new TaskRequest();
        taskRequest.setAction(FLOWACTION.ACCETP);
        taskRequest.setTaskId(task.getId());
        List<ChildProcessRequest> childProcessRequests = Lists.newArrayList();

        ChildProcessRequest childProcessRequest = new ChildProcessRequest();
        childProcessRequest.setChildBusinessKey("国投测试buskey_0003_sub_lisi");
        Map<String, Object> stringObjectHashMap1 = new HashMap<>();
        stringObjectHashMap1.put("assignee", "lisi");
        childProcessRequest.setChildVariable(stringObjectHashMap1);
        childProcessRequests.add(childProcessRequest);

        ChildProcessRequest childProcessRequest1 = new ChildProcessRequest();
        childProcessRequest1.setChildBusinessKey("国投测试buskey_0003_sub_zhangsan");
        Map<String, Object> stringObjectHashMap2 = new HashMap<>();
        stringObjectHashMap2.put("assignee", "zhangsan");
        childProcessRequest1.setChildVariable(stringObjectHashMap2);
        childProcessRequests.add(childProcessRequest1);

        ChildProcessRequest childProcessRequest2 = new ChildProcessRequest();
        childProcessRequest2.setChildBusinessKey("国投测试buskey_0003_sub_wangwu");
        Map<String, Object> stringObjectHashMap3 = new HashMap<>();
        stringObjectHashMap3.put("assignee", "wangwu");
        childProcessRequest2.setChildVariable(stringObjectHashMap3);
        childProcessRequests.add(childProcessRequest2);

        ChildProcessRequest childProcessRequest3 = new ChildProcessRequest();
        childProcessRequest3.setChildBusinessKey("国投测试buskey_0003_sub_zhaoliu");
        Map<String, Object> stringObjectHashMap4 = new HashMap<>();
        stringObjectHashMap4.put("assignee", "zhaoliu");
        childProcessRequest3.setChildVariable(stringObjectHashMap4);
        childProcessRequests.add(childProcessRequest3);

        ChildProcessRequest childProcessRequest5 = new ChildProcessRequest();
        childProcessRequest5.setChildBusinessKey("国投测试buskey_0003_sub_admin");
        Map<String, Object> stringObjectHashMap6 = new HashMap<>();
        stringObjectHashMap6.put("assignee", "admin");
        childProcessRequest5.setChildVariable(stringObjectHashMap6);
        childProcessRequests.add(childProcessRequest5);

        HashMap<String, Object> stringObjectHashMap = new HashMap<>();
        stringObjectHashMap.put("orgApprove", "countersign");
        taskRequest.setRouterVariables(stringObjectHashMap);
        taskRequest.setChildProcessRequestList(childProcessRequests);
        flowableService.completeTask(taskRequest);

        List<Task> list = taskService.createTaskQuery().processInstanceId(task.getProcessInstanceId()).list();

        log.info("第一次提交任务数:{}", list);

        //前三个驳回
        for(int i = 0;i<3;i++) {
            TaskRequest taskRequest1 = new TaskRequest();
            taskRequest1.setAction(FLOWACTION.REJECT);
            taskRequest1.setTaskId(list.get(i).getId());
            TaskResponse taskResponse = flowableService.completeTask(taskRequest1);
        }

        //后两个同意
        for (int i=3;i<list.size();i++) {
            TaskRequest taskRequest1 = new TaskRequest();
            taskRequest1.setAction(FLOWACTION.ACCETP);
            taskRequest1.setTaskId(list.get(i).getId());
            flowableService.completeTask(taskRequest1);
        }

        List<Task> list1 = taskService.createTaskQuery().processInstanceId(task.getProcessInstanceId()).taskAssignee("lichong").list();

        log.info("lichong任务数:{}", list1);

        //第一个被驳回的重新提交审批
        TaskRequest taskRequest1 = new TaskRequest();
        taskRequest1.setAction(FLOWACTION.ACCETP);
        taskRequest1.setTaskId(list1.get(0).getId());
        flowableService.completeTask(taskRequest1);

        List<Task> list2 = taskService.createTaskQuery().processInstanceId(task.getProcessInstanceId()).list();
        log.info("驳回后重新提交任务数:{}", list2);
        list2.forEach(task1 -> {
            //全部重新提交并通过
            TaskRequest taskRequest2 = new TaskRequest();
            taskRequest2.setAction(FLOWACTION.ACCETP);
            taskRequest2.setTaskId(task1.getId());
            flowableService.completeTask(taskRequest2);
        });




    }





    @Test
    public void queryTaskByProcessInstanceId() throws Exception {
        List<ProcessInstance> uino_leave_approve2 = flowableService.getProcessInstanceByprocessDefinitionKey("multipart_demo");
        for (ProcessInstance processInstance : uino_leave_approve2) {
            List<Task> list = taskService.createTaskQuery().processInstanceId(processInstance.getProcessInstanceId()).list();
            for (Task task : list) {
                System.out.println(task.getId());
                System.out.println(task.getProcessInstanceId());
                System.out.println(task.getAssignee());
            }
        }
        Assert.assertNotNull(uino_leave_approve2);
    }

    @Test
    public void getProcessInstanceByprocessDefinitionKeyTest() {
        List<ProcessInstance> processInstances = flowableService.getProcessInstanceByprocessDefinitionKey("uino_leave_approve2");
        for (ProcessInstance processInstance : processInstances) {
            System.out.println(processInstance.getProcessInstanceId());
        }
        Assert.assertNotNull(processInstances);
    }

    @Test
    public void genProcessImage() throws Exception {
        String processInstanceId = "0bd1670b-9adf-11ec-8575-0c9a3cab56f3";
        String path = flowableService.genProcessDiagramByProcessId(processInstanceId);
        Assert.assertNotNull(path);
        log.info(path);
    }

    @Test
    public void deleteProcessInstance() {
        List<ProcessInstance> processInstanceByprocessDefinitionKey = flowableService.getProcessInstanceByprocessDefinitionKey("multipart_demo");
        for (ProcessInstance processInstance : processInstanceByprocessDefinitionKey) {
            flowableService.deleteProcessInstanceById(processInstance.getProcessInstanceId(), "");
        }
        Assert.assertNotNull(processInstanceByprocessDefinitionKey);
    }

    @Test
    public void queryTaskByUserId() {
        PageEntityInfo<TaskResponse> taskResponseListByUserPage = flowableService.getTaskResponseListByUser("lichong",1,11);
        List<TaskResponse> taskResponseListByUser = taskResponseListByUserPage.getList();
        for (TaskResponse task : taskResponseListByUser) {
            System.out.println(task.getTaskId());
            System.out.println(task.getProcessInstanceId());
        }
        Assert.assertNotNull(taskResponseListByUserPage);
    }

    @Test
    public void completeTaskTest() {
        TaskRequest taskRequest = new TaskRequest();
        taskRequest.setTaskId("");
        taskRequest.setAction(FLOWACTION.ACCETP);
        taskRequest.setRouterVariables(new HashMap<>());
        taskRequest.setNextUserIds("zhangqiang,lucong");
        TaskResponse taskResponse = flowableService.completeTask(taskRequest);
        Assert.assertNotNull(taskResponse);
    }

    @Test
    public void deletask() {
        taskService.deleteTask("48d34fda-9521-11ec-9db4-0c9a3cab56f3");
        Assert.assertTrue(true);
    }

    @Test
    public void deleteAll() {
        List<ProcessInstance> list1 = runtimeService.createProcessInstanceQuery().list();
        for (ProcessInstance processInstance : list1) {
            runtimeService.deleteProcessInstance(processInstance.getProcessInstanceId(), "");
        }
        List<Deployment> list = repositoryService.createDeploymentQuery().list();
        for (Deployment deployment : list) {
            System.out.println(deployment.getId());
            repositoryService.deleteDeployment(deployment.getId());
        }
        Assert.assertNotNull(list1);
    }

    /**
     * 综合案例
     *
     * @throws Exception
     */
    @Test
    public void singleMutipartDemoTest() throws Exception {

//        deleteAll();
//        //部署流程
//        InputStream fileInputStream = new ClassPathResource("processes/single_mutipart_demo.bpmn20.xml").getInputStream();
//        InputStream fileImageInputStream = new ClassPathResource("processes/single_mutipart_demo.png").getInputStream();
//        Deployment uinoApprovedeployment = flowableService.deployProcessDefinition("单任务和多任务综合示例"
//                , "single_mutipart_demo.bpmn20.xml", fileInputStream
//                , "single_mutipart_demo.png", fileImageInputStream);
        //发起流程，创建人lichong
        String owner = "lichong";
        HashMap<String, Object> objectObjectMap = new HashMap<>();
        objectObjectMap.put("name", "lichong");
        ProcessRequest processRequest = new ProcessRequest();
        processRequest.setProcessDefinitionKey("single_mutipart_demo");
        processRequest.setBusinessKey("ownercode001");
        processRequest.setRouterVariables(objectObjectMap);
        processRequest.setUserId(owner);
        processRequest.setOwner(owner);
        Task task = flowableService.startProcessBindAssignee(processRequest);
        String processInstanceId = task.getProcessInstanceId();
        log.info("启动流程成功,任务id是：{},流程实例id：{}", task.getId(), task.getProcessInstanceId());
        log.info("发起人{}，第一个任务被{}处理", owner, processRequest.getUserId());

        //多任务1审批人 mutipartUser1
        String mutipartUser1 = "zhangqiang,lucong";
        TaskRequest taskRequest = new TaskRequest();
        taskRequest.setTaskId(task.getId());
        taskRequest.setAction(FLOWACTION.ACCETP);
        taskRequest.setRouterVariables(new HashMap<>());
        taskRequest.setNextUserIds(mutipartUser1);
        flowableService.completeTask(taskRequest);
        List<TaskResponse> taskResponseList = flowableService.getTaskListByProcessInstanceId(processInstanceId);
        log.info("任务数量：{}", taskResponseList.size());
        for (TaskResponse taskResponse : taskResponseList) {
            log.info("处理人：{}", taskResponse.getUserId());
        }

        //多任务1处理
        for (String s : mutipartUser1.split(",")) {
            PageEntityInfo<TaskResponse> taskResponseListByUserPage = flowableService.getTaskResponseListByUser(s, 1, 10);
            List<TaskResponse> taskResponseListByUser = taskResponseListByUserPage.getList();
            if (!CollectionUtils.isEmpty(taskResponseListByUser)) {
                for (TaskResponse taskResponse : taskResponseListByUser) {
                    log.info("{}处理人正在处理", taskResponse.getUserId());
                    TaskRequest tmpTaskRequest = new TaskRequest();
                    tmpTaskRequest.setTaskId(taskResponse.getTaskId());
                    tmpTaskRequest.setAction(FLOWACTION.ACCETP);
                    tmpTaskRequest.setRouterVariables(new HashMap<>());
                    tmpTaskRequest.setNextUserIds("guanyuhao");
                    flowableService.completeTask(tmpTaskRequest);
                }
            }
        }

        //单任务2处理
        PageEntityInfo<TaskResponse> guanyuhaoPage = flowableService.getTaskResponseListByUser("guanyuhao", 1, 10);
        List<TaskResponse> guanyuhao = guanyuhaoPage.getList();
        log.info("任务数量：{}", guanyuhao.size());
        for (TaskResponse taskResponse : guanyuhao) {
            log.info("处理人：{}", taskResponse.getUserId());
        }
        //多任务处理人2
        String mutipartUser2 = "wangchunlei,yangxue";
        for (TaskResponse taskResponse : guanyuhao) {
            TaskRequest tmpTaskRequest = new TaskRequest();
            tmpTaskRequest.setTaskId(taskResponse.getTaskId());
            tmpTaskRequest.setAction(FLOWACTION.ACCETP);
            tmpTaskRequest.setRouterVariables(new HashMap<>());
            tmpTaskRequest.setNextUserIds(mutipartUser2);
            flowableService.completeTask(tmpTaskRequest);
        }

        List<TaskResponse> taskResponseList2 = flowableService.getTaskListByProcessInstanceId(processInstanceId);
        log.info("任务数量：{}", taskResponseList2.size());
        for (TaskResponse taskResponse : taskResponseList2) {
            log.info("处理人：{}", taskResponse.getUserId());
        }

        for (String s : mutipartUser2.split(",")) {
            PageEntityInfo<TaskResponse> taskResponseListByUserPage = flowableService.getTaskResponseListByUser(s, 1, 10);
            List<TaskResponse> taskResponseListByUser = taskResponseListByUserPage.getList();
            if (!CollectionUtils.isEmpty(taskResponseListByUser)) {
                for (TaskResponse taskResponse : taskResponseListByUser) {
                    log.info("{}处理人正在处理", taskResponse.getUserId());
                    TaskRequest tmpTaskRequest = new TaskRequest();
                    tmpTaskRequest.setTaskId(taskResponse.getTaskId());
                    tmpTaskRequest.setAction(FLOWACTION.ACCETP);
                    tmpTaskRequest.setRouterVariables(new HashMap<>());
                    tmpTaskRequest.setNextUserIds("baijing");
                    flowableService.completeTask(tmpTaskRequest);
                }
            }
        }

        List<TaskResponse> baijing = flowableService.getTaskResponseListByUser("baijing", 1, 10).getList();
        for (TaskResponse taskResponse : baijing) {
            log.info("{}处理人正在处理", taskResponse.getUserId());
            TaskRequest tmpTaskRequest = new TaskRequest();
            tmpTaskRequest.setTaskId(taskResponse.getTaskId());
            tmpTaskRequest.setAction(FLOWACTION.ACCETP);
            tmpTaskRequest.setRouterVariables(new HashMap<>());
            tmpTaskRequest.setNextUserIds("baijing");
            flowableService.completeTask(tmpTaskRequest);
        }

        List<TaskResponse> taskListByProcessInstanceId = flowableService.getTaskListByProcessInstanceId(processInstanceId);
        if (CollectionUtils.isEmpty(taskListByProcessInstanceId)) {
            log.info("流程结束");

        } else {
            log.info("流程未结束");
        }
        Assert.assertNotNull(taskListByProcessInstanceId);
//        deleteAll();
    }


    @Test
    public void testhistory1() {
        String processInstenceId = "f24db6a1-9869-11ec-9e8b-0c9a3cab56f3";
        List<HistoricTaskInstance> list = historyService.createHistoricTaskInstanceQuery().processInstanceId(processInstenceId).list();
        List<HistoricVariableInstance> list2 = historyService.createHistoricVariableInstanceQuery().processInstanceId(processInstenceId).list();
        for (HistoricTaskInstance historicTaskInstance : list) {
            log.info(historicTaskInstance.getAssignee());
            log.info(historicTaskInstance.getName());
            log.info("{}", historicTaskInstance.getProcessVariables());
            log.info(historicTaskInstance.getId());
        }
        HashMap< String,  List<HistoricDetail>> map1 = Maps.newHashMap();
        for (HistoricVariableInstance historicDetail : list2) {
            String taskId = historicDetail.getTaskId();
            if (taskId != null) {
                log.info(historicDetail.getTaskId());
                log.info(historicDetail.getVariableName());
                log.info("{}", historicDetail.getValue());
            }
        }
        System.out.println("aa");
        Assert.assertNotNull(list2);
    }


    @Test
    public void taskHistoryListsTest() {
        String processInstanceId = "c4ab2f8f-9863-11ec-a80c-0c9a3cab56f3";
        List<HistoryTaskResponse> historyTaskResponseList = flowableService.getHistoryTaskDataByPorcessInstanceId(processInstanceId);
        for (HistoryTaskResponse historyTaskResponse : historyTaskResponseList) {
            log.info("{}", historyTaskResponse);
        }
        Assert.assertNotNull(historyTaskResponseList);
    }

    /**
     * 仓颉审批逻辑处理
     */
    @Test
    public void cjApproveTest() throws Exception {

//        //部署流程
        InputStream fileInputStream = new ClassPathResource("processes/cj_technical_scheme_approve.xml").getInputStream();
        InputStream fileImageInputStream = new ClassPathResource("processes/cj_technical_scheme_approve.png").getInputStream();
        Deployment uinoApprovedeployment = flowableService.deployProcessDefinition("仓颉技术方案审批流程"
                , "仓颉技术方案审批流程.bpmn20.xml", fileInputStream
                , "仓颉技术方案审批流程.png", fileImageInputStream);

        //发起流程，发起人lichong
        String owner = "lichong";
        ProcessRequest processRequest = new ProcessRequest();
        processRequest.setProcessDefinitionKey("cj_technical_scheme_approve");
        processRequest.setBusinessKey("technical_00928");
        processRequest.setUserId(owner);
        processRequest.setOwner(owner);
        Task task = flowableService.startProcessBindAssignee(processRequest);
        String processInstanceId = task.getProcessInstanceId();
        log.info("启动流程成功,任务id是：{},流程实例id：{}", task.getId(), task.getProcessInstanceId());
        log.info("发起人{}，第一个任务被{}处理", owner, processRequest.getUserId());

        //一级审批 mutipartUser1
        String mutipartUser1 = "fanshaolong,lucong,changhu,zhangqiang";
        //二级审批 mutipartUser1
        String mutipartUser2 = "wangchunlei,yangxue,fulu,wuyinling";
        //三级审批 mutipartUser1
        String mutipartUser3 = "songmingxue,wenshuhui,zhoudinghai,fanliu";
        TaskRequest taskRequest = new TaskRequest();
        taskRequest.setTaskId(task.getId());
        taskRequest.setAction(FLOWACTION.ACCETP);
        taskRequest.setRouterVariables(new HashMap<>());
        taskRequest.setNextUserIds(mutipartUser1);
        flowableService.completeTask(taskRequest);

        //一级审批处理
        log.info("一级审批处理");
        List<TaskResponse> taskResponseList = flowableService.getTaskListByProcessInstanceId(processInstanceId);
        log.info("任务数量：{}", taskResponseList.size());
        for (TaskResponse taskResponse : taskResponseList) {
            log.info("处理人：{}", taskResponse.getUserId());
        }
        for (String s : mutipartUser1.split(",")) {
            List<TaskResponse> taskResponseListByUser = flowableService.getTaskResponseListByUser(s, 1, 10).getList();
            if (!CollectionUtils.isEmpty(taskResponseListByUser)) {
                for (TaskResponse taskResponse : taskResponseListByUser) {
                    log.info("{}处理人正在处理", taskResponse.getUserId());
                    TaskRequest tmpTaskRequest = new TaskRequest();
                    tmpTaskRequest.setTaskId(taskResponse.getTaskId());
                    tmpTaskRequest.setAction(FLOWACTION.ACCETP);
                    tmpTaskRequest.setRouterVariables(new HashMap<>());
                    tmpTaskRequest.setNextUserIds(mutipartUser2);
                    flowableService.completeTask(tmpTaskRequest);
                }
            }
        }

        //二级审批处理
        log.info("二级审批处理");
        List<TaskResponse> taskResponseList2 = flowableService.getTaskListByProcessInstanceId(processInstanceId);
        log.info("任务数量：{}", taskResponseList2.size());
        for (TaskResponse taskResponse : taskResponseList2) {
            log.info("处理人：{}", taskResponse.getUserId());
        }

        for (String s : mutipartUser2.split(",")) {
            List<TaskResponse> taskResponseListByUser = flowableService.getTaskResponseListByUser(s, 1, 10).getList();
            if (!CollectionUtils.isEmpty(taskResponseListByUser)) {
                for (TaskResponse taskResponse : taskResponseListByUser) {
                    log.info("{}处理人正在处理", taskResponse.getUserId());
                    TaskRequest tmpTaskRequest = new TaskRequest();
                    tmpTaskRequest.setTaskId(taskResponse.getTaskId());
                    tmpTaskRequest.setAction(FLOWACTION.ACCETP);
                    tmpTaskRequest.setRouterVariables(new HashMap<>());
                    tmpTaskRequest.setNextUserIds(mutipartUser3);
                    flowableService.completeTask(tmpTaskRequest);
                }
            }
        }

        //三级审批处理
        log.info("三级审批处理");
        List<TaskResponse> taskResponseList3 = flowableService.getTaskListByProcessInstanceId(processInstanceId);
        log.info("任务数量：{}", taskResponseList3.size());
        for (TaskResponse taskResponse : taskResponseList3) {
            log.info("处理人：{}", taskResponse.getUserId());
        }

        for (String s : mutipartUser3.split(",")) {
            List<TaskResponse> taskResponseListByUser = flowableService.getTaskResponseListByUser(s, 1, 10).getList();
            if (!CollectionUtils.isEmpty(taskResponseListByUser)) {
                for (TaskResponse taskResponse : taskResponseListByUser) {
                    log.info("{}处理人正在处理", taskResponse.getUserId());
                    TaskRequest tmpTaskRequest = new TaskRequest();
                    tmpTaskRequest.setTaskId(taskResponse.getTaskId());
                    tmpTaskRequest.setAction(FLOWACTION.ACCETP);
                    tmpTaskRequest.setRouterVariables(new HashMap<>());
                    tmpTaskRequest.setNextUserIds(mutipartUser2);
                    flowableService.completeTask(tmpTaskRequest);
                }
            }
        }
        Assert.assertNotNull(taskResponseList3);
    }

    @Test
    public void cj_ApproveTest2() throws Exception {

        //部署流程
//        InputStream fileInputStream = new ClassPathResource("processes/cj_technical_scheme_approve.xml").getInputStream();
//        InputStream fileImageInputStream = new ClassPathResource("processes/cj_technical_scheme_approve.png").getInputStream();
//        Deployment uinoApprovedeployment = flowableService.deployProcessDefinition("仓颉技术方案审批流程"
//                , "仓颉技术方案审批流程.bpmn20.xml", fileInputStream
//                , "仓颉技术方案审批流程.png", fileImageInputStream);

        //发起流程，发起人lichong
        String owner = "lichong";
        ProcessRequest processRequest = new ProcessRequest();
        processRequest.setProcessDefinitionKey("cj_technical_scheme_approve");
        String s = LocalDateTime.now().toString();
        processRequest.setBusinessKey(s);
        processRequest.setUserId(owner);
        processRequest.setOwner(owner);
        Task task = flowableService.startProcessBindAssignee(processRequest);
        String processInstanceId = task.getProcessInstanceId();
        log.info("启动流程成功,任务id是：{},流程实例id：{}", task.getId(), task.getProcessInstanceId());
        log.info("发起人{}，第一个任务被{}处理", owner, processRequest.getUserId());

        while (true) {
            List<TaskResponse> taskResponseListByUser = flowableService.getTaskListByProcessInstanceId(processInstanceId);
            if (CollectionUtils.isEmpty(taskResponseListByUser)) {
                break;
            }
            for (TaskResponse taskResponse : taskResponseListByUser) {
                log.info("{}处理人正在处理", taskResponse.getUserId());
                TaskRequest tmpTaskRequest = new TaskRequest();
                tmpTaskRequest.setTaskId(taskResponse.getTaskId());
                tmpTaskRequest.setAction(FLOWACTION.ACCETP);
                tmpTaskRequest.setRouterVariables(new HashMap<>());
                flowableService.completeTask(tmpTaskRequest);
            }
        }
        Assert.assertNotNull(task);
    }

    /**
     * 测试技术方案不通过代码
     *
     * @throws Exception
     */
    @Test
    public void cj_ApproveTest3() throws Exception {

        //部署流程
//        InputStream fileInputStream = new ClassPathResource("processes/cj_technical_scheme_approve.bpmn20.xml").getInputStream();
//        InputStream fileImageInputStream = new ClassPathResource("processes/cj_technical_scheme_approve.png").getInputStream();
//        Deployment uinoApprovedeployment = flowableService.deployProcessDefinition("仓颉技术方案审批流程"
//                , "仓颉技术方案审批流程.bpmn20.xml", fileInputStream
//                , "仓颉技术方案审批流程.png", fileImageInputStream);

        //发起流程，发起人lichong
        String owner = "lichong";
        ProcessRequest processRequest = new ProcessRequest();
        processRequest.setProcessDefinitionKey("cj_technical_scheme_approve");
        String s = LocalDateTime.now().toString();
        processRequest.setBusinessKey(s);
        processRequest.setUserId(owner);
        processRequest.setOwner(owner);
        processRequest.setProcessInstanceName("单元测试" + s + "审批流程");
        Task task = flowableService.startProcessBindAssignee(processRequest);
        String processInstanceId = task.getProcessInstanceId();
        log.info("启动流程成功,任务id是：{},流程实例id：{}", task.getId(), task.getProcessInstanceId());
        log.info("发起人{}，第一个任务被{}处理", owner, processRequest.getUserId());
        List<TaskResponse> taskResponseListByUser = flowableService.getTaskListByProcessInstanceId(processInstanceId);
        if (CollectionUtils.isEmpty(taskResponseListByUser)) {
            return;
        }
        for (TaskResponse taskResponse : taskResponseListByUser) {
            log.info("{}处理人正在处理", taskResponse.getUserId());
            TaskRequest tmpTaskRequest = new TaskRequest();
            tmpTaskRequest.setTaskId(taskResponse.getTaskId());
            tmpTaskRequest.setAction(FLOWACTION.ACCETP);
            tmpTaskRequest.setRouterVariables(new HashMap<>());
            flowableService.completeTask(tmpTaskRequest);
        }
        for (int i = 0; i < 100; i++) {
            Thread.sleep(2*1000);
            List<TaskResponse> taskResponseListByUser2 = flowableService.getTaskListByProcessInstanceId(processInstanceId);
            if (CollectionUtils.isEmpty(taskResponseListByUser2)) {
                return;
            }
            taReject(taskResponseListByUser2);
        }
        Assert.assertNotNull(task);
    }

    private void taReject(List<TaskResponse> taskResponseListByUser2) {
        for (int i = 0; i < taskResponseListByUser2.size(); i++) {
            TaskResponse taskResponse = taskResponseListByUser2.get(i);
            log.info("{}处理人正在处理", taskResponse.getUserId());
            TaskRequest tmpTaskRequest = new TaskRequest();
            tmpTaskRequest.setTaskId(taskResponse.getTaskId());
            if (i == taskResponseListByUser2.size() - 1) {
                tmpTaskRequest.setAction(FLOWACTION.ACCETP);
                tmpTaskRequest.setFilterUser(Boolean.TRUE);
            } else {
                tmpTaskRequest.setAction(FLOWACTION.REJECT);
            }
            tmpTaskRequest.setRouterVariables(new HashMap<>());
            tmpTaskRequest.setRemarks("我是备注");
            flowableService.completeTask(tmpTaskRequest);
        }
    }

    public void tst001(String processInstanceId) {

        //一级审批 拒绝
        List<TaskResponse> taskResponseListByUser2 = flowableService.getTaskListByProcessInstanceId(processInstanceId);
        TaskResponse taskResponse1 = taskResponseListByUser2.get(0);
        TaskRequest tmpTaskRequest1 = new TaskRequest();
        tmpTaskRequest1.setTaskId(taskResponse1.getTaskId());
        tmpTaskRequest1.setAction(FLOWACTION.REJECT);
        tmpTaskRequest1.setRouterVariables(new HashMap<>());
        flowableService.completeTask(tmpTaskRequest1);
        List<ActivityInstance> historyFlowSequence = flowableService.getHistoryFlowSequence(processInstanceId);
        ActivityInstance activityInstance = historyFlowSequence.get(0);
        log.info("来源线的id：{}", activityInstance.getActivityId());
        //剩下的通过
        for (int i = 1; i < taskResponseListByUser2.size(); i++) {
            TaskResponse taskResponse = taskResponseListByUser2.get(i);
            log.info("{}处理人正在处理", taskResponse.getUserId());
            TaskRequest tmpTaskRequest = new TaskRequest();
            tmpTaskRequest.setTaskId(taskResponse.getTaskId());
            tmpTaskRequest.setAction(FLOWACTION.ACCETP);
            tmpTaskRequest.setRouterVariables(new HashMap<>());
            flowableService.completeTask(tmpTaskRequest);
        }

        List<TaskResponse> taskResponseListByUser3 = flowableService.getTaskListByProcessInstanceId(processInstanceId);
        List<TaskResponse> lichong = flowableService.getTaskResponseListByUser("hewenchao", 1, 10).getList();
        for (TaskResponse taskResponse : lichong) {
            log.info(taskResponse.getUserId());
        }

        for (TaskResponse taskResponse : taskResponseListByUser3) {
            log.info("{}处理人正在处理", taskResponse.getUserId());
            TaskRequest tmpTaskRequest = new TaskRequest();
            tmpTaskRequest.setTaskId(taskResponse.getTaskId());
            tmpTaskRequest.setAction(FLOWACTION.ACCETP);
            tmpTaskRequest.setRouterVariables(new HashMap<>());
            flowableService.completeTask(tmpTaskRequest);
        }

        List<TaskResponse> taskResponseListByUser4 = flowableService.getTaskListByProcessInstanceId(processInstanceId);
        if (CollectionUtils.isEmpty(taskResponseListByUser4)) {
            return;
        }
        List<ActivityInstance> historyFlowSequence2 = flowableService.getHistoryFlowSequence(processInstanceId);
        ActivityInstance activityInstance2 = historyFlowSequence2.get(0);
        log.info("来源线的id：{}", activityInstance2.getId());
        for (TaskResponse taskResponse : taskResponseListByUser4) {
            log.info("{}处理人正在处理", taskResponse.getUserId());
            TaskRequest tmpTaskRequest = new TaskRequest();
            tmpTaskRequest.setTaskId(taskResponse.getTaskId());
            tmpTaskRequest.setAction(FLOWACTION.ACCETP);
            tmpTaskRequest.setRouterVariables(new HashMap<>());
            flowableService.completeTask(tmpTaskRequest);
        }
    }


    @Test
    public void testFlowSquence() throws Exception{
        InputStream fileInputStream = new ClassPathResource("processes/cj_technical_scheme_approve.bpmn20.xml").getInputStream();
        InputStream fileImageInputStream = new ClassPathResource("processes/cj_technical_scheme_approve.png").getInputStream();
        Deployment uinoApprovedeployment = flowableService.deployProcessDefinition("仓颉技术方案审批流程"
                , "仓颉技术方案审批流程.bpmn20.xml", fileInputStream
                , "仓颉技术方案审批流程.png", fileImageInputStream);
        Assert.assertNotNull(uinoApprovedeployment);
    }

    @Test
    public void getDoneTaskList() {
        PageEntityInfo<TaskResponse> lichong = flowableService.getDoneTaskListByUserId("lucong", 10, 1);
        Assert.assertNotNull(lichong);
        log.info(lichong.toString());

    }

    @Test
    public void deleteFilterUserTest(){
        filterUserService.deleteFilterUserByProcessInstanceId("lichong");
        Assert.assertTrue(true);
    }

}
