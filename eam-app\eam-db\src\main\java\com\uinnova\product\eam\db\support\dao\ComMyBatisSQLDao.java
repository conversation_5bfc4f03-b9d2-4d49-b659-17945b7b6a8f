package com.uinnova.product.eam.db.support.dao;

import com.binary.jdbc.Page;

import java.util.List;
import java.util.Map;

public interface ComMyBatisSQLDao {
	
	public <T> Page<T> queryByPage(String statementid, String sql, Map<String, Object> mapParams, long pageNum, long pageSize, Class<T> beanClass);

	public <T> Page<T> selectByPageOrderby(String statementid, String sql, Map<String, Object> mapParameter, String orderby, long pageNum, long pageSize, Class<T> beanClass);

	public List<Map<String,Object>> executeQueryParam(String statementid, String sql, Map<String, Object> mapParams);

	public List<Map<String,Object>> executeQuery(String statementid, String sql);

	public Page<Map<String, Object>> selectMapByPageOrderby(String statementid, String sql, Map<String, Object> mapParameter, String orderby, long pageNum, long pageSize);

	public <T> List<T> executeQueryBeanByParam(String statementid, String sql, Map<String, Object> mapParams, Class<T> beanClass);

	public int executeDeleteByParam(String statementid, String sql, Map<String, Object> mapParams);

	public int executeUpdateByParam(String statementid, String sql, Map<String, Object> mapParams);

	public int executeInsertByParam(String statementid, String sql, Map<String, Object> mapParams);
}
