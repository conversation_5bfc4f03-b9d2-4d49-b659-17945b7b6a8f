package com.uinnova.product.eam.service.ai.api;

import com.binary.framework.web.RemoteResult;
import com.uinnova.product.eam.model.vo.ESCISearchBeanVO;
import com.uinnova.product.vmdb.provider.ci.bean.CiGroupPage;
import com.uino.bean.cmdb.base.LibType;

import java.util.List;
import java.util.Map;

/**
 *  AI EA资产相关api接口
 */
public interface IDifyAssetApiService {


    /**
     *  分页查询对象数据-支持属性排序
     * @param bean 搜索条件bean
     * @param libType 库类型
     * @return
     */
    CiGroupPage queryPageBySearchBean(ESCISearchBeanVO bean, LibType libType);

    /**
     *  查询系统相关架构视图
     * @param ciCode ciCode
     * @return
     */
    List<Map<String, String>> getSystemArchitectureDiagram(String ciCode);

    /**
     * ai绘图-视图数据解析
     * @param param
     * @return
     */
    RemoteResult autoDataAnalysis(String param);

}
