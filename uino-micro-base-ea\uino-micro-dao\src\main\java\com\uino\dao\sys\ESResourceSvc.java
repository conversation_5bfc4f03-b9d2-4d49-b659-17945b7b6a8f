package com.uino.dao.sys;

import jakarta.annotation.PostConstruct;

import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.uino.dao.AbstractESBaseDao;
import com.uino.dao.ESConst;
import com.uino.bean.cmdb.base.ESResource;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
public class ESResourceSvc extends AbstractESBaseDao<ESResource, JSONObject> {

	@Override
	public String getIndex() {
		// TODO Auto-generated method stub
		return ESConst.INDEX_RESOURCE;
	}

	@Override
	public String getType() {
		// TODO Auto-generated method stub
		return ESConst.INDEX_RESOURCE;
	}

	@Override
	protected boolean showQueryLog() {
		// TODO Auto-generated method stub
		return false;
	}

	@PostConstruct
	public void init() {
		super.initIndex();
	}

}
