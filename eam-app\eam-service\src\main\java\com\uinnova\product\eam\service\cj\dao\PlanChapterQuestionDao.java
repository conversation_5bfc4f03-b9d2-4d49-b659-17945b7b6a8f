package com.uinnova.product.eam.service.cj.dao;

import com.uinnova.product.eam.model.cj.domain.PlanChapterQuestion;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;

/**
 * @description: 问题
 * @author: Lc
 * @create: 2022-03-01 18:45
 */
@Component
public class PlanChapterQuestionDao extends AbstractESBaseDao<PlanChapterQuestion, PlanChapterQuestion> {
    @Override
    public String getIndex() {
        return "uino_cj_plan_chapter_question";
    }

    @Override
    public String getType() {
        return getIndex();
    }

    @PostConstruct
    public void init() {
        initIndex();
    }
}
