package com.uinnova.product.vmdb.comm.dao.impl;

import com.binary.core.bean.BMProxy;
import com.binary.core.lang.Conver;
import com.binary.core.util.BinaryUtils;
import com.binary.framework.bean.Condition;
import com.binary.framework.bean.EntityBean;
import com.binary.framework.dao.Dao;
import com.binary.framework.exception.DaoException;
import com.binary.framework.exception.ServiceException;
import com.uinnova.product.vmdb.comm.dao.CommDao;
import com.uinnova.product.vmdb.comm.i18n.MessageUtil;
import com.uino.tarsier.tarsiercom.dao.mybatis.ComMyBatisSQLDaoImpl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 *
 */
public class CommDaoImpl extends ComMyBatisSQLDaoImpl implements CommDao {

    private Map<String, Object> selectFrist(String tableName, String parentFieldName, String sql) {
        try {
            String selectSql = "${selectSql}";
            Map<String, Object> mapParams = new HashMap<String, Object>();
            mapParams.put("selectSql", sql);
            List<Map<String, Object>> executeQuery = executeQueryParam(tableName + parentFieldName + "CommDao.selectCount", selectSql, mapParams);
//            List<Map<String, Object>> executeQuery = executeQuery(tableName + parentFieldName + "CommDao.selectCount", sql);
            if (!BinaryUtils.isEmpty(executeQuery)) {
                return executeQuery.get(0);
            }
            return null;
        } catch (Exception e) {
            throw new DaoException(e.getMessage(), e.getCause());
        }
    }

    private Integer selectCount(String tableName, String parentFieldName, String sql) {
        try {
            String selectSql = "${selectSql}";
            Map<String, Object> mapParams = new HashMap<String, Object>();
            mapParams.put("selectSql", sql);
            List<Map<String, Object>> executeQuery = executeQueryParam(tableName + parentFieldName + "CommDao.selectCount", selectSql, mapParams);

//            List<Map<String, Object>> executeQuery = executeQuery(tableName + parentFieldName + "CommDao.selectCount", sql);
            if (!BinaryUtils.isEmpty(executeQuery)) {
                return executeQuery.size();
            }
            return null;
        } catch (Exception e) {
            throw new DaoException(e.getMessage(), e.getCause());
        }
    }

    private List<Map<String, Object>> queryAll(String tableName, String parentFieldName, String sql, Map<String, Object> mapParams) {
        try {
            return executeQueryParam(tableName + parentFieldName + "CommDao.queryAll", sql, mapParams);
        } catch (Exception e) {
            throw new DaoException(e.getMessage(), e.getCause());
        }
    }

    private void update(String tableName, String parentFieldName, String sql ,Map<String, Object> mapParams) {
        try {
            executeUpdateByParam(tableName + parentFieldName + "CommDao.update", sql, mapParams);
        } catch (Exception e) {
            throw new DaoException(e.getMessage(), e.getCause());
        }
    }

    @Override
    public void updateTreeLevel(Long nodeId, Long oldParentId, Long newParentId, String tableName, String parentFieldName, String levelFieldName, String pathFieldName, String leafFieldName, boolean hasDataStatus) {
        MessageUtil.checkEmpty(nodeId, "nodeId");
        MessageUtil.checkEmpty(newParentId, "newParentId");
        MessageUtil.checkEmpty(tableName, "tableName");
        MessageUtil.checkEmpty(parentFieldName, "parentFieldName");
        MessageUtil.checkEmpty(levelFieldName, "levelFieldName");
        MessageUtil.checkEmpty(pathFieldName, "pathFieldName");
        MessageUtil.checkEmpty(leafFieldName, "leafFieldName");

        parentFieldName = parentFieldName.trim().toUpperCase();
        levelFieldName = levelFieldName.trim().toUpperCase();
        pathFieldName = pathFieldName.trim().toUpperCase();
        leafFieldName = leafFieldName.trim().toUpperCase();

        // 上级ID如果没变则无需更新
        if (oldParentId != null && oldParentId.equals(newParentId)) {
            return;
        }

        Long time = BinaryUtils.getNumberDateTime();

        StringBuilder sb = new StringBuilder();

        // 判断以前父节点是否有子节点, 如果没有则更新以前父节点为末级节点
        if (oldParentId != null && oldParentId.longValue() != 0) {
            sb.append(" select * from ").append(tableName).append(" where ").append(parentFieldName).append("=").append(oldParentId);
            if (hasDataStatus) {
                sb.append(" and DATA_STATUS=1 ");
            }
            Integer oldChildCount = selectCount(tableName, parentFieldName, sb.toString());
            if (oldChildCount != null && oldChildCount == 0) {
                sb.delete(0, sb.length());
                sb.append(" update ").append(tableName).append(" set ").append(leafFieldName).append("=1, MODIFY_TIME=#{modify_time:BIGINT} where id=#{id} ");

                Map<String, Object> mapParams = new HashMap<String, Object>();
                mapParams.put("modify_time", time);
                mapParams.put("id", oldParentId);

                update(tableName, parentFieldName, sb.toString(), mapParams);
            }
        }

        boolean isfirst = newParentId.longValue() == 0;

        String parentPath = "#";
        int parentLevel = 0;

        // 查询上级节点对象
        if (!isfirst) {
            sb.delete(0, sb.length());
            sb.append(" select ID, ").append(levelFieldName).append(", ").append(pathFieldName).append(", ").append(leafFieldName).append(" from ").append(tableName).append(" where ID=").append(newParentId);
            Map<String, Object> parent = selectFrist(tableName, parentFieldName, sb.toString());
            if (parent == null) {
                throw new ServiceException(" not found parentNode by id '" + newParentId + "'! ");
            }

            parentPath = Conver.to(parent.get(pathFieldName), String.class);
            parentLevel = Conver.to(parent.get(levelFieldName), Integer.class);

            // 如果新上级节点之前为末级, 则改为非末级
            Integer leaf = Conver.to(parent.get(leafFieldName), Integer.class);
            if (leaf == null || leaf.intValue() == 1) {
                sb.delete(0, sb.length());
                sb.append(" update ").append(tableName).append(" set ").append(leafFieldName).append("=0, MODIFY_TIME=#{modify_time:BIGINT} where id=#{id} ");

                Map<String, Object> mapParams = new HashMap<String, Object>();
                mapParams.put("modify_time", time);
                mapParams.put("id", oldParentId);

                update(tableName, parentFieldName, sb.toString(), mapParams);
            }
        }

        // 级联更新子结点级别
        sb.delete(0, sb.length());
        sb.append(" update ").append(tableName).append(" set ").append(levelFieldName).append("=#{level:INTEGER}, ").append(pathFieldName).append("=#{path}, ")
                .append(leafFieldName).append("=#{leaf:INTEGER}, ").append(" MODIFY_TIME=").append("#{modify_time:BIGINT}").append(" where id=#{id} ");

        String updateSql = sb.toString();
        List<Map<String, Object>> paramsList = new ArrayList<Map<String, Object>>();

        int level = parentLevel + 1;
        String path = parentPath + nodeId + "#";
        // 占位
        int leaf = 1;

        Map<String, Object> parentParams = new HashMap<String, Object>();
        parentParams.put("level", level);
        parentParams.put("path", path);
        parentParams.put("leaf", leaf);
        parentParams.put("id", nodeId);
        parentParams.put("modify_time",time);
        paramsList.add(parentParams);

        sb.delete(0, sb.length());
        sb.append(" select ID from ").append(tableName).append(" where ").append(parentFieldName).append("=#{parentId} ");
        if (hasDataStatus) {
            sb.append(" and DATA_STATUS=1 ");
        }
        String selectChildsSql = sb.toString();
        Map<String, Object> pp = new HashMap<String, Object>();
        pp.put("parentId", nodeId);

        // 补充更新所有子节点级别信息
        fillUpdateChilds(tableName, parentFieldName, selectChildsSql, pp, level, path, parentParams, paramsList);

        for (Map<String, Object> mapParams : paramsList) {
            update(tableName, parentFieldName, updateSql, mapParams);
        }
    }


    private void fillUpdateChilds(String tableName, String parentFieldName, String sql, Map<String, Object> pp, int parentLevel, String parentPath, Map<String, Object> parentParams, List<Map<String, Object>> paramsList) {
        Long time = BinaryUtils.getNumberDateTime();
        // 查询子节点
        List<Map<String, Object>> childs = queryAll(tableName, parentFieldName, sql, pp);
        if (childs.size() > 0) {
            // 如果含有子节点, 则将上级节点是否末级改为非末级
            parentParams.put("leaf", 0);
            for (int i = 0; i < childs.size(); i++) {
                Map<String, Object> m = childs.get(i);
                Long id = Conver.to(m.get("ID"), Long.class);

                int level = parentLevel + 1;
                String path = parentPath + id + "#";
                // 占位
                int leaf = 1;
                Map<String, Object> params = new HashMap<String, Object>();
                params.put("level", level);
                params.put("path", path);
                params.put("leaf", leaf);
                params.put("id", id);
                params.put("modify_time",time);
                paramsList.add(params);


                Map<String, Object> pp1 = new HashMap<String, Object>();
                pp1.put("parentId", id);
                fillUpdateChilds(tableName, parentFieldName, sql, pp1, level, path, params, paramsList);
            }
        }
    }


    @Override
    public <T extends EntityBean, F extends Condition> List<T> getTreeAllParents(Dao<T, F> dao, Long nodeId, String parentName) {
        MessageUtil.checkEmpty(dao, "dao");
        MessageUtil.checkEmpty(nodeId, "nodeId");
        MessageUtil.checkEmpty(parentName, "parentName");
        char c = '_';
        if (parentName.indexOf(c) > -1) {
            parentName = parentName.replaceAll("_", "");
        }

        List<T> ls = new ArrayList<T>();
        BMProxy<T> proxy = BMProxy.getInstance(dao.getDaoDefinition().getEntityClass());
        fillTreeAllParents(dao, nodeId, parentName, ls, proxy);
        return ls;
    }

    private <T extends EntityBean, F extends Condition> void fillTreeAllParents(Dao<T, F> dao, Long nodeId, String parentName, List<T> parents, BMProxy<T> proxy) {
        if (nodeId == null || nodeId.longValue() == 0) {
            return;
        }

        T t = dao.selectById(nodeId);

        if (t != null) {
            proxy.replaceInnerObject(t);
            Long parentId = proxy.get(parentName, Long.class);
            if (nodeId != null && nodeId.longValue() != 0) {
                fillTreeAllParents(dao, parentId, parentName, parents, proxy);
            }
            parents.add(t);
        }
    }

}
