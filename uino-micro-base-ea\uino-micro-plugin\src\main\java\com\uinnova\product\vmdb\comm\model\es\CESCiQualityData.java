package com.uinnova.product.vmdb.comm.model.es;


public class CESCiQualityData {

    private static final long serialVersionUID = 1L;

    private Long domainId;

    private String problemType;

    private String[] problemTypes;

    private String className;

    private String[] classNames;

    private String rltClassName;

    private String[] rltClassNames;

    private String oppositeClassName;

    private String[] oppositeClassNames;

    private String keyword;

    private String source;

    private Integer sourceType;

    private String sortField;

    private String sortOrder;

    private Long time;

    public Long getDomainId() {
        return domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }

    public String getProblemType() {
        return problemType;
    }

    public void setProblemType(String problemType) {
        this.problemType = problemType;
    }

    public String getClassName() {
        return className;
    }

    public void setClassName(String className) {
        this.className = className;
    }

    public String getRltClassName() {
        return rltClassName;
    }

    public void setRltClassName(String rltClassName) {
        this.rltClassName = rltClassName;
    }

    public String getOppositeClassName() {
        return oppositeClassName;
    }

    public void setOppositeClassName(String oppositeClassName) {
        this.oppositeClassName = oppositeClassName;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public String getSortField() {
        return sortField;
    }

    public void setSortField(String sortField) {
        this.sortField = sortField;
    }

    public String getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(String sortOrder) {
        this.sortOrder = sortOrder;
    }

    public Long getTime() {
        return time;
    }

    public void setTime(Long time) {
        this.time = time;
    }

    public String[] getProblemTypes() {
        return problemTypes;
    }

    public void setProblemTypes(String[] problemTypes) {
        this.problemTypes = problemTypes;
    }

    public String[] getClassNames() {
        return classNames;
    }

    public void setClassNames(String[] classNames) {
        this.classNames = classNames;
    }

    public String[] getRltClassNames() {
        return rltClassNames;
    }

    public void setRltClassNames(String[] rltClassNames) {
        this.rltClassNames = rltClassNames;
    }

    public String[] getOppositeClassNames() {
        return oppositeClassNames;
    }

    public void setOppositeClassNames(String[] oppositeClassNames) {
        this.oppositeClassNames = oppositeClassNames;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public Integer getSourceType() {
        return sourceType;
    }

    public void setSourceType(Integer sourceType) {
        this.sourceType = sourceType;
    }
}
