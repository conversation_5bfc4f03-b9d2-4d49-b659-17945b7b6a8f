package com.uinnova.product.vmdb.comm.expression;

import com.uinnova.product.vmdb.comm.exception.ExpressionException;

/**
 * 
 * <AUTHOR>
 *
 */
public enum OP {

    /**
     * 等于
     */
    Equal(1),

    /**
     * 不等于
     */
    NotEqual(2),

    /**
     * 小于
     */
    LT(3),

    /**
     * 小于或等于
     */
    LTEqual(4),

    /**
     * 大于
     */
    GT(5),

    /**
     * 大于或等于
     */
    GTEqual(6),

    /**
     * 模糊匹配...
     */
    Like(7),

    /**
     * 包含...
     */
    In(8),

    /**
     * 不包含...
     */
    NotIn(9),

    /**
     * 字符包含
     */
    INSTR(10);

    private final int value;

    OP(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public String getOperator() {
        switch (this) {
        case Equal:
            return "=";
        case NotEqual:
            return "<>";
        case LT:
            return "<";
        case LTEqual:
            return "<=";
        case GT:
            return ">";
        case GTEqual:
            return ">=";
        case Like:
            return " like ";
        case In:
            return " in ";
        case NotIn:
            return " not in ";
        case INSTR:
            return " instr ";
        default:
            return "";
        }
    }

    public static OP valueOf(int v) {
        switch (v) {
        case 1:
            return Equal;
        case 2:
            return NotEqual;
        case 3:
            return LT;
        case 4:
            return LTEqual;
        case 5:
            return GT;
        case 6:
            return GTEqual;
        case 7:
            return Like;
        case 8:
            return In;
        case 9:
            return NotIn;
        case 10:
            return INSTR;
        default:
            throw new ExpressionException(" is wrong OP:'" + v + "'! ");
        }
    }

}
