package com.uinnova.product.eam.model.vo;

import com.binary.framework.bean.annotation.Comment;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class WorkFlowMsgSaveParam {

    @Comment("是否审批通过")
    private Boolean pass;

    @Comment("审批消息结果")
    private String approveMsg;

    @Comment("是否资产仓库资产:0否1是")
    private Integer isPublic;

    @Comment("资产id")
    private String sourceId;
}
