#!/usr/bin/env bash

#该脚本可以备份es mysql 和rsm文件
#CURRENT_VMDB_SQL_FILE为vmdb的sql文件CURRENT_FLOWABLE_SQL_FILE为工作流库的sql
#ES_DATA_PATH为es的安装目录RSM_DATA_PATH为rsm的所在上级目录
# 使用示例：eam_data_manager.sh restore 重置数据
# 使用示例：eam_data_manager.sh backup 备份数据（建议备份数据时关闭es，防止备份过程中出现的增量数据丢失）

#添加到linux定时任务
#crontab -e 打开定时任务配置
#添加一行（示例中脚本放置在/uinnova/eam_data_manager目录下）
#   0  2  *  *  * sh /uinnova/eam_data_manager/eam_data_manager.sh restore >> /uinnova/eam_data_manager/cron.log

#若提示JAVA_HOME不存在请检查esuser中是否安装有JAVA_HOME ,并检查/etc/profile文件是否有其他用户的读取权限

CURRENT_VMDB_SQL_FILE=db_vmdb_current.sql
CURRENT_FLOWABLE_SQL_FILE=eam_flowable_current.sql
ES_DATA_PATH=/uinnova/elasticsearch
RSM_DATA_PATH=/uinnova/uino

CURRENT_DIR=$(
  cd $(dirname $0)
  pwd
)

function restore_data() {
  echo "-----------------还原数据开始-----------------"
  echo "restore_mysql"
  cd $CURRENT_DIR
  if [ -f db_vmdb_current.sql ]; then
    echo "还原db_vmdb数据库"
    mysql -uuinnova -pUinnova@123 db_vmdb <db_vmdb_current.sql
  fi
  if [ -f eam_flowable_current.sql ]; then
    echo "还原eam_flowable数据库"
    mysql -uuinnova -pUinnova@123 eam_flowable <eam_flowable_current.sql
  fi

  echo "restore_es"
  ES_PID=$(ps -ef | grep java | grep lastic |awk '{print $2}')
  if [ ! -n "$ES_PID" ]; then
    echo "es未运行"
  else
    echo "关闭es"
    kill -9 $ES_PID
  fi

  rm -rf $ES_DATA_PATH/data
  tar -zxf $CURRENT_DIR/eam_es_current.tar.gz -C $ES_DATA_PATH
  chown -R esuser:esuser $ES_DATA_PATH/data
  su - esuser -c "$ES_DATA_PATH/bin/elasticsearch -d"

  echo "restore_rsm"
  rm -rf $RSM_DATA_PATH/rsm
  tar -zxf $CURRENT_DIR/eam_rsm_current.tar.gz -C $RSM_DATA_PATH
  echo "-----------------还原数据结束-----------------"
}

function backup_data() {
  echo "-----------------备份数据开始-----------------"
  echo "backup_mysql"
  if [ -f db_vmdb_old.sql ]; then
    echo "db_vmdb_old文件存在，删除"
    rm -rf db_vmdb_old.sql
  fi
  if [ -f db_vmdb_current.sql ]; then
    echo "db_vmdb_current文件存在，备份成历史"
    mv db_vmdb_current.sql db_vmdb_old.sql
  fi

  if [ -f eam_flowable_old.sql ]; then
    echo "eam_flowable_old文件存在，删除"
    rm -rf eam_flowable_old.sql
  fi
  if [ -f eam_flowable_current.sql ]; then
    echo "eam_flowable_current文件存在，备份成历史"
    mv eam_flowable_current.sql eam_flowable_old.sql
  fi
  mysqldump -h localhost -uuinnova -pUinnova@123 -d db_vmdb >$CURRENT_VMDB_SQL_FILE
  mysqldump -h localhost -uuinnova -pUinnova@123 -d eam_flowable >$CURRENT_FLOWABLE_SQL_FILE
  if [ -f eam_es_old.tar.gz ]; then
    echo "eam_es_old.tar.gz文件存在,删除"
    rm -rf eam_es_old.tar.gz
  fi
  if [ -f eam_es_current.tar.gz ]; then
    echo "eam_es_current.tar.gz文件存在，备份成历史"
    mv eam_es_current.tar.gz eam_es_old.tar.gz
  fi
  echo "backup_es"
  cd $ES_DATA_PATH
  tar -zcf eam_es_current.tar.gz data
  mv eam_es_current.tar.gz "$CURRENT_DIR"
  cd $CURRENT_DIR
  echo "backup_rsm"
  if [ -f eam_rsm_old.tar.gz ]; then
    echo "eam_rsm_old文件存在，删除"
    rm -rf eam_rsm_old.tar.gz
  fi
  if [ -f eam_rsm_current.tar.gz ]; then
    echo "eam_rsm_current文件存在，备份成历史"
    mv eam_rsm_current.tar.gz eam_rsm_old.tar.gz
  fi
  cd $RSM_DATA_PATH
  tar -zcf eam_rsm_current.tar.gz rsm
  mv eam_rsm_current.tar.gz "$CURRENT_DIR"
  echo "-----------------备份结束-----------------"
}

if [ "$1" = "backup" ]; then
  backup_data
elif [ "$1" = "restore" ]; then
  restore_data
else
  echo "不支持的参数"
  exit 1
fi
