package com.uinnova.product.eam.web.diagram.bean;

import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;

public class ExcelParam  implements Condition {
	
	private static final long serialVersionUID = 1L;
	
	@Comment("表格模糊查询条件，包括内容和名称")
	private String word;
	
	@Comment("目录id")
	private Long dirId;
	
	public Long getDirId() {
		return dirId;
	}

	public void setDirId(Long dirId) {
		this.dirId = dirId;
	}

	public String getWord() {
		return word;
	}

	public void setWord(String word) {
		this.word = word;
	}

}
