package com.uinnova.product.vmdb.provider.rlt.bean;

import com.binary.core.util.BinaryUtils;
import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.vmdb.comm.model.ci.CcCi;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.comm.model.ci.CcCiClass;
import com.uinnova.product.vmdb.comm.model.ci.CcFixAttrMapping;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

public class CiNode implements Serializable {

	private static final long serialVersionUID = 1L;

	@Comment("CI对象")
	private CcCi ci;

	@Comment("CI属性")
	private Map<String, String> attrs;

	@Comment("当前CI所属分类")
	private CcCiClass ciClass;

	@Comment("常柱属性映射对象")
	private CcFixAttrMapping fixMapping;

	@Comment("当前CI属性定义")
	private List<CcCiAttrDef> attrDefs;
	
	@Comment("当前CILable")
	private String ciLable;
	
	public CiNode() {
	}

	public CiNode(CcCiInfo ciInfo) {
		this.ci = ciInfo.getCi();
		this.attrs = ciInfo.getAttrs();
		this.fixMapping = ciInfo.getFixMapping();
		this.attrDefs = ciInfo.getAttrDefs();
		this.ciClass = ciInfo.getCiClass();
		
		//设置lable
		StringBuffer sb = new StringBuffer();
		for (CcCiAttrDef def : attrDefs) {
			if(def.getIsCiDisp().intValue() == 1 && !BinaryUtils.isEmpty(this.attrs.get(def.getProStdName()))){
				sb.append(this.attrs.get(def.getProStdName())).append(" ,");
			}
		}
		if(sb.length()>2){
			sb.delete(sb.length()-2, sb.length());
		}
		ciLable = sb.toString();
	}

	public CiNode(CcCiInfo ciInfo, CcCiClassInfo ciClassInfo) {
		this.ci = ciInfo.getCi();
		this.attrs = ciInfo.getAttrs();
		this.fixMapping = ciClassInfo.getFixMapping();
		this.attrDefs = ciClassInfo.getAttrDefs();
		this.ciClass = ciClassInfo.getCiClass();
		//设置lable
		StringBuffer sb = new StringBuffer();
		for (CcCiAttrDef def : attrDefs) {
			if(def.getIsCiDisp().intValue() == 1 && !BinaryUtils.isEmpty(this.attrs.get(def.getProStdName()))){
				sb.append(this.attrs.get(def.getProStdName())).append(" ,");
			}
		}
		if(sb.length()>2){
			sb.delete(sb.length()-2, sb.length());
		}
		ciLable = sb.toString();
	}
	
	
	public Long getId() {
		return ci.getId();
	}

	public void setId(Long id) {
	}
	
	public String getCiLable(){
		return this.ciLable;
	}
	
	public void setCiLable(String ciLable){
		this.ciLable = ciLable;
	}
	
	public String getCiCode() {
		return ci.getCiCode();
	}

	public void setCiCode(String ciCode) {
	}

	public String getCiDesc() {
		return ci.getCiDesc();
	}

	public void setCiDesc(String ciDesc) {
	}

	public Long getClassId() {
		return ci.getClassId();
	}

	public void setClassId(Long classId) {
	}

	public String getClassName(){
		return ciClass != null ? ciClass.getClassName() : null;
	}
	
	public void setClassName(String ciClass){
		
	}
	
	public String getClassColor(){
		return ciClass != null ? ciClass.getClassColor() : null;
	}
	
	public void setClassColor(String classColor){
		
	}
	
	public Long getSourceId() {
		return ci.getSourceId();
	}

	public void setSourceId(Long sourceId) {
	}

	public Long getOrgId() {
		return ci.getOrgId();
	}

	public void setOrgId(Long orgId) {
	}

	public String getSubClass() {
		return ci.getSubClass();
	}

	public void setSubClass(String subClass) {
	}

	public Map<String, String> getAttrs() {
		return attrs;
	}

	public void setAttrs(Map<String, String> attrs) {
		this.attrs = attrs;
	}

	public CcCi getCi() {
		return ci;
	}

	public void setCi(CcCi ci) {
		this.ci = ci;
	}

	public CcCiClass getCiClass() {
		return ciClass;
	}

	public void setCiClass(CcCiClass ciClass) {
		this.ciClass = ciClass;
	}

	public CcFixAttrMapping getFixMapping() {
		return fixMapping;
	}

	public void setFixMapping(CcFixAttrMapping fixMapping) {
		this.fixMapping = fixMapping;
	}

	public List<CcCiAttrDef> getAttrDefs() {
		return attrDefs;
	}

	public void setAttrDefs(List<CcCiAttrDef> attrDefs) {
		this.attrDefs = attrDefs;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((ci == null) ? 0 : ci.hashCode());
		return result;
	}
	
	
}
