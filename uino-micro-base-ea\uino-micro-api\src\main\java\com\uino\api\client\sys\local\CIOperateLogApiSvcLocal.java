package com.uino.api.client.sys.local;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.binary.jdbc.Page;
import com.uino.service.sys.microservice.ICIOperateLogSvc;
import com.uino.bean.sys.base.ESCIOperateLog;
import com.uino.bean.sys.query.ESCIOperateLogSearchBean;
import com.uino.api.client.sys.ICIOperateLogApiSvc;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
public class CIOperateLogApiSvcLocal implements ICIOperateLogApiSvc {

    @Autowired
    private ICIOperateLogSvc logSvc;

    @Override
    public Page<ESCIOperateLog> getCIOperateLogPageByCdt(ESCIOperateLogSearchBean bean) {
        return logSvc.getCIOperateLogPageByCdt(bean);
    }

    @Override
    public Integer clearCIOperateLogByDuration(Integer durationDay) {
        return logSvc.clearCIOperateLogByDuration(durationDay);
    }

}
