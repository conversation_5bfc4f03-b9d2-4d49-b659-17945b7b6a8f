package com.uinnova.product.eam.api.diagram;

import com.binary.jdbc.Page;
import com.binary.json.JSONArray;
import com.binary.json.JSONObject;
import com.uinnova.product.eam.base.diagram.enums.DiagramCopyEnum;
import com.uinnova.product.eam.base.diagram.model.*;
import com.uinnova.product.eam.model.diagram.*;
import com.uinnova.product.eam.model.diagram.event.RuleParams;
import org.elasticsearch.index.query.QueryBuilder;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface ESDiagramApiClient {

    Map<String, String> saveESDiagram(ESDiagramInfoDTO esDiagramInfo);

    List<ESDiagramDTO> queryDiagramInfoByIds(Long[] diagramIds, String type, Boolean needAuth, Boolean asset);

    String deleteDiagramByIds(JSONObject jsonObject, JSONArray opArr);

    String deleteDiagramById(Long diagramId);

    Integer deleteDiagramByIds(Long[] diagramIds);

    /**
     * 根据视图id获取node节点
     * @param diagramIds
     * @return
     */
    List<ESDiagramNode> selectNodeByDiagramIds(List<Long> diagramIds);


    /**
     * 根据文件夹id查询文件夹下的视图信息
     * @param dirIds 文件夹id
     * @return
     */
    List<ESDiagram> selectDiagramsFromRecycle(List<Long> dirIds, String ownerCode, List<String> diagramIds);

    /**
     * 根据视图id获取link节点
     * @param diagramIds
     * @return
     */
    List<ESDiagramLink> selectLinkByDiagramIds(List<Long> diagramIds);

    /**
     * 根据ciCode获取视图节点
     * @param ciCodes
     * @return
     */
    List<ESDiagramNode> selectNodeByCiCodes(List<String> ciCodes, String ownerCode);

    /**
     * 根据rltCiCode获取视图节点
     * @param uniqueCodes
     * @return
     */
    List<ESDiagramLink> selectLinkByRltCiCodes(List<String> uniqueCodes);

    /**
     * 根据uniqueCode获取视图节点
     * @param uniqueCodes 关系标识
     * @param ownerCode 用户标识
     * @return 关系线
     */
    List<ESDiagramLink> selectLinkByRltCodes(Collection<String> uniqueCodes, String ownerCode);

    /**
     * 根据视图加密id获取视图基本信息
     * @param diagramIds 加密id
     * @param dirTypes 所属文件类型,可为空
     * @return
     */
    List<ESDiagram> selectByIds(Collection<String> diagramIds, List<Integer> dirTypes, List<Integer> isOpens);

    /**
     * 通过制品id查询视图
     * @param artifactIds 制品id
     * @return 视图基本信息
     */
    List<ESDiagram> queryByArtifactIds(List<Long> artifactIds);

    /**
     * 根据视图加密id获取视图基本信息
     * @param diagramIds 加密id
     * @param dirTypes 所属文件类型,可为空
     * @return
     */
    List<ESDiagram> getByIds(Collection<Long> diagramIds, List<Integer> dirTypes, List<Integer> isOpens);

    List<ESDiagram> selectMyOwnDiagramList(String ownerCode, List<Integer> dirTypes);

    /**
     * 更新link中ciCode节点
     * @param linkList
     */
    void replaceLinkList(List<ESDiagramLink> linkList);

    /**
     * 更新Node中ciCode节点
     * @param nodeList
     */
    void replaceNodeList(List<ESDiagramNode> nodeList);

    /**
     * 保存Link节点
     * @param linkList
     * @return
     */
    int saveLinkList(List<ESDiagramLink> linkList);

    /**
     * 保存Node节点
     * @param nodeList
     * @return
     */
    int saveNodeList(List<ESDiagramNode> nodeList);

    /**
     * 删除Link节点
     * @param ids
     * @param keys
     * @return
     */
    int delLinkList(List<Long> ids, List<String> keys);

    /**
     * 删除node节点
     * @param ids
     * @param keys
     * @return
     */
    int delNodeList(List<Long> ids, List<String> keys);

    /**
     * 批量发布视图
     * @param request 请求参数
     * @return 响应参数
     */
    DiagramPushResponse diagramPush(DiagramPushRequest request);

    /**
     * 批量检出视图
     * @param request 请求参数
     * @return 响应参数
     */
    DiagramPullResponse diagramPull(DiagramPullRequest request);

    Page<VcDiagramInfo> queryDiagramInfoPage(Long domainId, Integer pageNum, Integer pageSize, CVcDiagram dCdt, String order);

    List<Long> saveESDiagramBatch(List<ESDiagramInfoDTO> esDiagramInfoList, String newName, Long newDirId, String type);

    Map<String, List<ESResponseStruct>> saveOrUpdateDiagramComponent(String jsonStr);

    /**
     * <AUTHOR>
     * @Description 根据视图id查询视图详情
     * @Date 10:14 2021/7/6
     * @Param [diagramId, type--标识是否需查询视图附加信息，创造者、分享记录等(eg:cooy时不需要附加信息), versionFlag--标识查询历史视图还是普通视图]
     * @return com.uinnova.project.base.diagram.comm.model.ESDiagramDTO
     **/
    ESDiagramDTO queryESDiagramInfoById(Long diagramId, String type, Boolean versionFlag);


    /**
     * 全量更新视图
     * @param diagramId
     * @param esDiagramInfo
     * @return
     */
    ESDiagramDTO updateFullDiagramNew(Long diagramId, ESDiagramInfoDTO esDiagramInfo);

    /**
     * <AUTHOR>
     * @Description 复制视图
     * @Date 10:38 2021/7/2
     * @Param [diagramMoveCdt]
     * @return java.lang.Long
     **/
    String copyDiagramById(ESDiagramMoveCdt diagramMoveCdt);

    /**
     * <AUTHOR>
     * @Description 移动文件夹和视图
     * @Date 18:35 2021/7/8
     * @Param [domainId, targetDirId, dirIds, diagramIds]
     * @return java.lang.Integer
     **/
    Integer moveDirAndDiagram(MoveDirAndDiagramCdt move);

    /**
     * <AUTHOR>
     * @Description 批量复制视图
     * @Date 19:19 2021/7/8
     * @Param [diagramMoveCdt]
     * @return java.util.List<java.lang.Long>
     **/
    List<Long> copyDiagramByIds(ESDiagramMoveCdt diagramMoveCdt);

    /**
     * <AUTHOR>
     * @Description 复制视图和文件夹到指定文件夹
     * @Date 14:15 2021/7/9
     * @Param [targetDirId, dirIds, diagramIds]
     * @return java.util.List<java.lang.Long>
     **/
    List<Long> copyDirById(Long targetDirId, Long[] dirIds, String[] diagramIds);

    /**
     * <AUTHOR>
     * @Description 更新缩略图地址
     * @Date 15:26 2021/7/13
     * @Param [thumbnailBatch]
     * @return void
     **/
    void processBatchThumbnail(ThumbnailBatch thumbnailBatch);

    Page<ESSimpleDiagramDTO> queryESDiagramInfoPage(Long domainId, Integer pageNum, Integer pageSize, CVcDiagram dCdt, String modifyTime);

    Long[] queryDiagramInfoBydEnergy(String[] diagramIds);

    Long queryDiagramInfoByEnergy(String diagramId);

    Long getCountByDirId(Long dirId, Integer type);

    void removeDiagrams(List<Long> dirIds, String ownerCode);

    List<ESDiagram> findEsDiagramList(Long dirId, String ownerCode);

    ESDiagram getEsDiagram(String dEnergy, Integer isOpen);

    /**
     * 更新视图名称
     * @param newName
     * @param dEnergyId
     * @return
     */
    int updateNameByDiagramId(String newName, String dEnergyId);

    /**
     * 根据根据目录id获取目录下的视图信息
     * @param dirIds
     * @param dirTypes
     * @param ownerCode
     * @param isOpens
     * @return
     */
    List<ESDiagram> selectByDirIds(Collection<Long> dirIds, List<Integer> dirTypes, String ownerCode, List<Integer> isOpens);

    /**
     * 根据视图加密id清除发布视图id
     * @param dEnergyId
     * @return
     */
    Boolean clearReleaseDiagramIdBydEnergyId(Collection<String> dEnergyId,String ownerCode);

    /**
     * <AUTHOR>
     * @Description 批量复制视图 （伏羲项目）
     * @Date 19:19 2021/7/8
     * @Param [diagramMoveCdt]
     * @return java.util.List<java.lang.Long>
     **/
    List<Long> fxCopyDiagramByIds(ESDiagramMoveCdt diagramMoveCdt);

    /**
     * 根据视图了类型查询视图信息
     * @param dirTypes
     * @param ownerCode
     * @param isOpens
     * @return
     */
    List<ESDiagram> selectByDirType(Integer dirTypes, String ownerCode, List<Integer> isOpens);

    /**
     * 创建隐藏关系
     * @param params
     * @return
     */
    int createHiddenLink(RuleParams params);

    ESDiagram querySimpleDiagramInfoById(Long diagramId);

    /**
     * 根据ID从数据库获取信息
     * @param toArray
     */
    List<ESDiagram> queryDBDiagramInfoByIds(String[] toArray);

    /**
     *  库查
     * @param diagramIds
     * @return
     */
    Long[] queryDBDiagramInfoBydEnergy(String[] diagramIds);

    /**
     *  批量查询当前视图关联视图的跳转状态
     * @param diagramId
     * @return
     */
    Map<String, Set<DiagramRelationInfo>> getRelateInfoByDiagramIds(String diagramId, Integer browseStatus);

    /**
     * 条件查询视图信息
     * @param pageNum 页数
     * @param pageSize 条数
     * @param query 查询条件
     * @return
     */
    Page<ESDiagram> selectListByQuery(Integer pageNum, Integer pageSize, QueryBuilder query);

    /**
     * 通过视图id查询视图详细信息接口,不做权限控制及逻辑删除条件限制
     * @param diagramIds 视图id
     * @return 视图详细信息
     */
    List<ESDiagramDTO> queryFullDiagramByIds(List<Long> diagramIds);

    /**
     * 通过视图加密id查询视图全量信息接口,不做权限控制及逻辑删除条件限制
     * @param diagramIds 视图加密id
     * @return 视图全量信息
     */
    List<ESDiagramDTO> queryFullDiagramByIds(Collection<String> diagramIds);

    /**
     * 批量保存、更新视图基本信息
     * @param list 视图基本信息集合
     * @return 保存结果
     */
    Integer saveOrUpdateBatch(List<ESDiagram> list);

    /**
     * 删除视图
     * @param diagramIds 视图加密id
     * @param delDirId 删除至目录id（若空则删除到视图原目录）
     * @param type 1：逻辑删除 2: 物理删除
     * @return 删除异常提示
     */
    String deleteDiagramWithType(List<String> diagramIds, Long delDirId, Integer type);


    /**
     *  查询视图状态 （查库）
     * @param diagramId
     * @return
     */
    Integer queryFlowStatusById(String diagramId);

    /**
     * 批量复制视图
     * @param diagramDirIdMap 视图id、新视图文件夹id
     * @param diagramList 原视图集合
     * @param type 复制类型
     * @return 原视图id、新视图id映射
     */
    Map<String, String> copyDiagramBatch(Map<String, Long> diagramDirIdMap, List<ESDiagram> diagramList, DiagramCopyEnum type);
}
