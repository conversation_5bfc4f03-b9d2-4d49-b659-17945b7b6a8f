package com.uinnova.product.eam.base.model;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.util.Collections;
import java.util.List;
import java.util.Map;

@Data
public class AttrConfInfo {

    private String classCode;

    private Long classId;

    private Long domainId;

    @Comment("隐藏字段")
    private List<String> hiddenAttrs;

    @Comment("下拉菜单属性")
    private List<String> selectableAttrs;

    @Comment("下拉菜单接口调用类型")
    private Map<String, String> callbackType = Collections.emptyMap();

    @Comment("下拉菜单数据字段类型")
    private Map<String, AttrDictInfo> dictType = Collections.emptyMap();

    @Comment("单选框属性")
    private List<String> radioAttrs;

    @Comment("多选框属性")
    private List<String> checkBoxAttrs;

    @Comment("适用场景子属性")
    private List<String> subScenarioAttrs;

}
