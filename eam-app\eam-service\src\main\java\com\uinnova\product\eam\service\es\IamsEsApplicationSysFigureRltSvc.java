package com.uinnova.product.eam.service.es;

import com.uinnova.product.eam.comm.bean.ApplicationSysFigureRlt;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Repository;

import jakarta.annotation.PostConstruct;

/**
 * @Description 应用系统的图关系操作es的数据接口层
 * <AUTHOR>
 * @Date 2020/9/8
 */
@Repository
public class IamsEsApplicationSysFigureRltSvc extends AbstractESBaseDao<ApplicationSysFigureRlt, ApplicationSysFigureRlt> {
    @Override
    public String getIndex() {
        return "uino_eam_applicationsysfigurerlt";
    }

    @Override
    public String getType() {
        return "_doc";
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }
}
