package com.uinnova.product.vmdb.provider.license.proxy;

import com.binary.core.i18n.Language;
import com.binary.core.util.BinaryUtils;
import com.binary.framework.Local;
import com.binary.framework.bean.SimpleUser;
import com.binary.framework.bean.User;
import com.binary.framework.exception.ServiceException;
import com.uinnova.product.vmdb.comm.i18n.MessageUtil;
import com.uinnova.product.vmdb.comm.license.License;
import com.uinnova.product.vmdb.comm.license.LicenseProxy;
import com.uinnova.product.vmdb.provider.license.CcLicenseAuthSvc;
import org.springframework.beans.factory.annotation.Autowired;

public abstract class AbstractLicenseProxy implements LicenseProxy {

	
	@Autowired
	private CcLicenseAuthSvc licenseAuthSvc;
	
	
	private String licenseToken = "b0769e203e017171faea86baa58bc92e";
	private User taskUser;
	
	
	
	protected AbstractLicenseProxy() {
		SimpleUser user = new SimpleUser();
		user.setId(0l);
		user.setUserCode("system");
		user.setUserName("系统");
		user.setKind(2);
		user.setLanguage(Language.ZHC);
		user.setLoginCode("system");
		user.setDomainId(1l);
		this.taskUser = user;
	}
	
	
	@Override
	public License getLicense() {
		License license = null;
		try {
			boolean open = Local.isOpen();
			try {
				if(!open) {
					Local.open(this.taskUser);
					Local.getCriticalObject().setUserObject(licenseToken);
				}
				license = licenseAuthSvc.getRealLicense();
				if(!open) Local.commit();
			}catch(Throwable t) {
				if(!open) Local.rollback();
				throw t;
			}finally {
				if(!open) Local.close();
			}
		}catch(Throwable t) {
			throw BinaryUtils.transException(t, ServiceException.class);
		}
		return license;
	}
	
	
	
	
    public String getLicenseToken() {
		return licenseToken;
	}
	public void setLicenseToken(String licenseToken) {
		MessageUtil.checkEmpty(licenseToken, "licenseToken");
		this.licenseToken = licenseToken;
	}
	public User getTaskUser() {
		return taskUser;
	}
	public void setTaskUser(User taskUser) {
		MessageUtil.checkEmpty(taskUser, "taskUser");
		this.taskUser = taskUser;
	}
	
	
}
