package com.uinnova.product.eam.service.es;

import com.uinnova.product.eam.model.vo.CIRltDelParam;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Repository;

import jakarta.annotation.PostConstruct;

@Repository
public class CIRltDelParamDao extends AbstractESBaseDao<CIRltDelParam, CIRltDelParam> {

    @Override
    public String getIndex() {
        return "ci_rlt_del_param";
    }

    @Override
    public String getType() {
        return "_doc";
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }
}
