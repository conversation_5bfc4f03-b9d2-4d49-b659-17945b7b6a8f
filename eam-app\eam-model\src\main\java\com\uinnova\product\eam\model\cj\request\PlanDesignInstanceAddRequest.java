package com.uinnova.product.eam.model.cj.request;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 方案设计实例新增请求参数
 * <AUTHOR>
 */
@Data
public class PlanDesignInstanceAddRequest {

    /**
     * 主键id
     */
    private Long id;
    /**
     * 方案设计名称
     */
    @NotBlank(message = " 名称必填")
    private String name;

    /**
     * 方案设计说明
     */
    private String explain;

    /**
     * 方案设计模板id
     */
    @NotNull(message = "模板Id必填")
    private Long templateId;

    /**
     * 方案类型id
     */
    @NotNull(message = "方案类型必填")
    private Long typeId;

    /**
     * 主办系统id集合
     */
   /* @NotNull(message = "主办系统必填")
    @Size(min = 1, message = "主办系统必填")*/
    private List<String> ciCodeList;

    /**
     * 默认的系统ciCode
     */
    //@NotBlank(message = "默认系统ciCode必填")
    private String defaultSystemCiCode;

    /**
     * 方案所属文件夹
     */
    @NotNull(message = "方案所属文件夹必填")
    private Long dirId;

    @NotNull(message = "发布位置必填")
    private Long assetsDirId;

    @Comment("选择发布位置类型 方案模板配置了发布位置和类型")
    private Integer assetsDirType;

    @NotNull(message = "发布位置路径")
    private String echoDirName;

    @Comment("制品类型分类")
    private Integer dirType;

    @Comment("分类目录id")
    private Long domainDirId;
}
