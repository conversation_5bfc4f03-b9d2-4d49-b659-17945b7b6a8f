package com.uinnova.product.eam.model;

import java.util.List;

import com.uinnova.product.vmdb.comm.model.ci.CCcCi;

public class SearchCiInfoListWithProp {
	private Long domainId;
	private Integer pageNum;
	private Integer pageSize;
	private CCcCi cdt;
	private String[] words;
	private Long[] tagIds;
	private String lastCiCode;
	private List<DCVAttrBean> attrs;

	public Long getDomainId() {
		return domainId;
	}

	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}

	public Integer getPageNum() {
		return pageNum;
	}

	public void setPageNum(Integer pageNum) {
		this.pageNum = pageNum;
	}

	public Integer getPageSize() {
		return pageSize;
	}

	public void setPageSize(Integer pageSize) {
		this.pageSize = pageSize;
	}

	public CCcCi getCdt() {
		return cdt;
	}

	public void setCdt(CCcCi cdt) {
		this.cdt = cdt;
	}

	public String[] getWords() {
		return words;
	}

	public void setWords(String[] words) {
		this.words = words;
	}

	public Long[] getTagIds() {
		return tagIds;
	}

	public void setTagIds(Long[] tagIds) {
		this.tagIds = tagIds;
	}

	public String getLastCiCode() {
		return lastCiCode;
	}

	public void setLastCiCode(String lastCiCode) {
		this.lastCiCode = lastCiCode;
	}

	public List<DCVAttrBean> getAttrs() {
		return attrs;
	}

	public void setAttrs(List<DCVAttrBean> attrs) {
		this.attrs = attrs;
	}

}
