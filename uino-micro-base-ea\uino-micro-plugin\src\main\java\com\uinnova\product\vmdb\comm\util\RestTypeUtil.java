package com.uinnova.product.vmdb.comm.util;

import com.binary.core.lang.Conver;
import com.binary.framework.bean.Condition;
import com.binary.json.JSON;
import com.uinnova.product.vmdb.comm.bean.QueryListCondition;
import com.uinnova.product.vmdb.comm.bean.QueryPageCondition;
import com.uinnova.product.vmdb.comm.i18n.MessageUtil;
import com.uinnova.product.vmdb.comm.i18n.VerifyType;

import java.util.Map;

/**
 * 
 * <AUTHOR>
 *
 */
public abstract class RestTypeUtil {

    /**
     * 将条件对象转换为MAP
     * 
     * @param jsonString
     * @return
     */
    @SuppressWarnings({ "unchecked", "rawtypes" })
    public static Map<String, Object> toConditionMap(String jsonString) {
        MessageUtil.checkEmpty(jsonString, "jsonString");
        jsonString = jsonString.trim();
        Map<String, Object> map = (Map) JSON.toObject(jsonString);

        return map;
    }

    /**
     * 将json字符串转换为查询列表条件对象
     * 
     * @param jsonString
     * @return
     */
    public static <T extends Condition> QueryPageCondition<T> toPageCondition(String jsonString, Class<T> type) {
        Map<String, Object> map = toConditionMap(jsonString);

        QueryPageCondition<T> qc = new QueryPageCondition<T>();
        qc.setPageNum(Conver.to(map.get("pageNum"), Integer.class));
        qc.setPageSize(Conver.to(map.get("pageSize"), Integer.class));
        qc.setOrders(Conver.to(map.get("orders"), String.class));

        T cdt = Conver.mapping(type, map.get("cdt"));
        try {
            cdt = cdt == null ? type.newInstance() : cdt;
        } catch (Exception e) {
            MessageUtil.throwVerify(VerifyType.EMPTY, "cdt", "");
        }
        qc.setCdt(cdt);
        return qc;

    }

    /**
     * 将json字符串转换为分页查询条件对象
     * 
     * @param jsonString
     * @return
     */
    public static <T extends Condition> QueryListCondition<T> toListCondition(String jsonString, Class<T> type) {
        Map<String, Object> map = toConditionMap(jsonString);

        QueryListCondition<T> qc = new QueryListCondition<T>();
        qc.setOrders(Conver.to(map.get("orders"), String.class));

        T cdt = Conver.mapping(type, map.get("cdt"));
        try {
            cdt = cdt == null ? type.newInstance() : cdt;
        } catch (Exception e) {
            MessageUtil.throwVerify(VerifyType.EMPTY, "cdt", "");
        }
        qc.setCdt(cdt);
        return qc;
    }

}
