package com.uinnova.product.eam.model.dto;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.base.diagram.model.ESDiagram;
import com.uinnova.product.eam.base.diagram.model.ESDiagramLink;
import com.uinnova.product.eam.base.diagram.model.ESDiagramNode;
import com.uino.bean.cmdb.base.ESCIHistoryInfo;
import com.uino.bean.cmdb.base.ESCIRltInfoHistory;
import lombok.Data;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 *  视图快照相关参数
 */
@Data
public class DiagramSnapshotParam {

    @Comment("当前操作快照视图ID")
    private String snapshotId;

    @Comment("操作类型")
    private String type;

    @Comment("当前操作快照视图信息")
    private ESDiagram currentSnapshotInfo;

    @Comment("当前操作快照视图关联的主视图信息")
    private ESDiagram mainDiagramInfo;

    @Comment("在当前操作快照时间之后，最近的手动快照数据 本身如果是手动创建就存本身 后边没有的话就存空")
    private ESDiagram recentSnapshotInfo;

    @Comment("源端视图内的node节点集合")
    private Collection<ESDiagramNode> recentNodeList;

    @Comment("源端视图内的link节点集合")
    private Collection<ESDiagramLink> recentLinkList;

    @Comment("源端视图查询到的历史CI信息")
    private List<ESCIHistoryInfo> recentSnapshotCIList = new ArrayList<>();

    @Comment("源端视图查询到的历史RLT信息")
    private List<ESCIRltInfoHistory> recentSnapshotRLTList = new ArrayList<>();

    @Comment("当前操作视图关联的主视图拥有者")
    private String ownerCode;

    @Comment("当前操作者")
    private String currentCode;

    @Comment("结束操作的返回值：回滚-主视图信息，新建-新视图的信息")
    private String returnDiagramId;

    @Comment("需要删除的CI标识的node数据")
    private List<String> delNodeList = new ArrayList<>();

    @Comment("需要删除的RLT标识的link数据")
    private List<String> delLinkList = new ArrayList<>();

}

