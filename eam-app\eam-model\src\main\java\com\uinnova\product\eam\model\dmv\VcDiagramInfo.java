package com.uinnova.product.eam.model.dmv;

import com.uinnova.product.eam.base.diagram.model.ESDiagramInfoDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class VcDiagramInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("视图基本信息")
    private ESDiagramInfoDTO diagram;

    @ApiModelProperty("ci 3d 坐标信息")
    private String ci3dPoint;

}
