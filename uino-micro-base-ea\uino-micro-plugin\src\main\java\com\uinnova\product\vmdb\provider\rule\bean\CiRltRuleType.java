package com.uinnova.product.vmdb.provider.rule.bean;

import com.binary.framework.exception.ServiceException;



/**
 * CI关联规则类型
 * <AUTHOR>
 */
public enum CiRltRuleType {
	
	
	/**
	 * 朋友圈
	 */
	FRIENDS(1)
	
	;
	
	
	private int v;
	
	
	
	private CiRltRuleType(int v) {
		this.v = v;
	}
	
	
	public int getValue() {
		return this.v;
	}
	
	
	
	public static CiRltRuleType valueOf(int v) {
		switch(v) {
//			case 1: return FIRENDS;
			default: throw new ServiceException(" is wrong value '"+v+"'! ");
		}
	}
	

}
