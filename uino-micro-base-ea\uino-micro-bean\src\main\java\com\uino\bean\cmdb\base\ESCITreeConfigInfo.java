package com.uino.bean.cmdb.base;

import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;

import com.uino.bean.permission.business.IValidDto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * ci树状图配置信息
 * 
 * <AUTHOR>
 *
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value="ci树状图配置信息类",description = "ci树状图配置信息")
public class ESCITreeConfigInfo implements Serializable, IValidDto {
	private static final long serialVersionUID = 1L;
	/**
	 * 主键
	 */
	@ApiModelProperty(value="主键",example = "123")
	private Long id;

	/**
	 * 树名称
	 */
	@ApiModelProperty(value="树名称",example = "fakjf")
	private String name;
	/**
	 * ci分类ids
	 */
	@ApiModelProperty(value="对象分类id集合")
	private List<Long> ciClsIds;

	/**
	 * 层级属性名称
	 */
	@ApiModelProperty(value="层级属性名称集合")
	private List<String> levelAttrNames;
	/** 所属域 */
	@ApiModelProperty(value="所属域id",example="123")
	private Long domainId;

	/** 创建人 */
	@ApiModelProperty(value="创建人",example="mike")
	private String creator;

	/** 修改人 */
	@ApiModelProperty(value="修改人",example = "tom")
	private String modifier;

	/** 创建时间 */
	@ApiModelProperty(value="创建时间")
	private Long createTime;

	/** 修改时间 */
	@ApiModelProperty(value="修改时间")
	private Long modifyTime;

	@Override
	public void valid() {
		Assert.isTrue(StringUtils.isNotBlank(name), "name not null");
		Assert.notEmpty(ciClsIds, "ciClsIds not empty");
		Assert.notEmpty(levelAttrNames, "levelAttrNames not empty");
	}
}
