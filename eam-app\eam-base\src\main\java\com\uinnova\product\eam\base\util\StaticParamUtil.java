package com.uinnova.product.eam.base.util;
/**
 * 静态参数工具类
 * <AUTHOR>
 *
 */
public final class StaticParamUtil {
	public static final Integer DiagramEleType=null;
	//缓存key--------------------------------------------------
	/**
	 * it世界地图上ci缓存
	 */
	public static final String CACHE_KEY_ITMAP_CIS="itMapCiIds";
	//缓存key--------------------------------------------------
	
	//锁key--------------------------------------------------
	/**
	 * 刷新it世界地图ci缓存锁
	 */
	public static final String LOCK_KEY_REFRESH_ITMAP_Ci="lock::refreshItMapCi";
	//锁key--------------------------------------------------
	//常量key--------------------------------------------------
	/**
	 * 搜索ci时返回字段过滤header-key
	 */
	public static final String STATIC_SEARCHCI_FILTERCOLUMN_HEADER="searchciFilterColumn";
	/**
	 * 搜索ci时只返回ci-code
	 */
	public static final String STATIC_SEARCHCI_ONLYCICODE="onlyCiCode";
	/**
	 * 应用墙顺序配置key
	 */
	public static final String STATIC_CONFIG_APPWALLORDER="app_wall_order";
	//常量key--------------------------------------------------

}
