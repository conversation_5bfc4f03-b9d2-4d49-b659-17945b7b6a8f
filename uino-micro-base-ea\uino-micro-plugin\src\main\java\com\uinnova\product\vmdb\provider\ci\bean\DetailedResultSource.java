package com.uinnova.product.vmdb.provider.ci.bean;

/**
 * 保存结果明细来源和保存到服务器的目录名
 * 
 * <AUTHOR>
 *
 */
public enum DetailedResultSource {

	/**
	 * 对象管理页面CI导入
	 */
	CI_CLASS_IMPORT_TYPE(1, "ciClassImport"),

	/**
	 * 一键导入CI页面导入(此类型导出的错误记录可编辑再次导入)
	 */
	CI_CLASS_ONEKEY_IMPORT(2, "ciClassOnekeyImport"),

	/**
	 * 后台保存CI
	 */
	SAVE_CI(3, "SaveCi-DetailedResult"),
	/**
	 * 配置查询搜索CI导出结果
	 */
	CI_SEARCH_EXPORT(4, "ciSearchExport"),
	/**
	 * 关系管理页面CI关系导入
	 */
	CI_RLT_CLASS_IMPORT_TYPE(5, "ciRltClassImport"),
	/**
	 * 后台保存CI关系
	 */
	SAVE_CI_RLT(6, "SaveCiRlt-DetailedResult"),

	/**
	 * 后台删除CI
	 */
	REMOVE_CI(7, "RemoveCi-DetailedResult"),

	/**
	 * 后台删除关系
	 */
	REMOVE_CI_RLT(8, "RemoveCiRlt-DetailedResult");

	/**
	 * 来源类型
	 */
	private Integer type;
	/**
	 * 生成错误文件要存储的服务器的目录名
	 */
	private String value;

	private DetailedResultSource(Integer type, String value) {
		this.type = type;
		this.value = value;
	}

	public Integer getType() {
		return type;
	}

	public String getValue() {
		return value;
	}

}
