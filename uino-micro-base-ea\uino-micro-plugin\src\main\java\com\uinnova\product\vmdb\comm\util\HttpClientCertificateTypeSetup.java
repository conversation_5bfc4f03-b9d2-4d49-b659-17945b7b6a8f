package com.uinnova.product.vmdb.comm.util;

import com.binary.core.http.HttpClient;
import com.binary.core.util.BinaryUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class HttpClientCertificateTypeSetup {

    public final static Logger logger = LoggerFactory.getLogger(HttpClientCertificateTypeSetup.class);

    public HttpClientCertificateTypeSetup(final String httpCertificateType) {
        if (!BinaryUtils.isEmpty(httpCertificateType)) {
            new Thread(new Runnable() {
            	public void run() {
            		try {
                        HttpClient.setDefaultHttpsConfig(httpCertificateType);
                    } catch (Exception e) {
                        logger.error(e.getMessage());
                    }
            	}
            }).start();
        }
    }

}
