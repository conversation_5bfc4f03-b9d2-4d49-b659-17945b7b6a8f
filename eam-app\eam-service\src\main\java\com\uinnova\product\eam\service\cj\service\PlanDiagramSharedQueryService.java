package com.uinnova.product.eam.service.cj.service;

import com.uinnova.product.eam.model.cj.domain.PlanDesignShareRecord;
import com.uinnova.product.eam.model.cj.request.ShareRecordRequest;
import com.uinnova.product.eam.model.cj.vo.PlanShareDetailVO;
import com.uinnova.product.eam.model.cj.vo.ShareVO;

import java.util.List;

/**
 * 方案视图的分享查询service接口
 * <AUTHOR>
 */
public interface PlanDiagramSharedQueryService {

    /**
     * 查询你分享
     *
     * @param request 参数
     * @return list
     */
    List<ShareVO> queryShare(ShareRecordRequest request);

    /**
     * 查询方案分享详情
     *
     * @param planId 方案Id
     * @return {@link PlanShareDetailVO}
     */
    PlanShareDetailVO queryPlanShareDetail(Long planId);


}
