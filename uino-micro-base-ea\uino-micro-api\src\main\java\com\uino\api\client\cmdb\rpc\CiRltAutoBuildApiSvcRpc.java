package com.uino.api.client.cmdb.rpc;

import com.binary.jdbc.Page;
import com.uino.bean.cmdb.base.ESCiRltAutoBuild;
import com.uino.bean.cmdb.business.ClassRltListDto;
import com.uino.bean.cmdb.query.ESCiRltAutoBuildSearchBean;
import com.uino.dao.BaseConst;
import com.uino.provider.feign.cmdb.CiRltAutoBuildFeign;
import com.uino.api.client.cmdb.ICiRltAutoBuildApiSvc;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Classname CiRltAutoBuildApiSvcRpc
 * @Description TODO
 * @Date 2020/6/30 15:27
 * <AUTHOR> sh
 */
@Service
public class CiRltAutoBuildApiSvcRpc implements ICiRltAutoBuildApiSvc {

    @Autowired
    private CiRltAutoBuildFeign ciRltAutoBuildFeign;

    @Override
    public ESCiRltAutoBuild saveDefault(Long sourceCiClassId, Long targetCiClassId, Long rltClassId, Long visualModelId) {
        return ciRltAutoBuildFeign.saveDefault(BaseConst.DEFAULT_DOMAIN_ID, sourceCiClassId, targetCiClassId, rltClassId, visualModelId);
    }

    @Override
    public ESCiRltAutoBuild saveDefault(Long domainId, Long sourceCiClassId, Long targetCiClassId, Long rltClassId, Long visualModelId) {
        return ciRltAutoBuildFeign.saveDefault(domainId, sourceCiClassId, targetCiClassId, rltClassId, visualModelId);
    }

    @Override
    public Long updateCiRltAutoBuild(ESCiRltAutoBuild esCiRltAutoBuild) {
        return ciRltAutoBuildFeign.updateCiRltAutoBuild(esCiRltAutoBuild);
    }

    @Override
    public List<ClassRltListDto> getAutoBuildListByRltId(Long rltId) {
        return null;
    }

    @Override
    public ESCiRltAutoBuild findCiRltAutoBuild(Long sourceCiClassId, Long targetCiClassId, Long rltClassId, Long visualModelId) {
        return ciRltAutoBuildFeign.findCiRltAutoBuild(BaseConst.DEFAULT_DOMAIN_ID, sourceCiClassId, targetCiClassId, rltClassId, visualModelId);
    }

    @Override
    public ESCiRltAutoBuild findCiRltAutoBuild(Long domainId, Long sourceCiClassId, Long targetCiClassId, Long rltClassId, Long visualModelId) {
        return ciRltAutoBuildFeign.findCiRltAutoBuild(domainId, sourceCiClassId, targetCiClassId, rltClassId, visualModelId);
    }

    @Override
    public void deleteCiRltAutoBuild(Long sourceCiClassId, Long targetCiClassId, Long rltClassId, Long visualModelId) {
        ciRltAutoBuildFeign.deleteCiRltAutoBuild(sourceCiClassId, targetCiClassId, rltClassId, visualModelId);
    }

    @Override
    public void runCiRltAutoBuildAll() {
        ciRltAutoBuildFeign.runCiRltAutoBuildAll(BaseConst.DEFAULT_DOMAIN_ID);
    }

    @Override
    public void runCiRltAutoBuildAll(Long domainId) {
        ciRltAutoBuildFeign.runCiRltAutoBuildAll(domainId);
    }

    @Override
    public boolean runCiRltAutoBuildById(Long id) {
        return ciRltAutoBuildFeign.runCiRltAutoBuildById(id);
    }

    @Override
    public Long saveCiRltAutoBuild(ESCiRltAutoBuild esCiRltAutoBuild) {
        return ciRltAutoBuildFeign.saveCiRltAutoBuild(esCiRltAutoBuild);
    }

    @Override
    public Page<ESCiRltAutoBuild> queryCiRltAutoBuildPage(ESCiRltAutoBuildSearchBean searchBean) {
        return ciRltAutoBuildFeign.queryCiRltAutoBuildPage(searchBean);
    }

    @Override
    public Integer deleteById(Long id) {
        return ciRltAutoBuildFeign.deleteById(id);
    }

    @Override
    public Long saveOrUpdate(ESCiRltAutoBuild esCiRltAutoBuild) {
        return ciRltAutoBuildFeign.saveOrUpdate(esCiRltAutoBuild);
    }

    @Override
    public List<ESCiRltAutoBuild> getAutoBuildListByRltClassId(List<Long> rltClassIds) {

        return null;
    }

    @Override
    public void runCiRltAutoBuildByIds(List<Long> ids) {

    }

    @Override
    public void deleteCiRltAutoBuildBatchByCiClassIds(List<Long> ciClassIds, Long visualModelId) {

    }
}
