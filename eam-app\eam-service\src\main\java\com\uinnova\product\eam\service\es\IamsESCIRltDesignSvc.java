package com.uinnova.product.eam.service.es;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.ValueFilter;
import com.binary.jdbc.Page;
import com.uinnova.product.eam.model.vo.ESRltSearchBeanVO;
import com.uinnova.product.vmdb.comm.model.rlt.CCcCiRlt;
import com.uinnova.product.vmdb.provider.rlt.bean.CcCiRltInfo;
import com.uino.bean.cmdb.base.ESCIRltInfo;
import com.uino.bean.cmdb.query.ESRltSearchBean;
import com.uino.bean.local.RltDelContext;
import com.uino.bean.local.RltDelContextValue;
import com.uino.bean.sys.base.RltSourceId;
import com.uino.dao.AbstractESBaseDao;
import com.uino.dao.ESConst;
import com.uino.dao.util.ESUtil;
import jakarta.annotation.PostConstruct;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.elasticsearch.common.settings.Settings;
import org.elasticsearch.index.query.*;
import org.elasticsearch.search.sort.SortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.LinkedList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 设计库ci关系es
 * 
 * <AUTHOR>
 *
 */
 @Repository
public class IamsESCIRltDesignSvc extends AbstractESBaseDao<ESCIRltInfo, CCcCiRlt> {

    private static Log logger = LogFactory.getLog(IamsESCIRltDesignSvc.class);
    private static final String DATA_STATUS = "dataStatus";
    private static final String SOURCE_ID = "sourceId";

    @Autowired
    IamsESCmdbCommDesignSvc commSvc;

    @Autowired
    private IamsCIRltDesignNonComplianceDao iamsCIRltDesignNonComplianceDao;


    @Override
    public String getIndex() {
        return ESConst.INDEX_CMDB_RLT + "_design";
    }

    @Override
    public String getType() {
        return ESConst.INDEX_CMDB_RLT + "_design";
    }
    @PostConstruct
    public void init() {
        super.initIndex(5);
    }

    @Value("${rlt.del.ignore.options:[VIS_MODEL_BUILD_AOTO,INTERFACE_SYNC]}")
    private List<RltSourceId> rltDelIgnoreOptions;

    @Override
    public Settings getSetting(String analyzerName, int number_of_shards) {
        return Settings.builder()
                .put("number_of_shards", number_of_shards)
                .put("number_of_replicas", 1)
                //修改索引的最大字段，对象管理会自定义字段，超出会报错，这里改为1w个字段，初步推算在2000~3000个关系
                .put("index.mapping.total_fields.limit",10000)
                .put("index.max_result_window", 1000000)
                // analysis 配置开始
                .put("analysis.normalizer.my_normalizer.type", "custom")
                .putList("analysis.normalizer.my_normalizer.filter", "lowercase")
                .put("analysis.analyzer." + analyzerName + ".type", "custom")
                .put("analysis.analyzer." + analyzerName + ".tokenizer", "standard")
                .putList("analysis.analyzer." + analyzerName + ".filter", "lowercase", "reverse")
                .build();
    }
    @Override
    public Long saveOrUpdate(ESCIRltInfo t) {
        savePreOption(t);
        JSONObject json = JSON.parseObject(JSON.toJSONString(t, new ValueFilter() {

            @Override
            public Object process(Object object, String name, Object value) {
                if (value == null) { return ""; }
                return value;
            }
        }));
        return super.saveOrUpdate(json, true);
    }

    /**
     * <b>根据cdt对象查询分类
     *
     * @param pageNum
     * @param pageSize
     * @param bean
     *            自动生成的分类查询对象
     * @return
     */
    public Page<CcCiRltInfo> searchRltByCdt(int pageNum, int pageSize, CCcCiRlt bean) {
        QueryBuilder query = ESUtil.cdtToBuilder(bean);
        Page<ESCIRltInfo> page = super.getSortListByQuery(pageNum, pageSize, query, "modifyTime", false);
        return commSvc.tranCcCiRltInfoPage(page);
    }

    /**
     * <b> 查询关系
     *
     * @param bean
     * @return
     */
    public Page<CcCiRltInfo> searchRltByBean(ESRltSearchBean bean) {
        return commSvc.tranCcCiRltInfoPage(this.searchESRltByBean(bean));
    }

    public Page<ESCIRltInfo> searchESRltByBean(ESRltSearchBean bean) {
        QueryBuilder query = commSvc.getRltQueryBuilderByBean(bean);
        List<SortBuilder<?>> sorts = new LinkedList<>();
        if (bean.getSortField().startsWith("attrs.")) {
            sorts.add(SortBuilders.fieldSort(bean.getSortField().concat(".keyword")).order(bean.getIsAsc() ? SortOrder.ASC : SortOrder.DESC));
        } else {
            sorts.add(SortBuilders.fieldSort(bean.getSortField()).order(bean.getIsAsc() ? SortOrder.ASC : SortOrder.DESC));
        }
        Page<ESCIRltInfo> page = super.getSortListByQuery(bean.getPageNum(), bean.getPageSize(), query, sorts);

        return page;
    }

    public Page<CcCiRltInfo> rltPageToFullPage(Page<ESCIRltInfo> rltPage) {
        return commSvc.tranCcCiRltInfoPage(rltPage);
    }

    public List<CcCiRltInfo> searchRltByScroll(ESRltSearchBean bean) {
        return commSvc.tranCcCiRltInfoList(searchESRltByScroll(bean));
    }

    public List<ESCIRltInfo> searchESRltByScroll(ESRltSearchBean bean) {
        QueryBuilder query = commSvc.getRltQueryBuilderByBean(bean);
        List<SortBuilder<?>> sorts = new LinkedList<>();
        String sortField = bean.getSortField();
        if (bean.getSortField().startsWith("attrs.")) {
            String[] split = sortField.split("\\.");
            sortField = "attrs." + (split.length > 1 ? split[1] : sortField).toUpperCase() + ".keyword";
            long count = this.countByCondition(QueryBuilders.existsQuery(sortField));
            if (count <= 0) {
                sortField = "modifyTime";
            }
            sorts.add(SortBuilders.fieldSort(sortField).order(bean.getIsAsc() ? SortOrder.ASC : SortOrder.DESC));
        } else {
            sorts.add(SortBuilders.fieldSort(bean.getSortField()).order(bean.getIsAsc() ? SortOrder.ASC : SortOrder.DESC));
        }
        return super.getListByQueryScroll(query);
    }

    public Page<CcCiRltInfo> searchRltByBeanVO(ESRltSearchBeanVO bean) {
        return commSvc.tranCcCiRltInfoPage(this.searchESRltByBeanVO(bean));
    }

    private Page<ESCIRltInfo> searchESRltByBeanVO(ESRltSearchBeanVO bean) {
        BoolQueryBuilder query = (BoolQueryBuilder) commSvc.getRltQueryBuilderByBean(bean);
        if (StringUtils.isNotEmpty(bean.getGteTime())) {
            Long gteTime = convertTime(bean.getGteTime());
            query.must(QueryBuilders.rangeQuery("createTime").gte(gteTime));
        }
        if (StringUtils.isNotEmpty(bean.getLteTime())) {
            Long lteTime = convertEndTime(bean.getLteTime());
            query.must(QueryBuilders.rangeQuery("createTime").lte(lteTime));
        }
        List<SortBuilder<?>> sorts = new LinkedList<>();
        if (bean.getSortField().startsWith("attrs.")) {
            sorts.add(SortBuilders.fieldSort(bean.getSortField().concat(".keyword")).order(bean.getIsAsc() ? SortOrder.ASC : SortOrder.DESC));
        } else {
            sorts.add(SortBuilders.fieldSort(bean.getSortField()).order(bean.getIsAsc() ? SortOrder.ASC : SortOrder.DESC));
        }
        if (bean.getUsage() == 1) {
            Page<ESCIRltInfo> page = super.getSortListByQuery(bean.getPageNum(), bean.getPageSize(), query, sorts);
            return page;
        }else{
            Page<ESCIRltInfo> page = iamsCIRltDesignNonComplianceDao.getSortListByQuery(bean.getPageNum(), bean.getPageSize(), query, sorts);
            return page;
        }

    }

    private Long convertEndTime(String lteTime) {
        String replace = lteTime.replace("/", "")+"240000";
        return Long.valueOf(replace);
    }

    private Long convertTime(String convertTime) {
        String replace = convertTime.replace("/", "")+"000000";
        return Long.valueOf(replace);
    }

    @Override
    protected void savePreOptionCore(ESCIRltInfo esciRltInfo) {
        //保存时如果数据状态为空，则默认数据状态为正常
        if (esciRltInfo.getDataStatus() == null) {
            esciRltInfo.setDataStatus(1);
        }
    }

    @Override
    protected QueryBuilder searchQueryWrapper(QueryBuilder queryBuilder) {
        String queryStr = queryBuilder.toString();
        if (queryStr.contains(DATA_STATUS)) {
            return queryBuilder;
        }
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(queryBuilder);
        //这里用非删除状态去查，即空或正常。避免刷历史数据
        query.mustNot(QueryBuilders.termQuery(DATA_STATUS, 0));
        return query;
    }

    @Override
    protected QueryBuilder updateQueryWrapper(QueryBuilder queryBuilder) {
        String queryStr = queryBuilder.toString();
        if (queryStr.contains(DATA_STATUS)) {
            return queryBuilder;
        }
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(queryBuilder);
        //这里用非删除状态去查，即空或正常。避免刷历史数据
        query.mustNot(QueryBuilders.termQuery(DATA_STATUS, 0));
        return query;
    }

    @Override
    public Integer deleteByQuery(QueryBuilder query, boolean isRefresh) {
        BoolQueryBuilder wrapperQuery = QueryBuilders.boolQuery();
        wrapperQuery.must(query);
        //非强制删除（关系管理允许强制删除）
        if (!dropForce()) {
            //忽略不需要删除的操作
            Set<Long> ingoreSourceIds = rltDelIgnoreOptions.stream().map(RltSourceId::getCode).collect(Collectors.toSet());
            wrapperQuery.mustNot(QueryBuilders.termsQuery(SOURCE_ID, ingoreSourceIds));
        }
        long count = countByCondition(wrapperQuery);
        updateByQuery(wrapperQuery, "ctx._source.dataStatus=" + 0, isRefresh);
        return (int) count;
    }

    /**
     * 关系是否强制删除
     * @return
     */
    private Boolean dropForce() {
        RltDelContextValue contextValue = RltDelContext.getContextValue();
        return contextValue != null && Boolean.TRUE.equals(contextValue.getDelForce());
    }

    @Override
    public Integer deleteById(Long id) {
        TermQueryBuilder query = QueryBuilders.termQuery("id", id);
        return deleteByQuery(query, true);
    }

    @Override
    public Integer deleteByIds(Collection<Long> ids) {
        TermsQueryBuilder query = QueryBuilders.termsQuery("id", ids);
        return deleteByQuery(query, true);
    }
}
