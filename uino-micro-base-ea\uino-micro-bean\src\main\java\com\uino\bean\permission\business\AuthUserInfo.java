package com.uino.bean.permission.business;

import java.util.List;
import java.util.Map;
import java.util.Set;

import com.uino.bean.permission.base.SysModule;
import com.uino.bean.permission.base.SysRoleDataModuleRlt;
import com.uino.bean.permission.base.SysUser;

import lombok.Getter;
import lombok.Setter;

/**
 * 认证的用户信息
 * 
 * <AUTHOR>
 */
@Setter
@Getter
public class AuthUserInfo extends SysUser {

    private static final long serialVersionUID = 811660654598287496L;

    /**
     * 合并后的菜单
     */
    private Set<SysModule> menus;

    /**
     * 合并后的数据权限
     */
    private Map<String, List<SysRoleDataModuleRlt>> dataModule;

}
