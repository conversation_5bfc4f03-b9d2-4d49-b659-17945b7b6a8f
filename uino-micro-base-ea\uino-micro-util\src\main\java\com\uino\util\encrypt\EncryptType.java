package com.uino.util.encrypt;

/**
 * @Title: EncryptType
 * @Author: YGQ
 * @Create: 2021-08-08 22:34
 **/
public enum EncryptType {
    /**
     * Base64 encryption
     */
    Base64Encrypt,
    /**
     * Base64 decryption
     */
    Base64Decrypt,
    /**
     * AES encryption, public key needs 16 bits
     */
    AesEncrypt,
    /**
     * AES decryption, public key needs 16 bits
     */
    AesDecrypt,
    /**
     * DES encryption, public key needs 8 bits
     */
    DesEncrypt,
    /**
     * DES decryption, public key needs 8 bits
     */
    DesDecrypt,
    /**
     * Jasypt encryption, need public key
     */
    JasyptEncrypt,
    /**
     * Jasypt decryption, need public key
     */
    JasyptDecrypt,
    /**
     * Rsa encryption, need public key
     */
    RsaEncrypt,
    /**
     * Rsa decryption, need public key
     */
    RsaDecrypt
}
