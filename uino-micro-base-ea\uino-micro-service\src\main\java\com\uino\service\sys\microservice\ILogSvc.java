package com.uino.service.sys.microservice;

import com.binary.jdbc.Page;
import com.uino.bean.sys.base.SysLoginLog;
import com.uino.bean.sys.business.QueryLoginLogRequestDto;

/**
 * 日志相关服务类
 * 
 * <AUTHOR>
 *
 */
public interface ILogSvc {

	/**
     * 增加一条登陆日志
     * 
     * @param userId
     * @return
     */
	public SysLoginLog addLoginLog(Long userId);

	/**
	 * 增加一条登陆日志
	 * 
	 * @param userCode
	 * @return
	 */
	public SysLoginLog addLoginLog(Long domainId, String userCode);

	/**
	 * 查询登陆日志
	 * 
	 * @param query
	 * @return
	 */
	public Page<SysLoginLog> queryLoginLog(QueryLoginLogRequestDto query);
}
