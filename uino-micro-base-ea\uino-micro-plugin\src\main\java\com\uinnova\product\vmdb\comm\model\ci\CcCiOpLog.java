package com.uinnova.product.vmdb.comm.model.ci;

import com.binary.core.util.BinaryUtils;
import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;
import com.binary.json.JSON;
import com.uinnova.product.vmdb.comm.util.CommUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 
 * <AUTHOR>
 *
 */
@Comment("CI操作日志表[CC_CI_OP_LOG]")
public class CcCiOpLog implements EntityBean {
    private static final long serialVersionUID = 1L;

    private List<CcCiOpLog> subOplogList;

    @Comment("ID[ID]")
    private Long id;

    @Comment("所属CI[CI_ID]")
    private Long ciId;

    @Comment("CI分类[CLASS_ID]")
    private Long classId;

    @Comment("变更时间[OP_TIME]    yyyyMMddHHmmss")
    private Long opTime;

    @Comment("变更人ID[OPOR_ID]")
    private Long oporId;

    @Comment("变更人代码[OPOR_CODE]")
    private String oporCode;

    @Comment("变更人姓名[OPOR_NAME]")
    private String oporName;

    @Comment("操作类型[OP_TYPE]    1=创建 2=修改 3=删除")
    private Integer opType;

    @Comment("更新字段[UP_FIELDS]    [{id:'', name:'', before:'', after:''}]")
    private String upFields;

    @Comment("所属域[DOMAIN_ID]")
    private Long domainId;

    @Comment("创建时间[CREATE_TIME]    yyyyMMddHHmmss")
    private Long createTime;

    @Comment("修改时间[MODIFY_TIME]    yyyyMMddHHmmss")
    private Long modifyTime;

    /**
     * 检查生成的upField是否超出数据库长度. 如果超出,会生成一个CcCiOpLog集合来分段存贮 该方法因在其他属性全部赋值后调用
     * 
     * @param upFieldSb
     */
    public void checkUpFieldSbLength(List<Map<String, String>> upFieldSb) {
        if (BinaryUtils.isEmpty(upFieldSb)) {
            return;
        }
        // []
        int count = 2; 
        int fromIndex = 0;
        for (int i = 0; i < upFieldSb.size(); i++) {
            Map<String, String> map = upFieldSb.get(i);
            // {"id":"","after":"","before":"","name":""},
            int c = 43; 
            String id = map.get("id");
            String name = map.get("name");
            String before = map.get("before");
            String after = map.get("after");
            c += CommUtil.countUtf8ByteSize(id);
            c += CommUtil.countUtf8ByteSize(name);
            c += CommUtil.countUtf8ByteSize(before);
            c += CommUtil.countUtf8ByteSize(after);
            count += c;
            if (4000 < count) {
                // 超出数据库长度, 创建新的CcCiOpLog, 将当前i赋值给fromIndex, 重置count
                count = 2 + c;
                List<Map<String, String>> subList = upFieldSb.subList(fromIndex, fromIndex = i);
                CcCiOpLog subOpLog = CommUtil.copy(this, CcCiOpLog.class);
                subOpLog.setUpFields(JSON.toString(subList));
                if (null == subOplogList) {
                    subOplogList = new ArrayList<CcCiOpLog>();
                }
                subOplogList.add(subOpLog);
            }
        }

        // 如果超过4000长度, this对象存储最后一组的upFields
        List<Map<String, String>> subList = upFieldSb.subList(fromIndex, upFieldSb.size());
        setUpFields(JSON.toString(subList));
        if (null != subOplogList) {
            subOplogList.add(this);
        }
    }

    public List<CcCiOpLog> gainSubOplogList() {
        return subOplogList;
    }

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCiId() {
        return this.ciId;
    }

    public void setCiId(Long ciId) {
        this.ciId = ciId;
    }

    public Long getClassId() {
        return this.classId;
    }

    public void setClassId(Long classId) {
        this.classId = classId;
    }

    public Long getOpTime() {
        return this.opTime;
    }

    public void setOpTime(Long opTime) {
        this.opTime = opTime;
    }

    public Long getOporId() {
        return this.oporId;
    }

    public void setOporId(Long oporId) {
        this.oporId = oporId;
    }

    public String getOporCode() {
        return this.oporCode;
    }

    public void setOporCode(String oporCode) {
        this.oporCode = oporCode;
    }

    public String getOporName() {
        return this.oporName;
    }

    public void setOporName(String oporName) {
        this.oporName = oporName;
    }

    public Integer getOpType() {
        return this.opType;
    }

    public void setOpType(Integer opType) {
        this.opType = opType;
    }

    public String getUpFields() {
        return this.upFields;
    }

    public void setUpFields(String upFields) {
        this.upFields = upFields;
    }

    public Long getDomainId() {
        return this.domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }

    public Long getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getModifyTime() {
        return this.modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }

}
