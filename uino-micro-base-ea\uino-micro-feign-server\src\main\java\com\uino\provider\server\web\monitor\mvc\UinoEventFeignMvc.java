package com.uino.provider.server.web.monitor.mvc;

import com.binary.jdbc.Page;
import com.uino.bean.event.EventCiKpiCount;
import com.uino.bean.event.modeling.EventModel;
import com.uino.bean.event.param.EventCiKpiCountParam;
import com.uino.bean.event.param.EventCiSeverityQueryParam;
import com.uino.bean.monitor.base.ESAlarm;
import com.uino.bean.monitor.base.ESMonEapEvent;
import com.uino.bean.monitor.base.ESMonSysSeverityInfo;
import com.uino.bean.monitor.buiness.CurrentEventQueryDto;
import com.uino.bean.monitor.buiness.EventQueryDto;
import com.uino.monitor.event.service.IEventService;
import com.uino.provider.feign.monitor.UinoEventFeign;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.QueryBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Set;

@Slf4j
@RestController
@RequestMapping("feign/uinoEvent")
public class UinoEventFeignMvc implements UinoEventFeign {

	@Autowired
	private IEventService eventService;

	@Override
	public EventModel getEventModel() {
		return eventService.getEventModel();
	}

	@Override
	public Page<ESMonEapEvent> searchEventPage(EventQueryDto queryDto) {
		return eventService.searchEventPage(queryDto);
	}

	@Override
	public Page<ESMonEapEvent> searchEventPage(EventQueryDto queryDto, QueryBuilder mustQuery, QueryBuilder shouldQuery) {
		return eventService.searchEventPage(queryDto, mustQuery, shouldQuery);
	}

	@Override
	public void saveAlarm(ESAlarm saveBean) {
		eventService.saveAlarm(saveBean);
	}

	@Override
	public void saveCurrentEvent(ESMonEapEvent fmEvent) {
		eventService.saveCurrentEvent(fmEvent);
	}

	@Override
	public List<ESMonEapEvent> listCurrentEventByParam(CurrentEventQueryDto queryDto) {
		return eventService.listCurrentEventByParam(queryDto);
	}

	@Override
	public List<ESMonEapEvent> listCurrentEventByParam(List<CurrentEventQueryDto> queryDtos) {
		return eventService.listCurrentEventByParam(queryDtos);
	}

	@Override
	public void delCurrentEvent(ESMonEapEvent fmEvent) {
		eventService.delCurrentEvent(fmEvent);
	}

	@Override
	public List<ESMonEapEvent> listCurrentEventByIds(Set<String> ids) {
		return eventService.listCurrentEventByIds(ids);
	}

	@Override
	public List<ESMonEapEvent> listCurrentEventByCiIds(List<Long> ciIds) {
		return eventService.listCurrentEventByCiIds(ciIds);
	}

	@Override
	public ESMonEapEvent currentEventById(String id) {
		return eventService.currentEventById(id);
	}

	@Override
	public void clearCurrentEvent(Long domainId) {
		eventService.clearCurrentEvent(domainId);
	}

	@Override
	public ESMonEapEvent saveEventHis(ESMonEapEvent esMonEapEvent) {
		return eventService.saveEventHis(esMonEapEvent);
	}

	@Override
	public ESMonEapEvent hisEventById(String id) {
		return eventService.hisEventById(id);
	}

	@Override
	public List<ESMonEapEvent> listCurrentEventByCiCodes(Long domainId, List<String> ciCodes) {
		return eventService.listCurrentEventByCiCodes(domainId, ciCodes);
	}

	@Override
	public List<ESMonSysSeverityInfo> getAllEventSeverity(Long domainId) {
		return eventService.getAllEventSeverity(domainId);
	}

	@Override
	public List<EventCiKpiCount> getEventCountByCiCodeAndKpiCodes(Long domainId, EventCiKpiCountParam param) {
		return eventService.getEventCountByCiCodeAndKpiCodes(domainId, param.getCiCodes(), param.getKpiCodes());
	}

	@Override
	public boolean saveEventBatch(List<ESMonEapEvent> events) {
		return eventService.saveEventBatch(events);
	}

	@Override
	public List<ESMonEapEvent> queryCurrentEventsByCiCodesAndServeritys(Long domainId, EventCiSeverityQueryParam queryParam) {
		return eventService.queryCurrentEventsByCiCodesAndServeritys(domainId, queryParam.getCiCodes(), queryParam.getSeveritys(), queryParam.getStatus());
	}
}
