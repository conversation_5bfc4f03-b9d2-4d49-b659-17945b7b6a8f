package com.uino.dao.sys;

import com.alibaba.fastjson.JSONObject;
import com.uino.bean.sys.base.TenantDomain;
import com.uino.dao.AbstractESBaseDao;
import com.uino.dao.ESConst;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @Date 2021/10/27
 * @Version 1.0
 */
@Component
public class ESTenantDomainSvc extends AbstractESBaseDao<TenantDomain, JSONObject> {
    @Override
    public String getIndex() {
        return ESConst.TENANT_DOMAIN;
    }

    @Override
    public String getType() {
        return ESConst.TENANT_DOMAIN;
    }


    @PostConstruct
    public void init() {
//        TenantDomain tenantDomain = TenantDomain.builder()
//                .id(1L)
//                .name("默认租户")
//                .dataStatus(1)
//                .enableStatus(1)
//                .creator("superadmin").build();
        super.initIndex();
    }
}
