package com.uinnova.product.eam.service.es;

import com.uinnova.product.eam.comm.model.es.AutoLayoutDiagramConf;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;

/**
 * 自由绘图自动成图配置Dao
 */
@Service
public class AutoLayoutDiagramConfDao extends AbstractESBaseDao<AutoLayoutDiagramConf, AutoLayoutDiagramConf> {
    @Override
    public String getIndex() {
        return "uino_diagram_auto_layout_conf";
    }

    @Override
    public String getType() {
        return "_doc";
    }
    @PostConstruct
    public void init() {
        super.initIndex();
    }
}
