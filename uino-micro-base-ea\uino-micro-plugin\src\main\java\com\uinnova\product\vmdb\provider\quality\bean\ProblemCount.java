package com.uinnova.product.vmdb.provider.quality.bean;

import com.uinnova.product.vmdb.comm.model.es.ESCiQualityDataCount;

import java.util.HashMap;
import java.util.Map;

import static com.uinnova.product.vmdb.comm.util.CommUtil.doubleValue;

public class ProblemCount {

    private static final long serialVersionUID = 1L;

    private Long time;

    private Integer count;

    private Double orphanHealth = -1.0;

    private Double validityHealth = -1.0;

    private Double completenessHealth = -1.0;

    private Double health = -1.0;

    public ProblemCount() {}

    public ProblemCount(String source, ESCiQualityDataCount qualityDataCount, QualityCheckDefine.WeightCoefficient weightCoefficient) {

        Long time = qualityDataCount.getTime();
        Map<String, Double> originalCompletenessHealth = new HashMap<String, Double>();
        Map<String, Double> originalOrphanHealth = new HashMap<String, Double>();
        Map<String, Double> originalValidityHealth = new HashMap<String, Double>();
        Map<String, Integer> counts = new HashMap<String, Integer>();
        if (qualityDataCount.getOriginalCompletenessHealth() != null) {
            originalCompletenessHealth = qualityDataCount.getOriginalCompletenessHealth();
        }
        if (qualityDataCount.getOriginalOrphanHealth() != null) {
            originalOrphanHealth = qualityDataCount.getOriginalOrphanHealth();
        }
        if (qualityDataCount.getOriginalValidityHealth() != null) {
            originalValidityHealth = qualityDataCount.getOriginalValidityHealth();
        }
        counts = qualityDataCount.getProblemCounts();

        this.setTime(time);

        int healthCount = 0;
        double health = 0.0;
        Double comHealth = originalCompletenessHealth.get(source);
        if (comHealth != null) {
            this.setCompletenessHealth(doubleValue(comHealth * weightCoefficient.getCompleteness()));
            health += this.getCompletenessHealth();
            healthCount ++;
        }
        Double orphanHealth = originalOrphanHealth.get(source);
        if (orphanHealth != null) {
            this.setOrphanHealth(doubleValue(orphanHealth * weightCoefficient.getOrphan()));
            health += this.getOrphanHealth();
            healthCount ++;
        }
        Double validHealth = originalValidityHealth.get(source);
        if (validHealth != null) {
            this.setValidityHealth(doubleValue(validHealth * weightCoefficient.getValidity()));
            health += this.getValidityHealth();
            healthCount ++;
        }
        if (healthCount > 0) {
            this.setHealth(doubleValue(health / healthCount));
        }

        Integer count = counts.get(source);
        this.setCount(count);
    }

    public Long getTime() {
        return time;
    }

    public void setTime(Long time) {
        this.time = time;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
            this.count = count;
    }

    public Double getOrphanHealth() {
        return orphanHealth;
    }

    public void setOrphanHealth(Double orphanHealth) {
        if (orphanHealth > 100) {
            this.orphanHealth = 100.0;
        } else {
            this.orphanHealth = orphanHealth;
        }
    }

    public Double getValidityHealth() {
        return validityHealth;
    }

    public void setValidityHealth(Double validityHealth) {
        if (validityHealth > 100) {
            this.validityHealth = 100.0;
        } else {
            this.validityHealth = validityHealth;
        }
    }

    public Double getCompletenessHealth() {
        return completenessHealth;
    }

    public void setCompletenessHealth(Double completenessHealth) {
        if (completenessHealth > 100) {
            this.completenessHealth = 100.0;
        } else {
            this.completenessHealth = completenessHealth;
        }
    }

    public Double getHealth() {
        return health;
    }

    public void setHealth(Double health) {
        if (health > 100) {
            this.health = 100.0;
        } else {
            this.health = health;
        }
    }
}
