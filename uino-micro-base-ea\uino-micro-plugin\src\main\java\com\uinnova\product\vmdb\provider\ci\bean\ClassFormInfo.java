package com.uinnova.product.vmdb.provider.ci.bean;

import com.uinnova.product.vmdb.comm.model.ci.CcCiClassAttrDisp;
import com.uinnova.product.vmdb.comm.model.ci.CcCiClassForm;

import java.util.List;

public class ClassFormInfo {
	private CcCiClassForm ciClassForm;

	private List<CcCiClassAttrDisp> attrDisps;

	public CcCiClassForm getCiClassForm() {
		return ciClassForm;
	}

	public void setCiClassForm(CcCiClassForm ciClassForm) {
		this.ciClassForm = ciClassForm;
	}

	public List<CcCiClassAttrDisp> getAttrDisps() {
		return attrDisps;
	}

	public void setAttrDisps(List<CcCiClassAttrDisp> attrDisps) {
		this.attrDisps = attrDisps;
	}

}
