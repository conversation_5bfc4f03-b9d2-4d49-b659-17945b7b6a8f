package com.uinnova.product.eam.base.diagram.model;


import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description 视图节点dto
 * @Date 17:14 2021/6/3
 * @Param
 * @return
 **/
@Data
public class ESDiagramNodeQueryBean {

	private Long id;

	private String key;

	private String[] keys;

	private String sheetIdEqual;

	private String[] sheetIds;

	private Long diagramId;

	private String nodeJson;

	private Long createTime;

	private Long modifyTime;

	private Long[] ids;

	private Long[] diagramIds;

	@Comment("转换老视图标识，0-老视图")
	private Integer transFlag;

	private String reservedField1;

	private String reservedField2;

	private String reservedField3;
}


