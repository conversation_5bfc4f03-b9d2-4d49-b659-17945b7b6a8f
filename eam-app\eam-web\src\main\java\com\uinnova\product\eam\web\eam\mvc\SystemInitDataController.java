package com.uinnova.product.eam.web.eam.mvc;

import cn.hutool.core.io.resource.ClassPathResource;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.google.common.collect.Sets;
import com.uinnova.product.eam.service.IEamArtifactSvc;
import com.uinnova.product.eam.service.asset.BmConfigSvc;
import com.uinnova.product.eam.service.flow.init.FlowSystemInitService;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClass;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClassDir;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.comm.model.ci.CcCiClass;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uino.api.client.cmdb.IVisualModelApiSvc;
import com.uino.bean.cmdb.base.*;
import com.uino.dao.cmdb.ESRltClassSvc;
import com.uino.dao.util.ESUtil;
import com.uino.service.cmdb.microservice.ICIClassSvc;
import com.uino.service.cmdb.microservice.IDirSvc;
import com.uino.service.cmdb.microservice.IRltClassSvc;
import com.uino.service.util.FileUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;

/**
 * 系统初始化数据
 *
 * <AUTHOR>
 * @since 2025/2/21 16:46
 */
@Slf4j
@RestController
@RequestMapping("/system/init")
public class SystemInitDataController {

    @Resource
    private FlowSystemInitService flowSystemInitService;

    /**
     * 获取流程体系初始化数据
     *
     * @return
     */
    @GetMapping("/getFlowInitData")
    public ResponseEntity<byte[]> getFlowInitData() {
        return flowSystemInitService.getFlowInitData();
    }

    @GetMapping("/initFlowData")
    public void initFlowData() {
        flowSystemInitService.initFlowData();
    }

}
