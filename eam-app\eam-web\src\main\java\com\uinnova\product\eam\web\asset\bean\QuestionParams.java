package com.uinnova.product.eam.web.asset.bean;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.List;

@Data
public class QuestionParams {

    @NotBlank(message="子系统名称不能为空")
    private String systemName;

    @NotNull
    private List<SelectKey> matchList;

    @Data
    public static class SelectKey{
        private String value;
        private String itemNo;
    }

}
