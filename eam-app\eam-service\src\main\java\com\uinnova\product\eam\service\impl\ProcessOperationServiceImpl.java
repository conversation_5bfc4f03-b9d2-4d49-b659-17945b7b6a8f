package com.uinnova.product.eam.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.binary.core.util.BinaryUtils;
import com.uinnova.product.eam.model.FlowOperationStatisticsDto;
import com.uinnova.product.eam.service.ProcessOperationService;
import com.uinnova.product.eam.service.asset.BmConfigSvc;
import com.uinnova.product.eam.service.es.ActivityOperationDao;
import com.uinnova.product.eam.service.es.FlowSystemOperationDao;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.bucket.histogram.DateHistogramInterval;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.time.LocalDate;
import java.util.*;

@Slf4j
@Service
public class ProcessOperationServiceImpl implements ProcessOperationService {

    private final static String PROCESS_MAPPING = "PROCESS_MAPPING_RELATIONSHIP";

    private final static String ACTIVITY_MAPPING = "ACTIVITY_MAPPING_RELATIONSHIP";

    @Autowired
    private FlowSystemOperationDao workFlowOperationDao;

    @Autowired
    private ActivityOperationDao activityOperationDao;

    @Resource
    private BmConfigSvc bmConfigSvc;

    /**
     * 获取流程运行情况(各状态总计)
     *
     * @param dto
     * @return
     */
    @Override
    public Map<String, Long> getProcessOperationStatus(FlowOperationStatisticsDto dto) {
        Map<String, Long> result = new HashMap<>(3);
        String id = getId(dto.getCiCode());
        if (BinaryUtils.isEmpty(id)) {
            return result;
        }
        Map<String, String> keyMap = new HashMap<>();
        keyMap.put("1", "办结次数");
        keyMap.put("2", "在途次数");
        keyMap.put("3", "终止次数");
        BoolQueryBuilder query = QueryBuilders.boolQuery()
                .must(QueryBuilders.termQuery("processTypeId.keyword", id))
                .must(QueryBuilders.rangeQuery("processStateTime")// 筛选开始日期在指定范围内
                        .gte(DateUtil.parse(dto.getStartDate()).getTime())
                        .lte(DateUtil.parse(dto.getEndDate()).getTime()));
        Map<String, Long> map = workFlowOperationDao.groupByCountField("processOperationState.keyword", query);
        for (String s : map.keySet()) {
            result.put(keyMap.get(s), map.get(s));
        }
        return result;
    }

    /**
     * 获取已完成流程数据(按时间区间)
     *
     * @param dto
     * @return
     */
    @Override
    public List<Map<String, Long>> getProcessFinishCount(FlowOperationStatisticsDto dto) {
        List<Map<String, Long>> mapList = new ArrayList<>();
        String id = getId(dto.getCiCode());
        if (BinaryUtils.isEmpty(id)) {
            return mapList;
        }
        // 根据当前时间偏移至5年1月1日(目前是固定的后期可修改)
        dto.setStartDate(LocalDate.now().minusYears(5).withMonth(1).withDayOfMonth(1).toString());
        String format = "yyyy-MM-dd";
        DateHistogramInterval interval = DateHistogramInterval.DAY;
        switch (dto.getFrequencyStatistics()) {
            case "year":
                interval = DateHistogramInterval.YEAR;
                format = "yyyy";
                break;
            case "month":
                interval = DateHistogramInterval.MONTH;
                format = "yyyy-MM";
                break;
            case "week":
                interval = DateHistogramInterval.WEEK;
                break;
        }
        // 查询指定时间内的办结次数
        BoolQueryBuilder query = QueryBuilders.boolQuery()
                .must(QueryBuilders.termQuery("processTypeId.keyword", id))
                .must(QueryBuilders.rangeQuery("processStateTime")
                        .gte(DateUtil.parse(dto.getStartDate()).getTime())
                        .lte(DateUtil.parse(dto.getEndDate()).getTime()))
                .must(QueryBuilders.termQuery("processOperationState", "1"));

        mapList = workFlowOperationDao.getCountGroupByDate(query, interval, format);
        if (dto.getFrequencyStatistics().equals("week")) {
            List<Map<String, Long>> newMapList = new ArrayList<>(mapList.size());
            for (int i = 0; i < mapList.size(); i++) {
                Map<String, Long> map = new HashMap<>(1);
                String key = "第" + arabicToChinese(i + 1) + "周";
                mapList.get(i).forEach((k, v) -> {
                    map.put(key, v);
                });
                newMapList.add(map);
            }
            return newMapList;
        }
        return mapList;
    }

    /**
     * 获取活动运行时长top榜
     *
     * @param dto
     * @return
     */
    @Override
    public List<Map<String, String>> getActivityRuningDuration(FlowOperationStatisticsDto dto) {
        List<Map<String, String>> mapList = new ArrayList<>();
        String id = getId(dto.getCiCode());
        if (BinaryUtils.isEmpty(id)) {
            return mapList;
        }
        BoolQueryBuilder query = QueryBuilders.boolQuery()
                .must(QueryBuilders.termQuery("processTypeId.keyword", id))
                .must(QueryBuilders.rangeQuery("activityStateTime")// 筛选开始日期在指定范围内
                        .gte(DateUtil.parse(dto.getStartDate()).getTime())
                        .lte(DateUtil.parse(dto.getEndDate()).getTime()));
        mapList = activityOperationDao.getActivityRunningTimeTopList(query, 20);
        return getActivity(id, mapList);
    }

    /**
     * 根据映射关系修改活动名称
     *
     * @param id
     * @param mapList
     * @return
     */
    private List<Map<String, String>> getActivity(String id, List<Map<String, String>> mapList) {
        List<Map<String, String>> result = new ArrayList<>(mapList.size());
        String showType = bmConfigSvc.getConfigType(ACTIVITY_MAPPING);
        if (!BinaryUtils.isEmpty(showType) && !BinaryUtils.isEmpty(mapList)) {
            JSONArray array = JSON.parseArray(showType);
            for (Object o : array) {
                JSONObject obj = (JSONObject) o;
                String targetFlowId = obj.getString("targetFlowId");
                String targetActivity = obj.getString("targetActivity");
                String sourceActivity = obj.getString("sourceActivity");
                if (targetFlowId.equals(id)) {
                    for (Map<String, String> map : mapList) {
                        if (targetActivity.equals(map.get("activityName"))) {
                            map.put("activityName", sourceActivity);
                            result.add(map);
                        }
                    }
                }
            }
        }
        return result;
    }

    /**
     * 根据映射关系获取目标流程id
     *
     * @param ciCode
     * @return
     */
    private String getId(String ciCode) {
        String showType = bmConfigSvc.getConfigType(PROCESS_MAPPING);
        if (!BinaryUtils.isEmpty(showType)) {
            JSONArray array = JSON.parseArray(showType);
            for (Object o : array) {
                JSONObject obj = (JSONObject) o;
                String sourceId = obj.getString("sourceId");
                if (sourceId.equals(ciCode)) {
                    return obj.getString("targetId");
                }
            }
        }
        return "";
    }


    /**
     * 将阿拉伯数字转中文数字(100以内)
     *
     * @param num
     * @return
     */
    private String arabicToChinese(int num) {
        String[] chineseDigits = {"零", "一", "二", "三", "四", "五", "六", "七", "八", "九", "十"};
        if (num <= 10) {
            return chineseDigits[num];
        } else {
            int digit = num % 10;
            if (digit == 0) {
                return chineseDigits[num / 10] + chineseDigits[10];
            } else {
                int index = (num - digit) / 10;
                if (index <= 1) {
                    return chineseDigits[10] + chineseDigits[digit];
                } else {
                    return chineseDigits[index] + chineseDigits[10] + chineseDigits[digit];
                }
            }
        }
    }
}
