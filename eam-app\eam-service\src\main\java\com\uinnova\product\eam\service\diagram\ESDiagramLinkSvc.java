package com.uinnova.product.eam.service.diagram;
import com.uinnova.product.eam.base.diagram.model.ESDiagramLink;
import com.uinnova.product.eam.base.diagram.model.ESResponseStruct;

import java.util.Collection;
import java.util.List;

/**
 * 视图线接口
 * <AUTHOR>
 */
public interface ESDiagramLinkSvc {

    /**
     * 获取视图link节点
     * @param diagramIds 视图id集合
     * @param sheetIds sheetId集合（可空）
     * @return 视图link节点
     */
    List<ESDiagramLink> getLinkByDiagram(Collection<Long> diagramIds, Collection<String> sheetIds);

    /**
     * 获取视图link
     * @param diagramId 视图id
     * @param sheetIds 分页id
     * @param keys link标识
     * @return 关系线
     */
    List<ESDiagramLink> getDiagramLink(Long diagramId, Collection<String> sheetIds, Collection<String> keys);

    /**
     * 批量保存或更新线
     * @param links 线集合
     * @return 结果
     */
    Integer saveOrUpdateBatch(List<ESDiagramLink> links);

    /**
     * 批量通过视图id删除link
     * @param diagramIds 视图id
     */
    void deleteByDiagramIds(Collection<Long> diagramIds);

    /**
     * 新增或更新link, flag为true标识是更新操作
     * @param diagramId 视图id
     * @param sheetId sheet页id
     * @param opList 数据集
     * @param flag 是否是更新
     * @return 更新结果集
     */
    List<ESResponseStruct> saveOrUpdateLink(Long diagramId, String sheetId, String opList, Boolean flag);
}
