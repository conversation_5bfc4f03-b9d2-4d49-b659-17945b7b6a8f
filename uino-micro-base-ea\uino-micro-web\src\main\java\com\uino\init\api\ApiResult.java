package com.uino.init.api;

import java.util.Arrays;

import com.alibaba.fastjson.JSONObject;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @Title: WebApiResultConfig
 * @Description: WebApiResultConfig
 * @Author: YGQ
 * @Create: 2021-04-23 23:53
 **/

@Getter
@Setter
@ApiModel(value = "ApiResult", description = "接口返回数据格式")
@SuppressWarnings("all")
public class ApiResult<T> {
    /**
     * 成功或失败
     */
    private static final String SUCCESS = "Success";
    /**
     * 国际化定义码
     */
    private static final String MESSAGE_CODE = "MessageCode";
    /**
     * 国际化消息参数
     */
    private static final String MESSAGE_PARAM = "MessageParam";
    /**
     * 返回结果值
     */
    private static final String DATA = "Data";

    /**
     * 返回状态, true/false
     * */
    @ApiModelProperty(value = "返回状态", example = "true：成功，false：失败")
    private boolean success;

    /**
     * 状态码
     * */
    @ApiModelProperty(value = "国际化对应消息编号")
    private Integer code;

    /**
     * 消息参数
     * */
    @ApiModelProperty(value = "国际化对应消息参数")
    private String message = "";

    /**
     * 响应数据
     * */
    @ApiModelProperty(value = "返回的数据")
    private T data;


    /**
     * 请求通过正常返回值
     *
     * @param controller 当前controller
     * @return ApiResult
     */
    public static ApiResult ok(Object controller) {
        ApiResult result = new ApiResult();
        result.setSuccess(true);
        result.setCode(200);
        return result;
    }


    /**
     * 失败返回
     *
     * @param controller 当前controller
     * @return ApiResult
     */
    public static ApiResult error(Object controller) {
        ApiResult result = new ApiResult();
        result.setSuccess(false);
        result.setCode(500);
        return result;
    }

    public ApiResult data(T data) {
        this.setData(data);
        return this;
    }

    public ApiResult msgCode(Integer msgCode) {
        this.setCode(msgCode);
        return this;
    }

    public ApiResult msgParam(String msgParam) {
        this.setMessage(msgParam);
        return this;
    }

    public String toJsonString() {
        JSONObject jsonResult = new JSONObject();
        jsonResult.put(SUCCESS, this.success);
        jsonResult.put(DATA, this.data == null ? null : this.data);
        jsonResult.put(MESSAGE_CODE, this.code == null ? "" : this.code);
        jsonResult.put(MESSAGE_PARAM, this.message == null ? null : Arrays.asList(this.message));
//        jsonResult.put(VERSION, this.version == null ? "" : this.version);
        return jsonResult.toJSONString();
    }

    public static String getJsonResult(String version, Object data) {
        return getJsonResult(true, version, data, "");
    }

    public static String getJsonResult(boolean success, String version, String msgCode, String... msgParam) {
        return getJsonResult(success, version, null, msgCode, msgParam);
    }

    public static String getJsonResult(boolean success, String version, Object data, String msgCode, String... msgParam) {
        JSONObject jsonResult = new JSONObject();
        jsonResult.put(SUCCESS, success);
        jsonResult.put(DATA, data);
        jsonResult.put(MESSAGE_CODE, msgCode);
        jsonResult.put(MESSAGE_PARAM, Arrays.asList(msgParam));
//        jsonResult.put(VERSION, version);
        return jsonResult.toJSONString();
    }
}
