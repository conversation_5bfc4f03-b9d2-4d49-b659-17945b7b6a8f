package com.uino.bean.cmdb.base;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.util.Assert;

import com.binary.framework.bean.annotation.Comment;
import com.uino.bean.permission.business.IValidDto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 
 * <AUTHOR>
 *
 */
@Setter
@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value="条件详情类",description = "条件详情")
public class ESTagRuleItem implements Serializable, IValidDto {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value="ID",example = "123")
    @Comment("ID[ID]")
    private Long id;

    @ApiModelProperty(value="所属定义ID",example = "123")
    @Comment("所属定义ID[TAG_ID]")
    private Long tagId;

    @ApiModelProperty(value="所属规则ID",example = "123")
    @Comment("所属规则ID[RULE_ID]")
    private Long ruleId;

    @ApiModelProperty(value="规则分类id",example = "123")
    @Comment("规则分类[CLASS_ID]")
    private Long classId;

    @ApiModelProperty(value="规则类型",example = "1")
    @Comment("规则类型[RULE_TYPE]    1=属性 2=朋友圈")
    private Integer ruleType;

    @ApiModelProperty(value="关联分类id",example = "123")
    @Comment("关联分类[RLT_CLASS_ID]")
    private Long rltClassId;

    @ApiModelProperty(value="规则属性id",example = "123")
    @Comment("规则属性[CLASS_ATTR_ID]")
    private Long classAttrId;

    @ApiModelProperty(value="运算符",example = "1")
    @Comment("运算符[RULE_OP] 1=等于 2=不等于 3=小于 4=小于等于 5=大于 6=大于等于 7=like 8=not like 9=包含 10=不包含")
    private Integer ruleOp;

    @ApiModelProperty(value="条件值")
    @Comment("条件值[RULE_VAL]")
    private String ruleVal;

    @ApiModelProperty(value="非运算",example = "1")
    @Comment("非运算[IS_NOT]")
    private Integer isNot;

    @ApiModelProperty(value="排列顺序",example = "1")
    @Comment("排列顺序[ORDER_NO]")
    private Integer orderNo;

    @Override
    public void valid() {
        Assert.notNull(getClassAttrId(), "X_PARAM_NOT_NULL${name:classAttrId}");
        Assert.notNull(getRuleOp(), "X_PARAM_NOT_NULL${name:ruleOp}");
        Assert.notNull(getRuleVal(), "X_PARAM_NOT_NULL${name:ruleVal}");
        if (getRuleType() != null && getRuleType().intValue() != 1) {
            Assert.notNull(getRltClassId(), "X_PARAM_NOT_NULL${name:rltClassId}");
        }

    }

}
