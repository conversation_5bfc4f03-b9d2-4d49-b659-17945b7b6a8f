package com.uinnova.product.eam.service;

import com.binary.jdbc.Page;
import com.uinnova.product.eam.comm.model.es.EamCategory;
import com.uinnova.product.eam.model.InstitutionSystemDto;
import com.uinnova.product.eam.model.ObjectMovingDto;
import com.uinnova.product.eam.model.vo.InstitutionSystemTreeNewDto;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;

import java.util.List;

/**
 * description
 *
 * <AUTHOR>
 * @since 2024/6/15 9:56
 */
public interface InstitutionSystemService {

    List<InstitutionSystemTreeNewDto> getInstitutionSystemTreeNew(Long id);

    Long saveOrUpdateCategory(EamCategory vo);

    Page<CcCiInfo> getInstitutionListNew(InstitutionSystemDto institutionSystemDto);

    Integer delete(Long id);

    Integer moveCi(ObjectMovingDto objectMovingDto);

    List<InstitutionSystemTreeNewDto> getInstitutionSystemTreeWithCiNew(Long id);
}
