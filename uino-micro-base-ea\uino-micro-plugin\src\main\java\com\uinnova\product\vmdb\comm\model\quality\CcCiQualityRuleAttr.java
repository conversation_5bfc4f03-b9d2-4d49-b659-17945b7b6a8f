package com.uinnova.product.vmdb.comm.model.quality;

import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;

/**
 * 
 * <AUTHOR>
 *
 */
@Comment("CI数据质量规则属性表[CC_CI_QUALITY_RULE_ATTR]")
public class CcCiQualityRuleAttr implements EntityBean {
    private static final long serialVersionUID = 1L;

    @Comment("ID[ID]")
    private Long id;

    @Comment("所属规则[RULE_ID]")
    private Long ruleId;

    @Comment("规则类型[RULE_TYPE]    规则类型:10=完整性 11=合规性 12=准确性")
    private Integer ruleType;

    @Comment("规则子类型[RULE_SUB_TYPE]    规则子类型:1201=过期 1202=孤儿")
    private Integer ruleSubType;

    @Comment("所属分类[CLASS_ID]")
    private Long classId;

    @Comment("属性ID[ATTR_ID]")
    private Long attrId;

    @Comment("运算符[CDT_OP]")
    private Integer cdtOp;

    @Comment("条件值[CDT_VAL]")
    private String cdtVal;

    @Comment("所属域[DOMAIN_ID]")
    private Long domainId;

    @Comment("数据状态[DATA_STATUS]    数据状态:数据状态：1-正常 0-删除")
    private Integer dataStatus;

    @Comment("创建时间[CREATE_TIME]")
    private Long createTime;

    @Comment("修改时间[MODIFY_TIME]")
    private Long modifyTime;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getRuleId() {
        return this.ruleId;
    }

    public void setRuleId(Long ruleId) {
        this.ruleId = ruleId;
    }

    public Integer getRuleType() {
        return this.ruleType;
    }

    public void setRuleType(Integer ruleType) {
        this.ruleType = ruleType;
    }

    public Integer getRuleSubType() {
        return this.ruleSubType;
    }

    public void setRuleSubType(Integer ruleSubType) {
        this.ruleSubType = ruleSubType;
    }

    public Long getClassId() {
        return this.classId;
    }

    public void setClassId(Long classId) {
        this.classId = classId;
    }

    public Long getAttrId() {
        return this.attrId;
    }

    public void setAttrId(Long attrId) {
        this.attrId = attrId;
    }

    public Integer getCdtOp() {
        return this.cdtOp;
    }

    public void setCdtOp(Integer cdtOp) {
        this.cdtOp = cdtOp;
    }

    public String getCdtVal() {
        return this.cdtVal;
    }

    public void setCdtVal(String cdtVal) {
        this.cdtVal = cdtVal;
    }

    public Long getDomainId() {
        return this.domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }

    public Integer getDataStatus() {
        return this.dataStatus;
    }

    public void setDataStatus(Integer dataStatus) {
        this.dataStatus = dataStatus;
    }

    public Long getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getModifyTime() {
        return this.modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }

}
