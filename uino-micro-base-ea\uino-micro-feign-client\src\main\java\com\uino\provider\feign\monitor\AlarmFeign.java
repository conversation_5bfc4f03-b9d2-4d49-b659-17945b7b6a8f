package com.uino.provider.feign.monitor;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.binary.jdbc.Page;
import com.uino.bean.monitor.base.ESAlarm;
import com.uino.bean.monitor.buiness.AlarmInfo;
import com.uino.bean.monitor.buiness.AlarmQueryDto;
import com.uino.bean.monitor.buiness.SimulationAlarmBean;
import com.uino.provider.feign.config.BaseFeignConfig;

@Component
@FeignClient(name = "${feign-server-name:tarsier-platform}", path = "${feign-server-path:tarsier-platform}/feign/alarm", configuration = {
        BaseFeignConfig.class})
public interface AlarmFeign {

    /**
     * 分页查询告警信息
     * 
     * @param queryDto
     * @return
     */
    @PostMapping("searchAlarms")
    public Page<AlarmInfo> searchAlarms(@RequestBody(required = false) AlarmQueryDto queryDto);

    /**
     * 保存告警
     * 
     * @param saveDto
     */
    @PostMapping("saveAlarm")
    public void saveAlarm(@RequestBody(required = false) ESAlarm saveDto);

    /**
     * 根据条件count 告警
     * 
     * @param queryDto
     * @return
     */
    @PostMapping("countByQuery")
    public Long countByQuery(@RequestBody(required = false) AlarmQueryDto queryDto);

    @PostMapping("queryOpenAlarm")
    ESAlarm queryOpenAlarm(@RequestParam(required = false, value = "metric") String metric,
            @RequestParam(required = false, value = "classId") Long classId,
            @RequestParam(required = false, value = "objId") Long objId);

	/**
	 * 批量模拟告警信息
	 * 
	 * @param bean
	 */
	@PostMapping("simulationAlarms")
	void simulationAlarms(@RequestBody SimulationAlarmBean bean);
}
