package com.uino.user;

import static org.junit.Assert.assertEquals;

import java.io.File;
import java.io.FileInputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.uino.api.client.permission.IUserApiSvc;
import com.uino.bean.permission.base.SysModule;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.permission.business.ModuleNodeInfo;
import com.uino.bean.permission.query.CSysUser;
import com.uino.dao.permission.ESModuleSvc;
import com.uino.util.sys.SysUtil;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.binary.json.JSON;
import com.uino.StartBaseWebAppliaction;
import com.uino.bean.permission.base.SysUserRoleRlt;
import com.uino.bean.permission.business.SysUserPwdResetParam;
import com.uino.bean.permission.business.UserInfo;
import com.uino.bean.permission.business.request.GetUserAuthRequestDto;
import com.uino.bean.permission.query.SearchKeywordBean;

@RunWith(SpringRunner.class)
@WebAppConfiguration
@SpringBootTest(classes = {StartBaseWebAppliaction.class})
@ActiveProfiles("provider-local-dev")
@AutoConfigureMockMvc(addFilters = false)
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class UserMvcTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private IUserApiSvc userSvc;

    @Autowired
    private ESModuleSvc esModuleSvc;


    private String token = "ca14ed042fe7912426b484f6ea5c754b4fb51ae4a72037e9127da51743e0d1f9650e6e78ed4466ac970acb6a0b2f46fd26958c4ab2d115cc71b34ca8a02db24c";

    private static List<Long> opIds = new ArrayList<>();

    @Test
    public void atestSaveOrUpdate() throws Exception {
        UserInfo param = new UserInfo();
        param.setLoginCode("zmjUserTest");
        param.setUserName("zmjUserTest");
        param.setEmailAdress("<EMAIL>");
        String requestJson = JSONObject.toJSONString(param);
        ResultActions reaction =
            this.mockMvc.perform(MockMvcRequestBuilders.post("/permission/user/saveOrUpdate").contentType(MediaType.APPLICATION_JSON).content(requestJson).accept(MediaType.APPLICATION_JSON)// 返回值接收json
                .header("Content-Type", "application/json").header("token", token));
        MvcResult mvcResult = reaction.andReturn();
        String resStr = mvcResult.getResponse().getContentAsString();
        String userIdStr = JSON.toString(JSON.toObject(resStr, Map.class).get("data"));
        Long opId = JSON.toObject(userIdStr, Long.class);
        opIds.add(opId);
        int status = mvcResult.getResponse().getStatus();
        assertEquals(200, status);
    }

    @Test
    public void testToken() {
        SysUser userInfo = SysUtil.getCurrentUserInfo("8a6809fe-4a4d-460d-81d3-d3a6cb424e9eMSZhZG1pbiYx");
        System.out.println(userInfo);
    }

    @Test
    public void ctestUpdateCurUser() throws Exception {
        UserInfo param = new UserInfo();
        param.setId(opIds.get(0));
        param.setUserName("zmjUserTestUpdate");
        param.setEmailAdress("<EMAIL>");
        param.setMobileNo("13888888888");
        param.setNotes("测试修改个人信息");
        String requestJson = JSONObject.toJSONString(param);
        ResultActions reaction =
            this.mockMvc.perform(MockMvcRequestBuilders.multipart("/permission/user/updateCurUser").param("userInfo", requestJson).accept(MediaType.APPLICATION_JSON)// 返回值接收json
                .header("Content-Type", "application/json").header("token", token));
        MvcResult mvcResult = reaction.andReturn();
        int status = mvcResult.getResponse().getStatus();
        assertEquals(200, status);
    }

    @Test
    public void dtestResetUserPasswd() throws Exception {
        SysUserPwdResetParam param = new SysUserPwdResetParam();
        param.setUserId(opIds.get(0));
        param.setOldPasswd("yMQvN2GlHIWLATwU/N7a4Q==");
        param.setNewPasswd("yMQvN2GlHIWLATwU/N7a4Q==");
        String requestJson = JSONObject.toJSONString(param);
        ResultActions reaction =
            this.mockMvc.perform(MockMvcRequestBuilders.post("/permission/user/resetUserPasswd").contentType(MediaType.APPLICATION_JSON).content(requestJson).accept(MediaType.APPLICATION_JSON)// 返回值接收json
                .header("Content-Type", "application/json").header("token", token));
        MvcResult mvcResult = reaction.andReturn();
        int status = mvcResult.getResponse().getStatus();
        assertEquals(200, status);
    }

    @Test
    public void etestResetUserPasswdByAdmin() throws Exception {
        SysUserPwdResetParam param = new SysUserPwdResetParam();
        param.setUserId(opIds.get(0));
        param.setNewPasswd("yMQvN2GlHIWLATwU/N7a4Q==");
        String requestJson = JSONObject.toJSONString(param);
        ResultActions reaction =
            this.mockMvc.perform(MockMvcRequestBuilders.post("/permission/user/resetUserPasswdByAdmin").contentType(MediaType.APPLICATION_JSON).content(requestJson).accept(MediaType.APPLICATION_JSON)// 返回值接收json
                .header("Content-Type", "application/json").header("token", token));
        MvcResult mvcResult = reaction.andReturn();
        int status = mvcResult.getResponse().getStatus();
        assertEquals(200, status);
    }

    @Test
    public void ftestDownloadExcelTpl() throws Exception {
        ResultActions reaction = this.mockMvc.perform(MockMvcRequestBuilders.post("/permission/user/downloadUserExcelTpl").accept(MediaType.MULTIPART_FORM_DATA)// 返回值接收文件
            .header("Content-Type", "application/json").header("token", token));
        MvcResult mvcResult = reaction.andReturn();
        int status = mvcResult.getResponse().getStatus();
        assertEquals(200, status);
    }

    @Test
    public void gtestImportExcel() throws Exception {
        File file = new File("./src/test/resources/testdata/userTest.xlsx");
        System.out.println(file.getAbsolutePath());
        MockMultipartFile firstFile = new MockMultipartFile("file", "userTest.xlsx", MediaType.TEXT_PLAIN_VALUE, new FileInputStream(file));
        ResultActions reaction = mockMvc.perform(MockMvcRequestBuilders.multipart("/permission/user/importUserExcel").file(firstFile)).andExpect(MockMvcResultMatchers.status().isOk());
        String resStr = reaction.andReturn().getResponse().getContentAsString();
        String resultStr = JSONArray.toJSONString(JSON.toObject(resStr, Map.class).get("data"));
        int status = reaction.andReturn().getResponse().getStatus();
        assertEquals(200, status);
    }

    @Test
    public void htestExportExcel() throws Exception {
        ResultActions reaction = this.mockMvc.perform(MockMvcRequestBuilders.post("/permission/user/exportUserExcel").accept(MediaType.MULTIPART_FORM_DATA)// 返回值接收文件
            .header("Content-Type", "application/json").header("token", token));
        MvcResult mvcResult = reaction.andReturn();
        int status = mvcResult.getResponse().getStatus();
        assertEquals(200, status);
    }

    @Test
    public void itestQueryInfoPage() throws Exception {
        SearchKeywordBean param = new SearchKeywordBean();
        param.setPageNum(1);
        param.setPageSize(10);
        param.setKeyword("testUser");
        String requestJson = JSONObject.toJSONString(param);
        ResultActions reaction =
            this.mockMvc.perform(MockMvcRequestBuilders.post("/permission/user/queryInfoPage").contentType(MediaType.APPLICATION_JSON).content(requestJson).accept(MediaType.APPLICATION_JSON)// 返回值接收json
                .header("Content-Type", "application/json").header("token", token));
        MvcResult mvcResult = reaction.andReturn();
        String resStr = mvcResult.getResponse().getContentAsString();
       String pageStr = JSON.toString(JSON.toObject(resStr, Map.class).get("data"));
        String userStr = JSONObject.parseObject(pageStr).getString("data");
        List<UserInfo> userInfos = JSONArray.parseArray(userStr, UserInfo.class);
        List<Long> ids = userInfos.stream().map(UserInfo::getId).collect(Collectors.toList());
        opIds.addAll(ids);
        int status = mvcResult.getResponse().getStatus();
        assertEquals(200, status);
    }

    @Test
    public void jtestAddUserRoleRlt() throws Exception {
        List<SysUserRoleRlt> param = new ArrayList<>();
        SysUserRoleRlt userRoleRlt = new SysUserRoleRlt();
        userRoleRlt.setUserId(opIds.get(0));
        userRoleRlt.setRoleId(123L);
        param.add(userRoleRlt);
        String requestJson = JSONObject.toJSONString(param);
        ResultActions reaction =
            this.mockMvc.perform(MockMvcRequestBuilders.post("/permission/user/addUserRoleRlt").contentType(MediaType.APPLICATION_JSON).content(requestJson).accept(MediaType.APPLICATION_JSON)// 返回值接收json
                .header("Content-Type", "application/json").header("token", token));
        MvcResult mvcResult = reaction.andReturn();
        int status = mvcResult.getResponse().getStatus();
        assertEquals(200, status);
    }

    @Test
    public void ktestGetUserByRoleId() throws Exception {
        ResultActions reaction =
            this.mockMvc.perform(MockMvcRequestBuilders.post("/permission/user/getUserByRoleId").contentType(MediaType.APPLICATION_JSON).content("123").accept(MediaType.APPLICATION_JSON)// 返回值接收json
                .header("Content-Type", "application/json").header("token", token));
        MvcResult mvcResult = reaction.andReturn();
        int status = mvcResult.getResponse().getStatus();
        assertEquals(200, status);
    }

    // @Test
    public void ltestDeleteUserRoleRlt() throws Exception {
        List<SysUserRoleRlt> param = new ArrayList<>();
        SysUserRoleRlt userRoleRlt = new SysUserRoleRlt();
        userRoleRlt.setUserId(opIds.get(0));
        userRoleRlt.setRoleId(123L);
        param.add(userRoleRlt);
        String requestJson = JSONObject.toJSONString(param);
        ResultActions reaction =
            this.mockMvc.perform(MockMvcRequestBuilders.post("/permission/user/deleteUserRoleRlt").contentType(MediaType.APPLICATION_JSON).content(requestJson).accept(MediaType.APPLICATION_JSON)// 返回值接收json
                .header("Content-Type", "application/json").header("token", token));
        MvcResult mvcResult = reaction.andReturn();
        int status = mvcResult.getResponse().getStatus();
        assertEquals(200, status);
    }

    @Test
    public void mtestGetMenuTree() throws Exception {
        ResultActions reaction =
            this.mockMvc.perform(MockMvcRequestBuilders.post("/permission/user/getMenuTree").contentType(MediaType.APPLICATION_JSON).content("1").accept(MediaType.APPLICATION_JSON)// 返回值接收json
                .header("Content-Type", "application/json").header("token", token));
        MvcResult mvcResult = reaction.andReturn();
        int status = mvcResult.getResponse().getStatus();
        assertEquals(200, status);
    }

    @Test
    public void ntestGetAuth() throws Exception {
        GetUserAuthRequestDto param = new GetUserAuthRequestDto();
        param.setUserId(1L);
        String requestJson = JSONObject.toJSONString(param);
        ResultActions reaction =
            this.mockMvc.perform(MockMvcRequestBuilders.post("/permission/user/getAuth").contentType(MediaType.APPLICATION_JSON).content(requestJson).accept(MediaType.APPLICATION_JSON)// 返回值接收json
            .header("Content-Type", "application/json").header("token", token));
        MvcResult mvcResult = reaction.andReturn();
        int status = mvcResult.getResponse().getStatus();
        assertEquals(200, status);
    }

    @Test
    public void otestEnshrineMenu() throws Exception {
        String requestJson = JSONObject.toJSONString(123L);
        ResultActions reaction =
            this.mockMvc.perform(MockMvcRequestBuilders.post("/permission/user/enshrineMenu").contentType(MediaType.APPLICATION_JSON).content(requestJson).accept(MediaType.APPLICATION_JSON)// 返回值接收json
                .header("Content-Type", "application/json").header("token", token));
        MvcResult mvcResult = reaction.andReturn();
        int status = mvcResult.getResponse().getStatus();
        assertEquals(200, status);
    }

    @Test
    public void otestUnenshrineMenu() throws Exception {
        String requestJson = JSONObject.toJSONString(123L);
        ResultActions reaction =
            this.mockMvc.perform(MockMvcRequestBuilders.post("/permission/user/unenshrineMenu").contentType(MediaType.APPLICATION_JSON).content(requestJson).accept(MediaType.APPLICATION_JSON)// 返回值接收json
                .header("Content-Type", "application/json").header("token", token));
        MvcResult mvcResult = reaction.andReturn();
        int status = mvcResult.getResponse().getStatus();
        assertEquals(200, status);
    }

    @Test
    public void ztestRemoveUserById() throws Exception {
        for (Long opId : opIds) {
            String requestJson = JSONObject.toJSONString(opId);
            ResultActions reaction =
                this.mockMvc.perform(MockMvcRequestBuilders.post("/permission/user/removeUserById").contentType(MediaType.APPLICATION_JSON).content(requestJson).accept(MediaType.APPLICATION_JSON)// 返回值接收json
                    .header("Content-Type", "application/json").header("token", token));
            MvcResult mvcResult = reaction.andReturn();
            int status = mvcResult.getResponse().getStatus();
            assertEquals(200, status);
        }
    }
}
