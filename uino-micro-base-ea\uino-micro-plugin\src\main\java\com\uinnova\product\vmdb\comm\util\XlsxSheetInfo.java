package com.uinnova.product.vmdb.comm.util;

import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 可持续写入.XLSX格式Excel的Sheet页内容辅助类
 * 
 * <AUTHOR>
 *
 */
public class XlsxSheetInfo {

    private SXSSFWorkbook swb;

    /**
     * sheet名字
     */
    private String sheetName;

    /**
     * 标题行列值集合
     */
    private List<String> titleCellValues;

    /**
     * 主键列值
     */
    private String majorCellValue;

    /**
     * 业务主键列值
     */
    private Set<String> pkbCellValues;

    /**
     * sheet对象
     */
    private Sheet sheet;

    /**
     * 记录当前sheet写入的行数,也是开始行数,默认为1
     */
    private int writeRows = 1;

    public SXSSFWorkbook getSwb() {
        return swb;
    }

    public void setSwb(SXSSFWorkbook swb) {
        this.swb = swb;
    }

    public String getSheetName() {
        return sheetName;
    }

    public void setSheetName(String sheetName) {
        this.sheetName = sheetName;
    }

    public List<String> getTitleCellValues() {
        return titleCellValues;
    }

    public void setTitleCellValues(List<String> titleCellValues) {
        this.titleCellValues = titleCellValues;
    }

    public String getMajorCellValue() {
        return majorCellValue;
    }

    public void setMajorCellValue(String majorCellValue) {
        this.majorCellValue = majorCellValue;
    }

    public Set<String> getPkbCellValues() {
        return pkbCellValues;
    }

    public void setPkbCellValues(Set<String> pkbCellValues) {
        this.pkbCellValues = pkbCellValues;
    }

    public Sheet getSheet() {
        return sheet;
    }

    public void setSheet(Sheet sheet) {
        this.sheet = sheet;
    }

    public int getWriteRows() {
        return writeRows;
    }

    public void setWriteRows(int writeRows) {
        this.writeRows = writeRows;
    }

    /**
     * 创建sheet页
     * 
     * @param swb
     * @param sheetName
     *            sheet名称
     * @param titleCellValues
     *            标题列内容(用于区分样式)
     * @param majorCellValue
     *            主键列值(用于区分样式)
     */
    public XlsxSheetInfo(SXSSFWorkbook swb, String sheetName, List<String> titleCellValues, String majorCellValue) {
        this.swb = swb;
        this.sheetName = sheetName;
        this.titleCellValues = titleCellValues;
        this.majorCellValue = majorCellValue;

        // 创建Sheet并且设置标题行
        Sheet sheet = ExportXlsxExcelUtil.createExcelSheetAndTitle(this.swb, this.sheetName, this.titleCellValues, this.majorCellValue);
        this.sheet = sheet;
        this.writeRows = 1;
    }

    /**
     * 创建sheet页
     * 
     * @param swb
     * @param sheetName
     *            sheet名称
     * @param titleCellValues
     *            标题列内容
     * @param majorCellValue
     *            主键列值(用于区分样式)
     * @param pkbCellValues
     *            业务主键列值(用于区分样式)
     */
    public XlsxSheetInfo(SXSSFWorkbook swb, String sheetName, List<String> titleCellValues, String majorCellValue, Set<String> pkbCellValues) {
        this.swb = swb;
        this.sheetName = sheetName;
        this.titleCellValues = titleCellValues;
        this.majorCellValue = majorCellValue;
        this.pkbCellValues = pkbCellValues;

        // 创建Sheet并且设置标题行
        Sheet sheet = ExportXlsxExcelUtil.createExcelSheetAndTitle(this.swb, this.sheetName, this.titleCellValues, this.majorCellValue, this.pkbCellValues);
        this.sheet = sheet;
        this.writeRows = 1;
    }

    /**
     * 写入正文内容并统计写入行(海量数据建议循环写入,不建议放在整个List中在写入)
     * 
     * @param commentValueMaps
     *            正文值Map集合
     */
    public void writeExcelComment(List<Map<String, String>> commentValueMaps) {
        if (commentValueMaps != null && commentValueMaps.size() > 0) {
            Integer writeTotalRows = ExportXlsxExcelUtil.writeExcelComment(this.swb, this.sheet, this.writeRows, this.titleCellValues, this.majorCellValue, this.pkbCellValues, commentValueMaps);
            this.writeRows = writeTotalRows;
        }
    }

    /**
     * 有序写入正文列内容并统计写入行(海量数据建议循环写入,不建议放在整个List中在写入) </br>
     * 注:供新版导出有业务主键CI使用
     * 
     * @param commentValues
     *            正文List集合:每一项为字符串数组,长度必须与总列数一致,否则忽略
     */
    public void writeExcelArrayComment(List<String[]> commentValues) {
        if (commentValues != null && commentValues.size() > 0) {
            Integer writeTotalRows = ExportXlsxExcelUtil.writeExcelArrayComment(this.swb, this.sheet, this.writeRows, this.titleCellValues, this.majorCellValue, this.pkbCellValues, commentValues);
            this.writeRows = writeTotalRows;
        }
    }
}
