package com.uino.web.sys.mvc;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import com.uino.init.api.ApiResult;
import com.uino.init.api.ApiVersion;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import com.binary.core.lang.Conver;
import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uinnova.product.vmdb.comm.doc.annotation.MvcDesc;
import com.uinnova.product.vmdb.provider.sys.bean.SysLoginLdapConfigInfo;
import com.uino.bean.sys.base.LdapUserMapping;
import com.uino.bean.sys.base.LoginAuthConfig;
import com.uino.bean.sys.business.ActiveLoginAuthConfigDto;
import com.uino.bean.sys.business.LoginAuthConfigDto;
import com.uino.api.client.sys.ILoginAuthConfigApiSvc;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@ApiVersion(1)
@Api(value = "登录集成配置服务", tags = {"登录集成配置服务"})
@RestController
@RequestMapping("/sys/loginAuthConfig")
@MvcDesc(author = "xiongjian", desc = "登录集成配置服务")
public class SysLoginAuthConfigMvc {

    @Autowired
    private ILoginAuthConfigApiSvc apiSvc;

    @ApiOperation("根据protoName查询所有集成方式")
    @ModDesc(desc = "根据protoName查询所有集成方式", pDesc = "集成方式code", pType = String.class, rDesc = "查询结果", rType = Page.class,
        rcType = LoginAuthConfig.class)
    @RequestMapping(value="/querPageInfoByName",method= RequestMethod.POST)
    public ApiResult<Page<LoginAuthConfig>> querPageInfoByName(HttpServletRequest request, HttpServletResponse response,
                                        @RequestBody LoginAuthConfigDto authConfigCdt) {
        Page<LoginAuthConfig> queryPage = apiSvc.queryPage(authConfigCdt);
        return ApiResult.ok(this).data(queryPage);
    }

    @ApiOperation("根据id查询集成方式详细信息")
    @ModDesc(desc = "根据id查询集成方式详细信息", pDesc = "集成方式id", pType = String.class, rDesc = "查询结果",
        rType = LoginAuthConfig.class)
    @RequestMapping(value="/queryInfoById",method=RequestMethod.POST)
    public ApiResult<LoginAuthConfig> queryInfoById(HttpServletRequest request, HttpServletResponse response, @RequestBody String id) {
        LoginAuthConfig config = apiSvc.queryById(Conver.to(id, Long.class));
        return ApiResult.ok(this).data(config);
    }

    @ApiOperation("保存或更新集成配置")
    @ModDesc(desc = "保存或更新集成配置", pDesc = "配置信息", pType = LoginAuthConfig.class, rDesc = "id", rType = Long.class)
    @RequestMapping(value="/saveOrUpdateConfig",method=RequestMethod.POST)
    public ApiResult<Long> saveOrUpdateConfig(HttpServletRequest request, HttpServletResponse response,
        @RequestBody LoginAuthConfig authConfig) {
        Long id = apiSvc.saveOrUpdate(authConfig);
        return  ApiResult.ok(this).data(id);
    }

    @ApiOperation("激活集成配置")
    @ModDesc(desc = "激活集成配置", pDesc = "配置信息", pType = ActiveLoginAuthConfigDto.class, rDesc = "id",
        rType = Boolean.class)
    @RequestMapping(value="/activeConfig",method = RequestMethod.POST)
    public ApiResult<Boolean> activeConfig(HttpServletRequest request, HttpServletResponse response,
        @RequestBody ActiveLoginAuthConfigDto activeAuthConfigDto) {
        apiSvc.activeConfig(activeAuthConfigDto);
        return ApiResult.ok(this).data(true);
    }

    @ApiOperation("测试LDAP连接")
    @ModDesc(desc = "测试LDAP连接", pDesc = "配置信息", rDesc = "测试结果",
        rType = Boolean.class)
    @RequestMapping(value="/testAuthConnection",method = RequestMethod.POST)
    public ApiResult<Boolean> testAuthConnection(HttpServletRequest request, HttpServletResponse response,
        @RequestBody LoginAuthConfig authConfig) {
        boolean result = apiSvc.testConnection(authConfig);
        return ApiResult.ok(this).data(result);
    }

    @ApiOperation("测试LDAP连接")
    @ModDesc(desc = "测试LDAP连接", pDesc = "配置信息",  rDesc = "测试结果",
        rType = LdapUserMapping.class)
    @RequestMapping(value="/testConnectionAndMappingUser",method = RequestMethod.POST)
    public ApiResult<LdapUserMapping> testConnectionAndMappingUser(HttpServletRequest request, HttpServletResponse response,
        @RequestBody LoginAuthConfig authConfig) {
        LdapUserMapping result = apiSvc.testConnectionAndMappingUser(authConfig);
        return ApiResult.ok(this).data(result);
    }
    
    
    @ApiOperation("删除LDAP配置信息")
    @ModDesc(desc = "删除LDAP配置信息", pDesc = "删除条件", pType = Long.class, rDesc = "删除结果1成功0失败", rType = Long.class)
    @RequestMapping(value="/removeInfoById",method = RequestMethod.POST)
    public ApiResult<Boolean> removeInfoById(HttpServletRequest request, HttpServletResponse response, @RequestBody String id) {
        apiSvc.removeById(Conver.to(id, Long.class));
        return ApiResult.ok(this).data(true);
    }

}
