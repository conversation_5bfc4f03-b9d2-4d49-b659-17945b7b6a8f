package com.uino.api.client.sys.local;

import com.uino.dao.BaseConst;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.uino.service.sys.microservice.ITableDataConfigSvc;
import com.uino.bean.cmdb.base.ESTableDataConfigInfo;
import com.uino.api.client.sys.ITableDataConfigApiSvc;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
public class TableDataConfigApiSvcLocal implements ITableDataConfigApiSvc {

    @Autowired
    ITableDataConfigSvc configSvc;

    @Override
    public ESTableDataConfigInfo getCIDataConfigInfo(String uid) {
        return configSvc.getCIDataConfigInfo(BaseConst.DEFAULT_DOMAIN_ID,uid);
    }

    @Override
    public ESTableDataConfigInfo getCIDataConfigInfo(Long domainId, String uid) {
        return configSvc.getCIDataConfigInfo(domainId,uid);
    }

    @Override
    public Long saveCIDataConfigInfo(ESTableDataConfigInfo configInfo) {
        return configSvc.saveCIDataConfigInfo(configInfo);
    }

}
