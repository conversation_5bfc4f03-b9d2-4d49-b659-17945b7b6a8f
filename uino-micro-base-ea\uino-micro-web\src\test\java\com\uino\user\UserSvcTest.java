package com.uino.user;

import static org.junit.Assert.assertEquals;

import java.io.File;
import java.io.FileInputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import jakarta.servlet.http.Cookie;

import com.uino.dao.BaseConst;
import org.apache.commons.lang3.RandomStringUtils;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.binary.jdbc.Page;
import com.uino.dao.cmdb.ESCmdbCommSvc;
import com.uino.dao.permission.ESModuleSvc;
import com.uino.dao.permission.ESRoleSvc;
import com.uino.dao.permission.ESUserSvc;
import com.uino.service.permission.microservice.impl.OrgSvc;
import com.uino.service.permission.microservice.impl.UserSvc;
import com.uino.dao.permission.rlt.ESPerssionCommSvc;
import com.uino.dao.permission.rlt.ESUserModuleEnshrineRltSvc;
import com.uino.service.sys.microservice.ILogSvc;
import com.uino.service.sys.microservice.IResourceSvc;
import com.uino.bean.permission.base.SysOrg;
import com.uino.bean.permission.base.SysRole;
import com.uino.bean.permission.base.SysRoleDataModuleRlt;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.permission.base.SysUserModuleEnshrineRlt;
import com.uino.bean.permission.base.SysUserOrgRlt;
import com.uino.bean.permission.base.SysUserRoleRlt;
import com.uino.bean.permission.business.ModuleNodeInfo;
import com.uino.bean.permission.business.OrgNodeInfo;
import com.uino.bean.permission.business.SysUserPwdResetParam;
import com.uino.bean.permission.business.UserInfo;
import com.uino.bean.permission.business.request.SaveUserRoleRltRequestDto;
import com.uino.bean.permission.query.CSysOrg;
import com.uino.bean.permission.query.CSysRole;
import com.uino.bean.permission.query.CSysUser;
import com.uino.bean.permission.query.UserSearchBeanExtend;

@RunWith(SpringJUnit4ClassRunner.class)
public class UserSvcTest {

    @InjectMocks
    private UserSvc userSvc;

    private ESUserSvc esUserSvc;

    private ESPerssionCommSvc perssionCommSvc;

    private OrgSvc orgSvc;

    private ESRoleSvc roleSvc;

    private ESModuleSvc esModuleSvc;

    private ESUserModuleEnshrineRltSvc enshrineSvc;

    private ESCmdbCommSvc commSvc;

    private IResourceSvc resourceSvc;

    private ILogSvc logSvc;

    @BeforeClass
    public static void setUpBeforeClass() throws Exception {
        MockHttpServletRequest request = new MockHttpServletRequest();
        Cookie cookie = new Cookie("token", "ca14ed042fe7912426b484f6ea5c754b4fb51ae4a72037e9127da51743e0d1f9650e6e78ed4466ac970acb6a0b2f46fd26958c4ab2d115cc71b34ca8a02db24c");
        request.setCookies(cookie);
        ServletRequestAttributes attributes = new ServletRequestAttributes(request);
        RequestContextHolder.setRequestAttributes(attributes);
    }

    @Before
    public void setUp() {
        esUserSvc = Mockito.mock(ESUserSvc.class);
        ReflectionTestUtils.setField(userSvc, "userSvc", esUserSvc);

        perssionCommSvc = Mockito.mock(ESPerssionCommSvc.class);
        ReflectionTestUtils.setField(userSvc, "perssionCommSvc", perssionCommSvc);

        orgSvc = Mockito.mock(OrgSvc.class);
        ReflectionTestUtils.setField(userSvc, "orgSvc", orgSvc);

        roleSvc = Mockito.mock(ESRoleSvc.class);
        ReflectionTestUtils.setField(userSvc, "roleSvc", roleSvc);

        esModuleSvc = Mockito.mock(ESModuleSvc.class);
        ReflectionTestUtils.setField(userSvc, "esModuleSvc", esModuleSvc);

        enshrineSvc = Mockito.mock(ESUserModuleEnshrineRltSvc.class);
        ReflectionTestUtils.setField(userSvc, "enshrineSvc", enshrineSvc);

        commSvc = Mockito.mock(ESCmdbCommSvc.class);
        ReflectionTestUtils.setField(userSvc, "commSvc", commSvc);

        resourceSvc = Mockito.mock(IResourceSvc.class);
        ReflectionTestUtils.setField(userSvc, "resourceSvc", resourceSvc);

        logSvc = Mockito.mock(ILogSvc.class);
        ReflectionTestUtils.setField(userSvc, "logSvc", logSvc);

        ReflectionTestUtils.setField(userSvc, "urlPath", "urlPath");
        ReflectionTestUtils.setField(userSvc, "localPath", "localPath");

    }

    @Test
    public void testSaveOrUpdate() {
        Mockito.when(esUserSvc.getListByCdt(Mockito.any(CSysUser.class))).thenReturn(null);
        Mockito.when(esUserSvc.saveOrUpdate(Mockito.any(SysUser.class))).thenReturn(123L);
        Mockito.when(perssionCommSvc.deleteUserRoleRltByQuery(Mockito.any(), Mockito.anyBoolean())).thenReturn(123);
        Mockito.when(perssionCommSvc.deleteUserOrgRltByQuery(Mockito.any(), Mockito.anyBoolean())).thenReturn(123);
        Mockito.when(perssionCommSvc.saveUserRoleRltBatch(Mockito.anyList())).thenReturn(123);
        Mockito.when(perssionCommSvc.saveUseOrgRltBatch(Mockito.anyList())).thenReturn(123);

        Set<SysRole> roles = new HashSet<>();
        SysRole role = new SysRole();
        role.setId(1L);
        roles.add(role);
        Set<SysOrg> orgs = new HashSet<>();
        SysOrg org = new SysOrg();
        org.setId(1L);
        orgs.add(org);
        UserInfo userInfo = new UserInfo();
        userInfo.setId(123L);
        userInfo.setLoginCode("zmjUserTest");
        userInfo.setUserName("zmjUserTest");
        userInfo.setEmailAdress("<EMAIL>");
        userInfo.setRoles(roles);
        userInfo.setOrgs(orgs);

        // 常规测试
        Long id = userSvc.saveOrUpdate(userInfo);
        assertEquals(123L, id.longValue());

        // 用户信息校验失败
        userInfo.setEmailAdress("123");
        try {
            userSvc.saveOrUpdate(userInfo);
        } catch (Exception e) {
            // assertEquals("BS_USER_EMAIL_FORMAT_ERR", e.getMessage());
        }

        // 登录名重复
        List<SysUser> users = new ArrayList<>();
        SysUser user = new SysUser();
        user.setId(456L);
        user.setLoginCode("zmjUserTest");
        users.add(user);
        Mockito.when(esUserSvc.getListByCdt(Mockito.any(CSysUser.class))).thenReturn(users);
        try {
            userInfo.setEmailAdress("<EMAIL>");
            userSvc.saveOrUpdate(userInfo);
        } catch (Exception e) {
            assertEquals("BS_USER_LOGIN_CODE_EXIST", e.getMessage());
        }
    }

    @Test
    public void testSaveOrUpdateUserInfoBatch() {
        List<SysUser> users = new ArrayList<>();
        SysUser user = new SysUser();
        user.setId(123L);
        user.setLoginCode("user1");
        users.add(user);
        Mockito.when(esUserSvc.getListByQuery(Mockito.any())).thenReturn(users);
        Map<String, Object> map = new HashMap<>();
        map.put("failCount", 1);
        Mockito.when(esUserSvc.saveOrUpdateBatchMessage(Mockito.any(), Mockito.anyBoolean())).thenReturn(map);
        
        Mockito.when(perssionCommSvc.deleteUserRoleRltByQuery(Mockito.any(), Mockito.anyBoolean())).thenReturn(1);
        Mockito.when(perssionCommSvc.saveUserRoleRltBatch(Mockito.anyList())).thenReturn(1);
        List<SysRole> roles = new ArrayList<>();
        SysRole role = new SysRole();
        role.setId(1L);
        role.setRoleName("测试1");
        roles.add(role);
        Mockito.when(roleSvc.getListByCdt(Mockito.any(CSysRole.class))).thenReturn(roles);

        Mockito.when(perssionCommSvc.deleteUserOrgRltByQuery(Mockito.any(), Mockito.anyBoolean())).thenReturn(1);
        Mockito.when(perssionCommSvc.saveUserRoleRltBatch(Mockito.anyList())).thenReturn(1);
        List<SysOrg> orgs = new ArrayList<>();
        SysOrg org = new SysOrg();
        org.setOrgName("测试1");
        orgs.add(org);
        Mockito.when(orgSvc.queryListByCdt(Mockito.any())).thenReturn(orgs);

        Set<SysRole> uRoles = new HashSet<>();
        SysRole uRole = new SysRole();
        uRole.setId(1L);
        uRole.setRoleName("测试1");
        uRoles.add(uRole);
        Set<SysOrg> uOrgs = new HashSet<>();
        SysOrg uOrg = new SysOrg();
        uOrg.setId(1L);
        uOrg.setOrgName("测试1");
        uOrgs.add(uOrg);
        List<UserInfo> userInfos = new ArrayList<>();
        UserInfo userInfo1 = new UserInfo();
        userInfo1.setLoginCode("loginCode1");
        userInfo1.setUserName("userName1");
        userInfo1.setEmailAdress("<EMAIL>");
        userInfo1.setRoles(uRoles);
        userInfo1.setOrgs(uOrgs);
        UserInfo userInfo2 = new UserInfo();
        userInfo2.setLoginCode("loginCode1");
        userInfo2.setUserName("userName1");
        userInfo2.setEmailAdress("<EMAIL>");
        UserInfo userInfo3 = new UserInfo();
        userInfo3.setLoginCode("admin");
        userInfo3.setUserName("admin");
        userInfo3.setEmailAdress("<EMAIL>");
        UserInfo userInfo4 = new UserInfo();
        userInfo4.setLoginCode("test");
        userInfo4.setUserName("test");
        userInfo4.setMobileNo("mobileNo");
        UserInfo userInfo5 = new UserInfo();
        userInfo5.setLoginCode("user1");
        userInfo5.setUserName("user1");
        userInfo5.setEmailAdress("<EMAIL>");
        UserInfo userInfo6 = new UserInfo();
        userInfo6.setLoginCode("user2");
        userInfo6.setUserName("user2");
        userInfo6.setEmailAdress("<EMAIL>");
        userInfos.add(userInfo1);
        userInfos.add(userInfo2);
        userInfos.add(userInfo3);
        userInfos.add(userInfo4);
        userInfos.add(userInfo5);
        userInfos.add(userInfo6);

        userSvc.saveOrUpdateUserInfoBatch(BaseConst.DEFAULT_DOMAIN_ID, false, userInfos);

        // 保存数据为空
        userSvc.saveOrUpdateUserInfoBatch(BaseConst.DEFAULT_DOMAIN_ID, false,null);

    }

    @Test
    public void testSyncUserBatch() {
        List<SysUser> users = new ArrayList<>();
        SysUser user = new SysUser();
        user.setId(123L);
        user.setLoginCode("user1");
        users.add(user);
        Mockito.when(esUserSvc.getListByCdt(Mockito.any(CSysUser.class))).thenReturn(users);
        Map<String, Object> map = new HashMap<>();
        map.put("failCount", 1);
        Mockito.when(esUserSvc.saveOrUpdateBatchMessage(Mockito.any(), Mockito.anyBoolean())).thenReturn(map);
        Mockito.when(perssionCommSvc.saveUserRoleRltBatch(Mockito.anyList())).thenReturn(1);

        Set<SysRole> roles = new HashSet<>();
        SysRole role = new SysRole();
        role.setId(1L);
        roles.add(role);
        List<UserInfo> userInfos = new ArrayList<>();
        UserInfo userInfo1 = new UserInfo();
        userInfo1.setLoginCode("loginCode1");
        userInfo1.setUserName("userName1");
        userInfo1.setEmailAdress("<EMAIL>");
        userInfo1.setRoles(roles);
        UserInfo userInfo2 = new UserInfo();
        userInfo2.setLoginCode("loginCode1");
        userInfo2.setUserName("userName1");
        userInfo2.setEmailAdress("<EMAIL>");
        UserInfo userInfo3 = new UserInfo();
        userInfo3.setLoginCode("admin");
        userInfo3.setUserName("admin");
        userInfo3.setEmailAdress("<EMAIL>");
        UserInfo userInfo4 = new UserInfo();
        userInfo4.setLoginCode("test");
        userInfo4.setUserName("test");
        userInfo4.setMobileNo("mobileNo");
        UserInfo userInfo5 = new UserInfo();
        userInfo5.setLoginCode("user1");
        userInfo5.setUserName("user1");
        userInfo5.setEmailAdress("<EMAIL>");
        UserInfo userInfo6 = new UserInfo();
        userInfo6.setLoginCode("user2");
        userInfo6.setUserName("user2");
        userInfo6.setEmailAdress("<EMAIL>");
        userInfos.add(userInfo1);
        userInfos.add(userInfo2);
        userInfos.add(userInfo3);
        userInfos.add(userInfo4);
        userInfos.add(userInfo5);
        userInfos.add(userInfo6);

        userSvc.syncUserBatch(BaseConst.DEFAULT_DOMAIN_ID,userInfos);

        // 保存数据为空
        userSvc.syncUserBatch(BaseConst.DEFAULT_DOMAIN_ID,null);

    }

    @Test
    public void testGetUserInfoById() {
        SysUser user = new SysUser();
        user.setId(123L);
        user.setIcon("/122/icon.jpg");
        Mockito.when(esUserSvc.getById(Mockito.anyLong())).thenReturn(user);
        
        UserInfo info = new UserInfo();
        info.setId(123L);
        Mockito.when(esUserSvc.fillInfo(Mockito.anyList(), Mockito.anyBoolean())).thenReturn(Collections.singletonList(info));
        // 常规测试
        UserInfo userInfo = userSvc.getUserInfoById(123L);
        assertEquals(123L, userInfo.getId().longValue());

        // 用户不存在
        Mockito.when(esUserSvc.getById(Mockito.anyLong())).thenReturn(null);
        try {
            userSvc.getUserInfoById(123L);
        } catch (Exception e) {
            assertEquals("BS_USER_NOT_EXIST${loginCode:123}", e.getMessage());
        }
    }

    @Test
    public void testGetSysUserByCdt() {
        List<SysUser> users = new ArrayList<>();
        SysUser user = new SysUser();
        user.setId(456L);
        user.setLoginCode("loginCode1");
        users.add(user);
        Mockito.when(esUserSvc.getListByCdt(Mockito.any(CSysUser.class))).thenReturn(users);

        List<SysUser> list = userSvc.getSysUserByCdt(null);
        assertEquals("loginCode1", list.get(0).getLoginCode());
    }

    @Test
    public void testGetUserInfoByCdt() {
        List<UserInfo> users = new ArrayList<>();
        UserInfo user = new UserInfo();
        user.setId(456L);
        user.setLoginCode("loginCode1");
        users.add(user);
        Mockito.when(esUserSvc.getUserInfoListByQuery(Mockito.any(), Mockito.anyBoolean())).thenReturn(users);

        List<UserInfo> list = userSvc.getUserInfoByCdt(null, false);
        assertEquals("loginCode1", list.get(0).getLoginCode());
    }

    @Test
    public void testGetListByQuery() {
        List<SysUser> users = new ArrayList<>();
        SysUser user = new SysUser();
        user.setId(123L);
        users.add(user);
        Page<SysUser> page = new Page<>(1, 10, 1, 1, users);
        Mockito.when(esUserSvc.getSortListByQuery(Mockito.anyInt(), Mockito.anyInt(), Mockito.any(), Mockito.anyList())).thenReturn(page);

        UserInfo userInfo = new UserInfo();
        userInfo.setId(123L);
        userInfo.setLoginCode("zmjUserTest");
        userInfo.setUserName("zmjUserTest");
        userInfo.setEmailAdress("<EMAIL>");
        Mockito.when(esUserSvc.fillInfo(Mockito.anyList(), Mockito.anyBoolean())).thenReturn(Collections.singletonList(userInfo));

        List<SysUserRoleRlt> uRoles = new ArrayList<>();
        SysUserRoleRlt uRoleRlt = new SysUserRoleRlt();
        uRoleRlt.setUserId(123L);
        uRoleRlt.setRoleId(456L);
        uRoles.add(uRoleRlt);
        Mockito.when(perssionCommSvc.getUserRoleRltByRoleIds(Mockito.any())).thenReturn(uRoles);

        List<SysRole> roles = new ArrayList<>();
        SysRole role = new SysRole();
        role.setId(456L);
        roles.add(role);
        Mockito.when(roleSvc.getListByCdt(Mockito.any(CSysRole.class))).thenReturn(roles);

        userSvc.getListByQuery(null);

        // 条件包含角色id
        UserSearchBeanExtend bean = new UserSearchBeanExtend();
        bean.setRoleId(123L);
        bean.setKeyword("key");
        Mockito.when(perssionCommSvc.getUserRoleRltByRoleIds(Mockito.any())).thenReturn(new ArrayList<>());
        userSvc.getListByQuery(bean);
        Mockito.when(perssionCommSvc.getUserRoleRltByRoleIds(Mockito.any())).thenReturn(uRoles);
        userSvc.getListByQuery(bean);

        // 条件包含组织id
        bean.setOrgId(456L);
        Mockito.when(esUserSvc.getSortListByQuery(Mockito.anyInt(), Mockito.anyInt(), Mockito.any(), Mockito.any(QueryBuilder.class))).thenReturn(page);
        List<SysOrg> orgs = new ArrayList<>();
        SysOrg org = new SysOrg();
        org.setId(456L);
        orgs.add(org);
        Mockito.when(orgSvc.queryListByCdt(Mockito.any(CSysOrg.class))).thenReturn(orgs).thenReturn(null);
        Mockito.when(perssionCommSvc.getUserOrgRltByOrgIds(Mockito.any())).thenReturn(null);
        userSvc.getListByQuery(bean);

        List<SysUserOrgRlt> uOrgs = new ArrayList<>();
        SysUserOrgRlt uOrgRlt = new SysUserOrgRlt();
        uOrgRlt.setUserId(123L);
        uOrgRlt.setOrgId(456L);
        uOrgs.add(uOrgRlt);
        Mockito.when(perssionCommSvc.getUserOrgRltByOrgIds(Mockito.any())).thenReturn(uOrgs);
        userSvc.getListByQuery(bean);

        // 获取根组织下的用户
        bean.setOrgId(0L);
        userSvc.getListByQuery(bean);

        // 搜索长度超过200
        bean.setKeyword(RandomStringUtils.randomAlphanumeric(201));
        try {
            userSvc.getListByQuery(bean);
        } catch (Exception e) {
            assertEquals("BS_SEARCH_LENGTH_ERR", e.getMessage());
        }
    }

    @Test
    public void testGetUserByRoleId() {
        List<SysUserRoleRlt> uRoles = new ArrayList<>();
        SysUserRoleRlt uRoleRlt = new SysUserRoleRlt();
        uRoleRlt.setUserId(123L);
        uRoleRlt.setRoleId(456L);
        uRoles.add(uRoleRlt);
        Mockito.when(perssionCommSvc.getUserRoleRltByRoleIds(Mockito.any())).thenReturn(null);
        List<SysUser> users = new ArrayList<>();
        SysUser user = new SysUser();
        user.setLoginCode("loginCode1");
        user.setUserName("userName1");
        user.setEmailAdress("<EMAIL>");
        users.add(user);
        Mockito.when(esUserSvc.getListByCdt(Mockito.any(CSysUser.class))).thenReturn(users);

        // 该角色下无用户
        userSvc.getUserByRoleId(123L);

        Mockito.when(perssionCommSvc.getUserRoleRltByRoleIds(Mockito.any())).thenReturn(uRoles);
        userSvc.getUserByRoleId(123L);
    }

    @Test
    public void testUpdateCurUser() throws Exception {
        SysUser user = new SysUser();
        user.setId(123L);
        user.setLoginCode("loginCode");
        Mockito.when(esUserSvc.getById(Mockito.anyLong())).thenReturn(null);
        Mockito.when(esUserSvc.saveOrUpdate(Mockito.any(SysUser.class))).thenReturn(123L);

        SysUser cur = new SysUser();
        // cur.setId(123L);
        cur.setLoginCode("loginCode");
        cur.setUserName("userName");
        cur.setEmailAdress("<EMAIL>");
        cur.setIcon("/default.png");

        // 用户不存在
        try {
            userSvc.updateCurUser(cur, null);
        } catch (Exception e) {
            assertEquals("BS_USER_NOT_EXIST", e.getMessage());
        }

        // 常规保存
        Mockito.when(esUserSvc.getById(Mockito.anyLong())).thenReturn(user);
        File file = new File(UserSvcTest.class.getResource("/testdata/10006.png").getPath());
        System.out.println(file.getAbsolutePath());
        MockMultipartFile image = new MockMultipartFile("file", "10006.png", MediaType.TEXT_PLAIN_VALUE, new FileInputStream(file));
        userSvc.updateCurUser(cur, image);

        // 用户信息校验失败
        user.setLoginCode("测试");
        try {
            userSvc.updateCurUser(cur, null);
        } catch (Exception e) {
            // assertEquals("BS_USER_LOGIN_NAME_FORMAT_ERR", e.getMessage());
        }
    }

    @Test
    public void testResetPwd() {
        SysUser user = new SysUser();
        user.setId(123L);
        user.setLoginCode("loginCode");
        user.setLoginPasswd("6d714d66ca258f8be5272f8eec53fb2e"); // Tarsier_001
        Mockito.when(esUserSvc.getById(Mockito.anyLong())).thenReturn(user);
        Mockito.when(esUserSvc.saveOrUpdate(Mockito.any(SysUser.class))).thenReturn(123L);

        SysUserPwdResetParam param = new SysUserPwdResetParam();
        param.setUserId(1L);
        param.setOldPasswd("yMQvN2GlHIWLATwU/N7a4Q=="); // Tarsier_001
        param.setNewPasswd("yMQvN2GlHIWLATwU/N7a4Q==");

        // 新旧密码一样
        Long id = userSvc.resetPwd(BaseConst.DEFAULT_DOMAIN_ID,param);
        assertEquals(1L, id.longValue());

        // 原密码错误
        try {
            param.setOldPasswd("QNg7nQk4g4BWUyVvGB296Q==");
            userSvc.resetPwd(BaseConst.DEFAULT_DOMAIN_ID,param);
        } catch (Exception e) {
            assertEquals("BS_OLD_PASSWORD_ERROR", e.getMessage());
        }

        // 常规测试
        param.setOldPasswd("yMQvN2GlHIWLATwU/N7a4Q==");
        param.setNewPasswd("QNg7nQk4g4BWUyVvGB296Q=="); // Admin@123
        Long id2 = userSvc.resetPwd(BaseConst.DEFAULT_DOMAIN_ID,param);
        assertEquals(123L, id2.longValue());

        Mockito.when(esUserSvc.getById(Mockito.anyLong())).thenReturn(null);
        // 用户不存在
        try {
            userSvc.resetPwd(BaseConst.DEFAULT_DOMAIN_ID,param);
        } catch (Exception e) {
            assertEquals("BS_USER_NOT_EXIST", e.getMessage());
        }

        // 没有权限
        param.setUserId(123L);
        try {
            userSvc.resetPwd(BaseConst.DEFAULT_DOMAIN_ID,param);
        } catch (Exception e) {
            // assertEquals("BS_NOT_AUTH", e.getMessage());
        }
    }

    @Test
    public void testResetPwdByAdmin() {
        SysUser user = new SysUser();
        user.setId(123L);
        user.setLoginCode("loginCode");
        Mockito.when(esUserSvc.getById(Mockito.anyLong())).thenReturn(null);
        Mockito.when(esUserSvc.saveOrUpdate(Mockito.any(SysUser.class))).thenReturn(123L);

        SysUserPwdResetParam pwdParam = new SysUserPwdResetParam();
        pwdParam.setUserId(123L);
        pwdParam.setNewPasswd("QNg7nQk4g4BWUyVvGB296Q==");

        // 用户不存在
        try {
            userSvc.resetPwdByAdmin(pwdParam);
        } catch (Exception e) {
            // assertEquals("BS_USER_NOT_EXIST", e.getMessage());
        }

        Mockito.when(esUserSvc.getById(Mockito.anyLong())).thenReturn(user);
        Long id = userSvc.resetPwdByAdmin(pwdParam);
        assertEquals(123L, id.longValue());
    }

    @Test
    public void testImportUserByExcel() throws Exception {
        List<SysRole> roles = new ArrayList<>();
        SysRole role = new SysRole();
        role.setId(123L);
        role.setRoleName("测试1");
        SysRole role1 = new SysRole();
        role1.setId(456L);
        role1.setRoleName("测试2");
        roles.add(role);
        roles.add(role1);
        Mockito.when(roleSvc.getListByCdt(Mockito.any(CSysRole.class))).thenReturn(roles);

        Map<String, OrgNodeInfo> orgStructure = new HashMap<>();
        Mockito.when(orgSvc.getOrgStructure(BaseConst.DEFAULT_DOMAIN_ID)).thenReturn(orgStructure);

        SysOrg org = new SysOrg();
        org.setOrgName("测试1");
        SysOrg org1 = new SysOrg();
        org1.setOrgName("测试2");
        Mockito.when(orgSvc.getOrgByOrgFullName(Mockito.anyString(), Mockito.any())).thenReturn(org1).thenReturn(org1);

        List<SysUser> users = new ArrayList<>();
        SysUser user = new SysUser();
        user.setLoginCode("loginCode1");
        user.setUserName("userName1");
        user.setEmailAdress("<EMAIL>");
        users.add(user);
        Mockito.when(esUserSvc.getListByCdt(Mockito.any(CSysUser.class))).thenReturn(users).thenReturn(null);

        // saveOrUpdateBatch使用
        Mockito.when(esUserSvc.saveOrUpdateBatch(Mockito.any())).thenReturn(1);
        Mockito.when(perssionCommSvc.saveUserRoleRltBatch(Mockito.anyList())).thenReturn(1);
        
        Set<SysRole> roles2 = new HashSet<>();
        SysRole role2 = new SysRole();
        role2.setId(1L);
        roles2.add(role2);
        List<UserInfo> userInfos = new ArrayList<>();
        UserInfo userInfo1 = new UserInfo();
        userInfo1.setLoginCode("loginCode1");
        userInfo1.setUserName("userName1");
        userInfo1.setEmailAdress("<EMAIL>");
        userInfo1.setRoles(roles2);
        UserInfo userInfo2 = new UserInfo();
        userInfo2.setLoginCode("loginCode2");
        userInfo2.setUserName("userName2");
        userInfo2.setEmailAdress("<EMAIL>");
        userInfos.add(userInfo1);
        userInfos.add(userInfo2);

        File file = new File(UserSvcTest.class.getResource("/testdata/userTest.xlsx").getPath());
        System.out.println(file.getAbsolutePath());
        MockMultipartFile userFile = new MockMultipartFile("file", "userTest.xlsx", MediaType.TEXT_PLAIN_VALUE, new FileInputStream(file));

        userSvc.importUserByExcel(BaseConst.DEFAULT_DOMAIN_ID,userFile);

    }

    @Test
    public void testExportXlsxUserInfo() {
        Mockito.when(esUserSvc.countByCondition(Mockito.any())).thenReturn(10L);

        List<SysUser> users = new ArrayList<>();
        SysUser user = new SysUser();
        user.setId(123L);
        users.add(user);
        Page<SysUser> page = new Page<>(1, 10, 1, 1, users);
        Mockito.when(esUserSvc.getSortListByQuery(Mockito.anyInt(), Mockito.anyInt(), Mockito.any(QueryBuilder.class), Mockito.anyList())).thenReturn(page);
        Set<SysRole> tempRoles = new HashSet<>();
        SysRole tempRole = new SysRole();
        tempRole.setId(1L);
        tempRoles.add(tempRole);
        Set<SysOrg> tempOrgs = new HashSet<>();
        SysOrg tempOrg = new SysOrg();
        tempOrg.setId(1L);
        tempOrgs.add(tempOrg);
        UserInfo userInfo = new UserInfo();
        userInfo.setId(123L);
        userInfo.setLoginCode("zmjUserTest");
        userInfo.setUserName("zmjUserTest");
        userInfo.setEmailAdress("<EMAIL>");
        userInfo.setRoles(tempRoles);
        userInfo.setOrgs(tempOrgs);
        Mockito.when(esUserSvc.fillInfo(Mockito.anyList(), Mockito.anyBoolean())).thenReturn(Collections.singletonList(userInfo));

        List<SysRole> roles = new ArrayList<>();
        SysRole role = new SysRole();
        role.setId(456L);
        roles.add(role);
        Mockito.when(roleSvc.getListByCdt(Mockito.any(CSysRole.class))).thenReturn(roles);

        List<SysOrg> orgs = new ArrayList<>();
        SysOrg org = new SysOrg();
        org.setId(0L);
        org.setOrgName("优锘科技");
        SysOrg org1 = new SysOrg();
        org1.setId(1L);
        org1.setOrgName("测试1");
        org1.setParentOrgId(0L);
        orgs.add(org);
        orgs.add(org1);
        Mockito.when(orgSvc.queryListByCdt(Mockito.any(CSysOrg.class))).thenReturn(orgs);

        // 导出模板
        userSvc.exportUserToExcel(true, new CSysUser());

        // 导出用户信息
        userSvc.exportUserToExcel(false, new CSysUser());
    }

    @Test
    public void testDeleteById() {
        Mockito.when(esUserSvc.deleteUserById(Mockito.anyLong())).thenReturn(1);

        userSvc.deleteById(123L);

        // 删除当前id
        try {
            userSvc.deleteById(1L);
        } catch (Exception e) {
            // assertEquals("BS_USER_DELETE_CUR_ERROR", e.getMessage());
        }
    }

    @Test
    public void testDeleteSysUserByLoginCodeBatch() {
        List<SysUser> users = new ArrayList<>();
        SysUser user = new SysUser();
        user.setLoginCode("loginCode1");
        user.setUserName("userName1");
        user.setEmailAdress("<EMAIL>");
        users.add(user);
        Mockito.when(esUserSvc.getListByCdt(Mockito.any(CSysUser.class))).thenReturn(users).thenReturn(null);
        Mockito.when(esUserSvc.deleteByIds(Mockito.anyList())).thenReturn(1);

        List<String> loginCodes = new ArrayList<>();
        userSvc.deleteSysUserByLoginCodeBatch(BaseConst.DEFAULT_DOMAIN_ID,loginCodes);

        loginCodes.add("testCode");
        userSvc.deleteSysUserByLoginCodeBatch(BaseConst.DEFAULT_DOMAIN_ID,loginCodes);
    }

    @Test
    public void testGetModuleTree() {
        ModuleNodeInfo tree = new ModuleNodeInfo();
        Mockito.when(esModuleSvc.getModuleTree(Mockito.anyLong())).thenReturn(tree);

        userSvc.getModuleTree(BaseConst.DEFAULT_DOMAIN_ID,123L);
    }

    @Test
    public void testGetDataModule() {
        Map<String, List<SysRoleDataModuleRlt>> map = new HashMap<>();
        Mockito.when(esUserSvc.getDataModule(Mockito.anyLong(), Mockito.any())).thenReturn(map);

        userSvc.getDataModule(123L, new ArrayList<>());
    }

    @Test
    public void testEnshrineModule() {
        Mockito.when(enshrineSvc.saveOrUpdate(Mockito.any(SysUserModuleEnshrineRlt.class))).thenReturn(123L);

        userSvc.enshrineModule(BaseConst.DEFAULT_DOMAIN_ID,null, 456L);
        userSvc.enshrineModule(BaseConst.DEFAULT_DOMAIN_ID,123L, 456L);
    }

    @Test
    public void testUnenshrineModule() {
        Mockito.when(enshrineSvc.deleteByQuery(Mockito.any(QueryBuilder.class), Mockito.anyBoolean())).thenReturn(1);

        userSvc.unenshrineModule(BaseConst.DEFAULT_DOMAIN_ID,null, 456L);
        userSvc.unenshrineModule(BaseConst.DEFAULT_DOMAIN_ID,123L, 456L);
    }

    @Test
    public void testCountByCondition() {
        Mockito.when(esUserSvc.countByCondition(Mockito.any(QueryBuilder.class))).thenReturn(1L);

        userSvc.countByCondition(BaseConst.DEFAULT_DOMAIN_ID,QueryBuilders.boolQuery());
    }

    @Test
    public void testSaveUserRoleRlt() {
        Mockito.when(perssionCommSvc.deleteUserRoleRltByQuery(Mockito.any(), Mockito.anyBoolean())).thenReturn(1);
        Mockito.when(perssionCommSvc.saveUserRoleRltBatch(Mockito.anyList())).thenReturn(1);

        Set<Long> roleIds = new HashSet<>();
        roleIds.add(123L);
        SaveUserRoleRltRequestDto bean = SaveUserRoleRltRequestDto.builder().userId(123L).roleIds(roleIds).build();
        userSvc.saveUserRoleRlt(bean);
    }

    @Test
    public void testRecordLoginFailNum() {
        List<SysUser> users = new ArrayList<>();
        SysUser user = new SysUser();
        user.setId(456L);
        user.setLoginCode("loginCode1");
        users.add(user);
        Mockito.when(esUserSvc.getListByCdt(Mockito.any(CSysUser.class))).thenReturn(users);

        Mockito.when(esUserSvc.saveOrUpdate(Mockito.any(SysUser.class))).thenReturn(1L);

        Mockito.when(logSvc.addLoginLog(Mockito.anyLong())).thenReturn(null);

        userSvc.recordLoginFailNum(BaseConst.DEFAULT_DOMAIN_ID,"test", true);

    }

    @Test
    public void testGetCurrentUser() {
        SysUser user = new SysUser();
        user.setId(456L);
        user.setLoginCode("loginCode1");
        Mockito.when(esUserSvc.getById(Mockito.anyLong())).thenReturn(user);
        userSvc.getCurrentUser();
    }

    @Test
    public void testUnLockUsersByDuration() {
        List<SysUser> users = new ArrayList<>();
        SysUser user = new SysUser();
        user.setId(456L);
        user.setLoginCode("loginCode1");
        users.add(user);
        Mockito.when(esUserSvc.getListByQuery(Mockito.any())).thenReturn(users);

        Mockito.when(esUserSvc.saveOrUpdateBatch(Mockito.anyList())).thenReturn(1);
        
        userSvc.unLockUsersByDuration(123L);
    }

}
