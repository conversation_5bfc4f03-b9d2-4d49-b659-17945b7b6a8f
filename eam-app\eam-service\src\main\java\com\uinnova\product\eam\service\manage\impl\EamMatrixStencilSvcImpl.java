package com.uinnova.product.eam.service.manage.impl;

import com.binary.core.util.BinaryUtils;
import com.binary.jdbc.Page;
import com.uinnova.product.eam.base.diagram.utils.RedisUtil;
import com.uinnova.product.eam.base.util.EamUtil;
import com.uinnova.product.eam.comm.dto.EamMatrixQueryVO;
import com.uinnova.product.eam.comm.model.es.EamMatrixStencil;
import com.uinnova.product.eam.comm.model.es.EamMatrixStencilAttr;
import com.uinnova.product.eam.model.constants.StatusConstant;
import com.uinnova.product.eam.service.IEamCIClassApiSvc;
import com.uinnova.product.eam.service.es.EamMatrixStencilDao;
import com.uinnova.product.eam.service.manage.EamMatrixStencilSvc;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uino.api.client.cmdb.IRltClassApiSvc;
import com.uino.api.client.permission.IUserApiSvc;
import com.uino.bean.cmdb.base.ESCIAttrDefInfo;
import com.uino.bean.cmdb.base.ESCIClassInfo;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.sys.base.ESDictionaryItemInfo;
import com.uino.bean.sys.query.ESDictionaryItemSearchBean;
import com.uino.service.sys.microservice.IDictionarySvc;
import com.uino.util.sys.SysUtil;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 矩阵制品管理业务实现
 * <AUTHOR>
 */
@Slf4j
@Service
public class EamMatrixStencilSvcImpl implements EamMatrixStencilSvc {

    @Resource
    private EamMatrixStencilDao matrixStencilDao;

    @Resource
    private IEamCIClassApiSvc ciClassApiSvc;

    @Resource
    private IRltClassApiSvc rltClassApiSvc;

    @Autowired
    private IDictionarySvc dictSvc;

    @Autowired
    private RedisUtil redisUtil;

    @Resource
    private IUserApiSvc userApiSvc;

    private final static String MATRIX_STENCIL_PUBLISH = "MATRIX_STENCIL_PUBLISH:";

    private static final String DICT_MATRIX_STENCIL_TYPE = "矩阵制品类型分类";

    @Override
    public Long saveOrUpdate(EamMatrixStencil vo) {
        BinaryUtils.checkEmpty(vo.getName(), "name");
        BinaryUtils.checkEmpty(vo.getType(), "type");
        SysUser userInfo = SysUtil.getCurrentUserInfo();
        EamMatrixStencil save;
        if (BinaryUtils.isEmpty(vo.getId())) {
            save = EamUtil.copy(vo, EamMatrixStencil.class);
            save.setStatus(StatusConstant.ENABLE);
            save.setPublished(StatusConstant.UNPUBLISHED);
            save.setOrgId(vo.getOrgId());
            save.setVersion(0);
        } else {
            EamMatrixStencil old = matrixStencilDao.getById(vo.getId());
            save = EamUtil.copy(old, EamMatrixStencil.class);
            save.setName(vo.getName());
            save.setType(vo.getType());
            save.setCrowd(vo.getCrowd());
            save.setScene(vo.getScene());
            save.setContent(vo.getContent());
            save.setOrgId(vo.getOrgId());
            save.setDescription(vo.getDescription());
        }
        //校验重名
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("name.keyword", vo.getName().trim()));
        query.must(QueryBuilders.termQuery("domainId", userInfo.getDomainId()));
        query.must(QueryBuilders.termQuery("status", StatusConstant.ENABLE));
        EamMatrixStencil stencil = matrixStencilDao.selectOne(query);
        Assert.isTrue(stencil == null || stencil.getId().equals(vo.getId()), "制品名称已存在");
        return matrixStencilDao.saveOrUpdate(save);
    }

    @Override
    public Long saveOrUpdateDetail(EamMatrixStencil vo) {
        BinaryUtils.checkEmpty(vo.getId(), "ID");
        EamMatrixStencil matrixStencil = matrixStencilDao.getById(vo.getId());
        BinaryUtils.checkNull(matrixStencil, "矩阵制品");
        if(!BinaryUtils.isEmpty(vo.getMatrixName())){
            matrixStencil.setMatrixName(vo.getMatrixName());
        }
        matrixStencil.setSourceRules(vo.getSourceRules());
        matrixStencil.setTargetRules(vo.getTargetRules());
        matrixStencil.setSourceClass(vo.getSourceClass());
        matrixStencil.setSourceAttrs(vo.getSourceAttrs());
        matrixStencil.setTargetClass(vo.getTargetClass());
        matrixStencil.setTargetAttrs(vo.getTargetAttrs());
        matrixStencil.setRltClass(vo.getRltClass());
        matrixStencil.setRltAttrs(vo.getRltAttrs());
        matrixStencil.setDisplayNum(vo.getDisplayNum());
        matrixStencilDao.saveOrUpdate(matrixStencil);
        return vo.getId();
    }

    @Override
    public Page<EamMatrixStencil> queryList(EamMatrixQueryVO vo) {
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        if(!BinaryUtils.isEmpty(vo.getName())){
            BoolQueryBuilder shouldQuery = QueryBuilders.boolQuery();
            List<SysUser> userLike = userApiSvc.getUserInfoByName(vo.getName().trim());
            if(!CollectionUtils.isEmpty(userLike)){
                Set<String> nameLists = userLike.stream().map(SysUser::getLoginCode).collect(Collectors.toSet());
                shouldQuery.should(QueryBuilders.termsQuery("creator.keyword", nameLists));
            }
            shouldQuery.should(QueryBuilders.wildcardQuery("name.keyword", "*" + vo.getName().trim() + "*"));
            shouldQuery.should(QueryBuilders.wildcardQuery("creator.keyword", "*" + vo.getName().trim() + "*"));
            queryBuilder.must(shouldQuery);
        }
        if(!BinaryUtils.isEmpty(vo.getPublished())){
            queryBuilder.must(QueryBuilders.termQuery("published", vo.getPublished()));
        }
        if(!BinaryUtils.isEmpty(vo.getType())){
            queryBuilder.must(QueryBuilders.termQuery("type", vo.getType()));
        }
        Integer status = BinaryUtils.isEmpty(vo.getStatus())? StatusConstant.ENABLE : vo.getStatus();
        queryBuilder.must(QueryBuilders.termQuery("status", status));
        Page<EamMatrixStencil> result = matrixStencilDao.getSortListByQuery(vo.getPageNum(), vo.getPageSize(), queryBuilder, "modifyTime", false);
        if(!CollectionUtils.isEmpty(result.getData())){
            Set<String> userCodes = result.getData().stream().map(EamMatrixStencil::getCreator).collect(Collectors.toSet());
            Map<String, String> userMap = userApiSvc.getNameByCodes(userCodes);
            result.getData().forEach(each -> each.setCreator(userMap.getOrDefault(each.getCreator(), each.getCreator())));
        }
        return result;
    }

    @Override
    public Long updatePublished(Long id, Integer published) {
        BinaryUtils.checkEmpty(published, "published");
        BinaryUtils.checkEmpty(id, "id");
        EamMatrixStencil stencil = matrixStencilDao.getById(id);
        BinaryUtils.checkNull(stencil, "矩阵制品");
        boolean lock = redisUtil.setnx(MATRIX_STENCIL_PUBLISH + id, "1", 60, TimeUnit.SECONDS);
        if(!lock){
            throw new IllegalArgumentException("制品发布中，请稍候重试");
        }
        try {
            //发布动作时,保存一个历史版本,并记录当前分类属性名,否则会影响资产库矩阵表格展示
            stencil.setPublished(published);
            if(StatusConstant.PUBLISHED.equals(published)){
                if(BinaryUtils.isEmpty(stencil.getVersion())){
                    stencil.setVersion(1);
                    stencil.setPublishId(stencil.getId());
                }else{
                    stencil.setVersion(stencil.getVersion() + 1);
                }
                //校验必填项
                Assert.notNull(stencil.getMatrixName(), "矩阵名称不能为空");
                Assert.notNull(stencil.getSourceClass(), "源端分类不能为空");
                Assert.notEmpty(stencil.getSourceAttrs(), "源端属性不能为空");
                Assert.notNull(stencil.getTargetClass(), "目标端分类不能为空");
                Assert.notEmpty(stencil.getTargetAttrs(), "目标端属性不能为空");
                Assert.notNull(stencil.getRltClass(), "关系分类不能为空");
                this.createHistoryStencil(stencil);
            }
            matrixStencilDao.saveOrUpdate(stencil);
        } catch (Exception e){
            log.error("矩阵制品发布失败【{}】", e.getMessage());
            throw new IllegalArgumentException("矩阵制品发布失败");
        } finally {
            redisUtil.del(MATRIX_STENCIL_PUBLISH + id);
        }
        return id;
    }

    /**
     * 创建历史版本
     * @param stencil 矩阵
     */
    private void createHistoryStencil(EamMatrixStencil stencil) {
        EamMatrixStencil history = EamUtil.copy(stencil, EamMatrixStencil.class);
        history.setId(null);
        history.setStatus(StatusConstant.DISABLE);
        history.setPublishId(stencil.getId());
        List<Long> sourceAttrs = stencil.getSourceAttrs();
        if(!CollectionUtils.isEmpty(sourceAttrs)){
            ESCIClassInfo classInfo =  ciClassApiSvc.queryClassById(stencil.getSourceClass());
            BinaryUtils.checkNull(classInfo, "对象分类：" + stencil.getSourceClass());
            Map<Long, EamMatrixStencilAttr> attrMap = new HashMap<>();
            for (ESCIAttrDefInfo def : classInfo.getAttrDefs()) {
                if(sourceAttrs.contains(def.getId())){
                    attrMap.put(def.getId(), new EamMatrixStencilAttr(def.getProName(), def.getProType(), def.getProDropSourceDef(), def.getConstraintRule()));
                }
            }
            history.setSourceName(classInfo.getClassName());
            history.setSourceAttrMap(attrMap);
        }
        List<Long> targetAttrs = stencil.getTargetAttrs();
        if(!CollectionUtils.isEmpty(targetAttrs)){
            ESCIClassInfo classInfo =  ciClassApiSvc.queryClassById(stencil.getTargetClass());
            BinaryUtils.checkNull(classInfo, "对象分类：" + stencil.getTargetClass());
            Map<Long, EamMatrixStencilAttr> attrMap = new HashMap<>();
            for (ESCIAttrDefInfo def : classInfo.getAttrDefs()) {
                if(targetAttrs.contains(def.getId())){
                    attrMap.put(def.getId(), new EamMatrixStencilAttr(def.getProName(), def.getProType(), def.getProDropSourceDef(), def.getConstraintRule()));
                }
            }
            history.setTargetName(classInfo.getClassName());
            history.setTargetAttrMap(attrMap);
        }
        List<Long> rltAttrs = stencil.getRltAttrs();
        if(!CollectionUtils.isEmpty(rltAttrs)){
            CcCiClassInfo rltClass = rltClassApiSvc.getRltClassById(stencil.getRltClass());
            BinaryUtils.checkNull(rltClass, "关系分类：" + stencil.getSourceClass());
            Map<Long, EamMatrixStencilAttr> rltAttrMap = new HashMap<>();
            for (CcCiAttrDef def : rltClass.getAttrDefs()) {
                if(rltAttrs.contains(def.getId())){
                    rltAttrMap.put(def.getId(), new EamMatrixStencilAttr(def.getProName(), def.getProType(), def.getProDropSourceDef(), def.getConstraintRule()));
                }
            }
            history.setRltName(rltClass.getCiClass().getClassName());
            history.setRltAttrMap(rltAttrMap);
        }
        matrixStencilDao.saveOrUpdate(history);
    }

    @Override
    public Integer deleteById(Long id) {
        EamMatrixStencil matrixStencil = matrixStencilDao.getById(id);
        BinaryUtils.checkNull(matrixStencil, "矩阵制品");
        matrixStencil.setStatus(StatusConstant.DISABLE);
        matrixStencilDao.saveOrUpdate(matrixStencil);
        return 1;
    }

    @Override
    public EamMatrixStencil getById(Long id) {
        return matrixStencilDao.getById(id);
    }

    @Override
    public EamMatrixStencil getHistoryById(Long id, Integer version) {
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("publishId", id));
        query.must(QueryBuilders.termQuery("version", version));
        List<EamMatrixStencil> list = matrixStencilDao.getListByQuery(query);
        if(CollectionUtils.isEmpty(list)){
            return null;
        }
        return list.get(0);
    }

    @Override
    public List<EamMatrixStencil> queryMatrixStencilByType(Set<Long> types) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        //制品状态正常并且已发布
        boolQueryBuilder.must(QueryBuilders.termQuery("status", 1));
        boolQueryBuilder.must(QueryBuilders.termQuery("published", 1));
        //分类集合
        if (!BinaryUtils.isEmpty(types)) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("type", types));
        }
        List<EamMatrixStencil> artifactList = matrixStencilDao.getListByQuery(boolQueryBuilder);
        if(BinaryUtils.isEmpty(artifactList)){
            return Collections.emptyList();
        }
        return artifactList;
    }

    @Override
    public String queryStencilTypeNameByMatrixId(Long matrixStencilId) {
        String stencilTypeName = "";
        //查询矩阵信息
        EamMatrixStencil stencil = matrixStencilDao.getById(matrixStencilId);
        //查询矩阵制品分类信息
         ESDictionaryItemSearchBean dicSearchBean = ESDictionaryItemSearchBean.builder()
                .domainId(SysUtil.getCurrentUserInfo().getDomainId())
                .dictName(DICT_MATRIX_STENCIL_TYPE).build();
        List<ESDictionaryItemInfo> dictMatrixInfos = dictSvc.searchDictItemListByBean(dicSearchBean);
        //如果矩阵制品信息为空,type为空或者字典为空直接返回
        if(BinaryUtils.isEmpty(stencil) || BinaryUtils.isEmpty(stencil.getType())
                || BinaryUtils.isEmpty(dictMatrixInfos)){
            return stencilTypeName;
        }
        //定义分类名称和分类名称的map
        Map<Long, String> typeNameMap = new HashMap<>();
        //筛选出分类的id和名称
        dictMatrixInfos.forEach(e -> {
            String type = e.getAttrs().get("ID");
            String typeName = e.getAttrs().get("名称");
            typeNameMap.put(Long.valueOf(type), typeName);
        });
        //从字典中获取分类名称 如果为空返回空字符串
        String mapTypeName = typeNameMap.get(stencil.getType().longValue());
        return BinaryUtils.isEmpty(mapTypeName) ? stencilTypeName : mapTypeName;
    }
}
