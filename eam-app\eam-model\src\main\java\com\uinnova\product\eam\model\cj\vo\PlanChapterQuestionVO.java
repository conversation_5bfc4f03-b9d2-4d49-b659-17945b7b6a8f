package com.uinnova.product.eam.model.cj.vo;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.io.Serializable;

/**
 * @description:
 * @author: Lc
 * @create: 2022-03-02 11:01
 */
@Data
public class PlanChapterQuestionVO implements Serializable {

    @Comment("主键id")
    private Long id;

    @Comment("问题详情")
    private String question;

    @Comment("最新问题回复")
    private String answer;

    @Comment("所属章节/内容块")
    private String module;

    @Comment("问题状态信息")
    private String questionState;

    @Comment("验证功能是否可用")
    private Boolean checkSign;

    @Comment("验证结果")
    private String checkResult;

}
