package com.uinnova.product.vmdb.comm.rest;

import com.binary.core.util.BinaryUtils;
import com.binary.framework.Local;
import com.binary.framework.bean.User;
import com.binary.framework.exception.ServiceException;
import com.binary.framework.util.ControllerUtils;
import com.binary.framework.util.UserUtils;
import com.binary.framework.web.SessionKey;
import com.binary.json.JSON;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

/**
 *
 * <AUTHOR>
 *
 */
@RequestMapping("/provider/rest")
public class RestProviderMvc {

    private RestProviderManager restProviderManager;

    public RestProviderManager getRestProviderManager() {
        return restProviderManager;
    }

    public void setRestProviderManager(RestProviderManager restProviderManager) {
        this.restProviderManager = restProviderManager;
    }

    @RequestMapping("/{svc}/{mod}")
    public void post(HttpServletRequest request, HttpServletResponse response, @PathVariable("svc") String svc, @PathVariable("mod") String mod, @RequestBody String body) {
        if (BinaryUtils.isEmpty(svc) || BinaryUtils.isEmpty(mod)) {
            throw new ServiceException(" is wrong path : " + request.getPathInfo());
        }

        Object result = null;
        boolean open = Local.isOpen();
        try {
            if (!open) {
                String jsonUser = request.getHeader(SessionKey.SYSTEM_USER);
                User user = null;
                if (!BinaryUtils.isEmpty(jsonUser)) {
                    user = UserUtils.valueOf(jsonUser);
                }
                Local.open(user);
            }
            result = restProviderManager.execute(svc, mod, body);

            if (!open) {
                Local.commit();
            }
        } catch (Throwable t) {
            if (!open) {
                Local.rollback();
            }
            throw BinaryUtils.transException(t, ServiceException.class);
        } finally {
            if (!open) {
                Local.close();
            }
        }

        String jsonResult = null;
        if (result != null) {
            jsonResult = JSON.toString(result);
        }
        ControllerUtils.returnJson(request, response, jsonResult);
    }

}
