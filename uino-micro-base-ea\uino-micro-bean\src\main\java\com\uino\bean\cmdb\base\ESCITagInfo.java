package com.uino.bean.cmdb.base;

import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.util.Assert;

import com.uinnova.product.vmdb.comm.model.rule.CcCiTagDef;
import com.uino.bean.permission.business.IValidDto;

import lombok.Getter;
import lombok.Setter;

/**
 * ES-TAG类
 * 
 * <AUTHOR>
 *
 */
@Getter
@Setter
@ApiModel(value="标签信息类",description = "标签信息")
public class ESCITagInfo extends CcCiTagDef implements Serializable, IValidDto {

    private static final long serialVersionUID = 7130994900735605492L;
    
    @ApiModelProperty(value="标签定义集合")
    private List<ESTagRuleInfo> rules;

    @Override
    public void valid() {
        Assert.notNull(getDirId(), "X_PARAM_NOT_NULL${name:dirId}");
        Assert.notNull(getTagName(), "X_PARAM_NOT_NULL${name:tagName}");
        Assert.notNull(getRules(), "X_PARAM_NOT_NULL${name:rules}");
    }

}
