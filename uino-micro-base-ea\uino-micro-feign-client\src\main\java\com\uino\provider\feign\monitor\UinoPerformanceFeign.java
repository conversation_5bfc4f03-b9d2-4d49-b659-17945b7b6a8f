package com.uino.provider.feign.monitor;

import java.util.List;

import org.elasticsearch.index.query.QueryBuilder;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.binary.jdbc.Page;
import com.uino.bean.monitor.buiness.PerformanceQueryDto;
import com.uino.bean.tp.base.FinalPerformanceDTO;
import com.uino.provider.feign.config.BaseFeignConfig;

@Component
@FeignClient(name = "${feign-server-name:tarsier-platform}", path = "${feign-server-path:tarsier-platform}/feign/uinoPrformance", configuration = {
		BaseFeignConfig.class })
public interface UinoPerformanceFeign {
	/**
	 * 保存指标数据<br>
	 * 直接执行入库操作，不再校验指标各项属性信息
	 * 
	 * @param perf
	 * @return
	 */
	@PostMapping("saveOrUpdatePerformance")
	public Boolean saveOrUpdatePerformance(@RequestBody FinalPerformanceDTO perf);

	/**
	 * 分页查询指标数据
	 * 
	 * @param queryDto
	 * @return
	 */
	@PostMapping("queryPerformancePage")
	public Page<FinalPerformanceDTO> queryPerformancePage(@RequestBody PerformanceQueryDto queryDto);

	/**
	 * 批量保存指标数据<br>
	 * 直接执行入库操作，不再校验指标各项属性信息
	 * 
	 * @param perfs
	 * @return
	 */
	@PostMapping("saveOrUpdateBatch")
	public Boolean saveOrUpdateBatch(@RequestBody List<FinalPerformanceDTO> perfs);

	/**
	 * 条件查询指标数据
	 * 
	 * @param pageNum
	 * @param pageSize
	 * @param query
	 * @param order
	 * @param timeStart
	 * @param timeEnd
	 * @param asc
	 * @return
	 */
	@PostMapping("getSortListByQuery")
	public Page<FinalPerformanceDTO> getSortListByQuery(@RequestParam(value="domainId") Long domainId,@RequestParam(value = "pageNum") int pageNum,
			@RequestParam(value = "pageSize") int pageSize,
			@RequestBody QueryBuilder query,
			@RequestParam(value = "order", required = false) String order,
			@RequestParam(value = "timeStart", required = false) Long timeStart,
			@RequestParam(value = "timeEnd", required = false) Long timeEnd,
			@RequestParam(value = "asc") boolean asc);

	/**
	 * 获取当前最新一条指标数据
	 * 
	 * @param pageNum
	 * @param pageSize
	 * @param query
	 * @param order
	 * @param asc
	 * @return
	 */
	@PostMapping("getLastPerfListByQuery")
	public Page<FinalPerformanceDTO> getLastPerfListByQuery(@RequestParam(value="domainId") Long domainId ,@RequestParam(value = "pageNum") int pageNum,
			@RequestParam(value = "pageSize") int pageSize,@RequestBody QueryBuilder query,
			@RequestParam(value = "order", required = false) String order, @RequestParam(value = "asc") boolean asc);

	/**
	 * Get current performance data
	 *
	 * @param ciCodes
	 *            ciCodes
	 * @param kpiCodes
	 *            kpiCodes
	 * @return {@link List<FinalPerformanceDTO>}
	 */
	@PostMapping("getCurrentPerformance")
	List<FinalPerformanceDTO> getCurrentPerformance(@RequestParam(value = "domainId") Long domainId,@RequestBody List<String> ciCodes,
			@RequestParam(value = "kpiCodes", required = true) List<String> kpiCodes);

	/**
	 * Get current performance data
	 *
	 * @param ciCodes
	 *            ciCodes
	 * @return {@link List<FinalPerformanceDTO>}
	 */
	@PostMapping("getCurrentPerformanceByCiCodes")
	List<FinalPerformanceDTO> getCurrentPerformanceByCiCodes(@RequestParam(value="domainId") Long domainId,@RequestBody List<String> ciCodes);

	/**
	 * Get current performance data
	 *
	 * @param ciCode
	 *            ciCode
	 * @param kpiCode
	 *            kpiCode
	 * @param startTime
	 *            start time
	 * @param endTime
	 *            end time
	 * @return {@link List<FinalPerformanceDTO>}
	 */
	@PostMapping("getPerformanceByConditional")
	List<FinalPerformanceDTO> getPerformanceByConditional(@RequestParam(value = "domainId") Long domainId, @RequestBody String ciCode,
														  @RequestParam(value = "kpiCode", required = true) String kpiCode,
														  @RequestParam(value = "startTime", required = false) Long startTime,
														  @RequestParam(value = "endTime", required = false) Long endTime);
}
