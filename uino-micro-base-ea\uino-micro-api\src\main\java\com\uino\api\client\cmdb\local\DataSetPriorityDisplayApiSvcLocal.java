package com.uino.api.client.cmdb.local;

import com.uino.service.cmdb.dataset.microservice.IDataSetPriorityDisplaySvc;
import com.uino.api.client.cmdb.IDataSetPriorityDisplayApiSvc;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Classname DataSetPriorityDisplayApiSvcLocal
 * @Description TODO
 * @Date 2020/6/5 11:24
 * <AUTHOR> sh
 */
@Service
public class DataSetPriorityDisplayApiSvcLocal implements IDataSetPriorityDisplayApiSvc {
    @Autowired
    private IDataSetPriorityDisplaySvc dataSetPriorityDisplaySvc;

    @Override
    public boolean priorityDisplay(Long dataSetId, int displayType) {
        return dataSetPriorityDisplaySvc.priorityDisplay(dataSetId, displayType);
    }
}
