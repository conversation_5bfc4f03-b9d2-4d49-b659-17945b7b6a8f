package com.uino.bean.sys.base;

import com.binary.framework.bean.annotation.Comment;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.uino.bean.permission.business.IValidDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 
 * <AUTHOR>
 *
 */
@Comment("系统操作日志")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value="系统操作日志模块",description = "系统操作日志模块")
public class ESOperateLogModule implements Serializable, IValidDto {

    private static final long serialVersionUID = -440576289475763042L;

    @JsonIgnore
    @ApiModelProperty(value="ID",example = "123")
    private Long id;

    @ApiModelProperty(value="用户代码")
    private String moduleCode;

    @ApiModelProperty(value="用户姓名",example ="mike" )
    private String moduleName;

    @JsonIgnore
    @ApiModelProperty(value="操作模块",example = "commom")
    private String moduleMvcPackagePrefix;

    @JsonIgnore
    @ApiModelProperty(value="创建时间")
    private Long createTime;

    @JsonIgnore
    @ApiModelProperty(value="修改时间")
    private Long modifyTime;


    @Override
    public void valid() {

    }

}
