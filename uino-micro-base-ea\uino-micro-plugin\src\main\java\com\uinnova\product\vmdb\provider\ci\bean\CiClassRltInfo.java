package com.uinnova.product.vmdb.provider.ci.bean;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.vmdb.comm.model.ci.CcCiClassRlt;

import java.io.Serializable;

@Comment("分类与分类关系封装")
public class CiClassRltInfo implements Serializable {

	private static final long serialVersionUID = 1L;

	@Comment("分类与分类的关系")
	private CcCiClassRlt ciClassRlt;

	@Comment("起始分类信息")
	private CcCiClassInfo sourceCiClassInfo;

	@Comment("结束分类信息")
	private CcCiClassInfo targetCiClassInfo;

	@Comment("关系分类信息")
	private CcCiClassInfo rltClassInfo;

	public CcCiClassRlt getCiClassRlt() {
		return ciClassRlt;
	}

	public void setCiClassRlt(CcCiClassRlt ciClassRlt) {
		this.ciClassRlt = ciClassRlt;
	}

	public CcCiClassInfo getSourceCiClassInfo() {
		return sourceCiClassInfo;
	}

	public void setSourceCiClassInfo(CcCiClassInfo sourceCiClassInfo) {
		this.sourceCiClassInfo = sourceCiClassInfo;
	}

	public CcCiClassInfo getTargetCiClassInfo() {
		return targetCiClassInfo;
	}

	public void setTargetCiClassInfo(CcCiClassInfo targetCiClassInfo) {
		this.targetCiClassInfo = targetCiClassInfo;
	}

	public CcCiClassInfo getRltClassInfo() {
		return rltClassInfo;
	}

	public void setRltClassInfo(CcCiClassInfo rltClassInfo) {
		this.rltClassInfo = rltClassInfo;
	}

}
