package com.uinnova.product.eam.service.cj.dao;

import com.uinnova.product.eam.model.cj.domain.PlanTemplateChapter;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;

/**
 * @description:
 * @author: Lc
 * @create: 2022-01-05 16:57
 */
@Component
public class PlanTemplateChapterDao extends AbstractESBaseDao<PlanTemplateChapter, PlanTemplateChapter> {

    @Override
    public String getIndex() {
        return "uino_cj_plan_template_chapter";
    }

    @Override
    public String getType() {
        return getIndex();
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }

}
