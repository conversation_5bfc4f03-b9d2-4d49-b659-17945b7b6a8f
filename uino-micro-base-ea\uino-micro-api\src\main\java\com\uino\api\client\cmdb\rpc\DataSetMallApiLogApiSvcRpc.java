package com.uino.api.client.cmdb.rpc;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.binary.jdbc.Page;
import com.uino.bean.cmdb.base.dataset.log.DataSetMallApiLog;
import com.uino.provider.feign.cmdb.DataSetMallApiLogFeign;
import com.uino.api.client.cmdb.IDataSetMallApiLogApiSvc;

@Service
public class DataSetMallApiLogApiSvcRpc implements IDataSetMallApiLogApiSvc {

	@Autowired
	private DataSetMallApiLogFeign dataSetMallApiLogFeign;
	
	@Override
	public Page<DataSetMallApiLog> findPage(String dataSetId, int pageNum, int pageSize, String respUserName,
			String respUserCode) {
		JSONObject body = new JSONObject();
		body.put("dataSetId", dataSetId);
		body.put("pageNum", pageNum);
		body.put("pageSize", pageSize);
		body.put("respUserName", respUserName);
		body.put("respUserCode", respUserCode);
		return dataSetMallApiLogFeign.findPage(body);
	}

}
