package com.uino.api.client.monitor.local;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.binary.jdbc.Page;
import com.uino.api.client.monitor.IAlarmApiSvc;
import com.uino.bean.monitor.base.ESAlarm;
import com.uino.bean.monitor.buiness.AlarmInfo;
import com.uino.bean.monitor.buiness.AlarmQueryDto;
import com.uino.bean.monitor.buiness.SimulationAlarmBean;
import com.uino.service.simulation.IAlarmSvc;

@Service
public class AlarmApiSvcLocal implements IAlarmApiSvc {

    @Autowired
    private IAlarmSvc alarmSvc;

    @Override
    public Page<AlarmInfo> searchAlarms(AlarmQueryDto queryDto) {
        // TODO Auto-generated method stub
        return alarmSvc.searchAlarms(queryDto);
    }

    @Override
    public void saveAlarm(ESAlarm saveDto) {
        // TODO Auto-generated method stub
        alarmSvc.saveAlarm(saveDto);
    }

    @Override
    public Long countByQuery(AlarmQueryDto queryDto) {
        // TODO Auto-generated method stub
        return alarmSvc.countByQuery(queryDto);
    }

    @Override
    public ESAlarm queryOpenAlarm(String metric, Long classId, Long objId) {
        return alarmSvc.queryOpenAlarm(metric, classId,objId);
    }

	@Override
	public void simulationAlarms(SimulationAlarmBean bean) {
		alarmSvc.simulationAlarms(bean);
	}
}
