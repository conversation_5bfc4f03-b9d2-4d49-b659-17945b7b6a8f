package com.uinnova.product.vmdb.comm.bean;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.vmdb.comm.model.ci.CcCi;

/**
 * 
 * <AUTHOR>
 *
 */
@Comment("能够进行权限验证的")
public interface CiAuthable {

    /**
     * 获取数据ID
     * 
     * @return
     */
    CcCi getCi();

    /**
     * 设置数据权限
     * 
     * @param integer
     */
    void setAuthType(Integer integer);

    /**
     * 获取权限
     * 
     * @return
     */
    Integer getAuthType();

}
