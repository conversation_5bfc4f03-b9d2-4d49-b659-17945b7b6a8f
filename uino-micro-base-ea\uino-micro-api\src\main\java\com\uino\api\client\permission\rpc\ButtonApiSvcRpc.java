package com.uino.api.client.permission.rpc;

import com.uino.bean.permission.base.SysModule;
import com.uino.bean.permission.query.SysModuleCheck;
import com.uino.provider.feign.permission.ButtonFeign;
import com.uino.api.client.permission.IButtonApiSvc;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * @Title: ButtonApiSvcRpc
 * @Description: ButtonApiSvcRpc
 * @Author: YGQ
 * @Create: 2021-07-05 14:33
 **/
@Service
@Primary
public class ButtonApiSvcRpc implements IButtonApiSvc {

    private final ButtonFeign buttonFeign;

    @Autowired
    public ButtonApiSvcRpc(ButtonFeign buttonFeign) {
        this.buttonFeign = buttonFeign;
    }

    @Override
    public SysModule saveButton(SysModule buttonDto) {
        return buttonFeign.saveButton(buttonDto);
    }

    @Override
    public void deleteButton(Long buttonId) {
        buttonFeign.deleteButton(buttonId);
    }

    @Override
    public void saveButtonSort(Map<Long, Integer> orderDict) {
        buttonFeign.saveButtonSort(orderDict);
    }

    @Override
    public SysModuleCheck checkModuleSign(SysModule sysButton) {
        return buttonFeign.checkModuleSign(sysButton);
    }
}
