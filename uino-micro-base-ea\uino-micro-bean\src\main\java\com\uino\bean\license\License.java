package com.uino.bean.license;

import java.io.Serializable;

/**
 * 
 * <AUTHOR>
 *
 */
public class License implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 是否有效 **/
    private Boolean valid;

    /** 授权截止日期 **/
    private Integer authEndDate;

    /** 用户数 **/
    private Integer limitUserCount;

    /** 机柜数 **/
    private Integer limitRackCount;

    /** 开门数 **/
    private Integer limitDoorCount;

    /** 场景数 **/
    private Integer limitSceneCount;

    /** 视图数 **/
    private Integer limitDiagramCount;

    /** 使用时间, 单位：小时 **/
    private Integer limitUseTime;

    /** 冻结数据 **/
    private Boolean limitFreezeData;

    /** 系统实际使用时间, 单位：分钟 **/
    private Long onlineTime;

    /** 是否校验时间 thingJS使用 **/
    private Integer bindTime;

    /** 是否显示水印 thingJS使用 **/
    private Integer showLogo;
    
    public License() {
    }

    public License(Boolean valid, Integer authEndDate, Integer limitUserCount, Integer limitRackCount, Integer limitDoorCount, 
    		Integer limitSceneCount, Integer limitDiagramCount, Integer limitUseTime, Boolean limitFreezeData, Long onlineTime,
    		Integer bindTime, Integer showLogo) {
        this.valid = valid;
        this.authEndDate = authEndDate;
        this.limitUserCount = limitUserCount;
        this.limitRackCount = limitRackCount;
        this.limitDoorCount = limitDoorCount;
        this.limitSceneCount = limitSceneCount;
        this.limitDiagramCount = limitDiagramCount;
        this.limitUseTime = limitUseTime;
        this.limitFreezeData = limitFreezeData;
        this.onlineTime = onlineTime;
        this.bindTime = bindTime;
        this.showLogo = showLogo;        
    }

    @Override
    public String toString() {
        return this.authEndDate + ", " + this.limitUserCount + ", " + this.limitRackCount + ", " + this.limitDoorCount + ", " + this.limitSceneCount + ", " + this.limitDiagramCount + ", " + this.limitUseTime + ", " + this.limitFreezeData;
    }

    public Integer getAuthEndDate() {
        return authEndDate;
    }

    public void setAuthEndDate(Integer authEndDate) {
        this.authEndDate = authEndDate;
    }

    public Integer getLimitUserCount() {
        return limitUserCount;
    }

    public void setLimitUserCount(Integer limitUserCount) {
        this.limitUserCount = limitUserCount;
    }

    public Integer getLimitRackCount() {
        return limitRackCount;
    }

    public void setLimitRackCount(Integer limitRackCount) {
        this.limitRackCount = limitRackCount;
    }

    public Integer getLimitDoorCount() {
        return limitDoorCount;
    }

    public void setLimitDoorCount(Integer limitDoorCount) {
        this.limitDoorCount = limitDoorCount;
    }

    public Integer getLimitSceneCount() {
        return limitSceneCount;
    }

    public void setLimitSceneCount(Integer limitSceneCount) {
        this.limitSceneCount = limitSceneCount;
    }

    public Integer getLimitDiagramCount() {
        return limitDiagramCount;
    }

    public void setLimitDiagramCount(Integer limitDiagramCount) {
        this.limitDiagramCount = limitDiagramCount;
    }

    public Boolean getValid() {
        return valid;
    }

    public void setValid(Boolean valid) {
        this.valid = valid;
    }

    public Integer getLimitUseTime() {
        return limitUseTime;
    }

    public void setLimitUseTime(Integer limitUseTime) {
        this.limitUseTime = limitUseTime;
    }

    public Boolean getLimitFreezeData() {
        return limitFreezeData;
    }

    public void setLimitFreezeData(Boolean limitFreezeData) {
        this.limitFreezeData = limitFreezeData;
    }

    public Long getOnlineTime() {
        return onlineTime;
    }

    public void setOnlineTime(Long onlineTime) {
        this.onlineTime = onlineTime;
    }
    
    public Integer getBindTime() {
        return bindTime;
    }

    public void setBindTime(Integer bindTime) {
        this.bindTime = bindTime;
    }

    public Integer getShowLogo() {
        return showLogo;
    }

    public void setShowLogo(Integer showLogo) {
        this.showLogo = showLogo;
    }
}
