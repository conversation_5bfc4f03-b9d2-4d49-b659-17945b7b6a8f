package com.uinnova.product.eam.db.bean;

import com.binary.framework.bean.annotation.Comment;

public class GroupDiagramInfoCount {
	
	@Comment("小组id")
	private Long id;

	@Comment("小组名称")
	private String groupName;
	
	@Comment("小组视图被查看总数")
	private Long diagramReadCount;
	
	@Comment("视图Id")
	private Long diagramId;

	public Long getDiagramId() {
		return diagramId;
	}

	public void setDiagramId(Long diagramId) {
		this.diagramId = diagramId;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getGroupName() {
		return groupName;
	}

	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}

	public Long getDiagramReadCount() {
		return diagramReadCount;
	}

	public void setDiagramReadCount(Long diagramReadCount) {
		this.diagramReadCount = diagramReadCount;
	}
	
	
}
