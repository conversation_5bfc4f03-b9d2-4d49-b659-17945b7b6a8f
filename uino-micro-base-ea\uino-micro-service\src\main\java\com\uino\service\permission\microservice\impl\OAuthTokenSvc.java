package com.uino.service.permission.microservice.impl;

import java.util.List;

import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import com.uino.dao.permission.ESOauthRefreshTokenDetailSvc;
import com.uino.dao.permission.ESOauthTokenDetailSvc;
import com.uino.service.permission.microservice.IOAuthTokenSvc;
import com.uino.bean.permission.base.OauthRefreshTokenDetail;
import com.uino.bean.permission.base.OauthTokenDetail;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
public class OAuthTokenSvc implements IOAuthTokenSvc {

    @Autowired
    private ESOauthTokenDetailSvc tokenSvc;

    @Autowired
    private ESOauthRefreshTokenDetailSvc reTokenSvc;

    @Override
    public Long persistence(OauthTokenDetail tokenDetail) {
        // TODO Auto-generated method stub
        Assert.notNull(tokenDetail, "tokenDetail不得为空");
        return tokenSvc.saveOrUpdate(tokenDetail);
    }

    @Override
    public OauthTokenDetail getTokenDetailByCode(String tokenCode) {
        Assert.notNull(tokenCode, "tokenCode不得为空");
        List<OauthTokenDetail> tokens = tokenSvc
                .getListByQuery(QueryBuilders.termQuery("tokenCode.keyword", tokenCode));
        return tokens != null && tokens.size() > 0 ? tokens.get(0) : null;
    }

    @Override
    public OauthTokenDetail getTokenDetailByAuthId(String authId) {
        Assert.notNull(authId, "authId不得为空");
        List<OauthTokenDetail> tokens = tokenSvc
                .getListByQuery(QueryBuilders.termQuery("authenticationId.keyword", authId));
        return tokens != null && tokens.size() > 0 ? tokens.get(0) : null;
    }

    @Override
    public List<OauthTokenDetail> getTokenDetail(String clientId, String userLoginName) {
        BoolQueryBuilder query = new BoolQueryBuilder();
        if (clientId != null && !"".equals(clientId.trim())) {
            query.must(QueryBuilders.termQuery("client.keyword", clientId));
        }
        if (userLoginName != null && !"".equals(userLoginName.trim())) {
            query.must(QueryBuilders.termQuery("userName.keyword", userLoginName));
        }
        List<OauthTokenDetail> tokens = tokenSvc.getListByQuery(query);
        return tokens;
    }

    @Override
    public void delByCode(String tokenCode) {
        Assert.notNull(tokenCode, "tokenCode不得为空");
        tokenSvc.deleteByQuery(QueryBuilders.termQuery("tokenCode.keyword", tokenCode), true);
    }

    @Override
    public void delByRefreshTokenCode(String reTokenCode) {
        // TODO Auto-generated method stub
        Assert.notNull(reTokenCode, "retokenCode不得为空");
        tokenSvc.deleteByQuery(QueryBuilders.termQuery("refreshToken.keyword", reTokenCode), true);
    }

    @Override
    public Long persistenceRefreshToken(OauthRefreshTokenDetail reRokenDetail) {
        // TODO Auto-generated method stub reTokenSvc
        Assert.notNull(reRokenDetail, "reRokenDetail不得为空");
        return reTokenSvc.saveOrUpdate(reRokenDetail);
    }

    @Override
    public OauthRefreshTokenDetail getRefreshTokenDetail(String tokenCode) {
        Assert.notNull(tokenCode, "retokenCode不得为空");
        List<OauthRefreshTokenDetail> reTokens = reTokenSvc
                .getListByQuery(QueryBuilders.termQuery("tokenCode.keyword", tokenCode));
        return reTokens != null && reTokens.size() > 0 ? reTokens.get(0) : null;
    }

    @Override
    public void delRefreshTokenByCode(String tokenCode) {
        // TODO Auto-generated method stub
        reTokenSvc.deleteByQuery(QueryBuilders.termQuery("tokenCode.keyword", tokenCode), true);
    }

    @Override
    public OauthTokenDetail getTokenDetailByReTokenCode(String reTokenCode) {
        List<OauthTokenDetail> tokens = tokenSvc
                .getListByQuery(QueryBuilders.termQuery("refreshToken.keyword", reTokenCode));
        return tokens != null && tokens.size() > 0 ? tokens.get(0) : null;
    }
}
