package com.uinnova.product.vmdb.provider.ci.bean;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.vmdb.comm.model.classTpl.CcCiAttrDefTpl;
import com.uinnova.product.vmdb.comm.model.classTpl.CcCiClassTpl;
import com.uinnova.product.vmdb.comm.model.classTpl.CcFixAttrMappingTpl;

import java.io.Serializable;
import java.util.List;

public class CcCiClassTplInfo implements Serializable {
	private static final long serialVersionUID = 1L;

	
	
	@Comment("模板CI分类")
	private CcCiClassTpl classTpl;
	
	
	@Comment("模板属性定义")
	private List<CcCiAttrDefTpl> attrDefTpls;

	
	@Comment("模板常柱属性映射对象")
	private CcFixAttrMappingTpl attrMappingTpl;


	public CcCiClassTpl getClassTpl() {
		return classTpl;
	}


	public void setClassTpl(CcCiClassTpl classTpl) {
		this.classTpl = classTpl;
	}


	public List<CcCiAttrDefTpl> getAttrDefTpls() {
		return attrDefTpls;
	}


	public void setAttrDefTpls(List<CcCiAttrDefTpl> attrDefTpls) {
		this.attrDefTpls = attrDefTpls;
	}


	public CcFixAttrMappingTpl getAttrMappingTpl() {
		return attrMappingTpl;
	}


	public void setAttrMappingTpl(CcFixAttrMappingTpl attrMappingTpl) {
		this.attrMappingTpl = attrMappingTpl;
	}
	
	
	
	
	
}
