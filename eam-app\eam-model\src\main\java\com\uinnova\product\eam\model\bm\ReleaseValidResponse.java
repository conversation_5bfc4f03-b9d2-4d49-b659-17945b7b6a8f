package com.uinnova.product.eam.model.bm;

import lombok.Data;

import java.util.List;

/**
 * 发布校验响应类
 * <AUTHOR>
 */
@Data
public class ReleaseValidResponse {

    private int resultCode = 1;

    private ValidType validType = ValidType.PASS;

    private IResponseData responseData;


    public enum ValidType{
        /**
         * 通过
         */
        PASS,
        /**
         * ci数据
         */
        CI_DATA,
        /**
         * 视图版本
         */
        DIAGRAM_VERSION
    }

    public interface IResponseData{

    }

    @Data
    public static class CiData implements IResponseData {
        private List<CiInfo> ciInfoList;
        private List<String> ciCodes;
    }

    @Data
    public static class DiagramVersionData implements IResponseData {
        private List<DiagramVersionInfo> versionData;
    }

    @Data
    public static class CiInfo {
        private String diagramName;
        private List<CiAttrInfo> checkData;
    }
    @Data
    public static class CiAttrInfo {
        private String className;
        private String ciName;
        private String missAttr;

    }
    @Data
    public static class DiagramVersionInfo {
        private String diagramName;
        private String privateVersion;
        private String designVersion;
    }

}
