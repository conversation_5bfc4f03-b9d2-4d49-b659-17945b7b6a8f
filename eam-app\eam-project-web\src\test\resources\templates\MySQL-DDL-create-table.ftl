<#list data as entity>
DROP TABLE IF EXISTS `${entity.key}`;
CREATE TABLE `${entity.key}`(
<#if entity.attrList?? && (entity.attrList?size>0)>
<#list entity.attrList as item>
    `${item.name}` ${item.type}<#if item.length??>(${item.length}<#if item.precision??>${','}${item.precision}</#if>)</#if><#if item.notEmpty> NOT NULL</#if><#if item.defaultVal??>  DEFAULT '${item.defaultVal}'</#if>  COMMENT '${item.comment}' <#sep>,
</#list>
</#if>
<#if entity.pkList?? && (entity.pkList?size>0)>
,${'\n'}${'\t'}PRIMARY KEY (<#list entity.pkList as item>`${item}`<#sep>,</#list>)
</#if>
) COMMENT = '${entity.name}';


</#list>