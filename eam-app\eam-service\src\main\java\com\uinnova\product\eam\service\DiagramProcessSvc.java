package com.uinnova.product.eam.service;


import com.uinnova.product.eam.comm.model.es.DiagramApproveRlt;
import com.uinnova.product.eam.model.bm.CategoryNode;
import com.uinnova.product.eam.model.dto.ApproveParam;
import com.uinnova.product.eam.model.enums.AssetType;

import java.util.List;
import java.util.Map;

public interface DiagramProcessSvc {

    /**
     *  单张视图提交审批
     * @param diagramId 视图ID
     * @param dirId 发布位置
     * @param desc 发布描述
     * @param modelDirIds 模型发布目录参数
     * @param isChildProcess 当前提交流程是否属于子流程
     * @return 流程信息
     */
    ApproveParam submit(String diagramId, Long dirId, String desc, List<Long> modelDirIds, Boolean isChildProcess, AssetType assetType);

    ApproveParam submit(String diagramId, Long dirId, String desc, List<Long> modelDirIds, Boolean isChildProcess, AssetType assetType, String taskId);

    /**
     *  校验发布目录是否存在
     * @param businessKey 业务主键
     * @param processDefinitionKey 流程定义
     * @return
     */
    Boolean isExistDir(String businessKey, String processDefinitionKey);

    /**
     *  作废流程
     * @param processInstanceId 流程示例
     * @param reason 作废原因
     * @param businessKey 视图ID
     */
    void terminateProcess(String processInstanceId, String reason, String businessKey);

    /**
     *  根据 流程businessKey 获取模型的审批信息
     * @param businessKey 流程 businessKey
     * @return
     */
    CategoryNode queryApproveModelInfoByBusinessKey(String businessKey);

    /**
     *  获取审批中的发布位置信息
     * @param businessKey
     * @param processDefinitionKey
     */
    Map<String, Object> queryApprovePath(String businessKey, String processDefinitionKey);

    /**
     *  流程中视图提交审批
     * @param businessKey
     * @param processDefinitionKey
     * @param dirId
     * @param desc
     * @param taskId
     */
    void pushApproveAsset(String businessKey, String processDefinitionKey, Long dirId, String desc, String taskId);

    /**
     *  检查检出数据本地关联数据状态
     * @param dirId 资产仓库检出目录ID
     * @param diagramId 资产仓库检出视图ID
     * @return true-存在流程中 false-未在流程中
     */
    Boolean checkLocalStatus(Long dirId, String diagramId);

    /**
     *  回滚方案子流程审批状态
     * @param planBusinessKey 方案流程businessKey
     * @param rollBackStatus 回滚到的状态 0-正常 2-审批中 1-驳回
     * @return
     */
    Boolean rollBackApproveData(String planBusinessKey, Integer rollBackStatus);

    /**
     *  根据父流程或子流程key获取流程关联数据
     * @param planKey 父流程 key
     * @param modelKey 子流程 key
     * @param rootDirId 模型目录ID
     * @return 流程关联数据
     */
    List<DiagramApproveRlt> queryProcessRltByPlanOrModelKey(String planKey, String modelKey, Long rootDirId);

    /**
     *  根据审批视图查询流程关联信息
     * @param diagramId
     * @param ownerCode
     * @return
     */
    List<DiagramApproveRlt> queryProcessRltByRootDiagramId(String diagramId, String ownerCode);

    /**
     *  手动调整审批关联表数据及视图状态
     * @param businessKeys
     * @return
     */
    Boolean manualChangeRltAndDiagramStatusInfoByKeys(List<Long> businessKeys, Integer flowStatus);

    /**
     *  根据 businessKey 和 processDefinitionKey 查询资产数据状态
     * @param businessKey
     * @param processDefinitionKey
     * @return
     */
    Boolean selectDataStatus(String businessKey, String processDefinitionKey);
}
