package com.uinnova.product.eam.feign.server.web.controller;

import com.uinnova.product.eam.base.model.FileResourceMeta;
import com.uinnova.product.eam.base.model.ResourceInfo;
import com.uinnova.product.eam.feign.FeignConst;
import com.uinnova.product.eam.feign.client.EamResourceClient;
import com.uinnova.product.eam.model.vo.DefaultFileVo;
import com.uinnova.product.eam.service.resource.IEamResourceSvc;
import com.uino.bean.permission.base.SysUser;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;
import java.util.List;

@Controller
@RequestMapping(FeignConst.RESOURCE_PATH)
public class EamResourceController implements EamResourceClient {

    @Resource
    private IEamResourceSvc eamResourceSvc;

    @Override
    public List<ResourceInfo> upload(MultipartFile[] files, SysUser sysUser) {
        return eamResourceSvc.upload(files, sysUser);
    }

    @Override
    public List<FileResourceMeta> download(List<Long> ids) {
        return eamResourceSvc.download(ids);
    }

    @Override
    public List<ResourceInfo> uploadFileByType(MultipartFile[] files, Integer type) {
        return eamResourceSvc.uploadFileByType(files, type);
    }

    @Override
    public Integer deleteResource(Long id) {
        return eamResourceSvc.deleteResource(id);
    }

    @Override
    public List<DefaultFileVo> getImages(Integer type) {
        return eamResourceSvc.getImages(type);
    }
}
