<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:tx="http://www.springframework.org/schema/tx"
       xmlns:aop="http://www.springframework.org/schema/aop" xmlns:p="http://www.springframework.org/schema/p"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:util="http://www.springframework.org/schema/util"
       xmlns:mvc="http://www.springframework.org/schema/mvc"
       xsi:schemaLocation="
		  http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
		  http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd
		  http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.0.xsd
          http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-3.0.xsd
          http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util-3.0.xsd
          http://www.springframework.org/schema/mvc http://www.springframework.org/schema/mvc/spring-mvc-3.0.xsd">
    <!--	 数据库用户名和密码加密-->
    <bean id="propertyConfigurer" class="com.uino.tarsier.tarsiercom.util.PropertyPlaceholderConfigurerExt">
        <property name="locations">
            <list>
                <value>classpath:application-local.properties</value>
                <value>classpath:application-minor.properties</value>
            </list>
        </property>
        <property name="fileEncoding" value="UTF-8"/>
        <property name="order" value="1"/>
    </bean>

    <import resource="classpath:spring/spring-impl-iams.xml"/>
    <import resource="classpath:spring-ds.xml"/>

    <!--common-->
    <bean class="com.uinnova.product.vmdb.comm.dao.impl.CommDaoImpl">
        <property name="sqlSessionTemplate" ref="sqlSession"/>
    </bean>
</beans>


