package com.uino.web.cmdb.mvc;

import java.util.List;
import java.util.Set;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import com.uino.api.client.cmdb.ICIClassApiSvc;
import com.uino.bean.cmdb.query.ESShow3dAttributeDto;
import com.uino.bean.cmdb.query.ESShowDocumentAttributeDto;
import com.uino.bean.permission.base.SysUser;
import com.uino.util.sys.SysUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import com.binary.core.io.Resource;
import com.binary.jdbc.Page;
import com.binary.json.JSON;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClassDir;
import com.uino.bean.cmdb.base.CcImage;
import com.uino.bean.cmdb.business.ImageCount;
import com.uino.bean.cmdb.business.ImportDirMessage;
import com.uino.bean.cmdb.business.ImportResultMessage;
import com.uino.bean.cmdb.query.ESSearchImageBean;
import com.uino.bean.permission.query.SearchKeywordBean;
import com.uino.init.api.ApiResult;
import com.uino.init.api.ApiVersion;
import com.uino.api.client.cmdb.IImageApiSvc;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@ApiVersion(1)
@Api(value = "图标服务", tags = { "图标服务" })
@RestController
@RequestMapping("/cmdb/image")
public class ImageMvc {

    @Autowired
    private IImageApiSvc svc;

    @Autowired
    private ICIClassApiSvc iciClassApiSvc;

    /** 根据id查询图标 */
    @ApiOperation("根据id查询图标信息")
    @PostMapping("/queryImageById")
    @ModDesc(desc = "根据id查询图标信息", pDesc = "图标id", pType = Long.class, rDesc = "图标信息", rType = CcImage.class)
    public ApiResult<CcImage> queryImageById(HttpServletRequest request, HttpServletResponse response, @RequestBody String body)
            throws Exception {
        Long id = Long.parseLong(body.trim());
        return ApiResult.ok(this).data(svc.queryImageById(id));
    }

    /** 不分页查询图标目录 */
    @CrossOrigin(origins = {})
    @ApiOperation("查询图标文件夹列表")
    @PostMapping("/queryImageDirList")
    @ModDesc(desc = "查询图标文件夹列表", pDesc = "无", rDesc = "图标文件夹列表", rType = List.class, rcType = ImageCount.class)
	public ApiResult<List<ImageCount>> queryImageDirList(HttpServletRequest request, HttpServletResponse response,
			@RequestBody(required = false) CCcCiClassDir cdt) throws Exception {
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        return ApiResult.ok(this).data(svc.queryImageDirList(currentUserInfo.getDomainId(), cdt));
    }

    /** 分页查询Image数据 */
    @ApiOperation("分页查询图标数据")
    @PostMapping("/queryImagePage")
    @ModDesc(desc = "分页查询图标数据", pDesc = "查询条件", pType = ESSearchImageBean.class, rDesc = "分页查询结果", rType = Page.class, rcType = CcImage.class)
    public ApiResult<Page<CcImage>> queryImagePage(HttpServletRequest request, HttpServletResponse response,
            @RequestBody ESSearchImageBean bean) {
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        bean.setDomainId(currentUserInfo.getDomainId());
        return ApiResult.ok(this).data(svc.queryImagePage(bean));
    }

    /**
     * 上传图标，必须是ZIP格式
     * 
     * @param file
     * @param request
     * @param response
     */
    @ApiOperation("上传图标文件，必须是ZIP格式")
    @PostMapping("/importZipImage")
    @ModDesc(desc = "上传图标文件(必须是zip格式,且以压缩包中文件夹作为图标目录,文件夹下图标将被导入对应目录下)", pDesc = "图标zip文件", pType = MultipartFile.class, rDesc = "导入明细", rType = ImportResultMessage.class)
	public ApiResult<ImportResultMessage> importZipImage(@RequestParam(name = "file") MultipartFile file,
			@RequestParam(name = "sourceType", required = false) Integer sourceType, HttpServletRequest request,
            HttpServletResponse response) {
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        return ApiResult.ok(this).data(svc.importZipImage(currentUserInfo.getDomainId(), sourceType,file));
    }

    /**
     * 导入图标
     * 
     * @param file
     * @return
     */
    @ApiOperation("单个添加图标")
    @PostMapping("/importImage")
    @ModDesc(desc = "单个添加图标", pDesc = "图标文件及对应文件夹id", rDesc = "操作是否成功", rType = Boolean.class)
    public ApiResult<Boolean> importImage(@RequestParam(name = "file") MultipartFile file, @RequestParam(name = "dirId") Long dirId,
            HttpServletRequest request, HttpServletResponse response) {
        return ApiResult.ok(this).data(svc.importImage(dirId, file));
    }

    /**
     * 批量导入图标
     * 
     * @param files
     * @param dirId
     * @param request
     * @param response
     */
    @ApiOperation("批量添加图标")
    @PostMapping("/importImages")
    @ModDesc(desc = "批量添加图标", pDesc = "图标文件数组及对应文件夹id", rDesc = "添加明细", rType = ImportDirMessage.class)
    public ApiResult<ImportDirMessage> importImages(@RequestParam(name = "file") MultipartFile[] files,
            @RequestParam(name = "dirId") Long dirId, HttpServletRequest request, HttpServletResponse response) {
        return ApiResult.ok(this).data(svc.importImages(dirId, files));
    }

    /**
     * 替换图标
     * 
     * @param imageId
     * @param file
     * @return
     */
    @ApiOperation("2D图标替换")
    @PostMapping("/replaceImage")
    @ModDesc(desc = "2D图标替换", pDesc = "图标文件及被替换的图标id", rDesc = "操作是否成功", rType = Boolean.class)
    public ApiResult<Boolean> replaceImage(@RequestParam(name = "file") MultipartFile file, @RequestParam(name = "imgId") Long imgId,
            HttpServletRequest request, HttpServletResponse response) {
        return ApiResult.ok(this).data(svc.replaceImage(imgId, file));
    }

    @ApiOperation("2D映射3D图标")
    @PostMapping("/updateImageRlt")
    @ModDesc(desc = "2D映射3D图标", pDesc = "2D图标信息", pType = CcImage.class, rDesc = "图标id", rType = Long.class)
    public ApiResult<Long> updateImageRlt(HttpServletRequest request, HttpServletResponse response, @RequestBody String body) {
        CcImage param = JSON.toObject(body, CcImage.class);
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        param.setDomainId(currentUserInfo.getDomainId());
        Long result = svc.updateImageRlt(param);
        return ApiResult.ok(this).data(result);
    }

    /**
     * 删除图标
     * 
     * @param image
     * @return
     */
    @ApiOperation("删除2D图标")
    @PostMapping("/deleteImage")
    @ModDesc(desc = "删除2D图标", pDesc = "要删除的图标信息", pType = CcImage.class, rDesc = "操作是否成功", rType = Boolean.class)
    public ApiResult<Boolean> deleteImage(@RequestBody CcImage image, HttpServletRequest request, HttpServletResponse response) {
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        image.setDomainId(currentUserInfo.getDomainId());
        return ApiResult.ok(this).data(svc.deleteImage(image));
    }

    /**
     * 删除目录下的图标
     * 
     * @param dirId
     * @return
     */
    @ApiOperation("删除图标文件夹及其下图标")
    @PostMapping("/deleteDirImage")
    @ModDesc(desc = "删除图标文件夹里及其下图标", pDesc = "文件夹id", pType = Long.class, rDesc = "操作是否成功", rType = Boolean.class)
    public ApiResult<Boolean> deleteDirImage(@RequestBody Long dirId, HttpServletRequest request, HttpServletResponse response) {
        return ApiResult.ok(this).data(svc.deleteDirImage(dirId));
    }

    /**
     * 导出指定目录下图标
     * 
     * @param dirId
     * @param request
     * @param response
     */

    @ApiOperation(value="导出指定文件夹下图标",notes="- 导出指定文件夹下图标 \r\n - 传入参数文件夹id集合 \r\n - 返回值类型: **byte[]**")
    @RequestMapping(value = {"/exportImageZipByDirIds"},method = RequestMethod.POST)
    @ModDesc(desc = "导出指定文件夹下图标", pDesc = "文件夹id集合", pType = Set.class, pcType = Long.class, rDesc = "图标导出文件", rType = ResponseEntity.class)
    public ResponseEntity<byte[]> exportImageZipByDirId(@RequestBody Set<Long> dirIds, HttpServletRequest request,
            HttpServletResponse response) {
        return svc.exportImageZipByDirIds(dirIds);
//        ControllerUtils.returnR(request, response, file);
        // ControllerUtils.returnResource(request, response, file);
        // ControllerUtils.returnResource(request, response, file, null, true,
        // file.getName());
    }

    /**
     * 查询置顶图标
     * 
     * @param request
     * @param response
     */
    @ApiOperation("查询置顶图标")
    @PostMapping("/queryTopImage")
    @ModDesc(desc = "查询置顶图标", pDesc = "查询条件", pType = SearchKeywordBean.class, rDesc = "置顶图标集合", rType = List.class, rcType = CcImage.class)
    public ApiResult<List<CcImage>> queryTopImage(@RequestBody SearchKeywordBean bean, HttpServletRequest request,
            HttpServletResponse response) {
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        bean.setDomainId(currentUserInfo.getDomainId());
        return ApiResult.ok(this).data(svc.queryTopImage(bean));
    }

    /**
     * 上传图标，必须是ZIP格式
     * 
     * @param file
     * @param request
     * @param response
     */
    @ApiOperation("导入3D图标")
    @PostMapping("/import3DZipImage")
    @ModDesc(desc = "导入3D图标(必须是zip格式,且以Tarsier 3D文件夹名为3D图标标识,每个图标需包含对应名称的文件夹)", pDesc = "图标文件", pType = MultipartFile.class, rDesc = "导入明细", rType = ImportDirMessage.class)
	public ApiResult<ImportDirMessage> import3DZipImage(@RequestParam(name = "file") MultipartFile file,
			@RequestParam(name = "dirId") Long dirId, HttpServletRequest request,
            HttpServletResponse response) {
        return ApiResult.ok(this).data(svc.import3DZipImage(file, dirId,false));
    }

    /**
     * 替换图标
     * 
     * @param imageId
     * @param file
     * @return
     */
    @ApiOperation("3D图标替换(必须是zip格式)")
    @PostMapping("/replace3DImage")
    @ModDesc(desc = "3D图标替换(必须是zip格式)", pDesc = "图标zip文件及被替换的图标id", rDesc = "操作是否成功", rType = Boolean.class)
    public ApiResult<Boolean> replace3DImage(@RequestParam(name = "file") MultipartFile file,
            @RequestParam(name = "imgId") Long imgId, HttpServletRequest request, HttpServletResponse response) {
       boolean flag=svc.replace3DImage(imgId, file);
        //ControllerUtils.returnJson(request, response, svc.replace3DImage(imgId, file));
        return ApiResult.ok(this).data(flag);
    }

    /**
     * 删除图标
     * 
     * @param image
     * @return
     */
    @ApiOperation("删除3D图标")
    @PostMapping("/delete3DImage")
    @ModDesc(desc = "删除3D图标", pDesc = "要删除的图标信息", pType = CcImage.class, rDesc = "操作是否成功", rType = Boolean.class)
    public ApiResult<Boolean> delete3DImage(@RequestBody CcImage image, HttpServletRequest request, HttpServletResponse response) {
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        image.setDomainId(currentUserInfo.getDomainId());
        return ApiResult.ok(this).data(svc.delete3DImage(image));
    }

	/**
	 * 删除图标
	 * 
	 * @param image
	 * @return
	 */

	@PostMapping("/downloadImageResource")
	@ModDesc(desc = "下载图标资源", pDesc = "要下载的资源id", pType = List.class, pcType = Long.class, rDesc = "图标文件", rType = Resource.class)
	@ApiOperation(value="下载图标资源",notes="- 下载图标资源 \r\n - 传入参数为id集合 \r\n - 返回值为:**byte[]**")
	public  ResponseEntity<byte[]> downloadImageResource(@RequestBody List<Long> ids, HttpServletRequest request,
			HttpServletResponse response) {
	    return svc.downloadImageResource(ids);

	}

	@PostMapping(value = "/getVedioDefaultIcon")
	@ApiOperation(value = "获取视频资源默认图标")
	public ApiResult<String> getVedioDefaultIcon() {
		return ApiResult.ok(this).data(iciClassApiSvc.getHttpResourceSpace() + "/122/defaultIcon/default_vedio_icon.png");
	}

    @PostMapping(value = "/queryImageByPath")
    @ApiOperation(value = "根据资源路径获取资源")
    public ApiResult<Page<CcImage>> queryImageByPath(@RequestBody ESSearchImageBean bean){
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        bean.setDomainId(currentUserInfo.getDomainId());
        return ApiResult.ok(this).data(svc.queryImageByPath(bean));
    }

    @PostMapping(value = "/isShowDocumentAttribute")
    @ApiOperation(value = "创建分类是否增加文档属性")
    public ApiResult<ESShowDocumentAttributeDto> isShowDocumentAttribute() {
        ESShowDocumentAttributeDto esShowDocumentAttributeDto = new ESShowDocumentAttributeDto();
        esShowDocumentAttributeDto.setShowDocumentAttribute(svc.isShowDocumentAttribute());
        return ApiResult.ok(this).data(esShowDocumentAttributeDto);
    }
}
