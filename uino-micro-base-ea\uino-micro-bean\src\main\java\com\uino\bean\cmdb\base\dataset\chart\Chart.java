package com.uino.bean.cmdb.base.dataset.chart;

/**
 * 图表
 *
 * <AUTHOR>
 * @data 2019/12/26 10:46.
 */
public class Chart {
    //图例类型
    private int chartType;
    //计算类型
    private int statisticalType;

    //系列轴
    private String seriesPageNodeId;

    private String seriesAttrName;

    //取值轴
    private String valuePageNodeId;

    private String valueAttrName;

    //堆叠系列或饼图
    private String legendPageNodeId;

    private String legendAttrName;

    public int getChartType() {
        return chartType;
    }

    public void setChartType(int chartType) {
        this.chartType = chartType;
    }

    public int getStatisticalType() {
        return statisticalType;
    }

    public void setStatisticalType(int statisticalType) {
        this.statisticalType = statisticalType;
    }

    public String getSeriesPageNodeId() {
        return seriesPageNodeId;
    }

    public void setSeriesPageNodeId(String seriesPageNodeId) {
        this.seriesPageNodeId = seriesPageNodeId;
    }

    public String getSeriesAttrName() {
        return seriesAttrName;
    }

    public void setSeriesAttrName(String seriesAttrName) {
        this.seriesAttrName = seriesAttrName;
    }

    public String getValuePageNodeId() {
        return valuePageNodeId;
    }

    public void setValuePageNodeId(String valuePageNodeId) {
        this.valuePageNodeId = valuePageNodeId;
    }

    public String getValueAttrName() {
        return valueAttrName;
    }

    public void setValueAttrName(String valueAttrName) {
        this.valueAttrName = valueAttrName;
    }

    public String getLegendPageNodeId() {
        return legendPageNodeId;
    }

    public void setLegendPageNodeId(String legendPageNodeId) {
        this.legendPageNodeId = legendPageNodeId;
    }

    public String getLegendAttrName() {
        return legendAttrName;
    }

    public void setLegendAttrName(String legendAttrName) {
        this.legendAttrName = legendAttrName;
    }
}
