package com.uinnova.product.vmdb.comm.model.rule;

import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;

/**
 * 
 * <AUTHOR>
 *
 */
@Comment("CI标签目录表[CC_CI_TAG_DIR]")
public class CcCiTagDir implements EntityBean {
    private static final long serialVersionUID = 1L;

    @Comment("ID[ID]")
    private Long id;

    @Comment("目录名称[DIR_NAME]")
    private String dirName;

    @Comment("标签类型[TAG_TYPE]    1=权限标签 2=视图标签 3=数据标签")
    private Integer tagType;

    @Comment("上级目录ID[PARENT_ID]")
    private Long parentId;

    @Comment("分类层级级别[DIR_LVL]")
    private Integer dirLvl;

    @Comment("分类层级路径[DIR_PATH]    例：#1#2#7#")
    private String dirPath;

    @Comment("显示排序[ORDER_NO]")
    private Integer orderNo;

    @Comment("是否末级[IS_LEAF]    1=是 0=否")
    private Integer isLeaf;

    @Comment("目录图标[ICON]")
    private String icon;

    @Comment("目录描述[DIR_DESC]")
    private String dirDesc;

    @Comment("目录颜色[DIR_COLOR]")
    private String dirColor;

    @Comment("所属域[DOMAIN_ID]")
    private Long domainId;

    @Comment("数据状态[DATA_STATUS]    0=删除，1=正常")
    private Integer dataStatus;

    @Comment("创建人[CREATOR]")
    private String creator;

    @Comment("修改人[MODIFIER]")
    private String modifier;

    @Comment("创建时间[CREATE_TIME]    yyyyMMddHHmmss")
    private Long createTime;

    @Comment("更新时间[MODIFY_TIME]    yyyyMMddHHmmss")
    private Long modifyTime;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getDirName() {
        return this.dirName;
    }

    public void setDirName(String dirName) {
        this.dirName = dirName;
    }

    public Integer getTagType() {
        return this.tagType;
    }

    public void setTagType(Integer tagType) {
        this.tagType = tagType;
    }

    public Long getParentId() {
        return this.parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public Integer getDirLvl() {
        return this.dirLvl;
    }

    public void setDirLvl(Integer dirLvl) {
        this.dirLvl = dirLvl;
    }

    public String getDirPath() {
        return this.dirPath;
    }

    public void setDirPath(String dirPath) {
        this.dirPath = dirPath;
    }

    public Integer getOrderNo() {
        return this.orderNo;
    }

    public void setOrderNo(Integer orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getIsLeaf() {
        return this.isLeaf;
    }

    public void setIsLeaf(Integer isLeaf) {
        this.isLeaf = isLeaf;
    }

    public String getIcon() {
        return this.icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getDirDesc() {
        return this.dirDesc;
    }

    public void setDirDesc(String dirDesc) {
        this.dirDesc = dirDesc;
    }

    public String getDirColor() {
        return this.dirColor;
    }

    public void setDirColor(String dirColor) {
        this.dirColor = dirColor;
    }

    public Long getDomainId() {
        return this.domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }

    public Integer getDataStatus() {
        return this.dataStatus;
    }

    public void setDataStatus(Integer dataStatus) {
        this.dataStatus = dataStatus;
    }

    public String getCreator() {
        return this.creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getModifier() {
        return this.modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public Long getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getModifyTime() {
        return this.modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }

}
