package com.uino.bean.cmdb.query;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.vmdb.comm.model.rlt.CCcCiRlt;

import com.uino.bean.cmdb.base.LibType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 各属性之间的关系是AND，andAttrs和orAttrs属性是条件只能用其中一个
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value="关系查询类",description = "关系查询信息")
public class ESRltSearchBean implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "页码", example = "1")
    @Builder.Default
    private int pageNum = 1;

    @ApiModelProperty(value ="所属领域Id",example="0L" )
    @Builder.Default
    private long domainId = 0L;

    @ApiModelProperty(value = "每页数量", example = "20")
    @Builder.Default
    private int pageSize = 20;

	@ApiModelProperty(value = "CI的id,查询以该CI为源或目标的关系")
	private Long ciId;

    /**
     * 查询条件，会与其他条件平级and
     */
    @ApiModelProperty(value="查询条件，会与其他条件平级")
    private CCcCiRlt cdt;

    /**
     * 分类code
     */
    @ApiModelProperty(value="分类code",example="oiowoje")
    private String classCode;

    /**
     * 关键字全文检索
     */
    @ApiModelProperty(value="关键字全文检索")
    @Builder.Default
    private List<String> keywords = new ArrayList<String>();

    /**
     * 关系codes
     */
    @ApiModelProperty(value="关系codes")
    @Builder.Default
    private Set<String> rltCodes = new HashSet<>();

    /**
     * 关系绝对唯一codes
     */
    @ApiModelProperty(value="关系绝对唯一codes")
    @Builder.Default
    private Set<String> rltUniqueCodes = new HashSet<>();

    /**
     * 源CI的ID
     */
    @ApiModelProperty(value="源对象的ID")
    @Builder.Default
    private List<Long> sourceCiIds = new ArrayList<Long>();

    /**
     * 源ci的cicodes
     */
    @ApiModelProperty(value="源对象的cicodes")
    @Builder.Default
    private Set<String> sourceCiCodes = new HashSet<>();

    /**
     * 源ci业务主键
     */
    @ApiModelProperty(value = "源ci业务主键, Set<String>")
    @Builder.Default
    private Set<String> sourceCiPrimaryKeys = new HashSet<>();

    /**
     * 目标CI的ID
     */
    @ApiModelProperty(value="目标对象的ID")
    @Builder.Default
    private List<Long> targetCiIds = new ArrayList<Long>();

    /**
     * 目标ci的cicodes
     */
    @ApiModelProperty(value="目标对象的cicodes")
    @Builder.Default
    private Set<String> targetCiCodes = new HashSet<>();

    /**
     * 目标ci业务主键
     */
    @ApiModelProperty(value = "目标ci业务主键, Set<String>")
    @Builder.Default
    private Set<String> targetCiPrimaryKeys = new HashSet<>();

    /**
     * 关系分类的ID
     */
    @ApiModelProperty(value = "关系分类的ID, List<Long>")
    @Builder.Default
    private List<Long> rltClassIds = new ArrayList<Long>();

    /**
     * 源CI分类
     */
    @ApiModelProperty(value = "源CI分类, List<Long>")
    @Builder.Default
    private List<Long> sourceClassIds = new ArrayList<Long>();

    /**
     * 目标CI分类
     */
    @ApiModelProperty(value = "目标CI分类, List<Long>")
    @Builder.Default
    private List<Long> targetClassIds = new ArrayList<Long>();
    @ApiModelProperty(value="关系查询层级")
    @Builder.Default
    private List<Long> ciRltLvl = new ArrayList<Long>();

    /**
     * 属性之间是AND关系
     */
    @ApiModelProperty(value="属性之间是AND关系")
    @Builder.Default
    private List<ESAttrBean> andAttrs = new ArrayList<ESAttrBean>();

    /**
     * 属性之间是OR关系
     */
    @ApiModelProperty(value="属性之间是OR关系")
    @Builder.Default
    private List<ESAttrBean> orAttrs = new ArrayList<ESAttrBean>();

    /**
     * 排序字段
     */
    @ApiModelProperty(value="排序字段",example="modifyTime")
    @Builder.Default
    private String sortField = "modifyTime";

    /**
     * 是否升序
     */
    @ApiModelProperty(value="是否升序",example = "true")
    @Builder.Default
    private Boolean isAsc = true;


    private String ownerCode;

    private LibType libType = LibType.BASELINE;

    /**
     * 是否检查元模型
     */
    private Boolean checkData = Boolean.FALSE;
    private Set<Long> notMathSourceClassIds;
    private Set<Long> notMatchTargetClassIds;

    // 中心ci 根据中心点ci过滤查询出的关系数据
    private List<Long> centerCiIds;

    @ApiModelProperty(value="范围查询 大于等于这个时间创建的数据")
    private String gteTime;

    @ApiModelProperty(value="范围查询 小于等于这个时间创建的数据")
    private String lteTime;

    @ApiModelProperty(value="是否是不合规模式 0 表示不合规查询 1表示全部查询")
    private Integer usage=1;

    @ApiModelProperty(value="关系id")
    private Set<Long> rltId;

    @ApiModelProperty(value="是否导出数据")
    private Boolean exportData = true;
}
