package com.uino.bean.sys.business;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;
import java.util.Set;

/**
 * 通知消息对象
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "通知消息对象", description = "通知消息对象")
public class NotifyData implements Serializable {

    private static final long serialVersionUID = -3266085107590927599L;

    @ApiModelProperty(value = "通知渠道ID", example = "27393335556218", required = true)
    private Long channelId;

    @ApiModelProperty(value = "通知方式", example = "EMAIL / HTTP", required = true)
    private String type;

    @ApiModelProperty(value = "通知标题", example = "通知标题", required = true)
    private String title;

    @ApiModelProperty(value = "通知内容", example = "通知内容", required = true)
    private String content;

    @ApiModelProperty(value = "通知使用的工单处理流程ID, 暂时不用", example = "通知使用的工单处理流程ID，暂时不用")
    private Long ticketFlowId;

    @ApiModelProperty(value = "通知信息对象（完整的告警信息等）", example = "通知信息对象（完整的告警信息等）")
    private JSONObject notifyObject;

    @ApiModelProperty(value = "通知人", example = "例如通知类型为邮件时，notifyAddress为邮箱地址集合")
    private Set<String> notifyAddress;

    @ApiModelProperty(value = "邮件附件信息", example = "<附件名称,附件文件地址>")
    private Map<String, String> mailAttachments;

}
