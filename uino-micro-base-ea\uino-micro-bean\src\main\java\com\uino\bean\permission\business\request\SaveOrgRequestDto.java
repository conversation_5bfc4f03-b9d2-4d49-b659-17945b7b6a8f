package com.uino.bean.permission.business.request;

import io.swagger.annotations.ApiModel;
import org.springframework.util.Assert;

import com.uino.bean.permission.base.SysOrg;
import com.uino.bean.permission.business.IValidDto;


/**
 * 持久化组织请求dto
 * 
 * <AUTHOR>
 *
 */
@ApiModel(value="持久化组织请求",description = "持久化组织请求")
public class SaveOrgRequestDto extends SysOrg implements IValidDto {

	private static final long serialVersionUID = 1L;

	@Override
	public void valid() {
		Assert.notNull(this.getParentOrgId(), "SAVE_ORG_PARNETID_NOEXIST");
		Assert.notNull(this.getOrgName(), "ORGNAME_NOT_NULL");
		Assert.notNull(this.getOrderNo(), "ORGNO_NOT_NULL");
	}

}
