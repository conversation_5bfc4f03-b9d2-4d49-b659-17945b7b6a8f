package com.uino.bean.express;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;
import java.util.Set;

@ApiModel(value = "表达式参数", description = "表达式格式")
@Data
public class ExpressDTO {

    @ApiModelProperty(value = "计算参数")
    private Map<String, Object> param;

    @ApiModelProperty(value = "表达式公式")
    private String express;

    @ApiModelProperty(value = "参数名称列表，用于表达式验证")
    private Set<String> paramNames;
}
