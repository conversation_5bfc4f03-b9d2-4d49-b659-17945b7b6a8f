package com.uinnova.product.eam.service.es;

import com.uinnova.product.eam.comm.model.es.AssetListAttrConf;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;

/**
 * 列表页配置
 * <AUTHOR>
 */
@Service
public class AssetListAttrConfDao extends AbstractESBaseDao<AssetListAttrConf, AssetListAttrConf> {
    @Override
    public String getIndex() {
        return "uino_eam_asset_list_attr_conf";
    }

    @Override
    public String getType() {
        return "uino_eam_asset_list_attr_conf";
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }
}
