package com.uinnova.product.vmdb.provider.quality.bean;

import com.uinnova.product.vmdb.comm.model.quality.CcCiQualityChart;
import com.uinnova.product.vmdb.comm.model.quality.CcCiQualityChartSeries;
import com.uinnova.product.vmdb.comm.model.quality.CcCiQualityRule;

import java.io.Serializable;
import java.util.List;

public class QualityChartInfo implements Serializable {
	private static final long serialVersionUID = 1L;
	
	
	/**
	 * 数据质量图表信息
	 */
	private CcCiQualityChart chart;
	
	
	/**
	 * 当前图标对应的规则信息
	 */
	private CcCiQualityRule rule;
	
	
	/**
	 * 数据质量图表对应的序列信息
	 */
	private List<CcCiQualityChartSeries> chartSeries;


	public CcCiQualityChart getChart() {
		return chart;
	}


	public void setChart(CcCiQualityChart chart) {
		this.chart = chart;
	}


	public CcCiQualityRule getRule() {
		return rule;
	}


	public void setRule(CcCiQualityRule rule) {
		this.rule = rule;
	}


	public List<CcCiQualityChartSeries> getChartSeries() {
		return chartSeries;
	}


	public void setChartSeries(List<CcCiQualityChartSeries> chartSeries) {
		this.chartSeries = chartSeries;
	}


	

}
