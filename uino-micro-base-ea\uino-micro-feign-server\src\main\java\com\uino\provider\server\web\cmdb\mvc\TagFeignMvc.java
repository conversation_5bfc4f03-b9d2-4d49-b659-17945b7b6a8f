package com.uino.provider.server.web.cmdb.mvc;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uino.service.cmdb.microservice.ITagSvc;
import com.uino.bean.cmdb.base.ESCITagInfo;
import com.uino.bean.cmdb.business.ClassNodeInfo;
import com.uino.bean.cmdb.query.ESAttrAggBean;
import com.uino.bean.cmdb.query.ESTagSearchBean;
import com.uino.provider.feign.cmdb.TagFeign;

/**
 * 
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("feign/tag")
public class TagFeignMvc implements TagFeign {

    @Autowired
    private ITagSvc tagSvc;

    @Override
    public Long saveOrUpdateCITagRule(ESCITagInfo tagInfo) {
        return tagSvc.saveOrUpdateCITagRule(tagInfo);
    }

    @Override
    public ESCITagInfo getCITagRuleById(Long id) {
        return tagSvc.getCITagRuleById(id);
    }

    @Override
    public List<ClassNodeInfo> getTagTree(Long domainId) {
        return tagSvc.getTagTree(domainId);
    }

    @Override
    public Page<CcCiInfo> getCIInfoListByTag(ESTagSearchBean bean) {
        return tagSvc.getCIInfoListByTag(bean);
    }

    @Override
    public Integer deleteById(Long tagId) {
        return tagSvc.deleteById(tagId);
    }

    @Override
    public Page<String> getAttrValuesBySearchBean(Long domainId, ESAttrAggBean searchBean) {
        return tagSvc.getAttrValuesBySearchBean(domainId, searchBean);
    }

    @Override
    public Boolean changeTagDir(ESCITagInfo tagInfo) {
        return tagSvc.changeTagDir(tagInfo);
    }

}
