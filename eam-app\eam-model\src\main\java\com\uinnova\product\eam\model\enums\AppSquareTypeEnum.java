package com.uinnova.product.eam.model.enums;

import lombok.Getter;

/**
 * 应用广场类型
 * <AUTHOR>
 */

@Getter
public enum AppSquareTypeEnum {

    /**
     * 架构全景
     */
    PANORAMA_ARCH("1"),
    /**
     * 专题分析
     */
    ANALYSIS("2"),
    /**
     * 架构全景-自动成图
     */
    PANORAMA_DRAW("3"),
    /**
     * 资产清单
     */
    ASSET_LIST("4"),
    /**
     * 架构资产
     */
    ASSET("5"),
    /**
     * 架构全景
     */
    PANORAMA("6");

    private final String type;

    AppSquareTypeEnum(String type) {
        this.type = type;
    }

}
