package com.uinnova.product.eam.model.dto;

import com.uinnova.product.eam.comm.model.es.EamCategory;
import lombok.Data;

import java.util.List;
import java.util.Map;


@Data
public class ModelCategoryInfo {

    private List<Long> modelIds;
    private Map<Long, Integer> modelRootLvlMap;
    private List<String> diagramIds;
    private Map<Long, Map<Integer, List<EamCategory>>> modelGroupByIdAndLvlMap;
    private List<String> ciCodes;

    public ModelCategoryInfo(List<Long> modelIds,
                             Map<Long, Integer> modelRootLvlMap,
                             List<String> diagramIds, Map<Long, Map<Integer, List<EamCategory>>> modelGroupByIdAndLvlMap, List<String> ciCodes) {
        this.modelIds = modelIds;
        this.modelRootLvlMap = modelRootLvlMap;
        this.diagramIds = diagramIds;
        this.modelGroupByIdAndLvlMap = modelGroupByIdAndLvlMap;
        this.ciCodes = ciCodes;
    }
}
