package com.uino.bean.cmdb.business;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import com.uinnova.product.vmdb.comm.util.CiExcelUtil;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Tolerate;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * 
 * <AUTHOR>
 *
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ImportSheetMessage {

    private String sheetName;

    private String className;

    private Set<String> classCodes;

    private Set<String> classNames;

    @Builder.Default
    private Integer totalNum = 0;

    @Builder.Default
    private Integer successNum = 0;

    @Builder.Default
    private Integer failNum = 0;

    @Builder.Default
    private Integer insertNum = 0;

    @Builder.Default
    private Integer updateNum = 0;

    @Builder.Default
    private Integer ignoreNum = 0;

    @Builder.Default
    private Integer dbFailNum = 0;

    private Collection<String> titles;

    private String detailUrl;

    @Builder.Default
    private List<ImportRowMessage> rowMessages = new ArrayList<>();

    private String errMsg;

    private List<String> sucessCIPks;

    @Tolerate
    public ImportSheetMessage(String sheetName) {
        this.sheetName = sheetName;
        this.className = CiExcelUtil.removeSheetSuffix(sheetName);
        this.rowMessages = new ArrayList<ImportRowMessage>();
    }

    public String getSheetName() {
        return sheetName == null ? className : sheetName;
    }
}
