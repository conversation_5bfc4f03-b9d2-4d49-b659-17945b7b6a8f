package com.uinnova.product.eam.model.dto;

import com.binary.core.util.BinaryUtils;
import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.model.enums.CategoryTypeEnum;
import com.uinnova.product.eam.model.enums.ModelProcessTypeEnum;
import lombok.Data;

import java.util.List;

/**
 *  视图审批参数
 */
@Data
public class ApproveParam {

    @Comment("审批数据所属目录类型 默认为我的空间根目录")
    private int approveDirType = CategoryTypeEnum.ROOT.val();
    @Comment("发布位置")
    private Long targetDirId;
    @Comment("发布描述")
    private String desc;
    @Comment("审批视图IDs")
    private List<Long> diagramIds;
    @Comment("审批视图加密IDs")
    private List<String> dEnergys;
    @Comment("视图所属人标识")
    private String ownerCode;

    // 普通视图二级审批
    @Comment("视图所属制品类型")
    private Long viewType;
    @Comment("审批视图名称")
    private String diagramName;
    @Comment("审批视图ID")
    private Long diagramId;
    @Comment("审批视图加密")
    private String dEnergy;
    @Comment("视图的流程状态")
    private int flowStatus = 0;

    // 模型数据一级审批
    @Comment("审批模型顶级目录ID")
    private Long approveRootDirId;
    @Comment("模型发布转换后的发布参数--审批通过后使用这个参数进行发布")
    private List<Long> releaseParamDirList;
    @Comment("模型ID")
    private Long modelId;
    @Comment("模型工艺类型 1=数据(企业)建模 2=业务建模")
    private Integer modelType;
    @Comment("模型工艺类型 1=数据(企业)建模 2=业务建模")
    private String diagramType;
    @Comment("是否为模型的单图提交 true=是 false=否")
    private Boolean isSingle;


    // 开启流程的参数
    @Comment("流程定义")
    private String processDefinitionKey;
    @Comment("业务主键")
    private String businessKey;

    public String getDiagramType() {
        if (BinaryUtils.isEmpty(modelType)) {
            return null;
        }
        return modelType == 1 ? ModelProcessTypeEnum.ENTERPRISE.val() : ModelProcessTypeEnum.BUSINESS.val();
    }


    @Comment("流程实例名称")
    private String processInstanceName;

    @Comment("流程节点")
    private String taskId;
}

