package com.uinnova.product.vmdb.comm.model.excel;

import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;

/**
 * 
 * <AUTHOR>
 *
 */
@Comment("EXCEL表[CC_EXCEL]")
public class CCcExcel implements Condition {
    private static final long serialVersionUID = 1L;

    @Comment("ID[ID] operate-Equal[=]")
    private Long id;

    @Comment("ID[ID] operate-In[in]")
    private Long[] ids;

    @Comment("ID[ID] operate-GTEqual[>=]")
    private Long startId;

    @Comment("ID[ID] operate-LTEqual[<=]")
    private Long endId;

    @Comment("EXCEL名称[EXCEL_NAME] operate-Like[like]")
    private String excelName;

    @Comment("EXCEL全名[EXCEL_FULL_NAME] operate-Like[like]    EXCEL全名:目录名+图像名, 中间以|分隔")
    private String excelFullName;

    @Comment("所属目录[DIR_ID] operate-Equal[=]")
    private Long dirId;

    @Comment("所属目录[DIR_ID] operate-In[in]")
    private Long[] dirIds;

    @Comment("所属目录[DIR_ID] operate-GTEqual[>=]")
    private Long startDirId;

    @Comment("所属目录[DIR_ID] operate-LTEqual[<=]")
    private Long endDirId;

    @Comment("EXCEL描述[EXCEL_DESC] operate-Like[like]")
    private String excelDesc;

    @Comment("EXCEL格式[EXCEL_TYPE] operate-Equal[=]")
    private Integer excelType;

    @Comment("EXCEL格式[EXCEL_TYPE] operate-In[in]")
    private Integer[] excelTypes;

    @Comment("EXCEL格式[EXCEL_TYPE] operate-GTEqual[>=]")
    private Integer startExcelType;

    @Comment("EXCEL格式[EXCEL_TYPE] operate-LTEqual[<=]")
    private Integer endExcelType;

    @Comment("保存位置[EXCEL_PATH] operate-Like[like]")
    private String excelPath;

    @Comment("EXCEL范围[EXCEL_RANGE] operate-Equal[=]    EXCEL范围:1=公共 2=私有")
    private Integer excelRange;

    @Comment("EXCEL范围[EXCEL_RANGE] operate-In[in]    EXCEL范围:1=公共 2=私有")
    private Integer[] excelRanges;

    @Comment("EXCEL范围[EXCEL_RANGE] operate-GTEqual[>=]    EXCEL范围:1=公共 2=私有")
    private Integer startExcelRange;

    @Comment("EXCEL范围[EXCEL_RANGE] operate-LTEqual[<=]    EXCEL范围:1=公共 2=私有")
    private Integer endExcelRange;

    @Comment("EXCEL大小[EXCEL_SIZE] operate-Equal[=]    EXCEL大小:单位：byte")
    private Integer excelSize;

    @Comment("EXCEL大小[EXCEL_SIZE] operate-In[in]    EXCEL大小:单位：byte")
    private Integer[] excelSizes;

    @Comment("EXCEL大小[EXCEL_SIZE] operate-GTEqual[>=]    EXCEL大小:单位：byte")
    private Integer startExcelSize;

    @Comment("EXCEL大小[EXCEL_SIZE] operate-LTEqual[<=]    EXCEL大小:单位：byte")
    private Integer endExcelSize;

    @Comment("SHEET数[SHEET_COUNT] operate-Equal[=]")
    private Integer sheetCount;

    @Comment("SHEET数[SHEET_COUNT] operate-In[in]")
    private Integer[] sheetCounts;

    @Comment("SHEET数[SHEET_COUNT] operate-GTEqual[>=]")
    private Integer startSheetCount;

    @Comment("SHEET数[SHEET_COUNT] operate-LTEqual[<=]")
    private Integer endSheetCount;

    @Comment("上传人ID[UPOR_ID] operate-Equal[=]")
    private Long uporId;

    @Comment("上传人ID[UPOR_ID] operate-In[in]")
    private Long[] uporIds;

    @Comment("上传人ID[UPOR_ID] operate-GTEqual[>=]")
    private Long startUporId;

    @Comment("上传人ID[UPOR_ID] operate-LTEqual[<=]")
    private Long endUporId;

    @Comment("上传人姓名[UPOR_NAME] operate-Like[like]")
    private String uporName;

    @Comment("上传人姓名[UPOR_NAME] operate-Equal[=]")
    private String uporNameEqual;

    @Comment("上传人姓名[UPOR_NAME] operate-In[in]")
    private String[] uporNames;

    @Comment("上传时间[UP_TIME] operate-Equal[=]")
    private Long upTime;

    @Comment("上传时间[UP_TIME] operate-In[in]")
    private Long[] upTimes;

    @Comment("上传时间[UP_TIME] operate-GTEqual[>=]")
    private Long startUpTime;

    @Comment("上传时间[UP_TIME] operate-LTEqual[<=]")
    private Long endUpTime;

    @Comment("备用_1[CUSTOM_1] operate-Equal[=]")
    private Long custom1;

    @Comment("备用_1[CUSTOM_1] operate-In[in]")
    private Long[] custom1s;

    @Comment("备用_1[CUSTOM_1] operate-GTEqual[>=]")
    private Long startCustom1;

    @Comment("备用_1[CUSTOM_1] operate-LTEqual[<=]")
    private Long endCustom1;

    @Comment("备用_2[CUSTOM_2] operate-Equal[=]")
    private Long custom2;

    @Comment("备用_2[CUSTOM_2] operate-In[in]")
    private Long[] custom2s;

    @Comment("备用_2[CUSTOM_2] operate-GTEqual[>=]")
    private Long startCustom2;

    @Comment("备用_2[CUSTOM_2] operate-LTEqual[<=]")
    private Long endCustom2;

    @Comment("备用_3[CUSTOM_3] operate-Equal[=]")
    private Long custom3;

    @Comment("备用_3[CUSTOM_3] operate-In[in]")
    private Long[] custom3s;

    @Comment("备用_3[CUSTOM_3] operate-GTEqual[>=]")
    private Long startCustom3;

    @Comment("备用_3[CUSTOM_3] operate-LTEqual[<=]")
    private Long endCustom3;

    @Comment("备用_4[CUSTOM_4] operate-Like[like]")
    private String custom4;

    @Comment("备用_4[CUSTOM_4] operate-Equal[=]")
    private String custom4Equal;

    @Comment("备用_4[CUSTOM_4] operate-In[in]")
    private String[] custom4s;

    @Comment("备用_5[CUSTOM_5] operate-Like[like]")
    private String custom5;

    @Comment("备用_5[CUSTOM_5] operate-Equal[=]")
    private String custom5Equal;

    @Comment("备用_5[CUSTOM_5] operate-In[in]")
    private String[] custom5s;

    @Comment("备用_6[CUSTOM_6] operate-Like[like]")
    private String custom6;

    @Comment("备用_6[CUSTOM_6] operate-Equal[=]")
    private String custom6Equal;

    @Comment("备用_6[CUSTOM_6] operate-In[in]")
    private String[] custom6s;

    @Comment("搜索字段[SEARCH_FIELD] operate-Like[like]    搜索字段:图像名称")
    private String searchField;

    @Comment("排序字段[ORDER_NO] operate-Equal[=]")
    private Long orderNo;

    @Comment("排序字段[ORDER_NO] operate-In[in]")
    private Long[] orderNos;

    @Comment("排序字段[ORDER_NO] operate-GTEqual[>=]")
    private Long startOrderNo;

    @Comment("排序字段[ORDER_NO] operate-LTEqual[<=]")
    private Long endOrderNo;

    @Comment("所属域[DOMAIN_ID] operate-Equal[=]")
    private Long domainId;

    @Comment("所属域[DOMAIN_ID] operate-In[in]")
    private Long[] domainIds;

    @Comment("所属域[DOMAIN_ID] operate-GTEqual[>=]")
    private Long startDomainId;

    @Comment("所属域[DOMAIN_ID] operate-LTEqual[<=]")
    private Long endDomainId;

    @Comment("创建人[CREATOR] operate-Like[like]")
    private String creator;

    @Comment("创建人[CREATOR] operate-Equal[=]")
    private String creatorEqual;

    @Comment("创建人[CREATOR] operate-In[in]")
    private String[] creators;

    @Comment("修改人[MODIFIER] operate-Like[like]")
    private String modifier;

    @Comment("修改人[MODIFIER] operate-Equal[=]")
    private String modifierEqual;

    @Comment("修改人[MODIFIER] operate-In[in]")
    private String[] modifiers;

    @Comment("数据状态[DATA_STATUS] operate-Equal[=]    数据状态:数据状态：1-正常 0-删除")
    private Integer dataStatus;

    @Comment("数据状态[DATA_STATUS] operate-In[in]    数据状态:数据状态：1-正常 0-删除")
    private Integer[] dataStatuss;

    @Comment("数据状态[DATA_STATUS] operate-GTEqual[>=]    数据状态:数据状态：1-正常 0-删除")
    private Integer startDataStatus;

    @Comment("数据状态[DATA_STATUS] operate-LTEqual[<=]    数据状态:数据状态：1-正常 0-删除")
    private Integer endDataStatus;

    @Comment("创建时间[CREATE_TIME] operate-Equal[=]    创建时间:yyyyMMddHHmmss")
    private Long createTime;

    @Comment("创建时间[CREATE_TIME] operate-In[in]    创建时间:yyyyMMddHHmmss")
    private Long[] createTimes;

    @Comment("创建时间[CREATE_TIME] operate-GTEqual[>=]    创建时间:yyyyMMddHHmmss")
    private Long startCreateTime;

    @Comment("创建时间[CREATE_TIME] operate-LTEqual[<=]    创建时间:yyyyMMddHHmmss")
    private Long endCreateTime;

    @Comment("修改时间[MODIFY_TIME] operate-Equal[=]    修改时间:yyyyMMddHHmmss")
    private Long modifyTime;

    @Comment("修改时间[MODIFY_TIME] operate-In[in]    修改时间:yyyyMMddHHmmss")
    private Long[] modifyTimes;

    @Comment("修改时间[MODIFY_TIME] operate-GTEqual[>=]    修改时间:yyyyMMddHHmmss")
    private Long startModifyTime;

    @Comment("修改时间[MODIFY_TIME] operate-LTEqual[<=]    修改时间:yyyyMMddHHmmss")
    private Long endModifyTime;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long[] getIds() {
        return this.ids;
    }

    public void setIds(Long[] ids) {
        this.ids = ids;
    }

    public Long getStartId() {
        return this.startId;
    }

    public void setStartId(Long startId) {
        this.startId = startId;
    }

    public Long getEndId() {
        return this.endId;
    }

    public void setEndId(Long endId) {
        this.endId = endId;
    }

    public String getExcelName() {
        return this.excelName;
    }

    public void setExcelName(String excelName) {
        this.excelName = excelName;
    }

    public String getExcelFullName() {
        return this.excelFullName;
    }

    public void setExcelFullName(String excelFullName) {
        this.excelFullName = excelFullName;
    }

    public Long getDirId() {
        return this.dirId;
    }

    public void setDirId(Long dirId) {
        this.dirId = dirId;
    }

    public Long[] getDirIds() {
        return this.dirIds;
    }

    public void setDirIds(Long[] dirIds) {
        this.dirIds = dirIds;
    }

    public Long getStartDirId() {
        return this.startDirId;
    }

    public void setStartDirId(Long startDirId) {
        this.startDirId = startDirId;
    }

    public Long getEndDirId() {
        return this.endDirId;
    }

    public void setEndDirId(Long endDirId) {
        this.endDirId = endDirId;
    }

    public String getExcelDesc() {
        return this.excelDesc;
    }

    public void setExcelDesc(String excelDesc) {
        this.excelDesc = excelDesc;
    }

    public Integer getExcelType() {
        return this.excelType;
    }

    public void setExcelType(Integer excelType) {
        this.excelType = excelType;
    }

    public Integer[] getExcelTypes() {
        return this.excelTypes;
    }

    public void setExcelTypes(Integer[] excelTypes) {
        this.excelTypes = excelTypes;
    }

    public Integer getStartExcelType() {
        return this.startExcelType;
    }

    public void setStartExcelType(Integer startExcelType) {
        this.startExcelType = startExcelType;
    }

    public Integer getEndExcelType() {
        return this.endExcelType;
    }

    public void setEndExcelType(Integer endExcelType) {
        this.endExcelType = endExcelType;
    }

    public String getExcelPath() {
        return this.excelPath;
    }

    public void setExcelPath(String excelPath) {
        this.excelPath = excelPath;
    }

    public Integer getExcelRange() {
        return this.excelRange;
    }

    public void setExcelRange(Integer excelRange) {
        this.excelRange = excelRange;
    }

    public Integer[] getExcelRanges() {
        return this.excelRanges;
    }

    public void setExcelRanges(Integer[] excelRanges) {
        this.excelRanges = excelRanges;
    }

    public Integer getStartExcelRange() {
        return this.startExcelRange;
    }

    public void setStartExcelRange(Integer startExcelRange) {
        this.startExcelRange = startExcelRange;
    }

    public Integer getEndExcelRange() {
        return this.endExcelRange;
    }

    public void setEndExcelRange(Integer endExcelRange) {
        this.endExcelRange = endExcelRange;
    }

    public Integer getExcelSize() {
        return this.excelSize;
    }

    public void setExcelSize(Integer excelSize) {
        this.excelSize = excelSize;
    }

    public Integer[] getExcelSizes() {
        return this.excelSizes;
    }

    public void setExcelSizes(Integer[] excelSizes) {
        this.excelSizes = excelSizes;
    }

    public Integer getStartExcelSize() {
        return this.startExcelSize;
    }

    public void setStartExcelSize(Integer startExcelSize) {
        this.startExcelSize = startExcelSize;
    }

    public Integer getEndExcelSize() {
        return this.endExcelSize;
    }

    public void setEndExcelSize(Integer endExcelSize) {
        this.endExcelSize = endExcelSize;
    }

    public Integer getSheetCount() {
        return this.sheetCount;
    }

    public void setSheetCount(Integer sheetCount) {
        this.sheetCount = sheetCount;
    }

    public Integer[] getSheetCounts() {
        return this.sheetCounts;
    }

    public void setSheetCounts(Integer[] sheetCounts) {
        this.sheetCounts = sheetCounts;
    }

    public Integer getStartSheetCount() {
        return this.startSheetCount;
    }

    public void setStartSheetCount(Integer startSheetCount) {
        this.startSheetCount = startSheetCount;
    }

    public Integer getEndSheetCount() {
        return this.endSheetCount;
    }

    public void setEndSheetCount(Integer endSheetCount) {
        this.endSheetCount = endSheetCount;
    }

    public Long getUporId() {
        return this.uporId;
    }

    public void setUporId(Long uporId) {
        this.uporId = uporId;
    }

    public Long[] getUporIds() {
        return this.uporIds;
    }

    public void setUporIds(Long[] uporIds) {
        this.uporIds = uporIds;
    }

    public Long getStartUporId() {
        return this.startUporId;
    }

    public void setStartUporId(Long startUporId) {
        this.startUporId = startUporId;
    }

    public Long getEndUporId() {
        return this.endUporId;
    }

    public void setEndUporId(Long endUporId) {
        this.endUporId = endUporId;
    }

    public String getUporName() {
        return this.uporName;
    }

    public void setUporName(String uporName) {
        this.uporName = uporName;
    }

    public String getUporNameEqual() {
        return this.uporNameEqual;
    }

    public void setUporNameEqual(String uporNameEqual) {
        this.uporNameEqual = uporNameEqual;
    }

    public String[] getUporNames() {
        return this.uporNames;
    }

    public void setUporNames(String[] uporNames) {
        this.uporNames = uporNames;
    }

    public Long getUpTime() {
        return this.upTime;
    }

    public void setUpTime(Long upTime) {
        this.upTime = upTime;
    }

    public Long[] getUpTimes() {
        return this.upTimes;
    }

    public void setUpTimes(Long[] upTimes) {
        this.upTimes = upTimes;
    }

    public Long getStartUpTime() {
        return this.startUpTime;
    }

    public void setStartUpTime(Long startUpTime) {
        this.startUpTime = startUpTime;
    }

    public Long getEndUpTime() {
        return this.endUpTime;
    }

    public void setEndUpTime(Long endUpTime) {
        this.endUpTime = endUpTime;
    }

    public Long getCustom1() {
        return this.custom1;
    }

    public void setCustom1(Long custom1) {
        this.custom1 = custom1;
    }

    public Long[] getCustom1s() {
        return this.custom1s;
    }

    public void setCustom1s(Long[] custom1s) {
        this.custom1s = custom1s;
    }

    public Long getStartCustom1() {
        return this.startCustom1;
    }

    public void setStartCustom1(Long startCustom1) {
        this.startCustom1 = startCustom1;
    }

    public Long getEndCustom1() {
        return this.endCustom1;
    }

    public void setEndCustom1(Long endCustom1) {
        this.endCustom1 = endCustom1;
    }

    public Long getCustom2() {
        return this.custom2;
    }

    public void setCustom2(Long custom2) {
        this.custom2 = custom2;
    }

    public Long[] getCustom2s() {
        return this.custom2s;
    }

    public void setCustom2s(Long[] custom2s) {
        this.custom2s = custom2s;
    }

    public Long getStartCustom2() {
        return this.startCustom2;
    }

    public void setStartCustom2(Long startCustom2) {
        this.startCustom2 = startCustom2;
    }

    public Long getEndCustom2() {
        return this.endCustom2;
    }

    public void setEndCustom2(Long endCustom2) {
        this.endCustom2 = endCustom2;
    }

    public Long getCustom3() {
        return this.custom3;
    }

    public void setCustom3(Long custom3) {
        this.custom3 = custom3;
    }

    public Long[] getCustom3s() {
        return this.custom3s;
    }

    public void setCustom3s(Long[] custom3s) {
        this.custom3s = custom3s;
    }

    public Long getStartCustom3() {
        return this.startCustom3;
    }

    public void setStartCustom3(Long startCustom3) {
        this.startCustom3 = startCustom3;
    }

    public Long getEndCustom3() {
        return this.endCustom3;
    }

    public void setEndCustom3(Long endCustom3) {
        this.endCustom3 = endCustom3;
    }

    public String getCustom4() {
        return this.custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }

    public String getCustom4Equal() {
        return this.custom4Equal;
    }

    public void setCustom4Equal(String custom4Equal) {
        this.custom4Equal = custom4Equal;
    }

    public String[] getCustom4s() {
        return this.custom4s;
    }

    public void setCustom4s(String[] custom4s) {
        this.custom4s = custom4s;
    }

    public String getCustom5() {
        return this.custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }

    public String getCustom5Equal() {
        return this.custom5Equal;
    }

    public void setCustom5Equal(String custom5Equal) {
        this.custom5Equal = custom5Equal;
    }

    public String[] getCustom5s() {
        return this.custom5s;
    }

    public void setCustom5s(String[] custom5s) {
        this.custom5s = custom5s;
    }

    public String getCustom6() {
        return this.custom6;
    }

    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }

    public String getCustom6Equal() {
        return this.custom6Equal;
    }

    public void setCustom6Equal(String custom6Equal) {
        this.custom6Equal = custom6Equal;
    }

    public String[] getCustom6s() {
        return this.custom6s;
    }

    public void setCustom6s(String[] custom6s) {
        this.custom6s = custom6s;
    }

    public String getSearchField() {
        return this.searchField;
    }

    public void setSearchField(String searchField) {
        this.searchField = searchField;
    }

    public Long getOrderNo() {
        return this.orderNo;
    }

    public void setOrderNo(Long orderNo) {
        this.orderNo = orderNo;
    }

    public Long[] getOrderNos() {
        return this.orderNos;
    }

    public void setOrderNos(Long[] orderNos) {
        this.orderNos = orderNos;
    }

    public Long getStartOrderNo() {
        return this.startOrderNo;
    }

    public void setStartOrderNo(Long startOrderNo) {
        this.startOrderNo = startOrderNo;
    }

    public Long getEndOrderNo() {
        return this.endOrderNo;
    }

    public void setEndOrderNo(Long endOrderNo) {
        this.endOrderNo = endOrderNo;
    }

    public Long getDomainId() {
        return this.domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }

    public Long[] getDomainIds() {
        return this.domainIds;
    }

    public void setDomainIds(Long[] domainIds) {
        this.domainIds = domainIds;
    }

    public Long getStartDomainId() {
        return this.startDomainId;
    }

    public void setStartDomainId(Long startDomainId) {
        this.startDomainId = startDomainId;
    }

    public Long getEndDomainId() {
        return this.endDomainId;
    }

    public void setEndDomainId(Long endDomainId) {
        this.endDomainId = endDomainId;
    }

    public String getCreator() {
        return this.creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getCreatorEqual() {
        return this.creatorEqual;
    }

    public void setCreatorEqual(String creatorEqual) {
        this.creatorEqual = creatorEqual;
    }

    public String[] getCreators() {
        return this.creators;
    }

    public void setCreators(String[] creators) {
        this.creators = creators;
    }

    public String getModifier() {
        return this.modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public String getModifierEqual() {
        return this.modifierEqual;
    }

    public void setModifierEqual(String modifierEqual) {
        this.modifierEqual = modifierEqual;
    }

    public String[] getModifiers() {
        return this.modifiers;
    }

    public void setModifiers(String[] modifiers) {
        this.modifiers = modifiers;
    }

    public Integer getDataStatus() {
        return this.dataStatus;
    }

    public void setDataStatus(Integer dataStatus) {
        this.dataStatus = dataStatus;
    }

    public Integer[] getDataStatuss() {
        return this.dataStatuss;
    }

    public void setDataStatuss(Integer[] dataStatuss) {
        this.dataStatuss = dataStatuss;
    }

    public Integer getStartDataStatus() {
        return this.startDataStatus;
    }

    public void setStartDataStatus(Integer startDataStatus) {
        this.startDataStatus = startDataStatus;
    }

    public Integer getEndDataStatus() {
        return this.endDataStatus;
    }

    public void setEndDataStatus(Integer endDataStatus) {
        this.endDataStatus = endDataStatus;
    }

    public Long getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long[] getCreateTimes() {
        return this.createTimes;
    }

    public void setCreateTimes(Long[] createTimes) {
        this.createTimes = createTimes;
    }

    public Long getStartCreateTime() {
        return this.startCreateTime;
    }

    public void setStartCreateTime(Long startCreateTime) {
        this.startCreateTime = startCreateTime;
    }

    public Long getEndCreateTime() {
        return this.endCreateTime;
    }

    public void setEndCreateTime(Long endCreateTime) {
        this.endCreateTime = endCreateTime;
    }

    public Long getModifyTime() {
        return this.modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Long[] getModifyTimes() {
        return this.modifyTimes;
    }

    public void setModifyTimes(Long[] modifyTimes) {
        this.modifyTimes = modifyTimes;
    }

    public Long getStartModifyTime() {
        return this.startModifyTime;
    }

    public void setStartModifyTime(Long startModifyTime) {
        this.startModifyTime = startModifyTime;
    }

    public Long getEndModifyTime() {
        return this.endModifyTime;
    }

    public void setEndModifyTime(Long endModifyTime) {
        this.endModifyTime = endModifyTime;
    }

}
