package com.uino.api.client.cmdb.local;

import java.util.List;
import java.util.Set;

import com.uino.bean.cmdb.business.ClassReOrderDTO;
import com.uino.dao.BaseConst;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.binary.core.io.Resource;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClass;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uino.service.cmdb.microservice.IRltClassSvc;
import com.uino.bean.cmdb.base.ESCIClassInfo;
import com.uino.api.client.cmdb.IRltClassApiSvc;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
public class RltClassApiSvcLocal implements IRltClassApiSvc {
	@Autowired
	private IRltClassSvc rltClassSvc;

	@Override
	public Long saveOrUpdate(ESCIClassInfo rltClass) {
		// TODO Auto-generated method stub
		return rltClassSvc.saveOrUpdate(rltClass);
	}

	@Override
	public Integer deleteByIds(Set<Long> rltClassIds) {
		// TODO Auto-generated method stub
		return rltClassSvc.deleteByIds(rltClassIds);
	}

	@Override
	public List<CcCiClassInfo> queryAllClasses() {
		// TODO Auto-generated method stub
		return rltClassSvc.queryAllClasses(BaseConst.DEFAULT_DOMAIN_ID);
	}

	@Override
	public List<CcCiClassInfo> queryAllClasses(Long domainId) {
		return rltClassSvc.queryAllClasses(domainId);
	}

	@Override
	public CcCiClassInfo getRltClassById(Long rltClassId) {
		// TODO Auto-generated method stub
		return rltClassSvc.getRltClassById(rltClassId);
	}

	@Override
	public CcCiClassInfo getRltClassByName(Long domainId, String className) {
		return rltClassSvc.getRltClassByName(domainId, className);
	}

	@Override
    public List<CcCiClassInfo> getRltClassByCdt(CCcCiClass cdt) {
        return rltClassSvc.getRltClassByCdt(cdt);
    }

    @Override
	public Resource exportAttrDefs(Set<Long> clsIds) {
		// TODO Auto-generated method stub
		return rltClassSvc.exportAttrDefs(clsIds);
	}

	@Override
	public ESCIClassInfo importAttrDefs(MultipartFile excelFile, Long clsId) {
		// TODO Auto-generated method stub
		return rltClassSvc.importAttrDefs(excelFile, clsId);
	}

	@Override
	public boolean reOrder(ClassReOrderDTO reOrderDTO) {
		return rltClassSvc.reOrder(reOrderDTO);
	}

	@Override
	public boolean initAllOrderNo() {
		return rltClassSvc.initAllOrderNo(BaseConst.DEFAULT_DOMAIN_ID);
	}

	@Override
	public boolean initAllOrderNo(Long domainId) {
		return rltClassSvc.initAllOrderNo(domainId);
	}

}
