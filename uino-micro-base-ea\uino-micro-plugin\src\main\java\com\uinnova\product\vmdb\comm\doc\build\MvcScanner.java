package com.uinnova.product.vmdb.comm.doc.build;

import com.binary.core.lang.ClassUtils;
import com.uinnova.product.vmdb.comm.doc.annotation.Ignore;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.core.type.ClassMetadata;
import org.springframework.core.type.classreading.CachingMetadataReaderFactory;
import org.springframework.core.type.classreading.MetadataReader;
import org.springframework.core.type.classreading.MetadataReaderFactory;
import org.springframework.stereotype.Controller;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 
 * <AUTHOR>
 *
 */
public class MvcScanner {
    private static final Logger logger = LoggerFactory.getLogger(MvcScanner.class);

    private static String resourcePattern = "**/*.class";

    private static ResourcePatternResolver resourcePatternResolver = new PathMatchingResourcePatternResolver();
    private static MetadataReaderFactory metadataReaderFactory = new CachingMetadataReaderFactory(resourcePatternResolver);

    public static List<Class<?>> scanMvc(String location) {
        logger.info(" start scan-mvc '{}'... ", location);
        List<Class<?>> mvcls = new ArrayList<Class<?>>();
        try {
            String basePackage = location;
            if (basePackage == null || (basePackage = basePackage.trim()).length() == 0) {
                return mvcls;
            }

            String packageSearchPath = ResourcePatternResolver.CLASSPATH_ALL_URL_PREFIX + resolveBasePackage(basePackage) + "/" + resourcePattern;
            Resource[] resources = resourcePatternResolver.getResources(packageSearchPath);

            if (resources != null && resources.length > 0) {
                Set<Class<?>> scaneds = new HashSet<Class<?>>();

                for (int j = 0; j < resources.length; j++) {
                    Resource res = resources[j];

                    MetadataReader metadataReader = metadataReaderFactory.getMetadataReader(res);
                    ClassMetadata classMetadata = metadataReader.getClassMetadata();
                    String className = classMetadata.getClassName();
                    if (!className.toLowerCase().endsWith("mvc")) {
                        continue;
                    }

                    Class<?> c = ClassUtils.forName(className);
                    if (isController(c) && !scaneds.contains(c)) {
                        logger.info(" scanned mvc '" + c.getName() + "'. ");
                        scaneds.add(c);
                        mvcls.add(c);
                    }
                }
            }
        } catch (Throwable t) {
            logger.error(" scanMvc '" + location + "' error! ", t);
        }
        return mvcls;
    }

    private static String resolveBasePackage(String basePackage) {
        return basePackage.replace('.', '/');
    }

    private static boolean isController(Class<?> c) {
        return c.isAnnotationPresent(Controller.class) && !c.isAnnotationPresent(Ignore.class);
    }

}
