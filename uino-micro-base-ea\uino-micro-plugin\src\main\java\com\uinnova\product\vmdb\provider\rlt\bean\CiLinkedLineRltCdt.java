package com.uinnova.product.vmdb.provider.rlt.bean;

import com.binary.framework.bean.annotation.Comment;

import java.io.Serializable;
import java.util.List;

@Comment("线上具体分类的查询条件")
public class CiLinkedLineRltCdt implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	private Long rltClassId;

	private List<CiLinkedAttrCdt> cdts;

	public Long getRltClassId() {
		return rltClassId;
	}

	public void setRltClassId(Long rltClassId) {
		this.rltClassId = rltClassId;
	}

	public List<CiLinkedAttrCdt> getCdts() {
		return cdts;
	}

	public void setCdts(List<CiLinkedAttrCdt> cdts) {
		this.cdts = cdts;
	}

}
