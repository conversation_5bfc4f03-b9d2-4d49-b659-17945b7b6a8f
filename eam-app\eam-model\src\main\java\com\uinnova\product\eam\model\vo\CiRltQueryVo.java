package com.uinnova.product.eam.model.vo;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.io.Serializable;

/**
 * 对象分类关系分类查询实体
 * <AUTHOR>
 * @date 2023/11/10
 */
@Data
public class CiRltQueryVo implements Serializable {

	@Comment("ci关系分类Id")
	private Long classId;
	
	@Comment("来源CI分类ID")
	private Long sourceClassId;
	
	@Comment("目标CI分类ID")
	private Long targetClassId;

	@Comment("起始节点id")
	private Long nodeStartId;

	@Comment("终止节点id")
	private Long nodeEndId;

	@Comment("方向")
	private Boolean direction;

	public String getRltCode(){
		return this.sourceClassId + "_" + this.classId + "_" + this.targetClassId;
	}

}
