package com.uinnova.product.vmdb.provider.quality.bean;

/**
 * Created by <PERSON><PERSON>iming
 * Date: 2019/11/25
 * Time: 下午2:25
 */
public enum ProblemType {

	COMPLETENESS("完整性", "完整性"),
	VALIDITY("有效性", "有效性"),
	ORPHAN("孤儿", "孤岛");

	private String code;

	private String value;

	private ProblemType(String code, String value) {
		this.code = code;
		this.value = value;
	}

	public String getType() {
		return code;
	}

	public String getValue() {
		return value;
	}

	public static String getValue(String code) {
		if (COMPLETENESS.code.equals(code)) {
			return COMPLETENESS.getValue();
		} else if (VALIDITY.code.equals(code)) {
			return VALIDITY.getValue();
		} else if (ORPHAN.code.equals(code)) {
			return ORPHAN.getValue();
		} else {
			return null;
		}
	}

	public static String getType(String value) {
		if (COMPLETENESS.value.equals(value)) {
			return COMPLETENESS.getType();
		} else if (VALIDITY.value.equals(value)) {
			return VALIDITY.getType();
		} else if (ORPHAN.value.equals(value)) {
			return ORPHAN.getType();
		} else {
			return null;
		}
	}


}
