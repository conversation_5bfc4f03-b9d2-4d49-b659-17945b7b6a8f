package com.uinnova.product.eam.web.asset.bean;

import com.uinnova.product.eam.model.asset.EamQuestionDTO;
import lombok.Data;

import java.util.List;

@Data
public class EamQuestionVO {

    private Integer id;

    private List<EamQuestionVO.MetricItem> metrics;

    @Data
    public static class MetricItem{

        private String itemNo;

        private String item;

        private Integer itemRate;

        private List<EamQuestionVO.ItemSelector> selectors;

    }

    @Data
    public static class ItemSelector{

        private String code;

        private String name;

        private Integer score;

    }
}
