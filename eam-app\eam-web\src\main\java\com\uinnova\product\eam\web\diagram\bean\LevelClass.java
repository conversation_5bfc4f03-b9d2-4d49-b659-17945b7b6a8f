package com.uinnova.product.eam.web.diagram.bean;

import java.io.Serializable;

import com.binary.framework.bean.annotation.Comment;

/**
 * 层级分类
 * <AUTHOR>
 *
 */
public class LevelClass implements Serializable {

	private static final long serialVersionUID = 1L;
	
	@Comment("层级")
	private Integer level;
	
	@Comment("分类ID")
	private Long classId;
	
	@Comment("分类名称")
	private String className;

	public Integer getLevel() {
		return level;
	}

	public void setLevel(Integer level) {
		this.level = level;
	}

	public Long getClassId() {
		return classId;
	}

	public void setClassId(Long classId) {
		this.classId = classId;
	}

	public String getClassName() {
		return className;
	}

	public void setClassName(String className) {
		this.className = className;
	}
}
