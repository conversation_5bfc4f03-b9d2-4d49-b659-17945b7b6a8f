package com.uino.bean.cmdb.base;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 
 * <AUTHOR>
 *
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ESResource implements Serializable {
	private static final long serialVersionUID = 1L;
	/** ID */
	private Long id;
	/**
	 * 资源地址
	 */
	private String path;
	/**
	 * 资源公开的地址
	 */
	private String publicUrl;
	/**
	 * 操作类型0:创建1:删除2:修改
	 */
	private Integer optionType;

	/**
	 * 是否需要解压资源
	 */
	@Builder.Default
	private boolean unzip = false;
	/**
	 * 是否解压至当前目录 false:压缩包会在当前目录创建一个zipName文件夹并解压至该文件夹下 true:压缩包会解压至当前目录下
	 */
	@Builder.Default
	private boolean currentDir = false;
	/**
	 * 文件的md5，若为创建/修改，去拉取文件时发现源资源服务器上真实的实体文件md5值与这条资源动作的MD5不同则代表，该动作已被后面动作覆盖，
	 * 直接忽略该动作即可
	 */
	private String fileMD5;

	/** 所属域 */
	private Long domainId;

	/** 创建人 */
	private String creator;

	/** 修改人 */
	private String modifier;

	/**
	 * 由于无法保证递增id，该创建时间同时会被各个server当作指针起点使用
	 */
	private Long createTime;

	/** 修改时间 */
	private Long modifyTime;
}
