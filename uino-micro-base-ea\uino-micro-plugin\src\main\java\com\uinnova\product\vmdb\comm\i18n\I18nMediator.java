package com.uinnova.product.vmdb.comm.i18n;

import com.binary.core.i18n.LanguageResolver;
import com.binary.core.i18n.LanguageTranslator;

/**
 * 定制一个语言转化策略防止与全局冲突
 * <p>
 * 参考类{@link I18nMediatorResolver}和{@link MessageUtil}
 * 
 * <AUTHOR>
 *
 */
public interface I18nMediator {

    /**
     * 这个Translator 是被设置到{@link LanguageResolver}中的Translator
     * 
     * @return
     */
    LanguageTranslator getGlobalTranslator();

    /**
     * 这个Translator 应该是用户需要在{@link MessageUtil} 中使用的Translator
     * 
     * @return
     */
    LanguageTranslator getCustomTranslator();

}
