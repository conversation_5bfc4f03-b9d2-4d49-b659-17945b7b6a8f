package com.uinnova.product.eam.base.diagram.model;

public class ShareRecordQuery {

    private Long id;

    private Long diagramId;

    private Long[] diagramIds;

    private Long ownerId;

    private Long sharedUserId;

    private Integer permission;

    private Integer dirType;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getDiagramId() {
        return diagramId;
    }

    public void setDiagramId(Long diagramId) {
        this.diagramId = diagramId;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public Long getSharedUserId() {
        return sharedUserId;
    }

    public void setSharedUserId(Long sharedUserId) {
        this.sharedUserId = sharedUserId;
    }

    public Integer getPermission() {
        return permission;
    }

    public void setPermission(Integer permission) {
        this.permission = permission;
    }

    public Long[] getDiagramIds() {
        return diagramIds;
    }

    public void setDiagramIds(Long[] diagramIds) {
        this.diagramIds = diagramIds;
    }

    public Integer getDirType() {
        return dirType;
    }

    public void setDirType(Integer dirType) {
        this.dirType = dirType;
    }
}
