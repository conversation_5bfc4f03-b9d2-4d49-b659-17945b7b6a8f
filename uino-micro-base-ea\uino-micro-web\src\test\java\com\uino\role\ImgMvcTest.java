package com.uino.role;

import static org.junit.Assert.assertTrue;

import java.io.File;
import java.io.FileInputStream;

import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.uino.StartBaseWebAppliaction;
import com.uino.bean.permission.query.SearchKeywordBean;

import lombok.extern.slf4j.Slf4j;

@RunWith(SpringRunner.class)
@WebAppConfiguration
@SpringBootTest(classes = { StartBaseWebAppliaction.class })
@ActiveProfiles("provider-local")
@AutoConfigureMockMvc(addFilters = false)
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
@Slf4j
public class ImgMvcTest {

    @Autowired
    private MockMvc mockMvc;

    String token = "ca14ed042fe7912426b484f6ea5c754b4fb51ae4a72037e9127da51743e0d1f9650e6e78ed4466ac970acb6a0b2f46fd26958c4ab2d115cc71b34ca8a02db24c";

    @Test
    public void importImage() throws Exception {
        String uri = "/cmdb/image/importImage";
        String dirId = "3488878120450000";
        File file = new File("./src/test/resources/testdata/10006.png");
        ResultActions resultActions = mockMvc.perform(MockMvcRequestBuilders.multipart(uri)
            .file(new MockMultipartFile("file", "10006.png", "application/x-png", new FileInputStream(file)))
                .param("dirId", dirId));
        MvcResult mvcResult = resultActions.andDo(MockMvcResultHandlers.print())
                .andExpect(MockMvcResultMatchers.status().isOk()).andReturn();
        String result = mvcResult.getResponse().getContentAsString();
        log.info("==========结果为：==========\n" + result + "\n");
    }

    @Test
    public void replaceImage() throws Exception {
        String uri = "/cmdb/image/replaceImage";
        String imgId = "3518930328650001";
        File file = new File("./src/test/resources/testdata/10006.png");
        ResultActions resultActions = mockMvc.perform(MockMvcRequestBuilders.multipart(uri)
            .file(new MockMultipartFile("file", "10006.png", "application/x-png", new FileInputStream(file)))
                .param("imgId", imgId));
        MvcResult mvcResult = resultActions.andDo(MockMvcResultHandlers.print())
                .andExpect(MockMvcResultMatchers.status().isOk()).andReturn();
        String result = mvcResult.getResponse().getContentAsString();
        log.info("==========结果为：==========\n" + result + "\n");
    }

    @Test
    public void deleteDirImage() throws Exception {
        String uri = "/cmdb/image/deleteDirImage";
        String dirId = "3488878120450000";
        ResultActions reaction = this.mockMvc.perform(MockMvcRequestBuilders.post(uri).content(dirId)
                .contentType(MediaType.APPLICATION_JSON).accept(MediaType.APPLICATION_JSON)
                .header("Content-Type", "application/json").header("token", token));
        ;
        MvcResult mvcResult = reaction.andReturn();
        String rs = mvcResult.getResponse().getContentAsString();
        log.info(rs);

    }

    @Test
    public void importZipImage() throws Exception {
        String uri = "/cmdb/image/importZipImage";
        File file = new File("./src/test/resources/testdata/img_dir_01.zip");
        ResultActions resultActions = mockMvc.perform(MockMvcRequestBuilders.multipart(uri)
            .file(new MockMultipartFile("file", "img_dir_01.zip", "application/zip", new FileInputStream(file))));
        MvcResult mvcResult = resultActions.andDo(MockMvcResultHandlers.print())
                .andExpect(MockMvcResultMatchers.status().isOk()).andReturn();
        String result = mvcResult.getResponse().getContentAsString();
        log.info("==========结果为：==========\n" + result + "\n");
    }

    @Test
    public void queryImageDirList() throws Exception {
        String uri = "/cmdb/image/queryImageDirList";
        ResultActions reaction = this.mockMvc.perform(MockMvcRequestBuilders.post(uri)
                .contentType(MediaType.APPLICATION_JSON).accept(MediaType.APPLICATION_JSON)
                .header("Content-Type", "application/json").header("token", token));
        MvcResult mvcResult = reaction.andReturn();
        String rs = mvcResult.getResponse().getContentAsString();
        log.info(rs);
        JSONObject json = JSON.parseObject(rs);
        assertTrue(json.getBooleanValue("success") == true);
    }

    @Test
    public void queryImagePage() throws Exception {
        String uri = "/cmdb/image/queryImagePage";
        SearchKeywordBean query = new SearchKeywordBean();
        // query.setDirId(dirId);
        String body = JSON.toJSONString(query);
        ResultActions reaction = this.mockMvc.perform(MockMvcRequestBuilders.post(uri)
                .contentType(MediaType.APPLICATION_JSON).content(body).accept(MediaType.APPLICATION_JSON)
                .header("Content-Type", "application/json").header("token", token));
        MvcResult mvcResult = reaction.andReturn();
        String rs = mvcResult.getResponse().getContentAsString();
        log.info(rs);
    }

}
