package com.uinnova.product.eam.comm.model.es;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 矩阵表格数据
 * <AUTHOR>
 */
@Data
public class EamMatrixInstanceData implements Serializable {

    @Comment("主键")
    private Long id;

    @Comment("矩阵表格id")
    private Long tableId;

    @Comment("序号")
    private Integer num;

    @Comment("源端ciCode")
    private String sourceCiCode;

    @Comment("目标端ciCode")
    private String targetCiCode;

    @Comment("关系code")
    private String rltCode;

    //====================================资产库归档版本字段========================================

    @Comment("对应ci发布版本")
    private Long sourceVersion;

    @Comment("对应ci发布版本")
    private Long targetVersion;

    @Comment("对应rlt发布版本")
    private Long rltVersion;

    @Comment("行数据")
    private List<EamMatrixTableCase> data;

}
