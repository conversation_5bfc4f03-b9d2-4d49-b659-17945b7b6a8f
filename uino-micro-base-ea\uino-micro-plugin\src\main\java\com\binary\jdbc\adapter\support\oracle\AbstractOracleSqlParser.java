package com.binary.jdbc.adapter.support.oracle;

import com.binary.jdbc.adapter.SqlDissolver;
import com.binary.jdbc.adapter.support.AbstractSqlParser;
import com.binary.jdbc.exception.SqlParserException;

public abstract class AbstractOracleSqlParser extends AbstractSqlParser {
	
	
	
	
	@Override
	public String parseCountSql(String sql) {
		return "select count(1) from ("+sql+")";
	}
	

	
	@Override
	public String parseSimpleCountSql(String sql) {
		return parseCountSql(sql);
	}
	
	

	
	@Override
	public String parsePagingSql(SqlDissolver dissolver, String orderByFields, long pageNum, long pageSize) {
//		String[] fields = dissolver.getFields();
		return paging(dissolver, orderByFields, pageNum, pageSize);
	}
	
	
	
	private String paging(SqlDissolver dissolver, String orderByFields, long pageNum, long pageSize) {
//		if(orderByFields==null || (orderByFields=orderByFields.trim()).length()==0) throw new SqlParserException(" the orderByFields is NULL argument! ");
//		StringBuffer sb = new StringBuffer();
//		sb.append("select ");
//		
//		for(int i=0; i<fields.length; i++) {
//			if(i > 0) sb.append(", ");
//			sb.append(fields[i]);
//		}
//		
//		String baseSelect = sb.substring(0, sb.length());
//		long startRow = (pageNum - 1) * pageSize + 1;
//		long endRow = startRow + pageSize;
//		
//		sb.delete(0, sb.length());
//		sb.append(baseSelect).append(" from (").append(baseSelect).append(", rownum alias_for_rownum from (")
//					.append(sql).append(" order by ").append(orderByFields).append(") where rownum<").append(endRow).append(") where alias_for_rownum>=").append(startRow);
//		return sb.toString();
		
		
		if(orderByFields==null || (orderByFields=orderByFields.trim()).length()==0) throw new SqlParserException(" the orderByFields is NULL argument! ");
		String[] fields = dissolver.getFields();
		String selectSql = dissolver.getSelectSql();
		String fromSql = dissolver.getFromSql();
		
		StringBuffer sb = new StringBuffer();
		
		String outSelectSql = dissolver.getOutBracketsSelectSql();
		String temp_sql = outSelectSql.trim().toLowerCase().replaceAll("[\\s]", " ");
		boolean hasdistinct = temp_sql.substring(temp_sql.indexOf(" ")+1).startsWith("distinct");
		
		sb.append("select ");
		for(int i=0; i<fields.length; i++) {
			if(i > 0) sb.append(", ");
			sb.append(fields[i]);
		}
		String baseSelect = sb.substring(0, sb.length());
		sb.delete(0, sb.length());
		
		if(hasdistinct) {
			long startRow = (pageNum - 1) * pageSize + 1;
			long endRow = startRow + pageSize;
			sb.append(baseSelect)
					.append(" from (").append(baseSelect).append(", ROW_NUMBER() OVER(order by ").append(orderByFields).append(") alias_for_rownum ")
					.append(" from (").append(selectSql).append(fromSql).append(")")
					.append(" order by ").append(orderByFields).append(") where alias_for_rownum<").append(endRow).append(" and alias_for_rownum>=").append(startRow);
		}else {
			long startRow = (pageNum - 1) * pageSize + 1;
			long endRow = startRow + pageSize;
			sb.append(baseSelect).append(" from (").append(selectSql).append(", ROW_NUMBER() OVER(order by ").append(orderByFields).append(") alias_for_rownum ").append(fromSql)
						.append(" order by ").append(orderByFields).append(") where alias_for_rownum<").append(endRow).append(" and alias_for_rownum>=").append(startRow);
		}
		
		return sb.toString();
	}
	
	
	
	
}



