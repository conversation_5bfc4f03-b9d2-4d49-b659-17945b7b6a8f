package com.uinnova.product.eam.db.impl;



import com.uinnova.product.eam.comm.model.CDcCombDiagram;
import com.uinnova.product.eam.comm.model.DcCombDiagram;
import com.uinnova.product.eam.db.DcCombDiagramDao;
import com.uinnova.product.eam.db.support.dao.mybatis.ComMyBatisBinaryDaoImpl;


/**
 * 组合视图表[DC_COMB_DIAGRAM]数据访问对象实现
 */
public class DcCombDiagramDaoImpl extends ComMyBatisBinaryDaoImpl<DcCombDiagram, CDcCombDiagram> implements DcCombDiagramDao {

//    @Override
//    public String getTableName() {
//        return "IAMS." + getDaoDefinition().getTableName();
//    }

}


