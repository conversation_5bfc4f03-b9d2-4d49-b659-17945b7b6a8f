package com.uinnova.product.vmdb.comm.log;

/**
 * 操作描述
 * 
 * <AUTHOR>
 */
public class OperateDesc {

    /** 方法名称 **/
    private String modName;

    /** 方法访问全路径 **/
    private String fullPath;

    /**
     * 方法描述
     */
    private String desc;

    /** mvc名称(全限定类名) **/
    private String mvc;

    public OperateDesc() {
    }

    public OperateDesc(String modName, String fullPath, String desc, String mvc) {
        this.modName = modName;
        this.fullPath = fullPath;
        this.desc = desc;
        this.mvc = mvc;
    }

    public String getModName() {
        return modName;
    }

    public void setModName(String modName) {
        this.modName = modName;
    }

    public String getFullPath() {
        return fullPath;
    }

    public void setFullPath(String fullPath) {
        this.fullPath = fullPath;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getMvc() {
        return mvc;
    }

    public void setMvc(String mvc) {
        this.mvc = mvc;
    }

}
