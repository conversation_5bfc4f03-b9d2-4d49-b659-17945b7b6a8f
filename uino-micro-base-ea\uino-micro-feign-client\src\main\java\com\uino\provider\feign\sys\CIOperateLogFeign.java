package com.uino.provider.feign.sys;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.binary.jdbc.Page;
import com.uino.bean.sys.base.ESCIOperateLog;
import com.uino.bean.sys.query.ESCIOperateLogSearchBean;
import com.uino.provider.feign.config.BaseFeignConfig;

/**
 * 
 * <AUTHOR>
 *
 */
@Component
@FeignClient(name = "${feign-server-name:tarsier-platform}", path = "${feign-server-path:tarsier-platform}/feign/sys/ciLog", configuration = {BaseFeignConfig.class})
public interface CIOperateLogFeign {

    /**
     * 条件查询CI配置日志
     * 
     * @param bean
     * @return
     */
    @PostMapping("getCIOperateLogPageByCdt")
    Page<ESCIOperateLog> getCIOperateLogPageByCdt(@RequestBody ESCIOperateLogSearchBean bean);

    /**
     * 按保留时长清除CI配置日志
     * 
     * @param clearLogDuration
     * @return
     */
    @PostMapping("clearCIOperateLogByDuration")
    Integer clearCIOperateLogByDuration(@RequestBody Integer clearLogDuration);
}
