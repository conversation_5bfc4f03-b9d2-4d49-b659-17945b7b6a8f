FROM corvis/java8:latest
MAINTAINER uino
USER root
ADD application.yml /usr/local/
ADD start.sh /usr/local/
ADD eam-workable-deploy.tar.gz /usr/local/
ADD simsun.ttc /usr/share/fonts/dejavu/

RUN rm -f /usr/local/eam-workable/application.yml \
    && rm -f /usr/local/eam-workable/start.sh \
    && mv /usr/local/application.yml /usr/local/eam-workable/ \
    && mv /usr/local/start.sh /usr/local/eam-workable/ \
    && chmod +x /usr/local/eam-workable/start.sh

WORKDIR /usr/local/eam-workable

CMD /usr/local/eam-workable/start.sh