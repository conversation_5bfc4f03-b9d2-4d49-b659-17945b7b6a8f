package com.uino.service.license;

import com.binary.core.util.BinaryUtils;
import com.binary.framework.Local;
import com.binary.framework.bean.User;
import com.uinnova.product.vmdb.comm.i18n.MessageUtil;
import com.uino.bean.license.BaseLicenseAuth;
import com.uino.dao.license.ESLicenseAuthSvc;
import com.uino.license.sdk.LicenseAuthorityAdapter;
import com.uino.service.license.microservice.impl.LicenseAuthSvc;
import org.apache.commons.lang3.concurrent.BasicThreadFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.TimerTask;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 
 * <AUTHOR>
 *
 */
public class LicenseCumulativeTimer {
	private static final Logger logger = LoggerFactory.getLogger(LicenseCumulativeTimer.class);
	
	private final TimerTask heartbeatTask = new TimerTask() {
		
		@Override
		public void run() {
			try {
				boolean open = Local.isOpen();
				try {
                    if (!open) {
                        Local.open((User) null);
                    }
					heartbeat();
                    if (!open) {
                        Local.commit();
                    }
				}catch(Throwable t) {
                    if (!open) {
                        Local.rollback();
                    }
					throw t;
				}finally {
                    if (!open) {
                        Local.close();
                    }
				}
			}catch(Throwable t) {
				logger.error(" license cumulative hearbeat error! ", t);
			}
		}
	};

    private ESLicenseAuthSvc licenseAuthDao;
    private LicenseAuthSvc licenseAuthSvc;
	private LicenseAuthorityAdapter licenseAuthorityAdapter;
	private boolean updateUtTime;
	
	
	/** 累计时间, 单位：分钟 **/
	private long cumulativeTime = 0;
	
	/** 最近license **/
	private String lastLicense;
	private String lastCodeUser;
	
	
	
    public LicenseCumulativeTimer(LicenseAuthSvc licenseAuthSvc, LicenseAuthorityAdapter licenseAuthorityAdapter, ESLicenseAuthSvc licenseAuthDao, Long lastUsedTime) {
        this(licenseAuthSvc, licenseAuthorityAdapter, licenseAuthDao, lastUsedTime, false);
	}

    public LicenseCumulativeTimer(LicenseAuthSvc licenseAuthSvc, LicenseAuthorityAdapter licenseAuthorityAdapter, ESLicenseAuthSvc licenseAuthDao, Long lastUsedTime, boolean updateUtTime) {
		MessageUtil.checkEmpty(licenseAuthSvc, "licenseAuthSvc");
		MessageUtil.checkEmpty(licenseAuthorityAdapter, "licenseAuthorityAdapter");
		MessageUtil.checkEmpty(licenseAuthDao, "licenseAuthDao");
		this.updateUtTime = updateUtTime;
		
		if(lastUsedTime!=null && lastUsedTime.longValue()>0) {
			this.cumulativeTime = lastUsedTime;
		}
		this.licenseAuthSvc = licenseAuthSvc;
		this.licenseAuthorityAdapter = licenseAuthorityAdapter;
		this.licenseAuthDao = licenseAuthDao;
		
        ScheduledExecutorService timer = new ScheduledThreadPoolExecutor(1, new BasicThreadFactory.Builder().namingPattern("license-schedule-%d").daemon(true).build());
        int intervalTime = 60;
        timer.scheduleAtFixedRate(heartbeatTask, intervalTime, intervalTime, TimeUnit.SECONDS);
	}
	
	
	
	
	public boolean isUpdateUtTime() {
		return updateUtTime;
	}
	public void setUpdateUtTime(boolean updateUtTime) {
		this.updateUtTime = updateUtTime;
	}
	
	
	
	private synchronized void heartbeat() {
	
		//定时更新ClientCode
		licenseAuthSvc.initSelfClient();
		
		//检查ClientCode是否有变化
		BaseLicenseAuth auth = licenseAuthDao.selectOneRow();
		String codeUser = auth.getCodeUser();
		//logger.info(">>>codeUser="+codeUser+",lastCodeUser="+lastCodeUser);
		String license = auth.getAuthCode();
		if(!codeUser.equals(this.lastCodeUser)) {
			//重新设置客户端码
			//logger.info(">>>setClientHashCode("+codeUser+")");
			this.lastLicense = null;
			registerLicense(auth);
		}
		else if(license!=null && !license.equals(this.lastLicense)) {
			//重新设置授权码
			registerLicense(auth);
		} else if(licenseAuthorityAdapter.isValid() && !BinaryUtils.isEmpty(this.lastLicense)) {
			//如果当前系统license是有效的, 则记录运行时间
			if(this.updateUtTime) {
				this.cumulativeTime ++ ;
				int count = licenseAuthSvc.updateUtTimeByAuthCode(this.lastLicense, this.cumulativeTime);
				
				if(count == 0) {
					this.lastLicense = null;
					registerLicense(auth);
				}
			}
		}
		//如果当前license是无效的, 则重新注册license
		else {
			registerLicense(auth);
		}
	}
	
	
	
	private void registerLicense(BaseLicenseAuth auth) {
		try {
			//CcLicenseAuth auth = licenseAuthDao.selectOneRow();
			
			//注册license
			String license = auth.getAuthCode();
	//		if(BinaryUtils.isEmpty(license)) {
	//			logger.error(" Product without authorization! ");
	//			return ;
	//		}
			
			if(license!=null && license.equals(this.lastLicense)) {
				return ;
			} else if(license == null || license.equals("")) {
				this.lastLicense = "";
				this.lastCodeUser = "";
			} else {
				this.lastLicense = license;
				this.lastCodeUser = auth.getCodeUser();				
				setCumulativeTime(auth.getUtTime());
			}		
			
			//licenseAuthorityAdapter.registerAuthCode(license);
			licenseAuthorityAdapter.registerAuthCode(license);
			logger.info(" License authorization successful. ");
		}catch(Throwable t) {
			logger.error(" License authorization failed! ");
		}
	}
	
	
	
	/**
	 * 获取累计时间
	 * @return
	 */
	public long getCumulativeTime() {
		return this.cumulativeTime;
	}



	public void setCumulativeTime(long cumulativeTime) {
		this.cumulativeTime = cumulativeTime;
	}
	
	
	
	

}
