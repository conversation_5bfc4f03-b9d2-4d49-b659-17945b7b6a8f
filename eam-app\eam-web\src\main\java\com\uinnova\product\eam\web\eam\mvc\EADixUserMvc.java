package com.uinnova.product.eam.web.eam.mvc;

import com.binary.framework.web.RemoteResult;
import com.uinnova.product.eam.model.dto.UserInfoDto;
import com.uinnova.product.eam.service.EADixUserSvc;
import com.uino.bean.cmdb.business.ImportSheetMessage;
import com.uino.bean.permission.base.SysOrg;
import com.uino.bean.permission.base.SysUserOrgRlt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/ea/dix")
public class EADixUserMvc {

    @Autowired
    private EADixUserSvc eaDixUserSvc;

    @PostMapping("/saveBatchOrg")
    public RemoteResult saveBatchOrg(@RequestBody List<SysOrg> orgList) {
        Integer result = eaDixUserSvc.saveBatchOrg(orgList);
        return new RemoteResult(result);
    }

    @PostMapping("/saveBatchUser")
    public RemoteResult saveBatchUser(@RequestBody List<UserInfoDto> userInfos) {
        ImportSheetMessage result = eaDixUserSvc.saveBatchUser(userInfos);
        return new RemoteResult(result);
    }

    @PostMapping("/saveBatchOrgUserRlt")
    public RemoteResult saveBatchOrgUserRlt(@RequestBody List<SysUserOrgRlt> rltList) {
        Integer result = eaDixUserSvc.saveBatchOrgUserRlt(rltList);
        return new RemoteResult(result);
    }

    /**
    * @Description:同步组织数据至部门
    */
    @PostMapping("/saveBatchDepartment")
    public RemoteResult saveBatchDepartment() {
        Integer result = eaDixUserSvc.saveBatchDepartment();
        return new RemoteResult(result);
    }

}
