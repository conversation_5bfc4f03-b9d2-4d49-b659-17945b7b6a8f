package com.uinnova.product.eam.web.cj;

import com.binary.framework.web.RemoteResult;
import com.uinnova.product.eam.comm.model.es.TemplateBinding;
import com.uinnova.product.eam.service.PlanTemplateBindService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/cj/planTemplate")
public class PlanTemplateBindController {

    @Autowired
    private PlanTemplateBindService planTemplateBindService;

    @GetMapping("/addBind")
    public RemoteResult addBind(@RequestParam Long classId) {
        Integer result = planTemplateBindService.addBind(classId);
        return new RemoteResult(result);
    }

    @PostMapping("/saveOrUpdateBatch")
    public RemoteResult saveOrUpdateBatch(@RequestBody List<TemplateBinding> templateBindingList) {
        Integer result = planTemplateBindService.saveOrUpdateBatch(templateBindingList);
        return new RemoteResult(result);
    }

    @GetMapping("/getBindList")
    public RemoteResult getBindList() {
        List<TemplateBinding> result = planTemplateBindService.getBindList();
        return new RemoteResult(result);
    }

    @GetMapping("/deleteBindById")
    public RemoteResult deleteBindById(@RequestParam Long id) {
        Integer result =  planTemplateBindService.deleteBindById(id);
        return new RemoteResult(result);
    }

    @GetMapping("/dragSort")
    public RemoteResult dragSort(@RequestParam Long bindId , @RequestParam Long sortNum) {
        Assert.notNull(bindId, "拖拽模板绑定目录Id不能为空");
        Assert.notNull(sortNum, "要插入的位置不能为空");
        planTemplateBindService.dragSort(bindId,sortNum);
        return new RemoteResult(true);
    }
}
