package com.uinnova.product.vmdb.comm.bean;

import com.binary.framework.bean.annotation.Comment;

import java.io.Serializable;
import java.util.List;


/**
 * 
 * <AUTHOR>
 *
 */
@Comment("关系的节点")
public class RelationPathEle implements Serializable {
    private static final long serialVersionUID = 1L;
    @Comment("上级的分类ID")
    private Long prevCiClassId;
    @Comment("上级的ci的ID")
    private Long[] prevCiIds;
    @Comment("下级的分类ID")
    private Long nextCiClassId;
    @Comment("上级的ci的ID")
    private Long[] nextCiIds;
    @Comment("关系分类的ID")
    private List<Long> rltClassIds;

    public Long getPrevCiClassId() {
        return prevCiClassId;
    }

    public void setPrevCiClassId(Long prevCiClassId) {
        this.prevCiClassId = prevCiClassId;
    }

    public Long getNextCiClassId() {
        return nextCiClassId;
    }

    public void setNextCiClassId(Long nextCiClassId) {
        this.nextCiClassId = nextCiClassId;
    }

    public List<Long> getRltClassIds() {
        return rltClassIds;
    }

    public void setRltClassIds(List<Long> rltClassIds) {
        this.rltClassIds = rltClassIds;
    }

    public Long[] getPrevCiIds() {
        return prevCiIds;
    }

    public void setPrevCiIds(Long[] prevCiIds) {
        this.prevCiIds = prevCiIds;
    }

    public Long[] getNextCiIds() {
        return nextCiIds;
    }

    public void setNextCiIds(Long[] nextCiIds) {
        this.nextCiIds = nextCiIds;
    }

}
