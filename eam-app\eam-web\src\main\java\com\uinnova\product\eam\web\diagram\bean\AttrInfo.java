package com.uinnova.product.eam.web.diagram.bean;

import java.io.Serializable;

import com.binary.framework.bean.annotation.Comment;

public class AttrInfo implements Serializable{

	private static final long serialVersionUID = 1L;
	
	@Comment("键名称")
	private String key;
	
	@Comment("属性值")
	private String value;

	public String getKey() {
		return key;
	}

	public void setKey(String key) {
		this.key = key;
	}

	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}
	
	
	

}
