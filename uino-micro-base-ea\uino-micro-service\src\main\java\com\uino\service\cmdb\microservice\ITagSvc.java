package com.uino.service.cmdb.microservice;

import com.alibaba.fastjson.JSONObject;
import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uino.bean.cmdb.base.ESCIClassInfo;
import com.uino.bean.cmdb.base.ESCITagInfo;
import com.uino.bean.cmdb.base.ESTagRuleInfo;
import com.uino.bean.cmdb.business.ClassNodeInfo;
import com.uino.bean.cmdb.query.ESAttrAggBean;
import com.uino.bean.cmdb.query.ESTagSearchBean;
import org.elasticsearch.index.query.QueryBuilder;

import java.util.List;
import java.util.Map;

/**
 * 
 * <AUTHOR>
 *
 */
public interface ITagSvc {

    /**
     * 保存标签规则
     * 
     * @param tagInfo
     * @return
     */
    public Long saveOrUpdateCITagRule(ESCITagInfo tagInfo);

    /**
     * 根据id查询标签规则
     * 
     * @param id
     * @return
     */
    public ESCITagInfo getCITagRuleById(Long id);

    /**
     * 获取标签树
     * 
     * @return
     */
    public List<ClassNodeInfo> getTagTree(Long domainId);

    /**
     * 根据标签预览CI数据
     * 
     * @param bean
     * @return
     */
    public Page<CcCiInfo> getCIInfoListByTag(ESTagSearchBean bean);

    /**
     * 删除标签
     * 
     * @param tagId
     * @return
     */
    public Integer deleteById(Long tagId);

    /**
     * 获取属性值列表
     * 
     * @param searchBean
     * @return
     */
    public Page<String> getAttrValuesBySearchBean(Long domainId, ESAttrAggBean searchBean);

    /**
     * 更改标签所属目录
     * 
     * @param tagInfo
     * @return
     */
    public Boolean changeTagDir(ESCITagInfo tagInfo);

    /**
     * 根据id集合查询标签规则
     *
     * @param ids tagId集合
     * @return 标签规则
     */
    List<ESCITagInfo> getCITagRuleByIds(List<Long> ids);

    /**
     * 校验ci属性是否符合标签规则
     * @param rules 规则
     * @param attrs 属性
     * @param classInfo 分类信息
     * @return 符合true/不符false
     */
    boolean checkAttrByRules(List<ESTagRuleInfo> rules, JSONObject attrs, ESCIClassInfo classInfo);

    /**
     * 校验ci属性是否符合标签规则
     * @param rules 规则
     * @param attrs 属性
     * @param classInfo 分类信息
     * @return 属性填写规则map<属性字段名-规则>
     */
    Map<String, String> getCheckRulesInfo(List<ESTagRuleInfo> rules, JSONObject attrs, ESCIClassInfo classInfo);

    /**
     * 根据标签规则拼装查询条件
     * @param tag 标签规则
     * @return 查询条件
     */
    QueryBuilder getQueryByTag(ESCITagInfo tag);
}
