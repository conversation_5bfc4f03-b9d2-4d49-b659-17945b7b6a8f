package com.uinnova.product.eam.comm.dto;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.comm.model.AppSystemQueryConParam;
import lombok.Data;

import java.util.List;
@Data
public class ConfigListDto {

    @Comment("分类属性名称")
    private String title;

    @Comment("属性类型: 3=短文本(<=200) 4=长文本(<=1000) 6=枚举 7=日期 8=字典  11=关联属性")
    private Integer proType;

    @Comment("条件内容")
    private List<AppSystemQueryConParam> conditions;
}
