package com.uinnova.product.eam.model.cj.vo;

import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @description:
 * @author: Lc
 * @create: 2022-07-08 14:22
 */
@Data
public class HandlePlanChapterVO implements Serializable {

    @NotNull(message = "方案主键不能为空!")
    private Long planId;
    @NotNull(message = "章节主键不能为空!")
    private Long chapterId;
    @NotNull(message = "锁不能为空!")
    /** 1:加锁 0:解锁 */
    private Integer lock;

}
