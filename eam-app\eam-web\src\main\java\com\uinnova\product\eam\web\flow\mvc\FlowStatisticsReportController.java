package com.uinnova.product.eam.web.flow.mvc;

import jakarta.annotation.Resource;

import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.binary.framework.web.RemoteResult;
import com.uinnova.product.eam.model.FlowOperationStatisticsDto;
import com.uinnova.product.eam.service.FlowStatisticsReportService;
import com.uinnova.product.eam.service.ProcessOperationService;

/**
 * 流程管理/统计报表相关接口
 *
 * <AUTHOR>
 * @since 2024/12/4 下午14:07
 */
@RestController
@RequestMapping("/flowManager/flowStatistics/")
public class FlowStatisticsReportController {
	@Resource
	FlowStatisticsReportService flowStatisticsReportService;

    /**
     * 获取数据统计
     * @return
     */
    @GetMapping("getFlowDataStatistics")
    public RemoteResult getFlowDataStatistics(){
        return new RemoteResult(flowStatisticsReportService.getFlowDataStatistics());
    }
    /**
     * 获取流程数统计
     * 
     */
    @GetMapping("getFlowNumStatistics")
    public RemoteResult getFlowNumStatistics(){
        return new RemoteResult(flowStatisticsReportService.getFlowNumStatistics());
    }
    /**
     * 获取绩效数统计
     * 
     */
    @GetMapping("getFlowPerformanceNumStatistics")
    public RemoteResult getFlowPerformanceNumStatistics(){
        return new RemoteResult(flowStatisticsReportService.getFlowPerformanceNumStatistics());
    }
    
    /**
     * 获取末级流程活动数据
     * @return
     */
    @GetMapping("getFlowActivityNumData")
    public RemoteResult getFlowActivityNumData() {
        return new RemoteResult(flowStatisticsReportService.getFlowActivityNumData());
    }
    /**
     * 占比统计
     */
    @GetMapping("getFlowRatioStatistics")
    public RemoteResult getFlowRatioStatistics() {
        return new RemoteResult(flowStatisticsReportService.getFlowRatioStatistics());
    }
    /**
     * 流程情况统计
     */
    @GetMapping("getFlowSituationStatistics")
    public RemoteResult getFlowSituationStatistics() {
        return new RemoteResult(flowStatisticsReportService.getFlowSituationStatistics());
    }
    /**
     * 活动运行时长
     *
     * @param dto
     * @return
     */
    @GetMapping("getActivityRuningDuration")
    public RemoteResult getActivityRuningDuration(String sourceId) {
        Assert.notNull(sourceId,"系统来源id不能为空");
        FlowOperationStatisticsDto dto = new FlowOperationStatisticsDto();
        dto.setSourceId(sourceId);
        return new RemoteResult(flowStatisticsReportService.getAllActivityRuningDuration(dto));
    }
    
}
