package com.uinnova.product.eam.service.dix.api;

import com.uinnova.product.eam.comm.model.es.AppSystemSyncRecord;

/**
 * 应用系统对接记录(DDM)
 * <AUTHOR>
 */
public interface AppSystemSyncRecordSvc {
    /**
     * 保存或更新
     */
    Long saveOrUpdate(AppSystemSyncRecord record);

    /**
     * 根据系统id查询最新推送记录
     * @param systemId 系统id
     * @return 记录
     */
    AppSystemSyncRecord getMaxVersionRecordBySystemId(String systemId);
}
