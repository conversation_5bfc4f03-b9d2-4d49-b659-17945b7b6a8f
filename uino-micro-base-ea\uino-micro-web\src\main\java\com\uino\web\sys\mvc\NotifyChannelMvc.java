package com.uino.web.sys.mvc;

import java.util.List;
import java.util.Set;


import com.uino.bean.permission.base.SysUser;
import com.uino.init.api.ApiResult;
import com.uino.init.api.ApiVersion;
import com.uino.util.sys.SysUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import com.uino.bean.sys.base.NotifyChannel;
import com.uino.bean.sys.business.NotifyChannelReqDto;
import com.uino.bean.sys.business.NotifyData;
import com.uino.api.client.sys.INotifyChannelApiSvc;
import org.springframework.web.bind.annotation.RestController;

@ApiVersion(1)
@Api(value = "通知渠道", tags = {"通知服务"})
@RestController
@RequestMapping("/sys/notifyChannel")
public class NotifyChannelMvc {

    @Autowired
    private INotifyChannelApiSvc notifyChannelApi;

    @PostMapping("save")
    @ApiOperation(value = "保存通知渠道")
    public ApiResult<NotifyChannel> save(@RequestBody NotifyChannel saveInfo) {
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        saveInfo.setDomainId(currentUserInfo.getDomainId());
        NotifyChannel notifyChannel = notifyChannelApi.save(saveInfo);
        return ApiResult.ok(this).data(notifyChannel);
    }

    @PostMapping("delete")
    @ApiOperation(value = "删除通知渠道")
    public ApiResult<Boolean> delete(@RequestBody Set<Long> ids) {
        notifyChannelApi.delete(ids);
        return ApiResult.ok(this).data(true);
    }

    @PostMapping("search")
    @ApiOperation(value = "查询通知渠道")
    public ApiResult<List<NotifyChannel>> search(@RequestBody NotifyChannelReqDto searchDto) {
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        searchDto.setDomainId(currentUserInfo.getDomainId());
        List<NotifyChannel> notifyChannels = notifyChannelApi.search(searchDto);
        return ApiResult.ok(this).data(notifyChannels);
    }

    @PostMapping("sendNotify")
    @ApiOperation(value = "发送渠道通知")
    public ApiResult<Boolean> search(@RequestBody NotifyData notifyData) {
        boolean sendNotify = notifyChannelApi.sendNotify(notifyData);
        return ApiResult.ok(this).data(sendNotify);
    }
}
