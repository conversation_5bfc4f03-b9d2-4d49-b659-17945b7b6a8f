package com.uino.init.http.safe;

import java.io.IOException;

import jakarta.servlet.Filter;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

/**
 * web安全拦截器虚类
 * 
 * <AUTHOR>
 *
 */
public abstract class WebSafeFilter implements Filter {

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        HttpServletResponse httpResponse = (HttpServletResponse) response;
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        if (support(httpRequest)) {
            boolean validRes = validity(httpRequest);
            if (!validRes) {
                doError(httpRequest, httpResponse);
                return;
            }
        }
        chain.doFilter(request, response);
    }

    /**
     * 异常处理,默认返回425代表未通过安全验证
     * 
     * @param httpRequest
     * @param httpResponse
     * @throws IOException
     */
    protected void doError(HttpServletRequest httpRequest, HttpServletResponse httpResponse) throws IOException {
        // httpResponse.sendError(425, "Too Early");
        httpResponse.setStatus(425);
    }

    /**
     * 验证本次请求合法性
     * 
     * @param httpRequest
     * @param httpResponse
     * @return
     */
    protected abstract boolean validity(HttpServletRequest httpRequest);

    /**
     * 是否支持
     * 
     * @return
     */
    protected abstract boolean support(HttpServletRequest httpRequest);
}
