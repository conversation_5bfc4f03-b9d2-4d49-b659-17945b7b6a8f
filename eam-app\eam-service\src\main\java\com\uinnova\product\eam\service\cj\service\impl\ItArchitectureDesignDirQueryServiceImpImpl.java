package com.uinnova.product.eam.service.cj.service.impl;

import com.binary.core.util.BinaryUtils;
import com.uinnova.product.eam.comm.model.es.EamCategory;
import com.uinnova.product.eam.model.cj.domain.PlanDesignInstance;
import com.uinnova.product.eam.service.EamCategorySvc;
import com.uinnova.product.eam.service.cj.service.ItArchitectureDesignDirQueryService;
import com.uino.bean.cmdb.base.LibType;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * IT架构设计文件夹查询service实现
 * <p>因为文件夹伏羲操作,所以只提供一个查询接口</p>
 *
 * <AUTHOR>
 */
@Service
public class ItArchitectureDesignDirQueryServiceImpImpl implements ItArchitectureDesignDirQueryService {
    @Resource
    private EamCategorySvc categorySvc;

    /**
     * 获取一个map,key是文件夹Id,value表示文件夹是否存在,true存在,false不存在(被删除)
     *
     * @param planList 方案实例的List,方案的dirId为必填项
     * @param fromTemp 文件夹id是否从备份字段获取, 从回收站恢复时使用备份字段传入true,删除到回收站时传入false, 或者其他场景时传入false
     * @return key是文件夹Id, value表示文件夹是否存在, true存在, false不存在(被删除)
     */
    @Override
    public Map<Long, Boolean> getDirExistMap(Collection<PlanDesignInstance> planList, Boolean fromTemp) {
        Map<Long, Boolean> result = new HashMap<>();
        if (BinaryUtils.isEmpty(planList)) {
            return Collections.emptyMap();
        }
        List<Long> dirIdList;
        if (fromTemp != null && fromTemp) {
            dirIdList = planList.stream().map(plan -> plan.getTemp().getDirId()).filter(Objects::nonNull).collect(Collectors.toList());
        } else {
            dirIdList = planList.stream().map(PlanDesignInstance::getDirId).filter(Objects::nonNull).collect(Collectors.toList());
        }
        if (BinaryUtils.isEmpty(dirIdList)) {
            return Collections.emptyMap();
        }
        if (dirIdList.contains(0L)) {
            result.put(0L,true);
            dirIdList.remove(0L);
        }
        if (CollectionUtils.isEmpty(dirIdList)) {
            return result;
        }

        List<EamCategory> categoryList = categorySvc.getByIds(dirIdList, LibType.PRIVATE);
        for (EamCategory category : categoryList) {
            result.put(category.getId(), category.getDataStatus()==1);
        }
        return result;
    }
}
