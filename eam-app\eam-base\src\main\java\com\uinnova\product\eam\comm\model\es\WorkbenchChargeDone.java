package com.uinnova.product.eam.comm.model.es;

import com.binary.framework.bean.annotation.Comment;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class WorkbenchChargeDone {

    private Long id;

    @Comment("1:表示工作流(方案审批) 2：表示消息(视图更新) 3：表示视图审批 4:模型审批 5:表示填写流程任务  6:流程审批  7：流程签发")
    private Integer type;

    @Comment("1：表示代办 2：表示已完成")
    private Integer action;

    @Comment("内容")
    private String content;

    @Comment("关键字")
    private List<EamNoticeKeyword> keywords;

    private String planId;

    private List<String> diagramIds;

    private Integer sourceType;

    @Comment("业务主键，入库幂等性处理")
    private String businessId;

    private String taskId;

    private String processInstanceId;

    private String processDefinitionKey;

    private String businessKey;

    private String processInstanceName;

    private String taskName;

    private String taskDefinitionKey;

    private String description;

    private String userId;

    private String submitter;

    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss", timezone = "GMT+8")
    private Date processStartTime;

    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss", timezone = "GMT+8")
    private Date taskCreateTime;

    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss", timezone = "GMT+8")
    private Date taskEndTime;

    private String currentAssignees;

    /**
     * 流程是否结束，true是结束，false是未结束
     */
    private Boolean processEnd = Boolean.FALSE;

    private int errorCode;

    @Comment("领域id")
    private Long domainId;

    @Comment("创建时间")
    private Long createTime;

    @Comment("创建时间转换")
    private Long conversionCreateTime;

    @Comment("修改时间")
    private Long modifyTime;

    @Comment("修改时间转换")
    private Long conversionModifyTime;

    @Comment("创建人[CREATOR]")
    private String creator;

    @Comment("修改人[MODIFIER]")
    private String modifier;

    @Comment("任务的类别。这是一个可选字段，允许将任务“标记”为属于某个类别")
    private String category;

}
