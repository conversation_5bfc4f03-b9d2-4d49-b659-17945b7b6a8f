package com.uino.bean.sys.base;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.util.Assert;

import com.binary.framework.bean.annotation.Comment;
import com.uino.bean.permission.business.IValidDto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Comment("登录验证集成")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value="登录集成类",description = "登录集成信息")
public class LoginAuthConfig implements Serializable, IValidDto {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value="ID",example = "123")
    @Comment("ID[ID]")
    private Long id;

    @ApiModelProperty(value="协议代码",example = "LDAP")
    @Comment("协议代码[PROTO_CODE]    协议代码: LDAP")
    @Builder.Default
    private String protoCode = "LDAP";

    @ApiModelProperty(value="协议名称",example = "uino")
    @Comment("协议名称[PROTO_NAME]")
    private String protoName;

    @ApiModelProperty(value="协议状态,协议状态:0=无效 1=有效",example = "0")
    @Comment("协议状态[PROTO_STATUS]    协议状态:0=无效 1=有效")
    @Builder.Default
    private Integer protoStatus = 0;

    @ApiModelProperty(value="协议优先级",example = "5")
    @Builder.Default
    @Comment("协议状态[PROTO_STATUS]    协议优先级")
    private Integer priority = 5;

    @ApiModelProperty(value="连接超时时间,单位秒",example = "5")
    @Comment("连接超时时间,单位秒,为空或小于10时使用全局设置")
    private Integer timeout;

    @ApiModelProperty(value="创建时间")
    @Comment("创建时间")
    private Long createTime;

    @ApiModelProperty(value="修改时间")
    @Comment("修改时间")
    private Long lastModifyTime;

    @ApiModelProperty(value="通过ldap用户创建我方用户时的属性映射")
    @Comment("通过ldap用户创建我方用户时的属性映射")
    private LdapUserMapping ldapUserMapping;


    @ApiModelProperty(value="ldap模式下配置方式")
    @Comment("ldap模式下配置方式")
    private LoginLdapAuthConfig ldapAuthConfig;

    @ApiModelProperty(value="测试连接使用用户名",example = "test01")
    @Comment("测试连接使用用户名")
    private String testUserName;

    @ApiModelProperty(value="测试连接使用密码",example = "sjwfeo345")
    @Comment("测试连接使用密码")
    private String testPassword;

    @Override
    public void valid() {
        if (ldapAuthConfig != null) {
            ldapAuthConfig.valid();
        }
        if (ldapUserMapping != null) {
            ldapUserMapping.valid();
        }
        Assert.notNull(getProtoName(), "X_PARAM_NOT_NULL${name:protoName}");
        Assert.notNull(getProtoStatus(), "X_PARAM_NOT_NULL${name:protoStatus}");
    }

}
