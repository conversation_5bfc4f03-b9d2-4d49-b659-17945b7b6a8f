package com.uino.bean.permission.business.request;

import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;

import org.springframework.util.Assert;

import com.binary.core.util.BinaryUtils;
import com.uino.bean.permission.business.IValidDto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 增加功能模块权限请求dto
 * 
 * <AUTHOR>
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AddModuleAuthReuqestDto implements IValidDto, Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 模块ids
	 */
	@Builder.Default
	private Set<Long> moduleIds = new HashSet<>();
	/**
	 * 要添加权限的用户ids
	 */
	@Builder.Default
	private Set<Long> userIds = new HashSet<>();
	/**
	 * 要添加权限的角色ids
	 */
	@Builder.Default
	private Set<Long> roleIds = new HashSet<>();

	@Override
	public void valid() {
		Assert.isTrue(!BinaryUtils.isEmpty(moduleIds), "X_PARAM_NOT_NULL${name:moduleIds}");
		Assert.isTrue(!BinaryUtils.isEmpty(userIds) || !BinaryUtils.isEmpty(roleIds),
				"X_PARAM_NOT_NULL${name:roleIds/userIds}");
	}
}
