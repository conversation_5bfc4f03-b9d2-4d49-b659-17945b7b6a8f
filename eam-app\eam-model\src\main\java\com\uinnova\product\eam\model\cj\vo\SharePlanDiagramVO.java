package com.uinnova.product.eam.model.cj.vo;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.model.diagram.ESDiagramShareVO;
import com.uinnova.product.eam.model.enums.AssetType;
import lombok.Data;

import java.io.Serializable;

/**
 * @description:
 * @author: Lc
 * @create: 2022-08-03 13:50
 */
@Data
public class SharePlanDiagramVO implements Serializable {
    @Comment("方案信息")
    private PlanDesignInstanceResult plan;

//    private transient ESDiagramShareRecordResult diagram;
    @Comment("视图信息")
    private ESDiagramShareVO diagram;

    @Comment("是否被关注：0未关注 1关注")
    private Integer isAttention = 0;

    /** 修改实现，排序使用 */
    private Long modifyTime;
    @Comment("用作前端区分资源类型")
    private AssetType assetType;
    @Comment("是否已发布：0：未发布，1：已发布，2：审批中")
    private int releaseStatus;
}
