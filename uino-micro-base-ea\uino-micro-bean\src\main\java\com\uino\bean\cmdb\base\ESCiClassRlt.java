package com.uino.bean.cmdb.base;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

/**
 * 
 * <AUTHOR>
 *
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value="分类关系类",description = "分类关系信息")
public class ESCiClassRlt implements Serializable {

    private static final long serialVersionUID = -7789742102737229596L;

	@ApiModelProperty(value = "关系id",example = "123")
    private Long id;

	@ApiModelProperty(value = "分类id", example = "123", required = true)
    private Long classId;

	@ApiModelProperty(value = "源分类id", example = "123", required = true)
    private Long sourceClassId;

	@ApiModelProperty(value = "目标分类id", example = "123", required = true)
    private Long targetClassId;

    @ApiModelProperty(value="分类领域id",example="123")
	private Long domainId;

    @ApiModelProperty(value="创建者",example="mike")
    private String creator;

    @ApiModelProperty(value="修改者",example="tom")
    private String modifier;

   @ApiModelProperty(value="创建时间")
    private Long createTime;

   @ApiModelProperty(value="修改时间")
   private Long modifyTime;

    @Override
    public boolean equals(Object obj) {
        if (obj == null) {
            return false;
        } else if (!(obj instanceof ESCiClassRlt)) {
            return false;
        } else if (this == obj) {
            return true;
        } else {
            ESCiClassRlt clsRltobj = (ESCiClassRlt) obj;
            return this.getClassId().equals(clsRltobj.getClassId())
                    && this.getSourceClassId().equals(clsRltobj.getSourceClassId())
                    && this.getTargetClassId().equals(clsRltobj.getTargetClassId());
        }
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getClassId() {
        return classId;
    }

    public void setClassId(Long classId) {
        this.classId = classId;
    }

    public Long getSourceClassId() {
        return sourceClassId;
    }

    public void setSourceClassId(Long sourceClassId) {
        this.sourceClassId = sourceClassId;
    }

    public Long getTargetClassId() {
        return targetClassId;
    }

    public void setTargetClassId(Long targetClassId) {
        this.targetClassId = targetClassId;
    }

    public Long getDomainId() {
        return domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }
}
