package com.uinnova.product.eam.model;

import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;
import com.uino.bean.permission.base.SysUser;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 架构决策
 * <AUTHOR>
 */
@Data
public class ArchDecisionResponse implements EntityBean {
    @Comment("主键")
    private Long id;
    @Comment("标题")
    private String title;
    @Comment("描述")
    private String desc;
    @Comment("status:状态(枚举：1待受理，2重新申请，3待决策，4待发布，5已发布)")
    private Integer status;
    @Comment("备选方案或设想")
    private String planInfo;
    @Comment("涉及的应用系统")
    private List<Map<String, String>> systemList;
    @Comment("涉及的产品")
    private List<Map<String, String>> productList;
    @Comment("涉及的中心及团队")
    private List<Map<String, String>> teamList;
    @Comment("申请人")
    private SysUser proposer;
    @Comment("受理人")
    private SysUser acceptor;

    @Comment("主持人")
    private SysUser compere;
    @Comment("参与人")
    private List<SysUser> players;
    @Comment("外脑")
    private String helper;
    @Comment("决策结果")
    private String result;
    @Comment("驳回原因")
    private String reason;
    @Comment("跟踪机制")
    private String track;
    @Comment("决策编号")
    private String number;
    @Comment("研发阶段类型")
    private String researchStage;
    @Comment("决策输出类型")
    private String decisionStage;
    @Comment("备注")
    private String remarks;
    @Comment("发布人")
    private SysUser publisher;

    @Comment("审批动作:同意true or 驳回false")
    private Boolean success = true;
    @Comment("审批意见")
    private String opinion;
    @Comment("任务id")
    private String taskId;
    @Comment("流程实例id")
    private String processInstanceId;

    @Comment("申请发起时间")
    private Long createTime;
    @Comment("受理完成时间")
    private Long acceptTime;
    @Comment("决策修改时间")
    private Long modifyTime;
}
