package com.uinnova.product.eam.comm.diagram;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.base.domain.DiagramQ;
import com.uinnova.product.eam.comm.model.CVcDiagram;
import lombok.Data;

@Data
public class DiagramDirInfoDTO extends CVcDiagram {
    private static final long serialVersionUID = 1L;

    @Comment("父文件夹ID")
    private Long dirId;

    @Comment("目录类型")
    private Integer dirType;

    @Comment("模糊查询字段信息")
    private String like;

    @Comment("筛选类型[0:初始化,1:单图,2:组合视图,3:文件夹,4:我的模版]")
    private Integer type;

    @Comment("查询类型：0:名称,1:作者,2:标签,3:CI")
    private Integer queryType;

    @Comment("tagId")
    private Long tagId;

    @Comment("视图id")
    private Long diagramId;

    @Comment("需要查询的视图附加信息")
    private DiagramQ[] diagramQs;

    @Comment("是否是我协作的查询")
    private Integer isConcert;

    @Comment("按照字段排序")
    private String orders;

    @Comment("查询每页大小")
    private Integer pageSize;

    @Comment("查询页码")
    private Integer pageNum;

    @Comment("查询入口 本地 公开")
    private Integer isOpen;

    @Comment("是否发布")
    private Integer releaseStatus;

    //是否带视图权限
    private boolean auth=false;
}
