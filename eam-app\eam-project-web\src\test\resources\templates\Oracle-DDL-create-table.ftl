<#list data as entity>
CREATE TABLE ${entity.key}(
<#if entity.attrList?? && (entity.attrList?size>0)>
<#list entity.attrList as item>
    ${item.name} ${item.type}<#if item.length??>(${item.length}<#if item.precision??>,${item.precision}</#if>)</#if><#if item.notEmpty> NOT NULL</#if><#if item.defaultVal??> DEFAULT '${item.defaultVal}'</#if><#sep>,
</#list>
</#if>
<#if entity.pkList?? && (entity.pkList?size>0)>
,${'\n'}${'\t'}PRIMARY KEY (<#list entity.pkList as item>${item}<#sep>,</#list>)
</#if>
);
COMMENT ON TABLE ${entity.key} IS '${entity.name}';
<#list entity.attrList as item>
<#if item.comment??>
COMMENT ON COLUMN ${entity.key}.${item.name} IS '${item.comment}';
</#if>
</#list>


</#list>