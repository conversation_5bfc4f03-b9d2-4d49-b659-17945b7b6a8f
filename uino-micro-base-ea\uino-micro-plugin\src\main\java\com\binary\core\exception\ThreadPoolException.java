package com.binary.core.exception;

public class Thread<PERSON>oolException extends CoreException {
	private static final long serialVersionUID = 1L;

	public ThreadPoolException() {
		super();
	}
	
	public ThreadPoolException(String message) {
		super(message);
	}
	
	public ThreadPoolException(Throwable cause) {
		super(cause);
	}
	
	public ThreadPoolException(String message, Throwable cause) {
		super(message, cause);
	}
	
	
}


