package com.uinnova.product.eam.service.es;

import com.uinnova.product.eam.comm.model.es.FlowableUser;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class FlowableUserFilterDao extends AbstractESBaseDao<FlowableUser, FlowableUser> {

    @Override
    public String getIndex() {
        return "uino_flowable_user";
    }

    @Override
    public String getType() {
        return "_doc";
    }
}
