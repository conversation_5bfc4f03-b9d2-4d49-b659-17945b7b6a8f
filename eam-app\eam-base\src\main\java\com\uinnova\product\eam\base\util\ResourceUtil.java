package com.uinnova.product.eam.base.util;

import com.binary.core.io.support.FileResource;
import com.uinnova.product.eam.base.model.FileProResource;
import com.uinnova.product.eam.base.model.FileResourceMeta;
import org.apache.poi.ss.formula.functions.T;

import java.io.File;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

public class ResourceUtil {

    public static FileResource installFile(FileResourceMeta resourceMeta) {
        File file = new File(resourceMeta.getResPath());
        return new FileResource(file);
    }

    public static FileProResource[] installFiles(List<FileResourceMeta> resourceMetas, Function<FileResourceMeta, FileProResource> mapper) {
        List<FileProResource> files = resourceMetas.stream().map(mapper).collect(Collectors.toList());
        return files.toArray(new FileProResource[files.size()]);
    }

}
