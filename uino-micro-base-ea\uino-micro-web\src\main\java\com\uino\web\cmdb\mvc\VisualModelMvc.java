package com.uino.web.cmdb.mvc;

import java.util.List;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import com.uino.init.api.ApiResult;
import com.uino.init.api.ApiVersion;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uino.util.sys.SysUtil;
import com.uino.bean.cmdb.base.ESVisualModel;
import com.uino.bean.permission.base.SysUser;
import com.uino.api.client.cmdb.IVisualModelApiSvc;

@ApiVersion(1)
@Api(value = "可视化模型", tags = {"可视化模型"})
@RestController
@RequestMapping(value = "/cmdb/visualModel")
public class VisualModelMvc {

	@Autowired
	private IVisualModelApiSvc svc;
	
	/**
     * 查询可视化模型列表
     * 
     * @param domainId
     * @return
     */
	@ApiOperation("查询可视化模型列表")
	@PostMapping("queryVisualModels")
    @ModDesc(desc = "查询可视化模型列表", pDesc = "无", rDesc = "可视化模型列表", rType = List.class, rcType = ESVisualModel.class)
	public ApiResult<List<ESVisualModel>> queryVisualModels(HttpServletRequest request,HttpServletResponse response) {
		Long domainId = getDomainId();
		List<ESVisualModel> models = svc.queryVisualModels(domainId);

		return ApiResult.ok(this).data(models);
	}
	
	/**
     * 保存可视化模型
     * 
     * @param model
     * @return
     */
	@ApiOperation("保存可视化模型")
	@PostMapping("saveVisualModel")
    @ModDesc(desc = "保存可视化模型", pDesc = "可视化模型数据传输对象", pType = ESVisualModel.class, rDesc = "可视化模型id", rType = Long.class)
	public ApiResult<Long> saveVisualModel(@RequestBody ESVisualModel model, HttpServletRequest request,
			HttpServletResponse response) {
		Long domainId = getDomainId();
		model.setDomainId(domainId);
		Long id = svc.saveVisualModel(model);
		return ApiResult.ok(this).data(id);
	}
	
	/**
     * 修改可视化模型名称
     * 
     * @param model
     * @return
     */
	@ApiOperation("修改可视化模型名称")
	@PostMapping("updateVisualModelName")
    @ModDesc(desc = "修改可视化模型名称", pDesc = "可视化模型数据传输对象", pType = ESVisualModel.class, rDesc = "操作是否成功", rType = Boolean.class)
	public ApiResult<Boolean> updateVisualModelName(@RequestBody ESVisualModel model, HttpServletRequest request,
			HttpServletResponse response) {

		svc.updateVisualModelName(model);

		return ApiResult.ok(this).data(true);
	}
	
	/**
     * 删除可视化模型
     * 
     * @param id
     * @return
     */
	@ApiOperation("删除可视化模型")
	@PostMapping("deleteVisualModel")
    @ModDesc(desc = "根据id删除可视化模型", pDesc = "模型id", pType = Long.class, rDesc = "操作是否成功", rType = Boolean.class)
	public ApiResult<Boolean> deleteVisualModel(@RequestBody Long id, HttpServletRequest request,
			HttpServletResponse response) {
		svc.deleteVisualModel(id);
		return ApiResult.ok(this).data(true);
	}
	
	private Long getDomainId() {
		Long domainId = 1L;
		try {
			SysUser user = SysUtil.getCurrentUserInfo();
			domainId = user.getDomainId();
		} catch (Exception e) {
		}
		return domainId;
	}
	
}
