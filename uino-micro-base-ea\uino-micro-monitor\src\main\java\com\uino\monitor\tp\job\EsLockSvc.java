package com.uino.monitor.tp.job;

import com.alibaba.fastjson.JSONObject;
import com.binary.core.util.BinaryUtils;
import com.uino.dao.AbstractESBaseDao;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.delete.DeleteRequest;
import org.elasticsearch.action.delete.DeleteResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.index.IndexResponse;
import org.elasticsearch.action.support.WriteRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.ConstantScoreQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.rest.RestStatus;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;

/**
 * @author: weixuesong
 * @date: 2020/10/22 13:40
 **/
@Service
@Slf4j
@Deprecated
public class EsLockSvc extends AbstractESBaseDao<JSONObject, String> {
    @Override
    public String getIndex() {
        return "uino_es_lock";
    }

    @Override
    public String getType() {
        return "uino_es_lock";
    }

    /**
     * 上锁
     *
     * <AUTHOR>
     * @date 2020年2月7日
     * @param lockName
     * @param lockTime
     * @return
     */
    public boolean tryLock(String lockName, long lockTime) {
        boolean flag = false;
        IndexRequest indexRequest = this.getIndexRequest(lockName);
        JSONObject obj = new JSONObject();
        obj.put("lockTime", lockTime);
        obj.put("lockName", lockName);
        obj.put("createTime", System.currentTimeMillis());
        indexRequest.setRefreshPolicy(WriteRequest.RefreshPolicy.IMMEDIATE);
        indexRequest.source(obj);
        indexRequest.create(true);
        IndexResponse inResponse;
        try {
            inResponse = getClient().index(indexRequest, RequestOptions.DEFAULT);
            if (inResponse.status() == RestStatus.CREATED) {
                flag = true;
            }
        } catch (Exception e) {
            log.info("{}已被锁定", lockName);
        }
        if (!flag) {
            try {
                // 判断是否需要解锁
                BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
                ConstantScoreQueryBuilder constantScoreQuery = QueryBuilders.constantScoreQuery(boolQuery);
                boolQuery.must(QueryBuilders.termQuery("lockName.keyword", lockName));
                List<JSONObject> locks = getListByQuery(constantScoreQuery);
                if (!BinaryUtils.isEmpty(locks)) {
                    JSONObject lockObj = locks.get(0);
                    long createTime = lockObj.getLongValue("createTime");
                    if (System.currentTimeMillis() - createTime >= lockTime) {
                        // 删除锁信息
                        log.info("锁:{}已超时, 删除此锁", lockName);
                        breakLock(lockName);
                    }
                }
            } catch (Exception e) {
                log.error("删除锁:{}异常:", lockName, e);
            }
        }
        return flag;
    }

    /**
     * 解锁
     *
     * <AUTHOR>
     * @date 2020年2月7日
     * @param lockName
     * @return
     */
    public boolean breakLock(String lockName) {
        try {
            return deleteByID(lockName);
        } catch (Exception e) {
            log.error("解锁:{}失败", lockName, e);
        }
        return false;
    }

    /**
     * 删除对象
     *
     * @param id 唯一标识ID
     * @return
     */
    public boolean deleteByID(String id) {
        boolean flag = false;
        DeleteRequest deleteRequest = this.getDeleteRequest(id);
        deleteRequest.setRefreshPolicy(WriteRequest.RefreshPolicy.IMMEDIATE);
        try {
            DeleteResponse response = getClient().delete(deleteRequest, RequestOptions.DEFAULT);
            if (response.status().equals(RestStatus.OK)) {
                flag = true;
            }
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
        return flag;
    }

}
