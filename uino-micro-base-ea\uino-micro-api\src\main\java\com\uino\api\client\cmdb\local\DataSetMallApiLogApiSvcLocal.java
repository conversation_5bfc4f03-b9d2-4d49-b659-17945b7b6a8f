package com.uino.api.client.cmdb.local;

import com.binary.jdbc.Page;
import com.uino.bean.cmdb.base.dataset.log.DataSetMallApiLog;
import com.uino.service.cmdb.dataset.microservice.IDataSetMallApiLogSvc;
import com.uino.api.client.cmdb.IDataSetMallApiLogApiSvc;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Classname DataSetMallApiLogApiSvcLocal
 * @Description TODO
 * @Date 2020/6/1 10:54
 * @Created by sh
 */
@Service
public class DataSetMallApiLogApiSvcLocal implements IDataSetMallApiLogApiSvc {
    @Autowired
    private IDataSetMallApiLogSvc dataSetMallApiLogService;

    @Override
    public Page<DataSetMallApiLog> findPage(String dataSetId, int pageNum, int pageSize, String respUserName, String respUserCode) {
        return dataSetMallApiLogService.findPage(dataSetId, pageNum, pageSize, respUserName, respUserCode);
    }
}
