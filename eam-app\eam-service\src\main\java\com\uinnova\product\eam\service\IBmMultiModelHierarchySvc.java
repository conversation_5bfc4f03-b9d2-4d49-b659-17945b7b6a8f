package com.uinnova.product.eam.service;

import com.binary.jdbc.Page;
import com.uinnova.product.eam.comm.model.es.EamMultiModelHierarchy;
import com.uinnova.product.eam.comm.model.es.EamMultiModelType;
import com.uinnova.product.eam.model.dto.EamMultiModelHierarchyDto;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;

/**
 * 多模型层级
 * <AUTHOR>
 */
public interface IBmMultiModelHierarchySvc {
    /**
     * 根据数据状态查询模型树集合
     * @param releaseState 发布状态：0未发布，1已发布
     * @param dataStatus 数据状态：0已删除，1正常
     * @return 模型树
     */
    List<EamMultiModelHierarchy> queryList(Integer releaseState, Integer dataStatus);
    /**
     * 查询多模型树列表
     * @param pageNum
     * @param pageSize
     * @param like
     * @return
     */
    Page<EamMultiModelHierarchy> queryList(Integer pageNum, Integer pageSize, String like,Integer releaseState, Integer modelType);

    /**
     * 保存或修改模型树
     * @param multiModelHierarchy
     * @return
     */
    Long saveOrUpdate(EamMultiModelHierarchyDto multiModelHierarchy);

    /**
     * 获取模型树类型列表
     * @return
     */
    Collection<EamMultiModelType> queryModelTypeList();

    /**
     * 添加模型树类型
     * @param modelTypeList
     * @return
     */
    Integer saveModelType(List<EamMultiModelType> modelTypeList);

    /**
     * 删除模型树类型
     * @param id
     */
    Integer deleteModelType(Long id);

    /**
     * 发布模型树
     * @param modelId
     * @return
     */
    Long releaseModel(Long modelId);

    /**
     * 取消发布模型
     * @param modelId
     * @return
     */
    Long cancelReleaseModel(Long modelId);

    /**
     * 删除模型
     * @param modelId
     */
    Integer deleteModel(Long modelId);

    Integer saveOrUpdateBatch(List<EamMultiModelHierarchy> modelHierarchies);

    /**
     * 根据id获取模型树
     * @param id
     * @return
     */
    EamMultiModelHierarchy getModelById(Long id);

    /**
     * 复制模型树
     * @param id
     * @return
     */
    Integer copeModel(Long id);

    /**
     * 刷新初始化模型树字段
     * */
    Integer flashModuleData();

    /**
     * 获取数据模型
     * @return 模型树map
     */
    Map<Long, EamMultiModelHierarchy> getDataModel();


    Set<String> getTemplateRegexSet(List<String> inputList, Pattern p);

    /**
     * 根据id获取模型树
     * @param ids
     * @return
     */
    List<EamMultiModelHierarchy> getModelByIds(Set<Long> ids);


}
