package com.uino.cmdb.dir;

import static org.junit.Assert.assertEquals;

import java.util.ArrayList;
import java.util.List;

import jakarta.servlet.http.Cookie;

import org.junit.After;
import org.junit.AfterClass;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.uinnova.product.vmdb.comm.model.ci.CCcCiClassDir;
import com.uino.dao.cmdb.ESDirSvc;
import com.uino.service.cmdb.microservice.impl.DirSvc;
import com.uino.bean.cmdb.base.CcCiClassDir;

@RunWith(SpringJUnit4ClassRunner.class)
public class DirSvcTest {

    @InjectMocks
    DirSvc dirSvc;

    private ESDirSvc esDirSvc;

    @BeforeClass
    public static void setUpBeforeClass() throws Exception {
        MockHttpServletRequest request = new MockHttpServletRequest();
        Cookie cookie = new Cookie("token", "ca14ed042fe7912426b484f6ea5c754b4fb51ae4a72037e9127da51743e0d1f9650e6e78ed4466ac970acb6a0b2f46fd26958c4ab2d115cc71b34ca8a02db24c");
        request.setCookies(cookie);
        ServletRequestAttributes attributes = new ServletRequestAttributes(request);
        RequestContextHolder.setRequestAttributes(attributes);
    }

    @AfterClass
    public static void tearDownAfterClass() throws Exception {}

    @Before
    public void setUp() throws Exception {
        esDirSvc = Mockito.mock(ESDirSvc.class);
        ReflectionTestUtils.setField(dirSvc, "esDirSvc", esDirSvc);

    }

    @After
    public void tearDown() throws Exception {}

    @Test
    public void testSaveOrUpdate() {
        Mockito.when(esDirSvc.getListByQuery(Mockito.any())).thenReturn(new ArrayList<>());
        Mockito.when(esDirSvc.getById(Mockito.anyLong())).thenReturn(new CcCiClassDir());
        Mockito.when(esDirSvc.saveOrUpdate(Mockito.any(CcCiClassDir.class))).thenReturn(123L);

        CcCiClassDir dir = new CcCiClassDir();
        dir.setId(123L);
        dir.setDirName("dirName");
        dir.setCiType(1);
        dir.setParentId(0L);
        Long res = dirSvc.saveOrUpdate(dir);
        assertEquals(123L, res.longValue());
    }

    @Test
    public void testDeleteById() {
        Mockito.when(esDirSvc.deleteById(Mockito.anyLong())).thenReturn(1);

        Integer res = dirSvc.deleteById(123L);
        assertEquals(1, res.intValue());
    }

    @Test
    public void testQueryDirList() {
        Mockito.when(esDirSvc.getSortListByCdt(Mockito.any(CCcCiClassDir.class), Mockito.anyList())).thenReturn(new ArrayList<>());

        List<CcCiClassDir> res = dirSvc.queryDirList(null, null, null);
        assertEquals(0, res.size());
    }

}
