package com.uinnova.product.eam.model.diagram;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.io.Serializable;

@Data
@Comment("用户信息")
public class SysOpInfo implements Serializable{

	private static final long serialVersionUID = 1L;
	
	@Comment("ID[ID]")
	private Long opId;
	
	@Comment("操作员编码[OP_CODE]")
	private String opCode;


	@Comment("操作员姓名[OP_NAME]")
	private String opName;
	
	@Comment("查看的权限")
	private Integer seeAuth;
	
	@Comment("编辑的权限")
	private Integer editAuth;

	@Comment("用户头像")
	private String custom3;


}
