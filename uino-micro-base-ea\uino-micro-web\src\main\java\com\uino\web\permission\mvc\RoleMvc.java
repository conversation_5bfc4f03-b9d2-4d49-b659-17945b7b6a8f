package com.uino.web.permission.mvc;

import java.util.List;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import com.uino.bean.permission.base.*;
import com.uino.bean.permission.query.CSysRoleDataModuleRlt;
import com.uino.util.sys.SysUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uinnova.product.vmdb.comm.doc.annotation.MvcDesc;
import com.uino.api.client.permission.IRoleApiSvc;
import com.uino.bean.permission.business.DataRole;
import com.uino.bean.permission.business.ModuleNodeInfo;
import com.uino.bean.permission.business.request.OptionUserModuleAuthRequestDto;
import com.uino.bean.permission.business.request.SaveRoleOrgRltRequestDto;
import com.uino.bean.permission.business.request.SaveRoleUserRltRequestDto;
import com.uino.bean.permission.query.CAuthBean;
import com.uino.bean.permission.query.SearchKeywordBean;
import com.uino.init.api.ApiResult;
import com.uino.init.api.ApiVersion;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@ApiVersion(1)
@Api(value = "角色管理", tags = {"角色管理"})
@RestController
@RequestMapping("/permission/role")
@MvcDesc(author = "cgj", desc = "角色管理")
public class RoleMvc {
	@Autowired
	private IRoleApiSvc roleApiSvc;

	/**
	 * 保存或修改
	 */
	@ApiOperation("保存或更新角色信息")
	@RequestMapping(value="/saveOrUpdate",method = RequestMethod.POST)
    @ModDesc(desc = "保存或更新角色信息", pDesc = "用户信息封装对象", pType = SysRole.class, rDesc = "角色id", rType = Long.class)
	public ApiResult<Long> saveOrUpdate(HttpServletRequest request, HttpServletResponse response, @RequestBody SysRole role) {
		SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
		role.setDomainId(currentUserInfo.getDomainId());
		Long id = roleApiSvc.saveOrUpdate(role);
		return ApiResult.ok(this).data(id);
	}

	/**
	 * 删除
	 */
	@ApiOperation("根据id删除角色")
	@RequestMapping(value = "/deleteById",method =RequestMethod.POST)
    @ModDesc(desc = "根据id删除角色", pDesc = "角色id", pType = Long.class, rDesc = "操作是否成功", rType = Boolean.class)
	public ApiResult<Boolean> deleteById(HttpServletRequest request, HttpServletResponse response, @RequestBody String body) {
		Long roleId = Long.parseLong(body.trim());
        roleApiSvc.deleteById(roleId);
		return ApiResult.ok(this).data(true);
	}

	/**
	 * 分页查询角色
	 */
	@ApiOperation("分页查询角色信息")
	@RequestMapping(value="/getRolePageByQuery",method=RequestMethod.POST)
    @ModDesc(desc = "分页查询角色信息", pDesc = "查询条件", pType = SearchKeywordBean.class, rDesc = "分页结果", rType = Page.class, rcType = SysRole.class)
	public ApiResult<Page<SysRole>> getRolePageByQuery(HttpServletRequest request, HttpServletResponse response,
			@RequestBody SearchKeywordBean bean) {
		SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
		bean.setDomainId(currentUserInfo.getDomainId());
		Page<SysRole> rs = roleApiSvc.getRolePageByQuery(bean);
		return ApiResult.ok(this).data(rs);
	}

    /**
     * 角色添加用户
     */
    @ApiOperation("为角色分配用户")
    @RequestMapping(value="/addRoleUserRlt",method=RequestMethod.POST)
    @ModDesc(desc = "为角色分配用户", pDesc = "角色用户关系", pType = SaveRoleUserRltRequestDto.class, rDesc = "操作是否成功", rType = Boolean.class)
    public ApiResult<Boolean> addRoleUserRlt(HttpServletRequest request, HttpServletResponse response, @RequestBody SaveRoleUserRltRequestDto bean) {
		SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
		bean.setDomainId(currentUserInfo.getDomainId());
    	roleApiSvc.addRoleUserRlt(bean);
		return ApiResult.ok(this).data(true);
    }

    /**
     * 角色分配组织
     */
    @ApiOperation("为角色分配组织")
    @RequestMapping(value="/addRoleOrgRlt",method = RequestMethod.POST)
    @ModDesc(desc = "为角色分配组织", pDesc = "角色组织关系", pType = SaveRoleOrgRltRequestDto.class, rDesc = "操作是否成功", rType = Boolean.class)
    public ApiResult<Boolean> addRoleOrgRlt(HttpServletRequest request, HttpServletResponse response, @RequestBody SaveRoleOrgRltRequestDto bean) {
		SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
		bean.setDomainId(currentUserInfo.getDomainId());
    	roleApiSvc.addRoleOrgRlt(bean);
		return ApiResult.ok(this).data(true);
    }

	/**
	 * 添加角色和菜单的关系
	 */
	@ApiOperation("添加角色和菜单的关系")
	@PostMapping(value="/addRoleMenuRlt")
    @ModDesc(desc = "为角色添加菜单权限", pDesc = "角色菜单关系集合", pType = List.class, pcType = SysRoleModuleRlt.class, rDesc = "操作是否成功", rType = Boolean.class)
	public ApiResult<Boolean> addRoleMenuRlt(HttpServletRequest request, HttpServletResponse response,
			@RequestBody List<SysRoleModuleRlt> list) {
		SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        roleApiSvc.addRoleMenuRlt(currentUserInfo.getDomainId(), list);
		return ApiResult.ok(this).data(true);
	}

	/**
	 * 添加角色和数据模块的关系
	 */
	@ApiOperation("为角色添加数据权限")
	@PostMapping("/addRoleDataModuleRlt")
    @ModDesc(desc = "为角色添加数据权限", pDesc = "角色数据权限关系集合", pType = List.class, pcType = SysRoleDataModuleRlt.class, rDesc = "操作是否成功", rType = Boolean.class)
	public ApiResult<Boolean> addRoleDataModuleRlt(HttpServletRequest request, HttpServletResponse response,
			@RequestBody List<SysRoleDataModuleRlt> list) {
		SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        roleApiSvc.addRoleDataModuleRlt(currentUserInfo.getDomainId(), list);
		return ApiResult.ok(this).data(true);
	}

	/**
	 * 添加用户和菜单的关系
	 */
	@ApiOperation("为用户添加菜单权限")
	@PostMapping("/addUserMenuRlt")
    @ModDesc(desc = "为用户添加菜单权限", pDesc = "用户菜单关系集合", pType = List.class, rcType = SysUserModuleRlt.class, rDesc = "操作是否成功", rType = Boolean.class)
	public ApiResult<Boolean> addUserMenuRlt(HttpServletRequest request, HttpServletResponse response,
			@RequestBody List<SysUserModuleRlt> list) {
		SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        roleApiSvc.addUserMenuRlt(currentUserInfo.getDomainId(), list);
		return ApiResult.ok(this).data(true);
	}

	/**
	 * 添加用户和数据模块的关系
	 */
	@ApiOperation("为用户添加数据权限")
	@PostMapping("/addUserDataModuleRlt")
    @ModDesc(desc = "为用户添加数据权限", pDesc = "用户数据权限关系集合", pType = List.class, rcType = SysUserDataModuleRlt.class, rDesc = "操作是否成功", rType = Boolean.class)
	public ApiResult<Boolean> addUserDataModuleRlt(HttpServletRequest request, HttpServletResponse response,
			@RequestBody List<SysUserDataModuleRlt> list) {
		SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
		Integer rs = roleApiSvc.addUserDataModuleRlt(currentUserInfo.getDomainId(), list);
		return ApiResult.ok(this).data(true);
	}

	/**
     * 根据用户id获取角色信息
     * 
     * @param request
     * @param response
     * @param userId
     */
	@ApiOperation("查询用户角色信息")
    @PostMapping("/getRolesByUserId")
    @ModDesc(desc = "查询用户角色信息", pDesc = "用户id", pType = Long.class, rDesc = "用户个人角色集合", rType = List.class, rcType = SysRole.class)
    public ApiResult<List<SysRole>> getRolesByUserId(HttpServletRequest request, HttpServletResponse response, @RequestBody String body) {
    	Long userId = Long.parseLong(body.trim());
    	List<SysRole> results = roleApiSvc.getRolesByUserId(userId);
		return ApiResult.ok(this).data(results);
    }

    /**
	 * 获取CI分类数据项
	 * 
	 * @param request
	 * @param response
	 * @param body
	 */
    @ApiOperation("获取CI分类数据项")
	@PostMapping("/getAllCIClassRole")
    @ModDesc(desc = "获取CI分类数据项", pDesc = "无", rDesc = "分类数据", rType = List.class, rcType = DataRole.class)
	public ApiResult<List<DataRole>> getAllCIClassRole(HttpServletRequest request, HttpServletResponse response) {
		SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
    	List<DataRole> rs = roleApiSvc.getDataRoleByCIClass(currentUserInfo.getDomainId());
		return ApiResult.ok(this).data(rs);
	}

	/**
	 * 获取CI Tag数据项
	 * 
	 * @param request
	 * @param response
	 * @param body
	 */
	@ApiOperation("获取CI标签数据项")
	@PostMapping("/getAllCITagRole")
    @ModDesc(desc = "获取CI标签数据项", pDesc = "无", rDesc = "标签数据", rType = List.class, rcType = DataRole.class)
	public ApiResult<List<DataRole>> getAllCITagRole(HttpServletRequest request, HttpServletResponse response) {
		SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
		List<DataRole> rs = roleApiSvc.getDataRoleByCITag(currentUserInfo.getDomainId());
		return ApiResult.ok(this).data(rs);
	}

	/**
	 * 获取数据权限菜单
	 */
	@ApiOperation("获取数据权限菜单项")
	@PostMapping("/getAllDataRoleMenu")
    @ModDesc(desc = "获取数据权限菜单项", pDesc = "无", rDesc = "数据权限菜单", rType = List.class, rcType = SysDataModule.class)
	public ApiResult<List<SysDataModule>> getAllDataRoleMenu(HttpServletRequest request, HttpServletResponse response) {
		SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
		List<SysDataModule> rs = roleApiSvc.getAllDataRoleMenu(currentUserInfo.getDomainId());
		return ApiResult.ok(this).data(rs);
	}

	/**
	 * 获取菜单--树形结构
	 */
	@ApiOperation("获取所有菜单-树结构")
	@PostMapping("/getAllMenu")
    @ModDesc(desc = "获取所有菜单-树结构", pDesc = "无", rDesc = "菜单树", rType = ModuleNodeInfo.class)
	public ApiResult<ModuleNodeInfo> getAllMenu(HttpServletRequest request, HttpServletResponse response) {
		SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
		ModuleNodeInfo rs = roleApiSvc.getAllMenu(currentUserInfo.getDomainId());
		return ApiResult.ok(this).data(rs);
	}

	/**
	 * 获取角色下的菜单权限
	 */
	@ApiOperation("获取角色菜单权限")
	@PostMapping("/getAuthMenuByRoleId")
    @ModDesc(desc = "获取角色菜单权限", pDesc = "角色id", pType = Long.class, rDesc = "角色有权访问的菜单集合", rType = List.class, rcType = SysRoleModuleRlt.class)
	public ApiResult<List<SysRoleModuleRlt>> getAuthMenuByRoleId(HttpServletRequest request, HttpServletResponse response,
			@RequestBody String body) {
		Long roleId = Long.parseLong(body.trim());
		List<SysRoleModuleRlt> rs = roleApiSvc.getAuthMenuByRoleId(roleId);
		return ApiResult.ok(this).data(rs);
	}

	/**
	 * 获取角色下的数据权限
	 * 
	 * @param roleId
	 *            角色ID
	 * @param moduleCode
	 *            模块代码
	 * @return
	 */
	@ApiOperation("获取角色数据权限")
	@PostMapping("/getAuthDataRoleByRoleId")
    @ModDesc(desc = "获取角色数据权限", pDesc = "数据模块查询对象", pType = CAuthBean.class, rDesc = "角色有权访问的数据权限集合", rType = List.class, rcType = SysRoleDataModuleRlt.class)
	public ApiResult<List<SysRoleDataModuleRlt>> getAuthDataRoleByRoleId(HttpServletRequest request, HttpServletResponse response,
			@RequestBody CAuthBean bean) {
		List<SysRoleDataModuleRlt> rs = roleApiSvc.getAuthDataRoleByRoleId(bean);
		return ApiResult.ok(this).data(rs);
	}

	/**
	 * 获取用户下的所有数据权限（roleId为空或不存在，表示是来自于用户本身）
	 * 
	 * @param userId
	 *            用户ID
	 * @param moduleCode
	 *            模块代码
	 * @return
	 */
	@CrossOrigin(origins = {})
	@ApiOperation("获取用户的所有数据权限(包含其角色权限)")
	@PostMapping("/getAuthDataRoleByUserId")
    @ModDesc(desc = "获取用户的所有数据权限(包含其角色权限)", pDesc = "数据模块查询对象", pType = CAuthBean.class, rDesc = "用户有权访问的数据权限集合", rType = List.class, rcType = SysRoleDataModuleRlt.class)
	public ApiResult<List<SysRoleDataModuleRlt>> getAuthDataRoleByUserId(HttpServletRequest request, HttpServletResponse response,
			@RequestBody CAuthBean bean) {
		List<SysRoleDataModuleRlt> rs = roleApiSvc.getAuthDataRoleByUserId(bean);
		return ApiResult.ok(this).data(rs);
	}

	/**
	 * 获取用户下的所有菜单权限（roleId为空或不存在，表示是来自于用户本身）
	 * 
	 * @param userId
	 * @return
	 */
	@ApiOperation("获取用户所有菜单权限(包含其角色权限)")
	@PostMapping("/getAuthMenuByUserId")
    @ModDesc(desc = "获取用户所有菜单权限(包含其角色权限)", pDesc = "用户id", pType = Long.class, rDesc = "用户有权访问的菜单权限集合", rType = List.class, rcType = SysRoleModuleRlt.class)
	public ApiResult<List<SysRoleModuleRlt>> getAuthMenuByUserId(HttpServletRequest request, HttpServletResponse response,
			@RequestBody String body) {
		Long userId = Long.parseLong(body.trim());
		List<SysRoleModuleRlt> rs = roleApiSvc.getAuthMenuByUserId(userId);
		return ApiResult.ok(this).data(rs);
	}

	/**
	 * 根据数据模块id-转发数据模块数据源地址获取 数据模块tree
	 * 
	 * @param request
	 * @param response
	 * @param dataModuleId
	 */
	@ApiOperation("根据数据模块id,查询数据模块权限数据(数据结构根据模块配置返回，平铺或树状)")
	@PostMapping("/getDataModuleDataById")
    @ModDesc(desc = "根据数据模块id,查询数据模块权限数据(数据结构根据模块配置返回，平铺或树状)", pDesc = "数据模块id", pType = Long.class, rDesc = "数据模块权限数据", rType = Object.class)
	public ApiResult<Object> getDataModuleDataById(HttpServletRequest request, HttpServletResponse response,
			@RequestBody String body) {
		Long dataModuleId = Long.parseLong(body.trim());
		Object result = roleApiSvc.getDataModuleDataById(dataModuleId);
		return ApiResult.ok(this).data(result);
	}

	/**
	 * 获取用户下的数据权限
	 * 
	 * @param userId
	 *            用户ID
	 * @param moduleCode
	 *            模块代码
	 * @return
	 */
	@ApiOperation("获取用户数据权限")
	@PostMapping("/getUserAuthDataRoleByUserId")
    @ModDesc(desc = "获取用户数据权限", pDesc = "数据模块查询对象 ", pType = CAuthBean.class, rDesc = "用户有权访问的数据集合", rType = List.class, rcType = SysRoleDataModuleRlt.class)
	public ApiResult<List<SysRoleDataModuleRlt>> getUserAuthDataRoleByUserId(HttpServletRequest request, HttpServletResponse response,
			@RequestBody CAuthBean bean) {
		List<SysRoleDataModuleRlt> rs = roleApiSvc.getUserAuthDataRoleByUserId(bean);
		return ApiResult.ok(this).data(rs);
	}

	/**
	 * 获取用户下的所有角色的数据权限
	 * 
	 * @param userId
	 *            用户ID
	 * @param moduleCode
	 *            模块代码
	 * @return
	 */
	@ApiOperation("获取用户下的所有角色的数据权限")
	@PostMapping("/getRoleAuthDataRoleByUserId")
    @ModDesc(desc = "获取用户下的所有角色的数据权限", pDesc = "数据模块查询对象", pType = CAuthBean.class, rDesc = "角色有权访问的数据集合", rType = List.class, rcType = SysRoleDataModuleRlt.class)
	public ApiResult<List<SysRoleDataModuleRlt>> getRoleAuthDataRoleByUserId(HttpServletRequest request, HttpServletResponse response,
			@RequestBody CAuthBean bean) {
		List<SysRoleDataModuleRlt> rs = roleApiSvc.getRoleAuthDataRoleByUserId(bean);
		return ApiResult.ok(this).data(rs);
	}

	/**
	 * 操作用户与模块之间权限
	 * 
	 * @param req
	 */
	@ApiOperation("操作用户菜单权限,根据参数确定是否进行绑定与解绑操作")
	@PostMapping("/optionUserModuleAuth")
    @ModDesc(desc = "操作用户菜单权限,根据参数确定是否进行绑定与解绑操作", pDesc = "操作用户与模块权限数据传输类", pType = OptionUserModuleAuthRequestDto.class, rDesc = "操作是否成功", rType = Boolean.class)
	public ApiResult<Boolean> optionUserModuleAuth(@RequestBody OptionUserModuleAuthRequestDto req, HttpServletRequest request,
			HttpServletResponse response) {
		roleApiSvc.optionUserModuleAuth(req);
		return ApiResult.ok(this).data(true);
	}

    /**
     * 操作用户与模块之间权限
     * 
     * @param req
     */
    @ApiOperation("根据角色id查询用户角色关系(包含来自于组织的角色,id为空表示来自于组织的角色)")
    @PostMapping("/getUserRoleRltByRoleId")
    @ModDesc(desc = "根据角色id查询用户角色关系(包含来自于组织的角色,id为空表示来自于组织的角色)", pDesc = "角色id", pType = Long.class, rDesc = "用户-角色关系列表", rType = List.class, rcType = SysUserRoleRlt.class)
    public ApiResult<List<SysUserRoleRlt>> getUserRoleRltByRoleId(@RequestBody Long roleId, HttpServletRequest request, HttpServletResponse response) {
		return ApiResult.ok(this).data(roleApiSvc.getUserRoleRltByRoleId(roleId));
    }
}
