package com.uinnova.product.vmdb.comm.dao;

import com.binary.framework.bean.Condition;
import com.binary.framework.bean.EntityBean;
import com.binary.framework.exception.DaoException;
import com.binary.jdbc.Page;

import java.util.List;

/**
 * 
 * <AUTHOR>
 *
 */
@SuppressWarnings("rawtypes")
public class ExternalJdbcDaoTemplate {
	
	@Deprecated
	public Page selectPage(long pageNum, long pageSize, Condition cdt, String orders) {
		throw new DaoException(" not support selectPage(long pageNum, long pageSize, Condition cdt, String orders)! ");
	}

	
	@Deprecated
	public List selectList(long pageNum, long pageSize, Condition cdt, String orders) {
		throw new DaoException(" not support selectList(long pageNum, long pageSize, Condition cdt, String orders)! ");
	}

	
	@Deprecated
	public List selectList(Condition cdt, String orders) {
		throw new DaoException(" not support selectList(Condition cdt, String orders)! ");
	}

	
	@Deprecated
	public long selectCount(Condition cdt) {
		throw new DaoException(" not support selectCount(Condition cdt)! ");
	}

	
	@Deprecated
	public EntityBean selectById(long id) {
		throw new DaoException(" not support selectById(long id)! ");
	}

	
	@Deprecated
	public long insert(EntityBean record) {
		throw new DaoException(" not support insert(EntityBean record)! ");
	}

	
	@Deprecated
	public long[] insertBatch(List records) {
		throw new DaoException(" not support insertBatch(List records)! ");
	}

	
	@Deprecated
	public int updateById(EntityBean record, long id) {
		throw new DaoException(" not support updateById(EntityBean record, long id)! ");
	}

	
	@Deprecated
	public int updateByCdt(EntityBean record, Condition cdt) {
		throw new DaoException(" not support updateByCdt(EntityBean record, Condition cdt)! ");
	}

	
	@Deprecated
	public int[] updateBatch(List records) {
		throw new DaoException(" not support updateBatch(List records)! ");
	}

	
	@Deprecated
	public int deleteById(long id) {
		throw new DaoException(" not support deleteById(long id)! ");
	}

	
	@Deprecated
	public int deleteByCdt(Condition cdt) {
		throw new DaoException(" not support deleteByCdt(Condition cdt)! ");
	}

	
	@Deprecated
	public int[] deleteBatch(long[] ids) {
		throw new DaoException(" not support deleteBatch(long[] ids)! ");
	}
}
