package com.uino.api.client.cmdb;

import java.util.List;

import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uino.bean.cmdb.base.ESCITagInfo;
import com.uino.bean.cmdb.business.ClassNodeInfo;
import com.uino.bean.cmdb.query.ESAttrAggBean;
import com.uino.bean.cmdb.query.ESTagSearchBean;

/**
 * <AUTHOR>
 */
public interface ITagApiSvc {

    /**
     * 保存标签规则
     * 
     * @param tagInfo
     * @return
     */
    Long saveOrUpdateCITagRule(ESCITagInfo tagInfo);

    /**
     * 根据id查询标签规则
     * 
     * @param id
     * @return
     */
    ESCITagInfo getCITagRuleById(Long id);

    /**
     * 获取标签树
     * 
     * @return
     */
    List<ClassNodeInfo> getTagTree();

    List<ClassNodeInfo> getTagTree(Long domainId);

    /**
     * 根据标签获取CI信息
     * 
     * @param bean
     * @return
     */
    Page<CcCiInfo> getCIInfoListByTag(ESTagSearchBean bean);

    /**
     * 删除标签
     * 
     * @param tagId
     * @return
     */
    Integer deleteById(Long tagId);

    /**
     * 获取属性值列表
     * 
     * @param searchBean
     * @return
     */
    Page<String> getAttrValuesBySearchBean(ESAttrAggBean searchBean);
    Page<String> getAttrValuesBySearchBean(Long domainId, ESAttrAggBean searchBean);



    /**
     * 更改标签所属目录
     * 
     * @param tagInfo
     * @return
     */
    Boolean changeTagDir(ESCITagInfo tagInfo);
}
