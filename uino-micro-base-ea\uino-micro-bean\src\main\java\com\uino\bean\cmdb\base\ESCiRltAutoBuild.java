package com.uino.bean.cmdb.base;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@ApiModel(value = "关系自动构建信息", description = "关系自动构建信息")
public class ESCiRltAutoBuild implements Serializable {

    private static final long serialVersionUID = -1L;

    public static final String SOURCE_CI_PK = "sourcePrimaryKey";
    public static final String TARGET_CI_PK = "targetPrimaryKey";

    @ApiModelProperty(value = "id",example = "123")
    private Long id;
    /**
     * 规则名称
     */
    @ApiModelProperty(value = "规则名称",example = "rule")
    private String name;
    /**
     * 模型id
     */
    @ApiModelProperty(value = "模型id",example = "123")
    private Long visualModelId;
    /**
     * 任务状态（0：未启用；1：启用）
     */
    @ApiModelProperty(value = "任务状态（false：未启用；true：启用）",example = "true")
    private boolean run;

    /**
     * 规则状态
     */
    @ApiModelProperty(value = "规则状态（1：正常；2：失效）")
    private Integer ruleStatus;
    /**
     * 关系id
     */
    @ApiModelProperty(value = "关系id",example = "123")
    private Long rltClassId;

    @ApiModelProperty(value = "模型关系状态（1：正常；2：失效）")
    private Integer rltClassStatus;
    /**
     * 保存时关系名称
     */
    @ApiModelProperty(value = "保存时关系名称",example = "关系名称")
    private String rltClassOldName;
    /**
     * 源模型id
     */
    @ApiModelProperty(value = "源模型id",example = "123")
    private Long sourceCiClassId;
    /**
     * 保存时源模型名称
     */
    @ApiModelProperty(value = "保存时源模型名称",example = "模型名称")
    private String sourceCiClassOldName;
    /**
     * 目标模型id
     */
    @ApiModelProperty(value = "目标模型id",example = "123")
    private Long targetCiClassId;
    /**
     * 保存时目标模型名称
     */
    @ApiModelProperty(value = "保存时目标模型名称",example = "模型名称")
    private String targetCiClassOldName;

    @ApiModelProperty(value = "创建时间",example = "1627284368373")
    private Long createTime;

    @ApiModelProperty(value = "定时执行Cron表达式")
    private String strCronExpression;

    private Set<Long> sourceCiClassIds = new HashSet<>();    //父子
    private Set<Long> targetCiClassIds = new HashSet<>();    //父子
    /**
     * 关系构建条件
     */
    @ApiModelProperty(value = "关系构建条件集合")
    private List<AutoBuildCondition> buildConditions = new ArrayList<>();

    /**
     * 规则属性
     */
    @ApiModelProperty(value = "规则属性集合")
    private List<MappingItem> mappingItems = new ArrayList<>();

    @ApiModelProperty(value = "所属域")
    private Long domainId;

    public Long getDomainId() {
        return domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }

    public Integer getRuleStatus() {
        return ruleStatus;
    }

    public void setRuleStatus(Integer ruleStatus) {
        this.ruleStatus = ruleStatus;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public String getStrCronExpression() {
        return strCronExpression;
    }

    public void setStrCronExpression(String strCronExpression) {
        this.strCronExpression = strCronExpression;
    }

    public Integer getRltClassStatus() {
        return rltClassStatus;
    }

    public void setRltClassStatus(Integer rltClassStatus) {
        this.rltClassStatus = rltClassStatus;
    }

    /**
     * 关系构建条件
     */
    @ApiModel(value = "关系构建条件", description = "关系构建条件")
    public static class AutoBuildCondition {
        /**
         * 源属性id
         */
        @ApiModelProperty(value = "源属性id",example = "123")
        private Long sourceAttrDefId;

        @ApiModelProperty(value="源属性Script脚本")
        private String sourceAttrDefScript;
        /**
         * 目标属性id
         */
        @ApiModelProperty(value = "目标属性id",example = "123")
        private Long targetAttrDefId;

        @ApiModelProperty(value="目标属性Script脚本")
        private String targetAttrDefScript;
        /**
         * 条件
         * Only 0(==) now
         */
        @ApiModelProperty(value = "条件：Only 0(==) now")
        private Integer optType;
        /**
         * 源端属性
         */
        @ApiModelProperty(value = "源端属性",example = "color")
        private String sourceAttrStdName;
        /**
         * 目标端属性
         */
        @ApiModelProperty(value = "目标端属性",example = "color")
        private String targetAttrStdName;

        /**
         * 保存时源端属性
         */
        @ApiModelProperty(value = "保存时源端属性",example = "color")
        private String sourceAttrStdOldName;
        /**
         * 保存时目标端属性
         */
        @ApiModelProperty(value = "保存时目标端属性",example = "color")
        private String targetAttrStdOldName;

        public Long getSourceAttrDefId() {
            return sourceAttrDefId;
        }

        public void setSourceAttrDefId(Long sourceAttrDefId) {
            this.sourceAttrDefId = sourceAttrDefId;
        }

        public String getSourceAttrDefScript() {
            return sourceAttrDefScript;
        }

        public void setSourceAttrDefScript(String sourceAttrDefScript) {
            this.sourceAttrDefScript = sourceAttrDefScript;
        }

        public Long getTargetAttrDefId() {
            return targetAttrDefId;
        }

        public void setTargetAttrDefId(Long targetAttrDefId) {
            this.targetAttrDefId = targetAttrDefId;
        }

        public String getTargetAttrDefScript() {
            return targetAttrDefScript;
        }

        public void setTargetAttrDefScript(String targetAttrDefScript) {
            this.targetAttrDefScript = targetAttrDefScript;
        }

        public Integer getOptType() {
            return optType;
        }

        public void setOptType(Integer optType) {
            this.optType = optType;
        }

        public String getSourceAttrStdName() {
            return sourceAttrStdName;
        }

        public void setSourceAttrStdName(String sourceAttrStdName) {
            this.sourceAttrStdName = sourceAttrStdName;
        }

        public String getTargetAttrStdName() {
            return targetAttrStdName;
        }

        public void setTargetAttrStdName(String targetAttrStdName) {
            this.targetAttrStdName = targetAttrStdName;
        }

        public String getSourceAttrStdOldName() {
            return sourceAttrStdOldName;
        }

        public void setSourceAttrStdOldName(String sourceAttrStdOldName) {
            this.sourceAttrStdOldName = sourceAttrStdOldName;
        }

        public String getTargetAttrStdOldName() {
            return targetAttrStdOldName;
        }

        public void setTargetAttrStdOldName(String targetAttrStdOldName) {
            this.targetAttrStdOldName = targetAttrStdOldName;
        }
    }

    /**
     * 规则属性
     */
    @ApiModel(value = "规则属性", description = "规则属性")
    public static class MappingItem {

        @ApiModelProperty(value="关系属性默认id",example = "123")
        private Long rltAttrDefId;

        @ApiModelProperty(value="ci属性默认id",example = "123")
        private Long ciAttrDefId;

        @ApiModelProperty(value="源端或目标端")
        private Integer sourceOrTarget;

        @ApiModelProperty(value="关系属性名")
        private String rltAttrDefStdName;

        @ApiModelProperty(value="对象属性名")
        private String ciAttrDefStdName;

        public Long getRltAttrDefId() {
            return rltAttrDefId;
        }

        public void setRltAttrDefId(Long rltAttrDefId) {
            this.rltAttrDefId = rltAttrDefId;
        }

        public Long getCiAttrDefId() {
            return ciAttrDefId;
        }

        public void setCiAttrDefId(Long ciAttrDefId) {
            this.ciAttrDefId = ciAttrDefId;
        }

        public Integer getSourceOrTarget() {
            return sourceOrTarget;
        }

        public void setSourceOrTarget(Integer sourceOrTarget) {
            this.sourceOrTarget = sourceOrTarget;
        }

        public String getRltAttrDefStdName() {
            return rltAttrDefStdName;
        }

        public void setRltAttrDefStdName(String rltAttrDefStdName) {
            this.rltAttrDefStdName = rltAttrDefStdName;
        }

        public String getCiAttrDefStdName() {
            return ciAttrDefStdName;
        }

        public void setCiAttrDefStdName(String ciAttrDefStdName) {
            this.ciAttrDefStdName = ciAttrDefStdName;
        }

    }

    public enum RelationCiType {
        //
        SOURCE(0),
        TARGET(1);

        private Integer code;

        private RelationCiType(Integer code) {
            this.code = code;
        }

        public Integer getType() {
            return code;
        }

        public static RelationCiType valueOf(Integer code) {
            if (SOURCE.code.equals(code)) {
                return SOURCE;
            } else if (TARGET.code.equals(code)) {
                return TARGET;
            } else {
                return null;
            }
        }
    }


    public ESCiRltAutoBuild() {
    }

    public ESCiRltAutoBuild(JSONObject json) {
        if (json.containsKey("id") && json.getLong("id") != null) {
            this.id = json.getLong("id");
        } else {
            throw new RuntimeException("参数有误");
        }
        //增加规则名称字段
        if (json.containsKey("name") && json.getString("name") != null) {
            this.name = json.getString("name");
        } else {
            this.name = "";
        }
        if (json.containsKey("rltClassOldName") && json.getString("rltClassOldName") != null) {
            this.rltClassOldName = json.getString("rltClassOldName");
        } else {
            this.rltClassOldName = "";
        }
        if (json.containsKey("sourceCiClassOldName") && json.getString("sourceCiClassOldName") != null) {
            this.sourceCiClassOldName = json.getString("sourceCiClassOldName");
        } else {
            this.sourceCiClassOldName = "";
        }
        if (json.containsKey("targetCiClassOldName") && json.getString("targetCiClassOldName") != null) {
            this.targetCiClassOldName = json.getString("targetCiClassOldName");
        } else {
            this.targetCiClassOldName = "";
        }
        if (json.containsKey("visualModelId") && json.getLong("visualModelId") != null) {
            this.visualModelId = json.getLong("visualModelId");
        } else {
            throw new RuntimeException("参数有误");
        }
        if (json.containsKey("run")) {
            this.run = json.getBoolean("run");
        } else {
            throw new RuntimeException("参数有误");
        }
        if (json.containsKey("rltClassId") && json.getLong("rltClassId") != null) {
            this.rltClassId = json.getLong("rltClassId");
        } else {
            throw new RuntimeException("参数有误");
        }

        if (json.containsKey("sourceCiClassId") && json.getLong("sourceCiClassId") != null) {
            this.sourceCiClassId = json.getLong("sourceCiClassId");
        } else {
            throw new RuntimeException("参数有误");
        }

        if (json.containsKey("targetCiClassId") && json.getLong("targetCiClassId") != null) {
            this.targetCiClassId = json.getLong("targetCiClassId");
        } else {
            throw new RuntimeException("参数有误");
        }

        if (json.containsKey("buildConditions") && json.getJSONArray("buildConditions") != null) {
            JSONArray cdts = json.getJSONArray("buildConditions");
            this.buildConditions = new ArrayList<AutoBuildCondition>();
            for (int i = 0; i < cdts.size(); i++) {
                AutoBuildCondition cdt = new AutoBuildCondition();
                JSONObject cdtObj = cdts.getJSONObject(i);
                cdt.setSourceAttrDefId(cdtObj.getLong("sourceAttrDefId"));
                cdt.setSourceAttrDefScript(cdtObj.getString("sourceAttrDefScript"));
                cdt.setTargetAttrDefId(cdtObj.getLong("targetAttrDefId"));
                cdt.setTargetAttrDefScript(cdtObj.getString("targetAttrDefScript"));
                cdt.setOptType(cdtObj.getInteger("optType"));
                this.buildConditions.add(cdt);
            }
        }
        if (json.containsKey("mappingItems") && json.getJSONArray("mappingItems") != null) {
            JSONArray items = json.getJSONArray("mappingItems");
            this.mappingItems = new ArrayList<MappingItem>();
            for (int i = 0; i < items.size(); i++) {
                MappingItem item = new MappingItem();
                JSONObject itemObj = items.getJSONObject(i);
                item.setRltAttrDefId(itemObj.getLong("rltAttrDefId"));
                item.setCiAttrDefId(itemObj.getLong("ciAttrDefId"));
                item.setSourceOrTarget(itemObj.getInteger("sourceOrTarget"));
                this.mappingItems.add(item);
            }
        } else {
            this.mappingItems = new ArrayList<MappingItem>();
        }

        if (json.containsKey("sourceCiClassIds") && json.getJSONArray("sourceCiClassIds") != null) {
            this.sourceCiClassIds.addAll(json.getJSONArray("sourceCiClassIds").toJavaList(Long.class));
        } else {
            sourceCiClassIds.add(sourceCiClassId);
        }

        if (json.containsKey("targetCiClassIds") && json.getJSONArray("targetCiClassIds") != null) {
            this.targetCiClassIds.addAll(json.getJSONArray("targetCiClassIds").toJavaList(Long.class));
        } else {
            targetCiClassIds.add(targetCiClassId);
        }
    }

    public JSONObject toJson() {
        JSONObject json = new JSONObject();
        json.put("id",id);
        json.put("name", name);
        json.put("visualModelId", visualModelId);
        json.put("run", run);
        json.put("rltClassId", rltClassId);
        json.put("sourceCiClassId", sourceCiClassId);
        json.put("targetCiClassId", targetCiClassId);
        json.put("sourceCiClassIds", sourceCiClassIds);
        json.put("targetCiClassIds", targetCiClassIds);
        if (buildConditions != null) {
            JSONArray cdtArr = new JSONArray();
            for (AutoBuildCondition cdt : buildConditions) {
                JSONObject cdtObj = new JSONObject();
                cdtObj.put("sourceAttrDefId", cdt.getSourceAttrDefId());
                cdtObj.put("sourceAttrDefScript", cdt.getSourceAttrDefScript());
                cdtObj.put("targetAttrDefId", cdt.getTargetAttrDefId());
                cdtObj.put("targetAttrDefScript", cdt.getTargetAttrDefScript());
                cdtObj.put("optType", cdt.getOptType());
                cdtArr.add(cdtObj);
            }
            json.put("buildConditions", cdtArr);
        }
        if (mappingItems != null) {
            JSONArray itemArr = new JSONArray();
            for (MappingItem item : mappingItems) {
                JSONObject itemObj = new JSONObject();
                itemObj.put("rltAttrDefId", item.getRltAttrDefId());
                itemObj.put("ciAttrDefId", item.getCiAttrDefId());
                itemObj.put("sourceOrTarget", item.getSourceOrTarget());
                itemArr.add(itemObj);
            }
            json.put("mappingItems", itemArr);
        }
        return json;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getVisualModelId() {
        return visualModelId;
    }

    public void setVisualModelId(Long visualModelId) {
        this.visualModelId = visualModelId;
    }

    public boolean isRun() {
        return run;
    }

    public void setRun(boolean run) {
        this.run = run;
    }

    public Long getRltClassId() {
        return rltClassId;
    }

    public void setRltClassId(Long rltClassId) {
        this.rltClassId = rltClassId;
    }

    public Long getSourceCiClassId() {
        return sourceCiClassId;
    }

    public void setSourceCiClassId(Long sourceCiClassId) {
        this.sourceCiClassId = sourceCiClassId;
    }

    public Long getTargetCiClassId() {
        return targetCiClassId;
    }

    public void setTargetCiClassId(Long targetCiClassId) {
        this.targetCiClassId = targetCiClassId;
    }

    public Set<Long> getSourceCiClassIds() {
        return sourceCiClassIds;
    }

    public void setSourceCiClassIds(Set<Long> sourceCiClassIds) {
        this.sourceCiClassIds = sourceCiClassIds;
    }

    public Set<Long> getTargetCiClassIds() {
        return targetCiClassIds;
    }

    public void setTargetCiClassIds(Set<Long> targetCiClassIds) {
        this.targetCiClassIds = targetCiClassIds;
    }

    public List<AutoBuildCondition> getBuildConditions() {
        return buildConditions;
    }

    public void setBuildConditions(List<AutoBuildCondition> buildConditions) {
        this.buildConditions = buildConditions;
    }

    public List<MappingItem> getMappingItems() {
        return mappingItems;
    }

    public void setMappingItems(List<MappingItem> mappingItems) {
        this.mappingItems = mappingItems;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getRltClassOldName() {
        return rltClassOldName;
    }

    public void setRltClassOldName(String rltClassOldName) {
        this.rltClassOldName = rltClassOldName;
    }

    public String getSourceCiClassOldName() {
        return sourceCiClassOldName;
    }

    public void setSourceCiClassOldName(String sourceCiClassOldName) {
        this.sourceCiClassOldName = sourceCiClassOldName;
    }

    public String getTargetCiClassOldName() {
        return targetCiClassOldName;
    }

    public void setTargetCiClassOldName(String targetCiClassOldName) {
        this.targetCiClassOldName = targetCiClassOldName;
    }
}
