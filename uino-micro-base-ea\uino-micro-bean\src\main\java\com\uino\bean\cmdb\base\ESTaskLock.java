package com.uino.bean.cmdb.base;

import com.alibaba.fastjson.JSONObject;

import java.io.Serializable;

/**
 * 任务锁
 *
 * <AUTHOR>
 * @version 2020-4-24
 */
public class ESTaskLock implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 任务锁ID
     */
    private Long id;

    /**
     * 锁定的任务名称
     */
    private String taskName;

    /**
     * 任务锁定的时间（时间戳）
     */
    private Long lockTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public Long getLockTime() {
        return lockTime;
    }

    public void setLockTime(Long lockTime) {
        this.lockTime = lockTime;
    }

    public JSONObject toJson() {
        JSONObject json = new JSONObject();
        json.put("id", id);
        json.put("taskName", taskName);
        json.put("lockTime", lockTime);
        return json;
    }
}