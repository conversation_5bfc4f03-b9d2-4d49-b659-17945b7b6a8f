package com.uinnova.product.eam.base.diagram.exception;

public class UnAuthorizedException extends RuntimeException {

    public static final long serialVersionUID = 1;

    public UnAuthorizedException() {
        super();
    }

    public UnAuthorizedException(String message) {
        super(message);
    }

    public UnAuthorizedException(String message, Throwable cause) {
        super(message, cause);
    }

    public UnAuthorizedException(Throwable cause) {
        super(cause);
    }
}
