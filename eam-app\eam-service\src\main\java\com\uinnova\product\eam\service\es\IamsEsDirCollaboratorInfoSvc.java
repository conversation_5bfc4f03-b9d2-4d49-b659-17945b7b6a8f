package com.uinnova.product.eam.service.es;

import com.uinnova.product.eam.comm.bean.DirCollaboratorInfo;
import com.uino.dao.AbstractESBaseDao;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.admin.indices.get.GetIndexRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.script.Script;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.PostConstruct;
import java.util.List;

/**
 * 文件夹协作者操作es的数据接口层
 *
 * <AUTHOR>
 */
@Repository
@Slf4j
public class IamsEsDirCollaboratorInfoSvc extends AbstractESBaseDao<DirCollaboratorInfo,DirCollaboratorInfo> {

    private static final Long DEFAULT_DOMAIN_ID = 1L;

    @Override
    public String getIndex() {
        return "uino_eam_dircollaboratorinfo";
    }

    @Override
    public String getType() {
        return "_doc";
    }

    @PostConstruct
    public void init() {
        super.initIndex();
        this.updateEmptyDomainIdRecord();
    }

    private void updateEmptyDomainIdRecord() {
        try {
            RestHighLevelClient clientc = getClient();
            GetIndexRequest getRequest = new GetIndexRequest();
            getRequest.indices(getFullIndexName());
            boolean existIndex = clientc.indices().exists(getRequest, RequestOptions.DEFAULT);
            if (existIndex) {
                BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
                boolQueryBuilder.filter(QueryBuilders.scriptQuery(new Script("doc['domainId'].length==0")));
                List<DirCollaboratorInfo> list = this.getListByQuery(boolQueryBuilder);
                if (!CollectionUtils.isEmpty(list)) {
                    for (DirCollaboratorInfo dirCollaboratorInfo : list) {
                        dirCollaboratorInfo.setDomainId(DEFAULT_DOMAIN_ID);
                    }
                    this.saveOrUpdateBatch(list);
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }
}
