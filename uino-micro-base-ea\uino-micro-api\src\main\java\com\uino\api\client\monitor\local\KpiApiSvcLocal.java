package com.uino.api.client.monitor.local;

import java.util.Collection;
import java.util.List;

import com.uino.dao.BaseConst;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.binary.core.io.Resource;
import com.binary.jdbc.Page;
import com.uino.api.client.monitor.IKpiApiSvc;
import com.uino.bean.cmdb.business.ImportResultMessage;
import com.uino.bean.cmdb.business.ImportSheetMessage;
import com.uino.bean.monitor.base.ESKpiInfo;
import com.uino.bean.monitor.buiness.KpiRltBindDto;
import com.uino.bean.monitor.buiness.SearchKpiBean;
import com.uino.service.simulation.IKpiSvc;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
public class KpiApiSvcLocal implements IKpiApiSvc {

    @Autowired
    private IKpiSvc kpiSvc;

    @Override
	public ESKpiInfo getKpiInfoById(Long id) {
		return kpiSvc.getKpiInfoById(id);
	}
    
    @Override
    public List<ESKpiInfo> getKpiInfoByIds(Collection<Long> ids) {
        return kpiSvc.getKpiInfoByIds(ids);
    }

	@Override
    public Page<ESKpiInfo> queryKpiInfoPage(SearchKpiBean searchDto) {
        return kpiSvc.queryKpiInfoPage(searchDto);
    }

    @Override
    public Long saveOrUpdate(ESKpiInfo saveDto) {
        return kpiSvc.saveOrUpdate(saveDto);
    }

    @Override
    public void deleteByKpiIds(Collection<Long> kpiIds) {
        kpiSvc.deleteByKpiIds(kpiIds);
    }

    @Override
    public Resource exportKpiInfos(Boolean isTpl) {
        return kpiSvc.exportKpiInfos(BaseConst.DEFAULT_DOMAIN_ID, isTpl);
    }

    @Override
    public Resource exportKpiInfos(Long domainId, Boolean isTpl) {
        return kpiSvc.exportKpiInfos(domainId, isTpl);
    }

    @Override
    public ImportResultMessage importKpiInfos(MultipartFile file) {
        return kpiSvc.importKpiInfos(BaseConst.DEFAULT_DOMAIN_ID, file);
    }

    @Override
    public ImportResultMessage importKpiInfos(Long domainId, MultipartFile file) {
        return kpiSvc.importKpiInfos(domainId, file);
    }

    @Override
    public void bindCiClassRltToKpiInfo(KpiRltBindDto dto) {
        kpiSvc.bindCiClassRltToKpiInfo(dto);
    }

    @Override
    public void delCiClassRltToKpiInfo(KpiRltBindDto dto) {
        kpiSvc.delCiClassRltToKpiInfo(dto);
    }

	@Override
	public ImportSheetMessage saveOrUpdateBatch(List<ESKpiInfo> kpiInfos) {
		return kpiSvc.saveOrUpdateBatch(BaseConst.DEFAULT_DOMAIN_ID, kpiInfos);
	}

    @Override
    public ImportSheetMessage saveOrUpdateBatch(Long domainId, List<ESKpiInfo> kpiInfos) {
        return kpiSvc.saveOrUpdateBatch(domainId, kpiInfos);
    }

}
