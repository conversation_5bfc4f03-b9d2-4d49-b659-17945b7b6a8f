package com.uinnova.product.eam.db.impl;


import com.uinnova.product.eam.comm.model.CVcDiagramOp;
import com.uinnova.product.eam.comm.model.VcDiagramOp;
import com.uinnova.product.eam.db.VcDiagramOpDao;
import com.uinnova.product.eam.db.support.dao.mybatis.ComMyBatisBinaryDaoImpl;


/**
 * 视图操作表[VC_DIAGRAM_OP]数据访问对象实现
 */
public class VcDiagramOpDaoImpl extends ComMyBatisBinaryDaoImpl<VcDiagramOp, CVcDiagramOp> implements VcDiagramOpDao {

//    @Override
//    public String getTableName() {
//        return "IAMS." + getDaoDefinition().getTableName();
//    }
}


