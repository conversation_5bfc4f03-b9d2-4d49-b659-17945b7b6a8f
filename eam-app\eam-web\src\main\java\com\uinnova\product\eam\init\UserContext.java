package com.uinnova.product.eam.init;

import java.util.HashMap;
import java.util.Map;

/**
 * 用户获取方法，存放在threadLocal中，防止一次接口请求重复获取多次用户信息
 *
 * <AUTHOR>
 * @since 2025/3/5 14:50
 */
public class UserContext {

    // 使用泛型增强通用性
    private static final ThreadLocal<Map<String, Object>> context = new ThreadLocal<>();

    public static void set(String key, Object value) {
        if (context.get() == null) {
            context.set(new HashMap<>());
        }
        context.get().put(key, value);
    }

    public static <T> T get(String key) {
        return (context.get() != null) ? (T) context.get().get(key) : null;
    }

    public static void clear() {
        if (context.get() != null) {
            context.get().clear();
        }
        context.remove(); // 关键：防止内存泄漏
    }
}
