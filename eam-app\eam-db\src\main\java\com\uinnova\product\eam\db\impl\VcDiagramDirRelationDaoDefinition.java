package com.uinnova.product.eam.db.impl;


import com.binary.framework.dao.DaoDefinition;

import com.uinnova.product.eam.comm.model.CVcDiagramDirRelation;
import com.uinnova.product.eam.comm.model.VcDiagramDirRelation;


/**
 * 视图目录关系表[VC_DIAGRAM_DIR_RELATION]数据访问对象定义实现
 */
public class VcDiagramDirRelationDaoDefinition implements DaoDefinition<VcDiagramDirRelation, CVcDiagramDirRelation> {


	@Override
	public Class<VcDiagramDirRelation> getEntityClass() {
		return VcDiagramDirRelation.class;
	}


	@Override
	public Class<CVcDiagramDirRelation> getConditionClass() {
		return CVcDiagramDirRelation.class;
	}


	@Override
	public String getTableName() {
		return "VC_DIAGRAM_DIR_RELATION";
	}


	@Override
	public boolean hasDataStatusField() {
		return false;
	}


	@Override
	public void setDataStatusValue(VcDiagramDirRelation record, int status) {
	}


	@Override
	public void setDataStatusValue(CVcDiagramDirRelation cdt, int status) {
	}


	@Override
	public void setCreatorValue(VcDiagramDirRelation record, String creator) {
	}


	@Override
	public void setModifierValue(VcDiagramDirRelation record, String modifier) {
	}


}


