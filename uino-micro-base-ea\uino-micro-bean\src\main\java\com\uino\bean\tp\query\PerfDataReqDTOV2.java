package com.uino.bean.tp.query;

import lombok.Data;

import java.util.List;

/**
 * @author: weix<PERSON>ong
 * @date: 2020/11/18 15:24
 **/
@Data
public class PerfDataReqDTOV2 {

    private List<Long> classIds;
    private Long ciId;
    private List<String> metrics;
    /**
     * 聚合时间维度，1m，1h，1w，1M
     */
    private String interval;
    private Long startTime;
    private Long endTime;
    /**
     * 查询属性集合
     */
    private List<AttrInfo> attrInfos;
    private Integer pageSize = 10000;
    private Integer pageNum = 1;
    /**
     * 是否聚合，默认不聚合
     */
    private Boolean aggFlag = Boolean.FALSE;

    @Data
    public static class AttrInfo {
        private String attrKey;
        private String attrValue;
    }

}
