package com.uino.dao.cmdb;

import com.uinnova.product.vmdb.comm.model.ci.CCcCi;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.dao.AbstractESBaseDao;
import com.uino.dao.ESConst;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;

/**
 * 用于初始化运行库索引
 */
@Service
public class ESCIBaseLineSvc extends AbstractESBaseDao<ESCIInfo, CCcCi> {

    @Override
    public String getIndex() {
        return ESConst.INDEX_CMDB_CI;
    }

    @Override
    public String getType() {
        return ESConst.INDEX_CMDB_CI;
    }

    @PostConstruct
    public void init() {
        super.initIndex(5);
    }

}
