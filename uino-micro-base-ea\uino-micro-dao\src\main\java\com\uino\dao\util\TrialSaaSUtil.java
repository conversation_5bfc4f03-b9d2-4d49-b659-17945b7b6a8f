package com.uino.dao.util;

import com.uino.bean.permission.SysTypeConfig;
import com.uino.bean.permission.base.SysModule;
import com.uino.dao.permission.ESSysTypeConfigSvc;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Component
@RefreshScope
public class TrialSaaSUtil {

    private static final String APPLICATION_SQUARE_NAME = "应用广场";
    private static final String ARCH_MAP_NAME = "架构地图";
    private static final Set<String> SAAS_FILTER_MENU;
    private static final Set<String> SAAS_FILTER_BUTTON;
    static {
        SAAS_FILTER_MENU = Stream.of("功能配置","标签管理","树状图管理","LDAP集成","授权管理").collect(Collectors.toSet());
        SAAS_FILTER_BUTTON = Stream.of("数据权限","菜单配置","替换LOGO","LDAP集成","通知服务").collect(Collectors.toSet());
    }

    /**
     * 登录方式：sso-扫码、短信、thingjs, thingjs-只支持thingjs登录， oauth-用户密码方式登录
     */
    @Value("${monet.login.loginMethod:}")
    private String loginMethod;

    @Value("${wiki.oauth.client.id:}")
    private String CLIENT_ID;

    @Value("${wiki.oauth.client.secret:}")
    private String CLIENT_SECRET;

    @Value("${wiki.oauth.client.user_agent:}")
    private String USER_AGENT;

    @Value("${local.oauth.client.id:}")
    private String LOCAL_CLIENT_ID;

    @Value("${local.oauth.client.secret:}")
    private String LOCAL_CLIENT_SECRET;

    @Value("${local.oauth.client.user_agent:}")
    private String LOCAL_USER_AGENT;

    @Value("${wiki.oauth.server.url:}")
    private String oauthServerUrl;

    @Value("${wiki.oauth.server.token_callback.url:}")
    private String callbackUrl;

    @Autowired
    private ESSysTypeConfigSvc sysTypeConfigSvc;

    public void setLoginMethod(String loginMethod) {
        this.loginMethod = loginMethod;
    }

    public void setClientId(String clientId) {
        this.CLIENT_ID = clientId;
    }

    public void setClientSecret(String clientSecret) {
        this.CLIENT_SECRET = clientSecret;
    }

    public void setUserAgent(String userAgent) {
        this.USER_AGENT = userAgent;
    }

    public void setLocalClientId(String localClientId) {
        this.LOCAL_CLIENT_ID = localClientId;
    }

    public void setLocalClientSecret(String localClientSecret) {
        this.LOCAL_CLIENT_SECRET = localClientSecret;
    }

    public void setLocalUserAgent(String localUserAgent) {
        this.LOCAL_USER_AGENT = localUserAgent;
    }

    public void setOauthServerUrl(String oauthServerUrl) {
        this.oauthServerUrl = oauthServerUrl;
    }

    public void setCallbackUrl(String callbackUrl) {
        this.callbackUrl = callbackUrl;
    }

    /**
     * SaaS版菜单调整
     * @param list
     * @return
     */
    public List<SysModule> appSquareModuleRenameAndOnTop(List<SysModule> list) {
        if (getSysType().equals(2) || CollectionUtils.isEmpty(list)) {
            return list;
        }
        //SaaS版"应用广场"改名"架构地图"且置为第一个
        SysModule appSquare = null;
        List<SysModule> rusult = new ArrayList<>();
        for (SysModule sysModule : list) {
            if (APPLICATION_SQUARE_NAME.equals(sysModule.getModuleName())) {
                appSquare = sysModule;
                appSquare.setModuleName(ARCH_MAP_NAME);
                appSquare.setModuleSign(ARCH_MAP_NAME);
                appSquare.setLabel(ARCH_MAP_NAME);
                continue;
            }
            //SaaS版不展示对应菜单
            if (SAAS_FILTER_MENU.contains(sysModule.getLabel())) {
                continue;
            }
            //SaaS版不展示对应按钮
            if (SAAS_FILTER_BUTTON.contains(sysModule.getLabel())) {
                continue;
            }
            rusult.add(sysModule);
        }
        if (appSquare != null) {
            rusult.add(0, appSquare);
        }
        return rusult;
    }

    public Integer getSysType() {
        return sysTypeConfigSvc.getListByCdt(new SysTypeConfig()).get(0).getType();
    }

    public String getLoginMethod() {
        return loginMethod;
    }

    public String getClientId() {
        return CLIENT_ID;
    }

    public String getClientSecret() {
        return CLIENT_SECRET;
    }

    public String getUserAgent() {
        return USER_AGENT;
    }

    public String getLocalClientId() {
        return LOCAL_CLIENT_ID;
    }

    public String getLocalClientSecret() {
        return LOCAL_CLIENT_SECRET;
    }

    public String getLocalUserAgent() {
        return LOCAL_USER_AGENT;
    }

    public String getOauthServerUrl() {
        return oauthServerUrl;
    }

    public String getCallbackUrl() {
        return callbackUrl;
    }
}
