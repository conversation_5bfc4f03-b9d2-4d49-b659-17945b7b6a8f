package com.uinnova.product.eam.base.diagram.mix.model;

import java.time.LocalDateTime;
import java.util.Objects;


public class WordDoc {

    private Long id;

    private String ciCode;

    private String docName;

    private Long userId;

    private String docType;

    private String savePth;

    private LocalDateTime updateTime;

    private String operator;

    private String fullPath;

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getFullPath() {
        return fullPath;
    }

    public void setFullPath(String fullPath) {
        this.fullPath = fullPath;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCiCode() {
        return ciCode;
    }

    public void setCiCode(String ciCode) {
        this.ciCode = ciCode;
    }

    public String getDocName() {
        return docName;
    }

    public void setDocName(String docName) {
        this.docName = docName;
    }

    public String getDocType() {
        return docType;
    }

    public void setDocType(String docType) {
        this.docType = docType;
    }

    public String getSavePth() {
        return savePth;
    }

    public void setSavePth(String savePth) {
        this.savePth = savePth;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof WordDoc)) return false;
        WordDoc wordDoc = (WordDoc) o;
        return Objects.equals(id, wordDoc.id) &&
                Objects.equals(ciCode, wordDoc.ciCode) &&
                Objects.equals(docName, wordDoc.docName) &&
                Objects.equals(docType, wordDoc.docType) &&
                Objects.equals(savePth, wordDoc.savePth) &&
                Objects.equals(updateTime, wordDoc.updateTime) &&
                Objects.equals(operator, wordDoc.operator) &&
                Objects.equals(fullPath,wordDoc.getFullPath());
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, ciCode, docName, docType, savePth, updateTime, operator, fullPath);
    }
}
