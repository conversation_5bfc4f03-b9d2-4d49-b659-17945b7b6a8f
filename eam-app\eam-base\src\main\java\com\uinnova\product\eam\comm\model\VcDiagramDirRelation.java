package com.uinnova.product.eam.comm.model;




import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;


@Comment("视图目录关系表[VC_DIAGRAM_DIR_RELATION]")
public class VcDiagramDirRelation implements EntityBean {
	private static final long serialVersionUID = 1L;


	@Comment("ID[ID]")
	private Long id;


	@Comment("目录ID[DIR_ID]")
	private Long dirId;


	@Comment("目录类型[DIR_TYPE]")
	private Integer dirType;


	@Comment("视图ID[DIAGRAM_ID]")
	private Long diagramId;


	@Comment("所属域[DOMAIN_ID]")
	private Long domainId;


	@Comment("创建时间[CREATE_TIME]    创建时间:yyyyMMddHHmmss")
	private Long createTime;


	@Comment("修改时间[MODIFY_TIME]    修改时间:yyyyMMddHHmmss")
	private Long modifyTime;




	public Long getId() {
		return this.id;
	}
	public void setId(Long id) {
		this.id = id;
	}


	public Long getDirId() {
		return this.dirId;
	}
	public void setDirId(Long dirId) {
		this.dirId = dirId;
	}


	public Integer getDirType() {
		return this.dirType;
	}
	public void setDirType(Integer dirType) {
		this.dirType = dirType;
	}


	public Long getDiagramId() {
		return this.diagramId;
	}
	public void setDiagramId(Long diagramId) {
		this.diagramId = diagramId;
	}


	public Long getDomainId() {
		return this.domainId;
	}
	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}


	public Long getCreateTime() {
		return this.createTime;
	}
	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}


	public Long getModifyTime() {
		return this.modifyTime;
	}
	public void setModifyTime(Long modifyTime) {
		this.modifyTime = modifyTime;
	}


}


