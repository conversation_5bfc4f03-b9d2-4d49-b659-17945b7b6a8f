package com.uino.provider.feign.sys;

import java.util.Collection;
import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import com.binary.core.io.Resource;
import com.binary.jdbc.Page;
import com.uino.bean.cmdb.business.ImportResultMessage;
import com.uino.bean.cmdb.business.ImportSheetMessage;
import com.uino.bean.sys.base.ESDictionaryClassInfo;
import com.uino.bean.sys.base.ESDictionaryItemInfo;
import com.uino.bean.sys.business.DictionaryInfoDto;
import com.uino.bean.sys.query.ESDictionaryItemSearchBean;
import com.uino.bean.sys.query.ExportDictionaryDto;
import com.uino.provider.feign.config.BaseFeignConfig;

/**
 * 
 * <AUTHOR>
 *
 */
@Component
@FeignClient(name = "${feign-server-name:tarsier-platform}", path = "${feign-server-path:tarsier-platform}/feign/sys/dict", configuration = {BaseFeignConfig.class})
public interface DictionaryFeign {

    /**
     * 保存字典定义
     * 
     * @param dictClassInfo
     * @return
     */
    @PostMapping("saveDictionaryClassInfo")
    Long saveDictionaryClassInfo(@RequestBody ESDictionaryClassInfo dictClassInfo);

    /**
     * 获取字典定义
     * 
     * @return
     */
    @PostMapping("getDictionaryClassList")
    List<DictionaryInfoDto> getDictionaryClassList(@RequestBody Long domainId);

    /**
     * 根据id获取字典定义
     * 
     * @param id
     * @return
     */
    @PostMapping("getDictClassInfoById")
    ESDictionaryClassInfo getDictClassInfoById(@RequestBody Long dictClassId);

    /**
     * 根据id删除字典定义
     * 
     * @param dictClassId
     * @return
     */
    @PostMapping("deleteDictClassInfoById")
    Integer deleteDictClassInfoById(@RequestBody Long dictClassId);

	/**
	 * 根据id获取字典项
	 * 
	 * @param id
	 * @return
	 */
	@PostMapping("getDictItemInfoById")
	ESDictionaryItemInfo getDictItemInfoById(@RequestBody Long id);

    /**
     * 条件查询字典项-分页
     * 
     * @param bean
     * @return
     */
    @PostMapping("searchDictItemPageByBean")
    Page<ESDictionaryItemInfo> searchDictItemPageByBean(@RequestBody ESDictionaryItemSearchBean bean);

    /**
     * 条件查询字典项
     * 
     * @param bean
     * @return
     */
    @PostMapping("searchDictItemListByBean")
    List<ESDictionaryItemInfo> searchDictItemListByBean(@RequestBody ESDictionaryItemSearchBean bean);

    /**
     * 保存字典项
     * 
     * @param item
     * @return
     */
    @PostMapping(value = "saveOrUpdateDictionaryItem")
    Long saveOrUpdateDictionaryItem(@RequestParam("item") ESDictionaryItemInfo item);

    /**
     * 批量保存字典项
     * 
     * @param dictClassId
     * @param items
     * @return
     */
    @PostMapping("saveDictionaryItemsBatch")
    ImportSheetMessage saveDictionaryItemsBatch(@RequestParam(value = "dictClassId") Long dictClassId, @RequestBody List<ESDictionaryItemInfo> items);

    /**
     * 根据id删除字典项
     * 
     * @param id
     * @return
     */
    @PostMapping("deleteItemByIds")
    Integer deleteItemByIds(@RequestBody Collection<Long> ids);

    /**
     * 导出字典数据
     * 
     * @param dictClassId
     * @return
     */
    @PostMapping("exportDictionaryItems")
    Resource exportDictionaryItems(@RequestBody ExportDictionaryDto dto);

    /**
     * 导入字典数据
     * 
     * @param dictClassId
     * @param file
     * @return
     */
    @PostMapping(value = "importDictionaryItems", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    ImportResultMessage importDictionaryItems(@RequestParam(value = "dictClassId") Long dictClassId, @RequestPart(value = "file") MultipartFile file);

    /**
     * 查询引用字典值(支持dictClassId、dictDefId和dictName、dictProName两种获取方式)
     * 
     * @param bean
     * @return
     */
    @PostMapping("getExteralDictValues")
    List<String> getExteralDictValues(@RequestBody ESDictionaryItemSearchBean bean);
}
