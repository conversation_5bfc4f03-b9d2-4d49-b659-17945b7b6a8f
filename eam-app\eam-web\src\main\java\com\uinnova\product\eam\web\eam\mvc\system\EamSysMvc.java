package com.uinnova.product.eam.web.eam.mvc.system;

import com.binary.framework.util.ControllerUtils;
import com.uinnova.product.eam.service.sys.IEamSysSvc;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uino.bean.permission.business.CurrentUserInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 系统公共服务
 *
 * <AUTHOR>
 * @version 2020-8-4
 */
@Controller
@RequestMapping("/eam/sys")
public class EamSysMvc {

    @Autowired
    IEamSysSvc eamSysSvc;

    @Value("${uino.eam.project.enable}")
    private Boolean isAllow;

    @RequestMapping("/uploadFile")
    @ModDesc(desc = "上传文件", pDesc = "待上传文件", rDesc = "文件地址", rType = String.class)
    public void uploadFile(@RequestParam(name = "file") MultipartFile file, HttpServletRequest request,
                           HttpServletResponse response) {
        String path = eamSysSvc.uploadFile(file);
        ControllerUtils.returnJson(request, response, path);
    }

    @RequestMapping(value = "/getCurrentUser")
    @ModDesc(desc = "获取当前登录用户信息", pDesc = "无", rDesc = "当前用户信息", rType = CurrentUserInfo.class)
    public void getCurrentUser(HttpServletRequest request, HttpServletResponse response) {
        ControllerUtils.returnJson(request, response, eamSysSvc.getCurrentUser());
    }

    @RequestMapping(value = "/isAllow")
    public void isAllow(HttpServletRequest request, HttpServletResponse response) {
        ControllerUtils.returnJson(request, response, isAllow);
    }
}
