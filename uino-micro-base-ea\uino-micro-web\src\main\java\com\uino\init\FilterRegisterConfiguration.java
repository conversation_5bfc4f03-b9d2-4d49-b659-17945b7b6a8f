package com.uino.init;


import com.uino.init.http.ResponseFilter;
import com.uino.init.http.StaticFilter;
import com.uino.init.http.safe.PermissionFilter;
import com.uino.init.http.safe.RefererFilter;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Configuration;

@Configuration
@Log4j2
public class FilterRegisterConfiguration {

    static {
        log.info("开始注册Filter相关信息");
    }

    @Value("${nocover.custom_response.urls:}")
    private String noCoverCustomResponse;

    @Value("${safe.trust.referers:}")
    private String trustReferersStr;

    @Value("${permission.check:false}")
    private boolean needAuthorityCheck;

    @Value("${permission.ignore.filter.pattern:/redirectAuth;/getTokenByCode;/permission/user/getCurrentUser;/permission/module/getModuleTree;/sys/getLogos;/sys/tenantDomain/multiTenantStatus;/permission/user/enshrineMenu;/permission/user/unenshrineMenu;/permission/user/getUserInfoById}")
    private String strIgnoreFilterPattern;

    @Bean
    @SuppressWarnings({"rawtypes", "unchecked"})
    public FilterRegistrationBean responseFilter() {
        log.info("ResponseFilter注册成功");
        FilterRegistrationBean registration = new FilterRegistrationBean(new ResponseFilter());
        registration.addUrlPatterns("/*");
        registration.setName("responseFilter");
        registration.addInitParameter("noCoverCustomResponse", noCoverCustomResponse);
        registration.setOrder(1);
        return registration;
    }

    @Bean
    @SuppressWarnings({"rawtypes", "unchecked"})
    public FilterRegistrationBean refererFilter() {
        log.info("RefererFilter注册成功");
        FilterRegistrationBean registration = new FilterRegistrationBean(new RefererFilter());
        registration.addUrlPatterns("/*");
        registration.setName("refererFilter");
        registration.addInitParameter("trustReferersStr", trustReferersStr);
        registration.setOrder(2);
        return registration;
    }

    @Bean
    @SuppressWarnings({"rawtypes", "unchecked"})
    public FilterRegistrationBean staticFilter() {
        log.info("StaticFilter注册成功");
        FilterRegistrationBean registration = new FilterRegistrationBean(new StaticFilter());
        registration.addUrlPatterns("*.jpg", "*.JPG", "*.jpeg", "*.JPEG", "*.png");
        registration.setName("staticFilter");
        registration.setOrder(3);
        return registration;
    }

    @Bean
    @SuppressWarnings({"rawtypes", "unchecked"})
    public FilterRegistrationBean permissionFilter(@Autowired ApplicationContext applicationContext) {
        log.info("PermissionFilter注册成功");
        FilterRegistrationBean registration = new FilterRegistrationBean(new PermissionFilter(applicationContext));
        registration.addUrlPatterns("/*");
        registration.setName("permissionFilter");
        registration.addInitParameter("needAuthorityCheck", String.valueOf(needAuthorityCheck));
        registration.addInitParameter("strIgnoreFilterPattern", strIgnoreFilterPattern);
        registration.setOrder(4);
        return registration;
    }

    @Bean
    @SuppressWarnings({"rawtypes", "unchecked"})
    public FilterRegistrationBean pluginOperateLogFilter(@Autowired ApplicationContext applicationContext) {
        log.info("PluginOperateLogFilter注册成功");
        FilterRegistrationBean registration = new FilterRegistrationBean(new PluginOperateLogFilter(applicationContext));
        registration.addUrlPatterns("/plugin/*");
        registration.setName("pluginOperateLogFilter");
        registration.setOrder(5);
        return registration;
    }
}
