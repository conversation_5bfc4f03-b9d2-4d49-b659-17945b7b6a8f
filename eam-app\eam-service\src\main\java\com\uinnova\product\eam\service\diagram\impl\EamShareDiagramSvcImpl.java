package com.uinnova.product.eam.service.diagram.impl;

import com.binary.core.exception.BinaryException;
import com.binary.core.util.BinaryUtils;
import com.binary.jdbc.Page;
import com.uinnova.product.eam.base.diagram.model.*;
import com.uinnova.product.eam.db.diagram.es.ESShareDiagramDao;
import com.uinnova.product.eam.service.diagram.IEamShareDiagramSvc;
import com.uino.api.client.permission.IUserApiSvc;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.permission.query.CSysUser;
import com.uino.dao.util.ESUtil;
import com.uino.util.sys.SysUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;

import static java.util.Comparator.comparing;

@Deprecated
@Service
public class EamShareDiagramSvcImpl implements IEamShareDiagramSvc {

    @Autowired
    private ESShareDiagramDao esShareDiagramDao;

    /*@Autowired
    private VcDiagramSvc diagramSvc;*/

    @Value("${http.resource.space}")
    private String httpResouceUrl;

    @Autowired
    private IUserApiSvc userApiSvc;


    @Override
    public Page<DiagramShareRecordResult> queryShareRecordPage(ShareRecordQueryBean shareRecordQueryBean) {
        Page<DiagramShareRecordResult> diagramShareRecordPage = new Page<>();
        String order = shareRecordQueryBean.getOrder();
        Integer pageNum = shareRecordQueryBean.getPageNum();
        Integer pageSize = shareRecordQueryBean.getPageSize();
        String word = shareRecordQueryBean.getLike();
        Long totalRows = 0L;
        Long totalpages = 0L;

        ShareRecordQuery shareRecordQuery = new ShareRecordQuery();
        shareRecordQuery.setSharedUserId(shareRecordQueryBean.getSharedUserId());
        List<DiagramShareRecord> records = new ArrayList<>();

        /*// 需要模糊搜索的就查询全部跟我相关的视图id，然后进行搜索
        if (!StringUtils.isEmpty(shareRecordQueryBean.getLike())) {
            List<DiagramShareRecord> listByCdt = esShareDiagramDao.getListByCdt(shareRecordQuery);
            if (!CollectionUtils.isEmpty(listByCdt)) {
                Map<Long, DiagramShareRecord> diagramIdShareRecordMap = new HashMap<>();
                for (DiagramShareRecord diagramShareRecord : listByCdt) {
                    diagramIdShareRecordMap.put(diagramShareRecord.getDiagramId(), diagramShareRecord);
                }
                CVcDiagram cVcDiagram = new CVcDiagram();
                cVcDiagram.setDataStatus(1);
                cVcDiagram.setStatus(1);
                cVcDiagram.setIds(diagramIdShareRecordMap.keySet().toArray(new Long[0]));
                cVcDiagram.setName("%" + shareRecordQueryBean.getLike() + "%");

                Page<VcDiagram> vcDiagramPage = diagramSvc.queryDiagramPage(1L, pageNum, pageSize, cVcDiagram, "CREATE_TIME DESC");

                List<VcDiagram> data = vcDiagramPage.getData();

                if (!CollectionUtils.isEmpty(data)) {
                    for (VcDiagram datum : data) {
                        Long diagramId = datum.getId();
                        records.add(diagramIdShareRecordMap.get(diagramId));
                    }
                }

                totalRows = vcDiagramPage.getTotalRows();
                totalpages = vcDiagramPage.getTotalPages();
            }
        } else {
            //查出当前用户拥有的所有分享记录
            records = esShareDiagramDao.getListByCdt(shareRecordQuery);
            //Page<DiagramShareRecord> listByQuery = esShareDiagramDao.getListByQuery(shareRecordQueryBean.getPageNum(), shareRecordQueryBean.getPageSize(), ESUtil.cdtToBuilder(shareRecordQuery));

            //records = listByQuery.getData();

            int recordSize = records.size();
            totalRows = (long) recordSize;
            totalpages = (long) (recordSize / pageNum);
        }

        */
        if("modifyTime".equals(order)) {
            //按照视图的修改时间进行排序
            //查出当前用户拥有的所有分享记录
            records = esShareDiagramDao.getListByCdt(shareRecordQuery);
            int recordSize = records.size();
            totalRows = (long) recordSize;
            totalpages = (long) (recordSize / pageSize);
            if(!StringUtils.isEmpty(word)) {
                word = "%" + word + "%";
            }
        } else if("shareTime".equals(order)) {
            //TODO 按照视图的分享时间排序
        } else {
            throw new BinaryException("排序字段不符合规定");
        }

        if (!CollectionUtils.isEmpty(records)) {
            diagramShareRecordPage.setPageNum(pageNum);
            diagramShareRecordPage.setPageSize(pageSize);
            diagramShareRecordPage.setTotalRows(totalRows);
            diagramShareRecordPage.setTotalPages(totalpages);
            List<DiagramShareRecordResult> resultList = fillSysOPAndDiagramInfoForQuery(records, pageNum, pageSize, word, diagramShareRecordPage);
            //按照修改时间排序
            resultList.sort(comparing(DiagramShareRecordResult::getDiagramModifyTime).reversed());
            diagramShareRecordPage.setData(resultList);
        }

        return diagramShareRecordPage;
    }
/*

    public Map<Long, List<DiagramShareRecordResult>> queryDiagramShareRecords(Set<Long> diagramIds) {

        Map<Long, List<DiagramShareRecordResult>> map = new HashMap<>();
        if (CollectionUtils.isEmpty(diagramIds)) {
            return map;
        }

        ShareRecordQuery shareRecordQuery = new ShareRecordQuery();
        shareRecordQuery.setDiagramIds(diagramIds.toArray(new Long[0]));

        List<DiagramShareRecord> listByCdt = esShareDiagramDao.getListByCdt(shareRecordQuery);

        if (!CollectionUtils.isEmpty(listByCdt)) {

            List<DiagramShareRecordResult> diagramShareRecordResults = this.fillSysOPAndDiagramInfo(listByCdt);

            for (DiagramShareRecordResult result : diagramShareRecordResults) {
                Long diagramId = result.getDiagramId();

                if (map.containsKey(diagramId)) {
                    map.get(diagramId).add(result);
                } else {
                    List<DiagramShareRecordResult> records = new ArrayList<>();
                    records.add(result);
                    map.put(diagramId, records);
                }
            }
        }

        return map;
    }
*/

    private List<DiagramShareRecordResult> fillSysOPAndDiagramInfoForQuery(List<DiagramShareRecord> diagramShareRecords, Integer pageNum, Integer pageSize, String word, Page<DiagramShareRecordResult> recordResultPage) {
        List<DiagramShareRecordResult> diagramShareRecordResults = new ArrayList<>();
        Set<Long> diagramIds = new HashSet<>();
        Set<Long> userIds = new HashSet<>();
        //获取分享记录的视图id集合
        diagramShareRecords.forEach(
                shareRecord -> diagramIds.add(shareRecord.getDiagramId())
        );
        //根据所有分享记录的视图id集合分页排序查询视图信息
        CVcDiagram cVcDiagram = new CVcDiagram();
        cVcDiagram.setIds(diagramIds.toArray(new Long[0]));
        cVcDiagram.setStatus(1);
        cVcDiagram.setDataStatus(1);
        if(!StringUtils.isEmpty(word)) {
            cVcDiagram.setName(word);
        }
        // Page<VcDiagram> diagramPage = diagramSvc.queryDiagramPage(1L, pageNum, pageSize, cVcDiagram, "MODIFY_TIME DESC");
        Page<VcDiagram> diagramPage = new Page<>();
        recordResultPage.setTotalRows(diagramPage.getTotalRows());
        recordResultPage.setTotalPages(diagramPage.getTotalPages());
        List<VcDiagram> vcDiagramList = diagramPage.getData();
        //填充信息并建立映射关系
        Map<Long, VcDiagram> vcDiagramMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(vcDiagramList)) {
            for (VcDiagram diagram : vcDiagramList) {
                Long diagramId = diagram.getId();
                String icon1 = diagram.getIcon1();

                if (icon1 != null && !icon1.startsWith(httpResouceUrl)) {
                    StringBuilder stringBuilder = new StringBuilder();
                    stringBuilder.append(httpResouceUrl);
                    stringBuilder.append(icon1);
//                    icon1 = httpResouceUrl + icon1;
                    diagram.setIcon1(stringBuilder.toString());
                }
                String diagramJson = diagram.getDiagramJson();
                if (diagramJson != null && !diagramJson.startsWith(httpResouceUrl)) {
                    StringBuilder diagramBuilder = new StringBuilder();
                    diagramBuilder.append(httpResouceUrl);
                    diagramBuilder.append(diagramJson);
//                    diagramJson = httpResouceUrl + diagramJson;
                    diagram.setDiagramJson(diagramBuilder.toString());
                }
                vcDiagramMap.put(diagramId, diagram);
            }
        }

        //获取视图拥有者id集合，并查出其详细用户信息
        vcDiagramList.forEach(
                diagram -> {
                    userIds.add(diagram.getUserId());
                }
        );
        userIds.add(SysUtil.getCurrentUserInfo().getId());
        CSysUser sysUser = new CSysUser();
        sysUser.setIds(userIds.toArray(new Long[0]));
        sysUser.setStatus(1);
        List<SysUser> sysUserByCdt = userApiSvc.getSysUserByCdt(sysUser);
        Map<Long, SysUser> sysOpMap = new HashMap<>();
        for (SysUser sysOp : sysUserByCdt) {
            Long id = sysOp.getId();
            sysOpMap.put(id, sysOp);
        }

        //根据视图查询结果反推分享记录
        List<DiagramShareRecord> shareRecordResultList = new ArrayList<>();
        for (DiagramShareRecord shareRecord : diagramShareRecords) {
            if (diagramIds.contains(shareRecord.getDiagramId())) {
                shareRecordResultList.add(shareRecord);
            }
        }

        //封装返回结果
        for (DiagramShareRecord shareRecord : shareRecordResultList) {
            Long diagramId = shareRecord.getDiagramId();
            if (vcDiagramMap.containsKey(diagramId)) {
                DiagramShareRecordResult shareRecordResult = new DiagramShareRecordResult();
                BeanUtils.copyProperties(shareRecord, shareRecordResult);
                VcDiagram vcDiagram = vcDiagramMap.get(diagramId);
                shareRecordResult.setVcDiagram(vcDiagram);
                shareRecordResult.setDiagramModifyTime(vcDiagram.getModifyTime());
                Long ownerId = shareRecordResult.getOwnerId();
                Long sharedUserId = shareRecordResult.getSharedUserId();
                shareRecordResult.setOwnerSysUser(sysOpMap.get(ownerId));
                shareRecordResult.setSharedSysUser(sysOpMap.get(sharedUserId));
                diagramShareRecordResults.add(shareRecordResult);
            }
        }

        return diagramShareRecordResults;
    }

  /*  *//**
     * 补充分享记录的视图信息和用户信息
     * @param diagramShareRecords
     * @return
     *//*
    private List<DiagramShareRecordResult> fillSysOPAndDiagramInfo(List<DiagramShareRecord> diagramShareRecords) {

        List<DiagramShareRecordResult> diagramShareRecordResults = new ArrayList<>();

        Set<Long> diagramIds = new HashSet<>();

        Set<Long> userIds = new HashSet<>();

        for (DiagramShareRecord datum : diagramShareRecords) {
            diagramIds.add(datum.getDiagramId());
            userIds.add(datum.getOwnerId());
            userIds.add(datum.getSharedUserId());
        }

        CSysUser sysUser = new CSysUser();
        sysUser.setIds(userIds.toArray(new Long[0]));
        sysUser.setStatus(1);

        List<SysUser> sysUserByCdt = userApiSvc.getSysUserByCdt(sysUser);

        Map<Long, SysUser> sysOpMap = new HashMap<>();

        for (SysUser sysOp : sysUserByCdt) {
            Long id = sysOp.getId();
            sysOpMap.put(id, sysOp);
        }

        CVcDiagram cVcDiagram = new CVcDiagram();
        cVcDiagram.setIds(diagramIds.toArray(new Long[0]));
        cVcDiagram.setStatus(1);
        cVcDiagram.setDataStatus(1);

        List<VcDiagram> diagrams = diagramSvc.queryDiagramList(1L, cVcDiagram, null);

        Map<Long, VcDiagram> vcDiagramMap = new HashMap<>();

        if (!CollectionUtils.isEmpty(diagrams)) {
            for (VcDiagram diagram : diagrams) {
                Long id = diagram.getId();

                String icon1 = diagram.getIcon1();
                if (icon1 != null && !icon1.startsWith(httpResouceUrl)) {
                    StringBuilder stringBuilder = new StringBuilder();
                    stringBuilder.append(httpResouceUrl);
                    stringBuilder.append(icon1);
                    diagram.setIcon1(stringBuilder.toString());
                }
                String diagramJson = diagram.getDiagramJson();
                if (diagramJson != null && !diagramJson.startsWith(httpResouceUrl)) {
                    diagram.setDiagramJson(new StringBuilder(httpResouceUrl).append(diagramJson).toString());
                }
                vcDiagramMap.put(id, diagram);
            }
        }

        for (DiagramShareRecord datum : diagramShareRecords) {
            Long diagramId = datum.getDiagramId();
            if (vcDiagramMap.containsKey(diagramId)) {
                DiagramShareRecordResult recordResult = new DiagramShareRecordResult();
                BeanUtils.copyProperties(datum, recordResult);

                VcDiagram vcDiagram = vcDiagramMap.get(diagramId);
                recordResult.setVcDiagram(vcDiagram);
                recordResult.setDiagramModifyTime(vcDiagram.getModifyTime());

                Long ownerId = recordResult.getOwnerId();
                Long sharedUserId = recordResult.getSharedUserId();
                recordResult.setOwnerSysUser(sysOpMap.get(ownerId));
                recordResult.setSharedSysUser(sysOpMap.get(sharedUserId));

                diagramShareRecordResults.add(recordResult);
            }
        }

        return diagramShareRecordResults;
    }
*/
    public Integer removeShareRecord(Long id) {
        return esShareDiagramDao.deleteById(id);
    }

    public Map<Long, Set<Long>> queryDiagramSharedUserIds(Long[] diagramIds) {

        Map<Long, Set<Long>> diagramUserIdMap = new HashMap<>();

        ShareRecordQuery shareRecordQuery = new ShareRecordQuery();
        shareRecordQuery.setDiagramIds(diagramIds);

        List<DiagramShareRecord> listByCdt = esShareDiagramDao.getListByCdt(shareRecordQuery);

        if (!CollectionUtils.isEmpty(listByCdt)) {
            for (DiagramShareRecord diagramShareRecord : listByCdt) {
                Long diagramId = diagramShareRecord.getDiagramId();
                Long sharedUserId = diagramShareRecord.getSharedUserId();

                if (diagramUserIdMap.containsKey(diagramId)) {
                    diagramUserIdMap.get(diagramId).add(sharedUserId);
                } else {
                    Set<Long> userIds = new HashSet<>();
                    userIds.add(sharedUserId);
                    diagramUserIdMap.put(diagramId, userIds);
                }
            }
        }

        return diagramUserIdMap;
    }

    @Override
    public List<DiagramShareRecord> queryByUserId(Long userId) {
        ShareRecordQuery shareRecordQuery = new ShareRecordQuery();
        shareRecordQuery.setSharedUserId(userId);
        return esShareDiagramDao.getListByCdt(shareRecordQuery);
    }
}
