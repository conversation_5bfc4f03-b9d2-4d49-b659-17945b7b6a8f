package com.uino.dao.cmdb.dataset;

import com.alibaba.fastjson.JSONObject;
import com.uino.dao.AbstractESBaseDao;
import com.uino.bean.cmdb.base.dataset.batch.DataSetExeResult;
import com.uino.dao.ESConst;
import org.springframework.stereotype.Repository;

import jakarta.annotation.PostConstruct;

/**
 * 数据集执行结果
 *
 * <AUTHOR>
 * @version 2020-4-26
 */
@Repository
public class ESDataSetExeResultSvc extends AbstractESBaseDao<DataSetExeResult, JSONObject> {

    @Override
    public String getIndex() {
        return ESConst.INDEX_CMDB_DATASET_EXE_RESULT;
    }

    @Override
    public String getType() {
        return ESConst.INDEX_CMDB_DATASET_EXE_RESULT;
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }
}

