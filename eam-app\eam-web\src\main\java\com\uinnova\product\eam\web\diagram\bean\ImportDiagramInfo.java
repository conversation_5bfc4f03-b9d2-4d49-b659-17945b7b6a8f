package com.uinnova.product.eam.web.diagram.bean;

import java.io.Serializable;
import java.util.List;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.web.integration.bean.VcCiClassInfo;
import com.uinnova.product.eam.web.integration.bean.VcCiInfo;
import com.uinnova.product.vmdb.comm.model.rlt.CcCiRlt;

public class ImportDiagramInfo implements Serializable {
	private static final long serialVersionUID = 1L;
	
	
	@Comment("CI信息")
	private List<VcCiInfo> ciInfos;
	
	@Comment("CI关系数据")
	private List<CcCiRlt> ciRlts;
	
	@Comment("CI关系分类信息")
	private List<VcCiClassInfo> ciRltClassInfos;


	public List<VcCiInfo> getCiInfos() {
		return ciInfos;
	}


	public void setCiInfos(List<VcCiInfo> ciInfos) {
		this.ciInfos = ciInfos;
	}


	public List<CcCiRlt> getCiRlts() {
		return ciRlts;
	}


	public void setCiRlts(List<CcCiRlt> ciRlts) {
		this.ciRlts = ciRlts;
	}


	public List<VcCiClassInfo> getCiRltClassInfos() {
		return ciRltClassInfos;
	}


	public void setCiRltClassInfos(List<VcCiClassInfo> ciRltClassInfos) {
		this.ciRltClassInfos = ciRltClassInfos;
	}


	

}
