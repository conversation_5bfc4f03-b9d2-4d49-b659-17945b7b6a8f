package com.uinnova.product.eam.model;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.comm.model.VcDiagram;
import com.uinnova.product.eam.comm.model.VcDiagramGroup;
import com.uinnova.product.eam.comm.model.VcGroup;
import com.uinnova.product.eam.comm.model.VcGroupUser;
import com.uino.bean.permission.base.SysUser;

import java.io.Serializable;
import java.util.List;

public class VcGroupInfo implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 小组id
	 * */
	private Long id;
	
	/**
	 * 组对象
	 */
	private VcGroup group;
	
	
	/**
	 * 用户组集合
	 */
	private List<VcGroupUser> groupUsers;

	
	/**
	 * 用户组集合
	 */
	private List<VcGroupUserInfo> groupUserInfos;
	
	
	/**
	 * 具有查看视图权限的小组成员
	 */
	private List<SysUser> readUsers;
	
	
	/**
	 * 具有编辑视图权限的小组成员
	 */
	private List<SysUser> editUsers;
	
	
	
	/**
	 * 视图组集合
	 */
	private List<VcDiagramGroup> diagramGroups;
	
	/**
	 * 小组的视图信息
	 * */
	private List<VcDiagram> diagrams;
	
	
	/**
	 * 小组创建者信息
	 */
	private SysUser groupCreator;
	
	
	/**
	 * 当前小组下所有小组成员用户名数组
	 */
	private String[] groupUserNames;
	
	
	/**
	 * 当前登录用户ID
	 */
	private Long loginUserId;
	
	
	/**
	 * 当前登录用户Name
	 */
	private String loginUserName;
	
	
	public Long getId() {
		return id;
	}


	public void setId(Long id) {
		this.id = id;
	}


	public List<VcDiagram> getDiagrams() {
		return diagrams;
	}


	public void setDiagrams(List<VcDiagram> diagrams) {
		this.diagrams = diagrams;
	}


	@Comment("小组视图总数")
	private Long diagramCount;
	
	public Long getDiagramCount() {
		return diagramCount;
	}


	public void setDiagramCount(Long diagramCount) {
		this.diagramCount = diagramCount;
	}


	public VcGroup getGroup() {
		return group;
	}


	public void setGroup(VcGroup group) {
		this.group = group;
	}


	public List<VcGroupUser> getGroupUsers() {
		return groupUsers;
	}


	public void setGroupUsers(List<VcGroupUser> groupUsers) {
		this.groupUsers = groupUsers;
	}


	public List<VcGroupUserInfo> getGroupUserInfos() {
		return groupUserInfos;
	}


	public void setGroupUserInfos(List<VcGroupUserInfo> groupUserInfos) {
		this.groupUserInfos = groupUserInfos;
	}


	public List<SysUser> getReadUsers() {
		return readUsers;
	}


	public void setReadUsers(List<SysUser> readUsers) {
		this.readUsers = readUsers;
	}


	public List<SysUser> getEditUsers() {
		return editUsers;
	}


	public void setEditUsers(List<SysUser> editUsers) {
		this.editUsers = editUsers;
	}


	public List<VcDiagramGroup> getDiagramGroups() {
		return diagramGroups;
	}


	public void setDiagramGroups(List<VcDiagramGroup> diagramGroups) {
		this.diagramGroups = diagramGroups;
	}


	public SysUser getGroupCreator() {
		return groupCreator;
	}


	public void setGroupCreator(SysUser groupCreator) {
		this.groupCreator = groupCreator;
	}


	public String[] getGroupUserNames() {
		return groupUserNames;
	}


	public void setGroupUserNames(String[] groupUserNames) {
		this.groupUserNames = groupUserNames;
	}


	public Long getLoginUserId() {
		return loginUserId;
	}


	public void setLoginUserId(Long loginUserId) {
		this.loginUserId = loginUserId;
	}


	public String getLoginUserName() {
		return loginUserName;
	}


	public void setLoginUserName(String loginUserName) {
		this.loginUserName = loginUserName;
	}


	
	
}
