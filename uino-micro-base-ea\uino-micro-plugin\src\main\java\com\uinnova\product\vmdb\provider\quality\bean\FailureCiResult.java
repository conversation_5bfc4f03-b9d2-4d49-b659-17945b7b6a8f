package com.uinnova.product.vmdb.provider.quality.bean;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.vmdb.comm.model.quality.CcCiQualityFailed;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;

import java.io.Serializable;
import java.util.List;

@Comment("验证不通过的CI信息")
public class FailureCiResult implements Serializable{
	
	private static final long serialVersionUID = 1L;
	@Comment("页码")
	private long pageNum;
	@Comment("分页大小")
	private long pageSize;
	@Comment("数据总数")
	private long totalRows;
	@Comment("数据总页码")
	private long totalPages;
	@Comment("数据返回状态,1构建中,2构建完成")
	private Integer status;
	
	@Comment("CI信息")
	List<CcCiInfo> records;

	List<CcCiQualityFailed> faileds;

	public List<CcCiInfo> getRecords() {
		return records;
	}

	public void setRecords(List<CcCiInfo> records) {
		this.records = records;
	}

	public List<CcCiQualityFailed> getFaileds() {
		return faileds;
	}

	public void setFaileds(List<CcCiQualityFailed> faileds) {
		this.faileds = faileds;
	}

	public long getPageNum() {
		return pageNum;
	}

	public void setPageNum(long pageNum) {
		this.pageNum = pageNum;
	}

	public long getPageSize() {
		return pageSize;
	}

	public void setPageSize(long pageSize) {
		this.pageSize = pageSize;
	}

	public long getTotalRows() {
		return totalRows;
	}

	public void setTotalRows(long totalRows) {
		this.totalRows = totalRows;
	}

	public long getTotalPages() {
		return totalPages;
	}

	public void setTotalPages(long totalPages) {
		this.totalPages = totalPages;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	
	
}
