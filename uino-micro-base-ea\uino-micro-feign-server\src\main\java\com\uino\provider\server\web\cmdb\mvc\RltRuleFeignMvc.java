package com.uino.provider.server.web.cmdb.mvc;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.binary.jdbc.Page;
import com.uino.service.cmdb.microservice.IRltRuleSvc;
import com.uino.bean.cmdb.base.ESRltRuleInfo;
import com.uino.provider.feign.cmdb.RltRuleFeign;

/**
 * <AUTHOR>
 * @data 2019/8/1 15:58.
 */
@RestController
@RequestMapping("feign/rltRule")
public class RltRuleFeignMvc implements RltRuleFeign {

    @Autowired
    private IRltRuleSvc rltRuleSvc;

    @Override
    public Long saveOrUpdate(ESRltRuleInfo rltRuleInfo) {
        Long id = rltRuleSvc.saveOrUpdate(rltRuleInfo);
        return id;
    }

    @Override
    public int deleteById(Long id) {
        Integer integer = rltRuleSvc.deleteById(id);
        return integer.intValue();
    }

    @Override
    public ESRltRuleInfo queryInfoById(Long id) {
        ESRltRuleInfo esRltRuleInfo = rltRuleSvc.queryInfoById(id);
        return esRltRuleInfo;
    }

    @Override
    public List<ESRltRuleInfo> queryInfo(Long domainId) {
        return rltRuleSvc.queryInfo(domainId);
    }

    @Override
    public Page<ESRltRuleInfo> queryInfoPage(int pageNum, int pageSize, String name, Long domainId, String orders, boolean isAsc) {
        Page<ESRltRuleInfo> rltRuleInfoPage = rltRuleSvc.queryInfoPage(pageNum, pageSize, name, domainId, orders, isAsc);
        return rltRuleInfoPage;
    }


}
