package com.uinnova.product.eam.comm.model.es;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 版本目录表实体
 * <AUTHOR>
 */
@Data
public class EamVersionDir {

    @Comment("主键id")
    private Long id;

    @Comment("版本标签id")
    private Long tagId;

    @Comment("目录id")
    private Long dirId;

    @Comment("目录名称")
    private String dirName;

    @Comment("父级id")
    private Long parentDirId;

    @Comment("目录绑定的ciCode")
    private String ciCode;

    @Comment("目录绑定的视图加密id")
    private String diagramId;

    @Comment("当前目录其他视图集合：视图id-视图版本")
    private List<EamVersionDiagram> diagramList;

    @Comment("目录层级")
    private Integer dirLvl;

}
