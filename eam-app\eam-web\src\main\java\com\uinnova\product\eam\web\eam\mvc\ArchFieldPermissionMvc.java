package com.uinnova.product.eam.web.eam.mvc;

import com.binary.framework.web.RemoteResult;
import com.uinnova.product.eam.model.ArchFieldPermissionBO;
import com.uinnova.product.eam.service.ArchFieldPermissionSvc;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/eam/archPermission")
@Deprecated
public class ArchFieldPermissionMvc {

    @Autowired
    private ArchFieldPermissionSvc archFieldPermissionSvc;

    @GetMapping("/getDesignPermission")
    public RemoteResult getDesignPermission() {
        List<ArchFieldPermissionBO> result = archFieldPermissionSvc.getDesignPermission();
        return new RemoteResult(result);
    }

    @GetMapping("/getAssertPermission")
    public RemoteResult getAssertPermission() {
        List<ArchFieldPermissionBO> result = archFieldPermissionSvc.getAssertPermission();
        return new RemoteResult(result);
    }

}
