package com.uinnova.product.eam.model.cj.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * @description: 问题校验结果
 * @author: Lc
 * @create: 2022-03-07 16:15
 */
@Getter
public enum QuestionCheckEnum {

    LATER_CHECK(0, "待验证"),
    PASS(1, "验证通过"),
    NO_PASS(2, "验证不通过");

    private Integer code;

    private String desc;

    QuestionCheckEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getCheckDesc(Integer code) {
        for (QuestionCheckEnum checkEnum : QuestionCheckEnum.values()) {
            if (Objects.equals(code, checkEnum.getCode())) {
                return checkEnum.desc;
            }
        }
        return null;
    }
}
