package com.thingjs.framework.common.util;

import com.binary.core.os.OperateSystem;
import com.binary.core.os.OperateSystemFactory;
import com.binary.core.os.OperateSystemType;
import com.uinnova.product.vmdb.comm.util.SystemUtil;

public class CUtil {

    private static final String NATIVE_LIBARY_AUTH_WINDOWS = "thingjsc-native-windows-1.0.dll";

    private static final String NATIVE_LIBARY_LIBAUTH_LINUX = "thingjsc-native-linux-1.0.so";

    private static final String NATIVE_LIBARY_AUTH_MAC = "thingjsc-native-mac-1.0.dylib";

    static {
        OperateSystem os = OperateSystemFactory.getOperateSystem();
        OperateSystemType ostype = os.getType();
        if (ostype == OperateSystemType.LINUX) {
            SystemUtil.loadLibrary(NATIVE_LIBARY_LIBAUTH_LINUX);
        } else if (ostype == OperateSystemType.WINDOWS) {
            SystemUtil.loadLibrary(NATIVE_LIBARY_AUTH_WINDOWS);
        } else {
            SystemUtil.loadLibrary(NATIVE_LIBARY_AUTH_MAC);
        }

    }

    public static native void initGlobal(String jsPath, int jsFileSize);

    public static native String getUniqueMachineID();

    public static native String genAuthID(String config, String machineID);

    public static native int setAuthID(String authID);

    public static native String processRequest(String request, int isOriginalText);

}
