package com.uino.api.client.sys.rpc;

import com.uino.dao.BaseConst;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.binary.jdbc.Page;
import com.uino.bean.sys.base.SysLoginLog;
import com.uino.bean.sys.business.QueryLoginLogRequestDto;
import com.uino.provider.feign.sys.LogFeign;
import com.uino.api.client.sys.ILogApiSvc;

@Service
public class LogApiSvcRpc implements ILogApiSvc {

    @Autowired
    private LogFeign feign;

    @Override
    public SysLoginLog addLoginLog(Long userId) {
        return feign.addLoginLog(userId);
    }

    @Override
    public SysLoginLog addLoginLog(String userCode) {
        return feign.addLoginLog(BaseConst.DEFAULT_DOMAIN_ID, userCode);
    }

    @Override
    public SysLoginLog addLoginLog(Long domainId, String userCode) {
        return feign.addLoginLog(domainId, userCode);
    }

    @Override
    public Page<SysLoginLog> queryLoginLog(QueryLoginLogRequestDto query) {
        return feign.queryLoginLog(query);
    }
}
