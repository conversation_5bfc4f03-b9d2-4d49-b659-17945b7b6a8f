package com.uinnova.product.eam.service.utils;

import com.alibaba.fastjson.JSONObject;
import com.binary.core.util.BinaryUtils;
import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uino.bean.cmdb.base.ESCIAttrDefInfo;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.enums.AttrNameKeyEnum;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
public class AssetManageUtil {

    public static final String CI_CODE = "ciCode";

    public static void ciExportAttrTransition(Map<String, ESCIAttrDefInfo> attrDefInfoMap, Map<String, Object> attrs) {
        for (Map.Entry<String, Object> attrEntry : attrs.entrySet()) {
            Object value = attrEntry.getValue();
            if (!BinaryUtils.isEmpty(value)) {
                ESCIAttrDefInfo attrDefInfo = attrDefInfoMap.get(attrEntry.getKey());
                if (null != attrDefInfo) {
                    Integer proType = attrDefInfo.getProType();
                    if (proType == 15) {// 人员
                        String userName = "";
                        try {
                            userName = JSONObject.parseArray(value.toString(), JSONObject.class).stream().map(e -> BinaryUtils.isEmpty(e.get("userName")) ? "" : e.get("userName").toString()).collect(Collectors.joining(","));
                        } catch (Exception e) {
                            // 存量数据可能会有类型修改未清空，无法转换直接塞入
                            log.error("转换人员属性异常", e);
                            log.info("异常信息属性值:{}", JSONObject.toJSONString(attrs));
                            userName = value.toString();
                        }
                        attrs.put(attrEntry.getKey(), userName);
                    }
                }
            }
        }
    }

    /**
     * 转换关联资产属性  json 为主键
     * @param attrDefs
     * @param attrs
     * @param releAssetMap
     */
    public static void ciExportTranRelevancyAsset(List<CcCiAttrDef> attrDefs, Map<String, Object> attrs, Map<String, String> releAssetMap) {
        List<CcCiAttrDef> ciAttrDefs = attrDefs.stream().filter(e -> e.getProType().equals(AttrNameKeyEnum.LINK_CI.getType())).collect(Collectors.toList());
        for (CcCiAttrDef ciAttrDef : ciAttrDefs) {
            if (!BinaryUtils.isEmpty(attrs.get(ciAttrDef.getProStdName()))) {
                Object value = attrs.get(ciAttrDef.getProStdName());
                if (!BinaryUtils.isEmpty(value)&&value.toString().contains("[")) {
                    List<JSONObject> valueJson = JSONObject.parseArray(value.toString(), JSONObject.class);
                    List<String> tranValueList = new ArrayList<>();
                    for (JSONObject jsonObject : valueJson) {
                        String ciCode = jsonObject.getString("ciCode");
                        String primary = releAssetMap.get(ciCode);
                        if (!BinaryUtils.isEmpty(primary)) {
                            tranValueList.add(primary);
                        } else {
                            tranValueList.add(jsonObject.getString("primary"));
                        }
                    }
                    attrs.put(ciAttrDef.getProStdName(), tranValueList.stream().collect(Collectors.joining(",")));
                }
            }
        }
    }

    public static void ciExportPrefixIntegerAsset(List<CcCiAttrDef> attrDefs, Map<String, Object> attrs) {
        List<CcCiAttrDef> ciAttrDefs = attrDefs.stream().filter(e -> e.getProType().equals(AttrNameKeyEnum.PREFIX_INTEGER_CODE.getType())).collect(Collectors.toList());
        for (CcCiAttrDef ciAttrDef : ciAttrDefs) {
            String defVal = ciAttrDef.getDefVal();
            Object value = attrs.get(ciAttrDef.getProStdName());
            if (value == null) {
                attrs.put(ciAttrDef.getProStdName(), "");
            } else {
                String showVal = defVal + "[" + value + "]";
                attrs.put(ciAttrDef.getProStdName(), showVal);
            }
        }
    }


    public static void getRlerAssetCiCodeList(List<String> releAssetAttrNameList, Page<ESCIInfo> page, Set<String> releAssetCiCodeList) {
        for (ESCIInfo esciInfo : page.getData()) {
            Set<Map.Entry<String, Object>> entries = esciInfo.getAttrs().entrySet();
            for (Map.Entry<String, Object> entry : entries) {
                String attrName = entry.getKey();
                if (releAssetAttrNameList.contains(attrName) && !BinaryUtils.isEmpty(entry.getValue())) {
                    String value = entry.getValue().toString();
                    if (value.toString().contains("[")) {
                        List<JSONObject> valueJson = JSONObject.parseArray(value, JSONObject.class);
                        valueJson.forEach(e -> {
                            if (!BinaryUtils.isEmpty(e.get(CI_CODE))) {
                                releAssetCiCodeList.add(e.get(CI_CODE).toString());
                            }
                        });
                    }
                }
            }
        }
    }

}
