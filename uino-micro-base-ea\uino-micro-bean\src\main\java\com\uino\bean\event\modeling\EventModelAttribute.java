package com.uino.bean.event.modeling;

import lombok.Data;

/**
 * 事件的字段属性描述类。
 * <AUTHOR> Gao
 *
 */
@Data
public class EventModelAttribute {

	/**
	 * 属性名称
	 */
	private String name;

	/**
	 * 属性别名
	 */
	private String alias;

	/**
	 * 是否允许为空
	 */
	private boolean isNullAble;

	/**
	 * 数据库中的数据类型, 支持数值, 字符串, 时间类型
	 */
	private String dataType;

	/**
	 * 默认值
	 */
	private String defaultValue;

	/**
	 * 标识字段内容是否可以在标准化时重置。
	 */
	private boolean isRedefine;
	
	/**
	 * 标识字段内容是否可以在压缩时更新数据库。
	 */
	private boolean isUpatedOnDeduplicate ;
	
	/**
	 * 标识字段的来源 [EVENT/CI/KPI]
	 * 
	 * EVENT:接收到的事件内容中包含的字段
	 * CI:通过CI解析出的字段
	 * KPI:通过KPI解析出的字段
	 */
	private String sourceType;

	/**
	 * 用于映射CI或KPI字段在mmdb库中的对应字段
	 */
	private String mmdbKey;

	/**
	 * 是否作为缓存查询字段（存储redis时，将查询字段拼接作为key，id集合作为value，实现按照条件检索）
	 */
	private boolean isRedisSearchKey;

	public String toString() {
		return "name:" + name + ",data type: " + dataType;
	}



}
