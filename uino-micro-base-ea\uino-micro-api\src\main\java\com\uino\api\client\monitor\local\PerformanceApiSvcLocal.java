package com.uino.api.client.monitor.local;

import java.util.List;

import com.uino.dao.BaseConst;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.binary.core.io.Resource;
import com.binary.jdbc.Page;
import com.uino.api.client.monitor.IPerformanceApiSvc;
import com.uino.bean.chart.bean.UinoChartDataBean;
import com.uino.bean.monitor.base.SimulationRuleInfo;
import com.uino.bean.monitor.buiness.ImportPerformanceReqDto;
import com.uino.bean.monitor.buiness.PerformanceQueryDto;
import com.uino.bean.tp.base.FinalPerformanceDTO;
import com.uino.service.simulation.IPerformanceSvc;

@Service
public class PerformanceApiSvcLocal implements IPerformanceApiSvc {

    @Autowired
    private IPerformanceSvc svc;

    @Override
	public void importPerformance(MultipartFile excelFile, ImportPerformanceReqDto importDto) {
        // TODO Auto-generated method stub
		svc.importPerformance(excelFile, importDto);
    }

    @Override
    public Resource exportPerformanceTemplate(Long objId, Integer objType) {
        // TODO Auto-generated method stub
        return svc.exportPerformanceTemplate(BaseConst.DEFAULT_DOMAIN_ID,objId, objType);
    }

    @Override
    public Resource exportPerformanceTemplate(Long domainId, Long objId, Integer objType) {
        return svc.exportPerformanceTemplate(domainId,objId, objType);
    }

    @Override
	public Page<FinalPerformanceDTO> searchPerformance(PerformanceQueryDto queryDto) {
        // TODO Auto-generated method stub
        return svc.searchPerformance(queryDto);
    }

    @Override
	public UinoChartDataBean<List<Double>> searchPerformanceGraph(PerformanceQueryDto queryDto) {
		return svc.searchPerformanceGraph(queryDto);
	}

	@Override
	public Page<FinalPerformanceDTO> searchNoCiPerformance(PerformanceQueryDto queryDto) {
        return svc.searchNoCiPerformance(queryDto);
    }

    @Override
    public List<String> getPerfDataLabel(Long classId) {
        return svc.getPerfDataLabel(classId);
    }

    @Override
    public void importPerformance(ImportPerformanceReqDto importDto) {
        // TODO Auto-generated method stub
        svc.importPerformance(importDto);
    }

	@Override
	public void simulationPerformance(SimulationRuleInfo bean) {
		svc.simulationPerformance(bean);
	}
}
