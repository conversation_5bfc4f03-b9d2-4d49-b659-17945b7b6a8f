package com.uino.util.message.queue.tools;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONValidator;
import com.alibaba.fastjson.TypeReference;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * ConsumerTools Mainly used to convert messages received by consumers.
 * The supported conversions are:
 * <br>
 * {List&lt;Map&lt;String, Object&gt;&gt;}
 * <br>
 * {Map&lt;String, Object&gt;}
 *
 * @Author: YGQ
 * @Create: 2021-05-24 14:00
 **/
public class ConsumerTools {
    public static final TypeReference<List<Map<String, Object>>> LIST_MAP_TYPE = new TypeReference<List<Map<String, Object>>>() {
    };
    public static final TypeReference<Map<String, Object>> MAP_TYPE = new TypeReference<Map<String, Object>>() {
    };

    /**
     * Check the received batch data and convert it to List if it is not empty
     *
     * @param message Object<String>
     */
    public static List<Map<String, Object>> verifyAndConvertDataToList(Object message) {
        List<Map<String, Object>> convertListData = new ArrayList<>(128);
        try {
            Optional<?> result = Optional.ofNullable(message);
            if (result.isPresent()) {
                String messageStr = message.toString();
                JSONValidator.Type messageType = JSONValidator.from(messageStr).getType();
                if (messageType == JSONValidator.Type.Array) {
                    JSONArray messageArray = JSON.parseArray(messageStr);
                    JSONValidator.Type messageElementType = JSONValidator.from(messageArray.getString(0)).getType();
                    if (messageElementType == JSONValidator.Type.Object) {
                        convertListData = JSON.parseObject(message.toString(), LIST_MAP_TYPE);
                    } else if (messageElementType == JSONValidator.Type.Array) {
                        for (Object element : messageArray) {
                            List<Map<String, Object>> convertElementListData = JSON.parseObject(element.toString(), LIST_MAP_TYPE);
                            convertListData.addAll(convertElementListData);
                        }
                    } else {
                        throw new ClassCastException("message convert exception, unsupported format only convertible List<Map>");
                    }
                } else {
                    throw new ClassCastException("message convert exception, expect to input list the current type is " + messageType + ", current data : " + messageStr);
                }
            } else {
                throw new NullPointerException("message convert exception, message is empty");
            }
        } catch (Exception e) {
            throw new ClassCastException("message convert exception, unsupported format: " + e.getMessage());
        }
        return convertListData;
    }

    /**
     * Check the received single data, if it is not empty, convert it to Map
     */
    public static Map<String, Object> verifyAndConvertDataToMap(String message) {
        Map<String, Object> convertMapData;
        try {
            Optional<?> result = Optional.ofNullable(message);
            if (result.isPresent()) {
                JSONValidator.Type messageType = JSONValidator.from(message).getType();
                if (JSONValidator.Type.Object == messageType) {
                    convertMapData = JSON.parseObject(message, MAP_TYPE);
                } else {
                    throw new ClassCastException("message convert exception, expect to input map the current type is " + messageType + ", current data : " + message);
                }
            } else {
                throw new NullPointerException("message convert exception, message is empty");
            }
        } catch (Exception e) {
            throw new ClassCastException("message convert exception, unsupported format: " + e.getMessage());
        }
        return convertMapData;
    }
}
