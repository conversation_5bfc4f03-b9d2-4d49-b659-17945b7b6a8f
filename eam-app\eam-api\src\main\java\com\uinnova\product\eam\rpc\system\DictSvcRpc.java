package com.uinnova.product.eam.rpc.system;

import com.uinnova.product.eam.api.IDictAPIClient;
import com.uinnova.product.eam.base.model.DictInfo;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;

@Service
public class DictSvcRpc implements IDictAPIClient {


    @Override
    public List<DictInfo> selectListByType(String codeType, String parentCode, String className) {
        return Collections.emptyList();
    }

    @Override
    public Map<String, List<DictInfo>> selectGroupList(List<String> codeTypes, String className) {
        return Collections.emptyMap();
    }

    @Override
    public Map<String, String> getAllInfo(List<String> codeTypes, String className) {
        return Collections.emptyMap();
    }
}
