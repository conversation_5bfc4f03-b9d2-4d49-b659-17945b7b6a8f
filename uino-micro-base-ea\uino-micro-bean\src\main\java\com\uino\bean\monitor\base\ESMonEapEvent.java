package com.uino.bean.monitor.base;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.uino.bean.event.EntityEsBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

@Data
@NoArgsConstructor
@ApiModel(value = "全量事件表[MON_EAP_EVENT]", description = "全量事件表[MON_EAP_EVENT]")
public class ESMonEapEvent extends EntityEsBean<String> implements Serializable {

    @ApiModelProperty(value = "ID[ID]    代码自动生成必须ID", example = "HW91003-8540")
    private String id;


    @ApiModelProperty(value = "事件序列号[SERIAL]", example = "HW91003-8540")
    private String serial;


    @ApiModelProperty(value = "压缩唯一标识[IDENTIFIER]", example = "178_123")
    private String identifier;


    @ApiModelProperty(value = "级别[SEVERITY]", example = "2")
    private Integer severity;


    @ApiModelProperty(value = "事件描述[SUMMARY]", example = "")
    private String summary;


    @ApiModelProperty(value = "首次发生时间[FIRSTOCCURRENCE]", example = "")
    private Date firstOccurrence;


    @ApiModelProperty(value = "最近发生时间[LASTOCCURRENCE]", example = "")
    private Date lastOccurrence;


    @ApiModelProperty(value = "状态变更时间[STATECHANGE]", example = "")
    private Date stateChange;


    @ApiModelProperty(value = "重复次数[TALLY]", example = "")
    private Integer tally;


    @ApiModelProperty(value = "状态[STATUS]", example = "")
    private Integer status;


    @ApiModelProperty(value = "是否确认[ACKNOWLEDGED]", example = "")
    private Integer acknowledged;


    @ApiModelProperty(value = "是否升级[GRADE]", example = "")
    private Integer grade;


    @ApiModelProperty(value = "事件标题[EVENTTITLE]", example = "")
    private String eventTitle;


    @ApiModelProperty(value = "服务器名称[SERVERNAME]", example = "")
    private String servername;


    @ApiModelProperty(value = "服务器告警序列号[SERVERSERIAL]", example = "")
    private String serverSerial;


    @ApiModelProperty(value = "事件源标识[SOURCEID]", example = "")
    private Integer sourceId;


    @ApiModelProperty(value = "事件源描述[SOURCENAME]", example = "")
    private String sourceName;


    @ApiModelProperty(value = "事件源事件标识[SOURCEIDENTIFIER]    源事件标识", example = "")
    private String sourceIdentifier;


    @ApiModelProperty(value = "事件源事件序列号[SOURCEEVENTID]    源事件序列号", example = "")
    private String sourceEventId;


    @ApiModelProperty(value = "事件源事件CI名称[SOURCECINAME]    源事件CI名称", example = "")
    private String sourceCiName;


    @ApiModelProperty(value = "事件源事件KPI名称[SOURCEALERTKEY]    源事件KPI名称", example = "")
    private String sourceAlertKey;


    @ApiModelProperty(value = "事件源事件级别[SOURCESEVERITY]    源事件级别", example = "")
    private String sourceSeverity;


    @ApiModelProperty(value = "事件源事件描述[SOURCESUMMARY]    源事件描述", example = "")
    private String sourceSummary;


    @ApiModelProperty(value = "确认信息[ACKINFO]", example = "")
    private String ackInfo;


    @ApiModelProperty(value = "确认时间[ACKTIME]", example = "")
    private Date ackTime;


    @ApiModelProperty(value = "确认用户[ACKUID]", example = "")
    private String ackUid;


    @ApiModelProperty(value = "关闭信息[CLOSEINFO]", example = "")
    private String closeInfo;


    @ApiModelProperty(value = "关闭时间[CLOSETIME]", example = "")
    private Date closeTime;


    @ApiModelProperty(value = "关闭用户[CLOSEUID]", example = "")
    private String closeUId;


    @ApiModelProperty(value = "告警指标ID[KPIID]", example = "")
    private String kpiId;


    @ApiModelProperty(value = "告警指标名称[KPINAME]", example = "")
    private String kpiName;


    @ApiModelProperty(value = "告警指标分类ID[KPICATEGORYID]", example = "")
    private String kpiCategoryId;


    @ApiModelProperty(value = "告警指标分类名称[KPICATEGORYNAME]", example = "")
    private String kpiCategoryName;


    @ApiModelProperty(value = "指标描述[KPIDESCRIPTION]", example = "")
    private String kpiDescription;


    @ApiModelProperty(value = "告警指标实例[KPIINSTANCE]", example = "")
    private String kpiInstance;


    @ApiModelProperty(value = "告警指标二级分类[KPITYPE]", example = "")
    private String kpiType;


    @ApiModelProperty(value = "告警指标三级分类[KPIITEM]", example = "")
    private String kpiItem;


    @ApiModelProperty(value = "告警指标指标域[KPIDOMAIN]", example = "")
    private String kpiDomain;


    @ApiModelProperty(value = "告警指标单位[KPIUNIT]", example = "")
    private String kpiUnit;


    @ApiModelProperty(value = "配置项ID[CIID]", example = "")
    private String ciId;

    @ApiModelProperty(value = "配置项名称[CINAME]", example = "")
    private String ciName;

    @ApiModelProperty(value = "配置项名称[ciname]", example = "")
    private String ciname;

    public void setCustomCiName(String ciname) {
        this.ciname = ciname;
    }
    public String getCustomCiName() {
        return this.ciname;
    }

    @ApiModelProperty(value = "配置项主键[CIPRIMARYKEY]", example = "")
    private String ciPrimaryKey;

    @ApiModelProperty(value = "配置项分类ID[CICATEGORYID]", example = "")
    private String ciCategoryId;


    @ApiModelProperty(value = "配置项分类名称[CICATEGORYNAME]", example = "")
    private String ciCategoryName;


    @ApiModelProperty(value = "配置项二级分类[CITYPE]", example = "")
    private String ciType;


    @ApiModelProperty(value = "配置项三级分类[CIITEM]", example = "")
    private String ciItem;


    @ApiModelProperty(value = "配置项应用系统[CIAPPLICATION]", example = "")
    private String ciApplication;


    @ApiModelProperty(value = "配置项负责人[CIOWNER]", example = "")
    private String ciOwner;


    @ApiModelProperty(value = "配置项管理部室[CIMGMTTEAM]", example = "")
    private String ciMgmtTeam;


    @ApiModelProperty(value = "配置项数据中心[CIDATACENTER]", example = "")
    private String ciDataCenter;


    @ApiModelProperty(value = "配置项物理位置[CILOCATION]", example = "")
    private String ciLocation;


    @ApiModelProperty(value = "配置项状态[CISTATUS]", example = "")
    private String ciStatus;


    @ApiModelProperty(value = "配置项应用类型[CIUSAGETYPE]", example = "")
    private String ciUsagetype;


    @ApiModelProperty(value = "配置项群组[CIMGMTGROUP]", example = "")
    private String ciMgmtGroup;


    @ApiModelProperty(value = "配置项部署单元[CIDEPLOYUNIT]", example = "")
    private String ciDeployUnit;


    @ApiModelProperty(value = "是否屏蔽[BLACKOUT]", example = "")
    private Integer blackout;


    @ApiModelProperty(value = "是否已经短信通知[ALARMSMS]", example = "")
    private Integer alarmSms;


    @ApiModelProperty(value = "是否已经邮件通知[ALARMEMAIL]", example = "")
    private Integer alarmEmail;


    @ApiModelProperty(value = "是否已经创建工单[ALARMTICKET]", example = "")
    private Integer alarmTicket;


    @ApiModelProperty(value = "是否已经调用自动工作流[ALARMWORKFLOW]", example = "")
    private Integer alarmWorkflow;


    @ApiModelProperty(value = "重复事件序列号[DUPLICATESERIAL]", example = "")
    private String duplicateSerial;


    @ApiModelProperty(value = "维护期ID[MAPERIODID]", example = "")
    private Long maPeriodId;


    @ApiModelProperty(value = "所属场景[SCENE]", example = "")
    private String scene;


    @ApiModelProperty(value = "所属视图[VIEWID]", example = "")
    private Long viewId;


    @ApiModelProperty(value = "是否向订阅者发送通知[IFNOTIFY]", example = "")
    private Integer ifNotify;


    @ApiModelProperty(value = "持续时间[DURATION]", example = "")
    private Long duration;


    @ApiModelProperty(value = "重定级前的原始级别[OLDSEVERITY]", example = "")
    private Integer oldSeverity;


    @ApiModelProperty(value = "过滤类型[FILTERTYPE]    0：屏蔽;1: 直接关闭;2:进入维护期状态", example = "")
    private Integer filterType;


    @ApiModelProperty(value = "标签[TAG]", example = "")
    private Integer tag;


    @ApiModelProperty(value = "CREATE_TIME[CREATE_TIME]    yyyyMMddHHmmss", example = "")
    private Long createTime;

    @ApiModelProperty(value = "MODIFY_TIME[MODIFY_TIME]    yyyyMMddHHmmss", example = "")
    private Long modifyTime;

    @ApiModelProperty(value = "额外属性", example = "")
    private Map<String, Object> attribute;

    @ApiModelProperty(value = "CREATE_TIME[CREATE_TIME]    yyyyMMddHHmmss", example = "")
    private Long create_time;

    @ApiModelProperty(value = "MODIFY_TIME[MODIFY_TIME]    yyyyMMddHHmmss", example = "")
    private Long modify_time;

    @ApiModelProperty(value = "自定义字段", example = "")
    private String customize;

    @ApiModelProperty("孪生体名称[DTNAME]")
    private String DTName;

    @ApiModelProperty("地理位置[DTPHYSICALLOCATION]")
    private String DTPhysicallocation;

    @ApiModelProperty("DT所属空间[DTOWNEDSPACE]")
    private String DTOwnedSpace;
    /**
     * 事件转发时的传输标志。
     */
    private String transferFlag;

    @JSONField(name = "fieldMap")
    private JSONObject otherFieldMap;

    private boolean success;

    private int resultCode;

    private String message;

    private Long domainId;

    public ESMonEapEvent(JSONObject receivedEvent) {
        otherFieldMap = receivedEvent;
    }


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getIdentifier() {
        return identifier;
    }

    public void setIdentifier(String identifier) {
        this.identifier = identifier;
    }

    public JSONObject getFieldMap() {
        return otherFieldMap;
    }

    public String getTransferFlag() {
        return transferFlag;
    }

    public void setOtherFieldMap(JSONObject otherFieldMap) {
        this.otherFieldMap = otherFieldMap;
    }

    public void setTransferFlag(String transferFlag) {
        this.transferFlag = transferFlag;
    }

    /**
     * 设置某个字段的值。
     *
     * @param field 字段名
     * @param value 字段值
     */
    public void put(String field, String value) {
        this.otherFieldMap.put(field, value);
    }

    /**
     * 设置某个字段的值。
     *
     * @param field 字段名
     * @param value 字段值
     */
    public void putInteger(String field, Integer value) {
        this.otherFieldMap.put(field, value);
    }

    /**
     * 设置某个字段的值。
     *
     * @param field 字段名
     * @param value 字段值
     */
    public void putLongDate(String field, Long value) {
        this.otherFieldMap.put(field, value);
    }

    public void putBoolean(String field, boolean value) {
        this.otherFieldMap.put(field, value);
    }

    /**
     * 得到某个字段的值
     *
     * @param field 字段名
     * @return 返回某个字段的值
     */
    public String get(String field) {
        return this.otherFieldMap.getString(field);
    }

    public JSONObject getJSONObject(String field) {
        return this.otherFieldMap.getJSONObject(field);
    }

    public JSONArray getJSONArray(String field) {
        return this.otherFieldMap.getJSONArray(field);
    }

    public boolean getBooleanValue(String field) {
        return this.otherFieldMap.getBooleanValue(field);
    }

    /**
     * 得到某个字段值
     *
     * @param field 字段名
     * @return 返回某个字段的值
     */
    public Short getShort(String field) {
        return this.otherFieldMap.getShort(field);
    }

    /**
     * 得到某个字段值
     *
     * @param field 字段名
     * @return 返回某个字段的值
     */
    public Integer getInteger(String field) {
        return this.otherFieldMap.getInteger(field);
    }

    /**
     * 得到某个字段值
     *
     * @param field 字段名
     * @return 返回某个字段的值
     */
    public Long getLongDate(String field) {
        return this.otherFieldMap.getLong(field);
    }

    /**
     * 得到所有字段的值。
     *
     * @return
     */
    @JSONField(serialize = false)
    public JSONObject getAll() {
        if (null == otherFieldMap) {
            otherFieldMap = new JSONObject(16);
        }
        if (!otherFieldMap.containsKey("IDENTIFIER")) {
            this.put("IDENTIFIER", identifier);
        }
        if (!otherFieldMap.containsKey("EVENTID")) {
            this.put("EVENTID", getId());
        }
        return otherFieldMap;
    }

    public boolean containsKey(String key) {
        return this.otherFieldMap.containsKey(key);
    }

    public boolean getSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public int getResultCode() {
        return resultCode;
    }

    public void setResultCode(int resultCode) {
        this.resultCode = resultCode;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public JSONObject toJSONObject() {
        return getFieldMap();
    }

}
