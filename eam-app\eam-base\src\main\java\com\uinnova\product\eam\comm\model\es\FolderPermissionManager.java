package com.uinnova.product.eam.comm.model.es;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 文件夹权限表
 *
 * <AUTHOR>
 * @since 2022/6/30 10:16
 */
@Data
@Accessors
public class FolderPermissionManager implements Cloneable, Serializable {

    /**
     * id
     */
    private Long id;

    /**
     * 文件夹id
     */
    private Long dirId;

    /**
     * 文件夹类型
     */
    private Integer dirType;

    /**
     * 用户唯一标识
     */
    private String userLoginCode;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 角色id
     */
    private Long roleId;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 权限范围
     */
    private FolderApplicationScope folderApplicationScope;

    /**
     * 文件夹权限
     */
    private FolderPermission folderPermissions;

    /**
     * 文件权限
     */
    private FilePermission filePermissions;

    /**
     * 模型权限
     */
    private ModelPermission modelPermission;

    /**
     * 是否是继承的权限
     */
    private Boolean extendPermission;

    /**
     * 此处为浅克隆，若要深克隆可使用序列化方式进行深克隆
     * @return 副本
     * @throws CloneNotSupportedException
     */
    @Override
    public Object clone() throws CloneNotSupportedException {
        return super.clone();
    }
}
