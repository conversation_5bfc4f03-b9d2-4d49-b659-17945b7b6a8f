package com.uinnova.product.eam.base.exception;

public class ServerException extends EamException {

    public static final long serialVersionUID = 1;

    public ServerException() {
        super();
    }

    public ServerException(String message) {
        super(message);
    }

    public ServerException(String message, Throwable cause) {
        super(message, cause);
    }

    public ServerException(Throwable cause) {
        super(cause);
    }
}
