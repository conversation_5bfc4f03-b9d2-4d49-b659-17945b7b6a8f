# 开发测试配置文件
#端口
server.port=1536
#服务名称
spring.application.name=common-web
#服务前置地址
server.servlet.context-path=/tarsier-comm
#权限模块http server path
permission.http.prefix=http://*************:1536/tarsier-comm
#本地资源http服务地址
http.resource.space=http://*************/rsm
#各节点之间同步资源访问的url（当前服务地址）
http.resource.sync=http://*************:1536/tarsier-comm
#静态资源路径
spring.resources.static-locations=file:/home/<USER>/rsm
#本地资源存储地址
local.resource.space =D://
#创建分类是否显示3D模型属性
is.show.3d.attribute=true
# 创建分类是否显示文档属性
is.show.document.attribute=false
#自定义3D模型名称
3d.attribute.name=3D模型

#是否开启oauth鉴权
oauth.open=true
#oauth客户端id/资源端id
oauth.client.id=tarsier-comm
#客户端密钥
oauth.client.secret=tarsier-comm
#请求授权方式
oauth.client.grant_type=authorization_code
#认证中心地址
oauth.server.url=http://*************/oauth
#认证中心内网地址
oauth.server.in_url=http://*************/oauth
#code换token回调地址
oauth.server.token_callback.url=http://localhost:1536/tarsier-comm/getTokenByCode
#覆盖同名bean
spring.main.allow-bean-definition-overriding=true
spring.main.lazy-initialization=true


#elasticsearch相关配置
esIps=************:9200
isAuth=true
esUser=uinnova
esPwd=Uinnova@123


#基础模块服务加载方式，支持local/rpc
base.load-type=local
uino.license=true
#是否加载tp模块
base.load-tp=false
#dix接收性能数据接口
base.perf.dix-url=http://**************:10010/http/rest/tp/perf

spring.servlet.multipart.max-file-size=500MB
spring.servlet.multipart.max-request-size=500MB
server.tomcat.basedir=.
#同步请求超时时间，单位毫秒
spring.mvc.async.request-timeout=1000000
#连接超时时间-1为不限制
server.connection-timeout=-1

#对象管理勾选业务主键数量限制
uino.base.ci.primarykey.maxcount=5
#日志保留时长，单位：天
uino.log.clear.duration=7

#忽略授权认证的地址
license.ignore.filter.pattern=**.mp3;**.mp4;**.wav;**.js;**.css;**.jpg;**.jpeg;**.bmp;**.gif;**.png;**.ico;**.swf;**.eot;**.svg;**.ttf;**.woff;**.woff2;**.htm;**.html;**.txt;**.xml;**.json;**.map;/license/auth/**;/redirectAuth;/getTokenByCode;/permission/user/getCurrentUser;/permission/module/getModuleTree;/sys/getLogos;/sys/tenantDomain/multiTenantStatus;/**;
#授权跳转地址（多租户下为超管跳转地址）
project.license.register.url=http://*************/examples/#/license
#普通用户授权失败后跳转页面，需要在多租户下生效
project.license.ordinary.register.url=http://*************/examples/#/license

kpi.units=度,斤

## 是否有ep(无ep告警直接入库)
uino.monitor.ep.exist=false

#模拟告警发送调用的Dix提供的发送告警服务的URL
uino.monitor.event.send.url=http://0.0.0.0:10015/http/rest/event/mq/import

#关系遍历批处理
batch.process.relation.rule.cron = 0 0 */1 * * ?

#受信任的的Referer列表，多个用;隔开，为空代表不做该安全验证
safe.trust.referers=

base.api.enable=true
api.enable.user-id=1
api.enable.login-code=admin
api.enable.user-name=admin
api.enable.domain-id=1

#性能告警数据查询约束是否包含domainId
monitor.select.include.domainId=true

#多租户域
uino.tenantDomain=true

# kafka 地址
#kafka.server=192.168.21.147:9092,192.168.21.148:9092,192.168.21.149:9092

#模拟数据定时任务开关
base.simulation.rule.auto.excute= true