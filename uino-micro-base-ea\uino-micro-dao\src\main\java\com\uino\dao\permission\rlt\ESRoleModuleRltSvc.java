package com.uino.dao.permission.rlt;

import com.binary.core.util.BinaryUtils;
import com.uino.bean.permission.base.SysModule;
import com.uino.bean.permission.base.SysRoleModuleRlt;
import com.uino.bean.permission.query.CSysRoleModuleRlt;
import com.uino.dao.AbstractESBaseDao;
import com.uino.dao.ESConst;
import com.uino.util.sys.CommonFileUtil;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.util.LinkedList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <b>角色-功能模块关系
 * 
 * <AUTHOR>
 */
@Service
    public class ESRoleModuleRltSvc extends AbstractESBaseDao<SysRoleModuleRlt, CSysRoleModuleRlt> {

    @Override
    public String getIndex() {
        return ESConst.INDEX_SYS_ROLE_MODULE_RLT;
    }

    @Override
    public String getType() {
        return ESConst.INDEX_SYS_ROLE_MODULE_RLT;
    }

    @PostConstruct
    public void init() {
		List<SysModule> modules = CommonFileUtil.getData("/initdata/uino_sys_module.json", SysModule.class);
        List<SysRoleModuleRlt> roleModuleMappings = new LinkedList<>();
        modules.stream().collect(Collectors.groupingBy(SysModule::getId)).keySet().forEach(moduleId -> {
//            roleModuleMappings.add(SysRoleModuleRlt.builder().roleId(0L).moduleId(moduleId).build());
            roleModuleMappings.add(SysRoleModuleRlt.builder().roleId(1L).moduleId(moduleId).build());
        });
		List<SysRoleModuleRlt> data = CommonFileUtil.getData("/initdata/uino_sys_role_module_rlt.json",
				SysRoleModuleRlt.class);
		data = data.stream()
				.filter(roleModule -> roleModule.getRoleId() != 0L && roleModule.getRoleId() != 1L)
				.collect(Collectors.toList());
		if (!BinaryUtils.isEmpty(data)) {
			roleModuleMappings.addAll(data);
		}
        super.initIndex(roleModuleMappings);
    }

    /**
     * 获取角色下的数据权限
     * 
     * @param roleIds
     * @return
     */
    public List<SysRoleModuleRlt> getListByRoleIds(Set<Long> roleIds) {
        return super.getListByQuery(1, 2000, QueryBuilders.termsQuery("roleId", roleIds)).getData();
    }
}
