package com.uinnova.product.eam.model;

import java.io.Serializable;
import java.util.List;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.comm.model.VcNodeLinked;

@Comment("节点路径信息")
public class VcNodeLinkedInfo implements Serializable{

	private static final long serialVersionUID = 1L;

	@Comment("节点路径")
	private VcNodeLinked nodeLinked;
	
	@Comment("路径列表")
	private List<String> path;

	public VcNodeLinked getNodeLinked() {
		return nodeLinked;
	}

	public void setNodeLinked(VcNodeLinked nodeLinked) {
		this.nodeLinked = nodeLinked;
	}

	public List<String> getPath() {
		return path;
	}

	public void setPath(List<String> path) {
		this.path = path;
	}

}
