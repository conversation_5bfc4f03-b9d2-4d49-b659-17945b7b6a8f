<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<groupId>com.uino</groupId>
	<artifactId>eam-base</artifactId>
	<version>1.0.0-SNAPSHOT</version>
	<packaging>pom</packaging>

	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>3.4.5</version>
	</parent>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<maven-jar-plugin.version>3.1.1</maven-jar-plugin.version>
		<lombok.version>1.18.30</lombok.version>
		<java.version>17</java.version>
		<vmdb.version>4.0.0-SNAPSHOT</vmdb.version>
		<elasticsearch.version>7.17.28</elasticsearch.version>
		<snakeyaml.version>2.2</snakeyaml.version>
		<mica.version>2.7.18.4</mica.version>
		<i18n.version>5.140.1</i18n.version>
		<httpclient.version>4.5.13</httpclient.version>
		<knife4j.ui.version>3.0.2</knife4j.ui.version>
		<swagger.annotations.version>1.6.15</swagger.annotations.version>
		<log4j2.version>2.24.0</log4j2.version>
		<jackson.version>2.14.0</jackson.version>
		<nacos.config.version>2023.0.3.2</nacos.config.version>
		<poi.version>5.4.1</poi.version>
		<junit.version>4.13.2</junit.version>
		<hibernate-validator.version>6.2.5.Final</hibernate-validator.version>
		<jakarta.validation.version>2.0.2</jakarta.validation.version>
		<jakarta.servlet-api.version>6.1.0</jakarta.servlet-api.version>
		<jakarta.annotation-api.version>2.1.1</jakarta.annotation-api.version>
		<commons.compress.version>1.21</commons.compress.version>
		<com.fasterxml.jackson.version>2.13.4.2</com.fasterxml.jackson.version>
		<hutool.version>5.8.21</hutool.version>
		<itextpdf.version>5.5.13.3</itextpdf.version>
		<fastjson.version>1.2.83</fastjson.version>
		<openfeign.version>4.2.1</openfeign.version>
		<tomcat.version>11.0.6</tomcat.version>
		<ibatis.sqlmap.version>2.3.4.726</ibatis.sqlmap.version>
		<java.source.plugin.version>3.0.1</java.source.plugin.version>
		<jasypt.version>2.1.1</jasypt.version>
	</properties>


	<modules>
		<module>uino-micro-comm</module>
		<module>uino-micro-bean</module>
		<module>uino-micro-monitor</module>
		<module>uino-micro-service</module>
		<module>uino-micro-feign-client</module>
		<module>uino-micro-feign-server</module>
		<module>uino-micro-api</module>
		<module>uino-micro-web</module>
		<module>uino-micro-util</module>
    	<module>uino-micro-dao</module>
        <module>uino-micro-ed</module>
        <module>uino-micro-plugin</module>
    </modules>
	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>org.yaml</groupId>
				<artifactId>snakeyaml</artifactId>
				<version>${snakeyaml.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.commons</groupId>
				<artifactId>commons-compress</artifactId>
				<version>${commons.compress.version}</version>
			</dependency>
			<dependency>
				<groupId>org.xerial.snappy</groupId>
				<artifactId>snappy-java</artifactId>
				<version>1.1.10.4</version>
			</dependency>
			<dependency>
				<groupId>commons-net</groupId>
				<artifactId>commons-net</artifactId>
				<version>3.9.0</version>
			</dependency>

			<!-- Tomcat dependencies -->
			<dependency>
				<groupId>org.apache.tomcat.embed</groupId>
				<artifactId>tomcat-embed-core</artifactId>
				<version>${tomcat.version}</version><!--$NO-MVN-MAN-VER$ -->
			</dependency>
			<dependency>
				<groupId>org.apache.tomcat.embed</groupId>
				<artifactId>tomcat-embed-jasper</artifactId>
				<version>${tomcat.version}</version>
			</dependency>

			<!-- Jakarta dependencies -->
			<dependency>
				<groupId>jakarta.servlet</groupId>
				<artifactId>jakarta.servlet-api</artifactId>
				<version>${jakarta.servlet-api.version}</version>
			</dependency>
			<dependency>
				<groupId>jakarta.annotation</groupId>
				<artifactId>jakarta.annotation-api</artifactId>
				<version>${jakarta.annotation-api.version}</version>
			</dependency>
			<dependency>
				<groupId>jakarta.validation</groupId>
				<artifactId>jakarta.validation-api</artifactId>
				<version>${jakarta.validation.version}</version>
			</dependency>

			<!-- Commons dependencies -->
			<dependency>
				<groupId>commons-io</groupId>
				<artifactId>commons-io</artifactId>
				<version>2.7</version>
			</dependency>
			<dependency>
				<groupId>org.apache.httpcomponents</groupId>
				<artifactId>httpclient</artifactId>
				<version>${httpclient.version}</version>
			</dependency>

			<!-- Logging dependencies -->
			<dependency>
				<groupId>org.apache.logging.log4j</groupId>
				<artifactId>log4j-slf4j-impl</artifactId>
				<version>${log4j2.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.logging.log4j</groupId>
				<artifactId>log4j-core</artifactId>
				<version>${log4j2.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.logging.log4j</groupId>
				<artifactId>log4j-api</artifactId>
				<version>${log4j2.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.logging.log4j</groupId>
				<artifactId>log4j-jul</artifactId>
				<version>${log4j2.version}</version>
			</dependency>

			<!-- Utility dependencies -->
			<dependency>
				<groupId>com.google.guava</groupId>
				<artifactId>guava</artifactId>
				<version>33.4.8-jre</version>
			</dependency>
			<dependency>
				<groupId>com.alibaba</groupId>
				<artifactId>fastjson</artifactId>
				<version>${fastjson.version}</version>
			</dependency>
			<dependency>
				<groupId>cn.hutool</groupId>
				<artifactId>hutool-all</artifactId>
				<version>${hutool.version}</version>
			</dependency>
			<dependency>
				<groupId>com.github.ulisesbocchio</groupId>
				<artifactId>jasypt-spring-boot-starter</artifactId>
				<version>${jasypt.version}</version>
			</dependency>

			<!-- Office document dependencies -->
			<dependency>
				<groupId>org.apache.poi</groupId>
				<artifactId>poi</artifactId>
				<version>${poi.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.poi</groupId>
				<artifactId>poi-ooxml</artifactId>
				<version>${poi.version}</version>
			</dependency>

			<!-- Cloud dependencies -->
			<dependency>
				<groupId>com.alibaba.cloud</groupId>
				<artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
				<version>${nacos.config.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-starter-openfeign</artifactId>
				<version>${openfeign.version}</version>
			</dependency>

			<!-- Other dependencies -->
			<dependency>
				<groupId>net.minidev</groupId>
				<artifactId>json-smart</artifactId>
				<version>2.4.8</version>
			</dependency>
			<dependency>
				<groupId>io.swagger</groupId>
				<artifactId>swagger-annotations</artifactId>
				<version>${swagger.annotations.version}</version>
			</dependency>
		</dependencies>
	</dependencyManagement>
	<dependencies>
		<dependency>
			<groupId>jakarta.validation</groupId>
			<artifactId>jakarta.validation-api</artifactId>
		</dependency>

		<dependency>
			<groupId>com.uino</groupId>
			<artifactId>i18n</artifactId>
			<version>${i18n.version}</version>
		</dependency>

		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<scope>provided</scope>
			<version>${lombok.version}</version><!--$NO-MVN-MAN-VER$ -->
		</dependency>

		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-log4j2</artifactId>
		</dependency>

		<!-- 添加jakarta.annotation-api依赖，解决Java 9+中移除的jakarta.annotation包问题 -->
		<dependency>
			<groupId>jakarta.annotation</groupId>
			<artifactId>jakarta.annotation-api</artifactId>
		</dependency>
	</dependencies>

	<distributionManagement>
		<repository>
			<id>uinnova-releases</id>
			<name>Nexus Release Repository</name>
			<url>https://mvn-dev.uino.cn/repository/uinnova-releases/</url>
		</repository>
		<snapshotRepository>
			<id>uinnova-snapshots</id>
			<name>Nexus Snapshot Repository</name>
			<url>https://mvn-dev.uino.cn/repository/uinnova-snapshots/</url>
		</snapshotRepository>
	</distributionManagement>

	<repositories>
		<repository>
			<id>uino-releases</id>
			<name>Release of Uino</name>
			<url>https://mvn.uino.cn/repository/i18n-releases/</url>
		</repository>
	</repositories>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.10.1</version><!--$NO-MVN-MAN-VER$ -->
				<configuration>
					<source>${java.version}</source>
					<target>${java.version}</target>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<version>2.22.2</version><!--$NO-MVN-MAN-VER$ -->
				<configuration>
					<testFailureIgnore>true</testFailureIgnore>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-source-plugin</artifactId>
				<version>3.0.1</version><!--$NO-MVN-MAN-VER$ -->
				<executions>
					<execution>
						<id>attach-sources</id>
						<goals>
							<goal>jar-no-fork</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.jacoco</groupId>
				<artifactId>jacoco-maven-plugin</artifactId>
				<version>0.8.2</version>
				<executions>
					<execution>
						<id>prepare-agent</id>
						<goals>
							<goal>prepare-agent</goal>
						</goals>
					</execution>
				</executions>
			</plugin>

		</plugins>
	</build>

</project>