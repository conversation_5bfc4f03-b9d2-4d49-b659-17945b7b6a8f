package com.uinnova.product.eam.web.analog.mvc;

import java.util.List;

import com.uinnova.product.eam.model.diagram.EamHttpRequestParam;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import com.binary.framework.util.ControllerUtils;
import com.binary.json.JSON;
import com.uinnova.product.eam.web.analog.bean.CCBExternalData;
import com.uinnova.product.eam.web.analog.bean.CCBRequestParam;
import com.uinnova.product.eam.web.analog.bean.DmvHttpRequestParam;
import com.uinnova.product.eam.web.analog.peer.ExternalDataPeer;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uinnova.product.vmdb.comm.doc.annotation.MvcDesc;

@Controller
@RequestMapping("/dmv/externalData")
@MvcDesc(author="wangchuanping",desc="提供DMV查询外部数据的相关接口")
public class DmvExternalDataMvc {
	
	@Autowired
	private ExternalDataPeer externalDataPeer;
	
	@RequestMapping(value = "/requestByUrl")
	@ModDesc(desc = "通过URL请求外部数据，前端给定url", pDesc = "请求连接及参数信息",pType=DmvHttpRequestParam.class, rDesc = "信息", rType = Object.class)
	public void requestByUrl(HttpServletRequest request, HttpServletResponse response ,@RequestBody String body) {
		EamHttpRequestParam param = JSON.toObject(body, EamHttpRequestParam.class);
		Object result = externalDataPeer.requestByUrl(param);
		ControllerUtils.returnJson(request, response, result);

	}
	
	@RequestMapping(value = "/queryExternalDataForCCBITMap")
	@ModDesc(desc = "查询建行的IT世界地图外部数据，包括交易码，流水号查询路径以及实时关系", pDesc = "查询参数",pType=CCBRequestParam.class, rDesc = "信息", rType = List.class,rcType=CCBExternalData.class)
	public void queryExternalDataForCCBITMap(HttpServletRequest request, HttpServletResponse response,@RequestBody String body) {
		CCBRequestParam param = JSON.toObject(body, CCBRequestParam.class);
		Object result = externalDataPeer.queryExternalDataForCCBITMap(param);
		ControllerUtils.returnJson(request, response, result);

	}

    @RequestMapping({"/queryHealthDegreeList"})
    @ModDesc(desc="查询健康度", pDesc="查询参数", rDesc="健康度", rType=List.class)
    public void queryHealthDegreeByCiCodes(HttpServletRequest request, HttpServletResponse response) {
        Object result = externalDataPeer.queryHealthDegreeList();
        ControllerUtils.returnJson(request, response, result);
    }
}
