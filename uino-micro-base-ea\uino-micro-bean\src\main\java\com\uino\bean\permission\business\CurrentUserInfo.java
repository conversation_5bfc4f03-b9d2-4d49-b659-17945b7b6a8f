package com.uino.bean.permission.business;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 
 * <AUTHOR>
 *
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value="当前登录用户信息",description = "当前登录用户信息")
public class CurrentUserInfo {

    @ApiModelProperty(value="id",example = "123")
    private Long id;

    @ApiModelProperty(value="登录代码")
    private String loginCode;

    @ApiModelProperty(value="用户代码")
    private String userCode;

    @ApiModelProperty(value="用户名",example = "mike")
    private String userName;

    @ApiModelProperty(value="头像")
    private String icon;

    @ApiModelProperty(value="语言")
    private String language;

    @ApiModelProperty(value="所属领域id",example = "123")
    private Long domainId;

    @ApiModelProperty(value="最近一次登录时间")
    private Long lastLoginTime;

	@ApiModelProperty(value = "是否需要修改密码")
	private Integer isUpdatePwd;

    /** 电子邮件地址 */
    @ApiModelProperty(value="电子邮件地址",example = "<EMAIL>")
    private String emailAdress;

    @ApiModelProperty(value="访问资源的前缀",example = "/rsm")
    private String rsmPrefix;
}
