package com.uino.bean.cmdb.base.dataset.batch;


import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 存放简化完之后的朋友圈信息
 * <p>
 * CiNodes及CiRtLines中只提取ID进行存储
 *
 * <AUTHOR>
 * @version 2020/5/18
 */
public class SimpleFriendInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    private List<Long> ciClassIds;

    //节点上的Ci,value值为cicode
    private Map<Long, Set<Long>> ciIdMap;

    private List<Long> ciIds;

    private List<Long> ciRltLineIds;

    public List<Long> getCiClassIds() {
        return ciClassIds;
    }

    public void setCiClassIds(List<Long> ciClassIds) {
        this.ciClassIds = ciClassIds;
    }

    public Map<Long, Set<Long>> getCiIdMap() {
        return ciIdMap;
    }

    public void setCiIdMap(Map<Long, Set<Long>> ciIdMap) {
        this.ciIdMap = ciIdMap;
    }

    public List<Long> getCiIds() {
        return ciIds;
    }

    public void setCiIds(List<Long> ciIds) {
        this.ciIds = ciIds;
    }

    public List<Long> getCiRltLineIds() {
        return ciRltLineIds;
    }

    public void setCiRltLineIds(List<Long> ciRltLineIds) {
        this.ciRltLineIds = ciRltLineIds;
    }
    //    public SimpleFriendInfo(JSONObject json) {
//        if (json.containsKey("ciClassIds")) {
//            this.ciClassIds = JSONArray.parseArray(json.getString("ciClassIds"), Long.class);
//        }
//        if (json.containsKey("ciNodeMap")) {
//            JSONObject nodeMap = json.getJSONObject("ciNodeMap");
//            this.ciNodeMap = new HashMap<>();
//            nodeMap.forEach((k,v)->{
//                ciNodeMap.put(Long.parseLong(k),)
//            });
//
//
//        }
//        if (json.containsKey("ciCodes")) {
//            this.ciCodes = JSONArray.parseArray(json.getString("ciCodes"), String.class);
//        }
//        if (json.containsKey("ciRltLineCodes")) {
//            this.ciRltLineCodes = JSONArray.parseArray(json.getString("ciRltLineCodes"), String.class);
//        }
//    }
}
