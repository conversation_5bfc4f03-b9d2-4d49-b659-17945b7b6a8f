package com.uinnova.product.eam.base.diagram.model;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.vmdb.comm.model.ci.CcCiClassDir;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Classname
 * @Description 我的图形返回结果
 * <AUTHOR>
 * @Date 2021-08-24-10:21
 */
@Data
public class ESUserShapeResult implements Serializable {

    private static final Long serialVersionUID = 1L;

    @Comment("shapes")
    private List<ESUserShape> shapes;

    @Comment("dir")
    private CcCiClassDir dir;


}
