package com.uinnova.product.eam.service.diagram.impl;

import com.binary.core.lang.Conver;
import com.binary.core.util.BinaryUtils;
import com.binary.framework.Local;
import com.binary.jdbc.Page;
import com.uinnova.product.eam.base.diagram.mix.enums.DiagramQ;
import com.uinnova.product.eam.base.diagram.mix.model.VcDiagramVersionInfo;
import com.uinnova.product.eam.base.diagram.model.CVcDiagramVersion;
import com.uinnova.product.eam.base.diagram.model.VcDiagramVersion;
import com.uinnova.product.eam.db.VcDiagramVersionDao;
import com.uinnova.product.eam.model.diagram.VcDiagramInfo;
import com.uinnova.product.eam.service.diagram.VcDiagramVersionSvc;
import com.uinnova.product.vmdb.comm.i18n.MessageUtil;
import com.uinnova.product.vmdb.comm.i18n.VerifyType;
import com.uinnova.product.vmdb.comm.util.CommUtil;
import com.uino.api.client.permission.IUserApiSvc;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.permission.business.UserInfo;
import com.uino.util.sys.SysUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;


@Service
public class VcDiagramVersionSvcImpl implements VcDiagramVersionSvc {

	@Autowired
	private VcDiagramVersionDao diagramVersionDao;

	@Autowired
	private IUserApiSvc userApiSvc;
	
	@Override
	public List<VcDiagramVersion> queryVcDiagramVersionList(Long domainId,
															CVcDiagramVersion cdt, String orders) {
		MessageUtil.checkEmpty(domainId, "domainid");
		if (cdt == null) {
			cdt = new CVcDiagramVersion();
			cdt.setDomainId(domainId);
		}

		return diagramVersionDao.selectList(cdt, orders);
	}

	@Override
	public Page<VcDiagramVersion> queryVcDiagramVersionPageList(Long domainId,
																Integer pageNum, Integer pageSize, CVcDiagramVersion cdt,
																String orders) {
		MessageUtil.checkEmpty(domainId, "domainid");
		if (cdt == null) {
			cdt = new CVcDiagramVersion();
			cdt.setDomainId(domainId);
		}

		return diagramVersionDao.selectPage(pageNum, pageSize, cdt, orders);
	}
	
	@Override
	public VcDiagramVersionInfo queryDiagramVersionInfoById(Long domainId,
															Long id, DiagramQ[] diagramQs) {
		
		MessageUtil.checkEmpty(domainId, "domainid");
		MessageUtil.checkEmpty(id, "id");
		VcDiagramVersion diagramVersion = diagramVersionDao.selectById(id);
		if(diagramVersion == null){
			//TODO
			MessageUtil.throwVerify(VerifyType.NOT_EXIST, "record", "");
		}
		
		VcDiagramVersionInfo diagramVersionInfo = new VcDiagramVersionInfo();
		
		if(!BinaryUtils.isEmpty(diagramVersion.getVersionNo()) && diagramVersion.getVersionNo().startsWith("A")){
			String versionDesc = diagramVersion.getVersionDesc();
			List<String> upRelations = str2List(versionDesc);
			diagramVersionInfo.setUpRelations(upRelations);
		}
		
		/*CVcDiagramEleVersion cdt = new CVcDiagramEleVersion();
		cdt.setDomainId(domainId);
		cdt.setDiagramId(diagramVersion.getId());
		List<VcDiagramEleVersion> deVersions = diagramEleVersionDao.selectList(cdt , "ID");
		
		
		diagramVersionInfo.setDiagramVersion(diagramVersion);
		diagramVersionInfo.setDiagramEleVersions(deVersions);
		Long diagramId = diagramVersion.getDiagramId();
		
		if(diagramQs != null && diagramQs.length > 0 ){
			for (int i = 0; i < diagramQs.length; i++) {
				DiagramQ diagramQ = diagramQs[i];
				if(diagramQ == DiagramQ.GROUPS){
					CVcDiagramGroup dgCdt = new CVcDiagramGroup();
					dgCdt.setDomainId(domainId);
					dgCdt.setDiagramId(diagramId);
					List<VcDiagramGroup> dg = diagramGroupDao.selectList(dgCdt , "ID");
					diagramVersionInfo.setDiagramGroups(dg);
					int size = dg.size();
					if(size > 0){
						Long[] gIds = new Long[size];
						for (int j = 0; j < gIds.length; j++) {
							gIds[j] = dg.get(j).getGroupId();
						}
						CVcGroup gCdt = new CVcGroup();
						gCdt.setDomainId(domainId);
						gCdt.setIds(gIds);
						List<VcGroup> groups = groupDao.selectList(gCdt , "ID");
						diagramVersionInfo.setGroups(groups);
					}
				}else if(diagramQ == DiagramQ.TAGS){
					CVcDiagramTag dtCdt = new CVcDiagramTag();
					dtCdt.setDiagramId(diagramId);
					dtCdt.setDomainId(domainId);
					List<VcDiagramTag> dt = diagramTagDao.selectList(dtCdt, "ID");
					diagramVersionInfo.setDiagramTags(dt);
					int size = dt.size();
					if(size > 0){
						Long[] tIds = new Long[size];
						for (int j = 0; j < tIds.length; j++) {
							tIds[j] = dt.get(j).getTagId();
						}
						CVcTag tCdt = new CVcTag();
						tCdt.setDomainId(domainId);
						tCdt.setIds(tIds);
						List<VcTag> tags = tagDao.selectList(tCdt, "ID");
						diagramVersionInfo.setTags(tags);
					}
				}
			}
		}*/
		
		
		return diagramVersionInfo;
	}
	
	private List<String> str2List(String str){
		List<String> result = new ArrayList<String>();
		String[] split = str.split(",");
		for(String s : split){
			result.add(s);
		}
		return result;
	}
	
	@Override
	public Long saveDiagramVersionByDiagramId(Long domainId, Long diagramId) {
		/*// TODO 先判断100个上限,然后替换
		MessageUtil.checkEmpty(diagramId, "diagramId");
		VcDiagramInfo diagramInfo = diagramSvc.queryDiagramInfoById(domainId, diagramId, new DiagramQ[]{});
		if(diagramInfo == null) { 
			//TODO 数据不存在异常
			MessageUtil.throwVerify(VerifyType.NOT_EXIST, "diagramId", "diagramId");
		}
		VcDiagram diagram = diagramInfo.getDiagram();
		List<VcDiagramEle> diagramEles = diagramInfo.getDiagramEles();
		
		Integer versionNo = 1; 
		String startFlag = "";
		
		CVcDiagramVersion cdt = new CVcDiagramVersion();
		cdt.setDomainId(domainId);
		cdt.setDiagramId(diagramId);

		List<VcDiagramVersion> lastData = diagramVersionDao.selectList(1, 1, cdt, "ID  DESC");
		
		if(lastData.size() > 0){
			String vNo = lastData.get(0).getVersionNo();
			
			if(vNo.startsWith("A") || vNo.startsWith("M")){
				//startFlag = vNo.substring(0, 1);
				versionNo = Conver.to(vNo.substring(1, vNo.length()), Integer.class);
			}else{
				versionNo = Conver.to(vNo, Integer.class);
			}
			versionNo = versionNo+1;
		}
		
		VcDiagramVersion diagramVersion = CommUtil.copy(diagram, VcDiagramVersion.class);
		diagramVersion.setId(null);
		diagramVersion.setDiagramId(diagram.getId());
		diagramVersion.setVersionNo(startFlag+versionNo);
		diagramVersion.setVersionTime(BinaryUtils.getNumberDateTime());
		
		long id = diagramVersionDao.save(diagramVersion);
		
		if (!BinaryUtils.isEmpty(diagramEles)) {
			List<VcDiagramEleVersion> vEles = new ArrayList<VcDiagramEleVersion>();
			for (VcDiagramEle vcDiagramEle : diagramEles) {
				VcDiagramEleVersion e = new VcDiagramEleVersion();
				e.setDiagramId(id);
				e.setDomainId(domainId);
				e.setEleId(String.valueOf(vcDiagramEle.getId()));
				e.setEleType(vcDiagramEle.getEleType());
				vEles.add(e);
			}
			
			diagramEleVersionDao.saveBatch(vEles);
		}
		
		List<VcDiagramVersion> data =  diagramVersionDao.selectList(100, 1, cdt, "ID desc");
		if(data.size() > 0 ) {
			List<Long> idList = new ArrayList<Long>();
			for (VcDiagramVersion vcDiagramVersion : data) {
				idList.add(vcDiagramVersion.getId());
			}
			CVcDiagramVersion cdtt = new CVcDiagramVersion();
			cdtt.setIds(idList.toArray(new Long[]{}));
			try {
				diagramVersionDao.deleteByCdt(cdtt);
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		
		Local.commit();*/
		
		return 1L;
	}
	
	/**
	 * 组件创建者 格式：用户名[用户code]
	 * @return
	 */
	private String buildCreator() {
		String creator = null;
		//查询当前用户最新的信息
		SysUser loginUser = SysUtil.getCurrentUserInfo();
		UserInfo op = userApiSvc.getUserInfoById(loginUser.getId());
		if(!BinaryUtils.isEmpty(op)) {
			creator = op.getUserName()+"["+op.getLoginCode()+"]";
		} else {
			creator = loginUser.getUserName()+"["+ loginUser.getLoginCode()+"]";
		}
		return creator;
	}
	
	@Override
	public Long saveDiagramVersionByDiagramInfo(Long domainId, Long diagramId,VcDiagramInfo diagramInfo) {
		/*VcDiagram diagram = diagramInfo.getDiagram();
		List<VcDiagramEle> diagramEles = diagramInfo.getDiagramEles();

		Integer versionNo = 1; 
		
		CVcDiagramVersion cdt = new CVcDiagramVersion();
		cdt.setDomainId(domainId);
		cdt.setDiagramId(diagramId);
		VcDiagramVersion diagramVersion = CommUtil.copy(diagram, VcDiagramVersion.class);
		diagramVersion.setVersionNo(diagramInfo.getVersionNo());
		diagramVersion.setVersionName(diagramInfo.getVersionName());

		List<VcDiagramVersion> diagramVersions = diagramVersionDao.selectList(cdt, "VERSION_TIME DESC");
		if(!BinaryUtils.isEmpty(diagramVersions)){
			for(VcDiagramVersion version : diagramVersions){
				String vNo = version.getVersionNo();

				if(!BinaryUtils.isEmpty(vNo) && (vNo.startsWith("A") || vNo.startsWith("M"))){
					versionNo = Conver.to(vNo.substring(1, vNo.length()), Integer.class) + 1;
					break;
				}
			}
		}
		
		Integer dataUpType = diagram.getDataUpType();
		if(dataUpType != null && diagramInfo.getUpdateType() == 2){
			String startFlag = "";
			switch (dataUpType) {
				case 2: startFlag = "M"; break;
				case 3: startFlag = "A"; break;
				default: break;
			}
			
			//更新diagram表DataUpType 
			VcDiagram vcDiagram = new VcDiagram();
			vcDiagram.setId(diagramId);
			vcDiagram.setDataUpType(dataUpType);	
			diagramDao.updateById(vcDiagram, diagramId);
			
			diagramVersion.setVersionNo(startFlag + versionNo);
		}

		if (!StringUtils.isEmpty(diagramInfo.getDiagramVersionId())) {
			diagramVersion.setId(diagramInfo.getDiagramVersionId());
		} else {
			diagramVersion.setId(null);
		}
		diagramVersion.setDiagramId(diagram.getId());
		
		diagramVersion.setVersionTime(BinaryUtils.getNumberDateTime());
		//diagramVersion.setVersionDesc(diagramInfo.getVersionDesc());
		diagramVersion.setVersionDescPath(diagramInfo.getVersionDescPath());
		long id = diagramVersionDao.save(diagramVersion);
		Local.commit();
		if(!BinaryUtils.isEmpty(id)) {
			String creator = buildCreator();
			
			VcDiagramVersion update = new VcDiagramVersion();
			update.setDomainId(domainId);
			update.setId(id);
			update.setCreator(creator);
			diagramVersionDao.updateById(update, id);
		}
		
		if (!BinaryUtils.isEmpty(diagramEles)) {
			List<VcDiagramEleVersion> vEles = new ArrayList<VcDiagramEleVersion>();
			for (VcDiagramEle vcDiagramEle : diagramEles) {
				VcDiagramEleVersion e = new VcDiagramEleVersion();
				e.setDiagramId(id);
				e.setEleId(String.valueOf(vcDiagramEle.getEleId()));
				e.setEleCode(vcDiagramEle.getEleCode());
				e.setEleType(vcDiagramEle.getEleType());
				e.setDomainId(domainId);
				vEles.add(e);
			}
			
			diagramEleVersionDao.saveBatch(vEles);
		}
		
		List<VcDiagramVersion> data =  diagramVersionDao.selectList(100, 1, cdt, "ID desc");
		if(data.size() > 0 ) {
			List<Long> idList = new ArrayList<Long>();
			for (VcDiagramVersion vcDiagramVersion : data) {
				idList.add(vcDiagramVersion.getId());
			}
			CVcDiagramVersion cdtt = new CVcDiagramVersion();
			cdtt.setIds(idList.toArray(new Long[]{}));
			try {
				diagramVersionDao.deleteByCdt(cdtt);
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		Local.commit();

		// 检查如果自动创建(名称为空)的历史视图超过20个，则删除最早的那个视图
		CVcDiagramVersion cVcDiagramVersion = new CVcDiagramVersion();
		cVcDiagramVersion.setDiagramId(diagramId);
		cVcDiagramVersion.setDataStatus(1);

		List<VcDiagramVersion> create_time_desc_desc = diagramVersionDao.selectList(cVcDiagramVersion, "VERSION_TIME DESC");

		if (create_time_desc_desc.size() > 20) {
			int countEmpty = 0;
			for (int i = 0; i < create_time_desc_desc.size(); i++) {
				VcDiagramVersion vcDiagramVersion = create_time_desc_desc.get(i);
				if (StringUtils.isEmpty(vcDiagramVersion.getVersionName())) {
					if (countEmpty > 19) {
						diagramVersionDao.deleteById(vcDiagramVersion.getId());
					}
					countEmpty++;
				}
			}
		}

		return id;*/
		return 1L;
	}
	
	@Override
	public Integer removeDiagramVersionById(Long domainId, Long id) {
		/*if(id == null){
			return 1;
		}
		
		CVcDiagramEleVersion cdt = new CVcDiagramEleVersion();
		cdt.setDiagramId(id);
		try {
			diagramEleVersionDao.deleteByCdt(cdt);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		int result = diagramVersionDao.deleteById(id);
		Local.commit();
		return result;*/
		return 1;
	}

	@Override
	public Long saveOrUpdateDiagramVersion(Long domainId,
			VcDiagramVersion diagramVersion) {
		MessageUtil.checkEmpty(domainId, "domainid");
		MessageUtil.checkEmpty(diagramVersion, "diagramVersion");
		
		diagramVersion.setDomainId(domainId);
		long result = diagramVersionDao.save(diagramVersion);
		Local.commit();
		return result;
	}
	
	@Override
	public Long updateDiagramVersionDescAndVersionNo(Long domainId,Long id , String versionDesc,String versionNo){
		MessageUtil.checkEmpty(id, "id");
		diagramVersionDao.updateDiagramVersionDescAndVersionNo(domainId,id,versionDesc,versionNo);
		Local.commit();
		return id;
	}

}
