package com.uinnova.product.vmdb.comm.doc.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface ModDesc {

    /**
     * 方法描述
     * 
     * @return
     */
    String desc();

    /**
     * 参数描述
     */
    String pDesc();

    /**
     * 参数类型
     * 
     * @return
     */
    Class<?> pType() default void.class;

    /**
     * 如果参数是泛型, 需指定泛型类型
     * 
     * @return
     */
    Class<?> pcType() default void.class;

    /**
     * 结果描述
     * 
     * @return
     */
    String rDesc();

    /**
     * 结果类型
     * 
     * @return
     */
    Class<?> rType();

    /**
     * 如果结果是泛型, 需指定泛型类型
     * 
     * @return
     */
    Class<?> rcType() default void.class;

}
