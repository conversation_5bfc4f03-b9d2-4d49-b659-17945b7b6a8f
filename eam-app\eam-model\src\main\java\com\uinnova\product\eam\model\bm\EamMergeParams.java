package com.uinnova.product.eam.model.bm;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.base.diagram.model.ESDiagram;
import com.uinnova.product.eam.base.diagram.model.ESDiagramLink;
import com.uinnova.product.eam.base.diagram.model.ESDiagramNode;
import com.uinnova.product.eam.comm.model.es.EamMatrixInstanceData;
import com.uinnova.product.eam.comm.model.es.EamCategory;
import com.uinnova.product.eam.comm.model.es.EamMatrixInstance;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.base.ESCIRltInfo;
import com.uino.bean.cmdb.base.LibType;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 架构资产发布、检出参数传递实体
 * <AUTHOR>
 */
@Data
public class EamMergeParams {
    @Comment("域id")
    private Long domainId;
    @Comment("用户标识")
    private String ownerCode;
    @Comment("模型树id")
    private Long modelId;
    @Comment("备注")
    private String desc;
    @Comment("是否单张视图发布/检出")
    private Boolean singleFlag = true;
    @Comment("私有库、设计库")
    private LibType libType = LibType.PRIVATE;
    @Comment("目录id")
    private List<Long> dirIds;
    @Comment("目录ID ---》 扩展到上级L0层级数据")
    private List<Long> ExpDirIds;
    @Comment("模型资产检出位置")
    private Long targetDirId;
    @Comment("资产库视图id集合")
    private List<String> diagramIds;
    @Comment("父级文件夹信息 发布时代表选中的资产仓库目录，检出时代表本地设计空间的目录")
    private EamCategory parentCategory;
    @Comment("模型树根文件夹信息 发布时：我的资产的模型树根目录，检出：资产仓库模型树根目录")
    private EamCategory rootModelCategory;

    @Comment("与设计库对象对比,ciCode不一致映射集合:<私有库ciCode,设计库ciCode>")
    private Map<String, String> syncCodeMap;
    @Comment("与设计库对象对比,uniqueCode不一致映射集合:<私有库对象uniqueCode,设计库对象uniqueCode>")
    private Map<String, String> syncRltCodeMap;

    @Comment("根据前端传参查询出的 私有库<目录id,目录信息>集合")
    private List<EamCategory> privateCategoryList = new ArrayList<>();
    @Comment("根据前端传参查询出扩展的目录数据 ---》 上级所属目录")
    private List<EamCategory> privateExpCategoryList = new ArrayList<>();
    @Comment("资产库目录信息集合")
    private List<EamCategory> designCategoryList = new ArrayList<>();
    @Comment("目录信息集合")
    private List<EamCategory> mergeCategoryList = new ArrayList<>();

    @Comment("私有库模型树下的所以目录集合")
    private List<EamCategory> privateModelList = new ArrayList<>();
    @Comment("私有库模型树目录idMap")
    private Map<Long, EamCategory> privateIdMap = new HashMap<>(16);
    @Comment("私有库模型树目录codeMap")
    private Map<String, EamCategory> privateCodeMap = new HashMap<>(16);

    @Comment("资产库模型树的所以目录集合")
    private List<EamCategory> designModelList = new ArrayList<>();
    @Comment("资产库模型树目录idMap")
    private Map<Long, EamCategory> designIdMap = new HashMap<>(16);
    @Comment("资产库模型树目录codeMap")
    private Map<String, EamCategory> designCodeMap = new HashMap<>(16);

    @Comment("视图基本信息集合")
    private List<ESDiagram> mergeDiagramList;
    @Comment("视图id映射:<私有库视图id,资产库视图id>,(只筛选目录映射的视图)")
    private Map<String, String> syncDiagramMap = new HashMap<>(16);

    @Comment("视图Node节点信息集合")
    private List<ESDiagramNode> mergeNodeList;
    @Comment("视图Link节点信息集合")
    private List<ESDiagramLink> mergeLinkList;

    @Comment("ci信息集合:发布时存放私有库对象,检出时存放资产库对象")
    private List<ESCIInfo> mergeCiList;
    @Comment("ci信息扩展集合---》上级目录关联ciCode:发布时存放私有库对象,检出时存放资产库对象")
    private List<ESCIInfo> mergeExpCiList;
    @Comment("rlt信息集合:发布时存放私有库关系,检出时存放资产库关系")
    private List<ESCIRltInfo> mergeRltList;

    @Comment("初始化设计库目录数据")
    private List<Long> initDesignCategroyIds = new ArrayList<>();

    /******************************矩阵***********************************/
    @Comment("矩阵实例")
    private EamMatrixInstance matrixInstance;

    @Comment("矩阵表格数据")
    private List<EamMatrixInstanceData> tableData;

}
