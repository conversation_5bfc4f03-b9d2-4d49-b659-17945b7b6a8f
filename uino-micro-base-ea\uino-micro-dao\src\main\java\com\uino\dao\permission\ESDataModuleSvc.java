package com.uino.dao.permission;

import java.util.List;

import jakarta.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import com.uino.dao.AbstractESBaseDao;
import com.uino.dao.ESConst;
import com.uino.util.sys.CommonFileUtil;
import com.uino.bean.permission.base.SysDataModule;
import com.uino.bean.permission.query.CSysDataModule;

/**
 * <b>数据模块
 * 
 * <AUTHOR>
 */
@Service
@RefreshScope
public class ESDataModuleSvc extends AbstractESBaseDao<SysDataModule, CSysDataModule> {

	@Value("${permission.http.prefix:}")
	private String commUrl;

	@Override
	public String getIndex() {
		return ESConst.INDEX_SYS_DATA_MODULE;
	}

	@Override
	public String getType() {
		return ESConst.INDEX_SYS_DATA_MODULE;
	}

	@PostConstruct
	public void init() {
		List<SysDataModule> data = CommonFileUtil.getData("/initdata/uino_sys_data_module.json", SysDataModule.class);
		for (SysDataModule dataModule : data) {
			dataModule.setDataSourceUrl(commUrl + dataModule.getDataSourceUrl());
		}
		super.initIndex(data);
	}

}
