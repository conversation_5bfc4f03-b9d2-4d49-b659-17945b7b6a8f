package com.uinnova.product.eam.web.mix.diagram.v2.web;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 模板相关接口
 */
@RestController
@RequestMapping("/template")
public class TemplateMvc {

    /*@Autowired
    private IEamTemplateDirSvc eamTemplateDirSvc;

    @PostMapping("/convertDiagramAndTemplate")
    @ModDesc(desc = "视图与模板转换", pType = List.class, pcType = Long.class, pDesc = "", rDesc = "", rType = Long.class)
    public RemoteResult convertDiagramAndTemplate(@RequestBody ConvertTemplateBean convertTemplateBean) {

        Integer result = eamTemplateDirSvc.convertDiagramAndTemplate(convertTemplateBean);
        //在进行模板转换时，模板类型被删除，返回报错信息
        if(result != null && result == 1) {
            return new RemoteResult(false, ErrorCode.SERVER_ERROR.getCode(), "模板转换异常，模板分类已被删除", null);
        }

        return new RemoteResult("");
    }

    @PostMapping("/queryTemplateDirs")
    @ModDesc(desc = "查询模板目录信息", pType = List.class, pcType = Long.class, pDesc = "", rDesc = "", rType = Long.class)
    public RemoteResult queryTemplateDir(@RequestParam(defaultValue = "true") Boolean all) {

        return new RemoteResult(eamTemplateDirSvc.queryTemplateDirs(all));
    }

    *//*@PostMapping("/queryDiagramByTemplateDirId")
    @ModDesc(desc = "通过模板目录id查询视图", pType = List.class, pcType = Long.class, pDesc = "", rDesc = "", rType = Long.class)
    public RemoteResult queryDiagramByTemplateDirId(@RequestBody TemplateDirQueryBean queryBean) {

        return new RemoteResult(eamTemplateDirSvc.queryDiagramByTemplateDirId(
                queryBean.getPageNum(), queryBean.getPageSize(), queryBean.getDirId()
        ));
    }

    @PostMapping("/queryTemplateDiagrams")
    @ModDesc(desc = "模糊查询模板视图", pType = List.class, pcType = Long.class, pDesc = "", rDesc = "", rType = Long.class)
    public RemoteResult queryTemplateDiagrams(@RequestBody TemplateDirQueryBean queryBean) {
        return new RemoteResult(eamTemplateDirSvc.queryDiagramTemplateByLike(queryBean));
    }*//*

    @PostMapping("/saveOrUpdateTemplateDirs")
    @ModDesc(desc = "保存模板目录信息", pType = List.class, pcType = Long.class, pDesc = "视图目录信息", rDesc = "", rType = Long.class)
    public RemoteResult saveOrUpdateTemplateDirs(@RequestBody TemplateDir templateDir) {

        return new RemoteResult(eamTemplateDirSvc.saveOrUpdate(templateDir));
    }

    *//*@PostMapping("/deleteTemplateDir")
    @ModDesc(desc = "删除模板目录信息", pType = List.class, pcType = Long.class, pDesc = "视图目录信息", rDesc = "", rType = Long.class)
    public RemoteResult deleteTemplateDir(@RequestParam Long id) {

        eamTemplateDirSvc.removeTemDirById(id);

        return new RemoteResult("");
    }*/
}
