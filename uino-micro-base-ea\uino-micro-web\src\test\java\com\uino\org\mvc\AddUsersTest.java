package com.uino.org.mvc;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;

import org.elasticsearch.index.query.QueryBuilder;
import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import com.alibaba.fastjson.JSON;
import com.binary.framework.web.RemoteResult;
import com.binary.jdbc.Page;
import com.uino.StartBaseWebAppliaction;
import com.uino.dao.permission.ESOrgSvc;
import com.uino.dao.permission.ESUserSvc;
import com.uino.dao.permission.rlt.ESUserOrgRltSvc;
import com.uino.bean.permission.base.SysOrg;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.permission.business.request.AddOrRemoveUserToOrgRequestDto;

import lombok.extern.slf4j.Slf4j;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = { StartBaseWebAppliaction.class }, webEnvironment = WebEnvironment.RANDOM_PORT)
@Slf4j
@ActiveProfiles("provider-local")
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class AddUsersTest {
	@Autowired
	private TestRestTemplate restTemplate;
	@MockBean
	private ESOrgSvc esOrgSvc;
	@MockBean
	private ESUserOrgRltSvc esUserOrgRltSvc;
	@MockBean
	private ESUserSvc esUserSvc;
	private static String testUrl;

	@BeforeClass
	public static void initCls() {
		AddUsersTest.testUrl = "/permission/org/addUsers";
	}

	@Before
	public void start() {
		Mockito.when(esUserOrgRltSvc.deleteByQuery(Mockito.any(QueryBuilder.class), Mockito.anyBoolean()))
				.thenReturn(0);
		Mockito.when(esUserOrgRltSvc.saveOrUpdateBatch(Mockito.anyList())).thenReturn(0);
		Mockito.when(esOrgSvc.getById(Mockito.eq(1L))).thenReturn(SysOrg.builder().id(1L).build());
		Page<SysUser> validPage = new Page<>(1, 1, 1, 1, new ArrayList<>());
		Mockito.when(esUserSvc.getListByQuery(Mockito.anyInt(), Mockito.anyInt(), Mockito.any())).thenReturn(validPage);
	}

	@Test
	public void test01() {
		AddOrRemoveUserToOrgRequestDto requestBody = AddOrRemoveUserToOrgRequestDto.builder()
				.userIds(new HashSet<>(Arrays.asList(1L))).build();
		String responseBodyStr = restTemplate.postForObject(testUrl, requestBody, String.class);
		RemoteResult responseBody = JSON.parseObject(responseBodyStr, RemoteResult.class);
		Assert.assertFalse(responseBody.isSuccess());
	}

	@Test
	public void test02() {
		AddOrRemoveUserToOrgRequestDto requestBody = AddOrRemoveUserToOrgRequestDto.builder().orgId(1L).build();
		String responseBodyStr = restTemplate.postForObject(testUrl, requestBody, String.class);
		RemoteResult responseBody = JSON.parseObject(responseBodyStr, RemoteResult.class);
		Assert.assertFalse(responseBody.isSuccess());
	}

	@Test
	public void test03() {
		AddOrRemoveUserToOrgRequestDto requestBody = AddOrRemoveUserToOrgRequestDto.builder()
				.userIds(new HashSet<>(Arrays.asList(1L))).orgId(1L).build();
		String responseBodyStr = restTemplate.postForObject(testUrl, requestBody, String.class);
		RemoteResult responseBody = JSON.parseObject(responseBodyStr, RemoteResult.class);
		Assert.assertTrue(responseBody.isSuccess());
	}
}
