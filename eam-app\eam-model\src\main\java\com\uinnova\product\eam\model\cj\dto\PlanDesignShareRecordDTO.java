package com.uinnova.product.eam.model.cj.dto;

import lombok.Data;

@Data
public class PlanDesignShareRecordDTO {

    private Long id;
    /**
     * 被分享的设计方案
     */
    private Long planDesignId;
    /**
     * 分享人code
     */
    private String ownerLoginCode;
    /**
     * 被分享人code
     */
    private String sharedLoginCode;
    /**
     * 权限: 以枚举类为准com.uinnova.product.cj.enums.PlanSharePermissionEnum)
     */
    private Integer permission;
    /**
     * 分享状态: 以枚举类为准com.uinnova.product.cj.enums.PlanShareEnum
     */
    private Integer status;
    /**
     * 分享的时间
     */
    private Long shareTime;
}
