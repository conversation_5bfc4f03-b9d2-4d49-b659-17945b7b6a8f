package com.uinnova.product.eam.init;

import com.uino.api.client.permission.IOauthApiSvc;
import com.uino.util.cache.CacheKeyPrefix;
import com.uino.util.cache.ICacheService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

/**
 * description
 *
 * <AUTHOR>
 * @since 2022/5/13 10:18
 */
@Component
public class UserOnlineInterceptor implements HandlerInterceptor {

    @Autowired
    ICacheService cacheService;

    @Autowired
    private IOauthApiSvc oauthApiSvc;

    @Value("${uino.token.online.time:30}")
    private Integer sessionOnlineTime;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String tokenStr = extractToken(request);
        if (tokenStr == null) {
            return true;
        }
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        cacheService.setCache(CacheKeyPrefix.UINO_USER_ONLINE_USER_PREFIX + ":" + authentication.getName(), "", sessionOnlineTime * 60 * 1000);
        return true;
    }

    private String extractToken(HttpServletRequest request) {
        String authHeader = request.getHeader("Authorization");
        if (authHeader != null && authHeader.startsWith("Bearer ")) {
            return authHeader.substring(7);
        }
        return null;
    }
}
