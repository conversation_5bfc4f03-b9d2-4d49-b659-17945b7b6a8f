package com.uinnova.product.eam.db.impl;


import com.binary.framework.dao.DaoDefinition;
import com.uinnova.product.eam.base.diagram.model.CVcDiagramVersion;
import com.uinnova.product.eam.base.diagram.model.VcDiagramVersion;

/**
 * 视图设计版本表[VC_DIAGRAM_VERSION]数据访问对象定义实现
 */
public class VcDiagramVersionDaoDefinition implements DaoDefinition<VcDiagramVersion, CVcDiagramVersion> {


	@Override
	public Class<VcDiagramVersion> getEntityClass() {
		return VcDiagramVersion.class;
	}


	@Override
	public Class<CVcDiagramVersion> getConditionClass() {
		return CVcDiagramVersion.class;
	}


	@Override
	public String getTableName() {
		return "VC_DIAGRAM_VERSION";
	}


	@Override
	public boolean hasDataStatusField() {
		return true;
	}


	@Override
	public void setDataStatusValue(VcDiagramVersion record, int status) {
		record.setDataStatus(status);
	}


	@Override
	public void setDataStatusValue(CVcDiagramVersion cdt, int status) {
		cdt.setDataStatus(status);
	}


	@Override
	public void setCreatorValue(VcDiagramVersion record, String creator) {
		record.setCreator(creator);
	}


	@Override
	public void setModifierValue(VcDiagramVersion record, String modifier) {
		record.setModifier(modifier);
	}


}


