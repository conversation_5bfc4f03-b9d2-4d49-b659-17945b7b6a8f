package com.uinnova.product.eam.db.impl;


import com.binary.framework.dao.DaoDefinition;

import com.uinnova.product.eam.comm.model.CVcClassAddAttr;
import com.uinnova.product.eam.comm.model.VcClassAddAttr;


/**
 * 分类附加属性表[VC_CLASS_ADD_ATTR]数据访问对象定义实现
 */
public class VcClassAddAttrDaoDefinition implements DaoDefinition<VcClassAddAttr, CVcClassAddAttr> {


	@Override
	public Class<VcClassAddAttr> getEntityClass() {
		return VcClassAddAttr.class;
	}


	@Override
	public Class<CVcClassAddAttr> getConditionClass() {
		return CVcClassAddAttr.class;
	}


	@Override
	public String getTableName() {
		return "VC_CLASS_ADD_ATTR";
	}


	@Override
	public boolean hasDataStatusField() {
		return true;
	}


	@Override
	public void setDataStatusValue(VcClassAddAttr record, int status) {
		record.setDataStatus(status);
	}


	@Override
	public void setDataStatusValue(CVcClassAddAttr cdt, int status) {
		cdt.setDataStatus(status);
	}


	@Override
	public void setCreatorValue(VcClassAddAttr record, String creator) {
		record.setCreator(creator);
	}


	@Override
	public void setModifierValue(VcClassAddAttr record, String modifier) {
		record.setModifier(modifier);
	}


}


