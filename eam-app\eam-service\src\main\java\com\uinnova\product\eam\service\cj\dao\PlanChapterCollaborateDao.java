package com.uinnova.product.eam.service.cj.dao;

import com.uinnova.product.eam.model.cj.domain.PlanChapterCollaborate;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;

/**
 * @description: 方案章节协作
 * @author: Lc
 * @create: 2022-03-01 18:45
 */
@Component
public class PlanChapterCollaborateDao extends AbstractESBaseDao<PlanChapterCollaborate, PlanChapterCollaborate> {
    @Override
    public String getIndex() {
        return "uino_cj_plan_chapter_collaborate";
    }

    @Override
    public String getType() {
        return getIndex();
    }

    @PostConstruct
    public void init() {
        initIndex();
    }
}
