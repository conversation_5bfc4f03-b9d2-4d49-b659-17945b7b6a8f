package com.uinnova.product.eam.model.dto;

import com.uinnova.product.eam.comm.model.es.EamArtifactElement;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> wangchun<PERSON>i
 * @Date :
 * @Description :
 */
@Data
public class ArtifactElementDto {
    /**
     * <AUTHOR> wcl
     * @Date : 2022/1/10 17:41
     * @param :
     * @description : 添加或修改的信息
     * @Return :
     **/
    List<EamArtifactElement> elements;
    /**
     * <AUTHOR> wcl
     * @Date : 2022/1/10 17:42
     * @param :
     * @description : 要删除的分栏id
     * @Return :
     **/
    List<Long> deleteId;
}
