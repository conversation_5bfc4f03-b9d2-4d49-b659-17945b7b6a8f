package com.uino.web.permission.mvc;

import java.util.List;
import java.util.Map;
import java.util.Set;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;

import com.binary.framework.web.RemoteResult;
import com.uino.bean.permission.base.SysUser;
import com.uino.init.api.ApiResult;
import com.uino.init.api.ApiVersion;
import com.uino.util.sys.SysUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uino.bean.permission.base.SysModule;
import com.uino.bean.permission.business.ModuleNodeInfo;
import com.uino.bean.permission.query.CSysModule;
import com.uino.api.client.permission.IModuleApiSvc;
import org.springframework.web.multipart.MultipartFile;

@ApiVersion(1)
@Api(value = "系统菜单", tags = {"系统菜单"})
@RestController
@RequestMapping(value = "/permission/module")
public class ModuleMvc {
    @Autowired
    private IModuleApiSvc moduleApiSvc;

    @ApiOperation("获取菜单树结构")
    @GetMapping("getModuleTree")
    @ModDesc(desc = "获取菜单树结构", pDesc = "用户id", pType = Long.class, rDesc = "菜单树", rType = ModuleNodeInfo.class)
    public ApiResult<ModuleNodeInfo> getModuleTree(@RequestParam(required = false) Long userId, HttpServletRequest request,
                                                   HttpServletResponse response) {
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        ModuleNodeInfo tree = moduleApiSvc.getModuleTree(currentUserInfo.getDomainId(), userId);
        //ControllerUtils.returnJson(request, response, tree);
        return ApiResult.ok(this).data(tree);
    }
    @ApiOperation("根据权限标识获取下级菜单树结构")
    @GetMapping("getModuleTreeBySign")
    @ModDesc(desc = "根据权限标识获取下级菜单树结构", pDesc = "用户id", pType = Long.class, rDesc = "菜单树", rType = ModuleNodeInfo.class)
    public ApiResult<ModuleNodeInfo> getModuleTreeBySign(@RequestParam(required = false) Long userId,@RequestParam String moduleSign) {
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        ModuleNodeInfo tree = moduleApiSvc.getModuleTreeBySign(currentUserInfo.getDomainId(), userId,moduleSign);
        return ApiResult.ok(this).data(tree);
    }


    @ApiOperation("保存系统模块")
    @PostMapping("saveModule")
    @ModDesc(desc = "保存系统模块", pDesc = "系统模块对象", pType = SysModule.class, rDesc = "系统模块数据", rType = SysModule.class)
    public ApiResult<SysModule> saveModule(@RequestBody SysModule saveDto, HttpServletRequest request, HttpServletResponse response) {
        saveDto = saveDto == null ? new SysModule() : saveDto;
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        saveDto.setDomainId(currentUserInfo.getDomainId());
        SysModule res = moduleApiSvc.saveModule(saveDto);
        return ApiResult.ok(this).data(res);
    }

    @ApiOperation("删除系统模块")
    @PostMapping("delModule")
    @ModDesc(desc = "删除系统模块", pDesc = "模块id", pType = Long.class, rDesc = "操作是否成功", rType = Boolean.class)
    public ApiResult<Boolean> delModule(@RequestBody Long moduleId, HttpServletRequest request, HttpServletResponse response) {
        moduleApiSvc.delModule(moduleId);
        return ApiResult.ok(this).data(true);
    }

    @ApiOperation("保存系统模块顺序")
    @PostMapping("saveOrder")
    @ModDesc(desc = "保存系统模块顺序", pDesc = "系统模块排序集合", pType = Map.class, rDesc = "操作是否成功", rType = Boolean.class)
    public ApiResult<Boolean> saveOrder(@RequestBody Map<Long, Integer> orderDict, HttpServletRequest request,
                                        HttpServletResponse response) {
        moduleApiSvc.saveOrder(orderDict);
        return ApiResult.ok(this).data(true);
    }

    @ApiOperation("系统模块还原")
    @PostMapping("recoverModules")
    @ModDesc(desc = "系统模块还原", pDesc = "模块id集合", pType = Set.class, pcType = Long.class, rDesc = "受影响的模块集合", rType = Object.class)
    public ApiResult<Object> recoverModules(@RequestBody(required = false) Set<Long> moduleIds, HttpServletRequest request,
                                            HttpServletResponse response) {
        Object res = moduleApiSvc.recoverModules(moduleIds);
        return ApiResult.ok(this).data(res);
    }

    @ApiOperation("获取系统模块配置信息")
    @RequestMapping(value = "getModules", method = RequestMethod.POST)
    @ModDesc(desc = "获取系统模块配置信息", pDesc = "查询条件", pcType = CSysModule.class, rDesc = "系统模块配置", rType = List.class, rcType = SysModule.class)
    public ApiResult<List<SysModule>> getModuleConfig(HttpServletRequest request, HttpServletResponse response, @RequestBody CSysModule cdt) {
        cdt = cdt == null ? new CSysModule() : cdt;
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        if (cdt.getDomainId() == null) {
            cdt.setDomainId(currentUserInfo.getDomainId());
        }
        return ApiResult.ok(this).data(moduleApiSvc.getModulesByCdt(cdt));
    }

    @ApiOperation("导出菜单配置")
    @GetMapping("exportModules")
    public ResponseEntity<byte[]> exportModules() {
        return moduleApiSvc.exportModules();
    }

    @ApiOperation("导入菜单配置")
    @PostMapping("importModules")
    public RemoteResult importModules(@RequestParam("file") @NotNull MultipartFile file) {
        moduleApiSvc.importModules(file);
        return new RemoteResult("success");
    }

}
