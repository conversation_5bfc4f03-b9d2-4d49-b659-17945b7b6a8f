package com.uinnova.product.eam.service.cj.service;

import com.uinnova.product.eam.model.cj.domain.PlanTemplateChapter;
import com.uinnova.product.eam.model.cj.domain.PlanTemplateChapterData;
import com.uinnova.product.eam.model.cj.vo.PlanTemplateChapterDataVo;
import com.uinnova.product.eam.model.cj.vo.PlanTemplateChapterTreeVo;
import com.uinnova.product.eam.model.cj.vo.PlanTemplateChapterVo;
import com.uinnova.product.eam.model.cj.vo.ResetSortVo;
import com.uino.bean.permission.base.SysUser;

import java.util.List;

public interface PlanTemplateChapterService {

    /**
     * 新增方案模板章节
     * @param planTemplateChapterVo
     * @return
     */
    Long addPlanTemplateChapter(PlanTemplateChapterVo planTemplateChapterVo, SysUser user);

    /**
     * 方案模板章节列表
     * @return
     */
    List<PlanTemplateChapterTreeVo> findPlanTemplateChapterList(PlanTemplateChapter templateChapter);

    /**
     * 获取模板章节详情
     * @param id
     */
    PlanTemplateChapterDataVo getPlanTemplateChapter(Long id);

    /**
     * 删除模板章节
     * @param templateChapter
     * @return
     */
    boolean deletePlanTemplateChapter(PlanTemplateChapter templateChapter);

    /**
     * 复制方案模板章节
     * @param chapterId
     * @return
     */
    Long copyPlanTemplateChapter(Long chapterId, String chapterName, SysUser user);

    /**
     * 章节重排序
     * @param resetSortVo
     * @return
     */
    boolean resetSort(ResetSortVo resetSortVo);

    /**
     * 删除模板章节数据
     * @param id
     * @return
     */
    boolean deletePlanTemplateChapterData(Long id);

    /**
     * 获取章节备注信息
     * @param chapterId
     * @return
     */
    PlanTemplateChapter getRemark(Long chapterId);

    /**
     * 模板章节数据
     * @param planChapterContextId
     * @return
     */
    PlanTemplateChapterData getTemplateChapterData(Long planChapterContextId);

    /**
     * 校验制品是否所属制品分类
     * @param viewType
     * @return
     */
    boolean checkViewType(String viewType);

    /**
     * 通过方案模板id获取方案模板章节列表
     * @param planTemplateId
     * @return
     */
    List<PlanTemplateChapter> findPlanChapterList(Long planTemplateId);

    /**
     * 通过模板章节id获取数据
     * @param templateChapterId
     * @return
     */
    List<PlanTemplateChapterData> findChapterDataList(Long templateChapterId);

    /**
     * 获取层级下模板章节列表
     * @param templateParentId
     * @param level
     * @return
     */
    List<PlanTemplateChapter> findLevelTemplateChapterList(Long planTemplateId, Long templateParentId, Integer level);

    /**
     * 刷制品全部类型数据
     * @return
     */
    Boolean refreshTemplateData();
}
