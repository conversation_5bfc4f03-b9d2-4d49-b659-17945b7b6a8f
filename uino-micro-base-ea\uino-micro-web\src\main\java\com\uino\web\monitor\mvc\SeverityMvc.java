package com.uino.web.monitor.mvc;

import java.io.File;
import java.util.List;
import java.util.Set;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import com.uino.bean.permission.base.SysUser;
import com.uino.util.sys.SysUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.binary.framework.util.ControllerUtils;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uino.bean.cmdb.business.ImportResultMessage;
import com.uino.bean.monitor.base.ESMonSysSeverityInfo;
import com.uino.bean.sys.query.ExportDictionaryDto;
import com.uino.api.client.monitor.IMonSeverityApiSvc;

/**
 * 事件定义相关控制层
 * 
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping(value = "/monitor/severity")
public class SeverityMvc {

    @Autowired
    private IMonSeverityApiSvc monSeverityApiSvc;

    /**
     * 查询事件级别定义
     * 
     * @param searchVal
     * @param request
     * @param response
     */
    @PostMapping("querySeverityList")
    @ModDesc(desc = "查询事件级别定义列表", pDesc = "查询条件", pType = String.class, rDesc = "事件级别定义列表", rType = List.class, rcType = ESMonSysSeverityInfo.class)
    public void querySeverityList(@RequestBody(required = false) String searchVal, HttpServletRequest request,
            HttpServletResponse response) {
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        List<ESMonSysSeverityInfo> result = monSeverityApiSvc.querySeverityList(currentUserInfo.getDomainId(),searchVal);
        ControllerUtils.returnJson(request, response, result);
    }

    /**
     * 持久化事件级别定义
     * 
     * @param saveDto
     * @param request
     * @param response
     */
    @PostMapping("saveOrUpdateSeverity")
    @ModDesc(desc = "保存或更新事件级别定义", pDesc = "事件级别定义数据对象", pType = ESMonSysSeverityInfo.class, rDesc = "事件定义id", rType = Long.class)
    public void saveOrUpdateSeverity(@RequestBody ESMonSysSeverityInfo saveDto, HttpServletRequest request,
            HttpServletResponse response) {
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        saveDto.setDomainId(currentUserInfo.getDomainId());
        Long result = monSeverityApiSvc.saveOrUpdateSeverity(saveDto);
        ControllerUtils.returnJson(request, response, result);
    }

    /**
     * 根据事件级别定义ids删除事件定义
     * 
     * @param delIds
     * @param request
     * @param response
     */
    @PostMapping("deleteServrityByIds")
    @ModDesc(desc = "跟据id批量删除事件级别定义", pDesc = "事件定义id集合", pType = Set.class, pcType = Long.class, rDesc = "操作是否成功", rType = Boolean.class)
    public void deleteServrityByIds(@RequestBody Set<Long> delIds, HttpServletRequest request,
            HttpServletResponse response) {
        monSeverityApiSvc.deleteServrityByIds(delIds);
        ControllerUtils.returnJson(request, response, true);
    }

    @RequestMapping("/exportSeverityInfos")
    @ModDesc(desc = "导出告警级别定义", pDesc = "是否导出模板", pcType = Long.class, rDesc = "导出Excel文件", rType = File.class)
    public void exportSeverityInfos(HttpServletRequest request, HttpServletResponse response, @RequestBody ExportDictionaryDto dto) {
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        ControllerUtils.returnResource(request, response, monSeverityApiSvc.exportServrityInfos(currentUserInfo.getDomainId(),dto.getIsTpl()));
    }

    @PostMapping("/importSeverityInfos")
    @ModDesc(desc = "导入告警级别定义", pDesc = "导入文件", rDesc = "导入明细", rType = ImportResultMessage.class)
    public void importSeverityInfos(HttpServletRequest request, HttpServletResponse response, @RequestParam("file") MultipartFile file) {
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        ControllerUtils.returnJson(request, response, monSeverityApiSvc.importSeverityInfos(currentUserInfo.getDomainId(), file));
    }
}
