package com.uino.api.client.sys.rpc;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.binary.core.io.Resource;
import com.uino.provider.feign.sys.SysFeign;
import com.uino.api.client.sys.ISysApiSvc;

@Service
public class SysApiSvcRpc implements ISysApiSvc {

    @Autowired
    private SysFeign feign;

    @Override
    public String uploadFile(MultipartFile file) {
        // TODO Auto-generated method stub
        return feign.uploadFile(file);
    }

    @Override
    public Resource downloadFile(String filePath) {
        return feign.downloadFile(filePath);
    }

    @Override
    public String uploadFile(byte[] fileBytes, String fileName) {
        // TODO Auto-generated method stub
        return feign.uploadFile(fileBytes, fileName);
    }
}
