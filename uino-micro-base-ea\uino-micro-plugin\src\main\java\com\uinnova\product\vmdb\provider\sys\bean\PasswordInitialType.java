package com.uinnova.product.vmdb.provider.sys.bean;

public enum PasswordInitialType {

	RANDOM(0),
	FIXED(1),
	CUSTOM(2);
	
	private Integer code;

    private PasswordInitialType(Integer code) {
        this.code = code;
    }

    public Integer getType() {
        return code;
    }

    public static PasswordInitialType valueOf(Integer code) {
        if (RANDOM.code.equals(code)) {
            return RANDOM;
        } else if (FIXED.code.equals(code)) {
            return FIXED;
        } else if (CUSTOM.code.equals(code)) {
            return CUSTOM;
        } else {
            return null;
        }
    }
}
