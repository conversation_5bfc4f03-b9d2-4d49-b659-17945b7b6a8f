package com.uinnova.product.vmdb.comm.bean;

import com.binary.framework.bean.annotation.Comment;

import java.io.Serializable;
import java.util.List;

/**
 * 
 * <AUTHOR>
 *
 */
@Comment("树状菜单列表")
public class MenuTreeNode implements Serializable {

    private static final long serialVersionUID = 1L;

    @Comment("ID")
    private String id;

    @Comment("菜单ID")
    private Long menuId;

    @Comment("模块Id")
    private Long moduId;

    @Comment("菜单显示名")
    private String label;

    @Comment("菜单原名称")
    private String menuName;

    @Comment("模块的Url")
    private String moduUrl;

    private String url;

    @Comment("模块的Code")
    private String code;

    @Comment("图标的CSS样")
    private String iconClassName;

    @Comment("是否收藏,true 收藏,false 未收藏")
    private String isFavorite;

    @Comment("是否展开,1 展开,0 收起")
    private Integer isOpen;

    @Comment("子菜单")
    private List<MenuTreeNode> children;

    private List<String> menuNames;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Long getMenuId() {
        return menuId;
    }

    public void setMenuId(Long menuId) {
        this.menuId = menuId;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public Long getModuId() {
        return moduId;
    }

    public void setModuId(Long moduId) {
        this.moduId = moduId;
    }

    public String getMenuName() {
        return menuName;
    }

    public void setMenuName(String menuName) {
        this.menuName = menuName;
    }

    public String getModuUrl() {
        return moduUrl;
    }

    public void setModuUrl(String moduUrl) {
        this.moduUrl = moduUrl;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getIconClassName() {
        return iconClassName;
    }

    public void setIconClassName(String iconClassName) {
        this.iconClassName = iconClassName;
    }

    public String getIsFavorite() {
        return isFavorite;
    }

    public void setIsFavorite(String isFavorite) {
        this.isFavorite = isFavorite;
    }

    public List<MenuTreeNode> getChildren() {
        return children;
    }

    public void setChildren(List<MenuTreeNode> children) {
        this.children = children;
    }

    public void setMenuNames(List<String> menuNames) {
        this.menuNames = menuNames;
    }

    public List<String> getMenuNames() {
        return menuNames;
    }

    public Integer getIsOpen() {
        return isOpen;
    }

    public void setIsOpen(Integer isOpen) {
        this.isOpen = isOpen;
    }

}
