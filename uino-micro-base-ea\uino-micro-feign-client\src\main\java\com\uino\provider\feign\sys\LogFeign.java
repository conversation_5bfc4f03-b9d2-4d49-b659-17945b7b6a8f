package com.uino.provider.feign.sys;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.binary.jdbc.Page;
import com.uino.bean.sys.base.SysLoginLog;
import com.uino.bean.sys.business.QueryLoginLogRequestDto;
import com.uino.provider.feign.config.BaseFeignConfig;

@Component
@FeignClient(name = "${feign-server-name:tarsier-platform}", path = "${feign-server-path:tarsier-platform}/feign/sys/log", configuration = {
        BaseFeignConfig.class})
public interface LogFeign {

    /**
     * 增加一条登陆日志
     * 
     * @param userId
     * @return
     */
    @PostMapping("addLoginLog")
    public SysLoginLog addLoginLog(@RequestParam(value = "id", required = false) Long userId);

    /**
     * 增加一条登陆日志
     * 
     * @param userCode
     * @return
     */
    @PostMapping("addLoginLogByCode")
    public SysLoginLog addLoginLog(@RequestParam(value = "domainId") Long domainId, @RequestParam(value = "id", required = false) String userCode);

    /**
     * 查询登陆日志
     * 
     * @param query
     * @return
     */
    @PostMapping("queryLoginLog")
    public Page<SysLoginLog> queryLoginLog(@RequestBody(required = false) QueryLoginLogRequestDto query);
}
