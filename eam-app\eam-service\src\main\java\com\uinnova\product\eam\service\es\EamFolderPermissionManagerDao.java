package com.uinnova.product.eam.service.es;

import com.uinnova.product.eam.comm.model.es.FolderPermissionManager;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;

/**
 * 文件夹权限关系Dao
 *
 * <AUTHOR>
 * @since 2022/6/30 10:26
 */
@Service
public class EamFolderPermissionManagerDao extends AbstractESBaseDao<FolderPermissionManager,FolderPermissionManager> {
    @Override
    public String getIndex() {
        return "uino_eam_folder_permission_manager";
    }

    @Override
    public String getType() {
        return "uino_eam_folder_permission_manager";
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }
}
