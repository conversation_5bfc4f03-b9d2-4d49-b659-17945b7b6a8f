package com.uino.web.sys.mvc;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.util.Collections;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.core.env.Environment;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSONObject;
import com.binary.core.io.Resource;
import com.binary.core.util.BinaryUtils;
import com.binary.framework.util.ControllerUtils;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uino.util.sys.CommonFileUtil;
import com.uino.util.sys.SysUtil;
import com.uino.bean.sys.base.Logo;
import com.uino.bean.sys.business.SysUpdateLogDto;
import com.uino.bean.sys.business.SysUpdateLogDto.Info;
import com.uino.bean.sys.business.SysUpdateLogTree;
import com.uino.init.api.ApiResult;
import com.uino.init.api.ApiVersion;
import com.uino.api.client.sys.ILogoApiSvc;
import com.uino.api.client.sys.ISysApiSvc;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@ApiVersion(1)
@Api(value = "系统数据", tags = {"系统数据"})
@RequestMapping("/sys")
@RestController
@RefreshScope
public class SystemMvc {

    @Autowired
    private ILogoApiSvc logoApi;

    private List<SysUpdateLogTree> updateLogTreeCache;

    @Value("${permission.http.prefix:}")
    private String serverRootUrl;

    @Autowired
    private ISysApiSvc sysApi;

    @Autowired
    private Environment environment;

    @ApiOperation("上传文件")
    @PostMapping("uploadFile")
    @ModDesc(desc = "上传文件", pDesc = "待上传文件", rDesc = "文件地址", rType = String.class)
    public ApiResult<String> uploadFile(@RequestParam(name = "file", required = true) MultipartFile file, HttpServletRequest request,
            HttpServletResponse response) {
        String path = sysApi.uploadFile(file);
        return ApiResult.ok(this).data(path);
    }

//    @ApiOperation("下载文件")
//    @PostMapping("downloadFile")
//    @ModDesc(desc = "下载文件", pDesc = "文件地址", rDesc = "文件", rType = String.class)
//    public void downloadFile(HttpServletRequest request,
//                           HttpServletResponse response,@RequestBody JSONObject body) {
//        String filePath = body.getString("filePath");
//        Resource resource = sysApi.downloadFile(filePath);
//        ControllerUtils.returnResource(request, response, resource);
//    }

    @PostMapping("getConfig")
	@ApiOperation(value="获取配置信息")
    @ModDesc(desc = "获取配置信息", pDesc = "配置名", rDesc = "系统Logo", rType = Object.class)
    public ApiResult<Object> getConfig(@RequestBody String configName, HttpServletRequest request, HttpServletResponse response)
            throws UnsupportedEncodingException {
		Object val = null;
		// 优先从json配置文件中取
		List<Map> data = CommonFileUtil.getData("/initdata/uino_sys_config.json", Map.class);
		if (!BinaryUtils.isEmpty(data)) {
			Map config = data.get(0);
			val = config.get(configName);
		}
		if (BinaryUtils.isEmpty(val)) {
			val = environment.getProperty(configName);
			if (val != null) {
				val = new String(val.toString().getBytes(), "UTF-8");
			} else {
				if ("uino.base.ci.primarykey.maxcount".equals(configName)) {
					val = "5";
				}
			}
		}
		if ("uino.permission.user.initPassword".equals(configName) && !BinaryUtils.isEmpty(val)) {
			val = SysUtil.EncryptDES.encryptDES(String.valueOf(val));
		}
		return ApiResult.ok(this).data(val);
    }

    @ApiOperation("获取系统Logo")
    @PostMapping("getLogos")
    @ModDesc(desc = "获取系统Logo", pDesc = "无", rDesc = "系统Logo", rType = Map.class)
    public ApiResult<Map<String, Logo>> getLogos(HttpServletRequest request, HttpServletResponse response) {
        Map<String, Logo> res = logoApi.getLogos();
        return ApiResult.ok(this).data(res);
    }

    @ApiOperation("更新系统Logo")
    @PostMapping("updateLogo")
    @ModDesc(desc = "更新系统Logo", pDesc = "Logo文件及类型", pType = File.class, rDesc = "Logo数据", rType = Logo.class)
    public ApiResult<Logo> updateLogo(@RequestParam(name = "file", required = false) MultipartFile file,
            @RequestParam(name = "logoType") String logoType, HttpServletRequest request,
            HttpServletResponse response) {
        Map<String, Logo> res = logoApi.updateLogo(logoType, file);
        return ApiResult.ok(this).data(res.get(logoType));
    }

    @ApiOperation("通过图片地址更新系统Logo")
    @PostMapping("updateLogoByPath")
    @ModDesc(desc = "通过图片地址更新系统Logo", pDesc = "图片地址及类型", pType = File.class, rDesc = "Logo数据", rType = Logo.class)
    public ApiResult<Logo> updateLogoByPath(@RequestBody JSONObject json) {
        String logoType = json.getString("logoType");
        String path = json.getString("path");
        Long fileId = json.getLong("fileId");
        Map<String, Logo> res = logoApi.updateLogoByPath(logoType, path, fileId);
        return ApiResult.ok(this).data(res.get(logoType));
    }

    @ApiOperation("删除系统Logo")
    @RequestMapping(value="deleteLogo",method = RequestMethod.POST)
    @ModDesc(desc = "删除系统Logo", pDesc = "Logo文件及类型", pType = String.class, rDesc = "Logo数据", rType = Logo.class)
    public ApiResult<Map<String, Logo>> updateLogo(@RequestParam(name = "logoType") String logoType, HttpServletRequest request,
            HttpServletResponse response) {
        Map<String, Logo> res = logoApi.deleteLogo(logoType);
        return ApiResult.ok(this).data(res);
    }

    @ApiOperation("获取系统更新日志")
    @RequestMapping(value="getSysUpdateLog",method = RequestMethod.POST)
    @ModDesc(desc = "获取系统更新日志", pDesc = "无", rDesc = "更新日志列表", rType = List.class, rcType = SysUpdateLogTree.class)
    public ApiResult<List<SysUpdateLogTree>> getSysUpdateLog(HttpServletRequest request, HttpServletResponse response) {
        List<SysUpdateLogTree> updateLogTree = null;
        if (this.updateLogTreeCache == null) {
            synchronized (this.getClass()) {
                if (this.updateLogTreeCache == null) {
                    this.updateLogTreeCache = buildUpdateLogTree();
                }
            }
        }
        updateLogTree = this.updateLogTreeCache;
        return ApiResult.ok(this).data(updateLogTree);
    }

    @ApiOperation("获取系统配置信息")
    @RequestMapping(value="getSysConfig",method = RequestMethod.POST)
    @ModDesc(desc = "获取系统配置信息", pDesc = "无", rDesc = "系统配置", rType = Map.class)
    public ApiResult<Map<String, String>> getSysConfig(HttpServletRequest request, HttpServletResponse response) {
        Map<String, String> configMap = new LinkedHashMap<>();
        configMap.put("serverRootPath", serverRootUrl);
        return ApiResult.ok(this).data(configMap);
    }

    private List<SysUpdateLogTree> buildUpdateLogTree() {
        File updateLogsDir = new File(this.getClass().getResource("/sys_updat_logs").getPath());
        File[] classDirs = updateLogsDir.listFiles();
        List<SysUpdateLogTree> firstLevel = new LinkedList<>();
        /**
         * 文件夹名
         * <p>
         * 一级 1_架构可视化管理
         * <P>
         * 二级 V5.11.0_2019-11-14
         */
        for (File classDir : classDirs) {
            String dirName = classDir.getName();
            int procuctOrder = Integer.valueOf(dirName.split("_")[0]);
            String procuctName = dirName.split("_")[1];
            SysUpdateLogTree productNode = SysUpdateLogTree.builder().title(procuctName).orderNo(procuctOrder).build();
            firstLevel.add(productNode);
            // 读取该文件夹下文件解析第二层
            for (File procuctVersionFile : classDir.listFiles()) {
                String fileName = procuctVersionFile.getName();
                String version = fileName.split("_")[0];
                String updateTimeStr = fileName.split("_")[1];
                Long updateTime = Long.valueOf(updateTimeStr.replaceAll("-", ""));
                SysUpdateLogTree versionNode = SysUpdateLogTree.builder().title(fileName).version(version)
                        .updateTimeStr(updateTimeStr).updateTime(updateTime).build();
                this.resolverUpdateFile(procuctVersionFile, versionNode);
                productNode.getChildren().add(versionNode);
            }
        }
        // 对产品排序(第一层排序),按排序字段排升序
        Collections.sort(firstLevel, new Comparator<SysUpdateLogTree>() {

            @Override
            public int compare(SysUpdateLogTree o1, SysUpdateLogTree o2) {
                if (o1.getOrderNo() < o2.getOrderNo())
                    return -1;
                else if (o1.getOrderNo() == o2.getOrderNo())
                    return 0;
                else
                    return 1;
            }
        });
        // 对版本层排序(第二层排序)，按修改时间排倒叙
        firstLevel.forEach(versionNode -> {
            Collections.sort(versionNode.getChildren(), new Comparator<SysUpdateLogTree>() {

                @Override
                public int compare(SysUpdateLogTree o1, SysUpdateLogTree o2) {
                    if (o1.getUpdateTime() > o2.getUpdateTime())
                        return -1;
                    else if (o1.getUpdateTime() == o2.getUpdateTime())
                        return 0;
                    else
                        return 1;
                }
            });
        });
        return firstLevel;
    }

    /**
     * 解析修改记录文件
     *
     * @param file
     * @param node
     */
    private void resolverUpdateFile(File file, SysUpdateLogTree node) {
        List<String> lines = readFileLines(file);
        SysUpdateLogDto currentNode = null;
        for (String content : lines) {
            content = content.trim();
            if (content.startsWith("[")) {
                String typeStr = content.substring(content.indexOf("[") + 1, content.indexOf("]"));
                Integer type = null;
                if ("新增".equals(typeStr))
                    type = 0;
                else if ("修复".equals(typeStr)) {
                    type = 1;
                } else if ("优化".equals(typeStr)) {
                    type = 2;
                } else if ("移除".equals(typeStr)) {
                    type = 3;
                }
                currentNode.getInfos()
                        .add(Info.builder().type(type).content(content.substring(content.indexOf("]") + 1)).build());
            } else {
                SysUpdateLogDto record = SysUpdateLogDto.builder().className(content).build();
                node.getRecords().add(record);
                currentNode = record;
            }
        }
    }

    private List<String> readFileLines(File file) {
        List<String> lines = new LinkedList<>();
        try {
            InputStreamReader input = new InputStreamReader(new FileInputStream(file));
            BufferedReader bf = new BufferedReader(input);
            // 按行读取字符串
            String str;
            while ((str = bf.readLine()) != null) {
                if (str != null && !"".equals(str.trim()))
                    lines.add(str);
            }
            bf.close();
            input.close();
        } catch (Exception e) {
        }
        return lines;
    }
}
