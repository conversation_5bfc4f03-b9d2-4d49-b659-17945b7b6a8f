package com.uinnova.product.eam.web.bm.peer;

import com.uinnova.product.eam.model.CiQueryCdtExtend;
import com.uinnova.product.eam.model.VcCiClassInfoDto;
import com.uinnova.product.eam.model.vo.EamAnalyseCiVo;
import com.uinnova.product.eam.model.dto.AnalysePathDto;
import com.uinnova.product.eam.model.dto.DataAnalyzeBatch;
import com.uinnova.product.eam.model.enums.AnalyseLeaf;
import com.uinnova.product.eam.model.vo.EamAnalyseTableVo;
import com.uinnova.product.eam.service.IBmDataAnalyzeSvc;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 数据分析
 *
 * <AUTHOR>
 */
@Service
public class BmDataAnalyzePeer {
    @Resource
    private IBmDataAnalyzeSvc bmDataAnalyzeSvc;

    public List<VcCiClassInfoDto> queryClassInfoByMetaModel(CiQueryCdtExtend cdt) {
        return bmDataAnalyzeSvc.queryClassInfoByMetaModel(cdt);
    }

    public Map<String, Object> queryByCiCode(String ciCode, Integer position) {
        return bmDataAnalyzeSvc.queryByCiCode(ciCode,position);
    }

    public AnalysePathDto queryPath(String ciCode) {
        return bmDataAnalyzeSvc.queryPath(ciCode);
    }

    public List<DataAnalyzeBatch> queryByPath(String ciCode, AnalyseLeaf leaf, List<String> path) {
        return bmDataAnalyzeSvc.queryByPath(ciCode, leaf, path);
    }

    public List<EamAnalyseCiVo> queryByCiCodes(List<String> ciCodes) {
        return bmDataAnalyzeSvc.queryByCiCodes(ciCodes);
    }

    public List<DataAnalyzeBatch>  queryPathOneSide(String ciCode,AnalyseLeaf leaf, String rootCiCode) {
        return bmDataAnalyzeSvc.queryPathOneSide(ciCode,leaf,rootCiCode);
    }

    public ResponseEntity<byte[]> export(String root, List<String> ciCodeList, List<String> rltCodeList) {
        return bmDataAnalyzeSvc.export(root, ciCodeList, rltCodeList);
    }

    public List<EamAnalyseTableVo> getTable(String root, List<String> ciCodeList, List<String> rltCodeList) {
        return bmDataAnalyzeSvc.getTable(root, ciCodeList, rltCodeList);
    }
}
