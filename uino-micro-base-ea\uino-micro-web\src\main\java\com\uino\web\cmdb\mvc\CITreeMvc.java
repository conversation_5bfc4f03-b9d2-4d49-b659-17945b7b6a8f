package com.uino.web.cmdb.mvc;

import java.util.List;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import com.uino.bean.permission.base.SysUser;
import com.uino.init.api.ApiResult;
import com.uino.init.api.ApiVersion;
import com.uino.util.sys.SysUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uino.bean.cmdb.base.ESCITreeConfigInfo;
import com.uino.bean.cmdb.business.CITreeNode;
import com.uino.api.client.cmdb.ICITreeApiSvc;

@ApiVersion(1)
@Api(value = "ci Tree配置", tags = {"对象管理"})
@RestController
@RequestMapping("/cmdb/ciTree")
public class CITreeMvc {
	@Autowired
	private ICITreeApiSvc ciTreeApiSvc;

	/**
	 * 获取ci tree配置
	 * 
	 * @return
	 */
	@ApiOperation(value="获取树状图配置列表")
	@RequestMapping(value = "getCITreeConfigs",method= RequestMethod.POST)
    @ModDesc(desc = "获取树状图配置列表", pDesc = "无", rDesc = "树状图配置列表", rType = List.class, rcType = ESCITreeConfigInfo.class)
	public ApiResult<List<ESCITreeConfigInfo>> getCITreeConfigs(HttpServletRequest request, HttpServletResponse response) {
		SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
		return ApiResult.ok(this).data(ciTreeApiSvc.getCITreeConfigs(currentUserInfo.getDomainId()));
	}

	/**
	 * 获取ci tree配置by id
	 * 
	 * @return
	 */
	@ApiOperation(value="根据id获取树状图配置")
	@RequestMapping(value="getCITreeConfig",method=RequestMethod.POST)
    @ModDesc(desc = "根据id获取树状图配置", pDesc = "配置id", pType = Long.class, rDesc = "树状图配置数据", rType = ESCITreeConfigInfo.class)
	public ApiResult<ESCITreeConfigInfo> getCITreeConfig(@RequestBody Long id, HttpServletRequest request, HttpServletResponse response) {
		return ApiResult.ok(this).data(ciTreeApiSvc.getCITreeConfig(id));
	}

	/**
	 * 保存ci tree配置
	 * 
	 * @return
	 */
	@ApiOperation(value="保存树状图配置")
	@RequestMapping(value="save",method=RequestMethod.POST)
    @ModDesc(desc = "保存树状图配置", pDesc = "树状图配置对象", pType = ESCITreeConfigInfo.class, rDesc = "保存后的配置详情", rType = ESCITreeConfigInfo.class)
	public ApiResult<ESCITreeConfigInfo> save(@RequestBody ESCITreeConfigInfo saveInfo, HttpServletRequest request,
			HttpServletResponse response) {
		SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
		saveInfo.setDomainId(currentUserInfo.getDomainId());
		return ApiResult.ok(this).data(ciTreeApiSvc.save(saveInfo));
	}

	/**
	 * 删除ci tree配置
	 * 
	 * @param id
	 * @return
	 */
	@ApiOperation(value="根据id删除树状图配置")
	@RequestMapping(value="delete",method = RequestMethod.POST)
    @ModDesc(desc = "根据id删除树状图配置", pDesc = "配置id", pType = Long.class, rDesc = "树状图配置列表", rType = List.class, rcType = ESCITreeConfigInfo.class)
	public ApiResult<List<ESCITreeConfigInfo>> delete(@RequestBody Long id, HttpServletRequest request, HttpServletResponse response) {
		SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
		return ApiResult.ok(this).data(ciTreeApiSvc.delete(currentUserInfo.getDomainId(),id));
	}

	/**
	 * 获取ci树
	 * 
	 * @param config
	 *            ciTree配置信息
	 * @param hasNullNode
	 *            返回信息是否包含空节点true:包含 false:不包含
	 * @param returnAllCI
	 *            是否返回所有ci true:返回所有匹配上的ci false:每个节点最多只挂载60条ci，其余的自己条件查询翻页
	 * @return 符合条件的ci树
	 */
	@ApiOperation(value="树状图配置-数据预览")
	@RequestMapping(value="getCITree",method=RequestMethod.POST)
    @ModDesc(desc = "树状图配置-数据预览", pDesc = "配置信息及hasNullNode:是否包含空节点", pType = ESCITreeConfigInfo.class, rDesc = "对象数据-树结构", rType = CITreeNode.class)
	public ApiResult<CITreeNode> getCITree(@RequestBody ESCITreeConfigInfo config, @RequestParam(required = false) boolean hasNullNode,
			@RequestParam(required = false) boolean returnAllCI, HttpServletRequest request,
			HttpServletResponse response) {
		SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
		config.setDomainId(currentUserInfo.getDomainId());
		return ApiResult.ok(this).data(ciTreeApiSvc.getCITree(config, hasNullNode, returnAllCI));
	}

}
