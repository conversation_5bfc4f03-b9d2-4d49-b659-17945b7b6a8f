package com.uinnova.product.vmdb.comm.rest.support;

import java.lang.reflect.Method;
import java.lang.reflect.Type;

/**
 * 
 * <AUTHOR>
 *
 */
public class ProviderPoint {

    private String name;
    private Method method;

    private Class<?> ifaceType;
    private Object instance;

    public ProviderPoint(String name, Method method, Class<?> ifaceType, Object instance) {
        this.name = name;
        this.method = method;
        this.ifaceType = ifaceType;
        this.instance = instance;
    }

    public String getName() {
        return name;
    }

    public Method getMethod() {
        return method;
    }

    public Class<?> getIfaceType() {
        return ifaceType;
    }

    public Object getInstance() {
        return instance;
    }

    public Class<?>[] getParameterTypes() {
        return this.method.getParameterTypes();
    }

    public Class<?> getReturnType() {
        return this.method.getReturnType();
    }

    public Type[] getGenericParameterTypes() {
        return this.method.getGenericParameterTypes();
    }

    public Type getGenericReturnType() {
        return this.method.getGenericReturnType();
    }

}
