<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="VC_USER_PV_COUNT">
	<resultMap type="com.uinnova.product.eam.comm.dto.VcPvCountDto" id="countResult">
		<result column="USER_ID" jdbcType="BIGINT" property="userId"/>
		<result column="COUNT_NUM" jdbcType="BIGINT" property="countNum"/>
		<result column="TARGET_CODE" jdbcType="VARCHAR" property="optionCode"/>
	</resultMap>
	<select id="selectIdByIndexForUpdate" parameterType="java.util.Map" resultType="java.lang.Long">
		select ID FROM VC_USER_PV_COUNT where DOMAIN_ID=#{domain:BIGINT} and USER_ID=#{user:BIGINT} and COUNT_DATA_TIME=#{time:BIGINT} and TARGET_CODE=#{code:VARCHAR}
	</select>
	<select id="countPv"  parameterType="java.util.Map" resultMap="countResult">
		SELECT
			USER_ID,
			TARGET_CODE,
			sum(PC_COUNT) AS COUNT_NUM
		FROM
			VC_USER_PV_COUNT
		WHERE
			DOMAIN_ID = #{domainId:BIGINT}
			<if test="countDataTimeStart != null">and
				COUNT_DATA_TIME &gt;= #{countDataTimeStart:BIGINT}
			</if>
			<if test="countDataTimeEnd != null ">and
				COUNT_DATA_TIME &lt;= #{countDataTimeEnd:BIGINT}
			</if>
		GROUP BY
			DOMAIN_ID,USER_ID,TARGET_CODE
	</select>
	<update id="addClickRecordById" parameterType="java.util.Map">
		UPDATE VC_USER_PV_COUNT set PC_COUNT=PC_COUNT+${addNumber},MODIFY_TIME = #{modifyTime:BIGINT} where ID=#{id:BIGINT}
	</update>
	
	
	
</mapper>
