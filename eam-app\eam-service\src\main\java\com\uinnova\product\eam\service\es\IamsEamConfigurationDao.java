package com.uinnova.product.eam.service.es;

import com.uinnova.product.eam.comm.bean.EamConfiguration;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;

/**
 * eam项目配置表
 */
@Service
public class IamsEamConfigurationDao extends AbstractESBaseDao<EamConfiguration, EamConfiguration> {
    @Override
    public String getIndex() {
        return "uino_eam_configuration";
    }

    @Override
    public String getType() {
        return "_doc";
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }
}
