package com.uinnova.product.eam.base.diagram.model;

import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.vmdb.comm.model.image.CcImage;
import lombok.Data;

/**
 * @Classname
 * @Description 我的图形DTO
 * <AUTHOR>
 * @Date 2021-08-24-10:21
 */
@Data
public class ESUserShapeDTO extends CcImage implements EntityBean {


    @Comment("key")
    private String key;

    @Comment("userId")
    private Long userId;

    @Comment("ids")
    private Long[] ids;

    @Comment("shapeJson")
    private String shapeJson;

}
