package com.uinnova.product.eam.model.cj.vo;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @description:
 * @author: Lc
 * @create: 2022-01-10 10:01
 */
@Data
@Comment("重置排序")
public class ResetSortVo implements Serializable {

    @Comment("主键id")
    private Long id;
    @Comment("父id")
    private Long parentId;
    @Comment("目标位置")
    private BigDecimal targetPosition;
    @Comment("初始位置")
    private BigDecimal initPosition;

    @Comment("目标id")
    private Long targetId;
    @Comment("标识，-1：拖到目标上边 0：拖到目标下级 1：拖到目标的下边")
    private Integer sign;
}
