package com.uinnova.product.eam.service.es;

import com.uinnova.product.eam.comm.model.es.ClassSetting;
import com.uinnova.product.eam.comm.model.es.DirSetting;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;

/**
 * <AUTHOR>
 */
@Service
public class EamDirSettingDao extends AbstractESBaseDao<DirSetting, DirSetting> {
    @Override
    public String getIndex() {
        return "uino_eam_dir_setting";
    }

    @Override
    public String getType() {
        return getIndex();
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }
}
