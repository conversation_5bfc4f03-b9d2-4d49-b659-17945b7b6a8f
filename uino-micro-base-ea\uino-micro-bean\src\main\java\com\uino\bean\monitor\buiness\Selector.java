package com.uino.bean.monitor.buiness;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Set;

@ApiModel(value = "事件选择器内容")
@Data
public class Selector implements Serializable {

    private static final long serialVersionUID = 491517654504886659L;

    @JSONField(ordinal = 1)
    @ApiModelProperty(value = "属性字段")
    private String property;

    @JSONField(ordinal = 2)
    @ApiModelProperty(value = "操作符（等于：==；包含：contains；不等于：!=；区间：between）")
    private String operator;

    @JSONField(ordinal = 3)
    @ApiModelProperty(value = "录入值")
    private String value;

    @JSONField(ordinal = 4)
    @ApiModelProperty(value = "属性字段描述")
    private String propertyDesc;

    @JSONField(ordinal = 5)
    @ApiModelProperty(value = "操作符描述")
    private String operatorDesc;

    @JSONField(ordinal = 6)
    @ApiModelProperty(value = "录入值(数组)")
    private Set<String> values;

    @JSONField(ordinal = 7)
    @ApiModelProperty(value = "条件判断类型（前端判断筛选条件使用）")
    private Boolean singleType;

    @JSONField(ordinal = 8)
    @ApiModelProperty(value = "开始时间")
    private String startTime;

    @JSONField(ordinal = 9)
    @ApiModelProperty(value = "结束时间")
    private String endTime;

}
