package com.uino.bean.cmdb.base.dataset;

import com.alibaba.fastjson.JSONObject;

import java.io.Serializable;

/**
 * @Classname Coordination
 * @Description 协作权限
 * @Date 星期三
 * @Created by sh
 */
public class DataSetCoordination implements Serializable {

    private static final long serialVersionUID = 1L;

    //    用户code，规则id，读写权限，时间，创建人，修改人
    private Long id;
    private String coordinationUserCode;
    private Long dataSetMallApiId;
    private DataSetMallApiType dataSetType;
    //权限等级、暂不使用
    private int permissionLevel;
    private String creater;
    private String modifier;
    private Long createTime;
    private Long modifyTime;

    private Long domainId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getDataSetMallApiId() {
        return dataSetMallApiId;
    }

    public void setDataSetMallApiId(Long dataSetMallApiId) {
        this.dataSetMallApiId = dataSetMallApiId;
    }

    public String getCoordinationUserCode() {
        return coordinationUserCode;
    }

    public void setCoordinationUserCode(String coordinationUserCode) {
        this.coordinationUserCode = coordinationUserCode;
    }


    public DataSetMallApiType getDataSetType() {
        return dataSetType;
    }

    public void setDataSetType(DataSetMallApiType dataSetType) {
        this.dataSetType = dataSetType;
    }

    public int getPermissionLevel() {
        return permissionLevel;
    }

    public void setPermissionLevel(int permissionLevel) {
        this.permissionLevel = permissionLevel;
    }

    public String getCreater() {
        return creater;
    }

    public void setCreater(String creater) {
        this.creater = creater;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Long getDomainId() {
        return domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }

    public DataSetCoordination() {
    }

    public DataSetCoordination(JSONObject json) {
        if (json.containsKey("id")) {
            this.id = json.getLong("id");
        }
        if (json.containsKey("coordinationUserCode")) {
            this.coordinationUserCode = json.getString("coordinationUserCode");
        }
        if (json.containsKey("dataSetMallApiId")) {
            this.dataSetMallApiId = json.getLong("dataSetMallApiId");
        }
        if (json.containsKey("dataSetType")) {
            DataSetMallApiType type = DataSetMallApiType.valueOf(json.getInteger("dataSetType"));
            if (type != null) {
                this.dataSetType = type;
            }

        }
        //权限等级、暂不使用
        if (json.containsKey("permissionLevel")) {
                this.permissionLevel = json.getInteger("permissionLevel");
        }
        if (json.containsKey("creater")) {
            this.creater = json.getString("creater");
        }
        if (json.containsKey("modifier")) {
            this.modifier = json.getString("domainId");
        }
        if (json.containsKey("createTime")) {
            this.createTime = json.getLong("createTime");
        }
        if (json.containsKey("modifyTime")) {
            this.modifyTime = json.getLong("modifyTime");
        }
        if (json.containsKey("domainId")) {
            this.domainId = json.getLong("domainId");
        }
    }

    public JSONObject toJson() {
        JSONObject json = new JSONObject();
        json.put("id", id);
        json.put("coordinationUserCode", coordinationUserCode);
        json.put("dataSetMallApiId", dataSetMallApiId);
        json.put("dataSetType", dataSetType.getCode());
        json.put("creater", creater);
        json.put("modifier", modifier);
        json.put("createTime", createTime);
        json.put("modifyTime", modifyTime);
        json.put("permissionLevel", permissionLevel);
        json.put("domainId", domainId);
        return json;
    }
}
