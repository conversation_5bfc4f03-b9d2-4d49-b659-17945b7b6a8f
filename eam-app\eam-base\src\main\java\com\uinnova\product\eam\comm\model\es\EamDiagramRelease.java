package com.uinnova.product.eam.comm.model.es;

import cn.hutool.crypto.SecureUtil;
import com.binary.framework.bean.annotation.Comment;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.uinnova.product.eam.base.diagram.model.ESDiagramInfoDTO;
import lombok.Data;

import java.io.Serializable;

/**
 * 视图发布表
 * <AUTHOR>
 */
@Deprecated
@Data
public class EamDiagramRelease implements Serializable {
    @Comment("Id")
    private Long id;

    @Comment("关联视图ID")
    @JsonIgnore
    private Long diagramId;

    @Comment("视图类型")
    private Long diagramType;

    @Comment("自定义版本号")
    private Integer versionNo;

    @Comment("版本id")
    private Long versionId;

    @Comment("版本Name")
    private String versionName;

    @Comment("所属目录")
    private Long dirId;

    @Comment("视图创建者")
    private String creatorId;

    @Comment("视图发布者")
    private Long releaseUserId;

    @Comment("创建时间")
    private Long createTime;

    @Comment("修改时间")
    private Long modifyTime;

    @Comment("发布说明")
    private String releaseDesc;

    @Comment("历史版本标识，1--主版本，0--历史版本")
    private Integer historyVersionFlag;

    @Comment("类型，1--企业级架构设计，2--系统级架构设计")
    private Integer dirType;

    @Comment("视图基础信息")
    private ESDiagramInfoDTO diagram;

    @Comment("视图ID加密字段")
    @JsonProperty("diagramId")
    private String dEnergy;

    public void setDiagramId(Long diagramId) {
        this.diagramId = diagramId;
        this.dEnergy = SecureUtil.md5(String.valueOf(diagramId)).substring(8, 24);
    }
}
