package com.uinnova.product.eam.service;

import com.uinnova.product.eam.model.CiQueryCdtExtend;
import com.uinnova.product.eam.model.QueryCiValueRequest;
import com.uinnova.product.eam.model.VcCiClassInfoDto;
import com.uinnova.product.eam.model.asset.EamCiRltCopyDTO;
import com.uinnova.product.eam.model.asset.EamCiRltCopyResult;
import com.uinnova.product.eam.model.dto.CheckCIAttr;
import com.uinnova.product.eam.model.vo.ESAttrAggScreenBean;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uino.bean.cmdb.base.LibType;

import java.util.List;
import java.util.Map;

/**
 * eam的ci服务层实现
 *
 * <AUTHOR>
 * @version 2020-8-10
 */
public interface IEamCiSvc {

    /**
     * 按指定条件查询CI，并按分类进行汇总
     *
     * @param cdt 查询条件
     * @param libType 库类型    // 私有库 PRIVATE, 设计库 DESIGN, 运行库/基线库 BASELINE
     * @return 包含CI数量的分类列表信息
     */
    List<VcCiClassInfoDto> queryCiCountByClass(CiQueryCdtExtend cdt, LibType libType);

    /**
     * 按指定条件查询CI，并按分类进行汇总
     *
     * @param cdt 查询条件
     * @param libType 库类型    // 私有库 PRIVATE, 设计库 DESIGN, 运行库/基线库 BASELINE
     * @return 包含CI数量的分类列表信息
     */
    List<VcCiClassInfoDto> queryCiCountByClassV2(CiQueryCdtExtend cdt, LibType libType);

    /**
     * 根据ci分类名称获取全部的ci信息
     *
     * @param ciClassName ci类型名称
     * @param libType     库类型
     * @return ci信息列表
     */
    List<CcCiInfo> getCiInfoListByClassName(String ciClassName, LibType libType);
    /**
     * 根据ci分类标识获取全部的ci信息
     *
     * @param ciClassCode 分类标识
     * @param libType     库类型
     * @return ci信息列表
     */
    List<CcCiInfo> getCiInfoListByClassCode(String ciClassCode, LibType libType);

    boolean enableRemoveCiFromView(List<String> ciCodes, String diagramId,String ownerCode, LibType libType);

    Map<Long,String> checkCiAttrs(CheckCIAttr checkCIAttr);

    /**
     * 验证ci在私有库/共享库是否已存在
     * @param ids
     * @param libType
     * @param ownerCode
     * @return
     */
    Integer handleCiExist(List<Long> ids, LibType libType, String ownerCode);

    List<String> queryValues(QueryCiValueRequest request);

    /**
     * 批量复制对象及关系数据
     * @param params ci/rlt参数
     * @return 复制的ci、rlt信息
     */
    EamCiRltCopyResult copyCiAndRltBatch(EamCiRltCopyDTO params);

    /**
     * 获取不做展示的领域id
     * @return id
     */
    List<Long> getDisplayDomain();

    /**
     * 校验主键是否填写完整
     * @param ciCode ci标识
     * @param ownerCode 用户标识
     * @return 是true否false
     */
    Boolean checkPrimaryKey(String ciCode, String ownerCode);

    /**
     * 校验新建权限
     * @param diagramId 视图id
     * @return <分类id-是否可新建ci>
     */
    Map<Long, Boolean> checkAdd(String diagramId);

    /**
     * 校验编辑权限
     * @param ciCode ci标识
     * @param ownerCode 用户标识
     * @return 是否可编辑
     */
    Boolean checkEdit(String ciCode, String ownerCode);

    /**
     * 获取ci属性校验标签规则
     * @param ciInfo ci信息
     * @return 属性填写规则map<属性字段名-规则>
     */
    Map<String, String> getCheckRulesInfo(CcCiInfo ciInfo);

    /**
     * 批量获取视图ci编辑权限
     * @param ciCodes ciCode
     * @return 有编辑权限的ciCode集合
     */
    List<String> checkEditBatch(List<String> ciCodes, String ownerCode);

    Object getAttrValuesBySearchBean(ESAttrAggScreenBean bean);
}
