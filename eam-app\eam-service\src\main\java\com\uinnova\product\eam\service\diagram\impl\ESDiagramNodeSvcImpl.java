package com.uinnova.product.eam.service.diagram.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.binary.core.util.BinaryUtils;
import com.uinnova.product.eam.base.diagram.model.ESDiagramNode;
import com.uinnova.product.eam.base.diagram.model.ESDiagramNodeQueryBean;
import com.uinnova.product.eam.base.diagram.model.ESResponseStruct;
import com.uinnova.product.eam.db.diagram.es.ESDiagramNodeDao;
import com.uinnova.product.eam.service.diagram.ESDiagramNodeSvc;
import com.uinnova.product.vmdb.comm.i18n.MessageUtil;
import com.uino.bean.cmdb.base.ESCIClassInfo;
import com.uino.dao.cmdb.ESCIClassSvc;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 视图节点接口业务实现
 * <AUTHOR>
 */
@Service
@Slf4j
public class ESDiagramNodeSvcImpl implements ESDiagramNodeSvc {
    @Autowired
    private ESDiagramNodeDao diagramNodeDao;
    @Autowired
    private ESCIClassSvc ciClassSvc;


    @Override
    public List<ESDiagramNode> getNodeByIds(List<Long> ids) {
        //es根据ids批量查询
        BoolQueryBuilder nodeQuery = QueryBuilders.boolQuery();
        nodeQuery.must(QueryBuilders.termsQuery("id", ids));
        return diagramNodeDao.getListByQueryScroll(nodeQuery);
    }

    @Override
    public List<ESDiagramNode> getNodeByDiagram(Collection<Long> diagramIds, Collection<String> sheetIds) {
        Assert.notEmpty(diagramIds, "视图id");
        BoolQueryBuilder nodeQuery = QueryBuilders.boolQuery();
        nodeQuery.must(QueryBuilders.termsQuery("diagramId", diagramIds));
        if(CollectionUtils.isNotEmpty(sheetIds)){
            nodeQuery.must(QueryBuilders.termsQuery("sheetId.keyword", sheetIds));
        }
        List<ESDiagramNode> nodeList = diagramNodeDao.getListByQueryScroll(nodeQuery);
        if(CollectionUtils.isEmpty(nodeList)){
            return Collections.emptyList();
        }
        List<ESCIClassInfo> listByQueryScroll = ciClassSvc.getListByQueryScroll(QueryBuilders.matchAllQuery());
        Map<String, Long> convertMap = listByQueryScroll.stream().collect(Collectors.toMap(ESCIClassInfo::getClassName, ESCIClassInfo::getId, (k1, k2) -> k1));
        for (ESDiagramNode each : nodeList) {
            JSONObject jsonObject = JSON.parseObject(each.getNodeJson());
            String className = jsonObject.getString("className");
            Long classId = jsonObject.getLong("classId");
            if (!BinaryUtils.isEmpty(classId) && org.apache.commons.lang3.StringUtils.isNotEmpty(className)) {
                Long aLong = convertMap.get(className);
                if (aLong != null && !classId.equals(aLong)) {
                    jsonObject.put("classId", aLong);
                    each.setNodeJson(JSON.toJSONString(jsonObject));
                }
            }
        }
        return nodeList;
    }

    @Override
    public Integer saveOrUpdateBatch(List<ESDiagramNode> nodes){
        return diagramNodeDao.saveOrUpdateBatch(nodes);
    }

    @Override
    public void deleteByDiagramIds(Collection<Long> diagramIds){
        if(CollectionUtils.isEmpty(diagramIds)){
            return;
        }
        diagramNodeDao.deleteByQuery(QueryBuilders.termsQuery("diagramId", diagramIds), true);
    }

    @Override
    public List<ESResponseStruct> saveOrUpdateNode(Long diagramId, String sheetId, String opList, Boolean flag) {
        MessageUtil.checkEmpty(diagramId, "保存或更新link时diagramId不能为空");
        MessageUtil.checkEmpty(sheetId, "保存或更新link时sheetId不能为空");
        List<ESDiagramNode> saveOrUpdateNodeList = JSONUtil.toList(opList, ESDiagramNode.class);
        for (ESDiagramNode eachNode : saveOrUpdateNodeList) {
            eachNode.setSheetId(sheetId);
            eachNode.setDiagramId(diagramId);
            eachNode.setCiCode("");
            if (!BinaryUtils.isEmpty(eachNode.getNodeJson())) {
                JSONObject nodeJson = JSONObject.parseObject(eachNode.getNodeJson());
                String ciCode = nodeJson.getString("ciCode");
                if (BinaryUtils.isEmpty(ciCode)) {
                    continue;
                }
                eachNode.setCiCode(ciCode);
            }
        }

        List<ESResponseStruct> structList = new ArrayList<>();
        //更新操作
        if (flag) {
            //获取所有的nodeId，并根据nodeId查出其对应的node对象
            String[] nodeKeyArr = saveOrUpdateNodeList.stream().map(ESDiagramNode::getKey).distinct().toArray(String[]::new);
            if (BinaryUtils.isEmpty(nodeKeyArr)) {
                ESResponseStruct struct = new ESResponseStruct();
                struct.setErrorMessage("更新node时需要设置nodeKey");
                structList.add(struct);
                return structList;
            }
            ESDiagramNodeQueryBean nodeQuery = new ESDiagramNodeQueryBean();
            nodeQuery.setDiagramId(diagramId);
            nodeQuery.setSheetIdEqual(sheetId);
            nodeQuery.setKeys(nodeKeyArr);
            List<ESDiagramNode> originNodeList = diagramNodeDao.getListByCdt(nodeQuery);
            //建立nodeId和originId之间的映射关系
            Map<String, Long> nodeIdMap = new HashMap<>();
            if (!BinaryUtils.isEmpty(originNodeList)) {
                for (ESDiagramNode originNode : originNodeList) {
                    nodeIdMap.put(originNode.getKey(), originNode.getId());
                }
            } else {
                return structList;
            }
            if (BinaryUtils.isEmpty(nodeIdMap)) {
                return structList;
            }
            List<ESDiagramNode> skipNodeList = new ArrayList<>();
            for (ESDiagramNode updateNode : saveOrUpdateNodeList) {
                String nodeKey = updateNode.getKey();
                Long tmpLogicId = nodeIdMap.get(nodeKey);
                if (!BinaryUtils.isEmpty(tmpLogicId)) {
                    updateNode.setId(tmpLogicId);
                } else {
                    skipNodeList.add(updateNode);
                }
            }
            if (!BinaryUtils.isEmpty(skipNodeList)) {
                saveOrUpdateNodeList.removeAll(skipNodeList);
            }
            if (!BinaryUtils.isEmpty(saveOrUpdateNodeList)) {
                diagramNodeDao.saveOrUpdateBatch(saveOrUpdateNodeList);
            }
        } else {
            diagramNodeDao.saveOrUpdateBatch(saveOrUpdateNodeList);
        }
        return structList;
    }
}
