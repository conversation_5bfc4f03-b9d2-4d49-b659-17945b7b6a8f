package com.uino.bean.permission.base;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Tolerate;

/**
 * mapping-table: 用户表[SYS_USER]
 * 
 * <AUTHOR>
 */
@Builder
@Getter
@Setter
@ApiModel(value="用户表",description = "用户表")
public class SysUser implements Serializable {
	private static final long serialVersionUID = 1L;

	@Tolerate
	public SysUser() {

	}

	/** ID */
	@ApiModelProperty(value="id",example = "123")
	private Long id;

	/** 登录代码 */
	@ApiModelProperty(value="登录代码")
	private String loginCode;

	/** 登录密码 */
	@ApiModelProperty(value="登录密码")
	private String loginPasswd;

	/** 显示名 */
	@ApiModelProperty(value="显示名")
	private String userName;

	/** 用户头像 */
	@ApiModelProperty(value="用户头像")
	private String icon;

	/** 手机号 */
	@ApiModelProperty(value="手机号",example = "123456677")
	private String mobileNo;

	/** 电子邮件地址 */
	@ApiModelProperty(value="电子邮件地址",example = "<EMAIL>")
	private String emailAdress;

	/** 即时通讯地址 */
	@ApiModelProperty(value="即时通讯地址")
	private String imsAdress;

	/** 备注 */
	@ApiModelProperty(value="备注")
	private String notes;

	/** 是否允许修改密码 */
	@ApiModelProperty(value="是否允许修改密码")
	private Integer allowChangePasswd;

	/** 最后一次登录日志 */
	@ApiModelProperty(value="最后一次登录日志")
	private Long lastLoginLogId;

	/** 最后一次登录时间 */
	@ApiModelProperty("最后一次登录时间")
	private Long lastLoginTime;

	/** 登录尝试次数 */
	@ApiModelProperty(value="登录尝试次数",example = "1")
	private Integer tryTimes;

	/** 登录标志 */
	@ApiModelProperty(value="登录标志")
	private Integer loginFlag;

	/** 超级用户标志 */
	@ApiModelProperty(value="超级用户标志")
	private Integer superUserFlag;

	/** 密码有效期 */
	@ApiModelProperty(value="密码有效期")
	private Long passwdValidDays;

	/** 开始锁定的时间 */
	@ApiModelProperty(value="开始锁定的时间")
	private Long lockedTime;

	/**
	 * 锁定标志
	 * <p>
	 * 0:未锁定 1:锁定 其他:未锁定(暂无其他状态都认为未锁定)
	 */
	@ApiModelProperty(value="锁定标志，0:未锁定 1:锁定 其他:未锁定",example = "1")
	private Integer lockFlag;

	/** 是否需要修改密码 */
	@ApiModelProperty(value="是否需要修改密码")
	private Integer isUpdatePwd;

	/** 登录认证代码 */
	@ApiModelProperty(value="登录认证代码")
	private String loginAuthCode;

	/** 用户状态 */
	@ApiModelProperty(value="用户状态")
	private Integer status;

	/** 所属域 */
	@ApiModelProperty(value="所属域id")
	private Long domainId;

	/** 创建人 */
	@ApiModelProperty(value="创建人",example = "mike")
	private String creator;

	/** 修改人 */
	@ApiModelProperty(value="修改人",example = "mike")
	private String modifier;

	/** 创建时间 */
	@ApiModelProperty(value="创建时间")
	private Long createTime;

	/** 修改时间 */
	@ApiModelProperty(value="修改时间")
	private Long modifyTime;

	/**
	 * 去除当前对象敏感数据
	 */
	public SysUser clearSensitive() {
		this.setAllowChangePasswd(null);
		this.setCreator(null);
		this.setLoginPasswd(null);
		this.setLastLoginLogId(null);
		this.setLoginAuthCode(null);
		return this;
	}

}
