package com.uino.bean.cmdb.base.dataset.relation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(value="")
public class RelationRuleNodeExternal {

	@ApiModelProperty(value="分类名称",example = "fruit")
	private String className;

	@ApiModelProperty(value="")
	private Long pageNodeId;

	@ApiModelProperty(value="关系构建规则")
	private List<RelationRuleAttrCdtExternal> cdts;
	
	public String getClassName() {
		return className;
	}
	public void setClassName(String className) {
		this.className = className;
	}
	public Long getPageNodeId() {
		return pageNodeId;
	}
	public void setPageNodeId(Long pageNodeId) {
		this.pageNodeId = pageNodeId;
	}
	public List<RelationRuleAttrCdtExternal> getCdts() {
		return cdts;
	}
	public void setCdts(List<RelationRuleAttrCdtExternal> cdts) {
		this.cdts = cdts;
	}
	
}
