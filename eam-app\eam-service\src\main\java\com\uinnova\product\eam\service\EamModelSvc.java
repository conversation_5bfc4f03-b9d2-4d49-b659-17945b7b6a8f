package com.uinnova.product.eam.service;

import com.uinnova.product.eam.base.model.BaseQueryDiagramDto;
import com.uinnova.product.eam.model.EamCategoryCdt;
import com.uinnova.product.eam.model.ModuleInfo;
import com.uinnova.product.eam.model.bm.CategoryNode;
import com.uinnova.product.eam.model.diagram.DiagramBindLogo;
import com.uinnova.product.eam.model.dto.ModelNavigateDTO;
import com.uinnova.product.eam.model.vo.EamCategoryVO;
import com.uinnova.product.eam.model.vo.ModelDiagramResp;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.base.LibType;

import java.util.List;
import java.util.Map;

/**
 * 模型相关接口
 * <AUTHOR>
 */
public interface EamModelSvc {
    /**
     * 建模工艺id
     * @param modelId 模型id
     * @param parentId 目录id
     * @param modelName 模型名称
     * @return 创建信息
     */
    String createModel(Long modelId, Long parentId, String modelName);

    /**
     * 创建模型视图
     * @param cdt 模型目录id或视图id+ciCode
     * @return 视图加密id
     */
    ModelDiagramResp createModelDiagram(EamCategoryCdt cdt);

    /**
     * 获取模型导航条
     * @param dto 视图id
     * @return 导航条
     */
    ModelNavigateDTO getModelNavigate(BaseQueryDiagramDto dto);

    /**
     * 获取模型视图下钻icon
     * @param dto 视图id、目录id
     * @return 下钻标识集合
     */
    List<DiagramBindLogo> getDiagramBindLogo(BaseQueryDiagramDto dto);

    /**
     * 保存或更新文件夹信息
     * @param vo 文件夹信息
     * @param libType 库
     * @return 文件夹id
     */
    Long saveOrUpdateCategory(EamCategoryVO vo, LibType libType);

    /**
     * 批量根据ci创建模型目录
     * @param diagramId 当前ci所在视图
     * @param ciList ci信息
     * @param ownerCode 用户标识
     * @param libType 库
     * @return <ciCode,创建出的目录绑定的视图id(针对已经存在目录的情况)>
     */
    Map<String, String> createModelCategory(String diagramId, List<ESCIInfo> ciList, String ownerCode, LibType libType);

    /**
     * 保存CI中自动填充，视图中元素的所属上级元素
     * @param ciList 保存的ci集合
     * @param diagramId 视图id
     * @param libType 库
     */
    void fillAttrBelongUpperElement(List<CcCiInfo> ciList, String diagramId, LibType libType);

    /**
     * 批量删除文件夹，同时删除文件夹下的视图、方案信息
     * @param cdt 参数
     * @return 删除结果
     */
    Integer deleteBatch(EamCategoryCdt cdt);

    /**
     * 删除文件夹
     * @param ids 文件夹ids
     * @param delType 删除类型
     * @param ownerCode 用户标识
     * @param libType 库
     */
    void deleteCategory(List<Long> ids, int delType, String ownerCode, LibType libType);

    /**
     * 查询批量发布列表
     * @param dirId 目录id
     * @param libType 库
     * @return 目录树
     */
    List<CategoryNode> selectNodeTree(Long dirId, LibType libType);

    /**
     * 查询已发布的建模层级详细信息
     * @return 模型树
     */
    List<ModuleInfo> queryModuleInfo();

    /**
     * 批量复制文件夹、视图OR方案
     * @param cdt 文件夹、视图OR方案id集合,目标文件夹id
     * @return 复制结果
     */
    Integer copyBatch(EamCategoryCdt cdt);
}
