package com.uinnova.product.eam.model.cj.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * @description:
 * @author: Lc
 * @create: 2022-03-02 14:04
 */
@Getter
public enum ChapterDataTypeEnum {

    PRODUCT(1, "制品"),
    DATA_TABLE(2, "表格"),
    RICH_TEXT(3, "富文本"),
    DATA_SET(4, "清单制品"),
    APPENDIX(5, "附件");

    private Integer dataType;

    private String dataDesc;

    ChapterDataTypeEnum(Integer dataType, String dataDesc) {
        this.dataType = dataType;
        this.dataDesc = dataDesc;
    }

}
