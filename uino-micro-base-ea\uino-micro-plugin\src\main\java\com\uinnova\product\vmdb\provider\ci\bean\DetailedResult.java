package com.uinnova.product.vmdb.provider.ci.bean;

import com.binary.core.util.BinaryUtils;
import com.uinnova.product.vmdb.comm.err.ErrorBean;

import java.util.List;

/**
 * 按分类保存详细的保存结果<br/>
 * 当前错误级别为分类错误
 * 
 * <AUTHOR>
 *
 */
public class DetailedResult extends ErrorBean {
	private static final long serialVersionUID = 1L;

	/**
	 * 当前组入参总数
	 */
	private Integer totalCount = 0;
	/**
	 * 当前组成功总数[add+update]
	 */
	private Integer successCount = 0;

	/**
	 * 当前组保存失败总数
	 */
	private Integer failCount = 0;

	/**
	 * 详细的新增数量
	 */
	private Integer insertCount = 0;

	/**
	 * 详细的更新数量
	 */
	private Integer updateCount = 0;

	/**
	 * 详细的忽略数量
	 */
	private Integer ignoreCount = 0;

	/**
	 * 当前所属分类ID
	 */
	private Long classId;

	/**
	 * 当前所属分类Name
	 */
	private String className;

	/**
	 * 操作用户名
	 */
	private String userName;

	/**
	 * 根据需要最终返回所有保存的主键ID
	 */
	private List<Long> ids;

	/**
	 * 根据需要最终返回所有新增的主键ID
	 */
	private List<Long> insertIds;

	/**
	 * 根据需要最终返回所有更新的主键ID
	 */
	private List<Long> updateIds;

	/**
	 * 导入时需要使用的title行内容(Excel标题行原始值)
	 */
	private List<String> titles;

	/**
	 * 非主干错误更加具体的错误信息
	 */
	private List<DetailedErrInfo> errInfos;

	/**
	 * 每次导出的错误信息文件位置
	 */
	private String errFilePath;

	public DetailedResult() {
	}

	/**
	 * 可填充主干错误信息
	 * 
	 * @param classId
	 * @param errKey
	 * @param errType
	 */
	public DetailedResult(Long classId, String errKey, Integer errType) {
		this.classId = classId;
		if (!BinaryUtils.isEmpty(errKey)) {
			this.setErrKey(errKey);
		}
		if (!BinaryUtils.isEmpty(errType)) {
			this.setErrType(errType);
		}
	}

	public Integer getTotalCount() {
		return totalCount;
	}

	public void setTotalCount(Integer totalCount) {
		this.totalCount = totalCount;
	}

	public Integer getSuccessCount() {
		return successCount;
	}

	public void setSuccessCount(Integer successCount) {
		this.successCount = successCount;
	}

	public Integer getFailCount() {
		return failCount;
	}

	public void setFailCount(Integer failCount) {
		this.failCount = failCount;
	}

	public Integer getInsertCount() {
		return insertCount;
	}

	public void setInsertCount(Integer insertCount) {
		this.insertCount = insertCount;
	}

	public Integer getUpdateCount() {
		return updateCount;
	}

	public void setUpdateCount(Integer updateCount) {
		this.updateCount = updateCount;
	}

	public Integer getIgnoreCount() {
		return ignoreCount;
	}

	public void setIgnoreCount(Integer ignoreCount) {
		this.ignoreCount = ignoreCount;
	}

	public Long getClassId() {
		return classId;
	}

	public void setClassId(Long classId) {
		this.classId = classId;
	}

	public String getClassName() {
		return className;
	}

	public void setClassName(String className) {
		this.className = className;
	}

	public List<Long> getIds() {
		return ids;
	}

	public void setIds(List<Long> ids) {
		this.ids = ids;
	}

	public List<DetailedErrInfo> getErrInfos() {
		return errInfos;
	}

	public void setErrInfos(List<DetailedErrInfo> errInfos) {
		this.errInfos = errInfos;
	}

	public String getErrFilePath() {
		return errFilePath;
	}

	public void setErrFilePath(String errFilePath) {
		this.errFilePath = errFilePath;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public List<String> getTitles() {
		return titles;
	}

	public void setTitles(List<String> titles) {
		this.titles = titles;
	}

	public List<Long> getInsertIds() {
		return insertIds;
	}

	public void setInsertIds(List<Long> insertIds) {
		this.insertIds = insertIds;
	}

	public List<Long> getUpdateIds() {
		return updateIds;
	}

	public void setUpdateIds(List<Long> updateIds) {
		this.updateIds = updateIds;
	}

}
