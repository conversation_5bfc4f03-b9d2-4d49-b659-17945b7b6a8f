//package com.uino.init.api;
//
//import com.uino.plugin.client.init.UinoBaseRequestMappingHandlerMapping;
//import org.springframework.core.annotation.AnnotationUtils;
//import org.springframework.web.servlet.mvc.condition.RequestCondition;
//
//import java.lang.reflect.Method;
//
///**
// * @Title: ApiVersioningRequestMappingHandlerMapping
// * @Description: ApiVersioningRequestMappingHandlerMapping
// * @Author: YGQ
// * @Create: 2021-04-24 00:04
// **/
//public class ApiVersioningRequestMappingHandlerMapping extends UinoBaseRequestMappingHandlerMapping {
//
//    @Override
//    protected RequestCondition<ApiVersionCondition> getCustomTypeCondition(Class<?> handlerType) {
//        ApiVersion apiVersion = AnnotationUtils.findAnnotation(handlerType, ApiVersion.class);
//        return createCondition(apiVersion);
//    }
//
//    @Override
//    protected RequestCondition<ApiVersionCondition> getCustomMethodCondition(Method method) {
//        ApiVersion apiVersion = AnnotationUtils.findAnnotation(method, ApiVersion.class);
//        return createCondition(apiVersion);
//    }
//
//    private RequestCondition<ApiVersionCondition> createCondition(ApiVersion apiVersion) {
//        return apiVersion == null ? null : new ApiVersionCondition(apiVersion.value());
//    }
//
//}
