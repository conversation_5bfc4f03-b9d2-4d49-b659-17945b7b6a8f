package com.uinnova.product.eam.model;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 智能成图参数接收
 * <AUTHOR>
 */
@Data
public class EamAutoLayoutCdt implements Serializable {

    @Comment("分类id")
    private Long classId;

    @Comment("分类名称")
    private String className;

    @Comment("子级")
    private List<EamAutoLayoutCdt> children;

}
