package com.uinnova.product.eam.model.diagram;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.util.List;

/**
 * 元模型link节点实体
 * <AUTHOR>
 * @date 2022/5/25
 */
@Data
public class VisualModelLink {
    @Comment("完整路径：源端classId-关系classId-目标端classId")
    private String path;
    @Comment("显示名称")
    private String label;
    @Comment("关系分类id")
    private Long classId;
    @Comment("起始节点key")
    private Long from;
    @Comment("终止节点key")
    private Long to;
    private List<Double> points;

    public VisualModelLink(){

    }

    public VisualModelLink(String path){
        this.path = path;
        String[] str = path.split("_");
        this.from = Long.parseLong(str[0]);
        this.classId = Long.parseLong(str[1]);
        this.to = Long.parseLong(str[2]);
    }
}
