package com.uinnova.product.eam.comm.model.es;

import com.binary.framework.bean.annotation.Comment;
import com.uino.bean.cmdb.base.LibType;
import lombok.Data;

/**
 * 导航层级配置
 */
@Data
public class EamHierarchyNavigation{
    @Comment("主键")
    private Long id;

    @Comment("所属人")
    private String ownerCode;

    @Comment("模型id")
    private Long modelId;

    @Comment("模型层级id")
    private Long modelLvlId;

    @Comment("完成状态 0 未开始 1 进行中 2 已完成")
    private Integer status;

    @Comment("完成时间")
    private Long finishTime;

    @Comment("三库标识")
    private LibType libType;
}
