package com.uinnova.product.eam.web.asset.mvc;

import com.binary.core.util.BinaryUtils;
import com.binary.framework.web.RemoteResult;
import com.uino.bean.permission.base.SysModule;
import com.uino.bean.permission.base.SysRoleModuleRlt;
import com.uino.dao.permission.ESModuleSvc;
import com.uino.dao.permission.rlt.ESRoleModuleRltSvc;
import com.uino.dao.util.ESUtil;
import com.uino.util.sys.SysUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/asset/test")
@Slf4j
public class ModuleRefreshDataMVC {


    @Resource
    ESModuleSvc esModuleSvc;

    @Resource
    ESRoleModuleRltSvc esRoleModuleRltSvc;


    @GetMapping("/moduleRefreshData")
    public RemoteResult moduleRefreshData() {
        /**
         * 1.读取配置文件目录结构
         * 2.读取数据库下系统目录层级
         * 3.删除初始化部分-获取资产仓库下用户创建文件夹
         * 4.保存更新后目录结构
         */
        List<SysModule> listByQuery = esModuleSvc.getListByQuery(null);
        Optional<SysModule> first1 = listByQuery.stream().filter(sysModule -> "资产仓库".equals(sysModule.getModuleSign())).findFirst();
        List<Long> assetIds = new ArrayList<>();
        List<Long> assetOneChildren = Collections.emptyList();
        if (first1.isPresent()) {
            SysModule oldAsset = first1.get();
            // 获取资产仓库下自建目录
            assetOneChildren = listByQuery.stream().filter(sysModule -> sysModule.getParentId().equals(oldAsset.getId()))
                    .map(sysModule -> sysModule.getId()).collect(Collectors.toList());
            getChildrenIdsByPrentId(assetIds, oldAsset.getId(), listByQuery);
        }
        // 获取所有目录
        List<Long> removeIds = listByQuery.stream().map(sysModule -> sysModule.getId()).collect(Collectors.toList());
//        removeIds.removeAll(assetIds);
        if (CollectionUtils.isNotEmpty(removeIds)) {
            esModuleSvc.deleteByIds(removeIds);
        }
        esModuleSvc.init();
        List<SysModule> listByQueryNew = esModuleSvc.getListByQuery(null);

//        List<SysModule> assetSysModule = listByQuery.stream().filter(sysModule -> assetIds.contains(sysModule.getId())).collect(Collectors.toList());

        Optional<SysModule> firstNew = listByQueryNew.stream().filter(sysModule -> "资产仓库".equals(sysModule.getModuleSign())).findFirst();
        Long assetId = null;
        boolean flag = false;
        if (firstNew.isPresent()) {
            flag = true;
            assetId = firstNew.get().getId();
        }
//        for (SysModule sysModule : assetSysModule) {
//            sysModule.setIsInit(false);
//            if (assetOneChildren.contains(sysModule.getId()) && flag) {
//                sysModule.setParentId(assetId);
//            }
//        }
//        Integer integer = esModuleSvc.saveOrUpdateBatch(assetSysModule);
        // 查询admin角色是否存在菜单权限
        List<Long> quickeaSysIds = listByQueryNew.stream().map(sysModule -> sysModule.getId()).collect(Collectors.toList());
        List<SysRoleModuleRlt> roleId = esRoleModuleRltSvc.getListByQuery(QueryBuilders.termQuery("roleId", 1));
        for (SysRoleModuleRlt sysRoleModuleRlt : roleId) {
            if (quickeaSysIds.contains(sysRoleModuleRlt.getModuleId())) {
                quickeaSysIds.remove(sysRoleModuleRlt.getModuleId());
            }
        }
        if (CollectionUtils.isNotEmpty(quickeaSysIds)) {
            ArrayList<SysRoleModuleRlt> list = new ArrayList<>();
            for (Long quickeaSysId : quickeaSysIds) {
                SysRoleModuleRlt sysRoleModuleRlt = new SysRoleModuleRlt();
                sysRoleModuleRlt.setRoleId(1L);
                sysRoleModuleRlt.setModuleId(quickeaSysId);
                sysRoleModuleRlt.setDomainId(SysUtil.getCurrentUserInfo().getDomainId());
                sysRoleModuleRlt.setCreateTime(ESUtil.getNumberDateTime());
                sysRoleModuleRlt.setModifyTime(ESUtil.getNumberDateTime());
                list.add(sysRoleModuleRlt);
            }
            esRoleModuleRltSvc.saveOrUpdateBatch(list);
        }
        return new RemoteResult(1);
    }

    private void getChildrenIdsByPrentId(List<Long> assetIds, Long id, List<SysModule> listByQuery) {
        for (SysModule sysModule : listByQuery) {
            if (!BinaryUtils.isEmpty(sysModule.getParentId()) && id.equals(sysModule.getParentId())) {
                assetIds.add(sysModule.getId());
                getChildrenIdsByPrentId(assetIds, sysModule.getId(), listByQuery);
            }
        }
    }
}
