package com.uino.api.client.cmdb.local;

import com.uino.service.cmdb.dataset.microservice.IGraphAnalysisSvc;
import com.uino.bean.cmdb.base.dataset.DataSetMallApiRelationRuleExternal;
import com.uino.bean.cmdb.business.dataset.FriendInfo;
import com.uino.bean.cmdb.business.dataset.UpDownAttrCdt;
import com.uino.api.client.cmdb.IGraphAnalysisApiSvc;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @data 2019/8/7 11:31.
 */
@Service
public class GraphAnalysisApiSvcLocal implements IGraphAnalysisApiSvc {

    @Autowired
    private IGraphAnalysisSvc graphAnalysisSvc;


    @Override
    public FriendInfo queryCiUpDownByCiIds(Long domainId, List<Long> startCiIds, List<UpDownAttrCdt> ciConditions, List<UpDownAttrCdt> rltConditions, List<Long> rltLvls, Integer upLevel, Integer downLevel, Boolean hasAttr) {
        return graphAnalysisSvc.queryCiUpDownByCiIds(domainId, startCiIds, ciConditions, rltConditions, rltLvls, upLevel, downLevel, hasAttr);
    }

    @Override
    public FriendInfo queryCiUpDownByCiId(Long domainId, Long startCiId, List<UpDownAttrCdt> ciConditions, List<UpDownAttrCdt> rltConditions, List<Long> rltLvls, Integer upLevel, Integer downLevel, Boolean hasAttr) {
        return graphAnalysisSvc.queryCiUpDownByCiId(domainId, startCiId, ciConditions, rltConditions, rltLvls, upLevel, downLevel, hasAttr);
    }

	@Override
	public FriendInfo queryFriendByCiUsingRule(List<String> enter, DataSetMallApiRelationRuleExternal rule) {
		return graphAnalysisSvc.queryFriendByCiUsingRule(enter, rule);
	}
}
