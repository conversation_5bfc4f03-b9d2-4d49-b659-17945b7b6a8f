package com.uinnova.product.eam.comm.model.es;

import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

@Data
@Comment("操作计数表[UINO_EAM_DIAGRAM_DIR]")
@Deprecated
public class EamDiagramDir implements EntityBean {


    private Long id;

    private String dirName;

    private Integer dirType;

    private Long parentId;


    private Long userId;


    private Integer dirLvl;


    private String dirPath;


    private Integer orderNo;


    private Integer isLeaf;


    private String icon;


    private String dirDesc;


    private Long domainId;


    private Integer dataStatus;


    private String creator;


    private String modifier;


    private Long createTime;


    private Long modifyTime;

    private Long subjectId;

    private Long oldParentId;

    private Integer dirInit;

    private String esSysId;

    // 关联文件，1：企业级我的文件，2：系统级我的文件
    private Integer relationFile;

    private String sysType;

    // monet应用系统编码
    private String ciCode;

    //做前端反显文件夹创建者用
    private String userName;

    private Integer sysDir;

    /**
     * 是否是继承文件夹权限
     */
    private Boolean extendPermisson;

    /**
     * 继承文件夹权限的id
     */
    private Long extendDirId;

    private FolderPermissionManager folderPermissionManager;
}
