package com.binarys.product.sys.comm.model.sys;




import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;


@Comment("操作员表[SYS_OP]")
public class CSysOp implements Condition {
	private static final long serialVersionUID = 1L;


	@Comment("ID[ID] operate-Equal[=]")
	private Long id;


	@Comment("ID[ID] operate-In[in]")
	private Long[] ids;


	@Comment("ID[ID] operate-GTEqual[>=]")
	private Long startId;

	@Comment("ID[ID] operate-LTEqual[<=]")
	private Long endId;


	@Comment("操作员编码[OP_CODE] operate-Like[like]")
	private String opCode;


	@Comment("操作员编码[OP_CODE] operate-Equal[=]")
	private String opCodeEqual;


	@Comment("操作员编码[OP_CODE] operate-In[in]")
	private String[] opCodes;


	@Comment("操作员姓名[OP_NAME] operate-Like[like]")
	private String opName;


	@Comment("操作员姓名[OP_NAME] operate-Equal[=]")
	private String opNameEqual;


	@Comment("操作员姓名[OP_NAME] operate-In[in]")
	private String[] opNames;


	@Comment("操作员类别[OP_KIND] operate-Equal[=]    操作员类别:0-超级管理员 1-管理员 2-普通用户")
	private Integer opKind;


	@Comment("操作员类别[OP_KIND] operate-In[in]    操作员类别:0-超级管理员 1-管理员 2-普通用户")
	private Integer[] opKinds;


	@Comment("操作员类别[OP_KIND] operate-GTEqual[>=]    操作员类别:0-超级管理员 1-管理员 2-普通用户")
	private Integer startOpKind;

	@Comment("操作员类别[OP_KIND] operate-LTEqual[<=]    操作员类别:0-超级管理员 1-管理员 2-普通用户")
	private Integer endOpKind;


	@Comment("手机号[MOBILE_NO] operate-Like[like]")
	private String mobileNo;


	@Comment("手机号[MOBILE_NO] operate-Equal[=]")
	private String mobileNoEqual;


	@Comment("手机号[MOBILE_NO] operate-In[in]")
	private String[] mobileNos;


	@Comment("电子邮件地址[EMAIL_ADRESS] operate-Like[like]")
	private String emailAdress;


	@Comment("电子邮件地址[EMAIL_ADRESS] operate-Equal[=]")
	private String emailAdressEqual;


	@Comment("电子邮件地址[EMAIL_ADRESS] operate-In[in]")
	private String[] emailAdresss;


	@Comment("登录代码[LOGIN_CODE] operate-Like[like]")
	private String loginCode;


	@Comment("登录代码[LOGIN_CODE] operate-Equal[=]")
	private String loginCodeEqual;


	@Comment("登录代码[LOGIN_CODE] operate-In[in]")
	private String[] loginCodes;


	@Comment("登录密码[LOGIN_PASSWD] operate-Like[like]")
	private String loginPasswd;


	@Comment("登录密码[LOGIN_PASSWD] operate-Equal[=]")
	private String loginPasswdEqual;


	@Comment("登录密码[LOGIN_PASSWD] operate-In[in]")
	private String[] loginPasswds;


	@Comment("操作员简称[SHORT_NAME] operate-Like[like]")
	private String shortName;


	@Comment("操作员简称[SHORT_NAME] operate-Equal[=]")
	private String shortNameEqual;


	@Comment("操作员简称[SHORT_NAME] operate-In[in]")
	private String[] shortNames;


	@Comment("备注[NOTES] operate-Like[like]")
	private String notes;


	@Comment("是否允许修改密码[ALLOW_CHANGE_PASSWD] operate-Equal[=]")
	private Integer allowChangePasswd;


	@Comment("是否允许修改密码[ALLOW_CHANGE_PASSWD] operate-In[in]")
	private Integer[] allowChangePasswds;


	@Comment("是否允许修改密码[ALLOW_CHANGE_PASSWD] operate-GTEqual[>=]")
	private Integer startAllowChangePasswd;

	@Comment("是否允许修改密码[ALLOW_CHANGE_PASSWD] operate-LTEqual[<=]")
	private Integer endAllowChangePasswd;


	@Comment("最后一次登录日志[LAST_LOGIN_LOG_ID] operate-Equal[=]")
	private Long lastLoginLogId;


	@Comment("最后一次登录日志[LAST_LOGIN_LOG_ID] operate-In[in]")
	private Long[] lastLoginLogIds;


	@Comment("最后一次登录日志[LAST_LOGIN_LOG_ID] operate-GTEqual[>=]")
	private Long startLastLoginLogId;

	@Comment("最后一次登录日志[LAST_LOGIN_LOG_ID] operate-LTEqual[<=]")
	private Long endLastLoginLogId;


	@Comment("最后一次登录时间[LAST_LOGIN_TIME] operate-Equal[=]")
	private Long lastLoginTime;


	@Comment("最后一次登录时间[LAST_LOGIN_TIME] operate-In[in]")
	private Long[] lastLoginTimes;


	@Comment("最后一次登录时间[LAST_LOGIN_TIME] operate-GTEqual[>=]")
	private Long startLastLoginTime;

	@Comment("最后一次登录时间[LAST_LOGIN_TIME] operate-LTEqual[<=]")
	private Long endLastLoginTime;


	@Comment("登录尝试次数[TRY_TIMES] operate-Equal[=]")
	private Integer tryTimes;


	@Comment("登录尝试次数[TRY_TIMES] operate-In[in]")
	private Integer[] tryTimess;


	@Comment("登录尝试次数[TRY_TIMES] operate-GTEqual[>=]")
	private Integer startTryTimes;

	@Comment("登录尝试次数[TRY_TIMES] operate-LTEqual[<=]")
	private Integer endTryTimes;


	@Comment("登录标志[LOGIN_FLAG] operate-Equal[=]    登录标志:0-未登录 1-已登录")
	private Integer loginFlag;


	@Comment("登录标志[LOGIN_FLAG] operate-In[in]    登录标志:0-未登录 1-已登录")
	private Integer[] loginFlags;


	@Comment("登录标志[LOGIN_FLAG] operate-GTEqual[>=]    登录标志:0-未登录 1-已登录")
	private Integer startLoginFlag;

	@Comment("登录标志[LOGIN_FLAG] operate-LTEqual[<=]    登录标志:0-未登录 1-已登录")
	private Integer endLoginFlag;


	@Comment("超级用户标志[SUPER_USER_FLAG] operate-Equal[=]    超级用户标志:0-否 1-是")
	private Integer superUserFlag;


	@Comment("超级用户标志[SUPER_USER_FLAG] operate-In[in]    超级用户标志:0-否 1-是")
	private Integer[] superUserFlags;


	@Comment("超级用户标志[SUPER_USER_FLAG] operate-GTEqual[>=]    超级用户标志:0-否 1-是")
	private Integer startSuperUserFlag;

	@Comment("超级用户标志[SUPER_USER_FLAG] operate-LTEqual[<=]    超级用户标志:0-否 1-是")
	private Integer endSuperUserFlag;


	@Comment("密码有效期[PASSWD_VALID_DAYS] operate-Equal[=]    密码有效期:单位：天")
	private Long passwdValidDays;


	@Comment("密码有效期[PASSWD_VALID_DAYS] operate-In[in]    密码有效期:单位：天")
	private Long[] passwdValidDayss;


	@Comment("密码有效期[PASSWD_VALID_DAYS] operate-GTEqual[>=]    密码有效期:单位：天")
	private Long startPasswdValidDays;

	@Comment("密码有效期[PASSWD_VALID_DAYS] operate-LTEqual[<=]    密码有效期:单位：天")
	private Long endPasswdValidDays;


	@Comment("开始锁定的时间[LOCKED_TIME] operate-Equal[=]    用户被锁定的时间")
	private Long lockedTime;


	@Comment("开始锁定的时间[LOCKED_TIME] operate-In[in]    用户被锁定的时间")
	private Long[] lockedTimes;


	@Comment("开始锁定的时间[LOCKED_TIME] operate-GTEqual[>=]    用户被锁定的时间")
	private Long startLockedTime;

	@Comment("开始锁定的时间[LOCKED_TIME] operate-LTEqual[<=]    用户被锁定的时间")
	private Long endLockedTime;


	@Comment("锁定标志[LOCK_FLAG] operate-Equal[=]    锁定标志:0-未锁定 1-锁定")
	private Integer lockFlag;


	@Comment("锁定标志[LOCK_FLAG] operate-In[in]    锁定标志:0-未锁定 1-锁定")
	private Integer[] lockFlags;


	@Comment("锁定标志[LOCK_FLAG] operate-GTEqual[>=]    锁定标志:0-未锁定 1-锁定")
	private Integer startLockFlag;

	@Comment("锁定标志[LOCK_FLAG] operate-LTEqual[<=]    锁定标志:0-未锁定 1-锁定")
	private Integer endLockFlag;


	@Comment("是否需要修改密码[IS_UPDATE_PWD] operate-Equal[=]    是否需要修改密码:1=是 0=否")
	private Integer isUpdatePwd;


	@Comment("是否需要修改密码[IS_UPDATE_PWD] operate-In[in]    是否需要修改密码:1=是 0=否")
	private Integer[] isUpdatePwds;


	@Comment("是否需要修改密码[IS_UPDATE_PWD] operate-GTEqual[>=]    是否需要修改密码:1=是 0=否")
	private Integer startIsUpdatePwd;

	@Comment("是否需要修改密码[IS_UPDATE_PWD] operate-LTEqual[<=]    是否需要修改密码:1=是 0=否")
	private Integer endIsUpdatePwd;


	@Comment("登录认证代码[LOGIN_AUTH_CODE] operate-Like[like]")
	private String loginAuthCode;


	@Comment("登录认证代码[LOGIN_AUTH_CODE] operate-Equal[=]")
	private String loginAuthCodeEqual;


	@Comment("登录认证代码[LOGIN_AUTH_CODE] operate-In[in]")
	private String[] loginAuthCodes;

	@Comment("即时通讯[IM_ACCOUNT]")
	private String imAccount;

	@Comment("即时通讯[IM_ACCOUNT]")
	private String imAccountEqual;

	@Comment("即时通讯[IM_ACCOUNT]")
	private String[] imAccounts;

	@Comment("备用_1[CUSTOM_1] operate-Like[like]")
	private String custom1;


	@Comment("备用_1[CUSTOM_1] operate-Equal[=]")
	private String custom1Equal;


	@Comment("备用_1[CUSTOM_1] operate-In[in]")
	private String[] custom1s;


	@Comment("备用_2[CUSTOM_2] operate-Like[like]")
	private String custom2;


	@Comment("备用_2[CUSTOM_2] operate-Equal[=]")
	private String custom2Equal;


	@Comment("备用_2[CUSTOM_2] operate-In[in]")
	private String[] custom2s;


	@Comment("备用_3[CUSTOM_3] operate-Like[like]    备用_3:第三方用户ID")
	private String custom3;


	@Comment("备用_3[CUSTOM_3] operate-Equal[=]    备用_3:第三方用户ID")
	private String custom3Equal;


	@Comment("备用_3[CUSTOM_3] operate-In[in]    备用_3:第三方用户ID")
	private String[] custom3s;


	@Comment("备用_4[CUSTOM_4] operate-Equal[=]")
	private Long custom4;


	@Comment("备用_4[CUSTOM_4] operate-In[in]")
	private Long[] custom4s;


	@Comment("备用_4[CUSTOM_4] operate-GTEqual[>=]")
	private Long startCustom4;

	@Comment("备用_4[CUSTOM_4] operate-LTEqual[<=]")
	private Long endCustom4;


	@Comment("备用_5[CUSTOM_5] operate-Equal[=]")
	private Long custom5;


	@Comment("备用_5[CUSTOM_5] operate-In[in]")
	private Long[] custom5s;


	@Comment("备用_5[CUSTOM_5] operate-GTEqual[>=]")
	private Long startCustom5;

	@Comment("备用_5[CUSTOM_5] operate-LTEqual[<=]")
	private Long endCustom5;


	@Comment("备用_6[CUSTOM_6] operate-Equal[=]")
	private Long custom6;


	@Comment("备用_6[CUSTOM_6] operate-In[in]")
	private Long[] custom6s;


	@Comment("备用_6[CUSTOM_6] operate-GTEqual[>=]")
	private Long startCustom6;

	@Comment("备用_6[CUSTOM_6] operate-LTEqual[<=]")
	private Long endCustom6;


	@Comment("状态[STATUS] operate-Equal[=]    状态:1-正常 0-停用")
	private Integer status;


	@Comment("状态[STATUS] operate-In[in]    状态:1-正常 0-停用")
	private Integer[] statuss;


	@Comment("状态[STATUS] operate-GTEqual[>=]    状态:1-正常 0-停用")
	private Integer startStatus;

	@Comment("状态[STATUS] operate-LTEqual[<=]    状态:1-正常 0-停用")
	private Integer endStatus;


	@Comment("所属域[DOMAIN_ID] operate-Equal[=]")
	private Long domainId;


	@Comment("所属域[DOMAIN_ID] operate-In[in]")
	private Long[] domainIds;


	@Comment("所属域[DOMAIN_ID] operate-GTEqual[>=]")
	private Long startDomainId;

	@Comment("所属域[DOMAIN_ID] operate-LTEqual[<=]")
	private Long endDomainId;


	@Comment("所属用户域[USER_DOMAIN_ID] operate-Equal[=]")
	private Long userDomainId;


	@Comment("所属用户域[USER_DOMAIN_ID] operate-In[in]")
	private Long[] userDomainIds;


	@Comment("所属用户域[USER_DOMAIN_ID] operate-GTEqual[>=]")
	private Long startUserDomainId;

	@Comment("所属用户域[USER_DOMAIN_ID] operate-LTEqual[<=]")
	private Long endUserDomainId;


	@Comment("数据状态[DATA_STATUS] operate-Equal[=]    数据状态:1-正常 0-删除")
	private Integer dataStatus;


	@Comment("数据状态[DATA_STATUS] operate-In[in]    数据状态:1-正常 0-删除")
	private Integer[] dataStatuss;


	@Comment("数据状态[DATA_STATUS] operate-GTEqual[>=]    数据状态:1-正常 0-删除")
	private Integer startDataStatus;

	@Comment("数据状态[DATA_STATUS] operate-LTEqual[<=]    数据状态:1-正常 0-删除")
	private Integer endDataStatus;


	@Comment("创建人[CREATOR] operate-Like[like]")
	private String creator;


	@Comment("创建人[CREATOR] operate-Equal[=]")
	private String creatorEqual;


	@Comment("创建人[CREATOR] operate-In[in]")
	private String[] creators;


	@Comment("修改人[MODIFIER] operate-Like[like]")
	private String modifier;


	@Comment("修改人[MODIFIER] operate-Equal[=]")
	private String modifierEqual;


	@Comment("修改人[MODIFIER] operate-In[in]")
	private String[] modifiers;


	@Comment("创建时间[CREATE_TIME] operate-Equal[=]    创建时间:yyyyMMddHHmmss")
	private Long createTime;


	@Comment("创建时间[CREATE_TIME] operate-In[in]    创建时间:yyyyMMddHHmmss")
	private Long[] createTimes;


	@Comment("创建时间[CREATE_TIME] operate-GTEqual[>=]    创建时间:yyyyMMddHHmmss")
	private Long startCreateTime;

	@Comment("创建时间[CREATE_TIME] operate-LTEqual[<=]    创建时间:yyyyMMddHHmmss")
	private Long endCreateTime;


	@Comment("修改时间[MODIFY_TIME] operate-Equal[=]    修改时间:yyyyMMddHHmmss")
	private Long modifyTime;


	@Comment("修改时间[MODIFY_TIME] operate-In[in]    修改时间:yyyyMMddHHmmss")
	private Long[] modifyTimes;


	@Comment("修改时间[MODIFY_TIME] operate-GTEqual[>=]    修改时间:yyyyMMddHHmmss")
	private Long startModifyTime;

	@Comment("修改时间[MODIFY_TIME] operate-LTEqual[<=]    修改时间:yyyyMMddHHmmss")
	private Long endModifyTime;




	public Long getId() {
		return this.id;
	}
	public void setId(Long id) {
		this.id = id;
	}


	public Long[] getIds() {
		return this.ids;
	}
	public void setIds(Long[] ids) {
		this.ids = ids;
	}


	public Long getStartId() {
		return this.startId;
	}
	public void setStartId(Long startId) {
		this.startId = startId;
	}


	public Long getEndId() {
		return this.endId;
	}
	public void setEndId(Long endId) {
		this.endId = endId;
	}


	public String getOpCode() {
		return this.opCode;
	}
	public void setOpCode(String opCode) {
		this.opCode = opCode;
	}


	public String getOpCodeEqual() {
		return this.opCodeEqual;
	}
	public void setOpCodeEqual(String opCodeEqual) {
		this.opCodeEqual = opCodeEqual;
	}


	public String[] getOpCodes() {
		return this.opCodes;
	}
	public void setOpCodes(String[] opCodes) {
		this.opCodes = opCodes;
	}


	public String getOpName() {
		return this.opName;
	}
	public void setOpName(String opName) {
		this.opName = opName;
	}


	public String getOpNameEqual() {
		return this.opNameEqual;
	}
	public void setOpNameEqual(String opNameEqual) {
		this.opNameEqual = opNameEqual;
	}


	public String[] getOpNames() {
		return this.opNames;
	}
	public void setOpNames(String[] opNames) {
		this.opNames = opNames;
	}


	public Integer getOpKind() {
		return this.opKind;
	}
	public void setOpKind(Integer opKind) {
		this.opKind = opKind;
	}


	public Integer[] getOpKinds() {
		return this.opKinds;
	}
	public void setOpKinds(Integer[] opKinds) {
		this.opKinds = opKinds;
	}


	public Integer getStartOpKind() {
		return this.startOpKind;
	}
	public void setStartOpKind(Integer startOpKind) {
		this.startOpKind = startOpKind;
	}


	public Integer getEndOpKind() {
		return this.endOpKind;
	}
	public void setEndOpKind(Integer endOpKind) {
		this.endOpKind = endOpKind;
	}


	public String getMobileNo() {
		return this.mobileNo;
	}
	public void setMobileNo(String mobileNo) {
		this.mobileNo = mobileNo;
	}


	public String getMobileNoEqual() {
		return this.mobileNoEqual;
	}
	public void setMobileNoEqual(String mobileNoEqual) {
		this.mobileNoEqual = mobileNoEqual;
	}


	public String[] getMobileNos() {
		return this.mobileNos;
	}
	public void setMobileNos(String[] mobileNos) {
		this.mobileNos = mobileNos;
	}


	public String getEmailAdress() {
		return this.emailAdress;
	}
	public void setEmailAdress(String emailAdress) {
		this.emailAdress = emailAdress;
	}


	public String getEmailAdressEqual() {
		return this.emailAdressEqual;
	}
	public void setEmailAdressEqual(String emailAdressEqual) {
		this.emailAdressEqual = emailAdressEqual;
	}


	public String[] getEmailAdresss() {
		return this.emailAdresss;
	}
	public void setEmailAdresss(String[] emailAdresss) {
		this.emailAdresss = emailAdresss;
	}


	public String getLoginCode() {
		return this.loginCode;
	}
	public void setLoginCode(String loginCode) {
		this.loginCode = loginCode;
	}


	public String getLoginCodeEqual() {
		return this.loginCodeEqual;
	}
	public void setLoginCodeEqual(String loginCodeEqual) {
		this.loginCodeEqual = loginCodeEqual;
	}


	public String[] getLoginCodes() {
		return this.loginCodes;
	}
	public void setLoginCodes(String[] loginCodes) {
		this.loginCodes = loginCodes;
	}


	public String getLoginPasswd() {
		return this.loginPasswd;
	}
	public void setLoginPasswd(String loginPasswd) {
		this.loginPasswd = loginPasswd;
	}


	public String getLoginPasswdEqual() {
		return this.loginPasswdEqual;
	}
	public void setLoginPasswdEqual(String loginPasswdEqual) {
		this.loginPasswdEqual = loginPasswdEqual;
	}


	public String[] getLoginPasswds() {
		return this.loginPasswds;
	}
	public void setLoginPasswds(String[] loginPasswds) {
		this.loginPasswds = loginPasswds;
	}


	public String getShortName() {
		return this.shortName;
	}
	public void setShortName(String shortName) {
		this.shortName = shortName;
	}


	public String getShortNameEqual() {
		return this.shortNameEqual;
	}
	public void setShortNameEqual(String shortNameEqual) {
		this.shortNameEqual = shortNameEqual;
	}


	public String[] getShortNames() {
		return this.shortNames;
	}
	public void setShortNames(String[] shortNames) {
		this.shortNames = shortNames;
	}


	public String getNotes() {
		return this.notes;
	}
	public void setNotes(String notes) {
		this.notes = notes;
	}


	public Integer getAllowChangePasswd() {
		return this.allowChangePasswd;
	}
	public void setAllowChangePasswd(Integer allowChangePasswd) {
		this.allowChangePasswd = allowChangePasswd;
	}


	public Integer[] getAllowChangePasswds() {
		return this.allowChangePasswds;
	}
	public void setAllowChangePasswds(Integer[] allowChangePasswds) {
		this.allowChangePasswds = allowChangePasswds;
	}


	public Integer getStartAllowChangePasswd() {
		return this.startAllowChangePasswd;
	}
	public void setStartAllowChangePasswd(Integer startAllowChangePasswd) {
		this.startAllowChangePasswd = startAllowChangePasswd;
	}


	public Integer getEndAllowChangePasswd() {
		return this.endAllowChangePasswd;
	}
	public void setEndAllowChangePasswd(Integer endAllowChangePasswd) {
		this.endAllowChangePasswd = endAllowChangePasswd;
	}


	public Long getLastLoginLogId() {
		return this.lastLoginLogId;
	}
	public void setLastLoginLogId(Long lastLoginLogId) {
		this.lastLoginLogId = lastLoginLogId;
	}


	public Long[] getLastLoginLogIds() {
		return this.lastLoginLogIds;
	}
	public void setLastLoginLogIds(Long[] lastLoginLogIds) {
		this.lastLoginLogIds = lastLoginLogIds;
	}


	public Long getStartLastLoginLogId() {
		return this.startLastLoginLogId;
	}
	public void setStartLastLoginLogId(Long startLastLoginLogId) {
		this.startLastLoginLogId = startLastLoginLogId;
	}


	public Long getEndLastLoginLogId() {
		return this.endLastLoginLogId;
	}
	public void setEndLastLoginLogId(Long endLastLoginLogId) {
		this.endLastLoginLogId = endLastLoginLogId;
	}


	public Long getLastLoginTime() {
		return this.lastLoginTime;
	}
	public void setLastLoginTime(Long lastLoginTime) {
		this.lastLoginTime = lastLoginTime;
	}


	public Long[] getLastLoginTimes() {
		return this.lastLoginTimes;
	}
	public void setLastLoginTimes(Long[] lastLoginTimes) {
		this.lastLoginTimes = lastLoginTimes;
	}


	public Long getStartLastLoginTime() {
		return this.startLastLoginTime;
	}
	public void setStartLastLoginTime(Long startLastLoginTime) {
		this.startLastLoginTime = startLastLoginTime;
	}


	public Long getEndLastLoginTime() {
		return this.endLastLoginTime;
	}
	public void setEndLastLoginTime(Long endLastLoginTime) {
		this.endLastLoginTime = endLastLoginTime;
	}


	public Integer getTryTimes() {
		return this.tryTimes;
	}
	public void setTryTimes(Integer tryTimes) {
		this.tryTimes = tryTimes;
	}


	public Integer[] getTryTimess() {
		return this.tryTimess;
	}
	public void setTryTimess(Integer[] tryTimess) {
		this.tryTimess = tryTimess;
	}


	public Integer getStartTryTimes() {
		return this.startTryTimes;
	}
	public void setStartTryTimes(Integer startTryTimes) {
		this.startTryTimes = startTryTimes;
	}


	public Integer getEndTryTimes() {
		return this.endTryTimes;
	}
	public void setEndTryTimes(Integer endTryTimes) {
		this.endTryTimes = endTryTimes;
	}


	public Integer getLoginFlag() {
		return this.loginFlag;
	}
	public void setLoginFlag(Integer loginFlag) {
		this.loginFlag = loginFlag;
	}


	public Integer[] getLoginFlags() {
		return this.loginFlags;
	}
	public void setLoginFlags(Integer[] loginFlags) {
		this.loginFlags = loginFlags;
	}


	public Integer getStartLoginFlag() {
		return this.startLoginFlag;
	}
	public void setStartLoginFlag(Integer startLoginFlag) {
		this.startLoginFlag = startLoginFlag;
	}


	public Integer getEndLoginFlag() {
		return this.endLoginFlag;
	}
	public void setEndLoginFlag(Integer endLoginFlag) {
		this.endLoginFlag = endLoginFlag;
	}


	public Integer getSuperUserFlag() {
		return this.superUserFlag;
	}
	public void setSuperUserFlag(Integer superUserFlag) {
		this.superUserFlag = superUserFlag;
	}


	public Integer[] getSuperUserFlags() {
		return this.superUserFlags;
	}
	public void setSuperUserFlags(Integer[] superUserFlags) {
		this.superUserFlags = superUserFlags;
	}


	public Integer getStartSuperUserFlag() {
		return this.startSuperUserFlag;
	}
	public void setStartSuperUserFlag(Integer startSuperUserFlag) {
		this.startSuperUserFlag = startSuperUserFlag;
	}


	public Integer getEndSuperUserFlag() {
		return this.endSuperUserFlag;
	}
	public void setEndSuperUserFlag(Integer endSuperUserFlag) {
		this.endSuperUserFlag = endSuperUserFlag;
	}


	public Long getPasswdValidDays() {
		return this.passwdValidDays;
	}
	public void setPasswdValidDays(Long passwdValidDays) {
		this.passwdValidDays = passwdValidDays;
	}


	public Long[] getPasswdValidDayss() {
		return this.passwdValidDayss;
	}
	public void setPasswdValidDayss(Long[] passwdValidDayss) {
		this.passwdValidDayss = passwdValidDayss;
	}


	public Long getStartPasswdValidDays() {
		return this.startPasswdValidDays;
	}
	public void setStartPasswdValidDays(Long startPasswdValidDays) {
		this.startPasswdValidDays = startPasswdValidDays;
	}


	public Long getEndPasswdValidDays() {
		return this.endPasswdValidDays;
	}
	public void setEndPasswdValidDays(Long endPasswdValidDays) {
		this.endPasswdValidDays = endPasswdValidDays;
	}


	public Long getLockedTime() {
		return this.lockedTime;
	}
	public void setLockedTime(Long lockedTime) {
		this.lockedTime = lockedTime;
	}


	public Long[] getLockedTimes() {
		return this.lockedTimes;
	}
	public void setLockedTimes(Long[] lockedTimes) {
		this.lockedTimes = lockedTimes;
	}


	public Long getStartLockedTime() {
		return this.startLockedTime;
	}
	public void setStartLockedTime(Long startLockedTime) {
		this.startLockedTime = startLockedTime;
	}


	public Long getEndLockedTime() {
		return this.endLockedTime;
	}
	public void setEndLockedTime(Long endLockedTime) {
		this.endLockedTime = endLockedTime;
	}


	public Integer getLockFlag() {
		return this.lockFlag;
	}
	public void setLockFlag(Integer lockFlag) {
		this.lockFlag = lockFlag;
	}


	public Integer[] getLockFlags() {
		return this.lockFlags;
	}
	public void setLockFlags(Integer[] lockFlags) {
		this.lockFlags = lockFlags;
	}


	public Integer getStartLockFlag() {
		return this.startLockFlag;
	}
	public void setStartLockFlag(Integer startLockFlag) {
		this.startLockFlag = startLockFlag;
	}


	public Integer getEndLockFlag() {
		return this.endLockFlag;
	}
	public void setEndLockFlag(Integer endLockFlag) {
		this.endLockFlag = endLockFlag;
	}


	public Integer getIsUpdatePwd() {
		return this.isUpdatePwd;
	}
	public void setIsUpdatePwd(Integer isUpdatePwd) {
		this.isUpdatePwd = isUpdatePwd;
	}


	public Integer[] getIsUpdatePwds() {
		return this.isUpdatePwds;
	}
	public void setIsUpdatePwds(Integer[] isUpdatePwds) {
		this.isUpdatePwds = isUpdatePwds;
	}


	public Integer getStartIsUpdatePwd() {
		return this.startIsUpdatePwd;
	}
	public void setStartIsUpdatePwd(Integer startIsUpdatePwd) {
		this.startIsUpdatePwd = startIsUpdatePwd;
	}


	public Integer getEndIsUpdatePwd() {
		return this.endIsUpdatePwd;
	}
	public void setEndIsUpdatePwd(Integer endIsUpdatePwd) {
		this.endIsUpdatePwd = endIsUpdatePwd;
	}


	public String getLoginAuthCode() {
		return this.loginAuthCode;
	}
	public void setLoginAuthCode(String loginAuthCode) {
		this.loginAuthCode = loginAuthCode;
	}


	public String getLoginAuthCodeEqual() {
		return this.loginAuthCodeEqual;
	}
	public void setLoginAuthCodeEqual(String loginAuthCodeEqual) {
		this.loginAuthCodeEqual = loginAuthCodeEqual;
	}


	public String[] getLoginAuthCodes() {
		return this.loginAuthCodes;
	}
	public void setLoginAuthCodes(String[] loginAuthCodes) {
		this.loginAuthCodes = loginAuthCodes;
	}

	public String getImAccount() {
		return imAccount;
	}

	public void setImAccount(String imAccount) {
		this.imAccount = imAccount;
	}

	public String getImAccountEqual() {
		return imAccountEqual;
	}

	public void setImAccountEqual(String imAccountEqual) {
		this.imAccountEqual = imAccountEqual;
	}

	public String[] getImAccounts() {
		return imAccounts;
	}

	public void setImAccounts(String[] imAccounts) {
		this.imAccounts = imAccounts;
	}

	public String getCustom1() {
		return this.custom1;
	}
	public void setCustom1(String custom1) {
		this.custom1 = custom1;
	}


	public String getCustom1Equal() {
		return this.custom1Equal;
	}
	public void setCustom1Equal(String custom1Equal) {
		this.custom1Equal = custom1Equal;
	}


	public String[] getCustom1s() {
		return this.custom1s;
	}
	public void setCustom1s(String[] custom1s) {
		this.custom1s = custom1s;
	}


	public String getCustom2() {
		return this.custom2;
	}
	public void setCustom2(String custom2) {
		this.custom2 = custom2;
	}


	public String getCustom2Equal() {
		return this.custom2Equal;
	}
	public void setCustom2Equal(String custom2Equal) {
		this.custom2Equal = custom2Equal;
	}


	public String[] getCustom2s() {
		return this.custom2s;
	}
	public void setCustom2s(String[] custom2s) {
		this.custom2s = custom2s;
	}


	public String getCustom3() {
		return this.custom3;
	}
	public void setCustom3(String custom3) {
		this.custom3 = custom3;
	}


	public String getCustom3Equal() {
		return this.custom3Equal;
	}
	public void setCustom3Equal(String custom3Equal) {
		this.custom3Equal = custom3Equal;
	}


	public String[] getCustom3s() {
		return this.custom3s;
	}
	public void setCustom3s(String[] custom3s) {
		this.custom3s = custom3s;
	}


	public Long getCustom4() {
		return this.custom4;
	}
	public void setCustom4(Long custom4) {
		this.custom4 = custom4;
	}


	public Long[] getCustom4s() {
		return this.custom4s;
	}
	public void setCustom4s(Long[] custom4s) {
		this.custom4s = custom4s;
	}


	public Long getStartCustom4() {
		return this.startCustom4;
	}
	public void setStartCustom4(Long startCustom4) {
		this.startCustom4 = startCustom4;
	}


	public Long getEndCustom4() {
		return this.endCustom4;
	}
	public void setEndCustom4(Long endCustom4) {
		this.endCustom4 = endCustom4;
	}


	public Long getCustom5() {
		return this.custom5;
	}
	public void setCustom5(Long custom5) {
		this.custom5 = custom5;
	}


	public Long[] getCustom5s() {
		return this.custom5s;
	}
	public void setCustom5s(Long[] custom5s) {
		this.custom5s = custom5s;
	}


	public Long getStartCustom5() {
		return this.startCustom5;
	}
	public void setStartCustom5(Long startCustom5) {
		this.startCustom5 = startCustom5;
	}


	public Long getEndCustom5() {
		return this.endCustom5;
	}
	public void setEndCustom5(Long endCustom5) {
		this.endCustom5 = endCustom5;
	}


	public Long getCustom6() {
		return this.custom6;
	}
	public void setCustom6(Long custom6) {
		this.custom6 = custom6;
	}


	public Long[] getCustom6s() {
		return this.custom6s;
	}
	public void setCustom6s(Long[] custom6s) {
		this.custom6s = custom6s;
	}


	public Long getStartCustom6() {
		return this.startCustom6;
	}
	public void setStartCustom6(Long startCustom6) {
		this.startCustom6 = startCustom6;
	}


	public Long getEndCustom6() {
		return this.endCustom6;
	}
	public void setEndCustom6(Long endCustom6) {
		this.endCustom6 = endCustom6;
	}


	public Integer getStatus() {
		return this.status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}


	public Integer[] getStatuss() {
		return this.statuss;
	}
	public void setStatuss(Integer[] statuss) {
		this.statuss = statuss;
	}


	public Integer getStartStatus() {
		return this.startStatus;
	}
	public void setStartStatus(Integer startStatus) {
		this.startStatus = startStatus;
	}


	public Integer getEndStatus() {
		return this.endStatus;
	}
	public void setEndStatus(Integer endStatus) {
		this.endStatus = endStatus;
	}


	public Long getDomainId() {
		return this.domainId;
	}
	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}


	public Long[] getDomainIds() {
		return this.domainIds;
	}
	public void setDomainIds(Long[] domainIds) {
		this.domainIds = domainIds;
	}


	public Long getStartDomainId() {
		return this.startDomainId;
	}
	public void setStartDomainId(Long startDomainId) {
		this.startDomainId = startDomainId;
	}


	public Long getEndDomainId() {
		return this.endDomainId;
	}
	public void setEndDomainId(Long endDomainId) {
		this.endDomainId = endDomainId;
	}


	public Long getUserDomainId() {
		return this.userDomainId;
	}
	public void setUserDomainId(Long userDomainId) {
		this.userDomainId = userDomainId;
	}


	public Long[] getUserDomainIds() {
		return this.userDomainIds;
	}
	public void setUserDomainIds(Long[] userDomainIds) {
		this.userDomainIds = userDomainIds;
	}


	public Long getStartUserDomainId() {
		return this.startUserDomainId;
	}
	public void setStartUserDomainId(Long startUserDomainId) {
		this.startUserDomainId = startUserDomainId;
	}


	public Long getEndUserDomainId() {
		return this.endUserDomainId;
	}
	public void setEndUserDomainId(Long endUserDomainId) {
		this.endUserDomainId = endUserDomainId;
	}


	public Integer getDataStatus() {
		return this.dataStatus;
	}
	public void setDataStatus(Integer dataStatus) {
		this.dataStatus = dataStatus;
	}


	public Integer[] getDataStatuss() {
		return this.dataStatuss;
	}
	public void setDataStatuss(Integer[] dataStatuss) {
		this.dataStatuss = dataStatuss;
	}


	public Integer getStartDataStatus() {
		return this.startDataStatus;
	}
	public void setStartDataStatus(Integer startDataStatus) {
		this.startDataStatus = startDataStatus;
	}


	public Integer getEndDataStatus() {
		return this.endDataStatus;
	}
	public void setEndDataStatus(Integer endDataStatus) {
		this.endDataStatus = endDataStatus;
	}


	public String getCreator() {
		return this.creator;
	}
	public void setCreator(String creator) {
		this.creator = creator;
	}


	public String getCreatorEqual() {
		return this.creatorEqual;
	}
	public void setCreatorEqual(String creatorEqual) {
		this.creatorEqual = creatorEqual;
	}


	public String[] getCreators() {
		return this.creators;
	}
	public void setCreators(String[] creators) {
		this.creators = creators;
	}


	public String getModifier() {
		return this.modifier;
	}
	public void setModifier(String modifier) {
		this.modifier = modifier;
	}


	public String getModifierEqual() {
		return this.modifierEqual;
	}
	public void setModifierEqual(String modifierEqual) {
		this.modifierEqual = modifierEqual;
	}


	public String[] getModifiers() {
		return this.modifiers;
	}
	public void setModifiers(String[] modifiers) {
		this.modifiers = modifiers;
	}


	public Long getCreateTime() {
		return this.createTime;
	}
	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}


	public Long[] getCreateTimes() {
		return this.createTimes;
	}
	public void setCreateTimes(Long[] createTimes) {
		this.createTimes = createTimes;
	}


	public Long getStartCreateTime() {
		return this.startCreateTime;
	}
	public void setStartCreateTime(Long startCreateTime) {
		this.startCreateTime = startCreateTime;
	}


	public Long getEndCreateTime() {
		return this.endCreateTime;
	}
	public void setEndCreateTime(Long endCreateTime) {
		this.endCreateTime = endCreateTime;
	}


	public Long getModifyTime() {
		return this.modifyTime;
	}
	public void setModifyTime(Long modifyTime) {
		this.modifyTime = modifyTime;
	}


	public Long[] getModifyTimes() {
		return this.modifyTimes;
	}
	public void setModifyTimes(Long[] modifyTimes) {
		this.modifyTimes = modifyTimes;
	}


	public Long getStartModifyTime() {
		return this.startModifyTime;
	}
	public void setStartModifyTime(Long startModifyTime) {
		this.startModifyTime = startModifyTime;
	}


	public Long getEndModifyTime() {
		return this.endModifyTime;
	}
	public void setEndModifyTime(Long endModifyTime) {
		this.endModifyTime = endModifyTime;
	}


}


