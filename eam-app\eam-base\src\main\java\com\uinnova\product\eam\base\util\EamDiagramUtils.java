package com.uinnova.product.eam.base.util;

import cn.hutool.crypto.SecureUtil;
import com.binary.core.util.BinaryUtils;
import com.binary.json.JSON;
import com.uinnova.product.eam.base.diagram.model.*;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 视图常用方法工具类
 * <AUTHOR>
 * @date 2021/11/3
 */
public class EamDiagramUtils {

    /**
     * 取出视图中ci信息
     * @param diagrams
     * @return
     */
    public static Set<String> getDiagramCiList(List<ESDiagramInfoDTO> diagrams){
        Set<String> ciCodes = new HashSet<>();
        for (ESDiagramInfoDTO diagram : diagrams) {
            for(ESDiagramModel model : diagram.getModelList()){
                if(BinaryUtils.isEmpty(model.getNodeDataArray())){
                    continue;
                }
                List<String> ciList = model.getNodeDataArray().stream().map(ESDiagramNode::getCiCode).filter(Objects::nonNull).distinct().collect(Collectors.toList());
                if(BinaryUtils.isEmpty(ciList)){
                    continue;
                }
                ciCodes.addAll(ciList);
            }
        }
        return ciCodes;
    }

    /**
     * 取出视图中rlt信息
     * @param diagrams
     * @return
     */
    public static Set<String> getDiagramRltList(List<ESDiagramInfoDTO> diagrams){
        Set<String> rltCodes = new HashSet<>();
        for (ESDiagramInfoDTO diagram : diagrams) {
            for(ESDiagramModel model : diagram.getModelList()){
                List<ESDiagramLink> nodeDataArray = model.getLinkDataArray();
                List<String> rltList = getRltList(nodeDataArray);
                rltCodes.addAll(rltList);
            }
        }
        return rltCodes;
    }

    /**
     * 从视图信息中link数组中取出关系的rltCode
     * @param linkDataArray
     * @return
     */
    public static List<String> getRltList(List<ESDiagramLink> linkDataArray){
        List<String> rltCodes = new ArrayList<>();
        if (!CollectionUtils.isEmpty(linkDataArray)) {
            for (ESDiagramLink node : linkDataArray){
                Map map = JSON.toObject(node.getLinkJson(), Map.class);
                if(BinaryUtils.isEmpty(map.get("rltCode"))){
                    continue;
                }
                rltCodes.add(String.valueOf(map.get("rltCode")));
            }
        }
        return rltCodes;
    }

    /**
     * 设置复制视图信息
     * @param diagram 视图信息
     * @param dirCode 文件目录code
     * @param open 视图发布状态
     * @return
     */
    public static ESDiagramMoveCdt setDiagramCopyCdt(ESDiagramInfoDTO diagram, Long dirCode, Integer open){
        ESDiagramMoveCdt cdt = new ESDiagramMoveCdt();
        cdt.setDiagramId(diagram.getDEnergy());
        cdt.setDirType(diagram.getDirType());
        cdt.setNewDirId(dirCode);
        cdt.setNewName(diagram.getName());
        cdt.setViewType(diagram.getViewType());
        cdt.setIsOpen(open);
        cdt.setReleaseDiagramId(diagram.getDEnergy());
        return cdt;
    }

    /**
     * 获取加密视图id
     * @param diagramId
     * @return
     */
    public static String getMd5DiagramId(Long diagramId){
        return SecureUtil.md5(String.valueOf(diagramId)).substring(8, 24);
    }
}
