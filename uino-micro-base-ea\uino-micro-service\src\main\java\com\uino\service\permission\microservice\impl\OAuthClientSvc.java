package com.uino.service.permission.microservice.impl;

import java.util.List;

import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import com.binary.core.encrypt.Encrypt;
import com.binary.core.util.BinaryUtils;
import com.uino.dao.permission.ESOauthClientDetailSvc;
import com.uino.service.permission.microservice.IOAuthClientSvc;
import com.uino.util.sys.BeanUtil;
import com.uino.util.sys.ValidDtoUtil;
import com.uino.bean.permission.base.OAuthClientDetail;
import com.uino.bean.permission.business.request.RegisterClientReq;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
public class OAuthClientSvc implements IOAuthClientSvc {
	@Autowired
	private ESOauthClientDetailSvc clientSvc;

	@Override
	public OAuthClientDetail getClientInfoByCode(String clientCode) {
		Assert.notNull(clientCode, "客户端code不得为空");
		OAuthClientDetail client = null;
		List<OAuthClientDetail> clients = clientSvc
				.getListByQuery(QueryBuilders.termQuery("clientCode.keyword", clientCode));
        if (!BinaryUtils.isEmpty(clients)) {
            client = clients.get(0);
        }
		return client;
	}

	@Override
	public void register(RegisterClientReq req) {
		// 验证基础数据
		ValidDtoUtil.valid(req);
		// 康康是否客户端已经注册过，注册过则视为修改-其实就是直接用这个id了
		OAuthClientDetail validClient = getClientInfoByCode(req.getClientCode());
		// 注册
		OAuthClientDetail saveDto = OAuthClientDetail.builder().build();
		BeanUtil.copyProperties(req, saveDto);
        if (validClient != null) {
            saveDto.setId(validClient.getId());
        } else {
            saveDto.setClientSecret(Encrypt.encrypt(saveDto.getClientCode()));
        }
		clientSvc.saveOrUpdate(saveDto);
	}

	@Override
	public void removeClientInfoByCode(String clientCode) {
		Assert.notNull(clientCode, "clientcode_notnull");
		clientSvc.deleteByQuery(QueryBuilders.termQuery("clientCode.keyword", clientCode), true);
	}

	@Override
	public List<OAuthClientDetail> getAllClient() {
		List<OAuthClientDetail> res = clientSvc.getListByQuery(QueryBuilders.boolQuery());
		return res;
	}
}
