package com.uino.service.cmdb.dataset.microservice;

import com.uino.bean.cmdb.base.dataset.DataSetMallApiRelationRuleExternal;
import com.uino.bean.cmdb.business.dataset.FriendInfo;
import com.uino.bean.cmdb.business.dataset.UpDownAttrCdt;

import java.util.List;

/**
 * @Classname IGraphAnalysisSvc
 * @Description 上下多少层
 * @Date 2020/7/16 9:52
 * <AUTHOR> sh
 */
public interface IGraphAnalysisSvc {

    /**
     * 上下N层(多入口CI)
     *
     * @param domainId        	domainId
     * @param startCiIds        起始ciIds
     * @param ciConditions		CI过滤条件
     * @param rltConditions		关系过滤条件
     * @param rltLvls			关系子CODE过滤条件
     * @param upLevel			上几层
     * @param downLevel			下几层
     * @param hasAttr			是否需要属性
     * @return FriendInfo
     */
    public FriendInfo queryCiUpDownByCiIds(Long domainId, List<Long> startCiIds, List<UpDownAttrCdt> ciConditions, List<UpDownAttrCdt> rltConditions,
                                           List<Long> rltLvls, Integer upLevel, Integer downLevel, Boolean hasAttr);

    /**
     * 上下N层(单入口CI)
     *
     * @param domainId        	domainId
     * @param startCiId         起始ciId
     * @param ciConditions		CI过滤条件
     * @param rltConditions		关系过滤条件
     * @param rltLvls			关系子CODE过滤条件
     * @param upLevel			上几层
     * @param downLevel			下几层
     * @param hasAttr			是否需要属性
     * @return FriendInfo
     */
    public FriendInfo queryCiUpDownByCiId(Long domainId, Long startCiId, List<UpDownAttrCdt> ciConditions, List<UpDownAttrCdt> rltConditions,
                                          List<Long> rltLvls, Integer upLevel, Integer downLevel, Boolean hasAttr);
    
    /**
     * 通过关系遍历规则查询数据
     *
     * @param enter         起始ci
     * @param rule			关系遍历规则
     * @return FriendInfo
     */
    public FriendInfo queryFriendByCiUsingRule(List<String> enter, DataSetMallApiRelationRuleExternal rule);
}
