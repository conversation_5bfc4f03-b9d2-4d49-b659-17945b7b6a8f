package com.uinnova.product.eam.web.asset.bean;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

@Data
public class HtAppSystemTaskVo {

    @Comment("taskId")
    private String taskId;

    @Comment("业务主键")
    private String ciCode;

    @Comment("分类ID")
    private Long classId;

    @Comment("变更数据")
    private String data;

    @Comment("任务类型")
    private String type = "CHANGE_APP_SYSTEM";

    @Comment("审批动作 true=同意 false=驳回")
    private Boolean isAgree;

    @Comment("应用系统名称")
    private String systemName;

    @Comment("视图发布ID")
    private String diagramId;

    @Comment("视图版本号")
    private Integer diagramVersion;

    @Comment("制品ID")
    private Long articleId;

    @Comment("模糊查询值")
    private String like;

    @Comment("代办1已办2")
    private Integer isAlready;

    @Comment("变更记录")
    private String remarks;

    private Integer pageNum;

    private Integer pageSize;

}
