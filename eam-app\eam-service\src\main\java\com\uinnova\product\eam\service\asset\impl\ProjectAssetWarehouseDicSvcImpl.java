package com.uinnova.product.eam.service.asset.impl;

import com.binary.core.exception.BinaryException;
import com.binary.core.util.BinaryUtils;
import com.uinnova.product.eam.base.diagram.model.ESDiagram;
import com.uinnova.product.eam.comm.model.es.AssetWarehouseDir;
import com.uinnova.product.eam.comm.model.es.EamCategory;
import com.uinnova.product.eam.comm.model.es.EamMatrixInstance;
import com.uinnova.product.eam.model.AssetWarehouseDirVo;
import com.uinnova.product.eam.model.enums.AssetType;
import com.uinnova.product.eam.model.enums.CategoryTypeEnum;
import com.uinnova.product.eam.service.EamCategorySvc;
import com.uinnova.product.eam.service.asset.ProjectAssetWarehouseDicSvc;
import com.uinnova.product.eam.service.diagram.ESDiagramSvc;
import com.uinnova.product.eam.service.es.AssetWarehouseDirDao;
import com.uinnova.product.eam.service.manage.EamMatrixInstanceSvc;
import com.uino.bean.cmdb.base.LibType;
import com.uino.bean.permission.base.SysRole;
import com.uino.dao.permission.ESRoleSvc;
import com.uino.util.sys.BeanUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 资产仓库目录
 * <p>
 *
 * @autor szq
 * @date 2025/01/09
 */
@Slf4j
@Service
public class ProjectAssetWarehouseDicSvcImpl implements ProjectAssetWarehouseDicSvc {


    @Autowired
    private EamCategorySvc categorySvc;

    @Autowired
    private EamMatrixInstanceSvc instanceSvc;


    @Autowired
    private ESDiagramSvc diagramApiClient;

    @Autowired
    private AssetWarehouseDirDao assetWarehouseDirDao;

    @Autowired
    private ESRoleSvc roleSvc;
    @Override
    public List<AssetWarehouseDirVo> getPublishDirTree(AssetType assetType, Long userId, String assetId) {
        List<AssetWarehouseDirVo> result = new ArrayList<>();
        List<AssetWarehouseDir> assetWarehouseDirs = assetWarehouseDirDao.getListByQuery(null);
        if (BinaryUtils.isEmpty(userId)) {
            // 返回所有目录树
            listToDirTree(result, assetWarehouseDirs);
        } else {
            // 返回用户具有权限的目录树
            List<SysRole> roles = roleSvc.getListByUserId(userId);
            if (!BinaryUtils.isEmpty(roles)) {
                // 找出这些角色有的模块权限加进去
                List<Long> roleIds = roles.stream().map(SysRole::getId).collect(Collectors.toList());
                Iterator<AssetWarehouseDir> iterator = assetWarehouseDirs.iterator();
                while (iterator.hasNext()) {
                    AssetWarehouseDir next = iterator.next();
                    if (next.getModuleType().equals(3)) {
                        if (!containsAny(next.getRoleIdList(), roleIds)) {
                            iterator.remove();
                            continue;
                        }
                        //关联视图位置查询 资产类型为空
                        if (!BinaryUtils.isEmpty(assetType)) {
                            //获取资产类型ID
                            Long assetTypeId = getAssetTypeId(assetType, assetId);
                            //判断是否移除目录
                            if (!categorySvc.checkAssetType(next, assetType, assetId, assetTypeId)) {
                                iterator.remove();
                            }
                        }
                    }
                }
            }

            listToDirTree(result, assetWarehouseDirs);
        }
        // 排序
        sortDirTree(result);
        return result;
    }

    /**
     * 获取资产类型
     * @param assetType 资产类型
     * @param assetId 资产id
     * @return LONG 资产类型ID
     */
    private Long getAssetTypeId(AssetType assetType, String assetId){
        //交付物模版
        if (assetType == AssetType.TEMPLATE) {
            return -1L;
        }
        //方案
        if (assetType == AssetType.SCHEME) {
            return Long.valueOf(assetId);
        }
        //模型
        if (assetType == AssetType.DIAGRAM) {
            ESDiagram diagram = queryDiagram(assetId);
            LibType libType = diagram.getIsOpen() == 0 ? LibType.PRIVATE : LibType.DESIGN;
            EamCategory category = categorySvc.getById(diagram.getDirId(), libType);
            if (category != null && category.getType() == CategoryTypeEnum.MODEL.val()) {
                //是模型MODEL 直接返回
                return category.getModelId();
            }
            //视图
            if (diagram.getDiagramSubType() != null) {
                //自由视图
                if (diagram.getDiagramSubType() == 1) {
                    return 0L;
                }
                //制品视图
                if (diagram.getDiagramSubType() == 2) {
                    //返回制品id
                    return Long.valueOf(diagram.getViewType());
                }
            }
        }
        // 矩阵
        if (assetType == AssetType.MATRIX) {
            EamMatrixInstance instance = instanceSvc.getBaseInfoById(Long.valueOf(assetId), LibType.PRIVATE);
            return instance.getMatrixId();
        }
        return -1L;
    }

    private ESDiagram queryDiagram(String dEnergy) {
        String[] diagramIds = new String[]{dEnergy};
        List<ESDiagram> diagrams = diagramApiClient.queryDBDiagramInfoByIds(diagramIds);
        if (CollectionUtils.isEmpty(diagrams)) {
            throw new BinaryException("未找到视图");
        }
        return diagrams.get(0);
    }

    private void listToDirTree(List<AssetWarehouseDirVo> result, List<AssetWarehouseDir> assetWarehouseDirs) {
        // 过滤不包换页面的分组
        List<AssetWarehouseDir> assetPageList = assetWarehouseDirs.stream().filter(assetWarehouseDir -> assetWarehouseDir.getModuleType().equals(3)).collect(Collectors.toList());
        Map<Long, AssetWarehouseDir> dirIdMap = assetWarehouseDirs.stream().collect(Collectors.toMap(AssetWarehouseDir::getId, e -> e, (k1, k2) -> k1));
        List<AssetWarehouseDir> dirArrayList = new ArrayList<>(assetPageList);
        // 寻找分组到根目录
        for (AssetWarehouseDir assetWarehouseDir : assetPageList) {
            getParentDir(dirArrayList, dirIdMap, assetWarehouseDir);
        }
        Map<Long, List<AssetWarehouseDir>> parentIdGroup = dirArrayList.stream().collect(Collectors.groupingBy(AssetWarehouseDir::getParentId));
        List<AssetWarehouseDir> dirList = parentIdGroup.get(0L);
        if (!CollectionUtils.isEmpty(dirList)) {
            for (AssetWarehouseDir assetWarehouseDir : dirList) {
                AssetWarehouseDirVo target = new AssetWarehouseDirVo();
                BeanUtil.copyProperties(assetWarehouseDir, target);
                fillChildren(parentIdGroup, target);
                result.add(target);
            }
        }
    }

    private void getParentDir(List<AssetWarehouseDir> dirArrayList, Map<Long, AssetWarehouseDir> dirIdMap, AssetWarehouseDir assetWarehouseDir) {
        AssetWarehouseDir dir = dirIdMap.get(assetWarehouseDir.getParentId());
        if (!BinaryUtils.isEmpty(dir)) {
            getParentDir(dirArrayList, dirIdMap, dir);
            if (!dirArrayList.contains(dir)) {
                dirArrayList.add(dir);
            }
        }
    }


    private void fillChildren(Map<Long, List<AssetWarehouseDir>> parentIdGroup, AssetWarehouseDirVo target) {
        Long parentNodeId = target.getId();
        List<AssetWarehouseDir> childrenList = parentIdGroup.get(parentNodeId);
        if (!BinaryUtils.isEmpty(childrenList)) {
            target.setChildren(BeanUtil.converBean(childrenList, AssetWarehouseDirVo.class));
            for (AssetWarehouseDirVo dirVo : target.getChildren()) {
                fillChildren(parentIdGroup, dirVo);
            }
        }
    }

    private boolean containsAny(List<?> list1, List<?> list2) {
        if (CollectionUtils.isEmpty(list1) || CollectionUtils.isEmpty(list2)) {
            return false;
        }
        return !Collections.disjoint(list1, list2);
    }

    private void sortDirTree(List<AssetWarehouseDirVo> result) {
        if (CollectionUtils.isEmpty(result)) {
            return;
        }
        result.sort(Comparator.comparing(AssetWarehouseDir::getOrderNo));
        for (AssetWarehouseDirVo assetWarehouseDirVo : result) {
            sortDirTree(assetWarehouseDirVo.getChildren());
        }
    }
}
