package com.uinnova.product.eam.model.vo;

import com.binary.framework.bean.annotation.Comment;
import com.uino.bean.cmdb.query.ESRltSearchBean;
import lombok.Data;

import java.util.Set;

@Data
public class ESRltSearchBeanVO extends ESRltSearchBean {
    @Comment("范围查询 大于等于这个时间创建的数据")
    private String gteTime;
    @Comment("范围查询 小于等于这个时间创建的数据")
    private String lteTime;
    @Comment("是否是不合规模式 0 表示不合规查询 1表示全部查询")
    private Integer usage=1;

    //查询完的时候支持选几个导出
    private Set<Long> rltId;

}
