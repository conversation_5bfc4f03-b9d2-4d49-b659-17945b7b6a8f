package com.uinnova.product.eam.comm.dto;

import com.binary.framework.bean.annotation.Comment;
import com.uino.bean.cmdb.base.LibType;
import lombok.Data;
import java.io.Serializable;

/**
 * 矩阵制品查询dto
 */
@Data
public class EamMatrixQueryVO implements Serializable {

    @Comment("id")
    private Long id;

    @Comment("名称")
    private String name;

    @Comment("矩阵制品分类")
    private Integer type;

    @Comment("是否已发布: 1已发布,0未发布")
    private Integer published;

    @Comment("逻辑删除状态StatusConstant：1正常,0 已删除")
    private Integer status;

    @Comment("每页条数")
    private Integer pageSize = 1000;

    @Comment("页码")
    private Integer pageNum = 1;

    @Comment("libType")
    private LibType libType = LibType.PRIVATE;

}
