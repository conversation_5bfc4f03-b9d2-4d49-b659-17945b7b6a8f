package com.uinnova.product.eam.model.vo;

import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class C2CTreeNodeDto implements Comparable<C2CTreeNodeDto> {
    private Long id;
    private String name;
    private String type;
    private String ciCode;
    private CcCiInfo ciInfo;
    private Map<String, String> listMap;
    private List<C2CTreeNodeDto> children;
    private Integer ciCount;

    @Override
    public int compareTo(C2CTreeNodeDto o) {
        return this.getId().compareTo(o.getId());
    }
}
