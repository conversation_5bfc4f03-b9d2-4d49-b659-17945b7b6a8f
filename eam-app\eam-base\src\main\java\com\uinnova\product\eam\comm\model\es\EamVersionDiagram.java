package com.uinnova.product.eam.comm.model.es;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

/**
 * 版本标签视图信息
 * <AUTHOR>
 */
@Data
public class EamVersionDiagram {

    @Comment("视图id")
    private String diagramId;

    @Comment("视图版本")
    private Integer version;

    public EamVersionDiagram(String diagramId, Integer version) {
        this.diagramId = diagramId;
        this.version = version;
    }
}
