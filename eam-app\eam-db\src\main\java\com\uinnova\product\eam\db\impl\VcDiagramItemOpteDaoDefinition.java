package com.uinnova.product.eam.db.impl;


import com.binary.framework.dao.DaoDefinition;

import com.uinnova.product.eam.comm.model.CVcDiagramItemOpte;
import com.uinnova.product.eam.comm.model.VcDiagramItemOpte;


/**
 * 视图图标操作表[VC_DIAGRAM_ITEM_OPTE]数据访问对象定义实现
 */
public class VcDiagramItemOpteDaoDefinition implements DaoDefinition<VcDiagramItemOpte, CVcDiagramItemOpte> {


	@Override
	public Class<VcDiagramItemOpte> getEntityClass() {
		return VcDiagramItemOpte.class;
	}


	@Override
	public Class<CVcDiagramItemOpte> getConditionClass() {
		return CVcDiagramItemOpte.class;
	}


	@Override
	public String getTableName() {
		return "VC_DIAGRAM_ITEM_OPTE";
	}


	@Override
	public boolean hasDataStatusField() {
		return true;
	}


	@Override
	public void setDataStatusValue(VcDiagramItemOpte record, int status) {
		record.setDataStatus(status);
	}


	@Override
	public void setDataStatusValue(CVcDiagramItemOpte cdt, int status) {
		cdt.setDataStatus(status);
	}


	@Override
	public void setCreatorValue(VcDiagramItemOpte record, String creator) {
		record.setCreator(creator);
	}


	@Override
	public void setModifierValue(VcDiagramItemOpte record, String modifier) {
		record.setModifier(modifier);
	}


}


