package com.uinnova.product.eam.comm.model;




import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;


@Comment("节点链路表[VC_NODE_LINKED]")
public class VcNodeLinked implements EntityBean {
	private static final long serialVersionUID = 1L;


	@Comment("ID[ID]")
	private Long id;


	@Comment("链路名称[LINKED_NAME]")
	private String linkedName;


	@Comment("开始节点代码[START_CODE]")
	private String startCode;


	@Comment("结束节点代码[END_CODE]")
	private String endCode;


	@Comment("链路描述[LINKED_DESC]")
	private String linkedDesc;


	@Comment("关联应用名称[RELATED_APP_NAME]")
	private String relatedAppName;


	@Comment("所属域[DOMAIN_ID]")
	private Long domainId;


	@Comment("数据状态[DATA_STATUS]    数据状态:数据状态：1-正常 0-删除")
	private Integer dataStatus;


	@Comment("创建人[CREATOR]")
	private String creator;


	@Comment("修改人[MODIFIER]")
	private String modifier;


	@Comment("创建时间[CREATE_TIME]    创建时间:yyyyMMddHHmmss")
	private Long createTime;


	@Comment("修改时间[MODIFY_TIME]    修改时间:yyyyMMddHHmmss")
	private Long modifyTime;




	public Long getId() {
		return this.id;
	}
	public void setId(Long id) {
		this.id = id;
	}


	public String getLinkedName() {
		return this.linkedName;
	}
	public void setLinkedName(String linkedName) {
		this.linkedName = linkedName;
	}


	public String getStartCode() {
		return this.startCode;
	}
	public void setStartCode(String startCode) {
		this.startCode = startCode;
	}


	public String getEndCode() {
		return this.endCode;
	}
	public void setEndCode(String endCode) {
		this.endCode = endCode;
	}


	public String getLinkedDesc() {
		return this.linkedDesc;
	}
	public void setLinkedDesc(String linkedDesc) {
		this.linkedDesc = linkedDesc;
	}


	public String getRelatedAppName() {
		return this.relatedAppName;
	}
	public void setRelatedAppName(String relatedAppName) {
		this.relatedAppName = relatedAppName;
	}


	public Long getDomainId() {
		return this.domainId;
	}
	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}


	public Integer getDataStatus() {
		return this.dataStatus;
	}
	public void setDataStatus(Integer dataStatus) {
		this.dataStatus = dataStatus;
	}


	public String getCreator() {
		return this.creator;
	}
	public void setCreator(String creator) {
		this.creator = creator;
	}


	public String getModifier() {
		return this.modifier;
	}
	public void setModifier(String modifier) {
		this.modifier = modifier;
	}


	public Long getCreateTime() {
		return this.createTime;
	}
	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}


	public Long getModifyTime() {
		return this.modifyTime;
	}
	public void setModifyTime(Long modifyTime) {
		this.modifyTime = modifyTime;
	}


}


