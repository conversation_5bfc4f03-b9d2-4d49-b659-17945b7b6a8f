package com.uino.plugin.classloader.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface PluginOperateLogModule {

    /**
     * 模块code
     */
    String code();

    /**
     * 模块显示名称
     */
    String name();

    /**
     * 模块Mvc包路径，必须以com.uino开头
     */
    String mvcPackagePrefix();
}
