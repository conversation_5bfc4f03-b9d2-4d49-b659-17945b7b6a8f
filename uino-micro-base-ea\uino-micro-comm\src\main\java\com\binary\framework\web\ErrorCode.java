package com.binary.framework.web;

public enum ErrorCode {
	
	
	URL_WRONG(400),
	
	
	SERVER_ERROR(500),
	
	
	SESSION_INVALID(501),
	
	
	NOT_LOGIN(401);
	
	
	
	private int value;
	
	
	private ErrorCode(int value) {
		this.value = value;
	}
	
	
	public int getCode() {
		return this.value;
	}
	
	
	
	public static ErrorCode valueOf(int code) {
		switch(code) {
			case 400: return URL_WRONG;
			case 500: return SERVER_ERROR;
			case 501: return SESSION_INVALID;
			case 401: return NOT_LOGIN;
			default: throw new RuntimeException(" is wrong error-code:'"+code+"'! ");
		}
	}
	
		
}
