package com.uinnova.product.eam.base.model;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import lombok.Data;

import java.util.Map;

@Data
public class SystemInfo {

    private Long classId;

    private Long id;

    private String ciCode;

    private Map<String, String> attrs;

    public SystemInfo(CcCiInfo ci){
        this.attrs = ci.getAttrs();
        this.classId = ci.getCi().getClassId();
        this.id = ci.getCi().getId();
        this.ciCode = ci.getCi().getCiCode();
    }

    public SystemInfo() {
    }
}

