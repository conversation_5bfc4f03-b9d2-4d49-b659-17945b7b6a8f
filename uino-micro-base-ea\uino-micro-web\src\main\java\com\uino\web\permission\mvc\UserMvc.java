package com.uino.web.permission.mvc;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import com.uino.api.client.permission.IOauthApiSvc;
import com.uino.bean.permission.base.OauthTokenDetail;
import com.uino.bean.permission.base.SysModuleOutSide;
import com.uino.service.permission.microservice.IUserSvc;
import com.uino.web.auth.VerifyAuthUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import com.binary.core.exception.MessageException;
import com.binary.core.util.BinaryUtils;
import com.binary.framework.util.ControllerUtils;
import com.binary.jdbc.Page;
import com.binary.json.JSON;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uinnova.product.vmdb.comm.doc.annotation.MvcDesc;
import com.uino.util.sys.BeanUtil;
import com.uino.util.sys.SysUtil;
import com.uino.bean.cmdb.business.ImportResultMessage;
import com.uino.bean.permission.base.SysRoleDataModuleRlt;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.permission.business.CurrentUserInfo;
import com.uino.bean.permission.business.ModuleNodeInfo;
import com.uino.bean.permission.business.SysUserPwdResetParam;
import com.uino.bean.permission.business.UserInfo;
import com.uino.bean.permission.business.request.GetUserAuthRequestDto;
import com.uino.bean.permission.business.request.SaveUserRoleRltRequestDto;
import com.uino.bean.permission.query.CSysUser;
import com.uino.bean.permission.query.UserSearchBeanExtend;
import com.uino.init.api.ApiResult;
import com.uino.init.api.ApiVersion;
import com.uino.api.client.permission.IUserApiSvc;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@ApiVersion(1)
@Api(value = "用户管理", tags = {"用户管理"})
@RestController
@RequestMapping("/permission/user")
@MvcDesc(author = "cgj", desc = "用户管理")
public class UserMvc {
	public static final String SYS_MODULE_SIGN = "用户管理";

	@Autowired
	private IUserSvc iUserSvc;
	@Autowired
	private IUserApiSvc userSvc;
	@Autowired
	private IOauthApiSvc oauthApiSvc;

	@Resource
	VerifyAuthUtil verifyAuthUtil;


	@ApiOperation("保存或更新用户信息")
	@RequestMapping(value="/saveOrUpdate",method =RequestMethod.POST)
	@ModDesc(desc = "保存或更新用户信息", pDesc = "用户封装对象", pType = UserInfo.class, rDesc = "用户主键", rType = Long.class)
	public ApiResult<Long> saveOrUpdate(HttpServletRequest request, HttpServletResponse response, @RequestBody UserInfo userInfo) {
		verifyAuthUtil.verifyAuth(SYS_MODULE_SIGN);
		SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
		userInfo.setDomainId(currentUserInfo.getDomainId());
		Long result = userSvc.saveOrUpdate(userInfo);

		return ApiResult.ok(this).data(result);
	}

	@ApiOperation("根据id查询用户信息，包含组织及角色信息")
	@GetMapping(value="/getUserInfoById")
	@ModDesc(desc = "根据id查询用户信息，包含组织及角色信息", pDesc = "用户id", pType = Long.class, rDesc = "分页结果", rType = UserInfo.class)
	public ApiResult<UserInfo> getUserInfoById(HttpServletRequest request, HttpServletResponse response, @RequestParam Long userId) {
		if (!BinaryUtils.isEmpty(userId) && !userId.equals(SysUtil.getCurrentUserInfo().getId())) {
			verifyAuthUtil.verifyAuth(SYS_MODULE_SIGN);
		}
		UserInfo result = userSvc.getUserInfoById(userId);
		return ApiResult.ok(this).data(result);
	}

	@ApiOperation("根据id查询用户信息，包含组织及角色信息")
	@GetMapping(value="/getUserInfoByIdNoverify")
	@ModDesc(desc = "根据id查询用户信息，包含组织及角色信息", pDesc = "用户id", pType = Long.class, rDesc = "分页结果", rType = UserInfo.class)
	public ApiResult<UserInfo> getUserInfoByIdNoverify(HttpServletRequest request, HttpServletResponse response, @RequestParam Long userId) {
		UserInfo result = userSvc.getUserInfoById(userId);
		// 敏感信息进行脱敏
		UserInfo.filterSensitiveInfo(result);
		return ApiResult.ok(this).data(result);
	}

	@ApiOperation("分页查询用户数据，支持根据角色id或组织id查询")
	@RequestMapping(value="/queryInfoPage",method = RequestMethod.POST)
	@ModDesc(desc = "分页查询用户数据，支持根据角色id或组织id查询", pDesc = "查询条件", pType = UserSearchBeanExtend.class, rDesc = "分页结果", rType = Page.class, rcType = UserInfo.class)
	public ApiResult<Page<UserInfo>> queryInfoPage(HttpServletRequest request, HttpServletResponse response,
												   @RequestBody UserSearchBeanExtend searchBean) {
		verifyAuthUtil.verifyAuth(SYS_MODULE_SIGN);
		searchBean = searchBean == null ? new UserSearchBeanExtend() : searchBean;
		SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
		searchBean.setDomainId(currentUserInfo.getDomainId());
		Page<UserInfo> result = userSvc.getListByQuery(searchBean);
		return ApiResult.ok(this).data(result);
	}
	@ApiOperation("分页查询用户数据，支持根据角色id或组织id查询")
	@RequestMapping(value="/queryInfoPageNoVerify",method = RequestMethod.POST)
	@ModDesc(desc = "分页查询用户数据，支持根据角色id或组织id查询", pDesc = "查询条件", pType = UserSearchBeanExtend.class, rDesc = "分页结果", rType = Page.class, rcType = UserInfo.class)
	public ApiResult<Page<UserInfo>> queryInfoPageNoVerify(HttpServletRequest request, HttpServletResponse response,
														   @RequestBody UserSearchBeanExtend searchBean) {
		searchBean = searchBean == null ? new UserSearchBeanExtend() : searchBean;
		SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
		searchBean.setDomainId(currentUserInfo.getDomainId());
		Page<UserInfo> result = userSvc.getListByQuery(searchBean);
		return ApiResult.ok(this).data(result);
	}

    @ApiOperation("跟据角色id查询用户")
	@RequestMapping(value="/getUserByRoleId",method = RequestMethod.POST)
    @ModDesc(desc = "跟据角色id查询用户", pDesc = "角色id", pType = Long.class, rDesc = "用户列表", rType = List.class, rcType = SysUser.class)
    public ApiResult<List<SysUser>> getUserByRoleId(HttpServletRequest request, HttpServletResponse response, @RequestBody Long roleId) {
		// userPeer.verifyAuth(request, response);
		List<SysUser> result = userSvc.getUserByRoleId(roleId);
		return ApiResult.ok(this).data(result);
	}

	@ApiOperation("根据角色名称查询用户ids")
	@GetMapping("/getUserByRoleName")
	public ApiResult<Page<SysUser>> getUserByRoleName(HttpServletRequest request, HttpServletResponse response,
													  @RequestParam String roleName,
													  @RequestParam(defaultValue = "") String loginName,
													  @RequestParam(defaultValue = "1") int pageNum,
													  @RequestParam(defaultValue = "100") int pageSize) {
		Page<SysUser> result = userSvc.queryPageByRoleName(roleName, loginName, pageNum, pageSize);
		return ApiResult.ok(this).data(result);
	}

	@RequestMapping("/updateCurUser")
    @ModDesc(desc = "更新用户基本信息(不包含权限和密码),包含用户头像", pDesc = "用户封装对象", pType = UserInfo.class, rDesc = "用户主键", rType = Long.class)
	public void updateCurUser(HttpServletRequest request, HttpServletResponse response,
			@RequestParam(value = "file", required = false) MultipartFile file,
			@RequestParam("userInfo") String userInfoStr) {
		BinaryUtils.checkEmpty(userInfoStr, "userInfo");
		UserInfo userInfo = JSON.toObject(userInfoStr, UserInfo.class);
		if (userInfo.getId() == null
				|| SysUtil.getCurrentUserInfo().getId().longValue() != userInfo.getId().longValue()) {
			throw MessageException.i18n("BS_NOT_AUTH", new HashMap<String, String>());
			// userSvc.verifyAuth();
		}
		SysUser user = BeanUtil.converBean(userInfo, SysUser.class);
		SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
		user.setDomainId(currentUserInfo.getDomainId());
		Long id = userSvc.updateCurUser(user, file);
		ControllerUtils.returnJson(request, response, id);
	}

	@ApiOperation("用户更新密码")
    @RequestMapping(value="/resetUserPasswd",method = RequestMethod.POST)
    @ModDesc(desc = "用户更新密码", pDesc = "封装对象", pType = SysUserPwdResetParam.class, rDesc = "是否成功", rType = Boolean.class)
	public ApiResult<Boolean> resetUserPasswd(HttpServletRequest request, HttpServletResponse response,
			@RequestBody SysUserPwdResetParam pwdResetParam) {
		SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
		userSvc.resetPwd(currentUserInfo.getDomainId(),pwdResetParam);
		return ApiResult.ok(this).data(true);
	}

	@ApiOperation("管理员更新用户密码")
	@RequestMapping(value="/resetUserPasswdByAdmin",method = RequestMethod.POST)
    @ModDesc(desc = "管理员更新用户密码", pDesc = "封装对象", pType = SysUserPwdResetParam.class, rDesc = "是否成功", rType = Boolean.class)
	public ApiResult<Boolean> resetUserPasswdByAdmin(HttpServletRequest request, HttpServletResponse response,
			@RequestBody SysUserPwdResetParam pwdResetParam) {
		userSvc.verifyAuth();
		SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
		if(pwdResetParam.getDomainId() == null){
			pwdResetParam.setDomainId(currentUserInfo.getDomainId());
		}
		userSvc.resetPwdByAdmin(pwdResetParam);
		return ApiResult.ok(this).data(true);
	}

	@ApiOperation("根据主键删除用户数据")
	@RequestMapping(value="/removeUserById",method = RequestMethod.POST)
    @ModDesc(desc = "跟据主键删除用户数据", pDesc = "用户主键", pType = Long.class, rDesc = "操作是否成功", rType = Boolean.class)
    public ApiResult<Boolean> removeUserById(HttpServletRequest request, HttpServletResponse response, @RequestBody Long userId) {
		userSvc.verifyAuth();
		//删除用户时同步删除token及refreshToken--避免删除后创建同名loginCode时取到旧的token，从而解析到错的userId
		this.deleteToken(userId);
        userSvc.deleteById(userId);
		return ApiResult.ok(this).data(true);
	}

	private void deleteToken(Long userId) {
		UserInfo userInfo = userSvc.getUserInfoById(userId);
		if (userInfo == null) {
			return;
		}
		List<OauthTokenDetail> oauthTokenDetails = oauthApiSvc.getTokenDetail(null, userInfo.getLoginCode());
		if (CollectionUtils.isEmpty(oauthTokenDetails)) {
			return;
		}
		for (OauthTokenDetail tokenDetail : oauthTokenDetails) {
			oauthApiSvc.delByCode(tokenDetail.getTokenCode());
			oauthApiSvc.delRefreshTokenByCode(tokenDetail.getTokenCode());
		}
	}

	@ApiOperation("导入用户信息")
	@RequestMapping(value = "/importUserExcel", method = RequestMethod.POST)
    @ModDesc(desc = "导入用户信息", pDesc = "导入文件流", pType = MultipartFile.class, rDesc = "导入结果", rType = ImportResultMessage.class)
	public ApiResult<ImportResultMessage> importExcel(HttpServletRequest request, HttpServletResponse response,
			@RequestParam("file") MultipartFile file) throws IOException {
		userSvc.verifyAuth();
		SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
		ImportResultMessage message = userSvc.importUserByExcel(currentUserInfo.getDomainId(), file);
		return ApiResult.ok(this).data(message);
	}

    @ApiOperation("下载用户信息模版")
	@RequestMapping(value = "/downloadUserExcelTpl",method = RequestMethod.POST)
    @ModDesc(desc = "下载用户信息模版", pDesc = "用户模板参数", pType = String.class, rDesc = "用户信息模版EXCEl表格", rType = ResponseEntity.class)
	public ResponseEntity<byte[]> downloadExcelTpl(HttpServletRequest request, HttpServletResponse response) {
		SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
		CSysUser cSysUser=new CSysUser();
		cSysUser.setDomainId(currentUserInfo.getDomainId());
		return userSvc.exportUserToExcel(true, cSysUser);
	}

	@ApiOperation("导出用户信息")
	@RequestMapping(value = "/exportUserExcel",method = RequestMethod.POST)
    @ModDesc(desc = "导出用户信息", pDesc = "限定条件", pType = CSysUser.class, rDesc = "用户信息EXCEl表格", rType = ResponseEntity.class)
	public ResponseEntity<byte[]> exportExcel(HttpServletRequest request, HttpServletResponse response,
			@RequestBody(required = false) CSysUser cdt) {
		// userPeer.verifyAuth(request, response);
		cdt = BinaryUtils.isEmpty(cdt) ? new CSysUser() : cdt;
		SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
		cdt.setDomainId(currentUserInfo.getDomainId());
		return userSvc.exportUserToExcel(false, cdt);
	}

	@ApiOperation("为用户绑定角色")
	@RequestMapping(value="/addUserRoleRlt",method = RequestMethod.POST)
    @ModDesc(desc = "为用户绑定角色", pDesc = "用户角色关系", pType = SaveUserRoleRltRequestDto.class, rDesc = "添加结果", rType = Boolean.class)
	public ApiResult<Boolean> addUserRoleRlt(HttpServletRequest request, HttpServletResponse response,
			@RequestBody SaveUserRoleRltRequestDto bean) {
		SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
		bean.setDomainId(currentUserInfo.getDomainId());
		userSvc.saveUserRoleRlt(bean);
		return ApiResult.ok(this).data(true);
	}

	/**
	 * 获取菜单tree结构
	 *
	 * @param request
	 * @param response
	 * @param userId
	 *            传递则只返回该用户有权限的，为空则返回整个菜单结构
	 */
	@ApiOperation("获取菜单tree结构(不传用户id则返回所有菜单)")
	@RequestMapping(value="/getMenuTree",method = RequestMethod.POST)
    @ModDesc(desc = "获取菜单tree结构(不传用户id则返回所有菜单)", pDesc = "用户id", pType = Long.class, rDesc = "树状菜单数据", rType = ModuleNodeInfo.class)
    public ApiResult<ModuleNodeInfo> getMenuTree(HttpServletRequest request, HttpServletResponse response, @RequestBody(required = false) Long userId) {
		SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
		ModuleNodeInfo result = userSvc.getModuleTree(currentUserInfo.getDomainId(),userId);
		return ApiResult.ok(this).data(result);
	}

	/**
	 * 获取指定用户拥有的数据权限字典
	 *
	 * @param request
	 * @param response
	 * @param userId
	 * @param moduleCodes
	 */
	@ApiOperation("获取指定用户拥有的数据权限字典")
	@RequestMapping(value="/getAuth",method = RequestMethod.POST)
    @ModDesc(desc = "获取指定用户拥有的数据权限字典", pDesc = "用户id", pType = Long.class, rDesc = "用户拥有的数据权限", rType = Map.class)
	public ApiResult<Map<String, List<SysRoleDataModuleRlt>>> getAuth(HttpServletRequest request, HttpServletResponse response,
			@RequestBody GetUserAuthRequestDto bean) {
		Long userId = bean.getUserId();
		List<String> moduleCodes = bean.getModuleCodes();
		Map<String, List<SysRoleDataModuleRlt>> result = userSvc.getDataModule(userId, moduleCodes);
		return ApiResult.ok(this).data(result);
	}

	@ApiOperation("用户收藏菜单")
	@RequestMapping(value = "/enshrineMenu", method = RequestMethod.POST)
    @ModDesc(desc = "用户收藏菜单", pDesc = "菜单id", pType = Long.class, rDesc = "用户菜单收藏关系id", rType = Long.class)
    public ApiResult<Long> enshrineMenu(HttpServletRequest request, HttpServletResponse response, @RequestBody Long moduleId) {
		SysUser user = SysUtil.getCurrentUserInfo();
		Long userId = user.getId();
		SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
		Long result = userSvc.enshrineModule(currentUserInfo.getDomainId(), userId, moduleId);
		return ApiResult.ok(this).data(result);
	}

	@ApiOperation("用户取消菜单收藏")
	@RequestMapping(value = "/unenshrineMenu", method = RequestMethod.POST)
    @ModDesc(desc = "用户取消菜单收藏", pDesc = "用户id", pType = Long.class, rDesc = "操作是否成功", rType = Long.class)
    public ApiResult<Integer> unenshrineMenu(HttpServletRequest request, HttpServletResponse response, @RequestBody Long moduleId) {
		SysUser user = SysUtil.getCurrentUserInfo();
		Long userId = user.getId();
		Integer result = userSvc.unenshrineModule(user.getDomainId(),userId, moduleId);
		return ApiResult.ok(this).data(result);
	}

	@ApiOperation("获取当前登录用户信息")
	@RequestMapping(value = "/getCurrentUser",method=RequestMethod.POST)
    @ModDesc(desc = "获取当前登录用户信息", pDesc = "无", rDesc = "当前用户信息", rType = CurrentUserInfo.class)
	public ApiResult<CurrentUserInfo> getCurrentUser(HttpServletRequest request, HttpServletResponse response) {
		return ApiResult.ok(this).data(userSvc.getCurrentUser());
	}

	@ApiOperation("根据loginCode获取用户菜单")
	@GetMapping(value = "/getModulesByLoginCode")
	@ModDesc(desc = "根据loginCode获取用户菜单", pDesc = "无", rDesc = "菜单信息", rType = List.class)
	public ApiResult<List<SysModuleOutSide>> getModulesByLoginCode(@RequestParam(value = "loginCode") String loginCode) {
		SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
		return ApiResult.ok(this).data(userSvc.getModulesByLoginCode(currentUserInfo.getDomainId(), loginCode));
	}

	@ApiOperation("根据用户名或登录名模糊查询用户数据")
	@PostMapping(value = "/getUserInfoByName")
	@ModDesc(desc = "根据用户名或登录名模糊查询用户数据", pDesc = "无", rDesc = "菜单信息", rType = List.class)
	public ApiResult<List<SysUser>> getUserInfoByName(HttpServletRequest request, HttpServletResponse response, @RequestBody SysUser sysUser) {
		List<SysUser> result = userSvc.getUserInfoByName(sysUser.getUserName());
		return ApiResult.ok(this).data(result);
	}

	@ApiOperation("根据用户名或登录名模糊查询用户数据，分页展示")
	@GetMapping("/findUserInfoByNameForPage")
	public ApiResult<Page<SysUser>> findUserInfoByNameForPage(@RequestParam Integer pageNum, @RequestParam Integer pageSize, @RequestParam String userName) {
		Page<SysUser> userPage = userSvc.findUserInfoByNameForPage(pageNum, pageSize, userName);
		return ApiResult.ok(this).data(userPage);
	}

	@ApiOperation("调用用户数据同步功能")
	@RequestMapping("/initUserCiDataByLoginCode")
	public ApiResult<Page<SysUser>> initUserCiDataByLoginCode(HttpServletRequest request, HttpServletResponse response, @RequestBody Set<String> userCodes) {
		for (String userCode : userCodes) {
			iUserSvc.initUserCiData(userCode);
		}
		return ApiResult.ok(this).data(Boolean.TRUE);
	}

}
