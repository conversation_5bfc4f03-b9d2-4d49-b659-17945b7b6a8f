package com.uinnova.product.eam.service;

import com.uinnova.product.eam.comm.model.es.AppPanoramaInfo;
import com.uinnova.product.eam.model.AppPanoramaDetailConfigVo;

public interface AppPanoramaSvc {
    Long saveOrUpdateDetailConf(AppPanoramaDetailConfigVo detailConfig);

    AppPanoramaDetailConfigVo getDetailConf(Long appSquareConfId);

    Long saveOrUpdatePanorama(AppPanoramaInfo panoramaInfo);

    Long change(AppPanoramaInfo panoramaInfo);

    AppPanoramaInfo getPanoramaInfoByCiCode(String ciCode);
}
