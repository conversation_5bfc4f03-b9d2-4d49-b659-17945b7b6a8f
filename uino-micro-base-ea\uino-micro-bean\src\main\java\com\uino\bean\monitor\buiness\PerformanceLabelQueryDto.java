package com.uino.bean.monitor.buiness;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ApiModel(value = "性能标签查询", description = "性能标签查询")
public class PerformanceLabelQueryDto {

    @ApiModelProperty(value = "属性关键字")
    private String labelKey;

    @ApiModelProperty(value = "属性值")
    private String labelValue;

    @ApiModelProperty(value = "1 表示等于，2 表示like, 默认为 1")
    private Integer operationType = 1;
}
