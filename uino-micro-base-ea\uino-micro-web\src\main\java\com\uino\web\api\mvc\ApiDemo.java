package com.uino.web.api.mvc;


import com.uino.bean.demo.Student;
import com.uino.init.api.ApiResult;
import com.uino.init.api.ApiVersion;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * Controller 示例, 在定义方法mapping时, 请按照Restful风格定义
 *
 * @ApiVersion(version) 版本号, 用于生成接口版本号
 * @Api
 * - value 具体描述信息
 * - tags 标签组, 用于生成左侧菜单列表
 **/
@ApiVersion(1)
@Api(value = "示例", tags = {"示例"})
@RequestMapping("/apiDemo")
@RestController
public class ApiDemo {

    /**
     * 接口返回值示例, 方法定义时, 统一定义返回值为 ApiResult<T>
     *
     * @ApiOperation 接口描述
     * - value 接口描述
     * - tags 接口组
     *
     * @return ok/error
     * - ApiResult.ok(this).data(data) 成功返回
     * - ApiResult.error(this).data(data) 错误返回
     * */
    @GetMapping("/getAll")
    @ApiOperation(value = "查询全部学生信息")
    public ApiResult<List<Student>> getAll() {
        List<Student> students = new ArrayList<>();
        Student student = new Student();
        student.setId(123456L);
        student.setAge(12);
        student.setName("mike");
        student.setSex(0);
        students.add(student);
        return ApiResult.ok(this).data(students);
    }


    /**
     * 接口返回值示例, 方法定义时, 统一定义返回值为 ApiResult<T>
     *
     * @ApiOperation 接口描述
     * - value 接口描述
     * - tags 接口组
     *
     * @return ok/error
     * - ApiResult.ok(this).data(data) 成功返回
     * - ApiResult.error(this).data(data) 错误返回
     * */
    @PostMapping("/saveOrUpdate")
    @ApiOperation(value = "创建或更新学生信息")
    public ApiResult<Boolean> saveOrUpdate(@RequestBody Student student) {
        // saveOrUpdate
        boolean data = true;
        return ApiResult.ok(this).data(data);
    }
}
