package com.uinnova.product.eam.web.asset.mvc;

import com.binary.framework.web.RemoteResult;
import com.uinnova.product.eam.model.AppPanoramaDetailConfigVo;
import com.uinnova.product.eam.model.AppPanoramaInfoVo;
import com.uinnova.product.eam.web.asset.peer.AppPanoramaPeer;
import com.uino.bean.cmdb.base.LibType;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;

/**
 * 应用全景配置控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/eam/panorama")
public class AppPanoramaMvc {
    @Resource
    AppPanoramaPeer panoramaPeer;

    @PostMapping("saveOrUpdateDetailConf")
    public RemoteResult saveOrUpdateDetailConf(@RequestBody AppPanoramaDetailConfigVo detailConfig) {
        Long id = panoramaPeer.saveOrUpdateDetailConf(detailConfig);
        return new RemoteResult(id);
    }

    @GetMapping("getDetailConf")
    public RemoteResult getDetailConf(Long appSquareConfId) {
        AppPanoramaDetailConfigVo result = panoramaPeer.getDetailConf(appSquareConfId);
        return new RemoteResult(result);
    }

    @PostMapping("saveOrUpdatePanorama")
    public RemoteResult saveOrUpdatePanorama(@RequestParam(defaultValue = "DESIGN") LibType libType, @RequestBody AppPanoramaInfoVo panoramaInfo) {
        return new RemoteResult(panoramaPeer.saveOrUpdatePanorama(panoramaInfo, libType));
    }

    @PostMapping("changePanorama")
    public RemoteResult changePanorama(@RequestBody AppPanoramaInfoVo panoramaInfo) {
        return new RemoteResult(panoramaPeer.changePanorama(panoramaInfo));
    }
}
