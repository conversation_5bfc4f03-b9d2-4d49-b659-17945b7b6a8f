package com.uino.service.permission.microservice.impl;

import com.alibaba.fastjson.JSON;
import com.binary.core.exception.MessageException;
import com.binary.core.util.BinaryUtils;
import com.binary.jdbc.Page;
import com.uino.bean.permission.base.*;
import com.uino.bean.permission.business.OrgNodeInfo;
import com.uino.bean.permission.business.request.*;
import com.uino.bean.permission.query.CSysOrg;
import com.uino.bean.permission.query.CSysUser;
import com.uino.dao.BaseConst;
import com.uino.dao.permission.ESOrgSvc;
import com.uino.dao.permission.ESRoleSvc;
import com.uino.dao.permission.ESUserSvc;
import com.uino.dao.permission.rlt.ESPerssionCommSvc;
import com.uino.dao.util.ESUtil;
import com.uino.service.permission.microservice.IOrgSvc;
import com.uino.service.permission.microservice.IUserSvc;
import com.uino.util.sys.BeanUtil;
import com.uino.util.sys.ValidDtoUtil;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
@Slf4j
@RefreshScope
public class OrgSvc implements IOrgSvc {

    @Autowired
    private ESOrgSvc esOrgSvc;

    @Autowired
    private ESPerssionCommSvc perssionCommSvc;

    @Autowired
    private ESUserSvc esUserSvc;

    @Autowired
    private ESRoleSvc esRoleSvc;

    @Autowired
    private IUserSvc userSvc;

    @Value("${uino.permission.org.threshold.count:500}")
    private Integer orgThresholdCount;

    private void findOrgNodeByOrgId(Long orgId, OrgNodeInfo orgTree, OrgNodeInfo resNode) {
        if (orgTree.isTypeOrg()) {
            orgTree.setChildren(orgTree.getChildren() == null ? new ArrayList<>() : orgTree.getChildren());
            orgTree.setChildren(
                    orgTree.getChildren().stream().filter(child -> child.isTypeOrg()).collect(Collectors.toList()));
            if (orgTree.getOrgInfo().getId().longValue() == orgId.longValue()) {
                BeanUtil.copyProperties(orgTree, resNode);
                return;
            } else if (orgTree.getChildren() != null && orgTree.getChildren().size() > 0) {
                orgTree.getChildren().forEach(node -> findOrgNodeByOrgId(orgId, node, resNode));
            }
        }
    }


    /**
     * 根组织Id(原来为0L)用domainId暂代
     */
    @Override
    public OrgNodeInfo getOrgTree(Long domainId) {
        domainId = domainId == null ? BaseConst.DEFAULT_DOMAIN_ID : domainId;
        Long orgRootId = domainId;
        return getOrgTree(domainId,orgRootId,Boolean.TRUE);
    }

    @Override
    public List<SysOrg> getOrgListByParentId(Long parentId) {
        Assert.notNull(parentId,"组织id不可为空");
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.termQuery("parentOrgId",parentId));
        return esOrgSvc.getListByQuery(boolQueryBuilder);
    }

    public OrgNodeInfo getOrgTree(Long domainId, Long rootOrg, boolean findUser) {
        domainId = domainId == null ? BaseConst.DEFAULT_DOMAIN_ID : domainId;
        // 获取组织列表
        int size = new BigDecimal(esOrgSvc.countByCondition(QueryBuilders.termQuery("domainId", domainId))).intValue();
        size = size == 0 ? 3000 : size;
        List<SysOrg> orgs = esOrgSvc.getSortListByCdt(1, size, CSysOrg.builder().domainId(domainId).build(), "orderNo", true).getData();
        SysOrg rootNode = null;
        for (SysOrg org : orgs) {
            if (org.getId().longValue() == rootOrg) {
                rootNode = BeanUtil.converBean(org, SysOrg.class);
                break;
            }
        }
        Assert.notNull(rootNode, "根节点不存在");

        orgs.removeIf(org -> org.getId().longValue() == rootOrg);
        /// 往组织下挂人-先获取所有有效组织下的用户字典（把没组织的扔根下）=》遍历组织tree挂上去
        Map<Long, Set<SysUser>> orgIdUsersDict = new LinkedHashMap<>();
        if (findUser) {
            Set<Long> orgIds = new HashSet<>();
            orgIds.add(rootOrg);
            orgs.forEach(org -> {
                orgIds.add(org.getId());
            });
            // 组织用户id:obj字典
            CSysUser cdt = new CSysUser();
            cdt.setStartId(1L);
            cdt.setDomainId(domainId);
            Page<SysUser> allUser = esUserSvc.getListByCdt(1, 99999, cdt);
            Map<Long, SysUser> idUserMap = BinaryUtils.toObjectMap(allUser.getData(), "id");
            orgIdUsersDict.put(rootOrg, new HashSet<>());
            List<SysUserOrgRlt> userOrgMappings = perssionCommSvc.getUserOrgRltByOrgIds(orgIds);
            Set<Long> hasOrgUserIds = new HashSet<>();
            userOrgMappings.forEach(userOrgMapping -> {
                hasOrgUserIds.add(userOrgMapping.getUserId());
                orgIdUsersDict.computeIfAbsent(userOrgMapping.getOrgId(), k -> new HashSet<>());
                if (idUserMap.get(userOrgMapping.getUserId()) != null) {
                    orgIdUsersDict.get(userOrgMapping.getOrgId())
                            .add(idUserMap.get(userOrgMapping.getUserId()).clearSensitive());
                } else {
                    log.error("组织用户关系org【{}】user【{}】，对应用户不存在", userOrgMapping.getOrgId(), userOrgMapping.getUserId());
                }
            });
            // 没有组织用户
            BoolQueryBuilder query = QueryBuilders.boolQuery();

            query.mustNot(QueryBuilders.termsQuery("id", hasOrgUserIds));
            query.must(QueryBuilders.termQuery("domainId", domainId));
            query.mustNot(QueryBuilders.termQuery("loginCode.keyword", BaseConst._SUPER_ADMIN_LOGIN_CODE));
            Page<SysUser> noHasOrgUsers = esUserSvc.getListByQuery(1, 99999, query);
            noHasOrgUsers.getData().forEach(user -> {
                orgIdUsersDict.get(rootOrg).add(user.clearSensitive());
            });
        }
        // 平铺结构转tree结构
        OrgNodeInfo tree = OrgSvc.listToTree(rootNode, orgs, orgIdUsersDict);
        tree.setIsTotal(true);
        return tree;
    }

    @Override
    public SysOrg getRootOrg(Long domainId) {
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("domainId", domainId));
        query.must(QueryBuilders.termQuery("orgCode.keyword",BaseConst._ORG_CODE));
        List<SysOrg> listByQuery = esOrgSvc.getListByQuery(query);
        try {
            return listByQuery.get(0);
        }catch (Exception e){
            throw MessageException.i18n("根节点组织不存在");
        }
    }

    @Override
    public OrgNodeInfo getOrgTreeV2(Long domainId, Long orgId) {
        domainId = domainId == null ? BaseConst.DEFAULT_DOMAIN_ID : domainId;
        Long finalDomainId = domainId;
        //从根组织获取树时，判断加载策略
        if (orgId == null || orgId.longValue() == finalDomainId) {
            // 获取组织列表
            int size = new BigDecimal(esOrgSvc.countByCondition(QueryBuilders.termQuery("domainId", domainId))).intValue();
            //组织数未超过设定阈值，获取所有组织结构
            if (size <= this.orgThresholdCount) {
                return this.getOrgTree(domainId);
            }
            orgId = domainId;
        }

        //组织数超过设定阈值，根据传入的父组织id，逐级展示
        BoolQueryBuilder orgQuery = QueryBuilders.boolQuery();
        orgQuery.should(QueryBuilders.termQuery("id", orgId));
        orgQuery.should(QueryBuilders.termQuery("parentOrgId", orgId));
        List<SysOrg> orgs = esOrgSvc.getSortListByQuery(1, 9999, orgQuery, "orderNo", true).getData();
        OrgNodeInfo tree = null;
        Set<Long> orgIds = new HashSet<>();
        Iterator<SysOrg> it = orgs.iterator();
        while (it.hasNext()) {
            SysOrg org = it.next();
            orgIds.add(org.getId());
            if (org.getId().longValue() == orgId.longValue()) {
                tree = OrgNodeInfo.builder().nodeName(org.getOrgName()).hasChilds(false)
                        .orgInfo(SysOrg.builder().id(org.getId()).orgName(org.getOrgName()).orgCode(org.getOrgName()).orderNo(1).domainId(domainId).build())
                        .isTotal(false).build();
                if (orgs.size() > 1) {
                    tree.setHasChilds(true);
                }
                it.remove();
            }
        }

        List<SysOrg> childOrgs = esOrgSvc.getSortListByQuery(1, 9999, QueryBuilders.termsQuery("parentOrgId", orgs.stream().map(SysOrg::getId).collect(Collectors.toList())), "orderNo", true).getData();
        Map<Long, List<SysOrg>> childOrgMap = BinaryUtils.toObjectGroupMap(childOrgs, "parentOrgId");
        /// 往组织下挂人-先获取所有有效组织下的用户字典（把没组织的扔根下）=》遍历组织tree挂上去
        Map<Long, Set<SysUser>> orgIdUsersDict = new LinkedHashMap<>();
        // 组织用户id:obj字典
        CSysUser cdt = new CSysUser();
        cdt.setStartId(1L);
        cdt.setDomainId(domainId);
        Page<SysUser> allUser = esUserSvc.getListByCdt(1, 99999, cdt);
        Map<Long, SysUser> idUserMap = BinaryUtils.toObjectMap(allUser.getData(), "id");
        orgIdUsersDict.put(finalDomainId, new HashSet<>());
        List<SysUserOrgRlt> userOrgMappings = perssionCommSvc.getUserOrgRltByOrgIds(orgIds);
        Set<Long> hasOrgUserIds = new HashSet<>();
        userOrgMappings.forEach(userOrgMapping -> {
            hasOrgUserIds.add(userOrgMapping.getUserId());
            orgIdUsersDict.computeIfAbsent(userOrgMapping.getOrgId(), k -> new HashSet<>());
            if (idUserMap.get(userOrgMapping.getUserId()) != null) {
                orgIdUsersDict.get(userOrgMapping.getOrgId())
                        .add(idUserMap.get(userOrgMapping.getUserId()).clearSensitive());
            } else {
                log.error("组织用户关系org【{}】user【{}】，对应用户不存在", userOrgMapping.getOrgId(), userOrgMapping.getUserId());
            }
        });
        if (orgId.longValue() == domainId) {
            // 没有组织用户
            List<SysUser> noHasOrgUsers = allUser.getData().stream().filter(user -> !hasOrgUserIds.contains(user.getId())).collect(Collectors.toList());

            noHasOrgUsers.forEach(user -> {
                orgIdUsersDict.get(finalDomainId).add(user.clearSensitive());
            });
        }
        for (SysOrg org : orgs) {
            OrgNodeInfo node = OrgNodeInfo.builder().dataId(org.getId()).nodeName(org.getOrgName()).hasChilds(false)
                    .orgInfo(org).build();
            if (childOrgMap.get(org.getId()) != null) {
                node.setHasChilds(true);
            }
            tree.getChildren().add(node);
            // 组织下放人
            if (node.isTypeOrg() && orgIdUsersDict.get(node.getOrgInfo().getId()) != null) {
                orgIdUsersDict.get(node.getOrgInfo().getId()).forEach(user -> {
                    node.addUserIds(user.getId());
                    node.getChildren().add(OrgNodeInfo.builder().dataId(user.getId()).nodeName(user.getUserName())
                            .typeOrg(false).userInfo(user).build());
                });
            }

        }
        OrgNodeInfo.countAllOrgHasUserCount(tree);
        return tree;
    }

    @Override
    public OrgNodeInfo getOrgTreeV3(Long domainId) {
        Long orgId = 1L;
        domainId = domainId == null ? BaseConst.DEFAULT_DOMAIN_ID : domainId;
        // 获取组织列表
        int size = new BigDecimal(esOrgSvc.countByCondition(QueryBuilders.termQuery("domainId", domainId))).intValue();
        size = size == 0 ? 3000 : size;
        List<SysOrg> orgs = esOrgSvc.getSortListByCdt(1, size, CSysOrg.builder().domainId(domainId).build(), "orderNo", true).getData();
        SysOrg rootNode = null;
        for (SysOrg org : orgs) {
            if (org.getId().longValue() == orgId) {
                rootNode = BeanUtil.converBean(org, SysOrg.class);
                break;
            }
        }
        Assert.notNull(rootNode, "根节点不存在");

        orgs.removeIf(org -> org.getId().longValue() == orgId);
        Set<Long> orgIds = new HashSet<>();
        orgIds.add(orgId);
        orgs.forEach(org -> {
            orgIds.add(org.getId());
        });
        List<SysUserOrgRlt> userOrgMappings = perssionCommSvc.getUserOrgRltByOrgIds(orgIds);
        Map<Long, List<SysUserOrgRlt>> groupByOrgId = userOrgMappings.stream().collect(Collectors.groupingBy(SysUserOrgRlt::getOrgId));
        /// 往组织下挂人-先获取所有有效组织下的用户字典（把没组织的扔根下）=》遍历组织tree挂上去
        Map<Long, Set<SysUser>> orgIdUsersDict = new LinkedHashMap<>();
        // 虚拟一个根节点
        OrgNodeInfo tree = OrgNodeInfo.builder().nodeName(rootNode.getOrgName())
                .orgInfo(SysOrg.builder().id(rootNode.getId()).orgName(rootNode.getOrgName()).orgCode(rootNode.getOrgCode()).orderNo(1).build())
                .build();
        // parentId:[childInfos]字典
        Map<Long, Set<SysOrg>> parentIdChildsDict = new LinkedHashMap<>();
        orgs.forEach(org -> {
            parentIdChildsDict.computeIfAbsent(org.getParentOrgId(), k -> new LinkedHashSet<>());
            parentIdChildsDict.get(org.getParentOrgId()).add(org);
        });
        // 平铺列表转换tree结构
        listToTreeCore(parentIdChildsDict, orgIdUsersDict, tree);
        tree.setIsTotal(true);
        countTree(tree, groupByOrgId);
        setUserIdsNull(tree);
        return tree;
    }

    private void countTree(OrgNodeInfo tree, Map<Long, List<SysUserOrgRlt>> groupByOrgId) {
        List<SysUserOrgRlt> userOrgRlts = groupByOrgId.getOrDefault(tree.getOrgInfo().getId(), new ArrayList<>());
        Set<Long> userIds = userOrgRlts.stream().map(SysUserOrgRlt::getUserId).collect(Collectors.toSet());
        for (OrgNodeInfo child : tree.getChildren()) {
            countTree(child, groupByOrgId);
            tree.addAllUserIds(child.getUserIds());
        }
        tree.addAllUserIds(userIds);
    }

    private void setUserIdsNull(OrgNodeInfo tree) {
        tree.setUserIds(null);
        for (OrgNodeInfo child : tree.getChildren()) {
            setUserIdsNull(child);
        }
    }

    @Override
    public Long saveOrUpdateOrg(SaveOrgRequestDto request) {
        this.validSaveOrgRequestDto(request);
        // 数据持久化
        return esOrgSvc.saveOrUpdate(request);
    }

    /**
     * 校验持久化组织请求
     * 根组织Id(原来为0L)用domainId暂代
     *
     * @param request
     */
    private void validSaveOrgRequestDto(SaveOrgRequestDto request) {
        // 校验请求(还需验证重名，parent有效性，其他看实际情况)
        ValidDtoUtil.valid(request);
        Long id = request.getId();
        Long domainId = request.getDomainId() == null ? BaseConst.DEFAULT_DOMAIN_ID : request.getDomainId();
        // 验证parent
        SysOrg parent = esOrgSvc.getById(request.getParentOrgId());
        Assert.isTrue(parent != null || request.getParentOrgId() == 0L, "SAVE_ORG_PARNETID_NOEXIST");
        // if (parent == null && request.getParentOrgId().longValue() != 0L) {
        // throw new MessageException("SAVE_ORG_PARNETID_NOEXIST");
        // }
        // 根的话就不去校验重名
        if (id != null && id.intValue() == domainId) {
            return;
        }
        // 检测重名-通层级下不允许
        String orgName = request.getOrgName();
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("orgName.keyword", orgName));
        query.must(QueryBuilders.termQuery("parentOrgId", request.getParentOrgId()));
        query.must(QueryBuilders.termQuery("domainId", domainId));
        Set<Long> ignoreOrgIds = new HashSet<>();
        ignoreOrgIds.add(domainId);
        if (id == null) {
            // 新增生成一个机构code
            request.setOrgCode(String.valueOf(ESUtil.getUUID()));
        } else {
            // 修改的话需要把自已刨除判定重名
            ignoreOrgIds.add(id);
        }
        query.mustNot(QueryBuilders.termsQuery("id", ignoreOrgIds));
        Page<SysOrg> validRepeatNameResult = esOrgSvc.getListByQuery(1, 1, query);
        Assert.isTrue(validRepeatNameResult.getTotalRows() <= 0, "SAVE_ORG_NAME_REPEAT");
    }

    @Override
    public void deleteOrg(Set<Long> removeOrgIds) {
        Assert.notEmpty(removeOrgIds, "PARAM_NOT_NULL");
        // 校验请求-待实现
        Assert.isTrue(perssionCommSvc.getUserOrgRltByOrgIds(removeOrgIds).size() <= 0L, "组织下有用户不可删除");
        // 删除组织
        esOrgSvc.deleteByIds(new ArrayList<>(removeOrgIds));
        // 级联删除关联信息
        perssionCommSvc.deleteOrgRoleRltByQuery(QueryBuilders.termsQuery("orgId", removeOrgIds), true);
    }

    /**
     * 列表机构机构转换树结构
     *
     * @param orgs
     * @return
     */
    public static OrgNodeInfo listToTree(SysOrg rootOrg, List<SysOrg> orgs, Map<Long, Set<SysUser>> orgIdUsersDict) {
        // 虚拟一个根节点
        OrgNodeInfo tree = OrgNodeInfo.builder().nodeName(rootOrg.getOrgName())
                .orgInfo(SysOrg.builder().id(rootOrg.getId()).orgName(rootOrg.getOrgName()).orgCode(rootOrg.getOrgCode()).orderNo(1).build())
                .build();
        if (orgs == null) {
            orgs = new LinkedList<>();
        }
        // parentId:[childInfos]字典
        Map<Long, Set<SysOrg>> parentIdChildsDict = new LinkedHashMap<>();
        orgs.forEach(org -> {
            parentIdChildsDict.computeIfAbsent(org.getParentOrgId(), k -> new LinkedHashSet<>());
            parentIdChildsDict.get(org.getParentOrgId()).add(org);
        });
        // 平铺列表转换tree结构
        listToTreeCore(parentIdChildsDict, orgIdUsersDict, tree);
        OrgNodeInfo.countAllOrgHasUserCount(tree);
        return tree;
    }

    /**
     * 列表结构转树形结构核心
     *
     * @param parentIdChildsDict
     * @param node
     */
    private static void listToTreeCore(Map<Long, Set<SysOrg>> parentIdChildsDict,
                                       Map<Long, Set<SysUser>> orgIdUsersDict, OrgNodeInfo node) {
        long parentNodeId = node.getOrgInfo().getId();
        Set<SysOrg> childs = parentIdChildsDict.get(parentNodeId);
        if (!BinaryUtils.isEmpty(childs)) {
            childs.forEach(orgInfo -> {
                node.getChildren().add(OrgNodeInfo.builder().dataId(orgInfo.getId()).nodeName(orgInfo.getOrgName())
                        .orgInfo(orgInfo).build());
            });
            node.getChildren().forEach(nextNode -> {
                listToTreeCore(parentIdChildsDict, orgIdUsersDict, nextNode);
            });
        }
        // 组织下放人
        if (node.isTypeOrg() && orgIdUsersDict.get(node.getOrgInfo().getId()) != null) {
            orgIdUsersDict.get(node.getOrgInfo().getId()).forEach(user -> {
                node.addUserIds(user.getId());
            });
            // node.setUserCount(orgIdUsersDict.get(node.getOrgInfo().getId()).size());
            orgIdUsersDict.get(node.getOrgInfo().getId()).forEach(userInfo -> {
                node.getChildren().add(OrgNodeInfo.builder().dataId(userInfo.getId()).nodeName(userInfo.getUserName())
                        .typeOrg(false).userInfo(userInfo).build());
            });
        }
    }

    @Override
    public void addUserForOrg(AddOrRemoveUserToOrgRequestDto request) {
        validAddUserRequest(request);
        Set<Long> userIds = request.getUserIds();
        Long orgId = request.getOrgId();
        Long domainId = request.getDomainId() == null ? BaseConst.DEFAULT_DOMAIN_ID : request.getDomainId();
        // 删除重复数据
        this.removeUserForOrg(request);
        // 绑定数据-创建人修改人未填补，需要做登陆拦截才能获取
        List<SysUserOrgRlt> bindUserOrgMappings = new LinkedList<>();
        userIds.forEach(userId -> {
            SysUserOrgRlt addMapping = SysUserOrgRlt.builder().userId(userId).orgId(orgId).domainId(domainId).build();
            bindUserOrgMappings.add(addMapping);
        });
        perssionCommSvc.saveUseOrgRltBatch(bindUserOrgMappings);
        // 应前端强烈要求，继承策略做在后端，无法沟通(更加希望分开去做保障灵活性)
        // 暂时取消继承策略
        // this.userExtentOrgRole(ExtendOrgRoleRequestDto.builder().orgId(orgId).userIds(userIds).build());
        // 应测试要求，向组织添加用户要视为对用户本身的修改，保证新加入的在前面
        List<SysUser> users = esUserSvc.getListByQuery(QueryBuilders.termsQuery("id", userIds));
        esUserSvc.saveOrUpdateBatch(users);
    }

    /**
     * 校验向组织添加用户请求
     *
     * @param request
     */
    private void validAddUserRequest(AddOrRemoveUserToOrgRequestDto request) {
        // 校验
        ValidDtoUtil.valid(request);
        Set<Long> userIds = request.getUserIds();
        Long orgId = request.getOrgId();
        // valid org
        SysOrg validOrg = esOrgSvc.getById(orgId);
        Assert.notNull(validOrg,
                "BS_VERIFY_ERROR#{name:BS_MNAME_ORG}#{type:BS_MVTYPE_NOT_EXIST}${value:" + orgId + "}");
        // valid users
        Page<SysUser> validUser = esUserSvc.getListByQuery(1, 1, QueryBuilders.termsQuery("id", userIds));
        Assert.isTrue(userIds.size() <= validUser.getTotalRows(),
                "BS_VERIFY_ERROR#{name:BS_MNAME_USERID}#{type:BS_MVTYPE_NOT_EXIST}${value:" + JSON.toJSONString(userIds)
                        + "}");
    }

    @Override
    public void removeUserForOrg(AddOrRemoveUserToOrgRequestDto request) {
        // 校验
        ValidDtoUtil.valid(request);
        Long rootOrgId = request.getOrgId();
        Assert.notNull(esOrgSvc.getById(rootOrgId), "组织不存在");
        Set<Long> userIds = request.getUserIds();
        BoolQueryBuilder delQuery = QueryBuilders.boolQuery();
        delQuery.must(QueryBuilders.termQuery("orgId", rootOrgId));
        delQuery.must(QueryBuilders.termsQuery("userId", userIds));
        perssionCommSvc.deleteUserOrgRltByQuery(delQuery, true);
        // Set<Long> orgIds = new HashSet<>();
        // OrgNodeInfo orgTree = this.getOrgTree();
        // OrgNodeInfo rootOrg = OrgNodeInfo.builder().build();
        // findOrgNodeByOrgId(rootOrgId, orgTree, rootOrg);
        // Assert.notNull(rootOrg, "未从组织树中寻找到对应组织");
        // buildChildOrgIds(rootOrg, orgIds);
        // // 删除数据
        // BoolQueryBuilder query = QueryBuilders.boolQuery();
        // query.must(QueryBuilders.termsQuery("orgId", orgIds));
        // query.must(QueryBuilders.termsQuery("userId", userIds));
        // esUserOrgRltSvc.deleteByQuery(query, true);
        // // 经过解绑操作后如果这些用户不属于任何组织则删除用户
        // userIds.forEach(userId -> {
        // long userOrgRltNum =
        // esUserOrgRltSvc.countByCondition(QueryBuilders.termQuery("userId",
        // userId));
        // if (userOrgRltNum <= 0)
        // userSvc.deleteById(userId);
        // });
    }

    @Override
    public void setUsersOrgs(SetUsersOrgsRequestDto reqDto) {
        ValidDtoUtil.valid(reqDto);
        Set<Long> userIds = reqDto.getUserIds();
        Set<Long> orgIds = reqDto.getOrgIds();
        if (BinaryUtils.isEmpty(reqDto.getDomainId())) {
            reqDto.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
        }
        Long validUserCount = esUserSvc.countByCondition(QueryBuilders.termsQuery("id", userIds));
        Assert.isTrue(validUserCount.longValue() == Long.valueOf(userIds.size()).longValue(), "有失效用户");
        Long validOrgCount = esOrgSvc.countByCondition(QueryBuilders.termsQuery("id", orgIds));
        Assert.isTrue(validOrgCount.longValue() == Long.valueOf(orgIds.size()).longValue(), "有失效组织");
        perssionCommSvc.deleteUserOrgRltByQuery(QueryBuilders.termsQuery("userId", userIds), true);
        orgIds.forEach(orgId -> this
                .addUserForOrg(AddOrRemoveUserToOrgRequestDto.builder().orgId(orgId).userIds(userIds).domainId(reqDto.getDomainId()).build()));
    }

    private void buildChildOrgIds(OrgNodeInfo currentNode, Set<Long> orgIds) {
        if (currentNode.isTypeOrg()) {
            orgIds.add(currentNode.getOrgInfo().getId());
            if (currentNode.getChildren() != null && currentNode.getChildren().size() > 0) {
                currentNode.getChildren().forEach(childNode -> buildChildOrgIds(childNode, orgIds));
            }
        }
    }

    @Override
    public void addRoleForOrg(AddOrRemoveRoleToOrgRequestDto request) {
        validAddRoleRequest(request);
        Set<Long> roleIds = request.getRoleIds();
        Long orgId = request.getOrgId();
        Long domainId = request.getDomainId() == null ? BaseConst.DEFAULT_DOMAIN_ID : request.getDomainId();
        // 删除重复数据
        this.removeRoleForOrg(request);
        // 绑定数据
        List<SysOrgRoleRlt> bindMappings = new LinkedList<>();
        roleIds.forEach(roleId -> {
            SysOrgRoleRlt addMapping = SysOrgRoleRlt.builder().roleId(roleId).orgId(orgId).domainId(domainId).build();
            bindMappings.add(addMapping);
        });
        perssionCommSvc.saveOrgRoleRltBatch(bindMappings);
    }

    /**
     * 校验向组织加角色请求
     * 
     * @param request
     */
    private void validAddRoleRequest(AddOrRemoveRoleToOrgRequestDto request) {
        ValidDtoUtil.valid(request);
        Set<Long> roleIds = request.getRoleIds();
        Long orgId = request.getOrgId();
        // valid org
        SysOrg validOrg = esOrgSvc.getById(orgId);
        Assert.notNull(validOrg,
                "BS_VERIFY_ERROR#{name:BS_MNAME_ORG}#{type:BS_MVTYPE_NOT_EXIST}${value:" + orgId + "}");
        // valid roles
        Page<SysRole> validRole = esRoleSvc.getListByQuery(1, 1, QueryBuilders.termsQuery("id", roleIds));
        Assert.isTrue(roleIds.size() <= validRole.getTotalRows(),
                "BS_VERIFY_ERROR#{name:BS_MNAME_ROLE}#{type:BS_MVTYPE_NOT_EXIST}${value:" + JSON.toJSONString(roleIds)
                        + "}");
    }

    @Override
    public void removeRoleForOrg(AddOrRemoveRoleToOrgRequestDto request) {
        ValidDtoUtil.valid(request);
        Set<Long> roleIds = request.getRoleIds();
        Long orgId = request.getOrgId();
        // 删除数据
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("orgId", orgId));
        query.must(QueryBuilders.termsQuery("roleId", roleIds));
        perssionCommSvc.deleteOrgRoleRltByQuery(query, true);
    }

    @Override
    public Set<Long> getUserIds(Long orgId) {
        Assert.notNull(orgId, "PARAM_NOT_NULL");
        return esOrgSvc.getInOrgUserIds(orgId);
    }

    @Override
    public Set<Long> getRoleIds(Long orgId) {
        Assert.notNull(orgId, "PARAM_NOT_NULL");
        return esOrgSvc.getInOrgRoleIds(orgId);
    }

    @Override
    public List<SysOrg> getOrgByRoleId(Long roleId) {
        Assert.notNull(roleId, "PARAM_NOT_NULL");
        List<SysOrgRoleRlt> orgRoleRlts = perssionCommSvc.getOrgRoleRltByRoleIds(Collections.singleton(roleId));
        Set<Long> orgIds = orgRoleRlts.stream().map(SysOrgRoleRlt::getOrgId).collect(Collectors.toSet());
        if (BinaryUtils.isEmpty(orgIds)) {
            return new ArrayList<>();
        }
        return esOrgSvc.getListByQuery(QueryBuilders.termsQuery("id", orgIds));
    }

    @Override
    public Page<SysOrg> queryPageByCdt(int pageNum, int pageSize, CSysOrg query) {
        if (query == null) {
            query = new CSysOrg();
        }
        if(BinaryUtils.isEmpty(query.getDomainId())){
            query.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
        }
        return esOrgSvc.getListByCdt(pageNum, pageSize, query);
    }

    @Override
    public List<SysOrg> queryListByCdt(CSysOrg query) {
        if (query == null) {
            query = new CSysOrg();
        }
        if (BinaryUtils.isEmpty(query.getDomainId())) {
            query.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
        }
        return esOrgSvc.getListByCdt(query);
    }

    @Override
    public void userExtentOrgRole(ExtendOrgRoleRequestDto reqDto) {
        validUserExtentOrgRole(reqDto);
        Long orgId = reqDto.getOrgId();
        Set<Long> userIds = reqDto.getUserIds();
        // 获取组织下角色，这些角色要绑定在这些用户上
        Set<Long> roleIds = this.getRoleIds(orgId);
        if (roleIds == null || roleIds.size() <= 0) {
            return;
        }
        // 开始绑定,逻辑是先清除已存在的，再去重新绑定，userSvc已有该逻辑，若发现之后有重复关系可从此观察userSvc是否取消了逻辑
        List<SysUserRoleRlt> bindUserRoleRlts = new LinkedList<>();
        for (Long userId : userIds) {
            for (Long roleId : roleIds) {
                SysUserRoleRlt bindDto = SysUserRoleRlt.builder().userId(userId).roleId(roleId).domainId(reqDto.getDomainId()).build();
                bindUserRoleRlts.add(bindDto);
            }
        }
        perssionCommSvc.saveUserRoleRltBatch(bindUserRoleRlts);
    }

    /**
     * 校验用户继承组织角色请求 现基础校验，再去验证org/users合法性
     *
     * @param reqDto
     */
    private void validUserExtentOrgRole(ExtendOrgRoleRequestDto reqDto) {
        ValidDtoUtil.valid(reqDto);
        Long orgId = reqDto.getOrgId();
        Set<Long> userIds = reqDto.getUserIds();
        SysOrg org = esOrgSvc.getById(orgId);
        Assert.notNull(org, "ORG_NOT_NULL");
        List<SysUser> users = esUserSvc.getListByQuery(QueryBuilders.termsQuery("id", userIds));
        Assert.isTrue(userIds.size() <= users.size(),
                "BS_VERIFY_ERROR#{name:BS_MNAME_USERID}#{type:BS_MVTYPE_NOT_EXIST}${value:" + JSON.toJSONString(userIds)
                        + "}");
    }

    @Override
    public void interchangeOrgNo(InterchangeOrgNoRequestDto reqDto) {
        // 校验，没单独拿出去是因为校验数据时获取的obj后面可以用。。就省事不定义校验返回了
        ValidDtoUtil.valid(reqDto);
        Long sOrgId = reqDto.getSourceOrgId();
        Long tOrgId = reqDto.getTargetOrgId();
        SysOrg sOrg = esOrgSvc.getById(sOrgId);
        Assert.notNull(sOrg, "X_PARAM_NOT_NULL#{name:BS_MNAME_ORG}");
        SysOrg tOrg = esOrgSvc.getById(tOrgId);
        Assert.notNull(tOrg, "X_PARAM_NOT_NULL#{name:BS_MNAME_ORG}");
        // 开始交换
        Integer oldTargetOrgNo = tOrg.getOrderNo();
        tOrg.setOrderNo(sOrg.getOrderNo());
        sOrg.setOrderNo(oldTargetOrgNo);
        esOrgSvc.saveOrUpdateBatch(new ArrayList<>(Arrays.asList(sOrg, tOrg)));
    }

    @Override
    public SysOrg getOrgByOrgFullName(Long domainId, String fullName) {
        Assert.notNull(fullName, "组织全名称不得为空");
        domainId = domainId == null ? BaseConst.DEFAULT_DOMAIN_ID : domainId;
        Map<String, OrgNodeInfo> orgStructure = getOrgStructure(domainId);
        return getOrgByOrgFullName(fullName, orgStructure);
    }

    @Override
    public SysOrg getOrgByOrgFullName(String fullName, Map<String, OrgNodeInfo> orgStructure) {
        Assert.notNull(fullName, "组织全名称不得为空");
        String splitStr = "/";
        String[] nodeNames = fullName.split(splitStr);
        for (int index = 0; index < nodeNames.length; index++) {
            try {
                String nodeName = nodeNames[index];
                if (index == nodeNames.length - 1) {
                    return orgStructure.get(nodeName).getOrgInfo();
                }
                orgStructure = orgStructure.get(nodeName).getChildrenNameMap();
            } catch (NullPointerException e) {
                return null;
            }
        }
        return null;
    }

    @Override
    public Map<String, OrgNodeInfo> getOrgStructure(Long domainId) {
        domainId = domainId == null ? BaseConst.DEFAULT_DOMAIN_ID : domainId;
        Long rootOrgId = domainId;
        // 获取组织列表
        List<SysOrg> orgs = esOrgSvc.getSortListByCdt(CSysOrg.builder().domainId(domainId).build(),
                new ArrayList<>(Arrays.asList(SortBuilders.fieldSort("orderNo").order(SortOrder.ASC))));
        SysOrg rootNode = null;
        for (SysOrg org : orgs) {
            if (org.getId().equals(rootOrgId)) {
                rootNode = BeanUtil.converBean(org, SysOrg.class);
            }
        }
        Assert.notNull(rootNode, "根节点不存在");
        orgs.removeIf(org -> org.getId().equals(rootOrgId));
        // parentId:[childInfos]字典
        Map<Long, Set<SysOrg>> parentIdChildsDict = new LinkedHashMap<>();
        orgs.forEach(org -> {
            parentIdChildsDict.computeIfAbsent(org.getParentOrgId(), k -> new LinkedHashSet<>());
            parentIdChildsDict.get(org.getParentOrgId()).add(org);
        });
        /**
         * 组织结构{orgName:node}
         */
        return OrgSvc.listToStructure(rootNode, orgs);
    }

    /**
     * 平铺转结构
     *
     * @param rootOrg
     * @param orgs
     * @return
     */
    public static Map<String, OrgNodeInfo> listToStructure(SysOrg rootOrg, List<SysOrg> orgs) {
        Map<String, OrgNodeInfo> orgStructure = new LinkedHashMap<>();
        String rootNodeName = rootOrg.getOrgName();
        OrgNodeInfo tree = OrgNodeInfo.builder().nodeName(rootNodeName)
                .orgInfo(SysOrg.builder().id(rootOrg.getId()).orgName(rootNodeName).orgCode(rootOrg.getOrgCode()).orderNo(1).build())
                .build();
        if (orgs == null) {
            orgs = new LinkedList<>();
        }
        // parentId:[childInfos]字典
        Map<Long, Set<SysOrg>> parentIdChildsDict = new LinkedHashMap<>();
        orgs.forEach(org -> {
            parentIdChildsDict.computeIfAbsent(org.getParentOrgId(), k -> new LinkedHashSet<>());
            parentIdChildsDict.get(org.getParentOrgId()).add(org);
        });
        // 平铺列表转换tree结构
        OrgSvc.listToStructureCore(parentIdChildsDict, tree);
        orgStructure.put(rootNodeName, tree);
        return orgStructure;
    }

    /**
     * 平铺转结构核心
     *
     * @param parentIdChildsDict
     * @param node
     */
    public static void listToStructureCore(Map<Long, Set<SysOrg>> parentIdChildsDict, OrgNodeInfo node) {
        long parentNodeId = node.getOrgInfo().getId();
        Set<SysOrg> childs = parentIdChildsDict.get(parentNodeId);
        if (!BinaryUtils.isEmpty(childs)) {
            childs.forEach(orgInfo -> {
                node.getChildrenNameMap().put(orgInfo.getOrgName(), OrgNodeInfo.builder().dataId(orgInfo.getId())
                        .nodeName(orgInfo.getOrgName()).orgInfo(orgInfo).build());
            });
            node.getChildrenNameMap().forEach((nextNodeName, nestNode) -> {
                listToStructureCore(parentIdChildsDict, nestNode);
            });
        }
    }

    @Override
    public List<SysOrg> getListByUserId(Long userId) {
        return esOrgSvc.getListByUserId(userId);
    }

    @Override
    public Map<Long, String> getOrgIdAllNameMap(Long domainId, Collection<Long> orgIds) {
        return esOrgSvc.getOrgIdAllNameMap(domainId, orgIds);
    }
}
