package com.uinnova.product.eam.service.manage.impl;

import com.binary.core.util.BinaryUtils;
import com.binary.jdbc.Page;
import com.google.common.collect.Lists;
import com.uinnova.product.eam.base.enums.ResultCodeEnum;
import com.uinnova.product.eam.base.util.EamUtil;
import com.uinnova.product.eam.comm.dto.EamMatrixHeaderVO;
import com.uinnova.product.eam.comm.dto.EamMatrixQueryVO;
import com.uinnova.product.eam.comm.dto.EamMatrixTableQuery;
import com.uinnova.product.eam.comm.dto.EamMatrixTableSaveVO;
import com.uinnova.product.eam.comm.model.es.EamMatrixInstance;
import com.uinnova.product.eam.comm.model.es.EamMatrixInstanceData;
import com.uinnova.product.eam.comm.model.es.EamMatrixStencil;
import com.uinnova.product.eam.comm.model.es.EamMatrixStencilAttr;
import com.uinnova.product.eam.model.CiInfoQueryVo;
import com.uinnova.product.eam.model.VcCiClassInfo;
import com.uinnova.product.eam.model.VcCiRltInfo;
import com.uinnova.product.eam.model.cj.dto.RenameRequestDTO;
import com.uinnova.product.eam.model.constants.StatusConstant;
import com.uinnova.product.eam.model.vo.ESAttrAggScreenBean;
import com.uinnova.product.eam.service.ICISwitchSvc;
import com.uinnova.product.eam.service.IEamCIClassApiSvc;
import com.uinnova.product.eam.service.es.EamMatrixInstanceDesignDao;
import com.uinnova.product.eam.service.es.EamMatrixInstancePrivateDao;
import com.uinnova.product.eam.service.exception.BusinessException;
import com.uinnova.product.eam.service.impl.IamsCIRltSwitchSvc;
import com.uinnova.product.eam.service.manage.EamMatrixDataSvc;
import com.uinnova.product.eam.service.manage.EamMatrixInstanceSvc;
import com.uinnova.product.eam.service.manage.EamMatrixStencilSvc;
import com.uinnova.product.vmdb.comm.bean.CIState;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uinnova.product.vmdb.provider.rlt.bean.CcCiRltInfo;
import com.uino.api.client.cmdb.IRltClassApiSvc;
import com.uino.api.client.permission.IUserApiSvc;
import com.uino.bean.cmdb.base.ESCIClassInfo;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.base.LibType;
import com.uino.bean.cmdb.base.dataset.relation.RelationRuleAttrCdt;
import com.uino.bean.cmdb.query.ESAttrBean;
import com.uino.bean.cmdb.query.ESCISearchBean;
import com.uino.dao.AbstractESBaseDao;
import com.uino.util.sys.SysUtil;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.TermsQueryBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 矩阵表格业务实现
 * <AUTHOR>
 */
@Slf4j
@Service
public class EamMatrixInstanceSvcImpl implements EamMatrixInstanceSvc {

    @Autowired
    private EamMatrixInstancePrivateDao instancePrivateDao;

    @Autowired
    private EamMatrixInstanceDesignDao instanceDesignDao;

    @Autowired
    private EamMatrixStencilSvc stencilSvc;

    @Resource
    private ICISwitchSvc ciSwitchSvc;

    @Resource
    private IamsCIRltSwitchSvc rltSwitchSvc;

    @Autowired
    private EamMatrixDataSvc matrixDataSvc;

    @Resource
    private IEamCIClassApiSvc ciClassApiSvc;

    @Resource
    private IRltClassApiSvc rltClassApiSvc;

    @Resource
    private IUserApiSvc userApiSvc;

    @Override
    public Long create(Long matrixId, Long dirId) {
        BinaryUtils.checkNull(matrixId, "矩阵id");
        BinaryUtils.checkNull(dirId, "文件夹id");
        EamMatrixStencil matrixStencil = stencilSvc.getById(matrixId);
        BinaryUtils.checkNull(matrixStencil, "矩阵制品");
        EamMatrixInstance instance = new EamMatrixInstance();
        instance.setName(matrixStencil.getMatrixName());
        instance.setMatrixId(matrixId);
        instance.setDirId(dirId);
        instance.setPublished(StatusConstant.UNPUBLISHED);
        instance.setStatus(StatusConstant.ENABLE);
        instance.setOwnerCode(SysUtil.getCurrentUserInfo().getLoginCode());
        return instancePrivateDao.saveOrUpdate(instance);
    }

    @Override
    public Long saveOrUpdate(EamMatrixInstance instance, LibType libType) {
        return getPriOrDesDao(libType).saveOrUpdate(instance);
    }

    @Override
    public Long saveOrUpdateTable(EamMatrixTableSaveVO vo) {
        EamMatrixInstance instance = instancePrivateDao.getById(vo.getId());
        BinaryUtils.checkNull(instance, "矩阵表格");
        EamMatrixStencil stencil = stencilSvc.getById(instance.getMatrixId());
        BinaryUtils.checkNull(stencil, "矩阵制品");
        //if(CollectionUtils.isEmpty(vo.getTable())){
        //    return instance.getId();
        //}
        matrixDataSvc.updateTableInfo(instance, vo.getTable(), stencil, instance.getOwnerCode());
        return instancePrivateDao.saveOrUpdate(instance);
    }

    @Override
    public EamMatrixInstance getBaseInfoById(Long id, LibType libType) {
        if(LibType.PRIVATE.equals(libType)){
            return instancePrivateDao.getById(id);
        }else{
            return instanceDesignDao.getById(id);
        }
    }

    @Override
    public Page<EamMatrixInstance> getSortListByQuery(QueryBuilder query, int pageNum, int pageSize, String sortField, LibType libType) {
        Page<EamMatrixInstance> page;
        if(LibType.PRIVATE.equals(libType)){
            page = instancePrivateDao.getSortListByQuery(pageNum, pageSize, query, sortField, false);
        }else{
            page = instanceDesignDao.getSortListByQuery(pageNum, pageSize, query, sortField, false);
        }
        return page;
    }

    @Override
    public EamMatrixTableSaveVO getTableInfo(EamMatrixQueryVO queryVO) {
        EamMatrixInstance instance = this.getBaseInfoById(queryVO.getId(), queryVO.getLibType());
        BinaryUtils.checkNull(instance, "矩阵表格");
        EamMatrixStencil stencil;
        if(LibType.PRIVATE.equals(queryVO.getLibType())){
            if(!SysUtil.getCurrentUserInfo().getLoginCode().equals(instance.getOwnerCode())){
                throw new BusinessException("您没有权限操作该矩阵表格");
            }
            stencil = stencilSvc.getById(instance.getMatrixId());
        }else{
            stencil = stencilSvc.getHistoryById(instance.getMatrixId(), instance.getMatrixVersion());
        }
        BinaryUtils.checkNull(stencil, "矩阵制品");

        EamMatrixTableSaveVO result = new EamMatrixTableSaveVO(queryVO.getId(), instance.getName(), instance.getMatrixId());
        result.setOwnerCode(instance.getOwnerCode());
        //组装表头
        result.setHeader(this.getHeader(stencil, queryVO.getLibType()));
        //组装表格数据
        Page<EamMatrixInstanceData> tableData = matrixDataSvc.getTableData(queryVO);
        result.setTable(tableData.getData());
        result.setPageNum(tableData.getPageNum());
        result.setPageSize(tableData.getPageSize());
        result.setTotalRows(tableData.getTotalRows());
        result.setTotalPages(tableData.getTotalPages());
        return result;
    }

    /**
     * 表格表头组装
     * @param stencil 矩阵制品
     * @return 表头
     */
    private List<EamMatrixHeaderVO> getHeader(EamMatrixStencil stencil, LibType libType){
        List<EamMatrixHeaderVO> header = new ArrayList<>();
        if(!BinaryUtils.isEmpty(stencil.getDisplayNum()) && stencil.getDisplayNum()){
            EamMatrixHeaderVO headerVO = new EamMatrixHeaderVO();
            headerVO.setName("序号");
            headerVO.setClassId(1L);
            header.add(headerVO);
        }
        if(LibType.PRIVATE.equals(libType)){
            Map<Long, CcCiAttrDef> sourceDefMap = ciClassApiSvc.getAttrDefMap(stencil.getSourceClass());
            this.getHeaderPrivate(stencil.getSourceAttrs(), sourceDefMap, stencil.getSourceClass(), header, false);
            Map<Long, CcCiAttrDef> targetDefMap = ciClassApiSvc.getAttrDefMap(stencil.getTargetClass());
            this.getHeaderPrivate(stencil.getTargetAttrs(), targetDefMap, stencil.getTargetClass(), header, false);
            CcCiClassInfo rltClass = rltClassApiSvc.getRltClassById(stencil.getRltClass());
            Map<Long, CcCiAttrDef> rltDefMap = rltClass.getAttrDefs().stream().collect(Collectors.toMap(CcCiAttrDef::getId, e -> e, (v1, v2) -> v2));
            this.getHeaderPrivate(stencil.getRltAttrs(), rltDefMap, stencil.getRltClass(), header, true);
        } else {
            this.getHeaderDesign(stencil.getSourceAttrs(), stencil.getSourceAttrMap(), stencil.getSourceClass(), header, false);
            this.getHeaderDesign(stencil.getTargetAttrs(), stencil.getTargetAttrMap(), stencil.getTargetClass(), header, false);
            this.getHeaderDesign(stencil.getRltAttrs(), stencil.getRltAttrMap(), stencil.getRltClass(), header, true);
        }
        return header;
    }

    /**
     * 根据属性id获取属性定义
     * @param defIds 属性定义id集合
     * @param defMap 字段map
     * @param classId 分类id
     * @param header 表头
     */
    private void getHeaderPrivate(List<Long> defIds, Map<Long, CcCiAttrDef> defMap, Long classId, List<EamMatrixHeaderVO> header, boolean isRlt) {
        if(CollectionUtils.isEmpty(defIds)){
            return;
        }
        for (Long defId : defIds) {
            CcCiAttrDef attrDef = defMap.get(defId);
            BinaryUtils.checkNull(attrDef, "矩阵表格属性:defId=" + defId + ",classId=" + classId);
            EamMatrixHeaderVO headerVO = new EamMatrixHeaderVO();
            headerVO.setClassId(classId);
            headerVO.setAttrDefId(defId);
            headerVO.setIsRlt(isRlt);
            headerVO.setName(attrDef.getProName());
            headerVO.setIsMajor(attrDef.getIsMajor());
            headerVO.setDefVal(attrDef.getDefVal());
            headerVO.setProType(attrDef.getProType());
            headerVO.setIsRequired(attrDef.getIsRequired());
            if(attrDef.getProType().equals(8) || attrDef.getProType().equals(11) || attrDef.getProType().equals(18)){
                headerVO.setProDropSourceDef(attrDef.getProDropSourceDef());
            }
            if(attrDef.getProType().equals(16)){
                headerVO.setConstraintRule(attrDef.getConstraintRule());
            }
            header.add(headerVO);
        }
    }

    /**
     * 根据属性id获取属性定义
     * @param defIds 属性定义id集合
     * @param defMap 字段map
     * @param classId 分类id
     * @param header 表头
     */
    private void getHeaderDesign(List<Long> defIds, Map<Long, EamMatrixStencilAttr> defMap, Long classId, List<EamMatrixHeaderVO> header, boolean isRlt){
        if(CollectionUtils.isEmpty(defIds)){
            return;
        }
        for (Long defId : defIds) {
            EamMatrixHeaderVO headerVO = new EamMatrixHeaderVO();
            headerVO.setClassId(classId);
            headerVO.setAttrDefId(defId);
            headerVO.setIsRlt(isRlt);
            EamMatrixStencilAttr attrDef = defMap.get(defId);
            headerVO.setName(attrDef.getName());
            headerVO.setProType(attrDef.getType());
            if(attrDef.getType().equals(8) || attrDef.getType().equals(11) || attrDef.getType().equals(18)){
                headerVO.setProDropSourceDef(attrDef.getProDrop());
            }
            if(attrDef.getType().equals(16)){
                headerVO.setConstraintRule(attrDef.getRule());
            }
            header.add(headerVO);
        }
    }

    @Override
    public Integer saveOrUpdateBatchMatrix(List<EamMatrixInstance> matrixInstances, LibType libType) {
        return getPriOrDesDao(libType).saveOrUpdateBatch(matrixInstances);
    }

    @Override
    public List<EamMatrixInstance> selectByMatrixIds(List<Long> matrixIds, LibType libType) {
        return getPriOrDesDao(libType).getListByQuery(QueryBuilders.termsQuery("id", matrixIds));
    }

    @Override
    public void deleteBatchMatrix(List<EamMatrixInstance> matrixInstances, long dirId, LibType libType, int delType) {
        //判断是否为空
        if (BinaryUtils.isEmpty(matrixInstances)) {
            return;
        }
        //定义矩阵id集合
        List<Long> matrixIds;
        //判断是逻辑删还是物理删除
        if (delType == 1) {
            //逻辑删除 将原来所在的文件夹赋值给OLD_DIR_ID
            matrixInstances.forEach(e ->{
                e.setStatus(StatusConstant.DISABLE);
                e.setOldDirId(e.getDirId());
                e.setDirId(0L);
            });
            getPriOrDesDao(libType).saveOrUpdateBatch(matrixInstances);
        }else {
            //物理删除
            //查询矩阵表格行数据
            matrixIds = matrixInstances.stream()
                    .map(EamMatrixInstance::getId).collect(Collectors.toList());
            deleteBatchByMatrixIdsPhysics(libType, matrixIds);
        }
    }

    /**
     * 物理删除矩阵表格及关联数据
     *
     * @param libType 库
     * @param matrixIds 矩阵ids
     */
    @Override
    public void deleteBatchByMatrixIdsPhysics(LibType libType, List<Long> matrixIds) {
        //查询表格数据
        List<EamMatrixInstanceData> instanceDatas = matrixDataSvc.selectByTableIds(matrixIds, libType);
        //判断查询的是否有数据
        if (BinaryUtils.isEmpty(instanceDatas)) {
            //如果没有数据则可能是空表格则进行处理并返回
            getPriOrDesDao(libType).deleteByIds(matrixIds);
            cleanPrivateMatrixPublishInfo(libType, matrixIds);
            return;
        }
        //过滤出tableDataID
        List<Long> tableDataIds = instanceDatas.stream()
                .map(EamMatrixInstanceData::getId).collect(Collectors.toList());
        //删除表格行数据 矩阵表格数据
        // TODO ci对象数据 关系数据是否删除
        getPriOrDesDao(libType).deleteByIds(matrixIds);
        matrixDataSvc.deleteBatchTableDataByIds(tableDataIds, libType);
        cleanPrivateMatrixPublishInfo(libType, matrixIds);
    }

    /**
     * 私有库矩阵发布信息清除
     * @param libType
     * @param matrixIds
     */
    private void cleanPrivateMatrixPublishInfo(LibType libType, List<Long> matrixIds) {
        if (!LibType.DESIGN.equals(libType)) {
            return;
        }
        TermsQueryBuilder query = QueryBuilders.termsQuery("publishId", matrixIds);
        List<EamMatrixInstance> privateMatrixInstances = getPriOrDesDao(LibType.PRIVATE).getListByQueryScroll(query);
        if (!CollectionUtils.isEmpty(privateMatrixInstances)) {
            for (EamMatrixInstance privateMatrixInstance : privateMatrixInstances) {
                privateMatrixInstance.setPublished(0);
                privateMatrixInstance.setReleasePath(null);
                privateMatrixInstance.setPublishId(null);
            }
            getPriOrDesDao(LibType.PRIVATE).deleteByQuery(query, true);
            getPriOrDesDao(LibType.PRIVATE).saveOrUpdateBatch(privateMatrixInstances);
        }
    }

    @Override
    public Long renameMatrix(RenameRequestDTO renameRequestDTO) {
        //获取库
        LibType libType = renameRequestDTO.getLibType();
        //对重命名后的名字进行查询是否有重复的
        //validRename(renameRequestDTO.getRename(), libType);
        //查询矩阵信息
        EamMatrixInstance instance = getPriOrDesDao(libType)
                .getById(renameRequestDTO.getId());
        //为空返回
        if (BinaryUtils.isEmpty(instance)) {
            throw new BusinessException(ResultCodeEnum.MATRIX_NOT_FOUND);
        }
        //名字重新赋值
        instance.setName(renameRequestDTO.getRename());
        //更新
        return getPriOrDesDao(libType).saveOrUpdate(instance);
    }

    @Override
    public List<EamMatrixInstance> queryMatrixByDirIds(Set<Long> dirIds, LibType libType) {

        List<EamMatrixInstance> eamMatrixInstances = null;
        if (CollectionUtils.isEmpty(dirIds)) {
            return eamMatrixInstances;
        }
        //构建查询条件
        BoolQueryBuilder dirBuilder = QueryBuilders.boolQuery();
        //查询需要区分不同的库信息
        if (LibType.PRIVATE.equals(libType)) {
            dirBuilder.must(QueryBuilders.termsQuery("dirId", dirIds));
            dirBuilder.must(QueryBuilders.termQuery("status", StatusConstant.ENABLE));
        }else {
            dirBuilder.must(QueryBuilders.termsQuery("dirId", dirIds));
            dirBuilder.must(QueryBuilders.termQuery("published", StatusConstant.PUBLISHED));
        }
        eamMatrixInstances = getPriOrDesDao(libType).getListByQuery(dirBuilder);
        return eamMatrixInstances;
    }

    private AbstractESBaseDao<EamMatrixInstance, EamMatrixInstance> getPriOrDesDao(LibType libType){
        if (LibType.PRIVATE.equals(libType)){
            return instancePrivateDao;
        }else {
            return instanceDesignDao;
        }
    }

    @Override
    public Page<ESCIInfo> getCiByClass(ESAttrAggScreenBean query) {
        BinaryUtils.checkEmpty(query.getOwnerCode(), "ownerCode");
        //优先查询私有库
        ESCISearchBean bean = new ESCISearchBean();
        bean.setClassIds(Collections.singletonList(query.getClassId()));
        bean.setOwnerCode(query.getOwnerCode());
        bean.setPageNum(query.getPageNum());
        bean.setPageSize(query.getPageSize());
        bean.setStates(Lists.newArrayList(CIState.CREATE_PENDING, CIState.CREATE_COMPLETE));
        this.setRules(query.getBindId(), query.getClassId(), bean);
        if(!BinaryUtils.isEmpty(query.getLike())){
            bean.setWordLabel(true);
            bean.setWords(Lists.newArrayList(query.getLike()));
        }
        bean.setPermission(true);
        Page<ESCIInfo> page = ciSwitchSvc.searchESCIByBean(bean, LibType.PRIVATE);
        log.info("矩阵数据查询>>>私有库数据共【{}】条", page.getTotalRows());
        if(CollectionUtils.isEmpty(page.getData())){
            page.setData(new ArrayList<>());
        }
        Set<String> privateCodes = queryPrivateCiCodes(EamUtil.copy(bean, ESCISearchBean.class), page.getTotalRows());
        bean.setOwnerCode(null);
        bean.setNotCiCodes(new ArrayList<>(privateCodes));
        //手动分页
        Page<ESCIInfo> designPage;
        int size = page.getData().size();
        if(size >= query.getPageSize() && page.getTotalPages() >= query.getPageNum()){
            bean.setPageNum(1);
            designPage = ciSwitchSvc.searchESCIByBean(bean, LibType.DESIGN);
        }else{
            int pageNum;
            if(page.getTotalRows() == 0){
                pageNum = query.getPageNum();
            }else if(page.getData().isEmpty()){
                pageNum = query.getPageNum() - (int)page.getTotalPages();
            }else{
                pageNum = 1;
            }
            bean.setPageNum(pageNum);
            bean.setPageSize(query.getPageSize() - size);
            designPage = ciSwitchSvc.searchESCIByBean(bean, LibType.DESIGN);
            if(!CollectionUtils.isEmpty(designPage.getData())){
                page.getData().addAll(designPage.getData());
            }
        }
        log.info("矩阵数据查询>>>设计库数据共【{}】条", designPage.getTotalRows());
        page.setPageNum(query.getPageNum());
        page.setPageSize(query.getPageSize());
        //总条数及总页数合并设计库
        page.setTotalRows(page.getTotalRows() + designPage.getTotalRows());
        BigDecimal totalRows = new BigDecimal(page.getTotalRows());
        BigDecimal pageSize = new BigDecimal(query.getPageSize());
        BigDecimal quotient = totalRows.divide(pageSize, 0, RoundingMode.UP);
        page.setTotalPages(quotient.longValue());
        return page;
    }

    /**
     * 设置约束规则
     * @param id 制品id
     * @param bean 查询条件
     */
    private void setRules(Long id, Long classId, ESCISearchBean bean) {
        if(BinaryUtils.isEmpty(id)){
           return;
        }
        EamMatrixStencil stencil = stencilSvc.getById(id);
        if(stencil == null || !StatusConstant.PUBLISHED.equals(stencil.getPublished())){
            return;
        }
        ESCIClassInfo classInfo = ciClassApiSvc.queryClassById(classId);
        Map<Long, String> defMap = classInfo.getAttrDefs().stream().collect(Collectors.toMap(CcCiAttrDef::getId, CcCiAttrDef::getProName, (v1, v2) -> v1));
        if(stencil.getSourceClass().equals(classId) && !CollectionUtils.isEmpty(stencil.getSourceRules())){
            for (RelationRuleAttrCdt rule : stencil.getSourceRules()) {
                ESAttrBean attr = new ESAttrBean();
                attr.setKey(defMap.get(rule.getAttrId()));
                attr.setOptType(rule.getOp().getOp());
                attr.setValue(rule.getValue());
                bean.getAndAttrs().add(attr);
            }
        }else if(stencil.getTargetClass().equals(classId) && !CollectionUtils.isEmpty(stencil.getTargetRules())){
            for (RelationRuleAttrCdt rule : stencil.getTargetRules()) {
                ESAttrBean attr = new ESAttrBean();
                attr.setKey(defMap.get(rule.getAttrId()));
                attr.setOptType(rule.getOp().getOp());
                attr.setValue(rule.getValue());
                bean.getAndAttrs().add(attr);
            }
        }
    }

    private Set<String> queryPrivateCiCodes(ESCISearchBean bean, long rows){
        if(rows == 0){
            return Collections.emptySet();
        }
        bean.setPageNum(1);
        bean.setPageSize((int) rows);
        Page<ESCIInfo> page = ciSwitchSvc.searchESCIByBean(bean, LibType.PRIVATE);
        if(page.getTotalRows() == 0){
            return Collections.emptySet();
        }
        return page.getData().stream().map(ESCIInfo::getCiCode).collect(Collectors.toSet());
    }

    @Override
    public Map<String, String> getRltByCode(EamMatrixTableQuery query) {
        BinaryUtils.checkEmpty(query.getTableId(), "矩阵制品");
        BinaryUtils.checkEmpty(query.getSourceCiCode(), "源端code");
        BinaryUtils.checkEmpty(query.getTargetCiCode(), "目标端code");
        EamMatrixStencil stencil = stencilSvc.getById(query.getTableId());
        BinaryUtils.checkNull(stencil, "矩阵制品");
        String rltCode = query.getSourceCiCode() + "_" + stencil.getRltClass() + "_" + query.getTargetCiCode();
        List<CcCiRltInfo> rltList = rltSwitchSvc.getRltByCode(rltCode, query.getOwnerCode(), LibType.PRIVATE);
        if(CollectionUtils.isEmpty(rltList)){
            //查设计库
            rltList = rltSwitchSvc.getRltByCode(rltCode, null, LibType.DESIGN);
        }
        if(CollectionUtils.isEmpty(rltList)){
            return Collections.emptyMap();
        }
        return rltList.get(0).getAttrs();
    }

    @Override
    public List<EamMatrixInstance> queryByPublishId(Long id, String ownerCode, LibType libType) {
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("publishId", id));
        if(!BinaryUtils.isEmpty(ownerCode)){
            query.must(QueryBuilders.termQuery("ownerCode.keyword", ownerCode));
        }
        return getPriOrDesDao(libType).getListByQuery(query);
    }

    @Override
    public List<EamMatrixInstance> getTableHistory(Long id) {
        List<EamMatrixInstance> result = this.queryByPublishId(id, null, LibType.DESIGN);
        if(CollectionUtils.isEmpty(result)){
            return Collections.emptyList();
        }
        Set<String> userCodes = result.stream().map(EamMatrixInstance::getCreator).collect(Collectors.toSet());
        Map<String, String> userMap = userApiSvc.getNameByCodes(userCodes);
        result.forEach(each -> each.setCreator(userMap.getOrDefault(each.getCreator(), each.getCreator())));
        //按version字段倒序排序
        result.sort(Comparator.comparingInt(EamMatrixInstance::getVersion).reversed());
        return result;
    }

    @Override
    public Object getTableData(EamMatrixTableQuery query) {
        BinaryUtils.checkEmpty(query.getId(), "矩阵行id");
        BinaryUtils.checkEmpty(query.getCode(), "数据标识");
        EamMatrixInstanceData data = matrixDataSvc.getById(query.getId(), query.getLibType());
        if(data == null){
            return null;
        }
        if(query.getCode().equals(data.getSourceCiCode())){
            return ciSwitchSvc.queryByCode(new CiInfoQueryVo(query.getCode(), data.getSourceVersion(), query.getOwnerCode(), query.getLibType()));
        }else if(query.getCode().equals(data.getTargetCiCode())){
            return ciSwitchSvc.queryByCode(new CiInfoQueryVo(query.getCode(), data.getTargetVersion(), query.getOwnerCode(), query.getLibType()));
        }else{
            CcCiRltInfo rltInfo = rltSwitchSvc.queryByCode(new CiInfoQueryVo(query.getCode(), data.getRltVersion(), query.getOwnerCode(), query.getLibType()));
            VcCiRltInfo rltInfoVo = new VcCiRltInfo();
            if(rltInfo != null){
                CcCiInfo source = ciSwitchSvc.queryByCode(new CiInfoQueryVo(data.getSourceCiCode(), data.getSourceVersion(), query.getOwnerCode(), query.getLibType()));
                CcCiInfo target = ciSwitchSvc.queryByCode(new CiInfoQueryVo(data.getTargetCiCode(), data.getTargetVersion(), query.getOwnerCode(), query.getLibType()));
                CcCiClassInfo rltClass = rltClassApiSvc.getRltClassById(rltInfo.getCiRlt().getClassId());
                if(rltClass != null){
                    rltInfoVo.setRltClassInfo(EamUtil.copy(rltClass, VcCiClassInfo.class));
                }
                rltInfoVo.setCiRlt(rltInfo.getCiRlt());
                rltInfoVo.setAttrs(rltInfo.getAttrs());
                rltInfoVo.setSourceCiInfo(source);
                rltInfoVo.setTargetCiInfo(target);
            }
            return rltInfoVo;
        }
    }
}
