package com.uinnova.product.eam.service.cj.service.impl;

import com.binary.core.util.BinaryUtils;
import com.uinnova.product.eam.base.util.EamUtil;
import com.uinnova.product.eam.model.cj.domain.ChapterInstance;
import com.uinnova.product.eam.model.cj.domain.PlanChapterCollaborate;
import com.uinnova.product.eam.model.cj.domain.PlanDesignInstance;
import com.uinnova.product.eam.model.cj.enums.PlanSharePermissionEnum;
import com.uinnova.product.eam.model.cj.vo.ChapterContextVO;
import com.uinnova.product.eam.model.cj.vo.PlanChapterVO;
import com.uinnova.product.eam.model.cj.vo.SharedUserVO;
import com.uinnova.product.eam.service.cj.dao.PlanDesignInstanceDao;
import com.uinnova.product.eam.service.cj.service.*;
import com.uino.util.sys.SysUtil;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 方案章节接口实现类扩展
 * <AUTHOR>
 */
@Service
public class EamPlanChapterSvcImpl implements EamPlanChapterSvc {

    @Resource
    private PlanDesignInstanceDao planDesignInstanceDao;
    @Resource
    private PlanChapterInstanceService chapterInstanceService;
    @Resource
    private ChapterContextService chapterContextService;
    @Resource
    private PlanChapterCollaborateService collaborateService;
    @Resource
    private ShareService shareService;

    @Override
    public List<PlanChapterVO> getFullPlanContext(Long id, String processInstanceId, String like) {
        PlanDesignInstance plan = planDesignInstanceDao.getById(id);
        List<ChapterInstance> chapterList = chapterInstanceService.getChapterByPlanId(id, new String[]{like});
        if(CollectionUtils.isEmpty(chapterList)){
            return Collections.emptyList();
        }
        //协作信息
        Map<Long, PlanChapterCollaborate> collaborateMap = collaborateService.getByPlanId(id);

        Map<Long, List<ChapterInstance>> chapterGroup = chapterList.stream().collect(Collectors.groupingBy(ChapterInstance::getParentId));

        boolean checkShare = this.checkCollaborate(plan);

        //章节相信信息
        List<ChapterContextVO> chapterContextList = chapterContextService.findByPlanId(id, processInstanceId);
        if(!BinaryUtils.isEmpty(like)){
            chapterContextList = createChapterList(chapterContextList, like);
        }
        Map<Long, ChapterContextVO> contextMap = chapterContextList.stream().collect(Collectors.toMap(ChapterContextVO::getChapterId, e -> e, (k1, k2) -> k2));

        return this.createChapterVO(chapterGroup.get(0L), chapterGroup, collaborateMap, contextMap, checkShare);
    }

    private List<ChapterContextVO> createChapterList(List<ChapterContextVO> chapterContextVOs, String likeName){
        Map<Long, ChapterContextVO> instanceNameLikeMap = new HashMap<>();
        Map<Long, ChapterContextVO> instanceMap = new HashMap<>();
        List<Long> instanceIds = new ArrayList<>();
        chapterContextVOs.forEach(chapterContextVO ->{
            if (chapterContextVO.getName().contains(likeName)){
                instanceNameLikeMap.put(chapterContextVO.getChapterId(), chapterContextVO);
                instanceIds.add(chapterContextVO.getChapterId());
            }
            instanceMap.put(chapterContextVO.getChapterId(), chapterContextVO);
        });

        instanceIds.forEach(instanceId ->{
            collectChapterListByParentId(instanceNameLikeMap, instanceMap,instanceId);
        });
        return new ArrayList<>(instanceNameLikeMap.values());
    }

    private void collectChapterListByParentId(Map<Long, ChapterContextVO> chapterContextVOs, Map<Long, ChapterContextVO> instanceMap, long instanceId){
        if (instanceMap.get(instanceId) != null){
            ChapterContextVO chapterInstance = instanceMap.get(instanceId);
            Long parentId = chapterInstance.getParentId();
            if(parentId != 0) {
                chapterContextVOs.put(parentId, instanceMap.get(parentId));
                collectChapterListByParentId(chapterContextVOs, instanceMap, parentId);
            }
        }
    }

    private List<PlanChapterVO> createChapterVO(List<ChapterInstance> chapterList, Map<Long, List<ChapterInstance>> chapterGroup, Map<Long, PlanChapterCollaborate> collaborateMap,
                                                Map<Long, ChapterContextVO> contextMap, boolean checkShare) {
        if(CollectionUtils.isEmpty(chapterList)){
            return Collections.emptyList();
        }
        chapterList.sort(((x, y) -> {
            BigDecimal a = new BigDecimal(x.getOrderId().toString());
            BigDecimal b = new BigDecimal(y.getOrderId().toString());
            return a.compareTo(b);
        }));
        String loginCode = SysUtil.getCurrentUserInfo().getLoginCode();
        List<PlanChapterVO> result = new ArrayList<>();
        for (ChapterInstance chapter : chapterList) {
            PlanChapterVO vo = EamUtil.copy(chapter, PlanChapterVO.class);
            vo.setFullName(String.format("%s %s", vo.getSerialNum(), vo.getName()));
            vo.setExpand(true);
            vo.setContext(contextMap.get(chapter.getId()));

            // 判断章节是否存在协作
            PlanChapterCollaborate collaborate = collaborateMap.get(chapter.getId());
            if (collaborate != null && !CollectionUtils.isEmpty(collaborate.getShareMemberList())) {
                vo.setExistCollaborate(1);
            } else {
                vo.setExistCollaborate(0);
            }
            //协作信息
            if(collaborate != null && Objects.equals(collaborate.getComplete(), 1) &&
                    (checkShare || (!CollectionUtils.isEmpty(collaborate.getShareMemberList()) && collaborate.getShareMemberList().contains(loginCode)))){
                vo.setCompleteCollaborate(1);
                vo.setButtonSign(1);
            }
            List<PlanChapterVO> childChapterList = createChapterVO(chapterGroup.get(chapter.getId()), chapterGroup, collaborateMap, contextMap, checkShare);
            vo.setChildChapterList(childChapterList);
            result.add(vo);
        }
        return result;
    }

    /**
     * 判断是否已完成章节协作
     */
    private boolean checkCollaborate(PlanDesignInstance plan){
        String loginCode = SysUtil.getCurrentUserInfo().getLoginCode();
        if(plan.getCreatorCode().equals(loginCode)){
            return true;
        }
        List<SharedUserVO> sharedList = shareService.findPlanSharedList(plan.getId(), false);
        if(CollectionUtils.isEmpty(sharedList)){
            return false;
        }
        List<String> collect = sharedList.stream()
                .filter(e -> PlanSharePermissionEnum.EDIT_AND_SHARE.getFlag().equals(e.getPermission())
                        || PlanSharePermissionEnum.PUBLISH.getFlag().equals(e.getPermission()))
                .map(SharedUserVO::getLoginCode).filter(Objects::nonNull).collect(Collectors.toList());
        return collect.contains(loginCode);
    }

}
