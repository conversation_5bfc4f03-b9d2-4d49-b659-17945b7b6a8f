package com.uinnova.product.eam.web.diagram.bean;

import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;

public class DiagramPlazaCdt implements Condition {
	private static final long serialVersionUID = 1L;

	@Comment("查询条件")
	private String like;
	
	@Comment("类型")
	private Integer type;
	
	@Comment("筛选类型[0:默认,1=视图,2=组合视图,3=我收藏的]")
	private Integer filterType;
	
	public Integer getFilterType() {
		return filterType;
	}

	public void setFilterType(Integer filterType) {
		this.filterType = filterType;
	}

	public String getLike() {
		return like;
	}

	public void setLike(String like) {
		this.like = like;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

}
