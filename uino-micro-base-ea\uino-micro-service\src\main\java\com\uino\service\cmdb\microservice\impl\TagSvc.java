package com.uino.service.cmdb.microservice.impl;

import java.util.*;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSONObject;
import com.uino.bean.cmdb.base.*;
import com.uino.dao.BaseConst;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.binary.core.util.BinaryUtils;
import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.comm.util.CommUtil;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uino.bean.cmdb.business.ClassNodeInfo;
import com.uino.bean.cmdb.query.ESAttrAggBean;
import com.uino.bean.cmdb.query.ESTagSearchBean;
import com.uino.bean.permission.base.SysUser;
import com.uino.dao.cmdb.ESCIClassSvc;
import com.uino.dao.cmdb.ESCISvc;
import com.uino.dao.cmdb.ESCmdbCommSvc;
import com.uino.dao.cmdb.ESDirSvc;
import com.uino.dao.cmdb.ESTagSvc;
import com.uino.dao.util.ESUtil;
import com.uino.service.cmdb.microservice.ITagSvc;
import com.uino.util.sys.SysUtil;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

/**
 * ES-TAG
 * 
 * <AUTHOR>
 *
 */
@Service
@Slf4j
public class TagSvc implements ITagSvc {
	@Autowired
	private ESTagSvc tagSvc;

	@Autowired
	private ESDirSvc dirSvc;

	@Autowired
	private ESCIClassSvc classSvc;

	@Autowired
	private ESCISvc ciSvc;

	@Autowired
	private ESCmdbCommSvc commSvc;

	@Override
	public Long saveOrUpdateCITagRule(ESCITagInfo tagInfo) {
		this.saveValid(tagInfo);
		return tagSvc.saveOrUpdate(tagInfo);
	}

	@Override
	public ESCITagInfo getCITagRuleById(Long id) {
		return tagSvc.getById(id);
	}

	@Override
	public List<ClassNodeInfo> getTagTree(Long domainId) {
		domainId = domainId == null ? BaseConst.DEFAULT_DOMAIN_ID : domainId;
		List<ClassNodeInfo> res = new ArrayList<>();
		// 获取标签文件夹
		List<CcCiClassDir> dirs = dirSvc.getSortListByQuery(1, 9999,
				QueryBuilders.boolQuery().must(QueryBuilders.termQuery("ciType", 4)).must(QueryBuilders.termQuery("domainId",domainId))
				, "createTime", true).getData();
		// 获取标签列表
		List<ESCITagInfo> tags = tagSvc.getSortListByQuery(1, 9999,
				QueryBuilders.boolQuery().must(QueryBuilders.termQuery("tagType", 1)).must(QueryBuilders.termQuery("domainId",domainId))
				, "createTime", true).getData();
		Map<Object, List<ESCITagInfo>> tagMap = BinaryUtils.toObjectGroupMap(tags, "dirId");
		// 组装标签展示结构
		for (CcCiClassDir dir : dirs) {
			ClassNodeInfo dirNode = ClassNodeInfo.builder().id(dir.getId()).name(dir.getDirName()).type("dir").build();
			List<ESCITagInfo> tempTags = tagMap.get(dir.getId());
			if (tempTags != null) {
				for (ESCITagInfo tag : tempTags) {
					dirNode.getChildren()
							.add(ClassNodeInfo.builder().id(tag.getId()).name(tag.getTagName()).type("tag").build());
				}
			}
			res.add(dirNode);
		}
		return res;
	}

	@Override
	public Page<CcCiInfo> getCIInfoListByTag(ESTagSearchBean bean) {
		ESCITagInfo tagInfo = bean.getTagInfo();
		List<ESTagRuleInfo> rules = tagInfo.getRules();
		tagInfo.setDomainId(tagInfo.getDomainId() == null ? BaseConst.DEFAULT_DOMAIN_ID : tagInfo.getDomainId());
		if (BinaryUtils.isEmpty(rules)) {
			return new Page<>(bean.getPageNum(), bean.getPageSize(), 0, 0, new ArrayList<>());
		}
		List<ESTagRuleItem> itemsAll = new ArrayList<ESTagRuleItem>();
		Set<Long> attrIds = new HashSet<>();
		rules.forEach(rule -> {
			rule.valid();
			rule.getItemGroups().forEach(group -> {
				group.valid();
				group.getItems().forEach(item -> {
					item.valid();
					itemsAll.add(item);
					attrIds.add(item.getClassAttrId());
				});
			});
		});
		// 校验规则是否规范
		validateAttr(attrIds, itemsAll);
		QueryBuilder query = tagSvc.getQueryByTag(tagInfo);
		if (query == null) {
			return new Page<>(bean.getPageNum(), bean.getPageSize(), 0, 0, new ArrayList<>());
		}
		Page<ESCIInfo> page = ciSvc.getSortListByQuery(bean.getPageNum(), bean.getPageSize(), query, "createTime",
				true);
		return commSvc.transEsInfoPage(page, false);
	}

	@Override
	public Integer deleteById(Long tagId) {
		return tagSvc.deleteById(tagId);
	}

	@Override
	public Page<String> getAttrValuesBySearchBean(Long domainId, ESAttrAggBean searchBean) {
		domainId = domainId == null ? BaseConst.DEFAULT_DOMAIN_ID: domainId;
		Long classId = searchBean.getClassId();
		String field = searchBean.getAttrName();
		Assert.notNull(classId, "X_PARAM_NOT_NULL${name:classId}");
		Assert.notNull(field, "X_PARAM_NOT_NULL${name:attrName}");
		// searchBean.setAttrName("attrs." + searchBean.getAttrName());
		return ciSvc.queryAttrVal(domainId, searchBean);
	}

	@Override
	public Boolean changeTagDir(ESCITagInfo tagInfo) {
		Long id = tagInfo.getId();
		Long dirId = tagInfo.getDirId();
		Long domainId = tagInfo.getDomainId() == null ? BaseConst.DEFAULT_DOMAIN_ID : tagInfo.getDomainId();
		Assert.notNull(id, "X_PARAM_NOT_NULL${name:tagId}");
		Assert.notNull(dirId, "X_PARAM_NOT_NULL${name:dirId}");
		return tagSvc.updateByQuery(QueryBuilders.boolQuery().must(QueryBuilders.termQuery("id", id))
				.must(QueryBuilders.termQuery("domainId",domainId)), "ctx._source.dirId=" + dirId + "L", true);
	}

	private void saveValid(ESCITagInfo tagInfo) {
		tagInfo.valid();
		boolean isAdd = tagInfo.getId() == null;
		tagInfo.setDomainId(tagInfo.getDomainId()==null? BaseConst.DEFAULT_DOMAIN_ID:tagInfo.getDomainId());
		// 校验重复
		BoolQueryBuilder query = QueryBuilders.boolQuery();
		query.must(QueryBuilders.termQuery("domainId",tagInfo.getDomainId()));
		query.must(QueryBuilders.termQuery("tagType", 1));
		query.must(QueryBuilders.termQuery("tagName.keyword", tagInfo.getTagName()));
		if (!isAdd) {
			query.mustNot(QueryBuilders.termQuery("id", tagInfo.getId()));
		}
		List<ESCITagInfo> tags = tagSvc.getListByQuery(query);
		Assert.isTrue(BinaryUtils.isEmpty(tags), "标签名称重复");

		// 组织规则条件
		long tagId = isAdd ? ESUtil.getUUID() : tagInfo.getId();
		tagInfo.setId(tagId);
		List<ESTagRuleInfo> rules = tagInfo.getRules();
		List<ESTagRuleItem> itemsAll = new ArrayList<ESTagRuleItem>();
		Set<Long> attrIds = new HashSet<>();
		for (ESTagRuleInfo rule : rules) {
			rule.valid();
			long ruleId = ESUtil.getUUID();
			rule.setTagId(tagId);
			rule.setOrderNo(rules.indexOf(rule) + 1);
			List<ESTagRuleItemGroup> itemGroups = rule.getItemGroups();
			for (ESTagRuleItemGroup group : itemGroups) {
				group.valid();
				for (ESTagRuleItem item : group.getItems()) {
					item.valid();
					itemsAll.add(item);
					attrIds.add(item.getClassAttrId());
					item.setTagId(tagId);
					item.setRuleId(ruleId);
					item.setClassId(rule.getClassId());
					item.setOrderNo(group.getItems().indexOf(item) + 1);
					item.setRuleType(item.getRuleType() == null ? 1 : item.getRuleType());
				}
			}
		}
		// 校验规则是否规范
		validateAttr(attrIds, itemsAll);
		// 填充必要属性
		if (isAdd) {
			// 因为保存前丰富了id属性，此处手动添加creator
			try {
				SysUser loginUser = SysUtil.getCurrentUserInfo();
				tagInfo.setCreator(loginUser.getLoginCode());
			} catch (Exception e) {
				log.error("获取登陆用户失败，所有值取默认值");
				tagInfo.setCreator("system");
			}
			tagInfo.setBuildStatus(1);
			tagInfo.setIsValid(1);
			tagInfo.setTagType(tagInfo.getTagType() == null ? 1 : tagInfo.getTagType());
		}
	}

	private void validateAttr(Set<Long> attrIds, List<ESTagRuleItem> items) {
		List<ESCIClassInfo> cls = classSvc.getListByQuery(QueryBuilders.termsQuery("attrDefs.id", attrIds));
		Assert.notEmpty(cls, "BS_MNAME_CLASS_NOT_EXSIT");
		List<CcCiAttrDef> defs = new ArrayList<>();
		cls.forEach(ciClass -> {
			defs.addAll(ciClass.getCcAttrDefs());
		});
		// ClassAttrId = id
		Map<Long, CcCiAttrDef> map = BinaryUtils.toObjectMap(defs, "id");
		// 1=整数 2=小数 3=短文本(<=200) 4=长文本(<=1000) 5=文章 6=枚举 7=日期
		for (int i = 0; i < items.size(); i++) {
			ESTagRuleItem ruleItem = items.get(i);
			CcCiAttrDef attrDef = map.get(ruleItem.getClassAttrId());
			Assert.isTrue(attrDef != null, "属性不存在");
			//标签不能以图片和3d模型为条件
			Integer proType=attrDef.getProType();
			Assert.isTrue(proType != null && proType != ESPropertyType.MODEL.getValue()
							&& proType != ESPropertyType.PICTURE.getValue() && proType != ESPropertyType.DOCUMENT.getValue(),
					"属性[" + attrDef.getProName().toUpperCase() + "]引用不合法");
			String ruleVal = ruleItem.getRuleVal();
			Assert.isTrue(CommUtil.countUtf8ByteSize(ruleVal) <= 500, ruleItem.getRuleVal() + "字符超长");
			int ruleOp = ruleItem.getRuleOp();

			ESPropertyType type = ESPropertyType.valueOf(attrDef.getProType());
			Assert.notNull(ruleItem.getRuleVal(), "标签规则为空");
			switch (type) {
			case INTEGER_CODE:
			case PREFIX_INTEGER_CODE:
			case INTEGER: {
				if (ruleOp == 9 || ruleOp == 10) {
					JSONArray words = JSON.parseArray(ruleVal.toString());
					words.forEach(word -> Assert.isTrue(CommUtil.isInteger(word.toString()), "整数类型不匹配"));
				} else {
					Assert.isTrue(CommUtil.isInteger(ruleVal), "整数类型不匹配");
				}
				break;
			}
			case DOUBLE: {
				if (ruleOp == 9 || ruleOp == 10) {
					JSONArray words = JSON.parseArray(ruleVal.toString());
					words.forEach(word -> Assert.isTrue(CommUtil.isDouble(word.toString()), "小数类型不匹配"));
				} else {
					Assert.isTrue(CommUtil.isDouble(ruleVal), "小数类型不匹配");
				}
				break;
			}
			case ENUM: {
				Assert.isTrue(CommUtil.isEnum(ruleVal), "枚举类型不匹配");
				break;
			}
			case DATE: {
				Assert.isTrue(CommUtil.isShortVar(ruleVal), "日期类型不匹配");
				break;
			}
			case VARCHAR: {
				Assert.isTrue(CommUtil.isShortVar(ruleVal), "字符串类型不匹配");
				break;
			}
			case LONG_VARCHAR: {
				Assert.isTrue(CommUtil.isLongVar(ruleVal), "长文本类型不匹配");
				break;
			}
			default:
				break;
			// throw new ServiceException(" is wrong PropertyType-value '" + type + "'! ");
			}
		}

	}

	@Override
	public List<ESCITagInfo> getCITagRuleByIds(List<Long> ids) {
		List<ESCITagInfo> tags = tagSvc.getListByQuery(QueryBuilders.termsQuery("id", ids));
		return CollectionUtils.isEmpty(tags)?new ArrayList<>() : tags;
	}

	@Override
	public boolean checkAttrByRules(List<ESTagRuleInfo> rules, JSONObject attrs, ESCIClassInfo classInfo){
		Map<Long, ESCIAttrDefInfo> attrDefMap = classInfo.getAttrDefs().stream().collect(Collectors.toMap(CcCiAttrDef::getId, e -> e, (k1, k2) -> k2));
		for (ESTagRuleInfo rule : rules) {
			//规则组之间只有and关系
			boolean check = rule.getItemGroups().stream().allMatch(group -> {
				switch (group.getLogicOp()) {
					case 1: // 逻辑“与”(AND)
						return group.getItems().stream().allMatch(item -> this.checkByRuleType(item, attrs, attrDefMap, new HashMap<>(), ""));
					case 2: // 逻辑“或”(OR)
						return group.getItems().stream().anyMatch(item -> this.checkByRuleType(item, attrs, attrDefMap, new HashMap<>(), ""));
					default:
						return false;
				}
			});
			//每组条件(只针对当前分类)之间只有or关系
			if(check){
				return true;
			}
		}
		return false;
	}

	private boolean checkByRuleType(ESTagRuleItem item, JSONObject attrs, Map<Long, ESCIAttrDefInfo> attrDefMap, Map<String, String> infoMap, String logicInfo){
		CcCiAttrDef def = attrDefMap.get(item.getClassAttrId());
		boolean result = true;
		if(BinaryUtils.isEmpty(def)){
			return result;
		}
		String attrVal = attrs.getString(def.getProStdName());
		if(BinaryUtils.isEmpty(attrVal)){
			return result;
		}
		String ruleVal = item.getRuleVal();
		String checkInfo = "";
		switch (item.getRuleOp()) {
			case 1:
				// 等于
				checkInfo = "等于";
				result = attrVal.equals(ruleVal);
				break;
			case 2:
				// 不等于
				checkInfo = "不等于";
				result = !attrVal.equals(ruleVal);
				break;
			case 3:
				// 小于
				checkInfo = "小于";
				result = Double.parseDouble(attrVal) < Double.parseDouble(ruleVal);
				break;
			case 4:
				// 小于或等于
				checkInfo = "小于或等于";
				result = Double.parseDouble(attrVal) <= Double.parseDouble(ruleVal);
				break;
			case 5:
				// 大于
				checkInfo = "大于";
				result = Double.parseDouble(attrVal) > Double.parseDouble(ruleVal);
				break;
			case 6:
				// 大于或等于
				checkInfo = "大于或等于";
				result = Double.parseDouble(attrVal) >= Double.parseDouble(ruleVal);
				break;
			case 7:
				// 模糊匹配
				// 整数、小数类型不支持模糊搜索
				Assert.isTrue(def.getProType() > 2, "整数、小数类型不支持like查询");
				checkInfo = "包含字符串";
				result = attrVal.contains(ruleVal);
				break;
			case 8:
				// 模糊匹配取反
				Assert.isTrue(def.getProType() > 2, "整数、小数类型不支持not like查询");
				checkInfo = "不包含字符串";
				result = !attrVal.contains(ruleVal);
				break;
			case 9:
				// 包含
				JSONArray words = JSON.parseArray(ruleVal);
				checkInfo = "包含";
				result = words.contains(attrVal);
				break;
			case 10:
				// 不包含
				JSONArray words1 = JSON.parseArray(ruleVal);
				checkInfo = "不包含";
				result = !words1.contains(attrVal);
		}
		if(!result){
			String info = "【" + def.getProStdName() + "】必须" + checkInfo + "【" + ruleVal + "】";
			infoMap.compute(def.getProStdName(), (k, v) -> v == null ? info : v + "<br>" + logicInfo + "<br>" + info);
		}
		return result;
	}

	@Override
	public Map<String, String> getCheckRulesInfo(List<ESTagRuleInfo> rules, JSONObject attrs, ESCIClassInfo classInfo){
		Map<String, String> result = new HashMap<>();
		Map<Long, ESCIAttrDefInfo> attrDefMap = classInfo.getAttrDefs().stream().collect(Collectors.toMap(CcCiAttrDef::getId, e -> e, (k1, k2) -> k2));
		Map<String, String> groupCheckInfo = new HashMap<>();
		//每组条件(只针对当前分类)之间只有or关系
		boolean ruleCheck = rules.stream().anyMatch(rule -> {
			//规则组之间只有and关系
			return rule.getItemGroups().stream().allMatch(group -> {
				Map<String, String> itemCheckInfo = new HashMap<>();
				boolean itemCheck = true;
				if(group.getLogicOp() == 1){
					itemCheck = group.getItems().stream().allMatch(item -> this.checkByRuleType(item, attrs, attrDefMap, itemCheckInfo, "且"));
				}else if(group.getLogicOp() == 2){
					itemCheck = group.getItems().stream().anyMatch(item -> this.checkByRuleType(item, attrs, attrDefMap, itemCheckInfo, "或"));
				}
                if(!itemCheck){
					itemCheckInfo.forEach((key, info) -> groupCheckInfo.compute(key, (k, v) -> v == null ? info : v + "<br>" + "或" + "<br>" + info));
				}
				return itemCheck;
			});
		});
		if(!ruleCheck){
			groupCheckInfo.forEach((key, info) -> result.compute(key, (k, v) -> v == null ? info : v + "<br>" + "或" + "<br>" + info));
		}
		return result;
	}

	@Override
	public QueryBuilder getQueryByTag(ESCITagInfo tagInfo) {
		return tagSvc.getQueryByTag(tagInfo);
	}
}
