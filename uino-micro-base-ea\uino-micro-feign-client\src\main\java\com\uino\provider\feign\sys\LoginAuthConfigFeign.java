package com.uino.provider.feign.sys;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.binary.jdbc.Page;
import com.uino.bean.sys.base.LdapUserMapping;
import com.uino.bean.sys.base.LoginAuthConfig;
import com.uino.bean.sys.business.ActiveLoginAuthConfigDto;
import com.uino.bean.sys.business.LoginAuthConfigDto;
import com.uino.provider.feign.config.BaseFeignConfig;

/**
 * 
 * <AUTHOR>
 * @since 2020年6月24日上午11:30:56
 *
 */
@Component
@FeignClient(name = "${feign-server-name:tarsier-platform}", path = "${feign-server-path:tarsier-platform}/feign/sys/loginAuthConfig", configuration = {BaseFeignConfig.class})
public interface LoginAuthConfigFeign {

    /**
     * 通过ID 查看配置信息
     * 
     * @param id
     *            配置ID
     * @return
     */
    @PostMapping("queryById")
    LoginAuthConfig queryById(@RequestBody Long id);

    /**
     * 查询当前集成配置项
     * 
     * @param authConfigCdt
     * @return
     */
    @PostMapping("queryPage")
    Page<LoginAuthConfig> queryPage(@RequestBody LoginAuthConfigDto authConfigCdt);

    /**
     * 保存或更新集成登录配置
     * 
     * @param authConfig
     * @return id
     */
    @PostMapping("saveOrUpdate")
    Long saveOrUpdate(@RequestBody LoginAuthConfig authConfig);

    /**
     * 启用配置项
     * 
     * @param active
     */
    @PostMapping("activeConfig")
    void activeConfig(@RequestBody ActiveLoginAuthConfigDto active);

    /**
     * 测试连接是否可用
     * 
     * @param authConfig
     * @return
     */
    @PostMapping("testConnection")
    boolean testConnection(@RequestBody LoginAuthConfig authConfig);

    /**
     * 测试并返回用户映射关系
     * 
     * @param authConfig
     * @return null时表示测试失败
     */
    @PostMapping("testConnectionAndMappingUser")
    LdapUserMapping testConnectionAndMappingUser(@RequestBody LoginAuthConfig authConfig);

    /**
     * 删除配置项
     * 
     * @param id
     */
    @PostMapping("removeById")
    void removeById(@RequestBody Long id);
}
