package com.uinnova.product.vmdb.provider.license.bean;

import com.uinnova.product.vmdb.comm.model.license.CcLicenseAuth;
import com.uinnova.product.vmdb.comm.model.license.CcLicenseAuthServer;

import java.io.Serializable;
import java.util.List;

public class CcLicenseAuthInfo implements Serializable {
	private static final long serialVersionUID = 1L;
	
	
	
	/**
	 * 许可证授权信息
	 */
	private CcLicenseAuth auth;
	
	
	
	/**
	 * 安装服务器信息
	 */
	private List<CcLicenseAuthServer> serverList;

	
	
	/** 状态, 1=已授权    2=未授权    3=已过期 **/
	private Integer status;
	


	public CcLicenseAuth getAuth() {
		return auth;
	}



	public void setAuth(CcLicenseAuth auth) {
		this.auth = auth;
	}



	public List<CcLicenseAuthServer> getServerList() {
		return serverList;
	}



	public void setServerList(List<CcLicenseAuthServer> serverList) {
		this.serverList = serverList;
	}



	public Integer getStatus() {
		return status;
	}



	public void setStatus(Integer status) {
		this.status = status;
	}
	
	
	
	
	
}
