package com.uinnova.product.eam.comm.model.es;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.io.Serializable;

/**
 * 目录&文件夹
 * <AUTHOR>
 */
@Data
@Comment("[uino_eam_category_design]")
public class EamCategory implements Serializable {
    @Comment("主键")
    private Long id;
    @Comment("目录名称")
    private String dirName;
    @Comment("领域id")
    private Long domainId;
    @Comment("目录绑定的ciCode,模型目录及系统目录有值,普通目录为空")
    private String ciCode;
    @Comment("父级目录id")
    private Long parentId;
    @Comment("目录层级")
    private Integer dirLvl;
    @Comment("目录层级路径:例：#1#2#7#")
    private String dirPath;
    @Comment("目录绑定的流程视图id")
    private String diagramId;
    @Comment("模型树Id")
    private Long modelId;
    @Comment("顶级目录=1、普通文件夹=2、系统文件夹=3、模型树(有icon的)=4、模型文件夹=5、分类文件夹=6 (原使用sysType、sysDir字段的判断逻辑使用该字段调整)")
    private Integer type;
    @Comment("逻辑删除字段(0=删除，1=正常)")
    private Integer dataStatus;
    @Comment("回收站记录原来的父id")
    private Long oldParentId;
    @Comment("用户标识")
    private String ownerCode;
    @Comment("创建人")
    private String creator;
    @Comment("创建时间")
    private Long createTime;
    @Comment("修改人")
    private String modifier;
    @Comment("修改时间")
    private Long modifyTime;
}
