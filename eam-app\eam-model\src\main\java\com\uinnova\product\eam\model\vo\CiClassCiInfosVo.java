package com.uinnova.product.eam.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.uinnova.product.eam.model.enums.AutoDrawDepArchImageName;
import com.uino.bean.cmdb.base.ESCIInfo;
import lombok.Data;

import java.util.List;

@Data
public class CiClassCiInfosVo {

    public CiClassCiInfosVo(ESCIInfo esciInfo, String ciCode, String name) {
        this.esciInfo = esciInfo;
        this.ciCode = ciCode;
        this.name = name;
    }

    private ESCIInfo esciInfo;

    /**
     * ciCode
     */
    private String ciCode;

    /**
     *  展示内容
     */
    private String name;

    /**
     * 展示图片名称(与前端约定字段，控制层级图片展示)
     */
    private AutoDrawDepArchImageName imageName;

    /**
     * 子集
     */
    private List<CiClassCiInfosVo> children;

    /**
     * 数据中心横向最大部署单元个数
     */
    private Integer maxCount;

    /**
     * 共有属性
     */
    private List<String> commonTags;

    /**
     * 排序
     */
    @JsonIgnore
    private Integer sort;

    /**
     * 扩展排序
     */
    @JsonIgnore
    private String extendsSort;
}
