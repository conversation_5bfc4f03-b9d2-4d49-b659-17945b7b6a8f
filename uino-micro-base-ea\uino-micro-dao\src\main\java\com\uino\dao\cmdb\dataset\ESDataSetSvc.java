package com.uino.dao.cmdb.dataset;

import com.alibaba.fastjson.JSONObject;
import com.binary.jdbc.Page;
import com.uino.dao.AbstractESBaseDao;
import com.uino.dao.ESConst;
import com.uino.dao.util.ESUtil;
import com.uino.bean.cmdb.base.dataset.*;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.sort.SortBuilder;
import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;

import jakarta.annotation.PostConstruct;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * @Classname DataSetMallApiDao
 * @Description 数据集--实体类对应：DataSetMallApi
 * 字段不明确可查看DataSetMallApi
 * @Date 2020/3/19 10:03
 * @Created by sh
 */
@Repository
@Slf4j
public class ESDataSetSvc extends AbstractESBaseDao<JSONObject, JSONObject> {

    @Override
    public String getIndex() {
        return ESConst.INDEX_CMDB_DATASET;
    }

    @Override
    public String getType() {
        return ESConst.INDEX_CMDB_DATASET;
    }

    @PostConstruct
    public void init() {
        super.initIndex();

        // 迭代34版本起 数据超市初始化不再加载默认数据

        /*BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.filter(QueryBuilders.termsQuery("name.keyword", Arrays.asList("CI分类", "关系分类", "上下N层")));
        List<JSONObject> listByQuery = this.getListByQuery(queryBuilder);
        Set<String> names = new HashSet<>();
        if (listByQuery != null) {
            listByQuery.forEach(json -> names.add(json.getString("name")));
        }
        if (!names.contains("CI分类")) {
            DataSetMallApiCiClass dataSetMallApiCiClass = new DataSetMallApiCiClass();
            dataSetMallApiCiClass.setId(ESUtil.getUUID());
            dataSetMallApiCiClass.setType(DataSetMallApiType.CiClass.getCode());
            dataSetMallApiCiClass.setName("CI分类");
            dataSetMallApiCiClass.setDescription("查询分类下ci");
            dataSetMallApiCiClass.setCreateTime(System.currentTimeMillis());
            dataSetMallApiCiClass.setModifyTime(System.currentTimeMillis());
            dataSetMallApiCiClass.setShareLevel(OperateType.Read.getCode());
            this.saveOrUpdate(dataSetMallApiCiClass.toJson());
            log.info("添加ci分类标签");
        }
        if (!names.contains("关系分类")) {
            DataSetMallApiRelClass dataSetMallApiRelClass = new DataSetMallApiRelClass();
            dataSetMallApiRelClass.setId(ESUtil.getUUID());
            dataSetMallApiRelClass.setType(DataSetMallApiType.RelClass.getCode());
            dataSetMallApiRelClass.setName("关系分类");
            dataSetMallApiRelClass.setDescription("查询关系分类下所有关系");
            dataSetMallApiRelClass.setCreateTime(System.currentTimeMillis());
            dataSetMallApiRelClass.setModifyTime(System.currentTimeMillis());
            dataSetMallApiRelClass.setShareLevel(OperateType.Read.getCode());
            this.saveOrUpdate(dataSetMallApiRelClass.toJson());
            log.info("添加关系分类标签");
        }
        if (!names.contains("上下N层")) {
            DataSetMallApiUpDownNFloor dataSetMallApiUpDownNFloor = new DataSetMallApiUpDownNFloor();
            dataSetMallApiUpDownNFloor.setId(ESUtil.getUUID());
            dataSetMallApiUpDownNFloor.setType(DataSetMallApiType.UpDownNFloor.getCode());
            dataSetMallApiUpDownNFloor.setName("上下N层");
            dataSetMallApiUpDownNFloor.setDescription("查询ci上下N层关系");
            dataSetMallApiUpDownNFloor.setCreateTime(System.currentTimeMillis());
            dataSetMallApiUpDownNFloor.setModifyTime(System.currentTimeMillis());
            dataSetMallApiUpDownNFloor.setShareLevel(OperateType.Read.getCode());
            this.saveOrUpdate(dataSetMallApiUpDownNFloor.toJson());
            log.info("添加上下N层标签");
        }*/
    }

    public List<JSONObject> getSortListByQuery(QueryBuilder query, List<SortBuilder<?>> sorts) {
        Page<JSONObject> results = super.getSortListByQuery(1, 3000, query, sorts);
        Assert.isTrue(results.getTotalRows() <= 3000, "不分页查询一次最多拉取3000条数据，本次查询已超出");
        return results.getData();
    }
}
