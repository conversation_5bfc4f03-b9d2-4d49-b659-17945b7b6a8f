package com.uinnova.product.eam.model;

import com.binary.framework.bean.annotation.Comment;
import com.uino.bean.cmdb.base.LibType;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 目录&文件夹查询参数
 * <AUTHOR>
 */
@Accessors(chain = true)
@Data
public class EamCategoryCdt implements Serializable {
    @Comment("ciCode")
    private String ciCode;
    @Comment("ciCodes")
    private List<String> ciCodes;
    @Comment("目录id")
    private Long dirId;
    @Comment("目录绑定的流程视图id")
    private String diagramId;
    @Comment("视图id集合")
    private List<String> diagramIds;
    @Comment("方案id集合")
    private List<Long> planIds;
    @Comment("方案id")
    private Long planId;

    @Comment("矩阵表格id")
    private Long matrixId;
    @Comment("矩阵表格id")
    private List<Long> matrixIds;

    @Comment("库")
    private LibType libType = LibType.PRIVATE;
    @Comment("目录id集合")
    private List<Long> dirIds;
    @Comment("目标目录id")
    private Long targetId;
    @Comment("模型id")
    private Long modelId;
    @Comment("用户标识")
    private String ownerCode;
    @Comment("0:自动成图调用 类型 1：逻辑删除 2: 物理删除  Constants")
    private Integer delType = 1;
    @Comment("视图类型  1:默认视图  2:新建 -> 空白视图")
    private Integer diagramType;
    @Comment("方案内选择视图/模型发布 资产信息与发布位置信息map")
    private Map<String, Long> diagramIdLocMap;
    @Comment("核查类型ConflictEnum")
    private int checkType;
}
