package com.uinnova.product.eam.db.impl;


import com.uinnova.product.eam.comm.model.CVcNodeLinkedPath;
import com.uinnova.product.eam.comm.model.VcNodeLinkedPath;
import com.uinnova.product.eam.db.VcNodeLinkedPathDao;
import com.uinnova.product.eam.db.support.dao.mybatis.ComMyBatisBinaryDaoImpl;


/**
 * 节点路径详情表[VC_NODE_LINKED_PATH]数据访问对象实现
 */
public class VcNodeLinkedPathDaoImpl extends ComMyBatisBinaryDaoImpl<VcNodeLinkedPath, CVcNodeLinkedPath> implements VcNodeLinkedPathDao {

//    @Override
//    public String getTableName() {
//        return "IAMS." + getDaoDefinition().getTableName();
//    }
}


