package com.uinnova.product.vmdb.comm.model.ci;

import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;

/**
 * 
 * <AUTHOR>
 *
 */
@Comment("CI分类外挂属性[CC_CI_PLUGIN_ATTR]")
public class CCcCiPluginAttr implements Condition {
    private static final long serialVersionUID = 1L;

    @Comment("ID[ID] operate-Equal[=]")
    private Long id;

    @Comment("ID[ID] operate-In[in]")
    private Long[] ids;

    @Comment("ID[ID] operate-GTEqual[>=]")
    private Long startId;

    @Comment("ID[ID] operate-LTEqual[<=]")
    private Long endId;

    @Comment("CI分类ID[CLASS_ID] operate-Equal[=]")
    private Long classId;

    @Comment("CI分类ID[CLASS_ID] operate-In[in]")
    private Long[] classIds;

    @Comment("CI分类ID[CLASS_ID] operate-GTEqual[>=]")
    private Long startClassId;

    @Comment("CI分类ID[CLASS_ID] operate-LTEqual[<=]")
    private Long endClassId;

    @Comment("分类类型[CI_TYPE] operate-Equal[=]    分类类型:1=基础CI 2=关系CI")
    private Integer ciType;

    @Comment("分类类型[CI_TYPE] operate-In[in]    分类类型:1=基础CI 2=关系CI")
    private Integer[] ciTypes;

    @Comment("分类类型[CI_TYPE] operate-GTEqual[>=]    分类类型:1=基础CI 2=关系CI")
    private Integer startCiType;

    @Comment("分类类型[CI_TYPE] operate-LTEqual[<=]    分类类型:1=基础CI 2=关系CI")
    private Integer endCiType;

    @Comment("属性名[PRO_NAME] operate-Like[like]")
    private String proName;

    @Comment("属性名[PRO_NAME] operate-Equal[=]")
    private String proNameEqual;

    @Comment("属性名[PRO_NAME] operate-In[in]")
    private String[] proNames;

    @Comment("标准名[PRO_STD_NAME] operate-Like[like]    标准名:全部大写")
    private String proStdName;

    @Comment("标准名[PRO_STD_NAME] operate-Equal[=]    标准名:全部大写")
    private String proStdNameEqual;

    @Comment("标准名[PRO_STD_NAME] operate-In[in]    标准名:全部大写")
    private String[] proStdNames;

    @Comment("属性类型[PRO_TYPE] operate-Equal[=]    属性类型:1=外部链接 2=外部数据")
    private Integer proType;

    @Comment("属性类型[PRO_TYPE] operate-In[in]    属性类型:1=外部链接 2=外部数据")
    private Integer[] proTypes;

    @Comment("属性类型[PRO_TYPE] operate-GTEqual[>=]    属性类型:1=外部链接 2=外部数据")
    private Integer startProType;

    @Comment("属性类型[PRO_TYPE] operate-LTEqual[<=]    属性类型:1=外部链接 2=外部数据")
    private Integer endProType;

    @Comment("属性描述[PRO_DESC] operate-Like[like]")
    private String proDesc;

    @Comment("属性值1[PRO_VAL_1] operate-Like[like]")
    private String proVal1;

    @Comment("属性值2[PRO_VAL_2] operate-Like[like]")
    private String proVal2;

    @Comment("属性值3[PRO_VAL_3] operate-Like[like]")
    private String proVal3;

    @Comment("排列顺序[ORDER_NO] operate-Equal[=]")
    private Integer orderNo;

    @Comment("排列顺序[ORDER_NO] operate-In[in]")
    private Integer[] orderNos;

    @Comment("排列顺序[ORDER_NO] operate-GTEqual[>=]")
    private Integer startOrderNo;

    @Comment("排列顺序[ORDER_NO] operate-LTEqual[<=]")
    private Integer endOrderNo;

    @Comment("备用_1[CUSTOM_1] operate-Like[like]")
    private String custom1;

    @Comment("备用_2[CUSTOM_2] operate-Like[like]")
    private String custom2;

    @Comment("备用_3[CUSTOM_3] operate-Like[like]")
    private String custom3;

    @Comment("备用_4[CUSTOM_4] operate-Like[like]")
    private String custom4;

    @Comment("备用_5[CUSTOM_5] operate-Like[like]")
    private String custom5;

    @Comment("备用_6[CUSTOM_6] operate-Like[like]")
    private String custom6;

    @Comment("所属域[DOMAIN_ID] operate-Equal[=]")
    private Long domainId;

    @Comment("所属域[DOMAIN_ID] operate-In[in]")
    private Long[] domainIds;

    @Comment("所属域[DOMAIN_ID] operate-GTEqual[>=]")
    private Long startDomainId;

    @Comment("所属域[DOMAIN_ID] operate-LTEqual[<=]")
    private Long endDomainId;

    @Comment("数据状态[DATA_STATUS] operate-Equal[=]    数据状态:0=删除，1=正常")
    private Integer dataStatus;

    @Comment("数据状态[DATA_STATUS] operate-In[in]    数据状态:0=删除，1=正常")
    private Integer[] dataStatuss;

    @Comment("数据状态[DATA_STATUS] operate-GTEqual[>=]    数据状态:0=删除，1=正常")
    private Integer startDataStatus;

    @Comment("数据状态[DATA_STATUS] operate-LTEqual[<=]    数据状态:0=删除，1=正常")
    private Integer endDataStatus;

    @Comment("创建人[CREATOR] operate-Like[like]")
    private String creator;

    @Comment("创建人[CREATOR] operate-Equal[=]")
    private String creatorEqual;

    @Comment("创建人[CREATOR] operate-In[in]")
    private String[] creators;

    @Comment("修改人[MODIFIER] operate-Like[like]")
    private String modifier;

    @Comment("修改人[MODIFIER] operate-Equal[=]")
    private String modifierEqual;

    @Comment("修改人[MODIFIER] operate-In[in]")
    private String[] modifiers;

    @Comment("创建时间[CREATE_TIME] operate-Equal[=]    创建时间:yyyyMMddHHmmss")
    private Long createTime;

    @Comment("创建时间[CREATE_TIME] operate-In[in]    创建时间:yyyyMMddHHmmss")
    private Long[] createTimes;

    @Comment("创建时间[CREATE_TIME] operate-GTEqual[>=]    创建时间:yyyyMMddHHmmss")
    private Long startCreateTime;

    @Comment("创建时间[CREATE_TIME] operate-LTEqual[<=]    创建时间:yyyyMMddHHmmss")
    private Long endCreateTime;

    @Comment("更新时间[MODIFY_TIME] operate-Equal[=]    更新时间:yyyyMMddHHmmss")
    private Long modifyTime;

    @Comment("更新时间[MODIFY_TIME] operate-In[in]    更新时间:yyyyMMddHHmmss")
    private Long[] modifyTimes;

    @Comment("更新时间[MODIFY_TIME] operate-GTEqual[>=]    更新时间:yyyyMMddHHmmss")
    private Long startModifyTime;

    @Comment("更新时间[MODIFY_TIME] operate-LTEqual[<=]    更新时间:yyyyMMddHHmmss")
    private Long endModifyTime;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long[] getIds() {
        return this.ids;
    }

    public void setIds(Long[] ids) {
        this.ids = ids;
    }

    public Long getStartId() {
        return this.startId;
    }

    public void setStartId(Long startId) {
        this.startId = startId;
    }

    public Long getEndId() {
        return this.endId;
    }

    public void setEndId(Long endId) {
        this.endId = endId;
    }

    public Long getClassId() {
        return this.classId;
    }

    public void setClassId(Long classId) {
        this.classId = classId;
    }

    public Long[] getClassIds() {
        return this.classIds;
    }

    public void setClassIds(Long[] classIds) {
        this.classIds = classIds;
    }

    public Long getStartClassId() {
        return this.startClassId;
    }

    public void setStartClassId(Long startClassId) {
        this.startClassId = startClassId;
    }

    public Long getEndClassId() {
        return this.endClassId;
    }

    public void setEndClassId(Long endClassId) {
        this.endClassId = endClassId;
    }

    public Integer getCiType() {
        return this.ciType;
    }

    public void setCiType(Integer ciType) {
        this.ciType = ciType;
    }

    public Integer[] getCiTypes() {
        return this.ciTypes;
    }

    public void setCiTypes(Integer[] ciTypes) {
        this.ciTypes = ciTypes;
    }

    public Integer getStartCiType() {
        return this.startCiType;
    }

    public void setStartCiType(Integer startCiType) {
        this.startCiType = startCiType;
    }

    public Integer getEndCiType() {
        return this.endCiType;
    }

    public void setEndCiType(Integer endCiType) {
        this.endCiType = endCiType;
    }

    public String getProName() {
        return this.proName;
    }

    public void setProName(String proName) {
        this.proName = proName;
    }

    public String getProNameEqual() {
        return this.proNameEqual;
    }

    public void setProNameEqual(String proNameEqual) {
        this.proNameEqual = proNameEqual;
    }

    public String[] getProNames() {
        return this.proNames;
    }

    public void setProNames(String[] proNames) {
        this.proNames = proNames;
    }

    public String getProStdName() {
        return this.proStdName;
    }

    public void setProStdName(String proStdName) {
        this.proStdName = proStdName;
    }

    public String getProStdNameEqual() {
        return this.proStdNameEqual;
    }

    public void setProStdNameEqual(String proStdNameEqual) {
        this.proStdNameEqual = proStdNameEqual;
    }

    public String[] getProStdNames() {
        return this.proStdNames;
    }

    public void setProStdNames(String[] proStdNames) {
        this.proStdNames = proStdNames;
    }

    public Integer getProType() {
        return this.proType;
    }

    public void setProType(Integer proType) {
        this.proType = proType;
    }

    public Integer[] getProTypes() {
        return this.proTypes;
    }

    public void setProTypes(Integer[] proTypes) {
        this.proTypes = proTypes;
    }

    public Integer getStartProType() {
        return this.startProType;
    }

    public void setStartProType(Integer startProType) {
        this.startProType = startProType;
    }

    public Integer getEndProType() {
        return this.endProType;
    }

    public void setEndProType(Integer endProType) {
        this.endProType = endProType;
    }

    public String getProDesc() {
        return this.proDesc;
    }

    public void setProDesc(String proDesc) {
        this.proDesc = proDesc;
    }

    public String getProVal1() {
        return this.proVal1;
    }

    public void setProVal1(String proVal1) {
        this.proVal1 = proVal1;
    }

    public String getProVal2() {
        return this.proVal2;
    }

    public void setProVal2(String proVal2) {
        this.proVal2 = proVal2;
    }

    public String getProVal3() {
        return this.proVal3;
    }

    public void setProVal3(String proVal3) {
        this.proVal3 = proVal3;
    }

    public Integer getOrderNo() {
        return this.orderNo;
    }

    public void setOrderNo(Integer orderNo) {
        this.orderNo = orderNo;
    }

    public Integer[] getOrderNos() {
        return this.orderNos;
    }

    public void setOrderNos(Integer[] orderNos) {
        this.orderNos = orderNos;
    }

    public Integer getStartOrderNo() {
        return this.startOrderNo;
    }

    public void setStartOrderNo(Integer startOrderNo) {
        this.startOrderNo = startOrderNo;
    }

    public Integer getEndOrderNo() {
        return this.endOrderNo;
    }

    public void setEndOrderNo(Integer endOrderNo) {
        this.endOrderNo = endOrderNo;
    }

    public String getCustom1() {
        return this.custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }

    public String getCustom2() {
        return this.custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }

    public String getCustom3() {
        return this.custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }

    public String getCustom4() {
        return this.custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }

    public String getCustom5() {
        return this.custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }

    public String getCustom6() {
        return this.custom6;
    }

    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }

    public Long getDomainId() {
        return this.domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }

    public Long[] getDomainIds() {
        return this.domainIds;
    }

    public void setDomainIds(Long[] domainIds) {
        this.domainIds = domainIds;
    }

    public Long getStartDomainId() {
        return this.startDomainId;
    }

    public void setStartDomainId(Long startDomainId) {
        this.startDomainId = startDomainId;
    }

    public Long getEndDomainId() {
        return this.endDomainId;
    }

    public void setEndDomainId(Long endDomainId) {
        this.endDomainId = endDomainId;
    }

    public Integer getDataStatus() {
        return this.dataStatus;
    }

    public void setDataStatus(Integer dataStatus) {
        this.dataStatus = dataStatus;
    }

    public Integer[] getDataStatuss() {
        return this.dataStatuss;
    }

    public void setDataStatuss(Integer[] dataStatuss) {
        this.dataStatuss = dataStatuss;
    }

    public Integer getStartDataStatus() {
        return this.startDataStatus;
    }

    public void setStartDataStatus(Integer startDataStatus) {
        this.startDataStatus = startDataStatus;
    }

    public Integer getEndDataStatus() {
        return this.endDataStatus;
    }

    public void setEndDataStatus(Integer endDataStatus) {
        this.endDataStatus = endDataStatus;
    }

    public String getCreator() {
        return this.creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getCreatorEqual() {
        return this.creatorEqual;
    }

    public void setCreatorEqual(String creatorEqual) {
        this.creatorEqual = creatorEqual;
    }

    public String[] getCreators() {
        return this.creators;
    }

    public void setCreators(String[] creators) {
        this.creators = creators;
    }

    public String getModifier() {
        return this.modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public String getModifierEqual() {
        return this.modifierEqual;
    }

    public void setModifierEqual(String modifierEqual) {
        this.modifierEqual = modifierEqual;
    }

    public String[] getModifiers() {
        return this.modifiers;
    }

    public void setModifiers(String[] modifiers) {
        this.modifiers = modifiers;
    }

    public Long getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long[] getCreateTimes() {
        return this.createTimes;
    }

    public void setCreateTimes(Long[] createTimes) {
        this.createTimes = createTimes;
    }

    public Long getStartCreateTime() {
        return this.startCreateTime;
    }

    public void setStartCreateTime(Long startCreateTime) {
        this.startCreateTime = startCreateTime;
    }

    public Long getEndCreateTime() {
        return this.endCreateTime;
    }

    public void setEndCreateTime(Long endCreateTime) {
        this.endCreateTime = endCreateTime;
    }

    public Long getModifyTime() {
        return this.modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Long[] getModifyTimes() {
        return this.modifyTimes;
    }

    public void setModifyTimes(Long[] modifyTimes) {
        this.modifyTimes = modifyTimes;
    }

    public Long getStartModifyTime() {
        return this.startModifyTime;
    }

    public void setStartModifyTime(Long startModifyTime) {
        this.startModifyTime = startModifyTime;
    }

    public Long getEndModifyTime() {
        return this.endModifyTime;
    }

    public void setEndModifyTime(Long endModifyTime) {
        this.endModifyTime = endModifyTime;
    }

}
