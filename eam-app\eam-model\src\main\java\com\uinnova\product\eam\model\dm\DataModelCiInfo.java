package com.uinnova.product.eam.model.dm;

import com.binary.framework.bean.annotation.Comment;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.comm.model.ci.CcCiClass;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据建模保存ci返回体
 * <AUTHOR>
 */
@Data
public class DataModelCiInfo extends CcCiInfo {

    @Comment("继承节点集合：实体ciCode-继承属性list")
    private Map<String, List<CcCiInfo>> inheritMap = new HashMap<>();

    @Comment("实体属性所属分类")
    private CcCiClass attributeCiClass;

    @Comment("实体属性的属性定义")
    private List<CcCiAttrDef> attributeDefs;

    @Comment("原实体属性中文名称")
    @JsonIgnore
    private String oldCnName;

    @Comment("原实体属性英文名称")
    @JsonIgnore
    private String oldEnName;

    @Comment("批量保存实体时用于返回实体数据")
    private List<CcCiInfo> ciList;
}
