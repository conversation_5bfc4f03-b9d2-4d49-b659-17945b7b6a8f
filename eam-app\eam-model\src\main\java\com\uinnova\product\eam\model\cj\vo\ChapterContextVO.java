package com.uinnova.product.eam.model.cj.vo;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.model.cj.domain.ChapterResource;
import com.uinnova.product.eam.model.cj.domain.ReferencesInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 方案章节详情（章节的延续） EntityBean、Condition
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ChapterContextVO {

    /**
     * 方案id
     */
    private Long planId;

    /**
     * 主键id， 跟章节用同一个id
     */
    private Long chapterId;

    /**
     * 是否必填
     */
    private Boolean required;

    /**
     * 章节是否来自模板
     */
    private Boolean isFromTemplate;

    /**
     * 父章节id
     */
    private Long parentId;

    /**
     * 章节说明
     */
    private String chapterDesc;

    /**
     * 备注
     */
    private String remark;

    /**
     * 章节名称
     */
    private String name;

    /**
     * 是否可自行添加内容块
     */
    private Boolean userAddContent;

    /**
     * 是否有上个章节
     */
    private Boolean hasPrevious;

    /**
     * 是否有下个章节
     */
    private Boolean hasNext;

    /**
     * 上个章节id
     */
    private Long previousId;

    /**
     * 下个章节id
     */
    private Long nextId;

    /**
     * 上下文内容块
     */
    private List<ContextModuleVO> moduleList;


    private String planName;

    private String chapterFullName;

    private Long createTime;

    private Map<Long, Map<String, String>> tableAssetMap;

    private List<ChapterResource> chapterResources;

    /**
     * 是否已加锁，1：加锁， 0：未加锁
     */
    private Integer lock;

    @Comment("引入章节信息")
    private List<PlanTemplateIntroduceChapterVo> planTemplateIntroduceChapterVoList;

    /**
     * 引入章节的名称
     */
    private String introduceChapterName;
    /**
     * 引用方式  1表示插入 2表示覆盖
     */
    private Integer introduceWay;
    /**
     * 引入章节的方案的id
     */
    private Long introducePlanId;
    /**
     * 引入章节的id
     */
    private Long introduceChapterId;

    @Comment("参考规范信息")
    private ReferencesInfo referencesInfo;
}
