package com.uinnova.product.vmdb.provider.rlt.bean;

import com.binary.framework.bean.annotation.Comment;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Comment("关系分类数据")
public class CiRltRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @Comment("导入时用于记录排序")
    private Integer index;
    
    @Comment("关系主键ID")
    private Long id;

    @Comment("关系分类的ID")
    private Long rltClassId;

    @Comment("关系来源ID")
    private Long sourceId;

    @Comment("自定义子关系CODE,将追加到CI_CODE中,完整的CI_CODE ${sourceCiCode}_${rltClassId}_${targetCiCode}_${subCode}")
    private String subCode;

    @Comment("源分类的ID")
    private Long sourceCiClassId;

    @Comment("目标分类的ID")
    private Long targetCiClassId;

    @Comment("源字段的名字")
    private String sourceFieldName;

    @Comment("目标字段的名字")
    private String targetFieldName;

    @Comment("源Ci的code")
    private String sourceCiCode;

    @Comment("目标Ci的code")
    private String targetCiCode;

    @Comment("源Ci的业务主键值数组")
    private List<String> sourceCiPrimaryKeys;

    @Comment("目标Ci的业务主键值数组")
    private List<String> targetCiPrimaryKeys;

    private Map<String, String> attrs;

    public CiRltRecord() {
    }

    public CiRltRecord(Long rltClassId, Long sourceId, String sourceCiCode, String targetCiCode,
            Map<String, String> attrs) {
        super();
        this.rltClassId = rltClassId;
        this.sourceId = sourceId;
        this.sourceCiCode = sourceCiCode;
        this.targetCiCode = targetCiCode;
        this.attrs = attrs;
    }

    public CiRltRecord(Long rltClassId, Long sourceId,  List<String> sourceCiPrimaryKeys, List<String> targetCiPrimaryKeys,
            Map<String, String> attrs) {
        super();
        this.rltClassId = rltClassId;
        this.sourceId = sourceId;
        this.sourceCiPrimaryKeys = sourceCiPrimaryKeys;
        this.targetCiPrimaryKeys = targetCiPrimaryKeys;
        this.attrs = attrs;
    }

    public CiRltRecord(Long rltClassId, Long sourceCiClassId, Long targetCiClassId, String sourceFieldName,
            String targetFieldName, String sourceCiCode, String targetCiCode, Map<String, String> attrs) {
        super();
        this.rltClassId = rltClassId;
        this.sourceCiClassId = sourceCiClassId;
        this.targetCiClassId = targetCiClassId;
        this.sourceFieldName = sourceFieldName;
        this.targetFieldName = targetFieldName;
        this.sourceCiCode = sourceCiCode;
        this.targetCiCode = targetCiCode;
        this.attrs = attrs;
    }

    public CiRltRecord(int rIdx, Long rltId, Long object, String sCiCode, String tCiCode, Map<String, String> attrs) {
        this(rltId, rltId, object, tCiCode, tCiCode, sCiCode, tCiCode, attrs);
        this.index = rIdx;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public Long getRltClassId() {
        return rltClassId;
    }

    public void setRltClassId(Long rltClassId) {
        this.rltClassId = rltClassId;
    }

    public Long getSourceId() {
        return sourceId;
    }

    public void setSourceId(Long sourceId) {
        this.sourceId = sourceId;
    }

    public Long getSourceCiClassId() {
        return sourceCiClassId;
    }

    public void setSourceCiClassId(Long sourceCiClassId) {
        this.sourceCiClassId = sourceCiClassId;
    }

    public Long getTargetCiClassId() {
        return targetCiClassId;
    }

    public void setTargetCiClassId(Long targetCiClassId) {
        this.targetCiClassId = targetCiClassId;
    }

    public String getSourceFieldName() {
        return sourceFieldName;
    }

    public void setSourceFieldName(String sourceFieldName) {
        this.sourceFieldName = sourceFieldName;
    }

    public String getTargetFieldName() {
        return targetFieldName;
    }

    public void setTargetFieldName(String targetFieldName) {
        this.targetFieldName = targetFieldName;
    }

    public String getSourceCiCode() {
        return sourceCiCode;
    }

    public void setSourceCiCode(String sourceCiCode) {
        this.sourceCiCode = sourceCiCode;
    }

    public String getTargetCiCode() {
        return targetCiCode;
    }

    public void setTargetCiCode(String targetCiCode) {
        this.targetCiCode = targetCiCode;
    }

    public Map<String, String> getAttrs() {
        return attrs;
    }

    public void setAttrs(Map<String, String> attrs) {
        this.attrs = attrs;
    }

    public String getSubCode() {
        return subCode;
    }

    public void setSubCode(String subCode) {
        this.subCode = subCode;
    }
	
    public List<String> getSourceCiPrimaryKeys() {
        return sourceCiPrimaryKeys;
    }

    
    public void setSourceCiPrimaryKeys(List<String> sourceCiPrimaryKeys) {
        this.sourceCiPrimaryKeys = sourceCiPrimaryKeys;
    }

    
    public List<String> getTargetCiPrimaryKeys() {
        return targetCiPrimaryKeys;
    }

    
    public void setTargetCiPrimaryKeys(List<String> targetCiPrimaryKeys) {
        this.targetCiPrimaryKeys = targetCiPrimaryKeys;
    }

    public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}
    
}
