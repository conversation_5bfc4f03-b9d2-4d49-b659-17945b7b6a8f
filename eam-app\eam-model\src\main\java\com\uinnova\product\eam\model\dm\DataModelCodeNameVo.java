package com.uinnova.product.eam.model.dm;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

/**
 * 数据建模名称校验实体
 * <AUTHOR>
 */
@Data
public class DataModelCodeNameVo {
    @Comment("名称")
    private String name;

    @Comment("英文名称")
    private String enName;

    @Comment("ciCode")
    private String ciCode;

    @Comment("主键")
    private Boolean primaryKey;

    public DataModelCodeNameVo(){

    }

    public DataModelCodeNameVo(String name, String ciCode){
        this.name = name;
        this.ciCode = ciCode;
    }

    public DataModelCodeNameVo(String name, String enName, Boolean primaryKey, String ciCode){
        this.name = name;
        this.enName = enName;
        this.ciCode = ciCode;
        this.primaryKey = primaryKey;
    }
}
