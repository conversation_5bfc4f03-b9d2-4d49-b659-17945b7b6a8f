package com.uino.service.cmdb.microservice;

import java.util.Collection;
import java.util.List;
import java.util.Set;

import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClass;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uino.bean.cmdb.base.ESCIClassInfo;
import com.uino.bean.cmdb.business.ClassNodeInfo;
import com.uino.bean.cmdb.business.ClassReOrderDTO;
import com.uino.bean.cmdb.business.ImportResultMessage;
import com.uino.bean.cmdb.query.ESCIClassSearchBean;

/**
 * 
 * <AUTHOR>
 *
 */
public interface ICIClassSvc {

    /**
     * 获取分类树(包含CI统计结果) 获取全量数据
     * 
     * @return
     */
    List<ClassNodeInfo> getClassTree(Long domainId);

    /**
     * 根据id查询CI分类
     * 
     * @param id
     * @return
     */
    ESCIClassInfo queryESClassInfoById(Long id);

    /**
     * 根据id查询CI分类
     * 
     * @param id
     * @return
     */
    CcCiClassInfo queryClassInfoById(Long id);

    /**
     * 根据条件查询CI分类信息
     * 
     * @param cdt
     * @return
     */
    List<CcCiClassInfo> queryClassByCdt(CCcCiClass cdt);

    /**
     * 条件查询CI分类
     * 
     * @param domainId
     * @param cdt
     * @param orders 排序字段
     * @param isAsc 是否升序
     * @return
     */
    List<CcCiClassInfo> queryCiClassInfoList(Long domainId, CCcCiClass cdt, String orders, Boolean isAsc);

    /**
     * 条件查询CI分类
     * 
     * @param bean
     * @return
     */
    List<CcCiClassInfo> queryCiClassInfoListBySearchBean(ESCIClassSearchBean bean);

    /**
     * 条件查询CI分类
     * 
     * @param bean
     * @return
     */
    List<ESCIClassInfo> queryESCiClassInfoListBySearchBean(ESCIClassSearchBean bean);

	/**
     * 分页查询CI分类
     * @param bean
     * @return
     */
	Page<ESCIClassInfo> queryESCiClassInfoPageBySearchBean(ESCIClassSearchBean bean);

    CcCiClassInfo getCiClassByClassName(String className);

    CcCiClassInfo getCiClassByClassCode(String classCode);

    /**
     * 保存或更新CI分类
     * 
     * @param record
     * @return
     */
    Long saveOrUpdate(CcCiClassInfo record);

    /**
     * 保存或更新CI分类
     * 
     * @param record
     * @return
     */
    Long saveOrUpdate(ESCIClassInfo record);

    /**
     * 保存或更新CI分类
     *
     * @param record
     * @return
     */
    Long saveOrUpdateExtra(ESCIClassInfo record);


    /**
     * 批量保存或更新分类
     * @param clsInfos
     * @return
     */
    Integer saveOrUpdateBatch(Long domainId,List<CcCiClassInfo> clsInfos);

    /**
     * 根据id删除CI分类
     * 
     * @param id
     * @return
     */
    Integer deleteById(Long id);

    /**
     * 导入CI分类属性
     * 
     * @param file
     * @param ciClsId
     * @return
     */
    ImportResultMessage importCiClassAttr(MultipartFile file, Long ciClsId);

    /**
     * 导出属性模板
     * 
     * @param clsIds 分类id
     * @param isBatchTpl 是否批量模板
     * @return
     */
    ResponseEntity<byte[]> exportClassAttrExcel(Set<Long> clsIds, Boolean isBatchTpl);

    /**
     * ci分类拖动排序
     * @author: weixuesong
     * @date: 2020/8/5 14:24
     * @param reOrderDTO
     * @return: void
     */
    boolean reOrder(ClassReOrderDTO reOrderDTO);

    /**
     * 初始化所有ci分类的orderNo
     * @author: weixuesong
     * @date: 2020/8/6 17:00
     * @return: boolean
     */
    boolean initAllOrderNo(Long domainId);

    /**
     * 根据id查询分类-(属性返回实际存储名称：proName为显示名称；proStdName为实际存储名称)
     * 
     * @param classIds
     * @return
     */
    List<ESCIClassInfo> getTargetAttrDefsByClassIds(Long domainId, Collection<Long> classIds);

	/**
	 * 获取属性tag列表
	 * 
	 * @return
	 */
	List<String> queryAttrDefGroupList(Long domainId);

    /**
     * get http resource path
     *
     * @return custom path
     * */
    String getHttpResourceSpace();

    /**
     * whether to display the 3d model
     *
     * @return boolean
     * */
    Boolean isShow3dAttribute();

    List<ESCIClassInfo> queryESClassInfoByIds(List<Long> classIds);

    /**
     * query class info by dt class id
     *
     * @param dtClassIds dt class id
     * @param domainId do main id
     *
     * @return {@link List<ESCIClassInfo>}
     * */
    List<ESCIClassInfo> queryEsClassInfoByDtClassIds(List<String> dtClassIds, Long domainId);
}
