package com.uinnova.product.eam.web.diagram.bean;

import java.io.Serializable;
import java.util.List;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.vmdb.comm.model.dir.CcGeneralDir;

public class DirAndImageInfo implements Serializable{
	
	
	private static final long serialVersionUID = 1L;


	@Comment("目录下的图标信息")
	private List<VcImageInfo> imageInfos;
	
	
	@Comment("图标目录")
	private CcGeneralDir dir;


	public List<VcImageInfo> getImageInfos() {
		return imageInfos;
	}


	public void setImageInfos(List<VcImageInfo> imageInfos) {
		this.imageInfos = imageInfos;
	}


	public CcGeneralDir getDir() {
		return dir;
	}


	public void setDir(CcGeneralDir dir) {
		this.dir = dir;
	}
	
	
}
