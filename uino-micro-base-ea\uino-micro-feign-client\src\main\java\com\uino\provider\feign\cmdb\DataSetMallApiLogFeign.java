package com.uino.provider.feign.cmdb;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.alibaba.fastjson.JSONObject;
import com.binary.jdbc.Page;
import com.uino.bean.cmdb.base.dataset.log.DataSetMallApiLog;
import com.uino.provider.feign.config.BaseFeignConfig;

/**
 * 
 * <AUTHOR>
 *
 */
@Component
@FeignClient(name = "${feign-server-name:tarsier-platform}", path = "${feign-server-path:tarsier-platform}/feign/cmdb/datasetMallApiLog", configuration = {
		BaseFeignConfig.class })
public interface DataSetMallApiLogFeign {

	@PostMapping("findPage")
	Page<DataSetMallApiLog> findPage(@RequestBody JSONObject body);
	
}
