package com.uino.provider.feign.cmdb;

import com.alibaba.fastjson.JSONObject;
import com.binary.core.io.Resource;
import com.uino.bean.cmdb.base.dataset.DataSetMallApiRelationRule;
import com.uino.bean.cmdb.base.dataset.batch.DataSetTableResult;
import com.uino.bean.cmdb.base.dataset.query.DataSetMallApiDto;
import com.uino.bean.cmdb.base.dataset.query.DataSetMallApiRelationRuleDto;
import com.uino.bean.cmdb.business.dataset.*;
import com.uino.bean.dataset.base.DataSetPathInfoVo;
import com.uino.bean.dataset.base.DataSetThumbnailDTO;
import com.uino.bean.tp.query.MetricAttrValQueryDTO;
import com.uino.bean.tp.query.QueryDataTableDTO;
import com.uino.bean.tp.query.TpRuleReqDTO;
import com.uino.provider.feign.config.BaseFeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * 
 * <AUTHOR>
 *
 */
@Component
@FeignClient(name = "${feign-server-name:tarsier-platform}", path = "${feign-server-path:tarsier-platform}/feign/cmdb/dataset", configuration = {
		BaseFeignConfig.class })
public interface DataSetFeign {
	
	@PostMapping("saveOrUpdateDataSet")
    Long saveOrUpdateDataSet(@RequestBody JSONObject body);
	
	@PostMapping("delete")
    Boolean delete(@RequestBody Long id);
	
	@PostMapping("findDataSet")
	List<JSONObject> findDataSet(@RequestBody JSONObject body);
	
	@PostMapping("findDataSetById")
	JSONObject findDataSetById(@RequestBody JSONObject body);
	
	@PostMapping("execute")
	JSONObject execute(@RequestBody JSONObject body);

	@PostMapping("/realTime/execute")
	JSONObject realTimeExecute(@RequestBody JSONObject body);
	
	@PostMapping("shareDataSet")
	Boolean shareDataSet(@RequestBody JSONObject body);
	
	@PostMapping("getDataSetSheets")
	List<Map<String, Object>> getDataSetSheets(@RequestBody Long dataSetId);
	
	@PostMapping("queryRuleLegitimateCi")
	JSONObject queryRuleLegitimateCi(@RequestBody JSONObject body);
	
	@PostMapping("queryFriendByStartCiId")
	FriendInfo queryFriendByStartCiId(@RequestBody JSONObject body);

	@PostMapping("queryRuleNodeCiNum")
	Map<Long,Integer> queryRuleNodeCiNum(@RequestBody JSONObject body);

	@PostMapping("queryDisassembleFriendInfoDataByPath")
	RltRuleTableData queryDisassembleFriendInfoDataByPath(@RequestBody JSONObject body);

	@PostMapping("queryFriendByStartCiIdAndTargetClass")
	FriendInfo queryFriendByStartCiIdAndTargetClass(@RequestBody JSONObject body);
	
	@PostMapping("countStatistics")
	JSONObject countStatistics(@RequestBody JSONObject body);
	
	@PostMapping("getQueryCondition")
	List<JSONObject> getQueryCondition(@RequestBody JSONObject body);
	
	@PostMapping("queryDataSetResultBySheet")
	DataSetExeResultSheetPage queryDataSetResultBySheet(@RequestBody JSONObject body);

	@PostMapping("queryDataSetResultList")
	List<DataSetExeResultSheetPage> queryDataSetResultList(@RequestBody JSONObject body);
	
	@PostMapping("getResultUsingRule")
	List<DataSetExeResultSheetPage> getResultUsingRule(@RequestBody JSONObject body);

	@GetMapping("/getResultUsingRuleByDataSetId")
	public List<DataSetExeResultSheetPage> getResultUsingRuleByDataSetId(@RequestParam("dataSetId") Long dataSetId);
	
	@PostMapping("downloadSheetData")
	Resource downloadSheetData(@RequestBody JSONObject body);
	
	@PostMapping("isTaskRunning")
	Integer isTaskRunning(@RequestBody Long dataSetId);
	
	@PostMapping("getCode")
	String getCode(@RequestBody String jsonStr) throws Exception;
	
	@PostMapping("groupDataSetMallApiLogCount")
	List<Map<String, Object>> groupDataSetMallApiLogCount(@RequestBody Long domainId);

    @PostMapping("getTpMetricLabelDTOList")
    List<String> getTpMetricLabelDTOList(@RequestBody TpRuleReqDTO ruleReqDTO);

    @PostMapping("queryMetricAttrValue")
    List<String> queryMetricAttrValue(@RequestBody MetricAttrValQueryDTO query);

	@PostMapping("findAllRelationDateSet")
	List<DataSetMallApiRelationRule> findAllRelationDateSet(@RequestBody Long domainId);

	@PostMapping("updateMallApiExeResult")
	void updateMallApiExeResult(@RequestBody DataSetMallApiRelationRuleDto dataSetMallApiRelationRuleDto);

	@PostMapping("checkOperate")
	void checkOperate(@RequestBody DataSetMallApiDto dataSetMallApiDto);

	@PostMapping("queryFriendList")
    FriendBatchInfo queryFriendByStartCiIds(List<FriendInfoRequestDto> body);

	@PostMapping("findPathList")
    List<DataSetExeResultSheetPage> findPathList(@RequestBody JSONObject body);

	@PostMapping("thumbnail")
	String updateThumbnail(@RequestBody DataSetThumbnailDTO body);

	@PostMapping("pathInfo")
	List<DataSetPathInfoVo> getPathInfo(List<Long> dataSetIds);

    void updateDataSetById(Long id);

	@PostMapping("queryCiTableList")
	List<DataSetTableResult> queryCiTableList(@RequestBody QueryDataTableDTO body);

	@PostMapping("ciTableListExport")
	ResponseEntity<byte[]> ciTableListExport(List<DataSetTableResult> ret, QueryDataTableDTO body);

}
