package com.uino.web.sys.mvc;

import java.io.File;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import com.binary.core.util.BinaryUtils;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.sys.business.DictionaryInfoDto;
import com.uino.init.api.ApiResult;
import com.uino.init.api.ApiVersion;
import com.uino.util.sys.SysUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import com.binary.framework.util.ControllerUtils;
import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uinnova.product.vmdb.comm.doc.annotation.MvcDesc;
import com.uino.bean.cmdb.business.ImportResultMessage;
import com.uino.bean.sys.base.ESDictionaryClassInfo;
import com.uino.bean.sys.base.ESDictionaryItemInfo;
import com.uino.bean.sys.query.ESDictionaryItemSearchBean;
import com.uino.bean.sys.query.ExportDictionaryDto;
import com.uino.api.client.sys.IDictionaryApiSvc;

@ApiVersion(1)
@Api(value = "字典表", tags = {"字典表"})
@RestController
@RequestMapping("/sys/dict")
@MvcDesc(author = "zmj", desc = "字典表")
public class DictionaryMvc {

    @Autowired
    IDictionaryApiSvc dictApiSvc;


    @ApiOperation("保存字典表定义")
    @PostMapping("/saveDictionaryClassInfo")
    @ModDesc(desc = "保存字典表定义", pDesc = "字典表定义", pType = ESDictionaryClassInfo.class, rDesc = "字典表定义id", rType = Long.class)
    public ApiResult<Long> saveDictionaryClassInfo(HttpServletRequest request, HttpServletResponse response, @RequestBody ESDictionaryClassInfo bean) {
        bean = bean == null ? new ESDictionaryClassInfo() : bean;
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        bean.setDomainId(currentUserInfo.getDomainId());
        return ApiResult.ok(this).data(dictApiSvc.saveDictionaryClassInfo(bean));
    }

    @ApiOperation("查询字典表定义")
    @PostMapping("/getDictionaryClassList")
    @ModDesc(desc = "查询字典表定义", pDesc = "暂无参数", rDesc = "字典表定义", rType = List.class, rcType = ESDictionaryClassInfo.class)
    public ApiResult<List<DictionaryInfoDto>> getDictionaryClassList(HttpServletRequest request, HttpServletResponse response) {
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        return ApiResult.ok(this).data(dictApiSvc.getDictionaryClassList(currentUserInfo.getDomainId()));
    }

    @ApiOperation("根据id查询字典表定义")
    @PostMapping("/getDictionaryClassInfoById")
    @ModDesc(desc = "根据id查询字典表定义", pDesc = "字典定义id", rDesc = "字典表定义", rType = ESDictionaryClassInfo.class)
    public ApiResult<ESDictionaryClassInfo> getDictionaryClassInfoById(HttpServletRequest request, HttpServletResponse response, @RequestBody Long id) {

        return ApiResult.ok(this).data(dictApiSvc.getDictClassInfoById(id));
    }

    @ApiOperation("根据id删除字典定义")
    @PostMapping("/deleteDictClassInfoById")
    @ModDesc(desc = "根据id删除字典定义", pDesc = "字典定义id", rDesc = "操作是否成功", rType = Boolean.class)
    public ApiResult deleteDictClassInfoById(HttpServletRequest request, HttpServletResponse response, @RequestBody Long id) {
        //删除前先校验是否被分类应用该属性
        String result = dictApiSvc.checkAttrUsedMethod(id);
        if(BinaryUtils.isEmpty(result)){
            dictApiSvc.deleteDictClassInfoById(id);
            return ApiResult.ok(this).data(true);
        }else{
            return ApiResult.ok(this).data(result);
        }
    }

    @PostMapping("/searchDictItemPageByBean")
    @ApiOperation(value = "条件查询字典项-分页")
    public ApiResult<Page<ESDictionaryItemInfo>> searchDictItemPageByBean(@RequestBody ESDictionaryItemSearchBean bean) {
        bean = bean == null ? new ESDictionaryItemSearchBean() : bean;
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        bean.setDomainId(currentUserInfo.getDomainId());
        Page<ESDictionaryItemInfo> result = dictApiSvc.searchDictItemPageByBean(bean);
        return ApiResult.ok(this).data(result);
    }

    @PostMapping("/searchDictItemPageByIds")
    @ApiOperation(value = "条件查询字典项-分页")
    public ApiResult<Page<ESDictionaryItemInfo>> searchDictItemPageByIds(@RequestBody ESDictionaryItemSearchBean bean) {
        bean = bean == null ? new ESDictionaryItemSearchBean() : bean;
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        bean.setDomainId(currentUserInfo.getDomainId());
        Page<ESDictionaryItemInfo> result = dictApiSvc.searchDictItemPageByIds(bean);
        return ApiResult.ok(this).data(result);
    }

    @ApiOperation("条件查询字典项-不分页")
    @PostMapping("/searchDictItemListByBean")
    @ModDesc(desc = "条件查询字典项-不分页", pDesc = "查询条件", pcType = ESDictionaryItemSearchBean.class, rDesc = "字典项分页结果", rType = Page.class, rcType = ESDictionaryItemInfo.class)
    public ApiResult<List<ESDictionaryItemInfo>> searchDictItemListByBean(HttpServletRequest request, HttpServletResponse response, @RequestBody ESDictionaryItemSearchBean bean) {
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        bean.setDomainId(currentUserInfo.getDomainId());
        return ApiResult.ok(this).data(dictApiSvc.searchDictItemListByBean(bean));
    }

    @ApiOperation("保存字典项")
    @PostMapping("/saveOrUpdateDictionaryItem")
    @ModDesc(desc = "保存字典项", pDesc = "字典项传输对象", pcType = ESDictionaryItemInfo.class, rDesc = "字典项id", rType = Long.class)
    public ApiResult<Long> saveOrUpdateDictionaryItem(HttpServletRequest request, HttpServletResponse response, @RequestBody ESDictionaryItemInfo bean) {
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        bean.setDomainId(currentUserInfo.getDomainId());
        return ApiResult.ok(this).data(dictApiSvc.saveOrUpdateDictionaryItem(bean));
    }

    @ApiOperation("根据id删除字典项")
    @PostMapping("/deleteItemByIds")
    @ModDesc(desc = "根据id删除字典项", pDesc = "字典项id集合", pcType = Long.class, rDesc = "是否成功", rType = Integer.class)
    public ApiResult<Boolean> deleteItemByIds(HttpServletRequest request, HttpServletResponse response, @RequestBody Collection<Long> ids) {
        dictApiSvc.deleteItemByIds(ids);
        return ApiResult.ok(this).data(true);
    }

    @ApiOperation("导出字典项")
    @RequestMapping(value="/exportDictionaryItems",method = RequestMethod.POST)
    @ModDesc(desc = "导出字典项", pDesc = "字典分类id", pcType = Long.class, rDesc = "导出Excel文件", rType = File.class)
    public void exportDictionaryItems(HttpServletRequest request, HttpServletResponse response, @RequestBody ExportDictionaryDto dto) {
        ControllerUtils.returnResource(request, response, dictApiSvc.exportDictionaryItems(dto));
    }

    @ApiOperation(value="导入字典项")
    @PostMapping("/importDictionaryItems")
    @ModDesc(desc = "导入字典项", pDesc = "字典分类id及导入文件", rDesc = "导入明细", rType = ImportResultMessage.class)
    public ApiResult<ImportResultMessage> importDictionaryItems(HttpServletRequest request, HttpServletResponse response, @RequestParam("dictClassId") Long dictClassId, @RequestParam("file") MultipartFile file) {

        return ApiResult.ok(this).data(dictApiSvc.importDictionaryItems(dictClassId, file));
    }

    @ApiOperation(value="查询引用字典值")
    @PostMapping("/getExteralDictValues")
    @ModDesc(desc = "查询引用字典值", pDesc = "查询条件", pcType = ESDictionaryItemSearchBean.class, rDesc = "字典值集合", rType = List.class, rcType = String.class)
    public ApiResult<List<String>> getExteralDictValues(HttpServletRequest request, HttpServletResponse response, @RequestBody ESDictionaryItemSearchBean bean) {
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        bean.setDomainId(currentUserInfo.getDomainId());
        List<String> values = new ArrayList<>();
        if (BinaryUtils.isEmpty(bean.getDictDefIds())) {
            values = dictApiSvc.getExteralDictValues(bean);
        } else {
            values = dictApiSvc.getExteralDictValues(SysUtil.getCurrentUserInfo().getDomainId(), bean.getDictClassId(), bean.getDictDefIds());
        }
        return  ApiResult.ok(this).data(values);
    }
}
