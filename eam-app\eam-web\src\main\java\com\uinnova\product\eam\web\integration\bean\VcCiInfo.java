package com.uinnova.product.eam.web.integration.bean;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.web.diagram.bean.AttrInfo;
import com.uinnova.product.vmdb.comm.model.ci.CcCi;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.comm.model.ci.CcCiClass;
import com.uinnova.product.vmdb.comm.model.ci.CcFixAttrMapping;

public class VcCiInfo implements Serializable,Comparable<VcCiInfo> {
	private static final long serialVersionUID = 1L;

	
	
	@Comment("CI对象")
	private CcCi ci;
	
	
	@Comment("CI属性")
	private List<AttrInfo> attrs;
	
	
	
	@Comment("当前CI所属分类")
	private CcCiClass ciClass;
	
	
	
	@Comment("常柱属性映射对象")
	private CcFixAttrMapping fixMapping;
	
	
	
	@Comment("当前CI属性定义")
	private List<CcCiAttrDef> attrDefs;
	
	@Comment("CI属性map")
	private Map<String,String> attrsMap;


	@Comment("关联关系需要显示的属性")
	private Map<String,String> showRltAtrrs;

	//排序得分字段,得分高的排前面
	private int orderScore=-1;

	public Map<String, String> getShowRltAtrrs() {
		return showRltAtrrs;
	}



	public void setShowRltAtrrs(Map<String, String> showRltAtrrs) {
		this.showRltAtrrs = showRltAtrrs;
	}



	public Map<String, String> getAttrsMap() {
		return attrsMap;
	}



	public void setAttrsMap(Map<String, String> attrsMap) {
		this.attrsMap = attrsMap;
	}



	public CcCi getCi() {
		return ci;
	}



	public void setCi(CcCi ci) {
		this.ci = ci;
	}



	public List<AttrInfo> getAttrs() {
		return attrs;
	}



	public void setAttrs(List<AttrInfo> attrs) {
		this.attrs = attrs;
	}



	public CcCiClass getCiClass() {
		return ciClass;
	}



	public void setCiClass(CcCiClass ciClass) {
		this.ciClass = ciClass;
	}



	public List<CcCiAttrDef> getAttrDefs() {
		return attrDefs;
	}



	public void setAttrDefs(List<CcCiAttrDef> attrDefs) {
		this.attrDefs = attrDefs;
	}



	public CcFixAttrMapping getFixMapping() {
		return fixMapping;
	}



	public void setFixMapping(CcFixAttrMapping fixMapping) {
		this.fixMapping = fixMapping;
	}

	

	



	public int getOrderScore() {
		return orderScore;
	}



	public void setOrderScore(int orderScore) {
		this.orderScore = orderScore;
	}



	@Override
	public int compareTo(VcCiInfo o) {
		if(this.orderScore==o.orderScore)return 0;
		if(this.orderScore>o.orderScore)return -1;
		return 1;
	}
	
	
	
	
	
	
	
	
	
}
