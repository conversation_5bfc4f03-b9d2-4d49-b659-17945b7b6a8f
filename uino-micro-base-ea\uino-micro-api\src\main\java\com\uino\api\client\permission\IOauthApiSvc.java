package com.uino.api.client.permission;

import java.util.List;

import com.uino.bean.permission.base.OAuthClientDetail;
import com.uino.bean.permission.base.OauthRefreshTokenDetail;
import com.uino.bean.permission.base.OauthResourceDetail;
import com.uino.bean.permission.base.OauthTokenDetail;
import com.uino.bean.permission.business.request.RegisterClientReq;

/**
 * 授权认证相关api
 * 
 * <AUTHOR>
 */
public interface IOauthApiSvc {

    // 客户端信息相关-------------------------
    /**
     * 根据客户端code获取客户端详情
     * 
     * @param clientCode
     * @return
     */
    OAuthClientDetail getClientInfoByCode(String clientCode);

    /**
     * 注册客户端
     * 
     * @param req
     */
    void register(RegisterClientReq req);

    /**
     * 根据code移除客户端
     * 
     * @param clientCode
     */
    void removeClientInfoByCode(String clientCode);

    /**
     * 获取所有客户端
     * 
     * @return
     */
    List<OAuthClientDetail> getAllClents();
    // 资源信息相关---------------------------------------

    /**
     * 根据资源名称获取资源信息
     * 
     * @param name
     * @return
     */
    OauthResourceDetail getResourceDetail(String name);
    
    /**
	 * 保存oauth资源段
	 * @param saveDto
	 * @return
	 */
	public boolean save(OauthResourceDetail saveDto);

	/**
	 * 获取所有oauth资源端列表
	 * @return
	 */
	public List<OauthResourceDetail> list();

    // token相关--------------------------------------
    /**
     * 持久化
     * 
     * @param tokenDetail
     * @return
     */
    Long persistenceToken(OauthTokenDetail tokenDetail);

    /**
     * 根据刷新token获取accesstoken
     * 
     * @param reTokenCode
     * @return
     */
    public OauthTokenDetail getTokenDetailByReTokenCode(String reTokenCode);

    /**
     * 获取token
     *
     * @param tokenCode
     * @return
     */
    OauthTokenDetail getTokenDetailByCode(String tokenCode);

    /**
     * 获取token
     * 
     * @param authId
     * @return
     */
    OauthTokenDetail getTokenDetailByAuthId(String authId);

    /**
     * 获取token
     * 
     * @param clientId
     * @param userLoginName
     * @return
     */
    List<OauthTokenDetail> getTokenDetail(String clientId, String userLoginName);

    /**
     * 根据tokenCode删除token
     * 
     * @param tokenCode
     */
    void delByCode(String tokenCode);

    /**
     * 根据刷新tokencode删除accesstoken
     * 
     * @param reTokenCode
     */
    void delByRefreshTokenCode(String reTokenCode);

    // 刷新token相关-----------分割线----------------------
    /**
     * 持久化刷新token
     * 
     * @param reRokenDetail
     * @return
     */
    Long persistenceRefreshToken(OauthRefreshTokenDetail reRokenDetail);

    /**
     * 根据tokencode获取刷新token
     * 
     * @param tokenCode
     * @return
     */
    OauthRefreshTokenDetail getRefreshTokenDetail(String tokenCode);

    /**
     * 根据tokencode删除刷新token
     * 
     * @param tokenCode
     */
    void delRefreshTokenByCode(String tokenCode);
}
