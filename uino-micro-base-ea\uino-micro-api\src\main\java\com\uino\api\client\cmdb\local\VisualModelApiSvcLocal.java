package com.uino.api.client.cmdb.local;

import java.util.Collections;
import java.util.List;

import com.uino.bean.cmdb.base.ESVisualModelVo;
import com.uino.bean.cmdb.base.LibType;
import org.elasticsearch.index.query.QueryBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.uino.service.cmdb.microservice.IVisualModelSvc;
import com.uino.bean.cmdb.base.ESVisualModel;
import com.uino.api.client.cmdb.IVisualModelApiSvc;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
public class VisualModelApiSvcLocal implements IVisualModelApiSvc {

	@Autowired
    private IVisualModelSvc svc;

	@Override
	public List<ESVisualModel> queryVisualModels(Long domainId) {
		return svc.queryVisualModels(domainId);
	}

	@Override
	public Long saveVisualModel(ESVisualModel model) {
		return svc.saveVisualModel(model);
	}

	@Override
	public Long saveVisualModelPrivate(ESVisualModel model) {
		return svc.saveVisualModelPrivate(model);
	}

	@Override
	public Boolean flushAllEnableVisualModelCiRlt() {
		return svc.flushAllEnableVisualModelCiRlt();
	}

	public List<ESVisualModel> getVisualModelByQuery(QueryBuilder query, LibType libType) {
		return svc.getVisualModelByQuery(query, libType);
	}

	@Override
	public List<ESVisualModelVo> queryVisualModels(Long domainId, LibType libType, String loginCode) {
		return svc.queryVisualModels(domainId, libType,loginCode);
	}

	@Override
	public List<ESVisualModelVo> queryVisualModelsNoChickExit(Long domainId, LibType libType, String loginCode) {
		return svc.queryVisualModelsNoChickExit(domainId, libType,loginCode);
	}

	@Override
	public void updateVisualModelName(ESVisualModel model) {
		svc.updateVisualModelName(model);
	}

	@Override
	public void deleteVisualModel(Long id) {
		svc.deleteVisualModel(id);
	}

	@Override
	public void delVMThumbnailBySheetId(Long id, Long sheetId) {
		svc.delVMThumbnailBySheetId(id, sheetId);
	}

	@Override
	public ESVisualModel queryVisualModelById(Long id, Long domainId) {
		return svc.queryVisualModelById(id, domainId);
	}

	@Override
	public ESVisualModel queryPrivateVisualModelById(Long domainId, Long visId) {
		return svc.queryPrivateVisualModelById(domainId, visId);
	}

	@Override
	public void delValidVisData() {
		svc.delValidVisData();
	}


}
