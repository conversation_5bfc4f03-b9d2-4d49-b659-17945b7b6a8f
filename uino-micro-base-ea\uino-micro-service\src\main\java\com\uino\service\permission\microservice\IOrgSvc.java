package com.uino.service.permission.microservice;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.binary.jdbc.Page;
import com.uino.bean.permission.base.SysOrg;
import com.uino.bean.permission.business.OrgNodeInfo;
import com.uino.bean.permission.business.request.AddOrRemoveRoleToOrgRequestDto;
import com.uino.bean.permission.business.request.AddOrRemoveUserToOrgRequestDto;
import com.uino.bean.permission.business.request.ExtendOrgRoleRequestDto;
import com.uino.bean.permission.business.request.InterchangeOrgNoRequestDto;
import com.uino.bean.permission.business.request.SaveOrgRequestDto;
import com.uino.bean.permission.business.request.SetUsersOrgsRequestDto;
import com.uino.bean.permission.query.CSysOrg;

/**
 * 组织管理
 *
 * <AUTHOR>
 */
public interface IOrgSvc {

    /**
     * 获取组织tree
     *
     * @return
     */
    public OrgNodeInfo getOrgTree(Long domainId);


    /**
     * 根据父级id查询组织列表
     *
     * @param parentId
     * @return
     */
    List<SysOrg> getOrgListByParentId(Long parentId);

    /**
     * 根据传入的rootOrg成树
     * @param domainId 领域
     * @param rootOrg 组织
     * @param findUser 是否查询组织下的用户
     * @return
     */
    public OrgNodeInfo getOrgTree(Long domainId, Long rootOrg, boolean findUser);

    /**
     * 获取根组织
     * @param domainId
     * @return
     */
    public SysOrg getRootOrg(Long domainId);

    /**
     * 根据数据量动态获取组织tree
     */
    public OrgNodeInfo getOrgTreeV2(Long domainId, Long orgId);

    OrgNodeInfo getOrgTreeV3(Long domainId);

    /**
     * 持久化组织
     *
     * @param request
     * @return
     */
    public Long saveOrUpdateOrg(SaveOrgRequestDto request);

    /**
     * 移除组织
     *
     * @param removeOrgIds
     */
    public void deleteOrg(Set<Long> removeOrgIds);

    /**
     * 向某个组织下添加用户
     *
     * @param request
     */
    public void addUserForOrg(AddOrRemoveUserToOrgRequestDto request);

    /**
     * 将用户从某组织下移除
     *
     * @param request
     */
    public void removeUserForOrg(AddOrRemoveUserToOrgRequestDto request);

    /**
     * 设置某些用户的所在组织
     *
     * @param reqDto
     */
    public void setUsersOrgs(SetUsersOrgsRequestDto reqDto);

    /**
     * 向某个组织绑定角色
     *
     * @param request
     */
    public void addRoleForOrg(AddOrRemoveRoleToOrgRequestDto request);

    /**
     * 将某些角色从组织下移除
     *
     * @param request
     */
    public void removeRoleForOrg(AddOrRemoveRoleToOrgRequestDto request);

    /**
     * 获取指定组织下的用户
     *
     * @param orgId
     * @return
     */
    public Set<Long> getUserIds(Long orgId);

    /**
     * 获取指定组织下的角色
     *
     * @param orgId
     * @return
     */
    public Set<Long> getRoleIds(Long orgId);

    /**
     * 获取指定角色下的组织
     *
     * @param roleId
     * @return
     */
    public List<SysOrg> getOrgByRoleId(Long roleId);

    /**
     * 根据查询条件分页查询组织
     *
     * @param pageNum
     * @param pageSize
     * @param query
     * @return
     */
    public Page<SysOrg> queryPageByCdt(int pageNum, int pageSize, CSysOrg query);

    /**
     * 不分页条件查询组织
     *
     * @param query
     * @return
     */
    public List<SysOrg> queryListByCdt(CSysOrg query);

    /**
     * 用户继承组织绑定的角色
     *
     * @param reqDto
     */
    public void userExtentOrgRole(ExtendOrgRoleRequestDto reqDto);

    /**
     * 交换组织顺序
     *
     * @param reqDto
     */
    public void interchangeOrgNo(InterchangeOrgNoRequestDto reqDto);

    /**
     * 根据组织带路径全名称获取组织信息
     *
     * @param fullName
     * @return
     * @see #getOrgByOrgFullName(String, Map)
     */
    public SysOrg getOrgByOrgFullName(Long domainId, String fullName);

    /**
     * 根据组织带路径全名称获取组织信息
     *
     * @param fullName     eg：优锘科技/研发部门/DMV
     * @param orgStructure
     * @return 存在则返回组织信息，匹配失败返回空
     * @see {@link #getOrgStructure()}
     */
    public SysOrg getOrgByOrgFullName(String fullName, Map<String, OrgNodeInfo> orgStructure);

    /**
     * 获取组织结构
     *
     * @return
     */
    public Map<String, OrgNodeInfo> getOrgStructure(Long domainId);

    /**
     * 获取用户所在的组织
     *
     * @param userId 用户ID
     * @return
     */
    public List<SysOrg> getListByUserId(Long userId);

    /**
     * 获取组织{id:allName}键值对
     * <p>
     * eg: {45:"优锘科技/产品研发部/1组"}
     *
     * @param orgIds
     * @return
     */
    public Map<Long, String> getOrgIdAllNameMap(Long domainId, Collection<Long> orgIds);

}
