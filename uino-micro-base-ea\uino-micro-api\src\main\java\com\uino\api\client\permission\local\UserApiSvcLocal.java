package com.uino.api.client.permission.local;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import com.uino.bean.permission.base.SysModuleOutSide;
import com.uino.bean.permission.query.CAuthDataModuleBean;
import com.uino.dao.BaseConst;
import org.elasticsearch.index.query.QueryBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.binary.jdbc.Page;
import com.uino.service.permission.microservice.IUserSvc;
import com.uino.bean.cmdb.business.ImportResultMessage;
import com.uino.bean.cmdb.business.ImportSheetMessage;
import com.uino.bean.permission.base.SysRoleDataModuleRlt;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.permission.business.CurrentUserInfo;
import com.uino.bean.permission.business.ModuleNodeInfo;
import com.uino.bean.permission.business.SysUserPwdResetParam;
import com.uino.bean.permission.business.UserInfo;
import com.uino.bean.permission.business.request.SaveUserRoleRltRequestDto;
import com.uino.bean.permission.query.CSysUser;
import com.uino.bean.permission.query.UserSearchBeanExtend;
import com.uino.api.client.permission.IUserApiSvc;

/**
 * <AUTHOR>
 */
@Service
public class UserApiSvcLocal implements IUserApiSvc {

    @Autowired
    private IUserSvc userSvc;

    @Override
    public Long saveOrUpdate(UserInfo userInfo) {
        return userSvc.saveOrUpdate(userInfo);
    }

    @Override
    public ImportSheetMessage saveOrUpdateUserInfoBatch(List<UserInfo> userInfos) {
        return userSvc.saveOrUpdateUserInfoBatch(BaseConst.DEFAULT_DOMAIN_ID, false, userInfos);
    }

    @Override
    public ImportSheetMessage saveOrUpdateUserInfoBatch(Long domainId, boolean isUpdatePassword, List<UserInfo> userInfos) {
        return userSvc.saveOrUpdateUserInfoBatch(domainId, isUpdatePassword, userInfos);
    }

    @Override
    public ImportSheetMessage syncUserBatch(List<UserInfo> userInfos) {
        return userSvc.syncUserBatch(BaseConst.DEFAULT_DOMAIN_ID, userInfos);
    }

    @Override
    public ImportSheetMessage syncUserBatch(Long domainId, List<UserInfo> userInfos) {
        return userSvc.syncUserBatch(domainId, userInfos);
    }

    @Override
    public List<SysUser> getSysUserByCdt(CSysUser cdt) {
        List<SysUser> sysUser = userSvc.getSysUserByCdt(cdt);
        return sysUser;
    }

    @Override
    public UserInfo getUserInfoById(Long userId) {
        return userSvc.getUserInfoById(userId);
    }

    @Override
    public UserInfo getUserInfoByLoginCode(String loginCode) {
        return userSvc.getUserInfoByLoginCode(loginCode);
    }

    @Override
    public List<UserInfo> getUserInfoByCdt(CSysUser cdt, boolean rmSensitiveData) {
        return userSvc.getUserInfoByCdt(cdt, rmSensitiveData);
    }

    @Override
    public Page<UserInfo> getListByQuery(UserSearchBeanExtend bean) {
        return userSvc.getListByQuery(bean);
    }

    @Override
    public List<SysUser> getUserByRoleId(Long roleId) {
        return userSvc.getUserByRoleId(roleId);
    }

    @Override
    public Long updateCurUser(SysUser op, MultipartFile file) {
        return userSvc.updateCurUser(op, file);
    }

    @Override
    public Long resetPwd(SysUserPwdResetParam pwdParam) {
        return userSvc.resetPwd(BaseConst.DEFAULT_DOMAIN_ID, pwdParam);
    }

    @Override
    public Long resetPwd(Long domainId, SysUserPwdResetParam pwdParam) {
        return userSvc.resetPwd(domainId, pwdParam);
    }

    @Override
    public Long resetPwdByAdmin(SysUserPwdResetParam pwdParam) {
        return userSvc.resetPwdByAdmin(pwdParam);
    }

    @Override
    public ImportResultMessage importUserByExcel(MultipartFile file) {
        return userSvc.importUserByExcel(BaseConst.DEFAULT_DOMAIN_ID, file);
    }

    @Override
    public ImportResultMessage importUserByExcel(Long domainId, MultipartFile file) {
        return userSvc.importUserByExcel(domainId, file);
    }

    @Override
    public ResponseEntity<byte[]> exportUserToExcel(Boolean isTpl, CSysUser cdt) {
        return userSvc.exportUserToExcel(isTpl, cdt);
    }

    @Override
    public Integer deleteById(Long userId) {
        return userSvc.deleteById(userId);
    }

    @Override
    public Integer deleteSysUserByLoginCodeBatch(List<String> loginCodes) {
        return userSvc.deleteSysUserByLoginCodeBatch(BaseConst.DEFAULT_DOMAIN_ID, loginCodes);
    }

    @Override
    public Integer deleteSysUserByLoginCodeBatch(Long domainId, List<String> loginCodes) {
        return userSvc.deleteSysUserByLoginCodeBatch(domainId, loginCodes);
    }

    @Override
    public Integer saveUserRoleRlt(SaveUserRoleRltRequestDto bean) {
        return userSvc.saveUserRoleRlt(bean);
    }

    @Override
    public ModuleNodeInfo getModuleTree(Long userId) {
        return userSvc.getModuleTree(BaseConst.DEFAULT_DOMAIN_ID, userId);
    }

    @Override
    public ModuleNodeInfo getModuleTree(Long domainId, Long userId) {
        return userSvc.getModuleTree(domainId, userId);
    }

    @Override
    public Map<String, List<SysRoleDataModuleRlt>> getDataModule(Long userId, List<String> moduleCodes) {
        return userSvc.getDataModule(userId, moduleCodes);
    }

    @Override
    public Long enshrineModule(Long userId, Long moduleId) {
        return userSvc.enshrineModule(BaseConst.DEFAULT_DOMAIN_ID, userId, moduleId);
    }

    @Override
    public Long enshrineModule(Long domainId, Long userId, Long moduleId) {
        return userSvc.enshrineModule(domainId, userId, moduleId);
    }

    @Override
    public Integer unenshrineModule(Long userId, Long moduleId) {
        return userSvc.unenshrineModule(BaseConst.DEFAULT_DOMAIN_ID, userId, moduleId);
    }

    @Override
    public Integer unenshrineModule(Long domainId, Long userId, Long moduleId) {
        return userSvc.unenshrineModule(domainId, userId, moduleId);
    }

    @Override
    public long countByCondition(QueryBuilder query) {
        return userSvc.countByCondition(BaseConst.DEFAULT_DOMAIN_ID, query);
    }

    @Override
    public long countByCondition(Long domainId, QueryBuilder query) {
        return userSvc.countByCondition(domainId, query);
    }

    @Override
    public int recordLoginFailNum(String loginName, boolean success) {
        // TODO Auto-generated method stub
        return userSvc.recordLoginFailNum(BaseConst.DEFAULT_DOMAIN_ID, loginName, success);
    }

    @Override
    public int recordLoginFailNum(Long domainId, String loginName, boolean success) {
        return userSvc.recordLoginFailNum(domainId, loginName, success);
    }

    @Override
    public CurrentUserInfo getCurrentUser() {
        // TODO Auto-generated method stub
        return userSvc.getCurrentUser();
    }

    @Override
    public int unLockUsersByDuration(Long durationSecond) {
        // TODO Auto-generated method stub
        return userSvc.unLockUsersByDuration(durationSecond);
    }

    @Override
    public int getLoginFailLockNum() {
        // TODO Auto-generated method stub
        return userSvc.getLoginFailLockNum();
    }

    @Override
    public long getUserLockDuration() {
        // TODO Auto-generated method stub
        return userSvc.getUserLockDuration();
    }

    @Override
    public void verifyAuth() {
        userSvc.verifyAuth();
    }

    @Override
    public List<SysModuleOutSide> getModulesByLoginCode(String loginCode) {
        return userSvc.getModulesByLoginCode(BaseConst.DEFAULT_DOMAIN_ID, loginCode);
    }

    @Override
    public List<SysModuleOutSide> getModulesByLoginCode(Long domainId, String loginCode) {
        return userSvc.getModulesByLoginCode(domainId, loginCode);
    }

    @Override
    public Map<Long, Map<String, Integer>> getDataPermissionByUser(CAuthDataModuleBean cAuthDataModuleBean) {
        return userSvc.getDataPermissionByUser(cAuthDataModuleBean);
    }

    @Override
    public List<SysUser> getUserByRoleName(String roleName) {
        return userSvc.getUserByRoleName(roleName);
    }

    @Override
    public List<SysUser> getUserInfoByName(String userName) {
        return userSvc.getUserInfoByName(userName);
    }

    @Override
    public Page<SysUser> findUserInfoByNameForPage(Integer pageNum, Integer pageSize, String userName) {
        return userSvc.findUserInfoByNameForPage(pageNum, pageSize, userName);
    }

    @Override
    public Map<String, String> getNameByCodes(Collection<String> loginCodes) {
        return userSvc.getNameByCodes(loginCodes);
    }

    @Override
    public Page<SysUser> queryPageByRoleName(String roleName, String loginName, int pageNum, int pageSize) {
        return userSvc.queryPageByRoleName(roleName, loginName, pageNum, pageSize);
    }
}
