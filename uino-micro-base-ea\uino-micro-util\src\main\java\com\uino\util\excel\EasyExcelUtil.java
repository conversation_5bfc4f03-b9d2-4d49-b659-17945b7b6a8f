package com.uino.util.excel;

import java.io.File;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

import org.apache.poi.openxml4j.util.ZipSecureFile;
import org.springframework.util.Assert;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.builder.ExcelReaderBuilder;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.read.metadata.ReadSheet;

import lombok.Data;

/**
 * 简单读取excel工具
 * 
 * <AUTHOR>
 *
 */
public class EasyExcelUtil {
	static {
		ZipSecureFile.setMinInflateRatio(-1d);
	}

    /**
     * 读取sheet内容
     * 
     * @param sheetName
     *            要读取的sheet名称
     * @param sheetNo
     *            要读取的sheet编号
     * @param excelInputStream
     *            读取excel的流
     * @return sheet内容
     */
    public static SheetData readSheet(String sheetName, Integer sheetNo, InputStream excelInputStream) {
        ReadExcelListener defaultReadListener = new ReadExcelListener(null, -1);
        return readSheet(sheetName, sheetNo, getExcelReader(excelInputStream, defaultReadListener),
                defaultReadListener);
    }

    /**
     * 读取sheet内容
     * 
     * @param sheetName
     *            要读取的sheet名称
     * @param sheetNo
     *            要读取的sheet编号
     * @param excelFile
     *            excel文件
     * @return sheet内容
     */
    public static SheetData readSheet(String sheetName, Integer sheetNo, File excelFile) {
        ReadExcelListener defaultReadListener = new ReadExcelListener(null, -1);
        return readSheet(sheetName, sheetNo, getExcelReader(excelFile, defaultReadListener), defaultReadListener);
    }

    /**
     * 阅读excel
     * 
     * @param sheetName
     * @param sheetNo
     * @param excelReader
     * @return
     */
    private static SheetData readSheet(String sheetName, Integer sheetNo, ExcelReaderBuilder excelReader,
            ReadExcelListener readListener) {
        Assert.isTrue(sheetName != null || sheetNo != null, "sheetName/sheetNo至少指定一个");
        SheetData sheetData = new SheetData();
        if (sheetNo != null) {
            excelReader.sheet(sheetNo).headRowNumber(0).doRead();
        } else if (sheetName != null) {
            excelReader.sheet(sheetName).headRowNumber(0).doRead();
        }
        sheetData.setRows(readListener.getRows());
        sheetData.setTitles(readListener.getTitles());
        return sheetData;
    }

    /**
     * 获取excel所有sheet名称
     * 
     * @param excelInputStream
     * @return
     */
    public static List<String> getExcelAllSheetNames(InputStream excelInputStream) {
        return getExcelAllSheetNames(getExcelReader(excelInputStream, null));
    }

    /**
     * 获取excel所有sheet名称
     * 
     * @param excelFile
     * @return
     */
    public static List<String> getExcelAllSheetNames(File excelFile) {
        return getExcelAllSheetNames(getExcelReader(excelFile, null));
    }

    /**
     * 获取excel所有sheet名称
     * 
     * @param excelReader
     * @return
     */
    private static List<String> getExcelAllSheetNames(ExcelReaderBuilder excelReader) {
        List<ReadSheet> sheets = excelReader.build().excelExecutor().sheetList();
        List<String> sheetNames = new ArrayList<>();
        sheets.forEach(sheet -> sheetNames.add(sheet.getSheetName()));
        return sheetNames;
    }

    /**
     * 获取excel阅读者
     * 
     * @param excelFile
     * @return
     */
    private static ExcelReaderBuilder getExcelReader(File excelFile, ReadListener<?> readListener) {
        return EasyExcel.read(excelFile, readListener);
    }

    /**
     * 获取excel阅读者
     * 
     * @param excelInputStream
     * @return
     */
    private static ExcelReaderBuilder getExcelReader(InputStream excelInputStream, ReadListener<?> readListener) {
        return EasyExcel.read(excelInputStream, readListener);
    }

    /**
     * sheet页数据
     * 
     * <AUTHOR>
     *
     */
    @Data
    public static class SheetData {

        private List<String> titles;

        private List<String[]> rows;
    }
}
