package com.uinnova.product.eam.base.diagram.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.util.List;

/**
 * @Classname
 * @Description 视图sheet实体类
 * <AUTHOR>
 * @Date 2021-06-03-16:31
 */
@Data
public class ESDiagramSheetDTO extends ESDiagramDEnergy implements EntityBean {
    @JSONField(name = "id")
    private Long id;

    @JSONField(name = "name")
    private String name;

    @J<PERSON><PERSON>ield(name = "sheetId")
    private String sheetId;

    @JSONField(name = "active")
    private Boolean active;

    @JSONField(name = "sheetSign")
    private String sheetSign;

    @JSONField(name = "sheetOrder")
    private Long sheetOrder;

    @JSONField(name = "sheetSetting")
    private SheetSettingDTO sheetSetting;

    @JsonIgnore
    private Long diagramId;

    @JSONField(name = "diagramClass")
    private String diagramClass;

    @JSONField(name = "linkFromPortIdProperty")
    private String linkFromPortIdProperty;

    @JSONField(name = "linkToPortIdProperty")
    private String linkToPortIdProperty;

    @JSONField(name = "modelData")
    private String modelData;

    @JSONField(name = "copyId")
    private String copyId;

    @JSONField(name = "createTime")
    private Long createTime;

    @JSONField(name = "transFlag")
    private Integer transFlag;

    @JSONField(name = "modifyTime")
    private Long modifyTime;
    @Comment("保留字段1")
    private String reserveredField1;
    @Comment("保留字段2")
    private String reserveredField2;
    @Comment("保留字段3")
    private String reserveredField3;
    @Comment("保留字段4")
    private String reserveredField4;
    @Comment("保留字段5")
    private String reserveredField5;

    public static class SheetSettingDTO {
        @JSONField(name = "isInfiniteCanvas")
        private Boolean isInfiniteCanvas;
        @JSONField(name = "isAutoSave")
        private Boolean isAutoSave;
        @JSONField(name = "canvasX")
        private Integer canvasX;
        @JSONField(name = "canvasY")
        private Integer canvasY;
        @JSONField(name = "canvasWidth")
        private Integer canvasWidth;
        @JSONField(name = "canvasHeight")
        private Integer canvasHeight;
        @JSONField(name = "isAutoExpandCanvas")
        private Boolean isAutoExpandCanvas;
        @JSONField(name = "isSplitLine")
        private Boolean isSplitLine;
        @JSONField(name = "isStandardCanvasSize")
        private Boolean isStandardCanvasSize;
        @JSONField(name = "canvasIsHorizontal")
        private Boolean canvasIsHorizontal;
        @JSONField(name = "canvasBackgroundColor")
        private String canvasBackgroundColor;
        @JSONField(name = "canvasMargin")
        private Integer canvasMargin;
        @JSONField(name = "linkJumpOver")
        private Boolean linkJumpOver;
        @JSONField(name = "canvasMarginVisible")
        private Boolean canvasMarginVisible;
        @JSONField(name = "gridWidth")
        private Integer gridWidth;
        @JSONField(name = "layer")
        private List<LayerDTO> layer;
        @JSONField(name = "unit")
        private String unit;

        public Boolean getIsAutoSave() {
            return isAutoSave;
        }

        public void setIsAutoSave(Boolean isAutoSave) {
            this.isAutoSave = isAutoSave;
        }

        public Boolean getLinkJumpOver() {
            return linkJumpOver;
        }

        public void setLinkJumpOver(Boolean linkJumpOver) {
            this.linkJumpOver = linkJumpOver;
        }

        public Boolean getIsInfiniteCanvas() {
            return isInfiniteCanvas;
        }

        public void setIsInfiniteCanvas(Boolean isInfiniteCanvas) {
            this.isInfiniteCanvas = isInfiniteCanvas;
        }

        public Integer getCanvasX() {
            return canvasX;
        }

        public void setCanvasX(Integer canvasX) {
            this.canvasX = canvasX;
        }

        public Integer getCanvasY() {
            return canvasY;
        }

        public void setCanvasY(Integer canvasY) {
            this.canvasY = canvasY;
        }

        public Integer getCanvasWidth() {
            return canvasWidth;
        }

        public void setCanvasWidth(Integer canvasWidth) {
            this.canvasWidth = canvasWidth;
        }

        public Integer getCanvasHeight() {
            return canvasHeight;
        }

        public void setCanvasHeight(Integer canvasHeight) {
            this.canvasHeight = canvasHeight;
        }

        public Boolean getIsAutoExpandCanvas() {
            return isAutoExpandCanvas;
        }

        public void setIsAutoExpandCanvas(Boolean isAutoExpandCanvas) {
            this.isAutoExpandCanvas = isAutoExpandCanvas;
        }

        public Boolean getIsSplitLine() {
            return isSplitLine;
        }

        public void setIsSplitLine(Boolean isSplitLine) {
            this.isSplitLine = isSplitLine;
        }

        public Boolean getIsStandardCanvasSize() {
            return isStandardCanvasSize;
        }

        public void setIsStandardCanvasSize(Boolean isStandardCanvasSize) {
            this.isStandardCanvasSize = isStandardCanvasSize;
        }

        public Boolean getCanvasIsHorizontal() {
            return canvasIsHorizontal;
        }

        public void setCanvasIsHorizontal(Boolean canvasIsHorizontal) {
            this.canvasIsHorizontal = canvasIsHorizontal;
        }

        public String getCanvasBackgroundColor() {
            return canvasBackgroundColor;
        }

        public void setCanvasBackgroundColor(String canvasBackgroundColor) {
            this.canvasBackgroundColor = canvasBackgroundColor;
        }

        public Integer getCanvasMargin() {
            return canvasMargin;
        }

        public void setCanvasMargin(Integer canvasMargin) {
            this.canvasMargin = canvasMargin;
        }

        public Boolean getCanvasMarginVisible() {
            return canvasMarginVisible;
        }

        public void setCanvasMarginVisible(Boolean canvasMarginVisible) {
            this.canvasMarginVisible = canvasMarginVisible;
        }

        public Integer getGridWidth() {
            return gridWidth;
        }

        public void setGridWidth(Integer gridWidth) {
            this.gridWidth = gridWidth;
        }

        public List<LayerDTO> getLayer() {
            return layer;
        }

        public void setLayer(List<LayerDTO> layer) {
            this.layer = layer;
        }

        public String getUnit() {
            return unit;
        }

        public void setUnit(String unit) {
            this.unit = unit;
        }

        public static class LayerDTO {
            @JSONField(name = "name")
            private String name;
            @JSONField(name = "isLock")
            private Boolean isLock;
            @JSONField(name = "isVisibility")
            private Boolean isVisibility;
            @JSONField(name = "selected")
            private Boolean selected;
            @JSONField(name = "layerName")
            private String layerName;

            public String getName() {
                return name;
            }

            public void setName(String name) {
                this.name = name;
            }

            public Boolean getIsLock() {
                return isLock;
            }

            public void setIsLock(Boolean isLock) {
                this.isLock = isLock;
            }

            public Boolean getIsVisibility() {
                return isVisibility;
            }

            public void setIsVisibility(Boolean isVisibility) {
                this.isVisibility = isVisibility;
            }

            public Boolean getSelected() {
                return selected;
            }

            public void setSelected(Boolean selected) {
                this.selected = selected;
            }

            public String getLayerName() {
                return layerName;
            }

            public void setLayerName(String layerName) {
                this.layerName = layerName;
            }

            @Override
            public String toString() {
                return "LayerDTO{" +
                        "name='" + name + '\'' +
                        ", isLock=" + isLock +
                        ", isVisibility=" + isVisibility +
                        ", selected=" + selected +
                        ", layerName='" + layerName + '\'' +
                        '}';
            }
        }

        @Override
        public String toString() {
            return "SheetSettingDTO{" +
                    "isInfiniteCanvas=" + isInfiniteCanvas +
                    ", isAutoSave=" + isAutoSave +
                    ", canvasX=" + canvasX +
                    ", canvasY=" + canvasY +
                    ", canvasWidth=" + canvasWidth +
                    ", canvasHeight=" + canvasHeight +
                    ", isAutoExpandCanvas=" + isAutoExpandCanvas +
                    ", isSplitLine=" + isSplitLine +
                    ", isStandardCanvasSize=" + isStandardCanvasSize +
                    ", canvasIsHorizontal=" + canvasIsHorizontal +
                    ", canvasBackgroundColor='" + canvasBackgroundColor + '\'' +
                    ", canvasMargin=" + canvasMargin +
                    ", linkJumpOver=" + linkJumpOver +
                    ", canvasMarginVisible=" + canvasMarginVisible +
                    ", gridWidth=" + gridWidth +
                    ", layer=" + layer +
                    ", unit='" + unit + '\'' +
                    '}';
        }
    }
}
