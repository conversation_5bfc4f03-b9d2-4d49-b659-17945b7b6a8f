package com.uinnova.product.eam.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.binary.core.exception.BinaryException;
import com.binary.core.util.BinaryUtils;
import com.uinnova.product.eam.base.util.EamUtil;
import com.uinnova.product.eam.comm.model.es.CITableSnapshot;
import com.uinnova.product.eam.model.CITableDataInfo;
import com.uinnova.product.eam.model.bm.DataSetEnum;
import com.uinnova.product.eam.service.CITableSnapshotSvc;
import com.uinnova.product.eam.service.ICISwitchSvc;
import com.uinnova.product.eam.service.cj.service.PlanDesignInstanceService;
import com.uinnova.product.eam.service.es.CITableSnapshotDao;
import com.uinnova.product.vmdb.comm.model.ci.CCcCi;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uino.bean.cmdb.base.LibType;
import com.uino.util.sys.SysUtil;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CIDataSnapshotSvcImpl implements CITableSnapshotSvc {

    @Autowired
    private PlanDesignInstanceService planDesignInstanceService;

    @Autowired
    private CITableSnapshotDao ciTableSnapshotDao;

    @Autowired
    private ICISwitchSvc iciSwitchSvc;

    @Override
    public Long saveorupdateCiTableSnapshot(String assetKey, Integer assetType, List<String> ciCodes) {

        List<CITableDataInfo> realTimeCIInfo = this.getRealTimeDataSetInfo(assetKey, assetType, ciCodes);
        List<CITableDataInfo> snapshotCIInfo = this.getSnapshotCITableInfo(assetKey, assetType, ciCodes);

        Map<String, CITableDataInfo> snapshotCIMap = snapshotCIInfo
                .stream()
                .collect(Collectors.toMap(CITableSnapshot::getCiCode, each -> each, (k1, k2) -> k2));

        // todo

        if (!CollectionUtils.isEmpty(snapshotCIInfo) &&
                snapshotCIInfo.get(0).getFrom() == DataSetEnum.SNAPSHOT_DATA) {
            // 实时数据覆盖做更新
            log.info("更新临时ci数据表");
            realTimeCIInfo.forEach(each -> {
                CITableDataInfo ciTableDataInfo = snapshotCIMap.get(each.getCiCode());
                if (ciTableDataInfo != null) {
                    each.setId(ciTableDataInfo.getId());
                }
            });
        } else {
            // 临时数据为空的场景 直接根据实时数据集创建
            log.info("临时数据为空 直接根据实时ci数据创建并绑定资产");
        }
        List<CITableSnapshot> copy = EamUtil.copy(realTimeCIInfo, CITableSnapshot.class);
        ciTableSnapshotDao.saveOrUpdateBatch(copy);
        return 1L;
    }

    @Override
    public List<CITableDataInfo> getCiTableInfoByAssetKey(String assetKey, Integer assetType, List<String> ciCodes) {
        DataSetEnum dataSetEnum = planDesignInstanceService.getDataSetFromByPlanId(Long.valueOf(assetKey));
        switch (dataSetEnum) {
            case SNAPSHOT_DATA:
                return getSnapshotCITableInfo(assetKey, assetType, ciCodes);
            case REALTIME_DATA:
                return getRealTimeDataSetInfo(assetKey, assetType, ciCodes);
            default:
                throw new BinaryException("不支持的数据集来源" + JSONObject.toJSONString(dataSetEnum));
        }
    }

    @Override
    public Long deleteCiTableSnapshotByAssetKey(String assetKey) {
        // 删除关联资产相关的临时数据
        if (BinaryUtils.isEmpty(assetKey)) {
            return 0L;
        }
        BoolQueryBuilder bool = QueryBuilders.boolQuery();
        bool.must(QueryBuilders.termQuery("assetKey.keyword", assetKey));
        ciTableSnapshotDao.deleteByQuery(bool, Boolean.TRUE);
        return 1L;
    }

    private List<CITableDataInfo> getRealTimeDataSetInfo(String assetKey, Integer assetType, List<String> ciCodes) {
        List<CITableDataInfo> ciTableDataInfos = new ArrayList<>();
        // 设计库查询实时数据
        if (CollectionUtils.isEmpty(ciCodes)) {
            return ciTableDataInfos;
        }
        Long domainId = SysUtil.getCurrentUserInfo().getDomainId();
        CCcCi cdt = new CCcCi();
        cdt.setCiCodes(ciCodes.toArray(new String[0]));
        List<CcCiInfo> ccCiInfos = iciSwitchSvc.queryCiInfoList(domainId, cdt, "", Boolean.FALSE, Boolean.TRUE, LibType.DESIGN);

        for (CcCiInfo ci : ccCiInfos) {
            CITableDataInfo ciTableDataInfo = new CITableDataInfo();
            ciTableDataInfo.setAssetKey(assetKey);
            ciTableDataInfo.setAssetType(assetType);
            ciTableDataInfo.setFrom(DataSetEnum.REALTIME_DATA);
            ciTableDataInfo.setCiInfo(ci);
            ciTableDataInfo.setCiCode(ci.getCi().getCiCode());

            ciTableDataInfos.add(ciTableDataInfo);
        }
        return ciTableDataInfos;
    }

    /**
     *  根据资产主键和ciCodes获取临时数据
     * @param assetKey
     * @param ciCodes
     * @return
     */
    private List<CITableDataInfo> getSnapshotCITableInfo(String assetKey, Integer assetType, List<String> ciCodes) {
        BoolQueryBuilder bool = QueryBuilders.boolQuery();
        bool.must(QueryBuilders.termQuery("assetKey.keyword", assetKey));
        bool.must(QueryBuilders.termsQuery("ciCode.keyword", ciCodes));
        List<CITableSnapshot> ciTableRltList = ciTableSnapshotDao.getListByQuery(bool);

        if (BinaryUtils.isEmpty(ciTableRltList)) {
            log.error("资产【{}】关联的临时数据集【{}】不存在，补充查询实时数据集", assetKey, JSONObject.toJSONString(ciCodes));
            List<CITableDataInfo> realTimeCITableInfo = this.getRealTimeDataSetInfo(assetKey, assetType, ciCodes);
            realTimeCITableInfo.forEach(info -> info.setFrom(DataSetEnum.WITHOUT_SNAPSHOT_QUERY_REALTIME));
            return realTimeCITableInfo;
        }

        List<CITableDataInfo> citableDataInfo = EamUtil.copy(ciTableRltList, CITableDataInfo.class);
        citableDataInfo.forEach(info -> info.setFrom(DataSetEnum.SNAPSHOT_DATA));
        return citableDataInfo;
    }

}
