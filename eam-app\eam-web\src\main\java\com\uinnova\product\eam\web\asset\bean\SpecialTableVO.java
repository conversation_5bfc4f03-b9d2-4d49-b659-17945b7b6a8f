package com.uinnova.product.eam.web.asset.bean;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class SpecialTableVO {

    private static final long serialVersionUID = 1L;

    @Comment("CI分类集合")
    private VcCiClass ciClass;

//    @Comment("标题")
//    private List<String> ciAttrHeads;

    @Comment("属性定义")
    private List<CcCiAttrDef> attrDefs;

    @Comment("CI属性集合")
    private List<Map<String,Object>> attrValues;

    @Comment("总记录数")
    private long totalRows;

    @Data
    public static class VcCiClass{
        @Comment("ci分类id")
        private Long classId;

        @Comment("ci分类名称")
        private String className;

        @Comment("ci分类名称")
        private String classCode;
    }

}
