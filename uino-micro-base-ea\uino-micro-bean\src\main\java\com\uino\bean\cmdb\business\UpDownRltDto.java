package com.uino.bean.cmdb.business;

import java.util.List;
import java.util.Set;

import com.binary.framework.bean.annotation.Comment;

/**
 * <AUTHOR>
 * @data 2019/9/10 11:29.
 */
public class UpDownRltDto {
    @Comment("领域")
    Long domainId;
    @Comment("入口ciIds")
    List<Long> sCiIds;
    @Comment("关系分类")
    List<Long> ciRltClsidList;
    @Comment("分类")
    List<Long> ciClsIdList;
    @Comment("向上多少层")
    Integer upLevel;
    @Comment("向下多少层")
    Integer downLevel;
    @Comment("关系等级")
    Set<Integer> rltLvls;

    public Long getDomainId() {
        return domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }

    public List<Long> getsCiIds() {
        return sCiIds;
    }

    public void setsCiIds(List<Long> sCiIds) {
        this.sCiIds = sCiIds;
    }

    public List<Long> getCiRltClsidList() {
        return ciRltClsidList;
    }

    public void setCiRltClsidList(List<Long> ciRltClsidList) {
        this.ciRltClsidList = ciRltClsidList;
    }

    public List<Long> getCiClsIdList() {
        return ciClsIdList;
    }

    public void setCiClsIdList(List<Long> ciClsIdList) {
        this.ciClsIdList = ciClsIdList;
    }

    public Integer getUpLevel() {
        return upLevel;
    }

    public void setUpLevel(Integer upLevel) {
        this.upLevel = upLevel;
    }

    public Integer getDownLevel() {
        return downLevel;
    }

    public void setDownLevel(Integer downLevel) {
        this.downLevel = downLevel;
    }

    public Set<Integer> getRltLvls() {
        return rltLvls;
    }

    public void setRltLvls(Set<Integer> rltLvls) {
        this.rltLvls = rltLvls;
    }
}
