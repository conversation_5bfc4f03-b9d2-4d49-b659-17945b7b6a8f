package com.uino.bean.cmdb.business;

import java.io.Serializable;
import java.util.List;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.vmdb.comm.model.ci.CcCiClass;
import com.uino.bean.cmdb.base.ESCIAttrDefInfo;

import com.uino.bean.cmdb.base.ESCiRltAutoBuild;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * CI分类传输对象
 * 
 * <AUTHOR>
 *
 */
@ApiModel(value = "CI分类传输对象", description = "CI分类传输对象")
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CIClassInfoDto implements Serializable {

    /**
     * 
     */
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value="CI分类",example="fruit")
    @Comment("CI分类")
    private CcCiClass ciClass;

    @ApiModelProperty(value="CI属性",example="color")
    @Comment("CI属性")
    List<ESCIAttrDefInfo> attrDefs;


}
