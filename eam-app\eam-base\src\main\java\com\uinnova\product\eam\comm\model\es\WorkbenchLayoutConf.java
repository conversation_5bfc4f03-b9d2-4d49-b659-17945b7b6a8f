package com.uinnova.product.eam.comm.model.es;

import com.alibaba.fastjson.JSONObject;
import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.util.List;

@Data
public class WorkbenchLayoutConf {
    private Long id;

    private List<JSONObject> layoutConf;
    @Comment("领域id")
    private Long domainId;
    @Comment("创建人")
    private String creator;
    @Comment("创建时间")
    private Long createTime;
    @Comment("修改时间")
    private Long modifyTime;

}
