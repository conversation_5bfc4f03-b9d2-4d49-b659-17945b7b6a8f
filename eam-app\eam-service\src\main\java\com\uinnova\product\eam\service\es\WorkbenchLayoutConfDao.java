package com.uinnova.product.eam.service.es;

import com.uinnova.product.eam.comm.model.es.WorkbenchLayoutConf;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Repository;

import jakarta.annotation.PostConstruct;

@Repository
public class WorkbenchLayoutConfDao extends AbstractESBaseDao<WorkbenchLayoutConf, WorkbenchLayoutConf> {
    @Override
    public String getIndex() {
        return "uino_eam_workbench_layout_conf";
    }

    @Override
    public String getType() {
        return "_doc";
    }
    @PostConstruct
    public void init() {
        super.initIndex();
    }
}
