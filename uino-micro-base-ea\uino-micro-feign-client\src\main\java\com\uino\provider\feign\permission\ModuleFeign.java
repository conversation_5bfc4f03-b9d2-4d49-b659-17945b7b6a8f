package com.uino.provider.feign.permission;

import java.util.List;
import java.util.Map;
import java.util.Set;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.uino.bean.permission.base.SysModule;
import com.uino.bean.permission.business.ModuleNodeInfo;
import com.uino.bean.permission.query.CAuthModuleBean;
import com.uino.bean.permission.query.CSysModule;
import com.uino.provider.feign.config.BaseFeignConfig;

@Component
@FeignClient(name = "${feign-server-name:tarsier-platform}", path = "${feign-server-path:tarsier-platform}/feign/module", configuration = {
        BaseFeignConfig.class})
public interface ModuleFeign {

    /**
     * 获取菜单tree结构
     *
     * @param userId
     *            为空则获取所有tree；不为空则获取该用户有权限的菜单tree
     * @return
     */
    @PostMapping("getModuleTree")
    ModuleNodeInfo getModuleTree(@RequestParam(value="domainId") Long domainId,@RequestParam(value = "userId", required = false) Long userId);

    /**
     * 保存模块
     * 
     * @param saveDto
     * @return
     */
    @PostMapping("saveModule")
    SysModule saveModule(@RequestBody(required = false) SysModule saveDto);

    /**
     * 删除模块
     * 
     * @param id
     */
    @PostMapping("delModule")
    void delModule(@RequestParam(required = false, value = "id") Long id);

    /**
     * 保存排序
     * 
     * @param orderDict
     */
    @PostMapping("saveOrder")
    void saveOrder(@RequestBody(required = false) Map<Long, Integer> orderDict);

    /**
     * 恢复模块信息
     * 
     * @param moduleIds
     *            要还原模块的ids(只能是初始化类型的) if empty 则恢复所有初始化类型模块
     * @return {被影响的模块id:模块信息}
     */
    @PostMapping("recoverModules")
    Map<Long, SysModule> recoverModules(@RequestBody(required = false) Set<Long> moduleIds);

    /**
     * 查询模块数据
     * 
     * @param cdt
     * @return
     */
    @PostMapping("getModulesByCdt")
    List<SysModule> getModulesByCdt(@RequestBody(required = false) CSysModule cdt);

    /**
     * 获取用户或角色模块权限<br>
     * 根据userId查询，得到用户本身及其所属角色菜单权限的并集
     * 
     * @param bean
     * @return
     */
    @PostMapping("getAuthModulesBySearchBean")
    public List<SysModule> getAuthModulesBySearchBean(@RequestBody CAuthModuleBean bean);
}
