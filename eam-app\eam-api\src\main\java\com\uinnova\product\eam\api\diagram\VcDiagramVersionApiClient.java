package com.uinnova.product.eam.api.diagram;

import com.uinnova.product.eam.base.diagram.mix.enums.DiagramQ;
import com.uinnova.product.eam.base.diagram.mix.model.VcDiagramVersionInfo;
import com.uinnova.product.eam.base.diagram.model.CVcDiagramVersion;
import com.uinnova.product.eam.base.diagram.model.VcDiagramVersion;
import com.uinnova.product.eam.model.diagram.VcDiagramInfo;

import java.util.List;

public interface VcDiagramVersionApiClient {
    VcDiagramVersionInfo queryDiagramVersionInfoById(Long domainId, Long id, DiagramQ[] diagramQs);

    List<VcDiagramVersion> queryVcDiagramVersionList(Long domainId, CVcDiagramVersion cdt, String orders);

    Long saveDiagramVersionByDiagramInfo(Long domainId, Long diagramId, VcDiagramInfo record);

    Integer removeDiagramVersionById(Long domainId, Long id);

    void saveOrUpdateDiagramVersion(Long domainId, VcDiagramVersion version);

    void updateDiagramVersionDescAndVersionNo(Long domainId, Long id, String versionDesc, String versionNo);
}
