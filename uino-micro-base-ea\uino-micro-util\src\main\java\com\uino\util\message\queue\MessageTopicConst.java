package com.uino.util.message.queue;

/**
 * Message service topic constant class
 *
 * @Author: YGQ
 * @Create: 2021-05-24 15:15
 * @see <a href="http://192.168.21.86:8080/common/messageQueue/">http://192.168.21.86:8080/common/messageQueue/</a>
 **/
public class MessageTopicConst {

    /**
     * This topic is used by tp to receive performance data.
     */
    public static final String MIDDLE_TP_PERF = "middle-tp-perf";

    /**
     * This topic is used by ep to receive event forwarding messages.
     */
    public static final String TRANSFER_QUEUE_NAME = "transferQueue";

    /**
     * This topic is used by ep to receive and refresh cached messages.
     */
    public static final String REFRESH_TOPIC = "refreshTopic";

    /**
     * The topic center station sends out messages to notify the use.
     */
    public static final String MIDDLE_NOTIFY = "middle-notify";

    /**
     * This topic is used by ep to receive alarm event messages.
     */
    public static final String EVENT_QUEUE = "eventQueue";

    /**
     * This topic is used by message push
     */
    public static final String PUSHER_TOPIC = "push-msg-topic";

    /**
     * external notification service queue
     */
    public static final String OUTER_SERVICE = "serviceQueue";

    /**
     * This topic is used by linkage control service conditions send to tp
     */
    public static final String SERVICE_CONTROL_RULE_TOPIC = "serviceControlRule";


    /**
     * This topic is used by linkage control timer conditions send to op
     */
    public static final String TIMER_CONTROL_RULE_TOPIC = "timerControlRule";

    /**
     * This topic is used by sending a linkage control service id to issued instructions
     */
    public static final String LINKAGE_CONTROL_TOPIC = "linkageControl";

    /**
     * This topic is used for ap to receive work notice.
     * <p>
     * Note: ap only uses this topic to receive messages.
     */
    public static final String AP_HANDLE_TOPIC = "ap-handle";


    /**
     * This topic is used for ap to receive external messages.
     * <p>
     * Note: ap only uses this topic to receive messages.
     */
    public static final String AP_RULE_TOPIC = "ap-rule";
}
