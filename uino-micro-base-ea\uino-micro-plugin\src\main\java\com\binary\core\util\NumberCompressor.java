package com.binary.core.util;

import com.binary.core.exception.CoreException;



/**
 * 数字数据压缩器 
 * <AUTHOR>
 */
public abstract class NumberCompressor {

	
	
	private static final char[] CS = new char[]{
		'0', '1', '2', '3', '4', '5', '6', '7', '8', '9',
		'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J',
		'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T',
		'U', 'V', 'W', 'X', 'Y', 'Z', 58, 59, 60, 61,
		'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j',
		'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't',
		'u', 'v', 'w', 'x', 'y', 'z', 62, 63, 64, 9,
		32, 33, 34, 35, 36, 37, 38, 39, 40, 41,
		42, 43, 44, 45, 46, 47, 91, 92, 93, 94,
		95, 96, 123, 124, 125, 126, 28, 29, 30, 31
	};
	
	
	
	
	private static int indexOf(char c) {
		for(int i=0; i<CS.length; i++) {
			if(c == CS[i]) return i;
		}
		return -1;
	}
	
	
	
	
	
	/**
	 * 将一个long类型的数据按100进制压缩(不支持负数)
	 * 运行1千万次耗时1809ms
	 * @param num
	 * @return
	 */
	public static String compress(long num) {
		if(num < 0) throw new CoreException(" not support negative number! ");
		int len = Long.toString(num).length();
		char[] arr = new char[len!=0&&len%2==0 ? len/2 : (len/2+1)];
		for(int i=arr.length-1; i>=0; i--) {
			arr[i] = CS[(int)(num%100)];
			num /= 100;
		}
		return new String(arr);
	}
	
	
	
	
	/**
	 * 将一个被压缩的数据转换为数字
	 * 运行1千万次耗时2882ms
	 * @param s
	 * @return
	 */
	public static long decompress(String s) {
		char[] arr = s.toCharArray();
		long num = 0;
		for(int i=0; i<arr.length; i++) {
			if(i > 0) {
				num *= 100;
			}
			int idx = indexOf(arr[i]);
			if(idx < 0) throw new CoreException(" is wrong compressed string '"+s+"'! ");
			num += idx;
		}
		return num;
	}
	
	
	
	
	
	/**
	 * 将一个long类型的数据按128进制压缩(不支持负数)
	 * 运行1千万次耗时608ms
	 * @param num
	 * @return
	 */
	public static String compress128(long num) {
		if(num < 0) throw new CoreException(" not support negative number! ");
		if(num == 0) return new String(new char[]{0});
		char[] arr = new char[10];
		int count = 0;
		while(num > 0) {
			arr[arr.length-1-count] = (char)(num % 128);
			num >>= 7;
			count ++ ;
		}
		return new String(arr, arr.length-count, count);
	}
	
	
	
	/**
	 * 将一个被128进制压缩的数据转换为数字
	 * 运行1千万次耗时243ms
	 * @param s
	 * @return
	 */
	public static long decompress128(String s) {
		char[] arr = s.toCharArray();
		long num = 0;
		for(int i=0; i<arr.length; i++) {
			if(i > 0) {
				num <<= 7;
			}
			if(arr[i]<0 || arr[i]>127) throw new CoreException(" is wrong compressed string '"+s+"'! ");
			num += arr[i];
		}
		return num;
	}
	
	
	

	
}
