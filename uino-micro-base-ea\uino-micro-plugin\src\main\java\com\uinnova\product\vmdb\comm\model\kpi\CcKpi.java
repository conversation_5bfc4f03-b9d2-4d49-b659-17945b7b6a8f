package com.uinnova.product.vmdb.comm.model.kpi;




import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;


@Comment("KPI表[CC_KPI]")
public class CcKpi implements EntityBean {
	private static final long serialVersionUID = 1L;


	@Comment("ID[ID]")
	private Long id;


	@Comment("KPI名称[KPI_CODE]")
	private String kpiCode;


	@Comment("KPI别名[KPI_NAME]")
	private String kpiName;


	@Comment("所属分类[CLASS_ID]")
	private Long classId;


	@Comment("数值类型[VAL_TYPE]    数值类型:1=数值 2=字符")
	private Integer valType;


	@Comment("单位ID[UNIT_ID]")
	private Long unitId;


	@Comment("单位名称[UNIT_NAME]")
	private String unitName;


	@Comment("数据阈值[VAL_EXP]")
	private String valExp;


	@Comment("来源[SOURCE_ID]")
	private Long sourceId;


	@Comment("所有者[OWNER_ID]")
	private String ownerCode;


	@Comment("KPI描述[KPI_DESC]")
	private String kpiDesc;


	@Comment("搜索字段[SEARCH_VALUE]")
	private String searchValue;


	@Comment("备用_1[CUSTOM_1]")
	private Long custom1;


	@Comment("备用_2[CUSTOM_2]")
	private Long custom2;


	@Comment("备用_3[CUSTOM_3]")
	private Long custom3;


	@Comment("备用_4[CUSTOM_4]")
	private String custom4;


	@Comment("备用_5[CUSTOM_5]")
	private String custom5;


	@Comment("备用_6[CUSTOM_6]")
	private String custom6;


	@Comment("KPI扩展属性[KPI_EXT_PROS]    KPI扩展属性 [{val:'',img:''}]")
	private String kpiExtPros;


	@Comment("所属域[DOMAIN_ID]")
	private Long domainId;


	@Comment("数据状态[DATA_STATUS]    数据状态:1-正常 0-删除")
	private Integer dataStatus;


	@Comment("创建人[CREATOR]")
	private String creator;


	@Comment("修改人[MODIFIER]")
	private String modifier;


	@Comment("创建时间[CREATE_TIME]    创建时间:yyyyMMddHHmmss")
	private Long createTime;


	@Comment("修改时间[MODIFY_TIME]    修改时间:yyyyMMddHHmmss")
	private Long modifyTime;


	@Comment("指标单位转换模板ID[UINIT_CONVERT_MODEL_ID]")
	private Long uinitConvertModelId;


	@Comment("监控系统[MONITOR_SYSTEM]")
	private String monitorSystem;


	@Comment("指标类型[KPI_TYPE]")
	private String kpiType;


	@Comment("指标大类[KPI_CATEGORY]")
	private String kpiCategory;


	@Comment("采集周期[COLLECTION_CYCLE]")
	private String collectionCycle;




	public Long getId() {
		return this.id;
	}
	public void setId(Long id) {
		this.id = id;
	}


	public String getKpiCode() {
		return this.kpiCode;
	}
	public void setKpiCode(String kpiCode) {
		this.kpiCode = kpiCode;
	}


	public String getKpiName() {
		return this.kpiName;
	}
	public void setKpiName(String kpiName) {
		this.kpiName = kpiName;
	}


	public Long getClassId() {
		return this.classId;
	}
	public void setClassId(Long classId) {
		this.classId = classId;
	}


	public Integer getValType() {
		return this.valType;
	}
	public void setValType(Integer valType) {
		this.valType = valType;
	}


	public Long getUnitId() {
		return this.unitId;
	}
	public void setUnitId(Long unitId) {
		this.unitId = unitId;
	}


	public String getUnitName() {
		return this.unitName;
	}
	public void setUnitName(String unitName) {
		this.unitName = unitName;
	}


	public String getValExp() {
		return this.valExp;
	}
	public void setValExp(String valExp) {
		this.valExp = valExp;
	}


	public Long getSourceId() {
		return this.sourceId;
	}
	public void setSourceId(Long sourceId) {
		this.sourceId = sourceId;
	}


	public String getOwnerCode() {
		return this.ownerCode;
	}
	public void setOwnerCode(String ownerCode) {
		this.ownerCode = ownerCode;
	}


	public String getKpiDesc() {
		return this.kpiDesc;
	}
	public void setKpiDesc(String kpiDesc) {
		this.kpiDesc = kpiDesc;
	}


	public String getSearchValue() {
		return this.searchValue;
	}
	public void setSearchValue(String searchValue) {
		this.searchValue = searchValue;
	}


	public Long getCustom1() {
		return this.custom1;
	}
	public void setCustom1(Long custom1) {
		this.custom1 = custom1;
	}


	public Long getCustom2() {
		return this.custom2;
	}
	public void setCustom2(Long custom2) {
		this.custom2 = custom2;
	}


	public Long getCustom3() {
		return this.custom3;
	}
	public void setCustom3(Long custom3) {
		this.custom3 = custom3;
	}


	public String getCustom4() {
		return this.custom4;
	}
	public void setCustom4(String custom4) {
		this.custom4 = custom4;
	}


	public String getCustom5() {
		return this.custom5;
	}
	public void setCustom5(String custom5) {
		this.custom5 = custom5;
	}


	public String getCustom6() {
		return this.custom6;
	}
	public void setCustom6(String custom6) {
		this.custom6 = custom6;
	}


	public String getKpiExtPros() {
		return this.kpiExtPros;
	}
	public void setKpiExtPros(String kpiExtPros) {
		this.kpiExtPros = kpiExtPros;
	}


	public Long getDomainId() {
		return this.domainId;
	}
	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}


	public Integer getDataStatus() {
		return this.dataStatus;
	}
	public void setDataStatus(Integer dataStatus) {
		this.dataStatus = dataStatus;
	}


	public String getCreator() {
		return this.creator;
	}
	public void setCreator(String creator) {
		this.creator = creator;
	}


	public String getModifier() {
		return this.modifier;
	}
	public void setModifier(String modifier) {
		this.modifier = modifier;
	}


	public Long getCreateTime() {
		return this.createTime;
	}
	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}


	public Long getModifyTime() {
		return this.modifyTime;
	}
	public void setModifyTime(Long modifyTime) {
		this.modifyTime = modifyTime;
	}


	public Long getUinitConvertModelId() {
		return this.uinitConvertModelId;
	}
	public void setUinitConvertModelId(Long uinitConvertModelId) {
		this.uinitConvertModelId = uinitConvertModelId;
	}


	public String getMonitorSystem() {
		return this.monitorSystem;
	}
	public void setMonitorSystem(String monitorSystem) {
		this.monitorSystem = monitorSystem;
	}


	public String getKpiType() {
		return this.kpiType;
	}
	public void setKpiType(String kpiType) {
		this.kpiType = kpiType;
	}


	public String getKpiCategory() {
		return this.kpiCategory;
	}
	public void setKpiCategory(String kpiCategory) {
		this.kpiCategory = kpiCategory;
	}


	public String getCollectionCycle() {
		return this.collectionCycle;
	}
	public void setCollectionCycle(String collectionCycle) {
		this.collectionCycle = collectionCycle;
	}


}


