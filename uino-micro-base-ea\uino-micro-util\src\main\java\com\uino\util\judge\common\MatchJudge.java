package com.uino.util.judge.common;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.uino.util.judge.BaseJudgeProcess;

import lombok.Setter;

/**
 * 正则验证
 * 
 * <AUTHOR>
 *
 */
public class MatchJudge extends BaseJudgeProcess {
	/**
	 * 正则表达式
	 */
	@Setter
	private String regex;
	/**
	 * 匹配上是否通过 默认false匹配上不通过
	 */
	@Setter
	private boolean equalTrue = false;

	@Override
	protected boolean validCore(Object validObj) {
		String validVal = (String) validObj;
		Matcher matcher = Pattern.compile(regex).matcher(validVal);
		boolean result = matcher != null && matcher.find();
		// boolean result = validVal.matches(regex);
		return equalTrue ? result : !result;
	}

}
