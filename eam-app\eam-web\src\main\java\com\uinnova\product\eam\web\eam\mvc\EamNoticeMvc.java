package com.uinnova.product.eam.web.eam.mvc;

import com.binary.framework.web.RemoteResult;
import com.binary.jdbc.Page;
import com.uinnova.product.eam.comm.model.es.EamNotice;
import com.uinnova.product.eam.model.EamNoticeTransVo;
import com.uinnova.product.eam.model.EamNoticeVo;
import com.uinnova.product.eam.model.asset.ShareMsgDTO;
import com.uinnova.product.eam.model.vo.*;
import com.uinnova.product.eam.service.IEamNoticeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 工作台-消息&公告
 */
@RestController
@RequestMapping("/eam/notice")
public class EamNoticeMvc {

    @Autowired
    private IEamNoticeService eamNoticeService;


    /**
     * 审核流程结束时，消息保存
     * @param req
     */
    @PostMapping("/workflow/msg/save")
    public RemoteResult workflowMsgSave(@RequestBody WorkflowMsgSaveVo req) {
        eamNoticeService.workflowMsgSave(req.getResult(), req.getSourceType(), req.getSourceId(), req.getStartUserId());
        return new RemoteResult("");
    }

    /**
     * 更新消息/公告已读
     * @param noticeType
     */
    @GetMapping("/update/read/{noticeType}")
    public RemoteResult updateRead(@PathVariable String noticeType) {
        eamNoticeService.updateRead(noticeType);
        return new RemoteResult("");
    }

    /**
     * 消息/公告统计数
     * @return
     */
    @GetMapping("/num")
    public RemoteResult noticeNum() {
        NoticeVo noticeVo = eamNoticeService.noticeNum();
        return new RemoteResult(noticeVo);
    }

    /**
     * 消息/公告列表
     * @param req
     * @return
     */
    @PostMapping("/list")
    public RemoteResult list(@RequestBody NoticeListReq req) {
        Page<EamNoticeVo> page = eamNoticeService.list(req.getNoticeType(), req.getPageSize(), req.getPageNum());
        return new RemoteResult(page);
    }

    /**
     * 获取真实消息数据
     * @param noticeTransVo
     * @return
     */
    @PostMapping("/transform")
    public RemoteResult transform(@RequestBody EamNoticeTransVo noticeTransVo) {
        return eamNoticeService.transform(noticeTransVo);
    }

    /**
     * 与我协作消息推送
     * @param shareMsgSaveVos
     */
    @PostMapping("/share/msg/save")
    public RemoteResult shareMsgSave(@RequestBody List<ShareMsgDTO> shareMsgSaveVos) {
        eamNoticeService.shareMsgSave(shareMsgSaveVos);
        return new RemoteResult("");
    }

    /**
     * 交付物模板变更消息保存
     * @param saveVo
     * @return
     */
    @PostMapping("/deliverable/template/msg/save")
    public RemoteResult deliverableTemplataMsgSave(@RequestBody DeliTemplateMsgSaveVo saveVo) {
        eamNoticeService.deliverableTemplataMsgSave(saveVo);
        return new RemoteResult("");
    }

    /**
     * 交付物模板变更消息同步协作者
     * @param saveVo
     * @return
     */
    @PostMapping("/deliverable/template/msg/sync/share")
    public RemoteResult deliverableTemplataMsgSyncShare(@RequestBody DeliTemplateMsgSaveVo saveVo) {
        eamNoticeService.deliverableTemplataMsgSyncShare(saveVo);
        return new RemoteResult("");
    }
}
