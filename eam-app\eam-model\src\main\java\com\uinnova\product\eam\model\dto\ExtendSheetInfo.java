package com.uinnova.product.eam.model.dto;

import com.uinnova.product.eam.base.diagram.model.ESDiagramLink;
import com.uinnova.product.eam.base.diagram.model.ESDiagramNode;
import com.uinnova.product.eam.base.diagram.model.ESDiagramSheetDTO;
import lombok.Data;

import java.util.List;

/**
 *  视图sheet相关参数
 */
@Data
public class ExtendSheetInfo extends ESDiagramSheetDTO {
    private List<ESDiagramNode> nodeDataArray;
    private List<ESDiagramLink> linkDataArray;

}

