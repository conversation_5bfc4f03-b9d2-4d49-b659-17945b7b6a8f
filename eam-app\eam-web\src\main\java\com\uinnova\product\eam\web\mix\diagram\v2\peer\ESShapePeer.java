package com.uinnova.product.eam.web.mix.diagram.v2.peer;


import com.binary.framework.web.RemoteResult;
import com.binary.jdbc.Page;
import com.uinnova.product.eam.api.diagram.ESShapeApiClient;
import com.uinnova.product.eam.base.diagram.model.*;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClassDir;
import com.uinnova.product.vmdb.comm.model.image.CcImage;
import com.uino.bean.cmdb.business.ImageCount;
import com.uino.bean.cmdb.business.ImportDirMessage;
import com.uino.bean.cmdb.business.ImportResultMessage;
import com.uino.bean.cmdb.query.ESSearchImageBean;
import com.uino.bean.permission.query.SearchKeywordBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Set;


@Service
public class ESShapePeer {

    @Value("${http.resource.space}")
    private String httpResouceUrl;

    @Autowired
    private ESShapeApiClient svc;

    public Long saveOrUpdateDir(VcShapeDir dir) {
        return this.svc.saveOrUpdateDir(dir);
    }

    public List<VcShapeDirInfo> queryImageDirList() {
        return this.svc.queryImageDirList();
    }

    public List<ESUserShape> queryImagePage(Integer pageNum, Integer pageSize, Long dirId, String keyword) {
        return this.svc.queryImagePage(pageNum, pageSize, dirId, keyword);
    }

    public RemoteResult saveOrUpdateShape(ESUserShapeDTO esUserShapeDTO) {
        return this.svc.saveOrUpdateShape(esUserShapeDTO);
    }

    public ImportResultMessage importZipImage(Integer sourceType, MultipartFile file) {
        return this.svc.importZipImage(sourceType, file);
    }

    public List<ImageCount> queryImageDirList(CCcCiClassDir cdt) {
        return this.svc.queryImageDirList(cdt);
    }

    public Page<ESUserShape> queryImagePage(ESImageBean bean) {
        return this.svc.queryImagePage(bean);
    }

    public boolean replaceImage(Long imageId, MultipartFile file) {
        return this.svc.replaceImage(imageId, file);
    }

    public boolean deleteImage(CcImage image) {
        return this.svc.deleteImage(image);
    }

    public long deleteDirImage(Long dirId) {
        return this.svc.deleteDirImage(dirId);
    }

    public boolean importImage(Long dirId, MultipartFile file) {
        return this.svc.importImage(dirId, file);
    }

    public ResponseEntity<byte[]> exportImageZipByDirIds(Set<Long> dirIds) {
        return this.svc.exportImageZipByDirIds(dirIds);
    }

    public List<CcImage> queryTopImage(SearchKeywordBean bean) {
        return this.svc.queryTopImage(bean);
    }

    public List<ESUserShapeResult> queryAllImage(ESImageBean bean) {
        return this.svc.queryAllImage(bean);
    }

    public ImportDirMessage import3DZipImage(MultipartFile file, Long dirId, boolean isCover) {
//        return this.svc.import3DZipImage(file, dirId, (Map)null, isCover);
        return null;
    }

    public Long updateImageRlt(ESUserShape image) {
        return this.svc.updateImageRlt(image);
    }

    public boolean replace3DImage(Long imgId, MultipartFile file) {
        return this.svc.replace3DImage(imgId, file);
    }

    public boolean delete3DImage(CcImage image) {
        return this.svc.delete3DImage(image);
    }

    public CcImage queryImageById(Long id) {
        return this.svc.queryImageById(id);
    }

    public ImportDirMessage importImages(Long dirId, MultipartFile... files) {
        return this.svc.importImages(dirId, files);
    }

    public Long countBySearchBean(ESSearchImageBean bean) {
        return this.svc.countBySearchBean(bean);
    }

    public ResponseEntity<byte[]> downloadImageResource(List<Long> ids) {
        return this.svc.downloadImageResource(ids);
    }

    public Long saveOrUpdate(ESShapeDir dir) {
        return this.svc.saveOrUpdate(dir);
    }

    public boolean sort(ESUserShapeQuery cdt) {
        return this.svc.sort(cdt);
    }
}
