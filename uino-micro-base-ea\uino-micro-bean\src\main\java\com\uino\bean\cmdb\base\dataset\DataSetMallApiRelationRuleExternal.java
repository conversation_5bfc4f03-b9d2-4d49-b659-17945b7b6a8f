package com.uino.bean.cmdb.base.dataset;

import java.util.List;

import com.uino.bean.cmdb.base.dataset.relation.RelationRuleLineExternal;
import com.uino.bean.cmdb.base.dataset.relation.RelationRuleNodeExternal;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value="")
public class DataSetMallApiRelationRuleExternal {

	@ApiModelProperty(value="")
    private Long pageNodeId;

	@ApiModelProperty(value="")
    private List<RelationRuleNodeExternal> nodes;

	@ApiModelProperty(value="")
    private List<RelationRuleLineExternal> lines;
    
	public Long getPageNodeId() {
		return pageNodeId;
	}
	public void setPageNodeId(Long pageNodeId) {
		this.pageNodeId = pageNodeId;
	}
	public List<RelationRuleNodeExternal> getNodes() {
		return nodes;
	}
	public void setNodes(List<RelationRuleNodeExternal> nodes) {
		this.nodes = nodes;
	}
	public List<RelationRuleLineExternal> getLines() {
		return lines;
	}
	public void setLines(List<RelationRuleLineExternal> lines) {
		this.lines = lines;
	}
}
