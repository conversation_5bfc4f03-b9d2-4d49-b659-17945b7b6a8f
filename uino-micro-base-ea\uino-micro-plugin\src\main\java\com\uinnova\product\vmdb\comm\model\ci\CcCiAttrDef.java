package com.uinnova.product.vmdb.comm.model.ci;

import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.vmdb.comm.util.CommUtil;

@Comment("CI属性定义表[CC_CI_ATTR_DEF]")
public class CcCiAttrDef implements EntityBean {
    private static final long serialVersionUID = 1L;

    @Comment("ID[ID]")
    private Long id;

    @Comment("CI分类ID[CLASS_ID]")
    private Long classId;

    @Comment("分类类型[CI_TYPE]    分类类型:1=基础CI 2=关系CI")
    private Integer ciType;

    @Comment("属性名[PRO_NAME]")
    private String proName;

    @Comment("标准名[PRO_STD_NAME]    标准名:全部大写")
    private String proStdName;

    @Comment("属性类型[PRO_TYPE]    属性类型:1=整数 2=小数 3=短文本(<=200) 4=长文本(<=1000) 5=文章 6=枚举 7=日期 8=字典 9=3D模型 10=图片 11=关联属性 12=文档 150=编码类型")
    private Integer proType;

    @Comment("字典类型来源类型[PRO_DROP_SOURCE_TYPE]    字典类型来源类型: 1=标签 2=CI分类")
    private Integer proDropSourceType;

    @Comment("字典类型来源ID[PRO_DROP_SOURCE_ID]")
    private Long proDropSourceId;

    @Comment("外部属性[PRO_DROP_SOURCE_DEF]")
    private String proDropSourceDef;

    @Comment("属性描述[PRO_DESC]")
    private String proDesc;

    @Comment("映射字段[MP_CI_FIELD]")
    private Integer mpCiField;

    @Comment("是否主键[IS_MAJOR]    是否主键:0=否，1=是")
    private Integer isMajor;

    @Comment("是否必填[IS_REQUIRED]    是否必填:0=否，1=是")
    private Integer isRequired;

    @Comment("是否作为CI显示[IS_CI_DISP]    是否作为CI显示:0=否，1=是")
    private Integer isCiDisp;

    @Comment("缺省值[DEF_VAL]")
    private String defVal;

    @Comment("枚举值[ENUM_VALUES]    枚举值:多个以逗号分隔")
    private String enumValues;

    @Comment("排列顺序[ORDER_NO]")
    private Integer orderNo;

    @Comment("关系标签位置[LINE_LABEL_ALIGN]    关系标签位置:1=偏向源 2=偏向目标 3=居中")
    private Integer lineLabelAlign;

    @Comment("是否是代码[IS_CODE]    是否是代码 1=是 0=否")
    private Integer isCode;

    @Comment("重要等级[IMPORTANCE_LEVEL]")
    private Integer importanceLevel;

    @Comment("是否审计[IS_AUDIT]    是否审计:1=是，0=否")
    private Integer isAudit;

    @Comment("约束规则[CONSTRAINT_RULE]")
    private String constraintRule;

    @Comment("源端或者目标端[SOURCE_OR_TARGET]")
    private String sourceOrTarget;

    @Comment("生成的关系id[CONSTRUCT_RLT_ID]")
    private Long constructRltId;

    @Comment("所属域[DOMAIN_ID]")
    private Long domainId;

    @Comment("数据状态[DATA_STATUS]    数据状态:0=删除，1=正常")
    private Integer dataStatus;

    @Comment("创建人[CREATOR]")
    private String creator;

    @Comment("修改人[MODIFIER]")
    private String modifier;

    @Comment("创建时间[CREATE_TIME]    创建时间:yyyyMMddHHmmss")
    private Long createTime;

    @Comment("更新时间[MODIFY_TIME]    更新时间:yyyyMMddHHmmss")
    private Long modifyTime;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getClassId() {
        return this.classId;
    }

    public void setClassId(Long classId) {
        this.classId = classId;
    }

    public Integer getCiType() {
        return this.ciType;
    }

    public void setCiType(Integer ciType) {
        this.ciType = ciType;
    }

    public String getProName() {
        return this.proName;
    }

    public void setProName(String proName) {
        this.proName = proName;
    }

    public String getProStdName() {
        return this.proStdName;
    }

    public void setProStdName(String proStdName) {
        this.proStdName = proStdName;
    }

    public Integer getProType() {
        return this.proType;
    }

    public void setProType(Integer proType) {
        this.proType = proType;
    }

    public Integer getProDropSourceType() {
        return this.proDropSourceType;
    }

    public void setProDropSourceType(Integer proDropSourceType) {
        this.proDropSourceType = proDropSourceType;
    }

    public Long getProDropSourceId() {
        return this.proDropSourceId;
    }

    public void setProDropSourceId(Long proDropSourceId) {
        this.proDropSourceId = proDropSourceId;
    }

    public String getProDropSourceDef() {
        return proDropSourceDef;
    }

    public void setProDropSourceDef(String proDropSourceDef) {
        this.proDropSourceDef = proDropSourceDef;
    }

    public String getProDesc() {
        return this.proDesc;
    }

    public void setProDesc(String proDesc) {
        this.proDesc = proDesc;
    }

    public Integer getMpCiField() {
        return this.mpCiField;
    }

    public void setMpCiField(Integer mpCiField) {
        this.mpCiField = mpCiField;
    }

    public Integer getIsMajor() {
        return this.isMajor;
    }

    public void setIsMajor(Integer isMajor) {
        this.isMajor = isMajor;
    }

    public Integer getIsRequired() {
        return this.isRequired;
    }

    public void setIsRequired(Integer isRequired) {
        this.isRequired = isRequired;
    }

    public Integer getIsCiDisp() {
        return this.isCiDisp;
    }

    public void setIsCiDisp(Integer isCiDisp) {
        this.isCiDisp = isCiDisp;
    }

    public String getDefVal() {
        return this.defVal;
    }

    public void setDefVal(String defVal) {
        this.defVal = defVal;
    }

    public String getEnumValues() {
        return this.enumValues;
    }

    public void setEnumValues(String enumValues) {
        this.enumValues = enumValues;
    }

    public Integer getOrderNo() {
        return this.orderNo;
    }

    public void setOrderNo(Integer orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getLineLabelAlign() {
        return this.lineLabelAlign;
    }

    public void setLineLabelAlign(Integer lineLabelAlign) {
        this.lineLabelAlign = lineLabelAlign;
    }

    public Integer getIsCode() {
        return this.isCode;
    }

    public void setIsCode(Integer isCode) {
        this.isCode = isCode;
    }

    public Integer getImportanceLevel() {
        return this.importanceLevel;
    }

    public void setImportanceLevel(Integer importanceLevel) {
        this.importanceLevel = importanceLevel;
    }

    public Integer getIsAudit() {
        return this.isAudit;
    }

    public void setIsAudit(Integer isAudit) {
        this.isAudit = isAudit;
    }

    public String getConstraintRule() {
        return this.constraintRule;
    }

    public void setConstraintRule(String constraintRule) {
        this.constraintRule = constraintRule;
    }

    public String getSourceOrTarget() {
        return sourceOrTarget;
    }

    public void setSourceOrTarget(String sourceOrTarget) {
        this.sourceOrTarget = sourceOrTarget;
    }

    public Long getConstructRltId() {
        return constructRltId;
    }

    public void setConstructRltId(Long constructRltId) {
        this.constructRltId = constructRltId;
    }

    public Long getDomainId() {
        return this.domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }

    public Integer getDataStatus() {
        return this.dataStatus;
    }

    public void setDataStatus(Integer dataStatus) {
        this.dataStatus = dataStatus;
    }

    public String getCreator() {
        return this.creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getModifier() {
        return this.modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public Long getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getModifyTime() {
        return this.modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }

    /**
     * 比较两个CI分类属性定义是否相同，用于修改分类时过滤未修改属性
     * @param attrDef
     * @return
     */
    public boolean equalsForCiClass(CcCiAttrDef attrDef) {
        if (this == attrDef) return true;
        if (attrDef == null) return false;
        return CommUtil.equalsForModel(id, attrDef.id) &&
                CommUtil.equalsForModel(classId, attrDef.classId) &&
                CommUtil.equalsForModel(ciType, attrDef.ciType) &&
                CommUtil.equalsForModel(proName, attrDef.proName) &&
                CommUtil.equalsForModel(proStdName, attrDef.proStdName) &&
                CommUtil.equalsForModel(proType, attrDef.proType) &&
                CommUtil.equalsForModel(proDropSourceType, attrDef.proDropSourceType) &&
                CommUtil.equalsForModel(proDropSourceId, attrDef.proDropSourceId) &&
                CommUtil.equalsForModel(proDropSourceDef, attrDef.proDropSourceDef) &&
                CommUtil.equalsForModel(proDesc, attrDef.proDesc) &&
                CommUtil.equalsForModel(mpCiField, attrDef.mpCiField) &&
                CommUtil.equalsForModel(isMajor, attrDef.isMajor) &&
                CommUtil.equalsForModel(isRequired, attrDef.isRequired) &&
                CommUtil.equalsForModel(isCiDisp, attrDef.isCiDisp) &&
                CommUtil.equalsForModel(defVal, attrDef.defVal) &&
                CommUtil.equalsForModel(enumValues, attrDef.enumValues) &&
                CommUtil.equalsForModel(orderNo, attrDef.orderNo) &&
                CommUtil.equalsForModel(lineLabelAlign, attrDef.lineLabelAlign) &&
                CommUtil.equalsForModel(isCode, attrDef.isCode) &&
                CommUtil.equalsForModel(importanceLevel, attrDef.importanceLevel) &&
                CommUtil.equalsForModel(isAudit, attrDef.isAudit) &&
                CommUtil.equalsForModel(constraintRule, attrDef.constraintRule) &&
                CommUtil.equalsForModel(sourceOrTarget, attrDef.sourceOrTarget) &&
                CommUtil.equalsForModel(constructRltId, attrDef.constructRltId) &&
                CommUtil.equalsForModel(domainId, attrDef.domainId) &&
                CommUtil.equalsForModel(creator, attrDef.creator) &&
                CommUtil.equalsForModel(modifier, attrDef.modifier);
    }
}
