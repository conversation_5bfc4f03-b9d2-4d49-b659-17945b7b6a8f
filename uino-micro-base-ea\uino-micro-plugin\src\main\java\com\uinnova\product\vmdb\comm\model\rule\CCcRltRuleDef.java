package com.uinnova.product.vmdb.comm.model.rule;

import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;

/**
 * 
 * <AUTHOR>
 *
 */
@Comment("关系规则定义表[CC_RLT_RULE_DEF]")
public class CCcRltRuleDef implements Condition {
    private static final long serialVersionUID = 1L;

    @Comment("ID[ID] operate-Equal[=]")
    private Long id;

    @Comment("ID[ID] operate-In[in]")
    private Long[] ids;

    @Comment("ID[ID] operate-GTEqual[>=]")
    private Long startId;

    @Comment("ID[ID] operate-LTEqual[<=]")
    private Long endId;

    @Comment("定义类型[DEF_TYPE] operate-Equal[=]    定义类型:1=朋友圈")
    private Integer defType;

    @Comment("定义类型[DEF_TYPE] operate-In[in]    定义类型:1=朋友圈")
    private Integer[] defTypes;

    @Comment("定义类型[DEF_TYPE] operate-GTEqual[>=]    定义类型:1=朋友圈")
    private Integer startDefType;

    @Comment("定义类型[DEF_TYPE] operate-LTEqual[<=]    定义类型:1=朋友圈")
    private Integer endDefType;

    @Comment("定义代码[DEF_CODE] operate-Like[like]")
    private String defCode;

    @Comment("定义代码[DEF_CODE] operate-Equal[=]")
    private String defCodeEqual;

    @Comment("定义代码[DEF_CODE] operate-In[in]")
    private String[] defCodes;

    @Comment("定义名称[DEF_NAME] operate-Like[like]")
    private String defName;

    @Comment("定义名称[DEF_NAME] operate-Equal[=]")
    private String defNameEqual;

    @Comment("定义名称[DEF_NAME] operate-In[in]")
    private String[] defNames;

    @Comment("参照分类[CLASS_ID] operate-Equal[=]")
    private Long classId;

    @Comment("参照分类[CLASS_ID] operate-In[in]")
    private Long[] classIds;

    @Comment("参照分类[CLASS_ID] operate-GTEqual[>=]")
    private Long startClassId;

    @Comment("参照分类[CLASS_ID] operate-LTEqual[<=]")
    private Long endClassId;

    @Comment("页面节点ID[PAGE_NODE_ID] operate-Equal[=]")
    private Long pageNodeId;

    @Comment("页面节点ID[PAGE_NODE_ID] operate-In[in]")
    private Long[] pageNodeIds;

    @Comment("页面节点ID[PAGE_NODE_ID] operate-GTEqual[>=]")
    private Long startPageNodeId;

    @Comment("页面节点ID[PAGE_NODE_ID] operate-LTEqual[<=]")
    private Long endPageNodeId;

    @Comment("定义描述[DEF_DESC] operate-Like[like]")
    private String defDesc;

    @Comment("SVG图路径[SVG_URL] operate-Like[like]")
    private String svgUrl;

    @Comment("有效状态[USE_STATUS] operate-Equal[=]    有效状态:0=无效 1=有效")
    private Integer useStatus;

    @Comment("有效状态[USE_STATUS] operate-In[in]    有效状态:0=无效 1=有效")
    private Integer[] useStatuss;

    @Comment("有效状态[USE_STATUS] operate-GTEqual[>=]    有效状态:0=无效 1=有效")
    private Integer startUseStatus;

    @Comment("有效状态[USE_STATUS] operate-LTEqual[<=]    有效状态:0=无效 1=有效")
    private Integer endUseStatus;

    @Comment("无效说明[VALID_ERR_MSG] operate-Like[like]")
    private String validErrMsg;

    @Comment("所属域[DOMAIN_ID] operate-Equal[=]")
    private Long domainId;

    @Comment("所属域[DOMAIN_ID] operate-In[in]")
    private Long[] domainIds;

    @Comment("所属域[DOMAIN_ID] operate-GTEqual[>=]")
    private Long startDomainId;

    @Comment("所属域[DOMAIN_ID] operate-LTEqual[<=]")
    private Long endDomainId;

    @Comment("创建人[CREATOR] operate-Like[like]")
    private String creator;

    @Comment("创建人[CREATOR] operate-Equal[=]")
    private String creatorEqual;

    @Comment("创建人[CREATOR] operate-In[in]")
    private String[] creators;

    @Comment("修改人[MODIFIER] operate-Like[like]")
    private String modifier;

    @Comment("修改人[MODIFIER] operate-Equal[=]")
    private String modifierEqual;

    @Comment("修改人[MODIFIER] operate-In[in]")
    private String[] modifiers;

    @Comment("数据状态[DATA_STATUS] operate-Equal[=]    数据状态:数据状态：1-正常 0-删除")
    private Integer dataStatus;

    @Comment("数据状态[DATA_STATUS] operate-In[in]    数据状态:数据状态：1-正常 0-删除")
    private Integer[] dataStatuss;

    @Comment("数据状态[DATA_STATUS] operate-GTEqual[>=]    数据状态:数据状态：1-正常 0-删除")
    private Integer startDataStatus;

    @Comment("数据状态[DATA_STATUS] operate-LTEqual[<=]    数据状态:数据状态：1-正常 0-删除")
    private Integer endDataStatus;

    @Comment("创建时间[CREATE_TIME] operate-Equal[=]    创建时间:yyyyMMddHHmmss")
    private Long createTime;

    @Comment("创建时间[CREATE_TIME] operate-In[in]    创建时间:yyyyMMddHHmmss")
    private Long[] createTimes;

    @Comment("创建时间[CREATE_TIME] operate-GTEqual[>=]    创建时间:yyyyMMddHHmmss")
    private Long startCreateTime;

    @Comment("创建时间[CREATE_TIME] operate-LTEqual[<=]    创建时间:yyyyMMddHHmmss")
    private Long endCreateTime;

    @Comment("修改时间[MODIFY_TIME] operate-Equal[=]    修改时间:yyyyMMddHHmmss")
    private Long modifyTime;

    @Comment("修改时间[MODIFY_TIME] operate-In[in]    修改时间:yyyyMMddHHmmss")
    private Long[] modifyTimes;

    @Comment("修改时间[MODIFY_TIME] operate-GTEqual[>=]    修改时间:yyyyMMddHHmmss")
    private Long startModifyTime;

    @Comment("修改时间[MODIFY_TIME] operate-LTEqual[<=]    修改时间:yyyyMMddHHmmss")
    private Long endModifyTime;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long[] getIds() {
        return this.ids;
    }

    public void setIds(Long[] ids) {
        this.ids = ids;
    }

    public Long getStartId() {
        return this.startId;
    }

    public void setStartId(Long startId) {
        this.startId = startId;
    }

    public Long getEndId() {
        return this.endId;
    }

    public void setEndId(Long endId) {
        this.endId = endId;
    }

    public Integer getDefType() {
        return this.defType;
    }

    public void setDefType(Integer defType) {
        this.defType = defType;
    }

    public Integer[] getDefTypes() {
        return this.defTypes;
    }

    public void setDefTypes(Integer[] defTypes) {
        this.defTypes = defTypes;
    }

    public Integer getStartDefType() {
        return this.startDefType;
    }

    public void setStartDefType(Integer startDefType) {
        this.startDefType = startDefType;
    }

    public Integer getEndDefType() {
        return this.endDefType;
    }

    public void setEndDefType(Integer endDefType) {
        this.endDefType = endDefType;
    }

    public String getDefCode() {
        return this.defCode;
    }

    public void setDefCode(String defCode) {
        this.defCode = defCode;
    }

    public String getDefCodeEqual() {
        return this.defCodeEqual;
    }

    public void setDefCodeEqual(String defCodeEqual) {
        this.defCodeEqual = defCodeEqual;
    }

    public String[] getDefCodes() {
        return this.defCodes;
    }

    public void setDefCodes(String[] defCodes) {
        this.defCodes = defCodes;
    }

    public String getDefName() {
        return this.defName;
    }

    public void setDefName(String defName) {
        this.defName = defName;
    }

    public String getDefNameEqual() {
        return this.defNameEqual;
    }

    public void setDefNameEqual(String defNameEqual) {
        this.defNameEqual = defNameEqual;
    }

    public String[] getDefNames() {
        return this.defNames;
    }

    public void setDefNames(String[] defNames) {
        this.defNames = defNames;
    }

    public Long getClassId() {
        return this.classId;
    }

    public void setClassId(Long classId) {
        this.classId = classId;
    }

    public Long[] getClassIds() {
        return this.classIds;
    }

    public void setClassIds(Long[] classIds) {
        this.classIds = classIds;
    }

    public Long getStartClassId() {
        return this.startClassId;
    }

    public void setStartClassId(Long startClassId) {
        this.startClassId = startClassId;
    }

    public Long getEndClassId() {
        return this.endClassId;
    }

    public void setEndClassId(Long endClassId) {
        this.endClassId = endClassId;
    }

    public Long getPageNodeId() {
        return this.pageNodeId;
    }

    public void setPageNodeId(Long pageNodeId) {
        this.pageNodeId = pageNodeId;
    }

    public Long[] getPageNodeIds() {
        return this.pageNodeIds;
    }

    public void setPageNodeIds(Long[] pageNodeIds) {
        this.pageNodeIds = pageNodeIds;
    }

    public Long getStartPageNodeId() {
        return this.startPageNodeId;
    }

    public void setStartPageNodeId(Long startPageNodeId) {
        this.startPageNodeId = startPageNodeId;
    }

    public Long getEndPageNodeId() {
        return this.endPageNodeId;
    }

    public void setEndPageNodeId(Long endPageNodeId) {
        this.endPageNodeId = endPageNodeId;
    }

    public String getDefDesc() {
        return this.defDesc;
    }

    public void setDefDesc(String defDesc) {
        this.defDesc = defDesc;
    }

    public String getSvgUrl() {
        return this.svgUrl;
    }

    public void setSvgUrl(String svgUrl) {
        this.svgUrl = svgUrl;
    }

    public Integer getUseStatus() {
        return this.useStatus;
    }

    public void setUseStatus(Integer useStatus) {
        this.useStatus = useStatus;
    }

    public Integer[] getUseStatuss() {
        return this.useStatuss;
    }

    public void setUseStatuss(Integer[] useStatuss) {
        this.useStatuss = useStatuss;
    }

    public Integer getStartUseStatus() {
        return this.startUseStatus;
    }

    public void setStartUseStatus(Integer startUseStatus) {
        this.startUseStatus = startUseStatus;
    }

    public Integer getEndUseStatus() {
        return this.endUseStatus;
    }

    public void setEndUseStatus(Integer endUseStatus) {
        this.endUseStatus = endUseStatus;
    }

    public String getValidErrMsg() {
        return this.validErrMsg;
    }

    public void setValidErrMsg(String validErrMsg) {
        this.validErrMsg = validErrMsg;
    }

    public Long getDomainId() {
        return this.domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }

    public Long[] getDomainIds() {
        return this.domainIds;
    }

    public void setDomainIds(Long[] domainIds) {
        this.domainIds = domainIds;
    }

    public Long getStartDomainId() {
        return this.startDomainId;
    }

    public void setStartDomainId(Long startDomainId) {
        this.startDomainId = startDomainId;
    }

    public Long getEndDomainId() {
        return this.endDomainId;
    }

    public void setEndDomainId(Long endDomainId) {
        this.endDomainId = endDomainId;
    }

    public String getCreator() {
        return this.creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getCreatorEqual() {
        return this.creatorEqual;
    }

    public void setCreatorEqual(String creatorEqual) {
        this.creatorEqual = creatorEqual;
    }

    public String[] getCreators() {
        return this.creators;
    }

    public void setCreators(String[] creators) {
        this.creators = creators;
    }

    public String getModifier() {
        return this.modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public String getModifierEqual() {
        return this.modifierEqual;
    }

    public void setModifierEqual(String modifierEqual) {
        this.modifierEqual = modifierEqual;
    }

    public String[] getModifiers() {
        return this.modifiers;
    }

    public void setModifiers(String[] modifiers) {
        this.modifiers = modifiers;
    }

    public Integer getDataStatus() {
        return this.dataStatus;
    }

    public void setDataStatus(Integer dataStatus) {
        this.dataStatus = dataStatus;
    }

    public Integer[] getDataStatuss() {
        return this.dataStatuss;
    }

    public void setDataStatuss(Integer[] dataStatuss) {
        this.dataStatuss = dataStatuss;
    }

    public Integer getStartDataStatus() {
        return this.startDataStatus;
    }

    public void setStartDataStatus(Integer startDataStatus) {
        this.startDataStatus = startDataStatus;
    }

    public Integer getEndDataStatus() {
        return this.endDataStatus;
    }

    public void setEndDataStatus(Integer endDataStatus) {
        this.endDataStatus = endDataStatus;
    }

    public Long getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long[] getCreateTimes() {
        return this.createTimes;
    }

    public void setCreateTimes(Long[] createTimes) {
        this.createTimes = createTimes;
    }

    public Long getStartCreateTime() {
        return this.startCreateTime;
    }

    public void setStartCreateTime(Long startCreateTime) {
        this.startCreateTime = startCreateTime;
    }

    public Long getEndCreateTime() {
        return this.endCreateTime;
    }

    public void setEndCreateTime(Long endCreateTime) {
        this.endCreateTime = endCreateTime;
    }

    public Long getModifyTime() {
        return this.modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Long[] getModifyTimes() {
        return this.modifyTimes;
    }

    public void setModifyTimes(Long[] modifyTimes) {
        this.modifyTimes = modifyTimes;
    }

    public Long getStartModifyTime() {
        return this.startModifyTime;
    }

    public void setStartModifyTime(Long startModifyTime) {
        this.startModifyTime = startModifyTime;
    }

    public Long getEndModifyTime() {
        return this.endModifyTime;
    }

    public void setEndModifyTime(Long endModifyTime) {
        this.endModifyTime = endModifyTime;
    }

}
