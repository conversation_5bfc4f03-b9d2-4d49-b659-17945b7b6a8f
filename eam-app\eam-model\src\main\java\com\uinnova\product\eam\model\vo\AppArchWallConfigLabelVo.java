package com.uinnova.product.eam.model.vo;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.util.List;

@Data
public class AppArchWallConfigLabelVo {

    @Comment("属性名称")
    private String attrName;

    @Comment("字段id")
    private Long dictClassId;

    @Comment("属性详情")
    private List<AttrList> details;

    @Data
    public static class AttrList {

        private String name;

        private String color;
    }

}
