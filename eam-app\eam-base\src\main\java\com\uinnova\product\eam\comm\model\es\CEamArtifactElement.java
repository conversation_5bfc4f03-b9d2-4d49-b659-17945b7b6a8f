package com.uinnova.product.eam.comm.model.es;

import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.util.List;

@Data
@Comment("制品标签管理表[UINO_EAM_ARTIFACT_ELEMENT]")
public class CEamArtifactElement implements Condition {

    @Comment("自增id")
    private Long id;

    @Comment("制品信息表主id")
    private Long artifactId;

    @Comment("新建制品的所有标签")
    private List<String> elements;

    @Comment("新建制品的顺序")
    private Integer orderNum;

    @Comment(("制品分栏信息类型 type=1:图形、对象配置项 type=2:资产数据配置项 type=3:关系配置项 type=4:模板配置项"))
    private Integer type;

}
