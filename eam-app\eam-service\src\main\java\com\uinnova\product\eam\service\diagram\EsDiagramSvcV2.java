package com.uinnova.product.eam.service.diagram;


import com.uinnova.product.eam.base.diagram.enums.DiagramCopyEnum;
import com.uinnova.product.eam.base.diagram.model.ESDiagram;
import com.uinnova.product.eam.base.diagram.model.ESDiagramDTO;
import com.uinnova.product.eam.model.diagram.DiagramPullRequest;
import com.uinnova.product.eam.model.diagram.DiagramPullResponse;
import com.uinnova.product.eam.model.diagram.DiagramPushRequest;
import com.uinnova.product.eam.model.diagram.DiagramPushResponse;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 视图业务层接口扩展
 * <AUTHOR>
 */
public interface EsDiagramSvcV2 {
    /**
     * 通过视图id查询视图全量信息接口,不做权限控制及逻辑删除条件限制
     * @param diagramIds 视图id
     * @return 视图详细信息
     */
    List<ESDiagramDTO> queryDiagramByIds(List<Long> diagramIds);

    /**
     * 通过视图加密id查询视图全量信息接口,不做权限控制及逻辑删除条件限制
     * @param diagramIds 视图加密id
     * @return 视图全量信息
     */
    List<ESDiagramDTO> queryDiagramByIds(Collection<String> diagramIds);

    /**
     * 删除视图
     * @param diagramIds 视图加密id
     * @param delDirId 删除至目录id（若空则删除到视图原目录）
     * @param type 1：逻辑删除 2: 物理删除
     * @return 删除异常提示
     */
    String deleteDiagramWithType(List<String> diagramIds, Long delDirId, Integer type);

    /**
     * 批量发布视图
     * @param request 请求参数
     * @return 响应参数
     */
    DiagramPushResponse diagramPush(DiagramPushRequest request);

    /**
     * 批量检出视图
     * @param request 请求参数
     * @return 响应参数
     */
    DiagramPullResponse diagramPull(DiagramPullRequest request);

    /**
     * 通过制品id查询视图
     * @param artifactIds 制品id
     * @return 视图基本信息
     */
    List<ESDiagram> queryByArtifactIds(List<Long> artifactIds);

    /**
     * 批量复制视图
     * @param diagramDirIdMap 视图id、新视图文件夹id
     * @param diagramList 原视图集合
     * @param type 复制类型
     * @return 原视图id、新视图id映射
     */
    Map<String, String> copyDiagramBatch(Map<String, Long> diagramDirIdMap, List<ESDiagram> diagramList, DiagramCopyEnum type);

    Object deleteUselessDiagramInfo();
}
