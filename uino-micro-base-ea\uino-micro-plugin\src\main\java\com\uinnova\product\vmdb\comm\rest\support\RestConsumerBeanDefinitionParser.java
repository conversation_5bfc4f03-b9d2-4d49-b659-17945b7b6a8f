package com.uinnova.product.vmdb.comm.rest.support;

import com.binary.core.lang.ClassUtils;
import com.binary.core.util.BinaryUtils;
import com.binary.framework.exception.ServiceException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.beans.factory.support.RootBeanDefinition;
import org.springframework.beans.factory.xml.BeanDefinitionParser;
import org.springframework.beans.factory.xml.ParserContext;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

/**
 * 
 * <AUTHOR>
 *
 */
public class RestConsumerBeanDefinitionParser implements BeanDefinitionParser {
    private static final Logger logger = LoggerFactory.getLogger(RestConsumerBeanDefinitionParser.class);

    @Override
    public BeanDefinition parse(Element element, ParserContext parserContext) {
        String providerRoot = element.getAttribute("providerRoot");
        String headers = element.getAttribute("headers");

        if (BinaryUtils.isEmpty(providerRoot)) {
            throw new ServiceException(" the consumer-rest's attribute 'providerRoot' is empty! ");
        }
        RestConsumerClient restClient = new RestConsumerClient(providerRoot, headers);
        RestConsumerClientManager cmgr = RestConsumerClientManager.getInstance();
        cmgr.addClient(restClient);
        cmgr.register2SpringContext(parserContext);

        logger.info(" load rest consumer schema '" + providerRoot + "' ... ");

        NodeList subs = element.getChildNodes();
        if (subs != null && subs.getLength() > 0) {
            for (int i = 0; i < subs.getLength(); i++) {
                Node node = subs.item(i);
                if (!(node instanceof Element)) {
                    continue;
                }
                Element el = (Element) node;

                String id = el.getAttribute("id");
                String iface = el.getAttribute("interface");

                if (iface == null || (iface = iface.trim()).length() == 0) {
                    throw new ServiceException(" the consumer:reference's attribute 'interface' is empty! ");
                }

                logger.info(" register rest consumer '" + iface + "' ... ");
                registerConsumer(parserContext, id, iface, restClient);
            }
        }

        return null;
    }

    protected void registerConsumer(ParserContext parserContext, String id, String iface, RestConsumerClient restClient) {
        Class<?> type = ClassUtils.forName(iface, RestConsumerBeanDefinitionParser.class.getClassLoader());
        if (!type.isInterface()) {
            throw new ServiceException(" the class '" + type.getName() + "' is not interface! ");
        }

        if (BinaryUtils.isEmpty(id)) {
            id = iface;
        }
        id = id.trim();

        RestConsumerAware aware = new RestConsumerAware(id, type, restClient);

        RootBeanDefinition beanDefinition = new RootBeanDefinition();
        beanDefinition.setBeanClass(RestConsumerReferenceBean.class);
        beanDefinition.setLazyInit(false);

        beanDefinition.getConstructorArgumentValues().addIndexedArgumentValue(0, aware);
        parserContext.getRegistry().registerBeanDefinition(id, beanDefinition);
    }

}
