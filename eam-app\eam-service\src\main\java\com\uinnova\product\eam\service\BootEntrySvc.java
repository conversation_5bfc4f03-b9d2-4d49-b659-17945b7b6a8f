package com.uinnova.product.eam.service;

import com.uinnova.product.eam.comm.model.es.BootEntryUserConfig;
import com.uinnova.product.eam.comm.model.es.BootPageProgress;
import com.uinnova.product.eam.model.BootEntryBo;
import com.uinnova.product.eam.model.vo.BootEntryVo;
import org.springframework.http.ResponseEntity;

import java.util.List;

public interface BootEntrySvc {
    Long saveOrUpdateEntry(BootEntryBo bootEntryBo);

    List<BootEntryVo> getBootEntryList();

    int deleteBootEntry(Long bootEntryId,Long userRoleId);

    Long saveBootPageProgress(BootPageProgress bootPageProgress);

    Long saveBootTrialPageProgress(String bootTrialPageCode);

    Boolean getUserBootTrialPageProcess(String bootTrialPageCode);

    List<BootPageProgress> getUserRoleProgress(Long userRoleId);

    Long addBootPageUser(BootEntryUserConfig bootEntryUserConfig);

    List<BootEntryUserConfig> getBootPageUsers();

    ResponseEntity<byte[]> exportConfigInitData();

    ResponseEntity<byte[]> exportBootEntryInitData();

    List<Long> getModuleById(Long moduleId);

    Boolean showButton();

    Long updateRoleField();

    void brushStockRoles();
}
