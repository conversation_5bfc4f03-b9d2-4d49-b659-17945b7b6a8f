package com.uinnova.product.eam.web.bm.mvc;

import com.alibaba.fastjson.JSONObject;
import com.binary.framework.util.ControllerUtils;
import com.uinnova.product.eam.service.ICIHistorySwitchSvc;
import com.uinnova.product.eam.service.ICISwitchSvc;
import com.uinnova.product.eam.service.asset.BmConfigSvc;
import com.uinnova.product.vmdb.comm.doc.annotation.MvcDesc;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 配置管理
 * <AUTHOR>
 */
@Controller
@RequestMapping("/bm/config")
@MvcDesc(author="Shaolong.fan",desc="配置管理")
public class BmConfigMvc {

    @Resource
    private BmConfigSvc configSvc;

    @Autowired
    ICISwitchSvc iciSwitchSvc;

    @Autowired
    ICIHistorySwitchSvc ciHistorySwitchSvc;

    /**
     * 校验用户是否具有系统还原权限
     * @param request
     * @param response
     * @return
     */
    @GetMapping("checkSystemRestore")
    @Deprecated
    public void checkSystemRestore(HttpServletRequest request, HttpServletResponse response) {
        ControllerUtils.returnJson(request, response, false);
    }

    @GetMapping("checkShowTaskTag")
    public void checkShowTaskTag(HttpServletRequest request, HttpServletResponse response) {
        JSONObject result = configSvc.checkShowTaskTag();
        ControllerUtils.returnJson(request, response, result);
    }

}
