package com.uinnova.product.eam.base.diagram.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 目录类型
 *
 * <AUTHOR>
 */
@AllArgsConstructor
public enum DirTypeEnum {

    /**
     * 没有定义的目录/其他类型的目录
     */
    UNDEFINED(-1),
    /**
     * 通用视图
     */
    GENERAL(1),

    /**
     * 业务建模
     */
    BM(10),

    /**
     * 业务组件
     */
    BM_MODULE(100),

    /**
     * 应用架构
     */
    APPLICATION_ARCHITECTURE(11),

    /**
     * 数据架构
     */
    DATA_ARCHITECTURE(101),
    PRODUCT_SOLUTION(110),

    /**
     * 技术架构
     */
    TECH_ARCHITECTURE(111);

    @Getter
    private final int val;

    public static DirTypeEnum getByVal(int val) {
        for (DirTypeEnum dirTypeEnum : DirTypeEnum.values()) {
            if (dirTypeEnum.getVal() == val) {
                return dirTypeEnum;
            }
        }
        return DirTypeEnum.UNDEFINED;
    }
}

