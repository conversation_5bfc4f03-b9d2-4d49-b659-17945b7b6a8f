package com.uino.bean.cmdb.base;

import java.io.Serializable;
import java.util.List;

import com.alibaba.fastjson.annotation.JSONField;
import com.binary.framework.bean.annotation.Comment;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @data 2019/7/31 11:03.
 */
@Getter
@Setter
@ApiModel(value="关系遍历规则类",description = "关系遍历规则信息")
public class ESRltRuleInfo implements Serializable {


    private static final long serialVersionUID = 8072884742366036454L;
    @ApiModelProperty(value="规则id",example = "123")
    private Long id;

    @ApiModelProperty(value = "定义名称",example = "jfpf")
    @Comment("定义名称")
    private String name;

    @ApiModelProperty(value="入口分类id",example = "123")
    @Comment("入口分类")
    private Long startClassId;

    @ApiModelProperty(value="入口页面节点id",example = "123")
    @Comment("入口页面节点ID")
    private Long startPageNodeId;

    @ApiModelProperty(value="所属域id",example = "123")
    @Comment("所属域")
    private Long domainId;

    @ApiModelProperty(value="定义描述")
    @Comment("定义描述")
    private String desc;

    @ApiModelProperty(value="创建人",example = "tom")
    @Comment("创建人")
    private String creator;

    @ApiModelProperty(value="修改人",example = "mike")
    @Comment("修改人")
    private String modifier;

    @ApiModelProperty(value="创建时间")
    @Comment("创建时间")
    private Long createTime;

    @ApiModelProperty(value="修改时间")
    @Comment("修改时间")
    private Long modifyTime;

    @ApiModelProperty(value = "节点")
    @Comment("节点")
    private List<NodeInfo> nodes;

    @ApiModelProperty(value="规则线")
    @Comment("规则线")
    private List<LineInfo> lines;


    @Setter
    @Getter
    @ApiModel(value="规则线类",description = "规则线信息")
    public static class Line {
        @ApiModelProperty(value="规则线id",example = "123")
        private Long pageLineId;

        //分类关系id
        @ApiModelProperty(value="分类关系id",example="123")
        private Long clsRltId;

        @ApiModelProperty(value="起始分类id",example = "123")
        //起始分类
        private Long clsStartId;

        @ApiModelProperty(value="结束分类id",example = "123")
        //结束分类
        private Long clsEndId;

        @ApiModelProperty(value="起始节点id",example = "123")
        //起始节点ID   起始节点ID:供页面用
        private Long nodeStartId;

        @ApiModelProperty(value="结束节点id",example = "1123")
        //结束节点ID    结束节点ID:供页面用
        private Long nodeEndId;

        @ApiModelProperty(value="关系线类型",example = "1")
        //关系线类型   关系线类型:1=规则关系、2=empty
        private Integer lineType;

        @ApiModelProperty(value="运算符",example = "1")
        //运算符    运算符:1=between、2=大于等于、3=小于等于
        private Integer lineOp;

        @ApiModelProperty(value="条件值")
        //条件值    条件值为两个值时以逗号隔开
        private String lineVal;

        @ApiModelProperty(value="关系线方向",example = "1")
        //关系线条件方向：1=线起始，2=线终止
        private Integer lineDirect;
    }

    @Setter
    @Getter
    @ApiModel(value="关系线条件类",description = "关系线条件信息")
    public static class LineCdt {
        @ApiModelProperty(value="所属关系线")
        //所属关系线
        private Long lineId;

        @ApiModelProperty(value="条件属性")
        //条件属性
        private Long attrId;

        @ApiModelProperty(value="运算符")
        //运算符
        private Integer cdtOp;

        @ApiModelProperty(value="条件值")
        //条件值
        private String cdtVal;
    }

    @Setter
    @Getter
    @ApiModel(value="关系线类",description = "关系线信息")
    public static class LineInfo {
        @ApiModelProperty(value="关系线")
        private Line line;

        @ApiModelProperty(value="关系线条件集合")
        private List<LineCdt> lineCdts;

        @ApiModelProperty(value="ES对应的分类信息")
        @JSONField(serialize = false)
        private ESCIClassInfo classInfo;
    }

    @Setter
    @Getter
    @ApiModel(value="节点类",description = "节点信息")
    public static class Node {
        //页面节点ID
        @ApiModelProperty(value="页面节点ID",example = "123")
        private Long pageNodeId;

        @ApiModelProperty(value="节点分类ID",example = "123")
        //节点分类ID
        private Long classId;

        @ApiModelProperty(value="X坐标")
        //X坐标[X]
        private Integer x;

        @ApiModelProperty(value="Y坐标")
        //Y坐标[Y]
        private Integer y;

        @ApiModelProperty(value="节点类型",example="1")
        //节点类型   节点类型 1=CI节点、2=运算符节点
        private Integer nodeType;

        @ApiModelProperty(value="逻辑节点id",example="1")
        //逻辑节点ID    逻辑节点ID 1=and、2=or
        private Integer logicOperation;

        @ApiModelProperty(value="返回字段")
        //返回字段
        private String returns;

    }

    @Setter
    @Getter
    @ApiModel(value="节点条件类",description = "节点条件信息")
    public static class NodeCdt {
        @ApiModelProperty(value="所属节点id",example = "123")
        //所属节点
        private Long nodeId;

        @ApiModelProperty(value="条件属性id",example = "123")
        //条件属性
        private Long attrId;

        @ApiModelProperty(value="运算符",example = "1")
        //运算符   1：= ，2：like
        private Integer cdtOp;

        @ApiModelProperty(value="条件值")
        //条件值
        private String cdtVal;
    }

    @Setter
    @Getter
    @ApiModel(value="节点信息类",description = "节点信息")
    public static class NodeInfo implements Serializable {

        private static final long serialVersionUID = -880822066992965553L;
        @ApiModelProperty(value="节点")
        private Node node;

        @ApiModelProperty(value="节点条件集合")
        private List<NodeCdt> nodeCdts;
        //不存库

        @ApiModelProperty(value="分类信息")
        @JSONField(serialize = false)
        private ESCIClassInfo classInfo;

    }

}

