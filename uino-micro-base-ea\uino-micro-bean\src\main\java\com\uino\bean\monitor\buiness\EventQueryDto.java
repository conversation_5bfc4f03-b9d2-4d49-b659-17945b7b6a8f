package com.uino.bean.monitor.buiness;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * 告警查询数据传输类
 */
@Data
@ApiModel(value = "EventQueryDto.class", description = "告警查询数据传输类")
public class EventQueryDto {

    @ApiModelProperty(value = "第几页", example = "1",  required = true)
    private int pageNum = 1;

    @ApiModelProperty(value = "每页多少条", example = "10",  required = true)
    private int pageSize = 10;

    @ApiModelProperty(value = "搜索关键字")
    private String search_key;

    @ApiModelProperty(value = "排序字段", example = "lastOccurrence",  required = true)
    private String sortName;

    @ApiModelProperty(value = "排序规则：ASC 升序，DESC 降序", example = "DESC",  required = true)
    private String sortOrder;

    @ApiModelProperty(value = "CI的Id集合")
    private List<Long> ciIds;

    @ApiModelProperty(value = "事件源", example = "[]")
    private List<Integer> sourceId;

    @ApiModelProperty(value = "级别", example = "[]")
    private List<Integer> severity;

    @ApiModelProperty(value = "告警状态1：开启；2：关闭；3：恢复", example = "[]")
    private List<Integer> status;

    @ApiModelProperty(value = "响应/确认状态0：未确认；1：已确认", example = "[]")
    private List<Integer> ackStatus;

    @ApiModelProperty(value = "通知状态0：未通知；1：已通知", example = "[]")
    private List<Integer> notifyStatus;

    @ApiModelProperty(value = "工单状态0：未发送；1：已发送", example = "[]")
    private List<Integer> billStatus;

    @ApiModelProperty(value = "应用", example = "[]")
    private List<String> ciApplication;

    @ApiModelProperty(value = "标签", example = "[]")
    private List<Integer> tag;

    @ApiModelProperty(value = "是否屏蔽（0：未屏蔽；1：已屏蔽）", example = "[]")
    private List<Integer> blackouts;

    @ApiModelProperty(value = "开始时间", example = "时间戳格式：1620018196049；时间字符串格式：2021-04-03 14:38:34", required = true)
    private Object startTime;

    @ApiModelProperty(value = "结束时间", example = "时间戳格式：1620018196049；时间字符串格式：2021-04-03 14:38:34", required = true)
    private Object endTime;

    @ApiModelProperty(value = "所属域", example = "1")
    private Long domainId;

    @ApiModelProperty(value = "告警筛选范围信息")
    private List<SelectorList> selectorList;

    /**
     * 兼容时间戳与字符串时间格式查询
     * @return
     */
    public Long getStartTime() {
        if(startTime == null) return null;
        if (startTime instanceof Long) {
            return ((Long) startTime).longValue();
        } else {
            return dateToLong(startTime.toString());
        }
    }

    /**
     * 兼容时间戳与字符串时间格式查询
     * @return
     */
    public Long getEndTime() {
        if(endTime == null) return null;
        if (endTime instanceof Long) {
            return ((Long) endTime).longValue();
        } else {
            return dateToLong(endTime.toString());
        }
    }

    /**
     * 时间字符串转换为时间戳
     * @param dateString
     * @return
     */
    private Long dateToLong(String dateString) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = null;
        try {
            date = format.parse(dateString);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return date.getTime();
    }

    @ApiModelProperty(value = "是否被过滤规则处理过，filterType = 1 是被处理并关闭的")
    private List<Integer> filterType;

    @ApiModelProperty(value = "是否展示被压缩的告警")
    private int showCompressed = 0;

    @ApiModelProperty(value = "告警序列号")
    List<String> serials;

    @ApiModelProperty(value = "告警对象名称")
    List<String> sourceCiNames;

    @ApiModelProperty(value = "孪生体ID, 数字孪生全景下, 该字段必填", example = "7123062912700572", required = true)
    private String digitalTwinId;

    @ApiModelProperty(value = "是否屏蔽, 0 未屏蔽, 1 屏蔽中")
    private List<Integer> shield;

    @ApiModelProperty(value = "搜索字段名称, sourceSummary 告警描述, kpiName 告警指标")
    private String searchField;
}
