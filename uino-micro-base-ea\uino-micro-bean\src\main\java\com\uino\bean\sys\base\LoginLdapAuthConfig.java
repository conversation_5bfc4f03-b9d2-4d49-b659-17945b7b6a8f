package com.uino.bean.sys.base;

import java.io.Serializable;

import com.binary.framework.bean.annotation.Comment;
import com.uino.bean.permission.business.IValidDto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value="登录LADP验证集成",description = "登录LADP验证集成")
public class LoginLdapAuthConfig implements Serializable, IValidDto {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value="主机名",example = "host")
    @Comment("主机名")
    private String hostName;

    @ApiModelProperty(value="端口",example = "8081")
    @Comment("端口")
    private String port;

    @ApiModelProperty(value="使用ssl")
    @Comment("使用ssl")
    private Integer useSsl;


    @ApiModelProperty(value="登录用户",example = "admin")
    @Comment("登陆用户")
    private String loginUserDn;

    @ApiModelProperty(value="登用户密码",example = "123456")
    @Comment("登陆用户密码")
    private String password;

    @ApiModelProperty(value="基本DN")
    @Comment("基本DN")
    private String baseDn;

    @ApiModelProperty(value="用户id属性(登陆名)",example = "admin")
    @Comment("用户id属性(登陆名)")
    private String userNameAttr;

    @ApiModelProperty(value="用户名RDN(相对DN)属性")
    @Comment("用户名RDN(相对DN)属性")
    private String userNameRdnAttr;

    @ApiModelProperty(value="用户显示名属性")
    @Comment("用户显示名属性")
    private String displayNameAttr;

    @ApiModelProperty(value="用户邮箱属性")
    @Comment("用户邮箱属性")
    private String mailAttr;

    @Override
    public void valid() {

    }

}
