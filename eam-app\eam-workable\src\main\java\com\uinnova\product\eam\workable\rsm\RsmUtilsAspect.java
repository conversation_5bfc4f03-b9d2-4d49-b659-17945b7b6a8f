package com.uinnova.product.eam.workable.rsm;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * obs工具类切面类
 */
@Aspect
@Component
public class RsmUtilsAspect {

    /**
     * 判断系统当前是否使用了对象存储
     * 如果使用了对象存储则
     * 对于资源的读取操作，所有资源会优先从对象存储获取，并同步至当前服务器的资源文件夹下，再进行后续操作；
     * 对于资源的写入操作，所有资源写入到本地资源文件下后，会同步写入对象存储中
     * 对于资源的删除操作，删除本地资源文件夹下的文件后，会同步删除对象存储的文件
     * @return
     */
    public boolean isUseObs(){
        return this.obsUseFlag;
    }

    /**
     * 是否使用obs
     */
    @Value("${obs.use}")
    private boolean obsUseFlag;

    @Pointcut("execution(* com.uinnova.product.eam.workable.rsm.*.*(..))")
    public void servicePointcut() {}

    @Around("servicePointcut()")
    public Object aroundAddUser(ProceedingJoinPoint pjp) throws Throwable {
        if(this.isUseObs()){
            return pjp.proceed();
        }
        return true;
    }


}
