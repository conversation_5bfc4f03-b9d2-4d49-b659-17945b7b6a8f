package com.uinnova.product.eam.feign.workable.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * description
 *
 * <AUTHOR>
 * @since 2022/2/28 16:26
 */
@Data
public class DeployResponse {

    private String id;

    private String key;

    private String deployName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date deployTime;

}
