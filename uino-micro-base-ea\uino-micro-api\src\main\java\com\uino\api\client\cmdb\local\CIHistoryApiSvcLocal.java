package com.uino.api.client.cmdb.local;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import com.uino.service.cmdb.microservice.ICIHistorySvc;
import com.uino.bean.cmdb.base.ESCIHistoryInfo;
import com.uino.api.client.cmdb.ICIHistoryApiSvc;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
@Primary
public class CIHistoryApiSvcLocal implements ICIHistoryApiSvc {

    @Autowired
    private ICIHistorySvc ciHistorySvc;

    @Override
    public void delAll(List<Long> classIds) {
        ciHistorySvc.delAll(classIds);
    }

    @Override
    public List<String> getCIVersionList(String ciCode, Long classId) {
        return ciHistorySvc.getCIVersionList(ciCode, classId);
    }

    @Override
    public ESCIHistoryInfo getCIInfoHistoryByCIVersion(String ciCode, Long classId, Long version) {
        return ciHistorySvc.getCIInfoHistoryByCIVersion(ciCode, classId, version);
    }

    @Override
    public List<ESCIHistoryInfo> bathGetCIInfoHistoryByCIVersion(Map<String, Long> ciCodeVersionMap) {
        return ciHistorySvc.bathGetCIInfoHistoryByCIVersion(ciCodeVersionMap);
    }

}
