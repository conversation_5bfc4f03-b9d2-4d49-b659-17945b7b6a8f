package com.uino.api.client.sys.local;

import com.uino.dao.BaseConst;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.binary.jdbc.Page;
import com.uino.service.sys.microservice.IOperateLogSvc;
import com.uino.bean.sys.base.ESOperateLog;
import com.uino.bean.sys.query.ESOperateLogSearchBean;
import com.uino.api.client.sys.IOperateLogApiSvc;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
public class OperateLogApiSvcLocal implements IOperateLogApiSvc {

    @Autowired
    private IOperateLogSvc logSvc;

    @Override
    public Page<ESOperateLog> getOperateLogPageByCdt(ESOperateLogSearchBean bean) {
        return logSvc.getOperateLogPageByCdt(bean);
    }

    @Override
    public Integer clearOperateLogByDuration(Integer durationDay) {
        return logSvc.clearOperateLogByDuration(durationDay);
    }


    @Override
    public Long saveOrUpdate(ESOperateLog log) {
        return logSvc.saveOrUpdate(log);
    }

}
