package com.uinnova.product.vmdb.comm.util;

import java.util.Stack;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class SixtyTwoUtil {
	
	public static void main( String[] args ) {
		String temp = SixtyTwoUtil.decimal2ST(99999999);
		long sourceNumber = SixtyTwoUtil.st2Decimal(temp);
		temp = SixtyTwoUtil.decimal2ST(20190923);
		sourceNumber = SixtyTwoUtil.st2Decimal(temp);
		System.out.println("sourceNumber="+sourceNumber);
	}
	
	//
	//0-9a(10)-z(35)A(36)-Z(61)=62进制
	private final static Pattern patST =Pattern.compile("[0-9a-zA-Z]+");
	private final static int SIXTYTWO = 62;
	public final static char[] ST_Digit = new char[]{'0','1','2','3','4','5','6','7','8','9',
			'q','a','z','w','s','x','e','d','c','i','k','o','l','p','r','f','v','t','g','b','y','h','n','u','j','m',
			'Q','A','Z','W','S','X','E','D','C','I','K','O','L','P','R','F','V','T','G','B','Y','H','N','U','J','M'};
	
	public static String decimal2ST(long decimal) {
		// 创建栈
	    Stack<Character> stack = new Stack<Character>();
		StringBuffer sbSixTwo=new StringBuffer("");
		long rest = decimal;
		if(decimal<0) {
			return "";
		}else {
			rest = decimal;
		}
		
		//
		while(true) {
			//进栈
			Long lMod = rest%SIXTYTWO;
			stack.add(ST_Digit[lMod.intValue()]);
			rest = rest / SIXTYTWO;
			if(rest<=0) {
				break;
			}
		}
		
	    for (; !stack.isEmpty();) {
	        // 出栈
	    	sbSixTwo.append(stack.pop());
	    }		
		
		return sbSixTwo.toString();
	}
	
	public static long st2Decimal(String st) {
		
		Matcher matcher = patST.matcher(st);
		if(!matcher.matches()) {
			throw new NumberFormatException("invalid st:"+st);
		}
	    // 倍数
	    int multiple = 1;
	    long result = 0;
	    Character c;
	    for (int i = 0; i < st.length(); i++) {
	        c = st.charAt(st.length() - i - 1);
	        result += decodeChar(c) * multiple;
	        multiple = multiple * SIXTYTWO;
	    }
	    return result;
	 }

	public static int decodeChar(Character c) {
	    for (int i = 0; i < ST_Digit.length; i++) {
	        if (c == ST_Digit[i]) {
	        return i;
	        }
	    }
	    return -1;
    }
}
