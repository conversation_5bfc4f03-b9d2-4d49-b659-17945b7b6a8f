package com.uino.service.simulation.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import jakarta.annotation.PostConstruct;

import com.binary.core.exception.MessageException;
import com.uino.bean.sys.base.TenantDomain;
import com.uino.dao.sys.ESTenantDomainSvc;
import com.uino.dao.util.DateUtil;
import org.apache.lucene.queryparser.xml.builders.BooleanQueryBuilder;
import org.apache.lucene.search.BooleanQuery;
import org.checkerframework.checker.nullness.qual.Nullable;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.binary.core.util.BinaryUtils;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uino.bean.cmdb.base.ESCIClassInfo;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.event.Event2KafkaDTO;
import com.uino.bean.monitor.base.ESAlarm;
import com.uino.bean.monitor.base.ESKpiInfo;
import com.uino.bean.monitor.base.ESMonEapEvent;
import com.uino.bean.monitor.base.ESMonSysSeverityInfo;
import com.uino.bean.monitor.base.SimulationRuleInfo;
import com.uino.bean.monitor.buiness.SearchKpiBean;
import com.uino.bean.monitor.buiness.SimulationPerformanceBean;
import com.uino.bean.sys.base.ESDictionaryItemInfo;
import com.uino.bean.tp.base.FinalPerformanceDTO;
import com.uino.bean.tp.base.PerformanceDTO;
import com.uino.bean.tp.base.PerformanceDTO.CIObjectDTO;
import com.uino.dao.BaseConst;
import com.uino.dao.cmdb.ESCIClassSvc;
import com.uino.dao.cmdb.ESCISvc;
import com.uino.dao.cmdb.ESRltClassSvc;
import com.uino.dao.event.ESAlarmSvc;
import com.uino.dao.event.ESEventSvc;
import com.uino.dao.event.EventHistoryDao;
import com.uino.dao.simulation.ESSimulationRuleSvc;
import com.uino.dao.sys.ESDictionaryItemSvc;
import com.uino.dao.util.ESUtil;
import com.uino.monitor.event.service.impl.EventServiceImpl;
import com.uino.monitor.performance.IUinoPerformanceSvc;
import com.uino.service.simulation.IKpiSvc;
import com.uino.util.message.queue.MessageQueueProducer;
import com.uino.util.message.queue.MessageTopicConst;
import com.uino.util.sys.BeanUtil;
import com.uino.util.sys.SpringUtil;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RefreshScope
@ConditionalOnProperty(name = "base.simulation.rule.auto.excute", havingValue = "true", matchIfMissing = false)
public class SimulationRuleAutoTaskSvc {

    private final ESSimulationRuleSvc ruleSvc;

    private final IUinoPerformanceSvc performanceSvc;

    private final ESCISvc ciSvc;

    private final ESCIClassSvc esciClassSvc;

    private final ESRltClassSvc rltClassSvc;

    private final IKpiSvc KpiSvc;

    private final ESDictionaryItemSvc dictItemSvc;

    private final EventServiceImpl eventService;

    private final EventHistoryDao eventHistoryDao;

    private final ESAlarmSvc esAlarmSvc;

    private final ESEventSvc esEventSvc;

    private final ESTenantDomainSvc esTenantDomainSvc;
    @Value("${base.load-tp:false}")
    private boolean enableTp;

    //ep 是否存在 存在 告警发dix 不存在直接入库
    @Value("${uino.monitor.ep.exist:false}")
    private Boolean epExist;

    // 是否保存历史告警（默认false）
    @Value("${uino.monitor.event.saveEventHis.enable:true}")
    private Boolean enableSaveEventHis;

    @Value("${uino.tenantDomain:false}")
    private boolean isOpenTenantDomain;

    private static final Gson GSON = new GsonBuilder().create();

    public static Cache<Long, List<ESCIInfo>> ES_CI_INFO_CACHE = Caffeine.newBuilder().maximumSize(50000)
            .expireAfterWrite(20, TimeUnit.MINUTES).build();

    private static final int MAX_EXECUTE_POOL_SIZE = 10000;
    //创建一个定长线程池，控制线程最大并发数，超出的线程会在队列中等待
    private static final ExecutorService MAIN_EXECUTOR = Executors.newFixedThreadPool(1);

    private static final ThreadPoolExecutor THREAD_POOL_EXECUTOR = new ThreadPoolExecutor(5, 5, 0L,
            TimeUnit.MILLISECONDS, new ArrayBlockingQueue<>(MAX_EXECUTE_POOL_SIZE),
            new ThreadPoolExecutor.CallerRunsPolicy());

    ArrayBlockingQueue<SimulationPerformanceBean> SIMULATION_PERFORMANCCE_QUEUE = new ArrayBlockingQueue<>(
            MAX_EXECUTE_POOL_SIZE);

    @Autowired
    public SimulationRuleAutoTaskSvc(ESSimulationRuleSvc ruleSvc, IUinoPerformanceSvc performanceSvc, ESCISvc ciSvc,
                                     ESCIClassSvc esciClassSvc, ESRltClassSvc rltClassSvc, IKpiSvc KpiSvc, ESDictionaryItemSvc dictItemSvc, EventServiceImpl eventService, EventHistoryDao eventHistoryDao, ESAlarmSvc esAlarmSvc, ESEventSvc esEventSvc, ESTenantDomainSvc esTenantDomainSvc, @Value("${base.simulation.rule.ci.expire.time:}") Integer expireTime) {
        this.ruleSvc = ruleSvc;
        this.performanceSvc = performanceSvc;
        this.ciSvc = ciSvc;
        this.esciClassSvc = esciClassSvc;
        this.rltClassSvc = rltClassSvc;
        this.KpiSvc = KpiSvc;
        this.dictItemSvc = dictItemSvc;
        this.eventService = eventService;
        this.eventHistoryDao = eventHistoryDao;
        this.esAlarmSvc = esAlarmSvc;
        this.esEventSvc = esEventSvc;
        this.esTenantDomainSvc = esTenantDomainSvc;
        if (expireTime != null) {
            ES_CI_INFO_CACHE = Caffeine.newBuilder().maximumSize(50000).expireAfterWrite(expireTime, TimeUnit.MINUTES)
                    .build();
        }
    }

    @Scheduled(fixedRate = 1000L, initialDelay = 1000L * 5)
    // @Async
    //查询启用的模拟规则，如果规则已经开始，那么就调用执行模拟规则方法simulationPerformance()方法
    public void excuteSimulationRule() {
        long curTime = System.currentTimeMillis();
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        if (isOpenTenantDomain) {
            //查询开启的域
            List<TenantDomain> tenantDomains = esTenantDomainSvc.getListByQuery(QueryBuilders.termQuery("enableStatus", 1));
            if (tenantDomains != null && tenantDomains.size() > 0) {
                List<Long> domainIds = tenantDomains.stream().map(TenantDomain::getId).collect(Collectors.toList());
                query.must(QueryBuilders.termsQuery("domainId", domainIds));
            }
        } else {
            query.must(QueryBuilders.termQuery("domainId", BaseConst.DEFAULT_DOMAIN_ID));
        }
        query.must(QueryBuilders.termQuery("ruleStatus", 1));
        List<SimulationRuleInfo> ruleList = ruleSvc.getListByQuery(query);

        if (!BinaryUtils.isEmpty(ruleList)) {
            Map<Long, List<SimulationRuleInfo>> domain_ruleList = new HashMap<>();
            Map<Long, Set<String>> domain_kpiNames = new HashMap<>();
            for (SimulationRuleInfo ruleInfo : ruleList) {
                domain_ruleList.put(ruleInfo.getDomainId(), ruleList);
                if (ruleInfo.getRuleType() == 1) {
                    if (!domain_kpiNames.containsKey(ruleInfo.getDomainId())) {
                        Set<String> kpiNamesSet = new HashSet<>();
                        kpiNamesSet.add(ruleInfo.getMetric());
                        domain_kpiNames.put(ruleInfo.getDomainId(), kpiNamesSet);
                    } else {
                        domain_kpiNames.get(ruleInfo.getDomainId()).add(ruleInfo.getMetric());
                    }
                }
            }
            //按域执行规则
            for (Long domainId : domain_ruleList.keySet()) {
                Map<String, ESKpiInfo> kpiMap = new HashMap<>();
                //域内性能规则的kpi集合
                if (domain_kpiNames.size() > 0) {
                    Set<String> kpiNames = domain_kpiNames.get(domainId);
                    List<ESKpiInfo> kpiInfos = KpiSvc.queryKpiInfoPage(SearchKpiBean.builder().kpiNames(kpiNames).domainId(domainId).build())
                            .getData();
                    //kpiMap
                    kpiMap = BinaryUtils.toObjectMap(kpiInfos, "kpiCode");
                }
                List<SimulationRuleInfo> simulationRuleInfos = domain_ruleList.get(domainId);
                this.traverseSimulationRules(curTime, simulationRuleInfos, kpiMap);
            }

        }
    }


    private void traverseSimulationRules(long curTime, List<SimulationRuleInfo> ruleList, Map<String, ESKpiInfo> kpiMap) {
        for (SimulationRuleInfo ruleInfo : ruleList) {
            try {
                if (ruleInfo.getRuleType() == 1) {
                    //模拟性能若开始时间未填写，创建任务后立即开始执行
                    if (DateUtil.getSecondTimestamp(new Date(curTime)) >= DateUtil.getSecondTimestamp(new Date(ruleInfo.getStartTime()))) {
                        simulationPerformance(ruleInfo, kpiMap, curTime);
                    }
                } else {
                    simulationAlarms(ruleInfo, curTime);
                }
            } catch (Exception e) {
                // 规则执行错误，停用该规则
                log.info(e.getMessage());
                ES_CI_INFO_CACHE.invalidate(ruleInfo.getId());
                ruleInfo.setRuleStatus(0);
                ruleInfo.setStartTime(null);
                ruleInfo.setEndTime(null);
                ruleSvc.saveOrUpdate(ruleInfo);
            }
        }
    }

    //执行规则
    private void simulationPerformance(SimulationRuleInfo ruleInfo, Map<String, ESKpiInfo> kpiMap, long curTime) {
        ruleInfo.valid();
        String metric = ruleInfo.getMetric();
        Long startTime = ruleInfo.getStartTime();
        String sendCycle = ruleInfo.getSendCycle();
        Integer dataType = ruleInfo.getDataType();

        Assert.isTrue(dataType == 1 || dataType == 2, "不支持的数据类型");
        long cycle = 60;
        if (sendCycle.endsWith("s")) {
            cycle = Long.parseLong(sendCycle.replace("s", ""));
        } else if (sendCycle.endsWith("m")) {
            cycle = Long.parseLong(sendCycle.replace("m", "")) * 60;
        } else if (sendCycle.endsWith("h")) {
            cycle = Long.parseLong(sendCycle.replace("h", "")) * 60 * 60;
        } else {
            Assert.isTrue(false, "不支持的时间类型");
        }

        ESKpiInfo kpiInfo = kpiMap.get(metric);
        Assert.isTrue(kpiInfo != null, "未匹配到指标，已结束模拟规则[" + ruleInfo.getRuleName() + "]的指标[" + metric + "]推送任务！");
        if (ruleInfo.getEndTime() != null && DateUtil.getSecondTimestamp(new Date(curTime)) > DateUtil.getSecondTimestamp(new Date(ruleInfo.getEndTime()))) {
            throw new MessageException("任务[" + ruleInfo.getRuleName() + "]执行期已过！");
        }
        //判断当前时间是否该发
        if (((DateUtil.getSecondTimestamp(new Date(curTime)) - DateUtil.getSecondTimestamp(new Date(startTime))) % cycle != 0)) {
            return;
        }
        log.info("生成性能数据，当前时间：" +DateUtil.getHHmmss(new Date(curTime)));
        // num表示已经发送数据的次数
        int num = ruleInfo.getIndex();
        String val = getVal(ruleInfo, kpiInfo);
        num++;
        ruleSvc.updateByQuery(QueryBuilders.termQuery("id", ruleInfo.getId()), "ctx._source.index=" + num, true);
        SimulationPerformanceBean perfBean = new SimulationPerformanceBean();
        perfBean.setRuleInfo(ruleInfo);
        perfBean.setKpiInfo(kpiInfo);
        perfBean.setTime(curTime);
        perfBean.setVal(val);
        SIMULATION_PERFORMANCCE_QUEUE.add(perfBean);

    }

    /**
     * 计算告警值
     *
     * @param ruleInfo
     * @param kpiInfo
     * @return
     */
    private String getVal(SimulationRuleInfo ruleInfo, ESKpiInfo kpiInfo) {
        String val = null;
        Integer num = ruleInfo.getIndex();
        Integer dataType = ruleInfo.getDataType();
        String[] split = dataType == 1 ? ruleInfo.getConstraint().split("(?<=\\d)-") : ruleInfo.getConstraint().split(",");
        Assert.isTrue(split.length > 0, "数据范围格式异常");
        try {
            if (dataType == 1) { // 随机
                if (kpiInfo.getValType() == 1) {
                    // 数值型随机
                    Double start = Double.valueOf(split[0]);
                    Double end = Double.valueOf(split[1]);
                    double value = ((double) Math.abs(new Random().nextInt()) % 101) / 100 * (end - start) + start;
                    DecimalFormat df = new DecimalFormat("0.##");
                    df.setRoundingMode(RoundingMode.DOWN);
                    val = df.format(value);
                } else if (kpiInfo.getValType() == 3) {
                    // 枚举型随机
                    int index = 0;
                    if (split.length > 1) {
                        index = new Random().nextInt(split.length);
                    }
                    val = split[index];
                }
            } else if (dataType == 2) { // 序列
                Assert.isTrue(split.length > num || ruleInfo.getIsCycle(),
                        "模拟规则[" + ruleInfo.getRuleName() + "]执行完成，共执行：" + num + "次");
                // index表示发送的数据对应序列的下标
                int index = num % split.length;
                val = split[index];
            }
        } catch (Exception e) {
            Assert.isTrue(false, e.getMessage());
        }
        return val;
    }


    @PostConstruct
    public void workStart() {
        log.info(">>> Simulation rule processor initialized successfully");
        MAIN_EXECUTOR.execute(() -> {
            while (true) {
                try {
                    long executePoolSize = THREAD_POOL_EXECUTOR.getPoolSize();
                    if (executePoolSize < MAX_EXECUTE_POOL_SIZE) {
                        SimulationPerformanceBean performanceBean = SIMULATION_PERFORMANCCE_QUEUE.take();
                        if (performanceBean.getRuleInfo().getRuleType() == 1) {
                            THREAD_POOL_EXECUTOR.execute(() -> buildAndSavePerformances(performanceBean));
                        } else {
                            THREAD_POOL_EXECUTOR.execute(() -> buildAndSaveAlarmBatch(performanceBean));
                        }

                    } else {
                        log.warn(">>> The number of tasks executed by the current simulation rule is full");
                        Thread.sleep(5000);
                    }
                } catch (Exception e) {
                    log.error(">>> Threshold processing exception: ", e);
                }
            }
        });
    }

    private void buildAndSavePerformances(SimulationPerformanceBean bean) {
        SimulationRuleInfo ruleInfo = bean.getRuleInfo();
        ESKpiInfo kpiInfo = bean.getKpiInfo();
        long time = bean.getTime();
        String val = bean.getVal();
        Long classId = ruleInfo.getClassId();
        String metric = kpiInfo.getKpiCode();
        long perfCreateTime = ESUtil.getNumberDateTime(new Date(time));
        // 优先从缓存中取CI数据
        List<ESCIInfo> ciList = this.getSimulationRuleCIInfos(ruleInfo);
        List<FinalPerformanceDTO> saveDtos = new ArrayList<>();
        List<PerformanceDTO> sendDtos = new LinkedList<>();
        try {
            Double value = new BigDecimal(val).doubleValue();
            for (ESCIInfo ci : ciList) {
                if (enableTp) {
                    // 数据发送kafka
                    PerformanceDTO sendDto = new PerformanceDTO();
                    sendDto.setCiCode(ci.getCiCode());
                    sendDto.setCiId(ci.getId());
                    sendDto.setClassId(classId);
                    sendDto.setCiPrimaryKey(ci.getCiPrimaryKey());
                    sendDto.setOriginCiName(ci.getCiCode());
                    sendDto.setInstance("_");
                    sendDto.setKpiClass("_");
                    sendDto.setMetric(metric);
                    sendDto.setValue(val);
                    sendDto.setTimestamp(time);
                    sendDto.setClassName("_");
                    CIObjectDTO ciObject = new CIObjectDTO();
                    ciObject.setClassId(classId);
                    ciObject.setCiCode(ci.getCiCode());
                    ciObject.setHashCode(ci.getHashCode().longValue());
                    ciObject.setCiPrimaryKey(ci.getCiPrimaryKey());
                    ciObject.setId(ci.getId());
                    ciObject.setTime(time);
                    ciObject.setObject(new JSONObject(ci.getAttrs()));
                    sendDto.setCIObject(ciObject);
                    sendDtos.add(sendDto);
                } else {
                    // 直接入库
                    FinalPerformanceDTO saveDTO = new FinalPerformanceDTO();
                    saveDTO.setCiCode(ci.getCiCode());
                    saveDTO.setClassId(ci.getClassId());
                    saveDTO.setInstance("_");
                    saveDTO.setMetric(metric);
                    saveDTO.setUnit(kpiInfo.getUnitName());
                    saveDTO.setTime(time);
                    saveDTO.setValue(value);
                    saveDTO.setDomainId(ci.getDomainId());
                    saveDtos.add(saveDTO);
                }
            }
        } catch (Exception e) {
            log.error("Simulation perf value:{} is not a number", val);
        }
        if (!BinaryUtils.isEmpty(saveDtos) || !BinaryUtils.isEmpty(sendDtos)) {
            Integer size = this.savePerformancesBatch(saveDtos, sendDtos);
            log.info("模拟规则[" + ruleInfo.getRuleName() + "]成功发送" + size + "条数据至" + (enableTp ? "KAFKA" : "数据库") + "时间为:"
                    + perfCreateTime);
        }
    }

    private Integer savePerformancesBatch(List<FinalPerformanceDTO> saveDtos, List<PerformanceDTO> sendDtos) {
        Integer size = 0;
        if (enableTp) {
            // 数据直接发送至kafka
            try {
                MessageQueueProducer messageQueueProducer = SpringUtil.getBean(MessageQueueProducer.class);
                messageQueueProducer.sendMessage(MessageTopicConst.MIDDLE_TP_PERF, GSON.toJson(sendDtos));
                size = sendDtos.size();
            } catch (Exception e) {
                Assert.isTrue(false, "配置异常,数据发送失败");
            }
        } else {
            // 保存性能数据
            performanceSvc.saveOrUpdateBatch(saveDtos);
            size = saveDtos.size();
        }
        return size;
    }

    private List<ESCIInfo> getSimulationRuleCIInfos(SimulationRuleInfo ruleInfo) {
        Long classId = ruleInfo.getClassId();
        List<Long> ciIds = ruleInfo.getCiIds();
        // 优先从缓存中取CI数据
        @Nullable
        List<ESCIInfo> ciList = ES_CI_INFO_CACHE.getIfPresent(ruleInfo.getId());
        if (BinaryUtils.isEmpty(ciList) || ciIds.size() != ciList.size()) {
            BoolQueryBuilder query = QueryBuilders.boolQuery();
            if (classId != null) {
                query.must(QueryBuilders.termQuery("classId", classId));
            }
            if (!BinaryUtils.isEmpty(ciIds)) {
                query.must(QueryBuilders.termsQuery("id", ciIds));
            }
            ciList = ciSvc.getListByQuery(1, 99999, query).getData();
            if (BinaryUtils.isEmpty(ciList)) {
                ruleInfo.setCiIds(new ArrayList<>());
                ruleSvc.updateByQuery(QueryBuilders.termQuery("id", ruleInfo.getId()), "ctx._source.ruleStatus=0",
                        true);
                Assert.isTrue(false,
                        "未匹配到孪生体，已结束模拟规则[" + ruleInfo.getRuleName() + "]的指标[" + ruleInfo.getMetric() + "]推送任务！");
            } else if (ciIds.size() != ciList.size()) {
                List<Long> ids = ciList.stream().map(ESCIInfo::getId).collect(Collectors.toList());
                String idStr = "[" + ids.stream().map(id -> id + "L").collect(Collectors.joining(",")) + "]";
                ruleSvc.updateByQuery(QueryBuilders.termQuery("id", ruleInfo.getId()), "ctx._source.ciIds=" + idStr,
                        true);
            }
            ES_CI_INFO_CACHE.put(ruleInfo.getId(), ciList);
        }
        return ciList;
    }

    //执行告警规则
    private void simulationAlarms(SimulationRuleInfo ruleInfo, Long curTime) {
        String sendCycle = ruleInfo.getSendCycle();
        Integer num = ruleInfo.getIndex();//已经发送的次数
        Long openTime = ruleInfo.getStartTime();
        Long shutTime = ruleInfo.getEndTime();
        boolean openSend = false;
        boolean shutSend = false;
        if (sendCycle.endsWith("h")) {
            //按小时
            if (DateUtil.getmmss(new Date(curTime)).equals(DateUtil.getmmss(new Date(openTime)))) {
                openSend = true;
            }
            if (DateUtil.getmmss(new Date(curTime)).equals(DateUtil.getmmss(new Date(shutTime)))) {
               shutSend = true;
            }
        } else if (sendCycle.endsWith("d")) {
            //按天
            if (DateUtil.getmmss(new Date(curTime)).equals(DateUtil.getHHmmss(new Date(openTime)))) {
                openSend = true;
            }
            if (DateUtil.getmmss(new Date(curTime)).equals(DateUtil.getHHmmss(new Date(shutTime)))) {
                shutSend = true;
            }

        } else {
            Assert.isTrue(false, "不支持的时间类型");
        }
        if (openSend) {
            SimulationPerformanceBean perfBean = new SimulationPerformanceBean();
            perfBean.setVal("true");
            num++;
            perfBean.setRuleInfo(ruleInfo);
            perfBean.setTime(curTime);
            SIMULATION_PERFORMANCCE_QUEUE.add(perfBean);
            ruleSvc.updateByQuery(QueryBuilders.termQuery("id", ruleInfo.getId()), "ctx._source.index=" + num, true);
        }
        if (shutSend) {
            SimulationPerformanceBean perfBean = new SimulationPerformanceBean();
            perfBean.setVal("false");
            num++;
            perfBean.setRuleInfo(ruleInfo);
            perfBean.setTime(curTime);
            SIMULATION_PERFORMANCCE_QUEUE.add(perfBean);
            ruleSvc.updateByQuery(QueryBuilders.termQuery("id", ruleInfo.getId()), "ctx._source.index=" + num, true);
        }
    }


    private void buildAndSaveAlarmBatch(SimulationPerformanceBean bean) {
        SimulationRuleInfo ruleInfo = bean.getRuleInfo();
        List<Long> ciIds = ruleInfo.getCiIds();
        Long alarmCode = ruleInfo.getAlarmCode();
        List<ESAlarm> saveBeans = new ArrayList<>();
        for (int i = 0; i < ciIds.size(); i++) {

            Long ciId = ciIds.get(i);
            ESAlarm alarm = BeanUtil.converBean(ruleInfo, ESAlarm.class);
            if ("true".equals(bean.getVal())) {
                alarm.setStatus(0);
            }else {
                alarm.setStatus(1);
            }
            alarm.setAlarmTime(bean.getTime());
            alarm.setKpiName(ruleInfo.getMetric());
            alarm.setAlarmCode(alarmCode + i);
            alarm.setAlarmObjId(ciId);
            alarm.setUniqueKey(ciId + ruleInfo.getMetric());
            saveBeans.add(alarm);
        }

        //发送kafka数据
        List<Event2KafkaDTO> sendDtos = new ArrayList<>();
        //历史告警数据（mon_eap_event_all_YYYYMM）
        JSONArray eventHistory = new JSONArray();
        //当前告警数据（event）
        List<JSONObject> events = new ArrayList<>();

        saveBeans.stream().forEach(saveBean -> {
            if (saveBean.getDomainId() == null) {
                saveBean.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
            }
            Long domainId = saveBean.getDomainId();
            Long severityId = saveBean.getSeverityId();
            Assert.notNull(severityId, "告警级别不得为空");
            ESDictionaryItemInfo item = dictItemSvc.getById(severityId);
            ESMonSysSeverityInfo severityInfo = eventService.transMonSeverityInfoToDictItem(item);
            Assert.notNull(severityInfo, "告警级别不存在");
            saveBean.setSeverityLevel(severityInfo.getSeverity());

            //组装告警事件
            ESMonEapEvent monEapEvent = new ESMonEapEvent();
            eventService.initMonEapEvent(monEapEvent, saveBean);
            eventService.addDefaultValue(monEapEvent);

            CcCiInfo ci = null;
            //对象告警
            if (saveBean.getAlarmObjType() == 0) {
                ci = ciSvc.getCiInfoById(saveBean.getAlarmObjId());
                if (!BinaryUtils.isEmpty(ci)) {
                    monEapEvent.setSourceCiName(JSONArray.parseArray(ci.getCi().getCiPrimaryKey()).stream().map(e -> e.toString()).collect(Collectors.joining(",")));
                    String ciCode = ci.getCi().getCiCode();
                    monEapEvent.setCustomCiName(ciCode);
                    monEapEvent.setCiPrimaryKey(ci.getCi().getCiPrimaryKey());
                } else {
                    monEapEvent.setSourceCiName(saveBean.getAlarmObjId().toString());
                }
                ESCIClassInfo classInfo = esciClassSvc.getById(saveBean.getClassId());
                if (!BinaryUtils.isEmpty(classInfo)) {
                    monEapEvent.setCiCategoryName(classInfo.getClassName());
                }
            } else if (saveBean.getAlarmObjType() == 1) {//关系告警
                monEapEvent.setSourceCiName(saveBean.getAlarmObjId().toString());
                CcCiClassInfo ccCiClassInfo = rltClassSvc.queryClassById(saveBean.getAlarmObjId());
                if ((!BinaryUtils.isEmpty(ccCiClassInfo))) {
                    monEapEvent.setCiCategoryName(ccCiClassInfo.getCiClass().getClassName());
                }
            }

            if (epExist) {
                //发送kafka的数据准备
                Event2KafkaDTO sendDto = new Event2KafkaDTO();
                sendDto.setStatus(monEapEvent.getStatus());
                sendDto.setSourceAlertKey(monEapEvent.getSourceAlertKey());
                sendDto.setSourceSeverity(monEapEvent.getSourceSeverity());
                sendDto.setSourceID(monEapEvent.getSourceId());
                sendDto.setSourceCIName(monEapEvent.getSourceCiName());
                sendDto.setSourceIdentifier(monEapEvent.getSourceIdentifier());
                sendDto.setSummary(monEapEvent.getSummary());
                sendDto.setSeverity(monEapEvent.getSeverity());
                sendDto.setSourceEventID(monEapEvent.getSourceEventId());
                sendDto.setLastOccurrence(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(System.currentTimeMillis()));
                sendDto.setTimestamp(String.valueOf(new Date().getTime()));
                if (!BinaryUtils.isEmpty(ci)) {
                    Event2KafkaDTO.CIObjectDTO ciObject = new Event2KafkaDTO.CIObjectDTO();
                    ciObject.setClassId(ci.getCi().getClassId());
                    ciObject.setCiCode(ci.getCi().getCiCode());
                    ciObject.setHashCode(ci.getCi().getHashCode().longValue());
                    ciObject.setCiPrimaryKey(ci.getCi().getCiPrimaryKey());
                    ciObject.setClassName(ci.getCiClass().getClassName());
                    ciObject.setId(ci.getCi().getId());
                    Map<String, String> ciSourceAttr = ci.getAttrs();
                    ciObject.setObject(JSONObject.parseObject(JSONObject.toJSONString(ciSourceAttr)));
                    sendDto.setCIObject(ciObject);
                }
                sendDtos.add(sendDto);
            } else {
                if (BinaryUtils.isEmpty(monEapEvent.getId())) {
                    monEapEvent.setId(String.valueOf(ESUtil.getUUID()));
                    monEapEvent.setDuplicateSerial(monEapEvent.getId());
                    monEapEvent.setSerial(monEapEvent.getId());
                    monEapEvent.setSummary(saveBean.getDesc());
                    monEapEvent.setKpiName(saveBean.getKpiName());
                    monEapEvent.setCiName(monEapEvent.getCustomCiName());
                    monEapEvent.setSeverity(saveBean.getSeverityLevel());
                    monEapEvent.setCiCategoryId(saveBean.getClassId().toString());
                }
                JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(monEapEvent));
                //格式化事件为时间戳
                jsonObject.put("firstOccurrence", monEapEvent.getFirstOccurrence().getTime());
                jsonObject.put("lastOccurrence", monEapEvent.getLastOccurrence().getTime());
                //无EP时，模拟告警直接保存到历史表中
                eventHistory.add(jsonObject);
                //更新已存在告警
                String ciName = monEapEvent.getCustomCiName();
                String kpiName = monEapEvent.getKpiName();
                BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
                queryBuilder.filter(QueryBuilders.termQuery("domainId", domainId));
                queryBuilder.filter(QueryBuilders.termQuery("ciName.keyword", ciName));
                queryBuilder.filter(QueryBuilders.termQuery("kpiName.keyword", kpiName));
                List<JSONObject> oldEventList = esEventSvc.getListByQuery(1, 1, queryBuilder).getData();
                if (oldEventList.size() > 0) {
                    JSONObject oldEvent = oldEventList.get(0);
                    String id = oldEvent.getString("id");
                    monEapEvent.setId(id);
                }
                events.add(JSONObject.parseObject(JSONObject.toJSONString(monEapEvent)));

            }

        });

        if (epExist) {
            //批量发送数据
            try {
                MessageQueueProducer messageQueueProducer = SpringUtil.getBean(MessageQueueProducer.class);
                messageQueueProducer.sendMessage(MessageTopicConst.EVENT_QUEUE, GSON.toJson(sendDtos));
                log.debug("kafka send data:" + GSON.toJson(sendDtos));
            } catch (Exception e) {
                Assert.isTrue(false, "配置异常，数据发送失败");
            }
        } else {
            if (enableSaveEventHis) {
                //保存历史告警
                eventHistoryDao.saveOrUpdateBatchNoRefresh(eventHistory);

            }
            //保存当前告警
            eventService.saveCurrentEventBatch(events);
        }

        //保存告警推送记录
        esAlarmSvc.saveOrUpdateBatch(saveBeans);

    }
}