package com.uino.dao.license;

import java.util.List;


import org.elasticsearch.index.query.BoolQueryBuilder;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import com.binary.framework.exception.ServiceException;
import com.uinnova.product.vmdb.comm.model.license.CCcLicenseAuthServer;
import com.uinnova.product.vmdb.comm.model.license.CcLicenseAuthServer;
import com.uino.dao.AbstractESBaseDao;
import com.uino.dao.ESConst;
import com.uino.dao.util.ESUtil;

import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
@Slf4j
public class ESLicenseAuthServerSvc extends AbstractESBaseDao<CcLicenseAuthServer, CCcLicenseAuthServer> {

	public static final Long LICENSE_AUTH_ID = 1L;

    @Override
    public String getIndex() {
        return ESConst.INDEX_LICENSE_AUTH_SERVER;
    }

    @Override
    public String getType() {
        return ESConst.INDEX_LICENSE_AUTH_SERVER;
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }

    @Override
    public Long saveOrUpdate(CcLicenseAuthServer record) {
        Assert.notNull(record, "BS_FIELD_EMPTY_VAL${field:recor}");
		record.setAuthId(ESLicenseAuthServerSvc.LICENSE_AUTH_ID);
        Assert.notNull(record.getServerCode(), "BS_FIELD_EMPTY_VAL${field:serverCode}");
        CcLicenseAuthServer server = selectLicenseAuthServerByCode(record.getServerCode());
        record.setId(server.getId());
        return super.saveOrUpdate(record);
    }

    protected CcLicenseAuthServer selectLicenseAuthServerByCode(String code) {
        CCcLicenseAuthServer cdt = new CCcLicenseAuthServer();
        cdt.setServerCode(code);
        BoolQueryBuilder query = ESUtil.cdtToBuilder(cdt);
        List<CcLicenseAuthServer> list = super.getListByQuery(query);
        if (list.size() == 0) {
            try {
                CcLicenseAuthServer record = new CcLicenseAuthServer();
				record.setAuthId(ESLicenseAuthServerSvc.LICENSE_AUTH_ID);
                record.setServerCode(code);
                super.saveOrUpdate(record);
            } catch (Exception e) {
                log.error(" insert license auth server error! ", e);
            }
            List<CcLicenseAuthServer> listTemp = super.getListByQuery(query);
            if (listTemp.size() == 0) {
                throw new ServiceException(" not found license auth server by code '" + code + "'! ");
            }
            list = listTemp;
        }
        return list.get(0);
    }
}
