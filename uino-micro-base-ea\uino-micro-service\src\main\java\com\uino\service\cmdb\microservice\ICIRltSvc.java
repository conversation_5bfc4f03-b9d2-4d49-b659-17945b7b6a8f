package com.uino.service.cmdb.microservice;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import org.springframework.web.multipart.MultipartFile;

import com.binary.core.io.Resource;
import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.provider.rlt.bean.CcCiRltInfo;
import com.uino.bean.cmdb.base.ESCIClassInfo;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.base.ESCIRltInfo;
import com.uino.bean.cmdb.base.ESCIRltInfoHistory;
import com.uino.bean.cmdb.business.BindCiRltRequestDto;
import com.uino.bean.cmdb.business.DataModuleRltClassDto;
import com.uino.bean.cmdb.business.ImportExcelMessage;
import com.uino.bean.cmdb.business.ImportResultMessage;
import com.uino.bean.cmdb.query.ESAttrAggBean;
import com.uino.bean.cmdb.query.ESRltSearchBean;

/**
 * ci关系相关服务类
 * 
 * <AUTHOR>
 *
 */
public interface ICIRltSvc {

    /**
     *
     * @param bindCiRltRequestDto
     * @return
     */
    public Long bindCiRlt(BindCiRltRequestDto bindCiRltRequestDto);

    /**
     * @see ICIRltSvc#bindCiRlts(Set, boolean)
     * @param bindRltDtos
     * @return
     */
    public default ImportResultMessage bindCiRlts(Long domainId, Set<BindCiRltRequestDto> bindRltDtos) {
        return bindCiRlts(domainId, bindRltDtos, false);
    }

    /**
     * @see ICIRltSvc#bindCiRlts(Set, boolean, Map, Map)
     * @param bindRltDtos
     * @param repeat
     * @return
     */
    public default ImportResultMessage bindCiRlts(Long domainId, Set<BindCiRltRequestDto> bindRltDtos, boolean repeat) {
        return bindCiRlts(domainId, bindRltDtos, repeat, null, null);
    }

    /**
     * 批量绑定ci关系
     * 
     * @param bindRltDtos
     *            绑定dtos
     * @param repeat
     *            是否覆盖数据
     * @param idCiMap
     *            dtos中所用所有{ciId:ESCIInfo}字典,为空则主动获取
     * @param idRltClsMap
     *            dtos中所用所有{rltClsId:ESCIClassInfo}字典，为空则主动获取
     * @return
     */
    public ImportResultMessage bindCiRlts(Long domainId, Set<BindCiRltRequestDto> bindRltDtos, boolean repeat,
            Map<Long, ESCIInfo> idCiMap, Map<Long, ESCIClassInfo> idRltClsMap);

    /**
     * 根据ciId解除ci关系（无论是目标还是源都解除）
     * 
     * @param ciId
     *            源/目标ciId
     * @return
     */
    public Integer delRltByCiId(Long ciId);

    /**
     * 根据ciIds解除关系（无论是目标还是源都解除）
     * 
     * @param ciIds
     * @return
     */
    public Integer delRltByCiIds(Collection<Long> ciIds);

    /**
     * 根据CiClassId解除CI关系（无论是目标还是源都解除）
     * 
     * @param classId
     * @return
     */
    public Integer delRltByCiClassId(Long classId);

    /**
     * 删除关系
     * 
     * @param rltClsIds
     *            关系分类ids
     * @param sourceCIClsIds
     *            源ci分类ids
     * @param targetCIClsIds
     *            目标ci分类ids
     * @return
     */
    public Integer delRlts(Collection<Long> rltClsIds, Collection<Long> sourceCIClsIds,
            Collection<Long> targetCIClsIds);


    /**
     * 根据【关系ids OR 关系codes】解除ci关系
     * 
     * @param rltIds
     * @param rltCodes
     * @param ownerCode
     * @return
     */
    public Integer delRltByIdsOrRltCodes(Collection<Long> rltIds, Collection<String> rltCodes, String ownerCode);

    /**
     * 修改ci关系信息(只能修改属性信息)
     * 
     * @param ciRltId
     *            ci关系Id
     * @param attrs
     *            属性信息
     * @return
     */
    public Long updateCiRltAttr(Long ciRltId, Map<String, String> attrs);

    /**
     * @see ICIRltSvc#searchRlt(ESRltSearchBean)
     *      <p>
     *      对ICIRltSvc#searchRlt基础上增加了信息丰富
     * @param bean
     * @return
     */
    public Page<CcCiRltInfo> searchRltByBean(ESRltSearchBean bean);


    List<CcCiRltInfo> searchRltByScroll(ESRltSearchBean bean);

    /**
     * 分页查询ci关系（带条件）
     * 
     * @param bean
     * @return
     */
    public Page<ESCIRltInfo> searchRlt(ESRltSearchBean bean);

    /**
     * 根据ids查询ci关系
     * 
     * @param ids
     * @return
     */
    public List<CcCiRltInfo> searchRltByIds(Set<Long> ids);

    /**
     * 清除某个关系分类下所有ci关系
     * 
     * @param rltClassId
     * @return
     */
    public default Integer clearRltByClassId(Long rltClassId) {
        return delRlts(Arrays.asList(rltClassId), null, null);
    }

    /**
     * 清除当前用户关系分类下所有ci关系
     *
     * @param rltClassId
     * @return
     */
    public default Integer clearUserRltByClassId(Long rltClassId,String ownerCode) {
        return delUserRlts(Arrays.asList(rltClassId), ownerCode,null, null);
    }

    public Integer delUserRlts(Collection<Long> rltClsIds,String ownerCode, Collection<Long> sourceCIClsIds,
                           Collection<Long> targetCIClsIds);




    /**
     * 导出ci关系
     */
    Resource exportCiRlt(ESRltSearchBean bean);

    /**
     * 导入ci关系
     * 
     * @param excelFilePath
     *            excel文件在本地文件系统位置(若传入了excel文件流则已文件流为准)
     * @param excelFile
     *            excel文件流
     * @param rltClsCodes
     *            导入的关系分类(与sheet对应)
     * @return
     */
    public ImportResultMessage importCiRlt(Long domainId, String excelFilePath, MultipartFile excelFile, Set<String> rltClsCodes);

    /**
     * 解析关系excel
     * 
     * @param excelFile
     *            待解析excel
     * @return {关系分类code:是否已存在}
     */
    public Map<String, Boolean> comprehendRltExcel(MultipartFile excelFile);

    /**
     * 解析关系excel
     * 
     * @param excelFilePath
     * @param excelFile
     * @return
     */
    public ImportExcelMessage parseRltExcel(String excelFilePath, MultipartFile excelFile);

    /**
     * 类sql: select fieldName from table group by fieldName where *****
     * 
     * @param req
     *            条件
     * @return
     */
    public Page<String> groupByField(Long domainId, ESAttrAggBean req);

    /**
     * 根据分类筛选条件获取分类关系map
     *
     * @param clsIds
     *            ci分类ids
     * @param rltClsIds
     *            关系分类ids
     * @return {源分类id:{关系分类id:[目标分类ids]}}
     */
    public Map<Long, Map<Long, Set<Long>>> getClassRltMapByClsQuery(Set<Long> clsIds, Set<Long> rltClsIds);
    // ----------------------关系历史相关------------------------------

    /**
     * 获取{ciRltId:对应历史版本信息s}字典
     * 
     * @param rltIds
     *            需组装得关系历史字典得关系ids
     * @param hasCurrent
     *            是否需包含当前版本
     * @return {ciRltId:对应历史版本信息s}字典
     */
    public Map<Long, List<ESCIRltInfoHistory>> getRltsHistrysDict(Set<Long> rltIds, boolean hasCurrent);


    /**
     * 获取{rltId：maxVersion}键值对
     *
     * @param rltIds
     *            if null:获取全部rltid：maxversion键值对 ，if notnull:仅获取指定rltid
     * @return {rltid：maxVersion}
     */
    Map<Long, Long> getRltIdMaxVersion(Set<Long> rltIds);

    /**
     * 根据分类筛选条件获取分类关系map
     *
     * @param classIds ci分类ids
     * @param rltClsIds 关系分类ids
     * @return 元模型上使用包含起始分类和目标分类的关系对象信息
     */
    List<DataModuleRltClassDto> getClassRltList(Set<Long> classIds, Set<Long> rltClsIds);

    /**
     * 获取所有关系分类
     *
     * @param
     * @param
     */
    List<CcCiClassInfo> queryAllClasses(Long domainId);
}
