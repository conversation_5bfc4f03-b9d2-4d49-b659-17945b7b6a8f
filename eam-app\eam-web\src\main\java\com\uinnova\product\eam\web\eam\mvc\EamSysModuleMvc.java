package com.uinnova.product.eam.web.eam.mvc;

import com.binary.framework.util.ControllerUtils;
import com.uinnova.product.eam.model.dto.MatrixStencilSortInfoDTO;
import com.uinnova.product.eam.model.dto.SysArtifactVo;
import com.uinnova.product.eam.model.dto.SysModelVo;
import com.uinnova.product.eam.model.dto.SysTemplateDto;
import com.uinnova.product.eam.service.IModuleSvc;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uino.bean.permission.base.SysModule;
import com.uino.init.api.ApiResult;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.List;

@RestController
@Api(value = "系统菜单", tags = {"系统菜单"})
@RequestMapping(value = "/eam/module")
public class EamSysModuleMvc {

    @Autowired
    private IModuleSvc moduleSvc;


    @PostMapping("saveModule")
    @ModDesc(desc = "保存系统模块", pDesc = "系统模块对象", pType = SysModule.class, rDesc = "系统模块数据", rType = SysModule.class)
    public ApiResult<SysModule> saveModule(@RequestBody SysModule saveDto, HttpServletRequest request, HttpServletResponse response) {
        SysModule res = moduleSvc.eamSaveModule(saveDto);
        return ApiResult.ok(this).data(res);
    }

    @GetMapping("/deleteAssertDir")
    public void deleteAssertDir(HttpServletRequest request, HttpServletResponse response,@RequestParam Long assertDirId) {
        Boolean result = moduleSvc.deleteAssertDir(assertDirId);
        ControllerUtils.returnJson(request, response, result);
    }

    @GetMapping("queryDiagramType")
    @ModDesc(desc = "查询发布的视图类型", pDesc = "", pType = SysModule.class, rDesc = "视图类型分组", rType = SysModule.class)
    public void queryDiagramType(HttpServletRequest request, HttpServletResponse response) {
        List<SysArtifactVo>  s = moduleSvc.queryDiagramType();
        ControllerUtils.returnJson(request, response, s);
    }

    @GetMapping("queryModuleType")
    @ModDesc(desc = "查询发布的模型类型", pDesc = "", pType = SysModule.class, rDesc = "模型类型分组", rType = SysModule.class)
    public void queryModuleType(HttpServletRequest request, HttpServletResponse response) {
        List<SysModelVo>  s = moduleSvc.queryModuleType();
        ControllerUtils.returnJson(request, response, s);
    }

    @GetMapping("querySchemeType")
    @ModDesc(desc = "查询发布的模型类型", pDesc = "", pType = SysModule.class, rDesc = "方案类型分组", rType = SysModule.class)
    public void querySchemeType(HttpServletRequest request, HttpServletResponse response) {
        List<SysTemplateDto>  s = moduleSvc.querySchemeType();
        ControllerUtils.returnJson(request, response, s);
    }

    @GetMapping("/queryMatrixType")
    @ModDesc(desc = "查询发布的矩阵类型", pType = SysModule.class, pDesc = "", rDesc = "矩阵类型分组信息", rType = List.class)
    public void queryMatrixType(HttpServletRequest request, HttpServletResponse response) {
        List<MatrixStencilSortInfoDTO>  matrixResult = moduleSvc.queryMatrixType();
        ControllerUtils.returnJson(request, response, matrixResult);
    }
}
