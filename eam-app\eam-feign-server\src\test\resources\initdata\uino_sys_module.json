[{"createTime": 20200109105839, "creator": "admin", "customColor": "#D90000", "domainId": 1, "id": 1001, "isInit": true, "label": "对象管理", "modifier": "system", "modifyTime": 20200707145243, "moduleCode": "1001", "moduleImg": "uino uino-set-oprations", "moduleName": "对象管理", "moduleUrl": "/newci", "orderNo": 2, "parentId": 1000}, {"createTime": 20200109102552, "creator": "admin", "domainId": 1, "id": 1000, "isInit": true, "label": "公共组件", "modifier": "system", "modifyTime": 20200707145243, "moduleCode": "10", "moduleName": "公共组件", "moduleSign": "COMM", "orderNo": 6, "parentId": 0}, {"createTime": 20200111135130, "creator": "admin", "customColor": "#FF7718", "domainId": 1, "id": 1002, "isInit": true, "label": "关系管理", "modifier": "system", "modifyTime": 20200707145243, "moduleCode": "1002", "moduleImg": "uino uino-layout-circle-color", "moduleName": "关系管理", "moduleUrl": "/relation", "orderNo": 7, "parentId": 1000}, {"createTime": 20200111135130, "creator": "admin", "customColor": "#B9FFCD", "domainId": 1, "id": 1003, "isInit": true, "label": "标签管理", "modifier": "system", "modifyTime": 20200707145243, "moduleCode": "1003", "moduleImg": "ts ts-envamables", "moduleName": "标签管理", "moduleUrl": "/tag", "orderNo": 9, "parentId": 1000}, {"createTime": 20200111135238, "creator": "admin", "customColor": "#E9C690", "domainId": 1, "id": 1004, "isInit": true, "label": "图标管理", "modifier": "system", "modifyTime": 20200707145243, "moduleCode": "1004", "moduleImg": "uino uino-application", "moduleName": "图标管理", "moduleUrl": "/icon", "orderNo": 10, "parentId": 1000}, {"createTime": 20200109105839, "creator": "admin", "customColor": "#D90000", "domainId": 1, "id": 2004, "isInit": true, "label": "数据超市", "modifier": "system", "modifyTime": 20200707145243, "moduleCode": "2004", "moduleImg": "uino uino-cart", "moduleName": "数据超市", "moduleUrl": "/dataStore", "orderNo": 11, "parentId": 1000}, {"createTime": 20200111135203, "creator": "admin", "customColor": "#F5E304", "domainId": 1, "id": 1005, "isInit": true, "label": "用户管理", "modifier": "system", "modifyTime": 20200707145243, "moduleCode": "1005", "moduleImg": "uino uino-groupUser", "moduleName": "用户管理", "moduleUrl": "/user", "orderNo": 12, "parentId": 1000}, {"createTime": 20200111135224, "creator": "admin", "customColor": "#E7B6AC", "domainId": 1, "id": 1006, "isInit": true, "label": "权限管理", "modifier": "system", "modifyTime": 20200707145243, "moduleCode": "1006", "moduleImg": "uino uino-layout", "moduleName": "权限管理", "moduleUrl": "/authority_new", "orderNo": 13, "parentId": 1000}, {"createTime": 20200111135144, "creator": "admin", "customColor": "#F7AE20", "domainId": 1, "id": 1008, "isInit": true, "label": "组织管理", "modifier": "system", "modifyTime": 20200707145243, "moduleCode": "1008", "moduleImg": "uino uino-relationship", "moduleName": "组织管理", "moduleUrl": "/org", "orderNo": 14, "parentId": 1000}, {"createTime": 20200111135144, "creator": "admin", "customColor": "#58DEE5", "domainId": 1, "id": 1009, "isInit": true, "label": "树状图管理", "modifier": "system", "modifyTime": 20200707145243, "moduleCode": "1009", "moduleImg": "ts ts-envamables", "moduleName": "树状图管理", "moduleUrl": "/tree", "orderNo": 15, "parentId": 1000}, {"createTime": 20200111135340, "creator": "admin", "customColor": "#A3E558", "domainId": 1, "id": 1010, "isInit": true, "label": "系统设置", "modifier": "system", "modifyTime": 20200707145243, "moduleCode": "1010", "moduleImg": "uino uino-set-config", "moduleName": "系统设置", "moduleUrl": "/system", "orderNo": 17, "parentId": 1000}, {"createTime": 20200111135256, "creator": "admin", "customColor": "#206000", "domainId": 1, "id": 1011, "isInit": true, "label": "个人设置", "modifier": "system", "modifyTime": 20200707145243, "moduleCode": "1011", "moduleImg": "uino uino-form-pen", "moduleName": "个人设置", "moduleUrl": "/personal", "orderNo": 18, "parentId": 1000}, {"createTime": 20200111135327, "creator": "admin", "customColor": "#2FBC57", "domainId": 1, "id": 1012, "isInit": true, "label": "更新日志", "modifier": "system", "modifyTime": 20200707145243, "moduleCode": "1012", "moduleImg": "uino uino-note", "moduleName": "更新日志", "moduleUrl": "/sysUpdateLog", "orderNo": 19, "parentId": 1000}, {"createTime": 20200111135327, "creator": "admin", "customColor": "#2FBC57", "domainId": 1, "id": 1013, "isInit": true, "label": "日志管理", "modifier": "system", "modifyTime": 20200707145243, "moduleCode": "1013", "moduleImg": "uino uino-note", "moduleName": "日志管理", "moduleUrl": "/log", "orderNo": 20, "parentId": 1000}, {"createTime": 20200111135256, "creator": "admin", "customColor": "#206000", "domainId": 1, "id": 1014, "isInit": true, "label": "LDAP集成", "modifier": "system", "modifyTime": 20200707145243, "moduleCode": "1014", "moduleImg": "uino uino-integration-ldap", "moduleName": "LDAP集成", "moduleUrl": "/ldap", "orderNo": 21, "parentId": 1000}, {"createTime": 20200111135256, "creator": "admin", "customColor": "#206000", "domainId": 1, "id": 1016, "isInit": true, "label": "授权管理", "modifier": "system", "modifyTime": 20200707145243, "moduleCode": "1016", "moduleImg": "uino uino-integration-ldap", "moduleName": "授权管理", "moduleUrl": "/license", "orderNo": 23, "parentId": 1000}, {"id": 5432333084650458, "moduleCode": "5432333084650457", "moduleName": "QuickEA", "moduleSign": "QuickEA", "label": "QuickEA", "parentId": 0, "orderNo": 2, "isInit": true, "domainId": 1, "creator": "admin", "modifier": "admin", "createTime": 20200713151843, "modifyTime": 20200817162518}, {"id": 5802437402464206, "moduleCode": "5802437402464205", "moduleName": "工作台", "moduleSign": "工作台", "label": "工作台", "parentId": 5432333084650458, "orderNo": 18, "moduleUrl": "/instrument-panel", "isInit": true, "domainId": 1, "creator": "admin", "modifier": "admin", "createTime": 20200814153541, "modifyTime": 20200817173234, "moduleImg": "ts ts-envamables", "customColor": "#A3E558"}, {"id": 5594743278502593, "moduleCode": "5594743278502592", "moduleName": "架构视图", "moduleSign": "架构视图", "label": "架构视图", "parentId": 5432333084650458, "orderNo": 19, "moduleUrl": "/workbench", "isInit": true, "domainId": 1, "creator": "admin", "modifier": "admin", "createTime": 20200814153541, "modifyTime": 20200817173234, "moduleImg": "ts ts-envamables", "customColor": "#FF7718"}, {"id": 5594743278502614, "moduleCode": "5594743278502613", "moduleName": "Dashboard", "moduleSign": "Dashboard", "label": "Dashboard", "parentId": 5432333084650458, "orderNo": 20, "moduleUrl": "/dashboard", "isInit": true, "domainId": 1, "creator": "admin", "modifier": "admin", "createTime": 20200814153602, "modifyTime": 20200817173234, "moduleImg": "ts ts-envamables", "customColor": "#D90000"}, {"id": 5594743278502633, "moduleCode": "5594743278502632", "moduleName": "架构愿景", "moduleSign": "架构愿景", "label": "架构愿景", "parentId": 5432333084650458, "orderNo": 21, "moduleUrl": "/framework-vision", "isInit": true, "domainId": 1, "creator": "admin", "modifier": "admin", "createTime": 20200814153631, "modifyTime": 20200817173234, "moduleImg": "ts ts-envamables", "customColor": "#F7AE20"}, {"id": 5594743278502650, "moduleCode": "5594743278502649", "moduleName": "业务架构", "moduleSign": "业务架构", "label": "业务架构", "parentId": 5432333084650458, "orderNo": 22, "moduleUrl": "/business-framework", "isInit": true, "domainId": 1, "creator": "admin", "modifier": "admin", "createTime": 20200814153703, "modifyTime": 20200817173234, "moduleImg": "ts ts-envamables", "customColor": "#F5E304"}, {"id": 5594743278502670, "moduleCode": "5594743278502669", "moduleName": "应用架构", "moduleSign": "应用架构", "label": "应用架构", "parentId": 5432333084650458, "orderNo": 23, "moduleUrl": "/application-framework", "isInit": true, "domainId": 1, "creator": "admin", "modifier": "admin", "createTime": 20200814153720, "modifyTime": 20200817173234, "moduleImg": "ts ts-envamables", "customColor": "#E7B6AC"}, {"id": 5594743278502680, "moduleCode": "5594743278502679", "moduleName": "数据架构", "moduleSign": "数据架构", "label": "数据架构", "parentId": 5432333084650458, "orderNo": 24, "moduleUrl": "/data-framework", "isInit": true, "domainId": 1, "creator": "admin", "modifier": "admin", "createTime": 20200814153740, "modifyTime": 20200817173234, "moduleImg": "ts ts-envamables", "customColor": "#E9C690"}, {"id": 5739834866671916, "moduleCode": "5739834866671915", "moduleName": "技术架构", "moduleSign": "技术架构", "label": "技术架构", "parentId": 5432333084650458, "orderNo": 25, "moduleUrl": "/technology", "isInit": true, "domainId": 1, "creator": "admin", "modifier": "admin", "createTime": 20200814153541, "modifyTime": 20200817173234, "moduleImg": "ts ts-envamables", "customColor": "#2FBC57"}, {"id": 5827345292081971, "moduleCode": "5827345292081970", "moduleName": "规范管理", "moduleSign": "规范管理", "label": "规范管理", "parentId": 5432333084650458, "orderNo": 26, "moduleUrl": "/standard", "isInit": true, "domainId": 1, "creator": "admin", "modifier": "admin", "createTime": 20200814153541, "modifyTime": 20200817173234, "moduleImg": "ts ts-envamables", "customColor": "#B9FFCD"}, {"id": 5594743278502702, "moduleCode": "5594743278502701", "moduleName": "元模型", "moduleSign": "元模型", "label": "元模型", "parentId": 5432333084650458, "orderNo": 27, "moduleUrl": "/unit-modal", "isInit": true, "domainId": 1, "creator": "admin", "modifier": "admin", "createTime": 20200814153804, "modifyTime": 20200817173234, "moduleImg": "ts ts-envamables", "customColor": "#206000"}]