package com.uinnova.product.vmdb.comm.rest.support;

import com.binary.core.io.Resource;
import com.binary.core.io.ResourceResolver;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.support.RootBeanDefinition;
import org.springframework.beans.factory.xml.ParserContext;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.core.io.support.PropertiesLoaderSupport;
import org.springframework.util.PropertyPlaceholderHelper;
import org.springframework.util.PropertyPlaceholderHelper.PlaceholderResolver;
import org.springframework.util.StringValueResolver;

import java.io.InputStream;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Properties;

/**
 * 
 * <AUTHOR>
 *
 */
public class RestConsumerClientManager {
    private static final Logger logger = LoggerFactory.getLogger(RestConsumerClientManager.class);

    public static class RestConsumerClientManagerInitialization implements ApplicationContextAware, InitializingBean {
        private ApplicationContext springContext;

        @Override
        public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
            this.springContext = applicationContext;
        }

        @Override
        public void afterPropertiesSet() throws Exception {
            getInstance().parseRestConsumerClientUrl(springContext);
        }
    }

    public static class PlaceholderResolvingStringValueResolver implements StringValueResolver {

        private final PropertyPlaceholderHelper helper;

        private final PlaceholderResolver resolver;

        public PlaceholderResolvingStringValueResolver(Properties props) {
            this.helper = new PropertyPlaceholderHelper("${", "}", ":", false);
            this.resolver = new PropertyPlaceholderConfigurerResolver(props);
        }

        public String resolveStringValue(String strVal) throws BeansException {
            String value = this.helper.replacePlaceholders(strVal, this.resolver);
            return (value.equals(null) ? null : value);
        }
    }

    public static class PropertyPlaceholderConfigurerResolver implements PlaceholderResolver {

        private final Properties props;

        private PropertyPlaceholderConfigurerResolver(Properties props) {
            this.props = props;
        }

        public String resolvePlaceholder(String placeholderName) {
            String value = props.getProperty(placeholderName);
            if (value == null) {
                value = System.getProperty(placeholderName);
            }
            if (value == null) {
                value = System.getenv(placeholderName);
            }
            return value;
        }
    }

    private static RestConsumerClientManager instance;

    private boolean registered = false;
    private final List<RestConsumerClient> clients = new ArrayList<RestConsumerClient>();

    public synchronized void addClient(RestConsumerClient client) {
        this.clients.add(client);
    }

    public synchronized void parseRestConsumerClientUrl(ApplicationContext springContext) {
        Properties pros = loadSpringProperties(springContext);
        if (pros == null) {
            pros = loadProjectProperties(springContext);
        }

        if (pros != null) {
            StringValueResolver valueResolver = new PlaceholderResolvingStringValueResolver(pros);
            Iterator<RestConsumerClient> itor = this.clients.iterator();
            while (itor.hasNext()) {
                RestConsumerClient client = itor.next();
                client.init(valueResolver);
            }
        }
    }

    protected Properties loadSpringProperties(ApplicationContext springContext) {
        try {
            logger.info(" start load spring properties ... ");
            PropertiesLoaderSupport propertyPlaceholder = springContext.getBean(PropertiesLoaderSupport.class);

            Method method = PropertiesLoaderSupport.class.getDeclaredMethod("mergeProperties", (Class<?>[]) null);
            if (!Modifier.isPublic(method.getModifiers())) {
                method.setAccessible(true);
            }

            return (Properties) method.invoke(propertyPlaceholder, (Object[]) null);
        } catch (Throwable t) {
            logger.warn(" load spring properties failed! " + t.getMessage());
        }
        return null;
    }

    protected Properties loadProjectProperties(ApplicationContext springContext) {
        try {
            logger.info(" start load project properties ... ");
            Resource rs = ResourceResolver.getResource("classpath:project.properties");
            InputStream is = null;
            try {
                is = rs.getInputStream();
                Properties pros = new Properties();
                pros.load(is);
                return pros;
            } finally {
                if (is != null) {
                    is.close();
                }
            }

        } catch (Throwable t) {
            logger.warn(" load project properties failed! " + t.getMessage());
        }
        return null;
    }

    public synchronized void register2SpringContext(ParserContext parserContext) {
        if (registered) {
            return;
        }

        RootBeanDefinition beanDefinition = new RootBeanDefinition();
        beanDefinition.setBeanClass(RestConsumerClientManagerInitialization.class);
        beanDefinition.setLazyInit(false);
        parserContext.getRegistry().registerBeanDefinition(RestConsumerClientManagerInitialization.class.getName(), beanDefinition);

        registered = true;
    }

    public static RestConsumerClientManager getInstance() {
        if (instance == null) {
            createInstance();
        }
        return instance;
    }

    private static synchronized void createInstance() {
        if (instance == null) {
            instance = new RestConsumerClientManager();
        }
    }

}
