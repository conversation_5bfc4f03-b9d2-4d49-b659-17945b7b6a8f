package com.uino.service.simulation;

import java.util.Collection;
import java.util.List;

import org.springframework.web.multipart.MultipartFile;

import com.binary.core.io.Resource;
import com.uino.bean.cmdb.business.ImportResultMessage;
import com.uino.bean.monitor.base.ESMonSysSeverityInfo;

/**
 * 事件级别定义服务-应该只有告警用-告警数据访问层放在了es/svc/impl下了
 * 
 * <AUTHOR>
 *
 */
public interface IMonSysSeveritySvc {

	/**
	 * 全文检索搜索告警定义
	 * 
	 * @param searchVal
	 * @return
	 */
	public List<ESMonSysSeverityInfo> querySeverityList(Long domainId, String searchVal);

    /**
     * 根据id查询告警
     * 
     * @param id
     * @return
     */
    public ESMonSysSeverityInfo getMonSeverityInfoById(Long id);

	/**
	 * 持久化告警定义
	 * 
	 * @param saveDto
	 * @return
	 */
	public Long saveOrUpdateSeverity(ESMonSysSeverityInfo saveDto);

	/**
	 * 根据告警定义ids删除告警定义
	 * 
	 * @param delIds
	 */
	public void deleteServrityByIds(Collection<Long> delIds);

    /**
     * 导出告警定级数据
     * 
     * @param isTpl
     * @return
     */
    public Resource exportSeverityInfos(Long domainId, Boolean isTpl);

    /**
     * 导入告警定级数据
     * 
     * @param file
     * @return
     */
    public ImportResultMessage importSeverityInfos(Long domainId, MultipartFile file);
}
