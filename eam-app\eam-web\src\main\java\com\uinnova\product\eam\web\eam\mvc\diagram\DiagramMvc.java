package com.uinnova.product.eam.web.eam.mvc.diagram;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.binary.core.exception.MessageException;
import com.binary.core.util.BinaryUtils;
import com.binary.framework.util.ControllerUtils;
import com.binary.framework.web.RemoteResult;
import com.binary.json.JSON;
import com.uinnova.product.eam.comm.model.VcDiagram;
import com.uinnova.product.eam.comm.model.VcDiagramDir;
import com.uinnova.product.eam.model.DiagramQueryBean;
import com.uinnova.product.eam.model.VcDiagramInfo;
import com.uinnova.product.eam.service.IEamDiagramSvc;
import com.uinnova.product.eam.service.IEamDirCollaboratorInfoSvc;
import com.uinnova.product.eam.web.diagram.bean.DiagramMoveCdt;
import com.uinnova.product.eam.web.diagram.bean.DiagramParam;
import com.uinnova.product.eam.web.diagram.peer.DiagramPeer;
import com.uinnova.product.vmdb.comm.bean.QueryListCondition;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uinnova.product.vmdb.comm.util.RestTypeUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 视图相关接口
 *
 * <AUTHOR>
 */
@Deprecated
@RestController
@RequestMapping("/eam/diagram")
public class DiagramMvc {

    @Autowired
    private DiagramPeer diagramPeer;

    @Autowired
    private IEamDiagramSvc diagramSvc;

    @Autowired
    private IEamDirCollaboratorInfoSvc dirCollaboratorInfoSvc;

    @PostMapping("/queryDiagramList")
    @ModDesc(desc = "查询列表信息", pDesc = "", rDesc = "视图信息", rType = List.class)
    public RemoteResult queryDiagramList(@RequestBody String body) {
        QueryListCondition<DiagramQueryBean> con = RestTypeUtil.toListCondition(body, DiagramQueryBean.class);
        List<VcDiagram> result = diagramSvc.queryDiagramList(con.getCdt(), con.getOrders());
        return new RemoteResult(result);
    }

    @PostMapping("/queryDiagramListPage")
    @ModDesc(desc = "分页查询列表信息", pDesc = "", rDesc = "视图信息", rType = List.class)
    public RemoteResult queryDiagramPage(@RequestBody String body) {
        QueryListCondition<DiagramQueryBean> con = RestTypeUtil.toListCondition(body, DiagramQueryBean.class);
        return new RemoteResult(diagramSvc.queryDiagramListPage(con.getCdt(), con.getOrders()));
    }

    @PostMapping("/updateDiagramNameAndDirById")
    @ModDesc(desc = "更新视图名字和目录信息", pDesc = "更新的内容", pType = DiagramMoveCdt.class, rDesc = "操作结果", rType = Integer.class)
    public RemoteResult updateDiagramNameAndDirById(@RequestBody String body) {
        DiagramMoveCdt cdt = JSON.toObject(body, DiagramMoveCdt.class);
        Integer result = diagramSvc.updateDiagramNameById(cdt.getNewName(), cdt.getNewDirId(), cdt.getDiagramId());
        return new RemoteResult(result);
    }

    @PostMapping("/updateDiagramBaseInfo")
    @ModDesc(desc = "更新视图的基本信息", pDesc = "视图信息", pType = VcDiagram.class, rDesc = "保存后的视图信息", rType = VcDiagram.class)
    public RemoteResult updateDiagramBaseInfo(@RequestBody String body) {
        VcDiagram record = JSON.toObject(body, VcDiagram.class);
        VcDiagram result = diagramSvc.updateDiagramBaseInfo(record);
        return new RemoteResult(result);
    }

    @RequestMapping("/queryDiagramInfoByIds")
    @ModDesc(desc = "根据视图ID数组查询视图的详情", pDesc = "视图ID数组", pType = DiagramParam.class, rDesc = "视图信息", rType = List.class, rcType = VcDiagramInfo.class)
    public RemoteResult queryDiagramInfoByIds(@RequestBody String body) {
        DiagramParam param = JSON.toObject(body, DiagramParam.class);
        List<VcDiagramInfo> result = diagramSvc.queryDiagramInfoByIds(param.getIds());
        return new RemoteResult(result);
    }

    @PostMapping("/findDirUserById")
    public RemoteResult findDirUserById(@RequestBody String body){
        JSONObject jsonObject = JSONObject.parseObject(body);
        Long dirId = jsonObject.getLong("dirId");
        //判断入参
        BinaryUtils.checkEmpty(dirId, "dirId");
        VcDiagramDir vcDiagramDir = diagramPeer.queryDirById(dirId);
        List<JSONObject> list = dirCollaboratorInfoSvc.findDirUserById(dirId,vcDiagramDir);
        return new RemoteResult(list);
    }

    @PostMapping("/updateDirUserById")
    public RemoteResult updateDirUserById(@RequestBody String body){
        JSONObject jsonObject = JSONObject.parseObject(body);
        Long dirId = jsonObject.getLong("dirId");
        //判断入参
        BinaryUtils.checkEmpty(dirId, "dirId");
        if (!JSONArray.isValidArray(jsonObject.getString("userList"))){
            throw MessageException.i18n("参数错误");
        }
        VcDiagramDir vcDiagramDir = diagramPeer.queryDirById(dirId);
        List<JSONObject> userList = JSONArray.parseArray(jsonObject.getString("userList"), JSONObject.class);
        Boolean flag =  dirCollaboratorInfoSvc.updateCooperation(dirId,userList,vcDiagramDir);
        return new RemoteResult(flag);
    }

    @RequestMapping("/copyDiagramById")
    @ModDesc(desc="拷贝视图信息",pDesc="拷贝源及目标目录信息",pType=DiagramMoveCdt.class,rDesc="拷贝结果视图id",rType=Long.class)
    public void copyDiagramById(HttpServletRequest request, HttpServletResponse response, @RequestBody String body){
        DiagramMoveCdt cdt = JSON.toObject(body, DiagramMoveCdt.class);

        Long result = diagramSvc.copyDiagramById(cdt.getNewName(), cdt.getNewDirId(), cdt.getDiagramId());

        ControllerUtils.returnJson(request, response, result);
    }
}
