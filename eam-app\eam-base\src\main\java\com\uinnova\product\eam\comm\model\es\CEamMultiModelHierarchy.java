package com.uinnova.product.eam.comm.model.es;

import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

/**
 * 多模型树查询VO
 * <AUTHOR>
 */
@Data
public class CEamMultiModelHierarchy implements Condition {
    @Comment("主键id")
    private Long id;

    @Comment("领域id")
    private Long domainId;

    @Comment("模型树名称")
    private String name;

    @Comment("模型树类型")
    @Deprecated
    private EamMultiModelType type;

    @Comment("模型树类型")
    private Integer modelType;

    @Comment("模型树描述")
    private String describe;

    @Comment("是否发布   0=未发布（默认），1=已发布")
    private Integer releaseState;

    @Comment("模型树是否发布过：true:发布过；false:未发布过")
    private Boolean releaseFlag;

    @Comment("创建时间")
    private Long createTime;

    @Comment("更改时间")
    private Long modifyTime;
}
