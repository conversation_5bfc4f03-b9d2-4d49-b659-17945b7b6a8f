package com.uinnova.product.eam.base.diagram.mix.model;

import com.uinnova.product.eam.base.diagram.model.ESDiagram;

import java.util.List;

/**
 * 模板目录信息
 */
public class TemplateDir {

    private Long id;

    private Long domainId;

    // 1：标准类型 2：个人类型
    private Integer type;

    private String dirName;

    private String viewType;

    private Integer order;

    private Long parentId;

    private String modifier;

    private Long modifyTime;

    private Long createTime;

    private List<ESDiagram> diagramList;

    public List<TemplateDir> subTemplateDirs;

    public List<TemplateDir> getSubTemplateDirs() {
        return subTemplateDirs;
    }

    public void setSubTemplateDirs(List<TemplateDir> subTemplateDirs) {
        this.subTemplateDirs = subTemplateDirs;
    }

    public List<ESDiagram> getDiagramList() {
        return diagramList;
    }

    public void setDiagramList(List<ESDiagram> diagramList) {
        this.diagramList = diagramList;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getDirName() {
        return dirName;
    }

    public void setDirName(String dirName) {
        this.dirName = dirName;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getViewType() {
        return viewType;
    }

    public void setViewType(String viewType) {
        this.viewType = viewType;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public Integer getOrder() {
        return order;
    }

    public void setOrder(Integer order) {
        this.order = order;
    }

    public Long getDomainId() {
        return domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public Long getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }
}
