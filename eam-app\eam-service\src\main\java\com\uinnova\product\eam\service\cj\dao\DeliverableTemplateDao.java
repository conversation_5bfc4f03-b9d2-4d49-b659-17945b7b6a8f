package com.uinnova.product.eam.service.cj.dao;

import com.uinnova.product.eam.model.cj.domain.DeliverableTemplate;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Service;
import jakarta.annotation.PostConstruct;

/**
 * 给交付物模板基本信息表[UINO_CJ_Deliverable_Template]数据访问对象
 */
@Service
public class DeliverableTemplateDao extends AbstractESBaseDao<DeliverableTemplate,DeliverableTemplate> {


    @Override
    public String getIndex() {
        return "uino_cj_deliverable_template";
    }

    @Override
    public String getType() {
        return getIndex();
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }
}
