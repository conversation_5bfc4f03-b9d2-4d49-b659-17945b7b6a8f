package com.uino.init.http;

import lombok.extern.slf4j.Slf4j;

import jakarta.servlet.Filter;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;

//@WebFilter(filterName = "staticFilter", urlPatterns = { "*.jpg", "*.JPG", "*.jpeg", "*.JPEG", "*.png" })
@Slf4j
//@Order(3)
public class StaticFilter implements Filter {

	{
		log.info("静态资源请求拦截器注册成功");
	}

	@Override
	public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
			throws IOException, ServletException {
		// TODO Auto-generated method stub
		((HttpServletResponse) response).addHeader("Access-Control-Allow-Origin", "*");
		((HttpServletResponse) response).addHeader("Access-Control-Allow-Headers", "*");
		chain.doFilter(request, response);
	}
}
