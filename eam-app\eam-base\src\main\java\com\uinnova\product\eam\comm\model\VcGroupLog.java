package com.uinnova.product.eam.comm.model;




import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;


@Comment("组操作记录表[VC_GROUP_LOG]")
public class VcGroupLog implements EntityBean {
	private static final long serialVersionUID = 1L;


	@Comment("ID[ID]")
	private Long id;


	@Comment("组ID[GROUP_ID]")
	private Long groupId;


	@Comment("日志时间[LOG_TIME]    日志时间:yyyyMMddHHmmss")
	private Long logTime;


	@Comment("操作人ID[OP_ID]")
	private Long opId;


	@Comment("操作人名称[OP_NAME]")
	private String opName;


	@Comment("来源类别[SOURCE_TYPE]    来源类别:1=组视图 2=组人")
	private Integer sourceType;


	@Comment("日志类型[LOG_TYPE]    日志类型:1=添加 2=修改 3=删除 4=查询")
	private Integer logType;


	@Comment("日志描述[LOG_DESC]")
	private String logDesc;


	@Comment("所属域[DOMAIN_ID]")
	private Long domainId;


	@Comment("创建时间[CREATE_TIME]    创建时间:yyyyMMddHHmmss")
	private Long createTime;


	@Comment("修改时间[MODIFY_TIME]    修改时间:yyyyMMddHHmmss")
	private Long modifyTime;




	public Long getId() {
		return this.id;
	}
	public void setId(Long id) {
		this.id = id;
	}


	public Long getGroupId() {
		return this.groupId;
	}
	public void setGroupId(Long groupId) {
		this.groupId = groupId;
	}


	public Long getLogTime() {
		return this.logTime;
	}
	public void setLogTime(Long logTime) {
		this.logTime = logTime;
	}


	public Long getOpId() {
		return this.opId;
	}
	public void setOpId(Long opId) {
		this.opId = opId;
	}


	public String getOpName() {
		return this.opName;
	}
	public void setOpName(String opName) {
		this.opName = opName;
	}


	public Integer getSourceType() {
		return this.sourceType;
	}
	public void setSourceType(Integer sourceType) {
		this.sourceType = sourceType;
	}


	public Integer getLogType() {
		return this.logType;
	}
	public void setLogType(Integer logType) {
		this.logType = logType;
	}


	public String getLogDesc() {
		return this.logDesc;
	}
	public void setLogDesc(String logDesc) {
		this.logDesc = logDesc;
	}


	public Long getDomainId() {
		return this.domainId;
	}
	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}


	public Long getCreateTime() {
		return this.createTime;
	}
	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}


	public Long getModifyTime() {
		return this.modifyTime;
	}
	public void setModifyTime(Long modifyTime) {
		this.modifyTime = modifyTime;
	}


}


