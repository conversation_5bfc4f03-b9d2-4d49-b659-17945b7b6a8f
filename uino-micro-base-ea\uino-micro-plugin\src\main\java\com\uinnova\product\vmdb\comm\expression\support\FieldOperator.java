package com.uinnova.product.vmdb.comm.expression.support;

import com.uinnova.product.vmdb.comm.expression.Expression;
import com.uinnova.product.vmdb.comm.expression.ExpressionFactory;
import com.uinnova.product.vmdb.comm.expression.Field;
import com.uinnova.product.vmdb.comm.expression.OP;

/**
 * 
 * <AUTHOR>
 *
 */
public class FieldOperator {

    public static final FieldOperator operator = new FieldOperator();

    /**
     * 等于
     */
    public <T> Expression<T> equal(Field<T> field, String value) {
        return ExpressionFactory.getExpression(field, OP.Equal, value);
    }

    /**
     * 不等于
     */
    public <T> Expression<T> notEqual(Field<T> field, String value) {
        return ExpressionFactory.getExpression(field, OP.NotEqual, value);
    }

    /**
     * 小于
     */
    public <T> Expression<T> less(Field<T> field, String value) {
        return ExpressionFactory.getExpression(field, OP.LT, value);
    }

    /**
     * 小于或等于
     */
    public <T> Expression<T> lessEqual(Field<T> field, String value) {
        return ExpressionFactory.getExpression(field, OP.LTEqual, value);
    }

    /**
     * 大于
     */
    public <T> Expression<T> greater(Field<T> field, String value) {
        return ExpressionFactory.getExpression(field, OP.GT, value);
    }

    /**
     * 大于或等于
     */
    public <T> Expression<T> greaterEqual(Field<T> field, String value) {
        return ExpressionFactory.getExpression(field, OP.GTEqual, value);
    }

    /**
     * 模糊匹配...
     */
    public <T> Expression<T> like(Field<T> field, String value) {
        return ExpressionFactory.getExpression(field, OP.Like, value);
    }

    /**
     * 字符包含
     */
    public <T> Expression<T> instr(Field<T> field, String value) {
        return ExpressionFactory.getExpression(field, OP.INSTR, value);
    }

    /**
     * 包含...
     */
    public <T> Expression<T> in(Field<T> field, String[] values) {
        return ExpressionFactory.getExpression(field, OP.In, values);
    }

    /**
     * 不包含...
     */
    public <T> Expression<T> notIn(Field<T> field, String[] values) {
        return ExpressionFactory.getExpression(field, OP.NotIn, values);
    }

    protected FieldOperator() {
    }

}
