package com.uinnova.product.eam.service.impl;

import com.binary.core.util.BinaryUtils;
import com.binary.jdbc.Page;
import com.uinnova.product.eam.comm.bean.EamFeedback;
import com.uinnova.product.eam.service.IEamFeedbackSvc;
import com.uinnova.product.eam.service.es.IamsESFeedbackDao;
import com.uino.dao.util.ESUtil;
import com.uino.util.sys.SysUtil;
import com.uino.bean.permission.base.SysUser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 意见反馈服务层实现
 *
 * <AUTHOR>
 */
@Service
public class IamsEamFeedbackSvcImpl implements IEamFeedbackSvc {

    private static final Logger log = LoggerFactory.getLogger(IamsEamFeedbackSvcImpl.class);

    @Autowired
    private IamsESFeedbackDao feedbackDao;

    @Override
    public Long saveFeedback(EamFeedback feedback) {
        SysUser loginUser = SysUtil.getCurrentUserInfo();
        feedback.setUserId(loginUser.getId());
        feedback.setUserName(loginUser.getUserName());
        feedback.setCreateTime(BinaryUtils.getNumberDateTime());
        return feedbackDao.saveOrUpdate(feedback);
    }

    @Override
    public Page<EamFeedback> queryInfoPage(Integer pageNum, Integer pageSize, EamFeedback cdt, String orders) {
        if (cdt == null) {
            cdt = new EamFeedback();
        }
        orders = BinaryUtils.isEmpty(orders) ? "createTime" : orders;
        orders = ESUtil.underlineToCamel(orders);
        Page<EamFeedback> sortListByQuery = feedbackDao.getSortListByQuery(pageNum, pageSize, ESUtil.cdtToBuilder(cdt), orders, false);
        List<EamFeedback> data = sortListByQuery.getData();
        if(!CollectionUtils.isEmpty(data)){
            for(int i=0;i<data.size();i++){
                data.get(i).setNo(i+1);
            }
        }
        return sortListByQuery;
    }
}
