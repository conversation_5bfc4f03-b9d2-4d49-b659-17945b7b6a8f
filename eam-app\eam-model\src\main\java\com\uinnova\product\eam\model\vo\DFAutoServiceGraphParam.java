package com.uinnova.product.eam.model.vo;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.model.enums.GraphType;
import lombok.Data;

@Data
@Comment("dify智能绘图-业务能力全景图")
public class DFAutoServiceGraphParam {

    @Comment("制图分类")
    private GraphType graphType;
    @Comment("层级数据")
    private DFAutoServiceGraphLevelData layerTree;
    @Comment("样式")
    private Object layoutScheme;
}
