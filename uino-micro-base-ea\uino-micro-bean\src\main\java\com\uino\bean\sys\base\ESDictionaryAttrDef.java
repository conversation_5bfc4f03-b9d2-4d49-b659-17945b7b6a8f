package com.uino.bean.sys.base;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

import com.binary.framework.bean.annotation.Comment;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Builder.Default;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 
 * <AUTHOR>
 *
 */
@Comment("字典表属性定义")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value="字典表属性定义",description = "字典表属性定义")
public class ESDictionaryAttrDef implements Serializable {

    /**
     * 
     */
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value="id",example = "123")
    @Comment("ID[ID]")
    private Long id;

    @ApiModelProperty(value="属性名",example = "color")
    @Comment("属性名[PRO_NAME]")
    private String proName;

	@ApiModelProperty(value = "标准属性名-数据库实际存储的名称", example = "dbcolor")
	@Comment("标准名[PRO_STD_NAME] 标准名:数据库实际存储的名称")
	private String proStdName;

    @ApiModelProperty(value="属性类型,1=文本 2=文件 3=数值 4=颜色 5=数据字典 6=枚举 7=长文本 ",example = "1")
    @Comment("属性类型[PRO_TYPE]    属性类型:1=文本 2=文件 3=数值 4=颜色 5=数据字典 6=枚举 7=长文本 ")
    @Default
    private Integer proType = 1;

    @ApiModelProperty(value="引用类型,1=引用字典 2=引用分类 3=引用角色 4=自定义取值为selectValues",example = "1")
    @Comment("引用类型[CITE_TYPE]   引用类型:1=引用字典 2=引用分类 3=引用角色 4=自定义取值为selectValues")
    @Default
    private Integer citeType = 0;

    @ApiModelProperty(value="引用字典id",example = "123")
    @Comment("引用字典id[SOURCE_DICT_CLASS_ID]")
    private Long sourceDictClassId;

    @ApiModelProperty(value="引用字典属性id",example = "123")
    @Comment("引用字典属性id[SOURCE_DICT_DEF_ID]")
    private Long sourceDictDefId;

    @ApiModelProperty(value="选择框类型,0=单选 1=复选",example = "0")
    @Comment("选择框类型[SELECT_TYPE]  选择框类型:0=单选 1=复选 ")
    @Default
    private Integer selectType = 0;

    @ApiModelProperty(value="复选框值")
    @Comment("枚举值[SELECT_VALUES]")
    private List<Map<String , String>> selectValues;

    @ApiModelProperty(value="是否是主键,0=否，1=是",example = "0")
    @Comment("是否主键[IS_MAJOR]    是否主键:0=否，1=是")
    @Default
    private Integer isMajor = 0;

    @ApiModelProperty(value="是否可编辑,0可编辑，1不可编辑",example = "0")
    @Comment("是否可编辑[IS_EDIT]")
    @Default
    private Integer isNotEdit = 0;

    @ApiModelProperty(value="是否唯一,0不是唯一，1是唯一",example = "0")
    @Comment("是否可编辑[IS_EDIT]")
    @Default
    private Integer isUnique = 0;

    @ApiModelProperty(value="是否必填,0=否，1=是",example = "0")
    @Comment("是否必填[IS_REQUIRED]    是否必填:0=否，1=是")
    @Default
    private Integer isRequired = 0;

    @ApiModelProperty(value="缺省值")
    @Comment("缺省值[DEF_VAL]")
    private String defVal;

    @ApiModelProperty(value="枚举值")
    @Comment("枚举值[ENUM_VALUES]")
    private List<String> enumValues;

   @ApiModelProperty(value="属性描述")
    @Comment("属性描述[PRO_DESC]")
    private String proDesc;

   @ApiModelProperty(value="排列顺序",example = "1")
    @Comment("排列顺序[ORDER_NO]")
    private Integer orderNo;

	@ApiModelProperty(value="是否显示",example = "true")
   @Comment("是否显示[DISPLAY]")
	@Default
	private Boolean display = true;

    @ApiModelProperty(value="默认列宽",example = "100")
    @Comment("排列顺序[DEFAULT_COLUMN_WIDTH]")
	private int defaultColumnWidth = 100;

    @ApiModelProperty(value="domainId",example = "123")
    @Comment("DOMAINID[DOMAINID]")
    private Long doaminId;


    public String getProStdName() {
		return proStdName == null ? getProName() : proStdName;
	}

	public void setProStdName(String proStdName) {
		this.proStdName = proStdName;
	}

	public enum DictProTypeEnum {
        TEXT("字符串", 1), FILE("文件", 2), NUMBER("整数", 3), COLOR("颜色", 4), EXTERNAL_DICT("数据字典", 5), ENUM("枚举", 6), LONG_VARCHAR("文本", 7), CLOB("文章", 8);

        private String value;

        private int type;

        private DictProTypeEnum(String value, Integer type) {
            this.value = value;
            this.type = type;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }

        public int getType() {
            return type;
        }

        public void setType(int type) {
            this.type = type;
        }

        public static DictProTypeEnum valueOf(int v) {
            switch (v) {
                case 1:
                    return TEXT;
                case 2:
                    return FILE;
                case 3:
                    return NUMBER;
                case 4:
                    return COLOR;
                case 5:
                    return EXTERNAL_DICT;
                case 6:
                    return ENUM;
                case 7:
                    return LONG_VARCHAR;
                case 8:
                    return CLOB;
                default:
                    return null;
            }
        }
    }

    public enum DictCiteTypeEnum {
        DICT("数据字典", 1), CLASS("分类引用", 2), ROLE("角色引用", 3) , CUSTOM("自定义", 4);

        private String value;

        private int type;

        private DictCiteTypeEnum(String value, Integer type) {
            this.value = value;
            this.type = type;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }

        public int getType() {
            return type;
        }

        public void setType(int type) {
            this.type = type;
        }

        public static DictCiteTypeEnum valueOf(int v) {
            switch (v) {
                case 1:
                    return DICT;
                case 2:
                    return CLASS;
                case 3:
                    return DictCiteTypeEnum.ROLE;
                default:
                    return null;
            }
        }
    }
}
