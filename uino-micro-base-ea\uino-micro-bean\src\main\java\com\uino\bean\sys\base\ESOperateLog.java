package com.uino.bean.sys.base;

import java.io.Serializable;

import com.binary.framework.bean.annotation.Comment;
import com.uino.bean.permission.business.IValidDto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 
 * <AUTHOR>
 *
 */
@Comment("系统操作日志")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value="系统操作日志",description = "系统操作日志")
public class ESOperateLog implements Serializable, IValidDto {

    private static final long serialVersionUID = -440576289475763042L;

    @ApiModelProperty(value="ID",example = "123")
    @Comment("ID[ID]")
    private Long id;

    @ApiModelProperty(value="用户ID",example = "123")
    @Comment("用户ID[USER_ID]")
    private Long userId;

    @ApiModelProperty(value="用户代码")
    @Comment("用户代码[USER_CODE]")
    private String userCode;

    @ApiModelProperty(value="用户姓名",example ="mike" )
    @Comment("用户姓名[USER_NAME]")
    private String userName;

    @ApiModelProperty(value="操作模块",example = "commom")
    @Comment("操作模块[MODULE_NAME]")
    private String moduleName;

    @ApiModelProperty(value="操作路径")
    @Comment("操作路径[OP_PATH]")
    private String opPath;

    @ApiModelProperty(value="操作描述")
    @Comment("操作描述[OP_DESC]")
    private String opDesc;

    @ApiModelProperty(value="结果状态,1=成功 0=失败",example = "1")
    @Comment("结果状态[OP_STATUS]    1=成功 0=失败")
    private Integer opStatus;

    @ApiModelProperty(value="输入参数")
    @Comment("输入参数[OP_PARAM]")
    private String opParam;

    @ApiModelProperty(value="输出结果")
    @Comment("输出结果[OP_RESULT]")
    private String opResult;

    @ApiModelProperty(value="类名",example = "SysoLog")
    @Comment("类名[MVC_FULL_NAME]")
    private String mvcFullName;

    @ApiModelProperty(value="方法名",example = "query")
    @Comment("方法名[MVC_MOD_NAME]")
    private String mvcModName;

    @ApiModelProperty(value="所属域ID",example = "123")
    @Comment("所属域[DOMAIN_ID]")
    private Long domainId;

    @ApiModelProperty(value="创建时间")
    @Comment("创建时间[CREATE_TIME]    创建时间:yyyyMMddHHmmss")
    private Long createTime;

    @ApiModelProperty(value="修改时间")
    @Comment("修改时间[MODIFY_TIME]    修改时间:yyyyMMddHHmmss")
    private Long modifyTime;

    @Comment("发起请求的IP地址")
    private String ipAddress;

    @Override
    public void valid() {

    }
}
