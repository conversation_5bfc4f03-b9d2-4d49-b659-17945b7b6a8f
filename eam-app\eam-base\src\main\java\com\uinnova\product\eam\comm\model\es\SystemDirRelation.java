package com.uinnova.product.eam.comm.model.es;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.util.List;

@Data
public class SystemDirRelation {
    private Long id;
    private Long dirId;
    private Long parentId;
    @Comment("关联es系统id")
    private Long esSysId;
    @Comment("子系统名称也是目录名称")
    private String subSysName;
    @Comment("系统文件夹下面的四个固定的类型：1：应用架构 2：业务架构 3：数据架构 4：技术架构 5:普通文件夹 6：设计方案")
    private Long type;
    @Comment("设计方案ids")
    private List<Long> planIds;
}
