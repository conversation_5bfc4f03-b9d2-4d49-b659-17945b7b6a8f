package com.uinnova.product.eam.service.cj.service.impl;

import cn.hutool.core.collection.ListUtil;
import com.uinnova.product.eam.model.cj.domain.*;
import com.uinnova.product.eam.model.cj.enums.QuestionStatusEnum;
import com.uinnova.product.eam.model.cj.request.PlanModuleAnnotationRequest;
import com.uinnova.product.eam.model.cj.vo.ChapterContextVO;
import com.uinnova.product.eam.model.cj.vo.ContextModuleVO;
import com.uinnova.product.eam.model.cj.vo.PlanChapterVO;
import com.uinnova.product.eam.model.cj.vo.PlanModuleAnnotationVO;
import com.uinnova.product.eam.model.constants.Constants;
import com.uinnova.product.eam.model.vo.AnnotationBean;
import com.uinnova.product.eam.model.vo.ReplyAnnotationVO;
import com.uinnova.product.eam.service.cj.dao.ChapterContextDao;
import com.uinnova.product.eam.service.cj.dao.PlanChapterInstanceDao;
import com.uinnova.product.eam.service.cj.dao.PlanChapterQuestionDao;
import com.uinnova.product.eam.service.cj.dao.PlanModuleAnnotationDao;
import com.uinnova.product.eam.service.cj.service.EamPlanChapterSvc;
import com.uinnova.product.eam.service.cj.service.FlowableService;
import com.uinnova.product.eam.service.cj.service.PlanModuleAnnotationService;
import com.uinnova.product.eam.service.exception.BusinessException;
import com.uino.api.client.permission.IUserApiSvc;
import com.uino.bean.permission.business.UserInfo;
import com.uino.bean.permission.query.CSysUser;
import org.springframework.util.CollectionUtils;
import com.binary.core.lang.StringUtils;
import com.binary.core.util.BinaryUtils;
import com.uino.bean.permission.base.SysUser;
import com.uino.dao.util.ESUtil;
import com.uino.util.sys.SysUtil;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.sort.FieldSortBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 方案批注service实现
 *
 * <AUTHOR>
 * @since 2022-3-1 20:40:43
 */
@Service
public class PlanModuleAnnotationServiceImpl implements PlanModuleAnnotationService {

    @Resource
    private ChapterContextDao chapterContextDao;

    @Resource
    private PlanModuleAnnotationDao planModuleAnnotationDao;

    @Resource
    private PlanChapterQuestionDao planChapterQuestionDao;

    @Resource
    private PlanChapterInstanceDao planChapterInstanceDao;

    @Resource
    private FlowableService flowableService;

    @Resource
    private IUserApiSvc userApiSvc;

    @Resource
    private EamPlanChapterSvc eamPlanChapterSvc;

    /**
     * 批注查询
     *
     * @param planId    方案id
     * @param chapterId 章节id
     * @param delFlag   查询状态
     * @return {@link PlanModuleAnnotationEntity}
     */
    @Override
    public List<PlanModuleAnnotationEntity> list(Long planId, Long chapterId, Boolean delFlag, String processInstanceId) {
        if (planId == null && chapterId == null && delFlag == null) {
            return Collections.emptyList();
        }

        BoolQueryBuilder query = QueryBuilders.boolQuery();

        if (planId != null) {
            query.must(QueryBuilders.termQuery("planId", planId));
        }

        if (chapterId != null) {
            query.must(QueryBuilders.termQuery("planChapterId", chapterId));
        }

        if (delFlag != null) {
            query.must(QueryBuilders.termQuery("delFlag", delFlag));
        }

        if (!StringUtils.isEmpty(processInstanceId)) {
            query.must(QueryBuilders.termQuery("processInstanceId.keyword", processInstanceId));
        }

        return planModuleAnnotationDao.getListByQuery(query);
    }

    @Override
    public void deleteAnnotationByPlanId(Long planId) {
        if (planId == null) {
            throw new BusinessException("方案主键不能为空!");
        }
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("planId", planId));
        planModuleAnnotationDao.deleteByQuery(query, true);
    }

    /**
     * 方案批注新增
     *
     * @param request {@link PlanModuleAnnotationRequest}
     * @return 批注id
     */
    @Override
    public Long saveAnnotation(PlanModuleAnnotationRequest request) {
        ChapterContext context = chapterContextDao.getById(request.getChapterId());

        ContextModule module = context.getModuleList().stream()
                .filter(m -> request.getPlanChapterModuleId().equals(m.getId()))
                .findFirst().orElseThrow(() -> new BusinessException(Constants.PLAN_MODULE_NOT_FOUND));

        PlanModuleAnnotationEntity annotationEntity = PlanModuleAnnotationEntity.builder()
                .planId(context.getPlanId())
                .planChapterId(context.getId())
                .planChapterModuleId(module.getId())
                .annotationContent(request.getAnnotationContent())
                .delFlag(false)
                .processInstanceId(request.getProcessInstanceId())
                .taskDefinitionKey(request.getTaskDefinitionKey())
                .build();

        // 是否为问题
        boolean problem = request.getProblem() != null ? request.getProblem() : false;
        annotationEntity.setProblem(problem);

        SysUser user = SysUtil.getCurrentUserInfo();
        annotationEntity.setId(ESUtil.getUUID());
        annotationEntity.setCreatorCode(user.getLoginCode());
        annotationEntity.setCreatorName(user.getUserName());
        annotationEntity.setCreateTime(ESUtil.getNumberDateTime());

        planModuleAnnotationDao.saveOrUpdate(annotationEntity);

        return annotationEntity.getId();
    }

    /**
     * 方案批注删除
     *
     * @param request {@link PlanModuleAnnotationRequest}
     */
    @Override
    public void deleteAnnotation(PlanModuleAnnotationRequest request) {
        PlanModuleAnnotationEntity target = planModuleAnnotationDao.getById(request.getAnnotationId());
        target.setDelFlag(true);
        planModuleAnnotationDao.saveOrUpdate(target);
    }

    /**
     * 方案批注修改
     *
     * @param request {@link PlanModuleAnnotationRequest}
     */
    @Override
    public void modifyAnnotation(PlanModuleAnnotationRequest request) {
        PlanModuleAnnotationEntity target = planModuleAnnotationDao.getById(request.getAnnotationId());
        SysUser user = SysUtil.getCurrentUserInfo();
        if (!StringUtils.isEmpty(request.getAnnotationContent())) {
            target.setAnnotationContent(request.getAnnotationContent());
        }
        if (request.getProblem() != null) {
            target.setProblem(request.getProblem());
        }
        if (!StringUtils.isEmpty(request.getProcessInstanceId())) {
            target.setProcessInstanceId(request.getProcessInstanceId());
        }
        if (!StringUtils.isEmpty(request.getTaskDefinitionKey())) {
            target.setTaskDefinitionKey(request.getTaskDefinitionKey());
        }
        target.setModifyTime(ESUtil.getNumberDateTime());
        target.setModifierCode(user.getLoginCode());
        target.setModifierName(user.getLoginCode());
        planModuleAnnotationDao.saveOrUpdate(target);
    }

    /**
     * 方案批注分页查询
     *
     * @param request {@link PlanModuleAnnotationRequest}
     * @return map说明 ： size - 批注的个数, annotationList - 批注列表
     */
    @Override
    public Map<String, Object> list(PlanModuleAnnotationRequest request) {
        BoolQueryBuilder query = QueryBuilders.boolQuery()
                .must(QueryBuilders.termQuery("delFlag", false))
                .must(QueryBuilders.termQuery("planId", request.getPlanId()));
        query.mustNot(QueryBuilders.existsQuery("parentAnnotationId"));
        if (!StringUtils.isEmpty(request.getProcessInstanceId())) {
            query.must(QueryBuilders.termQuery("processInstanceId.keyword", request.getProcessInstanceId()));
        }

        FieldSortBuilder sortBuilder = new FieldSortBuilder("createTime").order(SortOrder.DESC);
        List<PlanModuleAnnotationEntity> persistentList = planModuleAnnotationDao.getSortListByQuery(1, 3000, query,
                ListUtil.of(sortBuilder)).getData();

        //下面过滤掉删除的章节评论
        filterDeleteAnnotation(persistentList,request.getPlanId());
        int annotationSize;
        List<PlanModuleAnnotationVO> annotationList;
        if (BinaryUtils.isEmpty(persistentList)) {
            annotationSize = 0;
            annotationList = Collections.emptyList();
        } else {
            annotationSize = persistentList.size();

            BoolQueryBuilder queryByPlanId = QueryBuilders.boolQuery().must(QueryBuilders.termQuery("planId",
                    request.getPlanId()));

            List<ChapterInstance> chapterList = planChapterInstanceDao.getListByQuery(queryByPlanId);
            List<ChapterContext> contextList = chapterContextDao.getListByQuery(queryByPlanId);

            Map<Long, ChapterInstance> chapterMap = chapterList.stream().collect(Collectors.toMap(ChapterInstance::getId
                    , Function.identity()));

            Map<Long, ChapterContext> contextMap =
                    contextList.stream().collect(Collectors.toMap(ChapterContext::getId, Function.identity()));

            //下面找出此方案下面所有回复的批注信息
            List<Long> parentAnnotationIds = persistentList.stream().map(item -> item.getId()).collect(Collectors.toList());
            Map<Long,PlanModuleAnnotationEntity> childAnnotationMap = findChildAnnotation(parentAnnotationIds);

            annotationList = persistentList.
                    stream().
                    map(p -> buildAnnotationVoList(chapterMap, contextMap, p, request.getTaskDefinitionKey(),childAnnotationMap)).
                    filter(item -> item != null).collect(Collectors.toList());

        }
        //查询方案所有章节和内容块信息
        List<PlanChapterVO> allPlanInfo = eamPlanChapterSvc.getFullPlanContext(request.getPlanId(),request.getProcessInstanceId()!=null ? request.getProcessInstanceId():null, null);
        List<String> chapterContextIds = new ArrayList<>();
        getChapterContextIds(allPlanInfo,chapterContextIds);
        Map<String, List<PlanModuleAnnotationVO>> groupByChapterModuleId = annotationList.stream().collect(Collectors.groupingBy(item -> item.getPlanChapterId().toString() + item.getPlanChapterModuleId()));
        List<AnnotationBean> result = new ArrayList<>();
        for (String chapterContextId : chapterContextIds) {
            if (!CollectionUtils.isEmpty(groupByChapterModuleId.get(chapterContextId))) {
                List<PlanModuleAnnotationVO> planModuleAnnotationVOS = groupByChapterModuleId.get(chapterContextId);
                AnnotationBean annotationBean = new AnnotationBean();
                annotationBean.setPlanModuleAnnotationVOList(planModuleAnnotationVOS);
                annotationBean.setPlanChapterId(planModuleAnnotationVOS.get(0).getPlanChapterId());
                annotationBean.setPlanChapterModuleId(planModuleAnnotationVOS.get(0).getPlanChapterModuleId());
                result.add(annotationBean);
            }
        }
        Map<String, Object> targetMap = new HashMap<>((int) (2 / 0.75) + 1);
        targetMap.put("annotationSize", annotationSize);
        targetMap.put("annotationList", result);

        return targetMap;
    }

    private void getChapterContextIds(List<PlanChapterVO> allPlanInfo,List<String> chapterContextIds) {
        if (CollectionUtils.isEmpty(allPlanInfo)) {
            return;
        }
        for (PlanChapterVO planChapterVO : allPlanInfo) {
            ChapterContextVO context = planChapterVO.getContext();
            List<ContextModuleVO> moduleList = context.getModuleList();
            if (!CollectionUtils.isEmpty(moduleList)) {
                for (ContextModuleVO contextModuleVO : moduleList) {
                    chapterContextIds.add(planChapterVO.getId().toString()+contextModuleVO.getId().toString());
                }
            }else {
                chapterContextIds.add(planChapterVO.getId().toString());
            }
            List<PlanChapterVO> childChapterList = planChapterVO.getChildChapterList();
            getChapterContextIds(childChapterList,chapterContextIds);
        }
    }

    private Map<Long, PlanModuleAnnotationEntity> findChildAnnotation(List<Long> parentAnnotationIds) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termsQuery("parentAnnotationId",parentAnnotationIds));
        boolQueryBuilder.must(QueryBuilders.termsQuery("delFlag",false));
        List<PlanModuleAnnotationEntity> childAnnotationList = planModuleAnnotationDao.getListByQuery(boolQueryBuilder);
        if (CollectionUtils.isEmpty(childAnnotationList)) {
            return new HashMap<>();
        }
        return childAnnotationList.stream().collect(Collectors.toMap(item -> item.getParentAnnotationId(), item -> item, (k1, k2) -> k2));
    }

    private void filterDeleteAnnotation(List<PlanModuleAnnotationEntity> persistentList,Long planId) {

        List<PlanChapterVO> fullPlanContext = eamPlanChapterSvc.getFullPlanContext(planId, null, null);
        List<Long> collect = new ArrayList<>();
        findModuleIdList(fullPlanContext,collect);
        persistentList.removeIf(item ->  !collect.contains(item.getPlanChapterModuleId()));
    }

    private void findModuleIdList(List<PlanChapterVO> fullPlanContext, List<Long> collect) {
        if (CollectionUtils.isEmpty(fullPlanContext)) {
            return;
        }
        for (PlanChapterVO planChapterVO : fullPlanContext) {
            ChapterContextVO context = planChapterVO.getContext();
            if (context != null && !CollectionUtils.isEmpty(context.getModuleList())) {
                List<ContextModuleVO> moduleList = context.getModuleList();
                for (ContextModuleVO contextModuleVO : moduleList) {
                    collect.add(contextModuleVO.getId());
                }
            }
            List<PlanChapterVO> childChapterList = planChapterVO.getChildChapterList();
            if (!CollectionUtils.isEmpty(childChapterList)) {
                findModuleIdList(childChapterList,collect);
            }
        }
    }

    private PlanModuleAnnotationVO buildAnnotationVoList(Map<Long, ChapterInstance> chapterMap,
                                                         Map<Long, ChapterContext> contextMap,
                                                         PlanModuleAnnotationEntity annotationPersistent,
                                                         String taskDefinitionKey,
                                                         Map<Long,PlanModuleAnnotationEntity> childAnnotationMap) {

        Long targetModuleId = annotationPersistent.getPlanChapterModuleId();
        Long targetChapterId = annotationPersistent.getPlanChapterId();

        ChapterInstance targetChapter = chapterMap.get(targetChapterId);
        ChapterContext targetContext = contextMap.get(targetChapterId);
        ContextModule targetModule =
                targetContext.getModuleList().stream().filter(module -> targetModuleId.equals(module.getId()))
                        .findFirst()
                        .orElseThrow(() -> new BusinessException(Constants.PLAN_MODULE_NOT_FOUND));

        PlanTemplateChapterData moduleDefinition = targetModule.getModuleDefinition();
        Integer moduleType = moduleDefinition.getType();
        String moduleName = "";

        // 制品
        int product = 1;
        // 表格
        int table = 2;
        // 富文本
        int richText = 3;
        //清单制品
        int dataSet = 4;
        //附件
        int appendix = 5;
        if (moduleType == product) {
            moduleName = moduleDefinition.getProductName();
        } else if (moduleType == table) {
            moduleName = moduleDefinition.getDataTableName();
        } else if (moduleType == richText) {
            moduleName = moduleDefinition.getRichTextName();
        } else if (moduleType == dataSet) {
            moduleName = moduleDefinition.getDataSetName();
        } else if (moduleType == appendix) {
            moduleName = moduleDefinition.getAppendixName();
        }

        String author = annotationPersistent.getCreatorCode();
        if (!BinaryUtils.isEmpty(annotationPersistent.getCreatorName())) {
            author = annotationPersistent.getCreatorName();
        } else {
            String creatorCode = annotationPersistent.getCreatorCode();
            CSysUser cdt = new CSysUser();
            cdt.setLoginCodeEqual(creatorCode);
            List<SysUser> shareUsers = userApiSvc.getSysUserByCdt(cdt);
            if (!CollectionUtils.isEmpty(shareUsers)) {
                SysUser sysUser = shareUsers.get(0);
                if (sysUser != null) {
                    author = sysUser.getUserName();
                }
            }
        }

        PlanModuleAnnotationVO vo = new PlanModuleAnnotationVO();
        vo.setPlanChapterModuleId(targetModule.getId());
        vo.setPlanChapterModuleName(moduleName);
        vo.setPlanChapterName(targetChapter.getName());
        vo.setPlanChapterId(targetChapter.getId());
        vo.setAnnotationContent(annotationPersistent.getAnnotationContent());
        vo.setProblem(annotationPersistent.getProblem());
        vo.setAnnotationId(annotationPersistent.getId());
        vo.setCreateTime(annotationPersistent.getCreateTime());
        vo.setAnnotationTitle(String.format("%s/%s", vo.getPlanChapterName(), vo.getPlanChapterModuleName()));
        vo.setAuthor(author);
        vo.setAuthorCode(annotationPersistent.getCreatorCode());
        this.setReplyAnnotation(vo,childAnnotationMap.get(annotationPersistent.getId()));

        // 获取试题的属性
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.must(QueryBuilders.termQuery("annotationId", annotationPersistent.getId()));
        List<PlanChapterQuestion> questionList = planChapterQuestionDao.getListByQuery(queryBuilder);
        if (!CollectionUtils.isEmpty(questionList)) {
            PlanChapterQuestion question = questionList.get(0);
            vo.setQuestionDate(question.getCreateTime());
            vo.setQuestionState(QuestionStatusEnum.getQuestionDesc(question.getQuestionState()));
        }
        boolean questionPower = Objects.equals(annotationPersistent.getCreatorCode(), SysUtil.getCurrentUserInfo().getLoginCode());
        if (questionPower && !StringUtils.isEmpty(taskDefinitionKey) && !StringUtils.isEmpty(annotationPersistent.getTaskDefinitionKey())
                && Objects.equals(taskDefinitionKey, annotationPersistent.getTaskDefinitionKey())) {
            vo.setQuestionPower(true);
        }
        return vo;
    }

    private void setReplyAnnotation(PlanModuleAnnotationVO vo, PlanModuleAnnotationEntity planModuleAnnotationEntity) {
        if (planModuleAnnotationEntity == null) {
            return;
        }
        ReplyAnnotationVO replyAnnotationVO = new ReplyAnnotationVO();
        replyAnnotationVO.setPlanChapterId(planModuleAnnotationEntity.getPlanChapterId());
        replyAnnotationVO.setPlanChapterModuleId(planModuleAnnotationEntity.getPlanChapterModuleId());
        replyAnnotationVO.setAnnotationId(planModuleAnnotationEntity.getId());
        replyAnnotationVO.setParentAnnotationId(planModuleAnnotationEntity.getParentAnnotationId());
        replyAnnotationVO.setAnnotationContent(planModuleAnnotationEntity.getAnnotationContent());
        replyAnnotationVO.setApplyTime(planModuleAnnotationEntity.getCreateTime());
        String applyAuthor = planModuleAnnotationEntity.getCreatorCode();
        if (!BinaryUtils.isEmpty(planModuleAnnotationEntity.getCreatorName())) {
            applyAuthor = planModuleAnnotationEntity.getCreatorName();
        }else {
            String applyCode = planModuleAnnotationEntity.getCreatorCode();
            UserInfo userInfo = userApiSvc.getUserInfoByLoginCode(applyCode);
            applyAuthor = userInfo.getUserName();
        }
        replyAnnotationVO.setApplyCode(planModuleAnnotationEntity.getCreatorCode());
        replyAnnotationVO.setApplyAuthor(applyAuthor);
        vo.setReplyAnnotationVO(replyAnnotationVO);
    }


}
