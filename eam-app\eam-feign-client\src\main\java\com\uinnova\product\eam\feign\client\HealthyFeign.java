package com.uinnova.product.eam.feign.client;

import com.uino.provider.feign.config.BaseFeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * description
 *
 * <AUTHOR>
 * @since 2022/2/22 15:28
 */
@FeignClient(name = "${fuxi-feign-server-name:eam-fuxi}"
        , path = "${fuxi-feign-server-path:tarsier-eam}/healthy",
        configuration = {BaseFeignConfig.class})
public interface HealthyFeign {

    @GetMapping("/ping")
    String ping();
}
