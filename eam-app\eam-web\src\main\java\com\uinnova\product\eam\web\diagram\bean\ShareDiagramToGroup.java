package com.uinnova.product.eam.web.diagram.bean;

import java.util.List;

import com.binary.framework.bean.annotation.Comment;

public class ShareDiagramToGroup {

	@Comment("视图id")
	private Long diagramId;

	@Comment("小组IDS")
	private List<Long> groupIds;

	public Long getDiagramId() {
		return diagramId;
	}

	public void setDiagramId(Long diagramId) {
		this.diagramId = diagramId;
	}

	public List<Long> getGroupIds() {
		return groupIds;
	}

	public void setGroupIds(List<Long> groupIds) {
		this.groupIds = groupIds;
	}

}
