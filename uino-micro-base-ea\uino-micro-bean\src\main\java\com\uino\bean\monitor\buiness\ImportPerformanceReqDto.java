package com.uino.bean.monitor.buiness;

import java.io.Serializable;
import java.util.List;

import org.springframework.util.Assert;

import com.binary.core.util.BinaryUtils;
import com.uino.bean.permission.business.IValidDto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(value = "模拟性能参数类")
@Data
public class ImportPerformanceReqDto implements Serializable, IValidDto {

    private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "上传的性能数据文件地址")
    private String excelFilePath;

	@ApiModelProperty(value = "孪生模型的id")
	private Long classId;

	@ApiModelProperty(value = "选中的孪生体id集合", example = "[123,456]")
	private List<Long> ciIds;

	private Long objId;

	@ApiModelProperty(value = "模拟数据对象类型，1=孪生体，2=关系")
    private Integer objType;

	@ApiModelProperty(value="所属域id")
	private Long domainId;

    @Override
    public void valid() {
		// Assert.notNull(excelFilePath, "excelFilePath not null");
		Assert.isTrue(classId != null || !BinaryUtils.isEmpty(ciIds), "X_PARAM_NOT_NULL${name:classId/ciIds}");
        Assert.notNull(objType, "objType not null");
    }
}
