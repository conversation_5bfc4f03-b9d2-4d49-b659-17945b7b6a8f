package com.uinnova.product.eam.web.eam.mvc;

import com.binary.core.io.Resource;
import com.binary.core.io.support.FileResource;
import com.binary.core.util.BinaryUtils;
import com.binary.framework.util.ControllerUtils;
import com.binary.framework.web.RemoteResult;
import com.uinnova.product.eam.comm.bean.Resolution;
import com.uinnova.product.eam.comm.bean.ResolutionDoc;
import com.uinnova.product.eam.model.ResolutionCdt;
import com.uinnova.product.eam.service.IEamResolutionSvc;
import com.uino.util.rsm.RsmUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.File;
import java.text.ParseException;
import java.util.Map;
import java.util.Set;

/**
 * 架构决议Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/eam/resolution")
public class EamResolutionMvc {

    @Autowired
    private IEamResolutionSvc resolutionSvc;

    @Autowired
    private RsmUtils rsmUtils;

    /**
     * 新增决议数据
     *
     * @param files      决议文档
     * @param resolution 架构决议数据
     * @return 执行结果
     * @throws ParseException 时间解析异常
     */
    @PostMapping("/addResolution")
    public RemoteResult addResolution(MultipartFile[] files, Resolution resolution) throws ParseException {
        //判断入参
        BinaryUtils.checkEmpty(resolution.getCiCode(), "ciCode");
        BinaryUtils.checkEmpty(resolution.getResolutionSubject(), "resolutionSubject");
        return new RemoteResult(resolutionSvc.addResolution(resolution, files));
    }

    /**
     * 根据id查询决议信息
     *
     * @param id 决议id
     * @return 执行结果
     */
    @GetMapping("/getResolutionById")
    public RemoteResult getResolutionById(Long id) {
        //判断入参
        BinaryUtils.checkEmpty(id, "id");
        return new RemoteResult(resolutionSvc.getById(id));
    }

    /**
     * 修改决议
     *
     * @param resolution 决议数据
     * @return 执行结果
     */
    @PostMapping("/editResolution")
    public RemoteResult editResolution(@RequestBody Resolution resolution) {
        BinaryUtils.checkEmpty(resolution.getId(), "id");
        resolutionSvc.editResolution(resolution);
        return new RemoteResult("更新成功");
    }

    /**
     * 修改决议
     *
     * @param resolutions 决议数据
     * @return 执行结果
     */
    @PostMapping("/batchEditResolution")
    public RemoteResult batchEditResolution(@RequestBody Resolution[] resolutions) {
        for (Resolution resolution : resolutions) {
            BinaryUtils.checkEmpty(resolution.getId(), "id");
        }
        resolutionSvc.batchEditResolution(resolutions);
        return new RemoteResult("更新成功");
    }

    /**
     * 根据决议id上传决议文档
     *
     * @param resolutionId 决议id
     * @param files        文档
     * @return 执行结果
     */
    @PostMapping("/uploadResolutionDoc")
    public RemoteResult uploadResolutionDoc(Long resolutionId, MultipartFile[] files) {
        Set<Map<String, Object>> maps = resolutionSvc.uploadResolutionDoc(resolutionId, files);
        return new RemoteResult(maps);
    }

    /**
     * 根据决议文档id删除文档
     *
     * @param resolutionDocId 决议文档id
     * @return 执行结果
     */
    @PostMapping("/deleteResolutionDoc")
    public RemoteResult deleteResolutionDoc(Long resolutionId, Long resolutionDocId) {
        resolutionSvc.deleteResolutionDoc(resolutionId, resolutionDocId);
        ResolutionDoc resolutionDoc = resolutionSvc.getResolutionDocByResolutionDocId(resolutionDocId);
        if (resolutionDoc == null) {
            return new RemoteResult(true);
        }
        return new RemoteResult(false);
    }

    /**
     * 删除决议
     *
     * @param ids 决议id
     * @return 执行结果
     */
    @PostMapping("/deleteResolution")
    public RemoteResult deleteResolution(Long[] ids) {
        resolutionSvc.deleteResolutionByIds(ids);
        return new RemoteResult(true);
    }

    /**
     * 查询决议列表
     *
     * @param resolutionsCdt 架构决议查询条件
     * @return 决议信息
     */
    @GetMapping("/searchResolutions")
    public RemoteResult searchResolutions(ResolutionCdt resolutionsCdt) {
        return new RemoteResult(resolutionSvc.searchResolutions(resolutionsCdt));
    }

    /**
     * 下载架构决议文档
     *
     * @param resolutionDocId 架构决议文档id
     * @param request         请求
     * @param response        响应
     */
    @GetMapping("/downloadResolutionDoc")
    public void downloadResolutionDoc(Long resolutionDocId,
                                      HttpServletRequest request, HttpServletResponse response) {
        ResolutionDoc resolutionDoc = resolutionSvc.getResolutionDocById(resolutionDocId);

        String realPath = resolutionDoc.getRealPath();
        rsmUtils.downloadRsmAndUpdateLocalRsm(realPath);

        String[] split = realPath.split("\\.");
        File file = new File(realPath);
        Resource resource = new FileResource(file);
        ControllerUtils.returnResource(request, response, resource, null,
                false, resolutionDoc.getName() + "." + split[1]);
    }

    /**
     * 判断决议中文件是否存在
     *
     * @param resolutionId 架构决议id
     * @param fileName     文件名
     * @return 执行结果
     */
    @GetMapping("/docIsExist")
    public RemoteResult docIsExist(Long resolutionId, String fileName) {
        BinaryUtils.checkEmpty(resolutionId, "resolutionId");
        BinaryUtils.checkEmpty(fileName, "fileName");
        String docName = fileName.substring(0, fileName.lastIndexOf('.'));
        String docType = fileName.substring(fileName.lastIndexOf('.') + 1);
        Resolution resolution = resolutionSvc.getById(resolutionId);
        Set<Map<String, Object>> docs = resolution.getDocs();
        for (Map<String, Object> docMap : docs) {
            if (docName.equals(docMap.get("docName")) && docType.equals(docMap.get("docType"))) {
                return new RemoteResult(true);
            }
        }
        return new RemoteResult(false);
    }
}
