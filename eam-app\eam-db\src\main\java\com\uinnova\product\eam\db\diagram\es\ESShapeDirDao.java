package com.uinnova.product.eam.db.diagram.es;

import com.uinnova.product.eam.base.diagram.model.ESShapeDir;
import com.uinnova.product.eam.base.diagram.model.ESShapeDirQuery;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Repository;

import jakarta.annotation.PostConstruct;

/**
 * @Classname Es图形数据访问层
 * @Description 该类用来操作es中的我的形状index
 * <AUTHOR>
 * @Date 2021-08-25-13:57
 */
@Repository
public class ESShapeDirDao extends AbstractESBaseDao<ESShapeDir, ESShapeDirQuery> {
    @Override
    public String getIndex() {
        return "uino_my_shape_dir";
    }

    @Override
    public String getType() {
        return "_doc";
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }

}
