package com.uino.service.sys.microservice.impl;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import com.binary.core.util.BinaryUtils;
import com.uino.service.sys.microservice.ILogoSvc;
import com.uino.service.sys.microservice.IResourceSvc;
import com.uino.util.rsm.RsmUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

import com.uino.dao.sys.ESLogoSvc;
import com.uino.bean.sys.base.Logo;

import lombok.extern.slf4j.Slf4j;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
@Slf4j
public class LogoSvcSvc implements ILogoSvc {
    @Autowired
    private ESLogoSvc esLogoSvc;

    @Value("${http.resource.space:}")
    private String httpPath;

    @Value("${http.resource.sync:}")
    private String syncHttpPath;

    @Value("${local.resource.space:}")
    private String localPath;

    @Autowired
    private IResourceSvc resourceSvc;

    @Autowired
    private RsmUtils rsmUtils;

    @Override
    public Map<String, Logo> getLogos() {
        Map<String, Logo> res = new HashMap<>();
        List<Logo> logos = esLogoSvc.getListByQuery(QueryBuilders.boolQuery());
        logos.forEach(logo -> {
            if (StringUtils.isNotBlank(logo.getUrl())) {
                logo.setUrl(httpPath + logo.getUrl());
            }
            if(BinaryUtils.isEmpty(logo.getFileId())){
                logo.setFileId(logo.getId());
            }
            res.put(logo.getType(), logo);
        });
        return res;
    }

    @Override
    public Map<String, Logo> updateLogo(String logoType, MultipartFile file) {
        Assert.notNull(logoType, "logo type not null");
        List<Logo> logos = esLogoSvc.getListByQuery(QueryBuilders.termQuery("type.keyword", logoType));
        Assert.notEmpty(logos, logoType + " inexistence");
        Logo logo = logos.get(0);
        if (file == null) {
            logo.setUrl(logo.getDefaultUrl());
        } else {
            String newFileType = file.getOriginalFilename().split("\\.")[file.getOriginalFilename().split("\\.").length - 1];
            String newFileUrl = "/" + UUID.randomUUID() + "." + newFileType;
            File newFile = new File(localPath + newFileUrl);
            if (!newFile.getParentFile().exists()) {
                newFile.getParentFile().mkdirs();
            }
            FileOutputStream fos = null;
            try {
                fos = new FileOutputStream(newFile);

                fos.write(file.getBytes());

                // 新logo文件上传至对象存储
                rsmUtils.uploadRsmFromFile(newFile);

                resourceSvc.saveSyncResourceInfo(newFileUrl, syncHttpPath + newFileUrl, false, 0);
            } catch (Exception e) {
                log.error("替换logo写入文件异常", e);
                throw new RuntimeException("替换logo写入文件异常");
            } finally {
                if (fos != null) {
                    try {
                        fos.close();
                    } catch (IOException e) {
                        log.error("关闭替换logo文件流异常", e);
                    }
                }
            }
            String oldFileUrl = logo.getUrl();
            logo.setUrl(newFileUrl);
            try {
                if (!oldFileUrl.equals(logo.getDefaultUrl())) {
                    new File(oldFileUrl).delete();

                    // 旧logo从对象存储删除
                    rsmUtils.deleteRsm(oldFileUrl);

                    resourceSvc.saveSyncResourceInfo(oldFileUrl, syncHttpPath + oldFileUrl, false, 1);
                }
            } catch (Exception e) {
                log.error("删除旧logo失败【{}】【{}】【{}】", logo.getType(), oldFileUrl, e);
            }
        }

        esLogoSvc.saveOrUpdate(logo);

        return this.getLogos();
    }

    @Override
    public Map<String, Logo> deleteLogo(String logoType) {
        Logo logo = esLogoSvc.getListByQuery(QueryBuilders.termQuery("type.keyword", logoType)).get(0);
        logo.setUrl("");
        esLogoSvc.saveOrUpdate(logo);
        return this.getLogos();
    }

    @Override
    public Map<String, Logo> updateLogoByPath(String logoType, String path, Long fileId) {
        Assert.notNull(logoType, "logo type not null");
        Assert.notNull(path, "path not null");
        List<Logo> logos = esLogoSvc.getListByQuery(QueryBuilders.termQuery("type.keyword", logoType));
        Assert.notEmpty(logos, logoType + " inexistence");
        Logo logo = logos.get(0);
        if(path.startsWith(httpPath)){
            path = path.replace(httpPath, "");
        }
        logo.setUrl(path);
        logo.setFileId(fileId);
        esLogoSvc.saveOrUpdate(logo);
        return this.getLogos();
    }
}
