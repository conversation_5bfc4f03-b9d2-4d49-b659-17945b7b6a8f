package com.uino.provider.server.web.sys.mvc;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.binary.jdbc.Page;
import com.uino.service.sys.microservice.ILoginAuthConfigSvc;
import com.uino.bean.sys.base.LdapUserMapping;
import com.uino.bean.sys.base.LoginAuthConfig;
import com.uino.bean.sys.business.ActiveLoginAuthConfigDto;
import com.uino.bean.sys.business.LoginAuthConfigDto;
import com.uino.provider.feign.sys.LoginAuthConfigFeign;

/**
 * 
 * <AUTHOR>
 * @since 2020年6月24日上午11:44:00
 *
 */
@RestController
@RequestMapping("feign/sys/loginAuthConfig")
public class LoginAuthConfigFeignMvc implements LoginAuthConfigFeign {

    @Autowired
    private ILoginAuthConfigSvc authConfigSvc;

    @Override
    public LoginAuthConfig queryById(Long id) {
        return authConfigSvc.queryById(id);
    }

    @Override
    public Page<LoginAuthConfig> queryPage(LoginAuthConfigDto authConfigCdt) {
        return authConfigSvc.queryPage(authConfigCdt);
    }

    @Override
    public Long saveOrUpdate(LoginAuthConfig authConfig) {
        return authConfigSvc.saveOrUpdate(authConfig);
    }

    @Override
    public void activeConfig(ActiveLoginAuthConfigDto active) {
        authConfigSvc.activeConfig(active);
    }

    @Override
    public boolean testConnection(LoginAuthConfig authConfig) {
        return authConfigSvc.testConnection(authConfig);
    }

    @Override
    public LdapUserMapping testConnectionAndMappingUser(LoginAuthConfig authConfig) {
        return authConfigSvc.testConnectionAndMappingUser(authConfig);
    }

    @Override
    public void removeById(Long id) {
        authConfigSvc.removeById(id);
    }
}
