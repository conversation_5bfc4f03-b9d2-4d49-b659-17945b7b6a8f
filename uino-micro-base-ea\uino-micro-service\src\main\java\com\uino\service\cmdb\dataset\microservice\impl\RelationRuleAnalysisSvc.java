package com.uino.service.cmdb.dataset.microservice.impl;

import com.alibaba.fastjson.JSONObject;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uino.service.cmdb.dataset.microservice.IDataSetSvc;
import com.uino.service.cmdb.dataset.microservice.IRelationRuleAnalysisBase;
import com.uino.service.cmdb.dataset.microservice.IRelationRuleAnalysisSvc;
import com.uino.service.cmdb.microservice.ICIRltSvc;
import com.uino.service.cmdb.microservice.ICISvc;
import com.uino.bean.cmdb.base.dataset.DataSetMallApiRelationRule;
import com.uino.bean.cmdb.base.dataset.batch.SimpleFriendInfo;
import com.uino.bean.cmdb.base.dataset.chart.Chart;
import com.uino.bean.cmdb.business.dataset.FriendInfo;
import com.uino.bean.cmdb.business.dataset.QueryCondition;
import com.uino.bean.cmdb.business.dataset.RltRuleTableData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * @Classname GraphAnalysisSvc
 * @Description 关系遍历规则查数据
 * @Date 2020/6/3 11:32
 * <AUTHOR> sh
 */
@Service
public class RelationRuleAnalysisSvc implements IRelationRuleAnalysisSvc {

    @Autowired
    private IDataSetSvc iDataSetSvc;
    
    @Autowired
    private IRelationRuleAnalysisBase relationRuleAnalysisBase;

    @Autowired
    private ICISvc iciSvc;

    @Autowired
    private ICIRltSvc iciRltSvc;

    @Override
    public Map<Long, List<QueryCondition>> findTravalTree(DataSetMallApiRelationRule relationRule) {
        return relationRuleAnalysisBase.findTravalTree(relationRule);
    }

    @Override
    public FriendInfo queryCiFriendByCiId(CcCiInfo sCi, Long dataSetId) {
        JSONObject jsonObject = iDataSetSvc.findDataSetById(dataSetId, true);
        Assert.isNull(jsonObject, "规则不存在");
        DataSetMallApiRelationRule dataSetMallApiRelationRule = JSONObject.parseObject(jsonObject.toJSONString(), DataSetMallApiRelationRule.class);
        return queryCiFriendByCiIdAndRule(sCi, dataSetMallApiRelationRule);
    }

    @Override
    public FriendInfo queryCiFriendByCiIdAndRule(CcCiInfo sCi, DataSetMallApiRelationRule relationRule) {
        Map<Long, FriendInfo> friendInfoMap = queryCiFriendByCiIdsAndRule(Arrays.asList(sCi), relationRule, true);
        return friendInfoMap.get(sCi.getCi().getId());
    }

    @Override
    public Map<Long, FriendInfo> queryCiFriendByCiIds(List<CcCiInfo> sCis, Long dataSetId, boolean isIncludeAllStartCI) {
        JSONObject jsonObject = iDataSetSvc.findDataSetById(dataSetId, true);
        Assert.isTrue(null != jsonObject, "规则不存在");
        DataSetMallApiRelationRule dataSetMallApiRelationRule = JSONObject.parseObject(jsonObject.toJSONString(), DataSetMallApiRelationRule.class);
        Map<Long, FriendInfo> friendInfoMap = queryCiFriendByCiIdsAndRule(sCis, dataSetMallApiRelationRule, isIncludeAllStartCI);
        return friendInfoMap;
    }

    @Override
    public Map<Long, FriendInfo> queryCiFriendByCiIdsAndRule(List<CcCiInfo> sCis, DataSetMallApiRelationRule relationRule, boolean isIncludeAllStartCI) {
    	return relationRuleAnalysisBase.queryCiFriendByCiIdsAndRule(sCis, relationRule, isIncludeAllStartCI, iciSvc, iciRltSvc);
    }

    @Override
    public Map<Long, FriendInfo> queryCiFriendByRule(DataSetMallApiRelationRule relationRule) {
    	return relationRuleAnalysisBase.queryCiFriendByRule(relationRule, iciSvc, iciRltSvc);
    }
    
    @Override
    public Map<Long, FriendInfo> queryCiFriendByRuleWithLimit(DataSetMallApiRelationRule relationRule, Integer limit) {
    	return relationRuleAnalysisBase.queryCiFriendByRuleWithLimit(relationRule, limit, iciSvc, iciRltSvc);
    }

    @Override
    public FriendInfo mergeFriendInfoMap(Map<Long, FriendInfo> friendInfoMap) {
        return relationRuleAnalysisBase.mergeFriendInfoMap(friendInfoMap);
    }

    @Override
    public RltRuleTableData disassembleFriendInfoDataByPath(DataSetMallApiRelationRule relationRule, List<Long> ciIds, FriendInfo friendInfo) {
        return relationRuleAnalysisBase.disassembleFriendInfoDataByPath(relationRule,ciIds,friendInfo);
    }

    @Override
    public RltRuleTableData disassembleFriendInfoDataByPath(List<CcCiInfo> sCis, DataSetMallApiRelationRule relationRule) {
        return relationRuleAnalysisBase.disassembleFriendInfoDataByPath(sCis,relationRule,iciSvc,iciRltSvc);
    }


    @Override
    public JSONObject countStatistics(DataSetMallApiRelationRule dataSetMallRelationApi, Map<Long, SimpleFriendInfo> simpleFriendInfoMap, Chart chart) {
        return relationRuleAnalysisBase.countStatistics(dataSetMallRelationApi,simpleFriendInfoMap,chart,iciSvc,iciRltSvc);
    }


}
