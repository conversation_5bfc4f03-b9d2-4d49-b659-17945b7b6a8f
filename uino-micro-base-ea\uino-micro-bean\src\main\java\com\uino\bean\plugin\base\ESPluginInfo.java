package com.uino.bean.plugin.base;

import com.binary.framework.bean.annotation.Comment;
import com.uino.bean.permission.business.IValidDto;
import com.uino.plugin.bean.OperatePluginDetails;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "插件信息", description = "插件信息")
public class ESPluginInfo implements Serializable, IValidDto {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id", example = "123")
    @Comment("id")
    private Long id;

    @ApiModelProperty(value = "domainId", example = "123")
    @Comment("domainId")
    private Long domainId;

    @ApiModelProperty(value = "插件名称", example = "name")
    @Comment("name")
    private String name;

    @ApiModelProperty(value = "插件文件名称（jar名称）")
    @Comment("fileName")
    private String fileName;

    @ApiModelProperty(value = "用户名")
    @Comment("userCode")
    private String userCode;

    @ApiModelProperty(value = "加载状态", example = "0")
    @Comment("loadStatus")
    private Integer loadStatus;

    @ApiModelProperty(value = "所属服务", example = "common-web")
    @Comment("所属服务")
    private List<String> ownedService;

    @ApiModelProperty(value = "自动加载", example = "false")
    @Comment("自动加载")
    private Boolean autoload;

    @ApiModelProperty(value = "加载异常信息", example = "error")
    @Comment("加载异常信息")
    private List<OperatePluginDetails> errorDescription = new ArrayList<>();

    @ApiModelProperty(value = "创建时间", example = "123")
    @Comment("创建时间")
    private Long createTime;

    @ApiModelProperty(value = "修改时间", example = "123")
    @Comment("修改时间")
    private Long modifyTime;


    @Override
    public void valid() {
    }
}
