package com.uino.bean.chart.bean;

import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "雷达图图表数据项")
public class UinoChartDataItems {

	@ApiModelProperty(value = "数据类别")
	private String name;

	@ApiModelProperty(value = "维度数值")
	private List<Double> value;

}
