package com.uinnova.product.eam.service.fx;

import com.uinnova.product.eam.model.bm.PushParams;
import com.uinnova.product.eam.model.vo.PushCheckVO;

import java.util.List;
import java.util.Map;

/**
 *  模型/视图冲突
 */
public interface CheckConflictSvc {

    /**
     *  制品数量校验
     * @param pushParams
     * @return
     */
    List<PushCheckVO> checkProductNum(PushParams pushParams);

    /**
     *  CI必填项校验
     * @param pushParams
     * @return
     */
    List<PushCheckVO> checkRequiredField(PushParams pushParams);

    /**
     *  CI业务主键冲突校验
     * @param pushParams
     * @return
     */
    List<PushCheckVO> checkPrimaryKey(PushParams pushParams);

    /**
     *  视图自身版本校验
     * @param pushParams
     * @return
     */
    List<PushCheckVO> checkDiagramVersion(PushParams pushParams);

    /**
     *  视图内CI数据版本校验
     * @param pushParams
     * @return
     */
    Map<String, Object> checkCIVersion(PushParams pushParams);

    /**
     *  模型父级存在性校验
     * @param pushParams
     * @return
     */
    List<PushCheckVO> checkModelPlvlExist(PushParams pushParams);

    /**
     *  模型视图中的普通视图单独发布仓库位置存在性校验
     * @param pushParams
     * @return
     */
    List<PushCheckVO> checkModelDiagramLocExist(PushParams pushParams);

    /**
     *  模型树完整性校验
     * @param pushParams
     * @return
     */
    List<PushCheckVO> checkModelHierarchyComplete(PushParams pushParams);

    /**
     *  默认回显发布位置合法性校验
     * @param pushParams
     * @return
     */
    List<PushCheckVO> checkPushLocation(PushParams pushParams);

    /**
     * 驳回流程重新提交时，校验当前用户是否为流程发起人(协作发布权限)
     * @param pushParams
     */
    void checkProcessStartUserIsCurUserCaseUnPassSubmit(PushParams pushParams);

    /**
     * 关系/ci是否被删除校验
     * @param pushParams
     * @return
     */
    List<PushCheckVO> checkCiAndRltIsDel(PushParams pushParams);

}
