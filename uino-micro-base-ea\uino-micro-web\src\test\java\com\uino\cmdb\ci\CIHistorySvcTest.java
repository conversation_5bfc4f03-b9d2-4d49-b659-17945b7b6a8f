package com.uino.cmdb.ci;

import static org.junit.Assert.assertEquals;

import java.util.ArrayList;
import java.util.List;

import jakarta.servlet.http.Cookie;

import org.junit.After;
import org.junit.AfterClass;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.comm.model.ci.CcCi;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uino.dao.cmdb.ESCIHistorySvc;
import com.uino.dao.cmdb.ESCISvc;
import com.uino.dao.cmdb.ESCmdbCommSvc;
import com.uino.service.cmdb.microservice.impl.CIHistorySvc;
import com.uino.bean.cmdb.base.ESCIHistoryInfo;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.query.ESCIHistorySearchBean;

@RunWith(SpringJUnit4ClassRunner.class)
public class CIHistorySvcTest {

    @InjectMocks
    CIHistorySvc historySvc;

    private ESCIHistorySvc ciHistorySvc;

    private ESCmdbCommSvc commSvc;

    private ESCISvc esCiSvc;

    @BeforeClass
    public static void setUpBeforeClass() throws Exception {
        MockHttpServletRequest request = new MockHttpServletRequest();
        Cookie cookie = new Cookie("token", "ca14ed042fe7912426b484f6ea5c754b4fb51ae4a72037e9127da51743e0d1f9650e6e78ed4466ac970acb6a0b2f46fd26958c4ab2d115cc71b34ca8a02db24c");
        request.setCookies(cookie);
        ServletRequestAttributes attributes = new ServletRequestAttributes(request);
        RequestContextHolder.setRequestAttributes(attributes);
    }

    @AfterClass
    public static void tearDownAfterClass() throws Exception {}

    @Before
    public void setUp() throws Exception {
        ciHistorySvc = Mockito.mock(ESCIHistorySvc.class);
        ReflectionTestUtils.setField(historySvc, "ciHistorySvc", ciHistorySvc);

        commSvc = Mockito.mock(ESCmdbCommSvc.class);
        ReflectionTestUtils.setField(historySvc, "commSvc", commSvc);

        esCiSvc = Mockito.mock(ESCISvc.class);
        ReflectionTestUtils.setField(historySvc, "esCiSvc", esCiSvc);
    }

    @After
    public void tearDown() throws Exception {}

    @Test
    public void testSaveOrUpdate() {
        Mockito.when(ciHistorySvc.saveOrUpdateHistoryInfo(Mockito.any(ESCIInfo.class), Mockito.any())).thenReturn(123L);

        Long res = historySvc.saveOrUpdate(new ESCIInfo(), ESCIHistoryInfo.ActionType.SAVE_OR_UPDATE);
        assertEquals(123L, res.longValue());
    }

    @Test
    public void testSaveOrUpdateBatch() {
        Mockito.when(ciHistorySvc.saveOrUpdateHistoryInfosBatch(Mockito.anyList(), Mockito.any())).thenReturn(1);

        Integer res1 = historySvc.saveOrUpdateBatch(new ArrayList<ESCIInfo>(), ESCIHistoryInfo.ActionType.SAVE_OR_UPDATE);
        assertEquals(1, res1.intValue());

        List<ESCIInfo> ciInfos = new ArrayList<>();
        ESCIInfo esciInfo = new ESCIInfo();
        ciInfos.add(esciInfo);
        Integer res2 = historySvc.saveOrUpdateBatch(ciInfos, ESCIHistoryInfo.ActionType.SAVE_OR_UPDATE);
        assertEquals(1, res2.intValue());
    }

    @Test
    public void testGetCIInfoHistoryByCIVersion() {
        List<ESCIHistoryInfo> historyInfos=new ArrayList<>();
        ESCIHistoryInfo historyInfo=new ESCIHistoryInfo();
        historyInfo.setCiCode("ciCode");
        historyInfos.add(historyInfo);
        Mockito.when(ciHistorySvc.getListByQuery(Mockito.any())).thenReturn(historyInfos);
        
        Mockito.doNothing().when(esCiSvc).transCIAttrs(Mockito.anyList(), Mockito.anyBoolean());
        CcCiInfo ciInfo = new CcCiInfo();
        CcCi ci = new CcCi();
        ci.setCiCode("ciCode");
        ciInfo.setCi(ci);
        Mockito.when(commSvc.tranCcCiInfo(Mockito.any(ESCIInfo.class), Mockito.anyBoolean())).thenReturn(ciInfo);
        
        ESCIHistoryInfo res = historySvc.getCIInfoHistoryByCIVersion("ciCode", 123L, 1l);
        assertEquals("ciCode", res.getCiCode());
    }

    @Test
    public void testGetCIInfoHistoryByCIId() {
        Mockito.when(ciHistorySvc.countByCondition(Mockito.any())).thenReturn(1L);
        Page<ESCIHistoryInfo> page = new Page<>();
        List<ESCIHistoryInfo> historyInfos = new ArrayList<>();
        ESCIHistoryInfo historyInfo = new ESCIHistoryInfo();
        historyInfo.setCiCode("ciCode");
        historyInfos.add(historyInfo);
        page.setData(historyInfos);
        Mockito.when(ciHistorySvc.getSortListByQuery(Mockito.anyInt(), Mockito.anyInt(), Mockito.any(), Mockito.anyString(), Mockito.anyBoolean())).thenReturn(page);

        Mockito.doNothing().when(esCiSvc).transCIAttrs(Mockito.anyList(), Mockito.anyBoolean());

        List<CcCiInfo> ciInfos = new ArrayList<>();
        CcCiInfo ciInfo = new CcCiInfo();
        CcCi ci = new CcCi();
        ci.setCiCode("ciCode");
        ciInfo.setCi(ci);
        ciInfos.add(ciInfo);
        Mockito.when(commSvc.transEsInfoList(Mockito.anyList(), Mockito.anyBoolean())).thenReturn(ciInfos);

        List<ESCIHistoryInfo> res = historySvc.getCIInfoHistoryByCICode("ciCode", 123L);
        assertEquals("ciCode", res.get(0).getCiCode());
    }

    @Test
    public void testGetCIHistoryPageBySearchBean() {
        Page<ESCIHistoryInfo> historyPage = new Page<>();
        List<ESCIHistoryInfo> historyInfos = new ArrayList<>();
        ESCIHistoryInfo historyInfo = new ESCIHistoryInfo();
        historyInfo.setCiCode("ciCode");
        historyInfos.add(historyInfo);
        historyPage.setData(historyInfos);
        Mockito.when(ciHistorySvc.getSortListByQuery(Mockito.anyInt(), Mockito.anyInt(), Mockito.any(), Mockito.anyString(), Mockito.anyBoolean())).thenReturn(historyPage);
        Mockito.doNothing().when(esCiSvc).transCIAttrs(Mockito.anyList(), Mockito.anyBoolean());

        List<CcCiInfo> ciInfos = new ArrayList<>();
        CcCiInfo ciInfo = new CcCiInfo();
        CcCi ci = new CcCi();
        ci.setCiCode("ciCode");
        ciInfo.setCi(ci);
        ciInfos.add(ciInfo);
        Mockito.when(commSvc.transEsInfoList(Mockito.anyList(), Mockito.anyBoolean())).thenReturn(ciInfos);

        ESCIHistorySearchBean bean = new ESCIHistorySearchBean();
        bean.setCiId(123L);
        bean.setClassId(123L);
        bean.setCiCode("ciCode");
        bean.setVersion(1L);

        historySvc.getCIHistoryPageBySearchBean(bean);

    }

    @Test
    public void testGetCIVersionList() {
        Mockito.when(ciHistorySvc.countByCondition(Mockito.any())).thenReturn(1L);

        Page<ESCIHistoryInfo> page = new Page<>();
        List<ESCIHistoryInfo> historyInfos = new ArrayList<>();
        ESCIHistoryInfo historyInfo = new ESCIHistoryInfo();
        historyInfo.setCiCode("ciCode");
        historyInfo.setVersion(1L);
        historyInfo.setCreateTime(20200730121212L);
        historyInfos.add(historyInfo);
        page.setData(historyInfos);
        Mockito.when(ciHistorySvc.getSortListByQuery(Mockito.anyInt(), Mockito.anyInt(), Mockito.any(), Mockito.anyString(), Mockito.anyBoolean())).thenReturn(page);

        List<String> res = historySvc.getCIVersionList("ciCode", 123L);
        assertEquals("1-20200730121212", res.get(0));
    }

}
