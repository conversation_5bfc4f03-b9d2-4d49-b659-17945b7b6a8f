package com.uinnova.product.eam.service;

import com.alibaba.fastjson.JSONObject;
import com.uinnova.product.eam.model.TechnologyStackDto;
import com.uinnova.product.eam.model.dto.EamDataSetClassDto;
import com.uinnova.product.eam.model.dto.EamTableFriendDto;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uino.bean.cmdb.base.ESCIClassInfo;
import com.uino.bean.cmdb.base.LibType;
import com.uino.bean.cmdb.business.dataset.DataSetExeResultSheetPage;
import com.uino.bean.cmdb.business.dataset.FriendInfo;
import org.springframework.http.ResponseEntity;

import java.util.List;
import java.util.Map;


/**
 * 数据超市相关服务，所有通过数据超市或者上下几层查询CI的接口都在此服务中
 *
 * <AUTHOR>
 * @version 2020-7-14
 */
public interface ICIDataSetSvc {

    /**
     * 通过数据超市查询指定CI的关联数据
     *
     * @param libType 库类型(私有/设计/运行)
     * @param dataSetId 数据集ID
     * @param startCiId 起始CI的id
     *
     * @return 关联CI的信息
     */
    FriendInfo queryFriendByStartCiId(LibType libType, Long dataSetId, Long startCiId);


    /**
     * 查询此应用关联的技术栈
     *
     * @param libType 库类型(私有/设计/运行)
     * @param dataSetId 技术栈使用的数据集Id
     * @param startCiId 起始CI的id
     * @return 关联的CI技术栈信息
     */
    TechnologyStackDto queryAppTechnologyStack(LibType libType, Long dataSetId, Long startCiId);

    /**
     * 查询此应用通过数据超市关联的第一层CI信息
     *
     * @param libType 库类型(私有/设计/运行)
     * @param dataSetId 数据集Id
     * @param startCiId 起始CI的id
     * @return 关联的CI信息
     */
    List<CcCiInfo> queryRelatedCIByDataSet(LibType libType, Long dataSetId, Long startCiId);

    /**
     * 通过名称获取数据集信息
     *
     * @param dataSetName 数据集名称
     * @param url 数据集提供api的url前缀
     * @return 数据集对象
     */
    JSONObject queryDataSetByName(String dataSetName, String url);

    /**
     * 通过系统ciId查找到技术栈参考标准所依赖的ci信息
     * @param ciId
     * @return
     */
    List<CcCiInfo> queryRelatedListCIInfo(long ciId,LibType libType);

    /**
     * 比较两个CiInfo之间的差异，返回数据表
     * @param realComponentList  系统标准技术站列表
     * @param componentList 系统实际使用技术栈列表
     * @return
     */
    Map<String, Map<String, Object>> getResultMap(List<CcCiInfo> realComponentList, List<CcCiInfo> componentList);

    /**
     * 通过数据集的名称获取到数据集信息并返回根节点分类信息
     * @param ids
     * @param names
     * @param like
     * @return
     */
    List<EamDataSetClassDto> queryDataSetByNames(List<Long> ids, List<String> names, String like);

    /**
     *通过规则和入口查询关系呈现为表格
     * @param body
     * @return
     */
    DataSetExeResultSheetPage queryTableFriend(EamTableFriendDto body);

    /**
     * 通过规则和入口批量查询关系呈现为表格
     * @param body
     * @return
     */
    List<DataSetExeResultSheetPage> queryTableFriendList(List<EamTableFriendDto> body);

    /**
     * 获取数据集全部分类信息
     * @param dataSetId 数据集id
     * @return 分类信息
     */
    List<ESCIClassInfo> getDataSetClass(Long dataSetId);

    /**
     * 获取数据集层级
     * @param dataSetId 数据集id
     * @return 层级数
     */
    int getDataSetDepth(Long dataSetId);

    /**
     *  导出表格数据
     * @param result
     * @param cardName
     * @return
     */
    ResponseEntity<byte[]> tableListExport(List<DataSetExeResultSheetPage> result, String cardName);
}
