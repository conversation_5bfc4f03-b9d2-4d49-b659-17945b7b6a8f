package com.uinnova.product.eam.model.vo;

import com.binary.framework.bean.annotation.Comment;
import com.uino.bean.cmdb.base.dataset.relation.RelationRuleAttrCdt;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 矩阵分析配置vo
 * <AUTHOR>
 */
@Data
public class MatrixAnalysisConfigVo implements Serializable {

    @Comment("配置类型:matrix")
    private String pattern;

    @Comment("矩阵类型:1=通过关系，2=通过属性")
    private Integer matrixType;

    @Comment("对象分类-横向")
    private Long rowClassId;

    @Comment("源端约束规则")
    private List<RelationRuleAttrCdt> rowRules;

    @Comment("对象分类-纵向")
    private Long colClassId;

    @Comment("目标端约束规则")
    private List<RelationRuleAttrCdt> colRules;

    @Comment("关系分类id")
    private Long rltClassId;

    @Comment("横向属性id")
    private Long rowAttrId;

    @Comment("纵向属性id")
    private Long colAttrId;

    @Comment("关系属性id")
    private Long rltAttrId;

}
