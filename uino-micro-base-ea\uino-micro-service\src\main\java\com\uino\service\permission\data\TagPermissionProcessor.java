package com.uino.service.permission.data;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.comm.bean.CIState;
import com.uinnova.product.vmdb.comm.model.ci.CcCi;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uino.bean.cmdb.base.*;
import com.uino.bean.cmdb.query.ESTagSearchBean;
import com.uino.bean.permission.base.SysRoleDataModuleRlt;
import com.uino.bean.permission.enums.PermissionOperateEnum;
import com.uino.dao.cmdb.ESCISvc;
import com.uino.service.cmdb.microservice.ITagSvc;
import com.uino.service.permission.microservice.IRoleSvc;
import com.uino.util.sys.LibTypeUtil;
import com.uino.util.sys.SysUtil;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 标签权限控制实现
 */
@Slf4j
@Component
public class TagPermissionProcessor {
    @Autowired
    private IRoleSvc roleSvc;

    @Autowired
    private ITagSvc tagSvc;

    @Autowired
    private ESCISvc ciSvc;

    private static final Integer CHECK = 1;

    /**
     * 获取分类标签权限规则集
     * @param classIds 分类id
     * @param permissionEnum 权限类型
     * @return 规则集
     */
    public List<ESTagRuleInfo> getTagRulePermission(List<Long> classIds, PermissionOperateEnum permissionEnum){
        List<ESTagRuleInfo> result = new ArrayList<>();
        Long userId = SysUtil.getCurrentUserInfo().getId();
        List<Long> tagIds = this.getTagIds(classIds, userId, permissionEnum);
        if(CollectionUtils.isEmpty(tagIds)){
            return result;
        }
        List<ESCITagInfo> ruleList = tagSvc.getCITagRuleByIds(tagIds);
        for (ESCITagInfo each : ruleList) {
            for (ESTagRuleInfo rule : each.getRules()) {
                if(classIds.contains(rule.getClassId())){
                    result.add(rule);
                }
            }
        }
        return result;
    }

    /**
     * 获取分类标签权限规则集id
     * @param classIds 分类id
     * @param userId 用户id
     * @param permissionEnum 权限类型
     * @return 规则集id
     */
    public List<Long> getTagIds(List<Long> classIds, Long userId, PermissionOperateEnum permissionEnum){
        List<SysRoleDataModuleRlt> dataModules = roleSvc.getRoleByUserAndCode(userId, "CITAG");
        if(CollectionUtils.isEmpty(classIds) || CollectionUtils.isEmpty(dataModules)){
            return Collections.emptyList();
        }
        List<SysRoleDataModuleRlt> tagList = filterModuleByPermission(dataModules, permissionEnum);
        if(CollectionUtils.isEmpty(tagList)){
            return Collections.emptyList();
        }
        Map<String, List<SysRoleDataModuleRlt>> tagGroup = tagList.stream().collect(Collectors.groupingBy(SysRoleDataModuleRlt::getDataValue));
        List<SysRoleDataModuleRlt> result = new ArrayList<>();
        //过滤出所有角色都有的标签,并且取并集,如果某个角色没有分类权限则不考虑其标签权限
        for (Map.Entry<String, List<SysRoleDataModuleRlt>> entry : tagGroup.entrySet()) {
            SysRoleDataModuleRlt moduleRlt = mergeDataModuleRlt(entry.getValue());
            if(moduleRlt.getId() != null){
                result.add(moduleRlt);
            }
        }
        return result.stream().map(SysRoleDataModuleRlt::getUid).map(Long::parseLong).collect(Collectors.toList());
    }

    /**
     * 合并数据模块权限结果。
     * 该方法通过遍历模块权限列表（modules），针对给定的角色集合（roles），
     * 组合出一个新的权限结果对象（moduleRlt），该对象表示指定角色集合对数据模块的访问权限。
     *
     * @param modules 数据模块权限列表，包含多个角色对数据模块的操作权限信息。
     * @return SysRoleDataModuleRlt 返回合并后的角色数据模块权限结果对象。
     */
    private static SysRoleDataModuleRlt mergeDataModuleRlt(List<SysRoleDataModuleRlt> modules) {
        SysRoleDataModuleRlt moduleRlt = new SysRoleDataModuleRlt();
        moduleRlt.setIscreate(1);
        moduleRlt.setIsdelete(0);
        moduleRlt.setIsupdate(0);
        moduleRlt.setIssee(0);
        for (SysRoleDataModuleRlt each : modules) {
            //判断当前角色是否和当前标签对应
            moduleRlt.setId(each.getId());
            moduleRlt.setUid(each.getUid());
            if(each.getIsdelete() != null && each.getIsdelete() == 1){
                moduleRlt.setIsdelete(1);
            }
            if(each.getIsupdate() != null && each.getIsupdate() == 1){
                moduleRlt.setIsupdate(1);
            }
            if(each.getIssee() != null && each.getIssee() == 1){
                moduleRlt.setIssee(1);
            }
        }
        return moduleRlt;
    }

    private List<SysRoleDataModuleRlt> filterModuleByPermission(List<SysRoleDataModuleRlt> dataModules, PermissionOperateEnum permissionEnum){
        List<SysRoleDataModuleRlt> filterList;
        switch (permissionEnum){
            case VIEW:
                filterList = dataModules.stream().filter(e -> CHECK.equals(e.getIssee())).collect(Collectors.toList());
                break;
            case ADD:
                filterList = dataModules.stream().filter(e -> CHECK.equals(e.getIscreate())).collect(Collectors.toList());
                break;
            case UPDATE:
                filterList = dataModules.stream().filter(e -> CHECK.equals(e.getIsupdate())).collect(Collectors.toList());
                break;
            case DELETE:
                filterList = dataModules.stream().filter(e -> CHECK.equals(e.getIsdelete())).collect(Collectors.toList());
                break;
            default:
                filterList = new ArrayList<>();
        }
        return filterList;
    }

    /**
     * TODO 这方法太耗内存了
     * 通过标签规则查询ci
     * @param ruleList 规则集合
     * @param classIds 分类权限
     * @param libType 库
     */
    public Set<String> getCIInfoListByTag(List<ESTagRuleInfo> ruleList, List<Long> classIds, LibType libType){
        Set<String> result = new HashSet<>();
        if(CollectionUtils.isEmpty(ruleList)){
            return result;
        }
        LibTypeUtil.setLibType(libType.toString());
        ESTagSearchBean bean = new ESTagSearchBean();
        ESCITagInfo tagInfo = new ESCITagInfo();
        tagInfo.setRules(ruleList);
        bean.setTagInfo(tagInfo);
        bean.setPageNum(1);
        bean.setPageSize(10000);
        Page<CcCiInfo> pageList = tagSvc.getCIInfoListByTag(bean);
        if(!CollectionUtils.isEmpty(pageList.getData())){
            result.addAll(pageList.getData().stream().map(e -> e.getCi().getCiCode()).collect(Collectors.toSet()));
        }
        String loginCode = SysUtil.getCurrentUserInfo().getLoginCode();
        List<Long> ruleClassIds = ruleList.stream().map(ESTagRuleInfo::getClassId).distinct().collect(Collectors.toList());
        //有标签控制的分类，在查私有库时，先把草稿态的ci查出来
        if(LibType.PRIVATE.equals(libType)){
            BoolQueryBuilder query = QueryBuilders.boolQuery();
            query.must(QueryBuilders.termsQuery("classId", ruleClassIds));
            query.must(QueryBuilders.termQuery("state", CIState.CREATE_INIT.val()));
            query.must(QueryBuilders.termQuery("ownerCode.keyword", loginCode));
            Page<ESCIInfo> classCiPage = ciSvc.getSortListByQuery(1, 100000, query, null, false);
            if(!CollectionUtils.isEmpty(classCiPage.getData())){
                result.addAll(classCiPage.getData().stream().map(CcCi::getCiCode).collect(Collectors.toSet()));
            }
        }
        classIds.removeAll(ruleClassIds);
        //除了有规则限制的，其它查询全量分类下数据
        if(!CollectionUtils.isEmpty(classIds)){
            BoolQueryBuilder query = QueryBuilders.boolQuery();
            query.must(QueryBuilders.termsQuery("classId", classIds));
            if(LibType.PRIVATE.equals(libType)){
                query.must(QueryBuilders.termQuery("ownerCode.keyword", loginCode));
            }
            Page<ESCIInfo> classCiPage = ciSvc.getSortListByQuery(1, 100000, query, null, false);
            if(!CollectionUtils.isEmpty(classCiPage.getData())){
                result.addAll(classCiPage.getData().stream().map(CcCi::getCiCode).collect(Collectors.toSet()));
            }
        }
        LibTypeUtil.removeLibType();
        if(CollectionUtils.isEmpty(result)){
            result.add("-9999999999");
        }
        return result;
    }

    /**
     * 校验ci属性是否符合标签规则
     * @param attrs 属性
     * @param classInfo 分类信息
     * @return 符合true/不符false
     */
    public boolean checkEditPermission(JSONObject attrs, ESCIClassInfo classInfo){
        List<ESTagRuleInfo> ruleList = this.getTagRulePermission(Collections.singletonList(classInfo.getId()), PermissionOperateEnum.UPDATE);
        if(CollectionUtils.isEmpty(ruleList)){
            return true;
        }
        return tagSvc.checkAttrByRules(ruleList, attrs, classInfo);
    }

    /**
     * 校验ci是否符合数据权限或标签规则
     * @param userId 用户id
     * @param attrs 属性
     * @param classInfo 分类信息
     * @param permission 校验权限类型
     * @return 符合true/不符false
     */
    public boolean checkPermission(Long userId, JSONObject attrs, ESCIClassInfo classInfo, PermissionOperateEnum permission){
        List<SysRoleDataModuleRlt> dataModules = roleSvc.getRoleByUserAndCode(userId, "CICLASS");
        List<SysRoleDataModuleRlt> moduleRltList = filterModuleByPermission(dataModules, permission);
        List<Long> classIds = moduleRltList.stream().map(e -> Long.parseLong(e.getDataValue())).distinct().collect(Collectors.toList());
        //校验编辑
        if(!classIds.contains(classInfo.getId())){
            return false;
        }
        List<ESTagRuleInfo> ruleList = this.getTagRulePermission(Collections.singletonList(classInfo.getId()), permission);
        if(CollectionUtils.isEmpty(ruleList)){
            return true;
        }
        return tagSvc.checkAttrByRules(ruleList, attrs, classInfo);
    }

    /**
     * 批量校验有权限的ci
     * @param userId 用户id
     * @param ciList ci集合
     * @param classMap 分类map
     * @param permission 权限类型
     * @return 有权限的ciCode集合
     */
    public List<String> checkPermissionBatch(Long userId, List<ESCIInfo> ciList, Map<Long, ESCIClassInfo> classMap, PermissionOperateEnum permission){
        List<SysRoleDataModuleRlt> dataModules = roleSvc.getRoleByUserAndCode(userId, "CICLASS");
        List<SysRoleDataModuleRlt> moduleRltList = filterModuleByPermission(dataModules, permission);
        List<Long> classIds = moduleRltList.stream().map(e -> Long.parseLong(e.getDataValue())).distinct().collect(Collectors.toList());
        List<ESTagRuleInfo> ruleList = this.getTagRulePermission(classIds, permission);
        Map<Long, List<ESTagRuleInfo>> ruleGroup = ruleList.stream().collect(Collectors.groupingBy(ESTagRuleInfo::getClassId));
        List<String> result = new ArrayList<>();
        for (ESCIInfo each : ciList) {
            if(!classIds.contains(each.getClassId())){
                continue;
            }
            List<ESTagRuleInfo> rules = ruleGroup.get(each.getClassId());
            if(CollectionUtils.isEmpty(rules)){
                result.add(each.getCiCode());
                continue;
            }
            boolean check = tagSvc.checkAttrByRules(rules, JSON.parseObject(JSON.toJSONString(each.getAttrs())), classMap.get(each.getClassId()));
            if(check){
                result.add(each.getCiCode());
            }
        }
        return result;
    }

    public List<Long> filterDeletePermission(String ownerCode, List<Long> ids, LibType libType){
        LibTypeUtil.setLibType(libType.toString());
        List<ESCIInfo> ciList = ciSvc.getListByQuery(QueryBuilders.termsQuery("id", ids));
        if(CollectionUtils.isEmpty(ciList)){
            return new ArrayList<>();
        }
        List<Long> classIds = ciList.stream().map(CcCi::getClassId).distinct().collect(Collectors.toList());
        List<SysRoleDataModuleRlt> dataModules = roleSvc.getRoleByUserAndCode(null, "CICLASS");
        List<SysRoleDataModuleRlt> moduleRltList = filterModuleByPermission(dataModules, PermissionOperateEnum.DELETE);
        if(CollectionUtils.isEmpty(moduleRltList)){
            return new ArrayList<>();
        }
        List<Long> roleClassIds = moduleRltList.stream().map(e -> Long.parseLong(e.getDataValue())).distinct().collect(Collectors.toList());
        //过滤掉没删除权限的分类id
        classIds = classIds.stream().filter(roleClassIds::contains).collect(Collectors.toList());
        //校验编辑
        if(CollectionUtils.isEmpty(classIds)){
            return new ArrayList<>();
        }
        List<ESTagRuleInfo> ruleList = this.getTagRulePermission(classIds, PermissionOperateEnum.DELETE);
        if(CollectionUtils.isEmpty(ruleList)){
            return ids;
        }
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termsQuery("id", ids));
        if(LibType.PRIVATE.equals(libType)){
            query.must(QueryBuilders.termQuery("ownerCode.keyword", ownerCode));
        }
        ESCITagInfo tagInfo = new ESCITagInfo();
        tagInfo.setRules(ruleList);
        tagInfo.setDomainId(SysUtil.getCurrentUserInfo().getDomainId());
        query.must(tagSvc.getQueryByTag(tagInfo));
        List<ESCIInfo> resultList = ciSvc.getListByQuery(query);
        if(CollectionUtils.isEmpty(resultList)){
            return new ArrayList<>();
        }
        LibTypeUtil.removeLibType();
        return resultList.stream().map(CcCi::getId).collect(Collectors.toList());
    }

    /**
     * 获取ci属性校验标签规则
     * @param attrs 属性
     * @param classInfo 分类信息
     * @return 属性填写规则map<属性字段名-规则>
     */
    public Map<String, String> getCheckRulesInfo(JSONObject attrs, ESCIClassInfo classInfo){
        List<ESTagRuleInfo> ruleList = this.getTagRulePermission(Collections.singletonList(classInfo.getId()), PermissionOperateEnum.UPDATE);
        if(CollectionUtils.isEmpty(ruleList)){
            return new HashMap<>();
        }
        return tagSvc.getCheckRulesInfo(ruleList, attrs, classInfo);
    }

    /**
     * 获取用户拥有权限的分类
     * @param userId 用户id
     * @param permissionEnum 权限类型
     * @return 分类id
     */
    public List<Long> hasPermissionClass(Long userId, PermissionOperateEnum permissionEnum){
        List<SysRoleDataModuleRlt> dataModules = roleSvc.getRoleByUserAndCode(userId, "CICLASS");
        List<Long> result = new ArrayList<>();
        if(CollectionUtils.isEmpty(dataModules)){
            return result;
        }
        List<SysRoleDataModuleRlt> moduleRltList = filterModuleByPermission(dataModules, permissionEnum);
        result = moduleRltList.stream().map(e -> Long.parseLong(e.getDataValue())).distinct().collect(Collectors.toList());
        return result;
    }

}
