package com.uino.cmdb.rlt_class.mvc;

import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;

import org.elasticsearch.index.query.QueryBuilders;
import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import com.alibaba.fastjson.JSON;
import com.binary.framework.web.RemoteResult;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uino.StartBaseWebAppliaction;
import com.uino.dao.cmdb.ESCIRltSvc;
import com.uino.dao.cmdb.ESRltClassSvc;
import com.uino.bean.cmdb.base.ESCIClassInfo;

import lombok.extern.slf4j.Slf4j;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = { StartBaseWebAppliaction.class }, webEnvironment = WebEnvironment.RANDOM_PORT)
@Slf4j
@ActiveProfiles("provider-local")
public class SaveOrUpdateTest {
	@Autowired
	private TestRestTemplate restTemplate;
	@MockBean
	private ESRltClassSvc esRltClassSvc;
	@MockBean
	private ESCIRltSvc esCiRltSvc;
	private static String testUrl;

	@BeforeClass
	public static void initCls() {
		SaveOrUpdateTest.testUrl = "/cmdb/rltClass/saveOrUpdate";
	}

	@Before
	public void start() {
		Mockito.when(esRltClassSvc.saveOrUpdate(Mockito.any())).thenReturn(1L);
		Mockito.when(esCiRltSvc.countByCondition(Mockito.eq(QueryBuilders.termQuery("classId", 1L)))).thenReturn(0L);
		Mockito.when(esCiRltSvc.countByCondition(Mockito.eq(QueryBuilders.termQuery("classId", 2L)))).thenReturn(1L);
	}

	@Test
	public void test01() {
		ESCIClassInfo reqBean = new ESCIClassInfo();
		reqBean.setClassName("testRltClass01");
		reqBean.setClassCode("testRltClass01");
		reqBean.setClassStdCode("testRltClass01");
		List<CcCiAttrDef> attrDefs = new LinkedList<CcCiAttrDef>();
        reqBean.setCcAttrDefs(attrDefs);
		CcCiAttrDef attrDef = new CcCiAttrDef();
		attrDefs.add(attrDef);
		attrDef.setProName("attr01");
		attrDef.setProType(1);
		attrDef.setIsRequired(1);
		String responseBodyStr = restTemplate.postForObject(testUrl, reqBean, String.class);
		RemoteResult responseBody = JSON.parseObject(responseBodyStr, RemoteResult.class);
		Assert.assertTrue(responseBody.isSuccess());
	}

	@Test
	public void test02() {
		ESCIClassInfo reqBean = new ESCIClassInfo();
		reqBean.setId(1L);
		reqBean.setClassName("testRltClass01");
		reqBean.setClassCode("testRltClass01");
		reqBean.setClassStdCode("testRltClass01");
		List<CcCiAttrDef> attrDefs = new LinkedList<CcCiAttrDef>();
        reqBean.setCcAttrDefs(attrDefs);
		CcCiAttrDef attrDef = new CcCiAttrDef();
		attrDefs.add(attrDef);
		attrDef.setProName("attr01");
		attrDef.setProType(1);
		attrDef.setIsRequired(1);
		String responseBodyStr = restTemplate.postForObject(testUrl, reqBean, String.class);
		RemoteResult responseBody = JSON.parseObject(responseBodyStr, RemoteResult.class);
		Assert.assertTrue(responseBody.isSuccess());
	}

	@Test
	public void test03() {
		ESCIClassInfo reqBean = new ESCIClassInfo();
		reqBean.setId(2L);
		reqBean.setClassName("testRltClass01");
		reqBean.setClassCode("testRltClass01");
		reqBean.setClassStdCode("testRltClass01");
		List<CcCiAttrDef> attrDefs = new LinkedList<CcCiAttrDef>();
        reqBean.setCcAttrDefs(attrDefs);
		CcCiAttrDef attrDef = new CcCiAttrDef();
		attrDefs.add(attrDef);
		attrDef.setProName("attr01");
		attrDef.setProType(1);
		attrDef.setIsRequired(1);
		String responseBodyStr = restTemplate.postForObject(testUrl, reqBean, String.class);
		RemoteResult responseBody = JSON.parseObject(responseBodyStr, RemoteResult.class);
		Assert.assertFalse(responseBody.isSuccess());
	}

	@Test
	public void test04() {
		ESCIClassInfo reqBean = new ESCIClassInfo();
		reqBean.setClassName("testRltClass01");
		reqBean.setClassCode("testRltClass01");
		reqBean.setClassStdCode("testRltClass01");
		List<CcCiAttrDef> attrDefs = null;
        reqBean.setCcAttrDefs(attrDefs);
		String responseBodyStr = restTemplate.postForObject(testUrl, reqBean, String.class);
		RemoteResult responseBody = JSON.parseObject(responseBodyStr, RemoteResult.class);
		Assert.assertTrue(responseBody.isSuccess());
	}

	@Test
	public void test05() {
		ESCIClassInfo reqBean = new ESCIClassInfo();
		reqBean.setClassName("testRltClass01");
		reqBean.setClassCode("testRltClass01");
		reqBean.setClassStdCode("testRltClass01");
		List<CcCiAttrDef> attrDefs = new LinkedList<CcCiAttrDef>();
        reqBean.setCcAttrDefs(attrDefs);
		CcCiAttrDef attrDef = new CcCiAttrDef();
		attrDefs.add(attrDef);
        attrDef.setProName("testDef");
		attrDef.setProType(1);
		attrDef.setIsRequired(1);
		String responseBodyStr = restTemplate.postForObject(testUrl, reqBean, String.class);
		RemoteResult responseBody = JSON.parseObject(responseBodyStr, RemoteResult.class);
		Assert.assertTrue(responseBody.isSuccess());
	}

	@Test
	public void test06() {
		Mockito.when(esRltClassSvc.getListByQuery(Mockito.any()))
				.thenReturn(Collections.singletonList(new ESCIClassInfo()));
		ESCIClassInfo reqBean = new ESCIClassInfo();
		reqBean.setId(1L);
		reqBean.setClassName("testRltClass01");
		reqBean.setClassCode("testRltClass01");
		reqBean.setClassStdCode("testRltClass01");
		List<CcCiAttrDef> attrDefs = new LinkedList<CcCiAttrDef>();
        reqBean.setCcAttrDefs(attrDefs);
		CcCiAttrDef attrDef = new CcCiAttrDef();
		attrDefs.add(attrDef);
		attrDef.setProName("attr01");
		attrDef.setProType(1);
		attrDef.setIsRequired(1);
		String responseBodyStr = restTemplate.postForObject(testUrl, reqBean, String.class);
		RemoteResult responseBody = JSON.parseObject(responseBodyStr, RemoteResult.class);
		Assert.assertFalse(responseBody.isSuccess());
	}

	@Test
	public void test07() {
		Mockito.when(esRltClassSvc.getListByQuery(Mockito.any())).thenReturn(new ArrayList<>());
		ESCIClassInfo reqBean = new ESCIClassInfo();
		reqBean.setId(1L);
		reqBean.setClassName("testRltClass01");
		reqBean.setClassCode("testRltClass01");
		reqBean.setClassStdCode("testRltClass01");
		List<CcCiAttrDef> attrDefs = new LinkedList<CcCiAttrDef>();
        reqBean.setCcAttrDefs(attrDefs);
		CcCiAttrDef attrDef = new CcCiAttrDef();
		attrDefs.add(attrDef);
		attrDef.setProName("attr01");
		attrDef.setProType(1);
		attrDef.setIsRequired(1);
		String responseBodyStr = restTemplate.postForObject(testUrl, reqBean, String.class);
		RemoteResult responseBody = JSON.parseObject(responseBodyStr, RemoteResult.class);
		Assert.assertTrue(responseBody.isSuccess());
	}
}
