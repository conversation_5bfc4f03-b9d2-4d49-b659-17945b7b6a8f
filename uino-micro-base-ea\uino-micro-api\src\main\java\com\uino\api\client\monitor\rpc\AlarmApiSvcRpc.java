package com.uino.api.client.monitor.rpc;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.binary.jdbc.Page;
import com.uino.api.client.monitor.IAlarmApiSvc;
import com.uino.bean.monitor.base.ESAlarm;
import com.uino.bean.monitor.buiness.AlarmInfo;
import com.uino.bean.monitor.buiness.AlarmQueryDto;
import com.uino.bean.monitor.buiness.SimulationAlarmBean;
import com.uino.provider.feign.monitor.AlarmFeign;

/**
 * <AUTHOR>
 *
 */
@Service
public class AlarmApiSvcRpc implements IAlarmApiSvc {

    @Autowired
    private AlarmFeign feign;

    @Override
    public Page<AlarmInfo> searchAlarms(AlarmQueryDto queryDto) {
        // TODO Auto-generated method stub
        return feign.searchAlarms(queryDto);
    }

    @Override
    public void saveAlarm(ESAlarm saveDto) {
        // TODO Auto-generated method stub
        feign.saveAlarm(saveDto);
    }

    @Override
    public Long countByQuery(AlarmQueryDto queryDto) {
        // TODO Auto-generated method stub
        return feign.countByQuery(queryDto);
    }

    @Override
    public ESAlarm queryOpenAlarm(String metric, Long classId, Long objId) {
        // TODO Auto-generated method stub
        return feign.queryOpenAlarm(metric, classId, objId);
    }

	@Override
	public void simulationAlarms(SimulationAlarmBean bean) {
		feign.simulationAlarms(bean);
	}
}
