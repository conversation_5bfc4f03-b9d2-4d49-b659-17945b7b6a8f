package com.uinnova.product.vmdb.comm.model.es;

import com.binary.framework.bean.annotation.Comment;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Comment("CI操作日志")
public class ESCiOperateLog {

    private static final long serialVersionUID = 1L;

    @Comment("ID 自动生成")
    private String id;

    @Comment("domainId")
    private Long domainId;

    @Comment("CI_ID 以备不时之需")
    private Long ciId;

    @Comment("CI分类名")
    private String ciClassName;

    @Comment("CI_CODE")
    private String ciCode;

    @Comment("CI业务主键")
    private Map<String, String> ciPrimaryKey;

    @Comment("动态   1：添加；2：修改；3删除")
    private Integer dynamic;

    @Comment("发生时间")
    private Long time;

    @Comment("日志来源")
    private String source;

    @Comment("日志来源类型    1：用户页面；2：配置处理；8：DIX")
    private Long sourceId;

    @Comment("旧属性值")
    private Map<String, String> oldAttrs;

    @Comment("新属性值")
    private Map<String, String> newAttrs;

    @Comment("属性定义顺序")
    private List<String> proNames;

    public ESCiOperateLog() {
        oldAttrs = new HashMap<>();
        newAttrs = new HashMap<>();
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Long getDomainId() {
        return domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }

    public Long getCiId() {
        return ciId;
    }

    public void setCiId(Long ciId) {
        this.ciId = ciId;
    }

    public String getCiClassName() {
        return ciClassName;
    }

    public void setCiClassName(String ciClassName) {
        this.ciClassName = ciClassName;
    }

    public String getCiCode() {
        return ciCode;
    }

    public void setCiCode(String ciCode) {
        this.ciCode = ciCode;
    }

    public Map<String, String> getCiPrimaryKey() {
        return ciPrimaryKey;
    }

    public void setCiPrimaryKey(Map<String, String> ciPrimaryKey) {
        this.ciPrimaryKey = ciPrimaryKey;
    }

    public Integer getDynamic() {
        return dynamic;
    }

    public void setDynamic(Integer dynamic) {
        this.dynamic = dynamic;
    }

    public Long getTime() {
        return time;
    }

    public void setTime(Long time) {
        this.time = time;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public Long getSourceId() {
        return sourceId;
    }

    public void setSourceId(Long sourceId) {
        this.sourceId = sourceId;
    }

    public Map<String, String> getOldAttrs() {
        return oldAttrs;
    }

    public void setOldAttrs(Map<String, String> oldAttrs) {
        this.oldAttrs = oldAttrs;
    }

    public Map<String, String> getNewAttrs() {
        return newAttrs;
    }

    public void setNewAttrs(Map<String, String> newAttrs) {
        this.newAttrs = newAttrs;
    }

    public List<String> getProNames() {
        return proNames;
    }

    public void setProNames(List<String> proNames) {
        this.proNames = proNames;
    }
}
