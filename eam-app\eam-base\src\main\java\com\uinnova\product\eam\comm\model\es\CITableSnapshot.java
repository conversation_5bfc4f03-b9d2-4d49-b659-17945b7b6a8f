package com.uinnova.product.eam.comm.model.es;

import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import lombok.Data;

@Data
@Comment("ci表格临时数据表[uino_eam_ci_table_snapshot]")
public class CITableSnapshot implements Condition {

    @Comment("主键")
    private Long id;

    @Comment("临时ci表格所关联的资产业务主键")
    private String assetKey;

    @Comment("临时ci表格所关联的资产类型：方案-3")
    private Integer assetType;

    @Comment("CI数据")
    private CcCiInfo ciInfo;

    @Comment("ciCode")
    private String ciCode;

}

