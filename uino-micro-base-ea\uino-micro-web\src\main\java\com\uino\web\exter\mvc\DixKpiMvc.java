package com.uino.web.exter.mvc;

import com.binary.framework.util.ControllerUtils;
import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uinnova.product.vmdb.provider.kpi.bean.CcKpiInfo;
import com.uino.bean.monitor.buiness.SearchKpiBean;
import com.uino.api.client.monitor.IKpiApiSvc;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

/**
 * kpi指标相关
 * 
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping(value = "/dix/monitor/kpi")
public class DixKpiMvc {

    @Autowired
    private IKpiApiSvc kpiApiSvc;

    /**
     * 分页查询kpi信息
     * 
     * @param reqDto
     * @param request
     * @param response
     */
    @PostMapping("queryKpiInfoPage")
    @ModDesc(desc = "分页查询指标数据", pDesc = "查询条件", pType = SearchKpiBean.class, rDesc = "指标分页数据", rType = Page.class, rcType = CcKpiInfo.class)
    public void queryKpiInfoPage(@RequestBody SearchKpiBean reqDto, HttpServletRequest request,
            HttpServletResponse response) {
        ControllerUtils.returnJson(request, response, kpiApiSvc.queryKpiInfoPage(reqDto));
    }

//    /**
//     * 持久化kpi信息
//     *
//     * @param reqDto
//     * @param request
//     * @param response
//     */
//    @PostMapping("saveOrUpdate")
//    @ModDesc(desc = "保存或更新指标数据", pDesc = "指标对象", pType = CcKpiInfo.class, rDesc = "指标数据id", rType = Long.class)
//    public void saveOrUpdate(@RequestBody ESKpiInfo reqDto, HttpServletRequest request, HttpServletResponse response) {
//        Long retult = kpiApiSvc.saveOrUpdate(reqDto);
//        ControllerUtils.returnJson(request, response, retult);
//    }
//
//    /**
//     * 删除kpi信息
//     *
//     * @param kpiIds
//     * @param request
//     * @param response
//     */
//    @PostMapping("deleteByKpiIds")
//    @ModDesc(desc = "根据id批量删除指标", pDesc = "指标id集合", pType = Set.class, pcType = Long.class, rDesc = "操作是否成功", rType = Boolean.class)
//    public void deleteByKpiIds(@RequestBody Set<Long> kpiIds, HttpServletRequest request,
//            HttpServletResponse response) {
//        kpiApiSvc.deleteByKpiIds(kpiIds);
//        ControllerUtils.returnJson(request, response, true);
//    }
}
