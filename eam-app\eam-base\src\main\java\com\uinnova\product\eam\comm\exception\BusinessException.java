package com.uinnova.product.eam.comm.exception;

import com.uinnova.product.eam.base.enums.ResultCodeEnum;
import lombok.Data;

/**
 * @description: 业务功能异常
 * @author: Lc
 * @create: 2021-12-09 16:09
 */
@Data
public class BusinessException extends RuntimeException {

    // 错误码
    private final int code;
    // 错误信息
    private final String message;

    public BusinessException(int code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }

    public BusinessException(String message) {
        this(ResultCodeEnum.SERVICE_ERROR.getCode(), message);
    }

    public BusinessException(ResultCodeEnum resultCodeEnum) {
        this(resultCodeEnum.getCode(), resultCodeEnum.getMessage());
    }

}
