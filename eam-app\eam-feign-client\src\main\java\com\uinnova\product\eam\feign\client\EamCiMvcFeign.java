package com.uinnova.product.eam.feign.client;

import com.uinnova.product.eam.feign.EamFeignConst;
import com.uinnova.product.eam.feign.config.EamFeignConfig;
import com.uinnova.product.vmdb.comm.bean.QueryPageCondition;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uinnova.product.vmdb.provider.ci.bean.CiGroupPage;
import com.uino.bean.cmdb.base.LibType;
import com.uino.bean.cmdb.query.ESCISearchBean;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = EamFeignConst.SERVER_NAME, path = EamFeignConst.SERVER_ROOT + "/eam/ci",
        configuration = EamFeignConfig.class)
public interface EamCiMvcFeign {

    /**
     * 查找CI列表信息
     *
     * @param libType 库类型
     * @param bean    查询条件
     * @return 结果集
     */
    @PostMapping("/getCiList")
    @ModDesc(desc = "分页查询对象数据-支持属性排序", pDesc = "条件查询", pType = QueryPageCondition.class, pcType =
            ESCISearchBean.class, rDesc = "对象数据", rType = CiGroupPage.class)
    CiGroupPage getCiList(@RequestParam(defaultValue = "DESIGN") LibType libType,
                          @RequestBody ESCISearchBean bean);
}


