package com.uino.org.mvc;

import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.binary.framework.web.RemoteResult;
import com.binary.jdbc.Page;
import com.uino.StartBaseWebAppliaction;
import com.uino.dao.permission.ESOrgSvc;
import com.uino.bean.permission.base.SysOrg;

import lombok.extern.slf4j.Slf4j;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = { StartBaseWebAppliaction.class }, webEnvironment = WebEnvironment.RANDOM_PORT)
@Slf4j
@ActiveProfiles("provider-local")
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class SaveOrUpdateOrgTest {
	@Autowired
	private TestRestTemplate restTemplate;
	@MockBean
	private ESOrgSvc esOrgSvc;
	private static String testUrl;

	@BeforeClass
	public static void initCls() {
		SaveOrUpdateOrgTest.testUrl = "/permission/org/saveOrUpdate";
	}

	@Before
	public void testStart() {
		Mockito.when(esOrgSvc.getById(Mockito.eq(0L))).thenReturn(null);
		Mockito.when(esOrgSvc.getById(Mockito.eq(1L)))
				.thenReturn(SysOrg.builder().id(1L).orgName("模拟parentNode").orderNo(1).build());
		Mockito.when(esOrgSvc.getById(Mockito.eq(2L))).thenReturn(null);
		Mockito.when(esOrgSvc.saveOrUpdate(Mockito.any())).thenReturn(1L);
	}

	@Test
	public void test01() {
		/// 模拟三种基础校验参数传递不全
		// parentOrgId null
		JSONObject requestBody = new JSONObject();
		requestBody.put("orgName", "org01");
		requestBody.put("orderNo", "0");
		String responseBodyStr = restTemplate.postForObject(testUrl, requestBody, String.class);
		RemoteResult responseBody = JSON.parseObject(responseBodyStr, RemoteResult.class);
		Assert.assertFalse(responseBody.isSuccess());
		// orgName null
		requestBody = new JSONObject();
		requestBody.put("parentOrgId", "0");
		requestBody.put("orderNo", "0");
		responseBodyStr = restTemplate.postForObject(testUrl, requestBody, String.class);
		responseBody = JSON.parseObject(responseBodyStr, RemoteResult.class);
		Assert.assertFalse(responseBody.isSuccess());
		// orderNo null
		requestBody = new JSONObject();
		requestBody.put("parentOrgId", "0");
		requestBody.put("orgName", "org01");
		responseBodyStr = restTemplate.postForObject(testUrl, requestBody, String.class);
		responseBody = JSON.parseObject(responseBodyStr, RemoteResult.class);
		Assert.assertFalse(responseBody.isSuccess());
	}

	@Test
	public void test02() {
		/// 通过基础校验，但是未通过重名校验
		// mock一个重名的判定
		Mockito.when(esOrgSvc.getListByQuery(Mockito.anyInt(), Mockito.anyInt(), Mockito.any()))
				.thenReturn(new Page<>(1, 1, 1, 1, null));
		JSONObject requestBody = new JSONObject();
		requestBody.put("parentOrgId", "0");
		requestBody.put("orgName", "org01");
		requestBody.put("orderNo", "0");
		String responseBodyStr = restTemplate.postForObject(testUrl, requestBody, String.class);
		RemoteResult responseBody = JSON.parseObject(responseBodyStr, RemoteResult.class);
		Assert.assertFalse(responseBody.isSuccess());
	}

	@Test
	public void test03() {
		// 应该成功
		Mockito.when(esOrgSvc.getListByQuery(Mockito.anyInt(), Mockito.anyInt(), Mockito.any()))
				.thenReturn(new Page<>(1, 1, 0, 0, null));
		JSONObject requestBody = new JSONObject();
		requestBody.put("parentOrgId", "0");
		requestBody.put("orgName", "org01");
		requestBody.put("orderNo", "0");
		String responseBodyStr = restTemplate.postForObject(testUrl, requestBody, String.class);
		RemoteResult responseBody = JSON.parseObject(responseBodyStr, RemoteResult.class);
		Assert.assertTrue(responseBody.isSuccess());
	}

	@Test
	public void test04() {
		Mockito.when(esOrgSvc.getListByQuery(Mockito.anyInt(), Mockito.anyInt(), Mockito.any()))
				.thenReturn(new Page<>(1, 1, 0, 0, null));
		Mockito.when(esOrgSvc.saveOrUpdate(Mockito.any())).thenReturn(0L);
		JSONObject requestBody = new JSONObject();
		requestBody.put("parentOrgId", "0");
		requestBody.put("orgName", "org01");
		requestBody.put("orderNo", "0");
		String responseBodyStr = restTemplate.postForObject(testUrl, requestBody, String.class);
		RemoteResult responseBody = JSON.parseObject(responseBodyStr, RemoteResult.class);
		Assert.assertTrue(responseBody.isSuccess());
	}

	@Test
	public void test05() {
		// 应该成功
		Mockito.when(esOrgSvc.getListByQuery(Mockito.anyInt(), Mockito.anyInt(), Mockito.any()))
				.thenReturn(new Page<>(1, 1, 0, 0, null));
		JSONObject requestBody = new JSONObject();
		requestBody.put("parentOrgId", "0");
		requestBody.put("orgName", "org01");
		requestBody.put("orderNo", "0");
		requestBody.put("id", "111");
		String responseBodyStr = restTemplate.postForObject(testUrl, requestBody, String.class);
		RemoteResult responseBody = JSON.parseObject(responseBodyStr, RemoteResult.class);
		Assert.assertTrue(responseBody.isSuccess());
	}

	@Test
	public void test06() {
		// 应该成功
		Mockito.when(esOrgSvc.getListByQuery(Mockito.anyInt(), Mockito.anyInt(), Mockito.any()))
				.thenReturn(new Page<>(1, 1, 0, 0, null));
		JSONObject requestBody = new JSONObject();
		requestBody.put("parentOrgId", "2");
		requestBody.put("orgName", "org01");
		requestBody.put("orderNo", "0");
		requestBody.put("id", "111");
		String responseBodyStr = restTemplate.postForObject(testUrl, requestBody, String.class);
		RemoteResult responseBody = JSON.parseObject(responseBodyStr, RemoteResult.class);
		Assert.assertFalse(responseBody.isSuccess());
	}

	@Test
	public void test07() {
		// 应该成功
		Mockito.when(esOrgSvc.getListByQuery(Mockito.anyInt(), Mockito.anyInt(), Mockito.any()))
				.thenReturn(new Page<>(1, 1, 0, 0, null));
		JSONObject requestBody = new JSONObject();
		requestBody.put("parentOrgId", "1");
		requestBody.put("orgName", "org01");
		requestBody.put("orderNo", "0");
		requestBody.put("id", "111");
		String responseBodyStr = restTemplate.postForObject(testUrl, requestBody, String.class);
		RemoteResult responseBody = JSON.parseObject(responseBodyStr, RemoteResult.class);
		Assert.assertTrue(responseBody.isSuccess());
	}

}
