package com.uinnova.product.vmdb.comm.model.rlt;

import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;

/**
 * 
 * <AUTHOR>
 *
 */
@Comment("CI关系规则表[CC_CI_RLT_RULE]")
public class CcCiRltRule implements EntityBean {
    private static final long serialVersionUID = 1L;

    @Comment("ID[ID]")
    private Long id;

    @Comment("分类关系线ID[CLASS_RLT_ID]")
    private Long classRltId;

    @Comment("所属关系分类[CLASS_ID]")
    private Long classId;

    @Comment("源分类ID[SOURCE_CLASS_ID]")
    private Long sourceClassId;

    @Comment("源属性定义ID[SOURCE_DEF_ID]")
    private Long sourceDefId;

    @Comment("目标分类ID[TARGET_CLASS_ID]")
    private Long targetClassId;

    @Comment("目标属性定义ID[TARGET_DEF_ID]")
    private Long targetDefId;

    @Comment("有效状态[USE_STATUS]    有效状态:0=无效 1=有效")
    private Integer useStatus;

    @Comment("无效说明[VALID_ERR_MSG]")
    private String validErrMsg;

    @Comment("所属域[DOMAIN_ID]")
    private Long domainId;

    @Comment("数据状态[DATA_STATUS]    数据状态:1-正常 0-删除")
    private Integer dataStatus;

    @Comment("创建人[CREATOR]")
    private String creator;

    @Comment("修改人[MODIFIER]")
    private String modifier;

    @Comment("创建时间[CREATE_TIME]    创建时间:yyyyMMddHHmmss")
    private Long createTime;

    @Comment("修改时间[MODIFY_TIME]    修改时间:yyyyMMddHHmmss")
    private Long modifyTime;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getClassRltId() {
        return this.classRltId;
    }

    public void setClassRltId(Long classRltId) {
        this.classRltId = classRltId;
    }

    public Long getClassId() {
        return this.classId;
    }

    public void setClassId(Long classId) {
        this.classId = classId;
    }

    public Long getSourceClassId() {
        return this.sourceClassId;
    }

    public void setSourceClassId(Long sourceClassId) {
        this.sourceClassId = sourceClassId;
    }

    public Long getSourceDefId() {
        return this.sourceDefId;
    }

    public void setSourceDefId(Long sourceDefId) {
        this.sourceDefId = sourceDefId;
    }

    public Long getTargetClassId() {
        return this.targetClassId;
    }

    public void setTargetClassId(Long targetClassId) {
        this.targetClassId = targetClassId;
    }

    public Long getTargetDefId() {
        return this.targetDefId;
    }

    public void setTargetDefId(Long targetDefId) {
        this.targetDefId = targetDefId;
    }

    public Integer getUseStatus() {
        return this.useStatus;
    }

    public void setUseStatus(Integer useStatus) {
        this.useStatus = useStatus;
    }

    public String getValidErrMsg() {
        return this.validErrMsg;
    }

    public void setValidErrMsg(String validErrMsg) {
        this.validErrMsg = validErrMsg;
    }

    public Long getDomainId() {
        return this.domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }

    public Integer getDataStatus() {
        return this.dataStatus;
    }

    public void setDataStatus(Integer dataStatus) {
        this.dataStatus = dataStatus;
    }

    public String getCreator() {
        return this.creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getModifier() {
        return this.modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public Long getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getModifyTime() {
        return this.modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }

}
