package com.uino.bean.cmdb;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 置顶数据
 * 
 * <AUTHOR>
 *
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value="置顶数据类",description = "置顶数据")
public class SysTopData implements Serializable {
	private static final long serialVersionUID = 1L;
	/** ID */
	@ApiModelProperty(value="id",example = "123")
	private Long id;
	/**
	 * 置顶数据id
	 */
	@ApiModelProperty(value="置顶数据id",example = "123")
	private Long topDataId;
	/**
     * 置顶数据类型 3:图标,4:文件夹
     */
	@ApiModelProperty(value="置顶数据类型,3:图标,4:文件夹",example = "3")
	private Long topDataType;

	/**
	 * 置顶用户
	 */
	@ApiModelProperty(value="置顶用户id")
	private Long userId;

	/** 所属域 */
	@ApiModelProperty(value="所属域id",example = "123")
	private Long domainId;

	/** 创建人 */
	@ApiModelProperty(value="创建人",example = "mike")
	private String creator;

	/** 修改人 */
	@ApiModelProperty(value="修改人",example = "mike")
	private String modifier;

	/** 创建时间 */
	@ApiModelProperty(value="创建时间")
	private Long createTime;

	/** 修改时间 */
	@ApiModelProperty(value="修改时间")
	private Long modifyTime;

}
