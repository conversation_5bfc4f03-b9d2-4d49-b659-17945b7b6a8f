package com.uinnova.product.eam.model.vo;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.vmdb.comm.model.ci.CcCiClass;
import com.uino.bean.cmdb.base.ESCIAttrDefInfo;
import com.uino.bean.cmdb.base.ESCIInfo;
import lombok.Data;

import java.util.List;

@Data
public class DiagramSelectVo {

    @Comment("目标端分类信息")
    private CcCiClass ciClass;

    @Comment("分类属性信息")
    private List<ESCIAttrDefInfo> attrDefs;

    @Comment("分类中的ci数据")
    private List<ESCIInfo> ciList;
}
