package com.uinnova.product.eam.comm.model.es;

import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.util.List;

@Data
@Comment("操作计数表[UINO_EAM_RECENTLY_VIEW]")
public class CEamRecentlyView implements Condition {

    private Long id;

    private Long createTime;

    private Long modifyTime;
    /** 观看的视图id */
    private String diagramId;
    @Comment("/观看的方案的id")
    private Long planId;
    @Comment("矩阵id")
    private Long matrixId;
    @Deprecated
    /** 观看的架构类型，1：企业级架构资产，2：系统级架构资产  3:表示应用架构资产 */
    private Integer viewBuild;
    /** 用户id */
    private Long userId;
    /** 特殊视图 0：否  1:是 */
    private Integer specialView;

    @Comment("2表示的是查看的方案")
    private Integer viewType;

    @Comment("1.表示架构设计  2.表示架构资产")
    private Integer viewArchitectureType;

    @Deprecated
    @Comment("如果查看的架构设计的话需要表示查看的是哪个架构设计的")
    private Integer dirType;

    private List<Long> planIds;
}
