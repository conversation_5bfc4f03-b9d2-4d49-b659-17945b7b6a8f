package com.uinnova.product.eam.service.es;

import com.uinnova.product.eam.comm.model.es.CEamAttention;
import com.uinnova.product.eam.comm.model.es.EamAttention;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;

@Service
public class IamsEsAttentionDao extends AbstractESBaseDao<EamAttention, CEamAttention> {
    @Override
    public String getIndex() {
        return "uino_eam_attention";
    }

    @Override
    public String getType() {
        return getIndex();
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }
}
