package com.uino.bean.monitor.buiness;

import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class PerformanceQueryDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 关键字查询
     */
	@ApiModelProperty(value = "查询关键字", example = "abc")
    private String keyword;

    /**
     * 查询的字段
     */
	@ApiModelProperty(value = "查询的字段", example = "")
    private List<String> searchFields;

    /**
     * 性能对象所属分类筛选
     */
	@ApiModelProperty(value = "分类id/孪生模型id", example = "123")
    private Long classId;

	/**
	 * 性能对象对应CI的id
	 */
	@ApiModelProperty(value = "对象id/孪生体id", example = "456")
	private Long ciId;

	/**
	 * 性能对象对应CI的ciCode
	 */
	@ApiModelProperty(value = "对象id/孪生体ciCode", example = "abc")
	private String ciCode;

    /**
     * 是否仅查看最后发生得性能
     */
	@ApiModelProperty(value = "是否仅查看最后发生得性能", example = "true/false")
    private Boolean last;

    /**
     * 时间筛选起
     */
	@ApiModelProperty(value = "起始时间", example = "时间戳格式")
    private Long timeStart;

    /**
     * 时间筛选止
     */
	@ApiModelProperty(value = "终止时间", example = "时间戳格式")
    private Long timeEnd;

    /**
     * 页码
     */
	@ApiModelProperty(value = "分页页码", example = "1")
    private int pageNum = 1;

    /**
     * 每页行数
     */
	@ApiModelProperty(value = "每页条数", example = "20")
    private int pageSize = 20;

    /**
     * 排序字段
     */
	@ApiModelProperty(value = "排序字段", example = "默认time")
    private String order = "time";

    /**
     * 是否升序
     */
	@ApiModelProperty(value = "是否升序", example = "true/false")
    private boolean asc;

	@ApiModelProperty(value = "业务主键", example = "abc")
    private String ciPrimaryKey;

	@ApiModelProperty(value = "指标名称", example = "CPU利用率")
    private String metric;

    @ApiModelProperty(value = "领域", example = "1")
	private Long domainId;

    @ApiModelProperty(value = "根据标签查询(and关系)")
    private List<PerformanceLabelQueryDto> andLabels;

    @ApiModelProperty(value = "根据标签查询(or关系)")
    private List<PerformanceLabelQueryDto> orLabels;
}
