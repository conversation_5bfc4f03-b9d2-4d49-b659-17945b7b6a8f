package com.uinnova.product.eam.comm.model;




import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;


@Comment("系统反馈表[VC_FEEDBACK]")
public class CVcFeedback implements Condition {
	private static final long serialVersionUID = 1L;


	@Comment("ID[ID] operate-Equal[=]")
	private Long id;


	@Comment("ID[ID] operate-In[in]")
	private Long[] ids;


	@Comment("ID[ID] operate-GTEqual[>=]")
	private Long startId;

	@Comment("ID[ID] operate-LTEqual[<=]")
	private Long endId;


	@Comment("反馈用户ID[USER_ID] operate-Equal[=]")
	private Long userId;


	@Comment("反馈用户ID[USER_ID] operate-In[in]")
	private Long[] userIds;


	@Comment("反馈用户ID[USER_ID] operate-GTEqual[>=]")
	private Long startUserId;

	@Comment("反馈用户ID[USER_ID] operate-LTEqual[<=]")
	private Long endUserId;


	@Comment("反馈用户代码[USER_CODE] operate-Like[like]")
	private String userCode;


	@Comment("反馈用户代码[USER_CODE] operate-Equal[=]")
	private String userCodeEqual;


	@Comment("反馈用户代码[USER_CODE] operate-In[in]")
	private String[] userCodes;


	@Comment("反馈用户姓名[USER_NAME] operate-Like[like]")
	private String userName;


	@Comment("反馈用户姓名[USER_NAME] operate-Equal[=]")
	private String userNameEqual;


	@Comment("反馈用户姓名[USER_NAME] operate-In[in]")
	private String[] userNames;


	@Comment("反馈时间[FEEDBACK_TIME] operate-Equal[=]")
	private Long feedbackTime;


	@Comment("反馈时间[FEEDBACK_TIME] operate-In[in]")
	private Long[] feedbackTimes;


	@Comment("反馈时间[FEEDBACK_TIME] operate-GTEqual[>=]")
	private Long startFeedbackTime;

	@Comment("反馈时间[FEEDBACK_TIME] operate-LTEqual[<=]")
	private Long endFeedbackTime;


	@Comment("反馈类型[FEEDBACK_TYPE] operate-Equal[=]")
	private Integer feedbackType;


	@Comment("反馈类型[FEEDBACK_TYPE] operate-In[in]")
	private Integer[] feedbackTypes;


	@Comment("反馈类型[FEEDBACK_TYPE] operate-GTEqual[>=]")
	private Integer startFeedbackType;

	@Comment("反馈类型[FEEDBACK_TYPE] operate-LTEqual[<=]")
	private Integer endFeedbackType;


	@Comment("反馈内容[CONTENT] operate-Like[like]    逗号分隔")
	private String content;


	@Comment("所属域[DOMAIN_ID] operate-Equal[=]")
	private Long domainId;


	@Comment("所属域[DOMAIN_ID] operate-In[in]")
	private Long[] domainIds;


	@Comment("所属域[DOMAIN_ID] operate-GTEqual[>=]")
	private Long startDomainId;

	@Comment("所属域[DOMAIN_ID] operate-LTEqual[<=]")
	private Long endDomainId;


	@Comment("数据状态[DATA_STATUS] operate-Equal[=]    数据状态:数据状态：1-正常 0-删除")
	private Integer dataStatus;


	@Comment("数据状态[DATA_STATUS] operate-In[in]    数据状态:数据状态：1-正常 0-删除")
	private Integer[] dataStatuss;


	@Comment("数据状态[DATA_STATUS] operate-GTEqual[>=]    数据状态:数据状态：1-正常 0-删除")
	private Integer startDataStatus;

	@Comment("数据状态[DATA_STATUS] operate-LTEqual[<=]    数据状态:数据状态：1-正常 0-删除")
	private Integer endDataStatus;


	@Comment("创建时间[CREATE_TIME] operate-Equal[=]    创建时间:yyyyMMddHHmmss")
	private Long createTime;


	@Comment("创建时间[CREATE_TIME] operate-In[in]    创建时间:yyyyMMddHHmmss")
	private Long[] createTimes;


	@Comment("创建时间[CREATE_TIME] operate-GTEqual[>=]    创建时间:yyyyMMddHHmmss")
	private Long startCreateTime;

	@Comment("创建时间[CREATE_TIME] operate-LTEqual[<=]    创建时间:yyyyMMddHHmmss")
	private Long endCreateTime;


	@Comment("修改时间[MODIFY_TIME] operate-Equal[=]    修改时间:yyyyMMddHHmmss")
	private Long modifyTime;


	@Comment("修改时间[MODIFY_TIME] operate-In[in]    修改时间:yyyyMMddHHmmss")
	private Long[] modifyTimes;


	@Comment("修改时间[MODIFY_TIME] operate-GTEqual[>=]    修改时间:yyyyMMddHHmmss")
	private Long startModifyTime;

	@Comment("修改时间[MODIFY_TIME] operate-LTEqual[<=]    修改时间:yyyyMMddHHmmss")
	private Long endModifyTime;




	public Long getId() {
		return this.id;
	}
	public void setId(Long id) {
		this.id = id;
	}


	public Long[] getIds() {
		return this.ids;
	}
	public void setIds(Long[] ids) {
		this.ids = ids;
	}


	public Long getStartId() {
		return this.startId;
	}
	public void setStartId(Long startId) {
		this.startId = startId;
	}


	public Long getEndId() {
		return this.endId;
	}
	public void setEndId(Long endId) {
		this.endId = endId;
	}


	public Long getUserId() {
		return this.userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}


	public Long[] getUserIds() {
		return this.userIds;
	}
	public void setUserIds(Long[] userIds) {
		this.userIds = userIds;
	}


	public Long getStartUserId() {
		return this.startUserId;
	}
	public void setStartUserId(Long startUserId) {
		this.startUserId = startUserId;
	}


	public Long getEndUserId() {
		return this.endUserId;
	}
	public void setEndUserId(Long endUserId) {
		this.endUserId = endUserId;
	}


	public String getUserCode() {
		return this.userCode;
	}
	public void setUserCode(String userCode) {
		this.userCode = userCode;
	}


	public String getUserCodeEqual() {
		return this.userCodeEqual;
	}
	public void setUserCodeEqual(String userCodeEqual) {
		this.userCodeEqual = userCodeEqual;
	}


	public String[] getUserCodes() {
		return this.userCodes;
	}
	public void setUserCodes(String[] userCodes) {
		this.userCodes = userCodes;
	}


	public String getUserName() {
		return this.userName;
	}
	public void setUserName(String userName) {
		this.userName = userName;
	}


	public String getUserNameEqual() {
		return this.userNameEqual;
	}
	public void setUserNameEqual(String userNameEqual) {
		this.userNameEqual = userNameEqual;
	}


	public String[] getUserNames() {
		return this.userNames;
	}
	public void setUserNames(String[] userNames) {
		this.userNames = userNames;
	}


	public Long getFeedbackTime() {
		return this.feedbackTime;
	}
	public void setFeedbackTime(Long feedbackTime) {
		this.feedbackTime = feedbackTime;
	}


	public Long[] getFeedbackTimes() {
		return this.feedbackTimes;
	}
	public void setFeedbackTimes(Long[] feedbackTimes) {
		this.feedbackTimes = feedbackTimes;
	}


	public Long getStartFeedbackTime() {
		return this.startFeedbackTime;
	}
	public void setStartFeedbackTime(Long startFeedbackTime) {
		this.startFeedbackTime = startFeedbackTime;
	}


	public Long getEndFeedbackTime() {
		return this.endFeedbackTime;
	}
	public void setEndFeedbackTime(Long endFeedbackTime) {
		this.endFeedbackTime = endFeedbackTime;
	}


	public Integer getFeedbackType() {
		return this.feedbackType;
	}
	public void setFeedbackType(Integer feedbackType) {
		this.feedbackType = feedbackType;
	}


	public Integer[] getFeedbackTypes() {
		return this.feedbackTypes;
	}
	public void setFeedbackTypes(Integer[] feedbackTypes) {
		this.feedbackTypes = feedbackTypes;
	}


	public Integer getStartFeedbackType() {
		return this.startFeedbackType;
	}
	public void setStartFeedbackType(Integer startFeedbackType) {
		this.startFeedbackType = startFeedbackType;
	}


	public Integer getEndFeedbackType() {
		return this.endFeedbackType;
	}
	public void setEndFeedbackType(Integer endFeedbackType) {
		this.endFeedbackType = endFeedbackType;
	}


	public String getContent() {
		return this.content;
	}
	public void setContent(String content) {
		this.content = content;
	}


	public Long getDomainId() {
		return this.domainId;
	}
	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}


	public Long[] getDomainIds() {
		return this.domainIds;
	}
	public void setDomainIds(Long[] domainIds) {
		this.domainIds = domainIds;
	}


	public Long getStartDomainId() {
		return this.startDomainId;
	}
	public void setStartDomainId(Long startDomainId) {
		this.startDomainId = startDomainId;
	}


	public Long getEndDomainId() {
		return this.endDomainId;
	}
	public void setEndDomainId(Long endDomainId) {
		this.endDomainId = endDomainId;
	}


	public Integer getDataStatus() {
		return this.dataStatus;
	}
	public void setDataStatus(Integer dataStatus) {
		this.dataStatus = dataStatus;
	}


	public Integer[] getDataStatuss() {
		return this.dataStatuss;
	}
	public void setDataStatuss(Integer[] dataStatuss) {
		this.dataStatuss = dataStatuss;
	}


	public Integer getStartDataStatus() {
		return this.startDataStatus;
	}
	public void setStartDataStatus(Integer startDataStatus) {
		this.startDataStatus = startDataStatus;
	}


	public Integer getEndDataStatus() {
		return this.endDataStatus;
	}
	public void setEndDataStatus(Integer endDataStatus) {
		this.endDataStatus = endDataStatus;
	}


	public Long getCreateTime() {
		return this.createTime;
	}
	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}


	public Long[] getCreateTimes() {
		return this.createTimes;
	}
	public void setCreateTimes(Long[] createTimes) {
		this.createTimes = createTimes;
	}


	public Long getStartCreateTime() {
		return this.startCreateTime;
	}
	public void setStartCreateTime(Long startCreateTime) {
		this.startCreateTime = startCreateTime;
	}


	public Long getEndCreateTime() {
		return this.endCreateTime;
	}
	public void setEndCreateTime(Long endCreateTime) {
		this.endCreateTime = endCreateTime;
	}


	public Long getModifyTime() {
		return this.modifyTime;
	}
	public void setModifyTime(Long modifyTime) {
		this.modifyTime = modifyTime;
	}


	public Long[] getModifyTimes() {
		return this.modifyTimes;
	}
	public void setModifyTimes(Long[] modifyTimes) {
		this.modifyTimes = modifyTimes;
	}


	public Long getStartModifyTime() {
		return this.startModifyTime;
	}
	public void setStartModifyTime(Long startModifyTime) {
		this.startModifyTime = startModifyTime;
	}


	public Long getEndModifyTime() {
		return this.endModifyTime;
	}
	public void setEndModifyTime(Long endModifyTime) {
		this.endModifyTime = endModifyTime;
	}


}


