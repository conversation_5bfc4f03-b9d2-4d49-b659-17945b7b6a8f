package com.uinnova.product.eam.web.diagram.bean;

import java.io.Serializable;
import java.util.List;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.web.integration.bean.VcCiInfo;
import com.uinnova.product.eam.web.integration.bean.VcCiRltInfo;

public class ApplicationRingInfo implements Serializable{

	private static final long serialVersionUID = 1L;
	
	@Comment("关系信息")
	private List<VcCiRltInfo>  rltInfos;
	
	@Comment("应用环图中心ci信息")
	private VcCiInfo ci;

	public List<VcCiRltInfo> getRltInfos() {
		return rltInfos;
	}

	public void setRltInfos(List<VcCiRltInfo> rltInfos) {
		this.rltInfos = rltInfos;
	}

	public VcCiInfo getCi() {
		return ci;
	}

	public void setCi(VcCiInfo ci) {
		this.ci = ci;
	}
	
	

}
