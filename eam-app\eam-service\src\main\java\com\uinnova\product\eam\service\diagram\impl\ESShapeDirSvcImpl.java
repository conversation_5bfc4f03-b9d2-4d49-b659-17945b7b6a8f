package com.uinnova.product.eam.service.diagram.impl;

import com.binary.core.exception.MessageException;
import com.binary.core.util.BinaryUtils;
import com.uinnova.product.eam.base.diagram.model.ESShapeDir;
import com.uinnova.product.eam.db.diagram.es.ESShapeDirDao;
import com.uinnova.product.eam.service.diagram.ESShapeDirSvc;
import com.uinnova.product.vmdb.comm.model.ci.CcCiClassDir;
import com.uino.bean.cmdb.enums.DirTypeEnum;
import com.uino.bean.permission.base.SysUser;
import com.uino.util.sys.SysUtil;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.Iterator;
import java.util.List;

@Deprecated
@Service
public class ESShapeDirSvcImpl implements ESShapeDirSvc {


    @Autowired
    private ESShapeDirDao svc;

    private static final Logger logger = LoggerFactory.getLogger(ESShapeDirSvcImpl.class);

    @Override
    public Long saveOrUpdate(ESShapeDir dir) {
        BinaryUtils.checkEmpty(dir, "dir");
        dir.setParentId(dir.getParentId() == null ? 0L : dir.getParentId());
        dir.setCiType(dir.getCiType() == null ? 1 : dir.getCiType());
        if (StringUtils.isEmpty(dir.getDirName())) {
            dir.setDirName("我的形状");
        }
        if (dir.getCiType() == 3 && !dir.getDirName().matches("[A-Za-z0-9 _\\-\\@\\.\\(\\)\\u4e00-\\u9fa5]+")) {
            throw MessageException.i18n("BS_MNAME_IMAGE_DIR_NAME_FORMAT");
        } else {
            if (dir.getCiType() == 1) {
//                Assert.isTrue(dir.getDirName().trim().length() <= 20, "我的形状名称不可超过20位");
                Assert.isTrue(dir.getDirName().matches("[A-Za-z0-9 _\\-\\@\\.\\(\\)\\u4e00-\\u9fa5]+"), "我的形状名称仅允许输入中文、字母、数字空格以及()._-@符号");
            }

            BoolQueryBuilder query = QueryBuilders.boolQuery();
            query.must(QueryBuilders.termQuery("dirName.keyword", dir.getDirName()));
            if (dir.getCiType() != DirTypeEnum.IMAGE.getType() && dir.getCiType() != DirTypeEnum.IMAGE3D.getType() && dir.getCiType() != DirTypeEnum.VEDIO.getType()) {
                query.must(QueryBuilders.termQuery("ciType", dir.getCiType()));
            } else {
                query.must(QueryBuilders.termsQuery("ciType", Arrays.asList(DirTypeEnum.IMAGE.getType(), DirTypeEnum.IMAGE3D.getType(), DirTypeEnum.VEDIO.getType())));
            }

            if (dir.getId() != null) {
                query.mustNot(QueryBuilders.termQuery("id", dir.getId()));
            }

            List<ESShapeDir> dirs = this.svc.getListByQuery(query);
            CcCiClassDir grandfatherDir;
            if (dir.getCiType() != DirTypeEnum.IMAGE.getType() && dir.getCiType() != DirTypeEnum.IMAGE3D.getType() && dir.getCiType() != DirTypeEnum.VEDIO.getType()) {
            } else {
                Iterator var4 = dirs.iterator();

                while (var4.hasNext()) {
                    grandfatherDir = (CcCiClassDir) var4.next();
                    if (grandfatherDir.getParentId().equals(dir.getParentId())) {
                        StringBuilder stringBuilder = new StringBuilder();
                        stringBuilder.append("BS_VERIFY_ERROR#{name: BS_DIR_FOLDER }#{type:BS_MVTYPE_DUPLICATE}${value:[");
                        stringBuilder.append(dir.getDirName());
                        stringBuilder.append("]}");
                        Assert.isTrue(false, stringBuilder.toString());
                    }
                }
            }

            ESShapeDir dir2;
            SysUser loginUser = SysUtil.getCurrentUserInfo();
            if (dir.getId() != null) {
                dir2 = this.svc.getById(dir.getId());
                Assert.notNull(dir2, "文件夹不存在");
                if (dir2 == null) {
                    throw new MessageException("要修改的目录不存在");
                } else if (!dir2.getUserId().equals(loginUser.getId())) {
                    throw new MessageException("要修改的目录无权限");
                } else {
                    BeanUtils.copyProperties(dir, dir2, new String[]{"createTime"});
                    return this.svc.saveOrUpdate(dir2);
                }
            } else {
                dir.setDomainId(loginUser.getDomainId());
                dir.setDataStatus(1);
                if (BinaryUtils.isEmpty(dir.getUserId())) {
                    dir.setUserId(loginUser.getId());
                }
                return this.svc.saveOrUpdate(dir);
            }
        }
    }
}
