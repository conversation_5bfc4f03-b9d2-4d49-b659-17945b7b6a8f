package com.uino.comm.exception;

public class FrameworkException extends UinoBasicException {

    public static final long serialVersionUID = 1;

    public FrameworkException() {
        super();
    }

    public FrameworkException(String message) {
        super(message);
    }

    public FrameworkException(String message, Throwable cause) {
        super(message, cause);
    }

    public FrameworkException(Throwable cause) {
        super(cause);
    }
}
