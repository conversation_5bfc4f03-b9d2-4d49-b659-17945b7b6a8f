package com.uino.service.sys.microservice.impl;

import java.util.*;

import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import com.binary.jdbc.Page;
import com.uino.dao.sys.ESResourceSvc;
import com.uino.service.sys.microservice.IResourceSvc;
import com.uino.dao.util.ESUtil;
import com.uino.bean.cmdb.base.ESResource;
import org.springframework.util.CollectionUtils;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
public class ResourceSvc implements IResourceSvc {

	@Autowired
	private ESResourceSvc esResourceSvc;

	@Override
	public String saveSyncResourceInfo(String path, String publicUrl, boolean unzip, Integer optionType) {
		return this.saveSyncResourceInfo(path, publicUrl, unzip, false, optionType);
	}

	@Override
	public String saveSyncResourceInfo(String path, String publicUrl, boolean unzip, boolean currentDir,
									   Integer optionType) {
		// 数据合法性校验
		this.validResourceInfo(path, publicUrl, unzip, optionType);
        ESResource resourceInfo = ESResource.builder().path(path).unzip(unzip).currentDir(currentDir).publicUrl(publicUrl)
				.optionType(optionType).build();
		// 删除操作的话找到已存在的对应的新增和修改操作删除(指针指到这里也是要删除了,之前同步的信息可以无视)
		if (resourceInfo.getOptionType().intValue() == 1) {
			BoolQueryBuilder delQuery = QueryBuilders.boolQuery();
			delQuery.must(QueryBuilders.termsQuery("optionType", new ArrayList<>(Arrays.asList(1, 3))));
			delQuery.must(QueryBuilders.termQuery("path.keyword", resourceInfo.getPath()));
			esResourceSvc.deleteByQuery(delQuery, true);
		}
		esResourceSvc.saveOrUpdate(resourceInfo);
		return path;
	}

	@Override
	public void saveSyncResourceBatch(Map<String, String> pathMap, boolean unzip, boolean currentDir, Integer optionType) {
		if(CollectionUtils.isEmpty(pathMap)){
			return;
		}
		List<ESResource> resourceList = new ArrayList<>();
		for (Map.Entry<String, String> entry : pathMap.entrySet()) {
			// 数据合法性校验
			this.validResourceInfo(entry.getKey(), entry.getValue(), unzip, optionType);
			ESResource resourceInfo = ESResource.builder().path(entry.getKey()).unzip(unzip)
					.currentDir(currentDir).publicUrl(entry.getValue()).optionType(optionType).build();
			resourceList.add(resourceInfo);
		}
		if (optionType == 1) {
			BoolQueryBuilder delQuery = QueryBuilders.boolQuery();
			delQuery.must(QueryBuilders.termQuery("optionType", optionType));
			delQuery.must(QueryBuilders.termsQuery("path.keyword", pathMap.keySet()));
			esResourceSvc.deleteByQuery(delQuery, true);
		}
		esResourceSvc.saveOrUpdateBatch(resourceList);
	}

	@Override
	public List<ESResource> getWaitSyncResources(Long startTime) {
		if (startTime == null) {
			Date date = new Date();
			Calendar calendar = Calendar.getInstance();
			calendar.setTime(date);
			calendar.add(Calendar.DAY_OF_MONTH, -1);
			date = (Date) calendar.getTime();
			startTime = ESUtil.getNumberDateTime(date);

		}
		BoolQueryBuilder query = QueryBuilders.boolQuery();
		query.must(QueryBuilders.rangeQuery("createTime").gte(startTime));
		Page<ESResource> res = esResourceSvc.getSortListByQuery(1, 9999, query, "createTime", true);
		return res.getData();
	}

	@Override
	public void saveSyncResourceInfo(List<ESResource> saveDtos) {
		Assert.notEmpty(saveDtos, "saveDtos don't empty");
		saveDtos.forEach(
				dto -> validResourceInfo(dto.getPath(), dto.getPublicUrl(), dto.isUnzip(), dto.getOptionType()));
		esResourceSvc.saveOrUpdateBatch(saveDtos);
	}

	/**
	 * 验证资源信息
	 *
	 * @param path
	 * @param publicUrl
	 * @param unzip
	 * @param optionType
	 */
	private void validResourceInfo(String path, String publicUrl, boolean unzip, Integer optionType) {
		Assert.notNull(path, "pathNotNull");
		Assert.notNull(publicUrl, "pathNotNull");
		Assert.isTrue(optionType != null && optionType >= 0 && optionType <= 2, "optionType illegal");
        if (unzip) {
            Assert.isTrue((path.endsWith(".zip") && publicUrl.endsWith(".zip")) || (path.endsWith(".tjs") && publicUrl.endsWith(".tjs")), "path and publicUrl must .zip or .tjs");
        }
	}

}
