package com.uino.bean.dix;

import java.io.Serializable;
import java.util.Map;

/**
 * 返回给Dix的查询结果
 *
 * <AUTHOR>
 */
public class Record implements Serializable {

    private static final long serialVersionUID = -3775082968340954089L;

    /** ci主键 */
    private Long id;

    /** ci属性 */
    private Map<String, String> object;

    /** 分类主键 */
    private Long classId;

    /** 分类名称 */
    private String className;

    /** ci的 code */
    private String ciCode;

    /** CI业务主键值的hashcode[HASH_CODE] */
    private Integer hashCode;

    /** 当前CI所有业务主键值集合[JSON格式字符串][CI_PRIMARY_KEY] */
    private String ciPrimaryKey;

    /** 记录发生时间 yyyyMMddHHmmss */
    private Long time;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Map<String, String> getObject() {
        return object;
    }

    public void setObject(Map<String, String> object) {
        this.object = object;
    }

    public Long getTime() {
        return time;
    }

    public void setTime(Long time) {
        this.time = time;
    }

    public Long getClassId() {
        return classId;
    }

    public void setClassId(Long classId) {
        this.classId = classId;
    }

    public String getClassName() {
        return className;
    }

    public void setClassName(String className) {
        this.className = className;
    }

    public Integer getHashCode() {
        return hashCode;
    }

    public void setHashCode(Integer hashCode) {
        this.hashCode = hashCode;
    }

    public String getCiPrimaryKey() {
        return ciPrimaryKey;
    }

    public void setCiPrimaryKey(String ciPrimaryKey) {
        this.ciPrimaryKey = ciPrimaryKey;
    }

    public String getCiCode() {
        return ciCode;
    }

    public void setCiCode(String ciCode) {
        this.ciCode = ciCode;
    }
}
