package com.uino.provider.server.web.cmdb.mvc;

import com.uino.bean.cmdb.base.ESCIHistoryInfo;
import com.uino.provider.feign.cmdb.CIHistoryFeign;
import com.uino.service.cmdb.microservice.ICIHistorySvc;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("feign/ciHistory")
public class CIHistoryFeignMvc implements CIHistoryFeign {

    @Autowired
    private ICIHistorySvc ciHistorySvc;

    @Override
    public List<String> getCIVersionList(String ciCode, Long classId) {
        return ciHistorySvc.getCIVersionList(ciCode, classId);
    }

    @Override
    public ESCIHistoryInfo getCIInfoHistoryByCIVersion(String ciCode, Long classId, Long version) {
        return ciHistorySvc.getCIInfoHistoryByCIVersion(ciCode, classId, version);
    }

    @Override
    public void delAll(List<Long> classIds) {
        ciHistorySvc.delAll(classIds);
    }

}
