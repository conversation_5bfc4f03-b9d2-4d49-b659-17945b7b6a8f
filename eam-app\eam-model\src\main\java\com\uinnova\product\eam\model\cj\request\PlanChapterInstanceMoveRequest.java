package com.uinnova.product.eam.model.cj.request;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;

/**
 * 方案章节 实例 移动请求参数
 *
 * <AUTHOR>
 */
@Data
public class PlanChapterInstanceMoveRequest {

    /**
     * 方案id
     */
    @NotNull(message = "章节id必填")
    private Long id;

    /**
     * 新的下表
     */
    //@NotNull(message = "index必填")
    //@Min(value = 0L, message = "index不合法")
    private Integer index;

    /**
     * 移动位置所属父Id
     */
    //@NotNull(message = "父主键id必填")
    private Long parentId;


    /**
     * 目标id
     */
    @NotNull(message = "父主键id必填")
    private Long targetId;

    /**
     * 标识，-1：拖到目标上边 0：拖到目标下级 1：拖到目标的下边
     */
    @NotNull(message = "位置标识必填!")
    private Integer sign;
}
