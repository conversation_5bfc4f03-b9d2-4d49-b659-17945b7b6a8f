package com.uinnova.product.eam.service.diagram;

import com.binary.jdbc.Page;
import com.uinnova.product.eam.base.diagram.model.ESDiagramShareRecordResult;
import com.uinnova.product.eam.base.diagram.model.ShareRecordQueryBean;

import java.util.List;
import java.util.Map;

public interface ESShareDesignDiagramSvc {

    /**
     * 分页查询分享记录
     * @param shareRecordQueryBean
     * @return
     */
    Page<ESDiagramShareRecordResult> queryShareRecordPage(ShareRecordQueryBean shareRecordQueryBean);

    /**
     * 查询视图分享记录 map
     * @param diagramIds
     * @return
     */
    Map<Long, List<ESDiagramShareRecordResult>> queryDiagramShareRecords(String[] diagramIds, Boolean needAuth);

}
