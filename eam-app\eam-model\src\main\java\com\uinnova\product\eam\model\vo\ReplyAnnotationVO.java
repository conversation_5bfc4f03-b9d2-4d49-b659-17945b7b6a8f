package com.uinnova.product.eam.model.vo;

import lombok.Data;

@Data
public class ReplyAnnotationVO {

    /**
     * 章节id
     */
    private Long planChapterId;

    /**
     * 内容块id
     */
    private Long planChapterModuleId;

    /**
     * 批注id
     */
    private Long annotationId;

    /**
     * 回复的批注id
     */
    private Long parentAnnotationId;

    /**
     * 批注内容
     */
    private String annotationContent;

    /**
     * 回复批注的时间
     */
    private Long applyTime;

    /**
     * 回复批注的人
     */
    private String applyAuthor;

    /**
     * 回复批注人的code
     */
    private String applyCode;

}
