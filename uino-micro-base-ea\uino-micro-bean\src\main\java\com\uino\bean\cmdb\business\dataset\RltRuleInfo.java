package com.uino.bean.cmdb.business.dataset;

import com.alibaba.fastjson.annotation.JSONField;
import com.binary.framework.bean.annotation.Comment;
import com.uino.bean.cmdb.base.ESCIClassInfo;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @data 2019/7/31 11:03.
 */
@Getter
@Setter
public class RltRuleInfo implements Serializable {


    private static final long serialVersionUID = 8072884742366036454L;
    private Long id;
    @Comment("定义名称")
    private String name;
    @Comment("入口分类")
    private Long startClassId;
    @Comment("入口页面节点ID")
    private Long startPageNodeId;
    @Comment("所属域")
    private Long domainId;
    @Comment("定义描述")
    private String desc;
    @Comment("创建人")
    private String creator;
    @Comment("修改人")
    private String modifier;
    @Comment("创建时间")
    private Long createTime;
    @Comment("修改时间")
    private Long modifyTime;
    @Comment("节点")
    private List<NodeInfo> nodes;
    @Comment("规则线")
    private List<LineInfo> lines;


    @Setter
    @Getter
    public static class Line {
        //
        private Long pageLineId;
        //分类关系id
        private Long clsRltId;
        //起始分类
        private Long clsStartId;
        //结束分类
        private Long clsEndId;
        //起始节点ID   起始节点ID:供页面用
        private Long nodeStartId;
        //结束节点ID    结束节点ID:供页面用
        private Long nodeEndId;
        //关系线类型   关系线类型:1=规则关系、2=empty
        private Integer lineType;
        //运算符    运算符:1=between、2=大于等于、3=小于等于
        private Integer lineOp;
        //条件值    条件值为两个值时以逗号隔开
        private String lineVal;
        //关系线条件方向：1=线起始，2=线终止
        private Integer lineDirect;
    }

    @Setter
    @Getter
    public static class LineCdt {
        //所属关系线
        private Long lineId;
        //条件属性
        private Long attrId;
        //运算符
        private Integer cdtOp;
        //条件值
        private String cdtVal;
    }

    @Setter
    @Getter
    public static class LineInfo {
        private Line line;
        private List<LineCdt> lineCdts;
        @JSONField(serialize = false)
        private ESCIClassInfo classInfo;
    }

    @Setter
    @Getter
    public static class Node {
        //页面节点ID
        private Long pageNodeId;
        //节点分类ID
        private Long classId;
        //X坐标[X]
        private Integer x;
        //Y坐标[Y]
        private Integer y;
        //节点类型   节点类型 1=CI节点、2=运算符节点
        private Integer nodeType;

        //逻辑节点ID    逻辑节点ID 1=and、2=or
        private Integer logicOperation;
        //返回字段
        private String returns;

    }

    @Setter
    @Getter
    public static class NodeCdt {
        //所属节点
        private Long nodeId;
        //条件属性
        private Long attrId;
        //运算符   1：= , 2：like ,3: > , 4: >= , 5: < , 6: <=
        private Integer cdtOp;
        //条件值
        private String cdtVal;
        //关联关系 OR AND   默认没有就是AND
        private String relRlt;
    }

    @Setter
    @Getter
    public static class NodeInfo implements Serializable {

        private static final long serialVersionUID = -880822066992965553L;

        private Node node;
        private List<NodeCdt> nodeCdts;
        //不存库
        @JSONField(serialize = false)
        private ESCIClassInfo classInfo;

    }

}

