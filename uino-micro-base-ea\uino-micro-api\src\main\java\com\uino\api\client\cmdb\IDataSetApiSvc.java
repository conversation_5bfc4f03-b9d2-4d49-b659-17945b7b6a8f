package com.uino.api.client.cmdb;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.binary.core.io.Resource;
import com.uino.bean.cmdb.base.dataset.DataSetMallApi;
import com.uino.bean.cmdb.base.dataset.DataSetMallApiRelationRule;
import com.uino.bean.cmdb.base.dataset.DataSetMallApiType;
import com.uino.bean.cmdb.base.dataset.batch.DataSetTableResult;
import com.uino.bean.cmdb.base.dataset.chart.Chart;
import com.uino.bean.cmdb.business.dataset.*;
import com.uino.bean.dataset.base.DataSetPathInfoVo;
import com.uino.bean.dataset.base.DataSetThumbnailDTO;
import com.uino.bean.tp.query.MetricAttrValQueryDTO;
import com.uino.bean.tp.query.QueryDataTableDTO;
import com.uino.bean.tp.query.TpRuleReqDTO;
import org.springframework.http.ResponseEntity;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @Classname DataSetMallApi
 * @Description TODO
 * @Date 2020/3/20 16:18
 * @Created by sh
 */
public interface IDataSetApiSvc {

    /**
     * 新增或保存数据集
     *
     * @param json              数据集对象
     * @param ifExeBatchProcess 保存的时候是否执行批处理操作
     * @return
     */
    Long saveOrUpdateDataSet(JSONObject json, Boolean ifExeBatchProcess);

    /**
     * @param id
     * @return
     */
    boolean delete(Long id);

    /**
     * @param name     名称
     * @param isMyself 是否自己创建
     * @param url
     * @return
     */
    List<JSONObject> findDataSet(String name, boolean isMyself, String url, List<DataSetMallApiType> typeList);

    /**
     * 通过ID查找数据集
     *
     * @param id 数据集ID
     * @return 数据集对象
     */
    JSONObject findDataSetById(Long id);

    /**
     *
     * @param id
     * @param isCheck   是否校验
     * @return
     */
    JSONObject findDataSetById(Long id, boolean isCheck);

    /**
     * 执行api查询
     *
     * @param language
     * @param id
     * @param body
     * @return
     */
    JSONObject execute(String language, Long id, JSONObject body);
    JSONObject execute(JSONObject body);
    JSONObject execute(Long domainId, String language, Long id, JSONObject body);

    /**
     * 关系遍历实时查询api
     *
     * @param language
     * @param id
     * @param body
     * @return
     */
    JSONObject realTimeExecute(String language, Long id, JSONObject body);
    JSONObject realTimeExecute(Long domainId, String language, Long id, JSONObject body);

    /**
     * 更新分享
     *
     * @param id         数据集ID
     * @param shareLevel 分享级别
     * @return 是否为更新操作
     */
    boolean shareDataSet(Long id, int shareLevel);

    /**
     * 获取到所有关系遍历的数据集
     *
     * @return List<DataSetMallApiRelationRule> 关系遍历数据集集合
     */
    List<DataSetMallApiRelationRule> findAllRelationDateSet();
    List<DataSetMallApiRelationRule> findAllRelationDateSet(Long domainId);

    /**
     * 更新数据集执行全量结果
     *
     * @param dataSetRelationRule 关系遍历数据集
     * @param friendInfoMap       关系遍历结果Map
     */
    void updateMallApiExeResult(DataSetMallApiRelationRule dataSetRelationRule, Map<Long, FriendInfo> friendInfoMap);


    /**
     * 获取指定数据集里包含的Sheet
     *
     * @param dataSetId 数据集Id
     * @return Sheet列表
     */
    List<Map<String, Object>> getDataSetSheets(Long dataSetId);

    /**
     * 查询数据集中包含的行数
     * @param dataSetIds 数据集ID
     * @return Map<String, Long>
     */
    Map<String, Long> getDataSetLineCount(List<String> dataSetIds);

    /**
     * 查询符合规则的入口ci
     * @param pageNum
     * @param pageSize
     * @param dataSetId
     * @param name
     * @param like
     * @return
     */
    JSONObject queryRuleLegitimateCi(Integer pageNum, Integer pageSize, Long dataSetId, String name, String like);

    /**
     * 根据规则和入口id查询朋友圈规则
     *
     * @param dataSetId
     * @param startCiId
     * @return
     */
    FriendInfo queryFriendByStartCiId(Long dataSetId, Long startCiId);

    /**
     * 根据规则和入口id查询朋友圈规则
     * 限制目标分类，通过剪枝使用规则一部分
     *
     * @param dataSetId
     * @param startCiId
     * @param targetClassIds
     * @return
     */
    FriendInfo queryFriendByStartCiIdAndTargetClass(Long dataSetId, Long startCiId, Set<Long> targetClassIds);

    Map<Long, Integer> queryRuleNodeCiNum(Long dataSetId);

    RltRuleTableData queryDisassembleFriendInfoDataByPath(Long dataSetId, Long startCiId);

    /**
     * 统计规则
     *
     * @param dataSetId
     * @param chart
     * @return
     */
    JSONObject countStatistics(Long dataSetId, Chart chart);

    /**
     * 获取数据集的查询条件
     *
     * @param dataSetId 数据集ID
     * @param sheetId   sheet页ID
     * @return 条件集合
     */
    List<JSONObject> getQueryCondition(Long dataSetId, String sheetId);

    /**
     * 通过条件查询数据集的遍历结果
     *
     * @param dataSetId 数据集ID
     * @param sheetId   Sheet页ID
     * @param pageNum   页数
     * @param pageSize  每页大小
     * @param sortCol   排序的列名
     * @param isDesc    是否降序排列
     * @param condition 查询条件
     * @param userCode  请求用户的code
     * @return
     */
    DataSetExeResultSheetPage queryDataSetResultBySheet(Long dataSetId, String sheetId, int pageNum, int pageSize, String sortCol, boolean isDesc, JSONArray condition, String userCode);

    /**
     * 批量通过条件查询数据集的遍历结果
     * @param dataSetIds
     * @param sheetId
     * @param sortCol
     * @param isDesc
     * @return
     */
    List<DataSetExeResultSheetPage> queryDataSetResultList(List<Long> dataSetIds, String sheetId, String sortCol, boolean isDesc);

    /**
     * 通过条件查询数据集的遍历结果
     * @param domainId
     * @param dataSetId
     * @param sheetId
     * @param pageNum
     * @param pageSize
     * @param sortCol
     * @param isDesc
     * @param condition
     * @param userCode
     * @return
     */
    DataSetExeResultSheetPage queryDataSetResultBySheet(Long domainId, Long dataSetId, String sheetId, int pageNum, int pageSize, String sortCol, boolean isDesc, JSONArray condition, String userCode);

    /**
     * 根据ruleInfo查询全部表格形式数据
     *
     * @param rule  关系遍历规则
     * @param limit 入口CI个数
     */
    List<DataSetExeResultSheetPage> getResultUsingRule(JSONObject rule, Integer limit);

    /**
     * 通过条件查询数据集的遍历结果
     *
     * @param dataSetId 数据集ID
     * @param sheetId   Sheet页ID
     * @param sortCol   排序的列名
     * @param isDesc    是否降序排列
     * @param condition 查询条件
     */
    Resource downloadSheetData(Long dataSetId, String sheetId, String sortCol, boolean isDesc, JSONArray condition);
    Resource downloadSheetData(Long domainId, Long dataSetId, String sheetId, String sortCol, boolean isDesc, JSONArray condition);

    /**
     * 判断数据集的批处理任务是否在执行中
     *
     * @param dataSetId 数据集ID
     * @return 1：执行中 0：未执行
     */
    int isTaskRunning(Long dataSetId);

    /**
     * 获取Code
     *
     * @param jsonStr 数据集参数
     * @return code代码
     */
    String getCode(String jsonStr) throws Exception;

    /**
     * 判断用户是否有此数据集的权限
     *
     * @param userCode       用户Code
     * @param dataSetMallApi 数据集对象
     */
    void checkOperate(String userCode, DataSetMallApi dataSetMallApi);

    /**
     * 数据集请求次数统计
     *
     * @return [{"id":1, "name":"数据集名称", "count":100}, ...]
     */
    List<Map<String, Object>> groupDataSetMallApiLogCount();
    List<Map<String, Object>> groupDataSetMallApiLogCount(Long domainId);

    /**
     * 获取指标丰富的标签名称
     *
     * @param ruleReqDTO
     * @return
     */
    List<String> getTpMetricLabelDTOList(TpRuleReqDTO ruleReqDTO);

    /**
     * 获取标签值
     *
     * @param query
     * @return
     */
    List<String> queryMetricAttrValue(MetricAttrValQueryDTO query);

    /**
     * 根据规则和入口查询关系-多节点
     * @param body
     * @return
     */
    FriendBatchInfo queryFriendByStartCiIds(List<FriendInfoRequestDto> body);

    /**
     * 获取路径列表
     * @param rule
     * @param limit
     * @return
     */
    List<DataSetExeResultSheetPage> findDataSetRuleList(JSONObject rule, Integer limit);

    /**
     * 更新数据集缩略图
     * @param body base64
     * @return 缩略图地址
     */
    String updateThumbnail(DataSetThumbnailDTO body);

    /**
     * 获取数据集路径信息
     * @param dataSetIds 数据超市id
     * @return 路径信息
     */
    List<DataSetPathInfoVo> getPathInfo(List<Long> dataSetIds);

    void updateDataSetById(Long id);

    /**
     *  根据报表格式查询数据
     * @return
     */
    List<DataSetTableResult> queryCiTableList(QueryDataTableDTO queryDataTableDTO);

    ResponseEntity<byte[]> ciTableListExport(List<DataSetTableResult> ret, QueryDataTableDTO body);

}

