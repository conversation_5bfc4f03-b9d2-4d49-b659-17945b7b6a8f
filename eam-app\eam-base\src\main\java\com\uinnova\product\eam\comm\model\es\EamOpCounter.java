package com.uinnova.product.eam.comm.model.es;


import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;


@Comment("操作计数表[UINO_EAM_OP_COUNTER]")
public class EamOpCounter implements EntityBean {

	private Long id;

	private String ciCode;

	private Long times;

	private String counterType;

	private Long domainId;

	private Long createTime;

	private Long modifyTime;


	@Override
	public Long getId() {
		return id;
	}

	@Override
	public void setId(Long id) {
		this.id = id;
	}

	public String getCiCode() {
		return ciCode;
	}

	public void setCiCode(String ciCode) {
		this.ciCode = ciCode;
	}

	public Long getTimes() {
		return times;
	}

	public void setTimes(Long times) {
		this.times = times;
	}

	public String getCounterType() {
		return counterType;
	}

	public void setCounterType(String counterType) {
		this.counterType = counterType;
	}

	public Long getDomainId() {
		return domainId;
	}

	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}

	@Override
	public Long getCreateTime() {
		return createTime;
	}

	@Override
	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}

	@Override
	public Long getModifyTime() {
		return modifyTime;
	}

	@Override
	public void setModifyTime(Long modifyTime) {
		this.modifyTime = modifyTime;
	}
}


