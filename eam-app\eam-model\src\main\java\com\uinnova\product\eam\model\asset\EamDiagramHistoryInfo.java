package com.uinnova.product.eam.model.asset;

import com.binary.framework.bean.annotation.Comment;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.uino.bean.permission.base.SysUser;
import lombok.Data;


/**
 * @Description 视图发布历史列表精简信息
 */
@Data
public class EamDiagramHistoryInfo {

    @Comment("发布视图作者信息")
    private SysUser releaseUser;

    @Comment("视图信息")
    private PublishHisDiagramInfo releaseInfo;

    @Data
    private class PublishHisDiagramInfo {

        @Comment("自定义版本号")
        private Integer versionNo;

        @Comment("创建时间")
        private Long createTime;

        @Comment("发布说明")
        private String releaseDesc;

        @Comment("视图基础信息")
        private DiagramInfo diagram;

        @Comment("视图ID加密字段")
        @JsonProperty("diagramId")
        private String dEnergy;

        @Data
        private class DiagramInfo {
            @Comment("视图id[id]")
            @JsonIgnore
            private Long id;

            @Comment("视图名称[NAME]")
            private String name;

            @Comment("所属用户[USER_ID]")
            private Long userId;

            @Comment("所属用户编码[OWNER_CODE]")
            private String ownerCode;

            @Comment("所属目录[DIR_ID]")
            private Long dirId;

            @Comment("目录类型[DIR_TYPE]   ")
            private Integer dirType;

            @Comment("视图类型[DIAGRAM_TYPE]    视图类型:1=视图 3=公共模版 4=个人模板 5=wiki新建视图")
            private Integer diagramType;

            @Comment("历史版本标识，1--主版本，0--历史版本")
            private Integer historyVersionFlag;

            @Comment("视图描述[DIAGRAM_DESC]")
            private String diagramDesc;

            @Comment("是否公开[IS_OPEN]    是否公开:1=开放 0=私有")
            private Integer isOpen;

            @Comment("公开时间[OPEN_TIME]")
            private Long openTime;

            @Comment("数据驱动类型[DATA_UP_TYPE]    数据驱动类型:1=不更新 2=标记提示 3=自动更新")
            private Integer dataUpType;

            @Comment("视图状态[STATUS]    视图状态:1=正常 0=回收站")
            private Integer status;

            @Comment("创建人[CREATOR]")
            private String creator;

            @Comment("修改人[MODIFIER]")
            private String modifier;

            @Comment("创建时间[CREATE_TIME]    创建时间:yyyyMMddHHmmss")
            private Long createTime;

            @Comment("更新时间[MODIFY_TIME]    更新时间:yyyyMMddHHmmss")
            private Long modifyTime;

            @Comment("版本描述")
            private String versionDesc;

            @Comment("视图版本id")
            private Long diagramVersionId;

            @Comment("版本名称")
            private String versionName;

            @Comment("版本描述文件存储路径")
            private String versionDescPath;

            @Comment("自定义版本号")
            private String versionNo;

            @Comment("视图类型[VIEW_TYPE]")
            private String viewType;

            @Comment("视图类型  1: 空白视图 2：制品视图 3：基于模版 4：默认视图")
            private Integer diagramSubType;

            @Comment("视图版本标识")
            private String version;

            @Comment("视图ID加密字段")
            @JsonProperty("id")
            private String dEnergy;

            @Comment("视图本地版本")
            private Integer localVersion;

            @Comment("视图发布版本")
            private Integer releaseVersion;

            @Comment("视图发布后新视图id")
            private String releaseDiagramId;

            @Comment("发布说明")
            private String releaseDesc;

            @Comment("预制视图发布ID")
            private String prepareDiagramId;

            @Comment("视图是否处于流程中 true = 流程中 / false = 未在流程中")
            private Integer flowStatus;
        }
    }
}
