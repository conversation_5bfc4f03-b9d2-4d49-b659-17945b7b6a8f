package com.uinnova.product.eam.model.dmv;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel("指定查询Rlt时附加的数据信息")
public enum VcCiRltQ {

    @ApiModelProperty("只有关系和属性值")
    CI_RLt_ATTR,

    @ApiModelProperty("有关系和属性值,还有起始CI,结束CI信息,不包含分类信息")
    CI_RLt_CI,

    @ApiModelProperty("有关系和属性值,还有起始CI,结束CI信息,不包含分类信息")
    CI_RLt_CI_Attr,

    @ApiModelProperty("有关系和属性值,还有起始CI,结束CI包含分类的信息")
    CI_RLt_CI_CLASS,

    @ApiModelProperty("有关系和属性值,还有起始CI,结束CI包含分类的信息")
    CI_RLt_CI_CLASS_ATTR_DEF;

}
