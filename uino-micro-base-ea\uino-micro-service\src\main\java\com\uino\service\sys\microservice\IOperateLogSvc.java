package com.uino.service.sys.microservice;

import java.util.List;

import com.binary.jdbc.Page;
import com.uino.bean.sys.base.ESOperateLog;
import com.uino.bean.sys.query.ESOperateLogSearchBean;

/**
 * 
 * <AUTHOR>
 *
 */
public interface IOperateLogSvc {
    /**
     * 条件查询系统操作日志
     * 
     * @param bean
     * @return
     */
    public Page<ESOperateLog> getOperateLogPageByCdt(ESOperateLogSearchBean bean);

    /**
     * 保存系统操作日志
     * 
     * @param log
     * @return
     */
    public Long saveOrUpdate(ESOperateLog log);

    /**
     * 批量保存系统操作日志
     * 
     * @param logs
     * @return
     */
    public Integer saveOrUpdateBatch(List<ESOperateLog> logs);

    /**
     * 按保留时长清除系统操作日志
     * 
     * @param durationDay
     * @return
     */
    public Integer clearOperateLogByDuration(Integer durationDay);
}
