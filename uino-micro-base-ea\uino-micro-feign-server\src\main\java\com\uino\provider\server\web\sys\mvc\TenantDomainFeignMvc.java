package com.uino.provider.server.web.sys.mvc;

import com.binary.jdbc.Page;
import com.uino.bean.sys.base.TenantDomain;
import com.uino.provider.feign.sys.TenantDomainFeign;
import com.uino.service.sys.microservice.ITenantDomainSvc;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("feign/tenantDomain")
public class TenantDomainFeignMvc implements TenantDomainFeign {

    @Autowired
    private ITenantDomainSvc svc;

    @Override
    public Long saveOrUpdate(TenantDomain tenantDomain) {
        return svc.saveOrUpdate(tenantDomain);
    }

    @Override
    public Page<TenantDomain> queryPage(int pageNum, int pageSize, String name) {
        return svc.queryPage(pageNum, pageSize, name);
    }

    @Override
    public Long deleteById(Long id) {
        return svc.deleteById(id);
    }

    @Override
    public Long startOrStop(Long id) {
        return svc.startOrStop(id);
    }

    @Override
    public boolean resetPasswdByAdmin(Long id) {
        return svc.resetPasswdByAdmin(id);
    }

    @Override
    public List<TenantDomain> queryAvailableList() {
        return svc.queryAvailableList();
    }
}
