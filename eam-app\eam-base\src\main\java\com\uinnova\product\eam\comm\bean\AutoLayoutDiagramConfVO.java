package com.uinnova.product.eam.comm.bean;

import com.uinnova.product.eam.comm.model.es.AutoLayoutDiagramConf;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uino.bean.cmdb.base.ESCIInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class AutoLayoutDiagramConfVO extends AutoLayoutDiagramConf {

    private CcCiClassInfo classInfo;
    private ESCIInfo centreCiInfo;
    private List<CcCiClassInfo> targetClassInfoList;
    private List<CcCiClassInfo> rltClassInfoList;

}
