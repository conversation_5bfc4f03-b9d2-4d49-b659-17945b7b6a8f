package com.uino.api.client.cmdb;

import java.util.List;
import java.util.Set;

import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClassDir;
import com.uino.bean.cmdb.base.CcImage;
import com.uino.bean.cmdb.business.ImageCount;
import com.uino.bean.cmdb.business.ImportDirMessage;
import com.uino.bean.cmdb.business.ImportResultMessage;
import com.uino.bean.cmdb.query.ESSearchImageBean;
import com.uino.bean.permission.query.SearchKeywordBean;

/**
 * 图标管理
 * 
 * <AUTHOR>
 */
public interface IImageApiSvc {

    /**
     * 导入zip 图标
     * 
     * @param file
     * @return
     */
	ImportResultMessage importZipImage(Integer sourceType, MultipartFile file);

    ImportResultMessage importZipImage(Long domainId, Integer sourceType, MultipartFile file);

    /**
     * 汇总查询目录下的图标
     * 
     * @return
     */
    List<ImageCount> queryImageDirList(CCcCiClassDir cdt);

    List<ImageCount> queryImageDirList(Long domainId, CCcCiClassDir cdt);


    /**
     * 全文检索目录下的图标
     *
     * @param bean
     * @return
     */
    Page<CcImage> queryImagePage(ESSearchImageBean bean);


    /**
     * 替换图标
     * 
     * @param imageId
     * @param file
     * @return
     */
    boolean replaceImage(Long imageId, MultipartFile file);


    /**
     * 删除图标
     * 
     * @param image
     * @return
     */
    boolean deleteImage(CcImage image);

    /**
     * 删除目录下的图标
     * 
     * @param dirId
     * @return
     */
    boolean deleteDirImage(Long dirId);

    /**
     * 导入图标
     * 
     * @param dirId
     *            文件夹id
     * @param file
     *            图标文件
     * @return
     */
    boolean importImage(Long dirId, MultipartFile file);

    /**
     * 批量导入图标
     * 
     * @param dirId
     * @param files
     * @return
     */
    ImportDirMessage importImages(Long dirId, MultipartFile... files);

    /**
     * 导出指定文件夹下图标
     * 
     * @param dirIds
     * @return
     */
    ResponseEntity<byte[]> exportImageZipByDirIds(Set<Long> dirIds);

    /**
     * 查询当前登陆用户置顶图片
     * 
     * @param bean
     *            查询条件
     * @return
     */
    List<CcImage> queryTopImage(SearchKeywordBean bean);



    /**
     * 上传3D图标
     * 
     * @param file
     *            zip文件
     * @param isCover
     *            是否全量，true=全量覆盖，false=增量添加
     * @return
     */
    ImportDirMessage import3DZipImage(MultipartFile file, Long dirId, boolean isCover);



    /**
     * 映射3D图标
     * 
     * @param image
     * @return
     */
    Long updateImageRlt(CcImage image);

    /**
     * 替换3D图标
     * 
     * @param imgId
     * @param file
     * @return
     */
    boolean replace3DImage(Long imgId, MultipartFile file);

    /**
     * 删除3D图标
     * 
     * @param image
     * @return
     */
    boolean delete3DImage(CcImage image);

    /**
     * 根据id查询图标
     * 
     * @param id
     * @return
     */
    CcImage queryImageById(Long id);

    /**
     * 统计符合条件的图标数量
     * 
     * @param bean
     * @return
     */
    Long countBySearchBean(ESSearchImageBean bean);

	/**
	 * 下载图标资源
	 * 
	 * @param imageId
	 */
	public ResponseEntity<byte[]> downloadImageResource(List<Long> ids);


    /**
     * 根据资源路径查询资源
     *
     * @param bean
     *
     */
    Page<CcImage> queryImageByPath(ESSearchImageBean bean);

    /**
     * whether to display the document attribute
     *
     * @return boolean
     * */
    Boolean isShowDocumentAttribute();
}
