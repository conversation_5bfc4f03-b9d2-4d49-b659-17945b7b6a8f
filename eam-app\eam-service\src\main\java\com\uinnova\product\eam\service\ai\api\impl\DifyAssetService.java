package com.uinnova.product.eam.service.ai.api.impl;

import com.alibaba.fastjson.JSONObject;
import com.binary.core.exception.BinaryException;
import com.binary.core.util.BinaryUtils;
import com.binary.framework.web.RemoteResult;
import com.uinnova.product.eam.comm.exception.BusinessException;
import com.uinnova.product.eam.model.AutoGraphVo;
import com.uinnova.product.eam.model.enums.GraphType;
import com.uinnova.product.eam.model.vo.*;
import com.uinnova.product.eam.service.ICISwitchSvc;
import com.uinnova.product.eam.service.IEamCIClassApiSvc;
import com.uinnova.product.eam.service.ai.api.IDifyAssetApiService;
import com.uinnova.product.eam.service.asset.AppSystemDetailSvc;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClass;
import com.uinnova.product.vmdb.comm.model.ci.CcCiClass;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CiGroupPage;
import com.uino.api.client.cmdb.ICIClassApiSvc;
import com.uino.bean.cmdb.base.LibType;
import com.uino.bean.cmdb.query.ESCISearchBean;
import com.uino.service.cmdb.microservice.IRltClassSvc;
import com.uino.util.sys.SysUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DifyAssetService implements IDifyAssetApiService {

    @Resource
    IEamCIClassApiSvc ciClassApiSvc;

    @Resource
    ICISwitchSvc ciSwitchSvc;

    @Resource
    AppSystemDetailSvc appSystemDetailSvc;
    @Resource
    private ICIClassApiSvc classSvc;
    @Resource
    private IRltClassSvc rltClassSvc;

    @Override
    public CiGroupPage queryPageBySearchBean(ESCISearchBeanVO bean, LibType libType) {
        isConvertibleToLong(bean);
        if (!CollectionUtils.isEmpty(bean.getClassCodes())) {
            List<CcCiClassInfo> classInfos = ciClassApiSvc.getByClassCodes(bean.getClassCodes(), SysUtil.getCurrentUserInfoNotThrow().getDomainId());
            if (CollectionUtils.isEmpty(classInfos) || classInfos.size() < bean.getClassCodes().size()) {
                throw new BinaryException("分类信息不存在");
            }
            List<Long> classIds = classInfos.stream().map(e -> e.getCiClass().getId()).collect(Collectors.toList());
            bean.getClassIds().addAll(classIds);
        }
        return ciSwitchSvc.queryPageBySearchBeanVO(bean, false, libType);
    }

    @Override
    public List<Map<String, String>> getSystemArchitectureDiagram(String ciCode) {
        return appSystemDetailSvc.getSystemArchitectureDiagram(ciCode);
    }

    private void isConvertibleToLong(ESCISearchBeanVO esciSearchBeanVO) {
        if (StringUtils.isNotBlank(esciSearchBeanVO.getGteTime())) {
            String gteTime = esciSearchBeanVO.getGteTime().replace("/", "");
            try {
                Long.parseLong(gteTime);
            } catch (NumberFormatException e) {
                throw new BusinessException("时间区间查询 大于等于传参有问题请核实在传入");
            }
        }
        if (StringUtils.isNotBlank(esciSearchBeanVO.getLteTime())) {
            String lteTime = esciSearchBeanVO.getLteTime().replace("/", "");
            try {
                Long.parseLong(lteTime);
            } catch (NumberFormatException e) {
                throw new BusinessException("时间区间查询 大于等于传参有问题请核实在传入");
            }
        }
        if (StringUtils.isNotBlank(esciSearchBeanVO.getGteTime()) && StringUtils.isNotBlank(esciSearchBeanVO.getLteTime())) {
            String gteTime = esciSearchBeanVO.getGteTime().replace("/", "");
            String lteTime = esciSearchBeanVO.getLteTime().replace("/", "");
            if (Long.parseLong(gteTime) > Long.parseLong(lteTime)) {
                throw new BusinessException("时间区间查询 起始时间不能大于终止时间");
            }
        }
    }

    @Override
    public RemoteResult autoDataAnalysis(String param) {
        JSONObject json;
        try {
            json = JSONObject.parseObject(param.replace("\\n","")
                    .replace("\\t","")
                    .replace("\\","")
                    .replace(" ", ""));
        } catch (Exception e) {
            return new RemoteResult(true, -2, null, param);
        }
        //大模型可能会直接基于历史会话数据直接调整，此时接口入参类型为RemoteResult，直接返回调整好的结果即可
        if (Boolean.TRUE.equals(json.getBoolean("success"))) {
            JSONObject data = json.getJSONObject("data");
            if (StringUtils.isBlank(data.getString("key"))) {
                return null;
            }
            try {
                GraphType graphType = GraphType.valueOf(data.getString("key"));
            } catch (Exception e) {
                log.error("graphType解析异常:{}", data.getString("key"));
                return null;
            }
            return json.toJavaObject(RemoteResult.class);
        }
        String graphType = json.getString("graphType");
        if (StringUtils.isBlank(graphType)) {
            return new RemoteResult(true, -2, null, "制图分类不能为空");
        }
        switch (GraphType.valueOf(graphType)) {
            case CONTEXT_GRAPH:
                return autoContextGraph(json.toJavaObject(DFAutoContextGraphParam.class));
            case SERVICE_GRAPH:
                return autoServiceGraph(json.toJavaObject(DFAutoServiceGraphParam.class));
            default:
                return new RemoteResult(true, -2, null, "不支持的制图类型");
        }
    }

    private RemoteResult autoContextGraph(DFAutoContextGraphParam param) {
        if (StringUtils.isBlank(param.getCenterElement())) {
            return new RemoteResult(true, -2, null, "未解析出中心元素，请进一步描述中心元素信息");
        }
        // 查询中心元素的CI数据
        List<String> ciSearchWord = new ArrayList<>();
        ciSearchWord.add(param.getCenterElement());
        ESCISearchBean ciSearchBean = new ESCISearchBean();
        ciSearchBean.setPageNum(1);
        ciSearchBean.setPageSize(1);
        ciSearchBean.setWords(ciSearchWord);
        ciSearchBean.setWordLabel(true);
        CiGroupPage page = ciSwitchSvc.queryPageBySearchBean(ciSearchBean, false, LibType.DESIGN);
        if (BinaryUtils.isEmpty(page.getData())) {
            return new RemoteResult(true, -2, null, "未查询到中心元素信息");
        }
        CcCiInfo ciInfo = page.getData().get(0);

        List<CcCiClassInfo> rltClassList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(param.getRelations())) {
            CCcCiClass cdt = new CCcCiClass();
            cdt.setClassNames(param.getRelations().toArray(new String[0]));
            rltClassList = rltClassSvc.getRltClassByCdt(cdt);
        }
        List<Long> rltClassIds = rltClassList.stream().map(CcCiClassInfo::getCiClass)
                .map(CcCiClass::getId).collect(Collectors.toList());

        CcCiClassInfo classInfo = classSvc.queryClassInfoById(ciInfo.getCi().getClassId());
        if (classInfo == null) {
            return new RemoteResult(true, -2, null, "未查询到中心元素信息");
        }
        //布局
        AutoGraphVo.LayoutScheme layoutScheme = new AutoGraphVo.LayoutScheme();
        DFAutoContextGraphLayout layout = param.getLayout();
        layoutScheme.setLayoutType(layout.getLayoutType());
        if (layout.getLayoutType() == 1) {
            layoutScheme.setColumnSpacing(layout.getColumnSpacing());
            layoutScheme.setLayerSpacing(layout.getLayerSpacing());
            layoutScheme.setLayoutDirection(layout.getLayoutDirection());
        }
        //数据
        AutoGraphVo.DataScheme dataScheme = new AutoGraphVo.DataScheme();
        dataScheme.setCoreCiCode(ciInfo.getCi().getCiCode());
        dataScheme.setCiClass(ciInfo.getCi().getClassId());
        dataScheme.setRelatedRltIds(rltClassIds);
        dataScheme.setRelatedClassIds(Collections.singletonList(ciInfo.getCi().getClassId()));
        //制品--暂时不用考虑
        AutoGraphVo.ArtifactScheme artifactScheme = new AutoGraphVo.ArtifactScheme();
        AutoGraphVo.Value value = new AutoGraphVo.Value(layoutScheme, dataScheme, artifactScheme, param.getStyle());
        AutoGraphVo autoGraphVo = new AutoGraphVo();
        autoGraphVo.setKey(param.getGraphType().name());
        autoGraphVo.setValue(value);
        return new RemoteResult(autoGraphVo);
    }

    private RemoteResult autoServiceGraph(DFAutoServiceGraphParam param) {
        if (param.getLayerTree() == null) {
            return new RemoteResult(true, -2, null, "未解析出层级分类，请进一步描述分类信息");
        }
        DFAutoServiceGraphLevelData layerTree = param.getLayerTree();
        Set<String> ciClassNames = new HashSet<>();
        ciClassNames.add(layerTree.getTitle());
        if (!CollectionUtils.isEmpty(layerTree.getChildren())) {
            for (DFAutoServiceGraphLevelData child : layerTree.getChildren()) {
                fillCIClassNames(child, ciClassNames);
            }
        }
        if (CollectionUtils.isEmpty(ciClassNames)) {
            return new RemoteResult(true, -2, null, "未解析出层级分类，请进一步描述分类信息");
        }

        CCcCiClass cdt = new CCcCiClass();
        cdt.setClassNames(ciClassNames.toArray(new String[0]));
        List<CcCiClassInfo> ciClassInfos = classSvc.queryClassByCdt(cdt);

        AutoGraphVo.DataScheme dataScheme = new AutoGraphVo.DataScheme();
        if (!CollectionUtils.isEmpty(ciClassInfos)) {
            Map<String, CcCiClass> ciClassInfoMap = ciClassInfos.stream().map(CcCiClassInfo::getCiClass)
                    .collect(Collectors.toMap(CcCiClass::getClassName, Function.identity(), (k1,k2) -> k1));
            if (ciClassInfoMap.containsKey(layerTree.getTitle())) {
                layerTree.setId(ciClassInfoMap.get(layerTree.getTitle()).getId());
                if (!CollectionUtils.isEmpty(layerTree.getChildren())) {
                    List<DFAutoServiceGraphLevelData> cs = new ArrayList<>();
                    for (DFAutoServiceGraphLevelData child : layerTree.getChildren()) {
                        if (ciClassInfoMap.containsKey(child.getTitle())) {
                            child.setId(ciClassInfoMap.get(child.getTitle()).getId());
                            child.setChildren(fillCIClassId(child, ciClassInfoMap));
                            cs.add(child);
                        }
                    }
                    layerTree.setChildren(cs);
                }
                dataScheme.setLayerTree(Collections.singletonList(layerTree));
            }
        }
        AutoGraphVo.ArtifactScheme artifactScheme = new AutoGraphVo.ArtifactScheme();
        AutoGraphVo.Value value = new AutoGraphVo.Value(param.getLayoutScheme(), dataScheme, artifactScheme, null);
        AutoGraphVo autoGraphVo = new AutoGraphVo();
        autoGraphVo.setKey(param.getGraphType().name());
        autoGraphVo.setValue(value);
        return new RemoteResult(autoGraphVo);
    }

    private void fillCIClassNames(DFAutoServiceGraphLevelData data, Set<String> ciClassNames) {
        ciClassNames.add(data.getTitle());
        if (!CollectionUtils.isEmpty(data.getChildren())) {
            for (DFAutoServiceGraphLevelData child : data.getChildren()) {
                fillCIClassNames(child, ciClassNames);
            }
        }
    }

    private List<DFAutoServiceGraphLevelData> fillCIClassId(DFAutoServiceGraphLevelData child, Map<String, CcCiClass> ciClassInfoMap) {
        if (CollectionUtils.isEmpty(child.getChildren())) {
            return new ArrayList<>();
        }
        List<DFAutoServiceGraphLevelData> cs = new ArrayList<>();
        for (DFAutoServiceGraphLevelData c : child.getChildren()) {
            if (ciClassInfoMap.containsKey(c.getTitle())) {
                c.setId(ciClassInfoMap.get(c.getTitle()).getId());
                c.setChildren(fillCIClassId(c, ciClassInfoMap));
                cs.add(c);
            }
        }
        return cs;
    }
}
