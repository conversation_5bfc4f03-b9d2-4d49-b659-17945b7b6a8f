package com.uino.provider.feign.cmdb;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.uinnova.product.vmdb.comm.model.ci.CCcCiClassDir;
import com.uino.bean.cmdb.base.CcCiClassDir;
import com.uino.provider.feign.config.BaseFeignConfig;

/**
 * 
 * <AUTHOR>
 *
 */
@Component
@FeignClient(name = "${feign-server-name:tarsier-platform}", path = "${feign-server-path:tarsier-platform}/feign/dir", configuration = {
		BaseFeignConfig.class })
public interface DirFeign {

	/**
	 * 保存或更新目录信息
	 * 
	 * @param dir
	 * @return
	 */
	@PostMapping("saveOrUpdateDir")
	Long saveOrUpdateDir(@RequestBody CcCiClassDir dir);

	/**
	 * 根据id删除目录
	 * 
	 * @param id
	 * @return
	 */
	@PostMapping("removeDirById")
	Integer removeDirById(@RequestBody Long id);

    /**
     * 条件查询文件夹信息
     * 
     * @param cdt ciType=1,查询分类文件夹;=3查询图标文件夹
     * @param orders
     * @param isAsc
     * @return
     */
    @PostMapping("queryDirList")
    public List<CcCiClassDir> queryDirList(@RequestBody CCcCiClassDir cdt, @RequestParam(value = "orders", required = false) String orders,
        @RequestParam(value = "isAsc", required = false) Boolean isAsc);
}
