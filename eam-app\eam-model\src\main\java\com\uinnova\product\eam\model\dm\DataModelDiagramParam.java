package com.uinnova.product.eam.model.dm;

import com.alibaba.fastjson.JSON;
import com.binary.core.util.BinaryUtils;
import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.base.diagram.model.ESDiagramInfoDTO;
import com.uinnova.product.eam.base.diagram.model.ESDiagramLink;
import com.uinnova.product.eam.base.diagram.model.ESDiagramModel;
import com.uinnova.product.eam.base.diagram.model.ESDiagramNode;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据建模视图参数传递
 * <AUTHOR>
 */
@Data
public class DataModelDiagramParam {
    private String ownerCode;
    @Comment("视图信息")
    private ESDiagramInfoDTO diagram;
    private ESDiagramModel model;
    @Comment("node节点ciCodeMap")
    private Map<String, ESDiagramNode> nodeMap = new HashMap<>();
    @Comment("node节点keyMap")
    private Map<String, ESDiagramNode> keyMap = new HashMap<>();
    @Comment("按源端节点key分组关系线")
    private Map<String, List<ESDiagramLink>> fromLinkGroup = new HashMap<>();
    @Comment("按目标端节点key分组关系线")
    private Map<String, List<ESDiagramLink>> toLinkGroup = new HashMap<>();

    public void setModel(ESDiagramModel model) {
        this.model = model;
        if(!BinaryUtils.isEmpty(model.getNodeDataArray())){
            for (ESDiagramNode node : model.getNodeDataArray()) {
                if(BinaryUtils.isEmpty(node.getCiCode())){
                    continue;
                }
                this.nodeMap.put(node.getCiCode(), node);
                this.keyMap.put(node.getKey(), node);
            }
        }
        if(!BinaryUtils.isEmpty(model.getLinkDataArray())){
            for (ESDiagramLink link : model.getLinkDataArray()) {
                Object from = JSON.parseObject(link.getLinkJson()).get("from");
                if(!BinaryUtils.isEmpty(from)){
                    this.fromLinkGroup.putIfAbsent(from.toString(), new ArrayList<>());
                    this.fromLinkGroup.get(from.toString()).add(link);
                }
                Object to = JSON.parseObject(link.getLinkJson()).get("to");
                if(!BinaryUtils.isEmpty(to)){
                    this.toLinkGroup.putIfAbsent(to.toString(), new ArrayList<>());
                    this.toLinkGroup.get(to.toString()).add(link);
                }
            }
        }
    }
}
