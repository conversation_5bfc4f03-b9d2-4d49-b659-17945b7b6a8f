package com.uino.bean.sys.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 租户域
 *
 * <AUTHOR>
 * @Date 2021/10/27
 * @Version 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value="租户域",description = "域信息")
public class TenantDomain {

    /*
     * 域id保持id一致
     */
    @ApiModelProperty(value="id",example = "123")
    private Long id;

    @ApiModelProperty(value="租户名称",example = "123")
    private String name;

    /**
     * 临时使用，删除域时没有真清数据
     */
    @ApiModelProperty(value="数据状态[DATA_STATUS]    1-正常 0-删除",example = "1")
    private Integer dataStatus;

    @ApiModelProperty(value="启动状态[enableStatus]    -1 正在创建 1-启动 0-停用",example = "1")
    private Integer enableStatus;

    /** 创建人 */
    @ApiModelProperty(value="创建人",example = "mike")
    private String creator;

    /** 修改人 */
    @ApiModelProperty(value="修改人",example = "mike")
    private String modifier;

    /** 创建时间 */
    @ApiModelProperty(value="创建时间")
    private Long createTime;

    /** 修改时间 */
    @ApiModelProperty(value="修改时间")
    private Long modifyTime;
}
