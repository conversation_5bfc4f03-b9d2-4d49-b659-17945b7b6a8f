package com.uinnova.product.eam.web.diagram.bean;

import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;

public class CiDiagramCdt implements Condition {

	private static final long serialVersionUID = 1L;

	@Comment("CI-ID数组")
	private Long[] ciIds;

	@Comment("是否公开:1=开放    0=私有")
	private Integer authOpen;
	
	@Comment("是否获取xml信息 1=是 2=否")
	private Integer retXml;
	
	@Comment("CI-CODE 数组")
	private String[] ciCodes;
	
	

	public String[] getCiCodes() {
		return ciCodes;
	}

	public void setCiCodes(String[] ciCodes) {
		this.ciCodes = ciCodes;
	}

	public Integer getRetXml() {
		return retXml;
	}

	public void setRetXml(Integer retXml) {
		this.retXml = retXml;
	}

	public Long[] getCiIds() {
		return ciIds;
	}

	public void setCiIds(Long[] ciIds) {
		this.ciIds = ciIds;
	}

	public Integer getAuthOpen() {
		return authOpen;
	}

	public void setAuthOpen(Integer authOpen) {
		this.authOpen = authOpen;
	}

}
