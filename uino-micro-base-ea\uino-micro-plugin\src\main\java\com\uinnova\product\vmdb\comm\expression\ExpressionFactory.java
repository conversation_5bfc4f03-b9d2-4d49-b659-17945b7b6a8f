package com.uinnova.product.vmdb.comm.expression;

import com.uinnova.product.vmdb.comm.expression.support.ArrayExpression;
import com.uinnova.product.vmdb.comm.expression.support.OrExpression;
import com.uinnova.product.vmdb.comm.expression.support.SimpleExpression;

/**
 * 
 * <AUTHOR>
 *
 */
public abstract class ExpressionFactory {

    /**
     * 字段运算得出表达式
     * 
     * @param field
     * @param op
     * @param value
     * @return
     */
    public static <T> Expression<T> getExpression(Field<T> field, OP op, String value) {
        return new SimpleExpression<T>(field, op, value);
    }

    /**
     * 字段运算得出表达式
     * 
     * @param field
     * @param op
     * @param value
     * @return
     */
    public static <T> Expression<T> getExpression(Field<T> field, OP op, String[] values) {
        return new ArrayExpression<T>(field, op, values);
    }

    /**
     * 获取OR表达式
     * 
     * @param master
     * @param or
     * @return
     */
    public static <T> Expression<T> getExpression(Expression<T> master, Expression<T> or) {
        return new OrExpression<T>(master, or);
    }

}
