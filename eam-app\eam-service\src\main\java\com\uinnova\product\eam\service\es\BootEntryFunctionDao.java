package com.uinnova.product.eam.service.es;

import com.uinnova.product.eam.comm.model.es.BootEntryFunctionConfig;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;

@Service
public class BootEntryFunctionDao extends AbstractESBaseDao<BootEntryFunctionConfig, BootEntryFunctionConfig> {
    @Override
    public String getIndex() {
        return "uino_eam_boot_function_config";
    }

    @Override
    public String getType() {
        return getIndex();
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }
}
