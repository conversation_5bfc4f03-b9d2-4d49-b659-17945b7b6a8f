<?xml version="1.0"?>
<project
		xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd"
		xmlns="http://maven.apache.org/POM/4.0.0"
		xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.uino</groupId>
		<artifactId>eam-base</artifactId>
		<version>1.0.0-SNAPSHOT</version>
	</parent>
	<artifactId>uino-eam-micro-feign-client</artifactId>
	<name>uino-eam-micro-feign-client</name>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
	</properties>
	<dependencies>

		<dependency>
			<groupId>com.uino</groupId>
			<artifactId>uino-eam-micro-bean</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>org.elasticsearch</groupId>
			<artifactId>elasticsearch</artifactId>
			<version>${elasticsearch.version}</version>
			<exclusions>
				<exclusion>
					<groupId>org.elasticsearch</groupId>
					<artifactId>jna</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<!--		nacos discovery-->
		<dependency>
			<groupId>com.alibaba.cloud</groupId>
			<artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
			<version>${nacos.config.version}</version>
			<exclusions>
				<exclusion>
					<groupId>commons-lang</groupId>
					<artifactId>commons-lang</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<!-- https://mvnrepository.com/artifact/org.springframework.cloud/spring-cloud-starter-netflix-hystrix -->
<!--		<dependency>-->
<!--			<groupId>org.springframework.cloud</groupId>-->
<!--			<artifactId>spring-cloud-starter-netflix-hystrix</artifactId>-->
<!--			<version>2.2.10.RELEASE</version>-->
<!--		</dependency>-->
		<!--		nacos config-->
		<dependency>
			<groupId>com.alibaba.cloud</groupId>
			<artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
			<version>${nacos.config.version}</version>
		</dependency>

		<!--		sentinel-->
<!--		<dependency>-->
<!--			<groupId>com.alibaba.cloud</groupId>-->
<!--			<artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>-->
<!--			<version>${nacos.config.version}</version>-->
<!--			<exclusions>-->
<!--				<exclusion>-->
<!--					<artifactId>jackson-dataformat-xml</artifactId>-->
<!--					<groupId>com.fasterxml.jackson.dataformat</groupId>-->
<!--				</exclusion>-->
<!--			</exclusions>-->
<!--		</dependency>-->

		<!--        openfegin-->
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-openfeign</artifactId>
			<version>${openfeign.version}</version>
			<exclusions>
				<exclusion>
					<artifactId>spring-boot-starter-logging</artifactId>
					<groupId>org.springframework.boot</groupId>
				</exclusion>
			</exclusions>
		</dependency>
	</dependencies>
</project>
