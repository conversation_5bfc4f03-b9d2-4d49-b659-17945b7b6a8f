package com.uinnova.product.vmdb.provider.rlt.bean;

import com.binary.framework.bean.annotation.Comment;

import java.io.Serializable;
import java.util.List;

public class ImpactPath implements Serializable{

	private static final long serialVersionUID = 1L;
	
	@Comment("关注分类ID")
	private Long focusClassId;
	
	@Comment("关联分类关系IDs")
	private List<Long> ciClassRltIds;

	public Long getFocusClassId() {
		return focusClassId;
	}

	public void setFocusClassId(Long focusClassId) {
		this.focusClassId = focusClassId;
	}

	public List<Long> getCiClassRltIds() {
		return ciClassRltIds;
	}

	public void setCiClassRltIds(List<Long> ciClassRltIds) {
		this.ciClassRltIds = ciClassRltIds;
	}
	
	
}
