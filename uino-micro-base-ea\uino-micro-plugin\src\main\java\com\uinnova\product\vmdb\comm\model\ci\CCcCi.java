package com.uinnova.product.vmdb.comm.model.ci;

import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;

/**
 * 
 * <AUTHOR>
 *
 */
@Comment("CI表[CC_CI]")
public class CCcCi implements Condition {
    private static final long serialVersionUID = 1L;

    @Comment("ID[ID] operate-Equal[=]")
    private Long id;

    @Comment("ID[ID] operate-In[in]")
    private Long[] ids;

    @Comment("ID[ID] operate-GTEqual[>=]")
    private Long startId;

    @Comment("ID[ID] operate-LTEqual[<=]")
    private Long endId;

    @Comment("CI代码[CI_CODE] operate-Like[like]")
    private String ciCode;

    @Comment("CI代码[CI_CODE] operate-Equal[=]")
    private String ciCodeEqual;

    @Comment("CI代码[CI_CODE] operate-In[in]")
    private String[] ciCodes;

    @Comment("CI描述[CI_DESC] operate-Like[like]")
    private String ciDesc;

    @Comment("所属分类[CLASS_ID] operate-Equal[=]")
    private Long classId;

    @Comment("所属分类[CLASS_ID] operate-In[in]")
    private Long[] classIds;

    @Comment("所属分类[CLASS_ID] operate-GTEqual[>=]")
    private Long startClassId;

    @Comment("所属分类[CLASS_ID] operate-LTEqual[<=]")
    private Long endClassId;

    @Comment("来源[SOURCE_ID] operate-Equal[=]")
    private Long sourceId;

    @Comment("来源[SOURCE_ID] operate-In[in]")
    private Long[] sourceIds;

    @Comment("来源[SOURCE_ID] operate-GTEqual[>=]")
    private Long startSourceId;

    @Comment("来源[SOURCE_ID] operate-LTEqual[<=]")
    private Long endSourceId;

    private String ownerCodeEqual;

    private String[] ownerCodes;

    @Comment("所属组织[ORG_ID] operate-Equal[=]")
    private Long orgId;

    @Comment("所属组织[ORG_ID] operate-In[in]")
    private Long[] orgIds;

    @Comment("所属组织[ORG_ID] operate-GTEqual[>=]")
    private Long startOrgId;

    @Comment("所属组织[ORG_ID] operate-LTEqual[<=]")
    private Long endOrgId;

    @Comment("子类型[SUB_CLASS] operate-Like[like]")
    private String subClass;

    @Comment("CI版本[CI_VERSION] operate-Like[like]")
    private String ciVersion;

    @Comment("CI版本[CI_VERSION] operate-Equal[=]")
    private String ciVersionEqual;

    @Comment("CI版本[CI_VERSION] operate-In[in]")
    private String[] ciVersions;

    @Comment("CI业务主键值的hashcode[HASH_CODE]")
    private Integer hashCode;

    @Comment("CI业务主键值的hashcode[HASH_CODE]")
    private Integer[] hashCodes;

    @Comment("备用_1[CUSTOM_1] operate-Like[like]")
    private String custom1;

    @Comment("备用_2[CUSTOM_2] operate-Like[like]")
    private String custom2;

    @Comment("备用_3[CUSTOM_3] operate-Like[like]")
    private String custom3;

    @Comment("备用_4[CUSTOM_4] operate-Like[like]")
    private String custom4;

    @Comment("备用_5[CUSTOM_5] operate-Like[like]")
    private String custom5;

    @Comment("备用_6[CUSTOM_6] operate-Like[like]")
    private String custom6;

    @Comment("CI属性[ATTRS_STR] operate-Like[like]")
    private String attrsStr;

    @Comment("CI属性[ATTRS_STR] operate-Equal[=]")
    private String attrsStrEqual;

    @Comment("CI属性[ATTRS_STR] operate-In[in]")
    private String[] attrsStrs;

    @Comment("所属域[DOMAIN_ID] operate-Equal[=]")
    private Long domainId;

    @Comment("所属域[DOMAIN_ID] operate-In[in]")
    private Long[] domainIds;

    @Comment("所属域[DOMAIN_ID] operate-GTEqual[>=]")
    private Long startDomainId;

    @Comment("所属域[DOMAIN_ID] operate-LTEqual[<=]")
    private Long endDomainId;

    @Comment("数据状态[DATA_STATUS] operate-Equal[=]    1-正常 0-删除")
    private Integer dataStatus;

    @Comment("数据状态[DATA_STATUS] operate-In[in]    1-正常 0-删除")
    private Integer[] dataStatuss;

    @Comment("数据状态[DATA_STATUS] operate-GTEqual[>=]    1-正常 0-删除")
    private Integer startDataStatus;

    @Comment("数据状态[DATA_STATUS] operate-LTEqual[<=]    1-正常 0-删除")
    private Integer endDataStatus;

    @Comment("创建人[CREATOR] operate-Like[like]")
    private String creator;

    @Comment("创建人[CREATOR] operate-Equal[=]")
    private String creatorEqual;

    @Comment("创建人[CREATOR] operate-In[in]")
    private String[] creators;

    @Comment("修改人[MODIFIER] operate-Like[like]")
    private String modifier;

    @Comment("修改人[MODIFIER] operate-Equal[=]")
    private String modifierEqual;

    @Comment("修改人[MODIFIER] operate-In[in]")
    private String[] modifiers;

    @Comment("创建时间[CREATE_TIME] operate-Equal[=]    yyyyMMddHHmmss")
    private Long createTime;

    @Comment("创建时间[CREATE_TIME] operate-In[in]    yyyyMMddHHmmss")
    private Long[] createTimes;

    @Comment("创建时间[CREATE_TIME] operate-GTEqual[>=]    yyyyMMddHHmmss")
    private Long startCreateTime;

    @Comment("创建时间[CREATE_TIME] operate-LTEqual[<=]    yyyyMMddHHmmss")
    private Long endCreateTime;

    @Comment("修改时间[MODIFY_TIME] operate-Equal[=]    yyyyMMddHHmmss")
    private Long modifyTime;

    @Comment("修改时间[MODIFY_TIME] operate-In[in]    yyyyMMddHHmmss")
    private Long[] modifyTimes;

    @Comment("修改时间[MODIFY_TIME] operate-GTEqual[>=]    yyyyMMddHHmmss")
    private Long startModifyTime;

    @Comment("修改时间[MODIFY_TIME] operate-LTEqual[<=]    yyyyMMddHHmmss")
    private Long endModifyTime;

    private String diagramId;

    private String[] diagramIds;

    private String sheetId;

    private String[] sheetIds;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long[] getIds() {
        return ids;
    }

    public void setIds(Long[] ids) {
        this.ids = ids;
    }

    public Long getStartId() {
        return startId;
    }

    public void setStartId(Long startId) {
        this.startId = startId;
    }

    public Long getEndId() {
        return endId;
    }

    public void setEndId(Long endId) {
        this.endId = endId;
    }

    public String getCiCode() {
        return ciCode;
    }

    public void setCiCode(String ciCode) {
        this.ciCode = ciCode;
    }

    public String getCiCodeEqual() {
        return ciCodeEqual;
    }

    public void setCiCodeEqual(String ciCodeEqual) {
        this.ciCodeEqual = ciCodeEqual;
    }

    public String[] getCiCodes() {
        return ciCodes;
    }

    public void setCiCodes(String[] ciCodes) {
        this.ciCodes = ciCodes;
    }

    public String getCiDesc() {
        return ciDesc;
    }

    public void setCiDesc(String ciDesc) {
        this.ciDesc = ciDesc;
    }

    public Long getClassId() {
        return classId;
    }

    public void setClassId(Long classId) {
        this.classId = classId;
    }

    public Long[] getClassIds() {
        return classIds;
    }

    public void setClassIds(Long[] classIds) {
        this.classIds = classIds;
    }

    public Long getStartClassId() {
        return startClassId;
    }

    public void setStartClassId(Long startClassId) {
        this.startClassId = startClassId;
    }

    public Long getEndClassId() {
        return endClassId;
    }

    public void setEndClassId(Long endClassId) {
        this.endClassId = endClassId;
    }

    public Long getSourceId() {
        return sourceId;
    }

    public void setSourceId(Long sourceId) {
        this.sourceId = sourceId;
    }

    public Long[] getSourceIds() {
        return sourceIds;
    }

    public void setSourceIds(Long[] sourceIds) {
        this.sourceIds = sourceIds;
    }

    public Long getStartSourceId() {
        return startSourceId;
    }

    public void setStartSourceId(Long startSourceId) {
        this.startSourceId = startSourceId;
    }

    public Long getEndSourceId() {
        return endSourceId;
    }

    public void setEndSourceId(Long endSourceId) {
        this.endSourceId = endSourceId;
    }

    public String getOwnerCodeEqual() {
        return ownerCodeEqual;
    }

    public void setOwnerCodeEqual(String ownerCodeEqual) {
        this.ownerCodeEqual = ownerCodeEqual;
    }

    public String[] getOwnerCodes() {
        return ownerCodes;
    }

    public void setOwnerCodes(String[] ownerCodes) {
        this.ownerCodes = ownerCodes;
    }

    public Long getOrgId() {
        return orgId;
    }

    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    public Long[] getOrgIds() {
        return orgIds;
    }

    public void setOrgIds(Long[] orgIds) {
        this.orgIds = orgIds;
    }

    public Long getStartOrgId() {
        return startOrgId;
    }

    public void setStartOrgId(Long startOrgId) {
        this.startOrgId = startOrgId;
    }

    public Long getEndOrgId() {
        return endOrgId;
    }

    public void setEndOrgId(Long endOrgId) {
        this.endOrgId = endOrgId;
    }

    public String getSubClass() {
        return subClass;
    }

    public void setSubClass(String subClass) {
        this.subClass = subClass;
    }

    public String getCiVersion() {
        return ciVersion;
    }

    public void setCiVersion(String ciVersion) {
        this.ciVersion = ciVersion;
    }

    public String getCiVersionEqual() {
        return ciVersionEqual;
    }

    public void setCiVersionEqual(String ciVersionEqual) {
        this.ciVersionEqual = ciVersionEqual;
    }

    public String[] getCiVersions() {
        return ciVersions;
    }

    public void setCiVersions(String[] ciVersions) {
        this.ciVersions = ciVersions;
    }

    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }

    public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }

    public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }

    public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }

    public String getCustom5() {
        return custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }

    public String getCustom6() {
        return custom6;
    }

    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }

    public String getAttrsStr() {
        return attrsStr;
    }

    public void setAttrsStr(String attrsStr) {

        this.attrsStr = attrsStr;
    }

    public String getAttrsStrEqual() {
        return attrsStrEqual;
    }

    public void setAttrsStrEqual(String attrsStrEqual) {
        this.attrsStrEqual = attrsStrEqual;
    }

    public String[] getAttrsStrs() {
        return attrsStrs;
    }

    public void setAttrsStrs(String[] attrsStrs) {
        this.attrsStrs = attrsStrs;
    }

    public Long getDomainId() {
        return domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }

    public Long[] getDomainIds() {
        return domainIds;
    }

    public void setDomainIds(Long[] domainIds) {
        this.domainIds = domainIds;
    }

    public Long getStartDomainId() {
        return startDomainId;
    }

    public void setStartDomainId(Long startDomainId) {
        this.startDomainId = startDomainId;
    }

    public Long getEndDomainId() {
        return endDomainId;
    }

    public void setEndDomainId(Long endDomainId) {
        this.endDomainId = endDomainId;
    }

    public Integer getDataStatus() {
        return dataStatus;
    }

    public void setDataStatus(Integer dataStatus) {
        this.dataStatus = dataStatus;
    }

    public Integer[] getDataStatuss() {
        return dataStatuss;
    }

    public void setDataStatuss(Integer[] dataStatuss) {
        this.dataStatuss = dataStatuss;
    }

    public Integer getStartDataStatus() {
        return startDataStatus;
    }

    public void setStartDataStatus(Integer startDataStatus) {
        this.startDataStatus = startDataStatus;
    }

    public Integer getEndDataStatus() {
        return endDataStatus;
    }

    public void setEndDataStatus(Integer endDataStatus) {
        this.endDataStatus = endDataStatus;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getCreatorEqual() {
        return creatorEqual;
    }

    public void setCreatorEqual(String creatorEqual) {
        this.creatorEqual = creatorEqual;
    }

    public String[] getCreators() {
        return creators;
    }

    public void setCreators(String[] creators) {
        this.creators = creators;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public String getModifierEqual() {
        return modifierEqual;
    }

    public void setModifierEqual(String modifierEqual) {
        this.modifierEqual = modifierEqual;
    }

    public String[] getModifiers() {
        return modifiers;
    }

    public void setModifiers(String[] modifiers) {
        this.modifiers = modifiers;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long[] getCreateTimes() {
        return createTimes;
    }

    public void setCreateTimes(Long[] createTimes) {
        this.createTimes = createTimes;
    }

    public Long getStartCreateTime() {
        return startCreateTime;
    }

    public void setStartCreateTime(Long startCreateTime) {
        this.startCreateTime = startCreateTime;
    }

    public Long getEndCreateTime() {
        return endCreateTime;
    }

    public void setEndCreateTime(Long endCreateTime) {
        this.endCreateTime = endCreateTime;
    }

    public Long getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Long[] getModifyTimes() {
        return modifyTimes;
    }

    public void setModifyTimes(Long[] modifyTimes) {
        this.modifyTimes = modifyTimes;
    }

    public Long getStartModifyTime() {
        return startModifyTime;
    }

    public void setStartModifyTime(Long startModifyTime) {
        this.startModifyTime = startModifyTime;
    }

    public Long getEndModifyTime() {
        return endModifyTime;
    }

    public void setEndModifyTime(Long endModifyTime) {
        this.endModifyTime = endModifyTime;
    }

    public Integer getHashCode() {
        return hashCode;
    }

    public void setHashCode(Integer hashCode) {
        this.hashCode = hashCode;
    }

    public Integer[] getHashCodes() {
        return hashCodes;
    }

    public void setHashCodes(Integer[] hashCodes) {
        this.hashCodes = hashCodes;
    }

    public String getDiagramId() {
        return diagramId;
    }

    public void setDiagramId(String diagramId) {
        this.diagramId = diagramId;
    }

    public String[] getDiagramIds() {
        return diagramIds;
    }

    public void setDiagramIds(String[] diagramIds) {
        this.diagramIds = diagramIds;
    }

    public String getSheetId() {
        return sheetId;
    }

    public void setSheetId(String sheetId) {
        this.sheetId = sheetId;
    }

    public String[] getSheetIds() {
        return sheetIds;
    }

    public void setSheetIds(String[] sheetIds) {
        this.sheetIds = sheetIds;
    }
}
