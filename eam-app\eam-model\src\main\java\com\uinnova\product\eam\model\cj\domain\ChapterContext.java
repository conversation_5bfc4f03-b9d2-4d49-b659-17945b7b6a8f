package com.uinnova.product.eam.model.cj.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 方案章节详情（章节的延续） EntityBean、Condition
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ChapterContext {

    /**
     * 方案id
     */
    private Long planId;

    /**
     * 主键id， 跟章节用同一个id
     */
    private Long id;

    /**
     * 是否可自行添加内容块
     */
    private Boolean userAddContent;

    /**
     * 章节说明
     */
    private String chapterDesc;

    /**
     * 备注
     */
    private String remark;

    /**
     * 上下文内容块
     */
    private List<ContextModule> moduleList;

    /**
     * 业务主键
     */
    private String businessId;

    /**
     * 版本号
     */
    private String version;

    /**
     * 状态 0:历史版本 1:草稿状态 2:发布状态
     */
    private Integer status;
}
