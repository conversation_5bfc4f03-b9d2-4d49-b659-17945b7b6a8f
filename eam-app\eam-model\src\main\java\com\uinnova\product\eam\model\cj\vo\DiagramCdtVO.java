package com.uinnova.product.eam.model.cj.vo;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.comm.model.CVcDiagram;
import com.uinnova.product.eam.model.DiagramQ;

@Comment("我的查询视图参数")
public class DiagramCdtVO extends CVcDiagram{

	private static final long serialVersionUID = 1L;

	@Comment("父文件夹ID")
	private Long dirId;
	
	@Comment("目录类型")
	private Integer dirType;
	
	@Comment("模糊查询字段信息")
	private String like;
	
	@Comment("筛选类型[0:初始化,1:单图,2:组合视图,3:文件夹,4:我的模版]")
	private Integer type;
	
	@Comment("查询类型：0:名称,1:作者,2:标签,3:CI")
	private Integer queryType;
	
	@Comment("tagId")
	private Long tagId;
	
	@Comment("视图id")
	private Long diagramId;
	
	@Comment("需要查询的视图附加信息")
	private DiagramQ[] diagramQs;

	@Comment("是否是我协作的查询")
	private Integer isConcert;

	@Comment("按照字段排序")
	private String orders;

	@Comment("查询入口 本地 公开")
	private Integer isOpen;

	public String getOrders() {
		return orders;
	}

	public void setOrders(String orders) {
		this.orders = orders;
	}

	//是否带视图权限
	private boolean auth=false;


	public Integer getIsConcert() {
		return isConcert;
	}

	public void setIsConcert(Integer isConcert) {
		this.isConcert = isConcert;
	}

	public DiagramQ[] getDiagramQs() {
		return diagramQs;
	}

	public void setDiagramQs(DiagramQ[] diagramQs) {
		this.diagramQs = diagramQs;
	}

	public Integer getDirType() {
		return dirType;
	}

	public void setDirType(Integer dirType) {
		this.dirType = dirType;
	}

	public Long getTagId() {
		return tagId;
	}

	public void setTagId(Long tagId) {
		this.tagId = tagId;
	}

	public Long getDiagramId() {
		return diagramId;
	}

	public void setDiagramId(Long diagramId) {
		this.diagramId = diagramId;
	}

	public Integer getQueryType() {
		return queryType;
	}

	public void setQueryType(Integer queryType) {
		this.queryType = queryType;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public Long getDirId() {
		return dirId;
	}

	public void setDirId(Long dirId) {
		this.dirId = dirId;
	}

	public String getLike() {
		return like;
	}

	public void setLike(String like) {
		this.like = like;
	}

	public boolean isAuth() {
		return auth;
	}

	public void setAuth(boolean auth) {
		this.auth = auth;
	}

	public Integer getIsOpen() {
		return isOpen;
	}

	public void setIsOpen(Integer isOpen) {
		this.isOpen = isOpen;
	}

}
