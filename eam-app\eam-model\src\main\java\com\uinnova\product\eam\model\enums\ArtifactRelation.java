package com.uinnova.product.eam.model.enums;

/**
 * 制品架构元素约束条件
 * <AUTHOR>
 * @date 2022/2/24
 */
public enum ArtifactRelation {
    /**
     * 约束条件为 =
     */
    EQUALS("1"),
    /**
     * 约束条件为 >=
     */
    GREAT("2"),
    /**
     * 约束条件为 <=
     */
    LESS("3");

    String val;

    ArtifactRelation(String val) {
        this.val = val;
    }

    public String val() {
        return this.val;
    }
}
