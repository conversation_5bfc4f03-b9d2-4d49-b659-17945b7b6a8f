package com.uinnova.product.eam.service.impl;

import com.binary.jdbc.Page;
import com.uinnova.product.eam.base.diagram.model.ESDiagram;
import com.uinnova.product.eam.base.diagram.model.ESDiagramLink;
import com.uinnova.product.eam.db.diagram.es.ESDiagramLinkDao;
import com.uinnova.product.eam.comm.model.es.EamMatrixInstance;
import com.uinnova.product.eam.comm.model.es.EamMatrixInstanceData;

import com.uinnova.product.eam.service.es.EamMatrixDataDesignDao;
import com.uinnova.product.eam.service.es.EamMatrixDataPrivateDao;
import com.uino.bean.cmdb.base.ESCIRltInfo;
import com.uino.bean.cmdb.base.LibType;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

public class CiRltTask implements Runnable{

    private  ConcurrentLinkedQueue<List<String>> allCiCodeQueue;
    private List<ESCIRltInfo> returnVals;
    private List<ESDiagram> diagramList;
    private ESDiagramLinkDao esDiagramLinkDao;
    private List<EamMatrixInstance> matrixInstances;
    private LibType libType;
    private EamMatrixDataPrivateDao matrixDataPrivateDao;
    private EamMatrixDataDesignDao matrixDataDesignDao;

    private CountDownLatch countDownLatch;

    public CiRltTask(ConcurrentLinkedQueue<List<String>> allCiCodeQueue, List<ESCIRltInfo> returnVals, List<ESDiagram> diagramList, ESDiagramLinkDao esDiagramLinkDao, CountDownLatch countDownLatch) {
        this.allCiCodeQueue = allCiCodeQueue;
        this.returnVals = returnVals;
        this.diagramList = diagramList;
        this.esDiagramLinkDao = esDiagramLinkDao;
        this.countDownLatch = countDownLatch;
    }

    public CiRltTask(ConcurrentLinkedQueue<List<String>> allCiCodeQueue, List<ESCIRltInfo> returnVals, List<EamMatrixInstance> matrixInstances, LibType libType, EamMatrixDataPrivateDao matrixDataPrivateDao, EamMatrixDataDesignDao matrixDataDesignDao, CountDownLatch countDownLatch) {
        this.allCiCodeQueue = allCiCodeQueue;
        this.returnVals = returnVals;
        this.matrixInstances = matrixInstances;
        this.libType = libType;
        this.matrixDataPrivateDao = matrixDataPrivateDao;
        this.matrixDataDesignDao = matrixDataDesignDao;
        this.countDownLatch = countDownLatch;
    }

    @Override
    public void run() {
        try {
            if (!CollectionUtils.isEmpty(diagramList)) {
                doDiagramTask();
            }
            if (!CollectionUtils.isEmpty(matrixInstances)) {
                doMatrixTask();
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            countDownLatch.countDown();
        }
    }

    private void doDiagramTask() {
        Set<Long> diagramIdSet = diagramList.stream().map(ESDiagram::getId).collect(Collectors.toSet());
        BoolQueryBuilder boolQueryLinkBuilder = QueryBuilders.boolQuery();
        boolQueryLinkBuilder.must(QueryBuilders.termsQuery("diagramId",diagramIdSet));
        long count = esDiagramLinkDao.countByCondition(boolQueryLinkBuilder);
        List<ESDiagramLink> esDiagramLinks = new ArrayList<>();
        if (count > 0) {
            Map<String, Page<ESDiagramLink>> linkMap = esDiagramLinkDao.getScrollByQuery(1, 3000, boolQueryLinkBuilder, "id", true);
            String scrollId = linkMap.keySet().iterator().next();
            if (linkMap.get(scrollId).getData() != null && linkMap.get(scrollId).getData().size() > 0) {
                List<ESDiagramLink> firstData = linkMap.get(scrollId).getData();
                esDiagramLinks.addAll(firstData);
            }
            if (linkMap.get(scrollId).getTotalRows() > 3000) {
                while (true) {
                    List<ESDiagramLink> rltNextResult = esDiagramLinkDao.getListByScroll(scrollId);
                    if (rltNextResult != null && rltNextResult.size() > 0) {
                        esDiagramLinks.addAll(rltNextResult);
                    } else {
                        break;
                    }
                }
            }
            List<String> rltUniqueCodes = esDiagramLinks.stream().map(ESDiagramLink::getUniqueCode)
                    .filter(item -> StringUtils.isNotEmpty(item))
                    .distinct().collect(Collectors.toList());
            List<String> findRltCode = returnVals.stream().filter(item -> rltUniqueCodes.contains(item.getUniqueCode())).map(item -> item.getUniqueCode()).distinct().collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(findRltCode)) {
                allCiCodeQueue.add(findRltCode);
            }
        }
    }

    private void doMatrixTask() {
        Set<Long> tableIdSet = matrixInstances.stream().map(EamMatrixInstance::getId).collect(Collectors.toSet());
        BoolQueryBuilder matrixDataQuery = QueryBuilders.boolQuery();
        matrixDataQuery.must(QueryBuilders.termsQuery("tableId", tableIdSet));
        long count = LibType.PRIVATE.equals(libType) ?
                matrixDataPrivateDao.countByCondition(matrixDataQuery) :
                matrixDataDesignDao.countByCondition(matrixDataQuery);
        List<EamMatrixInstanceData> matrixInstanceDatas = new ArrayList<>();
        if (count > 0) {
            Map<String, Page<EamMatrixInstanceData>> linkMap = LibType.PRIVATE.equals(libType) ?
                    matrixDataPrivateDao.getScrollByQuery(1, 3000, matrixDataQuery, "id", true) :
                    matrixDataDesignDao.getScrollByQuery(1, 3000, matrixDataQuery, "id", true);
            String scrollId = linkMap.keySet().iterator().next();
            if (linkMap.get(scrollId).getData() != null && linkMap.get(scrollId).getData().size() > 0) {
                List<EamMatrixInstanceData> firstData = linkMap.get(scrollId).getData();
                matrixInstanceDatas.addAll(firstData);
            }
            if (linkMap.get(scrollId).getTotalRows() > 3000) {
                while (true) {
                    List<EamMatrixInstanceData> rltNextResult = LibType.PRIVATE.equals(libType) ?
                            matrixDataPrivateDao.getListByScroll(scrollId) :
                            matrixDataDesignDao.getListByScroll(scrollId);
                    if (rltNextResult != null && rltNextResult.size() > 0) {
                        matrixInstanceDatas.addAll(rltNextResult);
                    } else {
                        break;
                    }
                }
            }
            List<String> rltCodes = matrixInstanceDatas.stream().map(EamMatrixInstanceData::getRltCode)
                    .filter(Objects::nonNull)
                    .distinct().collect(Collectors.toList());
            List<String> findRltCode = returnVals.stream()
                    .filter(item -> rltCodes.contains(item.getCiCode()))
                    .map(ESCIRltInfo::getUniqueCode)
                    .distinct().collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(findRltCode)) {
                allCiCodeQueue.add(findRltCode);
            }
        }
    }
}
