package com.uino.dao.cmdb;

import jakarta.annotation.PostConstruct;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.stereotype.Service;

import com.uino.dao.AbstractESBaseDao;
import com.uino.dao.ESConst;
import com.uino.bean.cmdb.base.ESVisualModel;
import com.uino.bean.cmdb.query.ESVisualModelSearchBean;

import java.util.List;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
public class ESVisualModelSvc extends AbstractESBaseDao<ESVisualModel, ESVisualModelSearchBean> {

	Log logger = LogFactory.getLog(ESVisualModelSvc.class);
	
	@Override
	public String getIndex() {
		return ESConst.INDEX_CMDB_VISUALMODEL;
	}

	@Override
	public String getType() {
		return "visualmodel";
	}
	
	@PostConstruct
    public void init() {
        super.initIndex();
    }

	public List<ESVisualModel> getEnableModel(Long domainId){
		BoolQueryBuilder query = QueryBuilders.boolQuery();
		query.must(QueryBuilders.termQuery("enable", true));
		query.must(QueryBuilders.termQuery("domainId", domainId));
		return getListByQuery(query);
	}

	public List<ESVisualModel> getEnableModel(Long domainId,List<Long> orgIds){
		BoolQueryBuilder query = QueryBuilders.boolQuery();
		query.must(QueryBuilders.termQuery("enable", true));
		query.must(QueryBuilders.termsQuery("orgId", orgIds));
		query.must(QueryBuilders.termQuery("domainId", domainId));
		return getListByQuery(query);
	}

}
