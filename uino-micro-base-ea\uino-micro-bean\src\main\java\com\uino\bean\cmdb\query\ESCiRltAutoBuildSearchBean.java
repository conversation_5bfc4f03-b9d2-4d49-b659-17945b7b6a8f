package com.uino.bean.cmdb.query;

import com.uino.bean.permission.query.SearchBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @data 2021/5/13
 */
@Getter
@Setter
public class ESCiRltAutoBuildSearchBean extends SearchBase {

    /**
     * 源分类id
     */
    @ApiModelProperty(value = "源分类id")
    private Long sourceCiClassId;

    /**
     * 目标分类id
     */
    @ApiModelProperty(value = "目标分类id")
    private Long targetCiClassId;

    /**
     * 目标分类id集合
     */
    @ApiModelProperty(value = "目标分类id集合")
    private List<Long> targetCiClassIds;

    /**
     * 搜索名称
     *
     */
    @ApiModelProperty(value = "搜索名称")
    private String name;

    /**
     * 关系id集合
     */
    @ApiModelProperty(value = "关系id集合")
    private List<Long> rltClassIds;

    /**
     * 属性id集合
     */
    @ApiModelProperty(value = "属性id集合")
    private List<Long> attrDefIds;

    /**
     * 任务状态（0：未启用；1：启用）
     */
    @ApiModelProperty(value = "（0：未启用；1：启用）")
    private Integer status;

    /**
     * 规则状态
     */
    @ApiModelProperty(value = "规则状态（0：全部；1：正常；2：失效）")
    private Integer ruleStatus;

    @ApiModelProperty(value = "所属域")
    private Long domainId;
}
