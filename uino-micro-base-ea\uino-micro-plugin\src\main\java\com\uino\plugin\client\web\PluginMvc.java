package com.uino.plugin.client.web;

import com.uino.plugin.bean.OperatePluginDetails;
import com.uino.plugin.client.service.PluginSvc;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @Description 插件管理
 * @Date 2021/9/26
 * @Version 1.0
 */
@RestController
@RequestMapping(value = "/currentServer/plugin")
public class PluginMvc {


    @Autowired
    private PluginSvc pluginSvc;

    @RequestMapping(value = "uploadPlugin")
    public OperatePluginDetails uploadPlugin(@RequestParam(name = "file") MultipartFile file) {
        return pluginSvc.uploadPlugin(file);
    }


    /**
     * 加载插件
     *
     * @return
     */
    @RequestMapping(value = "loadPlugin")
    public OperatePluginDetails loadPlugin(@RequestParam(name = "jarName") String jarName) {
        return pluginSvc.loadPlugin(jarName);
    }

    /**
     * 卸载插件
     *
     * @return
     */
    @RequestMapping(value = "unloadAndDeletePlugin")
    public OperatePluginDetails unloadAndDeletePlugin(@RequestParam(name = "jarName") String jarName) {
        return pluginSvc.unloadAndDeletePlugin(jarName);
    }

}
