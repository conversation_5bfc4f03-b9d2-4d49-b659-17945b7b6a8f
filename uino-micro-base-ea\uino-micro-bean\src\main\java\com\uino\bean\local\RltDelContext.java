package com.uino.bean.local;

public class RltDelContext {

    private static final ThreadLocal<RltDelContextValue> context = new ThreadLocal<>();

    public static void setContextValue(RltDelContextValue contextValue) {
        context.set(contextValue);
    }

    public static RltDelContextValue getContextValue() {
        return context.get();
    }

    public static void release() {
        context.remove();
    }
}
