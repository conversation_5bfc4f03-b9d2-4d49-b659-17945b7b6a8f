package com.uinnova.product.eam.comm.model.es;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;


@Data
@Comment("接口参数属性表[uino_eam_interface_parameters]")
public class InterfaceParameter {
    @Comment("主键")
    private Long id;
    private String code;
    @Comment("属性名称")
    private String name;
    @Comment("属性是否必填")
    private String required;
    @Comment("属性类型")
    private String type;
    @Comment("属性长度")
    private Integer length;
    @Comment("属性描述")
    private String explain;
    @Comment("属性父类id")
    private String parentId;
    @Comment("是否来源标准数据")
    private Boolean isFromStandard;
    @Comment("创建者，做数据隔离使用")
    private String createUser;
    @Comment("是否可编辑")
    private Boolean disable;
    @Comment("是否可删除")
    private Boolean isDel;
    @Comment("是否可添加")
    private Boolean isAdd;
    @Comment("是入参属性还是出参属性 0表示入参 1表示出参")
    private Integer parameterType;
    @Comment("创建时间")
    private Long createTime;
    @Comment("修改时间")
    private Long modifyTime;
}
