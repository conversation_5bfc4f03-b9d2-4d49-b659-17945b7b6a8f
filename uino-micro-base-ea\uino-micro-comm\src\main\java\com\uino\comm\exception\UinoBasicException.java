package com.uino.comm.exception;

public class UinoBasicException extends RuntimeException {

    public static final long serialVersionUID = 1;

    public UinoBasicException() {
        super();
    }

    public UinoBasicException(String message) {
        super(message);
    }

    public UinoBasicException(String message, Throwable cause) {
        super(message, cause);
    }

    public UinoBasicException(Throwable cause) {
        super(cause);
    }
}
