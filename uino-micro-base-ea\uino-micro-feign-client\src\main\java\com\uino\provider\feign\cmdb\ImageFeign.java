package com.uino.provider.feign.cmdb;

import java.util.List;
import java.util.Set;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClassDir;
import com.uino.bean.cmdb.base.CcImage;
import com.uino.bean.cmdb.business.ImageCount;
import com.uino.bean.cmdb.business.ImportDirMessage;
import com.uino.bean.cmdb.business.ImportResultMessage;
import com.uino.bean.cmdb.query.ESSearchImageBean;
import com.uino.bean.permission.query.SearchKeywordBean;
import com.uino.provider.feign.config.BaseFeignConfig;

/**
 * 
 * <AUTHOR>
 *
 */
@Component
@FeignClient(name = "${feign-server-name:tarsier-platform}", path = "${feign-server-path:tarsier-platform}/feign/image", configuration = {
        BaseFeignConfig.class})
public interface ImageFeign {

    /**
     * 不分页查询图标目录
     * 
     * @return
     */
    @PostMapping("/queryImageDirList")
	public List<ImageCount> queryImageDirList(@RequestParam(name = "domainId")Long domainId, @RequestBody(required = false) CCcCiClassDir cdt);

    /**
     * 分页查询Image数据
     * 
     * @param bean
     * @return
     */
    @PostMapping("/queryImagePage")
    public Page<CcImage> queryImagePage(@RequestBody ESSearchImageBean bean);

    /**
     * 上传Image，必须是ZIP
     * 
     * @param file
     * @return
     */
    @PostMapping(value = "/importZipImage", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
	public ImportResultMessage importZipImage(@RequestParam(name = "domainId")Long domainId,@RequestParam(name = "sourceType", required = false) Integer sourceType,
			@RequestPart(name = "file") MultipartFile file);

    /**
     * 导入图标
     * 
     * @param dirId
     *            目录ID
     * @param file
     * @return
     */
    @PostMapping(value = "/importImage", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public boolean importImage(@RequestParam(name = "dirId", required = false) Long dirId,
            @RequestPart(name = "file", required = false) MultipartFile file);

    /**
     * 替换图标
     * 
     * @param imgId
     *            图标ID
     * @param file
     * @return
     */
    @PostMapping("/replaceImage")
    public boolean replaceImage(@RequestParam(name = "imgId", required = false) Long imgId,
            @RequestPart(name = "file", required = false) MultipartFile file);

    /**
     * 删除图标
     * 
     * @param image
     * @return
     */
    @PostMapping("/deleteImage")
    public boolean deleteImage(@RequestBody CcImage image);

    /**
     * 删除目录下的图标
     * 
     * @param dirId
     * @return
     */
    @PostMapping("/deleteDirImage")
    public boolean deleteDirImage(@RequestParam(value = "dirId", required = false) Long dirId);

    /**
     * 导出指定文件夹下图标
     * 
     * @param dirIds
     * @return
     */
    @PostMapping("/exportImageZipByDirIds")
    public ResponseEntity<byte[]> exportImageZipByDirIds(@RequestBody Set<Long> dirIds);

    /**
     * 导入3D图标-全量覆盖
     * 
     * @param file
     * @param zipEntriesMap
     * @param isCover
     * @return
     */
    @PostMapping("/import3DZipImage")
    public ImportDirMessage import3DZipImage(@RequestPart(value = "file") MultipartFile file, @RequestParam(name = "dirId") Long dirId,
			@RequestParam(name = "isCover", required = false) boolean isCover);

    @PostMapping("/queryTopImage")
    public List<CcImage> queryTopImage(@RequestBody(required = false) SearchKeywordBean bean);

    @PostMapping("/updateImageRlt")
    public Long updateImageRlt(@RequestBody(required = false) CcImage image);

    @PostMapping("/replace3DImage")
    public boolean replace3DImage(@RequestParam(value = "imgId", required = false) Long imgId,
            @RequestPart(value = "file") MultipartFile file);

    @PostMapping("/delete3DImage")
    public boolean delete3DImage(@RequestBody(required = false) CcImage image);

    @PostMapping("/queryImageById")
    public CcImage queryImageById(@RequestParam(value = "id", required = false) Long id);

    @PostMapping("/importImages")
    public ImportDirMessage importImages(@RequestParam(value = "imgId", required = false) Long dirId,
            @RequestPart(value = "file") MultipartFile[] files);

    @PostMapping("/countBySearchBean")
    public Long countBySearchBean(@RequestBody(required = false) ESSearchImageBean bean);

	/**
	 * 下载图标资源
	 * 
	 * @param imageId
	 */
	@PostMapping("downloadImageResource")
	public ResponseEntity<byte[]> downloadImageResource(@RequestBody List<Long> ids);

    /**
     * 根据资源路径查询资源id
     *
     * @param bean
     */
    @PostMapping("queryImageByPath")
    public Page<CcImage> queryImageByPath(ESSearchImageBean bean);

    /**
     * whether to display the document attribute
     *
     * @return boolean
     * */
    @PostMapping("isShowDocumentAttribute")
    Boolean isShowDocumentAttribute();
}
