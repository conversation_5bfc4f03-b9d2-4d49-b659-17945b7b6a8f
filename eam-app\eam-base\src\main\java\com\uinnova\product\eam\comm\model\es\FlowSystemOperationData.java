package com.uinnova.product.eam.comm.model.es;

import lombok.*;

import java.util.Date;

/**
 * description
 *
 * <AUTHOR>
 * @since 2024/12/30 10:46
 */
@Data
@RequiredArgsConstructor
@ToString
public class FlowSystemOperationData {

    private Long id;

    private String sourceId;

    private String processId;

    private String processName;

    private String processTypeId;

    private String processTypeName;

    private String processOperationState;

    private Long processStateTime;

    private Long processEndTime;

}
