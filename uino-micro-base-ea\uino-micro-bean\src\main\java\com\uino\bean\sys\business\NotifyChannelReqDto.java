package com.uino.bean.sys.business;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "渠道通知查询参数体", description = "渠道通知查询参数体")
public class NotifyChannelReqDto {

    @ApiModelProperty(value = "ID", example = "27393335556218", required = true)
    private Long id;

    @ApiModelProperty(value = "渠道名称", required = true)
    private String name;

    @ApiModelProperty(value="所属域id",example = "123")
    private Long domainId;
}
