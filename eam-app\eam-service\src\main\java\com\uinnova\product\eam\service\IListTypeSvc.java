package com.uinnova.product.eam.service;

import com.uinnova.product.eam.comm.model.CListType;
import com.uinnova.product.eam.comm.model.ListType;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface IListTypeSvc {

    /**
     * 保存清单类型接口
     * @param listTypes 清单类型参数
     * @return 保存成功的清单类型id
     */
    Integer saveOrUpdateListType(List<CListType> listTypes);

    /**
     * 删除清单类型接口
     * @param id 清单类型id
     * @return 删除响应
     */
    Integer deleteListingType(Long id);

    /**
     * 查询清单类型
     * @return list集合
     */
    List<ListType> queryListTypes();

}
