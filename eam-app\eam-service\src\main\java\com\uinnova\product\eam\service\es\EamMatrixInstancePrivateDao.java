package com.uinnova.product.eam.service.es;

import com.uinnova.product.eam.comm.model.es.EamMatrixInstance;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;

/**
 * 矩阵表格
 * <AUTHOR>
 */
@Service
public class EamMatrixInstancePrivateDao extends AbstractESBaseDao<EamMatrixInstance, EamMatrixInstance> {
    @Override
    public String getIndex() {
        return "uino_eam_matrix_instance_private";
    }

    @Override
    public String getType() {
        return "_doc";
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }
}
