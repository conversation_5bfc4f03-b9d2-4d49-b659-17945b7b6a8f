package com.uinnova.product.eam.base.diagram.exception;

public class DiagramNotFoundException extends RuntimeException {

    public static final long serialVersionUID = 1;

    public DiagramNotFoundException() {
        super();
    }

    public DiagramNotFoundException(String message) {
        super(message);
    }

    public DiagramNotFoundException(String message, Throwable cause) {
        super(message, cause);
    }

    public DiagramNotFoundException(Throwable cause) {
        super(cause);
    }
}
