package com.uino.tarsier.tarsiercom.feign;

import java.util.Map;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import com.uino.tarsier.tarsiercom.filter.AccessKeyFilter;
import com.uino.tarsier.tarsiercom.util.AccessKey;

import feign.RequestInterceptor;
import feign.RequestTemplate;

@Configuration
public class TarsierFeignInterceptor implements RequestInterceptor {
    
    @Autowired(required = false)
    private ITokenGetter tokenGetter;

    @Autowired(required = false)
    private IRequestHeaderGetter rqHeaderGetter;
    
    @Override
    public void apply(RequestTemplate template) {
        String accessToken = AccessKey.getTimeKey();
        template.header(AccessKeyFilter.ACCESS_KEY, accessToken);
        
        if(rqHeaderGetter != null) {
            Map<String, String> headers = rqHeaderGetter.getRequestHeaders();
            if(headers != null && headers.size() > 0) {
                Set<String> keySet = headers.keySet();
                for (String key : keySet) {
                    template.header(key, headers.get(key));
                }
            }
        }
        
        if (tokenGetter != null) {
            String token = tokenGetter.getToken();
            if (token != null && !"".equals(token.trim())) {
                template.header(ITokenGetter.TOKEN_KEY, token);
            }
        }
        
        
    }

}
