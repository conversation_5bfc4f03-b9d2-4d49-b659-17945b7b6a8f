package com.uinnova.product.eam.service.merge.impl;

import com.binary.core.util.BinaryUtils;
import com.uinnova.product.eam.base.util.EamUtil;
import com.uinnova.product.eam.comm.model.es.EamMatrixInstanceData;
import com.uinnova.product.eam.comm.model.es.EamMatrixInstance;
import com.uinnova.product.eam.comm.model.es.EamMatrixStencil;
import com.uinnova.product.eam.model.bm.EamMergeParams;
import com.uinnova.product.eam.model.constants.StatusConstant;
import com.uinnova.product.eam.service.manage.EamMatrixDataSvc;
import com.uinnova.product.eam.service.manage.EamMatrixInstanceSvc;
import com.uinnova.product.eam.service.manage.EamMatrixStencilSvc;
import com.uino.bean.cmdb.base.LibType;
import com.uino.dao.util.ESUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 矩阵表格发布/检出业务实现
 * <AUTHOR>
 */
@Slf4j
@Service
public class EamMatrixMerge {

    @Autowired
    private EamMatrixInstanceSvc instanceSvc;

    @Autowired
    private EamMatrixDataSvc matrixDataSvc;

    @Autowired
    private EamMatrixStencilSvc stencilSvc;

    public void push(EamMergeParams params, String description) {
        EamMatrixInstance instance = params.getMatrixInstance();
        EamMatrixStencil stencil = stencilSvc.getById(instance.getMatrixId());
        BinaryUtils.checkNull(stencil, "矩阵制品");
        EamMatrixInstance designInstance = this.pushMatrix(instance, stencil, params.getTargetDirId(), description);

        List<EamMatrixInstanceData> privateTableData = params.getTableData();
        List<EamMatrixInstanceData> designTableData = EamUtil.copy(privateTableData, EamMatrixInstanceData.class);
        matrixDataSvc.setTableData(designTableData, stencil.getId(), null, LibType.DESIGN);
        for (EamMatrixInstanceData each : designTableData) {
            each.setId(null);
            each.setTableId(designInstance.getId());
        }
        matrixDataSvc.saveOrUpdateMatrixTable(designTableData, LibType.DESIGN);
    }

    /**
     * 矩阵基本信息发布
     * @param privateInstance 待发布矩阵
     * @param stencil 矩阵模板
     * @param dirId 发布位置
     * @param description 说明
     * @return 发布矩阵
     */
    private EamMatrixInstance pushMatrix(EamMatrixInstance privateInstance, EamMatrixStencil stencil, Long dirId, String description) {
        //先查询是否有已发布版本
        EamMatrixInstance instance = null;
        if(!BinaryUtils.isEmpty(privateInstance.getPublishId())){
            instance = instanceSvc.getBaseInfoById(privateInstance.getPublishId(), LibType.DESIGN);
        }
        if(instance == null){
            long publishId = ESUtil.getUUID();
            privateInstance.setPublishId(publishId);
            privateInstance.setVersion(1);
            instance = EamUtil.copy(privateInstance, EamMatrixInstance.class);
            instance.setId(publishId);
        }else{
            //创建一个历史版本
            this.createMatrixHistory(instance);
            //删除之前发布的数据
            matrixDataSvc.deleteByTableId(instance.getId(), LibType.DESIGN);
            int version = instance.getVersion() + 1;
            privateInstance.setPublishId(instance.getId());
            privateInstance.setVersion(version);
            instance.setVersion(version);
        }
        privateInstance.setPublished(StatusConstant.PUBLISHED);
        instanceSvc.saveOrUpdate(privateInstance, LibType.PRIVATE);
        instance.setCreator(privateInstance.getOwnerCode());
        instance.setCreateTime(ESUtil.getNumberDateTime());
        instance.setMatrixVersion(stencil.getVersion());
        instance.setDirId(dirId);
        instance.setDescription(description);
        instance.setPublished(StatusConstant.PUBLISHED);
        instanceSvc.saveOrUpdate(instance, LibType.DESIGN);
        return instance;
    }

    /**
     * 创建设计库矩阵表格数据历史版本
     * @param instance 矩阵表格数据
     */
    private void createMatrixHistory(EamMatrixInstance instance) {
        EamMatrixInstance history = EamUtil.copy(instance, EamMatrixInstance.class);
        history.setId(ESUtil.getUUID());
        history.setStatus(StatusConstant.DISABLE);
        instanceSvc.saveOrUpdate(history, LibType.DESIGN);
        List<EamMatrixInstanceData> tableData = matrixDataSvc.getTableData(instance.getId(), LibType.DESIGN);
        tableData.forEach(e -> e.setTableId(history.getId()));
        matrixDataSvc.saveOrUpdateMatrixTable(tableData, LibType.DESIGN);
    }

    public void pull(EamMergeParams params) {
        EamMatrixInstance instance = params.getMatrixInstance();
        EamMatrixInstance privateInstance = this.pullMatrix(instance, params);

        List<EamMatrixInstanceData> designTableData = params.getTableData();
        List<EamMatrixInstanceData> privateTableData = EamUtil.copy(designTableData, EamMatrixInstanceData.class);
        for (EamMatrixInstanceData each : privateTableData) {
            each.setId(null);
            each.setSourceVersion(null);
            each.setTargetVersion(null);
            each.setData(null);
            each.setTableId(privateInstance.getId());
        }
        matrixDataSvc.saveOrUpdateMatrixTable(privateTableData, LibType.PRIVATE);
    }

    private EamMatrixInstance pullMatrix(EamMatrixInstance designInstance, EamMergeParams params) {
        //先查询是否有已发布版本
        EamMatrixInstance instance;
        List<EamMatrixInstance> instances = instanceSvc.queryByPublishId(designInstance.getId(), params.getOwnerCode(), LibType.PRIVATE);
        if(CollectionUtils.isEmpty(instances)){
            long publishId = ESUtil.getUUID();
            instance = EamUtil.copy(designInstance, EamMatrixInstance.class);
            instance.setId(publishId);
        }else{
            instance = instances.get(0);
            //删除原来的数据
            matrixDataSvc.deleteByTableId(instance.getId(), LibType.PRIVATE);
            instance.setPublishId(designInstance.getId());
            instance.setVersion(designInstance.getVersion());
        }
        instance.setPublished(StatusConstant.PUBLISHED);
        instance.setOwnerCode(params.getOwnerCode());
        instance.setCreator(params.getOwnerCode());
        instance.setCreateTime(ESUtil.getNumberDateTime());
        instance.setStatus(StatusConstant.ENABLE);
        instance.setMatrixVersion(null);
        instance.setDirId(params.getTargetDirId());
        instance.setDescription(null);
        instanceSvc.saveOrUpdate(instance, LibType.PRIVATE);
        return instance;
    }
}
