package com.uinnova.product.eam.model.vo;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.comm.model.es.EamCategory;
import com.uino.bean.cmdb.base.LibType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 目录&文件夹
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class EamCategoryVO extends EamCategory {
    @Comment("库")
    private LibType libType = LibType.PRIVATE;
    @Comment("用户名称")
    private String userName;
    @Comment("是否关注")
    private int isAttention;
}
