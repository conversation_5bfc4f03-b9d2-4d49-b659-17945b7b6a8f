package com.uinnova.product.vmdb.comm.model.sys;

import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;

/**
 * 
 * <AUTHOR>
 *
 */
@Comment("系统操作日志[SYS_OPERATE_LOG]")
public class SysOperateLog implements EntityBean {
    private static final long serialVersionUID = 1L;

    @Comment("ID[ID]")
    private Long id;

    @Comment("日志时间[LOG_TIME]")
    private Long logTime;

    @Comment("用户ID[USER_ID]")
    private Long userId;

    @Comment("用户代码[USER_CODE]")
    private String userCode;

    @Comment("用户姓名[USER_NAME]")
    private String userName;

    @Comment("操作模块[OP_NAME]")
    private String opName;

    @Comment("操作路径[OP_PATH]")
    private String opPath;

    @Comment("操作描述[OP_DESC]")
    private String opDesc;

    @Comment("结果状态[OP_STATUS]    1=成功 0=失败")
    private Integer opStatus;

    @Comment("输入参数[OP_PARAM]")
    private String opParam;

    @Comment("输出结果[OP_RESULT]")
    private String opResult;

    @Comment("类名[MVC_FULL_NAME]")
    private String mvcFullName;

    @Comment("方法名[MVC_MOD_NAME]")
    private String mvcModName;

    @Comment("所属域[DOMAIN_ID]")
    private Long domainId;

    @Comment("所属用户域[USER_DOMAIN_ID]")
    private Long userDomainId;

    @Comment("创建时间[CREATE_TIME]    创建时间:yyyyMMddHHmmss")
    private Long createTime;

    @Comment("修改时间[MODIFY_TIME]    修改时间:yyyyMMddHHmmss")
    private Long modifyTime;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getLogTime() {
        return this.logTime;
    }

    public void setLogTime(Long logTime) {
        this.logTime = logTime;
    }

    public Long getUserId() {
        return this.userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUserCode() {
        return this.userCode;
    }

    public void setUserCode(String userCode) {
        this.userCode = userCode;
    }

    public String getUserName() {
        return this.userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getOpName() {
        return this.opName;
    }

    public void setOpName(String opName) {
        this.opName = opName;
    }

    public String getOpPath() {
        return this.opPath;
    }

    public void setOpPath(String opPath) {
        this.opPath = opPath;
    }

    public String getOpDesc() {
        return this.opDesc;
    }

    public void setOpDesc(String opDesc) {
        this.opDesc = opDesc;
    }

    public Integer getOpStatus() {
        return this.opStatus;
    }

    public void setOpStatus(Integer opStatus) {
        this.opStatus = opStatus;
    }

    public String getOpParam() {
        return this.opParam;
    }

    public void setOpParam(String opParam) {
        this.opParam = opParam;
    }

    public String getOpResult() {
        return this.opResult;
    }

    public void setOpResult(String opResult) {
        this.opResult = opResult;
    }

    public String getMvcFullName() {
        return this.mvcFullName;
    }

    public void setMvcFullName(String mvcFullName) {
        this.mvcFullName = mvcFullName;
    }

    public String getMvcModName() {
        return this.mvcModName;
    }

    public void setMvcModName(String mvcModName) {
        this.mvcModName = mvcModName;
    }

    public Long getDomainId() {
        return this.domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }

    public Long getUserDomainId() {
        return this.userDomainId;
    }

    public void setUserDomainId(Long userDomainId) {
        this.userDomainId = userDomainId;
    }

    public Long getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getModifyTime() {
        return this.modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }

}
