package com.uino.provider.feign.monitor;

import com.binary.jdbc.Page;
import com.uino.bean.event.EventCiKpiCount;
import com.uino.bean.event.modeling.EventModel;
import com.uino.bean.event.param.EventCiKpiCountParam;
import com.uino.bean.event.param.EventCiSeverityQueryParam;
import com.uino.bean.monitor.base.ESAlarm;
import com.uino.bean.monitor.base.ESMonEapEvent;
import com.uino.bean.monitor.base.ESMonSysSeverityInfo;
import com.uino.bean.monitor.buiness.CurrentEventQueryDto;
import com.uino.bean.monitor.buiness.EventQueryDto;
import com.uino.provider.feign.config.BaseFeignConfig;
import org.elasticsearch.index.query.QueryBuilder;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Set;

@Component
@FeignClient(name = "${feign-server-name:tarsier-platform}", path = "${feign-server-path:tarsier-platform}/feign/uinoEvent/", configuration = {BaseFeignConfig.class })
public interface UinoEventFeign {

	@PostMapping("getEventModel")
	EventModel getEventModel();

	/**
	 * 分页查询告警信息
	 * @param queryDto
	 * @return
	 */
	@PostMapping("searchEventPage")
	Page<ESMonEapEvent> searchEventPage(@RequestBody EventQueryDto queryDto);

	@PostMapping("searchEventPage/mom")
	Page<ESMonEapEvent> searchEventPage(@RequestBody(required = false) EventQueryDto queryDto
			, @RequestParam(required = false, value = "mustQuery") QueryBuilder mustQuery
			, @RequestParam(required = false, value = "shouldQuery") QueryBuilder shouldQuery);

	/**
	 * 保存告警-模拟告警
	 * @param saveBean
	 */
	@PostMapping("saveAlarm")
	void saveAlarm(@RequestBody ESAlarm saveBean);

	/**
	 * 保存当前未关闭告警信息
	 * @param fmEvent
	 */
	@PostMapping("saveCurrentEvent")
	void saveCurrentEvent(@RequestBody ESMonEapEvent fmEvent);

	/**
	 * 查询当前未关闭告警信息
	 * @param queryDto
	 * @return
	 */
	@PostMapping("listCurrentEventByParam")
	List<ESMonEapEvent> listCurrentEventByParam(@RequestBody CurrentEventQueryDto queryDto);

	@PostMapping("listCurrentEventByParams")
	List<ESMonEapEvent> listCurrentEventByParam(List<CurrentEventQueryDto> queryDtos);

	/**
	 * 删除当前告警信息
	 * @param fmEvent
	 */
	@PostMapping("delCurrentEvent")
	void delCurrentEvent(@RequestBody ESMonEapEvent fmEvent);

	/**
	 * 根据id查询列表
	 * @param ids
	 * @return
	 */
	@PostMapping("listCurrentEventByIds")
	List<ESMonEapEvent> listCurrentEventByIds(@RequestBody Set<String> ids);

	/**
	 * 获取ciId集合下的告警信息
	 * @param ciIds
	 * @return
	 */
	@PostMapping("listCurrentEventByCiIds")
	List<ESMonEapEvent> listCurrentEventByCiIds(@RequestBody List<Long> ciIds);

	/**
	 * 根据id查询当前告警信息
	 * @param id
	 * @return
	 */
	@PostMapping("currentEventById")
	ESMonEapEvent currentEventById(@RequestParam(value = "id")String id);

	/**
	 * 清除当前告警信息
	 */
	@PostMapping("clearCurrentEvent")
	void clearCurrentEvent(@RequestBody Long domainId);

	/**
	 * 保存历史告警信息
	 * @param esMonEapEvent
	 * @return
	 */
	@PostMapping("saveEventHis")
	ESMonEapEvent saveEventHis(@RequestBody ESMonEapEvent esMonEapEvent);

	/**
	 * 根据id查询历史告警信息
	 * @param id
	 * @return
	 */
	@PostMapping("hisEventById")
	ESMonEapEvent hisEventById(@RequestParam(value = "id") String id);

	/**
	 * 获取ciCode集合下的告警信息
	 * @param ciCodes
	 * @return
	 */
	@PostMapping("listCurrentEventByCiCodes")
	List<ESMonEapEvent> listCurrentEventByCiCodes(@RequestParam(value = "domainId") Long domainId, @RequestBody List<String> ciCodes);

	/**
	 * 查询告警级别列表
	 * @return
	 */
	@PostMapping("getAllEventSeverity")
	List<ESMonSysSeverityInfo> getAllEventSeverity(@RequestBody Long domainId);

	/**
	 * 告警级别
	 * @param param
	 * @return ciCode及对应告警的最严重级别、数量、颜色的信息
	 */
	@PostMapping("getEventCountByCiCodeAndKpiCodes")
	List<EventCiKpiCount> getEventCountByCiCodeAndKpiCodes(@RequestParam(value = "domainId") Long domainId, @RequestBody EventCiKpiCountParam param);

	/**
	 * 批量保存告警信息
	 * @param events
	 */
	@PostMapping("saveEventBatch")
	boolean saveEventBatch(@RequestBody List<ESMonEapEvent> events);

	/**
	 * 根据ciCode及告警等级查询当前告警信息（备注：告警状态参数status只针对ES中event索引查询，redis查询不生效）
	 * @param queryParam 封装查询参数（ciCode集合、severity集合、status集合）
	 * @return
	 */
	@PostMapping("queryCurrentEventsByCiCodesAndServeritys")
	List<ESMonEapEvent> queryCurrentEventsByCiCodesAndServeritys(@RequestParam(value = "domainId") Long domainId, @RequestBody EventCiSeverityQueryParam queryParam);

}
