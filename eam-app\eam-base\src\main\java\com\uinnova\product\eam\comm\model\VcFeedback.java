package com.uinnova.product.eam.comm.model;




import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;


@Comment("系统反馈表[VC_FEEDBACK]")
public class VcFeedback implements EntityBean {
	private static final long serialVersionUID = 1L;


	@Comment("ID[ID]")
	private Long id;


	@Comment("反馈用户ID[USER_ID]")
	private Long userId;


	@Comment("反馈用户代码[USER_CODE]")
	private String userCode;


	@Comment("反馈用户姓名[USER_NAME]")
	private String userName;


	@Comment("反馈时间[FEEDBACK_TIME]")
	private Long feedbackTime;


	@Comment("反馈类型[FEEDBACK_TYPE]")
	private Integer feedbackType;


	@Comment("反馈内容[CONTENT]    逗号分隔")
	private String content;


	@Comment("所属域[DOMAIN_ID]")
	private Long domainId;


	@Comment("数据状态[DATA_STATUS]    数据状态:数据状态：1-正常 0-删除")
	private Integer dataStatus;


	@Comment("创建时间[CREATE_TIME]    创建时间:yyyyMMddHHmmss")
	private Long createTime;


	@Comment("修改时间[MODIFY_TIME]    修改时间:yyyyMMddHHmmss")
	private Long modifyTime;




	public Long getId() {
		return this.id;
	}
	public void setId(Long id) {
		this.id = id;
	}


	public Long getUserId() {
		return this.userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}


	public String getUserCode() {
		return this.userCode;
	}
	public void setUserCode(String userCode) {
		this.userCode = userCode;
	}


	public String getUserName() {
		return this.userName;
	}
	public void setUserName(String userName) {
		this.userName = userName;
	}


	public Long getFeedbackTime() {
		return this.feedbackTime;
	}
	public void setFeedbackTime(Long feedbackTime) {
		this.feedbackTime = feedbackTime;
	}


	public Integer getFeedbackType() {
		return this.feedbackType;
	}
	public void setFeedbackType(Integer feedbackType) {
		this.feedbackType = feedbackType;
	}


	public String getContent() {
		return this.content;
	}
	public void setContent(String content) {
		this.content = content;
	}


	public Long getDomainId() {
		return this.domainId;
	}
	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}


	public Integer getDataStatus() {
		return this.dataStatus;
	}
	public void setDataStatus(Integer dataStatus) {
		this.dataStatus = dataStatus;
	}


	public Long getCreateTime() {
		return this.createTime;
	}
	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}


	public Long getModifyTime() {
		return this.modifyTime;
	}
	public void setModifyTime(Long modifyTime) {
		this.modifyTime = modifyTime;
	}


}


