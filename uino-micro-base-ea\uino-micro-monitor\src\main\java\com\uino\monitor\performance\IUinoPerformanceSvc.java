package com.uino.monitor.performance;

import java.util.List;

import org.elasticsearch.index.query.QueryBuilder;

import com.binary.jdbc.Page;
import com.uino.bean.monitor.buiness.PerformanceQueryDto;
import com.uino.bean.tp.base.FinalPerformanceDTO;

public interface IUinoPerformanceSvc {

    /**
     * Save performance
     * <br>
     * Execute the warehousing operation directly, no longer verify the attribute information of the indicator
     *
     * @param perf performance {@link FinalPerformanceDTO}
     * @return save result {@link Boolean}
     */
    Boolean saveOrUpdatePerformance(FinalPerformanceDTO perf);

    /**
     * Save performances in batches
     * <br>
     * Execute the warehousing operation directly, no longer verify the attribute information of the indicator
     *
     * @param performances performances {@link List<FinalPerformanceDTO>}
     * @return save result {@link Boolean}
     */
    Boolean saveOrUpdateBatch(List<FinalPerformanceDTO> performances);

    /**
     * Paging query performance data
     *
     * @param queryDto performance query dto  {@link PerformanceQueryDto}
     * @return {@link Page<FinalPerformanceDTO>}
     */
    Page<FinalPerformanceDTO> queryPerformancePage(PerformanceQueryDto queryDto);

    /**
     * Conditional query performance data
     *
     * @param pageNum   page number
     * @param pageSize  page size
     * @param query     query conditional
     * @param order     sort field
     * @param timeStart start time {@link Long}
     * @param timeEnd   end time {@link Long}
     * @param asc       ascending
     * @return {@link Page<FinalPerformanceDTO>}
     */
    Page<FinalPerformanceDTO> getSortListByQuery(Long domainId,int pageNum, int pageSize, QueryBuilder query, String order, Long timeStart, Long timeEnd, boolean asc);

    /**
     * Get the latest performance data
     *
     * @param pageNum  page number
     * @param pageSize page size
     * @param query    query conditional
     * @param order    sort field
     * @param asc      ascending
     * @return {@link Page<FinalPerformanceDTO>}
     */
    Page<FinalPerformanceDTO> getLastPerfListByQuery(Long domainId ,int pageNum, int pageSize,QueryBuilder query, String order, boolean asc);

    /**
     * Get current performance data
     *
     * @param ciCodes  ciCodes
     * @param kpiCodes kpiCodes
     * @return {@link List<FinalPerformanceDTO>}
     */
    List<FinalPerformanceDTO> getCurrentPerformance(Long domainId,List<String> ciCodes, List<String> kpiCodes);

    /**
     * Get current performance data
     * This method can only query real-time performance data from {@code ES}
     *
     * @param ciCodes  ciCodes
     * @return {@link List<FinalPerformanceDTO>}
     */
    List<FinalPerformanceDTO> getCurrentPerformanceByCiCodes(Long domainId,List<String> ciCodes);

    /**
     * Get current performance data
     *
     * @param ciCode    ciCode
     * @param kpiCode   kpiCode
     * @param startTime start time
     * @param endTime   end time
     * @return {@link List<FinalPerformanceDTO>}
     */
    List<FinalPerformanceDTO> getPerformanceByConditional(Long domainId,String ciCode, String kpiCode, Long startTime, Long endTime);
}
