package com.uinnova.product.vmdb.comm.model.quality;

import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;

/**
 * 
 * <AUTHOR>
 *
 */
@Comment("CI数据质量图表[CC_CI_QUALITY_CHART]")
public class CcCiQualityChart implements EntityBean {
    private static final long serialVersionUID = 1L;

    @Comment("ID[ID]")
    private Long id;

    @Comment("仪表盘ID[DASHBOARD_ID]")
    private Long dashboardId;

    @Comment("图表类型[CHART_TYPE]    图表类型:1=时间序列 2=当前值")
    private Integer chartType;

    @Comment("统计规则[RULE_ID]")
    private Long ruleId;

    @Comment("所在行位置[DASHBOARD_ROW_NUM]")
    private Integer dashboardRowNum;

    @Comment("所在列位置[DASHBOARD_COL_NUM]")
    private Integer dashboardColNum;

    @Comment("图表宽度[DASHBOARD_WIDTH]")
    private Integer dashboardWidth;

    @Comment("图表高度[DASHBOARD_HEIGHT]")
    private Integer dashboardHeight;

    @Comment("显示标题[TITLE_DISP]    显示标题:1=显示 0=不显示")
    private Integer titleDisp;

    @Comment("图标标题[TITLE]")
    private String title;

    @Comment("标题样式[TITLE_STYLE]")
    private String titleStyle;

    @Comment("图表样式[CHART_STYLE]    图表背景")
    private String chartStyle;

    @Comment("坐标方向[CHART_DIRECT]    坐标方向:1=下->上 2=左->右 3=上->下 4=右->左")
    private Integer chartDirect;

    @Comment("显示图例[LEGEND_DISP]    显示图例:1=显示 0=不显示")
    private Integer legendDisp;

    @Comment("图例样式[LEGEND_STYLE]    图例颜色")
    private String legendStyle;

    @Comment("图例位置[LEGEND_POSITION]    图例位置:1=上 2=左 3=下 4=右")
    private Integer legendPosition;

    @Comment("显示X轴标题[X_AXIS_DISP]    显示X轴标题:1=显示 0=不显示")
    private Integer xAxisDisp;

    @Comment("X轴标题[X_AXIS_TITLE]")
    private String xAxisTitle;

    @Comment("X轴标题样式[X_AXIS_TITLE_STYLE]")
    private String xAxisTitleStyle;

    @Comment("X轴显示网络线[X_AXIS_LINE_DISP]    X轴显示网络线:1=显示 0=不显示")
    private Integer xAxisLineDisp;

    @Comment("X轴网络线样式[X_AXIS_LINE_STYLE]")
    private String xAxisLineStyle;

    @Comment("X轴显示标签[X_AXIS_LABEL_DISP]    X轴显示标签:1=显示 0=不显示")
    private Integer xAxisLabelDisp;

    @Comment("X轴标签样式[X_AXIS_LABEL_STYLE]")
    private String xAxisLabelStyle;

    @Comment("显示Y轴标题[Y_AXIS_DISP]    显示Y轴标题:1=显示 0=不显示")
    private Integer yAxisDisp;

    @Comment("Y轴标题[Y_AXIS_TITLE]")
    private String yAxisTitle;

    @Comment("Y轴标题样式[Y_AXIS_TITLE_STYLE]")
    private String yAxisTitleStyle;

    @Comment("Y轴显示网络线[Y_AXIS_LINE_DISP]    Y轴显示网络线:1=显示 0=不显示")
    private Integer yAxisLineDisp;

    @Comment("Y轴网络线样式[Y_AXIS_LINE_STYLE]")
    private String yAxisLineStyle;

    @Comment("Y轴显示标签[Y_AXIS_LABEL_DISP]    Y轴显示标签:1=显示 0=不显示")
    private Integer yAxisLabelDisp;

    @Comment("Y轴标签样式[Y_AXIS_LABEL_STYLE]")
    private String yAxisLabelStyle;

    @Comment("阈值上限值[THRESHOLD_UPPER_VALUE]")
    private Long thresholdUpperValue;

    @Comment("阈值下限值[THRESHOLD_FLOOR_VALUE]")
    private Long thresholdFloorValue;

    @Comment("阈值上限样式[THRESHOLD_UPPER_STYLE]")
    private String thresholdUpperStyle;

    @Comment("阈值下限样式[THRESHOLD_FLOOR_STYLE]")
    private String thresholdFloorStyle;

    @Comment("阈值中间样式[THRESHOLD_MIDDLE_STYLE]")
    private String thresholdMiddleStyle;

    @Comment("备用1[CUSTOM_1]")
    private String custom1;

    @Comment("备用2[CUSTOM_2]")
    private String custom2;

    @Comment("备用3[CUSTOM_3]")
    private String custom3;

    @Comment("备用4[CUSTOM_4]")
    private String custom4;

    @Comment("备用5[CUSTOM_5]")
    private String custom5;

    @Comment("备用6[CUSTOM_6]")
    private String custom6;

    @Comment("所属域[DOMAIN_ID]")
    private Long domainId;

    @Comment("数据状态[DATA_STATUS]    数据状态:0=删除，1=正常")
    private Integer dataStatus;

    @Comment("创建时间[CREATE_TIME]    创建时间:yyyyMMddHHmmss")
    private Long createTime;

    @Comment("更新时间[MODIFY_TIME]    更新时间:yyyyMMddHHmmss")
    private Long modifyTime;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getDashboardId() {
        return this.dashboardId;
    }

    public void setDashboardId(Long dashboardId) {
        this.dashboardId = dashboardId;
    }

    public Integer getChartType() {
        return this.chartType;
    }

    public void setChartType(Integer chartType) {
        this.chartType = chartType;
    }

    public Long getRuleId() {
        return this.ruleId;
    }

    public void setRuleId(Long ruleId) {
        this.ruleId = ruleId;
    }

    public Integer getDashboardRowNum() {
        return this.dashboardRowNum;
    }

    public void setDashboardRowNum(Integer dashboardRowNum) {
        this.dashboardRowNum = dashboardRowNum;
    }

    public Integer getDashboardColNum() {
        return this.dashboardColNum;
    }

    public void setDashboardColNum(Integer dashboardColNum) {
        this.dashboardColNum = dashboardColNum;
    }

    public Integer getDashboardWidth() {
        return this.dashboardWidth;
    }

    public void setDashboardWidth(Integer dashboardWidth) {
        this.dashboardWidth = dashboardWidth;
    }

    public Integer getDashboardHeight() {
        return this.dashboardHeight;
    }

    public void setDashboardHeight(Integer dashboardHeight) {
        this.dashboardHeight = dashboardHeight;
    }

    public Integer getTitleDisp() {
        return this.titleDisp;
    }

    public void setTitleDisp(Integer titleDisp) {
        this.titleDisp = titleDisp;
    }

    public String getTitle() {
        return this.title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getTitleStyle() {
        return this.titleStyle;
    }

    public void setTitleStyle(String titleStyle) {
        this.titleStyle = titleStyle;
    }

    public String getChartStyle() {
        return this.chartStyle;
    }

    public void setChartStyle(String chartStyle) {
        this.chartStyle = chartStyle;
    }

    public Integer getChartDirect() {
        return this.chartDirect;
    }

    public void setChartDirect(Integer chartDirect) {
        this.chartDirect = chartDirect;
    }

    public Integer getLegendDisp() {
        return this.legendDisp;
    }

    public void setLegendDisp(Integer legendDisp) {
        this.legendDisp = legendDisp;
    }

    public String getLegendStyle() {
        return this.legendStyle;
    }

    public void setLegendStyle(String legendStyle) {
        this.legendStyle = legendStyle;
    }

    public Integer getLegendPosition() {
        return this.legendPosition;
    }

    public void setLegendPosition(Integer legendPosition) {
        this.legendPosition = legendPosition;
    }

    public Integer getXAxisDisp() {
        return this.xAxisDisp;
    }

    public void setXAxisDisp(Integer xAxisDisp) {
        this.xAxisDisp = xAxisDisp;
    }

    public String getXAxisTitle() {
        return this.xAxisTitle;
    }

    public void setXAxisTitle(String xAxisTitle) {
        this.xAxisTitle = xAxisTitle;
    }

    public String getXAxisTitleStyle() {
        return this.xAxisTitleStyle;
    }

    public void setXAxisTitleStyle(String xAxisTitleStyle) {
        this.xAxisTitleStyle = xAxisTitleStyle;
    }

    public Integer getXAxisLineDisp() {
        return this.xAxisLineDisp;
    }

    public void setXAxisLineDisp(Integer xAxisLineDisp) {
        this.xAxisLineDisp = xAxisLineDisp;
    }

    public String getXAxisLineStyle() {
        return this.xAxisLineStyle;
    }

    public void setXAxisLineStyle(String xAxisLineStyle) {
        this.xAxisLineStyle = xAxisLineStyle;
    }

    public Integer getXAxisLabelDisp() {
        return this.xAxisLabelDisp;
    }

    public void setXAxisLabelDisp(Integer xAxisLabelDisp) {
        this.xAxisLabelDisp = xAxisLabelDisp;
    }

    public String getXAxisLabelStyle() {
        return this.xAxisLabelStyle;
    }

    public void setXAxisLabelStyle(String xAxisLabelStyle) {
        this.xAxisLabelStyle = xAxisLabelStyle;
    }

    public Integer getYAxisDisp() {
        return this.yAxisDisp;
    }

    public void setYAxisDisp(Integer yAxisDisp) {
        this.yAxisDisp = yAxisDisp;
    }

    public String getYAxisTitle() {
        return this.yAxisTitle;
    }

    public void setYAxisTitle(String yAxisTitle) {
        this.yAxisTitle = yAxisTitle;
    }

    public String getYAxisTitleStyle() {
        return this.yAxisTitleStyle;
    }

    public void setYAxisTitleStyle(String yAxisTitleStyle) {
        this.yAxisTitleStyle = yAxisTitleStyle;
    }

    public Integer getYAxisLineDisp() {
        return this.yAxisLineDisp;
    }

    public void setYAxisLineDisp(Integer yAxisLineDisp) {
        this.yAxisLineDisp = yAxisLineDisp;
    }

    public String getYAxisLineStyle() {
        return this.yAxisLineStyle;
    }

    public void setYAxisLineStyle(String yAxisLineStyle) {
        this.yAxisLineStyle = yAxisLineStyle;
    }

    public Integer getYAxisLabelDisp() {
        return this.yAxisLabelDisp;
    }

    public void setYAxisLabelDisp(Integer yAxisLabelDisp) {
        this.yAxisLabelDisp = yAxisLabelDisp;
    }

    public String getYAxisLabelStyle() {
        return this.yAxisLabelStyle;
    }

    public void setYAxisLabelStyle(String yAxisLabelStyle) {
        this.yAxisLabelStyle = yAxisLabelStyle;
    }

    public Long getThresholdUpperValue() {
        return this.thresholdUpperValue;
    }

    public void setThresholdUpperValue(Long thresholdUpperValue) {
        this.thresholdUpperValue = thresholdUpperValue;
    }

    public Long getThresholdFloorValue() {
        return this.thresholdFloorValue;
    }

    public void setThresholdFloorValue(Long thresholdFloorValue) {
        this.thresholdFloorValue = thresholdFloorValue;
    }

    public String getThresholdUpperStyle() {
        return this.thresholdUpperStyle;
    }

    public void setThresholdUpperStyle(String thresholdUpperStyle) {
        this.thresholdUpperStyle = thresholdUpperStyle;
    }

    public String getThresholdFloorStyle() {
        return this.thresholdFloorStyle;
    }

    public void setThresholdFloorStyle(String thresholdFloorStyle) {
        this.thresholdFloorStyle = thresholdFloorStyle;
    }

    public String getThresholdMiddleStyle() {
        return this.thresholdMiddleStyle;
    }

    public void setThresholdMiddleStyle(String thresholdMiddleStyle) {
        this.thresholdMiddleStyle = thresholdMiddleStyle;
    }

    public String getCustom1() {
        return this.custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }

    public String getCustom2() {
        return this.custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }

    public String getCustom3() {
        return this.custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }

    public String getCustom4() {
        return this.custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }

    public String getCustom5() {
        return this.custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }

    public String getCustom6() {
        return this.custom6;
    }

    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }

    public Long getDomainId() {
        return this.domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }

    public Integer getDataStatus() {
        return this.dataStatus;
    }

    public void setDataStatus(Integer dataStatus) {
        this.dataStatus = dataStatus;
    }

    public Long getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getModifyTime() {
        return this.modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }

}
