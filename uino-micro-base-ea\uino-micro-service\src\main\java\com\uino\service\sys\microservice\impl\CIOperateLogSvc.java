package com.uino.service.sys.microservice.impl;

import java.util.Calendar;
import java.util.List;
import java.util.*;
import java.util.stream.Collectors;

import com.uino.bean.permission.base.SysRole;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.permission.base.SysUserRoleRlt;
import com.uino.dao.BaseConst;
import com.uino.dao.permission.ESRoleSvc;
import com.uino.dao.permission.ESUserSvc;
import com.uino.dao.permission.rlt.ESPerssionCommSvc;
import com.uino.service.permission.microservice.IRoleSvc;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.MultiMatchQueryBuilder.Type;
import org.elasticsearch.index.query.Operator;
import org.elasticsearch.index.query.QueryBuilders;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.binary.core.util.BinaryUtils;
import com.binary.jdbc.Page;
import com.uino.bean.sys.base.ESCIOperateLog;
import com.uino.bean.sys.query.ESCIOperateLogSearchBean;
import com.uino.dao.sys.ESCIOperateLogSvc;
import com.uino.dao.util.ESUtil;
import com.uino.service.sys.microservice.ICIOperateLogSvc;
import com.uino.util.sys.SysUtil;
import org.springframework.util.CollectionUtils;

/**
 *
 * <AUTHOR>
 *
 */
@Service
public class CIOperateLogSvc implements ICIOperateLogSvc {

    @Autowired
    ESCIOperateLogSvc logSvc;

    @Autowired
    private IRoleSvc roleSvc;

    @Override
    public Page<ESCIOperateLog> getCIOperateLogPageByCdt(ESCIOperateLogSearchBean bean) {
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        Long domainId = bean.getDomainId() == null ? BaseConst.DEFAULT_DOMAIN_ID : bean.getDomainId();
        query.must(QueryBuilders.termQuery("domainId", domainId));
        roleSvc.sanyuanCondition(query,"ciOperate");
        if (bean.getStartTime() != null) {
            query.must(QueryBuilders.rangeQuery("createTime").from(bean.getStartTime()));
        }
        if (bean.getEndTime() != null) {
            query.must(QueryBuilders.rangeQuery("createTime").to(bean.getEndTime()));
        }
        if (!BinaryUtils.isEmpty(bean.getCiClassName())) {
            query.must(QueryBuilders.multiMatchQuery(bean.getCiClassName(), "ciClassName").operator(Operator.AND).type(Type.PHRASE_PREFIX).lenient(true));
        }
        if (!BinaryUtils.isEmpty(bean.getCiCode())) {
            query.must(QueryBuilders.multiMatchQuery(bean.getCiCode(), "ciCode").operator(Operator.AND).type(Type.PHRASE_PREFIX).lenient(true));
        }
        if (!BinaryUtils.isEmpty(bean.getCiPrimaryKey())) {
            query.must(QueryBuilders.multiMatchQuery(bean.getCiPrimaryKey(), "ciPrimaryKey").operator(Operator.AND).type(Type.PHRASE_PREFIX).lenient(true));
        }
        if (!BinaryUtils.isEmpty(bean.getOperator())) {
            query.must(QueryBuilders.multiMatchQuery(bean.getOperator(), "operator").operator(Operator.AND).type(Type.PHRASE_PREFIX).lenient(true));
        }
        if (!BinaryUtils.isEmpty(bean.getDynamic())) {
            query.must(QueryBuilders.termQuery("dynamic", bean.getDynamic()));
        }
        if (!BinaryUtils.isEmpty(bean.getAttrs())) {
            query.must(QueryBuilders.multiMatchQuery(bean.getAttrs(), "newAttrs.*", "oldAttrs.*").operator(Operator.AND).type(Type.PHRASE_PREFIX).lenient(true));
        }
        if (!BinaryUtils.isEmpty(bean.getKeyword())) {
			query.must(QueryBuilders
					.multiMatchQuery(bean.getKeyword(), "operator", "ciClassName", "ciCode", "source", "oldAttrs.*",
							"newAttrs.*", "ciPrimaryKey")
					.operator(Operator.AND)
                .type(Type.PHRASE_PREFIX).lenient(true));
        }
        return logSvc.getSortListByQuery(bean.getPageNum(), bean.getPageSize(), query, "createTime", false);
    }

    @Override
    public Long saveOrUpdate(ESCIOperateLog log) {
		logSvc.duplicateAttrFilter(log);
        if (log.getDynamic().equals(SysUtil.StaticUtil.LOG_DYNAMIC_UPDATE) && log.getOldAttrs().size() == 0 && log.getNewAttrs().size() == 0) {
            return 1L;
        }
        if (log.getSourceId() == null) {
            log.setSourceId(1L);
        }
        log.setIpAddress(SysUtil.getIpAddress(null));
        return logSvc.saveOrUpdate(log);
    }

    @Override
    public Integer saveOrUpdateBatch(List<ESCIOperateLog> logs) {
		return logSvc.saveOrUpdateBatch(logs);
    }

    @Override
    public Integer clearCIOperateLogByDuration(Integer durationDay) {
        if (durationDay == null) {
            durationDay = 7;
        }
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DATE, -durationDay);
        query.must(QueryBuilders.rangeQuery("createTime").lte(ESUtil.getNumberDateTime(cal.getTime())));
        return logSvc.deleteByQuery(query, true);
    }

    /**
     * 过滤重复字段
     *
     * @param ciLog
     */
    private void duplicateAttrFilter(ESCIOperateLog ciLog) {
        if (ciLog != null && ciLog.getDynamic().equals(SysUtil.StaticUtil.LOG_DYNAMIC_UPDATE)) {
            Map<String, String> newAttrs = new HashMap<>();
            newAttrs.putAll(ciLog.getNewAttrs());
            Map<String, String> oldAttrs = ciLog.getOldAttrs();
            Iterator<Map.Entry<String, String>> it = oldAttrs.entrySet().iterator();
            while (it.hasNext()) {
                String key = it.next().getKey();
                boolean isDuplicate =
                    (oldAttrs.get(key) == null && newAttrs.get(key) == null) || (oldAttrs.get(key) != null && newAttrs.get(key) != null && oldAttrs.get(key).equals(newAttrs.get(key)));
                if (isDuplicate) {
                    it.remove();
                    newAttrs.remove(key);
                }
            }
        }
    }

}
