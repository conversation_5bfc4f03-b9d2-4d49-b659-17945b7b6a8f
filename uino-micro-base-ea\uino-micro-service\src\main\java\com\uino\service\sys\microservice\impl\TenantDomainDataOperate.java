package com.uino.service.sys.microservice.impl;

import com.alibaba.fastjson.JSONObject;
import com.binary.core.io.Compression;
import com.binary.core.util.BinaryUtils;
import com.uino.bean.cmdb.base.CcCiClassDir;
import com.uino.bean.cmdb.base.CcImage;
import com.uino.bean.cmdb.base.dataset.*;
import com.uino.bean.permission.base.*;
import com.uino.bean.sys.base.ESDictionaryAttrDef;
import com.uino.bean.sys.base.ESDictionaryClassInfo;
import com.uino.bean.sys.base.ESDictionaryItemInfo;
import com.uino.bean.sys.enums.DictionaryOptionEnum;
import com.uino.dao.BaseConst;
import com.uino.dao.cmdb.ESDirSvc;
import com.uino.dao.cmdb.ESImageSvc;
import com.uino.dao.cmdb.dataset.ESDataSetSvc;
import com.uino.dao.permission.*;
import com.uino.dao.permission.rlt.ESRoleModuleRltSvc;
import com.uino.dao.permission.rlt.ESUserOrgRltSvc;
import com.uino.dao.permission.rlt.ESUserRoleRltSvc;
import com.uino.dao.sys.ESDictionaryClassSvc;
import com.uino.dao.sys.ESDictionaryItemSvc;
import com.uino.dao.util.ESUtil;
import com.uino.service.sys.microservice.IResourceSvc;
import com.uino.util.sys.CommonFileUtil;
import lombok.extern.log4j.Log4j;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 租户数据处理
 *
 * <AUTHOR>
 * @Date 2021/10/28
 * @Version 1.0
 */
@Slf4j
@Component
public class TenantDomainDataOperate {

    @Value("${local.resource.space:}")
    private String localPath;
    @Value("${http.resource.space:}")
    private static String httpPath;
    @Value("${permission.http.prefix:}")
    private String commUrl;

    @Autowired
    private IResourceSvc iResourceSvc;

    @Autowired
    private ESDirSvc esDirSvc;
    @Autowired
    private ESImageSvc esImageSvc;
    @Autowired
    private ESDataModuleSvc esDataModuleSvc;
    @Autowired
    private ESDictionaryClassSvc esDictionaryClassSvc;
    @Autowired
    private ESDictionaryItemSvc esDictionaryItemSvc;
    @Autowired
    private ESModuleSvc esModuleSvc;
    @Autowired
    private ESOrgSvc esOrgSvc;
    @Autowired
    private ESRoleSvc esRoleSvc;
    @Autowired
    private ESUserSvc esUserSvc;
    @Autowired
    private ESRoleModuleRltSvc esRoleModuleRltSvc;
    @Autowired
    private ESUserOrgRltSvc esUserOrgRltSvc;
    @Autowired
    private ESUserRoleRltSvc esUserRoleRltSvc;
    @Autowired
    private ESDataSetSvc esDataSetSvc;

    /**
     * 初始化租户数据
     *
     * @param domainId
     */
    public void initDomainData(Long domainId) {
        initResource(domainId);
        Map<Long, Long> dirMap = initUinoCiDir(domainId);
        initUinoCmdbImage(domainId, dirMap);

        initUinoSysDataModule(domainId);

        Map<Long, Long> dictionaryClassMap = initUinoSysDictionaryClass(domainId);
        initUinoSysDictionaryItem(domainId, dictionaryClassMap);

        Map<Long, Long> moduleIdMap = initUinoSysModule(domainId);
        Map<Long, Long> orgIdMap = initUinoSysOrg(domainId);
        Map<Long, Long> roleIdMap = initUinoSysRole(domainId);
        Map<Long, Long> userIdMap = initUinoSysUser(domainId);
        initUinoSysUserOrgRlt(domainId, userIdMap, orgIdMap);
        initUinoSysUserRoleRlt(domainId, userIdMap, roleIdMap);
        initUinoSysRoleModuleRlt(domainId, roleIdMap, moduleIdMap);
        initDataset(domainId);
    }

    /**
     * 同步资源
     *
     * @param domainId
     */
    private void initResource(Long domainId) {
        // TODO 对象存储改造
        File sourceFile = new File(localPath + "/122");
        String relativePath = domainId + "/" + "122.zip";
        File targetFile = new File(localPath + "/" + relativePath);
        if (!targetFile.getParentFile().exists()) {
            targetFile.getParentFile().mkdirs();
        }
        Compression.compressZip(sourceFile, targetFile);
        Compression.uncompressZip(targetFile, targetFile.getParentFile());
        //同步资源
        iResourceSvc.saveSyncResourceInfo(relativePath, httpPath + relativePath, true, true, 1);
    }

    /**
     * 初始化目录
     *
     * @param domainId
     * @return 目录id映射，key：默认初始化id，value:新生成id
     */
    private Map<Long, Long> initUinoCiDir(Long domainId) {
        Map<Long, Long> result = new HashMap<>();
        List<CcCiClassDir> list = CommonFileUtil.getData("/initdata/uino_ci_dir.json", CcCiClassDir.class);
        list.forEach(ccCiClassDir -> {
            long uuid = ESUtil.getUUID();
            result.put(ccCiClassDir.getId(), uuid);
            ccCiClassDir.setDomainId(domainId);
            ccCiClassDir.setId(uuid);
            ccCiClassDir.setDirPath("#" + uuid + "#");
            ccCiClassDir.setCreateTime(System.currentTimeMillis());
            ccCiClassDir.setModifyTime(System.currentTimeMillis());
        });
        esDirSvc.saveOrUpdateBatch(list);
        return result;
    }

    /**
     * 初始化分类
     */
    private void initUinoCiClass(Long domainId) {

    }

    /**
     * 初始化资源数据
     *
     * @param domainId
     * @param dirMap   目录id映射，key：默认初始化id，value:新生成id
     */
    private void initUinoCmdbImage(Long domainId, Map<Long, Long> dirMap) {
        List<CcImage> list = CommonFileUtil.getData("/initdata/uino_cmdb_image.json", CcImage.class);
        list.forEach(ccImage -> {
            ccImage.setDomainId(domainId);
            ccImage.setDirId(dirMap.get(ccImage.getDirId()));
            long uuid = ESUtil.getUUID();
            ccImage.setId(uuid);
            ccImage.setImgPath("/" + domainId + ccImage.getImgPath());
            ccImage.setCreateTime(System.currentTimeMillis());
            ccImage.setModifyTime(System.currentTimeMillis());
        });
        esImageSvc.saveOrUpdateBatch(list);
    }

    /**
     * 初始化数据模块
     *
     * @param domainId
     */
    private void initUinoSysDataModule(Long domainId) {
        List<SysDataModule> data = CommonFileUtil.getData("/initdata/uino_sys_data_module.json", SysDataModule.class);
        data.forEach(sysDataModule -> {
            sysDataModule.setDataSourceUrl(commUrl + sysDataModule.getDataSourceUrl());
            long uuid = ESUtil.getUUID();
            sysDataModule.setId(uuid);
            sysDataModule.setDomainId(domainId);
            sysDataModule.setCreateTime(System.currentTimeMillis());
            sysDataModule.setModifyTime(System.currentTimeMillis());
        });
        esDataModuleSvc.saveOrUpdateBatch(data);
    }

    /**
     * 初始化字典定义
     *
     * @param domainId
     * @return 字典定义id映射，key：默认初始化id，value:新生成id
     */
    private Map<Long, Long> initUinoSysDictionaryClass(Long domainId) {
        Map<Long, Long> result = new HashMap<>();
        List<ESDictionaryClassInfo> dates = CommonFileUtil.getData("/initdata/uino_sys_dictionary_class.json",
                ESDictionaryClassInfo.class);
        for (ESDictionaryClassInfo dictClassInfo : dates) {
            long uuid = ESUtil.getUUID();
            result.put(dictClassInfo.getId(), uuid);
            dictClassInfo.setId(uuid);
            dictClassInfo.setDomainId(domainId);
            dictClassInfo.setIsInit(1);
            for (ESDictionaryAttrDef def : dictClassInfo.getDictAttrDefs()) {
                def.setDoaminId(domainId);
                if (BinaryUtils.isEmpty(def.getProStdName())) {
                    def.setProStdName(def.getProName());
                }
            }
        }
        //更新属性中引用字典分类Id
        for (ESDictionaryClassInfo dictClassInfo : dates) {
            for (ESDictionaryAttrDef def : dictClassInfo.getDictAttrDefs()) {
                if (def.getSourceDictClassId() != null) {
                    Long dictClasssId = result.get(def.getSourceDictClassId());
                    def.setSourceDictClassId(dictClasssId);
                }
            }
        }
        esDictionaryClassSvc.saveOrUpdateBatch(dates);
        return result;
    }

    /**
     * @param domainId
     * @param dictionaryClassMap 字典类id映射，key：默认初始化id，value:新生成id
     */
    private void initUinoSysDictionaryItem(Long domainId, Map<Long, Long> dictionaryClassMap) {
        List<ESDictionaryItemInfo> dates = CommonFileUtil.getData("/initdata/uino_sys_dictionary_item.json",
                ESDictionaryItemInfo.class);
        for (ESDictionaryItemInfo item : dates) {
            long uuid = ESUtil.getUUID();
            item.setId(uuid);
            item.setDomainId(domainId);
            item.setDictClassId(dictionaryClassMap.get(item.getDictClassId()));
            if (BinaryUtils.isEmpty(item.getOption())) {
                item.setOption(DictionaryOptionEnum.READ);
            }
        }
        esDictionaryItemSvc.saveOrUpdateBatch(dates);
    }

    /**
     * 初始化菜单
     *
     * @param domainId
     * @return 菜单id映射，key：默认初始化id，value:新生成id
     */
    private Map<Long, Long> initUinoSysModule(Long domainId) {
        List<SysModule> dates = CommonFileUtil.getData("/initdata/uino_sys_module.json", SysModule.class);
        //id映射，key：默认初始化id，value:新生成id
        Map<Long, Long> moduleIdMap = new HashMap<>();
        List<SysModule> sysModuleList = new ArrayList<>();
        for (SysModule sysModule : dates) {
            if (!"授权管理".equals(sysModule.getModuleName())) {
                long uuid = ESUtil.getUUID();
                moduleIdMap.put(sysModule.getId(), uuid);
                sysModule.setId(uuid);
                sysModuleList.add(sysModule);
            }

        }
        int orderNum = 0;
        for (SysModule module : sysModuleList) {
            module.setOrderNo(++orderNum);
            module.setParentId(moduleIdMap.get(module.getParentId()));
            module.setDomainId(domainId);
            module.setIsInit(true);
        }
        esModuleSvc.saveOrUpdateBatch(sysModuleList);
        return moduleIdMap;
    }

    /**
     * 初始化组织
     *
     * @param domainId
     * @return id映射，key：默认初始化id，value:新生成id
     */
    private Map<Long, Long> initUinoSysOrg(Long domainId) {
        List<SysOrg> dates = CommonFileUtil.getData("/initdata/uino_sys_org.json", SysOrg.class);
        Map<Long, Long> orgIdMap = new HashMap<>();
        dates.forEach(sysOrg -> {
            orgIdMap.put(sysOrg.getId(), domainId);
            sysOrg.setId(domainId);
            sysOrg.setDomainId(domainId);
        });

        esOrgSvc.saveOrUpdateBatch(dates);
        return orgIdMap;
    }

    /**
     * 初始化角色
     *
     * @param domainId
     * @return id映射，key：默认初始化id，value:新生成id
     */
    private Map<Long, Long> initUinoSysRole(Long domainId) {
        Map<Long, Long> roleIdMap = new HashMap<>();
        List<SysRole> data = CommonFileUtil.getData("/initdata/uino_sys_role.json", SysRole.class);
        for (SysRole sysRole : data) {
            long uuid = ESUtil.getUUID();
            roleIdMap.put(sysRole.getId(), uuid);
            sysRole.setDomainId(domainId);
            sysRole.setId(uuid);
        }
        esRoleSvc.saveOrUpdateBatch(data);
        return roleIdMap;
    }

    /**
     * 初始化用户
     *
     * @param domainId
     * @return id映射，key：默认初始化id，value:新生成id
     */
    private Map<Long, Long> initUinoSysUser(Long domainId) {
        Map<Long, Long> userIdMap = new HashMap<>();
        List<SysUser> data = CommonFileUtil.getData("/initdata/uino_sys_user.json", SysUser.class);
        List<SysUser> userList = new ArrayList<>();
        for (SysUser sysUser : data) {
            if (BaseConst._SUPER_ADMIN_LOGIN_CODE.equals(sysUser.getLoginCode())) {
                continue;
            }
            long uuid = ESUtil.getUUID();
            userIdMap.put(sysUser.getId(), uuid);
            sysUser.setId(uuid);
            sysUser.setDomainId(domainId);
            sysUser.setIcon("/" + domainId + sysUser.getIcon());
            userList.add(sysUser);
        }
        esUserSvc.saveOrUpdateBatch(userList);
        return userIdMap;
    }

    /**
     * 初始化角色功能关系
     *
     * @param domainId
     * @param roleIdMap
     * @param moduleIdMap
     */
    private void initUinoSysRoleModuleRlt(Long domainId, Map<Long, Long> roleIdMap, Map<Long, Long> moduleIdMap) {
        //管理员拥有菜单所有权限
        List<SysModule> modules = CommonFileUtil.getData("/initdata/uino_sys_module.json", SysModule.class);
        List<SysRoleModuleRlt> roleModuleMappings = new LinkedList<>();
        modules.stream().collect(Collectors.groupingBy(SysModule::getId)).keySet().forEach(moduleId -> {
            //有不需要初始化的菜单过滤掉，例如：授权管理
            if (moduleIdMap.get(moduleId) != null) {
                roleModuleMappings.add(SysRoleModuleRlt.builder().id(ESUtil.getUUID()).domainId(domainId).roleId(roleIdMap.get(1L)).moduleId(moduleIdMap.get(moduleId)).build());
            }

        });
        //在初始化关系文件中自定义的角色功能权限
        List<SysRoleModuleRlt> data = CommonFileUtil.getData("/initdata/uino_sys_role_module_rlt.json",
                SysRoleModuleRlt.class);
        data = data.stream()
                .filter(roleModule -> roleModule.getRoleId() != 0L && roleModule.getRoleId() != 1L)
                .collect(Collectors.toList());
        if (!BinaryUtils.isEmpty(data)) {
            for (SysRoleModuleRlt roleModuleRlt : data) {
                if (moduleIdMap.get(roleModuleRlt.getModuleId()) != null) {
                    roleModuleRlt.setId(ESUtil.getUUID());
                    roleModuleRlt.setDomainId(domainId);
                    roleModuleRlt.setModuleId(moduleIdMap.get(roleModuleRlt.getModuleId()));
                    roleModuleRlt.setRoleId(roleIdMap.get(roleModuleRlt.getRoleId()));
                } else {
                    log.error("初始化文件uino_sys_role_module_rlt.json中数据有误，内容为：" + JSONObject.toJSONString(roleModuleRlt));
                }

            }
            roleModuleMappings.addAll(data);
        }
        esRoleModuleRltSvc.saveOrUpdateBatch(roleModuleMappings);
    }

    /**
     * 初始化用户组织关系
     *
     * @param domainId
     * @param userIdMap
     * @param orgIdMap
     */
    private void initUinoSysUserOrgRlt(Long domainId, Map<Long, Long> userIdMap, Map<Long, Long> orgIdMap) {
        List<SysUserOrgRlt> data = CommonFileUtil.getData("/initdata/uino_sys_user_org_rlt.json", SysUserOrgRlt.class);
        data.forEach(sysUserOrgRlt -> {
            sysUserOrgRlt.setId(ESUtil.getUUID());
            sysUserOrgRlt.setDomainId(domainId);
            sysUserOrgRlt.setUserId(userIdMap.get(sysUserOrgRlt.getUserId()));
            sysUserOrgRlt.setOrgId(orgIdMap.get(sysUserOrgRlt.getOrgId()));
        });
        esUserOrgRltSvc.saveOrUpdateBatch(data);
    }

    /**
     * 初始化用户角色关系
     *
     * @param domainId
     * @param userIdMap
     * @param roleIdMap
     */
    private void initUinoSysUserRoleRlt(Long domainId, Map<Long, Long> userIdMap, Map<Long, Long> roleIdMap) {
        List<SysUserRoleRlt> data = CommonFileUtil.getData("/initdata/uino_sys_user_role_rlt.json", SysUserRoleRlt.class);
        data.forEach(sysUserRoleRlt -> {
            sysUserRoleRlt.setId(ESUtil.getUUID());
            sysUserRoleRlt.setDomainId(domainId);
            sysUserRoleRlt.setUserId(userIdMap.get(sysUserRoleRlt.getUserId()));
            sysUserRoleRlt.setRoleId(roleIdMap.get(sysUserRoleRlt.getRoleId()));
        });
        esUserRoleRltSvc.saveOrUpdateBatch(data);
    }

    /**
     * 初始化数据超市
     *
     * @param domainId
     */
    private void initDataset(Long domainId) {
        DataSetMallApiCiClass dataSetMallApiCiClass = new DataSetMallApiCiClass();
        dataSetMallApiCiClass.setDomainId(domainId);
        dataSetMallApiCiClass.setType(DataSetMallApiType.CiClass.getCode());
        dataSetMallApiCiClass.setName("CI分类");
        dataSetMallApiCiClass.setDescription("查询分类下ci");
        dataSetMallApiCiClass.setCreateTime(System.currentTimeMillis());
        dataSetMallApiCiClass.setModifyTime(System.currentTimeMillis());
        dataSetMallApiCiClass.setShareLevel(OperateType.Read.getCode());
        esDataSetSvc.saveOrUpdate(dataSetMallApiCiClass.toJson());

        DataSetMallApiRelClass dataSetMallApiRelClass = new DataSetMallApiRelClass();
        dataSetMallApiRelClass.setDomainId(domainId);
        dataSetMallApiRelClass.setType(DataSetMallApiType.RelClass.getCode());
        dataSetMallApiRelClass.setName("关系分类");
        dataSetMallApiRelClass.setDescription("查询关系分类下所有关系");
        dataSetMallApiRelClass.setCreateTime(System.currentTimeMillis());
        dataSetMallApiRelClass.setModifyTime(System.currentTimeMillis());
        dataSetMallApiRelClass.setShareLevel(OperateType.Read.getCode());
        esDataSetSvc.saveOrUpdate(dataSetMallApiRelClass.toJson());
        DataSetMallApiUpDownNFloor dataSetMallApiUpDownNFloor = new DataSetMallApiUpDownNFloor();
        dataSetMallApiUpDownNFloor.setDomainId(domainId);
        dataSetMallApiUpDownNFloor.setType(DataSetMallApiType.UpDownNFloor.getCode());
        dataSetMallApiUpDownNFloor.setName("上下N层");
        dataSetMallApiUpDownNFloor.setDescription("查询ci上下N层关系");
        dataSetMallApiUpDownNFloor.setCreateTime(System.currentTimeMillis());
        dataSetMallApiUpDownNFloor.setModifyTime(System.currentTimeMillis());
        dataSetMallApiUpDownNFloor.setShareLevel(OperateType.Read.getCode());
        esDataSetSvc.saveOrUpdate(dataSetMallApiUpDownNFloor.toJson());
    }


    /**
     * todo 删除域
     *
     * @param domainId
     */
    public void removeDomain(Long domainId) {

    }
}
