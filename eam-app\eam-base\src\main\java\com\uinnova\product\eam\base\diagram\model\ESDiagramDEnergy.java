package com.uinnova.product.eam.base.diagram.model;

import com.binary.framework.bean.annotation.Comment;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;


/**
 * @Classname
 * @Description 视图ID加密
 * <AUTHOR>
 * @Date 2021-08-27-10:28
 */
@Data
public class ESDiagramDEnergy {
    @Comment("视图ID加密字段")
//    @JsonIgnore
    @JsonProperty("diagramId")
    private String dEnergy;
}
