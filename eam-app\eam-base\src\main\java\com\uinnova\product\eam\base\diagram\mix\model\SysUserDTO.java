package com.uinnova.product.eam.base.diagram.mix.model;

import lombok.Data;

import java.io.Serializable;

/**
 * @Classname
 * @Description 协同用户信息类
 * <AUTHOR>
 * @Date 2021-07-01-16:02
 */
@Data
public class SysUserDTO implements Serializable {

    /** ID */
    private Long id;

    /** 登录代码 */
    private String loginCode;

    /** 显示名 */
    private String userName;

    @Override
    public String toString() {
        return "SysUserDTO{" +
                "id=" + id +
                ", loginCode='" + loginCode + '\'' +
                ", userName='" + userName + '\'' +
                '}';
    }
}
