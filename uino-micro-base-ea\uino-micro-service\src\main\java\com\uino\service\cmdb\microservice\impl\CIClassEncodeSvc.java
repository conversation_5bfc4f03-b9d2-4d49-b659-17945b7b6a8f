package com.uino.service.cmdb.microservice.impl;

import com.binary.core.util.BinaryUtils;
import com.binary.framework.exception.ServiceException;
import com.google.common.collect.Lists;
import com.uinnova.product.vmdb.comm.i18n.MessageUtil;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.comm.model.ci.CcCiClass;
import com.uino.bean.cmdb.base.*;
import com.uino.dao.cmdb.ESCIAttrTransConfigSvc;
import com.uino.dao.cmdb.ESCIClassSvc;
import com.uino.dao.cmdb.ESCISvc;
import com.uino.dao.cmdb.ESCiClassEncodeSvc;
import com.uino.service.cmdb.microservice.ICIClassEncodeSvc;
import com.uino.util.cache.ICacheService;
import com.uino.util.sys.LibTypeUtil;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 分类编码字段处理
 * <AUTHOR>
 */
@Service
@Slf4j
public class CIClassEncodeSvc implements ICIClassEncodeSvc {

    @Resource
    private ESCiClassEncodeSvc classEncodeSvc;

    @Resource
    private ICacheService cacheService;

    @Resource
    private ESCIClassSvc ciClassSvc;

    @Resource
    private ESCISvc esciSvc;

    @Autowired
    private ESCIAttrTransConfigSvc attrConfigSvc;

    public static ExecutorService EXECUTOR = new ThreadPoolExecutor(
            10,
            15,
            1L,
            TimeUnit.SECONDS,
            new LinkedBlockingDeque<>(1000),
            new ThreadPoolExecutor.CallerRunsPolicy());

    @Override
    public  synchronized Long getMaxNum(String classCode, String proName, Long attrDefId, Long domainId, LibType libType) {
        MessageUtil.checkEmpty(classCode, "classCode");
        MessageUtil.checkEmpty(proName, "proName");
        if(libType == null){
            libType = LibType.DESIGN;
        }
        if(domainId == null){
            domainId = 1L;
        }
        StringBuffer masterKey = new StringBuffer("BASE_ATTR_NUM_").append(domainId);
        if(LibType.BASELINE.equals(libType)){
            masterKey.append("_").append(libType);
        }
        masterKey.append("_").append(classCode).append("_").append(proName);
        Long cache = cacheService.atomicGet(masterKey.toString());
        Long num;
        if(BinaryUtils.isEmpty(cache) || cache.equals(0L)){
            //查询最大值
            num = this.getMaxNumByES(classCode, proName, attrDefId, domainId, libType);
            cacheService.getAndSet(masterKey.toString(), num);
        }else{
            num = cacheService.incrementAndGet(masterKey.toString());
        }
        LibType finalLibType = libType;
        Long finalDomainId = domainId;
        EXECUTOR.submit(() -> this.saveOrUpdate(num, classCode, proName, finalDomainId, finalLibType));
        return num;
    }

    @Override
    public Long getMaxNumByES(String classCode, String proName, Long attrDefId, Long domainId, LibType libType) {
        MessageUtil.checkEmpty(classCode, "classCode");
        MessageUtil.checkEmpty(proName, "proName");
        if(LibType.PRIVATE.equals(libType)){
            libType = LibType.DESIGN;
        }
        BoolQueryBuilder builder = QueryBuilders.boolQuery();
        builder.must(QueryBuilders.termQuery("classCode.keyword", classCode));
        builder.must(QueryBuilders.termQuery("proName.keyword", proName));
        builder.must(QueryBuilders.termQuery("domainId", domainId));
        builder.must(QueryBuilders.termQuery("libType.keyword", libType.toString()));
        ESCiClassEncode encode = classEncodeSvc.selectOne(builder);
        Long result;
        if(BinaryUtils.isEmpty(encode)){
            result  = this.getClassMaxNum(classCode, proName, attrDefId, domainId);
        }else{
            result = encode.getMaxNum() + 1;
        }
        return result;
    }

    @Override
    public Long saveOrUpdate(Long maxNum, String classCode, String proName, Long domainId, LibType libType) {
        BoolQueryBuilder builder = QueryBuilders.boolQuery();
        builder.must(QueryBuilders.termQuery("classCode.keyword", classCode));
        builder.must(QueryBuilders.termQuery("proName.keyword", proName));
        builder.must(QueryBuilders.termQuery("domainId", domainId));
        builder.must(QueryBuilders.termQuery("libType.keyword", libType.toString()));
        ESCiClassEncode encode = classEncodeSvc.selectOne(builder);
        if(BinaryUtils.isEmpty(encode)){
            encode = new ESCiClassEncode();
            encode.setClassCode(classCode);
            encode.setDomainId(domainId);
            encode.setLibType(libType.toString());
            encode.setProName(proName);
        }
        encode.setMaxNum(maxNum);
        return classEncodeSvc.saveOrUpdate(encode);
    }

    @Override
    public void clearCacheEncode(List<Long> classIds) {
        BoolQueryBuilder classQuery = QueryBuilders.boolQuery();
        classQuery.must(QueryBuilders.termsQuery("id", classIds));
        List<ESCIClassInfo> classList = ciClassSvc.getListByQuery(classQuery);
        if(CollectionUtils.isEmpty(classList)){
            return;
        }
        Set<String> classCodes = classList.stream().map(CcCiClass::getClassCode).collect(Collectors.toSet());
        BoolQueryBuilder builder = QueryBuilders.boolQuery();
        builder.must(QueryBuilders.termsQuery("classCode.keyword", classCodes));
        classEncodeSvc.deleteByQuery(builder, true);
        List<String> keys = getKeysByClass(classList);
        if(CollectionUtils.isEmpty(keys)){
            return;
        }
        cacheService.delKeys(keys.toArray(new String[]{}));
    }

    private List<String> getKeysByClass(List<ESCIClassInfo> classList) {
        List<String> result = new ArrayList<>();
        for (ESCIClassInfo each : classList) {
            if(CollectionUtils.isEmpty(each.getAttrDefs())){
                continue;
            }
            for (ESCIAttrDefInfo attrDef : each.getAttrDefs()) {
                //编码字段
                if(attrDef.getProType().equals(150)){
                    String key = "BASE_ATTR_NUM_" + each.getDomainId() + "_" + each.getClassCode() + "_" + attrDef.getProName();
                    result.add(key);
                }
            }
        }
        return result;
    }

    /**
     * 获取分类最大编码
     * @param classCode 分类标识
     * @param proName 字段名称
     * @param domainId 领域
     * @return 最大值
     */
    private synchronized Long getClassMaxNum(String classCode, String proName, Long attrDefId, Long domainId){
        BoolQueryBuilder classQuery = QueryBuilders.boolQuery();
        classQuery.must(QueryBuilders.termQuery("classCode.keyword", classCode));
        classQuery.must(QueryBuilders.termQuery("domainId", domainId));
        List<ESCIClassInfo> classList = ciClassSvc.getListByQuery(classQuery);
        if(CollectionUtils.isEmpty(classList)){
            throw new ServiceException("未查询到分类信息:"+classCode);
        }
        ESCIClassInfo classInfo = classList.get(0);
        CcCiAttrDef attrDef = null;
        for (CcCiAttrDef each : classInfo.getAttrDefs()) {
            if(proName!=null && proName.equals(each.getProName()) && each.getProType().equals(150)){
                attrDef = each;
            } else if ((each.getProType().equals(21) || each.getProType().equals(20)) && each.getId().equals(attrDefId)) {
                attrDef = each;
            }
        }
        if(attrDef == null){
            throw new ServiceException("未查询到编码字段信息:"+proName);
        }
        List<ESCIInfo> ciListPrivate = this.getCIByClass(classInfo.getId(), domainId, LibType.PRIVATE);
        List<ESCIInfo> ciListDesign = this.getCIByClass(classInfo.getId(), domainId, LibType.DESIGN);
        if(CollectionUtils.isEmpty(ciListPrivate) && CollectionUtils.isEmpty(ciListDesign)){
            return 1L;
        }
        //如果字段名被改动过,则需要用老字段名匹配ci数据
        BoolQueryBuilder defQuery = QueryBuilders.boolQuery();
        defQuery.must(QueryBuilders.termQuery("classId", classInfo.getId()));
        defQuery.must(QueryBuilders.termQuery("defId", attrDef.getId()));
        ESCIAttrTransConfig transConfig = attrConfigSvc.selectOne(defQuery);
        String queryName = proName;
        if(transConfig != null && !BinaryUtils.isEmpty(transConfig.getSourceAttrName())){
            queryName = transConfig.getShowName();
        }
        ciListPrivate.addAll(ciListDesign);
        return getMaxNumByCiList(ciListPrivate, queryName.toUpperCase());
    }

    /**
     * 获取ci集合中编码字段最大值
     * @param ciList ci集合
     * @param proName 字段
     * @return 最大值
     */
    private Long getMaxNumByCiList(List<ESCIInfo> ciList, String proName){
        long num = 0L;
        for (ESCIInfo each : ciList) {
            Map<String, Object> attrs = each.getAttrs();
            if(CollectionUtils.isEmpty(attrs) || attrs.get(proName) == null){
                continue;
            }
            Pattern pattern = Pattern.compile("\\[(.*?)]");
            Matcher matcher = pattern.matcher(attrs.get(proName).toString());
            if (matcher.find()) {
                long parse = Long.parseLong(matcher.group(1));
                if(num < parse){
                    num = parse;
                }
            }
        }
        return ++num;
    }

    private List<ESCIInfo> getCIByClass(Long classId, Long domainId, LibType libType){
        LibTypeUtil.setLibType(libType.toString());
        BoolQueryBuilder ciQuery = QueryBuilders.boolQuery();
        ciQuery.must(QueryBuilders.termQuery("classId", classId));
        ciQuery.must(QueryBuilders.termQuery("domainId", domainId));
        List<ESCIInfo> result = esciSvc.getListByQueryScroll(ciQuery);
        LibTypeUtil.removeLibType();
        return CollectionUtils.isEmpty(result)? Lists.newArrayList() : result;
    }
}
