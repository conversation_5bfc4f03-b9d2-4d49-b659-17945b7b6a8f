package com.uinnova.product.eam.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.binary.core.util.BinaryUtils;
import com.binary.framework.exception.ServiceException;
import com.binary.jdbc.Page;
import com.uinnova.product.eam.base.diagram.model.ESDiagram;
import com.uinnova.product.eam.base.util.EamUtil;
import com.uinnova.product.eam.comm.model.es.EamArtifact;
import com.uinnova.product.eam.comm.model.es.EamCategory;
import com.uinnova.product.eam.comm.model.es.EamHierarchy;
import com.uinnova.product.eam.comm.model.es.EamMultiModelHierarchy;
import com.uinnova.product.eam.config.Env;
import com.uinnova.product.eam.db.diagram.es.ESDiagramDao;
import com.uinnova.product.eam.model.EamArtifactRltVo;
import com.uinnova.product.eam.model.bm.ProductEle;
import com.uinnova.product.eam.model.bm.SimpCategoryInfo;
import com.uinnova.product.eam.model.bm.SimpDiagramInfo;
import com.uinnova.product.eam.model.dto.*;
import com.uinnova.product.eam.model.enums.CategoryTypeEnum;
import com.uinnova.product.eam.service.*;
import com.uinnova.product.eam.service.es.*;
import com.uinnova.product.vmdb.comm.model.ci.CCcCi;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.base.LibType;
import com.uino.util.sys.SysUtil;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.sort.SortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * 层级配置实现
 *
 * <AUTHOR>
 * @date 2022/1/6
 */
@Slf4j
@Service
public class BmHierarchySvcImpl implements IBmHierarchySvc {

    @Resource
    private BmHierarchyDao bmHierarchyDao;

    @Resource
    private EamCategorySvc categorySvc;

    @Resource
    private IBmMultiModelHierarchySvc modelHierarchySvc;

    @Resource
    private EamArtifactDao artifactDao;

    @Resource
    private EamCategoryPrivateDao categoryPrivateDao;

    @Resource
    private IBmNavigationHierarchySvc navigationHierarchySvc;

    @Resource
    private ESDiagramDao diagramDao;

    @Resource
    private BmMultiModelHierarchyDao multiModelHierarchyDao;

    @Resource
    private IamsESCIPrivateSvc privateCISvc;

    @Value("${http.resource.space}")
    private String httpResource;

    private static final String THROW_MESSAGE = "层级配置已被更新，请刷新后查看！";
    private static final String SHAPE_FLAG = "0";
    private static final String ICON = "icon";
    private static final String MODEL_ID = "modelId";
    private static final String DIR_LEL = "dirLvl";

    @Override
    public Long saveOrUpdate(EamHierarchyDto dto) {
        Long modelId = dto.getModelId();
        EamHierarchy eamHierarchyDto = new EamHierarchy();
        eamHierarchyDto.setModelId(modelId);
        List<EamHierarchyDto> hierarchyList = queryList(eamHierarchyDto);
        Map<Integer, EamHierarchyDto> hierarchyMap = new HashMap<>();
        if (!BinaryUtils.isEmpty(hierarchyList)) {
            hierarchyMap = hierarchyList.stream().collect(Collectors.toMap(EamHierarchyDto::getDirLvl, each -> each, (k1, k2) -> k1));
            //每次只能存一个层级，不允许重复添加同一层级
            EamHierarchyDto hierarchy = hierarchyMap.get(dto.getDirLvl());
            if (!BinaryUtils.isEmpty(hierarchy) && (dto.getId() == null || !hierarchy.getId().equals(dto.getId()))) {
                throw new ServiceException(THROW_MESSAGE);
            }
        }
        //
        Integer dirLvl = dto.getDirLvl();
        Long domainId = SysUtil.getCurrentUserInfo().getDomainId();
        if (!BinaryUtils.isEmpty(dirLvl) && dirLvl > 0) {
            EamHierarchyDto eamHierarchy = hierarchyMap.get(dto.getDirLvl() - 1);
            if (BinaryUtils.isEmpty(eamHierarchy)) {
                throw new ServiceException(THROW_MESSAGE);
            }
            String upShape = eamHierarchy.getShape();
            //将上一层及的shape判断是否为0，不是0 的话，就拿到里面的id,复制给classId
            if (SHAPE_FLAG.equals(upShape) || BinaryUtils.isEmpty(upShape)) {
                dto.setClassId(0L);
                dto.setInheritShape(upShape);
            } else {
                ProductEle ele = JSON.parseObject(upShape, ProductEle.class);
                dto.setClassId(ele.getId());
                if (!BinaryUtils.isEmpty(ele.getIcon())) {
                    ele.setIcon(ele.getIcon().replaceAll(httpResource, ""));
                    ele.setViewIcon(ele.getViewIcon().replaceAll(httpResource, ""));
                }
                dto.setInheritShape(JSON.toJSONString(ele));
            }
        }
        //TODO 更新绑定当前层级视图的制品id
        /*if(!BinaryUtils.isEmpty(dto.getArtifactId())){
            List<EamDiagramCatalog> privateCatalog = catalogSvc.getByModelIdAndLv(dto.getModelId(), dto.getDirLvl(), LibType.PRIVATE, null);
            List<EamDiagramCatalog> catalogs = catalogSvc.getByModelIdAndLv(dto.getModelId(), dto.getDirLvl(), LibType.DESIGN, null);
            catalogs.addAll(privateCatalog);
            Set<String> diagramIds = catalogs.stream().map(EamDiagramCatalog::getDiagramId).filter(Objects::nonNull).collect(Collectors.toSet());
            if(!BinaryUtils.isEmpty(diagramIds)){
                diagramSvc.updateArtifactId(diagramIds, dto.getArtifactId());
            }
        }*/
        EamHierarchy eamHierarchy = EamUtil.copy(dto, EamHierarchy.class);
        eamHierarchy.setRltList(JSON.toJSONString(dto.getRltList()));
        if (!BinaryUtils.isEmpty(eamHierarchy.getShape())) {
            if (SHAPE_FLAG.equals(eamHierarchy.getShape())) {
                eamHierarchy.setShape(eamHierarchy.getShape());
                eamHierarchy.setEffectClassId(0L);
            } else {
                ProductEle ele = JSON.parseObject(eamHierarchy.getShape(), ProductEle.class);
                if (!BinaryUtils.isEmpty(ele.getIcon())) {
                    ele.setIcon(ele.getIcon().replaceAll(httpResource, ""));
                }
                eamHierarchy.setShape(JSON.toJSONString(ele));
                eamHierarchy.setEffectClassId(ele.getId());
            }
        }
        eamHierarchy.setType(Env.BM_TYPE);
        eamHierarchy.setDomainId(domainId);
        Long aLong = bmHierarchyDao.saveOrUpdate(eamHierarchy);
        //更新下一层级的classId和inheritShape
        int nextDirLvl = eamHierarchy.getDirLvl() + 1;
        if (!BinaryUtils.isEmpty(hierarchyMap)) {
            EamHierarchyDto nextChy = hierarchyMap.get(nextDirLvl);
            if (!BinaryUtils.isEmpty(nextChy)) {
                //看是否有下一层级，将下一层及的“当前层级视图名称”和“自动填充属性”字段值给清空……并保存；
                if (dto.getShape().equals(SHAPE_FLAG) || "".equals(dto.getShape())) {
                    nextChy.setProName("");
                    nextChy.setBelongCIProName("");
                }
                String shape = eamHierarchy.getShape();
                if (SHAPE_FLAG.equals(shape) || "".equals(shape)) {
                    nextChy.setInheritShape(shape);
                    nextChy.setClassId(0L);
                } else if (!BinaryUtils.isEmpty(shape)) {
                    ProductEle ele = JSON.parseObject(shape, ProductEle.class);
                    nextChy.setClassId(ele.getId());
                    if (!BinaryUtils.isEmpty(ele.getIcon())) {
                        ele.setIcon(ele.getIcon().replaceAll(httpResource, ""));
                    }
                    nextChy.setInheritShape(JSON.toJSONString(ele));
                }
                EamHierarchy nextChyCopy = EamUtil.copy(nextChy, EamHierarchy.class);
                bmHierarchyDao.saveOrUpdate(nextChyCopy);
            }
        }
        return aLong;
    }

    @Override
    public List<EamHierarchyDto> queryByLvl(Integer level, Long domainId) {
        Predicate<EamHierarchyDto> predicate;
        if (!BinaryUtils.isEmpty(level)) {
            predicate = each -> level == each.getDirLvl().longValue();
        } else {
            predicate = each -> false;
        }
        List<EamHierarchyDto> list = selectAll(predicate, domainId, null);
        if (BinaryUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list;
    }

    @Override
    public EamHierarchyDto queryByLvlAndModelId(Integer level, Long modelId, Long domainId) {
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("domainId", domainId));
        if (!BinaryUtils.isEmpty(modelId)) {
            query.must(QueryBuilders.termQuery(MODEL_ID, modelId));
        }
        if (!BinaryUtils.isEmpty(level)) {
            query.must(QueryBuilders.termQuery(DIR_LEL, level));
        }
        List<EamHierarchy> list = bmHierarchyDao.getListByQuery(query);
        if (BinaryUtils.isEmpty(list)) {
            return null;
        }
        List<EamHierarchyDto> result = replaceIcon(list);
        EamHierarchyDto eamHierarchyDto = result.get(0);
        if (checkExistNextLve(eamHierarchyDto, domainId)) {
            eamHierarchyDto.setNextDirLvl(1);
        }
        if (!BinaryUtils.isEmpty(eamHierarchyDto) && !BinaryUtils.isEmpty(eamHierarchyDto.getModelId())) {
            Long modelIdByEs = eamHierarchyDto.getModelId();
            EamMultiModelHierarchy model = modelHierarchySvc.getModelById(modelIdByEs);
            EamMultiModelHierarchyDto modelDto = EamUtil.copy(model, EamMultiModelHierarchyDto.class);
            eamHierarchyDto.setModelHierarchyDto(modelDto);
        }
        this.getHierarchyStepName(Collections.singletonList(eamHierarchyDto));
        return eamHierarchyDto;
    }

    private boolean checkExistNextLve(EamHierarchyDto eamHierarchyDto, Long domainId) {
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("domainId", domainId));
        query.must(QueryBuilders.termQuery(MODEL_ID, eamHierarchyDto.getModelId()));
        query.must(QueryBuilders.termQuery(DIR_LEL, eamHierarchyDto.getDirLvl() + 1));
        return bmHierarchyDao.existByCondition(query);
    }

    private List<EamHierarchyDto> replaceIcon(List<EamHierarchy> list) {
        Map<Integer, EamHierarchy> dirLvlMap = list.stream().collect(Collectors.toMap(EamHierarchy::getDirLvl, each -> each, (k1, k2) -> k2));
        List<EamHierarchyDto> dto = EamUtil.copy(list, EamHierarchyDto.class, (s, t) -> {
            if (!BinaryUtils.isEmpty(t.getShape()) && !"0".equals(t.getShape())) {
                JSONObject shape = JSON.parseObject(t.getShape());
                if (shape.containsKey(ICON) && !BinaryUtils.isEmpty(shape.get(ICON))) {
                    shape.put(ICON, httpResource + shape.get(ICON));
                    t.setShape(JSON.toJSONString(shape));
                }
            }
            if (!BinaryUtils.isEmpty(t.getInheritShape()) && !"0".equals(t.getInheritShape())) {
                JSONObject shape = JSON.parseObject(t.getInheritShape());
                if (shape.containsKey(ICON) && !BinaryUtils.isEmpty(shape.get(ICON))) {
                    shape.put(ICON, httpResource + shape.get(ICON));
                    t.setInheritShape(JSON.toJSONString(shape));
                }
            }
            if (!BinaryUtils.isEmpty(s.getRltList())) {
                t.setRltList(JSON.parseArray(s.getRltList(), EamArtifactRltVo.class));
            }
            if (!BinaryUtils.isEmpty(t.getDirLvl()) && !BinaryUtils.isEmpty(dirLvlMap.get(t.getDirLvl() + 1))) {
                t.setNextDirLvl(t.getDirLvl() + 1);
            }
        });
        this.getHierarchyStepName(dto);
        return dto;
    }

    @Override
    public List<EamHierarchyDto> queryList(EamHierarchy cdt) {
        if (cdt == null) {
            cdt = new EamHierarchy();
        }
        if (BinaryUtils.isEmpty(cdt.getDomainId())) {
            cdt.setDomainId(1L);
        }
        Predicate<EamHierarchyDto> predicate = null;
        if (!BinaryUtils.isEmpty(cdt.getDirLvl())) {
            int dirLvl = cdt.getDirLvl();
            predicate = each -> each.getDirLvl() == dirLvl;
        }
        return selectAll(predicate, cdt.getDomainId(), cdt.getModelId());
    }

    @Override
    public List<EamHierarchyDto> queryByModelId(Long modelId) {
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("domainId", SysUtil.getCurrentUserInfo().getDomainId()));
        query.must(QueryBuilders.termQuery(MODEL_ID, modelId));
        List<EamHierarchy> eamList = bmHierarchyDao.getSortListByQuery(1, 1000, query, DIR_LEL, true).getData();
        if (BinaryUtils.isEmpty(eamList)) {
            return Collections.emptyList();
        }
        return replaceIcon(eamList);
    }

    @Override
    public List<EamHierarchyDto> queryByModelIds(List<Long> modelIds) {
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("domainId", SysUtil.getCurrentUserInfo().getDomainId()));
        query.must(QueryBuilders.termsQuery(MODEL_ID, modelIds));
        Page<EamHierarchy> eamHierarchyPage = bmHierarchyDao.getSortListByQuery(1, 1000, query, DIR_LEL, true);
        if (CollectionUtils.isEmpty(eamHierarchyPage.getData())) {
            return new ArrayList<>();
        }
        return replaceIcon(eamHierarchyPage.getData());
    }


    private List<EamHierarchyDto> selectAll(Predicate<EamHierarchyDto> predicate, Long domainId, Long modelId) {
        try {
            BoolQueryBuilder query = QueryBuilders.boolQuery();
            query.must(QueryBuilders.termQuery("type.keyword", Env.BM_TYPE));
            query.must(QueryBuilders.termQuery("domainId", domainId));
            if (!BinaryUtils.isEmpty(modelId)) {
                query.must(QueryBuilders.termQuery(MODEL_ID, modelId));
            }
            List<EamHierarchy> eamList = bmHierarchyDao.getSortListByQuery(1, 1000, query, DIR_LEL, true).getData();
            List<EamHierarchyDto> result = replaceIcon(eamList);
            if (null != predicate) {
                result = result.stream().filter(predicate).collect(Collectors.toList());
            }
            if (!BinaryUtils.isEmpty(result) && !BinaryUtils.isEmpty(result.get(0).getModelId())) {
                Long modelIdByEs = result.get(0).getModelId();
                EamMultiModelHierarchy model = modelHierarchySvc.getModelById(modelIdByEs);
                EamMultiModelHierarchyDto modelDto = EamUtil.copy(model, EamMultiModelHierarchyDto.class);
                for (EamHierarchyDto dto : result) {
                    dto.setModelHierarchyDto(modelDto);
                }
                this.getHierarchyStepName(result);
            }
            return result;
        } catch (Exception e) {
            log.error("查询失败", e);
        }
        return new ArrayList<>();
    }

    @Override
    public Integer deleteById(Long id) {
        //1.查询包含层级信息
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("id", id));
        List<EamHierarchy> listByQuery = bmHierarchyDao.getListByQuery(query);
        if (CollectionUtils.isEmpty(listByQuery)) {
            throw new ServiceException(THROW_MESSAGE);
        }
        EamHierarchy hierarchy = listByQuery.get(0);
        Integer dirLvl = hierarchy.getDirLvl();
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.must(QueryBuilders.termQuery("type.keyword", hierarchy.getType()));
        queryBuilder.must(QueryBuilders.termQuery("domainId", hierarchy.getDomainId()));
        queryBuilder.must(QueryBuilders.rangeQuery(DIR_LEL).gte(dirLvl));
        //删除该模型树下的
        queryBuilder.must(QueryBuilders.termQuery(MODEL_ID, hierarchy.getModelId()));
        List<EamHierarchy> deleteList = bmHierarchyDao.getListByQuery(queryBuilder);
        if (CollectionUtils.isEmpty(deleteList)) {
            throw new ServiceException(THROW_MESSAGE);
        }
        return bmHierarchyDao.deleteByQuery(queryBuilder, true);
    }


    @Override
    public Integer deleteNoVerifyById(Long id) {
        //1.查询包含层级信息
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("id", id));
        List<EamHierarchy> listByQuery = bmHierarchyDao.getListByQuery(query);
        if (CollectionUtils.isEmpty(listByQuery)) {
            throw new ServiceException(THROW_MESSAGE);
        }
        EamHierarchy hierarchy = listByQuery.get(0);
        Integer dirLvl = hierarchy.getDirLvl();
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.must(QueryBuilders.termQuery("type.keyword", hierarchy.getType()));
        queryBuilder.must(QueryBuilders.termQuery("domainId", hierarchy.getDomainId()));
        queryBuilder.must(QueryBuilders.rangeQuery(DIR_LEL).gte(dirLvl));
        List<EamHierarchy> deleteList = bmHierarchyDao.getListByQuery(queryBuilder);
        if (CollectionUtils.isEmpty(deleteList)) {
            throw new ServiceException(THROW_MESSAGE);
        }
        return bmHierarchyDao.deleteByQuery(queryBuilder, true);
    }

    @Override
    public EamHierarchy queryModeHierarchyByLvl(Integer level, Long domainId, Long modelId) {
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery(DIR_LEL, level));
        query.must(QueryBuilders.termQuery("domainId", domainId));
        query.must(QueryBuilders.termQuery(MODEL_ID, modelId));
        List<EamHierarchy> hierarchies = bmHierarchyDao.getListByQuery(query);
        if (BinaryUtils.isEmpty(hierarchies)) {
            return null;
        }
        return hierarchies.get(0);
    }

    @Override
    public Integer saveOrUpdateBatch(List<EamHierarchy> hierarchyList) {
        return bmHierarchyDao.saveOrUpdateBatch(hierarchyList);
    }

    @Override
    public List<EamHierarchyDto> queryByDiagramId(String diagramId) {
        Long modelId = categorySvc.getModelIdByDiagram(diagramId, LibType.PRIVATE);
        EamHierarchy hierarchy = new EamHierarchy();
        hierarchy.setModelId(modelId);
        return queryList(hierarchy);
    }

    @Override
    public boolean checkPictureUse(String name) {
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("type.keyword", Env.BM_TYPE));
        query.must(QueryBuilders.termQuery("domainId", SysUtil.getCurrentUserInfo().getDomainId()));
        query.must(QueryBuilders.termQuery("markedImg.keyword", name));
        List<EamHierarchy> eamList = bmHierarchyDao.getSortListByQuery(1, 1000, query, DIR_LEL, true).getData();
        return !CollectionUtils.isEmpty(eamList);
    }

    /**
     * 获取层级导航信息
     *
     * @param state 状态：todo(待创建) 或 done(已创建)
     * @return 导航模型DTO列表
     */
    @Override
    public List<NavigationModelDto> getHierarchyNavigation(String state) {
        // 参数校验
        if (BinaryUtils.isEmpty(state) || (!state.equals("todo") && !state.equals("done"))) {
            log.info("无效的状态参数: {}, 使用默认值 'done'", state);
            state = "done";
        }

        // 获取当前用户信息
        String loginCode = SysUtil.getCurrentUserInfo().getLoginCode();

        // 获取当前用户设计空间下的模型目录信息
        List<EamCategory> privateModelList = getUserPrivateModels(loginCode);

        // 获取系统所有发布建模工艺数据
        Page<EamMultiModelHierarchy> modelHierarchyPage = modelHierarchySvc.queryList(1, 500, null, 1, null);
        List<EamMultiModelHierarchy> allModelHierarchys = modelHierarchyPage.getData();

        if (CollectionUtils.isEmpty(allModelHierarchys)) {
            log.info("未找到任何建模工艺数据");
            return Collections.emptyList();
        }

        // 根据状态处理不同的逻辑
        if ("todo".equals(state)) {
            return processTodoModels(privateModelList, allModelHierarchys);
        } else {
            return processDoneModels(privateModelList, allModelHierarchys, loginCode, null);
        }
    }

    @Override
    public NavigationModelDto getHierarchyNavigationByDiagramId(String diagramId) {
        List<ESDiagram> diagramList = diagramDao.getListByQuery(
                QueryBuilders.termQuery("dEnergy.keyword", diagramId));
        if (CollectionUtils.isEmpty(diagramList)) {
            log.info("视图不存在: {}", diagramId);
            return new NavigationModelDto();
        }

        ESDiagram diagramInfo = diagramList.get(0);
        Long dirId = diagramInfo.getDirId();
        Integer isOpen = diagramInfo.getIsOpen();
        LibType libType = 0 == isOpen ? LibType.PRIVATE : LibType.DESIGN;
        String ownerCode = libType == LibType.PRIVATE ? diagramInfo.getOwnerCode() : null;

        // 获取当前视图所属目录关联的模型信息
        EamCategory eamCategory = categorySvc.getById(dirId, libType);
        if (BinaryUtils.isEmpty(eamCategory)) {
            log.info("视图所属目录信息异常: {}", dirId);
            return new NavigationModelDto();
        }

        Long modelId = eamCategory.getModelId();

        List<EamCategory> modelCategory = categorySvc.selectByModelId(modelId, libType, ownerCode);

        // 获取模型基本信息与模型层级信息
        EamMultiModelHierarchy modelBaseInfo = modelHierarchySvc.getModelById(modelId);
        
        if (BinaryUtils.isEmpty(modelBaseInfo)) {
            log.info("模型信息不存在: {}", modelId);
            return new NavigationModelDto();
        }

        return processDoneModels(modelCategory, Collections.singletonList(modelBaseInfo), ownerCode, diagramId).get(0);
    }

    /**
     * 获取用户私有模型列表
     */
    private List<EamCategory> getUserPrivateModels(String loginCode) {
        BoolQueryBuilder query = QueryBuilders.boolQuery()
                .must(QueryBuilders.existsQuery("modelId"))
                .must(QueryBuilders.termQuery("ownerCode.keyword", loginCode));

        return categoryPrivateDao.getListByQuery(query);
    }

    /**
     * 处理待创建模型
     */
    private List<NavigationModelDto> processTodoModels(List<EamCategory> privateModelList, List<EamMultiModelHierarchy> allModels) {
        // 获取用户已有的模型ID列表
        Set<Long> existModelIds = privateModelList.stream()
                .map(EamCategory::getModelId)
                .collect(Collectors.toSet());

        // 筛选出未创建的模型
        Map<Long, EamMultiModelHierarchy> todoModelMap = allModels.stream()
                .filter(model -> !existModelIds.contains(model.getId()))
                .collect(Collectors.toMap(
                    EamMultiModelHierarchy::getId,
                    Function.identity(),
                    (existing, replacement) -> existing
                ));

        if (todoModelMap.isEmpty()) {
            log.info("没有待创建的模型");
            return Collections.emptyList();
        }

        // 获取模型层级详情
        List<Long> todoModelIds = new ArrayList<>(todoModelMap.keySet());
        List<EamHierarchyDto> hierarchyDtos = queryByModelIds(todoModelIds);
        List<EamHierarchyNavigationDto> navigationDtos = EamUtil.copy(hierarchyDtos, EamHierarchyNavigationDto.class);

        // 按模型ID分组
        Map<Long, List<EamHierarchyNavigationDto>> hierarchyDtosMap = navigationDtos.stream()
                .collect(Collectors.groupingBy(EamHierarchyNavigationDto::getModelId));

        // 构建结果
        return buildNavigationModelDtos(todoModelMap, hierarchyDtosMap);
    }

    /**
     *  层级排序
     */
    private List<NavigationModelDto> sortModelInfo(List<NavigationModelDto> modelDtos) {

        if (CollectionUtils.isEmpty(modelDtos)) {
            return Collections.emptyList();
        }

        List<Long> modelIds = modelDtos.stream()
                .map(NavigationModelDto::getModelId)
                .collect(Collectors.toList());
        // 获取模型信息 取更新时间进行排序
        BoolQueryBuilder bool = QueryBuilders.boolQuery();
        bool.must(QueryBuilders.termsQuery("id", modelIds));
        List<SortBuilder<?>> sorts = new ArrayList<>();
        sorts.add(SortBuilders.fieldSort("modifyTime").order(SortOrder.DESC));
        List<EamMultiModelHierarchy> modelList = multiModelHierarchyDao.
                getSortListByQuery(1, 500, bool, sorts).getData();

        Map<Long, Long> modelIdMap = modelList
                .stream()
                .collect(Collectors.toMap(EamMultiModelHierarchy::getId, EamMultiModelHierarchy::getModifyTime));

        for (NavigationModelDto dto : modelDtos) {
            dto.setModifyTime(modelIdMap.get(dto.getModelId()));
        }

        return modelDtos.stream()
                .sorted(Comparator.comparingLong(NavigationModelDto::getModifyTime).reversed())
                .collect(Collectors.toList());
    }

    /**
     * 处理已创建模型
     */
    private List<NavigationModelDto> processDoneModels(List<EamCategory> privateModelList,
                                                     List<EamMultiModelHierarchy> allModels,
                                                     String loginCode, String paramDiagramId) {
        if (CollectionUtils.isEmpty(privateModelList)) {
            log.info("用户没有已创建的模型");
            return Collections.emptyList();
        }

        // 提取模型信息
        ModelCategoryInfo categoryInfo = extractModelCategoryInfo(privateModelList);
        List<Long> doneModelIds = categoryInfo.getModelIds();

        if (doneModelIds.isEmpty()) {
            log.info("没有有效的已创建模型");
            return Collections.emptyList();
        }

        // 获取层级信息
        List<EamHierarchyDto> hierarchyDtos = queryByModelIds(doneModelIds);
        List<EamHierarchyNavigationDto> navigationList = navigationHierarchySvc.getNavigationList(hierarchyDtos, loginCode);

        if (CollectionUtils.isEmpty(navigationList)) {
            log.info("未找到导航层级信息");
            return Collections.emptyList();
        }

        // 获取模型ID列表
        Set<Long> existModelIds = navigationList.stream()
                .map(EamHierarchyNavigationDto::getModelId)
                .collect(Collectors.toSet());

        // 筛选出已创建的模型
        Map<Long, EamMultiModelHierarchy> doneModelMap = allModels.stream()
                .filter(model -> existModelIds.contains(model.getId()))
                .collect(Collectors.toMap(
                    EamMultiModelHierarchy::getId,
                    Function.identity(),
                    (existing, replacement) -> existing
                ));

        // 处理视图信息和目录信息
        processNavigationDiagramInfo(navigationList, categoryInfo, paramDiagramId, loginCode);

        // 按模型ID分组
        Map<Long, List<EamHierarchyNavigationDto>> hierarchyDtosMap = navigationList.stream()
                .collect(Collectors.groupingBy(EamHierarchyNavigationDto::getModelId));

        // 构建结果
        return buildNavigationModelDtos(doneModelMap, hierarchyDtosMap);
    }

    /**
     * 提取模型目录信息
     */
    private ModelCategoryInfo extractModelCategoryInfo(List<EamCategory> privateModelList) {
        List<Long> modelIds = new ArrayList<>();
        Map<Long, Integer> modelRootLvlMap = new HashMap<>();
        List<String> diagramIds = new ArrayList<>();
        Map<Long, Map<Integer, List<EamCategory>>> modelGroupByIdAndLvlMap = new HashMap<>();
        List<String> ciCodes = new ArrayList<>();

        for (EamCategory category : privateModelList) {
            // 跳过已删除的数据
            if (category.getDataStatus() == null || category.getDataStatus() == 0) {
                continue;
            }

            Long modelId = category.getModelId();
            modelIds.add(modelId);

            // 记录模型根目录层级
            if (CategoryTypeEnum.MODEL_ROOT.val() == category.getType()) {
                modelRootLvlMap.put(modelId, category.getDirLvl());
            }

            // 处理视图信息
            if (!BinaryUtils.isEmpty(category.getDiagramId())) {
                diagramIds.add(category.getDiagramId());
            }

            if (!BinaryUtils.isEmpty(category.getCiCode())) {
                ciCodes.add(category.getCiCode());
            }

            // 按模型ID和目录层级分组视图ID
            modelGroupByIdAndLvlMap
                    .computeIfAbsent(modelId, k -> new HashMap<>())
                    .computeIfAbsent(category.getDirLvl(), k -> new ArrayList<>())
                    .add(category);
        }

        return new ModelCategoryInfo(modelIds, modelRootLvlMap, diagramIds, modelGroupByIdAndLvlMap, ciCodes);
    }

    /**
     * 处理导航视图信息
     */
    private void processNavigationDiagramInfo(List<EamHierarchyNavigationDto> navigationList,
                                              ModelCategoryInfo categoryInfo, String paramDiagramId,
                                              String loginCode) {
        /*// 如果没有视图ID，直接返回
        if (CollectionUtils.isEmpty(categoryInfo.getDiagramIds())) {
            return;
        }*/

        // 查询视图信息
        Map<String, ESDiagram> diagramMap = new HashMap<>();

        if (!CollectionUtils.isEmpty(categoryInfo.getDiagramIds())) {
            List<ESDiagram> diagramList = diagramDao.getListByQuery(
                    QueryBuilders.termsQuery("dEnergy.keyword", categoryInfo.getDiagramIds()));

            diagramMap = diagramList.stream()
                    .collect(Collectors.toMap(
                            ESDiagram::getDEnergy,
                            Function.identity(),
                            (existing, replacement) -> existing
                    ));
        }

        // 获取目录ci信息
        Map<String, String> ciLabelMap = getDirCiInfo(categoryInfo.getCiCodes(), loginCode);

        // 为每个导航项设置视图信息
        for (EamHierarchyNavigationDto navigation : navigationList) {

            /*// 跳过未开始的状态
            if ((navigation.getCompletionStatus() == null || navigation.getCompletionStatus() == 0) && 0 != navigation.getDirLvl()) {
                continue;
            }*/

            Integer navigationLvl = navigation.getDirLvl();
            Long modelId = navigation.getModelId();

            // 获取模型根目录层级
            Integer modelRootLvl = categoryInfo.getModelRootLvlMap().get(modelId);
            if (modelRootLvl == null) {
                continue;
            }

            // 获取该层级的视图ID列表
            Map<Integer, List<EamCategory>> lvlDiagramMap = categoryInfo.getModelGroupByIdAndLvlMap().get(modelId);
            if (lvlDiagramMap == null) {
                continue;
            }

            List<EamCategory> lvlCategoryList = lvlDiagramMap.get(modelRootLvl + navigationLvl + 1);
            if (CollectionUtils.isEmpty(lvlCategoryList)) {
                continue;
            }

            // 创建简化视图信息列表
            List<SimpDiagramInfo> simpDiagramInfos = new ArrayList<>();
            List<SimpCategoryInfo> simpCategoryInfos = new ArrayList<>();
            for (EamCategory dir : lvlCategoryList) {
                String diagramId = dir.getDiagramId();
                if (BinaryUtils.isEmpty(diagramId)) {
                    SimpCategoryInfo simpCategoryInfo = creatSimpleCategoryInfo(dir, ciLabelMap);
                    simpCategoryInfos.add(simpCategoryInfo);

                    if (0 == navigation.getDirLvl()) {
                        navigation.setCompletionStatus(1);
                    }

                } else {
                    ESDiagram diagram = diagramMap.get(diagramId);
                    if (diagram == null) {
                        continue;
                    }

                    SimpDiagramInfo diagramInfo = createSimpleDiagramInfo(diagram);
                    simpDiagramInfos.add(diagramInfo);

                    if (!BinaryUtils.isEmpty(paramDiagramId) && diagram.getDEnergy().equals(paramDiagramId)) {
                        navigation.setIsLocate(Boolean.TRUE);
                    }

                    if (0 == navigation.getDirLvl()) {
                        Integer status = BinaryUtils.isEmpty(navigation.getCompletionStatus()) ||  1 == navigation.getCompletionStatus() || 0 == navigation.getCompletionStatus()?
                                1 : 2;
                        navigation.setCompletionStatus(status);
                    }
                }
            }

            navigation.setDiagramInfos(simpDiagramInfos);
            navigation.setDirInfos(simpCategoryInfos);
        }
    }

    private Map<String, String> getDirCiInfo(List<String> ciCodes, String loginCode) {
        Map<String, String> ciLabelMap = new HashMap<>();
        if (CollectionUtils.isEmpty(ciCodes)) {
            return ciLabelMap;
        }

        CCcCi cdt = new CCcCi();
        cdt.setOwnerCodeEqual(loginCode);
        cdt.setCiCodes(ciCodes.toArray(new String[0]));
        List<ESCIInfo> esciInfos = privateCISvc.queryESCiInfoList(cdt, null, Boolean.FALSE);

        for (ESCIInfo esci : esciInfos) {
            String label = BinaryUtils.isEmpty(esci.getCiLabel()) ? esci.getCiPrimaryKey() : esci.getCiLabel();
            ciLabelMap.put(esci.getCiCode(), label.replaceAll("[\\[\\]\\\\\"]", ""));
        }
        return ciLabelMap;
    }

    /**
     * 创建简化视图信息
     */
    private SimpDiagramInfo createSimpleDiagramInfo(ESDiagram diagram) {
        SimpDiagramInfo diagramInfo = new SimpDiagramInfo();
        diagramInfo.setIcon1(httpResource + diagram.getIcon1());
        diagramInfo.setName(diagram.getName());
        diagramInfo.setDEnergy(diagram.getDEnergy());
        diagramInfo.setOwnerCode(diagram.getOwnerCode());
        diagramInfo.setDirId(diagram.getDirId());
        diagramInfo.setViewType(diagram.getViewType());
        diagramInfo.setModifyTime(diagram.getModifyTime());
        diagramInfo.setThumbnailSaveTime(diagram.getThumbnailSaveTime());
        diagramInfo.setFlowStatus(BinaryUtils.isEmpty(diagram.getFlowStatus()) ? 0 : diagram.getFlowStatus());
        return diagramInfo;
    }

    /**
     * 创建目录信息
     */
    private SimpCategoryInfo creatSimpleCategoryInfo(EamCategory category, Map<String, String> ciLabelMap) {
        String ciCode = category.getCiCode();
        String name = ciLabelMap.get(ciCode);
        SimpCategoryInfo simpCategoryInfo = new SimpCategoryInfo();
        simpCategoryInfo.setDirId(category.getId());
        simpCategoryInfo.setDirName(name);
        simpCategoryInfo.setCiCode(ciCode);
        return simpCategoryInfo;
    }

    /**
     * 构建导航模型DTO列表
     */
    private List<NavigationModelDto> buildNavigationModelDtos(Map<Long, EamMultiModelHierarchy> modelMap,
            Map<Long, List<EamHierarchyNavigationDto>> hierarchyDtosMap) {

        List<NavigationModelDto> result = new ArrayList<>();

        for (Map.Entry<Long, EamMultiModelHierarchy> entry : modelMap.entrySet()) {
            Long modelId = entry.getKey();
            EamMultiModelHierarchy model = entry.getValue();

            NavigationModelDto dto = new NavigationModelDto();
            dto.setModelId(modelId);
            dto.setModelName(model.getName());
            dto.setEamHierarchyDtos(hierarchyDtosMap.get(modelId));

            result.add(dto);
        }

        return sortModelInfo(result);
    }

    /**
     *  获取层级步骤名称信息
     * @param hierarchyDtos
     */
    private void getHierarchyStepName(List<EamHierarchyDto> hierarchyDtos) {
        if (CollectionUtils.isEmpty(hierarchyDtos)) {
            return;
        }
        // 需要补全步骤名称的层级信息
        List<EamHierarchyDto> needFillStepNameList = new ArrayList<>();
        for (EamHierarchyDto eamHierarchyDto : hierarchyDtos) {
            if (BinaryUtils.isEmpty(eamHierarchyDto.getStepName())) {
                needFillStepNameList.add(eamHierarchyDto);
            }
        }
        if (CollectionUtils.isEmpty(needFillStepNameList)) {
            return;
        }

        List<Long> artifactIds = needFillStepNameList.
                stream().
                filter(e->!BinaryUtils.isEmpty(e.getArtifactId())).
                map(EamHierarchyDto::getArtifactId).distinct().collect(Collectors.toList());

        if (CollectionUtils.isEmpty(artifactIds)) {
            return;
        }
        // 获取制品信息
        List<EamArtifact> artifactInfoList = artifactDao.getListByQuery(QueryBuilders.termsQuery("id", artifactIds));
        if (CollectionUtils.isEmpty(artifactInfoList)) {
            return;
        }
        Map<Long, EamArtifact> artifactInfoMap = artifactInfoList.stream().collect(Collectors.toMap(EamArtifact::getId, e -> e, (k1, k2) -> k1));

        for (EamHierarchyDto eamHierarchyDto : hierarchyDtos) {
            if (!BinaryUtils.isEmpty(eamHierarchyDto.getStepName())) {
                continue;
            }
            Long artifactId = eamHierarchyDto.getArtifactId();
            if (BinaryUtils.isEmpty(artifactId)) {
                continue;
            }
            EamArtifact eamArtifact = artifactInfoMap.get(artifactId);
            if (BinaryUtils.isEmpty(eamArtifact)) {
                continue;
            }
            Integer dirLvl = eamHierarchyDto.getDirLvl();
            if (BinaryUtils.isEmpty(dirLvl)) {
                continue;
            }
            eamHierarchyDto.setStepName("L" + dirLvl + " " + eamArtifact.getArtifactName());
        }

    }


}