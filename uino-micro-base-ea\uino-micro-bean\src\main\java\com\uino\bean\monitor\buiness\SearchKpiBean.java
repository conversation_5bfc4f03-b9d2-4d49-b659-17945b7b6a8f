package com.uino.bean.monitor.buiness;

import java.util.Collection;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Builder.Default;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 
 * <AUTHOR>
 *
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SearchKpiBean {

    /**
     * 全文检索值
     */
    private String searchVal;
    
    /**
     * 
     */
    private String searchCodeOrName;

    // private String kpiName;

    private Collection<String> kpiNames;

    private String sortField;

    @Default
    private Boolean isAsc = true;

    /**
     * 指标所属分类id
     */
    private Long classId;

	private String serviceId;

	private Long domainId;
    /**
     * 页码
     */
    @Builder.Default
    private int pageNum = 1;

    /**
     * 每页行数
     */
    @Builder.Default
    private int pageSize = 20;
}
