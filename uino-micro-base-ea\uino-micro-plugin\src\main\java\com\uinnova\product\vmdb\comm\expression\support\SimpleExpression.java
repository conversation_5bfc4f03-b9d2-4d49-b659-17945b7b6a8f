package com.uinnova.product.vmdb.comm.expression.support;

import com.uinnova.product.vmdb.comm.exception.ExpressionException;
import com.uinnova.product.vmdb.comm.expression.Field;
import com.uinnova.product.vmdb.comm.expression.OP;

/**
 * 一般表达式
 * 
 * <AUTHOR>
 */
public class SimpleExpression<E> extends AbstractExpression<E> {
    private static final long serialVersionUID = 1L;

    private Field<E> left;
    private OP op;
    private String right;

    public SimpleExpression(Field<E> left, OP op, String right) {
        if (left == null) {
            throw new ExpressionException(" the left is NULL argument! ");
        }
        if (op == null) {
            throw new ExpressionException(" the op is NULL argument! ");
        }
        if (right == null) {
            throw new ExpressionException(" the right is NULL argument! ");
        }
        if (op == OP.In || op == OP.NotIn) {
            throw new ExpressionException(" the op:'" + op + "' is not support operate simple-value! ");
        }

        this.left = left;
        this.op = op;
        this.right = right;
    }

    @Override
    protected String buildSqlFragment() {
        if (this.op == OP.INSTR) {
            return this.op.getOperator() + "(" + this.left.getName() + ",'" + this.right + "')>0";
        } else {
            return this.left.getName() + this.op.getOperator() + "'" + this.right + "'";
        }
    }

}
