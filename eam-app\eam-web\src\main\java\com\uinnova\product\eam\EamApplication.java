package com.uinnova.product.eam;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.ImportResource;
import org.springframework.scheduling.annotation.EnableScheduling;

@ImportResource(locations={"classpath:spring-context.xml"})
@SpringBootApplication
@Configuration
@EnableScheduling
@ServletComponentScan("com.uino.init")
@ComponentScan(
		basePackages = {"com.uino.api.init",
		"com.uinnova.product.eam.web","com.uino.web",
		"com.uinnova.product.eam.config","com.uino.init",
        "com.uinnova.product.eam.init", "com.uino.oauth2", "com.uino.oauth.common",
		"com.uinnova.product.eam.db",
		"com.uinnova.product.eam.base.diagram.utils",
		"com.uino.service","com.uino.dao",
		"com.uino.monitor","com.uino.util",
		"com.uinnova.product.eam.service.cj"})
@EnableFeignClients(basePackages = { "com.uinnova.product.eam.feign"})
@Slf4j
@EnableDiscoveryClient
public class EamApplication {

	static {
		log.info("开始加载eam-web模块");
	}

	public static void main(String[] args) {
		SpringApplication.run(EamApplication.class, args);
		log.info("EAM application start success :)");
	}


}
