package com.uinnova.product.devcloud.i18n.client.support;

import com.binary.core.bean.BMProxy;
import com.binary.core.i18n.Language;
import com.binary.core.lang.Conver;
import com.binary.core.util.BinaryUtils;
import com.binary.core.util.SecurityMap;
import com.uinnova.product.devcloud.i18n.client.I18nClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.*;

/**
 * description
 *
 * <AUTHOR>
 * @since 2024/5/16 13:54
 */
public abstract class AbstractI18nClient implements I18nClient {
    private static final Logger logger = LoggerFactory.getLogger(AbstractI18nClient.class);
    private final String defaultClassCode;
    private final Object syncobj;
    private Map<Language, Map<String, String>> languageData;
    private Map<Language, Map<String, Map<String, String>>> languageGroupData;
    private boolean refreshData;
    private int refreshPeriod;

    protected AbstractI18nClient() {
        this(false, -1);
    }

    protected AbstractI18nClient(boolean refreshData) {
        this(refreshData, -1);
    }

    protected AbstractI18nClient(boolean refreshData, int refreshPeriod) {
        this.defaultClassCode = BinaryUtils.getUUID();
        this.syncobj = new Object();
        this.languageData = new HashMap();
        this.languageGroupData = new HashMap();
        this.refreshData = false;
        this.refreshPeriod = 60;
        this.refreshData = refreshData;
        if (refreshPeriod > 0) {
            this.refreshPeriod = refreshPeriod;
        }

    }

    public boolean isRefreshData() {
        return this.refreshData;
    }

    public int getRefreshPeriod() {
        return this.refreshPeriod;
    }

    protected void afterInit() {
        if (this.refreshData) {
            this.createRefreshTimer();
        } else {
            this.refresh();
        }

    }

    protected String toUpperFirstChar(String name) {
        char c = name.charAt(0);
        if (c >= 'a' && c <= 'z') {
            char[] arr = name.toCharArray();
            arr[0] = (char)(c - 32);
            name = String.valueOf(arr);
        }

        return name;
    }

    protected void createRefreshTimer() {
        Timer timer = new Timer(true);
        timer.schedule(new TimerTask() {
            public void run() {
                AbstractI18nClient.this.refresh();
            }
        }, new Date(), (long)(this.refreshPeriod * 1000));
    }

    protected void refresh() {
        logger.debug(" verify refresh language data ... ");

        try {
            boolean ba = this.verifyRefresh();
            if (ba) {
                logger.info(" has refresh language data ... ");
                List<I18nTransData> data = this.loadLanguageData();
                logger.info(" load refresh language data [" + (data == null ? 0 : data.size()) + "]. ");
                if (data != null && data.size() > 0) {
                    this.parseLanguageData(data);
                }
            }
        } catch (Throwable var3) {
            logger.error(" refresh language data error '" + var3.getClass().getName() + "' : " + var3.getMessage());
        }

    }

    protected void parseLanguageData(List<I18nTransData> data) {
        BMProxy<I18nTransData> proxy = BMProxy.getInstance(I18nTransData.class);
        Map<Language, Map<String, String>> lanData = new HashMap();
        Map<Language, Map<String, Map<String, String>>> lanGroupData = new HashMap();

        I18nTransData row;
        for(int i = 0; i < data.size(); ++i) {
            row = (I18nTransData)data.get(i);
            String code = row.getCode();
            String classCode = row.getClassCode();
            if (code != null && (code = code.trim()).length() != 0) {
                if (classCode == null || (classCode = classCode.trim()).length() == 0) {
                    classCode = this.defaultClassCode;
                }

                classCode = classCode.trim().toUpperCase();
                proxy.replaceInnerObject(row);
                Iterator<Map.Entry<String, Object>> itor = proxy.entryIterator();

                while(itor.hasNext()) {
                    Map.Entry<String, Object> e = (Map.Entry)itor.next();
                    String key = (String)e.getKey();
                    Object value = e.getValue();
                    if (key != null && key.startsWith("lan") && !BinaryUtils.isEmpty(value)) {
                        String sv = (String) Conver.to(value, String.class);
                        Language lan = Language.valueOf(key.substring(3).toUpperCase());
                        Map<String, String> pk = (Map)lanData.get(lan);
                        if (pk == null) {
                            pk = new HashMap();
                            lanData.put(lan, pk);
                        }

                        ((Map)pk).put(code, sv);
                        Map<String, Map<String, String>> grouppk = (Map)lanGroupData.get(lan);
                        if (grouppk == null) {
                            grouppk = new HashMap();
                            lanGroupData.put(lan, grouppk);
                        }

                        Map<String, String> gpk = (Map)((Map)grouppk).get(classCode);
                        if (gpk == null) {
                            gpk = new HashMap();
                            ((Map)grouppk).put(classCode, gpk);
                        }

                        ((Map)gpk).put(code, sv);
                    }
                }
            }
        }

        Map<Language, Map<String, String>> oldLanData = this.languageData;
        Map<Language, Map<String, Map<String, String>>> oldLanGroupData = this.languageGroupData;
        synchronized(this.syncobj) {
            this.languageData = lanData;
            this.languageGroupData = lanGroupData;
        }

        oldLanData.clear();
        oldLanGroupData.clear();
        oldLanData = null;
        row = null;
    }

    protected abstract boolean verifyRefresh() throws IOException;

    protected abstract List<I18nTransData> loadLanguageData() throws IOException;

    public String getLanguageValue(Language language, String code) {
        BinaryUtils.checkEmpty(language, "language");
        BinaryUtils.checkEmpty(code, "code");
        Map<String, String> pk = (Map)this.languageData.get(language);
        return pk == null ? null : (String)pk.get(code);
    }

    public Map<String, String> getLanguagePackage(Language language) {
        return this.getLanguagePackage(language, (String)null);
    }

    public Map<String, String> getLanguagePackage(Language language, String classCode) {
        BinaryUtils.checkEmpty(language, "language");
        Map grouppk;
        if (BinaryUtils.isEmpty(classCode)) {
            grouppk = (Map)this.languageData.get(language);
            if (grouppk != null) {
                return new SecurityMap(grouppk);
            }
        } else if (classCode.indexOf(44) < 0) {
            grouppk = (Map)this.languageGroupData.get(language);
            if (grouppk != null) {
                Map<String, String> pk = (Map)grouppk.get(classCode.trim().toUpperCase());
                if (pk != null) {
                    return new SecurityMap(pk);
                }
            }
        } else {
            grouppk = (Map)this.languageGroupData.get(language);
            if (grouppk != null) {
                String[] arr = classCode.split("[,]");
                Map<String, String> rpk = null;

                for(int i = 0; i < arr.length; ++i) {
                    Map<String, String> pk = (Map)grouppk.get(arr[i].trim().toUpperCase());
                    if (pk != null && pk.size() > 0) {
                        if (rpk == null) {
                            rpk = new HashMap();
                        }

                        rpk.putAll(pk);
                    }
                }

                return rpk;
            }
        }

        return null;
    }
}
