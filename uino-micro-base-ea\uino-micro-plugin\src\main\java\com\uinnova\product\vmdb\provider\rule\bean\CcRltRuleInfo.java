package com.uinnova.product.vmdb.provider.rule.bean;

import com.uinnova.product.vmdb.comm.model.rule.CcRltRuleDef;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;

import java.io.Serializable;
import java.util.List;

public class CcRltRuleInfo implements Serializable {
	private static final long serialVersionUID = 1L;
	
	
	
	/**
	 * 规则定义对象
	 */
	private CcRltRuleDef def;
	
	
	
	/**
	 * 规则线
	 */
	private List<CcRltLineInfo> lines;

	
	/**
	 * 关系节点
	 */
	private List<CcRltNodeInfo> nodes;
	
	/**
	 * 数据版本
	 */
	private Integer version;
	
	/**
	 * 起始点CI分类信息
	 */
	private CcCiClassInfo ciClassInfo;
	
	
	public CcRltRuleDef getDef() {
		return def;
	}


	public void setDef(CcRltRuleDef def) {
		this.def = def;
	}

	public List<CcRltLineInfo> getLines() {
		return lines;
	}


	public void setLines(List<CcRltLineInfo> lines) {
		this.lines = lines;
	}


	public List<CcRltNodeInfo> getNodes() {
		return nodes;
	}


	public void setNodes(List<CcRltNodeInfo> nodes) {
		this.nodes = nodes;
	}


	public CcCiClassInfo getCiClassInfo() {
		return ciClassInfo;
	}


	public void setCiClassInfo(CcCiClassInfo ciClassInfo) {
		this.ciClassInfo = ciClassInfo;
	}


	public Integer getVersion() {
		return version;
	}


	public void setVersion(Integer version) {
		this.version = version;
	}




	
	
	

}
