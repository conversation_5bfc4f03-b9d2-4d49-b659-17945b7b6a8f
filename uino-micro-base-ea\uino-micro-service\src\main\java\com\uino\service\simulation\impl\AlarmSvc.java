package com.uino.service.simulation.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import com.uino.dao.BaseConst;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.ConstantScoreQueryBuilder;
import org.elasticsearch.index.query.MultiMatchQueryBuilder.Type;
import org.elasticsearch.index.query.Operator;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import com.binary.core.util.BinaryUtils;
import com.binary.jdbc.Page;
import com.uino.bean.cmdb.base.ESCIClassInfo;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.base.ESCIRltInfo;
import com.uino.bean.monitor.base.ESAlarm;
import com.uino.bean.monitor.buiness.AlarmInfo;
import com.uino.bean.monitor.buiness.AlarmQueryDto;
import com.uino.bean.monitor.buiness.SimulationAlarmBean;
import com.uino.bean.sys.base.ESDictionaryAttrDef;
import com.uino.bean.sys.base.ESDictionaryClassInfo;
import com.uino.bean.sys.base.ESDictionaryItemInfo;
import com.uino.bean.sys.query.ESDictionaryItemSearchBean;
import com.uino.dao.cmdb.ESCIClassSvc;
import com.uino.dao.cmdb.ESCIRltSvc;
import com.uino.dao.cmdb.ESCISvc;
import com.uino.dao.cmdb.ESRltClassSvc;
import com.uino.dao.sys.ESDictionaryClassSvc;
import com.uino.dao.event.ESAlarmSvc;
import com.uino.monitor.event.service.IEventService;
import com.uino.service.simulation.IAlarmSvc;
import com.uino.service.sys.microservice.IDictionarySvc;
import com.uino.util.sys.BeanUtil;
import com.uino.util.sys.SysUtil;

/**
 * 告警服务默认实现
 *
 * <AUTHOR>
 */
@Service
@RefreshScope
public class AlarmSvc implements IAlarmSvc {

    @Value("${monitor.select.include.domainId:true}")
    private boolean includeDomainId;

    @Autowired
    private ESAlarmSvc esAlarmSvc;

    @Autowired
    private ESCIClassSvc esCIClassSvc;

    @Autowired
    private ESRltClassSvc esRltClassSvc;

    @Autowired
    private ESCISvc esCISvc;

    @Autowired
    private ESCIRltSvc esCIRltSvc;

    @Autowired
    IDictionarySvc dictionarySvc;

    @Autowired
    private ESDictionaryClassSvc dictClsSvc;

    @Autowired
    ESCISvc esciSvc;

    @Autowired
    private IEventService eventService;

    @Override
    public Page<AlarmInfo> searchAlarms(AlarmQueryDto queryDto) {
        int pageNum = queryDto.getPageNum();
        int pageSize = queryDto.getPageSize();
        String order = queryDto.getOrder();
        order = SysUtil.StringUtil.isNotBack(order) ? order : "alarmTime";
        boolean asc = queryDto.isAsc();
        if(includeDomainId && BinaryUtils.isEmpty(queryDto.getDomainId())){
            queryDto.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
        }
        BoolQueryBuilder query = this.queryDtoToESQuery(queryDto);
        Page<ESAlarm> res = esAlarmSvc.getSortListByQuery(pageNum, pageSize, query, order, asc);
        return this.fullAarlmInfo(res);
    }

    @Override
    public void saveAlarm(ESAlarm saveDto) {
        eventService.saveAlarm(saveDto);
    }

    /**
     * 完善告警信息
     *
     * @param alarmPageInfo
     * @return
     */
    private Page<AlarmInfo> fullAarlmInfo(Page<ESAlarm> alarmPageInfo) {
        Page<AlarmInfo> fullPageInfo = new Page<>(alarmPageInfo.getPageNum(), alarmPageInfo.getPageSize(),
                alarmPageInfo.getTotalRows(), alarmPageInfo.getTotalPages(), new LinkedList<>());
        if (alarmPageInfo.getData() != null && alarmPageInfo.getData().size() > 0) {
            /**
             * 1完善指标信息 2完善对象/关系分类信息 3完善对象/关系信息
             */
            // ci分类ids
            Set<Long> ciClsIds = new HashSet<>();
            // 关系分类ids
            Set<Long> rltClsIds = new HashSet<>();
            // ciIds
            Set<Long> ciIds = new HashSet<>();
            // 关系ids
            Set<Long> rltIds = new HashSet<>();
            // 获取需要完善信息的所有相关ids，待之后填补使用
            for (ESAlarm alarm : alarmPageInfo.getData()) {
                if (alarm.getAlarmObjType() == 0) {
                    // 代表告警对象为ci
                    ciClsIds.add(alarm.getClassId());
                    ciIds.add(alarm.getAlarmObjId());
                } else {
                    // 代表告警对象为关系
                    rltClsIds.add(alarm.getClassId());
                    rltIds.add(alarm.getAlarmObjId());
                }
            }
            // ci分类字典
            Map<Long, ESCIClassInfo> ciClsDict = new HashMap<>();
            if (ciClsIds != null && ciClsIds.size() > 0) {
                List<ESCIClassInfo> ciClss = esCIClassSvc.getListByQuery(QueryBuilders.termsQuery("id", ciClsIds));
                ciClss.forEach(cls -> ciClsDict.put(cls.getId(), cls));
            }
            // 关系分类字典
            Map<Long, ESCIClassInfo> rltClsDict = new HashMap<>();
            if (ciClsIds != null && ciClsIds.size() > 0) {
                List<ESCIClassInfo> rltClss = esRltClassSvc.getListByQuery(QueryBuilders.termsQuery("id", rltClsIds));
                rltClss.forEach(cls -> rltClsDict.put(cls.getId(), cls));
            }
            // ci字典
            Map<Long, ESCIInfo> ciDict = new HashMap<>();
            if (ciIds != null && ciIds.size() > 0) {
                List<ESCIInfo> cis = esCISvc.getListByQuery(QueryBuilders.termsQuery("id", ciIds));
                cis.forEach(ci -> ciDict.put(ci.getId(), ci));
            }
            // 关系字典
            Map<Long, ESCIRltInfo> rltDict = new HashMap<>();
            if (rltIds != null && rltIds.size() > 0) {
                List<ESCIRltInfo> rlts = esCIRltSvc.getListByQuery(QueryBuilders.termsQuery("id", rltIds));
                rlts.forEach(rlt -> rltDict.put(rlt.getId(), rlt));
            }
            // 所需字典都构建完毕，现状开始拼装完整数据
            alarmPageInfo.getData().forEach(alarmInfo -> {
                AlarmInfo addDto = new AlarmInfo();
                fullPageInfo.getData().add(addDto);
                addDto.setAlarmInfo(alarmInfo);
                // addDto.setKpiInfo(kpiDict.get(alarmInfo.getKpiId()));
                if (alarmInfo.getAlarmObjType() == 0) {
                    addDto.setCiInfo(ciDict.get(alarmInfo.getAlarmObjId()));
                    addDto.setClsInfo(ciClsDict.get(alarmInfo.getClassId()));
                } else {
                    addDto.setCiRltInfo(rltDict.get(alarmInfo.getAlarmObjId()));
                    addDto.setClsInfo(rltClsDict.get(alarmInfo.getClassId()));
                }
            });
        }
        return fullPageInfo;
    }

    @Override
    public Long countByQuery(AlarmQueryDto queryDto) {
        if(includeDomainId && BinaryUtils.isEmpty(queryDto.getDomainId())){
            queryDto.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
        }
        BoolQueryBuilder query = this.queryDtoToESQuery(queryDto);
        return esAlarmSvc.countByCondition(query);
    }

    @Override
    public ESAlarm queryOpenAlarm(String metric, Long classId, Long objId) {
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        ConstantScoreQueryBuilder constantScoreQueryBuilder = QueryBuilders.constantScoreQuery(query);
        query.must(QueryBuilders.termQuery("kpiName.keyword", metric));
        query.must(QueryBuilders.termQuery("classId", classId));
        query.must(QueryBuilders.termQuery("alarmObjId", objId));
        query.must(QueryBuilders.termQuery("status", 0));
        Page<ESAlarm> alarmPage = esAlarmSvc.getListByQuery(1, 1, constantScoreQueryBuilder);
        if (alarmPage.getData().isEmpty()) {
            return null;
        } else {
            return alarmPage.getData().get(0);
        }
    }

    /**
     * queryDto 转换esQuery
     *
     * @param queryDto
     * @return
     */
    private BoolQueryBuilder queryDtoToESQuery(AlarmQueryDto queryDto) {
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        if (queryDto == null) {
            return query;
        }
        if (queryDto.getMock() != null) {
            query.must(QueryBuilders.termQuery("mock", queryDto.getMock()));
        }
        if (queryDto.getDomainId() != null) {
            query.must(QueryBuilders.termQuery("domainId", queryDto.getDomainId()));
        }
        if (queryDto.getStatus() != null) {
            query.must(QueryBuilders.termsQuery("status", queryDto.getStatus()));
        }
        if (queryDto.getSeverityIds() != null) {
            query.must(QueryBuilders.termsQuery("severityId", queryDto.getSeverityIds()));
        }
        if (queryDto.getAlarmTimeStart() != null) {
            query.must(QueryBuilders.rangeQuery("alarmTime").gte(queryDto.getAlarmTimeStart()));
        }
        if (queryDto.getAlarmTimeEnd() != null) {
            query.must(QueryBuilders.rangeQuery("alarmTime").lte(queryDto.getAlarmTimeEnd()));
        }
        if (queryDto.getKeyword() != null && !"".equals(queryDto.getKeyword().trim())) {
            // 告警对象/指标/描述
            BoolQueryBuilder keywordQuery = QueryBuilders.boolQuery();
            keywordQuery.should(QueryBuilders.multiMatchQuery(queryDto.getKeyword(), "kpiName", "desc")
                    .operator(Operator.AND).type(Type.PHRASE_PREFIX).lenient(true));
            // 告警对象筛选
            Set<Long> objIds = new HashSet<>();
            // 寻找ci对象
            Page<ESCIInfo> ciPage = esCISvc.getListByQuery(1, 5000,
                    QueryBuilders.multiMatchQuery(queryDto.getKeyword(), "ciPrimaryKey").operator(Operator.AND)
                            .type(Type.PHRASE_PREFIX).lenient(true));
            if (ciPage.getTotalRows() > 0) {
                Map<Long, List<ESCIInfo>> ciIdObjsMap = ciPage.getData().stream()
                        .collect(Collectors.groupingBy(ESCIInfo::getId));
                objIds.addAll(ciIdObjsMap.keySet());
            }
            // 寻找关系对象
            Page<ESCIRltInfo> rltPage = esCIRltSvc.getListByQuery(1, 5000,
                    QueryBuilders.multiMatchQuery(queryDto.getKeyword(), "uniqueCode").operator(Operator.AND)
                            .type(Type.PHRASE_PREFIX).lenient(true));
            if (rltPage.getTotalRows() > 0) {
                Map<Long, List<ESCIRltInfo>> rltIdObjsMap = rltPage.getData().stream()
                        .collect(Collectors.groupingBy(ESCIRltInfo::getId));
                objIds.addAll(rltIdObjsMap.keySet());
            }
            if (objIds.size() > 0) {
                keywordQuery.should(QueryBuilders.termsQuery("alarmObjId", objIds));
            }
            query.must(keywordQuery);
        }
        return query;
    }

    /**
     * 获取 字典表 告警来源名称和keycode  map
     *
     * @return
     */
    public Map<String, String> getExteralDictValues() {
        ESDictionaryItemSearchBean bean = new ESDictionaryItemSearchBean();
        bean.setDictCode("monitorSource");
        bean.setDictDefId(303L);
        // 校验参数
        Long dictClassId = bean.getDictClassId();
        String dictCode = bean.getDictCode();
        String dictName = bean.getDictName();
        Long dictDefId = bean.getDictDefId();
        String dictProName = bean.getDictProName();
        Assert.isTrue(dictClassId != null || dictCode != null || dictName != null, "X_PARAM_NOT_NULL${name:引用字典定义}");
        Assert.isTrue(dictDefId != null || dictProName != null, "X_PARAM_NOT_NULL${name:引用属性}");
        Map<String, String> res = new HashMap<>();
        // 获取字典项
        List<ESDictionaryItemInfo> items = dictionarySvc.searchDictItemListByBean(bean);
        if (BinaryUtils.isEmpty(items)) {
            return res;
        }
        // 判断引用属性是否存在
        ESDictionaryClassInfo dictClassInfo = dictClsSvc.getById(items.get(0).getDictClassId());
        String proName = null;
        for (ESDictionaryAttrDef def : dictClassInfo.getDictAttrDefs()) {
            if (dictDefId != null && dictDefId.longValue() == def.getId().longValue()) {
                proName = def.getProName();
            }
            if (!BinaryUtils.isEmpty(dictProName) && dictProName.equals(def.getProName())) {
                proName = def.getProName();
            }
        }
        if (proName == null) {
            return res;
        }

        for (ESDictionaryItemInfo item : items) {
            Map<String, String> attrs = item.getAttrs();
            String val = attrs.get(proName);
            if (!BinaryUtils.isEmpty(val) && !res.containsKey(val)) {
                res.put(val, item.getKeyCode());
            }
        }
        return res;
    }

	@Override
	public void simulationAlarms(SimulationAlarmBean bean) {
		List<ESAlarm> alarms = new ArrayList<ESAlarm>();
		List<Long> ciIds = bean.getCiIds();
		Long alarmCode = bean.getAlarmCode();
		for (int i = 0; i < ciIds.size(); i++) {
			Long ciId = ciIds.get(i);
			ESAlarm alarm = BeanUtil.converBean(bean, ESAlarm.class);
			alarm.setAlarmCode(alarmCode + i);
			alarm.setAlarmObjId(ciId);
			alarm.setUniqueKey(ciId + bean.getKpiName());
			alarms.add(alarm);
		}
		if (!BinaryUtils.isEmpty(alarms)) {
			eventService.saveAlarmBatch(alarms);
		}
	}
}
