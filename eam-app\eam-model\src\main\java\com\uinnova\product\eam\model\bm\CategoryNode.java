package com.uinnova.product.eam.model.bm;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.comm.model.es.EamVersionTag;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class CategoryNode {
    @Comment("目录id")
    private Long id;
    @Comment("版本id")
    private Long tagId;
    @Comment("目录名称")
    private String dirName;
    @Comment("ciCode")
    private String ciCode;
    @Comment("视图id")
    private String diagramId;
    @Deprecated
    @Comment("目录类型")
    private Integer dirType;
    @Comment("新目录类型")
    private Integer type;
    @Comment("模型树Id")
    private Long modelId;
    @Comment("模型类型Id")
    private Integer typeId;
    @Comment("是否不可选中")
    private Boolean unSelect = false;
    @Comment("是否历史版本")
    private Boolean historyFlag = false;
    @Comment("父级id")
    private Long parentId;
    @Comment("用户code")
    private String ownerCode;
    @Comment("目录层级")
    private Integer dirLvl;
    @Comment("目录层级路径")
    private String dirPath;
    @Comment("校验单张视图")
    private Integer oneView;
    @Comment("创建时间")
    private Long createTime;
    @Comment("修改时间")
    private Long modifyTime;
    @Comment("序号")
    private String sortNum;
    @Comment("Label")
    private String label;
    @Comment("子级")
    private List<CategoryNode> children;
    @Comment("历史版本标签集合")
    private List<EamVersionTag> historyList;

    @Comment("目录是否可选择 默认 = true（可选）")
    private Boolean isSelectable = Boolean.TRUE;
    @Comment("当前层级视图信息集合")
    private List<SimpDiagramInfo> esDiagramList;
    @Comment("当前目录流程中状态 0=正常 1=驳回 2=流程中")
    private Integer flowStatus = 0;
}
