package com.uino.provider.server.web.sys.mvc;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.uino.service.sys.microservice.ILogoSvc;
import com.uino.bean.sys.base.Logo;
import com.uino.provider.feign.sys.LogoFeign;

@RestController
@RequestMapping("feign/sys/logo")
public class LogoFeignMvc implements LogoFeign {

    @Autowired
    ILogoSvc svc;

    @Override
    public Map<String, Logo> getLogos() {
        return svc.getLogos();
    }

    @Override
    public Map<String, Logo> updateLogo(String logoType, MultipartFile file) {
        return svc.updateLogo(logoType, file);
    }

    @Override
    public Map<String, Logo> deleteLogo(String logoType) {
        return svc.deleteLogo( logoType);
    }

    @Override
    public Map<String, Logo> updateLogoByPath(String logoType, String path, Long fileId) {
        return svc.updateLogoByPath(logoType, path, fileId);
    }
}
