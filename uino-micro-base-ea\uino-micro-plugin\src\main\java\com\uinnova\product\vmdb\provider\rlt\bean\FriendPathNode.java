package com.uinnova.product.vmdb.provider.rlt.bean;

import java.io.Serializable;

public class FriendPathNode implements Serializable {

	private static final long serialVersionUID = 1L;

	private Long ciClassId;
	
	private String className;

	private Long[] ciIds;

	private Integer ciCount;

	public Long getCiClassId() {
		return ciClassId;
	}

	public void setCiClassId(Long ciClassId) {
		this.ciClassId = ciClassId;
	}

	public Long[] getCiIds() {
		return ciIds;
	}

	public void setCiIds(Long[] ciIds) {
		this.ciIds = ciIds;
	}

	public Integer getCiCount() {
		return ciCount;
	}

	public void setCiCount(Integer ciCount) {
		this.ciCount = ciCount;
	}

	public String getClassName() {
		return className;
	}

	public void setClassName(String className) {
		this.className = className;
	}
	
	

}
