package com.uinnova.product.eam.model.cj.domain;

import com.uinnova.product.eam.base.cj.BaseEntity;
import lombok.*;

/**
 * 方案批注持久化类
 *
 * <AUTHOR>
 * @since 2022-3-1 20:40:43
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PlanModuleAnnotationEntity extends BaseEntity {

    /**
     * 方案id
     */
    private Long planId;

    /**
     * 章节id
     */
    private Long planChapterId;

    /**
     * 内容块id
     */
    private Long planChapterModuleId;

    /**
     * 批注内容
     */
    private String annotationContent;

    /**
     * 删除标志
     */
    private Boolean delFlag;

    /**
     * 是否归档为问题
     */
    private Boolean problem;

    /**
     * 流程实例id
     */
    private String processInstanceId;

    /**
     * 任务定义主键
     */
    private String taskDefinitionKey;

    /**
     * 如果是回复 此字段表示父类批注id
     */
    private Long parentAnnotationId;
}
