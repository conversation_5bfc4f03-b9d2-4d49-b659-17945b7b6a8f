package com.uinnova.product.eam.web.eam.mvc;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.binary.core.exception.BinaryException;
import com.binary.core.util.BinaryUtils;
import com.binary.framework.exception.ServiceException;
import com.binary.framework.util.ControllerUtils;
import com.binary.framework.web.RemoteResult;
import com.binary.jdbc.Page;
import com.google.common.collect.Lists;
import com.uinnova.product.eam.base.diagram.model.ESDiagram;
import com.uinnova.product.eam.base.util.EamUtil;
import com.uinnova.product.eam.base.util.StaticParamUtil;
import com.uinnova.product.eam.comm.exception.BusinessException;
import com.uinnova.product.eam.model.*;
import com.uinnova.product.eam.model.asset.EamCiRltCopyDTO;
import com.uinnova.product.eam.model.asset.EamCiRltCopyResult;
import com.uinnova.product.eam.model.bm.VcCiQ;
import com.uinnova.product.eam.model.bm.VcCiSearchPage;
import com.uinnova.product.eam.model.dto.CheckCIAttr;
import com.uinnova.product.eam.model.enums.ArtifactType;
import com.uinnova.product.eam.model.enums.FlowSystemType;
import com.uinnova.product.eam.model.vo.ESAttrAggScreenBean;
import com.uinnova.product.eam.model.vo.ESCISearchBeanVO;
import com.uinnova.product.eam.model.vo.ESCiReleAssetSearchBean;
import com.uinnova.product.eam.service.*;
import com.uinnova.product.eam.service.diagram.ESDiagramSvc;
import com.uinnova.product.eam.service.es.IndicatorDetectionInformationAssociationDao;
import com.uinnova.product.eam.web.asset.peer.ArchitectureAssetPeer;
import com.uinnova.product.eam.web.diagram.bean.AttrInfo;
import com.uinnova.product.eam.web.eam.bean.CiGroupAttrsDTO;
import com.uinnova.product.eam.web.eam.bean.CiGroupAttrsVO;
import com.uinnova.product.eam.web.eam.peer.IExportPeer;
import com.uinnova.product.eam.web.integration.bean.VcCiInfo;
import com.uinnova.product.eam.web.integration.bean.VcCiTableInfo;
import com.uinnova.product.eam.web.util.FormatUtil;
import com.uinnova.product.vmdb.comm.bean.QueryPageCondition;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uinnova.product.vmdb.comm.doc.annotation.MvcDesc;
import com.uinnova.product.vmdb.comm.model.ci.CCcCi;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClass;
import com.uinnova.product.vmdb.comm.model.ci.CcCi;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.comm.util.RestTypeUtil;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CiGroupPage;
import com.uinnova.product.vmdb.provider.ci.bean.CiQueryCdt;
import com.uinnova.product.vmdb.provider.search.bean.CcCiSearchPage;
import com.uino.bean.cmdb.base.*;
import com.uino.bean.cmdb.business.*;
import com.uino.bean.cmdb.enums.AttrNameKeyEnum;
import com.uino.bean.cmdb.query.ESAttrBean;
import com.uino.bean.cmdb.query.ESCIClassSearchBean;
import com.uino.bean.cmdb.query.ESCISearchBean;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.permission.enums.PermissionOperateEnum;
import com.uino.dao.cmdb.CiClassProDropSourceDefHelper;
import com.uino.dao.sys.ESDictionaryItemSvc;
import com.uino.init.api.ApiResult;
import com.uino.service.cmdb.microservice.ICIClassSvc;
import com.uino.service.permission.data.TagPermissionProcessor;
import com.uino.util.sys.SysUtil;
import io.micrometer.core.instrument.util.StringUtils;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import net.dreamlu.mica.xss.core.XssCleanIgnore;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.sort.SortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/eam/ci")
@MvcDesc(author = "zjm", desc = "对象管理")
public class EamCiMvc {

    @Autowired
    ICISwitchSvc ciSwitchSvc;
    @Autowired
    IEamCiSvc eamCiSvc;
    @Resource
    IEamCIClassApiSvc ciClassApiSvc;
    @Resource
    IExportPeer exportPeer;
    @Resource
    private EamModelSvc modelSvc;
    @Value("${uino.eam.lib_type.show}")
    String libTypes;
    @Autowired
    private IEamArtifactColumnSvc artifactColumnSvc;
    @Resource
    private ESDiagramSvc diagramApiClient;
    @Autowired
    private ESDictionaryItemSvc dictSvc;
    @Resource
    private ICIClassSvc iciClassSvc;

    @Resource
    private TagPermissionProcessor tagPermissionProcessor;
    @Resource
    ArchitectureAssetPeer assetPeer;

    @Resource
    IndicatorDetectionInformationAssociationDao informationAssociationDao;

    @RequestMapping("/saveOrUpdate")
    @ModDesc(desc = "保存或更新对象信息(ID存在则更新)", pDesc = "对象数据", pType = CcCiInfo.class, rDesc = "1成功0失败", rType =
            Integer.class)
    public void saveOrUpdate(@RequestParam(defaultValue = "DESIGN") LibType libType,
                             HttpServletRequest request, HttpServletResponse response, @RequestBody CcCiInfo ciInfo ) {
        BinaryUtils.checkEmpty(ciInfo.getCi(), "ci");
        if (BinaryUtils.isEmpty(ciInfo.getCi().getOwnerCode())) {
            ciInfo.getCi().setOwnerCode(SysUtil.getCurrentUserInfo().getLoginCode());
        }
        // 校验是否存在架构资产的默认字段
        assetPeer.checkFillAssetDefAttr(ciInfo, libType);
        BinaryUtils.checkEmpty(ciInfo.getCi().getOwnerCode(), "ownerCode");
        BinaryUtils.checkEmpty(ciInfo.getCi().getClassId(), "classId");
        Long id = ciSwitchSvc.saveOrUpdateCI(ciInfo,libType);
        ControllerUtils.returnJson(request, response, id);
    }

    @RequestMapping("/v3/saveOrUpdate")
    @ModDesc(desc = "保存或更新对象信息(ID存在则更新)", pDesc = "对象数据", pType = CcCiInfo.class, rDesc = "1成功0失败", rType =
            Integer.class)
    public void saveOrUpdate(@RequestParam(defaultValue = "DESIGN") LibType libType,
                             @RequestParam(defaultValue = "WRITE") SaveType saveType,
                             HttpServletRequest request, HttpServletResponse response, @RequestBody CcCiInfo ciInfo) {
        BinaryUtils.checkEmpty(ciInfo.getCi(), "ci");
        if (BinaryUtils.isEmpty(ciInfo.getCi().getOwnerCode())) {
            ciInfo.getCi().setOwnerCode(SysUtil.getCurrentUserInfo().getLoginCode());
        }
        BinaryUtils.checkEmpty(ciInfo.getCi().getOwnerCode(), "ownerCode");
        BinaryUtils.checkEmpty(ciInfo.getCi().getClassId(), "classId");
        Long id = ciSwitchSvc.saveOrUpdateCI(ciInfo, libType, saveType);
        ControllerUtils.returnJson(request, response, id);
    }

    @RequestMapping("/v2/saveOrUpdate")
    @XssCleanIgnore  //设置该注解，用于跳过配置得Xss防护
    @ModDesc(desc = "保存或更新对象信息(ID存在则更新)", pDesc = "对象数据", pType = CcCiInfo.class, rDesc = "1成功0失败", rType = Integer.class)
    public void saveOrUpdateV2(@RequestParam(defaultValue = "DESIGN") LibType libType, @RequestParam String diagramId,
                               HttpServletRequest request, HttpServletResponse response, @RequestBody CcCiInfo ciInfo) {
        BinaryUtils.checkEmpty(ciInfo.getCi(), "ci");
        if (BinaryUtils.isEmpty(ciInfo.getCi().getOwnerCode())) {
            ciInfo.getCi().setOwnerCode(SysUtil.getCurrentUserInfo().getLoginCode());
        }
        CcCi ciData = ciInfo.getCi();
        //对存在所属上级元素属性字段的CI，进行自动填充该字段
        modelSvc.fillAttrBelongUpperElement(Lists.newArrayList(ciInfo), diagramId, libType);
        Long id = ciSwitchSvc.saveOrUpdateCI(ciInfo, libType);
        CcCiInfo ciNew = ciSwitchSvc.getCiInfoById(id, libType);
        CiInfoExtend extend = EamUtil.copy(ciNew, CiInfoExtend.class);
        List<ESCIInfo> coverCiList = EamUtil.coverCiInfoList(Collections.singletonList(ciNew));
        Map<String, String> diagramMap = modelSvc.createModelCategory(diagramId, coverCiList, ciData.getOwnerCode(), libType);
        if(!BinaryUtils.isEmpty(diagramMap)){
            extend.setCurrentDiagramId(diagramMap.get(ciNew.getCi().getCiCode()));
        }
        ControllerUtils.returnJson(request, response, extend);
    }

    @RequestMapping("/copyCiListByIds")
    @ModDesc(desc = "保存或更新对象信息(ID存在则更新)", pDesc = "对象数据", pType = CcCiInfo.class, rDesc = "1成功0失败", rType = Integer.class)
    public RemoteResult copyCiListByIds(@RequestBody EamCiRltCopyDTO params) {
        Map<String, ? extends SaveBatchCIContext> contextMap = ciSwitchSvc.copyCiListByIds(params.getCiList(), params.getOwnerCode(), params.getLibType(), params.getPostfix());
        List<ESCIInfo> dbCiList = contextMap.values().stream().map(SaveBatchCIContext::getEsCi).collect(Collectors.toList());
        List<Long> classIds = dbCiList.stream().map(ESCIInfo::getClassId).distinct().collect(Collectors.toList());
        List<ESCIClassInfo> classList = ciClassApiSvc.selectCiClassByIds(classIds);
        Map<Long, ESCIClassInfo> classMap = classList.stream().collect(Collectors.toMap(ESCIClassInfo::getId, each -> each));
        modelSvc.createModelCategory(params.getDiagramId(), dbCiList, params.getOwnerCode(), params.getLibType());
        List<CcCiInfo> result = new ArrayList<>();
        for (ESCIInfo esciInfo : dbCiList) {
            ESCIClassInfo classInfo = classMap.get(esciInfo.getClassId());
            CcCiInfo ciInfo = EamUtil.coverESCIInfo(esciInfo, EamUtil.copy(classInfo.getAttrDefs(), CcCiAttrDef.class), classInfo);
            CiInfoExtend extendCi = EamUtil.copy(ciInfo, CiInfoExtend.class);
            SaveBatchCIContext context = contextMap.get(esciInfo.getCiCode());
            extendCi.setOriginId(context.getOriginCiId());
            extendCi.setOriginCiCode(context.getOriginCiCode());
            result.add(extendCi);
        }
        return new RemoteResult(result);
    }

    @RequestMapping("/copyCiAndRltBatch")
    @ModDesc(desc = "批量复制对象及关系数据", pDesc = "对象及关系数据", pType = String.class, rDesc = "关系及对象信息", rType = Object.class)
    public RemoteResult copyCiAndRltBatch(@RequestBody EamCiRltCopyDTO params) {
        EamCiRltCopyResult result = eamCiSvc.copyCiAndRltBatch(params);
        return new RemoteResult(result);
    }

    @RequestMapping("/v2/saveOrUpdateBatch")
    @ModDesc(desc = "保存或更新对象信息(ID存在则更新)", pDesc = "对象数据", pType = CcCiInfo.class, rDesc = "1成功0失败", rType = Integer.class)
    public void saveOrUpdateBatch(HttpServletRequest request, HttpServletResponse response,
                                  @RequestParam(defaultValue = "DESIGN") LibType libType, @RequestParam String diagramId,
                                  @RequestParam(required = false) String shareUser, @RequestBody List<CcCiInfo> ciList) {
        BinaryUtils.checkEmpty(ciList, "ciList");
        List<Long> classIds = new ArrayList<>();
        Set<String> distinct = new HashSet<>();
        List<CcCiInfo> removeList = new ArrayList<>();
        for (CcCiInfo each : ciList) {
            String ciCode = each.getCi().getCiCode();
            Long classId = each.getCi().getClassId();
            if(BinaryUtils.isEmpty(ciCode) || BinaryUtils.isEmpty(classId)){
                throw new BinaryException("保存的ci数据有误:ciCode="+ciCode+";classId="+classId);
            }
            classIds.add(classId);
            String ciPrimaryKey = each.getCi().getCiPrimaryKey();
            if(ciPrimaryKey !=null && !ciPrimaryKey.equals(ciCode) && distinct.contains(ciPrimaryKey)){
                removeList.add(each);
            }
            distinct.add(ciPrimaryKey);
        }
        ciList.removeAll(removeList);
        Map<String, CcCiInfo> rMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(ciList)){
            //对存在所属上级元素属性字段的CI，进行自动填充该字段
            modelSvc.fillAttrBelongUpperElement(ciList, diagramId, libType);
            //shareUser视图所属用户, 解决协作者自动成图后调用保存ci造成的bug
            String loginCode = shareUser;
            if(BinaryUtils.isEmpty(shareUser)){
                loginCode = SysUtil.getCurrentUserInfo().getLoginCode();
            }
            String ownerCode = ciList.get(0).getCi().getOwnerCode();
            if (BinaryUtils.isEmpty(ownerCode)){
                ownerCode = loginCode;
            }
            List<ESCIInfo> coverCiList = EamUtil.coverCiInfoList(ciList);
            Map<String, ? extends SaveBatchCIContext> contextMap = ciSwitchSvc.saveOrUpdateBatchCI(coverCiList, classIds, ownerCode, loginCode, libType);
            List<ESCIInfo> dbCiList = contextMap.values().stream().map(SaveBatchCIContext::getEsCi).collect(Collectors.toList());
            Map<String, String> relIdMap = modelSvc.createModelCategory(diagramId, dbCiList, ownerCode, libType);
            List<ESCIClassInfo> classList = ciClassApiSvc.selectCiClassByIds(classIds);
            Map<Long, ESCIClassInfo> classMap = classList.stream().collect(Collectors.toMap(ESCIClassInfo::getId, each -> each));
            for (SaveBatchCIContext ciContext : contextMap.values()) {
                ESCIInfo ciInfo = ciContext.getEsCi();
                ESCIClassInfo classInfo = classMap.get(ciInfo.getClassId());
                CcCiInfo ccCiInfo = EamUtil.coverESCIInfo(ciInfo, EamUtil.copy(classInfo.getAttrDefs(), CcCiAttrDef.class), classInfo);
                CiInfoExtend extendCi = EamUtil.copy(ccCiInfo, CiInfoExtend.class);
                extendCi.setCurrentDiagramId(relIdMap.get(ciInfo.getCiCode()));
                rMap.put(ciContext.getCiCode(), extendCi);
            }
        }
        ControllerUtils.returnJson(request, response, rMap);
    }

    @PostMapping("/enableRemoveCiFromView")
    @Deprecated
    public void enableRemoveCiFromView(HttpServletRequest request, HttpServletResponse response, @RequestBody EamCategoryCdt removeData) {
        BinaryUtils.checkEmpty(removeData.getCiCodes(), "ciCode");
        boolean success = eamCiSvc.enableRemoveCiFromView(removeData.getCiCodes(), removeData.getDiagramId(), removeData.getOwnerCode(), removeData.getLibType());
        ControllerUtils.returnJson(request, response, success);
    }

    @ModDesc(desc = "通过ID查询对象信息", pDesc = "对象的id", pType = Long.class, rDesc = "对象信息", rType = CcCiInfo.class)
    @RequestMapping("/queryById")
    public void queryById(@RequestParam(defaultValue = "DESIGN") LibType libType,
                          HttpServletRequest request, HttpServletResponse response, @RequestParam Long id) {
        //Long id = Long.parseLong(body.trim());
        CcCiInfo result = ciSwitchSvc.getCiInfoById(id, libType);
        ControllerUtils.returnJson(request, response, result);
    }

    @PostMapping(value = "/version/queryByCode")
    @ModDesc(desc = "通过code查询对象信息(设计库按版本)", pDesc = "对象分类id", pType = Long.class, rDesc = "分类信息", rType = Object.class)
    public RemoteResult queryByCode(@RequestBody CiInfoQueryVo query) {
        CcCiInfo res = ciSwitchSvc.queryByCode(query);
        return new RemoteResult(res);
    }

    @ModDesc(desc = "通过code查询对象信息", pDesc = "对象的code", pType = String.class, rDesc = "对象信息", rType = CcCiInfo.class)
    @RequestMapping("/queryByCode")
    public void queryByCode(@RequestParam(defaultValue = "DESIGN") LibType libType,
                            HttpServletRequest request, HttpServletResponse response, @RequestBody JSONObject body) {
        BinaryUtils.checkEmpty(body.getString("ciCode"), "ciCode");
        CCcCi cdt = new CCcCi();
        cdt.setCiCode(body.getString("ciCode"));
        List<CcCiInfo> result = ciSwitchSvc.queryCiInfoList(SysUtil.getCurrentUserInfo().getDomainId(), cdt, null, false, true, libType);
        if (!BinaryUtils.isEmpty(result)) {
            ControllerUtils.returnJson(request, response, result.get(0));
        } else {
            ControllerUtils.returnJson(request, response, new JSONObject());
        }
    }


    @RequestMapping("/queryPageByIndex")
    @ModDesc(desc = "分页查询对象数据", pDesc = "条件查询", pType = QueryPageCondition.class, pcType = CiQueryCdtExtend.class, rDesc = "对象数据", rType = CiGroupPage.class)
    public void queryPageByIndex(@RequestParam(defaultValue = "DESIGN") LibType libType,
                                 HttpServletRequest request, HttpServletResponse response, @RequestBody String body) {
        QueryPageCondition<CiQueryCdtExtend> pageCdt = RestTypeUtil.toPageCondition(body, CiQueryCdtExtend.class);
        CiGroupPage result = ciSwitchSvc.queryPageByIndex(pageCdt.getPageNum(), pageCdt.getPageSize(), pageCdt.getCdt(), false, libType);
        ControllerUtils.returnJson(request, response, result);
    }

    @RequestMapping("/queryPageBySearchBean")
    @ModDesc(desc = "分页查询对象数据-支持属性排序", pDesc = "条件查询", pType = QueryPageCondition.class, pcType =
            ESCISearchBean.class, rDesc = "对象数据", rType = CiGroupPage.class)
    public void queryPageBySearchBean(@RequestParam(defaultValue = "DESIGN") LibType libType,
                                      HttpServletRequest request, HttpServletResponse response,
                                      @RequestBody ESCISearchBeanVO bean) {
        isConvertibleToLong(bean);
        if (!CollectionUtils.isEmpty(bean.getClassCodes())) {
            List<CcCiClassInfo> classInfos = ciClassApiSvc.getByClassCodes(bean.getClassCodes(), SysUtil.getCurrentUserInfo().getDomainId());
            if (CollectionUtils.isEmpty(classInfos) || classInfos.size() < bean.getClassCodes().size()) {
                throw new BinaryException("分类信息不存在");
            }
            List<Long> classIds = classInfos.stream().map(e -> e.getCiClass().getId()).collect(Collectors.toList());
            bean.getClassIds().addAll(classIds);
        }
        CiGroupPage result = ciSwitchSvc.queryPageBySearchBeanVO(bean, false, libType);
        ControllerUtils.returnJson(request, response, result);
    }

    @RequestMapping("/queryPageBySearchBeanV2")
    @ModDesc(desc = "根据分类标识分页查询对象数据-支持属性排序", pDesc = "条件查询", pType = QueryPageCondition.class, pcType =
            ESCISearchBean.class, rDesc = "对象数据", rType = CiGroupPage.class)
    public void queryPageBySearchBeanV2(@RequestParam(defaultValue = "DESIGN") LibType libType,
                                      HttpServletRequest request, HttpServletResponse response,
                                      @RequestBody ESCISearchBeanVO bean) {
        isConvertibleToLong(bean);
        if (!CollectionUtils.isEmpty(bean.getClassCodes())) {
            List<CcCiClassInfo> classInfos = ciClassApiSvc.getByClassCodes(bean.getClassCodes(), SysUtil.getCurrentUserInfo().getDomainId());
            if (CollectionUtils.isEmpty(classInfos) || classInfos.size() < bean.getClassCodes().size()) {
                throw new BinaryException("分类信息不存在");
            }
            List<Long> classIds = classInfos.stream().map(e -> e.getCiClass().getId()).collect(Collectors.toList());
            bean.getClassIds().addAll(classIds);
        }
        CiGroupPage result = ciSwitchSvc.queryPageBySearchBeanVO(bean, true, libType);
        ControllerUtils.returnJson(request, response, result);

    }

    private void isConvertibleToLong(ESCISearchBeanVO esciSearchBeanVO) {
        if (StringUtils.isNotBlank(esciSearchBeanVO.getGteTime())) {
            String gteTime = esciSearchBeanVO.getGteTime().replace("/", "");
            try {
                Long.parseLong(gteTime);
            } catch (NumberFormatException e) {
                throw new BusinessException("时间区间查询 大于等于传参有问题请核实在传入");
            }
        }
        if (StringUtils.isNotBlank(esciSearchBeanVO.getLteTime())) {
            String lteTime = esciSearchBeanVO.getLteTime().replace("/", "");
            try {
                Long.parseLong(lteTime);
            } catch (NumberFormatException e) {
                throw new BusinessException("时间区间查询 大于等于传参有问题请核实在传入");
            }
        }
        if (StringUtils.isNotBlank(esciSearchBeanVO.getGteTime()) && StringUtils.isNotBlank(esciSearchBeanVO.getLteTime())) {
            String gteTime = esciSearchBeanVO.getGteTime().replace("/", "");
            String lteTime = esciSearchBeanVO.getLteTime().replace("/", "");
            if (Long.parseLong(gteTime) > Long.parseLong(lteTime)) {
                throw new BusinessException("时间区间查询 起始时间不能大于终止时间");
            }
        }
    }

    @PostMapping("/queryClassMap")
    @ResponseBody
    public RemoteResult queryClassMap(@RequestParam(defaultValue = "DESIGN") LibType libType,@RequestBody ESCISearchBean bean) {
        CiGroupPage result = ciSwitchSvc.queryPageBySearchBean(bean, false, libType);
        List<CcCiInfo> data = result.getData();
        if (data != null && data.size() > 0) {
            return new RemoteResult(classListToMap(data));
        }
        return new RemoteResult("");
    }

    private  Map<Long, List<CcCiInfo>> classListToMap(List<CcCiInfo> data) {
        Map<Long, List<CcCiInfo>> resultMap = data.stream().collect(Collectors.groupingBy(item -> item.getCi().getClassId()));
        return resultMap;
    }

    @ResponseBody
    @PostMapping("/getCiList")
    @ModDesc(desc = "分页查询对象数据-支持属性排序", pDesc = "条件查询", pType = QueryPageCondition.class, pcType =
            ESCISearchBean.class, rDesc = "对象数据", rType = CiGroupPage.class)
    public CiGroupPage getCiList(@RequestParam(defaultValue = "DESIGN") LibType libType,
                                 @RequestBody ESCISearchBean bean) {
        return ciSwitchSvc.queryPageBySearchBean(bean, false, libType);
    }

    @PostMapping("/findCiSwitchUserList")
    @ResponseBody
    public RemoteResult findCiSwitchUserList(@RequestBody ESCISearchBean bean) {
        List<SysUser> ciSwitchUsers = ciSwitchSvc.findCiSwitchUserList(bean, false);
        return new RemoteResult(ciSwitchUsers);
    }

    @PostMapping("/queryCiInfoBySearchBean")
    @ModDesc(desc = "分页查询对象数据-支持属性排序", pDesc = "条件查询", pType = QueryPageCondition.class, pcType = ESCISearchBean.class, rDesc = "对象数据", rType = CiGroupPage.class)
    public void queryCiInfoBySearchBean(@RequestParam(defaultValue = "DESIGN") LibType libType,
                                        HttpServletRequest request, HttpServletResponse response, @RequestBody FakeSmartCiQuery bean) {
        bean.setDiagramId(null);
        if(LibType.PRIVATE.equals(libType)){
            bean.setPermission(true);
        }
        if (!CollectionUtils.isEmpty(bean.getAndAttrs())){
            for (ESAttrBean item: bean.getAndAttrs()) {
                if (BinaryUtils.isEmpty(item.getValue())) {
                    item.setValue(item.getKey());
                    item.setOptType(11);
                }
            }
        }
        bean.setQueryTag(!bean.getAiAssistant());
        CiGroupPage result = ciSwitchSvc.queryPageBySearchBean(bean, false, libType);
        ControllerUtils.returnJson(request, response, result);
    }
    @PostMapping("/queryCiGroupByAttrs")
    @ModDesc(desc = "在某个分类下，根据ci枚举/字典属性分组查询ci", pDesc = "条件查询", pType = QueryPageCondition.class, pcType = CiGroupAttrsDTO.class, rDesc = "对象数据", rType = CiGroupAttrsVO.class)
    public void queryCiGroupByAttrs(@RequestParam(defaultValue = "DESIGN") LibType libType,
                                    HttpServletRequest request, HttpServletResponse response, @RequestBody CiGroupAttrsDTO bean) {
        List<CiGroupAttrsVO> result = getCiGroupAttrsVO(bean, libType);
        ControllerUtils.returnJson(request, response, result);
    }
    @PostMapping("/queryCiGroupByAttrsV2")
    @ModDesc(desc = "在某个分类下，根据ci枚举/字典属性分组查询ci", pDesc = "条件查询", pType = QueryPageCondition.class, pcType = CiGroupAttrsDTO.class, rDesc = "对象数据", rType = CiGroupAttrsVO.class)
    public void queryCiGroupByAttrsV2(@RequestParam(defaultValue = "DESIGN") LibType libType,
                                      HttpServletRequest request, HttpServletResponse response, @RequestBody CiGroupAttrsDTO bean) {
        List<CiGroupAttrsVO> result = getCiGroupAttrsVOV2(bean, libType);
        ControllerUtils.returnJson(request, response, result);
    }

    private List<CiGroupAttrsVO> getCiGroupAttrsVOV2(CiGroupAttrsDTO bean, LibType libType) {
        //条件查询数据
        List<CiGroupAttrsVO> filterList = bean.getFilterList();
        List<ESAttrBean> esAttrBeans = new ArrayList<>();
        String groupField = bean.getGroupField();
        if (!CollectionUtils.isEmpty(filterList)) {
            for (CiGroupAttrsVO item : filterList) {
                ESAttrBean esAttrBean = new ESAttrBean();
                if (BinaryUtils.isEmpty(item.getGroupValue())) {
                    esAttrBean.setKey(item.getGroupField());
                    esAttrBean.setValue(item.getGroupField());
                    esAttrBean.setOptType(11);
                } else {
                    esAttrBean.setKey(item.getGroupField());
                    esAttrBean.setValue(item.getGroupValue());
                    esAttrBean.setOptType(1);
                }
                esAttrBeans.add(esAttrBean);
            }
        }
        bean.setAndAttrs(esAttrBeans);

        if (!BinaryUtils.isEmpty(bean.getDiagramId())) {//获取分类标签权限规则集id
            List<Long> tagIds = tagPermissionProcessor.getTagIds(bean.getClassIds(), SysUtil.getCurrentUserInfo().getId(), PermissionOperateEnum.VIEW);
            bean.setTagIds(tagIds);
        }
        CiGroupPage ciGroupList = this.getCiGroupList(bean, libType);
        //获取字典、枚举字段后整理数据并返回
        List<CcCiInfo> data = ciGroupList.getData();
        List<Map<String, String>> attrsList = data.stream().map(CcCiInfo::getAttrs).collect(Collectors.toList());
        Map<String, List<Map<String, String>>> collect = attrsList.stream().collect(Collectors.groupingBy(item -> item.get(groupField)));
        List<CiGroupAttrsVO> result = new ArrayList<>();
        List<String> groupValues = getGroupValues(bean);//分组名称
        int otherCount = 0;
        for (String groupValue : groupValues) {
            //符合字典项和枚举字段的分组情况
            CiGroupAttrsVO ciGroupAttrsVO = new CiGroupAttrsVO();
            ciGroupAttrsVO.setGroupField(bean.getGroupField());
            ciGroupAttrsVO.setGroupValue(groupValue);
            ciGroupAttrsVO.setCount(CollectionUtils.isEmpty(collect.get(groupValue)) ? 0 : (long) collect.get(groupValue).size());
            collect.remove(groupValue);
            result.add(ciGroupAttrsVO);
        }
        if (!CollectionUtils.isEmpty(collect)) {
            //不在字典项、枚举列表中的数据分组情况
            for (String item : collect.keySet()) {
                if (BinaryUtils.isEmpty(item)) {
                    otherCount = otherCount + collect.get(item).size();
                    continue;
                }
                CiGroupAttrsVO ciGroupAttrsVO = new CiGroupAttrsVO();
                ciGroupAttrsVO.setGroupField(bean.getGroupField());
                ciGroupAttrsVO.setGroupValue(item);
                ciGroupAttrsVO.setCount(CollectionUtils.isEmpty(collect.get(item)) ? 0 : (long) collect.get(item).size());
                result.add(ciGroupAttrsVO);
            }
        }
        //空值情况
        CiGroupAttrsVO ciGroupAttrsVO = new CiGroupAttrsVO();
        ciGroupAttrsVO.setGroupField(bean.getGroupField());
        ciGroupAttrsVO.setGroupValue("");
        ciGroupAttrsVO.setCount((long) otherCount);
        result.add(ciGroupAttrsVO);
        return result;
    }

    private List<String> getGroupValues(CiGroupAttrsDTO bean){
        Long classId = bean.getClassIds().get(0);
        ESCIClassInfo classInfo = ciClassApiSvc.queryClassById(classId);
        ESCIAttrDefInfo attrDefInfo = this.getAttrDefByField(classInfo, bean.getGroupField());
        bean.setGroupField(attrDefInfo.getProName());
        List<String> groupValues;
        if (AttrNameKeyEnum.ENUM.getType() == attrDefInfo.getProType()){
            String enumValues = attrDefInfo.getEnumValues();
            groupValues = JSONArray.parseArray(enumValues, String.class);
            log.info("enumValues：" + enumValues);
        } else if (AttrNameKeyEnum.DICT.getType() == attrDefInfo.getProType()) {
            String sourceDef = attrDefInfo.getProDropSourceDef();
            if(BinaryUtils.isEmpty(sourceDef)){
                throw new ServiceException("数据字典类型约束不可为空!");
            }
            Long dictClassId = CiClassProDropSourceDefHelper.getCiClassProDropSourceClassId(sourceDef.trim());
            Long[] dictDefIds = CiClassProDropSourceDefHelper.getCiClassProDropSourceDefIds(sourceDef.trim());
            groupValues = dictSvc.getAttrValues(dictClassId, dictDefIds);
            log.info("sourceDef：" + sourceDef);
        } else {
            throw new ServiceException("分类属性只支持枚举、数据字典!");
        }
        return groupValues;
    }

    private List<CiGroupAttrsVO> getCiGroupAttrsVO(CiGroupAttrsDTO bean, LibType libType) {
        List<CiGroupAttrsVO> result = new ArrayList<>();
        if(!BinaryUtils.isEmpty(bean.getGroupField())){
            if(bean.getClassIds() != null && bean.getClassIds().size() == 1){
                Long classId = bean.getClassIds().get(0);
                ESCIClassInfo classInfo = ciClassApiSvc.queryClassById(classId);
                ESCIAttrDefInfo attrDefInfo = this.getAttrDefByField(classInfo, bean.getGroupField());
                bean.setGroupField(attrDefInfo.getProName());
                List<String> groupValues;
                if (AttrNameKeyEnum.ENUM.getType() == attrDefInfo.getProType()){
                    String enumValues = attrDefInfo.getEnumValues();
                    groupValues = JSONArray.parseArray(enumValues, String.class);
                    log.info("enumValues：" + enumValues);
                } else if (AttrNameKeyEnum.DICT.getType() == attrDefInfo.getProType()) {
                    String sourceDef = attrDefInfo.getProDropSourceDef();
                    if(BinaryUtils.isEmpty(sourceDef)){
                        throw new ServiceException("数据字典类型约束不可为空!");
                    }
                    Long dictClassId = CiClassProDropSourceDefHelper.getCiClassProDropSourceClassId(sourceDef.trim());
                    Long[] dictDefIds = CiClassProDropSourceDefHelper.getCiClassProDropSourceDefIds(sourceDef.trim());
                    groupValues = dictSvc.getAttrValues(dictClassId, dictDefIds);
                    log.info("sourceDef：" + sourceDef);
                } else {
                    throw new ServiceException("分类属性只支持枚举、数据字典!");
                }

                bean.setPageSize(1);
                for (String groupValue:groupValues) {
                    CiGroupAttrsVO ciGroupAttrsVO = getCiGroupAttrsVO(bean, libType, groupValue, 1);
                    result.add(ciGroupAttrsVO);
                }
                //查询为空的
                CiGroupAttrsVO ciGroupAttrsVO = getCiGroupAttrsVO(bean, libType, "空字符串", 10);
                result.add(ciGroupAttrsVO);
                log.info("查询分类信息结束：" + classInfo.getClassName());
            } else {
                throw new ServiceException("根据属性分组时，classId有且只能有一个!");
            }
        } else {
            throw new ServiceException("分组属性名称不能为空！");
        }
        return result;
    }

    private CiGroupAttrsVO getCiGroupAttrsVO(CiGroupAttrsDTO bean, LibType libType, String groupValue, int optType) {
        ESAttrBean esAttrBean = new ESAttrBean();
        esAttrBean.setKey(bean.getGroupField());
        esAttrBean.setValue(groupValue);
        esAttrBean.setOptType(optType);
        List<ESAttrBean> esAttrBeans = new ArrayList<>();
        esAttrBeans.add(esAttrBean);
        bean.setAndAttrs(esAttrBeans);
        CiGroupPage ciGroupList = this.getCiGroupList(bean, libType);
        CiGroupAttrsVO ciGroupAttrsVO = new CiGroupAttrsVO();
        ciGroupAttrsVO.setGroupField(bean.getGroupField());
        if("空字符串".equals(groupValue)){
            ciGroupAttrsVO.setGroupValue("");
        } else {
            ciGroupAttrsVO.setGroupValue(groupValue);
        }
        ciGroupAttrsVO.setCount(ciGroupList.getTotalCiCount());
        return ciGroupAttrsVO;
    }

    private ESCIAttrDefInfo getAttrDefByField(ESCIClassInfo classInfo, String groupField) {
        if(classInfo != null && classInfo.getAttrDefs() != null){
            for (ESCIAttrDefInfo attrDefInfo:classInfo.getAttrDefs()) {
                if(groupField.equals(attrDefInfo.getProStdName())) {
                    return attrDefInfo;
                }
            }
        }
        throw new ServiceException("未获取到属性类型：" + groupField);
    }

    @PostMapping("/queryReleCiInfo")
    @ModDesc(desc = "分页查询对象数据-支持属性排序,添加数据筛选", pDesc = "条件查询", pType = QueryPageCondition.class, pcType = ESCiReleAssetSearchBean.class, rDesc = "对象数据", rType = CiGroupPage.class)
    public void queryReleCiInfo(@RequestParam(defaultValue = "DESIGN") LibType libType,
                                HttpServletRequest request, HttpServletResponse response, @RequestBody ESCiReleAssetSearchBean bean) {

        CiGroupPage result = ciSwitchSvc.queryReleCiInfo(bean, libType);
        Page<Map> page = new Page<>();
        page.setPageNum(result.getPageNum());
        page.setPageSize(result.getPageSize());
        page.setTotalPages(result.getTotalPages());
        page.setTotalRows(result.getTotalRows());
        List<Map> data = new ArrayList<>();
        result.getData().forEach(e->{
            HashMap<String, String> ciInfo = new HashMap<>();
            ciInfo.put("ciCode", e.getCi().getCiCode());
            String ciLabel = e.getCi().getCiLabel();
            if (StringUtils.isEmpty(ciLabel)||"[]".equals(ciLabel)){
                String ciPrimaryKey = e.getCi().getCiPrimaryKey();
                List<String> ciPrimaryList = JSONObject.parseArray(ciPrimaryKey, String.class);
                ciPrimaryList.remove(0);
                ciInfo.put("primary", ciPrimaryList.stream().collect(Collectors.joining("|")));
            }else{
                List<String> ciPrimaryList = JSONObject.parseArray(ciLabel, String.class);
                ciInfo.put("primary", ciPrimaryList.stream().collect(Collectors.joining("|")));
            }
            data.add(ciInfo);
        });
        page.setData(data);
        ControllerUtils.returnJson(request, response, page);
    }

    @RequestMapping("/queryCiInfoPage")
    @ModDesc(desc = "分页查询对象数据", pDesc = "条件查询", pType = QueryPageCondition.class, pcType = CCcCi.class, rDesc = "对象数据", rType = Page.class)
    public void queryCiInfoPage(@RequestParam(defaultValue = "DESIGN") LibType libType,
                                HttpServletRequest request, HttpServletResponse response, @RequestBody String body) {
        QueryPageCondition<CCcCi> pageCdt = RestTypeUtil.toPageCondition(body, CCcCi.class);
        Page<CcCiInfo> result = ciSwitchSvc.queryCiInfoPage(1L, pageCdt.getPageNum(), pageCdt.getPageSize(), pageCdt.getCdt(), pageCdt.getOrders(), false, true, libType);
        ControllerUtils.returnJson(request, response, result);
    }

    @RequestMapping(value = "/importCi", method = RequestMethod.POST)
    @ModDesc(desc = "通过XLS或者XLSX导入CI信息", pDesc = "XLS或者XLSX文件", rDesc = "导入明细", rType = ImportResultMessage.class)
    public void asyncImportCi(@RequestParam(defaultValue = "DESIGN") LibType libType,
                              HttpServletRequest request, HttpServletResponse response,
                              @RequestParam("file") MultipartFile file) {
        BinaryUtils.checkEmpty(request.getParameter("ciClassIds"), "ciClassIds");
        Long classId = Long.parseLong(request.getParameter("ciClassIds"));
        ImportResultMessage result = ciSwitchSvc.importCiByCiClsIds(file, classId, libType);
        ControllerUtils.returnJson(request, response, result);
    }

    @RequestMapping("/exportCi")
    @ModDesc(desc = "导出对象数据(hasData:0只导出分类及属性(默认),1:导出分类加数据;ciClassIds:分类的ID多个用','隔开,不传导出所有分类)", pDesc = "?hasData=1&ciClassIds=1", pType = String.class, rDesc = "导出全部CI", rType = String.class)
    public ResponseEntity<byte[]> exportCi(@RequestParam(defaultValue = "DESIGN") LibType libType, @RequestBody ExportCiDto exportDto) {
        return ciSwitchSvc.exportCiOrClass(exportDto, libType);
    }

    @GetMapping("/exportCiByDiagramId")
    @ModDesc(desc = "导出当前视图中的CI数据", pDesc = "?diagramId=1", pType = Long.class, rDesc = "导出当前视图中CI数据", rType = String.class)
    public ResponseEntity<byte[]> exportCiByDiagramId(@RequestParam(defaultValue = "DESIGN") LibType libType,
                                                      @RequestParam(name = "diagramId") Long diagramId) {
        return exportPeer.exportCiByDiagramId(diagramId, libType);
    }

    @RequestMapping(value = "/importCiExcelBatch", method = RequestMethod.POST)
    @ModDesc(desc = "一键导入解析Excel文件", pDesc = "XLS或者XLSX文件", pType = MultipartFile.class, rDesc = "文件解析内容", rType = ImportExcelMessage.class)
    public void importCiExcelBatch(@RequestParam(defaultValue = "DESIGN") LibType libType,
                                   HttpServletRequest request, HttpServletResponse response,
                                   @RequestParam("file") MultipartFile file) {
        ImportExcelMessage result = ciSwitchSvc.importCiExcel(file, libType);
        ControllerUtils.returnJson(request, response, result);
    }

    @RequestMapping(value = "/importCiByClassBatch", method = RequestMethod.POST)
    @ModDesc(desc = "一键导入批量导入分类数据", pDesc = "导入对象", pType = CiExcelInfoDto.class, rDesc = "导入明细", rType = ImportResultMessage.class)
    public void importCiByClassBatch(@RequestParam(defaultValue = "DESIGN") LibType libType,
                                     HttpServletRequest request, HttpServletResponse response,
                                     @RequestBody CiExcelInfoDto excelInfoDto) {
        ImportResultMessage result = ciSwitchSvc.importCiByClassBatch(excelInfoDto, libType);
        ControllerUtils.returnJson(request, response, result);
    }

    @GetMapping("/removeById")
    @ModDesc(desc = "通过ID删除对象", pDesc = "对象的ID", pType = Long.class, rDesc = "1成功0失败", rType = Integer.class)
    public void removeById(@RequestParam(defaultValue = "DESIGN") LibType libType,
                           HttpServletRequest request, HttpServletResponse response, @RequestParam Long id) {
        //long id = Long.parseLong(body.trim());
        //删除指标下的检测信息
        CcCiInfo ciInfoById = ciSwitchSvc.getCiInfoById(id, libType);
        if (ciInfoById.getCiClass().getClassCode().equals("指标")) {
            if (libType.equals("DESIGN")) {
                informationAssociationDao.deleteByQuery(QueryBuilders.termQuery("ciCode.keyword", ciInfoById.getCi().getCiCode()), true);
            } else {
                BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery()
                        .filter(QueryBuilders.termQuery("ciCode.keyword", ciInfoById.getCi().getCiCode()))
                        .filter(QueryBuilders.termQuery("creator.keyword", SysUtil.getCurrentUserInfo().getLoginCode()));
                informationAssociationDao.deleteByQuery(queryBuilder, true);
            }
        }
        Integer integer = ciSwitchSvc.removeById(id, 1L, libType);
        ControllerUtils.returnJson(request, response, integer);
    }

    @RequestMapping("/removeByIds")
    @ModDesc(desc = "通过ID批量删除对象", pDesc = "对象的ID集合", pType = List.class, pcType = Long.class, rDesc = "1成功0失败", rType = Integer.class)
    public void removeByIds(@RequestParam(defaultValue = "DESIGN") LibType libType,
                            HttpServletRequest request, HttpServletResponse response, @RequestBody List<Long> ciIds) {
        //删除指标下的检测信息
        ESCISearchBean bean = new ESCISearchBean();
        bean.setIds(ciIds);
        bean.setPageNum(1);
        bean.setPageSize(10000);
        bean.setDomainId(SysUtil.getCurrentUserInfo().getDomainId());
        Page<ESCIInfo> page = ciSwitchSvc.searchESCIByBean(bean, libType);
        if (CollectionUtils.isEmpty(page.getData())) {
            throw new BinaryException("参数未获取到数据");
        }
        List<ESCIInfo> ciByIds = page.getData();
        // List<ESCIInfo> ciByIds = ciSwitchSvc.getCiByIds(ciIds, SysUtil.getCurrentUserInfo().getLoginCode(), libType);
        CcCiClassInfo ccCiClassInfo = iciClassSvc.queryClassInfoById(ciByIds.get(0).getClassId());
        if (ccCiClassInfo.getCiClass().getClassCode().equals("指标")) {
            List<String> collect = ciByIds.stream().map(c -> c.getCiCode()).collect(Collectors.toList());
            if (libType.equals("DESIGN")){
                informationAssociationDao.deleteByQuery(QueryBuilders.termsQuery("ciCode.keyword", collect),true);
            }else{
                // 将ciByIds数据根据ciCode分组
                Map<String, List<ESCIInfo>> ciInfoByOwner = ciByIds.stream().collect(Collectors.groupingBy(ESCIInfo::getOwnerCode));
                BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
                // 遍历ciInfoByOwner
                for (Map.Entry<String, List<ESCIInfo>> entry : ciInfoByOwner.entrySet()) {
                    String ownerCode = entry.getKey();
                    List<ESCIInfo> ciList = entry.getValue();
                    List<String> ciCodeByOwner = ciList.stream().map(c -> c.getCiCode()).collect(Collectors.toList());
                    BoolQueryBuilder oneQueryBuilder = QueryBuilders.boolQuery()
                            .filter(QueryBuilders.termsQuery("ciCode.keyword", ciCodeByOwner))
                            .filter(QueryBuilders.termQuery("creator.keyword", ownerCode));
                    queryBuilder.should(oneQueryBuilder);
                }
                informationAssociationDao.deleteByQuery(queryBuilder,true);
            }
        }
        Integer result = ciSwitchSvc.removeByIds(ciIds, 1L, libType);
        ControllerUtils.returnJson(request, response, result);
    }

    @RequestMapping("/removeByClassId")
    @ModDesc(desc = "通过分类ID删除相关的对象数据", pDesc = "对象分类的ID", pType = Long.class, rDesc = "1成功0失败", rType = Integer.class)
    public void removeByClassId(@RequestParam(defaultValue = "DESIGN") LibType libType,
                                HttpServletRequest request, HttpServletResponse response, @RequestBody String body) {
        long id = Long.parseLong(body.trim());
        Integer integer = ciSwitchSvc.removeByClassId(id, 1L, libType);
        ControllerUtils.returnJson(request, response, integer);
    }

    @RequestMapping("/removeByOwnerCode")
    @ModDesc(desc = "根据用户删除相关分类的对象数据", pDesc = "对象分类的ID", pType = Long.class, rDesc = "1成功0失败", rType = Integer.class)
    public void removeByOwnerCodeAndClassId(
            @RequestParam(defaultValue = "DESIGN") LibType libType,
            HttpServletRequest request, HttpServletResponse response, @RequestBody String body) {
        JSONObject json = JSON.parseObject(body);
        Long classId ;
        if (json.containsKey("classId")) {
            classId = json.getLong("classId");
        } else {
            throw new ServiceException("classId不能为空");
        }
        String ownerCode = null;
        if (json.containsKey("ownerCode")) {
            ownerCode = json.getString("ownerCode");
        }
        Integer integer = ciSwitchSvc.removeByOwnerCodeAndClassId(libType, classId, ownerCode);
        ControllerUtils.returnJson(request, response, integer);
    }

    @RequestMapping("/searchCIByBean")
    @ModDesc(desc = "条件搜索对象数据", pDesc = "查询条件", pType = ESCISearchBean.class, rDesc = "对象数据", rType = CcCiSearchPage.class)
    public void searchCIByBean(@RequestParam(defaultValue = "DESIGN") LibType libType,
                               @RequestBody ESCISearchBean bean, HttpServletRequest request,
                               HttpServletResponse response) {
        Long domainId = SysUtil.getCurrentUserInfo().getDomainId();
        bean.setDomainId(domainId);
        if(!LibType.PRIVATE.equals(libType)){
            bean.setOwnerCode(null);
        }else {
            if (bean.getOwnerCode() == null) {
                bean.setOwnerCode(SysUtil.getCurrentUserInfo().getLoginCode());
            }
        }
        bean.setDiagramId(null);
        CcCiSearchPage res = ciSwitchSvc.searchCIByBean(bean, libType);
        ControllerUtils.returnJson(request, response, res);
    }

    @RequestMapping("/queryCiInfoList")
    @ModDesc(desc = "分页查询对象数据", pDesc = "条件查询", pType = QueryPageCondition.class, pcType = CCcCi.class, rDesc = "对象数据", rType = List.class)
    public void queryCiInfoList(@RequestParam(defaultValue = "DESIGN") LibType libType,
                                HttpServletRequest request, HttpServletResponse response, @RequestBody String body) {

        QueryPageCondition<CCcCi> pageCdt = RestTypeUtil.toPageCondition(body, CCcCi.class);

        // 接口传参的ciCodes[]为空 会导致searchESCIByBean方法查询用户全量 做个处理 这个接口将不支持除了ciCodes以外的字段去查询
        List<CcCiInfo> ciInfos = new ArrayList<>();
        if (!BinaryUtils.isEmpty(pageCdt.getCdt().getCiCodes())) {
            ESCISearchBean bean = new ESCISearchBean();
            bean.setPageNum(1);
            bean.setPageSize(3000);
            bean.setCdt(pageCdt.getCdt());
            bean.setDomainId(SysUtil.getCurrentUserInfo().getDomainId());
            if (LibType.PRIVATE == libType) {
                JSONObject params = JSONObject.parseObject(body);
                String ownerCode = params.getString("ownerCode");
                if (BinaryUtils.isEmpty(ownerCode)) {
                    ownerCode = SysUtil.getCurrentUserInfo().getLoginCode();
                }
                bean.setOwnerCode(ownerCode);
                if (!"all".equals(pageCdt.getCdt().getCustom1())) {
                    bean.setPermission(true);
                }
                bean.getCdt().setCustom1(null);
            }
            Page<ESCIInfo> page = ciSwitchSvc.searchESCIByBean(bean, libType);
            Set<Long> classIds = page.getData().stream().map(ESCIInfo::getClassId).collect(Collectors.toSet());

            List<ESCIClassInfo> esciClassInfos = ciClassApiSvc.selectCiClassByIds(new ArrayList<>(classIds));
            Map<Long, ESCIClassInfo> classMap = esciClassInfos.stream().collect(Collectors.toMap(ESCIClassInfo::getId, each -> each));
            Map<Long, List<ESCIAttrDefInfo>> classAttrMap = new HashMap<>();
            esciClassInfos.forEach(e -> {
                classAttrMap.put(e.getId(), e.getAttrDefs());
            });

            for (ESCIInfo ciInfo : page.getData()) {
                ESCIClassInfo classInfo = classMap.get(ciInfo.getClassId());
                CcCiInfo ci = EamUtil.coverESCIInfoOptimize(ciInfo, EamUtil.copy(classAttrMap.get(ciInfo.getClassId()), CcCiAttrDef.class), classInfo);
                ci.setAttrDefs(new ArrayList<>(classAttrMap.get(ciInfo.getClassId())));
                ciInfos.add(ci);
            }
        }
        ControllerUtils.returnJson(request, response, ciInfos);
    }

    @RequestMapping("/queryCiInfoListByCiCodes")
    @ModDesc(desc = "分页查询对象数据", pDesc = "条件查询", pType = QueryPageCondition.class, pcType = CCcCi.class, rDesc = "对象数据", rType = List.class)
    public void queryCiInfoListByCiCodes(@RequestParam(defaultValue = "DESIGN") LibType libType,
                                         HttpServletRequest request, HttpServletResponse response, @RequestBody String body) {
        QueryPageCondition<CCcCi> pageCdt = RestTypeUtil.toPageCondition(body, CCcCi.class);
        String[] ciCodes = pageCdt.getCdt().getCiCodes();
        if (ciCodes.length < 1) {
            ControllerUtils.returnJson(request, response, Collections.emptyList());
            return;
        }
        ESCISearchBean bean = new ESCISearchBean();
        bean.setPageNum(1);
        bean.setPageSize(3000);
        bean.setCdt(pageCdt.getCdt());
        bean.setCiCodes(Lists.newArrayList(ciCodes));
        bean.setDomainId(SysUtil.getCurrentUserInfo().getDomainId());
        if (LibType.PRIVATE == libType) {
            JSONObject params = JSONObject.parseObject(body);
            String ownerCode = params.getString("ownerCode");
            if (BinaryUtils.isEmpty(ownerCode)) {
                ownerCode = SysUtil.getCurrentUserInfo().getLoginCode();
            }
            bean.setOwnerCode(ownerCode);
        }
        Page<ESCIInfo> page = ciSwitchSvc.searchESCIByBean(bean, libType);
        Set<Long> classIds = page.getData().stream().map(ESCIInfo::getClassId).collect(Collectors.toSet());
        List<ESCIClassInfo> esciClassInfos = ciClassApiSvc.selectCiClassByIds(new ArrayList<>(classIds));
        Map<Long, ESCIClassInfo> classMap = esciClassInfos.stream().collect(Collectors.toMap(ESCIClassInfo::getId, each -> each));
        List<CcCiInfo> ciInfos = new ArrayList<>();
        for (ESCIInfo ciInfo : page.getData()) {
            ESCIClassInfo classInfo = classMap.get(ciInfo.getClassId());
            CcCiInfo ci = EamUtil.coverESCIInfo(ciInfo, EamUtil.copy(classInfo.getAttrDefs(), CcCiAttrDef.class), classInfo);
            ciInfos.add(ci);
        }
        ControllerUtils.returnJson(request, response, ciInfos);

    }

    @PostMapping("/queryCiInfoListByCiCodes/withoutCiClass")
    @ModDesc(desc = "分页查询对象数据", pDesc = "条件查询", pType = QueryPageCondition.class, pcType = CCcCi.class, rDesc = "对象数据", rType = List.class)
    public RemoteResult queryCiInfoListByCiCodesWithoutCiClass(@Valid @RequestBody CiInfoQueryVo ciInfoQueryVo) {
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        Long domainId = SysUtil.getCurrentUserInfo().getDomainId();
        query.must(QueryBuilders.termQuery("domainId", domainId));
        query.must(QueryBuilders.termsQuery("ciCode.keyword", ciInfoQueryVo.getCiCodes()));
        if (LibType.PRIVATE == ciInfoQueryVo.getLibType()) {
            query.must(QueryBuilders.termQuery("ownerCode.keyword", SysUtil.getCurrentUserInfo().getLoginCode()));
        }
        List<SortBuilder<?>> sorts = new ArrayList<>();
        sorts.add(SortBuilders.fieldSort("id").order(SortOrder.ASC));
        Page<ESCIInfo> page = ciSwitchSvc.getESCIInfoPageByQuery(domainId, 1, 3000, query, sorts, false, ciInfoQueryVo.getLibType());
        List<CcCiInfo> ciInfos = new ArrayList<>();
        for (ESCIInfo ciInfo : page.getData()) {
            CcCiInfo ci = EamUtil.coverESCIInfo(ciInfo, null, null);
            ciInfos.add(ci);
        }
        return new RemoteResult(ciInfos);
    }

    @RequestMapping("/getFigureCiInfo")
    @ModDesc(desc = "分页查询部署架构图对象数据", pDesc = "条件查询", pType = QueryPageCondition.class, pcType = CCcCi.class, rDesc = "对象数据", rType = List.class)
    public void getFigureCiInfo(@RequestParam(defaultValue = "DESIGN") LibType libType,
                                         HttpServletRequest request, HttpServletResponse response, @RequestBody String body) {
        QueryPageCondition<CCcCi> pageCdt = RestTypeUtil.toPageCondition(body, CCcCi.class);
        String[] ciCodes = pageCdt.getCdt().getCiCodes();
        if(ciCodes.length<1){
            ControllerUtils.returnJson(request,response, Collections.emptyList());
        }else {
            List<CcCiInfo> result = ciSwitchSvc.queryCiInfoList(SysUtil.getCurrentUserInfo().getDomainId(), pageCdt.getCdt(), null, false, true, libType);
            List<VcCiInfo> vcCiInfos = convertToVcCiInfoResult(result);
            vcCiInfos.forEach(each -> {
                each.setAttrDefs(null);
                each.setAttrsMap(null);
            });
            ControllerUtils.returnJson(request, response, vcCiInfos);
        }
    }


    @RequestMapping("/queryPageCiTableByCiCodes")
    @ModDesc(desc = "查询对象列表数据", pDesc = "条件查询", pType = QueryPageCondition.class, pcType = CCcCi.class, rDesc = "对象数据", rType = List.class)
    public void queryPageCiTableByCiCodes(@RequestParam(defaultValue = "DESIGN") LibType libType,
                                          HttpServletRequest request, HttpServletResponse response, @RequestBody String body) {
        QueryPageCondition<CCcCi> pageCdt = RestTypeUtil.toPageCondition(body, CCcCi.class);
        String[] ciCodes = pageCdt.getCdt().getCiCodes();
        VcCiTableInfo ciTable = new VcCiTableInfo();
        Page<Map<String, String>> rPage = new Page<>(pageCdt.getPageNum(), pageCdt.getPageSize(), 0, 0, Collections.emptyList());
        ciTable.setAttrValuePage(rPage);
        if (ciCodes.length < 1) {
            ControllerUtils.returnJson(request, response, ciTable);
            return;
        }
        Long queryClassId = pageCdt.getCdt().getClassId();
        pageCdt.getCdt().setClassId(null);
        List<CcCi> ciList = ciSwitchSvc.queryCiList(SysUtil.getCurrentUserInfo().getDomainId(), pageCdt.getCdt(), null, false, libType);
        if (CollectionUtils.isEmpty(ciList)) {
            ControllerUtils.returnJson(request, response, ciTable);
            return;
        }
        List<Long> classIds = ciList.stream().map(CcCi::getClassId).distinct().collect(Collectors.toList());
        List<ESCIClassInfo> ciClassList = ciClassApiSvc.selectCiClassByIds(classIds);
        if (null == queryClassId || queryClassId == 0) {
            queryClassId = ciClassList.get(0).getId();
        }
        pageCdt.getCdt().setClassId(queryClassId);
        final long matchClassId = queryClassId;
        List<VcCiTableInfo.VcCiClassTabs> ciClassTabs = ciClassList.stream().map((each) -> {
            VcCiTableInfo.VcCiClassTabs ciClassTab = new VcCiTableInfo.VcCiClassTabs();
            ciClassTab.setClassId(each.getId());
            ciClassTab.setClassName(each.getClassName());
            ciClassTab.setSelectEnable(each.getId().equals(matchClassId));
            return ciClassTab;
        }).collect(Collectors.toList());
        ciTable.setCiClassTabs(ciClassTabs);

        Page<CcCiInfo> ccCiInfoPage = ciSwitchSvc.queryCiInfoPage(SysUtil.getCurrentUserInfo().getDomainId(), pageCdt.getPageNum(), pageCdt.getPageSize(), pageCdt.getCdt(), null, false, true, libType);
        rPage.setTotalPages(ccCiInfoPage.getTotalPages());
        rPage.setTotalRows(ccCiInfoPage.getTotalRows());
        List<CcCiAttrDef> defs = ccCiInfoPage.getData().get(0).getAttrDefs();
        List<String> heads = defs.stream().map(CcCiAttrDef::getProName).collect(Collectors.toList());
        ciTable.setCiAttrHeads(heads);
        List<Map<String, String>> attrValues = ccCiInfoPage.getData().stream().map(CcCiInfo::getAttrs).collect(Collectors.toList());
        rPage.setData(attrValues);
        ControllerUtils.returnJson(request, response, ciTable);
    }

    @RequestMapping(value = "/searchCiPage", method = RequestMethod.POST)
    public void searchCiPage(@RequestParam(defaultValue = "DESIGN") LibType libType,
                             HttpServletRequest request, HttpServletResponse response, @RequestBody ESCISearchBean bean) {
        if ((libType == LibType.PRIVATE && BinaryUtils.isEmpty(bean.getOwnerCode()))) {
            bean.setOwnerCode(SysUtil.getCurrentUserInfo().getLoginCode());
        }
        if ((libType != LibType.PRIVATE && !BinaryUtils.isEmpty(bean.getOwnerCode()))) {
            bean.setOwnerCode(null);
        }
        String filterColumn = request.getHeader(StaticParamUtil.STATIC_SEARCHCI_ONLYCICODE);
        CcCiSearchPage ciInfoPage = ciSwitchSvc.searchCIByBean(bean, libType);
        Page<VcCiInfo> results = FormatUtil.esCiDtoToCmvDtoWithoutAttrDefs(ciInfoPage);
        if (!BinaryUtils.isEmpty(filterColumn) && StaticParamUtil.STATIC_SEARCHCI_ONLYCICODE.equals(filterColumn)) {
            List<String> datas = new LinkedList<>();
            if (results.getData() != null) {
                for (VcCiInfo data : results.getData()) {
                    datas.add(data.getCi().getCiCode());
                }
            }
            Page<String> realResults = new Page<>(results.getPageNum(), results.getPageSize(), results.getTotalRows(), results.getTotalPages(), datas);
            ControllerUtils.returnJson(request, response, realResults);
        } else {
            ControllerUtils.returnJson(request, response, results);
        }
    }

    @PostMapping(value = "/queryCiCountByClass")
    @ModDesc(desc = "按指定条件查询CI，并按分类进行汇总", pDesc = "查询条件", pType = CiQueryCdt.class, rDesc = "CI分类列表", rType = List.class)
    public void queryCiCountByClass(@RequestParam(defaultValue = "DESIGN") LibType libType,
                                    HttpServletRequest request, HttpServletResponse response, @RequestBody CiQueryCdtExtend cdt) {
        List<VcCiClassInfoDto> result = eamCiSvc.queryCiCountByClassV2(cdt, libType);
        ControllerUtils.returnJson(request, response, result);
    }

    @PostMapping(value = "/checkCiAttrs")
    public void checkCiAttrs(HttpServletRequest request, HttpServletResponse response, @RequestBody CheckCIAttr check) {
        if (CollectionUtils.isEmpty(check.getIds())) {
            ControllerUtils.returnJson(request, response, Collections.emptyMap());
            return;
        }
        Map<Long, String> errMap = eamCiSvc.checkCiAttrs(check);
        ControllerUtils.returnJson(request, response, errMap);
    }

    @GetMapping("/getLibTypes")
    @ResponseBody
    public RemoteResult getLibTypes() {
        if (BinaryUtils.isEmpty(libTypes)) {
            return new RemoteResult(LibType.BASELINE);
        }
        return new RemoteResult(libTypes);
    }

    private List<VcCiInfo> convertToVcCiInfoResult(List<CcCiInfo> result) {
        ArrayList<VcCiInfo> vcCiInfos = new ArrayList<>();
        for (CcCiInfo ccCiInfo : result) {
            Map<String, String> attrs = ccCiInfo.getAttrs();
            VcCiInfo vcCiInfo = new VcCiInfo();
            BeanUtils.copyProperties(ccCiInfo, vcCiInfo);
            List<AttrInfo> attrsList = new ArrayList<>();
            List<CcCiAttrDef> attrDefs = vcCiInfo.getAttrDefs();
            for (CcCiAttrDef attrDef : attrDefs) {
                String key = attrDef.getProName();
                AttrInfo attrInfo = new AttrInfo();
                attrInfo.setKey(key);
                attrInfo.setValue(attrs.get(key));
                attrsList.add(attrInfo);
            }
            vcCiInfo.setAttrs(attrsList);
            vcCiInfo.setAttrsMap(ccCiInfo.getAttrs());
            vcCiInfos.add(vcCiInfo);
        }
        return vcCiInfos;
    }

    /**
     * 查询对象分类树结构
     */
    @RequestMapping("/getClassTree")
    public RemoteResult getClassTree(@RequestParam(defaultValue = "false") Boolean display, @RequestParam(defaultValue = "BASELINE") LibType libType) {
        List<ClassNodeInfo> result = ciSwitchSvc.getClassTree(display, libType);
        return new RemoteResult(result);
    }

    @GetMapping("/findClassManagerList")
    public void findClassManagerList(@RequestParam(value = "classId", required = false) Long classId,
                                     HttpServletRequest request, HttpServletResponse response) {
        List<ClassNodeInfo> ls = ciSwitchSvc.findClassManagerList(classId);
        ControllerUtils.returnJson(request, response, ls);
    }

    /**
     * 验证ci在私有库/共享库是否已存在
     *
     */
    @PostMapping("/handleCiExist")
    @ResponseBody
    public RemoteResult handleCiExist(@RequestBody String body) {
        JSONObject json = JSONObject.parseObject(body);
        List<Long> ids = JSONObject.parseArray(json.getString("ids"), Long.class);
        String libType = json.getString("libType");
        LibType typeData = LibType.valueOf(libType);
        String ownerCode = json.getString("ownerCode");
        Integer result = eamCiSvc.handleCiExist(ids, typeData, ownerCode);
        return new RemoteResult(result);
    }

    @RequestMapping("/queryPageByAllIndex")
    @ModDesc(desc = "通过索引查询相关的CI", pDesc = "查询条件", pType = QueryPageCondition.class, pcType = CiQueryCdtExtend.class, rDesc = "CI的信息", rType = VcCiSearchPage.class)
    public void queryPageByAllIndex(HttpServletRequest request, HttpServletResponse response, @RequestBody QueryPageCondition<CiQueryCdtExtend> pageCdt) {
        int pageSize = pageCdt.getPageSize();
        if (pageSize < 1) {
            throw new ServiceException("is wrong pageSize '" + pageSize + "'! ");
        }
        List<VcCiSearchPage> result = ciSwitchSvc.queryPageByAllIndex(pageCdt.getPageNum(), pageCdt.getPageSize(), pageCdt.getCdt(), pageCdt.getOrders(), new VcCiQ[]{VcCiQ.ATTR});
        ControllerUtils.returnJson(request, response, result);
    }

    @RequestMapping("/queryEnumDataByClassId")
    public void queryEnumDataByClassId(HttpServletRequest request, HttpServletResponse response, @RequestBody CiQueryCdt cdt) {
        Map<Long, List<String>> result = ciSwitchSvc.queryEnumDataByClassId(cdt.getClassId());
        ControllerUtils.returnJson(request, response, result);
    }

    @RequestMapping("/getPrivateCIInfoByDesginCode")
    public void getPrivateCIInfoByDesginCode(HttpServletRequest request, HttpServletResponse response,
                                              String ciCode) {
        CcCiInfo privateCIInfo = ciSwitchSvc.getPrivateCIInfoByDesginCode(ciCode);
        ControllerUtils.returnJson(request, response, privateCIInfo);
    }

    /**
     * 元模型删除分类前校验该分类下是否存在数据
     */
    @RequestMapping("/existDataByClassId")
    @ModDesc(desc = "分页查询对象数据", pDesc = "条件查询", pType = QueryPageCondition.class, pcType = CiQueryCdtExtend.class, rDesc = "对象数据", rType = CiGroupPage.class)
    public void existDataByClassId(HttpServletRequest request, HttpServletResponse response, @RequestBody String body) {
        QueryPageCondition<CiQueryCdtExtend> pageCdt = RestTypeUtil.toPageCondition(body, CiQueryCdtExtend.class);
        Map<String, Long> result = ciSwitchSvc.existDataByClassId(pageCdt.getCdt());
        ControllerUtils.returnJson(request, response, result);
    }

    /**
     * 查询CI属性值
     */
    @RequestMapping(value = "/queryValues", method = RequestMethod.POST)
    @ResponseBody
    public RemoteResult queryValues(@RequestBody QueryCiValueRequest request) {

        List<String> data = eamCiSvc.queryValues(request);
        return new RemoteResult(data);
    }

    @PostMapping("/findNonCompliance")
    public void findNonCompliance(@RequestParam Long classId,@RequestParam(defaultValue = "BASELINE") LibType libType,HttpServletRequest request,
                                  HttpServletResponse response) {
        boolean result = ciSwitchSvc.findNonCompliance(classId,libType);
        ControllerUtils.returnJson(request, response, result);
    }

    @RequestMapping("/exportCiByConditions")
    public ResponseEntity<byte[]> exportCiByConditions(@RequestParam(defaultValue = "DESIGN") LibType libType, @RequestBody ExportCiDto exportDto) {
        ESCISearchBeanVO esciSearchBeanVO = new ESCISearchBeanVO();
        esciSearchBeanVO.setGteTime(exportDto.getGteTime());
        esciSearchBeanVO.setLteTime(exportDto.getLteTime());
        isConvertibleToLong(esciSearchBeanVO);
        return ciSwitchSvc.exportCiOrClassByConditions(exportDto, libType);
    }

    /**
     * 资产可见->筛选数据
     * @param bean
     * @param libType
     * @return
     */
    private CiGroupPage getCiGroupList(ESCISearchBean bean, LibType libType) {
        if (!StringUtils.isEmpty(bean.getDiagramId()) && Objects.equals(libType, LibType.DESIGN)) {
            Map<Long, String> filterMap = null;
            ESDiagram esDiagram = diagramApiClient.getEsDiagram(bean.getDiagramId(), 0);
            if (esDiagram != null && Objects.equals(esDiagram.getDiagramSubType(), 2) && !StringUtils.isEmpty(esDiagram.getViewType())) {
                List<EamArtifactElementVo> elementList = artifactColumnSvc.queryByArtifactId(Long.valueOf(esDiagram.getViewType()), Lists.newArrayList(ArtifactType.ASSET_TYPE.val()));
                if (!BinaryUtils.isEmpty(elementList)) {
                    filterMap = new HashMap<>();
                    for (EamArtifactElementVo artifactElementVo : elementList) {
                        List<String> elements = artifactElementVo.getElements();
                        for (String element : elements) {
                            EamArtifactCiVo vo = JSON.parseObject(element, EamArtifactCiVo.class);
                            if (BinaryUtils.isEmpty(vo.getType()) || !vo.getType().equals("class") || !vo.getAssetsFlag()) {
                                continue;
                            }
                            List<Long> classIds = bean.getClassIds();
                            if (!StringUtils.isEmpty(vo.getFilter()) && classIds.contains(Long.valueOf(vo.getId()))) {
                                filterMap.put(Long.valueOf(vo.getId()), vo.getFilter());
                            }
                            //TODO 模拟数据
                            /*if (classIds.contains(Long.valueOf(vo.getId()))) {
                                JSONArray jsonArr1 = new JSONArray();
                                JSONArray jsonArr2 = new JSONArray();
                                JSONObject filterJson1 = new JSONObject();
                                filterJson1.put("name", "重要性等级");
                                filterJson1.put("value", "A+");
                                jsonArr2.add(filterJson1);

                                JSONObject filterJson2 = new JSONObject();
                                filterJson2.put("name", "投产状态");
                                filterJson2.put("value", "下线");
                                jsonArr2.add(filterJson2);

                                JSONArray jsonArr3 = new JSONArray();
                                JSONObject filterJson3 = new JSONObject();
                                filterJson3.put("name", "中间件版本");
                                filterJson3.put("value", "VF11");
                                jsonArr3.add(filterJson3);

                                jsonArr1.add(jsonArr2);
                                jsonArr1.add(jsonArr3);
                                filterMap.put(Long.valueOf(vo.getId()), jsonArr1.toJSONString());
                            }*/
                        }
                    }
                }
                if (filterMap != null && filterMap.keySet().size() > 0) {
                    int size = bean.getPageSize();
                    for (Map.Entry<Long, String> filterInfo : filterMap.entrySet()) {
                        List<Long> classList = new ArrayList<>(1);
                        classList.add(filterInfo.getKey());
                        bean.setClassIds(classList);
                        // 获取过滤条件
                        String filterStr = filterInfo.getValue();
                        if (!com.binary.core.lang.StringUtils.isEmpty(filterStr)) {
                            JSONArray jsonArray = JSONArray.parseArray(filterStr);
                            if (jsonArray.size() > 0) {
                                List<CcCiInfo> allClassCiList = new ArrayList<>();
                                for (int i = 0; i < jsonArray.size(); i++) {
                                    JSONArray jsonArr = jsonArray.getJSONArray(i);
                                    List<ESAttrBean> andAttrs = new ArrayList<>();
                                    for (int j = 0; j < jsonArr.size(); j++) {
                                        JSONObject filterJson = jsonArr.getJSONObject(j);
                                        if (filterJson != null && !StringUtils.isEmpty(filterJson.getString("name"))
                                                && !StringUtils.isEmpty(filterJson.getString("value"))) {
                                            ESAttrBean esAttrBean = new ESAttrBean();
                                            esAttrBean.setKey(filterJson.getString("name"));
                                            esAttrBean.setOptType(1);
                                            esAttrBean.setValue(filterJson.getString("value"));
                                            andAttrs.add(esAttrBean);
                                        }
                                    }
                                    bean.setAndAttrs(andAttrs);
                                    bean.setDiagramId(null);
                                    bean.setPageSize(3000);
                                    CiGroupPage page = ciSwitchSvc.queryPageBySearchBean(bean, false, libType);
                                    if (page != null && !CollectionUtils.isEmpty(page.getData())) {
                                        if (page.getTotalRows() > 3000) {
                                            bean.setPageSize((int)page.getTotalRows());
                                            page = ciSwitchSvc.queryPageBySearchBean(bean, false, libType);
                                        }
                                        List<CcCiInfo> ciList = page.getData();
                                        allClassCiList.addAll(ciList);
                                    }
                                }
                                if (!CollectionUtils.isEmpty(allClassCiList)) {
                                    // ci去重
                                    List<CcCiInfo> collect = new ArrayList<>(allClassCiList.size());
                                    Set<String> ciCodeSet = new HashSet<>(allClassCiList.size());
                                    for (CcCiInfo ccCiInfo : allClassCiList) {
                                        if (!ciCodeSet.contains(ccCiInfo.getCi().getCiCode())) {
                                            collect.add(ccCiInfo);
                                            ciCodeSet.add(ccCiInfo.getCi().getCiCode());
                                        }
                                    }
                                    int records = collect.size();
                                    int del = records / size;
                                    int mod = records % size;
                                    Integer total = (mod != 0) ? del + 1 : del;
                                    CiGroupPage ciGroupPage = new CiGroupPage(bean.getPageNum(), size, records, total, collect);
                                    ciGroupPage.setTotalCiCount(records);
                                    return ciGroupPage;
                                }
                            }
                        }
                    }
                } else {
                    bean.setDiagramId(null);
                    return ciSwitchSvc.queryPageBySearchBean(bean, false, libType);
                }
            } else {
                bean.setDiagramId(null);
                return ciSwitchSvc.queryPageBySearchBean(bean, false, libType);
            }
        }
        bean.setDiagramId(null);
        return ciSwitchSvc.queryPageBySearchBean(bean, false, libType);
    }

    @PostMapping("/primaryKey/check")
    @ModDesc(desc = "校验主键完整性", pDesc = "对象标识", pType = String.class, rDesc = "是否完整", rType = Boolean.class)
    public RemoteResult checkPrimaryKey(@RequestBody String body) {
        JSONObject param = JSON.parseObject(body);
        String ciCode = param.getString("ciCode");
        String ownerCode = param.getString("ownerCode");
        Boolean result = eamCiSvc.checkPrimaryKey(ciCode, ownerCode);
        return new RemoteResult(result);
    }

    @GetMapping("/permission/checkAdd")
    @ModDesc(desc = "校验新建权限", pDesc = "视图id", pType = String.class, rDesc = "", rType = Map.class)
    public RemoteResult checkAdd(@RequestParam String diagramId) {
        BinaryUtils.checkEmpty(diagramId, "视图id");
        Map<Long, Boolean> result = eamCiSvc.checkAdd(diagramId);
        return new RemoteResult(result);
    }

    @GetMapping("/permission/checkEdit")
    @ModDesc(desc = "校验编辑权限", pDesc = "对象标识", pType = String.class, rDesc = "是否有编辑权限", rType = Boolean.class)
    public RemoteResult checkEdit(@RequestParam String ciCode, @RequestParam String ownerCode) {
        BinaryUtils.checkEmpty(ciCode, "ci标识");
        BinaryUtils.checkEmpty(ownerCode, "用户标识");
        Boolean result = eamCiSvc.checkEdit(ciCode, ownerCode);
        return new RemoteResult(result);
    }

    @PostMapping("/permission/checkEditBatch")
    @ModDesc(desc = "批量获取视图ci编辑权限", pDesc = "对象标识", pType = String.class, rDesc = "有编辑权限的ci集合", rType = Boolean.class)
    public RemoteResult checkEditBatch(@RequestBody String body) {
        JSONObject json = JSON.parseObject(body);
        String ownerCode = json.getString("ownerCode");
        List<String> ciCodes = json.getJSONArray("ciCodes").toJavaList(String.class);
        BinaryUtils.checkEmpty(ownerCode, "ownerCode");
        List<String> result = eamCiSvc.checkEditBatch(ciCodes, ownerCode);
        return new RemoteResult(result);
    }

    @PostMapping("/permission/checkInfo")
    @ModDesc(desc = "获取ci属性校验标签规则", pDesc = "对象标识", pType = String.class, rDesc = "校验信息", rType = Boolean.class)
    public RemoteResult checkInfo(@RequestBody CcCiInfo ciInfo) {
        BinaryUtils.checkEmpty(ciInfo.getCi(), "ci");
        BinaryUtils.checkEmpty(ciInfo.getCi().getClassId(), "分类id");
        Map<String, String> result = eamCiSvc.getCheckRulesInfo(ciInfo);
        return new RemoteResult(result);
    }

    @ApiOperation("获取关联ci对应属性值列表-添加数据筛选")
    @PostMapping("/getAttrValuesBySearchBean")
    public ApiResult<Page<CcCiInfo>> getAttrValuesBySearchBean(@RequestBody ESAttrAggScreenBean bean) {
        return  ApiResult.ok(this).data(eamCiSvc.getAttrValuesBySearchBean(bean));
    }
     /*
     *获取流程,流程组分类id集合
     */
    @GetMapping("/getProcessCategory")
    public RemoteResult getProcessCategory(@RequestParam String name) {
        Assert.notNull(name,"参数不能为空");
        CCcCiClass cCcCiClass = new CCcCiClass();
        if (name.equals("流程")) {
                cCcCiClass.setClassCodes(new String[]{FlowSystemType.FLOW.getFlowSystemTypeName()
                        , FlowSystemType.SUB_FLOW.getFlowSystemTypeName()});
        } else {
            cCcCiClass.setClassCodes(new String[]{name});
        }
        ESCIClassSearchBean esciClassSearchBean = new ESCIClassSearchBean();
        esciClassSearchBean.setCdt(cCcCiClass);
        List<ESCIClassInfo> esciClassInfos = iciClassSvc.queryESCiClassInfoListBySearchBean(esciClassSearchBean);
        List<Long> collect = esciClassInfos.stream().map(ESCIClassInfo::getId).collect(Collectors.toList());
        return new RemoteResult(collect);
    }

}