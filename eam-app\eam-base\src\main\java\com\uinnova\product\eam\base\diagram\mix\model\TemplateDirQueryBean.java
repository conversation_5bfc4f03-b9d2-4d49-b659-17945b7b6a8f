package com.uinnova.product.eam.base.diagram.mix.model;

/**
 * 查询模板目录请求参数
 */
public class TemplateDirQueryBean {

    private Integer pageNum = 1;

    private Integer pageSize = 20;

    private Integer[] diagramTypes = new Integer[]{3, 4};

    private Long dirId;

    private String like;

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Long getDirId() {
        return dirId;
    }

    public void setDirId(Long dirId) {
        this.dirId = dirId;
    }

    public String getLike() {
        return like;
    }

    public void setLike(String like) {
        this.like = like;
    }

    public Integer[] getDiagramTypes() {
        return diagramTypes;
    }

    public void setDiagramTypes(Integer[] diagramTypes) {
        this.diagramTypes = diagramTypes;
    }
}
