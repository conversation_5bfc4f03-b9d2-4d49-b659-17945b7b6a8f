package com.uino.bean.cmdb.base;

import java.io.Serializable;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.comm.model.rlt.CcCiRlt;

/**
 * ci关系历史
 * 
 * <AUTHOR>
 *
 */
public class ESCIRltInfoHistory extends CcCiRlt implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * <b>属性
     */
    private Map<String, String> attrs;

    /**
     * 根据关系以及子code拼接成的唯一标识 ${rltCiCode}-?-?-? ?代表按顺序的子code值，未填写会填入NULL
     */
    private String uniqueCode;

    /**
     * 根据关系以及子id拼接成的唯一标识 ${rltCiId}-?-?-? ?代表按顺序的子id值，未填写会填入NULL
     */
    private String uniqueId;

    /**
     * 关系id
     */
    private Long rltId;

    /**
     * 版本
     */
    private Long version;

    /**
     * EA字段---视图id
     */
    private String diagramId;

    /**
     * 动作 0:新增或修改 1:删除
     */
    private int action = 0;

    public String getDiagramId() {
        return diagramId;
    }

    public void setDiagramId(String diagramId) {
        this.diagramId = diagramId;
    }

    public Long getRltId() {
        return rltId;
    }

    public void setRltId(Long rltId) {
        this.rltId = rltId;
    }

    public String getUniqueCode() {
        return uniqueCode;
    }

    public void setUniqueCode(String uniqueCode) {
        this.uniqueCode = uniqueCode;
    }

    public String getUniqueId() {
        return uniqueId;
    }

    public void setUniqueId(String uniqueId) {
        this.uniqueId = uniqueId;
    }

    public Map<String, String> getAttrs() {
        return attrs;
    }

    public int getAction() {
        return action;
    }

    public void setAction(int action) {
        this.action = action;
    }

    public void setAttrs(Map<String, String> attrs) {
        Map<String, String> realAttrMap = new HashMap<>();
        if (attrs != null && attrs.size() > 0) {
            attrs.forEach((key, val) -> {
                realAttrMap.put(key.toUpperCase(), val);
            });
        }
        this.attrs = realAttrMap;
    }

    /**
     * 根据源和目标ci信息设置关系信息（不提供任何验证，使用自己注意）
     * 
     * @param sCi
     * @param tCi
     */
    public void setRltCiInfo(ESCIInfo sCi, ESCIInfo tCi) {
        // 设置源信息
        this.setSourceCiId(sCi.getId());
        this.setSourceCiCode(sCi.getCiCode());
        this.setSourceClassId(sCi.getClassId());
        // 设置目标信息
        this.setTargetCiId(tCi.getId());
        this.setTargetCiCode(tCi.getCiCode());
        this.setTargetClassId(tCi.getClassId());
        // 根据源和目标和自身已有关系分类信息计算该关系cicode
        // 继承之前规则，ci关系的ciCode拼装规则为：${sCiCode}_${rltClassId}_${tCiCode}
        String rltCiCode = new StringBuilder().append(sCi.getCiCode()).append("_").append(this.getClassId()).append("_")
                .append(tCi.getCiCode()).toString();
        this.setCiCode(rltCiCode);
    }

    /**
     * 根据关系分类属性模板以及对象已存在的cicode以及属性信息拼装唯一标识
     * 
     * @param attrDefs
     */
    public void setUniqueCode(List<CcCiAttrDef> attrDefs) {
        this.uniqueCode = getUniqueCode(attrDefs, this.getAttrs(), this.getCiCode());
    }

    /**
     * 根据关系分类属性模板以及rltcode以及属性信息拼装唯一标识
     * 
     * @param attrDefs
     * @param attrs
     * @param rltCode
     * @return
     */
    public static String getUniqueCode(List<CcCiAttrDef> attrDefs, Map<String, String> attrs, String rltCode) {
        String appendCode = "";
        if (attrDefs != null && attrDefs.size() > 0) {
            List<CcCiAttrDef> isMajorDefs = attrDefs.stream()
                    .filter(def -> def.getIsMajor() != null && def.getIsMajor().intValue() == 1)
                    .collect(Collectors.toList());
            Collections.sort(isMajorDefs, new Comparator<CcCiAttrDef>() {

                @Override
                public int compare(CcCiAttrDef o1, CcCiAttrDef o2) {
                    int o1HashCode = o1.getProStdName().hashCode();
                    int o2HashCode = o2.getProStdName().hashCode();
                    if (o1HashCode == o2HashCode) {
                        return 0;
                    } else if (o1HashCode < o2HashCode) {
                        return -1;
                    } else {
                        return 1;
                    }
                }
            });
            if (isMajorDefs != null && isMajorDefs.size() > 0) {
                attrs = attrs == null ? new HashMap<>() : attrs;
                for (CcCiAttrDef def : isMajorDefs) {
                    String defKey = def.getProStdName();
                    String attrVal = attrs.get(defKey);
                    attrVal = (attrVal == null || "".equals(attrVal.trim())) ? "NULL" : attrVal;
                    appendCode += "-" + attrVal;
                }
            }
        }
        // appendCode = appendCode != null && !"".equals(appendCode) ?
        // appendCode.toUpperCase() : appendCode;
        return "UK_" + ("".equals(appendCode) ? rltCode : rltCode + appendCode);
    }

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }

    /**
     * 对比关系本身信息一致性
     * 
     * @param obj
     * @return
     */
    public boolean equalsInfo(Object obj) {
        if (obj == null || !(obj instanceof ESCIRltInfo)) { return false; }
        ESCIRltInfo rltEq = (ESCIRltInfo) obj;
        String rltCICode = this.getCiCode();
        String rltEqCICode = rltEq.getCiCode();
        if ((rltCICode == null && rltEqCICode != null) || (rltCICode != null && rltEqCICode == null)) { return false; }
        boolean rltCICodeEq = rltCICode.equals(rltEqCICode);
        if (!rltCICodeEq) { return false; }
        Map<String, String> rltAttrs = this.getAttrs();
        Map<String, String> rltEqAttrs = rltEq.getAttrs();
        if ((rltAttrs == null && rltEqAttrs != null) || (rltAttrs != null && rltEqAttrs == null)) {
            return false;
        } else if (rltAttrs == null && rltEqAttrs == null) { return true; }
        return rltAttrs.equals(rltEqAttrs);
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null || !(obj instanceof ESCIRltInfoHistory)) { return false; }
        boolean infoEq = this.equalsInfo(obj);
        if (!infoEq) { return false; }
        ESCIRltInfoHistory rltHisEq = (ESCIRltInfoHistory) obj;
        Long rltHisVersion = this.getVersion();
        Long rltHisEqVersion = rltHisEq.getVersion();
        if ((rltHisVersion == null && rltHisEqVersion != null) || (rltHisVersion != null && rltHisEqVersion == null)) {
            return false;
        } else if (rltHisVersion == null && rltHisEqVersion == null) { return true; }
        return rltHisVersion.equals(rltHisEqVersion);
    }
}
