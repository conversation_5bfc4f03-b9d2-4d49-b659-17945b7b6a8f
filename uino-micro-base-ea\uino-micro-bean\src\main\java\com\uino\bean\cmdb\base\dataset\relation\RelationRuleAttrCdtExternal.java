package com.uino.bean.cmdb.base.dataset.relation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value="关系构建规则")
public class RelationRuleAttrCdtExternal {

	@ApiModelProperty(value="属性名")
	private String attrName;

	@ApiModelProperty(value="条件")
	private String op;

	@ApiModelProperty(value="值")
	private String value;
	
	public String getAttrName() {
		return attrName;
	}
	public void setAttrName(String attrName) {
		this.attrName = attrName;
	}
	public String getOp() {
		return op;
	}
	public void setOp(String op) {
		this.op = op;
	}
	public String getValue() {
		return value;
	}
	public void setValue(String value) {
		this.value = value;
	}
	
}
