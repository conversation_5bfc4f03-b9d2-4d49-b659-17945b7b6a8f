package com.uinnova.product.eam.service.counter;

import com.uinnova.product.eam.base.domain.CounterTypeEnum;
import com.uinnova.product.eam.comm.model.es.EamOpCounter;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface IEamOpCountSvc {

    Long insert(String ciCode, CounterTypeEnum counterType, Long domainId, Long times);

    boolean increaseCount(String ciCode, CounterTypeEnum counterType, Long domainId);

    Map<String, Long> selectCounterMap(List<String> ciCodes);

    List<EamOpCounter> selectTopList(int topNum, CounterTypeEnum counterType, Set<String> ciCodes);

    long countRecords(CounterTypeEnum counterType, long endTime);
}
