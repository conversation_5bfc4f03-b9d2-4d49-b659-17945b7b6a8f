package com.uinnova.product.eam.comm.model.es;

import com.uinnova.product.eam.base.cj.BaseEntity;
import lombok.Data;

/**
 * 流程体系发布记录
 *
 * <AUTHOR>
 * @since 2024/5/30 18:06
 */
@Data
public class FlowProcessSystemPublishHistory extends BaseEntity {

    private Long id;

    private String ciCode;

    /**
     * 流程图
     */
    private String flowDiagram;
    /**
     * 架构图
     */
    private String architectureDiagram;
    /**
     * 集成关系图
     */
    private String integrationDiagram;

    /**
     * 端到端流程图
     */
    private String c2cFlowDiagram;

    /**
     * ci历史表的id
     */
    private Long ciHistoryId;

    private String flowInstanceId;

    private String flowName;

    private String publishReason;

    private String changeData;

    private String checkUser;

    private String publishType;

    /**
     * 快照表的id
     */
    private Long flowSystemApproveDataId;

    /**
     * 发布人
     */
    private String creator;
}
