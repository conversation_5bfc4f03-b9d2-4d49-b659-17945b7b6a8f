package com.uinnova.product.vmdb.comm.model.license;

import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;

/**
 * 
 * <AUTHOR>
 *
 */
@Comment("受权服务器表[CC_LICENSE_AUTH_SERVER]")
public class CCcLicenseAuthServer implements Condition {
    private static final long serialVersionUID = 1L;

    @Comment("ID[ID] operate-Equal[=]")
    private Long id;

    @Comment("ID[ID] operate-In[in]")
    private Long[] ids;

    @Comment("ID[ID] operate-GTEqual[>=]")
    private Long startId;

    @Comment("ID[ID] operate-LTEqual[<=]")
    private Long endId;

    @Comment("授权ID[AUTH_ID] operate-Equal[=]")
    private Long authId;

    @Comment("授权ID[AUTH_ID] operate-In[in]")
    private Long[] authIds;

    @Comment("授权ID[AUTH_ID] operate-GTEqual[>=]")
    private Long startAuthId;

    @Comment("授权ID[AUTH_ID] operate-LTEqual[<=]")
    private Long endAuthId;

    @Comment("服务器IP[SERVER_IP] operate-Like[like]")
    private String serverIp;

    @Comment("服务器IP[SERVER_IP] operate-Equal[=]")
    private String serverIpEqual;

    @Comment("服务器IP[SERVER_IP] operate-In[in]")
    private String[] serverIps;

    @Comment("服务器编码[SERVER_CODE] operate-Like[like]")
    private String serverCode;

    @Comment("服务器编码[SERVER_CODE] operate-Equal[=]")
    private String serverCodeEqual;

    @Comment("服务器编码[SERVER_CODE] operate-In[in]")
    private String[] serverCodes;

    @Comment("服务器描述[SERVER_DESC] operate-Like[like]")
    private String serverDesc;

    @Comment("创建时间[CREATE_TIME] operate-Equal[=]    yyyyMMddHHmmss")
    private Long createTime;

    @Comment("创建时间[CREATE_TIME] operate-In[in]    yyyyMMddHHmmss")
    private Long[] createTimes;

    @Comment("创建时间[CREATE_TIME] operate-GTEqual[>=]    yyyyMMddHHmmss")
    private Long startCreateTime;

    @Comment("创建时间[CREATE_TIME] operate-LTEqual[<=]    yyyyMMddHHmmss")
    private Long endCreateTime;

    @Comment("修改时间[MODIFY_TIME] operate-Equal[=]    yyyyMMddHHmmss")
    private Long modifyTime;

    @Comment("修改时间[MODIFY_TIME] operate-In[in]    yyyyMMddHHmmss")
    private Long[] modifyTimes;

    @Comment("修改时间[MODIFY_TIME] operate-GTEqual[>=]    yyyyMMddHHmmss")
    private Long startModifyTime;

    @Comment("修改时间[MODIFY_TIME] operate-LTEqual[<=]    yyyyMMddHHmmss")
    private Long endModifyTime;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long[] getIds() {
        return this.ids;
    }

    public void setIds(Long[] ids) {
        this.ids = ids;
    }

    public Long getStartId() {
        return this.startId;
    }

    public void setStartId(Long startId) {
        this.startId = startId;
    }

    public Long getEndId() {
        return this.endId;
    }

    public void setEndId(Long endId) {
        this.endId = endId;
    }

    public Long getAuthId() {
        return this.authId;
    }

    public void setAuthId(Long authId) {
        this.authId = authId;
    }

    public Long[] getAuthIds() {
        return this.authIds;
    }

    public void setAuthIds(Long[] authIds) {
        this.authIds = authIds;
    }

    public Long getStartAuthId() {
        return this.startAuthId;
    }

    public void setStartAuthId(Long startAuthId) {
        this.startAuthId = startAuthId;
    }

    public Long getEndAuthId() {
        return this.endAuthId;
    }

    public void setEndAuthId(Long endAuthId) {
        this.endAuthId = endAuthId;
    }

    public String getServerIp() {
        return this.serverIp;
    }

    public void setServerIp(String serverIp) {
        this.serverIp = serverIp;
    }

    public String getServerIpEqual() {
        return this.serverIpEqual;
    }

    public void setServerIpEqual(String serverIpEqual) {
        this.serverIpEqual = serverIpEqual;
    }

    public String[] getServerIps() {
        return this.serverIps;
    }

    public void setServerIps(String[] serverIps) {
        this.serverIps = serverIps;
    }

    public String getServerCode() {
        return this.serverCode;
    }

    public void setServerCode(String serverCode) {
        this.serverCode = serverCode;
    }

    public String getServerCodeEqual() {
        return this.serverCodeEqual;
    }

    public void setServerCodeEqual(String serverCodeEqual) {
        this.serverCodeEqual = serverCodeEqual;
    }

    public String[] getServerCodes() {
        return this.serverCodes;
    }

    public void setServerCodes(String[] serverCodes) {
        this.serverCodes = serverCodes;
    }

    public String getServerDesc() {
        return this.serverDesc;
    }

    public void setServerDesc(String serverDesc) {
        this.serverDesc = serverDesc;
    }

    public Long getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long[] getCreateTimes() {
        return this.createTimes;
    }

    public void setCreateTimes(Long[] createTimes) {
        this.createTimes = createTimes;
    }

    public Long getStartCreateTime() {
        return this.startCreateTime;
    }

    public void setStartCreateTime(Long startCreateTime) {
        this.startCreateTime = startCreateTime;
    }

    public Long getEndCreateTime() {
        return this.endCreateTime;
    }

    public void setEndCreateTime(Long endCreateTime) {
        this.endCreateTime = endCreateTime;
    }

    public Long getModifyTime() {
        return this.modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Long[] getModifyTimes() {
        return this.modifyTimes;
    }

    public void setModifyTimes(Long[] modifyTimes) {
        this.modifyTimes = modifyTimes;
    }

    public Long getStartModifyTime() {
        return this.startModifyTime;
    }

    public void setStartModifyTime(Long startModifyTime) {
        this.startModifyTime = startModifyTime;
    }

    public Long getEndModifyTime() {
        return this.endModifyTime;
    }

    public void setEndModifyTime(Long endModifyTime) {
        this.endModifyTime = endModifyTime;
    }

}
