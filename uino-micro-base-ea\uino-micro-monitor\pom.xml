<?xml version="1.0"?>
<project
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd"
	xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.uino</groupId>
		<artifactId>eam-base</artifactId>
		<version>1.0.0-SNAPSHOT</version>
	</parent>

	<artifactId>uino-eam-micro-monitor</artifactId>
	<name>uino-eam-micro-monitor</name>

	<properties>
		<java.source.plugin.version>3.0.1</java.source.plugin.version>
		<uino.micro.version>1.0.0-SNAPSHOT</uino.micro.version>
	</properties>

	<dependencies>
		<!-- =================uino-micro-base===start================== -->
		<dependency>
			<groupId>com.uino</groupId>
			<artifactId>uino-eam-micro-dao</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>com.uino</groupId>
			<artifactId>uino-eam-micro-util</artifactId>
			<version>${project.version}</version>
		</dependency>
		<!-- =================uino-micro-base===end================== -->
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-source-plugin</artifactId>
				<version>${java.source.plugin.version}</version><!--$NO-MVN-MAN-VER$ -->
				<executions>
					<execution>
						<id>attach-sources</id>
						<goals>
							<goal>jar-no-fork</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
</project>
