package com.uino.web.cmdb.mvc;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import cn.hutool.core.bean.BeanUtil;
import com.uino.bean.cmdb.base.ESCIAttrDefInfo;
import com.uino.bean.cmdb.business.CIClassInfoDto;
import com.uino.bean.permission.base.SysUser;
import com.uino.init.api.ApiResult;
import com.uino.init.api.ApiVersion;
import com.uino.util.sys.SysUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import com.binary.core.io.Resource;
import com.binary.core.util.BinaryUtils;
import com.binary.framework.util.ControllerUtils;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uino.bean.cmdb.base.ESCIClassInfo;
import com.uino.bean.cmdb.business.ClassReOrderDTO;
import com.uino.api.client.cmdb.IRltClassApiSvc;
import java.util.stream.Collectors;

/**
 * 关系分类相关控制层
 * 
 * <AUTHOR>
 *
 */
@ApiVersion(1)
@Api(value = "关系分类相关控制层", tags = {"关系管理"})
@RestController
@RequestMapping(value = "/cmdb/rltClass")
public class RltClassMvc {
	@Autowired
	private IRltClassApiSvc rltClassApiSvc;

	/**
	 * 持久化关系分类
	 * 
	 * @param reqBean
	 * @param request
	 * @param response
	 */
	@ApiOperation(value="保存关系分类")
	@PostMapping("saveOrUpdate")
    @ModDesc(desc = "保存关系分类", pDesc = "关系分类对象", pType = ESCIClassInfo.class, rDesc = "关系分类id", rType = Long.class)
	public ApiResult<Long> saveOrUpdate(@RequestBody ESCIClassInfo reqBean, HttpServletRequest request,
			HttpServletResponse response) {
		SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
		reqBean.setDomainId(currentUserInfo.getDomainId());
		List<ESCIAttrDefInfo> defs = reqBean.getAttrDefs().stream().filter(each -> (!BinaryUtils.isEmpty(each.getId()) && each.getId().longValue() > 0) || BinaryUtils.isEmpty(each.getId())).collect(Collectors.toList());
		reqBean.setAttrDefs(defs);
		Long id = rltClassApiSvc.saveOrUpdate(reqBean);
		return ApiResult.ok(this).data(id);
	}

	/**
	 * 根据关系分类id删除关系分类
	 * 
	 * @param rltClassIds
	 * @param request
	 * @param response
	 */
	@ApiOperation(value="根据关系分类id批量删除关系分类")
	@PostMapping("deleteByIds")
    @ModDesc(desc = "跟据id批量删除关系分类", pDesc = "关系分类id集合", pType = Set.class, pcType = Long.class, rDesc = "操作是否成功", rType = Boolean.class)
	public ApiResult<Boolean> deleteByIds(@RequestBody Set<Long> rltClassIds, HttpServletRequest request,
			HttpServletResponse response) {
		rltClassApiSvc.deleteByIds(rltClassIds);
		return ApiResult.ok(this).data(true);
	}

	/**
	 * 获取所有关系分类
	 * 
	 * @param request
	 * @param response
	 */
	@ApiOperation(value="查询所有关系分类")
	@PostMapping("queryAllClasses")
    @ModDesc(desc = "查询所有关系分类", pDesc = "无", rDesc = "关系分类列表", rType = List.class, rcType = CcCiClassInfo.class)
	public ApiResult<List<CcCiClassInfo>> queryAllClasses(HttpServletRequest request, HttpServletResponse response) {
		SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
		List<CcCiClassInfo> results = rltClassApiSvc.queryAllClasses(currentUserInfo.getDomainId());
		return ApiResult.ok(this).data(results);
	}

	/**
	 * 根据关系分类id获取关系分类信息
	 * 
	 * @param rltClassId
	 * @param request
	 * @param response
	 */
	@ApiOperation(value="根据id查询关系分类")
	@PostMapping("getRltClassById")
    @ModDesc(desc = "跟据id查询关系分类", pDesc = "关系分类id", pType = Long.class, rDesc = "关系分类信息", rType = CcCiClassInfo.class)
	public ApiResult<CcCiClassInfo> getRltClassById(@RequestBody Long rltClassId, HttpServletRequest request,
			HttpServletResponse response) {
		CcCiClassInfo results = rltClassApiSvc.getRltClassById(rltClassId);
		return ApiResult.ok(this).data(results);
	}

	/**
	 * 根据关系分类id获取关系分类信息
	 *
	 * @param rltClassId
	 * @param request
	 * @param response
	 */
	@PostMapping("selectRltClassById")
	@ModDesc(desc = "跟据id查询关系分类", pDesc = "关系分类id", pType = Long.class, rDesc = "关系分类信息", rType = CcCiClassInfo.class)
	public void selectRltClassById(@RequestBody Long rltClassId, HttpServletRequest request,
								HttpServletResponse response) {
		CcCiClassInfo classInfo = rltClassApiSvc.getRltClassById(rltClassId);
		CIClassInfoDto result = BeanUtil.copyProperties(classInfo, CIClassInfoDto.class);
		ESCIAttrDefInfo defInfo = new ESCIAttrDefInfo();
		defInfo.setId(0L);
		defInfo.setIsCiDisp(0);
		defInfo.setIsMajor(0);
		defInfo.setIsRequired(0);
		defInfo.setProType(3);
		defInfo.setLineLabelAlign(1);
		defInfo.setOrderNo(99999);
		defInfo.setDataStatus(1);
		defInfo.setForbidden(true);
		defInfo.setProName("所属用户");
		defInfo.setProStdName("所属用户");
		if(CollectionUtils.isEmpty(result.getAttrDefs())){
			result.setAttrDefs(new ArrayList<>());
		}
		result.getAttrDefs().add(defInfo);
		ControllerUtils.returnJson(request, response, result);
	}

	/**
	 * 导入属性模板
	 * 
	 * @param file
	 * @param rltClassId
	 * @param request
	 * @param response
	 */
	@ApiOperation(value="导入关系属性")
	@PostMapping("importAttrDefs")
    @ModDesc(desc = "导入关系属性", pDesc = "属性文件及关系分类id", rDesc = "关系分类数据", rType = ESCIClassInfo.class)
	public ApiResult<ESCIClassInfo> importAttrDefs(@RequestParam("file") MultipartFile file, @RequestParam("rltClassId") Long rltClassId,
			HttpServletRequest request, HttpServletResponse response) {
		ESCIClassInfo results = rltClassApiSvc.importAttrDefs(file, rltClassId);
		return ApiResult.ok(this).data(results);
	}

	/**
	 * 导出属性模板
	 * 
	 * @param rltClassIds
	 * @param request
	 * @param response
	 */
	@ApiOperation(value="导出关系分类属性",notes="- 导出关系分类属性 \r\n -传入参数关系分类id集合 \r\n - 输出关系属性导出文件")
	@RequestMapping(value="exportAttrDefs",method = RequestMethod.POST)
    @ModDesc(desc = "导出关系分类属性", pDesc = "关系分类id集合", pType = Set.class, pcType = Long.class, rDesc = "关系属性导出文件", rType = Resource.class)
	public void exportAttrDefs(@RequestParam(value = "rltClassIds", required = false) Set<Long> rltClassIds,
			HttpServletRequest request, HttpServletResponse response) {
		Resource results = rltClassApiSvc.exportAttrDefs(rltClassIds);
		ControllerUtils.returnResource(request, response, results);
	}

	/**
	 * 关系拖动排序
	 * @author: weixuesong
	 * @date: 2020/8/6 13:43
	 * @param request
	 * @param response
	 * @param reOrderDTO
	 * @return: void
	 */
	@ApiOperation(value="关系分类排序")
	@RequestMapping(value = "/reOrder",method = RequestMethod.POST)
    @ModDesc(desc = "关系分类排序", pDesc = "关系分类的排序参数", pType = ClassReOrderDTO.class, rDesc = "操作是否成功", rType = Boolean.class)
	public ApiResult<Boolean> reOrder(HttpServletRequest request, HttpServletResponse response, @RequestBody ClassReOrderDTO reOrderDTO) {
		SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
		reOrderDTO.setDomainId(currentUserInfo.getDomainId());
		boolean flag = rltClassApiSvc.reOrder(reOrderDTO);
		return ApiResult.ok(this).data(flag);
	}

	/**
	 * 初始化所有关系的orderNo
	 * @author: weixuesong
	 * @date: 2020/8/6 17:00
	 * @return: boolean
	 */
	@ApiOperation(value="关系分类排序初始化")
	@RequestMapping(value = "/initAllOrderNo",method = RequestMethod.POST)
    @ModDesc(desc = "关系分类排序初始化", pDesc = "暂无参数", rDesc = "操作是否成功", rType = Boolean.class)
	public ApiResult<Boolean> initAllOrderNo(HttpServletRequest request, HttpServletResponse response) {
		SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
		return ApiResult.ok(this).data(rltClassApiSvc.initAllOrderNo(currentUserInfo.getDomainId()));
	}
}
