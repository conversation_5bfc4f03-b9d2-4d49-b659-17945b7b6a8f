package com.uinnova.product.eam.base.diagram.mix.model;

/**
 * 模板目录信息
 */
public class TemplateDirQuery {

    private Long id;

    private Long[] ids;

    // 1：标准类型 2：个人类型
    private Integer type;

    private Long[] parentIds;

    private String dirName;

    private Long domainId;

    public Long getDomainId() {
        return domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getDirName() {
        return dirName;
    }

    public void setDirName(String dirName) {
        this.dirName = dirName;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Long[] getIds() {
        return ids;
    }

    public void setIds(Long[] ids) {
        this.ids = ids;
    }

    public Long[] getParentIds() {
        return parentIds;
    }

    public void setParentIds(Long[] parentIds) {
        this.parentIds = parentIds;
    }
}
