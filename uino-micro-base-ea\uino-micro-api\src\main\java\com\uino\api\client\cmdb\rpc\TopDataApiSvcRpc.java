package com.uino.api.client.cmdb.rpc;

import com.uino.dao.BaseConst;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.uino.provider.feign.cmdb.TopDataFeign;
import com.uino.api.client.cmdb.ITopDataApiSvc;

@Service
public class TopDataApiSvcRpc implements ITopDataApiSvc {

    @Autowired
    private TopDataFeign topDataFeign;

    @Override
    public void unTop(Long topData, Long topDataType) {
        // TODO Auto-generated method stub
        topDataFeign.unTop(BaseConst.DEFAULT_DOMAIN_ID,topData, topDataType);
    }

    @Override
    public void unTop(Long domainId, Long topData, Long topDataType) {
        topDataFeign.unTop(domainId,topData, topDataType);
    }

    @Override
    public void top(Long topData, Long topDataType) {
        // TODO Auto-generated method stub
        topDataFeign.top(BaseConst.DEFAULT_DOMAIN_ID,topData, topDataType);
    }

    @Override
    public void top(Long domainId, Long topData, Long topDataType) {
        topDataFeign.top(domainId,topData, topDataType);
    }
}
