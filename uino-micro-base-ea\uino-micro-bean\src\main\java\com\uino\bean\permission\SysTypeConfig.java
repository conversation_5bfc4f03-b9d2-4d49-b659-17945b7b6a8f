package com.uino.bean.permission;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SysTypeConfig {

    @ApiModelProperty(value="id",example = "123")
    private Long id;

    @ApiModelProperty(value="类型(1-SaaS试用版，2-标准版，3-SaaS正式版)",example = "1")
    private Integer type;

    @ApiModelProperty(value="domainId")
    private Long domainId;

    @ApiModelProperty(value="创建时间")
    private Long createTime;

    @ApiModelProperty(value="修改时间")
    private Long modifyTime;
}
