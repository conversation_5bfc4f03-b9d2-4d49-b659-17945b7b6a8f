package com.uino.api.client.sys;

import com.binary.jdbc.Page;
import com.uino.bean.sys.base.SysLoginLog;
import com.uino.bean.sys.business.QueryLoginLogRequestDto;

/**
 * <AUTHOR>
 */
public interface ILogApiSvc {
    /**
     * 增加一条登陆日志
     *
     * @param userId
     * @return
     */
    public SysLoginLog addLoginLog(Long userId);

    /**
     * 增加一条登陆日志
     *
     * @param userCode
     * @return
     */
    public SysLoginLog addLoginLog(String userCode);
    public SysLoginLog addLoginLog(Long domainId, String userCode);

    /**
     * 查询登陆日志
     *
     * @param query
     * @return
     */
    public Page<SysLoginLog> queryLoginLog(QueryLoginLogRequestDto query);

}
