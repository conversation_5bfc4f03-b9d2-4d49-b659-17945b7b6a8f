package com.uinnova.product.eam.comm.model;




import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;


@Comment("元模型管理视图[VC_META_MODEL_DIAGRAM]")
public class VcMetaModelDiagram implements EntityBean {
	private static final long serialVersionUID = 1L;


	@Comment("ID[ID]")
	private Long id;


	@Comment("模型名称[NAME]")
	private String name;


	@Comment("用户ID[USER_ID]")
	private Long userId;


	@Comment("是否发布[IS_OPEN]    是否发布0=否 1=是")
	private Integer isOpen;


	@Comment("模型JSON[MODEL_JSON]")
	private String modelJson;


	@Comment("所属域[DOMAIN_ID]")
	private Long domainId;


	@Comment("数据状态[DATA_STATUS]    数据状态:0=删除，1=正常")
	private Integer dataStatus;


	@Comment("创建人[CREATOR]")
	private String creator;


	@Comment("修改人[MODIFIER]")
	private String modifier;


	@Comment("创建时间[CREATE_TIME]    创建时间:yyyyMMddHHmmss")
	private Long createTime;


	@Comment("更新时间[MODIFY_TIME]    更新时间:yyyyMMddHHmmss")
	private Long modifyTime;




	public Long getId() {
		return this.id;
	}
	public void setId(Long id) {
		this.id = id;
	}


	public String getName() {
		return this.name;
	}
	public void setName(String name) {
		this.name = name;
	}


	public Long getUserId() {
		return this.userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}


	public Integer getIsOpen() {
		return this.isOpen;
	}
	public void setIsOpen(Integer isOpen) {
		this.isOpen = isOpen;
	}


	public String getModelJson() {
		return this.modelJson;
	}
	public void setModelJson(String modelJson) {
		this.modelJson = modelJson;
	}


	public Long getDomainId() {
		return this.domainId;
	}
	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}


	public Integer getDataStatus() {
		return this.dataStatus;
	}
	public void setDataStatus(Integer dataStatus) {
		this.dataStatus = dataStatus;
	}


	public String getCreator() {
		return this.creator;
	}
	public void setCreator(String creator) {
		this.creator = creator;
	}


	public String getModifier() {
		return this.modifier;
	}
	public void setModifier(String modifier) {
		this.modifier = modifier;
	}


	public Long getCreateTime() {
		return this.createTime;
	}
	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}


	public Long getModifyTime() {
		return this.modifyTime;
	}
	public void setModifyTime(Long modifyTime) {
		this.modifyTime = modifyTime;
	}


}


