package com.uinnova.product.eam.web.bm.mvc;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.binary.core.util.BinaryUtils;
import com.binary.framework.web.RemoteResult;
import com.uinnova.product.eam.base.model.ModuleFieldInfo;
import com.uinnova.product.eam.base.model.SpecificationInfo;
import com.uinnova.product.eam.web.bm.bean.ReleaseModuleDiagramDTO;
import com.uinnova.product.eam.web.bm.peer.BmModulePeer;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uinnova.product.vmdb.provider.rlt.bean.CcCiRltInfo;
import com.uino.bean.cmdb.base.ESCIInfo;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 业务组件建模相关接口
 * <AUTHOR>
 */
@RestController
@RequestMapping("/bm/module")
public class BmModuleMvc {

    @Resource
    BmModulePeer bmModulePeer;

    /**
     * 查询组件定义图中任务所属领域
     * @return
     */
    @PostMapping("/queryBelongToDomainByTask")
    public RemoteResult queryBelongToDomainByTask(@RequestBody String diagramId) {
        List<ESCIInfo> domainList = bmModulePeer.queryBelongToDomainByTask(diagramId);
        return new RemoteResult(domainList);
    }

    /**
     * 领域组件说明书
     *
     * @return
     */
    @GetMapping("/domainAndModuleSpecification")
    public RemoteResult domainAndModuleSpecification(@RequestParam String diagramId) {
        SpecificationInfo result = bmModulePeer.domainAndModuleSpecification(diagramId);
        return new RemoteResult(result);
    }

    @PostMapping("/releaseModuleDiagram")
    public RemoteResult releaseModuleDiagram(@RequestBody ReleaseModuleDiagramDTO diagramDTO) {
        String result = bmModulePeer.releaseModuleDiagram(diagramDTO);
        return new RemoteResult(result);
    }

    @GetMapping("/createTestAndEntityRltByTestCiCode")
    public RemoteResult createTestAndEntityRltByTestCiCode(@RequestParam String ciCode) {
        String result = bmModulePeer.createTestAndEntityRltByTestCiCode(ciCode);
        return new RemoteResult(result);
    }

    @GetMapping("/getTargetCiAndRltBySourceCiCode")
    public RemoteResult getTargetCiAndRltBySourceCiCode(@RequestParam String ciCode,@RequestParam String ownerCode) {
        List<CcCiRltInfo> result = bmModulePeer.getTargetCiAndRltBySourceCiCode(ciCode,ownerCode);
        return new RemoteResult(result);
    }

    @PostMapping("/quickDrawing")
    @ModDesc(desc = "业务组件与领域关系图自动成图", pDesc = "视图id", rDesc = "对象信息", rType = RemoteResult.class)
    public RemoteResult quickDrawing(@RequestBody String body) {
        JSONObject json = JSON.parseObject(body);
        String diagramId = json.getString("diagramId");
        String ciCode = json.getString("ciCode");
        ModuleFieldInfo result = bmModulePeer.quickDrawing(diagramId, ciCode);
        return new RemoteResult(result);
    }

    @PostMapping("/checkModuleDomain")
    @ModDesc(desc = "校验业务组件所属领域", pDesc = "业务组件ciCode", rDesc = "校验结果", rType = RemoteResult.class)
    public RemoteResult checkModuleDomain(@RequestBody String body) {
        JSONObject jsonObject = JSON.parseObject(body);
        List<String> ciCodes = jsonObject.getJSONArray("ciCodes").toJavaList(String.class);
        String source = jsonObject.getString("source");
        BinaryUtils.checkEmpty(source, "source");
        Map<String, Boolean> result = bmModulePeer.checkModuleDomain(source, ciCodes);
        return new RemoteResult(result);
    }
}
