package com.uino.bean.permission.business.request;

import java.util.Set;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.util.Assert;

import com.uino.bean.permission.business.IValidDto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 设置一些用户的组织
 * 
 * <AUTHOR>
 *
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value="设置用户的组织",description = "设置用户的组织")
public class SetUsersOrgsRequestDto implements IValidDto {
	@ApiModelProperty(value="用户id集合")
	private Set<Long> userIds;

	@ApiModelProperty(value="组织id集合")
	private Set<Long> orgIds;

	@ApiModelProperty(value="所属域id")
	private Long domainId;

	@Override
	public void valid() {
		Assert.notEmpty(userIds, "userIds not null");
		Assert.notEmpty(orgIds, "orgIds not null");

	}
}
