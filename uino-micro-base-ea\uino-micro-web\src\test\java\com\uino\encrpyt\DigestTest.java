package com.uino.encrpyt;

import com.uino.StartBaseWebAppliaction;
import com.uino.util.digest.Digest;
import com.uino.util.digest.DigestType;
import com.uino.util.encrypt.EncryptType;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;

/**
 * @Title: Digest
 * @Description:
 * @Author: YGQ
 * @Create: 2021-08-09 11:34
 **/
@RunWith(SpringRunner.class)
@WebAppConfiguration
@SpringBootTest(classes = {StartBaseWebAppliaction.class})
@ActiveProfiles("provider-local-dev")
@AutoConfigureMockMvc(addFilters = false)
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class DigestTest {

    @Autowired
    private Digest digest;

    @Test
    public void md2() {
        System.out.println(digest.calculate(DigestType.MD2, "0123456789123456"));
    }

    @Test
    public void md5() {
        System.out.println(digest.calculate(DigestType.MD5, "0123456789123456"));
    }

    @Test
    public void sha1() {
        System.out.println(digest.calculate(DigestType.SHA1, "0123456789123456"));
    }

    @Test
    public void sha256() {
        System.out.println(digest.calculate(DigestType.SHA256, "0123456789123456"));
    }

    @Test
    public void sha384() {
        System.out.println(digest.calculate(DigestType.SHA384, "0123456789123456"));
    }

    @Test
    public void sha512() {
        System.out.println(digest.calculate(DigestType.SHA512, "0123456789123456"));
    }
}
