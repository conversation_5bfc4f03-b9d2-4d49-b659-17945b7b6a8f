package com.uinnova.product.eam.web.eam.mvc;

import com.uinnova.product.eam.feign.client.HealthyFeign;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * description
 *
 * <AUTHOR>
 * @since 2022/2/21 12:28
 */
@RestController
@RequestMapping("/healthy")
public class HealthyController implements HealthyFeign {

    @GetMapping("/ping")
    public String ping(){
        return "pong";
    }
}
