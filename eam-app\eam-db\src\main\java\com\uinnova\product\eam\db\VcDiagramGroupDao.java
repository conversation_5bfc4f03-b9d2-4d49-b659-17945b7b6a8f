package com.uinnova.product.eam.db;


import java.util.List;
import java.util.Map;

import com.uinnova.product.eam.comm.model.CVcDiagramGroup;
import com.uinnova.product.eam.comm.model.VcDiagramGroup;
import com.uinnova.product.eam.db.bean.GroupReadCount;
import com.uinnova.product.eam.db.bean.UserDiagramInfoCount;
import com.uinnova.product.eam.db.support.dao.ComMyBatisBinaryDao;


/**
 * 视图组表[VC_DIAGRAM_GROUP]数据访问对象
 */
public interface VcDiagramGroupDao extends ComMyBatisBinaryDao<VcDiagramGroup, CVcDiagramGroup> {

	
	/**
	 * 按分享的用户分组查询每个用户分享了多少视图
	 * @param domainId
	 * @return
	 * */
	public List<UserDiagramInfoCount> countDiagramByDeployUserId(Long domainId, Long startDate, Long endDate);

	Map<Long, String> selectAllTeamDiagramByUserId(Long userId);

    List<GroupReadCount> countReadByGroupId(Long domianId, Long startDate, Long endDate);
}


