package com.uino.util.sys;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import com.google.errorprone.annotations.Var;
import com.uino.bean.cmdb.base.ESPropertyType;
import org.springframework.util.Assert;

import com.alibaba.fastjson.JSONArray;
import com.binary.core.util.BinaryUtils;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.comm.util.PropertyType;

import lombok.extern.slf4j.Slf4j;

/**
 * 属性校验
 * 
 * <AUTHOR>
 */
@Slf4j
public class CheckAttrUtil {

    public static final int FAILURE = 0;
    public static final int SUCCESS = 1;
    public static final int EMPTY_VAL = 2;
    public static final int OVER_LENGTH = 3;
    public static final int FORMAT_ERROR = 4;
    public static final int MUST_VAL = 5;
    public static final int EXIST = 6;
    public static final int NOT_EXIST = 7;

    public static final Pattern INTEGER_REGEX = Pattern.compile("[0-9]{1,16}");
    public static final Pattern DOUBLE_REGEX1 = Pattern.compile("[0-9]+(([.][0-9]+))?");
    public static final Pattern DOUBLE_REGEX2 = Pattern.compile("[0-9]{1,10}(([.][0-9]{1,4}))?");
    public static final Pattern STRING_REGEX = Pattern.compile("%{2,}|\'");
    public static final Pattern DATE_REGEX = Pattern.compile("[0-9]{4}(\\-|\\/|\\.)[0-9]{1,2}(\\-|\\/|\\.)[0-9]{1,2}( [0-9]{1,2}:[0-9]{1,2}:[0-9]{1,2})?");
    public static  final Pattern LIST_REGEX=Pattern.compile("/^\\s*\\[\\s*[\\s\\S]*\\S\\s*\\]\\s*$/");

    public static DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    /**
     * 日期默认格式
     */
    public static final String DATE_FORMAT_DEFAULT = "yyyy-MM-dd HH:mm:ss";

    /**
     * 验证属性的值是否符合要求-验证必填、约束规则、属性类型
     *
     * @param defs
     * @param attrs
     * @return errMsgs 错误信息集合
     */
    public static Map<String, Integer> validateAttrValType(List<CcCiAttrDef> defs, Map<String, String> attrs) {
        Map<String, Integer> errMap = new HashMap<>();
        if (BinaryUtils.isEmpty(defs)) {
            return errMap;
        }
        Map<String, String> stdMap = toStdMap(attrs);
        for (CcCiAttrDef def : defs) {
            if (BinaryUtils.isEmpty(stdMap.get(def.getProStdName())) && !BinaryUtils.isEmpty(def.getDefVal())) {
                stdMap.put(def.getProStdName(), def.getDefVal());
                attrs.put(def.getProName(), def.getDefVal());
            }
            String attrVal = stdMap.get(def.getProStdName());
            int checkResult = validateAttrValType(def, attrVal);
            switch (checkResult) {
                case SUCCESS:
                    // 日期时间格式化"yyyy-MM-dd HH:mm:ss"
                    if (def.getProType() == PropertyType.DATE.getValue()) {
                        String date = getFormatDate(attrVal, def.getConstraintRule());
                        if(date != null){
                            attrs.put(def.getProName(), date);
                        }
                    }
                    break;
                case EMPTY_VAL:
                    errMap.put("必填属性[" + def.getProName() + "]值为空", EMPTY_VAL);
                    break;
                case OVER_LENGTH:
                    errMap.put("属性[" + def.getProName() + "]值超过类型限定长度", OVER_LENGTH);
                    break;
                case FORMAT_ERROR:
                    errMap.put("属性[" + def.getProName() + "]格式错误", FORMAT_ERROR);
                    break;
                case MUST_VAL:
                    errMap.put("属性[" + def.getProName() + "]缺少属性值", MUST_VAL);
                    break;
                default:
                    errMap.put("属性[" + def.getProName() + "]校验失败", FAILURE);
                    break;
            }
        }
        return errMap;
    }

    /**
     * 验证属性的值是否符合要求-验证必填、约束规则、属性类型 - 仅适用于EA视图发布校验 慎用
     *
     * @param defs
     * @param attrs
     * @return errMsgs 错误信息集合
     */
    public static String validateAttrValTypeForEAPD(List<CcCiAttrDef> defs, Map<String, String> attrs) {
        String errStr = "";
        if (BinaryUtils.isEmpty(defs)) {
            return errStr;
        }
        Map<String, String> stdMap = toStdMap(attrs);
        for (CcCiAttrDef def : defs) {
            if (BinaryUtils.isEmpty(stdMap.get(def.getProStdName())) && !BinaryUtils.isEmpty(def.getDefVal())) {
                stdMap.put(def.getProStdName(), def.getDefVal());
                attrs.put(def.getProName(), def.getDefVal());
            }
            String attrVal = stdMap.get(def.getProStdName());
            int checkResult = validateAttrValType(def, attrVal);
            if (SUCCESS != checkResult) {
                errStr = errStr + "[" + def.getProName() + "]";
            }
        }
        return errStr;
    }

    /**
     *
     * 验证属性的值是否符合要求
     *
     * @param def
     * @param val 被验证的值
     * @return
     */
    public static Integer validateAttrValType(CcCiAttrDef def, String val) {
        if (def == null) {
            throw new NullPointerException("attrDef is null");
        }
        ESPropertyType type = ESPropertyType.valueOf(def.getProType());
        if (BinaryUtils.isEmpty(val)) {
            if (def.getIsRequired() != null && def.getIsRequired().equals(1)) {
                return MUST_VAL;
            } else {
                return SUCCESS;
            }
        }

        int valLength = 200;
        switch (type) {
            case INTEGER_CODE:
            case INTEGER: {
                int longValLength = 15;
                if (val.length() > longValLength) {
                    return OVER_LENGTH;
                }
                if (!INTEGER_REGEX.matcher(val).matches()) {
                    return FORMAT_ERROR;
                }
                return SUCCESS;
            }
            case PREFIX_INTEGER_CODE:
            {
                int longValLength = 15;
                if (val.length() > longValLength) {
                    return OVER_LENGTH;
                }
                return SUCCESS;
            }
            case DOUBLE: {
                if (!DOUBLE_REGEX1.matcher(val).matches()) {
                    return FORMAT_ERROR;
                }
                if (!DOUBLE_REGEX2.matcher(val).matches()) {
                    return OVER_LENGTH;
                }
                return SUCCESS;
            }
            case ENUM: {
                if (val.length() > valLength) {
                    return OVER_LENGTH;
                }
                if (STRING_REGEX.matcher(val).find()) {
                    return FORMAT_ERROR;
                }
                List<String> enumValues = JSONArray.parseArray(def.getEnumValues(), String.class);
                String[] vals = val.split("@@");
                for (String value : vals) {
                    if (!enumValues.contains(value)) {
                        return FORMAT_ERROR;
                    }
                }
                return SUCCESS;
            }
            case DATE: {
                if (val.length() > valLength) {
                    return OVER_LENGTH;
                }
                if (!BinaryUtils.isEmpty(def.getConstraintRule())) {
                    checkDate(val, def.getConstraintRule());
                }
                if (!checkDate(val, null)) {
                    return FORMAT_ERROR;
                }
                return SUCCESS;
            }
            case VARCHAR: {
                if (val.length() > valLength) {
                    return OVER_LENGTH;
                }
                if (!BinaryUtils.isEmpty(def.getConstraintRule())) {
                    checkString(val, def.getConstraintRule());
                }
                // if (CommUtil.STRING_REGEX.matcher(val).find()) {
                // return FORMAT_ERROR;
                // }
                return SUCCESS;
            }
            case LONG_VARCHAR: {
                int longValLength = 1000;
                if (val.length() > longValLength) {
                    return OVER_LENGTH;
                }
                if (!BinaryUtils.isEmpty(def.getConstraintRule())) {
                    checkString(val, def.getConstraintRule());
                }
                // if (CommUtil.STRING_REGEX.matcher(val).find()) {
                // return FORMAT_ERROR;
                // }
                return SUCCESS;
            }
            case ATTACHMENT:
            case CLOB: {
                // int longValLength = 1500;
                // if (val.length() > longValLength) {
                // return OVER_LENGTH;
                // }
                if (!BinaryUtils.isEmpty(def.getConstraintRule())) {
                    checkString(val, def.getConstraintRule());
                }
                return SUCCESS;
            }
            case EXTERNAL_ATTR: {
                if (val.length() > valLength) {
                    return OVER_LENGTH;
                }
                if (STRING_REGEX.matcher(val).find()) {
                    return FORMAT_ERROR;
                }
                return SUCCESS;
            }
            case ENCODE:
            case PERSION:
            case ORGANIZATION:
            case CALCULATE:
            case PERCENT:
            case LINK_CI:
            case LINK_PLAN:
            case DICT:
            case MODEL:
            case PICTURE:
            case INTERFACE:
            case DOCUMENT:
                return SUCCESS;
            default: {
                return FAILURE;
            }
        }
    }

    /**
     * 校验属性约束规则中的时间格式是否合法
     *
     * @param dateFormat 时间格式
     * @return 是否合法
     */
    @SuppressWarnings("unused")
    public static boolean checkDateFormat(String dateFormat) {
        boolean result;
        if (BinaryUtils.isEmpty(dateFormat)) {
            result = true;
        } else {
            try {
                DateFormat format = new SimpleDateFormat(dateFormat);
                result = true;
            } catch (Exception e) {
                result = false;
            }
        }
        return result;
    }

    /**
     * 通过约束规则检查字符串类型属性是否合法
     *
     * @param value 属性值
     * @param rule 约束规则
     * @return
     */
    public static boolean checkString(String value, String rule) {
        boolean result = false;
        if (BinaryUtils.isEmpty(value) || BinaryUtils.isEmpty(rule)) {
            result = true;
        } else if (rule.startsWith("/")) {
            StringBuilder flagString = new StringBuilder();
            for (int i = rule.length() - 1; i >= 0; i--) {
                if (rule.charAt(i) == '/') {
                    break;
                } else {
                    flagString.append(rule.charAt(i));
                }
            }
            String regex = rule.substring(1, rule.length() - (flagString.length() + 1));

            int flags = -1;
            if (flagString.indexOf("i") != -1) {
                flags = Pattern.CASE_INSENSITIVE;
            }
            if (flagString.indexOf("m") != -1) {
                flags = flags | Pattern.MULTILINE;
            }
            if (flags != -1) {
                Pattern p = Pattern.compile(regex, flags);
                Matcher m = p.matcher(value);
                result = m.matches();
            } else {
                result = Pattern.matches(regex, value);
            }

        } else {
            result = Pattern.matches(rule, value);
        }
        return result;
    }

    /**
     * 校验日期是否符合约束规则
     * 
     * @param value 日期值
     * @param rule 日期约束规则，为空则默认
     * @return 是否符合约束规则
     */
    public static boolean checkDate(String value, String rule) {
        if (BinaryUtils.isEmpty(value)) {
            return true;
        }
        boolean result = false;
        try {
            Date date = getDate(value, rule);
            if (!BinaryUtils.isEmpty(date)) {
                result = true;
            }
        } catch (Exception e) {
            log.error("do checkDate format err!", e);
        }
        return result;
    }

    public static Date getDate(String value, String rule) {
        if (BinaryUtils.isEmpty(value)) {
            return null;
        }
        if (!BinaryUtils.isEmpty(rule)) {
            if (rule.contains("Y")) {
                StringBuilder sb = new StringBuilder();
                for (int i = 0; i < rule.length(); i++) {
                    if (rule.charAt(i) == 'Y') {
                        sb.append('y');
                    } else if (rule.charAt(i) == 'D') {
                        sb.append('d');
                    } else {
                        sb.append(rule.charAt(i));
                    }
                }
                rule = sb.toString();
            }
            DateFormat format = new SimpleDateFormat(rule);
            // 严格解析
            format.setLenient(false);
            try {
                return format.parse(value);
            } catch (Exception e) {
                log.error("do checkDate format err!", e);
            }
        }
        return getDateByDefaultRule(value);
    }

    public static Date getDateByDefaultRule(String value) {
        if (BinaryUtils.isEmpty(value)) {
            return null;
        }
        LocalDate localDate = null;
        try {
            localDate = LocalDate.parse(value);
            ZonedDateTime zdt = localDate.atStartOfDay(ZoneId.systemDefault());
            return Date.from(zdt.toInstant());
        } catch (Exception e) {
        }
        if (localDate == null) {
            try {
                LocalDateTime localDateTime = LocalDateTime.parse(value, dateTimeFormatter);
                ZonedDateTime zdt = localDateTime.atZone(ZoneId.systemDefault());
                return Date.from(zdt.toInstant());
            } catch (Exception e) {
                log.error("[" + value + "]转换为日期格式出错！");
            }
        }
        return null;
    }

    /**
     * 日期格式标准化，与validateAttrValType配合使用
     * 
     * @param value
     * @param rule
     * @return
     */
    public static String getFormatDate(String value, String rule) {
        if (BinaryUtils.isEmpty(value)) {
            return null;
        }
        // 与validateAttrValType配合使用，已校验过格式，直接返回
        if (!BinaryUtils.isEmpty(rule)) {
            return value;
        }
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        Date date = getDate(value, null);
        LocalDateTime dateTime = LocalDateTime.ofEpochSecond(date.getTime() / 1000L, 0, ZoneOffset.ofHours(8));
        return dateTime.format(timeFormatter);
    }

    public static String getDateString(Long time, String rule) {
        if (BinaryUtils.isEmpty(time)) {
            return null;
        }
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        if (!BinaryUtils.isEmpty(rule)) {
            timeFormatter = DateTimeFormatter.ofPattern(rule);
        }
        LocalDateTime dateTime = LocalDateTime.ofEpochSecond(time / 1000L, 0, ZoneOffset.ofHours(8));
        return dateTime.format(timeFormatter);
    }


    /**
     * 将日期数据格式化为制定的约束规则的格式
     *
     * @param value 日期值
     * @param rule 日期格式,为空则默认
     * @return 格式化后的日期
     */
    public static String formatDate(String value, String rule) {
        if (BinaryUtils.isEmpty(value)) {
            return null;
        }

        String formatRule = rule;
        if (!BinaryUtils.isEmpty(rule)) {
            // 校验规则
            if (formatRule.contains("Y")) {
                StringBuilder sb = new StringBuilder();
                for (int i = 0; i < formatRule.length(); i++) {
                    if (formatRule.charAt(i) == 'Y') {
                        sb.append('y');
                    } else if (formatRule.charAt(i) == 'D') {
                        sb.append('d');
                    } else {
                        sb.append(formatRule.charAt(i));
                    }
                }
                formatRule = sb.toString();
            }
        } else {
            // 没有规则则默认
            formatRule = DATE_FORMAT_DEFAULT;
        }
        String newValue = null;
        try {
            SimpleDateFormat format = new SimpleDateFormat(formatRule);
            format.setLenient(false);
            Date date = format.parse(value);
            newValue = format.format(date);
        } catch (Exception e) {
            log.error("do formatDate format err!", e);
        }

        return newValue;
    }
    
    public static List<String> getDefaultRuleByType(Integer proType) {
        List<String> res = new ArrayList<>();
        if (proType != null) {
            PropertyType type = PropertyType.valueOf(proType);
            switch (type) {
                case INTEGER:
                    res.add(INTEGER_REGEX.toString());
                    break;
                case DOUBLE:
                    res.add(DOUBLE_REGEX2.toString());
                    break;
                case DATE:
                    res.add("yyyy-MM-dd");
                    res.add(DATE_FORMAT_DEFAULT);
                    break;
                default:
                    break;
            }
        }
        return res;
    }

    /**
     * KEY转换成大写
     * 
     * @param map
     * @return
     */
    public static Map<String, String> toStdMap(Map<String, String> map) {
        Map<String, String> ret = new HashMap<String, String>();
        if (map == null) {
            return ret;
        }
        Set<String> keySet = map.keySet();
        for (String key : keySet) {
            if (key != null) {
                ret.put(key.toUpperCase(), map.get(key));
            } else {
                ret.put(null, map.get(key));
            }
        }
        return ret;
    }

    /**
     * 比较两个CI分类属性定义是否相同，用于修改分类时过滤未修改属性
     * 
     * @param attrDef
     * @return
     */
    public static boolean equalsAttrDef(CcCiAttrDef attrDef, CcCiAttrDef attrDef2) {
        if (attrDef == attrDef2) {
            return true;
        }
        return equalsForModel(attrDef.getId(), attrDef2.getId()) && equalsForModel(attrDef.getProName(), attrDef2.getProName()) && equalsForModel(attrDef.getProType(), attrDef2.getProType())
            && equalsForModel(attrDef.getIsMajor(), attrDef2.getIsMajor()) && equalsForModel(attrDef.getIsRequired(), attrDef2.getIsRequired())
            && equalsForModel(attrDef.getEnumValues(), attrDef2.getEnumValues());
    }

    /**
     * 对比Model中字段值，字符串“”与null相等
     * 
     * @param a
     * @param b
     * @return
     */
    public static boolean equalsForModel(Object a, Object b) {
        if ((a != null && a.getClass() == String.class && "".equals(a) && b == null) || (a == null && b != null && b.getClass() == String.class && "".equals(b))) {
            return true;
        } else {
            return (a == b) || (a != null && a.equals(b));
        }
    }

    /**
     * 根据属性定义按照orderNo排序后获取有序的业务主键属性定义名称
     * 
     * @param attrDefs 分类属性定义集合(非空)
     * @return 有序的业务主键属性定义名称集合
     */
    public static List<String> getCiPKAttrDefNames(List<CcCiAttrDef> attrDefs) {
        Assert.isTrue(!BinaryUtils.isEmpty(attrDefs), "X_PARAM_NOT_NULL${name:attrDefs}");
        // 先排序
        Collections.sort(attrDefs, new Comparator<CcCiAttrDef>() {

            @Override
            public int compare(CcCiAttrDef o1, CcCiAttrDef o2) {
                Integer o1No = o1.getOrderNo();
                Integer o2No = o2.getOrderNo();
                if (o1No == null && o2No == null)
                    return 0;
                if (o1No == null)
                    return 0 - o2No;
                if (o2No == null)
                    return o1No - 0;

                return o1No - o2No;
            }
        });
        List<String> majorAttrDefNames = new ArrayList<String>();
        for (CcCiAttrDef def : attrDefs) {
            if (def.getIsMajor().equals(1)) {
                majorAttrDefNames.add(def.getProStdName());
            }
        }
        return majorAttrDefNames;
    }

    /**
     * 校验属性是否相同
     * 
     * @param newAttrs
     * @param oldAttrs
     * @return
     */
    public static boolean checkAttrMapEqual(Map<String, String> newAttrs, Map<String, String> oldAttrs) {
        if (newAttrs.size() != oldAttrs.size()) {
            return false;
        }
        Map<String, String> checkNewAttrs = newAttrs.entrySet().stream().filter((e) -> !BinaryUtils.isEmpty(e.getValue())).collect(Collectors.toMap((e) -> e.getKey(), (e) -> e.getValue()));
        Map<String, String> checkOldAttrs = oldAttrs.entrySet().stream().filter((e) -> !BinaryUtils.isEmpty(e.getValue())).collect(Collectors.toMap((e) -> e.getKey(), (e) -> e.getValue()));
        Iterator<Map.Entry<String, String>> it = checkOldAttrs.entrySet().iterator();
        while (it.hasNext()) {
            String key = it.next().getKey();
            boolean isDuplicate = (checkOldAttrs.get(key) == null && checkNewAttrs.get(key) == null)
                || (checkOldAttrs.get(key) != null && checkNewAttrs.get(key) != null && checkOldAttrs.get(key).equals(checkNewAttrs.get(key)));
            if (isDuplicate) {
                it.remove();
                checkNewAttrs.remove(key);
            }
        }
        return checkNewAttrs.size() > 0 || checkOldAttrs.size() > 0 ? false : true;
    }

    /**
     * 关系数据校验属性是否相同 是否携带“所属用户”字段
     * @param newAttrs
     * @param oldAttrs
     * @param isCkeckUser
     * @return
     */
    public static boolean checkAttrMapEqual(Map<String, String> newAttrs, Map<String, String> oldAttrs, Boolean isCkeckUser) {

        if (newAttrs == null || oldAttrs == null) {
            return false;
        }

        if (!isCkeckUser) {
            // 无需校验attr中的 “所属用户”字段 直接设置为空
            newAttrs.put("所属用户", "");
            oldAttrs.put("所属用户", "");
        }
        if (newAttrs.size() != oldAttrs.size()) {
            return false;
        }
        Map<String, String> checkNewAttrs = newAttrs.entrySet().stream().filter((e) -> !BinaryUtils.isEmpty(e.getValue())).collect(Collectors.toMap((e) -> e.getKey(), (e) -> e.getValue()));
        Map<String, String> checkOldAttrs = oldAttrs.entrySet().stream().filter((e) -> !BinaryUtils.isEmpty(e.getValue())).collect(Collectors.toMap((e) -> e.getKey(), (e) -> e.getValue()));
        Iterator<Map.Entry<String, String>> it = checkOldAttrs.entrySet().iterator();
        while (it.hasNext()) {
            String key = it.next().getKey();
            boolean isDuplicate = (checkOldAttrs.get(key) == null && checkNewAttrs.get(key) == null)
                    || (checkOldAttrs.get(key) != null && checkNewAttrs.get(key) != null && checkOldAttrs.get(key).equals(checkNewAttrs.get(key)));
            if (isDuplicate) {
                it.remove();
                checkNewAttrs.remove(key);
            }
        }
        return checkNewAttrs.size() > 0 || checkOldAttrs.size() > 0 ? false : true;
    }
}
