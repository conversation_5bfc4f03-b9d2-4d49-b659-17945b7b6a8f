package com.uinnova.product.eam.service.es;

import com.binary.core.exception.MessageException;
import com.uinnova.product.eam.comm.model.es.ActivityOperationData;
import com.uino.dao.AbstractESBaseDao;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.TopHits;
import org.elasticsearch.search.aggregations.metrics.TopHitsAggregationBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/12/30 10:50
 */
@Slf4j
@Service
public class ActivityOperationDao extends AbstractESBaseDao<ActivityOperationData, ActivityOperationData> {
    @Override
    public String getIndex() {
        return "uino_flow_activity_operation_data";
    }

    @Override
    public String getType() {
        return "_doc";
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }

    /**
     * 获取活动运行时长排行榜（按活动ID去重）
     *
     * @param query
     * @param topSize
     * @return
     */
    public List<Map<String, String>> getActivityRunningTimeTopList(QueryBuilder query, int topSize) {
        List<Map<String, String>> mapList = new ArrayList<>();
        RestHighLevelClient c = getClient();
        SearchRequest searchRequest = new SearchRequest(getFullIndexName());
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        // 添加查询条件
        searchSourceBuilder.size(0);
        searchSourceBuilder.query(query);

        // 构建聚合查询，按 活动ID 字段进行分组，并在每个分组下获取运行时长最久的活动
        // 第一步:现将活动按ID分组，取目标(topSize)条数
        TermsAggregationBuilder term = AggregationBuilders
                .terms("activity_types")
                .field("activityId.keyword")
                .size(topSize);

        // 第二步:在按活动运行时长降序排序，并取时长最久的活动
        TopHitsAggregationBuilder top = AggregationBuilders
                .topHits("top_time")
                .sort("duration", SortOrder.DESC)
                .size(1);
        term.subAggregation(top);
        searchSourceBuilder.aggregation(term);
        searchRequest.source(searchSourceBuilder);

        // 执行查询
        SearchResponse searchResponse;
        try {
            searchResponse = c.search(searchRequest, RequestOptions.DEFAULT);
            Aggregations aggregations = searchResponse.getAggregations();

            // 处理查询结果
            Terms topTypesAggregation = aggregations.get("activity_types");
            for (Terms.Bucket bucket : topTypesAggregation.getBuckets()) {
                Map<String, String> map = new HashMap<>(3);
                // 获取对应目标活动的名称与运行时长
                TopHits topHitsAggregation = bucket.getAggregations().get("top_time");
                if (topHitsAggregation.getHits().getTotalHits().value > 0) {
                    String activityName = topHitsAggregation.getHits().getAt(0).getSourceAsMap().get("activityName").toString();
                    String duration = topHitsAggregation.getHits().getAt(0).getSourceAsMap().get("duration").toString();
                    map.put("activityName", activityName);
                    map.put("duration", duration);
                }
                mapList.add(map);
            }
            // 对最终结果按运行时长做降序排序
            mapList.sort((map1, map2) -> {
                Double duration1 = Double.valueOf(map1.get("duration"));
                Double duration2 = Double.valueOf(map2.get("duration"));
                return duration2.compareTo(duration1);
            });
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            throw new MessageException(e.getMessage());
        }
        return mapList;
    }
}

