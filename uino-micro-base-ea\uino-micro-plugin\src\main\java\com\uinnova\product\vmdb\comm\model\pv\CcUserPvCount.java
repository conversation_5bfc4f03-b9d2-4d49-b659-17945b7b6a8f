package com.uinnova.product.vmdb.comm.model.pv;




import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;


@Comment("用户流量统计表[CC_USER_PV_COUNT]")
public class CcUserPvCount implements EntityBean {
	private static final long serialVersionUID = 1L;


	@Comment("ID[ID]")
	private Long id;


	@Comment("DOMAINID[DOMAIN_ID]    DOMAIN_ID")
	private Long domainId;


	@Comment("用户ID[USER_ID]")
	private Long userId;


	@Comment("对象标识[TARGET_CODE]")
	private String targetCode;


	@Comment("对象描述[TARGET_DESC]")
	private String targetDesc;


	@Comment("点击次数[PV_COUNT]")
	private Long pvCount;


	@Comment("数据状态[DATA_STATUS]    数据状态:数据状态：1-正常 0-删除")
	private Integer dataStatus;


	@Comment("创建时间[CREATE_TIME]    创建时间:yyyyMMddHHmmss")
	private Long createTime;


	@Comment("修改时间[MODIFY_TIME]    修改时间:yyyyMMddHHmmss")
	private Long modifyTime;




	public Long getId() {
		return this.id;
	}
	public void setId(Long id) {
		this.id = id;
	}


	public Long getDomainId() {
		return this.domainId;
	}
	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}


	public Long getUserId() {
		return this.userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}


	public String getTargetCode() {
		return this.targetCode;
	}
	public void setTargetCode(String targetCode) {
		this.targetCode = targetCode;
	}


	public String getTargetDesc() {
		return this.targetDesc;
	}
	public void setTargetDesc(String targetDesc) {
		this.targetDesc = targetDesc;
	}


	public Long getPvCount() {
		return this.pvCount;
	}
	public void setPvCount(Long pvCount) {
		this.pvCount = pvCount;
	}


	public Integer getDataStatus() {
		return this.dataStatus;
	}
	public void setDataStatus(Integer dataStatus) {
		this.dataStatus = dataStatus;
	}


	public Long getCreateTime() {
		return this.createTime;
	}
	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}


	public Long getModifyTime() {
		return this.modifyTime;
	}
	public void setModifyTime(Long modifyTime) {
		this.modifyTime = modifyTime;
	}


}


