package com.uino.bean.cmdb.base.dataset.relation;

import java.util.List;

public class RelationRuleLineExternal {

	private String className;
	private Long nodeStartId;
	private Long nodeEndId;
	
	private Boolean direction;
	private List<RelationRuleAttrCdtExternal> cdts;
	private String lineOp;
	private String lineOpValue;
	
	public String getClassName() {
		return className;
	}
	public void setClassName(String className) {
		this.className = className;
	}
	public Long getNodeStartId() {
		return nodeStartId;
	}
	public void setNodeStartId(Long nodeStartId) {
		this.nodeStartId = nodeStartId;
	}
	public Long getNodeEndId() {
		return nodeEndId;
	}
	public void setNodeEndId(Long nodeEndId) {
		this.nodeEndId = nodeEndId;
	}
	public Boolean getDirection() {
		return direction;
	}
	public void setDirection(Boolean direction) {
		this.direction = direction;
	}
	public List<RelationRuleAttrCdtExternal> getCdts() {
		return cdts;
	}
	public void setCdts(List<RelationRuleAttrCdtExternal> cdts) {
		this.cdts = cdts;
	}
	public String getLineOp() {
		return lineOp;
	}
	public void setLineOp(String lineOp) {
		this.lineOp = lineOp;
	}
	public String getLineOpValue() {
		return lineOpValue;
	}
	public void setLineOpValue(String lineOpValue) {
		this.lineOpValue = lineOpValue;
	}
	
}
