package com.uinnova.product.eam.web.eam.mvc;

import com.binary.core.util.BinaryUtils;
import com.binary.framework.util.ControllerUtils;
import com.binary.framework.web.RemoteResult;
import com.google.common.collect.Lists;
import com.uinnova.product.eam.base.diagram.model.ESDiagramInfoDTO;
import com.uinnova.product.eam.base.model.BaseQueryDiagramDto;
import com.uinnova.product.eam.model.DiagramBo;
import com.uinnova.product.eam.model.EamCategoryCdt;
import com.uinnova.product.eam.model.diagram.SpaceResourceResultInfo;
import com.uinnova.product.eam.model.dto.DiagramSelectParamDto;
import com.uinnova.product.eam.model.dto.EamResourceDetail;
import com.uinnova.product.eam.model.dto.LaneDragCiDto;
import com.uinnova.product.eam.model.vo.DiagramSelectVo;
import com.uinnova.product.eam.service.ICISwitchSvc;
import com.uinnova.product.eam.service.IEamDiagramSvcV2;
import com.uinnova.product.eam.web.bm.bean.DiagramQueryVO;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uino.bean.cmdb.base.LibType;
import com.uino.web.auth.VerifyAuthUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * eam视图相关接口
 * <AUTHOR>
 */
@RestController
@RequestMapping("/eam")
public class EamDiagramMvc {
    public static final List<String> SYS_MODULE_SIGN = Lists.newArrayList("资产仓库-新版", "设计资产管理","架构设计");
    @Autowired
    private ICISwitchSvc ciSwitchSvc;
    @Resource
    private IEamDiagramSvcV2 eamDiagramSvc;
    @Resource
    VerifyAuthUtil verifyAuth;


    @PostMapping("/diagram/createDiagram")
    @ModDesc(desc = "新建视图", pDesc = "", rDesc = "视图信息", rType = RemoteResult.class)
    public RemoteResult createDiagram(@RequestBody ESDiagramInfoDTO diagramDto) {
        Map<String, String> resultMap = eamDiagramSvc.createDiagram(diagramDto);
        return new RemoteResult(resultMap);
    }

    @PostMapping("/diagram/createDiagramWithData")
    @ModDesc(desc = "新建视图-带数据", pDesc = "", rDesc = "视图信息", rType = RemoteResult.class)
    public RemoteResult createDiagramWithData(@RequestBody ESDiagramInfoDTO diagramDto) {
        Map<String, String> resultMap = eamDiagramSvc.createDiagramWithData(diagramDto);
        return new RemoteResult(resultMap);
    }

    @PostMapping("/diagram/getDiagramByDirId")
    @ModDesc(desc = "通过目录id获取视图、系统、方案等资源信息", pDesc = "查询参数", rDesc = "视图、系统、方案信息", rType = RemoteResult.class)
    public RemoteResult getDiagramByDirId(@RequestBody BaseQueryDiagramDto dto) {
        verifyAuth.verifyAuth(SYS_MODULE_SIGN);
        BinaryUtils.checkEmpty(dto.getId(), "目录id");
        SpaceResourceResultInfo result = eamDiagramSvc.getDiagramByDirId(dto);
        return new RemoteResult(result);
    }

    @PostMapping("/diagram/ci/remove")
    @ModDesc(desc = "删除画布ci", pDesc = "查询参数", rDesc = "视图、系统、方案信息", rType = RemoteResult.class)
    public RemoteResult removeCiInDiagram(@RequestBody EamCategoryCdt cdt) {
        BinaryUtils.checkEmpty(cdt.getCiCodes(), "ciCode不能为空");
        BinaryUtils.checkEmpty(cdt.getDiagramId(), "视图id不能为空");
        Integer result = eamDiagramSvc.removeCiInDiagram(cdt);
        return new RemoteResult(result);
    }

    @PostMapping("/diagram/detail")
    @ModDesc(desc = "文件夹、视图or方案详情", pDesc = "文件夹、视图or方案id", rDesc = "视图、系统、方案详情信息", rType = RemoteResult.class)
    public RemoteResult getDiagramByDirId(@RequestBody EamCategoryCdt cdt) {
        EamResourceDetail result = eamDiagramSvc.detail(cdt);
        return new RemoteResult(result);
    }

    @PostMapping("/diagram/openNextDiagram")
    @ModDesc(desc = "通过视图ci跳转对应下级视图", pDesc = "", rDesc = "视图信息", rType = RemoteResult.class)
    public RemoteResult openNextDiagram(@RequestBody EamCategoryCdt params) {
        BinaryUtils.checkEmpty(params.getDiagramId(), "视图id不能为空");
        ESDiagramInfoDTO esDiagram = eamDiagramSvc.openNextDiagram(params);
        return new RemoteResult(esDiagram);
    }

    @PostMapping("/diagram/selectClassByArtifact")
    @ModDesc(desc = "在画布中查询绑定制品中的关系目标端元素", pDesc = "", rDesc = "", rType = RemoteResult.class)
    public RemoteResult selectClassByArtifact(@RequestBody DiagramSelectParamDto paramDto) {
        List<DiagramSelectVo> list = ciSwitchSvc.selectClassByArtifact(paramDto);
        return new RemoteResult(list);
    }

    @GetMapping("/diagram/selectRelationDiagramByCi")
    @ModDesc(desc = "查询视图中超文本链接-ci绑定的当前目录的主视图", pDesc = "", rDesc = "", rType = RemoteResult.class)
    public RemoteResult selectRelationDiagramByCi(@RequestParam String ciCode,
                                                  @RequestParam String diagramId,
                                                  @RequestParam Integer dirType,
                                                  @RequestParam LibType libType
    ) {
        return new RemoteResult(ciSwitchSvc.selectRelationDiagramByCi(ciCode,diagramId,dirType,libType));
    }

    @PostMapping("/diagram/judgeLaneDragCi")
    @ModDesc(desc = "泳道图中拖拽ci 创建关系校验接口", pDesc = "", rDesc = "", rType = RemoteResult.class)
    public RemoteResult judgeLaneDragCi(@RequestBody @Validated LaneDragCiDto ciDto) {
        Boolean result = ciSwitchSvc.judgeLaneDragCi(ciDto);
        return new RemoteResult(result);
    }

    @PostMapping("/epdiagram/getPublishDiagramList")
    public void getPublishDiagramList(HttpServletRequest request, HttpServletResponse response, @RequestBody DiagramQueryVO diagramQueryVO){
        DiagramBo res = eamDiagramSvc.getPublishDiagramList(diagramQueryVO.getLike(),diagramQueryVO.getPageNum(),diagramQueryVO.getPageSize());
        ControllerUtils.returnJson(request, response, res);
    }

    /**
     * 根据ciCode查询绑定的视图列表
     * @param ciCode
     * @return
     */
    @GetMapping("/diagram/getSysLinkDigramByCiCode")
    public RemoteResult getSysLinkDigramByCiCode(String ciCode) {
        return new RemoteResult(eamDiagramSvc.getSysLinkDigramByCiCode(ciCode));
    }

}
