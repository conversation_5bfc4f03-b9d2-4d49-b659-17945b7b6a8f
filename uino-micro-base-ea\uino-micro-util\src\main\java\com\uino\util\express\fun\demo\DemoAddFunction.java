package com.uino.util.express.fun.demo;

import com.googlecode.aviator.runtime.function.FunctionUtils;
import com.googlecode.aviator.runtime.type.AviatorDouble;
import com.googlecode.aviator.runtime.type.AviatorObject;
import com.uino.util.express.annotation.UinoAviatorFunction;
import com.uino.util.express.annotation.UinoAviatorParam;
import com.uino.util.express.common.FunctionTagConst;
import com.uino.util.express.common.UinoAbstractAviatorFunction;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
@UinoAviatorFunction(name = "inner.demo.add", desc = "Add two numbers, demo", returnType = Double.class, tags = FunctionTagConst.DEMO)
public class DemoAddFunction extends UinoAbstractAviatorFunction {

    @Override
    public AviatorObject call(Map<String, Object> env, @UinoAviatorParam(type = Double.class, desc = "数字") AviatorObject arg1,
                              @UinoAviatorParam(type = Double.class, desc = "数字") AviatorObject arg2) {
        Number left = FunctionUtils.getNumberValue(arg1, env);
        Number right = FunctionUtils.getNumberValue(arg2, env);
        return new AviatorDouble(left.doubleValue() + right.doubleValue());
    }

    @Override
    public AviatorObject call(Map<String, Object> env, @UinoAviatorParam(type = Double.class, desc = "数字") AviatorObject a,
                              @UinoAviatorParam(type = Double.class, desc = "数字") AviatorObject b,
                              @UinoAviatorParam(type = Double.class, desc = "数字") AviatorObject c) {
        Number aN = FunctionUtils.getNumberValue(a, env);
        Number bN = FunctionUtils.getNumberValue(b, env);
        Number cN = FunctionUtils.getNumberValue(c, env);
        return new AviatorDouble(aN.doubleValue() + bN.doubleValue() + cN.doubleValue());
    }


//    @Override
//    public String getName() {
//        return "inner.demo.add";
//    }
}
