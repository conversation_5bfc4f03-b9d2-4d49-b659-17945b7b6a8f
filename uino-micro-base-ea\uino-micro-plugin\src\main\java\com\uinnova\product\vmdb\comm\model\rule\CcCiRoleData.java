package com.uinnova.product.vmdb.comm.model.rule;

import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;

/**
 * 
 * <AUTHOR>
 *
 */
@Comment("数据权限表[CC_CI_ROLE_DATA]")
public class CcCiRoleData implements EntityBean {
    private static final long serialVersionUID = 1L;

    @Comment("ID[ID]")
    private Long id;

    @Comment("角色ID[ROLE_ID]")
    private Long roleId;

    @Comment("数据权限ID[DATA_RES_ID]")
    private Long dataResId;

    @Comment("数据权限类型[DATA_RES_TYPE]    1=标签 2=CI分类")
    private Integer dataResType;

    @Comment("权限类型[AUTH_TYPE]    权限类型:3位二进制数表示 第1位=查询 第2位=修改 第3位=删除")
    private Integer authType;

    @Comment("所属域[DOMAIN_ID]")
    private Long domainId;

    @Comment("数据状态[DATA_STATUS]    数据状态:数据状态：1-正常 0-删除")
    private Integer dataStatus;

    @Comment("创建时间[CREATE_TIME]    创建时间:yyyyMMddHHmmss")
    private Long createTime;

    @Comment("修改时间[MODIFY_TIME]    修改时间:yyyyMMddHHmmss")
    private Long modifyTime;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getRoleId() {
        return this.roleId;
    }

    public void setRoleId(Long roleId) {
        this.roleId = roleId;
    }

    public Long getDataResId() {
        return this.dataResId;
    }

    public void setDataResId(Long dataResId) {
        this.dataResId = dataResId;
    }

    public Integer getDataResType() {
        return this.dataResType;
    }

    public void setDataResType(Integer dataResType) {
        this.dataResType = dataResType;
    }

    public Integer getAuthType() {
        return this.authType;
    }

    public void setAuthType(Integer authType) {
        this.authType = authType;
    }

    public Long getDomainId() {
        return this.domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }

    public Integer getDataStatus() {
        return this.dataStatus;
    }

    public void setDataStatus(Integer dataStatus) {
        this.dataStatus = dataStatus;
    }

    public Long getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getModifyTime() {
        return this.modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }

}
