package com.uinnova.product.eam.model.vo;

import lombok.Data;

import java.io.Serializable;

@Data
public class DifyDiagram implements Serializable {

    /**
     * 制图类型
     * 1: mermaid  流程图等mermaid类型的视图
     * 2. quickea  制品类型视图
     * 3. plantuml 暂未使用，后续扩展
     */
    private String drawType;

    /**
     * 具体的视图子类型
     * 例如 mermaid类型的视图可能包含以下子类型：
     *   1 流程图（Flowchart）：展示过程、决策和操作流程。
     *   2 序列图（Sequence Diagram）：展示对象之间的交互顺序。
     *   3 甘特图（Gantt Chart）：展示项目计划和进度。
     *   4 词云图（Class Diagram）：展示类的结构和关系。
     *   5 饼图（Pie Chart）：展示数据占比。
     *   6 捷径图（Shortcut）：简单展示快捷方式。
     *   7 状态图（State Diagram）：展示对象状态的转换。
     *   8 用户旅程图（Journey）：展示用户如何与应用程序交互。
     * 制品类型视图可能包含如下子类型：
     *   1. 上下文关系图
     *   2. 技术架构图
     */
    private String diagramType;

    /**
     * 输入的画图指令
     */
    private String diagramContent;
}
