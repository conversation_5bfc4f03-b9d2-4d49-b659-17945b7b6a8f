package com.uino.plugin.classloader;


import com.uino.plugin.bean.OperatePluginDetails;

/**
 * 热加载回调
 */
public interface ClassLoaderAware {

    public default void afterLoaderSuccess(OperatePluginDetails operatePluginDetails) {}

    public default void afterUnLoaderSuccess(OperatePluginDetails operatePluginDetails) {}

    public default void afterLoaderFailed(OperatePluginDetails operatePluginDetails) {}

    public default void afterUnLoaderFailed(OperatePluginDetails operatePluginDetails) {}

}
