package com.uino.api.client.cmdb.local;

import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.comm.model.ci.CCcCi;
import com.uinnova.product.vmdb.comm.model.ci.CcCi;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CiClassSaveInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CiGroupPage;
import com.uinnova.product.vmdb.provider.ci.bean.CiQueryCdt;
import com.uinnova.product.vmdb.provider.search.bean.CcCiSearchPage;
import com.uino.api.client.cmdb.ICIApiSvc;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.base.LibType;
import com.uino.bean.cmdb.base.SaveBatchCIContext;
import com.uino.bean.cmdb.base.SaveType;
import com.uino.bean.cmdb.business.*;
import com.uino.bean.cmdb.query.ESAttrAggBean;
import com.uino.bean.cmdb.query.ESCISearchBean;
import com.uino.dao.BaseConst;
import com.uino.service.cmdb.microservice.ICISvc;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.search.sort.SortBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
@Primary
public class CIApiSvcLocal implements ICIApiSvc {

    @Autowired
    private ICISvc ciSvc;

    @Override
    public CcCiInfo getCiInfoById(Long id) {
        return ciSvc.getCiInfoById(id);
    }

    @Override
    public CiGroupPage queryPageByIndex(Integer pageNum, Integer pageSize, CiQueryCdt cdt, Boolean hasClass) {
        return ciSvc.queryPageByIndex(BaseConst.DEFAULT_DOMAIN_ID, pageNum, pageSize, cdt, hasClass);
    }

    @Override
    public CiGroupPage queryPageByIndex(Long domainId, Integer pageNum, Integer pageSize, CiQueryCdt cdt, Boolean hasClass) {
        return ciSvc.queryPageByIndex(domainId, pageNum, pageSize, cdt, hasClass);
    }

    @Override
    public CiGroupPage queryPageBySearchBean(ESCISearchBean bean, Boolean hasClass) {
        return ciSvc.queryPageBySearchBean(bean, hasClass);
    }

    @Override
    public List<CcCi> queryCiList(Long domainId, CCcCi cdt, String orders, Boolean isAsc) {
        return ciSvc.queryCiList(domainId, cdt, orders, isAsc);
    }

    @Override
    public List<ESCIInfo> queryESCIInfoList(Long domainId, CCcCi cdt, String orders, Boolean isAsc) {
        return ciSvc.queryESCIInfoList(domainId, cdt, orders, isAsc);
    }

    @Override
    public List<CcCiInfo> queryCiInfoList(Long domainId, CCcCi cdt, String orders, Boolean isAsc, Boolean hasClass) {
        return ciSvc.queryCiInfoList(domainId, cdt, orders, isAsc, hasClass);
    }

    @Override
    public Page<CcCiInfo> queryCiInfoPage(Long domainId, Integer pageNum, Integer pageSize, CCcCi cdt, String orders,
            Boolean isAsc, Boolean hasClass) {
        return ciSvc.queryCiInfoPage(domainId, pageNum, pageSize, cdt, orders, isAsc, hasClass);
    }

    @Override
    public CcCiSearchPage searchCIByCdt(int pageNum, int pageSize, CCcCi bean) {
        return ciSvc.searchCIByCdt(pageNum, pageSize, bean);
    }

    @Override
    public Page<ESCIInfo> searchESCIByBean(ESCISearchBean bean) {
        return ciSvc.searchESCIByBean(bean);
    }

    @Override
    public CcCiSearchPage searchCIByBean(ESCISearchBean bean) {
        return ciSvc.searchCIByBean(bean);
    }

    @Override
    public Long saveOrUpdateCI(CcCiInfo ciInfo) {
        return ciSvc.saveOrUpdate(ciInfo);
    }

    @Override
    public Long saveOrUpdateCI(CcCiInfo ciInfo, SaveType saveType) {
        return ciSvc.saveOrUpdate(ciInfo,saveType);
    }

    @Override
    public Map<String, ? extends SaveBatchCIContext> saveOrUpdateBatchCI(List<ESCIInfo> ciList, List<Long> classIds, String ownerCode, String loginCode) {
        return ciSvc.saveOrUpdateBatchCI(ciList, classIds, ownerCode, loginCode);
    }

    @Override
    public Long saveOrUpdateCIExtra(CcCiInfo ciInfo) {
        return ciSvc.saveOrUpdateExtra(ciInfo);
    }

    @Override
    public ImportSheetMessage saveOrUpdateCiBatch(Long domainId, CiClassSaveInfo saveInfo) {
        return ciSvc.saveOrUpdateCiBath(domainId, saveInfo);
    }

    @Override
    public Integer updateESCIInfoBatch(List<ESCIInfo> esCiInfoList) {
        return ciSvc.updateESCIInfoBatch(esCiInfoList);
    }

    @Override
    public Integer removeById(Long id, Long sourceId) {
        return ciSvc.deleteById(id, sourceId);
    }

    @Override
    public Integer removeByIds(List<Long> ciIds, Long sourceId) {
        return ciSvc.removeByIds(ciIds, sourceId);
    }

    @Override
    public Integer removeByPrimaryKeys(List<String> ciPrimaryKeys, Long sourceId) {
        return ciSvc.removeByPrimaryKeys(BaseConst.DEFAULT_DOMAIN_ID, ciPrimaryKeys, sourceId);
    }

    @Override
    public Integer removeByPrimaryKeys(Long domainId, List<String> ciPrimaryKeys, Long sourceId) {
        return ciSvc.removeByPrimaryKeys(domainId, ciPrimaryKeys, sourceId);
    }

    @Override
    public Integer removeByClassId(Long classId, Long sourceId) {
        return ciSvc.removeByClassId(classId, sourceId);
    }

    @Override
    public Integer removeByOwnerCodeAndClassId(Long classId, String ownerCode) {
        return ciSvc.removeByOwnerCodeAndClassId(classId,ownerCode);
    }

    @Override
    public ResponseEntity<byte[]> exportCiOrClass(ExportCiDto exportDto) {
        return ciSvc.exportCiOrClass(exportDto);
    }

    @Override
    public ImportResultMessage importCiByCiClsIds(MultipartFile file, Long classId) {
        return ciSvc.importCiByCiClsIds(file, classId);
    }

    @Override
    public ImportExcelMessage importCiExcel(MultipartFile file) {
        return ciSvc.importCiExcel(file);
    }

    @Override
    public ImportResultMessage importCiByClassBatch(CiExcelInfoDto excelInfoDto) {
        return ciSvc.importCiByClassBatch(BaseConst.DEFAULT_DOMAIN_ID, excelInfoDto, false);
    }

    @Override
    public ImportResultMessage importCiByClassBatch(Long domainId, CiExcelInfoDto excelInfoDto) {
        return ciSvc.importCiByClassBatch(domainId, excelInfoDto, false);
    }

    @Override
    public ImportResultMessage importCiByClassBatchForAddAttr(CiExcelInfoDto excelInfoDto, boolean addAttr) {
        return ciSvc.importCiByClassBatch(BaseConst.DEFAULT_DOMAIN_ID, excelInfoDto, addAttr);
    }

    @Override
    public ImportResultMessage importCiByClassBatchForAddAttr(Long domainId, CiExcelInfoDto excelInfoDto, boolean addAttr) {
        return ciSvc.importCiByClassBatch(domainId, excelInfoDto, addAttr);
    }

    @Override
    public Map<Long, Long> countCiNumGroupClsByQuery(ESCISearchBean bean) {
        return ciSvc.countCiNumGroupClsByQuery(bean);
    }

    @Override
	public Long countByQuery(ESCISearchBean bean) {
		return ciSvc.countByQuery(bean);
	}

	@Override
    public Page<String> getAttrValuesBySearchBean(ESAttrAggBean searchBean) {
        return ciSvc.getAttrValuesBySearchBean(searchBean);
    }

    @Override
    public List<CcCiInfo> getCIInfoListByCIPrimaryKeys(List<List<String>> ciPrimaryKeys) {
        return ciSvc.getCIInfoListByCIPrimaryKeys(BaseConst.DEFAULT_DOMAIN_ID, ciPrimaryKeys);
    }

    @Override
    public List<CcCiInfo> getCIInfoListByCIPrimaryKeys(Long domainId, List<List<String>> ciPrimaryKeys) {
        return ciSvc.getCIInfoListByCIPrimaryKeys(domainId, ciPrimaryKeys);
    }

    @Override
    public List<ESCIInfo> getESCIInfoListByCIPrimaryKeys(List<List<String>> ciPrimaryKeys) {
        return ciSvc.getESCIInfoListByCIPrimaryKeys(BaseConst.DEFAULT_DOMAIN_ID, ciPrimaryKeys);
    }

    @Override
    public List<ESCIInfo> getESCIInfoListByCIPrimaryKeys(Long domainId, List<List<String>> ciPrimaryKeys) {
        return ciSvc.getESCIInfoListByCIPrimaryKeys(domainId, ciPrimaryKeys);
    }

    @Override
    public Page<ESCIInfo> getESCIInfoPageByQuery(int pageNum, int pageSize, QueryBuilder query, List<SortBuilder<?>> sorts, Boolean isHighLight) {
        return ciSvc.getESCIInfoPageByQuery(BaseConst.DEFAULT_DOMAIN_ID, pageNum, pageSize, query, sorts, isHighLight);
    }

    @Override
    public Page<ESCIInfo> getESCIInfoPageByQuery(Long domainId, int pageNum, int pageSize, QueryBuilder query, List<SortBuilder<?>> sorts, Boolean isHighLight) {
        return ciSvc.getESCIInfoPageByQuery(domainId, pageNum, pageSize, query, sorts, isHighLight);
    }


    @Override
    public Integer removeAllCI(QueryBuilder query) {
        return ciSvc.removeAllCI(BaseConst.DEFAULT_DOMAIN_ID, query);
    }

	@Override
	public boolean updateAttrValueBatch(CIAttrValueUpdateDto dto) {
		return ciSvc.updateAttrValueBatch(dto);
	}

    @Override
    public Map<String, Long> queryCiCountByClassId() {
        return ciSvc.queryCiCountByClassId();
    }

    @Override
    public Map<String, ? extends SaveBatchCIContext> copyCiListByIds(List<ESCIInfo> ciList, String ownerCode) {
        return ciSvc.copyCiListByIds(ciList, ownerCode);
    }

    @Override
    public Integer removeAllCI(Long domainId, QueryBuilder query) {
        return ciSvc.removeAllCI(domainId, query);
    }

    @Override
    public boolean modifyAttrValueBatch(CIAttrValueUpdateDto dto){
        return ciSvc.modifyAttrValueBatch(dto);
    }

    @Override
    public Integer removeCiBatch(CIRemoveBatchDto dto){
        return ciSvc.removeCiBatch(dto);
    }
}
