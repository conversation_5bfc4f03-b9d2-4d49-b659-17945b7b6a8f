package com.uinnova.product.vmdb.comm.integration;

import com.binary.core.http.URLResolver;
import com.binary.core.lang.Conver;
import com.binary.core.util.BinaryUtils;
import com.binary.framework.exception.ServiceException;
import com.binary.framework.util.ControllerUtils;
import com.uinnova.product.vmdb.comm.doc.api.MvcApi;
import com.uinnova.product.vmdb.comm.doc.build.DocBuilder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.regex.Pattern;

/**
 *
 * <AUTHOR>
 *
 */
@RequestMapping("/integration/dispatch")
public class DispatchMvc {

    private final Pattern NUM_REGX = Pattern.compile("[0-9]+");

    private String home;

    public String getHome() {
        return home;
    }

    public void setHome(String home) {
        this.home = home;
    }

    @RequestMapping("/main")
    public void main(HttpServletRequest request, HttpServletResponse response) {
        String token = request.getParameter("tk");
        if (token == null) {
            token = (String) request.getAttribute("tk");
        }
        String url = this.home;
        if (token != null) {
            if (!url.contains("?")) {
                url += "?tk=" + token;
            } else {
                url += "&tk=" + token;
            }
        }
        ControllerUtils.sendRedirect(request, response, url);
    }

    @RequestMapping("/mi/**")
    public String openModuById(HttpServletRequest request, HttpServletResponse response) {
        String url = request.getRequestURI();
        int idx = url.indexOf("/mi/");
        if (idx < 0) {
            throw new ServiceException(" is wrong url '" + url + "'! ");
        }
        String id = url.substring(idx + 4).trim();

        if (!NUM_REGX.matcher(id).matches()) {
            throw new ServiceException(" is wrong url '" + url + "'! ");
        }

        return "forward:/sys/frame/cross/modu/openModuleById?parentCascade=true&moduleId=" + id;
    }

    @RequestMapping("/mc/**")
    public String openModuByCode(HttpServletRequest request, HttpServletResponse response) {
        String url = request.getRequestURI();
        int idx = url.indexOf("/mc/");
        if (idx < 0) {
            throw new ServiceException(" is wrong url '" + url + "'! ");
        }
        String code = url.substring(idx + 4).trim();
        return "forward:/sys/frame/cross/modu/openModuleByCode?parentCascade=true&moduleCode=" + code;
    }

    @RequestMapping("/jsp/index")
    public void indexJsp(HttpServletRequest request, HttpServletResponse response) {
        String contextPath = request.getContextPath();
        String url = contextPath + "/integration/authority/verifyLogin";

        String beforeUrl = request.getParameter("beforeUrl");
        if (BinaryUtils.isEmpty(beforeUrl)) {
            beforeUrl = Conver.to(request.getAttribute("beforeUrl"), String.class);
        }

        if (!BinaryUtils.isEmpty(beforeUrl)) {
            char c = '/';
            if (beforeUrl.indexOf(c) >= 0) {
                beforeUrl = URLResolver.encode(beforeUrl);
            }
            url += "?beforeUrl=" + beforeUrl;
        }
        ControllerUtils.sendRedirect(request, response, url);
    }

    @RequestMapping("/jsp/error")
    @ResponseBody
    public void errorJsp(HttpServletRequest request, HttpServletResponse response) throws IOException {
        response.setStatus(500);
//        String contextPath = request.getContextPath();
//        Exception ex = (Exception) request.getAttribute("exception");
//        String refurl = request.getHeader("Referer");
//        if (refurl == null || refurl.length() == 0) {
//            refurl = request.getContextPath() + "/index.jsp";
//        }
//        String msg = "";
//        String fullmsg = "";
//        if (ex != null) {
//            msg = ex.getMessage();
//            fullmsg = ex.toString();
//        }
//
//        response.reset();
//        response.setContentType("text/html; charset=utf-8");
//        PrintWriter pw = response.getWriter();
//
//        pw.write("<!DOCTYPE html>");
//        pw.write("<html>");
//        pw.write("<head>");
//        pw.write("<meta charset=\"utf-8\">");
//        pw.write("<meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\"/>");
//        pw.write("<meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge,chrome=1\"/>");
//        pw.write("<title>异常</title>");
//        pw.write("<link rel=\"shortcut icon\" href=\"" + contextPath + "/img/favicon.png\" type=\"image/x-png\" />");
//        pw.write("<link rel=\"stylesheet\" type=\"text/css\" href=\"" + contextPath + "/frame/bootstrap/css/bootstrap.min.css\"/>");
//        pw.write("<link rel=\"stylesheet\" type=\"text/css\" href=\"" + contextPath + "/frame/centaurus/css/libs/font-awesome.css\"/>");
//        pw.write("<link rel=\"stylesheet\" type=\"text/css\" href=\"" + contextPath + "/frame/centaurus/css/libs/nanoscroller.css\"/>");
//        pw.write("<link rel=\"stylesheet\" type=\"text/css\" href=\"" + contextPath + "/frame/centaurus/css/compiled/theme_styles.css\"/>");
//        pw.write("</head>");
//        pw.write("<body id=\"error-page\">");
//        pw.write("<div class=\"container\">");
//        pw.write("<div class=\"row\">");
//        pw.write("<div class=\"col-xs-12\">");
//        pw.write("<div id=\"error-box\">");
//        pw.write("<div class=\"row\">");
//        pw.write("<div class=\"col-xs-12\">");
//        pw.write("<div id=\"error-box-inner\">");
//        pw.write("<img src=\"" + contextPath + "/frame/centaurus/img/error-500-v1.png\" alt=\"Error 500\"/>");
//        pw.write("</div>");
//        pw.write("<h1>ERROR 500</h1>");
//        pw.write("<p>");
//        pw.write("<font color='red'>" + msg + "</font>");
//        pw.write("</p>");
//        pw.write("<p>");
//        pw.write("<a href=\"###\" id=\"a_back\">返回</a>");
//        pw.write("</p>");
//        pw.write("</div>");
//        pw.write("</div>");
//        pw.write("</div>");
//        pw.write("</div>");
//        pw.write("</div>");
//        pw.write("</div>");
//        pw.write("<div style=\"display:none;\">");
//        pw.write(fullmsg);
//        pw.write("</div>");
//        pw.write("</body>");
//        pw.write("<script type=\"text/javascript\">");
//        pw.write("document.getElementById(\"a_back\").onclick = function() {");
//        pw.write("window.history.back(-1);");
//        pw.write("};");
//        pw.write("</script>");
//        pw.write("</html>");
//
//        pw.flush();
    }

    @RequestMapping("/jsp/noauth")
    public void noauthJsp(HttpServletRequest request, HttpServletResponse response) throws IOException {
        response.setStatus(500);
//        String contextPath = request.getContextPath();
//        String p = request.getParameter("p");
//        String token = request.getParameter("tk");
//        if (p == null) {
//            p = (String) request.getAttribute("p");
//        }
//        if (token == null) {
//            token = (String) request.getAttribute("tk");
//        }
//
//        if (BinaryUtils.isEmpty(p)) {
//            p = "";
//        } else {
//            char c = '%';
//            if (p.indexOf(c) >= 0) {
//                p = URLResolver.decode(p);
//            }
//        }
//        if (BinaryUtils.isEmpty(token)) {
//            token = "";
//        }
//
//        response.setContentType("text/html; charset=utf-8");
//        PrintWriter pw = response.getWriter();
//
//        pw.write("<!DOCTYPE html>");
//        pw.write("<html>");
//        pw.write("<head>");
//        pw.write("<meta charset=\"utf-8\">");
//        pw.write("<meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\"/>");
//        pw.write("<meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge,chrome=1\"/>");
//        pw.write("<title>没有权限</title>");
//        pw.write("</head>");
//        pw.write("<body id=\"error-page\">");
//        pw.write("<h1>ERROR 500</h1>");
//        pw.write("<p>");
//        pw.write("<font color='red'>抱歉! 您没有访问页面'" + p + "'的权限。</font>");
//        pw.write("</p>");
//        pw.write("<p>");
//        pw.write("<a href=\"" + contextPath + "//integration/dispatch/main?tk=" + token + "\" >返回首页</a>");
//        pw.write("&nbsp;&nbsp;&nbsp;&nbsp;");
//        pw.write("<a href=\"" + contextPath + "/index.jsp\" >重新登录</a>");
//        pw.write("</p>");
//        pw.write("</body>");
//        pw.write("</html>");
//
//        pw.flush();
    }

    @RequestMapping("/jsp/api")
    public void apiJsp(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String moduleName = request.getParameter("moduleName");
        String location = "com.uinnova.product";
        if (!BinaryUtils.isEmpty(moduleName)) {
            location += "." + moduleName.trim();
        }
        location += ".**";
        List<MvcApi> apis = DocBuilder.getMvcApi(location);
        ControllerUtils.returnJson(request, response, apis);
    }

}
