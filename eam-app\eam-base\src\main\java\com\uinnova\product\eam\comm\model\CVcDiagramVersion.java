package com.uinnova.product.eam.comm.model;




import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;


@Comment("视图设计版本表[VC_DIAGRAM_VERSION]")
public class CVcDiagramVersion implements Condition {
	private static final long serialVersionUID = 1L;


	@Comment("ID[ID] operate-Equal[=]")
	private Long id;


	@Comment("ID[ID] operate-In[in]")
	private Long[] ids;


	@Comment("ID[ID] operate-GTEqual[>=]")
	private Long startId;

	@Comment("ID[ID] operate-LTEqual[<=]")
	private Long endId;


	@Comment("源视图ID[DIAGRAM_ID] operate-Equal[=]")
	private Long diagramId;


	@Comment("源视图ID[DIAGRAM_ID] operate-In[in]")
	private Long[] diagramIds;


	@Comment("源视图ID[DIAGRAM_ID] operate-GTEqual[>=]")
	private Long startDiagramId;

	@Comment("源视图ID[DIAGRAM_ID] operate-LTEqual[<=]")
	private Long endDiagramId;


	@Comment("版本号[VERSION_NO] operate-Like[like]")
	private String versionNo;


	@Comment("版本号[VERSION_NO] operate-Equal[=]")
	private String versionNoEqual;


	@Comment("版本号[VERSION_NO] operate-In[in]")
	private String[] versionNos;


	@Comment("版本时间[VERSION_TIME] operate-Equal[=]")
	private Long versionTime;


	@Comment("版本时间[VERSION_TIME] operate-In[in]")
	private Long[] versionTimes;


	@Comment("版本时间[VERSION_TIME] operate-GTEqual[>=]")
	private Long startVersionTime;

	@Comment("版本时间[VERSION_TIME] operate-LTEqual[<=]")
	private Long endVersionTime;


	@Comment("版本描述[VERSION_DESC] operate-Like[like]")
	private String versionDesc;


	@Comment("视图名称[NAME] operate-Like[like]")
	private String name;


	@Comment("视图名称[NAME] operate-Equal[=]")
	private String nameEqual;


	@Comment("视图名称[NAME] operate-In[in]")
	private String[] names;


	@Comment("所属用户[USER_ID] operate-Equal[=]")
	private Long userId;


	@Comment("所属用户[USER_ID] operate-In[in]")
	private Long[] userIds;


	@Comment("所属用户[USER_ID] operate-GTEqual[>=]")
	private Long startUserId;

	@Comment("所属用户[USER_ID] operate-LTEqual[<=]")
	private Long endUserId;


	@Comment("所属目录[DIR_ID] operate-Equal[=]")
	private Long dirId;


	@Comment("所属目录[DIR_ID] operate-In[in]")
	private Long[] dirIds;


	@Comment("所属目录[DIR_ID] operate-GTEqual[>=]")
	private Long startDirId;

	@Comment("所属目录[DIR_ID] operate-LTEqual[<=]")
	private Long endDirId;


	@Comment("视图描述[DIAGRAM_DESC] operate-Like[like]")
	private String diagramDesc;


	@Comment("视图SVG[DIAGRAM_SVG] operate-Like[like]")
	private String diagramSvg;


	@Comment("视图XML[DIAGRAM_XML] operate-Like[like]")
	private String diagramXml;


	@Comment("视图JSON[DIAGRAM_JSON] operate-Like[like]    视图json格式信息")
	private String diagramJson;


	@Comment("视图图标_1[ICON_1] operate-Like[like]")
	private String icon1;


	@Comment("视图图标_2[ICON_2] operate-Like[like]")
	private String icon2;


	@Comment("视图图标_3[ICON_3] operate-Like[like]")
	private String icon3;


	@Comment("视图图标_4[ICON_4] operate-Like[like]")
	private String icon4;


	@Comment("视图图标_5[ICON_5] operate-Like[like]")
	private String icon5;


	@Comment("视图状态[STATUS] operate-Equal[=]    视图状态:1=正常 0=回收站")
	private Integer status;


	@Comment("视图状态[STATUS] operate-In[in]    视图状态:1=正常 0=回收站")
	private Integer[] statuss;


	@Comment("视图状态[STATUS] operate-GTEqual[>=]    视图状态:1=正常 0=回收站")
	private Integer startStatus;

	@Comment("视图状态[STATUS] operate-LTEqual[<=]    视图状态:1=正常 0=回收站")
	private Integer endStatus;


	@Comment("CI3D坐标[CI_3D_POINT] operate-Like[like]")
	private String ci3dPoint;


	@Comment("CI3D坐标2[CI_3D_POINT2] operate-Like[like]")
	private String ci3dPoint2;


	@Comment("版本描述文件路径信息[VERSION_DESC_PATH] operate-Like[like]")
	private String versionDescPath;


	@Comment("所属域[DOMAIN_ID] operate-Equal[=]")
	private Long domainId;


	@Comment("所属域[DOMAIN_ID] operate-In[in]")
	private Long[] domainIds;


	@Comment("所属域[DOMAIN_ID] operate-GTEqual[>=]")
	private Long startDomainId;

	@Comment("所属域[DOMAIN_ID] operate-LTEqual[<=]")
	private Long endDomainId;


	@Comment("数据状态[DATA_STATUS] operate-Equal[=]    数据状态:0=删除，1=正常")
	private Integer dataStatus;


	@Comment("数据状态[DATA_STATUS] operate-In[in]    数据状态:0=删除，1=正常")
	private Integer[] dataStatuss;


	@Comment("数据状态[DATA_STATUS] operate-GTEqual[>=]    数据状态:0=删除，1=正常")
	private Integer startDataStatus;

	@Comment("数据状态[DATA_STATUS] operate-LTEqual[<=]    数据状态:0=删除，1=正常")
	private Integer endDataStatus;


	@Comment("创建人[CREATOR] operate-Like[like]")
	private String creator;


	@Comment("创建人[CREATOR] operate-Equal[=]")
	private String creatorEqual;


	@Comment("创建人[CREATOR] operate-In[in]")
	private String[] creators;


	@Comment("修改人[MODIFIER] operate-Like[like]")
	private String modifier;


	@Comment("修改人[MODIFIER] operate-Equal[=]")
	private String modifierEqual;


	@Comment("修改人[MODIFIER] operate-In[in]")
	private String[] modifiers;


	@Comment("创建时间[CREATE_TIME] operate-Equal[=]    创建时间:yyyyMMddHHmmss")
	private Long createTime;


	@Comment("创建时间[CREATE_TIME] operate-In[in]    创建时间:yyyyMMddHHmmss")
	private Long[] createTimes;


	@Comment("创建时间[CREATE_TIME] operate-GTEqual[>=]    创建时间:yyyyMMddHHmmss")
	private Long startCreateTime;

	@Comment("创建时间[CREATE_TIME] operate-LTEqual[<=]    创建时间:yyyyMMddHHmmss")
	private Long endCreateTime;


	@Comment("更新时间[MODIFY_TIME] operate-Equal[=]    更新时间:yyyyMMddHHmmss")
	private Long modifyTime;


	@Comment("更新时间[MODIFY_TIME] operate-In[in]    更新时间:yyyyMMddHHmmss")
	private Long[] modifyTimes;


	@Comment("更新时间[MODIFY_TIME] operate-GTEqual[>=]    更新时间:yyyyMMddHHmmss")
	private Long startModifyTime;

	@Comment("更新时间[MODIFY_TIME] operate-LTEqual[<=]    更新时间:yyyyMMddHHmmss")
	private Long endModifyTime;


	@Comment("视图背景[DIAGRAM_BG_CSS] operate-Like[like]")
	private String diagramBgCss;

	@Comment("关联视图发布id[RELEASE_ID]")
	private Long releaseId;

	@Comment("已发布视图版本[RELEASE_VERSION]")
	private Integer releaseVersion;


	public Long getId() {
		return this.id;
	}
	public void setId(Long id) {
		this.id = id;
	}


	public Long[] getIds() {
		return this.ids;
	}
	public void setIds(Long[] ids) {
		this.ids = ids;
	}


	public Long getStartId() {
		return this.startId;
	}
	public void setStartId(Long startId) {
		this.startId = startId;
	}


	public Long getEndId() {
		return this.endId;
	}
	public void setEndId(Long endId) {
		this.endId = endId;
	}


	public Long getDiagramId() {
		return this.diagramId;
	}
	public void setDiagramId(Long diagramId) {
		this.diagramId = diagramId;
	}


	public Long[] getDiagramIds() {
		return this.diagramIds;
	}
	public void setDiagramIds(Long[] diagramIds) {
		this.diagramIds = diagramIds;
	}


	public Long getStartDiagramId() {
		return this.startDiagramId;
	}
	public void setStartDiagramId(Long startDiagramId) {
		this.startDiagramId = startDiagramId;
	}


	public Long getEndDiagramId() {
		return this.endDiagramId;
	}
	public void setEndDiagramId(Long endDiagramId) {
		this.endDiagramId = endDiagramId;
	}


	public String getVersionNo() {
		return this.versionNo;
	}
	public void setVersionNo(String versionNo) {
		this.versionNo = versionNo;
	}


	public String getVersionNoEqual() {
		return this.versionNoEqual;
	}
	public void setVersionNoEqual(String versionNoEqual) {
		this.versionNoEqual = versionNoEqual;
	}


	public String[] getVersionNos() {
		return this.versionNos;
	}
	public void setVersionNos(String[] versionNos) {
		this.versionNos = versionNos;
	}


	public Long getVersionTime() {
		return this.versionTime;
	}
	public void setVersionTime(Long versionTime) {
		this.versionTime = versionTime;
	}


	public Long[] getVersionTimes() {
		return this.versionTimes;
	}
	public void setVersionTimes(Long[] versionTimes) {
		this.versionTimes = versionTimes;
	}


	public Long getStartVersionTime() {
		return this.startVersionTime;
	}
	public void setStartVersionTime(Long startVersionTime) {
		this.startVersionTime = startVersionTime;
	}


	public Long getEndVersionTime() {
		return this.endVersionTime;
	}
	public void setEndVersionTime(Long endVersionTime) {
		this.endVersionTime = endVersionTime;
	}


	public String getVersionDesc() {
		return this.versionDesc;
	}
	public void setVersionDesc(String versionDesc) {
		this.versionDesc = versionDesc;
	}


	public String getName() {
		return this.name;
	}
	public void setName(String name) {
		this.name = name;
	}


	public String getNameEqual() {
		return this.nameEqual;
	}
	public void setNameEqual(String nameEqual) {
		this.nameEqual = nameEqual;
	}


	public String[] getNames() {
		return this.names;
	}
	public void setNames(String[] names) {
		this.names = names;
	}


	public Long getUserId() {
		return this.userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}


	public Long[] getUserIds() {
		return this.userIds;
	}
	public void setUserIds(Long[] userIds) {
		this.userIds = userIds;
	}


	public Long getStartUserId() {
		return this.startUserId;
	}
	public void setStartUserId(Long startUserId) {
		this.startUserId = startUserId;
	}


	public Long getEndUserId() {
		return this.endUserId;
	}
	public void setEndUserId(Long endUserId) {
		this.endUserId = endUserId;
	}


	public Long getDirId() {
		return this.dirId;
	}
	public void setDirId(Long dirId) {
		this.dirId = dirId;
	}


	public Long[] getDirIds() {
		return this.dirIds;
	}
	public void setDirIds(Long[] dirIds) {
		this.dirIds = dirIds;
	}


	public Long getStartDirId() {
		return this.startDirId;
	}
	public void setStartDirId(Long startDirId) {
		this.startDirId = startDirId;
	}


	public Long getEndDirId() {
		return this.endDirId;
	}
	public void setEndDirId(Long endDirId) {
		this.endDirId = endDirId;
	}


	public String getDiagramDesc() {
		return this.diagramDesc;
	}
	public void setDiagramDesc(String diagramDesc) {
		this.diagramDesc = diagramDesc;
	}


	public String getDiagramSvg() {
		return this.diagramSvg;
	}
	public void setDiagramSvg(String diagramSvg) {
		this.diagramSvg = diagramSvg;
	}


	public String getDiagramXml() {
		return this.diagramXml;
	}
	public void setDiagramXml(String diagramXml) {
		this.diagramXml = diagramXml;
	}


	public String getDiagramJson() {
		return this.diagramJson;
	}
	public void setDiagramJson(String diagramJson) {
		this.diagramJson = diagramJson;
	}


	public String getIcon1() {
		return this.icon1;
	}
	public void setIcon1(String icon1) {
		this.icon1 = icon1;
	}


	public String getIcon2() {
		return this.icon2;
	}
	public void setIcon2(String icon2) {
		this.icon2 = icon2;
	}


	public String getIcon3() {
		return this.icon3;
	}
	public void setIcon3(String icon3) {
		this.icon3 = icon3;
	}


	public String getIcon4() {
		return this.icon4;
	}
	public void setIcon4(String icon4) {
		this.icon4 = icon4;
	}


	public String getIcon5() {
		return this.icon5;
	}
	public void setIcon5(String icon5) {
		this.icon5 = icon5;
	}


	public Integer getStatus() {
		return this.status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}


	public Integer[] getStatuss() {
		return this.statuss;
	}
	public void setStatuss(Integer[] statuss) {
		this.statuss = statuss;
	}


	public Integer getStartStatus() {
		return this.startStatus;
	}
	public void setStartStatus(Integer startStatus) {
		this.startStatus = startStatus;
	}


	public Integer getEndStatus() {
		return this.endStatus;
	}
	public void setEndStatus(Integer endStatus) {
		this.endStatus = endStatus;
	}


	public String getCi3dPoint() {
		return this.ci3dPoint;
	}
	public void setCi3dPoint(String ci3dPoint) {
		this.ci3dPoint = ci3dPoint;
	}


	public String getCi3dPoint2() {
		return this.ci3dPoint2;
	}
	public void setCi3dPoint2(String ci3dPoint2) {
		this.ci3dPoint2 = ci3dPoint2;
	}


	public String getVersionDescPath() {
		return this.versionDescPath;
	}
	public void setVersionDescPath(String versionDescPath) {
		this.versionDescPath = versionDescPath;
	}


	public Long getDomainId() {
		return this.domainId;
	}
	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}


	public Long[] getDomainIds() {
		return this.domainIds;
	}
	public void setDomainIds(Long[] domainIds) {
		this.domainIds = domainIds;
	}


	public Long getStartDomainId() {
		return this.startDomainId;
	}
	public void setStartDomainId(Long startDomainId) {
		this.startDomainId = startDomainId;
	}


	public Long getEndDomainId() {
		return this.endDomainId;
	}
	public void setEndDomainId(Long endDomainId) {
		this.endDomainId = endDomainId;
	}


	public Integer getDataStatus() {
		return this.dataStatus;
	}
	public void setDataStatus(Integer dataStatus) {
		this.dataStatus = dataStatus;
	}


	public Integer[] getDataStatuss() {
		return this.dataStatuss;
	}
	public void setDataStatuss(Integer[] dataStatuss) {
		this.dataStatuss = dataStatuss;
	}


	public Integer getStartDataStatus() {
		return this.startDataStatus;
	}
	public void setStartDataStatus(Integer startDataStatus) {
		this.startDataStatus = startDataStatus;
	}


	public Integer getEndDataStatus() {
		return this.endDataStatus;
	}
	public void setEndDataStatus(Integer endDataStatus) {
		this.endDataStatus = endDataStatus;
	}


	public String getCreator() {
		return this.creator;
	}
	public void setCreator(String creator) {
		this.creator = creator;
	}


	public String getCreatorEqual() {
		return this.creatorEqual;
	}
	public void setCreatorEqual(String creatorEqual) {
		this.creatorEqual = creatorEqual;
	}


	public String[] getCreators() {
		return this.creators;
	}
	public void setCreators(String[] creators) {
		this.creators = creators;
	}


	public String getModifier() {
		return this.modifier;
	}
	public void setModifier(String modifier) {
		this.modifier = modifier;
	}


	public String getModifierEqual() {
		return this.modifierEqual;
	}
	public void setModifierEqual(String modifierEqual) {
		this.modifierEqual = modifierEqual;
	}


	public String[] getModifiers() {
		return this.modifiers;
	}
	public void setModifiers(String[] modifiers) {
		this.modifiers = modifiers;
	}


	public Long getCreateTime() {
		return this.createTime;
	}
	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}


	public Long[] getCreateTimes() {
		return this.createTimes;
	}
	public void setCreateTimes(Long[] createTimes) {
		this.createTimes = createTimes;
	}


	public Long getStartCreateTime() {
		return this.startCreateTime;
	}
	public void setStartCreateTime(Long startCreateTime) {
		this.startCreateTime = startCreateTime;
	}


	public Long getEndCreateTime() {
		return this.endCreateTime;
	}
	public void setEndCreateTime(Long endCreateTime) {
		this.endCreateTime = endCreateTime;
	}


	public Long getModifyTime() {
		return this.modifyTime;
	}
	public void setModifyTime(Long modifyTime) {
		this.modifyTime = modifyTime;
	}


	public Long[] getModifyTimes() {
		return this.modifyTimes;
	}
	public void setModifyTimes(Long[] modifyTimes) {
		this.modifyTimes = modifyTimes;
	}


	public Long getStartModifyTime() {
		return this.startModifyTime;
	}
	public void setStartModifyTime(Long startModifyTime) {
		this.startModifyTime = startModifyTime;
	}


	public Long getEndModifyTime() {
		return this.endModifyTime;
	}
	public void setEndModifyTime(Long endModifyTime) {
		this.endModifyTime = endModifyTime;
	}


	public String getDiagramBgCss() {
		return this.diagramBgCss;
	}
	public void setDiagramBgCss(String diagramBgCss) {
		this.diagramBgCss = diagramBgCss;
	}

	public Long getReleaseId() {
		return releaseId;
	}
	public void setReleaseId(Long releaseId) {
		this.releaseId = releaseId;
	}

	public Integer getReleaseVersion() {
		return releaseVersion;
	}
	public void setReleaseVersion(Integer releaseVersion) {
		this.releaseVersion = releaseVersion;
	}
}


