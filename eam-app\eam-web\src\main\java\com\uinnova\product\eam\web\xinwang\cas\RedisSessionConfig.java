package com.uinnova.product.eam.web.xinwang.cas;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.session.data.redis.config.annotation.web.http.EnableRedisHttpSession;

/**
 * description
 *
 * <AUTHOR>
 * @since 2023/3/1 19:05
 */
@ConditionalOnProperty(prefix = "monet.login", name = "loginMethod", havingValue = "xinwang")
@EnableRedisHttpSession(maxInactiveIntervalInSeconds = 1800)
@Slf4j
public class RedisSessionConfig {

    {
        log.info("开启redis的session共享");
    }

}
