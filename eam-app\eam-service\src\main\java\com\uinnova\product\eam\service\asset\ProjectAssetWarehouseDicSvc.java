package com.uinnova.product.eam.service.asset;

import com.uinnova.product.eam.model.AssetWarehouseDirVo;
import com.uinnova.product.eam.model.enums.AssetType;

import java.util.List;

/**
 * <p>
 * 资产仓库目录
 * <p>
 *
 * @autor szq
 * @date 2025/01/09
 */
public interface ProjectAssetWarehouseDicSvc {

    /**
     * 获取资产推送目录树
     * @param assetType 资产类型
     * @param userId 用户id
     * @param assetId 资产id
     * @return List<AssetWarehouseDirVo>
     */
    List<AssetWarehouseDirVo> getPublishDirTree(AssetType assetType, Long userId, String assetId);
}
