package com.uino.web.cmdb.mvc;

import java.util.List;
import java.util.Map;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import com.uino.init.api.ApiResult;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uino.bean.cmdb.base.ESCIHistoryInfo;
import com.uino.bean.cmdb.query.ESCIHistorySearchBean;
import com.uino.init.api.ApiVersion;
import com.uino.api.client.cmdb.ICIHistoryApiSvc;
import org.springframework.web.bind.annotation.RequestMethod;

import io.swagger.annotations.Api;

@ApiVersion(1)
@Api(value = "对象管理-CI历史版本", tags = { "对象管理" })
@RequestMapping("/cmdb/ciHistory")
@RestController
public class CIHistoryMvc {
    @Autowired
    private ICIHistoryApiSvc ciHistorySvc;

    @ApiOperation(value="查询历史版本号列表")
    @RequestMapping(value="/getCIVersionList",method=RequestMethod.POST)
    @ModDesc(desc = "根据CI ciCode和分类id查询历史版本号列表", pDesc = "CI id", pType = Long.class, rDesc = "版本号列表", rType = Map.class)
    public ApiResult<List<String>> getCIVersionList(HttpServletRequest request, HttpServletResponse response, @RequestBody ESCIHistorySearchBean bean) {
        List<String> res = ciHistorySvc.getCIVersionList(bean.getCiCode(), bean.getClassId());
        return ApiResult.ok(this).data(res);
    }

    @ApiOperation(value="查询指定CI版本信息")
    @RequestMapping(value="/getCIInfoHistoryByCIVersion",method = RequestMethod.POST)
    @ModDesc(desc = "查询指定CI版本信息", pDesc = "查询条件", pType = ESCIHistorySearchBean.class, rDesc = "历史版本数据", rType = ESCIHistoryInfo.class)
    public ApiResult<ESCIHistoryInfo> getCIInfoHistoryByCIVersion(HttpServletRequest request, HttpServletResponse response, @RequestBody ESCIHistorySearchBean bean) {
        ESCIHistoryInfo res = ciHistorySvc.getCIInfoHistoryByCIVersion(bean.getCiCode(), bean.getClassId(), bean.getVersion());
        return ApiResult.ok(this).data(res);

    }
}
