<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>eam</artifactId>
        <groupId>com.uinnova.product.eam</groupId>
        <version>fuxi-1.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>eam-feign-server</artifactId>

    <properties>
        <docker.host>http://**************:2375</docker.host>
        <docker.project.version>3.7</docker.project.version>
        <docker.registry.url>dk.uino.cn</docker.registry.url>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>logback-classic</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.yaml</groupId>
                    <artifactId>snakeyaml</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.uinnova.product.eam</groupId>
            <artifactId>eam-base</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.uinnova.product.eam</groupId>
            <artifactId>eam-service</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.uinnova.product.eam</groupId>
            <artifactId>eam-feign-client</artifactId>
            <version>${project.version}</version>
        </dependency>
        <!-- ===================集成uino-micro-base项目依赖================== -->
        <dependency>
            <groupId>com.binary</groupId>
            <artifactId>binary-framework</artifactId>
            <version>2.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.uino.tarsier</groupId>
            <artifactId>tarsier-primarykeyclient</artifactId>
            <version>2.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.cloud</groupId>
                    <artifactId>spring-cloud-starter-openfeign</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- -->
        <dependency>
            <groupId>com.uino</groupId>
            <artifactId>i18n</artifactId>
            <version>${i18n.version}</version>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>${mysql.version}</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.dom4j/dom4j -->
        <dependency>
            <groupId>org.dom4j</groupId>
            <artifactId>dom4j</artifactId>
            <version>2.1.3</version>
        </dependency>

        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
        </dependency>
        <!-- batik/org.apache.xmlgraphics -->
        <dependency>
            <groupId>org.apache.xmlgraphics</groupId>
            <artifactId>batik-svggen</artifactId>
            <version>${batik.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.xmlgraphics</groupId>
            <artifactId>batik-awt-util</artifactId>
            <version>${batik.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.xmlgraphics</groupId>
            <artifactId>batik-bridge</artifactId>
            <version>${batik.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.xmlgraphics</groupId>
            <artifactId>batik-css</artifactId>
            <version>${batik.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.xmlgraphics</groupId>
            <artifactId>batik-dom</artifactId>
            <version>${batik.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.xmlgraphics</groupId>
            <artifactId>batik-gvt</artifactId>
            <version>${batik.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.xmlgraphics</groupId>
            <artifactId>batik-parser</artifactId>
            <version>${batik.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.xmlgraphics</groupId>
            <artifactId>batik-script</artifactId>
            <version>${batik.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.xmlgraphics</groupId>
            <artifactId>batik-svg-dom</artifactId>
            <version>${batik.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.xmlgraphics</groupId>
            <artifactId>batik-transcoder</artifactId>
            <version>${batik.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.xmlgraphics</groupId>
            <artifactId>batik-util</artifactId>
            <version>${batik.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.xmlgraphics</groupId>
            <artifactId>batik-xml</artifactId>
            <version>${batik.version}</version>
        </dependency>
        <!-- 此处不能使用2.9.1版本，使用2.9.1生成png会失败 -->
        <dependency>
            <groupId>xerces</groupId>
            <artifactId>xercesImpl</artifactId>
        </dependency>
        <dependency>
            <groupId>xml-apis</groupId>
            <artifactId>xmlParserAPIs</artifactId>
            <version>2.0.2</version>
        </dependency>

        <dependency>
            <groupId>org.axsl.org.w3c.dom.svg</groupId>
            <artifactId>svg-dom-java</artifactId>
            <version>1.1</version>
        </dependency>
        <dependency>
            <groupId>xml-apis</groupId>
            <artifactId>xml-apis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.w3c.css</groupId>
            <artifactId>sac</artifactId>
            <version>1.3</version>
        </dependency>

        <dependency>
            <groupId>jdom</groupId>
            <artifactId>jdom</artifactId>
            <version>1.0</version>
        </dependency>

        <dependency>
            <groupId>fop</groupId>
            <artifactId>fop</artifactId>
            <version>0.20.5</version>
            <exclusions>
                <exclusion>
                    <groupId>batik</groupId>
                    <artifactId>batik-1.5-fop</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.apache.xmlgraphics</groupId>
            <artifactId>xmlgraphics-commons</artifactId>
            <version>2.1</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-properties-migrator</artifactId>
            <scope>runtime</scope>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <!-- Tomcat dependency -->
        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-core</artifactId>
            <version>${tomcat.version}</version><!--$NO-MVN-MAN-VER$ -->
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
            <optional>true</optional>
            <scope>runtime</scope>
        </dependency>

        <dependency>
            <groupId>com.uino</groupId>
            <artifactId>uino-eam-micro-comm</artifactId>
            <version>${uino-micro-base.version}</version>
        </dependency>
        <dependency>
            <groupId>com.uino</groupId>
            <artifactId>uino-eam-micro-service</artifactId>
            <version>${uino-micro-base.version}</version>
        </dependency>
        <dependency>
            <groupId>com.uino</groupId>
            <artifactId>uino-eam-micro-api</artifactId>
            <version>${uino-micro-base.version}</version>
        </dependency>

        <!-- Unit Test dependency -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.jmock</groupId>
            <artifactId>jmock</artifactId>
            <version>2.12.0</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-core</artifactId>
            <version>${powermock.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4</artifactId>
            <version>${powermock.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-mockito2</artifactId>
            <version>${powermock.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.dbunit</groupId>
            <artifactId>dbunit</artifactId>
            <version>2.5.4</version>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.poi</groupId>
                    <artifactId>poi</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.poi</groupId>
                    <artifactId>poi-ooxml</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.github.springtestdbunit</groupId>
            <artifactId>spring-test-dbunit</artifactId>
            <version>1.3.0</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.uino.tarsier</groupId>
                <artifactId>tarsier-com-project</artifactId>
                <version>2.0.0-EAM-SNAPSHOT</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <!--<plugin>
                <artifactId>maven-assembly-plugin</artifactId>
                <version>3.1.1</version>
                <configuration>
                    <finalName>EAM</finalName>
                </configuration>
                <executions>
                    <execution>
                        <id>pack-center</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                        <configuration>
                            <descriptors>
                                <descriptor>/pack-war2tar.xml</descriptor>
                            </descriptors>
                            <attach>false</attach>
                        </configuration>
                    </execution>
                </executions>
            </plugin>


            <plugin>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>2.5</version>
                <executions>
                    <execution>
                        <id>default-deploy</id>
                        <phase>deploy</phase>
                        <goals>
                            <goal>deploy</goal>
                        </goals>
                        &lt;!&ndash; skip默认deploy插件的执行 &ndash;&gt;
                        <configuration>
                            <skip>true</skip>
                        </configuration>
                    </execution>
                    <execution>
                        <id>deploy-file</id>
                        <phase>deploy</phase>
                        <goals>
                            <goal>deploy-file</goal>
                        </goals>
                        <configuration>
                            &lt;!&ndash; 开发阶段上传到snapshot仓库，上线阶段上传到release仓库 &ndash;&gt;
                            <packaging>jar</packaging>
                            <file>${project.build.directory}/${project.artifactId}-${project.version}.jar</file>
                            <groupId>${project.groupId}</groupId>
                            <artifactId>${project.artifactId}</artifactId>
                            <version>${project.version}</version>
                            <repositoryId>uinnova-snapshots</repositoryId>
                            <url>http://mvn.uinnova.cn/nexus/content/repositories/uinnova-snapshots</url>
                        </configuration>
                    </execution>
                </executions>
            </plugin>-->
        </plugins>
    </build>

</project>