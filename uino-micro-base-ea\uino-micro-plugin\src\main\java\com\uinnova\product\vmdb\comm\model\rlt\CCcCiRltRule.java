package com.uinnova.product.vmdb.comm.model.rlt;

import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;

/**
 * 
 * <AUTHOR>
 *
 */
@Comment("CI关系规则表[CC_CI_RLT_RULE]")
public class CCcCiRltRule implements Condition {
    private static final long serialVersionUID = 1L;

    @Comment("ID[ID] operate-Equal[=]")
    private Long id;

    @Comment("ID[ID] operate-In[in]")
    private Long[] ids;

    @Comment("ID[ID] operate-GTEqual[>=]")
    private Long startId;

    @Comment("ID[ID] operate-LTEqual[<=]")
    private Long endId;

    @Comment("分类关系线ID[CLASS_RLT_ID] operate-Equal[=]")
    private Long classRltId;

    @Comment("分类关系线ID[CLASS_RLT_ID] operate-In[in]")
    private Long[] classRltIds;

    @Comment("分类关系线ID[CLASS_RLT_ID] operate-GTEqual[>=]")
    private Long startClassRltId;

    @Comment("分类关系线ID[CLASS_RLT_ID] operate-LTEqual[<=]")
    private Long endClassRltId;

    @Comment("所属关系分类[CLASS_ID] operate-Equal[=]")
    private Long classId;

    @Comment("所属关系分类[CLASS_ID] operate-In[in]")
    private Long[] classIds;

    @Comment("所属关系分类[CLASS_ID] operate-GTEqual[>=]")
    private Long startClassId;

    @Comment("所属关系分类[CLASS_ID] operate-LTEqual[<=]")
    private Long endClassId;

    @Comment("源分类ID[SOURCE_CLASS_ID] operate-Equal[=]")
    private Long sourceClassId;

    @Comment("源分类ID[SOURCE_CLASS_ID] operate-In[in]")
    private Long[] sourceClassIds;

    @Comment("源分类ID[SOURCE_CLASS_ID] operate-GTEqual[>=]")
    private Long startSourceClassId;

    @Comment("源分类ID[SOURCE_CLASS_ID] operate-LTEqual[<=]")
    private Long endSourceClassId;

    @Comment("源属性定义ID[SOURCE_DEF_ID] operate-Equal[=]")
    private Long sourceDefId;

    @Comment("源属性定义ID[SOURCE_DEF_ID] operate-In[in]")
    private Long[] sourceDefIds;

    @Comment("源属性定义ID[SOURCE_DEF_ID] operate-GTEqual[>=]")
    private Long startSourceDefId;

    @Comment("源属性定义ID[SOURCE_DEF_ID] operate-LTEqual[<=]")
    private Long endSourceDefId;

    @Comment("目标分类ID[TARGET_CLASS_ID] operate-Equal[=]")
    private Long targetClassId;

    @Comment("目标分类ID[TARGET_CLASS_ID] operate-In[in]")
    private Long[] targetClassIds;

    @Comment("目标分类ID[TARGET_CLASS_ID] operate-GTEqual[>=]")
    private Long startTargetClassId;

    @Comment("目标分类ID[TARGET_CLASS_ID] operate-LTEqual[<=]")
    private Long endTargetClassId;

    @Comment("目标属性定义ID[TARGET_DEF_ID] operate-Equal[=]")
    private Long targetDefId;

    @Comment("目标属性定义ID[TARGET_DEF_ID] operate-In[in]")
    private Long[] targetDefIds;

    @Comment("目标属性定义ID[TARGET_DEF_ID] operate-GTEqual[>=]")
    private Long startTargetDefId;

    @Comment("目标属性定义ID[TARGET_DEF_ID] operate-LTEqual[<=]")
    private Long endTargetDefId;

    @Comment("有效状态[USE_STATUS] operate-Equal[=]    有效状态:0=无效 1=有效")
    private Integer useStatus;

    @Comment("有效状态[USE_STATUS] operate-In[in]    有效状态:0=无效 1=有效")
    private Integer[] useStatuss;

    @Comment("有效状态[USE_STATUS] operate-GTEqual[>=]    有效状态:0=无效 1=有效")
    private Integer startUseStatus;

    @Comment("有效状态[USE_STATUS] operate-LTEqual[<=]    有效状态:0=无效 1=有效")
    private Integer endUseStatus;

    @Comment("无效说明[VALID_ERR_MSG] operate-Like[like]")
    private String validErrMsg;

    @Comment("所属域[DOMAIN_ID] operate-Equal[=]")
    private Long domainId;

    @Comment("所属域[DOMAIN_ID] operate-In[in]")
    private Long[] domainIds;

    @Comment("所属域[DOMAIN_ID] operate-GTEqual[>=]")
    private Long startDomainId;

    @Comment("所属域[DOMAIN_ID] operate-LTEqual[<=]")
    private Long endDomainId;

    @Comment("数据状态[DATA_STATUS] operate-Equal[=]    数据状态:1-正常 0-删除")
    private Integer dataStatus;

    @Comment("数据状态[DATA_STATUS] operate-In[in]    数据状态:1-正常 0-删除")
    private Integer[] dataStatuss;

    @Comment("数据状态[DATA_STATUS] operate-GTEqual[>=]    数据状态:1-正常 0-删除")
    private Integer startDataStatus;

    @Comment("数据状态[DATA_STATUS] operate-LTEqual[<=]    数据状态:1-正常 0-删除")
    private Integer endDataStatus;

    @Comment("创建人[CREATOR] operate-Like[like]")
    private String creator;

    @Comment("创建人[CREATOR] operate-Equal[=]")
    private String creatorEqual;

    @Comment("创建人[CREATOR] operate-In[in]")
    private String[] creators;

    @Comment("修改人[MODIFIER] operate-Like[like]")
    private String modifier;

    @Comment("修改人[MODIFIER] operate-Equal[=]")
    private String modifierEqual;

    @Comment("修改人[MODIFIER] operate-In[in]")
    private String[] modifiers;

    @Comment("创建时间[CREATE_TIME] operate-Equal[=]    创建时间:yyyyMMddHHmmss")
    private Long createTime;

    @Comment("创建时间[CREATE_TIME] operate-In[in]    创建时间:yyyyMMddHHmmss")
    private Long[] createTimes;

    @Comment("创建时间[CREATE_TIME] operate-GTEqual[>=]    创建时间:yyyyMMddHHmmss")
    private Long startCreateTime;

    @Comment("创建时间[CREATE_TIME] operate-LTEqual[<=]    创建时间:yyyyMMddHHmmss")
    private Long endCreateTime;

    @Comment("修改时间[MODIFY_TIME] operate-Equal[=]    修改时间:yyyyMMddHHmmss")
    private Long modifyTime;

    @Comment("修改时间[MODIFY_TIME] operate-In[in]    修改时间:yyyyMMddHHmmss")
    private Long[] modifyTimes;

    @Comment("修改时间[MODIFY_TIME] operate-GTEqual[>=]    修改时间:yyyyMMddHHmmss")
    private Long startModifyTime;

    @Comment("修改时间[MODIFY_TIME] operate-LTEqual[<=]    修改时间:yyyyMMddHHmmss")
    private Long endModifyTime;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long[] getIds() {
        return this.ids;
    }

    public void setIds(Long[] ids) {
        this.ids = ids;
    }

    public Long getStartId() {
        return this.startId;
    }

    public void setStartId(Long startId) {
        this.startId = startId;
    }

    public Long getEndId() {
        return this.endId;
    }

    public void setEndId(Long endId) {
        this.endId = endId;
    }

    public Long getClassRltId() {
        return this.classRltId;
    }

    public void setClassRltId(Long classRltId) {
        this.classRltId = classRltId;
    }

    public Long[] getClassRltIds() {
        return this.classRltIds;
    }

    public void setClassRltIds(Long[] classRltIds) {
        this.classRltIds = classRltIds;
    }

    public Long getStartClassRltId() {
        return this.startClassRltId;
    }

    public void setStartClassRltId(Long startClassRltId) {
        this.startClassRltId = startClassRltId;
    }

    public Long getEndClassRltId() {
        return this.endClassRltId;
    }

    public void setEndClassRltId(Long endClassRltId) {
        this.endClassRltId = endClassRltId;
    }

    public Long getClassId() {
        return this.classId;
    }

    public void setClassId(Long classId) {
        this.classId = classId;
    }

    public Long[] getClassIds() {
        return this.classIds;
    }

    public void setClassIds(Long[] classIds) {
        this.classIds = classIds;
    }

    public Long getStartClassId() {
        return this.startClassId;
    }

    public void setStartClassId(Long startClassId) {
        this.startClassId = startClassId;
    }

    public Long getEndClassId() {
        return this.endClassId;
    }

    public void setEndClassId(Long endClassId) {
        this.endClassId = endClassId;
    }

    public Long getSourceClassId() {
        return this.sourceClassId;
    }

    public void setSourceClassId(Long sourceClassId) {
        this.sourceClassId = sourceClassId;
    }

    public Long[] getSourceClassIds() {
        return this.sourceClassIds;
    }

    public void setSourceClassIds(Long[] sourceClassIds) {
        this.sourceClassIds = sourceClassIds;
    }

    public Long getStartSourceClassId() {
        return this.startSourceClassId;
    }

    public void setStartSourceClassId(Long startSourceClassId) {
        this.startSourceClassId = startSourceClassId;
    }

    public Long getEndSourceClassId() {
        return this.endSourceClassId;
    }

    public void setEndSourceClassId(Long endSourceClassId) {
        this.endSourceClassId = endSourceClassId;
    }

    public Long getSourceDefId() {
        return this.sourceDefId;
    }

    public void setSourceDefId(Long sourceDefId) {
        this.sourceDefId = sourceDefId;
    }

    public Long[] getSourceDefIds() {
        return this.sourceDefIds;
    }

    public void setSourceDefIds(Long[] sourceDefIds) {
        this.sourceDefIds = sourceDefIds;
    }

    public Long getStartSourceDefId() {
        return this.startSourceDefId;
    }

    public void setStartSourceDefId(Long startSourceDefId) {
        this.startSourceDefId = startSourceDefId;
    }

    public Long getEndSourceDefId() {
        return this.endSourceDefId;
    }

    public void setEndSourceDefId(Long endSourceDefId) {
        this.endSourceDefId = endSourceDefId;
    }

    public Long getTargetClassId() {
        return this.targetClassId;
    }

    public void setTargetClassId(Long targetClassId) {
        this.targetClassId = targetClassId;
    }

    public Long[] getTargetClassIds() {
        return this.targetClassIds;
    }

    public void setTargetClassIds(Long[] targetClassIds) {
        this.targetClassIds = targetClassIds;
    }

    public Long getStartTargetClassId() {
        return this.startTargetClassId;
    }

    public void setStartTargetClassId(Long startTargetClassId) {
        this.startTargetClassId = startTargetClassId;
    }

    public Long getEndTargetClassId() {
        return this.endTargetClassId;
    }

    public void setEndTargetClassId(Long endTargetClassId) {
        this.endTargetClassId = endTargetClassId;
    }

    public Long getTargetDefId() {
        return this.targetDefId;
    }

    public void setTargetDefId(Long targetDefId) {
        this.targetDefId = targetDefId;
    }

    public Long[] getTargetDefIds() {
        return this.targetDefIds;
    }

    public void setTargetDefIds(Long[] targetDefIds) {
        this.targetDefIds = targetDefIds;
    }

    public Long getStartTargetDefId() {
        return this.startTargetDefId;
    }

    public void setStartTargetDefId(Long startTargetDefId) {
        this.startTargetDefId = startTargetDefId;
    }

    public Long getEndTargetDefId() {
        return this.endTargetDefId;
    }

    public void setEndTargetDefId(Long endTargetDefId) {
        this.endTargetDefId = endTargetDefId;
    }

    public Integer getUseStatus() {
        return this.useStatus;
    }

    public void setUseStatus(Integer useStatus) {
        this.useStatus = useStatus;
    }

    public Integer[] getUseStatuss() {
        return this.useStatuss;
    }

    public void setUseStatuss(Integer[] useStatuss) {
        this.useStatuss = useStatuss;
    }

    public Integer getStartUseStatus() {
        return this.startUseStatus;
    }

    public void setStartUseStatus(Integer startUseStatus) {
        this.startUseStatus = startUseStatus;
    }

    public Integer getEndUseStatus() {
        return this.endUseStatus;
    }

    public void setEndUseStatus(Integer endUseStatus) {
        this.endUseStatus = endUseStatus;
    }

    public String getValidErrMsg() {
        return this.validErrMsg;
    }

    public void setValidErrMsg(String validErrMsg) {
        this.validErrMsg = validErrMsg;
    }

    public Long getDomainId() {
        return this.domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }

    public Long[] getDomainIds() {
        return this.domainIds;
    }

    public void setDomainIds(Long[] domainIds) {
        this.domainIds = domainIds;
    }

    public Long getStartDomainId() {
        return this.startDomainId;
    }

    public void setStartDomainId(Long startDomainId) {
        this.startDomainId = startDomainId;
    }

    public Long getEndDomainId() {
        return this.endDomainId;
    }

    public void setEndDomainId(Long endDomainId) {
        this.endDomainId = endDomainId;
    }

    public Integer getDataStatus() {
        return this.dataStatus;
    }

    public void setDataStatus(Integer dataStatus) {
        this.dataStatus = dataStatus;
    }

    public Integer[] getDataStatuss() {
        return this.dataStatuss;
    }

    public void setDataStatuss(Integer[] dataStatuss) {
        this.dataStatuss = dataStatuss;
    }

    public Integer getStartDataStatus() {
        return this.startDataStatus;
    }

    public void setStartDataStatus(Integer startDataStatus) {
        this.startDataStatus = startDataStatus;
    }

    public Integer getEndDataStatus() {
        return this.endDataStatus;
    }

    public void setEndDataStatus(Integer endDataStatus) {
        this.endDataStatus = endDataStatus;
    }

    public String getCreator() {
        return this.creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getCreatorEqual() {
        return this.creatorEqual;
    }

    public void setCreatorEqual(String creatorEqual) {
        this.creatorEqual = creatorEqual;
    }

    public String[] getCreators() {
        return this.creators;
    }

    public void setCreators(String[] creators) {
        this.creators = creators;
    }

    public String getModifier() {
        return this.modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public String getModifierEqual() {
        return this.modifierEqual;
    }

    public void setModifierEqual(String modifierEqual) {
        this.modifierEqual = modifierEqual;
    }

    public String[] getModifiers() {
        return this.modifiers;
    }

    public void setModifiers(String[] modifiers) {
        this.modifiers = modifiers;
    }

    public Long getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long[] getCreateTimes() {
        return this.createTimes;
    }

    public void setCreateTimes(Long[] createTimes) {
        this.createTimes = createTimes;
    }

    public Long getStartCreateTime() {
        return this.startCreateTime;
    }

    public void setStartCreateTime(Long startCreateTime) {
        this.startCreateTime = startCreateTime;
    }

    public Long getEndCreateTime() {
        return this.endCreateTime;
    }

    public void setEndCreateTime(Long endCreateTime) {
        this.endCreateTime = endCreateTime;
    }

    public Long getModifyTime() {
        return this.modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Long[] getModifyTimes() {
        return this.modifyTimes;
    }

    public void setModifyTimes(Long[] modifyTimes) {
        this.modifyTimes = modifyTimes;
    }

    public Long getStartModifyTime() {
        return this.startModifyTime;
    }

    public void setStartModifyTime(Long startModifyTime) {
        this.startModifyTime = startModifyTime;
    }

    public Long getEndModifyTime() {
        return this.endModifyTime;
    }

    public void setEndModifyTime(Long endModifyTime) {
        this.endModifyTime = endModifyTime;
    }

}
