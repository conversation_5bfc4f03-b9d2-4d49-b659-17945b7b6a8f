package com.uino.plugin.client.init;

import com.uino.plugin.classloader.PluginOperateLogUtil;
import com.uino.plugin.classloader.comm.RequestMappingMethod;
import org.springframework.aop.support.AopUtils;
import org.springframework.core.MethodIntrospector;
import org.springframework.util.ClassUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 提供mapping动态注册、注销接口
 */
public class UinoBaseRequestMappingHandlerMapping extends RequestMappingHandlerMapping {

    private Map<String, List<RequestMappingInfo>> mapRequestMappingInfo = new ConcurrentHashMap<>();

    /**由于父类方法是protected类型，外部不能调用
     * 子类实现public方法，使其接口可以供外部调用
     * @param method
     * @param handlerType
     * @return
     */
    public RequestMappingInfo getMappingForMethod(Method method, Class<?> handlerType) {
        return super.getMappingForMethod(method, handlerType);
    }


    /**提供MVC RequestMapping动态注册接口
     * @param beanName
     * @param handlerType
     */
    public void registerMapping(String beanName, Class<?> handlerType) {
        List<RequestMappingInfo> listRequestMappingInfo = new ArrayList<>();
        if (handlerType != null) {
            Class<?> userType = ClassUtils.getUserClass(handlerType);
            Map<Method, RequestMappingInfo> methods = MethodIntrospector.selectMethods(userType,
                    (MethodIntrospector.MetadataLookup<RequestMappingInfo>) method -> {
                        try {
                            return getMappingForMethod(method, userType);
                        } catch (Throwable ex) {
                            throw new IllegalStateException(
                                    "Invalid mapping on handler class [" + userType.getName() + "]: " + method, ex);
                        }
                    });

            methods.forEach((method, mapping) -> {
                Method invocableMethod = AopUtils.selectInvocableMethod(method, userType);
                registerHandlerMethod(beanName, invocableMethod, mapping);
                listRequestMappingInfo.add(mapping);
                Set<String> patterns = mapping.getPatternsCondition().getPatterns();
                if (!CollectionUtils.isEmpty(patterns)) {
                    patterns.forEach(pattern -> {
                        RequestMappingMethod requestMappingMethod = RequestMappingMethod.builder().method(invocableMethod).clazz(userType).mapping(pattern).build();
                        PluginOperateLogUtil.mappingMethodMap.put(pattern, requestMappingMethod);
                    });
                }
            });
        }

        mapRequestMappingInfo.put(beanName, listRequestMappingInfo);
    }

    /**提供MVC RequestMapping动态注销接口
     * @param beanName
     */
    public void unregisterMapping(String beanName) {
        List<RequestMappingInfo> listRequestMappingInfo = mapRequestMappingInfo.get(beanName);
        if (listRequestMappingInfo != null) {
            for (RequestMappingInfo rmInfo : listRequestMappingInfo) {
                Set<String> patterns = rmInfo.getPatternsCondition().getPatterns();
                if (!CollectionUtils.isEmpty(patterns)) {
                    patterns.forEach(pattern -> PluginOperateLogUtil.mappingMethodMap.remove(pattern));
                }
                this.unregisterMapping(rmInfo);
            }
        }
        mapRequestMappingInfo.remove(beanName);
    }


}
