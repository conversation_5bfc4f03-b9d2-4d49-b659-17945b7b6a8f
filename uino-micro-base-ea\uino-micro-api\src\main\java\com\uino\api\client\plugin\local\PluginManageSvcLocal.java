package com.uino.api.client.plugin.local;

import com.binary.core.io.Resource;
import com.binary.jdbc.Page;
import com.uino.api.client.plugin.IPluginManageSvc;
import com.uino.bean.plugin.base.ESPluginInfo;
import com.uino.bean.plugin.query.CPluginInfo;
import com.uino.service.plugin.microservice.PluginManageSvc;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2021/9/27
 * @Version 1.0
 */
@Service
public class PluginManageSvcLocal implements IPluginManageSvc {

    @Autowired
    private PluginManageSvc pluginManageSvc;

    @Override
    public List<String> getServiceList() {
        return pluginManageSvc.getServiceList();
    }

    @Override
    public Long saveOrUpdate(ESPluginInfo pluginInfo) {
        return pluginManageSvc.saveOrUpdate(pluginInfo);
    }

    @Override
    public String uploadPlugin(Long id, MultipartFile file) {
        return pluginManageSvc.uploadPlugin(id, file);
    }

    @Override
    public boolean syncPlugin(MultipartFile file) {
        return pluginManageSvc.syncPlugin(file);
    }

    @Override
    public Resource downloadPlugin(Long id) {
        return pluginManageSvc.downloadPlugin(id);
    }

    @Override
    public Page<ESPluginInfo> queryList(int pageNum, int pageSize, CPluginInfo cPluginInfo) {
        return pluginManageSvc.queryList(pageNum, pageSize, cPluginInfo);
    }

    @Override
    public boolean loadOrUnloadPlugin(Long id) {
        return pluginManageSvc.loadOrUnloadPlugin(id);
    }


    @Override
    public boolean deletePlugin(Long id) {
        return pluginManageSvc.deletePlugin(id);
    }

    @Override
    public List<String> queryListByOpenServer(String serverName) {
        return pluginManageSvc.queryListByOpenServer(serverName);
    }
}
