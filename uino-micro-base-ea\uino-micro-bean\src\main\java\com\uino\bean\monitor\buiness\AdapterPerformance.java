package com.uino.bean.monitor.buiness;

import java.io.Serializable;

import com.binary.framework.bean.annotation.Comment;

/**
 * 性能数据整合实体
 * <调用PMV和NOAH返回的性能数据格式有差异,为了数据统一和方便,仅提取有用的信息>
 *
 * <AUTHOR>
 */
public class AdapterPerformance implements Serializable {
    private static final long serialVersionUID = 1L;


    @Comment("主键ID[ID]")
    private Long id;

    @Comment("CI_ID[CI_ID]    对象ID")
    private Long ciId;

    @Comment("CI_NAME[CI_NAME]    ci名称")
    private String ciName;

    @Comment("KPI_ID[KPI_ID]    KPIID")
    private Long kpiId;

    @Comment("KPI_NAME[KPI_NAME]    kpi名称")
    private String kpiName;

    @Comment("KPI_CLASS_NAME[KPI_CLASS_NAME]    kpi分类名称")
    private String kpiClassName;

    @Comment("UNIT[UNIT]    所属单位")
    private String unit;

    @Comment("最新时间[TIME]    YYYYMMDDHHMISS")
    private Long time;

    @Comment("VAL[VAL]   值")
    private String val;

    @Comment("INSTANCE[INSTANCE]    实例")
    private String instance;

    @Comment("创建时间[CREATE_TIME]    YYYYMMDDHHMISS")
    private Long createTime;

    @Comment("单位是否转换成功")
    private boolean converValueSuccess;
    
    @Comment("转换后的值")
    private String converValue;
    
    @Comment("转换后的单位")
    private String converUnit;
    
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCiId() {
        return ciId;
    }

    public void setCiId(Long ciId) {
        this.ciId = ciId;
    }

    public String getCiName() {
        return ciName;
    }

    public void setCiName(String ciName) {
        this.ciName = ciName;
    }

    public Long getKpiId() {
        return kpiId;
    }

    public void setKpiId(Long kpiId) {
        this.kpiId = kpiId;
    }

    public String getKpiName() {
        return kpiName;
    }

    public void setKpiName(String kpiName) {
        this.kpiName = kpiName;
    }

    public String getKpiClassName() {
        return kpiClassName;
    }

    public void setKpiClassName(String kpiClassName) {
        this.kpiClassName = kpiClassName;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public Long getTime() {
        return time;
    }

    public void setTime(Long time) {
        this.time = time;
    }

    public String getVal() {
        return val;
    }

    public void setVal(String val) {
        this.val = val;
    }

    public String getInstance() {
        return instance;
    }

    public void setInstance(String instance) {
        this.instance = instance;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

	public boolean isConverValueSuccess() {
		return converValueSuccess;
	}

	public void setConverValueSuccess(boolean converValueSuccess) {
		this.converValueSuccess = converValueSuccess;
	}

	public String getConverValue() {
		return converValue;
	}

	public void setConverValue(String converValue) {
		this.converValue = converValue;
	}

	public String getConverUnit() {
		return converUnit;
	}

	public void setConverUnit(String converUnit) {
		this.converUnit = converUnit;
	}


}
