package com.uino.api.client.cmdb.rpc;

import com.alibaba.fastjson.JSONObject;
import com.binary.core.io.Resource;
import com.binary.core.io.support.ByteArrayResource;
import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uinnova.product.vmdb.provider.rlt.bean.CcCiRltInfo;
import com.uino.api.client.cmdb.ICIRltApiSvc;
import com.uino.bean.cmdb.base.ESCIRltInfo;
import com.uino.bean.cmdb.base.ESCIRltInfoHistory;
import com.uino.bean.cmdb.business.BindCiRltRequestDto;
import com.uino.bean.cmdb.business.DataModuleRltClassDto;
import com.uino.bean.cmdb.business.ImportExcelMessage;
import com.uino.bean.cmdb.business.ImportResultMessage;
import com.uino.bean.cmdb.query.ESAttrAggBean;
import com.uino.bean.cmdb.query.ESRltSearchBean;
import com.uino.dao.BaseConst;
import com.uino.dao.util.ESUtil;
import com.uino.provider.feign.cmdb.CIRltFeign;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
@Primary
public class CIRltApiSvcRpc implements ICIRltApiSvc {

    @Autowired
    private CIRltFeign ciRltSvc;

    @Override
    public Long bindCiRlt(BindCiRltRequestDto bindCiRltRequestDto) {
        return ciRltSvc.bindCiRlt(bindCiRltRequestDto);
    }

    @Override
    public ImportResultMessage bindCiRlts(Set<BindCiRltRequestDto> bindRltDtos, boolean repeat) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("bindRltDtos", bindRltDtos);
        jsonObject.put("repeat", repeat);
        return ciRltSvc.bindCiRlts(BaseConst.DEFAULT_DOMAIN_ID, jsonObject);
    }

    @Override
    public ImportResultMessage bindCiRlts(Long domainId, Set<BindCiRltRequestDto> bindRltDtos, boolean repeat) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("bindRltDtos", bindRltDtos);
        jsonObject.put("repeat", repeat);
        return ciRltSvc.bindCiRlts(domainId, jsonObject);
    }

    @Override
    public Integer delRltByCiId(Long ciId) {
        return ciRltSvc.delRltByCiId(ciId);
    }

    @Override
    public Long updateCiRltAttr(Long ciRltId, Map<String, String> attrs) {
        // TODO Auto-generated method stub
        return ciRltSvc.updateCiRltAttr(ciRltId, attrs);
    }

    @Override
    public Page<CcCiRltInfo> searchRltByBean(ESRltSearchBean bean) {
        // TODO Auto-generated method stub
        return ciRltSvc.searchRltByBean(bean);
    }

    @Override
    public Integer clearRltByClassId(Long rltClassId) {
        // TODO Auto-generated method stub
        return ciRltSvc.clearRltByClassId(rltClassId);
    }

    @Override
    public ImportResultMessage importCiRlt(String excelFilePath, MultipartFile excelFile, Set<String> rltClsCodes) {
        // TODO Auto-generated method stub
        return ciRltSvc.importCiRlt(BaseConst.DEFAULT_DOMAIN_ID, excelFilePath, excelFile, rltClsCodes);
    }

    @Override
    public ImportResultMessage importCiRlt(Long domainId, String excelFilePath, MultipartFile excelFile, Set<String> rltClsCodes) {
        return ciRltSvc.importCiRlt(domainId, excelFilePath, excelFile, rltClsCodes);
    }

    @Override
    public Integer delRltByIdsOrRltCodes(Set<Long> rltIds, Set<String> rltCodes) {
        // TODO Auto-generated method stub
        return ciRltSvc.delRltByIdsOrRltCodes(rltIds, rltCodes);
    }

    @Override
    public Map<String, Boolean> comprehendRltExcel(MultipartFile excelFile) {
        // TODO Auto-generated method stub
        return ciRltSvc.comprehendRltExcel(excelFile);
    }

    @Override
    public ImportExcelMessage parseRltExcel(String excelFilePath, MultipartFile excelFile) {
        // TODO Auto-generated method stub
        return ciRltSvc.parseRltExcel(excelFilePath, excelFile);
    }

    @Override
    public ImportResultMessage bindCiRlts(Set<BindCiRltRequestDto> bindRltDtos) {
        return ciRltSvc.bindCiRlts(BaseConst.DEFAULT_DOMAIN_ID, bindRltDtos);
    }

    @Override
    public ImportResultMessage bindCiRlts(Long domainId, Set<BindCiRltRequestDto> bindRltDtos) {
        return ciRltSvc.bindCiRlts(domainId, bindRltDtos);
    }

    @Override
    public List<CcCiRltInfo> searchRltByIds(Set<Long> ids) {
        // TODO Auto-generated method stub
        return ciRltSvc.searchRltByIds(ids);
    }

    @Override
    public Page<ESCIRltInfo> searchRlt(ESRltSearchBean bean) {
        // TODO Auto-generated method stub
        return ciRltSvc.searchRlt(bean);
    }

    @Override
    public Page<String> groupByField(ESAttrAggBean req) {
        // TODO Auto-generated method stub
        return ciRltSvc.groupByField(BaseConst.DEFAULT_DOMAIN_ID, req);
    }

    @Override
    public Page<String> groupByField(Long domainId, ESAttrAggBean req) {
        return ciRltSvc.groupByField(domainId, req);
    }

    @Override
    public Map<Long, Map<Long, Set<Long>>> getClassRltMapByClsQuery(Set<Long> clsIds, Set<Long> rltClsIds) {
        JSONObject req = new JSONObject();
        req.put("clsIds", clsIds);
        req.put("rltClsIds", rltClsIds);
        return ciRltSvc.getClassRltMapByClsQuery(req);
    }

    @Override
    public Resource exportCiRlt(ESRltSearchBean bean) {
        JSONObject req = new JSONObject();
        req.put("clsIds", bean.getRltClassIds());
        req.put("rltIds", bean.getRltId());
        ResponseEntity<byte[]> httpResponse = ciRltSvc.exportCiRlt(req.toJSONString());
        Resource returnVal = new ByteArrayResource(httpResponse.getBody(),
                "CI关系_" + ESUtil.getNumberDateTime() + ".xlsx");
        return returnVal;
    }

    @Override
    public List<DataModuleRltClassDto> getClassRltList(Set<Long> classIds, Set<Long> rltClsIds) {
        // TODO Auto-generated method stub
        return ciRltSvc.getClassRltList(classIds, rltClsIds);
    }

    @Override
    public Map<Long, List<ESCIRltInfoHistory>> getRltsHistrysDict(Set<Long> rltIds, boolean hasCurrent) {
        return ciRltSvc.getRltsHistrysDict(rltIds, hasCurrent);
    }

    @Override
    public Map<Long, Long> getRltIdMaxVersion(Set<Long> rltIds) {
        return ciRltSvc.getRltIdMaxVersion(rltIds);
    }

    @Override
    public List<CcCiClassInfo> queryAllClasses(Long domainId) {

        return null;
    }

}
