package com.uino.init;

import com.uino.dao.license.ESLicenseAuthSvc;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

import com.binary.framework.Local;
import com.binary.framework.bean.User;
import com.uinnova.product.vmdb.comm.integration.SpringContextAware;
import com.uino.service.license.microservice.LicenseProxy;
import com.uinnova.product.vmdb.comm.util.ApplicationProperties;
import com.uino.service.license.microservice.impl.SimpleLicenseProxy;
import com.uino.service.license.microservice.ILicenseAuthSvc;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;

@Configuration
@Slf4j
@ComponentScan("com.uino.api.client.license.local")
@ConditionalOnProperty(name = "uino.license", havingValue = "true", matchIfMissing = true)
public class UinoLicenseConfiguration {

    static {
        log.info("开始注册License相关 bean信息");
    }

    @Bean
    public LicenseProxy licenseProxy() {
        log.info("LicenseProxy注册成功");
        return new SimpleLicenseProxy();
    }

    @Bean
    @Lazy(false)
    public SpringContextAware springContextAware(ApplicationContext applicationContext){
        SpringContextAware springContextAware = new SpringContextAware();
        springContextAware.setApplicationContext(applicationContext);
        return springContextAware;
    }

    // 启动项目初始化授权信息
    @Bean
    public CommandLineRunner applicationInitialization(ApplicationContext act) {
        final ILicenseAuthSvc licenseAuthSvc = act.getBean(ILicenseAuthSvc.class);
        return args -> {
            try {
                Local.open((User) null);
                licenseAuthSvc.initialization();
                licenseAuthSvc.setLicenseCumulativeTimerUpdated(true);
            } finally {
                Local.close();
            }
        };
    }

    // 注册授权Filter,并注入参数
    @Bean
    @SuppressWarnings({"rawtypes", "unchecked"})
    public FilterRegistrationBean licenseAuthorityFilter(
            @Value("${uino.tenantDomain:false}")String isOpenTenantDomain,
            @Value("${license.ignore.filter.pattern:}") String licenseFilterPattern,
            @Value("${project.license.register.url:}") String registerUrl,
            @Value("${project.license.ordinary.register.url:}") String ordinaryRegisterLicenseUrl) {
        FilterRegistrationBean registration = new FilterRegistrationBean(new UinoLicenseAuthorityFilter());
        registration.addUrlPatterns("/*");
        registration.addInitParameter("isOpenTenantDomain", isOpenTenantDomain);
        registration.addInitParameter("ignore-filter-pattern", licenseFilterPattern);
//        registration.addInitParameter("registerLicensePropertyName", registerUrl);
        registration.addInitParameter("registerLicenseUrl", registerUrl);
        registration.addInitParameter("ordinaryRegisterLicenseUrl", ordinaryRegisterLicenseUrl);
        registration.setName("LicenseAuthorityFilter");
        registration.setOrder(2);
        return registration;
    }


    // 用于加载自定义配置
    @Bean
    public ApplicationProperties loadProperties() {
        return new ApplicationProperties();
    }
}
