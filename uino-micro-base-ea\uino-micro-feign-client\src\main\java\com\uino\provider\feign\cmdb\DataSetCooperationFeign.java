package com.uino.provider.feign.cmdb;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.alibaba.fastjson.JSONObject;
import com.uino.provider.feign.config.BaseFeignConfig;

/**
 * 
 * <AUTHOR>
 *
 */
@Component
@FeignClient(name = "${feign-server-name:tarsier-platform}", path = "${feign-server-path:tarsier-platform}/feign/cmdb/datasetCooperation", configuration = {
		BaseFeignConfig.class })
public interface DataSetCooperationFeign {

	@PostMapping("findByDataSetId")
	List<JSONObject> findByDataSetId(@RequestBody Long dataSetId);
	
	@PostMapping("updateCooperation")
	Boolean updateCooperation(@RequestBody JSONObject body);
}
