package com.uino.bean.monitor.buiness.unit;

import java.io.Serializable;

/**
 * 单位转换模型
 * 
 * <AUTHOR>
 *
 */
public class UnitConverModel implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 转换比例
     */
    private Long ratio;

    /**
     * 操作符
     */
    private Integer operator;

    /**
     * 模型名称
     */
    private String modelName;

    /**
     * 保留精度
     */
    private Integer precision;
    
    /**
     * 模型名称中文
     */
    private String modelNameZH;
    
    /**
     * 模型名称英文
     */
    private String modelNameEN;
    
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getRatio() {
        return ratio;
    }

    public void setRatio(Long ratio) {
        this.ratio = ratio;
    }

    public Integer getOperator() {
        return operator;
    }

    public void setOperator(Integer operator) {
        this.operator = operator;
    }

    public String getModelName() {
        return modelName;
    }

    public void setModelName(String modelName) {
        this.modelName = modelName;
    }

    public Integer getPrecision() {
        return precision;
    }

    public void setPrecision(Integer precision) {
        this.precision = precision;
    }

	public String getModelNameZH() {
		return modelNameZH;
	}

	public void setModelNameZH(String modelNameZH) {
		this.modelNameZH = modelNameZH;
	}

	public String getModelNameEN() {
		return modelNameEN;
	}

	public void setModelNameEN(String modelNameEN) {
		this.modelNameEN = modelNameEN;
	}


}
