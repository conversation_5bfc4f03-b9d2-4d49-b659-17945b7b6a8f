package com.uino.init;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.uino.api.client.sys.ICIOperateLogApiSvc;
import com.uino.api.client.sys.IOperateLogApiSvc;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RefreshScope
public class ClearLogJob {
    /**
     * 日志保留时长-单位天
     */
    @Value("${uino.log.clear.duration:7}")
    private int clearLogDuration;
    @Autowired
    private ICIOperateLogApiSvc ciLogSvc;
    @Autowired
    private IOperateLogApiSvc opLogSvc;

    /**
     * 定时清理配置日志及操作日志，每天凌晨2点执行
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void clearLog() {
        try {
            ciLogSvc.clearCIOperateLogByDuration(clearLogDuration);
        } catch (Exception e) {
            log.error("配置日志清理发生异常", e);
        }
        try {
            opLogSvc.clearOperateLogByDuration(clearLogDuration);
        } catch (Exception e) {
            log.error("操作日志清理发生异常", e);
        }
    }
}
