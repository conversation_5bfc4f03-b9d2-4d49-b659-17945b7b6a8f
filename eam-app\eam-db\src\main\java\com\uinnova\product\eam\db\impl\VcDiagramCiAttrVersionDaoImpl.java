package com.uinnova.product.eam.db.impl;


import com.uinnova.product.eam.comm.model.CVcDiagramCiAttrVersion;
import com.uinnova.product.eam.comm.model.VcDiagramCiAttrVersion;
import com.uinnova.product.eam.db.VcDiagramCiAttrVersionDao;
import com.uinnova.product.eam.db.support.dao.mybatis.ComMyBatisBinaryDaoImpl;


/**
 * 视图CI属性显示版本表[VC_DIAGRAM_CI_ATTR_VERSION]数据访问对象实现
 */
public class VcDiagramCiAttrVersionDaoImpl extends ComMyBatisBinaryDaoImpl<VcDiagramCiAttrVersion, CVcDiagramCiAttrVersion> implements VcDiagramCiAttrVersionDao {

//    @Override
//    public String getTableName() {
//        return "IAMS." + getDaoDefinition().getTableName();
//    }
}


