package com.uino.api.client.permission;

import java.util.List;

import com.uino.bean.permission.query.CSysRoleDataModuleRlt;
import org.elasticsearch.index.query.QueryBuilder;

import com.binary.jdbc.Page;
import com.uino.bean.permission.base.SysDataModule;
import com.uino.bean.permission.base.SysRole;
import com.uino.bean.permission.base.SysRoleDataModuleRlt;
import com.uino.bean.permission.base.SysRoleModuleRlt;
import com.uino.bean.permission.base.SysUserDataModuleRlt;
import com.uino.bean.permission.base.SysUserModuleRlt;
import com.uino.bean.permission.base.SysUserRoleRlt;
import com.uino.bean.permission.business.DataRole;
import com.uino.bean.permission.business.ModuleNodeInfo;
import com.uino.bean.permission.business.request.OptionUserModuleAuthRequestDto;
import com.uino.bean.permission.business.request.SaveRoleOrgRltRequestDto;
import com.uino.bean.permission.business.request.SaveRoleUserRltRequestDto;
import com.uino.bean.permission.query.CAuthBean;
import com.uino.bean.permission.query.SearchKeywordBean;

/**
 * 角色权限相关服务
 * 
 * <AUTHOR>
 */
public interface IRoleApiSvc {
    /**
     * 保存/更新
     * 
     * @param role
     * @return
     */
    Long saveOrUpdate(SysRole role);

    /**
     * 批量保存/更新
     *
     * @param roles
     * @return
     */
    Integer saveOrUpdateBatch(Long domainId,List<SysRole> roles);

    /**
     * 删除
     * 
     * @param roleId
     * @return
     */
    Integer deleteById(Long roleId);

    /**
     * 角色搜索--分页
     * 
     * @param bean
     * @return
     */
    Page<SysRole> getRolePageByQuery(SearchKeywordBean bean);

    /**
     * 根据id获取角色信息
     * @param ids
     * @return
     */
    List<SysRole> getRoleListByIds(List<Long> ids,Long domainId);

    /**
     * 绑定角色和用户关系-全量
     * 
     * @param bean
     * @return
     */
    Integer addRoleUserRlt(SaveRoleUserRltRequestDto bean);

    /**
     * 绑定角色和组织关系-全量
     * 
     * @param bean
     * @return
     */
    Integer addRoleOrgRlt(SaveRoleOrgRltRequestDto bean);

    /**
     * 添加角色和菜单的关系
     * 
     * @param bean
     * @return
     */
    Integer addRoleMenuRlt(List<SysRoleModuleRlt> bean);
    Integer addRoleMenuRlt(Long domainId,List<SysRoleModuleRlt> bean);

    /**
     * 添加角色和数据模块的关系
     * 
     * @param bean
     * @return
     */
    Integer addRoleDataModuleRlt(List<SysRoleDataModuleRlt> bean);
    Integer addRoleDataModuleRlt(Long domainId,List<SysRoleDataModuleRlt> bean);

	/**
	 * 
	 * @description
	 * @author: ZMJ
	 * @param domainId
	 *            所属域id
	 * @param rlts
	 *            角色-数据权限关系集合
	 * @param isComplete
	 *            是否全量(全量时仅支持保存一个角色的权限数据)
	 * @return
	 * @example
	 */
	Integer addRoleDataModuleRlt(Long domainId, List<SysRoleDataModuleRlt> rlts, boolean isComplete);

    /**
     * 添加用户和菜单的关系
     * 
     * @param bean
     * @return
     */
    Integer addUserMenuRlt(List<SysUserModuleRlt> bean);
    Integer addUserMenuRlt(Long domainId,List<SysUserModuleRlt> bean);

    /**
     * 添加用户和数据模块的关系
     * 
     * @param bean
     * @return
     */
    Integer addUserDataModuleRlt(List<SysUserDataModuleRlt> bean);
    Integer addUserDataModuleRlt(Long domainId,List<SysUserDataModuleRlt> bean);
    /**
     * 注册数据权限菜单项
     * 
     * @param dataModule
     * @return
     */
    Long addDataModuleMenu(SysDataModule dataModule);

    /**
     * 获取CI分类数据项
     * 
     * @return
     */
    List<DataRole> getDataRoleByCIClass();

    List<DataRole> getDataRoleByCIClass(Long domainId);
    /**
     * 获取CI标签数据项
     * 
     * @return
     */
    List<DataRole> getDataRoleByCITag();

    List<DataRole> getDataRoleByCITag(Long domainId);

    /**
     * 获取数据权限菜单
     * 
     * @return
     */
    List<SysDataModule> getAllDataRoleMenu();
    List<SysDataModule> getAllDataRoleMenu(Long domainId);


    List<SysRoleDataModuleRlt> getRoleDataModuleRltByCdt(CSysRoleDataModuleRlt cdt);
    /**
     * 获取菜单
     * 
     * @return
     */
    ModuleNodeInfo getAllMenu();
    ModuleNodeInfo getAllMenu(Long domainId);

    /**
     * 获取角色下的菜单权限
     * 
     * @param roleId
     * @return
     */
    List<SysRoleModuleRlt> getAuthMenuByRoleId(Long roleId);


    /**
     * 获取角色下的数据权限
     * 
     * @param bean
     * @return
     */
    List<SysRoleDataModuleRlt> getAuthDataRoleByRoleId(CAuthBean bean);

    /**
     * 获取用户下的所有数据权限（roleId为空或不存在，表示是来自于用户本身）
     * 
     * @param bean
     * @return
     */
    List<SysRoleDataModuleRlt> getAuthDataRoleByUserId(CAuthBean bean);

    /**
     * 获取用户下的所有菜单权限（roleId为空或不存在，表示是来自于用户本身）
     * 
     * @param userId
     * @return
     */
    List<SysRoleModuleRlt> getAuthMenuByUserId(Long userId);

    /**
     * 获取某个用户绑定的角色信息
     * 
     * @param userId
     * @return
     */
    List<SysRole> getRolesByUserId(Long userId);

    /**
     * 根据数据模块id-转发数据模块数据源地址获取 数据模块数据
     * 
     * @param dataModuleId
     * @return
     */
    Object getDataModuleDataById(Long dataModuleId);

    /**
     * 获取用户下的数据权限
     * 
     * @param bean
     * @return
     */
    List<SysRoleDataModuleRlt> getUserAuthDataRoleByUserId(CAuthBean bean);

    /**
     * 获取用户下的所有角色数据权限（角色合并后的权限）
     * 
     * @param bean
     * @return
     */
    List<SysRoleDataModuleRlt> getRoleAuthDataRoleByUserId(CAuthBean bean);

    /**
     * 获取某个组织的角色
     * 
     * @param orgId
     * @return
     */
    List<SysRole> getRolesByOrgId(Long orgId);

    /**
     * 统计符合条件角色数量
     * 
     * @param query
     * @return
     */
    long countByCondition(QueryBuilder query);

    /**
     * 获取符合条件的角色
     * 
     * @param query
     * @return
     */
    List<SysRole> getRolesByQuery(QueryBuilder query);

    /**
     * 操作用户与模块之间权限
     * 
     * @param req
     */
    void optionUserModuleAuth(OptionUserModuleAuthRequestDto req);

    /**
     * 根据角色id查询用户角色关系(包含来自于组织的角色,id为空表示来自于组织的角色)
     * 
     * @param roleId
     * @return
     */
    List<SysUserRoleRlt> getUserRoleRltByRoleId(Long roleId);

    Integer deleteRoleDataModuleRlt(CSysRoleDataModuleRlt cdt);
}
