package com.uino.bean.cmdb.query;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.vmdb.comm.bean.CIState;
import com.uinnova.product.vmdb.comm.model.ci.CCcCi;
import com.uino.bean.cmdb.base.LibType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 全文检索或条件查询，各个字段之间是AND关系
 * 
 * <AUTHOR>
 *
 */
@ApiModel(value = "对象查询类", description = "对象查询类")
@Getter
@Setter
public class ESCISearchBean extends ESSearchBase implements Serializable {

    private static final long serialVersionUID = 9071314721828145382L;

    /**
     * 查询条件
     */
	@ApiModelProperty(value = "CI查询条件")
    private CCcCi cdt;

	/**
	 * 参与检索的字段-与words配合使用
	 */
	@ApiModelProperty(value = "参与检索的字段-与word配合使用", example = "[\"ciCode\",\"ciPrimaryKey\",\"属性名称\"]")
	private List<String> searchKeys = new ArrayList<>();

    /**
     * 全文检索 关键字之间是 and 关系
     */
	@ApiModelProperty(value = "全文检索关键字列表")
    private List<String> words = new ArrayList<>();

    @ApiModelProperty(value = "关键字是否只查label字段")
    private Boolean wordLabel = false;

    /**
     * 标签之间是 and 关系
     */
	@ApiModelProperty(value = "标签id列表")
    private List<Long> tagIds = new ArrayList<>();

    @ApiModelProperty(value = "用户唯一标识编码")
    private String ownerCode;

    @ApiModelProperty(value = "数据状态")
    private List<CIState> states = new ArrayList<>();

    private List<Integer> stateCodes = new ArrayList<>();

    /**
     * 数据域ID，默认为1
     */
	@ApiModelProperty(value = "所属域id",example = "1")
    private Long domainId;

    /**
     * 属性之间是AND 关系
     */
	@ApiModelProperty(value = "属性查询(and关系)")
    private List<ESAttrBean> andAttrs = new ArrayList<ESAttrBean>();

    /**
     * 属性之间是OR 关系
     */
	@ApiModelProperty(value = "属性查询(or关系)")
    private List<ESAttrBean> orAttrs = new ArrayList<ESAttrBean>();

    /**
     * CI分类ID，它们之间是or的关系，范围内的分类
     */
	@ApiModelProperty(value = "CI所属分类id")
    private List<Long> classIds = new ArrayList<>();

    /**
     * CICODE 以前的唯一值，现在可能不是唯一值
     */
	@ApiModelProperty(value = "ciCode列表")
    private List<String> ciCodes = new ArrayList<>();

    @ApiModelProperty(value = "排除ciCode列表mustNot")
    private List<String> notCiCodes = new ArrayList<>();

    /**
     * 业务主键
     */
	@ApiModelProperty(value = "业务主键列表")
    private Collection<String> ciPrimaryKeys = new HashSet<>();

    /**
     * hashCode 业务主键，它们之间是or的关系，范围内的业务主键
     */
	@ApiModelProperty(value = "hashCode列表")
    private List<Long> hashCodes = new ArrayList<>();

    /**
     * 唯一标识，它们之间是or的关系
     */
	@ApiModelProperty(value = "对象id列表")
    private List<Long> ids = new ArrayList<>();

	@ApiModelProperty(value = "排序字段")
    private String sortField;

	@ApiModelProperty(value = "是否升序", example = "true/false 默认false")
    private boolean asc = false;

	@ApiModelProperty(value = "关键字是否高亮", example = "true/false 默认false")
    private boolean highLight = false;

	@ApiModelProperty(value = "高亮字段")
    private List<String> highLightFields;

    /**
     * EA产品字段
     */
    @ApiModelProperty(value = "视图id")
    private String diagramId;

    /**
     * DCV属性之间是AND 关系
     */
	@ApiModelProperty(value = "DCV属性and查询")
    private List<ESAttrBean> andDcvAttrs = new ArrayList<ESAttrBean>();

    /**
     * DCV属性之间是OR 关系
     */
	@ApiModelProperty(value = "DCV属性or查询")
    private List<ESAttrBean> orDcvAttrs = new ArrayList<ESAttrBean>();

    /**
     * ruleItems 用户选择的属性和值组成的且关系然后组成的或关系集合，每个OrRuleItem之间都是或关系
     */
	@ApiModelProperty(value = " ruleItems 用户选择的属性和值组成的且关系然后组成的或关系集合，每个OrRuleItem之间都是或关系")
    private List<ESOrRuleItem> ruleItems = new ArrayList<ESOrRuleItem>();

    /**
     * and连接分类属性组查询
     */
	@ApiModelProperty(value = " and连接分类属性组查询")
    private List<ClsAttrQueryGroup> andClsAttrQueryGroups = new ArrayList<>();

    /**
     * or连接分类属性组查询
     */
	@ApiModelProperty(value = " or连接分类属性组查询")
    private List<ClsAttrQueryGroup> orClsAttrQueryGroups = new ArrayList<>();

    /**
     * and连接分类属性组查询
     */
    @ApiModelProperty(value = " and连接分类属性组查询")
    private AndAttrsQueryGroup andAttrsQueryGroup = new AndAttrsQueryGroup();

    @ApiModelProperty(value = " 默认查询基线库")
    private LibType libType = LibType.BASELINE;

    @ApiModelProperty(value = "是否使用权限控制")
    private Boolean permission = false;

    @ApiModelProperty(value = "仅查询ci属性tag[搜索项]标识")
    private Boolean queryTag = Boolean.FALSE;

    public void setStates(List<CIState> states) {
        this.states = states;
        setStateCodes(states.stream().map(CIState::val).collect(Collectors.toList()));
    }

    private void setStateCodes(List<Integer> stateCodes){
        this.stateCodes = stateCodes;
    }
}
