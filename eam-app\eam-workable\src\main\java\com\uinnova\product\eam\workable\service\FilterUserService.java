package com.uinnova.product.eam.workable.service;

import com.uinnova.product.eam.workable.model.FilterUser;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface FilterUserService {

    /**
     * 保存同意人员的记录信息
     * @param filterUser
     * @return
     */
    String saveAgreeUser(FilterUser filterUser);

    /**
     * 查询当前流程实例任务下面委派人同意的列表
     * @param processInstanceId
     * @param taskKey
     * @return
     */
    List<FilterUser> getCurrentTaskAgreeUser(String processInstanceId,String taskKey);


    /**
     * 流程结束是将本次流程数据删除
     * @param processInstanceId
     */
    void deleteFilterUserByProcessInstanceId(String processInstanceId);
}
