package com.uinnova.product.eam.service.fx;

import java.util.List;
import java.util.Map;

/**
 *  伏羲 通用发布相关
 */
public interface GeneralPushSvc {

    /**
     * 视图发布 单图发布
     * @param diagramId 视图EID
     * @param releaseDesc 视图发布描述
     * @param dirId 视图发布目录
     * @param needApproval 审批发布-true 直接发布-false
     * @return 返回发布后设计库视图的EID
     */
    String publishDiagram(String diagramId, String releaseDesc, Long dirId, String releaseDiagramId, Boolean needApproval);

    /**
     * 定制化 视图发布
     * @param diagramId 视图EID
     * @param releaseDesc 视图发布描述
     * @param dirId 视图发布目录
     * @param isCustom 是否为定制化
     * @return 返回发布后设计库视图的EID
     */
    String htPublishDiagram(String diagramId, String releaseDesc, Long dirId, String releaseDiagramId, Boolean isCustom);
    
    /**
     * 视图发布 批量发布
     * @param diagramIds 视图EIDS
     * @param publishDirSite 视图EID 与 视图发布的DIR_ID
     * @return
     */
    Map<String, String> batchPublishDiagram(List<String> diagramIds, Map<String, Long> publishDirSite);

    /**
     * 发布前根据视图IDs校验冲突 支持批量 支持多用户
     * @param diagramIds
     * @return 返回值map中的key为冲突类型 value为冲突内容
     */
    Map<Integer, Object> publishCheck(List<String> diagramIds);

    /**
     *  模型及模型视图发布校验
     * @param dirIds 目录ID集合
     * @param diagramIds 视图ID集合
     * @return 校验结果
     */
    Map<Integer, Object> pushCkeck(List<Long> dirIds, List<String> diagramIds);

    /**
     * 根据校验类型进行数据校验
     * @param dirIds 文件夹IDs
     * @param diagramIds 视图IDs
     * @param checkType 校验类型
     * @return 校验结果
     */
    Map<Integer, Object> checkByType(List<Long> dirIds, List<String> diagramIds,int checkType);

}
