package com.uino.provider.server.web.cmdb.mvc;

import com.uino.service.cmdb.microservice.ITaskLockSvc;
import com.uino.provider.feign.cmdb.TaskLockFeign;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Classname TaskLockFeignMvc
 * @Description TODO
 * @Date 2020/6/28 19:03
 * <AUTHOR> sh
 */
@RestController
@RequestMapping("feign/taskLocal")
public class TaskLockFeignMvc implements TaskLockFeign {

    @Autowired
    private ITaskLockSvc iTaskLockSvc;

    @Override
    public boolean getLock(String taskName, long taskExecuteTime) {
        return iTaskLockSvc.getLock(taskName, taskExecuteTime);
    }

    @Override
    public void breakLock(String taskName) {
        iTaskLockSvc.breakLock(taskName);
    }

    @Override
    public boolean addLock(String taskName) {
        return iTaskLockSvc.addLock(taskName);
    }

    @Override
    public boolean isLocked(String taskName) {
        return iTaskLockSvc.isLocked(taskName);
    }
}
