package com.uinnova.product.vmdb.comm.model.rule;

import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;

/**
 * 
 * <AUTHOR>
 *
 */
@Comment("朋友圈实例分层[CC_RLT_RULE_INST_LAYER]")
public class CcRltRuleInstLayer implements EntityBean {
    private static final long serialVersionUID = 1L;

    @Comment("ID[ID]")
    private Long id;

    @Comment("所属朋友圈规则[RULE_DEF_ID]")
    private Long ruleDefId;

    @Comment("所属朋友圈实例[RULE_INST_ID]")
    private Long ruleInstId;

    @Comment("分层名称[LAYER_NAME]")
    private String layerName;

    @Comment("背景图[BG_PATH]    非运算")
    private String bgPath;

    @Comment("背景样式[BG_CSS]    排列顺序")
    private String bgCss;

    @Comment("排列顺序[ORDER_NO]")
    private Integer orderNo;

    @Comment("所属域[DOMAIN_ID]")
    private Long domainId;

    @Comment("数据状态[DATA_STATUS]    数据状态:数据状态：1-正常 0-删除")
    private Integer dataStatus;

    @Comment("创建时间[CREATE_TIME]    创建时间:yyyyMMddHHmmss")
    private Long createTime;

    @Comment("修改时间[MODIFY_TIME]    修改时间:yyyyMMddHHmmss")
    private Long modifyTime;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getRuleDefId() {
        return this.ruleDefId;
    }

    public void setRuleDefId(Long ruleDefId) {
        this.ruleDefId = ruleDefId;
    }

    public Long getRuleInstId() {
        return this.ruleInstId;
    }

    public void setRuleInstId(Long ruleInstId) {
        this.ruleInstId = ruleInstId;
    }

    public String getLayerName() {
        return this.layerName;
    }

    public void setLayerName(String layerName) {
        this.layerName = layerName;
    }

    public String getBgPath() {
        return this.bgPath;
    }

    public void setBgPath(String bgPath) {
        this.bgPath = bgPath;
    }

    public String getBgCss() {
        return this.bgCss;
    }

    public void setBgCss(String bgCss) {
        this.bgCss = bgCss;
    }

    public Integer getOrderNo() {
        return this.orderNo;
    }

    public void setOrderNo(Integer orderNo) {
        this.orderNo = orderNo;
    }

    public Long getDomainId() {
        return this.domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }

    public Integer getDataStatus() {
        return this.dataStatus;
    }

    public void setDataStatus(Integer dataStatus) {
        this.dataStatus = dataStatus;
    }

    public Long getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getModifyTime() {
        return this.modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }

}
