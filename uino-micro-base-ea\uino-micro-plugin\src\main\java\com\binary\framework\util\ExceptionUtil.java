package com.binary.framework.util;

import com.binary.core.exception.BinaryException;
import com.binary.core.util.BinaryUtils;
import com.binary.framework.exception.ServiceException;

public abstract class ExceptionUtil {
	
	
	
	
	/**
	 * 获取实际抛出的异常
	 * @param t
	 * @return
	 */
	public static Throwable getRealThrowable(Throwable t) {
        if (t instanceof BinaryException) {
            BinaryException tx = (BinaryException) t;
            Throwable top = tx.getOriginalThrowable();
            if (top != null && tx != top) { return getRealThrowable(top); }
        } else {
            boolean hasDubbo = true;
            try {
                Class.forName("com.alibaba.dubbo.rpc.RpcException");
            } catch (ClassNotFoundException e) {
                hasDubbo = false;
            }
            Throwable tx = t.getCause();
            if (tx != null && tx != t) { return getRealThrowable(tx); }
        }
        return BinaryUtils.transException(t, ServiceException.class).getOriginalThrowable();
	}
	
}
