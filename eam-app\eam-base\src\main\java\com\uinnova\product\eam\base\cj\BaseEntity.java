package com.uinnova.product.eam.base.cj;

import lombok.Data;

import java.io.Serializable;

/**
 * @description: 基础字段
 * @author: Lc
 * @create: 2022-01-05 16:44
 */
@Data
public class BaseEntity implements Serializable{

    /**
     * 主键id
     */
    private Long id;

    /**
     * 创建人 账号
     */
    private String creatorCode;

    /**
     * 创建人 姓名
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 修改者 工号
     */
    private String modifierCode;

    /**
     * 修改者 姓名
     */
    private String modifierName;

    /**
     * 修改时间
     */
    private Long modifyTime;

    /**
     * 租户标识
     */
    private Long domainId = 1L;
}
