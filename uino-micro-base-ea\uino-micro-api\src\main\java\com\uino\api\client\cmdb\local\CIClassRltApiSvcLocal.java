package com.uino.api.client.cmdb.local;

import java.util.List;

import com.uino.dao.BaseConst;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.binary.jdbc.Page;
import com.uino.service.cmdb.microservice.ICIClassRltSvc;
import com.uino.bean.cmdb.base.ESCiClassRlt;
import com.uino.bean.cmdb.business.BindCIClassRltDto;
import com.uino.bean.cmdb.business.ClassRltQueryDto;
import com.uino.api.client.cmdb.ICIClassRltApiSvc;

@Service
public class CIClassRltApiSvcLocal implements ICIClassRltApiSvc {

    @Autowired
    private ICIClassRltSvc svc;

    @Override
    public Page<ESCiClassRlt> queryClassRltPage(ClassRltQueryDto queryDto) {
        // TODO Auto-generated method stub
        return svc.queryClassRltPage(queryDto);
    }

    @Override
    public List<ESCiClassRlt> queryClassRlt(ClassRltQueryDto queryDto) {
        // TODO Auto-generated method stub
        return svc.queryClassRlt(queryDto);
    }

    @Override
    public boolean existRlt(long sourceClsId, long targetClsId, long clsId, boolean onlyCurrentModel) {
        // TODO Auto-generated method stub
        return svc.existRlt(sourceClsId, targetClsId, clsId, onlyCurrentModel);
    }

	@Override
	public Integer saveRlts(List<ESCiClassRlt> classRlts) {
		return svc.saveRlts(BaseConst.DEFAULT_DOMAIN_ID, classRlts);
	}

	@Override
	public Integer saveRlts(Long domainId, List<ESCiClassRlt> classRlts) {
		return svc.saveRlts(domainId, classRlts);
	}

	@Override
	public Integer saveRltsForClass(BindCIClassRltDto dto) {
		return svc.saveRltsForClass(dto);
	}

	@Override
	public Integer deleteRltsByClassId(Long classId) {
		return svc.deleteRltsByClassId(classId);
	}

	@Override
	public Integer deleteClassRlt(ESCiClassRlt rlt) {
		return svc.deleteClassRlt(rlt);
	}
}
