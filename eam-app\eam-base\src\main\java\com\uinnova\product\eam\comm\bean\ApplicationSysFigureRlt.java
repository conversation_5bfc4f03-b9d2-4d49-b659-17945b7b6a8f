package com.uinnova.product.eam.comm.bean;

import com.binary.framework.bean.annotation.Comment;

import java.io.Serializable;
import java.util.Objects;

/**
 * 应用系统下面的所有图
 *
 * <AUTHOR>
 */
public class ApplicationSysFigureRlt implements Serializable {

    @Comment("应用系统与图关系id")
    private Long id;

    @Comment("应用系统ciCode")
    private String ciCode;

    @Comment("业务能力图id")
    private Long businessCapacityId;

    @Comment("业务场景图id")
    private Long businessSceneId;

    @Comment("业务流程图id")
    private Long businessProcessId;

    @Comment("逻辑架构图id")
    private Long logicalArchitectureId;

    @Comment("er图id")
    private Long erDiagramId;

    @Comment("数据流图id")
    private Long dataFlowId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCiCode() {
        return ciCode;
    }

    public void setCiCode(String ciCode) {
        this.ciCode = ciCode;
    }

    public Long getBusinessCapacityId() {
        return businessCapacityId;
    }

    public void setBusinessCapacityId(Long businessCapacityId) {
        this.businessCapacityId = businessCapacityId;
    }

    public Long getBusinessSceneId() {
        return businessSceneId;
    }

    public void setBusinessSceneId(Long businessSceneId) {
        this.businessSceneId = businessSceneId;
    }

    public Long getBusinessProcessId() {
        return businessProcessId;
    }

    public void setBusinessProcessId(Long businessProcessId) {
        this.businessProcessId = businessProcessId;
    }

    public Long getLogicalArchitectureId() {
        return logicalArchitectureId;
    }

    public void setLogicalArchitectureId(Long logicalArchitectureId) {
        this.logicalArchitectureId = logicalArchitectureId;
    }

    public Long getErDiagramId() {
        return erDiagramId;
    }

    public void setErDiagramId(Long erDiagramId) {
        this.erDiagramId = erDiagramId;
    }

    public Long getDataFlowId() {
        return dataFlowId;
    }

    public void setDataFlowId(Long dataFlowId) {
        this.dataFlowId = dataFlowId;
    }
}
