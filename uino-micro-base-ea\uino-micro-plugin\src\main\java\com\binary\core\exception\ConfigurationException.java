package com.binary.core.exception;

public class ConfigurationException extends CoreException {
	private static final long serialVersionUID = 1L;

	public ConfigurationException() {
		super();
	}
	
	public ConfigurationException(String message) {
		super(message);
	}
	
	public ConfigurationException(Throwable cause) {
		super(cause);
	}
	
	public ConfigurationException(String message, Throwable cause) {
		super(message, cause);
	}
	
	
}


