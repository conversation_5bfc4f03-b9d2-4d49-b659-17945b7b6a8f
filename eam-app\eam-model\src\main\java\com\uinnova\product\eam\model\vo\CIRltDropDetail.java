package com.uinnova.product.eam.model.vo;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

@Comment("关系删除详情")
@Data
public class CIRltDropDetail {

    @Comment("关系code")
    private String rltCode;
    @Comment("源端分类id")
    private Long sourceCIClassId;
    @Comment("源端分类图标")
    private String sourceCIClassIcon;
    @Comment("源端分类名称")
    private String sourceCIClassName;
    @Comment("源端ciCode")
    private String sourceCICode;
    @Comment("源端ci名称")
    private String sourceCIName;
    @Comment("源端对象展示名称")
    private String sourceName;
    @Comment("关系线样式")
    private String rltLineType;
    @Comment("关系名称")
    private String rltClassName;
    @Comment("目标端分类id")
    private Long targetCIClassId;
    @Comment("目标端分类图标")
    private String targetCIClassIcon;
    @Comment("目标端分类名称")
    private String targetCIClassName;
    @Comment("目标端ciCode")
    private String targetCICode;
    @Comment("目标端ci名称")
    private String targetCIName;
    @Comment("操作类型：1=保留，2=删除")
    private Integer opType;

    public String getSourceName() {
        return sourceCIClassName.concat("/").concat(sourceCIName);
    }
}
