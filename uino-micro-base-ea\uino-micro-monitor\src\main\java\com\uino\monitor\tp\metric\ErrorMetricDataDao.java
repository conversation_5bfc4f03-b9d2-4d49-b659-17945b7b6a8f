package com.uino.monitor.tp.metric;

import com.alibaba.fastjson.JSONObject;
import com.uino.dao.AbstractESBaseDao;
import com.uino.dao.ESConst;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

/**
 * 错误格式的性能数据
 * @author: we<PERSON><PERSON>ong
 * @date: 2021/02/24 14:57
 **/
@Repository
@Slf4j
public class ErrorMetricDataDao extends AbstractESBaseDao<JSONObject, JSONObject> {

    @Override
    public String getIndex() {
		return ESConst.ERROR_METRIC_DATA;
    }

    @Override
    public String getType() {
		return ESConst.ERROR_METRIC_DATA;
    }

    @PostConstruct
    public void init() {
        // this.initIndex();
    }
}
