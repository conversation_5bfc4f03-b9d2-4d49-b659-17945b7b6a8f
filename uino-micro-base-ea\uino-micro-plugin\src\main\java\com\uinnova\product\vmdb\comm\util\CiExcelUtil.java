package com.uinnova.product.vmdb.comm.util;

import com.binary.core.bean.BMProxy;
import com.binary.core.lang.Conver;
import com.binary.core.util.BinaryUtils;
import com.binary.tools.excel.Column;
import com.binary.tools.excel.ColumnRender;
import com.binary.tools.excel.ExcelStyle;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.usermodel.Row.MissingCellPolicy;

import java.util.*;

/**
 *
 * <AUTHOR>
 *
 */
public class CiExcelUtil {
    /**
     * 去除导入XLS SHEET页后缀“（NUM）”
     *
     * @return 分类代码
     */
    public static String removeSheetSuffix(String sheetName) {
        int s = sheetName.lastIndexOf("(");
        if (s >= 0) {
            String numStr = sheetName.substring(s);
            char right = ')';
            if (numStr.substring(numStr.length() - 1).equals(right)) {
                numStr = numStr.substring(1, numStr.length() - 1);
                String reg = "[1-9][0-9]*";
                if (numStr.matches(reg)) {
                    sheetName = sheetName.substring(0, s);
                }
            }
        }
        return sheetName.replace("&right;", "\\").replace("&left;", "/").replace("^", "*").replace("<<", "[").replace(">>", "]");
    }

    /**
     * 还原EXCEL中SHEET名称的\ / ? * [ ]
     *
     * @return 分类代码
     */
    public static String convertSheetNameSpecialChar(String sheetName) {
        return sheetName.replace("\\", "&right;").replace("/", "&left;").replace("*", "^").replace("[", "<<").replace("]", ">>");
    }

    private static void majorAttrColor(final Integer isMajor, HSSFCellStyle cellstyle, HSSFWorkbook hwb) {
        Integer m = 1;
        if (m.equals(isMajor)) {
            cellstyle.getFont(hwb).setColor(IndexedColors.RED.index);
        } else {
            cellstyle.getFont(hwb).setColor(IndexedColors.BLACK.index);
        }
    }

    public static Object getValue(Cell cell) {
        if (cell == null) {
            return null;
        }

        if (cell.getCellType() == CellType.STRING) {
            return cell.getStringCellValue();
        } else if (cell.getCellType() == CellType.NUMERIC) {
            Double doubleValue = Double.valueOf(cell.getNumericCellValue());
            return doubleValue - doubleValue.intValue() == 0 ? doubleValue.intValue() + "" : doubleValue + "";
        } else if (cell.getCellType() == CellType.BOOLEAN) {
            return Boolean.valueOf(cell.getBooleanCellValue());
        } else if (cell.getCellType() == CellType.BLANK) {
            return "";
        } else {
            try {
                return cell.getDateCellValue();
            } catch (Exception e) {
                return cell.toString();
            }
        }

    }

    public static String getStringValue(Cell cell) {
        Object value = getValue(cell);
        if (value == null) {
            return (String) value;
        }
        return value.toString().trim();
    }

    public static List<Map<String, String>> getSheetVal(HSSFSheet sheet) {
        List<Map<String, String>> datas = new ArrayList<Map<String, String>>();

        int lastRowNum = sheet.getLastRowNum();
        HSSFRow titleRow = sheet.getRow(0);
        if (titleRow == null) {
            return datas;
        }

        short lastCellNum = titleRow.getLastCellNum();

        List<String> keys = new ArrayList<String>();
        for (int i = 0; i < lastCellNum; i++) {
            HSSFCell cell = titleRow.getCell(i);
            Object value = getValue(cell);
            keys.add(value.toString());
        }

        int size = keys.size();
        for (int i = 1; i <= lastRowNum; i++) {
            Map<String, String> data = new HashMap<String, String>();
            HSSFRow row = sheet.getRow(i);
            if (row != null) {
                for (int j = 0; j < size; j++) {
                    HSSFCell cell = row.getCell(j);

                    if (!BinaryUtils.isEmpty(cell)) {
                        CellType cellType = cell.getCellType();
                        if (CellType.NUMERIC == cellType) {
                            if (DateUtil.isCellDateFormatted(cell)) {
                                Date d = cell.getDateCellValue();
                                long time = d.getTime();

                                String v = Conver.to(d, String.class, time > 1500000000000l ? "yyyy-MM-dd HH:mm:ss" : "HH:mm:ss");
                                int idx = v.indexOf(' ');
                                if (idx > 0 && v.endsWith("00:00:00")) {
                                    v = v.substring(0, idx);
                                }
                                cell.setCellValue(v);
                            }
                        }
                    }
                    String key = keys.get(j);
                    String value = getStringValue(cell);
                    data.put(key, value);
                }
            }
            datas.add(data);
        }
        return datas;
    }

    /**
     * 获取sheet正文内容(通用XLS和XLSX两种格式)
     *
     * @param sheet
     * @return
     */
    public static List<Map<String, String>> getGeneralSheetVals(Sheet sheet) {
        List<Map<String, String>> datas = new ArrayList<Map<String, String>>();

        int lastRowNum = sheet.getLastRowNum();
        Row titleRow = sheet.getRow(0);
        if (titleRow == null) {
            return datas;
        }

        short lastCellNum = titleRow.getLastCellNum();

        List<String> keys = new ArrayList<String>();
        for (int i = 0; i < lastCellNum; i++) {
            Cell cell = titleRow.getCell(i);
            Object value = getValue(cell);
            keys.add(value.toString());
        }

        int size = keys.size();
        for (int i = 1; i <= lastRowNum; i++) {
            Map<String, String> data = new HashMap<String, String>();
            Row row = sheet.getRow(i);
            if (row != null) {
                for (int j = 0; j < size; j++) {
                    Cell cell = row.getCell(j);

                    if (!BinaryUtils.isEmpty(cell)) {
                        CellType cellType = cell.getCellType();
                        if (CellType.NUMERIC == cellType) {
                            if (DateUtil.isCellDateFormatted(cell)) {
                                Date d = cell.getDateCellValue();
                                long time = d.getTime();

                                String v = Conver.to(d, String.class, time > 1500000000000l ? "yyyy-MM-dd HH:mm:ss" : "HH:mm:ss");
                                int idx = v.indexOf(' ');
                                if (idx > 0 && v.endsWith("00:00:00")) {
                                    v = v.substring(0, idx);
                                }
                                cell.setCellValue(v);
                            }
                        }
                    }
                    String key = keys.get(j);
                    String value = getStringValue(cell);
                    data.put(key, value);
                }
            }
            datas.add(data);
        }
        return datas;
    }

    public static final int FAILURE = 0;
    public static final int SUCCESS = 1;
    public static final int EMPTY_VAL = 2;
    public static final int OVER_LENGTH = 3;
    public static final int FORMAT_ERROR = 4;
    public static final int MUST_VAL = 5;
    public static final int MAJOR_EXIST = 6;
    public static final int DUPLICATE_VALUE = 7;
    public static final int EXIST = 8;
    public static final int NOT_EXIST = 9;

    /**
     * 验证属性的值是否符合要求
     *
     * @param def
     * @param val
     *            被验证的值
     * @return
     */
    public static Integer validateAttrValType(CcCiAttrDef def, String val) {
        if (def == null) {
            throw new NullPointerException("def is null");
        }

        PropertyType type = PropertyType.valueOf(def.getProType());

        if (BinaryUtils.isEmpty(val)) {
            return def.getIsRequired() != 1 ? SUCCESS : MUST_VAL;
        }

        int valLength = 200;

        switch (type) {
        case INTEGER: {
            if (val != null) {
                if (!CommUtil.INTEGER_REGEX.matcher(val).matches()) {
                    return FORMAT_ERROR;
                }
            }
            return SUCCESS;
        }
        case DOUBLE: {
            if (val != null) {
                if (!CommUtil.DOUBLE_REGEX.matcher(val).matches()) {
                    return FORMAT_ERROR;
                }
            }
            return SUCCESS;
        }
        case ENUM: {
            if (val != null) {
                if (val.length() > valLength) {
                    return OVER_LENGTH;
                }
                if (CommUtil.STRING_REGEX.matcher(val).find()) {
                    return FORMAT_ERROR;
                }
            }
            return SUCCESS;
        }
        case DATE: {

            if (val != null && val.length() > valLength) {
                System.out.println(val.length());
                return OVER_LENGTH;
            }
            return SUCCESS;
        }
        case VARCHAR: {
            if (val != null) {
                if (val.length() > valLength) {
                    return OVER_LENGTH;
                }
                if (CommUtil.STRING_REGEX.matcher(val).find()) {
                    return FORMAT_ERROR;
                }
            }
            return SUCCESS;
        }
        case LONG_VARCHAR: {
            if (val != null) {
                int longValLength = 1000;
                if (val.length() > longValLength) {
                    return OVER_LENGTH;
                }
                if (CommUtil.STRING_REGEX.matcher(val).find()) {
                    return FORMAT_ERROR;
                }
            }
            return SUCCESS;
        }
        case ATTACHMENT: {
        }
        case CLOB: {
            return SUCCESS;
        }
        case PERSION: {
            return SUCCESS;
        }
        case ORGANIZATION: {
            return SUCCESS;
        }
        case DICT: {
            if (val != null) {
                if (val.length() > valLength) {
                    return OVER_LENGTH;
                }
                if (CommUtil.STRING_REGEX.matcher(val).find()) {
                    return FORMAT_ERROR;
                }
            }
            return SUCCESS;
        }
        case EXTERNAL_ATTR: {
            if (val != null) {
                if (val.length() > valLength) {
                    return OVER_LENGTH;
                }
                if (CommUtil.STRING_REGEX.matcher(val).find()) {
                    return FORMAT_ERROR;
                }
            }
            return SUCCESS;
        }
        default: {
            return FAILURE;
        }
        }

    }

    public static List<Map<String, String>> getSheetDatas(Sheet sheet, Map<String, String> fieldMap) {
        List<Map<String, String>> ret = new ArrayList<Map<String, String>>();
        if (sheet == null) {
            return ret;
        }
        // 防止存在空列时的映射错误, 获得titleNums
        Map<Integer, String> sheetTitleMap = getSheetTitles(sheet);
        Set<Integer> titleNums = sheetTitleMap.keySet();

        int lastRowNum = sheet.getLastRowNum();
        for (int i = 1; i <= lastRowNum; i++) {
            Row row = sheet.getRow(i);
            if (row != null && !isBlankRow(row)) {
                Map<String, String> data = new LinkedHashMap<String, String>();
                // 计数器j用于从fieldMap中取映射关系
                int j = 0;
                for (Integer titleNum : titleNums) {
                    String key;
                    if (BinaryUtils.isEmpty(fieldMap)) {
                        // 没有fieldMap时返回默认的 title
                        key = sheetTitleMap.get(titleNum);
                    } else {
                        key = fieldMap.get("" + j);
                    }

                    ++j;
                    if (!BinaryUtils.isEmpty(key)) {
                        // 此key为ci属性名, value为从Excel中读取的值
                        data.put(key, getStringValue(row.getCell(titleNum)));
                    }
                }
                ret.add(data);
            }
        }
        return ret;
    }

    private static Map<Integer, String> getSheetTitles(Sheet sheet) {
        LinkedHashMap<Integer, String> ret = new LinkedHashMap<Integer, String>();
        Row titleRow = sheet.getRow(0);
        if (titleRow == null) {
            return ret;
        }

        short lastCellNum = titleRow.getLastCellNum();

        for (int i = 0; i < lastCellNum; i++) {
            Cell cell = titleRow.getCell(i);
            String title = getStringValue(cell);
            if (!BinaryUtils.isEmpty(title, true)) {
                ret.put(i, title);
            }
        }

        return ret;
    }

    /**
     * 判断 row 的数据是否为空
     *
     * @param row
     * @return
     */
    public static boolean isBlankRow(Row row) {
        if (row == null) {
            return true;
        }
        boolean result = true;
        for (int i = row.getFirstCellNum(); i < row.getLastCellNum(); i++) {
            Cell cell = row.getCell(i, MissingCellPolicy.RETURN_BLANK_AS_NULL);
            String value = "";
            if (cell != null) {
                CellType cellType = cell.getCellType();
                if (cellType == CellType.STRING) {
                    value = cell.getStringCellValue();
                } else if (cellType == CellType.NUMERIC) {
                    value = String.valueOf((int) cell.getNumericCellValue());
                } else if (cellType == CellType.BOOLEAN) {
                    value = String.valueOf(cell.getBooleanCellValue());
                } else if (cellType == CellType.FORMULA) {
                    value = String.valueOf(cell.getCellFormula());
                }
                if (!"".equals(value.trim())) {
                    result = false;
                    break;
                }
            }
        }

        return result;
    }

}
