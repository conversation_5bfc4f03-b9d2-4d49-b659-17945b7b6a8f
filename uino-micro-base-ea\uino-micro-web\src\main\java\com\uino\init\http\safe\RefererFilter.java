package com.uino.init.http.safe;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import jakarta.servlet.FilterConfig;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import java.util.HashSet;
import java.util.Set;

/**
 * 信任站点拦截器
 * 
 * <AUTHOR>
 *
 */
//@WebFilter(urlPatterns = "/*")
@Slf4j
//@Order(2)
public class RefererFilter extends WebSafeFilter {

    private String trustReferersStr;

    private volatile Set<String> trustReferers;

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        this.trustReferersStr = filterConfig.getInitParameter("trustReferersStr");
    }

    @Override
    protected boolean validity(HttpServletRequest httpRequest) {
        Set<String> trustUrls = getTrustReferers();
        String reqReferer = httpRequest.getHeader("Referer");
        if (StringUtils.isBlank(reqReferer)) {
            log.warn("接收到未携带Referer的请求");
            return false;
        }
        boolean res = false;
        for (String trustUrl : trustUrls) {
            res = reqReferer.startsWith(trustUrl);
            if (res) {
                break;
            }
        }
        if (!res) {
            log.warn("接收到不再信任列表【{}】内的请求【{}】", trustReferersStr, reqReferer);
        }
        return res;
    }

    @Override
    protected boolean support(HttpServletRequest httpRequest) {
        return StringUtils.isNotBlank(trustReferersStr);
    }

    /**
     * 获取信任地址
     * 
     * @return
     */
    private Set<String> getTrustReferers() {
        if (trustReferers == null) {
            synchronized (RefererFilter.class) {
                if (trustReferers == null) {
                    trustReferers = new HashSet<>();
                    String[] trustReferersStrs = trustReferersStr.split(";");
                    for (String trustReferer : trustReferersStrs) {
                        trustReferers.add(trustReferer);
                    }
                }
            }
        }
        return this.trustReferers;
    }
}
