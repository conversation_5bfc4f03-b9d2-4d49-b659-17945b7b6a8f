package com.uino.service.cmdb.microservice;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.zip.ZipEntry;

import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClassDir;
import com.uinnova.product.vmdb.comm.model.image.CCcImage;
import com.uino.bean.cmdb.base.CcImage;
import com.uino.bean.cmdb.business.ImageCount;
import com.uino.bean.cmdb.business.ImportDirMessage;
import com.uino.bean.cmdb.business.ImportResultMessage;
import com.uino.bean.cmdb.query.ESSearchImageBean;
import com.uino.bean.permission.query.SearchKeywordBean;

/**
 * <AUTHOR>
 */
public interface IImageSvc {

	/**
	 * 导入zip 图标
	 * 
	 * @param sourceType
	 *            资源类型：3=2D图标资源(默认)，4=3D图标资源，5=视频资源
	 * @param file
	 * @return
	 */
	ImportResultMessage importZipImage(Long domainId,Integer sourceType, MultipartFile file);

	/**
	 * 汇总查询目录下的图标
	 * 
	 * @return
	 */
	List<ImageCount> queryImageDirList(Long domainId, CCcCiClassDir cdt);

	/**
	 * 全文检索目录下的图标
	 * 
	 * @param bean
	 * @return
	 */
	Page<CcImage> queryImagePage(ESSearchImageBean bean);

	/**
     * 查看置顶的图片
     *
     * @param bean
     * @return
     */
    List<CcImage> queryTopImage(SearchKeywordBean bean);

    /**
     * 导入图标
     *
     * @param dirId
     * @param file
     * @return
     */
    boolean importImage(Long dirId, MultipartFile file);

    /**
     * 支持批量的导入图标
     *
     * @param dirId
     * @param files
     * @return
     */
    ImportDirMessage importImages(Long dirId, MultipartFile... files);

    /**
     * 替换图标
     *
     * @param imageId
     * @param file
     * @return
     */
    boolean replaceImage(Long imageId, MultipartFile file);

    /**
     * 删除图标
     *
     * @param image
     * @return
     */
    boolean deleteImage(CcImage image);

    /**
     * 根据条件删除图片
     *
     * @param delCdt
     */
    void deleteImage(CCcImage delCdt);

    /**
     * 删除目录下的图标
     *
     * @param dirId
     * @return
     */
    boolean deleteDirImage(Long dirId);

    /**
     * 导出指定目录图标
     *
     * @param dirIds
     * @return
     */
    ResponseEntity<byte[]> exportImageZipByDirIds(Set<Long> dirIds);

    /**
     * 导入3D图标
     *
     * @param file          导入文件
     * @param zipEntriesMap 3D图标对应的ziEntriesMap
     * @param isCover
     * @return
     */
    ImportDirMessage import3DZipImage(MultipartFile file, Long dirId, Map<ZipEntry, byte[]> zipEntriesMap, boolean isCover);

    /**
     * 映射3D图标
     *
     * @param image
     * @return
     */
    Long updateImageRlt(CcImage image);

    /**
     * 替换3D图标
     *
     * @param imageId
     * @param file
     * @return
     */
    boolean replace3DImage(Long imageId, MultipartFile file);

    /**
     * 删除3D图标
     *
     * @param image
     * @return
     */
    boolean delete3DImage(CcImage image);

    /**
     * 根据id查询图标
     *
     * @param id
     * @return
     */
    CcImage queryImageById(Long id);

    /**
     * 统计符合条件的图标数量
     *
     * @param bean
     * @return
     */
    Long countBySearchBean(ESSearchImageBean bean);

    /**
     * 下载图标资源
     *
     * @param ids
     */
    ResponseEntity<byte[]> downloadImageResource(List<Long> ids);

    /**
     * 根据资源路径查询资源
     *
     * @param bean
     *
     */
    Page<CcImage> queryImageByPath(ESSearchImageBean bean);

    /**
     * whether to display the document attribute
     *
     * @return boolean
     * */
    Boolean isShowDocumentAttribute();
}
