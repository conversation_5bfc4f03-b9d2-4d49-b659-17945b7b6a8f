package com.uino.monitor.event.service.impl;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.binary.core.exception.MessageException;
import com.binary.core.i18n.LanguageResolver;
import com.binary.core.util.BinaryUtils;
import com.binary.jdbc.Page;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uino.bean.cmdb.base.ESCIClassInfo;
import com.uino.bean.event.Event2KafkaDTO;
import com.uino.bean.event.EventCiKpiCount;
import com.uino.bean.event.modeling.EventModel;
import com.uino.bean.event.modeling.EventModelAttribute;
import com.uino.bean.event.modeling.parser.EventModelXMLParser;
import com.uino.bean.monitor.base.ESAlarm;
import com.uino.bean.monitor.base.ESMonEapEvent;
import com.uino.bean.monitor.base.ESMonSysSeverityInfo;
import com.uino.bean.monitor.buiness.CurrentEventQueryDto;
import com.uino.bean.monitor.buiness.EventQueryDto;
import com.uino.bean.sys.base.ESDictionaryAttrDef;
import com.uino.bean.sys.base.ESDictionaryClassInfo;
import com.uino.bean.sys.base.ESDictionaryItemInfo;
import com.uino.bean.sys.enums.DictionaryOptionEnum;
import com.uino.bean.sys.query.ESDictionaryItemSearchBean;
import com.uino.dao.BaseConst;
import com.uino.dao.cmdb.ESCIClassSvc;
import com.uino.dao.cmdb.ESCISvc;
import com.uino.dao.cmdb.ESRltClassSvc;
import com.uino.dao.event.ESAlarmSvc;
import com.uino.dao.event.ESEventSvc;
import com.uino.dao.event.EventHistoryDao;
import com.uino.dao.sys.ESDictionaryClassSvc;
import com.uino.dao.sys.ESDictionaryItemSvc;
import com.uino.dao.util.ESUtil;
import com.uino.monitor.event.service.IEventService;
import com.uino.util.cache.ICacheService;
import com.uino.util.message.queue.MessageQueueProducer;
import com.uino.util.message.queue.MessageTopicConst;
import com.uino.util.sys.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.Operator;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;

import org.elasticsearch.script.Script;
import org.elasticsearch.search.sort.SortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static org.elasticsearch.search.sort.SortOrder.ASC;
import static org.elasticsearch.search.sort.SortOrder.DESC;

@Slf4j
@Service(IEventService.SERVICE_NAME)
public class EventServiceImpl implements IEventService {

    private final static SimpleDateFormat SIMPLE_DATE_FORMAT = new SimpleDateFormat("yyyyMMddHHmmss");

    private final static SimpleDateFormat SIMPLE_DATE_FORMAT_Z = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.S'Z'");

    private static final Gson GSON = new GsonBuilder().create();

    //事件模型
    private volatile EventModel eventModel;

    //ep 是否存在 存在 告警发dix 不存在直接入库
    @Value("${uino.monitor.ep.exist:false}")
    private Boolean epExist;

    // 是否保存历史告警（默认false）
    @Value("${uino.monitor.event.saveEventHis.enable:false}")
    private Boolean enableSaveEventHis;

    @Value("${monitor.select.include.domainId:true}")
    private boolean includeDomainId;

    @Autowired
    private ESCISvc esciSvc;

    @Autowired
    private ESDictionaryItemSvc dictItemSvc;

    @Autowired
    private ESAlarmSvc esAlarmSvc;

    @Autowired
    private ESCIClassSvc esCIClassSvc;

    @Autowired
    private ESDictionaryClassSvc dictClsSvc;

    @Autowired
    private EventHistoryDao eventHistoryDao;

    @Autowired
    private ICacheService cacheService;

    @Autowired
    ESEventSvc esEventSvc;

    @Autowired
    private ESRltClassSvc rltClassSvc;

    private static final TypeReference<List<ESMonEapEvent>> LIST_EVENT_TYPE = new TypeReference<List<ESMonEapEvent>>() {
    };

    //查询关键字
    private static Set<String> keywordSet = new HashSet<>();

    static {
        keywordSet.add("firstOccurrence");
        keywordSet.add("lastOccurrence");
        keywordSet.add("stateChange");
        keywordSet.add("ackTime");
        keywordSet.add("closeTime");
        keywordSet.add("severity");
        keywordSet.add("status");
        keywordSet.add("tally");
        keywordSet.add("acknowledged");
        keywordSet.add("grade");
        keywordSet.add("sourceId");
        keywordSet.add("blackout");
        keywordSet.add("alarmSMS");
        keywordSet.add("alarmEmail");

        keywordSet.add("alarmTicket");
        keywordSet.add("alarmWorkflow");
        keywordSet.add("maPeriodId");
        keywordSet.add("filterType");
        keywordSet.add("ifNotify");
        keywordSet.add("duration");
        keywordSet.add("tag");
    }

    @Override
    public Page<ESMonEapEvent> searchEventPage(EventQueryDto queryDto) {
        return searchEventPage(queryDto, null, null);
    }

    @Override
    public Page<ESMonEapEvent> searchEventPage(EventQueryDto queryDto, QueryBuilder mustQuery, QueryBuilder shouldQuery) {
        if( includeDomainId && BinaryUtils.isEmpty(queryDto.getDomainId())){
            queryDto.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
        }
        int pageNum = queryDto.getPageNum();
        int pageSize = queryDto.getPageSize();
        List<SortBuilder<?>> sorts = new ArrayList<>();
        String sortName = queryDto.getSortName();
        if (!keywordSet.contains(queryDto.getSortName())) {
            queryDto.setSortName(sortName + ".keyword");
        }
        if (!BinaryUtils.isEmpty(queryDto.getSortName()) && !BinaryUtils.isEmpty(queryDto.getSortOrder())) {
            SortBuilder sortBuilder = SortBuilders.fieldSort(queryDto.getSortName())
                    .order(queryDto.getSortOrder().equals("ASC") ? ASC : DESC).unmappedType("integer");
            sorts.add(sortBuilder);
        } else {
            SortBuilder defSort = SortBuilders.fieldSort("lastOccurrence").order(DESC).unmappedType("integer");
            sorts.add(defSort);
        }
        BoolQueryBuilder queryBuilder = queryDtoToESQuery(queryDto);
        if (mustQuery != null) {
            queryBuilder.filter(mustQuery);
        }
        if (shouldQuery != null) {
            queryBuilder.should(shouldQuery);
        }

        Page<JSONObject> jsonObject = eventHistoryDao.getSortListByQuery(pageNum, pageSize, queryBuilder, sorts, queryDto.getStartTime(), queryDto.getEndTime());
        return eventJsonToJava(jsonObject);
    }


    private Page<ESMonEapEvent> eventJsonToJava(Page<JSONObject> eventPageJson) {
        Page<ESMonEapEvent> resultEvent = new Page<>();
        List<JSONObject> eventData = eventPageJson.getData();
        if (!CollectionUtils.isEmpty(eventData)) {
            List<ESMonEapEvent> jsonToJava = JSON.parseObject(JSON.toJSONString(eventData), LIST_EVENT_TYPE);
            resultEvent.setData(jsonToJava);
        } else {
            resultEvent.setData(new ArrayList<>(0));
        }
        resultEvent.setPageNum(eventPageJson.getPageNum());
        resultEvent.setPageSize(eventPageJson.getPageSize());
        resultEvent.setTotalPages(eventPageJson.getTotalPages());
        resultEvent.setTotalRows(eventPageJson.getTotalRows());
        return resultEvent;
    }

    @Override
    public void saveAlarm(ESAlarm saveBean) {
        saveAlarmBatch(Collections.singletonList(saveBean));
    }

    @Override
    public void saveAlarmBatch(List<ESAlarm> saveBeans) {

        //发送kafka数据
        List<Event2KafkaDTO> sendDtos = new ArrayList<>();
        //历史告警数据（mon_eap_event_all_YYYYMM）
        JSONArray eventHistory = new JSONArray();
        //当前告警数据（event）
        List<JSONObject> events = new ArrayList<>();

        saveBeans.stream().forEach(saveBean -> {
            if (saveBean.getDomainId() == null) {
                saveBean.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
            }
            Long domainId = saveBean.getDomainId();
            Long severityId = saveBean.getSeverityId();
            Assert.notNull(severityId, "告警级别不得为空");
            ESDictionaryItemInfo item = dictItemSvc.getById(severityId);
            ESMonSysSeverityInfo serverityInfo = transMonSeverityInfoToDictItem(item);
            Assert.notNull(serverityInfo, "告警级别不存在");
            saveBean.setSeverityLevel(serverityInfo.getSeverity());

            //启用ep 就发送数据到dix 走ep处理
            ESMonEapEvent monEapEvent = new ESMonEapEvent();
            initMonEapEvent(monEapEvent, saveBean);
            addDefaultValue(monEapEvent);
            CcCiInfo ci = null;

            //对象告警
            if (saveBean.getAlarmObjType() == 0) {
                //SourceCIName 为ci的业务主键 查询不到用ci id
                ci = esciSvc.getCiInfoById(saveBean.getAlarmObjId());//.getCi().getCiPrimaryKey();
                if (!BinaryUtils.isEmpty(ci)) {
                    monEapEvent.setSourceCiName(JSONArray.parseArray(ci.getCi().getCiPrimaryKey()).stream().map(e -> e.toString()).collect(Collectors.joining(",")));
                    //获取ciCode赋值
                    String ciCode = ci.getCi().getCiCode();
                    monEapEvent.setCustomCiName(ciCode);
                    monEapEvent.setCiPrimaryKey(ci.getCi().getCiPrimaryKey());
                } else {
                    monEapEvent.setSourceCiName(saveBean.getAlarmObjId().toString());
                }
                ESCIClassInfo classInfo = esCIClassSvc.getById(saveBean.getClassId());
                if (!BinaryUtils.isEmpty(classInfo)) {
                    monEapEvent.setCiCategoryName(classInfo.getClassName());
                }
            } else if (saveBean.getAlarmObjType() == 1) { //关系告警
                monEapEvent.setSourceCiName(saveBean.getAlarmObjId().toString());
                CcCiClassInfo ccCiClassInfo = rltClassSvc.queryClassById(saveBean.getAlarmObjId());
                if (!BinaryUtils.isEmpty(ccCiClassInfo)) {
                    monEapEvent.setCiCategoryName(ccCiClassInfo.getCiClass().getClassName());
                }
            }

            if (epExist) {
                //修改直接发送kafka
                Event2KafkaDTO sendDto = new Event2KafkaDTO();
                sendDto.setStatus(monEapEvent.getStatus());
                sendDto.setSourceAlertKey(monEapEvent.getSourceAlertKey());
                sendDto.setSourceSeverity(monEapEvent.getSourceSeverity());
                sendDto.setSourceID(monEapEvent.getSourceId());
                sendDto.setSourceCIName(monEapEvent.getSourceCiName());
                sendDto.setSourceIdentifier(monEapEvent.getSourceIdentifier());
                sendDto.setSummary(monEapEvent.getSummary());
                sendDto.setSeverity(monEapEvent.getSeverity());
                sendDto.setSourceEventID(monEapEvent.getSourceEventId());
                sendDto.setLastOccurrence(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(System.currentTimeMillis()));
                sendDto.setTimestamp(String.valueOf(new Date().getTime()));
                if (!BinaryUtils.isEmpty(ci)) {
                    Event2KafkaDTO.CIObjectDTO ciObject = new Event2KafkaDTO.CIObjectDTO();
                    ciObject.setClassId(ci.getCi().getClassId());
                    ciObject.setCiCode(ci.getCi().getCiCode());
                    ciObject.setHashCode(ci.getCi().getHashCode().longValue());
                    ciObject.setCiPrimaryKey(ci.getCi().getCiPrimaryKey());
                    ciObject.setClassName(ci.getCiClass().getClassName());
                    ciObject.setId(ci.getCi().getId());
                    Map<String, String> ciSourceAttr = ci.getAttrs();
                    ciObject.setObject(JSONObject.parseObject(JSONObject.toJSONString(ciSourceAttr)));
                    sendDto.setCIObject(ciObject);
                }
                sendDtos.add(sendDto);
            } else {
                if (BinaryUtils.isEmpty(monEapEvent.getId())) {
                    monEapEvent.setId(String.valueOf(ESUtil.getUUID()));
                    monEapEvent.setDuplicateSerial(monEapEvent.getId());
                    monEapEvent.setSerial(monEapEvent.getId());
                    monEapEvent.setSummary(saveBean.getDesc());
                    monEapEvent.setKpiName(saveBean.getKpiName());
                    monEapEvent.setCiName(monEapEvent.getCustomCiName());
                    monEapEvent.setSeverity(saveBean.getSeverityLevel());
                    monEapEvent.setCiCategoryId(saveBean.getClassId().toString());
                }

                JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(monEapEvent));
                //格式化时间为时间戳类型
                jsonObject.put("firstOccurrence", monEapEvent.getFirstOccurrence().getTime());
                jsonObject.put("lastOccurrence", monEapEvent.getLastOccurrence().getTime());
                //无EP时，模拟告警直接保存到历史表中
                eventHistory.add(jsonObject);
//                eventHistoryDao.saveOrUpdateNoRefresh(jsonObject, monEapEvent.getLastOccurrence().getTime());

                // 更新已存在告警（根据ciname与kpiName进行搜索匹配）
                String ciName = monEapEvent.getCustomCiName();
                String kpiName = monEapEvent.getKpiName();
                BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
                queryBuilder.filter(QueryBuilders.termQuery("domainId", domainId));
                queryBuilder.filter(QueryBuilders.termQuery("ciName.keyword", ciName));
                queryBuilder.filter(QueryBuilders.termQuery("kpiName.keyword", kpiName));

                List<JSONObject> oldEventList = esEventSvc.getListByQuery(1, 1, queryBuilder).getData();
                if (oldEventList.size() > 0) {
                    JSONObject oldEvent = oldEventList.get(0);
                    String id = oldEvent.getString("id");
                    monEapEvent.setId(id);
                }
                //更新ES当前告警表
                events.add(JSONObject.parseObject(JSONObject.toJSONString(monEapEvent)));
//                esEventSvc.saveOrUpdate(JSONObject.parseObject(JSONObject.toJSONString(monEapEvent)));
            }
        });

        //批量处理数据
        if (epExist) {
            //批量发送kafka数据
            try {
                MessageQueueProducer messageQueueProducer = SpringUtil.getBean(MessageQueueProducer.class);
                messageQueueProducer.sendMessage(MessageTopicConst.EVENT_QUEUE, GSON.toJson(sendDtos));
                log.debug("kafka send data:" + GSON.toJson(sendDtos));
            } catch (Exception e) {
                Assert.isTrue(false, "配置异常,数据发送失败");
            }
        } else {
            // 2021-08-30 增加是否保存历史告警开关
            if (enableSaveEventHis) {
                //保存历史报警（mon_eap_event_all_YYYYMM）
                eventHistoryDao.saveOrUpdateBatchNoRefresh(eventHistory);
            }
            //保存当前告警（event）
            esEventSvc.saveOrUpdateBatch(events);
        }
        //模拟告警 推送记录
        esAlarmSvc.saveOrUpdateBatch(saveBeans);
    }

    /**
     * 字典值转换为告警级别
     *
     * @param item
     * @return
     */
    public ESMonSysSeverityInfo transMonSeverityInfoToDictItem(ESDictionaryItemInfo item) {
        Map<String, String> attrs = item.getAttrs();
        ESMonSysSeverityInfo severityInfo = new ESMonSysSeverityInfo();
        severityInfo.setId(item.getId());
        severityInfo.setSeverity(Integer.valueOf(attrs.get("severity")));
        severityInfo.setColor(attrs.get("color"));
        String voiceUrl = attrs.get("voiceUrl");
        if (!BinaryUtils.isEmpty(voiceUrl)) {
            String voiceName = voiceUrl.substring(voiceUrl.lastIndexOf("/") + 1);
            severityInfo.setVoiceName(voiceName);
        }
        severityInfo.setVoiceUrl(voiceUrl);
        severityInfo.setChineseName(attrs.get("chineseName"));
        severityInfo.setOption(item.getOption());
        severityInfo.setCreator(item.getCreator());
        severityInfo.setModifier(item.getModifier());
        severityInfo.setCreateTime(item.getCreateTime());
        severityInfo.setModifyTime(item.getModifyTime());
        return severityInfo;
    }

    /**
     * 创建BoolQueryBuilder
     *
     * @param queryDto
     * @return
     */
    private BoolQueryBuilder queryDtoToESQuery(EventQueryDto queryDto) {
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        Long domainId = queryDto.getDomainId() == null ? BaseConst.DEFAULT_DOMAIN_ID : queryDto.getDomainId();
        boolQuery.must(QueryBuilders.termQuery("domainId", domainId));
        // Additional query conditions for data twin alarms
        this.dataTwinEventQuery(boolQuery, queryDto);

        List<Integer> sourceIdList = queryDto.getSourceId();
        if (null != sourceIdList && !sourceIdList.isEmpty()) {
            boolQuery.filter(QueryBuilders.termsQuery("sourceId", sourceIdList));
        }
        List<Integer> severityList = queryDto.getSeverity();
        if (null != severityList && !severityList.isEmpty()) {
            boolQuery.filter(QueryBuilders.termsQuery("severity", severityList));
        }
        List<Integer> statusList = queryDto.getStatus();
        if (null != statusList && !statusList.isEmpty()) {
            boolQuery.filter(QueryBuilders.termsQuery("status", statusList));
        }
        List<Integer> acknowledgedList = queryDto.getAckStatus();
        if (null != acknowledgedList && !acknowledgedList.isEmpty()) {
            boolQuery.filter(QueryBuilders.termsQuery("acknowledged", acknowledgedList));
        }
        List<Integer> ifNotifyList = queryDto.getNotifyStatus();
        if (null != ifNotifyList && !ifNotifyList.isEmpty()) {
            boolQuery.filter(QueryBuilders.termsQuery("ifNotify", ifNotifyList));
        }
        List<Integer> ticket = queryDto.getBillStatus();
        if (null != ticket && !ticket.isEmpty()) {
            boolQuery.filter(QueryBuilders.termsQuery("alarmTicket", ticket));
        }
        List<Integer> filterTypes = queryDto.getFilterType();
        if (null != filterTypes && !filterTypes.isEmpty()) {
            boolQuery.mustNot(QueryBuilders.termsQuery("filterType", filterTypes));
        }
        List<String> serials = queryDto.getSerials();
        if (null != serials && !serials.isEmpty()) {
            boolQuery.must(QueryBuilders.termsQuery("serial.keyword", serials));
        }
        List<String> sourceCiNames = queryDto.getSourceCiNames();
        if (null != sourceCiNames && !sourceCiNames.isEmpty()) {
            boolQuery.must(QueryBuilders.termsQuery("sourceCiName.keyword", sourceCiNames));
        }
        if (queryDto.getShowCompressed() == 1) {
            String script = "doc['id.keyword'].value == doc['duplicateSerial.keyword'].value";
            boolQuery.filter(QueryBuilders.scriptQuery(new Script(script)));
        }
        if (!CollectionUtils.isEmpty(queryDto.getCiIds())) {
            boolQuery.must(QueryBuilders.termsQuery("ciId.keyword", queryDto.getCiIds()));
        }

        List<Integer> tags = queryDto.getTag();
        if (null != tags && !tags.isEmpty()) {
            if (tags.contains(0)) {
                BoolQueryBuilder boolQuery1 = new BoolQueryBuilder();
                BoolQueryBuilder boolQuery2 = new BoolQueryBuilder();
                boolQuery1.should(boolQuery2.mustNot(QueryBuilders.existsQuery("tag")));
                // tags.remove(0);
                if (null != tags && !tags.isEmpty()) {
                    boolQuery1.should(QueryBuilders.termsQuery("tag", tags));
                }
                boolQuery.must(boolQuery1);
            } else {
                boolQuery.must(QueryBuilders.termsQuery("tag", tags));
            }
        }
        List<String> ciApplicationList = queryDto.getCiApplication();
        if (null != ciApplicationList && !ciApplicationList.isEmpty()) {
            // （空）
            String nullString = LanguageResolver.trans("EMV_LAST_CI_APPLICATION_NULL");
            if (ciApplicationList.contains(nullString)) {
                BoolQueryBuilder boolQuery1 = new BoolQueryBuilder();
                BoolQueryBuilder boolQuery2 = new BoolQueryBuilder();
                boolQuery1.should(boolQuery2.mustNot(QueryBuilders.existsQuery("ciApplication")));
                ciApplicationList.remove(nullString);
                // 增加上ciApplcation为""的情况
                ciApplicationList.add("");
                if (null != ciApplicationList && !ciApplicationList.isEmpty()) {
                    boolQuery1.should(QueryBuilders.termsQuery("ciApplication.keyword", ciApplicationList));
                }
                boolQuery.must(boolQuery1);
            } else {
                boolQuery.must(QueryBuilders.termsQuery("ciApplication.keyword", ciApplicationList));
            }
        }
        if (!BinaryUtils.isEmpty(queryDto.getStartTime())) {
            boolQuery.filter(QueryBuilders.rangeQuery("lastOccurrence").gte(queryDto.getStartTime()));
        }
        if (!BinaryUtils.isEmpty(queryDto.getEndTime())) {
            boolQuery.filter(QueryBuilders.rangeQuery("lastOccurrence").lte(queryDto.getEndTime()));
        }
        if (!BinaryUtils.isEmpty(queryDto.getSearch_key())) {
            String searchInfo = "*" + queryDto.getSearch_key() + "*";
            BoolQueryBuilder keywordQuery = QueryBuilders.boolQuery();

            String searchField = queryDto.getSearchField();
            if (null != searchField && !searchField.trim().isEmpty()) {
                searchField = searchField.trim();
                keywordQuery.should(QueryBuilders.wildcardQuery(searchField + ".keyword", searchInfo));
            } else {
                keywordQuery.should(QueryBuilders.wildcardQuery("sourceCiName.keyword", searchInfo));
                keywordQuery.should(QueryBuilders.wildcardQuery("sourceAlertKey.keyword", searchInfo));
                keywordQuery.should(QueryBuilders.wildcardQuery("sourceSummary.keyword", searchInfo));
                keywordQuery.should(QueryBuilders.wildcardQuery("kpiName.keyword", searchInfo));
                keywordQuery.should(QueryBuilders.wildcardQuery("ciName.keyword", searchInfo));
                keywordQuery.should(QueryBuilders.wildcardQuery("ciPrimaryKey.keyword", searchInfo));
                keywordQuery.should(QueryBuilders.wildcardQuery("summary.keyword", searchInfo));
                keywordQuery.should(QueryBuilders.wildcardQuery("ciApplication.keyword", searchInfo));
                keywordQuery.should(QueryBuilders.wildcardQuery("ciOwner.keyword", searchInfo));
            }
            boolQuery.must(keywordQuery);
        }

        if (!BinaryUtils.isEmpty(queryDto.getBlackouts())) {
            boolQuery.must(QueryBuilders.termsQuery("blackout", queryDto.getBlackouts()));
        }

        if (!BinaryUtils.isEmpty(queryDto.getSelectorList())) {
            // 告警筛选范围
            BoolQueryBuilder selectorListQuery = QueryBuilders.boolQuery();
            queryDto.getSelectorList().forEach(selector -> {
                BoolQueryBuilder selectorQuery = QueryBuilders.boolQuery();
                selector.getSelectors().forEach(selectorInfo -> {
                    if (!BinaryUtils.isEmpty(selectorInfo.getValue()) && !"*".equals(selectorInfo.getValue())) {
                        // 内层条件为且，使用must
                        String field = setFieldType(selectorInfo.getProperty());
                        if ("==".equals(selectorInfo.getOperator())) {
                            selectorQuery.must(QueryBuilders.termQuery(field, selectorInfo.getValue()));
                        } else if ("contains".equals(selectorInfo.getOperator())) {
                            selectorQuery.must(QueryBuilders.multiMatchQuery(selectorInfo.getValue(), field.replace(".keyword", ""))
                                    .operator(Operator.AND).type("phrase_prefix").lenient(true));
                        } else if ("!=".equals(selectorInfo.getOperator())) {
                            selectorQuery.mustNot(QueryBuilders.termQuery(field, selectorInfo.getValue()));
                        } else if ("between".equals(selectorInfo.getOperator())) {
                            String startTime = BinaryUtils.isEmpty(selectorInfo.getStartTime()) ? "00:00" : selectorInfo.getStartTime();
                            String endTime = BinaryUtils.isEmpty(selectorInfo.getEndTime()) ? "23:59" : selectorInfo.getEndTime();
                            selectorQuery.must(QueryBuilders.rangeQuery(field).gte(startTime).lte(endTime));
                        }
                    }
                });

                // 外层条件为或，使用should
                selectorListQuery.should(selectorQuery);
            });
            boolQuery.must(selectorListQuery);
        }
        return boolQuery;
    }

    /**
     * 告警字段根据类型拼装keyword
     * @param field
     * @return
     */
    private String setFieldType(String field) {
        if (BinaryUtils.isEmpty(field)) {
            return field;
        }
        EventModel eventModel = getEventModel();
        EventModelAttribute attr = eventModel.getAttribute(field);

        // 使用自定义筛选字段时，对fieldMap进行检索
        if (BinaryUtils.isEmpty(attr)) {
            return "fieldMap." + field.toUpperCase() + ".keyword";
        }
        // event_model进行字段匹配，确认是否为字符串类型
        if (attr.getDataType().startsWith("varchar")) {
            return field + ".keyword";
        }
        return field;
    }

    private void dataTwinEventQuery(BoolQueryBuilder boolQuery, EventQueryDto queryDto) {
        // Twin ID
        String digitalTwinId = queryDto.getDigitalTwinId();
        if (null != digitalTwinId) {
            if (epExist) {
                BoolQueryBuilder digitalIdQuery = QueryBuilders.boolQuery();
                digitalIdQuery.should(QueryBuilders.termQuery("ciname.keyword", digitalTwinId));
                digitalIdQuery.should(QueryBuilders.termQuery("ciName.keyword", digitalTwinId));
                digitalIdQuery.should(QueryBuilders.termQuery("ciId.keyword", digitalTwinId));
                boolQuery.must(digitalIdQuery);
            } else {
                boolQuery.must(QueryBuilders.termQuery("customCiName.keyword", digitalTwinId));
            }
        }
        // Whether to block
        List<Integer> isShield = queryDto.getShield();
        if (null != isShield && !isShield.isEmpty()) {
            boolQuery.filter(QueryBuilders.termsQuery("blackout", isShield));
        }
    }

    /**
     * 初始化ESMonEapEvent
     *
     * @param monEapEvent
     * @param saveDto
     */
    public void initMonEapEvent(ESMonEapEvent monEapEvent, ESAlarm saveDto) {
        monEapEvent.setDomainId(saveDto.getDomainId());
        /**
         * 模拟告警的来源设置 暂时不考虑关系告警
         */
        if (!BinaryUtils.isEmpty(saveDto.getSourceId())) {
            monEapEvent.setSourceId(saveDto.getSourceId());
        } else {
            Map<String, String> valueKeycode = getExteralDictValues(saveDto.getDomainId());
            if (!BinaryUtils.isEmpty(saveDto.getSource()) && valueKeycode.containsKey(saveDto.getSource())) {
                monEapEvent.setSourceId(Integer.valueOf(valueKeycode.get(saveDto.getSource())));
            } else {
                //系统派生
                monEapEvent.setSourceId(999);
            }
        }

        //打开的告警 status 1   关闭的 2
        monEapEvent.setStatus(saveDto.getStatus() == 0 ? 1 : 2);
        if (!BinaryUtils.isEmpty(saveDto.getFirstOccurrence())) {
            monEapEvent.setFirstOccurrence(saveDto.getFirstOccurrence());
        } else {
            monEapEvent.setFirstOccurrence(new Date(saveDto.getAlarmTime()));
        }
        if (!BinaryUtils.isEmpty(saveDto.getLastOccurrence())) {
            monEapEvent.setLastOccurrence(saveDto.getLastOccurrence());
        } else {
            monEapEvent.setLastOccurrence(new Date(saveDto.getAlarmTime()));
        }
        monEapEvent.setCustomize(saveDto.getCustomize());
        monEapEvent.setSourceEventId(saveDto.getAlarmCode().toString());
        monEapEvent.setSourceSeverity(saveDto.getSeverityLevel().toString());
        monEapEvent.setSourceIdentifier(saveDto.getAlarmMark());
        monEapEvent.setSourceSummary(saveDto.getDesc());
        monEapEvent.setSummary(saveDto.getDesc());
        monEapEvent.setSourceAlertKey(saveDto.getKpiName());
        monEapEvent.setSeverity(saveDto.getSeverityLevel());
        long time = System.currentTimeMillis();
        long longTime = Long.parseLong(SIMPLE_DATE_FORMAT.format(time));
        monEapEvent.setModify_time(longTime);
        monEapEvent.setCreate_time(longTime);
    }

    /**
     * 设置默认值
     *
     * @param monEapEvent
     */
    public void addDefaultValue(ESMonEapEvent monEapEvent) {
        if (BinaryUtils.isEmpty(monEapEvent.getAcknowledged())) {
            monEapEvent.setAcknowledged(0);
        }
        if (BinaryUtils.isEmpty(monEapEvent.getIfNotify())) {
            monEapEvent.setIfNotify(0);
        }
        if (BinaryUtils.isEmpty(monEapEvent.getAlarmTicket())) {
            monEapEvent.setAlarmTicket(0);
        }
    }

    /**
     * 获取 字典表 告警来源名称和keycode  map
     *
     * @return
     */
    public Map<String, String> getExteralDictValues(Long domainId) {
        ESDictionaryItemSearchBean bean = new ESDictionaryItemSearchBean();
        bean.setDomainId(domainId);
        bean.setDictCode("monitorSource");
        bean.setDictDefId(303L);
        // 校验参数
        Long dictClassId = bean.getDictClassId();
        String dictCode = bean.getDictCode();
        String dictName = bean.getDictName();
        Long dictDefId = bean.getDictDefId();
        String dictProName = bean.getDictProName();
        Assert.isTrue(dictClassId != null || dictCode != null || dictName != null, "X_PARAM_NOT_NULL${name:引用字典定义}");
        Assert.isTrue(dictDefId != null || dictProName != null, "X_PARAM_NOT_NULL${name:引用属性}");
        Map<String, String> res = new HashMap<>();
        // 获取字典项
        QueryBuilder queryBuilder = dictItemSvc.getDictQueryBuilderByBean(bean);
        List<ESDictionaryItemInfo> items = dictItemSvc.getListByQuery(queryBuilder);
        if (BinaryUtils.isEmpty(items)) {
            return res;
        }
        // 判断引用属性是否存在
        ESDictionaryClassInfo dictClassInfo = dictClsSvc.getById(items.get(0).getDictClassId());
        String proName = null;
        for (ESDictionaryAttrDef def : dictClassInfo.getDictAttrDefs()) {
            if (dictDefId != null && dictDefId.longValue() == def.getId().longValue()) {
                proName = def.getProName();
            }
            if (!BinaryUtils.isEmpty(dictProName) && dictProName.equals(def.getProName())) {
                proName = def.getProName();
            }
        }
        if (proName == null) {
            return res;
        }

        for (ESDictionaryItemInfo item : items) {
            Map<String, String> attrs = item.getAttrs();
            String val = attrs.get(proName);
            if (!BinaryUtils.isEmpty(val) && !res.containsKey(val)) {
                res.put(val, item.getKeyCode());
            }
        }
        return res;
    }

    @Override
    public EventModel getEventModel() {
        EventModel tempModel = this.eventModel;
        if (null == tempModel) {
            synchronized (this) {
                tempModel = this.eventModel;
                if (null == tempModel) {
                    this.eventModel = tempModel = loadModel();
                }
            }
        }
        return tempModel;
    }

    //加载event_model.xml文件，实例化EventModel对象。
    private EventModel loadModel() {
        EventModel model = new EventModel();
        try {
            BufferedInputStream in = null;
            InputStream io = null;
            try {
                io = this.getClass().getResourceAsStream("/model/event_model.xml");
                in = new BufferedInputStream(io, 8192);

                EventModelXMLParser parser = new EventModelXMLParser();
                model = parser.load(in);
                log.info("loadEventModel success.");
            } catch (Exception e) {
                throw new RuntimeException("An IO error occurred while loading the event model: " + e.getMessage(), e);
            } finally {
                try {
                    if (io != null) {
                        io.close();
                    }
                } catch (IOException e) {
                }

                try {
                    if (in != null) {
                        in.close();
                    }
                } catch (IOException e) {
                }
            }
        } catch (RuntimeException e) {
            e.printStackTrace();
        }
        return model;
    }

    @Override
    public void saveCurrentEvent(ESMonEapEvent fmEvent) {
        if (fmEvent.getDomainId() == null) {
            fmEvent.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
        }
        //保存告警信息
        //启用ep并且启用，使用redis保存，否则使用es保存
        if (epExist) {
            saveEventCache(fmEvent);
        } else {
            esEventSvc.saveOrUpdate(JSONObject.parseObject(JSONObject.toJSONString(fmEvent)));
        }
    }

    public void saveCurrentEventBatch(List<JSONObject> events) {

        //保存告警信息
        //启用ep并且启用，使用redis保存，否则使用es保存
        if (epExist) {
            saveEventCacheBatch(events);
        } else {
            esEventSvc.saveOrUpdateBatch(events);
        }
    }

    /**
     * 保存告警信息到缓存
     *
     * @param fmEvent
     */
    private void saveEventCache(ESMonEapEvent fmEvent) {
        // id为空设置默认值
        if (BinaryUtils.isEmpty(fmEvent.getId())) {
            fmEvent.setId(String.valueOf(ESUtil.getUUID()));
        }

        //先查询告警信息,存在则先删除筛选字段关系
        ESMonEapEvent old = cacheService.getCacheByType("eventId_" + fmEvent.getId(), ESMonEapEvent.class);
        if (!BinaryUtils.isEmpty(old)) {
            for (String searchKey : getEventModel().getRedisSearchKeys()) {
                cacheService.setRemoveValue(searchKey + "_" + old.get(searchKey.toUpperCase()), "eventId_" + fmEvent.getId());
            }
        }

        //保存告警信息到缓存
        cacheService.setCache("eventId_" + fmEvent.getId(), getFMEventByEventModel(fmEvent));

        //增加查询条件key
        for (String searchKey : getEventModel().getRedisSearchKeys()) {
            if (!BinaryUtils.isEmpty(fmEvent.get(searchKey.toUpperCase()))) {
                cacheService.setAddValue(searchKey + "_" + fmEvent.get(searchKey.toUpperCase()), "eventId_" + fmEvent.getId());
            }
        }
    }

    private void saveEventCacheBatch(List<JSONObject> events) {
        events.stream().forEach(event -> {
           ESMonEapEvent monEapEvent = JSONObject.toJavaObject(event, ESMonEapEvent.class);
           this.saveEventCache(monEapEvent);
        });
    }

    @Override
    public List<ESMonEapEvent> listCurrentEventByParam(CurrentEventQueryDto queryDto) {
        QueryBuilder queryBuilder = buildEqualsParam(queryDto);
        //查询告警信息
        //启用ep并且启用redis，查询redis，否则查询ES（索引event）
        if (epExist) {
            List<ESMonEapEvent> list = currentEventCache(queryDto);
            if (BinaryUtils.isEmpty(list)) {
                return list;
            }
            //按照告警发生时间lastOccurrence进行筛选（查询出rediss所有数据，然后代码进行过滤）
            if (!BinaryUtils.isEmpty(queryDto.getStartTime()) && queryDto.getStartTime() > 0) {
                list = list.stream().filter(e -> e.getLastOccurrence().getTime() >= queryDto.getStartTime()).collect(Collectors.toList());
            }
            if (!BinaryUtils.isEmpty(queryDto.getEndTime()) && queryDto.getEndTime() > 0) {
                list = list.stream().filter(e -> e.getLastOccurrence().getTime() <= queryDto.getEndTime()).collect(Collectors.toList());
            }
            return list;
        } else {
            List<ESMonEapEvent> esMonEapEvents = new ArrayList<>();
            List<JSONObject> currentEvent = esEventSvc.getListByQuery(queryBuilder);
//            if (!BinaryUtils.isEmpty(currentEvent)) {
//                currentEvent.stream().forEach(e -> esMonEapEvents.add(e));
//            }
            return JSON.parseObject(JSON.toJSONString(currentEvent), LIST_EVENT_TYPE);
        }
    }

    /**
     * 构建当前告警查询参数
     *
     * @param queryDto
     */
    private QueryBuilder buildEqualsParam(CurrentEventQueryDto queryDto) {
        if (includeDomainId && queryDto.getDomainId() == null) {
            queryDto.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
        }
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("domainId", queryDto.getDomainId()));
        Map<String, String> equalsParams = null;
        if (!BinaryUtils.isEmpty(queryDto.getEqualsParams())) {
            equalsParams = queryDto.getEqualsParams();
        } else {
            equalsParams = new HashMap<>();
        }

        if (!BinaryUtils.isEmpty(queryDto.getIdentifier())) {
            equalsParams.put("IDENTIFIER", queryDto.getIdentifier());
            query.must(QueryBuilders.termQuery("identifier.keyword", queryDto.getIdentifier()));
        }
        if (!BinaryUtils.isEmpty(queryDto.getSerial())) {
            equalsParams.put("SERIAL", queryDto.getSerial());
            query.must(QueryBuilders.termQuery("serial.keyword", queryDto.getSerial()));
        }
        if (!BinaryUtils.isEmpty(queryDto.getCiName())) {
            equalsParams.put("CINAME", queryDto.getCiName());
            BoolQueryBuilder ciCodeQuery = QueryBuilders.boolQuery();
            ciCodeQuery.should(QueryBuilders.termQuery("ciCode.keyword", queryDto.getCiName()));
            ciCodeQuery.should(QueryBuilders.termQuery("ci_name.keyword", queryDto.getCiName()));
            ciCodeQuery.should(QueryBuilders.termQuery("ciname.keyword", queryDto.getCiName()));
            ciCodeQuery.should(QueryBuilders.termQuery("ciName.keyword", queryDto.getCiName()));
            query.must(ciCodeQuery);
//            query.must(QueryBuilders.termQuery("ciName.keyword", queryDto.getCiName()));
        }
        if (!BinaryUtils.isEmpty(queryDto.getSeverity())) {
            equalsParams.put("SEVERITY", String.valueOf(queryDto.getSeverity()));
            query.must(QueryBuilders.termQuery("severity", queryDto.getSeverity()));
        }
        if (!BinaryUtils.isEmpty(queryDto.getSummary())) {
            equalsParams.put("SUMMARY", queryDto.getSummary());
            query.must(QueryBuilders.termQuery("summary.keyword", queryDto.getSummary()));
        }
        if (!BinaryUtils.isEmpty(queryDto.getEventTitle())) {
            equalsParams.put("EVENTTITLE", queryDto.getEventTitle());
            query.must(QueryBuilders.termQuery("eventTitle.keyword", queryDto.getEventTitle()));
        }
        if (!BinaryUtils.isEmpty(queryDto.getStatus())) {
            equalsParams.put("STATUS", String.valueOf(queryDto.getStatus()));
            query.must(QueryBuilders.termQuery("status", queryDto.getStatus()));
        }
        if (!BinaryUtils.isEmpty(queryDto.getGrade())) {
            equalsParams.put("GRADE", String.valueOf(queryDto.getGrade()));
            query.must(QueryBuilders.termQuery("grade", queryDto.getGrade()));
        }
        if (!BinaryUtils.isEmpty(queryDto.getSourceId())) {
            equalsParams.put("SOURCEID", String.valueOf(queryDto.getSourceId()));
            BoolQueryBuilder sourceId = QueryBuilders.boolQuery();
            sourceId.should(QueryBuilders.termQuery("sourceid.keyword", queryDto.getCiName()));
            sourceId.should(QueryBuilders.termQuery("sourceId.keyword", queryDto.getCiName()));
            query.must(sourceId);
//            query.must(QueryBuilders.termQuery("sourceId", queryDto.getSourceId()));
        }
        if (!BinaryUtils.isEmpty(queryDto.getSourceEventId())) {
            equalsParams.put("SOURCEEVENTID", queryDto.getSourceEventId());
            BoolQueryBuilder sourceEventId = QueryBuilders.boolQuery();
            sourceEventId.should(QueryBuilders.termQuery("sourceEventId.keyword", queryDto.getCiName()));
            sourceEventId.should(QueryBuilders.termQuery("sourceeventid.keyword", queryDto.getCiName()));
            query.must(sourceEventId);
//            query.must(QueryBuilders.termQuery("sourceEventId.keyword", queryDto.getSourceEventId()));
        }
        if (!BinaryUtils.isEmpty(queryDto.getSourceCiName())) {
            equalsParams.put("SOURCECINAME", queryDto.getSourceCiName());
            BoolQueryBuilder sourceCiName = QueryBuilders.boolQuery();
            sourceCiName.should(QueryBuilders.termQuery("sourceCiName.keyword", queryDto.getCiName()));
            sourceCiName.should(QueryBuilders.termQuery("sourceciname.keyword", queryDto.getCiName()));
            query.must(sourceCiName);
//            query.must(QueryBuilders.termQuery("sourceCiName.keyword", queryDto.getSourceCiName()));
        }
        if (!BinaryUtils.isEmpty(queryDto.getKpiName())) {
            equalsParams.put("KPINAME", queryDto.getKpiName());
            BoolQueryBuilder kpiName = QueryBuilders.boolQuery();
            kpiName.should(QueryBuilders.termQuery("kpiName.keyword", queryDto.getCiName()));
            kpiName.should(QueryBuilders.termQuery("kpiname.keyword", queryDto.getCiName()));
            query.must(kpiName);
//            query.must(QueryBuilders.termQuery("kpiName.keyword", queryDto.getKpiName()));
        }
        if (!BinaryUtils.isEmpty(queryDto.getKpiCategoryID())) {
            equalsParams.put("KPICATEGORYID", queryDto.getKpiCategoryID());
            query.must(QueryBuilders.termQuery("kpiCategoryID.keyword", queryDto.getKpiCategoryID()));
        }
        if (!BinaryUtils.isEmpty(queryDto.getCiId())) {
            equalsParams.put("CIID", queryDto.getCiId());
            query.must(QueryBuilders.termQuery("ciId.keyword", queryDto.getCiId()));
        }
        if (!BinaryUtils.isEmpty(queryDto.getBlackout())) {
            equalsParams.put("BLACKOUT", String.valueOf(queryDto.getBlackout()));
            query.must(QueryBuilders.termQuery("blackout", queryDto.getBlackout()));
        }
        if (!BinaryUtils.isEmpty(queryDto.getFilterType())) {
            equalsParams.put("FILTERTYPE", String.valueOf(queryDto.getFilterType()));
            query.must(QueryBuilders.termQuery("filterType", queryDto.getFilterType()));
        }
        if (!BinaryUtils.isEmpty(queryDto.getCiApplication())) {
            query.must(QueryBuilders.termsQuery("ciApplication.keyword", queryDto.getCiApplication()));
        }
        if (!BinaryUtils.isEmpty(queryDto.getStartTime()) && queryDto.getStartTime() > 0) {
            query.filter(QueryBuilders.rangeQuery("lastOccurrence").gte(queryDto.getStartTime()));
        }
        if (!BinaryUtils.isEmpty(queryDto.getEndTime()) && queryDto.getEndTime() > 0) {
            query.filter(QueryBuilders.rangeQuery("lastOccurrence").lte(queryDto.getEndTime()));
        }
        queryDto.setEqualsParams(equalsParams);
        return query;
    }

    /**
     * 缓存查询告警信息
     */
    private List<ESMonEapEvent> currentEventCache(CurrentEventQueryDto queryDto) {

        //如果查询条件为空，查询所有数据
        if (BinaryUtils.isEmpty(queryDto) ||
                (BinaryUtils.isEmpty(queryDto.getEqualsParams()) &&
                        BinaryUtils.isEmpty(queryDto.getLikeParams()) &&
                        BinaryUtils.isEmpty(queryDto.getNotEqualsParams()))) {
            return cacheService.listByPattern("eventId_*", ESMonEapEvent.class);
        }
        //查询符合条件keys
        Set<String> keys = getKeysByQuery(queryDto);
        if (BinaryUtils.isEmpty(keys)) {
            return null;
        }
        return cacheService.listByKeys(keys.toArray(new String[keys.size()]), ESMonEapEvent.class);
    }

    @Override
    public List<ESMonEapEvent> listCurrentEventByParam(List<CurrentEventQueryDto> queryDtos) {
        //EP查询当前告警信息
        return currentEventCache(queryDtos);
    }

    /**
     * 缓存查询告警信息,查缓存不考虑domainId
     */
    private List<ESMonEapEvent> currentEventCache(List<CurrentEventQueryDto> queryDtos) {
        //如果查询条件为空，查询所有数据
        if (BinaryUtils.isEmpty(queryDtos)) {
            return this.currentEventCache(new CurrentEventQueryDto());
        }
        //查询符合条件keys,取交集操作
        Set<String> keys = new HashSet<>();
        queryDtos.stream().forEach(queryDto -> {
            keys.addAll(getKeysByQuery(queryDto));
        });
        if (BinaryUtils.isEmpty(keys)) {
            return null;
        }
        List<ESMonEapEvent> esMonEapEvents = cacheService.listByKeys(keys.toArray(new String[keys.size()]), ESMonEapEvent.class);
        if (BinaryUtils.isEmpty(esMonEapEvents)) {
            return null;
        }

        return esMonEapEvents;
    }

    /**
     * 根据查询参数获取redis缓存keys
     *
     * @param queryDto
     * @return
     */
    private Set<String> getKeysByQuery(CurrentEventQueryDto queryDto) {
        //外层循环xml配置的查询参数key，再根据查询参数查询符合条件的告警id，最后根据id查询告警信息
        Set<String> keys = new HashSet<>();
        //保证keys集合第一次匹配到数据，后面并集为空则认为无满足条件数据
        boolean firstAdd = true;
        for (String key : getEventModel().getRedisSearchKeys()) {
            Set<String> tempKeys = new HashSet<>();
            //查询条件相等
            if (!BinaryUtils.isEmpty(queryDto.getEqualsParams())) {
                String value = queryDto.getEqualsParams().get(key.toUpperCase());
                if (!BinaryUtils.isEmpty(value)) {
                    tempKeys.addAll(cacheService.setByKey(key + "_" + value));
                    if (BinaryUtils.isEmpty(tempKeys)) {
                        return tempKeys;
                    }
                }
            }

            //查询条件包含
            if (!BinaryUtils.isEmpty(queryDto.getLikeParams()) && BinaryUtils.isEmpty(tempKeys)) {
                String value = queryDto.getLikeParams().get(key.toUpperCase());
                if (!BinaryUtils.isEmpty(value)) {
                    Set<String> patternKeys = cacheService.getKeysByPattern(key + "_*" + value + "*");
                    if (!BinaryUtils.isEmpty(patternKeys)) {
                        patternKeys.forEach(patternKey -> {
                            tempKeys.addAll(cacheService.setByKey(patternKey));
                        });
                    }
                    if (BinaryUtils.isEmpty(tempKeys)) {
                        return tempKeys;
                    }
                }
            }

            //查询条件不相等：先查询所有告警id，然后删除符合条件id，剩下的即为不相等
            if (!BinaryUtils.isEmpty(queryDto.getNotEqualsParams()) && BinaryUtils.isEmpty(tempKeys)) {
                String value = queryDto.getNotEqualsParams().get(key.toUpperCase());
                if (!BinaryUtils.isEmpty(value)) {
                    Set<String> eventIds = cacheService.getKeysByPattern("eventId_*");
                    eventIds.removeAll(cacheService.setByKey(key + "_" + value));
                    tempKeys.addAll(eventIds);
                    if (BinaryUtils.isEmpty(tempKeys)) {
                        return tempKeys;
                    }
                }
            }

            //如果结果为空，进行赋值，否则取交集操作
            if (!BinaryUtils.isEmpty(tempKeys)) {
                if (BinaryUtils.isEmpty(keys) && firstAdd) {
                    keys.addAll(tempKeys);
                    firstAdd = false;
                } else {
                    keys.retainAll(tempKeys);
                }
            }
        }
        return keys;
    }

    @Override
    public void delCurrentEvent(ESMonEapEvent fmEvent) {
        //删除告警信息
        //启用ep并且启用redis，删除redis
        if (epExist) {
            //删除索引中事件id
            for (String key : getEventModel().getRedisSearchKeys()) {
                cacheService.setRemoveValue(key + "_" + fmEvent.get(key.toUpperCase()), "eventId_" + fmEvent.getId());
            }
            //删除告警事件信息
            cacheService.delKey("eventId_" + fmEvent.getId());
        } else {
            BoolQueryBuilder query = new BoolQueryBuilder();
            query.must(QueryBuilders.termQuery("id", fmEvent.getId()));
            esEventSvc.deleteByQuery(query, true);
        }
    }

    @Override
    public List<ESMonEapEvent> listCurrentEventByIds(Set<String> ids) {
        //启用ep并且启用redis，查询redis，否则查询ES
        if (epExist) {
            Set<String> newIds = new HashSet<>();
            if (BinaryUtils.isEmpty(ids)) {
                return null;
            }
            ids.stream().forEach(id -> {
                newIds.add("eventId_" + id);
            });
            return cacheService.listByKeys(newIds.toArray(new String[newIds.size()]), ESMonEapEvent.class);
        } else {
            List<ESMonEapEvent> esMonEapEvents = new ArrayList<>();
            BoolQueryBuilder query = new BoolQueryBuilder();
            query.must(QueryBuilders.termsQuery("id.keyword", ids));
            List<JSONObject> esMonEapEvent = esEventSvc.getListByQuery(query);
//            if (!BinaryUtils.isEmpty(esMonEapEvent)) {
//                esMonEapEvent.stream().forEach(e -> esMonEapEvents.add(covert2java(e.toJSONString())));
//            }
//            return esMonEapEvents;

            return JSON.parseObject(JSON.toJSONString(esMonEapEvent), LIST_EVENT_TYPE);
//            JSONObject.parseObject(JSONObject.toJSONString(esEventSvc.getListByQuery(1, 3000, query).getData()), List.class);
        }
    }

    public List<ESMonEapEvent> queryEventList(Set<String> ids) {
        BoolQueryBuilder query = new BoolQueryBuilder();
        query.must(QueryBuilders.termsQuery("id.keyword", ids));
        List<JSONObject> esMonEapEvent = esEventSvc.getListByQuery(query);
        return JSON.parseObject(JSON.toJSONString(esMonEapEvent), LIST_EVENT_TYPE);
    }


    @Override
    public ESMonEapEvent currentEventById(String id) {
        if (epExist) {
            return cacheService.getCacheByType("eventId_" + id, ESMonEapEvent.class);
        } else {
            BoolQueryBuilder query = new BoolQueryBuilder();
            query.must(QueryBuilders.termQuery("id.keyword", id));
            List<JSONObject> esMonEapEvent = esEventSvc.getListByQuery(query);
            if (BinaryUtils.isEmpty(esMonEapEvent)) {
                return null;
            }

//            return covert2java(esMonEapEvent.get(0).toJSONString());
            return JSONObject.parseObject(JSONObject.toJSONString(esMonEapEvent.get(0)), ESMonEapEvent.class);
        }
    }

    @Override
    public void clearCurrentEvent(Long domainId) {
        //清除redis当前告警信息
        if (epExist) {
            Set<String> keys = new HashSet<>();
            //遍历已配置的查询参数key
            for (String key : getEventModel().getRedisSearchKeys()) {
                keys.addAll(cacheService.getKeysByPattern(key + "_*"));
            }

            //删除所有eventId
            keys.addAll(cacheService.getKeysByPattern("eventId_*"));
            if (!BinaryUtils.isEmpty(keys)) {
                cacheService.delKeys(keys.toArray(new String[keys.size()]));
            }
        } else {

            esEventSvc.deleteByQuery(QueryBuilders.termQuery("domainId", domainId), true);
        }
    }

    /**
     * 告警对象转换为JSON
     *
     * @param event
     * @return JSONObject
     */
    private ESMonEapEvent getFMEventByEventModel(ESMonEapEvent event) {
        EventModel eventModel = getEventModel();
        Map<String, String> attrMap = eventModel.getAttrKeys();
        JSONObject monEapEvent = event.getFieldMap();
        if (BinaryUtils.isEmpty(monEapEvent)) {
            return event;
        }
        JSONObject jsonEvent = new JSONObject(attrMap.size());
        Map<String, Object> extraAttrMap =
                event.getAttribute() == null ? new HashMap<>() : event.getAttribute();
        for (String key : monEapEvent.keySet()) {
            if (attrMap.containsKey(key)) {
                EventModelAttribute attr = eventModel.getAttribute(key);
                if ((attr.getDataType().equalsIgnoreCase("timestamp")
                        || attr.getDataType().equalsIgnoreCase("datetime")) && null != monEapEvent.getLong(key)) {
                    Long value = monEapEvent.getLong(key);
                    if (null != value) {
                        jsonEvent.put(attrMap.get(key), value);
                    }
                    continue;
                }
                if (attr.getDataType().equalsIgnoreCase("integer")) {
                    jsonEvent.put(attrMap.get(key), monEapEvent.getInteger(key));
                    continue;
                }
                if (attr.getDataType().equalsIgnoreCase("map")) {
                    extraAttrMap.put(attrMap.get(key), monEapEvent.getLong(key));
                    jsonEvent.put("attribute", extraAttrMap);
                    continue;
                }
                jsonEvent.put(attrMap.get(key), monEapEvent.get(key));
            }
        }
        //合并ESMonEapEvent与FMEvent后保存原jsonObject
        ESMonEapEvent event_ = jsonEvent.toJavaObject(ESMonEapEvent.class);
        event_.setOtherFieldMap(event.toJSONObject());
        return event_;
    }

    @Override
    public ESMonEapEvent saveEventHis(ESMonEapEvent esMonEapEvent) {
        if (esMonEapEvent.getDomainId() == null) {
            esMonEapEvent.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
        }
        //更新当前告警信息
        ESMonEapEvent currentEvent = currentEventById(esMonEapEvent.getId());
        if (!BinaryUtils.isEmpty(currentEvent)) {
            saveCurrentEvent(esMonEapEvent);
        }

        JSONObject jsonEvent = JSONObject.parseObject(JSON.toJSONString(esMonEapEvent));
        eventHistoryDao.saveOrUpdate(jsonEvent);
        return esMonEapEvent;
    }

    @Override
    public ESMonEapEvent hisEventById(String id) {
        if (StringUtils.isEmpty(id)) {
            return null;
        }
        JSONObject event = eventHistoryDao.getByID(id);
        return event.toJavaObject(ESMonEapEvent.class);
    }

    @Override
    public List<ESMonEapEvent> listCurrentEventByCiCodes(Long domainId, List<String> ciCodes) {
        if(includeDomainId && domainId == null){
            domainId = BaseConst.DEFAULT_DOMAIN_ID;
        }
        if (BinaryUtils.isEmpty(ciCodes)) {
            return null;
        }
        if (epExist) {
            Set<String> keys = new HashSet<>();
            ciCodes.stream().forEach(ciCode -> {
                keys.addAll(cacheService.setByKey("ciName_" + ciCode));
            });
            return cacheService.listByKeys(keys.toArray(new String[keys.size()]), ESMonEapEvent.class);
        } else {
            BoolQueryBuilder query = QueryBuilders.boolQuery();
            query.must(QueryBuilders.termQuery("domainId", domainId));
            query.should(QueryBuilders.termsQuery("ci_name.keyword", ciCodes));
            query.should(QueryBuilders.termsQuery("ciname.keyword", ciCodes));
            query.should(QueryBuilders.termsQuery("ciName.keyword", ciCodes));
            List<JSONObject> currentEvent = esEventSvc.getListByQuery(query);
            if (BinaryUtils.isEmpty(currentEvent)) {
                return null;
            }
//            List<ESMonEapEvent> currentEvents = new ArrayList<>();
//            currentEvent.stream().forEach(e -> currentEvents.add(covert2java(e.toJSONString())));
//            return currentEvents;
            return JSON.parseObject(JSON.toJSONString(currentEvent), LIST_EVENT_TYPE);
        }

    }

    @Override
    public List<ESMonEapEvent> listCurrentEventByCiIds(List<Long> ciIds) {
        if (BinaryUtils.isEmpty(ciIds)) {
            return null;
        }
        if (epExist) {
            Set<String> keys = new HashSet<>();
            ciIds.stream().forEach(ciId -> {
                keys.addAll(cacheService.setByKey("ciId_" + ciId));
            });
            if (BinaryUtils.isEmpty(keys)) {
                return null;
            }
            return cacheService.listByKeys(keys.toArray(new String[keys.size()]), ESMonEapEvent.class);
        } else {
            BoolQueryBuilder query = QueryBuilders.boolQuery();
            query.must(QueryBuilders.termsQuery("ciId.keyword", ciIds));
            List<JSONObject> currentEvent = esEventSvc.getListByQuery(query);
            if (BinaryUtils.isEmpty(currentEvent)) {
                return null;
            }
//            List<ESMonEapEvent> currentEvents = new ArrayList<>();
//            currentEvent.stream().forEach(e -> currentEvents.add(covert2java(e.toJSONString())));
//            return currentEvents;
            return JSON.parseObject(JSON.toJSONString(currentEvent), LIST_EVENT_TYPE);
        }
    }

    @Override
    public List<ESMonSysSeverityInfo> getAllEventSeverity(Long domainId) {
        domainId = domainId == null ? BaseConst.DEFAULT_DOMAIN_ID : domainId;
        List<ESMonSysSeverityInfo> result = new ArrayList<>();
        ESDictionaryItemSearchBean bean = ESDictionaryItemSearchBean.builder().dictName(BaseConst._MONSYS_SEVERITY_NAME).domainId(domainId).sortField("attrs.severity").isAsc(true).domainId(domainId).build();
        QueryBuilder query = dictItemSvc.getDictQueryBuilderByBean(bean);
        List<ESDictionaryItemInfo> itemInfos = dictItemSvc.getListByQuery(query);
        // 默认为只读
        itemInfos.forEach(item -> {
            if (BinaryUtils.isEmpty(item.getOption())) {
                item.setOption(DictionaryOptionEnum.READ);
            }
            result.add(transMonSeverityInfoToDictItem(item));
        });
        return result;
    }

    @Override
    public List<EventCiKpiCount> getEventCountByCiCodeAndKpiCodes(Long domainId, Set<String> ciCodes, Set<String> kpiCodes) {
        if(includeDomainId && domainId == null){
            domainId = BaseConst.DEFAULT_DOMAIN_ID;
        }
        if (BinaryUtils.isEmpty(ciCodes) || BinaryUtils.isEmpty(kpiCodes)) {
            throw new MessageException("ciCodes或者kpiCodes不能为空");
        }
        List<ESMonEapEvent> esMonEapEvents;
        List<EventCiKpiCount> result = new ArrayList<>();
        if (epExist) {
            Set<String> keys = new HashSet<>();
            ciCodes.stream().forEach(ciCode -> {
                keys.addAll(cacheService.setByKey("ciName_" + ciCode));
            });
            if (BinaryUtils.isEmpty(keys)) {
                return result;
            }

            Set<String> kpiNameKeys = new HashSet<>();
            kpiCodes.stream().forEach(kpiCode -> {
                kpiNameKeys.addAll(cacheService.setByKey("kpiName_" + kpiCode));
            });
            if (BinaryUtils.isEmpty(kpiNameKeys)) {
                return result;
            }

            //同时满足条件数据，取交集操作
            keys.retainAll(kpiNameKeys);
            if (BinaryUtils.isEmpty(keys)) {
                return result;
            }

            esMonEapEvents = cacheService.listByKeys(keys.toArray(new String[keys.size()]), ESMonEapEvent.class);
        } else {
            BoolQueryBuilder query = QueryBuilders.boolQuery();
            query.must(QueryBuilders.termQuery("domainId", domainId));
            if (!BinaryUtils.isEmpty(ciCodes)) {
                BoolQueryBuilder ciNameQuery = QueryBuilders.boolQuery();
                ciNameQuery.should(QueryBuilders.termsQuery("ci_name.keyword", ciCodes));
                ciNameQuery.should(QueryBuilders.termsQuery("ciname.keyword", ciCodes));
                ciNameQuery.should(QueryBuilders.termsQuery("ciName.keyword", ciCodes));
                query.must(ciNameQuery);
            }

            if (!BinaryUtils.isEmpty(kpiCodes)) {
                BoolQueryBuilder kpiNameQuery = QueryBuilders.boolQuery();
                kpiNameQuery.should(QueryBuilders.termsQuery("kpiName.keyword", kpiCodes));
                kpiNameQuery.should(QueryBuilders.termsQuery("kpiname.keyword", kpiCodes));
                query.must(kpiNameQuery);
            }
            List<JSONObject> currentEvent = esEventSvc.getListByQuery(query);
            esMonEapEvents = JSON.parseObject(JSON.toJSONString(currentEvent), LIST_EVENT_TYPE);
        }

        if (!BinaryUtils.isEmpty(esMonEapEvents)) {
            //查询所有告警等级
            List<ESMonSysSeverityInfo> allSeverity = getAllEventSeverity(domainId);
            Map<Integer, String> severityColor = new HashMap<>();
            allSeverity.stream().forEach(severity -> {
                severityColor.put(severity.getSeverity(), severity.getColor());
            });

            //将告警信息按照ciName、kpiName、severity进行分组计算
            Map<String, Map<String, Map<Integer, List<ESMonEapEvent>>>> eventMap = esMonEapEvents.stream().collect(Collectors.groupingBy(ESMonEapEvent::getCiName, Collectors.groupingBy(ESMonEapEvent::getKpiName, Collectors.groupingBy(ESMonEapEvent::getSeverity))));
            eventMap.forEach((ciName, ciNameEvent) -> {
                ciNameEvent.forEach((kpiName, kpiNameEvent) -> {
                    AtomicReference<Integer> seriousSeverity = new AtomicReference<>(null);
                    AtomicReference<Integer> count = new AtomicReference<>(0);
                    AtomicReference<Integer> tally = new AtomicReference<>(0);
                    kpiNameEvent.forEach((severity, event) -> {
                        if (null == seriousSeverity.get()) {
                            seriousSeverity.set(severity);
                        }
                        if (severity < seriousSeverity.get()) {
                            seriousSeverity.set(severity);
                        }
                        count.getAndSet(event.size() + count.get());
                        tally.getAndSet(event.stream().mapToInt(e -> e.getTally()).sum() + tally.get());
                    });
                    EventCiKpiCount vcEventCiKpiCount = new EventCiKpiCount();
                    vcEventCiKpiCount.setCiCode(ciName);
                    vcEventCiKpiCount.setKpiCode(kpiName);
                    vcEventCiKpiCount.setCount(count.get());
                    vcEventCiKpiCount.setSeverity(seriousSeverity.get());
                    vcEventCiKpiCount.setColor(severityColor.get(seriousSeverity.get()));
                    vcEventCiKpiCount.setTally(tally.get());
                    result.add(vcEventCiKpiCount);
                });
            });
        }

        return result;
    }

    /**
     * jsonString转ESMonEapEvent。dix直接存储到es（event）中，查询时进行转换赋值操作
     *
     * @param JSONString
     * @return
     */
    private ESMonEapEvent covert2java(String JSONString) {
        ESMonEapEvent esMonEapEvent = new ESMonEapEvent();

        JSONObject jsonObject = JSONObject.parseObject(JSONString);
        esMonEapEvent.setId((String) jsonObject.get("id"));
        esMonEapEvent.setIdentifier((String) jsonObject.get("identifier"));
        esMonEapEvent.setSerial((String) jsonObject.get("serial"));
        esMonEapEvent.setSeverity((Integer) jsonObject.get("severity"));
        esMonEapEvent.setSummary((String) jsonObject.get("summary"));
        esMonEapEvent.setTally((Integer) jsonObject.get("tally"));
        esMonEapEvent.setStatus((Integer) jsonObject.get("status"));
        esMonEapEvent.setSourceId((Integer) jsonObject.get("sourceid"));
        esMonEapEvent.setSourceIdentifier((String) jsonObject.get("sourceidentifier"));
        esMonEapEvent.setSourceEventId((String) jsonObject.get("sourceeventid"));
        esMonEapEvent.setSourceCiName((String) jsonObject.get("sourceciname"));
        esMonEapEvent.setSourceAlertKey((String) jsonObject.get("sourcealertkey"));
        esMonEapEvent.setSourceSeverity((String) jsonObject.get("sourceseverity"));
        esMonEapEvent.setKpiName((String) jsonObject.get("kpiname"));
        esMonEapEvent.setCustomCiName((String) jsonObject.get("ciname"));
        esMonEapEvent.setCiName((String) jsonObject.get("ciname"));
        esMonEapEvent.setAcknowledged((Integer) jsonObject.get("acknowledged"));
        esMonEapEvent.setDuplicateSerial((String) jsonObject.get("duplicateserial"));
        esMonEapEvent.setCreate_time((Long) jsonObject.get("create_time"));
        esMonEapEvent.setModify_time((Long) jsonObject.get("modify_time"));
        esMonEapEvent.setCiPrimaryKey((String) jsonObject.get("ciPrimaryKey"));
        esMonEapEvent.setAckUid((String) jsonObject.get("ackuid"));
        esMonEapEvent.setAckInfo((String) jsonObject.get("ackinfo"));
        esMonEapEvent.setCloseUId((String) jsonObject.get("closeuid"));
        esMonEapEvent.setCloseInfo((String) jsonObject.get("closeinfo"));

        //时间格式转化
        String firstoccurrence = (String) jsonObject.get("firstoccurrence");
        try {
            if (!BinaryUtils.isEmpty(firstoccurrence)) {
                esMonEapEvent.setFirstOccurrence(SIMPLE_DATE_FORMAT_Z.parse(firstoccurrence));
            }
        } catch (Exception e) {
            log.error("covert date error! " + e);
        }

        String lastoccurrence = (String) jsonObject.get("lastoccurrence");
        try {
            if (!BinaryUtils.isEmpty(lastoccurrence)) {
                esMonEapEvent.setLastOccurrence(SIMPLE_DATE_FORMAT_Z.parse(lastoccurrence));
            }
        } catch (Exception e) {
            log.error("covert date error! " + e);
        }

        String statechange = (String) jsonObject.get("statechange");
        try {
            if (!BinaryUtils.isEmpty(statechange)) {
                esMonEapEvent.setStateChange(SIMPLE_DATE_FORMAT_Z.parse(statechange));
            }
        } catch (Exception e) {
            log.error("covert date error! " + e);
        }

        String acktime = (String) jsonObject.get("acktime");
        try {
            if (!BinaryUtils.isEmpty(acktime)) {
                esMonEapEvent.setAckTime(SIMPLE_DATE_FORMAT_Z.parse(acktime));
            }
        } catch (Exception e) {
            log.error("covert date error! " + e);
        }

        String closetime = (String) jsonObject.get("closetime");
        try {
            if (!BinaryUtils.isEmpty(closetime)) {
                esMonEapEvent.setCloseTime(SIMPLE_DATE_FORMAT_Z.parse(closetime));
            }
        } catch (Exception e) {
            log.error("covert date error! " + e);
        }

        return esMonEapEvent;
    }

    @Override
    public boolean saveEventBatch(List<ESMonEapEvent> events) {
        //保存当前告警信息。启用ep使用redis保存，否则使用es保存
        if (epExist) {
            if (!BinaryUtils.isEmpty(events)) {
                events.stream().forEach(event -> saveEventCache(event));
            }
        } else {
            List<JSONObject> jsonObjectList = new ArrayList<>();
            if (!BinaryUtils.isEmpty(events)) {
                events.stream().forEach(event -> {
                    if (event.getDomainId() == null) {
                        event.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
                    }
                    jsonObjectList.add(JSONObject.parseObject(JSONObject.toJSONString(event)));
                });
                esEventSvc.saveOrUpdateBatch(jsonObjectList);
            }
        }

        // 保存历史告警
        // 2021-08-30 增加是否保存历史告警开关
        if (enableSaveEventHis) {
            return eventHistoryDao.saveOrUpdateBatchNoRefresh(JSON.parseArray(JSONObject.toJSONString(events)));

        } else {
            return true;
        }
    }

    @Override
    public List<ESMonEapEvent> queryCurrentEventsByCiCodesAndServeritys(Long domainId, Set<String> ciCodes, List<Integer> severitys, List<Integer> status) {
        if(includeDomainId && domainId == null){
            domainId=BaseConst.DEFAULT_DOMAIN_ID;
        }
        List<ESMonEapEvent> events = new ArrayList<>();
        // 启用EP查询redis数据，否则查询ES数据
        if (epExist) {
            if (BinaryUtils.isEmpty(ciCodes) && BinaryUtils.isEmpty(severitys)) {
                return events;
            } else {
                Set<String> eventIdKeys = new HashSet<>();
                if (BinaryUtils.isEmpty(ciCodes)) {
                    severitys.stream().forEach(severity -> eventIdKeys.addAll(cacheService.setByKey("severity_" + severity)));
                } else if (BinaryUtils.isEmpty(severitys)) {
                    ciCodes.stream().forEach(ciCode -> eventIdKeys.addAll(cacheService.setByKey("ciName_" + ciCode)));
                } else {
                    // 满足两个条件数据取交集操作
                    Set<String> ciNameKeys = new HashSet<>();
                    ciCodes.stream().forEach(ciCode -> ciNameKeys.addAll(cacheService.setByKey("ciName_" + ciCode)));

                    Set<String> severityKeys = new HashSet<>();
                    severitys.stream().forEach(severity -> severityKeys.addAll(cacheService.setByKey("severity_" + severity)));
                    if (BinaryUtils.isEmpty(ciNameKeys) || BinaryUtils.isEmpty(severityKeys)) {
                        return events;
                    }
                    ciNameKeys.retainAll(severityKeys);
                    if (BinaryUtils.isEmpty(ciNameKeys)) {
                        return events;
                    }
                    eventIdKeys.addAll(ciNameKeys);
                }
                events = cacheService.listByKeys(eventIdKeys.toArray(new String[eventIdKeys.size()]), ESMonEapEvent.class);
            }
        } else {
            BoolQueryBuilder query = QueryBuilders.boolQuery();
            query.must(QueryBuilders.termQuery("domainId", domainId));
            if (!BinaryUtils.isEmpty(ciCodes)) {
                BoolQueryBuilder ciNameQuery = QueryBuilders.boolQuery();
                ciNameQuery.should(QueryBuilders.termsQuery("ci_name.keyword", ciCodes));
                ciNameQuery.should(QueryBuilders.termsQuery("ciname.keyword", ciCodes));
                ciNameQuery.should(QueryBuilders.termsQuery("ciName.keyword", ciCodes));
                query.must(ciNameQuery);
            }

            if (!BinaryUtils.isEmpty(severitys)) {
                query.must(QueryBuilders.termsQuery("severity", severitys));
            }

            if (!BinaryUtils.isEmpty(status)) {
                query.must(QueryBuilders.termsQuery("status", status));
            }

            List<JSONObject> currentEvent = esEventSvc.getListByQuery(query);
            events = JSON.parseObject(JSON.toJSONString(currentEvent), LIST_EVENT_TYPE);
        }
        return events;
    }
}
