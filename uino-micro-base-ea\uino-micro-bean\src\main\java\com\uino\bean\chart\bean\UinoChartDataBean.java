package com.uino.bean.chart.bean;

import java.util.List;
import java.util.Map;

import org.springframework.util.Assert;

import com.alibaba.fastjson.annotation.JSONField;
import com.binary.core.util.BinaryUtils;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.uino.bean.chart.enums.UinoChartType;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
@ApiModel(value = "图表类", description = "图表数据类")
@JsonInclude(Include.NON_NULL)
public class UinoChartDataBean<T> {

	@JsonIgnore
	@JSONField(serialize = false, deserialize = false)
	@ApiModelProperty(value = "图表类型，PIE=饼图, LINECHART=折线图, LINESTACK=折现堆叠图, RADAR=雷达图;")
	private UinoChartType chartType;
	
	@ApiModelProperty(value = "X轴坐数据")
	private Object xData;

	@ApiModelProperty("Y轴数据")
	private List<String> yData;
	
	@ApiModelProperty(value = "数据的统计维度")
	private List<String> dimension;

	@ApiModelProperty("坐标数据")
	private T data;
	
	public UinoChartDataBean(UinoChartType chartType) {
		super();
		this.chartType = chartType;
	}

	public void setXData(Object xData) {
		if (xData instanceof List) {
			this.xData = xData;
		}
	}

	@JsonProperty(value = "xData")
	public Object getXData() {
		return this.xData;
	}

	@JsonProperty(value = "yData")
	public List<String> getYData() {
		return yData;
	}

	public void setYData(List<String> yData) {
		this.yData = yData;
	}

	public void setData(T data) {
		if(BinaryUtils.isEmpty(data)) {
			this.data = data;
			return;
		}
		switch (chartType) {
		case PIE:
			if (data instanceof List) {
				List l = (List) data;
				if (l.get(0) instanceof UinoChartDataItem) {
					this.data = data;
				}
			}
			break;
		case LINECHART:
			if (data instanceof List) {
				List l = (List) data;
				if (l.get(0) instanceof Number) {
					this.data = data;
				}
			}
			break;
		case LINESTACK:
			if (data instanceof Map) {
				this.data = data;
				// Map m = (Map) data;
				// if (m.values() instanceof List) {
				// List l = (List) data;
				// if (l.get(0) instanceof Number) {
				// this.data = data;
				// }
				// }
			}
			break;
		case RADAR:
			if (data instanceof List) {
				List l = (List) data;
				if (l.get(0) instanceof UinoChartDataItems) {
					this.data = data;
				}
			}
			break;
		default:
			Assert.isTrue(false, "不支持的图表类型");
			break;
		}
		Assert.notNull(this.data, "图表数据格式异常");
	}

}
