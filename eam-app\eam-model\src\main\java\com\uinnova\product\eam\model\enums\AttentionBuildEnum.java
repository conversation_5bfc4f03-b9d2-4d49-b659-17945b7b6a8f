package com.uinnova.product.eam.model.enums;

import lombok.Getter;

@Getter
@Deprecated
public enum AttentionBuildEnum {

    ENTERPRISE_BUILD_ASSETS(1, "企业级架构资产"),
    SYSTEM_BUILD_ASSETS(2, "系统级架构资产"),
    APPLICATION_ARCHITECTURE_ASSETS(3, "应用架构资产"),
    DA_ASSETS(4, "数据架构"),
    AP_ASSETS(6, "架构方案"),
    TA_ASSETS(5, "技术架构");

    private Integer buildType;

    private String buildDesc;

    AttentionBuildEnum(Integer buildType, String buildDesc) {
        this.buildType = buildType;
        this.buildDesc = buildDesc;
    }
}
