package com.uino.bean.cmdb.business;

import java.io.Serializable;
import java.util.List;

import org.springframework.util.Assert;

import com.uino.bean.cmdb.base.ESCiClassRlt;
import com.uino.bean.permission.business.IValidDto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class BindCIClassRltDto implements Serializable, IValidDto {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	public Long sourceClassId;

	private Long targetClassId;

	private Long domainId;

	public List<ESCiClassRlt> classRlts;

	@Override
	public void valid() {
		Assert.isTrue(
				(sourceClassId == null && targetClassId != null) || (sourceClassId != null && targetClassId == null),
				"请指定源或目标分类id");
	}
}
