package com.binary.jdbc.adapter;

import java.lang.reflect.Method;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.List;

import com.binary.jdbc.JdbcType;



/**
 * Jdbc各数据库适配器
 */
public interface JdbcAdapter {
	
	
	/**
	 * 获取所对应的JdbcType
	 * @return
	 */
	public JdbcType getJdbcType();
	
	
	
	/**
	 * 获取SQL函数对象
	 * @return
	 */
	public SqlFunction getSqlFunction();
	
	
	
	/**
	 * 获取SQL解析器
	 * @return
	 */
	public SqlParser getSqlParser();
	
	
	
	/** ResultSet-Java Mapping *************************************************************************************************/
	
	/**
	 * 根据数据库字段类型获取ResultSet读方法
	 * @param dbFieldType: 数据库字段类型
	 * @return
	 */
	public Method getRSMethod(String dbFieldType);
	
	
	
	
	/**
	 * 获取java读取ResultSet方法
	 * @param javaFieldType: java字段类型
	 * @return
	 */
	public Method getJavaRSMethod(Class<?> javaFieldType);
	
	
	
	
	
	
	
	
	/** PreparedStatement ***********************************************************************************/
	/**
	 * 获取查询操作PreparedStatement
	 * @param conn: 数据库连接
	 * @param sql: 执行的SQL
	 * @return
	 */
	public PreparedStatement prepareQueryStatement(Connection conn, String sql);
	
	
	/**
	 * 获取更新操作PreparedStatement
	 * @param conn: 数据库连接
	 * @param sql: 执行的SQL
	 * @return
	 */
	public PreparedStatement prepareUpdateStatement(Connection conn, String sql) ;
	
	
	/**
	 * 对PreparedStatement添加参数
	 * @param ps
	 * @param params
	 */
	public void setPreparedStatementParams(PreparedStatement ps, Object[] params);
	
	
	/**
	 * PreparedStatement查询
	 * @param ps
	 * @return
	 */
	public ResultSet executeQuery(PreparedStatement ps);
	
	
	/**
	 * 执行更新
	 * @param ps
	 * @return
	 */
	public int executeUpdate(PreparedStatement ps);
	
	
	/**
	 * 批量操作
	 * @param ps
	 * @return
	 */
	public int[] executeBatch(PreparedStatement ps);
	
	
	
	/**
	 * 关闭PreparedStatement
	 * @param ps
	 */
	public void closePreparedStatement(PreparedStatement ps);
	
	
	
	/**
	 * 半闭ResultSet
	 * @param rs
	 */
	public void closeResultSet(ResultSet rs);
		
	
	
	
	/**
	 * 获取conn作用域
	 * @param conn: 数据库连接
	 * @return
	 */
	public String getCatalog(Connection conn);
	
	
	/**
	 * 获取模式
	 * @param conn: 数据库连接
	 * @param userName: 数据库用户名
	 * @return
	 */
	public String getSchema(Connection conn, String userName);
	
	
	
	/**
	 * 获取rs中字段名
	 * @param rs: ResultSet
	 * @return 所有名称大写
	 */
	public String[] getColumnNames(ResultSet rs);
	
	
	/**
	 * 获取rs中字段名
	 * @param rs: ResultSet
	 * @param upperCase: 指定大小写
	 * @return
	 */
	public String[] getColumnNames(ResultSet rs, boolean upperCase);
	
	
	
	
	/**
	 * 获取表信息
	 * @param conn: 数据库连接
	 * @param userName: 数据库用户名
	 * @param tableName: 表名
	 * @return
	 */
	public Table getTable(Connection conn, String userName, String tableName);
	
	
	
	/**
	 * 获取视图信息
	 * @param conn: 数据库连接
	 * @param userName: 数据库用户名
	 * @param tableName: 表名
	 * @return
	 */
	public Table getView(Connection conn, String userName, String viewName);
	
	
	
	
	
	/**
	 * 跟据表名获取所有字段信息
	 * @param conn: 数据库连接
	 * @param userName: 数据库用户名
	 * @param tableName: 表名
	 * @return List<Column>
	 */
	public List<Column> getColumns(Connection conn, String userName, String tableName);
	
	
}








