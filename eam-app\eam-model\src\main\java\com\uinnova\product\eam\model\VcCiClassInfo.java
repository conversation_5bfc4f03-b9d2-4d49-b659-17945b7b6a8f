package com.uinnova.product.eam.model;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.comm.model.ci.CcCiClass;
import com.uinnova.product.vmdb.comm.model.ci.CcFixAttrMapping;

import java.io.Serializable;
import java.util.List;

public class VcCiClassInfo implements Serializable {
	private static final long serialVersionUID = 1L;

	@Comment("CI分类")
	private CcCiClass ciClass;
	
	@Comment("属性定义")
	private List<CcCiAttrDef> attrDefs;

	@Comment("常柱属性映射对象")
	private CcFixAttrMapping fixMapping;
	
	@Comment("分类下CI的数量")
	private Long ciCount;

	@Comment("图标名")
	private String iconName;

	public CcCiClass getCiClass() {
		return ciClass;
	}

	public void setCiClass(CcCiClass ciClass) {
		this.ciClass = ciClass;
	}

	public List<CcCiAttrDef> getAttrDefs() {
		return attrDefs;
	}

	public void setAttrDefs(List<CcCiAttrDef> attrDefs) {
		this.attrDefs = attrDefs;
	}

	public CcFixAttrMapping getFixMapping() {
		return fixMapping;
	}

	public void setFixMapping(CcFixAttrMapping fixMapping) {
		this.fixMapping = fixMapping;
	}

	public Long getCiCount() {
		return ciCount;
	}

	public void setCiCount(Long ciCount) {
		this.ciCount = ciCount;
	}

	public String getIconName() {
		return iconName;
	}

	public void setIconName(String iconName) {
		this.iconName = iconName;
	}
}
