package com.uinnova.product.eam.comm.model.es;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

@Data
@Comment("引导项进度表[uino_eam_boot_page_progress]")
public class BootPageProgress {
    @Comment("主键id")
    private Long id;
    @Comment("当前用户id")
    private String userId;
    @Comment("用户角色")
    private Long roleId;
    @Comment("引导项id")
    private Long bootEntryId;
    @Comment("引导项功能id")
    private Long bootEntryFunctionId;
}
