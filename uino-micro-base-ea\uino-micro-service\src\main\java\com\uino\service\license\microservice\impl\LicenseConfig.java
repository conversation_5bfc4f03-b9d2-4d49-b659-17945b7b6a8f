package com.uino.service.license.microservice.impl;

import com.uino.license.sdk.LicenseAuthorityAdapter;
import com.uino.license.sdk.LicenseAuthorityCallback;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * description
 *
 * <AUTHOR>
 * @since 2022/4/27 16:36
 */
@Configuration
public class LicenseConfig {

    @Bean
    public LicenseAuthorityAdapter licenseAuthorityAdapter(){
        return new LicenseAuthorityAdapter(new LicenseAuthorityCallback() {
            @Override
            public String getProductAbbreviation() {
                return "quickea";
            }

            @Override
            public String getProductID() {
                return "quickea";
            }

            @Override
            public String getAuthType() {
                return "";
            }

            @Override
            public String getReleaseUserTime() {
                return "";
            }

            @Override
            public String getProjectID() {
                return "";
            }

            @Override
            public String getUserID() {
                return "007";
            }

            @Override
            public String getLogFilePath() {
                return "";
            }

            @Override
            public void migrationCallback(String s) {

            }

            @Override
            public void revocationCallback(String s) {

            }
        });
    }
}
