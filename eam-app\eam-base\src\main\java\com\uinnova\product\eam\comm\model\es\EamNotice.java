package com.uinnova.product.eam.comm.model.es;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 消息公告
 */
@Data
public class EamNotice implements Serializable {

    @Comment("主键id")
    private Long id;

    @Comment("领域id")
    private Long domainId;

    @Comment("类型（message消息、notice公告）")
    private String type;

    @Comment("分类（system系统通知、workflow工作流）")
    private String classify;

    @Comment("标识（plan方案、diagram视图、matrix矩阵）")
    private String tag;

    @Comment("内容")
    private String content;

    @Comment("是否已读（0未读1已读）")
    private Integer read;

    @Comment("用户")
    private String userName;

    @Comment("关键字")
    private List<EamNoticeKeyword> keywords;

    @Comment("创建时间")
    private Long createTime;

    @Comment("是否删除(0否1是)")
    private Integer isDeleted;

    @Comment("扩展字段")
    private String customer;

    @Comment("扩展字段")
    private String customer1;

    @Comment("扩展字段")
    private String customer2;

    @Comment("扩展字段")
    private String customer3;

    @Comment("消息类型(planDiagramUpdate=方案视图更新消息;workflow=审批消息;planNewVersion:方案版本更新消息;share=与我协作消息)")
    private String msgType;
}
