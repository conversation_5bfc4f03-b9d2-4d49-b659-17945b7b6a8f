package com.uinnova.product.eam.service.es;

import com.uinnova.product.eam.comm.model.es.FlowSystemAssociatedFeatures;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;

/**
 * 流程体系关联要素
 *
 * <AUTHOR>
 * @since 2024/5/27 14:50
 */
@Service
public class FlowSystemAssociatedFeaturesPrivateDao extends AbstractESBaseDao<FlowSystemAssociatedFeatures, FlowSystemAssociatedFeatures> {
    @Override
    public String getIndex() {
        return "uino_flow_system_associated_features_private";
    }

    @Override
    public String getType() {
        return "_doc";
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }
}
