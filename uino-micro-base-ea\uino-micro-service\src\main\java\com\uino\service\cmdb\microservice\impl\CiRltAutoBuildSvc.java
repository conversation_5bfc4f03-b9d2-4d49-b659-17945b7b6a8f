package com.uino.service.cmdb.microservice.impl;

import com.binary.core.exception.BinaryException;
import com.binary.core.util.BinaryUtils;
import com.binary.framework.exception.ServiceException;
import com.binary.jdbc.Page;
import com.google.common.collect.Iterables;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClass;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.comm.model.ci.CcCiClass;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uino.bean.cmdb.base.*;
import com.uino.bean.cmdb.business.BindCiRltRequestDto;
import com.uino.bean.cmdb.business.ClassRltListDto;
import com.uino.bean.cmdb.business.ClassRltQueryDto;
import com.uino.bean.cmdb.business.ImportResultMessage;
import com.uino.bean.cmdb.query.ESCISearchBean;
import com.uino.bean.cmdb.query.ESCiRltAutoBuildSearchBean;
import com.uino.bean.sys.base.RltSourceId;
import com.uino.dao.BaseConst;
import com.uino.dao.cmdb.ESCiRltAutoBuildSvc;
import com.uino.dao.util.ESUtil;
import com.uino.service.cmdb.microservice.*;
import com.uino.util.lock.LockUtil;
import com.uino.util.message.queue.MessageQueueProducer;
import com.uino.util.sys.LibTypeUtil;
import com.uino.util.sys.SysUtil;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.Operator;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.sort.SortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.script.Invocable;
import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * @Classname CiRltAutoBuildSvc
 * @Description TODO
 * @Date 2020/6/30 10:31
 * <AUTHOR> sh
 */
@Service
@Slf4j
public class CiRltAutoBuildSvc implements ICiRltAutoBuildSvc {

    @Autowired
    private ESCiRltAutoBuildSvc esCiRltAutoBuildSvc;

    @Autowired
    private ICIClassSvc iciClassSvc;

    @Autowired
    private IRltClassSvc iRltClassSvc;

    @Autowired
    private CIClassRltSvc ciClassRltSvc;

    @Autowired
    private ICISvc iciSvc;

    @Autowired
    private ICIRltSvc iciRltSvc;

    @Autowired
    private IVisualModelSvc iVisualModelSvc;

    @Autowired(required = false)
    private LockUtil lockUtil;

    @Autowired
    ITaskLockSvc taskLockSvc;

    @Autowired(required = false)
    private MessageQueueProducer messageQueueProducer;

    @Override
    public ESCiRltAutoBuild saveDefault(Long domainId, Long sourceCiClassId, Long targetCiClassId, Long rltClassId, Long visualModelId) {
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.filter(QueryBuilders.termQuery("sourceCiClassId", sourceCiClassId));
        queryBuilder.filter(QueryBuilders.termQuery("targetCiClassId", targetCiClassId));
        queryBuilder.filter(QueryBuilders.termQuery("rltClassId", rltClassId));
        queryBuilder.filter(QueryBuilders.termQuery("visualModelId", visualModelId));
        List<ESCiRltAutoBuild> listByQuery = esCiRltAutoBuildSvc.getListByQuery(queryBuilder);
        if (listByQuery != null && !listByQuery.isEmpty()) {
            throw new RuntimeException("构建规则已存在");
        }
        CCcCiClass cdt = new CCcCiClass();
        cdt.setDomainId(domainId);
        cdt.setIds(new Long[]{sourceCiClassId, targetCiClassId});
        List<CcCiClassInfo> ccCiClassInfos = iciClassSvc.queryClassByCdt(cdt);
        if (ccCiClassInfos == null) {
            throw new RuntimeException("分类id有误");
        }

        CCcCiClass rltcdt = new CCcCiClass();
        rltcdt.setDomainId(domainId);
        rltcdt.setId(rltClassId);
        List<CcCiClassInfo> rltClassByCdt = iRltClassSvc.getRltClassByCdt(rltcdt);
        if (rltClassByCdt == null || rltClassByCdt.size() != 1) {
            throw new RuntimeException("关系id有误");
        }
        // ESVisualModel visualModel = findVisualModel();
        ESVisualModel esVisualModel = iVisualModelSvc.queryVisualModelById(SysUtil.getCurrentUserInfo().getDomainId(), visualModelId);

        if (BinaryUtils.isEmpty(esVisualModel)) {
            throw new RuntimeException("模型id有误");
        }

        Long sourceAttrDefId = 0L;
        Long targetAttrDefId = 0L;
        for (CcCiClassInfo ccCiClassInfo : ccCiClassInfos) {
            List<CcCiAttrDef> attrDefs = ccCiClassInfo.getAttrDefs();
            if (ccCiClassInfo.getCiClass().getId().equals(sourceCiClassId)) {
                sourceAttrDefId = attrDefs.get(0).getId();
            } else if (ccCiClassInfo.getCiClass().getId().equals(targetCiClassId)) {
                targetAttrDefId = attrDefs.get(0).getId();
            }
        }
        List<ESCiRltAutoBuild.AutoBuildCondition> buildConditions = new ArrayList<>();
        ESCiRltAutoBuild.AutoBuildCondition autoBuildCondition = new ESCiRltAutoBuild.AutoBuildCondition();
        autoBuildCondition.setOptType(0);
        autoBuildCondition.setSourceAttrDefId(sourceAttrDefId);
        autoBuildCondition.setTargetAttrDefId(targetAttrDefId);
        buildConditions.add(autoBuildCondition);

        ESCiRltAutoBuild esCiRltAutoBuild = new ESCiRltAutoBuild();
        esCiRltAutoBuild.setDomainId(domainId);
        esCiRltAutoBuild.setId(ESUtil.getUUID());
        esCiRltAutoBuild.setSourceCiClassId(sourceCiClassId);
        esCiRltAutoBuild.setTargetCiClassId(targetCiClassId);
        esCiRltAutoBuild.setRltClassId(rltClassId);
        esCiRltAutoBuild.setRun(false);
        esCiRltAutoBuild.setVisualModelId(visualModelId);
        esCiRltAutoBuild.setBuildConditions(buildConditions);
        esCiRltAutoBuildSvc.saveOrUpdate(esCiRltAutoBuild);
        return esCiRltAutoBuild;

    }

    @Override
    public Long updateCiRltAutoBuild(ESCiRltAutoBuild esCiRltAutoBuild) {
        ESCiRltAutoBuild before = esCiRltAutoBuildSvc.getById(esCiRltAutoBuild.getId());
        if (before == null) {
            throw new RuntimeException("规则id有误");
        }
        CCcCiClass cdt = new CCcCiClass();
        cdt.setIds(new Long[]{esCiRltAutoBuild.getSourceCiClassId(), esCiRltAutoBuild.getTargetCiClassId()});
        List<CcCiClassInfo> ccCiClassInfos = iciClassSvc.queryClassByCdt(cdt);
        if (ccCiClassInfos == null) {
            throw new RuntimeException("分类id有误");
        }

        CCcCiClass rltcdt = new CCcCiClass();
        rltcdt.setId(esCiRltAutoBuild.getRltClassId());
        List<CcCiClassInfo> rltClassByCdt = iRltClassSvc.getRltClassByCdt(rltcdt);
        if (rltClassByCdt == null || rltClassByCdt.size() != 1) {
            throw new RuntimeException("关系id有误");
        }
        // ESVisualModel visualModel = findVisualModel();
        // ESVisualModel esVisualModel = iVisualModelSvc.queryVisualModelById(SysUtil.getCurrentUserInfo().getDomainId(), esCiRltAutoBuild.getVisualModelId());
        // if (BinaryUtils.isEmpty(esVisualModel)) {
        //     throw new RuntimeException("模型id有误");
        // }
        return esCiRltAutoBuildSvc.saveOrUpdate(esCiRltAutoBuild);
    }

    @Override
    public List<ClassRltListDto> getAutoBuildListByRltId(Long rltId) {
        // 根据关系id查询出来当前关系在元模型设置的所有源端和目标端
        ClassRltQueryDto classRltQueryDto = new ClassRltQueryDto();
        classRltQueryDto.setRltClassId(rltId);
        List<ESCiClassRlt> classRlt = ciClassRltSvc.queryClassRlt(classRltQueryDto);

        // 使用关系id查询当前关系的所有存在的构建条件
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.filter(QueryBuilders.termQuery("rltClassId", rltId));
        List<ESCiRltAutoBuild> listByQueryScroll = esCiRltAutoBuildSvc.getListByQueryScroll(boolQuery);
        // 构建listByQueryScroll的map
        Map<String, ESCiRltAutoBuild> esCiRltAutoBuildMap = new HashMap<>();
        for (ESCiRltAutoBuild esCiRltAutoBuild : listByQueryScroll) {
            esCiRltAutoBuildMap.put(esCiRltAutoBuild.getSourceCiClassId() + "_" + esCiRltAutoBuild.getTargetCiClassId(),
                    esCiRltAutoBuild);
        }
        HashSet<Long> classIds = new HashSet<>();
        for (ESCiClassRlt esCiClassRlt : classRlt) {
            classIds.add(esCiClassRlt.getSourceClassId());
            classIds.add(esCiClassRlt.getTargetClassId());
        }
        HashMap<Long, CcCiClassInfo> classMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(classIds)) {
            CCcCiClass cCcCiClass = new CCcCiClass();
            cCcCiClass.setIds(classIds.toArray(new Long[0]));
            List<CcCiClassInfo> queryClassByCdt = iciClassSvc.queryClassByCdt(cCcCiClass);
            for (CcCiClassInfo ccCiClassInfo : queryClassByCdt) {
                classMap.put(ccCiClassInfo.getCiClass().getId(), ccCiClassInfo);
            }
        }

        // 循环classRlt，并构建ClassRltListDto的集合返回
        List<ClassRltListDto> classRltListDtos = new ArrayList<>();
        for (ESCiClassRlt esCiClassRlt : classRlt) {
            ClassRltListDto classRltListDto = new ClassRltListDto();
            CcCiClassInfo sourceClassInfo = classMap.get(esCiClassRlt.getSourceClassId());
            CcCiClassInfo targetClassInfo = classMap.get(esCiClassRlt.getTargetClassId());
            if (sourceClassInfo == null || targetClassInfo == null) {
                continue;
            }
            classRltListDto.setSourceClassName(sourceClassInfo.getCiClass().getClassName());
            classRltListDto.setSourceClassId(esCiClassRlt.getSourceClassId());
            classRltListDto.setTargetClassName(targetClassInfo.getCiClass().getClassName());
            classRltListDto.setTargetClassId(esCiClassRlt.getTargetClassId());
            if(esCiRltAutoBuildMap.get(esCiClassRlt.getSourceClassId() + "_" + esCiClassRlt.getTargetClassId())!=null){
                classRltListDto.setCiRltAutoBuild(esCiRltAutoBuildMap.get(esCiClassRlt.getSourceClassId() + "_" + esCiClassRlt.getTargetClassId()));
            }else{
                classRltListDto.setCiRltAutoBuild(new ESCiRltAutoBuild());
            }

            classRltListDtos.add(classRltListDto);
        }
        return classRltListDtos;
    }

    @Override
    public ESCiRltAutoBuild findCiRltAutoBuild(Long domainId, Long sourceCiClassId, Long targetCiClassId, Long rltClassId, Long visualModelId) {
        if (sourceCiClassId == null) {
            throw new ServiceException("获取信息失败，源分类已删除！");
        }
        if (targetCiClassId == null) {
            throw new ServiceException("获取信息失败，目标分类已删除！");
        }
        if (rltClassId == null) {
            throw new ServiceException("获取信息失败，关联关系已删除！");
        }
        if (visualModelId == null) {
            throw new ServiceException("获取信息失败，元模型不存在！");
        }
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.filter(QueryBuilders.termQuery("sourceCiClassId", sourceCiClassId));
        queryBuilder.filter(QueryBuilders.termQuery("targetCiClassId", targetCiClassId));
        queryBuilder.filter(QueryBuilders.termQuery("rltClassId", rltClassId));
        queryBuilder.filter(QueryBuilders.termQuery("visualModelId", visualModelId));
        List<ESCiRltAutoBuild> listByQuery = esCiRltAutoBuildSvc.getListByQuery(queryBuilder);
        if (listByQuery == null || listByQuery.isEmpty()) {
            return saveDefault(domainId, sourceCiClassId, targetCiClassId, rltClassId, visualModelId);
        } else {
            return listByQuery.get(0);
        }
    }

    @Override
    public void deleteCiRltAutoBuild(Long sourceCiClassId, Long targetCiClassId, Long rltClassId, Long visualModelId) {
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.filter(QueryBuilders.termQuery("sourceCiClassId", sourceCiClassId));
        queryBuilder.filter(QueryBuilders.termQuery("targetCiClassId", targetCiClassId));
        queryBuilder.filter(QueryBuilders.termQuery("rltClassId", rltClassId));
        queryBuilder.filter(QueryBuilders.termQuery("visualModelId", visualModelId));
        esCiRltAutoBuildSvc.deleteByQuery(queryBuilder, true);
    }

    @Override
    public void runCiRltAutoBuildAll(Long domainId) {
        domainId = domainId == null ? BaseConst.DEFAULT_DOMAIN_ID : domainId;
        String taskName = "ciRltAutoBuildAll";
        // 任务执行时长默认估算为60分钟
        long taskExecuteTime = 3 * 60 * 60 * 1000L;
        // 无法获得任务锁则直接返回
        if (!taskLockSvc.getLock(taskName, taskExecuteTime)) {
            log.info("Can not get lock, return.");
            return;
        }
        try {
//            ESVisualModel visualModel = findVisualModel();
            BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
            QueryBuilders.termQuery("domainId", domainId);
//            querybuilder.filter(QueryBuilders.termQuery("visualModelId", visualModel.getId()));
            queryBuilder.filter(QueryBuilders.termQuery("run", true));
            List<ESCiRltAutoBuild> listByQuery = esCiRltAutoBuildSvc.getListByQuery(queryBuilder);
            listByQuery.forEach(esCiRltAutoBuild -> {
                try {
//                    //2021-07-29 修改发送kafka消息，ap执行构建任务
//                    runCiRltAutoBuildById(esCiRltAutoBuild.getId());
                    build(esCiRltAutoBuild);
                } catch (Exception e) {
                    log.error("", e);
                }

            });
        } catch (Exception e) {
            log.error("", e);
        } finally {
            taskLockSvc.breakLock(taskName);
        }
    }

    @Override
    public boolean runCiRltAutoBuildById(Long id) {

        ESCiRltAutoBuild esCiRltAutoBuild = esCiRltAutoBuildSvc.getById(id);
        if (esCiRltAutoBuild == null) {
            throw new RuntimeException("规则不存在");
        }

//        //2021-07-29 修改发送kafka消息，ap执行构建任务
//        Event<APBaseRule> sendData = new Event();
//        sendData.setEventID(String.valueOf(ESUtil.getUUID()));
//        sendData.setTriggerType(2);
//        sendData.setEventName(EventTypeConst.EVENT_NAME_AP_RLT_BUILD);
//        sendData.setEventType(EventTypeConst.EVENT_TYPE_AP);
//        sendData.setTimestamp(System.currentTimeMillis());
//
//        APBaseRule apBaseRule = new APBaseRule();
//        apBaseRule.setId(esCiRltAutoBuild.getId());
//        apBaseRule.setName(EventTypeConst.EVENT_NAME_AP_RLT_BUILD);
//        apBaseRule.setDescription(esCiRltAutoBuild.getName());
//        sendData.setApRule(apBaseRule);
//        String msgStr = JSONObject.toJSONString(sendData);
//        log.debug("关系自动构建发送kafka消息：{}", msgStr);
//        messageQueueProducer.sendMessage(MessageTopicConst.AP_HANDLE_TOPIC, msgStr);

        return build(esCiRltAutoBuild);
    }

    @Override
    public boolean apRunRltAutoBuild(Long id) {
        ESCiRltAutoBuild esCiRltAutoBuild = esCiRltAutoBuildSvc.getById(id);
        if (esCiRltAutoBuild == null) {
            throw new RuntimeException("规则不存在");
        }
        return build(esCiRltAutoBuild);
    }

    @Override
    public Long saveCiRltAutoBuild(ESCiRltAutoBuild esCiRltAutoBuild) {
        if (esCiRltAutoBuild.getDomainId() == null) {
            esCiRltAutoBuild.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
        }
        Long domainId = esCiRltAutoBuild.getDomainId();
        //构建规则重复性校验
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.filter(QueryBuilders.termQuery("sourceCiClassId", esCiRltAutoBuild.getSourceCiClassId()));
        queryBuilder.filter(QueryBuilders.termQuery("targetCiClassId", esCiRltAutoBuild.getTargetCiClassId()));
        queryBuilder.filter(QueryBuilders.termQuery("rltClassId", esCiRltAutoBuild.getRltClassId()));

        List<ESCiRltAutoBuild> listByQuery = esCiRltAutoBuildSvc.getListByQuery(queryBuilder);
        if (listByQuery != null && !listByQuery.isEmpty()) {
                throw new RuntimeException("构建规则已存在");
        }

        if (BinaryUtils.isEmpty(esCiRltAutoBuild.getId())) {
            esCiRltAutoBuild.setId(ESUtil.getUUID());
            esCiRltAutoBuild.setCreateTime(new Date().getTime());
        } else {
            ESCiRltAutoBuild esCiRltAutoBuildSvcById = esCiRltAutoBuildSvc.getById(esCiRltAutoBuild.getId());
            esCiRltAutoBuild.setCreateTime(esCiRltAutoBuildSvcById.getCreateTime());
        }
        CCcCiClass cdt = new CCcCiClass();
        cdt.setDomainId(domainId);
        cdt.setIds(new Long[]{esCiRltAutoBuild.getSourceCiClassId(), esCiRltAutoBuild.getTargetCiClassId()});
        List<CcCiClassInfo> ccCiClassInfos = iciClassSvc.queryClassByCdt(cdt);
        if (ccCiClassInfos == null) {
            throw new RuntimeException("分类id有误");
        }

        CCcCiClass rltcdt = new CCcCiClass();
        rltcdt.setDomainId(domainId);
        rltcdt.setId(esCiRltAutoBuild.getRltClassId());
        List<CcCiClassInfo> rltClassByCdt = iRltClassSvc.getRltClassByCdt(rltcdt);
        if (rltClassByCdt == null || rltClassByCdt.size() != 1) {
            throw new RuntimeException("关系id有误");
        }
        return esCiRltAutoBuildSvc.saveOrUpdate(esCiRltAutoBuild);
    }

    @Override
    public Page<ESCiRltAutoBuild> queryCiRltAutoBuildPage(ESCiRltAutoBuildSearchBean searchBean) {
        if (searchBean.getDomainId() == null) {
            searchBean.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
        }
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        List<SortBuilder<?>> sorts = new ArrayList<>();
        //设置默认id降序
        if (BinaryUtils.isEmpty(searchBean.getOrder())) {
            sorts.add(SortBuilders.fieldSort("createTime").order(SortOrder.DESC));
        } else {
            sorts.add(SortBuilders.fieldSort(searchBean.getOrder()).order(searchBean.isDesc() ? SortOrder.DESC : SortOrder.ASC));
        }
        //根据源分类id查询
        if (!BinaryUtils.isEmpty(searchBean.getSourceCiClassId())) {
            query.must(QueryBuilders.termQuery("sourceCiClassId", searchBean.getSourceCiClassId()));
        }

        //根据目标分类id查询
        if (!BinaryUtils.isEmpty(searchBean.getTargetCiClassIds())) {
            query.must(QueryBuilders.termsQuery("targetCiClassId", searchBean.getTargetCiClassIds()));
        }

        //根据源分类属性名称查询
        if (!BinaryUtils.isEmpty(searchBean.getAttrDefIds())) {
            BoolQueryBuilder attrQuery = QueryBuilders.boolQuery();
            attrQuery.should(QueryBuilders.termsQuery("buildConditions.sourceAttrDefId", searchBean.getAttrDefIds()));
            attrQuery.should(QueryBuilders.termsQuery("buildConditions.targetAttrDefId", searchBean.getAttrDefIds()));
            query.must(attrQuery);
        }

        //根据名称查询
        if (!BinaryUtils.isEmpty(searchBean.getName())) {
            query.must(QueryBuilders.multiMatchQuery(searchBean.getName(), "name").operator(Operator.AND));
        }

        //根据关系id查询
        if (!BinaryUtils.isEmpty(searchBean.getRltClassIds())) {
            query.must(QueryBuilders.termsQuery("rltClassId", searchBean.getRltClassIds()));
        }

        //根据状态查询
        if (!BinaryUtils.isEmpty(searchBean.getStatus())) {
            query.must(QueryBuilders.termQuery("run", searchBean.getStatus() != 0));
        }

        //根据状态查询
        if (!BinaryUtils.isEmpty(searchBean.getRuleStatus()) && searchBean.getRuleStatus() != 0) {
            query.must(QueryBuilders.termQuery("ruleStatus", searchBean.getRuleStatus()));
        }
        return esCiRltAutoBuildSvc.getSortListByQuery(searchBean.getPageNum(), searchBean.getPageSize(), query, sorts);
    }

    @Override
    public Integer deleteById(Long id) {
        return esCiRltAutoBuildSvc.deleteById(id);
    }

    @Override
    public Long saveOrUpdate(ESCiRltAutoBuild esCiRltAutoBuild) {
        return esCiRltAutoBuildSvc.saveOrUpdate(esCiRltAutoBuild);
    }

    @Override
    public List<ESCiRltAutoBuild> getAutoBuildListByRltClassId(List<Long> rltClassIds) {
        ESCiRltAutoBuildSearchBean esCiRltAutoBuildSearchBean = new ESCiRltAutoBuildSearchBean();
        esCiRltAutoBuildSearchBean.setRltClassIds(rltClassIds);
        Page<ESCiRltAutoBuild> esCiRltAutoBuildPage = queryCiRltAutoBuildPage(esCiRltAutoBuildSearchBean);
        List<ESCiRltAutoBuild> data = esCiRltAutoBuildPage.getData();
        richRltName(data);
        return data;
    }

    @Override
    public void runCiRltAutoBuildByIds(List<Long> ids) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        for (Long id : ids) {
            boolQueryBuilder.should(QueryBuilders.termQuery("id",id));
        }
        List<ESCiRltAutoBuild> listByQuery = esCiRltAutoBuildSvc.getListByQuery(boolQueryBuilder);
        listByQuery.forEach(esCiRltAutoBuild -> {
            try {
                build(esCiRltAutoBuild);
            } catch (Exception e) {
                log.error("构建失败", e);
                throw new BinaryException("关系构建失败" + e.getMessage());
            }
        });
    }

    @Override
    public void deleteCiRltAutoBuildBatchByCiClassIds(List<Long> ciClassIds, Long visualModelId) {
        Iterables.removeIf(ciClassIds, Objects::isNull);
        if (CollectionUtils.isEmpty(ciClassIds)) {
            return;
        }
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.termQuery("visualModelId",visualModelId));
        BoolQueryBuilder shouldBuilder = QueryBuilders.boolQuery();
        shouldBuilder.should(QueryBuilders.termsQuery("sourceCiClassId",ciClassIds));
        shouldBuilder.should(QueryBuilders.termsQuery("targetCiClassId",ciClassIds));
        boolQueryBuilder.filter(shouldBuilder);
        esCiRltAutoBuildSvc.deleteByQuery(boolQueryBuilder,Boolean.TRUE);
    }

    private void richRltName(List<ESCiRltAutoBuild> data) {
        HashSet<Long> ciClassIds = new HashSet<>();
        for (ESCiRltAutoBuild datum : data) {
            ciClassIds.add(datum.getSourceCiClassId());
            ciClassIds.add(datum.getTargetCiClassId());
        }
        List<ESCIClassInfo> esciClassInfos = iciClassSvc.queryESClassInfoByIds(new ArrayList<>(ciClassIds));
        HashMap<Long, String> ciClassNameMap = new HashMap<>();
        HashMap<Long, String> ciClassAttrDefNameMap = new HashMap<>();
        for (ESCIClassInfo esciClassInfo : esciClassInfos) {
            ciClassNameMap.put(esciClassInfo.getId(),esciClassInfo.getClassName());
            for (ESCIAttrDefInfo attrDef : esciClassInfo.getAttrDefs()) {
                ciClassAttrDefNameMap.put(attrDef.getId(),attrDef.getProStdName());
            }
        }

        //过滤掉已删除的分类
        data.removeIf(datum -> ciClassNameMap.get(datum.getSourceCiClassId()) == null || datum.getTargetCiClassId() == null);

        for (ESCiRltAutoBuild datum : data) {
            datum.setSourceCiClassOldName(ciClassNameMap.get(datum.getSourceCiClassId()));
            datum.setTargetCiClassOldName(ciClassNameMap.get(datum.getTargetCiClassId()));
            for (ESCiRltAutoBuild.AutoBuildCondition buildCondition : datum.getBuildConditions()) {
                buildCondition.setSourceAttrStdName(ciClassAttrDefNameMap.get(buildCondition.getSourceAttrDefId()));
                buildCondition.setTargetAttrStdName(ciClassAttrDefNameMap.get(buildCondition.getTargetAttrDefId()));
            }
        }
    }

    /**
     * 构建规则
     *
     * @param esCiRltAutoBuild
     */
    private boolean build(ESCiRltAutoBuild esCiRltAutoBuild) {
        log.info("ciRltAutoBuild start. id is {}", esCiRltAutoBuild.getId());
        boolean existRlt = ciClassRltSvc.existRlt(esCiRltAutoBuild.getSourceCiClassId(), esCiRltAutoBuild.getTargetCiClassId(), esCiRltAutoBuild.getRltClassId(), true);
        if (!existRlt) {
            log.error("分类间关系不存在");
            throw new RuntimeException("分类间关系不存在");
        }
        String taskName = "ciRltAutoBuild" + esCiRltAutoBuild.getId();
        // 任务执行时长默认估算为60分钟
        long taskExecuteTime = 30 * 60 * 1000L;
        // 无法获得任务锁则直接返回
        if (!taskLockSvc.getLock(taskName, taskExecuteTime)) {
            log.info("Can not get lock, return.");
            log.info("无法获取到锁");
            return false;
        }
        try {
            richCiRltAutoBuild(esCiRltAutoBuild);
            buildData(esCiRltAutoBuild);
            return true;
        } catch (Exception e) {
            log.error("自动构建失败", e);
            log.error("构建规则为：{}", esCiRltAutoBuild.toJson());
            throw new RuntimeException("自动构建失败，构建规则为：" + esCiRltAutoBuild.toJson());
        } finally {
            // 解除任务锁
            taskLockSvc.breakLock(taskName);
        }
    }

    /**
     * 获取当前生效模型
     *
     * @return
     */
    private ESVisualModel findVisualModel() {
        List<ESVisualModel> esVisualModels = iVisualModelSvc.queryVisualModels(1L);
        for (ESVisualModel esVisualModel : esVisualModels) {
            Boolean enable = esVisualModel.getEnable();
            if (enable) {
                return esVisualModel;
            }
        }
        throw new RuntimeException("可视化建模模型不存在");
    }


    /**
     * 丰富规则
     *
     * @param esCiRltAutoBuild
     * @throws Exception
     */
    public void richCiRltAutoBuild(ESCiRltAutoBuild esCiRltAutoBuild) {
        try {
            Long domainId = esCiRltAutoBuild.getDomainId();
            Long sourceCiClassId = esCiRltAutoBuild.getSourceCiClassId();
            Long targetCiClassId = esCiRltAutoBuild.getTargetCiClassId();

            esCiRltAutoBuild.getSourceCiClassIds().add(sourceCiClassId);
            esCiRltAutoBuild.getTargetCiClassIds().add(targetCiClassId);
            CCcCiClass cdtP = new CCcCiClass();
            cdtP.setDomainId(domainId);
            cdtP.setParentIds(new Long[]{sourceCiClassId, targetCiClassId});
            List<CcCiClassInfo> classList = iciClassSvc.queryClassByCdt(cdtP);
            if (classList != null && !classList.isEmpty()) {
                classList.forEach(ccCiClass -> {
                    if (ccCiClass.getCiClass().getParentId().equals(sourceCiClassId)) {
                        esCiRltAutoBuild.getSourceCiClassIds().add(ccCiClass.getCiClass().getId());
                    }
                    if (ccCiClass.getCiClass().getParentId().equals(targetCiClassId)) {
                        esCiRltAutoBuild.getTargetCiClassIds().add(ccCiClass.getCiClass().getId());
                    }
                });
            }


            // 查询分类校验分类及属性定义
            Long[] ciClassIds = new Long[]{sourceCiClassId, targetCiClassId};
            CCcCiClass ciClassCdt = new CCcCiClass();
            ciClassCdt.setDomainId(domainId);
            ciClassCdt.setIds(ciClassIds);
            List<CcCiClassInfo> clsInfoList = iciClassSvc.queryClassByCdt(ciClassCdt);
            CCcCiClass rltClassCdt = new CCcCiClass();
            rltClassCdt.setDomainId(domainId);
            rltClassCdt.setId(esCiRltAutoBuild.getRltClassId());
            List<CcCiClassInfo> rltClassByCdt = iRltClassSvc.getRltClassByCdt(rltClassCdt);

            Map<Long, CcCiAttrDef> sourceAttrDefMap = new HashMap<>();
            Map<Long, CcCiAttrDef> targetAttrDefMap = new HashMap<>();
            Map<Long, CcCiAttrDef> rltAttrDefMap = new HashMap<>();
            if (clsInfoList != null && clsInfoList.size() != 0) {
                for (CcCiClassInfo classInfo : clsInfoList) {
                    CcCiClass ciClass = classInfo.getCiClass();
                    Long classId = ciClass.getId();
                    List<CcCiAttrDef> attrDefs = classInfo.getAttrDefs();
                    if (classId.equals(sourceCiClassId) && attrDefs != null) {
                        for (CcCiAttrDef def : attrDefs) {
                            sourceAttrDefMap.put(def.getId(), def);
                        }
                    }
                    if (classId.equals(targetCiClassId) && attrDefs != null) {
                        for (CcCiAttrDef def : attrDefs) {
                            targetAttrDefMap.put(def.getId(), def);
                        }
                    }
                }
            } else {
                throw new RuntimeException("分类不存在");
            }
            if (rltClassByCdt != null && !rltClassByCdt.isEmpty()) {
                List<CcCiAttrDef> attrDefs = rltClassByCdt.get(0).getAttrDefs();
                for (CcCiAttrDef def : attrDefs) {
                    rltAttrDefMap.put(def.getId(), def);
                }
            } else {
                throw new RuntimeException("关系不存在");
            }
            // 校验构建条件中的属性定义
            List<ESCiRltAutoBuild.AutoBuildCondition> buildConditions = esCiRltAutoBuild.getBuildConditions();
            for (ESCiRltAutoBuild.AutoBuildCondition buildCondition : buildConditions) {
                Long sourceAttrDefId = buildCondition.getSourceAttrDefId();
                Long targetAttrDefId = buildCondition.getTargetAttrDefId();
                if (!sourceAttrDefMap.containsKey(sourceAttrDefId)) {
                    throw new RuntimeException("属性不存在");
                }
                if (!targetAttrDefMap.containsKey(targetAttrDefId)) {
                    throw new RuntimeException("属性不存在");
                }
                // 设置SourceAttrStdName和TargetAttrStdName
                buildCondition.setSourceAttrStdName(sourceAttrDefMap.get(sourceAttrDefId).getProStdName());
                buildCondition.setTargetAttrStdName(targetAttrDefMap.get(targetAttrDefId).getProStdName());

            }
            // 校验映射字段中的属性定义
            for (ESCiRltAutoBuild.MappingItem item : esCiRltAutoBuild.getMappingItems()) {
                Long ciAttrDefId = item.getCiAttrDefId();
                Long rltAttrDefId = item.getRltAttrDefId();
                Integer sourceOrTarget = item.getSourceOrTarget();
                if (!rltAttrDefMap.containsKey(rltAttrDefId)) {
                    throw new RuntimeException("属性不存在");
                } else {
                    item.setRltAttrDefStdName(rltAttrDefMap.get(rltAttrDefId).getProStdName());
                }
                if (ESCiRltAutoBuild.RelationCiType.SOURCE.equals(ESCiRltAutoBuild.RelationCiType.valueOf(sourceOrTarget))) {
                    if (!sourceAttrDefMap.containsKey(ciAttrDefId)) {
                        throw new RuntimeException("属性不存在");
                    } else {
                        item.setCiAttrDefStdName(sourceAttrDefMap.get(ciAttrDefId).getProStdName());
                    }
                } else if (ESCiRltAutoBuild.RelationCiType.TARGET.equals(ESCiRltAutoBuild.RelationCiType.valueOf(sourceOrTarget))) {
                    if (!targetAttrDefMap.containsKey(ciAttrDefId)) {
                        throw new RuntimeException("属性不存在");
                    } else {
                        item.setCiAttrDefStdName(targetAttrDefMap.get(ciAttrDefId).getProStdName());
                    }
                } else {
                    throw new RuntimeException("属性不存在");
                }
            }
        } catch (Exception e) {
            log.error("", e);
            throw e;
        }
    }

   /* private void clearRltData(ESCiRltAutoBuild esCiRltAutoBuild) {
        iciRltSvc.delRlts(Collections.singletonList(esCiRltAutoBuild.getRltClassId()), esCiRltAutoBuild.getSourceCiClassIds(), esCiRltAutoBuild.getTargetCiClassIds());
    }*/

    private void buildData(ESCiRltAutoBuild esCiRltAutoBuild) throws Exception {
        ESCISearchBean searchBean = new ESCISearchBean();
        searchBean.setDomainId(esCiRltAutoBuild.getDomainId());
        List<Long> classIds = searchBean.getClassIds();
        classIds.add(esCiRltAutoBuild.getSourceCiClassId());
        classIds.add(esCiRltAutoBuild.getTargetCiClassId());
        classIds.addAll(esCiRltAutoBuild.getSourceCiClassIds());
        classIds.addAll(esCiRltAutoBuild.getTargetCiClassIds());
        Map<Long, Long> countMap = iciSvc.countCiNumGroupClsByQuery(searchBean);
        AtomicReference<Long> sourceCiCount = new AtomicReference<>(0L);
        AtomicReference<Long> targetCiCount = new AtomicReference<>(0L);
        countMap.forEach((classId, count) -> {
            if (esCiRltAutoBuild.getSourceCiClassIds().contains(classId)) {
                sourceCiCount.updateAndGet(v -> v + count);
            } else {
                targetCiCount.updateAndGet(v -> v + count);
            }
        });

        // 构建数据
        if (sourceCiCount.get() > targetCiCount.get()) {
            autoBuild(esCiRltAutoBuild, esCiRltAutoBuild.getTargetCiClassIds(), esCiRltAutoBuild.getSourceCiClassIds(), ESCiRltAutoBuild.RelationCiType.TARGET);
        } else {
            autoBuild(esCiRltAutoBuild, esCiRltAutoBuild.getSourceCiClassIds(), esCiRltAutoBuild.getTargetCiClassIds(), ESCiRltAutoBuild.RelationCiType.SOURCE);
        }
    }

    /**
     * 遍历两个分类下所有CI，开始构建关系数据
     * 外层遍历小数据量分类，减少查询次数
     *
     * @param ciRltAutoBuild
     * @param minCiClassIds  小数据量分类ID
     * @param maxCiClassIds  大数据量分类ID
     * @param minType        小数据量分类类型（源或目标）
     * @return
     * @throws Exception
     */
    private void autoBuild(ESCiRltAutoBuild ciRltAutoBuild, Set<Long> minCiClassIds, Set<Long> maxCiClassIds, ESCiRltAutoBuild.RelationCiType minType) throws Exception {
        Long domainId = ciRltAutoBuild.getDomainId();
        Set<BindCiRltRequestDto> bindCiRltRequestDtos = new HashSet<>();
        Map<Long, ESCIInfo> idCiMap = new HashMap<>();
        Integer pageSize = 1000;
        int minPageNum = 1;
        ESCISearchBean minSearch = new ESCISearchBean();
        minSearch.setDomainId(domainId);
        minSearch.setPageSize(pageSize);
        if (LibTypeUtil.isPrivate()) {
            String loginCode = SysUtil.getCurrentUserInfo().getLoginCode();
            minSearch.setOwnerCode(loginCode);
        }
        minSearch.getClassIds().addAll(minCiClassIds);
        while (true) {
            minSearch.setPageNum(minPageNum);
            Page<ESCIInfo> minCiInfoPage = iciSvc.searchESCIByBean(minSearch);
            List<ESCIInfo> minCiList = minCiInfoPage.getData();

            if (minCiList != null && minCiList.size() != 0) {

                //构建minCiList比对结果
                Map<Long, ESCIInfo> minCiMap = new HashMap<>();
                Map<Long, List<List<String>>> minValSplitMap = new HashMap<>();
                for (ESCIInfo esciInfo : minCiList) {
                    List<List<String>> valSplitList = new ArrayList<>();
                    valSplit(ciRltAutoBuild, ESCiRltAutoBuild.RelationCiType.SOURCE.equals(minType), esciInfo, valSplitList);
                    minValSplitMap.put(esciInfo.getId(), valSplitList);
                    minCiMap.put(esciInfo.getId(), esciInfo);
                }

                int maxPageNum = 1;
                ESCISearchBean maxSearch = new ESCISearchBean();
                maxSearch.setDomainId(domainId);
                maxSearch.getClassIds().addAll(maxCiClassIds);
                maxSearch.setPageSize(pageSize);
                if (LibTypeUtil.isPrivate()) {
                    String loginCode = SysUtil.getCurrentUserInfo().getLoginCode();
                    maxSearch.setOwnerCode(loginCode);
                }
                while (true) {
                    maxSearch.setPageNum(maxPageNum);
                    Page<ESCIInfo> maxCiInfoPage = iciSvc.searchESCIByBean(maxSearch);
                    List<ESCIInfo> maxCiList = maxCiInfoPage.getData();
                    if (maxCiList != null && maxCiList.size() > 0) {
                        for (Map.Entry<Long, List<List<String>>> entry : minValSplitMap.entrySet()) {
                            Long minCiId = entry.getKey();
                            List<List<String>> minValSplitList = entry.getValue();
                            for (ESCIInfo maxCIInfo : maxCiList) {
                                List<List<String>> maxValSplitList = new ArrayList<>();
                                valSplit(ciRltAutoBuild, !ESCiRltAutoBuild.RelationCiType.SOURCE.equals(minType), maxCIInfo, maxValSplitList);

                                if (equals(minValSplitList, maxValSplitList)) {
                                    //根据源孪生体id、关系id、目标孪生体id进行加锁，防止数据重复
                                    String lockName = "ciRltUniqueCode_" + minCiId + "_" + ciRltAutoBuild.getRltClassId() + "_" + maxCIInfo.getId();
                                    try {
                                        lockUtil.tryLock(lockName, true);
                                        // 建立关系
                                        idCiMap.put(maxCIInfo.getId(), maxCIInfo);
                                        idCiMap.put(minCiId, minCiMap.get(minCiId));
                                        BindCiRltRequestDto bindCiRltRequestDto;
                                        if (ESCiRltAutoBuild.RelationCiType.SOURCE.equals(minType)) {
                                            bindCiRltRequestDto = buildRecord(ciRltAutoBuild.getMappingItems(), minCiMap.get(minCiId), maxCIInfo, ciRltAutoBuild.getRltClassId());
                                        } else {
                                            bindCiRltRequestDto = buildRecord(ciRltAutoBuild.getMappingItems(), maxCIInfo, minCiMap.get(minCiId), ciRltAutoBuild.getRltClassId());
                                        }
                                        if (LibTypeUtil.isPrivate()) {
                                            String loginCode = SysUtil.getCurrentUserInfo().getLoginCode();
                                            bindCiRltRequestDto.setOwnerCode(loginCode);
                                        }
                                        bindCiRltRequestDto.setRltSourceId(RltSourceId.VIS_MODEL_BUILD_AOTO);
                                        bindCiRltRequestDtos.add(bindCiRltRequestDto);
                                    } catch (Exception ex) {
                                        ex.printStackTrace();
                                        throw ex;
                                    } finally {
                                        //释放锁
                                        lockUtil.unLock(lockName);
                                    }
                                }
                                if (bindCiRltRequestDtos.size() > 1000) {
                                    ImportResultMessage importResultMessage = iciRltSvc.bindCiRlts(domainId, bindCiRltRequestDtos, true, idCiMap, null);
                                    log.debug("batch process size:{}", bindCiRltRequestDtos.size());
                                    bindCiRltRequestDtos = new HashSet<>();
                                    idCiMap = new HashMap<>();
                                }
                            }
                        }
                        maxPageNum++;
                    } else {
                        break;
                    }
                }

                minPageNum++;
            } else {
                break;
            }
        }
        if (bindCiRltRequestDtos.size() > 0) {
            ImportResultMessage importResultMessage = iciRltSvc.bindCiRlts(domainId, bindCiRltRequestDtos, true, idCiMap, null);
            log.debug("batch process size:{}", bindCiRltRequestDtos.size());
        }
    }

    private void valSplit(ESCiRltAutoBuild ciRltAutoBuild, boolean isSource, ESCIInfo esciInfo, List<List<String>> valSplitList) throws Exception {
        Map<String, Object> attrs = esciInfo.getAttrs();
        for (ESCiRltAutoBuild.AutoBuildCondition buildCondition : ciRltAutoBuild.getBuildConditions()) {
            List<String> valSplit = null;
            String sourceVal = isSource ? (attrs.get(buildCondition.getSourceAttrStdName()) == null ? null : attrs.get(buildCondition.getSourceAttrStdName()).toString()) : (attrs.get(buildCondition.getTargetAttrStdName()) == null ? null : attrs.get(buildCondition.getTargetAttrStdName()).toString());
            if (sourceVal != null && buildCondition.getSourceAttrDefScript() != null && !buildCondition.getSourceAttrDefScript().equals("")) {
                valSplit = handleData(sourceVal, buildCondition.getSourceAttrDefScript());
            } else {
                valSplit = Collections.singletonList(sourceVal);
            }
            valSplitList.add(valSplit);
        }
    }

    /**
     * 使用符合自动构建条件的两个CI构建关系数据
     *
     * @param itemList 字段映射关系
     *                 关系属性仅包含mapping中映射的属性
     */
    private BindCiRltRequestDto buildRecord(List<ESCiRltAutoBuild.MappingItem> itemList, ESCIInfo sourceCi, ESCIInfo targetCi, Long rltClassId) {
        BindCiRltRequestDto bindCiRltRequestDto = new BindCiRltRequestDto();
        bindCiRltRequestDto.setRltClassId(rltClassId);
        bindCiRltRequestDto.setSourceCiId(sourceCi.getId());
        bindCiRltRequestDto.setTargetCiId(targetCi.getId());
        Map<String, String> attrs = new HashMap<>();
        for (ESCiRltAutoBuild.MappingItem mappingItem : itemList) {
            String ciAttrDefStdName = mappingItem.getCiAttrDefStdName();
            String rltAttrDefStdName = mappingItem.getRltAttrDefStdName();
            Integer sourceOrTarget = mappingItem.getSourceOrTarget();
            if (sourceOrTarget.equals(ESCiRltAutoBuild.RelationCiType.SOURCE.getType()) && sourceCi.getAttrs().get(ciAttrDefStdName) != null) {
                attrs.put(rltAttrDefStdName, sourceCi.getAttrs().get(ciAttrDefStdName).toString());
            } else if (sourceOrTarget.equals(ESCiRltAutoBuild.RelationCiType.TARGET.getType()) && targetCi.getAttrs().get(ciAttrDefStdName) != null) {
                attrs.put(rltAttrDefStdName, targetCi.getAttrs().get(ciAttrDefStdName).toString());
            }
        }
        bindCiRltRequestDto.setAttrs(attrs);
        return bindCiRltRequestDto;
    }


    /**
     * 比较两个CI是否符合自动构建规则
     *
     * @param sourceAttrs        源属性值
     * @param targetAttrs        目标属性值
     * @param buildConditionList 构建条件
     * @return
     * @throws Exception
     */
    private Boolean equals(Map<String, Object> sourceAttrs, Map<String, Object> targetAttrs, List<ESCiRltAutoBuild.AutoBuildCondition> buildConditionList) throws Exception {

        Boolean ret = true;
        for (ESCiRltAutoBuild.AutoBuildCondition buildCondition : buildConditionList) {
            String sourceAttrStdName = buildCondition.getSourceAttrStdName();
            String targetAttrStdName = buildCondition.getTargetAttrStdName();
            if (sourceAttrs.containsKey(sourceAttrStdName) && targetAttrs.containsKey(targetAttrStdName) &&
                    sourceAttrs.get(sourceAttrStdName) != null && targetAttrs.get(targetAttrStdName) != null &&
                    !sourceAttrs.get(sourceAttrStdName).equals("") && !targetAttrs.get(targetAttrStdName).equals("")) {
                String sourceVal = sourceAttrs.get(sourceAttrStdName).toString();
                String targetVal = targetAttrs.get(targetAttrStdName).toString();
                List<String> sourceValSplit = null;
                if (buildCondition.getSourceAttrDefScript() != null && !buildCondition.getSourceAttrDefScript().equals("")) {
                    sourceValSplit = handleData(sourceVal, buildCondition.getSourceAttrDefScript());
                } else {
                    sourceValSplit = Collections.singletonList(sourceVal);
                }
                List<String> targetValSplit = null;
                if (buildCondition.getTargetAttrDefScript() != null && !buildCondition.getTargetAttrDefScript().equals("")) {
                    targetValSplit = handleData(targetVal, buildCondition.getTargetAttrDefScript());
                } else {
                    targetValSplit = Collections.singletonList(targetVal);
                }

                // 符合当前构建规则，当前仅支持且
                Boolean itemFlag = false;
                for (String source : sourceValSplit) {
                    for (String target : targetValSplit) {
                        if (source.equals(target)) {
                            itemFlag = true;
                            break;
                        }
                    }
                }
                if (!itemFlag) {
                    ret = false;
                    break;
                }

            } else {
                ret = false;
                break;
            }

        }
        return ret;

    }

    private Boolean equals(List<List<String>> minValSplitList, List<List<String>> maxValSplitList) {
        for (int i = 0; i < minValSplitList.size(); i++) {
            // 符合当前构建规则，当前仅支持且
            for (String source : minValSplitList.get(i)) {
                if (source == null) {
                    return false;
                }
                for (String target : maxValSplitList.get(i)) {
                    if (source.equals(target)) {
                        return true;
                    }
                }
            }

        }
        return false;
    }


    /**
     * 将属性值通过script转为list，若非split则为singleList
     *
     * @param data       属性值
     * @param dataScript script
     * @return
     * @throws Exception
     */
    private List<String> handleData(String data, String dataScript) throws Exception {
        ScriptEngineManager factory = new ScriptEngineManager();
        ScriptEngine engine = factory.getEngineByName("nashorn");
        engine.put("logger", log);
        engine.put("data", data);
        String script = "function run() {"
                + "	var ArrayList = Java.type(\"java.util.ArrayList\");"
                + " var ls = new ArrayList();"
                + "	try {"
                + "		"
                + dataScript
                + "     if(Array.isArray(data)){ "
                + "         for(i=0;i<data.length;i++){"
                + "             ls.add(data[i]);"
                + "         }"
                + "     } else {"
                + "         ls.add(data);"
                + "     }"
                + "		return ls;"
                + "	} catch (err) {"
                + "		logger.error(err);"
                + "		return null;"
                + "	}"
                + "}";
        engine.eval(script);
        Invocable inv = (Invocable) engine;
        Object ret = inv.invokeFunction("run");
        return (List<String>) ret;
    }
}
