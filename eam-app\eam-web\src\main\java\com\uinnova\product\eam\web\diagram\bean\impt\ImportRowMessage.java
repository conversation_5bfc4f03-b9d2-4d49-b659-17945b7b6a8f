package com.uinnova.product.eam.web.diagram.bean.impt;

import java.util.List;
import java.util.Map;

public class ImportRowMessage {
	private Integer rowNum;
	private List<ImportCellMessage> cellMessages;
	private Map<String, String> attrs;
	public Integer getRowNum() {
		return rowNum;
	}

	public void setRowNum(Integer rowNum) {
		this.rowNum = rowNum;
	}

	public List<ImportCellMessage> getMessageItems() {
		return cellMessages;
	}

	public void setMessageItems(List<ImportCellMessage> messageItems) {
		this.cellMessages = messageItems;
	}

	public Map<String, String> getAttrs() {
		return attrs;
	}

	public void setAttrs(Map<String, String> attrs) {
		this.attrs = attrs;
	}

}
