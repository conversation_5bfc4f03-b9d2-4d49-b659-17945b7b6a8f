package com.uinnova.product.vmdb.provider.ci.bean;

import com.alibaba.fastjson.JSONArray;
import com.binary.json.JSON;
import com.uinnova.product.vmdb.comm.model.ci.CcCi;

import java.util.*;

/**
 * 一个简易的追踪业务主键变更的工具
 * 
 * <AUTHOR>
 *
 */
public class CiChangeTracker {

    private Map<Long, List<CcCi>> map = new HashMap<Long, List<CcCi>>();

    public CiChangeTracker() {
    }

    public boolean equalPrimaryKey(Long ciId) {
        CcCi oldCi = getOldCi(ciId);
        CcCi newCi = getNewCi(ciId);
        if (oldCi == null || newCi == null) { return false; }
        if (oldCi.getCiPrimaryKey() == null || oldCi.getCiPrimaryKey() == null) { return false; }
        return oldCi.getCiPrimaryKey().equals(newCi.getCiPrimaryKey());
    }

    public boolean equalPrimaryKey() {
        return equalPrimaryKey(null);
    }

    public String getNewCiPrimaryKey(Long ciId) {
        return getNewCi(ciId).getCiPrimaryKey();
    }

    public List<String> getNewCiPrimaryKeyJSON(Long ciId) {
        return JSON.toList(getNewCi(ciId).getCiPrimaryKey(), String.class);
    }

    public CcCi getOldCi(Long ciId) {
        return map.containsKey(ciId) ? map.get(ciId).get(0) : null;
    }

    /**
     * #com.uinnova.product.es.ESService.updateCiRltCiPrimaryKeys(JSONArray)
     * 
     * @param ciId
     * @return [{'id':2132,'ciPrimaryKey':['aa','bbb']}]
     */
    public JSONArray toCdt(Long ciId) {
        JSONArray ret = new JSONArray();
        List<CcCi> list = map.get(ciId);
        if (list == null) { return ret; }
        Map<String, Object> jsonObject = new HashMap<String, Object>();
        jsonObject.put("id", getOldCi(ciId).getId());
        jsonObject.put("ciPrimaryKey", JSONArray.parse(getNewCi(ciId).getCiPrimaryKey()));
        ret.add(jsonObject);
        return ret;
    }

    public JSONArray toCdt() {
        JSONArray ret = new JSONArray();
        if (map.size() == 0) { return ret; }
        Set<Long> keySet = map.keySet();
        for (Long ciId : keySet) {
            if (getOldCi(ciId) == null || getNewCi(ciId) == null) {
                continue;
            }
            Map<String, Object> jsonObject = new HashMap<String, Object>();
            jsonObject.put("id", getOldCi(ciId).getId());
            jsonObject.put("ciPrimaryKey", JSONArray.parse(getNewCi(ciId).getCiPrimaryKey()));
            ret.add(jsonObject);
        }
        return ret;
    }

    public JSONArray toCdtWhenPKChange() {
        JSONArray ret = new JSONArray();
        if (map.size() == 0) { return ret; }
        Set<Long> keySet = map.keySet();
        for (Long ciId : keySet) {
            if (!equalPrimaryKey(ciId)) {
                if (getOldCi(ciId) == null || getNewCi(ciId) == null) {
                    continue;
                }
                Map<String, Object> jsonObject = new HashMap<String, Object>();
                jsonObject.put("id", getOldCi(ciId).getId());
                jsonObject.put("ciPrimaryKey", JSONArray.parse(getNewCi(ciId).getCiPrimaryKey()));
                ret.add(jsonObject);
            }
        }
        return ret;
    }

    public void setOldCi(Long id, CcCi oldCi) {
        List<CcCi> list = map.get(id);
        if (list == null) {
            list = new ArrayList<CcCi>();
            list.add(null);
            list.add(null);
            map.put(id, list);
        }
        list.set(0, oldCi);
    }

    public CcCi getNewCi(Long ciId) {
        return map.containsKey(ciId) ? map.get(ciId).get(1) : null;
    }

    public void setNewCi(Long id, CcCi newCi) {
        List<CcCi> list = map.get(id);
        if (list == null) {
            list = new ArrayList<CcCi>();
            list.add(null);
            list.add(null);
            map.put(id, list);
        }
        list.set(1, newCi);
    }

    public void setOldCi(CcCi oldCi) {
        setOldCi(null, oldCi);
    }

    public void setNewCi(CcCi newCi) {
        setNewCi(null, newCi);
    }
}
