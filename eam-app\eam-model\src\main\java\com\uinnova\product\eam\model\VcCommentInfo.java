package com.uinnova.product.eam.model;

import com.uinnova.product.eam.comm.model.VcComment;
import com.uinnova.product.eam.comm.model.VcDiagram;
import com.uino.bean.permission.base.SysUser;

import java.io.Serializable;
import java.util.List;

public class VcCommentInfo implements Serializable{
	private static final long serialVersionUID = 1L;
	
	
	/**
	 * 视图信息
	 */
	private VcDiagram diagram;
	
	
	
	/**
	 * 评论信息
	 */
	private List<VcComment> comments;
	
	
	/**
	 * 评论用户信息
	 */
	private List<SysUser> ops;


	public VcDiagram getDiagram() {
		return diagram;
	}


	public void setDiagram(VcDiagram diagram) {
		this.diagram = diagram;
	}


	public List<VcComment> getComments() {
		return comments;
	}


	public void setComments(List<VcComment> comments) {
		this.comments = comments;
	}


	public List<SysUser> getOps() {
		return ops;
	}


	public void setOps(List<SysUser> ops) {
		this.ops = ops;
	}





}
