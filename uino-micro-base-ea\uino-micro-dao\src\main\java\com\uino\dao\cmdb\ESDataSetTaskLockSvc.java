package com.uino.dao.cmdb;

import com.alibaba.fastjson.JSONObject;
import com.uino.dao.AbstractESBaseDao;
import com.uino.dao.ESConst;
import com.uino.bean.cmdb.base.ESTaskLock;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.stereotype.Repository;

import jakarta.annotation.PostConstruct;

/**
 * 任务锁使用的索引
 *
 * <AUTHOR>
 * @version 2020-4-24
 */
@Repository
public class ESDataSetTaskLockSvc extends AbstractESBaseDao<ESTaskLock, JSONObject> {

    Log logger = LogFactory.getLog(ESDataSetTaskLockSvc.class);

    @Override
    public String getIndex() {
        return ESConst.INDEX_CMDB_DATASET_TASK_LOCK;
    }

    @Override
    public String getType() {
        return ESConst.INDEX_CMDB_DATASET_TASK_LOCK;
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }
}

