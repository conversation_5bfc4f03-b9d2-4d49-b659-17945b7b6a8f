package com.uino.bean.license;

import com.uinnova.product.vmdb.comm.model.license.CcLicenseAuthServer;
import lombok.Data;

import java.util.List;

/**
 * description
 *
 * <AUTHOR>
 * @since 2022/4/28 14:47
 */
@Data
public class BaseLicenseAuthInfo {

    /**
     * 许可证授权信息
     */
    private BaseLicenseAuth auth;

    /**
     * 安装服务器信息
     */
    private List<CcLicenseAuthServer> serverList;

    /** 状态, 1=已授权    2=未授权    3=已过期 **/
    private Integer status;
}
