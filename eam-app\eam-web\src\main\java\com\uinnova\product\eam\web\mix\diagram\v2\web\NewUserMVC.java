package com.uinnova.product.eam.web.mix.diagram.v2.web;

import com.binary.framework.web.RemoteResult;
import com.uinnova.product.eam.base.diagram.model.NewUserRecord;
import com.uinnova.product.eam.web.mix.diagram.v2.peer.SysPeer;
import com.uino.util.sys.SysUtil;
import com.uino.bean.permission.base.SysUser;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.List;

@RestController
@RequestMapping("/eam/user")
public class NewUserMVC {

    @Resource
    private SysPeer sysPeer;

    @CrossOrigin(origins = {})
    @PostMapping("/getOlderUser")
    public RemoteResult getIsOlderUser() {
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        Long userId = currentUserInfo.getId();
        List<NewUserRecord> result = sysPeer.getIsOlderUser(userId);
        if (CollectionUtils.isEmpty(result)) {
            return new RemoteResult(new NewUserRecord());
        } else {
            NewUserRecord record = result.get(0);
            return new RemoteResult(record);
        }
    }
    @PostMapping("/setOlderUser")
    public RemoteResult setIsOlderUser(@RequestBody String jsonStr, HttpServletRequest request,
                                       HttpServletResponse response) {
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        Long userId = currentUserInfo.getId();
        cn.hutool.json.JSONObject jsonObj = new cn.hutool.json.JSONObject(jsonStr);
        return new RemoteResult(sysPeer.setIsOlderUser(userId, jsonObj));
    }
}
