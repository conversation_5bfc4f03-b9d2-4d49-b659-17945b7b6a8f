# 是否开启配置中心
spring.cloud.nacos.config.enabled=true
# 是否开启服务注册远程调用
spring.cloud.nacos.discovery.enabled=true
# nacos服务地址
spring.cloud.nacos.config.server-addr=192.168.21.223:8848
# 是否开启流量监控
feign.sentinel.enabled=false
# sentinel服务地址
spring.cloud.sentinel.transport.dashboard=192.168.21.223:8145
# sentinel回调本地端口
spring.cloud.sentinel.transport.port=8245


# 主配置文件 (data-id 根据部署实例修改, 其他无须修改)
spring.cloud.nacos.config.extension-configs[0].data-id=common-server-first.properties
spring.cloud.nacos.config.extension-configs[0].group=common
spring.cloud.nacos.config.extension-configs[0].refresh=true
# ES配置文件(该配置模块无须修改)
spring.cloud.nacos.config.extension-configs[1].data-id=es-config.properties
spring.cloud.nacos.config.extension-configs[1].group=common
spring.cloud.nacos.config.extension-configs[1].refresh=true
# 共配置文件(该配置模块无须修改)
spring.cloud.nacos.config.extension-configs[2].data-id=common-server-basic.properties
spring.cloud.nacos.config.extension-configs[2].group=common
spring.cloud.nacos.config.extension-configs[2].refresh=true
# 默认配置项(该配置模块无须修改)
spring.application.name=tarsier-platform
spring.cloud.nacos.discovery.group=middle-platform
spring.cloud.nacos.config.group=middle-platform
spring.cloud.nacos.discovery.server-addr=${spring.cloud.nacos.config.server-addr}