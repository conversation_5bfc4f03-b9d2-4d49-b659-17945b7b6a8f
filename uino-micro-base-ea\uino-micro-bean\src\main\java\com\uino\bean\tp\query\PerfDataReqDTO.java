package com.uino.bean.tp.query;

import com.binary.core.util.BinaryUtils;
import com.uino.bean.permission.business.IValidDto;
import lombok.Data;
import org.springframework.util.Assert;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.List;

/**
 * 查询性能数据DTO
 * @author: weixuesong
 * @data: 2020/09/14 14:57
 **/
@Data
public class PerfDataReqDTO implements IValidDto {
    private String ciCode;
    private List<String> metrics;
    /**
     * 聚合时间维度，单位为分钟
     */
    private Integer interval;
    private Long startTime;
    private Long endTime;
    /**
     * 属性名
     */
    private String attrKey;
    /**
     * 属性值
     */
    private String attrValue;

    private Integer pageSize;
    private Integer pageNum;
    /**
     * 是否聚合，默认不聚合
     */
    private Boolean aggFlag = Boolean.FALSE;

    @Override
    public void valid() {
        Assert.isTrue(!BinaryUtils.isEmpty(ciCode) || !BinaryUtils.isEmpty(attrKey), "ciCode和attrKey不能同时为空");
        Assert.notEmpty(metrics, "metric不能为空");
        Assert.isTrue(startTime != null && endTime != null && endTime >= startTime, "startTime和endTime不能为空并且startTime不能大于endTime");
        LocalDateTime start = LocalDateTime.ofInstant(Instant.ofEpochMilli(startTime), ZoneId.systemDefault());
        LocalDateTime end = LocalDateTime.ofInstant(Instant.ofEpochMilli(endTime), ZoneId.systemDefault());
        if (ChronoUnit.YEARS.between(start, end) >= 1) {
            throw new IllegalArgumentException("查询时间不能超过一年");
        }
        Assert.isTrue((!BinaryUtils.isEmpty(attrKey) && !BinaryUtils.isEmpty(attrValue)) || BinaryUtils.isEmpty(attrKey), "attrKey不为空时attrValue必须不为空");
        if ((Boolean.FALSE == aggFlag) && (pageSize == null || pageNum == null)) {
            throw new IllegalArgumentException("查询原始数据时pageSize和pageNum不能为空");
        }
        if (Boolean.TRUE == aggFlag && interval == null) {
            throw new IllegalArgumentException("查询聚合数据时interval不能为空");
        }
    }

}
