package com.uinnova.product.eam.model.cj.vo;

import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;

public class MoveDirAndDiagramCdtVO implements Condition {

	private static final long serialVersionUID = 1L;

	@Comment("要移动到的目录id")
	private Long targetDirId;
	@Comment("要移动的目录")
	private Long[] dirIds;
	@Comment("要移动的视图")
	private String[] diagramIds;
	@Comment("要移动到的SubjectId")
	private Long targetSubjectId;
	@Comment("要移动的Subject")
	private Long[] SubjectIds;
	@Comment("文件夹类型")
	private Integer dirType;

	public Long getTargetSubjectId() {
		return targetSubjectId;
	}

	public void setTargetSubjectId(Long targetSubjectId) {
		this.targetSubjectId = targetSubjectId;
	}

	public Long[] getSubjectIds() {
		return SubjectIds;
	}

	public void setSubjectIds(Long[] subjectIds) {
		SubjectIds = subjectIds;
	}

	public Long getTargetDirId() {
		return targetDirId;
	}

	public void setTargetDirId(Long targetDirId) {
		this.targetDirId = targetDirId;
	}

	public Long[] getDirIds() {
		return dirIds;
	}

	public void setDirIds(Long[] dirIds) {
		this.dirIds = dirIds;
	}

	public String[] getDiagramIds() {
		return diagramIds;
	}

	public void setDiagramIds(String[] diagramIds) {
		this.diagramIds = diagramIds;
	}

	public Integer getDirType() {
		return dirType;
	}

	public void setDirType(Integer dirType) {
		this.dirType = dirType;
	}
}
