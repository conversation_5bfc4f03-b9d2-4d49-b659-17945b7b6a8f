package com.uino.dao.permission.rlt;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.binary.core.util.BinaryUtils;
import com.uino.bean.permission.base.SysOrgRoleRlt;
import com.uino.bean.permission.base.SysUserOrgRlt;
import com.uino.bean.permission.base.SysUserRoleRlt;

import lombok.extern.slf4j.Slf4j;

/**
 * 权限共用服务
 * 
 * <AUTHOR>
 */
@Service
@Slf4j
public class ESPerssionCommSvc {
    @Autowired
    private ESUserOrgRltSvc userOrgRltSvc;

    @Autowired
    private ESUserRoleRltSvc userRoleRltSvc;

    @Autowired
    private ESOrgRoleRltSvc orgRoleRltSvc;

    public Integer saveUseOrgRltBatch(List<SysUserOrgRlt> list) {
        return userOrgRltSvc.saveOrUpdateBatch(list);
    }

    public List<SysUserOrgRlt> getUserOrgRltByUserIds(Set<Long> userIds) {
        return userOrgRltSvc.getListByQueryScroll(QueryBuilders.boolQuery().must(QueryBuilders.termsQuery("userId", userIds)));
    }

    public List<SysUserOrgRlt> getUserOrgRltByOrgIds(Set<Long> orgIds) {
        return userOrgRltSvc.getListByQueryScroll(QueryBuilders.boolQuery().must(QueryBuilders.termsQuery("orgId", orgIds)));
    }

    public Integer deleteUserOrgRltByQuery(QueryBuilder query, boolean isRefresh) {
        return userOrgRltSvc.deleteByQuery(query, isRefresh);
    }

    public Integer saveOrgRoleRltBatch(List<SysOrgRoleRlt> list) {
        return orgRoleRltSvc.saveOrUpdateBatch(list);
    }

    public List<SysOrgRoleRlt> getOrgRoleRltByOrgIds(Set<Long> orgIds) {
        return orgRoleRltSvc.getListByQueryScroll(QueryBuilders.boolQuery().must(QueryBuilders.termsQuery("orgId", orgIds)));

    }

    public List<SysOrgRoleRlt> getOrgRoleRltByRoleIds(Set<Long> roleIds) {
        return orgRoleRltSvc.getListByQueryScroll(QueryBuilders.boolQuery().must(QueryBuilders.termsQuery("roleId", roleIds)));
    }

    public Integer deleteOrgRoleRltByQuery(QueryBuilder query, boolean isRefresh) {
        return orgRoleRltSvc.deleteByQuery(query, isRefresh);
    }

    public Integer saveUserRoleRltBatch(List<SysUserRoleRlt> list) {
        return userRoleRltSvc.saveOrUpdateBatch(list);
    }

    public List<SysUserRoleRlt> getUserRoleRltByUserIds(Set<Long> userIds) {
        List<SysUserRoleRlt> res = new ArrayList<>();
        // 获取用户从组织继承的角色
        List<SysUserOrgRlt> userOrgRlts = this.getUserOrgRltByUserIds(userIds);
        List<SysOrgRoleRlt> orgRoleRlts = this.getOrgRoleRltByOrgIds(userOrgRlts.stream().map(SysUserOrgRlt::getOrgId).collect(Collectors.toSet()));
        Map<Long, List<SysOrgRoleRlt>> orgRoleMap = BinaryUtils.toObjectGroupMap(orgRoleRlts, "orgId");
        Map<Long, Set<Long>> userRoleIdsFromOrg = new HashMap<>();
        for (SysUserOrgRlt userOrgRlt : userOrgRlts) {
            List<SysOrgRoleRlt> list = orgRoleMap.get(userOrgRlt.getOrgId());
            if (!BinaryUtils.isEmpty(list)) {
                Long userId = userOrgRlt.getUserId();
                if (userRoleIdsFromOrg.get(userId) == null) {
                    userRoleIdsFromOrg.put(userId, new HashSet<>());
                }
                userRoleIdsFromOrg.get(userId).addAll(list.stream().map(SysOrgRoleRlt::getRoleId).collect(Collectors.toSet()));
            }
        }
        userRoleIdsFromOrg.forEach((userId, roleIds) -> {
            roleIds.forEach(roleId -> res.add(SysUserRoleRlt.builder().roleId(roleId).userId(userId).build()));

        });
        
        //获取用户本身的角色
        List<SysUserRoleRlt> data = userRoleRltSvc.getListByQueryScroll(
            QueryBuilders.boolQuery().must(QueryBuilders.termsQuery("userId", userIds)));
        res.addAll(data);
        return res;
    }

    public List<SysUserRoleRlt> getUserRoleRltByRoleIds(Set<Long> roleIds) {
        List<SysUserRoleRlt> res = new ArrayList<>();
        // 获取拥有当前角色的组织
        List<SysOrgRoleRlt> orgRoleRlts = this.getOrgRoleRltByRoleIds(roleIds);
        // 获取组织下的人
        List<SysUserOrgRlt> userOrgRlts = this.getUserOrgRltByOrgIds(orgRoleRlts.stream().map(SysOrgRoleRlt::getOrgId).collect(Collectors.toSet()));
        Map<Long, List<SysUserOrgRlt>> orgUserMap = BinaryUtils.toObjectGroupMap(userOrgRlts, "orgId");
        // 存储继承于组织的角色-用户关系，用户可能属于多个组织，避免重复
        Map<Long, Set<Long>> roleUserIdsFromOrg = new HashMap<>();
        for (SysOrgRoleRlt orgRoleRlt : orgRoleRlts) {
            List<SysUserOrgRlt> list = orgUserMap.get(orgRoleRlt.getOrgId());
            if (!BinaryUtils.isEmpty(list)) {
                Long roleId = orgRoleRlt.getRoleId();
                if (roleUserIdsFromOrg.get(roleId) == null) {
                    roleUserIdsFromOrg.put(roleId, new HashSet<>());
                }
                roleUserIdsFromOrg.get(roleId).addAll(list.stream().map(SysUserOrgRlt::getUserId).collect(Collectors.toSet()));
            }
        }
        roleUserIdsFromOrg.forEach((roleId, userIds) -> {
            userIds.forEach(userId -> res.add(SysUserRoleRlt.builder().roleId(roleId).userId(userId).build()));

        });
        // 获取角色本身用户
        List<SysUserRoleRlt> data = userRoleRltSvc.getListByQueryScroll(QueryBuilders.boolQuery().must(QueryBuilders.termsQuery("roleId", roleIds)));
        res.addAll(data);
        return res;
    }

    public Integer deleteUserRoleRltByQuery(QueryBuilder query, boolean isRefresh) {
        return userRoleRltSvc.deleteByQuery(query, isRefresh);
    }

}
