package com.uinnova.product.eam.web.analog.bean;

import java.io.Serializable;
import java.util.Map;

import com.binary.framework.bean.annotation.Comment;

@Comment("建行外部数据类")
public class CCBExternalData implements Serializable{

	private static final long serialVersionUID = 1L;

	@Comment("源cicode")
	private String sourceciname;
	
	@Comment("目标cicode")
	private String targetciname;
	
	@Comment("分类名称")
	private String classesname;
	
	@Comment("cicode")
	private String ciname;
	
	@Comment("性能数据")
	private Map<String,Object> kpi;

	public String getSourceciname() {
		return sourceciname;
	}

	public void setSourceciname(String sourceciname) {
		this.sourceciname = sourceciname;
	}

	public String getTargetciname() {
		return targetciname;
	}

	public void setTargetciname(String targetciname) {
		this.targetciname = targetciname;
	}

	public String getClassesname() {
		return classesname;
	}

	public void setClassesname(String classesname) {
		this.classesname = classesname;
	}

	public String getCiname() {
		return ciname;
	}

	public void setCiname(String ciname) {
		this.ciname = ciname;
	}

	public Map<String, Object> getKpi() {
		return kpi;
	}

	public void setKpi(Map<String, Object> kpi) {
		this.kpi = kpi;
	}
	
	
}
