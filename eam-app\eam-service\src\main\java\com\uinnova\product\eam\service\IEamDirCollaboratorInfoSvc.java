package com.uinnova.product.eam.service;

import com.alibaba.fastjson.JSONObject;
import com.uinnova.product.eam.comm.model.VcDiagramDir;

import java.util.List;

/**
 * 文件夹协作者关系接口
 *
 * <AUTHOR>
 */
public interface IEamDirCollaboratorInfoSvc {

    List<JSONObject>  findDirUserById(Long dirId, VcDiagramDir vcDiagramDir);

    Boolean updateCooperation(Long dirId, List<JSONObject> userList, VcDiagramDir vcDiagramDir);
}
