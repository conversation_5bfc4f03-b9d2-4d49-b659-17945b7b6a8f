package com.uinnova.product.eam.web.eam.mvc.diagram;

import com.binary.framework.web.RemoteResult;
import com.uinnova.product.eam.comm.model.es.CEamRecentlyView;
import com.uinnova.product.eam.comm.model.es.EamRecentlyView;
import com.uinnova.product.eam.service.asset.IRecentlyViewSvc;
import com.uino.util.sys.SysUtil;
import com.uino.bean.permission.base.SysUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/eam/recently/view")
public class RecentlyViewMvc {

    @Autowired
    private IRecentlyViewSvc recentlyViewSvc;

    @PostMapping("/saveRecentlyView")
    public RemoteResult saveRecentlyView(@RequestBody CEamRecentlyView ceamRecentlyView) {
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        ceamRecentlyView.setUserId(currentUserInfo.getId());
        Long result = recentlyViewSvc.saveOrUpdateRecentlyView(ceamRecentlyView);
        return new RemoteResult(result);
    }

}
