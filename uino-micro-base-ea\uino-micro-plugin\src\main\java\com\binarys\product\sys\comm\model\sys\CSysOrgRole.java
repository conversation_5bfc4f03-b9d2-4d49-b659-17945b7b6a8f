package com.binarys.product.sys.comm.model.sys;




import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;


@Comment("组织角色表[SYS_ORG_ROLE]")
public class CSysOrgRole implements Condition {
	private static final long serialVersionUID = 1L;


	@Comment("ID[ID] operate-Equal[=]")
	private Long id;


	@Comment("ID[ID] operate-In[in]")
	private Long[] ids;


	@Comment("ID[ID] operate-GTEqual[>=]")
	private Long startId;

	@Comment("ID[ID] operate-LTEqual[<=]")
	private Long endId;


	@Comment("角色表_ID[ROLE_ID] operate-Equal[=]    角色ID")
	private Long roleId;


	@Comment("角色表_ID[ROLE_ID] operate-In[in]    角色ID")
	private Long[] roleIds;


	@Comment("角色表_ID[ROLE_ID] operate-GTEqual[>=]    角色ID")
	private Long startRoleId;

	@Comment("角色表_ID[ROLE_ID] operate-LTEqual[<=]    角色ID")
	private Long endRoleId;


	@Comment("组织ID[ORG_ID] operate-Equal[=]")
	private Long orgId;


	@Comment("组织ID[ORG_ID] operate-In[in]")
	private Long[] orgIds;


	@Comment("组织ID[ORG_ID] operate-GTEqual[>=]")
	private Long startOrgId;

	@Comment("组织ID[ORG_ID] operate-LTEqual[<=]")
	private Long endOrgId;


	@Comment("直属标志[DIRECT_FLAG] operate-Equal[=]    直属标志:直属标志：1-直属 0-非直属")
	private Integer directFlag;


	@Comment("直属标志[DIRECT_FLAG] operate-In[in]    直属标志:直属标志：1-直属 0-非直属")
	private Integer[] directFlags;


	@Comment("直属标志[DIRECT_FLAG] operate-GTEqual[>=]    直属标志:直属标志：1-直属 0-非直属")
	private Integer startDirectFlag;

	@Comment("直属标志[DIRECT_FLAG] operate-LTEqual[<=]    直属标志:直属标志：1-直属 0-非直属")
	private Integer endDirectFlag;


	@Comment("管理员标志[ADMIN_FLAG] operate-Equal[=]    管理员标志:管理员标志：1-是 0-否")
	private Integer adminFlag;


	@Comment("管理员标志[ADMIN_FLAG] operate-In[in]    管理员标志:管理员标志：1-是 0-否")
	private Integer[] adminFlags;


	@Comment("管理员标志[ADMIN_FLAG] operate-GTEqual[>=]    管理员标志:管理员标志：1-是 0-否")
	private Integer startAdminFlag;

	@Comment("管理员标志[ADMIN_FLAG] operate-LTEqual[<=]    管理员标志:管理员标志：1-是 0-否")
	private Integer endAdminFlag;


	@Comment("所属域[DOMAIN_ID] operate-Equal[=]")
	private Long domainId;


	@Comment("所属域[DOMAIN_ID] operate-In[in]")
	private Long[] domainIds;


	@Comment("所属域[DOMAIN_ID] operate-GTEqual[>=]")
	private Long startDomainId;

	@Comment("所属域[DOMAIN_ID] operate-LTEqual[<=]")
	private Long endDomainId;


	@Comment("所属用户域[USER_DOMAIN_ID] operate-Equal[=]")
	private Long userDomainId;


	@Comment("所属用户域[USER_DOMAIN_ID] operate-In[in]")
	private Long[] userDomainIds;


	@Comment("所属用户域[USER_DOMAIN_ID] operate-GTEqual[>=]")
	private Long startUserDomainId;

	@Comment("所属用户域[USER_DOMAIN_ID] operate-LTEqual[<=]")
	private Long endUserDomainId;


	@Comment("数据状态[DATA_STATUS] operate-Equal[=]    数据状态:数据状态：1-正常 0-删除")
	private Integer dataStatus;


	@Comment("数据状态[DATA_STATUS] operate-In[in]    数据状态:数据状态：1-正常 0-删除")
	private Integer[] dataStatuss;


	@Comment("数据状态[DATA_STATUS] operate-GTEqual[>=]    数据状态:数据状态：1-正常 0-删除")
	private Integer startDataStatus;

	@Comment("数据状态[DATA_STATUS] operate-LTEqual[<=]    数据状态:数据状态：1-正常 0-删除")
	private Integer endDataStatus;


	@Comment("创建人[CREATOR] operate-Like[like]")
	private String creator;


	@Comment("创建人[CREATOR] operate-Equal[=]")
	private String creatorEqual;


	@Comment("创建人[CREATOR] operate-In[in]")
	private String[] creators;


	@Comment("修改人[MODIFIER] operate-Like[like]")
	private String modifier;


	@Comment("修改人[MODIFIER] operate-Equal[=]")
	private String modifierEqual;


	@Comment("修改人[MODIFIER] operate-In[in]")
	private String[] modifiers;


	@Comment("创建时间[CREATE_TIME] operate-Equal[=]    创建时间:yyyyMMddHHmmss")
	private Long createTime;


	@Comment("创建时间[CREATE_TIME] operate-In[in]    创建时间:yyyyMMddHHmmss")
	private Long[] createTimes;


	@Comment("创建时间[CREATE_TIME] operate-GTEqual[>=]    创建时间:yyyyMMddHHmmss")
	private Long startCreateTime;

	@Comment("创建时间[CREATE_TIME] operate-LTEqual[<=]    创建时间:yyyyMMddHHmmss")
	private Long endCreateTime;


	@Comment("修改时间[MODIFY_TIME] operate-Equal[=]    修改时间:yyyyMMddHHmmss")
	private Long modifyTime;


	@Comment("修改时间[MODIFY_TIME] operate-In[in]    修改时间:yyyyMMddHHmmss")
	private Long[] modifyTimes;


	@Comment("修改时间[MODIFY_TIME] operate-GTEqual[>=]    修改时间:yyyyMMddHHmmss")
	private Long startModifyTime;

	@Comment("修改时间[MODIFY_TIME] operate-LTEqual[<=]    修改时间:yyyyMMddHHmmss")
	private Long endModifyTime;




	public Long getId() {
		return this.id;
	}
	public void setId(Long id) {
		this.id = id;
	}


	public Long[] getIds() {
		return this.ids;
	}
	public void setIds(Long[] ids) {
		this.ids = ids;
	}


	public Long getStartId() {
		return this.startId;
	}
	public void setStartId(Long startId) {
		this.startId = startId;
	}


	public Long getEndId() {
		return this.endId;
	}
	public void setEndId(Long endId) {
		this.endId = endId;
	}


	public Long getRoleId() {
		return this.roleId;
	}
	public void setRoleId(Long roleId) {
		this.roleId = roleId;
	}


	public Long[] getRoleIds() {
		return this.roleIds;
	}
	public void setRoleIds(Long[] roleIds) {
		this.roleIds = roleIds;
	}


	public Long getStartRoleId() {
		return this.startRoleId;
	}
	public void setStartRoleId(Long startRoleId) {
		this.startRoleId = startRoleId;
	}


	public Long getEndRoleId() {
		return this.endRoleId;
	}
	public void setEndRoleId(Long endRoleId) {
		this.endRoleId = endRoleId;
	}


	public Long getOrgId() {
		return this.orgId;
	}
	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}


	public Long[] getOrgIds() {
		return this.orgIds;
	}
	public void setOrgIds(Long[] orgIds) {
		this.orgIds = orgIds;
	}


	public Long getStartOrgId() {
		return this.startOrgId;
	}
	public void setStartOrgId(Long startOrgId) {
		this.startOrgId = startOrgId;
	}


	public Long getEndOrgId() {
		return this.endOrgId;
	}
	public void setEndOrgId(Long endOrgId) {
		this.endOrgId = endOrgId;
	}


	public Integer getDirectFlag() {
		return this.directFlag;
	}
	public void setDirectFlag(Integer directFlag) {
		this.directFlag = directFlag;
	}


	public Integer[] getDirectFlags() {
		return this.directFlags;
	}
	public void setDirectFlags(Integer[] directFlags) {
		this.directFlags = directFlags;
	}


	public Integer getStartDirectFlag() {
		return this.startDirectFlag;
	}
	public void setStartDirectFlag(Integer startDirectFlag) {
		this.startDirectFlag = startDirectFlag;
	}


	public Integer getEndDirectFlag() {
		return this.endDirectFlag;
	}
	public void setEndDirectFlag(Integer endDirectFlag) {
		this.endDirectFlag = endDirectFlag;
	}


	public Integer getAdminFlag() {
		return this.adminFlag;
	}
	public void setAdminFlag(Integer adminFlag) {
		this.adminFlag = adminFlag;
	}


	public Integer[] getAdminFlags() {
		return this.adminFlags;
	}
	public void setAdminFlags(Integer[] adminFlags) {
		this.adminFlags = adminFlags;
	}


	public Integer getStartAdminFlag() {
		return this.startAdminFlag;
	}
	public void setStartAdminFlag(Integer startAdminFlag) {
		this.startAdminFlag = startAdminFlag;
	}


	public Integer getEndAdminFlag() {
		return this.endAdminFlag;
	}
	public void setEndAdminFlag(Integer endAdminFlag) {
		this.endAdminFlag = endAdminFlag;
	}


	public Long getDomainId() {
		return this.domainId;
	}
	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}


	public Long[] getDomainIds() {
		return this.domainIds;
	}
	public void setDomainIds(Long[] domainIds) {
		this.domainIds = domainIds;
	}


	public Long getStartDomainId() {
		return this.startDomainId;
	}
	public void setStartDomainId(Long startDomainId) {
		this.startDomainId = startDomainId;
	}


	public Long getEndDomainId() {
		return this.endDomainId;
	}
	public void setEndDomainId(Long endDomainId) {
		this.endDomainId = endDomainId;
	}


	public Long getUserDomainId() {
		return this.userDomainId;
	}
	public void setUserDomainId(Long userDomainId) {
		this.userDomainId = userDomainId;
	}


	public Long[] getUserDomainIds() {
		return this.userDomainIds;
	}
	public void setUserDomainIds(Long[] userDomainIds) {
		this.userDomainIds = userDomainIds;
	}


	public Long getStartUserDomainId() {
		return this.startUserDomainId;
	}
	public void setStartUserDomainId(Long startUserDomainId) {
		this.startUserDomainId = startUserDomainId;
	}


	public Long getEndUserDomainId() {
		return this.endUserDomainId;
	}
	public void setEndUserDomainId(Long endUserDomainId) {
		this.endUserDomainId = endUserDomainId;
	}


	public Integer getDataStatus() {
		return this.dataStatus;
	}
	public void setDataStatus(Integer dataStatus) {
		this.dataStatus = dataStatus;
	}


	public Integer[] getDataStatuss() {
		return this.dataStatuss;
	}
	public void setDataStatuss(Integer[] dataStatuss) {
		this.dataStatuss = dataStatuss;
	}


	public Integer getStartDataStatus() {
		return this.startDataStatus;
	}
	public void setStartDataStatus(Integer startDataStatus) {
		this.startDataStatus = startDataStatus;
	}


	public Integer getEndDataStatus() {
		return this.endDataStatus;
	}
	public void setEndDataStatus(Integer endDataStatus) {
		this.endDataStatus = endDataStatus;
	}


	public String getCreator() {
		return this.creator;
	}
	public void setCreator(String creator) {
		this.creator = creator;
	}


	public String getCreatorEqual() {
		return this.creatorEqual;
	}
	public void setCreatorEqual(String creatorEqual) {
		this.creatorEqual = creatorEqual;
	}


	public String[] getCreators() {
		return this.creators;
	}
	public void setCreators(String[] creators) {
		this.creators = creators;
	}


	public String getModifier() {
		return this.modifier;
	}
	public void setModifier(String modifier) {
		this.modifier = modifier;
	}


	public String getModifierEqual() {
		return this.modifierEqual;
	}
	public void setModifierEqual(String modifierEqual) {
		this.modifierEqual = modifierEqual;
	}


	public String[] getModifiers() {
		return this.modifiers;
	}
	public void setModifiers(String[] modifiers) {
		this.modifiers = modifiers;
	}


	public Long getCreateTime() {
		return this.createTime;
	}
	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}


	public Long[] getCreateTimes() {
		return this.createTimes;
	}
	public void setCreateTimes(Long[] createTimes) {
		this.createTimes = createTimes;
	}


	public Long getStartCreateTime() {
		return this.startCreateTime;
	}
	public void setStartCreateTime(Long startCreateTime) {
		this.startCreateTime = startCreateTime;
	}


	public Long getEndCreateTime() {
		return this.endCreateTime;
	}
	public void setEndCreateTime(Long endCreateTime) {
		this.endCreateTime = endCreateTime;
	}


	public Long getModifyTime() {
		return this.modifyTime;
	}
	public void setModifyTime(Long modifyTime) {
		this.modifyTime = modifyTime;
	}


	public Long[] getModifyTimes() {
		return this.modifyTimes;
	}
	public void setModifyTimes(Long[] modifyTimes) {
		this.modifyTimes = modifyTimes;
	}


	public Long getStartModifyTime() {
		return this.startModifyTime;
	}
	public void setStartModifyTime(Long startModifyTime) {
		this.startModifyTime = startModifyTime;
	}


	public Long getEndModifyTime() {
		return this.endModifyTime;
	}
	public void setEndModifyTime(Long endModifyTime) {
		this.endModifyTime = endModifyTime;
	}


}


