package com.uinnova.product.eam.base.diagram.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @Classname
 * @Description 封装新增的sheet、node、link id集合
 * <AUTHOR>
 * @Date 2021-06-04-14:15
 */
@Data
public class ESResponseStruct {

    @JSONField(name = "id")
    private Long id;

    @JsonProperty(value = "UUID")
    private String UUID;

    @JSONField(name = "diagramId")
    private Long diagramId;

    @JSONField(name = "errorMessage")
    private String errorMessage;

    public ESResponseStruct() {
    }

    public ESResponseStruct(Long id, String UUID) {
        this.id = id;
        this.UUID = UUID;
    }

    @JsonIgnore
    public String getUUID() {
        return UUID;
    }

    public void setUUID(String UUID) {
        this.UUID = UUID;
    }
}
