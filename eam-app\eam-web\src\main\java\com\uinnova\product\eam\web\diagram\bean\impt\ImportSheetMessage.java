package com.uinnova.product.eam.web.diagram.bean.impt;

import java.util.ArrayList;
import java.util.List;

import com.binary.tools.excel.ExcelUtils.SheetInfo;
import com.uinnova.product.vmdb.comm.util.CiExcelUtil;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;

public class ImportSheetMessage {
	private String sheetName;
	private String className;
	private Integer success;
	private String processedMsg;
	private Integer totalNum;
	private Integer successNum;
	private Integer failNum;
	private Integer insertNum;
	private Integer updateNum;
	private Integer ignoreNum;
	private List<String> titles;
	
	private SheetInfo sheetInfo;
	
	private CcCiClassInfo ciClassInfo;
	
	private List<ImportRowMessage> rowMessages;
	
	public CcCiClassInfo getCiClassInfo() {
		return ciClassInfo;
	}
	public void setCiClassInfo(CcCiClassInfo ciClassInfo) {
		this.ciClassInfo = ciClassInfo;
	}
	public SheetInfo getSheetInfo() {
		return sheetInfo;
	}
	public void setSheetInfo(SheetInfo sheetInfo) {
		this.sheetInfo = sheetInfo;
	}

	public ImportSheetMessage() {
	}
	public ImportSheetMessage(String sheetName) {
		this.sheetName = sheetName;
		this.className = CiExcelUtil.removeSheetSuffix(sheetName);
		this.rowMessages = new ArrayList<ImportRowMessage>();
	}
	public List<String> getTitles() {
		return titles;
	}
	public void setTitles(List<String> titles) {
		this.titles = titles;
	}
	public String getSheetName() {
		return sheetName;
	}
	public void setSheetName(String sheetName) {
		this.sheetName = sheetName;
	}
	public String getClassName() {
		return className;
	}
	public void setClassName(String className) {
		this.className = className;
	}
	public Integer getSuccess() {
		return success;
	}
	public void setSuccess(Integer success) {
		this.success = success;
	}
	public String getProcessedMsg() {
		return processedMsg;
	}
	public void setProcessedMsg(String processedMsg) {
		this.processedMsg = processedMsg;
	}
	public Integer getTotalNum() {
		return totalNum;
	}
	public void setTotalNum(Integer totalNum) {
		this.totalNum = totalNum;
	}
	public Integer getSuccessNum() {
		return successNum;
	}
	public void setSuccessNum(Integer successNum) {
		this.successNum = successNum;
	}
	public Integer getFailNum() {
		return failNum;
	}
	public void setFailNum(Integer failNum) {
		this.failNum = failNum;
	}
	public List<ImportRowMessage> getRowMessages() {
		return rowMessages;
	}
	public void setRowMessages(List<ImportRowMessage> rowMessages) {
		this.rowMessages = rowMessages;
	}
	public Integer getInsertNum() {
		return insertNum;
	}
	public void setInsertNum(Integer insertNum) {
		this.insertNum = insertNum;
	}
	public Integer getUpdateNum() {
		return updateNum;
	}
	public void setUpdateNum(Integer updateNum) {
		this.updateNum = updateNum;
	}
	public Integer getIgnoreNum() {
		return ignoreNum;
	}
	public void setIgnoreNum(Integer ignoreNum) {
		this.ignoreNum = ignoreNum;
	}
	

	
	

}
