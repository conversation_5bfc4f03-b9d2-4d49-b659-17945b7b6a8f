package com.uinnova.product.eam.web.eam.mvc.diagram;

import com.alibaba.fastjson.JSONObject;
import com.binary.framework.web.RemoteResult;
import com.uinnova.product.eam.model.EamCiUpdateDto;
import com.uinnova.product.eam.model.diagram.ArtifactCheckDto;
import com.uinnova.product.eam.model.dm.DataModelBatchResp;
import com.uinnova.product.eam.service.ICanvasDiagramSvc;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uinnova.product.vmdb.comm.doc.annotation.MvcDesc;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/eam/canvas")
@MvcDesc(author="cole",desc="莫奈画布相关接口")
public class CanvasDiagramMvc {

    @Autowired
    private ICanvasDiagramSvc canvasDiagramSvc;

    @PostMapping("/queryCiRltInfo")
    @ModDesc(desc = "查询对象间存在的关系", pType = List.class, pcType = Long.class, pDesc = "", rDesc = "", rType = Long.class)
    public RemoteResult queryCiRltInfo(@RequestBody String body) {
        JSONObject jsonObject = JSONObject.parseObject(body);
        Long from = jsonObject.getLong("from");
        Long to = jsonObject.getLong("to");
        List<CcCiClassInfo> result = canvasDiagramSvc.queryCiRltInfo(from, to);
        return new RemoteResult(result);
    }

    @RequestMapping(value = "/checkCiInfoByDiagramId", method = {RequestMethod.POST})
    @ModDesc(desc = "通过视图ID校验设计库CI对象是否有更新", pDesc = "视图ID", pType = Long.class, rDesc = "", rType = Long.class)
    public RemoteResult checkCiInfoByDiagramId(@RequestBody String body) {
        JSONObject jsonObject = JSONObject.parseObject(body);
        String diagramId = jsonObject.getString("diagramId");
        List<EamCiUpdateDto> result = canvasDiagramSvc.checkCiInfoByDiagramId(diagramId, false);
        return new RemoteResult(result);
    }

    @RequestMapping(value = "/updateCiInfoByDiagramId", method = {RequestMethod.POST})
    @ModDesc(desc = "通过视图Id更新对象及关系数据", pDesc = "视图ID", pType = Long.class, rDesc = "", rType = Long.class)
    public RemoteResult updateCiInfoByDiagramId(@RequestBody String body) {
        JSONObject jsonObject = JSONObject.parseObject(body);
        String diagramId = jsonObject.getString("diagramId");
        DataModelBatchResp result = canvasDiagramSvc.updateCiInfoByDiagramId(diagramId);
        return new RemoteResult(result);
    }

    @GetMapping(value = "/checkByArtifact")
    @ModDesc(desc = "通过制品校验视图关系是否合规", pDesc = "视图ID", pType = Long.class, rDesc = "", rType = List.class)
    public RemoteResult checkByArtifact(@RequestParam String diagramId) {
        List<ArtifactCheckDto> result = canvasDiagramSvc.checkByArtifact(diagramId);
        return new RemoteResult(result);
    }
}
