package com.uino.util.change_notify;

import java.util.Collection;
import java.util.HashSet;
import java.util.Set;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

/**
 * 数据变更处理流程
 * 
 * <AUTHOR>
 *
 */
public interface IChangeProcs<T> {

    /**
     * 新增或修改处理
     * 
     * @param changeDtos
     */
    public void saveOrUpdate(Collection<T> changeDtos);

    /**
     * 以id为标识删除处理
     * 
     * @param delIds
     */
    public void deleteByIds(Collection<Long> delIds);

    /**
     * 删除处理 {@link IChangeProcs#deleteByIds(Collection)}
     * 
     * @param delDtos
     */
    public default void delete(Collection<T> delDtos) {
        if (delDtos == null || delDtos.size() <= 0) { return; }
        Set<Long> delIds = new HashSet<>();
        JSONArray delObjs = JSON.parseArray(JSON.toJSONString(delDtos));
        for (int index = 0; index < delObjs.size(); index++) {
            JSONObject delObj = delObjs.getJSONObject(index);
            delIds.add(delObj.getLong("id"));
        }
        deleteByIds(delIds);
    }
}
