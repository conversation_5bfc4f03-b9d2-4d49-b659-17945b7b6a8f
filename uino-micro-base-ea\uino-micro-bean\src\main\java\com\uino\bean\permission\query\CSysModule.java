package com.uino.bean.permission.query;




import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;


/**
 * condition-table: 系统模块表[SYS_MODULE]
 * 
 * <AUTHOR>
 */
@ApiModel("系统模块表")
public class CSysModule implements Serializable {
	private static final long serialVersionUID = 1L;


	/**
	 * condition-field: ID[ID] operate-Equal[=]
	 */
	@ApiModelProperty(value="id",example = "123")
	private Long id;


	/**
	 * condition-field: ID[ID] operate-In[in]
	 */
	@ApiModelProperty(value="id数组")
	private Long[] ids;


	/**
	 * condition-field: ID[ID] operate-GTEqual[>=]
	 */
	@ApiModelProperty(value="起始id",example = "123")
	private Long startId;

	/**
	 * condition-field: ID[ID] operate-LTEqual[<=]
	 */
	@ApiModelProperty(value="结束id",example = "123")
	private Long endId;


	/**
	 * condition-field: 模块代码[MODULE_CODE] operate-Like[like]
	 */
	@ApiModelProperty(value="模块代码")
	private String moduleCode;

	@ApiModelProperty(value="模块类型", example = "0:菜单, 1:按钮 2：资产仓库小组，3：资产仓库目录", required = true)
	private Integer moduleType = 0;

	public Integer getModuleType() {
		return moduleType;
	}

	public void setModuleType(Integer moduleType) {
		this.moduleType = moduleType;
	}

	/**
	 * condition-field: 模块代码[MODULE_CODE] operate-Equal[=]
	 */
	@ApiModelProperty(value="模块代码")
	private String moduleCodeEqual;


	/**
	 * condition-field: 模块代码[MODULE_CODE] operate-In[in]
	 */
	@ApiModelProperty(value="模块代码数组")
	private String[] moduleCodes;


	/**
	 * condition-field: 模块名称[MODULE_NAME] operate-Like[like]
	 */
	@ApiModelProperty(value="模块名称")
	private String moduleName;

	public String getModuleSignEqual() {
		return moduleSignEqual;
	}

	public void setModuleSignEqual(String moduleSignEqual) {
		this.moduleSignEqual = moduleSignEqual;
	}

	@ApiModelProperty(value="模块标识")
	private String moduleSignEqual;
	/**
	 * condition-field: 父节点[PARENT_ID] operate-Equal[=]
	 * 是否目录:0=模块 1=目录
	 */
	@ApiModelProperty(value="父节点，是否目录:0=模块 1=目录",example = "0")
	private Long parentId;


	/**
	 * condition-field: 父节点[PARENT_ID] operate-In[in]
	 * 是否目录:0=模块 1=目录
	 */
	@ApiModelProperty(value="父节点数组")
	private Long[] parentIds;


	/**
	 * condition-field: 父节点[PARENT_ID] operate-GTEqual[>=]
	 * 是否目录:0=模块 1=目录
	 */
	@ApiModelProperty(value="起始父节点，是否目录:0=模块 1=目录",example = "0")
	private Long startParentId;

	/**
	 * condition-field: 父节点[PARENT_ID] operate-LTEqual[<=]
	 * 是否目录:0=模块 1=目录
	 */
	@ApiModelProperty(value="结束父节点，是否目录:0=模块 1=目录",example = "0")
	private Long endParentId;


	/**
	 * condition-field: 模块描述[MODU_DESC] operate-Like[like]
	 */
	@ApiModelProperty(value="模块描述")
	private String moduDesc;


	/**
	 * condition-field: 显示排序[ORDER_NO] operate-Equal[=]
	 */
	@ApiModelProperty(value="显示排序")
	private Integer orderNo;


	/**
	 * condition-field: 显示排序[ORDER_NO] operate-In[in]
	 */
	@ApiModelProperty(value="显示排序数组")
	private Integer[] orderNos;


	/**
	 * condition-field: 显示排序[ORDER_NO] operate-GTEqual[>=]
	 */
	@ApiModelProperty(value="起始排序")
	private Integer startOrderNo;

	/**
	 * condition-field: 显示排序[ORDER_NO] operate-LTEqual[<=]
	 */
	@ApiModelProperty(value="最终显示排序")
	private Integer endOrderNo;


	/**
	 * condition-field: 链接地址[MODULE_URL] operate-Like[like]
	 */
	@ApiModelProperty("链接地址")
	private String moduleUrl;


	/**
	 * condition-field: 状态[STATUS] operate-Equal[=]
	 * 状态:0=无效 1=有效
	 */
	@ApiModelProperty(value="状态:0=无效 1=有效",example = "0")
	private Integer status;


	/**
	 * condition-field: 状态[STATUS] operate-In[in]
	 * 状态:0=无效 1=有效
	 */
	@ApiModelProperty(value="状态数组:0=无效 1=有效",example = "0")
	private Integer[] statuss;


	/**
	 * condition-field: 状态[STATUS] operate-GTEqual[>=]
	 * 状态:0=无效 1=有效
	 */
	@ApiModelProperty(value="起始状态:0=无效 1=有效",example = "0")
	private Integer startStatus;

	/**
	 * condition-field: 状态[STATUS] operate-LTEqual[<=]
	 * 状态:0=无效 1=有效
	 */
	@ApiModelProperty(value="结束状态:0=无效 1=有效",example = "0")
	private Integer endStatus;


	/**
	 * condition-field: 所属域[DOMAIN_ID] operate-Equal[=]
	 */
	@ApiModelProperty(value = "所属域",example = "123")
	private Long domainId;


	/**
	 * condition-field: 所属域[DOMAIN_ID] operate-In[in]
	 */
	@ApiModelProperty("所属域数组")
	private Long[] domainIds;


	/**
	 * condition-field: 所属域[DOMAIN_ID] operate-GTEqual[>=]
	 */
	@ApiModelProperty(value = "起始所属域id",example = "123")
	private Long startDomainId;

	/**
	 * condition-field: 所属域[DOMAIN_ID] operate-LTEqual[<=]
	 */
	@ApiModelProperty(value = "结束所属域id",example = "123")
	private Long endDomainId;


	/**
	 * condition-field: 创建人[CREATOR] operate-Like[like]
	 */
	@ApiModelProperty(value = "创建人",example = "mike")
	private String creator;


	/**
	 * condition-field: 创建人[CREATOR] operate-Equal[=]
	 */
	@ApiModelProperty("创建人")
	private String creatorEqual;


	/**
	 * condition-field: 创建人[CREATOR] operate-In[in]
	 */
	@ApiModelProperty("创建人数组")
	private String[] creators;


	/**
	 * condition-field: 修改人[MODIFIER] operate-Like[like]
	 */
	@ApiModelProperty(value = "修改人",example = "123")
	private String modifier;


	/**
	 * condition-field: 修改人[MODIFIER] operate-Equal[=]
	 */
	@ApiModelProperty("修改人")
	private String modifierEqual;


	/**
	 * condition-field: 修改人[MODIFIER] operate-In[in]
	 */
	@ApiModelProperty("修改人数组")
	private String[] modifiers;


	/**
	 * condition-field: 创建时间[CREATE_TIME] operate-Equal[=]
	 * 创建时间:yyyyMMddHHmmss
	 */
	@ApiModelProperty("创建时间")
	private Long createTime;


	/**
	 * condition-field: 创建时间[CREATE_TIME] operate-In[in]
	 * 创建时间:yyyyMMddHHmmss
	 */
	@ApiModelProperty("创建时间数组")
	private Long[] createTimes;


	/**
	 * condition-field: 创建时间[CREATE_TIME] operate-GTEqual[>=]
	 * 创建时间:yyyyMMddHHmmss
	 */
	@ApiModelProperty("起始创建时间")
	private Long startCreateTime;

	/**
	 * condition-field: 创建时间[CREATE_TIME] operate-LTEqual[<=]
	 * 创建时间:yyyyMMddHHmmss
	 */
	@ApiModelProperty("结束创建时间")
	private Long endCreateTime;


	/**
	 * condition-field: 修改时间[MODIFY_TIME] operate-Equal[=]
	 * 修改时间:yyyyMMddHHmmss
	 */
	@ApiModelProperty("修改时间")
	private Long modifyTime;


	/**
	 * condition-field: 修改时间[MODIFY_TIME] operate-In[in]
	 * 修改时间:yyyyMMddHHmmss
	 */
	@ApiModelProperty("修改时间数组")
	private Long[] modifyTimes;


	/**
	 * condition-field: 修改时间[MODIFY_TIME] operate-GTEqual[>=]
	 * 修改时间:yyyyMMddHHmmss
	 */
	@ApiModelProperty("起始修改时间")
	private Long startModifyTime;

	/**
	 * condition-field: 修改时间[MODIFY_TIME] operate-LTEqual[<=]
	 * 修改时间:yyyyMMddHHmmss
	 */
	@ApiModelProperty("结束修改时间")
	private Long endModifyTime;


	/**
	 * condition-field: 模块图片[MODULE_IMG] operate-Like[like]
	 */
	@ApiModelProperty(value="模块图片")
	private String moduleImg;


	/**
	 * condition-field: 自定义图片[CUSTOM_IMG] operate-Like[like]
	 */
	@ApiModelProperty(value="自定义图片")
	private String customImg;




	public Long getId() {
		return this.id;
	}
	public void setId(Long id) {
		this.id = id;
	}


	public Long[] getIds() {
		return this.ids;
	}
	public void setIds(Long[] ids) {
		this.ids = ids;
	}


	public Long getStartId() {
		return this.startId;
	}
	public void setStartId(Long startId) {
		this.startId = startId;
	}


	public Long getEndId() {
		return this.endId;
	}
	public void setEndId(Long endId) {
		this.endId = endId;
	}


	public String getModuleCode() {
		return this.moduleCode;
	}
	public void setModuleCode(String moduleCode) {
		this.moduleCode = moduleCode;
	}


	public String getModuleCodeEqual() {
		return this.moduleCodeEqual;
	}
	public void setModuleCodeEqual(String moduleCodeEqual) {
		this.moduleCodeEqual = moduleCodeEqual;
	}


	public String[] getModuleCodes() {
		return this.moduleCodes;
	}
	public void setModuleCodes(String[] moduleCodes) {
		this.moduleCodes = moduleCodes;
	}


	public String getModuleName() {
		return this.moduleName;
	}
	public void setModuleName(String moduleName) {
		this.moduleName = moduleName;
	}


	public Long getParentId() {
		return this.parentId;
	}
	public void setParentId(Long parentId) {
		this.parentId = parentId;
	}


	public Long[] getParentIds() {
		return this.parentIds;
	}
	public void setParentIds(Long[] parentIds) {
		this.parentIds = parentIds;
	}


	public Long getStartParentId() {
		return this.startParentId;
	}
	public void setStartParentId(Long startParentId) {
		this.startParentId = startParentId;
	}


	public Long getEndParentId() {
		return this.endParentId;
	}
	public void setEndParentId(Long endParentId) {
		this.endParentId = endParentId;
	}


	public String getModuDesc() {
		return this.moduDesc;
	}
	public void setModuDesc(String moduDesc) {
		this.moduDesc = moduDesc;
	}


	public Integer getOrderNo() {
		return this.orderNo;
	}
	public void setOrderNo(Integer orderNo) {
		this.orderNo = orderNo;
	}


	public Integer[] getOrderNos() {
		return this.orderNos;
	}
	public void setOrderNos(Integer[] orderNos) {
		this.orderNos = orderNos;
	}


	public Integer getStartOrderNo() {
		return this.startOrderNo;
	}
	public void setStartOrderNo(Integer startOrderNo) {
		this.startOrderNo = startOrderNo;
	}


	public Integer getEndOrderNo() {
		return this.endOrderNo;
	}
	public void setEndOrderNo(Integer endOrderNo) {
		this.endOrderNo = endOrderNo;
	}


	public String getModuleUrl() {
		return this.moduleUrl;
	}
	public void setModuleUrl(String moduleUrl) {
		this.moduleUrl = moduleUrl;
	}


	public Integer getStatus() {
		return this.status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}


	public Integer[] getStatuss() {
		return this.statuss;
	}
	public void setStatuss(Integer[] statuss) {
		this.statuss = statuss;
	}


	public Integer getStartStatus() {
		return this.startStatus;
	}
	public void setStartStatus(Integer startStatus) {
		this.startStatus = startStatus;
	}


	public Integer getEndStatus() {
		return this.endStatus;
	}
	public void setEndStatus(Integer endStatus) {
		this.endStatus = endStatus;
	}


	public Long getDomainId() {
		return this.domainId;
	}
	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}


	public Long[] getDomainIds() {
		return this.domainIds;
	}
	public void setDomainIds(Long[] domainIds) {
		this.domainIds = domainIds;
	}


	public Long getStartDomainId() {
		return this.startDomainId;
	}
	public void setStartDomainId(Long startDomainId) {
		this.startDomainId = startDomainId;
	}


	public Long getEndDomainId() {
		return this.endDomainId;
	}
	public void setEndDomainId(Long endDomainId) {
		this.endDomainId = endDomainId;
	}


	public String getCreator() {
		return this.creator;
	}
	public void setCreator(String creator) {
		this.creator = creator;
	}


	public String getCreatorEqual() {
		return this.creatorEqual;
	}
	public void setCreatorEqual(String creatorEqual) {
		this.creatorEqual = creatorEqual;
	}


	public String[] getCreators() {
		return this.creators;
	}
	public void setCreators(String[] creators) {
		this.creators = creators;
	}


	public String getModifier() {
		return this.modifier;
	}
	public void setModifier(String modifier) {
		this.modifier = modifier;
	}


	public String getModifierEqual() {
		return this.modifierEqual;
	}
	public void setModifierEqual(String modifierEqual) {
		this.modifierEqual = modifierEqual;
	}


	public String[] getModifiers() {
		return this.modifiers;
	}
	public void setModifiers(String[] modifiers) {
		this.modifiers = modifiers;
	}


	public Long getCreateTime() {
		return this.createTime;
	}
	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}


	public Long[] getCreateTimes() {
		return this.createTimes;
	}
	public void setCreateTimes(Long[] createTimes) {
		this.createTimes = createTimes;
	}


	public Long getStartCreateTime() {
		return this.startCreateTime;
	}
	public void setStartCreateTime(Long startCreateTime) {
		this.startCreateTime = startCreateTime;
	}


	public Long getEndCreateTime() {
		return this.endCreateTime;
	}
	public void setEndCreateTime(Long endCreateTime) {
		this.endCreateTime = endCreateTime;
	}


	public Long getModifyTime() {
		return this.modifyTime;
	}
	public void setModifyTime(Long modifyTime) {
		this.modifyTime = modifyTime;
	}


	public Long[] getModifyTimes() {
		return this.modifyTimes;
	}
	public void setModifyTimes(Long[] modifyTimes) {
		this.modifyTimes = modifyTimes;
	}


	public Long getStartModifyTime() {
		return this.startModifyTime;
	}
	public void setStartModifyTime(Long startModifyTime) {
		this.startModifyTime = startModifyTime;
	}


	public Long getEndModifyTime() {
		return this.endModifyTime;
	}
	public void setEndModifyTime(Long endModifyTime) {
		this.endModifyTime = endModifyTime;
	}


	public String getModuleImg() {
		return this.moduleImg;
	}
	public void setModuleImg(String moduleImg) {
		this.moduleImg = moduleImg;
	}


	public String getCustomImg() {
		return this.customImg;
	}
	public void setCustomImg(String customImg) {
		this.customImg = customImg;
	}


}


