package com.uinnova.product.eam.model.dm;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.comm.model.ci.CcCiClass;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uinnova.product.vmdb.provider.rlt.bean.CcCiRltInfo;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据建模保存rlt返回体
 * <AUTHOR>
 */
@Data
public class DataModelRltInfo extends CcCiRltInfo {
    @Comment("继承节点集合：实体ciCode-继承属性list")
    private Map<String, List<CcCiInfo>> inheritMap = new HashMap<>();
    @Comment("删除节点集合：实体ciCode-继承属性list")
    private Map<String, List<String>> delMap = new HashMap<>();
    @Comment("实体属性的属性定义")
    private List<CcCiAttrDef> attributeDefs;
    @Comment("实体属性所属分类")
    private CcCiClass attributeCiClass;
}
