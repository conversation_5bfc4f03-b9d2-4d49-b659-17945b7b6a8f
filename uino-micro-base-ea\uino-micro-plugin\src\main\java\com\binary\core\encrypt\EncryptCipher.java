package com.binary.core.encrypt;

import java.security.Key;

import javax.crypto.Cipher;

import com.binary.core.exception.EncryptException;
import com.binary.core.util.BinaryUtils;

public class EncryptCipher {
	
	
	/** 算法名称 **/
	public static final String TRANS_AES = "AES";
	public static final String TRANS_RSA = "RSA";
	
	
	/** 加密/解密 **/
	public static final int OPMODE_ENCRYPT = Cipher.ENCRYPT_MODE;
	public static final int OPMODE_DECRYPT = Cipher.DECRYPT_MODE;
	
	
	
	
	private Cipher cipher;
	private String transformation;
	
	
	
	public EncryptCipher(Key key, String transformation, int opmode) {
		BinaryUtils.checkEmpty(key, "key");
		BinaryUtils.checkEmpty(transformation, "transformation");
		EncryptKey ek = new EncryptKey(key);
		init(ek, transformation, opmode);
	}
	
	
	public EncryptCipher(String key, String transformation, int opmode) {
		BinaryUtils.checkEmpty(key, "key");
		BinaryUtils.checkEmpty(transformation, "transformation");
		EncryptKey ek = new EncryptKey(key);
		init(ek, transformation, opmode);
	}
	
	
	public EncryptCipher(Cipher cipher) {
		BinaryUtils.checkEmpty(cipher, "cipher");
		this.cipher = cipher;
	}
	
	
	
	private void init(EncryptKey ek, String transformation, int opmode) {
		try {
			this.cipher = Cipher.getInstance(transformation);
			cipher.init(opmode, ek.getKey());
		} catch (Exception e) {
			throw BinaryUtils.transException(e, EncryptException.class);
		}
	}


	
	
	public Cipher getCipher() {
		return cipher;
	}


	public String getTransformation() {
		return transformation;
	}
	
	
	
	
	
	

}
