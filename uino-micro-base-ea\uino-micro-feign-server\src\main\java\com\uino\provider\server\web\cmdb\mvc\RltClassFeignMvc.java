package com.uino.provider.server.web.cmdb.mvc;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;
import java.util.Set;

import com.uino.bean.cmdb.business.ClassReOrderDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.binary.core.io.Resource;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClass;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uino.service.cmdb.microservice.IRltClassSvc;
import com.uino.bean.cmdb.base.ESCIClassInfo;
import com.uino.provider.feign.cmdb.RltClassFeign;

import lombok.extern.slf4j.Slf4j;

/**
 * 
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("feign/rltClass")
@Slf4j
public class RltClassFeignMvc implements RltClassFeign {

    @Autowired
    private IRltClassSvc rltClassSvc;

    @Override
    public Long saveOrUpdate(ESCIClassInfo rltClass) {
        return rltClassSvc.saveOrUpdate(rltClass);
    }

    @Override
    public Integer deleteByIds(Set<Long> rltClassIds) {
        return rltClassSvc.deleteByIds(rltClassIds);
    }

    @Override
    public List<CcCiClassInfo> queryAllClasses(Long domainId) {
        return rltClassSvc.queryAllClasses(domainId);
    }

    @Override
    public CcCiClassInfo getRltClassById(Long rltClassId) {
        return rltClassSvc.getRltClassById(rltClassId);
    }

    @Override
    public CcCiClassInfo getRltClassByName(Long domainId, String className) {
        return rltClassSvc.getRltClassByName(domainId, className);
    }

    @Override
    public ResponseEntity<byte[]> exportAttrDefs(Set<Long> clsIds) {
        Resource resource = rltClassSvc.exportAttrDefs(clsIds);
        byte[] tempBytes = new byte[4096];
        int readLine = 0;
        ByteArrayOutputStream outStream = new ByteArrayOutputStream();
        try {
            while ((readLine = resource.getInputStream().read(tempBytes)) != -1) {
                outStream.write(tempBytes, 0, readLine);
            }
        } catch (IOException e) {
            log.error("读流异常", e);
        }
        byte[] body = outStream.toByteArray();
        try {
            outStream.close();
        } catch (IOException e) {
        }
        return ResponseEntity.ok(body);
    }

    @Override
    public ESCIClassInfo importAttrDefs(MultipartFile excelFile, Long clsId) {
        return rltClassSvc.importAttrDefs(excelFile, clsId);
    }

    @Override
    public List<CcCiClassInfo> getRltClassByCdt(CCcCiClass cdt) {
        return rltClassSvc.getRltClassByCdt(cdt);
    }

    @Override
    public boolean reOrder(ClassReOrderDTO reOrderDTO) {
        return rltClassSvc.reOrder(reOrderDTO);
    }

    @Override
    public boolean initAllOrderNo(Long domainId) {
        return rltClassSvc.initAllOrderNo(domainId);
    }
}
