package com.uinnova.product.eam.db.diagram.es;

import com.binary.core.util.BinaryUtils;

import com.uinnova.product.eam.base.diagram.model.ESDiagramNode;
import com.uinnova.product.eam.base.diagram.model.ESDiagramNodeQueryBean;
import com.uino.dao.AbstractESBaseDao;
import com.uino.service.util.FileUtil;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.stereotype.Repository;

import jakarta.annotation.PostConstruct;
import java.util.Collection;
import java.util.List;

/**
 * @Classname ES视图节点数据访问层
 * @Description 该类用来操作ES中的视图节点index
 * <AUTHOR>
 * @Date 2021-06-02-16:25
 */
@Repository
public class ESDiagramNodeDao extends AbstractESBaseDao<ESDiagramNode, ESDiagramNodeQueryBean> {
    @Override
    public String getIndex() {
        return "monet_diagram_node";
    }

    @Override
    public String getType() {
        return "_doc";
    }

    @PostConstruct
    public void init() {
        List<ESDiagramNode> list = FileUtil.getData("/initdata/uino_monet_diagram_node.json", ESDiagramNode.class);
        super.initIndex(list);
    }

    public void remove(Collection<Long> ids){
        if(BinaryUtils.isEmpty(ids)){
            return;
        }
        deleteByQuery(QueryBuilders.termsQuery("diagramId", ids),true);
    }
}
