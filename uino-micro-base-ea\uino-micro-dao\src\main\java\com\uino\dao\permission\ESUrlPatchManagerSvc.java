package com.uino.dao.permission;

import com.uino.bean.permission.base.SysUrlPatchManager;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.util.ArrayList;

/**
 * 项目Url管理
 *
 * <AUTHOR>
 * @since 2023/3/15 14:44
 */
@Service
public class ESUrlPatchManagerSvc extends AbstractESBaseDao<SysUrlPatchManager, SysUrlPatchManager> {
    @Override
    public String getIndex() {
        return "uino_sys_url_patch_manager";
    }

    @Override
    public String getType() {
        return getIndex();
    }

    @PostConstruct
    public void init() {
        initIndex(new ArrayList<>());
    }
}
