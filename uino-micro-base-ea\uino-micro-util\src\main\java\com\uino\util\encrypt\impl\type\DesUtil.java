package com.uino.util.encrypt.impl.type;

import lombok.extern.log4j.Log4j2;

import javax.crypto.Cipher;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESKeySpec;
import javax.crypto.spec.IvParameterSpec;
import java.security.Key;
import java.util.Base64;

/**
 * @Title DES Util
 * @Author: YGQ
 * @Create: 2021-08-08 13:50
 **/
@Log4j2
public class DesUtil {
    /**
     * offset variable fixed to occupy 8 bytes
     */
    private final static String IV_PARAMETER = "12345678";
    /**
     * key algorithm
     */
    private static final String ALGORITHM = "DES";
    /**
     * encryption and decryption algorithm-working mode-filling mode
     */
    private static final String CIPHER_ALGORITHM = "DES/CBC/PKCS5Padding";
    /**
     * default encoding
     */
    private static final String CHARSET = "utf-8";

    /**
     * DES encrypted string
     *
     * @param password Encryption password, the length cannot be less than 8 digits
     * @param data     String to be encrypted
     * @return {@code Encrypted content}
     */
    public static String encrypt(String password, String data) {
        if (password == null || password.length() < 8) {
            throw new RuntimeException("Encryption fails. The key cannot be smaller than 8 bits");
        }
        if (data == null) {
            return null;
        }
        try {
            Key secretKey = generateKey(password);
            Cipher cipher = Cipher.getInstance(CIPHER_ALGORITHM);
            IvParameterSpec iv = new IvParameterSpec(IV_PARAMETER.getBytes(CHARSET));
            cipher.init(Cipher.ENCRYPT_MODE, secretKey, iv);
            byte[] bytes = cipher.doFinal(data.getBytes(CHARSET));

            return new String(Base64.getEncoder().encode(bytes));
        } catch (Exception e) {
            log.error(">>> encrypt error : ", e);
            return data;
        }
    }

    /**
     * DES decrypt string
     *
     * @param password The decryption password cannot be less than 8 characters long
     * @param data     String to decrypt
     * @return {@code Decrypted content}
     */
    public static String decrypt(String password, String data) {
        if (password == null || password.length() < 8) {
            throw new RuntimeException("The key cannot be smaller than 8 bits");
        }

        if (data == null) {
            return null;
        }

        try {
            Key secretKey = generateKey(password);
            Cipher cipher = Cipher.getInstance(CIPHER_ALGORITHM);
            IvParameterSpec iv = new IvParameterSpec(IV_PARAMETER.getBytes(CHARSET));
            cipher.init(Cipher.DECRYPT_MODE, secretKey, iv);
            return new String(cipher.doFinal(Base64.getDecoder().decode(data.getBytes(CHARSET))), CHARSET);
        } catch (Exception e) {
            log.error(">>> decrypt error : ", e);
            return data;
        }
    }

    /**
     * Generate key
     *
     * @param password password
     * @return {@link Key}
     * @throws Exception exception
     */
    private static Key generateKey(String password) throws Exception {
        DESKeySpec dks = new DESKeySpec(password.getBytes(CHARSET));
        SecretKeyFactory keyFactory = SecretKeyFactory.getInstance(ALGORITHM);
        return keyFactory.generateSecret(dks);
    }

    /**
     * Invoke the sample
     */
    public static void main(String[] args) {
        System.out.println(encrypt("12345678", "adsf"));
        System.out.println(decrypt("12345678", "PFR+oMPVSEc="));
    }
}
