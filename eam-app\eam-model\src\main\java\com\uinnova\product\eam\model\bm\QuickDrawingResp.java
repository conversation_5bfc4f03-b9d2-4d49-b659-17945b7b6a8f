package com.uinnova.product.eam.model.bm;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uino.bean.cmdb.base.ESCIInfo;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/10
 */
@Data
public class QuickDrawingResp {
    @Comment("ci集合")
    private List<ESCIInfo> ci = new ArrayList<>();
    @Comment("ci分类")
    private CcCiClassInfo ciClass;
    @Comment("活动图数据")
    List<ActivityDrawingResp> activityList = new ArrayList<>();
    @Comment("提示信息")
    private String message;
}
