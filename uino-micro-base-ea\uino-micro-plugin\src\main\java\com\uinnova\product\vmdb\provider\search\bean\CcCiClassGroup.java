package com.uinnova.product.vmdb.provider.search.bean;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.vmdb.comm.model.ci.CcCiClass;

import java.io.Serializable;

public class CcCiClassGroup implements Serializable {
	private static final long serialVersionUID = 1L;
	
	
	
	@Comment("当前CI所属分类")
	private CcCiClass cls;
	
	
	@Comment("分类下CI的数量")
	private Long ciCount;

	


	public CcCiClass getCls() {
		return cls;
	}



	public void setCls(CcCiClass cls) {
		this.cls = cls;
	}



	public Long getCiCount() {
		return ciCount;
	}



	public void setCiCount(Long ciCount) {
		this.ciCount = ciCount;
	}



	
	


	
	
	

}
