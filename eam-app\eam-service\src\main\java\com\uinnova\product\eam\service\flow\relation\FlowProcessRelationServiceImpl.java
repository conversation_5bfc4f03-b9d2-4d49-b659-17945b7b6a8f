package com.uinnova.product.eam.service.flow.relation;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.binary.core.lang.StringUtils;
import com.binary.jdbc.Page;
import com.google.common.collect.Sets;
import com.uinnova.product.eam.base.diagram.model.*;
import com.uinnova.product.eam.comm.model.es.EamDiagramRelationSys;
import com.uinnova.product.eam.comm.model.es.FlowSystemAssociatedFeatures;
import com.uinnova.product.eam.comm.model.es.IndicatorDetectionInformationAssociation;
import com.uinnova.product.eam.model.ProcessPerformanceDto;
import com.uinnova.product.eam.model.SceneActiveRltDto;
import com.uinnova.product.eam.model.VcCiRltInfo;
import com.uinnova.product.eam.model.dto.FlowSystemAssociatedFeaturesDto;
import com.uinnova.product.eam.model.vo.KcpInfoVo;
import com.uinnova.product.eam.service.exception.BusinessException;
import com.uinnova.product.eam.service.flow.base.AbstractFlowProcessSystemService;
import com.uinnova.product.vmdb.comm.model.ci.CCcCi;
import com.uinnova.product.vmdb.comm.model.ci.CcCi;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uinnova.product.vmdb.provider.rlt.bean.CcCiRltInfo;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.base.ESCIRltInfo;
import com.uino.bean.cmdb.base.ESPropertyType;
import com.uino.bean.cmdb.base.LibType;
import com.uino.bean.cmdb.business.BindCiRltRequestDto;
import com.uino.bean.cmdb.query.ESCISearchBean;
import com.uino.bean.cmdb.query.ESRltSearchBean;
import com.uino.bean.permission.base.SysUser;
import com.uino.dao.util.ESUtil;
import com.uino.service.cmdb.microservice.ICISvc;
import com.uino.util.sys.SysUtil;
import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 流程关联要素服务
 * 负责关联要素管理、场景活动关联等功能
 */
@Service
@Slf4j
public class FlowProcessRelationServiceImpl extends AbstractFlowProcessSystemService {

    /**
     * 添加流程关联要素
     */
    public FlowSystemAssociatedFeaturesDto addFlowSystemAssociatedFeatures(
            FlowSystemAssociatedFeatures flowProcessSystem) {
        Assert.notNull(flowProcessSystem.getCiCode(), "ciCode不能为空");
        Assert.notNull(flowProcessSystem.getClassCode(), "classCode不能为空");
        Assert.notNull(flowProcessSystem.getLinkedCiCode(), "linkedCiCode不能为空");

        Long flowLinkId = flowSystemAssociatedFeaturesPrivateDao.saveOrUpdate(flowProcessSystem);
        CCcCi cCcCi = new CCcCi();
        cCcCi.setCiCodes(new String[]{flowProcessSystem.getLinkedCiCode()});
        List<CcCiInfo> AllCiInfo = iamsCIDesignSvc.queryCiInfoList(1L, cCcCi, null, Boolean.FALSE, Boolean.FALSE);

        FlowSystemAssociatedFeaturesDto flowSystemAssociatedFeaturesDto = new FlowSystemAssociatedFeaturesDto();
        flowSystemAssociatedFeaturesDto.setId(flowLinkId);
        flowSystemAssociatedFeaturesDto.setCiCode(flowProcessSystem.getCiCode());
        flowSystemAssociatedFeaturesDto.setLinkedCiCode(flowProcessSystem.getLinkedCiCode());
        flowSystemAssociatedFeaturesDto.setClassCode(flowProcessSystem.getClassCode());

        if (!CollectionUtils.isEmpty(AllCiInfo)) {
            CcCiInfo ccCiInfo = AllCiInfo.get(0);
            flowSystemAssociatedFeaturesDto.setCi(ccCiInfo.getCi());
            flowSystemAssociatedFeaturesDto.setAttrs(ccCiInfo.getAttrs());
        }

        return flowSystemAssociatedFeaturesDto;
    }

    /**
     * 获取流程关联要素
     */
    public Map<String, Object> getFlowSystemAssociatedFeatures(
            String ciCode, String classCode, LibType libType) {

        Set<String> ciCodeSet = new HashSet<>();
        ciCodeSet.add(ciCode);
        CcCiClassInfo ciClassByClassCode = new CcCiClassInfo();
        Map<String, Object> resultMap = new HashMap<>();
        List<FlowSystemAssociatedFeatures> flowSystemAssociatedFeatures = new ArrayList<>();
        if (Objects.equals(classCode, "制度") || Objects.equals(classCode, "标准")) {
            ciClassByClassCode = iciClassSvc.getCiClassByClassCode(classCode);
            List<CcCiAttrDef> attrDefs = ciClassByClassCode.getAttrDefs();
            if (!CollectionUtils.isEmpty(attrDefs)) {
                if (Objects.equals(classCode, "制度") && (attrDefs.size() - 1) >= 0) {
                    CcCiAttrDef def = new CcCiAttrDef();
                    def.setProName("相关活动");
                    def.setProType(3);
                    attrDefs.add(attrDefs.size() - 1, def);
                } else {
                    CcCiAttrDef def = new CcCiAttrDef();
                    def.setProName("相关活动");
                    attrDefs.add(def);
                }
            }
            resultMap.put("attrDefs", attrDefs);
        } else if (Objects.equals(classCode, "关联应用")) {
            List<CcCiAttrDef> attrDefs = new ArrayList<>();
            CcCiAttrDef app = new CcCiAttrDef();
            app.setProName("应用系统");
            app.setProType(3);

            CcCiAttrDef activity = new CcCiAttrDef();
            activity.setProName("相关活动");
            activity.setProType(3);
            attrDefs.add(app);
            attrDefs.add(activity);
            resultMap.put("attrDefs", attrDefs);
        } else if (Objects.equals(classCode, "业务对象")) {
            List<CcCiAttrDef> attrDefs = new ArrayList<>();
            CcCiAttrDef app = new CcCiAttrDef();
            app.setProName("业务对象名称");
            app.setProType(3);

            CcCiAttrDef model = new CcCiAttrDef();
            model.setProName("关联表单");
            model.setProType(3);

            CcCiAttrDef activity = new CcCiAttrDef();
            activity.setProName("相关活动");
            activity.setProType(3);
            attrDefs.add(app);
            attrDefs.add(model);
            attrDefs.add(activity);
            resultMap.put("attrDefs", attrDefs);
        } else if (Objects.equals(classCode, "KCP")) {
            ciClassByClassCode = iciClassSvc.getCiClassByClassCode(classCode);
            List<CcCiAttrDef> attrDefs = ciClassByClassCode.getAttrDefs();
            if (!CollectionUtils.isEmpty(attrDefs)) {
                CcCiAttrDef def = new CcCiAttrDef();
                def.setProName("相关活动");
                def.setProType(3);
                attrDefs.add(def);
            }
            resultMap.put("attrDefs", attrDefs);
            List<KcpInfoVo> kcpInfoList = this.findKcpInfoList(ciCode, 1, libType);
            List<FlowSystemAssociatedFeaturesDto> resultList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(kcpInfoList)) {
                for (KcpInfoVo kcp : kcpInfoList) {
                    Map<String, String> contentMap = new HashMap<>();
                    contentMap.put("关键控制点编号", kcp.getKeyContPointNum());
                    contentMap.put("控制点名称", kcp.getKeyContPoint());
                    contentMap.put("控制目标", kcp.getControlTarget());
                    contentMap.put("控制方法", kcp.getControlMethod());
                    contentMap.put("关联的风险名称", kcp.getRelatedRisk());
                    contentMap.put("相关活动", kcp.getRelatedActivities());

                    FlowSystemAssociatedFeaturesDto flowSystemAssociatedFeaturesDto = new FlowSystemAssociatedFeaturesDto();
                    flowSystemAssociatedFeaturesDto.setAttrs(contentMap);
                    resultList.add(flowSystemAssociatedFeaturesDto);
                }
            }
            resultMap.put("dataList", resultList);
            return resultMap;
        } else if (Objects.equals(classCode, "表单")) {
            ciClassByClassCode = iciClassSvc.getCiClassByClassCode(classCode);
            List<CcCiAttrDef> attrDefs = ciClassByClassCode.getAttrDefs();
            if (!CollectionUtils.isEmpty(attrDefs)) {
                CcCiAttrDef def = new CcCiAttrDef();
                def.setProName("相关活动");
                def.setProType(3);
                attrDefs.add(def);

                CcCiAttrDef type = new CcCiAttrDef();
                type.setProName("输入/输出类型");
                type.setProType(3);
                attrDefs.add(type);
            }
            resultMap.put("attrDefs", attrDefs);
        } else if (Objects.equals(classCode, "指标") || Objects.equals(classCode, "风险") || Objects.equals(classCode, "场景") || Objects.equals(classCode, "档案")) {
            ciClassByClassCode = iciClassSvc.getCiClassByClassCode(classCode);
            List<CcCiAttrDef> attrDefs = ciClassByClassCode.getAttrDefs();
            resultMap.put("attrDefs", attrDefs);
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
            boolQueryBuilder.filter(QueryBuilders.termsQuery("ciCode.keyword", ciCodeSet));
            boolQueryBuilder.filter(QueryBuilders.termQuery("classCode.keyword", classCode));
            if (Objects.equals(LibType.DESIGN, libType)) {
                boolQueryBuilder.filter(QueryBuilders.termQuery("status", 1));
                flowSystemAssociatedFeatures = flowSystemAssociatedFeaturesDao.getListByQueryScroll(boolQueryBuilder);
            } else if (Objects.equals(LibType.PRIVATE, libType)) {
                SysUser sysUser = SysUtil.getCurrentUserInfo();
                boolQueryBuilder.filter(QueryBuilders.termQuery("creator.keyword", sysUser.getLoginCode()));
                flowSystemAssociatedFeatures = flowSystemAssociatedFeaturesPrivateDao.getListByQueryScroll(boolQueryBuilder);
            }
            if (CollectionUtils.isEmpty(flowSystemAssociatedFeatures)) {
                resultMap.put("dataList", new ArrayList<>());
                return resultMap;
            }
        } else if (Objects.equals(classCode, "岗位角色")) {
            ciClassByClassCode = iciClassSvc.getCiClassByClassCode(classCode);
            List<CcCiAttrDef> attrDefs = ciClassByClassCode.getAttrDefs();
            if (!CollectionUtils.isEmpty(attrDefs)) {
                CcCiAttrDef def = new CcCiAttrDef();
                def.setProName("相关活动");
                def.setProType(3);
                attrDefs.add(def);
            }
            resultMap.put("attrDefs", attrDefs);
        } else {
            ciClassByClassCode = iciClassSvc.getCiClassByClassCode(classCode);
            List<CcCiAttrDef> attrDefs = ciClassByClassCode.getAttrDefs();
            resultMap.put("attrDefs", attrDefs);
        }

        // 查询关联要素下的标签的相关活动
        boolean sign = Objects.equals(classCode, "制度") || Objects.equals(classCode, "标准")
                || Objects.equals(classCode, "关联应用") || Objects.equals(classCode, "业务对象")
                || Objects.equals(classCode, "表单") || Objects.equals(classCode, "岗位角色");
        Map<String, Set<String>> ciCodeMap = new HashMap<>();
        if (sign) {
            ciCodeMap = handlerRelaActivity(ciCode, classCode, libType);
        }
        if (!(Objects.equals(classCode, "制度") || Objects.equals(classCode, "标准")
                || Objects.equals(classCode, "指标") || Objects.equals(classCode, "风险") || Objects.equals(classCode, "场景")
                || Objects.equals(classCode, "档案") || Objects.equals(classCode, "术语") || Objects.equals(classCode, "要素")) &&
                CollectionUtils.isEmpty(ciCodeMap)) {
            return resultMap;
        }

        Set<String> codeSet = new HashSet<>();
        if (Objects.equals(classCode, "指标") || Objects.equals(classCode, "风险") || Objects.equals(classCode, "场景") || Objects.equals(classCode, "档案")) {
            codeSet = flowSystemAssociatedFeatures.stream().filter(o -> o.getLinkedCiCode() != null)
                    .map(FlowSystemAssociatedFeatures::getLinkedCiCode).collect(Collectors.toSet());
            if (!CollectionUtils.isEmpty(ciCodeMap)) {
                codeSet.addAll(ciCodeMap.keySet());
            }
        } else {
            codeSet = ciCodeMap.keySet();
        }

        CCcCi cCcCi = new CCcCi();
        cCcCi.setCiCodes(codeSet.toArray(new String[0]));
        List<CcCiInfo> allCiInfo = new ArrayList<>();
        //指标数据有些特殊单独处理下
        if (!CollectionUtils.isEmpty(codeSet)) {
            if (libType.equals(LibType.PRIVATE)) {
                cCcCi.setOwnerCodeEqual(SysUtil.getCurrentUserInfo().getLoginCode());
                allCiInfo = ciSwitchSvc.getCiSvc(LibType.PRIVATE).queryCiInfoList(1L, cCcCi, null, Boolean.FALSE, Boolean.FALSE);
            } else {
                allCiInfo = iamsCIDesignSvc.queryCiInfoList(1L, cCcCi, null, Boolean.FALSE, Boolean.FALSE);
            }
        }

        // 关联要素下各个分类处理结果
        List<FlowSystemAssociatedFeaturesDto> resultList = handlerAssociateResult(classCode, allCiInfo, ciCodeMap, flowSystemAssociatedFeatures, libType);
        Set<String> collect = resultList.stream().map(FlowSystemAssociatedFeaturesDto::getLinkedCiCode).collect(Collectors.toSet());
        Set<String> flowLinkCiCode = new HashSet<>();
        if (Objects.equals(classCode, "制度") || Objects.equals(classCode, "标准")
                || Objects.equals(classCode, "术语") || Objects.equals(classCode, "要素")) {
            ESCISearchBean esciSearchBean = new ESCISearchBean();
            esciSearchBean.setCiCodes(Collections.singletonList(ciCode));
            esciSearchBean.setPageSize(1);
            esciSearchBean.setPageNum(1);
            CcCiInfo ciInfoByCiCode = null;
            if (LibType.PRIVATE.equals(libType)) {
                ciInfoByCiCode = ciSwitchSvc.getCiSvc(libType).getCiInfoByCiCode(ciCode, SysUtil.getCurrentUserInfo().getLoginCode());
            } else {
                ciInfoByCiCode = ciSwitchSvc.getCiSvc(libType).getCiInfoByCiCode(ciCode, null);
            }
            if (ciInfoByCiCode != null) {
                List<CcCiAttrDef> attrDefs = ciInfoByCiCode.getAttrDefs();
                String jsonStr = null;
                Map<String, String> attrs = ciInfoByCiCode.getAttrs();
                for (CcCiAttrDef attrDef : attrDefs) {
                    if (attrDef.getProType().equals(ESPropertyType.LINK_CI.getValue())) {
                        String proDropSourceDef = attrDef.getProDropSourceDef();
                        if (proDropSourceDef.equalsIgnoreCase(ciClassByClassCode.getCiClass().getId().toString())) {
                            String s = attrs.get(attrDef.getProStdName());
                            if (org.apache.commons.lang3.StringUtils.isNotBlank(s)) {
                                jsonStr = s;
                            }
                        }
                    }
                }
                if (jsonStr != null) {
                    JSONArray objects = JSONArray.parseArray(jsonStr);
                    for (int i = 0; i < objects.size(); i++) {
                        String ciCode1 = objects.getJSONObject(i).getString("ciCode");
                        if (!collect.contains(ciCode1)) {
                            flowLinkCiCode.add(ciCode1);
                        }
                    }
                }
            }
        }
        if (!CollectionUtils.isEmpty(flowLinkCiCode)) {
            CCcCi cCcCiLink = new CCcCi();
            cCcCiLink.setCiCodes(flowLinkCiCode.toArray(new String[0]));
            if (LibType.PRIVATE.equals(libType)) {
                cCcCiLink.setOwnerCodeEqual(SysUtil.getCurrentUserInfo().getLoginCode());
            }
            List<CcCiInfo> ccCiInfos = ciSwitchSvc.getCiSvc(libType).queryCiInfoList(1L, cCcCiLink, null, Boolean.FALSE, Boolean.FALSE);
            for (CcCiInfo ccCiInfo : ccCiInfos) {
                FlowSystemAssociatedFeaturesDto flowSystemAssociatedFeaturesDto = new FlowSystemAssociatedFeaturesDto();
                flowSystemAssociatedFeaturesDto.setAttrs(ccCiInfo.getAttrs());
                flowSystemAssociatedFeaturesDto.setLinkedCiCode(ccCiInfo.getCi().getCiCode());
                resultList.add(flowSystemAssociatedFeaturesDto);
            }
        }
        resultMap.put("dataList", resultList);
        return resultMap;
    }

    public CcCiInfo createProcessPerformance(ProcessPerformanceDto processPerformanceDto, String classCode) {
        Long id = processPerformanceDto.getCi().getId();
        processPerformanceDto.getCi().setOwnerCode(SysUtil.getCurrentUserInfo().getLoginCode());
        ICISvc ciSvc = ciSwitchSvc.getCiSvc(LibType.PRIVATE);
        Long l = ciSvc.saveOrUpdate(processPerformanceDto);
        CcCiInfo ciInfoById = ciSwitchSvc.getCiSvc(LibType.PRIVATE).getCiInfoById(l);
        if (id == null) {
            FlowSystemAssociatedFeatures flowSystemAssociatedFeatures = new FlowSystemAssociatedFeatures();
            flowSystemAssociatedFeatures.setCiCode(processPerformanceDto.getParentCiCode());
            flowSystemAssociatedFeatures.setClassCode(classCode);
            flowSystemAssociatedFeatures.setLinkedCiCode(ciInfoById.getCi().getCiCode());
            flowSystemAssociatedFeaturesPrivateDao.saveOrUpdate(flowSystemAssociatedFeatures);
        }
        return ciInfoById;
    }

    /**
     * 批量保存场景活动关联
     */
    public List<String> batchSaveSceneActiveRlt(SceneActiveRltDto sceneActiveRltDto) {
        String loginCode = SysUtil.getCurrentUserInfo().getLoginCode();
        CcCiInfo ciByCode = ciSwitchSvc.getCiByCode(sceneActiveRltDto.getSceneCiCode(), loginCode, LibType.PRIVATE);
        List<ESCIInfo> ciByCodes = ciSwitchSvc.getCiByCodes(sceneActiveRltDto.getActiveCiCodes(), loginCode, LibType.PRIVATE);
        List<Long> activeIds = new ArrayList<>();
        for (ESCIInfo byCode : ciByCodes) {
            activeIds.add(byCode.getId());
        }
        Long sceneCiId = ciByCode.getCi().getId();
        CcCiClassInfo linkClass = iRltClassSvc.getRltClassByName(1L, "关联");
        CcCiClassInfo activeClass = iciClassSvc.getCiClassByClassCode("活动");
        ESRltSearchBean esRltSearchBean = new ESRltSearchBean();
        //目标是场景id
        esRltSearchBean.setTargetCiIds(Collections.singletonList(sceneCiId));
        //关联关系
        esRltSearchBean.setRltClassIds(Collections.singletonList(linkClass.getCiClass().getId()));
        //源端类型时活动
        esRltSearchBean.setSourceClassIds(Collections.singletonList(activeClass.getCiClass().getId()));
        esRltSearchBean.setOwnerCode(loginCode);
        List<CcCiRltInfo> ccCiRltInfos = iciRltSwitchSvc.searchRltByScroll(esRltSearchBean, LibType.PRIVATE);
        //判断新增还是删除
        Set<Long> delRltIds = new HashSet<>();
        Set<Long> exitActiveIds = new HashSet<>();
        for (CcCiRltInfo ccCiRltInfo : ccCiRltInfos) {
            //源端是活动
            CcCiInfo sourceCiInfo = ccCiRltInfo.getSourceCiInfo();
            if (!activeIds.contains(sourceCiInfo.getCi().getId())) {
                //删除库中有，入参没有的关系
                delRltIds.add(ccCiRltInfo.getCiRlt().getId());
            } else {
                //不需要删除的关系
                exitActiveIds.add(sourceCiInfo.getCi().getId());
            }
        }
        Sets.SetView<Long> difference = Sets.difference(new HashSet<>(activeIds), exitActiveIds);
        List<BindCiRltRequestDto> reqBean = new ArrayList<>();
        for (Long activeCiId : difference) {

            BindCiRltRequestDto bindRlt = BindCiRltRequestDto.builder().ownerCode(loginCode).repetitionError(false).rltClassId(linkClass.getCiClass().getId())
                    .custom1("3").sourceCiId(activeCiId).targetCiId(sceneCiId).build();
            reqBean.add(bindRlt);
        }
        if (!CollectionUtils.isEmpty(reqBean)) {
            //新增关系
            iciRltSwitchSvc.bindCiRltBatch(reqBean, LibType.PRIVATE);
        }
        if (!CollectionUtils.isEmpty(delRltIds)) {
            //删除关系
            iciRltSwitchSvc.delRltByIdsOrRltCodes(delRltIds, null, loginCode, LibType.PRIVATE);
        }

        List<CcCiRltInfo> newCcCiRltInfos = iciRltSwitchSvc.searchRltByScroll(esRltSearchBean, LibType.PRIVATE);
        List<String> resultList = new ArrayList<>();
        for (CcCiRltInfo newCcCiRltInfo : newCcCiRltInfos) {
            resultList.add(newCcCiRltInfo.getSourceCiInfo().getCi().getCiCode());
        }
        return resultList;
    }

    /**
     * 查询KCP信息列表
     */
    public List<KcpInfoVo> findKcpInfoList(String ciCode, Integer sign, LibType libType) {
        if (StringUtils.isEmpty(ciCode)) {
            throw new BusinessException("ciCode不能为空!");
        }
        Set<String> ciCodes = null;
        Map<String, CcCiInfo> ciInfoMap = new HashMap<>();
        if (Objects.equals(sign, 2)) {
            CcCiInfo designCiInfo = ciSwitchSvc.getCiByCode(ciCode, SysUtil.getCurrentUserInfo().getLoginCode(), libType);

            // 关联关系 ——> 包含 关系
            CcCiClassInfo rltClass = iciRltSwitchSvc.getRltClassByCode("包含");


            if (rltClass != null && rltClass.getCiClass() != null && rltClass.getCiClass().getId() != null) {
                List<Long> rltClassIds = new ArrayList<>();
                rltClassIds.add(rltClass.getCiClass().getId());

                List<VcCiRltInfo> vcCiRltInfos = iciRltSwitchSvc.queryUpAndDownRlt(LibType.DESIGN, designCiInfo.getCi().getId(),
                        null, rltClassIds, 0, 10, false);
                if (!CollectionUtils.isEmpty(vcCiRltInfos)) {
                    vcCiRltInfos.forEach(vcCiRltInfo -> {
                        if (vcCiRltInfo.getCiRlt() != null && !StringUtils.isEmpty(vcCiRltInfo.getCiRlt().getTargetCiCode())
                                && vcCiRltInfo.getTargetCiInfo() != null) {
                            ciInfoMap.put(vcCiRltInfo.getCiRlt().getTargetCiCode(), vcCiRltInfo.getTargetCiInfo());
                        }
                    });
                }
            }
            if (CollectionUtils.isEmpty(ciInfoMap)) {
                return null;
            }
            ciCodes = ciInfoMap.keySet();
        } else {
            ciCodes = new HashSet<>();
            ciCodes.add(ciCode);
        }
        // 视图关联ci Map
        Map<String, String> diagramRelaCiMap = this.diagramRelaCiMap(ciCodes, libType);
        if (CollectionUtils.isEmpty(diagramRelaCiMap)) {
            return null;
        }
        List<ESDiagramDTO> esDiagramDTOList = this.esDiagramDTOList(diagramRelaCiMap);
        if (CollectionUtils.isEmpty(esDiagramDTOList)) {
            return null;
        }
        Map<String, KcpInfoVo> kcpInfoVoMap = new HashMap<>();
        for (ESDiagramDTO eSDiagramDto : esDiagramDTOList) {
            ESDiagramInfoDTO diagram = eSDiagramDto.getDiagram();
            List<ESDiagramModel> modelList = diagram.getModelList();
            if (!CollectionUtils.isEmpty(modelList)) {
                for (ESDiagramModel model : modelList) {
                    List<ESDiagramLink> linkDataArray = model.getLinkDataArray();
                    if (CollectionUtils.isEmpty(linkDataArray)) {
                        continue;
                    }
                    List<ESDiagramNode> nodeDataArray = model.getNodeDataArray();
                    if (CollectionUtils.isEmpty(nodeDataArray)) {
                        continue;
                    }
                    // 节点是否关联对象
                    Map<String, String> collect = nodeDataArray.stream().filter(item -> !StringUtils.isEmpty(item.getCiCode()))
                            .collect(Collectors.toMap(item -> item.getKey(), item -> item.getCiCode()));
                    for (ESDiagramLink link : linkDataArray) {
                        String linkJson = link.getLinkJson();
                        JSONObject jsonObject = JSONObject.parseObject(linkJson);
                        String from = jsonObject.getString("from");
                        String to = jsonObject.getString("to");
                        String className = jsonObject.getString("className");
                        // 统计源点是KCP对象，目标点是活动，且KCP-包含关系的数据
                        if (collect.containsKey(from) && collect.containsKey(to) && !StringUtils.isEmpty(className)
                                && Objects.equals(className, "KCP-包含")) {
                            String fromCiCode = collect.get(from);
                            CcCiInfo fromCiInfo = ciSwitchSvc.getCiByCode(fromCiCode, SysUtil.getCurrentUserInfo().getLoginCode(), libType);
                            if (fromCiInfo != null && fromCiInfo.getCiClass() != null && Objects.equals(fromCiInfo.getCiClass().getClassCode(), "KCP")) {
                                String toCiCode = collect.get(to);
                                CcCiInfo toCiInfo = ciSwitchSvc.getCiByCode(toCiCode, SysUtil.getCurrentUserInfo().getLoginCode(), libType);

                                // 有存在相同的KCP数据的统计为1条，活动逗号分割
                                Map<String, String> fromAttrs = fromCiInfo.getAttrs();
                                if (!kcpInfoVoMap.containsKey(fromAttrs.get("关键控制点编号"))) {
                                    KcpInfoVo kcpInfoVo = new KcpInfoVo();
                                    kcpInfoVo.setKeyContPointNum(fromAttrs.get("关键控制点编号"));
                                    kcpInfoVo.setKeyContPoint(fromAttrs.get("控制点名称"));
                                    kcpInfoVo.setControlTarget(fromAttrs.get("控制目标"));
                                    kcpInfoVo.setControlMethod(fromAttrs.get("控制方法"));
                                    kcpInfoVo.setRelatedRisk(fromAttrs.get("关联的风险名称"));
                                    Map<String, String> activeAttrs = toCiInfo.getAttrs();
                                    String active = activeAttrs.get("活动名称");
                                    kcpInfoVo.setRelatedActivities(active);
                                    if (Objects.equals(sign, 2)) {
                                        String diagramCiCode = diagramRelaCiMap.get(diagram.getDEnergy());
                                        CcCiInfo ccCiInfo = ciInfoMap.get(diagramCiCode);
                                        Map<String, String> attrs = ccCiInfo.getAttrs();
                                        String s = attrs.get("流程编码");
                                        String s1 = attrs.get("流程名称");
                                        kcpInfoVo.setKeySubProcesses(s + " " + s1);
                                    }
                                    kcpInfoVoMap.put(fromAttrs.get("关键控制点编号"), kcpInfoVo);
                                } else {
                                    KcpInfoVo infoVo = kcpInfoVoMap.get(fromAttrs.get("关键控制点编号"));
                                    Map<String, String> activeAttrs = toCiInfo.getAttrs();
                                    String active = activeAttrs.get("活动名称");
                                    if (!StringUtils.isEmpty(infoVo.getRelatedActivities())) {
                                        List<String> activityList = Arrays.stream(infoVo.getRelatedActivities().split("，")).collect(Collectors.toList());
                                        if (!activityList.contains(active)) {
                                            infoVo.setRelatedActivities(infoVo.getRelatedActivities() + "，" + active);
                                        }
                                    } else {
                                        infoVo.setRelatedActivities(active);
                                    }

                                    if (Objects.equals(sign, 2)) {
                                        String diagramCiCode = diagramRelaCiMap.get(diagram.getDEnergy());
                                        CcCiInfo ccCiInfo = ciInfoMap.get(diagramCiCode);
                                        Map<String, String> attrs = ccCiInfo.getAttrs();
                                        String s = attrs.get("流程编码");
                                        String s1 = attrs.get("流程名称");
                                        String process = s + " " + s1;
                                        if (!StringUtils.isEmpty(infoVo.getKeySubProcesses())) {
                                            List<String> processList = Arrays.stream(infoVo.getKeySubProcesses().split("，")).collect(Collectors.toList());
                                            if (!processList.contains(process)) {
                                                infoVo.setKeySubProcesses(infoVo.getKeySubProcesses() + "，" + process);
                                            }
                                        } else {
                                            infoVo.setKeySubProcesses(process);
                                        }
                                    }
                                    kcpInfoVoMap.put(fromAttrs.get("关键控制点编号"), infoVo);
                                }

                            }
                        }
                    }
                }
            }
        }
        return new ArrayList<>(kcpInfoVoMap.values());
    }

    /**
     * 获取制度和标准的活动
     *
     * @param ciCode
     * @param classCode
     * @return
     */
    private Map<String, Set<String>> handlerRelaActivity(String ciCode, String classCode, LibType libType) {
        // 查询流程图
        Set<String> modelCiCodeSet = new HashSet<>();
        List<ESDiagramModel> modelList = null;
        List<EamDiagramRelationSys> diagramRelationSysList = null;
        if (LibType.PRIVATE.equals(libType)) {
            diagramRelationSysList = eamDiagramRelationSysService.findFlowSystemDiagramRelationPrivate(ciCode, "flowDiagram");
        } else {
            diagramRelationSysList = eamDiagramRelationSysService.findFlowSystemDiagramRelation(ciCode, "flowDiagram");
        }

        if (!CollectionUtils.isEmpty(diagramRelationSysList)) {
            log.info("视图版本数量：" + diagramRelationSysList.size() + "；视图信息：" + JSONObject.toJSONString(diagramRelationSysList));
            EamDiagramRelationSys eamDiagramRelationSys = diagramRelationSysList.get(0);
            if (eamDiagramRelationSys != null && !StringUtils.isEmpty(eamDiagramRelationSys.getDiagramEnergy())) {
                Long diagramId = diagramApiClient.queryDiagramInfoByEnergy(eamDiagramRelationSys.getDiagramEnergy());
                if (diagramId != null) {
                    List<ESDiagramDTO> esDiagramDTOList = diagramApiClient.queryDiagramInfoByIds(new Long[]{diagramId}, null, false, false);
                    if (!CollectionUtils.isEmpty(esDiagramDTOList)) {
                        ESDiagramDTO esDiagramDTO = esDiagramDTOList.get(0);
                        ESDiagramInfoDTO diagram = esDiagramDTO.getDiagram();
                        modelList = diagram.getModelList();
                        if (!CollectionUtils.isEmpty(modelList) && !Objects.equals(classCode, "岗位角色")) {
                            for (ESDiagramModel model : modelList) {
                                List<ESDiagramNode> nodeDataArray = model.getNodeDataArray();
                                if (CollectionUtils.isEmpty(nodeDataArray)) {
                                    continue;
                                }
                                // 节点是否关联对象
                                for (ESDiagramNode node : nodeDataArray) {
                                    if (!StringUtils.isEmpty(node.getCiCode())) {
                                        modelCiCodeSet.add(node.getCiCode());
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        Map<String, Set<String>> ciCodeMap = new HashMap<>();
        // 角色岗位单独去处理
        if (Objects.equals(classCode, "岗位角色")) {
            if (!CollectionUtils.isEmpty(modelList)) {
                Set<String> uniqueCodeSet = new HashSet<>();
                for (ESDiagramModel model : modelList) {
                    List<ESDiagramLink> linkDataArray = model.getLinkDataArray();
                    if (CollectionUtils.isEmpty(linkDataArray)) {
                        continue;
                    }
                    // 节点是否关联对象
                    for (ESDiagramLink link : linkDataArray) {
                        String linkJson = link.getLinkJson();
                        if (!StringUtils.isEmpty(linkJson)) {
                            JSONObject jsonObject = JSONObject.parseObject(linkJson);
                            String className = jsonObject.getString("className");
                            if (!StringUtils.isEmpty(className) && Objects.equals(className, "由...执行")) {
                                uniqueCodeSet.add(link.getUniqueCode());
                            }
                        }
                    }
                }
                if (!CollectionUtils.isEmpty(uniqueCodeSet)) {
                    List<ESCIRltInfo> rltByUniqueCodes = iciRltSwitchSvc.getRltByUniqueCodes(uniqueCodeSet, libType.equals(LibType.PRIVATE) ? SysUtil.getCurrentUserInfo().getLoginCode() : null, libType);
                    if (!CollectionUtils.isEmpty(rltByUniqueCodes)) {
                        for (ESCIRltInfo rltInfo : rltByUniqueCodes) {
                            ciCodeMap.computeIfAbsent(rltInfo.getTargetCiCode(), key -> new HashSet<>()).add(rltInfo.getSourceCiCode());
                        }
                    }
                }
            }
            return ciCodeMap;
        }

        // 活动关联的制度、标准、关联应用、业务对象、表单
        if (!CollectionUtils.isEmpty(modelCiCodeSet)) {
            CcCiClassInfo ciClass = iciClassSvc.getCiClassByClassCode("活动");
            if (ciClass != null && ciClass.getCiClass() != null && ciClass.getCiClass().getId() != null) {
                CCcCi cdt = new CCcCi();
                cdt.setClassId(ciClass.getCiClass().getId());
                cdt.setCiCodes(modelCiCodeSet.toArray(new String[0]));
                if (LibType.PRIVATE.equals(libType)) {
                    cdt.setOwnerCodeEqual(SysUtil.getCurrentUserInfo().getLoginCode());
                }
                List<CcCiInfo> ccCiInfos = ciSwitchSvc.queryCiInfoList(1L, cdt, "", false, true, libType);
                if (!CollectionUtils.isEmpty(ccCiInfos)) {
                    for (CcCiInfo ccCiInfo : ccCiInfos) {
                        Map<String, String> attrs = ccCiInfo.getAttrs();
                        if (Objects.equals(classCode, "制度")) {
                            String institution = attrs.get("关联制度");
                            handlerInstitution(institution, ciCodeMap, attrs);
                        } else if (Objects.equals(classCode, "标准")) {
                            String institution = attrs.get("关联标准");
                            handlerInstitution(institution, ciCodeMap, attrs);
                        } else if (Objects.equals(classCode, "关联应用")) {
                            String institution = attrs.get("关联应用");
                            handlerInstitution(institution, ciCodeMap, attrs);
                        } else if (Objects.equals(classCode, "业务对象")) {
                            String inputForm = attrs.get("输入表单");
                            String outputForm = attrs.get("输出表单");
                            if (!StringUtils.isEmpty(inputForm)) {
                                JSONArray inputJson = JSONObject.parseArray(inputForm);
                                if (!CollectionUtils.isEmpty(inputJson)) {
                                    for (int i = 0; i < inputJson.size(); i++) {
                                        JSONObject jsonObject = inputJson.getJSONObject(i);
                                        String ciCodeInfo = jsonObject.getString("ciCode");
                                        ciCodeMap.computeIfAbsent(ciCodeInfo, key -> new HashSet<>()).add(attrs.get("活动名称"));
                                    }
                                }
                            }
                            if (!StringUtils.isEmpty(outputForm)) {
                                JSONArray outputJson = JSONObject.parseArray(outputForm);
                                if (!CollectionUtils.isEmpty(outputJson)) {
                                    for (int i = 0; i < outputJson.size(); i++) {
                                        JSONObject jsonObject = outputJson.getJSONObject(i);
                                        String ciCodeInfo = jsonObject.getString("ciCode");
                                        ciCodeMap.computeIfAbsent(ciCodeInfo, key -> new HashSet<>()).add(attrs.get("活动名称"));
                                    }
                                }
                            }
                        } else if (Objects.equals(classCode, "表单")) {
                            String inputForm = attrs.get("输入表单");
                            String outputForm = attrs.get("输出表单");
                            if (!StringUtils.isEmpty(inputForm)) {
                                JSONArray inputJson = JSONObject.parseArray(inputForm);
                                if (!CollectionUtils.isEmpty(inputJson)) {
                                    for (int i = 0; i < inputJson.size(); i++) {
                                        JSONObject jsonObject = inputJson.getJSONObject(i);
                                        String ciCodeInfo = jsonObject.getString("ciCode");
                                        ciCodeMap.computeIfAbsent(ciCodeInfo, key -> new HashSet<>()).add(ccCiInfo.getCi().getCiCode());
                                    }
                                }
                            }
                            if (!StringUtils.isEmpty(outputForm)) {
                                JSONArray outputJson = JSONObject.parseArray(outputForm);
                                if (!CollectionUtils.isEmpty(outputJson)) {
                                    for (int i = 0; i < outputJson.size(); i++) {
                                        JSONObject jsonObject = outputJson.getJSONObject(i);
                                        String ciCodeInfo = jsonObject.getString("ciCode");
                                        ciCodeMap.computeIfAbsent(ciCodeInfo, key -> new HashSet<>()).add(ccCiInfo.getCi().getCiCode());
                                    }
                                }
                            }
                        }
                    }
                }

            }
        }
        log.info(classCode + " 关联活动数据：" + JSONObject.toJSONString(ciCodeMap));
        return ciCodeMap;
    }

    /**
     * 删除流程关联要素
     */
    public void deleteFlowSystemAssociatedFeatures(Long associatedFeatureId) {
        FlowSystemAssociatedFeatures byId = flowSystemAssociatedFeaturesPrivateDao.getById(associatedFeatureId);
        if (byId != null) {
            flowSystemAssociatedFeaturesPrivateDao.deleteById(associatedFeatureId);
        }
    }

    /**
     * 批量添加流程关联要素
     */
    public void batchAddFlowSystemAssociatedFeatures(FlowSystemAssociatedFeaturesDto flowProcessSystemDto) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.termQuery("ciCode.keyword", flowProcessSystemDto.getCiCode()));
        boolQueryBuilder.filter(QueryBuilders.termQuery("classCode.keyword", flowProcessSystemDto.getClassCode()));
        boolQueryBuilder
                .filter(QueryBuilders.termQuery("creator.keyword", SysUtil.getCurrentUserInfo().getLoginCode()));
        List<FlowSystemAssociatedFeatures> listByQueryScroll = flowSystemAssociatedFeaturesPrivateDao
                .getListByQueryScroll(boolQueryBuilder);
        if (!CollectionUtils.isEmpty(listByQueryScroll)) {
            flowSystemAssociatedFeaturesPrivateDao.deleteByQuery(boolQueryBuilder, true);
        }
        List<FlowSystemAssociatedFeatures> flowSystemAssociatedFeatures1 = new ArrayList<>();
        for (String linkedCiCode : flowProcessSystemDto.getLinkedCiCodes()) {
            FlowSystemAssociatedFeatures flowSystemAssociatedFeatures = new FlowSystemAssociatedFeatures();
            flowSystemAssociatedFeatures.setCiCode(flowProcessSystemDto.getCiCode());
            flowSystemAssociatedFeatures.setClassCode(flowProcessSystemDto.getClassCode());
            flowSystemAssociatedFeatures.setLinkedCiCode(linkedCiCode);
            flowSystemAssociatedFeatures.setCreator(SysUtil.getCurrentUserInfo().getLoginCode());
            flowSystemAssociatedFeatures1.add(flowSystemAssociatedFeatures);
        }
        flowSystemAssociatedFeaturesPrivateDao.saveOrUpdateBatch(flowSystemAssociatedFeatures1);
    }

    public Map<String, Map> getFlowSystemAssociatedFeaturesNew(String diagramIds, String ciCode, LibType libType) {
        Map<String, Map<String, String>> featureMaps = getStringMapMap(diagramIds, ciCode, libType);
        Map<String, Map> result = new HashMap<>();
        String[] keys = {"制度", "标准", "KCP", "术语", "输入表单", "输出表单", "岗位角色", "要素"};
        for (String key : keys) {
            Map<String, Object> resultEntry = new HashMap<>();
            CcCiClassInfo ciClass = "输入表单".equals(key) || "输出表单".equals(key)
                    ? iciClassSvc.getCiClassByClassCode("表单")
                    : iciClassSvc.getCiClassByClassCode(key);
            List<CcCiAttrDef> attrDefs = ciClass.getAttrDefs();
            if ("输入表单".equals(key) || "输出表单".equals(key)) {
                CcCiAttrDef formAttrDef = new CcCiAttrDef();
                formAttrDef.setProName("输入/输出类型");
                formAttrDef.setProType(3);
                attrDefs.add(formAttrDef);
            }
            if (!key.equals("术语") && !key.equals("要素")) {
                CcCiAttrDef relatedActivityAttrDef = new CcCiAttrDef();
                relatedActivityAttrDef.setProName("相关活动");
                relatedActivityAttrDef.setProType(3);
                int size = attrDefs.size();
                if (size > 0) {
                    attrDefs.add(size - 1, relatedActivityAttrDef);
                } else {
                    attrDefs.add(relatedActivityAttrDef);
                }
            }
            resultEntry.put("attrDefs", attrDefs);
            if (featureMaps.isEmpty() || !featureMaps.containsKey(key)) {
                resultEntry.put("dataList", new ArrayList<>());
            } else {
                resultEntry.put("dataList", buildFeaturesList(key, featureMaps.get(key), libType));
            }
            result.put(key, resultEntry);
        }
        // 合并输入表单和输出表单
        mergingInputAndOutputForms(result);
        result.put("业务对象", buildBusinessObjectsResult(featureMaps, libType));
        result.put("关联应用", buildApplicationArchitectureResult(featureMaps));
        //风险,指标
        otherResult(result, ciCode, libType);
        return result;
    }

    public Map obtainTheProcessFile(String ciCode, LibType libType) {
        Map<String, Map> result = new HashMap<>();
        otherResult(result, ciCode, libType);
        return result.get("档案管理");
    }

    public Set<String> whetherDataIsChanged(String ciCode, String classCode) {
        // 1. 查询设计库数据
        BoolQueryBuilder designQueryBuilder = QueryBuilders.boolQuery()
                .filter(QueryBuilders.termQuery("ciCode.keyword", ciCode))
                .filter(QueryBuilders.termQuery("classCode.keyword", classCode))
                .filter(QueryBuilders.termQuery("status", 1));
        List<FlowSystemAssociatedFeatures> designFeatures = flowSystemAssociatedFeaturesDao.getListByQuery(designQueryBuilder);
        if (designFeatures.isEmpty()) {
            return new HashSet<>();
        }
        // 2. 查询私有库数据
        String loginCode = SysUtil.getCurrentUserInfo().getLoginCode();
        BoolQueryBuilder privateQueryBuilder = QueryBuilders.boolQuery()
                .filter(QueryBuilders.termQuery("ciCode.keyword", ciCode))
                .filter(QueryBuilders.termQuery("creator.keyword", loginCode))
                .filter(QueryBuilders.termQuery("classCode.keyword", classCode));
        List<FlowSystemAssociatedFeatures> privateFeatures = flowSystemAssociatedFeaturesPrivateDao.getListByQuery(privateQueryBuilder);

        // 3. 比较数据并收集修改人
        Set<String> modifiers = new HashSet<>();
        if (privateFeatures.isEmpty()) {
            // 私有库无数据，收集所有设计库数据的修改人
            modifiers.addAll(designFeatures.stream()
                    .map(FlowSystemAssociatedFeatures::getModifier)
                    .collect(Collectors.toSet()));
        } else {
            // 构建私有库数据的Map，用于快速查找
            Map<String, Long> privateVersionMap = privateFeatures.stream()
                    .collect(Collectors.toMap(
                            FlowSystemAssociatedFeatures::getLinkedCiCode,
                            feature -> feature.getVersionId() != null ? feature.getVersionId() : 0L,
                            (existing, replacement) -> replacement  // 保留最新的值
                    ));
            // 比较版本并收集修改人
            designFeatures.forEach(design -> {
                if (design != null) {  // 检查design对象是否为空
                    String linkedCiCode = design.getLinkedCiCode();
                    Long designVersion = design.getVersionId();
                    if (linkedCiCode != null) {  // 检查关键属性是否为空
                        Long privateVersion = privateVersionMap.get(linkedCiCode);
                        // 如果privateVersion为null，将其视为0
                        privateVersion = privateVersion == null ? 0L : privateVersion;
                        if (privateVersion == 0L || !privateVersion.equals(designVersion)) {
                            String modifier = design.getModifier();
                            if (modifier != null) {  // 检查modifier是否为空
                                modifiers.add(modifier);
                            }
                        }
                    }
                }
            });
        }
        if (!modifiers.isEmpty()) {
            modifiers.remove(loginCode);
        }
        return modifiers;
    }

    public Long mergePullAssociationCi(String ciCode, String classCode) {
        // 1. 查询设计库数据
        BoolQueryBuilder designQueryBuilder = QueryBuilders.boolQuery()
                .filter(QueryBuilders.termQuery("ciCode.keyword", ciCode))
                .filter(QueryBuilders.termQuery("status", 1))
                .filter(QueryBuilders.termQuery("classCode.keyword", classCode));
        List<FlowSystemAssociatedFeatures> designFeatures = flowSystemAssociatedFeaturesDao.getListByQuery(designQueryBuilder);
        if (designFeatures.isEmpty()) {
            return 1L;
        }
        // 2. 查询私有库数据
        String loginCode = SysUtil.getCurrentUserInfo().getLoginCode();
        BoolQueryBuilder privateQueryBuilder = QueryBuilders.boolQuery()
                .filter(QueryBuilders.termQuery("ciCode.keyword", ciCode))
                .filter(QueryBuilders.termQuery("creator.keyword", loginCode))
                .filter(QueryBuilders.termQuery("classCode.keyword", classCode));
        List<FlowSystemAssociatedFeatures> privateFeatures = flowSystemAssociatedFeaturesPrivateDao.getListByQuery(privateQueryBuilder);

        // 处理数据同步
        if (privateFeatures.isEmpty()) {
            // 私有库无数据，直接同步全部设计库数据
            for (FlowSystemAssociatedFeatures feature : designFeatures) {
                feature.setId(ESUtil.getUUID());
                feature.setCreator(loginCode);
            }
            flowSystemAssociatedFeaturesPrivateDao.saveOrUpdateBatch(designFeatures);
            // 同步关联的CI数据
            Set<String> designLinkCodes = designFeatures.stream()
                    .map(FlowSystemAssociatedFeatures::getLinkedCiCode)
                    .collect(Collectors.toSet());
            List<ESCIInfo> ciByCodes = ciSwitchSvc.getCiByCodes(new ArrayList<>(designLinkCodes), null, LibType.DESIGN);
            if (!ciByCodes.isEmpty()) {
                CcCiClassInfo classInfo = iciClassSvc.getCiClassByClassCode(classCode);
                for (ESCIInfo ciByCode : ciByCodes) {
                    ciByCode.setId(null);
                    ciByCode.setOwnerCode(loginCode);
                }
                ciSwitchSvc.saveOrUpdateBatchCI(ciByCodes, Collections.singletonList(classInfo.getCiClass().getId()),
                        loginCode, loginCode, LibType.PRIVATE);
                // 如果是指标，同步指标检查数据
                //在这里因为私有库没有次数据,可以先将IndicatorDetectionInformationAssociation清空再将数据同步进去
                if ("指标".equals(classCode)) {
                    syncIndicatorDetectionData(designLinkCodes, loginCode);
                }
            }
        } else {
            Set<FlowSystemAssociatedFeatures> modifiedFeatures = new HashSet<>();
            // 构建私有库数据的Map，用于快速查找
            Map<String, Long> privateVersionMap = privateFeatures.stream()
                    .collect(Collectors.toMap(
                            FlowSystemAssociatedFeatures::getLinkedCiCode,
                            feature -> feature.getVersionId() != null ? feature.getVersionId() : 0L,
                            (existing, replacement) -> replacement  // 保留最新的值
                    ));
            // 比较版本并收集修改人
            designFeatures.forEach(design -> {
                if (design != null) {  // 检查design对象是否为空
                    String linkedCiCode = design.getLinkedCiCode();
                    Long designVersion = design.getVersionId();
                    if (linkedCiCode != null) {  // 检查关键属性是否为空
                        Long privateVersion = privateVersionMap.get(linkedCiCode);
                        // 如果privateVersion为null，将其视为0
                        privateVersion = privateVersion == null ? 0L : privateVersion;
                        if (privateVersion == 0L || !privateVersion.equals(designVersion)) {
                            modifiedFeatures.add(design);
                        }
                    }
                }
            });

            //清理下私有库有资产库没有的ci数据
            HashSet<String> designLinkCiCodes = new HashSet<>();
            for (FlowSystemAssociatedFeatures designFeature : designFeatures) {
                designLinkCiCodes.add(designFeature.getLinkedCiCode());
            }
            Set<FlowSystemAssociatedFeatures> delLinkCi = new HashSet<>();
            for (FlowSystemAssociatedFeatures privateFeature : privateFeatures) {
                if (!designLinkCiCodes.contains(privateFeature.getLinkedCiCode())) {
                    delLinkCi.add(privateFeature);
                }
            }
            if (!CollectionUtils.isEmpty(delLinkCi)) {
                Set<String> delLinkCiCodes = delLinkCi.stream().map(del -> del.getLinkedCiCode()).collect(Collectors.toSet());
                BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery()
                        .filter(QueryBuilders.termsQuery("linkedCiCode.keyword", delLinkCiCodes))
                        .filter(QueryBuilders.termQuery("creator.keyword", loginCode));
                flowSystemAssociatedFeaturesPrivateDao.deleteByQuery(queryBuilder, true);
                List<ESCIInfo> ciByCodes = ciSwitchSvc.getCiByCodes(new ArrayList<>(delLinkCiCodes), SysUtil.getCurrentUserInfo().getLoginCode(), LibType.PRIVATE);
                if (!CollectionUtils.isEmpty(ciByCodes)) {
                    List<Long> collect = ciByCodes.stream().map(ESCIInfo::getId).collect(Collectors.toList());
                    ciSwitchSvc.removeByIds(collect, 1L, LibType.PRIVATE);
                }
            }

            if (!modifiedFeatures.isEmpty()) {
                // 删除需要更新的旧数据
                Set<String> updateCiCodes = modifiedFeatures.stream()
                        .map(FlowSystemAssociatedFeatures::getLinkedCiCode)
                        .collect(Collectors.toSet());
                BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery()
                        .filter(QueryBuilders.termsQuery("linkedCiCode.keyword", updateCiCodes))
                        .filter(QueryBuilders.termQuery("creator.keyword", loginCode));
                flowSystemAssociatedFeaturesPrivateDao.deleteByQuery(queryBuilder, true);

                // 保存新的关联关系数据
                for (FlowSystemAssociatedFeatures feature : modifiedFeatures) {
                    feature.setId(ESUtil.getUUID());
                    feature.setCreator(loginCode);
                }
                flowSystemAssociatedFeaturesPrivateDao.saveOrUpdateBatch(new ArrayList<>(modifiedFeatures));
                // 同步CI数据
                for (String linkedCiCode : updateCiCodes) {
                    CcCiInfo designCiByCode = ciSwitchSvc.getCiByCode(linkedCiCode, null, LibType.DESIGN);
                    if (designCiByCode == null) {
                        continue;
                    }
                    CcCiInfo privateCiByCode = ciSwitchSvc.getCiByCode(linkedCiCode, loginCode, LibType.PRIVATE);
                    if (privateCiByCode != null) {
                        privateCiByCode.setAttrs(designCiByCode.getAttrs());
                        privateCiByCode.getCi().setPublicVersion(designCiByCode.getCi().getPublicVersion());
                        ciSwitchSvc.saveOrUpdateCI(privateCiByCode, LibType.PRIVATE);
                    } else {
                        designCiByCode.getCi().setId(null);
                        designCiByCode.getCi().setOwnerCode(loginCode);
                        ciSwitchSvc.saveOrUpdateCI(designCiByCode, LibType.PRIVATE);
                    }

                    // 如果是指标，同步指标检查数据
                    if ("指标".equals(classCode)) {
                        syncIndicatorDetectionData(Collections.singleton(linkedCiCode), loginCode);
                    }
                }
            }
        }
        return 1L;
    }


    /**
     * 处理关联要素的返回结果
     *
     * @param classCode
     * @param allCiInfo
     * @param ciCodeMap
     * @param flowSystemAssociatedFeatures
     */
    private List<FlowSystemAssociatedFeaturesDto> handlerAssociateResult(String classCode, List<CcCiInfo> allCiInfo, Map<String, Set<String>> ciCodeMap,
                                                                         List<FlowSystemAssociatedFeatures> flowSystemAssociatedFeatures, LibType libType) {
        List<FlowSystemAssociatedFeaturesDto> resultList = new ArrayList<>();
        if (Objects.equals(classCode, "制度") || Objects.equals(classCode, "标准")
                || Objects.equals(classCode, "指标") || Objects.equals(classCode, "术语") || Objects.equals(classCode, "要素")
                || Objects.equals(classCode, "风险") || Objects.equals(classCode, "场景") || Objects.equals(classCode, "档案")) {
            Set<String> ciCodesSet = new HashSet<>();
            Map<String, CcCiInfo> cCiInfoMap = new HashMap<>();
            for (CcCiInfo ccCiInfo : allCiInfo) {
                cCiInfoMap.put(ccCiInfo.getCi().getCiCode(), ccCiInfo);
            }
            if ((Objects.equals(classCode, "制度") || Objects.equals(classCode, "标准"))
                    && !CollectionUtils.isEmpty(ciCodeMap)) {
                for (Map.Entry<String, Set<String>> app : ciCodeMap.entrySet()) {
                    CcCiInfo ccCiInfo = cCiInfoMap.get(app.getKey());
                    if (ccCiInfo != null) {
                        Map<String, String> attrs = ccCiInfo.getAttrs();
                        Set<String> activeSet = app.getValue();
                        if (!CollectionUtils.isEmpty(activeSet)) {
                            attrs.put("相关活动", Strings.join(activeSet, ","));
                            FlowSystemAssociatedFeaturesDto flowSystemAssociatedFeaturesDto = new FlowSystemAssociatedFeaturesDto();
                            flowSystemAssociatedFeaturesDto.setAttrs(attrs);
                            flowSystemAssociatedFeaturesDto.setLinkedCiCode(app.getKey());
                            resultList.add(flowSystemAssociatedFeaturesDto);
                        }
                    }
                    ciCodesSet.add(app.getKey());
                }
            }
            if (!CollectionUtils.isEmpty(flowSystemAssociatedFeatures)) {
                for (FlowSystemAssociatedFeatures flowSystemAssociatedFeature : flowSystemAssociatedFeatures) {
                    if (ciCodesSet.contains(flowSystemAssociatedFeature.getLinkedCiCode())) {
                        continue;
                    }
                    FlowSystemAssociatedFeaturesDto flowSystemAssociatedFeaturesDto = new FlowSystemAssociatedFeaturesDto();
                    BeanUtils.copyProperties(flowSystemAssociatedFeature, flowSystemAssociatedFeaturesDto);
                    CcCiInfo ccCiInfo = cCiInfoMap.get(flowSystemAssociatedFeature.getLinkedCiCode());
                    if (ccCiInfo != null) {
                        Map<String, String> attrs = ccCiInfo.getAttrs();
                        CcCi ci = ccCiInfo.getCi();
                        if (!CollectionUtils.isEmpty(ciCodeMap)) {
                            Set<String> relaInfo = ciCodeMap.get(ci.getCiCode());
                            if (!CollectionUtils.isEmpty(relaInfo)) {
                                attrs.put("相关活动", String.join("，", relaInfo));
                            }
                        }
                        flowSystemAssociatedFeaturesDto.setAttrs(attrs);
                        flowSystemAssociatedFeaturesDto.setCi(ccCiInfo.getCi());
                    } else {
                        continue;
                    }
                    ciCodesSet.add(flowSystemAssociatedFeature.getLinkedCiCode());
                    resultList.add(flowSystemAssociatedFeaturesDto);
                }
            }
        } else if (Objects.equals(classCode, "关联应用")) {
            Map<String, CcCiInfo> ciInfoMap = new HashMap<>();
            for (CcCiInfo ccCiInfo : allCiInfo) {
                ciInfoMap.put(ccCiInfo.getCi().getCiCode(), ccCiInfo);
            }
            if (!CollectionUtils.isEmpty(ciCodeMap)) {
                log.info(classCode + " ci信息：" + JSONObject.toJSONString(ciInfoMap));
                for (Map.Entry<String, Set<String>> app : ciCodeMap.entrySet()) {
                    Map<String, String> contentMap = new HashMap<>();
                    String key = app.getKey();
                    if (key != null) {
                        CcCiInfo ccCi = ciInfoMap.get(key);
                        if (ccCi != null && !CollectionUtils.isEmpty(ccCi.getAttrs())) {
                            Map<String, String> attrs = ccCi.getAttrs();
                            String appName = attrs.get("系统名称");
                            contentMap.put("应用系统", appName);
                        }
                    }
                    if (!StringUtils.isEmpty(contentMap.get("应用系统"))) {
                        contentMap.put("相关活动", String.join(",", app.getValue()));

                        FlowSystemAssociatedFeaturesDto flowSystemAssociatedFeaturesDto = new FlowSystemAssociatedFeaturesDto();
                        flowSystemAssociatedFeaturesDto.setAttrs(contentMap);
                        resultList.add(flowSystemAssociatedFeaturesDto);
                    }
                }
            }
        } else if (Objects.equals(classCode, "业务对象")) {
            Map<String, CcCiInfo> ciInfoMap = new HashMap<>();
            for (CcCiInfo ccCiInfo : allCiInfo) {
                ciInfoMap.put(ccCiInfo.getCi().getCiCode(), ccCiInfo);
            }
            if (!CollectionUtils.isEmpty(ciCodeMap)) {
                for (Map.Entry<String, Set<String>> app : ciCodeMap.entrySet()) {
                    Map<String, String> contentMap = new HashMap<>();
                    CcCiInfo ccCiInfo = ciInfoMap.get(app.getKey());
                    if (ccCiInfo != null) {
                        Map<String, String> attrs = ccCiInfo.getAttrs();
                        String form = attrs.get("表单名称");
                        if (!StringUtils.isEmpty(form)) {
                            contentMap.put("关联表单", form);
                        }
                        String business = attrs.get("关联业务对象");
                        if (StringUtils.isEmpty(business)) {
                            continue;
                        }
                        JSONArray jsonArr = JSONObject.parseArray(business);
                        if (CollectionUtils.isEmpty(jsonArr)) {
                            continue;
                        }
                        StringBuilder sb = new StringBuilder();
                        for (int i = 0; i < jsonArr.size(); i++) {
                            JSONObject jsonObject = jsonArr.getJSONObject(i);
                            if (Objects.equals(i, jsonArr.size() - 1)) {
                                sb.append(jsonObject.getString("primary"));
                            } else {
                                sb.append(jsonObject.getString("primary")).append(",");
                            }
                        }
                        if (StringUtils.isEmpty(sb.toString())) {
                            continue;
                        }
                        contentMap.put("业务对象名称", sb.toString());
                    }
                    if (!StringUtils.isEmpty(contentMap.get("关联表单")) || !StringUtils.isEmpty(contentMap.get("业务对象名称"))) {
                        contentMap.put("相关活动", String.join(",", app.getValue()));

                        FlowSystemAssociatedFeaturesDto flowSystemAssociatedFeaturesDto = new FlowSystemAssociatedFeaturesDto();
                        flowSystemAssociatedFeaturesDto.setAttrs(contentMap);
                        resultList.add(flowSystemAssociatedFeaturesDto);
                    }
                }
            }
        } else if (Objects.equals(classCode, "表单")) {
            Map<String, CcCiInfo> ciInfoMap = new HashMap<>();
            for (CcCiInfo ccCiInfo : allCiInfo) {
                ciInfoMap.put(ccCiInfo.getCi().getCiCode(), ccCiInfo);
            }
            if (!CollectionUtils.isEmpty(ciCodeMap)) {
                for (Map.Entry<String, Set<String>> app : ciCodeMap.entrySet()) {
                    CcCiInfo ccCiInfo = ciInfoMap.get(app.getKey());
                    if (ccCiInfo != null) {
                        Map<String, String> attrs = ccCiInfo.getAttrs();
                        Set<String> formSet = app.getValue();
                        if (!CollectionUtils.isEmpty(formSet)) {
                            List<ESCIInfo> esciInfoList = ciSwitchSvc.getCiByCodes(new ArrayList<>(formSet),
                                    libType.equals(LibType.PRIVATE) ? SysUtil.getCurrentUserInfo().getLoginCode() : null, libType);
                            if (!CollectionUtils.isEmpty(esciInfoList)) {
                                List<String> activeList = new ArrayList<>();
                                // 类型 1：输入表单 2：输出表单 3：输入和输出表单
                                int type = 0;
                                for (ESCIInfo ciInfo : esciInfoList) {
                                    Map<String, Object> attrMap = ciInfo.getAttrs();
                                    String activeName = String.valueOf(attrMap.get("活动名称"));
                                    activeList.add(activeName);

                                    Object inputFormObj = attrMap.get("输入表单");
                                    Object outputFormObj = attrMap.get("输出表单");
                                    if (inputFormObj != null && !StringUtils.isEmpty(String.valueOf(inputFormObj))) {
                                        String inputForm = String.valueOf(inputFormObj);
                                        JSONArray inputJson = JSONObject.parseArray(inputForm);
                                        if (!CollectionUtils.isEmpty(inputJson)) {
                                            for (int i = 0; i < inputJson.size(); i++) {
                                                JSONObject jsonObject = inputJson.getJSONObject(i);
                                                String ciCodeInfo = jsonObject.getString("ciCode");
                                                if (Objects.equals(ciCodeInfo, app.getKey())) {
                                                    if (Objects.equals(type, 2)) {
                                                        type = 3;
                                                    } else {
                                                        type = 1;
                                                    }
                                                    break;
                                                }
                                            }
                                        }
                                    }
                                    if (outputFormObj != null && !StringUtils.isEmpty(String.valueOf(outputFormObj))) {
                                        String outputForm = String.valueOf(outputFormObj);
                                        JSONArray outputJson = JSONObject.parseArray(outputForm);
                                        if (!CollectionUtils.isEmpty(outputJson)) {
                                            for (int i = 0; i < outputJson.size(); i++) {
                                                JSONObject jsonObject = outputJson.getJSONObject(i);
                                                String ciCodeInfo = jsonObject.getString("ciCode");
                                                if (Objects.equals(ciCodeInfo, app.getKey())) {
                                                    if (Objects.equals(type, 1)) {
                                                        type = 3;
                                                        break;
                                                    } else {
                                                        type = 2;
                                                        break;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                                attrs.put("相关活动", Strings.join(activeList, ","));
                                if (Objects.equals(type, 1)) {
                                    attrs.put("输入/输出类型", "输入");
                                } else if (Objects.equals(type, 2)) {
                                    attrs.put("输入/输出类型", "输出");
                                } else if (Objects.equals(type, 3)) {
                                    attrs.put("输入/输出类型", "输入，输出");
                                }
                            }
                            FlowSystemAssociatedFeaturesDto flowSystemAssociatedFeaturesDto = new FlowSystemAssociatedFeaturesDto();
                            flowSystemAssociatedFeaturesDto.setAttrs(attrs);
                            resultList.add(flowSystemAssociatedFeaturesDto);
                        }
                    }
                }
            }
        } else if (Objects.equals(classCode, "岗位角色")) {
            Map<String, CcCiInfo> ciInfoMap = new HashMap<>();
            for (CcCiInfo ccCiInfo : allCiInfo) {
                ciInfoMap.put(ccCiInfo.getCi().getCiCode(), ccCiInfo);
            }
            if (!CollectionUtils.isEmpty(ciCodeMap)) {
                for (Map.Entry<String, Set<String>> app : ciCodeMap.entrySet()) {
                    CcCiInfo ccCiInfo = ciInfoMap.get(app.getKey());
                    if (ccCiInfo != null) {
                        Map<String, String> attrs = ccCiInfo.getAttrs();
                        Set<String> activeSet = app.getValue();
                        if (!CollectionUtils.isEmpty(activeSet)) {
                            List<ESCIInfo> esciInfoList = ciSwitchSvc.getCiByCodes(new ArrayList<>(activeSet), libType.equals(LibType.PRIVATE) ? SysUtil.getCurrentUserInfo().getLoginCode() : null, libType);
                            if (!CollectionUtils.isEmpty(esciInfoList)) {
                                List<String> activeList = new ArrayList<>();
                                for (ESCIInfo ciInfo : esciInfoList) {
                                    Map<String, Object> attrMap = ciInfo.getAttrs();
                                    String activeName = String.valueOf(attrMap.get("活动名称"));
                                    activeList.add(activeName);
                                }
                                attrs.put("相关活动", Strings.join(activeList, ","));
                            }
                            FlowSystemAssociatedFeaturesDto flowSystemAssociatedFeaturesDto = new FlowSystemAssociatedFeaturesDto();
                            flowSystemAssociatedFeaturesDto.setAttrs(attrs);
                            resultList.add(flowSystemAssociatedFeaturesDto);
                        }
                    }
                }
            }
        }
        return resultList;
    }

    /**
     * 已发布的流程图关联子流程信息
     *
     * @param ciCodes
     * @return
     */
    private Map<String, String> diagramRelaCiMap(Set<String> ciCodes, LibType libType) {
        List<EamDiagramRelationSys> diagramRelationSysList = new ArrayList<>();
        if (libType.equals(LibType.PRIVATE)) {
            for (String ciCode : ciCodes) {
                diagramRelationSysList = eamDiagramRelationSysService.findFlowSystemDiagramRelationPrivate(ciCode, "flowDiagram");
            }
        } else {
            diagramRelationSysList = eamDiagramRelationSysService.findDiagramRelationSysList(ciCodes, "flowDiagram");
        }

        if (CollectionUtils.isEmpty(diagramRelationSysList)) {
            return null;
        }
        Map<String, String> diagramRelaCiMap = new HashMap<>();
        Map<String, List<EamDiagramRelationSys>> diamSysListMap = diagramRelationSysList.stream().filter(sys -> !StringUtils.isEmpty(sys.getEsSysId()))
                .collect(Collectors.groupingBy(sys -> sys.getEsSysId()));
        for (Map.Entry<String, List<EamDiagramRelationSys>> item : diamSysListMap.entrySet()) {
            List<EamDiagramRelationSys> diagRelaList = item.getValue();
            if (CollectionUtils.isEmpty(diagRelaList)) {
                continue;
            } else if (Objects.equals(diagRelaList.size(), 1)) {
                EamDiagramRelationSys diagramSys = diagRelaList.get(0);
                diagramRelaCiMap.put(diagramSys.getDiagramEnergy(), diagramSys.getEsSysId());
            } else if (diagRelaList.size() > 1) {
                Optional<EamDiagramRelationSys> first = diagRelaList.stream().sorted(new Comparator<EamDiagramRelationSys>() {
                    @Override
                    public int compare(EamDiagramRelationSys o1, EamDiagramRelationSys o2) {
                        return (int) (o2.getModifyTime() - o1.getModifyTime());
                    }
                }).findFirst();
                EamDiagramRelationSys diagramSys = first.get();
                if (diagramSys != null) {
                    diagramRelaCiMap.put(diagramSys.getDiagramEnergy(), diagramSys.getEsSysId());
                }
            }
        }
        return diagramRelaCiMap;
    }

    /**
     * 流程图信息
     *
     * @param diagramRelaCiMap
     * @return
     */
    private List<ESDiagramDTO> esDiagramDTOList(Map<String, String> diagramRelaCiMap) {
        Long[] diagramIds = diagramApiClient.queryDiagramInfoBydEnergy(diagramRelaCiMap.keySet().toArray(new String[0]));
        if (diagramIds == null || diagramIds.length <= 0) {
            return null;
        }
        List<ESDiagramDTO> esDiagramDTOList = diagramApiClient.queryDiagramInfoByIds(diagramIds, null, false, false);
        if (CollectionUtils.isEmpty(esDiagramDTOList)) {
            return null;
        }
        return esDiagramDTOList;
    }

    /**
     * 初始制度和标准数据
     *
     * @param institution
     * @param ciCodeMap
     */
    private void handlerInstitution(String institution, Map<String, Set<String>> ciCodeMap, Map<String, String> attrs) {
        if (!StringUtils.isEmpty(institution)) {
            JSONArray jsonArray = JSONObject.parseArray(institution);
            if (!CollectionUtils.isEmpty(jsonArray)) {
                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONObject jsonObject = jsonArray.getJSONObject(i);
                    String ciCodeInfo = jsonObject.getString("ciCode");
                    if (!StringUtils.isEmpty(ciCodeInfo)) {
                        ciCodeMap.computeIfAbsent(ciCodeInfo, key -> new HashSet<>()).add(attrs.get("活动名称"));
                    }
                }
            }
        }
    }

    private Map<String, Map<String, String>> getStringMapMap(String diagramIds, String ciCode, LibType libType) {
        Map<String, Map<String, String>> featureMaps = new HashMap<>();
        List<ESDiagramDTO> diagrams = diagramSvcV2.queryDiagramByIds(Arrays.asList(diagramIds));
        CcCiInfo mainCiInfo = ciSwitchSvc.getCiByCode(ciCode, SysUtil.getCurrentUserInfo().getLoginCode(), libType);
        List<String> activityCodes = new ArrayList<>();
        List<String> kcpCodes = new ArrayList<>();
        List<String> qzCodes = new ArrayList<>();
        List<String> hzCodes = new ArrayList<>();
        Map<String, String> kcpRelations = new HashMap<>();
        if (!diagrams.isEmpty()) {
            ESDiagramInfoDTO diagram = diagrams.get(0).getDiagram();
            List<ESDiagramModel> models = diagram.getModelList();
            for (ESDiagramModel model : models) {
                for (ESDiagramNode node : model.getNodeDataArray()) {
                    JSONObject nodeData = JSONObject.parseObject(node.getNodeJson());
                    String className = nodeData.getString("className");
                    if ("活动".equals(className)) {
                        activityCodes.add(nodeData.getString("ciCode"));
                    } else if ("KCP".equals(className)) {
                        kcpCodes.add(nodeData.getString("ciCode"));
                    }
                }
                if (model.getLinkDataArray() == null) {
                    continue;
                }
                for (ESDiagramLink link : model.getLinkDataArray()) {
                    JSONObject linkData = JSONObject.parseObject(link.getLinkJson());
                    String className = linkData.getString("className");
                    if ("KCP-包含".equals(className)) {
                        String relationCode = linkData.getString("rltCode");
                        String[] parts = relationCode.split("_");
                        if (parts.length == 4 && kcpCodes.contains(parts[1]) && activityCodes.contains(parts[3])) {
                            kcpRelations.put(parts[3], parts[1]);
                        }
                    }
                }
            }

            List<ESCIInfo> relatedCis = ciSwitchSvc.getCiByCodes(activityCodes, SysUtil.getCurrentUserInfo().getLoginCode(), libType);
            activityCodes.forEach(code ->
                    featureMaps.computeIfAbsent("活动", k -> new HashMap<>()).put(code, "活动")
            );
            for (ESCIInfo relatedCi : relatedCis) {
                Map<String, Object> attrs = relatedCi.getAttrs();
                String activityName = (String) attrs.get("活动名称");
                String ciCode1 = relatedCi.getCiCode();

                if (ciCode1 != null) {
                    if (kcpRelations.containsKey(ciCode1)) {
                        featureMaps.computeIfAbsent("KCP", k -> new HashMap<>()).put(kcpRelations.get(ciCode1), activityName);
                    }
                }
                String[] keys = {"责任角色", "关联制度", "关联标准", "输入表单", "输出表单", "关联应用", "关联模块"};
                for (String key : keys) {
                    String value = (String) attrs.get(key);
                    if (value != null) {
                        JSONArray objects = JSONArray.parseArray(value);
                        for (int i = 0; i < objects.size(); i++) {
                            String code = objects.getJSONObject(i).getString("ciCode");
                            String primary = objects.getJSONObject(i).getString("primary");
                            switch (key) {
                                case "关联应用":
                                case "关联模块":
                                    Map<String, String> appOrModuleMap = featureMaps.computeIfAbsent(key, k -> new HashMap<>());
                                    appOrModuleMap.put(activityName, primary);
                                    break;
                                case "关联标准":
                                    featureMaps.computeIfAbsent("标准", k -> new HashMap<>()).put(code, activityName);
                                    break;
                                case "关联制度":
                                    featureMaps.computeIfAbsent("制度", k -> new HashMap<>()).put(code, activityName);
                                    break;
                                case "输入表单":
                                    featureMaps.computeIfAbsent("输入表单", k -> new HashMap<>()).put(code, activityName);
                                    break;
                                case "输出表单":
                                    featureMaps.computeIfAbsent("输出表单", k -> new HashMap<>()).put(code, activityName);
                                    break;
                                case "责任角色":
                                    featureMaps.computeIfAbsent("岗位角色", k -> new HashMap<>()).put(code, activityName);
                                    break;
                            }
                            break;
                        }
                    }
                }
            }
        }
        Map<String, String> mainCiAttrs = mainCiInfo.getAttrs();
        String[] categories = {"制度", "标准", "术语", "输入表单", "输出表单", "要素"};
        for (String category : categories) {
            String value = null;
            if (!category.equals("输入表单") || !category.equals("输出表单")) {
                for (Map.Entry<String, String> entry : mainCiAttrs.entrySet()) {
                    if (entry.getKey().contains(category)) {
                        value = entry.getValue();
                        break;
                    }
                }
            } else {
                value = mainCiAttrs.get(category);
            }
            if (StringUtils.isEmpty(value)) {
                continue;
            }
            JSONArray objects = JSONArray.parseArray(value);
            for (int i = 0; i < objects.size(); i++) {
                String ciCode1 = objects.getJSONObject(i).getString("ciCode");
                featureMaps.computeIfAbsent(category, k -> new HashMap<>())
                        .putIfAbsent(ciCode1, "");
            }
        }
        return featureMaps;
    }

    private List<FlowSystemAssociatedFeaturesDto> buildFeaturesList(String key, Map<String, String> featureMap, LibType libType) {
        List<FlowSystemAssociatedFeaturesDto> features = new ArrayList<>();
        List<String> ciCodes = new ArrayList<>(featureMap.keySet());
        List<ESCIInfo> ciInfos = ciSwitchSvc.getCiByCodes(ciCodes, SysUtil.getCurrentUserInfo().getLoginCode(), libType);
        // 构建 DTO 列表
        List<CcCiInfo> ccCiInfos = commSvc.transEsInfoList(ciInfos, true);
        for (CcCiInfo ciInfo : ccCiInfos) {
            FlowSystemAssociatedFeaturesDto featureDto = new FlowSystemAssociatedFeaturesDto();
            featureDto.setLinkedCiCode(ciInfo.getCi().getCiCode());
            featureDto.setCi(ciInfo.getCi());
            Map<String, String> attrs = ciInfo.getAttrs();
            if (!key.equals("术语") && !key.equals("要素")) {
                attrs.put("相关活动", featureMap.get(ciInfo.getCi().getCiCode()));
            }
            if ("输入表单".equals(key)) {
                attrs.put("输入/输出类型", "输入");
            } else if ("输出表单".equals(key)) {
                attrs.put("输入/输出类型", "输出");
            }
            featureDto.setAttrs(safeGetAttrs(attrs));
            features.add(featureDto);
        }
        return features;
    }

    private void mergingInputAndOutputForms(Map<String, Map> result) {
        List<Object> combinedFeatures = new ArrayList<>();
        // 提取输入表单的 Features
        Map<String, Object> inputFormMap = result.get("输入表单");
        Object inputFeatures = inputFormMap != null ? inputFormMap.get("dataList") : null;
        if (inputFeatures instanceof List) {
            combinedFeatures.addAll((List<?>) inputFeatures);
        }
        // 提取输出表单的 Features
        Map<String, Object> outputFormMap = result.get("输出表单");
        Object outputFeatures = outputFormMap != null ? outputFormMap.get("dataList") : null;
        if (outputFeatures instanceof List) {
            combinedFeatures.addAll((List<?>) outputFeatures);
        }
        // 如果 Features 都为空，则赋值为新的空数组
        if (inputFeatures == null && outputFeatures == null) {
            combinedFeatures = new ArrayList<>();
        }
        // 创建新的表单 Map
        Map<String, Object> formMap = new HashMap<>();
        formMap.put("dataList", combinedFeatures);
        formMap.put("attrDefs", inputFormMap.get("attrDefs"));
        // 替换 result 中的键
        result.remove("输入表单");
        result.remove("输出表单");
        result.put("表单", formMap);
    }

    private Map<String, Object> buildBusinessObjectsResult(Map<String, Map<String, String>> featureMaps, LibType libType) {
        Map<String, Object> result = new HashMap<>();
        List<FlowSystemAssociatedFeaturesDto> features = new ArrayList<>();
        // 构建属性定义
        List<CcCiAttrDef> attrDefs = Arrays.asList(
                createAttrDef("业务对象名称", 3),
                createAttrDef("关联表单", 3),
                createAttrDef("相关活动", 3)
        );
        result.put("attrDefs", attrDefs);
        // 获取输入表单和输出表单的映射，并确保它们不为空
        Map<String, String> inputFormMap = featureMaps.getOrDefault("输入表单", new HashMap<>());
        Map<String, String> outputFormMap = featureMaps.getOrDefault("输出表单", new HashMap<>());
        if (inputFormMap.isEmpty() && outputFormMap.isEmpty()) {
            result.put("dataList", new ArrayList<>());
            return result;
        }
        // 合并输入表单和输出表单的映射
        Map<String, String> combinedMap = new HashMap<>(inputFormMap);
        combinedMap.putAll(outputFormMap);
        // 获取相关的 CI 信息
        List<ESCIInfo> relatedCis = ciSwitchSvc.getCiByCodes(new ArrayList<>(combinedMap.keySet()), SysUtil.getCurrentUserInfo().getLoginCode(), libType);
        for (ESCIInfo ciInfo : relatedCis) {
            String associatedBusinessObjects = (String) ciInfo.getAttrs().get("关联业务对象");
            if (associatedBusinessObjects != null) {
                JSONArray objectsArray = JSONArray.parseArray(associatedBusinessObjects);
                for (int i = 0; i < objectsArray.size(); i++) {
                    JSONObject object = objectsArray.getJSONObject(i);
                    String primary = object.getString("primary");
                    String activityName = (String) ciInfo.getAttrs().get("表单名称");
                    String formValue = combinedMap.get(ciInfo.getCiCode());
                    FlowSystemAssociatedFeaturesDto featureDto = new FlowSystemAssociatedFeaturesDto();
                    Map<String, String> attrs = new HashMap<>();
                    attrs.put("业务对象名称", primary);
                    attrs.put("关联表单", activityName);
                    attrs.put("相关活动", formValue);
                    featureDto.setAttrs(safeGetAttrs(attrs));
                    features.add(featureDto);
                }
            }
        }
        result.put("dataList", features);
        return result;
    }

    private Map<String, Object> buildApplicationArchitectureResult(Map<String, Map<String, String>> featureMaps) {
        Map<String, Object> result = new HashMap<>();
        List<CcCiAttrDef> attrDefs = new ArrayList<>();
        attrDefs.add(createAttrDef("应用系统", 3));
        attrDefs.add(createAttrDef("应用模块", 3));
        attrDefs.add(createAttrDef("相关活动", 3));
        result.put("attrDefs", attrDefs);
        List<FlowSystemAssociatedFeaturesDto> features = new ArrayList<>();
        // 检查并获取"关联应用"映射
        Map<String, String> applicationArchitectures = featureMaps.getOrDefault("关联应用", Collections.emptyMap());
        if (applicationArchitectures.isEmpty()) {
            result.put("dataList", features);
            return result;
        }
        Map<String, String> moduleRelations = featureMaps.getOrDefault("关联模块", Collections.emptyMap());

        for (Map.Entry<String, String> entry : applicationArchitectures.entrySet()) {
            FlowSystemAssociatedFeaturesDto featureDto = new FlowSystemAssociatedFeaturesDto();
            Map<String, String> attrs = new HashMap<>();
            attrs.put("应用系统", entry.getValue());
            attrs.put("应用模块", moduleRelations.getOrDefault(entry.getKey(), ""));
            attrs.put("相关活动", entry.getKey());
            featureDto.setAttrs(safeGetAttrs(attrs));
            features.add(featureDto);
        }
        result.put("dataList", features);
        return result;
    }

    private void otherResult(Map<String, Map> result, String ciCode, LibType libType) {
        String[] categories = {"风险", "指标", "档案"};
        for (String category : categories) {
            Map<String, Object> resultEntry = new HashMap<>();
            List<FlowSystemAssociatedFeatures> flowSystemAssociatedFeatures = new ArrayList<>();
            CcCiClassInfo ciClassByClassCode = iciClassSvc.getCiClassByClassCode(category);
            List<CcCiAttrDef> attrDefs = ciClassByClassCode.getAttrDefs();
            resultEntry.put("attrDefs", attrDefs);
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
            boolQueryBuilder.filter(QueryBuilders.termsQuery("ciCode.keyword", ciCode));
            boolQueryBuilder.filter(QueryBuilders.termQuery("classCode.keyword", category));
            SysUser sysUser = SysUtil.getCurrentUserInfo();
            boolQueryBuilder.filter(QueryBuilders.termQuery("creator.keyword", sysUser.getLoginCode()));
            flowSystemAssociatedFeatures = flowSystemAssociatedFeaturesPrivateDao.getListByQueryScroll(boolQueryBuilder);

            if (CollectionUtils.isEmpty(flowSystemAssociatedFeatures)) {
                resultEntry.put("dataList", new ArrayList<>());
            } else {
                Set<String> linkedCiCodes = flowSystemAssociatedFeatures.stream()
                        .filter(o -> o.getLinkedCiCode() != null)
                        .map(FlowSystemAssociatedFeatures::getLinkedCiCode)
                        .collect(Collectors.toSet());
                List<FlowSystemAssociatedFeaturesDto> features = new ArrayList<>();
                List<ESCIInfo> relatedCis = ciSwitchSvc.getCiByCodes(new ArrayList<>(linkedCiCodes), SysUtil.getCurrentUserInfo().getLoginCode(), LibType.PRIVATE);
                List<CcCiInfo> ccCiInfos = commSvc.transEsInfoList(relatedCis, true);

                // 按创建时间排序
                ccCiInfos.sort((ci1, ci2) -> {
                    Long time1 = ci1.getCi().getCreateTime();
                    Long time2 = ci2.getCi().getCreateTime();
                    // 处理可能的空值
                    if (time1 == null) time1 = 0L;
                    if (time2 == null) time2 = 0L;
                    return time1.compareTo(time2);  // 升序

                });
                for (CcCiInfo relatedCi : ccCiInfos) {
                    FlowSystemAssociatedFeaturesDto featureDto = new FlowSystemAssociatedFeaturesDto();
                    featureDto.setLinkedCiCode(relatedCi.getCi().getCiCode());
                    featureDto.setCi(relatedCi.getCi());
                    featureDto.setAttrs(safeGetAttrs(relatedCi.getAttrs()));
                    features.add(featureDto);
                }//对数据进行排序用relatedCi.getCi().getCreateTime()进行排序
                resultEntry.put("dataList", features);
            }
            if (category.equals("风险")) {
                result.put("流程风险", resultEntry);
            } else if (category.equals("指标")) {
                result.put("流程绩效", resultEntry);
            } else if (category.equals("档案")) {
                result.put("档案管理", resultEntry);
            }
        }
    }

    private CcCiAttrDef createAttrDef(String proName, int proType) {
        CcCiAttrDef attrDef = new CcCiAttrDef();
        attrDef.setProName(proName);
        attrDef.setProType(proType);
        return attrDef;
    }

    private void syncIndicatorDetectionData(Set<String> ciCodes, String loginCode) {
        for (String ciCode : ciCodes) {

            BoolQueryBuilder designQuery = QueryBuilders.boolQuery()
                    .filter(QueryBuilders.termQuery("ciCode.keyword", ciCode));
            List<IndicatorDetectionInformationAssociation> designDetections =
                    informationAssociationDao.getListByQuery(designQuery);
            if (!designDetections.isEmpty()) {
                // 删除私有库中已存在的数据
                BoolQueryBuilder privateQuery = QueryBuilders.boolQuery()
                        .filter(QueryBuilders.termQuery("ciCode.keyword", ciCode))
                        .filter(QueryBuilders.termQuery("creator.keyword", loginCode));
                informationAssociationDao.deleteByQuery(privateQuery, true);

                // 同步设计库数据到私有库
                for (IndicatorDetectionInformationAssociation detection : designDetections) {
                    detection.setId(ESUtil.getUUID());
                    detection.setCreator(loginCode);
                }
                informationAssociationDao.saveOrUpdateBatch(designDetections);
            }
        }
    }

}