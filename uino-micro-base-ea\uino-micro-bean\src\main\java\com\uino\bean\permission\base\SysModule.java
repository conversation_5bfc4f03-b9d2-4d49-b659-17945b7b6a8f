
package com.uino.bean.permission.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * mapping-table: 系统模块表[SYS_MODULE]
 *
 * <AUTHOR>
 */
@ApiModel(value="系统模块表",description = "系统模块表")
@Data
public class SysModule implements Serializable {
	private static final long serialVersionUID = 1L;

	/** ID */
	@ApiModelProperty(value="id",example = "123")
	private Long id;

	/** 模块代码 */
	@ApiModelProperty(value="模块代码")
	private String moduleCode;

	/** 模块名称 */
	@ApiModelProperty(value="模块名称", example = "模块名称", required = true)
	private String moduleName;

    /**
     * 模块标识
     */
    @ApiModelProperty(value="模块标识", example = "/123", required = true)
    private String moduleSign;

	/** 用户自定义名称 */
	@ApiModelProperty(value="用户自定义名称")
	private String label;

	/** 父节点 */
	@ApiModelProperty(value="父节点", required = true)
	private Long parentId;

	/** 模块描述 */
	@ApiModelProperty(value="模块描述")
	private String moduDesc;

	/** 显示排序 */
	@ApiModelProperty(value="显示排序")
	private Integer orderNo;

	/** 链接地址 */
	@ApiModelProperty(value="链接地址")
	private String moduleUrl;

	/** 状态 */
	@ApiModelProperty(value="状态", example = "0:不显示, 1:显示", required = true)
	private Integer status = 1;

	@ApiModelProperty(value="禁用状态", example = "true:禁用, false:启用", required = true)
	private Boolean isDisable = false;

	@ApiModelProperty(value="模块类型", example = "0:菜单, 1:按钮 2：资产仓库小组，3：资产仓库目录", required = true)
	private Integer moduleType = 0;

	/** 是否初始化菜单,初始化菜单不可删除 */
	@ApiModelProperty(value="是否初始化菜单,初始化菜单不可删除")
	private Boolean isInit;

	/** 所属域 */
	@ApiModelProperty(value="所属域id",example = "123")
	private Long domainId;

	/** 创建人 */
	@ApiModelProperty(value="创建人",example = "mike")
	private String creator;

	/** 修改人 */
	@ApiModelProperty(value="修改人",example = "mike")
	private String modifier;

	/** 创建时间 */
	@ApiModelProperty(value="创建时间")
	private Long createTime;

	/** 修改时间 */
	@ApiModelProperty(value="修改时间")
	private Long modifyTime;

	/** 模块图片 */
	@ApiModelProperty(value="模块icon图标")
	private String moduleImg;


	@ApiModelProperty(value="模块图片")
	private String modulePic;

	/** 自定义图片 */
	@ApiModelProperty(value="自定义图片")
	private String customImg;

	/**
	 * 自定义颜色
	 */
	@ApiModelProperty(value="自定义颜色",example = "red")
	private String customColor;

	@ApiModelProperty(value = "业务组")
	private Integer businessGroup;

	@ApiModelProperty(value = "业务模块")
	private Integer businessModule;

	@ApiModelProperty(value = "存放资产类型-枚举：1=视图；2=模型；3=方案")
	private List<Integer> assetType;

	@ApiModelProperty(value = "视图类型")
	private List<TypeVo> diagramList;

	@ApiModelProperty(value = "模型类型")
	private List<TypeVo> modelList;

	@ApiModelProperty(value = "方案类型")
	private List<TypeVo> schemeList;


	public String getModulePic() {
		return modulePic;
	}

	public void setModulePic(String modulePic) {
		this.modulePic = modulePic;
	}

	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getModuleCode() {
		return this.moduleCode;
	}

	public void setModuleCode(String moduleCode) {
		this.moduleCode = moduleCode;
	}

	public String getModuleName() {
		return this.moduleName;
	}

	public void setModuleName(String moduleName) {
		this.moduleName = moduleName;
	}

    public String getModuleSign() {
        return moduleSign;
    }

    public void setModuleSign(String moduleSign) {
        this.moduleSign = moduleSign;
    }

	public Long getParentId() {
		return this.parentId;
	}

	public void setParentId(Long parentId) {
		this.parentId = parentId;
	}

	public String getModuDesc() {
		return this.moduDesc;
	}

	public void setModuDesc(String moduDesc) {
		this.moduDesc = moduDesc;
	}

	public Integer getOrderNo() {
		return this.orderNo;
	}

	public void setOrderNo(Integer orderNo) {
		this.orderNo = orderNo;
	}

	public String getModuleUrl() {
		return this.moduleUrl;
	}

	public void setModuleUrl(String moduleUrl) {
		this.moduleUrl = moduleUrl;
	}

	public Integer getStatus() {
		return this.status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Long getDomainId() {
		return this.domainId;
	}

	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}

	public String getCreator() {
		return this.creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public String getModifier() {
		return this.modifier;
	}

	public void setModifier(String modifier) {
		this.modifier = modifier;
	}

	public Long getCreateTime() {
		return this.createTime;
	}

	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}

	public Long getModifyTime() {
		return this.modifyTime;
	}

	public void setModifyTime(Long modifyTime) {
		this.modifyTime = modifyTime;
	}

	public String getModuleImg() {
		return this.moduleImg;
	}

	public void setModuleImg(String moduleImg) {
		this.moduleImg = moduleImg;
	}

	public String getCustomImg() {
		return this.customImg;
	}

	public void setCustomImg(String customImg) {
		this.customImg = customImg;
	}

	public String getLabel() {
		return label;
	}

	public void setLabel(String label) {
		this.label = label;
	}

	public Boolean getIsInit() {
		return isInit;
	}

	public void setIsInit(Boolean isInit) {
		this.isInit = isInit;
	}

	public String getCustomColor() {
		return customColor;
	}

	public void setCustomColor(String customColor) {
		this.customColor = customColor;
	}

	public Integer getModuleType() {
		return moduleType;
	}

	public Boolean getIsDisable() {
		return isDisable;
	}

	public void setIsDisable(Boolean isDisable) {
		this.isDisable = isDisable;
	}

	public void setModuleType(Integer moduleType) {
		this.moduleType = moduleType;
	}

	public Integer getBusinessGroup() {
		return businessGroup;
	}

	public void setBusinessGroup(Integer businessGroup) {
		this.businessGroup = businessGroup;
	}

	public Integer getBusinessModule() {
		return businessModule;
	}

	public void setBusinessModule(Integer businessModule) {
		this.businessModule = businessModule;
	}

	public void fullData() {
        if (this.getLabel() == null || "".equals(this.getLabel())) {
            this.setLabel(this.getModuleName());
        }
	}
}
