package com.uinnova.product.eam.workable.dto;

import com.uinnova.product.eam.feign.workable.entity.TaskResponse;
import lombok.Data;

@Data
public class WorkBenchMessageDto extends TaskResponse {

    private String businessId;

    /**
     * 1:表示工作流(方案审批) 2：表示消息(视图更新) 3：表示视图审批 4:模型视图审批(业务建模和数据建模  5:填写任务流程  6：流程审批  7：流程签发)
     */
    private Integer type;

    /**
     * "1：表示代办 2：表示已完成"
     */
    private Integer action;

    private String category;

}
