package com.uinnova.product.eam.service.asset;

import com.uinnova.product.eam.comm.model.es.CEamAttention;
import com.uinnova.product.eam.comm.model.es.EamAttention;
import com.uinnova.product.eam.model.dto.AttentionDto;
import com.uinnova.product.eam.model.vo.AttentionVo;
import com.uinnova.product.eam.model.vo.CiDirVo;

import java.util.List;

/**
 * 我的关注服务层
 */
public interface IAttentionSvc {

    /**
     * 新增关注
     * @param eamAttention
     * @return
     */
    Long saveOrUpdateAttention(EamAttention eamAttention);

    /**
     * 关注列表
     * @param attentionDto
     */
    AttentionVo findAttentionList(Integer pageNum, Integer pageSize, AttentionDto attentionDto);

    /**
     * 取消关注
     * @param eamAttention
     */
    Integer cancelAttention(EamAttention eamAttention);

    void cancelAttentionBatch(List<Long> attentionIds, Integer attentionType);

    /**
     * 用户关注列表
     * @param eamAttention
     * @return
     */
    List<EamAttention> findUserAttentionList(EamAttention eamAttention);

    /**
     * 获取我的关注系统列表
     * @return
     */
    List<CiDirVo> findMineAttentionSysList(Long userId);

    /**
     * 获取用户关注的数量
     * @param attentionDto
     * @return
     */
    Long countAttentionNum(AttentionDto attentionDto);

    /**
     * 获取
     * @param attentionId
     * @return
     */
    EamAttention getEamAttention(Integer attentionBuild, Long attentionId, Long userId);
    EamAttention getEamAttentionByType(Integer attentionType, Long attentionId, Long userId, String attentionCode, Integer source);

    List<Long> getMyAttentionPlans();

    AttentionVo getMineFocus(int pageNum, int pageSize, AttentionDto attentionDto);

    List<Long> getMyAttentionDiagramIds();

    List<EamAttention> findCurrentUserAttentionList();

    AttentionVo findAttentionList(Integer pageNum, Integer pageSize, Long userId);

    /**
     * 批量取消关注
     * @param eamAttention
     * @return
     */
    Integer batchCancelAttention(CEamAttention eamAttention);

    /**
     * 获取我的关注矩阵
     * @return LIST<long></>
     */
    List<Long> getMyAttentionMatrix();
}
