package com.uinnova.product.eam.model.cj.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 方案分享状态枚举类
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum PlanSharePermissionEnum {

    /**
     * 只读权限
     */
    READ_ONLY(3, "查看"),

    /**
     * 编辑权限
     */
    EDIT(2, "编辑"),

    /**
     * 编辑和分享权限
     */
    EDIT_AND_SHARE(1, "编辑和分享"),

    /**
     * 只读权限
     */
    PUBLISH(4, "发布");

    /**
     * 权限对应的标记
     */
    private Integer flag;

    /**
     * 显示名称
     */
    private String displayName;

    public static PlanSharePermissionEnum getPermission(int flag) {


        for (PlanSharePermissionEnum permissionEnum : PlanSharePermissionEnum.values()) {
            if (flag == permissionEnum.getFlag()) {
                return permissionEnum;
            }
        }

        return null;
    }
}
