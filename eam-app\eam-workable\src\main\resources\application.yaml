server:
  port: 1518
  servlet:
    context-path: /eam-flowable
  max-http-request-header-size: 102400
spring:
  application:
    name: eam-flowable
  datasource:
    url: *******************************************************************************************************************************************************************************
    username: uinnova
    password: Uinnova@123
  cloud:
    nacos:
      server-addr: 10.100.31.79:8848
      discovery:
        enabled: true
        group: tarsier-eam
        namespace: c320efca-4548-48e3-8b2a-a478c6255ddb
        username: nacos
        password: nacos
      config:
        enabled: false
    huawei:
      obs:
        endpoint: obs.cn-north-4.myhuaweicloud.com
        access-key: testAK
        secret-key: testSK
        bucketName: quickea
        urlExpireSeconds: 3600
  jpa:
    hibernate:
      ddl-auto: none
    show-sql: true
# flowable 配置
flowable:
  # 关闭异步，不关闭历史数据的插入就是异步的，会在同一个事物里面，无法回滚
  # 开发可开启会提高些效率，上线需要关闭
  async-executor-activate: false
  history-level: full
  db-history-used: true
  database-schema-update: false

quickea:
  server:
    eam: http://192.168.21.143
#本地资源http服务地址
http:
  resource:
    space: http://192.168.21.143/rsm

#本地资源存储地址
local:
  resource:
    space: /uinnova/uino/rsm

eam:
  query:
    user:
      url: ${quickea.server.eam}/tarsier-eam/eam/user/getUserByRoleName
  message:
    save:
      url: ${quickea.server.eam}/tarsier-eam/eam/workbenchChargeDone/saveOrUpdate
    change:
      url: ${quickea.server.eam}/tarsier-eam/eam/workbenchChargeDone/changeAction
  push:
    plan:
      notice: ${quickea.server.eam}/tarsier-eam/eam/notice/workflow/msg/save

cj:
  update:
    diagram:
      status: ${quickea.server.eam}/tarsier-eam/planDesign/updatePlanDiagramIsFlow
generic:
  user:
    url: ${quickea.server.eam}/tarsier-eam/flowable/getApprovalUser
approval:
  task:
    url: ${quickea.server.eam}/tarsier-eam/flowable/approval/task
  bind:
    url: ${quickea.server.eam}/tarsier-eam/flowable/bindTaskToBusiness
rsm:
  util:
    sdkType: huawei
obs:
  use: false
batch:
  modify:
    workbench:
      task: ${quickea.server.eam}/tarsier-eam/flowable/batchModifyWorkbenchTask
