package com.uinnova.product.eam.model.enums;

/**
 * 新版文件夹类型枚举
 */
public enum CategoryTypeEnum {
    /**
     * 顶级根目录
     */
    ROOT(1),
    /**
     * 普通文件夹
     */
    UNIVERSAL(2),
    /**
     * 系统文件夹
     */
    SYSTEM(3),
    /**
     * 模型根文件夹
     */
    MODEL_ROOT(4),
    /**
     * 模型文件夹
     */
    MODEL(5),
    /**
     * 分类文件夹
     */
    CLASSIFY(6);

    final int val;

    CategoryTypeEnum(int val) {
        this.val = val;
    }

    public int val() {
        return this.val;
    }
}
