package com.uinnova.product.eam.model.asset;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uino.bean.cmdb.base.ESCIRltInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 批量复制ci、rlt响应体
 * <AUTHOR>
 */
@Data
public class EamCiRltCopyResult implements Serializable {
    @Comment("ci集合")
    private List<CcCiInfo> ciList = new ArrayList<>();

    @Comment("关系集合")
    private List<ESCIRltInfo> rltList = new ArrayList<>();
}
