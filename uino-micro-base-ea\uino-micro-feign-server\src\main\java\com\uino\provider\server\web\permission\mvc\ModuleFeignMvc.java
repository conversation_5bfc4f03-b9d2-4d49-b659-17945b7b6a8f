package com.uino.provider.server.web.permission.mvc;

import java.util.List;
import java.util.Map;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.uino.service.permission.microservice.IModuleSvc;
import com.uino.bean.permission.base.SysModule;
import com.uino.bean.permission.business.ModuleNodeInfo;
import com.uino.bean.permission.query.CAuthModuleBean;
import com.uino.bean.permission.query.CSysModule;
import com.uino.provider.feign.permission.ModuleFeign;

@RestController
@RequestMapping("feign/module")
public class ModuleFeignMvc implements ModuleFeign {

    @Autowired
    private IModuleSvc svc;

    @Override
    public ModuleNodeInfo getModuleTree(Long domainId,Long userId) {
        // TODO Auto-generated method stub
        return svc.getModuleTree(domainId,userId);
    }

    @Override
    public SysModule saveModule(SysModule saveDto) {
        // TODO Auto-generated method stub
        return svc.saveModule(saveDto);
    }

    @Override
    public void delModule(Long id) {
        // TODO Auto-generated method stub
        svc.delModule(id);
    }

    @Override
    public void saveOrder(Map<Long, Integer> orderDict) {
        // TODO Auto-generated method stub
        svc.saveOrder(orderDict);
    }

    @Override
    public Map<Long, SysModule> recoverModules(Set<Long> moduleIds) {
        // TODO Auto-generated method stub
        return svc.recoverModules(moduleIds);
    }

    @Override
    public List<SysModule> getModulesByCdt(CSysModule cdt) {
        // TODO Auto-generated method stub
        return svc.getModulesByCdt(cdt);
    }

    @Override
    public List<SysModule> getAuthModulesBySearchBean(CAuthModuleBean bean) {
        return svc.getAuthModulesBySearchBean(bean);
    }
}
