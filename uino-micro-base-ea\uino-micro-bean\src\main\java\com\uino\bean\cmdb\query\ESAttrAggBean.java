package com.uino.bean.cmdb.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 属性分析
 * 
 * <AUTHOR>
 *
 */
@ApiModel(value="属性分析类",description = "属性分析")
public class ESAttrAggBean extends ESSearchBase implements Serializable {

    private static final long serialVersionUID = -1495466213255833490L;

    /**
     * CI关系分类Id
     */
    @ApiModelProperty(value="Ci关系分类id",example = "123")
    private Long classId = 0L;

    /**
     *属性id
     */
    @ApiModelProperty(value="属性id",example = "123")
    private Long attrDefId;

    @ApiModelProperty(value="属性id列表")
    private Long[] attrDefIds;
    /**
     * 属性名称
     */
    @ApiModelProperty(value="属性名称")
    private String attrName;

    /**
     * 检索值
     */
    @ApiModelProperty(value="检索值")
    private String like;

    public Long getAttrDefId() {
        return attrDefId;
    }

    public void setAttrDefId(Long attrDefId) {
        this.attrDefId = attrDefId;
    }

    /**
     * 属性类型
     * 
     * @return
     */
    @ApiModelProperty(value = "属性类型",example = "1")
    private Integer attrType;

    public Long getClassId() {
        return classId;
    }

    public void setClassId(Long classId) {
        this.classId = classId;
    }

    public String getAttrName() {
        return attrName;
    }

    public void setAttrName(String attrName) {
        this.attrName = attrName;
    }

    public String getLike() {
        return like;
    }

    public void setLike(String like) {
        this.like = like;
    }

    public Integer getAttrType() {
        return attrType;
    }

    public void setAttrType(Integer attrType) {
        this.attrType = attrType;
    }

    public Long[] getAttrDefIds() {
        return attrDefIds;
    }

    public void setAttrDefIds(Long[] attrDefIds) {
        this.attrDefIds = attrDefIds;
    }
}
