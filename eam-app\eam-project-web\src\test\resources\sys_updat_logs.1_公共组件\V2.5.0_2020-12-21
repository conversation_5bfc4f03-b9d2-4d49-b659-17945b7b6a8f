kiss-core
	[优化]正式更名为@uino/kiss-core
对象管理
	[新增]增加数据字典类型
	[新增]增加自定义列/描述字段
	[新增]增加对象属性列黑名单
	[优化]双击CI数据进入编辑界面
关系管理
	[新增]对关系数据导出增加了指定数据导出支持
指标管理
	[新增]增加关联分类
数据超市
	[新增]新增数据超市
	[新增]增加数据集规则部分外部接口
可视化建模
	[新增]新增可视化建模模块
	[新增]可通过在系统设置的菜单配置中设置地址为/model和/data-store来调配使用
	[修复]修复部分可视化建模（元模型）相关bug
系统设置
	[新增]增加字典表模块
	[新增]增加字典表定义接口
组织管理
	[修复]修复编辑完组织后继续修改用户时数据不同步问题
模拟性能
	[修复]修复下载偶尔会出现null文件的问题
日志管理
	[修复]修改日期后搜索传参不正确的问题
后端
	[新增]增加了ci查询高亮支持
	[新增]增加了部分针对dix的ci主键操作接口
	[优化]数据权限接口增加了cookie头转发
	[优化]license去除了对c模块的依赖修改为纯java实现
	[优化]调整国际化引用方案, 解决其他项目引入时可能存在的bug
	[优化]新增配置项，可自定义授权地址
	[优化]修改图片引入规则, 解决其他项目引入时导致的图片消失问题
