package com.uinnova.product.eam.service.bm;

import com.uinnova.product.eam.base.diagram.model.ESDiagramDTO;
import com.uinnova.product.eam.base.model.BaseQueryDiagramDto;
import com.uinnova.product.eam.model.bm.EamDiagramUpdateInfo;

import java.util.List;

/**
 * 流程模型树业务接口层
 * <AUTHOR>
 */
public interface IEamFlowModelSvc {
    /**
     * 通过视图id查询视图全量信息(兼容历史版本视图)
     * @param dto 查询参数
     * @return 视图全量信息
     */
    List<ESDiagramDTO> queryDiagramByIds(BaseQueryDiagramDto dto);

    /**
     * 查询视图更新详情
     * @param dirIds 目录id
     * @param diagramId 视图id(用于单个视图查询)
     * @return 更新详情
     */
    List<EamDiagramUpdateInfo> queryModelUpdateInfo(List<Long> dirIds, String diagramId);

}
