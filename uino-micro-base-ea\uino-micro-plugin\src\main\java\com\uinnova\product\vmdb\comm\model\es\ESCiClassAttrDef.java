package com.uinnova.product.vmdb.comm.model.es;

import java.io.Serializable;

/**
 * 
 * <AUTHOR>
 *
 */
public class ESCiClassAttrDef implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private Long classId;

    private Integer ciType;

    private String proName;

    private String proStdName;

    private Integer proType;

    private Integer proDropSourceType;

    private Long proDropSourceId;

    private String proDropSourceDef;

    private String proDesc;

    private Integer mpCiField;

    private Integer isMajor;

    private Integer isRequired;

    private Integer isCiDisp;

    private String defVal;

    private String enumValues;

    private Integer orderNo;

    private Integer lineLabelAlign;

    private Integer isCode;

    private Integer importanceLevel;

    private Integer isAudit;

    private String constraintRule;

    private Long domainId;

    private Integer dataStatus;

    private String creator;

    private String modifier;

    private Long createTime;

    private Long modifyTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getClassId() {
        return classId;
    }

    public void setClassId(Long classId) {
        this.classId = classId;
    }

    public Integer getCiType() {
        return ciType;
    }

    public void setCiType(Integer ciType) {
        this.ciType = ciType;
    }

    public String getProName() {
        return proName;
    }

    public void setProName(String proName) {
        this.proName = proName;
    }

    public String getProStdName() {
        return proStdName;
    }

    public void setProStdName(String proStdName) {
        this.proStdName = proStdName;
    }

    public Integer getProType() {
        return proType;
    }

    public void setProType(Integer proType) {
        this.proType = proType;
    }

    public Integer getProDropSourceType() {
        return proDropSourceType;
    }

    public void setProDropSourceType(Integer proDropSourceType) {
        this.proDropSourceType = proDropSourceType;
    }

    public Long getProDropSourceId() {
        return proDropSourceId;
    }

    public void setProDropSourceId(Long proDropSourceId) {
        this.proDropSourceId = proDropSourceId;
    }

    public String getProDropSourceDef() {
        return proDropSourceDef;
    }

    public void setProDropSourceDef(String proDropSourceDef) {
        this.proDropSourceDef = proDropSourceDef;
    }

    public String getProDesc() {
        return proDesc;
    }

    public void setProDesc(String proDesc) {
        this.proDesc = proDesc;
    }

    public Integer getMpCiField() {
        return mpCiField;
    }

    public void setMpCiField(Integer mpCiField) {
        this.mpCiField = mpCiField;
    }

    public Integer getIsMajor() {
        return isMajor;
    }

    public void setIsMajor(Integer isMajor) {
        this.isMajor = isMajor;
    }

    public Integer getIsRequired() {
        return isRequired;
    }

    public void setIsRequired(Integer isRequired) {
        this.isRequired = isRequired;
    }

    public Integer getIsCiDisp() {
        return isCiDisp;
    }

    public void setIsCiDisp(Integer isCiDisp) {
        this.isCiDisp = isCiDisp;
    }

    public String getDefVal() {
        return defVal;
    }

    public void setDefVal(String defVal) {
        this.defVal = defVal;
    }

    public String getEnumValues() {
        return enumValues;
    }

    public void setEnumValues(String enumValues) {
        this.enumValues = enumValues;
    }

    public Integer getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(Integer orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getLineLabelAlign() {
        return lineLabelAlign;
    }

    public void setLineLabelAlign(Integer lineLabelAlign) {
        this.lineLabelAlign = lineLabelAlign;
    }

    public Integer getIsCode() {
        return isCode;
    }

    public void setIsCode(Integer isCode) {
        this.isCode = isCode;
    }

    public Integer getImportanceLevel() {
        return importanceLevel;
    }

    public void setImportanceLevel(Integer importanceLevel) {
        this.importanceLevel = importanceLevel;
    }

    public Integer getIsAudit() {
        return isAudit;
    }

    public void setIsAudit(Integer isAudit) {
        this.isAudit = isAudit;
    }

    public String getConstraintRule() {
        return constraintRule;
    }

    public void setConstraintRule(String constraintRule) {
        this.constraintRule = constraintRule;
    }

    public Long getDomainId() {
        return domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }

    public Integer getDataStatus() {
        return dataStatus;
    }

    public void setDataStatus(Integer dataStatus) {
        this.dataStatus = dataStatus;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }

}
