package com.uino.bean.monitor.buiness;

import java.io.Serializable;

import com.uino.bean.cmdb.base.ESCIClassInfo;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.base.ESCIRltInfo;
import com.uino.bean.monitor.base.ESAlarm;

import lombok.Data;

/**
 * 告警info
 * 
 * <AUTHOR>
 *
 */
@Data
public class AlarmInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 告警信息
     */
    private ESAlarm alarmInfo;

    // /**
    // * 告警指标信息
    // */
    // private ESKpiInfo kpiInfo;
    /**
     * 告警对象/关系分类信息
     */
    private ESCIClassInfo clsInfo;

    /**
     * 告警对象信息
     */
    private ESCIInfo ciInfo;

    /**
     * 告警关系信息
     */
    private ESCIRltInfo ciRltInfo;
}
