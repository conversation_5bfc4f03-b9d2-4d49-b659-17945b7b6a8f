package com.uinnova.product.eam.comm.model;




import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;


@Comment("视图CI属性显示表[VC_DIAGRAM_CI_ATTR_DISP]")
public class CVcDiagramCiAttrDisp implements Condition {
	private static final long serialVersionUID = 1L;


	@Comment("ID[ID] operate-Equal[=]")
	private Long id;


	@Comment("ID[ID] operate-In[in]")
	private Long[] ids;


	@Comment("ID[ID] operate-GTEqual[>=]")
	private Long startId;

	@Comment("ID[ID] operate-LTEqual[<=]")
	private Long endId;


	@Comment("视图ID[DIAGRAM_ID] operate-Equal[=]")
	private Long diagramId;


	@Comment("视图ID[DIAGRAM_ID] operate-In[in]")
	private Long[] diagramIds;


	@Comment("视图ID[DIAGRAM_ID] operate-GTEqual[>=]")
	private Long startDiagramId;

	@Comment("视图ID[DIAGRAM_ID] operate-LTEqual[<=]")
	private Long endDiagramId;


	@Comment("配置类型[OBJ_TYPE] operate-Equal[=]    1=CI分类 2=CI")
	private Integer objType;


	@Comment("配置类型[OBJ_TYPE] operate-In[in]    1=CI分类 2=CI")
	private Integer[] objTypes;


	@Comment("配置类型[OBJ_TYPE] operate-GTEqual[>=]    1=CI分类 2=CI")
	private Integer startObjType;

	@Comment("配置类型[OBJ_TYPE] operate-LTEqual[<=]    1=CI分类 2=CI")
	private Integer endObjType;


	@Comment("配置对象[OBJ_ID] operate-Like[like]    配置类型为1时为ClassID，类型为2时为CI_ID")
	private String objId;


	@Comment("配置对象[OBJ_ID] operate-Equal[=]    配置类型为1时为ClassID，类型为2时为CI_ID")
	private String objIdEqual;


	@Comment("配置对象[OBJ_ID] operate-In[in]    配置类型为1时为ClassID，类型为2时为CI_ID")
	private String[] objIds;


	@Comment("属性定义ID[ATTR_DEF_ID] operate-Equal[=]")
	private Long attrDefId;


	@Comment("属性定义ID[ATTR_DEF_ID] operate-In[in]")
	private Long[] attrDefIds;


	@Comment("属性定义ID[ATTR_DEF_ID] operate-GTEqual[>=]")
	private Long startAttrDefId;

	@Comment("属性定义ID[ATTR_DEF_ID] operate-LTEqual[<=]")
	private Long endAttrDefId;


	@Comment("所属域[DOMAIN_ID] operate-Equal[=]")
	private Long domainId;


	@Comment("所属域[DOMAIN_ID] operate-In[in]")
	private Long[] domainIds;


	@Comment("所属域[DOMAIN_ID] operate-GTEqual[>=]")
	private Long startDomainId;

	@Comment("所属域[DOMAIN_ID] operate-LTEqual[<=]")
	private Long endDomainId;


	@Comment("创建时间[CREATE_TIME] operate-Equal[=]    yyyyMMddHHmmss")
	private Long createTime;


	@Comment("创建时间[CREATE_TIME] operate-In[in]    yyyyMMddHHmmss")
	private Long[] createTimes;


	@Comment("创建时间[CREATE_TIME] operate-GTEqual[>=]    yyyyMMddHHmmss")
	private Long startCreateTime;

	@Comment("创建时间[CREATE_TIME] operate-LTEqual[<=]    yyyyMMddHHmmss")
	private Long endCreateTime;


	@Comment("修改时间[MODIFY_TIME] operate-Equal[=]    yyyyMMddHHmmss")
	private Long modifyTime;


	@Comment("修改时间[MODIFY_TIME] operate-In[in]    yyyyMMddHHmmss")
	private Long[] modifyTimes;


	@Comment("修改时间[MODIFY_TIME] operate-GTEqual[>=]    yyyyMMddHHmmss")
	private Long startModifyTime;

	@Comment("修改时间[MODIFY_TIME] operate-LTEqual[<=]    yyyyMMddHHmmss")
	private Long endModifyTime;




	public Long getId() {
		return this.id;
	}
	public void setId(Long id) {
		this.id = id;
	}


	public Long[] getIds() {
		return this.ids;
	}
	public void setIds(Long[] ids) {
		this.ids = ids;
	}


	public Long getStartId() {
		return this.startId;
	}
	public void setStartId(Long startId) {
		this.startId = startId;
	}


	public Long getEndId() {
		return this.endId;
	}
	public void setEndId(Long endId) {
		this.endId = endId;
	}


	public Long getDiagramId() {
		return this.diagramId;
	}
	public void setDiagramId(Long diagramId) {
		this.diagramId = diagramId;
	}


	public Long[] getDiagramIds() {
		return this.diagramIds;
	}
	public void setDiagramIds(Long[] diagramIds) {
		this.diagramIds = diagramIds;
	}


	public Long getStartDiagramId() {
		return this.startDiagramId;
	}
	public void setStartDiagramId(Long startDiagramId) {
		this.startDiagramId = startDiagramId;
	}


	public Long getEndDiagramId() {
		return this.endDiagramId;
	}
	public void setEndDiagramId(Long endDiagramId) {
		this.endDiagramId = endDiagramId;
	}


	public Integer getObjType() {
		return this.objType;
	}
	public void setObjType(Integer objType) {
		this.objType = objType;
	}


	public Integer[] getObjTypes() {
		return this.objTypes;
	}
	public void setObjTypes(Integer[] objTypes) {
		this.objTypes = objTypes;
	}


	public Integer getStartObjType() {
		return this.startObjType;
	}
	public void setStartObjType(Integer startObjType) {
		this.startObjType = startObjType;
	}


	public Integer getEndObjType() {
		return this.endObjType;
	}
	public void setEndObjType(Integer endObjType) {
		this.endObjType = endObjType;
	}


	public String getObjId() {
		return this.objId;
	}
	public void setObjId(String objId) {
		this.objId = objId;
	}


	public String getObjIdEqual() {
		return this.objIdEqual;
	}
	public void setObjIdEqual(String objIdEqual) {
		this.objIdEqual = objIdEqual;
	}


	public String[] getObjIds() {
		return this.objIds;
	}
	public void setObjIds(String[] objIds) {
		this.objIds = objIds;
	}


	public Long getAttrDefId() {
		return this.attrDefId;
	}
	public void setAttrDefId(Long attrDefId) {
		this.attrDefId = attrDefId;
	}


	public Long[] getAttrDefIds() {
		return this.attrDefIds;
	}
	public void setAttrDefIds(Long[] attrDefIds) {
		this.attrDefIds = attrDefIds;
	}


	public Long getStartAttrDefId() {
		return this.startAttrDefId;
	}
	public void setStartAttrDefId(Long startAttrDefId) {
		this.startAttrDefId = startAttrDefId;
	}


	public Long getEndAttrDefId() {
		return this.endAttrDefId;
	}
	public void setEndAttrDefId(Long endAttrDefId) {
		this.endAttrDefId = endAttrDefId;
	}


	public Long getDomainId() {
		return this.domainId;
	}
	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}


	public Long[] getDomainIds() {
		return this.domainIds;
	}
	public void setDomainIds(Long[] domainIds) {
		this.domainIds = domainIds;
	}


	public Long getStartDomainId() {
		return this.startDomainId;
	}
	public void setStartDomainId(Long startDomainId) {
		this.startDomainId = startDomainId;
	}


	public Long getEndDomainId() {
		return this.endDomainId;
	}
	public void setEndDomainId(Long endDomainId) {
		this.endDomainId = endDomainId;
	}


	public Long getCreateTime() {
		return this.createTime;
	}
	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}


	public Long[] getCreateTimes() {
		return this.createTimes;
	}
	public void setCreateTimes(Long[] createTimes) {
		this.createTimes = createTimes;
	}


	public Long getStartCreateTime() {
		return this.startCreateTime;
	}
	public void setStartCreateTime(Long startCreateTime) {
		this.startCreateTime = startCreateTime;
	}


	public Long getEndCreateTime() {
		return this.endCreateTime;
	}
	public void setEndCreateTime(Long endCreateTime) {
		this.endCreateTime = endCreateTime;
	}


	public Long getModifyTime() {
		return this.modifyTime;
	}
	public void setModifyTime(Long modifyTime) {
		this.modifyTime = modifyTime;
	}


	public Long[] getModifyTimes() {
		return this.modifyTimes;
	}
	public void setModifyTimes(Long[] modifyTimes) {
		this.modifyTimes = modifyTimes;
	}


	public Long getStartModifyTime() {
		return this.startModifyTime;
	}
	public void setStartModifyTime(Long startModifyTime) {
		this.startModifyTime = startModifyTime;
	}


	public Long getEndModifyTime() {
		return this.endModifyTime;
	}
	public void setEndModifyTime(Long endModifyTime) {
		this.endModifyTime = endModifyTime;
	}


}


