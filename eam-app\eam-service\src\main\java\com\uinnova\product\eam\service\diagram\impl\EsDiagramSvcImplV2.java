package com.uinnova.product.eam.service.diagram.impl;

import com.alibaba.fastjson.JSONObject;
import com.binary.core.exception.BinaryException;
import com.binary.core.util.BinaryUtils;
import com.binary.jdbc.Page;
import com.google.common.collect.Lists;
import com.uinnova.product.eam.base.diagram.enums.DiagramCopyEnum;
import com.uinnova.product.eam.base.diagram.model.*;
import com.uinnova.product.eam.db.VcDiagramVersionDao;
import com.uinnova.product.eam.db.diagram.es.ESDiagramDao;
import com.uinnova.product.eam.db.diagram.es.ESDiagramLinkDao;
import com.uinnova.product.eam.db.diagram.es.ESDiagramNodeDao;
import com.uinnova.product.eam.db.diagram.es.ESDiagramSheetDao;
import com.uinnova.product.eam.model.diagram.DiagramPullRequest;
import com.uinnova.product.eam.model.diagram.DiagramPullResponse;
import com.uinnova.product.eam.model.diagram.DiagramPushRequest;
import com.uinnova.product.eam.model.diagram.DiagramPushResponse;
import com.uinnova.product.eam.service.diagram.*;
import com.uino.api.client.permission.IUserApiSvc;
import com.uino.bean.cmdb.base.LibType;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.permission.query.CSysUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 视图业务层接口实现扩展类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class EsDiagramSvcImplV2 implements EsDiagramSvcV2 {

    @Value("${http.resource.space}")
    private String httpResourceUrl;
    @Autowired
    private ESDiagramDao esDiagramDao;
    @Autowired
    private IUserApiSvc userApiSvc;
    @Autowired
    private ESDiagramSheetSvc diagramSheetSvc;
    @Autowired
    private ESShareDiagramSvc shareDiagramSvc;
    @Autowired
    private ESDiagramNodeSvc diagramNodeSvc;
    @Autowired
    private ESDiagramLinkSvc diagramLinkSvc;
    @Autowired
    private ESDiagramSvcImpl diagramSvc;
    @Autowired
    private ESDiagramExtendSvc diagramExtendSvc;
    @Autowired
    private ESDiagramLinkDao esDiagramLinkDao;
    @Autowired
    private ESShareDiagramSvc esShareDiagramSvc;
    @Autowired
    private ESDiagramNodeDao esDiagramNodeDao;
    @Autowired
    private ESDiagramSheetDao esDiagramSheetDao;
    @Autowired
    private VcDiagramVersionDao diagramVersionDao;

    @Override
    public List<ESDiagramDTO> queryDiagramByIds(List<Long> diagramIds) {
        List<ESDiagram> diagramList = esDiagramDao.getListByQuery(QueryBuilders.termsQuery("id", diagramIds));
        if (BinaryUtils.isEmpty(diagramList)) {
            return Collections.emptyList();
        }
        return queryDiagram(diagramList);
    }

    @Override
    public List<ESDiagramDTO> queryDiagramByIds(Collection<String> diagramIds) {
        List<ESDiagram> diagramList = esDiagramDao.getListByQuery(QueryBuilders.termsQuery("dEnergy.keyword", diagramIds));
        return BinaryUtils.isEmpty(diagramList) ? Collections.emptyList() : queryDiagram(diagramList);
    }

    private List<ESDiagramDTO> queryDiagram(List<ESDiagram> diagramList) {
        //架构资产打开视图时做特殊处理
        Map<Long, ESDiagram> diagramIdMap = new HashMap<>();
        Set<Long> creatorIdSet = new HashSet<>();
        Set<Long> diagramIdSet = new HashSet<>();
        for (ESDiagram esDiagram : diagramList) {
            diagramIdMap.put(esDiagram.getId(), esDiagram);
            diagramIdSet.add(esDiagram.getId());
            creatorIdSet.add(esDiagram.getUserId());
            String icon1 = esDiagram.getIcon1();
            if (icon1 != null && !icon1.startsWith(httpResourceUrl)) {
//                icon1 = httpResourceUrl + icon1;
                esDiagram.setIcon1(new StringBuilder(httpResourceUrl).append(icon1).toString());
            }
        }
        //2.根据视图id集合查询其包含的所有sheet数据
        List<ESDiagramSheetDTO> sheetList = diagramSheetSvc.getSheetByDiagram(diagramIdSet);
        List<String> sheetIdArr = sheetList.stream().map(ESDiagramSheetDTO::getSheetId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(sheetIdArr)) {
            throw new BinaryException("错误sheet，没有sheetId");
        }
        //将sheet按照视图id进行分组
        Map<Long, List<ESDiagramSheetDTO>> diagramSheetMap = sheetList.stream().collect(Collectors.groupingBy(ESDiagramSheetDTO::getDiagramId));
        //3.查询所有的node节点，将其按照diagramId和sheetId进行分组
        List<ESDiagramNode> nodeList = diagramNodeSvc.getNodeByDiagram(diagramIdSet, sheetIdArr);
        Map<Long, Map<String, List<ESDiagramNode>>> diagramSheetNodeMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(nodeList)) {
            Map<Long, List<ESDiagramNode>> diagramNodeMap = nodeList.stream().collect(Collectors.groupingBy(ESDiagramNode::getDiagramId));
            for (Map.Entry<Long, List<ESDiagramNode>> entry : diagramNodeMap.entrySet()) {
                Map<String, List<ESDiagramNode>> sheetNodeMap = new HashMap<>();
                if (!BinaryUtils.isEmpty(entry.getValue())) {
                    sheetNodeMap = entry.getValue().stream().collect(Collectors.groupingBy(ESDiagramNode::getSheetId));
                }
                diagramSheetNodeMap.put(entry.getKey(), sheetNodeMap);
            }
        }
        //4.查询所有的link节点，将其按照diagramId和sheetId进行分组
        List<ESDiagramLink> linkList = diagramLinkSvc.getLinkByDiagram(diagramIdSet, sheetIdArr);
        Map<Long, Map<String, List<ESDiagramLink>>> diagramSheetLinkMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(linkList)) {
            Map<Long, List<ESDiagramLink>> diagramLinkMap = linkList.stream().collect(Collectors.groupingBy(ESDiagramLink::getDiagramId));
            for (Map.Entry<Long, List<ESDiagramLink>> entry : diagramLinkMap.entrySet()) {
                Map<String, List<ESDiagramLink>> sheetLinkMap = new HashMap<>();
                if (!BinaryUtils.isEmpty(entry.getValue())) {
                    sheetLinkMap = entry.getValue().stream().collect(Collectors.groupingBy(ESDiagramLink::getSheetId));
                }
                diagramSheetLinkMap.put(entry.getKey(), sheetLinkMap);
            }
        }
        Map<String, SysUser> diagramCreatorMap = new HashMap<>();
        CSysUser queryUser = new CSysUser();
        queryUser.setIds(creatorIdSet.toArray(new Long[]{}));
        queryUser.setSuperUserFlags(new Integer[]{0, 1});
        List<SysUser> userList = userApiSvc.getSysUserByCdt(queryUser);
        for (SysUser user : userList) {
            user.setLoginPasswd(null);
            String icon = user.getIcon();
            if (icon != null && !icon.startsWith(this.httpResourceUrl)) {
                user.setIcon(new StringBuilder(httpResourceUrl).append(icon).toString());
            }
            diagramCreatorMap.put(user.getLoginCode(), user);
        }
        //封装返回结果
        List<ESDiagramDTO> esDiagramDTOList = new ArrayList<>();
        for (Long diagramId : diagramIdSet) {
            ESDiagramDTO esDiagramDTO = new ESDiagramDTO();
            ESDiagramInfoDTO esDiagramInfoDTO = new ESDiagramInfoDTO();
            //封装视图基础信息
            ESDiagram esDiagram = diagramIdMap.get(diagramId);
            BeanUtils.copyProperties(esDiagram, esDiagramInfoDTO);
            //封装sheetList
            List<ESDiagramSheetDTO> sheetDTOList = diagramSheetMap.get(diagramId);
            List<ESDiagramSheetDTO> sortedSheetDTOList = sheetDTOList.stream().sorted(Comparator.comparing(ESDiagramSheetDTO::getSheetOrder)).collect(Collectors.toList());
            esDiagramInfoDTO.setSheetList(sortedSheetDTOList);
            //封装modelList
            List<ESDiagramModel> modelList = new ArrayList<>();
            for (ESDiagramSheetDTO sheetDTO : sheetDTOList) {
                ESDiagramModel esDiagramModel = new ESDiagramModel();
                String sheetId = sheetDTO.getSheetId();
                if (!BinaryUtils.isEmpty(diagramSheetNodeMap)) {
                    List<ESDiagramNode> nodeDTOList = new ArrayList<>();
                    Map<String, List<ESDiagramNode>> diagramNodeMap = diagramSheetNodeMap.get(diagramId);
                    if (!BinaryUtils.isEmpty(diagramNodeMap)) {
                        nodeDTOList = diagramNodeMap.get(sheetId);
                    }
                    if (!BinaryUtils.isEmpty(nodeDTOList)) {
                        esDiagramModel.setNodeDataArray(nodeDTOList);
                    }
                }
                if (!BinaryUtils.isEmpty(diagramSheetLinkMap)) {
                    List<ESDiagramLink> linkDTOList = new ArrayList<>();
                    Map<String, List<ESDiagramLink>> diagramLinkMap = diagramSheetLinkMap.get(diagramId);
                    if (!BinaryUtils.isEmpty(diagramLinkMap)) {
                        linkDTOList = diagramLinkMap.get(sheetId);
                    }
                    if (!BinaryUtils.isEmpty(linkDTOList)) {
                        esDiagramModel.setLinkDataArray(linkDTOList);
                    }
                }
                esDiagramModel.setDiagramId(diagramId);
                esDiagramModel.setModelData(sheetDTO.getModelData());
                esDiagramModel.setDiagramClass(sheetDTO.getDiagramClass());
                esDiagramModel.setLinkToPortIdProperty(sheetDTO.getLinkToPortIdProperty());
                esDiagramModel.setLinkFromPortIdProperty(sheetDTO.getLinkFromPortIdProperty());
                esDiagramModel.setSheetId(sheetDTO.getSheetId());
                modelList.add(esDiagramModel);
            }
            esDiagramInfoDTO.setModelList(modelList);
            esDiagramDTO.setDiagram(esDiagramInfoDTO);
            //封装视图作者信息
            esDiagramDTO.setCreator(diagramCreatorMap.get(esDiagramInfoDTO.getCreator()));
            //封装视图分享记录信息
//            esDiagramDTO.setShareRecords(diagramShareRecordMap.get(diagramId));
            esDiagramDTOList.add(esDiagramDTO);
        }
        return esDiagramDTOList;
    }

    @Override
    public String deleteDiagramWithType(List<String> diagramIds, Long delDirId, Integer type) {
        if (CollectionUtils.isEmpty(diagramIds)) {
            return null;
        }
        EamDiagramQuery diagramQuery = new EamDiagramQuery();
        diagramQuery.setDEnergys(diagramIds.toArray(new String[]{}));
        diagramQuery.setStatus(1);
        diagramQuery.setDataStatus(1);
        List<ESDiagram> diagramList = esDiagramDao.getListByCdt(diagramQuery);
        if (BinaryUtils.isEmpty(diagramList)) {
            return null;
        }
        List<Long> ids = diagramList.stream().map(ESDiagram::getId).collect(Collectors.toList());
        if (type == 1) {
            //逻辑删除
            for (ESDiagram each : diagramList) {
                each.setStatus(0);
                each.setOldDirId(each.getDirId());
                if (delDirId != null) {
                    each.setDirId(delDirId);
                }
            }
            esDiagramDao.saveOrUpdateBatch(diagramList);
        } else {
            //根据diagramId集合物理删除视图
            esDiagramDao.deleteByIds(ids);
            diagramSheetSvc.deleteByDiagramIds(ids);
            diagramNodeSvc.deleteByDiagramIds(ids);
            diagramLinkSvc.deleteByDiagramIds(ids);
        }
        shareDiagramSvc.removeShareByDiagramIds(ids);
        //本地源视图与删除的发布视图解除绑定关系
        this.releaseLocalDiagram(diagramIds);
        return null;
    }

    /**
     * 本地源视图与删除的发布视图解除绑定关系
     *
     */
    private void releaseLocalDiagram(List<String> diagramIds) {
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termsQuery("releaseDiagramId.keyword", diagramIds));
        query.must(QueryBuilders.termQuery("isOpen", 0));
        List<ESDiagram> diagrams = esDiagramDao.getListByQuery(query);
        for (ESDiagram esDiagram : diagrams) {
            esDiagram.setReleaseDiagramId("");
            esDiagram.setReleaseVersion(0);
            esDiagramDao.saveOrUpdate(esDiagram);
        }
    }

    @Override
    public DiagramPushResponse diagramPush(DiagramPushRequest request) {
        long startTime = System.currentTimeMillis();
        DiagramPushResponse response = new DiagramPushResponse();
        List<ESDiagram> pushList = diagramSvc.selectByIds(request.getDiagramIds(), null, Collections.singletonList(0));
        if (CollectionUtils.isEmpty(pushList)) {
            return response.setResultCode(0).setErrMsg("所要发布视图不存在");
        }
        //查询所有对应资产库视图 <资产仓库视图id, 设计空间视图id>
        Map<String, String> diagramIdMap = new HashMap<>();
        for (ESDiagram each : pushList) {
            //发布说明从这里直接带过去
            each.setReleaseDesc(request.getReleaseDesc());
            if (BinaryUtils.isEmpty(each.getReleaseDiagramId())) {
                continue;
            }
            diagramIdMap.put(each.getReleaseDiagramId(), each.getDEnergy());
        }
        if (!BinaryUtils.isEmpty(request.getWriteReleaseDiagramIdMap())) {
            for (Map.Entry<String, String> entry : request.getWriteReleaseDiagramIdMap().entrySet()) {
                diagramIdMap.put(entry.getValue(), entry.getKey());
            }
        }
        //我的空间视图id->资产仓库目录id
        Map<String, Long> dirIdMap = request.getDiagramIdDirIdMap();
        //我的空间视图id->资产仓库视图id  映射
        Map<String, String> resultIdMap = new HashMap<>();
        //资产有对应视图,则对视图内容做更新
        if (!BinaryUtils.isEmpty(diagramIdMap)) {
            List<ESDiagram> assetDiagramList = diagramSvc.selectByIds(diagramIdMap.keySet(), null, Collections.singletonList(1));
            //设置发布后的目录
            assetDiagramList.forEach(each -> each.setDirId(dirIdMap.getOrDefault(each.getDEnergy(), each.getDirId())));
            //对已发布视图做历史版本记录
            diagramExtendSvc.createDiagramHistoryBatch(assetDiagramList);
            List<ESDiagram> updateList = new ArrayList<>(pushList);
            updateList.addAll(assetDiagramList);
            Map<String, String> updateMap = diagramExtendSvc.updateDiagramBatch(diagramIdMap, updateList, LibType.DESIGN);
            for (Map.Entry<String, String> entry : updateMap.entrySet()) {
                resultIdMap.put(entry.getValue(), entry.getKey());
            }
        }
        //过滤出需要做新增的视图
        List<ESDiagram> copyList = pushList.stream().filter(each -> !resultIdMap.containsKey(each.getDEnergy())).collect(Collectors.toList());
        resultIdMap.putAll(diagramExtendSvc.copyDiagramBatch(dirIdMap, copyList, DiagramCopyEnum.PUSH));
        //更新我的空间视图发布版本
        diagramExtendSvc.updateDiagramPublicVersion(resultIdMap);
        log.info("发布耗时:" + (System.currentTimeMillis() - startTime));
        return response.setDiagramMap(resultIdMap);
    }

    @Override
    public DiagramPullResponse diagramPull(DiagramPullRequest request) {
        long startTime = System.currentTimeMillis();
        DiagramPullResponse response = new DiagramPullResponse();
        List<ESDiagram> pullList = diagramSvc.selectByIds(request.getDiagramIds(), null, Collections.singletonList(1));
        if (CollectionUtils.isEmpty(pullList)) {
            return response.setResultCode(0).setErrMsg("检出视图已删除!");
        }
        Set<String> pullDiagramIds = pullList.stream().map(ESDiagram::getDEnergy).collect(Collectors.toSet());
        //查询我的空间中已有的发布视图
        List<ESDiagram> privateDiagramList = diagramExtendSvc.queryByReleaseDiagramId(pullDiagramIds, request.getOwnerCode(), 0);
        //根据模型关联上的视图
        Map<String, String> diagramIdMap = request.getDiagramIdMap();
        List<ESDiagram> privateModelDiagramList = diagramSvc.selectByIds(diagramIdMap.keySet(), null, Collections.singletonList(0));
        if (CollectionUtils.isNotEmpty(privateModelDiagramList)) {
            privateDiagramList.addAll(privateModelDiagramList);
        }
        //资产仓库视图id, 我的空间目录id
        Map<String, Long> dirIdMap = request.getDesignIdMap();
        //资产仓库视图id->设计空间视图id  映射
        Map<String, String> resultIdMap = new HashMap<>();
        //检出,对我的空间已有对应视图做内容更新
        if (CollectionUtils.isNotEmpty(privateDiagramList)) {
            for (ESDiagram each : privateDiagramList) {
                //更新视图目录id
                each.setDirId(dirIdMap.getOrDefault(each.getReleaseDiagramId(), each.getDirId()));
                if (!diagramIdMap.containsKey(each.getDEnergy()) && !BinaryUtils.isEmpty(each.getReleaseDiagramId())) {
                    diagramIdMap.put(each.getDEnergy(), each.getReleaseDiagramId());
                }
            }
            List<ESDiagram> updateList = new ArrayList<>(pullList);
            updateList.addAll(privateDiagramList);
            resultIdMap.putAll(diagramExtendSvc.updateDiagramBatch(diagramIdMap, updateList, LibType.PRIVATE));
        }
        //筛选出需要做新增的视图
        List<ESDiagram> copyList = pullList.stream().filter(each -> !resultIdMap.containsValue(each.getDEnergy())).collect(Collectors.toList());
        resultIdMap.putAll(diagramExtendSvc.copyDiagramBatch(dirIdMap, copyList, DiagramCopyEnum.PULL));
        log.info("检出耗时:" + (System.currentTimeMillis() - startTime));
        return response.setDiagramMap(resultIdMap);
    }

    @Override
    public List<ESDiagram> queryByArtifactIds(List<Long> artifactIds) {
        if (BinaryUtils.isEmpty(artifactIds)) {
            return Collections.emptyList();
        }
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.must(QueryBuilders.termsQuery("viewType.keyword", artifactIds));
        boolQuery.must(QueryBuilders.termQuery("status", 1));
        boolQuery.must(QueryBuilders.termQuery("historyVersionFlag", 1));
        boolQuery.must(QueryBuilders.termQuery("dataStatus", 1));
        List<ESDiagram> result = esDiagramDao.getListByQueryScroll(boolQuery);
        return CollectionUtils.isEmpty(result)?Collections.emptyList():result;
    }

    @Override
    public Map<String, String> copyDiagramBatch(Map<String, Long> diagramDirIdMap, List<ESDiagram> diagramList, DiagramCopyEnum type) {
        return diagramExtendSvc.copyDiagramBatch(diagramDirIdMap, diagramList, type);
    }

    @Override
    public Object deleteUselessDiagramInfo() {
        // 1.查询删除视图，回收站清除的
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("status", 0));
        query.must(QueryBuilders.termQuery("dataStatus", 0));
        List<ESDiagram> diagramInfoList = esDiagramDao.getListByQuery(query);
        List<Long> clearDiagramIds = new ArrayList<>();

        log.info("查询失效视图信息集合：{}", JSONObject.toJSON(diagramInfoList));
        // 2. 查询失效视图历史版本信息
        if (CollectionUtils.isNotEmpty(diagramInfoList)) {
            CVcDiagramVersion cVcDiagramVersion = new CVcDiagramVersion();
            Set<Long> diagramIds = diagramInfoList.stream().map(ESDiagram::getId).collect(Collectors.toSet());
            clearDiagramIds.addAll(diagramIds);
            cVcDiagramVersion.setDiagramIds(diagramIds.toArray(new Long[0]));
            List<VcDiagramVersion> vcDiagramVersions = diagramVersionDao.selectList(cVcDiagramVersion, null);
            List<Long> diagramIdList = vcDiagramVersions.stream().map(VcDiagramVersion::getId).collect(Collectors.toList());
            clearDiagramIds.addAll(diagramIdList);
            log.info("删除失效视图Id:{}", JSONObject.toJSONString(clearDiagramIds));
            //1.视图
            if (CollectionUtils.isNotEmpty(clearDiagramIds)) {
                esDiagramDao.deleteByIds(clearDiagramIds);
                //2.sheet数据
                BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
                boolQueryBuilder.must(QueryBuilders.termsQuery("diagramId", clearDiagramIds));
                esDiagramSheetDao.deleteByQuery(boolQueryBuilder, true);
                //3.node节点
                esDiagramNodeDao.deleteByQuery(boolQueryBuilder, true);
                //4.link节点;
                esDiagramLinkDao.deleteByQuery(boolQueryBuilder, true);
                // 5、分享记录
                esShareDiagramSvc.removeShareByDiagramIds(clearDiagramIds);
            }
        }
        // 获取所有视图id
        Page<ESDiagram> diagramPage = esDiagramDao.getListByQuery(1, 3000, QueryBuilders.boolQuery());
        List<Long> diagramALlIds = diagramPage.getData().stream().map(ESDiagram::getId).distinct().collect(Collectors.toList());
        if (diagramPage.getTotalRows() > 3000) {
            for (int i = 2; i < diagramPage.getTotalRows() + 1; i++) {
                List<ESDiagram> esDiagrams = esDiagramDao.selectListByQuery(i, 3000, QueryBuilders.boolQuery());
                if (CollectionUtils.isEmpty(esDiagrams)) {
                    break;
                }
                Set<Long> ids = esDiagrams.stream().map(ESDiagram::getId).collect(Collectors.toSet());
                diagramALlIds.addAll(ids);
            }
        }
        // 删除link节点
        List<Long> linkIds = new ArrayList<>();
        Page<ESDiagramLink> linkPage = esDiagramLinkDao.getListByQuery(1, 3000, QueryBuilders.boolQuery());
        if (linkPage.getTotalRows() > 3000) {
            List<Long> uselessLinkPage = linkPage.getData().stream().filter(esDiagramLink -> !diagramALlIds.contains(esDiagramLink.getDiagramId()))
                    .map(ESDiagramLink::getId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(uselessLinkPage)) {
                linkIds.addAll(uselessLinkPage);
            }
            for (int i = 2; i < linkPage.getTotalRows() + 1; i++) {
                List<ESDiagramLink> esDiagramLinks = esDiagramLinkDao.selectListByQuery(i, 3000, QueryBuilders.boolQuery());
                if (CollectionUtils.isEmpty(esDiagramLinks)) {
                    break;
                }
                List<Long> uselessLink = esDiagramLinks.stream().filter(esDiagramLink -> !diagramALlIds.contains(esDiagramLink.getDiagramId()))
                        .map(ESDiagramLink::getId).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(uselessLink)) {
                    linkIds.addAll(uselessLink);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(linkIds)) {
            log.info("删除link失效信息id集合{}", linkIds);
            for (List<Long> longs : Lists.partition(linkIds, 3000)) {
                esDiagramLinkDao.deleteByIds(longs);
            }
        }
        // 删除node
       List<Long> nodeIds = new ArrayList<>();
        Page<ESDiagramNode> nodePage = esDiagramNodeDao.getListByQuery(1, 3000, QueryBuilders.boolQuery());
        if (nodePage.getTotalRows() > 3000) {
            List<Long> uselessNodePage = nodePage.getData().stream().filter(e -> !diagramALlIds.contains(e.getDiagramId()))
                    .map(ESDiagramNode::getId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(uselessNodePage)) {
                nodeIds.addAll(uselessNodePage);
            }
            for (int i = 2; i < nodePage.getTotalRows() + 1; i++) {
                List<ESDiagramNode> esDiagramNodes = esDiagramNodeDao.selectListByQuery(i, 3000, QueryBuilders.boolQuery());
                if (CollectionUtils.isEmpty(esDiagramNodes)) {
                    break;
                }
                List<Long> uselessNode = esDiagramNodes.stream().filter(e -> !diagramALlIds.contains(e.getDiagramId())).map(ESDiagramNode::getId).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(uselessNode)) {
                    nodeIds.addAll(uselessNode);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(nodeIds)) {
            log.info("删除node失效信息id集合{}", nodeIds);
            for (List<Long> longs : Lists.partition(nodeIds, 3000)) {
                esDiagramNodeDao.deleteByIds(longs);
            }
        }
        return null;
    }
}
