package com.uino.bean.sys.business;

import com.uino.bean.sys.base.ESDictionaryClassInfo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 
 * <AUTHOR>
 *
 */
@Getter
@Setter
@ApiModel(value="字典表定义",description = "字典表定义")
public class DictionaryInfoDto extends ESDictionaryClassInfo {
    /**
     * 
     */
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value="字典数量",example = "1")
    private Long dictCount;
}
