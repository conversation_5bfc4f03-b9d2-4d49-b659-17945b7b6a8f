package com.uino.util.message.queue.config;

import com.uino.util.message.queue.MessageTopicConst;
import com.uino.util.message.queue.tools.EnableMessageQueue;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.admin.AdminClient;
import org.apache.kafka.clients.admin.ListTopicsResult;
import org.apache.kafka.clients.admin.NewTopic;
import org.apache.kafka.common.KafkaFuture;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Configuration;

import jakarta.annotation.PostConstruct;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ExecutionException;

/**
 * Check whether the current Kafka cluster contains the required topics,
 * if not created, the number of partitions defaults to 3, and the number of replicas defaults to 1.
 *
 * @Author: YGQ
 * @Create: 2021-05-24 09:45
 **/
@Slf4j
@Configuration
@Conditional(EnableMessageQueue.class)
@ComponentScan(basePackages = {"com.uino.common.util.message.queue.config"})
public class KafkaTopicInit {

    private final AdminClient adminClient;

    @Autowired
    public KafkaTopicInit(AdminClient adminClient) {
        this.adminClient = adminClient;
    }

    @PostConstruct
    public void initTopic() {
        try {
            ListTopicsResult result = adminClient.listTopics();
            KafkaFuture<Set<String>> names = result.names();
            Set<String> existTopics = names.get();
            List<NewTopic> createTopics = new ArrayList<>(32);

            Field[] fields = MessageTopicConst.class.getDeclaredFields();
            for (Field f : fields) {
                String topic = f.get("").toString();
                if (!existTopics.contains(topic)) {
                    log.info(">>> topic [ {} ] will be created", topic);
                    NewTopic newTopic = new NewTopic(topic, KafkaProp.numberOfPartitions, KafkaProp.numberOfCopies);
                    createTopics.add(newTopic);
                }
            }
            if (createTopics.size() > 0) {
                adminClient.createTopics(createTopics);
            }
        } catch (IllegalAccessException | InterruptedException | ExecutionException e) {
            log.error(e.getMessage());
        }
    }


}