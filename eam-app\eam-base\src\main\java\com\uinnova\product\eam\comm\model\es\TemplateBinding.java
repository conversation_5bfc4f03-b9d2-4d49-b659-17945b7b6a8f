package com.uinnova.product.eam.comm.model.es;

import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

@Data
@Comment("方案模板绑定分类[UINO_CJ_TEMPLATE_BIND]")
public class TemplateBinding implements Condition {
    @Comment("业务主键")
    private Long id;
    @Comment("绑定的分类id")
    private Long classId;
    @Comment("绑定分类的名称")
    private String className;
    @Comment("排序字段")
    private Long sortNum;
    private Long createTime;
    private Long modifyTime;
}
