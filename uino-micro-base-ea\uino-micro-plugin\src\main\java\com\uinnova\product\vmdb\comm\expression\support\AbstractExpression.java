package com.uinnova.product.vmdb.comm.expression.support;

import com.uinnova.product.vmdb.comm.exception.ExpressionException;
import com.uinnova.product.vmdb.comm.expression.Expression;
import com.uinnova.product.vmdb.comm.expression.ExpressionFactory;

import java.util.ArrayList;
import java.util.List;

/**
 * 
 * <AUTHOR>
 *
 */
public abstract class AbstractExpression<E> implements Expression<E> {
    private static final long serialVersionUID = 1L;

    /** 与的表达式 **/
    private List<Expression<E>> appends;

    /** SQL片断 **/
    private String sqlFragment;

    @Override
    public String toString() {
        return getSqlFragment();
    }

    @Override
    public Expression<E> and(Expression<E> exp) {
        if (exp == null) {
            throw new ExpressionException(" the expression is NULL argument! ");
        }
        if (this.appends == null) {
            buildAppendsList();
        }
        this.appends.add(exp);
        reset();
        return this;
    }

    private void buildAppendsList() {
        if (this.appends != null) {
            return;
        }
        this.appends = new ArrayList<Expression<E>>();
    }

    @Override
    public Expression<E> or(Expression<E> exp) {
        if (exp == null) {
            throw new ExpressionException(" the expression is NULL argument! ");
        }
        return ExpressionFactory.getExpression(this, exp);
    }

    @Override
    public String getSqlFragment() {
        if (this.sqlFragment == null) {
            parse();
        }
        return this.sqlFragment;
    }

    private void reset() {
        this.sqlFragment = null;
    }

    private void parse() {
        this.sqlFragment = buildSqlFragment();

        StringBuilder buff = null;
        if (this.appends != null && this.appends.size() > 0) {
            buff = new StringBuilder();
            if (this.sqlFragment != null) {
                buff.append(this.sqlFragment);
            }

            for (int i = 0; i < this.appends.size(); i++) {
                Expression<E> app = this.appends.get(i);
                buff.append(" and ").append(app.getSqlFragment());
            }
        }

        if (buff != null) {
            this.sqlFragment = buff.toString();
        }
    }

    protected abstract String buildSqlFragment();

}
