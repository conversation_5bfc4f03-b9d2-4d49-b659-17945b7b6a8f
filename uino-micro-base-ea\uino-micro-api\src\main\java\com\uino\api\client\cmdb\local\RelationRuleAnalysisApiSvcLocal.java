package com.uino.api.client.cmdb.local;

import com.alibaba.fastjson.JSONObject;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uino.service.cmdb.dataset.microservice.IRelationRuleAnalysisSvc;
import com.uino.bean.cmdb.base.dataset.DataSetMallApiRelationRule;
import com.uino.bean.cmdb.base.dataset.batch.SimpleFriendInfo;
import com.uino.bean.cmdb.base.dataset.chart.Chart;
import com.uino.bean.cmdb.business.dataset.FriendInfo;
import com.uino.bean.cmdb.business.dataset.QueryCondition;
import com.uino.bean.cmdb.business.dataset.RltRuleTableData;
import com.uino.api.client.cmdb.IRelationRuleAnalysisApiSvc;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * @Title: IRelationRuleAnalysisApiSvcLocal
 * @Description:
 * @Author: YGQ
 * @Create: 2021-05-31 14:22
 **/
@Service
public class RelationRuleAnalysisApiSvcLocal implements IRelationRuleAnalysisApiSvc {

    private final IRelationRuleAnalysisSvc relationRuleAnalysisSvc;

    @Autowired
    public RelationRuleAnalysisApiSvcLocal(IRelationRuleAnalysisSvc relationRuleAnalysisSvc) {
        this.relationRuleAnalysisSvc = relationRuleAnalysisSvc;
    }

    @Override
    public Map<Long, List<QueryCondition>> findTravalTree(DataSetMallApiRelationRule relationRule) {
        return relationRuleAnalysisSvc.findTravalTree(relationRule);
    }

    @Override
    public FriendInfo queryCiFriendByCiId(CcCiInfo sCi, Long dataSetId) {
        return relationRuleAnalysisSvc.queryCiFriendByCiId(sCi, dataSetId);
    }

    @Override
    public FriendInfo queryCiFriendByCiIdAndRule(CcCiInfo sCi, DataSetMallApiRelationRule relationRule) {
        return relationRuleAnalysisSvc.queryCiFriendByCiIdAndRule(sCi, relationRule);
    }

    @Override
    public Map<Long, FriendInfo> queryCiFriendByCiIds(List<CcCiInfo> sCis, Long dataSetId, boolean isIncludeAllStartCI) {
        return relationRuleAnalysisSvc.queryCiFriendByCiIds(sCis, dataSetId, isIncludeAllStartCI);
    }

    @Override
    public Map<Long, FriendInfo> queryCiFriendByCiIdsAndRule(List<CcCiInfo> sCis, DataSetMallApiRelationRule relationRule, boolean isIncludeAllStartCI) {
        return relationRuleAnalysisSvc.queryCiFriendByCiIdsAndRule(sCis, relationRule, isIncludeAllStartCI);
    }

    @Override
    public Map<Long, FriendInfo> queryCiFriendByRule(DataSetMallApiRelationRule relationRule) {
        return relationRuleAnalysisSvc.queryCiFriendByRule(relationRule);
    }

    @Override
    public Map<Long, FriendInfo> queryCiFriendByRuleWithLimit(DataSetMallApiRelationRule relationRule, Integer limit) {
        return relationRuleAnalysisSvc.queryCiFriendByRuleWithLimit(relationRule, limit);
    }

    @Override
    public FriendInfo mergeFriendInfoMap(Map<Long, FriendInfo> friendInfoMap) {
        return relationRuleAnalysisSvc.mergeFriendInfoMap(friendInfoMap);
    }

    @Override
    public RltRuleTableData disassembleFriendInfoDataByPath(DataSetMallApiRelationRule relationRule, List<Long> ciIds, FriendInfo friendInfo) {
        return relationRuleAnalysisSvc.disassembleFriendInfoDataByPath(relationRule, ciIds, friendInfo);
    }

    @Override
    public RltRuleTableData disassembleFriendInfoDataByPath(List<CcCiInfo> sCis, DataSetMallApiRelationRule relationRule) {
        return relationRuleAnalysisSvc.disassembleFriendInfoDataByPath(sCis, relationRule);
    }

    @Override
    public JSONObject countStatistics(DataSetMallApiRelationRule dataSetMallRelationApi, Map<Long, SimpleFriendInfo> simpleFriendInfoMap, Chart chart) {
        return relationRuleAnalysisSvc.countStatistics(dataSetMallRelationApi, simpleFriendInfoMap, chart);
    }
}
