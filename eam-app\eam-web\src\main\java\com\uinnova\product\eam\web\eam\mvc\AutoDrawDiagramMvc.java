package com.uinnova.product.eam.web.eam.mvc;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.binary.framework.web.RemoteResult;
import com.uinnova.product.eam.model.vo.CiClassCiInfosVo;
import com.uinnova.product.eam.model.AutoDrawCIVo;
import com.uinnova.product.eam.service.AutoDrawDiagramSvc;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 *  Automatic mapping related interface --- （Lie flat version）
 */
@RestController
@RequestMapping("/eam/autoDrawDiagram")
public class AutoDrawDiagramMvc {

    @Autowired
    AutoDrawDiagramSvc autoDrawDiagramSvc;

    @ModDesc(desc = "系统上下文关系架构图", pDesc = "对象库", pType = String.class, rDesc = "应用策略信息", rType = AutoDrawDiagramMvc.class)
    @RequestMapping("/getContextArchDiagramData")
    public RemoteResult getContextArchDiagramData(@RequestParam Long dataSetId, @RequestParam String ciCode) {
        Map<String, Object> result = autoDrawDiagramSvc.getContextArchDiagramData(dataSetId, ciCode);
        return new RemoteResult(result);
    }

    @ModDesc(desc = "系统分层分级的功能架构图", pDesc = "对象库", pType = String.class, rDesc = "应用策略信息", rType = AutoDrawDiagramMvc.class)
    @RequestMapping("/getFunctionalArchDiagramData")
    public RemoteResult getFunctionalArchDiagramData(@RequestParam Long dataSetId, @RequestParam String ciCode) {
        AutoDrawCIVo functionalArchDiagramData = autoDrawDiagramSvc.getFunctionalArchDiagramData(dataSetId, ciCode);
        return new RemoteResult(functionalArchDiagramData);
    }

    @ModDesc(desc = "系统技术架构图", pDesc = "对象库", pType = String.class, rDesc = "应用策略信息", rType = AutoDrawDiagramMvc.class)
    @RequestMapping("/getTechnologyArchDiagramData")
    public RemoteResult getTechnologyArchDiagramData(@RequestParam Long dataSetId, @RequestParam String ciCode) {
        AutoDrawCIVo technologyArchDiagramData = autoDrawDiagramSvc.getTechnologyArchDiagramData(dataSetId, ciCode);
        return new RemoteResult(technologyArchDiagramData);
    }

    @ModDesc(desc = "系统部署架构图", pDesc = "对象库", pType = String.class, rDesc = "应用策略信息", rType = AutoDrawDiagramMvc.class)
    @RequestMapping("/getDeploymentArchDiagramData")
    public RemoteResult getDeploymentArchDiagramData(@RequestParam Long dataSetId, @RequestParam String ciCode) {
        List<CiClassCiInfosVo> ciClassCiInfos = autoDrawDiagramSvc.getDeploymentArchDiagramData(dataSetId, ciCode);
        return new RemoteResult(ciClassCiInfos);
    }

    @ModDesc(desc = "系统应用服务架构图", pDesc = "对象库", pType = String.class, rDesc = "应用策略信息", rType = AutoDrawDiagramMvc.class)
    @RequestMapping("/getApplicationArchDiagramData")
    public RemoteResult getApplicationArchDiagramData(@RequestParam Long dataSetId, @RequestParam String ciCode) {
        AutoDrawCIVo applicationArchDiagramData = autoDrawDiagramSvc.getApplicationArchDiagramData(dataSetId, ciCode);
        return new RemoteResult(applicationArchDiagramData);
    }

    @ModDesc(desc = "系统应用服务架构图左侧数据列表获取", pDesc = "对象库", pType = String.class, rDesc = "应用策略信息", rType = AutoDrawDiagramMvc.class)
    @RequestMapping("/getADCIDataList")
    public RemoteResult getADCIDataList(@RequestParam Long dataSetId, @RequestParam String ciCode) {
        List<CcCiInfo> adciDataList = autoDrawDiagramSvc.getADCIDataList(dataSetId, ciCode);
        return new RemoteResult(adciDataList);
    }

    @ModDesc(desc = "自动成图转化本地数据创建", pDesc = "对象库", pType = String.class, rDesc = "应用策略信息", rType = AutoDrawDiagramMvc.class)
    @RequestMapping("/convertLocalViewData")
    public RemoteResult convertLocalViewData(@RequestBody String body) {
        JSONObject json = JSON.parseObject(body);
        List<String> ciCodes = json.getJSONArray("ciCodes").toJavaList(String.class);
        List<String> rltCodes = json.getJSONArray("rltCodes").toJavaList(String.class);
        Boolean flag = autoDrawDiagramSvc.convertLocalViewData(ciCodes, rltCodes);
        return new RemoteResult(flag);
    }
}
