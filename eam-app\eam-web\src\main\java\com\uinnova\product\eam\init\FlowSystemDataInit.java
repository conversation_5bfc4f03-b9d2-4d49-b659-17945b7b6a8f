package com.uinnova.product.eam.init;

import com.uinnova.product.eam.service.flow.init.FlowSystemInitService;
import com.uinnova.product.eam.web.eam.mvc.SystemInitDataController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

/**
 * description
 *
 * <AUTHOR>
 * @since 2024/11/12 11:01
 */
@Slf4j
@Component
public class FlowSystemDataInit implements ApplicationRunner {

    @Resource
    private FlowSystemInitService flowSystemInitService;

    @Value("${uino.eam.flow.init.open:false}")
    private Boolean initFlowData;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        if (initFlowData) {
            log.info("流程体系数据初始化开始");
            flowSystemInitService.initFlowData();
            log.info("流程体系数据初始化结束");
        } else {
            log.info("流程体系数据初始化开关关闭");
        }
    }
}
