package com.uinnova.product.vmdb.comm.bean;


import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;


@Comment("模块菜单与url关联表[SYS_MODU_CODE_URL]")
public class SysModuCodeUrl implements EntityBean {
    private static final long serialVersionUID = 1L;


    @Comment("ID[ID]")
    private Long id;


    @Comment("模块代码[MODU_CODE]")
    private String moduCode;


    @Comment("URL[URL]")
    private String url;


    @Comment("所属域[DOMAIN_ID]")
    private Long domainId;


    @Comment("数据状态[DATA_STATUS]    数据状态：1-正常 0-删除")
    private Integer dataStatus;


    @Comment("创建时间[CREATE_TIME]    yyyyMMddHHmmss")
    private Long createTime;


    @Comment("修改时间[MODIFY_TIME]    yyyyMMddHHmmss")
    private Long modifyTime;


    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }


    public String getModuCode() {
        return this.moduCode;
    }

    public void setModuCode(String moduCode) {
        this.moduCode = moduCode;
    }


    public String getUrl() {
        return this.url;
    }

    public void setUrl(String url) {
        this.url = url;
    }


    public Long getDomainId() {
        return this.domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }


    public Integer getDataStatus() {
        return this.dataStatus;
    }

    public void setDataStatus(Integer dataStatus) {
        this.dataStatus = dataStatus;
    }


    public Long getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }


    public Long getModifyTime() {
        return this.modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }
}