package com.uinnova.product.vmdb.provider.sys.bean;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class PasswordConfig {

	private Integer minLength;
	private Integer maxLength;
	private List<PasswordComplicationType> complicationTypeList;
	private Integer inspireDay;
	private Integer informDay;
	private PasswordInitialType initialType;
	private String fixedInitialPwd;
	
	public PasswordConfig(JSONObject json) {
		if (json.containsKey("minLength") && json.getInteger("minLength")!=null) {
			this.minLength = json.getInteger("minLength");
		}
		if (json.containsKey("maxLength") && json.getInteger("maxLength")!=null) {
			this.maxLength = json.getInteger("maxLength");
		}
		if (json.containsKey("complicationTypeList") && json.getJSONArray("complicationTypeList")!=null) {
			complicationTypeList = new ArrayList<PasswordComplicationType>();
			JSONArray arr = json.getJSONArray("complicationTypeList");
			for (int i=0;i<arr.size();i++) {
				PasswordComplicationType type = PasswordComplicationType.valueOf(arr.getInteger(i));
				if (type!=null) {
					complicationTypeList.add(type);
				}
			}
		}
		if (json.containsKey("inspireDay") && json.getInteger("inspireDay")!=null) {
			this.inspireDay = json.getInteger("inspireDay");
		}
		if (json.containsKey("informDay")) {
			this.informDay = json.getInteger("informDay");
		}
		if (json.containsKey("initialType") && json.getInteger("initialType")!=null) {
			this.initialType = PasswordInitialType.valueOf(json.getInteger("initialType"));
		}
		if (json.containsKey("fixedInitialPwd") && json.getString("fixedInitialPwd")!=null && !"".equals(json.getString("fixedInitialPwd").trim())) {
			this.fixedInitialPwd = json.getString("fixedInitialPwd").trim();
		}
	}
	
	public Integer getMinLength() {
		return minLength;
	}
	public void setMinLength(Integer minLength) {
		this.minLength = minLength;
	}
	public Integer getMaxLength() {
		return maxLength;
	}
	public void setMaxLength(Integer maxLength) {
		this.maxLength = maxLength;
	}
	public List<PasswordComplicationType> getComplicationTypeList() {
		return complicationTypeList;
	}
	public void setComplicationTypeList(List<PasswordComplicationType> complicationTypeList) {
		this.complicationTypeList = complicationTypeList;
	}
	public Integer getInspireDay() {
		return inspireDay;
	}
	public void setInspireDay(Integer inspireDay) {
		this.inspireDay = inspireDay;
	}
	public Integer getInformDay() {
		return informDay;
	}
	public void setInformDay(Integer informDay) {
		this.informDay = informDay;
	}
	public PasswordInitialType getInitialType() {
		return initialType;
	}
	public void setInitialType(PasswordInitialType initialType) {
		this.initialType = initialType;
	}
	public String getFixedInitialPwd() {
		return fixedInitialPwd;
	}
	public void setFixedInitialPwd(String fixedInitialPwd) {
		this.fixedInitialPwd = fixedInitialPwd;
	}
	
	public JSONObject toJson() {
		JSONObject json = new JSONObject();
		json.put("minLength", this.minLength);
		json.put("maxLength", this.maxLength);
		JSONArray arr = new JSONArray();
		if (this.complicationTypeList!=null) {
			for (PasswordComplicationType type:complicationTypeList) {
				arr.add(type.getType());
			}
		}
		json.put("complicationTypeList", arr);
		json.put("inspireDay", this.inspireDay);
		json.put("informDay", this.informDay);
		json.put("initialType", this.initialType.getType());
		json.put("fixedInitialPwd", this.fixedInitialPwd);
		return json;
	}
}
