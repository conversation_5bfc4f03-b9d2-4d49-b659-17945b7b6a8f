package com.uinnova.product.eam.service.cj.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.map.MapUtil;
import com.binary.core.util.BinaryUtils;
import com.binary.framework.web.RemoteResult;
import com.uinnova.product.eam.model.cj.request.PlanDiagramDirBatchCopyParam;
import com.uinnova.product.eam.model.cj.request.PlanDiagramDirBatchMoveParam;
import com.uinnova.product.eam.model.constants.Constants;
import com.uinnova.product.eam.model.diagram.MoveDirAndDiagramCdt;
import com.uinnova.product.eam.service.cj.service.PlanDesignInstanceService;
import com.uinnova.product.eam.service.cj.service.PlanDiagramDirBatchService;
import com.uinnova.product.eam.service.diagram.ESDiagramSvc;
import com.uinnova.product.eam.service.exception.BusinessException;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 方案、视图、文件夹批量操作  service 实现类
 *
 * <AUTHOR>
 * @since 2022-3-9 19:49:22
 */
@Service
public class PlanDiagramDirBatchServiceImpl implements PlanDiagramDirBatchService {

    @Resource
    private PlanDesignInstanceService planDesignInstanceService;

    @Resource
    private ESDiagramSvc esDiagramApiClient;

    /**
     * 批量移动
     *
     * @param request {@link PlanDiagramDirBatchMoveParam}
     */
    @Override
    public void moveBatch(PlanDiagramDirBatchMoveParam request) {

        if (BinaryUtils.isEmpty(request.getTargetDirId())) {
            throw new BusinessException(Constants.BATCH_MOVE_FAILED_TARGET_DIR_IS_NULL);
        }

        if (!BinaryUtils.isEmpty(request.getDiagramIds()) || !BinaryUtils.isEmpty(request.getDirIds())) {
            // 调用feign接口移动文件夹和视图

            Integer result = 1;
            Long targetDirId = request.getTargetDirId();
            Long[] dirIds = request.getDirIds();
            String[] diagramIds = request.getDiagramIds();
            if (BinaryUtils.isEmpty(targetDirId)) {
                throw new BusinessException("目标文件夹不能为空");
            }
            if (BinaryUtils.isEmpty(dirIds) && BinaryUtils.isEmpty(diagramIds)) {
                throw new BusinessException("待移动目录和视图不能同时为空");
            }

            MoveDirAndDiagramCdt move  = new MoveDirAndDiagramCdt();
            BeanUtils.copyProperties(request, move);
            /*result = esDiagramApiClient.moveDirAndDiagram(move);
            if (result == 500) {
                throw new BusinessException("移动到的文件夹与当前移动的文件夹名称重复");
            }*/

            RemoteResult remoteResult = new RemoteResult(result);
            Map<String, Object> feignResult = BeanUtil.beanToMap(remoteResult);
            Object feignInvokeSuccess = feignResult.get("success");
            Object code = feignResult.get("code");
            if (code != null && (Integer)code == 500 && !((boolean) feignInvokeSuccess)) {
                if (feignResult.get("message") != null) {
                    throw new BusinessException(feignResult.get("message").toString());
                }
            }
            if (!(feignInvokeSuccess instanceof Boolean) || !((boolean) feignInvokeSuccess)) {
                throw new BusinessException(Constants.INVOKE_FEIGN_EXCEPTION);
            }
        }

        List<Long> planIdList = request.getPlanIdList();
        if (!BinaryUtils.isEmpty(planIdList)) {
            planDesignInstanceService.moveBatch(request.getTargetDirId(), planIdList);
        }
    }

    /**
     * 批量复制
     *
     * @param request {@link PlanDiagramDirBatchCopyParam}
     */
    @SuppressWarnings("unchecked")
    @Override
    public void copyBatch(PlanDiagramDirBatchCopyParam request) {
        Long targetDirId = request.getTargetDirId();
        if (BinaryUtils.isEmpty(targetDirId)) {
            throw new BusinessException(Constants.BATCH_MOVE_FAILED_TARGET_DIR_IS_NULL);
        }

        Map<Long, Long> oldDirIdAndNewDirIdMap = null;
        if (!BinaryUtils.isEmpty(request.getDiagramIds()) || !BinaryUtils.isEmpty(request.getDirIds())) {
            // 调用feign接口移动文件夹和视图

            Long[] dirIds = request.getDirIds();
            String[] diagramIds = request.getDiagramIds();
            if (BinaryUtils.isEmpty(targetDirId)) {
                throw new BusinessException("目标文件夹不能为空");
            }
            if ((BinaryUtils.isEmpty(dirIds) && BinaryUtils.isEmpty(diagramIds))) {
                throw new BusinessException("待复制文件夹id和视图id不能同时为空");
            }
            // 旧的文件夹id 和新的文件夹id对应关系
            Map<Long, Long> result = new HashMap<>();//esDiagramSvc.copyDirByIdCodeClone(targetDirId, dirIds, diagramIds, diagramCopyHandle);
            RemoteResult remoteResult = new RemoteResult(result);
            Map<String, Object> feignResult = BeanUtil.beanToMap(remoteResult);
            Object feignInvokeSuccess = feignResult.get("success");
            if (!(feignInvokeSuccess instanceof Boolean) || !((boolean) feignInvokeSuccess)) {
                throw new BusinessException(Constants.INVOKE_FEIGN_EXCEPTION);
            }

            Map<String, Long> dataMap = (Map<String, Long>) feignResult.get("data");
            if (!BinaryUtils.isEmpty(dataMap)) {
                oldDirIdAndNewDirIdMap = MapUtil.newHashMap(dataMap.size());
                for (Map.Entry<String, Long> entry : dataMap.entrySet()) {
                    String key = entry.getKey();
                    Long value = entry.getValue();
                    oldDirIdAndNewDirIdMap.put(Long.parseLong(key), value);
                }
            }
        }

        planDesignInstanceService.copyBatch(request.getPlanIdList(), oldDirIdAndNewDirIdMap, targetDirId);
    }
}
