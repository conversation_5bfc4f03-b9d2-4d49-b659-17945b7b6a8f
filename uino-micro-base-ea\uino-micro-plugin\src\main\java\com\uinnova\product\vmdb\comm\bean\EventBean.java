package com.uinnova.product.vmdb.comm.bean;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * 
 * <AUTHOR>
 *
 */
public class EventBean implements Serializable {
    private static final long serialVersionUID = 1L;
    private String id;
    private String summary;
    private Integer severity;
    private int tally;
    private String status;
    private String sourceName;
    private Integer sourceID;
    private Timestamp lastOccurrence;
    private String sourceIdentifier;
    private String sourceEventID;
    private String sourceCIName;
    private String sourceAlertKey;
    private String sourceSeverity;
    private String identifier;
    private String serial;
    private Timestamp firstOccurrence;
    private Timestamp stateChange;
    private String ciPrimaryKey;
    
    @Override
    public boolean equals(Object obj) {
        EventBean event = (EventBean) obj;
        return sourceCIName.equals(event.getSourceCIName()) && sourceAlertKey.equals(event.getSourceAlertKey());
    }

    @Override
    public int hashCode() {
        return super.hashCode();
    }
    
    public String getId() {
        if(id == null) {
            return getSerial();
        }
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Timestamp getFirstOccurrence() {
        return firstOccurrence;
    }

    public void setFirstOccurrence(Timestamp firstOccurrence) {
        this.firstOccurrence = firstOccurrence;
    }

    public String getSourceName() {
        return sourceName;
    }

    public void setSourceName(String sourceName) {
        this.sourceName = sourceName;
    }

    public Timestamp getStateChange() {
        return stateChange;
    }

    public void setStateChange(Timestamp stateChange) {
        this.stateChange = stateChange;
    }

    public String getSerial() {
        return serial;
    }

    public void setSerial(String serial) {
        this.serial = serial;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public Integer getSeverity() {
        return severity;
    }

    public void setSeverity(Integer severity) {
        this.severity = severity;
    }

    public int getTally() {
        return tally;
    }

    public void setTally(int tally) {
        this.tally = tally;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getSourceID() {
        return sourceID;
    }

    public void setSourceID(Integer sourceID) {
        this.sourceID = sourceID;
    }

    public String getSourceIdentifier() {
        return sourceIdentifier;
    }

    public void setSourceIdentifier(String sourceIdentifier) {
        this.sourceIdentifier = sourceIdentifier;
    }

    public String getSourceEventID() {
        return sourceEventID;
    }

    public void setSourceEventID(String sourceEventID) {
        this.sourceEventID = sourceEventID;
    }

    public String getSourceCIName() {
        return sourceCIName;
    }

    public void setSourceCIName(String sourceCIName) {
        this.sourceCIName = sourceCIName;
    }

    public String getSourceAlertKey() {
        return sourceAlertKey;
    }

    public void setSourceAlertKey(String sourceAlertKey) {
        this.sourceAlertKey = sourceAlertKey;
    }

    public String getSourceSeverity() {
        return sourceSeverity;
    }

    public void setSourceSeverity(String sourceSeverity) {
        this.sourceSeverity = sourceSeverity;
    }

    public Timestamp getLastOccurrence() {
        return lastOccurrence;
    }

    public void setLastOccurrence(Timestamp lastOccurrence) {
        this.lastOccurrence = lastOccurrence;
    }

    public String getIdentifier() {
        return identifier;
    }

    public void setIdentifier(String identifier) {
        this.identifier = identifier;
    }

    public String getCiPrimaryKey() {
        return ciPrimaryKey;
    }

    public void setCiPrimaryKey(String ciPrimaryKey) {
        this.ciPrimaryKey = ciPrimaryKey;
    }
    
    
    
}
