package com.uino.service.permission.microservice;

import com.binary.jdbc.Page;
import com.uino.bean.permission.base.*;
import com.uino.bean.permission.business.DataRole;
import com.uino.bean.permission.business.ModuleNodeInfo;
import com.uino.bean.permission.business.request.OptionUserModuleAuthRequestDto;
import com.uino.bean.permission.business.request.SaveRoleOrgRltRequestDto;
import com.uino.bean.permission.business.request.SaveRoleUserRltRequestDto;
import com.uino.bean.permission.query.CAuthBean;
import com.uino.bean.permission.query.CSysRoleDataModuleRlt;
import com.uino.bean.permission.query.SearchKeywordBean;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;

import java.util.List;

/**
 * 
 * <AUTHOR>
 *
 */
public interface IRoleSvc {

	/**
	 * 保存/更新
	 * 
	 * @param role
	 * @return
	 */
	public Long saveOrUpdate(SysRole role);

	/**
	 * 批量保存/更新
	 *
	 * @param roles
	 * @return
	 */
	public Integer saveOrUpdateBatch(Long domainId,List<SysRole> roles);
	/**
	 * 删除
	 * 
	 * @param roleId
	 * @return
	 */
	public Integer deleteById(Long roleId);

	/**
	 * 角色搜索--分页
	 * 
	 * @param bean
	 * @return
	 */
	public Page<SysRole> getRolePageByQuery(SearchKeywordBean bean);

    /**
     * 绑定角色和用户关系-全量
     * 
     * @param bean
     * @return
     */
    public Integer addRoleUserRlt(SaveRoleUserRltRequestDto bean);

    /**
     * 绑定角色和组织关系-全量
     * 
     * @param bean
     * @return
     */
    public Integer addRoleOrgRlt(SaveRoleOrgRltRequestDto bean);

	/**
	 * 添加角色和菜单的关系
	 * 
	 * @param bean
	 * @return
	 */
	public Integer addRoleMenuRlt(Long domainId,List<SysRoleModuleRlt> bean);

	/**
	 * 添加角色和数据模块的关系
	 * 
	 * @param bean
	 * @return
	 */
	public Integer addRoleDataModuleRlt(Long domainId, List<SysRoleDataModuleRlt> bean);

	/**
	 * 
	 * @description 添加角色和数据模块的关系
	 * @author: ZMJ
	 * @param domainId
	 *            所属域id
	 * @param rlts
	 *            角色-数据权限关系集合
	 * @param isComplete
	 *            是否全量(全量时仅支持保存一个角色的权限数据)
	 * @return
	 * @example
	 */
	public Integer addRoleDataModuleRlt(Long domainId, List<SysRoleDataModuleRlt> rlts, boolean isComplete);

	/**
	 * 添加用户和菜单的关系
	 * 
	 * @param bean
	 * @return
	 */
	public Integer addUserMenuRlt(Long domainId,List<SysUserModuleRlt> bean);

	/**
	 * 添加用户和数据模块的关系
	 * 
	 * @param bean
	 * @return
	 */
	public Integer addUserDataModuleRlt(Long domainId,List<SysUserDataModuleRlt> bean);

	/**
	 * 注册数据权限菜单项
	 * 
	 * @param dataModule
	 * @return
	 */
	public Long addDataModuleMenu(SysDataModule dataModule);

	/**
	 * 获取CI分类数据项
	 * 
	 * @return
	 */
	public List<DataRole> getDataRoleByCIClass(Long domainId);

	/**
	 * 获取CI标签数据项
	 * 
	 * @return
	 */
	public List<DataRole> getDataRoleByCITag(Long domainId);

	/**
	 * 获取数据权限菜单
	 * 
	 * @return
	 */
	public List<SysDataModule> getAllDataRoleMenu(Long domainId);

	/**
	 * 获取菜单
	 * 
	 * @return
	 */
	public ModuleNodeInfo getAllMenu(Long domainId);

	/**
	 * 获取角色下的菜单权限
	 * 
	 * @param roleId
	 * @return
	 */
	public List<SysRoleModuleRlt> getAuthMenuByRoleId(Long roleId);

	/**
     * 获取角色下的数据权限
     * 
     * @param bean
     * @return
     */
	public List<SysRoleDataModuleRlt> getAuthDataRoleByRoleId(CAuthBean bean);

	/**
     * 获取用户下的所有数据权限（roleId为空或不存在，表示是来自于用户本身）
     * 
     * @param bean
     * @return
     */
	public List<SysRoleDataModuleRlt> getAuthDataRoleByUserId(CAuthBean bean);

	/**
	 * 获取用户下的所有菜单权限（roleId为空或不存在，表示是来自于用户本身）
	 * 
	 * @param userId
	 * @return
	 */
	public List<SysRoleModuleRlt> getAuthMenuByUserId(Long userId);

	/**
	 * 获取某个用户绑定的角色信息
	 * 
	 * @param userId
	 * @return
	 */
	public List<SysRole> getRolesByUserId(Long userId);

	/**
	 * 根据数据模块id-转发数据模块数据源地址获取 数据模块数据
	 * 
	 * @param dataModuleId
	 * @return
	 */
	public Object getDataModuleDataById(Long dataModuleId);

	/**
     * 获取用户下的数据权限
     * 
     * @param bean
     * @return
     */
	public List<SysRoleDataModuleRlt> getUserAuthDataRoleByUserId(CAuthBean bean);

	/**
     * 获取用户下的所有角色数据权限（角色合并后的权限）
     * 
     * @param bean
     * @return
     */
	public List<SysRoleDataModuleRlt> getRoleAuthDataRoleByUserId(CAuthBean bean);

	/**
	 * 获取某个组织的角色
	 * 
	 * @param orgId
	 * @return
	 */
	public List<SysRole> getRolesByOrgId(Long orgId);

	/**
	 * 统计符合条件的角色数量
	 * 
	 * @param query
	 * @return
	 */
	public long countByCondition(QueryBuilder query);

	/**
	 * 获取符合条件的角色列表
	 * 
	 * @param query
	 * @return
	 */
	public List<SysRole> getRolesByQuery(QueryBuilder query);

	/**
	 * 操作用户与模块权限关系
	 * 
	 * @param req
	 */
	public void optionUserModuleAuth(OptionUserModuleAuthRequestDto req);

    /**
     * 根据角色id查询用户角色关系(包含来自于组织的角色,id为空表示来自于组织的角色)
     * 
     * @param roleId
     * @return
     */
    public List<SysUserRoleRlt> getUserRoleRltByRoleId(Long roleId);

    public List<SysRoleDataModuleRlt> getRoleDataModuleRltByCdt(CSysRoleDataModuleRlt cdt);

	public Integer deleteRoleDataModuleRlt(CSysRoleDataModuleRlt cdt);

	/**
	 * 根据id获取角色信息
	 * @param ids
	 * @param domainId
	 * @return
	 */
	List<SysRole> getRoleListByIds(List<Long> ids, Long domainId);

	/**
	 * 通过用户id及标识获取权限
	 * @param userId 用户id
	 * @param code 权限标识
	 * @return 权限
	 */
	List<SysRoleDataModuleRlt> getRoleByUserAndCode(Long userId, String code);

	/**
	 * 三员管理查询条件，日志功能使用
	 * @param query
	 */
	void sanyuanCondition(BoolQueryBuilder query,String type);

}
