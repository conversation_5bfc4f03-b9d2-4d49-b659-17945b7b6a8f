package com.uinnova.product.eam.comm.model.es;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;


/**
 * 层级配置VO
 * <AUTHOR>
 * @date 2022/1/5
 */
@Data
public class EamHierarchy {
    @Comment("主键id")
    private Long id;

    @Comment("领域id")
    private Long domainId;

    @Comment("层级名称")
    private String name;

    @Comment("业务类型 用于业务建模：BUSINESS")
    private String type;

    @Comment("层级Lvl")
    private Integer dirLvl;

    @Comment("父层级")
    private Long parentId;

    @Comment("关联制品id")
    private Long artifactId;

    @Comment("生成下一层级使用shape")
    private String shape;

    @Comment("引用制品下的默认分类Id")
    private Long effectClassId;

    @Comment("使用分类属性作为下级名称")
    private String proName;

    @Comment("继承父级节点shape")
    private String inheritShape;

    @Comment("继承父级节点布局")
    private Integer inheritLayout;

    @Comment("当前层级绑定的分类Id")
    private Long classId;

    @Comment("Number 1~6 1：价值链全景视图 2：价值链主视图 3：业务领域主视图 4：价值流主视图 5：活动主视图 6：任务主视图")
    private Integer modalType;

    @Comment("前导提示图")
    private String markedImg;

    @Comment("前导提示语")
    private String markedWord;

    @Comment("下级层级所属元素，当前层级元素选择字段")
    private String belongCIProName;

    @Comment("模型树id")
    private Long modelId;

    @Comment("是否继承父级节点布局")
    private Boolean inheritFlag;

    @Comment("成图查询数据")
    private String rltList;

    @Comment("单行最大实例个数")
    private Integer count;

    @Comment("模型不完整校验 是:true,否:false 默认false")
    private Boolean flag = false;

    @Comment("活动图是否校验拖拽开关 是:true,否:false 默认false")
    private Boolean checkActive = false;

    @Comment("步骤名称")
    private String stepName;
}
