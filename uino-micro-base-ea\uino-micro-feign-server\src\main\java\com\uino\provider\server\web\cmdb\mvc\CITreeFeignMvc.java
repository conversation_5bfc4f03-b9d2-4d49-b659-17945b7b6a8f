package com.uino.provider.server.web.cmdb.mvc;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.uino.service.cmdb.microservice.impl.CITreeSvc;
import com.uino.bean.cmdb.base.ESCITreeConfigInfo;
import com.uino.bean.cmdb.business.CITreeNode;
import com.uino.provider.feign.cmdb.CITreeFeign;

import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("feign/ciTree")
@Slf4j
public class CITreeFeignMvc implements CITreeFeign {

    @Autowired
    private CITreeSvc svc;

    @Override
    public List<ESCITreeConfigInfo> getCITreeConfigs(Long domainId) {
        // TODO Auto-generated method stub
        return svc.getCITreeConfigs(domainId);
    }

    @Override
    public ESCITreeConfigInfo getCITreeConfig(Long id) {
        // TODO Auto-generated method stub
        return svc.getCITreeConfig(id);
    }

    @Override
    public ESCITreeConfigInfo save(ESCITreeConfigInfo saveInfo) {
        // TODO Auto-generated method stub
        return svc.save(saveInfo);
    }

    @Override
    public List<ESCITreeConfigInfo> delete(Long domainId, Long id) {
        return svc.delete(domainId, id);
    }

    @Override
    public CITreeNode getCITree(ESCITreeConfigInfo config, boolean hasNullNode, boolean returnAllCI) {
        // TODO Auto-generated method stub
        return svc.getCITree(config, hasNullNode, returnAllCI);
    }
}
