---
# 数据基础服务
---

# 1.各模块引用方式

## 1.1只需要api服务，不需要预置web服务

```xml
<dependency>
			<groupId>com.uino</groupId>
			<artifactId>uino-micro-api</artifactId>
			<version>${uino-base.version}</version>
</dependency>
```

## 1.2需要web服务

```xml
<dependency>
			<groupId>com.uino</groupId>
			<artifactId>uino-micro-web</artifactId>
			<version>${uino-base.version}</version>
</dependency>
```
## 1.3调用方式
 - 本地jar调用
扫描bean时只扫描以下目录
```java
"com.uino.init", "com.uino.api.init", "com.uino.oauth2", "com.uino.oauth.common"
```

 -  rpc方式调用
启动了需增加额外注解
```java
@EnableFeignClients(basePackages={"com.uino.provider.feign"})
```


# 项目说明（以下简称该项目为base）
## 模块简要说明
### 

- **uino-micro-bean** : 提供base中自身所有用到的bean定义
- **uino-micro-service** : 提供base中的服务实现,真正的业务实现放在这里
- **uino-micro-feign-client** : spring cloud feign接口定义
- **uino-micro-feign-server** : 提供spring cloud feign服务
- **uino-micro-api** : 提供外部调用服务，所有下游业务系统对本系统服务调用都通过该jar
- **uino-micro-web** : 预置了一部分web服务
- **uino-micro-util** : 提供了一部分工具类
- **uino-micro-monitor** : 封装了性能以及告警的处理业务

	
## 初始化数据说明
	部分索引提供根据json文件初始化功能，预置初始化文件放置于uino-micro-service/src/main/resources/initdata/。当索引不存在时orm层的bean创建完毕时会进行索引初始化与数据初始化，内部初始化文件可被外部覆盖，各模块需定制初始化信息时可选择在外部resources建立initdata文件夹，在之下放入需要覆盖的初始化文件。目前支持初始化的文件有：

	uino_ci_dir.json					文件夹
	uino_ciclass.json					ci分类
	uino_cmdb_image.json				图标
	uino_oauth_client.json				oauth客户端
	uino_oauth_resource.json			oauth资源端
	uino_sys_data_module.json			数据模块
	uino_sys_logo.json					系统logo
	uino_sys_module.json				菜单
	uino_sys_org.json					组织架构
	uino_sys_role_module_rlt.json		角色与模块关联信息
	uino_sys_role.json					角色
	uino_sys_user_org_rlt.json			用户组织关联信息
	uino_sys_user_role_rlt.json			用户角色关联信息
	uino_sys_user.json					用户信息
