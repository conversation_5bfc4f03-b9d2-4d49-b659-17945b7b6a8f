package com.uinnova.product.eam.model.constants;

/**
 * 流程定义、流程任务定义常量
 *
 */
public class FlowableConstant {
    /**
     * 架构决策流程定义key
     */
    public static final String DECISION_DEFINITION_KEY = "architecture_decision";
    /**
     * 方案审批流程定义key-一个审批节点
     */
    public static final String PLAN_DEFINITION_KEY1 = "cj_technical_scheme_approve";
    /**
     * 方案审批流程定义key-两个审批节点
     */
    public static final String PLAN_DEFINITION_KEY2 = "xw_it_technical_approve";

    /**
     * 视图审批流程定义key
     */
    public static final String DIAGRAM_DEFINITION_KEY = "xw_it_diagram_approve";

    /**
     * 模型审批流程定义key
     */
    public static final String MODEL_DEFINITION_KEY = "xw_model_approve";

    /**
     * 流程体系-末级流程发布
     */
    public static final String FLOW_SYSTEM_PUBLISH_APPROVE = "flow_system_publish_approve";

    /**
     * 流程体系-末级流程签发
     */
    public static final String FLOW_SYSTEM_FLOW_SIGN = "flow_system_flow_sign";

    /**
     * 业务方案审批流程定义key
     */
    public static final String XW_BUSINESS_SCENARIO_APPROVE = "xw_business_scenario_approve";

    /**
     * 流程第一任务节点-组织审批节点
     */
    public static final String ORGANIZE_TASK_KEY = "OrganizationalStructureReviewTask";

    /**
     * 流程第二任务节点-文件夹管理员审批
     */
    public static final String CATEGORY_TASK_KEY = "PublishLocationDirectoryTask";

    /**
     * 一级审批角色
     */
    public static final String APPROVAL_ROLE = "组织架构审批角色";

    /**
     * 管理员审批角色
     */
    public static final String ADMIN_ROLE = "admin";

    /**
     *  普通视图审批二级节点结束描述
     */
    public static final String IT_VIEW_END_NODE = "IT制品审批流程结束";

    /**
     * 企业级一级主题域owner节点
     */
    public static final String MODEL_ENTERPRISE_TASK_KEY = "enterprise";

    /**
     * 业务领域owner节点
     */
    public static final String MODEL_BUSINESS_TASK_KEY = "business";

    /**
     * 元模型审批定义key
     */
    public static final String VISUAL_MODEL_PUBLISH_APPROVE = "visual_model_publish_approve";
}
