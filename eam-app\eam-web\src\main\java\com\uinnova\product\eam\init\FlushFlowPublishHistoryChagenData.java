package com.uinnova.product.eam.init;

import com.binary.jdbc.Page;
import com.uinnova.product.eam.comm.model.es.FlowProcessSystemPublishHistory;
import com.uinnova.product.eam.service.ICISwitchSvc;
import com.uinnova.product.eam.service.es.FlowProcessSystemPublishHistoryDao;
import com.uinnova.product.eam.service.es.IamsESCIDesignSvc;
import com.uinnova.product.eam.service.impl.IamsCIDesignSvc;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.base.LibType;
import com.uino.bean.cmdb.query.ESCISearchBean;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.util.*;

/**
 * description
 *
 * <AUTHOR>
 * @since 2024/11/12 11:01
 */
@Component
public class FlushFlowPublishHistoryChagenData implements ApplicationRunner {

    @Resource
    private FlowProcessSystemPublishHistoryDao flowProcessSystemPublishHistoryDao;

    @Resource
    private ICISwitchSvc iciSwitchSvc;

    @Autowired
    private IamsESCIDesignSvc esCiSvc;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        List<FlowProcessSystemPublishHistory> flowSystemPublishHistory  = flowProcessSystemPublishHistoryDao
                .getSortListByQueryScroll(QueryBuilders.matchAllQuery(), "createTime", false);
        ArrayList<FlowProcessSystemPublishHistory> flowProcessSystemPublishHistories = new ArrayList<>();
        HashSet<String> ciCodes = new HashSet<>();
        ArrayList<FlowProcessSystemPublishHistory> needFlushData = new ArrayList<>();
        for (FlowProcessSystemPublishHistory flowProcessSystemPublishHistory : flowSystemPublishHistory) {
            String flowName = flowProcessSystemPublishHistory.getFlowName();
            if(StringUtils.isBlank(flowName)||"null null".equalsIgnoreCase(flowName)){
                String ciCode = flowProcessSystemPublishHistory.getCiCode();
                ciCodes.add(ciCode);
                needFlushData.add(flowProcessSystemPublishHistory);
            }
        }
        ArrayList<FlowProcessSystemPublishHistory> flowProcessSystemPublishHistories1 = new ArrayList<>();
        if(!CollectionUtils.isEmpty(ciCodes)){
            ESCISearchBean esciSearchBean = new ESCISearchBean();
            esciSearchBean.setCiCodes(new ArrayList<>(ciCodes));
            esciSearchBean.setPageSize(ciCodes.size());
            esciSearchBean.setPageNum(1);
            esciSearchBean.setDomainId(1L);
            Page<ESCIInfo> esciInfoPage = esCiSvc.searchESCIByBean(esciSearchBean);
            List<ESCIInfo> ciByCodes = esciInfoPage.getData();
            Map<String, ESCIInfo> objectObjectHashMap = new HashMap<>();
            for (ESCIInfo ciByCode : ciByCodes) {
                objectObjectHashMap.put(ciByCode.getCiCode(),ciByCode);
            }
            for (FlowProcessSystemPublishHistory needFlushDatum : needFlushData) {
                String ciCode = needFlushDatum.getCiCode();
                ESCIInfo esciInfo = objectObjectHashMap.get(ciCode);
                if(esciInfo!=null){
                    Object flowCode = esciInfo.getAttrs().get("流程编码");
                    if(flowCode==null){
                        flowCode = esciInfo.getAttrs().get("流程组编码");
                    }
                    Object flowName = esciInfo.getAttrs().get("流程名称");
                    if(flowName==null){
                        flowName=esciInfo.getAttrs().get("流程组名称");
                    }
                    if(flowCode!=null&&flowName!=null){
                        needFlushDatum.setFlowName(flowCode+" "+flowName);
                        String changeData = needFlushDatum.getChangeData();
                        String s = changeData.replaceFirst("null", flowCode.toString());
                        s = s.replaceFirst("null",flowName.toString());
                        needFlushDatum.setChangeData(s);
                        flowProcessSystemPublishHistories1.add(needFlushDatum);
                    }
                }
            }
            flowProcessSystemPublishHistoryDao.saveOrUpdateBatch(flowProcessSystemPublishHistories1);
        }



    }
}
