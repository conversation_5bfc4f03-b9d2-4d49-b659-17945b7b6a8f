package com.uino.bean.cmdb.base.dataset.relation;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class RelationRuleNode implements Serializable {

	private static final long serialVersionUID = 1L;

	private Long classId;
	private Long pageNodeId;
	private Double x;
	private Double y;
	private String nodeReturns;
	private String attachField;
	private List<RelationRuleAttrCdt> cdts;
	
	public RelationRuleNode() {
		
	}
	
	public RelationRuleNode(JSONObject json) {
		if (json.containsKey("classId")) {
			this.classId = json.getLong("classId");
		}
		if (json.containsKey("pageNodeId")) {
			this.pageNodeId = json.getLong("pageNodeId");
		}
		if (json.containsKey("x")) {
			this.x = json.getDouble("x");
		}
		if (json.containsKey("y")) {
			this.y = json.getDouble("y");
		}
		if (json.containsKey("nodeReturns")) {
			this.nodeReturns = json.getString("nodeReturns");
		}
		if (json.containsKey("attachField")) {
			this.attachField = json.getString("attachField");
		}
		if (json.containsKey("cdts") && json.getJSONArray("cdts")!=null) {
			this.cdts = new ArrayList<RelationRuleAttrCdt>();
			for (int i=0;i<json.getJSONArray("cdts").size();i++) {
				this.cdts.add(new RelationRuleAttrCdt(json.getJSONArray("cdts").getJSONObject(i)));
			}
		}
	}
	
	public List<RelationRuleAttrCdt> getCdts() {
		return cdts;
	}
	public void setCdts(List<RelationRuleAttrCdt> cdts) {
		this.cdts = cdts;
	}
	public Long getClassId() {
		return classId;
	}
	public void setClassId(Long classId) {
		this.classId = classId;
	}
	public Long getPageNodeId() {
		return pageNodeId;
	}
	public void setPageNodeId(Long pageNodeId) {
		this.pageNodeId = pageNodeId;
	}
	public Double getX() {
		return x;
	}
	public void setX(Double x) {
		this.x = x;
	}
	public Double getY() {
		return y;
	}
	public void setY(Double y) {
		this.y = y;
	}
	public String getNodeReturns() {
		return nodeReturns;
	}
	public void setNodeReturns(String nodeReturns) {
		this.nodeReturns = nodeReturns;
	}

	public String getAttachField() {
		return attachField;
	}

	public void setAttachField(String attachField) {
		this.attachField = attachField;
	}

	public JSONObject toJson() {
		JSONObject json = new JSONObject();
		json.put("classId", classId);
		json.put("pageNodeId", pageNodeId);
		json.put("x", x);
		json.put("y", y);
		json.put("nodeReturns", nodeReturns);
		json.put("attachField", attachField);
		if (this.cdts!=null) {
			JSONArray arr = new JSONArray();
			for (RelationRuleAttrCdt cdt:cdts) {
				arr.add(cdt.toJson());
			}
			json.put("cdts", arr);
		}
		return json;
	}
}
