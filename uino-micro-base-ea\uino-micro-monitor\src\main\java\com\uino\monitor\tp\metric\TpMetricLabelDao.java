package com.uino.monitor.tp.metric;

import jakarta.annotation.PostConstruct;

import org.springframework.stereotype.Repository;

import com.uino.dao.AbstractESBaseDao;
import com.uino.dao.ESConst;
import com.uino.bean.tp.base.TpMetricLabelDTO;

@Repository
public class TpMetricLabelDao extends AbstractESBaseDao<TpMetricLabelDTO, TpMetricLabelDTO> {
    @Override
    public String getIndex() {
		return ESConst.INDEX_TP_METRIC_LABEL;
    }

    @Override
    public String getType() {
		return ESConst.INDEX_TP_METRIC_LABEL;
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }
}
