package com.uino.provider.server.web.cmdb.mvc;

import com.uino.service.cmdb.dataset.microservice.IGraphAnalysisSvc;
import com.uino.bean.cmdb.base.dataset.DataSetMallApiRelationRuleExternal;
import com.uino.bean.cmdb.business.dataset.FriendInfo;
import com.uino.bean.cmdb.business.dataset.UpDownAttrCdt;
import com.uino.provider.feign.cmdb.GraphAnalysisFeign;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @data 2019/8/7 16:32.
 */
@RestController
@RequestMapping("feign/graphAnaly")
public class GraphAnalysisFeignMvc implements GraphAnalysisFeign {

    @Autowired
    private IGraphAnalysisSvc graphAnalysisSvc;

    @Override
    public FriendInfo queryCiUpDownByCiIds(Long domainId, List<Long> startCiIds, List<UpDownAttrCdt> ciConditions, List<UpDownAttrCdt> rltConditions, List<Long> rltLvls, Integer upLevel, Integer downLevel, Boolean hasAttr) {
        return graphAnalysisSvc.queryCiUpDownByCiIds(domainId, startCiIds, ciConditions, rltConditions, rltLvls, upLevel, downLevel, hasAttr);
    }

    @Override
    public FriendInfo queryCiUpDownByCiId(Long domainId, Long startCiId, List<UpDownAttrCdt> ciConditions, List<UpDownAttrCdt> rltConditions, List<Long> rltLvls, Integer upLevel, Integer downLevel, Boolean hasAttr) {
        return graphAnalysisSvc.queryCiUpDownByCiId(domainId, startCiId, ciConditions, rltConditions, rltLvls, upLevel, downLevel, hasAttr);
    }

	@Override
	public FriendInfo queryFriendByCiUsingRule(List<String> enter, DataSetMallApiRelationRuleExternal rule) {
		return graphAnalysisSvc.queryFriendByCiUsingRule(enter, rule);
	}
}
