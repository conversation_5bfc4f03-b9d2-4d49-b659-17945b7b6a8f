package com.uinnova.product.eam.comm.model.es;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.base.model.ChangeInfoDto;
import lombok.Data;

/**
 * 资产变更记录表
 *
 * <AUTHOR>
 */
@Data
public class AssetChangeRecord {

    @Comment("主键id")
    private Long id;

    @Comment("ciCode")
    private String ciCode;

    @Comment("类型： 1:创建、2:变更、3:作废,4:关联")
    private String type;

    @Comment("变更原因")
    private String changeReason;
    /**
     * [{
     *     changeAttr:"",
     *     before:"",
     *     after:"",
     *     type:""
     *
     * }
     * ]
     */
    @Comment("变更内容")
    private String changeContent;
    @Comment("视图信息变更")
    private String diagramChange;
    @Comment("方案信息变更")
    private String planChange;
    @Comment("决策信息变更")
    private String decisionChange;
    @Comment("状态 1:审批中，2：审批通过")
    private Integer state;

    @Comment("领域id")
    private Long domainId;

    @Comment("提交人")
    private String submitter;

    @Comment("变更时间")
    private Long createTime;

    @Comment("修改人")
    private String modifier;

    @Comment("修改时间")
    private Long modifyTime;

    private ChangeInfoDto diagramJson;
    private ChangeInfoDto planJson;
    private ChangeInfoDto decisionJson;
    private String ciLabel;
    private String className;

}


