package com.uino.api.client.permission.local;

import java.util.List;

import com.uino.bean.permission.query.CSysRoleDataModuleRlt;
import org.elasticsearch.index.query.QueryBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.binary.jdbc.Page;
import com.uino.api.client.permission.IRoleApiSvc;
import com.uino.bean.permission.base.SysDataModule;
import com.uino.bean.permission.base.SysRole;
import com.uino.bean.permission.base.SysRoleDataModuleRlt;
import com.uino.bean.permission.base.SysRoleModuleRlt;
import com.uino.bean.permission.base.SysUserDataModuleRlt;
import com.uino.bean.permission.base.SysUserModuleRlt;
import com.uino.bean.permission.base.SysUserRoleRlt;
import com.uino.bean.permission.business.DataRole;
import com.uino.bean.permission.business.ModuleNodeInfo;
import com.uino.bean.permission.business.request.OptionUserModuleAuthRequestDto;
import com.uino.bean.permission.business.request.SaveRoleOrgRltRequestDto;
import com.uino.bean.permission.business.request.SaveRoleUserRltRequestDto;
import com.uino.bean.permission.query.CAuthBean;
import com.uino.bean.permission.query.SearchKeywordBean;
import com.uino.dao.BaseConst;
import com.uino.service.permission.microservice.IRoleSvc;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
public class RoleApiSvcLocal implements IRoleApiSvc {
	@Autowired
    private IRoleSvc roleSvc;

	@Override
	public Long saveOrUpdate(SysRole role) {

		return roleSvc.saveOrUpdate(role);
	}

	@Override
	public Integer saveOrUpdateBatch(Long domainId,List<SysRole> roles) {
		return roleSvc.saveOrUpdateBatch(domainId,roles);
	}

	@Override
	public Integer deleteById(Long roleId) {

		return roleSvc.deleteById(roleId);
	}

	@Override
	public List<SysRole> getRoleListByIds(List<Long> ids,Long domainId) {
		return roleSvc.getRoleListByIds(ids, domainId);
	}

	@Override
	public Page<SysRole> getRolePageByQuery(SearchKeywordBean bean) {

		return roleSvc.getRolePageByQuery(bean);
	}

    @Override
    public Integer addRoleUserRlt(SaveRoleUserRltRequestDto bean) {
        return roleSvc.addRoleUserRlt(bean);
    }

    @Override
    public Integer addRoleOrgRlt(SaveRoleOrgRltRequestDto bean) {
        return roleSvc.addRoleOrgRlt(bean);
    }

    @Override
	public Integer addRoleMenuRlt(List<SysRoleModuleRlt> bean) {

		return roleSvc.addRoleMenuRlt(BaseConst.DEFAULT_DOMAIN_ID,bean);
	}

	@Override
	public Integer addRoleMenuRlt(Long domainId, List<SysRoleModuleRlt> bean) {
		return roleSvc.addRoleMenuRlt(domainId,bean);
	}

	@Override
	public Integer addRoleDataModuleRlt(List<SysRoleDataModuleRlt> bean) {
		return roleSvc.addRoleDataModuleRlt(BaseConst.DEFAULT_DOMAIN_ID, bean, true);
	}

	@Override
	public Integer addRoleDataModuleRlt(Long domainId, List<SysRoleDataModuleRlt> rlts, boolean isComplete) {
		return roleSvc.addRoleDataModuleRlt(domainId, rlts, isComplete);
	}

	@Override
	public Integer addRoleDataModuleRlt(Long domainId, List<SysRoleDataModuleRlt> bean) {
		return roleSvc.addRoleDataModuleRlt(domainId, bean, true);
	}

	@Override
	public Integer addUserMenuRlt(List<SysUserModuleRlt> bean) {

		return roleSvc.addUserMenuRlt(BaseConst.DEFAULT_DOMAIN_ID,bean);
	}

	@Override
	public Integer addUserMenuRlt(Long domainId, List<SysUserModuleRlt> bean) {
		return roleSvc.addUserMenuRlt(domainId,bean);
	}

	@Override
	public Integer addUserDataModuleRlt(List<SysUserDataModuleRlt> bean) {

		return roleSvc.addUserDataModuleRlt(BaseConst.DEFAULT_DOMAIN_ID,bean);
	}

	@Override
	public Integer addUserDataModuleRlt(Long domainId, List<SysUserDataModuleRlt> bean) {
		return roleSvc.addUserDataModuleRlt(domainId,bean);
	}

	@Override
	public Long addDataModuleMenu(SysDataModule dataModule) {
		return roleSvc.addDataModuleMenu(dataModule);
	}

	@Override
	public List<DataRole> getDataRoleByCIClass() {

		return roleSvc.getDataRoleByCIClass(BaseConst.DEFAULT_DOMAIN_ID);
	}

	@Override
	public List<DataRole> getDataRoleByCIClass(Long domainId) {
		return roleSvc.getDataRoleByCIClass(domainId);
	}

	@Override
	public List<DataRole> getDataRoleByCITag() {

		return roleSvc.getDataRoleByCITag(BaseConst.DEFAULT_DOMAIN_ID);
	}

	@Override
	public List<DataRole> getDataRoleByCITag(Long domainId) {
		return roleSvc.getDataRoleByCITag(domainId);
	}

	@Override
	public List<SysDataModule> getAllDataRoleMenu() {

		return roleSvc.getAllDataRoleMenu(BaseConst.DEFAULT_DOMAIN_ID);
	}

	@Override
	public List<SysDataModule> getAllDataRoleMenu(Long domainId) {
		return roleSvc.getAllDataRoleMenu(domainId);
	}

	@Override
	public List<SysRoleDataModuleRlt> getRoleDataModuleRltByCdt(CSysRoleDataModuleRlt cdt) {
		return roleSvc.getRoleDataModuleRltByCdt(cdt);
	}

	@Override
	public ModuleNodeInfo getAllMenu() {

		return roleSvc.getAllMenu(BaseConst.DEFAULT_DOMAIN_ID);
	}

	@Override
	public ModuleNodeInfo getAllMenu(Long domainId) {
		return roleSvc.getAllMenu(domainId);
	}

	@Override
	public List<SysRoleModuleRlt> getAuthMenuByRoleId(Long roleId) {

		return roleSvc.getAuthMenuByRoleId(roleId);
	}

	@Override
	public List<SysRoleDataModuleRlt> getAuthDataRoleByRoleId(CAuthBean bean) {

		return roleSvc.getAuthDataRoleByRoleId(bean);
	}

	@Override
	public List<SysRoleDataModuleRlt> getAuthDataRoleByUserId(CAuthBean bean) {

		return roleSvc.getAuthDataRoleByUserId(bean);
	}

	@Override
	public List<SysRoleModuleRlt> getAuthMenuByUserId(Long userId) {

		return roleSvc.getAuthMenuByUserId(userId);
	}

	@Override
	public List<SysRole> getRolesByUserId(Long userId) {

		return roleSvc.getRolesByUserId(userId);
	}

	@Override
	public Object getDataModuleDataById(Long dataModuleId) {

		return roleSvc.getDataModuleDataById(dataModuleId);
	}

	@Override
	public List<SysRoleDataModuleRlt> getUserAuthDataRoleByUserId(CAuthBean bean) {

		return roleSvc.getUserAuthDataRoleByUserId(bean);
	}

	@Override
	public List<SysRoleDataModuleRlt> getRoleAuthDataRoleByUserId(CAuthBean bean) {

		return roleSvc.getRoleAuthDataRoleByUserId(bean);
	}

	@Override
	public List<SysRole> getRolesByOrgId(Long orgId) {
		return roleSvc.getRolesByOrgId(orgId);
	}

	@Override
	public long countByCondition(QueryBuilder query) {
		return roleSvc.countByCondition(query);
	}

	@Override
	public List<SysRole> getRolesByQuery(QueryBuilder query) {
		return roleSvc.getRolesByQuery(query);
	}

	@Override
	public void optionUserModuleAuth(OptionUserModuleAuthRequestDto req) {
		// TODO Auto-generated method stub
		roleSvc.optionUserModuleAuth(req);
	}

    @Override
    public List<SysUserRoleRlt> getUserRoleRltByRoleId(Long roleId) {
        return roleSvc.getUserRoleRltByRoleId(roleId);
    }

	@Override
	public Integer deleteRoleDataModuleRlt(CSysRoleDataModuleRlt cdt) {
		return roleSvc.deleteRoleDataModuleRlt(cdt);
	}

}
