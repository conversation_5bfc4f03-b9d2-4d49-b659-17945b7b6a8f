package com.uinnova.product.vmdb.comm.model.ci;

import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;

/**
 * 
 * <AUTHOR>
 *
 */
@Comment("CI动态分类模版[CC_DYNAMIC_CLASS_TPL]")
public class CcDynamicClassTpl implements EntityBean {
    private static final long serialVersionUID = 1L;

    @Comment("ID[ID]")
    private Long id;

    @Comment("模板名称[TPL_NAME]")
    private String tplName;

    @Comment("模板描述[TPL_DESC]")
    private String tplDesc;

    @Comment("分级名称_1[LEVEL_NAME_1]")
    private String levelName1;

    @Comment("分级名称_2[LEVEL_NAME_2]")
    private String levelName2;

    @Comment("分级名称_3[LEVEL_NAME_3]")
    private String levelName3;

    @Comment("分级名称_4[LEVEL_NAME_4]")
    private String levelName4;

    @Comment("分级名称_5[LEVEL_NAME_5]")
    private String levelName5;

    @Comment("分级名称_6[LEVEL_NAME_6]")
    private String levelName6;

    @Comment("筛选CI分类[CI_CLASS_IDS]    筛选CI分类的ID,多个以逗号隔开")
    private String ciClassIds;

    @Comment("模版状态[STATUS]    模版状态: 1=有效 0=无效 2=构建中")
    private Integer status;

    @Comment("所属域[DOMAIN_ID]")
    private Long domainId;

    @Comment("数据状态[DATA_STATUS]    数据状态:1-正常 0-删除")
    private Integer dataStatus;

    @Comment("创建人[CREATOR]")
    private String creator;

    @Comment("修改人[MODIFIER]")
    private String modifier;

    @Comment("创建时间[CREATE_TIME]    创建时间:yyyyMMddHHmmss")
    private Long createTime;

    @Comment("修改时间[MODIFY_TIME]    修改时间:yyyyMMddHHmmss")
    private Long modifyTime;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTplName() {
        return this.tplName;
    }

    public void setTplName(String tplName) {
        this.tplName = tplName;
    }

    public String getTplDesc() {
        return this.tplDesc;
    }

    public void setTplDesc(String tplDesc) {
        this.tplDesc = tplDesc;
    }

    public String getLevelName1() {
        return this.levelName1;
    }

    public void setLevelName1(String levelName1) {
        this.levelName1 = levelName1;
    }

    public String getLevelName2() {
        return this.levelName2;
    }

    public void setLevelName2(String levelName2) {
        this.levelName2 = levelName2;
    }

    public String getLevelName3() {
        return this.levelName3;
    }

    public void setLevelName3(String levelName3) {
        this.levelName3 = levelName3;
    }

    public String getLevelName4() {
        return this.levelName4;
    }

    public void setLevelName4(String levelName4) {
        this.levelName4 = levelName4;
    }

    public String getLevelName5() {
        return this.levelName5;
    }

    public void setLevelName5(String levelName5) {
        this.levelName5 = levelName5;
    }

    public String getLevelName6() {
        return this.levelName6;
    }

    public void setLevelName6(String levelName6) {
        this.levelName6 = levelName6;
    }

    public String getCiClassIds() {
        return this.ciClassIds;
    }

    public void setCiClassIds(String ciClassIds) {
        this.ciClassIds = ciClassIds;
    }

    public Integer getStatus() {
        return this.status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Long getDomainId() {
        return this.domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }

    public Integer getDataStatus() {
        return this.dataStatus;
    }

    public void setDataStatus(Integer dataStatus) {
        this.dataStatus = dataStatus;
    }

    public String getCreator() {
        return this.creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getModifier() {
        return this.modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public Long getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getModifyTime() {
        return this.modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }

}
