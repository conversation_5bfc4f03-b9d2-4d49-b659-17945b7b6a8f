package com.uinnova.product.eam.web;

import com.uinnova.product.eam.init.UserOnlineInterceptor;
import com.uino.init.SwitchLiberaryInterceptor;
import com.uino.util.rsm.RsmInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import jakarta.annotation.Resource;

@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    @Resource
    private SwitchLiberaryInterceptor switchLiberaryInterceptor;

    @Resource
    private UserOnlineInterceptor userOnlineInterceptor;

    @Autowired
    private RsmInterceptor rsmInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(rsmInterceptor).addPathPatterns("/**").order(Ordered.HIGHEST_PRECEDENCE);
        registry.addInterceptor(switchLiberaryInterceptor);
        registry.addInterceptor(userOnlineInterceptor);
    }
}
