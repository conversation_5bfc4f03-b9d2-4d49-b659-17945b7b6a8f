package com.uino.api.client.cmdb.rpc;

import com.alibaba.fastjson.JSONObject;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uino.bean.cmdb.base.dataset.DataSetMallApiRelationRule;
import com.uino.bean.cmdb.base.dataset.batch.SimpleFriendInfo;
import com.uino.bean.cmdb.base.dataset.chart.Chart;
import com.uino.bean.cmdb.base.dataset.query.QueryCiFriendDto;
import com.uino.bean.cmdb.business.dataset.FriendInfo;
import com.uino.bean.cmdb.business.dataset.QueryCondition;
import com.uino.bean.cmdb.business.dataset.RltRuleTableData;
import com.uino.provider.feign.cmdb.RelationRuleAnalysisFegin;
import com.uino.api.client.cmdb.IRelationRuleAnalysisApiSvc;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * @Title: IRelationRuleAnalysisApiSvcLocal
 * @Description:
 * @Author: YGQ
 * @Create: 2021-05-31 14:22
 **/
@Service
public class RelationRuleAnalysisApiSvcRpc implements IRelationRuleAnalysisApiSvc {

    private final RelationRuleAnalysisFegin relationRuleAnalysisFegin;

    @Autowired
    public RelationRuleAnalysisApiSvcRpc(RelationRuleAnalysisFegin relationRuleAnalysisFegin) {
        this.relationRuleAnalysisFegin = relationRuleAnalysisFegin;
    }


    @Override
    public Map<Long, List<QueryCondition>> findTravalTree(DataSetMallApiRelationRule relationRule) {
        return relationRuleAnalysisFegin.findTravalTree(relationRule);
    }

    @Override
    public FriendInfo queryCiFriendByCiId(CcCiInfo sCi, Long dataSetId) {
        QueryCiFriendDto query = QueryCiFriendDto.builder().sCi(sCi).dataSetId(dataSetId).build();
        return relationRuleAnalysisFegin.queryCiFriendByCiId(query);
    }

    @Override
    public FriendInfo queryCiFriendByCiIdAndRule(CcCiInfo sCi, DataSetMallApiRelationRule relationRule) {
        QueryCiFriendDto query = QueryCiFriendDto.builder().sCi(sCi).relationRule(relationRule).build();
        return relationRuleAnalysisFegin.queryCiFriendByCiIdAndRule(query);
    }

    @Override
    public Map<Long, FriendInfo> queryCiFriendByCiIds(List<CcCiInfo> sCis, Long dataSetId, boolean isIncludeAllStartCi) {
        QueryCiFriendDto query = QueryCiFriendDto.builder().sCis(sCis).dataSetId(dataSetId).isIncludeAllStartCI(isIncludeAllStartCi).build();
        return relationRuleAnalysisFegin.queryCiFriendByCiIds(query);
    }

    @Override
    public Map<Long, FriendInfo> queryCiFriendByCiIdsAndRule(List<CcCiInfo> sCis, DataSetMallApiRelationRule relationRule, boolean isIncludeAllStartCi) {
        QueryCiFriendDto query = QueryCiFriendDto.builder().sCis(sCis).relationRule(relationRule).isIncludeAllStartCI(isIncludeAllStartCi).build();
        return relationRuleAnalysisFegin.queryCiFriendByCiIdsAndRule(query);
    }

    @Override
    public Map<Long, FriendInfo> queryCiFriendByRule(DataSetMallApiRelationRule relationRule) {
        return relationRuleAnalysisFegin.queryCiFriendByRule(relationRule);
    }

    @Override
    public Map<Long, FriendInfo> queryCiFriendByRuleWithLimit(DataSetMallApiRelationRule relationRule, Integer limit) {
        QueryCiFriendDto query = QueryCiFriendDto.builder().relationRule(relationRule).limit(limit).build();
        return relationRuleAnalysisFegin.queryCiFriendByRuleWithLimit(query);
    }

    @Override
    public FriendInfo mergeFriendInfoMap(Map<Long, FriendInfo> friendInfoMap) {
        return relationRuleAnalysisFegin.mergeFriendInfoMap(friendInfoMap);
    }

    @Override
    public RltRuleTableData disassembleFriendInfoDataByPath(DataSetMallApiRelationRule relationRule, List<Long> ciIds, FriendInfo friendInfo) {
        QueryCiFriendDto query = QueryCiFriendDto.builder().relationRule(relationRule).ciIds(ciIds).friendInfo(friendInfo).build();
        return relationRuleAnalysisFegin.disassembleFriendInfoDataByPath(query);
    }

    @Override
    public RltRuleTableData disassembleFriendInfoDataByPath(List<CcCiInfo> sCis, DataSetMallApiRelationRule relationRule) {
        QueryCiFriendDto query = QueryCiFriendDto.builder().sCis(sCis).relationRule(relationRule).build();
        return relationRuleAnalysisFegin.disassembleFriendInfoDataByCisAndRule(query);
    }

    @Override
    public JSONObject countStatistics(DataSetMallApiRelationRule relationRule, Map<Long, SimpleFriendInfo> simpleFriendInfoMap, Chart chart) {
        QueryCiFriendDto query = QueryCiFriendDto.builder().relationRule(relationRule).simpleFriendInfoMap(simpleFriendInfoMap).chart(chart).build();
        return relationRuleAnalysisFegin.countStatistics(query);
    }
}
