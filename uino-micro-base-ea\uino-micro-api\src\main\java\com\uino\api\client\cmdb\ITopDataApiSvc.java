package com.uino.api.client.cmdb;

/**
 * 置顶操作
 * 
 * <AUTHOR>
 */
public interface ITopDataApiSvc {
    /**
     * 取消置顶
     * 
     * @param topData 取消置顶的数据
     * @param topDataType 取消置顶的数据类型3：图标
     */
    void unTop(Long topData, Long topDataType);

    void unTop(Long domainId,Long topData, Long topDataType);

    /**
     * 置顶数据
     * 
     * @param topData
     * @param topDataType
     */
    void top(Long topData, Long topDataType);

    void top(Long domainId,Long topData, Long topDataType);
}
