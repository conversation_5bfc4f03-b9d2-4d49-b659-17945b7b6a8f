package com.uinnova.product.eam.model.cj.vo;

import com.binary.framework.bean.annotation.Comment;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 方案分享详情VO
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PlanShareDetailVO {

    @Comment("创建者登录账号")
    private String ownerLoginCode;

    @Comment("分享列表")
    private List<Map<String, Object>> shareList;
}
