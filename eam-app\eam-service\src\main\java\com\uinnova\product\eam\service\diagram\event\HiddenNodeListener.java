package com.uinnova.product.eam.service.diagram.event;

import com.uinnova.product.eam.model.diagram.event.DiagramRuleEnum;
import com.uinnova.product.eam.model.diagram.event.RuleParams;
import org.springframework.stereotype.Service;

@Service
public class HiddenNodeListener implements IHiddenEventListener {


    @Override
    public DiagramRuleEnum getDiagramRule() {
        return DiagramRuleEnum.NODE_DIAGRAM;
    }

    @Override
    public void process(RuleParams params) {

    }
}
