{"class": "GraphLinksModel", "name": "流程体系", "linkFromPortIdProperty": "fromPort", "linkToPortIdProperty": "to<PERSON><PERSON>", "nodeDataArray": [{"image": "http://**************/rsm/122/eaInitImage/cd94a9bc-92e9-4fec-a370-4b03f9a828c5.png", "classCode": "活动", "loc": "-1702.000903968918 6.**************5", "clippingHeight": 24, "visible": true, "shape": "BPMN|活动", "clippingWidth": 56, "label": "活动", "anchors": [], "clippingStrokeWidth": 0, "classId": 6062728188450160, "isClipping": true, "size": "56 24", "isSubClass": false, "clippingFill": "transparent", "width": 56, "category": "image", "clippingStroke": "rgb(191, 191, 191)", "clippingFigure": "RoundedRectangle", "key": -3, "height": 24}, {"image": "http://**************/rsm/122/eaInitImage/d20fd182-bfe1-4f81-83c1-8ba4d8f8b982.png", "classCode": "岗位角色", "loc": "-1708.5700192826512 154.11568252744632", "clippingHeight": 56, "visible": true, "shape": "Uml|ActorPic", "clippingWidth": 56, "label": "角色", "anchors": [], "clippingStrokeWidth": 0, "classId": 6062728188450129, "isClipping": true, "size": "56 56", "isSubClass": false, "clippingFill": "transparent", "width": 56, "category": "image", "clippingStroke": "rgb(191, 191, 191)", "clippingFigure": "RoundedRectangle", "key": -4, "height": 56}, {"strokeWidth": 2, "loc": "-1624.9418549027819 -46.12367276819799", "figure": "Rectangle", "strokeDashArray": [10, 6], "clippingHeight": 775, "clippingWidth": 631, "textOriginalHeight": 60, "zOrder": 0, "label": "", "fill": "#fff", "stroke": "#000", "clippingStrokeWidth": 0, "isClipping": true, "size": "631 775", "clippingFill": "transparent", "width": 631, "category": "text", "clippingStroke": "rgb(191, 191, 191)", "key": -7, "clippingFigure": "RoundedRectangle", "height": 775}, {"strokeWidth": 2, "loc": "-1114.1077274550837 -50.056827604503326", "figure": "Rectangle", "strokeDashArray": [10, 6], "clippingHeight": 777, "clippingWidth": 260, "textOriginalHeight": 60, "zOrder": 0, "label": "", "fill": "#fff", "stroke": "#000", "clippingStrokeWidth": 0, "isClipping": true, "size": "260 777", "clippingFill": "transparent", "width": 260, "category": "text", "clippingStroke": "rgb(191, 191, 191)", "key": -8, "clippingFigure": "RoundedRectangle", "height": 777}, {"strokeWidth": 2, "loc": "-1113.1194864703039 -418.7544726965333", "figure": "Rectangle", "clippingHeight": 60, "clippingWidth": 240, "textOriginalHeight": 60, "zOrder": 0, "label": "数据架构", "fill": "transparent", "stroke": "transparent", "clippingStrokeWidth": 0, "isClipping": true, "size": "240 60", "clippingFill": "transparent", "width": 240, "category": "text", "clippingStroke": "rgb(191, 191, 191)", "key": -9, "fontColor": "#4e81bb", "clippingFigure": "RoundedRectangle", "height": 60}, {"image": "http://**************/rsm/122/eaInitImage/72642a99-5c8b-4490-8267-89a2ecf1420c.svg", "classCode": "LogicEntity", "loc": "-1107.6474654599124 50.4137742904432", "clippingHeight": 56, "visible": true, "shape": "实体ER图|实体", "clippingWidth": 56, "label": "S-F.B.逻辑实体", "anchors": [], "clippingStrokeWidth": 0, "classId": 6062728188450396, "isClipping": true, "size": "56 56", "isSubClass": false, "clippingFill": "transparent", "width": 56, "category": "image", "clippingStroke": "rgb(191, 191, 191)", "clippingFigure": "RoundedRectangle", "key": -25, "height": 56}, {"image": "http://**************/rsm/122/eaInitImage/72642a99-5c8b-4490-8267-89a2ecf1420c.svg", "classCode": "PhysicalEntity", "loc": "-1107.6474654599124 206.8201021487688", "clippingHeight": 56, "visible": true, "shape": "实体ER图|实体", "clippingWidth": 56, "label": "S-F.B.物理实体", "anchors": [], "clippingStrokeWidth": 0, "classId": 6062728188450389, "isClipping": true, "size": "56 56", "isSubClass": false, "clippingFill": "transparent", "width": 56, "category": "image", "clippingStroke": "rgb(191, 191, 191)", "clippingFigure": "RoundedRectangle", "key": -27, "height": 56}, {"strokeWidth": 2, "loc": "-492.63649693848936 -55.85478733774596", "figure": "Rectangle", "strokeDashArray": [10, 6], "clippingHeight": 776, "clippingWidth": 261, "textOriginalHeight": 60, "zOrder": 0, "label": "", "fill": "#fff", "stroke": "#000", "clippingStrokeWidth": 0, "isClipping": true, "size": "261 776", "clippingFill": "transparent", "width": 261, "category": "text", "clippingStroke": "rgb(191, 191, 191)", "key": -32, "clippingFigure": "RoundedRectangle", "height": 776}, {"strokeWidth": 2, "loc": "-495.5223759858236 -415.0105090348785", "figure": "Rectangle", "clippingHeight": 60, "clippingWidth": 240, "textOriginalHeight": 60, "zOrder": 0, "label": "技术架构", "fill": "transparent", "stroke": "transparent", "clippingStrokeWidth": 0, "isClipping": true, "size": "240 60", "clippingFill": "transparent", "width": 240, "category": "text", "clippingStroke": "rgb(191, 191, 191)", "key": -34, "fontColor": "#4e81bb", "clippingFigure": "RoundedRectangle", "height": 60}, {"image": "http://**************/rsm/122/eaInitImage/12241ae9-02d6-42d8-bb8b-e5e07388d0ad.png", "classCode": "KCP", "loc": "-1876.3318947884916 49.27106094997367", "clippingHeight": 29.89830508474576, "visible": true, "clippingWidth": 56, "label": "KCP", "anchors": [], "clippingStrokeWidth": 0, "classId": 6062728188450144, "isClipping": true, "size": "56 29.89830508474576", "isSubClass": false, "clippingFill": "transparent", "width": 56, "category": "image", "isGroup": false, "clippingStroke": "rgb(191, 191, 191)", "clippingFigure": "RoundedRectangle", "key": -2, "height": 29.89830508474576}, {"image": "http://**************/rsm/122/eaInitImage/75096e48-e345-4d17-a7f8-b2a7a3db3f71.png", "classCode": "部门", "loc": "-1582.1409239566995 219.51323894055014", "clippingHeight": 56, "visible": true, "clippingWidth": 56, "label": "部门", "anchors": [], "clippingStrokeWidth": 0, "classId": 6062728188450201, "isClipping": true, "size": "56 56", "isSubClass": false, "clippingFill": "transparent", "width": 56, "category": "image", "isGroup": false, "clippingStroke": "rgb(191, 191, 191)", "clippingFigure": "RoundedRectangle", "key": -18, "height": 56}, {"image": "http://**************/rsm/122/eaInitImage/9daa245a-74b4-4d81-9854-81cf9ff72988.png", "classCode": "风险", "loc": "-1879.0027608739526 176.13374227091185", "clippingHeight": 28.54901960784314, "visible": true, "clippingWidth": 56, "label": "风险", "anchors": [], "clippingStrokeWidth": 0, "classId": 6062728188450119, "isClipping": true, "size": "56 28.54901960784314", "isSubClass": false, "clippingFill": "transparent", "width": 56, "category": "image", "isGroup": false, "clippingStroke": "rgb(191, 191, 191)", "clippingFigure": "RoundedRectangle", "key": -17, "height": 28.54901960784314}, {"image": "http://**************/rsm/122/eaInitImage/56d0d601-be52-4bc2-99a1-cca6a1e03c59.png", "classCode": "标准大类", "loc": "-1884.6784124955739 -149.96768959233796", "clippingHeight": 29.81818181818182, "visible": true, "clippingWidth": 56, "label": "标准大类", "anchors": [], "clippingStrokeWidth": 0, "classId": 6062728188450196, "isClipping": true, "size": "56 29.81818181818182", "isSubClass": false, "clippingFill": "transparent", "width": 56, "category": "image", "isGroup": false, "clippingStroke": "rgb(191, 191, 191)", "clippingFigure": "RoundedRectangle", "key": -15, "height": 29.81818181818182}, {"image": "http://**************/rsm/122/eaInitImage/ff9dff27-8b4b-4273-b313-77113d77ced0.png", "classCode": "术语", "loc": "-1884.415218594137 -216.1869626938672", "clippingHeight": 28.54901960784314, "visible": true, "clippingWidth": 56, "label": "术语", "anchors": [], "clippingStrokeWidth": 0, "classId": 6062728188450126, "isClipping": true, "size": "56 28.54901960784314", "isSubClass": false, "clippingFill": "transparent", "width": 56, "category": "image", "isGroup": false, "clippingStroke": "rgb(191, 191, 191)", "clippingFigure": "RoundedRectangle", "key": -13, "height": 28.54901960784314}, {"image": "http://**************/rsm/122/eaInitImage/f989c9b6-4c78-449e-bccd-ddf24480e95f.png", "classCode": "指标", "loc": "-1884.8788769926275 -264.7912891699793", "clippingHeight": 16.59259259259259, "visible": true, "clippingWidth": 56, "label": "指标", "anchors": [], "clippingStrokeWidth": 0, "classId": 6062728188450181, "isClipping": true, "size": "56 16.59259259259259", "isSubClass": false, "clippingFill": "transparent", "width": 56, "category": "image", "isGroup": false, "clippingStroke": "rgb(191, 191, 191)", "clippingFigure": "RoundedRectangle", "key": -41, "height": 16.59259259259259}, {"image": "http://**************/rsm/122/eaInitImage/ea068bf9-a741-4ba3-bf1e-b64f9e94c1b5.png", "classCode": "标准", "loc": "-1883.6873833769423 -70.85295991490452", "clippingHeight": 25.91891891891892, "visible": true, "clippingWidth": 56, "label": "标准", "anchors": [], "clippingStrokeWidth": 0, "classId": 6062728188450110, "isClipping": true, "size": "56 25.91891891891892", "isSubClass": false, "clippingFill": "transparent", "width": 56, "category": "image", "isGroup": false, "clippingStroke": "rgb(191, 191, 191)", "clippingFigure": "RoundedRectangle", "key": -16, "height": 25.91891891891892}, {"loc": "-1162.3558155138471 -72.25115946945826", "icon": "static/images/yiqi/thumbnail/yewuduixiang.svg", "zOrder": 10, "anchors": [], "shapeName": "业务对象", "clippingStrokeWidth": 0, "classId": 6062728188450142, "archiShape": "Archimate-Expand|业务对象", "isSubClass": false, "clippingFill": "transparent", "clippingStroke": "rgb(191, 191, 191)", "key": -21, "height": 55, "classCode": "业务对象", "image": "http://**************/rsm/122/eaInitImage/e0349285-4ac9-4a79-8d28-47a250650706.png", "figure": "Rectangle", "clippingHeight": 55, "visible": true, "clippingWidth": 104, "label": "业务对象", "fill": "#A5f998", "stroke": "rgb(163, 163, 163)", "isClipping": true, "size": "104 55", "width": 104, "category": "archi-shape", "isGroup": true, "clippingFigure": "RoundedRectangle"}, {"image": "http://**************/rsm/122/eaInitImage/ed32ae25-68c3-42f3-8da3-95b5c048aba4.png", "classCode": "制度", "loc": "-1878.415787033181 -18.21842812728734", "clippingHeight": 25.960264900662253, "visible": true, "clippingWidth": 56, "label": "制度", "anchors": [], "clippingStrokeWidth": 0, "classId": 6062728188450096, "isClipping": true, "size": "56 25.960264900662253", "isSubClass": false, "clippingFill": "transparent", "width": 56, "category": "image", "isGroup": false, "clippingStroke": "rgb(191, 191, 191)", "clippingFigure": "RoundedRectangle", "key": -14, "height": 25.960264900662253}, {"image": "http://**************/rsm/122/eaInitImage/ed32ae25-68c3-42f3-8da3-95b5c048aba4.png", "classCode": "岗位", "loc": "-1711.3273005922192 256.070454950064", "clippingHeight": 25.91891891891892, "visible": true, "clippingWidth": 56, "label": "岗位", "anchors": [], "clippingStrokeWidth": 0, "classId": 6062728188450205, "isClipping": true, "size": "56 25.91891891891892", "isSubClass": false, "clippingFill": "transparent", "width": 56, "category": "image", "isGroup": false, "clippingStroke": "rgb(191, 191, 191)", "clippingFigure": "RoundedRectangle", "key": -19, "height": 25.91891891891892}, {"image": "http://**************/rsm/122/eaInitImage/ea068bf9-a741-4ba3-bf1e-b64f9e94c1b5.png", "classCode": "表单", "loc": "-1414.605379360719 -48.25980873077778", "clippingHeight": 26.56528925619835, "visible": true, "clippingWidth": 56, "label": "表单", "anchors": [], "clippingStrokeWidth": 0, "classId": 6062728188450150, "isClipping": true, "size": "56 26.56528925619835", "isSubClass": false, "clippingFill": "transparent", "width": 56, "category": "image", "isGroup": false, "clippingStroke": "rgb(191, 191, 191)", "clippingFigure": "RoundedRectangle", "key": -20, "height": 26.56528925619835}, {"image": "http://**************/rsm/122/eaInitImage/c8ec404f-d824-48ff-ace8-a38d833dea9c.png", "classCode": "流程组", "loc": "-1701.************* -273.0428435360724", "clippingHeight": 21.853658536585368, "visible": true, "shape": "BPMN|价值流", "clippingWidth": 56, "label": "流程组", "anchors": [], "clippingStrokeWidth": 0, "classId": 6062728188450239, "isClipping": true, "size": "56 21.853658536585368", "isSubClass": false, "clippingFill": "transparent", "width": 56, "category": "image", "clippingStroke": "rgb(191, 191, 191)", "clippingFigure": "RoundedRectangle", "key": -48, "height": 21.853658536585368}, {"image": "http://**************/rsm/122/eaInitImage/0b4b8f1b-e3fa-40ec-ad67-2166d381db50.png", "classCode": "流程", "loc": "-1699.************* -145.25827464926545", "clippingHeight": 21.747572815533978, "visible": true, "shape": "BPMN|流程", "clippingWidth": 56, "label": "流程", "anchors": [], "clippingStrokeWidth": 0, "classId": 6062728188450213, "isClipping": true, "size": "56 21.747572815533978", "isSubClass": false, "clippingFill": "transparent", "width": 56, "category": "image", "clippingStroke": "rgb(191, 191, 191)", "clippingFigure": "RoundedRectangle", "key": -49, "height": 21.747572815533978}, {"image": "http://**************/rsm/122/eaInitImage/96f15d58-c2a3-44a8-9680-4a40f8ed1052.png", "classCode": "开始事件", "loc": "-1483.5227453832017 76.3938995177769", "clippingHeight": 56, "visible": true, "shape": "流程架构|开始事件", "clippingWidth": 56, "label": "开始事件", "anchors": [], "clippingStrokeWidth": 0, "classId": 6062728188450259, "isClipping": true, "size": "56 56", "isSubClass": false, "clippingFill": "transparent", "width": 56, "category": "image", "clippingStroke": "rgb(191, 191, 191)", "clippingFigure": "RoundedRectangle", "key": -50, "height": 56}, {"image": "http://**************/rsm/122/eaInitImage/f5ac3b7c-b667-4ec0-a195-f23160898021.png", "classCode": "结束事件", "loc": "-1479.571764053289 277.7754634741615", "clippingHeight": 56, "visible": true, "shape": "流程架构|结束事件", "clippingWidth": 56, "label": "结束事件", "anchors": [], "clippingStrokeWidth": 0, "classId": 6062728188450262, "isClipping": true, "size": "56 56", "isSubClass": false, "clippingFill": "transparent", "width": 56, "category": "image", "clippingStroke": "rgb(191, 191, 191)", "clippingFigure": "RoundedRectangle", "key": -51, "height": 56}, {"image": "http://**************/rsm/122/eaInitImage/5b28482c-7fa0-44b5-b8f2-e6d444c74c88.png", "classCode": "中间事件", "loc": "-1483.4316039453715 178.40163514456117", "clippingHeight": 56, "visible": true, "shape": "流程架构|中间事件", "clippingWidth": 56, "label": "中间事件", "anchors": [], "clippingStrokeWidth": 0, "classId": 6062728188450265, "isClipping": true, "size": "56 56", "isSubClass": false, "clippingFill": "transparent", "width": 56, "category": "image", "clippingStroke": "rgb(191, 191, 191)", "clippingFigure": "RoundedRectangle", "key": -52, "height": 56}, {"image": "http://**************/rsm/122/eaInitImage/550bdc90-0f40-4ead-99f2-8a464ce576bf.png", "classCode": "包容网关", "loc": "-1357.7583315745826 181.6002831756989", "clippingHeight": 56, "visible": true, "shape": "流程架构|包容网关", "clippingWidth": 56, "label": "包容网关", "anchors": [], "clippingStrokeWidth": 0, "classId": 6062728188450271, "isClipping": true, "size": "56 56", "isSubClass": false, "clippingFill": "transparent", "width": 56, "category": "image", "clippingStroke": "rgb(191, 191, 191)", "clippingFigure": "RoundedRectangle", "key": -53, "height": 56}, {"image": "http://**************/rsm/122/eaInitImage/f9b882a6-6e3c-44bd-be92-cd7e70b74402.png", "classCode": "并行网关", "loc": "-1357.3475874404749 279.0727910389046", "clippingHeight": 56, "visible": true, "shape": "流程架构|并行网关", "clippingWidth": 56, "label": "并行网关", "anchors": [], "clippingStrokeWidth": 0, "classId": 6062728188450274, "isClipping": true, "size": "56 56", "isSubClass": false, "clippingFill": "transparent", "width": 56, "category": "image", "clippingStroke": "rgb(191, 191, 191)", "clippingFigure": "RoundedRectangle", "key": -54, "height": 56}, {"image": "http://**************/rsm/122/eaInitImage/1791dee0-63ff-47c4-a654-bc76caa55383.png", "classCode": "排他网关", "loc": "-1356.7481658194802 81.26784689239423", "clippingHeight": 56, "visible": true, "shape": "流程架构|排他网关", "clippingWidth": 56, "label": "排他网关", "anchors": [], "clippingStrokeWidth": 0, "classId": 6062728188450268, "isClipping": true, "size": "56 56", "isSubClass": false, "clippingFill": "transparent", "width": 56, "category": "image", "clippingStroke": "rgb(191, 191, 191)", "clippingFigure": "RoundedRectangle", "key": -55, "height": 56}, {"image": "http://**************/rsm/122/eaInitImage/c90e6e58-b5d3-48ac-9260-11f0eb02314b.png", "classCode": "端到端流程", "loc": "-1700.************* -387.8445159413126", "clippingHeight": 30.464000000000002, "visible": true, "shape": "BPMN|规则类型", "clippingWidth": 56, "label": "端到端流程", "anchors": [], "clippingStrokeWidth": 0, "classId": 6062728188450280, "isClipping": true, "size": "56 30.464000000000002", "isSubClass": false, "clippingFill": "transparent", "width": 56, "category": "image", "clippingStroke": "rgb(191, 191, 191)", "clippingFigure": "RoundedRectangle", "key": -57, "height": 30.464000000000002}, {"strokeWidth": 2, "loc": "-1684.4051777857048 -416.7806120197034", "figure": "Rectangle", "clippingHeight": 60, "clippingWidth": 240, "textOriginalHeight": 60, "zOrder": 0, "label": "业务架构", "fill": "transparent", "stroke": "transparent", "clippingStrokeWidth": 0, "isClipping": true, "size": "240 60", "clippingFill": "transparent", "width": 240, "category": "text", "clippingStroke": "rgb(191, 191, 191)", "key": -61, "fontColor": "#4e81bb", "clippingFigure": "RoundedRectangle", "height": 60}, {"strokeWidth": 2, "loc": "-804.4139315945333 -53.42106738051427", "figure": "Rectangle", "strokeDashArray": [10, 6], "clippingHeight": 776, "clippingWidth": 261, "textOriginalHeight": 60, "zOrder": 0, "label": "", "fill": "#fff", "stroke": "#000", "clippingStrokeWidth": 0, "isClipping": true, "size": "261 776", "clippingFill": "transparent", "width": 261, "category": "text", "clippingStroke": "rgb(191, 191, 191)", "key": -60, "clippingFigure": "RoundedRectangle", "height": 776}, {"strokeWidth": 2, "loc": "-805.4912437566951 -423.84881196320276", "figure": "Rectangle", "clippingHeight": 60, "clippingWidth": 240, "textOriginalHeight": 60, "zOrder": 0, "label": "数据架构", "fill": "transparent", "stroke": "transparent", "clippingStrokeWidth": 0, "isClipping": true, "size": "240 60", "clippingFill": "transparent", "width": 240, "category": "text", "clippingStroke": "rgb(191, 191, 191)", "key": -65, "fontColor": "#4e81bb", "clippingFigure": "RoundedRectangle", "height": 60}, {"loc": "-1864.9485617259427 -318.510093778079", "icon": "static/images/yiqi/thumbnail/jiazhiliujieduan.svg", "zOrder": 10, "shapeName": "价值流阶段", "clippingStrokeWidth": 0, "classId": 6062728188450288, "archiShape": "Archimate-Expand|价值流阶段", "isSubClass": false, "clippingFill": "transparent", "clippingStroke": "rgb(191, 191, 191)", "key": -59, "height": 28, "classCode": "价值流阶段", "image": "http://**************/rsm/122/eaInitImage/3a3130ed-0ceb-436c-a1eb-639702b95694.png", "figure": "RoundedRectangle", "clippingHeight": 28, "visible": true, "shape": "Archimate-Expand|价值流阶段", "clippingWidth": 70, "label": "价值流阶段", "fill": "rgb(241, 215, 154)", "stroke": "rgb(163, 163, 163)", "isClipping": true, "size": "70 28", "width": 70, "category": "archi-shape", "isGroup": true, "clippingFigure": "RoundedRectangle"}, {"loc": "-1869.6569962274227 -401.81154183744735", "icon": "static/images/yiqi/thumbnail/jiazhiliu.svg", "zOrder": 10, "shapeName": "价值流", "clippingStrokeWidth": 0, "classId": 6062728188450285, "archiShape": "Archimate-Expand|价值流", "isSubClass": false, "clippingFill": "transparent", "clippingStroke": "rgb(191, 191, 191)", "key": -58, "height": 36, "classCode": "价值流", "image": "http://**************/rsm/122/eaInitImage/f7b4afb9-bbe1-4cf9-a6c3-c5ea467f8e10.png", "figure": "RoundedRectangle", "clippingHeight": 36, "visible": true, "shape": "Archimate-Expand|价值流", "clippingWidth": 77, "label": "价值流", "fill": "rgb(241, 215, 154)", "stroke": "rgb(163, 163, 163)", "isClipping": true, "size": "77 36", "width": 77, "category": "archi-shape", "isGroup": true, "clippingFigure": "RoundedRectangle"}, {"image": "http://**************/rsm/122/eaInitImage/deeea7dc-2f44-4d32-b413-b078c96775fd.png", "classCode": "前置流程", "loc": "-1504.1412204011199 -382.7775878581644", "clippingHeight": 20, "visible": true, "shape": "流程架构|前置流程", "clippingWidth": 56, "label": "前置流程", "anchors": [], "clippingStrokeWidth": 0, "classId": 6062728188450292, "isClipping": true, "size": "56 20", "isSubClass": false, "clippingFill": "transparent", "width": 56, "category": "image", "clippingStroke": "rgb(191, 191, 191)", "clippingFigure": "RoundedRectangle", "key": -66, "height": 20}, {"image": "http://**************/rsm/122/eaInitImage/801b6114-7d69-4a6e-beda-758a582a1860.png", "classCode": "后置流程", "loc": "-1499.5025769935544 -293.62982351975995", "clippingHeight": 20, "visible": true, "shape": "流程架构|后置流程", "clippingWidth": 56, "label": "后置流程", "anchors": [], "clippingStrokeWidth": 0, "classId": 6062728188450299, "isClipping": true, "size": "56 20", "isSubClass": false, "clippingFill": "transparent", "width": 56, "category": "image", "clippingStroke": "rgb(191, 191, 191)", "clippingFigure": "RoundedRectangle", "key": -67, "height": 20}, {"image": "http://**************/rsm/122/eaInitImage/56d0d601-be52-4bc2-99a1-cca6a1e03c59.png", "classCode": "要素", "loc": "-1813.0209705584343 248.24839558995183", "clippingHeight": 47.56164383561644, "visible": true, "clippingWidth": 56, "label": "要素", "anchors": [], "clippingStrokeWidth": 0, "classId": 6062728188450314, "isClipping": true, "size": "56 47.56164383561644", "isSubClass": false, "clippingFill": "transparent", "width": 56, "category": "image", "clippingStroke": "rgb(191, 191, 191)", "clippingFigure": "RoundedRectangle", "key": -69, "height": 47.56164383561644}, {"loc": "-1442.57341381722 -153.36762861362593", "icon": "static/images/yiqi/thumbnail/jiazhiliuchangjing.svg", "zOrder": 10, "shapeName": "价值流场景", "clippingStrokeWidth": 0, "classId": 6062728188450277, "archiShape": "Archimate-Expand|价值流场景", "isSubClass": false, "clippingFill": "transparent", "clippingStroke": "rgb(191, 191, 191)", "key": -56, "height": 35, "classCode": "场景", "image": "http://**************/rsm/122/eaInitImage/84a6b316-cdc8-4a04-8538-7d311b5908b7.png", "figure": "RoundedRectangle", "clippingHeight": 35, "visible": true, "shape": "Archimate-Expand|价值流场景", "clippingWidth": 70, "label": "场景", "fill": "rgb(241, 215, 154)", "stroke": "rgb(163, 163, 163)", "isClipping": true, "size": "70 35", "width": 70, "category": "archi-shape", "isGroup": true, "clippingFigure": "RoundedRectangle"}, {"loc": "-1158.6474654599124 -361.5276107127561", "icon": "static/images/yiqi/thumbnail/zhutiyufenzu.svg", "zOrder": 10, "shapeName": "主题域分组", "clippingStrokeWidth": 0, "isSubClass": false, "clippingFill": "transparent", "clippingStroke": "rgb(191, 191, 191)", "key": -47, "height": 52, "image": "http://**************/rsm/122/eaInitImage/4218fbdb-a37c-4b75-bd00-721e1bd7d200.png", "classCode": "主题域分组", "figure": "Rectangle", "clippingHeight": 52, "visible": true, "shape": "Archimate-Expand|主题域分组", "clippingWidth": 101, "label": "主题域分组", "fill": "#A5f998", "stroke": "rgb(163, 163, 163)", "isClipping": true, "size": "101 52", "width": 101, "category": "archi-shape", "isGroup": true, "clippingFigure": "RoundedRectangle"}, {"loc": "-1160.1474654599124 -237.8804823783563", "icon": "static/images/yiqi/thumbnail/zhutiyu.svg", "zOrder": 10, "shapeName": "主题域", "clippingStrokeWidth": 0, "classId": 6062728188450140, "isSubClass": false, "clippingFill": "transparent", "clippingStroke": "rgb(191, 191, 191)", "key": -70, "height": 55, "image": "http://**************/rsm/122/eaInitImage/9b6ccb85-2b5a-4978-be6b-bcbdabdf0ad1.png", "classCode": "主题域", "figure": "Rectangle", "clippingHeight": 55, "visible": true, "shape": "Archimate-Expand|主题域", "clippingWidth": 104, "label": "主题域", "fill": "#A5f998", "stroke": "rgb(163, 163, 163)", "isClipping": true, "size": "104 55", "width": 104, "category": "archi-shape", "isGroup": true, "clippingFigure": "RoundedRectangle"}, {"loc": "-858.9701808721144 -228.40240730027784", "icon": "static/images/yiqi/thumbnail/yingyongxitong.svg", "zOrder": 10, "shapeName": "应用系统", "clippingStrokeWidth": 0, "classId": 6062728188450136, "isSubClass": false, "clippingFill": "transparent", "clippingStroke": "rgb(191, 191, 191)", "key": -46, "height": 49, "image": "http://**************/rsm/122/eaInitImage/69f6ce52-1efb-48a9-b0b3-6d53982d3bb0.png", "classCode": "应用系统", "figure": "Rectangle", "clippingHeight": 49, "visible": true, "shape": "Archimate-Expand|应用系统", "clippingWidth": 115, "label": "应用系统", "fill": "rgb(168, 255, 254)", "stroke": "rgb(163, 163, 163)", "isClipping": true, "size": "115 49", "width": 115, "category": "archi-shape", "isGroup": true, "clippingFigure": "RoundedRectangle"}, {"loc": "-854.4701808721144 -73.00137622406598", "icon": "static/images/yiqi/thumbnail/yingyongzujian.svg", "zOrder": 10, "shapeName": "应用组件", "clippingStrokeWidth": 0, "classId": 6062728188450068, "isSubClass": false, "clippingFill": "transparent", "clippingStroke": "rgb(191, 191, 191)", "key": -62, "height": 49, "image": "http://**************/rsm/122/eaInitImage/c9f61409-a562-4bfa-9245-60d579086349.png", "classCode": "应用组件", "figure": "Rectangle", "clippingHeight": 49, "visible": true, "shape": "Archimate-Expand|应用组件", "clippingWidth": 106, "label": "应用组件", "fill": "rgb(168, 255, 254)", "stroke": "rgb(163, 163, 163)", "isClipping": true, "size": "106 49", "width": 106, "category": "archi-shape", "isGroup": true, "clippingFigure": "RoundedRectangle"}, {"loc": "-851.9876046256107 52.39384039350534", "icon": "/static/images/taibao/thumbnail/application-function.svg", "zOrder": 10, "clippingStrokeWidth": 0, "classId": 6062728188450138, "isSubClass": false, "clippingFill": "transparent", "clippingStroke": "rgb(191, 191, 191)", "key": -63, "height": 47, "image": "http://**************/rsm/122/eaInitImage/8dc94f55-5fbe-4376-960d-c84016a761de.png", "classCode": "应用服务", "figure": "RoundedRectangle", "clippingHeight": 47, "visible": true, "shape": "Archimate-Expand2|功能", "clippingWidth": 108, "label": "应用服务", "fill": "#A8FFFE", "isClipping": true, "size": "108 47", "width": 108, "category": "archi-shape", "isGroup": true, "clippingFigure": "RoundedRectangle"}, {"loc": "-561.5413184166714 -371.186429938389", "icon": "/static/images/taibao/thumbnail/node.svg", "zOrder": 10, "clippingStrokeWidth": 0, "classId": 6062728188450070, "isSubClass": false, "clippingFill": "transparent", "clippingStroke": "rgb(191, 191, 191)", "key": -64, "height": 60, "image": "http://**************/rsm/122/eaInitImage/83e994c8-398f-43a9-97d4-013151747015.png", "classCode": "机房", "figure": "Rectangle", "clippingHeight": 60, "visible": true, "shape": "Archimate-Expand2|节点", "clippingWidth": 120, "label": "机房", "fill": "#daffd9", "isClipping": true, "size": "120 60", "width": 120, "category": "archi-shape", "isGroup": true, "clippingFigure": "RoundedRectangle"}, {"loc": "-556.0413184166714 -230.03963989648122", "icon": "/static/images/taibao/thumbnail/communication-network.svg", "zOrder": 10, "clippingStrokeWidth": 0, "classId": 6062728188450072, "isSubClass": false, "clippingFill": "transparent", "clippingStroke": "rgb(191, 191, 191)", "key": -68, "height": 53, "image": "http://**************/rsm/122/eaInitImage/1c60fc70-bd81-464d-a46d-e366df82f517.png", "classCode": "网络分区", "figure": "Rectangle", "clippingHeight": 53, "visible": true, "shape": "Archimate-Expand2|网络", "clippingWidth": 109, "label": "网络分区", "fill": "#daffd9", "isClipping": true, "size": "109 53", "width": 109, "category": "archi-shape", "isGroup": true, "clippingFigure": "RoundedRectangle"}, {"loc": "-558.5413184166714 -96.39284985457346", "icon": "/static/images/taibao/thumbnail/device.svg", "zOrder": 10, "clippingStrokeWidth": 0, "classId": 6062728188450074, "isSubClass": false, "clippingFill": "transparent", "clippingStroke": "rgb(191, 191, 191)", "key": -71, "height": 55, "image": "http://**************/rsm/122/eaInitImage/7ae3091c-6bfb-4345-bfbd-4a6f3f2ae62e.png", "classCode": "设备节点", "figure": "Archi-Node", "clippingHeight": 55, "visible": true, "shape": "Archimate-Expand2|设备", "clippingWidth": 114, "label": "设备节点", "fill": "#daffd9", "isClipping": true, "size": "114 55", "width": 114, "category": "archi-shape", "isGroup": true, "clippingFigure": "RoundedRectangle"}, {"loc": "-557.5413184166714 39.25394018733431", "icon": "/static/images/taibao/thumbnail/hu.svg", "zOrder": 10, "clippingStrokeWidth": 0, "classId": 6062728188450076, "isSubClass": false, "clippingFill": "transparent", "clippingStroke": "rgb(191, 191, 191)", "key": -72, "height": 50, "image": "http://**************/rsm/122/eaInitImage/874891f4-1943-4af0-baa7-55910730efcb.png", "classCode": "部署单元", "figure": "Rectangle", "clippingHeight": 50, "visible": true, "shape": "Archimate-Expand2|部署单元", "clippingWidth": 112, "label": "部署单元", "fill": "#daffd9", "isClipping": true, "size": "112 50", "width": 112, "category": "archi-shape", "isGroup": true, "clippingFigure": "RoundedRectangle"}, {"loc": "-554.0413184166714 169.40073022924207", "icon": "/static/images/taibao/thumbnail/system-software.svg", "zOrder": 10, "clippingStrokeWidth": 0, "classId": 6062728188450087, "isSubClass": false, "clippingFill": "transparent", "clippingStroke": "rgb(191, 191, 191)", "key": -73, "height": 52, "image": "http://**************/rsm/122/eaInitImage/29068a20-d124-4282-9998-27c31ac4b683.png", "classCode": "系统软件", "figure": "Rectangle", "clippingHeight": 52, "visible": true, "shape": "Archimate-Expand2|软件", "clippingWidth": 105, "label": "系统软件", "fill": "#daffd9", "isClipping": true, "size": "105 52", "width": 105, "category": "archi-shape", "isGroup": true, "clippingFigure": "RoundedRectangle"}, {"loc": "-854.3893585625041 -355.77806009892714", "icon": "static/images/yiqi/thumbnail/yingyongyu.svg", "zOrder": 10, "shapeName": "应用域", "clippingStrokeWidth": 0, "classId": 6065233540250529, "isSubClass": false, "clippingFill": "transparent", "clippingStroke": "rgb(191, 191, 191)", "key": -74, "height": 51, "image": "http://**************/rsm/122/eaInitImage/e4558e21-349e-4e2f-a881-3e777a9dae35.png", "classCode": "应用域", "figure": "Rectangle", "clippingHeight": 51, "visible": true, "shape": "Archimate-Expand|应用域", "clippingWidth": 111, "label": "应用域", "fill": "rgb(168, 255, 254)", "stroke": "rgb(163, 163, 163)", "isClipping": true, "size": "111 51", "width": 111, "category": "archi-shape", "isGroup": true, "clippingFigure": "RoundedRectangle"}], "linkDataArray": [{"strokeWidth": 1, "classCode": "由...执行", "strokeDashArray": [], "toArrowOutline": false, "fromPort": "", "toPort": "", "toArrow": "Standard", "label": "由...执行", "stroke": "#000", "points": [-1702.5344847866552, 18.**************5, -1757.5494518741182, 69.90767355565349, -1707.3249973745978, 126.11568252744632], "classId": 6062728188450048, "from": -3, "to": -4}, {"strokeWidth": 1, "classCode": "衍生", "strokeDashArray": [], "toArrowOutline": false, "fromPort": "", "toPort": "", "toArrow": "Standard", "label": "衍生", "stroke": "#000", "points": [-1107.6474654599124, 78.4137742904432, -1107.6474654599124, 178.8201021487688], "classId": 6062728188450041, "from": -25, "to": -27}, {"strokeWidth": 1, "classCode": "KCP-包含", "strokeDashArray": [], "toArrowOutline": false, "fromPort": "", "toPort": "", "toArrow": "Standard", "label": "KCP-包含", "stroke": "#000", "points": [-1848.3318947884916, 42.38201783068404, -1730.000903968918, 13.268185755530176], "classId": 6062728188450040, "from": -2, "to": -3}, {"strokeWidth": 1, "classCode": "关联", "strokeDashArray": [], "toArrowOutline": false, "fromPort": "", "toPort": "", "toArrow": "Standard", "label": "关联", "stroke": "#000", "points": [-1875.*************, 63.**************, -1876.*************, 112.**************, -1877.*************, 161.**************], "classId": ****************, "from": -2, "to": -17}, {"strokeWidth": 1, "classCode": "关联", "strokeDashArray": [], "toArrowOutline": false, "fromPort": "", "toPort": "", "toArrow": "Standard", "label": "关联", "stroke": "#000", "points": [-1727.4111664975317, -4.422365587462437, -1857.3058727790722, -59.63858921111043], "classId": ****************, "from": -3, "to": -16}, {"strokeWidth": 1, "classCode": "包含", "strokeDashArray": [], "toArrowOutline": false, "fromPort": "", "toPort": "", "toArrow": "Standard", "label": "包含", "stroke": "#000", "points": [-1884.4916540590164, -135.05859868324706, -1883.8497197931624, -83.81241937436397], "classId": ****************, "from": -15, "to": -16}, {"strokeWidth": 1, "classCode": "包含", "strokeDashArray": [], "toArrowOutline": false, "fromPort": "", "toPort": "", "toArrow": "Standard", "label": "包含", "stroke": "#000", "points": [-1109.2026295974338, -16.25115946945826, -1108.3006513763257, 22.4137742904432], "classId": ****************, "from": -21, "to": -25}, {"strokeWidth": 1, "classCode": "关联", "strokeDashArray": [], "toArrowOutline": false, "fromPort": "", "toPort": "", "toArrow": "Standard", "label": "关联", "stroke": "#000", "points": [-1730.000903968918, 2.475095713351987, -1850.415787033181, -14.314381204398781], "classId": ****************, "from": -3, "to": -14}, {"strokeWidth": 1, "classCode": "拥有", "strokeDashArray": [], "toArrowOutline": false, "fromEndSegmentLength": 60, "fromPort": "", "toEndSegmentLength": 60, "toPort": "", "toArrow": "Standard", "label": "拥有", "stroke": "#000", "points": [-1710, 243, -1709.5, 212.5, -1709, 182], "classId": 6062728188450047, "from": -19, "to": -4}, {"strokeWidth": 1, "classCode": "包含", "strokeDashArray": [], "toArrowOutline": false, "fromPort": "", "toPort": "", "toArrow": "Standard", "label": "包含", "stroke": "#000", "points": [-1610, 227, -1646.5, 237.5, -1683, 248], "classId": ****************, "from": -18, "to": -19}, {"strokeWidth": 1, "classCode": "输出", "strokeDashArray": [], "toArrowOutline": false, "fromPort": "", "toPort": "", "toArrow": "Standard", "label": "输出", "stroke": "#000", "points": [-1674.000903968918, 1.0558494486998367, -1569.3149988529472, -78.86162169416411, -1442.605379360719, -42.936515543237064], "classId": 6062728188450053, "from": -3, "to": -20}, {"strokeWidth": 1, "classCode": "输入", "strokeDashArray": [], "toArrowOutline": false, "fromEndSegmentLength": 60, "fromPort": "", "toEndSegmentLength": 60, "toPort": "", "toArrow": "Standard", "label": "输入", "stroke": "#000", "points": [-1674.000903968918, 1.0558494486998367, -1547.2912844766902, 36.98095559962688, -1442.605379360719, -42.936515543237064], "classId": 6062728188450042, "from": -3, "to": -20}, {"strokeWidth": 1, "classCode": "关联", "strokeDashArray": [], "toArrowOutline": false, "fromPort": "", "toPort": "", "toArrow": "Standard", "label": "关联", "stroke": "#000", "points": [-1386.605379360719, -47.89149916570373, -1162.8558155138471, -44.948316860491275], "classId": ****************, "from": -20, "to": -21}, {"strokeWidth": 1, "classCode": "关联", "strokeDashArray": [], "toArrowOutline": false, "fromEndSegmentLength": 60, "fromPort": "", "toEndSegmentLength": 60, "toPort": "", "toArrow": "Standard", "label": "关联", "stroke": "#000", "points": [-1716.5913028328082, -156.13206105703244, -1872.0298663520996, -256.494992873683], "classId": ****************, "from": -49, "to": -41}, {"strokeWidth": 1, "classCode": "关联", "strokeDashArray": [], "toArrowOutline": false, "fromEndSegmentLength": 60, "fromPort": "", "toEndSegmentLength": 60, "toPort": "", "toArrow": "Standard", "label": "关联", "stroke": "#000", "points": [-1725.0645952918599, -154.9813216354939, -1857.0331804904079, -205.6696822409102], "classId": ****************, "from": -49, "to": -13}, {"strokeWidth": 1, "classCode": "关联", "strokeDashArray": [], "toArrowOutline": false, "fromEndSegmentLength": 60, "fromPort": "", "toEndSegmentLength": 60, "toPort": "", "toArrow": "Standard", "label": "关联", "stroke": "#000", "points": [-1724.4913358673434, -135.25017709372713, -1857.0812286148284, -81.61555329151399], "classId": ****************, "from": -49, "to": -16}, {"strokeWidth": 1, "classCode": "关联", "strokeDashArray": [], "toArrowOutline": false, "fromEndSegmentLength": 60, "fromPort": "", "toEndSegmentLength": 60, "toPort": "", "toArrow": "Standard", "label": "关联", "stroke": "#000", "points": [-1715.0429670585654, -134.**************, -1860.160878155582, -31.198560577618466], "classId": ****************, "from": -49, "to": -14}, {"strokeWidth": 1, "classCode": "包含", "strokeDashArray": [], "toArrowOutline": false, "fromEndSegmentLength": 60, "fromPort": "", "toEndSegmentLength": 60, "toPort": "", "toArrow": "Standard", "label": "包含", "stroke": "#000", "points": [-1699.9117506234911, -134.**************, -1701.8228051234364, -5.620857363759455], "classId": ****************, "from": -49, "to": -3}, {"strokeWidth": 1, "classCode": "包含", "strokeDashArray": [], "toArrowOutline": false, "fromEndSegmentLength": 60, "fromPort": "", "toEndSegmentLength": 60, "toPort": "", "toArrow": "Standard", "label": "包含", "stroke": "#000", "points": [-1701.432719425183, -262.*************, -1699.906911992039, -156.13206105703244], "classId": ****************, "from": -48, "to": -49}, {"strokeWidth": 1, "classCode": "包含", "strokeDashArray": [], "toArrowOutline": false, "fromEndSegmentLength": 60, "fromPort": "", "curve": {"class": "<PERSON>.<PERSON>", "classType": "Link", "name": "<PERSON><PERSON>"}, "toEndSegmentLength": 60, "toPort": "", "toArrow": "Standard", "label": "包含", "stroke": "#000", "points": [-1695.*************, -262.*************, -1649.*************, -182.**************, -1753.*************, -182.*************, -1707.*************, -262.*************], "routing": {"class": "<PERSON>.<PERSON>", "classType": "Link", "name": "Normal"}, "classId": ****************, "from": -48, "to": -48}, {"strokeWidth": 1, "classCode": "顺序流", "strokeDashArray": [], "toArrowOutline": false, "fromEndSegmentLength": 60, "fromPort": "", "curve": {"class": "<PERSON>.<PERSON>", "classType": "Link", "name": "<PERSON><PERSON>"}, "toEndSegmentLength": 60, "toPort": "", "toArrow": "Standard", "label": "顺序流", "stroke": "#000", "points": [-1693.*************, -134.**************, -1647.*************, -54.***************, -1752.************, -54.**************, -1706.************, -134.**************], "routing": {"class": "<PERSON>.<PERSON>", "classType": "Link", "name": "Normal"}, "classId": ****************, "from": -49, "to": -49}, {"strokeWidth": 1, "classCode": "关联", "strokeDashArray": [], "toArrowOutline": false, "fromEndSegmentLength": 60, "fromPort": "", "toEndSegmentLength": 60, "toPort": "", "toArrow": "Standard", "label": "关联", "stroke": "#000", "points": [-1704.*************, -134.**************, -1787.************, 13.***************, -1870.************5, 161.**************], "classId": ****************, "from": -49, "to": -17}, {"strokeWidth": 1, "classCode": "关联", "strokeDashArray": [], "toArrowOutline": false, "fromEndSegmentLength": 60, "fromPort": "", "toEndSegmentLength": 60, "toPort": "", "toArrow": "Standard", "label": "关联", "stroke": "#000", "points": [-1713.*************, 17.**************, -1788.************, 89.**************, -1863.************3, 161.**************], "classId": ****************, "from": -3, "to": -17}, {"strokeWidth": 1, "classCode": "顺序流", "strokeDashArray": [], "toArrowOutline": false, "fromEndSegmentLength": 60, "fromPort": "", "toEndSegmentLength": 60, "toPort": "", "toArrow": "Standard", "label": "顺序流", "stroke": "#000", "points": [-1674.8471408354108, 15.08099245279988, -1612.5251913148682, 101.60137227556473, -1511.5227453832017, 67.42085980811694], "classId": ****************, "from": -3, "to": -50}, {"strokeWidth": 1, "classCode": "顺序流", "strokeDashArray": [], "toArrowOutline": false, "fromEndSegmentLength": 60, "fromPort": "", "toEndSegmentLength": 60, "toPort": "", "toArrow": "Standard", "label": "顺序流", "stroke": "#000", "points": [-1511.5227453832017, 67.42085980811694, -1573.8446949037443, -19.099520014647915, -1674.8471408354108, 15.08099245279988], "classId": ****************, "from": -50, "to": -3}, {"strokeWidth": 1, "classCode": "顺序流", "strokeDashArray": [], "toArrowOutline": false, "fromEndSegmentLength": 60, "fromPort": "", "curve": {"class": "<PERSON>.<PERSON>", "classType": "Link", "name": "<PERSON><PERSON>"}, "toEndSegmentLength": 60, "toPort": "", "toArrow": "Standard", "label": "顺序流", "stroke": "#000", "points": [-1695.*************, 18.**************, -1649.*************, 98.**************, -1754.*************, 98.*************, -1708.*************, 18.**************5], "routing": {"class": "<PERSON>.<PERSON>", "classType": "Link", "name": "Normal"}, "classId": ****************, "from": -3, "to": -3}, {"strokeWidth": 1, "classCode": "包含", "strokeDashArray": [], "toArrowOutline": false, "fromEndSegmentLength": 60, "fromPort": "", "toEndSegmentLength": 60, "toPort": "", "toArrow": "Standard", "label": "包含", "stroke": "#000", "points": [-1672.*************, -388.*************, -1610.*************, -390.*************, -1609.************, -147.**************, -1671.*************, -145.*************], "classId": ****************, "from": -57, "to": -49}, {"strokeWidth": 1, "classCode": "包含", "strokeDashArray": [], "toArrowOutline": false, "fromEndSegmentLength": 60, "fromPort": "", "toEndSegmentLength": 60, "toPort": "", "toArrow": "Standard", "label": "包含", "stroke": "#000", "points": [-1672.*************, -388.*************, -1630.*************, -389.**************, -1628.*************, -275.*************, -1673.*************, -273.********734105], "classId": ****************, "from": -57, "to": -48}, {"strokeWidth": 1, "classCode": "包含", "strokeDashArray": [], "toArrowOutline": false, "fromEndSegmentLength": 60, "fromPort": "", "toEndSegmentLength": 60, "toPort": "", "toArrow": "Standard", "label": "包含", "stroke": "#000", "points": [-1807.5742733311517, -318.2833269519745, -1724.1640499058637, -372.70948914518056], "classId": ****************, "from": -59, "to": -57}, {"strokeWidth": 1, "classCode": "包含", "strokeDashArray": [], "toArrowOutline": false, "fromEndSegmentLength": 60, "fromPort": "", "toEndSegmentLength": 60, "toPort": "", "toArrow": "Standard", "label": "包含", "stroke": "#000", "points": [-1791.1573013746347, -384.6921718048181, -1728.*************, -386.8658339735035], "classId": ****************, "from": -58, "to": -57}, {"strokeWidth": 1, "classCode": "包含", "strokeDashArray": [], "toArrowOutline": false, "fromEndSegmentLength": 60, "fromPort": "", "toEndSegmentLength": 60, "toPort": "", "toArrow": "Standard", "label": "包含", "stroke": "#000", "points": [-1830.3750850053261, -364.81159988025036, -1829.6695189790487, -318.510035735276], "classId": ****************, "from": -58, "to": -59}, {"strokeWidth": 1, "classCode": "引用", "strokeDashArray": [], "toArrowOutline": false, "fromEndSegmentLength": 60, "fromPort": "", "toEndSegmentLength": 60, "toPort": "", "toArrow": "Standard", "label": "引用", "stroke": "#000", "points": [-1512, -283, -1598, -219.5, -1684, -156], "classId": 6062728188450054, "from": -67, "to": -49}, {"strokeWidth": 1, "classCode": "输入", "strokeDashArray": [], "toArrowOutline": false, "fromEndSegmentLength": 60, "fromPort": "", "toEndSegmentLength": 60, "toPort": "", "toArrow": "Standard", "label": "输入", "stroke": "#000", "points": [-1496, -283, -1457.5, -172, -1419, -61], "classId": 6062728188450042, "from": -67, "to": -20}, {"strokeWidth": 1, "classCode": "输出", "strokeDashArray": [], "toArrowOutline": false, "fromEndSegmentLength": 60, "fromPort": "", "toEndSegmentLength": 60, "toPort": "", "toArrow": "Standard", "label": "输出", "stroke": "#000", "points": [-1501.464655906582, -372.7775878581644, -1418.1605648612324, -61.542453358876955], "classId": 6062728188450053, "from": -66, "to": -20}, {"strokeWidth": 1, "classCode": "顺序流", "strokeDashArray": [], "toArrowOutline": false, "fromEndSegmentLength": 60, "fromPort": "", "toEndSegmentLength": 60, "toPort": "", "toArrow": "Standard", "label": "顺序流", "stroke": "#000", "points": [-1455.5227453832017, 77.4703813089919, -1422.1479109321829, 131.1761610895872, -1384.7481658194802, 80.19136510117922], "classId": ****************, "from": -50, "to": -55}, {"strokeWidth": 1, "classCode": "顺序流", "strokeDashArray": [], "toArrowOutline": false, "fromEndSegmentLength": 60, "fromPort": "", "toEndSegmentLength": 60, "toPort": "", "toArrow": "Standard", "label": "顺序流", "stroke": "#000", "points": [-1384.7481658194802, 80.19136510117922, -1418.123000270499, 26.485585320583937, -1455.5227453832017, 77.4703813089919], "classId": ****************, "from": -55, "to": -50}, {"strokeWidth": 1, "classCode": "顺序流", "strokeDashArray": [], "toArrowOutline": false, "fromEndSegmentLength": 60, "fromPort": "", "toEndSegmentLength": 60, "toPort": "", "toArrow": "Standard", "label": "顺序流", "stroke": "#000", "points": [-1455.4316039453715, 179.11429379649482, -1423.22871231736, 283.4794581675255, -1385.7583315745826, 180.88762452376525], "classId": ****************, "from": -52, "to": -53}, {"strokeWidth": 1, "classCode": "顺序流", "strokeDashArray": [], "toArrowOutline": false, "fromEndSegmentLength": 60, "fromPort": "", "toEndSegmentLength": 60, "toPort": "", "toArrow": "Standard", "label": "顺序流", "stroke": "#000", "points": [-1385.7583315745826, 180.88762452376525, -1420.594967759977, 180.00095916013004, -1455.4316039453715, 179.11429379649482], "classId": ****************, "from": -53, "to": -52}, {"strokeWidth": 1, "classCode": "顺序流", "strokeDashArray": [], "toArrowOutline": false, "fromEndSegmentLength": 60, "fromPort": "", "toEndSegmentLength": 60, "toPort": "", "toArrow": "Standard", "label": "顺序流", "stroke": "#000", "points": [-1686.7538768968293, 18.**************5, -1642.8267784699701, 142.9396859347472, -1511.4316039453715, 156.36455221610743], "classId": ****************, "from": -3, "to": -52}, {"strokeWidth": 1, "classCode": "顺序流", "strokeDashArray": [], "toArrowOutline": false, "fromEndSegmentLength": 60, "fromPort": "", "toEndSegmentLength": 60, "toPort": "", "toArrow": "Standard", "label": "顺序流", "stroke": "#000", "points": [-1511.4316039453715, 156.36455221610743, -1555.3587023722307, 31.804008917600775, -1686.7538768968293, 18.**************5], "classId": ****************, "from": -52, "to": -3}, {"strokeWidth": 1, "classCode": "顺序流", "strokeDashArray": [], "toArrowOutline": false, "fromEndSegmentLength": 60, "fromPort": "", "toEndSegmentLength": 60, "toPort": "", "toArrow": "Standard", "label": "顺序流", "stroke": "#000", "points": [-1692.1660260650956, 18.**************5, -1502.5198124955411, 249.77546347416148], "classId": ****************, "from": -3, "to": -51}, {"strokeWidth": 1, "classCode": "顺序流", "strokeDashArray": [], "toArrowOutline": false, "fromEndSegmentLength": 60, "fromPort": "", "toEndSegmentLength": 60, "toPort": "", "toArrow": "Standard", "label": "顺序流", "stroke": "#000", "points": [-1385.3475874404749, 293.1784919168716, -1417.2119006932917, 309.2309373249458, -1451.571764053289, 291.8991837706198], "classId": ****************, "from": -54, "to": -51}, {"strokeWidth": 1, "classCode": "顺序流", "strokeDashArray": [], "toArrowOutline": false, "fromEndSegmentLength": 60, "fromPort": "", "toEndSegmentLength": 60, "toPort": "", "toArrow": "Standard", "label": "顺序流", "stroke": "#000", "points": [-1455.6712876042677, 99.69262976894217, -1385.6097893535166, 158.30155292453367], "classId": ****************, "from": -50, "to": -53}, {"strokeWidth": 1, "classCode": "顺序流", "strokeDashArray": [], "toArrowOutline": false, "fromEndSegmentLength": 60, "fromPort": "", "toEndSegmentLength": 60, "toPort": "", "toArrow": "Standard", "label": "顺序流", "stroke": "#000", "points": [-1466.0917026343416, 104.3938995177769, -1374.778630189335, 251.0727910389046], "classId": ****************, "from": -50, "to": -54}, {"strokeWidth": 1, "classCode": "顺序流", "strokeDashArray": [], "toArrowOutline": false, "fromEndSegmentLength": 60, "fromPort": "", "toEndSegmentLength": 60, "toPort": "", "toArrow": "Standard", "label": "顺序流", "stroke": "#000", "points": [-1455.4316039453715, 156.9327982445243, -1384.7481658194802, 102.7366837924311], "classId": ****************, "from": -52, "to": -55}, {"strokeWidth": 1, "classCode": "顺序流", "strokeDashArray": [], "toArrowOutline": false, "fromEndSegmentLength": 60, "fromPort": "", "toEndSegmentLength": 60, "toPort": "", "toArrow": "Standard", "label": "顺序流", "stroke": "#000", "points": [-1455.4316039453715, 179.11429379649482, -1417.9612232025938, 76.5224601527346, -1385.7583315745826, 180.88762452376525], "classId": ****************, "from": -52, "to": -53}, {"strokeWidth": 1, "classCode": "顺序流", "strokeDashArray": [], "toArrowOutline": false, "fromEndSegmentLength": 60, "fromPort": "", "toEndSegmentLength": 60, "toPort": "", "toArrow": "Standard", "label": "顺序流", "stroke": "#000", "points": [-1455.4316039453715, 200.75809588542074, -1385.3475874404749, 256.716330298045], "classId": ****************, "from": -52, "to": -54}, {"strokeWidth": 1, "classCode": "顺序流", "strokeDashArray": [], "toArrowOutline": false, "fromEndSegmentLength": 60, "fromPort": "", "toEndSegmentLength": 60, "toPort": "", "toArrow": "Standard", "label": "顺序流", "stroke": "#000", "points": [-1374.2490688906628, 109.26784689239423, -1462.0708609821063, 249.77546347416148], "classId": ****************, "from": -55, "to": -51}, {"strokeWidth": 1, "classCode": "顺序流", "strokeDashArray": [], "toArrowOutline": false, "fromEndSegmentLength": 60, "fromPort": "", "toEndSegmentLength": 60, "toPort": "", "toArrow": "Standard", "label": "顺序流", "stroke": "#000", "points": [-1385.7583315745826, 203.70708202013412, -1451.571764053289, 255.66866462972627], "classId": ****************, "from": -53, "to": -51}, {"strokeWidth": 1, "classCode": "顺序流", "strokeDashArray": [], "toArrowOutline": false, "fromEndSegmentLength": 60, "fromPort": "", "toEndSegmentLength": 60, "toPort": "", "toArrow": "Standard", "label": "顺序流", "stroke": "#000", "points": [-1385.3475874404749, 278.77558984009534, -1417.918079677075, 227.39913134349632, -1451.571764053289, 278.0726646729708], "classId": ****************, "from": -54, "to": -51}, {"strokeWidth": 1, "classCode": "关联", "strokeDashArray": [], "toArrowOutline": false, "fromEndSegmentLength": 60, "fromPort": "", "toEndSegmentLength": 60, "toPort": "", "toArrow": "Standard", "label": "关联", "stroke": "#000", "points": [-1702.8803779319214, -134.**************, -1806.175678557011, 224.4675736721436], "classId": ****************, "from": -49, "to": -69}, {"strokeWidth": 1, "classCode": "关联", "strokeDashArray": [], "toArrowOutline": false, "fromEndSegmentLength": 60, "fromPort": "", "toEndSegmentLength": 60, "toPort": "", "toArrow": "Standard", "label": "关联", "stroke": "#000", "points": [-1671.*************, -144.31205019801968, -1443.07341381722, -136.5842029080848], "classId": ****************, "from": -49, "to": -56}, {"strokeWidth": 1, "classCode": "关联", "strokeDashArray": [], "toArrowOutline": false, "fromEndSegmentLength": 60, "fromPort": "", "toEndSegmentLength": 60, "toPort": "", "toArrow": "Standard", "label": "关联", "stroke": "#000", "points": [-1678.033650178145, -5.139894937438175, -1443.07341381722, -118.06546474151455], "classId": ****************, "from": -3, "to": -56}, {"strokeWidth": 1, "classCode": "关联", "strokeDashArray": [], "toArrowOutline": false, "fromEndSegmentLength": 60, "fromPort": "", "toEndSegmentLength": 60, "toPort": "", "toArrow": "Standard", "label": "关联", "stroke": "#000", "points": [-1678.4084065839413, -262.20632491516295, -1443.07341381722, -152.19624706897235], "classId": ****************, "from": -48, "to": -56}, {"strokeWidth": 1, "classCode": "引用", "strokeDashArray": [], "toArrowOutline": false, "fromEndSegmentLength": 60, "fromPort": "", "toEndSegmentLength": 60, "toPort": "", "toArrow": "Standard", "label": "引用", "stroke": "#000", "points": [-1512.3767252749165, -372.7775878581644, -1690.7952544606094, -156.13206105703244], "classId": 6062728188450054, "from": -66, "to": -49}, {"strokeWidth": 1, "classCode": "参与", "strokeDashArray": [], "toArrowOutline": false, "fromEndSegmentLength": 60, "fromPort": "", "toEndSegmentLength": 60, "toPort": "", "toArrow": "Standard", "label": "参与", "stroke": "#000", "points": [-1707.3249973745978, 126.11568252744632, -1652.310030287135, 74.58715160803338, -1702.5344847866552, 18.**************5], "classId": 6062728188450045, "from": -4, "to": -3}, {"strokeWidth": 1, "classCode": "包含", "strokeDashArray": [], "toArrowOutline": false, "fromEndSegmentLength": 60, "fromPort": "", "toEndSegmentLength": 60, "toPort": "", "toArrow": "Standard", "label": "包含", "stroke": "#000", "points": [-1107.6474654599124, -308.5276107127561, -1107.6474654599124, -237.8804823783563], "classId": ****************, "from": -47, "to": -70}, {"strokeWidth": 1, "classCode": "包含", "strokeDashArray": [], "toArrowOutline": false, "fromEndSegmentLength": 60, "fromPort": "", "toEndSegmentLength": 60, "toPort": "", "toArrow": "Standard", "label": "包含", "stroke": "#000", "points": [-1108.020791875937, -181.8804823783563, -1109.4824890978225, -72.25115946945826], "classId": ****************, "from": -70, "to": -21}, {"strokeWidth": 1, "classCode": "包含", "strokeDashArray": [], "toArrowOutline": false, "fromEndSegmentLength": 60, "fromPort": "", "toEndSegmentLength": 60, "toPort": "", "toArrow": "Standard", "label": "包含", "stroke": "#000", "points": [-800.9701808721144, -178.40240730027784, -800.9701808721144, -73.00137622406598], "classId": ****************, "from": -46, "to": -62}, {"strokeWidth": 1, "classCode": "包含", "strokeDashArray": [], "toArrowOutline": false, "fromEndSegmentLength": 60, "fromPort": "", "toEndSegmentLength": 60, "toPort": "", "toArrow": "Standard", "label": "包含", "stroke": "#000", "points": [-800.2702793121513, -23.00137622406598, -798.1595101231753, 52.39384039350534], "classId": ****************, "from": -62, "to": -63}, {"fromEndSegmentLength": 60, "fromPort": "", "toEndSegmentLength": 60, "toPort": "", "toArrow": "Standard", "from": -68, "to": -64, "label": "位于", "points": [-501.0413184166714, -230.03963989648122, -501.0413184166714, -310.186429938389]}, {"strokeWidth": 1, "classCode": "位于", "strokeDashArray": [], "toArrowOutline": false, "fromEndSegmentLength": 60, "fromPort": "", "toEndSegmentLength": 60, "toPort": "", "toArrow": "Standard", "label": "位于", "stroke": "#000", "points": [-501.0413184166714, -96.39284985457346, -501.0413184166714, -176.03963989648122], "classId": 6062728188450046, "from": -71, "to": -68}, {"strokeWidth": 1, "classCode": "部署于", "strokeDashArray": [], "toArrowOutline": false, "fromEndSegmentLength": 60, "fromPort": "", "toEndSegmentLength": 60, "toPort": "", "toArrow": "Standard", "label": "部署于", "stroke": "#000", "points": [-746.9701808721144, -27.70060544672822, -558.0413184166714, 43.32534881125555], "classId": 6062728188450049, "from": -62, "to": -72}, {"strokeWidth": 1, "classCode": "使用", "strokeDashArray": [], "toArrowOutline": false, "fromEndSegmentLength": 60, "fromPort": "", "toEndSegmentLength": 60, "toPort": "", "toArrow": "Standard", "label": "使用", "stroke": "#000", "points": [-770.2274305514176, -23.00137622406598, -533.62863375661, 169.40073022924207], "classId": 6062728188450043, "from": -62, "to": -73}, {"strokeWidth": 1, "classCode": "关联", "strokeDashArray": [], "toArrowOutline": false, "fromEndSegmentLength": 60, "fromPort": "", "toEndSegmentLength": 60, "toPort": "", "toArrow": "Standard", "label": "关联", "stroke": "#000", "points": [-1674.000903968918, -0.1399285204390317, -859.4701808721144, -189.78220506221516], "classId": ****************, "from": -3, "to": -46}, {"strokeWidth": 1, "classCode": "关联", "strokeDashArray": [], "toArrowOutline": false, "fromEndSegmentLength": 60, "fromPort": "", "toEndSegmentLength": 60, "toPort": "", "toArrow": "Standard", "label": "关联", "stroke": "#000", "points": [-1056.8558155138471, -23.781134768305563, -852.4876046256107, 55.151361930045], "classId": ****************, "from": -21, "to": -63}, {"strokeWidth": 1, "classCode": "关联", "strokeDashArray": [], "toArrowOutline": false, "fromEndSegmentLength": 60, "fromPort": "", "toEndSegmentLength": 60, "toPort": "", "toArrow": "Standard", "label": "关联", "stroke": "#000", "points": [-1671.*************, -142.22839793493588, -854.9701808721144, -53.84470988741586], "classId": ****************, "from": -49, "to": -62}, {"strokeWidth": 1, "classCode": "部署于", "strokeDashArray": [], "toArrowOutline": false, "fromEndSegmentLength": 60, "fromPort": "", "toEndSegmentLength": 60, "toPort": "", "toArrow": "Standard", "label": "部署于", "stroke": "#000", "points": [-501.0413184166714, 39.25394018733431, -501.0413184166714, -40.392849854573456], "classId": 6062728188450049, "from": -72, "to": -71}, {"strokeWidth": 1, "classCode": "包含", "strokeDashArray": [], "toArrowOutline": false, "fromEndSegmentLength": 60, "fromPort": "", "toEndSegmentLength": 60, "toPort": "", "toArrow": "Standard", "label": "包含", "stroke": "#000", "points": [-798.9203261861236, -303.77806009892714, -800.4596350801725, -228.40240730027784], "classId": ****************, "from": -74, "to": -46}]}