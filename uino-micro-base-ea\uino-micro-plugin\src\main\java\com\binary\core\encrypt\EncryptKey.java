package com.binary.core.encrypt;

import java.io.Serializable;
import java.security.Key;

import com.binary.core.io.SerializationUtils;
import com.binary.core.util.BinaryUtils;

public class Encrypt<PERSON>ey implements Serializable {
	private static final long serialVersionUID = 1L;
	
	
	private Key key;
	
	
	
	public EncryptKey(Key key) {
		BinaryUtils.checkEmpty(key, "key");
		this.key = key;
	}
	
	
	public EncryptKey(String key) {
		BinaryUtils.checkEmpty(key, "key");
		byte[] array = Encrypt.string2Byte(key);
		this.key = SerializationUtils.deserialize(array, Key.class);
	}
	
	
	
	
	public Key getKey() {
		return this.key;
	}
	
	
	

}
