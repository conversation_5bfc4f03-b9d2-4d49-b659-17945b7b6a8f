package com.uinnova.product.eam.comm.model;




import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;


@Comment("角色组织表[VC_ROLE_ORG]")
public class VcRoleOrg implements EntityBean {
	private static final long serialVersionUID = 1L;


	@Comment("ID[ID]")
	private Long id;


	@Comment("角色ID[ROLE_ID]")
	private Long roleId;


	@Comment("组织名称[ORG_NAMES]")
	private String orgNames;


	@Comment("查看的权限[SEE_AUTH]")
	private Integer seeAuth;


	@Comment("编辑的权限[EDIT_AUTH]")
	private Integer editAuth;


	@Comment("所属域[DOMAIN_ID]")
	private Long domainId;


	@Comment("数据状态[DATA_STATUS]    数据状态:0=删除，1=正常")
	private Integer dataStatus;


	@Comment("创建人[CREATOR]")
	private String creator;


	@Comment("修改人[MODIFIER]")
	private String modifier;


	@Comment("创建时间[CREATE_TIME]    创建时间:yyyyMMddHHmmss")
	private Long createTime;


	@Comment("更新时间[MODIFY_TIME]    更新时间:yyyyMMddHHmmss")
	private Long modifyTime;




	public Long getId() {
		return this.id;
	}
	public void setId(Long id) {
		this.id = id;
	}


	public Long getRoleId() {
		return this.roleId;
	}
	public void setRoleId(Long roleId) {
		this.roleId = roleId;
	}


	public String getOrgNames() {
		return this.orgNames;
	}
	public void setOrgNames(String orgNames) {
		this.orgNames = orgNames;
	}


	public Integer getSeeAuth() {
		return this.seeAuth;
	}
	public void setSeeAuth(Integer seeAuth) {
		this.seeAuth = seeAuth;
	}


	public Integer getEditAuth() {
		return this.editAuth;
	}
	public void setEditAuth(Integer editAuth) {
		this.editAuth = editAuth;
	}


	public Long getDomainId() {
		return this.domainId;
	}
	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}


	public Integer getDataStatus() {
		return this.dataStatus;
	}
	public void setDataStatus(Integer dataStatus) {
		this.dataStatus = dataStatus;
	}


	public String getCreator() {
		return this.creator;
	}
	public void setCreator(String creator) {
		this.creator = creator;
	}


	public String getModifier() {
		return this.modifier;
	}
	public void setModifier(String modifier) {
		this.modifier = modifier;
	}


	public Long getCreateTime() {
		return this.createTime;
	}
	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}


	public Long getModifyTime() {
		return this.modifyTime;
	}
	public void setModifyTime(Long modifyTime) {
		this.modifyTime = modifyTime;
	}


}


