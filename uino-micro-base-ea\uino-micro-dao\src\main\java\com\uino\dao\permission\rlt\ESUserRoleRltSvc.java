package com.uino.dao.permission.rlt;

import java.util.ArrayList;
import java.util.List;

import jakarta.annotation.PostConstruct;

import org.elasticsearch.index.query.BoolQueryBuilder;
import org.springframework.stereotype.Service;

import com.uino.dao.AbstractESBaseDao;
import com.uino.dao.ESConst;
import com.uino.dao.util.ESUtil;
import com.uino.util.sys.CommonFileUtil;
import com.uino.bean.permission.base.SysUserRoleRlt;
import com.uino.bean.permission.query.CSysUserRoleRlt;

/**
 * <b>用户 - 角色 关系
 *
 * <AUTHOR>
 */
@Service
public class ESUserRoleRltSvc extends AbstractESBaseDao<SysUserRoleRlt, CSysUserRoleRlt> {

	@Override
	public String getIndex() {
		return ESConst.INDEX_SYS_USER_ROLE_RLT;
	}

	@Override
	public String getType() {
		return ESConst.INDEX_SYS_USER_ROLE_RLT;
	}

	@PostConstruct
	public void init() {
		List<SysUserRoleRlt> data = CommonFileUtil.getData("/initdata/uino_sys_user_role_rlt.json",
				SysUserRoleRlt.class);
		super.initIndex(data);
	}

	public Integer removeRltByCdt(CSysUserRoleRlt cdt) {
		BoolQueryBuilder query = ESUtil.cdtToBuilder(cdt);
		return deleteByQuery(query, true);
	}

	public Integer saveOrUpdateRlt(Long userId, Long[] roleIds) {
		List<SysUserRoleRlt> records = new ArrayList<>();
		for (Long roleId : roleIds) {
			SysUserRoleRlt userRoleRlt = new SysUserRoleRlt();
			userRoleRlt.setUserId(userId);
			userRoleRlt.setRoleId(roleId);
			records.add(userRoleRlt);
		}
		return saveOrUpdateBatch(records);
	}

}
