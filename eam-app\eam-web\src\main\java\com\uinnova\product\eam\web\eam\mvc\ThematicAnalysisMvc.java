package com.uinnova.product.eam.web.eam.mvc;

import com.alibaba.fastjson.JSONObject;
import com.binary.core.util.BinaryUtils;
import com.binary.framework.web.RemoteResult;
import com.uinnova.product.eam.model.vo.EamMatrixAnalysisVo;
import com.uinnova.product.eam.model.vo.EamMatrixTableVo;
import com.uinnova.product.eam.service.ThematicAnalysisSvc;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 专题分析相关接口
 * <AUTHOR>
 */
@RestController
@RequestMapping(value = "/eam/analysis")
public class ThematicAnalysisMvc {

    @Resource
    private ThematicAnalysisSvc thematicAnalysisSvc;

    @GetMapping(value = "/matrix")
    @ModDesc(desc = "获取矩阵分析数据", pDesc = "专题分析配置id", pType = Long.class, rDesc = "矩阵数据", rType = Object.class)
    public RemoteResult getMatrixById(@RequestParam Long id) {
        EamMatrixAnalysisVo result = thematicAnalysisSvc.getMatrixById(id);
        return new RemoteResult(result);
    }

    @GetMapping(value = "/matrix/table")
    @ModDesc(desc = "获取矩阵表格数据", pDesc = "专题分析配置id", pType = Long.class, rDesc = "矩阵表格数据", rType = Object.class)
    public RemoteResult getMatrixTable(@RequestParam Long id) {
        List<EamMatrixTableVo> result = thematicAnalysisSvc.getMatrixTable(id);
        return new RemoteResult(result);
    }

    @RequestMapping("/matrix/export")
    @ModDesc(desc = "导出", pDesc = "专题分析配置id", pType = Long.class, rDesc = "excel", rType = ResponseEntity.class)
    public ResponseEntity<byte[]> export(@RequestParam Long id) {
        return thematicAnalysisSvc.export(id);
    }

    @PostMapping(value = "/graph/table")
    @ModDesc(desc = "应用系统关系图谱接口数据查询", pDesc = "分页模糊匹配条件", pType = Long.class, rDesc = "", rType = Object.class)
    public RemoteResult getRltTable(@RequestBody JSONObject body) {
        List<Long> rltIds = body.getJSONArray("id").toJavaList(Long.class);
        BinaryUtils.checkEmpty(rltIds, "id");
        String like = body.getString("like");
        List<EamMatrixTableVo> result = thematicAnalysisSvc.getRltInterface(rltIds, like);
        return new RemoteResult(result);
    }

    @RequestMapping("/graph/export")
    @ModDesc(desc = "应用系统关系图谱导出", pDesc = "", pType = Long.class, rDesc = "excel", rType = ResponseEntity.class)
    public ResponseEntity<byte[]> rltGraphExport(@RequestBody JSONObject body) {
        List<Long> rltIds = body.getJSONArray("id").toJavaList(Long.class);
        return thematicAnalysisSvc.rltGraphExport(rltIds);
    }

}
