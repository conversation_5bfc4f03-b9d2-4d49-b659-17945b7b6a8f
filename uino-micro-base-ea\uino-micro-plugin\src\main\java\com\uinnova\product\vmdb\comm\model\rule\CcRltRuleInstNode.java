package com.uinnova.product.vmdb.comm.model.rule;

import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;

/**
 * 
 * <AUTHOR>
 *
 */
@Comment("朋友圈实例节点[CC_RLT_RULE_INST_NODE]")
public class CcRltRuleInstNode implements EntityBean {
    private static final long serialVersionUID = 1L;

    @Comment("ID[ID]")
    private Long id;

    @Comment("所属朋友圈规则[RULE_DEF_ID]")
    private Long ruleDefId;

    @Comment("所属朋友圈实例[RULE_INST_ID]")
    private Long ruleInstId;

    @Comment("所属朋友圈节点[RULE_NODE_ID]")
    private Long ruleNodeId;

    @Comment("所在层级[RULE_INST_LAYER_ID]")
    private Long ruleInstLayerId;

    @Comment("节点样式[NODE_CSS]    排列顺序")
    private String nodeCss;

    @Comment("X坐标[X]    X坐标:依赖、调用、等等")
    private Integer x;

    @Comment("Y坐标[Y]    Y坐标:供页面用")
    private Integer y;

    @Comment("所属域[DOMAIN_ID]")
    private Long domainId;

    @Comment("数据状态[DATA_STATUS]    数据状态:数据状态：1-正常 0-删除")
    private Integer dataStatus;

    @Comment("创建时间[CREATE_TIME]    创建时间:yyyyMMddHHmmss")
    private Long createTime;

    @Comment("修改时间[MODIFY_TIME]    修改时间:yyyyMMddHHmmss")
    private Long modifyTime;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getRuleDefId() {
        return this.ruleDefId;
    }

    public void setRuleDefId(Long ruleDefId) {
        this.ruleDefId = ruleDefId;
    }

    public Long getRuleInstId() {
        return this.ruleInstId;
    }

    public void setRuleInstId(Long ruleInstId) {
        this.ruleInstId = ruleInstId;
    }

    public Long getRuleNodeId() {
        return this.ruleNodeId;
    }

    public void setRuleNodeId(Long ruleNodeId) {
        this.ruleNodeId = ruleNodeId;
    }

    public Long getRuleInstLayerId() {
        return this.ruleInstLayerId;
    }

    public void setRuleInstLayerId(Long ruleInstLayerId) {
        this.ruleInstLayerId = ruleInstLayerId;
    }

    public String getNodeCss() {
        return this.nodeCss;
    }

    public void setNodeCss(String nodeCss) {
        this.nodeCss = nodeCss;
    }

    public Integer getX() {
        return this.x;
    }

    public void setX(Integer x) {
        this.x = x;
    }

    public Integer getY() {
        return this.y;
    }

    public void setY(Integer y) {
        this.y = y;
    }

    public Long getDomainId() {
        return this.domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }

    public Integer getDataStatus() {
        return this.dataStatus;
    }

    public void setDataStatus(Integer dataStatus) {
        this.dataStatus = dataStatus;
    }

    public Long getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getModifyTime() {
        return this.modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }

}
