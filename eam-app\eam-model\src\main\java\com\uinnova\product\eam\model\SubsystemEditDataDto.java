package com.uinnova.product.eam.model;

import com.uinnova.product.eam.comm.bean.SubsystemEditData;

import java.util.Map;

public class SubsystemEditDataDto extends SubsystemEditData {

    private Map<String,String> diffData;

    private Map<String, Map<Long, Object>> historyData;

    public Map<String, String> getDiffData() {
        return diffData;
    }

    public void setDiffData(Map<String, String> diffData) {
        this.diffData = diffData;
    }

    public Map<String, Map<Long, Object>> getHistoryData() {
        return historyData;
    }

    public void setHistoryData(Map<String, Map<Long, Object>> historyData) {
        this.historyData = historyData;
    }
}
