package com.uinnova.product.eam.model.diagram;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 检出视图请求体
 * <AUTHOR>
 */
@Data
public class DiagramPullRequest {

    @Comment("检出的视图id")
    private List<String> diagramIds;

    @Comment("设计库视图id、私有库目录id")
    private Map<String, Long> designIdMap = new HashMap<>(16);

    @Comment("私有库默认视图id与设计库默认视图id映射")
    private Map<String, String> diagramIdMap = new HashMap<>(16);

    @Comment("用户标识")
    private String ownerCode;
}
