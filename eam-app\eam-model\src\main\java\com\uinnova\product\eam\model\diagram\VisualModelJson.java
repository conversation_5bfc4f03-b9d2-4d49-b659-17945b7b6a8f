package com.uinnova.product.eam.model.diagram;

import lombok.Data;

import java.util.List;

/**
 * 元模型Json类
 * <AUTHOR>
 * @date 2022/5/25
 */
@Data
public class VisualModelJson {
    private Long modelId;
    private String name;
    private Boolean active;
    private String sheetId;
    private String linkToPortIdProperty;
    private List<VisualModelNode> nodeDataArray;
    private List<VisualModelLink> linkDataArray;
}
