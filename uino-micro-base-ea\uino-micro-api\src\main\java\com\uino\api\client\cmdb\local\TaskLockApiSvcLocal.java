package com.uino.api.client.cmdb.local;

import com.uino.dao.cmdb.ESDataSetTaskLockSvc;
import com.uino.dao.util.ESUtil;
import com.uino.bean.cmdb.base.ESTaskLock;
import com.uino.api.client.cmdb.ITaskLockApiSvc;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 任务锁实现类
 *
 * <AUTHOR>
 * @version 2020-4-24
 */
@Service
public class TaskLockApiSvcLocal implements ITaskLockApiSvc {

    private final static Logger logger = LoggerFactory.getLogger(TaskLockApiSvcLocal.class);

    @Autowired
    private ESDataSetTaskLockSvc dataSetTaskLockSvc;

    @Override
    public boolean getLock(String taskName, long taskExecuteTime) {
        BoolQueryBuilder lockQuery = QueryBuilders.boolQuery();
        lockQuery.must(QueryBuilders.termQuery("taskName.keyword", taskName));
        List<ESTaskLock> listByQuery = dataSetTaskLockSvc.getListByQuery(lockQuery);
        // 查询任务锁是否已经存在，如果已经存在则认为有其他任务已经在执行
        if (!listByQuery.isEmpty()) {
            long now = System.currentTimeMillis();
            ESTaskLock oldLockObj = listByQuery.get(0);
            long lockTime = oldLockObj.getLockTime();
            // 如果发现任务执行的时长已经超过了任务执行时间，可能是任务执行过程中出错了
            // 导致任务没有被解锁，此处进行一下解锁
            if (lockTime != 0
                    && ((now - lockTime) > taskExecuteTime)) {
                breakLock(taskName);
                logger.info("Execute " + taskName + " task,lock time is more than "
                        + taskExecuteTime + "ms. Unlocked");
            } else {
                logger.info("Execute " + taskName + " task,failed to obtain " +
                        "the execution of the lock, waiting for the next execution");
            }
            return false;
        } else {
            // 由于入库有一定延时，有可能在查询任务时还未完成入库，此处进行进一步校验
            boolean isAdd = addLock(taskName);
            if (isAdd) {
                // 只有新增时才认为是获取到了任务锁
                return true;
            } else {
                return false;
            }
        }
    }

    @Override
    public void breakLock(String taskName) {
        BoolQueryBuilder lockQuery = QueryBuilders.boolQuery();
        lockQuery.must(QueryBuilders.termQuery("taskName.keyword", taskName));
        dataSetTaskLockSvc.deleteByQuery(lockQuery, true);
    }

    @Override
    public boolean addLock(String taskName) {
        ESTaskLock lock = new ESTaskLock();
        lock.setId(ESUtil.getUUID());
        lock.setTaskName(taskName);
        lock.setLockTime(System.currentTimeMillis());
        // 新增为true 更新为false
        dataSetTaskLockSvc.saveOrUpdate(lock);
        return true;
    }

    @Override
    public boolean isLocked(String taskName) {
        BoolQueryBuilder lockQuery = QueryBuilders.boolQuery();
        lockQuery.must(QueryBuilders.termQuery("taskName.keyword", taskName));
        List<ESTaskLock> listByQuery = dataSetTaskLockSvc.getListByQuery(lockQuery);
        // 查询任务锁是否已经存在，如果已经存在则认为有其他任务已经在执行
        if (!listByQuery.isEmpty()) {
            return true;
        } else {
            return false;
        }
    }
}
