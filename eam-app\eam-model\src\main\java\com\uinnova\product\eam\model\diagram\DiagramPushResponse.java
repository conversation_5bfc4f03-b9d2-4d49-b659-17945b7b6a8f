package com.uinnova.product.eam.model.diagram;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Collections;
import java.util.Map;

/**
 * 视图发布响应体
 * <AUTHOR>
 */
@Accessors(chain = true)
@Data
public class DiagramPushResponse {

    @Comment("响应code")
    private int resultCode = 1;

    @Comment("私有库视图id,资产库视图id")
    private Map<String,String> diagramMap = Collections.emptyMap();

    @Comment("设计库中视图id, 设计库中最新状态的视图Id")
    private Map<Long,String> designDiagramMap = Collections.emptyMap();

    @Comment("错误信息")
    private String errMsg;

}
