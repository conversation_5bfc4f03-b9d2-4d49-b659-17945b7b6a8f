package com.uinnova.product.eam.model.vo;

import com.uinnova.product.eam.base.diagram.model.ESDiagram;
import com.uinnova.product.eam.comm.model.es.EamAttention;
import com.uinnova.product.eam.model.RecentlyViewBo;
import com.uinnova.product.eam.model.asset.AssetCiDetailInfoDTO;
import com.uinnova.product.eam.model.cj.domain.PlanDesignInstance;
import com.uinnova.product.vmdb.provider.ci.bean.CiGroupPage;
import com.uino.bean.permission.business.UserInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
public class AttentionVo implements Serializable {

    // 企业级架构资产或系统级架构资产文件夹列表
    private List<EamCategoryVO> assetsDirList;
    // 视图列表
    private List<ESDiagram> diagramList;
    //方案列表
    private List<PlanDesignInstance> planDesignList;
    // 资产ci属性信息
    private List<AssetCiDetailInfoDTO> assetCiDetailInfoDTOList;
    // 分页信息
    private Map<String, Long> pageMap;
    // 系统详情
    private List<CiGroupPage> ciGroupPage;

    //返回关注的方案id和视图的id，并且是按照时间降序的list
    private List<EamAttention> attentionList;

    private RecentlyViewBo recentlyViewBo;

    // 创建人信息
    private Map<String, UserInfo> userInfoMap;

}
