package com.uino.service.i18;

import java.io.IOException;
import java.util.Collection;
import java.util.LinkedList;
import java.util.List;

import jakarta.annotation.PostConstruct;

import org.springframework.util.Assert;

import com.uinnova.product.devcloud.i18n.client.support.AbstractI18nClient;
import com.uinnova.product.devcloud.i18n.client.support.I18nTransData;
import com.uino.util.sys.CommonFileUtil;

import lombok.extern.slf4j.Slf4j;

/**
 *
 * <AUTHOR>
 *
 */
@Slf4j
public class BaseI18Client extends AbstractI18nClient {

    private List<String> i18JsonFiles = null;
    private static final List<I18nTransData> DATAS = new LinkedList<>();

    public BaseI18Client(Collection<String> i18FileNames) {
        Assert.notEmpty(i18FileNames, "请指明要读取的i18n文件");
        i18JsonFiles = new LinkedList<>(i18FileNames);
        readI18JsonFiles();
    }

    @Override
    protected boolean verifyRefresh() throws IOException {
        // TODO Auto-generated method stub
        return true;
    }

    @Override
    protected List<I18nTransData> loadLanguageData() throws IOException {
        return BaseI18Client.DATAS;
    }

    @PostConstruct
    public void construct() {
        super.refresh();
        // log.info("成功刷新i18{}", JSON.toJSONString(datas));
    }

    /**
     * 读取i18json文件
     */
    public void readI18JsonFiles() {
        DATAS.clear();
        if (i18JsonFiles != null && i18JsonFiles.size() > 0) {
            i18JsonFiles.forEach(i18File -> {
                List<I18nTransData> addI18Datas = CommonFileUtil.getData("/i18/" + i18File, I18nTransData.class);
                if (addI18Datas != null && addI18Datas.size() > 0) {
                    DATAS.addAll(addI18Datas);
                }
            });
        } else {
            log.error("未检测到国际化文件");
        }
    }

}
