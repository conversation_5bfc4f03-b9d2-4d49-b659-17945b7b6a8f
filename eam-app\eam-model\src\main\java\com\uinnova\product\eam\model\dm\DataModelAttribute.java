package com.uinnova.product.eam.model.dm;

/**
 * 数据建模实体属性字段
 * <AUTHOR>
 */
public class DataModelAttribute {
    public static final String NUM = "序号";
    public static final String NAME_CN = "中文名称";
    public static final String NAME_EN = "英文名称";
    public static final String DATA_TYPE = "数据类型";
    public static final String STANDARD = "数据标准";
    public static final String VAL_DOMAIN = "值域";
    public static final String PRIMARY_KEY = "主键";
    public static final String FOREIGN_KEY = "外键";
    public static final String NON_EMPTY = "非空";
    public static final String ENTITY = "所属实体";
    public static final String INHERIT_ID = "继承标识";
    public static final String DEFAULT_VAL = "默认值";
    public static final String TARGET = "目的";
    public static final String DEFINITION = "定义";
    public static final String RANGE = "范围";

    public static final String STANDARD_NUM = "标准编号";
    public static final String LENGTH = "长度";
    public static final String PRECISION = "精度";

    public static final String BELONG_CLASS = "所属分类";
    public static final String BELONG_DOMAIN = "所属域组";

}
