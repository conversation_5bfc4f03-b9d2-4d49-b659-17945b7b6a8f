package com.uinnova.product.eam.db.diagram.es;

import com.uinnova.product.eam.base.diagram.mix.model.WordDoc;
import com.uino.dao.AbstractESBaseDao;
import com.uino.service.util.FileUtil;
import org.springframework.stereotype.Repository;

import jakarta.annotation.PostConstruct;
import java.util.List;

/**
 * 文档管理数据访问层
 *
 * <AUTHOR>
 */
@Repository
public class ESWordDocEsDao extends AbstractESBaseDao<WordDoc, WordDoc> {
    @Override
    public String getIndex() {
        return "uino_eam_word_doc";
    }

    @Override
    public String getType() {
        return "_doc";
    }

    //    @PostConstruct
//    public void init() {
//        super.initIndex();
//    }
    @PostConstruct
    public void init() {
        List<WordDoc> list = FileUtil.getData("/initdata/uino_eam_word_doc.json", WordDoc.class);
        super.initIndex(list);
    }
}
