package com.uinnova.product.eam.comm.model;




import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;


@Comment("分类附加属性表[VC_CLASS_ADD_ATTR]")
public class CVcClassAddAttr implements Condition {
	private static final long serialVersionUID = 1L;


	@Comment("ID[ID] operate-Equal[=]")
	private Long id;


	@Comment("ID[ID] operate-In[in]")
	private Long[] ids;


	@Comment("ID[ID] operate-GTEqual[>=]")
	private Long startId;

	@Comment("ID[ID] operate-LTEqual[<=]")
	private Long endId;


	@Comment("分类ID[CLASS_ID] operate-Equal[=]")
	private Long classId;


	@Comment("分类ID[CLASS_ID] operate-In[in]")
	private Long[] classIds;


	@Comment("分类ID[CLASS_ID] operate-GTEqual[>=]")
	private Long startClassId;

	@Comment("分类ID[CLASS_ID] operate-LTEqual[<=]")
	private Long endClassId;


	@Comment("分类类型[CLASS_TYPE] operate-Equal[=]")
	private Integer classType;


	@Comment("分类类型[CLASS_TYPE] operate-In[in]")
	private Integer[] classTypes;


	@Comment("分类类型[CLASS_TYPE] operate-GTEqual[>=]")
	private Integer startClassType;

	@Comment("分类类型[CLASS_TYPE] operate-LTEqual[<=]")
	private Integer endClassType;


	@Comment("附加属性1[ADD_ATTR_1] operate-Like[like]")
	private String addAttr1;


	@Comment("附加属性2[ADD_ATTR_2] operate-Like[like]")
	private String addAttr2;


	@Comment("附加属性3[ADD_ATTR_3] operate-Like[like]")
	private String addAttr3;


	@Comment("附加属性4[ADD_ATTR_4] operate-Like[like]")
	private String addAttr4;


	@Comment("附加属性5[ADD_ATTR_5] operate-Like[like]")
	private String addAttr5;


	@Comment("附加属性6[ADD_ATTR_6] operate-Like[like]")
	private String addAttr6;


	@Comment("所属域[DOMAIN_ID] operate-Equal[=]")
	private Long domainId;


	@Comment("所属域[DOMAIN_ID] operate-In[in]")
	private Long[] domainIds;


	@Comment("所属域[DOMAIN_ID] operate-GTEqual[>=]")
	private Long startDomainId;

	@Comment("所属域[DOMAIN_ID] operate-LTEqual[<=]")
	private Long endDomainId;


	@Comment("数据状态[DATA_STATUS] operate-Equal[=]    数据状态:0=删除，1=正常")
	private Integer dataStatus;


	@Comment("数据状态[DATA_STATUS] operate-In[in]    数据状态:0=删除，1=正常")
	private Integer[] dataStatuss;


	@Comment("数据状态[DATA_STATUS] operate-GTEqual[>=]    数据状态:0=删除，1=正常")
	private Integer startDataStatus;

	@Comment("数据状态[DATA_STATUS] operate-LTEqual[<=]    数据状态:0=删除，1=正常")
	private Integer endDataStatus;


	@Comment("创建人[CREATOR] operate-Like[like]")
	private String creator;


	@Comment("创建人[CREATOR] operate-Equal[=]")
	private String creatorEqual;


	@Comment("创建人[CREATOR] operate-In[in]")
	private String[] creators;


	@Comment("修改人[MODIFIER] operate-Like[like]")
	private String modifier;


	@Comment("修改人[MODIFIER] operate-Equal[=]")
	private String modifierEqual;


	@Comment("修改人[MODIFIER] operate-In[in]")
	private String[] modifiers;


	@Comment("创建时间[CREATE_TIME] operate-Equal[=]    创建时间:yyyyMMddHHmmss")
	private Long createTime;


	@Comment("创建时间[CREATE_TIME] operate-In[in]    创建时间:yyyyMMddHHmmss")
	private Long[] createTimes;


	@Comment("创建时间[CREATE_TIME] operate-GTEqual[>=]    创建时间:yyyyMMddHHmmss")
	private Long startCreateTime;

	@Comment("创建时间[CREATE_TIME] operate-LTEqual[<=]    创建时间:yyyyMMddHHmmss")
	private Long endCreateTime;


	@Comment("更新时间[MODIFY_TIME] operate-Equal[=]    更新时间:yyyyMMddHHmmss")
	private Long modifyTime;


	@Comment("更新时间[MODIFY_TIME] operate-In[in]    更新时间:yyyyMMddHHmmss")
	private Long[] modifyTimes;


	@Comment("更新时间[MODIFY_TIME] operate-GTEqual[>=]    更新时间:yyyyMMddHHmmss")
	private Long startModifyTime;

	@Comment("更新时间[MODIFY_TIME] operate-LTEqual[<=]    更新时间:yyyyMMddHHmmss")
	private Long endModifyTime;




	public Long getId() {
		return this.id;
	}
	public void setId(Long id) {
		this.id = id;
	}


	public Long[] getIds() {
		return this.ids;
	}
	public void setIds(Long[] ids) {
		this.ids = ids;
	}


	public Long getStartId() {
		return this.startId;
	}
	public void setStartId(Long startId) {
		this.startId = startId;
	}


	public Long getEndId() {
		return this.endId;
	}
	public void setEndId(Long endId) {
		this.endId = endId;
	}


	public Long getClassId() {
		return this.classId;
	}
	public void setClassId(Long classId) {
		this.classId = classId;
	}


	public Long[] getClassIds() {
		return this.classIds;
	}
	public void setClassIds(Long[] classIds) {
		this.classIds = classIds;
	}


	public Long getStartClassId() {
		return this.startClassId;
	}
	public void setStartClassId(Long startClassId) {
		this.startClassId = startClassId;
	}


	public Long getEndClassId() {
		return this.endClassId;
	}
	public void setEndClassId(Long endClassId) {
		this.endClassId = endClassId;
	}


	public Integer getClassType() {
		return this.classType;
	}
	public void setClassType(Integer classType) {
		this.classType = classType;
	}


	public Integer[] getClassTypes() {
		return this.classTypes;
	}
	public void setClassTypes(Integer[] classTypes) {
		this.classTypes = classTypes;
	}


	public Integer getStartClassType() {
		return this.startClassType;
	}
	public void setStartClassType(Integer startClassType) {
		this.startClassType = startClassType;
	}


	public Integer getEndClassType() {
		return this.endClassType;
	}
	public void setEndClassType(Integer endClassType) {
		this.endClassType = endClassType;
	}


	public String getAddAttr1() {
		return this.addAttr1;
	}
	public void setAddAttr1(String addAttr1) {
		this.addAttr1 = addAttr1;
	}


	public String getAddAttr2() {
		return this.addAttr2;
	}
	public void setAddAttr2(String addAttr2) {
		this.addAttr2 = addAttr2;
	}


	public String getAddAttr3() {
		return this.addAttr3;
	}
	public void setAddAttr3(String addAttr3) {
		this.addAttr3 = addAttr3;
	}


	public String getAddAttr4() {
		return this.addAttr4;
	}
	public void setAddAttr4(String addAttr4) {
		this.addAttr4 = addAttr4;
	}


	public String getAddAttr5() {
		return this.addAttr5;
	}
	public void setAddAttr5(String addAttr5) {
		this.addAttr5 = addAttr5;
	}


	public String getAddAttr6() {
		return this.addAttr6;
	}
	public void setAddAttr6(String addAttr6) {
		this.addAttr6 = addAttr6;
	}


	public Long getDomainId() {
		return this.domainId;
	}
	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}


	public Long[] getDomainIds() {
		return this.domainIds;
	}
	public void setDomainIds(Long[] domainIds) {
		this.domainIds = domainIds;
	}


	public Long getStartDomainId() {
		return this.startDomainId;
	}
	public void setStartDomainId(Long startDomainId) {
		this.startDomainId = startDomainId;
	}


	public Long getEndDomainId() {
		return this.endDomainId;
	}
	public void setEndDomainId(Long endDomainId) {
		this.endDomainId = endDomainId;
	}


	public Integer getDataStatus() {
		return this.dataStatus;
	}
	public void setDataStatus(Integer dataStatus) {
		this.dataStatus = dataStatus;
	}


	public Integer[] getDataStatuss() {
		return this.dataStatuss;
	}
	public void setDataStatuss(Integer[] dataStatuss) {
		this.dataStatuss = dataStatuss;
	}


	public Integer getStartDataStatus() {
		return this.startDataStatus;
	}
	public void setStartDataStatus(Integer startDataStatus) {
		this.startDataStatus = startDataStatus;
	}


	public Integer getEndDataStatus() {
		return this.endDataStatus;
	}
	public void setEndDataStatus(Integer endDataStatus) {
		this.endDataStatus = endDataStatus;
	}


	public String getCreator() {
		return this.creator;
	}
	public void setCreator(String creator) {
		this.creator = creator;
	}


	public String getCreatorEqual() {
		return this.creatorEqual;
	}
	public void setCreatorEqual(String creatorEqual) {
		this.creatorEqual = creatorEqual;
	}


	public String[] getCreators() {
		return this.creators;
	}
	public void setCreators(String[] creators) {
		this.creators = creators;
	}


	public String getModifier() {
		return this.modifier;
	}
	public void setModifier(String modifier) {
		this.modifier = modifier;
	}


	public String getModifierEqual() {
		return this.modifierEqual;
	}
	public void setModifierEqual(String modifierEqual) {
		this.modifierEqual = modifierEqual;
	}


	public String[] getModifiers() {
		return this.modifiers;
	}
	public void setModifiers(String[] modifiers) {
		this.modifiers = modifiers;
	}


	public Long getCreateTime() {
		return this.createTime;
	}
	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}


	public Long[] getCreateTimes() {
		return this.createTimes;
	}
	public void setCreateTimes(Long[] createTimes) {
		this.createTimes = createTimes;
	}


	public Long getStartCreateTime() {
		return this.startCreateTime;
	}
	public void setStartCreateTime(Long startCreateTime) {
		this.startCreateTime = startCreateTime;
	}


	public Long getEndCreateTime() {
		return this.endCreateTime;
	}
	public void setEndCreateTime(Long endCreateTime) {
		this.endCreateTime = endCreateTime;
	}


	public Long getModifyTime() {
		return this.modifyTime;
	}
	public void setModifyTime(Long modifyTime) {
		this.modifyTime = modifyTime;
	}


	public Long[] getModifyTimes() {
		return this.modifyTimes;
	}
	public void setModifyTimes(Long[] modifyTimes) {
		this.modifyTimes = modifyTimes;
	}


	public Long getStartModifyTime() {
		return this.startModifyTime;
	}
	public void setStartModifyTime(Long startModifyTime) {
		this.startModifyTime = startModifyTime;
	}


	public Long getEndModifyTime() {
		return this.endModifyTime;
	}
	public void setEndModifyTime(Long endModifyTime) {
		this.endModifyTime = endModifyTime;
	}


}


