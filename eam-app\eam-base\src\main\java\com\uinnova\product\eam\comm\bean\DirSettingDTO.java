package com.uinnova.product.eam.comm.bean;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class DirSettingDTO {
    private Long id;
    @Comment("分类ID")
    private Long classId;
    @Comment("配置的目录名称")
    private String dirName;
    @Comment("配置的目录的层级父目录id")
    private Long parentId;
    @Comment("排序字段")
    private Integer order;
    private List<DirSettingDTO> childNode;
}
