package com.uinnova.product.eam.model.vo;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.model.enums.HandleTypeEnum;
import lombok.Data;

@Data
public class MineAssetsReq {

    @Comment("操作类型：我的发布-MINE_PUBLISH，我的关注-MINE_ATTENTION，最近查看-RECENTLY_VIEW")
    private HandleTypeEnum handleType;

    private String like;

    @Comment("资产库根目录id")
    private Long rootId;
}
