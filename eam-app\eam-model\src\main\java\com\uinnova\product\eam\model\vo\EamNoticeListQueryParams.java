package com.uinnova.product.eam.model.vo;

import com.uinnova.product.eam.base.diagram.model.ESDiagram;
import com.uinnova.product.eam.comm.model.es.EamMatrixInstance;
import com.uinnova.product.eam.comm.model.es.EamMultiModelHierarchy;
import com.uinnova.product.eam.model.cj.dto.PlanDesignInstanceDTO;
import com.uinnova.product.eam.model.cj.dto.PlanVersionDTO;
import com.uino.bean.permission.base.SysUser;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Data
@Builder
public class EamNoticeListQueryParams {

    private List<PlanVersionDTO> planVersionList;
    private Set<Long> planIds;
    private Set<String> diagramIds;
    private Set<Long> matrixIds;
    private Set<Long> userIds;
    private Set<Long> modelIds;
    private Set<Long> dirIds;
    private Map<String, PlanDesignInstanceDTO> businessKeyPlanMap;
    private Map<Long, PlanDesignInstanceDTO> planMap;
    private Map<String, ESDiagram> diagramMap;
    private Map<Long, SysUser> userMap;
    private Map<Long, EamMultiModelHierarchy> modelMap;
    private Map<Long, EamMatrixInstance> matrixInstanceMap;
}

