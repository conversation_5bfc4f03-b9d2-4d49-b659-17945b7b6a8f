package com.uino.service.cmdb.microservice;

import java.util.List;

import com.uino.bean.cmdb.base.ESCITreeConfigInfo;
import com.uino.bean.cmdb.business.CITreeNode;

/**
 * ci树服务
 * 
 * <AUTHOR>
 *
 */
public interface ICITreeSvc {
	/**
	 * 获取ci tree配置
	 * 
	 * @return
	 */
	public List<ESCITreeConfigInfo> getCITreeConfigs(Long domainId);

	/**
     * 获取ci tree配置by id
     * 
     * @param id
     * @return
     */
	public ESCITreeConfigInfo getCITreeConfig(Long id);

	/**
     * 保存ci tree配置
     * 
     * @param saveInfo
     * @return
     */
	public ESCITreeConfigInfo save(ESCITreeConfigInfo saveInfo);

	/**
	 * 删除ci tree配置
	 * 
	 * @param id
	 * @return
	 */
	public List<ESCITreeConfigInfo> delete(Long domainId, Long id);

	/**
	 * 获取ci树
	 * 
	 * @param config
	 *            ciTree配置信息
	 * @param hasNullNode
	 *            返回信息是否包含空节点true:包含 false:不包含
	 * @param returnAllCI
	 *            是否返回所有ci true:返回所有匹配上的ci false:每个节点最多只挂载60条ci，其余的自己条件查询翻页
	 * @return 符合条件的ci树
	 */
	public CITreeNode getCITree(ESCITreeConfigInfo config, boolean hasNullNode, boolean returnAllCI);
}
