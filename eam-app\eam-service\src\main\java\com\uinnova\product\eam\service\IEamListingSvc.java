package com.uinnova.product.eam.service;

import com.uinnova.product.eam.comm.model.es.CEamListing;
import com.uinnova.product.eam.comm.model.es.EamListing;
import com.uinnova.product.eam.model.DirListConfigDto;
import com.uinnova.product.eam.model.ListCondition;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uino.bean.cmdb.business.dataset.DataSetExeResultSheetPage;

import java.util.List;

/**
 * <AUTHOR>
 * @description : 清单类型业务接口层
 **/
public interface IEamListingSvc {
    /**
     * 新增/修改清单基本信息
     * @param eamList params
     * @return 清单id
     */
    Long saveOrUpdate(CEamListing eamList);


    /**
     * 清单列表查询（支持发布状态，清单名称模糊搜索）
     * @param dto 参数
     * @return 清单数据集合
     */
    List<EamListing> queryLists(ListCondition dto);

    /**
     * 根据id查询指定清单数据
     * @param id id
     * @return 单个清单信息
     */
    EamListing queryListById(Long id);

    /**
     * 逻辑删除清单
     * @param id 清单id
     * @return
     */
    String deleteListing(Long id);

    /**
     * 清单的发布/取消发布
     * @param id 清单id
     * @param releaseState 发布/取消发布参数
     * @return
     */
    Long releaseListing(Long id, Integer releaseState);

    /**
     * 数据模型资产清单左侧列表
     * @param cardId 卡片id
     * @return 左侧列表信息
     */
    List<DirListConfigDto> dataAssetsList(Long cardId);

    /**
     * 根据资产清单id查询对应数据超市数据列表
     * @param id 资产清单id
     * @return 数据集列表
     */
    DataSetExeResultSheetPage getListingData(Long id, String like, int pageNum, int pageSize);

    /**
     * 根据数据集id获取对象分类信息
     * @param id 数据集id
     * @return 对象分类信息
     */
    List<CcCiClassInfo> getClassByDataSet(Long id);
}
