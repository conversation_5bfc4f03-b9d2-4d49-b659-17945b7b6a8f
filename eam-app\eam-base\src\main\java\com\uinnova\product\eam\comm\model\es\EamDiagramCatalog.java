package com.uinnova.product.eam.comm.model.es;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@Comment("业务建模目录[UINO_EAM_DIAGRAM_CATALOG]")
@Deprecated
public class EamDiagramCatalog implements Serializable {
    @Comment("主键")
    private Long id;
    @Deprecated
    private Long dirCode;
    @Comment("目录层级路径:例：#1#2#7#")
    private String dirPath;
    @Deprecated
    private Long releaseDirCode;
    @Comment("目录绑定的ciCode,普通目录为空")
    private String assetCode;
    @Comment("目录绑定的流程视图id")
    private String diagramId;
    @Comment("目录名称")
    private String dirName;
    @Comment("目录类型：10业务建模,101数据建模")
    private Integer dirType;
    @Comment("父级目录id")
    private Long parentDirCode;
    @Comment("目录层级")
    private Integer dirLvl;
    @Comment("模型树Id")
    private Long modelId;
    @Comment("领域id")
    private Long domainId;
    @Comment("用户标识")
    private String ownerCode;
    @Comment("创建人")
    private String creator;
    @Comment("修改人")
    private String modifier;
    @Comment("创建时间")
    private Long createTime;
    @Comment("修改时间")
    private Long modifyTime;

}
