package com.uino.util.express.annotation;

import com.uino.util.express.common.FunctionTagConst;
import org.springframework.stereotype.Component;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Uino aviator注解
 * name()值与{@link com.googlecode.aviator.runtime.function.AbstractFunction}中的getName()方法返回值保持一致
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Component
public @interface UinoAviatorFunction {

    /**
     * 方法名
     */
    String name();

    /**
     * 别名，不填使用方法名以“.”分割的最后一部分
     */
    String shortName() default "";

    /**
     * 方法描述
     */
    String desc();

    /**
     * 返回值类型
     */
    Class<?> returnType();

    /**
     * 公式标签
     */
    String[] tags() default FunctionTagConst.OTHER;

}
