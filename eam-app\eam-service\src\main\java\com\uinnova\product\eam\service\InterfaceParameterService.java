package com.uinnova.product.eam.service;

import com.uinnova.product.eam.comm.model.es.InterfaceParameter;

import java.util.List;
import java.util.Map;

public interface InterfaceParameterService {
    Integer save(List<InterfaceParameter> interfaceParameterList);

    Map<String,List<InterfaceParameter>> getList(Integer parameterType,Integer requestMethod);

    Integer deleteById(Long id);

    List<InterfaceParameter> getChildListByCode(String parentCode);
}
