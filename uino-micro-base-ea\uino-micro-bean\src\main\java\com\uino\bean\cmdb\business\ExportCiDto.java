package com.uino.bean.cmdb.business;

import com.binary.framework.bean.annotation.Comment;
import com.uino.bean.permission.business.IValidDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.util.Assert;

import java.util.*;

/**
 * 
 * <AUTHOR>
 *
 */
@Data
@ApiModel(value="导出对象数据",description = "导出对象数据")
public class ExportCiDto implements IValidDto {



    @ApiModelProperty(value="下载指定的分类数据的id集合")
    @Comment("下载指定的分类数据,与ciIds不应该同时出现.同时出现抛出异常")
    private Set<Long> ciClassIds;

    @ApiModelProperty(value="下载指定的ci数据的id集合")
    @Comment("下载指定的ci数据,与ciClassIds不应该同时出现.同时出现抛出异常")
    private Set<Long> ciIds;

    @ApiModelProperty(value="与分类ID配套使用,0时只下载模板,1时下载数据",example = "0")
    @Comment("与分类ID配套使用,0时只下载模板,1时下载数据")
    private Integer hasData = 0;

    @ApiModelProperty(value="与分类ID配套使用,0时不下载类定义，1时下载",example = "0")
    @Comment("与分类ID配套使用,0时不下载类定义，1时下载")
    private Integer hasClsDef = 0;

    @ApiModelProperty(value="是否指定准确数据模式",example = "false")
    private Boolean dataMode;

    @ApiModelProperty(value="所属域",example = "1L")
    private Long domainId;

    @Comment("范围查询 大于等于这个时间创建的数据")
    private String gteTime;
    @Comment("范围查询 小于等于这个时间创建的数据")
    private String lteTime;
    @Comment("0表示导出不合格数据  1表示走的全部数据")
    private Integer usage=1;
    @Comment("ownerCode")
    private String ownerCode;

    @ApiModelProperty(value = "全文检索关键字列表")
    private List<String> words = new ArrayList<>();

    @ApiModelProperty(value = "参与检索的字段-与word配合使用", example = "[\"ciCode\",\"ciPrimaryKey\",\"属性名称\"]")
    private List<String> searchKeys = new ArrayList<>();

    @Comment("分类导出属性字段，默认导出全部字段")
    private Map<Long, List<Long>> attrsIdMap=new HashMap<>();

    //选定的部门id
    @Comment("导出岗位模版选定的部门id")
    private Long deptId;


    /**
     * 是否指定准确数据模式
     */
    public boolean isDataMode() {
        return dataMode=ciIds != null && !ciIds.isEmpty();
    }


    @Override
    public void valid() {
        if (this.getCiClassIds() != null && !this.getCiClassIds().isEmpty() && this.getCiIds() != null
                && !this.getCiIds().isEmpty()) {
            Assert.isTrue(false, "ciClassIds与ciIds不可同时存在");
        }
    }
}
