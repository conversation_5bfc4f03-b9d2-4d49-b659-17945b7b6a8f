package com.uino.service.cmdb.microservice.impl;

import java.io.*;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.nio.file.DirectoryStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import java.util.zip.ZipOutputStream;

import javax.imageio.ImageIO;
import javax.imageio.ImageReader;
import javax.imageio.stream.ImageInputStream;

import com.uino.util.rsm.RsmUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.MultiMatchQueryBuilder.Type;
import org.elasticsearch.index.query.Operator;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.TermQueryBuilder;
import org.elasticsearch.index.query.TermsQueryBuilder;
import org.elasticsearch.search.sort.SortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.ResourceUtils;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.binary.core.exception.MessageException;
import com.binary.core.i18n.LanguageResolver;
import com.binary.core.io.Compression;
import com.binary.core.io.FileSystem;
import com.binary.core.io.Resource;
import com.binary.core.lang.Conver;
import com.binary.core.util.BinaryUtils;
import com.binary.framework.spring.MultipartFileResource;
import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClassDir;
import com.uinnova.product.vmdb.comm.model.image.CCcImage;
import com.uino.bean.cmdb.SysTopData;
import com.uino.bean.cmdb.base.CcCiClassDir;
import com.uino.bean.cmdb.base.CcImage;
import com.uino.bean.cmdb.base.ESResource;
import com.uino.bean.cmdb.business.ImageCount;
import com.uino.bean.cmdb.business.ImportDirMessage;
import com.uino.bean.cmdb.business.ImportImageMessage;
import com.uino.bean.cmdb.business.ImportResultMessage;
import com.uino.bean.cmdb.enums.DirTypeEnum;
import com.uino.bean.cmdb.query.ESSearchImageBean;
import com.uino.bean.permission.query.SearchKeywordBean;
import com.uino.dao.BaseConst;
import com.uino.dao.cmdb.ESCIClassSvc;
import com.uino.dao.cmdb.ESDirSvc;
import com.uino.dao.cmdb.ESImageSvc;
import com.uino.dao.util.ESUtil;
import com.uino.service.cmdb.microservice.IImageSvc;
import com.uino.service.cmdb.microservice.ITopDataSvc;
import com.uino.service.sys.microservice.IResourceSvc;
import com.uino.service.sys.microservice.ISysSvc;
import com.uino.service.util.FileUtil;
import com.uino.util.sys.CompressionUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ImageSvc implements IImageSvc {

    @Autowired
    private ESImageSvc svc;

    @Autowired
    private ESDirSvc dirSvc;

    @Autowired
    private ITopDataSvc topdataSvc;

    @Autowired
    private IResourceSvc resourceSvc;

    @Autowired
    private ESCIClassSvc esClassSvc;

    @Autowired
    private ISysSvc sysSvc;

    @Autowired
    private RsmUtils rsmUtils;

    @Value("${http.resource.space:}")
    private String urlPath;

    @Value("${http.resource.sync:}")
    private String syncHttpPath;

    @Value("${local.resource.space:}")
    private String localPath;

    @Value("${is.show.document.attribute:false}")
    private Boolean isShowDocumentAttribute;
    /**
     * 图标名称合法性正则
     */
    private Pattern imgNamePattern = Pattern.compile(
            ".*[.](jpg|png|gif|jpeg|bmp|tiff|pcx|tga|exif|fpx|svg|psd|cdr|pcd|dxf|ufo|eps|ai|raw|WMF)",
            Pattern.CASE_INSENSITIVE);

    /**
     * 视频名称合法性正则
     */
    private Pattern vedioNamePattern = Pattern.compile(".*[.](avi|rmvb|rm|asf|divx|mpg|mpeg|mpe|wmv|mp4|mkv|vob)",
            Pattern.CASE_INSENSITIVE);
    /**
     * 文档名称合法性正则
     */
    private Pattern documentNamePattern = Pattern.compile(".*[.](doc|docx|xls|xlsx|ppt|pptx|pdf)",
            Pattern.CASE_INSENSITIVE);
    /**
     * 验证filePart是否为zip格式S
     *
     * @param filePart
     */
    private void validFileContentTypeIsZip(MultipartFile filePart) {
        if (!"application/zip".equals(filePart.getContentType().toLowerCase())
                && !"application/x-zip".equals(filePart.getContentType().toLowerCase())
                && !"application/x-zip-compressed"
                .equals(filePart.getContentType().toLowerCase())) {
            throw MessageException
                    .i18n("BS_MDOMAIN_ONLY_ZIP");
        }
    }

    @Override
    public ImportResultMessage importZipImage(Long domainId, Integer sourceType, MultipartFile filePart) {
        domainId = domainId == null ? BaseConst.DEFAULT_DOMAIN_ID : domainId;
        sourceType = sourceType == null ? DirTypeEnum.IMAGE.getType() : sourceType;
        DirTypeEnum dirType = DirTypeEnum.valueOf(sourceType);
        Assert.notNull(dirType, "不支持的资源类型");
        MultipartFileResource imageSource = new MultipartFileResource(filePart);
        // 验证zip
        this.validFileContentTypeIsZip(filePart);
        // 保存当前目录上传的信息
        List<ImportDirMessage> dirMessages = new ArrayList<>();
        // 现支持多文件夹导入，全都按照zip流进行读取
        ZipInputStream zipIs = new ZipInputStream(imageSource.getInputStream(), Charset.forName("gbk"));

        // 按文件夹分组的压缩包内元素操作集合
        Map<String, List<ZipEntry>> dirImgEntryMap = new HashMap<>();
        Map<ZipEntry, byte[]> imgEntryBytesMap = new HashMap<>();
        Map<String, Map<ZipEntry, byte[]>> dirImg3DEntryMap = new HashMap<>();

        //查找系统中所有的3D资源目录
        BoolQueryBuilder dirquery = QueryBuilders.boolQuery();
        dirquery.must(QueryBuilders.termQuery("domainId", domainId));
        dirquery.must(QueryBuilders.termQuery("ciType", DirTypeEnum.IMAGE3D.getType()));
        List<CcCiClassDir> dir3D = dirSvc.getListByQuery(dirquery);
        Map<String, Long> dir3DSet = dir3D.stream()
                .collect(Collectors.toMap(CcCiClassDir::getDirName, CcCiClassDir::getId));

        //目录名称对应id
        Map<String, Long> dirIdByNameMap = new HashMap<>();

        // 将zip内对象遍历一次,组装map
        while (true) {
            ZipEntry zipEntry = null;
            try {
                zipEntry = zipIs.getNextEntry();
            } catch (Exception e) {
                log.error("读取导入图标文件异常,终止读取", e);
                throw new RuntimeException("读取导入图标文件异常，请检查zip文件");
            }
            if (zipEntry == null) {
                break;
            }

            //将数据组装到对应的数据操作集合中
            if (zipEntry.getName().contains("/")) {
                String dirName = zipEntry.getName().split("/")[0];
                if (dir3DSet.containsKey(dirName)) {
                    // 3D图标单独处理
                    if (!dirImg3DEntryMap.containsKey(dirName)) {
                        dirImg3DEntryMap.put(dirName, new HashMap<>());
                    }
                    dirImg3DEntryMap.get(dirName).put(zipEntry, FileUtil.readStream(zipIs, false));
                } else {
                    String dir = zipEntry.getName().substring(0, zipEntry.getName().lastIndexOf("/"));
                    dirImgEntryMap.computeIfAbsent(dir, k -> new LinkedList<>());
                    dirImgEntryMap.get(dir).add(zipEntry);
                    imgEntryBytesMap.put(zipEntry, FileUtil.readStream(zipIs, false));
                }
            }

            //判断目录结构并保存
            String name = zipEntry.getName();
            Long parentId = null;
            if (name.contains("/") && !dir3DSet.containsKey(name.split("/")[0])) {
                String parentPath = name.substring(0, name.lastIndexOf("/"));
                parentId = dirIdByNameMap.get(parentPath);
                //如果父目录不存在
                if (parentId == null) {
                    String[] split = parentPath.split("/");
                    Long p = 0L;
                    StringBuilder dir = new StringBuilder();
                    for (String s : split) {
                        dir.append(s);
                        Long pID = dirIdByNameMap.get(dir.toString());
                        if (pID == null) {
                            BoolQueryBuilder query = QueryBuilders.boolQuery();
                            query.must(QueryBuilders.termQuery("domainId", domainId));
                            query.must(QueryBuilders.termQuery("dirName.keyword", s));
                            query.must(QueryBuilders.termQuery("parentId", p));
                            query.must(QueryBuilders.termsQuery("ciType", Arrays.asList(DirTypeEnum.IMAGE.getType(),
                                    DirTypeEnum.IMAGE3D.getType(), DirTypeEnum.VEDIO.getType(),DirTypeEnum.DOCUMENT.getType())));
                            List<CcCiClassDir> dirs = dirSvc.getListByQuery(query);
                            if (dirs.size() > 0) {
                                dirIdByNameMap.put(dir.toString(), dirs.get(0).getId());
                                p = dirs.get(0).getId();
                            } else {
                                CcCiClassDir t = new CcCiClassDir();
                                // 3表示是图标目录，1表示CI分类，2表示关系分类
                                t.setDomainId(domainId);
                                t.setCiType(dirType.getType());
                                t.setDirName(s);
                                t.setParentId(p);
                                Long id = dirSvc.saveOrUpdate(t);
                                p = id;
                                dirIdByNameMap.put(dir.toString(), id);
                            }
                        } else {
                            p = pID;
                        }
                        dir.append("/");
                    }

                }

            }
        }
        Assert.isTrue(dirImgEntryMap.keySet().size() > 0 || dirImg3DEntryMap.keySet().size() > 0, "压缩包中未找到文件夹");

        // 导入3D图标
        if (dirType == DirTypeEnum.IMAGE3D && dirImg3DEntryMap.keySet().size() > 0) {
            Iterator<String> it = dirImg3DEntryMap.keySet().iterator();
            while (it.hasNext()) {
                String dirName = it.next();
                Long dirId = dir3DSet.get(dirName);
                Map<ZipEntry, byte[]> entryMap = dirImg3DEntryMap.get(dirName);
                ImportDirMessage im3dMessage = this.import3DZipImage(null, dirId, entryMap, true);
                if (!BinaryUtils.isEmpty(im3dMessage)) {
                    dirMessages.add(im3dMessage);
                }
            }
        }

        // 待入库列表
        List<CcImage> records = new LinkedList<>();
        List<ESResource> resInfos = new ArrayList<>();

        for (String dirName : dirImgEntryMap.keySet()) {

            ImportDirMessage dirMessage = ImportDirMessage.builder().dirName(dirName).build();
            List<ImportImageMessage> imgMessages = new ArrayList<>();
            Integer totalCount = 0;
            Integer failCount = 0;

            List<ZipEntry> imgEntrys = dirImgEntryMap.get(dirName);

            BoolQueryBuilder img_query = QueryBuilders.boolQuery();
            img_query.must(QueryBuilders.termQuery("domainId", domainId));
            img_query.must(QueryBuilders.termQuery("dirId", dirIdByNameMap.get(dirName)));
            // 获取该文件夹下已有的图片，导入时替换
            List<CcImage> images = svc.getListByQuery(1, 9999, img_query).getData();
            Map<Object, CcImage> imgMap = BinaryUtils.toObjectMap(images, "imgName");

            // 创造出该虚拟文件夹下操作对应的文件系统里的文件夹
            Long dateTimeFolder = ESUtil.getNumberDate();
            File destFolder = new File(localPath + "/" + dateTimeFolder);
            if (!destFolder.exists()) {
                destFolder.mkdirs();
            }

            List<String> saveImgs = new ArrayList<String>();
            // 遍历操作对象
            for (ZipEntry imgEntry : imgEntrys) {
                if (imgEntry.isDirectory()) {
                    continue;
                }
                totalCount++;
                String[] split = imgEntry.getName().split("/");
                String imgFullName = split[split.length - 1];
                String imgName = imgFullName.substring(0, imgFullName.lastIndexOf("."));
                String imgType = imgFullName.substring(imgFullName.lastIndexOf(".") + 1);
                ImportImageMessage imageMessage = ImportImageMessage.builder().imageName(imgFullName).build();
                // 文件名重复略过
                if (saveImgs.contains(imgName)) {
                    failCount++;
                    imageMessage.setMessage("[" + imgName + "]图标重复");
                    imgMessages.add(imageMessage);
                    continue;
                }
                // 不符合文件名校验的和已存在的不略过
                if (DirTypeEnum.IMAGE == dirType && !validFileSuffix(imgNamePattern, imgFullName)) {
                    failCount++;
                    imageMessage.setMessage("不支持的文件类型[" + imgFullName + "]");
                    imgMessages.add(imageMessage);
                    continue;
                } else if (DirTypeEnum.VEDIO == dirType && !validFileSuffix(vedioNamePattern, imgFullName)) {
                    failCount++;
                    imageMessage.setMessage("不支持的文件类型[" + imgFullName + "]");
                    imgMessages.add(imageMessage);
                    continue;
                }else if (DirTypeEnum.DOCUMENT == dirType && !validFileSuffix(documentNamePattern, imgFullName)) {
                    failCount++;
                    imageMessage.setMessage("不支持的文件类型[" + imgFullName + "]");
                    imgMessages.add(imageMessage);
                    continue;
                }
                // 图片大小超过1M的略过
                if (DirTypeEnum.IMAGE == dirType && imgEntry.getSize() > Math.pow(2, 20)) {
                    failCount++;
                    imageMessage.setMessage("[" + imgName + "]图标文件大小超过1M");
                    imgMessages.add(imageMessage);
                    continue;
                } else if (DirTypeEnum.VEDIO == dirType && imgEntry.getSize() > 10 * Math.pow(2, 20)) {
                    failCount++;
                    imageMessage.setMessage("[" + imgName + "]视频文件大小超过10M");
                    imgMessages.add(imageMessage);
                    continue;
                }else if (DirTypeEnum.DOCUMENT == dirType && imgEntry.getSize() > 100 * Math.pow(2, 20)) {
                    failCount++;
                    imageMessage.setMessage("[" + imgName + "]文档大小超过100M");
                    imgMessages.add(imageMessage);
                    continue;
                }

                // 将读取到的图片写入文件系统
                String imgFilePath = "/" + dateTimeFolder + "/" + UUID.randomUUID().toString() + "." + imgType;
                int optionType = 0;
                // 图标存在则替换
                if (imgMap.containsKey(imgName)) {
                    imgFilePath = imgMap.get(imgName).getImgPath();
                    optionType = 2;
                    String fileType = imgFilePath.substring(imgFilePath.lastIndexOf(".") + 1);
                    if (!imgType.equals(fileType)) {
                        // 后缀名更新，操作记录先删再存
                        new File(localPath + imgFilePath).delete();

                        // 删除原来的文件
                        rsmUtils.deleteRsm(imgFilePath);

                        resInfos.add(ESResource.builder().path(imgFilePath).publicUrl(syncHttpPath + imgFilePath)
                                .optionType(1).unzip(false).build());
                        imgFilePath = imgMap.get(imgName).getImgPath().replace(fileType, imgType);
                        optionType = 0;
                    }
                }

                File destFile = new File(localPath + imgFilePath);
                try {
                    FileUtils.writeByteArrayToFile(destFile, imgEntryBytesMap.get(imgEntry));
                    rsmUtils.uploadRsmFromFile(destFile);
                    // 记录资源操作信息
                    resInfos.add(ESResource.builder().path(imgFilePath).publicUrl(syncHttpPath + imgFilePath)
                            .optionType(optionType).unzip(false).build());
                } catch (Exception e) {
                    log.error("写入图片异常", e);
                    throw new RuntimeException("写入图片异常");
                }
                Integer sourceSize = Conver.to(destFile.length(), Integer.class);
                Integer width = 0;
                Integer heigh = 0;
                Map<String, Integer> imageSizeMap = getImageSize(destFile);
                if (imageSizeMap != null && !imageSizeMap.isEmpty()) {
                    if (!BinaryUtils.isEmpty(imageSizeMap.get("width"))) {
                        width = imageSizeMap.get("width");
                    }
                    if (!BinaryUtils.isEmpty(imageSizeMap.get("height"))) {
                        heigh = imageSizeMap.get("height");
                    }
                }
                CcImage record = imgMap.get(imgName);
                if (record == null) {
                    record = new CcImage();
                }
                record.setImgFullName(dirName + "|" + imgName);
                record.setImgName(imgName);
                record.setImgSize(sourceSize);
                record.setDirId(dirIdByNameMap.get(dirName));
                record.setImgGroup(dirType == DirTypeEnum.IMAGE3D ? 3 : (dirType == DirTypeEnum.VEDIO ? 4 :  (dirType == DirTypeEnum.DOCUMENT ? 5 : 1)));
                record.setImgWidth(width);
                record.setImgHeigh(heigh);
                record.setImgPath(imgFilePath);
                record.setDomainId(domainId);
                records.add(record);

                saveImgs.add(imgName);
            }
            if (imgMessages.size() > 0) {
                dirMessage.setTotalCount(totalCount);
                dirMessage.setFailCount(failCount);
                dirMessage.setSuccessCount(totalCount - failCount);
                dirMessage.setImageMessages(imgMessages);
                dirMessages.add(dirMessage);
            }
        }
        // 保存文件操作记录
        if (!BinaryUtils.isEmpty(resInfos)) {
            resourceSvc.saveSyncResourceInfo(resInfos);
        }
        // 保存入库
        if (records.size() > 0) {
            svc.saveOrUpdateBatch(records);
        }
        return this.writeSheetMessageToFile(dirMessages);
    }

    @Override
    public CcImage queryImageById(Long id) {
        CcImage img = svc.getById(id);
        if (img != null) {
            img.setImgPath(urlPath + img.getImgPath());
        }
        return img;
    }

    @Override
    public List<ImageCount> queryImageDirList(Long domainId, CCcCiClassDir cdt) {
        domainId = domainId == null ? BaseConst.DEFAULT_DOMAIN_ID : domainId;
        List<ImageCount> list = new ArrayList<ImageCount>();
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("domainId",domainId));
        if (cdt != null) {
            cdt.setDomainId(domainId);
            query = ESUtil.cdtToBuilder(cdt);
        } else {
            query.must(QueryBuilders.termsQuery("ciType", Arrays.asList(DirTypeEnum.IMAGE.getType(),
                    DirTypeEnum.IMAGE3D.getType(), DirTypeEnum.VEDIO.getType(),DirTypeEnum.DOCUMENT.getType())));
        }

        List<CcCiClassDir> dirs = dirSvc.getSortListByQuery(1, 1000, query, "createTime", true)
                .getData();
        //{dirId:该dir下图标数量}
        BoolQueryBuilder countQuery = QueryBuilders.boolQuery();
        countQuery.must(QueryBuilders.matchAllQuery());
        countQuery.must(QueryBuilders.termQuery("domainId", domainId));
        Map<String, Long> map = svc.groupByCountField("dirId", countQuery);
        //{dirId:[imgCountDto]}
        Map<Long, List<ImageCount>> dirByParentMap = new HashMap<>();
        for (CcCiClassDir dir : dirs) {
            //统计对象
            ImageCount ic = new ImageCount();
            ic.setDir(dir);
            Long count = map.get("" + dir.getId());
            count = count == null ? 0L : count;
            ic.setImageCount(count);
            if (dir.getParentId() == 0) {
                list.add(ic);
            }
            //填补一下dirByParentMap初始数据
            List<ImageCount> imageCounts = dirByParentMap.computeIfAbsent(dir.getParentId(), v -> new ArrayList<>());
            imageCounts.add(ic);
        }
        //层级拼接
        List<ImageCount> host = new ArrayList<>(list);
        while (true) {
            List<ImageCount> newHost = new ArrayList<>();
            for (ImageCount imageCount : host) {
                List<ImageCount> imageCounts = dirByParentMap.get(imageCount.getDir().getId());

                if (imageCounts != null && !(imageCount.getDir().getId() == 0L && imageCount.getDir().getParentId() == 0L)) {
                    imageCount.getChildren().addAll(imageCounts);
                    newHost.addAll(imageCounts);

                }
            }
            if (newHost.isEmpty()) {
                break;
            }
            host = newHost;
        }
        return list;
    }

    private BoolQueryBuilder searchBeanToQuery(ESSearchImageBean bean) {
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        if (bean != null) {
            if (bean.getDirId() != null) {
                query.must(QueryBuilders.termQuery("dirId", bean.getDirId()));
            }
            if (bean.getKeyword() != null && bean.getKeyword().length() > 0) {
                query.must(QueryBuilders.multiMatchQuery(bean.getKeyword(), "imgName").operator(Operator.AND)
                        .type(Type.PHRASE_PREFIX).lenient(true));
            }
            if (bean.getCdt() != null) {
                query.must(ESUtil.cdtToBuilder(bean.getCdt()));
            }
            if (bean.getImgFullNames() != null && bean.getImgFullNames().size() > 0) {
                Set<String> dirNames = new HashSet<>();
                Set<String> imgNames = new HashSet<>();
                bean.getImgFullNames().forEach(fullName -> {
                    String[] split = fullName.split("\\|");
                    if (split.length > 1) {
                        dirNames.add(split[0]);
                        imgNames.add(split[1]);
                    }
                });
                if (BinaryUtils.isEmpty(dirNames) || BinaryUtils.isEmpty(imgNames)) {
                    return null;
                }
                List<CcCiClassDir> dirs = dirSvc.getListByQuery(QueryBuilders.termsQuery("dirName.keyword", dirNames));
                if (BinaryUtils.isEmpty(dirs)) {
                    return null;
                }
                Set<Long> dirIds = dirs.stream().map(CcCiClassDir::getId).collect(Collectors.toSet());
                query.must(QueryBuilders.termsQuery("dirId", dirIds));
                if (BinaryUtils.isEmpty(bean.getImgNames())) {
                    bean.setImgNames(imgNames);
                }
                // 图标名称取交集，无相同名称直接返回空
                bean.getImgNames().retainAll(imgNames);
                if (BinaryUtils.isEmpty(bean.getImgNames())) {
                    return null;
                }
            }
            if (bean.getImgNames() != null && bean.getImgNames().size() > 0) {
                query.must(QueryBuilders.termsQuery("imgName.keyword", bean.getImgNames()));
            }
            if (bean.getImgPaths() != null && bean.getImgPaths().size() > 0) {
                query.must(QueryBuilders.termsQuery("imgPath.keyword", bean.getImgPaths()));
            }
            if (bean.getIgnoreDatas() != null && bean.getIgnoreDatas().size() > 0) {
                query.mustNot(QueryBuilders.termsQuery("id", bean.getIgnoreDatas()));
            }
        }
        return query;
    }

    @Override
    public Page<CcImage> queryImagePage(ESSearchImageBean bean) {
        if(BinaryUtils.isEmpty(bean.getDomainId())){
            bean.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
        }
        // BinaryUtils.checkEmpty(bean.getDirId(), "dirId");
        BoolQueryBuilder query = this.searchBeanToQuery(bean);
        query.must(QueryBuilders.termQuery("domainId",bean.getDomainId()));
        List<SortBuilder<?>> sorts = new LinkedList<>();
        sorts.add(SortBuilders.fieldSort("modifyTime").order(SortOrder.DESC));
        sorts.add(SortBuilders.fieldSort("id").order(SortOrder.ASC));
        Page<CcImage> page = svc.getSortListByQuery(bean.getPageNum(), bean.getPageSize(), query, sorts);
        List<CcImage> images = page.getData();
        for (CcImage img : images) {
            img.setImgPath(urlPath + img.getImgPath());
        }
        return page;
    }

    @Override
    public Page<CcImage> queryImageByPath(ESSearchImageBean bean) {
        Long domainId = bean.getDomainId() == null ? BaseConst.DEFAULT_DOMAIN_ID : bean.getDomainId();
        Set<String> imgPaths = new HashSet<String>();
        Assert.isTrue(!bean.getImgPaths().isEmpty(), "资源路径不能为空");

        if(!BinaryUtils.isEmpty(bean.getImgPaths())){
            Iterator<String> iterator = bean.getImgPaths().iterator();
            while(iterator.hasNext()){
                String imgPath=iterator.next();
                if(imgPath.startsWith(urlPath)){
                    imgPath=imgPath.replaceAll(urlPath,"");
                }
                imgPaths.add(imgPath);
            }
            bean.setImgPaths(imgPaths);

        }
        BoolQueryBuilder query = this.searchBeanToQuery(bean);
        query.must(QueryBuilders.termQuery("domainId",domainId));
        Page<CcImage> images = svc.getListByQuery(bean.getPageNum(), bean.getPageSize(), query);
        return images;

    }

    @Override
    public Boolean isShowDocumentAttribute() {
        return isShowDocumentAttribute;
    }

    @Override
    public List<CcImage> queryTopImage(SearchKeywordBean bean) {
        List<SysTopData> tops = topdataSvc.searTopData(3L);

        if (tops == null || tops.size() <= 0) {
            return new ArrayList<>();
        }

        Set<Long> imgIds = new LinkedHashSet<>();
        Map<Long, Integer> idScoreMap = new HashMap<>();
        int currentScore = imgIds.size();
        for (SysTopData top : tops) {
            imgIds.add(top.getTopDataId());
            idScoreMap.put(top.getTopDataId(), currentScore);
            currentScore--;
        }
        BoolQueryBuilder query = QueryBuilders.boolQuery();

        query.must(QueryBuilders.termsQuery("id", imgIds));
        if (bean.getDirId() != null) {
            query.must(QueryBuilders.termQuery("dirId", bean.getDirId()));
        }
        if (bean.getKeyword() != null && bean.getKeyword().length() > 0) {
            query.must(QueryBuilders.multiMatchQuery(bean.getKeyword(), "imgName").operator(Operator.AND)
                    .type(Type.PHRASE_PREFIX).lenient(true));
        }
        List<CcImage> topImgs = svc.getListByQuery(query);
        for (CcImage img : topImgs) {
            img.setImgPath(urlPath + img.getImgPath());
        }
        if (topImgs != null && topImgs.size() > 0) {
            Collections.sort(topImgs, new Comparator<CcImage>() {

                @Override
                public int compare(CcImage o1, CcImage o2) {
                    int o1Score = idScoreMap.get(o1.getId()) == null ? -1 : idScoreMap.get(o1.getId());
                    int o2Score = idScoreMap.get(o2.getId()) == null ? -1 : idScoreMap.get(o2.getId());
                    if (o1Score == o2Score) {
                        return 0;
                    } else if (o1Score > o2Score) {
                        return -1;
                    } else {
                        return 1;
                    }
                }
            });
        }
        return topImgs;
    }

    @Override
    public ImportDirMessage importImages(Long dirId, MultipartFile... files) {
        BinaryUtils.checkEmpty(dirId, "dirId");
        Assert.isTrue(files != null && files.length > 0, "上传资源不得为空");
        // 校验资源文件夹
        CcCiClassDir dir = dirSvc.getById(dirId);
        Assert.notNull(dir, "文件夹不存在");
        Long domainId = dir.getDomainId() == null ? BaseConst.DEFAULT_DOMAIN_ID : dir.getDomainId();
        DirTypeEnum dirType = DirTypeEnum.valueOf(dir.getCiType());
        Long dateTimeFolder = ESUtil.getNumberDate();
        ImportDirMessage res = ImportDirMessage.builder().dirId(dirId).totalCount(files.length).build();
        List<CcImage> saveInfos = new LinkedList<>();
        // 将文件排重
        Map<String, MultipartFile> fileStdNameContentMap = new LinkedHashMap<>();
        for (MultipartFile file : files) {
            String fileStdName = file.getOriginalFilename().substring(0, file.getOriginalFilename().lastIndexOf("."))
                    .toUpperCase();
            if (fileStdNameContentMap.get(fileStdName) == null) {
                fileStdNameContentMap.put(fileStdName, file);
            } else {
                res.setIgnoreCount(res.getIgnoreCount() + 1);
            }
        }
        // 查出数据库中已存在的,做同名替换
        List<CcImage> images = svc
                .getListByQuery(QueryBuilders.boolQuery().must(QueryBuilders.termQuery("dirId", dirId))
                        .must(QueryBuilders.termsQuery("imgName.stdkeyword", fileStdNameContentMap.keySet()))
                        .must(QueryBuilders.termQuery("domainId", domainId)));
        Map<String, CcImage> existStdNameImgMap = new HashMap<>();
        images.forEach(img -> existStdNameImgMap.put(img.getImgName().toUpperCase(), img));
        List<ESResource> resInfos = new ArrayList<>();
        fileStdNameContentMap.keySet().forEach(fileStdName -> {
            MultipartFile file = fileStdNameContentMap.get(fileStdName);
            if (dirType == DirTypeEnum.IMAGE) {
                if (!validFileSuffix(imgNamePattern, file.getOriginalFilename()) || file.getSize() > Math.pow(2, 20)) {
                    res.setFailCount(res.getFailCount() + 1);
                    return;
                }
            } else if (dirType == DirTypeEnum.VEDIO) {
                if (!validFileSuffix(vedioNamePattern, file.getOriginalFilename())
                        || file.getSize() > 10 * Math.pow(2, 20)) {
                    res.setFailCount(res.getFailCount() + 1);
                    return;
                }
            }else if (dirType == DirTypeEnum.DOCUMENT) {
                if (!validFileSuffix(documentNamePattern, file.getOriginalFilename())
                        || file.getSize() > 100 * Math.pow(2, 20)) {
                    res.setFailCount(res.getFailCount() + 1);
                    return;
                }

                Assert.isTrue(existStdNameImgMap.get(fileStdName) == null, "文档已存在");
            }
            try {
                String imgName = file.getOriginalFilename().substring(0, file.getOriginalFilename().lastIndexOf("."));
                String fileType = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf(".") + 1);
                CcImage img = (existStdNameImgMap.get(fileStdName) == null) ? new CcImage()
                        : existStdNameImgMap.get(fileStdName);
                imgName = img.getImgName() != null && !"".equals(img.getImgName()) ? img.getImgName() : imgName;
                String targetPath = "/" + dateTimeFolder + "/" + UUID.randomUUID() + "." + fileType;

                File targetFile = new File(this.localPath + targetPath);
                FileUtil.writeFile(targetFile, file.getBytes());
                if (dirType == DirTypeEnum.IMAGE) {
                    Map<String, Integer> imageSizeMap = getImageSize(targetFile);
                    if (imageSizeMap != null && !imageSizeMap.isEmpty()) {
                        img.setImgWidth(imageSizeMap.get("width"));
                        img.setImgHeigh(imageSizeMap.get("height"));
                    }
                }
                Integer sourceSize = Conver.to(file.getBytes().length, Integer.class);
                // 代表这个是替换操作，先删旧的
                if (img.getImgPath() != null && !"".equals(img.getImgPath())) {

                    FileUtil.delFileOrDir(img.getImgPath());
					resInfos.add(ESResource.builder().path(img.getImgPath()).publicUrl(syncHttpPath + img.getImgPath())
							.optionType(1).unzip(false).build());
                }
                String fullName = dir.getDirName() + "|" + imgName;
                img.setImgFullName(fullName);
                img.setImgName(imgName);
                img.setImgSize(sourceSize);
                img.setDirId(dirId);
                img.setDomainId(domainId);
                img.setImgGroup(dirType == DirTypeEnum.IMAGE ? 1 : (dirType == DirTypeEnum.VEDIO ? 4 : (dirType == DirTypeEnum.DOCUMENT ? 5 : 3)));
                img.setImgPath(targetPath);
                saveInfos.add(img);
                res.setSuccessCount(res.getSuccessCount() + 1);
				// 记录资源操作信息
//				resInfos.add(ESResource.builder().path(targetPath).publicUrl(syncHttpPath + targetPath).optionType(0)
//						.unzip(false).build());
            } catch (Exception e) {
                log.error("操作图标异常", e);
                res.setFailCount(res.getFailCount() + 1);
            }
        });
        if (!BinaryUtils.isEmpty(resInfos)) {
            resourceSvc.saveSyncResourceInfo(resInfos);
        }
        if (saveInfos != null && saveInfos.size() > 0) {
            svc.saveOrUpdateBatch(saveInfos);
        }
        return res;
    }

    @Override
    public boolean importImage(Long dirId, MultipartFile file) {
        ImportDirMessage message = this.importImages(dirId, file);
        Assert.isTrue(BinaryUtils.isEmpty(message.getImageMessages()), message.getImageMessages().get(0).getMessage());
        return message.getFailCount() == 0 ? true : false;
    }

    @Override
    public ImportDirMessage import3DZipImage(MultipartFile file, Long dirId, Map<ZipEntry, byte[]> zipEntriesMap, boolean isCover) {
        Assert.notNull(dirId, "X_PARAM_NOT_NULL${name:dirId}");
        CcCiClassDir dir = dirSvc.getById(dirId);
        Long domainId = dir.getDomainId() == null ? BaseConst.DEFAULT_DOMAIN_ID : dir.getDomainId();
        Assert.notNull(dir, "文件夹不存在");
        String dirName3D = dir.getDirName();
        //
        Map<String, String> imageModelMap = new HashMap<>();
        // 压缩包内图片和文件夹元素操作集合
        Map<String, Map<ZipEntry, byte[]>> imageEntryMap = new HashMap<>();
        if (!BinaryUtils.isEmpty(file)) {
            MultipartFileResource imageSource = new MultipartFileResource(file);
            // 验证zip
            this.validFileContentTypeIsZip(file);
            ZipInputStream zipIs = new ZipInputStream(imageSource.getInputStream(), Charset.forName("gbk"));
            // 将zip内对象遍历一次,组装map
            while (true) {
                ZipEntry zipEntry = null;
                try {
                    zipEntry = zipIs.getNextEntry();
                } catch (Exception e) {
                    log.error("读取导入图标文件异常,终止读取", e);
                    throw new RuntimeException("读取导入图标文件异常，请检查zip文件");
                }
                if (zipEntry == null) {
                    break;
                }
                String entryName = zipEntry.getName();
                if (BinaryUtils.isEmpty(entryName)) {
                    continue;
                }
                if (validFileSuffix(imgNamePattern, entryName) || entryName.endsWith(".zip")) {
                    String imgName = entryName.substring(0, entryName.lastIndexOf("."));
                    if (!imageEntryMap.containsKey(imgName)) {
                        imageEntryMap.put(imgName, new HashMap<>());
                    }
                    imageEntryMap.get(imgName).put(zipEntry, FileUtil.readStream(zipIs, false));
                } else if ("product_lib.json".equals(entryName)) {
                    String modelKey = LanguageResolver.trans("UINO_BS_3D_IMAGE_MODEL");
                    String modelTypeKey = LanguageResolver.trans("UINO_BS_3D_IMAGE_MODEL_TYPE");
                    byte[] readStream = FileUtil.readStream(zipIs, false);
                    Map productMap = JSON.parseObject(new String(readStream), Map.class);
                    Iterator it = productMap.values().iterator();
                    while (it.hasNext()) {
                        Map<String, String> next = (Map<String, String>) it.next();
                        String model = next.get(modelKey);
                        String modelType = next.get(modelTypeKey);
                        if (!BinaryUtils.isEmpty(model) && !BinaryUtils.isEmpty(modelType)) {
                            imageModelMap.put(model, modelType);
                        }
                    }
                }
            }
        } else if (!BinaryUtils.isEmpty(zipEntriesMap)) {
            Iterator<ZipEntry> it = zipEntriesMap.keySet().iterator();
            while (it.hasNext()) {
                ZipEntry zipEntry = it.next();
                String entryName = zipEntry.getName();
                if (BinaryUtils.isEmpty(entryName)) {
                    continue;
                }
                String imgName = entryName.substring(0, entryName.lastIndexOf("."));
                if (!imageEntryMap.containsKey(imgName)) {
                    imageEntryMap.put(imgName, new HashMap<>());
                }
                if (validFileSuffix(imgNamePattern, entryName) || entryName.endsWith(".zip")) {
                    imageEntryMap.get(imgName).put(zipEntry, zipEntriesMap.get(zipEntry));
                } else if ("product_lib.json".equals(entryName)) {
                    String modelKey = LanguageResolver.trans("UINO_BS_3D_IMAGE_MODEL");
                    String modelTypeKey = LanguageResolver.trans("UINO_BS_3D_IMAGE_MODEL_TYPE");
                    byte[] readStream = zipEntriesMap.get(zipEntry);
                    Map productMap = JSON.parseObject(new String(readStream), Map.class);
                    Iterator iterator = productMap.values().iterator();
                    while (iterator.hasNext()) {
                        Map<String, String> next = (Map<String, String>) iterator.next();
                        String model = next.get(modelKey);
                        String modelType = next.get(modelTypeKey);
                        if (!BinaryUtils.isEmpty(model) && !BinaryUtils.isEmpty(modelType)) {
                            imageModelMap.put(model, modelType);
                        }
                    }
                }
            }
        } else {
            return null;
        }
        Assert.isTrue(!BinaryUtils.isEmpty(imageModelMap), "缺少资源映射文件product_lib.json");
        Integer totalCount = 0;
        Integer failCount = 0;
        // 保存当前目录上传的信息
        ImportDirMessage dirMessage = ImportDirMessage.builder().dirName(dirName3D).build();
        BoolQueryBuilder img_query = QueryBuilders.boolQuery();
        img_query.must(QueryBuilders.termQuery("domainId", domainId));
        img_query.must(QueryBuilders.termQuery("dirId", dirId));
        // 获取该文件夹下已有的图片，重复替换
        List<CcImage> images = svc.getListByQuery(1, 9999, img_query).getData();
        Map<String, CcImage> imgMap = BinaryUtils.toObjectMap(images, "imgName");
        // 创造出该虚拟文件夹下操作对应的文件系统里的文件夹
        Long dateTimeFolder = ESUtil.getNumberDate();
        File destFolder = new File(localPath + "/" + dateTimeFolder);
        if (!destFolder.exists()) {
            destFolder.mkdirs();
        }
        // 遍历操作对象
        List<CcImage> records = new LinkedList<>();
        List<ESResource> resInfos = new ArrayList<>();
        List<String> saveImgs = new ArrayList<String>();
        List<ImportImageMessage> imageMessages = new ArrayList<>();
        for (String imgFileName : imageEntryMap.keySet()) {
            String imgName = imgFileName;
            totalCount++;
            Map<ZipEntry, byte[]> entryMap = imageEntryMap.get(imgFileName);
            byte[] imgByte = null;
            byte[] zipByte = null;
            String imgFullName = "";
            String imgType = null;
            Iterator<ZipEntry> it = entryMap.keySet().iterator();
            while (it.hasNext()) {
                ZipEntry entry = it.next();
                String[] split = entry.getName().split("/");
                if (validFileSuffix(imgNamePattern, entry.getName())) {
                    imgByte = entryMap.get(entry);
                    imgFullName = split[split.length - 1];
                    imgType = imgFullName.substring(imgFullName.lastIndexOf(".") + 1);
                } else if (entry.getName().endsWith(".zip")) {
                    zipByte = entryMap.get(entry);
                }
            }

            ImportImageMessage imageMessage = ImportImageMessage.builder().imageName(imgFullName).build();
            if (!imageModelMap.containsKey(imgFileName)) {
                failCount++;
                imageMessage.setMessage("[" + imgFileName + "]图标资源异常");
                imageMessages.add(imageMessage);
                continue;
            } else {
                imgName = imageModelMap.get(imgFileName);
            }
            // 判断资源是否缺失
            if (imgByte == null || zipByte == null) {
                failCount++;
                imageMessage.setMessage("[" + imgFileName + "]图标资源异常");
                imageMessages.add(imageMessage);
                continue;
            }
            // 图标重复略过
            if (saveImgs.contains(imgName)) {
                failCount++;
                imageMessage.setMessage("[" + imgFileName + "]图标重复");
                imageMessages.add(imageMessage);
                continue;
            }
            // 不符合文件名校验的略过
            if (!validFileSuffix(imgNamePattern, imgFullName)) {
                failCount++;
                imageMessage.setMessage("不支持的图标文件" + imgFullName + "]");
                imageMessages.add(imageMessage);
                continue;
            }
            // 图片大小超过1M的略过
            if (imgByte.length > Math.pow(2, 20)) {
                failCount++;
                imageMessage.setMessage("[" + imgFileName + "]图片大小超过1M");
                imageMessages.add(imageMessage);
                continue;
            }
            // 将读取到的图片写入文件系统，重复图标替换
            // String imgUUName = UUID.randomUUID().toString();
            String imgFilePath = "/" + dateTimeFolder + "/" + dir.getId() + "/" + imgFullName;
            int optionType = 0;
            if (imgMap.containsKey(imgName)) {
                imgFilePath = imgMap.get(imgName).getImgPath();
                optionType = 2;
                String fileType = imgFilePath.substring(imgFilePath.lastIndexOf(".") + 1);
                if (!imgType.equals(fileType)) {
                    // 后缀名更新，操作记录先删再存
                    new File(localPath + imgFilePath).delete();

                    rsmUtils.deleteRsm(imgFilePath);

                    resInfos.add(ESResource.builder().path(imgFilePath).publicUrl(syncHttpPath + imgFilePath).optionType(1)
                            .unzip(false).build());
                    imgFilePath = imgMap.get(imgName).getImgPath().replace(fileType, imgType);
                    optionType = 0;
                }
            }
            File destFile = new File(localPath + imgFilePath);
            if (!destFile.getParentFile().exists()) {
                destFile.getParentFile().mkdirs();
            }
            try {
                String imgDirPath = imgFilePath.substring(0, imgFilePath.lastIndexOf("."));
                String imgZipPath = imgDirPath + ".zip";
                // 写图标
                FileUtils.writeByteArrayToFile(destFile, imgByte);

                //将图标写入到对象存储
                rsmUtils.uploadRsmFromFile(destFile);

                // 记录资源操作信息
                resInfos.add(ESResource.builder().path(imgFilePath).publicUrl(syncHttpPath + imgFilePath)
                        .optionType(optionType).unzip(false).build());
                // 删除3D文件夹
                File imgDirFile = new File(localPath + imgDirPath);
                if (imgDirFile.exists()) {
                    FileUtils.deleteDirectory(imgDirFile);
                }
                //删除对象存储中的文件
                rsmUtils.deleteRsmByFolder(imgDirFile);

                // 写3D文件夹
                File destZip = new File(localPath + imgZipPath);
                FileUtils.writeByteArrayToFile(destZip, zipByte);
                rsmUtils.uploadRsm(rsmUtils.processObjectKey(imgZipPath),destZip);

                File destDir = new File(destFile.getParentFile(), imgFileName);
                Compression.uncompressZip(destZip, destDir);
                rsmUtils.uploadRsmFromFolder(destDir);


                // destZip.delete();
                resInfos.add(ESResource.builder().path(imgZipPath)
                        .publicUrl(syncHttpPath + imgZipPath)
                        .optionType(optionType).unzip(false).build());
                if (destDir.exists()) {
                    DirectoryStream<Path> directory = Files.newDirectoryStream(destDir.toPath());
                    for (Path path : directory) {
                        resInfos.add(
                                ESResource.builder().path(path.toString().replace("\\", "/").replace(localPath, ""))
                                        .publicUrl(path.toString().replace("\\", "/").replace(localPath, syncHttpPath))
                                        .optionType(2).unzip(false).build());
                    }
                    directory.close();
                }
            } catch (Exception e) {
                log.error("写入图片异常", e);
                throw new RuntimeException("写入图片异常");
            }
            CcImage record = imgMap.get(imgName);
            if (record == null) {
                record = new CcImage();
            }
            Map<String, Integer> imageSizeMap = getImageSize(destFile);
            if (imageSizeMap != null && !imageSizeMap.isEmpty()) {
                record.setImgWidth(imageSizeMap.get("width"));
                record.setImgHeigh(imageSizeMap.get("height"));
            }
            record.setImgFullName(imgName);
            record.setImgName(imgName);
            record.setImgSize(Conver.to(destFile.length(), Integer.class));
            record.setDirId(dirId);
            record.setImgGroup(3);
            record.setImgPath(imgFilePath);
            record.setDomainId(domainId);
            records.add(record);
            saveImgs.add(imgName);
        }
        if (!BinaryUtils.isEmpty(resInfos)) {
            resourceSvc.saveSyncResourceInfo(resInfos);
        }
        // 保存入库
        if (records.size() > 0) {
            svc.saveOrUpdateBatch(records);
        }
        dirMessage.setDirId(dirId);
        dirMessage.setDirName(dirName3D);
        dirMessage.setTotalCount(totalCount);
        dirMessage.setSuccessCount(totalCount - failCount);
        dirMessage.setFailCount(failCount);
//        if (!BinaryUtils.isEmpty(imageMessages)) {
        dirMessage.setImageMessages(imageMessages);
//        }
        Assert.isTrue(dirMessage.getFailCount() <= 0, "部分图标格式异常");
        return dirMessage;
    }

    @Override
    public ResponseEntity<byte[]> exportImageZipByDirIds(Set<Long> dirIds) {
        List<CcCiClassDir> dirs = new ArrayList<>();
        if (BinaryUtils.isEmpty(dirIds)) {
            // 默认导出所有2D图标
            dirs = dirSvc.getListByQuery(QueryBuilders.termQuery("dirType", DirTypeEnum.IMAGE.getType()));
        } else {
            dirs = dirSvc.getListByQuery(QueryBuilders.termsQuery("id", dirIds));
        }
        Assert.notEmpty(dirs, "X_PARAM_NOT_NULL${name:dirs}");

        // 导出zip的名称
        String zipFileName = "图标.zip";
        if (dirs.size() == 1) {
            zipFileName = dirs.get(0).getDirName() + ".zip";
        }

        //查询结构，拼接路径
        Map<Long, String> dirPath = new HashMap<>();
        Set<String> dirName3DSet = new HashSet<>();
        dirs.forEach(ccCiClassDir -> {
            dirPath.put(ccCiClassDir.getId(), ccCiClassDir.getDirName());
            if (ccCiClassDir.getCiType().intValue() == DirTypeEnum.IMAGE3D.getType()) {
                dirName3DSet.add(ccCiClassDir.getDirName());
            }
        });
        while (true) {
            List<CcCiClassDir> childDirs = dirSvc.getListByQuery(QueryBuilders.termsQuery("parentId", dirIds));
            if (childDirs.isEmpty()) {
                break;
            }
            Set<Long> newIds = new HashSet<>();
            for (CcCiClassDir ccCiClassDir : childDirs) {
                dirs.add(ccCiClassDir);
                newIds.add(ccCiClassDir.getId());
                dirPath.put(ccCiClassDir.getId(),
                        dirPath.get(ccCiClassDir.getParentId()) + "/" + ccCiClassDir.getDirName());
                if (ccCiClassDir.getCiType().intValue() == DirTypeEnum.IMAGE3D.getType()) {
                    dirName3DSet.add(ccCiClassDir.getDirName());
                }

            }
            dirIds = newIds;
        }
        // //查询3D目录及所有子目录
        // List<CcCiClassDir> ccCiClassDirs =
        // dirSvc.getListByQuery(QueryBuilders.termQuery("dirName.keyword", dirName3D));
        // Set<String> dirName3DSet = new HashSet<>();
        // Long dir3DRootId = null;
        // if (!ccCiClassDirs.isEmpty()) {
        // dir3DRootId = ccCiClassDirs.get(0).getId();
        // dirName3DSet.add(ccCiClassDirs.get(0).getDirName());
        // }
        // if (dir3DRootId != null) {
        // List<Long> dir3DIds = Arrays.asList(dir3DRootId);
        // while (true) {
        // List<Long> newDir3DIds = new ArrayList<>();
        // List<CcCiClassDir> child3DDirs =
        // dirSvc.getListByQuery(QueryBuilders.termsQuery("parentId", dir3DIds));
        // if (child3DDirs.isEmpty()) {
        // break;
        // }
        // child3DDirs.forEach(ccCiClassDir -> {
        // dirName3DSet.add(ccCiClassDir.getDirName());
        // newDir3DIds.add(ccCiClassDir.getId());
        // });
        // dir3DIds = newDir3DIds;
        // }
        // }


        // 本次导出是否一张图片没有
        boolean hasImg = false;
        // zip相关操作流
        ZipOutputStream zipOs = null;
        ByteArrayOutputStream zipByteOs = new ByteArrayOutputStream();
        zipOs = new ZipOutputStream(zipByteOs);
        for (CcCiClassDir dir : dirs) {
            // 文件夹名称
            String dirName = dir.getDirName();
            TermQueryBuilder query = QueryBuilders.termQuery("dirId", dir.getId());
            long count = svc.countByCondition(query);
            List<CcImage> exportImgs = svc.getListByQuery(1, new BigDecimal(count).intValue(), query).getData();
            if (exportImgs == null || exportImgs.size() <= 0) {
                // 文件夹下无图标则忽略
                continue;
            }
            hasImg = true;
            boolean is3D = dirName3DSet.contains(dirName);
            try {
                if (is3D) {
                    if (dirs.size() == 1) {
                        // 导出单个3D文件夹时，不创建父文件夹
                        this.export3DImage(exportImgs, null, zipOs);
                    } else {
                        this.export3DImage(exportImgs, dirPath.get(dir.getId()), zipOs);
                    }
                } else {
                    for (CcImage img : exportImgs) {

                        // 将对象存储中的图片文件更新到本地文件中
                        rsmUtils.downloadRsmAndUpdateLocalRsm(img.getImgPath());

                        String imgPath = localPath + img.getImgPath();
                        String fileType = imgPath.substring(imgPath.lastIndexOf("."));
                        String imgName = img.getImgName() + fileType;
                        InputStream imgInputStream = new FileInputStream(imgPath);
                        zipOs.putNextEntry(new ZipEntry(dirPath.get(dir.getId()) + "/" + imgName));
                        IOUtils.copy(imgInputStream, zipOs);
                        zipOs.closeEntry();
                        imgInputStream.close();
                    }
                }
            } catch (Exception e) {
                log.error("导出imgs-zip异常", e);
            }
        }
        // 数据写入完毕，进行流关闭等操作
        try {
            if (zipOs != null) {
                zipOs.flush();
                zipOs.close();
                zipByteOs.close();
            }
        } catch (Exception e) {
            log.error("关闭流异常", e);
        }
        Assert.isTrue(hasImg, "导出图标为空");
        // 导出文件
        ResponseEntity<byte[]> resource = null;
        try {
            HttpHeaders headers = new HttpHeaders();
            byte[] bytes = zipByteOs.toByteArray();
            zipFileName = URLEncoder.encode(zipFileName, "UTF-8");
            // 解决文件名空格变+问题
            headers.add("Content-Disposition",
                    "attachment;filename=" + zipFileName.replace("+", "%20").replace("%40", "@"));
            resource = new ResponseEntity<byte[]>(bytes, headers, HttpStatus.OK);
        } catch (Exception e) {
            throw new MessageException(e.getMessage());
        } finally {
            if (zipByteOs != null) {
                try {
                    zipByteOs.close();
                } catch (IOException e) {
                    throw new MessageException(e.getMessage());
                }
            }
        }
        return resource;
    }

    @Override
    public boolean replaceImage(Long imageId, MultipartFile file) {
        boolean flag = true;
        BinaryUtils.checkEmpty(imageId, "imgId");
        CcImage image = svc.getById(imageId);
        Long domainId = image.getDomainId() == null ? BaseConst.DEFAULT_DOMAIN_ID : image.getDomainId();
        Assert.notNull(image, "图标不存在");
        DirTypeEnum dirType = image.getImgGroup().intValue() == 1 ? DirTypeEnum.IMAGE : DirTypeEnum.VEDIO;
        // List<CcImage> images = svc.getListByQuery(QueryBuilders.termQuery("id",
        // imageId));
        if (dirType == DirTypeEnum.IMAGE) {
            Assert.isTrue(validFileSuffix(imgNamePattern, file.getOriginalFilename()) && file.getSize() < Math.pow(2, 20),
                    "上传失败");
        } else {
            Assert.isTrue(validFileSuffix(vedioNamePattern, file.getOriginalFilename())
                            && file.getSize() < 10 * Math.pow(2, 20),
                    "上传失败");
        }
        String imgFilePath = image.getImgPath();
        String imgType = imgFilePath.substring(imgFilePath.lastIndexOf(".") + 1);
        String fileType = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf(".") + 1);
        int optionType = 2;
        if (!imgType.equals(fileType)) {
            // 后缀名更新，操作记录先删再存
            new File(localPath + imgFilePath).delete();

            // 删除对象存储中的文件
            rsmUtils.deleteRsm(imgFilePath);

            resourceSvc.saveSyncResourceInfo(imgFilePath, syncHttpPath + imgFilePath, false, 1);
            imgFilePath = imgFilePath.replace(imgType, fileType);
            optionType = 0;
        }
        File destFile = new File(localPath + imgFilePath);
        try {
            Integer sourceSize = Conver.to(file.getBytes().length, Integer.class);

            file.transferTo(destFile);

            // 将文件写入对象存储
            rsmUtils.uploadRsmFromFile(destFile);

            // 记录资源操作信息
            resourceSvc.saveSyncResourceInfo(imgFilePath, syncHttpPath + imgFilePath, false, optionType);
            CcImage record = new CcImage();
            record.setImgSize(sourceSize);
            record.setId(imageId);
            record.setImgPath(imgFilePath);
            record.setDomainId(domainId);
            if (dirType == DirTypeEnum.IMAGE) {
                Map<String, Integer> imageSizeMap = getImageSize(destFile);
                if (imageSizeMap != null && !imageSizeMap.isEmpty()) {
                    record.setImgWidth(imageSizeMap.get("width"));
                    record.setImgHeigh(imageSizeMap.get("height"));
                }
            }
            svc.saveOrUpdate(record);
        } catch (IOException e) {
            flag = false;
        }
        return flag;
    }

    @Override
    public boolean replace3DImage(Long imageId, MultipartFile file) {
        BinaryUtils.checkEmpty(imageId, "imgId");
        boolean flag = false;
        CcImage image = svc.getById(imageId);
        Long domainId = image.getDomainId() == null ? BaseConst.DEFAULT_DOMAIN_ID : image.getDomainId();
        image.setDomainId(domainId);
        Assert.isTrue(image != null, "BS_MNAME_IMAGE#{name:BS_MVTYPE_NOT_EXIST}");
        // 验证zip
        this.validFileContentTypeIsZip(file);
        try {
            MultipartFileResource imageSource = new MultipartFileResource(file);
            ZipInputStream zipIs = new ZipInputStream(imageSource.getInputStream(), Charset.forName("gbk"));
            // 将zip内对象遍历一次,组装map
            String imgFileName = null;
            String imgType = null;
            byte[] imgByte = null;
            byte[] zipByte = null;
            Map<String, byte[]> imageEntryMap = new HashMap<>();
            while (true) {
                ZipEntry zipEntry = null;
                try {
                    zipEntry = zipIs.getNextEntry();
                } catch (Exception e) {
                    log.error("读取导入图标文件异常,终止读取", e);
                    throw new RuntimeException("读取导入图标文件异常，请检查zip文件");
                }
                if (zipEntry == null) {
                    break;
                }
                if ("product_lib.json".equals(zipEntry.getName())) {
                    String modelKey = LanguageResolver.trans("UINO_BS_3D_IMAGE_MODEL");
                    String modelTypeKey = LanguageResolver.trans("UINO_BS_3D_IMAGE_MODEL_TYPE");
                    byte[] readStream = FileUtil.readStream(zipIs, false);
                    Map productMap = JSON.parseObject(new String(readStream), Map.class);
                    Iterator it = productMap.values().iterator();
                    while (it.hasNext()) {
                        Map<String, String> next = (Map<String, String>) it.next();
                        String model = next.get(modelKey);
                        String modelType = next.get(modelTypeKey);
                        if (!BinaryUtils.isEmpty(model) && !BinaryUtils.isEmpty(modelType)
                                && modelType.equals(image.getImgName())) {
                            imgFileName = model;
                        }
                    }
                }
                if (validFileSuffix(imgNamePattern, zipEntry.getName()) || zipEntry.getName().endsWith(".zip")) {
                    imageEntryMap.put(zipEntry.getName(), FileUtil.readStream(zipIs, false));
                }
            }
            Assert.isTrue(!BinaryUtils.isEmpty(imgFileName), "未找到格式正确的【" + image.getImgName() + "】图标");
            Iterator<String> imgIterator = imageEntryMap.keySet().iterator();
            while (imgIterator.hasNext()) {
                String fileName = imgIterator.next();
                String subFileName = fileName.substring(0, fileName.lastIndexOf("."));
                if (validFileSuffix(imgNamePattern, fileName) && imgFileName.equals(subFileName)) {
                    imgByte = imageEntryMap.get(fileName);
                    zipByte = imageEntryMap.get(imgFileName + ".zip");
                    imgType = fileName.substring(fileName.lastIndexOf(".") + 1);
                }
            }
            if (!BinaryUtils.isEmpty(imgByte) && !BinaryUtils.isEmpty(zipByte)) {
                List<ESResource> resInfos = new ArrayList<>();
                String imgFilePath = image.getImgPath();
                String fileType = imgFilePath.substring(imgFilePath.lastIndexOf(".") + 1);
                int optionType = 2;
                if (!imgType.equals(fileType)) {
                    // 后缀名更新，操作记录先删再存
                    new File(localPath + imgFilePath).delete();

                    rsmUtils.deleteRsm(imgFilePath);

                    resInfos.add(ESResource.builder().path(imgFilePath).publicUrl(syncHttpPath + imgFilePath).optionType(1)
                            .unzip(false).build());
                    imgFilePath = imgFilePath.replace(fileType, imgType);
                    optionType = 0;
                }
                File destFile = new File(localPath + imgFilePath);

                FileUtils.writeByteArrayToFile(destFile, imgByte);
                rsmUtils.uploadRsmFromFile(destFile);

                // 写入3D资源
                File destZip = new File(localPath + imgFilePath.substring(0, imgFilePath.lastIndexOf(".")) + ".zip");

                FileUtils.writeByteArrayToFile(destZip, zipByte);
                rsmUtils.uploadRsmFromFile(destZip);

                File destDir = new File(destFile.getParentFile(),
                        imgFilePath.substring(imgFilePath.lastIndexOf("/") + 1, imgFilePath.lastIndexOf(".")));
                Compression.uncompressZip(destZip, destDir);

                rsmUtils.uploadRsmFromFolder(destDir);

                // destZip.delete();
                Map<String, Integer> imageSizeMap = getImageSize(destFile);
                if (imageSizeMap != null && !imageSizeMap.isEmpty()) {
                    if (!BinaryUtils.isEmpty(imageSizeMap.get("width"))) {
                        image.setImgWidth(imageSizeMap.get("width"));
                    }
                    if (!BinaryUtils.isEmpty(imageSizeMap.get("height"))) {
                        image.setImgHeigh(imageSizeMap.get("height"));
                    }
                }
                image.setImgSize(Conver.to(file.getBytes().length, Integer.class));
                image.setId(imageId);
                image.setImgPath(imgFilePath);
                svc.saveOrUpdate(image);
                flag = true;
                // 记录资源操作信息
                resInfos.add(ESResource.builder().path(imgFilePath).publicUrl(syncHttpPath + imgFilePath)
                        .optionType(optionType).unzip(false).build());
                if (destDir.exists()) {
                    DirectoryStream<Path> directory = Files.newDirectoryStream(destDir.toPath());
                    for (Path path : directory) {
                        resInfos.add(
                                ESResource.builder().path(path.toString().replace("\\", "/").replace(localPath, ""))
                                        .publicUrl(path.toString().replace("\\", "/").replace(localPath, syncHttpPath))
                                        .optionType(2).unzip(false).build());
                    }
                    directory.close();
                }
                if (!BinaryUtils.isEmpty(resInfos)) {
                    resourceSvc.saveSyncResourceInfo(resInfos);
                }
            }
            Assert.isTrue(flag, "未找到格式正确的【" + image.getImgName() + "】图标");
        } catch (IOException e) {
            flag = false;
            log.info("图标替换失败");
        }
        return flag;
    }

    @Override
    public boolean deleteImage(CcImage image) {
        Long imgId = image.getId();
        Long domainId = image.getDomainId() == null ? BaseConst.DEFAULT_DOMAIN_ID : image.getDomainId();

        Assert.notNull(imgId, "X_PARAM_NOT_NULL${name:imgId}");
        CcImage img = svc.getById(imgId);
        File file = new File(localPath + img.getImgPath());

        file.delete();
        rsmUtils.deleteRsm(img.getImgPath());

        // 记录文件操作
        resourceSvc.saveSyncResourceInfo(img.getImgPath(), syncHttpPath + img.getImgPath(), false, 1);
        // 删除图标，将分类图标改为默认
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("domainId", domainId));
        query.must(QueryBuilders.termQuery("icon.keyword", img.getImgPath()));
        esClassSvc.updateByQuery(query,
                "ctx._source.icon='" + esClassSvc.getDefaultIcon() + "';ctx._source.shape='"+"'", true);
        return svc.deleteById(imgId) == 1 ? true : false;
    }

    @Override
    public boolean delete3DImage(CcImage image) {
        Long domainId = image.getDomainId() == null ? BaseConst.DEFAULT_DOMAIN_ID : image.getDomainId();
        Assert.notNull(image.getId(), "X_PARAM_NOT_NULL${name:imgId}");
        Assert.notNull(image.getImgPath(), "X_PARAM_NOT_NULL${name:imgPath}");
        String imgPath = image.getImgPath();
        File file = new File(imgPath.replaceAll(urlPath, localPath));

        file.delete();
        rsmUtils.deleteRsm(imgPath.replaceAll(urlPath, localPath));

        List<ESResource> resInfos = new ArrayList<>();
        // 记录文件操作
        resInfos.add(ESResource.builder().path(imgPath.replace(urlPath, "")).publicUrl(image.getImgPath()).optionType(1)
                .unzip(false).build());
        imgPath = imgPath.replace(urlPath, localPath);
        File dirFile = new File(imgPath.substring(0, imgPath.lastIndexOf(".")));
        try {
            if (dirFile.exists()) {
                DirectoryStream<Path> directory = Files.newDirectoryStream(dirFile.toPath());
                for (Path path : directory) {
                    resInfos.add(ESResource.builder().path(path.toString().replace("\\", "/").replace(localPath, ""))
                            .publicUrl(path.toString().replace("\\", "/").replace(localPath, syncHttpPath)).optionType(1)
                            .unzip(false).build());
                }
                directory.close();

                FileUtils.deleteDirectory(dirFile);
                rsmUtils.deleteRsm(imgPath.substring(0, imgPath.lastIndexOf(".")) + "/");
            }
        } catch (IOException e) {
            log.info("3D文件夹删除失败");
        }
        if (!BinaryUtils.isEmpty(resInfos)) {
            resourceSvc.saveSyncResourceInfo(resInfos);
        }
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("domainId", domainId));
        query.must(QueryBuilders.termQuery("rltImgId1", image.getId()));
        svc.updateByQuery(query, "ctx._source.rltImgId1=\"\"", true);
        return svc.deleteById(image.getId()) == 1 ? true : false;
    }

    @Override
    public boolean deleteDirImage(Long dirId) {
        Assert.notNull(dirId, "X_PARAM_NOT_NULL${name:dirId}");
        if (dirId.longValue() == 1) {
            throw MessageException.i18n("BS_MNAME_IMAGE_DELETE_3D_NOT_ALLOW");
        }

        //获取所有子目录id
        List<Long> dirIds = new ArrayList<>();
        dirIds.add(dirId);
        List<Long> hostDirIds = new ArrayList<>();
        hostDirIds.add(dirId);
        while (true) {
            List<CcCiClassDir> childList = dirSvc.getListByQuery(QueryBuilders.termsQuery("parentId", hostDirIds));
            if (!childList.isEmpty()) {
                List<Long> newHostDirIds = new ArrayList<>();
                childList.forEach(ccCiClassDir -> {
                    dirIds.add(ccCiClassDir.getId());
                    newHostDirIds.add(ccCiClassDir.getId());
                });
                hostDirIds = newHostDirIds;
            } else {
                break;
            }
        }


        TermsQueryBuilder query = QueryBuilders.termsQuery("dirId", dirIds);
        long count = svc.countByCondition(query);
        List<CcImage> images = svc.getListByQuery(1, new BigDecimal(count).intValue(), query).getData();
        List<ESResource> resInfos = new ArrayList<>();
        List<String> imgPaths = new ArrayList<>();
        images.forEach(img -> {
            imgPaths.add(img.getImgPath());
            File file = new File(localPath + img.getImgPath());
            file.delete();
            rsmUtils.deleteRsm(img.getImgPath());
            // 记录文件操作
            resInfos.add(ESResource.builder().path(img.getImgPath()).publicUrl(syncHttpPath + img.getImgPath()).optionType(1)
                    .unzip(false).build());
        });
        // 删除图标，将分类图标改为默认
        if (!BinaryUtils.isEmpty(imgPaths)) {
            esClassSvc.updateByQuery(QueryBuilders.termsQuery("icon.keyword", imgPaths),
                    "ctx._source.icon='" + esClassSvc.getDefaultIcon() + "'", true);
        }
        if (!BinaryUtils.isEmpty(resInfos)) {
            resourceSvc.saveSyncResourceInfo(resInfos);
        }
        svc.deleteByQuery(QueryBuilders.termsQuery("dirId", dirIds), true);
        return dirSvc.deleteByIds(dirIds) == 1;
    }

    @Override
    public Long updateImageRlt(CcImage image) {
        Long imgId = image.getId();
        Long rltImgId1 = image.getRltImgId1();
        Long domainId = image.getDomainId() == null ? BaseConst.DEFAULT_DOMAIN_ID : image.getDomainId();
        image.setDomainId(domainId);
        Assert.notNull(imgId, "X_PARAM_NOT_NULL${name:imgId}");
        if (BinaryUtils.isEmpty(rltImgId1)) {
            return imgId;
        }
        // 验证图标是否存在
        Set<Long> idSets = new HashSet<Long>();
        idSets.add(imgId);
        idSets.add(rltImgId1);
        List<CcImage> images = svc.getListByQuery(QueryBuilders.termsQuery("id", idSets));
        Map<Long, CcImage> imgMap = BinaryUtils.toObjectMap(images, "id");
        if (!imgMap.containsKey(imgId)) {
            throw new MessageException(
                    "BS_VERIFY_ERROR#{name:BS_MNAME_IMAGE}#{type:BS_MVTYPE_NOT_EXIST}${value:" + imgId + "}");
        }
        CcImage img = imgMap.get(imgId);
        if (!imgMap.containsKey(rltImgId1)) {
            throw new MessageException(
                    "BS_VERIFY_ERROR#{name:BS_MNAME_IMAGE}#{type:BS_MVTYPE_NOT_EXIST}${value:" + rltImgId1 + "}");
        }
        img.setRltImgId1(rltImgId1);
        return svc.saveOrUpdate(img);
    }

    /**
     * 导出3D图标zip
     *
     * @param exportImgs
     * @param zipOs
     * @return
     */
    public void export3DImage(List<CcImage> exportImgs, String path, ZipOutputStream zipOs) {
        path = BinaryUtils.isEmpty(path) ? "" : path + "/";
        String modelKey = LanguageResolver.trans("UINO_BS_3D_IMAGE_MODEL");
        String modelTypeKey = LanguageResolver.trans("UINO_BS_3D_IMAGE_MODEL_TYPE");
        Map<String, Map<String, String>> productMap = new HashMap<>();
        for (CcImage img : exportImgs) {

            // 从对象存储获取图标，并更新图标到本地
            rsmUtils.downloadRsmAndUpdateLocalRsm(img.getImgPath());

            String imgPath = localPath + img.getImgPath();
            Map<String, String> modelMap = new HashMap<>();
            String[] split = imgPath.split("/");
            String imgFileName = split[split.length - 1];
            modelMap.put(modelKey, imgFileName.substring(0, imgFileName.lastIndexOf(".")));
            modelMap.put(modelTypeKey, img.getImgName());
            productMap.put(img.getImgName(), modelMap);
            try {
                // 写图标
                File destFile = new File(imgPath);
                writeToZipFile(destFile, zipOs, path);
                // 写文件夹
                String zipName = img.getImgName() + ".zip";
                File dirFile = new File(imgPath.substring(0, imgPath.lastIndexOf(".")));
                File destZip = new File(imgPath.substring(0, imgPath.lastIndexOf(".")) + ".zip");
                if (!destZip.exists()) {
                    CompressionUtil.compressZip(dirFile, destZip, null, true);
                }
                writeToZipFile(destZip, zipOs, path);
            } catch (Exception e) {
                log.error("导出3D图标文件异常", e);
            }
        }
        // 写入product_lib.json文件
        String productJson = JSON.toJSONString(productMap, SerializerFeature.PrettyFormat);
        try {
            String jsonPath = URLDecoder.decode(ResourceUtils.getURL("classpath:").getPath(), "utf-8");
            File exportDir = new File(jsonPath + "/static/download");
            if (!exportDir.exists()) {
                exportDir.mkdirs();
            }
            File jsonFile = new File(exportDir, "product_lib.json");
            if (jsonFile.exists()) {
                jsonFile.delete();
            }
            FileOutputStream fileOutputStream = new FileOutputStream(jsonFile);
            Writer write = new OutputStreamWriter(fileOutputStream, "UTF-8");
            write.write(productJson);
            write.flush();
            fileOutputStream.close();
            writeToZipFile(jsonFile, zipOs, path);
        } catch (Exception e) {
            log.info("写入product_lib.json文件失败：{}", productJson);
        }
    }

    /**
     * 解压资源压缩包
     *
     * @param imageSource
     * @return
     */
    @SuppressWarnings("unused")
    private File doUncompressZip(Resource imageSource) {
        String path = getClassPath();
        File dir = new File(path + "/static/upload", BinaryUtils.getUUID());
        File zip = new File(dir, BinaryUtils.getUUID());
        try {
            // 将文件保存到zip目录下
            if (!zip.exists()) {
                zip.getParentFile().mkdirs();
            }
            // 将上传的文件信息保存到相应的文件目录里
            InputStream is = null;
            try {
                is = imageSource.getInputStream();
                FileSystem.copy(is, zip);
            } finally {
                if (is != null) {
                    is.close();
                }
            }
        } catch (Exception e) {
            throw new MessageException(e.getMessage());
        }
        // 将文件解压到指定目录
        File output = new File(dir, BinaryUtils.getUUID());
        Compression.uncompressZip(zip, output);
        zip.delete();
        return output;
    }

    /**
     * 获取本地路径
     *
     * @return
     */
    private String getClassPath() {
        String path = "";
        try {
            path = URLDecoder.decode(ResourceUtils.getURL("classpath:").getPath(), "utf-8");
        } catch (UnsupportedEncodingException | FileNotFoundException e1) {
            throw new MessageException(e1.getMessage());
        }
        return path;
    }

    /**
     * 把单个文件写入到ZIP输出流
     *
     * @param file
     */
    private void writeToZipFile(File file, ZipOutputStream zipOs, String dirName) {
        dirName = BinaryUtils.isEmpty(dirName) ? "" : dirName + "/";
        if (file.exists()) {
            if (file.isFile()) {
                try {
                    InputStream imgInputStream = new FileInputStream(file);
                    zipOs.putNextEntry(new ZipEntry(dirName + file.getName()));
                    IOUtils.copy(imgInputStream, zipOs);
                    zipOs.closeEntry();
                    imgInputStream.close();
                } catch (Exception e) {
                    log.error("导出imgs-zip异常", e);
                }
            } else {
                File[] files = file.listFiles();
                int length = files.length;
                for (int i = 0; i < length; i++) {
                    writeToZipFile(files[i], zipOs, dirName + file.getName());
                }
            }
        } else {
            log.error(file.getName() + "文件不存在");
        }
    }

    /**
     * 使用ImageReader获取图片尺寸
     *
     * @param file 支持格式：jpg|png|gif|jpeg|bmp
     */
    private Map<String, Integer> getImageSize(File file) {
        Map<String, Integer> map = new HashMap<String, Integer>();
        if (file == null || !file.exists()) {
            return map;
        }
        String regex = ".*[.](jpg|png|gif|jpeg|bmp)";
        String fileName = file.getName();
        if (!fileName.matches(regex)) {
            return map;
        }
        ImageInputStream iis = null;
        try {
            iis = ImageIO.createImageInputStream(file);
            if (iis == null) {
                return map;
            }
            Iterator<ImageReader> iter = ImageIO.getImageReaders(iis);
            if (iter.hasNext()) {
                ImageReader reader = iter.next();
                reader.setInput(iis, true);
                map.put("width", reader.getWidth(0));
                map.put("height", reader.getHeight(0));
            }
        } catch (IOException e) {
            throw new MessageException(e.getMessage());
        } finally {
            if (iis != null) {
                try {
                    iis.close();
                } catch (IOException e) {
                    log.error("流关闭异常");
                }
            }
        }
        return map;
    }

    @Override
    public void deleteImage(CCcImage delCdt) {
        // TODO Auto-generated method stub
        List<CcImage> delImages = svc.getListByCdt(delCdt);
        Set<String> delUrls = new HashSet<>();
        if (delImages != null && delImages.size() > 0) {
            Set<Long> delIds = new HashSet<>();
            for (CcImage img : delImages) {
                String delUrl = img.getImgPath();
                delUrls.add(delUrl);
                delIds.add(img.getId());
            }
            svc.deleteByIds(delIds);
            delUrls.forEach(delUrl -> {
                try {
                    FileUtil.delFileOrDir(delUrl);
                } catch (Exception e) {
                    log.error("删除图标异常", e);
                }
            });
        }
    }

    @Override
    public Long countBySearchBean(ESSearchImageBean bean) {
        Long domainId = bean.getCdt().getDomainId() == null ? BaseConst.DEFAULT_DOMAIN_ID : bean.getCdt().getDomainId();
        BoolQueryBuilder query = this.searchBeanToQuery(bean);
        return svc.countByCondition(query);
    }

    private ImportResultMessage writeSheetMessageToFile(List<ImportDirMessage> dirMessages) {
        ImportResultMessage result = new ImportResultMessage();
        Long dateTimeFolder = ESUtil.getNumberDate();
        Workbook swb = new XSSFWorkbook();
        FileOutputStream fileOutputStream = null;
        String filePath = "/" + dateTimeFolder + "/" + "图标导入明细" + "_"
                + new SimpleDateFormat("yyyyMMdd-HHmmss").format(new Date()) + ".xlsx";
        try {
            File output = new File(localPath + filePath);
            if (!output.getParentFile().exists()) {
                output.getParentFile().mkdirs();
            }
            fileOutputStream = new FileOutputStream(output);
            dirMessages.forEach(dirMessage -> {
                result.setSuccessNum(result.getSuccessNum() + dirMessage.getSuccessCount());
                result.setFailNum(result.getFailNum() + dirMessage.getFailCount());
                List<ImportImageMessage> imgMessages = dirMessage.getImageMessages();
                if (!BinaryUtils.isEmpty(imgMessages)) {
                    Sheet sheet = swb.createSheet(dirMessage.getDirName() + "导入明细");
                    Row titleRow = sheet.createRow(0);
                    Cell titleCell0 = titleRow.createCell(0, CellType.STRING);
                    titleCell0.setCellValue(dirMessage.getDirName() + "-导入明细");
                    Cell titleCell1 = titleRow.createCell(1, CellType.STRING);
                    titleCell1
                            .setCellValue("成功：" + dirMessage.getSuccessCount() + ";失败：" + (dirMessage.getFailCount()));
                    for (int i = 0; i < imgMessages.size(); i++) {
                        ImportImageMessage imgMessage = imgMessages.get(i);
                        Row row = sheet.createRow(i + 1);
                        Cell cell0 = row.createCell(0, CellType.STRING);
                        cell0.setCellValue(imgMessage.getImageName());
                        Cell cell1 = row.createCell(1, CellType.STRING);
                        cell1.setCellValue(imgMessage.getMessage());
                    }
                }
            });

            //将文件写入对象存储服务器
            rsmUtils.uploadRsmFromFile(output);

            if (result.getFailNum() > 0) {
                result.setFailFile(urlPath + filePath);
            }
        } catch (Exception e) {
            log.error("写入导入图标异常文件异常", e);
        } finally {
            try {
                if (swb != null) {
                    swb.write(fileOutputStream);
                    swb.close();
                }
                if (fileOutputStream != null) {
                    fileOutputStream.close();
                }
            } catch (Exception e2) {
            }
        }
        return result;
    }

    @Override
    public ResponseEntity<byte[]> downloadImageResource(List<Long> ids) {
        Assert.isTrue(!BinaryUtils.isEmpty(ids), "X_PARAM_NOT_NULL${name:ids}");
        List<CcImage> images = svc.getListByQuery(QueryBuilders.termsQuery("id", ids));
        Assert.isTrue(!BinaryUtils.isEmpty(images), "图标资源不存在");
        ResponseEntity<byte[]> entity = null;
        HttpHeaders headers = new HttpHeaders();
        InputStream inStream = null;
        String fileName = null;
        if (images.size() > 1) {
            // 现阶段只支持单个视频资源下载，暂不处理其他
            return null;
        } else {
            String[] split = images.get(0).getImgPath().split("\\.");
            fileName = images.get(0).getImgName() + "." + split[split.length - 1];
            Resource downloadFile = sysSvc.downloadFile(images.get(0).getImgPath());
            inStream = downloadFile.getInputStream();
        }
        // 导出文件
        try {
            byte[] bytes = new byte[inStream.available()];
            inStream.read(bytes);
            headers.add("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            entity = new ResponseEntity<byte[]>(bytes, headers, HttpStatus.OK);
        } catch (Exception e) {
            throw new MessageException(e.getMessage());
        } finally {
            if (inStream != null) {
                try {
                    inStream.close();
                } catch (Exception e) {
                    log.error("流关闭异常");
                }
            }
        }
        return entity;
    }


    private boolean validFileSuffix(Pattern pattern, String name) {
        return pattern.matcher(name).find();
    }

}
