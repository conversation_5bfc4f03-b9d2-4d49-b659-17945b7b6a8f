package com.uino.service.cmdb.microservice;

import com.binary.jdbc.Page;
import com.uino.bean.cmdb.base.ESCiRltAutoBuild;
import com.uino.bean.cmdb.business.ClassRltListDto;
import com.uino.bean.cmdb.query.ESCiRltAutoBuildSearchBean;

import java.util.List;

/**
 * @Classname ICiRltAutoBuildSvc
 * @Description
 * @Date 2020/6/29 17:20
 * <AUTHOR> sh
 */
public interface ICiRltAutoBuildSvc {
    /**
     * 添加默认构建规则
     *
     * @param sourceCiClassId
     * @param targetCiClassId
     * @param rltClassId
     * @param visualModelId
     */
    ESCiRltAutoBuild saveDefault(Long domainId, Long sourceCiClassId, Long targetCiClassId, Long rltClassId, Long visualModelId);

    /**
     * 修改构建规则
     *
     * @param esCiRltAutoBuild
     */
    Long updateCiRltAutoBuild(ESCiRltAutoBuild esCiRltAutoBuild);


    /**
     * 根据关系id查询关系构建列表
     * @param rltId
     */
    List<ClassRltListDto> getAutoBuildListByRltId(Long rltId);

    /**
     * 查看构建规则
     *
     * @param sourceCiClassId
     * @param targetCiClassId
     * @param rltClassId
     * @param visualModelId
     * @return
     */
    ESCiRltAutoBuild findCiRltAutoBuild(Long domainId, Long sourceCiClassId, Long targetCiClassId, Long rltClassId, Long visualModelId);

    /**
     * 删除构建规则
     *
     * @param sourceCiClassId
     * @param targetCiClassId
     * @param rltClassId
     * @param visualModelId
     */
    void deleteCiRltAutoBuild(Long sourceCiClassId, Long targetCiClassId, Long rltClassId, Long visualModelId);

    /**
     * 执行关系自动构建
     *
     * @param
     */
    void runCiRltAutoBuildAll(Long domainId);

    boolean runCiRltAutoBuildById(Long id);

    /**
     * ap执行自动构建任务方法
     * @param id
     * @return
     */
    boolean apRunRltAutoBuild(Long id);

    /**
     * 保存构建规则
     * @param esCiRltAutoBuild
     * @return
     */
    Long saveCiRltAutoBuild(ESCiRltAutoBuild esCiRltAutoBuild);

    /**
     * 分页查询
     * @param searchBean
     * @return
     */
    Page<ESCiRltAutoBuild> queryCiRltAutoBuildPage(ESCiRltAutoBuildSearchBean searchBean);

    /**
     * 根据主键删除
     * @param id
     * @return
     */
    Integer deleteById(Long id);

    /**
     * 直接保存构建规则
     * @param esCiRltAutoBuild
     * @return
     */
    Long saveOrUpdate(ESCiRltAutoBuild esCiRltAutoBuild);

    List<ESCiRltAutoBuild> getAutoBuildListByRltClassId(List<Long> rltClassIds);

    void runCiRltAutoBuildByIds(List<Long> ids);

    void deleteCiRltAutoBuildBatchByCiClassIds(List<Long> ciClassIds, Long visualModelId);
}
