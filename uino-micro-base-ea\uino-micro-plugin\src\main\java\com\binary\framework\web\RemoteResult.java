package com.binary.framework.web;

import java.io.Serializable;

public class <PERSON>moteR<PERSON>ult implements Serializable {
	private static final long serialVersionUID = 2588163462392220979L;
	
	/**
	 * 无异常时error-code
	 */
	public static final int NO_ERROR_CODE = -1;
	
	
	private boolean success;
	private Object data;
	private int code;
	private String message;
	
	
	public RemoteResult(Object data) {
		this(true, NO_ERROR_CODE, null, data);
	}
	
	
	public RemoteResult(Throwable t) {
		this(false, ErrorCode.SERVER_ERROR.getCode(), t.getMessage(), null);
	}
	
	
	public RemoteResult(boolean success, int code, String message) {
		this(success, code, message, null);
	}
	
	
	public RemoteResult(boolean success, int code, String message, Object data) {
		this.success = success;
		this.code = code;
		this.message = message;
		this.data = data;
	}
	

	
	
	
	public boolean isSuccess() {
		return success;
	}


	public void setSuccess(boolean success) {
		this.success = success;
	}


	public Object getData() {
		return data;
	}


	public void setData(Object data) {
		this.data = data;
	}



	public int getCode() {
		return code;
	}


	public void setCode(int code) {
		this.code = code;
	}


	public String getMessage() {
		return message;
	}


	public void setMessage(String message) {
		this.message = message;
	}


	
	
	
	
}
