package com.uinnova.product.eam.comm.model;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.config.EnvConfig;
import lombok.Data;

@Data
public class AppSystemQueryConParam {

    public AppSystemQueryConParam(String paramName, String paramColor) {
        this.paramName = paramName;
        this.paramColor = paramColor;
    }

    @Comment("参数名称")
    private String paramName;

    @Comment("参数颜色")
    private String paramColor;
}
