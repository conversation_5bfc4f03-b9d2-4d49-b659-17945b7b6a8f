package com.uino.init;

import com.binary.core.lang.StringUtils;
import com.binary.framework.util.ControllerUtils;
import com.binary.framework.web.RemoteResult;
import com.uino.bean.license.BaseLicenseAuth;
import com.uino.bean.permission.base.SysUser;
import com.uino.dao.license.ESLicenseAuthSvc;
import com.uino.dao.util.ESUtil;
import com.uino.init.http.LicenseAuthorityFilter;
import com.uino.license.sdk.license.License;
import com.uino.util.sys.SysUtil;
import lombok.extern.slf4j.Slf4j;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;

@Slf4j
public class UinoLicenseAuthorityFilter extends LicenseAuthorityFilter {

    @Override
    public void doFilter(ServletRequest req, ServletResponse resp, FilterChain chain)
            throws IOException, ServletException {
        HttpServletRequest request = (HttpServletRequest) req;
        HttpServletResponse response = (HttpServletResponse) resp;
        request.setAttribute(LicenseAuthorityFilter.class.getName(), this);

        License license = getLicense();
        Integer authEndDate = license.getAuthEndDate();
        int expiredDay = 0;
        if (authEndDate != 0) {
            //过期天数（过期日期计算所得）
            expiredDay = daysBetween(ESUtil.getNumberDate() + "", license.getAuthEndDate() + "");
        }
        //剩余使用天数
        int remainingUseDay =  (int)(license.getAuthEndDate() - (license.getOnlineTime() / 60 / 24));

        response.addHeader("license_valid_time", String.valueOf(Math.min(expiredDay, remainingUseDay)));

        // 如里不需要过滤
        if (isIgnoreRequest(request)) {
            chain.doFilter(req, resp);
            return;
        }

        if (!license.getValid()) {
            if (getTrialSaaSUtil().getSysType().equals(1)) {
                processTrialSaaSExtrl(request, response);
                return;
            }
            boolean isajax = request.getHeader("REQUEST_HEADER") != null;
            if (isajax) {
                processError4Ajax(request, response);
            } else {
                processError4Forward(request, response);
            }
            return;
        }
        // Integer authEndTime = license.getAuthEndDate();
        // int now = new BigDecimal(ESUtil.getNumberDate()).intValue();

        chain.doFilter(request, response);
    }

    @Override
    protected void processError4Ajax(HttpServletRequest request, HttpServletResponse response) {
        response.setStatus(423);
        RemoteResult rs = new RemoteResult(false, 423, "Authorized license is invalid!");
        ControllerUtils.returnSimpleJson(request, response, rs);
    }


    public int daysBetween(String currentDate, String endDate) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        Calendar cal = Calendar.getInstance();
        int betweenDays = -1;
        try {
            cal.setTime(sdf.parse(currentDate));
            long currentTime = cal.getTimeInMillis();

            cal.setTime(sdf.parse(endDate));
            cal.add(Calendar.DAY_OF_MONTH, 1);
            long endTime = cal.getTimeInMillis();
            long day = (endTime - currentTime) / (1000 * 3600 * 24);
            if (day <= 0L) {
                return betweenDays;
            }else {
                return Integer.parseInt(String.valueOf(day));
            }

        } catch (ParseException e) {
            log.error("授权日期有误", e);
//            e.printStackTrace();
        }
        return betweenDays;
    }

    private void processTrialSaaSExtrl(HttpServletRequest request, HttpServletResponse response) throws IOException {
        if (StringUtils.isEmpty(request.getHeader("Authorization"))
                || StringUtils.isEmpty(request.getHeader("tk"))) {
            SysUser sysUser = SysUtil.getCurrentUserInfo();
            if ("superadmin".equals(sysUser.getLoginCode())) {
                boolean isajax = request.getHeader("REQUEST_HEADER") != null;
                if (isajax) {
                    processError4Ajax(request, response);
                } else {
                    processError4Forward(request, response);
                }
                return;
            }
        }
        response.setStatus(424);
        RemoteResult rs = new RemoteResult(false, 424, "license is invalid!");
        ControllerUtils.returnSimpleJson(request, response, rs);
        //this.clearToken(request, response, sysUser);
    }

}
