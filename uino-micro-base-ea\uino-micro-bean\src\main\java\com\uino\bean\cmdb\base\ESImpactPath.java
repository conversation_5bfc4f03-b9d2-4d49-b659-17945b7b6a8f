package com.uino.bean.cmdb.base;

import java.io.Serializable;

/**
 * 
 * <AUTHOR>
 *
 */
public class ESImpactPath implements Serializable {

    private static final long serialVersionUID = 4335084845284304101L;

    private String id;

    private Long focusClassId;

    private Long sourceClassId;

    private Long targetClassId;

    private Long rltClassId;

    private Long domainId;

    private Long createTime;

    private Long modifyTime;

    @Override
    public boolean equals(Object obj) {
        if (obj == null) {
            return false;
        } else if (!(obj instanceof ESImpactPath)) {
            return false;
        } else if (this == obj) {
            return true;
        } else {
            ESImpactPath impacePathObj = (ESImpactPath) obj;
            return this.getFocusClassId().equals(impacePathObj.getFocusClassId())
                    && this.getSourceClassId().equals(impacePathObj.getSourceClassId())
                    && this.getTargetClassId().equals(impacePathObj.getTargetClassId())
                    && this.getRltClassId().equals(impacePathObj.getRltClassId());
        }
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Long getFocusClassId() {
        return focusClassId;
    }

    public void setFocusClassId(Long focusClassId) {
        this.focusClassId = focusClassId;
    }

    public Long getSourceClassId() {
        return sourceClassId;
    }

    public void setSourceClassId(Long sourceClassId) {
        this.sourceClassId = sourceClassId;
    }

    public Long getTargetClassId() {
        return targetClassId;
    }

    public void setTargetClassId(Long targetClassId) {
        this.targetClassId = targetClassId;
    }

    public Long getRltClassId() {
        return rltClassId;
    }

    public void setRltClassId(Long rltClassId) {
        this.rltClassId = rltClassId;
    }

    public Long getDomainId() {
        return domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }
}
