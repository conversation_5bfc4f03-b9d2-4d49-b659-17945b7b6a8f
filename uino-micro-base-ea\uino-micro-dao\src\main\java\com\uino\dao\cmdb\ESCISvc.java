package com.uino.dao.cmdb;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.PropertyFilter;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.binary.core.exception.MessageException;
import com.binary.core.util.BinaryUtils;
import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.comm.model.ci.CCcCi;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.comm.util.CommUtil;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CiGroupPage;
import com.uinnova.product.vmdb.provider.ci.bean.CiQueryCdt;
import com.uinnova.product.vmdb.provider.search.bean.CcCiSearchPage;
import com.uino.bean.cmdb.base.*;
import com.uino.bean.cmdb.enums.AttrNameKeyEnum;
import com.uino.bean.cmdb.query.ESAttrAggBean;
import com.uino.bean.cmdb.query.ESAttrBean;
import com.uino.bean.cmdb.query.ESCISearchBean;
import com.uino.bean.permission.base.SysUser;
import com.uino.dao.AbstractESBaseDao;
import com.uino.dao.ESConst;
import com.uino.dao.util.ESUtil;
import com.uino.util.sys.CheckAttrUtil;
import com.uino.util.sys.LibTypeUtil;
import com.uino.util.sys.SysUtil;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.elasticsearch.common.settings.Settings;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.MultiMatchQueryBuilder.Type;
import org.elasticsearch.index.query.Operator;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.sort.SortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.Map.Entry;
import java.util.stream.Collectors;

/**
 * CI配置项DAO
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RefreshScope
public class ESCISvc extends AbstractESBaseDao<ESCIInfo, CCcCi> {

    Log logger = LogFactory.getLog(ESCISvc.class);

    @Autowired
    @Lazy
    ESCIClassSvc classSvc;

    @Autowired
    @Lazy
    ESCmdbCommSvc commSvc;

    @Autowired
    private ESCIHistorySvc ciHistorySvc;

    @Autowired
    private ESCIAttrTransConfigSvc attrConfigSvc;

    @Autowired
    private ESTagSvc esTagSvc;

    @Value("${http.resource.space:}")
    private String urlPath;

    @Value("${is.show.3d.attribute:false}")
    private Boolean isShow3dAttribute;

    @Override
    public String getIndex() {
        String libType = LibTypeUtil.getLibType();
        if (LibType.PRIVATE.toString().equals(libType)) {
            return ESConst.INDEX_CMDB_CI_PRIVATE;
        } else if (LibType.DESIGN.toString().equals(libType)) {
            return ESConst.INDEX_CMDB_CI_DESIGN;
        }
        return ESConst.INDEX_CMDB_CI;
    }

    @Override
    public String getType() {
        String libType = LibTypeUtil.getLibType();
        if (LibType.PRIVATE.toString().equals(libType)) {
            return ESConst.INDEX_CMDB_CI_PRIVATE;
        } else if (LibType.DESIGN.toString().equals(libType)) {
            return ESConst.INDEX_CMDB_CI_DESIGN;
        }
        return ESConst.INDEX_CMDB_CI;
    }

    @PostConstruct
    public void init() {
        super.initIndex(5);
    }

    public Map<String, Object> getCIMapping() {
        return this.getIndexMapping();
    }

    @Override
    public Settings getSetting(String analyzerName, int number_of_shards) {
        return Settings.builder()
                .put("number_of_shards", number_of_shards)
                .put("number_of_replicas", 1)
                //修改索引的最大字段，对象管理会自定义字段，超出会报错，这里改为2w个字段，初步推算在500~800个分类
                .put("index.mapping.total_fields.limit",20000)
                .put("index.max_result_window", 100000)
                // analysis 配置开始
                .put("analysis.normalizer.my_normalizer.type", "custom")
                .putList("analysis.normalizer.my_normalizer.filter", "lowercase")
                .put("analysis.analyzer." + analyzerName + ".type", "custom")
                .put("analysis.analyzer." + analyzerName + ".tokenizer", "standard")
                .putList("analysis.analyzer." + analyzerName + ".filter", "lowercase", "reverse")
                .build();
    }

    /**
     * <b>获取CICIinfo对象
     *
     * @param id
     * @return
     */
    public CcCiInfo getCiInfoById(Long id) {
        ESCIInfo esCI = this.getById(id);
        if (esCI != null) {
            CcCiInfo ciInfo = commSvc.tranCcCiInfo(esCI, false);
            List<ESCIClassInfo> classInfos = classSvc.getAllDefESClassInfosByClassIds(ciInfo.getCi().getDomainId(), Arrays.asList(esCI.getClassId()));
            if (!BinaryUtils.isEmpty(classInfos)) {
                ESCIClassInfo classInfo = classInfos.get(0);
                ciInfo.setCiClass(classInfo);
                ciInfo.setAttrDefs(classInfo.getCcAttrDefs());
            }
            return ciInfo;
        }
        return null;
    }

    public CcCiInfo getCiInfoByCiCode(String ciCode) {
        ESCIInfo esciInfo = this.selectOne(QueryBuilders.termQuery("ciCode.keyword", ciCode));
        if (esciInfo != null) {
            CcCiInfo ciInfo = commSvc.tranCcCiInfo(esciInfo, false);
            List<ESCIClassInfo> classInfos = classSvc.getAllDefESClassInfosByClassIds(ciInfo.getCi().getDomainId(), Arrays.asList(esciInfo.getClassId()));
            if (!BinaryUtils.isEmpty(classInfos)) {
                ESCIClassInfo classInfo = classInfos.get(0);
                ciInfo.setCiClass(classInfo);
                ciInfo.setAttrDefs(classInfo.getCcAttrDefs());
            }
            return ciInfo;
        }
        return null;
    }

    public List<CcCiInfo> queryCiInfoList(CCcCi cdt, String orders, Boolean isAsc, Boolean hasClass) {
        orders = BinaryUtils.isEmpty(orders) ? "modifyTime" : orders;
        List<Long> classIds = new ArrayList<>();
        if (cdt.getClassId() != null) {
            classIds.add(cdt.getClassId());
        }
        if (cdt.getClassIds() != null) {
            classIds.addAll(Arrays.asList(cdt.getClassIds()));
        }
        orders = this.transSortField(cdt.getDomainId(), classIds, orders);
        QueryBuilder query = ESUtil.cdtToBuilder(cdt);
        long count = this.countByCondition(query);
        List<ESCIInfo> list = this.getSortListByQuery(1, new BigDecimal(count).intValue(), query, orders, isAsc).getData();
        return commSvc.transEsInfoList(list, hasClass);
    }

    public List<ESCIInfo> queryESCiInfoList(CCcCi cdt, String orders, Boolean isAsc) {
        orders = BinaryUtils.isEmpty(orders) ? "modifyTime" : orders;
        List<Long> classIds = new ArrayList<>();
        if (cdt.getClassId() != null) {
            classIds.add(cdt.getClassId());
        }
        if (cdt.getClassIds() != null) {
            classIds.addAll(Arrays.asList(cdt.getClassIds()));
        }
        orders = this.transSortField(cdt.getDomainId(), classIds, orders);
        QueryBuilder query = ESUtil.cdtToBuilder(cdt);
        long count = this.countByCondition(query);
        List<ESCIInfo> list = this.getSortListByQuery(1, new BigDecimal(count).intValue(), query, orders, isAsc).getData();
        return list;
    }

    public Page<CcCiInfo> queryCiInfoPage(Integer pageNum, Integer pageSize, CCcCi cdt, String orders, Boolean isAsc, Boolean hasClass) {
        orders = BinaryUtils.isEmpty(orders) ? "modifyTime" : orders;
        List<Long> classIds = new ArrayList<>();
        if (cdt.getClassId() != null) {
            classIds.add(cdt.getClassId());
        }
        if (cdt.getClassIds() != null) {
            classIds.addAll(Arrays.asList(cdt.getClassIds()));
        }
        orders = this.transSortField(cdt.getDomainId(), classIds, orders);
        Page<ESCIInfo> esPage = this.getSortListByQuery(pageNum, pageSize, ESUtil.cdtToBuilder(cdt), orders, isAsc);
        return commSvc.transEsInfoPage(esPage, hasClass);
    }

    public Page<CcCiInfo> getCIInfoPageByQuery(int pageNum, int pageSize, QueryBuilder query, Boolean hasClass) {
        Page<ESCIInfo> esPage = this.getListByQuery(pageNum, pageSize, query);
        return commSvc.transEsInfoPage(esPage, hasClass);
    }

    /**
     * <b>保存到ES
     *
     * @param ciInfo
     * @return 唯一ID
     */
    public Long saveOrUpdateCI(CcCiInfo ciInfo) {
        if (BinaryUtils.isEmpty(ciInfo.getCi().getId())) {
            long id = ESUtil.getUUID();
            ciInfo.getCi().setId(id);
            ciInfo.getCi().setCiCode(String.valueOf(id));
            try {
                SysUser loginUser = SysUtil.getCurrentUserInfo();
                ciInfo.getCi().setCreator(loginUser.getLoginCode());
                if (BinaryUtils.isEmpty(ciInfo.getCi().getOwnerCode())) {
                    ciInfo.getCi().setOwnerCode(ciInfo.getCi().getCreator());
                }
            } catch (Exception e) {
                ciInfo.getCi().setCreator("system");
                if (BinaryUtils.isEmpty(ciInfo.getCi().getOwnerCode())) {
                    ciInfo.getCi().setOwnerCode("system");
                }
            }
        }
        ESCIInfo esCI = commSvc.tranESCIInfo(ciInfo);
        // String jsonStr = JSON.toJSONString(esCI);
        // JSONObject json = JSON.parseObject(jsonStr);
        return this.saveOrUpdate(esCI);
    }

    public Integer saveOrUpdateCiList(List<ESCIInfo> ciList) {
        for (ESCIInfo ciInfo : ciList) {
            if (BinaryUtils.isEmpty(ciInfo.getId())) {
                long id = ESUtil.getUUID();
                ciInfo.setId(id);
                ciInfo.setCiCode(String.valueOf(id));
                try {
                    SysUser loginUser = SysUtil.getCurrentUserInfo();
                    ciInfo.setCreator(loginUser.getLoginCode());
                    if (BinaryUtils.isEmpty(ciInfo.getOwnerCode())) {
                        ciInfo.setOwnerCode(ciInfo.getCreator());
                    }
                } catch (Exception e) {
                    ciInfo.setCreator("system");
                    if (BinaryUtils.isEmpty(ciInfo.getOwnerCode())) {
                        ciInfo.setOwnerCode("system");
                    }
                }
            }
            commSvc.tranESCIInfo(ciInfo);
        }
        return this.saveOrUpdateBatch(ciList);
    }

    public Long saveOrUpdateCI(ESCIInfo ciInfo) {
        if (BinaryUtils.isEmpty(ciInfo.getId())) {
            long id = ESUtil.getUUID();
            ciInfo.setId(id);
            ciInfo.setCiCode(String.valueOf(id));
            try {
                SysUser loginUser = SysUtil.getCurrentUserInfo();
                ciInfo.setCreator(loginUser.getLoginCode());
                if (BinaryUtils.isEmpty(ciInfo.getOwnerCode())) {
                    ciInfo.setOwnerCode(ciInfo.getCreator());
                }
            } catch (Exception e) {
                ciInfo.setCreator("system");
                if (BinaryUtils.isEmpty(ciInfo.getOwnerCode())) {
                    ciInfo.setOwnerCode("system");
                }
            }
        }
        commSvc.tranESCIInfo(ciInfo);
        return this.saveOrUpdate(ciInfo);
    }


    /**
     * 批量保存并返回保存详情
     *
     * @param ciInfoList
     * @return
     */
    public Map<String, Object> saveOrUpdateCIBatch(List<CcCiInfo> ciInfoList, boolean isAsync) {
        // JSONArray list = new JSONArray();
        List<ESCIInfo> esciInfos = new ArrayList<>();
        SysUser loginUser = null;
        try {
            loginUser = SysUtil.getCurrentUserInfo();
        } catch (Exception e) {
            loginUser = new SysUser();
            loginUser.setLoginCode("system");
        }
        for (CcCiInfo ciInfo : ciInfoList) {
            ESCIInfo esCI = commSvc.tranESCIInfo(ciInfo);
            if (esCI.getId() == null) {
                esCI.setId(ESUtil.getUUID());
                ciInfo.getCi().setCreator(loginUser == null ? "system" : loginUser.getLoginCode());
            }
            if (BinaryUtils.isEmpty(ciInfo.getCi().getOwnerCode())) {
                ciInfo.getCi().setOwnerCode(loginUser == null ? "system" : loginUser.getLoginCode());
            }
            if (BinaryUtils.isEmpty(esCI.getCiCode())) {
                esCI.setCiCode(String.valueOf(esCI.getId()));
            }
            esciInfos.add(esCI);
            // String jsonStr = JSON.toJSONString(esCI);
            // JSONObject json = JSON.parseObject(jsonStr);
            // list.add(json);
        }
        this.transCIAttrs(esciInfos, false);
        ciHistorySvc.saveOrUpdateHistoryInfosBatch(esciInfos, ESCIHistoryInfo.ActionType.SAVE_OR_UPDATE);
        return this.saveOrUpdateBatchMessage(this.writeMapNullValueForAttrs(esciInfos), isAsync);
    }

    /**
     * dcv特有，不允许别人调用，否则可能有严重问题
     *
     * @param esCiInfoList
     * @return
     */
    public Integer updateESCIInfoBatch(List<ESCIInfo> esCiInfoList) {
        if (!BinaryUtils.isEmpty(esCiInfoList)) {
            Integer i = super.saveOrUpdateBatch(esCiInfoList);
            return i;
        } else {
            return 1;
        }
    }

    /**
     * <b>删除分类下的CI
     *
     * @param id 分类ID
     * @return
     */
    public Integer removeByClassId(Long id) {
        return super.deleteByQuery(QueryBuilders.termQuery("classId", id), true);
    }

    /**
     * <b>全文检索
     *
     * @param pageNum
     * @param pageSize
     * @param cdt
     * @param hasClass
     * @return CiGroupPage
     */
    public CiGroupPage queryPageByIndex(Long domainId, Integer pageNum, Integer pageSize, CiQueryCdt cdt, Boolean hasClass) {
        if (hasClass == null) {
            hasClass = false;
        }
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("domainId", domainId));
        // 全文检索
        String like = cdt.getLike();
        if (like != null && !like.isEmpty()) {
            query.must(QueryBuilders.multiMatchQuery(like, "attrs.*").operator(Operator.AND).type(Type.PHRASE_PREFIX).lenient(true));
        }
        // 根据分类id
        Long classId = cdt.getClassId();
        if (classId != null) {
            query.must(QueryBuilders.termQuery("classId", classId));
        }
        Long[] classIds = cdt.getClassIds();
        if (classIds != null) {
            query.must(QueryBuilders.termsQuery("classId", Arrays.asList(classIds)));
        }
        // 根据标签id
        Long tagId = cdt.getTagId();
        if (tagId != null) {
            ESCITagInfo tag = esTagSvc.getById(tagId);
            if (tag == null) {
                return commSvc.tranCiGroupPage(new Page<>(), false);
            }
            query.must(esTagSvc.getQueryByTag(tag));
        }
        Long[] tagIds = cdt.getTagIds();
        if (tagIds != null) {
            List<ESCITagInfo> tags = esTagSvc.getListByQueryScroll(QueryBuilders.termsQuery("id", Arrays.asList(tagIds)));
            if (tags.size() < tagIds.length) {
                return commSvc.tranCiGroupPage(new Page<>(), false);
            }
            for (ESCITagInfo tag : tags) {
                query.must(esTagSvc.getQueryByTag(tag));
            }
        }
        Page<ESCIInfo> page = this.getSortListByQuery(pageNum, pageSize, query, "modifyTime", false);
        return commSvc.tranCiGroupPage(page, hasClass);
    }

    public CiGroupPage queryPageBySearchBean(ESCISearchBean bean, Boolean hasClass) {
        Page<ESCIInfo> page = this.searchESCIByBean(bean);
        return commSvc.tranCiGroupPage(page, hasClass);
    }

    /**
     * es分页查询ci接口支持<br/>
     * 1、模糊匹配<br/>
     * 2、根据指定属性查询(and /or )<br/>
     * 3、ciCode/ciId/hashcode筛选<br/>
     * 4、classIds筛选<br/>
     * 5、tagIds筛选<br/>
     * 6、默认根据匹配度排序<br/>
     * 7、排序字段 如果是字符串类型必须加keyword
     *
     * @param bean 查询BEAN，定义了各个字段之间的关系
     * @return
     */
    public CcCiSearchPage searchCIByBean(ESCISearchBean bean) {
        return commSvc.tranCcCiSearchPage(this.searchESCIByBean(bean));
    }

    public Page<ESCIInfo> searchESCIByBean(ESCISearchBean bean) {
        QueryBuilder query = commSvc.getCIQueryBuilderByBean(bean);
        String sort = !BinaryUtils.isEmpty(bean.getSortField()) ? bean.getSortField() : "modifyTime";
        // 映射排序字段
        Set<Long> classIds = new HashSet<>();
        if (!BinaryUtils.isEmpty(bean.getClassIds())) {
            classIds.addAll(bean.getClassIds());
        }
        if (bean.getCdt() != null && bean.getCdt().getClassId() != null) {
            classIds.add(bean.getCdt().getClassId());
        }
        sort = this.transSortField(bean.getDomainId(), classIds, sort);
        if (bean.isHighLight()) {
            SortOrder orderType = bean.isAsc() ? SortOrder.ASC : SortOrder.DESC;
            List<SortBuilder<?>> sorts = new LinkedList<>();
            sorts.add(SortBuilders.fieldSort(sort).order(orderType));
            return this.getSortListByHighLightQuery(bean.getPageNum(), bean.getPageSize(), query, sorts, bean.getHighLightFields());
        } else {
            return this.getSortListByQuery(bean.getPageNum(), bean.getPageSize(), query, sort, bean.isAsc());
        }
    }

    /**
     * <b>根据cdt对象查询分类
     *
     * @param pageNum
     * @param pageSize
     * @param bean     自动生成的分类查询对象
     * @return
     */
    public CcCiSearchPage searchCIByCdt(int pageNum, int pageSize, CCcCi bean) {
        QueryBuilder query = ESUtil.cdtToBuilder(bean);
        Page<ESCIInfo> page = this.getSortListByQuery(pageNum, pageSize, query, "modifyTime", false);
        return commSvc.tranCcCiSearchPage(page);
    }

    public List<ESCIInfo> getCIInfoListByCIPrimaryKeys(Long domainId, List<List<String>> ciPrimaryKeys) {
        if (BinaryUtils.isEmpty(ciPrimaryKeys)) {
            return Collections.emptyList();
        }
        // 生成hashCodes
        Set<Integer> hashCodes = getHashCodesByPrimaryKeys(ciPrimaryKeys);
        Assert.isTrue(!BinaryUtils.isEmpty(hashCodes), "X_PARAM_NOT_NULL${name:hashCodes}");
        // 查询CI信息
        CCcCi cdt = new CCcCi();
        cdt.setDomainId(domainId);
        cdt.setHashCodes(hashCodes.toArray(new Integer[]{}));
        List<ESCIInfo> ciInfos = this.queryESCiInfoList(cdt, null, true);
        if (BinaryUtils.isEmpty(ciInfos)) {
            return Collections.emptyList();
        }
        // 分组
        Map<Integer, List<ESCIInfo>> ciHashCodeInfoMap = new HashMap<Integer, List<ESCIInfo>>();
        for (ESCIInfo info : ciInfos) {
            if (info == null) {
                // 防数据不完整
                continue;
            }
            Integer key = info.getHashCode();
            List<ESCIInfo> infos = ciHashCodeInfoMap.get(key);
            if (infos == null) {
                infos = new ArrayList<ESCIInfo>();
            }
            infos.add(info);
            ciHashCodeInfoMap.put(key, infos);
        }
        // 组装
        List<ESCIInfo> resultInfos = new ArrayList<ESCIInfo>();
        for (List<String> pks : ciPrimaryKeys) {
            Integer hashCode = CommUtil.getCiMajorHashCode(pks);
            List<ESCIInfo> infos = ciHashCodeInfoMap.get(hashCode);
            if (infos == null || infos.isEmpty()) {
                continue;
            }
            // 比较业务主键值是否一样
            for (ESCIInfo info : infos) {
                try {
                    if (CommUtil.compareToCiPks(pks, com.binary.json.JSON.toList(info.getCiPrimaryKey(), String.class))) {
                        resultInfos.add(info);
                    }
                } catch (Exception e) {
                    log.error("do compareToCiPks 【" + JSON.toJSONString(ciPrimaryKeys) + "】 and 【 " + info.getCiPrimaryKey() + "】  err!", e);
                    throw MessageException.i18n("BS_MNAME_CIPRIMARYKEY_ERRO");
                }
            }
        }
        return resultInfos;
    }

    /**
     * 根据业务主键值集合生成hashCode集合
     *
     * @param ciPrimaryKeys 业务主键值集合
     * @return hashCodeSet
     */
    private Set<Integer> getHashCodesByPrimaryKeys(List<List<String>> ciPrimaryKeys) {
        if (ciPrimaryKeys == null || ciPrimaryKeys.isEmpty()) {
            return null;
        }
        Set<Integer> hashCodes = new HashSet<Integer>();
        for (List<String> pks : ciPrimaryKeys) {
            if (pks == null || pks.isEmpty()) {
                continue;
            }
            hashCodes.add(CommUtil.getCiMajorHashCode(pks));
        }
        return hashCodes;
    }

    public void transCIAttrs(List<ESCIInfo> esciInfos, boolean changeToShowName) {
        if (!BinaryUtils.isEmpty(esciInfos)) {
            Set<Long> classIds = esciInfos.stream().map(ESCIInfo::getClassId).filter(Objects::nonNull).collect(Collectors.toSet());
            //查分类
            List<ESCIClassInfo> ciClassInfos = classSvc.getListByQuery(0, classIds.size(),
                    QueryBuilders.boolQuery().must(QueryBuilders.termsQuery("id", classIds))).getData();
            //父分类ids
            Map<Long, ESCIClassInfo> parentCiClassInfoMap = getParentCiClassMap(classIds, ciClassInfos);
            // 获取属性中间表记录，用于映射属性
            Map<Long, List<ESCIAttrTransConfig>> attrConfigMap = getCiAttrTransConfig(classIds);
            // 组装分类id与其所有属性的对应关系，包括父级属性
            Map<Long, List<CcCiAttrDef>> clsAlldefsMap = getClsAlldefsMap(ciClassInfos, parentCiClassInfoMap, attrConfigMap);
            // CI属性转换
            for (ESCIInfo esciInfo : esciInfos) {
                List<CcCiAttrDef> defs = clsAlldefsMap.get(esciInfo.getClassId());
                // 判断CI所属分类存在，避免脏数据
                Assert.notEmpty(defs, "BS_MNAME_CLASS_NOT_EXSIT");
                List<ESCIAttrTransConfig> transConfigs = attrConfigMap.get(esciInfo.getClassId());
                Map<Object, ESCIAttrTransConfig> defConfigMap = BinaryUtils.toObjectMap(transConfigs, "defId");
                // 属性类型转换
                if (!changeToShowName) {
                    esciInfo.setAttrs(this.transCIAttrType(esciInfo.getAttrs(), defs, transConfigs));
                }
                // 原属性map
                Map<String, Object> attrs = esciInfo.getAttrs();
                // 只返回定义过的属性map
                Map<String, Object> transAttrs = getCiAttrDefMap(changeToShowName, defs, defConfigMap, attrs);
                // 丰富ciLabel
                if (changeToShowName) {
                    List<CcCiAttrDef> labelDefs = defs.stream().filter(def -> def.getIsCiDisp() != null && def.getIsCiDisp() == 1).collect(Collectors.toList());
                    List<String> ciLabels = new ArrayList<>();
                    for (CcCiAttrDef labelDef : labelDefs) {
                        ESCIAttrTransConfig esciAttrTransConfig = defConfigMap.get(labelDef.getId());
                        Object object ;
                        if(esciAttrTransConfig!=null){
                            object = transAttrs.get(esciAttrTransConfig.getShowName().toUpperCase());
                        }else {
                            object = transAttrs.get(labelDef.getProName().toUpperCase());
                        }
                        if (!BinaryUtils.isEmpty(object)) {
                            ciLabels.add(object.toString());
                        }
                    }
                    esciInfo.setCiLabel(JSON.toJSONString(ciLabels));

                    // 查询CI属性中的null值转空串
                    transAttrs.entrySet().forEach(entry -> {
                        if (entry.getValue() == null) {
                            entry.setValue("");
                        }
                    });
                }

                // // 3D attribute path conversion
                // if (isShow3dAttribute) {
                // String proName = "3D模型";
                // Object imagePathFor3d = transAttrs.get(proName);
                // if (imagePathFor3d != null) {
                // String path = String.valueOf(imagePathFor3d);
                // if (!path.startsWith(this.urlPath)) {
                // transAttrs.put(proName, this.urlPath.trim() + path);
                // }
                // }
                // }
                esciInfo.setAttrs(transAttrs);
            }
        }
    }

    /**
     * 提取外部属性关联属性的类型
     * @param esciClassInfo
     * @param esciAttrTransConfigs
     * @param dictDefId
     * @return
     */
    private int extractedExternalType(ESCIClassInfo esciClassInfo, List<ESCIAttrTransConfig> esciAttrTransConfigs, Long dictDefId) {
        Map<Object, ESCIAttrTransConfig> configMap = BinaryUtils.toObjectMap(esciAttrTransConfigs, "defId");
        ESCIAttrTransConfig esciAttrTransConfig = configMap.get(dictDefId);
        Map<Object, ESCIAttrDefInfo> defInfoMap = BinaryUtils.toObjectMap(esciClassInfo.getAttrDefs(), "id");
        ESCIAttrDefInfo esciAttrDefInfo = defInfoMap.get(dictDefId);
        int externalType = esciAttrDefInfo.getProType();
        if (esciAttrTransConfig != null) {
            externalType = esciAttrTransConfig.getTargetAttrType() == null ? esciAttrTransConfig.getSourceAttrType() : esciAttrTransConfig.getTargetAttrType();
        }
        return externalType;

    }

    private Map<String, Object> transCIAttrType(Map<String, Object> attrs, List<CcCiAttrDef> attrDefs, List<ESCIAttrTransConfig> transConfigs) {
        Map<String, Object> esAttr = new HashMap<>();
        if (attrs != null && !attrs.isEmpty()) {
            Map<Long, ESCIAttrTransConfig> attrConfigMap = BinaryUtils.toObjectMap(transConfigs, "defId");
            for (CcCiAttrDef def : attrDefs) {
                ESCIAttrTransConfig attrConfig = attrConfigMap.get(def.getId());
                // 通过中间表映射属性
                if (!BinaryUtils.isEmpty(attrConfig)) {
                    def.setProName(attrConfig.getShowName());
                    def.setProStdName(attrConfig.getShowName().toUpperCase());
                    if (attrConfig.getUpType() > 1) {
                        def.setProType(attrConfig.getTargetAttrType());
                    }
                }
            }
            Map<String, CcCiAttrDef> defMap = BinaryUtils.toObjectMap(attrDefs, "proStdName");
            // DateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            for (Entry<String, Object> entry : attrs.entrySet()) {
                String key = entry.getKey();
                if (BinaryUtils.isEmpty(entry.getValue())) {
                    continue;
                }
                String value = entry.getValue().toString();
                CcCiAttrDef def = defMap.get(key);
                if (!BinaryUtils.isEmpty(def)) {
                    Integer proType = def.getProType();
                    ESCIAttrTransConfig transConfig = attrConfigMap.get(def.getId());
                    if (transConfig != null && transConfig.getTargetAttrType() != null) {
                        proType = transConfig.getTargetAttrType();
                    }
                    ESPropertyType type = ESPropertyType.valueOf(proType);
                    // CI保存前已校验过属性，此处可以直接转换类型
                    switch (type) {
                        case INTEGER_CODE:
                        case PREFIX_INTEGER_CODE:
                        case INTEGER: {
                            esAttr.put(key, Long.valueOf(value));
                            break;
                        }
                        case DOUBLE: {
                            // 此处可以通过new BigDecimal(str2).setScale(4,
                            // BigDecimal.ROUND_HALF_UP).toPlainString()转换
                            // JSON.parseObject默认会使用BigDecimal转换，所以此处可不做
                            esAttr.put(key, Double.valueOf(value));
                            break;
                        }
                        case ENUM:
                        case PICTURE:
                        case DOCUMENT:
                        case VARCHAR:
                        case ATTACHMENT:
                        case LONG_VARCHAR:
                        case CLOB:
                        case PERSION:
                        case EXTERNAL_ATTR: {
                            esAttr.put(key, value);
                            break;
                        }
                        case DATE: {
                            // 日期类型目前未做校验，先按字符串存储
                            Date date = CheckAttrUtil.getDate(value, def.getConstraintRule());
                            if (date != null) {
                                esAttr.put(key, date.getTime());
                            } else {
                                log.error("[" + value + "]转换为时间类型出错！");
                            }
                            // 额外存储日期字符串，用于查询
                            esAttr.put(key + "_date", value);
                            break;
                        }
                        default: {
                            esAttr.put(key, value);
                        }
                    }
                }
            }
        }
        return esAttr;
    }

    private String transSortField(Long domainId, Collection<Long> classIds, String sortField) {
        Assert.isTrue(!BinaryUtils.isEmpty(sortField), "X_PARAM_NOT_NULL${name:sortField}");
        if ("ciCode".equals(sortField)) {
            sortField = "ciCode.keyword";
        }
        // 映射排序字段
        if (sortField.startsWith("attrs.")) {
            // 分类为空时无法定位属性真实名称，默认按修改时间排序
            if (BinaryUtils.isEmpty(classIds)) {
                sortField = "modifyTime";
            }
            // 兼容attrs.属性名.keyword的传参方式
            String[] split = sortField.split("\\.");
            sortField = split.length > 1 ? split[1] : sortField;
            List<ESCIClassInfo> classInfos = classSvc.getTargetAttrDefsByClassIds(domainId, classIds);
            CcCiAttrDef def = null;
            for (ESCIClassInfo clsInfo : classInfos) {
                for (CcCiAttrDef temDef : clsInfo.getCcAttrDefs()) {
                    if (temDef.getProName().equals(sortField)) {
                        def = temDef;
                    }
                }
            }
            if (!BinaryUtils.isEmpty(def)) {
                sortField = "attrs.".concat(def.getProStdName());
                Integer proType = def.getProType();
                if (proType > 2 && proType != ESPropertyType.DATE.getValue()) {
                    sortField += ".keyword";
                }
            } else {
                sortField += ".keyword";
            }
            long count = this.countByCondition(QueryBuilders.existsQuery(sortField));
            if (count <= 0) {
                sortField = "modifyTime";
            }
        } else {
            sortField = ESUtil.underlineToCamel(sortField);
        }
        return sortField;
    }

    /**
     * 转换时保留attrs中的null值
     *
     * @param esciInfos
     * @return
     */
    private JSONArray writeMapNullValueForAttrs(List<ESCIInfo> esciInfos) {
        if (!BinaryUtils.isEmpty(esciInfos)) {
            return JSON.parseArray(JSON.toJSONString(esciInfos, new PropertyFilter() {

                @Override
                public boolean apply(Object object, String name, Object value) {
                    if (!(object instanceof HashMap) && value == null) {
                        return false;
                    }
                    return true;
                }
            }, SerializerFeature.WriteMapNullValue));
        }
        return new JSONArray();
    }

    @Override
    public ESCIInfo getById(Long id) {
        ESCIInfo esciInfo = super.getById(id);
        if (esciInfo != null) {
            this.transCIAttrs(Collections.singletonList(esciInfo), true);
        }
        return esciInfo;
    }

    @Override
    public Page<ESCIInfo> getListByQuery(int pageNum, int pageSize, QueryBuilder query) {
        Page<ESCIInfo> page = super.getListByQuery(pageNum, pageSize, query);
        if (!page.getData().isEmpty()) {
            this.transCIAttrs(page.getData(), true);
        }

        return page;
    }

    @Override
    public List<ESCIInfo> getListByQuery(QueryBuilder query) {
        List<ESCIInfo> list = super.getListByQuery(query);
        // this.transCIAttrs(list, true);
        return list;
    }

    @Override
    public List<ESCIInfo> getListByQueryScroll(QueryBuilder query) {
        List<ESCIInfo> list = super.getListByQueryScroll(query);
        // this.transCIAttrs(list, true);
        return list;
    }

    @Override
    public Page<ESCIInfo> getSortListByQuery(int pageNum, int pageSize, QueryBuilder query, String sortField, boolean isAsc) {
        Page<ESCIInfo> page = super.getSortListByQuery(pageNum, pageSize, query, sortField, isAsc);
        // this.transCIAttrs(page.getData(), true);
        return page;
    }

    @Override
    public Page<ESCIInfo> getSortListByQuery(int pageNum, int pageSize, QueryBuilder query, List<SortBuilder<?>> sorts) {
        Page<ESCIInfo> page = super.getSortListByQuery(pageNum, pageSize, query, sorts);
        this.transCIAttrs(page.getData(), true);
        return page;
    }

    @Override
    public Page<ESCIInfo> getSortListByHighLightQuery(int pageNum, int pageSize, QueryBuilder query, List<SortBuilder<?>> sorts, Collection<String> highLightFields) {
        Page<ESCIInfo> page = super.getSortListByHighLightQuery(pageNum, pageSize, query, sorts, highLightFields);
        this.transCIAttrs(page.getData(), true);
        return page;
    }

    @Override
    public Page<ESCIInfo> getSortListByQuery(int pageNum, int pageSize, QueryBuilder searchQuery, QueryBuilder sortQuery) {
        Page<ESCIInfo> page = super.getSortListByQuery(pageNum, pageSize, searchQuery, sortQuery);
        this.transCIAttrs(page.getData(), true);
        return page;
    }

    @Override
    public Page<ESCIInfo> getSortListByCdt(int pageNum, int pageSize, CCcCi searchCdt, CCcCi sortCdt) {
        Page<ESCIInfo> page = super.getSortListByCdt(pageNum, pageSize, searchCdt, sortCdt);
        // this.transCIAttrs(page.getData(), true);
        return page;
    }

    @Override
    public Page<String> queryAttrVal(Long domainId, ESAttrAggBean bean) {
        if (!BinaryUtils.isEmpty(bean.getAttrName())) {
            BoolQueryBuilder query = QueryBuilders.boolQuery();
            query.must(QueryBuilders.termQuery("classId", bean.getClassId()));
            query.must(QueryBuilders.termQuery("showName.keyword", bean.getAttrName()));
            List<ESCIAttrTransConfig> attrConfigs = attrConfigSvc.getListByQuery(query);
            if (!BinaryUtils.isEmpty(attrConfigs)) {
                ESCIAttrTransConfig attrConfig = attrConfigs.get(0);
                if (attrConfig.getUpType() != null && attrConfig.getUpType().longValue() > 1) {
                    bean.setAttrName("attrs." + attrConfig.getTargetAttrName());
                    bean.setAttrType(attrConfig.getTargetAttrType());
                } else {
                    bean.setAttrName("attrs." + attrConfig.getSourceAttrName().toUpperCase());
                    bean.setAttrType(attrConfig.getSourceAttrType());
                }
            } else {
                List<CcCiAttrDef> defs = classSvc.getAllDefsByClassId(domainId, bean.getClassId());
                Map<Object, CcCiAttrDef> defMap = BinaryUtils.toObjectMap(defs, "proName");
                CcCiAttrDef def = defMap.get(bean.getAttrName());
                Assert.notNull(def, "BS_MNAME_NOT_CI_ATTR_DEF");
                bean.setAttrName("attrs." + def.getProStdName());
                bean.setAttrType(def.getProType());
            }
        }
        List<String> values = super.queryAttrVal(domainId, bean).getData();
       // Assert.isTrue(!BinaryUtils.isEmpty(values),"属性值不存在");
        if(bean.getAttrType()==AttrNameKeyEnum.PICTURE.getType() ){
            values = values.stream().map(imgPath -> urlPath + imgPath).collect(Collectors.toList());
        }
        Page<String> page = new Page<>();
        page.setData(values);
        page.setTotalPages(1);
        page.setPageNum(bean.getPageNum());
        page.setPageSize(bean.getPageSize());
        page.setTotalRows(values.size());
        return page;

    }

    @Override
    public Page<Object> queryAttrValObj(Long domainId, ESAttrAggBean bean) {
        if (!BinaryUtils.isEmpty(bean.getAttrName())) {
            BoolQueryBuilder query = QueryBuilders.boolQuery();
            query.must(QueryBuilders.termQuery("classId", bean.getClassId()));
            query.must(QueryBuilders.termQuery("showName.keyword", bean.getAttrName()));
            List<ESCIAttrTransConfig> attrConfigs = attrConfigSvc.getListByQuery(query);
            if (!BinaryUtils.isEmpty(attrConfigs)) {
                ESCIAttrTransConfig attrConfig = attrConfigs.get(0);
                if (attrConfig.getUpType() != null && attrConfig.getUpType().longValue() > 1) {
                    bean.setAttrName("attrs." + attrConfig.getTargetAttrName());
                    bean.setAttrType(attrConfig.getTargetAttrType());
                } else {
                    bean.setAttrName("attrs." + attrConfig.getSourceAttrName().toUpperCase());
                    bean.setAttrType(attrConfig.getSourceAttrType());
                }
            } else {
                List<CcCiAttrDef> defs = classSvc.getAllDefsByClassId(domainId, bean.getClassId());
                Map<Object, CcCiAttrDef> defMap = BinaryUtils.toObjectMap(defs, "proName");
                CcCiAttrDef def = defMap.get(bean.getAttrName());
                Assert.notNull(def, "BS_MNAME_NOT_CI_ATTR_DEF");
                bean.setAttrName("attrs." + def.getProStdName());
                bean.setAttrType(def.getProType());
            }
        }
        return super.queryAttrValObj(domainId, bean);
    }

    @Override
    public Page<ESCIInfo> getListByCdt(int pageNum, int pageSize, CCcCi obj) {
        Page<ESCIInfo> page = super.getListByCdt(pageNum, pageSize, obj);
        // this.transCIAttrs(page.getData(), true);
        return page;
    }

    @Override
    public Page<ESCIInfo> getSortListByCdt(int pageNum, int pageSize, CCcCi obj, List<SortBuilder<?>> sorts) {
        Page<ESCIInfo> page = super.getSortListByCdt(pageNum, pageSize, obj, sorts);
        // this.transCIAttrs(page.getData(), true);
        return page;
    }

    @Override
    public Page<ESCIInfo> getSortListByCdt(int pageNum, int pageSize, CCcCi obj, String sortField, boolean isAsc) {
        Page<ESCIInfo> page = super.getSortListByCdt(pageNum, pageSize, obj, sortField, isAsc);
        // this.transCIAttrs(page.getData(), true);
        return page;
    }

    @Override
    public List<ESCIInfo> getListByCdt(CCcCi obj) {
        List<ESCIInfo> list = super.getListByCdt(obj);
        // this.transCIAttrs(list, true);
        return list;
    }

    @Override
    public List<ESCIInfo> getSortListByCdt(CCcCi obj, List<SortBuilder<?>> sorts) {
        List<ESCIInfo> list = super.getSortListByCdt(obj, sorts);
        // this.transCIAttrs(list, true);
        return list;
    }

    @Override
    public Long saveOrUpdate(ESCIInfo esciInfo) {
        this.transCIAttrs(Collections.singletonList(esciInfo), false);
        ciHistorySvc.saveOrUpdateHistoryInfo(esciInfo, ESCIHistoryInfo.ActionType.SAVE_OR_UPDATE);
        savePreOption(esciInfo);
        // SerializerFeature.WriteMapNullValue保留CI属性中的null值
        JSONObject attrObject = JSON.parseObject(JSON.toJSONString(esciInfo.getAttrs(), SerializerFeature.WriteMapNullValue));
        JSONObject json = JSON.parseObject(JSON.toJSONString(esciInfo));
        json.fluentPut("attrs", attrObject);
        return super.saveOrUpdate(json, true);
    }

    @Override
    public Integer saveOrUpdateBatch(List<ESCIInfo> list) {
        return saveOrUpdateBatch(list,Boolean.TRUE);
    }

    public Integer saveOrUpdateBatch(List<ESCIInfo> list,boolean isRefresh) {
        SysUser loginUser = null;
        try {
            loginUser = SysUtil.getCurrentUserInfo();
        } catch (Exception e) {
        }
        for (ESCIInfo esCI : list) {
            if (esCI.getId() == null) {
                esCI.setId(ESUtil.getUUID());
                esCI.setCreator(loginUser == null ? "system" : loginUser.getLoginCode());
            }
            if (BinaryUtils.isEmpty(esCI.getCiCode())) {
                esCI.setCiCode(String.valueOf(esCI.getId()));
            }
        }
        this.transCIAttrs(list, false);
        list.forEach(obj -> savePreOption(obj));
        ciHistorySvc.saveOrUpdateHistoryInfosBatch(list, ESCIHistoryInfo.ActionType.SAVE_OR_UPDATE);
        return saveOrUpdateBatch(this.writeMapNullValueForAttrs(list), isRefresh);
    }


    @Override
    public Map<String, Page<ESCIInfo>> getScrollByQuery(int pageNum, int pageSize, QueryBuilder query, String sortField, boolean isAsc) {
        Map<String, Page<ESCIInfo>> map = super.getScrollByQuery(pageNum, pageSize, query, sortField, isAsc);
        for (Entry<String, Page<ESCIInfo>> stringPageEntry : map.entrySet()) {
            this.transCIAttrs(stringPageEntry.getValue().getData(), true);
        }
        return map;
    }

    @Override
    public boolean existByCondition(QueryBuilder query) {
        return super.existByCondition(query);
    }

    @Override
    public long countByCondition(QueryBuilder query) {
        return super.countByCondition(query);
    }

    @Override
    public List<ESCIInfo> getListByScroll(String scrollId) {
        List<ESCIInfo> list = super.getListByScroll(scrollId);
        if (!list.isEmpty()) {
            this.transCIAttrs(list, true);
        }

        return list;
    }

    /**
     * 根据属性名称清除对应分类CI的属性值
     *
     * @param classId
     * @param proName
     */
    public void clearCIAttrByProName(Long classId, String proName) {
        ESCIClassInfo cls = classSvc.getTargetAttrDefsByClassId(classId);
        for (CcCiAttrDef def : cls.getCcAttrDefs()) {
            if (def.getProName().equals(proName)) {
                Set<Long> classIds = new HashSet<>();
                classIds.add(classId);
                if (cls.getParentId().longValue() != 0L) {
                    // 子类属性值也要判断
                    classIds.add(cls.getParentId());
                }
                BoolQueryBuilder query = QueryBuilders.boolQuery();
                query.should(QueryBuilders.termsQuery("classId", classIds));
                this.updateByQuery(query, "ctx._source.attrs.remove('" + def.getProStdName() + "')", false);
            }
        }
    }

    /**
     * 刷新CI主键
     *
     * @param classId          分类id
     * @param ciPKAttrDefNames 所有主键属性名称
     */
    public void updateCIPrimaryKeys(Long classId, List<String> ciPKAttrDefNames) {
        ESCIClassInfo classInfo = classSvc.getTargetAttrDefsByClassId(classId);
        Map<String, CcCiAttrDef> defMap = BinaryUtils.toObjectMap(classInfo.getCcAttrDefs(), "proName");
        String primaryKeyScript = "ctx._source.ciPrimaryKey='[";
        for (String name : ciPKAttrDefNames) {
            CcCiAttrDef def = defMap.get(name);
            Assert.notNull(def, "主键定义不存在");
            if (ciPKAttrDefNames.indexOf(name) > 0) {
                primaryKeyScript += ",";
            }
            primaryKeyScript += "\"'+ctx._source.attrs['" + def.getProStdName() + "']+'\"";
        }
        Set<Long> classIds = new HashSet<>();
        classIds.add(classId);
        if (classInfo.getParentId().longValue() != 0L) {
            // 子类属性值也要判断
            classIds.add(classInfo.getParentId());
        }
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.should(QueryBuilders.termsQuery("classId", classIds));
        this.updateByQuery(query, primaryKeyScript + "]'", false);
    }

    /**
     * 删除所有的的CI
     *
     * @param query
     * @return
     */
    public Integer removeAllCI(QueryBuilder query) {
        return super.deleteByQuery(query, true);
    }

    /**
     * 获取指定分类的指定属性定义下的CI attrvalues
     * @param domainId
     * @param classId
     * @param attrNames
     * @param needPage
     * @param searchBean
     * @return
     */
    public Page<String> queryAttrVal(Long domainId, Long classId, List<String> attrNames, boolean needPage, ESAttrAggBean searchBean) {
        List<ESCIInfo> esciInfos;
        if (needPage) {
            trans(attrNames, classId);
            ESCISearchBean bean = new ESCISearchBean();
            bean.setDomainId(domainId);
            bean.setClassIds(Arrays.asList(classId));
            if (StringUtils.isNotBlank(searchBean.getLike())) {
                List<ESAttrBean> orAttrs = new ArrayList<>();
                for (String attrName : attrNames) {
                    ESAttrBean attrBean = new ESAttrBean();
                    attrBean.setKey(attrName);
                    attrBean.setOptType(2);
                    attrBean.setValue(searchBean.getLike());
                    orAttrs.add(attrBean);
                }
                bean.setOrAttrs(orAttrs);
            }
            QueryBuilder query = commSvc.getCIQueryBuilderByBean(bean);
            Page<ESCIInfo> page = super.getSortListByQuery(searchBean.getPageNum(),
                    searchBean.getPageSize(), query, "modifyTime", false);
            esciInfos = page.getData();
        } else {
            searchBean.setPageNum(1);
            searchBean.setPageSize(20);
            BoolQueryBuilder query = QueryBuilders.boolQuery();
            query.must(QueryBuilders.termQuery("domainId", domainId));
            query.must(QueryBuilders.termQuery("classId", classId));
            esciInfos = super.getListByQueryScroll(query);
        }
        List<String> values = new ArrayList<>();
        if (CollectionUtils.isEmpty(esciInfos)) {
            return new Page<>(searchBean.getPageNum(), searchBean.getPageSize(), 0, 0, new ArrayList<>());
        }

        for (ESCIInfo esciInfo : esciInfos) {
            Map<String, Object> attrs = esciInfo.getAttrs();
            if (CollectionUtils.isEmpty(attrs)) {
                continue;
            }
            List<String> attrValues = new ArrayList<>();
            for (String attrName : attrNames) {
                Object value = attrs.get(attrName);
                if (BinaryUtils.isEmpty(value)) {
                    continue;
                }
                attrValues.add(value.toString());
            }
            if (!CollectionUtils.isEmpty(attrValues)) {
                values.add(String.join(CiClassProDropSourceDefHelper.DEF_PRONAME_SEPARATOR, attrValues));
            }
        }
        long totalCount = values.size();
        int pageSize = searchBean.getPageSize();
        long totalPages = totalCount % pageSize;
        long resultTotalPages;
        if (totalPages == 0) {
            resultTotalPages = totalCount / pageSize;
        } else {
            resultTotalPages = totalCount / pageSize + 1;
        }
        return new Page<>(searchBean.getPageNum(), searchBean.getPageSize(), totalCount, resultTotalPages, values);
    }

    private void trans(List<String> attrNames, Long classId) {
        if (!BinaryUtils.isEmpty(attrNames)) {
            BoolQueryBuilder attrConfigQuery = QueryBuilders.boolQuery();
            attrConfigQuery.must(QueryBuilders.termQuery("classId", classId));
            List<ESCIAttrTransConfig> attrConfigs = attrConfigSvc.getListByQuery(attrConfigQuery);
            if (!BinaryUtils.isEmpty(attrConfigs)) {
                for (ESCIAttrTransConfig attrConfig : attrConfigs) {
                    String attrName = "";
                    if (attrConfig.getUpType() != null && attrConfig.getUpType().longValue() > 1) {
                        attrName = attrConfig.getTargetAttrName();
                    } else {
                        attrName = attrConfig.getSourceAttrName();
                    }
                    if (!attrNames.contains(attrName)) {
                        continue;
                    }
                    //维护原排序
                    int index = attrNames.indexOf(attrName);
                    //维护变更前的属性名称
                    attrNames.add(index, attrConfig.getShowName());
                    attrNames.remove(attrName);
                }
            }
        }
    }

    public void transCIHistoryAttrs(List<ESCIHistoryInfo> historyInfos, boolean changeToShowName) {
        if (!BinaryUtils.isEmpty(historyInfos)) {
            Set<Long> classIds = historyInfos.stream().map(ESCIHistoryInfo::getClassId).filter(Objects::nonNull).collect(Collectors.toSet());
            //查分类
            List<ESCIClassInfo> ciClassInfos = classSvc.getListByQuery(0, classIds.size(),
                    QueryBuilders.boolQuery().must(QueryBuilders.termsQuery("id", classIds))).getData();
            Map<Long, ESCIClassInfo> parentCiClassInfoMap = getParentCiClassMap(classIds, ciClassInfos);
            // 获取映射属性
            Map<Long, List<ESCIAttrTransConfig>> attrConfigMap = getCiAttrTransConfig(classIds);
            // 组装分类id与其所有属性的对应关系，包括父级属性
            Map<Long, List<CcCiAttrDef>> clsAlldefsMap = getClsAlldefsMap(ciClassInfos, parentCiClassInfoMap, attrConfigMap);
            // CI属性转换
            for (ESCIHistoryInfo esciInfo : historyInfos) {
                tnarsAttr(changeToShowName, attrConfigMap, clsAlldefsMap, esciInfo);
            }
        }
    }

    private Map<Long, List<CcCiAttrDef>> getClsAlldefsMap(List<ESCIClassInfo> ciClassInfos, Map<Long, ESCIClassInfo> parentCiClassInfoMap, Map<Long, List<ESCIAttrTransConfig>> attrConfigMap) {
        Map<Long, List<CcCiAttrDef>> clsAlldefsMap = new HashMap<>();
        for (ESCIClassInfo classInfo : ciClassInfos) {
            clsAlldefsMap.put(classInfo.getId(), classInfo.getCcAttrDefs());
            if (classInfo.getParentId() != null && classInfo.getParentId() != 0L) {
                ESCIClassInfo parClsInfo = parentCiClassInfoMap.get(classInfo.getParentId());
                if (parClsInfo == null) {
                    continue;
                }
                clsAlldefsMap.get(classInfo.getId()).addAll(parClsInfo.getCcAttrDefs());
                List<ESCIAttrTransConfig> parAttrConfigs = attrConfigMap.get(parClsInfo.getId());
                if (!BinaryUtils.isEmpty(parAttrConfigs)) {
                    if (!attrConfigMap.containsKey(classInfo.getId())) {
                        attrConfigMap.put(classInfo.getId(), new ArrayList<>());
                    }
                    attrConfigMap.get(classInfo.getId()).addAll(parAttrConfigs);
                }
            }
        }
        return clsAlldefsMap;
    }

    private Map<Long, ESCIClassInfo> getParentCiClassMap(Set<Long> classIds, List<ESCIClassInfo> ciClassInfos) {
        Map<Long, ESCIClassInfo> parentCiClassInfoMap = new HashMap<>();
        //父分类ids
        Set<Long> parentCiClassIds = ciClassInfos.stream()
                .filter(c -> c.getParentId() != null && c.getParentId() != 0L)
                .map(ESCIClassInfo::getParentId).collect(Collectors.toSet());
        if (!BinaryUtils.isEmpty(parentCiClassIds)) {
            List<ESCIClassInfo> parentCiClassInfos = classSvc.getListByQuery(0, classIds.size(),
                    QueryBuilders.boolQuery().must(QueryBuilders.termsQuery("id", parentCiClassIds))).getData();
            parentCiClassInfoMap = parentCiClassInfos.stream().collect(Collectors.toMap(ESCIClassInfo::getId, e -> e));
            classIds.addAll(parentCiClassIds);
        }
        return parentCiClassInfoMap;
    }

    private Map<Long, List<ESCIAttrTransConfig>> getCiAttrTransConfig(Set<Long> classIds) {
        // 获取属性中间表记录，用于映射属性
        List<ESCIAttrTransConfig> attrConfigs = attrConfigSvc.getListByQuery(
                QueryBuilders.boolQuery().must(QueryBuilders.termsQuery("classId", classIds)));
        Map<Long, List<ESCIAttrTransConfig>> attrConfigMap = BinaryUtils.toObjectGroupMap(attrConfigs, "classId");
        return attrConfigMap;
    }

    private void tnarsAttr(boolean changeToShowName, Map<Long, List<ESCIAttrTransConfig>> attrConfigMap, Map<Long, List<CcCiAttrDef>> clsAlldefsMap, ESCIHistoryInfo esciInfo) {
        List<CcCiAttrDef> defs = clsAlldefsMap.get(esciInfo.getClassId());
        // 判断CI所属分类存在，避免脏数据
        Assert.notEmpty(defs, "BS_MNAME_CLASS_NOT_EXSIT");
        List<ESCIAttrTransConfig> transConfigs = attrConfigMap.get(esciInfo.getClassId());
        Map<Object, ESCIAttrTransConfig> defConfigMap = BinaryUtils.toObjectMap(transConfigs, "defId");
        // 属性类型转换
        if (!changeToShowName) {
            esciInfo.setAttrs(this.transCIAttrType(esciInfo.getAttrs(), defs, transConfigs));
        }
        // 原属性map
        Map<String, Object> attrs = esciInfo.getAttrs();
        // 只返回定义过的属性map
        Map<String, Object> transAttrs = getCiAttrDefMap(changeToShowName, defs, defConfigMap, attrs);
        // 丰富ciLabel
        if (changeToShowName) {
            List<CcCiAttrDef> labelDefs = defs.stream().filter(def -> def.getIsCiDisp() != null && def.getIsCiDisp() == 1).collect(Collectors.toList());
            List<String> ciLabels = new ArrayList<>();
            for (CcCiAttrDef labelDef : labelDefs) {
                ESCIAttrTransConfig esciAttrTransConfig = defConfigMap.get(labelDef.getId());
                Object object ;
                if(esciAttrTransConfig!=null){
                    object = transAttrs.get(esciAttrTransConfig.getShowName().toUpperCase());
                }else {
                    object = transAttrs.get(labelDef.getProName().toUpperCase());
                }
                if (!BinaryUtils.isEmpty(object)) {
                    ciLabels.add(object.toString());
                }
            }
            esciInfo.setCiLabel(JSON.toJSONString(ciLabels));

            // 查询CI属性中的null值转空串
            transAttrs.entrySet().forEach(entry -> {
                if (entry.getValue() == null) {
                    entry.setValue("");
                }
            });
        }

        // // 3D attribute path conversion
        // if (isShow3dAttribute) {
        // String proName = "3D模型";
        // Object imagePathFor3d = transAttrs.get(proName);
        // if (imagePathFor3d != null) {
        // String path = String.valueOf(imagePathFor3d);
        // if (!path.startsWith(this.urlPath)) {
        // transAttrs.put(proName, this.urlPath.trim() + path);
        // }
        // }
        // }
        esciInfo.setAttrs(transAttrs);
    }

    private Map<String, Object> getCiAttrDefMap(boolean changeToShowName, List<CcCiAttrDef> defs, Map<Object, ESCIAttrTransConfig> defConfigMap, Map<String, Object> attrs) {
        Map<String, Object> transAttrs = new HashMap<>();
        for (CcCiAttrDef def : defs) {

            int currentType = def.getProType();
            ESCIAttrTransConfig attrConfig = defConfigMap.get(def.getId());
            if (!BinaryUtils.isEmpty(attrConfig)) {
                //由于修改时不改原分类数据中的属性，因此这里如果有映射属性，则从映射属性中取
                currentType = attrConfig.getTargetAttrType() == null ? attrConfig.getSourceAttrType() : attrConfig.getTargetAttrType();

                int targetType = attrConfig.getTargetAttrType() != null ? attrConfig.getTargetAttrType() : def.getProType();
                // 转换为显示名称
                if (changeToShowName) {
                    // 属性名称转换，修改过属性类型，实际属性值存储在目标字段中
                    if (!BinaryUtils.isEmpty(attrConfig.getTargetAttrName())) {
                        Object object = attrs.get(attrConfig.getTargetAttrName());
                        // attrs.remove(attrConfig.getTargetAttrName());
                        transAttrs.put(attrConfig.getShowName().toUpperCase(), object);
                        if (!BinaryUtils.isEmpty(object)) {
                            // 转换时间显示格式
                            if (targetType == AttrNameKeyEnum.DATE.getType()) {
                                Object dateStr = attrs.get(attrConfig.getTargetAttrName().concat("_date"));
                                if (dateStr != null) {
                                    transAttrs.put(attrConfig.getShowName().toUpperCase(), dateStr);
                                } else {
                                    transAttrs.put(attrConfig.getShowName().toUpperCase(), object);
                                }
                                // try {
                                // long val = Long.parseLong(object.toString());
                                // object = format.format(val);
                                // } catch (Exception e) {
                                // log.error("时间类型转换异常，保留原值返回[" + object + "]");
                                // }
                                // transAttrs.put(attrConfig.getShowName().toUpperCase(), object);
                            }
                            String transVal = object.toString();
                            if (targetType == AttrNameKeyEnum.DOUBLE.getType()) {
                                try {
                                    transVal = new BigDecimal(object.toString()).toPlainString();
                                } catch (Exception e) {
                                }
                                transAttrs.put(attrConfig.getShowName().toUpperCase(), transVal);
                            }
                        }
                    } else {
                        Object object = attrs.get(attrConfig.getSourceAttrName().toUpperCase());
                        // attrs.remove(attrConfig.getSourceAttrName().toUpperCase());
                        transAttrs.put(attrConfig.getShowName().toUpperCase(), object);
                        if (!BinaryUtils.isEmpty(object)) {
                            // 转换时间显示格式
                            if (targetType == AttrNameKeyEnum.DATE.getType()) {
                                Object dateStr = attrs.get(attrConfig.getSourceAttrName().toUpperCase().concat("_date"));
                                if (dateStr != null) {
                                    transAttrs.put(attrConfig.getShowName().toUpperCase(), dateStr);
                                } else {
                                    transAttrs.put(attrConfig.getShowName().toUpperCase(), object);
                                }
                                // try {
                                // long val = Long.parseLong(object.toString());
                                // object = format.format(val);
                                // } catch (Exception e) {
                                // log.error("时间类型转换异常，保留原值返回[" + object + "]");
                                // }
                                // transAttrs.put(attrConfig.getShowName().toUpperCase(), object);
                            }
                            String transVal = object.toString();
                            if (targetType == AttrNameKeyEnum.DOUBLE.getType()) {
                                try {
                                    transVal = new BigDecimal(object.toString()).toPlainString();
                                } catch (Exception e) {
                                }
                                transAttrs.put(attrConfig.getShowName().toUpperCase(), transVal);
                            }
                        }
                    }
                } else {
                    // 转换为实际存储名称
                    if (!BinaryUtils.isEmpty(attrConfig.getTargetAttrName())) {
                        Object object = attrs.get(attrConfig.getShowName().toUpperCase());
                        // attrs.remove(attrConfig.getShowName().toUpperCase());
                        transAttrs.put(attrConfig.getTargetAttrName(), object);
                        if (targetType == AttrNameKeyEnum.DATE.getType()) {
                            // attrs.remove(attrConfig.getShowName().toUpperCase() + "_date");
                            transAttrs.put(attrConfig.getTargetAttrName() + "_date", attrs.get(attrConfig.getShowName().toUpperCase() + "_date"));
                        }
                    } else {
                        Object object = attrs.get(attrConfig.getShowName().toUpperCase());
                        // attrs.remove(attrConfig.getShowName().toUpperCase());
                        transAttrs.put(attrConfig.getSourceAttrName().toUpperCase(), object);
                        if (targetType == AttrNameKeyEnum.DATE.getType()) {
                            // attrs.remove(attrConfig.getShowName().toUpperCase() + "_date");
                            transAttrs.put(attrConfig.getSourceAttrName().toUpperCase() + "_date", attrs.get(attrConfig.getShowName().toUpperCase() + "_date"));
                        }
                    }
                }
            } else {
                Object object = attrs.get(def.getProName().toUpperCase());
                transAttrs.put(def.getProName().toUpperCase(), object);
                if (!BinaryUtils.isEmpty(object)) {
                    // 转换时间显示格式
                    if (def.getProType() == AttrNameKeyEnum.DATE.getType()) {
                        if (changeToShowName) {
                            transAttrs.put(def.getProName().toUpperCase(), attrs.get(def.getProName().toUpperCase().concat("_date")));
                        } else {
                            transAttrs.put(def.getProName().toUpperCase() + "_date", attrs.get(def.getProName().toUpperCase().concat("_date")));

                        }
                        // try {
                        // long val = Long.parseLong(object.toString());
                        // object = format.format(val);
                        // } catch (Exception e) {
                        // log.error("时间类型转换异常，保留原值返回[" + object + "]");
                        // }
                        // transAttrs.put(def.getProName().toUpperCase(), object);
                    }
                    if (changeToShowName) {
                        // 丰富ciLabel字段
                        String transVal = object.toString();
                        if (def.getProType() == AttrNameKeyEnum.DOUBLE.getType()) {
                            try {
                                transVal = new BigDecimal(object.toString()).toPlainString();
                            } catch (Exception e) {
                            }
                            transAttrs.put(def.getProName().toUpperCase(), transVal);
                        }
                    }
                }
            }

            // 丰富图片类型Url地址
            if (changeToShowName) {

                //外部属性处理
//                        if (currentType == AttrNameKeyEnum.EXTERNAL_ATTR.getType()) {
//                            Map dictMap = JSON.parseObject(def.getProDropSourceDef().trim(), Map.class);
//                            Long dictClassId = Conver.to(dictMap.keySet().iterator().next(), Long.class);
//                            Long dictDefId = Conver.to(dictMap.values().iterator().next(), Long.class);
//                            currentType = extractedExternalType(clsMap.get(dictClassId), attrConfigMap.get(dictClassId), dictDefId);
//                        }
                String proName = def.getProName();
                if (currentType == AttrNameKeyEnum.PICTURE.getType()
                        || (isShow3dAttribute && currentType == AttrNameKeyEnum.MODEL.getType()) || currentType == AttrNameKeyEnum.DOCUMENT.getType()) {
                    Object attrValueForImg = transAttrs.get(proName.toUpperCase());
                    if (null != attrValueForImg && !"".equals(attrValueForImg)) {
                        String path = attrValueForImg.toString();
                        if (!path.startsWith(this.urlPath)) {
                            transAttrs.put(proName.toUpperCase(), this.urlPath.trim() + path);
                        }
                    }
                }

            }
        }
        return transAttrs;
    }
}
