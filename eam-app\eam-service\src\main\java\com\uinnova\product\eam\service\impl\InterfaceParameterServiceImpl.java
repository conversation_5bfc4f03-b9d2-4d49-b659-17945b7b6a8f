package com.uinnova.product.eam.service.impl;

import com.google.common.collect.Lists;
import com.uinnova.product.eam.comm.model.es.InterfaceParameter;
import com.uinnova.product.eam.service.InterfaceParameterService;
import com.uinnova.product.eam.service.es.InterfaceParameterDao;
import com.uino.dao.util.ESUtil;
import com.uino.util.sys.SysUtil;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class InterfaceParameterServiceImpl implements InterfaceParameterService {


    @Autowired
    private InterfaceParameterDao interfaceParameterDao;

    @Override
    public Integer save(List<InterfaceParameter> interfaceParameterList) {
       //处理前端传入的参数，只保存用户自己新创建的数据
        List<InterfaceParameter> filterResult = filterMethod(interfaceParameterList);
        for (InterfaceParameter interfaceParameter : filterResult) {
            if (interfaceParameter.getId() == null) {
                interfaceParameter.setId(ESUtil.getUUID());
            }
            interfaceParameter.setCreateUser(SysUtil.getCurrentUserInfo().getLoginCode());
            interfaceParameter.setCreateTime(ESUtil.getNumberDateTime());
            interfaceParameter.setModifyTime(ESUtil.getNumberDateTime());
            interfaceParameter.setCreateUser(SysUtil.getCurrentUserInfo().getLoginCode());
        }
        Integer result = interfaceParameterDao.saveOrUpdateBatch(interfaceParameterList);
        return result;
    }

    private List<InterfaceParameter> filterMethod(List<InterfaceParameter> interfaceParameterList) {
        List<InterfaceParameter> filterIsFromStandard = interfaceParameterList.stream().filter(item -> (item.getIsFromStandard() != true)).collect(Collectors.toList());
        List<InterfaceParameter> filterSpecialData = filterIsFromStandard.stream().filter(item -> ((!item.getName().equals("root")) && (!item.getName().equals("items")))).collect(Collectors.toList());
        if (filterSpecialData == null || filterSpecialData.size() < 1) {
            return Lists.newArrayList();
        }
        String loginCode = SysUtil.getCurrentUserInfo().getLoginCode();
        BoolQueryBuilder queryCreate = QueryBuilders.boolQuery();
        queryCreate.must(QueryBuilders.termQuery("isFromStandard",false));
        queryCreate.must(QueryBuilders.termQuery("parameterType",filterSpecialData.get(0).getParameterType()));
        queryCreate.must(QueryBuilders.termQuery("createUser.keyword",loginCode));
        List<InterfaceParameter> createParameter = interfaceParameterDao.getListByQuery(queryCreate);
        Set<String> createdData = createParameter.stream().map(InterfaceParameter::getName).collect(Collectors.toSet());
        List<InterfaceParameter> filterData = filterSpecialData.stream().filter(item -> !createdData.contains(item.getName())).collect(Collectors.toList());
        return filterData;
    }

    @Override
    public Map<String,List<InterfaceParameter>> getList(Integer parameterType,Integer requestMethod) {
        //首先查看标准参数数据
        BoolQueryBuilder queryStandard = QueryBuilders.boolQuery();
        queryStandard.must(QueryBuilders.termQuery("isFromStandard",true));
        queryStandard.must(QueryBuilders.termQuery("parameterType",parameterType));
        List<InterfaceParameter> standardParameter = interfaceParameterDao.getListByQuery(queryStandard);

        String loginCode = SysUtil.getCurrentUserInfo().getLoginCode();
        BoolQueryBuilder queryCreate = QueryBuilders.boolQuery();
        queryCreate.must(QueryBuilders.termQuery("isFromStandard",false));
        queryCreate.must(QueryBuilders.termQuery("parameterType",parameterType));
        //queryCreate.must(QueryBuilders.termQuery("createUser.keyword",loginCode));
        List<InterfaceParameter> createParameter = interfaceParameterDao.getListByQuery(queryCreate);
        Map<String,List<InterfaceParameter>> result = new HashMap<>();
        if (requestMethod == 2) {
            result.put("standard",standardParameter);
            result.put("create",createParameter);
            return result;
        }else{
            List<InterfaceParameter> getMethodStandard = standardParameter.stream().filter(item -> !item.getType().contains("Items") && !item.getType().contains("Object") && !item.getType().contains("Array")).collect(Collectors.toList());
            List<InterfaceParameter> getMethodCreate = createParameter.stream().filter(item -> !item.getType().contains("Items") && !item.getType().contains("Object") && !item.getType().contains("Array")).collect(Collectors.toList());
            result.put("standard",getMethodStandard);
            result.put("create",getMethodCreate);
            return result;
        }
    }

    @Override
    public Integer deleteById(Long id) {
        return interfaceParameterDao.deleteById(id);
    }

    @Override
    public List<InterfaceParameter> getChildListByCode(String parentCode) {
        List<InterfaceParameter> result = Lists.newArrayList();
        List<InterfaceParameter> mildResult = Lists.newArrayList();
        InterfaceParameter interfaceParameter = new InterfaceParameter();
        interfaceParameter.setParentId(parentCode);
        List<InterfaceParameter> interfaceParameterList = interfaceParameterDao.getListByCdt(interfaceParameter);
        if (interfaceParameterList == null || interfaceParameterList.size() < 1) {
            return null;
        }
        result.addAll(interfaceParameterList);
        mildResult.addAll(interfaceParameterList);
        findChildData(result,mildResult);
        return result;
    }

    private void findChildData(List<InterfaceParameter> result, List<InterfaceParameter> mildResult) {
        if (mildResult == null || mildResult.size() < 1) {
            return;
        }
        for (InterfaceParameter interfaceParameter : mildResult) {
            InterfaceParameter child = new InterfaceParameter();
            child.setParentId(interfaceParameter.getCode());
            List<InterfaceParameter> interfaceParameterList = interfaceParameterDao.getListByCdt(child);
            result.addAll(interfaceParameterList);
            findChildData(result,interfaceParameterList);
        }
    }


}
