package com.uinnova.product.eam.service.manage;

import com.binary.jdbc.Page;
import com.uinnova.product.eam.comm.dto.EamMatrixQueryVO;
import com.uinnova.product.eam.comm.model.es.EamMatrixInstanceData;
import com.uinnova.product.eam.comm.model.es.EamMatrixInstance;
import com.uinnova.product.eam.comm.model.es.EamMatrixStencil;
import com.uino.bean.cmdb.base.LibType;

import java.util.List;
import java.util.Set;

public interface EamMatrixDataSvc {

    EamMatrixInstanceData getById(Long id, LibType libType);

    /**
     * 删除矩阵表格数据
     * @param tableId 表格id
     * @param libType 库
     * @return 删除结果
     */
    Integer deleteByTableId(Long tableId, LibType libType);

    /**
     * 更新矩阵表格数据
     * @param instance 矩阵表格基本信息
     * @param table 表格数据
     * @param matrixStencil 矩阵模板
     * @param ownerCode 所有者
     */
    void updateTableInfo(EamMatrixInstance instance, List<EamMatrixInstanceData> table, EamMatrixStencil matrixStencil, String ownerCode);

    /**
     * 按条件获取矩阵表格基本数据
     * @param tableId 表格id
     * @param ciCodes ciCode
     * @param libType 库
     * @return 表格数据
     */
    List<EamMatrixInstanceData> getTableData(Long tableId, Set<String> ciCodes, LibType libType);

    /**
     * 获取矩阵表格数据
     * @param tableId 表格id
     * @param libType 库
     * @return 表格数据
     */
    List<EamMatrixInstanceData> getTableData(Long tableId, LibType libType);

    /**
     * 按条件获取矩阵表格数据
     * @param queryVO 查询条件
     * @return Page<EamMatrixInstanceData>
     */
    Page<EamMatrixInstanceData> getTableData(EamMatrixQueryVO queryVO);

    /**
     * 设置矩阵表格行数据，架构设计保证和私有库数据一致,填充table
     * @param tableList 表格数据
     * @param matrixId 矩阵制品id
     * @param ownerCode 用户标识
     */
    void setTableData(List<EamMatrixInstanceData> tableList, Long matrixId, String ownerCode, LibType libType);

    /**
     * 查询表格信息信息
     * @param tableIds 表格iD
     * @param libType 库
     * @return List<EamMatrixInstanceData>
     */
    List<EamMatrixInstanceData> selectByTableIds(List<Long> tableIds, LibType libType);

    /**
     * 批量更新表格信息
     * @param matrixTable matrixTable
     * @param libType libType
     * @return Long
     */
    Integer saveOrUpdateMatrixTable(List<EamMatrixInstanceData> matrixTable, LibType libType);

    /**
     * 删除矩阵表格数据
     * @param matrixTable matrixTable
     * @param libType libType
     */
    void deleteBatchTableDataByIds(List<Long> matrixTable, LibType libType);
}
