package com.uinnova.product.eam.model;

import java.io.Serializable;
import java.util.List;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.comm.model.VcDiagramTag;
import com.uinnova.product.eam.comm.model.VcTag;

@Comment("标签信息")
public class VcTagInfo implements Serializable{
	private static final long serialVersionUID = 1L;
	
	@Comment("标签详情")
	private VcTag tag;
	
	
	@Comment("标签视图关联信息")
	private List<VcDiagramTag> diagramTags;


	public VcTag getTag() {
		return tag;
	}


	public void setTag(VcTag tag) {
		this.tag = tag;
	}


	public List<VcDiagramTag> getDiagramTags() {
		return diagramTags;
	}


	public void setDiagramTags(List<VcDiagramTag> diagramTags) {
		this.diagramTags = diagramTags;
	}
	
}
