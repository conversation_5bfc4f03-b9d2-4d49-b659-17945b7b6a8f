package com.uino.service.cmdb.dataset.microservice;

import com.uino.bean.cmdb.base.dataset.batch.DataSetExeResultSheet;

import java.util.List;

/**
 * @Title: IDataSetExeResultApiSvc
 * @Description:
 * @Author: YGQ
 * @Create: 2021-05-31 15:55
 **/
public interface IDataSetExeResultSvc {

    /**
     * save or update exe result by batch
     *
     * @param dataSetExeResultSheets execute result
     */
    void saveOrUpdateBatch(List<DataSetExeResultSheet> dataSetExeResultSheets);

    /**
     * delete by query
     *
     * @param dataSetId data set id
     */
    void deleteByQuery(Long dataSetId);
}
