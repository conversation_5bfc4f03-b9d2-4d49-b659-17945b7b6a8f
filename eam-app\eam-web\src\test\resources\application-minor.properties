# 端口
server.port=1515
# 服务名
spring.application.name=eam-fuxi
## 数据公共属性(连接池配置)
ds.conn.pool.initialSize=0
ds.conn.pool.maxActive=20
ds.conn.pool.maxWait=30000
ds.conn.pool.maxIdle=20
ds.conn.pool.minIdle=0
ds.conn.pool.validationQuery=select 1
ds.conn.pool.maxOpenPreparedStatements=500
ds.jdbc.vmdb.printtype=NONE
ds.jdbc.vmdb.writertype=CONSOLE
ds.jdbc.vmdb.dsname=ds_vmdb
ds.jdbc.diagram.dsname=ds_vmdb
ds.jdbc.sys.dsname=ds_vmdb
ds.jdbc.monitor.dsname=ds_vmdb
ds.jdbc.monitor.emv.dsname=ds_vmdb
ds.jdbc.dcv.dsname=ds_vmdb
ds.jdbc.vmdb.dstype=MySQL5
ds.jdbc.vmdb.driver=com.mysql.cj.jdbc.Driver
ds.jdbc.vmdb.validationQuery=select 1 from dual
# 禁用Spring Sleuth功能, 此设置关闭了Spring Sleuth的自动追踪机制，以减少不必要的性能开销
spring.sleuth.enabled=false
spring.sleuth.sampler.percentage=0.1
# 熔断与监控
feign.hystrix.enabled=false
# 请求连接的超时时间
feign.client.config.default.connectTimeout=120000
# 请求处理的超时时间
feign.client.config.default.readTimeout=120000
# cloud是否传递上下文
hystrix.shareSecurityContext=true
#hystrix.command.default.execution.isolation.strategy = SEMAPHORE
# 是否压缩request
feign.compression.request.enabled=true
# 是否压缩response
feign.compression.response.enabled=true
# pv缓存路径
dmv.cache.pv.path=/uinnova/tarsier/data/DMV/cache/pvCountTemp.cache
# 上传文件大小限制
spring.servlet.multipart.max-file-size=20MB
spring.servlet.multipart.max-request-size=100MB
#spring.mvc.resources.static-locations=file:./src/main/webapp
server.tomcat.basedir=./tmp
# 是否覆盖同名bean
spring.main.allow-bean-definition-overriding=true
# 同步请求超时时间，单位毫秒
spring.mvc.async.request-timeout=1000000
spring.devtools.restart.enabled=false
spring.main.lazy-initialization=false
# License校验filter
license.ignore.filter.pattern=**.mp3;**.mp4;**.wav;**.js;**.css;**.jpg;**.jpeg;**.bmp;**.gif;**.png;**.ico;**.swf;**.eot;**.svg;**.ttf;**.woff;**.woff2;**.htm;**.html;**.txt;**.xml;**.json;**.map;/license/auth/**;/redirectAuth;/getTokenByCode;/permission/user/getCurrentUser;/permission/module/getModuleTree;/sys/getLogos;/permission/oauth/resource/cleanOnlineUser;/trial/saas/login/check;/login/getLoginMethod;

# 接口日志最大限制，单位B，-1不限制
print.request.param.size=1024
# jackson序列化json时不返回null字段
spring.jackson.default-property-inclusion=non_null
spring.jackson.time-zone=GMT+8
## 工作流模块使用jpa做dao层
#spring.jpa.show-sql = true
#spring.jpa.hibernate.ddl-auto = update
#spring.jpa.database-platform = org.hibernate.dialect.MySQL5InnoDBDialect
#\u914D\u7F6E\u4FE1\u606F-\u6807\u51C6\u89C4\u8303\u53CA\u7248\u672C

# 配置quartz使用jdbc方式
#spring.quartz.job-store-type=jdbc
#spring.quartz.startup-delay=10
#spring.quartz.properties.jobStore.isClustered=true

# 是否启用Nacos配置中心
spring.cloud.nacos.config.enabled=false

# quickea功能权限配置
uino.eam.project.enable=true

#系统名称和单点登录url
oauth.server.systemName=架构流程管理平台
oauth.server.systemUrl=

#====================数字空间登录相关配置====================================================
# 使用数字空间登录时，用户登录的默认角色
uino.user.rolename=admin
# 数字空间权限相关配置
wiki.oauth.client.id=d5fqDTxwkchgD4EPxnyKMw1KK9OhRYo94jbOjWHxh1jje7FxuCOWN8Kc6tEhqwdE
wiki.oauth.client.secret=Dw7lGst3QLDHhSPBOTyr0VncTK9xTX9pMfnXfOFOpfjpk9Z5eXBzCe4jF69s2RxA
wiki.oauth.client.grant_type=authorization_code
wiki.oauth.client.user_agent=eam-79 (Internal App)
wiki.oauth.server.url=https://sso.uino.com
wiki.oauth.server.token_callback.url=http://192.168.21.143/tarsier-eam/wiki/getTokenByCode

#数字空间本地权限相关配置
local.oauth.client.id=d5fqDTxwkchgD4EPxnyKMw1KK9OhRYo94jbOjWHxh1jje7FxuCOWN8Kc6tEhqwdE
local.oauth.client.secret=Dw7lGst3QLDHhSPBOTyr0VncTK9xTX9pMfnXfOFOpfjpk9Z5eXBzCe4jF69s2RxA
local.oauth.client.grant_type=authorization_code
local.oauth.client.user_agent=eam-79 (Internal App)
local.oauth.server.url=https://sso.uino.com
local.oauth.server.token_callback.url=http://192.168.21.143/tarsier-eam/wiki/getTokenByCode