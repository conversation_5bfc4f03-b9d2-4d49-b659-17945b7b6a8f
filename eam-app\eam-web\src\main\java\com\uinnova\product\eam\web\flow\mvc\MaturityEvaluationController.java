package com.uinnova.product.eam.web.flow.mvc;

import com.binary.core.util.BinaryUtils;
import com.binary.framework.web.RemoteResult;
import com.uinnova.product.eam.service.MaturityEvaluationService;
import com.uino.bean.cmdb.base.LibType;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;

/**
 * 流程管理/成熟度评价相关接口
 *
 * <AUTHOR>
 * @since 2024/12/17
 */
@RestController
@RequestMapping("/flowManager/maturityEvaluation/")
public class MaturityEvaluationController {

    @Resource
    private MaturityEvaluationService maturityEvaluationService;
    
    /** 
    * @Description: 资产分类复制-判断属性中是否有"只读"需复制设计库数据
    * @Param:copiedClassId:目标类id
     * @Param:classidAfterReplication:复制后类id
    * @return:
    */
    @GetMapping("copyData")
    public RemoteResult copyData(@RequestParam Long copiedClassId, Long classidAfterReplication) {
        BinaryUtils.checkEmpty(copiedClassId, "目标类id不能为空");
        BinaryUtils.checkEmpty(classidAfterReplication, "复制后类id不能为空");
        return new RemoteResult(maturityEvaluationService.copyData(copiedClassId,classidAfterReplication));
    }
   /*
   * 卡片详情数据查询
   *
   * */
    @GetMapping("/queryClassificationFullData")
    public RemoteResult queryClassificationFullData(@RequestParam(defaultValue = "PRIVATE") LibType libType,Long id,String sortFieldName,Boolean isAsc) {
        BinaryUtils.checkEmpty(id, "id不能为空");
        return new RemoteResult(maturityEvaluationService.queryClassificationFullData(id, libType, sortFieldName, isAsc));
    }
    /**
     * 发布
     * @param id
     * @return
     */
    @GetMapping("/publish")
    public RemoteResult publish( Long id) {
        BinaryUtils.checkEmpty(id, "id不能为空");
        return new RemoteResult(maturityEvaluationService.publish(id));
    }

}
