package com.binary.framework.spring;

import java.util.HashMap;
import java.util.Map;

import org.springframework.context.ApplicationContext;
import org.springframework.core.env.Environment;
import org.springframework.core.env.Profiles;

import com.binary.core.io.Resource;
import com.binary.core.io.ResourceResolver;
import com.binary.core.lang.Conver;
import com.binary.core.util.BinaryUtils;
import com.binary.core.util.Properties;
import com.binary.framework.ApplicationListener;
import com.binary.framework.exception.FrameworkException;
import com.binary.framework.exception.ServiceException;

public class SpringProperties implements ApplicationListener, Environment {

    private static SpringProperties sp;

    private final Map<String, String> properties = new HashMap<String, String>();

    private Environment e;

    private String[] locations;

    public String[] getLocations() {
        return locations;
    }

    public void setLocation(String location) {
        BinaryUtils.checkEmpty(location, "location");
        setLocations(new String[] {location});
    }

    public void setLocations(String[] locations) {
        BinaryUtils.checkEmpty(locations, "locations");
        this.locations = locations;
        loadResource();
    }

    private void loadResource() {
        this.properties.clear();
        for (int i = 0; i < locations.length; i++) {
            Resource res = ResourceResolver.getResource(locations[i]);
            Properties pro = new Properties(res);
            this.properties.putAll(pro);
        }
    }

    @Override
    public void afterInitialization(ApplicationContext context) {
        this.e = context.getEnvironment();
        sp = this;
    }

    private Environment getEnvironment() {
        if (this.e == null) { throw new ServiceException(" Not initialized in the spring-context ! "); }
        return this.e;
    }

    @Override
    public boolean containsProperty(String key) {
        if (properties.containsKey(key)) { return true; }
        return getEnvironment().containsProperty(key);
    }

    @Override
    public String getProperty(String key) {
        String value = properties.get(key);
        if (value != null)
            return value;
        return getEnvironment().getProperty(key);
    }

    @Override
    public String getProperty(String key, String defaultValue) {
        String value = properties.get(key);
        if (value != null)
            return value;
        return getEnvironment().getProperty(key, defaultValue);
    }

    @Override
    public <T> T getProperty(String key, Class<T> targetType) {
        String value = properties.get(key);
        if (value != null)
            return Conver.to(key, targetType);
        return getEnvironment().getProperty(key, targetType);
    }

    @Override
    public <T> T getProperty(String string, Class<T> targetType, T defaultValue) {
        String value = properties.get(string);
        if (value != null)
            return Conver.to(string, targetType);
        return getEnvironment().getProperty(string, targetType, defaultValue);
    }

    @Override
    public String getRequiredProperty(String key) throws IllegalStateException {
        String value = properties.get(key);
        if (value != null)
            return value;
        return getEnvironment().getRequiredProperty(key);
    }

    @Override
    public <T> T getRequiredProperty(String key, Class<T> targetType) throws IllegalStateException {
        String value = properties.get(key);
        if (value != null)
            return Conver.to(key, targetType);
        return getEnvironment().getRequiredProperty(key, targetType);
    }

    @Override
    public String resolvePlaceholders(String text) {
        return getEnvironment().resolvePlaceholders(text);
    }

    @Override
    public String resolveRequiredPlaceholders(String text) throws IllegalArgumentException {
        return getEnvironment().resolveRequiredPlaceholders(text);
    }

    @Override
    public String[] getActiveProfiles() {
        return getEnvironment().getActiveProfiles();
    }

    @Override
    public String[] getDefaultProfiles() {
        return getEnvironment().getDefaultProfiles();
    }

    @Override
    public boolean acceptsProfiles(String... profiles) {
        return getEnvironment().acceptsProfiles(profiles);
    }

    public static SpringProperties getInstance() {
        if (sp == null)
            throw new FrameworkException(" Not initialized in the spring-context ! ");
        return sp;
    }

    @Override
    public boolean acceptsProfiles(Profiles profiles) {
        return getEnvironment().acceptsProfiles(profiles);
    }
}
