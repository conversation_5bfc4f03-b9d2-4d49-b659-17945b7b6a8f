package com.uinnova.product.vmdb.comm.exception;

import com.binary.framework.exception.ServiceException;

public class FeignInterfaceException extends ServiceException {

    private static final long serialVersionUID = 1L;

    public FeignInterfaceException() {
        super();
    }

    public FeignInterfaceException(String message) {
        super(message);
    }

    public FeignInterfaceException(Throwable cause) {
        super(cause);
    }

    public FeignInterfaceException(String message, Throwable cause) {
        super(message, cause);
    }
}
