package com.uinnova.product.eam.model.dix;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
public class AppSystemRltVO implements Serializable {
    @Comment("关系id")
    private String id;
    @Comment("关系名称")
    private String name;
    @Comment("关系类型")
    private String type;
    @Comment("主键所在的实体的Id")
    private String sourceEntityId;
    @Comment("外键所在的实体的Id")
    private String targetEntityId;
    @Comment("主键实体属性id-外键实体属性id映射集合")
    private List<Map<String, String>> keyMap;
    @Comment("新增:0/更新:1")
    private Integer status;

    public AppSystemRltVO() {
    }

    public AppSystemRltVO(String id, String name, String type, String sourceEntityId, String targetEntityId, Integer status) {
        this.id = id;
        this.name = name;
        this.type = type;
        this.sourceEntityId = sourceEntityId;
        this.targetEntityId = targetEntityId;
        this.status = status;
    }
}
