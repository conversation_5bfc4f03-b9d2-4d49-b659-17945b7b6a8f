package com.uinnova.product.eam.web.asset.mvc;

import com.binary.framework.web.RemoteResult;
import com.uinnova.product.eam.comm.model.es.AssetAttrDataScreen;
import com.uinnova.product.eam.service.AssetAttrDataScreenSvc;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;

/**
 * 资产属性数据筛选
 */
@RestController
@RequestMapping("/data/screen")
public class AssetAttrDataScreenMvc {

    @Resource
    AssetAttrDataScreenSvc assetAttrDataScreenSvc;

    @RequestMapping("saveOrUpdate")
    public RemoteResult saveOrUpdate(@RequestBody AssetAttrDataScreen assetAttrDataScreen) {
        return new RemoteResult(assetAttrDataScreenSvc.saveOrUpdate(assetAttrDataScreen));
    }

    @RequestMapping("getDateScreenInfo")
    public RemoteResult getDateScreenInfo(@RequestParam Long bindId, @RequestParam Long classId) {
        return new RemoteResult(assetAttrDataScreenSvc.getDateScreenInfo(bindId, classId));
    }

    @RequestMapping("removeDateScreenInfo")
    public RemoteResult removeDateScreenInfo(@RequestParam Long bindId, @RequestParam Long classId) {
        return new RemoteResult(assetAttrDataScreenSvc.removeDateScreenInfo(bindId, classId));
    }
}
