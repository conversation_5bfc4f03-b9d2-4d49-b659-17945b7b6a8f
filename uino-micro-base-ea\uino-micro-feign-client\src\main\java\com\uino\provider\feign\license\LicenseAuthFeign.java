package com.uino.provider.feign.license;

import java.util.List;

import com.uino.bean.license.BaseLicenseAuthInfo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.binary.core.io.Resource;
import com.uinnova.product.vmdb.comm.model.license.CcLicenseAuthServer;
import com.uino.provider.feign.config.BaseFeignConfig;

/**
 * 
 * <AUTHOR>
 *
 */
@Component
@FeignClient(name = "${feign-server-name:tarsier-platform}", path = "${feign-server-path:tarsier-platform}/feign/license", configuration = {
        BaseFeignConfig.class})
public interface LicenseAuthFeign {

    /**
     * 查询授权许可信息
     * 
     * @return
     */
    @PostMapping("queryLicenseAuthInfo")
    BaseLicenseAuthInfo queryLicenseAuthInfo();

    /**
     * 查询服务器列表
     * 
     * @return
     */
    @PostMapping("queryServerList")
    List<CcLicenseAuthServer> queryServerList();

    /**
     * 删除服务器
     * 
     * @param serverId
     * @return
     */
    @PostMapping("removeServer")
    Integer removeServer(@RequestBody Long serverId);

    /**
     * 创建客户识别码
     * 
     * @return
     */
    @PostMapping("createClientCode")
    String createClientCode();

    /**
     * 注册授权
     * 
     * @param authCode
     *            授权码
     * @param authUser
     *            授权用户
     * @return
     */
    @PostMapping("registerLicense")
    void registerLicense(@RequestBody String authCode, @RequestParam(value = "authUser") String authUser);

    /**
     * 跟据数据库中登记的授权许可证注册
     */
    @PostMapping("registerLicenseByDb")
    void registerLicenseByDb();

    /**
     * 获取二维码
     * 
     * @param width
     * @param height
     * @return
     */
    @PostMapping("getQRCodeImage")
    Resource getQRCodeImage(@RequestParam(value = "width", required = false) Integer width, @RequestParam(value = "height", required = false) Integer height);

    /**
     * 获取thingjsLicense信息
     * 
     * @param msg
     * @return
     */
    @PostMapping("processRequest")
    String processRequest(String msg);

}
