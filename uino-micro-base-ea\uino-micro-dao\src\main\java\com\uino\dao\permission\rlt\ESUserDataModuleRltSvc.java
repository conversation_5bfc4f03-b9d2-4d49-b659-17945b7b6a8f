package com.uino.dao.permission.rlt;

import java.util.List;

import jakarta.annotation.PostConstruct;

import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.stereotype.Service;

import com.uino.dao.AbstractESBaseDao;
import com.uino.dao.ESConst;
import com.uino.bean.permission.base.SysUserDataModuleRlt;
import com.uino.bean.permission.query.CSysUserDataModuleRlt;

/**
 * <b>用户-数据模块关系
 * 
 * <AUTHOR>
 */
@Service
public class ESUserDataModuleRltSvc extends AbstractESBaseDao<SysUserDataModuleRlt, CSysUserDataModuleRlt> {

    @Override
    public String getIndex() {
        return ESConst.INDEX_SYS_USER_DATAMODULE_RLT;
    }

    @Override
    public String getType() {
        return ESConst.INDEX_SYS_USER_DATAMODULE_RLT;
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }

    /**
     * 获取数据权限
     * 
     * @param userId 用户id
     * @param moduleCodes 模块代码
     * @return
     */
    public List<SysUserDataModuleRlt> getListByUserId(Long userId, List<String> moduleCodes) {
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("userId", userId));
        if (moduleCodes != null && !moduleCodes.isEmpty()) {
            query.must(QueryBuilders.termsQuery("dataModuleCode.keyword", moduleCodes));
        }
        return super.getListByQuery(1, 5000, query).getData();
    }

    /**
     * 获取用户所有模块数据权限
     * 
     * @param userId 用户id
     * @return
     */
    public List<SysUserDataModuleRlt> getListByUserId(Long userId) {
        return getListByUserId(userId, null);
    }
}
