package com.uino.service.cmdb.microservice;

import java.util.List;

import com.binary.jdbc.Page;
import com.uino.bean.cmdb.base.ESRltRuleInfo;

/**
 * 关系遍历
 *
 * <AUTHOR>
 * @data 2019/7/31 11:18.
 */
public interface IRltRuleSvc {
    /**
     * 保存或更新CI
     *
     * @param rltRuleInfo
     * @return
     */
    Long saveOrUpdate(ESRltRuleInfo rltRuleInfo);

    /**
     * 根据id删除规则
     *
     * @param id
     * @return
     */
    Integer deleteById(Long id);


    /**
     * 根据id查询规则信息
     *
     * @param id
     * @return
     */
    ESRltRuleInfo queryInfoById(Long id);

    /**
     * 不分页查询规则数据列表
     * 
     * @param domainId
     * @return
     */
    List<ESRltRuleInfo> queryInfo(Long domainId);

    /**
     * 根据name模糊匹配
     * 
     * @param pageNum
     * @param pageSize
     * @param name
     * @param domainId
     * @return
     */
    Page<ESRltRuleInfo> queryInfoPage(Integer pageNum, Integer pageSize,String name,Long domainId);


    /**
     * 分页查询关系规则数据
     *
     * @param pageNum : 指定页码
     * @param pageSize : 指定页行数
     * @param domainId
     * @return
     */

    Page<ESRltRuleInfo> queryInfoPage(Integer pageNum, Integer pageSize, Long domainId);

    /**
     * 分页查询关系规则数据
     * 
     * @param pageNum
     * @param pageSize
     * @param domainId
     * @param orders
     * @param isAsc
     * @return
     */
    Page<ESRltRuleInfo> queryInfoPage(Integer pageNum, Integer pageSize, Long domainId, String orders, boolean isAsc);

    /**
     * 分页查询关系规则数据
     * 
     * @param pageNum
     * @param pageSize
     * @param name
     * @param domainId
     * @param orders
     * @param isAsc
     * @return
     */
    Page<ESRltRuleInfo> queryInfoPage(Integer pageNum, Integer pageSize,String name,Long domainId,String orders, boolean isAsc);




}
