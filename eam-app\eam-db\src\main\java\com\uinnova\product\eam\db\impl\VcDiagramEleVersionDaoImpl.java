package com.uinnova.product.eam.db.impl;


import com.uinnova.product.eam.comm.model.CVcDiagramEleVersion;
import com.uinnova.product.eam.comm.model.VcDiagramEleVersion;
import com.uinnova.product.eam.db.VcDiagramEleVersionDao;
import com.uinnova.product.eam.db.support.dao.mybatis.ComMyBatisBinaryDaoImpl;


/**
 * 视图元素版本表[VC_DIAGRAM_ELE_VERSION]数据访问对象实现
 */
public class VcDiagramEleVersionDaoImpl extends ComMyBatisBinaryDaoImpl<VcDiagramEleVersion, CVcDiagramEleVersion> implements VcDiagramEleVersionDao {

//    @Override
//    public String getTableName() {
//        return "IAMS." + getDaoDefinition().getTableName();
//    }
}


