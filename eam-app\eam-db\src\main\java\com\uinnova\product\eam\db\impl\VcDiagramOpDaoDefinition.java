package com.uinnova.product.eam.db.impl;


import com.binary.framework.dao.DaoDefinition;

import com.uinnova.product.eam.comm.model.CVcDiagramOp;
import com.uinnova.product.eam.comm.model.VcDiagramOp;


/**
 * 视图操作表[VC_DIAGRAM_OP]数据访问对象定义实现
 */
public class VcDiagramOpDaoDefinition implements DaoDefinition<VcDiagramOp, CVcDiagramOp> {


	@Override
	public Class<VcDiagramOp> getEntityClass() {
		return VcDiagramOp.class;
	}


	@Override
	public Class<CVcDiagramOp> getConditionClass() {
		return CVcDiagramOp.class;
	}


	@Override
	public String getTableName() {
		return "VC_DIAGRAM_OP";
	}


	@Override
	public boolean hasDataStatusField() {
		return false;
	}


	@Override
	public void setDataStatusValue(VcDiagramOp record, int status) {
	}


	@Override
	public void setDataStatusValue(CVcDiagramOp cdt, int status) {
	}


	@Override
	public void setCreatorValue(VcDiagramOp record, String creator) {
	}


	@Override
	public void setModifierValue(VcDiagramOp record, String modifier) {
	}


}


