package com.uino.service.sys.microservice.impl;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.BooleanSupplier;
import java.util.stream.Collectors;

import com.binary.core.util.BinaryUtils;
import com.uino.dao.BaseConst;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import com.binary.jdbc.Page;
import com.uino.dao.permission.ESUserSvc;
import com.uino.dao.sys.ESNotifyChannelSvc;
import com.uino.service.sys.microservice.INotifyChannelSvc;
import com.uino.service.sys.notify.impl.NotifySvcFactory;
import com.uino.util.sys.ValidDtoUtil;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.sys.base.NotifyChannel;
import com.uino.bean.sys.business.NotifyChannelReqDto;
import com.uino.bean.sys.business.NotifyData;

@Service
public class NotifyChannelSvc implements INotifyChannelSvc {

    @Autowired
    private ESNotifyChannelSvc esNotifyChannelSvc;

    @Autowired
    private ESUserSvc esUserSvc;

    @Override
    public NotifyChannel save(NotifyChannel saveInfo) {
        ValidDtoUtil.valid(saveInfo);
        Long id = saveInfo.getId();
        if(BinaryUtils.isEmpty(saveInfo.getDomainId())){
            saveInfo.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
        }
        BoolQueryBuilder validQuery = QueryBuilders.boolQuery();
        validQuery.must(QueryBuilders.termQuery("name.stdkeyword", saveInfo.getName()));
        validQuery.must(QueryBuilders.termQuery("domainId",saveInfo.getDomainId()));
        if (id != null) {
            validQuery.mustNot(QueryBuilders.termQuery("id", id));
        }
        long validCount = esNotifyChannelSvc.countByCondition(validQuery);
        Assert.isTrue(validCount <= 0L, "渠道名称已存在");
        id = esNotifyChannelSvc.saveOrUpdate(saveInfo);
        saveInfo.setId(id);
        return saveInfo;
    }

    @Override
    public void delete(Collection<Long> ids) {
        Assert.notEmpty(ids, "入参为空");
        esNotifyChannelSvc.deleteByIds(ids);
    }

    @Override
    public List<NotifyChannel> search(NotifyChannelReqDto searchDto) {
        if(BinaryUtils.isEmpty(searchDto.getDomainId())){
            searchDto.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
        }
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("domainId",searchDto.getDomainId()));
        if (searchDto != null) {
            if (searchDto.getId() != null) {
                query.must(QueryBuilders.termQuery("id", searchDto.getId()));
            }
            if (searchDto.getName() != null && !"".equals(searchDto.getName().trim())) {
                query.must(QueryBuilders.wildcardQuery("name.stdkeyword", "*" + searchDto.getName() + "*"));
            }
        }
        Page<NotifyChannel> res = esNotifyChannelSvc.getSortListByQuery(1, 3000, query, "id", true);
        Set<String> userCodes = res.getData().stream().collect(Collectors.groupingBy(NotifyChannel::getCreator))
                .keySet();
        if (userCodes != null && userCodes.size() > 0) {
            BoolQueryBuilder boolQueryBuilder=QueryBuilders.boolQuery();
            boolQueryBuilder.filter(QueryBuilders.termsQuery("loginCode.stdkeyword", userCodes)).must(QueryBuilders.termQuery("domainId",searchDto.getDomainId()));
            List<SysUser> users = esUserSvc.getListByQuery(boolQueryBuilder);
            Map<String, List<SysUser>> userCodeUserMap = users.stream()
                    .collect(Collectors.groupingBy(SysUser::getLoginCode));
            res.getData().forEach(data -> data.setCreateNickName(userCodeUserMap.get(data.getCreator()) == null
                    ? data.getCreator() : userCodeUserMap.get(data.getCreator()).get(0).getUserName()));
        }
        return res.getData();
    }

    @Override
    public NotifyChannel getNotifyChannelById(Long id) {
        return esNotifyChannelSvc.getById(id);
    }

    @Override
    public boolean sendNotify(NotifyData notifyData) {
        Assert.notNull(notifyData.getChannelId(), "X_PARAM_NOT_NULL${name:channelId}");
        NotifyChannel notifyChannel = this.getNotifyChannelById(notifyData.getChannelId());
        Assert.isTrue(notifyChannel.isEnable(), "通知渠道未启用");
        Assert.isTrue(notifyChannel.getChannelType().equals(notifyData.getType()), "通知类型不匹配");
        return NotifySvcFactory.sendNotify(notifyData);
    }
}
