package com.uino.ed.eventdrive.handle;

import com.uino.ed.eventdrive.event.Event;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public abstract class EventHandle {

    /**
     * 需要执行的事件及规则
     */
    private ThreadLocal<Event> event = new ThreadLocal<>();

    abstract public String getName();

    public void handle(Event event) {
        try {
            this.event.set(event);
            Boolean enableExecute = this.beforeHandle();
            if (null != enableExecute && enableExecute) {
                try {
                    this.process();
                } catch (Exception e) {
                    this.exceptionHandle(e);
                }
            }
        } catch (Exception exp) {
            log.error("handler error!", exp);
        } finally {
            this.afterHandle();
            this.event.remove();
        }
    }

    /**
     * 任务逻辑执行前操作，一般进行加锁、初始化执行记录等操作
     *
     * @return {@link Boolean} true 则开始执行任务
     */
    abstract protected Boolean beforeHandle();

    /**
     * 任务执行逻辑
     */
    abstract protected void process();

    /**
     * 任务逻辑执行前操作，一般进行释放锁、更新执行记录状态等操作
     */
    abstract protected void afterHandle();

    /**
     * 执行异常回调, 该方法只捕获 {@code process()} 方法异常
     *
     * @param exp {@link Exception}
     */
    protected void exceptionHandle(Exception exp) {};

    public Event getEvent() {
        return event.get();
    }
}
