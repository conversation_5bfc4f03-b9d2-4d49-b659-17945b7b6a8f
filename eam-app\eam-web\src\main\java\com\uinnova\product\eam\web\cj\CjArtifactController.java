package com.uinnova.product.eam.web.cj;

import com.binary.framework.web.RemoteResult;
import com.uinnova.product.eam.comm.model.es.EamArtifact;
import com.uinnova.product.eam.model.dto.ElementConditionDto;
import com.uinnova.product.eam.model.vo.CjArtifactVo;
import com.uinnova.product.eam.service.CjArtifactSvc;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 仓颉制品
 * <AUTHOR>
 */
@RestController
@RequestMapping("/cj/artifact")
public class CjArtifactController {

    @Resource
    private CjArtifactSvc cjArtifactSvc;

    /**
     * @param : 条件查询参数（制品名称，类型标签）
     * @description :查询制品列表信息，按制品类型名称（支持模糊查）进行搜索查询
     * @Return : 制品信息列表
     */
    @PostMapping("/findArtifactList")
    public RemoteResult findArtifactList(@RequestBody(required = false) ElementConditionDto dto) {
        List<CjArtifactVo> artifactMap = cjArtifactSvc.findArtifactList(dto);
        return new RemoteResult(artifactMap);
    }

    /**
     * 通过制品id列表获取制品列表
     * @return
     */
    @GetMapping("/findAllArtifactList")
    public RemoteResult findAllArtifactList() {
        List<EamArtifact> eamArtifactList = cjArtifactSvc.findAllArtifactList();
        return new RemoteResult(eamArtifactList);
    }

    /**
     * 通过制品id获取所有制品
     * @return
     */
    @PostMapping("/findArtifactListByIds")
    public RemoteResult findArtifactListByIds(@RequestBody List<Long> ids) {
        Map<Long, String> result = cjArtifactSvc.findArtifactListByIds(ids);
        return new RemoteResult(result);
    }

}
