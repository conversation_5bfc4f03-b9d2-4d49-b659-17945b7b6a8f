package com.uino.init.api;

import com.uino.init.SwitchLiberaryInterceptor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import jakarta.annotation.Resource;

@Configuration
public class BaseWebMvcConfig implements WebMvcConfigurer {

    @Resource
    private SwitchLiberaryInterceptor switchLiberaryInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(switchLiberaryInterceptor);
    }
}
