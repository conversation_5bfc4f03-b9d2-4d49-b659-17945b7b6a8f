package com.uinnova.product.eam.service.diagram;


import com.binary.jdbc.Page;
import com.uinnova.product.eam.base.diagram.model.ESUploadManage;
import com.uinnova.product.vmdb.comm.model.image.CcImage;
import org.elasticsearch.index.query.BoolQueryBuilder;

/**
 * @Classname 上传(图片)管理逻辑层
 * <AUTHOR>
 * @Date 2021-10-21-17:14
 */
public interface ESUploadManageSvc {


    /**
     * 上传文件
     *
     * @param file
     *            待上传文件
     * @return 文件路径
     */
    ESUploadManage upload(String file, Long size);

    Page<ESUploadManage> queryData(int pageNum, int pageSize, String orders, BoolQueryBuilder bQuery);

    Long deleteImage(CcImage image);
}
