package com.uinnova.product.eam.service.cj.service;

import com.uinnova.product.eam.feign.workable.entity.PorcessResponse;
import com.uinnova.product.eam.feign.workable.entity.ProcessRequest;
import com.uinnova.product.eam.feign.workable.entity.TaskRequest;
import com.uinnova.product.eam.feign.workable.entity.TaskResponse;

/**
 * 流程
 */
public interface FlowableService {

    /**
     * 发起流程
     * @param request
     * @return
     */
    PorcessResponse startProcessBindAssignee(ProcessRequest request);

    /**
     * 执行审批
     * @param taskRequest
     */
    TaskResponse completeTask(TaskRequest taskRequest);

    /**
     * 获取当前任务定义信息
     * @param businessKey
     * @return
     */
    TaskResponse getCurrentTaskDefinitionInfo(String businessKey);

    /**
     * 获取当前任务节点所有的代办人
     * @param businessKey
     * @return
     */
    TaskResponse getCurrentTaskAssignees(String businessKey);

}
