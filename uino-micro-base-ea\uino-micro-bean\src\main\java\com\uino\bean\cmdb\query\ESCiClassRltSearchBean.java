package com.uino.bean.cmdb.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 
 * <AUTHOR>
 *
 */
@ApiModel(value="CI分类关系查询类",description = "CI分类之间的关系信息")
public class ESCiClassRltSearchBean implements Serializable {

	private static final long serialVersionUID = 6871105743971196816L;

	@ApiModelProperty(value="关系id",example = "123")
	private Long id;

	@ApiModelProperty(value="关系id集合")
	private Long[] ids;

	@ApiModelProperty(value="分类id",example = "1234")
	private Long classId;

	@ApiModelProperty(value="分类id集合")
	private Long[] classIds;

	@ApiModelProperty(value="源分类id",example = "123")
	private Long sourceClassId;

	@ApiModelProperty(value="源分类id集合")
	private Long[] sourceClassIds;

	@ApiModelProperty(value="目标分类id",example = "234")
	private Long targetClassId;

	@ApiModelProperty(value="目标分类id集合")
	private Long[] targetClassIds;

	@ApiModelProperty(value="领域id",example="567")
	private Long domainId;

	@ApiModelProperty(value="领域id集合")
	private Long[] domainIds;
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long[] getIds() {
		return ids;
	}
	public void setIds(Long[] ids) {
		this.ids = ids;
	}
	public Long getClassId() {
		return classId;
	}
	public void setClassId(Long classId) {
		this.classId = classId;
	}
	public Long[] getClassIds() {
		return classIds;
	}
	public void setClassIds(Long[] classIds) {
		this.classIds = classIds;
	}
	public Long getSourceClassId() {
		return sourceClassId;
	}
	public void setSourceClassId(Long sourceClassId) {
		this.sourceClassId = sourceClassId;
	}
	public Long[] getSourceClassIds() {
		return sourceClassIds;
	}
	public void setSourceClassIds(Long[] sourceClassIds) {
		this.sourceClassIds = sourceClassIds;
	}
	public Long getTargetClassId() {
		return targetClassId;
	}
	public void setTargetClassId(Long targetClassId) {
		this.targetClassId = targetClassId;
	}
	public Long[] getTargetClassIds() {
		return targetClassIds;
	}
	public void setTargetClassIds(Long[] targetClassIds) {
		this.targetClassIds = targetClassIds;
	}
	public Long getDomainId() {
		return domainId;
	}
	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}
	public Long[] getDomainIds() {
		return domainIds;
	}
	public void setDomainIds(Long[] domainIds) {
		this.domainIds = domainIds;
	}
	
}
