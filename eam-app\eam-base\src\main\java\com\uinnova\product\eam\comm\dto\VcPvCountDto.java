package com.uinnova.product.eam.comm.dto;

import java.io.Serializable;

/**
 * dmv埋点统计数据传输类
 * <AUTHOR>
 *
 */
public class VcPvCountDto implements Serializable{

	private static final long serialVersionUID = 1L;

	private Long userId;
	
	private String optionCode;
	
	private String optionName;
	
	private Long countNum = 0L;

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public String getOptionCode() {
		return optionCode;
	}

	public void setOptionCode(String optionCode) {
		this.optionCode = optionCode;
	}

	public String getOptionName() {
		return optionName;
	}

	public void setOptionName(String optionName) {
		this.optionName = optionName;
	}

	public Long getCountNum() {
		return countNum;
	}

	public void setCountNum(Long countNum) {
		this.countNum = countNum;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}
	
	
}
