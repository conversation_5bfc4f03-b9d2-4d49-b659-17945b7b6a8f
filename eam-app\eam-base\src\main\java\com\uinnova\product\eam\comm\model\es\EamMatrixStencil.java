package com.uinnova.product.eam.comm.model.es;

import com.binary.framework.bean.annotation.Comment;
import com.uino.bean.cmdb.base.dataset.relation.RelationRuleAttrCdt;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 矩阵制品管理
 * <AUTHOR>
 */
@Data
public class EamMatrixStencil {

    /*=================================基本信息======================================*/
    @Comment("主键")
    private Long id;

    @Comment("领域id")
    private Long domainId;

    @Comment("矩阵制品名称")
    private String name;

    @Comment("矩阵制品分类")
    private Integer type;

    @Comment("使用人群")
    private String crowd;

    @Comment("适用组织")
    private Long orgId;

    @Comment("试用场景")
    private String scene;

    @Comment("关键内容")
    private String content;

    @Comment("说明")
    private String description;

    /*=================================制品内容字段======================================*/

    @Comment("矩阵表格名称")
    private String matrixName;

    @Comment("源端分类")
    private Long sourceClass;

    @Comment("源端属性")
    private List<Long> sourceAttrs;

    @Comment("源端约束规则")
    private List<RelationRuleAttrCdt> sourceRules;

    @Comment("目标端分类")
    private Long targetClass;

    @Comment("目标端属性")
    private List<Long> targetAttrs;

    @Comment("目标端约束规则")
    private List<RelationRuleAttrCdt> targetRules;

    @Comment("目标端属性<属性id-属性类型>")
    private Map<Long, String> targetTypeMap;

    @Comment("关系分类")
    private Long rltClass;

    @Comment("关系属性")
    private List<Long> rltAttrs;

    @Comment("关系属性<属性id-属性类型>")
    private Map<Long, String> rltTypeMap;

    @Comment("显示序号")
    private Boolean displayNum;

    /*=================================历史版本必须保留字段======================================*/

    @Comment("源端分类名称")
    private String sourceName;

    @Comment("源端属性<属性id-属性名称>")
    private Map<Long, EamMatrixStencilAttr> sourceAttrMap;

    @Comment("目标端分类名称")
    private String targetName;

    @Comment("目标端属性<属性id-属性名称>")
    private Map<Long, EamMatrixStencilAttr> targetAttrMap;

    @Comment("关系分类名称")
    private String rltName;

    @Comment("关系属性<属性id-属性名称>")
    private Map<Long, EamMatrixStencilAttr> rltAttrMap;

    @Comment("发布版本")
    private Integer version;

    @Comment("源矩阵制品ID")
    private Long publishId;

    /*=================================状态控制======================================*/

    @Comment("是否已发布: 1已发布,0未发布")
    private Integer published;

    @Comment("逻辑删除状态StatusConstant：1正常,0 已删除")
    private Integer status;

    @Comment("创建人")
    private String creator;

    @Comment("创建时间")
    private Long createTime;

    @Comment("修改人")
    private String modifier;

    @Comment("修改时间")
    private Long modifyTime;
}
