package com.uinnova.product.eam.model.cj.domain;

import com.binary.framework.bean.Condition;
import com.uinnova.product.eam.base.cj.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 方案与系统关联实体
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PlanSystemRelation extends BaseEntity implements Condition{

    private Long planId;

    private String ciCode;
    /** 状态 0：删除，1：未发布，2：已发布 */
    private Integer status;

    /**
     * 方案之前的状态
     * <p>
     *     <font color="yellow">删除后需要记录删除之前的状态，以便回复时能恢复到删除之前的状态</font>
     * </p>
     */
    private Integer previousStatus;
}
