package com.binary.core.exception;

import com.binary.core.i18n.LanguageResolver;
import com.binary.core.util.BinaryUtils;

public class MessageException extends CoreException {
	private static final long serialVersionUID = 1L;
	
	
	/** 缺省消息代码 **/
	public static final int DEFAULT_MESSAGE_CODE = 0;
	
	
	
	private int messageCode;
	private String message;
	
	
	
	
	public MessageException(String message) {
		this(DEFAULT_MESSAGE_CODE, message, null);
	}
	
	public MessageException(String message, Throwable cause) {
		this(DEFAULT_MESSAGE_CODE, message, cause);
	}
	
	
	public MessageException(int messageCode, String message) {
		this(messageCode, message, null);
	}
	
	public MessageException(int messageCode, String message, Throwable cause) {
		super(cause);
		this.messageCode = messageCode;
		this.message = message;
		
		if(BinaryUtils.isEmpty(this.message)) {
			throw new CoreException(" the message is empty! ");
		}
	}
	
	
	
	
	
	

	public int getMessageCode() {
		return messageCode;
	}
	

	@Override
	public String getMessage() {
		return this.message;
	}
	
	

	@Override
	public Throwable getMessageThrowable() {
		return this;
	}
	
	

	@Override
	public Throwable getOriginalThrowable() {
		return this;
	}
	
	
	
	

	@Override
	public String getOriginalMessage() {
		return getMessage();
	}
	
	
	

	@Override
	public String getFullMessage() {
		return getMessage()+"\n\t"+super.getFullMessage();
	}
	
	
	
	
	
	
	
	/**
	 * @see #i18n(int messageCode, String languageCode, Throwable cause)
	 */
	public static MessageException i18n(String languageCode) {
		return i18n(DEFAULT_MESSAGE_CODE, languageCode, (Throwable)null);
	}
	
	/**
	 * @see #i18n(int messageCode, String languageCode, Throwable cause)
	 */
	public static MessageException i18n(String languageCode, Throwable cause) {
		return i18n(DEFAULT_MESSAGE_CODE, languageCode, cause);
	}
	
	/**
	 * @see #i18n(int messageCode, String languageCode, Throwable cause)
	 */
	public static MessageException i18n(int messageCode, String languageCode) {
		return i18n(messageCode, languageCode, (Throwable)null);
	}
	
	/**
	 * 创建国际化消息异常
	 * @param messageCode 消息代码
	 * @param languageCode 语言代码
	 * @param cause 携带子异常
	 * @return
	 */
	public static MessageException i18n(int messageCode, String languageCode, Throwable cause) {
		String message = LanguageResolver.trans(languageCode);
		return new MessageException(messageCode, message, cause);
	}
	
	
	
	
	
	
	
	/**
	 * @see #i18n(int messageCode, String languageCode, String jsonParams, Throwable cause)
	 */
	public static MessageException i18n(String languageCode, String jsonParams) {
		return i18n(DEFAULT_MESSAGE_CODE, languageCode, jsonParams, (Throwable)null);
	}
	
	/**
	 * @see #i18n(int messageCode, String languageCode, String jsonParams, Throwable cause)
	 */
	public static MessageException i18n(String languageCode, String jsonParams, Throwable cause) {
		return i18n(DEFAULT_MESSAGE_CODE, languageCode, jsonParams, cause);
	}
	
	/**
	 * @see #i18n(int messageCode, String languageCode, String jsonParams, Throwable cause)
	 */
	public static MessageException i18n(int messageCode, String languageCode, String jsonParams) {
		return i18n(messageCode, languageCode, jsonParams, (Throwable)null);
	}
	
	
	/**
	 * 创建国际化消息异常
	 * @param messageCode 消息代码
	 * @param languageCode 语言代码
	 * @param jsonParams 语言中动态参数(JSON格式)
	 * @param cause 携带子异常
	 * @return
	 */
	public static MessageException i18n(int messageCode, String languageCode, String jsonParams, Throwable cause) {
		String message = LanguageResolver.trans(languageCode, jsonParams);
		return new MessageException(messageCode, message, cause);
	}
	
	
	
	
	
	
	/**
	 * @see #i18n(int messageCode, String languageCode, Object params, Throwable cause)
	 */
	public static MessageException i18n(String languageCode, Object params) {
		return i18n(DEFAULT_MESSAGE_CODE, languageCode, params, (Throwable) null);
	}
	
	/**
	 * @see #i18n(int messageCode, String languageCode, Object params, Throwable cause)
	 */
	public static MessageException i18n(String languageCode, Object params, Throwable cause) {
		return i18n(DEFAULT_MESSAGE_CODE, languageCode, params, cause);
	}
	
	/**
	 * @see #i18n(int messageCode, String languageCode, Object params, Throwable cause)
	 */
	public static MessageException i18n(int messageCode, String languageCode, Object params) {
		return i18n(messageCode, languageCode, params, (Throwable) null);
	}
	
	
	/**
	 * 创建国际化消息异常
	 * @param messageCode 消息代码
	 * @param languageCode 语言代码
	 * @param jsonParams 语言中动态参数, Bean or Map
	 * @param cause 携带子异常
	 * @return
	 */
	public static MessageException i18n(int messageCode, String languageCode, Object params, Throwable cause) {
		String message = LanguageResolver.trans(languageCode, params);
		return new MessageException(messageCode, message, cause);
	}
	
	
	
	
	

}
