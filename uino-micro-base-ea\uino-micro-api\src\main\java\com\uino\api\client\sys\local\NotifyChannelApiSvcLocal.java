package com.uino.api.client.sys.local;

import java.util.Collection;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.uino.service.sys.microservice.INotifyChannelSvc;
import com.uino.bean.sys.base.NotifyChannel;
import com.uino.bean.sys.business.NotifyChannelReqDto;
import com.uino.bean.sys.business.NotifyData;
import com.uino.api.client.sys.INotifyChannelApiSvc;

@Service
public class NotifyChannelApiSvcLocal implements INotifyChannelApiSvc {

    @Autowired
    private INotifyChannelSvc notifyChannelSvc;

    @Override
    public NotifyChannel save(NotifyChannel saveInfo) {
        // TODO Auto-generated method stub
        return notifyChannelSvc.save(saveInfo);
    }

    @Override
    public void delete(Collection<Long> ids) {
        // TODO Auto-generated method stub
        notifyChannelSvc.delete(ids);
    }

    @Override
    public List<NotifyChannel> search(NotifyChannelReqDto searchDto) {
        // TODO Auto-generated method stub
        return notifyChannelSvc.search(searchDto);
    }

    @Override
    public boolean sendNotify(NotifyData notifyData) {
        // TODO Auto-generated method stub
        return notifyChannelSvc.sendNotify(notifyData);
    }
}
