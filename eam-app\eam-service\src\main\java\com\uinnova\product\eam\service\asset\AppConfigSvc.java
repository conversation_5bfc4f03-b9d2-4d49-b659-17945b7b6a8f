package com.uinnova.product.eam.service.asset;

import com.uinnova.product.eam.model.dto.EamAppConfigDto;
import com.uinnova.product.eam.model.vo.EamAppConfigVo;

/**
 * 筛选配置接口
 */
public interface AppConfigSvc {

    /**
     * 保存筛选配置
     * @param appSysConfig 保存参数
     * @return 返回值
     */
    Long saveFilterConfig(EamAppConfigDto appSysConfig);

    /**
     * 查询筛选配置数据
     * @param id
     * @return
     */
    EamAppConfigVo queryFilterConfig(String classCode,Long cardId);


}
