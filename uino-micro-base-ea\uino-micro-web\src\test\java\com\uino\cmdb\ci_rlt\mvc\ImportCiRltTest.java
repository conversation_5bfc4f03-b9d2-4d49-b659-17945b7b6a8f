package com.uino.cmdb.ci_rlt.mvc;

import java.io.BufferedInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;

import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.core.io.FileSystemResource;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import com.alibaba.fastjson.JSON;
import com.binary.core.exception.MessageException;
import com.binary.framework.web.RemoteResult;
import com.binary.jdbc.Page;
import com.uino.StartBaseWebAppliaction;
import com.uino.dao.cmdb.ESCIClassSvc;
import com.uino.dao.cmdb.ESCIRltSvc;
import com.uino.dao.cmdb.ESCISvc;
import com.uino.dao.cmdb.ESRltClassSvc;
import com.uino.bean.cmdb.base.ESCIClassInfo;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.base.ESCIRltInfo;

import lombok.extern.slf4j.Slf4j;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = { StartBaseWebAppliaction.class }, webEnvironment = WebEnvironment.RANDOM_PORT)
@Slf4j
@ActiveProfiles("provider-local")
public class ImportCiRltTest {
	@Autowired
	private TestRestTemplate restTemplate;
	@MockBean
	private ESCIRltSvc esCiRltSvc;
	@MockBean
	private ESRltClassSvc esRltClassSvc;
	@MockBean
	private ESCISvc esCiSvc;
	@MockBean
	private ESCIClassSvc esClassSvc;
	private static String testUrl;

	@BeforeClass
	public static void initCls() {
		ImportCiRltTest.testUrl = "/cmdb/ciRlt/importCiRlt";
	}

	@Before
	public void before() {
		Page<ESCIRltInfo> oldRltPage = new Page<>(1, 111, 0, 1, new LinkedList<>());
		Mockito.when(esCiRltSvc.getListByQuery(Mockito.anyInt(), Mockito.anyInt(), Mockito.any()))
				.thenReturn(oldRltPage);

		ESCIClassInfo rltCls = new ESCIClassInfo();
		rltCls.setId(1L);
		rltCls.setClassCode("依赖");
		rltCls.setClassStdCode("依赖");
		rltCls.setClassName("依赖");
		List<ESCIClassInfo> rltClsInfos = Collections.singletonList(rltCls);
		Mockito.when(esRltClassSvc.getListByQuery(Mockito.any())).thenReturn(rltClsInfos);

		List<ESCIInfo> ciInfos = new LinkedList<>();
		ESCIInfo sCi = new ESCIInfo();
		ciInfos.add(sCi);
		sCi.setId(1L);
		sCi.setCiCode("sCiCode");
		sCi.setClassId(1L);
		ESCIInfo tCi = new ESCIInfo();
		ciInfos.add(tCi);
		tCi.setId(2L);
		tCi.setCiCode("tCiCode");
		tCi.setClassId(1L);
        Page<ESCIInfo> ciPage = new Page<>();
        ciPage.setData(ciInfos);
        Mockito.when(esCiSvc.getListByQuery(Mockito.anyInt(), Mockito.anyInt(), Mockito.any())).thenReturn(ciPage);

		List<ESCIClassInfo> ciClassInfos = new LinkedList<>();
		ESCIClassInfo cls = new ESCIClassInfo();
		ciClassInfos.add(cls);
		cls.setId(1L);
		cls.setClassCode("clsCode");
		cls.setClassStdCode("CLSCODE");
		cls.setClassName("clsName");
		Mockito.when(esClassSvc.getListByQuery(Mockito.any())).thenReturn(ciClassInfos);

		Mockito.when(esCiRltSvc.saveOrUpdateBatch(Mockito.anyList())).thenReturn(1);

	}

	@Test
	public void test01() {
		// byte[] fileBytes =
		// toByteArray("./src/test/resources/testdata/ciRlt.xls");
        FileSystemResource resource = new FileSystemResource(new File("./src/test/resources/testdata/ciRlt.xlsx"));
		MultiValueMap<String, Object> param = new LinkedMultiValueMap<>();
		param.add("file", resource);
        param.add("rltClsCodes", "依赖");
		String responseBodyStr = restTemplate.postForObject(testUrl, param, String.class);
		RemoteResult responseBody = JSON.parseObject(responseBodyStr, RemoteResult.class);
		Assert.assertTrue(responseBody.isSuccess());
	}

	public static byte[] toByteArray(String filename) {

		File f = new File(filename);
		if (!f.exists()) {
			log.error("导入测试文件不存在");
		}

		ByteArrayOutputStream bos = new ByteArrayOutputStream((int) f.length());
		BufferedInputStream in = null;
		try {
			in = new BufferedInputStream(new FileInputStream(f));
			int buf_size = 1024;
			byte[] buffer = new byte[buf_size];
			int len = 0;
			while (-1 != (len = in.read(buffer, 0, buf_size))) {
				bos.write(buffer, 0, len);
			}
			return bos.toByteArray();
		} catch (IOException e) {
			throw new RuntimeException(e);
		} finally {
			try {
				in.close();
				bos.close();
			} catch (IOException e) {
				throw new MessageException(e.getMessage());
			}

		}
	}
}
