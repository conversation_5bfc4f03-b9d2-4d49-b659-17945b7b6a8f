package com.uinnova.product.eam.db.diagram.es;


import com.uinnova.product.eam.base.diagram.model.ESUserShape;
import com.uinnova.product.eam.base.diagram.model.ESUserShapeQuery;
import com.uino.dao.AbstractESBaseDao;
import com.uino.service.util.FileUtil;
import org.springframework.stereotype.Repository;

import jakarta.annotation.PostConstruct;
import java.util.List;

/**
 * @Classname Es图形数据访问层
 * @Description 该类用来操作es中的图形index
 * <AUTHOR>
 * @Date 2021-08-25-13:57
 */
@Repository
public class ESShapeDao extends AbstractESBaseDao<ESUserShape, ESUserShapeQuery> {
    @Override
    public String getIndex() {
        return "uino_my_shape";
    }

    @Override
    public String getType() {
        return "_doc";
    }

    @PostConstruct
    public void init() {
        List<ESUserShape> list = FileUtil.getData("/initdata/uino_my_shape.json", ESUserShape.class);
        super.initIndex(list);
    }


}
