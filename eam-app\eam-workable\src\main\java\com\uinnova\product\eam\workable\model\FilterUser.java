package com.uinnova.product.eam.workable.model;

import lombok.Data;

import jakarta.persistence.*;

@Data
@Entity
@Table(name="flowable_filter_user")
public class FilterUser {

    @Id    //主键id
    private String id;

    @Column(name="flowableInstanceId")
    private String flowableInstanceId;

    @Column(name="flowableTaskKey")
    private String flowableTaskKey;

    @Column(name="agreeUserCode")
    private String agreeUserCode;

    @Column(name="domainId")
    private Integer domainId;

    @Column(name="createTime")
    private Long createTime;

    @Column(name="modifyTime")
    private Long modifyTime;

}