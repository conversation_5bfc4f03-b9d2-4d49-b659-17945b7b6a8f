package com.uinnova.product.vmdb.comm.rest.support;

import com.uinnova.product.vmdb.comm.i18n.MessageUtil;
import org.springframework.beans.factory.FactoryBean;

/**
 * 
 * <AUTHOR>
 *
 */
public class RestConsumerReferenceBean implements FactoryBean<Object> {

    private RestConsumerAware aware;

    public RestConsumerReferenceBean(RestConsumerAware aware) {
        MessageUtil.checkEmpty(aware, "aware");
        this.aware = aware;
    }

    @Override
    public Object getObject() throws Exception {
        return this.aware.getProxyObject();
    }

    @Override
    public Class<?> getObjectType() {
        return this.aware.getProxyClass();
    }

    @Override
    public boolean isSingleton() {
        return true;
    }

}
