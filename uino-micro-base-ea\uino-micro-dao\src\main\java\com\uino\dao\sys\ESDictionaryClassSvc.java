package com.uino.dao.sys;

import java.util.ArrayList;
import java.util.List;

import jakarta.annotation.PostConstruct;

import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import com.alibaba.fastjson.JSONObject;
import com.binary.core.util.BinaryUtils;
import com.uino.dao.AbstractESBaseDao;
import com.uino.dao.ESConst;
import com.uino.util.sys.CommonFileUtil;
import com.uino.bean.sys.base.ESDictionaryAttrDef;
import com.uino.bean.sys.base.ESDictionaryClassInfo;
import com.uino.bean.sys.query.ESDictionaryItemSearchBean;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
public class ESDictionaryClassSvc extends AbstractESBaseDao<ESDictionaryClassInfo, JSONObject> {

    @Override
    public String getIndex() {
        return ESConst.INDEX_SYS_DICTIONARY_CLASS;
    }

    @Override
    public String getType() {
        return ESConst.INDEX_SYS_DICTIONARY_CLASS;
    }

    @PostConstruct
    public void init() {
		List<ESDictionaryClassInfo> datas = CommonFileUtil.getData("/initdata/uino_sys_dictionary_class.json",
				ESDictionaryClassInfo.class);
        for (ESDictionaryClassInfo dictClassInfo : datas) {
            dictClassInfo.setIsInit(1);
			for (ESDictionaryAttrDef def : dictClassInfo.getDictAttrDefs()) {
			    def.setDoaminId(1L);
				if (BinaryUtils.isEmpty(def.getProStdName())) {
					def.setProStdName(def.getProName());
				}
			}
        }
        super.initIndex(datas);
    }

    public List<ESDictionaryClassInfo> queryDcitClassInfosByBean(ESDictionaryItemSearchBean bean) {
        BoolQueryBuilder clsQuery = getDictClassQueryBuilderByBean(bean);
        return super.getListByQuery(clsQuery);
    }

    private BoolQueryBuilder getDictClassQueryBuilderByBean(ESDictionaryItemSearchBean bean) {

        Long dictClassId = bean.getDictClassId();
        String dictCode = bean.getDictCode();
        String dictName = bean.getDictName();
        Assert.isTrue(dictClassId != null || dictCode != null || dictName != null, "X_PARAM_NOT_NULL${name:引用字典定义}");
        BoolQueryBuilder clsQuery = QueryBuilders.boolQuery();
        clsQuery.must(QueryBuilders.termQuery("domainId", bean.getDomainId()));
        if (!BinaryUtils.isEmpty(dictClassId)) {
            clsQuery.must(QueryBuilders.termQuery("id", dictClassId));
        }
        if (!BinaryUtils.isEmpty(dictCode)) {
            clsQuery.must(QueryBuilders.termQuery("dictCode.keyword", dictCode));
        }
        if (!BinaryUtils.isEmpty(dictName)) {
            clsQuery.must(QueryBuilders.termQuery("dictName.keyword", dictName));
        }
        return clsQuery;
    }
}
