package com.uinnova.product.vmdb.comm.model.image;

import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;

/**
 * 
 * <AUTHOR>
 *
 */
@Comment("图像表[CC_IMAGE]")
public class CCcImage implements Condition {
    private static final long serialVersionUID = 1L;

    @Comment("ID[ID] operate-Equal[=]")
    private Long id;

    @Comment("ID[ID] operate-In[in]")
    private Long[] ids;

    @Comment("ID[ID] operate-GTEqual[>=]")
    private Long startId;

    @Comment("ID[ID] operate-LTEqual[<=]")
    private Long endId;

    @Comment("图像名称[IMG_NAME] operate-Like[like]")
    private String imgName;

    @Comment("图像全名[IMG_FULL_NAME] operate-Like[like]    图像全名:目录名+图像名, 中间以|分隔")
    private String imgFullName;

    @Comment("图像分类[IMG_GROUP] operate-Equal[=]    图像分类:1=CI分类图标 2=视图背景图")
    private Integer imgGroup;

    @Comment("图像分类[IMG_GROUP] operate-In[in]    图像分类:1=CI分类图标 2=视图背景图")
    private Integer[] imgGroups;

    @Comment("图像分类[IMG_GROUP] operate-GTEqual[>=]    图像分类:1=CI分类图标 2=视图背景图")
    private Integer startImgGroup;

    @Comment("图像分类[IMG_GROUP] operate-LTEqual[<=]    图像分类:1=CI分类图标 2=视图背景图")
    private Integer endImgGroup;

    @Comment("所属目录[DIR_ID] operate-Equal[=]")
    private Long dirId;

    @Comment("所属目录[DIR_ID] operate-In[in]")
    private Long[] dirIds;

    @Comment("所属目录[DIR_ID] operate-GTEqual[>=]")
    private Long startDirId;

    @Comment("所属目录[DIR_ID] operate-LTEqual[<=]")
    private Long endDirId;

    @Comment("图像描述[IMG_DESC] operate-Like[like]")
    private String imgDesc;

    @Comment("图像格式[IMG_TYPE] operate-Equal[=]")
    private Integer imgType;

    @Comment("图像格式[IMG_TYPE] operate-In[in]")
    private Integer[] imgTypes;

    @Comment("图像格式[IMG_TYPE] operate-GTEqual[>=]")
    private Integer startImgType;

    @Comment("图像格式[IMG_TYPE] operate-LTEqual[<=]")
    private Integer endImgType;

    @Comment("图像类型[IMG_THEME] operate-Equal[=]    图像类型:红、黄、蓝等，待续")
    private Integer imgTheme;

    @Comment("图像类型[IMG_THEME] operate-In[in]    图像类型:红、黄、蓝等，待续")
    private Integer[] imgThemes;

    @Comment("图像类型[IMG_THEME] operate-GTEqual[>=]    图像类型:红、黄、蓝等，待续")
    private Integer startImgTheme;

    @Comment("图像类型[IMG_THEME] operate-LTEqual[<=]    图像类型:红、黄、蓝等，待续")
    private Integer endImgTheme;

    @Comment("保存位置[IMG_PATH] operate-Like[like]")
    private String imgPath;

    @Comment("保存位置[IMG_PATH] operate-In[in]")
    private String[] imgPaths;

    @Comment("图像范围[IMG_RANGE] operate-Equal[=]    图像范围:1=公共 2=私有")
    private Integer imgRange;

    @Comment("图像范围[IMG_RANGE] operate-In[in]    图像范围:1=公共 2=私有")
    private Integer[] imgRanges;

    @Comment("图像范围[IMG_RANGE] operate-GTEqual[>=]    图像范围:1=公共 2=私有")
    private Integer startImgRange;

    @Comment("图像范围[IMG_RANGE] operate-LTEqual[<=]    图像范围:1=公共 2=私有")
    private Integer endImgRange;

    @Comment("图标大小[IMG_SIZE] operate-Equal[=]    图标大小:单位：byte")
    private Integer imgSize;

    @Comment("图标大小[IMG_SIZE] operate-In[in]    图标大小:单位：byte")
    private Integer[] imgSizes;

    @Comment("图标大小[IMG_SIZE] operate-GTEqual[>=]    图标大小:单位：byte")
    private Integer startImgSize;

    @Comment("图标大小[IMG_SIZE] operate-LTEqual[<=]    图标大小:单位：byte")
    private Integer endImgSize;

    @Comment("图标宽度[IMG_WIDTH] operate-Equal[=]    图标宽度:单位：像素")
    private Integer imgWidth;

    @Comment("图标宽度[IMG_WIDTH] operate-In[in]    图标宽度:单位：像素")
    private Integer[] imgWidths;

    @Comment("图标宽度[IMG_WIDTH] operate-GTEqual[>=]    图标宽度:单位：像素")
    private Integer startImgWidth;

    @Comment("图标宽度[IMG_WIDTH] operate-LTEqual[<=]    图标宽度:单位：像素")
    private Integer endImgWidth;

    @Comment("图标高度[IMG_HEIGH] operate-Equal[=]    图标高度:单位：像素")
    private Integer imgHeigh;

    @Comment("图标高度[IMG_HEIGH] operate-In[in]    图标高度:单位：像素")
    private Integer[] imgHeighs;

    @Comment("图标高度[IMG_HEIGH] operate-GTEqual[>=]    图标高度:单位：像素")
    private Integer startImgHeigh;

    @Comment("图标高度[IMG_HEIGH] operate-LTEqual[<=]    图标高度:单位：像素")
    private Integer endImgHeigh;

    @Comment("上传人ID[UPOR_ID] operate-Equal[=]")
    private Long uporId;

    @Comment("上传人ID[UPOR_ID] operate-In[in]")
    private Long[] uporIds;

    @Comment("上传人ID[UPOR_ID] operate-GTEqual[>=]")
    private Long startUporId;

    @Comment("上传人ID[UPOR_ID] operate-LTEqual[<=]")
    private Long endUporId;

    @Comment("上传人姓名[UPOR_NAME] operate-Like[like]")
    private String uporName;

    @Comment("上传人姓名[UPOR_NAME] operate-Equal[=]")
    private String uporNameEqual;

    @Comment("上传人姓名[UPOR_NAME] operate-In[in]")
    private String[] uporNames;

    @Comment("上传时间[UP_TIME] operate-Equal[=]")
    private Long upTime;

    @Comment("上传时间[UP_TIME] operate-In[in]")
    private Long[] upTimes;

    @Comment("上传时间[UP_TIME] operate-GTEqual[>=]")
    private Long startUpTime;

    @Comment("上传时间[UP_TIME] operate-LTEqual[<=]")
    private Long endUpTime;

    @Comment("关联图标1[RLT_IMG_ID_1] operate-Equal[=]")
    private Long rltImgId1;

    @Comment("关联图标1[RLT_IMG_ID_1] operate-In[in]")
    private Long[] rltImgId1s;

    @Comment("关联图标1[RLT_IMG_ID_1] operate-GTEqual[>=]")
    private Long startRltImgId1;

    @Comment("关联图标1[RLT_IMG_ID_1] operate-LTEqual[<=]")
    private Long endRltImgId1;

    @Comment("关联图标2[RLT_IMG_ID_2] operate-Equal[=]")
    private Long rltImgId2;

    @Comment("关联图标2[RLT_IMG_ID_2] operate-In[in]")
    private Long[] rltImgId2s;

    @Comment("关联图标2[RLT_IMG_ID_2] operate-GTEqual[>=]")
    private Long startRltImgId2;

    @Comment("关联图标2[RLT_IMG_ID_2] operate-LTEqual[<=]")
    private Long endRltImgId2;

    @Comment("关联图标3[RLT_IMG_ID_3] operate-Equal[=]")
    private Long rltImgId3;

    @Comment("关联图标3[RLT_IMG_ID_3] operate-In[in]")
    private Long[] rltImgId3s;

    @Comment("关联图标3[RLT_IMG_ID_3] operate-GTEqual[>=]")
    private Long startRltImgId3;

    @Comment("关联图标3[RLT_IMG_ID_3] operate-LTEqual[<=]")
    private Long endRltImgId3;

    @Comment("备用_1[CUSTOM_1] operate-Equal[=]")
    private Long custom1;

    @Comment("备用_1[CUSTOM_1] operate-In[in]")
    private Long[] custom1s;

    @Comment("备用_1[CUSTOM_1] operate-GTEqual[>=]")
    private Long startCustom1;

    @Comment("备用_1[CUSTOM_1] operate-LTEqual[<=]")
    private Long endCustom1;

    @Comment("备用_2[CUSTOM_2] operate-Equal[=]")
    private Long custom2;

    @Comment("备用_2[CUSTOM_2] operate-In[in]")
    private Long[] custom2s;

    @Comment("备用_2[CUSTOM_2] operate-GTEqual[>=]")
    private Long startCustom2;

    @Comment("备用_2[CUSTOM_2] operate-LTEqual[<=]")
    private Long endCustom2;

    @Comment("备用_3[CUSTOM_3] operate-Equal[=]")
    private Long custom3;

    @Comment("备用_3[CUSTOM_3] operate-In[in]")
    private Long[] custom3s;

    @Comment("备用_3[CUSTOM_3] operate-GTEqual[>=]")
    private Long startCustom3;

    @Comment("备用_3[CUSTOM_3] operate-LTEqual[<=]")
    private Long endCustom3;

    @Comment("备用_4[CUSTOM_4] operate-Like[like]")
    private String custom4;

    @Comment("备用_4[CUSTOM_4] operate-Equal[=]")
    private String custom4Equal;

    @Comment("备用_4[CUSTOM_4] operate-In[in]")
    private String[] custom4s;

    @Comment("备用_5[CUSTOM_5] operate-Like[like]")
    private String custom5;

    @Comment("备用_5[CUSTOM_5] operate-Equal[=]")
    private String custom5Equal;

    @Comment("备用_5[CUSTOM_5] operate-In[in]")
    private String[] custom5s;

    @Comment("备用_6[CUSTOM_6] operate-Like[like]")
    private String custom6;

    @Comment("备用_6[CUSTOM_6] operate-Equal[=]")
    private String custom6Equal;

    @Comment("备用_6[CUSTOM_6] operate-In[in]")
    private String[] custom6s;

    @Comment("搜索字段[SEARCH_FIELD] operate-Like[like]    搜索字段:图像名称")
    private String searchField;

    @Comment("排序字段[ORDER_NO] operate-Equal[=]")
    private Long orderNo;

    @Comment("排序字段[ORDER_NO] operate-In[in]")
    private Long[] orderNos;

    @Comment("排序字段[ORDER_NO] operate-GTEqual[>=]")
    private Long startOrderNo;

    @Comment("排序字段[ORDER_NO] operate-LTEqual[<=]")
    private Long endOrderNo;

    @Comment("所属域[DOMAIN_ID] operate-Equal[=]")
    private Long domainId;

    @Comment("所属域[DOMAIN_ID] operate-In[in]")
    private Long[] domainIds;

    @Comment("所属域[DOMAIN_ID] operate-GTEqual[>=]")
    private Long startDomainId;

    @Comment("所属域[DOMAIN_ID] operate-LTEqual[<=]")
    private Long endDomainId;

    @Comment("创建人[CREATOR] operate-Like[like]")
    private String creator;

    @Comment("创建人[CREATOR] operate-Equal[=]")
    private String creatorEqual;

    @Comment("创建人[CREATOR] operate-In[in]")
    private String[] creators;

    @Comment("修改人[MODIFIER] operate-Like[like]")
    private String modifier;

    @Comment("修改人[MODIFIER] operate-Equal[=]")
    private String modifierEqual;

    @Comment("修改人[MODIFIER] operate-In[in]")
    private String[] modifiers;

    @Comment("数据状态[DATA_STATUS] operate-Equal[=]    数据状态:数据状态：1-正常 0-删除")
    private Integer dataStatus;

    @Comment("数据状态[DATA_STATUS] operate-In[in]    数据状态:数据状态：1-正常 0-删除")
    private Integer[] dataStatuss;

    @Comment("数据状态[DATA_STATUS] operate-GTEqual[>=]    数据状态:数据状态：1-正常 0-删除")
    private Integer startDataStatus;

    @Comment("数据状态[DATA_STATUS] operate-LTEqual[<=]    数据状态:数据状态：1-正常 0-删除")
    private Integer endDataStatus;

    @Comment("创建时间[CREATE_TIME] operate-Equal[=]    创建时间:yyyyMMddHHmmss")
    private Long createTime;

    @Comment("创建时间[CREATE_TIME] operate-In[in]    创建时间:yyyyMMddHHmmss")
    private Long[] createTimes;

    @Comment("创建时间[CREATE_TIME] operate-GTEqual[>=]    创建时间:yyyyMMddHHmmss")
    private Long startCreateTime;

    @Comment("创建时间[CREATE_TIME] operate-LTEqual[<=]    创建时间:yyyyMMddHHmmss")
    private Long endCreateTime;

    @Comment("修改时间[MODIFY_TIME] operate-Equal[=]    修改时间:yyyyMMddHHmmss")
    private Long modifyTime;

    @Comment("修改时间[MODIFY_TIME] operate-In[in]    修改时间:yyyyMMddHHmmss")
    private Long[] modifyTimes;

    @Comment("修改时间[MODIFY_TIME] operate-GTEqual[>=]    修改时间:yyyyMMddHHmmss")
    private Long startModifyTime;

    @Comment("修改时间[MODIFY_TIME] operate-LTEqual[<=]    修改时间:yyyyMMddHHmmss")
    private Long endModifyTime;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long[] getIds() {
        return this.ids;
    }

    public void setIds(Long[] ids) {
        this.ids = ids;
    }

    public Long getStartId() {
        return this.startId;
    }

    public void setStartId(Long startId) {
        this.startId = startId;
    }

    public Long getEndId() {
        return this.endId;
    }

    public void setEndId(Long endId) {
        this.endId = endId;
    }

    public String getImgName() {
        return this.imgName;
    }

    public void setImgName(String imgName) {
        this.imgName = imgName;
    }

    public String getImgFullName() {
        return this.imgFullName;
    }

    public void setImgFullName(String imgFullName) {
        this.imgFullName = imgFullName;
    }

    public Integer getImgGroup() {
        return this.imgGroup;
    }

    public void setImgGroup(Integer imgGroup) {
        this.imgGroup = imgGroup;
    }

    public Integer[] getImgGroups() {
        return this.imgGroups;
    }

    public void setImgGroups(Integer[] imgGroups) {
        this.imgGroups = imgGroups;
    }

    public Integer getStartImgGroup() {
        return this.startImgGroup;
    }

    public void setStartImgGroup(Integer startImgGroup) {
        this.startImgGroup = startImgGroup;
    }

    public Integer getEndImgGroup() {
        return this.endImgGroup;
    }

    public void setEndImgGroup(Integer endImgGroup) {
        this.endImgGroup = endImgGroup;
    }

    public Long getDirId() {
        return this.dirId;
    }

    public void setDirId(Long dirId) {
        this.dirId = dirId;
    }

    public Long[] getDirIds() {
        return this.dirIds;
    }

    public void setDirIds(Long[] dirIds) {
        this.dirIds = dirIds;
    }

    public Long getStartDirId() {
        return this.startDirId;
    }

    public void setStartDirId(Long startDirId) {
        this.startDirId = startDirId;
    }

    public Long getEndDirId() {
        return this.endDirId;
    }

    public void setEndDirId(Long endDirId) {
        this.endDirId = endDirId;
    }

    public String getImgDesc() {
        return this.imgDesc;
    }

    public void setImgDesc(String imgDesc) {
        this.imgDesc = imgDesc;
    }

    public Integer getImgType() {
        return this.imgType;
    }

    public void setImgType(Integer imgType) {
        this.imgType = imgType;
    }

    public Integer[] getImgTypes() {
        return this.imgTypes;
    }

    public void setImgTypes(Integer[] imgTypes) {
        this.imgTypes = imgTypes;
    }

    public Integer getStartImgType() {
        return this.startImgType;
    }

    public void setStartImgType(Integer startImgType) {
        this.startImgType = startImgType;
    }

    public Integer getEndImgType() {
        return this.endImgType;
    }

    public void setEndImgType(Integer endImgType) {
        this.endImgType = endImgType;
    }

    public Integer getImgTheme() {
        return this.imgTheme;
    }

    public void setImgTheme(Integer imgTheme) {
        this.imgTheme = imgTheme;
    }

    public Integer[] getImgThemes() {
        return this.imgThemes;
    }

    public void setImgThemes(Integer[] imgThemes) {
        this.imgThemes = imgThemes;
    }

    public Integer getStartImgTheme() {
        return this.startImgTheme;
    }

    public void setStartImgTheme(Integer startImgTheme) {
        this.startImgTheme = startImgTheme;
    }

    public Integer getEndImgTheme() {
        return this.endImgTheme;
    }

    public void setEndImgTheme(Integer endImgTheme) {
        this.endImgTheme = endImgTheme;
    }

    public String getImgPath() {
        return this.imgPath;
    }

    public void setImgPath(String imgPath) {
        this.imgPath = imgPath;
    }

    public String[] getImgPaths() {
        return imgPaths;
    }

    public void setImgPaths(String[] imgPaths) {
        this.imgPaths = imgPaths;
    }

    public Integer getImgRange() {
        return this.imgRange;
    }

    public void setImgRange(Integer imgRange) {
        this.imgRange = imgRange;
    }

    public Integer[] getImgRanges() {
        return this.imgRanges;
    }

    public void setImgRanges(Integer[] imgRanges) {
        this.imgRanges = imgRanges;
    }

    public Integer getStartImgRange() {
        return this.startImgRange;
    }

    public void setStartImgRange(Integer startImgRange) {
        this.startImgRange = startImgRange;
    }

    public Integer getEndImgRange() {
        return this.endImgRange;
    }

    public void setEndImgRange(Integer endImgRange) {
        this.endImgRange = endImgRange;
    }

    public Integer getImgSize() {
        return this.imgSize;
    }

    public void setImgSize(Integer imgSize) {
        this.imgSize = imgSize;
    }

    public Integer[] getImgSizes() {
        return this.imgSizes;
    }

    public void setImgSizes(Integer[] imgSizes) {
        this.imgSizes = imgSizes;
    }

    public Integer getStartImgSize() {
        return this.startImgSize;
    }

    public void setStartImgSize(Integer startImgSize) {
        this.startImgSize = startImgSize;
    }

    public Integer getEndImgSize() {
        return this.endImgSize;
    }

    public void setEndImgSize(Integer endImgSize) {
        this.endImgSize = endImgSize;
    }

    public Integer getImgWidth() {
        return this.imgWidth;
    }

    public void setImgWidth(Integer imgWidth) {
        this.imgWidth = imgWidth;
    }

    public Integer[] getImgWidths() {
        return this.imgWidths;
    }

    public void setImgWidths(Integer[] imgWidths) {
        this.imgWidths = imgWidths;
    }

    public Integer getStartImgWidth() {
        return this.startImgWidth;
    }

    public void setStartImgWidth(Integer startImgWidth) {
        this.startImgWidth = startImgWidth;
    }

    public Integer getEndImgWidth() {
        return this.endImgWidth;
    }

    public void setEndImgWidth(Integer endImgWidth) {
        this.endImgWidth = endImgWidth;
    }

    public Integer getImgHeigh() {
        return this.imgHeigh;
    }

    public void setImgHeigh(Integer imgHeigh) {
        this.imgHeigh = imgHeigh;
    }

    public Integer[] getImgHeighs() {
        return this.imgHeighs;
    }

    public void setImgHeighs(Integer[] imgHeighs) {
        this.imgHeighs = imgHeighs;
    }

    public Integer getStartImgHeigh() {
        return this.startImgHeigh;
    }

    public void setStartImgHeigh(Integer startImgHeigh) {
        this.startImgHeigh = startImgHeigh;
    }

    public Integer getEndImgHeigh() {
        return this.endImgHeigh;
    }

    public void setEndImgHeigh(Integer endImgHeigh) {
        this.endImgHeigh = endImgHeigh;
    }

    public Long getUporId() {
        return this.uporId;
    }

    public void setUporId(Long uporId) {
        this.uporId = uporId;
    }

    public Long[] getUporIds() {
        return this.uporIds;
    }

    public void setUporIds(Long[] uporIds) {
        this.uporIds = uporIds;
    }

    public Long getStartUporId() {
        return this.startUporId;
    }

    public void setStartUporId(Long startUporId) {
        this.startUporId = startUporId;
    }

    public Long getEndUporId() {
        return this.endUporId;
    }

    public void setEndUporId(Long endUporId) {
        this.endUporId = endUporId;
    }

    public String getUporName() {
        return this.uporName;
    }

    public void setUporName(String uporName) {
        this.uporName = uporName;
    }

    public String getUporNameEqual() {
        return this.uporNameEqual;
    }

    public void setUporNameEqual(String uporNameEqual) {
        this.uporNameEqual = uporNameEqual;
    }

    public String[] getUporNames() {
        return this.uporNames;
    }

    public void setUporNames(String[] uporNames) {
        this.uporNames = uporNames;
    }

    public Long getUpTime() {
        return this.upTime;
    }

    public void setUpTime(Long upTime) {
        this.upTime = upTime;
    }

    public Long[] getUpTimes() {
        return this.upTimes;
    }

    public void setUpTimes(Long[] upTimes) {
        this.upTimes = upTimes;
    }

    public Long getStartUpTime() {
        return this.startUpTime;
    }

    public void setStartUpTime(Long startUpTime) {
        this.startUpTime = startUpTime;
    }

    public Long getEndUpTime() {
        return this.endUpTime;
    }

    public void setEndUpTime(Long endUpTime) {
        this.endUpTime = endUpTime;
    }

    public Long getRltImgId1() {
        return this.rltImgId1;
    }

    public void setRltImgId1(Long rltImgId1) {
        this.rltImgId1 = rltImgId1;
    }

    public Long[] getRltImgId1s() {
        return this.rltImgId1s;
    }

    public void setRltImgId1s(Long[] rltImgId1s) {
        this.rltImgId1s = rltImgId1s;
    }

    public Long getStartRltImgId1() {
        return this.startRltImgId1;
    }

    public void setStartRltImgId1(Long startRltImgId1) {
        this.startRltImgId1 = startRltImgId1;
    }

    public Long getEndRltImgId1() {
        return this.endRltImgId1;
    }

    public void setEndRltImgId1(Long endRltImgId1) {
        this.endRltImgId1 = endRltImgId1;
    }

    public Long getRltImgId2() {
        return this.rltImgId2;
    }

    public void setRltImgId2(Long rltImgId2) {
        this.rltImgId2 = rltImgId2;
    }

    public Long[] getRltImgId2s() {
        return this.rltImgId2s;
    }

    public void setRltImgId2s(Long[] rltImgId2s) {
        this.rltImgId2s = rltImgId2s;
    }

    public Long getStartRltImgId2() {
        return this.startRltImgId2;
    }

    public void setStartRltImgId2(Long startRltImgId2) {
        this.startRltImgId2 = startRltImgId2;
    }

    public Long getEndRltImgId2() {
        return this.endRltImgId2;
    }

    public void setEndRltImgId2(Long endRltImgId2) {
        this.endRltImgId2 = endRltImgId2;
    }

    public Long getRltImgId3() {
        return this.rltImgId3;
    }

    public void setRltImgId3(Long rltImgId3) {
        this.rltImgId3 = rltImgId3;
    }

    public Long[] getRltImgId3s() {
        return this.rltImgId3s;
    }

    public void setRltImgId3s(Long[] rltImgId3s) {
        this.rltImgId3s = rltImgId3s;
    }

    public Long getStartRltImgId3() {
        return this.startRltImgId3;
    }

    public void setStartRltImgId3(Long startRltImgId3) {
        this.startRltImgId3 = startRltImgId3;
    }

    public Long getEndRltImgId3() {
        return this.endRltImgId3;
    }

    public void setEndRltImgId3(Long endRltImgId3) {
        this.endRltImgId3 = endRltImgId3;
    }

    public Long getCustom1() {
        return this.custom1;
    }

    public void setCustom1(Long custom1) {
        this.custom1 = custom1;
    }

    public Long[] getCustom1s() {
        return this.custom1s;
    }

    public void setCustom1s(Long[] custom1s) {
        this.custom1s = custom1s;
    }

    public Long getStartCustom1() {
        return this.startCustom1;
    }

    public void setStartCustom1(Long startCustom1) {
        this.startCustom1 = startCustom1;
    }

    public Long getEndCustom1() {
        return this.endCustom1;
    }

    public void setEndCustom1(Long endCustom1) {
        this.endCustom1 = endCustom1;
    }

    public Long getCustom2() {
        return this.custom2;
    }

    public void setCustom2(Long custom2) {
        this.custom2 = custom2;
    }

    public Long[] getCustom2s() {
        return this.custom2s;
    }

    public void setCustom2s(Long[] custom2s) {
        this.custom2s = custom2s;
    }

    public Long getStartCustom2() {
        return this.startCustom2;
    }

    public void setStartCustom2(Long startCustom2) {
        this.startCustom2 = startCustom2;
    }

    public Long getEndCustom2() {
        return this.endCustom2;
    }

    public void setEndCustom2(Long endCustom2) {
        this.endCustom2 = endCustom2;
    }

    public Long getCustom3() {
        return this.custom3;
    }

    public void setCustom3(Long custom3) {
        this.custom3 = custom3;
    }

    public Long[] getCustom3s() {
        return this.custom3s;
    }

    public void setCustom3s(Long[] custom3s) {
        this.custom3s = custom3s;
    }

    public Long getStartCustom3() {
        return this.startCustom3;
    }

    public void setStartCustom3(Long startCustom3) {
        this.startCustom3 = startCustom3;
    }

    public Long getEndCustom3() {
        return this.endCustom3;
    }

    public void setEndCustom3(Long endCustom3) {
        this.endCustom3 = endCustom3;
    }

    public String getCustom4() {
        return this.custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }

    public String getCustom4Equal() {
        return this.custom4Equal;
    }

    public void setCustom4Equal(String custom4Equal) {
        this.custom4Equal = custom4Equal;
    }

    public String[] getCustom4s() {
        return this.custom4s;
    }

    public void setCustom4s(String[] custom4s) {
        this.custom4s = custom4s;
    }

    public String getCustom5() {
        return this.custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }

    public String getCustom5Equal() {
        return this.custom5Equal;
    }

    public void setCustom5Equal(String custom5Equal) {
        this.custom5Equal = custom5Equal;
    }

    public String[] getCustom5s() {
        return this.custom5s;
    }

    public void setCustom5s(String[] custom5s) {
        this.custom5s = custom5s;
    }

    public String getCustom6() {
        return this.custom6;
    }

    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }

    public String getCustom6Equal() {
        return this.custom6Equal;
    }

    public void setCustom6Equal(String custom6Equal) {
        this.custom6Equal = custom6Equal;
    }

    public String[] getCustom6s() {
        return this.custom6s;
    }

    public void setCustom6s(String[] custom6s) {
        this.custom6s = custom6s;
    }

    public String getSearchField() {
        return this.searchField;
    }

    public void setSearchField(String searchField) {
        this.searchField = searchField;
    }

    public Long getOrderNo() {
        return this.orderNo;
    }

    public void setOrderNo(Long orderNo) {
        this.orderNo = orderNo;
    }

    public Long[] getOrderNos() {
        return this.orderNos;
    }

    public void setOrderNos(Long[] orderNos) {
        this.orderNos = orderNos;
    }

    public Long getStartOrderNo() {
        return this.startOrderNo;
    }

    public void setStartOrderNo(Long startOrderNo) {
        this.startOrderNo = startOrderNo;
    }

    public Long getEndOrderNo() {
        return this.endOrderNo;
    }

    public void setEndOrderNo(Long endOrderNo) {
        this.endOrderNo = endOrderNo;
    }

    public Long getDomainId() {
        return this.domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }

    public Long[] getDomainIds() {
        return this.domainIds;
    }

    public void setDomainIds(Long[] domainIds) {
        this.domainIds = domainIds;
    }

    public Long getStartDomainId() {
        return this.startDomainId;
    }

    public void setStartDomainId(Long startDomainId) {
        this.startDomainId = startDomainId;
    }

    public Long getEndDomainId() {
        return this.endDomainId;
    }

    public void setEndDomainId(Long endDomainId) {
        this.endDomainId = endDomainId;
    }

    public String getCreator() {
        return this.creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getCreatorEqual() {
        return this.creatorEqual;
    }

    public void setCreatorEqual(String creatorEqual) {
        this.creatorEqual = creatorEqual;
    }

    public String[] getCreators() {
        return this.creators;
    }

    public void setCreators(String[] creators) {
        this.creators = creators;
    }

    public String getModifier() {
        return this.modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public String getModifierEqual() {
        return this.modifierEqual;
    }

    public void setModifierEqual(String modifierEqual) {
        this.modifierEqual = modifierEqual;
    }

    public String[] getModifiers() {
        return this.modifiers;
    }

    public void setModifiers(String[] modifiers) {
        this.modifiers = modifiers;
    }

    public Integer getDataStatus() {
        return this.dataStatus;
    }

    public void setDataStatus(Integer dataStatus) {
        this.dataStatus = dataStatus;
    }

    public Integer[] getDataStatuss() {
        return this.dataStatuss;
    }

    public void setDataStatuss(Integer[] dataStatuss) {
        this.dataStatuss = dataStatuss;
    }

    public Integer getStartDataStatus() {
        return this.startDataStatus;
    }

    public void setStartDataStatus(Integer startDataStatus) {
        this.startDataStatus = startDataStatus;
    }

    public Integer getEndDataStatus() {
        return this.endDataStatus;
    }

    public void setEndDataStatus(Integer endDataStatus) {
        this.endDataStatus = endDataStatus;
    }

    public Long getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long[] getCreateTimes() {
        return this.createTimes;
    }

    public void setCreateTimes(Long[] createTimes) {
        this.createTimes = createTimes;
    }

    public Long getStartCreateTime() {
        return this.startCreateTime;
    }

    public void setStartCreateTime(Long startCreateTime) {
        this.startCreateTime = startCreateTime;
    }

    public Long getEndCreateTime() {
        return this.endCreateTime;
    }

    public void setEndCreateTime(Long endCreateTime) {
        this.endCreateTime = endCreateTime;
    }

    public Long getModifyTime() {
        return this.modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Long[] getModifyTimes() {
        return this.modifyTimes;
    }

    public void setModifyTimes(Long[] modifyTimes) {
        this.modifyTimes = modifyTimes;
    }

    public Long getStartModifyTime() {
        return this.startModifyTime;
    }

    public void setStartModifyTime(Long startModifyTime) {
        this.startModifyTime = startModifyTime;
    }

    public Long getEndModifyTime() {
        return this.endModifyTime;
    }

    public void setEndModifyTime(Long endModifyTime) {
        this.endModifyTime = endModifyTime;
    }

}
