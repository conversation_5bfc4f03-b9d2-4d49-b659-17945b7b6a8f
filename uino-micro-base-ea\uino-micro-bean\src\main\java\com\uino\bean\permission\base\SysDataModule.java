package com.uino.bean.permission.base;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

/**
 * mapping-table: 数据模块表[SYS_DATA_MODULE]
 *
 * <AUTHOR>
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "数据模块表", description = "数据模块表")
public class SysDataModule implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ApiModelProperty(value = "id", example = "123")
    private Long id;

    /**
     * 数据模块名称
     */
    @ApiModelProperty(value = "数据模块名称")
    private String dataModuleName;

    /**
     * 数据模块代码(处理数据权限的类别)
     */
    @ApiModelProperty(value = "数据模块代码(处理数据权限的类别)")
    private String dataModuleCode;

    /**
     * 数据模块描述
     */
    @ApiModelProperty(value = "数据模块描述")
    private String dataModuleDesc;

    /**
     * 数据来源地址
     */
    @ApiModelProperty(value = "数据来源地址")
    private String dataSourceUrl;

    /**
     * 显示排序
     */
    @ApiModelProperty(value = "显示排序")
    private Integer orderNo;

    /**
     * 复制权限
     */
    @ApiModelProperty(value = "复制权限")
    private Integer isdelete;

    /**
     * 修改权限
     */
    @ApiModelProperty(value = "修改权限")
    private Integer isupdate;

    /**
     * 创建权限
     */
    @ApiModelProperty(value = "创建权限")
    private Integer iscreate;

    /**
     * 查看权限
     */
    @ApiModelProperty(value = "查看权限")
    private Integer issee;

    /**
     * 是否启用(1:启用、0：不启用)
     */
    @ApiModelProperty(value = "是否启用(1:启用、0：不启用)")
    private Long isenable;

    /**
     * 数据展示形式（1：列表、2：.树状）
     */
    @ApiModelProperty(value = "数据展示形式（1：列表、2：.树状）")
    private Long displayType;

    /**
     * 所属域
     */
    @ApiModelProperty(value = "所属域")
    private Long domainId;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String creator;

    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private String modifier;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Long createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Long modifyTime;

    /**
     * 扩展权限
     */
    @ApiModelProperty(value = "扩展权限")
    private List<ExtendedPermission> extendedPermissions;

    @ApiModelProperty(value = "提示语描述")
    private Map<String, String> tipDesc = new HashMap<>();

    /**
     * 扩展自定义页面
     */
    @ApiModelProperty(value = "iframeUrl")
    private String iframeUrl;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getDataModuleName() {
        return this.dataModuleName;
    }

    public void setDataModuleName(String dataModuleName) {
        this.dataModuleName = dataModuleName;
    }

    public String getDataModuleCode() {
        return this.dataModuleCode;
    }

    public void setDataModuleCode(String dataModuleCode) {
        this.dataModuleCode = dataModuleCode;
    }

    public String getDataModuleDesc() {
        return this.dataModuleDesc;
    }

    public void setDataModuleDesc(String dataModuleDesc) {
        this.dataModuleDesc = dataModuleDesc;
    }

    public String getDataSourceUrl() {
        return this.dataSourceUrl;
    }

    public void setDataSourceUrl(String dataSourceUrl) {
        this.dataSourceUrl = dataSourceUrl;
    }

    public Integer getOrderNo() {
        return this.orderNo;
    }

    public void setOrderNo(Integer orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getIsdelete() {
        return this.isdelete;
    }

    public void setIsdelete(Integer isdelete) {
        this.isdelete = isdelete;
    }

    public Integer getIsupdate() {
        return this.isupdate;
    }

    public void setIsupdate(Integer isupdate) {
        this.isupdate = isupdate;
    }

    public Integer getIscreate() {
        return this.iscreate;
    }

    public void setIscreate(Integer iscreate) {
        this.iscreate = iscreate;
    }

    public Integer getIssee() {
        return this.issee;
    }

    public void setIssee(Integer issee) {
        this.issee = issee;
    }

    public Long getIsenable() {
        return this.isenable;
    }

    public void setIsenable(Long isenable) {
        this.isenable = isenable;
    }

    public Long getDisplayType() {
        return this.displayType;
    }

    public void setDisplayType(Long displayType) {
        this.displayType = displayType;
    }

    public Long getDomainId() {
        return this.domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }

    public String getCreator() {
        return this.creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getModifier() {
        return this.modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public Long getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getModifyTime() {
        return this.modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }

    public List<ExtendedPermission> getExtendedPermissions() {
        return extendedPermissions;
    }

    public void setExtendedPermissions(List<ExtendedPermission> extendedPermissions) {
        this.extendedPermissions = extendedPermissions;
    }

    public Map<String, String> getTipDesc() {
        return tipDesc;
    }

    public void setTipDesc(Map<String, String> tipDesc) {
        this.tipDesc = tipDesc;
    }

    public String getIframeUrl() {
        return iframeUrl;
    }

    public void setIframeUrl(String iframeUrl) {
        this.iframeUrl = iframeUrl;
    }
}
