package com.uino.bean.cmdb.business;

import java.util.Set;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 删除关系请求数据传输类
 * 
 * <AUTHOR>
 *
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value="删除关系请求数据传输类",description = "删除关系请求数据传输类信息")
public class DelRltReqDto {

	/**
	 * 关系ids
	 */
	@ApiModelProperty(value="关系id集合")
	private Set<Long> rltIds;

	/**
	 * 关系codes
	 */
	@ApiModelProperty(value="关系codes")
	private Set<String> rltCodes;

	/**
	 * 用户code
	 */
	@ApiModelProperty(value="用户code")
	private String ownerCode;

	/**
	 * 是否强制删除：忽略数据来源
	 */
	@ApiModelProperty(value="用户code")
	private Boolean dropForce;
}
