package com.uinnova.product.eam.web.eam.mvc;

import com.binary.core.io.Resource;
import com.binary.core.io.support.FileResource;
import com.binary.framework.util.ControllerUtils;
import com.binary.framework.web.RemoteResult;
import com.uinnova.product.eam.comm.bean.WordDoc;
import com.uinnova.product.eam.service.IEamWordSvc;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

/**
 * 文档的上传下载删除接口
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/eam/word")
public class EamWordMvc {

    @Autowired
    private IEamWordSvc wordPeer;

    /**
     * 上传文档
     *
     * @param ciCode   关联的ciId
     * @param wordFile 实际的文档
     * @return 上传结果
     */
    @PostMapping("/uploadWord")
    public RemoteResult uploadWord(String ciCode, MultipartFile[] wordFile) {
        ArrayList<String> strings = new ArrayList<>();
        Map<String,WordDoc> map=new HashMap<>();
        for (MultipartFile multipartFile : wordFile) {
            WordDoc wordDoc = wordPeer.uploadWord(ciCode, multipartFile);
            if (wordDoc==null) {
                strings.add(multipartFile.getOriginalFilename());
            }else {
                map.put(wordDoc.getCiCode(),wordDoc);
            }
        }
        if (strings.size() == 0) {
            return new RemoteResult(true, -1,"文档上传成功",map);
        } else {
            return new RemoteResult(false, 500, "文档上传失败，请检查文档格式，稍后再试", strings);
        }

    }

    @GetMapping("/wordDocIsExist")
    public RemoteResult wordDocIsExist(String ciCode, String fileName) {
        return new RemoteResult(wordPeer.wordDocIsExist(ciCode, fileName));
    }

    /**
     * 根据id下载文档
     *
     * @param id 文档id
     * @return 文档
     */
    @GetMapping("/downloadWord")
    public ModelAndView downloadWord(Long id, HttpServletRequest request, HttpServletResponse response) {
        WordDoc wordDoc = wordPeer.getById(id);
        if (wordDoc == null) {
            return null;
        }
        File file = new File(wordDoc.getSavePth());
        String[] split = wordDoc.getSavePth().split("\\.");
        Resource resource = new FileResource(file);
        return ControllerUtils.returnResource(request, response, resource,
                null, false, wordDoc.getDocName() + "." + split[1]);
    }

    /**
     * 删除doc文档
     *
     * @param id 文档id
     * @return 执行结果
     */
    @PostMapping("/deleteWord")
    public RemoteResult deleteWord(Long id) {
        return new RemoteResult(wordPeer.deleteWord(id));
    }

    /**
     * 根据ciCode查询所关联的doc文档信息
     *
     * @return 分页数据
     */
    @GetMapping("/getWordDocByCiCode")
    public RemoteResult getWordDocByCiId(String ciCode, Integer pageNum, Integer pageSize) {
        if (pageNum == null || pageNum < 0) {
            pageNum = 0;
        }
        if (pageSize == null || pageSize > 2000) {
            pageSize = 10;
        }
        return new RemoteResult(wordPeer.getByCiCode(ciCode, pageNum, pageSize));
    }
}
