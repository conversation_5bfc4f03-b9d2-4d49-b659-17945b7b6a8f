package com.uinnova.product.eam.service;

import com.uinnova.product.eam.comm.bean.DirSettingBO;
import com.uinnova.product.eam.comm.bean.DirSettingDTO;
import com.uinnova.product.eam.comm.model.es.ClassSetting;
import com.uinnova.product.eam.model.ClassSettingDTO;
import com.uinnova.product.eam.model.dmv.ClassDefinitionVO;
import com.uino.bean.cmdb.base.ESCIAttrDefInfo;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface IOperateSettingService {

    Long saveClassSetting(ClassSetting classSetting);

    Integer deleteClassSettingById(Long id);

    Boolean addDirSetting(DirSettingBO dirSettingBO);

    List<ClassSetting> getClassSettingList(Long artifactId);

    List<DirSettingDTO> getDirSettingByClassId(Long classId);

    Integer saveClassSettingList(List<ClassSetting> classSettings);

    List<ClassSetting> getEsSettings();

    Integer judgeDirLevel(Long parentId);

    List<ClassSettingDTO> isMkClassDir();

    List<ClassDefinitionVO> findClassDefinitionList();

    Boolean addPlanBindClass(Long classId);
}
