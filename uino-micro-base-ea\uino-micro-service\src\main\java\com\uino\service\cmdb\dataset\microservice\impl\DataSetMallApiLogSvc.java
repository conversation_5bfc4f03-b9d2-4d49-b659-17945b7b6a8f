package com.uino.service.cmdb.dataset.microservice.impl;

import com.binary.jdbc.Page;
import com.uino.bean.cmdb.base.dataset.log.DataSetMallApiLog;
import com.uino.dao.cmdb.dataset.ESDataSetMallApiLogSvc;
import com.uino.service.cmdb.dataset.microservice.IDataSetMallApiLogSvc;
import com.uino.dao.util.ESUtil;

import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.sort.SortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @Classname DataSetMallApiLogServiceImpl
 * @Description TODO
 * @Date 2020/3/30 16:36
 * @Created by sh
 */
@Service
public class DataSetMallApiLogSvc implements IDataSetMallApiLogSvc {


    @Autowired
    private ESDataSetMallApiLogSvc dataSetMallApiLogSvc;


    @Override
    public Long save(DataSetMallApiLog dataSetMallApiLog) {
        dataSetMallApiLog.setId(ESUtil.getUUID());
        dataSetMallApiLog.setCreateTime(System.currentTimeMillis());
        return dataSetMallApiLogSvc.saveOrUpdate(dataSetMallApiLog);
    }

    @Override
    public Page<DataSetMallApiLog> findPage(String dataSetId, int pageNum, int pageSize, String respUserName, String respUserCode) {
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("dataSetMallApiId", dataSetId));
        if (respUserName != null && !"".equals(respUserName)) {
            query.must(QueryBuilders.termQuery("respUserName.keyword", respUserName));
        }
        if (respUserCode != null && !"".equals(respUserCode)) {
            query.must(QueryBuilders.termQuery("respUserCode.keyword", respUserCode));
        }
        SortBuilder<?> sort = SortBuilders.fieldSort("createTime").order(SortOrder.DESC);
        List<SortBuilder<?>> sorts = new ArrayList<>();
        sorts.add(sort);
        Page<DataSetMallApiLog> page = dataSetMallApiLogSvc.getSortListByQuery(pageNum, pageSize, query, sorts);
        return page;

    }
}
