package com.uinnova.product.eam.web.asset.bean;

import com.uinnova.product.vmdb.comm.model.ci.CcCi;
import lombok.Data;

import java.util.Map;

@Data
public class SubsystemVo extends CreateBaseVo {

    /**
     * 最新
     */
    private Boolean recent = false;

    /**
     * 最热
     */
    private Boolean mostClick = false;

    private String systemMasterCiId;

    private String ciCode;

    private Long ciId;

    private String searchData;

    private Integer pageSize;

    private Integer pageNum;

    private Long [] ciClassIds;

    /**
     * 子系统的ci信息
     */
    private CcCi subsystemCi;

    /**
     * 子系统的属性
     */
    private Map<String,String> subsystemAttrs;

    /**
     * 非功能信息ci
     */
    private CcCi noFunctionalCi;

    /**
     * 非功能信息的属性
     */
    private Map<String,String> noFunctionalAttrs;

    private Long newSystemMasterCiId;
}
