package com.uinnova.product.eam.model.dm;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.model.CiInfoExtend;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.comm.model.ci.CcCiClass;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import lombok.Data;

import java.util.List;

/**
 * 数据建模复制实体返回体
 * <AUTHOR>
 */
@Data
public class DataModelCopyInfo {
    @Comment("实体ci")
    private CiInfoExtend ciInfo;
    @Comment("实体属性ci")
    private List<CcCiInfo> attrList;
    @Comment("实体属性的属性定义")
    private List<CcCiAttrDef> attributeDefs;
    @Comment("实体属性所属分类")
    private CcCiClass attributeCiClass;

}
