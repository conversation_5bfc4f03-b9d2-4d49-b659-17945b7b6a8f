<?xml version="1.0" encoding="UTF-8"?><!--Converted at: Sat Dec 08 16:22:38 CST 2018-->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="VC_GROUP">


	<resultMap id="queryResult" type="com.uinnova.product.eam.comm.model.VcGroup">
		<result property="id" column="ID" jdbcType="BIGINT"/>	<!-- ID -->
		<result property="groupName" column="GROUP_NAME" jdbcType="VARCHAR"/>	<!-- 组名称 -->
		<result property="groupDesc" column="GROUP_DESC" jdbcType="VARCHAR"/>	<!-- 组描述 -->
		<result property="authRegion" column="AUTH_REGION" jdbcType="INTEGER"/>	<!-- 权限范围 -->
		<result property="groupImage" column="GROUP_IMAGE" jdbcType="VARCHAR"/>	<!-- 组图标 -->
		<result property="domainId" column="DOMAIN_ID" jdbcType="BIGINT"/>	<!-- 所属域 -->
		<result property="dataStatus" column="DATA_STATUS" jdbcType="INTEGER"/>	<!-- 数据状态 -->
		<result property="creator" column="CREATOR" jdbcType="VARCHAR"/>	<!-- 创建人 -->
		<result property="modifier" column="MODIFIER" jdbcType="VARCHAR"/>	<!-- 修改人 -->
		<result property="createTime" column="CREATE_TIME" jdbcType="BIGINT"/>	<!-- 创建时间 -->
		<result property="modifyTime" column="MODIFY_TIME" jdbcType="BIGINT"/>	<!-- 修改时间 -->
	</resultMap>
	

	<sql id="sql_query_where">
		<if test="cdt != null and cdt.id != null">and
			ID = #{cdt.id:BIGINT}
		</if>
		<if test="ids != null and ids != ''">and
			ID in (${ids})
		</if>
		<if test="cdt != null and cdt.startId != null">and
			 ID &gt;= #{cdt.startId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endId != null">and
			 ID &lt;= #{cdt.endId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.groupName != null and cdt.groupName != ''">and
			GROUP_NAME like #{cdt.groupName,jdbcType=VARCHAR} 
		</if>
		<if test="cdt != null and cdt.groupNameEqual != null and cdt.groupNameEqual != ''">and
			GROUP_NAME = #{cdt.groupNameEqual,jdbcType=VARCHAR}
		</if>
		<if test="groupNames != null and groupNames != ''">and
			GROUP_NAME in (${groupNames})
		</if>
		<if test="cdt != null and cdt.groupDesc != null and cdt.groupDesc != ''">and
			GROUP_DESC like #{cdt.groupDesc,jdbcType=VARCHAR} 
		</if>
		<if test="cdt != null and cdt.authRegion != null">and
			AUTH_REGION = #{cdt.authRegion:INTEGER}
		</if>
		<if test="authRegions != null and authRegions != ''">and
			AUTH_REGION in (${authRegions})
		</if>
		<if test="cdt != null and cdt.startAuthRegion != null">and
			 AUTH_REGION &gt;= #{cdt.startAuthRegion:INTEGER} 
		</if>
		<if test="cdt != null and cdt.endAuthRegion != null">and
			 AUTH_REGION &lt;= #{cdt.endAuthRegion:INTEGER} 
		</if>
		<if test="cdt != null and cdt.groupImage != null and cdt.groupImage != ''">and
			GROUP_IMAGE like #{cdt.groupImage,jdbcType=VARCHAR} 
		</if>
		<if test="cdt != null and cdt.groupImageEqual != null and cdt.groupImageEqual != ''">and
			GROUP_IMAGE = #{cdt.groupImageEqual,jdbcType=VARCHAR}
		</if>
		<if test="groupImages != null and groupImages != ''">and
			GROUP_IMAGE in (${groupImages})
		</if>
		<if test="cdt != null and cdt.domainId != null">and
			DOMAIN_ID = #{cdt.domainId:BIGINT}
		</if>
		<if test="domainIds != null and domainIds != ''">and
			DOMAIN_ID in (${domainIds})
		</if>
		<if test="cdt != null and cdt.startDomainId != null">and
			 DOMAIN_ID &gt;= #{cdt.startDomainId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endDomainId != null">and
			 DOMAIN_ID &lt;= #{cdt.endDomainId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.dataStatus != null">and
			DATA_STATUS = #{cdt.dataStatus:INTEGER}
		</if>
		<if test="dataStatuss != null and dataStatuss != ''">and
			DATA_STATUS in (${dataStatuss})
		</if>
		<if test="cdt != null and cdt.startDataStatus != null">and
			 DATA_STATUS &gt;= #{cdt.startDataStatus:INTEGER} 
		</if>
		<if test="cdt != null and cdt.endDataStatus != null">and
			 DATA_STATUS &lt;= #{cdt.endDataStatus:INTEGER} 
		</if>
		<if test="cdt != null and cdt.creator != null and cdt.creator != ''">and
			CREATOR like #{cdt.creator,jdbcType=VARCHAR} 
		</if>
		<if test="cdt != null and cdt.creatorEqual != null and cdt.creatorEqual != ''">and
			CREATOR = #{cdt.creatorEqual,jdbcType=VARCHAR}
		</if>
		<if test="creators != null and creators != ''">and
			CREATOR in (${creators})
		</if>
		<if test="cdt != null and cdt.modifier != null and cdt.modifier != ''">and
			MODIFIER like #{cdt.modifier,jdbcType=VARCHAR} 
		</if>
		<if test="cdt != null and cdt.modifierEqual != null and cdt.modifierEqual != ''">and
			MODIFIER = #{cdt.modifierEqual,jdbcType=VARCHAR}
		</if>
		<if test="modifiers != null and modifiers != ''">and
			MODIFIER in (${modifiers})
		</if>
		<if test="cdt != null and cdt.createTime != null">and
			CREATE_TIME = #{cdt.createTime:BIGINT}
		</if>
		<if test="createTimes != null and createTimes != ''">and
			CREATE_TIME in (${createTimes})
		</if>
		<if test="cdt != null and cdt.startCreateTime != null">and
			 CREATE_TIME &gt;= #{cdt.startCreateTime:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endCreateTime != null">and
			 CREATE_TIME &lt;= #{cdt.endCreateTime:BIGINT} 
		</if>
		<if test="cdt != null and cdt.modifyTime != null">and
			MODIFY_TIME = #{cdt.modifyTime:BIGINT}
		</if>
		<if test="modifyTimes != null and modifyTimes != ''">and
			MODIFY_TIME in (${modifyTimes})
		</if>
		<if test="cdt != null and cdt.startModifyTime != null">and
			 MODIFY_TIME &gt;= #{cdt.startModifyTime:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endModifyTime != null">and
			 MODIFY_TIME &lt;= #{cdt.endModifyTime:BIGINT} 
		</if>
	</sql>
	

	<sql id="sql_update_columns">
		<if test="record != null and record.id != null"> 
			ID = #{record.id:BIGINT}
		,</if>
		<if test="record != null and record.groupName != null"> 
			GROUP_NAME = #{record.groupName,jdbcType=VARCHAR}
		,</if>
		<if test="record != null and record.groupDesc != null"> 
			GROUP_DESC = #{record.groupDesc,jdbcType=VARCHAR}
		,</if>
		<if test="record != null and record.authRegion != null"> 
			AUTH_REGION = #{record.authRegion:INTEGER}
		,</if>
		<if test="record != null and record.groupImage != null"> 
			GROUP_IMAGE = #{record.groupImage,jdbcType=VARCHAR}
		,</if>
		<if test="record != null and record.domainId != null"> 
			DOMAIN_ID = #{record.domainId:BIGINT}
		,</if>
		<if test="record != null and record.dataStatus != null"> 
			DATA_STATUS = #{record.dataStatus:INTEGER}
		,</if>
		<if test="record != null and record.creator != null"> 
			CREATOR = #{record.creator,jdbcType=VARCHAR}
		,</if>
		<if test="record != null and record.modifier != null"> 
			MODIFIER = #{record.modifier,jdbcType=VARCHAR}
		,</if>
		<if test="record != null and record.createTime != null"> 
			CREATE_TIME = #{record.createTime:BIGINT}
		,</if>
		<if test="record != null and record.modifyTime != null"> 
			MODIFY_TIME = #{record.modifyTime:BIGINT}
		,</if>
	</sql>
	

	<sql id="sql_query_columns">
		ID, GROUP_NAME, GROUP_DESC, AUTH_REGION, GROUP_IMAGE, DOMAIN_ID, 
		DATA_STATUS, CREATOR, MODIFIER, CREATE_TIME, MODIFY_TIME
	</sql>
	

	

	<select id="selectList" parameterType="java.util.Map" resultMap="queryResult">
		select 
			<include refid="VC_GROUP.sql_query_columns"/>
		from VC_GROUP 
			<where>
				<include refid="VC_GROUP.sql_query_where"/>
			</where>
		order by 
			<if test="orders != null and orders != ''">
				${orders}
			</if>
			<if test="orders == null or orders == ''">
				ID
			</if>
	</select>
	<select id="selectCount" parameterType="java.util.Map" resultType="java.lang.Long">
		select count(1) from VC_GROUP 
			<where>
				<include refid="VC_GROUP.sql_query_where"/>
			</where>
	</select>
	<select id="selectById" parameterType="java.util.Map" resultMap="queryResult">
		select 
			<include refid="VC_GROUP.sql_query_columns"/>
		from VC_GROUP where ID=#{id:BIGINT} and DATA_STATUS=1  
	</select>
	

	

	<insert id="insert" parameterType="java.util.Map">
		insert into VC_GROUP(
			ID, GROUP_NAME, GROUP_DESC, AUTH_REGION, GROUP_IMAGE, 
			DOMAIN_ID, DATA_STATUS, CREATOR, MODIFIER, CREATE_TIME, 
			MODIFY_TIME)
		values (
			#{record.id:BIGINT}, #{record.groupName,jdbcType=VARCHAR}, #{record.groupDesc,jdbcType=VARCHAR}, #{record.authRegion:INTEGER}, #{record.groupImage,jdbcType=VARCHAR}, 
			#{record.domainId:BIGINT}, #{record.dataStatus:INTEGER}, #{record.creator,jdbcType=VARCHAR}, #{record.modifier,jdbcType=VARCHAR}, #{record.createTime:BIGINT}, 
			#{record.modifyTime:BIGINT})
	</insert>
	

	

	<update id="updateById" parameterType="java.util.Map">
		update VC_GROUP
			<set> 
				<include refid="VC_GROUP.sql_update_columns"/> 
			</set>
		where ID = #{id:BIGINT}
	</update>
	<update id="updateByCdt" parameterType="java.util.Map">
		update VC_GROUP
			<set> 
				<include refid="VC_GROUP.sql_update_columns"/> 
			</set>
			<where> 
				<include refid="VC_GROUP.sql_query_where"/> 
			</where>
	</update>
	
	

	

	<delete id="deleteById" parameterType="java.util.Map">
		delete from VC_GROUP where ID = #{id:BIGINT}
	</delete>
	<delete id="deleteByCdt" parameterType="java.util.Map">
		delete from VC_GROUP
			<where> 
				<include refid="VC_GROUP.sql_query_where"/> 
			</where>
	</delete>
	



</mapper>