package com.uino.api.client.sys.local;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.binary.core.util.BinaryUtils;
import com.binary.jdbc.Page;
import com.uino.service.sys.microservice.ILoginAuthConfigSvc;
import com.uino.bean.sys.base.LdapUserMapping;
import com.uino.bean.sys.base.LoginAuthConfig;
import com.uino.bean.sys.business.ActiveLoginAuthConfigDto;
import com.uino.bean.sys.business.LoginAuthConfigDto;
import com.uino.api.client.sys.ILoginAuthConfigApiSvc;

@Service
public class LoginAuthConfigApiSvcLocal implements ILoginAuthConfigApiSvc {

    @Autowired
    ILoginAuthConfigSvc loginAuthConfigSvc;

    @Override
    public LoginAuthConfig queryById(Long id) {
        return loginAuthConfigSvc.queryById(id);
    }

    @Override
    public Page<LoginAuthConfig> queryPage(LoginAuthConfigDto authConfigCdt) {
        return loginAuthConfigSvc.queryPage(authConfigCdt);
    }

    @Override
    public Long saveOrUpdate(LoginAuthConfig authConfig) {
        return loginAuthConfigSvc.saveOrUpdate(authConfig);
    }

    @Override
    public void activeConfig(ActiveLoginAuthConfigDto active) {
        BinaryUtils.checkEmpty(active, "cdt");
        loginAuthConfigSvc.activeConfig(active);
    }

    @Override
    public boolean testConnection(LoginAuthConfig authConfig) {
        return loginAuthConfigSvc.testConnection(authConfig);
    }

    @Override
    public LdapUserMapping testConnectionAndMappingUser(LoginAuthConfig authConfig) {
        return loginAuthConfigSvc.testConnectionAndMappingUser(authConfig);
    }

    @Override
    public void removeById(Long id) {
        loginAuthConfigSvc.removeById(id);
    }

}
