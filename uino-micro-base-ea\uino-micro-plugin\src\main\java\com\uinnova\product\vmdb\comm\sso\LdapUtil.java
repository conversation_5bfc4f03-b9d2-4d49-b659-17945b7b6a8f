package com.uinnova.product.vmdb.comm.sso;

import com.binary.core.util.BinaryUtils;
import com.binary.framework.exception.ServiceException;
import com.binary.json.JSON;
import com.uinnova.product.vmdb.comm.util.ApplicationProperties;
import org.apache.commons.codec.binary.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import javax.naming.AuthenticationException;
import javax.naming.Context;
import javax.naming.NamingEnumeration;
import javax.naming.NamingException;
import javax.naming.directory.Attribute;
import javax.naming.directory.Attributes;
import javax.naming.directory.SearchControls;
import javax.naming.directory.SearchResult;
import javax.naming.ldap.InitialLdapContext;
import javax.naming.ldap.LdapContext;
import javax.script.Invocable;
import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;
import java.util.*;

/**
 * <p>
 * ldaps 下需要项目自行初始化证书,可以使用%JAVA_HOME%/bin中的keytool.exe工具
 * 
 * <pre>
 * keytool -import -v -trustcacerts -alias uinoldap -file D:\\uinoldap.cer -keystore D:\\ssl-ldap-uino.jks
 * 回车输入一个自己记得的密码例如123456
 * </pre>
 * 
 * 然后在*.properties中添加 javax.net.ssl.trustStore=D:\\ssl-ldap-uino.jks
 * 和javax.net.ssl.keyStorePassword=123456
 * <p>
 * 
 * <AUTHOR>
 * @since 2020年12月30日上午10:29:35
 *
 */
public strictfp class LdapUtil implements AutoCloseable {

    private static Logger logger = LoggerFactory.getLogger(LdapUtil.class);

    private static Log DEF_LOG = new Log() {
    };

    /** 标识是否已经成功初始化jks信息 **/
    private static volatile boolean initJKSFile = false;

    private final static Long DEFAULT_CONNECTION_TIMEOUT = 10000L;

    private final static String CONN_SECURITY_AUTHENTICATION = "simple";

    /** 用户反复使用的Ldap配置 **/
    private static Map<Long, LdapUtil> cache = new HashMap<Long, LdapUtil>();

    private ScriptEngine engine = null;

    private Long id; // 连接唯一标识

    // ======================== connection 相关的配置 start ========================
    private String url;

    private String host;

    private String port;

    private String baseDN;

    /** 是否启用SSL **/
    private boolean useSSL;

    /** 暂时不支持 **/
    private boolean useTLS;

    /** 超时时间毫米 **/
    private Long timeout;
    // ======================== connection 相关的配置 end ========================
    // ======================== account 相关的配置 start =========================
    // === ldap 允许匿名登录如果有权限的话
    // ========================================================================

    /** 匿名登录,默认为true,当设置userDN后自动为false **/
    private boolean anonymousConnection = true;

    /** 用户的DN **/
    private String userDN;

    /** 用户对应的密码 **/
    private String userPwd;

    /**
     * 通过表达式生成用户完整的DN
     * <p>
     * 格式如: uid={username},cn=users,dc=uinnova,dc=com
     * <p>
     * {username}为固定写法,会被自动替换成用户输入的用户名
     **/
    private String userFullDNExpression;

    /**
     * 使用脚本的方式生成用户的DN
     * <p>
     * 脚本格式如下
     * 
     * <pre>
     * 
     * </pre>
     * 
     */
    private String userFullDNScript;
    // ======================== account 相关的配置 end ========================

    /** 属性映射规则 key为用户的字段名,value为ldap属性的字段值 **/
    private Map<String, String> attrMaps = null;

    private String attrMapScript = null;

    public static LdapUtil getLdapUtil(Long id) {
        return cache.get(id);
    }

    public static void clearCache() {
        cache.clear();
    }

    public static Builder newBuilder() {
        return new Builder();
    }

    public static TraceLog newTraceLog() {
        return new TraceLog();
    }

    /***
     * 初始化证书相关的信息
     * <p>
     * 
     */
    private synchronized static void initJKSFile() {
        if (initJKSFile) { return; }
        String filePath = ApplicationProperties.getProperty("javax.net.ssl.trustStore");
        String filePwd = ApplicationProperties.getProperty("javax.net.ssl.keyStorePassword");
        if (!BinaryUtils.isEmpty(filePath)) {
            System.setProperty("com.sun.jndi.ldap.object.disableEndpointIdentification", "true");
            System.setProperty("javax.net.ssl.trustStore", filePath);
            System.setProperty("javax.net.ssl.keyStorePassword", filePwd);
            initJKSFile = true;
        } else {
            logger.error(
                    "使用了SSL格式但是没有配置[javax.net.ssl.trustStore]和[javax.net.ssl.keyStorePassword]系统无法保证能正常连接到该ldap系统!");
        }
    }

    public LdapContext getLdapContext() throws AuthenticationException {
        Properties environment = buildConnPropertiesNoAccount();
        if (!anonymousConnection) {
            environment.put(Context.SECURITY_PRINCIPAL, userDN);
            environment.put(Context.SECURITY_CREDENTIALS, userPwd);
        }
        try {
            return new InitialLdapContext(environment, null);
        } catch (AuthenticationException e) {
            // 登录失败
            throw e;
        } catch (NamingException e) {
            if (logger.isDebugEnabled()) {
                logger.error("测试连接ldap异常", e);
            }
            throw new ServiceException("ldap connect err!");
        }
    }

    public LdapContext getLdapContext(String userFullDN, String password) throws AuthenticationException {
        Properties environment = buildConnPropertiesNoAccount();
        environment.put(Context.SECURITY_PRINCIPAL, userFullDN);
        environment.put(Context.SECURITY_CREDENTIALS, password);
        try {
            return new InitialLdapContext(environment, null);
        } catch (AuthenticationException e) {
            throw e;
        } catch (NamingException e) {
            if (logger.isDebugEnabled()) {
                logger.error("测试连接ldap异常", e);
            }
            throw new ServiceException(e);
        }
    }

    public boolean testConnection() {
        LdapContext ldapContext = null;
        try {
            ldapContext = getLdapContext();
            return true;
        } catch (AuthenticationException e) {
            if (logger.isDebugEnabled()) {
                logger.error("测试连接认证失败!", e);
            }
            return false;
        } catch (Exception e) {
            return false;
        } finally {
            if (ldapContext != null) {
                try {
                    ldapContext.close();
                } catch (NamingException e) {
                }
            }
        }
    }

    /**
     * 测试指定DN和密码,连接是否正常
     * 
     * @param userFullDN
     *            完整的DN路径
     * @param password
     *            未加密的密码
     * @return
     */
    public boolean testConnection(String userFullDN, String password) {
        LdapContext ldapContext = null;
        try {
            ldapContext = getLdapContext(userFullDN, password);
            return true;
        } catch (AuthenticationException e) {
            if (logger.isDebugEnabled()) {
                logger.error("测试连接认证失败!", e);
            }
            return false;
        } catch (Exception e) {
            return false;
        } finally {
            if (ldapContext != null) {
                try {
                    ldapContext.close();
                } catch (NamingException e) {
                }
            }
        }
    }

    public String getUserFullDN(String user) {
        return getUserFullDN(user, (Log) null);
    }

    public String getUserFullDN(String user, Log log) {
        return getUserFullDN(user, null, log);
    }

    public String getUserFullDN(String user, CharSequence password) {
        return getUserFullDN(user, password, null);
    }

    public String getUserFullDN(String user, CharSequence password, Log log) {
        if (BinaryUtils.isEmpty(userFullDNExpression) && BinaryUtils.isEmpty(userFullDNScript)) {
            throw new ServiceException("ldap未配置用户完整DN获取方法!");
        }
        String userDN = null;
        if (!BinaryUtils.isEmpty(userFullDNExpression)) {
            userDN = userFullDNExpression.replaceAll("\\{username\\}", user);
        } else {
            boolean customLog = log != null;
            log = customLog ? log : DEF_LOG;
            log.scope(0);
            LdapInvoker ldapInvoker = new LdapInvoker(this, log);
            String dnScript = "function run(log,ldap,username,baseDN) { \n" + userFullDNScript + "\n}";
            Object userDNObj = null;
            try {
                engine.eval(dnScript);
                Invocable inv = (Invocable) engine;
                userDNObj = inv.invokeFunction("run", log, ldapInvoker, user, baseDN);
            } catch (Exception e) {
                scriptErrLog(dnScript, e, customLog, log);
            } finally {
                try {
                    ldapInvoker.close();
                } catch (Exception e) {
                }
            }
            if (userDNObj != null) {
                userDN = userDNObj.toString();
            }
        }
        userDN = userDN == null ? "" : userDN;
        if (log != null) {
            log.info("user dn : " + userDN);
        }
        return userDN;
    }

    private void scriptErrLog(String script, Exception ex, boolean customLog, Log log) {
        if (ex == null) { return; }
        if (ex instanceof ScriptException) {
            ScriptException e = (ScriptException) ex;
            if (customLog) {
                StringBuffer sbuf = new StringBuffer();
                sbuf.append("script err: line [").append(e.getLineNumber()).append("]");
                if (e.getColumnNumber() >= 0) {
                    sbuf.append("column[").append(e.getColumnNumber()).append("]");
                }
                log.error(sbuf.toString());
                logger.error("\n获取用户DN脚本第{}行执行错误\n{}\n", e.getLineNumber(), script, e.getCause());
            } else {
                throw new ServiceException(ex.getMessage(), ex.getCause());
            }
        } else if (ex instanceof AuthenticationException
                || (ex.getCause() != null && ex.getCause() instanceof AuthenticationException)) {
            if (customLog) {
                log.error("ldap connect err! user dn or password error!");
            } else {
                throw new ServiceException(ex.getMessage(), ex.getCause());
            }
        } else {
            if (customLog) {
                log.error(ex.getMessage());
                logger.error("获取用户DN脚本执行错误[{}]", script, ex);
            } else {
                throw new ServiceException(ex.getMessage(), ex.getCause());
            }
        }
    }

    /**
     * 
     * @param userFullDN
     * @param password
     * @return
     * @throws AuthenticationException
     */
    public Map<String, String> getUserInfoMapping(String userFullDN, String password) {
        return getUserInfoMapping(userFullDN, password, null);
    }

    /**
     * 
     * @param userFullDN
     * @param password
     * @return
     * @throws AuthenticationException
     */
    public Map<String, String> getUserInfoMapping(String userFullDN, String password, Log log) {
        Map<String, String> result = null;
        boolean customLog = log != null;
        log = customLog ? log : DEF_LOG;
        log.scope(1);
        if (attrMaps != null) {
            result = getUserInfoMappingByAttrMap(userFullDN, password, log);
        } else if (!BinaryUtils.isEmpty(this.attrMapScript)) {
            result = new HashMap<String, String>();
            LdapInvoker ldapInvoker = new LdapInvoker(this, userFullDN, password, log);
            String dnScript = "function run(log,ldap,userDN,result) { " + this.attrMapScript + " }";
            try {
                engine.eval(dnScript);
                Invocable inv = (Invocable) engine;
                inv.invokeFunction("run", log, ldapInvoker, userDN, result);
            } catch (Exception e) {
                scriptErrLog(dnScript, e, customLog, log);
            } finally {
                try {
                    ldapInvoker.close();
                } catch (Exception e) {
                }
            }
        } else {
            if (customLog) {
                log.error("user attributes mapping undefined!");
            }
        }
        if (customLog) {
            log.info("user info : " + String.valueOf(result));
        }
        return result;
    }

    private Map<String, String> getUserInfoMappingByAttrMap(String userFullDN, String password, Log log) {
        boolean customLog = log != null && log != DEF_LOG ? true : false;
        // ========== 准备LDAP中搜索的属性
        Set<String> ldapAttrIds = new HashSet<String>(attrMaps.size());
        attrMaps.forEach((k, v) -> {
            if (!BinaryUtils.isEmpty(v)) {
                ldapAttrIds.add(v.trim());
            }
        });
        // 如果熟悉为空就是直接返回无映射
        if (ldapAttrIds.isEmpty()) {
            log.error("user attributes mapping undefined!");
            return null;
        }
        Map<String, String> result = new HashMap<String, String>();
        LdapContext userLdapCtx = null;
        try {
            userLdapCtx = getLdapContext(userFullDN, password);
            // key是ldap的属性ID,同时也是 this.attrMaps的value值.
            Map<String, String> ldapAttrMap = new HashMap<String, String>();
            //
            // ============= 进行属性搜索
            Attributes attributes = userLdapCtx.getAttributes(userFullDN,
                    ldapAttrIds.toArray(new String[ldapAttrIds.size()]));
            if (attributes != null) {
                NamingEnumeration<? extends Attribute> all = attributes.getAll();
                while (all.hasMoreElements()) {
                    Attribute attribute = (Attribute) all.nextElement();
                    ldapAttrMap.put(attribute.getID(), attribute.get().toString());
                }
            }
            //
            // =========== 将处理结果属性映射
            this.attrMaps.forEach((k, v) -> {
                result.put(k, ldapAttrMap.get(v));
            });
        } catch (Exception e) {
            scriptErrLog(null, e, customLog, log);
        } finally {
            if (userLdapCtx != null) {
                try {
                    userLdapCtx.close();
                } catch (NamingException e) {
                }
            }
        }
        return result;
    }

    private Properties buildConnPropertiesNoAccount() {
        Properties environment = new Properties();
        environment.put(Context.INITIAL_CONTEXT_FACTORY, "com.sun.jndi.ldap.LdapCtxFactory");
        environment.put(Context.PROVIDER_URL, url);
        environment.put("com.sun.jndi.ldap.connect.timeout", String.valueOf(timeout));
        environment.put(Context.SECURITY_AUTHENTICATION, CONN_SECURITY_AUTHENTICATION);
        environment.put("java.naming.ldap.attributes.binary", "objectSid objectGUID");
        if (useSSL) {
            environment.put(Context.SECURITY_PROTOCOL, "ssl");
        }
        return environment;
    }

    public Long getId() {
        return id;
    }

    public boolean isUseSSL() {
        return useSSL;
    }

    public boolean isUseTLS() {
        return useTLS;
    }

    @Override
    public void close() throws Exception {
    }

    /**
     * 使用流程: 配置connection->配置account->search方案->映射方案
     * 
     * <AUTHOR>
     * @since 2020年12月30日上午11:10:55
     *
     */
    public static class Builder {

        private LdapUtil ldapUtil;

        private boolean initConnection = false;

        public Builder() {
            ldapUtil = new LdapUtil();
        }

        public Builder connection(String host, String port, String baseDN) {
            return connection(host, port, baseDN, false, DEFAULT_CONNECTION_TIMEOUT);
        }

        public Builder connection(String host, String port, String baseDN, boolean useSSL, Long timeout) {
            if (timeout == null) {
                timeout = DEFAULT_CONNECTION_TIMEOUT;
            } else if (timeout < 300) {
                timeout = 300L;
                logger.info("ldap超时时间最少0.3秒!");
            }
            if (useSSL && !initJKSFile) {
                initJKSFile();
            }
            // ldap://***********:389/dc=uinnova,dc=com
            // ldaps://***********:883/dc=uinnova,dc=com
            StringBuffer urlBuild = new StringBuffer();
            urlBuild.append(useSSL ? "ldaps" : "ldap");
            urlBuild.append("://").append(host);
            if (!BinaryUtils.isEmpty(port)) {
                urlBuild.append(":").append(port);
            }
            // 不加baseDN好进行搜索.
            // if (!BinaryUtils.isEmpty(baseDN)) {
            // urlBuild.append("/").append(baseDN);
            // }
            ldapUtil.host = host;
            ldapUtil.port = port;
            ldapUtil.baseDN = baseDN;
            ldapUtil.timeout = timeout;
            ldapUtil.useSSL = initJKSFile & useSSL;
            ldapUtil.url = urlBuild.toString();
            initConnection = true;
            return this;
        }

        /**
         * 配置登录时使用的账号信息
         * 
         * @param userFullDN
         *            完整的用户DN信息
         * @param password
         * 
         * @return
         */
        public Builder account(String userFullDN, String password) {
            if (BinaryUtils.isEmpty(userFullDN)) {
                ldapUtil.anonymousConnection = true;
                return this;
            }
            ldapUtil.userDN = userFullDN.trim();
            Base64 base64 = new Base64();
            ldapUtil.userPwd = password == null ? "" : new String(base64.decode(password.trim()));
            ldapUtil.anonymousConnection = false;
            return this;
        }

        /**
         * 设置如何将用户名转换成用户完整DN的脚本
         * <p>
         * 这个方法和{@linkplain #userFullDNByExpression(String)} 只能生效一个
         * 
         * @param script
         *            {@link LdapUtil#userFullDNScript}
         * @return
         */
        public Builder userFullDNByScript(String script) {
            ScriptEngineManager factory = new ScriptEngineManager();
            ldapUtil.engine = factory.getEngineByName("nashorn");
            ldapUtil.userFullDNScript = script;
            ldapUtil.userFullDNExpression = null;
            return this;
        }

        /**
         * 设置如何将用户名转换成用户完整DN的表达式
         * <p>
         * 这个方法和{@linkplain #userFullDNByScript(String)} 只能生效一个
         * 
         * @param expression
         *            {@link LdapUtil#userFullDNExpression}
         * @return
         */
        public Builder userFullDNByExpression(String expression) {
            ldapUtil.userFullDNExpression = expression;
            ldapUtil.userFullDNScript = null;
            if(ldapUtil.attrMapScript == null) {
                ldapUtil.engine = null;
            }
            return this;
        }

        public Builder userInfoMapping(Map<String, String> userKeyMappingLdapAttrKey) {
            ldapUtil.attrMaps = userKeyMappingLdapAttrKey;
            ldapUtil.attrMapScript = null;
            return this;
        }

        public Builder userInfoMappingByScript(String mappingScript) {
            ldapUtil.attrMapScript = mappingScript;
            ldapUtil.attrMaps = null;
            if(ldapUtil.engine == null) {
                ScriptEngineManager factory = new ScriptEngineManager();
                ldapUtil.engine = factory.getEngineByName("nashorn");
            }
            return this;
        }

        /**
         * 构建一个临时使用的LdapUtil工具,需要手动关闭连接
         * 
         * @return
         */
        public LdapUtil builderTempLdapUtil() {
            return ldapUtil;
        }

        /**
         * 
         * @return
         */
        public LdapUtil builderToCache(Long id) {
            if (id == null) { throw new ServiceException("放入缓存时,必须要唯一ID"); }
            ldapUtil.id = id;
            LdapUtil oldLdapUtil = cache.get(id);
            try {
                if (oldLdapUtil != null) {
                    oldLdapUtil.close();
                }
            } catch (Exception e) {
                logger.info("关闭源ID为[{}]的配置失败![{}]", id, oldLdapUtil.toString());
            } finally {
                cache.put(id, ldapUtil);
            }
            return ldapUtil;
        }

        protected void validate() {
            if (!initConnection) { throw new ServiceException("链接配置信息为初始化!"); }
        }
    }

    public class LdapInvoker implements AutoCloseable {

        private LdapUtil ldapUtil;

        public String userFullDN;

        private String password;

        private LdapContext ldapCtx;

        private Log log;

        public LdapInvoker(LdapUtil ldapUtil, Log log) {
            super();
            this.ldapUtil = ldapUtil;
            this.log = log == null ? DEF_LOG : log;
        }

        public LdapInvoker(LdapUtil ldapUtil, String userFullDN, String password, Log log) {
            super();
            this.ldapUtil = ldapUtil;
            this.userFullDN = userFullDN;
            this.password = password;
            this.log = log == null ? DEF_LOG : log;
        }

        public LdapInvoker connect() throws AuthenticationException {
            if (userFullDN != null) {
                this.ldapCtx = ldapUtil.getLdapContext(userFullDN, password);
            } else {
                this.ldapCtx = ldapUtil.getLdapContext();
            }
            return this;
        }

        public List<String> searchDNList(String filter) {
            return searchDNList(null, filter);
        }

        public List<String> searchDNList(String fullDN, String filter) {
            return searchDNList(fullDN, filter, null);
        }

        public List<String> searchDNList(String fullDN, String filter, String searchScope) {
            if (BinaryUtils.isEmpty(fullDN)) {
                fullDN = ldapUtil.baseDN;
            }
            int ss = SearchControls.SUBTREE_SCOPE;
            if (!BinaryUtils.isEmpty(searchScope)) {
                searchScope = searchScope.trim().toUpperCase();
                if ("ONELEVEL_SCOPE".equals(searchScope)) {
                    ss = SearchControls.ONELEVEL_SCOPE;
                } else if ("OBJECT_SCOPE".equals(searchScope)) {
                    ss = SearchControls.OBJECT_SCOPE;
                }
            } else {
                searchScope = "SUBTREE_SCOPE";
            }
            log.info("searchDNList DN: [" + fullDN + "],filter: [" + filter + "],scope[" + searchScope + "]");
            SearchControls searchControls = new SearchControls();
            searchControls.setSearchScope(ss);
            searchControls.setReturningAttributes(new String[] {});
            try {
                List<String> result = new ArrayList<String>();
                NamingEnumeration<SearchResult> search = ldapCtx.search(fullDN, filter, searchControls);
                while (search.hasMoreElements()) {
                    String namespace = search.next().getNameInNamespace();
                    result.add(namespace);
                }
                return result;
            } catch (NamingException e) {
                log.error(e.getMessage());
            }
            return null;
        }

        public String searchDN(String filter) {
            return searchDN(null, filter);
        }

        public String searchDN(String fullDN, String filter) {
            return searchDN(fullDN, filter, null);
        }

        public String searchDN(String fullDN, String filter, String searchScope) {
            if (BinaryUtils.isEmpty(fullDN)) {
                fullDN = ldapUtil.baseDN;
            }
            int ss = SearchControls.SUBTREE_SCOPE;
            if (!BinaryUtils.isEmpty(searchScope)) {
                searchScope = searchScope.trim().toUpperCase();
                if ("ONELEVEL_SCOPE".equals(searchScope)) {
                    ss = SearchControls.ONELEVEL_SCOPE;
                } else if ("OBJECT_SCOPE".equals(searchScope)) {
                    ss = SearchControls.OBJECT_SCOPE;
                }
            } else {
                searchScope = "SUBTREE_SCOPE";
            }
            log.info("searchDN DN: [" + fullDN + "],filter: [" + filter + "],scope: [" + searchScope + "]");
            SearchControls searchControls = new SearchControls();
            searchControls.setSearchScope(ss);
            searchControls.setReturningAttributes(new String[] {});
            try {
                NamingEnumeration<SearchResult> search = ldapCtx.search(fullDN, filter, searchControls);
                while (search.hasMoreElements()) {
                    return search.next().getNameInNamespace();
                }
            } catch (NamingException e) {
                log.error(e.getMessage());
            }
            return null;
        }

        public Map<String, String> searchInfo() {
            return searchInfo((String[]) null);
        }

        public Map<String, String> searchInfo(String[] attrNames) {
            return searchInfo(userFullDN, attrNames);
        }

        public Map<String, String> searchInfo(String DN) {
            return searchInfo(DN, null);
        }

        public Map<String, String> searchInfo(String DN, String[] attrNames) {
            Map<String, String> result = new HashMap<String, String>();
            try {
                if (BinaryUtils.isEmpty(attrNames)) {
                    log.info("searchInfo DN: [" + DN + "],attrs: all ");
                } else {
                    log.info("searchInfo DN: [" + DN + "],attrs: [" + JSON.toString(attrNames) + "]");
                }
                Attributes attributes = BinaryUtils.isEmpty(attrNames) ? ldapCtx.getAttributes(DN)
                        : ldapCtx.getAttributes(DN, attrNames);
                if (attributes != null) {
                    NamingEnumeration<? extends Attribute> all = attributes.getAll();
                    while (all.hasMoreElements()) {
                        Attribute attribute = (Attribute) all.nextElement();
                        result.put(attribute.getID(), attribute.get().toString());
                    }
                }
            } catch (NamingException e) {
                log.error(e.getMessage());
            }
            return result;
        }

        @Override
        public void close() throws Exception {
            if (ldapCtx != null) {
                try {
                    ldapCtx.close();
                } catch (Exception e) {
                }
            }
        }
    }

    public static class LogInfo {

        // 1 info ,2 error
        private int level;

        private int scope;

        private String msg;

        public LogInfo(int level, int scope, String msg) {
            super();
            this.level = level;
            this.scope = scope;
            this.msg = msg;
        }

        public int getLevel() {
            return level;
        }

        public void setLevel(int level) {
            this.level = level;
        }

        public int getScope() {
            return scope;
        }

        public void setScope(int scope) {
            this.scope = scope;
        }

        public String getMsg() {
            return msg;
        }

        public void setMsg(String msg) {
            this.msg = msg;
        }
    }

    public static interface Log {

        default void info(Object obj) {
            if (obj != null && logger.isDebugEnabled()) {
                try {
                    logger.info(obj.toString());
                } catch (Exception e) {
                }
            }
        }

        default void error(Object obj) {
            if (obj != null && logger.isDebugEnabled()) {
                try {
                    logger.error(obj.toString());
                } catch (Exception e) {
                }
            }
        }

        default void scope(int scope) {
            // 什么也不管
        }

        default List<LogInfo> getLogs() {
            return null;
        }
    }

    public static class TraceLog implements Log {

        private List<LogInfo> logs = new ArrayList<LogInfo>();

        // -1空日志, 0当前写findUserNDLogs, 1当前写 mappingUserLogs
        private int scope = 0;

        @Override
        public void info(Object obj) {
            if (obj != null) {
                logs.add(new LogInfo(1, scope, String.valueOf(obj)));
            }
        }

        @Override
        public strictfp void error(Object obj) {
            if (obj != null) {
                logs.add(new LogInfo(2, scope, String.valueOf(obj)));
            }
        }

        @Override
        public strictfp void scope(int scope) {
            this.scope = scope;
        }

        @Override
        public List<LogInfo> getLogs() {
            return logs;
        }
    }
}
