package com.uinnova.product.vmdb.comm.model.rlt;

import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;

/**
 * 
 * <AUTHOR>
 *
 */
@Comment("CI关系表[CC_CI_RLT]")
public class CCcCiRlt implements Condition {
    private static final long serialVersionUID = 1L;

    @Comment("ID[ID] operate-Equal[=]")
    private Long id;

    @Comment("ID[ID] operate-In[in]")
    private Long[] ids;

    @Comment("ID[ID] operate-GTEqual[>=]")
    private Long startId;

    @Comment("ID[ID] operate-LTEqual[<=]")
    private Long endId;

    @Comment("CI代码[CI_CODE] operate-Like[like]")
    private String ciCode;

    @Comment("CI描述[CI_DESC] operate-Like[like]")
    private String ciDesc;

    @Comment("所属分类[CLASS_ID] operate-Equal[=]")
    private Long classId;

    @Comment("所属分类[CLASS_ID] operate-In[in]")
    private Long[] classIds;

    @Comment("所属分类[CLASS_ID] operate-GTEqual[>=]")
    private Long startClassId;

    @Comment("所属分类[CLASS_ID] operate-LTEqual[<=]")
    private Long endClassId;

    @Comment("关系级别[CI_RLT_LVL] operate-Equal[=]    关系级别:1=1级 2=2级 3=3级 ... n=n级")
    private Integer ciRltLvl;

    @Comment("关系级别[CI_RLT_LVL] operate-In[in]    关系级别:1=1级 2=2级 3=3级 ... n=n级")
    private Integer[] ciRltLvls;

    @Comment("关系级别[CI_RLT_LVL] operate-GTEqual[>=]    关系级别:1=1级 2=2级 3=3级 ... n=n级")
    private Integer startCiRltLvl;

    @Comment("关系级别[CI_RLT_LVL] operate-LTEqual[<=]    关系级别:1=1级 2=2级 3=3级 ... n=n级")
    private Integer endCiRltLvl;

    @Comment("来源[SOURCE_ID] operate-Equal[=]")
    private Long sourceId;

    @Comment("来源[SOURCE_ID] operate-In[in]")
    private Long[] sourceIds;

    @Comment("来源[SOURCE_ID] operate-GTEqual[>=]")
    private Long startSourceId;

    @Comment("来源[SOURCE_ID] operate-LTEqual[<=]")
    private Long endSourceId;

    private String ownerCodeEqual;

    private String[] ownerCodes;

    @Comment("所属组织[ORG_ID] operate-Equal[=]")
    private Long orgId;

    @Comment("所属组织[ORG_ID] operate-In[in]")
    private Long[] orgIds;

    @Comment("所属组织[ORG_ID] operate-GTEqual[>=]")
    private Long startOrgId;

    @Comment("所属组织[ORG_ID] operate-LTEqual[<=]")
    private Long endOrgId;

    @Comment("源CI_ID[SOURCE_CI_ID] operate-Equal[=]")
    private Long sourceCiId;

    @Comment("源CI_ID[SOURCE_CI_ID] operate-In[in]")
    private Long[] sourceCiIds;

    @Comment("源CI_ID[SOURCE_CI_ID] operate-GTEqual[>=]")
    private Long startSourceCiId;

    @Comment("源CI_ID[SOURCE_CI_ID] operate-LTEqual[<=]")
    private Long endSourceCiId;

    @Comment("源CI代码[SOURCE_CI_CODE] operate-Like[like]")
    private String sourceCiCode;

    @Comment("源CI分类[SOURCE_CLASS_ID] operate-Equal[=]")
    private Long sourceClassId;

    @Comment("源CI分类[SOURCE_CLASS_ID] operate-In[in]")
    private Long[] sourceClassIds;

    @Comment("源CI分类[SOURCE_CLASS_ID] operate-GTEqual[>=]")
    private Long startSourceClassId;

    @Comment("源CI分类[SOURCE_CLASS_ID] operate-LTEqual[<=]")
    private Long endSourceClassId;

    @Comment("目标CI_ID[TARGET_CI_ID] operate-Equal[=]")
    private Long targetCiId;

    @Comment("目标CI_ID[TARGET_CI_ID] operate-In[in]")
    private Long[] targetCiIds;

    @Comment("目标CI_ID[TARGET_CI_ID] operate-GTEqual[>=]")
    private Long startTargetCiId;

    @Comment("目标CI_ID[TARGET_CI_ID] operate-LTEqual[<=]")
    private Long endTargetCiId;

    @Comment("目标CI代码[TARGET_CI_CODE] operate-Like[like]")
    private String targetCiCode;

    @Comment("目标CI分类[TARGET_CLASS_ID] operate-Equal[=]")
    private Long targetClassId;

    @Comment("目标CI分类[TARGET_CLASS_ID] operate-In[in]")
    private Long[] targetClassIds;

    @Comment("目标CI分类[TARGET_CLASS_ID] operate-GTEqual[>=]")
    private Long startTargetClassId;

    @Comment("目标CI分类[TARGET_CLASS_ID] operate-LTEqual[<=]")
    private Long endTargetClassId;

    @Comment("备用_1[CUSTOM_1] operate-Like[like]")
    private String custom1;

    @Comment("备用_2[CUSTOM_2] operate-Like[like]")
    private String custom2;

    @Comment("备用_3[CUSTOM_3] operate-Like[like]")
    private String custom3;

    @Comment("备用_4[CUSTOM_4] operate-Like[like]")
    private String custom4;

    @Comment("备用_5[CUSTOM_5] operate-Like[like]")
    private String custom5;

    @Comment("备用_6[CUSTOM_6] operate-Like[like]")
    private String custom6;

    @Comment("CI关系属性[ATTRS_STR] operate-Like[like]")
    private String attrsStr;

    @Comment("CI关系属性[ATTRS_STR] operate-Equal[=]")
    private String attrsStrEqual;

    @Comment("CI关系属性[ATTRS_STR] operate-In[in]")
    private String[] attrsStrs;

    @Comment("所属域[DOMAIN_ID] operate-Equal[=]")
    private Long domainId;

    @Comment("所属域[DOMAIN_ID] operate-In[in]")
    private Long[] domainIds;

    @Comment("所属域[DOMAIN_ID] operate-GTEqual[>=]")
    private Long startDomainId;

    @Comment("所属域[DOMAIN_ID] operate-LTEqual[<=]")
    private Long endDomainId;

    @Comment("数据状态[DATA_STATUS] operate-Equal[=]    数据状态:1-正常 0-删除")
    private Integer dataStatus;

    @Comment("数据状态[DATA_STATUS] operate-In[in]    数据状态:1-正常 0-删除")
    private Integer[] dataStatuss;

    @Comment("数据状态[DATA_STATUS] operate-GTEqual[>=]    数据状态:1-正常 0-删除")
    private Integer startDataStatus;

    @Comment("数据状态[DATA_STATUS] operate-LTEqual[<=]    数据状态:1-正常 0-删除")
    private Integer endDataStatus;

    @Comment("创建人[CREATOR] operate-Like[like]")
    private String creator;

    @Comment("创建人[CREATOR] operate-Equal[=]")
    private String creatorEqual;

    @Comment("创建人[CREATOR] operate-In[in]")
    private String[] creators;

    @Comment("修改人[MODIFIER] operate-Like[like]")
    private String modifier;

    @Comment("修改人[MODIFIER] operate-Equal[=]")
    private String modifierEqual;

    @Comment("修改人[MODIFIER] operate-In[in]")
    private String[] modifiers;

    @Comment("创建时间[CREATE_TIME] operate-Equal[=]    创建时间:yyyyMMddHHmmss")
    private Long createTime;

    @Comment("创建时间[CREATE_TIME] operate-In[in]    创建时间:yyyyMMddHHmmss")
    private Long[] createTimes;

    @Comment("创建时间[CREATE_TIME] operate-GTEqual[>=]    创建时间:yyyyMMddHHmmss")
    private Long startCreateTime;

    @Comment("创建时间[CREATE_TIME] operate-LTEqual[<=]    创建时间:yyyyMMddHHmmss")
    private Long endCreateTime;

    @Comment("修改时间[MODIFY_TIME] operate-Equal[=]    修改时间:yyyyMMddHHmmss")
    private Long modifyTime;

    @Comment("修改时间[MODIFY_TIME] operate-In[in]    修改时间:yyyyMMddHHmmss")
    private Long[] modifyTimes;

    @Comment("修改时间[MODIFY_TIME] operate-GTEqual[>=]    修改时间:yyyyMMddHHmmss")
    private Long startModifyTime;

    @Comment("修改时间[MODIFY_TIME] operate-LTEqual[<=]    修改时间:yyyyMMddHHmmss")
    private Long endModifyTime;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long[] getIds() {
        return this.ids;
    }

    public void setIds(Long[] ids) {
        this.ids = ids;
    }

    public Long getStartId() {
        return this.startId;
    }

    public void setStartId(Long startId) {
        this.startId = startId;
    }

    public Long getEndId() {
        return this.endId;
    }

    public void setEndId(Long endId) {
        this.endId = endId;
    }

    public String getCiCode() {
        return this.ciCode;
    }

    public void setCiCode(String ciCode) {
        this.ciCode = ciCode;
    }

    public String getCiDesc() {
        return this.ciDesc;
    }

    public void setCiDesc(String ciDesc) {
        this.ciDesc = ciDesc;
    }

    public Long getClassId() {
        return this.classId;
    }

    public void setClassId(Long classId) {
        this.classId = classId;
    }

    public Long[] getClassIds() {
        return this.classIds;
    }

    public void setClassIds(Long[] classIds) {
        this.classIds = classIds;
    }

    public Long getStartClassId() {
        return this.startClassId;
    }

    public void setStartClassId(Long startClassId) {
        this.startClassId = startClassId;
    }

    public Long getEndClassId() {
        return this.endClassId;
    }

    public void setEndClassId(Long endClassId) {
        this.endClassId = endClassId;
    }

    public Integer getCiRltLvl() {
        return this.ciRltLvl;
    }

    public void setCiRltLvl(Integer ciRltLvl) {
        this.ciRltLvl = ciRltLvl;
    }

    public Integer[] getCiRltLvls() {
        return this.ciRltLvls;
    }

    public void setCiRltLvls(Integer[] ciRltLvls) {
        this.ciRltLvls = ciRltLvls;
    }

    public Integer getStartCiRltLvl() {
        return this.startCiRltLvl;
    }

    public void setStartCiRltLvl(Integer startCiRltLvl) {
        this.startCiRltLvl = startCiRltLvl;
    }

    public Integer getEndCiRltLvl() {
        return this.endCiRltLvl;
    }

    public void setEndCiRltLvl(Integer endCiRltLvl) {
        this.endCiRltLvl = endCiRltLvl;
    }

    public Long getSourceId() {
        return this.sourceId;
    }

    public void setSourceId(Long sourceId) {
        this.sourceId = sourceId;
    }

    public Long[] getSourceIds() {
        return this.sourceIds;
    }

    public void setSourceIds(Long[] sourceIds) {
        this.sourceIds = sourceIds;
    }

    public Long getStartSourceId() {
        return this.startSourceId;
    }

    public void setStartSourceId(Long startSourceId) {
        this.startSourceId = startSourceId;
    }

    public Long getEndSourceId() {
        return this.endSourceId;
    }

    public void setEndSourceId(Long endSourceId) {
        this.endSourceId = endSourceId;
    }

    public String getOwnerCodeEqual() {
        return ownerCodeEqual;
    }

    public void setOwnerCodeEqual(String ownerCodeEqual) {
        this.ownerCodeEqual = ownerCodeEqual;
    }

    public String[] getOwnerCodes() {
        return ownerCodes;
    }

    public void setOwnerCodes(String[] ownerCodes) {
        this.ownerCodes = ownerCodes;
    }

    public Long getOrgId() {
        return this.orgId;
    }

    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    public Long[] getOrgIds() {
        return this.orgIds;
    }

    public void setOrgIds(Long[] orgIds) {
        this.orgIds = orgIds;
    }

    public Long getStartOrgId() {
        return this.startOrgId;
    }

    public void setStartOrgId(Long startOrgId) {
        this.startOrgId = startOrgId;
    }

    public Long getEndOrgId() {
        return this.endOrgId;
    }

    public void setEndOrgId(Long endOrgId) {
        this.endOrgId = endOrgId;
    }

    public Long getSourceCiId() {
        return this.sourceCiId;
    }

    public void setSourceCiId(Long sourceCiId) {
        this.sourceCiId = sourceCiId;
    }

    public Long[] getSourceCiIds() {
        return this.sourceCiIds;
    }

    public void setSourceCiIds(Long[] sourceCiIds) {
        this.sourceCiIds = sourceCiIds;
    }

    public Long getStartSourceCiId() {
        return this.startSourceCiId;
    }

    public void setStartSourceCiId(Long startSourceCiId) {
        this.startSourceCiId = startSourceCiId;
    }

    public Long getEndSourceCiId() {
        return this.endSourceCiId;
    }

    public void setEndSourceCiId(Long endSourceCiId) {
        this.endSourceCiId = endSourceCiId;
    }

    public String getSourceCiCode() {
        return this.sourceCiCode;
    }

    public void setSourceCiCode(String sourceCiCode) {
        this.sourceCiCode = sourceCiCode;
    }

    public Long getSourceClassId() {
        return this.sourceClassId;
    }

    public void setSourceClassId(Long sourceClassId) {
        this.sourceClassId = sourceClassId;
    }

    public Long[] getSourceClassIds() {
        return this.sourceClassIds;
    }

    public void setSourceClassIds(Long[] sourceClassIds) {
        this.sourceClassIds = sourceClassIds;
    }

    public Long getStartSourceClassId() {
        return this.startSourceClassId;
    }

    public void setStartSourceClassId(Long startSourceClassId) {
        this.startSourceClassId = startSourceClassId;
    }

    public Long getEndSourceClassId() {
        return this.endSourceClassId;
    }

    public void setEndSourceClassId(Long endSourceClassId) {
        this.endSourceClassId = endSourceClassId;
    }

    public Long getTargetCiId() {
        return this.targetCiId;
    }

    public void setTargetCiId(Long targetCiId) {
        this.targetCiId = targetCiId;
    }

    public Long[] getTargetCiIds() {
        return this.targetCiIds;
    }

    public void setTargetCiIds(Long[] targetCiIds) {
        this.targetCiIds = targetCiIds;
    }

    public Long getStartTargetCiId() {
        return this.startTargetCiId;
    }

    public void setStartTargetCiId(Long startTargetCiId) {
        this.startTargetCiId = startTargetCiId;
    }

    public Long getEndTargetCiId() {
        return this.endTargetCiId;
    }

    public void setEndTargetCiId(Long endTargetCiId) {
        this.endTargetCiId = endTargetCiId;
    }

    public String getTargetCiCode() {
        return this.targetCiCode;
    }

    public void setTargetCiCode(String targetCiCode) {
        this.targetCiCode = targetCiCode;
    }

    public Long getTargetClassId() {
        return this.targetClassId;
    }

    public void setTargetClassId(Long targetClassId) {
        this.targetClassId = targetClassId;
    }

    public Long[] getTargetClassIds() {
        return this.targetClassIds;
    }

    public void setTargetClassIds(Long[] targetClassIds) {
        this.targetClassIds = targetClassIds;
    }

    public Long getStartTargetClassId() {
        return this.startTargetClassId;
    }

    public void setStartTargetClassId(Long startTargetClassId) {
        this.startTargetClassId = startTargetClassId;
    }

    public Long getEndTargetClassId() {
        return this.endTargetClassId;
    }

    public void setEndTargetClassId(Long endTargetClassId) {
        this.endTargetClassId = endTargetClassId;
    }

    public String getCustom1() {
        return this.custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }

    public String getCustom2() {
        return this.custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }

    public String getCustom3() {
        return this.custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }

    public String getCustom4() {
        return this.custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }

    public String getCustom5() {
        return this.custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }

    public String getCustom6() {
        return this.custom6;
    }

    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }

    public String getAttrsStr() {
        return attrsStr;
    }

    public void setAttrsStr(String attrsStr) {

        this.attrsStr = attrsStr;
    }

    public String getAttrsStrEqual() {
        return attrsStrEqual;
    }

    public void setAttrsStrEqual(String attrsStrEqual) {
        this.attrsStrEqual = attrsStrEqual;
    }

    public String[] getAttrsStrs() {
        return attrsStrs;
    }

    public void setAttrsStrs(String[] attrsStrs) {
        this.attrsStrs = attrsStrs;
    }

    public Long getDomainId() {
        return this.domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }

    public Long[] getDomainIds() {
        return this.domainIds;
    }

    public void setDomainIds(Long[] domainIds) {
        this.domainIds = domainIds;
    }

    public Long getStartDomainId() {
        return this.startDomainId;
    }

    public void setStartDomainId(Long startDomainId) {
        this.startDomainId = startDomainId;
    }

    public Long getEndDomainId() {
        return this.endDomainId;
    }

    public void setEndDomainId(Long endDomainId) {
        this.endDomainId = endDomainId;
    }

    public Integer getDataStatus() {
        return this.dataStatus;
    }

    public void setDataStatus(Integer dataStatus) {
        this.dataStatus = dataStatus;
    }

    public Integer[] getDataStatuss() {
        return this.dataStatuss;
    }

    public void setDataStatuss(Integer[] dataStatuss) {
        this.dataStatuss = dataStatuss;
    }

    public Integer getStartDataStatus() {
        return this.startDataStatus;
    }

    public void setStartDataStatus(Integer startDataStatus) {
        this.startDataStatus = startDataStatus;
    }

    public Integer getEndDataStatus() {
        return this.endDataStatus;
    }

    public void setEndDataStatus(Integer endDataStatus) {
        this.endDataStatus = endDataStatus;
    }

    public String getCreator() {
        return this.creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getCreatorEqual() {
        return this.creatorEqual;
    }

    public void setCreatorEqual(String creatorEqual) {
        this.creatorEqual = creatorEqual;
    }

    public String[] getCreators() {
        return this.creators;
    }

    public void setCreators(String[] creators) {
        this.creators = creators;
    }

    public String getModifier() {
        return this.modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public String getModifierEqual() {
        return this.modifierEqual;
    }

    public void setModifierEqual(String modifierEqual) {
        this.modifierEqual = modifierEqual;
    }

    public String[] getModifiers() {
        return this.modifiers;
    }

    public void setModifiers(String[] modifiers) {
        this.modifiers = modifiers;
    }

    public Long getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long[] getCreateTimes() {
        return this.createTimes;
    }

    public void setCreateTimes(Long[] createTimes) {
        this.createTimes = createTimes;
    }

    public Long getStartCreateTime() {
        return this.startCreateTime;
    }

    public void setStartCreateTime(Long startCreateTime) {
        this.startCreateTime = startCreateTime;
    }

    public Long getEndCreateTime() {
        return this.endCreateTime;
    }

    public void setEndCreateTime(Long endCreateTime) {
        this.endCreateTime = endCreateTime;
    }

    public Long getModifyTime() {
        return this.modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Long[] getModifyTimes() {
        return this.modifyTimes;
    }

    public void setModifyTimes(Long[] modifyTimes) {
        this.modifyTimes = modifyTimes;
    }

    public Long getStartModifyTime() {
        return this.startModifyTime;
    }

    public void setStartModifyTime(Long startModifyTime) {
        this.startModifyTime = startModifyTime;
    }

    public Long getEndModifyTime() {
        return this.endModifyTime;
    }

    public void setEndModifyTime(Long endModifyTime) {
        this.endModifyTime = endModifyTime;
    }

}
