package com.uino.dao.license;

import jakarta.annotation.PostConstruct;

import com.uino.bean.license.BaseLicenseAuth;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import com.binary.framework.exception.ServiceException;
import com.binary.framework.ibatis.IBatisUtils;
import com.uino.dao.AbstractESBaseDao;
import com.uino.dao.ESConst;
import com.uino.dao.util.ESUtil;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
public class ESLicenseAuthSvc extends AbstractESBaseDao<BaseLicenseAuth, BaseLicenseAuth> {

    /**
     * 授权许可证ID
     */
    public static final Long LICENSE_AUTH_ID = 1L;

    @Override
    public String getIndex() {
        return ESConst.INDEX_LICENSE_AUTH;
    }

    @Override
    public String getType() {
        return ESConst.INDEX_LICENSE_AUTH;
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }

    public BaseLicenseAuth selectOneRow() {
        BaseLicenseAuth record = super.getById(LICENSE_AUTH_ID);
        if (record == null) {
            try {
                record = new BaseLicenseAuth();
                record.setId(LICENSE_AUTH_ID);
                super.saveOrUpdate(record);
            } catch (Exception e) {
                record = super.getById(LICENSE_AUTH_ID);
                if (record == null) {
                    throw new ServiceException(" not found record! ");
                }
            }
        }
        return record;
    }

    @Override
    public Long saveOrUpdate(BaseLicenseAuth record) {
        selectOneRow();
        record.setId(LICENSE_AUTH_ID);
        return super.saveOrUpdate(record);
    }

    public int updateByAuthCode(Long id, String authCode, BaseLicenseAuth record) {
        selectOneRow();
        Assert.notNull(id, "BS_FIELD_EMPTY_VAL${field:id}");
        Assert.notNull(authCode, "BS_FIELD_EMPTY_VAL${field:authCode}");
        IBatisUtils.validateEntityEmpty(record);
        long time = ESUtil.getNumberDateTime();
        record.setModifyTime(time);
        record.setId(id);
        super.saveOrUpdate(record);
        return 1;
    }

}
