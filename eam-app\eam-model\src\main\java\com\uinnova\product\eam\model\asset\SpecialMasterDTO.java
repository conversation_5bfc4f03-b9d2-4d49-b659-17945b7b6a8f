package com.uinnova.product.eam.model.asset;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.base.model.CIDataInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;

@Data
public class SpecialMasterDTO implements Serializable {

    @Comment("标准规范主信息元素集合")
    private CIDataInfo special;

    @Comment("当前标准规范所属最新版本")
    private String editionNo = "0";

    @Comment("标准规范版本信息集合")
    private List<CIDataInfo> editionList = Collections.emptyList();

}
