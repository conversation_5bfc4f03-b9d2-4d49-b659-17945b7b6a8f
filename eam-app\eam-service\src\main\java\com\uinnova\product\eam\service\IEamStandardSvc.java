package com.uinnova.product.eam.service;

import com.binary.jdbc.Page;
import com.uinnova.product.eam.comm.bean.Standard;
import com.uinnova.product.eam.comm.bean.StandardDoc;
import com.uinnova.product.eam.model.StandardCdt;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;
import java.util.Set;

/**
 * 标准规范服务接口
 *
 * <AUTHOR>
 * @version 2020-9-23
 */
@Deprecated
public interface IEamStandardSvc {

    /**
     * 新增标准规范
     *
     * @param standard 标准规范信息
     * @param files 标准规范附件文档
     * @return 标准规范id
     */
    Long addStandard(Standard standard, MultipartFile[] files);

    /**
     * 删除标准规范文档
     *
     * @param standardId 标准规范id
     * @param standardDocId 标准规范文档id
     */
    void deleteStandardDoc(Long standardId, Long standardDocId);

    /**
     * 分页获取标准规范，按时间倒序
     *
     * @param standardsCdt 标准规范查询条件
     * @return 分页数据
     */
    Page<Standard> searchStandards(StandardCdt standardsCdt);

    /**
     * 根据id查询决议信息
     *
     * @param standardId 标准规范id
     * @return 决议信息
     */
    Standard getById(Long standardId);

    /**
     * 获取标准规范文档信息
     *
     * @param standardDocId 标准规范文档id
     * @return 决议文档信息
     */
    StandardDoc getStandardDocByStandardDocId(Long standardDocId);

    /**
     * 更新标准规范文档
     *
     * @param standardId 标准规范id
     * @param files 文件
     * @return 标准规范所有的文档信息，包括之前的和现在的
     */
    Set<Map<String, Object>> uploadStandardDoc(Long standardId, MultipartFile[] files);

    /**
     * 修改标准规范信息
     *
     * @param standard 标准规范
     */
    void editStandard(Standard standard);

    /**
     * 根据id删除决议文档
     *
     * @param id 决议文档id
     */
    void deleteStandardById(Long id);

    /**
     * 批量删除决议文档
     *
     * @param ids 多个决议文档id
     */
    void deleteStandardByIds(Long[] ids);

    /**
     * 根据id获取标准规范文档
     *
     * @param standardDocId 标准规范文档id
     * @return 标准规范文档
     */
    StandardDoc getStandardDocById(Long standardDocId);
}
