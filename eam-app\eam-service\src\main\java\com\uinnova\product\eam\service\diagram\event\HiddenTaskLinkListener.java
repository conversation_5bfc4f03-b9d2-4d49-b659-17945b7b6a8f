package com.uinnova.product.eam.service.diagram.event;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.binary.core.util.BinaryUtils;
import com.binary.jdbc.Page;
import com.uinnova.product.eam.base.diagram.model.ESDiagramLink;
import com.uinnova.product.eam.base.diagram.model.ESDiagramNode;
import com.uinnova.product.eam.db.diagram.es.ESDiagramLinkDao;
import com.uinnova.product.eam.db.diagram.es.ESDiagramNodeDao;
import com.uinnova.product.eam.model.diagram.event.DiagramRuleEnum;
import com.uinnova.product.eam.model.diagram.event.RuleParams;
import com.uinnova.product.eam.service.diagram.event.rule.IRuleLinkService;
import com.uino.bean.cmdb.base.ESCIRltInfo;
import com.uino.bean.cmdb.query.ESAttrBean;
import com.uino.bean.cmdb.query.ESRltSearchBean;
import com.uino.service.cmdb.microservice.impl.CIRltSvc;
import com.uino.service.cmdb.microservice.impl.CISvc;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class HiddenTaskLinkListener implements IHiddenEventListener{

    @Autowired
    private CISvc ciSvc;

    @Autowired
    private CIRltSvc ciRltSvc;

    @Autowired
    private ESDiagramNodeDao diagramNodeDao;

    @Autowired
    private ESDiagramLinkDao diagramLinkDao;

    @Autowired
    private List<IRuleLinkService> ruleLinkServices;

    @Autowired
    private DiagramConfigSvc diagramConfigSvc;

    @Override
    public DiagramRuleEnum getDiagramRule() {
        return DiagramRuleEnum.TASK_DIAGRAM;
    }

    @Override
    public void process(RuleParams params){
        if(BinaryUtils.isEmpty(params.getCiCode())){
            return;
        }
        String config = diagramConfigSvc.findConfigType(()->{
            ESAttrBean attrBean = new ESAttrBean();
            attrBean.setKey("CONF_TYPE");
            attrBean.setOptType(1);
            attrBean.setValue("BM_BELONG_ZJ");
            return Collections.singletonList(attrBean);
        });
        if(!BinaryUtils.isEmpty(config)){
            params.setConfig(JSON.parseObject(config));
        }
        boolean isReturn = validCiCode(params);
        if(isReturn){
            return;
        }
        clearHiddenLink(params);
        for (IRuleLinkService ruleLinkService : ruleLinkServices) {
            ruleLinkService.process(params);
        }
    }

    private boolean validCiCode(RuleParams params) {
        BoolQueryBuilder nodeQuery = QueryBuilders.boolQuery();
        nodeQuery.must(QueryBuilders.termQuery("diagramId", params.getDiagramId()));
        nodeQuery.must(QueryBuilders.termQuery("sheetId.keyword", params.getSheetId()));
        nodeQuery.must(QueryBuilders.termQuery("domainId", params.getDomainId()));
        nodeQuery.must(QueryBuilders.termQuery("ciCode.keyword", params.getCiCode()));
        ESDiagramNode esDiagramNode = diagramNodeDao.selectOne(nodeQuery);
        JSONObject nodeJson = JSONObject.parseObject(esDiagramNode.getNodeJson());
        String className = nodeJson.getString("className");
        if(BinaryUtils.isEmpty(className)){
            return true;
        }
        String makeHiddenLinkClass = params.getConfig().getString("makeHiddenLinkClass");
        if(!makeHiddenLinkClass.equals(className)){
            return true;
        }
        return false;
    }

    public void clearHiddenLink(RuleParams params){
        if(params.getRuleType() == null){
            return;
        }
        BoolQueryBuilder linkQuery = QueryBuilders.boolQuery();
        linkQuery.must(QueryBuilders.termQuery("diagramId", params.getDiagramId()));
        linkQuery.must(QueryBuilders.termQuery("sheetId.keyword", params.getSheetId()));
        linkQuery.must(QueryBuilders.termQuery("visible",0));
        linkQuery.must(QueryBuilders.termQuery("domainId",params.getDomainId()));
        if(!BinaryUtils.isEmpty(params.getCiCode())){
            linkQuery.must(QueryBuilders.matchPhraseQuery("uniqueCode", params.getCiCode()));
        }
        List<ESDiagramLink> esDiagramLinks = diagramLinkDao.selectListByQuery(1, 1000, linkQuery);
        if(BinaryUtils.isEmpty(esDiagramLinks)){
            return;
        }
        Set<String> uniqueCodes = esDiagramLinks.stream()
                .filter(each -> !BinaryUtils.isEmpty(each.getUniqueCode()))
                .map(ESDiagramLink::getUniqueCode)
                .collect(Collectors.toSet());
        if(BinaryUtils.isEmpty(uniqueCodes)){
            return;
        }

        ESRltSearchBean bean = new ESRltSearchBean();
        bean.setPageNum(1);
        bean.setPageSize(uniqueCodes.size());
        bean.setOwnerCode(params.getOwnerCode());
        bean.setRltUniqueCodes(uniqueCodes);
        bean.setDomainId(params.getDomainId());
        Page<ESCIRltInfo> rltPage = ciRltSvc.searchRlt(bean);
        if(!BinaryUtils.isEmpty(rltPage.getData())){
            Set<String> delCodes = rltPage.getData().stream().map(ESCIRltInfo::getUniqueCode).collect(Collectors.toSet());
            ciRltSvc.delRltByIdsOrRltCodes(Collections.emptyList(), delCodes, params.getOwnerCode());
        }
        diagramLinkDao.deleteByQuery(linkQuery, true);
    }

}
