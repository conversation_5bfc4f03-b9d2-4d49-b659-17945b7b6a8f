package com.uino.util.cache.config;

import com.uino.util.lock.condition.EnableRedisCondition;
import lombok.extern.slf4j.Slf4j;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;

@Slf4j
@Configuration
@Conditional(EnableRedisCondition.class)
public class RedissonConfiguration {

    //支持single、master、sentinel、cluster模式，默认single
    private String type;

    //master主从模式需要设置master地址
    @Value("${redis.masterAddress:redis://127.0.0.1:6379}")
    private String masterAddress;

    //哨兵模式需要设置masterName
    @Value("${spring.data.redis.sentinel.master:quickea}")
    private String masterName;

    //redis地址，多个地址使用英文状态逗号","进行分隔
//    @Value("${redis.address:redis://127.0.0.1:6379}")
    @Value("${spring.data.redis.host:127.0.0.1}")
    private String address;

    @Value("${spring.data.redis.port:6379}")
    private String port;

    //redis密码
    @Value("${spring.data.redis.password: }")
    private String password;

    //数据库编号
    @Value("${spring.data.redis.database: 0}")
    private int database;

    @Value("${spring.data.redis.cluster.nodes: }")
    private String clusterAddress;

    @Value("${spring.data.redis.sentinel.nodes: }")
    private String sentinelAddress;
    /**
     * 初始化redisson
     *
     * @return
     */
    @Bean
    public RedissonClient redisson() {
        //支持类型根据是否配置spring集群来判断,先改造集群和单点
        // TODO 增加哨兵模式判断，待优化
        if(clusterAddress.trim().isEmpty() && sentinelAddress.trim().isEmpty()){
            type=RedisTypeCons.SINGLE;
        }else if(clusterAddress.trim().isEmpty()){
            type=RedisTypeCons.SENTINEL;
        }else {
            type=RedisTypeCons.CLUSTER;
        }
        log.info("RedissonClient, type : " + type);
        try {
            Config config = new Config();
            if (password.trim().isEmpty()) {
                type(config);
            } else {
                typePassWord(config);
            }
            log.info("Create redissonClient.");
            return Redisson.create(config);
        } catch (Exception e) {
            log.error("create redissonClient fail: " + e.getMessage());
            return null;
        }
    }

    /**
     * 使用密码创建redisson连接
     *
     * @param config
     */
    private void typePassWord(Config config) {
        String[] addressUrl;
        if(type.equals(RedisTypeCons.SINGLE)){
            addressUrl = new String[]{"redis://"+address+":"+port};
        }else if(type.equals(RedisTypeCons.SENTINEL)){
            addressUrl = sentinelAddress.split(",");
            for (int i = 0; i < addressUrl.length; i++) {
                addressUrl[i] = "redis://" + addressUrl[i];
            }
        }else{
            addressUrl = clusterAddress.split(",");
            for (int i = 0; i < addressUrl.length; i++) {
                addressUrl[i] = "redis://" + addressUrl[i];
            }
        }
        switch (type) {
            case RedisTypeCons.MASTER:
                config.useMasterSlaveServers()
                        .setMasterAddress(masterAddress)
                        .addSlaveAddress(addressUrl)
                        .setDatabase(database)
                        .setPassword(password);
                break;
            case RedisTypeCons.SENTINEL:
                config.useSentinelServers()
                        .setMasterName(masterName)
                        .addSentinelAddress(addressUrl)
                        //.setDatabase(database)
                        .setPassword(password);
                break;
            case RedisTypeCons.CLUSTER:
                config.useClusterServers()
                        .addNodeAddress(addressUrl)
                        .setPassword(password);
                break;
            default:
                config.useSingleServer()
                        .setAddress("redis://"+address+":"+port)
                        .setDatabase(database)
                        .setPassword(password);
        }
    }

    /**
     * 无密码创建redisson连接
     *
     * @param config
     */
    private void type(Config config) {
        String[] addressUrl;
        if(type.equals(RedisTypeCons.SINGLE)){
            addressUrl = new String[]{"redis://"+address+":"+port};
        }else if(type.equals(RedisTypeCons.SENTINEL)){
            addressUrl = sentinelAddress.split(",");
            for (int i = 0; i < addressUrl.length; i++) {
                addressUrl[i] = "redis://" + addressUrl[i];
            }
        }else{
            addressUrl = clusterAddress.split(",");
            for (int i = 0; i < addressUrl.length; i++) {
                addressUrl[i] = "redis://" + addressUrl[i];
            }
        }
        switch (type) {
            case RedisTypeCons.MASTER:
                config.useMasterSlaveServers()
                        .setMasterAddress(masterAddress)
                        .setDatabase(database)
                        .addSlaveAddress(addressUrl);
                break;
            case RedisTypeCons.SENTINEL:
                config.useSentinelServers()
                        .setMasterName(masterName)
                        //.setDatabase(database)
                        .addSentinelAddress(addressUrl);
                break;
            case RedisTypeCons.CLUSTER:
                config.useClusterServers()
                        .addNodeAddress(addressUrl);
                break;
            default:
                config.useSingleServer()
                        .setDatabase(database)
                        .setAddress("redis://"+address+":"+port);
        }
    }

}
