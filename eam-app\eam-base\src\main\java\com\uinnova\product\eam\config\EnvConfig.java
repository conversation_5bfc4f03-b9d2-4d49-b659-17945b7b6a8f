package com.uinnova.product.eam.config;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

import java.util.List;

@Data
@Component
@ConfigurationProperties(prefix = "uinnova")
@PropertySource(value={"classpath:asset.yml"}, encoding = "utf-8", factory = ResourceFactory.class)
public class EnvConfig {

    private App app;

    private Dict dict;

    private Asset asset;

    private DataModel dataModel;

    @Data
    public static class Dict{
        private String className;
    }

    @Data
    public static class App{
        private String relClassName;
        private String className;
        private String bizClassName;
    }
    @Data
    public static class Asset {

        private Specification specification;

        private AppSystem appSystem;

        private AppSubSystem appSubSystem;

        private ArchResolution archResolution;

        private FreeSoftware freeSoftware;

        private PaySoftware paySoftware;

        private NoFunctionalItem noFunctionalItem;

        private NoFunctionalMsg noFunctionalMsg;

        private HtAppSystem htAppSystem;

        @Data
        public static class Specification {

            private String className;

            private String pageConfig;

            private Edition edition;

            @Data
            public static class Edition {
                private String className;
            }
        }
        @Data
        public static class AppSystem {
            private String className;
        }
        @Data
        public static class AppSubSystem {
            private String className;
        }
        @Data
        public static class ArchResolution {
            private String className;
            private String pageConfig;
            private String DOC;
            private String subsystemCode;
            private String ciCode;
            private String id;
            private String ResolutionName;
            private String ResolutionTime;
            private String ReviewMethod;
            private Long domainId;
        }
        @Data
        public static class FreeSoftware {
            private String className;
            private String pageConfig;
            private Edition edition;
            @Data
            public static class Edition {
                private String className;
            }
        }
        @Data
        public static class PaySoftware {
            private String className;
            private String pageConfig;
            private Edition edition;
            @Data
            public static class Edition {
                private String className;
            }
        }
        @Data
        public static class NoFunctionalItem {
            private String className;
            private String pageConfig;
            private String searchAttrs;
            private String indicatorName;
            private String indicatorClassName;
            private String subdivisionName;
            private String iamEmpLoginNo;
            private String lastEditTime;
            private String dataDictionary;
            private Long domainId;
        }
        @Data
        public static class NoFunctionalMsg {
            private String className;
        }
        @Data
        public static class HtAppSystem {
            private String classCode;
            private List<String> queryConditions;
            private String appSystemConfiguration;
            private String initAppSystemConfig;
        }
    }
    @Data
    @Comment("数据建模相关分类")
    public static class DataModel {
        @Comment("概念实体")
        private ConceptionEntity conceptionEntity;
        @Comment("逻辑实体")
        private LogicEntity logicEntity;
        @Comment("系统级逻辑实体")
        private SysLogicEntity sysLogicEntity;
        @Comment("实体属性")
        private Attributes attributes;
        @Comment("数据类型")
        private DataType dataType;
        @Comment("数据标准")
        private Standard standard;
        @Comment("数据标准分类")
        private StandardClass standardClass;
        @Comment("物理模型")
        private PhysicalEntity physicalEntity;
        @Comment("域组")
        private Domain domain;
        @Comment("值域")
        private DomainClass domainClass;

        @Data
        public static class ConceptionEntity {
            private String name;
            private String code;
        }
        @Data
        public static class LogicEntity {
            private String name;
            private String code;
        }
        @Data
        public static class SysLogicEntity {
            private String name;
            private String code;
        }
        @Data
        public static class Attributes {
            private String name;
            private String code;
        }
        @Data
        public static class DataType {
            private String name;
            private String code;
        }
        @Data
        public static class Standard {
            private String name;
            private String code;
        }
        @Data
        public static class StandardClass {
            private String name;
            private String code;
        }

        @Data
        public static class PhysicalEntity {
            private String name;
            private String code;
        }

        @Data
        public static class DomainClass {
            private String name;
            private String code;
        }

        @Data
        public static class Domain {
            private String name;
            private String code;
        }
    }
}
