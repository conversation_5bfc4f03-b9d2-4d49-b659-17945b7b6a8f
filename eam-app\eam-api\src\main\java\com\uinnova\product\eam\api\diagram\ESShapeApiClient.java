package com.uinnova.product.eam.api.diagram;

import com.binary.framework.web.RemoteResult;
import com.binary.jdbc.Page;
import com.uinnova.product.eam.base.diagram.model.*;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClassDir;
import com.uinnova.product.vmdb.comm.model.image.CcImage;
import com.uino.bean.cmdb.business.ImageCount;
import com.uino.bean.cmdb.business.ImportDirMessage;
import com.uino.bean.cmdb.business.ImportResultMessage;
import com.uino.bean.cmdb.query.ESSearchImageBean;
import com.uino.bean.permission.query.SearchKeywordBean;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Set;

public interface ESShapeApiClient {

    /**
     * <AUTHOR>
     * @Description
     * @Date 15:26 2021/7/13
     * @Param [thumbnailBatch]
     * @return void
     **/
    Long saveOrUpdateDir(VcShapeDir dir);

    List<VcShapeDirInfo> queryImageDirList();

    List<ESUserShape> queryImagePage(Integer pageNum, Integer pageSize, Long dirId, String keyword);

    RemoteResult saveOrUpdateShape(ESUserShapeDTO esUserShapeDTO);

    ImportResultMessage importZipImage(Integer sourceType, MultipartFile file);

    List<ImageCount> queryImageDirList(CCcCiClassDir cdt);

    Page<ESUserShape> queryImagePage(ESImageBean bean);

    boolean replaceImage(Long imageId, MultipartFile file);

    boolean deleteImage(CcImage image);

    long deleteDirImage(Long dirId);

    boolean importImage(Long dirId, MultipartFile file);

    ImportDirMessage importImages(Long dirId, MultipartFile... files);

    ResponseEntity<byte[]> exportImageZipByDirIds(Set<Long> dirIds);

    List<CcImage> queryTopImage(SearchKeywordBean bean);

    ImportDirMessage import3DZipImage(MultipartFile file, Long dirId, boolean isCover);

    Long updateImageRlt(ESUserShape image);

    boolean replace3DImage(Long imgId, MultipartFile file);

    boolean delete3DImage(CcImage image);

    CcImage queryImageById(Long id);

    Long countBySearchBean(ESSearchImageBean bean);

    ResponseEntity<byte[]> downloadImageResource(List<Long> ids);

    Long saveOrUpdate(ESShapeDir dir);

    boolean sort(ESUserShapeQuery cdt);

    List<ESUserShapeResult> queryAllImage(ESImageBean bean);
}
