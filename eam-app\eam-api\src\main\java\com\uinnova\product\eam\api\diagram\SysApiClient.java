package com.uinnova.product.eam.api.diagram;

import cn.hutool.json.JSONObject;
import com.binary.jdbc.Page;
import com.uinnova.product.eam.base.diagram.model.ESUploadManage;
import com.uinnova.product.eam.base.diagram.model.NewUserRecord;
import com.uinnova.product.vmdb.comm.model.image.CcImage;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface SysApiClient {
    String uploadFile(MultipartFile file);

    List<NewUserRecord> getIsOlderUser(Long userId);

    Long setIsOlderUser(Long userId, JSONObject Object);

    ESUploadManage upload(MultipartFile file);

    Page<ESUploadManage> queryData(int pageNum, int pageSize, String orders);

    Long deleteImage(CcImage image);
}
