package com.uinnova.product.eam.init;

import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * 微服务化部署扫描类
 * <AUTHOR>
 */
@ComponentScan(basePackages = { "com.uinnova.product.eam.feign.client",
        "com.uinnova.product.eam.api",
        "com.uinnova.product.eam.rpc" })
@Configuration
@EnableFeignClients(basePackages = { "com.uinnova.product.eam.feign.client"})
@ConditionalOnProperty(prefix = "eam", name = "load-type", havingValue = "rpc")
public class EamRpcRunConfig {
    {
        LoggerFactory.getLogger(getClass()).info("发现spring-boot配置为rpc加载");
    }
}
