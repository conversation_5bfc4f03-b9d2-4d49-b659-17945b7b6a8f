<?xml version="1.0" encoding="UTF-8"?><!--Converted at: Sat Dec 08 16:22:37 CST 2018-->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="VC_DIAGRAM">


	<resultMap id="queryResult" type="com.uinnova.product.eam.comm.model.VcDiagram">
		<result property="id" column="ID" jdbcType="BIGINT"/>	<!-- ID -->
		<result property="name" column="NAME" jdbcType="VARCHAR"/>	<!-- 视图名称 -->
		<result property="userId" column="USER_ID" jdbcType="BIGINT"/>	<!-- 所属用户 -->
		<result property="dirId" column="DIR_ID" jdbcType="BIGINT"/>	<!-- 所属目录 -->
		<result property="subjectId" column="SUBJECT_ID" jdbcType="BIGINT"/>	<!-- 主题目录 -->
		<result property="dirType" column="DIR_TYPE" jdbcType="INTEGER"/>	<!-- 目录类型 -->
		<result property="diagramType" column="DIAGRAM_TYPE" jdbcType="INTEGER"/>	<!-- 视图类型 -->
		<result property="diagramDesc" column="DIAGRAM_DESC" jdbcType="VARCHAR"/>	<!-- 视图描述 -->
		<result property="diagramSvg" column="DIAGRAM_SVG" jdbcType="VARCHAR"/>	<!-- 视图SVG -->
		<result property="diagramXml" column="DIAGRAM_XML" jdbcType="VARCHAR"/>	<!-- 视图XML -->
		<result property="diagramJson" column="DIAGRAM_JSON" jdbcType="VARCHAR"/>	<!-- 视图JSON -->
		<result property="diagramBgImg" column="DIAGRAM_BG_IMG" jdbcType="VARCHAR"/>	<!-- 背景图 -->
		<result property="diagramBgCss" column="DIAGRAM_BG_CSS" jdbcType="VARCHAR"/>	<!-- 背景样式 -->
		<result property="icon1" column="ICON_1" jdbcType="VARCHAR"/>	<!-- 视图图标_1 -->
		<result property="icon2" column="ICON_2" jdbcType="VARCHAR"/>	<!-- 视图图标_2 -->
		<result property="icon3" column="ICON_3" jdbcType="VARCHAR"/>	<!-- 视图图标_3 -->
		<result property="icon4" column="ICON_4" jdbcType="VARCHAR"/>	<!-- 视图图标_4 -->
		<result property="icon5" column="ICON_5" jdbcType="VARCHAR"/>	<!-- 视图图标_5 -->
		<result property="isOpen" column="IS_OPEN" jdbcType="INTEGER"/>	<!-- 是否公开 -->
		<result property="openTime" column="OPEN_TIME" jdbcType="BIGINT"/>	<!-- 公开时间 -->
		<result property="dataUpType" column="DATA_UP_TYPE" jdbcType="INTEGER"/>	<!-- 数据驱动类型 -->
		<result property="status" column="STATUS" jdbcType="INTEGER"/>	<!-- 视图状态 -->
		<result property="ci3dPoint" column="CI_3D_POINT" jdbcType="VARCHAR"/>	<!-- CI3D坐标 -->
		<result property="ci3dPoint2" column="CI_3D_POINT2" jdbcType="VARCHAR"/>	<!-- CI3D坐标2 -->
		<result property="searchField" column="SEARCH_FIELD" jdbcType="VARCHAR"/>	<!-- 搜索字段 -->
		<result property="combRows" column="COMB_ROWS" jdbcType="INTEGER"/>	<!-- 组合视图行数 -->
		<result property="combCols" column="COMB_COLS" jdbcType="INTEGER"/>	<!-- 组合视图列数 -->
		<result property="readCount" column="READ_COUNT" jdbcType="BIGINT"/>	<!-- 查看次数 -->
		<result property="appRltCiCode" column="APP_RLT_CI_CODE" jdbcType="VARCHAR"/>	<!-- 应用关联CI -->
		<result property="referVersionId" column="REFER_VERSION_ID" jdbcType="BIGINT"/>	<!-- 参照版本id -->
		<result property="domainId" column="DOMAIN_ID" jdbcType="BIGINT"/>	<!-- 所属域 -->
		<result property="dataStatus" column="DATA_STATUS" jdbcType="INTEGER"/>	<!-- 数据状态 -->
		<result property="creator" column="CREATOR" jdbcType="VARCHAR"/>	<!-- 创建人 -->
		<result property="modifier" column="MODIFIER" jdbcType="VARCHAR"/>	<!-- 修改人 -->
		<result property="createTime" column="CREATE_TIME" jdbcType="BIGINT"/>	<!-- 创建时间 -->
		<result property="modifyTime" column="MODIFY_TIME" jdbcType="BIGINT"/>	<!-- 更新时间 -->
	</resultMap>
	

	<sql id="sql_query_where">
		<if test="cdt != null and cdt.id != null">and
			ID = #{cdt.id:BIGINT}
		</if>
		<if test="ids != null and ids != ''">and
			ID in (${ids})
		</if>
		<if test="cdt != null and cdt.startId != null">and
			 ID &gt;= #{cdt.startId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endId != null">and
			 ID &lt;= #{cdt.endId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.name != null and cdt.name != ''">and
			NAME like #{cdt.name,jdbcType=VARCHAR} 
		</if>
		<if test="cdt != null and cdt.nameEqual != null and cdt.nameEqual != ''">and
			NAME = #{cdt.nameEqual,jdbcType=VARCHAR}
		</if>
		<if test="names != null and names != ''">and
			NAME in (${names})
		</if>
		<if test="cdt != null and cdt.userId != null">and
			USER_ID = #{cdt.userId:BIGINT}
		</if>
		<if test="userIds != null and userIds != ''">and
			USER_ID in (${userIds})
		</if>
		<if test="cdt != null and cdt.startUserId != null">and
			 USER_ID &gt;= #{cdt.startUserId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endUserId != null">and
			 USER_ID &lt;= #{cdt.endUserId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.dirId != null">and
			DIR_ID = #{cdt.dirId:BIGINT}
		</if>
		<if test="cdt != null and cdt.subjectId != null">and
			SUBJECT_ID = #{cdt.subjectId:BIGINT}
		</if>
		<if test="dirIds != null and dirIds != ''">and
			DIR_ID in (${dirIds})
		</if>
		<if test="cdt != null and cdt.startDirId != null">and
			 DIR_ID &gt;= #{cdt.startDirId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endDirId != null">and
			 DIR_ID &lt;= #{cdt.endDirId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.dirType != null">and
			DIR_TYPE = #{cdt.dirType:INTEGER}
		</if>
		<if test="dirTypes != null and dirTypes != ''">and
			DIR_TYPE in (${dirTypes})
		</if>
		<if test="cdt != null and cdt.startDirType != null">and
			 DIR_TYPE &gt;= #{cdt.startDirType:INTEGER} 
		</if>
		<if test="cdt != null and cdt.endDirType != null">and
			 DIR_TYPE &lt;= #{cdt.endDirType:INTEGER} 
		</if>
		<if test="cdt != null and cdt.diagramType != null">and
			DIAGRAM_TYPE = #{cdt.diagramType:INTEGER}
		</if>
		<if test="diagramTypes != null and diagramTypes != ''">and
			DIAGRAM_TYPE in (${diagramTypes})
		</if>
		<if test="cdt != null and cdt.startDiagramType != null">and
			 DIAGRAM_TYPE &gt;= #{cdt.startDiagramType:INTEGER} 
		</if>
		<if test="cdt != null and cdt.endDiagramType != null">and
			 DIAGRAM_TYPE &lt;= #{cdt.endDiagramType:INTEGER} 
		</if>
		<if test="cdt != null and cdt.diagramDesc != null and cdt.diagramDesc != ''">and
			DIAGRAM_DESC like #{cdt.diagramDesc,jdbcType=VARCHAR} 
		</if>
		<if test="cdt != null and cdt.diagramSvg != null and cdt.diagramSvg != ''">and
			DIAGRAM_SVG like #{cdt.diagramSvg,jdbcType=VARCHAR} 
		</if>
		<if test="cdt != null and cdt.diagramXml != null and cdt.diagramXml != ''">and
			DIAGRAM_XML like #{cdt.diagramXml,jdbcType=VARCHAR} 
		</if>
		<if test="cdt != null and cdt.diagramJson != null and cdt.diagramJson != ''">and
			DIAGRAM_JSON like #{cdt.diagramJson,jdbcType=VARCHAR} 
		</if>
		<if test="cdt != null and cdt.diagramBgImg != null and cdt.diagramBgImg != ''">and
			DIAGRAM_BG_IMG like #{cdt.diagramBgImg,jdbcType=VARCHAR} 
		</if>
		<if test="cdt != null and cdt.diagramBgCss != null and cdt.diagramBgCss != ''">and
			DIAGRAM_BG_CSS like #{cdt.diagramBgCss,jdbcType=VARCHAR} 
		</if>
		<if test="cdt != null and cdt.icon1 != null and cdt.icon1 != ''">and
			ICON_1 like #{cdt.icon1,jdbcType=VARCHAR} 
		</if>
		<if test="cdt != null and cdt.icon2 != null and cdt.icon2 != ''">and
			ICON_2 like #{cdt.icon2,jdbcType=VARCHAR} 
		</if>
		<if test="cdt != null and cdt.icon3 != null and cdt.icon3 != ''">and
			ICON_3 like #{cdt.icon3,jdbcType=VARCHAR} 
		</if>
		<if test="cdt != null and cdt.icon4 != null and cdt.icon4 != ''">and
			ICON_4 like #{cdt.icon4,jdbcType=VARCHAR} 
		</if>
		<if test="cdt != null and cdt.icon5 != null and cdt.icon5 != ''">and
			ICON_5 like #{cdt.icon5,jdbcType=VARCHAR} 
		</if>
		<if test="cdt != null and cdt.isOpen != null">and
			IS_OPEN = #{cdt.isOpen:INTEGER}
		</if>
		<if test="isOpens != null and isOpens != ''">and
			IS_OPEN in (${isOpens})
		</if>
		<if test="cdt != null and cdt.startIsOpen != null">and
			 IS_OPEN &gt;= #{cdt.startIsOpen:INTEGER} 
		</if>
		<if test="cdt != null and cdt.endIsOpen != null">and
			 IS_OPEN &lt;= #{cdt.endIsOpen:INTEGER} 
		</if>
		<if test="cdt != null and cdt.openTime != null">and
			OPEN_TIME = #{cdt.openTime:BIGINT}
		</if>
		<if test="openTimes != null and openTimes != ''">and
			OPEN_TIME in (${openTimes})
		</if>
		<if test="cdt != null and cdt.startOpenTime != null">and
			 OPEN_TIME &gt;= #{cdt.startOpenTime:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endOpenTime != null">and
			 OPEN_TIME &lt;= #{cdt.endOpenTime:BIGINT} 
		</if>
		<if test="cdt != null and cdt.dataUpType != null">and
			DATA_UP_TYPE = #{cdt.dataUpType:INTEGER}
		</if>
		<if test="dataUpTypes != null and dataUpTypes != ''">and
			DATA_UP_TYPE in (${dataUpTypes})
		</if>
		<if test="cdt != null and cdt.startDataUpType != null">and
			 DATA_UP_TYPE &gt;= #{cdt.startDataUpType:INTEGER} 
		</if>
		<if test="cdt != null and cdt.endDataUpType != null">and
			 DATA_UP_TYPE &lt;= #{cdt.endDataUpType:INTEGER} 
		</if>
		<if test="cdt != null and cdt.status != null">and
			STATUS = #{cdt.status:INTEGER}
		</if>
		<if test="statuss != null and statuss != ''">and
			STATUS in (${statuss})
		</if>
		<if test="cdt != null and cdt.startStatus != null">and
			 STATUS &gt;= #{cdt.startStatus:INTEGER} 
		</if>
		<if test="cdt != null and cdt.endStatus != null">and
			 STATUS &lt;= #{cdt.endStatus:INTEGER} 
		</if>
		<if test="cdt != null and cdt.ci3dPoint != null and cdt.ci3dPoint != ''">and
			CI_3D_POINT like #{cdt.ci3dPoint,jdbcType=VARCHAR} 
		</if>
		<if test="cdt != null and cdt.ci3dPoint2 != null and cdt.ci3dPoint2 != ''">and
			CI_3D_POINT2 like #{cdt.ci3dPoint2,jdbcType=VARCHAR} 
		</if>
		<if test="cdt != null and cdt.searchField != null and cdt.searchField != ''">and
			SEARCH_FIELD like #{cdt.searchField,jdbcType=VARCHAR} 
		</if>
		<if test="cdt != null and cdt.combRows != null">and
			COMB_ROWS = #{cdt.combRows:INTEGER}
		</if>
		<if test="combRowss != null and combRowss != ''">and
			COMB_ROWS in (${combRowss})
		</if>
		<if test="cdt != null and cdt.startCombRows != null">and
			 COMB_ROWS &gt;= #{cdt.startCombRows:INTEGER} 
		</if>
		<if test="cdt != null and cdt.endCombRows != null">and
			 COMB_ROWS &lt;= #{cdt.endCombRows:INTEGER} 
		</if>
		<if test="cdt != null and cdt.combCols != null">and
			COMB_COLS = #{cdt.combCols:INTEGER}
		</if>
		<if test="combColss != null and combColss != ''">and
			COMB_COLS in (${combColss})
		</if>
		<if test="cdt != null and cdt.startCombCols != null">and
			 COMB_COLS &gt;= #{cdt.startCombCols:INTEGER} 
		</if>
		<if test="cdt != null and cdt.endCombCols != null">and
			 COMB_COLS &lt;= #{cdt.endCombCols:INTEGER} 
		</if>
		<if test="cdt != null and cdt.readCount != null">and
			READ_COUNT = #{cdt.readCount:BIGINT}
		</if>
		<if test="readCounts != null and readCounts != ''">and
			READ_COUNT in (${readCounts})
		</if>
		<if test="cdt != null and cdt.startReadCount != null">and
			 READ_COUNT &gt;= #{cdt.startReadCount:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endReadCount != null">and
			 READ_COUNT &lt;= #{cdt.endReadCount:BIGINT} 
		</if>
		<if test="cdt != null and cdt.appRltCiCode != null and cdt.appRltCiCode != ''">and
			APP_RLT_CI_CODE like #{cdt.appRltCiCode,jdbcType=VARCHAR} 
		</if>
		<if test="cdt != null and cdt.appRltCiCodeEqual != null and cdt.appRltCiCodeEqual != ''">and
			APP_RLT_CI_CODE = #{cdt.appRltCiCodeEqual,jdbcType=VARCHAR}
		</if>
		<if test="appRltCiCodes != null and appRltCiCodes != ''">and
			APP_RLT_CI_CODE in (${appRltCiCodes})
		</if>
		<if test="cdt != null and cdt.referVersionId != null">and
			REFER_VERSION_ID = #{cdt.referVersionId:BIGINT}
		</if>
		<if test="referVersionIds != null and referVersionIds != ''">and
			REFER_VERSION_ID in (${referVersionIds})
		</if>
		<if test="cdt != null and cdt.startReferVersionId != null">and
			 REFER_VERSION_ID &gt;= #{cdt.startReferVersionId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endReferVersionId != null">and
			 REFER_VERSION_ID &lt;= #{cdt.endReferVersionId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.domainId != null">and
			DOMAIN_ID = #{cdt.domainId:BIGINT}
		</if>
		<if test="domainIds != null and domainIds != ''">and
			DOMAIN_ID in (${domainIds})
		</if>
		<if test="cdt != null and cdt.startDomainId != null">and
			 DOMAIN_ID &gt;= #{cdt.startDomainId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endDomainId != null">and
			 DOMAIN_ID &lt;= #{cdt.endDomainId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.dataStatus != null">and
			DATA_STATUS = #{cdt.dataStatus:INTEGER}
		</if>
		<if test="dataStatuss != null and dataStatuss != ''">and
			DATA_STATUS in (${dataStatuss})
		</if>
		<if test="cdt != null and cdt.startDataStatus != null">and
			 DATA_STATUS &gt;= #{cdt.startDataStatus:INTEGER} 
		</if>
		<if test="cdt != null and cdt.endDataStatus != null">and
			 DATA_STATUS &lt;= #{cdt.endDataStatus:INTEGER} 
		</if>
		<if test="cdt != null and cdt.creator != null and cdt.creator != ''">and
			CREATOR like #{cdt.creator,jdbcType=VARCHAR} 
		</if>
		<if test="cdt != null and cdt.creatorEqual != null and cdt.creatorEqual != ''">and
			CREATOR = #{cdt.creatorEqual,jdbcType=VARCHAR}
		</if>
		<if test="creators != null and creators != ''">and
			CREATOR in (${creators})
		</if>
		<if test="cdt != null and cdt.modifier != null and cdt.modifier != ''">and
			MODIFIER like #{cdt.modifier,jdbcType=VARCHAR} 
		</if>
		<if test="cdt != null and cdt.modifierEqual != null and cdt.modifierEqual != ''">and
			MODIFIER = #{cdt.modifierEqual,jdbcType=VARCHAR}
		</if>
		<if test="modifiers != null and modifiers != ''">and
			MODIFIER in (${modifiers})
		</if>
		<if test="cdt != null and cdt.createTime != null">and
			CREATE_TIME = #{cdt.createTime:BIGINT}
		</if>
		<if test="createTimes != null and createTimes != ''">and
			CREATE_TIME in (${createTimes})
		</if>
		<if test="cdt != null and cdt.startCreateTime != null">and
			 CREATE_TIME &gt;= #{cdt.startCreateTime:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endCreateTime != null">and
			 CREATE_TIME &lt;= #{cdt.endCreateTime:BIGINT} 
		</if>
		<if test="cdt != null and cdt.modifyTime != null">and
			MODIFY_TIME = #{cdt.modifyTime:BIGINT}
		</if>
		<if test="modifyTimes != null and modifyTimes != ''">and
			MODIFY_TIME in (${modifyTimes})
		</if>
		<if test="cdt != null and cdt.startModifyTime != null">and
			 MODIFY_TIME &gt;= #{cdt.startModifyTime:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endModifyTime != null">and
			 MODIFY_TIME &lt;= #{cdt.endModifyTime:BIGINT} 
		</if>
	</sql>
	

	<sql id="sql_update_columns">
		<if test="record != null and record.id != null"> 
			ID = #{record.id:BIGINT}
		,</if>
		<if test="record != null and record.name != null"> 
			NAME = #{record.name,jdbcType=VARCHAR}
		,</if>
		<if test="record != null and record.userId != null"> 
			USER_ID = #{record.userId:BIGINT}
		,</if>
		<if test="record != null and record.dirId != null"> 
			DIR_ID = #{record.dirId:BIGINT}
		,</if>
		<if test="record != null and record.subjectId != null">
			SUBJECT_ID = #{record.subjectId:BIGINT}
		,</if>
		<if test="record != null and record.dirType != null"> 
			DIR_TYPE = #{record.dirType:INTEGER}
		,</if>
		<if test="record != null and record.diagramType != null"> 
			DIAGRAM_TYPE = #{record.diagramType:INTEGER}
		,</if>
		<if test="record != null and record.diagramDesc != null"> 
			DIAGRAM_DESC = #{record.diagramDesc,jdbcType=VARCHAR}
		,</if>
		<if test="record != null and record.diagramSvg != null"> 
			DIAGRAM_SVG = #{record.diagramSvg,jdbcType=VARCHAR}
		,</if>
		<if test="record != null and record.diagramXml != null"> 
			DIAGRAM_XML = #{record.diagramXml,jdbcType=VARCHAR}
		,</if>
		<if test="record != null and record.diagramJson != null"> 
			DIAGRAM_JSON = #{record.diagramJson,jdbcType=VARCHAR}
		,</if>
		<if test="record != null and record.diagramBgImg != null"> 
			DIAGRAM_BG_IMG = #{record.diagramBgImg,jdbcType=VARCHAR}
		,</if>
		<if test="record != null and record.diagramBgCss != null"> 
			DIAGRAM_BG_CSS = #{record.diagramBgCss,jdbcType=VARCHAR}
		,</if>
		<if test="record != null and record.icon1 != null"> 
			ICON_1 = #{record.icon1,jdbcType=VARCHAR}
		,</if>
		<if test="record != null and record.icon2 != null"> 
			ICON_2 = #{record.icon2,jdbcType=VARCHAR}
		,</if>
		<if test="record != null and record.icon3 != null"> 
			ICON_3 = #{record.icon3,jdbcType=VARCHAR}
		,</if>
		<if test="record != null and record.icon4 != null"> 
			ICON_4 = #{record.icon4,jdbcType=VARCHAR}
		,</if>
		<if test="record != null and record.icon5 != null"> 
			ICON_5 = #{record.icon5,jdbcType=VARCHAR}
		,</if>
		<if test="record != null and record.isOpen != null"> 
			IS_OPEN = #{record.isOpen:INTEGER}
		,</if>
		<if test="record != null and record.openTime != null"> 
			OPEN_TIME = #{record.openTime:BIGINT}
		,</if>
		<if test="record != null and record.dataUpType != null"> 
			DATA_UP_TYPE = #{record.dataUpType:INTEGER}
		,</if>
		<if test="record != null and record.status != null"> 
			STATUS = #{record.status:INTEGER}
		,</if>
		<if test="record != null and record.ci3dPoint != null"> 
			CI_3D_POINT = #{record.ci3dPoint,jdbcType=VARCHAR}
		,</if>
		<if test="record != null and record.ci3dPoint2 != null"> 
			CI_3D_POINT2 = #{record.ci3dPoint2,jdbcType=VARCHAR}
		,</if>
		<if test="record != null and record.searchField != null"> 
			SEARCH_FIELD = #{record.searchField,jdbcType=VARCHAR}
		,</if>
		<if test="record != null and record.combRows != null"> 
			COMB_ROWS = #{record.combRows:INTEGER}
		,</if>
		<if test="record != null and record.combCols != null"> 
			COMB_COLS = #{record.combCols:INTEGER}
		,</if>
		<if test="record != null and record.readCount != null"> 
			READ_COUNT = #{record.readCount:BIGINT}
		,</if>
		<if test="record != null and record.appRltCiCode != null"> 
			APP_RLT_CI_CODE = #{record.appRltCiCode,jdbcType=VARCHAR}
		,</if>
		<if test="record != null and record.referVersionId != null"> 
			REFER_VERSION_ID = #{record.referVersionId:BIGINT}
		,</if>
		<if test="record != null and record.domainId != null"> 
			DOMAIN_ID = #{record.domainId:BIGINT}
		,</if>
		<if test="record != null and record.dataStatus != null"> 
			DATA_STATUS = #{record.dataStatus:INTEGER}
		,</if>
		<if test="record != null and record.creator != null"> 
			CREATOR = #{record.creator,jdbcType=VARCHAR}
		,</if>
		<if test="record != null and record.modifier != null"> 
			MODIFIER = #{record.modifier,jdbcType=VARCHAR}
		,</if>
		<if test="record != null and record.createTime != null"> 
			CREATE_TIME = #{record.createTime:BIGINT}
		,</if>
		<if test="record != null and record.modifyTime != null"> 
			MODIFY_TIME = #{record.modifyTime:BIGINT}
		,</if>
	</sql>
	

	<sql id="sql_query_columns">
		ID, NAME, USER_ID, DIR_ID, SUBJECT_ID, DIR_TYPE, DIAGRAM_TYPE,
		DIAGRAM_DESC, DIAGRAM_SVG, DIAGRAM_XML, DIAGRAM_JSON, DIAGRAM_BG_IMG, DIAGRAM_BG_CSS, 
		ICON_1, ICON_2, ICON_3, ICON_4, ICON_5, IS_OPEN, 
		OPEN_TIME, DATA_UP_TYPE, STATUS, CI_3D_POINT, CI_3D_POINT2, SEARCH_FIELD, 
		COMB_ROWS, COMB_COLS, READ_COUNT, APP_RLT_CI_CODE, REFER_VERSION_ID, DOMAIN_ID, 
		DATA_STATUS, CREATOR, MODIFIER, CREATE_TIME, MODIFY_TIME
	</sql>
	

	

	<select id="selectList" parameterType="java.util.Map" resultMap="queryResult">
		select 
			<include refid="VC_DIAGRAM.sql_query_columns"/>
		from VC_DIAGRAM 
			<where>
				<include refid="VC_DIAGRAM.sql_query_where"/>
			</where>
		order by 
			<if test="orders != null and orders != ''">
				${orders}
			</if>
			<if test="orders == null or orders == ''">
				ID
			</if>
	</select>
	<select id="selectCount" parameterType="java.util.Map" resultType="java.lang.Long">
		select count(1) from VC_DIAGRAM 
			<where>
				<include refid="VC_DIAGRAM.sql_query_where"/>
			</where>
	</select>
	<select id="selectById" parameterType="java.util.Map" resultMap="queryResult">
		select 
			<include refid="VC_DIAGRAM.sql_query_columns"/>
		from VC_DIAGRAM where ID=#{id:BIGINT} and DATA_STATUS=1  
	</select>
	

	

	<insert id="insert" parameterType="java.util.Map">
		insert into VC_DIAGRAM(
			ID, NAME, USER_ID, DIR_ID, SUBJECT_ID, DIR_TYPE,
			DIAGRAM_TYPE, DIAGRAM_DESC, DIAGRAM_SVG, DIAGRAM_XML, DIAGRAM_JSON, 
			DIAGRAM_BG_IMG, DIAGRAM_BG_CSS, ICON_1, ICON_2, ICON_3, 
			ICON_4, ICON_5, IS_OPEN, OPEN_TIME, DATA_UP_TYPE, 
			STATUS, CI_3D_POINT, CI_3D_POINT2, SEARCH_FIELD, COMB_ROWS, 
			COMB_COLS, READ_COUNT, APP_RLT_CI_CODE, REFER_VERSION_ID, DOMAIN_ID, 
			DATA_STATUS, CREATOR, MODIFIER, CREATE_TIME, MODIFY_TIME)
		values (
			#{record.id:BIGINT}, #{record.name,jdbcType=VARCHAR}, #{record.userId:BIGINT}, #{record.dirId:BIGINT}, #{record.subjectId:BIGINT}, #{record.dirType:INTEGER},
			#{record.diagramType:INTEGER}, #{record.diagramDesc,jdbcType=VARCHAR}, #{record.diagramSvg,jdbcType=VARCHAR}, #{record.diagramXml,jdbcType=VARCHAR}, #{record.diagramJson,jdbcType=VARCHAR}, 
			#{record.diagramBgImg,jdbcType=VARCHAR}, #{record.diagramBgCss,jdbcType=VARCHAR}, #{record.icon1,jdbcType=VARCHAR}, #{record.icon2,jdbcType=VARCHAR}, #{record.icon3,jdbcType=VARCHAR}, 
			#{record.icon4,jdbcType=VARCHAR}, #{record.icon5,jdbcType=VARCHAR}, #{record.isOpen:INTEGER}, #{record.openTime:BIGINT}, #{record.dataUpType:INTEGER}, 
			#{record.status:INTEGER}, #{record.ci3dPoint,jdbcType=VARCHAR}, #{record.ci3dPoint2,jdbcType=VARCHAR}, #{record.searchField,jdbcType=VARCHAR}, #{record.combRows:INTEGER}, 
			#{record.combCols:INTEGER}, #{record.readCount:BIGINT}, #{record.appRltCiCode,jdbcType=VARCHAR}, #{record.referVersionId:BIGINT}, #{record.domainId:BIGINT}, 
			#{record.dataStatus:INTEGER}, #{record.creator,jdbcType=VARCHAR}, #{record.modifier,jdbcType=VARCHAR}, #{record.createTime:BIGINT}, #{record.modifyTime:BIGINT})
	</insert>
	

	

	<update id="updateById" parameterType="java.util.Map">
		update VC_DIAGRAM
			<set> 
				<include refid="VC_DIAGRAM.sql_update_columns"/>
			</set>
		where ID = #{id:BIGINT}
	</update>
	<update id="updateByCdt" parameterType="java.util.Map">
		update VC_DIAGRAM
			<set> 
				<include refid="VC_DIAGRAM.sql_update_columns"/>
			</set>
			<where> 
				<include refid="VC_DIAGRAM.sql_query_where"/>
			</where>
	</update>
	
	

	

	<delete id="deleteById" parameterType="java.util.Map">
		delete from VC_DIAGRAM where ID = #{id:BIGINT}
	</delete>
	<delete id="deleteByCdt" parameterType="java.util.Map">
		delete from VC_DIAGRAM
			<where> 
				<include refid="VC_DIAGRAM.sql_query_where"/> 
			</where>
	</delete>
	



</mapper>