package com.uino.service.permission.microservice.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.binary.core.exception.MessageException;
import com.binary.core.i18n.LanguageResolver;
import com.binary.core.util.BinaryUtils;
import com.binary.framework.exception.ServiceException;
import com.binary.framework.spring.MultipartFileResource;
import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.comm.util.CiExcelUtil;
import com.uinnova.product.vmdb.comm.util.CommUtil;
import com.uino.bean.cmdb.base.ESCIClassInfo;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.base.ESCIRltInfo;
import com.uino.bean.cmdb.base.LibType;
import com.uino.bean.cmdb.business.ImportCellMessage;
import com.uino.bean.cmdb.business.ImportResultMessage;
import com.uino.bean.cmdb.business.ImportRowMessage;
import com.uino.bean.cmdb.business.ImportSheetMessage;
import com.uino.bean.cmdb.query.ESRltSearchBean;
import com.uino.bean.license.BaseLicenseAuth;
import com.uino.bean.permission.base.*;
import com.uino.bean.permission.business.*;
import com.uino.bean.permission.business.request.SaveUserRoleRltRequestDto;
import com.uino.bean.permission.query.*;
import com.uino.bean.sys.base.SysLoginLog;
import com.uino.dao.BaseConst;
import com.uino.dao.cmdb.ESCIClassSvc;
import com.uino.dao.cmdb.ESCIRltSvc;
import com.uino.dao.cmdb.ESCISvc;
import com.uino.dao.cmdb.ESCmdbCommSvc;
import com.uino.dao.license.ESLicenseAuthSvc;
import com.uino.dao.permission.ESModuleSvc;
import com.uino.dao.permission.ESRoleSvc;
import com.uino.dao.permission.ESUserSvc;
import com.uino.dao.permission.rlt.*;
import com.uino.dao.util.ESUtil;
import com.uino.service.permission.microservice.IUserSvc;
import com.uino.service.sys.microservice.ILogSvc;
import com.uino.service.sys.microservice.IResourceSvc;
import com.uino.service.util.FileUtil;
import com.uino.util.cache.CacheKeyPrefix;
import com.uino.util.cache.ICacheService;
import com.uino.util.rsm.RsmUtils;
import com.uino.util.sys.BeanUtil;
import com.uino.util.sys.CommonFileUtil;
import com.uino.util.sys.LibTypeUtil;
import com.uino.util.sys.SysUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.elasticsearch.index.query.*;
import org.elasticsearch.index.query.MultiMatchQueryBuilder.Type;
import org.elasticsearch.search.sort.SortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@RefreshScope
public class UserSvc implements IUserSvc {

    @Autowired
    private ESLicenseAuthSvc esLicenseAuthSvc;

    @Autowired
    private ESUserSvc userSvc;

    @Autowired
    private ESPerssionCommSvc perssionCommSvc;

    @Autowired
    private OrgSvc orgSvc;

    @Autowired
    private ESRoleSvc roleSvc;

    @Autowired
    private ESModuleSvc esModuleSvc;

    @Autowired
    private ESRoleDataModuleRltSvc rdRltSvc;

    @Autowired
    private ESUserModuleEnshrineRltSvc enshrineSvc;

    @Autowired
    @Lazy
    private ESCmdbCommSvc commSvc;

    @Autowired
    private IResourceSvc resourceSvc;

    @Autowired
    private ESUserRoleRltSvc userRoleRltSvc;

    @Autowired
    private ESOrgRoleRltSvc orgRoleRltSvc;

    @Autowired
    private ESUserOrgRltSvc userOrgRltSvc;

    @Autowired(required = false)
    private ILogSvc logSvc;

    @Value("${http.resource.space:}")
    private String urlPath;

    @Value("${http.resource.sync:}")
    private String syncHttpPath;

    @Value("${local.resource.space:}")
    private String localPath;

    @Value("${login_fail.lock.num:10}")
    private int loginFailLockNum;

    @Value("${user.lock.duration:600}")
    private long userLockDuration;

    private String defaultImgPath = "/122/defaultIcon/default_account_photo.png";

    private String initPassword;

    @Value("${uino.license:true}")
    private Boolean licenseEnable;

    @Resource
    private ICacheService iCacheService;

    private static final List<String> extendedPermissionsMapKey = Arrays.asList("extendedPermissions", "isdelete", "isupdate", "issee", "iscreate");

    private static final String ONLINE_USER_CODES_CACHE_KEY = "uino:online:user:cache";

    @Value("${uino.permission.user.initPassword:}")
    public void setInitPassword(String initPassword) {
        this.initPassword = BinaryUtils.isEmpty(initPassword) ? "Uinnova_001" : initPassword;
    }

    private String userModuleSign;

    @Autowired
    private RoleSvc roleService;

    @Value("${uino.permission.userModuleSign:}")
    public void setUserModuleSign(String userModuleSign) {
        this.userModuleSign = BinaryUtils.isEmpty(userModuleSign) ? "用户管理" : userModuleSign;
    }

    @Value("${uino.user.init.data.enable:false}")
    private Boolean initUserCiData;

    @Value("${uino.user.init.data.source.user:admin}")
    private String initDataSourceUser;

    @Value("${uino.user.init.demo.environment:false}")
    private Boolean isDemoEnvironment;

    @Autowired
    private ESCISvc esCiSvc;

    @Value("#{'${uino.user.init.data.ciclasscode:}'.split(',')}")
    private List<String> initCiClassList;

    @Autowired
    private ESCIClassSvc esciClassSvc;

    @Autowired
    private ESCIRltSvc esciRltSvc;

    @Autowired
    private RsmUtils rsmUtils;

    private static final String USER_IMAGE_REGEX = ".*[.](?i)(jpg|jpeg|png)";

    @Override
    public Long saveOrUpdate(UserInfo userInfo) {
        boolean isAdd = true; // 是否为新增用户
        BinaryUtils.checkEmpty(userInfo, "userInfo");
        //根据license校验用户数量
        verifyUserCount(1);
        // 校验用户信息格式
        List<String> checkUser = this.checkUser(userInfo, false);
        if (!checkUser.isEmpty()) {
            String errStr = checkUser.get(0);
            int i = checkUser.get(0).indexOf("$");
            if (i != -1) {
                throw MessageException.i18n(errStr.substring(0, i), errStr.substring(i + 1));
            } else {
                throw MessageException.i18n(errStr);
            }
        }
        Long domainId = userInfo.getDomainId() == null ? BaseConst.DEFAULT_DOMAIN_ID : userInfo.getDomainId();
        userInfo.setDomainId(domainId);
        // 校验登录名重复
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("loginCode.keyword", userInfo.getLoginCode()));
        query.must(QueryBuilders.termQuery("domainId", domainId));
        if (userInfo.getId() != null) {
            isAdd = false;
            query.mustNot(QueryBuilders.termQuery("id", userInfo.getId()));
        }
        List<SysUser> list = userSvc.getListByQuery(query);
        Assert.isTrue(BinaryUtils.isEmpty(list),
                "BS_USER_LOGIN_CODE_EXIST${loginCode:" + userInfo.getLoginCode() + "}");
        // 保存
        SysUser user = BeanUtil.converBean(userInfo, SysUser.class);
        Long userId = userSvc.saveOrUpdate(user);
        // 保存角色及组织信息，先清除再增加
        userInfo.setId(userId);
        this.saveRoleOrOrgDataByUserInfos(domainId, Collections.singletonList(userInfo), true);
        if(isAdd || isDemoEnvironment) { // 只有用户新增时再进行数据初始化
            log.info("环境参数isDemoEnvironment:{},初始化用户数据", isDemoEnvironment);
            initUserCiData(userInfo.getLoginCode());
        }
        return userId;
    }


    @Override
    public List<SysUser> getAllUser() {
        return userSvc.getListByQueryScroll(QueryBuilders.termQuery("domainId",SysUtil.getCurrentUserInfo().getDomainId()));
    }

    @Override
    public ImportSheetMessage saveOrUpdateUserInfoBatch(Long domainId, boolean isUpdatePassword, List<UserInfo> userInfos) {
        //根据license校验用户数量
        verifyUserCount(userInfos.size());
        domainId = domainId == null ? BaseConst.DEFAULT_DOMAIN_ID : domainId;
        ImportSheetMessage result = new ImportSheetMessage();
        List<ImportRowMessage> rowMessages = new ArrayList<>();
        if (BinaryUtils.isEmpty(userInfos)) {
            return result;
        }
        SysUser loginUser = null;
        try {
            loginUser = SysUtil.getCurrentUserInfo();
        } catch (Exception e) {
            log.error("获取登陆用户失败，所有值取默认值");
        }
        int ignore = 0;
        int fail = 0;
        int insert = 0;
        int update = 0;
        result.setTotalNum(userInfos.size());
        Set<String> codes = userInfos.stream().map(SysUser::getLoginCode).filter(x -> !BinaryUtils.isEmpty(x))
                .collect(Collectors.toSet());
        // TODO 后续应改为根据总条数计算分页分批次导入
        Assert.isTrue(codes.size() < 3000, "单次导入最大支持3000条记录，请分批次进行导入。");
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termsQuery("loginCode.keyword", codes));
        query.must(QueryBuilders.termQuery("domainId", domainId));
        List<SysUser> dbUsers = userSvc.getListByQueryScroll(query);
        Map<String, SysUser> stringSysUserHashMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(dbUsers)){
            for (SysUser dbUser : dbUsers) {
                stringSysUserHashMap.put(dbUser.getLoginCode(),dbUser);
            }
        }
        List<String> loginCodes = BinaryUtils.isEmpty(dbUsers) ? new ArrayList<>()
                : dbUsers.stream().map(SysUser::getLoginCode).collect(Collectors.toList());
        // 去除重复及不符合规范的数据
        List<String> imCodes = new ArrayList<>();
        Iterator<UserInfo> it = userInfos.iterator();
        while (it.hasNext()) {
            ImportRowMessage rowMessage = new ImportRowMessage();
            List<ImportCellMessage> cellMessages = new ArrayList<>();
            UserInfo user = it.next();
            // 在当前列表中重复，忽略
            if (imCodes.contains(user.getLoginCode())) {
                ImportCellMessage cellMessage = new ImportCellMessage();
                cellMessage
                        .setErrorDesc("【" + user.getLoginCode() + "】" + LanguageResolver.trans("BS_MVTYPE_DUPLICATE"));
                cellMessages.add(cellMessage);
            }
            // 校验用户信息
            List<String> checkUser = this.checkUser(user, isUpdatePassword);
            if (!BinaryUtils.isEmpty(checkUser)) {
                for (String errStr : checkUser) {
                    ImportCellMessage cellMessage = new ImportCellMessage();
                    String errDesc = null;
                    int i = errStr.indexOf("$");
                    if (i != -1) {
                        errDesc = LanguageResolver.trans(errStr.substring(0, i), errStr.substring(i + 1));
                    } else {
                        errDesc = LanguageResolver.trans(errStr);
                    }
                    cellMessage.setErrorDesc(errDesc);
                    cellMessages.add(cellMessage);
                }
            }
            if (loginCodes.contains(user.getLoginCode())) {
                SysUser sysUser = stringSysUserHashMap.get(user.getLoginCode());
                user.setId(sysUser.getId());
                update++;
            }
            if (!BinaryUtils.isEmpty(cellMessages)) {
                ignore++;
                rowMessage.setRowNum(user.getIndex());
                rowMessage.setMessageItems(cellMessages);
                rowMessages.add(rowMessage);
                it.remove();
                continue;
            }
            imCodes.add(user.getLoginCode());
            user.setDomainId(domainId);
            if (BinaryUtils.isEmpty(user.getId())) {
                user.setId(ESUtil.getUUID());
                user.setCreateTime(ESUtil.getNumberDateTime());
                user.setCreator(loginUser == null ? "system" : loginUser.getLoginCode());
            }
        }
        Map<String, Object> saveOrUpdateMsg = new HashMap<>();
        if (!BinaryUtils.isEmpty(userInfos)) {
            insert += userInfos.size();
            saveOrUpdateMsg = userSvc.saveOrUpdateBatchMessage(
                    JSON.parseArray(JSON.toJSONString(BeanUtil.converBean(userInfos, SysUser.class))), false);
            // 保存角色和组织关系
            saveRoleOrOrgDataByUserInfos(domainId, userInfos, true);
        }
        if (saveOrUpdateMsg.containsKey("failCount")) {
            fail = (Integer) saveOrUpdateMsg.get("failCount");
        }
        result.setSuccessNum(insert - fail - update);
        result.setInsertNum(insert);
        result.setUpdateNum(update);
        result.setIgnoreNum(ignore);
        result.setFailNum(fail);
        result.setRowMessages(rowMessages);
        return result;
    }

    @Override
    public ImportSheetMessage syncUserBatch(Long domainId, List<UserInfo> userInfos) {
        domainId = domainId == null ? BaseConst.DEFAULT_DOMAIN_ID : domainId;
        ImportSheetMessage result = new ImportSheetMessage();
        if (BinaryUtils.isEmpty(userInfos)) {
            return result;
        }
        int ignore = 0;
        int fail = 0;
        int update = 0;
        int insert = 0;
        result.setTotalNum(userInfos.size());
        // 去除重复及不符合规范的数据
        List<String> imCodes = new ArrayList<>();
        Iterator<UserInfo> it = userInfos.iterator();
        while (it.hasNext()) {
            SysUser user = it.next();
            // 在当前列表中重复，忽略
            if (imCodes.contains(user.getLoginCode())) {
                ignore++;
                it.remove();
                continue;
            }
            // 不更新产品内置用户
            if ("admin".equals(user.getLoginCode()) || "superadmin".equals(user.getLoginCode())) {
                ignore++;
                it.remove();
                continue;
            }
            // 邮箱添加默认值
            if (BinaryUtils.isEmpty(user.getEmailAdress())) {
                user.setEmailAdress("<EMAIL>");
            }
            //丰富domainId
            if (BinaryUtils.isEmpty(user.getDomainId())) {
                user.setDomainId(domainId);
            }
            // 校验用户信息
            List<String> checkUser = this.checkUser(user, false);
            if (!BinaryUtils.isEmpty(checkUser)) {
                fail++;
                it.remove();
            }
            imCodes.add(user.getLoginCode());
        }
        // loginCode重复则更新，否则新增
        Map<String, Object> saveOrUpdateMsg = new HashMap<>();
        if (!BinaryUtils.isEmpty(userInfos)) {
            CSysUser cdt = new CSysUser();
            Set<String> codes = userInfos.stream().map(SysUser::getLoginCode).collect(Collectors.toSet());
            cdt.setLoginCodes(codes.toArray(new String[]{}));
            cdt.setDomainId(domainId);
            List<SysUser> dbUsers = userSvc.getListByCdt(cdt);
            Map<String, Long> codeToId = new HashMap<>();
            if (!BinaryUtils.isEmpty(dbUsers)) {
                for (SysUser user : dbUsers) {
                    codeToId.put(user.getLoginCode(), user.getId());
                }
            }
            for (SysUser user : userInfos) {
                String loginCode = user.getLoginCode();
                if (codeToId.containsKey(loginCode)) {
                    // 更新
                    update++;
                    user.setId(codeToId.get(loginCode));
                } else {
                    // 新增
                    user.setId(ESUtil.getUUID());
                    user.setCreateTime(ESUtil.getNumberDateTime());
                    insert++;
                }
            }
            saveOrUpdateMsg = userSvc.saveOrUpdateBatchMessage(
                    JSON.parseArray(JSON.toJSONString(BeanUtil.converBean(userInfos, SysUser.class))), false);
            // 保存角色和组织关系
            saveRoleOrOrgDataByUserInfos(domainId, userInfos, true);
        }
        int saveFail = 0;
        if (saveOrUpdateMsg.containsKey("failCount")) {
            saveFail = (Integer) saveOrUpdateMsg.get("failCount");
        }
        result.setSuccessNum(insert + update - saveFail);
        result.setInsertNum(insert);
        result.setUpdateNum(update);
        result.setIgnoreNum(ignore);
        result.setFailNum(fail + saveFail);
        return result;
    }

    @Override
    public UserInfo getUserInfoById(Long userId) {
//        if (userId == null || SysUtil.getCurrentUserInfo().getId().longValue() != userId.longValue()) {
//            throw MessageException.i18n("BS_NOT_AUTH", new HashMap<String, String>());
//        }
        SysUser user = userSvc.getById(userId);
        Assert.notNull(user, "BS_USER_NOT_EXIST${loginCode:" + String.valueOf(userId) + "}");
        List<UserInfo> infos = userSvc.fillInfo(Collections.singletonList(user), true);
        return infos.get(0);
    }

    @Override
    public UserInfo getUserInfoByLoginCode(String loginCode) {
        BoolQueryBuilder must = QueryBuilders.boolQuery().must(QueryBuilders.termQuery("loginCode.keyword", loginCode));
        List<SysUser> listByQuery = userSvc.getListByQuery(must);
        if(CollectionUtils.isEmpty(listByQuery)){
            Assert.notEmpty(listByQuery, "BS_USER_NOT_EXIST${loginCode:" + loginCode + "}");
        }
        List<UserInfo> infos = userSvc.fillInfo(listByQuery, true);
        return infos.get(0);
    }

    @Override
    public SysUser getUserByLoginCode(String loginCode) {
        BoolQueryBuilder must = QueryBuilders.boolQuery().must(QueryBuilders.termQuery("loginCode.keyword", loginCode));
        List<SysUser> listByQuery = userSvc.getListByQuery(must);
        if(CollectionUtils.isEmpty(listByQuery)){
            Assert.notEmpty(listByQuery, "BS_USER_NOT_EXIST${loginCode:" + loginCode + "}");
        }
        return listByQuery.get(0);
    }

    @Override
    public List<SysUser> getSysUserByCdt(CSysUser cdt) {
        if (cdt == null) {
            cdt = new CSysUser();
        }
        cdt.setDomainId(cdt.getDomainId() == null ? BaseConst.DEFAULT_DOMAIN_ID : cdt.getDomainId());
        List<SysUser> sysUsers = userSvc.getListByCdt(cdt);
        sysUsers.forEach(user -> user.setIcon(urlPath + user.getIcon()));
        return sysUsers;
    }

    @Override
    public List<UserInfo> getUserInfoByCdt(CSysUser cdt, boolean rmSensitiveData) {
        if (cdt == null) {
            cdt = new CSysUser();
        }
        if (BinaryUtils.isEmpty(cdt.getDomainId())) {
            cdt.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
        }

        return userSvc.getUserInfoListByQuery(ESUtil.cdtToBuilder(cdt), rmSensitiveData);
    }

    @Override
    public Page<UserInfo> getListByQuery(UserSearchBeanExtend bean) {
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        if (bean == null) {
            bean = new UserSearchBeanExtend();
        }
        String keyword = bean.getKeyword();
        Assert.isTrue(BinaryUtils.isEmpty(keyword) || keyword.trim().length() <= 200, "BS_SEARCH_LENGTH_ERR");
        if (BinaryUtils.isEmpty(bean.getDomainId())) {
            bean.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
        }
        // 组装查询条件
        List<Long> userIds = new ArrayList<>();
        if (!BinaryUtils.isEmpty(bean.getRoleId()) || !BinaryUtils.isEmpty(bean.getRoleIds())) {
            Set<Long> roleIds = bean.getRoleIds() == null ? new HashSet<>() : bean.getRoleIds();
            if (bean.getRoleId() != null) {
                roleIds.add(bean.getRoleId());
            }
            List<SysUserRoleRlt> userRoleRlts = perssionCommSvc.getUserRoleRltByRoleIds(roleIds);
            Set<Long> rUserIds = userRoleRlts.stream().map(SysUserRoleRlt::getUserId).collect(Collectors.toSet());
            if (BinaryUtils.isEmpty(
                    rUserIds)) {
                return new Page<>(bean.getPageNum(), bean.getPageSize(), 0, 0, new ArrayList<>());
            }
            userIds.addAll(rUserIds);
        }
        // 需要组织筛选
        if (!BinaryUtils.isEmpty(bean.getOrgId()) || !BinaryUtils.isEmpty(bean.getOrgIds())) {
            // 仅查看当前组织
            if (bean.isOnlyCurrentOrg()) {
                Set<Long> orgIds = bean.getOrgIds() == null ? new HashSet<>() : bean.getOrgIds();
                if (bean.getOrgId() != null) {
                    orgIds.add(bean.getOrgId());
                }
                List<SysUserOrgRlt> userOrgRlts = perssionCommSvc.getUserOrgRltByOrgIds(orgIds);
                Set<Long> inOrgUserIds = userOrgRlts.stream().map(SysUserOrgRlt::getUserId).collect(Collectors.toSet());
                query.must(QueryBuilders.termsQuery("id", inOrgUserIds));
            } else if (bean.getOrgId()!=null && bean.getOrgId().longValue() != bean.getDomainId()) {

                // 需要级联查看下级，如果orgid为0则不筛选，因为级联下级传根等于没筛选了
                Set<Long> orgIds = new HashSet<>();
                CSysOrg cSysOrgCdt = new CSysOrg();
                cSysOrgCdt.setDomainId(bean.getDomainId());
                List<SysOrg> orgs = orgSvc.queryPageByCdt(1, 3000, cSysOrgCdt).getData();
                Map<Long, List<SysOrg>> orgIdChildsMap = new LinkedHashMap<>();
                if (orgs != null) {
                    for (SysOrg org : orgs) {
                        orgIdChildsMap.put(org.getParentOrgId(), orgIdChildsMap.get(org.getParentOrgId()) == null ? new ArrayList<>() : orgIdChildsMap.get(org.getParentOrgId()));
                        orgIdChildsMap.get(org.getParentOrgId()).add(org);
                    }
                }
                getAllChilds(orgIdChildsMap, orgIds, bean.getOrgId());
                if (orgIds.size() <= 0) {
                    return new Page<>(bean.getPageNum(), bean.getPageSize(), 0,
                            0, new ArrayList<>());
                }
                List<SysUserOrgRlt> userOrgRlts = perssionCommSvc.getUserOrgRltByOrgIds(orgIds);
                if (userOrgRlts == null || userOrgRlts.size() <= 0) {
                    return new Page<>(bean.getPageNum(),
                            bean.getPageSize(), 0, 0, new ArrayList<>());
                }
                Set<Long> inOrgUserIds = userOrgRlts.stream().collect(Collectors.groupingBy(SysUserOrgRlt::getUserId))
                        .keySet();
                query.must(QueryBuilders.termsQuery("id", inOrgUserIds));
            }
        }
        //集群环境下，keys数量会直接影响到iCacheService.getKeysByPattern()方法效率，这里调整策略：先获取用户，再查当前用户有无token，进一步在线用户数统计
        Set<String> onlineUserNames = getOnlineUserCodes(bean.getDomainId());
//        Set<String> keysByPattern = iCacheService.getKeysByPattern(CacheKeyPrefix.UINO_USER_ONLINE_PREFIX + "*");
//        Set<String> onlineUserNames = keysByPattern.parallelStream().map(s -> s.substring(s.lastIndexOf(":") + 1)).collect(Collectors.toSet());
        //在线用户
        if (bean.getOnlineStatus() != null && bean.getOnlineStatus() != 0) {
            if(bean.getOnlineStatus()==1){
                query.must(QueryBuilders.termsQuery("loginCode.keyword",onlineUserNames));
            }
            if(bean.getOnlineStatus()==2){
                query.mustNot(QueryBuilders.termsQuery("loginCode.keyword",onlineUserNames));
            }
        }

        query.must(QueryBuilders.rangeQuery("id").from(1));
        query.must(QueryBuilders.termQuery("domainId", bean.getDomainId()));
        if (!BinaryUtils.isEmpty(userIds)) {
            query.must(QueryBuilders.termsQuery("id", userIds));
        }
        if (keyword != null && keyword.length() > 0) {
            BoolQueryBuilder keywordQuery = QueryBuilders.boolQuery();
            keywordQuery.should(QueryBuilders.wildcardQuery("userName.keyword", "*" + keyword + "*"));
            keywordQuery.should(QueryBuilders.wildcardQuery("loginCode.keyword", "*" + keyword + "*"));
            query.must(keywordQuery);
//            query.must(QueryBuilders.multiMatchQuery(keyword, "userName", "loginCode").operator(Operator.AND)
//                    .type(Type.PHRASE_PREFIX).lenient(true));
        }
        List<SortBuilder<?>> sorts = new LinkedList<>();
        if (bean.getOrder() != null && !"".equals(bean.getOrder().trim())) {
            sorts.add(SortBuilders.fieldSort(bean.getOrder()).order(bean.isDesc() ? SortOrder.DESC : SortOrder.ASC));
        } else {
            sorts.add(SortBuilders.fieldSort("createTime").order(SortOrder.DESC));
            sorts.add(SortBuilders.fieldSort("id").order(SortOrder.ASC));
        }
        Page<SysUser> page = userSvc.getSortListByQuery(bean.getPageNum(), bean.getPageSize(), query, sorts);
        List<UserInfo> fillInfos = userSvc.fillInfo(page.getData(), true);
        if (!CollectionUtils.isEmpty(onlineUserNames)) {
            for (UserInfo fillInfo : fillInfos) {
                if (onlineUserNames.contains(fillInfo.getLoginCode())) {
                    fillInfo.setOnline(Boolean.TRUE);
                }
            }
        }
        UserPage<UserInfo> userInfoUserPage = new UserPage<>(page.getPageNum(), page.getPageSize(), page.getTotalRows(), page.getTotalPages(), fillInfos);
        if(!CollectionUtils.isEmpty(onlineUserNames)){
            userInfoUserPage.setTotalOnlineNum(onlineUserNames.size());
        }
        return userInfoUserPage;
    }

    /**
     * 获取在线用户登录codes
     * @param domainId
     * @return
     */
    private Set<String> getOnlineUserCodes(Long domainId) {
        //先cache一下
        Set<String> onlineUserCodes = (Set<String>)iCacheService.getCache(ONLINE_USER_CODES_CACHE_KEY);
        if (CollectionUtils.isEmpty(onlineUserCodes)) {
            onlineUserCodes = new HashSet<>();

            BoolQueryBuilder query = QueryBuilders.boolQuery();
            query.must(QueryBuilders.termQuery("domainId", domainId));
            //最后一次登录时间是最近两天的用户
            Calendar calendar = Calendar.getInstance();
            calendar.setTimeInMillis(System.currentTimeMillis());
            calendar.add(Calendar.DATE, -2);
            RangeQueryBuilder rangeQuery = QueryBuilders.rangeQuery("lastLoginTime");
            rangeQuery.gte(calendar.getTime().getTime());
            query.must(rangeQuery);
            List<SysUser> users = userSvc.getListByQueryScroll(query);
            if (!CollectionUtils.isEmpty(users)) {
                for (SysUser user : users) {
                    Object value = iCacheService.getCache(CacheKeyPrefix.UINO_USER_ONLINE_TOKEN_PREFIX + ":" + user.getLoginCode());
                    if (value != null) {
                        onlineUserCodes.add(user.getLoginCode());
                    }
                }
            }
            iCacheService.setCache(ONLINE_USER_CODES_CACHE_KEY, onlineUserCodes, 30 * 1000L);
        }
        return onlineUserCodes;
    }

    /**
     * 获取指定组织所有子节点
     *
     * @param orgDict      组织id:childNodes字典
     * @param orgIds       待填充所有子节点orgIds=>涵盖自身
     * @param currentOrgId 当前指定组织
     */
    public void getAllChilds(Map<Long, List<SysOrg>> orgDict, Set<Long> orgIds, Long currentOrgId) {
        orgIds.add(currentOrgId);
        if (orgDict.get(currentOrgId) != null) {
            orgDict.get(currentOrgId).forEach(child -> {
                // 最后一层可能不再字典中，反正用的set再添一次
                orgIds.add(child.getId());
                getAllChilds(orgDict, orgIds, child.getId());
            });
        }
    }

    @Override
    public List<SysUser> getUserByRoleId(Long roleId) {
        BinaryUtils.checkEmpty(roleId, "roleId");
        List<SysUserRoleRlt> userRoleRlts = perssionCommSvc.getUserRoleRltByRoleIds(Collections.singleton(roleId));
        if (BinaryUtils.isEmpty(userRoleRlts)) {
            return new ArrayList<SysUser>();
        }
        List<Long> userIds = userRoleRlts.stream().map(SysUserRoleRlt::getUserId).collect(Collectors.toList());
        List<SysUser> users = userSvc.getListByQueryScroll(QueryBuilders.termsQuery("id", userIds));
        users.forEach(user -> user.clearSensitive());
        return users;
    }

    @Override
    public Long resetPwd(Long domainId, SysUserPwdResetParam pwdParam) {
        // 参数校验
        domainId = domainId == null ? BaseConst.DEFAULT_DOMAIN_ID : domainId;
        Long userId = pwdParam.getUserId();
        String loginCode = pwdParam.getLoginCode();
        String oldPwd = pwdParam.getOldPasswd();
        String newPwd = pwdParam.getNewPasswd();
        Assert.isTrue(userId != null || !BinaryUtils.isEmpty(loginCode), "X_PARAM_NOT_NULL${name:userId/loginCode}");
        Assert.isTrue(!BinaryUtils.isEmpty(newPwd), "X_PARAM_NOT_NULL${name:newPwd}");
        Assert.isTrue(!BinaryUtils.isEmpty(oldPwd), "X_PARAM_NOT_NULL${name:oldPwd}");
        CSysUser cdt = new CSysUser();
        cdt.setId(userId);
        cdt.setLoginCodeEqual(loginCode);
        cdt.setDomainId(domainId);
        List<SysUser> users = userSvc.getListByCdt(cdt);
        Assert.isTrue(!BinaryUtils.isEmpty(users), "BS_USER_NOT_EXIST");
        SysUser user = users.get(0);
        // 权限校验
        if (user.getId().longValue() != SysUtil.getCurrentUserInfo().getId()
                .longValue()) {
            throw MessageException.i18n("BS_NOT_AUTH", new HashMap<String, String>());
        }
        // 密码比对
        oldPwd = SysUtil.EncryptDES.decryptDES(oldPwd);
        newPwd = SysUtil.EncryptDES.decryptDES(newPwd);
        String oldPasswd = DigestUtils.sha256Hex(oldPwd);
        Assert.isTrue(oldPasswd.equals(user.getLoginPasswd()), "BS_OLD_PASSWORD_ERROR");
        if (oldPwd.equals(newPwd)) {
            return userId;
        }
        user.setLoginPasswd(DigestUtils.sha256Hex(newPwd));
        user.setIsUpdatePwd(0);
        return userSvc.saveOrUpdate(user);
    }

    @Override
    public Long resetPwdByAdmin(SysUserPwdResetParam pwdParam) {
        Long userId = pwdParam.getUserId();
        String loginCode = pwdParam.getLoginCode();
        String newPwd = pwdParam.getNewPasswd();
        Assert.isTrue(userId != null || !BinaryUtils.isEmpty(loginCode), "X_PARAM_NOT_NULL${name:userId/loginCode}");
        Assert.isTrue(!BinaryUtils.isEmpty(newPwd), "X_PARAM_NOT_NULL${name:newPwd}");
        newPwd = SysUtil.EncryptDES.decryptDES(newPwd);
        CSysUser cdt = new CSysUser();
        if (pwdParam.getDomainId() == null) {
            cdt.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
        }else {
            cdt.setDomainId(pwdParam.getDomainId());
        }

        cdt.setId(userId);
        cdt.setLoginCodeEqual(loginCode);
        List<SysUser> users = userSvc.getListByCdt(cdt);
        if (!BinaryUtils.isEmpty(users)) {
            SysUser user = users.get(0);
            user.setLoginPasswd(DigestUtils.sha256Hex(newPwd));
            user.setIsUpdatePwd(0);
            return userSvc.saveOrUpdate(user);
        } else {
            throw MessageException.i18n("BS_USER_NOT_EXIST");
        }
    }

    @Override
    public Long updateCurUser(SysUser cur, MultipartFile file) {
        Long userId = cur.getId();
        if (BinaryUtils.isEmpty(userId)) {
            userId = SysUtil.getCurrentUserInfo().getId();
        }
        SysUser user = userSvc.getById(userId);
        Assert.notNull(user, "BS_USER_NOT_EXIST");
        if (BinaryUtils.isEmpty(cur.getDomainId())) {
            cur.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
        }
        // 保存头像
        if (file != null) {
            boolean matches = Objects.requireNonNull(file.getOriginalFilename()).matches(USER_IMAGE_REGEX);
            if (!matches) {
                throw MessageException.i18n("BS_FORMAT_ERROR", "{\"field\":\"图片\"}");
            }
            Long dateTimeFolder = ESUtil.getNumberDate();
            String imgType = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf(".") + 1);
            String fileName = UUID.randomUUID().toString() + "." + imgType;
            String imgPath = "/" + dateTimeFolder + "/" + fileName;
            File destFolder = new File(localPath + "/" + dateTimeFolder);
            if (!destFolder.exists()) {
                destFolder.mkdirs();
            }
            File destFile = new File(destFolder, "/" + fileName);
            try {
                file.transferTo(destFile);

                // 文件写入对象存储
                rsmUtils.uploadRsmFromFile(destFile);

                // 记录文件操作
                resourceSvc.saveSyncResourceInfo(imgPath, syncHttpPath + imgPath, false, 0);
            } catch (Exception e) {
                throw new MessageException(e.getMessage());
            }
            cur.setIcon(imgPath);
        } else {
            cur.setIcon(null);
        }
        user.setUserName(cur.getUserName());
        user.setEmailAdress(cur.getEmailAdress());
        user.setMobileNo(cur.getMobileNo());
        user.setNotes(cur.getNotes());
        user.setDomainId(cur.getDomainId());
        if (!BinaryUtils.isEmpty(cur.getIcon())) {
            user.setIcon(cur.getIcon());
        }
        List<String> checkUser = this.checkUser(user, false);
        if (!BinaryUtils.isEmpty(checkUser)) {
            String errStr = checkUser.get(0);
            int i = checkUser.get(0).indexOf("$");
            if (i != -1) {
                throw MessageException.i18n(errStr.substring(0, i), errStr.substring(i + 1));
            } else {
                throw MessageException.i18n(errStr);
            }
        }
        return userSvc.saveOrUpdate(user);
    }

    @Override
    public ImportResultMessage importUserByExcel(Long domainId,MultipartFile file) {
        domainId = domainId == null ? BaseConst.DEFAULT_DOMAIN_ID : domainId;
        if (file == null || file.isEmpty()) {
            throw new ServiceException(" the upload file is empty! ");
        }
        String importNotice = LanguageResolver.trans("BS_MNAME_IMPORT_NOTICE");
        if (importNotice == null) {
            importNotice = SysUtil.StaticUtil.README_SHEETNAME;
        }
        // 查询所有角色
        CSysRole cSysRole = new CSysRole();
        cSysRole.setDomainId(domainId);
        List<SysRole> roles = roleSvc.getListByCdt(cSysRole);
        Map<String, SysRole> roleMap = BinaryUtils.toObjectMap(roles, "roleName");
        // 查询组织树结构
        Map<String, OrgNodeInfo> orgStructure = orgSvc.getOrgStructure(domainId);
        // 解析文件
        MultipartFileResource resource = new MultipartFileResource(file);
        String fileType = CommUtil.getImportExcelType(resource.getName());
        InputStream is = resource.getInputStream();
        List<ImportSheetMessage> messages = new ArrayList<>();
        try {
            Workbook wb = null;
            Assert.isTrue(!BinaryUtils.isEmpty(fileType), "X_PARAM_NOT_NULL${name:fileType}");
            try {
                if (fileType.toUpperCase().equals(CommUtil.EXCEL03_XLS_EXTENSION.toUpperCase())) {
                    wb = new HSSFWorkbook(is);
                } else if (fileType.toUpperCase().equals(CommUtil.EXCEL07_XLSX_EXTENSION.toUpperCase())) {
                    wb = new XSSFWorkbook(is);
                } else {
                    // 无法操作不支持的文件类型！
                    throw MessageException.i18n("BS_MNAME_NOTSUPPORT_FILETYPE");
                }
            } catch (Exception e) {
                throw BinaryUtils.transException(e, ServiceException.class);
            }
            for (int numSheet = 0; numSheet < wb.getNumberOfSheets(); numSheet++) {
                Sheet hssfSheet = wb.getSheetAt(numSheet);
                if (hssfSheet == null) {
                    continue;
                }
                String sheetName = hssfSheet.getSheetName();
                if (sheetName.equals(importNotice)) {
                    continue;
                }
                ImportSheetMessage sheetMessage = ImportSheetMessage.builder().sheetName(sheetName).className(sheetName)
                        .build();
                messages.add(sheetMessage);
                List<String> titles = new ArrayList<>(FileUtil.ExcelUtil.getSheetTitles(hssfSheet).values());
                if (BinaryUtils.isEmpty(titles) || titles.size() <= 1) {
                    sheetMessage.setErrMsg(sheetName + "数据异常！");
                    continue;
                }
                // 用户记录当前sheet数据保存详情
                if (BinaryUtils.isEmpty(sheetMessage.getTitles())) {
                    sheetMessage.setTitles(titles);
                }
                // 获取现有用户，用于校验重复
                List<UserInfo> userInfos = new ArrayList<>();
                for (int rowNum = 1; rowNum <= hssfSheet.getLastRowNum(); rowNum++) {
                    Row row = hssfSheet.getRow(rowNum);
                    boolean isBlankRow = FileUtil.ExcelUtil.isBlankRow(row);
                    // 遇空行截断，不向下读取
                    if (isBlankRow) {
                        break;
                    }
                    if (row != null) {
                        UserInfo userInfo = new UserInfo();
                        int c = 0;
                        // 登录名
                        Cell loginCodeCell = row.getCell(c++);
                        String loginCode = getValue(loginCodeCell);
                        // 用户名
                        Cell userNameCell = row.getCell(c++);
                        String userName = getValue(userNameCell);
                        // 登录密码
                        Cell loginPwdCell = row.getCell(c++);
                        String pwd = getValue(loginPwdCell);
                        // 邮箱
                        Cell emailCell = row.getCell(c++);
                        String emailAddr = getValue(emailCell);
                        // 联系方式
                        Cell mobileNoCell = row.getCell(c++);
                        String mobileNo = getValue(mobileNoCell);
                        // 即时通讯
                        Cell imsCell = row.getCell(c++);
                        String imsAdress = getValue(imsCell);
                        // 所属组织
                        Cell orgCell = row.getCell(c++);
                        String orgAddr = getValue(orgCell);
                        if (!BinaryUtils.isEmpty(orgAddr)) {
                            String[] orgArr = orgAddr.split(";");
                            for (int i = 0; i < orgArr.length; i++) {
                                if (BinaryUtils.isEmpty(orgArr[i])) {
                                    continue;
                                }
                                // 根据组织名称得到SysOrg对象
                                SysOrg org = orgSvc.getOrgByOrgFullName(orgArr[i], orgStructure);
                                if (org != null) {
                                    if (userInfo.getOrgs() == null) {
                                        userInfo.setOrgs(new HashSet<>());
                                    }
                                    userInfo.getOrgs().add(org);
                                }
                            }
                        }
                        // 个人角色
                        Cell roleCell = row.getCell(c++);
                        String roleStr = getValue(roleCell);
                        if (!BinaryUtils.isEmpty(roleStr)) {
                            String[] roleArr = roleStr.split(";");
                            for (int i = 0; i < roleArr.length; i++) {
                                if (BinaryUtils.isEmpty(roleArr[i])) {
                                    continue;
                                }
                                SysRole r = roleMap.get(roleArr[i]);
                                if (r != null) {
                                    if (userInfo.getRoles() == null) {
                                        Set<SysRole> sysRoles = new HashSet<>();
                                        sysRoles.add(r);
                                        userInfo.setRoles(sysRoles);
                                    } else {
                                        userInfo.getRoles().add(r);
                                    }
                                }
                            }
                        }
                        // 描述
                        Cell notesCell = row.getCell(c++);
                        String notes = getValue(notesCell);
                        userInfo.setLoginCode(loginCode);
                        userInfo.setUserName(userName);
                        userInfo.setLoginPasswd(pwd);
                        userInfo.setEmailAdress(emailAddr);
                        userInfo.setMobileNo(mobileNo);
                        userInfo.setImsAdress(imsAdress);
                        userInfo.setNotes(notes);
                        userInfo.setIcon(defaultImgPath);
                        userInfo.setIndex(rowNum + 1);
                        userInfo.setDomainId(domainId);
                        userInfos.add(userInfo);
                    }
                }
                ImportSheetMessage saveMessage = this.saveOrUpdateUserInfoBatch(domainId, false, userInfos);
                BeanUtil.copyProperties(saveMessage, sheetMessage, "sheetName", "className");
            }
        } catch (Exception e) {
            throw BinaryUtils.transException(e, ServiceException.class);
        } finally {
            try {
                if (is != null) {
                    is.close();
                }
            } catch (Exception e2) {
            }
        }
        return FileUtil.ExcelUtil.writeSheetMessageToFile(messages, "用户导入明细");
    }

    /**
     * @param isTpl 是否只导出用户模板
     * @param cdt   导出的用户数据
     * @return
     */
    @Override
    public ResponseEntity<byte[]> exportUserToExcel(Boolean isTpl, CSysUser cdt) {
        UserExportLabel userLabel = new UserExportLabel();
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        Workbook swb = null;
        InputStream temIS = null;
        temIS = this.getClass().getResourceAsStream("/static_res/user_import_template.xlsx");
        try {
            swb = new XSSFWorkbook(temIS);
            // 创建Sheet并设置标题行
            String sheetName = CiExcelUtil.convertSheetNameSpecialChar(userLabel.getExcelName(isTpl));
            Set<String> reqCellValues = new HashSet<>();
            reqCellValues.add(userLabel.getLoginCode());
            reqCellValues.add(userLabel.getUserName());
            reqCellValues.add(userLabel.getEmail());
            Sheet sheet = FileUtil.ExcelUtil.createExcelSheetAndTitle(swb, sheetName, userLabel.getUserInfo(),
                    reqCellValues, null, null);

            if (!isTpl) {
                List<Map<String, String>> userDatas = new ArrayList<Map<String, String>>();
                if (cdt == null) {
                    cdt = new CSysUser();
                }
                if (BinaryUtils.isEmpty(cdt.getDomainId())) {
                    cdt.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
                }
                cdt.setStartId(1L);
                BoolQueryBuilder query = ESUtil.cdtToBuilder(cdt);
                long count = userSvc.countByCondition(query);
                List<SortBuilder<?>> sorts = new LinkedList<>();
                sorts.add(SortBuilders.fieldSort("createTime").order(SortOrder.DESC));
                sorts.add(SortBuilders.fieldSort("id").order(SortOrder.ASC));
                // Page<SysUser> users = userSvc.getSortListByQuery(1, new BigDecimal(count).intValue(), query, sorts);
                // List<UserInfo> ls = userSvc.fillInfo(users.getData(), false);
                List<UserInfo> userInfoList = new ArrayList<>();
                // 避免单次查询量过大，拆分为分批次查询
                if(count > 2000) {
                    int offset  = 1;
                    int batchSize  = 2000;
                    int batchSizeIncrement = 0;
                    Page<SysUser> users;
                    while (batchSizeIncrement <= count) {
                        users = userSvc.getSortListByQuery(offset , batchSize , query, sorts);
                        if(users.getData().isEmpty()){
                            break;
                        }
                        userInfoList.addAll( userSvc.fillInfo(users.getData(), false));
                        offset  += 1;
                        batchSizeIncrement += batchSize ;
                    }
                }else {
                    Page<SysUser> users = userSvc.getSortListByQuery(1, new BigDecimal(count).intValue(), query, sorts);
                    userInfoList = userSvc.fillInfo(users.getData(), false);
                }
                CSysOrg cSysOrg = new CSysOrg();
                cSysOrg.setDomainId(cdt.getDomainId());
                List<SysOrg> orgs = orgSvc.queryListByCdt(cSysOrg);
                Map<Long, SysOrg> orgMap = BinaryUtils.toObjectMap(orgs, "id");
                Map<Long, String> orgFullNameMap = new HashMap<>();
                for (SysOrg org : orgs) {
                    Long orgId = org.getId();
                    String fullName = org.getOrgName();
                    while (!BinaryUtils.isEmpty(org.getParentOrgId()) && org.getId() != cdt.getDomainId()) {
                        fullName = orgMap.get(org.getParentOrgId()).getOrgName() + "/" + fullName;
                        org = orgMap.get(org.getParentOrgId());
                    }
                    orgFullNameMap.put(orgId, fullName);
                }
                for (UserInfo info : userInfoList) {
                    if (BinaryUtils.isEmpty(info)) {
                        continue;
                    }
                    Map<String, String> map = new HashMap<String, String>();
                    map.put(userLabel.getLoginCode(), info.getLoginCode());
                    map.put(userLabel.getUserName(), info.getUserName());
                    map.put(userLabel.getLoginPasswd(), info.getLoginPasswd());
                    map.put(userLabel.getEmail(), info.getEmailAdress());
                    map.put(userLabel.getMobileNo(), info.getMobileNo());
                    map.put(userLabel.getIM(), info.getImsAdress());
                    map.put(userLabel.getDesc(), info.getNotes());
                    if (!BinaryUtils.isEmpty(info.getRoles())) {
                        List<String> roleList = info.getRoles().stream().map(SysRole::getRoleName)
                                .collect(Collectors.toList());
                        String roleNames = StringUtils.join(roleList.toArray(), ";");
                        map.put(userLabel.getRole(), roleNames);
                    }
                    if (!BinaryUtils.isEmpty(info.getOrgs())) {
                        List<String> orgNames = new ArrayList<>();
                        for (SysOrg org : info.getOrgs()) {
                            if (!BinaryUtils.isEmpty(orgFullNameMap.get(org.getId()))) {
                                orgNames.add(orgFullNameMap.get(org.getId()));
                            }
                        }
                        map.put(userLabel.getOrg(), StringUtils.join(orgNames, ";"));
                    }
                    userDatas.add(map);
                }
                // 持续写入正文
                if (userDatas != null && userDatas.size() > 0) {
                    FileUtil.ExcelUtil.writeExcelComment(swb, sheet, 1, userLabel.getUserInfo(), null, null, userDatas);
                }
            }
        } catch (Exception e) {
            throw BinaryUtils.transException(e, ServiceException.class);
        } finally {
            if (swb != null) {
                try {
                    swb.write(os);
                } catch (Exception e) {
                    throw BinaryUtils.transException(e, ServiceException.class);
                }
            }
        }
        String fileName = FileUtil.ExcelUtil.getExportFileName(userLabel.getExcelName(isTpl), ".xlsx",
                isTpl ? false : true);
        ResponseEntity<byte[]> entity = null;
        try {
            HttpHeaders headers = new HttpHeaders();
            byte[] bytes = os.toByteArray();
            headers.add("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            entity = new ResponseEntity<byte[]>(bytes, headers, HttpStatus.OK);
        } catch (Exception e) {
            throw new MessageException(e.getMessage());
        } finally {
            if (os != null) {
                try {
                    os.close();
                } catch (IOException e) {
                    throw new MessageException(e.getMessage());
                }
            }
        }
        return entity;
    }

    @Override
    public Integer deleteById(Long userId) {
        SysUser user = SysUtil.getCurrentUserInfo();
        Long uId = user.getId();
        if (userId.equals(uId)) {
            throw MessageException.i18n("BS_USER_DELETE_CUR_ERROR");
        }
        return userSvc.deleteUserById(userId);
    }

    @Override
    public Integer deleteSysUserByLoginCodeBatch(Long domainId, List<String> loginCodes) {
        Integer result = 1;
        if (BinaryUtils.isEmpty(loginCodes)) {
            return result;
        }
        domainId = domainId == null ? BaseConst.DEFAULT_DOMAIN_ID : domainId;
        loginCodes.remove("admin");
        loginCodes.remove("superadmin");
        if (loginCodes.size() > 0) {
            CSysUser cdt = new CSysUser();
            cdt.setLoginCodes(loginCodes.toArray(new String[loginCodes.size()]));
            cdt.setDomainId(domainId);
            List<SysUser> dbUsers = userSvc.getListByCdt(cdt);
            if (!BinaryUtils.isEmpty(dbUsers)) {
                List<Long> ids = dbUsers.stream().map(SysUser::getId).collect(Collectors.toList());
                result = userSvc.deleteUserByIds(ids);
            }
        }
        return result;
    }

    @Override
    public Integer saveUserRoleRlt(SaveUserRoleRltRequestDto bean) {
        Long userId = bean.getUserId();
        BinaryUtils.checkEmpty(userId, "userId");
        if (BinaryUtils.isEmpty(bean.getDomainId())) {
            bean.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
        }
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("userId", userId));
        query.must(QueryBuilders.termQuery("domainId", bean.getDomainId()));
        Integer rs = perssionCommSvc.deleteUserRoleRltByQuery(query, true);
        if (!BinaryUtils.isEmpty(bean.getRoleIds())) {
            List<SysUserRoleRlt> list = new ArrayList<>();
            bean.getRoleIds().forEach(roleId -> {
                list.add(SysUserRoleRlt.builder().userId(userId).roleId(roleId).domainId(bean.getDomainId()).build());
            });
            return perssionCommSvc.saveUserRoleRltBatch(list);
        }
        return rs;
    }

    @Override
    public ModuleNodeInfo getModuleTree(Long domainId, Long userId) {

        if (userId != null && userId == 0L){
            ModuleNodeInfo tree = getModuleNodeInfoUserIdNull();
            return tree;
        }
        return esModuleSvc.getModuleTree(domainId, userId);
    }

    @Override
    public ModuleNodeInfo getModuleTreeBySign(Long domainId, Long userId, String moduleSign) {
        if (userId != null && userId == 0L){
            ModuleNodeInfo tree = getModuleNodeInfoUserIdNull();
            return tree;
        }
        return esModuleSvc.getModuleTreeBySign(domainId, userId,moduleSign);
    }

    private static ModuleNodeInfo getModuleNodeInfoUserIdNull() {
        List<SysModule> dates = CommonFileUtil.getData("/initdata/uino_sys_superadmin_module.json", SysModule.class);
        long rootNodeId = 0L;
        ModuleNodeInfo tree = new ModuleNodeInfo();
        tree.setId(rootNodeId);
        tree.setModuleName("北京优锘科技有限公司");
        tree.setChildren(BeanUtil.converBean(dates, ModuleNodeInfo.class));
        return tree;
    }

    @Override
    public Map<String, List<SysRoleDataModuleRlt>> getDataModule(Long userId, List<String> moduleCodes) {
        return userSvc.getDataModule(userId, moduleCodes);
    }

    @Override
    public Long enshrineModule(Long domainId, Long userId, Long moduleId) {
        BinaryUtils.checkEmpty(moduleId, "moduleId");
        if (BinaryUtils.isEmpty(userId)) {
            return 1L;
        }
        domainId = domainId == null ? BaseConst.DEFAULT_DOMAIN_ID : domainId;
        SysUserModuleEnshrineRlt enshrineRlt = new SysUserModuleEnshrineRlt();
        enshrineRlt.setUserId(userId);
        enshrineRlt.setModuleId(moduleId);
        enshrineRlt.setDomainId(domainId);
        return enshrineSvc.saveOrUpdate(enshrineRlt);
    }

    @Override
    public Integer unenshrineModule(Long domainId, Long userId, Long moduleId) {
        BinaryUtils.checkEmpty(moduleId, "moduleId");
        if (BinaryUtils.isEmpty(userId)) {
            return 1;
        }
        domainId = domainId == null ? BaseConst.DEFAULT_DOMAIN_ID : domainId;
        CSysUserModuleEnshrineRlt cdt = new CSysUserModuleEnshrineRlt();
        cdt.setUserId(userId);
        cdt.setModuleId(moduleId);
        cdt.setDomainId(domainId);
        BoolQueryBuilder query = ESUtil.cdtToBuilder(cdt);
        return enshrineSvc.deleteByQuery(query, true);
    }

    @Override
    public long countByCondition(Long domainId, QueryBuilder query) {
        domainId = domainId == null ? BaseConst.DEFAULT_DOMAIN_ID : domainId;
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.must(QueryBuilders.termQuery("domainId", domainId));
        boolQuery.must(query);
        return userSvc.countByCondition(boolQuery);
    }

    /**
     * 校验用户信息格式
     *
     * @param user
     * @return 返回未翻译过的错误信息
     */
    private List<String> checkUser(SysUser user, boolean isUpdatePassword) {
        List<String> result = new ArrayList<>();
        String loginCode = user.getLoginCode();
        String userName = user.getUserName();
        String emailAdress = user.getEmailAdress();
        UserExportLabel userLabel = new UserExportLabel();
        // 登录名长度限制50，只允许字母/数字/减号(-)/句号(.)/符号(@)/下划线(_)
        String regexLogincodde = "[A-Za-z0-9\\.\\-\\_\\@\\(\\)]+$";
        if (BinaryUtils.isEmpty(loginCode)) {
            result.add("BS_FIELD_EMPTY_VAL${\"field\":\"" + userLabel.getLoginCode() + "\"}");
        } else if (loginCode.trim().length() > 50) {
            result.add("BS_USER_LOGIN_NAME_LENGTH_ERR");
        } else if (!Pattern.matches(regexLogincodde, loginCode)) {
            result.add("BS_USER_LOGIN_NAME_FORMAT_ERR");
        }
        // 用户名限制长度50
        if (BinaryUtils.isEmpty(userName)) {
            result.add("BS_FIELD_EMPTY_VAL${\"field\":\"" + userLabel.getUserName() + "\"}");
        } else if (userName.trim().length() > 50) {
            result.add("BS_USER_USER_NAME_LENGTH_ERR");
        }
        // 邮箱限制长度50，
        String regexEmail = "\\w[-\\w.+]*@([a-z0-9A-Z]+(-[a-z0-9A-Z]+)?\\.)+[a-zA-Z]{2,}$";
        if (BinaryUtils.isEmpty(emailAdress)) {
            result.add("BS_FIELD_EMPTY_VAL${\"field\":\"" + userLabel.getEmail() + "\"}");
        } else if (emailAdress.trim().length() > 50) {
            result.add("BS_USER_EMAIL_LENGTH_ERR");
        } else if (!Pattern.matches(regexEmail, emailAdress)) {
            result.add("BS_USER_EMAIL_FORMAT_ERR");
        }
        // 联系方式限制长度50，只允许输入数字和“-”
        String mobileNo = user.getMobileNo();
        String regenMobile = "[0-9\\-\\(\\)]+$";
        if (!BinaryUtils.isEmpty(mobileNo)) {
            if (mobileNo.trim().length() > 50) {
                result.add("BS_USER_MOBILE_NO_LENGTH_ERR");
            } else if (!Pattern.matches(regenMobile, mobileNo)) {
                result.add("BS_USER_MOBILE_NO_FORMAT_ERR");
            }
        }
        // 即时通讯限制长度50
        String msnAddress = user.getImsAdress();
        if (!BinaryUtils.isEmpty(msnAddress)) {
            if (msnAddress.trim().length() > 50) {
                result.add("BS_USER_MSN_ADDRESS_LENGTH_ERR");
            }
        }
        // 描述限制长度200
        String notes = user.getNotes();
        if (!BinaryUtils.isEmpty(notes) && notes.trim().length() > 200) {
            result.add("BS_USER_NOTE_LENGTH_ERR");
        }
        if (BinaryUtils.isEmpty(user.getId())) {
            if (BinaryUtils.isEmpty(user.getLoginPasswd())) {
                // 默认密码
                user.setLoginPasswd(DigestUtils.sha256Hex(initPassword));
                // user.setLoginPasswd(Encrypt.encrypt(initPassword));
            } else {
                String pwd = user.getLoginPasswd().trim();
                String reg1 = "[0-9a-f]{64}";
                // 密码是256Hex加密的
                boolean isenc = Pattern.compile(reg1).matcher(pwd).matches();
                if (isenc) {
                    user.setLoginPasswd(pwd);
                } else {
                    String pwd1 = SysUtil.EncryptDES.decryptDES(pwd);
                    if (pwd1 != null) {
                        // DES加密
                        user.setLoginPasswd(DigestUtils.sha256Hex(pwd1));
                    } else {
                        // 明文
                        String pwdReg = "^(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])(?=.*[~!@#$%^&*()_+{}|\":?><-=\\[\\];',./`~！@#￥%……&*（）——+{}|：“》？《【】、；‘，。、·])[\\da-zA-Z~!@#$%^&*()_+{}|\":?><-=\\[\\];',./`~！@#￥%……&*（）——+{}|：“》？《【】、；‘，。、·]+$";
                        if ((pwd.length() < 8 || pwd.length() > 20)) {
                            result.add("BS_USER_IMPORT_USER_PASSWORD_LENGTH_ERR");
                        } else if (!Pattern.matches(pwdReg, pwd)) {
                            result.add("BS_USER_IMPORT_USER_PASSWORD_FORMAT_ERR");
                        }
                        user.setLoginPasswd(DigestUtils.sha256Hex(pwd));
                    }
                }
            }
            if (BinaryUtils.isEmpty(user.getIcon())) {
                user.setIcon(defaultImgPath);
            }
            user.setSuperUserFlag(0);
            user.setIsUpdatePwd(1);
            user.setLockFlag(0);
            user.setStatus(1);
        } else {
            user.setLoginPasswd(null);
            user.setIsUpdatePwd(null);
        }
        String imgUrl = user.getIcon();
        if (!BinaryUtils.isEmpty(imgUrl) && imgUrl.startsWith(urlPath)) {
            user.setIcon(imgUrl.substring(imgUrl.indexOf(urlPath) + urlPath.length()));
        }
        return result;
    }

    /**
     * 把HSSFCell转为String
     *
     * @param cell
     * @return
     */
    private String getValue(Cell cell) {
        if (cell == null) {
            return "";
        }
        CellType cellType = cell.getCellType();
        if (CellType.STRING == cellType) {
            return cell.getStringCellValue();
        } else if (CellType.BOOLEAN == cellType) {
            return Boolean.valueOf(cell.getBooleanCellValue()).toString().trim();
        } else if (CellType.BLANK == cellType) {
            return "";
        } else {
            cell.setCellType(CellType.STRING);
            return cell.getStringCellValue();
        }
    }

    /**
     * 批量保存用户-角色，用户-组织关系，用户id不能为空
     *
     * @param userInfos      用户信息列表
     * @param extendOrgRoles 是否继承组织角色
     */
    private void saveRoleOrOrgDataByUserInfos(Long domainId, List<UserInfo> userInfos, boolean extendOrgRoles) {
        domainId = domainId == null ? BaseConst.DEFAULT_DOMAIN_ID : domainId;
        Long finalDomainId = domainId;
        if (BinaryUtils.isEmpty(userInfos)) {
            return;
        }
        Set<Long> userIds = userInfos.stream().filter(user -> user.getId() != null).map(UserInfo::getId)
                .collect(Collectors.toSet());
        if (!BinaryUtils.isEmpty(userIds)) {
            perssionCommSvc.deleteUserOrgRltByQuery(QueryBuilders.termsQuery("userId", userIds), true);
            perssionCommSvc.deleteUserRoleRltByQuery(QueryBuilders.termsQuery("userId", userIds), true);
        }
        // 查询组织数据
        CSysOrg cSysOrg = new CSysOrg();
        cSysOrg.setDomainId(domainId);
        List<SysOrg> orgs = orgSvc.queryListByCdt(cSysOrg);
        Map<Object, SysOrg> orgMap = BinaryUtils.toObjectMap(orgs, "orgName");
        // 查询角色数据
        CSysRole cSysRole = new CSysRole();
        cSysRole.setDomainId(domainId);
        List<SysRole> roles = roleSvc.getListByCdt(cSysRole);
        Map<String, SysRole> roleMap = BinaryUtils.toObjectMap(roles, "roleName");
        // 组装用户-角色/组织数据，批量保存
        List<SysUserRoleRlt> userRoleRlts = new ArrayList<>();
        List<SysUserOrgRlt> userOrgRlts = new ArrayList<>();
        for (UserInfo userInfo : userInfos) {
            Long userId = userInfo.getId();
            userInfo.setDomainId(domainId);
            if (userId == null) {
                continue;
            }
            // 组装用户组织数据
            Set<SysOrg> uOrgs = userInfo.getOrgs();
            List<SysUserOrgRlt> temp = new ArrayList<>();
            if (!BinaryUtils.isEmpty(uOrgs)) {
                Set<Long> oIds = uOrgs.stream().map(SysOrg::getId).filter(x -> x != null).collect(Collectors.toSet());
                if (!BinaryUtils.isEmpty(oIds)) {
                    // 用于页面操作，有id时直接存储
                    oIds.forEach(oId -> temp.add(SysUserOrgRlt.builder().userId(userId).orgId(oId).domainId(finalDomainId).build()));
                } else {
                    // 用于导入时根据组织结构存储
                    Set<String> oNames = uOrgs.stream().map(SysOrg::getOrgName).filter(x -> x != null)
                            .collect(Collectors.toSet());
                    if (!BinaryUtils.isEmpty(oNames)) {
                        oNames.forEach(oName -> {
                            SysOrg dbOrg = orgMap.get(oName);
                            if (dbOrg != null) {
                                temp.add(SysUserOrgRlt.builder().userId(userId).orgId(dbOrg.getId()).domainId(finalDomainId).build());
                            }
                        });
                    }
                }
            }
            if (BinaryUtils.isEmpty(temp)) {
                // 默认属于根组织
                userOrgRlts.add(SysUserOrgRlt.builder().userId(userId).orgId(finalDomainId).domainId(domainId).build());
            } else {
                userOrgRlts.addAll(temp);
            }
            // 组装用户角色数据
            Set<SysRole> uRoles = userInfo.getRoles();
            if (uRoles != null) {
                Set<Long> rIds = uRoles.stream().map(SysRole::getId).filter(x -> x != null).collect(Collectors.toSet());
                if (!BinaryUtils.isEmpty(rIds)) {
                    rIds.forEach(rId -> userRoleRlts.add(SysUserRoleRlt.builder().userId(userId).roleId(rId).domainId(finalDomainId).build()));
                } else {
                    // 用于导入时根据角色名称关联
                    Set<String> rNames = uRoles.stream().map(SysRole::getRoleName).filter(x -> x != null)
                            .collect(Collectors.toSet());
                    if (!BinaryUtils.isEmpty(rNames)) {
                        rNames.forEach(rName -> {
                            SysRole dbRole = roleMap.get(rName);
                            if (dbRole != null) {
                                userRoleRlts
                                        .add(SysUserRoleRlt.builder().userId(userId).roleId(dbRole.getId()).domainId(finalDomainId).build());
                            }
                        });
                    }
                }
            }
        }
        if (!BinaryUtils.isEmpty(userOrgRlts)) {
            perssionCommSvc.saveUseOrgRltBatch(userOrgRlts);
        }
        if (!BinaryUtils.isEmpty(userRoleRlts)) {
            perssionCommSvc.saveUserRoleRltBatch(userRoleRlts);
        }
    }

    @Override
    public int recordLoginFailNum(Long domainId, String loginName, boolean success) {
        if (loginName == null || "".equals(loginName.trim())) {
            return 0;
        }
        CSysUser query = new CSysUser();
        query.setLoginCodeEqual(loginName);
        query.setDomainId(domainId == null ? BaseConst.DEFAULT_DOMAIN_ID : domainId);
        List<SysUser> users = this.getSysUserByCdt(query);
        if (users != null && users.size() > 0) {
            SysUser user = users.get(0);
            int tryNum;
            if (success) {
                tryNum = 0;
                SysLoginLog loginLog = logSvc.addLoginLog(user.getId());
                user.setLastLoginLogId(loginLog.getId());
                user.setLastLoginTime(System.currentTimeMillis());
            } else {
                tryNum = user.getTryTimes() == null ? 1 : (user.getTryTimes().intValue() + 1);
            }
            user.setTryTimes(tryNum);
            if (tryNum >= loginFailLockNum) {
                log.debug("用户【{}】尝试登陆超过【{}】次，锁定账户", loginName, loginFailLockNum);
                // 同一账户尝试超过一定次数后锁定账户
                user.setLockedTime(System.currentTimeMillis());
                user.setLockFlag(1);
            }
            userSvc.saveOrUpdate(user);
            return tryNum;
        } else {
            return 0;
        }
    }

    @Override
    public CurrentUserInfo getCurrentUser() {
        SysUser currentUser = SysUtil.getCurrentUserInfo();
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("loginCode.keyword", currentUser.getLoginCode()));
        query.must(QueryBuilders.termQuery("domainId", currentUser.getDomainId()));
        List<SysUser> users = userSvc.getListByQuery(query);
        Assert.isTrue(!BinaryUtils.isEmpty(users), "用户[" + currentUser.getLoginCode() + "]不存在");
        SysUser user = users.get(0);
        return CurrentUserInfo.builder().id(user.getId()).icon(this.urlPath + user.getIcon())
                .domainId(user.getDomainId()).loginCode(user.getLoginCode()).userCode(user.getLoginCode())
                .userName(user.getUserName()).language("ZHC")
                .lastLoginTime(user.getLastLoginTime()).isUpdatePwd(user.getIsUpdatePwd()).build();
    }

    @Override
    public int unLockUsersByDuration(Long durationSecond) {
        int unLockUserNum = 0;
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("lockFlag", 1));
        query.must(QueryBuilders.rangeQuery("lockedTime").lte(System.currentTimeMillis() - durationSecond * 1000));
        List<SysUser> needUnlockUsers = userSvc.getListByQuery(query);
        if (needUnlockUsers != null && needUnlockUsers.size() > 0) {
            needUnlockUsers.forEach(user -> {
                user.setLockFlag(0);
                user.setLockedTime(null);
                user.setTryTimes(0);
            });
            unLockUserNum = userSvc.saveOrUpdateBatch(needUnlockUsers) == 1 ? needUnlockUsers.size() : 0;
        }
        return unLockUserNum;
    }

    @Override
    public int getLoginFailLockNum() {
        // TODO Auto-generated method stub
        return loginFailLockNum;
    }

    @Override
    public long getUserLockDuration() {
        // TODO Auto-generated method stub
        return userLockDuration * 1000L;
    }

    @Override
    public void verifyAuth() {
        CurrentUserInfo currentUser = this.getCurrentUser();
        // 获取用户所属角色有权限的模块
        List<SysRole> roles = roleSvc.getListByUserId(currentUser.getId());
        if (!BinaryUtils.isEmpty(roles)) {
            // 找出这些角色有的模块权限加进去f
            Set<Long> roleIds = roles.stream().map(SysRole::getId).collect(Collectors.toSet());
            List<SysModule> roleModules = esModuleSvc.getListByRoleIds(roleIds);
            Optional<SysModule> module = roleModules.stream().filter(e -> userModuleSign.equals(e.getModuleSign())).findFirst();
            if (module.isPresent()) {
                return;
            }
        }
        throw MessageException.i18n("BS_NOT_AUTH", new HashMap<String, String>());
    }

    @Override
    public List<SysModuleOutSide> getModulesByLoginCode(Long domainId, String loginCode) {
        List<SysModuleOutSide> sysModuleOutSideList = new ArrayList<>(32);
        domainId = domainId == null ? BaseConst.DEFAULT_DOMAIN_ID : domainId;
        if (null != loginCode && !"".equals(loginCode.trim())) {
            CSysUser cSysUser = new CSysUser();
            cSysUser.setLoginCode(loginCode);
            cSysUser.setDomainId(domainId);
            // query user info by login code
            BoolQueryBuilder query = QueryBuilders.boolQuery();
            query.must(QueryBuilders.termQuery("loginCode.keyword", loginCode));
            query.must(QueryBuilders.termQuery("domainId", domainId));
            List<UserInfo> userInfos = userSvc.getUserInfoListByQuery(query, true);
            if (!userInfos.isEmpty()) {
                UserInfo userInfo = userInfos.get(0);
                List<SysModule> sysModules = esModuleSvc.getModuleAuths(userInfo.getId());
                sysModules.forEach(sysModule -> {
                    try {
                        sysModuleOutSideList.add(SysModuleOutSide.transToSysModuleOutSide(sysModule));
                    } catch (Exception ignored) {
                    }
                });
            } else {
                throw new IllegalArgumentException("invalid login code");
            }
        } else {
            throw new IllegalArgumentException("invalid login code");
        }
        return sysModuleOutSideList;
    }

    @Override
    public Map<Long, Map<String, Integer>> getDataPermissionByUser(CAuthDataModuleBean cAuthDataModuleBean) {
        List<SysRole> currentUserBelongToRoles = roleSvc.getListByUserId(cAuthDataModuleBean.getUserId());
        Set<Long> userRoleList = currentUserBelongToRoles.stream().map(SysRole::getId).collect(Collectors.toSet());
        if (BinaryUtils.isEmpty(cAuthDataModuleBean.getDomainId())) {
            cAuthDataModuleBean.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
        }
        List<String> dataValues = new ArrayList<>();
        cAuthDataModuleBean.getModuleIds().forEach(data -> {
            dataValues.add(String.valueOf(data));
        });
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termsQuery("dataValue.keyword", dataValues));
        query.must(QueryBuilders.termsQuery("roleId", userRoleList));
        query.must(QueryBuilders.termQuery("domainId", cAuthDataModuleBean.getDomainId()));
        List<SysRoleDataModuleRlt> roleDataModuleRlts = rdRltSvc.getListByQuery(query);
        return this.mergeDataPermissions(roleDataModuleRlts);
    }

    @Override
    public List<SysUser> getUserByRoleName(String roleName) {
        SearchKeywordBean searchKeywordBean = new SearchKeywordBean();
        searchKeywordBean.setPageNum(1);
        searchKeywordBean.setPageSize(10000);
        Page<SysRole> rolePage = roleService.getRolePageByQuery(searchKeywordBean);
        List<SysRole> sysRoleList = rolePage.getData();
        Assert.isTrue(sysRoleList != null,"没有对应的角色");
        Assert.isTrue(! (sysRoleList.size() <=0),"没有对应的角色");
        List<SysRole> searchRole = sysRoleList.stream().filter(item -> item.getRoleName().equals(roleName)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(searchRole)) {
            return new ArrayList<>();
        }
        SysRole sysRole = searchRole.get(0);
        List<SysUser> sysUserList = getUserByRoleId(sysRole.getId());
        return sysUserList;
    }

    @Override
    public List<SysUser> getUserInfoByName(String userName) {
        BoolQueryBuilder bool = QueryBuilders.boolQuery();
        bool.should(QueryBuilders.wildcardQuery("userName.keyword","*" + userName + "*"));
        bool.should(QueryBuilders.wildcardQuery("loginCode.keyword","*" + userName + "*"));
        List<SysUser> userList = userSvc.getListByQuery(bool);
        // 将系统管理员过滤
        return userList.stream().filter(user -> user.getId().longValue() != 0L).collect(Collectors.toList());
    }

    @Override
    public Page<SysUser> findUserInfoByNameForPage(Integer pageNum, Integer pageSize, String userName) {
        BoolQueryBuilder bool = QueryBuilders.boolQuery();
        bool.should(QueryBuilders.wildcardQuery("userName.keyword","*" + userName + "*"));
        bool.should(QueryBuilders.wildcardQuery("loginCode.keyword","*" + userName + "*"));
        // 将系统管理员过滤
        bool.mustNot(QueryBuilders.termQuery("id", 0L));
        return userSvc.getListByQuery(pageNum, pageSize, bool);
    }

    @Override
    public void initUserCiData(String loginCode) {
        if(initUserCiData){
            log.info("开始初始化当前用户使用数据userCode:{}", loginCode);
            //不配置获取全部ci
            log.info("initCiClassList:{}",JSONObject.toJSONString(initCiClassList));
            if(CollectionUtils.isEmpty(initCiClassList)||"".equals(initCiClassList.get(0))){
                List<ESCIClassInfo> listByQueryScroll = esciClassSvc.getListByQueryScroll(QueryBuilders.matchAllQuery());
                initCiClassList = listByQueryScroll.stream().map(ESCIClassInfo::getClassStdCode).collect(Collectors.toList());
            }
            BoolQueryBuilder ciClassQueryBuilder = QueryBuilders.boolQuery();
            ciClassQueryBuilder.must(QueryBuilders.termsQuery("classStdCode.keyword",initCiClassList.stream().map(String::toUpperCase).collect(Collectors.toList())));
            List<ESCIClassInfo> listCiClass = esciClassSvc.getListByQueryScroll(ciClassQueryBuilder);
            log.info("同步分类数量:{}", listCiClass.size());
            //执行用户ci初始化
            if(!CollectionUtils.isEmpty(listCiClass)){
                LibTypeUtil.execute(()->{
                    //先判断该用户是否已经存在数据，同步前将该用户之前的私有库数据清掉（假如有的话）
                    List<Long> ciClassIds = listCiClass.stream().map(ESCIClassInfo::getId).collect(Collectors.toList());
                    BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
                    queryBuilder.must(QueryBuilders.termQuery("ownerCode.keyword",loginCode));
                    queryBuilder.must(QueryBuilders.termsQuery("classId",ciClassIds));
                    // esCiSvc.deleteByQuery(queryBuilder, Boolean.FALSE);
                    //没有初始化数据则从源用户获取然后赋值给当前用户
                    BoolQueryBuilder sourceUserQueryBuilder = QueryBuilders.boolQuery();
                    sourceUserQueryBuilder.must(QueryBuilders.termQuery("ownerCode.keyword",initDataSourceUser));
                    sourceUserQueryBuilder.must(QueryBuilders.termsQuery("classId",ciClassIds));
                    List<ESCIInfo> sourceUserCiList = esCiSvc.getListByQueryScroll(sourceUserQueryBuilder);
                    //记录下复制的数据与admin用户的id对应关系
                    HashMap<Long, Long> idMapConvert = new HashMap<>();
                    for (ESCIInfo esciInfo : sourceUserCiList) {
                        esciInfo.setLocalVersion(0);
                        esciInfo.setOwnerCode(loginCode);
                        Long oldCiId = esciInfo.getId();
                        long uuid = ESUtil.getUUID();
                        idMapConvert.put(oldCiId,uuid);
                        esciInfo.setId(uuid);
                        esciInfo.setCreator(loginCode);
                        esciInfo.setModifier(loginCode);
                    }
                    log.info("同步创建CI数据数量:{}", sourceUserCiList.size());
                    esCiSvc.saveOrUpdateBatch(sourceUserCiList,Boolean.FALSE);
                    //同步关系
                    //同步前将该用户之前的私有库数据清掉（假如有的话）
                    BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
                    boolQueryBuilder.must(QueryBuilders.termQuery("ownerCode.keyword",loginCode));
                    // esciRltSvc.deleteByQuery(boolQueryBuilder,Boolean.FALSE);
                    ESRltSearchBean esRltSearchBean = new ESRltSearchBean();
                    esRltSearchBean.setOwnerCode(initDataSourceUser);
                    List<ESCIRltInfo> esciRltInfos = esciRltSvc.searchESRltByScroll(esRltSearchBean);
                    Iterator<ESCIRltInfo> iterator = esciRltInfos.iterator();
                    while (iterator.hasNext()) {
                        ESCIRltInfo esciRltInfo = iterator.next();
                        Long newSourceId = idMapConvert.get(esciRltInfo.getSourceCiId());
                        Long newTargetId = idMapConvert.get(esciRltInfo.getTargetCiId());
                        if (newSourceId == null || newTargetId == null) {
                            iterator.remove();
                        } else {
                            long newRltId = ESUtil.getUUID();
                            esciRltInfo.setId(newRltId);
                            esciRltInfo.setOwnerCode(loginCode);
                            esciRltInfo.setCreator(loginCode);
                            esciRltInfo.setCiCode(String.valueOf(newRltId));

                            esciRltInfo.setSourceCiId(newSourceId);
                            esciRltInfo.setSourceCiCode(String.valueOf(newSourceId));
                            esciRltInfo.setTargetCiId(newTargetId);
                            esciRltInfo.setTargetCiCode(String.valueOf(newTargetId));
                            String oldRltCode = esciRltInfo.getCiCode();
                            esciRltInfo.setUniqueCode(esciRltInfo.getUniqueCode().replaceAll(oldRltCode, String.valueOf(newRltId)));
                        }
                    }
                    esciRltSvc.saveOrUpdateBatch(esciRltInfos);
                },LibType.PRIVATE);
            }
            log.info("私有库数据完成初始化");
        }
    }

    private void verifyUserCount(int saveCount) {
        if(!licenseEnable){
            return;
        }
        BaseLicenseAuth baseLicenseAuth = esLicenseAuthSvc.selectOneRow();
        Long licenseCount = baseLicenseAuth.getAuthCustom1();
        int size = userSvc.getListByQueryScroll(QueryBuilders.matchAllQuery()).size();
        //因为superadmin在用户管理页面不会被统计，故应该多一个
        if ((size + saveCount) > licenseCount + 1) {
            throw MessageException.i18n("USER_COUNT_BEYOND");
        }
    }



    /**
     * merge data permissions owned by users
     *
     * @param roleDataModuleRlts Module permissions corresponding to all roles owned by the user
     * @return {@link Map}
     */
    private Map<Long, Map<String, Integer>> mergeDataPermissions(List<SysRoleDataModuleRlt> roleDataModuleRlts) {
        TypeReference<HashMap<String, Object>> MAP_TYPE = new TypeReference<HashMap<String, Object>>() {
        };
        Map<Long, Map<String, Integer>> userDataPermission = new HashMap<>(6);

        roleDataModuleRlts.forEach(roleDataModuleRlt -> {
            Long moduleId = Long.parseLong(roleDataModuleRlt.getDataValue());
            Map<String, Integer> modulePermission = new HashMap<>(6);
            HashMap<String, Object> roleDataModuleRltMap = JSON.parseObject(JSON.toJSONString(roleDataModuleRlt), MAP_TYPE);
            for (Map.Entry<String, Object> entry : roleDataModuleRltMap.entrySet()) {
                String permissionKey = entry.getKey();
                if (extendedPermissionsMapKey.contains(permissionKey)) {
                    if (!"extendedPermissions".equals(permissionKey)) {
                        Integer permissionValue = (Integer) entry.getValue();
                        Integer existPermission = modulePermission.get(permissionKey);
                        if (null == existPermission || existPermission == 0) {
                            modulePermission.put(permissionKey, permissionValue);
                        }
                    } else {
                        List<ExtendedPermission> extendedPermissions = JSON.parseObject(JSON.toJSONString(entry.getValue()), new TypeReference<List<ExtendedPermission>>() {
                        });

                        if (null != extendedPermissions && !extendedPermissions.isEmpty()) {
                            extendedPermissions.forEach(extendedPermission -> {
                                String extendPermissionKey = extendedPermission.getKey();
                                Integer extendPermissionValue = extendedPermission.getValue();

                                Integer existExtendPermission = modulePermission.get(extendPermissionKey);
                                if (null == existExtendPermission || existExtendPermission == 0) {
                                    modulePermission.put(extendPermissionKey, extendPermissionValue);
                                }
                            });
                        }
                    }
                }
                userDataPermission.put(moduleId, modulePermission);
            }
        });
        return userDataPermission;
    }

    @Override
    public List<SysUser> getUserByRoleNameAndCount(String roleName, Integer count) {
        if (count == null) {
            count = 100;
        }
        SearchKeywordBean searchKeywordBean = new SearchKeywordBean();
        searchKeywordBean.setPageNum(1);
        searchKeywordBean.setPageSize(count);
        Page<SysRole> rolePage = roleService.getRolePageByQuery(searchKeywordBean);
        List<SysRole> sysRoleList = rolePage.getData();
        Assert.isTrue(sysRoleList != null,"没有对应的角色");
        Assert.isTrue(! (sysRoleList.size() <=0),"没有对应的角色");
        List<SysRole> searchRole = sysRoleList.stream().filter(item -> item.getRoleName().equals(roleName)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(searchRole)) {
            return new ArrayList<>();
        }
        SysRole sysRole = searchRole.get(0);
        List<SysUser> sysUserList = getUserByRoleId(sysRole.getId());
        return sysUserList;
    }

    @Override
    public Map<String, String> getNameByCodes(Collection<String> loginCodes) {
        CSysUser userCdt = new CSysUser();
        userCdt.setLoginCodes(loginCodes.toArray(new String[]{}));
        List<SysUser> userList = this.getSysUserByCdt(userCdt);
        if (CollectionUtils.isEmpty(userList)){
            return Collections.emptyMap();
        }
        return userList.stream().collect(Collectors.toMap(SysUser::getLoginCode, SysUser::getUserName));
    }

    @Override
    public Page<SysUser> queryPageByRoleName(String roleName, String loginName, int pageNum, int pageSize) {
        if (BinaryUtils.isEmpty(roleName)) {
            return new Page<>();
        }
        List<SysRole> rolesByQuery = roleService.getRolesByQuery(QueryBuilders.termQuery("roleName.keyword", roleName));
        if (CollectionUtils.isEmpty(rolesByQuery)) {
            return new Page<>();
        }

        Set<Long> userIdList = new HashSet<>();
        Long roleId = rolesByQuery.get(0).getId();
        List<SysUserRoleRlt> userRoleRltList = userRoleRltSvc.getListByQueryScroll(QueryBuilders.termQuery("roleId", roleId));
        if (!CollectionUtils.isEmpty(userRoleRltList)) {
            userIdList.addAll(userRoleRltList.stream().map(SysUserRoleRlt::getUserId).collect(Collectors.toList()));
        }

        List<SysOrgRoleRlt> orgRoleRltList = orgRoleRltSvc.getListByQueryScroll(QueryBuilders.termQuery("roleId", roleId));
        Set<Long> orgIds = orgRoleRltList.stream().map(SysOrgRoleRlt::getOrgId).collect(Collectors.toSet());
        List<SysUserOrgRlt> userOrgRltList = userOrgRltSvc.getListByQueryScroll(QueryBuilders.termsQuery("orgId", orgIds));
        if (!CollectionUtils.isEmpty(userOrgRltList)) {
            userIdList.addAll(userOrgRltList.stream().map(SysUserOrgRlt::getUserId).collect(Collectors.toList()));
        }

        // 根据userIdList获取用户信息
        if (!CollectionUtils.isEmpty(userIdList)) {
            BoolQueryBuilder bool = QueryBuilders.boolQuery();
            bool.must(QueryBuilders.termsQuery("id", userIdList));
            if (!BinaryUtils.isEmpty(loginName)) {
                bool.must(QueryBuilders.termsQuery("id", userIdList));
                BoolQueryBuilder oneBool = QueryBuilders.boolQuery();
                oneBool.should(QueryBuilders.wildcardQuery("userName.keyword","*" + loginName + "*"));
                oneBool.should(QueryBuilders.wildcardQuery("loginCode.keyword","*" + loginName + "*"));
                bool.must(oneBool);
            }
            Page<SysUser> userPage = userSvc.getListByQuery(pageNum, pageSize, bool);
            userPage.getData().forEach(user -> user.clearSensitive());
            return userPage;
        } else {
            return new Page<>();
        }
    }
}
