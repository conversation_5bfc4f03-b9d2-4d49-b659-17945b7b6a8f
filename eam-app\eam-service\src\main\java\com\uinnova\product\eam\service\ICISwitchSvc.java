package com.uinnova.product.eam.service;

import com.binary.framework.web.RemoteResult;
import com.binary.jdbc.Page;
import com.uinnova.product.eam.model.CiInfoQueryVo;
import com.uinnova.product.eam.model.CiQueryCdtExtend;
import com.uinnova.product.eam.model.bm.VcCiQ;
import com.uinnova.product.eam.model.bm.VcCiSearchPage;
import com.uinnova.product.eam.model.dto.DiagramSelectParamDto;
import com.uinnova.product.eam.model.dto.LaneDragCiDto;
import com.uinnova.product.eam.model.vo.DiagramSelectVo;
import com.uinnova.product.eam.model.vo.ESCISearchBeanVO;
import com.uinnova.product.eam.model.vo.ESCiReleAssetSearchBean;
import com.uinnova.product.eam.model.vo.ExportCiVO;
import com.uinnova.product.vmdb.comm.model.ci.CCcCi;
import com.uinnova.product.vmdb.comm.model.ci.CcCi;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CiClassSaveInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CiGroupPage;
import com.uinnova.product.vmdb.provider.search.bean.CcCiSearchPage;
import com.uino.api.client.cmdb.ICIApiSvc;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.base.LibType;
import com.uino.bean.cmdb.base.SaveBatchCIContext;
import com.uino.bean.cmdb.base.SaveType;
import com.uino.bean.cmdb.business.*;
import com.uino.bean.cmdb.query.ESAttrAggBean;
import com.uino.bean.cmdb.query.ESCISearchBean;
import com.uino.bean.permission.base.SysUser;
import com.uino.service.cmdb.microservice.ICISvc;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.search.sort.SortBuilder;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 扩展ICIApiSvc，用于扩展带libType参数的方法
 */
public interface ICISwitchSvc {

    ICIApiSvc getCiApiSvc();

    ICISvc getCiSvc(LibType libType);

    CcCiInfo getCiInfoById(Long id, LibType libType);

    /**
     * 根据ciCode查询ci
     *
     * @param ciCode    ci标识
     * @param ownerCode 用户标识
     * @param libType   库
     * @return ci
     */
    CcCiInfo getCiByCode(String ciCode, String ownerCode, LibType libType);

    List<ESCIInfo> getCiByIds(List<Long> ids, String ownerCode, LibType libType);

    /**
     * 通过ciCodes获取ci信息
     * @param ciCodes 对象code集合
     * @param ownerCode libType=PRIVATE时不可为空
     * @param libType 库
     * @return ci集合
     */
    List<ESCIInfo> getCiByCodes(List<String> ciCodes, String ownerCode, LibType libType);

    /**
     * 通过ciCodes获取ci信息（加入数据权限控制）
     * @param ciCodes 对象code集合
     * @param ownerCode libType=PRIVATE时不可为空
     * @param libType 库
     * @return ci集合
     */
    List<ESCIInfo> getCiByPermission(List<String> ciCodes, String ownerCode, LibType libType);

    /**
     * 通过ciCodes获取ci信息
     * @param classIds 分类id
     * @param ownerCode libType=PRIVATE时不可为空
     * @param libType 库
     * @return ci集合
     */
    List<ESCIInfo> getCiByClassIds(List<Long> classIds, String ownerCode, LibType libType);

    /**
     * 通过业务主键获取ci信息
     * @param primaryKeys 业务主键集合
     * @param ownerCode libType=PRIVATE时不可为空
     * @param libType 库
     * @return ci集合
     */
    List<ESCIInfo> getCiByPrimaryKeys(Collection<String> primaryKeys, String ownerCode, LibType libType);

    CiGroupPage queryPageByIndex(Integer pageNum, Integer pageSize, CiQueryCdtExtend cdt, Boolean hasClass, LibType libType);

    CiGroupPage queryPageBySearchBean(ESCISearchBean bean, Boolean hasClass, LibType libType);

    List<CcCi> queryCiList(Long domainId, CCcCi cdt, String orders, Boolean isAsc, LibType libType);

    List<ESCIInfo> queryESCIInfoList(Long domainId, CCcCi cdt, String orders, Boolean isAsc, LibType libType);

    List<CcCiInfo> queryCiInfoList(Long domainId, CCcCi cdt, String orders, Boolean isAsc, Boolean hasClass, LibType libType);

    Page<CcCiInfo> queryCiInfoPage(Long domainId, Integer pageNum, Integer pageSize, CCcCi cdt,
                                   String orders, Boolean isAsc, Boolean hasClass, LibType libType);

    CcCiSearchPage searchCIByCdt(int pageNum, int pageSize, CCcCi bean, LibType libType);

    Page<ESCIInfo> searchESCIByBean(ESCISearchBean bean, LibType libType);

    CcCiSearchPage searchCIByBean(ESCISearchBean bean, LibType libType);

    Long saveOrUpdateCI(CcCiInfo ciInfo, LibType libType);

    Long saveOrUpdateCI(CcCiInfo ciInfo, LibType libType, SaveType saveType);

    Long saveOrUpdateExtra(CcCiInfo ciInfo, LibType libType);

    Map<String, ? extends SaveBatchCIContext> saveOrUpdateBatchCI(List<ESCIInfo> ciList, List<Long> classIds, String ownerCode, String loginCode, LibType libType);

    Integer updateESCIInfoBatch(List<ESCIInfo> esCiInfoList, LibType libType);

    Integer removeById(Long id, Long sourceId, LibType libType);

    Integer removeByIds(List<Long> ciIds, Long sourceId, LibType libType);

    Integer removeByClassId(Long classId, Long sourceId, LibType libType);

    ImportSheetMessage saveOrUpdateCiBatch(Long domainId, CiClassSaveInfo saveInfo, LibType libType);

    ResponseEntity<byte[]> exportCiOrClass(ExportCiDto exportDto, LibType libType);

    ImportResultMessage importCiByCiClsIds(MultipartFile file, Long classId, LibType libType);

    ImportExcelMessage importCiExcel(MultipartFile file, LibType libType);

    ImportResultMessage importCiByClassBatch(CiExcelInfoDto excelInfoDto, LibType libType);

    Map<Long, Long> countCiNumGroupClsByQuery(ESCISearchBean bean, LibType libType);

    Page<String> getAttrValuesBySearchBean(ESAttrAggBean searchBean, LibType libType);

    Page<ESCIInfo> getESCIInfoPageByQuery(Long domainId,int pageNum, int pageSize, QueryBuilder query, List<SortBuilder<?>> sorts, Boolean isHighLight, LibType libType);

    /**
     * 获取ci最大历史版本号
     *
     * @param libType
     * @return
     */
    Map<String, Long> getCICodeMaxVersion(List<String> ciCodes, LibType libType);

    /**
     * 通过业务主键获取ci最大历史版本号
     * @param primaryKeys 业务主键
     * @return
     */
    Map<String, Long> getMaxVersionByPrimaryKey(List<String> primaryKeys, LibType libType);

    /**
     * 查询对象分类树结构
     *
     * @param display 隐藏领域
     * @param libType 库
     * @return 分类
     */
    List<ClassNodeInfo> getClassTree(Boolean display, LibType libType);

    /**
     * 验证ci在私有库/共享库是否已存在
     *
     * @param ids
     * @param libType
     * @param ownerCode
     * @return
     */
    Integer handleCiExist(List<Long> ids, LibType libType, String ownerCode);

    /**
     * @param :
     * <AUTHOR> wcl
     * @Date : 2021/11/29 15:50
     * @description : 模型资产管理-删除单独用户某一分类下的库中数据
     * @Return :
     **/
    Integer removeByOwnerCodeAndClassId(LibType libType,Long classId,String ownerCode);


    /**
     * ci用户列表
     *
     * @param bean
     * @return
     */
    List<SysUser> findCiSwitchUserList(ESCISearchBean bean, Boolean hasClass);

    List<VcCiSearchPage> queryPageByAllIndex(Integer pageNum, Integer pageSize, CiQueryCdtExtend cdt, String orders, VcCiQ[] vcCiQS);

    Map<String, ? extends SaveBatchCIContext> copyCiListByIds(List<ESCIInfo> ciList, String ownerCode, LibType libType, String postfix);

    /**
     * 通过classId查询当前分类字段的数据字典/关联属性数据
     * @param classId
     * @return
     */
    Map<Long, List<String>> queryEnumDataByClassId(Long classId);

    /**
     *  通过设计库code获取对应的私有库资产信息 存在直接查询 不存在创建
     * @param ciCode
     */
    CcCiInfo getPrivateCIInfoByDesginCode(String ciCode);

    /**
     * 元模型删除分类前校验该分类下是否存在数据
     * @param cdt
     * @return
     */
    Map<String, Long> existDataByClassId(CiQueryCdtExtend cdt);

    /**
     * 对象管理列表
     * @return
     */
    List<ClassNodeInfo> findClassManagerList(Long classId);

    /**
     * 在画布中查询绑定制品中的关系目标端元素
     * @param paramDto 查询参数
     * @return list集合
     */
    List<DiagramSelectVo> selectClassByArtifact(DiagramSelectParamDto paramDto);

    /**
     * 泳道图中拖拽ci 创建关系校验接口
     * @param ciDto 一个泳道内的ciCode和拖拽的ciCode
     * @return 是否可拖拽；
     */
    Boolean judgeLaneDragCi(LaneDragCiDto ciDto);




    /**
     * 查询视图中超文本链接-ci绑定的当前目录的主视图
     * @param ciCode ciCode
     * @param diagramId 当前视图加密id
     * @param dirType 目录类型
     * @return 跳转链接的视图信息
     */
    RemoteResult selectRelationDiagramByCi(String ciCode,String diagramId,Integer dirType,LibType libType);

    boolean findNonCompliance(Long classId, LibType libType);

    CiGroupPage queryPageBySearchBeanVO(ESCISearchBeanVO bean, boolean hasClass, LibType libType);

    ResponseEntity<byte[]> exportCiOrClassByConditions(ExportCiDto exportDto, LibType libType);

    /**
     * 导出Ci分类信息控制导出属性字段
     * @param exportDto
     * @param libType
     * @return
     */
    ResponseEntity<byte[]> exportCiClassAndAttrs(ExportCiVO exportDto, LibType libType);

    /**
     * 查询分类下ci数量
     * @param libType 库
     * @return 分类标识：对象数量
     */
    Map<String, Long> getCiCountMap(Collection<String> classCodes, String ownerCode, LibType libType);

    /**
     * 查询分类下ci数量
     * @param libType 库
     * @return 分类标识：对象数量
     */
    Map<Long, Long> getCiCountMap(ESCISearchBean bean, LibType libType);


    CiGroupPage queryReleCiInfo(ESCiReleAssetSearchBean bean, LibType libType);

    /**
     * 根据code查询ci信息
     * @param query 查询条件
     * @return ci
     */
    CcCiInfo queryByCode(CiInfoQueryVo query);
}
