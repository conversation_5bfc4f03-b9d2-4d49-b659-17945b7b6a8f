package com.uino.org.mvc;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.Set;

import org.elasticsearch.index.query.QueryBuilder;
import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import com.alibaba.fastjson.JSON;
import com.binary.framework.web.RemoteResult;
import com.uino.StartBaseWebAppliaction;
import com.uino.dao.permission.ESOrgSvc;
import com.uino.dao.permission.rlt.ESOrgRoleRltSvc;
import com.uino.dao.permission.rlt.ESPerssionCommSvc;
import com.uino.dao.permission.rlt.ESUserOrgRltSvc;

import lombok.extern.slf4j.Slf4j;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = { StartBaseWebAppliaction.class }, webEnvironment = WebEnvironment.RANDOM_PORT)
@Slf4j
@ActiveProfiles("provider-local")
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class DeleteByIdsTest {
	@Autowired
	private TestRestTemplate restTemplate;
	@MockBean
	private ESOrgSvc esOrgSvc;
	@MockBean
	private ESOrgRoleRltSvc esOrgRoleRltSvc;
	@MockBean
	private ESUserOrgRltSvc esUserOrgRltSvc;
    @MockBean
    private ESPerssionCommSvc perssionCommSvc;
	private static String testUrl;

	@BeforeClass
	public static void initCls() {
		DeleteByIdsTest.testUrl = "/permission/org/deleteByIds";
	}

	@Before
	public void start() {
		Mockito.when(esOrgSvc.deleteByIds(Mockito.anyList())).thenReturn(0);
		Mockito.when(esOrgRoleRltSvc.deleteByQuery(Mockito.any(QueryBuilder.class), Mockito.anyBoolean()))
				.thenReturn(0);
		Mockito.when(esUserOrgRltSvc.deleteByQuery(Mockito.any(QueryBuilder.class), Mockito.anyBoolean()))
				.thenReturn(0);
        Mockito.when(perssionCommSvc.getUserOrgRltByOrgIds(Mockito.any())).thenReturn(new ArrayList<>());
        Mockito.when(perssionCommSvc.deleteOrgRoleRltByQuery(Mockito.any(), Mockito.anyBoolean())).thenReturn(1);
	}

	@Test
	public void test01() {
		Set<Long> requestBody = new HashSet<>();
		requestBody.add(1L);
		requestBody.add(2L);
		String responseBodyStr = restTemplate.postForObject(testUrl, requestBody, String.class);
		RemoteResult responseBody = JSON.parseObject(responseBodyStr, RemoteResult.class);
		Assert.assertTrue(responseBody.isSuccess());
	}

	@Test
	public void test02() {
		Set<Long> requestBody = new HashSet<>();
		String responseBodyStr = restTemplate.postForObject(testUrl, requestBody, String.class);
		RemoteResult responseBody = JSON.parseObject(responseBodyStr, RemoteResult.class);
		Assert.assertFalse(responseBody.isSuccess());
	}
}
