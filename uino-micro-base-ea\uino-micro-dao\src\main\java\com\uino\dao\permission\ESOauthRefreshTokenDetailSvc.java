package com.uino.dao.permission;

import jakarta.annotation.PostConstruct;

import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.uino.dao.AbstractESBaseDao;
import com.uino.dao.ESConst;
import com.uino.bean.permission.base.OauthRefreshTokenDetail;

import lombok.extern.slf4j.Slf4j;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
@Slf4j
public class ESOauthRefreshTokenDetailSvc extends AbstractESBaseDao<OauthRefreshTokenDetail, JSONObject> {
	@Override
	public String getIndex() {
		// TODO Auto-generated method stub
		return ESConst.INDEX_OAUTH_REFRESHTOKEN;
	}

	@Override
	public String getType() {
		// TODO Auto-generated method stub
		return ESConst.INDEX_OAUTH_REFRESHTOKEN;

	}

	@PostConstruct
	public void init() {
		super.initIndex();
	}

}
