<?xml version="1.0" encoding="UTF-8"?><!--Converted at: Sat Dec 08 16:22:36 CST 2018-->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="VC_DIAGRAM_ITEM_OPTE">


	<resultMap id="queryResult" type="com.uinnova.product.eam.comm.model.VcDiagramItemOpte">
		<result property="id" column="ID" jdbcType="BIGINT"/>	<!-- ID -->
		<result property="opName" column="OP_NAME" jdbcType="VARCHAR"/>	<!-- 操作名称 -->
		<result property="opType" column="OP_TYPE" jdbcType="INTEGER"/>	<!-- 操作类型 -->
		<result property="classId" column="CLASS_ID" jdbcType="BIGINT"/>	<!-- 所属分类 -->
		<result property="opExp" column="OP_EXP" jdbcType="VARCHAR"/>	<!-- 操作表达式 -->
		<result property="opDesc" column="OP_DESC" jdbcType="VARCHAR"/>	<!-- 操作描述 -->
		<result property="domainId" column="DOMAIN_ID" jdbcType="BIGINT"/>	<!-- 所属域 -->
		<result property="dataStatus" column="DATA_STATUS" jdbcType="INTEGER"/>	<!-- 数据状态 -->
		<result property="creator" column="CREATOR" jdbcType="VARCHAR"/>	<!-- 创建人 -->
		<result property="modifier" column="MODIFIER" jdbcType="VARCHAR"/>	<!-- 修改人 -->
		<result property="createTime" column="CREATE_TIME" jdbcType="BIGINT"/>	<!-- 创建时间 -->
		<result property="modifyTime" column="MODIFY_TIME" jdbcType="BIGINT"/>	<!-- 修改时间 -->
	</resultMap>
	

	<sql id="sql_query_where">
		<if test="cdt != null and cdt.id != null">and
			ID = #{cdt.id:BIGINT}
		</if>
		<if test="ids != null and ids != ''">and
			ID in (${ids})
		</if>
		<if test="cdt != null and cdt.startId != null">and
			 ID &gt;= #{cdt.startId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endId != null">and
			 ID &lt;= #{cdt.endId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.opName != null and cdt.opName != ''">and
			OP_NAME like #{cdt.opName,jdbcType=VARCHAR} 
		</if>
		<if test="cdt != null and cdt.opType != null">and
			OP_TYPE = #{cdt.opType:INTEGER}
		</if>
		<if test="opTypes != null and opTypes != ''">and
			OP_TYPE in (${opTypes})
		</if>
		<if test="cdt != null and cdt.startOpType != null">and
			 OP_TYPE &gt;= #{cdt.startOpType:INTEGER} 
		</if>
		<if test="cdt != null and cdt.endOpType != null">and
			 OP_TYPE &lt;= #{cdt.endOpType:INTEGER} 
		</if>
		<if test="cdt != null and cdt.classId != null">and
			CLASS_ID = #{cdt.classId:BIGINT}
		</if>
		<if test="classIds != null and classIds != ''">and
			CLASS_ID in (${classIds})
		</if>
		<if test="cdt != null and cdt.startClassId != null">and
			 CLASS_ID &gt;= #{cdt.startClassId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endClassId != null">and
			 CLASS_ID &lt;= #{cdt.endClassId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.opExp != null and cdt.opExp != ''">and
			OP_EXP like #{cdt.opExp,jdbcType=VARCHAR} 
		</if>
		<if test="cdt != null and cdt.opDesc != null and cdt.opDesc != ''">and
			OP_DESC like #{cdt.opDesc,jdbcType=VARCHAR} 
		</if>
		<if test="cdt != null and cdt.domainId != null">and
			DOMAIN_ID = #{cdt.domainId:BIGINT}
		</if>
		<if test="domainIds != null and domainIds != ''">and
			DOMAIN_ID in (${domainIds})
		</if>
		<if test="cdt != null and cdt.startDomainId != null">and
			 DOMAIN_ID &gt;= #{cdt.startDomainId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endDomainId != null">and
			 DOMAIN_ID &lt;= #{cdt.endDomainId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.dataStatus != null">and
			DATA_STATUS = #{cdt.dataStatus:INTEGER}
		</if>
		<if test="dataStatuss != null and dataStatuss != ''">and
			DATA_STATUS in (${dataStatuss})
		</if>
		<if test="cdt != null and cdt.startDataStatus != null">and
			 DATA_STATUS &gt;= #{cdt.startDataStatus:INTEGER} 
		</if>
		<if test="cdt != null and cdt.endDataStatus != null">and
			 DATA_STATUS &lt;= #{cdt.endDataStatus:INTEGER} 
		</if>
		<if test="cdt != null and cdt.creator != null and cdt.creator != ''">and
			CREATOR like #{cdt.creator,jdbcType=VARCHAR} 
		</if>
		<if test="cdt != null and cdt.creatorEqual != null and cdt.creatorEqual != ''">and
			CREATOR = #{cdt.creatorEqual,jdbcType=VARCHAR}
		</if>
		<if test="creators != null and creators != ''">and
			CREATOR in (${creators})
		</if>
		<if test="cdt != null and cdt.modifier != null and cdt.modifier != ''">and
			MODIFIER like #{cdt.modifier,jdbcType=VARCHAR} 
		</if>
		<if test="cdt != null and cdt.modifierEqual != null and cdt.modifierEqual != ''">and
			MODIFIER = #{cdt.modifierEqual,jdbcType=VARCHAR}
		</if>
		<if test="modifiers != null and modifiers != ''">and
			MODIFIER in (${modifiers})
		</if>
		<if test="cdt != null and cdt.createTime != null">and
			CREATE_TIME = #{cdt.createTime:BIGINT}
		</if>
		<if test="createTimes != null and createTimes != ''">and
			CREATE_TIME in (${createTimes})
		</if>
		<if test="cdt != null and cdt.startCreateTime != null">and
			 CREATE_TIME &gt;= #{cdt.startCreateTime:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endCreateTime != null">and
			 CREATE_TIME &lt;= #{cdt.endCreateTime:BIGINT} 
		</if>
		<if test="cdt != null and cdt.modifyTime != null">and
			MODIFY_TIME = #{cdt.modifyTime:BIGINT}
		</if>
		<if test="modifyTimes != null and modifyTimes != ''">and
			MODIFY_TIME in (${modifyTimes})
		</if>
		<if test="cdt != null and cdt.startModifyTime != null">and
			 MODIFY_TIME &gt;= #{cdt.startModifyTime:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endModifyTime != null">and
			 MODIFY_TIME &lt;= #{cdt.endModifyTime:BIGINT} 
		</if>
	</sql>
	

	<sql id="sql_update_columns">
		<if test="record != null and record.id != null"> 
			ID = #{record.id:BIGINT}
		,</if>
		<if test="record != null and record.opName != null"> 
			OP_NAME = #{record.opName,jdbcType=VARCHAR}
		,</if>
		<if test="record != null and record.opType != null"> 
			OP_TYPE = #{record.opType:INTEGER}
		,</if>
		<if test="record != null and record.classId != null"> 
			CLASS_ID = #{record.classId:BIGINT}
		,</if>
		<if test="record != null and record.opExp != null"> 
			OP_EXP = #{record.opExp,jdbcType=VARCHAR}
		,</if>
		<if test="record != null and record.opDesc != null"> 
			OP_DESC = #{record.opDesc,jdbcType=VARCHAR}
		,</if>
		<if test="record != null and record.domainId != null"> 
			DOMAIN_ID = #{record.domainId:BIGINT}
		,</if>
		<if test="record != null and record.dataStatus != null"> 
			DATA_STATUS = #{record.dataStatus:INTEGER}
		,</if>
		<if test="record != null and record.creator != null"> 
			CREATOR = #{record.creator,jdbcType=VARCHAR}
		,</if>
		<if test="record != null and record.modifier != null"> 
			MODIFIER = #{record.modifier,jdbcType=VARCHAR}
		,</if>
		<if test="record != null and record.createTime != null"> 
			CREATE_TIME = #{record.createTime:BIGINT}
		,</if>
		<if test="record != null and record.modifyTime != null"> 
			MODIFY_TIME = #{record.modifyTime:BIGINT}
		,</if>
	</sql>
	

	<sql id="sql_query_columns">
		ID, OP_NAME, OP_TYPE, CLASS_ID, OP_EXP, OP_DESC, 
		DOMAIN_ID, DATA_STATUS, CREATOR, MODIFIER, CREATE_TIME, MODIFY_TIME
	</sql>
	

	

	<select id="selectList" parameterType="java.util.Map" resultMap="queryResult">
		select 
			<include refid="VC_DIAGRAM_ITEM_OPTE.sql_query_columns"/>
		from VC_DIAGRAM_ITEM_OPTE 
			<where>
				<include refid="VC_DIAGRAM_ITEM_OPTE.sql_query_where"/>
			</where>
		order by 
			<if test="orders != null and orders != ''">
				${orders}
			</if>
			<if test="orders == null or orders == ''">
				ID
			</if>
	</select>
	<select id="selectCount" parameterType="java.util.Map" resultType="java.lang.Long">
		select count(1) from VC_DIAGRAM_ITEM_OPTE 
			<where>
				<include refid="VC_DIAGRAM_ITEM_OPTE.sql_query_where"/>
			</where>
	</select>
	<select id="selectById" parameterType="java.util.Map" resultMap="queryResult">
		select 
			<include refid="VC_DIAGRAM_ITEM_OPTE.sql_query_columns"/>
		from VC_DIAGRAM_ITEM_OPTE where ID=#{id:BIGINT} and DATA_STATUS=1  
	</select>
	

	

	<insert id="insert" parameterType="java.util.Map">
		insert into VC_DIAGRAM_ITEM_OPTE(
			ID, OP_NAME, OP_TYPE, CLASS_ID, OP_EXP, 
			OP_DESC, DOMAIN_ID, DATA_STATUS, CREATOR, MODIFIER, 
			CREATE_TIME, MODIFY_TIME)
		values (
			#{record.id:BIGINT}, #{record.opName,jdbcType=VARCHAR}, #{record.opType:INTEGER}, #{record.classId:BIGINT}, #{record.opExp,jdbcType=VARCHAR}, 
			#{record.opDesc,jdbcType=VARCHAR}, #{record.domainId:BIGINT}, #{record.dataStatus:INTEGER}, #{record.creator,jdbcType=VARCHAR}, #{record.modifier,jdbcType=VARCHAR}, 
			#{record.createTime:BIGINT}, #{record.modifyTime:BIGINT})
	</insert>
	

	

	<update id="updateById" parameterType="java.util.Map">
		update VC_DIAGRAM_ITEM_OPTE
			<set> 
				<include refid="VC_DIAGRAM_ITEM_OPTE.sql_update_columns"/> 
			</set>
		where ID = #{id:BIGINT}
	</update>
	<update id="updateByCdt" parameterType="java.util.Map">
		update VC_DIAGRAM_ITEM_OPTE
			<set> 
				<include refid="VC_DIAGRAM_ITEM_OPTE.sql_update_columns"/> 
			</set>
			<where> 
				<include refid="VC_DIAGRAM_ITEM_OPTE.sql_query_where"/> 
			</where>
	</update>
	
	

	

	<delete id="deleteById" parameterType="java.util.Map">
		delete from VC_DIAGRAM_ITEM_OPTE where ID = #{id:BIGINT}
	</delete>
	<delete id="deleteByCdt" parameterType="java.util.Map">
		delete from VC_DIAGRAM_ITEM_OPTE
			<where> 
				<include refid="VC_DIAGRAM_ITEM_OPTE.sql_query_where"/> 
			</where>
	</delete>
	



</mapper>