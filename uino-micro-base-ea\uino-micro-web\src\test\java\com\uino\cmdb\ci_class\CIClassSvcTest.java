package com.uino.cmdb.ci_class;

import static org.junit.Assert.assertEquals;

import java.io.File;
import java.io.FileInputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import jakarta.servlet.http.Cookie;

import com.uino.dao.BaseConst;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClass;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uino.dao.cmdb.ESCIAttrTransConfigSvc;
import com.uino.dao.cmdb.ESCIClassSvc;
import com.uino.dao.cmdb.ESCISvc;
import com.uino.dao.cmdb.ESCmdbCommSvc;
import com.uino.dao.cmdb.ESDirSvc;
import com.uino.dao.cmdb.ESImageSvc;
import com.uino.service.cmdb.microservice.impl.CIClassSvc;
import com.uino.service.cmdb.microservice.impl.TopDataSvc;
import com.uino.service.simulation.impl.KpiSvc;
import com.uino.bean.cmdb.base.CcCiClassDir;
import com.uino.bean.cmdb.base.CcImage;
import com.uino.bean.cmdb.base.ESCIClassInfo;
import com.uino.bean.cmdb.query.ESCIClassSearchBean;
import com.uino.bean.monitor.base.ESKpiInfo;

@RunWith(SpringJUnit4ClassRunner.class)
public class CIClassSvcTest {

    @InjectMocks
    private CIClassSvc classSvc;

    private ESCIClassSvc esciClassSvc;

    private ESCISvc esCiSvc;

    private ESDirSvc esDirSvc;

    private ESCmdbCommSvc commSvc;

    private TopDataSvc topSvc;

    private ESImageSvc esImageSvc;

    private ESCIAttrTransConfigSvc attrTransConfigSvc;

    private KpiSvc kpiSvc;

    public static void setUpBeforeClass() throws Exception {
        MockHttpServletRequest request = new MockHttpServletRequest();
        Cookie cookie = new Cookie("token", "ca14ed042fe7912426b484f6ea5c754b4fb51ae4a72037e9127da51743e0d1f9650e6e78ed4466ac970acb6a0b2f46fd26958c4ab2d115cc71b34ca8a02db24c");
        request.setCookies(cookie);
        ServletRequestAttributes attributes = new ServletRequestAttributes(request);
        RequestContextHolder.setRequestAttributes(attributes);
    }

    @Before
    public void setUp() throws Exception {
        esciClassSvc = Mockito.mock(ESCIClassSvc.class);
        ReflectionTestUtils.setField(classSvc, "esciClassSvc", esciClassSvc);

        esCiSvc = Mockito.mock(ESCISvc.class);
        ReflectionTestUtils.setField(classSvc, "esCiSvc", esCiSvc);

        esDirSvc = Mockito.mock(ESDirSvc.class);
        ReflectionTestUtils.setField(classSvc, "esDirSvc", esDirSvc);

        commSvc = Mockito.mock(ESCmdbCommSvc.class);
        ReflectionTestUtils.setField(classSvc, "commSvc", commSvc);

        esImageSvc = Mockito.mock(ESImageSvc.class);
        ReflectionTestUtils.setField(classSvc, "esImageSvc", esImageSvc);

        attrTransConfigSvc = Mockito.mock(ESCIAttrTransConfigSvc.class);
        ReflectionTestUtils.setField(classSvc, "attrTransConfigSvc", attrTransConfigSvc);

        kpiSvc = Mockito.mock(KpiSvc.class);
        ReflectionTestUtils.setField(classSvc, "kpiSvc", kpiSvc);

        topSvc = Mockito.mock(TopDataSvc.class);
        ReflectionTestUtils.setField(classSvc, "topSvc", topSvc);
        ReflectionTestUtils.setField(classSvc, "rsmSlaveRoot", "rsmSlaveRoot");
        ReflectionTestUtils.setField(classSvc, "primaryKeyCount", 1);
    }

    @Test
    public void testSaveOrUpdate() {
        CcCiClassDir dir = new CcCiClassDir();
        dir.setId(123L);
        dir.setDirName("dirName");
        Mockito.when(esDirSvc.getById(Mockito.anyLong())).thenReturn(dir);

        List<ESCIClassInfo> list = new ArrayList<ESCIClassInfo>();
        ESCIClassInfo classInfo = new ESCIClassInfo();
        classInfo.setId(123L);
        classInfo.setClassCode("测试");
        list.add(classInfo);
        Mockito.when(esciClassSvc.getListByQuery(Mockito.any())).thenReturn(list);

        ESCIClassInfo esInfo = new ESCIClassInfo();
        esInfo.setClassCode("测试");
        esInfo.setClassName("测试");
        esInfo.setDirId(123L);
        esInfo.setParentId(0L);
        List<CcCiAttrDef> defs = new ArrayList<>();
        CcCiAttrDef def = new CcCiAttrDef();
        def.setId(123L);
        def.setProStdName("TEST");
        def.setProName("test");
        def.setProType(3);
        defs.add(def);
        CcCiAttrDef def2 = new CcCiAttrDef();
        def2.setId(1234L);
        def2.setProStdName("TEST2");
        def2.setProName("test2");
        def2.setIsMajor(1);
        def2.setProType(3);
        defs.add(def2);
        esInfo.setCcAttrDefs(defs);
        Mockito.when(commSvc.tranESCIClassInfo(Mockito.any(CcCiClassInfo.class))).thenReturn(esInfo);

        ESCIClassInfo esCls = new ESCIClassInfo();
        List<CcCiAttrDef> parDefs = new ArrayList<>();
        CcCiAttrDef parDef = new CcCiAttrDef();
        parDef.setId(123L);
        parDef.setProStdName("PARTEXT");
        parDef.setProName("parText");
        parDef.setProType(3);
        parDefs.add(parDef);
        esCls.setCcAttrDefs(parDefs);
        Mockito.when(esciClassSvc.getById(Mockito.anyLong())).thenReturn(null);

        Mockito.when(esciClassSvc.saveOrUpdate(Mockito.any(ESCIClassInfo.class))).thenReturn(1L);

        // 分类已存在
        CcCiClassInfo record = new CcCiClassInfo();
        try {
            classSvc.saveOrUpdate(record);
        } catch (Exception e) {
            // assertEquals("BS_CC_CLASS_EXIST", e.getMessage());
        }

        Mockito.when(esciClassSvc.getListByQuery(Mockito.any())).thenReturn(null);
        esInfo.setId(null);
        // 保存
        esInfo.setIcon("rsmSlaveRoot/abc.png");
        classSvc.saveOrUpdate(record);

        esInfo.setParentId(123L);
        defs.remove(1);
        esInfo.setId(null);
        // 父分类不存在
        try {
            classSvc.saveOrUpdate(record);
        } catch (Exception e) {
            assertEquals("BS_PARNET_CLASS_NOT_EXIST", e.getMessage());
        }

        Mockito.when(esciClassSvc.getById(Mockito.anyLong())).thenReturn(esCls);
        // 属性重复
        try {
            classSvc.saveOrUpdate(record);
        } catch (Exception e) {
            assertEquals("BS_CC_CLASS_DEF_EXIST${proName:TEST}", e.getMessage());
        }

    }

    @Test
    public void testQueryClassInfoById() {
        CcCiClassInfo classInfo = new CcCiClassInfo();
        classInfo.setCiCount(100L);
        Mockito.when(esciClassSvc.queryClassInfoById(Mockito.anyLong())).thenReturn(classInfo);

        CcCiClassInfo result = classSvc.queryClassInfoById(123L);
        assertEquals(100L, result.getCiCount().longValue());
    }

    @Test
    public void testQueryClassByCdt() {
        List<CcCiClassInfo> list = new ArrayList<>();
        CcCiClassInfo classInfo = new CcCiClassInfo();
        classInfo.setCiCount(100L);
        list.add(classInfo);
        Mockito.when(esciClassSvc.queryClassByCdt(Mockito.any(CCcCiClass.class))).thenReturn(list);

        List<CcCiClassInfo> result = classSvc.queryClassByCdt(new CCcCiClass());
        assertEquals(1, result.size());
    }

    @Test
    public void testQueryCiClassInfoList() {
        List<CcCiClassInfo> list = new ArrayList<>();
        CcCiClassInfo classInfo = new CcCiClassInfo();
        classInfo.setCiCount(100L);
        list.add(classInfo);
        Mockito.when(esciClassSvc.queryCiClassInfoList(Mockito.any(CCcCiClass.class), Mockito.any(), Mockito.anyBoolean())).thenReturn(list);

        List<CcCiClassInfo> result = classSvc.queryCiClassInfoList(1L, null, null, true);
        assertEquals(1, result.size());
    }

    @Test
    public void testGetClassTree() {
        List<ESCIClassInfo> list = new ArrayList<ESCIClassInfo>();
        ESCIClassInfo classInfo = new ESCIClassInfo();
        classInfo.setId(123L);
        classInfo.setClassCode("测试");
        classInfo.setDirId(1L);
        classInfo.setIcon("icon");
        list.add(classInfo);
        ESCIClassInfo classInfo1 = new ESCIClassInfo();
        classInfo1.setId(456L);
        classInfo1.setClassCode("测试");
        classInfo1.setDirId(1L);
        classInfo1.setIcon("icon");
        list.add(classInfo1);
        Mockito.when(esciClassSvc.getSortListByQuery(Mockito.anyInt(), Mockito.anyInt(), Mockito.any(), Mockito.anyString(), Mockito.anyBoolean())).thenReturn(new Page<>(1, 10, 10, 1, list));

        Map<String, Long> map = new HashMap<String, Long>();
        map.put("123", 100L);
        Mockito.when(esCiSvc.groupByCountField(Mockito.anyString(), Mockito.any())).thenReturn(map);
        
        List<CcCiClassDir> dirs = new ArrayList<>();
        CcCiClassDir dir = new CcCiClassDir();
        dir.setId(1L);
        dir.setDirName("dir");
        dirs.add(dir);
        Mockito.when(esDirSvc.getListByCdt(Mockito.any())).thenReturn(dirs);

        classSvc.getClassTree(BaseConst.DEFAULT_DOMAIN_ID);
    }

    @Test
    public void testImportCiClassAttr() throws Exception {
        List<CcCiAttrDef> defs = new ArrayList<>();
        CcCiAttrDef def = new CcCiAttrDef();
        def.setProName("重复");
        def.setOrderNo(1);
        defs.add(def);
        ESCIClassInfo esInfo = new ESCIClassInfo();
        esInfo.setClassName("test-test");
        esInfo.setParentId(0L);
        esInfo.setCcAttrDefs(defs);
        Mockito.when(esciClassSvc.getById(Mockito.anyLong())).thenReturn(null);

        List<CcCiAttrDef> dbDefs = new ArrayList<>();
        CcCiAttrDef dbDef = new CcCiAttrDef();
        dbDef.setProName("重复");
        dbDef.setOrderNo(1);
        dbDefs.add(dbDef);
        Mockito.when(esciClassSvc.getAllDefsByClassId(1L, Mockito.anyLong())).thenReturn(dbDefs);

        Mockito.when(esciClassSvc.saveOrUpdate(Mockito.any())).thenReturn(1L);

        File file = new File("./src/test/resources/testdata/ci.xlsx");
        System.out.println(file.getAbsolutePath());
        MockMultipartFile firstFile = new MockMultipartFile("file", "ci.xlsx", MediaType.TEXT_PLAIN_VALUE, new FileInputStream(file));
        // 分类不存在
        try {
            classSvc.importCiClassAttr(firstFile, 123L);
        } catch (Exception e) {
            // assertEquals("BS_MNAME_CLASS_NOT_EXSIT", e.getMessage());
        }

        // 保存
        Mockito.when(esciClassSvc.getById(Mockito.anyLong())).thenReturn(esInfo);
        classSvc.importCiClassAttr(firstFile, 123L);

        // 保存失败
        Mockito.when(esciClassSvc.saveOrUpdate(Mockito.any())).thenReturn(0L);
        classSvc.importCiClassAttr(firstFile, 123L);
    }

    @Test
    public void testExportClassAttrExcel() {
        classSvc.exportClassAttrExcel(null, false);
    }

    @Test
    public void testDeleteById() {
        List<ESCIClassInfo> list = new ArrayList<ESCIClassInfo>();
        ESCIClassInfo classInfo = new ESCIClassInfo();
        classInfo.setId(123L);
        classInfo.setClassCode("测试");
        classInfo.setDirId(1L);
        list.add(classInfo);
        ESCIClassInfo classInfo1 = new ESCIClassInfo();
        classInfo1.setId(456L);
        classInfo1.setClassCode("测试");
        classInfo1.setDirId(1L);
        list.add(classInfo1);
        Mockito.when(esciClassSvc.getListByQuery(Mockito.any())).thenReturn(list);

        Map<Object, Long> map = new HashMap<Object, Long>();
        map.put("123", 100L);
        Mockito.when(esCiSvc.countCIByQuery(Mockito.any())).thenReturn(map);

        Mockito.when(esciClassSvc.deleteById(Mockito.anyLong())).thenReturn(1);

        // 存在子类
        try {
            classSvc.deleteById(123L);
        } catch (Exception e) {
            // assertEquals("BS_CLASS_DELETE_EXIST_SUB_CLASS", e.getMessage());
        }


        Mockito.when(esciClassSvc.getListByQuery(Mockito.any())).thenReturn(new ArrayList<>());
        // 存在数据
        try {
            classSvc.deleteById(123L);
        } catch (Exception e) {
            assertEquals("BS_CC_CLASS_HAS_DATA", e.getMessage());
        }
        Mockito.when(esCiSvc.countCIByQuery(Mockito.any())).thenReturn(new HashMap<>());
        classSvc.deleteById(123L);
    }

    @Test
    public void testGetAllDefsByClassId() {
        List<CcCiAttrDef> defs = new ArrayList<>();
        CcCiAttrDef def = new CcCiAttrDef();
        def.setProName("proName");
        defs.add(def);
        Mockito.when(esciClassSvc.getAllDefsByClassId(1L, Mockito.anyLong())).thenReturn(defs);

//        List<CcCiAttrDef> res = classSvc.getAllDefsByClassId(123L);
//        assertEquals("proName", res.get(0).getProName());
    }

    @Test
    public void testQueryCiClassInfoListBySearchBean() {
        List<ESKpiInfo> kpiInfos = new ArrayList<>();
        ESKpiInfo kpiInfo = new ESKpiInfo();
        kpiInfo.setKpiCode("kpiCode");
        kpiInfos.add(kpiInfo);
        Page<ESKpiInfo> page = new Page<>();
        page.setData(kpiInfos);
        Mockito.when(kpiSvc.queryKpiInfoPage(Mockito.any())).thenReturn(page);

        Mockito.when(esciClassSvc.queryCiClassInfoList(Mockito.any(), Mockito.anyString(), Mockito.anyBoolean())).thenReturn(new ArrayList<>());

        ESCIClassSearchBean bean = new ESCIClassSearchBean();
        bean.setIsBindKpi(true);
        classSvc.queryCiClassInfoListBySearchBean(bean);
    }

    @Test
    public void testImportDirAndCIClass() {
        List<CcCiClassDir> dirs = new ArrayList<>();
        CcCiClassDir dir = new CcCiClassDir();
        dir.setDirName("dirName");
        dir.setId(123L);
        dirs.add(dir);
        Mockito.when(esDirSvc.getListByQuery(Mockito.any())).thenReturn(dirs);

        Page<CcImage> imgPage = new Page<>();
        List<CcImage> images = new ArrayList<>();
        CcImage image = new CcImage();
        image.setImgName("imgName");
        image.setImgPath("defaultIcon");
        images.add(image);
        imgPage.setData(images);
        Mockito.when(esImageSvc.getListByQuery(Mockito.anyInt(), Mockito.anyInt(), Mockito.any())).thenReturn(imgPage);

        List<ESCIClassInfo> clsInfos = new ArrayList<>();
        ESCIClassInfo classInfo = new ESCIClassInfo();
        classInfo.setClassName("className");
        classInfo.setId(123L);
        classInfo.setDirId(123L);
        classInfo.setParentId(0L);
        classInfo.setIcon("defaultIcon");
        List<CcCiAttrDef> defs = new ArrayList<>();
        CcCiAttrDef def1 = new CcCiAttrDef();
        def1.setProName("proName1");
        def1.setProType(3);
        def1.setIsCiDisp(1);
        def1.setIsMajor(1);
        def1.setIsRequired(1);
        def1.setDefVal("defaultVal");
        CcCiAttrDef def2 = new CcCiAttrDef();
        def2.setProName("proName2");
        def2.setProType(6);
        def2.setIsCiDisp(0);
        def2.setIsMajor(0);
        def2.setIsRequired(0);
        def2.setDefVal("1");
        def2.setEnumValues("[\"1\",\"2\",\"3\"]");
        defs.add(def1);
        defs.add(def2);
        classInfo.setCcAttrDefs(defs);
        clsInfos.add(classInfo);
        Mockito.when(esciClassSvc.getListByQuery(Mockito.any())).thenReturn(clsInfos);
        Mockito.when(esciClassSvc.getDefaultIcon()).thenReturn("defaultIcon");
        Mockito.when(esDirSvc.saveOrUpdateBatch(Mockito.anyList())).thenReturn(1);
        Mockito.when(esciClassSvc.saveOrUpdateBatch(Mockito.anyList())).thenReturn(1);

        List<String[]> sheetVal = new ArrayList<>();
        String[] row1 = {"dirName", "className", "", "imgName", "proName1", "字符串", "是", "是", "是", "defaultVal", ""};
        String[] row2 = {"dirName", "className", "", "imgName", "proName2", "枚举", "否", "否", "否", "1", "1,2,3"};
        // 子类
        String[] row3 = {"dirName", "className2", "className", "imgName2", "proName", "字符串", "否", "否", "否", "1", ""};
        String[] row4 = {"dirName2", "className3", "", "imgName2", "proName", "字符串", "是", "是", "是", "1", ""};
        sheetVal.add(row1);
        sheetVal.add(row2);
        sheetVal.add(row3);
        sheetVal.add(row4);
        // 无数据只导入类定义
        try {
            classSvc.importDirAndCIClass(1L, sheetVal, Arrays.asList("className", "className2"), false);
        } catch (Exception e) {
            assertEquals("第2行，分类[className]已存在", e.getMessage());
        }

        // 有数据
        classSvc.importDirAndCIClass(1L, sheetVal, Arrays.asList("className", "className2"), false);
    }
}
