package com.uinnova.product.eam.comm.model.es;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 应用系统同步记录(xw-DDM)
 * <AUTHOR>
 */
@Data
@Comment("应用系统对接[UINO_EAM_APP_SYNC_RECORD]")
public class AppSystemSyncRecord implements Serializable {
    @Comment("主键")
    private Long id;
    @Comment("系统id")
    private String systemId;
    @Comment("系统级逻辑实体C'的属性ciCode集合:<实体code,实体属性集合>")
    private Map<String, List<String>> entityAttr;
    @Comment("实体间关系id集合")
    private List<String> rltIds;
    @Comment("版本")
    private Integer version;
    @Comment("同步状态")
    private Integer status;
    @Comment("领域id")
    private Long domainId;
    @Comment("创建人")
    private String creator;
    @Comment("修改人")
    private String modifier;
    @Comment("创建时间")
    private Long createTime;
    @Comment("修改时间")
    private Long modifyTime;
}
