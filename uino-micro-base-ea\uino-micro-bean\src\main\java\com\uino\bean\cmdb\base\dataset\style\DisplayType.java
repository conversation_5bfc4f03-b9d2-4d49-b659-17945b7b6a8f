package com.uino.bean.cmdb.base.dataset.style;

import com.binary.core.exception.MessageException;

/**
 * @Classname DisplayType
 * @Description 显示项
 * @Date 2020/3/18 21:45
 * @Created by sh
 */
public enum DisplayType {
    //表格，拓朴，统计，接口
    Table(1),
    Topology(2),
    Statistics(3),
    Api(4);

    private Integer code;

    DisplayType(Integer code) {
        this.code = code;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public static DisplayType valueOf(Integer code) {
        if (Table.code.equals(code)) {
            return Table;
        } else if (Topology.code.equals(code)) {
            return Topology;
        } else if (Statistics.code.equals(code)) {
            return Statistics;
        }else if (Api.code.equals(code)) {
            return Api;
        } else {
            throw new MessageException("显示项code错误");
        }
    }
}
