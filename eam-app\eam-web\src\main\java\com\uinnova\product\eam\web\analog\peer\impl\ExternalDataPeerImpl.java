package com.uinnova.product.eam.web.analog.peer.impl;

import java.util.Map;

import com.uinnova.product.eam.model.diagram.EamHttpRequestParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;

import com.binary.core.http.HttpClient;
import com.binary.core.util.BinaryUtils;
import com.binary.json.JSON;
import com.uinnova.product.eam.web.analog.bean.CCBRequestParam;
import com.uinnova.product.eam.web.analog.bean.DmvHttpRequestParam;
import com.uinnova.product.eam.web.analog.peer.ExternalDataPeer;

class ExternalDataPeerImpl implements ExternalDataPeer{

	private static final Logger logger = LoggerFactory.getLogger(ExternalDataPeerImpl.class);
	
	private String ccbExternalDataUrl;

	private String healthDegreeUrl;
	
	@Override
	public Object requestByUrl(EamHttpRequestParam param) {
		String url = param.getUrl();
		if(BinaryUtils.isEmpty(url)) return null;
		HttpClient client = HttpClient.getInstance(url);
		Map<String, String> headers = param.getHeaders();
		if(!BinaryUtils.isEmpty(headers)){
			for(Map.Entry<String, String> entry : headers.entrySet()) {
				client.addRequestProperty(entry.getKey(),entry.getValue());
			}
		}

		String requestMethod = param.getRequestMethod();
		Object res = null;
		if(!BinaryUtils.isEmpty(requestMethod)){
			client.setRequestMethod(requestMethod);
			if("GET".equals(requestMethod)){
				res = client.request(param.getUrlparams());
			}else if("POST".equals(requestMethod)){
				res = client.rest((String) null ,JSON.toString(param.getFormbean()));
			}
		}

		return res;
	}

	@Override
	public Object queryExternalDataForCCBITMap(CCBRequestParam param) {
		if(BinaryUtils.isEmpty(ccbExternalDataUrl) || param == null) return null;
		HttpClient client = HttpClient.getInstance(ccbExternalDataUrl);
		client.setRequestMethod("GET");
		String startTime = param.getStartTime();
		String endTime = param.getEndTime();
		String queryType = param.getQueryType();
		String word = param.getWord();
		StringBuffer sb = new StringBuffer(1000);
		if(!BinaryUtils.isEmpty(startTime) || !BinaryUtils.isEmpty(endTime) || !BinaryUtils.isEmpty(queryType) ||!BinaryUtils.isEmpty(word)) {
			sb.append("?");
		}
		if(!BinaryUtils.isEmpty(startTime)) sb.append("starttime=").append(startTime).append("&");
		if(!BinaryUtils.isEmpty(endTime)) sb.append("endtime=").append(endTime).append("&");
		if(!BinaryUtils.isEmpty(queryType)) sb.append("classes=").append(queryType).append("&");
		if(!BinaryUtils.isEmpty(word)) sb.append("classcode=").append(word).append("&");
		if(sb.length()>0) sb.deleteCharAt(sb.length()-1);
		String urlparams = sb.toString();
		logger.info("rest接口请求参数====="+urlparams);
		String rest = client.rest(urlparams, null);
		logger.info("rest请求结果====="+rest);
		Map map = (Map)JSON.toObject(rest);
//		List<Object> list = null;
//		List<CCBExternalData> list = null;
		Object data = map.get("data");
//		if(!BinaryUtils.isEmpty(data)) {
//			try {
//				list = JSON.toList(JSON.toString(data));
//			} catch (Exception e) {
//				logger.error("rest请求结果不正确====="+rest);
//			}
//		}
		
		
		return data;
	}

	public Object queryHealthDegreeList() {
		HttpClient client = HttpClient.getInstance(this.healthDegreeUrl);
		client.setRequestMethod("GET");
		return client.request(this.healthDegreeUrl, null);
	}

}
