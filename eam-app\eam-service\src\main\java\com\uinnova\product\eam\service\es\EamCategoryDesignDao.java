package com.uinnova.product.eam.service.es;

import com.binary.core.util.BinaryUtils;
import com.uinnova.product.eam.comm.model.es.EamCategory;
import com.uinnova.product.eam.model.enums.CategoryTypeEnum;
import com.uino.bean.permission.base.SysModule;
import com.uino.dao.AbstractESBaseDao;
import com.uino.service.util.FileUtil;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 资产仓库目录&文件夹Dao
 * <AUTHOR>
 */
@Service
public class EamCategoryDesignDao extends AbstractESBaseDao<EamCategory, EamCategory> {


    @Resource
    private EamCategoryDesignDao categoryDesignDao;

    @Override
    public String getIndex() {
        return "uino_eam_category_design";
    }

    @Override
    public String getType() {
        return getIndex();
    }

    @PostConstruct
    public void init() {
        super.initIndex();
        //初始化顶级目录数据,获取uino_sys_module.json的菜单页数据；
        List<SysModule> moduleList = FileUtil.getData("/initdata/uino_sys_module.json", SysModule.class);
        if(!BinaryUtils.isEmpty(moduleList)){
            List<SysModule> moduleNames = moduleList.stream().filter(each -> each.getModuleType() == 3).collect(Collectors.toList());
            List<EamCategory> categoryList = new ArrayList<>();
            if(!BinaryUtils.isEmpty(moduleNames)){
                String loginCode = "admin";
                for (SysModule model : moduleNames) {
                    long id = Long.parseLong(String.valueOf(("QUICK_EA"+model.getModuleName()).hashCode() & Integer.MAX_VALUE));
                    EamCategory category = new EamCategory();
                    category.setId(id);
                    category.setParentId(0L);
                    category.setType(CategoryTypeEnum.ROOT.val());
                    category.setDirName(model.getLabel());
                    category.setDataStatus(1);
                    category.setDirLvl(1);
                    category.setDomainId(1L);
                    category.setOwnerCode(loginCode);
                    category.setCreator(loginCode);
                    category.setModifier(loginCode);
                    category.setDirPath("#"+id+"#");
                    categoryList.add(category);
                }
            }
            if(BinaryUtils.isEmpty(categoryList)){
                return;
            }
            categoryDesignDao.saveOrUpdateBatch(categoryList);
        }
    }

}
