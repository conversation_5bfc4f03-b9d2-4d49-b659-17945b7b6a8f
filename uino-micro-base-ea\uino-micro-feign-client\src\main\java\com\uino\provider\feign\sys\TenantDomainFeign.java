package com.uino.provider.feign.sys;

import com.binary.jdbc.Page;
import com.uino.bean.sys.base.TenantDomain;
import com.uino.provider.feign.config.BaseFeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@Component
@FeignClient(name = "${feign-server-name:tarsier-platform}", path = "${feign-server-path:tarsier-platform}/feign/tenantDomain", configuration = {BaseFeignConfig.class})
public interface TenantDomainFeign {
    /**
     * 保存或更新租户域
     *
     * @param tenantDomain
     * @return
     */
    @PostMapping("saveOrUpdate")
    Long saveOrUpdate(@RequestBody TenantDomain tenantDomain);

    /**
     * 查询租户域信息
     *
     * @param
     * @return
     */
    @PostMapping("query")
    Page<TenantDomain> queryPage(@RequestParam(value = "pageNum") int pageNum, @RequestParam(value = "pageSize") int pageSize, @RequestParam(value = "name") String name);


    /**
     * 删除租户域
     *
     * @param id
     * @return
     */
    @PostMapping("delete")
    Long deleteById(@RequestBody Long id);

    /**
     * 启用或停用租户域
     *
     * @param id
     * @return
     */
    @PostMapping("startOrStop")
    Long startOrStop(@RequestBody Long id);

    /**
     * 重置域管理员密码
     *
     * @param id
     * @return
     */
    @PostMapping("resetPasswdByAdmin")
    boolean resetPasswdByAdmin(@RequestBody Long id);


    /**
     * 查询可用的域
     * @return
     */
    @PostMapping("queryAvailableList")
    List<TenantDomain> queryAvailableList();

}
