package com.uino.plugin.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * <AUTHOR>
 * @Description 操作插件详情
 * @Date 2021/9/26
 * @Version 1.0
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "操作插件异常记录", description = "操作插件详情")
public class OperatePluginDetails {

    @ApiModelProperty("文件名，plugins/下相对路径")
    private String fileName;
    @ApiModelProperty(value = "服务名称", example = "123")
    private String serviceName;
    @ApiModelProperty(value = "服务地址", example = "123")
    private String address;
    @ApiModelProperty(value = "是否成功", example = "123")
    private boolean success;
    @ApiModelProperty(value = "说明", example = "123")
    private String message;


}
