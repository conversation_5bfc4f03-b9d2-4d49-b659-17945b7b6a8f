package com.uinnova.product.eam.model.vo;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.util.List;

@Data
@Comment("dify智能绘图-业务能力全景图-层级数据")
public class DFAutoServiceGraphLevelData {

    @Comment("层级")
    private Integer level;
    @Comment("分类id")
    private Long id;
    @Comment("分类名称")
    private String title;
    @Comment("下级分类")
    private List<DFAutoServiceGraphLevelData> children;
}
