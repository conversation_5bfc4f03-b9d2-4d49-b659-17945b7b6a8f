package com.uino.api.client.sys.rpc;

import java.util.Map;

import com.uino.dao.BaseConst;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.uino.bean.sys.base.Logo;
import com.uino.provider.feign.sys.LogoFeign;
import com.uino.api.client.sys.ILogoApiSvc;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
public class LogoApiSvcRpc implements ILogoApiSvc {

    @Autowired
    private LogoFeign svc;

    @Override
    public Map<String, Logo> getLogos() {
        return svc.getLogos();
    }


    @Override
    public Map<String, Logo> updateLogo(String logoType, MultipartFile file) {
        return svc.updateLogo(logoType, file);
    }



    @Override
    public Map<String, Logo> deleteLogo(String logoType) {
        return svc.deleteLogo(logoType);
    }

    @Override
    public Map<String, Logo> updateLogoByPath(String logoType, String path, Long fileId) {
        return svc.updateLogoByPath(logoType, path, fileId);
    }


}
