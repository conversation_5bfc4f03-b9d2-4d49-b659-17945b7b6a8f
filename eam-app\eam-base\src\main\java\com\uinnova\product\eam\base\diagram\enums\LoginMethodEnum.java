package com.uinnova.product.eam.base.diagram.enums;

/**
 * @Description 登录方式枚举类
 * <AUTHOR>
 * @Date 2021-09-03-10:36
 * @version 1.0
 */
public enum LoginMethodEnum {
    /** oauth方式登录 */
    TOKEN("oauth"),

    /** sso方式登录 */
    SSO("sso"),

    /** thingjs方式登录 */
    THING_JS("thingjs");

    private final String value;

    LoginMethodEnum(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }
}
