package com.uino.web.cmdb.mvc;

import com.binary.core.util.BinaryUtils;
import com.binary.framework.util.ControllerUtils;
import com.binary.jdbc.Page;
import com.binary.json.JSON;
import com.uinnova.product.vmdb.comm.bean.QueryPageCondition;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uinnova.product.vmdb.comm.util.CommUtil;
import com.uinnova.product.vmdb.comm.util.RestTypeUtil;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CiGroupPage;
import com.uinnova.product.vmdb.provider.ci.bean.CiQueryCdt;
import com.uinnova.product.vmdb.provider.search.bean.CcCiSearchPage;
import com.uino.api.client.cmdb.ICIApiSvc;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.business.*;
import com.uino.bean.cmdb.query.ESAttrAggBean;
import com.uino.bean.cmdb.query.ESCISearchBean;
import com.uino.bean.permission.base.SysUser;
import com.uino.init.api.ApiResult;
import com.uino.init.api.ApiVersion;
import com.uino.util.sys.SysUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

@ApiVersion(1)
@Api(value = "对象管理", tags = {"对象管理"})
@RestController
@RequestMapping("/cmdb/ci")
public class CIMvc {

	@Autowired
	private ICIApiSvc ciSvc;

	/*@PostMapping("/saveOrUpdate")
	@ApiOperation(value = "保存或更新对象信息(ID存在则更新)")
	public ApiResult<Long> saveOrUpdate(@RequestBody CcCiInfo ciInfo) {
		ESCIInfo ci = CommUtil.copy(ciInfo.getCi(), ESCIInfo.class);
		ciInfo.setCi(ci);
		SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
		ciInfo.getCi().setDomainId(currentUserInfo.getDomainId());
		Long id = ciSvc.saveOrUpdateCI(ciInfo);
		return ApiResult.ok(this).data(id);
	}*/

	@PostMapping("/saveOrUpdateExtra")
	@ApiOperation(value = "保存或更新对象信息(ID存在则更新)")
	public ApiResult<Long> saveOrUpdateExtra(@RequestBody CcCiInfo ciInfo) {
		SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
		ciInfo.getCi().setDomainId(currentUserInfo.getDomainId());
		Long id = ciSvc.saveOrUpdateCIExtra(ciInfo);
		return ApiResult.ok(this).data(id);
	}

	@ApiOperation(value = "通过id查询对象信息")
	@ModDesc(desc = "通过ID查询对象信息", pDesc = "对象的id", pType = Long.class, rDesc = "对象信息", rType = CcCiInfo.class)
	@PostMapping("/queryById")
	public ApiResult<CcCiInfo> queryById(@RequestBody String body) {
		Long id = Long.parseLong(body.trim());
		CcCiInfo result = ciSvc.getCiInfoById(id);
		return ApiResult.ok(this).data(result);
	}

	@ApiOperation(value = "分页查询对象数据")
	@RequestMapping(value="/queryPageByIndex",method=RequestMethod.POST)
    @ModDesc(desc = "分页查询对象数据", pDesc = "条件查询", pType = QueryPageCondition.class, pcType = CiQueryCdt.class, rDesc = "对象数据", rType = CiGroupPage.class)
	public ApiResult<CiGroupPage> queryPageByIndex(HttpServletRequest request, HttpServletResponse response, @RequestBody String body) {
		QueryPageCondition<CiQueryCdt> pageCdt = RestTypeUtil.toPageCondition(body, CiQueryCdt.class);
		SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
		CiGroupPage result = ciSvc.queryPageByIndex(currentUserInfo.getDomainId(),pageCdt.getPageNum(), pageCdt.getPageSize(), pageCdt.getCdt(), false);
		return ApiResult.ok(this).data(result);
	}

	@ApiOperation(value = "分页查询对象数据-支持属性排序")
    @PostMapping("/queryPageBySearchBean")
    public ApiResult<CiGroupPage> queryPageBySearchBean(@RequestBody ESCISearchBean bean) {
		SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
		bean.setDomainId(currentUserInfo.getDomainId());
		CiGroupPage result = ciSvc.queryPageBySearchBean(bean, false);
		result.getData().forEach(info ->{
			Map<String, String> attrs = info.getAttrs();
			String ownerCode = info.getCi().getOwnerCode();
			//根据这个ownerCode 去查询 对应的中文名字
			attrs.put("所属用户",ownerCode);
		});

        return ApiResult.ok(this).data(result);
    }

	@ApiOperation(value = "通过XLS或XLSX导入CI信息")
	@RequestMapping(value = "/importCi", method = RequestMethod.POST)
    @ModDesc(desc = "通过XLS或者XLSX导入CI信息", pDesc = "XLS或者XLSX文件", rDesc = "导入明细", rType = ImportResultMessage.class)
	public ApiResult<ImportResultMessage> asyncImportCi(HttpServletRequest request, HttpServletResponse response,
			@RequestParam("file") MultipartFile file) {
		BinaryUtils.checkEmpty(request.getParameter("ciClassIds"), "ciClassIds");
		Long classId = JSON.toObject(request.getParameter("ciClassIds"), Long.class);
		ImportResultMessage result = ciSvc.importCiByCiClsIds(file, classId);
		return ApiResult.ok(this).data(result);
	}

    @ApiOperation(value="导出对象数据",notes="- 下载分类属性或属性模板 \r\n -传分类id则表示下载分类属性，不传表示下载模板 \r\n - 返回值: **byte[]**")
	@RequestMapping(value="/exportCi",method=RequestMethod.POST)
    @ModDesc(desc = "导出对象数据(hasData:0只导出分类及属性(默认),1:导出分类加数据;ciClassIds:分类的ID多个用','隔开,不传导出所有分类)", pDesc = "?hasData=1&ciClassIds=1", pType = String.class, rDesc = "导出全部CI", rType = String.class)
    public ResponseEntity<byte[]> exportAllCi(@RequestBody ExportCiDto exportDto, HttpServletRequest request,
        HttpServletResponse response)
			throws IOException {
		exportDto = exportDto == null ? new ExportCiDto() : exportDto;
		SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
		exportDto.setDomainId(currentUserInfo.getDomainId());
		ResponseEntity<byte[]> resource = ciSvc.exportCiOrClass(exportDto);
		return resource;
	}

    @ApiOperation(value="一键导入解析Excel文件")
	@RequestMapping(value = "/importCiExcelBatch", method = RequestMethod.POST)
    @ModDesc(desc = "一键导入解析Excel文件", pDesc = "XLS或者XLSX文件", pType = MultipartFile.class, rDesc = "文件解析内容", rType = ImportExcelMessage.class)
	public ApiResult<ImportExcelMessage> importCiExcelBatch(HttpServletRequest request, HttpServletResponse response,
			@RequestParam("file") MultipartFile file) {
		ImportExcelMessage result = ciSvc.importCiExcel(file);
		return ApiResult.ok(this).data(result);
	}

    @ApiOperation(value="一键导入批量导入分类数据")
	@RequestMapping(value = "/importCiByClassBatch", method = RequestMethod.POST)
    @ModDesc(desc = "一键导入批量导入分类数据", pDesc = "导入对象", pType = CiExcelInfoDto.class, rDesc = "导入明细", rType = ImportResultMessage.class)
	public ApiResult<ImportResultMessage> importCiByClassBatch(HttpServletRequest request, HttpServletResponse response,
			@RequestBody CiExcelInfoDto excelInfoDto) {
		SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
		ImportResultMessage result = ciSvc.importCiByClassBatch(currentUserInfo.getDomainId(),excelInfoDto);
		return ApiResult.ok(this).data(result);
	}

    @ApiOperation(value="通过ID删除对象")
	@RequestMapping(value="/removeById",method=RequestMethod.POST)
    @ModDesc(desc = "通过ID删除对象", pDesc = "对象的ID", pType = Long.class, rDesc = "1成功0失败", rType = Integer.class)
	public ApiResult<Integer> removeById(HttpServletRequest request, HttpServletResponse response, @RequestBody String body) {
		long id = Long.parseLong(body.trim());
        Integer integer = ciSvc.removeById(id, 1L);
		return ApiResult.ok(this).data(integer);
	}

    @ApiOperation(value="通过分类ID删除相关的对象数据")
	@RequestMapping(value="/removeByClassId",method=RequestMethod.POST)
    @ModDesc(desc = "通过分类ID删除相关的对象数据", pDesc = "对象分类的ID", pType = Long.class, rDesc = "1成功0失败", rType = Integer.class)
	public ApiResult<Integer> removeByClassId(HttpServletRequest request, HttpServletResponse response, @RequestBody String body) {
		long id = Long.parseLong(body.trim());
        Integer integer = ciSvc.removeByClassId(id, 1L);
		return ApiResult.ok(this).data(integer);
	}

    @ApiOperation(value="条件搜索对象数据")
	@RequestMapping(value="/searchCIByBean",method=RequestMethod.POST)
    @ModDesc(desc = "条件搜索对象数据", pDesc = "查询条件", pType = ESCISearchBean.class, rDesc = "对象数据", rType = CcCiSearchPage.class)
	public ApiResult<CcCiSearchPage> searchCIByBean(@RequestBody ESCISearchBean bean, HttpServletRequest request,
			HttpServletResponse response) {
		if (BinaryUtils.isEmpty(bean)) {
			bean = new ESCISearchBean();
			bean.setPageNum(1);
			bean.setPageSize(30);
		}
		SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
		bean.setDomainId(currentUserInfo.getDomainId());
		CcCiSearchPage res = ciSvc.searchCIByBean(bean);
		return ApiResult.ok(this).data(res);
	}
	@ApiOperation(value="通过ID批量删除对象")
	@RequestMapping(value="/removeByIds",method=RequestMethod.POST)
	@ModDesc(desc = "通过ID批量删除对象", pDesc = "对象的ID集合", pType = List.class, pcType = Long.class, rDesc = "1成功0失败", rType = Integer.class)
	public ApiResult<Integer> removeByIds(HttpServletRequest request, HttpServletResponse response, @RequestBody List<Long> ciIds) {
		Integer result = ciSvc.removeByIds(ciIds, 1L);
		return ApiResult.ok(this).data(result);
	}


	@PostMapping("/modifyAttrValueBatch")
	@ApiOperation(value="批量修改对象属性,选择本页数据或全部数据")
	@ModDesc(desc = "批量修改对象属性，选择本页数据或全部数据", pDesc = "查询条件", pType = CIAttrValueUpdateDto.class, rDesc = "是否修改成功", rType = Boolean.class)
	public ApiResult<Boolean>  modifyAttrValueBatch(@RequestBody CIAttrValueUpdateDto dto, HttpServletRequest request,
													HttpServletResponse response){
		return ApiResult.ok(this).data(ciSvc.modifyAttrValueBatch(dto));
	}


	@ApiOperation(value="通过ID批量删除对象,选择全部或本页")
	@RequestMapping(value="/removeCiBatch",method=RequestMethod.POST)
	@ModDesc(desc = "通过ID批量删除对象,选择全部或本页", pDesc = "对象的ID集合", pType = List.class, pcType = Long.class, rDesc = "1成功0失败", rType = Integer.class)
	public ApiResult<Integer> removeCiBatch(HttpServletRequest request, HttpServletResponse response, @RequestBody CIRemoveBatchDto dto) {

		return ApiResult.ok(this).data(ciSvc.removeCiBatch(dto));
	}


	@ApiOperation("获取关联ci对应属性值列表")
	@RequestMapping(value="/getAttrValuesBySearchBean",method = RequestMethod.POST)
	@ModDesc(desc = "获取关联ci对应属性值列表", pDesc = "属性条件查询对象", pType = ESAttrAggBean.class, rDesc = "属性值分页查询结果", rType = Page.class, rcType = String.class)
	public ApiResult<Page<String>> getAttrValuesBySearchBean(HttpServletRequest request, HttpServletResponse response, @RequestBody ESAttrAggBean bean) {
		return  ApiResult.ok(this).data(ciSvc.getAttrValuesBySearchBean(bean));
	}
}
