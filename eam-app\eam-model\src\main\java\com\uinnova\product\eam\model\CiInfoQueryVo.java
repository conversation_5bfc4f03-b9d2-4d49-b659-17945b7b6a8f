package com.uinnova.product.eam.model;

import com.binary.framework.bean.annotation.Comment;
import com.uino.bean.cmdb.base.LibType;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;

@Data
public class CiInfoQueryVo {

    @Comment("ciCode")
    private String ciCode;

    @Comment("用户标识")
    private String ownerCode;

    @Comment("版本")
    private Long version;

    @NotEmpty(message = "不能为空")
    private List<String> ciCodes;

    @NotNull(message = "不能为空")
    private LibType libType;

    public CiInfoQueryVo() {
    }

    public CiInfoQueryVo(String ciCode, Long version, String ownerCode, LibType libType) {
        this.libType = libType;
        this.version = version;
        this.ownerCode = ownerCode;
        this.ciCode = ciCode;
    }
}
