package com.uinnova.product.eam.feign.client;

import com.uinnova.product.eam.base.diagram.model.ESDiagramShareRecordResult;
import com.uinnova.product.eam.base.diagram.model.ShareRecordQueryBean;
import com.uinnova.product.eam.feign.EamFeignConst;
import com.uinnova.product.eam.feign.config.EamFeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 视图分享Feign接口
 */
@FeignClient(name = EamFeignConst.SERVER_NAME, path = EamFeignConst.SERVER_ROOT + "/design/shareDiagram",
        configuration = EamFeignConfig.class)
public interface EamShareDesignDiagramFeign {

    /**
     * 查找分享记录
     *
     * @param queryBean {@link ShareRecordQueryBean}
     * @return {@link ESDiagramShareRecordResult}
     */
    @PostMapping("/feign/querySharedDiagramPage")
    List<ESDiagramShareRecordResult> queryShare(@RequestBody ShareRecordQueryBean queryBean);
}
