package com.uinnova.product.eam.model.vo;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.comm.dto.ConfigListDto;
import com.uinnova.product.eam.comm.model.AppSystemQueryConParam;
import lombok.Data;

import java.util.List;
@Data
public class EamAppConfigVo {

    @Comment("配置项id")
    private Long id;

    @Comment("所属域")
    private Long domainId;

    @Comment("卡片id")
    private Long cardId;

    @Comment("分类的classCode")
    private String classCode;

    @Comment("配置项属性数据集")
    private List<ConfigListDto> configLists;

    @Comment("创建时间")
    private Long createTime;

    @Comment("修改时间")
    private Long modifyTime;

    @Comment("创建人")
    private String creator;

    @Comment("修改人")
    private String modifier;

}
