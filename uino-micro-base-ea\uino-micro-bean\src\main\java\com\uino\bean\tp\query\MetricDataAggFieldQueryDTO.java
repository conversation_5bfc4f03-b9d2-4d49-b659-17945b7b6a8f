package com.uino.bean.tp.query;

import com.uino.bean.permission.business.IValidDto;
import lombok.Data;
import org.springframework.util.Assert;

import java.util.List;

/**
 * 性能数据聚合查询
 * @author: weixuesong
 * @date: 2021/03/08 17:12
 **/
@Data
public class MetricDataAggFieldQueryDTO implements IValidDto {
    /**
     * 取前几个
     */
    private int topN = 10;
    /**
     * 聚合字段
     */
    private String aggField;
    /**
     * 排序模式，avg, max, min, sum
     */
    private String sortMod = "avg";
    /**
     * 是否正序
     */
    private Boolean ifAsc = false;

    private Long startTime;
    private Long endTime;

    private List<Condition> conditions;

    private String metric;

    @Data
    public static class Condition {
        private String attrKey;
        private String attrValue;
    }

    @Override
    public void valid() {
        Assert.notNull(aggField, "aggField不能为空");
        Assert.notNull(sortMod, "sortMod不能为空");
        Assert.notNull(startTime, "startTime不能为空");
        Assert.notNull(endTime, "endTime不能为空");
        Assert.notNull(metric, "metric不能为空");
        Assert.isTrue(endTime > startTime, "endTime必须大于startTime");
        if (conditions == null) {
            return;
        }
        for (Condition condition : conditions) {
            Assert.notNull(condition.getAttrKey(), "attrKey不能为空");
            Assert.notNull(condition.getAttrValue(), "attrValue不能为空");
        }

    }
}
