package com.uino.service.cmdb.dataset.microservice.impl;

import com.uino.util.sys.SysUtil;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.cmdb.base.dataset.style.DataSetPriorityDisplay;
import com.uino.bean.cmdb.base.dataset.style.DisplayType;
import com.uino.dao.cmdb.dataset.ESDataSetPriorityDisplaySvc;
import com.uino.service.cmdb.dataset.microservice.IDataSetPriorityDisplaySvc;
import com.uino.dao.util.ESUtil;

import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Classname DataSetPriorityDisplayServiceImpl
 * @Description 优先显示
 * @Date 2020/3/30 11:22
 * @Created by sh
 */
@Service
public class DataSetPriorityDisplaySvc implements IDataSetPriorityDisplaySvc {

    @Autowired
    private ESDataSetPriorityDisplaySvc dataSetPriorityDisplaySvc;

    @Override
    public boolean priorityDisplay(Long dataSetId, int displayType) {
        SysUser user = SysUtil.getCurrentUserInfo();
        BoolQueryBuilder dataSetPriorityDisplayQuery = QueryBuilders.boolQuery();
        dataSetPriorityDisplayQuery.must(QueryBuilders.termQuery("userCode.keyword", user.getLoginCode()));
        dataSetPriorityDisplayQuery.must(QueryBuilders.termQuery("domainId", user.getDomainId()));
        dataSetPriorityDisplayQuery.must(QueryBuilders.termQuery("dataSetMallApiId", dataSetId));
        List<DataSetPriorityDisplay> priorityDisplayList = dataSetPriorityDisplaySvc.getListByQuery(dataSetPriorityDisplayQuery);
        if (priorityDisplayList.isEmpty()) {
            //新增
            DataSetPriorityDisplay dataSetPriorityDisplay = new DataSetPriorityDisplay();
            dataSetPriorityDisplay.setId(ESUtil.getUUID());
            dataSetPriorityDisplay.setUserCode(user.getLoginCode());
            dataSetPriorityDisplay.setDomainId(user.getDomainId());
            dataSetPriorityDisplay.setDataSetMallApiId(dataSetId);
            dataSetPriorityDisplay.setDisplay(DisplayType.valueOf(displayType).getCode());
            dataSetPriorityDisplay.setCreateTime(System.currentTimeMillis());
            dataSetPriorityDisplay.setModifyTime(System.currentTimeMillis());
            dataSetPriorityDisplaySvc.saveOrUpdate(dataSetPriorityDisplay);
        } else {
            DataSetPriorityDisplay dataSetPriorityDisplay = priorityDisplayList.get(0);
            dataSetPriorityDisplay.setDisplay(DisplayType.valueOf(displayType).getCode());
            dataSetPriorityDisplaySvc.saveOrUpdate(dataSetPriorityDisplay);
        }
        return true;
    }
}
