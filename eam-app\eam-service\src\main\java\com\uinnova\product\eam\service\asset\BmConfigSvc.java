package com.uinnova.product.eam.service.asset;

import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 */
public interface BmConfigSvc {

    /**
     * 获取业务组件建模配置
     * @return
     */
    public JSONObject getBusComponentConfig();
    /**
     * 业务建模活动图，展示图标
     * @return
     */
    JSONObject checkShowTaskTag();

    /**
     * 业务建模视图配置信息查询
     * @param val
     * @return
     */
    String getConfigType(String val);

    Long saveOrUpdateConfType(String confType, String confJson,String confName);
}
