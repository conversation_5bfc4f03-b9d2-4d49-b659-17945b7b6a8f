package com.uinnova.product.eam.model;

import java.io.Serializable;
import java.util.List;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.comm.model.VcDiagram;
import com.uinnova.product.eam.comm.model.VcDiagramDir;

public class DiagramDirTree implements Serializable{

	private static final long serialVersionUID = 1L;

	@Comment("目录ID")
	private Long id;

	@Comment("目录名称")
	private String dirName;
	
	@Comment("目录名称")
	private String label;

	@Comment("目录父亲ID")
	private Long parentId;

	@Comment("目录父亲子节点")
	private List<DiagramDirTree> children;
	
	@Comment("目录path")
	private String dirPath;
	
	private VcDiagramDir diagramDir;
	
	private List<VcDiagram> diagrams;
	
	public String getDirPath() {
		return diagramDir.getDirPath();
	}

	public void setDirPath(String dirPath) {
		this.dirPath = dirPath;
	}
	
	public DiagramDirTree(VcDiagramDir diagramDir) {
		this.diagramDir = diagramDir;
	}

	public Long getId() {
		return diagramDir.getId();
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getDirName() {
		return diagramDir.getDirName();
	}

	public void setDirName(String dirName) {
		this.dirName = dirName;
	}

	public List<DiagramDirTree> getChildren() {
		return children;
	}

	public void setChildren(List<DiagramDirTree> children) {
		this.children = children;
	}

	public Long getParentId() {
		return diagramDir.getParentId();
	}

	public void setParentId(Long parentId) {
		this.parentId = parentId;
	}

	public String getLabel() {
		return diagramDir.getDirName();
	}

	public void setLabel(String label) {
		this.label = label;
	}

	public List<VcDiagram> getDiagrams() {
		return diagrams;
	}

	public void setDiagrams(List<VcDiagram> diagrams) {
		this.diagrams = diagrams;
	}
	
	
	
}
