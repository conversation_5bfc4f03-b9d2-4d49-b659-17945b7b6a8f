package com.uinnova.product.vmdb.provider.sys.bean;

import com.alibaba.fastjson.JSONObject;

public class MailLog {

	private Long id;
	private String mail;
	private Long domainId;
	private String loginCode;
	private Long sendTime;
	private String verifyCode;
	
	public MailLog() {
		
	}
	
	public MailLog(JSONObject json) {
		if (json.containsKey("id") && json.getLong("id")!=null) {
			this.id = json.getLong("id");
		}
		if (json.containsKey("mail") && json.getString("mail")!=null && !"".equals(json.getString("mail").trim())) {
			this.mail = json.getString("mail").trim();
		}
		if (json.containsKey("domainId") && json.getLong("domainId")!=null) {
			this.domainId = json.getLong("domainId");
		}
		if (json.containsKey("loginCode") && json.getString("loginCode")!=null && !"".equals(json.getString("loginCode").trim())) {
			this.loginCode = json.getString("loginCode").trim();
		}
		if (json.containsKey("sendTime") && json.getLong("sendTime")!=null) {
			this.sendTime = json.getLong("sendTime");
		}
		if (json.containsKey("verifyCode") && json.getString("verifyCode")!=null && !"".equals(json.getString("verifyCode").trim())) {
			this.verifyCode = json.getString("verifyCode").trim();
		}
	}
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getMail() {
		return mail;
	}
	public void setMail(String mail) {
		this.mail = mail;
	}
	public Long getDomainId() {
		return domainId;
	}
	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}
	public String getLoginCode() {
		return loginCode;
	}
	public void setLoginCode(String loginCode) {
		this.loginCode = loginCode;
	}
	public Long getSendTime() {
		return sendTime;
	}
	public void setSendTime(Long sendTime) {
		this.sendTime = sendTime;
	}
	public String getVerifyCode() {
		return verifyCode;
	}
	public void setVerifyCode(String verifyCode) {
		this.verifyCode = verifyCode;
	}
	
	public JSONObject toJson() {
		JSONObject json = new JSONObject();
		json.put("id", this.id);
		json.put("mail", this.mail);
		json.put("domainId", this.domainId);
		json.put("loginCode", this.loginCode);
		json.put("sendTime", this.sendTime);
		json.put("verifyCode", this.verifyCode);
		return json;
	}
}
