package com.uino.role.svc;

import java.util.LinkedList;
import java.util.List;

import com.uino.dao.BaseConst;
import org.junit.Before;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import com.uino.service.permission.microservice.impl.RoleSvc;
import com.uino.dao.permission.rlt.ESUserModuleRltSvc;
import com.uino.bean.permission.base.SysUserModuleRlt;

@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("provider-local")
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class AddUserMenuRltTest {
	@Autowired
	private RoleSvc testSvc;
	@MockBean
	private ESUserModuleRltSvc umRltSvc;

	@Before
	public void before() {
		Mockito.when(umRltSvc.saveOrUpdateBatch(Mockito.anyList())).thenReturn(1);
		Mockito.when(umRltSvc.deleteByQuery(Mockito.any(), Mockito.anyBoolean())).thenReturn(1);

	}

	@Test
	public void test01() {
		List<SysUserModuleRlt> req = new LinkedList<>();
		SysUserModuleRlt reqNode01 = SysUserModuleRlt.builder().userId(1L).moduleId(1L).build();
		req.add(reqNode01);
		testSvc.addUserMenuRlt(BaseConst.DEFAULT_DOMAIN_ID,req);
	}

	@Test
	public void test02() {
		List<SysUserModuleRlt> req = new LinkedList<>();
		SysUserModuleRlt reqNode01 = SysUserModuleRlt.builder().userId(1L).moduleId(0L).build();
		req.add(reqNode01);
		testSvc.addUserMenuRlt(BaseConst.DEFAULT_DOMAIN_ID,req);
	}

	@Test(expected = IllegalArgumentException.class)
	public void test03() {
		List<SysUserModuleRlt> req = new LinkedList<>();
		SysUserModuleRlt reqNode01 = SysUserModuleRlt.builder().moduleId(1L).build();
		req.add(reqNode01);
		testSvc.addUserMenuRlt(BaseConst.DEFAULT_DOMAIN_ID,req);
	}

	@Test(expected = IllegalArgumentException.class)
	public void test04() {
		List<SysUserModuleRlt> req = new LinkedList<>();
		SysUserModuleRlt reqNode01 = SysUserModuleRlt.builder().userId(1L).build();
		req.add(reqNode01);
		testSvc.addUserMenuRlt(BaseConst.DEFAULT_DOMAIN_ID,req);
	}
}
