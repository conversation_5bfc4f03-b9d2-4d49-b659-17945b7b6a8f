package com.uino.util.cache;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 缓存接口
 */
public interface ICacheService {

    /**
     * 保存缓存（默认永不失效）
     * @param key key
     * @param value key
     */
    void setCache(String key, Object value);

    /**
     * 批量保存
     * @param params
     */
    void setCacheBatch(Map<String, Object> params);

    /**
     * 保存缓存（设置超时时间）
     * @param key key
     * @param value key
     * @param milliseconds 超时时间，单位：ms
     */
    void setCache(String key, Object value, long milliseconds);

    /**
     * 查询缓存
     * @param key
     * @return
     */
    Object getCache(String key);

    /**
     * 查询缓存-根据类型
     * @param key
     * @param clazz
     * @return
     */
    <T> T getCacheByType(String key, Class<T> clazz);

    /**
     * 删除缓存
     * @param key
     */
    void delKey(String key);

    /**
     * 删除缓存
     * @param keys
     */
    void delKeys(String ... keys);

    /**
     * 模糊查询结果
     * @param patternKey 需要自己拼装查询样式，例如："*zhang"、"zhang*"、"*zhang*"等
     * @param clazz
     * @return
     */
    <T> List<T> listByPattern(String patternKey, Class<T> clazz);

    /**
     * 多个key查询
     * @param keys
     * @param clazz
     * @return
     */
    <T> List<T> listByKeys(String[] keys, Class<T> clazz);

    /**
     * 获取所有key
     * @return
     */
    Set<String> getAllKeys();

    /**
     * 查询符合条件的key
     * @param key
     * @return
     */
    Set<String> getKeysByPattern(String key);

    /**
     * 存储到set中
     * @param key
     * @param value
     */
    void setAddValue(String key, Object value);

    /**
     * 批量存储set
     * @param key
     * @param values
     */
    void setAddValues(String key, Set<Object> values);

    /**
     * 删除set中值
     * @param key
     * @param value
     */
    void setRemoveValue(String key, Object value);

    /**
     * 查询set值
     * @param key
     * @return
     */
    <T> Set<T> setByKey(String key);

    /**
     * 获取键值
     * @param key 键值
     * @return 旧值
     */
    Long atomicGet(String key);

    /**
     * 设置键值，并返回旧值，原子操作
     * @param key 键值
     * @param value 值
     * @return 旧值
     */
    Long getAndSet(String key, Long value);

    /**
     * 原子自增并返回
     * @param key 键值
     * @return value值
     */
    Long incrementAndGet(String key);

    /**
     * 保存缓存（默认永不失效）
     * @param key key
     * @param milliseconds key
     */
    void setTTL(String key, long milliseconds);
}
