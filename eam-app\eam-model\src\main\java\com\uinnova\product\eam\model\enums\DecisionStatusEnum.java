package com.uinnova.product.eam.model.enums;

/**
 * 决策状态
 * <AUTHOR>
 */
public enum DecisionStatusEnum {

    /**
     * 待受理
     */
    TO_BE_ACCEPTED(1),
    /**
     * 重新申请
     */
    REAPPLY(2),
    /**
     * 待决策
     */
    WAITING_DECISION(3),
    /**
     * 待发布
     */
    TO_BE_RELEASED(4),

    /**
     * 已发布
     */
    RELEASED(5);

    int val;

    DecisionStatusEnum(int val) {
        this.val = val;
    }

    public int val() {
        return this.val;
    }
}
