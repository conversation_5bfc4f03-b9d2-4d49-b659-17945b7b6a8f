package com.uino.bean.cmdb.base.dataset;

import java.io.Serializable;
import java.util.List;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;

/**
 * @Classname DataSetMallApi
 * @Description 数据集父类
 * @Date 2020/3/18 17:05
 * @Created by sh
 */
public abstract class DataSetMallApi implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    private String name;
    private String description;
    private int type;
    private Long domainId;
    private String creater;
    private String modifier;
    private Long createTime;
    private Long modifyTime;
    private List<Long> dataSetLabel;
    //分享权限等级
    private int shareLevel;
    //卡片icon
    private String image;
    //数据超市缩略图
    private String thumbnail;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Long getDomainId() {
        return domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }

    public String getCreater() {
        return creater;
    }

    public void setCreater(String creater) {
        this.creater = creater;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getShareLevel() {
        return shareLevel;
    }

    public void setShareLevel(int shareLevel) {
        this.shareLevel = shareLevel;
    }

    public List<Long> getDataSetLabel() {
        return dataSetLabel;
    }

    public void setDataSetLabel(List<Long> dataSetLabel) {
        this.dataSetLabel = dataSetLabel;
    }

    public String getImage() {
        return image;
    }

    public void setImage(String image) {
        this.image = image;
    }

    public String getThumbnail() {
        return thumbnail;
    }

    public void setThumbnail(String thumbnail) {
        this.thumbnail = thumbnail;
    }

    public DataSetMallApi() {
    }

    public DataSetMallApi(JSONObject json) {
        if (json.containsKey("id")) {
            this.id = json.getLong("id");
        }
        if (json.containsKey("name")) {
            this.name = json.getString("name");
        }
        if (json.containsKey("description")) {
            this.description = json.getString("description");
        }
        if (json.containsKey("type")) {
                this.type = json.getInteger("type");
        }
        if (json.containsKey("domainId")) {
            this.domainId = json.getLong("domainId");
        }
        if (json.containsKey("creater")) {
            this.creater = json.getString("creater");
        }
        if (json.containsKey("modifier")) {
            this.modifier = json.getString("domainId");
        }
        if (json.containsKey("createTime")) {
            this.createTime = json.getLong("createTime");
        }
        if (json.containsKey("modifyTime")) {
            this.modifyTime = json.getLong("modifyTime");
        }
        //分享等级
        if (json.containsKey("shareLevel")) {
                this.shareLevel = json.getInteger("shareLevel");
        }
        if (json.containsKey("dataSetLabel")) {
            this.dataSetLabel = JSON.parseObject(json.getString("dataSetLabel"), new TypeReference<List<Long>>() {});
        }
        if (json.containsKey("image")) {
            this.image = json.getString("image");
        }
        if (json.containsKey("thumbnail")) {
            this.thumbnail = json.getString("thumbnail");
        }
    }

    public JSONObject toJson() {
        JSONObject json = new JSONObject();
        json.put("id", id);
        json.put("name", name);
        json.put("description", description);
        json.put("type", type);
        json.put("domainId", domainId);
        json.put("creater", creater);
        json.put("modifier", modifier);
        json.put("createTime", createTime);
        json.put("modifyTime", modifyTime);
        json.put("shareLevel", shareLevel);
        json.put("dataSetLabel", dataSetLabel);
        json.put("image", image);
        json.put("thumbnail", thumbnail);
        return json;
    }
}