package com.uinnova.product.eam.model.cj.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 方案预览VO
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PlanPreviewVO {

    /**
     * 方案名称
     */
    private String planName;

    /**
     * 方案部门
     */
    private String planDept;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 章节数据
     */
    private List<PlanChapterVO> chapterList;
}
