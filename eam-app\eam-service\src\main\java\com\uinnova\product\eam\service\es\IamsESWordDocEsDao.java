package com.uinnova.product.eam.service.es;

import com.uinnova.product.eam.comm.bean.WordDoc;
import com.uino.dao.AbstractESBaseDao;
import com.uino.service.util.FileUtil;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.admin.indices.create.CreateIndexRequest;
import org.elasticsearch.action.admin.indices.get.GetIndexRequest;
import org.elasticsearch.action.admin.indices.settings.put.UpdateSettingsRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.stereotype.Repository;

import jakarta.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;

/**
 * 文档管理数据访问层
 *
 * <AUTHOR>
 */
@Deprecated
@Repository
@Slf4j
public class IamsESWordDocEsDao extends AbstractESBaseDao<WordDoc, WordDoc> {
    @Override
    public String getIndex() {
        return "uino_eam_word_doc";
    }

    @Override
    public String getType() {
        return "_doc";
    }

    @PostConstruct
    public void init() {
        // 仅在初始化索引时添加数据
        List<WordDoc> data = FileUtil.getData("/initdata/uino_eam_word_doc.json", WordDoc.class);
        super.initIndex(data);
    }
}
