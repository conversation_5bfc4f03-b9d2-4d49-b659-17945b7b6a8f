package com.uinnova.product.vmdb.comm.util;

import com.binary.core.exception.MessageException;
import com.binary.core.util.BinaryUtils;
import com.binary.json.JSON;

import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import java.io.Serializable;
import java.util.*;

/**
 * 
 * <AUTHOR>
 *
 */
public class CiQualityRuleExp {
    public static final String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    /**
     * 零时的前缀用于排序
     */
    public static final String TMP_PREFIX = "==";
    public static final ScriptEngineManager manager = new ScriptEngineManager();

    public static class CiQualityVeracityAloneRlt {
        private String name;
        private Long rltClsId;
        private Long rltType;
        private Integer check;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public Long getRltClsId() {
            return rltClsId;
        }

        public void setRltClsId(Long rltClsId) {
            this.rltClsId = rltClsId;
        }

        public Long getRltType() {
            return rltType;
        }

        public void setRltType(Long rltType) {
            this.rltType = rltType;
        }

        public Integer getCheck() {
            return check;
        }

        public void setCheck(Integer check) {
            this.check = check;
        }

    }

    public static class CiQualityVeracityAlone {
        private String exp;
        private List<CiQualityVeracityAloneRlt> rlts;

        public String getExp() {
            return exp;
        }

        public void setExp(String exp) {
            this.exp = exp;
        }

        public List<CiQualityVeracityAloneRlt> getRlts() {
            return rlts;
        }

        public void setRlts(List<CiQualityVeracityAloneRlt> rlts) {
            this.rlts = rlts;
        }

        public void validate() {
            sork();

            Set<String> expChars = getExpChars();
            if (!BinaryUtils.isEmpty(this.exp)) {
                ScriptEngine engine = manager.getEngineByName("js");
                for (String cr : expChars) {
                    if (cr.charAt(0) != '|' && cr.charAt(0) != '&' && cr.charAt(0) != '(' && cr.charAt(0) != ')') {
                        engine.put(cr.toUpperCase(), true);
                    }
                }
                try {
                    String expt = exp.replaceAll("&", "&&");
                    expt = expt.replaceAll("\\|", "||");
                    engine.eval(expt);
                } catch (Exception e) {
                    throw MessageException.i18n("CC_CI_QUALITY_EXP_ERROR");
                }
            }

            Map<String, CiQualityVeracityAloneRlt> nameMap = new HashMap<String, CiQualityVeracityAloneRlt>();
            for (int i = 0; i < rlts.size(); i++) {
                CiQualityVeracityAloneRlt rlt = rlts.get(i);
                nameMap.put(rlt.getName(), rlt);
                if (expChars.contains(rlt.getName())) {
                    rlt.setCheck(1);
                } else {
                    rlt.setCheck(0);
                }
            }
            Set<String> keySet = nameMap.keySet();
            for (String cr : expChars) {
                if (cr.charAt(0) != '|' && cr.charAt(0) != '&' && cr.charAt(0) != '(' && cr.charAt(0) != ')') {
                    if (!keySet.contains(cr)) {
                        throw MessageException.i18n("CC_CI_QUALITY_EXP_ERROR");
                    }
                }
            }
        }

        public String getTrimExp() {
            return exp == null ? "" : exp.toUpperCase().replace(" ", "");
        }

        public Set<String> getExpChars() {
            Set<String> chars = new HashSet<String>();
            if (!BinaryUtils.isEmpty(this.exp)) {
                char[] charArray = exp.toCharArray();
                for (char c : charArray) {
                    chars.add(String.valueOf(c));
                }
            }
            return chars;
        }

        private void sork() {
            if (BinaryUtils.isEmpty(rlts) && BinaryUtils.isEmpty(exp)) {
                return;
            }
            String exp = this.exp == null ? "" : this.exp.toUpperCase().replace(" ", "");
            boolean emptyExp = false;

            if ("".equals(exp)) {
                this.exp = "";
                emptyExp = true;
            }

            Set<String> names = new HashSet<String>();
            Map<String, CiQualityVeracityAloneRlt> rltMap = new HashMap<String, CiQualityVeracityAloneRlt>();
            for (int i = 0; i < rlts.size(); i++) {
                CiQualityVeracityAloneRlt rlt = rlts.get(i);
                String name = rlt.getName();
                String pName = TMP_PREFIX + name;
                if (names.contains(pName)) {
                    throw MessageException.i18n("BS_CC_QUALITY_RULE_ITEM_EXP_DUPLICATE", "\"name\":\"" + name + "\"");
                }
                names.add(pName);
                rltMap.put(pName, rlt);
                if (!emptyExp) {
                    this.exp = this.exp.replaceAll(name, pName);
                }
            }
            ArrayList<String> nameList = new ArrayList<String>(names);
            Collections.sort(nameList);
            ArrayList<CiQualityVeracityAloneRlt> newRlts = new ArrayList<CiQualityVeracityAloneRlt>();

            for (int i = 0; i < nameList.size(); i++) {
                String pName = nameList.get(i);
                String newName = chars.charAt(i) + "";
                CiQualityVeracityAloneRlt rlt = rltMap.get(pName);
                rlt.setName(newName);
                if (!emptyExp) {
                    this.exp = this.exp.replaceAll(pName, newName);
                }
                newRlts.add(rlt);
            }

            this.rlts = newRlts;
        }

    }

    public static class CiQualityVeracityOverdueRuleExp implements Serializable {
        private static final long serialVersionUID = 1L;

        private Integer day;
        private Integer hour;
        private Integer minute;

        public Integer getDay() {
            return day;
        }

        public void setDay(Integer day) {
            this.day = day;
        }

        public Integer getHour() {
            return hour;
        }

        public void setHour(Integer hour) {
            this.hour = hour;
        }

        public Integer getMinute() {
            return minute;
        }

        public void setMinute(Integer minute) {
            this.minute = minute;
        }

    }

    public static CiQualityVeracityOverdueRuleExp getVeracityOverdueRuleExp(String exp) {
        CiQualityVeracityOverdueRuleExp expRule = JSON.toObject(exp, CiQualityVeracityOverdueRuleExp.class);
        Integer day = expRule.getDay();
        Integer hour = expRule.getHour();
        Integer minute = expRule.getMinute();

        day = day == null ? 0 : day;
        hour = hour == null ? 0 : hour > 24 ? 23 : hour;
        minute = minute == null ? 0 : minute > 60 ? 59 : minute;
        expRule.setMinute(minute);
        expRule.setHour(hour);
        expRule.setDay(day);

        return expRule;
    }

    public static Long getVeracityOverdueRuleExpTime(String exp) {
        CiQualityVeracityOverdueRuleExp veracityAloneRuleExp = getVeracityOverdueRuleExp(exp);
        Integer day = veracityAloneRuleExp.getDay();
        Integer hour = veracityAloneRuleExp.getHour();
        Integer minute = veracityAloneRuleExp.getMinute();
        long time = 0;
        time += minute * 60 * 1000;
        time += hour * 60 * 60 * 1000;
        time += day * 24 * 60 * 60 * 1000;
        long aa = System.currentTimeMillis() - time;
        return BinaryUtils.getNumberDateTime(new Date(aa));
    }

    public static CiQualityVeracityAlone getVeracityAloneRuleExp(String exp) {
        CiQualityVeracityAlone alone = JSON.toObject(exp, CiQualityVeracityAlone.class);
        alone.validate();
        return alone;
    }

    public static String validateExp(CiQualityRuleType ruleTypeEnum, String rltExp) {
        if (ruleTypeEnum == null) {
            return null;
        } else if (ruleTypeEnum == CiQualityRuleType.VERACITYALONE) {
            CiQualityVeracityAlone alone = JSON.toObject(rltExp, CiQualityVeracityAlone.class);
            alone.validate();
            return JSON.toString(alone);
        } else if (ruleTypeEnum == CiQualityRuleType.VERACITYOVERDUE) {
            CiQualityVeracityOverdueRuleExp exp = getVeracityOverdueRuleExp(rltExp);
            return JSON.toString(exp);
        }
        return rltExp;
    }
}
