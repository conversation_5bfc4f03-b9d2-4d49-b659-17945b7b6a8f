package com.uinnova.product.eam.service.diagram.impl;

import com.binary.core.exception.BinaryException;
import com.binary.core.util.BinaryUtils;
import com.binary.jdbc.Page;
import com.uinnova.product.eam.base.diagram.exception.UnAuthorizedException;
import com.uinnova.product.eam.base.diagram.model.*;
import com.uinnova.product.eam.db.diagram.es.ESDiagramDao;
import com.uinnova.product.eam.db.diagram.es.ESShareDiagramDao;
import com.uinnova.product.eam.service.diagram.ESDiagramSvc;
import com.uinnova.product.eam.service.diagram.ESShareDesignDiagramSvc;
import com.uinnova.product.eam.service.diagram.IEamShareDiagramSvc;
import com.uino.api.client.permission.IUserApiSvc;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.permission.query.CSysUser;
import com.uino.util.sys.SysUtil;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static java.util.Comparator.comparing;

@Service
public class ESShareDesignDiagramSvcImpl implements ESShareDesignDiagramSvc {

    @Autowired
    private ESShareDiagramDao esShareDiagramDao;

    @Value("${http.resource.space}")
    private String httpResouceUrl;

    @Autowired
    private IUserApiSvc userApiSvc;

    @Autowired
    private ESDiagramSvc esDiagramSvc;

    @Autowired
    private ESDiagramDao esDiagramDao;

    @Autowired
    private IEamShareDiagramSvc eamShareDiagramSvc;

    @Override
    public Page<ESDiagramShareRecordResult> queryShareRecordPage(ShareRecordQueryBean shareRecordQueryBean) {
        Page<ESDiagramShareRecordResult> diagramShareRecordPage = new Page<>();
        String order = shareRecordQueryBean.getOrder();
        Integer pageNum = shareRecordQueryBean.getPageNum();
        Integer pageSize = shareRecordQueryBean.getPageSize();
        String word = shareRecordQueryBean.getLike();
        Long totalRows = 0L;
        Long totalpages = 0L;

        BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
        queryBuilder.must(QueryBuilders.termQuery("sharedUserId", shareRecordQueryBean.getSharedUserId()));
//        queryBuilder.must(QueryBuilders.termQuery("dirType", shareRecordQueryBean.getDirType()));
        List<DiagramShareRecord> allShareRecordList = new ArrayList<>();
        if("modifyTime".equals(order)) {
            //查出当前用户拥有的所有分享记录
            allShareRecordList = esShareDiagramDao.getListByQuery(queryBuilder);
        } else if("shareTime".equals(order)) {
            //TODO 按照视图的分享时间排序
        } else {
            throw new BinaryException("排序字段不符合规定");
        }

        if (!CollectionUtils.isEmpty(allShareRecordList)) {
            diagramShareRecordPage.setPageNum(pageNum);
            diagramShareRecordPage.setPageSize(pageSize);
            diagramShareRecordPage.setTotalRows(totalRows);
            diagramShareRecordPage.setTotalPages(totalpages);
            // List<ESDiagramShareRecordResult> resultList = fillSysOPAndDiagramInfoForQuery(allShareRecordList, pageNum, pageSize, word, diagramShareRecordPage, order);
            List<ESDiagramShareRecordResult> resultList = new ArrayList<>();
            //按照修改时间排序
            resultList.sort(comparing(ESDiagramShareRecordResult::getDiagramModifyTime).reversed());
            diagramShareRecordPage.setData(resultList);
        }

        return diagramShareRecordPage;
    }

    @Override
    public Map<Long, List<ESDiagramShareRecordResult>> queryDiagramShareRecords(String[] diagramIds, Boolean needAuth) {
        /*
         * 根据视图id查询该视图的分享记录
         * 1.查询视图的分享记录
         * 2.填充用户信息
         * */
        //查出目标视图基础信息
        Map<Long, ESDiagram> diagramIdMap = new HashMap<>();
        Long[] ids = esDiagramSvc.queryDiagramInfoBydEnergy(diagramIds);
        List<ESDiagram> esDiagramList = judgeDiagramAuth(ids, null, needAuth);
        for (ESDiagram esDiagram : esDiagramList) {
            Long diagramId = esDiagram.getId();
            String icon1 = esDiagram.getIcon1();
            if (icon1 != null && !icon1.startsWith(httpResouceUrl)) {
                esDiagram.setIcon1(new StringBuilder(httpResouceUrl).append(icon1).toString());
            }
            diagramIdMap.put(diagramId, esDiagram);
        }

        ShareRecordQuery shareRecordQuery = new ShareRecordQuery();
        shareRecordQuery.setDiagramIds(ids);
        List<DiagramShareRecord> shareRecordList = esShareDiagramDao.getListByCdt(shareRecordQuery);
        Map<Long, List<DiagramShareRecord>> diagramShareRecordMap = shareRecordList.stream().collect(Collectors.groupingBy(DiagramShareRecord::getDiagramId));

        //查询用户信息
        Set<Long> ownerIdSet = shareRecordList.stream().map(DiagramShareRecord::getOwnerId).collect(Collectors.toSet());
        Set<Long> shareIdSet = shareRecordList.stream().map(DiagramShareRecord::getSharedUserId).collect(Collectors.toSet());
        ownerIdSet.addAll(shareIdSet);
        CSysUser sysUser = new CSysUser();
        sysUser.setIds(ownerIdSet.toArray(new Long[0]));
        sysUser.setStatus(1);
        List<SysUser> sysUserByCdt = userApiSvc.getSysUserByCdt(sysUser);
        Map<Long, SysUser> sysOpMap = new HashMap<>();
        for (SysUser sysOp : sysUserByCdt) {
            Long id = sysOp.getId();
            sysOpMap.put(id, sysOp);
        }

        //封装返回结果
        Map<Long, List<ESDiagramShareRecordResult>> resultMap = new HashMap<>();
        for (Map.Entry<Long, List<DiagramShareRecord>> entry : diagramShareRecordMap.entrySet()) {
            List<ESDiagramShareRecordResult> shareRecordResultList = new ArrayList<>();
            for (DiagramShareRecord shareRecord : entry.getValue()) {
                //封装分享记录对应的视图信息
                ESDiagramShareRecordResult shareRecordResult = new ESDiagramShareRecordResult();
                BeanUtils.copyProperties(shareRecord, shareRecordResult);
                ESDiagram esDiagram = diagramIdMap.get(entry.getKey());
                shareRecordResult.setEsDiagram(esDiagram);
                shareRecordResult.setDiagramModifyTime(esDiagram.getModifyTime());

                //封装用户信息
                shareRecordResult.setOwnerSysUser(sysOpMap.get(shareRecordResult.getOwnerId()));
                shareRecordResult.setSharedSysUser(sysOpMap.get(shareRecordResult.getSharedUserId()));
                shareRecordResultList.add(shareRecordResult);
            }
            resultMap.put(entry.getKey(), shareRecordResultList);
        }
        return resultMap;
    }

/*
    private List<ESDiagramShareRecordResult> fillSysOPAndDiagramInfoForQuery(List<DiagramShareRecord> diagramShareRecords, Integer pageNum, Integer pageSize,
                                                                             String word, Page<ESDiagramShareRecordResult> recordResultPage, String order) {
        SysUser currUser = SysUtil.getCurrentUserInfo();
        Long domainId = currUser.getDomainId();
        Long userId = currUser.getId();

        List<ESDiagramShareRecordResult> diagramShareRecordResults = new ArrayList<>();
        //获取当前用户所有分享记录的视图id集合diagramIds
        Long[] diagramIdArr = diagramShareRecords
                                .stream()
                                .map(DiagramShareRecord::getDiagramId)
                                .distinct()
                                .toArray(Long[]::new);
        //根据diagramIds分页排序查询视图信息
        CVcDiagram cVcDiagram = new CVcDiagram();
        cVcDiagram.setIds(diagramIdArr);
        cVcDiagram.setStatus(1);
        cVcDiagram.setDataStatus(1);
        if(!StringUtils.isEmpty(word)) {
            cVcDiagram.setName(word);
        }
        Page<ESDiagram> esDiagramPage = esDiagramSvc.queryESDiagramPage(domainId, pageNum, pageSize, cVcDiagram, order);
        recordResultPage.setTotalRows(esDiagramPage.getTotalRows());
        recordResultPage.setTotalPages(esDiagramPage.getTotalPages());
        //对查询结果
        List<ESDiagram> diagramList = esDiagramPage.getData();
        Map<Long, ESDiagram> diagramIdObjMap = new HashMap<>();
        Set<Long> diagramIdResultSet = new HashSet<>();
        if (!CollectionUtils.isEmpty(diagramList)) {
            for (ESDiagram diagram : diagramList) {
                Long diagramId = diagram.getId();
                String icon1 = diagram.getIcon1();
                if (icon1 != null && !icon1.startsWith(httpResouceUrl)) {
                    diagram.setIcon1(new StringBuilder(httpResouceUrl).append(icon1).toString());
                }
                diagramIdResultSet.add(diagramId);
                diagramIdObjMap.put(diagramId, diagram);
            }
        }

        //获取视图拥有者id集合，并查出其详细用户信息
        Set<Long> userIdSet = diagramList.stream().map(ESDiagram::getUserId).collect(Collectors.toSet());
        userIdSet.add(SysUtil.getCurrentUserInfo().getId());
        CSysUser sysUser = new CSysUser();
        sysUser.setIds(userIdSet.toArray(new Long[0]));
        sysUser.setStatus(1);
        List<SysUser> sysUserByCdt = userApiSvc.getSysUserByCdt(sysUser);
        Map<Long, SysUser> sysOpMap = new HashMap<>(16);
        for (SysUser sysOp : sysUserByCdt) {
            sysOpMap.put(sysOp.getId(), sysOp);
        }

        //根据视图获取查询结果
        List<DiagramShareRecord> shareRecordResultList = diagramShareRecords
                                                            .stream()
                                                            .filter(record -> diagramIdResultSet.contains(record.getDiagramId()))
                                                            .collect(Collectors.toList());

        //封装返回结果
        for (DiagramShareRecord shareRecord : shareRecordResultList) {
            Long diagramId = shareRecord.getDiagramId();
            if (diagramIdObjMap.containsKey(diagramId)) {
                //封装分享记录对应的视图信息
                ESDiagramShareRecordResult shareRecordResult = new ESDiagramShareRecordResult();
                BeanUtils.copyProperties(shareRecord, shareRecordResult);
                ESDiagram diagram = diagramIdObjMap.get(diagramId);
                diagram.setReleaseStatus(0);
                if (diagram.getDirId() != null) {
                    if (diagram.getDirId() == 0 || diagram.getDirId() == 1) {
                        diagram.setRelationLocation("我的文件");
                    } else {
                        VcDiagramDir vcDiagramDir = diagramSvc.queryDiagramDirById(1L, diagram.getDirId());
                        if (vcDiagramDir != null && !StringUtils.isEmpty(vcDiagramDir.getDirName())) {
                            diagram.setRelationLocation(vcDiagramDir.getDirName());
                        }
                    }
                }
                shareRecordResult.setEsDiagram(diagram);
                shareRecordResult.setDiagramModifyTime(diagram.getModifyTime());
                //封装用户信息
                shareRecordResult.setOwnerSysUser(sysOpMap.get(shareRecordResult.getOwnerId()));
                shareRecordResult.setSharedSysUser(sysOpMap.get(shareRecordResult.getSharedUserId()));
                diagramShareRecordResults.add(shareRecordResult);
            }
        }
        return diagramShareRecordResults;
    }
*/

    /**
     * <AUTHOR>
     * @Description 判断用户是否具有当前视图的权限
     * 1.不限制userId进行查询
     *  查不出---视图被删除
     *  查出来---判断视图的userId是否等于当前用户
     * 		等于---有权限
     * 		不等于---查询该用户是否被分享了该视图
     * 			分享了，有两种权限，一种是查看，一种是编辑
     * 			没分享，无权限，报错
     **/
    private List<ESDiagram> judgeDiagramAuth(Long[] diagramIds, String type, Boolean needAuth) {
        Boolean isSingle = true;
        if (diagramIds.length != 1) {
            isSingle = false;
        }
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        Long userId = currentUserInfo.getId();
        Long domainId = currentUserInfo.getDomainId();

        //1.不限制userId进行查询
        EamDiagramQuery diagramQuery = new EamDiagramQuery();
        diagramQuery.setIds(diagramIds);
        diagramQuery.setStatus(1);
        diagramQuery.setDataStatus(1);
        diagramQuery.setDomainId(domainId);
        if(!BinaryUtils.isEmpty(type) && "version".equals(type)) {
            diagramQuery.setHistoryVersionFlag(0);
        } else {
            diagramQuery.setHistoryVersionFlag(1);
        }
        List<ESDiagram> diagramList = esDiagramDao.getListByCdt(diagramQuery);
        //2.查不出，视图不存在，报错
        if(BinaryUtils.isEmpty(diagramList)) {
            throw new BinaryException("操作视图不存在，请核对视图当前信息");
        }
        //3.查出来，判断其是否具有视图权限
        if (isSingle && needAuth) {
            ESDiagram esDiagram = diagramList.get(0);
            if (esDiagram.getDiagramType().equals(3)) {
                return diagramList;
            }
            if (esDiagram.getUserId().equals(userId)) {
                //视图属于当前用户
                return diagramList;
            } else {
                //视图不属于当前用户，判断该视图是否分享给了该用户
                Map<Long, Set<Long>> diagramIdShareRecordMap = eamShareDiagramSvc.queryDiagramSharedUserIds(diagramIds);
                Set<Long> shareUserIdSet = diagramIdShareRecordMap.get(diagramIds[0]);
                if (!BinaryUtils.isEmpty(shareUserIdSet) && shareUserIdSet.contains(userId)) {
                    //视图被分享给了当前用户
                    return diagramList;
                } else {
                    throw new UnAuthorizedException("用户无当前视图权限");
                }
            }
        }
        return diagramList;
    }
}
