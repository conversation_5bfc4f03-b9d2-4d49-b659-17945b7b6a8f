package com.uinnova.product.eam.model.cj.request;

import com.binary.framework.bean.annotation.Comment;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 方案设计分页查询请求参数
 * <AUTHOR>
 */
@Data
public class PlanChapterQuestionRequest implements Serializable{

    /**
     * 每页个数
     */
    @NotNull(message = "pageSize不能为空")
    private Integer pageSize;

    /**
     * 页码
     */
    @NotNull(message = "pageNum不能为空")
    private Integer pageNum;

    /**
     * 方案类型id
     */
    private Long planId;

    /**
     * 问题详情
     */
    private String question;

    /**
     * 流程实例id
     */
    private String processInstanceId;

    /**
     * 任务定义主键
     */
    private String taskDefinitionKey;
}
