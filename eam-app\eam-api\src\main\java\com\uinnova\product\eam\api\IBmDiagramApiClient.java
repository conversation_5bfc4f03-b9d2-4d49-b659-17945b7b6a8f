package com.uinnova.product.eam.api;

import com.uinnova.product.eam.model.asset.EamReleaseHistoryDTO;
import com.uinnova.product.eam.model.vo.CheckBatchArtifactRuleVo;
import com.uinnova.product.vmdb.provider.rlt.bean.CcCiRltInfo;

import java.util.List;

/**
 * Shaolong.fan
 */
public interface IBmDiagramApiClient {

    List<EamReleaseHistoryDTO> queryReleaseHistory(String id, Boolean historyFlag);

    boolean handlerRelease(String diagramId);

    List<CcCiRltInfo> getActivityListByTaskRlt(String diagramId, String ciCode, String libType);

    /**
     * 视图制品数量校验接口--批量
     * @param diagramIds
     * @return
     */
    List<CheckBatchArtifactRuleVo> categoryEleNumCheckBatch(List<String> diagramIds);
}
