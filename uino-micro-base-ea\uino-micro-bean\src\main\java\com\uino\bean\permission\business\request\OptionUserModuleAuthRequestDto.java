package com.uino.bean.permission.business.request;

import java.util.Set;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.util.Assert;

import com.uino.bean.permission.business.IValidDto;

import lombok.Data;

/**
 * 操作用户与模块权限数据传输类
 * 
 * <AUTHOR>
 *
 */
@Data
@ApiModel(value="操作用户与模块权限数据传输类",description = "操作用户与模块权限数据传输类")
public class OptionUserModuleAuthRequestDto implements IValidDto {
	/**
	 * 用户ids
	 */
	@ApiModelProperty(value="用户id集合")
	private Set<Long> userIds;

	/**
	 * 模块ids
	 */
	@ApiModelProperty(value="模块id集合")
	private Set<Long> moduleIds;

	/**
	 * 是否绑定true:绑定false:解绑
	 */
	@ApiModelProperty(value="是否绑定true:绑定false:解绑")
	private Boolean mapping;

	@Override
	public void valid() {
		Assert.notEmpty(userIds, "用户不得为空");
		Assert.notEmpty(moduleIds, "模块不得为空");
		Assert.notNull(mapping, "mapping不得为空");
	}
}
