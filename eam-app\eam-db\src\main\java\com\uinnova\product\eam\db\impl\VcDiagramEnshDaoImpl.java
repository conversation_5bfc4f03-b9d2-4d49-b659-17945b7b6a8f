package com.uinnova.product.eam.db.impl;


import com.uinnova.product.eam.comm.model.CVcDiagramEnsh;
import com.uinnova.product.eam.comm.model.VcDiagramEnsh;
import com.uinnova.product.eam.db.VcDiagramEnshDao;
import com.uinnova.product.eam.db.support.dao.mybatis.ComMyBatisBinaryDaoImpl;


/**
 * 视图收藏表[VC_DIAGRAM_ENSH]数据访问对象实现
 */
public class VcDiagramEnshDaoImpl extends ComMyBatisBinaryDaoImpl<VcDiagramEnsh, CVcDiagramEnsh> implements VcDiagramEnshDao {

//    @Override
//    public String getTableName() {
//        return "IAMS." + getDaoDefinition().getTableName();
//    }
}


