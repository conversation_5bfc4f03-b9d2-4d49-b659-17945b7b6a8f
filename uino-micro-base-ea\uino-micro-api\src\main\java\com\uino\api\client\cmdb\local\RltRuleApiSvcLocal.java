package com.uino.api.client.cmdb.local;

import com.binary.jdbc.Page;
import com.uino.service.cmdb.microservice.IRltRuleSvc;
import com.uino.bean.cmdb.base.ESRltRuleInfo;
import com.uino.api.client.cmdb.IRltRuleApiSvc;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @data 2019/8/1 10:10.
 */
@Service
public class RltRuleApiSvcLocal implements IRltRuleApiSvc {

    @Autowired
    private IRltRuleSvc rltRuleSvc;

    @Override
    public Long saveOrUpdate(ESRltRuleInfo rltRuleInfo) {
        return rltRuleSvc.saveOrUpdate(rltRuleInfo);
    }

    @Override
    public Integer deleteById(Long id) {
        return rltRuleSvc.deleteById(id);
    }

    @Override
    public ESRltRuleInfo queryInfoById(Long id) {
        return rltRuleSvc.queryInfoById(id);
    }

    @Override
    public List<ESRltRuleInfo> queryInfo(Long domainId) {
        return rltRuleSvc.queryInfo(domainId);
    }

    @Override
    public Page<ESRltRuleInfo> queryInfoPage(Integer pageNum, Integer pageSize, String name, Long domainId, String orders, boolean isAsc) {
        return rltRuleSvc.queryInfoPage(pageNum, pageSize, name, domainId, orders, isAsc);
    }

}
