package com.uinnova.product.eam.local;

import com.uinnova.product.eam.api.IEamCiApiSvc;
import com.uinnova.product.eam.model.CiQueryCdtExtend;
import com.uinnova.product.eam.model.VcCiClassInfoDto;
import com.uinnova.product.eam.service.IEamCiSvc;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uino.bean.cmdb.base.LibType;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.List;

@Service
public class IEamCiApiSvcLocal implements IEamCiApiSvc {

    @Resource
    private IEamCiSvc iEamCiSvc;

    @Override
    public List<VcCiClassInfoDto> queryCiCountByClass(CiQueryCdtExtend cdt, LibType libType) {
        return iEamCiSvc.queryCiCountByClass(cdt, libType);
    }

    @Override
    public List<CcCiInfo> getCiInfoListByClassName(String ciClassName, LibType libType) {
        return iEamCiSvc.getCiInfoListByClassName(ciClassName, libType);
    }
}
