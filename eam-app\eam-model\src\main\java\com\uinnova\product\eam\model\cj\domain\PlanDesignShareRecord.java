package com.uinnova.product.eam.model.cj.domain;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.base.cj.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@Comment("交付物实例分享记录表[UINO_CJ_PLAN_DESIGN_SHARE_RECORD]")
public class PlanDesignShareRecord extends BaseEntity {
    private Long id;
    @Comment("被分享的设计方案")
    private Long planDesignId;
    @Comment("分享人code")
    private String ownerLoginCode;
    @Comment("被分享人code")
    private String sharedLoginCode;
    @Comment("权限: 以枚举类为准com.uinnova.product.cj.enums.PlanSharePermissionEnum)")
    private Integer permission;
    @Comment("分享状态: 以枚举类为准com.uinnova.product.cj.enums.PlanShareEnum")
    private Integer status;
    @Comment("分享的时间")
    private Long shareTime;
}
