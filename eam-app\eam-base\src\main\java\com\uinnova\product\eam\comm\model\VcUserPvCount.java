package com.uinnova.product.eam.comm.model;




import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;


@Comment("用户行为计数表[VC_USER_PV_COUNT]")
public class VcUserPvCount implements EntityBean {
	private static final long serialVersionUID = 1L;


	@Comment("ID[ID]")
	private Long id;


	@Comment("所属域[DOMAIN_ID]")
	private Long domainId;


	@Comment("数据状态[DATA_STATUS]    数据状态:0=删除，1=正常")
	private Integer dataStatus;


	@Comment("创建时间[CREATE_TIME]    创建时间:yyyyMMddHHmmss")
	private Long createTime;


	@Comment("更新时间[MODIFY_TIME]    更新时间:yyyyMMddHHmmss")
	private Long modifyTime;


	@Comment("用户id[USER_ID]")
	private Long userId;


	@Comment("行为描述[TARGET_DESC]")
	private String targetDesc;


	@Comment("行为编码[TARGET_CODE]")
	private String targetCode;


	@Comment("统计数据时间[COUNT_DATA_TIME]    统计数据时间yyyyMMdd")
	private Long countDataTime;


	@Comment("操作次数[PC_COUNT]")
	private Long pcCount;




	public Long getId() {
		return this.id;
	}
	public void setId(Long id) {
		this.id = id;
	}


	public Long getDomainId() {
		return this.domainId;
	}
	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}


	public Integer getDataStatus() {
		return this.dataStatus;
	}
	public void setDataStatus(Integer dataStatus) {
		this.dataStatus = dataStatus;
	}


	public Long getCreateTime() {
		return this.createTime;
	}
	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}


	public Long getModifyTime() {
		return this.modifyTime;
	}
	public void setModifyTime(Long modifyTime) {
		this.modifyTime = modifyTime;
	}


	public Long getUserId() {
		return this.userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}


	public String getTargetDesc() {
		return this.targetDesc;
	}
	public void setTargetDesc(String targetDesc) {
		this.targetDesc = targetDesc;
	}


	public String getTargetCode() {
		return this.targetCode;
	}
	public void setTargetCode(String targetCode) {
		this.targetCode = targetCode;
	}


	public Long getCountDataTime() {
		return this.countDataTime;
	}
	public void setCountDataTime(Long countDataTime) {
		this.countDataTime = countDataTime;
	}


	public Long getPcCount() {
		return this.pcCount;
	}
	public void setPcCount(Long pcCount) {
		this.pcCount = pcCount;
	}


}


