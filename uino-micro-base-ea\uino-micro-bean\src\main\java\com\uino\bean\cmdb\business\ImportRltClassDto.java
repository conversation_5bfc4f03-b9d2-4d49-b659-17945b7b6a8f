package com.uino.bean.cmdb.business;

import com.binary.core.util.BinaryUtils;
import com.uino.bean.permission.business.IValidDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.Assert;

/**
 * 导入关系类定义数据传输类
 * 
 * <AUTHOR>
 *
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ImportRltClassDto implements IValidDto {

    /**
     * 行号
     */
    private Integer index;
    /*
     * 分类名称
     */
    private String className;
    /**
     * 属性名称
     */
    private String attrName;
    /*
     * 属性类型
     */
    private String attrType;
    /*
     * 方向
     */
    private String lineLabelAlign;
    /*
     * 是否label
     */
    private String isLabel;
    /*
     * 是否主键
     */
    private String isMajor;

    
    public boolean islabel() {
        return "是".equals(isLabel);
    }

    public boolean isMajor() {
        return "是".equals(isMajor);
    }

    @Override
    public void valid() {
//        Assert.notNull(className, "X_PARAM_NOT_NULL${name:分类名称}");
        Assert.isTrue(className.matches("[^\\:\\/\\?\\*\\[\\]\\\\]{1,30}"), "分类名称不可超过30位，且不可包含:/\\?*[]等字符");

        Assert.notNull(attrName, "属性名称不能为空");
        Assert.isTrue(attrName.trim().length() <= 50, "属性名称不可超过50位");
        Assert.isTrue(attrName.trim().matches("^[A-Za-z0-9\\u4e00-\\u9fa5 @\\（\\）\\(\\)\\&\\-\\|\\/\\\\'_]+$"), "属性[" + attrName + "]包含非法字符，仅支持 _&()（）|/'-@");

        Assert.isTrue(BinaryUtils.isEmpty(lineLabelAlign) || (lineLabelAlign.equals("源端") || lineLabelAlign.equals("居中")|| lineLabelAlign.equals("目标端")), "[" + lineLabelAlign + "]字符违法");
        Assert.notNull(attrType, "属性类型不能为空");
        Assert.isTrue(BinaryUtils.isEmpty(isLabel) || (isLabel.equals("是") || isLabel.equals("否")), "[" + isLabel + "]字符违法");

        Assert.isTrue(BinaryUtils.isEmpty(isMajor) || (isMajor.equals("是") || isMajor.equals("否")), "[" + isMajor + "]字符违法");
    }
}
