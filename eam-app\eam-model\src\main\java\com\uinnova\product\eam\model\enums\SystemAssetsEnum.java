package com.uinnova.product.eam.model.enums;

import lombok.Getter;

@Getter
public enum SystemAssetsEnum {
    TECHNOLOGY_STACK(11,"技术栈", "/specialDiagram/jsz.png"),
    NON_FUNCTION(12,"非功能指标", "/specialDiagram/gnzb.png"),
    DEPLOY_BUILD(13, "部署架构", "/specialDiagram/bsjg.png"),
    APP_BUILD(14, "架构上下文", "/specialDiagram/jgsxw.png"),
    ARCHITECTURE_DESC(15,"架构描述", "/specialDiagram/jgms.png");


    private Integer type;
    private String name;
    private String icon;

    SystemAssetsEnum(Integer type, String name, String icon) {
        this.type = type;
        this.name = name;
        this.icon = icon;
    }
}
