package com.uinnova.product.vmdb.comm.err;

import com.binary.core.i18n.LanguageResolver;
import com.binary.core.util.BinaryUtils;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * 目前VMDB后台通用错误信息
 * 
 * <AUTHOR>
 *
 */
public class ErrorBean implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 错误key
	 */
	private String errKey;
	/**
	 * 错误类型
	 */
	private Integer errType;
	/**
	 * 错误提示信息
	 */
	private String errMsg;

	/**
	 * 仅调用setI18nErrKey有效 <br/>
	 * 如果是属性值错误,且errKey对作了国际化,则为方便使用保存一下属性名<br/>
	 * 使用位置:一键导入页面展示
	 */
	private String proName;

	public String getErrKey() {
		return errKey;
	}

	/**
	 * 正常赋值错误字段
	 * 
	 * @param errKey 错误字段值
	 */
	public void setErrKey(String errKey) {
		this.errKey = errKey;
	}

	/**
	 * 赋值错误字段,取国际化值
	 * 
	 * @param errKeyType 错误字段值类型
	 */
	public void setI18nErrKey(Integer errKeyType) {
		this.errKey = ErrorKeyType.getErrKey(errKeyType);
	}

	/**
	 * 赋值属性值错误字段
	 * 
	 * @param proName 属性名
	 */
	public void setI18nErrKey(String proName) {
		// BS_MNAME_ATTR_NAMEVALUE 属性[${proName}]值
		String i18nKey = "属性[" + proName + "]值";
		try {
			Map<String, String> params = new HashMap<String, String>();
			params.put("proName", proName);
			String message = LanguageResolver.trans("BS_MNAME_ATTR_NAMEVALUE", params);
			if (!BinaryUtils.isEmpty(message) && !message.startsWith("BS_")) {
				i18nKey = message;
			}
		} catch (Exception e) {
			// 获取国际化信息异常
		}
		this.errKey = i18nKey;
		this.proName = proName;
	}

	public Integer getErrType() {
		return errType;
	}

	/**
	 * 错误类型赋值,并填充错误提示信息
	 * 
	 * @param errType 错误类型
	 */
	public void setErrType(Integer errType) {
		this.setErrType(errType, null);
	}

	/**
	 * 错误类型赋值,并填充错误提示信息
	 * 
	 * @param errType   错误类型
	 * @param fieldName 验证字段名,与后台校验国际化方式一致[可选]
	 */
	public void setErrType(Integer errType, String fieldName) {
		this.errType = errType;
		String errMsg = "";
		if (fieldName != null && fieldName != "") {
			errMsg = ErrorType.getErrMsg(errType, fieldName);
		} else {
			errMsg = ErrorType.getErrMsg(errType);
		}
		this.setErrMsg(errMsg);
	}

	public String getErrMsg() {
		return errMsg;
	}

	public void setErrMsg(String errMsg) {
		this.errMsg = errMsg;
	}

	public String getProName() {
		return proName;
	}

	public void setProName(String proName) {
		this.proName = proName;
	}

}
