package com.uinnova.product.eam.service.flow.approval;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.binary.core.exception.BinaryException;
import com.binary.core.exception.MessageException;
import com.binary.core.lang.StringUtils;
import com.binary.core.util.BinaryUtils;
import com.binary.jdbc.Page;
import com.google.common.collect.Sets;
import com.uinnova.product.eam.base.diagram.enums.DiagramCopyEnum;
import com.uinnova.product.eam.base.diagram.model.*;
import com.uinnova.product.eam.base.util.EamUtil;
import com.uinnova.product.eam.comm.model.es.*;
import com.uinnova.product.eam.feign.workable.entity.*;
import com.uinnova.product.eam.model.*;
import com.uinnova.product.eam.model.enums.AppSquareTypeEnum;
import com.uinnova.product.eam.model.enums.FlowSystemType;
import com.uinnova.product.eam.model.vo.FileDocVo;
import com.uinnova.product.eam.model.vo.KcpInfoVo;
import com.uinnova.product.eam.service.WorkbenchChargeDoneSvc;
import com.uinnova.product.eam.service.asset.AssetContent;
import com.uinnova.product.eam.service.es.IamsESCIDesignSvc;
import com.uinnova.product.eam.service.es.IamsESCIHistoryDesignSvc;
import com.uinnova.product.eam.service.exception.BusinessException;
import com.uinnova.product.eam.service.flow.base.AbstractFlowProcessSystemService;
import com.uinnova.product.eam.service.flow.document.FlowProcessDocumentServiceImpl;
import com.uinnova.product.eam.service.flow.relation.FlowProcessRelationServiceImpl;
import com.uinnova.product.eam.service.impl.IamsCIRltDesignSvc;
import com.uinnova.product.vmdb.comm.model.ci.CCcCi;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClass;
import com.uinnova.product.vmdb.comm.model.ci.CcCi;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uinnova.product.vmdb.provider.rlt.bean.CcCiRltInfo;
import com.uino.api.client.permission.IUserApiSvc;
import com.uino.bean.cmdb.base.*;
import com.uino.bean.cmdb.query.ESCIClassSearchBean;
import com.uino.bean.cmdb.query.ESCISearchBean;
import com.uino.bean.cmdb.query.ESRltSearchBean;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.permission.business.UserInfo;
import com.uino.dao.util.ESUtil;
import com.uino.service.cmdb.microservice.ICIRltSvc;
import com.uino.util.sys.SysUtil;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.uinnova.product.eam.service.asset.AssetContent.CANCELLED;
import static com.uinnova.product.eam.service.asset.AssetContent.RELEASE_STATE;

/**
 * 流程审批发布服务
 * 负责流程的发布、审批、签发等功能
 */
@Service
@Slf4j
public class FlowProcessApprovalServiceImpl extends AbstractFlowProcessSystemService {


    @Resource
    private FlowProcessRelationServiceImpl flowProcessRelationService;

    @Resource
    private FlowProcessDocumentServiceImpl flowProcessDocumentService;

    @Resource
    protected IamsESCIDesignSvc iamsESCIDesignSvc;

    @Resource
    protected IamsESCIHistoryDesignSvc ciHistorySvc;

    @Resource
    protected IamsCIRltDesignSvc iamsCiRltDesignSvc;

    @Resource
    protected WorkbenchChargeDoneSvc workbenchChargeDoneSvc;



    /**
     * 发布流程
     */
    public Long publishFlowProcessSystem(String ciCode, String loginCode,
                                         String publishReason, String publishType, Long flowSystemApproveDataId) {

        List<String> changeModel = new ArrayList<>();
        FlowProcessSystemPublishHistory flowProcessSystemPublishHistory = new FlowProcessSystemPublishHistory();
        CcCiInfo privateCiInfo = ciSwitchSvc.getCiByCode(ciCode, loginCode, LibType.PRIVATE);
        if (privateCiInfo != null && privateCiInfo.getAttrs() != null) {
            if ("流程".equalsIgnoreCase(privateCiInfo.getCiClass().getClassCode())) {
                flowProcessSystemPublishHistory.setFlowName(privateCiInfo.getAttrs().get("流程编码") + " " + privateCiInfo.getAttrs().get("流程名称"));
            } else {
                flowProcessSystemPublishHistory.setFlowName(privateCiInfo.getAttrs().get("流程组编码") + " " + privateCiInfo.getAttrs().get("流程组名称"));
            }
        }
        CcCiInfo designCiInfo = ciSwitchSvc.getCiByCode(ciCode, null, LibType.DESIGN);
        if (StringUtils.isEmpty(flowProcessSystemPublishHistory.getFlowName())) {
            designCiInfo = ciSwitchSvc.getCiByCode(ciCode, SysUtil.getCurrentUserInfo().getLoginCode(), LibType.DESIGN);
            if ("流程".equalsIgnoreCase(designCiInfo.getCiClass().getClassCode())) {
                flowProcessSystemPublishHistory.setFlowName(designCiInfo.getAttrs().get("流程编码") + " " + designCiInfo.getAttrs().get("流程名称"));
            } else {
                flowProcessSystemPublishHistory.setFlowName(designCiInfo.getAttrs().get("流程组编码") + " " + designCiInfo.getAttrs().get("流程组名称"));
            }
        }

        flowProcessSystemPublishHistory.setCiCode(ciCode);
        flowProcessSystemPublishHistory.setPublishReason(publishReason);
        Long versionId = ESUtil.getUUID();
        flowProcessSystemPublishHistory.setId(versionId);
        // 发布视图
        String flowDiagramId = publishDiagram(ciCode, publishReason, "flowDiagram", loginCode);
        if (flowDiagramId != null) {
            changeModel.add("流程图");
            flowProcessSystemPublishHistory.setFlowDiagram(flowDiagramId);
        }
        String architectureDiagramId = publishDiagram(ciCode, publishReason, "architectureDiagram", loginCode);
        if (architectureDiagramId != null) {
            changeModel.add("架构图");
            flowProcessSystemPublishHistory.setArchitectureDiagram(architectureDiagramId);
        }
        String integrationDiagramId = publishDiagram(ciCode, publishReason, "integrationDiagram", loginCode);
        if (integrationDiagramId != null) {
            changeModel.add("集成关系图");
            flowProcessSystemPublishHistory.setArchitectureDiagram(integrationDiagramId);
        }

        //发布的时候判断ci的上级架构字段是否一致
        if (privateCiInfo != null && privateCiInfo.getAttrs().get("上级架构").equalsIgnoreCase(designCiInfo.getAttrs().get("上级架构"))) {
            privateCiInfo.getAttrs().put("上级架构", designCiInfo.getAttrs().get("上级架构"));
            iciSwitchSvc.saveOrUpdateCI(privateCiInfo, LibType.PRIVATE);
        }

        //发布ciCode
        Map<String, Long> stringLongMap = publishCiCode(ciCode, publishReason, loginCode);
        if (stringLongMap != null) {
            if (stringLongMap.get("change") == 1L) {
                if (designCiInfo.getCiClass().getClassCode().equalsIgnoreCase("流程")) {
                    changeModel.add("流程属性");
                } else {
                    changeModel.add("架构卡");
                }
            }
            flowProcessSystemPublishHistory.setCiHistoryId(stringLongMap.get("versionId"));
        }
        //发布关联要素
        String o = publishFlowSystemAssociatedFeatures(ciCode, loginCode, versionId);
        if (o != null) {
            changeModel.add("关联要素");
        }
        //发布流程文件
        String s = publishFlowSystemFile(ciCode, loginCode, versionId);
        if (!StringUtils.isEmpty(s)) {
            changeModel.add("流程文件");
        }
        //流程绩效
        String p = publishProcessPerformance(ciCode, loginCode, versionId, publishReason, "指标");
        if (!StringUtils.isEmpty(p)) {
            changeModel.add("流程绩效");
        }
        String p1 = publishProcessPerformance(ciCode, loginCode, versionId, publishReason, "风险");
        if (!StringUtils.isEmpty(p1)) {
            changeModel.add("流程风险");
        }
        String p2 = publishProcessPerformance(ciCode, loginCode, versionId, publishReason, "档案");
        if (!StringUtils.isEmpty(p2)) {
            changeModel.add("档案管理");
        }
        //发布裁剪指南
        //查询当前流程关联的所有场景
        String s1 = publishScene(ciCode, loginCode, versionId, publishReason);
        String p3 = publishProcessPerformance(ciCode, loginCode, versionId, publishReason, "场景");
        if (!StringUtils.isEmpty(s1)) {
            changeModel.add("裁剪指南");
        }
        flowProcessSystemPublishHistory.setCreator(loginCode);
        //签发人同意就是审批人
        flowProcessSystemPublishHistory.setCheckUser(SysUtil.getCurrentUserInfo().getLoginCode());
        flowProcessSystemPublishHistory.setPublishType(publishType);

        if (CollectionUtils.isEmpty(changeModel)) {
            changeModel.add("-");
        }

        if ("submit".equalsIgnoreCase(publishType)) {
            flowProcessSystemPublishHistory.setChangeData("修改并提交" + flowProcessSystemPublishHistory.getFlowName() + "【" + org.apache.commons.lang3.StringUtils.join(changeModel, "、") + "】");
        } else {
            flowProcessSystemPublishHistory.setChangeData("修改并审批" + flowProcessSystemPublishHistory.getFlowName() + "【" + org.apache.commons.lang3.StringUtils.join(changeModel, "、") + "】");
        }
        if ("published".equalsIgnoreCase(publishType)) {
            //来源是发布直接用
            flowProcessSystemPublishHistory.setFlowSystemApproveDataId(flowSystemApproveDataId);
            //更新视图为最新的发布视图
            FlowSystemApproveData flowSystemApproveData = flowSystemApproveDataDao.getById(flowSystemApproveDataId);
            //如果视图没有发布就获取当前最新的视图id
            List<EamDiagramRelationSys> diagramRelationSysList = eamDiagramRelationSysService.findFlowSystemDiagramRelation(ciCode, "flowDiagram");
            if (!CollectionUtils.isEmpty(diagramRelationSysList)) {
                ESDiagram diagram = diagramApiClient.getEsDiagram(diagramRelationSysList.get(0).getDiagramEnergy(), 1);
                String diagramEn = copyDiagramBatch(diagram, diagram.getName());
                flowSystemApproveData.setDiagramEnergy(diagramEn);
                flowSystemApproveDataDao.saveOrUpdate(flowSystemApproveData);
            }
        } else {
            //不是发布需要查询下快照信息
            FlowSystemApproveData submitUserProcessData = new FlowSystemApproveData();
            if ("流程".equalsIgnoreCase(designCiInfo.getCiClass().getClassCode())) {
                submitUserProcessData = getSubmitUserProcessData(ciCode);
                BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
                boolQueryBuilder.must(QueryBuilders.termQuery("ciCode.keyword", ciCode));
                boolQueryBuilder.must(QueryBuilders.termQuery("publishType.keyword", publishType));
                List<FlowProcessSystemPublishHistory> flowSystemPublishHistory = flowProcessSystemPublishHistoryDao
                        .getSortListByQueryScroll(boolQueryBuilder, "createTime", false);
                submitUserProcessData.setApproveSuccessVersion(flowSystemPublishHistory.size() + 1);
                FlowSnapTreeDto singleTreeByProcessCiId = getSingleTreeByProcessCiId(designCiInfo.getCi().getId());
                submitUserProcessData.setFlowSnapTreeDto(singleTreeByProcessCiId);
                List<EamDiagramRelationSys> diagramRelationSysList = eamDiagramRelationSysService.findFlowSystemDiagramRelation(ciCode, "flowDiagram");
                if (!CollectionUtils.isEmpty(diagramRelationSysList)) {
                    ESDiagram diagram = diagramApiClient.getEsDiagram(diagramRelationSysList.get(0).getDiagramEnergy(), 1);
                    String diagramEn = copyDiagramBatch(diagram, diagram.getName());
                    submitUserProcessData.setDiagramEnergy(diagramEn);
                }
            } else {
                CcCiInfo lastDesignCiInfo = ciSwitchSvc.getCiByCode(ciCode, null, LibType.DESIGN);
                List<EamDiagramRelationSys> diagramRelationSysList = eamDiagramRelationSysService.findFlowSystemDiagramRelation(ciCode, "integrationDiagram");
                submitUserProcessData.setCiInfo(lastDesignCiInfo);
                if (!CollectionUtils.isEmpty(diagramRelationSysList)) {
                    ESDiagram diagram = diagramApiClient.getEsDiagram(diagramRelationSysList.get(0).getDiagramEnergy(), 1);
                    String diagramEn = copyDiagramBatch(diagram, diagram.getName());
                    submitUserProcessData.setIntegrationDiagramId(diagramEn);
                }
                HashMap<String, Object> autoDiagramByParentCiCode = getAutoDiagramByParentCiCode(lastDesignCiInfo.getCi().getId());
                submitUserProcessData.setAutoDiagramData(autoDiagramByParentCiCode);
                List<FileDocVo> submit = findFileDocList(lastDesignCiInfo.getCi().getCiCode(), "submit");
                submitUserProcessData.setAllFileList(JSON.parseArray(JSON.toJSONString(submit)));
            }
            Long l = flowSystemApproveDataDao.saveOrUpdate(submitUserProcessData);
            flowProcessSystemPublishHistory.setFlowSystemApproveDataId(l);
        }
        return flowProcessSystemPublishHistoryDao.saveOrUpdate(flowProcessSystemPublishHistory);
    }

    /**
     * 流程发起
     */
    public TaskResponse processLaunch(ProcessLaunchDto processApprovalDto) {
        PorcessResponse porcessResponseExit = flowableFeign
                .getProcessInstanceByBusinessIdAndProcessDefinitionKey(processApprovalDto.getBusinessKey(), processApprovalDto.getProcessDefinitionKey());
        if (porcessResponseExit != null) {
            throw new BinaryException("流程已存在，禁止重复发起");
        }
        //获取用户code
        String loginCode = SysUtil.getCurrentUserInfo().getLoginCode();
        //根据流程ciCode查询对应末级流程数据
        CcCiInfo ciByCode = ciSwitchSvc.getCiByCode(processApprovalDto.getBusinessKey(), loginCode, LibType.PRIVATE);
        Assert.notNull(ciByCode, "当前末级流程未修改");
        ProcessRequest processRequest = new ProcessRequest();
        BeanUtils.copyProperties(processApprovalDto, processRequest);
        processRequest.setOwner(loginCode);
        processRequest.setUserId(loginCode);
        FlowSystemApproveData submitUserProcessData = getSubmitUserProcessData(processApprovalDto.getBusinessKey());
        //调用工作流接口发起一个审批流程
        PorcessResponse porcessResponse = flowableFeign.startProcessBindAssignee(processRequest);
        //更新CcCi信息w
        //判断下是未发布审批还是升班后审批
        BoolQueryBuilder ciBoolQueryBuilder = QueryBuilders.boolQuery();
        ciBoolQueryBuilder.filter(QueryBuilders.termQuery("ciCode.keyword", processApprovalDto.getBusinessKey()));
        ESCIInfo esciInfo = iamsESCIDesignSvc.selectOne(ciBoolQueryBuilder);
        int processApprovalStatus = esciInfo.getProcessApprovalStatus();
        //未发布->审批中/升版中->升版审批中
        esciInfo.setProcessApprovalStatus(processApprovalStatus + 1);
        esciInfo.setUpdateStatus(1);
        iamsESCIDesignSvc.saveOrUpdate(esciInfo);

        submitUserProcessData.setProcessInstanceId(porcessResponse.getProcessInstanceId());
        submitUserProcessData.setCiCode(processApprovalDto.getBusinessKey());
        submitUserProcessData.setSourceProcessApprovalStatus(processApprovalStatus);
        FlowSnapTreeDto singleTreeByProcessCiId = getSingleTreeByProcessCiId(esciInfo.getId());
        submitUserProcessData.setFlowSnapTreeDto(singleTreeByProcessCiId);
        //获取版本号数据
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termQuery("ciCode.keyword", processApprovalDto.getBusinessKey()));
        boolQueryBuilder.must(QueryBuilders.termQuery("publishType.keyword", "published"));
        List<FlowProcessSystemPublishHistory> flowSystemPublishHistory = flowProcessSystemPublishHistoryDao
                .getSortListByQueryScroll(boolQueryBuilder, "createTime", false);
        submitUserProcessData.setApproveSuccessVersion(flowSystemPublishHistory.size() + 1);

        flowSystemApproveDataDao.saveOrUpdate(submitUserProcessData);

        //调用执行审批任务接口
        TaskRequest taskRequest = new TaskRequest();
        taskRequest.setTaskId(porcessResponse.getTaskId());
        //写死成通过，流程转到审批中,生成对应的代办任务
        taskRequest.setAction(FLOWACTION.ACCETP);
        taskRequest.setNextUserIds(processApprovalDto.getNextUserIds());
        return flowableFeign.completeTask(taskRequest);
    }

    /**
     * 复制视图
     *
     * @param diagram     原视图信息
     * @param diagramName 新视图名称
     * <AUTHOR>
     */
    private String copyDiagramBatch(ESDiagram diagram, String diagramName) {
        Map<String, Long> diagramDirMap = new HashMap<>();
        diagramDirMap.put(diagram.getDEnergy(), diagram.getDirId());
        List<ESDiagram> copyList = new ArrayList<>();
        ESDiagram copy = EamUtil.copy(diagram, ESDiagram.class);
        copy.setName(diagramName);
        copyList.add(copy);
        // 返回map key：原视图编码 value：新视图编码
        Map<String, String> copyDiagramMap = diagramSvcV2.copyDiagramBatch(diagramDirMap, copyList, DiagramCopyEnum.COMMON);
        return copyDiagramMap.get(diagram.getDEnergy());
    }

    private Map<String, Long> publishCiCode(String ciCode, String publishReason, String loginCode) {

        HashMap<String, Long> stringObjectHashMap = new HashMap<>();
        stringObjectHashMap.put("change", 0L);
        CcCiInfo privateCiInfo = ciSwitchSvc.getCiByCode(ciCode, loginCode, LibType.PRIVATE);
        if (privateCiInfo != null) {
            CcCiInfo designCiInfo = ciSwitchSvc.getCiByCode(ciCode, null, LibType.DESIGN);
            String dateTime = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(LocalDateTime.now());
            if (designCiInfo == null) {
                privateCiInfo.getCi().setId(null);
                privateCiInfo.getAttrs().put(AssetContent.MODIFI_TIME, dateTime);
                privateCiInfo.getAttrs().put(AssetContent.RELEASE_TIME, dateTime);
                privateCiInfo.getAttrs().put(AssetContent.RELEASE_STATE, AssetContent.RELEASE);
                privateCiInfo.getAttrs().put(AssetContent.CREATION_TIME, dateTime);
                privateCiInfo.getAttrs().put(AssetContent.CHANGE_RECORD, publishReason);
                ciSwitchSvc.saveOrUpdateCI(privateCiInfo, LibType.DESIGN);
            } else {
                Map<String, String> designAttrs = designCiInfo.getAttrs();
                Map<String, String> privateCiInfoAttrs = privateCiInfo.getAttrs();
                for (String s : AssetContent.IGNORE_ATTR) {
                    designAttrs.remove(s);
                    privateCiInfoAttrs.remove(s);
                }

                boolean equals = designAttrs.equals(privateCiInfoAttrs);
                if (!equals) {
                    stringObjectHashMap.put("change", 1L);
                    privateCiInfo.getAttrs().put(AssetContent.MODIFI_TIME, dateTime);
                    privateCiInfo.getAttrs().put(AssetContent.RELEASE_TIME, dateTime);
                    privateCiInfo.getAttrs().put(AssetContent.RELEASE_STATE, AssetContent.RELEASE);
                    privateCiInfo.getAttrs().put(AssetContent.CREATION_TIME, dateTime);
                    privateCiInfo.getAttrs().put(AssetContent.CHANGE_RECORD, publishReason);
                    designCiInfo.setAttrs(privateCiInfo.getAttrs());
                    designCiInfo.getCi().setOwnerCode(loginCode);
                    ciSwitchSvc.saveOrUpdateCI(designCiInfo, LibType.DESIGN);

                    privateCiInfo.getCi().setPublicVersion(designCiInfo.getCi().getPublicVersion());
                    ciSwitchSvc.saveOrUpdateCI(privateCiInfo, LibType.PRIVATE);
                }
            }
        }

        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.termQuery("ciCode.keyword", ciCode));
        List<ESCIHistoryInfo> version = ciHistorySvc.getSortListByQueryScroll(boolQueryBuilder, "version", false);
        if (CollectionUtils.isEmpty(version)) {
            return null;
        }
        Long id = version.get(0).getId();
        stringObjectHashMap.put("versionId", id);
        return stringObjectHashMap;
    }

    private String publishFlowSystemAssociatedFeatures(String ciCode, String loginCode, Long versionId) {
        // 查询私有库的流程文件数据
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.filter(QueryBuilders.termQuery("ciCode.keyword", ciCode));
        queryBuilder.filter(QueryBuilders.termQuery("creator.keyword", loginCode));
        //指标有些特殊，单独处理
        queryBuilder.mustNot(QueryBuilders.termsQuery("classCode.keyword", "指标", "风险", "场景", "档案"));
        List<FlowSystemAssociatedFeatures> privateFeatureList = flowSystemAssociatedFeaturesPrivateDao.getListByQuery(queryBuilder);
        if (CollectionUtils.isEmpty(privateFeatureList)) {
            log.info("当前用户未编辑关联要素!");
            return null;
        }

        // 查询设计库现有的关联要素发布的数据
        BoolQueryBuilder designQueryBuilder = QueryBuilders.boolQuery();
        designQueryBuilder.filter(QueryBuilders.termQuery("ciCode.keyword", ciCode));
        designQueryBuilder.filter(QueryBuilders.termQuery("status", 1));
        List<FlowSystemAssociatedFeatures> designFeatureList = flowSystemAssociatedFeaturesDao.getListByQuery(designQueryBuilder);
        boolean equalCollection = org.apache.commons.collections4.CollectionUtils.isEqualCollection(privateFeatureList, designFeatureList);
        if (!equalCollection) {
            //不相等就发布
            if (!CollectionUtils.isEmpty(designFeatureList)) {
                designFeatureList.forEach(feature -> {
                    feature.setStatus(2);
                });
                flowSystemAssociatedFeaturesDao.saveOrUpdateBatch(designFeatureList);
            }
            // 发布关联要素
            for (FlowSystemAssociatedFeatures features : privateFeatureList) {
                features.setId(ESUtil.getUUID());
                features.setStatus(1);
                features.setVersionId(versionId);
            }
            try {
                flowSystemAssociatedFeaturesDao.saveOrUpdateBatch(privateFeatureList);
            } catch (Exception e) {
                // 发布失败，回滚数据
                if (!CollectionUtils.isEmpty(designFeatureList)) {
                    designFeatureList.forEach(feature -> {
                        feature.setStatus(1);
                    });
                    flowSystemAssociatedFeaturesDao.saveOrUpdateBatch(designFeatureList);
                }
                log.info("关键要素发布失败：" + e);
            }
            return "1";
        } else {
            return null;
        }
    }

    private String publishFlowSystemFile(String ciCode, String loginCode, Long versionId) {
        // 查询私有库的流程文件数据
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.filter(QueryBuilders.termQuery("ciCode.keyword", ciCode));
        queryBuilder.filter(QueryBuilders.termQuery("creator.keyword", loginCode));
        List<FlowSystemFile> privateFlowSystemFile = flowSystemFilePrivateDao.getListByQuery(queryBuilder);
        if (CollectionUtils.isEmpty(privateFlowSystemFile)) {
            log.info("当前用户未编辑流程文件!");
            return null;
        }
        FlowSystemFile flowSystemFile = privateFlowSystemFile.get(0);

        // 将设计库流程文件已有的发布版本改为历史版本
        BoolQueryBuilder designQueryBuilder = QueryBuilders.boolQuery();
        designQueryBuilder.filter(QueryBuilders.termQuery("ciCode.keyword", ciCode));
        designQueryBuilder.filter(QueryBuilders.termQuery("status", 1));
        List<FlowSystemFile> designFlowSystemFileList = flowSystemFileDao.getListByQuery(designQueryBuilder);
        if (!CollectionUtils.isEmpty(designFlowSystemFileList)) {
            FlowSystemFile designFlowSystemFile = designFlowSystemFileList.get(0);
            if (flowSystemFile.equals(designFlowSystemFile)) {
                log.info("流程文件未变化");
                return null;
            }
            designFlowSystemFileList.forEach(file -> {
                file.setStatus(2);
            });
            flowSystemFileDao.saveOrUpdateBatch(designFlowSystemFileList);
        }

        // 新增设计库已发布状态的流程文件
        flowSystemFile.setId(ESUtil.getUUID());
        flowSystemFile.setStatus(1);
        flowSystemFile.setVersionId(versionId);
        try {
            flowSystemFileDao.saveOrUpdate(flowSystemFile);
        } catch (Exception e) {
            // 新增失败，数据回滚
            if (!CollectionUtils.isEmpty(designFlowSystemFileList)) {
                // 历史版本，创建的时间最新的，改为最新的发布
                designFlowSystemFileList.forEach(file -> {
                    file.setStatus(1);
                });
                flowSystemFileDao.saveOrUpdateBatch(designFlowSystemFileList);
            }
            log.info("流程文件发布失败：" + e);
        }
        return "1";
    }

    private String publishScene(String ciCode, String loginCode, Long versionId, String publishReason) {

        String returnFlag = null;
        //查询当前流程关联的所有场景数据
        CcCiClassInfo linkClass = iRltClassSvc.getRltClassByName(1L, "关联");
        CcCiClassInfo sceneClass = iciClassSvc.getCiClassByClassCode("场景");
        CcCiClassInfo activeClass = iciClassSvc.getCiClassByClassCode("活动");
        ESRltSearchBean esRltSearchBean = new ESRltSearchBean();
        //源端是流程
        esRltSearchBean.setSourceCiCodes(Collections.singleton(ciCode));
        //关联关系
        esRltSearchBean.setRltClassIds(Collections.singletonList(linkClass.getCiClass().getId()));
        //目标端是场景
        esRltSearchBean.setTargetClassIds(Collections.singletonList(sceneClass.getCiClass().getId()));
        List<CcCiRltInfo> privateCcCiRltInfos = iciRltSwitchSvc.searchRltByScroll(esRltSearchBean, LibType.PRIVATE);

        List<CcCiRltInfo> designCcCiRltInfos = iciRltSwitchSvc.searchRltByScroll(esRltSearchBean, LibType.DESIGN);

        //对比下关系是否有差异
        Map<String, CcCiInfo> privateSceneMap = new HashMap<>();
        for (CcCiRltInfo privateCcCiRltInfo : privateCcCiRltInfos) {
            CcCiInfo targetCiInfo = privateCcCiRltInfo.getTargetCiInfo();
            privateSceneMap.put(targetCiInfo.getCi().getCiCode(), targetCiInfo);
        }

        Map<String, CcCiInfo> designSceneMap = new HashMap<>();
        for (CcCiRltInfo designCcCiRltInfo : designCcCiRltInfos) {
            CcCiInfo targetCiInfo = designCcCiRltInfo.getTargetCiInfo();
            designSceneMap.put(targetCiInfo.getCi().getCiCode(), targetCiInfo);
        }

        Sets.SetView<String> needDelScene = Sets.difference(designSceneMap.keySet(), privateSceneMap.keySet());
        List<Long> delSceneIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(needDelScene)) {
            returnFlag = "1";
            for (String s : needDelScene) {
                delSceneIds.add(designSceneMap.get(s).getCi().getId());
            }
            ciSwitchSvc.removeByIds(delSceneIds, 2L, LibType.DESIGN);
        }

        Sets.SetView<String> addDelScene = Sets.difference(privateSceneMap.keySet(), designSceneMap.keySet());
        //先发布下场景
        if (!CollectionUtils.isEmpty(addDelScene)) {
            returnFlag = "1";
            for (String s : addDelScene) {
                publishCiCode(s, loginCode, publishReason);
            }
        }
        //判断已有场景跟活动的关系是否有变化
        List<ESCIRltInfo> rltList = new ArrayList<>();
        Sets.SetView<String> intersection = Sets.intersection(designSceneMap.keySet(), privateSceneMap.keySet());
        if (!CollectionUtils.isEmpty(intersection)) {
            //如果有存在的场景判断场景和活动关系是否有变化
            ESRltSearchBean esRltSearchBean1 = new ESRltSearchBean();
            //目标为场景
            esRltSearchBean1.setTargetCiCodes(intersection);
            //关联关系
            esRltSearchBean1.setRltClassIds(Collections.singletonList(linkClass.getCiClass().getId()));
            //源端分类为活动
            esRltSearchBean1.setSourceClassIds(Collections.singletonList(activeClass.getCiClass().getId()));
            //资产库的场景和活动的关系
            List<CcCiRltInfo> ccCiRltInfos = iciRltSwitchSvc.searchRltByScroll(esRltSearchBean1, LibType.DESIGN);
            esRltSearchBean1.setOwnerCode(loginCode);
            //私有库的场景和活动的关系
            List<CcCiRltInfo> privatecCiRltInfos = iciRltSwitchSvc.searchRltByScroll(esRltSearchBean1, LibType.PRIVATE);
            Set<String> privateRltCodes = new HashSet<>();
            for (CcCiRltInfo privatecCiRltInfo : privatecCiRltInfos) {
                privateRltCodes.add(privatecCiRltInfo.getCiRlt().getCiCode());
            }
            Set<String> designActiveSceneRltCode = new HashSet<>();
            Set<Long> delActiveSceneRltIds = new HashSet<>();
            for (CcCiRltInfo ccCiRltInfo : ccCiRltInfos) {
                if (!privateRltCodes.contains(ccCiRltInfo.getCiRlt().getCiCode())) {
                    delActiveSceneRltIds.add(ccCiRltInfo.getCiRlt().getId());
                }
                designActiveSceneRltCode.add(ccCiRltInfo.getCiRlt().getCiCode());
            }
            if (!CollectionUtils.isEmpty(delActiveSceneRltIds)) {
                returnFlag = "1";
                iciRltSwitchSvc.delRltByIdsOrRltCodes(delActiveSceneRltIds, null, null, LibType.DESIGN);
            }
            //新增关系
            for (CcCiRltInfo privatecCiRltInfo : privatecCiRltInfos) {
                if (!designActiveSceneRltCode.contains(privatecCiRltInfo.getCiRlt().getCiCode())) {
                    ESCIRltInfo esciRltInfo = new ESCIRltInfo();
                    esciRltInfo.setClassId(linkClass.getCiClass().getId());
                    String key = privatecCiRltInfo.getSourceCiInfo().getCi().getCiCode() + "_" + linkClass.getCiClass().getId() + "_" + privatecCiRltInfo.getTargetCiInfo().getCi().getCiCode();
                    esciRltInfo.setCiCode(key);
                    esciRltInfo.setUniqueCode("UK_" + key);
                    esciRltInfo.setSourceCiCode(privatecCiRltInfo.getSourceCiInfo().getCi().getCiCode());
                    esciRltInfo.setSourceClassId(privatecCiRltInfo.getSourceCiInfo().getCi().getClassId());
                    esciRltInfo.setTargetCiCode(privatecCiRltInfo.getTargetCiInfo().getCi().getCiCode());
                    esciRltInfo.setTargetClassId(privatecCiRltInfo.getTargetCiInfo().getCi().getClassId());
                    rltList.add(esciRltInfo);
                }
            }
        }

        //新增的场景建立与活动的关系
        if (!CollectionUtils.isEmpty(addDelScene)) {
            ESRltSearchBean esRltSearchBean2 = new ESRltSearchBean();
            esRltSearchBean2.setOwnerCode(loginCode);
            //目标端场景ciCodes
            esRltSearchBean2.setTargetCiCodes(addDelScene);
            //关联关系
            esRltSearchBean2.setRltClassIds(Collections.singletonList(linkClass.getCiClass().getId()));
            //源端分类id
            esRltSearchBean2.setSourceClassIds(Collections.singletonList(activeClass.getCiClass().getId()));
            List<CcCiRltInfo> ccCiRltInfos1 = iciRltSwitchSvc.searchRltByScroll(esRltSearchBean2, LibType.PRIVATE);
            for (CcCiRltInfo ciRltInfo : ccCiRltInfos1) {
                ESCIRltInfo esciRltInfo = new ESCIRltInfo();
                esciRltInfo.setClassId(linkClass.getCiClass().getId());
                String key = ciRltInfo.getSourceCiInfo().getCi().getCiCode() + "_" + linkClass.getCiClass().getId() + "_" + ciRltInfo.getTargetCiInfo().getCi().getCiCode();
                esciRltInfo.setCiCode(key);
                esciRltInfo.setUniqueCode("UK_" + key);
                esciRltInfo.setSourceCiCode(ciRltInfo.getSourceCiInfo().getCi().getCiCode());
                esciRltInfo.setSourceClassId(ciRltInfo.getSourceCiInfo().getCi().getClassId());
                esciRltInfo.setTargetCiCode(ciRltInfo.getTargetCiInfo().getCi().getCiCode());
                esciRltInfo.setTargetClassId(ciRltInfo.getTargetCiInfo().getCi().getClassId());
                rltList.add(esciRltInfo);
            }
        }
        if (!CollectionUtils.isEmpty(rltList)) {
            returnFlag = "1";
            iamsCiRltDesignSvc.bindBatchCiRlt(rltList, loginCode);
        }
        return returnFlag;
    }

    private FlowSystemApproveData getSubmitUserProcessData(String businessKey) {
        FlowSystemApproveData flowSystemApproveData = new FlowSystemApproveData();
        //获取当前人本地的流程图
        List<EamDiagramRelationSys> diagramRelationSysPriavteList = eamDiagramRelationSysService
                .findFlowSystemDiagramRelationPrivate(businessKey, "flowDiagram");

        if (!CollectionUtils.isEmpty(diagramRelationSysPriavteList)) {
            EamDiagramRelationSys eamDiagramRelationSys = diagramRelationSysPriavteList.get(0);
            String diagramEnergy = eamDiagramRelationSys.getDiagramEnergy();
            flowSystemApproveData.setDiagramEnergy(diagramEnergy);
        }
        String loginCode = SysUtil.getCurrentUserInfo().getLoginCode();
        //获取当前人本地私有库ci数据
        CcCiInfo ciByCode = ciSwitchSvc.getCiByCode(businessKey, loginCode, LibType.PRIVATE);
        flowSystemApproveData.setCiInfo(ciByCode);
        //获取关联要素数据
        String[] classCodeArr = new String[]{"制度", "标准", "KCP", "术语", "表单", "岗位角色", "关联应用"
                , "业务对象", "指标", "风险", "档案", "要素"};
        Map<String, Object> linkCiMap = new HashMap<>();
        for (String classCode : classCodeArr) {
            Map<String, Object> flowSystemAssociatedFeatures = flowProcessRelationService.getFlowSystemAssociatedFeatures(businessKey, classCode, LibType.PRIVATE);
            linkCiMap.put(classCode, flowSystemAssociatedFeatures);
        }
        //裁剪指南
        List<Map<String, Object>> sceneActiveRltByFlowCiCode = getSceneActiveRltByFlowCiCode(businessKey, LibType.PRIVATE);
        List<ESCIInfo> flowDiagramLocalActiveList = getFlowDiagramLocalActiveList(businessKey, LibType.PRIVATE);
        HashMap<String, Object> stringObjectHashMap1 = new HashMap<>();
        stringObjectHashMap1.put("localActiveList", flowDiagramLocalActiveList);
        stringObjectHashMap1.put("sceneActiveRlt", sceneActiveRltByFlowCiCode);
        linkCiMap.put("场景", stringObjectHashMap1);
        //活动信息
        Map<String, Object> activedata = new HashMap<>();
        List<Map<String, Object>> endProcessTable = getEndProcessTable(businessKey, LibType.PRIVATE);
        activedata.put("dataList", endProcessTable);
        CcCiClassInfo activeClass = iciClassSvc.getCiClassByClassCode("活动");
        activedata.put("attrDefs", activeClass.getAttrDefs());
        linkCiMap.put("活动", activedata);
        Map<String, Map> flowFileNew = new HashMap<>();
        if (!CollectionUtils.isEmpty(diagramRelationSysPriavteList)) {
            flowFileNew = flowProcessDocumentService.getFlowFileNew(diagramRelationSysPriavteList.get(0).getDiagramEnergy(), businessKey, LibType.PRIVATE);
        } else {
            flowFileNew = flowProcessDocumentService.getFlowFileNew(null, businessKey, LibType.PRIVATE);
        }
        linkCiMap.put("流程文件", flowFileNew);
        flowSystemApproveData.setLinkCiMap(linkCiMap);
        //获取卡片id
        AppSquareConfig appSquareConfigByClassCode = appSquareConfigSvc.
                getAppSquareConfigByClassCode(FlowSystemType.SUB_FLOW.getFlowSystemTypeName(), null, AppSquareTypeEnum.ASSET.getType());
        flowSystemApproveData.setAppSquareConfId(appSquareConfigByClassCode.getId());
        //获取流程文件数据
        return flowSystemApproveData;
    }

    public FlowSnapTreeDto getSingleTreeByProcessCiId(Long ciId) {

        //查询出来所有流程分类中所有顶点的数据
        CCcCiClass cCcCiClass = new CCcCiClass();
        cCcCiClass.setClassCodes(new String[]{FlowSystemType.FLOW.getFlowSystemTypeName()
                , FlowSystemType.SUB_FLOW.getFlowSystemTypeName()});
        ESCIClassSearchBean esciClassSearchBean = new ESCIClassSearchBean();
        esciClassSearchBean.setCdt(cCcCiClass);
        List<ESCIClassInfo> esciClassInfos = iciClassSvc.queryESCiClassInfoListBySearchBean(esciClassSearchBean);

        Map<Long, ESCIClassInfo> classMap = esciClassInfos.stream().collect(Collectors.toMap(ESCIClassInfo::getId, each -> each));

        ArrayList<Long> targetIds = new ArrayList<>();
        for (ESCIClassInfo esciClassInfo : esciClassInfos) {
            targetIds.add(esciClassInfo.getId());
        }
        CcCiClassInfo rltClass = iciRltSwitchSvc.getRltClassByCode("包含");
        List<VcCiRltInfo> vcCiRltInfos = iciRltSwitchSvc.queryUpAndDownRlt(LibType.DESIGN, ciId,
                targetIds, Collections.singletonList(rltClass.getCiClass().getId()), 10, 0, false);

        Map<Long, CcCiInfo> longCcCiInfoHashMap = new HashMap<>();

        CcCiInfo rootCiInfo = null;

        for (VcCiRltInfo vcCiRltInfo : vcCiRltInfos) {
            CcCiInfo sourceCiInfo = vcCiRltInfo.getSourceCiInfo();
            if ("业务域".equalsIgnoreCase(sourceCiInfo.getAttrs().get("流程级别"))) {
                rootCiInfo = sourceCiInfo;
            }
            CcCiInfo targetCiInfo = vcCiRltInfo.getTargetCiInfo();
            longCcCiInfoHashMap.put(sourceCiInfo.getCi().getId(), targetCiInfo);
        }

        if (rootCiInfo == null) {
            return null;
        }

        FlowSnapTreeDto flowProcessSystemTreeDto = new FlowSnapTreeDto();
        CcCi ci = rootCiInfo.getCi();
        Map<String, String> attrs = rootCiInfo.getAttrs();
        flowProcessSystemTreeDto.setCiId(ci.getId());
        flowProcessSystemTreeDto.setCiCode(ci.getCiCode());
        flowProcessSystemTreeDto.setFlowSystemName(attrs.get("流程组名称"));
        flowProcessSystemTreeDto.setFlowCode(attrs.get("流程组编码"));
        flowProcessSystemTreeDto.setFlowSystemType(classMap.get(ci.getClassId()).getClassCode());

        FlowSnapTreeDto tmpFlowProcessSystemTreeDto = flowProcessSystemTreeDto;

        while (true) {
            long ciId1 = tmpFlowProcessSystemTreeDto.getCiId();
            CcCiInfo ccCiInfo = longCcCiInfoHashMap.get(ciId1);
            if (ccCiInfo != null) {
                CcCi ci1 = ccCiInfo.getCi();
                Map<String, String> attrs1 = ccCiInfo.getAttrs();
                FlowSnapTreeDto chiledFlowProcessSystemTreeDto = new FlowSnapTreeDto();
                chiledFlowProcessSystemTreeDto.setCiId(ci1.getId());
                chiledFlowProcessSystemTreeDto.setCiCode(ci1.getCiCode());
                //新增审批状态和是否可编辑状态
                ESCIClassInfo esciClassInfo = classMap.get(ccCiInfo.getCi().getClassId());
                chiledFlowProcessSystemTreeDto.setFlowSystemName(attrs1.get(esciClassInfo.getClassCode() + "名称"));
                chiledFlowProcessSystemTreeDto.setFlowCode(attrs1.get(esciClassInfo.getClassCode() + "编码"));
                chiledFlowProcessSystemTreeDto.setFlowSystemType(classMap.get(ci1.getClassId()).getClassCode());
                tmpFlowProcessSystemTreeDto.setChild(Collections.singleton(chiledFlowProcessSystemTreeDto));
                tmpFlowProcessSystemTreeDto = chiledFlowProcessSystemTreeDto;
            } else {
                break;
            }
        }
        return flowProcessSystemTreeDto;
    }

    public FlowSystemApproveData getFlowSystemApproveDataById(Long flowSystemApproveId) {
        FlowSystemApproveData flowSystemApproveData = flowSystemApproveDataDao.getById(flowSystemApproveId);
        CcCiInfo ciInfo = flowSystemApproveData.getCiInfo();
        String classCode = ciInfo.getCiClass().getClassCode();
        if (!"流程".equalsIgnoreCase(classCode)) {
            return flowSystemApproveData;
        }
        Map<String, Object> linkCiMap = new HashMap<>(flowSystemApproveData.getLinkCiMap()); // 创建新的Map

        // 处理档案
        if (!linkCiMap.containsKey("档案")) {
            CcCiClassInfo classInfo = iciClassSvc.getCiClassByClassCode("档案");
            Map<String, Object> archiveMap = new HashMap<>();
            archiveMap.put("attrDefs", classInfo.getAttrDefs()); // 创建新的Map
            archiveMap.put("dataList", new ArrayList<>());
            linkCiMap.put("档案", archiveMap);
        }
        if (!linkCiMap.containsKey("要素")) {
            CcCiClassInfo classInfo = iciClassSvc.getCiClassByClassCode("要素");
            Map<String, Object> archiveMap = new HashMap<>();
            archiveMap.put("attrDefs", classInfo.getAttrDefs()); // 创建新的Map
            archiveMap.put("dataList", new ArrayList<>());
            linkCiMap.put("要素", archiveMap);
        }
        // 处理流程文件
        if (!linkCiMap.containsKey("流程文件")) {
            Map<String, Object> flowFileMap = getFlowFileHistory(flowSystemApproveData);
            linkCiMap.put("流程文件", flowFileMap);
        }
        flowSystemApproveData.setLinkCiMap(linkCiMap);
        flowSystemApproveDataDao.saveOrUpdate(flowSystemApproveData);
        return flowSystemApproveData;

    }

    public CcCiInfo getApproveCiInfoByProcessesInstanceId(String processInstanceId) {
        FlowSystemApproveData flowSystemApproveData = flowSystemApproveDataDao.selectOne(QueryBuilders.termQuery("processInstanceId.keyword", processInstanceId));
        CcCiInfo ciInfo = flowSystemApproveData.getCiInfo();
        ciInfo.getAttrs().put("流程版本号", "v" + flowSystemApproveData.getApproveSuccessVersion());
        return ciInfo;
    }

    public TaskResponse reSubmitRejectApproveFlow(String businessKey) {
        PorcessResponse flowSystemPublishApprove = flowableFeign
                .getProcessInstanceByBusinessIdAndProcessDefinitionKey(businessKey
                        , "flow_system_publish_approve");
        String processStartUserId = flowSystemPublishApprove.getProcessStartUserId();
        String loginCode = SysUtil.getCurrentUserInfo().getLoginCode();
        if (!processStartUserId.equalsIgnoreCase(loginCode)) {
            throw new BinaryException("当前流程审批中，不可发布");
        }
        List<TaskResponse> activeTaskByProcessInstanceId = flowableFeign.getActiveTaskByProcessInstanceId(flowSystemPublishApprove.getProcessInstanceId());
        if (CollectionUtils.isEmpty(activeTaskByProcessInstanceId) || activeTaskByProcessInstanceId.size() != 1) {
            throw new BinaryException("流程节点异常，不可发布");
        }
        TaskResponse taskResponse = activeTaskByProcessInstanceId.get(0);
        String taskDefinitionKey = taskResponse.getTaskDefinitionKey();
        List<String> strings = new ArrayList<>();
        strings.add("business_leader_reject");
        strings.add("business_review_reject");
        strings.add("integrated_reviewer_reject");
        if (!strings.contains(taskDefinitionKey)) {
            throw new BinaryException("当前流程审批中，不可发布");
        }
        return taskResponse;
    }

    public void abolishFlow(String ciCode, Boolean upVersion) {
        CcCiInfo ciByCode = ciSwitchSvc.getCiByCode(ciCode, null, LibType.DESIGN);
        if (upVersion) {
            ciByCode.getAttrs().put("资产状态", "已作废");
        } else {
            ciByCode.getAttrs().put("资产状态", "已发布");
        }
        //更新CcCi信息
        ciSwitchSvc.saveOrUpdateCI(ciByCode, LibType.DESIGN);
    }

    public Boolean stopFlowSystemApprove(String processInstanceId, String deleteReason) {
        List<TaskResponse> activeTaskByProcessInstanceId = flowableFeign.getActiveTaskByProcessInstanceId(processInstanceId);
        if (CollectionUtils.isEmpty(activeTaskByProcessInstanceId)) {
            return Boolean.FALSE;
        }
        TaskResponse taskResponse = activeTaskByProcessInstanceId.get(0);
        String taskDefinitionKey = taskResponse.getTaskDefinitionKey();
        List<String> strings = new ArrayList<>();
        strings.add("business_leader_reject");
        strings.add("business_review_reject");
        strings.add("integrated_reviewer_reject");
        if (!strings.contains(taskDefinitionKey)) {
            throw new BinaryException("流程节点异常，不可终止");
        }
        PorcessResponse processInstanceByProcessInstanceId = flowableFeign.getProcessInstanceByProcessInstanceId(processInstanceId);
        if (processInstanceByProcessInstanceId.getStatus().equals(FLOWSTATUS.ACTIVE)) {
            ProcessRequest processRequest = new ProcessRequest();
            processRequest.setProcessInstanceId(processInstanceId);
            processRequest.setDeleteReason(deleteReason);
            flowableFeign.deleteProcessInstanceById(processRequest);
        }
        String businessKey = processInstanceByProcessInstanceId.getBusinessKey();
        BoolQueryBuilder ciBoolQueryBuilder = QueryBuilders.boolQuery();
        ciBoolQueryBuilder.filter(QueryBuilders.termQuery("ciCode.keyword", businessKey));
        ESCIInfo esciInfo = iamsESCIDesignSvc.selectOne(ciBoolQueryBuilder);
        int processApprovalStatus = esciInfo.getProcessApprovalStatus();
        if (processApprovalStatus == 1 || processApprovalStatus == 5) {
            esciInfo.setProcessApprovalStatus(processApprovalStatus - 1);
            esciInfo.setUpdateStatus(0);
            //更新CcCi信息
            iamsESCIDesignSvc.saveOrUpdate(esciInfo);
        }

        //终止的时候删除代办中的任务
        WorkbenchChargeDone workbenchChargeDone = new WorkbenchChargeDone();
        workbenchChargeDone.setProcessInstanceId(processInstanceId);
        workbenchChargeDoneSvc.deleteByCondition(workbenchChargeDone);
        return true;
    }

    public void withdrawFlowSystemApprove(String businessKey) {
        PorcessResponse flowSystemPublishApprove = flowableFeign
                .getProcessInstanceByBusinessIdAndProcessDefinitionKey(businessKey, "flow_system_publish_approve");
        if (flowSystemPublishApprove == null || !flowSystemPublishApprove.getStatus().equals(FLOWSTATUS.ACTIVE)) {
            throw new RuntimeException("该流程已结束，请检查该末级流程状态");
        }
        String processInstanceId = flowSystemPublishApprove.getProcessInstanceId();
        List<TaskResponse> activeTaskByProcessInstanceId = flowableFeign.getActiveTaskByProcessInstanceId(processInstanceId);
        TaskResponse taskResponse = activeTaskByProcessInstanceId.get(0);

        if (!taskResponse.getTaskDefinitionKey().equalsIgnoreCase("business_leader")
                && !taskResponse.getTaskDefinitionKey().equalsIgnoreCase("business_review")
                && !taskResponse.getTaskDefinitionKey().equalsIgnoreCase("integrated_reviewer")) {
            throw new BinaryException("流程节点已在提交人节点");
        }

        TaskRequest taskRequest = new TaskRequest();
        taskRequest.setTaskId(taskResponse.getTaskId());
        taskRequest.setAction(FLOWACTION.REJECT);
        taskRequest.setRemarks("流程撤回");
        flowableFeign.completeTask(taskRequest);
        BoolQueryBuilder ciBoolQueryBuilder = QueryBuilders.boolQuery();
        ciBoolQueryBuilder.filter(QueryBuilders.termQuery("ciCode.keyword", businessKey));
        ESCIInfo esciInfo = iamsESCIDesignSvc.selectOne(ciBoolQueryBuilder);
        //未发布->审批中/升版中->升版审批中
        esciInfo.setUpdateStatus(0);
        iamsESCIDesignSvc.saveOrUpdate(esciInfo);
    }

    public Boolean carryOutFlow(SignFlowActionVo signFlowActionVo) {
        List<Long> signIds = signFlowActionVo.getSignIds();
        String loginCode = SysUtil.getCurrentUserInfo().getLoginCode();
        Integer approvalStatus = signFlowActionVo.getApprovalStatus();
        String businessKey = null;
        for (Long signId : signIds) {
            FlowSystemProcessSingData flowSystemProcessSingData = flowSystemProcessSignDataDao.getById(signId);
            if (!flowSystemProcessSingData.getActionType().equalsIgnoreCase("noSign")) {
                throw new BinaryException("当前流程已被其他人处理，请刷新页面");
            }
            flowSystemProcessSingData.setRemarks(signFlowActionVo.getRemarks());
            String ciCode = flowSystemProcessSingData.getCiCode();
            String linkPublishProcessInstanceId = flowSystemProcessSingData.getLinkPublishProcessInstanceId();
            if (approvalStatus == 0) {
                PorcessResponse processInstanceByProcessInstanceId = flowableFeign.getProcessInstanceByProcessInstanceId(linkPublishProcessInstanceId);
                //通过
                FlowSystemApproveData flowSystemApproveData = flowSystemApproveDataDao
                        .selectOne(QueryBuilders.termQuery("processInstanceId.keyword", linkPublishProcessInstanceId));
                Long id = flowSystemApproveData.getId();
                publishFlowProcessSystem(ciCode, processInstanceByProcessInstanceId.getProcessStartUserId(), "签发", "published", id);
                flowSystemProcessSingData.setActionType("pass");
                ////更新CcCi信息
                BoolQueryBuilder ciBoolQueryBuilder = QueryBuilders.boolQuery();
                ciBoolQueryBuilder.filter(QueryBuilders.termQuery("ciCode.keyword", ciCode));
                ESCIInfo esciInfo = iamsESCIDesignSvc.selectOne(ciBoolQueryBuilder);
                esciInfo.setProcessApprovalStatus(3);
                esciInfo.setUpdateStatus(1);
                iamsESCIDesignSvc.saveOrUpdate(esciInfo);
            } else {
                //不通过回退
                //需要处理下未发布还是升版逻辑
                FlowSystemApproveData flowSystemApproveDataByProcessInstanceIdId = flowSystemApproveDataDao.selectOne(QueryBuilders.termQuery("processInstanceId.keyword", linkPublishProcessInstanceId));
                ////更新CcCi信息
                BoolQueryBuilder ciBoolQueryBuilder = QueryBuilders.boolQuery();
                ciBoolQueryBuilder.filter(QueryBuilders.termQuery("ciCode.keyword", ciCode));
                ESCIInfo esciInfo = iamsESCIDesignSvc.selectOne(ciBoolQueryBuilder);
                esciInfo.setUpdateStatus(0);
                esciInfo.setProcessApprovalStatus(flowSystemApproveDataByProcessInstanceIdId.getSourceProcessApprovalStatus());
                iamsESCIDesignSvc.saveOrUpdate(esciInfo);
                flowSystemProcessSingData.setActionType("noPass");
            }
            flowSystemProcessSingData.setSignUserCode(loginCode);
            flowSystemProcessSignDataDao.saveOrUpdate(flowSystemProcessSingData);
            businessKey = flowSystemProcessSingData.getBusinessKey();
        }
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.termQuery("businessKey.keyword", businessKey));
        boolQueryBuilder.filter(QueryBuilders.termQuery("actionType.keyword", "noSign"));
        List<FlowSystemProcessSingData> listByQueryScroll = flowSystemProcessSignDataDao.getListByQueryScroll(boolQueryBuilder);

        if (CollectionUtils.isEmpty(listByQueryScroll)) {
            //执行审批
            TaskRequest taskRequest = new TaskRequest();
            taskRequest.setTaskId(signFlowActionVo.getTaskId());
            taskRequest.setAction(FLOWACTION.ACCETP);
            flowableFeign.completeTask(taskRequest);
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    public Page<CcCiInfo> findSingFlowList(Long ciId, Integer pageNum, Integer pageSize, String word) {
        CcCiClassInfo rltClass = iciRltSwitchSvc.getRltClassByCode("包含");
        CCcCiClass cCcCiClass = new CCcCiClass();
        cCcCiClass.setClassCodes(new String[]{FlowSystemType.FLOW.getFlowSystemTypeName()
                , FlowSystemType.SUB_FLOW.getFlowSystemTypeName()});
        ESCIClassSearchBean esciClassSearchBean = new ESCIClassSearchBean();
        esciClassSearchBean.setCdt(cCcCiClass);
        List<ESCIClassInfo> esciClassInfos = iciClassSvc.queryESCiClassInfoListBySearchBean(esciClassSearchBean);
        Map<String, ESCIClassInfo> classMap = esciClassInfos.stream().collect(Collectors.toMap(ESCIClassInfo::getClassCode, each -> each));
        ESCIClassInfo flowClassCi = classMap.get(FlowSystemType.FLOW.getFlowSystemTypeName());
        ESCIClassInfo subflowClassCi = classMap.get(FlowSystemType.SUB_FLOW.getFlowSystemTypeName());
        ArrayList<Long> targetIds = new ArrayList<>();
        targetIds.add(flowClassCi.getId());
        targetIds.add(subflowClassCi.getId());
        List<VcCiRltInfo> vcCiRltInfos = iciRltSwitchSvc.queryUpAndDownRlt(LibType.DESIGN, ciId,
                targetIds, Collections.singletonList(rltClass.getCiClass().getId()), 0, 10, false);

        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

        boolQueryBuilder.filter(QueryBuilders.termQuery("actionType.keyword", "noSign"));
        List<FlowSystemProcessSingData> listByQueryScroll = flowSystemProcessSignDataDao.getListByQueryScroll(boolQueryBuilder);
        Set<String> noSingCiCodes = new HashSet<>();
        for (FlowSystemProcessSingData flowSystemProcessSingData : listByQueryScroll) {
            noSingCiCodes.add(flowSystemProcessSingData.getCiCode());
        }

        ArrayList<CcCiInfo> ccCiInfos = new ArrayList<>();
        for (VcCiRltInfo vcCiRltInfo : vcCiRltInfos) {
            CcCiInfo targetCiInfo = vcCiRltInfo.getTargetCiInfo();
            if (targetCiInfo.getCi().getClassId().equals(subflowClassCi.getId())
                    && targetCiInfo.getCi().getProcessApprovalStatus() == 2
                    && !noSingCiCodes.contains(targetCiInfo.getCi().getCiCode())) {
                if (org.apache.commons.lang3.StringUtils.isNotBlank(word)) {
                    Map<String, String> attrs = targetCiInfo.getAttrs();
                    String s = attrs.get("流程名称");
                    String s2 = attrs.get("流程编码");
                    if (!s.contains(word) && !s2.contains(word)) {
                        continue;
                    }
                }
                ccCiInfos.add(targetCiInfo);
            }
        }
        int totalItems = ccCiInfos.size();
        int totalPages = (int) Math.ceil((double) totalItems / pageSize);
        int start = (pageNum - 1) * pageSize;
        int end = Math.min(start + pageSize, totalItems);
        List<CcCiInfo> pageData = new ArrayList<>(ccCiInfos.subList(start, end));
        Page<CcCiInfo> ccCiInfoPage = new Page<>();
        ccCiInfoPage.setData(pageData);
        ccCiInfoPage.setPageNum(pageNum);
        ccCiInfoPage.setPageSize(pageSize);
        ccCiInfoPage.setTotalPages(totalPages);
        ccCiInfoPage.setTotalRows(totalItems);
        return ccCiInfoPage;
    }

    /**
     * 流程绩效发布
     *
     * @param ciCode
     * @param versionId
     */
    private String publishProcessPerformance(String ciCode, String loginCode, Long versionId, String publishReason, String classCode) {
        // 查询私有库的流程文件数据
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.filter(QueryBuilders.termQuery("ciCode.keyword", ciCode));
        queryBuilder.filter(QueryBuilders.termQuery("creator.keyword", loginCode));
        //指标有些特殊，单独处理
        queryBuilder.filter(QueryBuilders.termQuery("classCode.keyword", classCode));
        List<FlowSystemAssociatedFeatures> privateFeatureList = flowSystemAssociatedFeaturesPrivateDao.getListByQuery(queryBuilder);
        if (CollectionUtils.isEmpty(privateFeatureList)) {
            log.info("当前用户未编辑" + classCode + "!");
            return null;
        }

        Set<String> privateLinkCode = privateFeatureList.stream().map(FlowSystemAssociatedFeatures::getLinkedCiCode).collect(Collectors.toSet());
        Map<String, FlowSystemAssociatedFeatures> privateFlowSystemAssociatedFeaturesHashMap = new HashMap<>();
        for (FlowSystemAssociatedFeatures flowSystemAssociatedFeatures : privateFeatureList) {
            privateFlowSystemAssociatedFeaturesHashMap.put(flowSystemAssociatedFeatures.getLinkedCiCode(), flowSystemAssociatedFeatures);
        }
        List<ESCIInfo> ciByCodes = ciSwitchSvc.getCiByCodes(new ArrayList<>(privateLinkCode), loginCode, LibType.PRIVATE);
        Set<String> privateLinkCodeSet = new HashSet<>();
        for (ESCIInfo ciByCode : ciByCodes) {
            privateLinkCodeSet.add(ciByCode.getCiCode());
        }
        privateFeatureList.removeIf(ci -> !privateLinkCodeSet.contains(ci.getLinkedCiCode()));
        // 查询设计库现有的关联要素发布的数据
        BoolQueryBuilder designQueryBuilder = QueryBuilders.boolQuery();
        designQueryBuilder.filter(QueryBuilders.termQuery("ciCode.keyword", ciCode));
        designQueryBuilder.filter(QueryBuilders.termQuery("classCode.keyword", classCode));
        designQueryBuilder.filter(QueryBuilders.termQuery("status", 1));
        List<FlowSystemAssociatedFeatures> designFeatureList = flowSystemAssociatedFeaturesDao.getListByQuery(designQueryBuilder);
        Map<String, FlowSystemAssociatedFeatures> designFlowSystemAssociatedFeaturesHashMap = new HashMap<>();
        for (FlowSystemAssociatedFeatures flowSystemAssociatedFeatures : designFeatureList) {
            designFlowSystemAssociatedFeaturesHashMap.put(flowSystemAssociatedFeatures.getLinkedCiCode(), flowSystemAssociatedFeatures);
        }
        boolean equalCollection = org.apache.commons.collections4.CollectionUtils.isEqualCollection(privateFeatureList, designFeatureList);
        for (FlowSystemAssociatedFeatures flowSystemAssociatedFeatures : privateFeatureList) {
            flowSystemAssociatedFeatures.setVersionId(versionId);
        }
        flowSystemAssociatedFeaturesPrivateDao.saveOrUpdateBatch(privateFeatureList);
        if (!equalCollection) {
            //对比ci数据
            for (String s : privateLinkCodeSet) {
                publishCiCode(s, publishReason, loginCode);
            }
            Set<String> designLinkCode = new HashSet<>();
            //不相等就发布
            if (!CollectionUtils.isEmpty(designFeatureList)) {
                designFeatureList.forEach(feature -> {
                    designLinkCode.add(feature.getLinkedCiCode());
                    feature.setStatus(2);
                });
                flowSystemAssociatedFeaturesDao.saveOrUpdateBatch(designFeatureList);
            }
            // 发布关联要素
            for (FlowSystemAssociatedFeatures features : privateFeatureList) {
                features.setId(ESUtil.getUUID());
                features.setStatus(1);
                features.setVersionId(versionId);
                features.setModifier(SysUtil.getCurrentUserInfo().getUserName());
            }
            try {
                flowSystemAssociatedFeaturesDao.saveOrUpdateBatch(privateFeatureList);
            } catch (Exception e) {
                // 发布失败，回滚数据
                if (!CollectionUtils.isEmpty(designFeatureList)) {
                    designFeatureList.forEach(feature -> {
                        feature.setStatus(1);
                    });
                    flowSystemAssociatedFeaturesDao.saveOrUpdateBatch(designFeatureList);
                }
                log.info("关键要素发布失败：" + e);
            }
            Sets.SetView<String> difference = Sets.difference(designLinkCode, privateLinkCodeSet);
            if (!CollectionUtils.isEmpty(difference)) {
                CcCiClassInfo linkClass = iciRltSwitchSvc.getRltClassByCode("关联");
                Set<String> rltCodes = new HashSet<>();
                for (String s : difference) {
                    rltCodes.add(ciCode + "_" + linkClass.getCiClass().getId() + "_" + s);
                }
                ICIRltSvc ciRltSvc = iciRltSwitchSvc.getCiRltSvc(LibType.DESIGN);
                ciRltSvc.delRltByIdsOrRltCodes(null, rltCodes, null);
            }
            return "1";
        } else {
            List<String> collect = privateFeatureList.stream().map(FlowSystemAssociatedFeatures::getLinkedCiCode).collect(Collectors.toList());
            boolean flag = false;
            for (String s : collect) {
                Map<String, Long> stringLongMap = publishCiCode(s, publishReason, loginCode);
                if (!CollectionUtils.isEmpty(stringLongMap)) {
                    flag = true;
                    Long l = stringLongMap.get("change");
                    if (l == 1L) {
                        FlowSystemAssociatedFeatures privateFlowSystemAssociatedFeatures = privateFlowSystemAssociatedFeaturesHashMap.get(s);
                        privateFlowSystemAssociatedFeatures.setVersionId(versionId);
                        FlowSystemAssociatedFeatures designFlowSystemAssociatedFeatures = designFlowSystemAssociatedFeaturesHashMap.get(s);
                        designFlowSystemAssociatedFeatures.setVersionId(versionId);
                        flowSystemAssociatedFeaturesDao.saveOrUpdate(designFlowSystemAssociatedFeatures);
                        flowSystemAssociatedFeaturesPrivateDao.saveOrUpdate(privateFlowSystemAssociatedFeatures);
                    }
                }
            }
            if (!flag) {
                return null;
            } else {
                return "1";
            }
        }
    }

    public HashMap<String, Object> getAutoDiagramByParentCiCode(Long parentCiId) {

        CcCiClassInfo rltClass = iciRltSwitchSvc.getRltClassByCode("包含");
        int treeDeepin = 1;

        //查询出来所有流程分类中所有顶点的数据
        CCcCiClass cCcCiClass = new CCcCiClass();
        cCcCiClass.setClassCodes(new String[]{FlowSystemType.FLOW.getFlowSystemTypeName()
                , FlowSystemType.SUB_FLOW.getFlowSystemTypeName()});
        ESCIClassSearchBean esciClassSearchBean = new ESCIClassSearchBean();
        esciClassSearchBean.setCdt(cCcCiClass);
        List<ESCIClassInfo> esciClassInfos = iciClassSvc.queryESCiClassInfoListBySearchBean(esciClassSearchBean);

        List<Long> targetClassList = new ArrayList<>();
        for (ESCIClassInfo esciClassInfo : esciClassInfos) {
            targetClassList.add(esciClassInfo.getId());
        }

        List<VcCiRltInfo> vcCiRltInfos = iciRltSwitchSvc.queryUpAndDownRlt(LibType.DESIGN, parentCiId,
                targetClassList, Collections.singletonList(rltClass.getCiClass().getId()), 0, 2, false);
        Map<Long, List<VcCiRltInfo>> collect = vcCiRltInfos.stream().collect(Collectors.groupingBy(rlt -> rlt.getSourceCiInfo().getCi().getId()));
        CcCiInfo ciInfoById = iamsCIDesignSvc.getCiInfoById(parentCiId);
        ciInfoById.setAttrDefs(null);
        ciInfoById.setCiClass(null);
        FlowProcessAutoDiagramDto flowProcessAutoDiagramDto = new FlowProcessAutoDiagramDto();
        BeanUtils.copyProperties(ciInfoById, flowProcessAutoDiagramDto);
        List<VcCiRltInfo> vcCiRltInfos1 = collect.get(parentCiId);
        List<FlowProcessAutoDiagramDto> flowProcessAutoDiagramDtos = new ArrayList<>();
        if (!CollectionUtils.isEmpty(vcCiRltInfos1)) {
            treeDeepin = 2;
            for (VcCiRltInfo vcCiRltInfo : vcCiRltInfos1) {
                CcCiInfo targetCiInfo = vcCiRltInfo.getTargetCiInfo();
                String s = targetCiInfo.getAttrs().get("资产状态");
                if (org.apache.commons.lang3.StringUtils.isBlank(s) || s.equalsIgnoreCase("已作废")) {
                    continue;
                }
                FlowProcessAutoDiagramDto flowProcessAutoDiagramDto1 = new FlowProcessAutoDiagramDto();
                BeanUtils.copyProperties(targetCiInfo, flowProcessAutoDiagramDto1);
                flowProcessAutoDiagramDtos.add(flowProcessAutoDiagramDto1);
                List<VcCiRltInfo> vcCiRltInfos2 = collect.get(targetCiInfo.getCi().getId());
                if (!CollectionUtils.isEmpty(vcCiRltInfos2)) {
                    treeDeepin = 3;
                    List<FlowProcessAutoDiagramDto> chiledFlowProcessAutoDiagramDtos = new ArrayList<>();
                    for (VcCiRltInfo ciRltInfo : vcCiRltInfos2) {
                        CcCiInfo targetCiInfo1 = ciRltInfo.getTargetCiInfo();
                        String s1 = targetCiInfo1.getAttrs().get("资产状态");
                        if (org.apache.commons.lang3.StringUtils.isBlank(s1) || s1.equalsIgnoreCase("已作废")) {
                            continue;
                        }
                        FlowProcessAutoDiagramDto flowProcessAutoDiagramDto2 = new FlowProcessAutoDiagramDto();
                        BeanUtils.copyProperties(targetCiInfo1, flowProcessAutoDiagramDto2);
                        chiledFlowProcessAutoDiagramDtos.add(flowProcessAutoDiagramDto2);
                    }
                    flowProcessAutoDiagramDto1.setChiledList(chiledFlowProcessAutoDiagramDtos);
                }
            }
        }
        flowProcessAutoDiagramDto.setChiledList(flowProcessAutoDiagramDtos);
        HashMap<String, Object> resultMap = new HashMap<>();
        resultMap.put("ciClassList", esciClassInfos);
        resultMap.put("treeDeepin", treeDeepin);
        resultMap.put("ciTree", flowProcessAutoDiagramDto);
        return resultMap;
    }

    public List<FileDocVo> findFileDocList(String ciCode, String publishType) {
        CcCiInfo designCiInfo = ciSwitchSvc.getCiByCode(ciCode, SysUtil.getCurrentUserInfo().getLoginCode(), LibType.DESIGN);

        // 关联关系 ——> 包含 关系
        Set<String> ciCodeSet = new HashSet<>();
        CcCiClassInfo rltClass = iciRltSwitchSvc.getRltClassByCode("包含");
        CcCiClassInfo flowClass = iciClassSvc.getCiClassByClassCode("流程");
        if (rltClass != null && rltClass.getCiClass() != null && rltClass.getCiClass().getId() != null) {
            List<Long> rltClassIds = new ArrayList<>();
            rltClassIds.add(rltClass.getCiClass().getId());

            List<VcCiRltInfo> vcCiRltInfos = iciRltSwitchSvc.queryUpAndDownRlt(LibType.DESIGN, designCiInfo.getCi().getId(),
                    null, rltClassIds, 0, 10, false);
            if (!CollectionUtils.isEmpty(vcCiRltInfos)) {
                ciCodeSet = vcCiRltInfos.stream()
                        .filter(item -> {
                            if (item.getCiRlt() == null || StringUtils.isEmpty(item.getCiRlt().getTargetCiCode())) {
                                return false;
                            }
                            // 过滤已作废数据
                            CcCiInfo targetCiInfo = item.getTargetCiInfo();
                            return targetCiInfo != null && targetCiInfo.getCi().getClassId().equals(flowClass.getCiClass().getId()) &&
                                    !"已作废".equalsIgnoreCase(String.valueOf(targetCiInfo.getAttrs().get("资产状态")));
                        })
                        .map(item -> item.getCiRlt().getTargetCiCode()).collect(Collectors.toSet());
            }
        }
        if (CollectionUtils.isEmpty(ciCodeSet)) {
            return null;
        }

        // 查询流程文件列表
        Map<String, String> versionMap = flowProcessSystemPublishHistoryDao.getLatestPublishHistoryByCiCodes(ciCodeSet);
        List<FileDocVo> fileDocVoList = new ArrayList<>();
        ESCISearchBean esciSearchBean = new ESCISearchBean();
        esciSearchBean.setPageSize(ciCodeSet.size());
        esciSearchBean.setPageNum(1);
        esciSearchBean.setCiCodes(new ArrayList<>(ciCodeSet));
        Page<ESCIInfo> esciInfoPage = ciSwitchSvc.getCiSvc(LibType.DESIGN).searchESCIByBean(esciSearchBean);
        if (!CollectionUtils.isEmpty(esciInfoPage.getData())) {
            List<ESCIInfo> esciInfoList = esciInfoPage.getData();
            for (ESCIInfo esCi : esciInfoList) {
                // 有版本信息说明是有发布的子流程
                String version = versionMap.get(esCi.getCiCode());
                if (StringUtils.isEmpty(version)) {
                    continue;
                }

                FileDocVo fileDocVo = new FileDocVo();
                Map<String, Object> attrs = esCi.getAttrs();
                String code = null;
                String name = null;
                if (attrs.get("流程编码") != null) {
                    code = String.valueOf(attrs.get("流程编码"));
                }
                if (attrs.get("流程名称") != null) {
                    name = String.valueOf(attrs.get("流程名称"));
                }
                if (!StringUtils.isEmpty(code) && !StringUtils.isEmpty(name)) {
                    fileDocVo.setName(code + " " + name);
                }
                if (attrs.get("发布时间") != null && !StringUtils.isEmpty(String.valueOf(attrs.get("发布时间")))) {
                    fileDocVo.setDate(String.valueOf(attrs.get("发布时间")));
                }
                if (attrs.get("编写人") != null && !StringUtils.isEmpty(String.valueOf(attrs.get("编写人")))) {
                    String person = String.valueOf(attrs.get("编写人"));
                    JSONArray jsonArray = JSONObject.parseArray(person);
                    if (!CollectionUtils.isEmpty(jsonArray)) {
                        StringBuilder sb = new StringBuilder();
                        for (int i = 0; i < jsonArray.size(); i++) {
                            JSONObject jsonObject = jsonArray.getJSONObject(i);
                            String userName = jsonObject.getString("userName");
                            if (!Objects.equals(jsonArray.size() - 1, i)) {
                                sb.append(userName).append(",");
                            } else {
                                sb.append(userName);
                            }
                        }
                        fileDocVo.setUser(sb.toString());
                    }
                }
                fileDocVo.setCiCode(esCi.getCiCode());
                fileDocVo.setCiId(esCi.getId());
                fileDocVo.setVersion(version);
                fileDocVoList.add(fileDocVo);
            }
        }
        return fileDocVoList;
    }

    public List<FlowProcessSystemPublishHistoryDto> getFlowSystemPublishHistory(String ciCode, String publishType) {

        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termQuery("ciCode.keyword", ciCode));
        if (publishType != null) {
            boolQueryBuilder.must(QueryBuilders.termQuery("publishType.keyword", publishType));
        }
        List<FlowProcessSystemPublishHistory> flowSystemPublishHistory = flowProcessSystemPublishHistoryDao
                .getSortListByQueryScroll(boolQueryBuilder, "createTime", false);
        List<FlowProcessSystemPublishHistoryDto> flowProcessSystemPublishHistoryDtos = new ArrayList<>();
        for (int i = 0; i < flowSystemPublishHistory.size(); i++) {
            FlowProcessSystemPublishHistoryDto flowProcessSystemPublishHistoryDto = new FlowProcessSystemPublishHistoryDto();
            FlowProcessSystemPublishHistory flowProcessSystemPublishHistory = flowSystemPublishHistory.get(i);
            BeanUtils.copyProperties(flowProcessSystemPublishHistory, flowProcessSystemPublishHistoryDto);
            String creator = flowProcessSystemPublishHistory.getCreator();
            UserInfo creatorUser = iUserApiSvc.getUserInfoByLoginCode(creator);
            flowProcessSystemPublishHistoryDto.setCreator(creatorUser.getUserName());
            String checkUser = flowProcessSystemPublishHistory.getCheckUser();
            UserInfo checkUserObj = iUserApiSvc.getUserInfoByLoginCode(checkUser);
            flowProcessSystemPublishHistoryDto.setCheckUser(checkUserObj.getUserName());

            String publishType1 = flowProcessSystemPublishHistoryDto.getPublishType();
            if (publishType1.equalsIgnoreCase("submit")) {
                flowProcessSystemPublishHistoryDto.setPublishType("提交");
                flowProcessSystemPublishHistoryDto.setVersionName("SV" + (flowSystemPublishHistory.size() - i));
            } else {
                flowProcessSystemPublishHistoryDto.setPublishType("审批");
                flowProcessSystemPublishHistoryDto.setVersionName("V" + (flowSystemPublishHistory.size() - i));
            }
            flowProcessSystemPublishHistoryDtos.add(flowProcessSystemPublishHistoryDto);
        }
        return flowProcessSystemPublishHistoryDtos;

    }

    public List<Map<String, Object>> getSceneActiveRltByFlowCiCode(String ciCode, LibType libType) {
        //查询当前流程关联的所有场景数据
        String loginCode = SysUtil.getCurrentUserInfo().getLoginCode();
        BoolQueryBuilder privateQueryBuilder = QueryBuilders.boolQuery()
                .filter(QueryBuilders.termQuery("ciCode.keyword", ciCode))
                .filter(QueryBuilders.termQuery("creator.keyword", loginCode))
                .filter(QueryBuilders.termQuery("classCode.keyword", "场景"));
        List<FlowSystemAssociatedFeatures> privateFeatureList = flowSystemAssociatedFeaturesPrivateDao.getListByQuery(privateQueryBuilder);

        List<Map<String, Object>> resultMap = new ArrayList<>();
        if (!CollectionUtils.isEmpty(privateFeatureList)) {
            //拿到所有的场景
            List<CcCiInfo> sceneCiInfos = new ArrayList<>();
            Set<String> sceneCiCodes = new HashSet<>();

            for (FlowSystemAssociatedFeatures ccCiRltInfo : privateFeatureList) {
                sceneCiCodes.add(ccCiRltInfo.getLinkedCiCode());
            }
            //查找场景关联的所有活动(从es库查询)
            Map<String, List<String>> sceneActiveRlt = getSceneActiveRlt(sceneCiCodes, libType);
            for (FlowSystemAssociatedFeatures ccCiRltInfo : privateFeatureList) {
                CcCiInfo ciByCode = ciSwitchSvc.getCiByCode(ccCiRltInfo.getLinkedCiCode(), loginCode, LibType.PRIVATE);
                if (!BinaryUtils.isEmpty(ciByCode)) {
                    Map<String, Object> stringObjectHashMap = new HashMap<>();
                    ciByCode.setCiClass(null);
                    ciByCode.setAttrDefs(null);
                    ciByCode.setFixMapping(null);
                    stringObjectHashMap.put("sceneInfo", ciByCode);
                    stringObjectHashMap.put("rltActiveCiCode", sceneActiveRlt.get(ciByCode.getCi().getCiCode()));
                    resultMap.add(stringObjectHashMap);
                }
            }
        }
        return resultMap;
    }

    private Map<String, List<String>> getSceneActiveRlt(Set<String> sceneCiCodes, LibType libType) {
        CcCiClassInfo linkClass = iRltClassSvc.getRltClassByName(1L, "关联");
        CcCiClassInfo activeClass = iciClassSvc.getCiClassByClassCode("活动");
        ESRltSearchBean esRltSearchBean = new ESRltSearchBean();
        //目标是场景id
        esRltSearchBean.setTargetCiCodes(sceneCiCodes);
        //关联关系
        esRltSearchBean.setRltClassIds(Collections.singletonList(linkClass.getCiClass().getId()));
        //源端类型时活动
        esRltSearchBean.setSourceClassIds(Collections.singletonList(activeClass.getCiClass().getId()));
        List<CcCiRltInfo> ccCiRltInfos = iciRltSwitchSvc.searchRltByScroll(esRltSearchBean, libType);
        Map<String, List<String>> sceneActiveMap = new HashMap<>();
        for (CcCiRltInfo ccCiRltInfo : ccCiRltInfos) {
            //源端是活动，目标为场景
            CcCiInfo targetCiInfo = ccCiRltInfo.getTargetCiInfo();
            String sceneCiCode = targetCiInfo.getCi().getCiCode();
            List<String> orDefault = sceneActiveMap.getOrDefault(sceneCiCode, new ArrayList<>());
            orDefault.add(ccCiRltInfo.getSourceCiInfo().getCi().getCiCode());
            sceneActiveMap.put(sceneCiCode, orDefault);
        }
        return sceneActiveMap;
    }

    public List<ESCIInfo> getFlowDiagramLocalActiveList(String ciCode, LibType libType) {
        List<EamDiagramRelationSys> diagramRelationSysPriavteList = null;
        if (LibType.PRIVATE.equals(libType)) {
            diagramRelationSysPriavteList = eamDiagramRelationSysService.findFlowSystemDiagramRelationPrivate(ciCode, "flowDiagram");
        } else {
            diagramRelationSysPriavteList = eamDiagramRelationSysService.findFlowSystemDiagramRelation(ciCode, "flowDiagram");
        }
        List<ESDiagramModel> modelList;
        if (!CollectionUtils.isEmpty(diagramRelationSysPriavteList)) {
            EamDiagramRelationSys eamDiagramRelationSys = diagramRelationSysPriavteList.get(0);
            if (eamDiagramRelationSys != null && !StringUtils.isEmpty(eamDiagramRelationSys.getDiagramEnergy())) {
                Long diagramId = diagramApiClient.queryDiagramInfoByEnergy(eamDiagramRelationSys.getDiagramEnergy());
                Map<String, Long> map = new HashMap<>();
                if (diagramId != null) {
                    List<ESDiagramDTO> esDiagramDTOList = diagramApiClient.queryDiagramInfoByIds(new Long[]{diagramId}, null, false, false);
                    if (!CollectionUtils.isEmpty(esDiagramDTOList)) {
                        ESDiagramDTO esDiagramDTO = esDiagramDTOList.get(0);
                        ESDiagramInfoDTO diagram = esDiagramDTO.getDiagram();
                        modelList = diagram.getModelList();
                        if (!CollectionUtils.isEmpty(modelList)) {
                            for (ESDiagramModel model : modelList) {
                                if (!BinaryUtils.isEmpty(model) && !BinaryUtils.isEmpty(model.getNodeDataArray())) {
                                    for (ESDiagramNode esDiagramNode : model.getNodeDataArray()) {
                                        Map<String, Object> res = new HashMap<>();
                                        String nodeJson = esDiagramNode.getNodeJson();
                                        Pattern pattern = Pattern.compile("\"classCode\":\\s*\"([^\"]*)\"");
                                        Matcher matcher = pattern.matcher(nodeJson);
                                        if (matcher.find() && matcher.group(1).equals("活动")) {
                                            res.put("nodeId", esDiagramNode.getId());
                                            res.put("activeSortNum", esDiagramNode.getActiveSortNum());
                                            int start = nodeJson.indexOf("\"ciCode\":\"");
                                            start += "\"ciCode\":\"".length();
                                            int end = nodeJson.indexOf("\"", start);
                                            String numberPart = nodeJson.substring(start, end).trim();
                                            if (!StringUtils.isEmpty(numberPart)) {
                                                map.put(numberPart, esDiagramNode.getActiveSortNum());
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                if (!CollectionUtils.isEmpty(map)) {
                    ESCISearchBean esciSearchBean = new ESCISearchBean();
                    esciSearchBean.setCiCodes(new ArrayList<>(map.keySet()));
                    if (LibType.PRIVATE.equals(libType)) {
                        esciSearchBean.setOwnerCode(SysUtil.getCurrentUserInfo().getLoginCode());
                    }
                    esciSearchBean.setPageNum(1);
                    esciSearchBean.setPageSize(map.size());
                    Page<ESCIInfo> esciInfoPage = ciSwitchSvc.searchESCIByBean(esciSearchBean, libType);
                    if (esciInfoPage.getData() != null) {
                        esciInfoPage.getData().sort((a, b) -> {
                            Long sortNumA = map.get(a.getCiCode());
                            Long sortNumB = map.get(b.getCiCode());
                            return sortNumA != null && sortNumB != null ? sortNumA.compareTo(sortNumB) : 0;
                        });
                    }
                    return esciInfoPage.getData();
                }
            }
        }
        return new ArrayList<>();
    }

    public List<Map<String, Object>> getEndProcessTable(String ciCode, LibType libType) {
        List<EamDiagramRelationSys> diagramRelationSysList = null;
        CcCiClassInfo activeClass = iciClassSvc.getCiClassByClassCode("活动");
        CCcCi cCcCi = new CCcCi();
        cCcCi.setClassId(activeClass.getCiClass().getId());
        if (LibType.PRIVATE.equals(libType)) {
            diagramRelationSysList = eamDiagramRelationSysService.findFlowSystemDiagramRelationPrivate(ciCode, "flowDiagram");
            cCcCi.setOwnerCodeEqual(SysUtil.getCurrentUserInfo().getLoginCode());
        } else {
            diagramRelationSysList = eamDiagramRelationSysService.findFlowSystemDiagramRelation(ciCode, "flowDiagram");
        }
        List<CcCiInfo> ccCiInfos = iciSwitchSvc.queryCiInfoList(1L, cCcCi, null, false, false, libType);
        HashMap<String, CcCiInfo> activeCiMap = new HashMap<>();
        for (CcCiInfo ccCiInfo : ccCiInfos) {
            activeCiMap.put(ccCiInfo.getCi().getCiCode(), ccCiInfo);
        }
        List<ESDiagramModel> modelList = null;
        List<Map<String, Object>> resultMap = new ArrayList<>();
        if (!CollectionUtils.isEmpty(diagramRelationSysList)) {
            EamDiagramRelationSys eamDiagramRelationSys = diagramRelationSysList.get(0);
            if (eamDiagramRelationSys != null && !StringUtils.isEmpty(eamDiagramRelationSys.getDiagramEnergy())) {
                Long diagramId = diagramApiClient.queryDiagramInfoByEnergy(eamDiagramRelationSys.getDiagramEnergy());
                if (diagramId != null) {
                    List<ESDiagramDTO> esDiagramDTOList = diagramApiClient.queryDiagramInfoByIds(new Long[]{diagramId}, null, false, false);
                    if (!CollectionUtils.isEmpty(esDiagramDTOList)) {
                        ESDiagramDTO esDiagramDTO = esDiagramDTOList.get(0);
                        ESDiagramInfoDTO diagram = esDiagramDTO.getDiagram();
                        modelList = diagram.getModelList();
                        if (!CollectionUtils.isEmpty(modelList)) {
                            for (ESDiagramModel model : modelList) {
                                for (ESDiagramNode esDiagramNode : model.getNodeDataArray()) {
                                    Map<String, Object> res = new HashMap<>();
                                    String nodeJson = esDiagramNode.getNodeJson();
                                    Pattern pattern = Pattern.compile("\"classCode\":\\s*\"([^\"]*)\"");
                                    Matcher matcher = pattern.matcher(nodeJson);
                                    if (matcher.find() && matcher.group(1).equals("活动")) {
                                        res.put("nodeId", esDiagramNode.getId());
                                        res.put("activeSortNum", esDiagramNode.getActiveSortNum());
                                        int start = nodeJson.indexOf("\"ciCode\":\"");
                                        start += "\"ciCode\":\"".length();
                                        int end = nodeJson.indexOf("\"", start);
                                        String numberPart = nodeJson.substring(start, end).trim();
                                        if (!StringUtils.isEmpty(numberPart)) {
                                            CcCiInfo ciByCode = activeCiMap.get(numberPart);
                                            if (ciByCode != null) {
                                                res.put("Attrs", ciByCode.getAttrs());
                                            }
                                        }
                                        resultMap.add(res);
                                    }
                                }
                            }
                            return resultMap;
                        }
                    }
                }
            }
        }
        return resultMap;
    }

    private String extractPrimaryValues(Object values, String delimiter, String name) {
        if (values == null || values.toString().isEmpty()) {
            return "";
        }
        String valueStr = values.toString();
        try {
            Object jsonObj = JSON.parse(valueStr);
            if (!(jsonObj instanceof JSONArray)) {
                return valueStr;
            }

            JSONArray array = (JSONArray) jsonObj;
            if (array.isEmpty()) {
                return "";
            }
            JSONObject firstObj = array.getJSONObject(0);
            boolean hasRequiredFields = firstObj.containsKey("primary") ||
                    firstObj.containsKey("ciCode") ||
                    firstObj.containsKey("loginCode");
            if (!hasRequiredFields) {
                return valueStr;
            }
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < array.size(); i++) {
                JSONObject object = array.getJSONObject(i);
                String primary = object.getString("primary");
                String ciCode = object.getString("ciCode");
                String loginCode = object.getString("loginCode");
                if (primary == null && ciCode != null && loginCode == null) {
                    if (sb.length() > 0) {
                        sb.append(delimiter);
                    }
                    CcCiInfo ciByCode = ciSwitchSvc.getCiByCode(ciCode, SysUtil.getCurrentUserInfo().getLoginCode(), LibType.DESIGN);
                    if (ciByCode == null) {
                        continue;
                    }
                    String targetName = ("输入".equals(name) || "输出".equals(name)) ? "表单" : name;
                    sb.append(ciByCode.getAttrs().get(targetName + "名称"));
                } else if (primary != null || loginCode != null) {
                    if (sb.length() > 0) {
                        sb.append(delimiter);
                    }
                    sb.append(primary != null ? primary : loginCode);
                }
            }
            return sb.toString();
        } catch (Exception e) {
            return valueStr;
        }
    }

    private Set<String> processResult(List<Object> result) {
        return result.stream()
                .filter(Objects::nonNull)
                .map(Object::toString)
                .filter(org.apache.commons.lang.StringUtils::isNotBlank)
                .map(str -> {
                    try {
                        return JSONArray.parseArray(str);
                    } catch (Exception e) {
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .map(obj -> obj instanceof JSONObject ? ((JSONObject) obj).getString("ciCode") : null)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
    }

    public List<KcpInfoVo> findKcpInfoList(String ciCode, Integer sign, LibType libType) {
        if (StringUtils.isEmpty(ciCode)) {
            throw new BusinessException("ciCode不能为空!");
        }
        Set<String> ciCodes = null;
        Map<String, CcCiInfo> ciInfoMap = new HashMap<>();
        if (Objects.equals(sign, 2)) {
            CcCiInfo designCiInfo = ciSwitchSvc.getCiByCode(ciCode, SysUtil.getCurrentUserInfo().getLoginCode(), libType);

            // 关联关系 ——> 包含 关系
            CcCiClassInfo rltClass = iciRltSwitchSvc.getRltClassByCode("包含");


            if (rltClass != null && rltClass.getCiClass() != null && rltClass.getCiClass().getId() != null) {
                List<Long> rltClassIds = new ArrayList<>();
                rltClassIds.add(rltClass.getCiClass().getId());

                List<VcCiRltInfo> vcCiRltInfos = iciRltSwitchSvc.queryUpAndDownRlt(LibType.DESIGN, designCiInfo.getCi().getId(),
                        null, rltClassIds, 0, 10, false);
                if (!CollectionUtils.isEmpty(vcCiRltInfos)) {
                    vcCiRltInfos.forEach(vcCiRltInfo -> {
                        if (vcCiRltInfo.getCiRlt() != null && !StringUtils.isEmpty(vcCiRltInfo.getCiRlt().getTargetCiCode())
                                && vcCiRltInfo.getTargetCiInfo() != null) {
                            ciInfoMap.put(vcCiRltInfo.getCiRlt().getTargetCiCode(), vcCiRltInfo.getTargetCiInfo());
                        }
                    });
                }
            }
            if (CollectionUtils.isEmpty(ciInfoMap)) {
                return null;
            }
            ciCodes = ciInfoMap.keySet();
        } else {
            ciCodes = new HashSet<>();
            ciCodes.add(ciCode);
        }
        // 视图关联ci Map
        Map<String, String> diagramRelaCiMap = this.diagramRelaCiMap(ciCodes, libType);
        if (CollectionUtils.isEmpty(diagramRelaCiMap)) {
            return null;
        }
        List<ESDiagramDTO> esDiagramDTOList = this.esDiagramDTOList(diagramRelaCiMap);
        if (CollectionUtils.isEmpty(esDiagramDTOList)) {
            return null;
        }
        Map<String, KcpInfoVo> kcpInfoVoMap = new HashMap<>();
        for (ESDiagramDTO eSDiagramDto : esDiagramDTOList) {
            ESDiagramInfoDTO diagram = eSDiagramDto.getDiagram();
            List<ESDiagramModel> modelList = diagram.getModelList();
            if (!CollectionUtils.isEmpty(modelList)) {
                for (ESDiagramModel model : modelList) {
                    List<ESDiagramLink> linkDataArray = model.getLinkDataArray();
                    if (CollectionUtils.isEmpty(linkDataArray)) {
                        continue;
                    }
                    List<ESDiagramNode> nodeDataArray = model.getNodeDataArray();
                    if (CollectionUtils.isEmpty(nodeDataArray)) {
                        continue;
                    }
                    // 节点是否关联对象
                    Map<String, String> collect = nodeDataArray.stream().filter(item -> !StringUtils.isEmpty(item.getCiCode()))
                            .collect(Collectors.toMap(item -> item.getKey(), item -> item.getCiCode()));
                    for (ESDiagramLink link : linkDataArray) {
                        String linkJson = link.getLinkJson();
                        JSONObject jsonObject = JSONObject.parseObject(linkJson);
                        String from = jsonObject.getString("from");
                        String to = jsonObject.getString("to");
                        String className = jsonObject.getString("className");
                        // 统计源点是KCP对象，目标点是活动，且KCP-包含关系的数据
                        if (collect.containsKey(from) && collect.containsKey(to) && !StringUtils.isEmpty(className)
                                && Objects.equals(className, "KCP-包含")) {
                            String fromCiCode = collect.get(from);
                            CcCiInfo fromCiInfo = ciSwitchSvc.getCiByCode(fromCiCode, SysUtil.getCurrentUserInfo().getLoginCode(), libType);
                            if (fromCiInfo != null && fromCiInfo.getCiClass() != null && Objects.equals(fromCiInfo.getCiClass().getClassCode(), "KCP")) {
                                String toCiCode = collect.get(to);
                                CcCiInfo toCiInfo = ciSwitchSvc.getCiByCode(toCiCode, SysUtil.getCurrentUserInfo().getLoginCode(), libType);

                                // 有存在相同的KCP数据的统计为1条，活动逗号分割
                                Map<String, String> fromAttrs = fromCiInfo.getAttrs();
                                if (!kcpInfoVoMap.containsKey(fromAttrs.get("关键控制点编号"))) {
                                    KcpInfoVo kcpInfoVo = new KcpInfoVo();
                                    kcpInfoVo.setKeyContPointNum(fromAttrs.get("关键控制点编号"));
                                    kcpInfoVo.setKeyContPoint(fromAttrs.get("控制点名称"));
                                    kcpInfoVo.setControlTarget(fromAttrs.get("控制目标"));
                                    kcpInfoVo.setControlMethod(fromAttrs.get("控制方法"));
                                    kcpInfoVo.setRelatedRisk(fromAttrs.get("关联的风险名称"));
                                    Map<String, String> activeAttrs = toCiInfo.getAttrs();
                                    String active = activeAttrs.get("活动名称");
                                    kcpInfoVo.setRelatedActivities(active);
                                    if (Objects.equals(sign, 2)) {
                                        String diagramCiCode = diagramRelaCiMap.get(diagram.getDEnergy());
                                        CcCiInfo ccCiInfo = ciInfoMap.get(diagramCiCode);
                                        Map<String, String> attrs = ccCiInfo.getAttrs();
                                        String s = attrs.get("流程编码");
                                        String s1 = attrs.get("流程名称");
                                        kcpInfoVo.setKeySubProcesses(s + " " + s1);
                                    }
                                    kcpInfoVoMap.put(fromAttrs.get("关键控制点编号"), kcpInfoVo);
                                } else {
                                    KcpInfoVo infoVo = kcpInfoVoMap.get(fromAttrs.get("关键控制点编号"));
                                    Map<String, String> activeAttrs = toCiInfo.getAttrs();
                                    String active = activeAttrs.get("活动名称");
                                    if (!StringUtils.isEmpty(infoVo.getRelatedActivities())) {
                                        List<String> activityList = Arrays.stream(infoVo.getRelatedActivities().split("，")).collect(Collectors.toList());
                                        if (!activityList.contains(active)) {
                                            infoVo.setRelatedActivities(infoVo.getRelatedActivities() + "，" + active);
                                        }
                                    } else {
                                        infoVo.setRelatedActivities(active);
                                    }

                                    if (Objects.equals(sign, 2)) {
                                        String diagramCiCode = diagramRelaCiMap.get(diagram.getDEnergy());
                                        CcCiInfo ccCiInfo = ciInfoMap.get(diagramCiCode);
                                        Map<String, String> attrs = ccCiInfo.getAttrs();
                                        String s = attrs.get("流程编码");
                                        String s1 = attrs.get("流程名称");
                                        String process = s + " " + s1;
                                        if (!StringUtils.isEmpty(infoVo.getKeySubProcesses())) {
                                            List<String> processList = Arrays.stream(infoVo.getKeySubProcesses().split("，")).collect(Collectors.toList());
                                            if (!processList.contains(process)) {
                                                infoVo.setKeySubProcesses(infoVo.getKeySubProcesses() + "，" + process);
                                            }
                                        } else {
                                            infoVo.setKeySubProcesses(process);
                                        }
                                    }
                                    kcpInfoVoMap.put(fromAttrs.get("关键控制点编号"), infoVo);
                                }

                            }
                        }
                    }
                }
            }
        }
        return new ArrayList<>(kcpInfoVoMap.values());
    }

    private Map<String, String> diagramRelaCiMap(Set<String> ciCodes, LibType libType) {
        List<EamDiagramRelationSys> diagramRelationSysList = new ArrayList<>();
        if (libType.equals(LibType.PRIVATE)) {
            for (String ciCode : ciCodes) {
                diagramRelationSysList = eamDiagramRelationSysService.findFlowSystemDiagramRelationPrivate(ciCode, "flowDiagram");
            }
        } else {
            diagramRelationSysList = eamDiagramRelationSysService.findDiagramRelationSysList(ciCodes, "flowDiagram");
        }

        if (CollectionUtils.isEmpty(diagramRelationSysList)) {
            return null;
        }
        Map<String, String> diagramRelaCiMap = new HashMap<>();
        Map<String, List<EamDiagramRelationSys>> diamSysListMap = diagramRelationSysList.stream().filter(sys -> !StringUtils.isEmpty(sys.getEsSysId()))
                .collect(Collectors.groupingBy(sys -> sys.getEsSysId()));
        for (Map.Entry<String, List<EamDiagramRelationSys>> item : diamSysListMap.entrySet()) {
            List<EamDiagramRelationSys> diagRelaList = item.getValue();
            if (CollectionUtils.isEmpty(diagRelaList)) {
                continue;
            } else if (Objects.equals(diagRelaList.size(), 1)) {
                EamDiagramRelationSys diagramSys = diagRelaList.get(0);
                diagramRelaCiMap.put(diagramSys.getDiagramEnergy(), diagramSys.getEsSysId());
            } else if (diagRelaList.size() > 1) {
                Optional<EamDiagramRelationSys> first = diagRelaList.stream().sorted(new Comparator<EamDiagramRelationSys>() {
                    @Override
                    public int compare(EamDiagramRelationSys o1, EamDiagramRelationSys o2) {
                        return (int) (o2.getModifyTime() - o1.getModifyTime());
                    }
                }).findFirst();
                EamDiagramRelationSys diagramSys = first.get();
                if (diagramSys != null) {
                    diagramRelaCiMap.put(diagramSys.getDiagramEnergy(), diagramSys.getEsSysId());
                }
            }
        }
        return diagramRelaCiMap;
    }

    /**
     * 流程图信息
     *
     * @param diagramRelaCiMap
     * @return
     */
    private List<ESDiagramDTO> esDiagramDTOList(Map<String, String> diagramRelaCiMap) {
        Long[] diagramIds = diagramApiClient.queryDiagramInfoBydEnergy(diagramRelaCiMap.keySet().toArray(new String[0]));
        if (diagramIds == null || diagramIds.length <= 0) {
            return null;
        }
        List<ESDiagramDTO> esDiagramDTOList = diagramApiClient.queryDiagramInfoByIds(diagramIds, null, false, false);
        if (CollectionUtils.isEmpty(esDiagramDTOList)) {
            return null;
        }
        return esDiagramDTOList;
    }

    public List<FlowProcessSystemPublishHistoryDto> findOperationList(String ciCode, String publishType) {
        if (StringUtils.isEmpty(ciCode)) {
            throw new BusinessException("参数不能为空!");
        }
        CcCiInfo designCiInfo = ciSwitchSvc.getCiByCode(ciCode, SysUtil.getCurrentUserInfo().getLoginCode(), LibType.DESIGN);

        // 关联关系 ——> 包含 关系
        Set<String> ciCodeSet = new HashSet<>();
        CcCiClassInfo flow = iciClassSvc.getCiClassByClassCode("流程");
        CcCiClassInfo flowGroup = iciClassSvc.getCiClassByClassCode("流程组");
        CcCiClassInfo c2cFlow = iciClassSvc.getCiClassByClassCode("端到端流程");
        Map<Long, String> classIdCodeMap = new HashMap<>();
        classIdCodeMap.put(flow.getCiClass().getId(), flow.getCiClass().getClassCode());
        classIdCodeMap.put(flowGroup.getCiClass().getId(), flowGroup.getCiClass().getClassCode());
        classIdCodeMap.put(c2cFlow.getCiClass().getId(), c2cFlow.getCiClass().getClassCode());
        Map<String, String> ciAndClassMap = new HashMap<>();
        CcCiClassInfo rltClass = iciRltSwitchSvc.getRltClassByCode("包含");
        if (rltClass != null && rltClass.getCiClass() != null && rltClass.getCiClass().getId() != null) {
            List<Long> rltClassIds = new ArrayList<>();
            // Long classId = Optional.ofNullable(rltClass).map(item -> item.getCiClass()).map(item -> item.getId()).get();
            rltClassIds.add(rltClass.getCiClass().getId());
            List<Long> targetIds = new ArrayList<>();
            targetIds.add(flow.getCiClass().getId());
            targetIds.add(flowGroup.getCiClass().getId());
            List<VcCiRltInfo> vcCiRltInfos = iciRltSwitchSvc.queryUpAndDownRlt(LibType.DESIGN, designCiInfo.getCi().getId(),
                    targetIds, rltClassIds, 0, 10, false);
            if (!CollectionUtils.isEmpty(vcCiRltInfos)) {
                for (VcCiRltInfo vcCiRltInfo : vcCiRltInfos) {
                    CcCiInfo targetCiInfo = vcCiRltInfo.getTargetCiInfo();
                    ciAndClassMap.put(targetCiInfo.getCi().getCiCode(), classIdCodeMap.get(targetCiInfo.getCi().getClassId()));
                }
                ciCodeSet = vcCiRltInfos.stream()
                        .filter(item -> (item.getCiRlt() != null && !StringUtils.isEmpty(item.getCiRlt().getTargetCiCode())))
                        .map(item -> item.getCiRlt().getTargetCiCode()).collect(Collectors.toSet());
            }
        }
        ciAndClassMap.put(designCiInfo.getCi().getCiCode(), designCiInfo.getCiClass().getClassCode());
        ciCodeSet.add(ciCode);

        List<FlowProcessSystemPublishHistoryDto> flowSystemPublishHistoryList = new ArrayList<>();
        //去除状态为已废止的数据
        if (ciCodeSet != null) {
            List<ESCIInfo> ciByCodes = ciSwitchSvc.getCiByCodes(new ArrayList<>(ciCodeSet), SysUtil.getCurrentUserInfo().getLoginCode(), LibType.DESIGN);
            for (ESCIInfo ciByCode : ciByCodes) {
                if (CANCELLED.equals(ciByCode.getAttrs().get(RELEASE_STATE))) {
                    ciCodeSet.remove(ciByCode.getCiCode());
                }
            }
        }
        for (String ciCodeInfo : ciCodeSet) {
            List<FlowProcessSystemPublishHistoryDto> newFlowSystemPublishHistoryList = getFlowSystemPublishHistory(ciCodeInfo, publishType);
            if (!CollectionUtils.isEmpty(newFlowSystemPublishHistoryList)) {
                for (FlowProcessSystemPublishHistoryDto flowProcessSystemPublishHistoryDto : newFlowSystemPublishHistoryList) {
                    String ciCode1 = flowProcessSystemPublishHistoryDto.getCiCode();
                    flowProcessSystemPublishHistoryDto.setFlowType(ciAndClassMap.get(ciCode1));
                }
                flowSystemPublishHistoryList.addAll(newFlowSystemPublishHistoryList);
            }
        }
        if (!CollectionUtils.isEmpty(flowSystemPublishHistoryList)) {
            flowSystemPublishHistoryList = flowSystemPublishHistoryList.stream().sorted(new Comparator<FlowProcessSystemPublishHistoryDto>() {
                @Override
                public int compare(FlowProcessSystemPublishHistoryDto o1, FlowProcessSystemPublishHistoryDto o2) {
                    if (o1.getCreateTime() == null || o2.getCreateTime() == null) {
                        return 0;
                    } else if (o2.getCreateTime() > o1.getCreateTime()) {
                        return 1;
                    } else {
                        return -1;
                    }
                }
            }).collect(Collectors.toList());
        }
        return flowSystemPublishHistoryList;
    }

    /**
     * 流程审批
     */
    public void processApproval(ProcessApprovalDto processApprovalDto) {
        //调用执行审批任务接口
        TaskRequest taskRequest = new TaskRequest();
        taskRequest.setTaskId(processApprovalDto.getTaskId());
        taskRequest.setRemarks(processApprovalDto.getRemarks());
        //审批
        if (processApprovalDto.getApprovalStatus() == 0) {
            TaskResponse currentTaskResponse = flowableFeign.getTaskInfoByTaskId(processApprovalDto.getTaskId());
            if (currentTaskResponse.getTaskEndTime() != null) {
                return;
            }
            String taskDefinitionKey = currentTaskResponse.getTaskDefinitionKey();
            if (taskDefinitionKey.equalsIgnoreCase("business_review")) {
                List<TaskResponse> activeTasks = flowableFeign.getActiveTaskByProcessInstanceId(currentTaskResponse.getProcessInstanceId());
                PorcessResponse processInstanceByProcessInstanceId = flowableFeign.getProcessInstanceByProcessInstanceId(currentTaskResponse.getProcessInstanceId());
                Map<String, Object> routerVariables = processInstanceByProcessInstanceId.getRouterVariables();
                Object o = routerVariables.get("processAlreadyExistsUser");
                Map<String, Object> hashMap = new HashMap<>();
                if (activeTasks.size() > 1) {
                    String nextUserIds = processApprovalDto.getNextUserIds();
                    Set<String> loginCodes = new HashSet<>();
                    JSONArray objects;
                    if (o != null) {
                        objects = JSON.parseArray(JSON.toJSONString(o));
                        for (int i = 0; i < objects.size(); i++) {
                            loginCodes.add(objects.getString(i));
                        }
                    }
                    String[] split = nextUserIds.split(",");
                    loginCodes.addAll(Arrays.asList(split));
                    hashMap.put("processAlreadyExistsUser", loginCodes);
                    taskRequest.setRouterVariables(hashMap);
                } else {
                    Set<String> allUsers = new HashSet<>();
                    JSONArray objects;
                    if (o != null) {
                        objects = JSON.parseArray(JSON.toJSONString(o));
                        for (int i = 0; i < objects.size(); i++) {
                            allUsers.add(objects.getString(i));
                        }
                        String[] split = processApprovalDto.getNextUserIds().split(",");
                        allUsers.addAll(Arrays.asList(split));
                        String join = org.apache.commons.lang3.StringUtils.join(allUsers, ",");
                        taskRequest.setNextUserIds(join);
                    } else {
                        taskRequest.setNextUserIds(processApprovalDto.getNextUserIds());
                    }
                }
            } else {
                taskRequest.setNextUserIds(processApprovalDto.getNextUserIds());
            }
            taskRequest.setAction(FLOWACTION.ACCETP);
            taskRequest.setFilterUser(Boolean.TRUE);

            TaskResponse taskResponse = flowableFeign.completeTask(taskRequest);

            //如果是提交人修改节点，重新将ci改为不可编辑
            if (taskDefinitionKey.equalsIgnoreCase("business_review_reject")
                    || taskDefinitionKey.equalsIgnoreCase("integrated_reviewer_reject")
                    || taskDefinitionKey.equalsIgnoreCase("business_leader_reject")) {
                CcCiInfo ciByCode = ciSwitchSvc.getCiByCode(processApprovalDto.getBusinessKey(), null, LibType.DESIGN);
                ciByCode.getCi().setUpdateStatus(1);
                //更新CcCi信息
                ciSwitchSvc.saveOrUpdateCI(ciByCode, LibType.DESIGN);
                //重新获取下流程数据,新增到记录表中
                PorcessResponse processInstanceByProcessInstanceId = flowableFeign.getProcessInstanceByProcessInstanceId(currentTaskResponse.getProcessInstanceId());
                FlowSystemApproveData submitUserProcessData = getSubmitUserProcessData(processApprovalDto.getBusinessKey());
                FlowSystemApproveData flowSystemApproveData = flowSystemApproveDataDao.selectOne(QueryBuilders.termQuery("processInstanceId.keyword", processInstanceByProcessInstanceId.getProcessInstanceId()));
                if (flowSystemApproveData != null) {
                    submitUserProcessData.setId(flowSystemApproveData.getId());
                    FlowSnapTreeDto flowSnapTreeDto = submitUserProcessData.getFlowSnapTreeDto();
                    if (flowSnapTreeDto == null) {
                        FlowSnapTreeDto singleTreeByProcessCiId = getSingleTreeByProcessCiId(ciByCode.getCi().getId());
                        submitUserProcessData.setFlowSnapTreeDto(singleTreeByProcessCiId);
                    }
                }
                flowSystemApproveDataDao.saveOrUpdate(submitUserProcessData);
            }

            if (taskResponse.getProcessEnd()) {
                BoolQueryBuilder ciBoolQueryBuilder = QueryBuilders.boolQuery();
                ciBoolQueryBuilder.filter(QueryBuilders.termQuery("ciCode.keyword", processApprovalDto.getBusinessKey()));
                ESCIInfo esciInfo = iamsESCIDesignSvc.selectOne(ciBoolQueryBuilder);
                esciInfo.setProcessApprovalStatus(2);
                esciInfo.setUpdateStatus(1);
                iamsESCIDesignSvc.saveOrUpdate(esciInfo);
            }
        } else {
            taskRequest.setAction(FLOWACTION.REJECT);
            //如果状态是不通过。修改对应末级流程数据状态
            flowableFeign.completeTask(taskRequest);
            //根据流程ciCode查询对应末级流程数据
            BoolQueryBuilder ciBoolQueryBuilder = QueryBuilders.boolQuery();
            ciBoolQueryBuilder.filter(QueryBuilders.termQuery("ciCode.keyword", processApprovalDto.getBusinessKey()));
            ESCIInfo esciInfo = iamsESCIDesignSvc.selectOne(ciBoolQueryBuilder);
            esciInfo.setUpdateStatus(0);
            iamsESCIDesignSvc.saveOrUpdate(esciInfo);
        }
    }

    /**
     * 流程签发
     */
    public TaskResponse flowSingApproval(FlowSignProcessVo flowSignProcessVo) {
        List<String> ciCodes = flowSignProcessVo.getCiCodes();
        String loginCode = SysUtil.getCurrentUserInfo().getLoginCode();
        UserInfo userInfoByLoginCode = userApiSvc.getUserInfoByLoginCode(loginCode);
        long flowBusinessKey = ESUtil.getUUID();
        ProcessRequest processRequest = new ProcessRequest();
        processRequest.setBusinessKey(flowBusinessKey + "");
        processRequest.setProcessDefinitionKey("flow_system_flow_sign");
        processRequest.setProcessInstanceName(flowSignProcessVo.getProcessInstanceName() + "签发流程");
        processRequest.setOwner(loginCode);
        processRequest.setUserId(loginCode);
        //调用工作流接口发起一个审批流程
        PorcessResponse porcessResponse = flowableFeign.startProcessBindAssignee(processRequest);

        TaskRequest taskRequest = new TaskRequest();
        taskRequest.setTaskId(porcessResponse.getTaskId());
        //写死成通过，流程转到审批中,生成对应的代办任务
        taskRequest.setAction(FLOWACTION.ACCETP);
        taskRequest.setNextUserIds(flowSignProcessVo.getNextUserIds());
        TaskResponse taskResponse = flowableFeign.completeTask(taskRequest);

        //查询出来流程最后发布的流程实例id存下来让审核人查询
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.termsQuery("ciCode.keyword", ciCodes));
        List<FlowSystemApproveData> listByQueryScroll = flowSystemApproveDataDao.getListByQueryScroll(boolQueryBuilder);
        HashMap<String, FlowSystemApproveData> flowSystemApproveDataMap = new HashMap<>();
        for (FlowSystemApproveData flowSystemApproveData : listByQueryScroll) {
            FlowSystemApproveData flowSystemApproveData1 = flowSystemApproveDataMap.get(flowSystemApproveData.getCiCode());
            if (flowSystemApproveData1 == null || flowSystemApproveData.getCreateTime() > flowSystemApproveData1.getCreateTime()) {
                flowSystemApproveDataMap.put(flowSystemApproveData.getCiCode(), flowSystemApproveData);
            }
        }

        //流程发起成功后，添加签发信息到签发表中
        List<FlowSystemProcessSingData> flowSystemProcessSingDataList = new ArrayList<>();
        for (String ciCode : ciCodes) {
            FlowSystemProcessSingData flowSystemProcessSingData = new FlowSystemProcessSingData();
            flowSystemProcessSingData.setBusinessKey(flowBusinessKey + "");
            flowSystemProcessSingData.setCiCode(ciCode);

            FlowSystemApproveData flowSystemApproveData = flowSystemApproveDataMap.get(ciCode);
            if (flowSystemApproveData == null) {
                continue;
            } else {
                flowSystemProcessSingData.setLinkPublishProcessInstanceId(flowSystemApproveData.getProcessInstanceId());
            }
            flowSystemProcessSingData.setActionType("noSign");
            flowSystemProcessSingDataList.add(flowSystemProcessSingData);
        }
        flowSystemProcessSignDataDao.saveOrUpdateBatch(flowSystemProcessSingDataList);
        return taskResponse;
    }

    public List<FlowSystemProcessSingDataVo> getFlowSignCiList(String businessKey) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.termQuery("businessKey.keyword", businessKey));
        List<FlowSystemProcessSingData> listByQueryScroll = flowSystemProcessSignDataDao.getListByQueryScroll(boolQueryBuilder);
        List<String> linkProcessInstanceIds = new ArrayList<>();
        for (FlowSystemProcessSingData flowSystemProcessSingData : listByQueryScroll) {
            linkProcessInstanceIds.add(flowSystemProcessSingData.getLinkPublishProcessInstanceId());
        }
        BoolQueryBuilder approveBoolQueryBuilder = QueryBuilders.boolQuery();
        approveBoolQueryBuilder.filter(QueryBuilders.termsQuery("processInstanceId.keyword", linkProcessInstanceIds));
        List<FlowSystemApproveData> flowSystemApproveDataList = flowSystemApproveDataDao.getListByQueryScroll(approveBoolQueryBuilder);
        HashMap<String, CcCiInfo> processIdCiInfoMap = new HashMap<>();
        for (FlowSystemApproveData flowSystemApproveData : flowSystemApproveDataList) {
            CcCiInfo ciInfo = flowSystemApproveData.getCiInfo();
            ciInfo.getAttrs().put("流程版本号", "v" + flowSystemApproveData.getApproveSuccessVersion());
            processIdCiInfoMap.put(flowSystemApproveData.getProcessInstanceId(), ciInfo);
        }
        List<FlowSystemProcessSingDataVo> flowSystemProcessSingDataVos = new ArrayList<>();
        for (FlowSystemProcessSingData flowSystemProcessSingData : listByQueryScroll) {
            FlowSystemProcessSingDataVo flowSystemProcessSingDataVo = new FlowSystemProcessSingDataVo();
            BeanUtils.copyProperties(flowSystemProcessSingData, flowSystemProcessSingDataVo);
            String linkPublishProcessInstanceId = flowSystemProcessSingDataVo.getLinkPublishProcessInstanceId();
            flowSystemProcessSingDataVo.setCiInfo(processIdCiInfoMap.get(linkPublishProcessInstanceId));
            flowSystemProcessSingDataVos.add(flowSystemProcessSingDataVo);
        }
        return flowSystemProcessSingDataVos;
    }

    public FlowSystemApproveData getFlowSystemApproveDataByProcessInstanceIdId(String processInstanceId) {
        FlowSystemApproveData flowSystemApproveData = flowSystemApproveDataDao.selectOne(QueryBuilders.termQuery("processInstanceId.keyword", processInstanceId));
        return flowSystemApproveData;
    }

    /**
     * 变更流程状态
     */
    public void changeFlowStatus(ProcessApprovalChangeDto processApprovalChangeDto) {
        BoolQueryBuilder ciBoolQueryBuilder = QueryBuilders.boolQuery();
        ciBoolQueryBuilder.filter(QueryBuilders.termQuery("ciCode.keyword", processApprovalChangeDto.getCiCode()));
        ESCIInfo esciInfo = iamsESCIDesignSvc.selectOne(ciBoolQueryBuilder);
        if (processApprovalChangeDto.getUpVersion()) {
            if (esciInfo.getProcessApprovalStatus() != 3) {
                throw new RuntimeException("流程状态不是已发布，请检查状态");
            }
            esciInfo.setProcessApprovalStatus(4);
            esciInfo.setUpdateStatus(0);
        } else {
            if (esciInfo.getProcessApprovalStatus() != 4) {
                throw new RuntimeException("流程状态不是升版中，请检查状态");
            }
            esciInfo.setProcessApprovalStatus(3);
            esciInfo.setUpdateStatus(1);
        }
        //更新CcCi信息
        iamsESCIDesignSvc.saveOrUpdate(esciInfo);
    }

    /**
     * 发布流程图
     */
    private String publishDiagram(String ciCode, String publishReason,
                                  String diagramClassType, String loginCode) {

        //发布完视图将关联表中的视图id替换掉
        List<EamDiagramRelationSys> diagramRelationSysList = eamDiagramRelationSysService.findFlowSystemDiagramRelation(ciCode, diagramClassType);
        List<EamDiagramRelationSys> diagramRelationSysPirvateList = eamDiagramRelationSysService
                .findFlowSystemDiagramRelationPrivateByOwnerCode(ciCode, loginCode, diagramClassType);
        if (CollectionUtils.isEmpty(diagramRelationSysPirvateList)) {
            return null;
        }
        String diagramEnergy = diagramRelationSysPirvateList.get(0).getDiagramEnergy();
        ESDiagram esDiagram = diagramApiClient.getEsDiagram(diagramEnergy, 0);
        if (esDiagram.getLocalVersion() != 0) {
            EamDiagramRelationSys eamDiagramRelationSys;
            if (!CollectionUtils.isEmpty(diagramRelationSysList)) {
                eamDiagramRelationSys = diagramRelationSysList.get(0);
            } else {
                eamDiagramRelationSys = new EamDiagramRelationSys();
                eamDiagramRelationSys.setId(ESUtil.getUUID());
                eamDiagramRelationSys.setEsSysId(ciCode);
                eamDiagramRelationSys.setDiagramClassType(diagramClassType);
                eamDiagramRelationSys.setDelFlag(false);
                eamDiagramRelationSys.setCreator(SysUtil.getCurrentUserInfo().getLoginCode());
                eamDiagramRelationSys.setDirId(-100L);
            }

            //todo 发布前检查下活动是否已经有所属流程，没有的话需要填充
            List<ESDiagramNode> esDiagramNodes = diagramApiClient.selectNodeByDiagramIds(Collections.singletonList(esDiagram.getId()));
            List<String> activeCiCodes = new ArrayList<>();
            List<String> preProcessCiCodes = new ArrayList<>();
            List<String> postProcessCiCodes = new ArrayList<>();

            for (ESDiagramNode esDiagramNode : esDiagramNodes) {
                String nodeJson = esDiagramNode.getNodeJson();
                JSONObject jsonObject = JSON.parseObject(nodeJson);
                String classCode = jsonObject.getString("classCode");
                if ("活动".equalsIgnoreCase(classCode)) {
                    activeCiCodes.add(esDiagramNode.getCiCode());
                }
                if ("前置流程".equalsIgnoreCase(classCode)) {
                    preProcessCiCodes.add(esDiagramNode.getCiCode());
                }
                if ("后置流程".equalsIgnoreCase(classCode)) {
                    postProcessCiCodes.add(esDiagramNode.getCiCode());
                }
            }

            handleCiCodes(activeCiCodes, "所属流程", "活动OWNER", ciCode, loginCode);
            handleCiCodes(preProcessCiCodes, "所属末级流程", null, ciCode, loginCode);
            handleCiCodes(postProcessCiCodes, "所属末级流程", null, ciCode, loginCode);

            String publishDiagramEnergy = generalPushSvc.publishDiagram(diagramRelationSysPirvateList.get(0).getDiagramEnergy(), publishReason, -100L, null, false);
            eamDiagramRelationSys.setDiagramEnergy(publishDiagramEnergy);
            eamDiagramRelationSysDao.saveOrUpdate(eamDiagramRelationSys);

            return publishDiagramEnergy;
        } else {
            // 视图没有被修改过就不更新
            return null;
        }
    }

    private void handleCiCodes(List<String> ciCodes, String attrKey, String ownerKey, String ciCode, String loginCode) {
        if (CollectionUtils.isEmpty(ciCodes)) {
            return;
        }

        List<ESCIInfo> ciByCodes = ciSwitchSvc.getCiByCodes(ciCodes, loginCode, LibType.PRIVATE);
        CcCiInfo privateCiInfo = ciSwitchSvc.getCiByCode(ciCode, loginCode, LibType.PRIVATE);
        List<ESCIInfo> ccCiInfos = new ArrayList<>();

        for (ESCIInfo ciByCode : ciByCodes) {
            Object linkFlowMsg = ciByCode.getAttrs().get(attrKey);
            boolean changeFlag = false;

            if (linkFlowMsg == null || org.apache.commons.lang3.StringUtils.isBlank(linkFlowMsg.toString())) {
                // 填充当前ci
                Map<String, String> stringStringHashMap = new HashMap<>();
                stringStringHashMap.put("ciCode", ciCode);
                JSONArray objects = JSON.parseArray(privateCiInfo.getCi().getCiPrimaryKey());
                stringStringHashMap.put("primary", objects.getString(1) + "|" + objects.getString(2));
                ciByCode.getAttrs().put(attrKey, JSON.toJSONString(Collections.singletonList(stringStringHashMap)));
                changeFlag = true;
            }

            if (ownerKey != null) {
                Object activeOwner = ciByCode.getAttrs().get(ownerKey);
                if (activeOwner == null || StringUtils.isBlank(activeOwner.toString())) {
                    //填充活动OWNER字段
                    UserInfo userInfoByLoginCode = iUserApiSvc.getUserInfoByLoginCode(loginCode);
                    List<JSONObject> jsonObjects = new ArrayList<>();
                    JSONObject userJson = new JSONObject();
                    userJson.put("loginCode", loginCode);
                    userJson.put("userName", userInfoByLoginCode.getUserName());
                    jsonObjects.add(userJson);
                    ciByCode.getAttrs().put(ownerKey, JSON.toJSONString(jsonObjects));
                    changeFlag = true;
                }
            }
            if (changeFlag) {
                ccCiInfos.add(ciByCode);
            }
        }
        if (!CollectionUtils.isEmpty(ccCiInfos)) {
            SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
            List<Long> collect = ccCiInfos.stream().map(ESCIInfo::getClassId).collect(Collectors.toList());
            ciSwitchSvc.saveOrUpdateBatchCI(ccCiInfos, collect, currentUserInfo.getLoginCode(), currentUserInfo.getLoginCode(), LibType.PRIVATE);
        }
    }

    private Map<String, Object> getFlowFileHistory(FlowSystemApproveData approveData) {
        Map<String, Object> result = new HashMap<>();
        Map<String, Object> linkCiMap = approveData.getLinkCiMap();
        CcCiInfo ciInfo = approveData.getCiInfo();
        if (ciInfo == null) {
            throw new MessageException("流程数据为空");
        }
        List<String> keys = Arrays.asList("活动", "术语", "表单", "前置流程", "后置流程", "风险", "档案", "指标", "岗位角色");
        Map<String, CompletableFuture<List<Map<String, String>>>> futures = keys.stream()
                .collect(Collectors.toMap(
                        key -> key,
                        key -> CompletableFuture.supplyAsync(() -> {
                            try {
                                Map<String, Object> dataMap = (Map<String, Object>) linkCiMap.getOrDefault(key, Collections.emptyMap());
                                List<JSONObject> dataList = Optional.ofNullable(dataMap.get("dataList"))
                                        .map(data -> (List<JSONObject>) data)
                                        .orElse(Collections.emptyList());

                                return dataList.stream()
                                        .map(jsonObj -> {
                                            // 获取 attrs 字段，并转换为 Map<String, String>
                                            Map<String, String> attrs = jsonObj.getObject("attrs", Map.class);
                                            return attrs != null ? attrs : Collections.<String, String>emptyMap();
                                        })
                                        .collect(Collectors.toList());

                            } catch (Exception e) {
                                log.error("处理{}数据失败: {}", key, e.getMessage());
                                return Collections.emptyList();
                            }
                        })
                ));

        // 等待所有任务完成
        CompletableFuture.allOf(futures.values().toArray(new CompletableFuture[0])).join();

        // 收集结果
        Map<String, List<Map<String, String>>> collect = futures.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        e -> e.getValue().join()
                ));

// 术语
        Map<String, Object> termList = new HashMap<>();
        List<Map> dataList = new ArrayList<>();
        String[] attr = {"名称", "定义"};
        termList.put("attrs", attr);
        List<Map<String, String>> terms = collect.get("术语");
        if (!terms.isEmpty()) {
            for (Map<String, String> info : terms) {
                Map<String, String> attrs = new LinkedHashMap<>();
                attrs.put("名称", info.get("名称"));
                attrs.put("定义", info.get("定义"));
                dataList.add(safeGetAttrs(attrs));
            }
        }
        termList.put("dataList", dataList);
        result.put("术语", termList);

        // 处理角色、职责与对应岗位
        Map<String, Object> roleData = new HashMap<>();
        List<Map> roleDataList = new ArrayList<>();
        String[] roleAttrs = {"序号", "角色名称", "职责", "对应职位/岗位"};
        roleData.put("attrs", roleAttrs);
        List<Map<String, String>> roles = collect.get("岗位角色");
        if (!roles.isEmpty()) {
            int i = 1;
            for (Map<String, String> info : roles) {
                Map<String, String> attrs = new LinkedHashMap<>();
                attrs.put("序号", String.valueOf(i++));
                attrs.put("角色名称", info.get("角色名称"));
                attrs.put("职责", info.get("角色职责"));
                attrs.put("对应职位/岗位", extractPrimaryValues(info.get("关联岗位"), ";", "岗位"));
                roleDataList.add(safeGetAttrs(attrs));
            }
        }
        roleData.put("dataList", roleDataList);
        result.put("角色、职责与对应岗位", roleData);

        // 处理流程说明
        Map<String, Object> processData = new HashMap<>();
        Map<String, Object> flowData = new HashMap<>();
        List<Map> calledProcesses = new ArrayList<>();
        List<Map> processDataList = new ArrayList<>();
        String[] processAttrs = {"活动序号", "活动名称", "活动内容", "角色", "输入表单", "输入说明", "输出表单", "输出说明"};
        processData.put("attrs", processAttrs);
        List<Map<String, String>> active = collect.get("活动");
        if (!active.isEmpty()) {
            int a = 1;
            for (Map<String, String> esciInfo : active) {
                if (!StringUtils.isEmpty(esciInfo.get("责任角色"))) {
                    Map<String, String> detail = new LinkedHashMap<>();
                    detail.put("活动序号", String.valueOf(a++));
                    detail.put("活动名称", esciInfo.get("活动名称"));
                    detail.put("活动内容", esciInfo.get("活动说明"));
                    detail.put("角色", extractPrimaryValues(esciInfo.get("责任角色"), ",", "角色"));
                    detail.put("输入表单", extractPrimaryValues(esciInfo.get("输入表单"), ",", "表单"));
                    detail.put("输入说明", esciInfo.get("输入说明"));
                    detail.put("输出表单", extractPrimaryValues(esciInfo.get("输出表单"), ",", "表单"));
                    detail.put("输出说明", esciInfo.get("输出说明"));
                    processDataList.add(safeGetAttrs(detail));
                }
                if (!StringUtils.isEmpty(esciInfo.get("调用流程"))) {
                    String s1 = esciInfo.get("调用流程");
                    JSONArray array = JSONArray.parseArray(s1);
                    StringBuilder sb = new StringBuilder();
                    Set<String> processedPrimary = new HashSet<>();
                    for (int i = 0; i < array.size(); i++) {
                        JSONObject object = array.getJSONObject(i);
                        String primary = object.getString("primary");
                        // 如果这个 primary 已经处理过，就跳过
                        if (!processedPrimary.add(primary)) {
                            continue;
                        }
                        Map<String, String> processInfo = new LinkedHashMap<>();
                        String[] primaryParts = primary.split("\\|");
                        processInfo.put("流程名称", primaryParts[1]);
                        processInfo.put("流程编码", primaryParts[0]);
                        calledProcesses.add(processInfo);
                    }
                }
            }
        }
        processData.put("dataList", processDataList);
        result.put("流程说明", processData);
        String[] flowAttrs = {"流程名称", "流程编码"};
        flowData.put("attrs", flowAttrs);
        flowData.put("dataList", calledProcesses);
        result.put("调用流程", flowData);

// 处理前置流程
        Map<String, Object> preProcessData = new HashMap<>();
        List<Map> preProcessDataList = new ArrayList<>();
        String[] preProcessAttrs = {"序号", "前置流程", "(本)流程输入", "(本)流程要求"};
        preProcessData.put("attrs", preProcessAttrs);
        List<Map<String, String>> preProcesses = collect.get("前置流程");
        if (!preProcesses.isEmpty()) {
            int j = 1;
            for (Map<String, String> info : preProcesses) {
                Map<String, String> attrs = new LinkedHashMap<>();
                attrs.put("序号", String.valueOf(j++));
                attrs.put("前置流程", extractPrimaryValues(info.get("前置流程名称"), ",", "前置流程"));
                String outputs = extractPrimaryValues(info.get("输出"), ",", "表单");
                attrs.put("(本)流程输入", outputs);
                attrs.put("(本)流程要求", info.get("要求"));
                preProcessDataList.add(safeGetAttrs(attrs));
            }
        }
        preProcessData.put("dataList", preProcessDataList);
        result.put("前置流程", preProcessData);

// 处理后置流程
        Map<String, Object> postProcessData = new HashMap<>();
        List<Map> postProcessDataList = new ArrayList<>();
        String[] postProcessAttrs = {"序号", "后置流程", "(本)流程输出", "(本)流程要求"};
        postProcessData.put("attrs", postProcessAttrs);
        List<Map<String, String>> postProcesses = collect.get("后置流程");
        if (!postProcesses.isEmpty()) {
            int k = 1;
            for (Map<String, String> info : postProcesses) {
                Map<String, String> attrs = new LinkedHashMap<>();
                attrs.put("序号", String.valueOf(k++));
                attrs.put("后置流程", extractPrimaryValues(info.get("后置流程名称"), ",", "后置流程"));
                String inputs = extractPrimaryValues(info.get("输入"), ",", "表单");
                attrs.put("(本)流程输出", inputs);
                attrs.put("(本)流程要求", info.get("要求"));
                postProcessDataList.add(safeGetAttrs(attrs));
            }
        }
        postProcessData.put("dataList", postProcessDataList);
        result.put("后置流程", postProcessData);

        // 处理表单数据
        Map<String, Object> formData = new HashMap<>();
        List<Map> formDataList = new ArrayList<>();
        String[] formAttrs = {"序号", "文件名称", "文件编码"};
        formData.put("attrs", formAttrs);
        List<Map<String, String>> inputForms = collect.get("表单");
        if (!inputForms.isEmpty()) {
            int l = 1;
            for (Map<String, String> info : inputForms) {
                Map<String, String> attrs = new LinkedHashMap<>();
                attrs.put("序号", String.valueOf(l++));
                attrs.put("文件名称", info.get("表单名称"));
                attrs.put("文件编码", info.get("表单编号"));
                formDataList.add(attrs);
            }
        }
        formData.put("dataList", formDataList);
        result.put("相关文件", formData);

// 处理版本记录
        Map<String, Object> versionData = new HashMap<>();
        List<Map> versionDataList = new ArrayList<>();
        String[] versionAttrs = {"版本", "拟制/修订责任人", "拟制/修订日期", "修订内容及理由"};
        versionData.put("attrs", versionAttrs);
        List<FlowProcessSystemPublishHistoryDto> publishedVersions = findOperationList(ciInfo.getCi().getCiCode(), "published");
        FlowProcessSystemPublishHistoryDto latestVersion = null;
        if (!publishedVersions.isEmpty()) {
            for (FlowProcessSystemPublishHistoryDto version : publishedVersions) {
                if (latestVersion == null || version.getCreateTime() > latestVersion.getCreateTime()) {
                    latestVersion = version;
                }
                Map<String, String> versionDetails = new LinkedHashMap<>();
                versionDetails.put("版本", version.getVersionName());
                versionDetails.put("拟制/修订责任人", version.getCreator());
                versionDetails.put("拟制/修订日期", String.valueOf(version.getCreateTime()));
                versionDetails.put("修订内容及理由", version.getChangeData());
                versionDataList.add(safeGetAttrs(versionDetails));
            }
        }
        versionData.put("dataList", versionDataList);
        result.put("版本记录", versionData);
        Map<String, Object> departmentData = new HashMap<>();
        departmentData.put("dataList", new ArrayList<>());
        result.put("部门审查", departmentData);
        Map<String, Object> businessData = new HashMap<>();
        businessData.put("dataList", new ArrayList<>()); // 使用新的ArrayList而不是自引用
        result.put("业务审查", businessData);
        Map<String, Object> integrationData = new HashMap<>();
        integrationData.put("dataList", new ArrayList<>()); // 使用新的ArrayList而不是自引用
        result.put("集成验证", integrationData);

        // 处理指标
        String[] categoryAttrs = new String[]{"指标名称", "设置目的与定义", "计算公式", "计量单位", "统计周期", "说明"};
        List<Map> categoryDataList = new ArrayList<>();
        Map<String, Object> zData = new HashMap<>();
        zData.put("attrs", categoryAttrs);
        zData.put("dataList", categoryDataList);
        List<Map<String, String>> category = collect.get("指标");
        if (!category.isEmpty()) {
            for (Map<String, String> info : category) {
                Map<String, String> attrs = new LinkedHashMap<>();
                attrs.put("指标名称", info.get("名称"));
                attrs.put("设置目的与定义", info.get("设置目的与定义"));
                attrs.put("计算公式", info.get("计算公式"));
                attrs.put("计量单位", info.get("计量单位"));
                attrs.put("统计周期", info.get("统计周期"));
                attrs.put("说明", info.get("说明"));
                categoryDataList.add(attrs);
            }
        }
        result.put("流程绩效指标", zData);

// 处理档案
        categoryAttrs = new String[]{"记录名称", "移交责任人", "保存责任人", "保存场所", "归档时间", "保存期限", "到期处理方式"};
        zData = new HashMap<>();
        categoryDataList = new ArrayList<>();
        zData.put("attrs", categoryAttrs);
        zData.put("dataList", categoryDataList);
        category = collect.get("档案");
        if (!category.isEmpty()) {
            for (Map<String, String> info : category) {
                Map<String, String> attrs = new LinkedHashMap<>();
                attrs.put("记录名称", info.get("记录名称"));
                attrs.put("移交责任人", info.get("移交责任人"));
                attrs.put("保存责任人", info.get("保存责任人"));
                attrs.put("保存场所", info.get("保存场所"));
                attrs.put("归档时间", info.get("归档时间"));
                attrs.put("保存期限", info.get("保存期限"));
                attrs.put("到期处理方式", info.get("到期处理方式"));
                categoryDataList.add(attrs);
            }
        }
        result.put("记录保存", zData);

// 处理风险
        categoryAttrs = new String[]{"序号", "风险名称", "风险描述"};
        zData = new HashMap<>();
        categoryDataList = new ArrayList<>();
        zData.put("attrs", categoryAttrs);
        zData.put("dataList", categoryDataList);
        category = collect.get("风险");
        if (!category.isEmpty()) {
            int i = 1;
            for (Map<String, String> info : category) {
                Map<String, String> attrs = new LinkedHashMap<>();
                attrs.put("序号", String.valueOf(i++));
                attrs.put("风险名称", info.get("风险名称"));
                attrs.put("风险描述", info.get("风险描述"));
                categoryDataList.add(attrs);
            }
        }
        result.put("风险", zData);

// 处理场景
        Map<String, List<String>> sceneActiveRlt = (Map<String, List<String>>) approveData.getLinkCiMap().get("场景");
        zData = new HashMap<>();
        categoryDataList = new ArrayList<>();
        categoryAttrs = new String[]{"场景", "活动"};
        zData.put("attrs", categoryAttrs);
        zData.put("dataList", categoryDataList);

        if (sceneActiveRlt != null) {
            for (Map.Entry<String, List<String>> entry : sceneActiveRlt.entrySet()) {
                Map<String, String> attrs = new LinkedHashMap<>();
                CcCiInfo ciByCode = ciSwitchSvc.getCiByCode(entry.getKey(),
                        SysUtil.getCurrentUserInfo().getLoginCode(), LibType.PRIVATE);
                if (ciByCode != null) {
                    List<ESCIInfo> ciByCodes = ciSwitchSvc.getCiByCodes(entry.getValue(),
                            SysUtil.getCurrentUserInfo().getLoginCode(), LibType.PRIVATE);
                    String activityNames = ciByCodes.stream()
                            .map(c -> (String) c.getAttrs().get("活动名称"))
                            .filter(Objects::nonNull)
                            .collect(Collectors.joining(","));
                    attrs.put("场景", ciByCode.getAttrs().get("场景名称"));
                    attrs.put("活动", activityNames == null ? "" : activityNames);
                    categoryDataList.add(attrs);
                }
            }
        }
        result.put("裁剪指南", zData);

        Map<String, String> attributes = ciInfo.getAttrs();
        List<Map> integrationReview = new ArrayList<>();
        Map<String, String> reviewDetails = new LinkedHashMap<>();
        integrationReview.add(reviewDetails);
        Map<String, Object> departmentData1 = new HashMap<>();
        departmentData1.put("dataList", integrationReview);
        result.put("编写", departmentData1);
        attributes.put("签发", "");
        attributes.put("流程图标题", attributes.get("流程编码") + attributes.get("流程名称") + "流程图");
        String version = latestVersion != null ? "-" + latestVersion.getVersionName() : "";
        attributes.put("流程编码", "NPIC-BP-[" + attributes.get("流程编码") + "]" + version);
        attributes.put("目的", attributes.get("流程目的"));
        attributes.put("输入", attributes.get("流程输入"));
        attributes.put("输出", attributes.get("流程输出"));
        String configType = bmConfigSvc.getConfigType("FLOW_SYSTEM_FILE_HEADER");
        if(org.apache.commons.lang3.StringUtils.isNotBlank(configType)){
            attributes.put("标题", configType);
        }else {
            attributes.put("标题", "北京优锘科技股份有限公司");
        }

        Map<String, Object> flowDetails = new HashMap<>();
        flowDetails.put("dataList", attributes);
        result.put("流程", flowDetails);

        Map<String, Object> flowDetail = new HashMap<>();
        List<Map> detail1 = new ArrayList<>();
        Map<String, String> detail = new LinkedHashMap<>();
        String[] formAttr = {"流程起点", "流程终点", "输入", "输出"};
        detail.put("流程起点", attributes.get("流程起点"));
        detail.put("流程终点", attributes.get("流程终点"));
        detail.put("输入", attributes.get("输入描述"));
        detail.put("输出", attributes.get("输出描述"));
        detail1.add(detail);
        flowDetail.put("dataList", detail1);
        result.put("流程范围", flowDetail);
        return result;
    }


}