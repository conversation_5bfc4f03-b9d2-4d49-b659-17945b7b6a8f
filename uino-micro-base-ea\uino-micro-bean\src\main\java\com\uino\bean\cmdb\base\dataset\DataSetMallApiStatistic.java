package com.uino.bean.cmdb.base.dataset;

import java.util.List;

import org.springframework.util.Assert;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.binary.core.util.BinaryUtils;
import com.uino.bean.permission.business.IValidDto;

import lombok.Getter;
import lombok.Setter;

/**
 * @Classname DataSetMallApiStatistic
 * @Description 统计数据集
 * @Date 2021/01/12 15:46
 * <AUTHOR> zmj
 */
@Getter
@Setter
public class DataSetMallApiStatistic extends DataSetMallApi implements IValidDto {

    private static final long serialVersionUID = 1L;

    /**
     * 图表类型,0=柱状图，1=饼图，2=堆叠图，3=条形图,4=曲线图
     */
    private Integer chartType = 0;

    /**
     * 数据来源类型，1=配置数据集，2=配置数据
     */
    private Integer dataSourceType;

    /**
     * 数据来源：配置数据集id/分类id
     */
    private Long dataSourceId;

    /**
     * 维度分类id
     */
    private List<Long> dimensionClassIds;

    /**
     * 维度属性id
     */
    private Long dimensionDefId;

    /**
     * 度量分类id
     */
    private Long statisticClassId;

    /**
     * 度量属性id
     */
    private Long statisticDefId;

    /**
     * 统计类型：0=计数(去重),1=求和，2=计数
     */
    private Integer statisticalType = 0;

    public DataSetMallApiStatistic() {}

    public DataSetMallApiStatistic(JSONObject json) {
        super(json);
        if (json.containsKey("dataSourceType")) {
            this.dataSourceType = json.getInteger("dataSourceType");
        }
        if (json.containsKey("dataSourceId")) {
            this.dataSourceId = json.getLong("dataSourceId");
        }
        if (json.containsKey("dimensionClassIds")) {
            this.dimensionClassIds = JSON.parseArray(JSON.toJSONString(json.getJSONArray("dimensionClassIds")), Long.class);
        }
        if (json.containsKey("dimensionDefId")) {
            this.dimensionDefId = json.getLong("dimensionDefId");
        }
        if (json.containsKey("statisticClassId")) {
            this.statisticClassId = json.getLong("statisticClassId");
        }
        if (json.containsKey("statisticDefId")) {
            this.statisticDefId = json.getLong("statisticDefId");
        }
        if (json.containsKey("statisticalType")) {
            this.statisticalType = json.getInteger("statisticalType");
        }
        if (json.containsKey("chartType")) {
            this.chartType = json.getInteger("chartType");
        }
    }

    @Override
    public JSONObject toJson() {
        JSONObject json = super.toJson();
        json.put("dataSourceType", dataSourceType);
        json.put("dataSourceId", dataSourceId);
        json.put("dimensionClassIds", dimensionClassIds);
        json.put("dimensionDefId", dimensionDefId);
        json.put("statisticClassId", statisticClassId);
        json.put("statisticDefId", statisticDefId);
        json.put("statisticalType", statisticalType);
        json.put("chartType", chartType);
        return json;
    }

    @Override
    public void valid() {
        Assert.notNull(dataSourceType, "X_PARAM_NOT_NULL${name:dataSourceType}");
        Assert.isTrue(!BinaryUtils.isEmpty(dimensionClassIds), "X_PARAM_NOT_NULL${name:dimensionClassIds}");
        Assert.notNull(statisticalType, "X_PARAM_NOT_NULL${name:statisticalType}");
        // 配置数据集
        if (dataSourceType.intValue() == 1) {
            Assert.notNull(dataSourceId, "X_PARAM_NOT_NULL${name:dataSourceId}");
            Assert.isTrue(dimensionClassIds.size() == 1, "BS_MVTYPE_ARG_ERROR");
            Assert.notNull(dimensionDefId, "X_PARAM_NOT_NULL${name:dimensionDefId}");
            Assert.notNull(statisticClassId, "X_PARAM_NOT_NULL${name:statisticClassId}");
            Assert.notNull(statisticDefId, "X_PARAM_NOT_NULL${name:statisticDefId}");
        } else if (dataSourceType.intValue() == 2) {
            // 配置数据
        } else {
            Assert.isTrue(false, "不支持的数据来源");
        }
    }

}
