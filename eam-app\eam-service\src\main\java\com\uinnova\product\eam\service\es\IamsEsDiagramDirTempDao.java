package com.uinnova.product.eam.service.es;

import com.uinnova.product.eam.comm.model.es.CEamDiagramDir;
import com.uinnova.product.eam.comm.model.es.CEamDiagramDirTemp;
import com.uinnova.product.eam.comm.model.es.EamDiagramDir;
import com.uinnova.product.eam.comm.model.es.EamDiagramDirTemp;
import com.uino.dao.AbstractESBaseDao;
import com.uino.service.util.FileUtil;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.util.List;

/**
 * <AUTHOR>
 * @Classname IamsEsDiagramDirTempDao
 * @Date 2022/3/15 18:46
 */
@Deprecated
@Service
public class IamsEsDiagramDirTempDao extends AbstractESBaseDao<EamDiagramDirTemp, CEamDiagramDirTemp> {
    @Override
    public String getIndex() {
        return "uino_eam_diagram_dir_temp";
    }

    @Override
    public String getType() {
        return getIndex();
    }

    @PostConstruct
    public void init() {
        super.initIndex();
        //初始化架构资产目录
        List<EamDiagramDirTemp> data = FileUtil.getData("/initdata/uino_eam_diagram_dir.json", EamDiagramDirTemp.class);
        this.saveOrUpdateBatch(data);
    }
}
