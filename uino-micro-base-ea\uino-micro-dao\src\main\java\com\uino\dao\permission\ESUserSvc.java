package com.uino.dao.permission;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;

import com.uino.dao.BaseConst;
import com.uino.util.cache.ICacheService;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.TermQueryBuilder;
import org.elasticsearch.index.query.TermsQueryBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.binary.core.util.BinaryUtils;
import com.uinnova.product.vmdb.comm.util.CommUtil;
import com.uino.dao.AbstractESBaseDao;
import com.uino.dao.ESConst;
import com.uino.dao.permission.rlt.ESPerssionCommSvc;
import com.uino.dao.permission.rlt.ESRoleDataModuleRltSvc;
import com.uino.dao.permission.rlt.ESUserDataModuleRltSvc;
import com.uino.dao.permission.rlt.ESUserModuleRltSvc;
import com.uino.util.sys.BeanUtil;
import com.uino.util.sys.CommonFileUtil;
import com.uino.bean.permission.base.SysModule;
import com.uino.bean.permission.base.SysOrg;
import com.uino.bean.permission.base.SysRole;
import com.uino.bean.permission.base.SysRoleDataModuleRlt;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.permission.base.SysUserDataModuleRlt;
import com.uino.bean.permission.base.SysUserOrgRlt;
import com.uino.bean.permission.base.SysUserRoleRlt;
import com.uino.bean.permission.business.AuthUserInfo;
import com.uino.bean.permission.business.UserInfo;
import com.uino.bean.permission.query.CSysUser;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
@RefreshScope
public class ESUserSvc extends AbstractESBaseDao<SysUser, CSysUser> {

	@Override
	public String getIndex() {
		return ESConst.INDEX_SYS_USER;
	}

	@Override
	public String getType() {
		return ESConst.INDEX_SYS_USER;
	}

	@PostConstruct
	public void init() {
		List<SysUser> data = CommonFileUtil.getData("/initdata/uino_sys_user.json", SysUser.class);
		initIndex(data);
	}

	@Autowired
	ESRoleSvc roleSvc;

	@Autowired
	ESModuleSvc moduleSvc;

	@Autowired
	ESOrgSvc orgSvc;
	@Autowired
	private ESOrgSvc orgBuinessSvc;

    @Autowired
    ESPerssionCommSvc commSvc;

	@Autowired
	ESUserModuleRltSvc userModuleRltSvc;

	@Autowired
	ESRoleDataModuleRltSvc roleDataModuleSvc;

	@Autowired
	ESUserDataModuleRltSvc userDataModuleSvc;

	@Value("${http.resource.space:}")
	private String urlPath;

	@Resource
	private ICacheService iCacheService;

	@Override
	public Long saveOrUpdate(SysUser user) {
		if(BinaryUtils.isEmpty(user.getDomainId())){
			user.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
		}
		String icon = user.getIcon();
		if (icon != null && icon.startsWith(urlPath)) {
			user.setIcon(icon.substring(icon.indexOf(urlPath) + urlPath.length()));
		}
		return super.saveOrUpdate(user);
	}

	/**
	 * 获取指定用户拥有的数据权限字典
	 * 
	 * @param userId
	 * @param moduleCodes
	 * @return
	 */
	public Map<String, List<SysRoleDataModuleRlt>> getDataModule(Long userId, Collection<String> moduleCodes) {
		// 数据权限字典，最终结果会扔这里；结构 dataModuleCode:[]
		Map<String, List<SysRoleDataModuleRlt>> dataModule = new LinkedHashMap<>();
		if (userId == null) {
			return null;
		}
		// 用户所属角色
		List<SysRole> roles = roleSvc.getListByUserId(userId);
		Set<Long> roleIds = new HashSet<Long>();
		for (SysRole role : roles) {
			roleIds.add(role.getId());
		}
		// 用户绑定的数据权限
		List<SysUserDataModuleRlt> userDataMoulelist = null;
		// 用户所属角色绑定的数据权限
		List<SysRoleDataModuleRlt> roleDataMoulelist = null;
		// 获取用户以及所属角色对应权限，根据是否传递了数据模块codes走不同的获取策略
		if (moduleCodes != null && moduleCodes.size() > 0) {
			userDataMoulelist = userDataModuleSvc.getListByUserId(userId, new ArrayList<>(moduleCodes));
			roleDataMoulelist = roleDataModuleSvc.getListByRoleIds(roleIds, new ArrayList<>(moduleCodes));
		} else {
			userDataMoulelist = userDataModuleSvc.getListByUserId(userId);
			roleDataMoulelist = roleDataModuleSvc.getListByRoleIds(roleIds);
		}
		// 组合数据权限
		dataModule = mergeRole(roleDataMoulelist, userDataMoulelist);
		return dataModule;
	}

	/**
	 * 获取用户认证的具体权限
	 * 
	 * @param userId
	 * @return
	 */
	public AuthUserInfo getAuthUserInfo(Long userId) {
		SysUser sysUser = super.getById(userId);
		if (sysUser == null) {
			return null;
		}

		AuthUserInfo authUser = JSON.parseObject(JSON.toJSONString(sysUser), AuthUserInfo.class);
		List<SysRole> roles = roleSvc.getListByUserId(userId);
		Set<Long> roleIds = new HashSet<Long>();
		for (SysRole role : roles) {
			roleIds.add(role.getId());
		}

		// 合并菜单
		List<SysModule> userMenuList = moduleSvc.getListByUserId(userId);
		List<SysModule> roleMenuList = moduleSvc.getListByRoleIds(roleIds);
		Set<SysModule> menus = new HashSet<SysModule>();
		menus.addAll(userMenuList);
		menus.addAll(roleMenuList);
		authUser.setMenus(menus);

		// 数据权限
		// List<SysUserDataModuleRlt> userDataMoulelist =
		// userDataModuleSvc.getListByUserId(userId);
		// List<SysRoleDataModuleRlt> roleDataMoulelist =
		// roleDataModuleSvc.getListByRoleIds(roleIds);

		// Map<String, List<SysRoleDataModuleRlt>> dataModule =
		// mergeRole(roleDataMoulelist, userDataMoulelist);
		Map<String, List<SysRoleDataModuleRlt>> dataModule = getDataModule(userId, null);
		authUser.setDataModule(dataModule);

		authUser.setAllowChangePasswd(null);
		authUser.setCreateTime(null);
		authUser.setModifyTime(null);
		authUser.setCreator(null);
		authUser.setLoginPasswd(null);
		authUser.setIsUpdatePwd(null);
		authUser.setLastLoginLogId(null);
		authUser.setLoginAuthCode(null);
		return authUser;
	}

	/**
	 * 数据权限合并
	 * 
	 * @return
	 */
	private Map<String, List<SysRoleDataModuleRlt>> mergeRole(List<SysRoleDataModuleRlt> roleDMList,
			List<SysUserDataModuleRlt> userDMList) {

		Map<String, List<SysRoleDataModuleRlt>> dataModule = new HashMap<String, List<SysRoleDataModuleRlt>>();
		Map<String, SysRoleDataModuleRlt> map = new HashMap<String, SysRoleDataModuleRlt>();
		List<SysRoleDataModuleRlt> ulist = BeanUtil.converBean(userDMList, SysRoleDataModuleRlt.class);
		roleDMList.addAll(ulist);

		for (SysRoleDataModuleRlt rlt : roleDMList) {
			String key = rlt.getDataModuleCode() + "xx@@@@xx" + rlt.getDataValue();
			if (map.get(key) == null) {
				map.put(key, rlt);
			} else {
				// 新的对象
				int d = rlt.getIsdelete() == null ? 0 : rlt.getIsdelete();
				int r = rlt.getIssee() == null ? 0 : rlt.getIssee();
				int u = rlt.getIsupdate() == null ? 0 : rlt.getIsupdate();
				int c = rlt.getIscreate() == null ? 0 : rlt.getIscreate();
				// 原有对象
				SysRoleDataModuleRlt dm = map.get(key);
				int dd = dm.getIsdelete() == null ? 0 : dm.getIsdelete();
				int dr = dm.getIssee() == null ? 0 : dm.getIssee();
				int du = dm.getIsupdate() == null ? 0 : dm.getIsupdate();
				int dc = dm.getIscreate() == null ? 0 : dm.getIscreate();
				if (d > dd) {
					dm.setIsdelete(d);
				}
				if (r > dr) {
					dm.setIssee(r);
				}
				if (u > du) {
					dm.setIsupdate(u);
				}
				if (c > dc) {
					dm.setIscreate(dc);
				}

			}
		}

		Iterator<String> it = map.keySet().iterator();
		while (it.hasNext()) {
			String key = it.next();
			String[] keys = key.split("xx@@@@xx");
			String code = keys[0];
			SysRoleDataModuleRlt dm = map.get(key);
			if (dataModule.get(code) == null) {
				List<SysRoleDataModuleRlt> list = new ArrayList<SysRoleDataModuleRlt>();
				list.add(dm);
				dataModule.put(code, list);
			} else {
				dataModule.get(code).add(dm);
			}
		}

		return dataModule;
	}

	/**
	 * 级联删除用户相关数据
	 * 
	 * @param id
	 * @return
	 */
	public Integer deleteUserById(Long id) {
		TermQueryBuilder query = QueryBuilders.termQuery("userId", id);
		// 删除用户角色关系
        commSvc.deleteUserRoleRltByQuery(query, true);
		// 删除用户组织关系
        commSvc.deleteUserOrgRltByQuery(query, true);
		// 删除用户菜单关系
        userModuleRltSvc.deleteByQuery(query, true);
		// 删除用户数据关系
        userDataModuleSvc.deleteByQuery(query, true);
		return deleteById(id);
	}

	/**
	 * 级联删除用户相关数据
	 * 
	 * @param ids
	 * @return
	 */
	public Integer deleteUserByIds(List<Long> ids) {
		if (BinaryUtils.isEmpty(ids)) {
			return 1;
		}
		TermsQueryBuilder query = QueryBuilders.termsQuery("userId", ids);
		// 删除用户角色关系
        commSvc.deleteUserRoleRltByQuery(query, true);
		// 删除用户组织关系
        commSvc.deleteUserOrgRltByQuery(query, true);
		// 删除用户菜单关系
		userModuleRltSvc.deleteByQuery(query, true);
		// 删除用户数据关系
		userDataModuleSvc.deleteByQuery(query, true);
		return deleteByIds(ids);
	}

	public List<UserInfo> getUserInfoListByQuery(QueryBuilder query, boolean rmSensitiveData) {
		List<SysUser> users = getListByQuery(query);
		return this.fillInfo(users, rmSensitiveData);
	}

	/**
	 * 填充用户信息
	 *
	 * @param users
	 * @return
	 */
	public List<UserInfo> fillInfo(List<SysUser> users, boolean rmSensitiveData) {
		List<UserInfo> infos = new ArrayList<UserInfo>();
		Map<Long, String> orgIdAllNameMap = null;
		if (!BinaryUtils.isEmpty(users)) {
			infos = CommUtil.copy(users, UserInfo.class);
			Set<Long> userIds = infos.stream().map(SysUser::getId).collect(Collectors.toSet());
	         // 查询用户-组织关系
            Map<Long, List<SysUserOrgRlt>> uoMap = new HashMap<>();
            Map<Long, SysOrg> oMap = new HashMap<>();
            List<SysUserOrgRlt> uOrgRlts = commSvc.getUserOrgRltByUserIds(userIds);
            if (!BinaryUtils.isEmpty(uOrgRlts)) {
                uoMap = BinaryUtils.toObjectGroupMap(uOrgRlts, "userId");
                Set<Long> orgIds = uOrgRlts.stream().map(SysUserOrgRlt::getOrgId).collect(Collectors.toSet());
                List<SysOrg> orgs = orgSvc
                        .getListByQuery(QueryBuilders.boolQuery().must(QueryBuilders.termsQuery("id", orgIds)));
                orgIdAllNameMap = orgBuinessSvc.getOrgIdAllNameMap(users.get(0).getDomainId(), orgIds);
                oMap = BinaryUtils.toObjectMap(orgs, "id");
            }
            
			// 查询用户-角色关系
			Map<Long, List<SysUserRoleRlt>> urMap = new HashMap<>();
			Map<Long, SysRole> rMap = new HashMap<>();
            List<SysUserRoleRlt> uRoleRlts = commSvc.getUserRoleRltByUserIds(userIds);
			if (!BinaryUtils.isEmpty(uRoleRlts)) {
				urMap = BinaryUtils.toObjectGroupMap(uRoleRlts, "userId");
                Set<Long> roleIds = uRoleRlts.stream().map(SysUserRoleRlt::getRoleId).collect(Collectors.toSet());
				List<SysRole> roles = roleSvc
						.getListByQuery(QueryBuilders.boolQuery().must(QueryBuilders.termsQuery("id", roleIds)));
				rMap = BinaryUtils.toObjectMap(roles, "id");
			}

			// 组装用户信息
			for (UserInfo info : infos) {
				// 丰富用户头像并去除敏感信息
                if (info.getIcon().startsWith("/")) {
                    info.setIcon(urlPath + info.getIcon());
                }
				if (rmSensitiveData) {
					info.clearSensitive();
				}
				// 拼装角色数据
				List<SysUserRoleRlt> urRlts = urMap.get(info.getId());
				if (!BinaryUtils.isEmpty(urRlts)) {
					Set<SysRole> roles = new HashSet<SysRole>();
					for (SysUserRoleRlt mr : urRlts) {
						SysRole role = rMap.get(mr.getRoleId());
						if (role != null) {
							roles.add(role);
						}
					}
					info.setRoles(roles);
				}
				// 拼装组织信息
				List<SysUserOrgRlt> uoRlts = uoMap.get(info.getId());
				if (!BinaryUtils.isEmpty(uoRlts)) {
					Set<SysOrg> uOrgs = new HashSet<SysOrg>();
					for (SysUserOrgRlt uo : uoRlts) {
						SysOrg org = oMap.get(uo.getOrgId());
						if (org != null) {
							org.setOrgAllLevelName(orgIdAllNameMap.get(org.getId()));
							uOrgs.add(org);
						}
					}
					info.setOrgs(uOrgs);
				}
			}
		}
		return infos;
	}

	public List<SysUser> getByIds(Collection<Long> ids) {

		return getListByQuery(QueryBuilders.termsQuery("id",ids));

	}
}
