package com.uinnova.product.vmdb.comm.model.ci;

import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;

/**
 * 
 * <AUTHOR>
 *
 */
@Comment("CI分类外挂属性[CC_CI_PLUGIN_ATTR]")
public class CcCiPluginAttr implements EntityBean {
    private static final long serialVersionUID = 1L;

    @Comment("ID[ID]")
    private Long id;

    @Comment("CI分类ID[CLASS_ID]")
    private Long classId;

    @Comment("分类类型[CI_TYPE]    分类类型:1=基础CI 2=关系CI")
    private Integer ciType;

    @Comment("属性名[PRO_NAME]")
    private String proName;

    @Comment("标准名[PRO_STD_NAME]    标准名:全部大写")
    private String proStdName;

    @Comment("属性类型[PRO_TYPE]    属性类型:1=外部链接 2=外部数据")
    private Integer proType;

    @Comment("属性描述[PRO_DESC]")
    private String proDesc;

    @Comment("属性值1[PRO_VAL_1]")
    private String proVal1;

    @Comment("属性值2[PRO_VAL_2]")
    private String proVal2;

    @Comment("属性值3[PRO_VAL_3]")
    private String proVal3;

    @Comment("排列顺序[ORDER_NO]")
    private Integer orderNo;

    @Comment("备用_1[CUSTOM_1]")
    private String custom1;

    @Comment("备用_2[CUSTOM_2]")
    private String custom2;

    @Comment("备用_3[CUSTOM_3]")
    private String custom3;

    @Comment("备用_4[CUSTOM_4]")
    private String custom4;

    @Comment("备用_5[CUSTOM_5]")
    private String custom5;

    @Comment("备用_6[CUSTOM_6]")
    private String custom6;

    @Comment("所属域[DOMAIN_ID]")
    private Long domainId;

    @Comment("数据状态[DATA_STATUS]    数据状态:0=删除，1=正常")
    private Integer dataStatus;

    @Comment("创建人[CREATOR]")
    private String creator;

    @Comment("修改人[MODIFIER]")
    private String modifier;

    @Comment("创建时间[CREATE_TIME]    创建时间:yyyyMMddHHmmss")
    private Long createTime;

    @Comment("更新时间[MODIFY_TIME]    更新时间:yyyyMMddHHmmss")
    private Long modifyTime;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getClassId() {
        return this.classId;
    }

    public void setClassId(Long classId) {
        this.classId = classId;
    }

    public Integer getCiType() {
        return this.ciType;
    }

    public void setCiType(Integer ciType) {
        this.ciType = ciType;
    }

    public String getProName() {
        return this.proName;
    }

    public void setProName(String proName) {
        this.proName = proName;
    }

    public String getProStdName() {
        return this.proStdName;
    }

    public void setProStdName(String proStdName) {
        this.proStdName = proStdName;
    }

    public Integer getProType() {
        return this.proType;
    }

    public void setProType(Integer proType) {
        this.proType = proType;
    }

    public String getProDesc() {
        return this.proDesc;
    }

    public void setProDesc(String proDesc) {
        this.proDesc = proDesc;
    }

    public String getProVal1() {
        return this.proVal1;
    }

    public void setProVal1(String proVal1) {
        this.proVal1 = proVal1;
    }

    public String getProVal2() {
        return this.proVal2;
    }

    public void setProVal2(String proVal2) {
        this.proVal2 = proVal2;
    }

    public String getProVal3() {
        return this.proVal3;
    }

    public void setProVal3(String proVal3) {
        this.proVal3 = proVal3;
    }

    public Integer getOrderNo() {
        return this.orderNo;
    }

    public void setOrderNo(Integer orderNo) {
        this.orderNo = orderNo;
    }

    public String getCustom1() {
        return this.custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }

    public String getCustom2() {
        return this.custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }

    public String getCustom3() {
        return this.custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }

    public String getCustom4() {
        return this.custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }

    public String getCustom5() {
        return this.custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }

    public String getCustom6() {
        return this.custom6;
    }

    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }

    public Long getDomainId() {
        return this.domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }

    public Integer getDataStatus() {
        return this.dataStatus;
    }

    public void setDataStatus(Integer dataStatus) {
        this.dataStatus = dataStatus;
    }

    public String getCreator() {
        return this.creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getModifier() {
        return this.modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public Long getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getModifyTime() {
        return this.modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }

}
