package com.uinnova.product.eam.base.util;

public class PageRequest<T> {

    private Integer pageNum;

    private Integer pageSize;

    private String orders;

    private T cdt;

    public PageRequest() {
    }

    public PageRequest(Integer pageNum, Integer pageSize, T cdt) {
        this.pageNum = pageNum;
        this.pageSize = pageSize;
        this.cdt = cdt;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getOrders() {
        return orders;
    }

    public void setOrders(String orders) {
        this.orders = orders;
    }

    public T getCdt() {
        return cdt;
    }

    public void setCdt(T cdt) {
        this.cdt = cdt;
    }
}
