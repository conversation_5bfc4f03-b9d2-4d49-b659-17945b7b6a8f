package com.uinnova.product.eam;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.ImportResource;
import org.springframework.scheduling.annotation.EnableScheduling;

@ImportResource(locations={"classpath:spring-context.xml"})
@SpringBootApplication
@Configuration
@EnableScheduling
@ServletComponentScan("com.uino.init")
@ComponentScan(basePackages = {
		"com.uinnova.product.eam.feign.server",
				"com.uinnova.product.vmdb.client", "com.uinnova.product.es",
				"com.uinnova.product.base.client", "com.uinnova.product.cmdb.client",
		"com.uinnova.product.eam.feign.server.web.**",
        "com.uinnova.product.eam.init",
		"com.uino.service.init","com.uino.common.es"})
@EnableFeignClients(basePackages = { "com.uinnova.product.cmdb.client", "com.uinnova.product.vmdb.client","com.uinnova.product.es", "com.uinnova.product.base.client"})
@Slf4j
public class EamFeignServerApplication {

	static {
		log.info("开始加载eam-feign-server模块");
	}

	public static void main(String[] args) {
		SpringApplication.run(EamFeignServerApplication.class, args);
	}
}
