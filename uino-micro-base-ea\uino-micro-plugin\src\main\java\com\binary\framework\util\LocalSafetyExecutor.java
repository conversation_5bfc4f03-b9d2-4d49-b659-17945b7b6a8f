package com.binary.framework.util;

import com.binary.core.util.BinaryUtils;
import com.binary.framework.Local;
import com.binary.framework.bean.User;
import com.binary.framework.exception.ServiceException;

public abstract class LocalSafetyExecutor {
	
	
	
	public static interface Performer {
		public void perform();
	}
	
	
	
	
	
	
	public static void execute(User user, Performer performer) {
		BinaryUtils.checkEmpty(performer, "performer");
		try {
			boolean open = Local.isOpen();
			try {
				if(!open) Local.open(user);
				performer.perform();
				if(!open) Local.commit();
			}catch(Throwable t) {
				if(!open) Local.rollback();
				throw t;
			}finally {
				if(!open) Local.close();
			}
		}catch(Throwable t) {
			throw BinaryUtils.transException(t, ServiceException.class, " do local safety execute error! ");
		}
	}
	
	
	

}
