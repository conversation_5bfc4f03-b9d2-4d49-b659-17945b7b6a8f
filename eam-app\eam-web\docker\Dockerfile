FROM adoptopenjdk/openjdk8:latest
MAINTAINER uino
USER root
ADD application-local.properties /usr/local/
ADD eam-deploy.tar.gz /usr/local/
ADD docker_start.sh /usr/local/
ADD libLicense-native-5.2.1.so /usr/local/
COPY eam-rsm.tar.gz /usr/local/
RUN rm -f /usr/local/eam/conf/application-local.properties \
	&& mv /usr/local/docker_start.sh /usr/local/eam/bin/ \
	&& mv /usr/local/application-local.properties /usr/local/eam/conf/ \
	&& chmod -R 777 /usr/local/eam/*

CMD /usr/local/eam/bin/docker_start.sh