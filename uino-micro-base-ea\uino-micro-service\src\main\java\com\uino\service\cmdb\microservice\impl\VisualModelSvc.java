package com.uino.service.cmdb.microservice.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.binary.core.exception.BinaryException;
import com.binary.core.exception.MessageException;
import com.binary.core.util.BinaryUtils;
import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClass;
import com.uino.bean.cmdb.base.*;
import com.uino.bean.cmdb.query.ESVisualModelSearchBean;
import com.uino.bean.permission.base.SysUser;
import com.uino.dao.BaseConst;
import com.uino.dao.cmdb.*;
import com.uino.dao.util.ESUtil;
import com.uino.service.cmdb.microservice.IVisualModelSvc;
import com.uino.util.cache.ICacheService;
import com.uino.util.sys.SysUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.sort.SortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@RefreshScope
public class VisualModelSvc implements IVisualModelSvc {

    @Autowired
    private ESVisualModelPrivateSvc visualModelPrivateSvc;

    @Autowired
    private ESVisualModelSvc visualModelSvc;

    @Autowired
    private ESCiClassRltSvc ciClassRltSvc;

    @Autowired
    private ESImpactPathSvc impactPathSvc;

    @Autowired
    private ESCIClassSvc ciClassSvc;

    @Autowired
    private ESRltClassSvc rltClassSvc;

    @Autowired
    private ICacheService iCacheService;

    @Value("${http.resource.space:}")
    private String rsmSlaveRoot;

    private static final String VISUAL_MODEL_KEY = "VISUAL:MODEL:KEY:";

    @Override
    public List<ESVisualModel> queryVisualModels(Long domainId) {
        domainId = domainId == null ? BaseConst.DEFAULT_DOMAIN_ID : domainId;
        // 获取有效CI和关系分类
        Map<Long, ESCIClassInfo> ciClassMap = new HashMap<Long, ESCIClassInfo>();
        CCcCiClass ciClassQuery = new CCcCiClass();
        ciClassQuery.setDomainId(domainId);
//        @ClassViewPermission List<ESCIClassInfo> ciClassList = ciClassSvc.getListByCdt(ciClassQuery);
        List<ESCIClassInfo> ciClassList = ciClassSvc.getListByCdt(ciClassQuery);
        if (ciClassList != null) {
            for (ESCIClassInfo classInfo : ciClassList) {
                ciClassMap.put(classInfo.getId(), classInfo);
            }
        }
        Map<Long, ESCIClassInfo> rltClassMap = new HashMap<Long, ESCIClassInfo>();
        CCcCiClass rltClassQuery = new CCcCiClass();
        rltClassQuery.setDomainId(domainId);
        List<ESCIClassInfo> rltClassList = rltClassSvc.getListByCdt(rltClassQuery);
        if (rltClassList != null) {
            for (ESCIClassInfo classInfo : rltClassList) {
                rltClassMap.put(classInfo.getId(), classInfo);
            }
        }
        // 获取模型
        List<ESVisualModel> models = new ArrayList<ESVisualModel>();
        try {
            ESVisualModelSearchBean query = new ESVisualModelSearchBean();
            query.setDomainId(domainId);
            List<SortBuilder<?>> sorts = new ArrayList<SortBuilder<?>>();
            SortBuilder<?> sort = SortBuilders.fieldSort("modifyTime").order(SortOrder.DESC);
            sorts.add(sort);
            models = visualModelSvc.getSortListByCdt(query, sorts);
        } catch (Exception e) {
            log.error("模型获取异常",e);
            throw new MessageException(e.getMessage());
        }
        // 校验CI和关系分类是否存在
        for (ESVisualModel model : models) {
            if (model.getJson() != null && !"".equals(model.getJson())) {
                try {
                    JSONArray jsonArray = JSONObject.parseArray(model.getJson());
                    List<JSONObject> res = new LinkedList<>();
                    for (int index = 0; index < jsonArray.size(); index++) {
                        JSONObject json = jsonArray.getJSONObject(index);
                        boolean hasSheetId = json.containsKey("sheetId");
                        if (!hasSheetId) {
                            json.put("sheetId", ESUtil.getUUID());
                        }
                        res.add(json);
                        Set<Long> ciClassIdSet = new HashSet<Long>();
                        JSONArray nodeDataArray = json.getJSONArray("nodeDataArray");
                        if (nodeDataArray != null) {
                            JSONArray newNodeDataArray = new JSONArray();
                            for (int i = 0; i < nodeDataArray.size(); i++) {
                                JSONObject nodeData = nodeDataArray.getJSONObject(i);
                                Long classId = nodeData.getLong("classId");
                                ESCIClassInfo cls = ciClassMap.get(classId);
                                if (cls == null) {
                                    nodeData.put("classId", null);
                                } else {
                                    nodeData.put("label", cls.getClassName());
                                    nodeData.put("image", rsmSlaveRoot + cls.getIcon());
                                    ciClassIdSet.add(cls.getId());
                                }
                                newNodeDataArray.add(nodeData);
                            }
                            json.put("nodeDataArray", newNodeDataArray);
                        }
                        Set<Long> rltClassIdSet = new HashSet<Long>();
                        JSONArray linkDataArray = json.getJSONArray("linkDataArray");
                        if (linkDataArray != null) {
                            JSONArray newLinkDataArray = new JSONArray();
                            for (int i = 0; i < linkDataArray.size(); i++) {
                                JSONObject linkData = linkDataArray.getJSONObject(i);
                                Long classId = linkData.getLong("classId");
                                ESCIClassInfo cls = rltClassMap.get(classId);
                                if (cls == null) {
                                    linkData.put("classId", null);
                                } else {
                                    linkData.put("label", cls.getClassName());
                                    rltClassIdSet.add(cls.getId());
                                }
                                newLinkDataArray.add(linkData);
                            }
                            json.put("linkDataArray", newLinkDataArray);
                        }
                        // end
                    }
                    model.setJson(JSON.toJSONString(res));
                    List<ESVisualModel.ImpactPath> ips = new ArrayList<ESVisualModel.ImpactPath>();
                    if (model.getImpactPaths() != null) {
                        for (ESVisualModel.ImpactPath impactPath : model.getImpactPaths()) {
                            // if
                            // (ciClassIdSet.contains(impactPath.getFocusClassId())
                            // &&
                            // ciClassIdSet.contains(impactPath.getSourceClassId())
                            // &&
                            // ciClassIdSet.contains(impactPath.getTargetClassId())
                            // &&
                            // rltClassIdSet.contains(impactPath.getRltClassId()))
                            // {
                            ips.add(impactPath);
                            // }
                        }
                    }
                    model.setImpactPaths(ips);
                } catch (Exception e) {
                }
            }
        }
        // 空数据处理
        if (CollectionUtils.isEmpty(models)) {
            return models;
        }
        // 元模型展示顺序 --- 当前使用元模型置顶  其他按编辑时间倒叙
        List<ESVisualModel> sortModels = new LinkedList<>();
        int removeId = 0;
        for (int i = 0; i < models.size(); i++) {
            if (models.get(i).getEnable()) {
                sortModels.add(models.get(i));
                removeId = i;
                break;
            }
        }
        models.remove(removeId);
        sortModels.addAll(models);
        return sortModels;
    }

    @Override
    public List<ESVisualModelVo> queryVisualModels(Long domainId, LibType libType, String loginCode) {
        domainId = domainId == null ? BaseConst.DEFAULT_DOMAIN_ID : domainId;
        Assert.notNull(libType,"必须指定查询库类型");
        if(libType.equals(LibType.PRIVATE)){
            Assert.notNull(loginCode,"查询私有元模型必须指定用户");
        }
        // 获取有效CI和关系分类
        Map<Long, ESCIClassInfo> ciClassMap = new HashMap<>();
        CCcCiClass ciClassQuery = new CCcCiClass();
        ciClassQuery.setDomainId(domainId);
        List<ESCIClassInfo> ciClassList = ciClassSvc.getListByCdt(ciClassQuery);
        if (ciClassList != null) {
            for (ESCIClassInfo classInfo : ciClassList) {
                ciClassMap.put(classInfo.getId(), classInfo);
            }
        }
        Map<Long, ESCIClassInfo> rltClassMap = new HashMap<>();
        CCcCiClass rltClassQuery = new CCcCiClass();
        rltClassQuery.setDomainId(domainId);
        List<ESCIClassInfo> rltClassList = rltClassSvc.getListByCdt(rltClassQuery);
        if (rltClassList != null) {
            for (ESCIClassInfo classInfo : rltClassList) {
                rltClassMap.put(classInfo.getId(), classInfo);
            }
        }
        // 获取模型
        List<ESVisualModel> models;
        try {
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
            if (libType.equals(LibType.PRIVATE)) {
                boolQueryBuilder.filter(QueryBuilders.termQuery("creator.keyword", loginCode));
            }
            boolQueryBuilder.filter(QueryBuilders.termQuery("domainId", domainId));
            if(LibType.PRIVATE.equals(libType)){
                boolQueryBuilder.mustNot((QueryBuilders.termQuery("status", 0)));
                models = visualModelPrivateSvc.getSortListByQueryScroll(boolQueryBuilder, "modifyTime",Boolean.FALSE);
            }else {
                boolQueryBuilder.filter(QueryBuilders.termQuery("enable", true));
                models = visualModelSvc.getSortListByQueryScroll(boolQueryBuilder, "modifyTime",Boolean.FALSE);
            }
        } catch (Exception e) {
            log.error("模型获取异常", e);
            throw new MessageException(e.getMessage());
        }
        List<ESVisualModelVo> esVisualModelVos = new ArrayList<>();
        // 校验CI和关系分类是否存在
        for (ESVisualModel model : models) {
            if (StringUtils.isNotBlank(model.getJson())) {
                try {
                    JSONArray jsonArray = JSONObject.parseArray(model.getJson());
                    List<JSONObject> res = new LinkedList<>();
                    for (int index = 0; index < jsonArray.size(); index++) {
                        JSONObject json = jsonArray.getJSONObject(index);
                        boolean hasSheetId = json.containsKey("sheetId");
                        if (!hasSheetId) {
                            json.put("sheetId", ESUtil.getUUID());
                        }
                        res.add(json);
                        Set<Long> ciClassIdSet = new HashSet<>();
                        JSONArray nodeDataArray = json.getJSONArray("nodeDataArray");
                        if (nodeDataArray != null) {
                            JSONArray newNodeDataArray = new JSONArray();
                            for (int i = 0; i < nodeDataArray.size(); i++) {
                                JSONObject nodeData = nodeDataArray.getJSONObject(i);
                                Long classId = nodeData.getLong("classId");
                                ESCIClassInfo cls = ciClassMap.get(classId);
                                if (cls == null) {
                                    nodeData.put("classId", null);
                                } else {
                                    nodeData.put("label", cls.getClassName());
                                    nodeData.put("image", rsmSlaveRoot + cls.getIcon());
                                    ciClassIdSet.add(cls.getId());
                                }
                                newNodeDataArray.add(nodeData);
                            }
                            json.put("nodeDataArray", newNodeDataArray);
                        }
                        Set<Long> rltClassIdSet = new HashSet<Long>();
                        JSONArray linkDataArray = json.getJSONArray("linkDataArray");
                        if (linkDataArray != null) {
                            JSONArray newLinkDataArray = new JSONArray();
                            for (int i = 0; i < linkDataArray.size(); i++) {
                                JSONObject linkData = linkDataArray.getJSONObject(i);
                                Long classId = linkData.getLong("classId");
                                ESCIClassInfo cls = rltClassMap.get(classId);
                                if (cls == null) {
                                    linkData.put("classId", null);
                                } else {
                                    linkData.put("label", cls.getClassName());
                                    rltClassIdSet.add(cls.getId());
                                }
                                newLinkDataArray.add(linkData);
                            }
                            json.put("linkDataArray", newLinkDataArray);
                        }
                    }
                    model.setJson(JSON.toJSONString(res));
                } catch (Exception e) {
                    log.error("解析visualmode异常", e);
                }
            }
            ESVisualModelVo esVisualModelVo = new ESVisualModelVo();
            BeanUtils.copyProperties(model,esVisualModelVo);
            esVisualModelVo.setLibType(libType);
            esVisualModelVos.add(esVisualModelVo);
        }
        return esVisualModelVos;
    }

    @Override
    public List<ESVisualModelVo> queryVisualModelsNoChickExit(Long domainId, LibType libType, String loginCode) {
        domainId = domainId == null ? BaseConst.DEFAULT_DOMAIN_ID : domainId;
        Assert.notNull(libType,"必须指定查询库类型");
        if(libType.equals(LibType.PRIVATE)){
            Assert.notNull(loginCode,"查询私有元模型必须指定用户");
        }
        // 获取有效CI和关系分类
        Map<Long, ESCIClassInfo> ciClassMap = new HashMap<>();
        CCcCiClass ciClassQuery = new CCcCiClass();
        ciClassQuery.setDomainId(domainId);
        List<ESCIClassInfo> ciClassList = ciClassSvc.getListByCdt(ciClassQuery);
        if (ciClassList != null) {
            for (ESCIClassInfo classInfo : ciClassList) {
                ciClassMap.put(classInfo.getId(), classInfo);
            }
        }
        Map<Long, ESCIClassInfo> rltClassMap = new HashMap<>();
        CCcCiClass rltClassQuery = new CCcCiClass();
        rltClassQuery.setDomainId(domainId);
        List<ESCIClassInfo> rltClassList = rltClassSvc.getListByCdt(rltClassQuery);
        if (rltClassList != null) {
            for (ESCIClassInfo classInfo : rltClassList) {
                rltClassMap.put(classInfo.getId(), classInfo);
            }
        }
        // 获取模型
        List<ESVisualModel> models;
        try {
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
            if (libType.equals(LibType.PRIVATE)) {
                boolQueryBuilder.filter(QueryBuilders.termQuery("creator.keyword", loginCode));
            }
            boolQueryBuilder.filter(QueryBuilders.termQuery("domainId", domainId));
            if(LibType.PRIVATE.equals(libType)){
                boolQueryBuilder.mustNot((QueryBuilders.termQuery("status", 0)));
                models = visualModelPrivateSvc.getSortListByQueryScroll(boolQueryBuilder, "modifyTime",Boolean.FALSE);
            }else {
                models = visualModelSvc.getSortListByQueryScroll(boolQueryBuilder, "modifyTime",Boolean.FALSE);
            }
        } catch (Exception e) {
            log.error("模型获取异常", e);
            throw new MessageException(e.getMessage());
        }
        List<ESVisualModelVo> esVisualModelVos = new ArrayList<>();
        // 校验CI和关系分类是否存在
        for (ESVisualModel model : models) {
            ESVisualModelVo esVisualModelVo = new ESVisualModelVo();
            BeanUtils.copyProperties(model,esVisualModelVo);
            esVisualModelVo.setLibType(libType);
            esVisualModelVo.setJson("");
            esVisualModelVos.add(esVisualModelVo);
        }
        return esVisualModelVos;
    }


    @Override
    public Long saveVisualModel(ESVisualModel model) {
        // 校验
        if (model.getDomainId() == null) {
            throw MessageException.i18n("BS_VISUALMODEL_LACK_DOMAINID");
        }
        if (model.getName() == null || model.getName().trim().isEmpty()) {
            throw MessageException.i18n("BS_VISUALMODEL_LACK_NAME");
        }
        BoolQueryBuilder queryBuilders = QueryBuilders.boolQuery();
        queryBuilders.must(QueryBuilders.termQuery("name.keyword", model.getName()));
        String loginCode;
        try {
            loginCode = SysUtil.getCurrentUserInfo().getLoginCode();
        }catch (Exception e){
            loginCode = "system";
        }
        queryBuilders.must(QueryBuilders.termQuery("creator.keyword", loginCode));
        if (!BinaryUtils.isEmpty(model.getId())) {
            queryBuilders.must(QueryBuilders.termQuery("id", model.getId()));
        }
        // id为空 新增校验重名
        if (BinaryUtils.isEmpty(model.getId())) {
            // 后台校验同一用户下元模型名称唯一
            boolean exist = visualModelSvc.existByCondition(queryBuilders);
            if (exist) {
                throw new BinaryException("当前用户账号下存在同名元模型");
            }
        } else {
            // 更新操作
            List<ESVisualModel> listByQuery = visualModelSvc.getListByQuery(queryBuilders);
            for (ESVisualModel esVisualModel : listByQuery) {
                if (esVisualModel.getId().longValue() != model.getId().longValue()) {
                    throw new BinaryException("当前用户账号下存在同名元模型");
                }
            }
        }

        // 生成ciClassRlt列表
        List<ESCiClassRlt> ciClassRltList = new ArrayList<ESCiClassRlt>();
        // 生成impactPath列表
        List<ESImpactPath> impactPathList = new ArrayList<ESImpactPath>();
        if (model.getJson() == null) {
            throw MessageException.i18n("BS_VISUALMODEL_LACK_JSON");
        } else {
            try {
                JSONArray jsonArray = JSONObject.parseArray(model.getJson());
                JSONArray newJsonArray = new JSONArray(jsonArray.size());
                for (int index = 0; index < jsonArray.size(); index++) {
                    JSONObject json = jsonArray.getJSONObject(index);
                    boolean hasSheetId = json.containsKey("sheetId");
                    if (!hasSheetId) {
                        json.put("sheetId", ESUtil.getUUID());
                    }
                    newJsonArray.add(index, json);
                    Map<Long, Long> nodeClassMap = new HashMap<Long, Long>();
                    JSONArray nodeDataArray = json.getJSONArray("nodeDataArray");
                    if (nodeDataArray != null) {
                        for (int i = 0; i < nodeDataArray.size(); i++) {
                            JSONObject nodeData = nodeDataArray.getJSONObject(i);
                            Long key = nodeData.getLong("key");
                            Long classId = nodeData.getLong("classId");
                            if (key != null && classId != null) {
                                nodeClassMap.put(key, classId);
                            }
                        }
                    }
                    JSONArray linkDataArray = json.getJSONArray("linkDataArray");
                    if (linkDataArray != null) {
                        for (int i = 0; i < linkDataArray.size(); i++) {
                            JSONObject linkData = linkDataArray.getJSONObject(i);
                            Long from = linkData.getLong("from");
                            Long to = linkData.getLong("to");
                            Long classId = linkData.getLong("classId");
                            if (from != null && to != null && classId != null) {
                                Long fromClassId = nodeClassMap.get(from);
                                Long toClassId = nodeClassMap.get(to);
                                if (fromClassId != null && toClassId != null && classId != null) {
                                    ESCiClassRlt ccr = new ESCiClassRlt();
                                    ccr.setClassId(classId);
                                    ccr.setSourceClassId(fromClassId);
                                    ccr.setTargetClassId(toClassId);
                                    ccr.setDomainId(model.getDomainId());
                                    ciClassRltList.add(ccr);
                                }
                            } else {
                                log.error("linkDataArray[{}]from[{}]to[{}]class[{}]三参有异常值,略过该处理", i, from, to, classId);
                            }
                        }
                    }
                    if (model.getImpactPaths() != null) {
                        for (ESVisualModel.ImpactPath ip : model.getImpactPaths()) {
                            ESImpactPath impactPath = new ESImpactPath();
                            if (ip.getFocusClassId() == null) {
                                throw MessageException.i18n("BS_IMPACTPATH_WRONG");
                            } else {
                                impactPath.setFocusClassId(ip.getFocusClassId());
                            }
                            if (ip.getSourceClassId() == null) {
                                throw MessageException.i18n("BS_IMPACTPATH_WRONG");
                            } else {
                                impactPath.setSourceClassId(ip.getSourceClassId());
                            }
                            if (ip.getTargetClassId() == null) {
                                throw MessageException.i18n("BS_IMPACTPATH_WRONG");
                            } else {
                                impactPath.setTargetClassId(ip.getTargetClassId());
                            }
                            if (ip.getRltClassId() == null) {
                                throw MessageException.i18n("BS_IMPACTPATH_WRONG");
                            } else {
                                impactPath.setRltClassId(ip.getRltClassId());
                            }
                            impactPath.setDomainId(model.getDomainId());
                            impactPathList.add(impactPath);
                        }
                    }
                }
                model.setThumbnail(model.getThumbnail());
                model.setJson(newJsonArray.toJSONString());
            } catch (Exception e) {
                log.error("模型获取异常",e);
                throw MessageException.i18n("BS_VISUALMODEL_WRONG_JSON");
            }
        }

        // 当前元模型enable    true / false 取决与索引中的数据
        if (BinaryUtils.isEmpty(model.getEnable())) {
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
            boolQueryBuilder.filter(QueryBuilders.termQuery("domainId", SysUtil.getCurrentUserInfo().getDomainId()));
            List<ESVisualModel> listByQuery = visualModelSvc.getListByQuery(boolQueryBuilder);
            if (CollectionUtils.isEmpty(listByQuery)) {
                model.setEnable(true);
            } else {
                model.setEnable(false);
            }
        }
        // 添加当前选中元模型标识字段
        model.setSelect(true);
        visualModelSvc.updateByQuery(QueryBuilders.termQuery("domainId", model.getDomainId()),
                "ctx._source.select=false", true);
        Long id = visualModelSvc.saveOrUpdate(model);
        if (model.getEnable()) {
            ciClassRltSvc.deleteByQuery(QueryBuilders.termQuery("domainId", model.getDomainId()), true);
            ciClassRltSvc.saveOrUpdateBatch(this.remDupClsRlt(ciClassRltList));
            impactPathSvc.deleteByQuery(QueryBuilders.termQuery("domainId", model.getDomainId()), true);
            impactPathSvc.saveOrUpdateBatch(this.remDupImpactPath(impactPathList));
        }
        return id;
    }

    @Override
    public Long saveVisualModelPrivate(ESVisualModel model) {
        // 校验
        if (model.getDomainId() == null) {
            throw MessageException.i18n("BS_VISUALMODEL_LACK_DOMAINID");
        }
        if (model.getName() == null || model.getName().trim().isEmpty()) {
            throw MessageException.i18n("BS_VISUALMODEL_LACK_NAME");
        }
        BoolQueryBuilder designQueryBuilders = QueryBuilders.boolQuery();
        designQueryBuilders.must(QueryBuilders.termQuery("name.keyword", model.getName()));
        designQueryBuilders.filter(QueryBuilders.termQuery("enable",true));
        // id为空 新增校验重名
        if (BinaryUtils.isEmpty(model.getId())) {
            // 后台校验同一用户下元模型名称唯一
            boolean exist = visualModelSvc.existByCondition(designQueryBuilders);
            if (exist) {
                throw new BinaryException("发布库存在同名元模型");
            }
            BoolQueryBuilder privateBuilders = QueryBuilders.boolQuery();
            privateBuilders.must(QueryBuilders.termQuery("name.keyword", model.getName()));
            privateBuilders.must(QueryBuilders.termQuery("creator.keyword", SysUtil.getCurrentUserInfo().getLoginCode()));
            privateBuilders.must(QueryBuilders.termQuery("status", 1));
            exist = visualModelPrivateSvc.existByCondition(privateBuilders);
            if (exist) {
                throw new BinaryException("当前用户本地库下存在同名元模型");
            }
            //新建的时候将未来发的的id先进行填充
            model.setLinkPublishedId(ESUtil.getUUID());
            model.setPublishVersion(0L);
            model.setHistory(Boolean.FALSE);
            return visualModelPrivateSvc.saveOrUpdate(model);
        }else {
            //修改需要同样校验，查询出来名称校验id是否一致,与新建不同的是需要对比下id是否一致
            ESVisualModel designVisualModel = visualModelSvc.selectOne(designQueryBuilders);
            ESVisualModel dbVisualModel = visualModelPrivateSvc.getById(model.getId());
            if (designVisualModel != null && dbVisualModel != null) {
                Long id = designVisualModel.getId();
                Long linkPublishedId = dbVisualModel.getLinkPublishedId();
                if (!Objects.equals(id, linkPublishedId)) {
                    throw new BinaryException("当前用户账号下存在同名元模型");
                }
            }
            //判断下本是否有其他重名元模型
            BoolQueryBuilder privateBuilders = QueryBuilders.boolQuery();
            privateBuilders.must(QueryBuilders.termQuery("name.keyword", model.getName()));
            privateBuilders.must(QueryBuilders.termQuery("status", 1));
            privateBuilders.must(QueryBuilders.termQuery("creator.keyword", SysUtil.getCurrentUserInfo().getLoginCode()));
            ESVisualModel privateVisualModel = visualModelPrivateSvc.selectOne(privateBuilders);
            if (privateVisualModel != null && !model.getId().equals(privateVisualModel.getId())) {
                throw new BinaryException("当前用户账号下存在同名元模型");
            }
            dbVisualModel.setThumbnail(model.getThumbnail());
            dbVisualModel.setName(model.getName());
            dbVisualModel.setJson(model.getJson());
            dbVisualModel.setHistory(Boolean.FALSE);
            dbVisualModel.setPagesetting(model.getPagesetting());
            dbVisualModel.setReferModelIds(model.getReferModelIds());
            dbVisualModel.setEditRoleIds(model.getEditRoleIds());
            dbVisualModel.setViewRoleIds(model.getViewRoleIds());
            dbVisualModel.setOrgId(model.getOrgId());
            dbVisualModel.setSelectedShapeGroupIds(model.getSelectedShapeGroupIds());
            dbVisualModel.setThumbnail(model.getThumbnail());
            return visualModelPrivateSvc.saveOrUpdate(dbVisualModel);
        }
    }

    @Override
    public Boolean flushAllEnableVisualModelCiRlt() {

        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.termQuery("domainId", SysUtil.getCurrentUserInfo().getDomainId()));
        boolQueryBuilder.filter(QueryBuilders.termQuery("enable", Boolean.TRUE));
        List<ESVisualModel> allEnableVisualModel = visualModelSvc.getListByQuery(boolQueryBuilder);
        // 生成ciClassRlt列表
        List<ESCiClassRlt> ciClassRltList = new ArrayList<>();
        for (ESVisualModel model : allEnableVisualModel) {
            if (StringUtils.isNotBlank(model.getJson())) {
                try {
                    JSONArray jsonArray = JSONObject.parseArray(model.getJson());
                    for (int index = 0; index < jsonArray.size(); index++) {
                        JSONObject json = jsonArray.getJSONObject(index);
                        Map<Long, Long> nodeClassMap = new HashMap<>();
                        JSONArray nodeDataArray = json.getJSONArray("nodeDataArray");
                        if (nodeDataArray != null) {
                            for (int i = 0; i < nodeDataArray.size(); i++) {
                                JSONObject nodeData = nodeDataArray.getJSONObject(i);
                                Long key = nodeData.getLong("key");
                                Long classId = nodeData.getLong("classId");
                                if (key != null && classId != null) {
                                    nodeClassMap.put(key, classId);
                                }
                            }
                        }
                        JSONArray linkDataArray = json.getJSONArray("linkDataArray");
                        if (linkDataArray != null) {
                            for (int i = 0; i < linkDataArray.size(); i++) {
                                JSONObject linkData = linkDataArray.getJSONObject(i);
                                Long from = linkData.getLong("from");
                                Long to = linkData.getLong("to");
                                Long classId = linkData.getLong("classId");
                                if (from != null && to != null && classId != null) {
                                    Long fromClassId = nodeClassMap.get(from);
                                    Long toClassId = nodeClassMap.get(to);
                                    if (fromClassId != null && toClassId != null && classId != null) {
                                        ESCiClassRlt ccr = new ESCiClassRlt();
                                        ccr.setClassId(classId);
                                        ccr.setSourceClassId(fromClassId);
                                        ccr.setTargetClassId(toClassId);
                                        ccr.setDomainId(model.getDomainId());
                                        ciClassRltList.add(ccr);
                                    }
                                } else {
                                    log.error("linkDataArray[{}]from[{}]to[{}]class[{}]三参有异常值,略过该处理", i, from, to, classId);
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("模型获取异常",e);
                    throw MessageException.i18n("BS_VISUALMODEL_WRONG_JSON");
                }
            }
        }
        ciClassRltSvc.deleteByQuery(QueryBuilders.termQuery("domainId", 1L), true);
        ciClassRltSvc.saveOrUpdateBatch(this.remDupClsRlt(ciClassRltList));
        return Boolean.TRUE;
    }

    @Override
    public List<ESVisualModel> getVisualModelByQuery(QueryBuilder query, LibType libType) {
        List<ESVisualModel> listByQuery;
        if (LibType.PRIVATE.equals(libType)) {
            listByQuery = visualModelPrivateSvc.getSortListByQueryScroll(query, "modifyTime",Boolean.FALSE);
        } else {
            listByQuery = visualModelSvc.getSortListByQueryScroll(query, "modifyTime",Boolean.FALSE);
        }
        return listByQuery;
    }

    /**
     * 谁影响我去重
     *
     * @param impactPaths
     * @return
     */
    public List<ESImpactPath> remDupImpactPath(List<ESImpactPath> impactPaths) {
        List<ESImpactPath> res = new LinkedList<>();
        impactPaths.forEach(path -> {
            if (!res.contains(path)) {
                res.add(path);
            }
        });
        return res;
    }

    /**
     * 分类关系去重
     *
     * @param clsRlts
     * @return
     */
    private List<ESCiClassRlt> remDupClsRlt(List<ESCiClassRlt> clsRlts) {
        List<ESCiClassRlt> res = new LinkedList<>();
        clsRlts.forEach(clsRlt -> {
            if (!res.contains(clsRlt)) {
                res.add(clsRlt);
            }
        });
        return res;
    }

    @Override
    public void updateVisualModelName(ESVisualModel model) {
        // 校验
        if (model.getId() == null) {
            throw MessageException.i18n("BS_VISUALMODEL_LACK_ID");
        }
        if (model.getName() == null
                || "".equals(model.getName().trim())) {
            throw MessageException.i18n("BS_VISUALMODEL_LACK_NAME");
        }
        ESVisualModel exist = visualModelSvc.getById(model.getId());
        if (exist == null) {
            // 不存在模型
            throw MessageException.i18n("BS_VISUALMODEL_NO_EXIST");
        }
        visualModelSvc.updateByQuery(QueryBuilders.termQuery("id", model.getId()),
                "ctx._source.name='" + model.getName().trim().replace("'", "\\'") + "'", true);
    }

    @Override
    public void deleteVisualModel(Long id) {
        // 校验
        if (id == null) {
            throw MessageException.i18n("BS_VISUALMODEL_LACK_ID");
        }
        ESVisualModel model = visualModelSvc.getById(id);
        if (model == null) {
            // 不存在模型
            throw MessageException.i18n("BS_VISUALMODEL_NO_EXIST");
        } else {
            // 存在模型
            Page<ESVisualModel> page = visualModelSvc.getListByQuery(1, 1,
                    QueryBuilders.termQuery("domainId", model.getDomainId()));
            if (page != null && page.getTotalRows() > 1) {
                // 多于一个模型
                if (model.getEnable()) {
                    throw MessageException.i18n("BS_VISUALMODEL_CANT_BE_DELETE");
                } else {
                    visualModelSvc.deleteById(id);
                }
            } else { // 一个模型
                visualModelSvc.deleteById(id);
                ciClassRltSvc.deleteByQuery(QueryBuilders.termQuery("domainId", model.getDomainId()), true);
                impactPathSvc.deleteByQuery(QueryBuilders.termQuery("domainId", model.getDomainId()), true);
            }
        }
    }

    @Override
    public void delVMThumbnailBySheetId(Long id, Long sheetId) {
        ESVisualModel model = visualModelSvc.getById(id);
        if (model == null) {
            // 不存在模型
            throw MessageException.i18n("BS_VISUALMODEL_NO_EXIST");
        } else {
            // 解析 json 和 thumbnail 字段 删除json中对应的sheetId 遍历新的sheetId集合 删除thumbnail中不存在的缩略图
            List<Long> curSheetIds = new ArrayList<>();
            List<Map> newJsonArray = new ArrayList<>();
            List<Map> jsonArray = JSON.parseArray(model.getJson(), Map.class);
            // 删除sheetId
            for (Map sheetJson:jsonArray) {
                if (!sheetJson.get("sheetId").toString().equals(sheetId.toString())) {
                    newJsonArray.add(sheetJson);
                    curSheetIds.add(Long.valueOf(sheetJson.get("sheetId").toString()));
                }
            }
            // 根据最新的curSheet信息 重置缩略图信息
            Map<Long, Object> newThumbnailMap = new HashMap<>();
            Map<Long, Object> thumbnailMap = JSON.parseObject(model.getThumbnail(), Map.class);
            for (Long picSheetId : thumbnailMap.keySet()) {
                if (curSheetIds.contains(picSheetId)) {
                    // 如果缩略图sheetId不包含在curSheet集合内 删除掉
                    newThumbnailMap.put(picSheetId, thumbnailMap.get(picSheetId));
                }
            }

            model.setJson(JSON.toJSONString(newJsonArray));
            model.setThumbnail(JSON.toJSONString(newThumbnailMap));
            visualModelSvc.saveOrUpdate(model);
        }
    }

    @Override
    public ESVisualModel queryVisualModelById(Long domainId, Long visId) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.termQuery("domainId", domainId));
        boolQueryBuilder.filter(QueryBuilders.termQuery("id", visId));
        return visualModelSvc.selectOne(boolQueryBuilder);
    }

    @Override
    public ESVisualModel queryPrivateVisualModelById(Long domainId, Long visId) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.termQuery("domainId", domainId));
        boolQueryBuilder.filter(QueryBuilders.termQuery("id", visId));
        return visualModelPrivateSvc.selectOne(boolQueryBuilder);
    }

    @Override
    public VisualModelRltCiClassIds queryEnableVisualModelRltSourceAndTarget(Long domainId, Long rltId) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.termQuery("domainId", domainId));
        boolQueryBuilder.filter(QueryBuilders.termQuery("enable", true));
        List<ESVisualModel> listByQuery = visualModelSvc.getListByQuery(boolQueryBuilder);

        if (CollectionUtils.isEmpty(listByQuery)) {
            throw new BinaryException("当前未找到生效元模型");
        }
        HashSet<Long> sourceClassIds = new HashSet<>();
        HashSet<Long> targetClassIds = new HashSet<>();
        for (ESVisualModel esVisualModel : listByQuery) {
            String modelJson = esVisualModel.getJson();
            JSONArray sheets = JSON.parseArray(modelJson);

            for (int i = 0; i < sheets.size(); i++) {
                Map<Integer,Long> nodeDataMap = new HashMap<>();
                JSONObject jsonObject = sheets.getJSONObject(i);
                JSONArray nodeDataArray = jsonObject.getJSONArray("nodeDataArray");
                JSONArray linkDataArray = jsonObject.getJSONArray("linkDataArray");
                for (int j = 0; j < nodeDataArray.size(); j++) {
                    JSONObject nodeDataJSONObject = nodeDataArray.getJSONObject(j);
                    nodeDataMap.put(nodeDataJSONObject.getInteger("key"),nodeDataJSONObject.getLong("classId"));
                }
                for (int j = 0; j < linkDataArray.size(); j++){
                    JSONObject linkDataJSONObject = linkDataArray.getJSONObject(j);
                    Long classId = linkDataJSONObject.getLong("classId");
                    if (classId == null) {
                        continue;
                    }
                    if (classId.equals(rltId)) {
                        Integer form = linkDataJSONObject.getInteger("from");
                        Integer to = linkDataJSONObject.getInteger("to");
                        //兼容元模型图片情况
                        if (nodeDataMap.get(form) != null) {
                            sourceClassIds.add(nodeDataMap.get(form));
                        }
                        if (nodeDataMap.get(to) != null) {
                            targetClassIds.add(nodeDataMap.get(to));
                        }
                    }
                }
            }
        }

        VisualModelRltCiClassIds visualModelRltCiClassIds = new VisualModelRltCiClassIds();
        visualModelRltCiClassIds.setSourceClassIds(sourceClassIds);
        visualModelRltCiClassIds.setTargetClassIds(targetClassIds);
        return visualModelRltCiClassIds;
    }

    @Override
    public void delValidVisData() {
        // 获取当前登录用户信息
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        List<ESVisualModel> esVisualModels = this.queryVisualModels(currentUserInfo.getDomainId());     // 获取当前原模型数据
        if (CollectionUtils.isEmpty(esVisualModels)) {
            // 数据为空 结束操作
            return;
        }

        //Set<String> allKeys = iCacheService.getAllKeys();
        //Long cacheByType = iCacheService.getCacheByType(VISUAL_MODEL_KEY + esVisualModels.get(0).getId(), Long.class);
        Object userId = iCacheService.getCache(VISUAL_MODEL_KEY + esVisualModels.get(0).getId());       // 查询当前元模型操作用户
        if (BinaryUtils.isEmpty(userId)) {
            // redis中无信息 结束
            return;
        }
        if (Objects.equals(currentUserInfo.getId(), Long.valueOf(userId.toString()))) {
            // 当前登录用户正在编辑 强制退出 清空redis元模型锁
            for (ESVisualModel visualModel : esVisualModels) {
                iCacheService.delKey(VISUAL_MODEL_KEY + visualModel.getId());
            }
            log.info("当前用户元模型编辑缓存以清空");
        }
        log.info("缓存清除");
        return;
    }
}
