package com.uinnova.product.eam.model.enums;

/**
 * 应用广场卡片资产类型
 * <AUTHOR>
 */
public enum AssetConfigType {
    /**
     * 战略与转型
     */
    STRATEGY(1),
    /**
     * 业务架构
     */
    BUSINESS(2),
    /**
     * 应用架构
     */
    APP(3),
    /**
     * 数据架构
     */
    DATA(4),

    /**
     * 技术架构
     */
    TECHNOLOGY(5),
    /**
     * 其它
     */
    OTHER(6);

    final int val;

    AssetConfigType(int val) {
        this.val = val;
    }

    public int val() {
        return this.val;
    }
}
