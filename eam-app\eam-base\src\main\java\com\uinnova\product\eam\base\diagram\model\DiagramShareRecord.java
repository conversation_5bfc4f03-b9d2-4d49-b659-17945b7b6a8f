package com.uinnova.product.eam.base.diagram.model;

import com.binary.framework.bean.annotation.Comment;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.util.List;

@Data
public class DiagramShareRecord {

    private Long id;

    @JsonIgnore
    private Long diagramId;

    private Long ownerId;

    private Long sharedUserId;

    @Comment("视图使用形状json")
    private List<ESDiagramShape> leftPanelModel;

    private Integer permission;

    @Comment("创建时间[CREATE_TIME]    创建时间:yyyyMMddHHmmss")
    private Long createTime;

    @Comment("更新时间[MODIFY_TIME]    更新时间:yyyyMMddHHmmss")
    private Long modifyTime;

    @Comment("文件类型 1:企业级 2:系统级")
    private Integer dirType;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getDiagramId() {
        return diagramId;
    }

    public void setDiagramId(Long diagramId) {
        this.diagramId = diagramId;
    }

    public Long getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    public Long getSharedUserId() {
        return sharedUserId;
    }

    public void setSharedUserId(Long sharedUserId) {
        this.sharedUserId = sharedUserId;
    }

    public Integer getPermission() {
        return permission;
    }

    public void setPermission(Integer permission) {
        this.permission = permission;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Integer getDirType() {
        return dirType;
    }

    public void setDirType(Integer dirType) {
        this.dirType = dirType;
    }
}
