package com.uinnova.product.vmdb.comm.model.dir;

import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;

/**
 * 
 * <AUTHOR>
 *
 */
@Comment("通用目录表[CC_GENERAL_DIR]")
public class CCcGeneralDir implements Condition {
    private static final long serialVersionUID = 1L;

    @Comment("ID[ID] operate-Equal[=]")
    private Long id;

    @Comment("ID[ID] operate-In[in]")
    private Long[] ids;

    @Comment("ID[ID] operate-GTEqual[>=]")
    private Long startId;

    @Comment("ID[ID] operate-LTEqual[<=]")
    private Long endId;

    @Comment("目录名称[DIR_NAME] operate-Like[like]")
    private String dirName;

    @Comment("目录名称[DIR_NAME] operate-Equal[=]")
    private String dirNameEqual;

    @Comment("目录名称[DIR_NAME] operate-In[in]")
    private String[] dirNames;

    @Comment("目录类型[DIR_TYPE] operate-Equal[=]    1=图标")
    private Integer dirType;

    @Comment("目录类型[DIR_TYPE] operate-In[in]    1=图标")
    private Integer[] dirTypes;

    @Comment("目录类型[DIR_TYPE] operate-GTEqual[>=]    1=图标")
    private Integer startDirType;

    @Comment("目录类型[DIR_TYPE] operate-LTEqual[<=]    1=图标")
    private Integer endDirType;

    @Comment("上级目录ID[PARENT_ID] operate-Equal[=]")
    private Long parentId;

    @Comment("上级目录ID[PARENT_ID] operate-In[in]")
    private Long[] parentIds;

    @Comment("上级目录ID[PARENT_ID] operate-GTEqual[>=]")
    private Long startParentId;

    @Comment("上级目录ID[PARENT_ID] operate-LTEqual[<=]")
    private Long endParentId;

    @Comment("分类层级级别[DIR_LVL] operate-Equal[=]")
    private Integer dirLvl;

    @Comment("分类层级级别[DIR_LVL] operate-In[in]")
    private Integer[] dirLvls;

    @Comment("分类层级级别[DIR_LVL] operate-GTEqual[>=]")
    private Integer startDirLvl;

    @Comment("分类层级级别[DIR_LVL] operate-LTEqual[<=]")
    private Integer endDirLvl;

    @Comment("分类层级路径[DIR_PATH] operate-Like[like]    例：#1#2#7#")
    private String dirPath;

    @Comment("显示排序[ORDER_NO] operate-Equal[=]")
    private Integer orderNo;

    @Comment("显示排序[ORDER_NO] operate-In[in]")
    private Integer[] orderNos;

    @Comment("显示排序[ORDER_NO] operate-GTEqual[>=]")
    private Integer startOrderNo;

    @Comment("显示排序[ORDER_NO] operate-LTEqual[<=]")
    private Integer endOrderNo;

    @Comment("是否末级[IS_LEAF] operate-Equal[=]    1=是 0=否")
    private Integer isLeaf;

    @Comment("是否末级[IS_LEAF] operate-In[in]    1=是 0=否")
    private Integer[] isLeafs;

    @Comment("是否末级[IS_LEAF] operate-GTEqual[>=]    1=是 0=否")
    private Integer startIsLeaf;

    @Comment("是否末级[IS_LEAF] operate-LTEqual[<=]    1=是 0=否")
    private Integer endIsLeaf;

    @Comment("目录图标[ICON] operate-Like[like]")
    private String icon;

    @Comment("目录描述[DIR_DESC] operate-Like[like]")
    private String dirDesc;

    @Comment("目录颜色[DIR_COLOR] operate-Like[like]")
    private String dirColor;

    @Comment("目录颜色[DIR_COLOR] operate-Equal[=]")
    private String dirColorEqual;

    @Comment("目录颜色[DIR_COLOR] operate-In[in]")
    private String[] dirColors;

    @Comment("备用_1[CUSTOM_1] operate-Like[like]")
    private String custom1;

    @Comment("备用_2[CUSTOM_2] operate-Like[like]")
    private String custom2;

    @Comment("备用_3[CUSTOM_3] operate-Like[like]")
    private String custom3;

    @Comment("备用_4[CUSTOM_4] operate-Like[like]")
    private String custom4;

    @Comment("备用_5[CUSTOM_5] operate-Like[like]")
    private String custom5;

    @Comment("备用_6[CUSTOM_6] operate-Like[like]")
    private String custom6;

    @Comment("所属域[DOMAIN_ID] operate-Equal[=]")
    private Long domainId;

    @Comment("所属域[DOMAIN_ID] operate-In[in]")
    private Long[] domainIds;

    @Comment("所属域[DOMAIN_ID] operate-GTEqual[>=]")
    private Long startDomainId;

    @Comment("所属域[DOMAIN_ID] operate-LTEqual[<=]")
    private Long endDomainId;

    @Comment("数据状态[DATA_STATUS] operate-Equal[=]    0=删除，1=正常")
    private Integer dataStatus;

    @Comment("数据状态[DATA_STATUS] operate-In[in]    0=删除，1=正常")
    private Integer[] dataStatuss;

    @Comment("数据状态[DATA_STATUS] operate-GTEqual[>=]    0=删除，1=正常")
    private Integer startDataStatus;

    @Comment("数据状态[DATA_STATUS] operate-LTEqual[<=]    0=删除，1=正常")
    private Integer endDataStatus;

    @Comment("创建人[CREATOR] operate-Like[like]")
    private String creator;

    @Comment("创建人[CREATOR] operate-Equal[=]")
    private String creatorEqual;

    @Comment("创建人[CREATOR] operate-In[in]")
    private String[] creators;

    @Comment("修改人[MODIFIER] operate-Like[like]")
    private String modifier;

    @Comment("修改人[MODIFIER] operate-Equal[=]")
    private String modifierEqual;

    @Comment("修改人[MODIFIER] operate-In[in]")
    private String[] modifiers;

    @Comment("创建时间[CREATE_TIME] operate-Equal[=]    yyyyMMddHHmmss")
    private Long createTime;

    @Comment("创建时间[CREATE_TIME] operate-In[in]    yyyyMMddHHmmss")
    private Long[] createTimes;

    @Comment("创建时间[CREATE_TIME] operate-GTEqual[>=]    yyyyMMddHHmmss")
    private Long startCreateTime;

    @Comment("创建时间[CREATE_TIME] operate-LTEqual[<=]    yyyyMMddHHmmss")
    private Long endCreateTime;

    @Comment("更新时间[MODIFY_TIME] operate-Equal[=]    yyyyMMddHHmmss")
    private Long modifyTime;

    @Comment("更新时间[MODIFY_TIME] operate-In[in]    yyyyMMddHHmmss")
    private Long[] modifyTimes;

    @Comment("更新时间[MODIFY_TIME] operate-GTEqual[>=]    yyyyMMddHHmmss")
    private Long startModifyTime;

    @Comment("更新时间[MODIFY_TIME] operate-LTEqual[<=]    yyyyMMddHHmmss")
    private Long endModifyTime;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long[] getIds() {
        return this.ids;
    }

    public void setIds(Long[] ids) {
        this.ids = ids;
    }

    public Long getStartId() {
        return this.startId;
    }

    public void setStartId(Long startId) {
        this.startId = startId;
    }

    public Long getEndId() {
        return this.endId;
    }

    public void setEndId(Long endId) {
        this.endId = endId;
    }

    public String getDirName() {
        return this.dirName;
    }

    public void setDirName(String dirName) {
        this.dirName = dirName;
    }

    public String getDirNameEqual() {
        return this.dirNameEqual;
    }

    public void setDirNameEqual(String dirNameEqual) {
        this.dirNameEqual = dirNameEqual;
    }

    public String[] getDirNames() {
        return this.dirNames;
    }

    public void setDirNames(String[] dirNames) {
        this.dirNames = dirNames;
    }

    public Integer getDirType() {
        return this.dirType;
    }

    public void setDirType(Integer dirType) {
        this.dirType = dirType;
    }

    public Integer[] getDirTypes() {
        return this.dirTypes;
    }

    public void setDirTypes(Integer[] dirTypes) {
        this.dirTypes = dirTypes;
    }

    public Integer getStartDirType() {
        return this.startDirType;
    }

    public void setStartDirType(Integer startDirType) {
        this.startDirType = startDirType;
    }

    public Integer getEndDirType() {
        return this.endDirType;
    }

    public void setEndDirType(Integer endDirType) {
        this.endDirType = endDirType;
    }

    public Long getParentId() {
        return this.parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public Long[] getParentIds() {
        return this.parentIds;
    }

    public void setParentIds(Long[] parentIds) {
        this.parentIds = parentIds;
    }

    public Long getStartParentId() {
        return this.startParentId;
    }

    public void setStartParentId(Long startParentId) {
        this.startParentId = startParentId;
    }

    public Long getEndParentId() {
        return this.endParentId;
    }

    public void setEndParentId(Long endParentId) {
        this.endParentId = endParentId;
    }

    public Integer getDirLvl() {
        return this.dirLvl;
    }

    public void setDirLvl(Integer dirLvl) {
        this.dirLvl = dirLvl;
    }

    public Integer[] getDirLvls() {
        return this.dirLvls;
    }

    public void setDirLvls(Integer[] dirLvls) {
        this.dirLvls = dirLvls;
    }

    public Integer getStartDirLvl() {
        return this.startDirLvl;
    }

    public void setStartDirLvl(Integer startDirLvl) {
        this.startDirLvl = startDirLvl;
    }

    public Integer getEndDirLvl() {
        return this.endDirLvl;
    }

    public void setEndDirLvl(Integer endDirLvl) {
        this.endDirLvl = endDirLvl;
    }

    public String getDirPath() {
        return this.dirPath;
    }

    public void setDirPath(String dirPath) {
        this.dirPath = dirPath;
    }

    public Integer getOrderNo() {
        return this.orderNo;
    }

    public void setOrderNo(Integer orderNo) {
        this.orderNo = orderNo;
    }

    public Integer[] getOrderNos() {
        return this.orderNos;
    }

    public void setOrderNos(Integer[] orderNos) {
        this.orderNos = orderNos;
    }

    public Integer getStartOrderNo() {
        return this.startOrderNo;
    }

    public void setStartOrderNo(Integer startOrderNo) {
        this.startOrderNo = startOrderNo;
    }

    public Integer getEndOrderNo() {
        return this.endOrderNo;
    }

    public void setEndOrderNo(Integer endOrderNo) {
        this.endOrderNo = endOrderNo;
    }

    public Integer getIsLeaf() {
        return this.isLeaf;
    }

    public void setIsLeaf(Integer isLeaf) {
        this.isLeaf = isLeaf;
    }

    public Integer[] getIsLeafs() {
        return this.isLeafs;
    }

    public void setIsLeafs(Integer[] isLeafs) {
        this.isLeafs = isLeafs;
    }

    public Integer getStartIsLeaf() {
        return this.startIsLeaf;
    }

    public void setStartIsLeaf(Integer startIsLeaf) {
        this.startIsLeaf = startIsLeaf;
    }

    public Integer getEndIsLeaf() {
        return this.endIsLeaf;
    }

    public void setEndIsLeaf(Integer endIsLeaf) {
        this.endIsLeaf = endIsLeaf;
    }

    public String getIcon() {
        return this.icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getDirDesc() {
        return this.dirDesc;
    }

    public void setDirDesc(String dirDesc) {
        this.dirDesc = dirDesc;
    }

    public String getDirColor() {
        return this.dirColor;
    }

    public void setDirColor(String dirColor) {
        this.dirColor = dirColor;
    }

    public String getDirColorEqual() {
        return this.dirColorEqual;
    }

    public void setDirColorEqual(String dirColorEqual) {
        this.dirColorEqual = dirColorEqual;
    }

    public String[] getDirColors() {
        return this.dirColors;
    }

    public void setDirColors(String[] dirColors) {
        this.dirColors = dirColors;
    }

    public String getCustom1() {
        return this.custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }

    public String getCustom2() {
        return this.custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }

    public String getCustom3() {
        return this.custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }

    public String getCustom4() {
        return this.custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }

    public String getCustom5() {
        return this.custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }

    public String getCustom6() {
        return this.custom6;
    }

    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }

    public Long getDomainId() {
        return this.domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }

    public Long[] getDomainIds() {
        return this.domainIds;
    }

    public void setDomainIds(Long[] domainIds) {
        this.domainIds = domainIds;
    }

    public Long getStartDomainId() {
        return this.startDomainId;
    }

    public void setStartDomainId(Long startDomainId) {
        this.startDomainId = startDomainId;
    }

    public Long getEndDomainId() {
        return this.endDomainId;
    }

    public void setEndDomainId(Long endDomainId) {
        this.endDomainId = endDomainId;
    }

    public Integer getDataStatus() {
        return this.dataStatus;
    }

    public void setDataStatus(Integer dataStatus) {
        this.dataStatus = dataStatus;
    }

    public Integer[] getDataStatuss() {
        return this.dataStatuss;
    }

    public void setDataStatuss(Integer[] dataStatuss) {
        this.dataStatuss = dataStatuss;
    }

    public Integer getStartDataStatus() {
        return this.startDataStatus;
    }

    public void setStartDataStatus(Integer startDataStatus) {
        this.startDataStatus = startDataStatus;
    }

    public Integer getEndDataStatus() {
        return this.endDataStatus;
    }

    public void setEndDataStatus(Integer endDataStatus) {
        this.endDataStatus = endDataStatus;
    }

    public String getCreator() {
        return this.creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getCreatorEqual() {
        return this.creatorEqual;
    }

    public void setCreatorEqual(String creatorEqual) {
        this.creatorEqual = creatorEqual;
    }

    public String[] getCreators() {
        return this.creators;
    }

    public void setCreators(String[] creators) {
        this.creators = creators;
    }

    public String getModifier() {
        return this.modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public String getModifierEqual() {
        return this.modifierEqual;
    }

    public void setModifierEqual(String modifierEqual) {
        this.modifierEqual = modifierEqual;
    }

    public String[] getModifiers() {
        return this.modifiers;
    }

    public void setModifiers(String[] modifiers) {
        this.modifiers = modifiers;
    }

    public Long getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long[] getCreateTimes() {
        return this.createTimes;
    }

    public void setCreateTimes(Long[] createTimes) {
        this.createTimes = createTimes;
    }

    public Long getStartCreateTime() {
        return this.startCreateTime;
    }

    public void setStartCreateTime(Long startCreateTime) {
        this.startCreateTime = startCreateTime;
    }

    public Long getEndCreateTime() {
        return this.endCreateTime;
    }

    public void setEndCreateTime(Long endCreateTime) {
        this.endCreateTime = endCreateTime;
    }

    public Long getModifyTime() {
        return this.modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Long[] getModifyTimes() {
        return this.modifyTimes;
    }

    public void setModifyTimes(Long[] modifyTimes) {
        this.modifyTimes = modifyTimes;
    }

    public Long getStartModifyTime() {
        return this.startModifyTime;
    }

    public void setStartModifyTime(Long startModifyTime) {
        this.startModifyTime = startModifyTime;
    }

    public Long getEndModifyTime() {
        return this.endModifyTime;
    }

    public void setEndModifyTime(Long endModifyTime) {
        this.endModifyTime = endModifyTime;
    }

}
