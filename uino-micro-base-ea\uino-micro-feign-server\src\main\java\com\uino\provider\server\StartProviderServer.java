package com.uino.provider.server;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.quartz.QuartzAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

import lombok.extern.slf4j.Slf4j;

/**
 * 
 * <AUTHOR>
 *
 */
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class, QuartzAutoConfiguration.class})
@ComponentScan(basePackages = {
        "com.uino.comm",
        "com.uino.bean",
        "com.uino.dao",
        "com.uino.util",
        "com.uino.monitor",
        "com.uino.service",
        "com.uino.provider.server"
})
@Configuration
@EnableDiscoveryClient
@Slf4j
public class StartProviderServer {

    static {
        log.info("base rpc 服务开始启动");
    }

    public static void main(String[] args) {
        SpringApplication.run(StartProviderServer.class, args);
    }
}
