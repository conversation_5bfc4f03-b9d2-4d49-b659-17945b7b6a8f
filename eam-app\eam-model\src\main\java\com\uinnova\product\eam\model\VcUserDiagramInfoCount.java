package com.uinnova.product.eam.model;

import com.binary.framework.bean.annotation.Comment;

public class VcUserDiagramInfoCount {

	@Comment("用户id")
	private Long id;
	
	@Comment("用户code")
	private String opCode;
	
	@Comment("用户名称")
	private String opName;
	
	@Comment("创建数量")
	private Integer createCount;
	
	@Comment("分享到小组的数量")
	private Integer shareCount;
	
	@Comment("发布到广场数量")
	private Integer openCount;
	
	@Comment("查看视图数量")
	private Long readCount = 0L;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getOpCode() {
		return opCode;
	}

	public void setOpCode(String opCode) {
		this.opCode = opCode;
	}

	public String getOpName() {
		return opName;
	}

	public void setOpName(String opName) {
		this.opName = opName;
	}

	public Integer getCreateCount() {
		return createCount;
	}

	public void setCreateCount(Integer createCount) {
		this.createCount = createCount;
	}

	public Integer getShareCount() {
		return shareCount;
	}

	public void setShareCount(Integer shareCount) {
		this.shareCount = shareCount;
	}

	public Integer getOpenCount() {
		return openCount;
	}

	public void setOpenCount(Integer openCount) {
		this.openCount = openCount;
	}

	public Long getReadCount() {
		return readCount;
	}

	public void setReadCount(Long readCount) {
		this.readCount = readCount;
	}
}
