package com.uinnova.product.eam.feign.client;

import com.uinnova.product.eam.base.diagram.model.ESDiagram;
import com.uinnova.product.eam.feign.EamFeignConst;
import com.uinnova.product.eam.feign.config.EamFeignConfig;
import com.uinnova.product.eam.model.diagram.MoveDirAndDiagramCdt;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;

/**
 * 视图相关Feign接口
 */
@FeignClient(name = EamFeignConst.SERVER_NAME, path = EamFeignConst.SERVER_ROOT + "/eam/esDiagram",
        configuration = EamFeignConfig.class)
public interface DiagramFeignClient {

    /**
     * 复制文件夹
     *
     * @param body {@link MoveDirAndDiagramCdt}
     * @return {@link com.binary.framework.web.RemoteResult}
     */
    @PostMapping("/copyDirById")
    Map<String, Object> copyDirById(@RequestBody MoveDirAndDiagramCdt body);

    /**
     * 移动文件夹和视图
     *
     * @param body {@link MoveDirAndDiagramCdt}
     * @return {@link com.binary.framework.web.RemoteResult}
     */
    @PostMapping("/moveDirAndDiagram")
    Map<String, Object> moveDirAndDiagram(@RequestBody MoveDirAndDiagramCdt body);

    /**
     * 查找视图详情
     *
     * @param dEnergy 加密Id
     * @return {@link ESDiagram}
     */
    @GetMapping("diagramDetail")
    ESDiagram diagramDetail(@RequestParam("dEnergy") String dEnergy);
}
