package com.uinnova.product.eam.web.cj;

import com.binary.jdbc.Page;
import com.uinnova.product.eam.base.cj.ResultMsg;
import com.uinnova.product.eam.model.cj.vo.ShareUserParamVO;
import com.uinnova.product.eam.model.constants.PathConstants;
import com.uinnova.product.eam.service.cj.service.ShareService;
import com.uino.bean.permission.base.SysUser;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 回收站操作Controller
 *
 * <AUTHOR>
 * @since 2022-3-7 13:36:14
 */
@RestController
@RequestMapping(PathConstants.SHARE_START_PATH)
public class ShareController {

    @Resource
    private ShareService shareService;

    /**
     * 查找被分享
     */
    @PostMapping("/getShareList")
    public ResultMsg list(@RequestBody Map<String, Object> body) {

        return new ResultMsg(shareService.getSharedList(body));
    }

    /**
     * 查询方案被分享人列表
     * @param planDesignId
     * @return
     */
    @GetMapping("/findPlanSharedList")
    public ResultMsg findPlanSharedList(@RequestParam("planDesignId") Long planDesignId) {
        return new ResultMsg(shareService.findPlanSharedList(planDesignId, false));
    }

    /**
     * 查询方案有编辑和有分享权限的人员列表
     * @param planDesignId
     * @return
     */
    @GetMapping("/findPlanSharedMemberList")
    public ResultMsg findPlanSharedMemberList(@RequestParam("planDesignId") Long planDesignId) {
        return new ResultMsg(shareService.findPlanSharedMemberList(planDesignId));
    }

    /**
     * 获取分享的用户列表
     * @param shareUserParamVO
     * @return
     */
    @PostMapping("/findSharedUserList")
    public ResultMsg findSharedUserList(@RequestBody ShareUserParamVO shareUserParamVO) {
        Page<SysUser> result = shareService.findSharedUserList(shareUserParamVO);
        return new ResultMsg(result);
    }
}
