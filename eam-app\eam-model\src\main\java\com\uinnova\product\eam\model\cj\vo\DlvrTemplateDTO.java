package com.uinnova.product.eam.model.cj.vo;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class DlvrTemplateDTO {
    private Long id;
    private String templateName;
    private Long typeId;
    private Map<String,String> bindAssetMap;
    @Comment("模板绑定的资产List")
    private List<BindAssetVo> bindAssetList;

    @Comment("方案发布资产-目录名称")
    private String echoDirName;

    @Comment("方案发布资产-目录id")
    private Long assetsDirId;

    @Comment("方案发布资产-目录类型")
    private Integer dirType;

    @Comment("模板状态 0:禁用 1:设计中 变更 2:设计中 草稿 3:再用 已发布 4:再用 已屏蔽 5:停用 已发布")
    private Integer status;

    @Comment("分类目录id")
    private Long domainDirId;
}
