package com.uinnova.product.eam.web.eam.peer;

import java.util.Map;

/**
 * 刷数据接口
 */
public interface IFlushPeer {

    void flushNewDir();

    /**
     * 刷模型树类型字段
     */
    void flushModelType();

    /**
     * 将原流程建模目录数据刷入新表
     * 20230420发版改造
     */
    void flushBusinessDir(long businessModelId, long dataModelId);

    /**
     * 刷存量方案数据(旧发布目录id==)
     * 20230420发版改造
     */
    void flushPlanData();

    /**
     * 刷存量通用文件夹数据入新表
     */
    void flushGeneralDir();

    void flushSystemDir();

    /**
     * 刷存量方案数据入新表
     */
    void flushPlan();

    /**
     * 刷错乱的dirLvl
     */
    void refreshCategoryDirLvl();

    /**
     * 修复新版目录创建目录时未依据父目录权限同步创建权限
     * @return
     */
    Integer designCategoryPermissionFix();

    /**
     * 刷模型权限
     * @return
     */
    Map<String, Integer> flushModelPermission();

    /**
     * 刷模型版本目录(模型根目录及L0层级目录的parentId)
     * @return
     */
    Map<String, Integer> flushModelVersionTagDir();

    void flushDesignDiagramRelateSys();
}
