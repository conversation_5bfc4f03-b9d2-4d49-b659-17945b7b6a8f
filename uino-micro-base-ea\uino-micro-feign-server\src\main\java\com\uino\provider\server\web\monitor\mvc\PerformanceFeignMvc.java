package com.uino.provider.server.web.monitor.mvc;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.binary.core.io.Resource;
import com.binary.jdbc.Page;
import com.uino.bean.chart.bean.UinoChartDataBean;
import com.uino.bean.monitor.base.SimulationRuleInfo;
import com.uino.bean.monitor.buiness.ImportPerformanceReqDto;
import com.uino.bean.monitor.buiness.PerformanceQueryDto;
import com.uino.bean.tp.base.FinalPerformanceDTO;
import com.uino.provider.feign.monitor.PerformanceFeign;
import com.uino.service.simulation.IPerformanceSvc;

import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("feign/performance")
@Slf4j
public class PerformanceFeignMvc implements PerformanceFeign {

    @Autowired
	private IPerformanceSvc svc;

    @Override
	public void importPerformance(MultipartFile excelFile, ImportPerformanceReqDto importDto) {
        // TODO Auto-generated method stub
        svc.importPerformance(excelFile, importDto);
    }

    @Override
    public void importPerformance(ImportPerformanceReqDto importDto) {
        // TODO Auto-generated method stub
        svc.importPerformance(importDto);
    }

    @Override
    public ResponseEntity<byte[]> exportPerformanceTemplate(Long domainId,Long objId, Integer objType) {
        Resource res = svc.exportPerformanceTemplate(domainId,objId, objType);
        byte[] tempBytes = new byte[4096];
        int readLine = 0;
        ByteArrayOutputStream outStream = new ByteArrayOutputStream();
        try {
            while ((readLine = res.getInputStream().read(tempBytes)) != -1) {
                outStream.write(tempBytes, 0, readLine);
            }
        } catch (IOException e) {
            log.error("读流异常", e);
        }
        byte[] body = outStream.toByteArray();
        try {
            outStream.close();
        } catch (IOException e) {
        }
        ResponseEntity<byte[]> rep = ResponseEntity.ok(body);
        rep.getHeaders().add("fileName", res.getName());
        return rep;
    }

    @Override
	public Page<FinalPerformanceDTO> searchPerformance(PerformanceQueryDto queryDto) {
        // TODO Auto-generated method stub
        return svc.searchPerformance(queryDto);
    }

    @Override
	public UinoChartDataBean<List<Double>> searchPerformanceGraph(PerformanceQueryDto queryDto) {
		return svc.searchPerformanceGraph(queryDto);
	}

	@Override
	public Page<FinalPerformanceDTO> searchNoCiPerformance(PerformanceQueryDto queryDto) {
        return svc.searchNoCiPerformance(queryDto);
    }

    @Override
    public List<String> getPerfDataLabel(Long classId) {
        return svc.getPerfDataLabel(classId);
    }

	@Override
	public void simulationPerformance(SimulationRuleInfo bean) {
		svc.simulationPerformance(bean);
	}
}
