package com.uinnova.product.eam.feign.client;

import com.uinnova.product.eam.feign.EamFeignConst;
import com.uinnova.product.eam.feign.config.EamFeignConfig;
import com.uinnova.product.eam.model.CiQueryCdtExtend;
import com.uinnova.product.eam.model.VcCiClassInfoDto;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uino.bean.cmdb.base.LibType;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@Component
@FeignClient(name = EamFeignConst.SERVER_NAME, path = EamFeignConst.SERVER_ROOT + "/feign/ci", configuration = EamFeignConfig.class)
        public interface IEamCiFeign {

    /**
     * 按指定条件查询CI，并按分类进行汇总
     *
     * @param cdt     查询条件
     * @param libType 库类型    // 私有库 PRIVATE, 设计库 DESIGN, 运行库/基线库 BASELINE
     * @return 包含CI数量的分类列表信息
     */
    @RequestMapping("/queryCiCountByClass")
    List<VcCiClassInfoDto> queryCiCountByClass(@RequestBody CiQueryCdtExtend cdt, @RequestParam("libType") LibType libType);

    /**
     * 根据ci分类名称获取全部的ci信息
     *
     * @param ciClassName ci类型名称
     * @param libType     库类型
     * @return ci信息列表
     */
    @RequestMapping("/getCiInfoListByClassName")
    List<CcCiInfo> getCiInfoListByClassName(@RequestParam("ciClassName") String ciClassName, @RequestParam("libType") LibType libType);
}
