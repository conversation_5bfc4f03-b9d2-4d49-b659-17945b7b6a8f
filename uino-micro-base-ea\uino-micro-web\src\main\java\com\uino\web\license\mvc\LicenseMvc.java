package com.uino.web.license.mvc;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import com.uino.bean.license.BaseLicenseAuthInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.binary.core.io.Resource;
import com.binary.framework.util.ControllerUtils;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uinnova.product.vmdb.comm.model.license.CcLicenseAuthServer;
import com.uinnova.product.vmdb.provider.license.bean.CcLicenseAuthInfo;
import com.uino.init.api.ApiResult;
import com.uino.init.api.ApiVersion;
import com.uino.api.client.license.ILicenseAuthApiSvc;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@ApiVersion(1)
@RestController
@RequestMapping("/license/auth")
@Api(value = "授权管理", tags = { "提供license相关的方法" })
public class LicenseMvc {

    @Autowired
    private ILicenseAuthApiSvc licenseAuthSvc;

	@ApiOperation(value = "查询授权许可信息")
    @PostMapping(value = "/queryLicenseAuthInfo")
    @ModDesc(desc = "查询授权许可信息", pDesc = "查询条件(暂不需要参数)", rDesc = "授权许可信息", rType = CcLicenseAuthInfo.class)
	public ApiResult<BaseLicenseAuthInfo> queryLicenseAuthInfo(HttpServletRequest request, HttpServletResponse response) {
        BaseLicenseAuthInfo authInfo = licenseAuthSvc.queryLicenseAuthInfo();
		return ApiResult.ok(this).data(authInfo);
    }

	@ApiOperation(value = "查询服务器列表")
    @PostMapping("/removeServer")
    @ModDesc(desc = "查询服务器列表", pDesc = "查询条件", pType = Long.class, rDesc = "服务器列表", rType = List.class, rcType = CcLicenseAuthServer.class)
	public ApiResult<List<CcLicenseAuthServer>> removeServer(HttpServletRequest request, HttpServletResponse response,
			@RequestBody Long serverId) {
        licenseAuthSvc.removeServer(serverId);
        List<CcLicenseAuthServer> serverList = licenseAuthSvc.queryServerList();
		return ApiResult.ok(this).data(serverList);
    }

	@ApiOperation(value = "创建客户识别码")
    @PostMapping("/createClientCode")
    @ModDesc(desc = "创建客户识别码", pDesc = "创建条件(暂无条件)", pType = String.class, rDesc = "客户识别码", rType = String.class)
	public ApiResult<String> createClientCode(HttpServletRequest request, HttpServletResponse response) {
        String code = licenseAuthSvc.createClientCode();
		return ApiResult.ok(this).data(code);
    }

	@ApiOperation(value = "注册授权并查询授权信息")
    @PostMapping("/registerLicense")
    @ModDesc(desc = "注册授权并查询授权信息", pDesc = "注册条件", pType = String.class, rDesc = "授权信息", rType = CcLicenseAuthInfo.class)
	public ApiResult<BaseLicenseAuthInfo> registerLicense(HttpServletRequest request, HttpServletResponse response,
			@RequestBody String authCode) {
        licenseAuthSvc.registerLicense(authCode.replaceAll("\"", ""), null);
        BaseLicenseAuthInfo info = licenseAuthSvc.queryLicenseAuthInfo();
		return ApiResult.ok(this).data(info);
    }

	@ApiOperation(value = "根据数据库中登记的授权许可证注册")
    @PostMapping("/registerLicenseByDb")
    @ModDesc(desc = "根据数据库中登记的授权许可证注册", pDesc = "注册条件(暂无条件)", pType = String.class, rDesc = "注册结果(暂不需要)", rType = String.class)
	public ApiResult<Boolean> registerLicenseByDb(HttpServletRequest request, HttpServletResponse response) {
        licenseAuthSvc.registerLicenseByDb();
		return ApiResult.ok(this).data(true);
    }

	@ApiOperation(value = "获取二维码")
    @GetMapping("/getQRCodeImage")
    @ModDesc(desc = "获取二维码", pDesc = "获取条件", pType = Integer.class, rDesc = "二维码信息", rType = Resource.class)
    public void getQRCodeImage(HttpServletRequest request, HttpServletResponse response, @RequestBody(required = false) Map<String, Integer> param) {
        if (param == null) {
            param = new HashMap<>();
        }
        Resource res = licenseAuthSvc.getQRCodeImage(param.get("width"), param.get("height"));
        ControllerUtils.returnResource(request, response, res, "image/x-png", false, res.getName());
    }
}
