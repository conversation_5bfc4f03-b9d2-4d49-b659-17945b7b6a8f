package com.uino.bean.permission.business;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import com.binary.core.i18n.Language;
import com.uinnova.product.vmdb.comm.util.SystemUtil;

/**
 * 
 * <AUTHOR>
 *
 */
public class UserExportLabel {
    private boolean isZhEnv = true;

    /**
     * 默认的使用中文作为导出对象
     */
    public UserExportLabel() {
        try {
            Language language = SystemUtil.getLoginUser().getLanguage();
            if (!Language.ZHC.equals(language)) {
                isZhEnv = false;
            }
        } catch (Exception e) {
            // 默认中文环境
        }
    }

    public UserExportLabel(Language lan) {
        if (!Language.ZHC.equals(lan)) {
            isZhEnv = false;
        }
    }

    public String getLoginCode() {
        return isZhEnv ? "登录名" : "Login Code";
    }

    public String getUserName() {
        return isZhEnv ? "用户名" : "Username";
    }

    public String getLoginPasswd() {
        return isZhEnv ? "初始密码" : "Login Password";
    }

    public String getEmail() {
        return isZhEnv ? "电子邮箱" : "E-mail";
    }

    public String getOrg() {
        return isZhEnv ? "所属组织" : "Org";
    }

    public String getMobileNo() {
        return isZhEnv ? "联系方式" : "Contact information";
    }

    public String getIM() {
        return isZhEnv ? "即时通讯" : "Instant Message";
    }

    public String getRole() {
        return isZhEnv ? "个人角色" : "Role";
    }
    public String getDesc() {
        return isZhEnv ? "描述" : "Description";
    }

    public String getExcelName(boolean isTpl) {
        if (isTpl) {
            return isZhEnv ? "用户信息模板" : "UserInfo Template";
        }
        return isZhEnv ? "用户信息" : "UserInfo";
    }

    /**
     * 获取用户标题行
     * 
     * @return
     */
    public List<String> getUserInfo() {
        return Arrays.asList(getLoginCode(), getUserName(), getLoginPasswd(), getEmail(), getMobileNo(), getIM(), getOrg(), getRole(), getDesc());
    }

    /**
     * 获取用户须知
     * 
     * @return
     */
    public List<String> getInfoAttention() {
        List<String> result = new ArrayList<>();
        if (isZhEnv) {
            result.add("填写须知");
            result.add("1、不能在该Excel表中对成员信息类别进行增加、删除或修改；");
            result.add("2、Excel中红色字段为必填字段,黑色字段为选填字段；");
            result.add("3、登录名：成员的唯一标识，由1-50个字母、数字、点(.)、减号(-)或下划线(_)组成，登录名相同则不再支持导入；");
            result.add("4、初始密码：由8-20个大小写字母、数字和键盘特殊符号组成，支持明文密码导入，为空则默认初始密码为Uinnova_001；");
            result.add("5、所属组织：上下级组织间用‘/’隔开，且从最上级部门开始，例如\"优锘科技/产品研发部/产品管理组\"。若存在属于多个部门的情况，不同部门之间用“;”隔开。部门若为空，则自动将成员添加到选择的目录下。部门字段长度不能超过50个字符；");
            result.add("6、联系方式：支持手机号、固定电话，由数字和减号（-）组成；");
            result.add("7、个人角色：关联“权限管理”模块角色权限，需先在权限管理中创建对应的角色，个人角色列中数据方才支持导入。");
        } else {
            result.add("Guidance Notes");
            result.add("1. The membership information category cannot be added, deleted or modified in the Excel table.");
            result.add("2. The red field in Excel is the required field, and the black field is the optional field.");
            result.add(
                "3. Login name: the unique identification of members, consisting of 1-50 letters, numbers, dots (.), minus sign (-) or underscore (), the same login name no longer supports import.");
            result.add("4. Initial password: 8-20 uppercase letters, numbers and keyboard special symbols, support plaintext password import, if blank, the default initial password is Tarsier_001.");
            result.add(
                "5. Subordinate organizations: The subordinate and subordinate organizations are separated by'/'and start from the most superior departments, such as \"Superior Relief Technology/Product Research and Development Department/Product Management Group\". If there are multiple departments, different departments should be separated by ';'. If the Department is empty, members are automatically added to the selected directory. Sector field length cannot exceed 50 characters.");
            result.add("6. Contact information: support mobile phone number, fixed telephone, composed of numbers and minus sign (-).");
            result.add(
                "7. Personal role: To associate the role permissions of the \"permission management\" module, the corresponding roles need to be created in the permission management first, and the data in the personal role column can only support the import.");
        }
        return result;
    }
}
