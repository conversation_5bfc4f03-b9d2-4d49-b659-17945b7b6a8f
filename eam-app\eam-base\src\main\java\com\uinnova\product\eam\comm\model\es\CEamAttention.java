package com.uinnova.product.eam.comm.model.es;

import com.binary.framework.bean.Condition;
import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.util.List;

@Data
@Comment("操作计数表[UINO_EAM_ATTENTION]")
public class CEamAttention implements Condition {

    private Long id;

    private Long createTime;

    private Long modifyTime;
    /** 关注的文件夹或视图id */
    private Long attentionId;
    /** 关注的架构类型，1：企业级架构设计，2：系统级架构设计，3：企业级架构资产，4：系统级架构资产 */
    private Integer attentionBuild;
    /** 关注的文件类型：1:文件夹，2：视图  3:plan 5:矩阵*/
    private Integer attentionType;
    /** 用户id */
    private Long userId;

    /** 特殊视图 0：否  1:是 */
    private Integer specialView;
    /** 所属文件夹id */
    private Long relationDirId;
    /** 关注的文件夹或视图id或方案id */
    private List<Long> attentionIds;
}
