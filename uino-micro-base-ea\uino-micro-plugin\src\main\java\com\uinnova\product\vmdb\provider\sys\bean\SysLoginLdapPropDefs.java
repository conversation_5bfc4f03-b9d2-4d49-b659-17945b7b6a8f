package com.uinnova.product.vmdb.provider.sys.bean;

import com.binary.framework.bean.annotation.Comment;

public class SysLoginLdapPropDefs {

    @Comment("操作员编码")
    private String opCode;

    @Comment("操作员姓名")
    private String opName;

    @Comment("操作员类别")
    private Integer opKind;

    @Comment("手机号")
    private String mobileNo;

    @Comment("电子邮件地址")
    private String emailAdress;

    @Comment("备注")
    private String notes;

    @Comment("角色")
    private String roles;

    @Comment("登录代码")
    private String loginCode;

    @Comment("登录密码")
    private String loginPasswd;

    @Comment("操作员简称")
    private String shortName;

    @Comment("所属组织")
    private String custom1;

    @Comment("使用脚本的方式进行映射")
    private String script;

    public String getOpCode() {
        return opCode;
    }

    public void setOpCode(String opCode) {
        this.opCode = opCode;
    }

    public String getOpName() {
        return opName;
    }

    public void setOpName(String opName) {
        this.opName = opName;
    }

    public Integer getOpKind() {
        return opKind;
    }

    public void setOpKind(Integer opKind) {
        this.opKind = opKind;
    }

    public String getMobileNo() {
        return mobileNo;
    }

    public void setMobileNo(String mobileNo) {
        this.mobileNo = mobileNo;
    }

    public String getEmailAdress() {
        return emailAdress;
    }

    public void setEmailAdress(String emailAdress) {
        this.emailAdress = emailAdress;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public String getRoles() {
        return roles;
    }

    public void setRoles(String roles) {
        this.roles = roles;
    }

    public String getLoginCode() {
        return loginCode;
    }

    public void setLoginCode(String loginCode) {
        this.loginCode = loginCode;
    }

    public String getLoginPasswd() {
        return loginPasswd;
    }

    public void setLoginPasswd(String loginPasswd) {
        this.loginPasswd = loginPasswd;
    }

    public String getShortName() {
        return shortName;
    }

    public void setShortName(String shortName) {
        this.shortName = shortName;
    }

    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }

    public String getScript() {
        return script;
    }

    public void setScript(String script) {
        this.script = script;
    }
}
