package com.uinnova.product.eam.comm.model;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class DecisionParam {

    @Comment("查询参数：查询决策标题和应用系统")
    private String like;

    @Comment("查询参数：决策状态(枚举：1待受理，2重新申请，3待决策，4待发布，5已发布)")
    private List<Integer> status;

    @NotNull(message = "分页信息[每页条数]不可为空")
    private Integer pageSize;

    @NotNull(message = "分页信息[起始页码]不可为空")
    private Integer pageNum;
}
