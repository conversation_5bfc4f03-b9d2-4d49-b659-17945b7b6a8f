package com.uinnova.product.eam.service.es;

import com.uinnova.product.eam.comm.model.es.FlowSystemProcessSingData;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;

/**
 * 流程体系签发流程表
 *
 * <AUTHOR>
 * @since 2024/5/27 14:50
 */
@Service
public class FlowSystemProcessSignDataDao extends AbstractESBaseDao<FlowSystemProcessSingData, FlowSystemProcessSingData> {
    @Override
    public String getIndex() {
        return "uino_flow_system_process_sign_data";
    }

    @Override
    public String getType() {
        return "_doc";
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }
}
