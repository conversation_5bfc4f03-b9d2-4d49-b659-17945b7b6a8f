package com.uinnova.product.eam.model.diagram;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.model.enums.AssetType;
import lombok.Data;

import java.io.Serializable;

/**
 * 文件路径响应
 * <AUTHOR>
 */
@Data
public class EamPathResultDTO implements Serializable {

    @Comment("资产类型")
    private AssetType type;

    @Comment("文件夹id")
    private Long dirId;

    @Comment("类型")
    private Integer pushType;

    @Comment("路径全名")
    private String dirNamePath;

    @Comment("资产id")
    private String assetId;

    public void setType(AssetType type) {
        this.type = type;
        this.pushType = type.assetType;
    }
}
