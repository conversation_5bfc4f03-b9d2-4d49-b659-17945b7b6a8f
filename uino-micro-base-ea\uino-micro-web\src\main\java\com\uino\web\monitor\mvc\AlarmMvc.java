package com.uino.web.monitor.mvc;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import com.uino.bean.permission.base.SysUser;
import com.uino.util.sys.SysUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.binary.framework.util.ControllerUtils;
import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uino.api.client.monitor.IAlarmApiSvc;
import com.uino.bean.monitor.base.ESAlarm;
import com.uino.bean.monitor.buiness.AlarmInfo;
import com.uino.bean.monitor.buiness.AlarmQueryDto;
import com.uino.bean.monitor.buiness.SimulationAlarmBean;

/**
 * 告警mvc
 * 
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping(value = "/monitor/alarm")
public class AlarmMvc {

    @Autowired
    private IAlarmApiSvc alarmApi;

    /**
     * 分页查询告警
     * 
     * @param reqDto
     * @param request
     * @param response
     */
    @PostMapping("searchAlarms")
    @ModDesc(desc = "分页查询告警", pDesc = "查询条件", pType = AlarmQueryDto.class, rDesc = "告警分页数据", rType = Page.class, rcType = AlarmInfo.class)
    public void searchAlarms(@RequestBody AlarmQueryDto reqDto, HttpServletRequest request,
            HttpServletResponse response) {
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        reqDto.setDomainId(currentUserInfo.getDomainId());
        Page<AlarmInfo> rep = alarmApi.searchAlarms(reqDto);
        ControllerUtils.returnJson(request, response, rep);
    }

    /**
     * 保存告警
     * 
     * @param reqDto
     * @param request
     * @param response
     */
    @PostMapping("saveAlarm")
    @ModDesc(desc = "保存告警", pDesc = "待保存告警信息", pType = ESAlarm.class, rDesc = "操作结果", rType = Boolean.class)
    public void saveAlarm(@RequestBody ESAlarm reqDto, HttpServletRequest request, HttpServletResponse response) {
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        reqDto.setDomainId(currentUserInfo.getDomainId());
        alarmApi.saveAlarm(reqDto);
        ControllerUtils.returnJson(request, response, true);
    }

	@PostMapping("simulationAlarms")
	@ModDesc(desc = "批量模拟告警信息", pDesc = "待保存告警信息", pType = SimulationAlarmBean.class, rDesc = "操作结果", rType = Boolean.class)
	public void simulationAlarms(@RequestBody SimulationAlarmBean bean, HttpServletRequest request,
			HttpServletResponse response) {
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        bean.setDomainId(currentUserInfo.getDomainId());
		alarmApi.simulationAlarms(bean);
		ControllerUtils.returnJson(request, response, true);
	}
}
