package com.uinnova.product.eam.model.dix;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.util.List;

/**
 * 应用系统对接配置项实体
 * <AUTHOR>
 */
@Data
public class AppSystemConfig {
    @Comment("子系统分类标识")
    private String terminal;
    @Comment("所属子系统")
    private String belong;
    @Comment("系统编号获取字段名")
    private String systemId;
    @Comment("对象分类及关系标识")
    private List<AppSystemRltConfig> rltInfo;
}
