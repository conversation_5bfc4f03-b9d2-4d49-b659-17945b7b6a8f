package com.uinnova.product.eam.feign.client;

import com.binary.jdbc.Page;
import com.uinnova.product.eam.base.model.FreeSoftwareMeta;
import com.uinnova.product.eam.base.model.PageDTO;
import com.uinnova.product.eam.feign.FeignConst;
import com.uinnova.product.eam.model.asset.*;
import com.uino.bean.cmdb.base.ESCIInfo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@FeignClient(name = FeignConst.APPLICATION,path = FeignConst.FREESOFTWARE_PATH)
public interface IEamFreeSoftwareClient {

    Page<SoftwareMasterDTO> selectPage(PageDTO<SearchSoftwareDTO> page);

    Boolean checkSoftwareName(SoftwareCheckDTO softwareCheckDTO);

    Long saveSoftware(SoftwareMasterDTO copy);

    List<SoftwareMasterDTO> select(SearchSoftwareDTO searchSoftwareDTO);

    SoftwareMasterDTO selectById(SoftwarePrimaryKeyDTO keyDTO);

    boolean softwareNameisExist(Long ciClassId, String softwareName);

    SoftwareMasterEleDTO selectAttrConf(FreeSoftwareMeta freeSoftwareMeta);

    List<ESCIInfo> getFreeSoftwareByLoginCode(String loginCode);
}
