package com.uino.bean.sys.query;

import java.io.Serializable;

import com.uino.bean.cmdb.query.ESSearchBase;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 
 * <AUTHOR>
 *
 */
@Setter
@Getter
@ApiModel(value="操作日志查询类",description = "操作日志查询信息")
public class ESCIOperateLogSearchBean extends ESSearchBase implements Serializable {
    /**
    * 
    */
    private static final long serialVersionUID = 5753663721858667051L;

    @ApiModelProperty(value="开始时间")
    private Long startTime;

    @ApiModelProperty(value="结束时间")
    private Long endTime;

    @ApiModelProperty(value="分类名称",example = "sport")
    private String ciClassName;

    @ApiModelProperty(value="ciCode")
    private String ciCode;

    @ApiModelProperty(value="CI业务主键")
    private String ciPrimaryKey;

    @ApiModelProperty(value="操作者",example = "mike")
    private String operator;

    @ApiModelProperty(value="动态",example = "1")
    private Integer dynamic;

    /**
     * 包含新旧属性值
     */
    @ApiModelProperty(value="属性")
    private String attrs;

    @ApiModelProperty(value="检索值",example = "admin")
    private String keyword;

    @ApiModelProperty(value="所属域",example = "1")
    private Long domainId;

}
