package com.uinnova.product.eam.web.cj;

import cn.hutool.core.lang.Assert;
import com.binary.framework.util.ControllerUtils;
import com.binary.framework.web.RemoteResult;
import com.uinnova.product.eam.base.enums.ResultCodeEnum;
import com.uinnova.product.eam.model.cj.domain.CJResource;
import com.uinnova.product.eam.model.cj.domain.ChapterResource;
import com.uinnova.product.eam.model.cj.vo.ContextFileVo;
import com.uinnova.product.eam.service.cj.service.CJResourceDaoService;
import com.uinnova.product.eam.service.exception.BusinessException;
import com.uino.util.sys.SysUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/cj/resource")
public class ResourceController {

    @Autowired
    private CJResourceDaoService cjResourceDaoService;


    @Value("${uino.eam.word_name_regex}")
    private String resNameRegex;

    @Value(("${uino.eam.word.chapter_name_regex}"))
    private String resChapterNameRegx;

    @Value("${http.resource.space}")
    private String httpPath;

    @PostMapping("/uploadFile")
    public void uploadFile(HttpServletRequest request, HttpServletResponse response, @RequestParam(name = "files") MultipartFile[] files) {
        Optional<MultipartFile> optional = Arrays.stream(files).filter(file -> !file.getOriginalFilename().matches(resNameRegex)).findAny();
        if (optional.isPresent()) {
            throw new BusinessException(ResultCodeEnum.FILE_VALID_ERROR);
        }
        List<CJResource> resources = cjResourceDaoService.upload(files, SysUtil.getCurrentUserInfo());
        resources.stream().forEach(item -> item.setResPath(httpPath+item.getResPath()));
        ControllerUtils.returnJson(request, response, resources);
    }

    @PostMapping("/ossTest")
   public void ossTest(@RequestParam(name = "file")MultipartFile file,@RequestParam(required = false) String objectKey) {
        cjResourceDaoService.ossTest(file,objectKey);
    }


    @GetMapping("/uploadChapterFile")
    public void uploadChapterFile(HttpServletRequest request, HttpServletResponse response, @RequestParam(name = "files") MultipartFile[] files,
                                  @RequestParam Long templateId, @RequestParam Long chapterId) {
        Assert.isTrue(templateId != null,"上传的交付物模板id不能为空");
        Assert.isTrue(chapterId != null,"上传的交付物章节模板id不能为空");
        Optional<MultipartFile> optional = Arrays.stream(files).filter(file -> !file.getOriginalFilename().matches(resChapterNameRegx)).findAny();
        if (optional.isPresent()) {
            throw new BusinessException(ResultCodeEnum.FILE_VALID_ERROR);
        }
        boolean isUploadFile = cjResourceDaoService.checkUploadFileCount(templateId,chapterId);
        if (!isUploadFile) {
            throw new BusinessException(ResultCodeEnum.FILE_UPLOAD_COUNT_ERROR);
        }
        List<ChapterResource> chapterResources = cjResourceDaoService.uploadChapterFile(files, SysUtil.getCurrentUserInfo(),templateId,chapterId);
        ControllerUtils.returnJson(request, response, chapterResources);
    }

    @PostMapping("/uploadContextFile")
    public void uploadContextFile(HttpServletRequest request, HttpServletResponse response, @RequestParam(name = "file") MultipartFile file) {
        boolean matches = false;
        try {
            matches = file.getOriginalFilename().matches(resChapterNameRegx);
        } catch (Exception e) {
            throw new BusinessException("上传的文件名称不能为空");
        }
        if (!matches) {
            throw new BusinessException(ResultCodeEnum.FILE_VALID_ERROR);
        }
        ContextFileVo result = cjResourceDaoService.uploadContextFile(file);
        ControllerUtils.returnJson(request, response,  Collections.singletonList(result));
    }

    @GetMapping("/deleteChapterFile")
    public void deleteChapterFile(@RequestParam Long id) {
        cjResourceDaoService.deleteChapterFile(id);
    }

    @GetMapping("/getPlanResource")
    public RemoteResult getPlanResource(@RequestParam Long templateId ,@RequestParam Long chapterTemplateId){
        List<ChapterResource> result = cjResourceDaoService.getPlanResource(templateId,chapterTemplateId);
        return new RemoteResult(result);
    }

}
