package com.uino.api.client.sys.rpc;

import com.uino.api.client.sys.IOperateLogModuleApiSvc;
import com.uino.bean.sys.base.ESOperateLogModule;
import com.uino.provider.feign.sys.OperateLogModuleFeign;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class OperateLogModuleApiSvcRpc implements IOperateLogModuleApiSvc {

    @Autowired
    private OperateLogModuleFeign operateLogModuleFeign;

    @Override
    public List<ESOperateLogModule> getAll() {
        return operateLogModuleFeign.getAll();
    }

    @Override
    public ESOperateLogModule getModuleInfoByMvc(String mvcPath) {
        return operateLogModuleFeign.getModuleInfoByMvc(mvcPath);
    }
}
