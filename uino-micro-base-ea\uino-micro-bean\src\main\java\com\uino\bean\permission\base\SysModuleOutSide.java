package com.uino.bean.permission.base;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @Title: SysModuleOutSide
 * @Description: SysModuleOutSide
 * @Author: YGQ
 * @Create: 2021-08-02 19:59
 **/
@Getter
@Setter
public class SysModuleOutSide {

    @ApiModelProperty(value = "id", example = "123")
    private Long id;

    @ApiModelProperty(value = "模块代码")
    private String moduleCode;

    @ApiModelProperty(value = "模块名称", example = "模块名称", required = true)
    private String moduleName;

    @ApiModelProperty(value = "模块标识", example = "/123", required = true)
    private String moduleSign;

    @ApiModelProperty(value = "用户自定义名称")
    private String label;

    @ApiModelProperty(value = "父节点", required = true)
    private Long parentId;

    @ApiModelProperty(value = "模块描述")
    private String moduleDesc;

    @ApiModelProperty(value = "显示排序")
    private Integer orderNo;

    @ApiModelProperty(value = "链接地址")
    private String moduleUrl;

    @ApiModelProperty(value = "模块类型", example = "button/menu")
    private String moduleType;

    @ApiModelProperty(value = "创建时间")
    private Long createTime;

    @ApiModelProperty(value = "修改时间")
    private Long modifyTime;

    public static SysModuleOutSide transToSysModuleOutSide(SysModule sysModule) {
        SysModuleOutSide sysModuleOutSide = new SysModuleOutSide();
        sysModuleOutSide.setId(sysModule.getId());
        sysModuleOutSide.setModuleCode(sysModule.getModuleCode());
        sysModuleOutSide.setModuleName(sysModule.getModuleName());
        sysModuleOutSide.setModuleSign(sysModule.getModuleSign());
        sysModuleOutSide.setLabel(sysModule.getLabel());
        sysModuleOutSide.setParentId(sysModule.getParentId());
        sysModuleOutSide.setModuleDesc(sysModule.getModuDesc());
        sysModuleOutSide.setOrderNo(sysModule.getOrderNo());
        sysModuleOutSide.setCreateTime(sysModule.getCreateTime());
        sysModuleOutSide.setModifyTime(sysModule.getModifyTime());
        Integer moduleType = sysModule.getModuleType();
        if (null == moduleType || 0 == moduleType) {
            sysModuleOutSide.moduleType = "menu";
        } else {
            sysModuleOutSide.setModuleType("button");
        }
        return sysModuleOutSide;
    }
}
