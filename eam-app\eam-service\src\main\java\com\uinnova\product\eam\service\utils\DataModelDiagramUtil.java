package com.uinnova.product.eam.service.utils;

import com.alibaba.fastjson.JSON;
import com.binary.core.util.BinaryUtils;
import com.uinnova.product.eam.base.diagram.model.*;
import com.uinnova.product.eam.model.diagram.DiagramNodeJson;
import com.uinnova.product.eam.model.dm.bean.DataModelEntityNodeVo;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据建模视图相关工具类
 * <AUTHOR>
 */
public class DataModelDiagramUtil {

    /**
     * 取出视图中ci信息
     * @param diagrams 视图集合
     * @return ciCode集合
     */
    public static Set<String> getDiagramCiList(List<ESDiagramInfoDTO> diagrams){
        Set<String> ciCodes = new HashSet<>();
        for (ESDiagramInfoDTO diagram : diagrams) {
            for(ESDiagramModel model : diagram.getModelList()){
                if(BinaryUtils.isEmpty(model.getNodeDataArray())){
                    continue;
                }
                Set<String> ciList = getNodeCiCode(model.getNodeDataArray());
                if(BinaryUtils.isEmpty(ciList)){
                    continue;
                }
                ciCodes.addAll(ciList);
            }
        }
        return ciCodes;
    }

    /**
     * 取出视图中rlt信息
     * @param diagrams 视图集合
     * @return 关系code集合
     */
    public static Set<String> getDiagramRltList(List<ESDiagramInfoDTO> diagrams){
        Set<String> rltCodes = new HashSet<>();
        for (ESDiagramInfoDTO diagram : diagrams) {
            for(ESDiagramModel model : diagram.getModelList()){
                if(BinaryUtils.isEmpty(model.getLinkDataArray())){
                    continue;
                }
                Set<String> rltList = getLinkRltCode(model.getLinkDataArray());
                if(BinaryUtils.isEmpty(rltList)){
                    continue;
                }
                rltCodes.addAll(rltList);
            }
        }
        return rltCodes;
    }


    public static Set<String> getNodeCiCode(List<ESDiagramNode> nodes){
        Set<String> ciCodes = new HashSet<>();
        for (ESDiagramNode node : nodes) {
            if(BinaryUtils.isEmpty(node.getCiCode())){
                continue;
            }
            ciCodes.add(node.getCiCode());
            if(BinaryUtils.isEmpty(node.getNodeJson())){
                continue;
            }
            //添加处理ER图实体属性节点情况
            DiagramNodeJson nodeJson = JSON.parseObject(node.getNodeJson(), DiagramNodeJson.class);
            if(BinaryUtils.isEmpty(nodeJson.getItems())){
                continue;
            }
            List<String> attrCodes = nodeJson.getItems().stream().map(DataModelEntityNodeVo::getCiCode).filter(Objects::nonNull).collect(Collectors.toList());
            if(!BinaryUtils.isEmpty(attrCodes)){
                ciCodes.addAll(attrCodes);
            }
        }
        return ciCodes;
    }

    public static Set<String> getLinkRltCode(List<ESDiagramLink> links){
        Set<String> ciCodes = new HashSet<>();
        for (ESDiagramLink link : links) {
            if(BinaryUtils.isEmpty(link.getUniqueCode())){
                continue;
            }
            ciCodes.add(link.getUniqueCode());
        }
        return ciCodes;
    }

    /**
     * 数据建模特殊关系(实体与实体属性无实线关系)
     * 获取实体与实体属性间关系
     * @param diagramList 视图集合
     * @param entityArtifact 实体相关制品id
     * @param rltClassId 关系id
     * @return 关系code
     */
    public static Set<String> getDataModelRlt(List<ESDiagramDTO> diagramList, List<Long> entityArtifact, Long rltClassId) {
        Set<String> result = new HashSet<>();
        if(BinaryUtils.isEmpty(diagramList)){
            return result;
        }
        List<ESDiagramNode> nodeList = new ArrayList<>();
        for (ESDiagramDTO each : diagramList) {
            ESDiagramInfoDTO diagram = each.getDiagram();
            if(BinaryUtils.isEmpty(diagram.getViewType())){
                continue;
            }
            long viewType = Long.parseLong(diagram.getViewType());
            if(!entityArtifact.contains(viewType) || BinaryUtils.isEmpty(diagram.getModelList())){
                continue;
            }
            for (ESDiagramModel model : diagram.getModelList()) {
                if(BinaryUtils.isEmpty(model.getNodeDataArray())){
                    continue;
                }
                nodeList.addAll(model.getNodeDataArray());
            }
        }
        if(BinaryUtils.isEmpty(nodeList)){
            return result;
        }
        for (ESDiagramNode node : nodeList) {
            DiagramNodeJson nodeJson = JSON.parseObject(node.getNodeJson(), DiagramNodeJson.class);
            if(BinaryUtils.isEmpty(node.getCiCode()) || BinaryUtils.isEmpty(nodeJson) || BinaryUtils.isEmpty(nodeJson.getItems())){
                continue;
            }
            for (DataModelEntityNodeVo item : nodeJson.getItems()) {
                if(BinaryUtils.isEmpty(item.getCiCode())){
                    continue;
                }
                String uniqueCode = "UK_"+node.getCiCode()+"_"+rltClassId+"_"+item.getCiCode();
                result.add(uniqueCode);
            }
        }
        return result;
    }

}
