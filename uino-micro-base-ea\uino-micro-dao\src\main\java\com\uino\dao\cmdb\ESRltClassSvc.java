package com.uino.dao.cmdb;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.binary.core.exception.MessageException;
import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClass;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uino.bean.cmdb.base.ESCIClassInfo;
import com.uino.dao.AbstractESBaseDao;
import com.uino.dao.ESConst;
import com.uino.dao.util.ESUtil;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.TermQueryBuilder;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.sort.SortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * ES-CI分类服务
 *
 * <AUTHOR>
 */
@Service
public class ESRltClassSvc extends AbstractESBaseDao<ESCIClassInfo, CCcCiClass> {

    Log logger = LogFactory.getLog(ESRltClassSvc.class);

    @Autowired
    ESCmdbCommSvc commSvc;

    @Override
    public String getIndex() {
        return ESConst.INDEX_CMDB_RLTCLASS;
    }

    @Override
    public String getType() {
        return ESConst.INDEX_CMDB_RLTCLASS;
    }

    @Override
    protected void savePreOptionCore(ESCIClassInfo cls) {
        if (cls.getLineType() == null) {
            cls.setLineType("solid");
        }
        if (cls.getLineBorder() == null) {
            cls.setLineBorder(1);
        }
        if (cls.getLineColor() == null) {
            cls.setLineColor("#000");
        }
        if (cls.getLineDirect() == null) {
            cls.setLineDirect("classic");
        }
        if (cls.getLineDispType() == null) {
            cls.setLineDispType(1);
        }
        cls.setClassStdCode(cls.getClassCode().toUpperCase());
        cls.setCcAttrDefs(cls.getCcAttrDefs() == null ? new LinkedList<>() : cls.getCcAttrDefs());
        cls.getCcAttrDefs().forEach(def -> {
            def.setProStdName(def.getProName().toUpperCase());
            def.setIsRequired(def.getIsRequired() == null ? 0 : def.getIsRequired());
            def.setIsMajor(def.getIsMajor() == null ? 0 : def.getIsMajor());
            def.setLineLabelAlign(def.getLineLabelAlign() == null ? 1 : def.getLineLabelAlign());
            def.setIsCiDisp(def.getIsCiDisp() == null ? 0 : def.getIsCiDisp());

        });

    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }

    /**
     * <b> 根据ID获取CI分类
     *
     * @param id 分类ID
     * @return
     */
    public CcCiClassInfo queryClassById(Long id) {
        ESCIClassInfo ciClass = super.getById(id);
        return commSvc.tranCcCiClassInfo(ciClass);
    }

    /**
     * <b>根据cdt条件获取CI分类
     *
     * @param pageNum
     * @param pageSize
     * @param cdt 自动生成的查询的对象
     * @return
     */
    public List<CcCiClassInfo> queryClassByCdt(int pageNum, int pageSize, CCcCiClass cdt) {
        BoolQueryBuilder query = ESUtil.cdtToBuilder(cdt);
        Page<ESCIClassInfo> page = super.getListByQuery(pageNum, pageSize, query);
        List<CcCiClassInfo> list = new ArrayList<CcCiClassInfo>();
        List<ESCIClassInfo> datas = page.getData();
        for (ESCIClassInfo data : datas) {
            list.add(commSvc.tranCcCiClassInfo(data));
        }
        return list;
    }

    /**
     * <b>获取所有CI分类
     *
     * @return
     */
    public List<CcCiClassInfo> queryAllClasses(Long domainId) {
        List<SortBuilder<?>> sorts = new LinkedList<>();
        sorts.add(SortBuilders.fieldSort("orderNo").order(SortOrder.ASC).unmappedType("long"));
        sorts.add(SortBuilders.fieldSort("createTime").order(SortOrder.ASC));
        Page<ESCIClassInfo> page = super.getSortListByQuery(1, 3000, QueryBuilders.termQuery("domainId", domainId), sorts);
        List<CcCiClassInfo> list = new ArrayList<CcCiClassInfo>();
        List<ESCIClassInfo> datas = page.getData();
        for (ESCIClassInfo data : datas) {
            list.add(commSvc.tranCcCiClassInfo(data));
        }
        return list;
    }

    /**
     * <b>获取类定义的字段对照表
     *
     * @param classId 分类id
     * @return
     */
    public Map<Long, String> getClassAttrField(Long classId) {
        Map<Long, String> map = new HashMap<Long, String>();
        TermQueryBuilder query = QueryBuilders.termQuery("id", classId);
        try {
            SearchResponse response = this.getSearchResponseByQuery(query);
            SearchHit[] hits = response.getHits().getHits();
            for (SearchHit hit : hits) {
                String recordStr = hit.getSourceAsString();
                JSONObject result = JSON.parseObject(recordStr);
                JSONArray attrDefs = result.getJSONArray("attrDefs");
                for (int i = 0; i < attrDefs.size(); i++) {
                    JSONObject attrs = attrDefs.getJSONObject(i);
                    Long id = attrs.getLong("id");
                    String name = attrs.getString("proStdName");
                    map.put(id, name);
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
			throw new MessageException(e.getMessage());
        }
        return map;
    }

    /**
     * <b>获取类定义的字段类型对照表(key:属性id,value:属性的类型)
     *
     * @param classId
     * @return
     */
    public Map<Long, Integer> getClassAttrFieldType(Long classId) {
        Map<Long, Integer> map = new HashMap<Long, Integer>();
        TermQueryBuilder query = QueryBuilders.termQuery("id", classId);
        try {
            SearchResponse response = this.getSearchResponseByQuery(query);
            SearchHit[] hits = response.getHits().getHits();
            for (SearchHit hit : hits) {
                String recordStr = hit.getSourceAsString();
                JSONObject result = JSON.parseObject(recordStr);
                JSONArray attrDefs = result.getJSONArray("attrDefs");
                for (int i = 0; i < attrDefs.size(); i++) {
                    JSONObject attrs = attrDefs.getJSONObject(i);
                    Long id = attrs.getLong("id");
                    Integer name = attrs.getInteger("proType");
                    map.put(id, name);
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
			throw new MessageException(e.getMessage());
        }
        return map;
    }

    @Override
    public Long saveOrUpdate(ESCIClassInfo rltClass) {
        if (rltClass.getOrderNo() == null) {
            // 设置orderNo
            rltClass.setOrderNo(getNextOrderNo(rltClass.getDomainId()));
        }
        return super.saveOrUpdate(rltClass);
    }

    /**
     * 获取下一个orderNo
     * @author: weixuesong
     * @date: 2020/8/6 11:23
     * @return: int
     */
    public int getNextOrderNo(Long domainId) {
        List<SortBuilder<?>> sorts = new LinkedList<>();
        sorts.add(SortBuilders.fieldSort("orderNo").order(SortOrder.DESC).unmappedType("long"));
        sorts.add(SortBuilders.fieldSort("createTime").order(SortOrder.DESC));
        Page<ESCIClassInfo> page = super.getSortListByQuery(1, 1, QueryBuilders.constantScoreQuery(QueryBuilders.termQuery("domainId", domainId)), sorts);
        if (page.getData().isEmpty()) {
            return 0;
        }
        if (page.getData().get(0).getOrderNo() == null) {
            return 0;
        }
        return page.getData().get(0).getOrderNo() + 1;
    }

    /**
     * 初始化所有关系的orderNo
     * @author: weixuesong
     * @date: 2020/8/6 17:00
     * @return: boolean
     */
    public boolean initAllOrderNo(Long domainId) {


        AtomicInteger orderNo = new AtomicInteger((int)super.countByCondition(QueryBuilders.termQuery("domainId", domainId)));
        Page<ESCIClassInfo> page = super.getSortListByQuery(1, 9999, QueryBuilders.termQuery("domainId", domainId), "createTime", true);
        if (page.getData().isEmpty()) {
            return true;
        }
        page.getData().forEach(esCiClass -> esCiClass.setOrderNo(orderNo.getAndIncrement()));
        Integer result = super.saveOrUpdateBatch(page.getData());
        if (result == 1) {
            return true;
        }
        return false;
    }
}
