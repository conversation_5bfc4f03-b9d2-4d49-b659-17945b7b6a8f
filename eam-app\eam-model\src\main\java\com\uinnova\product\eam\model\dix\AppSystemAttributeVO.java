package com.uinnova.product.eam.model.dix;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.io.Serializable;

@Data
public class AppSystemAttributeVO implements Serializable {
    @Comment("属性id")
    private String id;
    @Comment("属性中文名称")
    private String cnName;
    @Comment("属性英文名称")
    private String enName;
    @Comment("属性数据类型")
    private String dataType;
    @Comment("是否非空：true非空/false可空")
    private Boolean nonEmpty;
    @Comment("是否主键：true是/false否")
    private Boolean primaryKey;
    @Comment("是否外键：true是/false否")
    private Boolean foreignKey;
    @Comment("新增:0/更新:1")
    private Integer status;

    public AppSystemAttributeVO() {
    }

    public AppSystemAttributeVO(String id, String cnName, String enName) {
        this.id = id;
        this.cnName = cnName;
        this.enName = enName;
    }
}
