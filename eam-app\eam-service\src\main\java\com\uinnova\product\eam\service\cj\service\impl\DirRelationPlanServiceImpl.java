package com.uinnova.product.eam.service.cj.service.impl;

import com.alibaba.excel.util.StringUtils;
import com.uinnova.product.eam.model.cj.domain.DirRelationPlan;
import com.uinnova.product.eam.service.cj.dao.DirRelationPlanDao;
import com.uinnova.product.eam.service.cj.service.DirRelationPlanService;
import com.uinnova.product.eam.service.exception.BusinessException;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * @description:
 * @author: Lc
 * @create: 2022-03-17 11:11
 */
@Service
public class DirRelationPlanServiceImpl implements DirRelationPlanService {

    @Resource
    private DirRelationPlanDao dirRelationPlanDao;

    @Override
    public void saveOrUpdate(DirRelationPlan dirRelationPlan) {
        dirRelationPlanDao.saveOrUpdate(dirRelationPlan);
    }

    @Override
    public List<DirRelationPlan> findDirRelationPlanList(List<Long> childDirIds) {
        if (CollectionUtils.isEmpty(childDirIds)) {
            throw new BusinessException("文件夹主键不能为空!");
        }
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.must(QueryBuilders.termsQuery("dirId", childDirIds));
        return dirRelationPlanDao.getListByQuery(queryBuilder);
    }

    @Override
    public List<DirRelationPlan> findUserPublishList(String loginCode) {
        if (StringUtils.isEmpty(loginCode)) {
            throw new BusinessException("用户信息不能为空");
        }
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.must(QueryBuilders.termQuery("ownerCode", loginCode));
        return dirRelationPlanDao.getListByQuery(queryBuilder);
    }

    @Override
    public void deleteDirRelationPlan(Long planId) {
        if (planId == null) {
            throw new BusinessException("参数不能为空");
        }
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.must(QueryBuilders.termQuery("planId", planId));
        dirRelationPlanDao.deleteByQuery(queryBuilder, true);
    }
}
