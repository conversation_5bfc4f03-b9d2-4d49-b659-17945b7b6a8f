package com.uino.service.sys.microservice;

import java.util.Collection;
import java.util.List;

import com.uino.bean.sys.base.NotifyChannel;
import com.uino.bean.sys.business.NotifyChannelReqDto;
import com.uino.bean.sys.business.NotifyData;

/**
 * 通知渠道服务
 * 
 * <AUTHOR>
 *
 */
public interface INotifyChannelSvc {

    /**
     * 保存
     * 
     * @param saveInfo
     * @return
     */
    public NotifyChannel save(NotifyChannel saveInfo);

    /**
     * 根据ids删除
     * 
     * @param ids
     */
    public void delete(Collection<Long> ids);

    /**
     * 查询
     * 
     * @param searchDto
     * @return
     */
    public List<NotifyChannel> search(NotifyChannelReqDto searchDto);

    /**
     * 根据id查询渠道信息
     * 
     * @param id
     * @return
     */
    public NotifyChannel getNotifyChannelById(Long id);

    /**
     * 发送通知
     * 
     * @param notifyData
     * @return
     */
    public boolean sendNotify(NotifyData notifyData);
}
