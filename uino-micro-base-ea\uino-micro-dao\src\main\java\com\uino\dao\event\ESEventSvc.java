package com.uino.dao.event;

import com.alibaba.fastjson.JSONObject;
import com.uino.dao.AbstractESBaseDao;
import com.uino.dao.ESConst;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.admin.indices.get.GetIndexRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.stereotype.Repository;

@Slf4j
@Repository
public class ESEventSvc extends AbstractESBaseDao<JSONObject, JSONObject> {
    @Override
    public String getIndex() {
        return ESConst.INDEX_MONITOR_MON_EAP_EVENT;
    }

    @Override
    public String getType() {
        return ESConst.INDEX_MONITOR_MON_EAP_EVENT;
    }

    @PostConstruct
    public void init(){
        try {
            RestHighLevelClient clientc = getClient();
            GetIndexRequest getRequest = new GetIndexRequest();
            getRequest.indices(getFullIndexName());
            boolean existIndex = clientc.indices().exists(getRequest, RequestOptions.DEFAULT);
            if (!existIndex) {
               super.initIndex();
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }
}
