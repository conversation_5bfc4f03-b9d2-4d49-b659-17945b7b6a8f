package com.uinnova.product.eam.model.vo;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.comm.model.es.AppSquareConfig;
import com.uinnova.product.eam.comm.model.es.AssetWarehouseDir;
import com.uinnova.product.eam.comm.model.es.EamCategory;
import com.uino.bean.cmdb.base.ESCIClassInfo;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.permission.base.SysModule;
import com.uino.bean.permission.base.SysUser;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Data
@Builder
public class GlobalSearchCategoryFillParam {

    @Comment("私有库目录")
    private Map<Long, EamCategory> privateCategoryMap;
    @Comment("资产库目录")
    private Map<Long, EamCategory> designCategoryMap;
    @Comment("与我协作视图id")
    private List<Long> shareDiagramIds;
    @Comment("与我协作方案id")
    private List<Long> sharePlanIds;
    @Comment("资产库顶级目录菜单列表映射")
    private Map<Long, List<AssetWarehouseDir>> libraryModuleMap;
    @Comment("资产仓库菜单")
    private SysModule designModule;
    @Comment("用户数据")
    private Map<String, SysUser> userInfoMap;
    @Comment("资产配置所有分类信息")
    private Map<Long, ESCIClassInfo> ciClassInfoMap;
    @Comment("资产卡片")
    private Map<String, AppSquareConfig> appSquareConfigMap;
    @Comment("资产-私有库ci信息")
    private Map<String, ESCIInfo> privateCiInfoMap;
    @Comment("资产-资产库ci信息")
    private Map<String, ESCIInfo> designCiInfoMap;
    @Comment("资产-分类属性label字段")
    private Map<Long, List<String>> classDefWithLabels;
}

