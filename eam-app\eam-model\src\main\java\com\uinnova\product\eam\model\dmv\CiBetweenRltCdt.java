package com.uinnova.product.eam.model.dmv;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("CI之间关系查询")
public class CiBetweenRltCdt {

    @ApiModelProperty("CI列表,查询这些CI之间的关系")
    private Long[] ids;

    @ApiModelProperty("CI代码,优先级高于ids,查询这些CI之间的关系")
    private String[] ciCodes;

    @ApiModelProperty("只查询该关系分类ID下的数据")
    private Long[] classIds;

    @ApiModelProperty("查询类型,1ci与ci之间的关系，2有关属性的")
    private Integer[] types;

    /**
     * 关系的查询深度的条件
     */
    @ApiModelProperty("关系的查询深度的条件 [CI_RLt_ATTR,CI_RLt_CI,CI_RLt_CI_Attr,CI_RLt_CI_CLASS,CI_RLt_CI_CLASS_ATTR_DEF]")
    private VcCiRltQ[] ciRltQs;

}
