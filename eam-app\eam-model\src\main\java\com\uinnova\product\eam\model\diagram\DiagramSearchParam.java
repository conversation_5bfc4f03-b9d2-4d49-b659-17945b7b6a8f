package com.uinnova.product.eam.model.diagram;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

@Data
public class DiagramSearchParam {

    @Comment("模糊查询字段")
    private String word ;

    @Comment("所属领域：流程建模=10，组件建模=100.其他=1，应用架构=11，数据架构=101，技术架构=111")
    private Integer dirType;

    @Comment("所属用户：我创建的=1，与我协作=2，其他=3")
    private Integer userFlag;

    @Comment("发布与否：未发布=0，已发布=1")
    private Integer isOpen;
}
