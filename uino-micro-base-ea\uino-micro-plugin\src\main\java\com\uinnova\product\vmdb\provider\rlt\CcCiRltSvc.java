package com.uinnova.product.vmdb.provider.rlt;

import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.comm.model.es.ESCiRltInfo;
import com.uinnova.product.vmdb.comm.model.rlt.CCcCiRlt;
import com.uinnova.product.vmdb.comm.model.rlt.CCcCiRltRule;
import com.uinnova.product.vmdb.comm.model.rlt.CcCiRlt;
import com.uinnova.product.vmdb.comm.model.rlt.CcCiRltRule;
import com.uinnova.product.vmdb.comm.util.SaveType;
import com.uinnova.product.vmdb.provider.ci.bean.DetailedResult;
import com.uinnova.product.vmdb.provider.rlt.bean.CcCiRltInfo;
import com.uinnova.product.vmdb.provider.rlt.bean.CiFriendInfo;
import com.uinnova.product.vmdb.provider.rlt.bean.CiRltQ;
import com.uinnova.product.vmdb.provider.rlt.bean.CiRltRecord;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface CcCiRltSvc {

	/**
	 * 通过ID查询关系.
	 * 
	 * @param domainId 所属域
	 * @param id       关系ID
	 * @return 关系基本信息.
	 */
	public CcCiRlt queryById(Long domainId, Long id);

	/**
	 * 条件查询关系
	 * 
	 * @param domainId 所属域
	 * @param cdt      查询条件
	 * @param orders   排序
	 * @return 关系基本信息.
	 */
	public List<CcCiRlt> queryList(Long domainId, CCcCiRlt cdt, String orders);

	/**
	 * 带分页的查询关系
	 * 
	 * @param domainId 所属域
	 * @param pageNum  起始(1开始)
	 * @param pageSize 每一页的大小
	 * @param cdt      查询条件
	 * @param orders   排序
	 * @return 关系基本信息.
	 */
	public List<CcCiRlt> queryPageList(Long domainId, Integer pageNum, Integer pageSize, CCcCiRlt cdt, String orders);

	/**
	 * 带分页的查询关系
	 * 
	 * @param domainId 所属域
	 * @param pageNum  起始(1开始)
	 * @param pageSize 每一页的大小
	 * @param cdt      查询条件
	 * @param orders   排序
	 * @return 关系基本信息.
	 */
	public Page<CcCiRlt> queryPage(Long domainId, Integer pageNum, Integer pageSize, CCcCiRlt cdt, String orders);

	/**
	 * 
	 * @param domainId 所属域
	 * @param cdt      查询条件
	 * @param ciIdxExp
	 * @param tagIds
	 * @return
	 */
	public List<CcCiRlt> queryRltByIndex(Long domainId, CCcCiRlt cdt, String ciIdxExp, Long[] tagIds);

	/**
	 * 通过index查询关系
	 * 
	 * @param domainId 所属域
	 * @param pageNum
	 * @param pageSize
	 * @param cdt      查询条件
	 * @param ciIdxExp
	 * @param ciRltQs
	 * @return
	 */
	public Page<CcCiRltInfo> queryRltPageByIndex(Long domainId, Integer pageNum, Integer pageSize, String like,
			CCcCiRlt cdt, String ciIdxExp, CiRltQ[] ciRltQs);

	/**
	 * 通过ID查询关系.
	 * 
	 * @param domainId 所属域
	 * @param id       关系ID
	 * @param ciRltQs  关系的查询深度的条件
	 * @return 关系详情信息.
	 */
	public CcCiRltInfo queryRltInfoById(Long domainId, Long id, CiRltQ[] ciRltQs);

	/**
	 * 查询
	 * 
	 * @param domainId 所属域
	 * @param cdt      查询条件
	 * @param orders   排序
	 * @param ciRltQs  关系的查询深度的条件
	 * @return 关系的详细信息
	 */
	public List<CcCiRltInfo> queryRltInfoList(Long domainId, CCcCiRlt cdt, String orders, CiRltQ[] ciRltQs);

	/**
	 * 分页的查询
	 * 
	 * @param domainId 所属域
	 * @param pageNum  起始(1开始)
	 * @param pageSize 每一页的大小
	 * @param cdt      查询条件
	 * @param orders   排序
	 * @param ciRltQs  关系的查询深度的条件
	 * @return 关系的详细信息
	 */
	public List<CcCiRltInfo> queryRltInfoPageList(Long domainId, Integer pageNum, Integer pageSize, CCcCiRlt cdt,
			String orders, CiRltQ[] ciRltQs);

	/**
	 * 分页的查询
	 * 
	 * @param domainId 所属域
	 * @param pageNum  起始(1开始)
	 * @param pageSize 每一页的大小
	 * @param cdt      查询条件
	 * @param orders   排序
	 * @param ciRltQs  关系的查询深度的条件
	 * @return 关系的详细信息
	 */
	public Page<CcCiRltInfo> queryRltInfoPage(Long domainId, Integer pageNum, Integer pageSize, CCcCiRlt cdt,
			String orders, CiRltQ[] ciRltQs);

	/**
	 * 通过
	 * 
	 * @param domainId
	 * @param cdt
	 * @param orders
	 * @param ciRltQs
	 * @return
	 */
	public List<CcCiRltInfo> queryCiRltInfoListByCodes(Long domainId, String[] codes, CCcCiRlt cdt, String orders,
			CiRltQ[] ciRltQs);

	/**
	 * 查询朋友圈的数据
	 * 
	 * @param domainId
	 * @param startCiId
	 * @param friendId  朋友圈定义的ID
	 * @return
	 */
	public CiFriendInfo queryCiinfoListByFriend(Long domainId, Long startCiId, Long friendId);

	/**
	 * 无源查询朋友圈的数据
	 * 
	 * @param domainId
	 * @param friendId 朋友圈定义的ID
	 * @return
	 */
	public CiFriendInfo queryNoStartCiInfoListByFriend(Long domainId, Long friendId);

	/**
	 * 查询一个CI的上下层关系.
	 * 
	 * @param domainId      所属域
	 * @param startCi       起始点的ID
	 * @param ciRltClassids 关系分类的ids为[]时表示无视关系分类这个条件
	 * @param upLevel       向上查多少层 0表示不往上查
	 * @param downLevel     向下查多少层 0表示不往下查
	 * @return 相关的关系,但是没有层级信息.
	 */
	public CiFriendInfo queryCiUpDowmFriend(Long domainId, Long sCiId, Long[] ciRltClassids, Integer upLevel,
			Integer downLevel);

	/**
	 * 查询一个CI的上下层关系.
	 * 
	 * @param domainId      所属域
	 * @param startCi       起始点的ID
	 * @param ciRltClassids 关系分类的ids为[]时表示无视关系分类这个条件
	 * @param upLevel       向上查多少层 0表示不往上查
	 * @param downLevel     向下查多少层 0表示不往下查
	 * @param ciRltQs       关系的查询深度的条件
	 * @return 相关的关系,但是没有层级信息.
	 */
	public List<CcCiRltInfo> queryCiUpDownRlt(Long domainId, Long startCi, Long[] ciRltClassids, Integer upLevel,
			Integer downLevel, CiRltQ[] ciRltQs);

	/**
	 * 查询一个CI的上下层关系.
	 * 
	 * @param domainId      所属域
	 * @param sCiId         起始点的ID
	 * @param ciClassIds    ci分类ID
	 * @param ciRltClassids 关系分类的ids为[]时表示无视关系分类这个条件
	 * @param upLevel       向上查多少层 0表示不往上查
	 * @param downLevel     向下查多少层 0表示不往下查
	 * @param ciRltQs       关系的查询深度的条件
	 * @return 相关的关系,但是没有层级信息.
	 */
	public List<CcCiRltInfo> queryCiUpDownRltHasCiClassIds(Long domainId, Long sCiId, Long[] ciClassIds,
			Long[] ciRltClassids, Integer upLevel, Integer downLevel, CiRltQ[] ciRltQs);

	/**
	 * 查询一堆CI之间的关系.有关系的返回,没关系的不管
	 * 
	 * @param domainId 所属域
	 * @param ciIds    被查询的CIIds
	 * @param ciRltQs  关系的查询深度的条件
	 * @return 相关的关系,
	 */
	public List<CcCiRltInfo> queryCiBetweenRlt(Long domainId, Long[] ciIds, CiRltQ[] ciRltQs);

	/**
	 * 不分页查询自动构建关系数据的规则信息
	 * 
	 * @param domainId 所属域
	 * @param cdt      查询条件
	 * @param orders   排序字段
	 * @return 列表结果
	 */
	public List<CcCiRltRule> queryCiRltRuleList(Long domainId, CCcCiRltRule cdt, String orders);

	/**
	 * 查询指定属性的值等于val的CI关系数据(分页是为了防止数据过大)
	 * 
	 * @param domainId
	 * @param pageNum
	 * @param pageSize
	 * @param ciType   CI类型
	 * @param attrIds  属性IDs
	 * @param val      属性值
	 * @return
	 */
    @Deprecated
    public Page<CcCiRlt> queryCiRltPageByAttrVal(Long domainId, Integer pageNum, Integer pageSize, Integer ciType, Long[] attrIds, String val);

	/**
	 * 通过CI分类的属性进行批量建立关系
	 * 
	 * @param domainId       所属域
	 * @param ciRltClassId   关系的分类
	 * @param startCiClassId 源CI分类
	 * @param startAttrId    进行匹配的属性
	 * @param endCiClassId   目标分类
	 * @param endAttrId      进行匹配的属性
	 * @param owerId         关系所属
	 */
	public void buildCiRelation(Long domainId, Long ciRltClassId, Long startCiClassId, Long startAttrId,
			Long endCiClassId, Long endAttrId, Long owerId);

	/**
	 * 保存一条关系.
	 * 
	 * @param domainId 所属域
	 * @param ciRlt    关系
	 * @param attrs    属性值
	 */
	public Long saveCiRlt(Long domainId, CcCiRlt ciRlt, Map<String, String> attrs);

	/**
	 * 批量保存关系数据
	 * 
	 * @param domainId
	 * @param rltClsId
	 * @param records
	 * @return [0]是保存的数量[1]是更新的数量[2]是忽略的数量[3]是Excel中已存在相同数据的关系的数量[10]开始是源对象或目标对象不存在的index接着是表格中已存在数据的index
	 */
	public Integer[] saveOrUpdateBatch(Long domainId, Long rltClsId, List<CiRltRecord> records);
	
	public DetailedResult saveOrUpdateBatchWithDetailError(Long domainId, Long rltClsId, List<CiRltRecord> records);
	
	/**
	 * 批量保存关系数据
	 * @param domainId
	 * @param rltClsId
	 * @param sourceId
	 * @param records
	 * @return 导入结果存储文件路径
	 */
    public DetailedResult saveOrUpdateBatchFromDix(Long domainId, Long rltClsId, Long sourceId, List<CiRltRecord> records);

	/**
	 * 批量保存关系数据(带显示事务)
	 * 
	 * @param domainId
	 * @param rltClsId
	 * @param records
	 * @return [0]是保存的数量[1]是更新的数量
	 */
	public Integer[] saveOrUpdateBatchTransaction(Long domainId, Long rltClsId, List<CiRltRecord> records);

	/**
	 * 根据条件规则集合自动构建关系数据
	 * 
	 * @param domainId
	 * @param records
	 * @return 保存结果,数组下标为0表示保存数量,1表示更新数量
	 */
	public Integer[] autosaveOrUpdateBatchByRules(Long domainId, List<CcCiRltRule> records);

	/**
	 * 批量保存或者更新自动构建关系数据的规则(没有传规则集合则会删调当前关系的规则)
	 * 
	 * @param domainId
	 * @param classRltId 分类关系线ID
	 * @param rltClassId 关系分类ID
	 * @param records    保存规则对象集合
	 * @return 新增主键序列
	 */
	public Long[] saveOrUpdateRltRulesBatch(Long domainId, Long classRltId, Long rltClassId, List<CcCiRltRule> records);

	/**
	 * 创建一条关系
	 * 
	 * @param domainId  所属域
	 * @param startCiId 源CI
	 * @param endCiId   目标CI
	 * @param attrs     属性值
	 * @return 关系的ID
	 */
	public Long createCiRlt(Long domainId, Long ciRltClassId, Long startCiId, Long endCiId, Map<String, String> attrs);

	/**
	 * 更新一个条关系
	 * 
	 * @param domainId 所属域
	 * @param id       关系的ID
	 * @param ciRlt    关系,可以不传,不传只更新属性.
	 * @param attrs    关系的属性
	 * @param saveType 保存的方式
	 * @return 关系的ID
	 */
	public Long updateCiRltAttr(Long domainId, Long id, CcCiRlt ciRlt, Map<String, String> attrs, SaveType saveType);

	/**
	 * 更新一个条关系
	 * 
	 * @param domainId
	 * @param id
	 * @param ciRlt
	 * @param attrs
	 * @param saveType
	 * @param newCodeAttrDefs 使用传入的子code属性定义对象的orderNo来决定子code顺序
	 * @return
	 */
	public Long updateCiRltAttrWithSubCodeAttrDefs(Long domainId, Long id, CcCiRlt ciRlt, Map<String, String> attrs,
			SaveType saveType, List<CcCiAttrDef> newCodeAttrDefs, List<ESCiRltInfo> esCiRltInfoList);

	/**
	 * 通过关系ID删除一条关系
	 * 
	 * @param domainId 所属域
	 * @param id       关系ID
	 * @return 0表示删除失败
	 */
	public Integer removeById(Long domainId, Long id);

	/**
	 * 通过关系ID删除一条关系
	 * 
	 * @param domainId 所属域
	 * @param ids      关系ID
	 * @return 0表示删除失败
	 */
	public Integer removeByIds(Long domainId, Long[] ids);

	/**
	 * 通过关系分类ID删除数据
	 * 
	 * @param domainId 所属域
	 * @param classId  关系分类ID
	 * @return 0表示失败1成功
	 */
	public Integer removeByClassId(Long domainId, Long classId);

	/**
	 * 通过CDT删除一组关系
	 * 
	 * @param domainId 所属域
	 * @param cdt      匹配条件
	 * @return 删除的信息
	 */
	public Integer removeByCdt(Long domainId, CCcCiRlt cdt);

	/**
	 * 通过CDT删除自动构建关系规则
	 * 
	 * @param domainId 所属域
	 * @param cdt      匹配条件
	 * @return 删除的信息
	 */
	public Integer removeRltRuleByCdt(Long domainId, CCcCiRltRule cdt);

	/**
	 * 通过cdt查询关系数量
	 * 
	 * @param domainId
	 * @param cdt
	 * @return
	 */
	long queryCount(Long domainId, CCcCiRlt cdt);

	/**
	 * 根据条件获取满足关系条件的源和目标下CI数量(不分关系双向去重查询)
	 * 
	 * @param domainId
	 * @param rltClassId 关系分类ID(可选)
	 * @param ciId       源CI-ID(对应目标分类)或者目标CI-ID(对应源分类)
	 * @param classId    源分类ID(对应目标CI)或者目标分类ID(对应源CI)
	 * @return 源和目标下CI数量
	 */
	public Long querySourceAndTargetCiCount(Long domainId, Long rltClassId, Long ciId, Long classId);

//	/**
//	 * 更新一个关系分类下的数据,用于刷新二级关系的子code
//	 *
//	 * @param domainId
//	 * @param classId
//	 */
//	public void updateRltSubCodeByClassId(Long domainId, Long classId, List<CcCiAttrDef> newCodeAttrDefs);
	
	
	/**
	 * 更新一个关系分类下的数据,用于刷新二级关系的子code
	 * @param domainId
	 * @param classId
	 * @param newCodeAttrDefs
	 */
    public void updateRltSubCodeByClassId(Long domainId, Long classId, List<CcCiAttrDef> newCodeAttrDefs);

	/**
	 * 批量保存关系数据(校验必须全部通过再入库,一个不过全军覆没)
	 * 
	 * @param domainId 数据域
	 * @param rltClsId 关系分类ID
	 * @param records  保存对象集合
	 * @return 保存明细
	 */
	public DetailedResult saveOrUpdateBatchFull(Long domainId, Long rltClsId, List<CiRltRecord> records);

	/**
	 * 批量保存CI对象并生成错误结果明细<br/>
	 * (供后台使用或者接口调用)<br/>
	 * (明细结果来源为后台保存,文件存放目录为后台保存文件的目录)
	 * 
	 * @param domainId 数据域
	 * @param rltClsId 关系分类ID
	 * @param records  保存对象集合
	 * @return 保存明细(含明细文件地址)
	 */
	public DetailedResult saveOrUpdateCiBatchGenerate(Long domainId, Long rltClsId, List<CiRltRecord> records);

	/**
	 * 根据关系分类ID和属性值创建关系子Code
	 * 
	 * @param domainId   数据域(必传)
	 * @param rltClassId 关系分类ID(必传)
	 * @param attrs      关系属性值
	 * @param attrDefs   关系分类属相定义(可不传,为空则会再查询一次)
	 * @return 有子Code返回,没有则返回空字符串
	 */
	public String createSubCode(Long domainId, Long rltClassId, Map<String, String> attrs, List<CcCiAttrDef> attrDefs);
	
	/**
	 * 更新关系modifyTime
	 * 
	 * @param domainId   数据域(必传)
	 * @param ids        关系IDs(必传)
	 * @return void
	 */
	public void updateModifyTimeByIds(Long domainId, Set<Long> ids);


	/**
	 * 5.7.0升级5.8.0刷CI属性专用
	 *
	 * @return 刷新数量
	 */
	public Integer refreshRltAttrs();
}
