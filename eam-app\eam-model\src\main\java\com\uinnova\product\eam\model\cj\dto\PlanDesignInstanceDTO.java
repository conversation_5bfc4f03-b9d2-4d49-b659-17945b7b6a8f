package com.uinnova.product.eam.model.cj.dto;


import lombok.Data;

import java.util.List;

/**
 * 方案设计实例 EntityBean、Condition
 * <AUTHOR>
 */

@Data
public class PlanDesignInstanceDTO extends BaseEntityDTO {

    /**
     * 主键数组
     */
    private Long[] ids;

    /**
     * 业务主键
     */
    private Long businessId;

    /**
     * 方案设计名称
     */
    private String name;

    /**
     * 方案设计说明
     */
    private String explain;

    /**
     * 方案设计模板businessId
     */
    private Long templateId;

    /**
     * 版本
     */
    private Integer version;

    /**
     * 是否是最新版本
     */
    private Boolean isCurrentVersion;

    /**
     * <p>状态</p>
     * <p>deleted：已删除</p>
     * <p>published：已发布</p>
     * <p>draft：草稿</p>
     */
    private String status;

    /**
     * 方案类型
     */
    private Long typeId;

    /**
     * 默认系统的ciCode
     */
    private String defaultSystemCiCode;

    /**
     * 默认系统的classId
     */
    private Long defaultSystemClassId;

    /**
     * 主办系统id集合
     */
    private List<String> ciCodeList;

    /**
     * 方案所属文件夹
     */
    private Long dirId;

    /**
     * 可回收状态
     */
    private Boolean recyclable;

    /**
     * 是否在流程审批中
     */
    private boolean processApproval;

    /**
     * 备份字段,便于删除后的恢复
     */
    private PlanDesignInstanceDTO temp;

    /**
     * 发布位置
     */
    private Long assetsDirId;

    /**
     * 发布位置中文路径
     */
    private String echoDirName;

    /**
     * 区分方案所属领域
     */
    private Integer dirType;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 资产类型：1:设计库 2:资产库
     */
    private Integer assetsType;

    /**
     * 业务主键，替换使用
     */
    private String businessKey;

    /**
     * 检出关联方案id
     */
    private Long detectionPlanId;
}
