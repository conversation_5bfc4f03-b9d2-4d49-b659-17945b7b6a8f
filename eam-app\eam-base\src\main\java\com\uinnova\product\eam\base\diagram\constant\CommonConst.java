package com.uinnova.product.eam.base.diagram.constant;

import com.binary.framework.bean.annotation.Comment;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 *  常量类
 * <AUTHOR>
 * @version 1.0
 * @date 2021-11-26-10:47
 */
public class CommonConst {

    @Comment("图片默认存储路径")
    public static final String DEFAULT_USER_ICON = "/122/defaultIcon/default_account_photo.png";
    public static final String DEFAULT_USER_NAME = "默认用户名";
    public static final String SSO_Auth = "SSO-Authorization";
    public static final String ACC_TOKEN = "AccToken";
    public static final String APP_NAME = "appName";
    public static final String AUTH_FAIL_MSG = "登录凭证已过期，请重新登录";
    public static final String TOKEN_STATUS_PREFIX = "STATUS_";
    public static final String TOKEN_VAL_PREFIX = "VAL_";
    public static final String SLASH = "/";
    public static final String EMPTY = "";
    public static final String DEFAULT_SUCCESS_MSG = "操作成功";
    public static final String DELETE = "DELETE";
    public static final String ADD = "ADD";
    public static final Integer STATUS_UNDELETED = 1;
    public static final Integer DEFAULT_MAP_SIZE = 16;
    public static final Integer MAX_QUERY_COUNT = 3000;
    //画布内查询
    public static final int QUERY_TYPE_SKETCH = 1;

    public static final List<Integer> ALL_DIAGRAM_TYPE =  Collections.unmodifiableList(Arrays.asList(1, 2, 3, 4, 6));

    @Comment("本地token存储时间，默认3小时")
    public static final long LOCAL_TOKEN_EXPIRE_TIME = 60 * 60 * 3;

    @Comment("本地token刷新间隔，每隔半小时刷新一次")
    public static final long LOCAL_TOKEN_REFRESH_TIME = 60 * 160;

    @Comment("请求转发日志记录过期时间，默认一周")
    public static final long QUEST_BY_URL_EXPIRE_TIME = 60 * 60 * 24 * 7;
}
