package com.uinnova.product.vmdb.comm.cucumber;

import com.binary.core.encrypt.Encrypt;
import com.binary.core.http.HttpClient;
import com.binary.core.http.HttpUtils;
import com.binary.core.io.Resource;
import com.binary.core.io.ResourceResolver;
import com.binary.core.util.BinaryUtils;
import com.binary.core.util.Properties;
import com.binary.framework.exception.ServiceException;
import com.binary.framework.util.ControllerUtils;
import com.uinnova.product.vmdb.comm.i18n.MessageUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * 
 * <AUTHOR>
 *
 */
public abstract class TarsierCucumber {
    private static final Logger logger = LoggerFactory.getLogger(TarsierCucumber.class);

    private static class DefaultTarsierCucumber extends TarsierCucumber {
    }

    public static final String PROJECT_HTTP_ROOT_NAME = "project.http.root";
    public static final String PROJECT_SSO_ROOT_NAME = "project.sso.root";
    public static final String PROJECT_LOGIN_USER_NAME = "project.login.user";
    public static final String PROJECT_LOGIN_PWD_NAME = "project.login.pwd";
    public static final String PROJECT_PWD_ENCRYPTED_NAME = "project.pwd.encrypted";

    private static final Object syncobj = new Object();
    private static TarsierCucumber instance;

    private Properties properties;

    private HttpClient httpClient;

    protected TarsierCucumber() {
        this(null);
    }

    protected TarsierCucumber(String config) {
        init(config);
    }

    private void init(String config) {
        synchronized (syncobj) {
            if (BinaryUtils.isEmpty(config)) {
                config = "classpath:cucumber/cucumber.properties";
            }

            if (instance != null) {
                throw new ServiceException(" The project has been initialized! ");
            }

            logger.info(" cucumber initializing properties '" + config + "' ... ");
            Resource res = ResourceResolver.getResource(config);
            Properties props = new Properties(res);

            String httpRoot = props.get(PROJECT_HTTP_ROOT_NAME);
            String ssoRoot = props.get(PROJECT_SSO_ROOT_NAME);
            String user = props.get(PROJECT_LOGIN_USER_NAME);
            String pwd = props.get(PROJECT_LOGIN_PWD_NAME);
            boolean encrypted = "true".equalsIgnoreCase(props.get(PROJECT_PWD_ENCRYPTED_NAME));

            MessageUtil.checkEmpty(httpRoot, "properties:project.http.root");
            MessageUtil.checkEmpty(ssoRoot, "properties:project.sso.root");
            MessageUtil.checkEmpty(user, "properties:project.login.user");
            MessageUtil.checkEmpty(pwd, "properties:project.login.pwd");

            String token = login(ssoRoot, user, pwd, encrypted);

            httpRoot = HttpUtils.formatContextPath(httpRoot).substring(1);
            HttpClient httpClient = HttpClient.getInstance(httpRoot);
            httpClient.addRequestProperty("REQUEST_HEADER", "binary-http-client-header");
            httpClient.addRequestProperty("token", token);

            this.properties = props;
            // this.token = token;
            this.httpClient = httpClient;
            instance = this;
        }
    }

    private String login(String ssoRoot, String user, String pwd, boolean encrypted) {
        logger.info(" cucumber do login '" + ssoRoot + "' ... ");
        char c = '|';
        if ((user = user.trim()).indexOf(c) < 0) {
            user = "uinnova|" + user;
        }
        if (!encrypted) {
            pwd = Encrypt.encodeBase64(pwd);
        }

        ssoRoot = HttpUtils.formatContextPath(ssoRoot).substring(1);
        HttpClient client = HttpClient.getInstance(ssoRoot);
        client.addRequestProperty("REQUEST_HEADER", "binary-http-client-header");

        Map<String, Object> form = new HashMap<String, Object>();
        form.put("loginCode", user);
        form.put("password", pwd);

        String s = client.request("/external/operation/loginToken", form);
        String token = ControllerUtils.toRemoteJsonObject(s, String.class);

        if (BinaryUtils.isEmpty(token)) {
            throw new ServiceException(" cucumber login failed! ");
        }

        return token;
    }

    private static TarsierCucumber getInstance() {
        if (instance == null) {
            synchronized (syncobj) {
                if (instance == null) {
                    instance = new DefaultTarsierCucumber();
                }
            }
        }
        return instance;
    }

    /**
     * 获取配置属性
     * 
     * @param name
     * @return
     */
    public static String getProperty(String name) {
        return getInstance().properties.get(name);
    }

    /**
     * rest请求
     * 
     * @param path
     * @param jsonString
     * @param resultType
     * @return
     */
    public static <T> T rest(String path, String jsonString, Class<T> resultType) {
        String s = getInstance().httpClient.rest(path, jsonString);
        return ControllerUtils.toRemoteJsonObject(s, resultType);
    }

    /**
     * http请求
     * 
     * @param path
     * @param form
     *            表单
     * @param resultType
     * @return
     */
    public static <T> T request(String path, Map<String, Object> form, Class<T> resultType) {
        String s = getInstance().httpClient.request(path, form);
        return ControllerUtils.toRemoteJsonObject(s, resultType);
    }

}
