package com.uino.provider.feign.monitor;

import java.util.Collection;
import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import com.binary.core.io.Resource;
import com.binary.jdbc.Page;
import com.uino.bean.cmdb.business.ImportResultMessage;
import com.uino.bean.cmdb.business.ImportSheetMessage;
import com.uino.bean.monitor.base.ESKpiInfo;
import com.uino.bean.monitor.buiness.KpiRltBindDto;
import com.uino.bean.monitor.buiness.SearchKpiBean;
import com.uino.provider.feign.config.BaseFeignConfig;

@Component
@FeignClient(name = "${feign-server-name:tarsier-platform}", path = "${feign-server-path:tarsier-platform}/feign/kpi", configuration = {BaseFeignConfig.class})
public interface KpiFeign {

    /**
     * 根据id获取指标数据
     * 
     * @param id
     * @return
     */
    @PostMapping("getKpiInfoById")
    public ESKpiInfo getKpiInfoById(@RequestBody Long id);

    /**
     * 根据id获取指标数据
     * 
     * @param id
     * @return
     */
    @PostMapping("getKpiInfoByIds")
    public List<ESKpiInfo> getKpiInfoByIds(@RequestBody Collection<Long> ids);

    /**
     * 根据kpiSearchDto查询kpi分页列表
     * 
     * @param searchDto
     * @return
     */
    @PostMapping("queryKpiInfoPage")
    public Page<ESKpiInfo> queryKpiInfoPage(@RequestBody(required = false) SearchKpiBean searchDto);

    /**
     * 持久化kpi
     * 
     * @param saveDto
     * @return
     */
    @PostMapping("saveOrUpdate")
    public Long saveOrUpdate(@RequestBody ESKpiInfo saveDto);

    /**
     * 根据kpiIds删除kpi相关信息
     * 
     * @param kpiIds
     */
    @PostMapping("deleteByKpiIds")
    public void deleteByKpiIds(@RequestBody Collection<Long> kpiIds);

    /**
     * 导出指标数据
     * 
     * @param isTpl
     * @return
     */
    @PostMapping("exportKpiInfos")
    public Resource exportKpiInfos(@RequestParam(value = "domainId")Long domainId,@RequestBody(required = false) Boolean isTpl);

    /**
     * 导入指标数据
     * 
     * @param file
     * @return
     */
    @PostMapping(value = "importKpiInfos", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ImportResultMessage importKpiInfos(@RequestParam(value = "domainId")Long domainId, @RequestPart(value = "file") MultipartFile file);

    /**
     * 绑定分类与KPI关联关系
     * 
     * @param dto
     */
    @PostMapping("bindCiClassRltToKpiInfo")
    public void bindCiClassRltToKpiInfo(@RequestBody KpiRltBindDto dto);

    /**
     * 解除分类与KPI关联关系
     * 
     * @param dto
     */
    @PostMapping("delCiClassRltToKpiInfo")
    public void delCiClassRltToKpiInfo(@RequestBody KpiRltBindDto dto);

	/**
	 * 批量保存指标信息
	 * 
	 * @description
	 * @author: ZMJ
	 * @param kpiInfos
	 * @return
	 * @example
	 */
	@PostMapping("saveOrUpdateBatch")
	public ImportSheetMessage saveOrUpdateBatch(@RequestParam(value = "domainId")Long domainId, @RequestBody List<ESKpiInfo> kpiInfos);

}
