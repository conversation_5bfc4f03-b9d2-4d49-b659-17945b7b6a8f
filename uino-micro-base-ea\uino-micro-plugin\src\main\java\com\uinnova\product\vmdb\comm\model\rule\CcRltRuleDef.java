package com.uinnova.product.vmdb.comm.model.rule;

import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;

/**
 * 
 * <AUTHOR>
 *
 */
@Comment("关系规则定义表[CC_RLT_RULE_DEF]")
public class CcRltRuleDef implements EntityBean {
    private static final long serialVersionUID = 1L;

    @Comment("ID[ID]")
    private Long id;

    @Comment("定义类型[DEF_TYPE]    定义类型:1=朋友圈")
    private Integer defType;

    @Comment("定义代码[DEF_CODE]")
    private String defCode;

    @Comment("定义名称[DEF_NAME]")
    private String defName;

    @Comment("参照分类[CLASS_ID]")
    private Long classId;

    @Comment("页面节点ID[PAGE_NODE_ID]")
    private Long pageNodeId;

    @Comment("定义描述[DEF_DESC]")
    private String defDesc;

    @Comment("SVG图路径[SVG_URL]")
    private String svgUrl;

    @Comment("有效状态[USE_STATUS]    有效状态:0=无效 1=有效")
    private Integer useStatus;

    @Comment("无效说明[VALID_ERR_MSG]")
    private String validErrMsg;

    @Comment("所属域[DOMAIN_ID]")
    private Long domainId;

    @Comment("创建人[CREATOR]")
    private String creator;

    @Comment("修改人[MODIFIER]")
    private String modifier;

    @Comment("数据状态[DATA_STATUS]    数据状态:数据状态：1-正常 0-删除")
    private Integer dataStatus;

    @Comment("创建时间[CREATE_TIME]    创建时间:yyyyMMddHHmmss")
    private Long createTime;

    @Comment("修改时间[MODIFY_TIME]    修改时间:yyyyMMddHHmmss")
    private Long modifyTime;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getDefType() {
        return this.defType;
    }

    public void setDefType(Integer defType) {
        this.defType = defType;
    }

    public String getDefCode() {
        return this.defCode;
    }

    public void setDefCode(String defCode) {
        this.defCode = defCode;
    }

    public String getDefName() {
        return this.defName;
    }

    public void setDefName(String defName) {
        this.defName = defName;
    }

    public Long getClassId() {
        return this.classId;
    }

    public void setClassId(Long classId) {
        this.classId = classId;
    }

    public Long getPageNodeId() {
        return this.pageNodeId;
    }

    public void setPageNodeId(Long pageNodeId) {
        this.pageNodeId = pageNodeId;
    }

    public String getDefDesc() {
        return this.defDesc;
    }

    public void setDefDesc(String defDesc) {
        this.defDesc = defDesc;
    }

    public String getSvgUrl() {
        return this.svgUrl;
    }

    public void setSvgUrl(String svgUrl) {
        this.svgUrl = svgUrl;
    }

    public Integer getUseStatus() {
        return this.useStatus;
    }

    public void setUseStatus(Integer useStatus) {
        this.useStatus = useStatus;
    }

    public String getValidErrMsg() {
        return this.validErrMsg;
    }

    public void setValidErrMsg(String validErrMsg) {
        this.validErrMsg = validErrMsg;
    }

    public Long getDomainId() {
        return this.domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }

    public String getCreator() {
        return this.creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getModifier() {
        return this.modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public Integer getDataStatus() {
        return this.dataStatus;
    }

    public void setDataStatus(Integer dataStatus) {
        this.dataStatus = dataStatus;
    }

    public Long getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getModifyTime() {
        return this.modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }

}
