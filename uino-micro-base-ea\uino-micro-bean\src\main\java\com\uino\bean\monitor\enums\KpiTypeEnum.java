package com.uino.bean.monitor.enums;

public enum KpiTypeEnum {
	NUMBER(1, "数值型"), VARCHAR(2, "字符型"), ENUM(3, "枚举型")
	;

	private Integer type;

	private String name;

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	KpiTypeEnum(Integer type, String name) {
		this.type = type;
		this.name = name;
	}

	public static KpiTypeEnum valueOfCode(String code) {
		if (NUMBER.type.toString().equals(code) || NUMBER.name.equals(code)) {
			return NUMBER;
		} else if (VARCHAR.type.toString().equals(code) || VARCHAR.name.equals(code)) {
			return VARCHAR;
		} else if (ENUM.type.toString().equals(code) || ENUM.name.equals(code)) {
			return ENUM;
		} else {
			return NUMBER;
		}
	}

}
