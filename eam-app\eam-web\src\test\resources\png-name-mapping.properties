Vm0=Visio(2013)|VM
LoadBalance0=Visio(2013)|Switch
Router0=Visio(2013)|Router
App0=Tarsier|Application
App1=Tarsier|AppSystem
Switch0=Visio(2013)|Switch-ATM
MultilayerSwitch0=ai|MultilayerSwitch0
LoadBalance1=Visio(2013)|LoadBalance
Client0=Visio(2013)|\u67DC\u9762\u7EC8\u7AEF
Firewall0=ai|Firewall0
Firewall1=Network and Peripherals|Firewall-1
Firewall2=Tarsier|Firewall-V
Firewall3=Visio(2013)|Firewall
Server0=Visio(2013)|\u670D\u52A1\u5668
DbServer0=Visio(2013)|\u6570\u636E\u5E93\u670D\u52A1\u5668
App2=Tarsier|AppNode
Server1=Visio(2013)|PCServer
Server2=Servers and Monitors|Server
Server3=Visio(2013)|\u7F51\u70B9
MainFrame0=Visio(2013)|\u5927\u578B\u673A
MainFrame1=Cisco Products|Mini-VAX
WinOs0=Tarsier|windows
MainFrame2=Visio(2013)|Mainframe
DbServer1=Servers and Monitors|Database-Server
WebServer0=Servers and Monitors|Web-Server
Vm1=Tarsier|VMX
Firewall4=Visio(2013)|\u9632\u706B\u5899
Firewall5=ai|Firewall
Router1=ai|Router
Switch1=ai|Switch
WebServer1=Visio(2013)|\u5185\u5BB9\u7BA1\u7406\u670D\u52A1\u5668
Server4=Visio(2013)|Servers
Server5=Visio(2013)|\u516C\u94A5-\u79C1\u94A5\u670D\u52A1\u5668
Server6=Visio(2013)|\u7BA1\u7406\u670D\u52A1\u5668
Cloud0=Network and Peripherals|Internet-Cloud-2
App3=Tarsier|vip
Server7=Visio(2013)|\u6587\u4EF6\u670D\u52A1\u5668
Switch2=Visio(2013)|ATM-switch
DbServer2=Tarsier|DBInst
Server8=Tarsier|NDS
Building0=Visio(2013)|Build
MultilayerSwitch1=Tarsier|RouterSwitch2
DiskArray0=Visio(2013)|DiskArray
MultilayerSwitch2=Tarsier|Spine_Border_Leaf
Server9=Tarsier|Cluster2
LoadBalance2=Tarsier|glsb
LoadBalance3=Tarsier|Loadbalance
Switch3=Visio(2013)|\u5DE5\u4F5C\u7EC4\u4EA4\u6362\u673A
Hub0=Visio(2013)|\u5C0F\u578B\u96C6\u7EBF\u5668
App4=Tarsier|PAI
Client1=Servers and Monitors|Desktop-PC-2
Switch4=Tarsier|SANSwitch
WebServer2=Tarsier|WEBInst
Server10=Servers and Monitors|FTP-Server
DbServer3=Visio(2013)|\u5173\u7CFB\u6570\u636E\u5E93
Switch5=Tarsier|ContentSwitch
Client2=Tarsier|cellphone
WebServer3=ai|WebServer3
Switch6=Tarsier|Switch
App5=Tarsier|CCPC
DbServer4=ai|DbServer4
MultilayerSwitch3=Tarsier|RouterSwitch
Switch7=Tarsier|leaf
Firewall6=Network and Peripherals|Firewall-2
Client3=Tarsier|Client1
Server11=Servers and Monitors|Management-Server
Server12=Servers and Monitors|Email-Server
Server13=Servers and Monitors|File-Server
Ips0=Tarsier|IPS-24
Server14=Visio(2013)|Server
Switch8=Visio(2013)|ATM FastGB\u4EE5\u592A\u7F51\u4EA4\u6362\u673A
DbServer5=Tarsier|DBService
DbServer6=ai|DbServer6
MultilayerSwitch4=Tarsier|SwitchC_(2)
Ips1=Tarsier|IPS(2)
DiskArray1=Tarsier|Storage
App6=Tarsier|AppCluster
LinuxOs0=Tarsier|linux
Switch9=Visio(2013)|Switch-Generic
Client4=Tarsier|Production
Building1=Cisco Buildings|Building-2
Switch10=Cisco WAN|PBX(Small)
Gateway0=Cisco Basic|Gateway
Switch11=Tarsier|Universal_Gateway
Switch12=Cisco Switches|Switch-ATM-Fast-GB-EtherSwitch
Gateway1=Visio(2013)|\u7F51\u5173
Server15=Tarsier|Cluster4
App7=Tarsier|NetPoint
App8=Tarsier|Business
Server16=Tarsier|RackServer
Server17=Servers and Monitors|Communication-Server
Server18=Servers and Monitors|Proxy-Server
Server19=Servers and Monitors|Print-Server
Server20=Servers and Monitors|Streaming-Media-Server
MainFrame3=Servers and Monitors|Mainframe
DbServer7=Tarsier|RS6000_DB
Client5=Servers and Monitors|Mac-Computer
Client6=Servers and Monitors|Tablet-Computer
Client7=Tarsier|Client2
Server21=Tarsier|AdminStation
Wireless0=Tarsier|AP
App9=Tarsier|AppInst
Server22=Tarsier|AppServer
DbServer8=Tarsier|BDServer
App10=Tarsier|BizService
Ceph0=Tarsier|CEPH
Client8=Tarsier|Client
Client9=Tarsier|Client3
Cloud1=Tarsier|Cloud(2)
Cloud2=Tarsier|Cloud
App11=Tarsier|Component
Server23=Tarsier|ComputePool
Building2=Tarsier|DataCenter
DbServer9=Tarsier|DB
App12=Tarsier|DeployUnit
DiskArray2=Tarsier|Disk
DiskArray3=Tarsier|DiskArray
DWDM0=Tarsier|DWDM-Filter
Building3=Tarsier|Facility