package com.uinnova.product.eam.service;

import com.uinnova.product.eam.comm.model.es.DiagramApproveRlt;

import java.util.List;

/**
 *  模型审批记录关联实现类
 */
public interface DiagramApproveRltSvc {

    /**
     *  创建当前审批的关联信息
     * @param diagramApproveRlt
     * @return 返回数据为流程中的 businesskey
     */
    Long saveDiagramApproveRlt(DiagramApproveRlt diagramApproveRlt);

    /**
     *  根据ids（businesskey）删除审批的关联信息
     * @param businesskey
     */
    void deleteApproveRlt(Long businesskey);

    /**
     *  修改当前审批关联信息的流程状态(只需要支持审批中的状态修改为驳回中，其他情况直接删除数据)
     * @param businesskey
     * @param flowStatus 流程状态 0=流程结束，1=驳回，2=流程中
     * @return
     */
    Boolean changeApproveRltFlowStatus(Long businesskey, Integer flowStatus);

    /**
     *  批量修改当前审批关联信息的流程状态(只需要支持审批中的状态修改为驳回中，其他情况直接删除数据)
     * @param businesskeys
     * @param flowStatus 流程状态 0=流程结束，1=驳回，2=流程中
     * @return
     */
    Boolean batchChangeApproveRltFlowStatus(List<Long> businesskeys, Integer flowStatus);

    /**
     *  查询审批关联数据
     * @param modelId 模型ID
     * @param ownerCode 提交人标识
     * @param approveRootDirId 提交审批模型顶级目录
     * @param flowStatus 流程状态 0=流程结束，1=驳回，2=流程中
     * @param isSingle 是否为模型单图审批
     * @return
     */
    List<DiagramApproveRlt> queryApproveRltByInfo(Long modelId, String ownerCode, Long approveRootDirId, String approveDiagramId, List<Integer> flowStatus, Boolean isSingle);

    /**
     *  根据ids（businesskey）获取审批的关联信息
     * @param businesskey
     */
    DiagramApproveRlt getApproveRlt(Long businesskey);

    /**
     *  批量获取（businesskeys）审批的关联信息
     * @param businessKeys
     * @return
     */
    List<DiagramApproveRlt> getApproveRlts(List<Long> businessKeys);

}

