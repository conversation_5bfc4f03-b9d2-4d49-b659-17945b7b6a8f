package com.uinnova.product.eam.comm.model.es;

import com.alibaba.fastjson.JSONObject;
import com.binary.core.util.BinaryUtils;
import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.comm.bean.CatalogDto;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Classname BasicOperationSetting
 * @Date 2022/5/25 14:04
 */
@Data
@Comment("基础操作设置配置表[uino_eam_square_config]")
public class AppSquareConfig implements Condition {
    @Comment("基础操作设置主键id")
    private Long id;
    @Comment("卡片名称")
    private String cardName;
    @Comment("地址链接")
    private String addressLink;
    @Comment("所属分类 这个是一个枚举")
    private String classification;
    @Comment("状态 0：不显示 1：显示")
    private Integer status;
    @Comment("是否是初始化数据 0表示是初始化数据 1表示新增数据 ")
    private Integer isInit;
    @Comment("图片信息 这是一个json对象包含图片地址和图片底色")
    private String pictureInfo;
    @Comment("专题分析配置 这也是一个json字符串里面有分析模式和数据集配置")
    private String configure;

    @Comment("排序字段，使用二分法进行排除")
    private Double sort;

    @Comment("分组排序字段")
    private Double groupSort;

    @Comment("卡片类型 1表示直接跳转2表示连接前缀拼接")
    private String cardType;

    // ============ 架构资产新增属性=============================

    /**
     * 改造后对应数据类型的值，dataType=1 classCode存的就是资产分类的值 dataType=2存的就是数据集的值
     */
    @Comment("资产分类ID")
    private String classCode;

    @Comment("数据类型： 1表示资产分类 2表示数据集")
    private String dataType;

    @Comment("资产类型 AssetConfigType")
    private Integer assetType;

    @Comment("资产分类下对象数量")
    private Long ciCount;

    @Comment("展示形式 1:卡片 2: 表单")
    private List<Integer> showLayout;

    @Comment("描述")
    private String describe;
    /**
     * [
     *  {
     *      roleId:111
     *      operation:[1:查看，2：维护]
     *  }
     * ]
     */
    @Comment("角色权限配置")
    private List<JSONObject> rolePermission;

    @Comment("全景视图ID")
    private String diagramId;

    @Comment("领域id")
    private Long domainId;

    // =========== 成熟度评价页=============================
    @Comment("数据维护  0：不显示 1：显示")
    private Integer dataMaintain;

    @Comment("排序字段")
    private String sortField;

    private String creator;

    private String modifier;

    private Long createTime;

    private Long modifyTime;

    private List<CatalogDto> catalogList;
}

