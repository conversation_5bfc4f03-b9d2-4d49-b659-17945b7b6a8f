package com.uinnova.product.eam.feign.workable.entity;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 任务请求实体类
 *
 * <AUTHOR>
 * @since 2022/2/24 14:57
 */
@Data
public class TaskRequest {

    private String taskId;

    /**
     * 审批动作
     */
    private FLOWACTION action;

    /**
     * 跟任务绑定的参数,查询任务时会携带回显
     */
    private Map<String, Object> taskEchoVariables;

    /**
     * 路由使用的参数
     */
    private Map<String, Object> routerVariables;

    /**
     * 子流程用得到数据
     */
    private List<ChildProcessRequest> childProcessRequestList;

    /**
     * 下一个审批人
     */
    private String nextUserIds;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 发布描述
     */
    private String publishDescription;

    /**
     * 将此人添加至过滤列表
     */
    private Boolean filterUser = Boolean.FALSE;
}
