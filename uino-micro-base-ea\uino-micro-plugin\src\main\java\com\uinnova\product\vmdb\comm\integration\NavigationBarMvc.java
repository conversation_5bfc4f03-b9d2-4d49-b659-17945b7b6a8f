package com.uinnova.product.vmdb.comm.integration;

import com.binary.framework.util.ControllerUtils;
import com.binary.json.JSON;
import org.springframework.web.bind.annotation.RequestMapping;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

/**
 *
 * <AUTHOR>
 *
 */
@RequestMapping("/navigationbar")
public class NavigationBarMvc {

    public String navigationBar;

    public String getNavigationBar() {
        return navigationBar;
    }

    public void setNavigationBar(String navigationBar) {
        this.navigationBar = navigationBar;
    }

    @RequestMapping("/projects")
    public void projects(HttpServletRequest request, HttpServletResponse response) {
        Object object = JSON.toObject(navigationBar);
        ControllerUtils.returnJson(request, response, object);
    }

}
