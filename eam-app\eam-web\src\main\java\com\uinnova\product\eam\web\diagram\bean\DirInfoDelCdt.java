package com.uinnova.product.eam.web.diagram.bean;

import java.io.Serializable;

import com.binary.framework.bean.annotation.Comment;

public class DirInfoDelCdt implements Serializable {
	private static final long serialVersionUID = 1L;

	@Comment("目录ID数组")
	private Long[] dirIds;
	
	@Comment("视图ID数组")
	private Long[] diagramIds;

	public Long[] getDirIds() {
		return dirIds;
	}

	public void setDirIds(Long[] dirIds) {
		this.dirIds = dirIds;
	}

	public Long[] getDiagramIds() {
		return diagramIds;
	}

	public void setDiagramIds(Long[] diagramIds) {
		this.diagramIds = diagramIds;
	}

}
