package com.uinnova.product.eam.service.es;

import cn.hutool.core.date.DateUtil;
import com.binary.core.exception.MessageException;
import com.uinnova.product.eam.comm.model.es.FlowSystemOperationData;
import com.uino.dao.AbstractESBaseDao;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.core.CountRequest;
import org.elasticsearch.client.core.CountResponse;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.histogram.DateHistogramAggregationBuilder;
import org.elasticsearch.search.aggregations.bucket.histogram.DateHistogramInterval;
import org.elasticsearch.search.aggregations.bucket.histogram.Histogram;
import org.elasticsearch.search.aggregations.bucket.histogram.ParsedDateHistogram;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.io.IOException;
import java.util.*;

/**
 * <AUTHOR>
 * @since 2024/12/30 10:50
 */
@Slf4j
@Service
public class FlowSystemOperationDao extends AbstractESBaseDao<FlowSystemOperationData, FlowSystemOperationData> {
    @Override
    public String getIndex() {
        return "uino_flow_system_operation_data";
    }

    @Override
    public String getType() {
        return "_doc";
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }

    /**
     * 统计符合条件的数据总数量
     *
     * @param query
     * @return
     */
    public long getCountInRange(QueryBuilder query) {
        long count = 0;
        RestHighLevelClient c = getClient();
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(query);
        CountRequest countRequest = new CountRequest(getFullIndexName());
        countRequest.source(searchSourceBuilder);
        CountResponse countResponse;
        try {
            countResponse = c.count(countRequest, RequestOptions.DEFAULT);
            count = countResponse.getCount();
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            throw new MessageException(e.getMessage());
        }
        return count;
    }

    /**
     * 根据时间维度分组获取
     *
     * @param query    查询条件
     * @param interval 分组维度(年，月，周)
     * @param format   时间格式
     * @return
     */
    public List<Map<String, Long>> getCountGroupByDate(QueryBuilder query, DateHistogramInterval interval, String format) {
        List<Map<String, Long>> mapList = new ArrayList<>();
        RestHighLevelClient c = getClient();
        SearchRequest searchRequest = new SearchRequest(getFullIndexName());
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        // 添加查询条件
        searchSourceBuilder.size(0);
        searchSourceBuilder.query(query);
        // 聚合查询条件
        DateHistogramAggregationBuilder aggregationBuilder = AggregationBuilders.dateHistogram("data_buckets")
                .field("processStateTime")  // 时间字段
                .calendarInterval(interval)  // 按不同情况分组
                .offset("-8h")
                .minDocCount(1);
        searchSourceBuilder.aggregation(aggregationBuilder);
        searchRequest.source(searchSourceBuilder);
        SearchResponse searchResponse;
        try {
            searchResponse = c.search(searchRequest, RequestOptions.DEFAULT);
            ParsedDateHistogram dateHistogram = searchResponse.getAggregations().get("data_buckets");
            for (Histogram.Bucket bucket : dateHistogram.getBuckets()) {
                Map<String, Long> map = new HashMap<>(1);
                String key = bucket.getKeyAsString();
                if (key == null || key.length() <= 0) {
                    key = "";
                } else {
                    key = DateUtil.format(new Date(Long.parseLong(key)), format);
                }
                map.put(key, bucket.getDocCount());
                mapList.add(map);
            }
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            throw new MessageException(e.getMessage());
        }
        return mapList;
    }
}

