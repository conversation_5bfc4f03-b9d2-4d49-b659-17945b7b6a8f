package com.binary.core.i18n;

import java.util.HashMap;
import java.util.Map;


public class DefaultTranslator implements LanguageTranslator {

	
	
	
	
	@Override
	public String trans(String languageCode) {
		return trans(languageCode, (Object)null);
	}

	
	
	@Override
	public String trans(String languageCode, String jsonParams) {
		return trans(languageCode, (Object)null);
	}
	
	

	@Override
	public String trans(String languageCode, Object params) {
		return languageCode;
	}


	
	@Override
	public Map<String, String> getLanguageDictionary() {
		return new HashMap<String, String>();
	}
	
	
	
	@Override
	public Map<String, String> getLanguageDictionary(Language language) {
		return new HashMap<String, String>();
	}
	
	

}
