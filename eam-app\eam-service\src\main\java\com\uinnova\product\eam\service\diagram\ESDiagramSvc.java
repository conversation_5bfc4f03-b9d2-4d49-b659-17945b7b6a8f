package com.uinnova.product.eam.service.diagram;

import com.binary.jdbc.Page;

import com.uinnova.product.eam.base.diagram.model.*;
import com.uinnova.product.eam.model.diagram.*;
import com.uino.bean.permission.base.SysUser;
import org.elasticsearch.index.query.QueryBuilder;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface ESDiagramSvc {
    /**
     * <AUTHOR>
     * @Description 新建空白视图
     * @Date 10:06 2021/6/30
     * @Param [esDiagramInfo]
     * @return java.util.Map<java.lang.String,java.lang.Long>
     **/
    Map<String, String> saveESDiagram(ESDiagramInfoDTO esDiagramInfo);

    /**
     * <AUTHOR>
     * @Description 批量保存视图数据
     * @Date 10:39 2021/8/12
     * @Param [esDiagramInfoList 视图信息集合, newName 新的视图名称, newDirId 新的文件夹id, type 操作类型]
     * @return java.util.List<java.lang.Long>
     **/
    List<Long> saveESDiagramBatch(List<ESDiagramInfoDTO> esDiagramInfoList, String newName, Long newDirId, String type);

    /**
     *  批量保存视图 （当前方法仅可在私有库创建视图快照场景下使用）
     * @param esDiagramInfoList
     * @return
     */
    List<Long> creatSnapshotBatch(List<ESDiagramInfoDTO> esDiagramInfoList);

    /**
     * 根据视图加密id获取视图基本信息
     * @param diagramIds 加密id
     * @param dirTypes 所属文件类型,可为空
     * @return
     */
    List<ESDiagram> selectByIds(Collection<String> diagramIds, List<Integer> dirTypes, List<Integer> isOpens);

    /**
     * 根据视图id获取视图基本信息
     * @param diagramIds 加密id
     * @param dirTypes 所属文件类型,可为空
     * @return
     */
    List<ESDiagram> getByIds(Collection<Long> diagramIds, List<Integer> dirTypes, List<Integer> isOpens);

    List<ESDiagram> selectMyOwnDiagramList(String ownerCode, List<Integer> dirTypes);

    /**
     * <AUTHOR>
     * @Description 根据视图id数组，批量查询视图全量数据
     * @Date 10:06 2021/6/30
     * @Param [ids]
     * @return java.util.List<com.uinnova.project.base.diagram.comm.model.ESDiagramDTO>
     **/
    List<ESDiagramDTO> queryDiagramInfoByIds(Long[] diagramIds, String type, Boolean needAuth, Boolean asset);

    /**
     * <AUTHOR>
     * @Description //查询单个视图的详细信息
     * @Date 14:03 2021/7/28
     * @Param [diagramId, type, needAuth]
     * @return com.uinnova.project.base.diagram.comm.model.ESDiagramDTO
     **/
    ESDiagramDTO queryDiagramInfoById(Long diagramId, String type, Boolean needAuth);

    /**
     * <AUTHOR>
     * @Description 分页查询视图
     * @Date 10:04 2021/6/30
     * @Param [domainId, pageNum, pageSize, cdt, orders, diagramQS]
     * @return com.binary.jdbc.Page<com.uinnova.project.model.diagram.VcDiagramInfo>
     **/
    Page<VcDiagramInfo> queryDiagramInfoPage(Long domainId, Integer pageNum, Integer pageSize, CVcDiagram cdt, String orders);

    /**
     * <AUTHOR>
     * @Description 全量更新视图，即视图基础信息不变(名称可能会变)，其包含的sheet、node、link发生改变
     * @Date 15:25 2021/6/28
     * @Param [esDiagramInfoList]
     * @return java.util.List<java.lang.Long>
     **/
    ESDiagramDTO updateFullDiagram(Long diagramId, ESDiagramInfoDTO esDiagramInfo);

    /**
     * <AUTHOR>
     * @Description 根据视图id物理删除视图信息(包括组件)
     * @Date 10:41 2021/8/12
     * @Param [diagramIds]
     * @return TODO
     **/
    Integer deleteDiagramByIds(Long[] diagramIds);

    /**
     * <AUTHOR>
     * @Description 根据视图id查询视图详情
     * @Date 10:14 2021/7/6
     * @Param [diagramId, type--标识是否需查询视图附加信息，创造者、分享记录等(eg:cooy时不需要附加信息), versionFlag--标识查询历史视图还是普通视图]
     * @return com.uinnova.project.base.diagram.comm.model.ESDiagramDTO
     **/
    ESDiagramDTO queryESDiagramInfoById(Long diagramId, String type, boolean versionFlag);

    /**
     * <AUTHOR>
     * @Description 复制视图
     * @Date 18:58 2021/7/8
     * @Param [diagramMoveCdt]
     * @return java.lang.Long
     **/
    String copyDiagramById(ESDiagramMoveCdt diagramMoveCdt);

    /**
     * <AUTHOR>
     * @Description 批量复制视图
     * @Date 18:58 2021/7/8
     * @Param [diagramMoveCdt]
     * @return java.lang.Long
     **/
    List<Long> copyDiagramByIds(ESDiagramMoveCdt diagramMoveCdt);

    /**
     * <AUTHOR>
     * @Description 根据查询条件分页查询基础视图信息
     * @Date 17:07 2021/7/6
     * @Param [domainId, pageNum, pageSize, cVcDiagram, orders]
     * @return com.binary.jdbc.Page<com.uinnova.project.base.diagram.comm.model.ESDiagram>
     **/
    Page<ESDiagram> queryESDiagramPage(Long domainId, Integer pageNum, Integer pageSize, CVcDiagram cVcDiagram, String orders);

    /**
     * <AUTHOR>
     * @Description 根据查询条件查询基础视图信息
     * @Date 18:00 2021/7/6
     * @Param [domainId, cVcDiagram, o]
     * @return java.util.List<com.uinnova.project.base.diagram.comm.model.ESDiagram>
     **/
    List<ESDiagram> queryESDiagramList(Long domainId, CVcDiagram cVcDiagram, String orders);

    /**
     * @return void
     * <AUTHOR>
     * @Description 批量更新缩略图地址
     * @Date 15:27 2021/7/13
     * @Param [thumbnailBatch]
     **/
    void processBatchThumbnail(ThumbnailBatch thumbnailBatch);

    Page<ESSimpleDiagramDTO> queryESDiagramInfoPage(Long domainId, Integer pageNum, Integer pageSize, CVcDiagram cdt, String orders);

    /**
     * 复制当前视图为历史版本，更新视图信息
     * @param designDiagramId 发布视图加密id
     * @param privateEsDiagramInfo  更新视图
     * @return
     */
    ESDiagramDTO updateFullDiagramNew(Long designDiagramId, ESDiagramInfoDTO privateEsDiagramInfo);

    /**
     * <AUTHOR>
     * @Description 工作台批量导入视图
     * @Date 10:43 2021/8/12
     * @Param [esDiagramInfoList]
     * @return 导入成功的视图id
     **/
    List<String> importDiagramBatch(List<ESDiagramInfoDTO> esDiagramInfoList);

    ESDiagram judgeSingleDiagramAuth(Long diagramId, String type, Boolean needAuth);

    void updateDiagramByQuery(Long diagramId, Long value);

    /**
     * <AUTHOR>
     * @Description 通过视图id物理删除视图
     * @Date 10:44 2021/8/12
     * @Param [diagramId]
     * @return 删除结果
     **/
    String deleteDiagramById(Long diagramId);

    /**
     * <AUTHOR>
     * @Description 将前台提供的json字符串以json文件的形式保存到本地
     * @Date 10:42 2021/8/12
     * @Param [jsonStr]
     * @return 文件存放路径
     **/
    String exportDiagram(String jsonStr);

    Long[] queryDiagramInfoBydEnergy(String[] diagramIds);

    Long queryDiagramInfoByEnergy(String diagramId);

    /**
     * <AUTHOR>
     * @Description 查询TingJs用户的视图列表
     * @Date 10:42 2021/10/27
     * @Param Long domainId, Integer pageNum, Integer pageSize, CVcDiagram dCdt, String orders
     * @return Page<ESDiagram>
     **/
    Page<ESDiagram> queryData(Long domainId, Integer pageNum, Integer pageSize, CVcDiagram dCdt, String orders);

    void clearData();

    /**
     * 删除视图列表
     * @param dirIds
     * @param ownerCode
     */
    void removeDiagrams(List<Long> dirIds, String ownerCode);

    /**
     * 视图列表
     * @param dirId
     * @param ownerCode
     * @return
     */
    List<ESDiagram> findEsDiagramList(Long dirId, String ownerCode);

    /**
     * 获取视图信息
     * @param dEnergy
     * @param isOpen
     * @return
     */
    ESDiagram getEsDiagram(String dEnergy, Integer isOpen);

    /**
     * 更新视图名称
     * @param newName
     * @param dEnergyId
     * @return
     */
    int updateNameByDEnergyId(String newName, String dEnergyId);

    List<ESDiagramNode> selectNodeByDiagramIds(List<Long> diagramIds);

    List<ESDiagramLink> selectLinkByDiagramIds(List<Long> diagramIds);

    List<ESDiagramNode> selectNodeByCiCodes(List<String> ciCodes, String ownerCode);

    List<ESDiagramLink> selectLinkByRltCiCodes(List<String> uniqueCodes);

    List<ESDiagramLink> selectLinkByRltCodes(Collection<String> uniqueCodes, String ownerCode);

    void replaceLinkList(List<ESDiagramLink> linkList);

    void replaceNodeList(List<ESDiagramNode> nodeList);

    List<ESDiagram> selectByDirIds(Collection<Long> dirIds, List<Integer> dirTypes, String ownerCode, List<Integer> isOpens);

    Boolean clearReleaseDiagramIdBydEnergyId(Collection<String> dEnergyId,String ownerCode);

    List<Long> fxCopyDiagramByIds(ESDiagramMoveCdt diagramMoveCdt);

    List<ESDiagram> selectByDirType(Integer dirTypes, String ownerCode, List<Integer> isOpens);

    Page<ESDiagram> queryDiagramByParentDirCode(DiagramSearchParam param, SysUser sysUser, int pageNum, int pageSize);

    int saveLinkList(List<ESDiagramLink> linkList);

    int saveNodeList(List<ESDiagramNode> nodeList);

    int delLinkList(List<Long> ids, List<String> keys);

    int delNodeList(List<Long> ids, List<String> keys);

    ESDiagram querySimpleDiagramInfoById(Long diagramId);

    Boolean updateFlowStatusByIds(List<Long> unPublishIds, Integer flowStatus);

    List<ESDiagram> queryDBDiagramInfoByIds(String[] toArray);

    Long[] queryDBDiagramInfoBydEnergy(String[] diagramIds);

    Map<String, Set<DiagramRelationInfo>> getRelateInfoByDiagramIds(String diagramId, Integer browseStatus);

    /**
     * 条件查询视图信息
     * @param pageNum 页数
     * @param pageSize 条数
     * @param query 查询条件
     * @return
     */
    Page<ESDiagram> selectListByQuery(Integer pageNum, Integer pageSize, QueryBuilder query);

    List<ESDiagramDTO> queryDiagramInfosById(Long[] ids, String o, Boolean needAuth, Boolean asset);

    /**
     * 批量保存、更新视图基本信息
     * @param list 视图基本信息集合
     * @return 保存结果
     */
    Integer saveOrUpdateBatch(List<ESDiagram> list);

    /**
     * 根据文件夹id查询文件夹下的视图信息
     * @param dirIds 文件夹id
     * @return
     */
    List<ESDiagram> selectDiagramsFromRecycle(List<Long> dirIds,String ownerCode,List<String> diagramIds);


    Integer queryFlowStatusById(String diagramId);
}
