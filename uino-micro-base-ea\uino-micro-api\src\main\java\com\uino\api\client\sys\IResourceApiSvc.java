package com.uino.api.client.sys;

import java.util.List;

import com.uino.bean.cmdb.base.ESResource;

/**
 * 资源相关服务
 * 
 * <AUTHOR>
 */
public interface IResourceApiSvc {

    /**
     * 保存同步资源信息
     * 
     * @see #saveSyncResourceInfo(String, String, boolean, boolean, Integer)
     * @param path
     * @param publicUrl
     * @param unzip
     * @param optionType
     * @return
     */
    String saveSyncResourceInfo(String path, String publicUrl, boolean unzip, Integer optionType);

    /**
     * 保存同步资源信息
     * 
     * @param path 资源在本地的真实路径eg:/usr/local/data/111.txt
     * @param publicUrl 资源公开可访问地址eg:http://************/dara/111.txt
     * @param unzip 是否解压
     * @param currentDir 是否解压当前目录
     * @param optionType 操作类型0:创建1:删除2:修改
     * @return
     */
    String saveSyncResourceInfo(String path, String publicUrl, boolean unzip, boolean currentDir, Integer optionType);

    /**
     * 批量保存同步资源信息
     * 
     * @see #saveSyncResourceInfo(String, String, String, Integer)
     * @param saveDtos
     */
    void saveSyncResourceInfo(List<ESResource> saveDtos);

    /**
     * 获取待同步资源信息(从当前时间前一天开始获取)
     * 
     * @param startDataId 起点数据id
     * @return
     */
    List<ESResource> getWaitSyncResources(Long createTime);

}
