package com.uinnova.product.eam.db.diagram.es;

import com.uinnova.product.eam.base.diagram.model.NewUserQuery;
import com.uinnova.product.eam.base.diagram.model.NewUserRecord;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Repository;

import jakarta.annotation.PostConstruct;
@Repository
public class ESNewUserDao extends AbstractESBaseDao<NewUserRecord, NewUserQuery> {
    @Override
    public String getIndex() {
        return "uino_new_user";
    }

    @Override
    public String getType() {
        return "_doc";
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }
}
