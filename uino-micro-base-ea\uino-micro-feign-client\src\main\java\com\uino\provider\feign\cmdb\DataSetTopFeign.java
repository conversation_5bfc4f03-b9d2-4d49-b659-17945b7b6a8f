package com.uino.provider.feign.cmdb;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.uino.provider.feign.config.BaseFeignConfig;

/**
 * 
 * <AUTHOR>
 *
 */
@Component
@FeignClient(name = "${feign-server-name:tarsier-platform}", path = "${feign-server-path:tarsier-platform}/feign/cmdb/datasetTop", configuration = {
		BaseFeignConfig.class })
public interface DataSetTopFeign {

	@PostMapping("collectDataSet")
	void collectDataSet(@RequestBody Long dataSetId);
	
	/**
	 * 取消置顶
	 * 
	 * @param dataSetId
	 */
	@PostMapping("deleteDataSetTop")
	void deleteDataSetTop(@RequestBody Long dataSetId);

}
