package com.uinnova.product.eam.service.cj.service;

import com.uinnova.product.eam.model.cj.domain.PlanModuleAnnotationEntity;
import com.uinnova.product.eam.model.cj.request.PlanModuleAnnotationRequest;

import java.util.List;
import java.util.Map;

/**
 * 方案批注service接口
 *
 * <AUTHOR>
 * @since 2022-3-1 20:40:43
 */
public interface PlanModuleAnnotationService {

    /**
     * 方案批注新增
     *
     * @param request {@link PlanModuleAnnotationRequest}
     * @return 批注id
     */
    Long saveAnnotation(PlanModuleAnnotationRequest request);

    /**
     * 方案批注删除
     *
     * @param request {@link PlanModuleAnnotationRequest}
     */
    void deleteAnnotation(PlanModuleAnnotationRequest request);

    /**
     * 方案批注修改
     *
     * @param request {@link PlanModuleAnnotationRequest}
     */
    void modifyAnnotation(PlanModuleAnnotationRequest request);

    /**
     * 方案批注分页查询
     *
     * @param request {@link PlanModuleAnnotationRequest}
     * @return map说明 ： size - 批注的个数, annotationList - 批注列表
     */
    Map<String, Object> list(PlanModuleAnnotationRequest request);

    /**
     * 批注查询
     *
     * @param planId    方案id
     * @param chapterId 章节id
     * @param delFlag   查询状态
     * @return {@link PlanModuleAnnotationEntity}
     */
    List<PlanModuleAnnotationEntity> list(Long planId, Long chapterId, Boolean delFlag, String processInstanceId);


    /**
     * 删除批注
     * @param planId
     */
    void deleteAnnotationByPlanId(Long planId);
}
