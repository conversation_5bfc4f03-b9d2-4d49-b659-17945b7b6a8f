package com.uinnova.product.eam.comm.model.es;


import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;


@Comment("操作计数表[UINO_EAM_QUESTION]")
public class EamQuestion implements EntityBean {

	private Long id;

	private String content;

	private String templateType;

	private Long domainId;

	private Long createTime;

	private Long modifyTime;


	@Override
	public Long getId() {
		return id;
	}

	@Override
	public void setId(Long id) {
		this.id = id;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public String getTemplateType() {
		return templateType;
	}

	public void setTemplateType(String templateType) {
		this.templateType = templateType;
	}

	public Long getDomainId() {
		return domainId;
	}

	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}

	@Override
	public Long getCreateTime() {
		return createTime;
	}

	@Override
	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}

	@Override
	public Long getModifyTime() {
		return modifyTime;
	}

	@Override
	public void setModifyTime(Long modifyTime) {
		this.modifyTime = modifyTime;
	}
}


