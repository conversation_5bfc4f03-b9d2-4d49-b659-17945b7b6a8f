package com.uino.service.cmdb.microservice;

import java.util.List;

import com.uino.bean.cmdb.base.ESVisualModel;
import com.uino.bean.cmdb.base.ESVisualModelVo;
import com.uino.bean.cmdb.base.LibType;
import com.uino.bean.cmdb.base.VisualModelRltCiClassIds;
import org.elasticsearch.index.query.QueryBuilder;

/**
 * 
 * <AUTHOR>
 *
 */
public interface IVisualModelSvc {

	/**
     * 查询可视化模型列表
     * 
     * @param domainId
     * @return
     */
	public List<ESVisualModel> queryVisualModels(Long domainId);

	List<ESVisualModelVo> queryVisualModels(Long domainId, LibType libType, String loginCode);

	List<ESVisualModelVo> queryVisualModelsNoChickExit(Long domainId, LibType libType, String loginCode);

	/**
     * 保存可视化模型
     * 
     * @param model
     * @return
     */
	public Long saveVisualModel(ESVisualModel model);

	/**
	 * 保存可视化模型至私有库
	 *
	 * @param model
	 * @return
	 */
	Long saveVisualModelPrivate(ESVisualModel model);

	Boolean flushAllEnableVisualModelCiRlt();

	List<ESVisualModel> getVisualModelByQuery(QueryBuilder query, LibType libType);


	/**
     * 修改可视化模型名称
     * 
     * @param model
     * @return
     */
	public void updateVisualModelName(ESVisualModel model);
	
	/**
     * 删除可视化模型
     * 
     * @param id
     * @return
     */
	public void deleteVisualModel(Long id);

	/**
	 * 删除元模型下的sheet页缩略图
	 *
	 * @param id
	 * @return
	 */
	void delVMThumbnailBySheetId(Long id, Long sheetId);

	/**
	 * 根据ID查询可视化模型列表
	 *
	 * @param domainId
	 * @return
	 */
	ESVisualModel queryVisualModelById(Long domainId, Long visId);

	/**
	 * 根据ID查询私有库可视化模型列表
	 *
	 * @param domainId
	 * @return
	 */
	ESVisualModel queryPrivateVisualModelById(Long domainId, Long visId);


	/**
	 * 查询元模型关系所有的源端ci分类和目标端ci分类
	 * @param domainId 域id
	 * @param rltId 关系
	 * @return
	 */
	VisualModelRltCiClassIds queryEnableVisualModelRltSourceAndTarget(Long domainId, Long rltId);

	/**
	 * 删除有效的元模型操作记录
	 */
    void delValidVisData();

}
