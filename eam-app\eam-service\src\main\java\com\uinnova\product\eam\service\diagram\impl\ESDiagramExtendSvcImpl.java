package com.uinnova.product.eam.service.diagram.impl;

import com.binary.core.util.BinaryUtils;
import com.google.common.collect.Lists;
import com.uinnova.product.eam.base.diagram.enums.DiagramCopyEnum;
import com.uinnova.product.eam.base.diagram.model.*;
import com.uinnova.product.eam.base.diagram.utils.FileFilterUtil;
import com.uinnova.product.eam.db.VcDiagramVersionDao;
import com.uinnova.product.eam.db.diagram.es.ESDiagramDao;
import com.uinnova.product.eam.db.diagram.es.ESShareLinkDao;
import com.uinnova.product.eam.service.diagram.ESDiagramExtendSvc;
import com.uinnova.product.eam.service.diagram.ESDiagramLinkSvc;
import com.uinnova.product.eam.service.diagram.ESDiagramNodeSvc;
import com.uinnova.product.eam.service.diagram.ESDiagramSheetSvc;
import com.uinnova.product.eam.service.diagram.threadlocal.ThreadVariable;
import com.uinnova.product.vmdb.comm.util.CommUtil;
import com.uino.api.client.permission.IUserApiSvc;
import com.uino.bean.cmdb.base.LibType;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.permission.business.UserInfo;
import com.uino.dao.util.ESUtil;
import com.uino.service.sys.microservice.IResourceSvc;
import com.uino.service.util.FileUtil;
import com.uino.util.rsm.RsmUtils;
import com.uino.util.sys.SysUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 视图扩展接口业务实现
 * <AUTHOR>
 */
@Service
@Slf4j
public class ESDiagramExtendSvcImpl implements ESDiagramExtendSvc {
    @Value("${local.resource.space}")
    private String localPath;
    @Value("${http.resource.space}")
    private String httpResourceUrl;
    public static ExecutorService DIAGRAM_EXECUTOR = new ThreadPoolExecutor(
            10,
            15,
            1L,
            TimeUnit.SECONDS,
            new LinkedBlockingDeque<>(1000),
            new ThreadPoolExecutor.CallerRunsPolicy());
    @Autowired
    private ESDiagramDao diagramDao;
    @Autowired
    private ESShareLinkDao shareLinkDao;
    @Autowired
    private ESDiagramNodeSvc diagramNodeSvc;
    @Autowired
    private ESDiagramLinkSvc diagramLinkSvc;
    @Autowired
    private ESDiagramSheetSvc diagramSheetSvc;
    @Autowired(required = false)
    private IResourceSvc resourceSvc;
    @Autowired
    private VcDiagramVersionDao diagramVersionDao;
    @Autowired
    private IUserApiSvc userApiSvc;

    @Autowired
    private RsmUtils rsmUtils;
    public static final String FILE_SEPARATOR = System.getProperty("file.separator");

    @Override
    public Map<String,String> updateDiagramBatch(Map<String, String> updateIdMap, LibType libType){
        Set<String> diagramIds = new HashSet<>(updateIdMap.values());
        diagramIds.addAll(updateIdMap.keySet());
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termsQuery("dEnergy.keyword", diagramIds));
        List<ESDiagram> diagramList = diagramDao.getListByQuery(query);
        return updateDiagramBatch(updateIdMap, diagramList, libType);
    }

    @Override
    public Map<String,String> updateDiagramBatch(Map<String, String> updateIdMap, List<ESDiagram> diagramList, LibType libType){
        Map<String,String> resultMap = new HashMap<>();
        if(CollectionUtils.isEmpty(diagramList)){
            return resultMap;
        }
        Map<String, ESDiagram> denergyIdMap = new HashMap<>();
        Map<Long, ESDiagram> diagramIdMap = new HashMap<>();
        for (ESDiagram each : diagramList) {
            denergyIdMap.put(each.getDEnergy(), each);
            diagramIdMap.put(each.getId(), each);
        }
        Set<Long> diagramIds = diagramIdMap.keySet();
        List<ESDiagramNode> nodeList = diagramNodeSvc.getNodeByDiagram(diagramIds, null);
        Map<Long, List<ESDiagramNode>> nodeGroup = nodeList.stream().collect(Collectors.groupingBy(ESDiagramNode::getDiagramId));
        List<ESDiagramLink> linkList = diagramLinkSvc.getLinkByDiagram(diagramIds, null);
        Map<Long, List<ESDiagramLink>> linkGroup = linkList.stream().collect(Collectors.groupingBy(ESDiagramLink::getDiagramId));
        List<ESDiagramSheetDTO> sheetList = diagramSheetSvc.getSheetByDiagram(diagramIds);
        Map<Long, List<ESDiagramSheetDTO>> sheetGroup = sheetList.stream().collect(Collectors.groupingBy(ESDiagramSheetDTO::getDiagramId));
        List<ESDiagram> saveDiagramList = new ArrayList<>();
        List<ESDiagramNode> saveNodeList = new ArrayList<>();
        List<ESDiagramLink> saveLinkList = new ArrayList<>();
        List<ESDiagramSheetDTO> saveSheetList = new ArrayList<>();
        //视图更新前需要删除老node、link、sheet
        List<Long> oldDiagramId = new ArrayList<>();
        List<String> oldEnergyId = new ArrayList<>();
        Map<String, String> updateIconMap = new HashMap<>();
        for (Map.Entry<String, String> entry : updateIdMap.entrySet()) {
            //被更新内容的视图
            ESDiagram updateDiagram = denergyIdMap.get(entry.getKey());
            //从该视图获取更新内容
            ESDiagram resourceDiagram = denergyIdMap.get(entry.getValue());
            if(updateDiagram==null || resourceDiagram==null){
                log.error("=============更新视图失败===========,updateDiagram:{},resourceDiagram:{}", entry.getKey(), entry.getValue());
                continue;
            }
            oldDiagramId.add(updateDiagram.getId());
            //获取新的node数据
            saveNodeList.addAll(getNewNode(updateDiagram, nodeGroup.get(resourceDiagram.getId())));
            //获取新的link数据
            saveLinkList.addAll(getNewLink(updateDiagram, linkGroup.get(resourceDiagram.getId())));
            //获取新的sheet数据
            saveSheetList.addAll(getNewSheet(updateDiagram, sheetGroup.get(resourceDiagram.getId())));
            if(BinaryUtils.isEmpty(updateDiagram.getIcon1())){
                StringBuilder stringBuilder = new StringBuilder();
                stringBuilder.append("/");
                stringBuilder.append(LocalDate.now());

                StringBuilder moreStringBuilder = new StringBuilder();
                moreStringBuilder.append(UUID.randomUUID());
                moreStringBuilder.append(".png");
                String icon1 = Paths.get(stringBuilder.toString(), moreStringBuilder.toString()).toString();
                updateDiagram.setIcon1(icon1);
            }
            //更新缩略图
            updateIconMap.put(updateDiagram.getIcon1(), resourceDiagram.getIcon1());
            //更新视图信息
            saveDiagramList.add(getNewDiagramInfo(updateDiagram, resourceDiagram, libType));
            resultMap.put(entry.getKey(), entry.getValue());
        }
        //删除旧数据
        diagramNodeSvc.deleteByDiagramIds(oldDiagramId);
        diagramLinkSvc.deleteByDiagramIds(oldDiagramId);
        diagramSheetSvc.deleteByDiagramIds(oldDiagramId);
        //删除视图包含的所有分享图片(视图内容更新，导致分享图片内容过期)
        this.deleteShareLink(oldEnergyId);
        //批量更新缩略图
        updateIconBatch(updateIconMap);
        //更新视图内容
        diagramNodeSvc.saveOrUpdateBatch(saveNodeList);
        diagramLinkSvc.saveOrUpdateBatch(saveLinkList);
        diagramSheetSvc.saveOrUpdateBatch(saveSheetList);
        diagramDao.saveOrUpdateBatch(saveDiagramList);
        return resultMap;
    }

    @Override
    public void createDiagramHistoryBatch(List<ESDiagram> diagramList) {
        if(CollectionUtils.isEmpty(diagramList)){
            return;
        }
        DIAGRAM_EXECUTOR.submit(() -> {
            Set<Long> diagramIds = diagramList.stream().map(ESDiagram::getId).collect(Collectors.toSet());
            List<ESDiagramNode> nodeList = diagramNodeSvc.getNodeByDiagram(diagramIds, null);
            Map<Long, List<ESDiagramNode>> nodeGroup = nodeList.stream().collect(Collectors.groupingBy(ESDiagramNode::getDiagramId));
            List<ESDiagramLink> linkList = diagramLinkSvc.getLinkByDiagram(diagramIds, null);
            Map<Long, List<ESDiagramLink>> linkGroup = linkList.stream().collect(Collectors.groupingBy(ESDiagramLink::getDiagramId));
            List<ESDiagramSheetDTO> sheetList = diagramSheetSvc.getSheetByDiagram(diagramIds);
            Map<Long, List<ESDiagramSheetDTO>> sheetGroup = sheetList.stream().collect(Collectors.groupingBy(ESDiagramSheetDTO::getDiagramId));
            List<ESDiagram> saveDiagramList = new ArrayList<>();
            List<ESDiagramNode> saveNodeList = new ArrayList<>();
            List<ESDiagramLink> saveLinkList = new ArrayList<>();
            List<ESDiagramSheetDTO> saveSheetList = new ArrayList<>();
            Map<String, String> updateIconMap = new HashMap<>();
            for (ESDiagram diagram : diagramList) {
                ESDiagram history = createHistoryDiagramInfo(diagram);
                //获取新的node数据
                saveNodeList.addAll(getNewNode(history, nodeGroup.get(diagram.getId())));
                //获取新的link数据
                saveLinkList.addAll(getNewLink(history, linkGroup.get(diagram.getId())));
                //获取新的sheet数据
                saveSheetList.addAll(getNewSheet(history, sheetGroup.get(diagram.getId())));
                //更新缩略图
                updateIconMap.put(history.getIcon1(), diagram.getIcon1());
                //更新视图信息
                saveDiagramList.add(history);
            }
            //批量更新缩略图
            updateIconBatch(updateIconMap);
            //更新视图内容
            diagramNodeSvc.saveOrUpdateBatch(saveNodeList);
            diagramLinkSvc.saveOrUpdateBatch(saveLinkList);
            diagramSheetSvc.saveOrUpdateBatch(saveSheetList);
            diagramDao.saveOrUpdateBatch(saveDiagramList);
        });
    }

    @Override
    public void updateIcon(String updateIcon, String sourceIcon) {
        if(BinaryUtils.isEmpty(updateIcon) || BinaryUtils.isEmpty(sourceIcon)){
            return;
        }
        try {
            rsmUtils.downloadRsmAndUpdateLocalRsm(sourceIcon.replace(httpResourceUrl, ""));
            byte[] iconBytes = FileUtil.readFileToByteArray(new File(FileFilterUtil.parseFilePath(localPath + sourceIcon.replace(httpResourceUrl, ""))));
            FileUtil.writeFile(updateIcon.replace(httpResourceUrl, ""), iconBytes);
        } catch (Exception e) {
            log.error("视图缩略图更新失败:updateIcon->{},sourceIcon->{}", updateIcon, sourceIcon);
        }
    }

    @Override
    public void updateIconBatch(Map<String, String> updateIconMap) {
        if(BinaryUtils.isEmpty(updateIconMap)){
            return;
        }
        for (Map.Entry<String, String> entry : updateIconMap.entrySet()) {
            log.info("视图缩略图更新开始:updateIcon->{},sourceIcon->{}", entry.getKey(), entry.getValue());
            updateIcon(entry.getKey(), entry.getValue());
        }
    }

    /**
     * 将node集合copy一份至目标视图
     * @param updateDiagram 目标视图
     * @param nodeList node集合
     * @return copy出的node集合
     */
    private List<ESDiagramNode> getNewNode(ESDiagram updateDiagram, List<ESDiagramNode> nodeList){
        List<ESDiagramNode> result = new ArrayList<>();
        if(CollectionUtils.isEmpty(nodeList)){
            return result;
        }
        for (ESDiagramNode each : nodeList) {
            ESDiagramNode node = CommUtil.copy(each, ESDiagramNode.class);
            node.setId(ESUtil.getUUID());
            node.setDiagramId(updateDiagram.getId());
            node.setDEnergy(updateDiagram.getDEnergy());
            node.setCreateTime(ESUtil.getNumberDateTime());
            node.setModifyTime(ESUtil.getNumberDateTime());
            result.add(node);
        }
        return result;
    }

    /**
     * 将link集合copy一份至目标视图
     * @param updateDiagram 目标视图
     * @param linkList link集合
     * @return copy出的link集合
     */
    private List<ESDiagramLink> getNewLink(ESDiagram updateDiagram, List<ESDiagramLink> linkList){
        List<ESDiagramLink> result = new ArrayList<>();
        if(CollectionUtils.isEmpty(linkList)){
            return result;
        }
        for (ESDiagramLink each : linkList) {
            ESDiagramLink link = CommUtil.copy(each, ESDiagramLink.class);
            link.setId(ESUtil.getUUID());
            link.setDiagramId(updateDiagram.getId());
            link.setDEnergy(updateDiagram.getDEnergy());
            link.setCreateTime(ESUtil.getNumberDateTime());
            link.setModifyTime(ESUtil.getNumberDateTime());
            result.add(link);
        }
        return result;
    }

    /**
     * 将sheet集合copy一份至目标视图
     * @param updateDiagram 目标视图
     * @param sheetList sheet集合
     * @return copy出的sheet集合
     */
    private List<ESDiagramSheetDTO> getNewSheet(ESDiagram updateDiagram, List<ESDiagramSheetDTO> sheetList){
        List<ESDiagramSheetDTO> result = new ArrayList<>();
        if(CollectionUtils.isEmpty(sheetList)){
            return result;
        }
        for (ESDiagramSheetDTO each : sheetList) {
            ESDiagramSheetDTO sheet = CommUtil.copy(each, ESDiagramSheetDTO.class);
            sheet.setId(ESUtil.getUUID());
            sheet.setDiagramId(updateDiagram.getId());
            sheet.setDEnergy(updateDiagram.getDEnergy());
            sheet.setCreateTime(ESUtil.getNumberDateTime());
            sheet.setModifyTime(ESUtil.getNumberDateTime());
            result.add(sheet);
        }
        return result;
    }

    /**
     * 将视图信息copy一份至目标视图
     * @param updateDiagram 更新的视图
     * @param resourceDiagram 源内容视图
     * @return copy出视图集合
     */
    private ESDiagram getNewDiagramInfo(ESDiagram updateDiagram, ESDiagram resourceDiagram, LibType libType){
        ESDiagram result = CommUtil.copy(resourceDiagram, ESDiagram.class);
        long dateTime = ESUtil.getNumberDateTime();
        result.setId(updateDiagram.getId());
        result.setDirId(updateDiagram.getDirId());
        result.setIsOpen(updateDiagram.getIsOpen());
        result.setIsAttention(updateDiagram.getIsAttention());
        result.setIcon1(updateDiagram.getIcon1());
        result.setPrepareDiagramId(updateDiagram.getPrepareDiagramId());
        result.setCooperateIds(updateDiagram.getCooperateIds());
        result.setFlowStatus(updateDiagram.getFlowStatus());
        result.setCreateTime(updateDiagram.getCreateTime());
        result.setStatus(1);
        result.setWatermarkInfo(resourceDiagram.getWatermarkInfo());
        result.setLocalVersion(0);
        result.setOpenTime(dateTime);
        if(LibType.PRIVATE.equals(libType)){
            //检出
            result.setOwnerCode(updateDiagram.getOwnerCode());
            result.setCreator(updateDiagram.getCreator());
            result.setModifier(updateDiagram.getModifier());
            result.setReleaseDiagramId(resourceDiagram.getDEnergy());
        }else{
            //发布
            result.setReleaseVersion(updateDiagram.getReleaseVersion()+1);
            result.setReleaseDiagramId(updateDiagram.getDEnergy());
        }
        result.setModifyTime(dateTime);
        return result;
    }

    /**
     *  回滚视图基本信息
     * @param mainInfo
     * @param snapshotInfo
     * @return
     */
    private ESDiagram rollBackDiagramBaseInfo(ESDiagram mainInfo, ESDiagram snapshotInfo) {
        ESDiagram result = CommUtil.copy(snapshotInfo, ESDiagram.class);
        long dateTime = ESUtil.getNumberDateTime();
        result.setDirId(mainInfo.getDirId());
        result.setIsOpen(mainInfo.getIsOpen());
        result.setIsAttention(mainInfo.getIsAttention());
        // result.setIcon1(mainInfo.getIcon1());
        result.setCooperateIds(mainInfo.getCooperateIds());
        result.setFlowStatus(mainInfo.getFlowStatus());
        result.setCreateTime(mainInfo.getCreateTime());
        result.setStatus(1);
        result.setLocalVersion(0);
        result.setOpenTime(dateTime);
        result.setOwnerCode(mainInfo.getOwnerCode());
        result.setCreator(mainInfo.getCreator());
        result.setModifier(mainInfo.getModifier());
        result.setReleaseDiagramId(mainInfo.getDEnergy());
        result.setReleaseVersion(mainInfo.getReleaseVersion());
        result.setModifyTime(dateTime);
        return result;
    }

    /**
     * 创建视图历史版本
     * @param resourceDiagram 源内容视图
     */
    private ESDiagram createHistoryDiagramInfo(ESDiagram resourceDiagram){
        ESDiagram result = CommUtil.copy(resourceDiagram, ESDiagram.class);
        result.setId(ESUtil.getUUID());
        result.setIsAttention(0);
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("/");
        stringBuilder.append(LocalDate.now());

        StringBuilder moreStringBuilder = new StringBuilder();
        moreStringBuilder.append(UUID.randomUUID());
        moreStringBuilder.append(".png");
        String icon1 = Paths.get(stringBuilder.toString(), moreStringBuilder.toString()).toString();
        result.setIcon1(icon1);
        result.setPrepareDiagramId(null);
        result.setCooperateIds(null);
        result.setFlowStatus(0);
        result.setStatus(0);
        result.setDataStatus(1);
        result.setLocalVersion(0);
        result.setHistoryVersionFlag(1);
        result.setPrepareDiagramId(result.getDEnergy());
        result.setReleaseDiagramId(resourceDiagram.getDEnergy());
        return result;
    }

    @Override
    public Map<String, String> copyDiagramBatch(Map<String, Long> diagramDirIdMap, List<ESDiagram> diagramList, DiagramCopyEnum type) {
        Map<String,String> resultMap = new HashMap<>();
        if(CollectionUtils.isEmpty(diagramList)){
            return resultMap;
        }
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        String loginCode = currentUserInfo.getLoginCode();
        Long loginId = currentUserInfo.getId();
        Set<Long> diagramIds = diagramList.stream().map(ESDiagram::getId).collect(Collectors.toSet());
        List<ESDiagramNode> nodeList = diagramNodeSvc.getNodeByDiagram(diagramIds, null);
        Map<Long, List<ESDiagramNode>> nodeGroup = nodeList.stream().collect(Collectors.groupingBy(ESDiagramNode::getDiagramId));
        List<ESDiagramLink> linkList = diagramLinkSvc.getLinkByDiagram(diagramIds, null);
        Map<Long, List<ESDiagramLink>> linkGroup = linkList.stream().collect(Collectors.groupingBy(ESDiagramLink::getDiagramId));
        List<ESDiagramSheetDTO> sheetList = diagramSheetSvc.getSheetByDiagram(diagramIds);
        Map<Long, List<ESDiagramSheetDTO>> sheetGroup = sheetList.stream().collect(Collectors.groupingBy(ESDiagramSheetDTO::getDiagramId));
        List<ESDiagram> saveDiagramList = new ArrayList<>();
        List<Long> saveDiagramIdList = new ArrayList<>();
        List<ESDiagramNode> saveNodeList = new ArrayList<>();
        List<ESDiagramLink> saveLinkList = new ArrayList<>();
        List<ESDiagramSheetDTO> saveSheetList = new ArrayList<>();
        Map<String, String> updateIconMap = new HashMap<>();
        for (ESDiagram each : diagramList) {
            Long dirId = diagramDirIdMap.getOrDefault(each.getDEnergy(), 0L);
            ESDiagram newDiagram = getCopyDiagramInfo(each, dirId, loginCode, loginId, type);
            //获取新的node数据
            saveNodeList.addAll(getNewNode(newDiagram, nodeGroup.get(each.getId())));
            //获取新的link数据
            saveLinkList.addAll(getNewLink(newDiagram, linkGroup.get(each.getId())));
            //获取新的sheet数据
            saveSheetList.addAll(getNewSheet(newDiagram, sheetGroup.get(each.getId())));
            //更新缩略图
            updateIconMap.put(newDiagram.getIcon1(), each.getIcon1());
            saveDiagramList.add(newDiagram);
            saveDiagramIdList.add(newDiagram.getId());
            resultMap.put(each.getDEnergy(), newDiagram.getDEnergy());
        }
        //批量更新缩略图
        updateIconBatch(updateIconMap);
        //更新视图内容
        diagramNodeSvc.saveOrUpdateBatch(saveNodeList);
        diagramLinkSvc.saveOrUpdateBatch(saveLinkList);
        diagramSheetSvc.saveOrUpdateBatch(saveSheetList);
        diagramDao.saveOrUpdateBatch(saveDiagramList);
        // 新视图初始化生成时创建历史版本数据
        createESDiagramVersionRecord(saveDiagramIdList.toArray(new Long[0]));
        return resultMap;
    }

    @Override
    public void updateDirIdBatch(Map<String, Long> diagramDirIdMap) {
        if(BinaryUtils.isEmpty(diagramDirIdMap)){
            return;
        }
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termsQuery("dEnergy.keyword", diagramDirIdMap.keySet()));
        List<ESDiagram> diagramList = diagramDao.getListByQuery(query);
        if(CollectionUtils.isEmpty(diagramList)){
            return;
        }
        for (ESDiagram each : diagramList) {
            Long dirId = diagramDirIdMap.get(each.getDEnergy());
            if(BinaryUtils.isEmpty(dirId)){
                continue;
            }
            each.setDirId(dirId);
        }
        diagramDao.saveOrUpdateBatch(diagramList);
    }

    /**
     * copy一份新视图
     * @param resourceDiagram 源内容视图
     */
    private ESDiagram getCopyDiagramInfo(ESDiagram resourceDiagram, Long dirId, String ownerCode, Long loginId, DiagramCopyEnum type){
        ESDiagram result = CommUtil.copy(resourceDiagram, ESDiagram.class);
        long dateTime = ESUtil.getNumberDateTime();
        result.setId(ESUtil.getUUID());
        result.setDirId(dirId);
        result.setIsAttention(0);
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("/");
        stringBuilder.append(LocalDate.now());

        StringBuilder moreStringBuilder = new StringBuilder();
        moreStringBuilder.append(UUID.randomUUID());
        moreStringBuilder.append(".png");
        String icon1 = Paths.get(stringBuilder.toString(), moreStringBuilder.toString()).toString();
        result.setIcon1(icon1);
        result.setPrepareDiagramId(result.getDEnergy());
        result.setCooperateIds(null);
        result.setFlowStatus(0);
        result.setOwnerCode(ownerCode);
        result.setCreator(ownerCode);
        result.setModifier(ownerCode);
        result.setOpenTime(dateTime);
        result.setCreateTime(dateTime);
        result.setModifyTime(dateTime);
        result.setStatus(1);
        result.setDataStatus(1);
        result.setLocalVersion(0);
        result.setHistoryVersionFlag(1);
        if (DiagramCopyEnum.SNAPSHOT.equals(type)) {
            result.setIsOpen(0);
            result.setReleaseDiagramId("");
            result.setReleaseVersion(0);
            if (!resourceDiagram.getOwnerCode().equals(ownerCode)) {
                // 基于历史版本复制 同一个用户操作，新生成的视图与旧视图存放位置一致 协作者操作新视图存放在根目录下
                result.setDirId(0L);
                result.setUserId(loginId);
            }
        }else if(DiagramCopyEnum.PUSH.equals(type)){
            //发布
            result.setIsOpen(1);
            result.setReleaseVersion(1);
            result.setReleaseDiagramId(result.getDEnergy());
        }else if(DiagramCopyEnum.PULL.equals(type)){
            //检出
            result.setIsOpen(0);
            result.setReleaseVersion(resourceDiagram.getReleaseVersion());
            result.setReleaseDiagramId(resourceDiagram.getDEnergy());
        } else {
            result.setIsOpen(0);
            result.setReleaseVersion(0);
            result.setReleaseDiagramId("");
        }
        return result;
    }

    @Override
    public void deleteShareLink(Collection<String> diagramIds){
        if(CollectionUtils.isEmpty(diagramIds)){
            return;
        }
        DIAGRAM_EXECUTOR.submit(() -> {
            BoolQueryBuilder query = QueryBuilders.boolQuery();
            query.must(QueryBuilders.termsQuery("diagramId", diagramIds));
            query.must(QueryBuilders.termQuery("linkType", 0));
            List<DiagramShareLink> shareLinks = shareLinkDao.getListByQuery(query);
            if(CollectionUtils.isEmpty(shareLinks)){
                return;
            }
            try {
                shareLinkDao.deleteByQuery(query, true);
                //删除文件
                String path = localPath.replace("/", FILE_SEPARATOR).replace("\\", FILE_SEPARATOR);
                Map<String, String> pathMap = new HashMap<>();
                for (DiagramShareLink shareLink : shareLinks) {
                    String fileUrl = shareLink.getStorePath();
                    if (BinaryUtils.isEmpty(fileUrl)) {
                        continue;
                    }
                    fileUrl = fileUrl.startsWith(path) ? fileUrl : new StringBuilder(path).append(fileUrl).toString();
                    File delFile = new File(FileFilterUtil.parseFilePath(fileUrl));
                    rsmUtils.deleteRsm(fileUrl);
                    if (delFile.exists()) {
                        delFile.delete();
                        pathMap.put(fileUrl, httpResourceUrl + fileUrl);
                    }
                }
                resourceSvc.saveSyncResourceBatch(pathMap, false, false,1);
            }catch (Exception e){
                log.info("删除分享图片失败");
            }
        });
    }

    @Override
    public List<ESDiagram> queryByReleaseDiagramId(Collection<String> diagramIds, String ownerCode, Integer open) {
        if(CollectionUtils.isEmpty(diagramIds)){
            return new ArrayList<>();
        }
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termsQuery("releaseDiagramId.keyword", diagramIds));
        if(open != null){
            query.must(QueryBuilders.termQuery("isOpen", open));
        }
        if(!BinaryUtils.isEmpty(ownerCode)){
            query.must(QueryBuilders.termQuery("ownerCode.keyword", ownerCode));
        }
        query.must(QueryBuilders.termQuery("historyVersionFlag", 1));
        query.must(QueryBuilders.termQuery("status", 1));
        query.must(QueryBuilders.termQuery("dataStatus", 1));
        List<ESDiagram> result = diagramDao.selectListByQuery(1, 3000, query);
        if(CollectionUtils.isEmpty(result)){
            return new ArrayList<>();
        }
        return result;
    }

    @Override
    public void updateDiagramPublicVersion(Map<String, String> diagramIdMap){
        if(BinaryUtils.isEmpty(diagramIdMap)){
            return;
        }
        DIAGRAM_EXECUTOR.submit(() -> {
            log.info("开始更新");
            Set<String> diagramIds = new HashSet<>(diagramIdMap.keySet());
            diagramIds.addAll(diagramIdMap.values());
            List<ESDiagram> diagramList = diagramDao.getListByQuery(QueryBuilders.termsQuery("dEnergy.keyword", diagramIds));
            if(CollectionUtils.isEmpty(diagramList)){
                return;
            }
            Map<String, ESDiagram> diagramMap = diagramList.stream().collect(Collectors.toMap(ESDiagram::getDEnergy, e -> e, (k1, k2) -> k2));
            List<ESDiagram> updateList = new ArrayList<>();
            for (Map.Entry<String, String> entry : diagramIdMap.entrySet()) {
                ESDiagram diagram = diagramMap.get(entry.getKey());
                ESDiagram resource = diagramMap.get(entry.getValue());
                if(diagram == null || resource == null){
                    continue;
                }
                diagram.setReleaseDiagramId(entry.getValue());
                diagram.setReleaseVersion(resource.getReleaseVersion());
                updateList.add(diagram);
            }
            log.info("更新版本成功");
            diagramDao.saveOrUpdateBatch(updateList);
        });
    }

    @Override
    public void rollBackDiagramById(ESDiagram snapshotInfo, ESDiagram mainInfo) {
        Long snapshotId = snapshotInfo.getId();
        Long mainId = mainInfo.getId();
        List<ESDiagramNode> nodeList = diagramNodeSvc.getNodeByDiagram(Lists.newArrayList(snapshotId, mainId), null);
        Map<Long, List<ESDiagramNode>> nodeGroup = nodeList.stream().collect(Collectors.groupingBy(ESDiagramNode::getDiagramId));
        List<ESDiagramLink> linkList = diagramLinkSvc.getLinkByDiagram(Lists.newArrayList(snapshotId, mainId), null);
        Map<Long, List<ESDiagramLink>> linkGroup = linkList.stream().collect(Collectors.groupingBy(ESDiagramLink::getDiagramId));
        List<ESDiagramSheetDTO> sheetList = diagramSheetSvc.getSheetByDiagram(Lists.newArrayList(snapshotId, mainId));
        Map<Long, List<ESDiagramSheetDTO>> sheetGroup = sheetList.stream().collect(Collectors.groupingBy(ESDiagramSheetDTO::getDiagramId));

        // 删除主视图上的node link sheet 信息 基于快照复制一份数据绑定到主视图上
        diagramNodeSvc.deleteByDiagramIds(Collections.singletonList(mainId));
        diagramLinkSvc.deleteByDiagramIds(Collections.singletonList(mainId));
        diagramSheetSvc.deleteByDiagramIds(Collections.singletonList(mainId));

        List<ESDiagramNode> newNodeFromSnapshot = getNewNode(mainInfo, nodeGroup.get(snapshotId));
        List<ESDiagramLink> newLinkFromSnapshot = getNewLink(mainInfo, linkGroup.get(snapshotId));
        List<ESDiagramSheetDTO> newSheetFromSnapshot = getNewSheet(mainInfo, sheetGroup.get(snapshotId));
        ESDiagram newDiagramInfo = rollBackDiagramBaseInfo(mainInfo, snapshotInfo);
        //批量更新缩略图
        updateIconBatch(new HashMap<String, String>(){{
            put(snapshotInfo.getIcon1(), mainInfo.getIcon1());
        }});
        //更新视图内容
        diagramNodeSvc.saveOrUpdateBatch(newNodeFromSnapshot);
        diagramLinkSvc.saveOrUpdateBatch(newLinkFromSnapshot);
        diagramSheetSvc.saveOrUpdateBatch(newSheetFromSnapshot);
        diagramDao.saveOrUpdate(newDiagramInfo);
    }

    /**
     *  初始化创建历史版本
     * @param diagramIds
     */
    private void createESDiagramVersionRecord(Long[] diagramIds) {
        List<VcDiagramVersion> diagramVersionList = new ArrayList<>();
        SysUser sysUser = ThreadVariable.getSysUser();
        if (sysUser == null) {
            sysUser = SysUtil.getCurrentUserInfo();
        }
        for (Long diagramId : diagramIds) {
            VcDiagramVersion diagramVersion = new VcDiagramVersion();
            diagramVersion.setId(diagramId);
            diagramVersion.setStatus(1);
            diagramVersion.setDiagramId(diagramId);
            diagramVersion.setDomainId(1L);
            diagramVersion.setVersionTime(BinaryUtils.getNumberDateTime());
            diagramVersion.setCreator(buildCreator(sysUser));
            diagramVersionList.add(diagramVersion);
        }
        diagramVersionDao.insertBatchAsync(diagramVersionList, sysUser);
    }

    /**
     * 组件创建者 格式：用户名[用户code]
     *
     * @return
     */
    private String buildCreator(SysUser loginUser) {
        String creator;
        //查询当前用户最新的信息
        UserInfo op = userApiSvc.getUserInfoById(loginUser.getId());
        if (!BinaryUtils.isEmpty(op)) {
            creator = op.getUserName() + "[" + op.getLoginCode() + "]";
        } else {
            creator = loginUser.getUserName() + "[" + loginUser.getLoginCode() + "]";
        }
        return creator;
    }
}
