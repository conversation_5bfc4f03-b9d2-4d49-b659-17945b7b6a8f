package com.uinnova.product.eam.service;

import com.uinnova.product.eam.comm.model.es.TemplateBinding;

import java.util.List;

public interface PlanTemplateBindService {

    Integer addBind(Long classId);

    List<TemplateBinding> getBindList();

    Integer deleteBindById(Long id);

    void dragSort(Long bindId, Long sortNum);

    Integer saveOrUpdateBatch(List<TemplateBinding> templateBindingList);
}
