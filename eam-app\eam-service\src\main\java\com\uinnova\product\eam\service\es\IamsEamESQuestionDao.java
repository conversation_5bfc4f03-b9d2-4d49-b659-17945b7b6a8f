package com.uinnova.product.eam.service.es;

import com.uinnova.product.eam.comm.model.es.CEamQuestion;
import com.uinnova.product.eam.comm.model.es.EamQuestion;
import com.uino.dao.AbstractESBaseDao;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.stereotype.Service;

/**
 * EAM 试题
 * <AUTHOR>
 */
@Service
public class IamsEamESQuestionDao extends AbstractESBaseDao<EamQuestion, CEamQuestion> {

    Log logger = LogFactory.getLog(getClass());

    @Override
    public String getIndex() {
        return "uino_eam_question";
    }

    @Override
    public String getType() {
        return getIndex();
    }

}
