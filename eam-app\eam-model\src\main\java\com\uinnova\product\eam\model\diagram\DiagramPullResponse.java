package com.uinnova.product.eam.model.diagram;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Collections;
import java.util.Map;

/**
 * 视图检出响应体
 * <AUTHOR>
 */
@Accessors(chain = true)
@Data
public class DiagramPullResponse {

    @Comment("code")
    private int resultCode = 1;

    @Comment("资产仓库视图id->设计空间视图id映射")
    private Map<String,String> diagramMap = Collections.emptyMap();

    @Comment("检出成功后,私有库中视图id与视图加密id映射")
    private Map<Long,String> privateDiagramMap = Collections.emptyMap();

    @Comment("错误信息")
    private String errMsg;

}
