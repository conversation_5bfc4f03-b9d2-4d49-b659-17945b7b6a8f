<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.uinnova.product.eam</groupId>
		<artifactId>eam</artifactId>
		<version>fuxi-1.0.0-SNAPSHOT</version>
	</parent>
	<artifactId>eam-project-web</artifactId>
	<packaging>jar</packaging>

	<properties>
		<docker.host>http://**************:2375</docker.host>
		<docker.project.version>quickea-1.8</docker.project.version>
		<docker.registry.url>dk.uino.cn</docker.registry.url>
		<skipTests>true</skipTests>
	</properties>

	<dependencies>
		<dependency>
			<groupId>com.uinnova.product.eam</groupId>
			<artifactId>eam-web</artifactId>
			<version>fuxi-1.0.0-SNAPSHOT</version>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<artifactId>maven-assembly-plugin</artifactId>
				<version>3.3.0</version>
				<configuration>
					<finalName>eam-project</finalName>
				</configuration>
				<executions>
					<execution>
						<id>pack-center</id>
						<phase>install</phase>
						<goals>
							<goal>single</goal>
						</goals>
						<configuration>
							<descriptors>
								<descriptor>/pack-war2tar.xml</descriptor>
							</descriptors>
							<attach>false</attach>
						</configuration>
					</execution>
				</executions>
			</plugin>


			<plugin>
				<artifactId>maven-deploy-plugin</artifactId>
				<version>2.8.2</version>
				<executions>
					<execution>
						<id>default-deploy</id>
						<phase>deploy</phase>
						<goals>
							<goal>deploy</goal>
						</goals>
						<!-- skip默认deploy插件的执行 -->
						<configuration>
							<skip>true</skip>
						</configuration>
					</execution>
					<execution>
						<id>deploy-file</id>
						<phase>deploy</phase>
						<goals>
							<goal>deploy-file</goal>
						</goals>
						<configuration>
							<!-- 开发阶段上传到snapshot仓库，上线阶段上传到release仓库 -->
							<packaging>jar</packaging>
							<file>${project.build.directory}/${project.artifactId}-${project.version}.jar</file>
							<groupId>${project.groupId}</groupId>
							<artifactId>${project.artifactId}</artifactId>
							<version>${project.version}</version>
							<repositoryId>uinnova-snapshots</repositoryId>
							<url>https://mvn-dev.uino.cn/repository/uinnova-snapshots</url>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>


</project>