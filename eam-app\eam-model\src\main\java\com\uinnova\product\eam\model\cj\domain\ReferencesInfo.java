package com.uinnova.product.eam.model.cj.domain;

import com.alibaba.fastjson.JSONObject;
import com.binary.framework.bean.annotation.Comment;
import com.uino.bean.cmdb.base.ESTableDataConfigInfo;
import lombok.Data;

import java.util.List;

/**
 * 交付物模板参考规范信息
 *
 * <AUTHOR>
 */
@Data
public class ReferencesInfo {
    @Comment("参考规范CiCode")
    private List<String> referencesCiCodes;

    @Comment("内容快配置属性显示信息")
    private List<ESTableDataConfigInfo.TableDataConfig> referencesClassAttrs;

    @Comment("内容块参考规范分类标识")
    private String referencesClassCode;

    @Comment("内容块参考规范分类ID层级树")
    private List<Long> referencesClassIdTree;

    @Comment("选中数据")
    private List<JSONObject> checkedInfo;
}
