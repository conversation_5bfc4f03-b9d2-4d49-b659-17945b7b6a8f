package com.uinnova.product.eam.comm.model;




import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;


@Comment("组合视图表[DC_COMB_DIAGRAM]")
public class CDcCombDiagram implements Condition {
	private static final long serialVersionUID = 1L;


	@Comment("ID[ID] operate-Equal[=]")
	private Long id;


	@Comment("ID[ID] operate-In[in]")
	private Long[] ids;


	@Comment("ID[ID] operate-GTEqual[>=]")
	private Long startId;

	@Comment("ID[ID] operate-LTEqual[<=]")
	private Long endId;


	@Comment("所属组合视图[COMB_DIAGRAM_ID] operate-Equal[=]")
	private Long combDiagramId;


	@Comment("所属组合视图[COMB_DIAGRAM_ID] operate-In[in]")
	private Long[] combDiagramIds;


	@Comment("所属组合视图[COMB_DIAGRAM_ID] operate-GTEqual[>=]")
	private Long startCombDiagramId;

	@Comment("所属组合视图[COMB_DIAGRAM_ID] operate-LTEqual[<=]")
	private Long endCombDiagramId;


	@Comment("视图ID[DIAGRAM_ID] operate-Equal[=]")
	private Long diagramId;


	@Comment("视图ID[DIAGRAM_ID] operate-In[in]")
	private Long[] diagramIds;


	@Comment("视图ID[DIAGRAM_ID] operate-GTEqual[>=]")
	private Long startDiagramId;

	@Comment("视图ID[DIAGRAM_ID] operate-LTEqual[<=]")
	private Long endDiagramId;


	@Comment("位置X[PX] operate-Equal[=]")
	private Integer px;


	@Comment("位置X[PX] operate-In[in]")
	private Integer[] pxs;


	@Comment("位置X[PX] operate-GTEqual[>=]")
	private Integer startPx;

	@Comment("位置X[PX] operate-LTEqual[<=]")
	private Integer endPx;


	@Comment("位置Y[PY] operate-Equal[=]")
	private Integer py;


	@Comment("位置Y[PY] operate-In[in]")
	private Integer[] pys;


	@Comment("位置Y[PY] operate-GTEqual[>=]")
	private Integer startPy;

	@Comment("位置Y[PY] operate-LTEqual[<=]")
	private Integer endPy;


	@Comment("方向[DIRECT] operate-Equal[=]    0=水平 1=垂直")
	private Integer direct;


	@Comment("方向[DIRECT] operate-In[in]    0=水平 1=垂直")
	private Integer[] directs;


	@Comment("方向[DIRECT] operate-GTEqual[>=]    0=水平 1=垂直")
	private Integer startDirect;

	@Comment("方向[DIRECT] operate-LTEqual[<=]    0=水平 1=垂直")
	private Integer endDirect;


	@Comment("所属域[DOMAIN_ID] operate-Equal[=]")
	private Long domainId;


	@Comment("所属域[DOMAIN_ID] operate-In[in]")
	private Long[] domainIds;


	@Comment("所属域[DOMAIN_ID] operate-GTEqual[>=]")
	private Long startDomainId;

	@Comment("所属域[DOMAIN_ID] operate-LTEqual[<=]")
	private Long endDomainId;


	@Comment("数据状态[DATA_STATUS] operate-Equal[=]    0=删除，1=正常")
	private Integer dataStatus;


	@Comment("数据状态[DATA_STATUS] operate-In[in]    0=删除，1=正常")
	private Integer[] dataStatuss;


	@Comment("数据状态[DATA_STATUS] operate-GTEqual[>=]    0=删除，1=正常")
	private Integer startDataStatus;

	@Comment("数据状态[DATA_STATUS] operate-LTEqual[<=]    0=删除，1=正常")
	private Integer endDataStatus;


	@Comment("创建时间[CREATE_TIME] operate-Equal[=]    yyyyMMddHHmmss")
	private Long createTime;


	@Comment("创建时间[CREATE_TIME] operate-In[in]    yyyyMMddHHmmss")
	private Long[] createTimes;


	@Comment("创建时间[CREATE_TIME] operate-GTEqual[>=]    yyyyMMddHHmmss")
	private Long startCreateTime;

	@Comment("创建时间[CREATE_TIME] operate-LTEqual[<=]    yyyyMMddHHmmss")
	private Long endCreateTime;


	@Comment("更新时间[MODIFY_TIME] operate-Equal[=]    yyyyMMddHHmmss")
	private Long modifyTime;


	@Comment("更新时间[MODIFY_TIME] operate-In[in]    yyyyMMddHHmmss")
	private Long[] modifyTimes;


	@Comment("更新时间[MODIFY_TIME] operate-GTEqual[>=]    yyyyMMddHHmmss")
	private Long startModifyTime;

	@Comment("更新时间[MODIFY_TIME] operate-LTEqual[<=]    yyyyMMddHHmmss")
	private Long endModifyTime;




	public Long getId() {
		return this.id;
	}
	public void setId(Long id) {
		this.id = id;
	}


	public Long[] getIds() {
		return this.ids;
	}
	public void setIds(Long[] ids) {
		this.ids = ids;
	}


	public Long getStartId() {
		return this.startId;
	}
	public void setStartId(Long startId) {
		this.startId = startId;
	}


	public Long getEndId() {
		return this.endId;
	}
	public void setEndId(Long endId) {
		this.endId = endId;
	}


	public Long getCombDiagramId() {
		return this.combDiagramId;
	}
	public void setCombDiagramId(Long combDiagramId) {
		this.combDiagramId = combDiagramId;
	}


	public Long[] getCombDiagramIds() {
		return this.combDiagramIds;
	}
	public void setCombDiagramIds(Long[] combDiagramIds) {
		this.combDiagramIds = combDiagramIds;
	}


	public Long getStartCombDiagramId() {
		return this.startCombDiagramId;
	}
	public void setStartCombDiagramId(Long startCombDiagramId) {
		this.startCombDiagramId = startCombDiagramId;
	}


	public Long getEndCombDiagramId() {
		return this.endCombDiagramId;
	}
	public void setEndCombDiagramId(Long endCombDiagramId) {
		this.endCombDiagramId = endCombDiagramId;
	}


	public Long getDiagramId() {
		return this.diagramId;
	}
	public void setDiagramId(Long diagramId) {
		this.diagramId = diagramId;
	}


	public Long[] getDiagramIds() {
		return this.diagramIds;
	}
	public void setDiagramIds(Long[] diagramIds) {
		this.diagramIds = diagramIds;
	}


	public Long getStartDiagramId() {
		return this.startDiagramId;
	}
	public void setStartDiagramId(Long startDiagramId) {
		this.startDiagramId = startDiagramId;
	}


	public Long getEndDiagramId() {
		return this.endDiagramId;
	}
	public void setEndDiagramId(Long endDiagramId) {
		this.endDiagramId = endDiagramId;
	}


	public Integer getPx() {
		return this.px;
	}
	public void setPx(Integer px) {
		this.px = px;
	}


	public Integer[] getPxs() {
		return this.pxs;
	}
	public void setPxs(Integer[] pxs) {
		this.pxs = pxs;
	}


	public Integer getStartPx() {
		return this.startPx;
	}
	public void setStartPx(Integer startPx) {
		this.startPx = startPx;
	}


	public Integer getEndPx() {
		return this.endPx;
	}
	public void setEndPx(Integer endPx) {
		this.endPx = endPx;
	}


	public Integer getPy() {
		return this.py;
	}
	public void setPy(Integer py) {
		this.py = py;
	}


	public Integer[] getPys() {
		return this.pys;
	}
	public void setPys(Integer[] pys) {
		this.pys = pys;
	}


	public Integer getStartPy() {
		return this.startPy;
	}
	public void setStartPy(Integer startPy) {
		this.startPy = startPy;
	}


	public Integer getEndPy() {
		return this.endPy;
	}
	public void setEndPy(Integer endPy) {
		this.endPy = endPy;
	}


	public Integer getDirect() {
		return this.direct;
	}
	public void setDirect(Integer direct) {
		this.direct = direct;
	}


	public Integer[] getDirects() {
		return this.directs;
	}
	public void setDirects(Integer[] directs) {
		this.directs = directs;
	}


	public Integer getStartDirect() {
		return this.startDirect;
	}
	public void setStartDirect(Integer startDirect) {
		this.startDirect = startDirect;
	}


	public Integer getEndDirect() {
		return this.endDirect;
	}
	public void setEndDirect(Integer endDirect) {
		this.endDirect = endDirect;
	}


	public Long getDomainId() {
		return this.domainId;
	}
	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}


	public Long[] getDomainIds() {
		return this.domainIds;
	}
	public void setDomainIds(Long[] domainIds) {
		this.domainIds = domainIds;
	}


	public Long getStartDomainId() {
		return this.startDomainId;
	}
	public void setStartDomainId(Long startDomainId) {
		this.startDomainId = startDomainId;
	}


	public Long getEndDomainId() {
		return this.endDomainId;
	}
	public void setEndDomainId(Long endDomainId) {
		this.endDomainId = endDomainId;
	}


	public Integer getDataStatus() {
		return this.dataStatus;
	}
	public void setDataStatus(Integer dataStatus) {
		this.dataStatus = dataStatus;
	}


	public Integer[] getDataStatuss() {
		return this.dataStatuss;
	}
	public void setDataStatuss(Integer[] dataStatuss) {
		this.dataStatuss = dataStatuss;
	}


	public Integer getStartDataStatus() {
		return this.startDataStatus;
	}
	public void setStartDataStatus(Integer startDataStatus) {
		this.startDataStatus = startDataStatus;
	}


	public Integer getEndDataStatus() {
		return this.endDataStatus;
	}
	public void setEndDataStatus(Integer endDataStatus) {
		this.endDataStatus = endDataStatus;
	}


	public Long getCreateTime() {
		return this.createTime;
	}
	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}


	public Long[] getCreateTimes() {
		return this.createTimes;
	}
	public void setCreateTimes(Long[] createTimes) {
		this.createTimes = createTimes;
	}


	public Long getStartCreateTime() {
		return this.startCreateTime;
	}
	public void setStartCreateTime(Long startCreateTime) {
		this.startCreateTime = startCreateTime;
	}


	public Long getEndCreateTime() {
		return this.endCreateTime;
	}
	public void setEndCreateTime(Long endCreateTime) {
		this.endCreateTime = endCreateTime;
	}


	public Long getModifyTime() {
		return this.modifyTime;
	}
	public void setModifyTime(Long modifyTime) {
		this.modifyTime = modifyTime;
	}


	public Long[] getModifyTimes() {
		return this.modifyTimes;
	}
	public void setModifyTimes(Long[] modifyTimes) {
		this.modifyTimes = modifyTimes;
	}


	public Long getStartModifyTime() {
		return this.startModifyTime;
	}
	public void setStartModifyTime(Long startModifyTime) {
		this.startModifyTime = startModifyTime;
	}


	public Long getEndModifyTime() {
		return this.endModifyTime;
	}
	public void setEndModifyTime(Long endModifyTime) {
		this.endModifyTime = endModifyTime;
	}


}


