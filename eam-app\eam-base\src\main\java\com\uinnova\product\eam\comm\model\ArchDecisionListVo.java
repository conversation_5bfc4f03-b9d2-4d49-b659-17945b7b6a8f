package com.uinnova.product.eam.comm.model;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ArchDecisionListVo {

    @Comment("决策id")
    private Long id;

    @Comment("决策标题")
    private String title;

    @Comment("决策状态 (枚举：1待受理，2重新申请，3待决策，4待发布，5已发布)")
    private Integer status;

    @Comment("涉及应用系统 ciName")
    private List<String> systemNameList;

    @Comment("申请人")
    private String proposer;

    @Comment("申请人loginCode")
    private String loginCode;

    @Comment("主持人")
    private String compere;

    @Comment("受理人")
    private String acceptor;

    @Comment("发布人")
    private String publisher;

    @Comment("每页条数")
    private Integer pageSize;

    @Comment("页码")
    private Integer pageNum;

    @Comment("创建时间")
    private Long createTime;

    @Comment("修改时间")
    private Long modifyTime;


}
