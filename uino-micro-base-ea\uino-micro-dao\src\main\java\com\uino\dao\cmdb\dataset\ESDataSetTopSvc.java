package com.uino.dao.cmdb.dataset;

import com.alibaba.fastjson.JSONObject;
import com.binary.jdbc.Page;
import com.uino.dao.AbstractESBaseDao;
import com.uino.bean.cmdb.base.dataset.style.DataSetTop;
import com.uino.dao.ESConst;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.search.sort.SortBuilder;
import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;

import jakarta.annotation.PostConstruct;
import java.util.List;

/**
 * @Classname DataSetTopDao
 * @Description 置顶
 * @Date 2020/3/19 10:12
 * @Created by sh
 */
@Repository
public class ESDataSetTopSvc extends AbstractESBaseDao<DataSetTop, JSONObject> {

    @Override
    public String getIndex() {
        return ESConst.INDEX_CMDB_DATASET_TOP;
    }

    @Override
    public String getType() {
        return ESConst.INDEX_CMDB_DATASET_TOP;
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }

    public List<DataSetTop> getSortListByQuery(QueryBuilder query, List<SortBuilder<?>> sorts){
        Page<DataSetTop> results = super.getSortListByQuery(1, 3000, query, sorts);
        Assert.isTrue(results.getTotalRows() <= 3000, "不分页查询一次最多拉取3000条数据，本次查询已超出");
        return results.getData();
    }
}

