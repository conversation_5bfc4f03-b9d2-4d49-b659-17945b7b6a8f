package com.uinnova.product.eam.web.analog.bean;

import java.io.Serializable;

import com.binary.framework.bean.annotation.Comment;

@Comment("建行外部数据查询参数")
public class CCBRequestParam implements Serializable{

	private static final long serialVersionUID = 1L;
	
	@Comment("开始时间")
	private String startTime;
	
	@Comment("结束时间")
	private String endTime;
	
	@Comment("查询类型:glb=流水号，tc=交易码，abr=批量关系，artr=实时关系")
	private String queryType;
	
	@Comment("查询关键字")
	private String word;

	public String getStartTime() {
		return startTime;
	}

	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}

	public String getEndTime() {
		return endTime;
	}

	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}

	public String getQueryType() {
		return queryType;
	}

	public void setQueryType(String queryType) {
		this.queryType = queryType;
	}

	public String getWord() {
		return word;
	}

	public void setWord(String word) {
		this.word = word;
	}

	
}
