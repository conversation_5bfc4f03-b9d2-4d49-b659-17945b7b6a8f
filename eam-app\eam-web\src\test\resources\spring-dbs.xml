<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:tx="http://www.springframework.org/schema/tx"
	   xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd

		  http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.0.xsd">

	<bean id="mybatisDataSource" class="org.apache.commons.dbcp.BasicDataSource" destroy-method="close" >	<!-- poolClass -->	
			<property name="driverClassName" value="${ds.jdbc.vmdb.driver}" />
			<property name="url" value="${ds.jdbc.vmdb.url}" />
			<property name="username" value="${ds.jdbc.vmdb.user}" />
			<property name="password" value="${ds.jdbc.vmdb.passwd}" />
			
			<property name="initialSize" value="${ds.conn.pool.initialSize}" />
			<property name="maxActive" value="${ds.conn.pool.maxActive}" />
			<property name="maxIdle" value="${ds.conn.pool.maxIdle}" />
			<property name="minIdle" value="${ds.conn.pool.minIdle}" />
			<property name="validationQuery" value="${ds.jdbc.vmdb.validationQuery}" />
			<property name="maxOpenPreparedStatements" value="${ds.conn.pool.maxOpenPreparedStatements}" />
		</bean>
	
	<bean id="sqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="mybatisDataSource" />
        <property name="mapperLocations" value="classpath*:sql/**/*SqlMap.xml"></property>
        <property name="plugins">
            <array>
                <bean class="com.github.pagehelper.PageInterceptor"></bean>
            </array>
        </property>
    </bean>
	
	<bean id="sqlSession" class="org.mybatis.spring.SqlSessionTemplate">
		 <constructor-arg index="0" ref="sqlSessionFactory" />
	</bean>
    		
	<!-- DAO -->
	<bean abstract="true" id="dao.parent.eam" >
		<property name="sqlSessionTemplate" ref="sqlSession"/>
	</bean>
	<bean abstract="true" id="dao.parent.vmdb" >
		<property name="sqlSessionTemplate" ref="sqlSession"/>
	</bean>

	<bean name="transactionManager"
        class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="mybatisDataSource"></property>
        <property name="enforceReadOnly" value="false"></property>
    </bean><!--  -->
	<tx:annotation-driven transaction-manager="transactionManager" />
	
</beans>
