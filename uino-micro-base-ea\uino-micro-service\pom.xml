<?xml version="1.0"?>
<project
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd"
	xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.uino</groupId>
		<artifactId>eam-base</artifactId>
		<version>1.0.0-SNAPSHOT</version>
	</parent>
	<artifactId>uino-eam-micro-service</artifactId>
	<name>uino-eam-micro-service</name>
	<properties>
		<skipTests>true</skipTests>
	</properties>

	<dependencies>
		<dependency>
			<groupId>com.uino</groupId>
			<artifactId>uino-eam-micro-dao</artifactId>
			<version>${project.version}</version>
			<exclusions>
				<exclusion>
					<groupId>org.elasticsearch</groupId>
					<artifactId>jna</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.uino</groupId>
			<artifactId>uino-eam-micro-monitor</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>com.uino</groupId>
			<artifactId>uino-eam-eventdrive</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-test</artifactId>
		</dependency>

		<!-- Unit Test dependency -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
			<exclusions>
				<exclusion>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-starter-logging</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>commons-fileupload</groupId>
			<artifactId>commons-fileupload</artifactId>
			<version>1.5</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-context-support</artifactId>
		</dependency>
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<scope>test</scope>
		</dependency>

		<!-- Unit Test dependency -->

		<!-- 二维码图片 -->
		<dependency>
			<groupId>com.google.zxing</groupId>
			<artifactId>core</artifactId>
			<version>3.3.0</version>
		</dependency>

		<!-- 主机地址识别 -->
		<dependency>
			<groupId>com.uino</groupId>
			<artifactId>productauth-sdk</artifactId>
			<version>1.5.0.RELEASE</version>
			<exclusions>
				<exclusion>
					<groupId>net.java.dev.jna</groupId>
					<artifactId>jna</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>net.java.dev.jna</groupId>
			<artifactId>jna</artifactId>
			<version>5.12.1</version>
		</dependency>
		<!-- common end -->
		<dependency>
			<groupId>com.alibaba.cloud</groupId>
			<artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
		</dependency>

		<dependency>
			<groupId>org.eclipse.angus</groupId>
			<artifactId>jakarta.mail</artifactId>
			<version>2.0.2</version>
		</dependency>

		<dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
            <version>2.9.0</version>
        </dependency>

		<!-- Jakarta Annotation API -->
		<dependency>
			<groupId>jakarta.annotation</groupId>
			<artifactId>jakarta.annotation-api</artifactId>
		</dependency>
	</dependencies>
	<build>
		<plugins>

		</plugins>
	</build>
</project>
