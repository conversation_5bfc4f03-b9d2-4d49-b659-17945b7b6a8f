package com.uinnova.product.eam.model.diagram;

import lombok.Data;

import java.util.List;

/**
 * 元模型node节点实体
 * <AUTHOR>
 * @date 2022/5/25
 */
@Data
public class VisualModelNode {
    private Long key;
    private Long classId;
    private String classCode;
    private Boolean visible;
    private String image;
    private String loc;
    private String note;
    private String noteWidth;
    private String noteHeight;
    private String label;
    private List<String> anchors;
    private Boolean isClipping;
    private String size;
    private Boolean isSubClass;
    private Double width;
    private Double height;
    private String clippingFill;
    private Double clippingWidth;
    private Double clippingHeight;
    private Double clippingStrokeWidth;
    private String category;
    private String clippingStroke;
    private String clippingFigure;
}
