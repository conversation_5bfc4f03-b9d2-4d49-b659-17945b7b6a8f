package com.uino.bean.cmdb.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 
 * <AUTHOR>
 *
 */
@ApiModel(value = "查询结果", description = "查询结果")
public class ESSearchBase implements Serializable {
    private static final long serialVersionUID = 6398899597432596632L;
    @ApiModelProperty(value = "页码", example = "1",  required = true)
    private int pageNum = 1;

    @ApiModelProperty(value = "每页数量", example = "20",  required = true)
    private int pageSize = 20;

    public int getPageNum() {
        return pageNum;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

}
