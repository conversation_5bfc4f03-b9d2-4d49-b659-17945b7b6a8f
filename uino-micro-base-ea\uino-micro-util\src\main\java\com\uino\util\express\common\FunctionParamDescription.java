package com.uino.util.express.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
 * @Title: FunctionParamDescription
 * @Description: function param description
 * @Author: YGQ
 * @Create: 2021-08-26 14:18
 **/
@Data
@ApiModel(value = "方法参数描述")
public class FunctionParamDescription {

    @ApiModelProperty("参数名")
    private String paramName;

    @ApiModelProperty("参数类型")
    private Class<?> paramType;

    @ApiModelProperty("参数描述")
    private String paramDescription;
}
