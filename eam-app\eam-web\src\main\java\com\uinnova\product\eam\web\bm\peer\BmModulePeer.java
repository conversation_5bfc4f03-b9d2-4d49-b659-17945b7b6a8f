package com.uinnova.product.eam.web.bm.peer;

import com.uinnova.product.eam.base.model.ModuleFieldInfo;
import com.uinnova.product.eam.base.model.SpecificationInfo;
import com.uinnova.product.eam.service.BmModuleSvc;
import com.uinnova.product.eam.web.bm.bean.ReleaseModuleDiagramDTO;
import com.uinnova.product.vmdb.provider.rlt.bean.CcCiRltInfo;
import com.uino.bean.cmdb.base.ESCIInfo;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class BmModulePeer {

    @Resource
    BmModuleSvc bmModuleSvc;

    public List<ESCIInfo> queryBelongToDomainByTask(String diagramId) {
        return bmModuleSvc.queryBelongToDomainByTask(diagramId);
    }

    public SpecificationInfo domainAndModuleSpecification(String diagramId) {
        return bmModuleSvc.domainAndModuleSpecification(diagramId);
    }

    public String releaseModuleDiagram(ReleaseModuleDiagramDTO dto) {
        return bmModuleSvc.releaseModuleDiagram(dto.getDiagramId(), dto.getReleaseDesc(), dto.getDirId(), dto.getTargetCiCode());
    }

    public String createTestAndEntityRltByTestCiCode(String ciCode) {
        return bmModuleSvc.createTestAndEntityRltByTestCiCode(ciCode);
    }

    public List<CcCiRltInfo> getTargetCiAndRltBySourceCiCode(String ciCode, String ownerCode) {
        return bmModuleSvc.getTargetCiAndRltBySourceCiCode(ciCode,ownerCode);
    }

    public ModuleFieldInfo quickDrawing(String diagramId, String ciCode) {
        return bmModuleSvc.quickDrawing(diagramId, ciCode);
    }

    public Map<String, Boolean> checkModuleDomain(String source, List<String> ciCodes) {
        return bmModuleSvc.checkModuleDomain(source, ciCodes);
    }
}
