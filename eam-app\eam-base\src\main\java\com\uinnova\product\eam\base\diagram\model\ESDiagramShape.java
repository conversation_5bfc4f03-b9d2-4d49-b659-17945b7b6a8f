package com.uinnova.product.eam.base.diagram.model;

import java.io.Serializable;
import java.util.Arrays;

/**
 * @Classname
 * @Description
 * <AUTHOR>
 * @Date 2021-06-03-15:11
 */
public class ESDiagramShape implements Serializable {
    private static final Long serialVersionUID = 1L;

    private String[] id;
    private String originalId;
    private String type;
    private boolean opened;

    public String[] getId() {
        return id;
    }

    public void setId(String[] id) {
        this.id = id;
    }

    public String getOriginalId() {
        return originalId;
    }

    public void setOriginalId(String originalId) {
        this.originalId = originalId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public boolean isOpened() {
        return opened;
    }

    public void setOpened(boolean opened) {
        this.opened = opened;
    }

    @Override
    public String toString() {
        return "DiagramShape{" +
                "id=" + Arrays.toString(id) +
                ", originalId=" + originalId +
                ", type='" + type + '\'' +
                ", opened=" + opened +
                '}';
    }
}
