package com.uino.provider.feign.permission;

import java.util.List;

import com.uino.bean.permission.query.CSysRoleDataModuleRlt;
import org.elasticsearch.index.query.QueryBuilder;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.binary.jdbc.Page;
import com.uino.bean.permission.base.SysDataModule;
import com.uino.bean.permission.base.SysRole;
import com.uino.bean.permission.base.SysRoleDataModuleRlt;
import com.uino.bean.permission.base.SysRoleModuleRlt;
import com.uino.bean.permission.base.SysUserDataModuleRlt;
import com.uino.bean.permission.base.SysUserModuleRlt;
import com.uino.bean.permission.base.SysUserRoleRlt;
import com.uino.bean.permission.business.DataRole;
import com.uino.bean.permission.business.ModuleNodeInfo;
import com.uino.bean.permission.business.request.OptionUserModuleAuthRequestDto;
import com.uino.bean.permission.query.CAuthBean;
import com.uino.bean.permission.query.SearchKeywordBean;
import com.uino.provider.feign.config.BaseFeignConfig;

/**
 * 
 * <AUTHOR>
 *
 */
@Component
@FeignClient(name = "${feign-server-name:tarsier-platform}", path = "${feign-server-path:tarsier-platform}/feign/role", configuration = {
        BaseFeignConfig.class})
public interface RoleFeign {

    /**
     * 保存/更新
     * 
     * @param role
     * @return
     */
    @PostMapping("saveOrUpdate")
    public Long saveOrUpdate(@RequestBody(required = false) SysRole role);

    /**
     * 批量保存/更新
     *
     * @param roles
     * @return
     */
    @PostMapping("saveOrUpdateBatch")
    public Integer saveOrUpdateBatch(@RequestParam(value="domainId") Long domainId,@RequestBody(required = false) List<SysRole> roles);
    /**
     * 删除
     * 
     * @param roleId
     * @return
     */
    @PostMapping("deleteById")
    public Integer deleteById(@RequestParam(value = "roleId", required = false) Long roleId);

    /**
     * 角色搜索--分页
     * 
     * @param bean
     * @return
     */
    @PostMapping("getRolePageByQuery")
    public Page<SysRole> getRolePageByQuery(@RequestBody(required = false) SearchKeywordBean bean);

    /**
     * 添加角色和菜单的关系
     * 
     * @param bean
     * @return
     */
    @PostMapping("addRoleMenuRlt")
    public Integer addRoleMenuRlt(@RequestParam(value="domainId") Long domainId,@RequestBody(required = false) List<SysRoleModuleRlt> bean);

	/**
	 * 
	 * @description 添加角色和数据模块的关系
	 * @author: ZMJ
	 * @param domainId
	 *            所属域id
	 * @param rlts
	 *            角色-数据权限关系集合
	 * @param isComplete
	 *            是否全量(全量时仅支持保存一个角色的权限数据)
	 * @return
	 * @example
	 */
	@PostMapping("addRoleDataModuleRlt")
	public Integer addRoleDataModuleRlt(@RequestParam(value = "domainId") Long domainId,
			@RequestBody List<SysRoleDataModuleRlt> rlts,
			@RequestParam(value = "isComplete") boolean isComplete);

    /**
     * 添加用户和菜单的关系
     * 
     * @param bean
     * @return
     */
    @PostMapping("addUserMenuRlt")
    public Integer addUserMenuRlt(@RequestParam(value="domainId") Long domainId,@RequestBody(required = false) List<SysUserModuleRlt> bean);

    /**
     * 添加用户和数据模块的关系
     * 
     * @param bean
     * @return
     */
    @PostMapping("addUserDataModuleRlt")
    public Integer addUserDataModuleRlt(@RequestParam(value="domainId")Long domainId,@RequestBody(required = false) List<SysUserDataModuleRlt> bean);

    /**
     * 注册数据权限菜单项
     * 
     * @param dataModule
     * @return
     */
    @PostMapping("addDataModuleMenu")
    public Long addDataModuleMenu(@RequestBody SysDataModule dataModule);

    /**
     * 获取CI分类数据项
     * 
     * @return
     */
    @PostMapping("getDataRoleByCIClass")
    public List<DataRole> getDataRoleByCIClass(@RequestParam(value="domainId") Long domainId);

    /**
     * 获取CI标签数据项
     * 
     * @return
     */
    @PostMapping("getDataRoleByCITag")
    public List<DataRole> getDataRoleByCITag(@RequestParam(value = "domainId") Long domainId);

    /**
     * 获取数据权限菜单
     * 
     * @return
     */
    @PostMapping("getAllDataRoleMenu")
    public List<SysDataModule> getAllDataRoleMenu(@RequestParam(value="domainId")Long domainId);

    /**
     * 获取菜单
     * 
     * @return
     */
    @PostMapping("getAllMenu")
    public ModuleNodeInfo getAllMenu(@RequestParam(value="domainId") Long domainId);

    /**
     * 获取角色下的菜单权限
     * 
     * @param roleId
     * @return
     */
    @PostMapping("getAuthMenuByRoleId")
    public List<SysRoleModuleRlt> getAuthMenuByRoleId(@RequestParam(value = "roleId", required = false) Long roleId);

    /**
     * 获取角色下的数据权限
     * 
     * @param bean
     * @return
     */
    @PostMapping("getAuthDataRoleByRoleId")
    public List<SysRoleDataModuleRlt> getAuthDataRoleByRoleId(@RequestBody(required = false) CAuthBean bean);

    /**
     * 获取用户下的所有数据权限（roleId为空或不存在，表示是来自于用户本身）
     * 
     * @param bean
     * @return
     */
    @PostMapping("getAuthDataRoleByUserId")
    public List<SysRoleDataModuleRlt> getAuthDataRoleByUserId(@RequestBody(required = false) CAuthBean bean);

    /**
     * 获取用户下的所有菜单权限（roleId为空或不存在，表示是来自于用户本身）
     * 
     * @param userId
     * @return
     */
    @PostMapping("getAuthMenuByUserId")
    public List<SysRoleModuleRlt> getAuthMenuByUserId(@RequestParam(value = "userId", required = false) Long userId);

    /**
     * 获取某个用户绑定的角色信息
     * 
     * @param userId
     * @return
     */
    @PostMapping("getRolesByUserId")
    public List<SysRole> getRolesByUserId(@RequestParam(value = "userId", required = false) Long userId);

    /**
     * 根据数据模块id-转发数据模块数据源地址获取 数据模块数据
     * 
     * @param dataModuleId
     * @return
     */
    @PostMapping("getDataModuleDataById")
    public Object getDataModuleDataById(@RequestParam(value = "moduleId", required = false) Long dataModuleId);

    /**
     * 获取用户下的数据权限
     * 
     * @param bean
     * @return
     */
    @PostMapping("getUserAuthDataRoleByUserId")
    public List<SysRoleDataModuleRlt> getUserAuthDataRoleByUserId(@RequestBody(required = false) CAuthBean bean);

    /**
     * 获取用户下的所有角色数据权限（角色合并后的权限）
     * 
     * @param bean
     * @return
     */
    @PostMapping("getRoleAuthDataRoleByUserId")
    public List<SysRoleDataModuleRlt> getRoleAuthDataRoleByUserId(@RequestBody(required = false) CAuthBean bean);

    /**
     * 获取某个组织的角色
     * 
     * @param orgId
     * @return
     */
    @PostMapping("getRolesByOrgId")
    public List<SysRole> getRolesByOrgId(@RequestParam(value = "id", required = false) Long orgId);

    @PostMapping("countByCondition")
    public long countByCondition(QueryBuilder query);

    @PostMapping("getRolesByQuery")
    public List<SysRole> getRolesByQuery(QueryBuilder query);

    @PostMapping("optionUserModuleAuth")
    public void optionUserModuleAuth(@RequestBody(required = false) OptionUserModuleAuthRequestDto req);

    @PostMapping("getUserRoleRltByRoleId")
    public List<SysUserRoleRlt> getUserRoleRltByRoleId(@RequestParam(value = "id", required = false) Long roleId);

    @PostMapping("getRoleDataModuleRltByCdt")
    public List<SysRoleDataModuleRlt> getRoleDataModuleRltByCdt(CSysRoleDataModuleRlt cdt);

    @PostMapping("deleteRoleDataModuleRlt")
    public Integer deleteRoleDataModuleRlt(CSysRoleDataModuleRlt cdt);
}
