package com.uinnova.product.eam.model.dto;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

/**
 * <AUTHOR> wangchunlei
 * @Date :
 * @Description : 添加分类数据的属性编码自动生成参数
 */
@Data
public class CiCodeDto {

    @Comment("前缀")
    private String defVal;

    @Comment("分类主键")
    private String classCode;

    @Comment("分类属性id")
    private Long attrDefId;

    @Comment("属性名称")
    private String proName;

    @Comment("编码初始值")
    private String startNum;

}
