package com.uino.monitor.tp.common;

import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.entity.ContentType;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.nio.entity.NStringEntity;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.*;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.io.IOException;

/**
 * ES操作抽象类 -- 所有方法需要传入索引
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class SpecifyIndexDao {

    private static RestHighLevelClient client = null;

    @Value("${isAuth}")
    private boolean isAuth;

    @Value("${esUser}")
    private String esUser;

    @Value("${esPwd}")
    private String esPwd;

    @Value("${esIps}")
    private String esIps;

    @PostConstruct
    public void init() {
        log.info("init SpecifyIndexDao");
        getClient();
    }

    private String getType(String index) {
        String indexPrefix = "performance";
        return index.contains(indexPrefix) ? indexPrefix : index;
    }

    public RestHighLevelClient getClient() {
        String[] ips = esIps.split(";");
        HttpHost[] httpHosts = new HttpHost[ips.length];
        for (int i = 0; i < ips.length; i++) {
            String[] ipPorts = ips[i].split("\\:");
            httpHosts[i] = new HttpHost(ipPorts[0], Integer.parseInt(ipPorts[1]), "http");
        }
        RestClientBuilder builder = initBuilder(httpHosts);
        if (client == null) {
            log.info(esIps);
            client = new RestHighLevelClient(builder);
        } else {
            boolean isAlive;
            try {
                isAlive = client.ping(RequestOptions.DEFAULT);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                isAlive = false;
                log.error("ping error!");
                if (client != null) {
                    try {
                        client.close();
                    } catch (IOException e1) {
                        log.error("close client error!");
                    }
                }
            }
            if (!isAlive) {
                client = new RestHighLevelClient(builder);
            }
        }
        return client;
    }

    /**
     * <b>初始化
     *
     * @param httpHosts
     * @return
     */
    public RestClientBuilder initBuilder(HttpHost[] httpHosts) {
        RestClientBuilder builder = RestClient.builder(httpHosts);
        builder.setRequestConfigCallback(requestConfigCallback -> {
            requestConfigCallback.setConnectTimeout(60000);
            requestConfigCallback.setSocketTimeout(60000);
            requestConfigCallback.setConnectionRequestTimeout(60000);
            return requestConfigCallback;
        });
        builder.setHttpClientConfigCallback(httpAsyncClientBuilder -> {
            httpAsyncClientBuilder.setMaxConnTotal(300);
            httpAsyncClientBuilder.setMaxConnPerRoute(300);
            if (isAuth) {
                CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
                credentialsProvider.setCredentials(AuthScope.ANY, new UsernamePasswordCredentials(esUser, esPwd));
                httpAsyncClientBuilder.setDefaultCredentialsProvider(credentialsProvider);
            }
            return httpAsyncClientBuilder;
        });
        return builder;
    }


    /**
     * <b>根据dsl查询
     *
     * @param index 索引名
     * @param dsl   dsl语句
     * @return
     */
    public Response searchListByDsl(String index, String dsl) {
        RestClient lowClient = getClient().getLowLevelClient();
        HttpEntity entity = new NStringEntity(dsl, ContentType.APPLICATION_JSON);
        Response res = null;
        try {
            String url = "/" + index + "/" + getType(index) + "/_search";
            Request request = new Request("POST", url);
            request.setEntity(entity);
            res = lowClient.performRequest(request);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return res;
    }

    public SearchResponse searchByQueryAndAgg(String index, QueryBuilder query, AggregationBuilder agg) {
        SearchRequest searchRequest = new SearchRequest(index);
        searchRequest.types(getType(index));
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.size(0);
        searchRequest.source(searchSourceBuilder);
        searchSourceBuilder.query(query);
        searchRequest.source().aggregation(agg);
        SearchResponse searchResponse = null;
        try {
            searchResponse = getClient().search(searchRequest, RequestOptions.DEFAULT);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return searchResponse;
    }

}
