package com.uinnova.product.vmdb.comm.model.image;

import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;

/**
 * 
 * <AUTHOR>
 *
 */
@Comment("图像表[CC_IMAGE]")
public class CcImage implements EntityBean {
    private static final long serialVersionUID = 1L;

    @Comment("ID[ID]")
    private Long id;

    @Comment("图像名称[IMG_NAME]")
    private String imgName;

    @Comment("图像全名[IMG_FULL_NAME]    图像全名:目录名+图像名, 中间以|分隔")
    private String imgFullName;

    @Comment("图像分类[IMG_GROUP]    图像分类:1=CI分类图标 2=视图背景图")
    private Integer imgGroup;

    @Comment("所属目录[DIR_ID]")
    private Long dirId;

    @Comment("图像描述[IMG_DESC]")
    private String imgDesc;

    @Comment("图像格式[IMG_TYPE]")
    private Integer imgType;

    @Comment("图像类型[IMG_THEME]    图像类型:红、黄、蓝等，待续")
    private Integer imgTheme;

    @Comment("保存位置[IMG_PATH]")
    private String imgPath;

    @Comment("图像范围[IMG_RANGE]    图像范围:1=公共 2=私有")
    private Integer imgRange;

    @Comment("图标大小[IMG_SIZE]    图标大小:单位：byte")
    private Integer imgSize;

    @Comment("图标宽度[IMG_WIDTH]    图标宽度:单位：像素")
    private Integer imgWidth;

    @Comment("图标高度[IMG_HEIGH]    图标高度:单位：像素")
    private Integer imgHeigh;

    @Comment("上传人ID[UPOR_ID]")
    private Long uporId;

    @Comment("上传人姓名[UPOR_NAME]")
    private String uporName;

    @Comment("上传时间[UP_TIME]")
    private Long upTime;

    @Comment("关联图标1[RLT_IMG_ID_1]")
    private Long rltImgId1;

    @Comment("关联图标2[RLT_IMG_ID_2]")
    private Long rltImgId2;

    @Comment("关联图标3[RLT_IMG_ID_3]")
    private Long rltImgId3;

    @Comment("备用_1[CUSTOM_1]")
    private Long custom1;

    @Comment("备用_2[CUSTOM_2]")
    private Long custom2;

    @Comment("备用_3[CUSTOM_3]")
    private Long custom3;

    @Comment("备用_4[CUSTOM_4]")
    private String custom4;

    @Comment("备用_5[CUSTOM_5]")
    private String custom5;

    @Comment("备用_6[CUSTOM_6]")
    private String custom6;

    @Comment("搜索字段[SEARCH_FIELD]    搜索字段:图像名称")
    private String searchField;

    @Comment("排序字段[ORDER_NO]")
    private Long orderNo;

    @Comment("所属域[DOMAIN_ID]")
    private Long domainId;

    @Comment("创建人[CREATOR]")
    private String creator;

    @Comment("修改人[MODIFIER]")
    private String modifier;

    @Comment("数据状态[DATA_STATUS]    数据状态:数据状态：1-正常 0-删除")
    private Integer dataStatus;

    @Comment("创建时间[CREATE_TIME]    创建时间:yyyyMMddHHmmss")
    private Long createTime;

    @Comment("修改时间[MODIFY_TIME]    修改时间:yyyyMMddHHmmss")
    private Long modifyTime;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getImgName() {
        return this.imgName;
    }

    public void setImgName(String imgName) {
        this.imgName = imgName;
    }

    public String getImgFullName() {
        return this.imgFullName;
    }

    public void setImgFullName(String imgFullName) {
        this.imgFullName = imgFullName;
    }

    public Integer getImgGroup() {
        return this.imgGroup;
    }

    public void setImgGroup(Integer imgGroup) {
        this.imgGroup = imgGroup;
    }

    public Long getDirId() {
        return this.dirId;
    }

    public void setDirId(Long dirId) {
        this.dirId = dirId;
    }

    public String getImgDesc() {
        return this.imgDesc;
    }

    public void setImgDesc(String imgDesc) {
        this.imgDesc = imgDesc;
    }

    public Integer getImgType() {
        return this.imgType;
    }

    public void setImgType(Integer imgType) {
        this.imgType = imgType;
    }

    public Integer getImgTheme() {
        return this.imgTheme;
    }

    public void setImgTheme(Integer imgTheme) {
        this.imgTheme = imgTheme;
    }

    public String getImgPath() {
        return this.imgPath;
    }

    public void setImgPath(String imgPath) {
        this.imgPath = imgPath;
    }

    public Integer getImgRange() {
        return this.imgRange;
    }

    public void setImgRange(Integer imgRange) {
        this.imgRange = imgRange;
    }

    public Integer getImgSize() {
        return this.imgSize;
    }

    public void setImgSize(Integer imgSize) {
        this.imgSize = imgSize;
    }

    public Integer getImgWidth() {
        return this.imgWidth;
    }

    public void setImgWidth(Integer imgWidth) {
        this.imgWidth = imgWidth;
    }

    public Integer getImgHeigh() {
        return this.imgHeigh;
    }

    public void setImgHeigh(Integer imgHeigh) {
        this.imgHeigh = imgHeigh;
    }

    public Long getUporId() {
        return this.uporId;
    }

    public void setUporId(Long uporId) {
        this.uporId = uporId;
    }

    public String getUporName() {
        return this.uporName;
    }

    public void setUporName(String uporName) {
        this.uporName = uporName;
    }

    public Long getUpTime() {
        return this.upTime;
    }

    public void setUpTime(Long upTime) {
        this.upTime = upTime;
    }

    public Long getRltImgId1() {
        return this.rltImgId1;
    }

    public void setRltImgId1(Long rltImgId1) {
        this.rltImgId1 = rltImgId1;
    }

    public Long getRltImgId2() {
        return this.rltImgId2;
    }

    public void setRltImgId2(Long rltImgId2) {
        this.rltImgId2 = rltImgId2;
    }

    public Long getRltImgId3() {
        return this.rltImgId3;
    }

    public void setRltImgId3(Long rltImgId3) {
        this.rltImgId3 = rltImgId3;
    }

    public Long getCustom1() {
        return this.custom1;
    }

    public void setCustom1(Long custom1) {
        this.custom1 = custom1;
    }

    public Long getCustom2() {
        return this.custom2;
    }

    public void setCustom2(Long custom2) {
        this.custom2 = custom2;
    }

    public Long getCustom3() {
        return this.custom3;
    }

    public void setCustom3(Long custom3) {
        this.custom3 = custom3;
    }

    public String getCustom4() {
        return this.custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }

    public String getCustom5() {
        return this.custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }

    public String getCustom6() {
        return this.custom6;
    }

    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }

    public String getSearchField() {
        return this.searchField;
    }

    public void setSearchField(String searchField) {
        this.searchField = searchField;
    }

    public Long getOrderNo() {
        return this.orderNo;
    }

    public void setOrderNo(Long orderNo) {
        this.orderNo = orderNo;
    }

    public Long getDomainId() {
        return this.domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }

    public String getCreator() {
        return this.creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getModifier() {
        return this.modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public Integer getDataStatus() {
        return this.dataStatus;
    }

    public void setDataStatus(Integer dataStatus) {
        this.dataStatus = dataStatus;
    }

    public Long getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getModifyTime() {
        return this.modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }

}
