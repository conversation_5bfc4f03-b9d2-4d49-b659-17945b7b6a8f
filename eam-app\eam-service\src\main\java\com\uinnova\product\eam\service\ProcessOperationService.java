package com.uinnova.product.eam.service;

import com.uinnova.product.eam.model.FlowOperationStatisticsDto;

import java.util.List;
import java.util.Map;

/**
 * 流程管理/流程运行情况相关接口
 *
 * <AUTHOR>
 * @since 2024/12/30
 */
public interface ProcessOperationService {

    Map<String, Long> getProcessOperationStatus(FlowOperationStatisticsDto dto);

    List<Map<String, Long>> getProcessFinishCount(FlowOperationStatisticsDto dto);

    List<Map<String, String>> getActivityRuningDuration(FlowOperationStatisticsDto dto);

}
