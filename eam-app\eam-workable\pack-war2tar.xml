<assembly
	xmlns="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.0 http://maven.apache.org/xsd/assembly-1.1.0.xsd">
	<id>deploy</id>
	<formats>
		<format>tar.gz</format>
	</formats>
	<fileSets>
		<fileSet>
			<directory>${project.basedir}/src/main/resources</directory>
			<excludes>
				<exclude>**/*.class</exclude>
			</excludes>
			<outputDirectory>/</outputDirectory>
		</fileSet>
		<fileSet>
			<directory>${project.basedir}/src/shell</directory>
			<fileMode>0755</fileMode>
			<lineEnding>unix</lineEnding>
			<outputDirectory>/</outputDirectory>
		</fileSet>
		<fileSet>
			<directory>${project.build.directory}</directory>
			<includes>
				<include>${project.artifactId}.jar</include>
			</includes>
			<outputDirectory>/</outputDirectory>
		</fileSet>
	</fileSets>
</assembly>
