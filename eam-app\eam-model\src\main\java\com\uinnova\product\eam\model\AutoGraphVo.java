package com.uinnova.product.eam.model;

import com.uinnova.product.eam.model.vo.DFAutoServiceGraphLevelData;

import java.util.List;

public class AutoGraphVo {

    private Value value;
    private String key;

    public static class LayoutScheme {
        private int columnSpacing;
        private int layoutType;
        private String layoutDirection;
        private int layerSpacing;

        // Getters and Setters
        public int getColumnSpacing() {
            return columnSpacing;
        }

        public void setColumnSpacing(int columnSpacing) {
            this.columnSpacing = columnSpacing;
        }

        public int getLayoutType() {
            return layoutType;
        }

        public void setLayoutType(int layoutType) {
            this.layoutType = layoutType;
        }

        public String getLayoutDirection() {
            return layoutDirection;
        }

        public void setLayoutDirection(String layoutDirection) {
            this.layoutDirection = layoutDirection;
        }

        public int getLayerSpacing() {
            return layerSpacing;
        }

        public void setLayerSpacing(int layerSpacing) {
            this.layerSpacing = layerSpacing;
        }
    }

    public static class DataScheme {
        private List<Long> relatedRltIds;
        private String coreCiCode;
        private List<Long> relatedClassIds;
        private long ciClass;
        private List<DFAutoServiceGraphLevelData> layerTree;

        // Getters and Setters
        public List<Long> getRelatedRltIds() {
            return relatedRltIds;
        }

        public void setRelatedRltIds(List<Long> relatedRltIds) {
            this.relatedRltIds = relatedRltIds;
        }

        public String getCoreCiCode() {
            return coreCiCode;
        }

        public void setCoreCiCode(String coreCiCode) {
            this.coreCiCode = coreCiCode;
        }

        public List<Long> getRelatedClassIds() {
            return relatedClassIds;
        }

        public void setRelatedClassIds(List<Long> relatedClassIds) {
            this.relatedClassIds = relatedClassIds;
        }

        public long getCiClass() {
            return ciClass;
        }

        public void setCiClass(long ciClass) {
            this.ciClass = ciClass;
        }

        public List<DFAutoServiceGraphLevelData> getLayerTree() {
            return layerTree;
        }

        public void setLayerTree(List<DFAutoServiceGraphLevelData> layerTree) {
            this.layerTree = layerTree;
        }
    }

    public static class ArtifactScheme {
        private long artifactId;

        // Getters and Setters
        public long getArtifactId() {
            return artifactId;
        }

        public void setArtifactId(long artifactId) {
            this.artifactId = artifactId;
        }
    }

    public static class Style {
        //线条类型：Orthogonal=正交、Normal=直线、Bezier=曲线
        private String routing;

        public String getRouting() {
            return routing;
        }

        public void setRouting(String routing) {
            this.routing = routing;
        }
    }

    public static class Value {
        private Object layoutScheme;
        private DataScheme dataScheme;
        private ArtifactScheme artifactScheme;
        private Style style;

        public Value(Object layoutScheme, DataScheme dataScheme, ArtifactScheme artifactScheme, Style style) {
            this.layoutScheme = layoutScheme;
            this.dataScheme = dataScheme;
            this.artifactScheme = artifactScheme;
            this.style = style;
        }

        // Getters and Setters
        public Object getLayoutScheme() {
            return layoutScheme;
        }

        public void setLayoutScheme(Object layoutScheme) {
            this.layoutScheme = layoutScheme;
        }

        public DataScheme getDataScheme() {
            return dataScheme;
        }

        public void setDataScheme(DataScheme dataScheme) {
            this.dataScheme = dataScheme;
        }

        public ArtifactScheme getArtifactScheme() {
            return artifactScheme;
        }

        public void setArtifactScheme(ArtifactScheme artifactScheme) {
            this.artifactScheme = artifactScheme;
        }

        public Style getStyle() {
            return style;
        }

        public void setStyle(Style style) {
            this.style = style;
        }
    }

    // Getters and Setters
    public Value getValue() {
        return value;
    }

    public void setValue(Value value) {
        this.value = value;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }
}