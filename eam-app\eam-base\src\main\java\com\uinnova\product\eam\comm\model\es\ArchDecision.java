package com.uinnova.product.eam.comm.model.es;

import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;
import lombok.Data;
import java.util.List;

/**
 * 架构决策
 * <AUTHOR>
 */
@Data
public class ArchDecision implements EntityBean {
    @Comment("主键")
    private Long id;
    @Comment("标题")
    private String title;
    @Comment("描述")
    private String desc;
    @Comment("status:状态(枚举：1待受理，2重新申请，3待决策，4待发布，5已发布)")
    private Integer status;
    @Comment("备选方案或设想")
    private String planInfo;
    @Comment("涉及的应用系统")
    private List<String> systemIds;
    @Comment("涉及的产品")
    private List<String> productIds;
    @Comment("涉及的中心及团队")
    private List<String> teamIds;
    @Comment("申请人")
    private String proposer;
    @Comment("受理人")
    private String acceptor;
    @Comment("发布人")
    private String publisher;

    @Comment("主持人")
    private String compere;
    @Comment("参与人")
    private List<String> players;
    @Comment("外脑")
    private String helper;

    @Comment("决策类型(枚举：1G-规划立项，2Y-研发实施，3C-测试部署，4S-生产运维，5W-做什么类决策，6H-怎么做类决策，7X-其他层面的决策)")
    private Integer type;
    @Comment("决策结果")
    private String result;
    @Comment("跟踪机制")
    private String track;
    @Comment("决策编号")
    private String number;
    @Comment("研发阶段类型")
    private String researchStage;
    @Comment("决策输出类型")
    private String decisionStage;
    @Comment("驳回原因")
    private String reason;
    @Comment("备注")
    private String remarks;
    @Comment("任务id")
    private String taskId;
    @Comment("流程实例id")
    private String processInstanceId;
    @Comment("申请发起时间")
    private Long createTime;
    @Comment("受理完成时间")
    private Long acceptTime;
    @Comment("决策修改时间")
    private Long modifyTime;
}
