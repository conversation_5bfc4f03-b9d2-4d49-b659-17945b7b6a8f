package com.uino.bean.cmdb.enums;


public enum DirTypeEnum {
    CLASS(1, "分类领域文件夹"),
    RLT(2, "关系领域文件夹"),
    IMAGE(3, "2D图标文件夹"),
    TAG(4, "标签组文件夹"),
    IMAGE3D(5, "3D图标文件夹"),
    VEDIO(6, "视频文件夹"),
    DOCUMENT(7, "文档文件夹");

    private Integer type;

    private String name;

    DirTypeEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static DirTypeEnum valueOf(Integer type) {
        if (CLASS.type.equals(type)) {
            return CLASS;
        } else if (RLT.type.equals(type)) {
            return RLT;
        } else if (IMAGE.type.equals(type)) {
            return IMAGE;
        } else if (TAG.type.equals(type)) {
            return TAG;
        } else if (IMAGE3D.type.equals(type)) {
            return IMAGE3D;
        } else if (VEDIO.type.equals(type)) {
            return VEDIO;
        } else if (DOCUMENT.type.equals(type)) {
            return DOCUMENT;
        } else {
            return null;
        }
    }
}
