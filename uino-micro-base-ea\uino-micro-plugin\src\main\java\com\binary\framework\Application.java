package com.binary.framework;

import com.binary.framework.exception.FrameworkException;
import org.springframework.context.ApplicationContext;

import java.util.ArrayList;
import java.util.List;

public class Application {

	
	private static final List<ApplicationListener> listeners = new ArrayList<ApplicationListener>();
	
	public Application() {
	}
	public Application(List<ApplicationListener> listeners) {
		setApplicationListeners(listeners);
	}
	
	
	
	public void setApplicationListeners(List<ApplicationListener> listeners) {
		if(listeners!=null && listeners.size()>0) {
			for(int i=0; i<listeners.size(); i++) {
				ApplicationListener listener = listeners.get(i);
				if(Application.listeners.contains(listener)) {
					throw new FrameworkException(" the application-listener '"+listener.toString()+"' is exists! ");
				}
				Application.listeners.add(listener);
			}
		}
	}

	
	
	
	
	public static boolean containsListener(ApplicationListener listener) {
		return listeners.contains(listener);
	}
	
	
	public static void addListener(ApplicationListener listener) {
		if(listeners.contains(listener)) {
			throw new FrameworkException(" the ApplicationListener '"+listener.getClass().getName()+"' is exists! ");
		}
		listeners.add(listener);
	}
	
	
	public static boolean removeListener(ApplicationListener listener) {
		return listeners.remove(listener);
	}
	
	
	
	
	
	
	public static void afterInitialization(ApplicationContext context) {
		for(int i=0; i<listeners.size(); i++) {
			listeners.get(i).afterInitialization(context);
		}
	}
	
	
	
	
	
	
	
	
}
