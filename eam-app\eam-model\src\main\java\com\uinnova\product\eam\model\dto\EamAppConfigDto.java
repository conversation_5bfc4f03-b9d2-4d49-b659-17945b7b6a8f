package com.uinnova.product.eam.model.dto;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.util.List;

@Data
public class EamAppConfigDto {

    @Comment("ID")
    private Long id;

    @Comment("卡片id")
    private Long cardId;

    @Comment("所属域")
    private Long domainId;

    @Comment("分类的classCode")
    private String classCode;

    @Comment("配置的分类属性")
    private List<String> attrList;

    @Comment("配置的分类属性-属性id --new")
    private List<Long> attrIds;

    @Comment("创建时间")
    private Long createTime;

    @Comment("修改时间")
    private Long modifyTime;

    @Comment("创建人")
    private String creator;

    @Comment("修改人")
    private String modifier;




}
