package com.uino.bean.sys.base;

import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.util.Assert;

import com.binary.framework.bean.annotation.Comment;
import com.uino.bean.permission.business.IValidDto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Builder.Default;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 
 * <AUTHOR>
 *
 */
@Comment("字典表类定义")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value="字典表类定义",description = "字典表类定义")
public class ESDictionaryClassInfo implements Serializable, IValidDto {
    /**
     * 
     */
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value="id",example = "123")
    @Comment("ID[ID]")
    private Long id;

    @ApiModelProperty(value="分类代码,唯一且不可修改")
    @Comment("分类代码[DICT_CODE] 唯一且不可修改")
    private String dictCode;

    @ApiModelProperty(value="分类名称")
    @Comment("分类名称[DICT_NAME]")
    private String dictName;

    @ApiModelProperty(value="显示排序",example = "1")
    @Comment("显示排序[ORDER_NO]")
    private Integer orderNo;

    @ApiModelProperty(value="分类描述")
    @Comment("分类描述[DICT_DESC]")
    private String dictDesc;

    @ApiModelProperty(value="是否初始字典",example = "0")
    @Comment("是否初始字典[IS_INIT]")
    @Default
    private Integer isInit = 0;

    @ApiModelProperty(value="字典定义")
    @Comment("字典定义[DICT_ATTR_DEFS]")
    private List<ESDictionaryAttrDef> dictAttrDefs;

    @ApiModelProperty(value="排序字段id",example = "1")
    @Comment("排序字段id[ORDER_DEF_ID]")
    private Long orderDefId;

    @ApiModelProperty(value="所属域id",example = "123")
    @Comment("所属域[DOMAIN_ID]")
    private Long domainId;

    @ApiModelProperty(value="创建人",example = "mike")
    @Comment("创建人[CREATOR]")
    private String creator;

    @ApiModelProperty(value="修改人",example = "mike")
    @Comment("修改人[MODIFIER]")
    private String modifier;

    @ApiModelProperty(value="创建时间")
    @Comment("创建时间[CREATE_TIME]    创建时间:yyyyMMddHHmmss")
    private Long createTime;

    @ApiModelProperty(value="更新时间")
    @Comment("更新时间[MODIFY_TIME]    更新时间:yyyyMMddHHmmss")
    private Long modifyTime;

    @Override
    public void valid() {
        Assert.notNull(this.getDictName(), "X_PARAM_NOT_NULL${name:字典名称}");
    }

}
