package com.uinnova.product.eam.service;


import com.uinnova.product.eam.comm.model.es.EamVersionDir;
import com.uinnova.product.eam.comm.model.es.EamVersionTag;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 版本标签数据操作层接口
 * <AUTHOR>
 */
public interface IVersionTagSvc {

    /**
     * 根据id获取历史版本标签
     * @param id 版本标签id
     * @return 版本标签
     */
    EamVersionTag getByTagId(Long id);

    /**
     * 根据id获取历史版本目录
     * @param id 版本目录id
     * @return 版本目录
     */
    EamVersionDir getByDirId(Long id);

    /**
     * 保存或更新标签信息
     * @param dto 标签信息
     * @return 版本标签id
     */
    Long saveOrUpdateTag(EamVersionTag dto);

    /**
     * 批量保存版本目录
     * @param list 版本目录
     * @return 结果
     */
    Integer saveVersionDirBatch(List<EamVersionDir> list);

    /**
     * 通过分支id获取版本信息
     * @param branchId 分支id
     * @return 结果
     */
    List<EamVersionTag> getVersionTagByBranchId(Long branchId);

    /**
     * 通过分支id获取版本信息map集合
     * @param branchIds 分支id集合
     * @return 标签版本Map集合<分支id-历史版本标签>
     */
    Map<Long, List<EamVersionTag>> getVersionTagMapByBranchId(Set<Long> branchIds);

    /**
     * 通过版本标签id获取版本目录信息
     * @param tagId 版本标签id
     * @return 版本目录信息
     */
    List<EamVersionDir> getVersionDirByTagId(Long tagId);

    /**
     * 通过分支id删除标签
     * @param branchId 分支id
     * @return 删除结果
     */
    Integer deleteByBranchId(Long branchId);
}
