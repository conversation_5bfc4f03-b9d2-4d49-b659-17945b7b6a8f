package com.uinnova.product.eam.web.analog.peer;

import com.uinnova.product.eam.model.diagram.EamHttpRequestParam;
import com.uinnova.product.eam.web.analog.bean.CCBRequestParam;
import com.uinnova.product.eam.web.analog.bean.DmvHttpRequestParam;

public interface ExternalDataPeer {

	/**
	 *根据前端给定的url请求信息
	 *@param param
	 *@return
	 * */
	Object requestByUrl(EamHttpRequestParam param);

	/**
	 * 查询建行的IT世界地图外部数据，包括交易码，流水号查询路径以及实时关系
	 * @param param
	 * @return
	 * */
	Object queryExternalDataForCCBITMap(CCBRequestParam param);

	Object queryHealthDegreeList();
}
