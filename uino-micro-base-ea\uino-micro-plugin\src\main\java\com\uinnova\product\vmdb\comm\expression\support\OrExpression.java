package com.uinnova.product.vmdb.comm.expression.support;

import com.uinnova.product.vmdb.comm.exception.ExpressionException;
import com.uinnova.product.vmdb.comm.expression.Expression;

/**
 * 或(or)表达式
 * 
 * <AUTHOR>
 */
public class OrExpression<E> extends AbstractExpression<E> {
    private static final long serialVersionUID = 1L;

    private Expression<E> master;
    private Expression<E> or;

    public OrExpression(Expression<E> master, Expression<E> or) {
        if (master == null) {
            throw new ExpressionException(" the master expression is NULL argument! ");
        }
        if (or == null) {
            throw new ExpressionException(" the or expression is NULL argument! ");
        }

        this.master = master;
        this.or = or;
    }

    @Override
    protected String buildSqlFragment() {
        return "((" + this.master.getSqlFragment() + ") or (" + this.or.getSqlFragment() + "))";
    }

}
