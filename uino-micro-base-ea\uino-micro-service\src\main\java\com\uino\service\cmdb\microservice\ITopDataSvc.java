package com.uino.service.cmdb.microservice;

import java.util.List;

import com.uino.bean.cmdb.SysTopData;

/**
 * 置顶数据服务
 * 
 * <AUTHOR>
 *
 */
public interface ITopDataSvc {

	/**
	 * 取消置顶
	 * 
	 * @param topData
	 *            取消置顶的数据
	 * @param topDataType
	 *            取消置顶的数据类型3：图标
	 */
	public void unTop(Long domainId,Long topData, Long topDataType);

	/**
	 * 置顶数据
	 * 
	 * @param topData
	 * @param topDataType
	 */
	public void top(Long domainID,Long topData, Long topDataType);

	/**
	 * 检索置顶数据
	 * 
	 * @param topDataType
	 * @return
	 */
	public List<SysTopData> searTopData(Long topDataType);
}
