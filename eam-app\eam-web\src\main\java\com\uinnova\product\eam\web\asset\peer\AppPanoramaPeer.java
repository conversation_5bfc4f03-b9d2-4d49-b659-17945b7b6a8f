package com.uinnova.product.eam.web.asset.peer;

import com.alibaba.fastjson.JSONObject;
import com.binary.core.util.BinaryUtils;
import com.uinnova.product.eam.base.model.ChangeInfoDto;
import com.uinnova.product.eam.base.util.EamUtil;
import com.uinnova.product.eam.comm.bean.CcCiAttrDefTapGroupConfVO;
import com.uinnova.product.eam.comm.model.es.AppPanoramaInfo;
import com.uinnova.product.eam.comm.model.es.AssetChangeRecord;
import com.uinnova.product.eam.model.AppPanoramaDetailConfigVo;
import com.uinnova.product.eam.model.AppPanoramaInfoVo;
import com.uinnova.product.eam.model.EamArtifactVo;
import com.uinnova.product.eam.model.enums.AssetDetailConfTypeEnum;
import com.uinnova.product.eam.service.AppPanoramaSvc;
import com.uinnova.product.eam.service.IEamArtifactSvc;
import com.uinnova.product.eam.service.asset.AssetChangeRecordSvc;
import com.uinnova.product.eam.service.asset.AssetContent;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uino.bean.cmdb.base.LibType;
import com.uino.util.sys.BeanUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 应用全景配置
 *
 * <AUTHOR>
 */
@Service
public class AppPanoramaPeer implements AssetContent {
    @Resource
    AppPanoramaSvc appPanoramaSvc;

    @Resource
    ArchitectureAssetPeer assetPeer;
    @Resource
    IEamArtifactSvc eamArtifactSvc;

    @Resource
    AssetChangeRecordSvc changeRecordSvc;

    public Long saveOrUpdateDetailConf(AppPanoramaDetailConfigVo detailConfig) {
        return appPanoramaSvc.saveOrUpdateDetailConf(detailConfig);
    }

    public AppPanoramaDetailConfigVo getDetailConf(Long appSquareConfId) {
        AppPanoramaDetailConfigVo detailConf = appPanoramaSvc.getDetailConf(appSquareConfId);
        if (BinaryUtils.isEmpty(detailConf)) {
            return null;
        }
        // 制品信息
        List<CcCiAttrDefTapGroupConfVO> detailAttrConfVO = detailConf.getDetailAttrConfVO();
        for (CcCiAttrDefTapGroupConfVO groupConfVO : detailAttrConfVO) {
            if (AssetDetailConfTypeEnum.DIAGRAM.type.equals(groupConfVO.getType())
                    && CollectionUtils.isNotEmpty(groupConfVO.getProductTypeList())) {
                List<Long> productTypeList = groupConfVO.getProductTypeList();
                List<EamArtifactVo> eamArtifactVos = eamArtifactSvc.queryArtifactByIds(productTypeList);
                detailConf.setArtifactVoList(eamArtifactVos);
            }
        }
        return detailConf;
    }

    public CcCiInfo saveOrUpdatePanorama(AppPanoramaInfoVo panoramaInfoVo, LibType libType) {
        CcCiInfo ciInfo = panoramaInfoVo.getCiInfo();
        CcCiInfo ccCiInfo = assetPeer.saveorUpdateCi(ciInfo, libType);
        Long assetChangeRecordId = null;
        if (BinaryUtils.isEmpty(ciInfo.getCi().getCiCode())) {
            assetChangeRecordId = assetPeer.addAssetChangeRecord(ciInfo, CREATE);
        }
        if (LibType.DESIGN.equals(libType)) {
            assetChangeRecordId = assetPeer.addAssetChangeRecord(ciInfo, RELEASING);
        }
        savePanoramaHistory(panoramaInfoVo, assetChangeRecordId,ccCiInfo.getCi().getCiCode());
        AppPanoramaInfo panoramaInfo = new AppPanoramaInfo();
        BeanUtil.copyProperties(panoramaInfoVo, panoramaInfo);
        panoramaInfo.setCiCode(ccCiInfo.getCi().getCiCode());
        if (LibType.DESIGN.equals(libType)) {
            panoramaInfo.setState(2);
        } else {
            panoramaInfo.setState(1);
        }
        appPanoramaSvc.saveOrUpdatePanorama(panoramaInfo);
        return ccCiInfo;
    }

    private void savePanoramaHistory(AppPanoramaInfoVo panoramaInfoVo, Long changeRecordId, String ciCode) {
        AssetChangeRecord assetChangeRecord;
        if (!BinaryUtils.isEmpty(changeRecordId)) {
            assetChangeRecord = changeRecordSvc.getById(changeRecordId);
        } else {
            assetChangeRecord = new AssetChangeRecord();
            assetChangeRecord.setType("关联");
            assetChangeRecord.setState(2);
            assetChangeRecord.setCiCode(ciCode);
        }

        CcCiInfo ciInfo = panoramaInfoVo.getCiInfo();
        // 对比视图、方案、决策变化
        AppPanoramaInfo changeBeforeInfo = appPanoramaSvc.getPanoramaInfoByCiCode(ciInfo.getCi().getCiCode());
        if (BinaryUtils.isEmpty(changeBeforeInfo)) {
            changeBeforeInfo = new AppPanoramaInfo();
        }
        // 视图
        boolean updateFlag = false;
        List<String> diagramIds = panoramaInfoVo.getDiagramIds();
        List<String> beforeDiagrmIds = changeBeforeInfo.getDiagramIds();
        ChangeInfoDto<String> diagramChange = EamUtil.compareLists(beforeDiagrmIds, diagramIds);
        if (CollectionUtils.isNotEmpty(diagramChange.getAdd())||CollectionUtils.isNotEmpty(diagramChange.getDelete())) {
            updateFlag = true;
        }
        assetChangeRecord.setDiagramChange(JSONObject.toJSONString(diagramChange));
        // 方案
        List<Long> planIds = panoramaInfoVo.getPlanIds();
        List<Long> beforeInfoPlanIds = changeBeforeInfo.getPlanIds();
        ChangeInfoDto<Long> planChange = EamUtil.compareLists(beforeInfoPlanIds, planIds);
        if (CollectionUtils.isNotEmpty(planChange.getAdd())||CollectionUtils.isNotEmpty(planChange.getDelete())) {
            updateFlag = true;
        }
        assetChangeRecord.setPlanChange(JSONObject.toJSONString(planChange));
        // 决策
        List<Long> decisionIds = panoramaInfoVo.getDecisionIds();
        List<Long> beforeInfoDecisionIds = changeBeforeInfo.getDecisionIds();
        ChangeInfoDto<Long> changeInfoDto = EamUtil.compareLists(beforeInfoDecisionIds, decisionIds);
        if (CollectionUtils.isNotEmpty(changeInfoDto.getAdd())||CollectionUtils.isNotEmpty(changeInfoDto.getDelete())) {
            updateFlag = true;
        }
        assetChangeRecord.setDecisionChange(JSONObject.toJSONString(changeInfoDto));
        if (updateFlag) {
            changeRecordSvc.saveChangeRecord(assetChangeRecord);
        }
    }

    public CcCiInfo changePanorama(AppPanoramaInfoVo panoramaInfoVo) {
        CcCiInfo ciInfo = panoramaInfoVo.getCiInfo();
        Long changeRecordId = assetPeer.addAssetChangeRecord(ciInfo, CHANGE);
        savePanoramaHistory(panoramaInfoVo, changeRecordId,null);
        CcCiInfo ccCiInfo = assetPeer.changeCi(ciInfo);
        AppPanoramaInfo panoramaInfo = new AppPanoramaInfo();
        BeanUtil.copyProperties(panoramaInfoVo, panoramaInfo);
        panoramaInfo.setCiCode(ccCiInfo.getCi().getCiCode());
        panoramaInfo.setState(2);
        Long id = appPanoramaSvc.saveOrUpdatePanorama(panoramaInfo);
        return ciInfo;
    }
}
