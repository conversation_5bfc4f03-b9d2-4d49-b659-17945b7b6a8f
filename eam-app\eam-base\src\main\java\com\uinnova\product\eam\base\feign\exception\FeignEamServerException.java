package com.uinnova.product.eam.base.feign.exception;


import com.uinnova.product.eam.base.exception.EamException;

/**
 * 将远程错误转换为本地错误.
 * 
 * <AUTHOR>
 * @since 2020年6月30日下午2:58:47
 *
 */
public class FeignEamServerException extends EamException {

    public static final long serialVersionUID = 1;

    public FeignEamServerException() {
        super();
    }

    public FeignEamServerException(String message) {
        super(message);
    }

    public FeignEamServerException(String message, Throwable cause) {
        super(message, cause);
    }

    public FeignEamServerException(Throwable cause) {
        super(cause);
    }
}
