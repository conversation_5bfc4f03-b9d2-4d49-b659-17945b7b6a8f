package com.uino.util.sys;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;

import java.util.Map;


/**
 * 数据集和接口超市公用的返回给前端展示的类
 *
 * <AUTHOR>
 * @version 1.0
 */
public class AppMallCode {
    /**
     * 格式化json
     *
     * @param object object
     * @return
     */
    private static String jsonFormat(JSONObject object) {
        String jsonString = JSON.toJSONString(object, SerializerFeature.PrettyFormat, SerializerFeature.WriteMapNullValue, SerializerFeature.WriteDateUseDateFormat);
        return jsonString;
    }

    /**
     * curl格式
     *
     * @param url    地址
     * @param params 参数
     * @return 结果
     */
    public static String getCurlCode(String url, String params, Map<String, String> heades) {
        StringBuilder code = new StringBuilder();
        code.append("curl -X POST \\\n");
        code.append("    " + url + " \\\n");
        code.append("    -H 'Content-Type:application/json' \\\n");
        code.append("    -H 'REQUEST_HEADER:binary-http-client-header' \\\n");
        if (heades != null) {
            heades.forEach((k, v) -> code.append("    -H '" + k + ":" + v + "' \\\n"));
        }

        code.append("    -d '");
        String s;
        try {
            JSONObject jsonObject = JSONObject.parseObject(params);
            s = jsonFormat(jsonObject);
        } catch (Exception e) {
            s = params;
        }
        s = s.replaceAll("\t", "\t\t");
        s = s.replace("}", "\t}");
        code.append(s);
        code.append("'");
        return code.toString();
    }

    /**
     * js格式
     *
     * @param url
     * @param params
     * @return
     */
    public static String getJavascriptCode(String url, String params, Map<String, String> heades) {
        StringBuilder code = new StringBuilder();
        code.append("var settings = {\n");
        code.append("    \"async\": true,\n");
        code.append("    \"crossDomain\": true,\n");
        code.append("    \"url\": \"" + url + "\",\n");
        code.append("    \"method\": \"POST\",\n");
        code.append("    \"headers\": {\n");
        code.append("        \"Content-Type\": \"application/json\",\n");
        code.append("        \"REQUEST_HEADER\": \"binary-http-client-header\",\n");
        if (heades != null) {
            heades.forEach((k, v) -> code.append("        \"" + k + "\": \"" + v + "\"\n"));
        }

        code.append("    },\n");
        code.append("    \"data\":\"");
        String s;
        try {
            JSONObject jsonObject = JSONObject.parseObject(params);
            s = jsonFormat(jsonObject);
        } catch (Exception e) {
            s = params;
        }
        s = s.replaceAll("\\\\\"", "\\\\\\\\\"");
        s = s.replaceAll("\"", "\\\\\"");
        s = s.replaceAll("\n", "\" +\n\t\t\t\"");   //
        code.append(s);
        code.append("\"\n");
        code.append("}\n");
        code.append("$.ajax(settings).done(function (response) {\n");
        code.append("    console.log(response);\n");
        code.append("});");
        return code.toString();
    }

    /**
     * Java格式
     *
     * @param url
     * @param params
     * @return
     */
    public static String getJavaCode(String url, String params, Map<String, String> heades) {
        StringBuilder code = new StringBuilder();
        code.append("CloseableHttpClient httpClient = HttpClients.createDefault();\n");
        code.append("CloseableHttpResponse response = null;\n");
        code.append("String result = \"\";\n");
        code.append("try {\n");
        code.append("    HttpPost httpPost = new HttpPost(\"" + url + "\");\n");
        code.append("    httpPost.setHeader(\"Content-Type\", \"application/json\");\n");
        code.append("    httpPost.setHeader(\"REQUEST_HEADER\", \"binary-http-client-header\");\n");
        if (heades != null) {
            heades.forEach((k, v) -> code.append("    httpPost.setHeader(\"" + k + "\", \"" + v + "\");\n"));
        }
        code.append("    StringEntity entity = new StringEntity(\"");
        String s;
        try {
            JSONObject jsonObject = JSONObject.parseObject(params);
            s = jsonFormat(jsonObject);
        } catch (Exception e) {
            s = params;
        }
        s = s.replaceAll("\\\\\"", "\\\\\\\\\"");
        s = s.replaceAll("\"", "\\\\\"");
        s = s.replaceAll("\n", "\" +\n\t\t\t\"");
        code.append(s);
        code.append("\", \"utf-8\");\n");
        code.append("    httpPost.setEntity(entity);\n");
        code.append("    response = httpClient.execute(httpPost);\n");
        code.append("    int responseCode = response.getStatusLine().getStatusCode();\n");
        code.append("    if (responseCode == 200) {\n");
        code.append("        String respBody = EntityUtils.toString(response.getEntity(), \"utf-8\");\n");
        code.append("        System.out.println(respBody);\n");
        code.append("    }\n");
        code.append("} catch (Exception e) {\n");
        code.append("    e.printStackTrace();\n");
        code.append("} finally {\n");
        code.append("    try {\n");
        code.append("        if (response != null) {\n");
        code.append("            response.close();\n");
        code.append("        }\n");
        code.append("        response = null;\n");
        code.append("        if (httpClient != null) {\n");
        code.append("            httpClient.close();\n");
        code.append("        }\n");
        code.append("        httpClient = null;\n");
        code.append("    } catch (IOException e) {\n");
        code.append("        e.printStackTrace();\n");
        code.append("    }\n");
        code.append("}");
        return code.toString();
    }

    /**
     * go格式
     *
     * @param url
     * @param params
     * @return
     */
    public static String getGolangCode(String url, String params, Map<String, String> heades) {
        StringBuilder code = new StringBuilder();
        code.append("package main\n");
        code.append("\n");
        code.append("import (\n");
        code.append("    \"fmt\"\n");
        code.append("    \"strings\"\n");
        code.append("    \"net/http\"\n");
        code.append("    \"io/ioutil\"\n");
        code.append(")\n");
        code.append("\n");
        code.append("func main() {\n");
        code.append("    url := \"" + url + "\"\n");
        code.append("    payload := strings.NewReader(`");
        String s;
        try {
            JSONObject jsonObject = JSONObject.parseObject(params);
            s = jsonFormat(jsonObject);
        } catch (Exception e) {
            s = params;
        }
        code.append(s);
        code.append("`)\n");
        code.append("    req, _ := http.NewRequest(\"POST\", url, payload)\n");
        code.append("    req.Header.Add(\"Content-Type\", \"application/json\")\n");
        code.append("    req.Header.Add(\"REQUEST_HEADER\", \"binary-http-client-header\")\n");
        if (heades != null) {
            heades.forEach((k, v) -> code.append("    req.Header.Add(\"" + k + "\", \"" + v + "\")\n"));
        }

        code.append("    res, _ := http.DefaultClient.Do(req)\n");
        code.append("    defer res.Body.Close()\n");
        code.append("    body, _ := ioutil.ReadAll(res.Body)\n");
        code.append("    fmt.Println(res)\n");
        code.append("    fmt.Println(string(body))\n");
        code.append("}");
        return code.toString();
    }

    public enum PostmanEnum {
        CURL(1),
        JAVASCRIPT(2),
        JAVA(3),
        GO(4);

        private int type;

        PostmanEnum(int type) {
            this.type = type;
        }

        public int getType() {
            return type;
        }

        public void setType(int type) {
            this.type = type;
        }
    }
}
