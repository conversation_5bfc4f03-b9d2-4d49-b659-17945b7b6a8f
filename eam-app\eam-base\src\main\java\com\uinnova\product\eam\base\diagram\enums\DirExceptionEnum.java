package com.uinnova.product.eam.base.diagram.enums;

public enum DirExceptionEnum {
    DIR_NAME_REPEAT(500,"文件夹名称重名");
    private final Integer code;
    private final String message;

   DirExceptionEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
