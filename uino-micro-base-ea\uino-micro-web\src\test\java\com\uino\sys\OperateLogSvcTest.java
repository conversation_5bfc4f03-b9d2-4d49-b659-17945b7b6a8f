package com.uino.sys;

import static org.junit.Assert.assertEquals;

import java.util.ArrayList;

import jakarta.servlet.http.Cookie;

import com.uino.dao.BaseConst;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.binary.jdbc.Page;
import com.uino.dao.permission.ESModuleSvc;
import com.uino.dao.permission.ESUserSvc;
import com.uino.dao.sys.ESOperateLogSvc;
import com.uino.service.sys.microservice.impl.OperateLogSvc;
import com.uino.bean.sys.base.ESOperateLog;
import com.uino.bean.sys.query.ESOperateLogSearchBean;

@RunWith(SpringJUnit4ClassRunner.class)
public class OperateLogSvcTest {

    @InjectMocks
    private OperateLogSvc svc;

    private ESOperateLogSvc logSvc;

    private ESUserSvc esUserSvc;

    private ESModuleSvc esModuleSvc;

    @BeforeClass
    public static void setUpBeforeClass() throws Exception {
        MockHttpServletRequest request = new MockHttpServletRequest();
        Cookie cookie = new Cookie("token", "ca14ed042fe7912426b484f6ea5c754b4fb51ae4a72037e9127da51743e0d1f9650e6e78ed4466ac970acb6a0b2f46fd26958c4ab2d115cc71b34ca8a02db24c");
        request.setCookies(cookie);
        ServletRequestAttributes attributes = new ServletRequestAttributes(request);
        RequestContextHolder.setRequestAttributes(attributes);
    }

    @Before
    public void setUp() throws Exception {
        logSvc = Mockito.mock(ESOperateLogSvc.class);
        ReflectionTestUtils.setField(svc, "logSvc", logSvc);

        esUserSvc = Mockito.mock(ESUserSvc.class);
        ReflectionTestUtils.setField(svc, "esUserSvc", esUserSvc);

        esModuleSvc = Mockito.mock(ESModuleSvc.class);
        ReflectionTestUtils.setField(svc, "esModuleSvc", esModuleSvc);
    }

    @Test
    public void testGetOperateLogPageByCdt() {
        Mockito.when(esUserSvc.getListByQuery(Mockito.any())).thenReturn(new ArrayList<>());
        Mockito.when(esModuleSvc.getListByQuery(Mockito.any())).thenReturn(new ArrayList<>());
        Mockito.when(logSvc.getSortListByQuery(Mockito.anyInt(), Mockito.anyInt(), Mockito.any(), Mockito.anyString(), Mockito.anyBoolean())).thenReturn(new Page<>(1, 30, 2, 1, new ArrayList<>()));

        ESOperateLogSearchBean bean = new ESOperateLogSearchBean();
        bean.setPageNum(1);
        bean.setPageSize(30);
        Page<ESOperateLog> rs = svc.getOperateLogPageByCdt(bean);
        assertEquals(2, rs.getTotalRows());
    }

    @Test
    public void testSaveOrUpdate() {
        Mockito.when(logSvc.saveOrUpdateDelayRefresh(Mockito.any(ESOperateLog.class))).thenReturn(123L);

        ESOperateLog opLog = ESOperateLog.builder().moduleName("moduleName").opDesc("test").build();
        Long rs = svc.saveOrUpdate(opLog);
        assertEquals(123L, rs.longValue());
    }

    @Test
    public void testSaveOrUpdateBatch() {
    }

    @Test
    public void testClearOperateLogByDuration() {
        Mockito.when(logSvc.deleteByQuery(Mockito.any(), Mockito.anyBoolean())).thenReturn(1);

        Integer rs = svc.clearOperateLogByDuration(1);
        assertEquals(1, rs.intValue());
    }

}
