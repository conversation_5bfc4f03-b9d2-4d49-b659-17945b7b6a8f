package com.uinnova.product.vmdb.comm.bean;

import java.io.Serializable;

/**
 * 
 * <AUTHOR>
 *
 */
public class CiClassTplCount implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 所属模板Id
     */
    private Long tplId;

    /**
     * 查询数量
     */
    private Integer valCount;

    public Long getTplId() {
        return tplId;
    }

    public void setTplId(Long tplId) {
        this.tplId = tplId;
    }

    public Integer getValCount() {
        return valCount;
    }

    public void setValCount(Integer valCount) {
        this.valCount = valCount;
    }

}
