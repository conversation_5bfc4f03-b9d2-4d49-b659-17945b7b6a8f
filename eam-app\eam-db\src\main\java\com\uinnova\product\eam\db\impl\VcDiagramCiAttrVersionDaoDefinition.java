package com.uinnova.product.eam.db.impl;


import com.binary.framework.dao.DaoDefinition;

import com.uinnova.product.eam.comm.model.CVcDiagramCiAttrVersion;
import com.uinnova.product.eam.comm.model.VcDiagramCiAttrVersion;


/**
 * 视图CI属性显示版本表[VC_DIAGRAM_CI_ATTR_VERSION]数据访问对象定义实现
 */
public class VcDiagramCiAttrVersionDaoDefinition implements DaoDefinition<VcDiagramCiAttrVersion, CVcDiagramCiAttrVersion> {


	@Override
	public Class<VcDiagramCiAttrVersion> getEntityClass() {
		return VcDiagramCiAttrVersion.class;
	}


	@Override
	public Class<CVcDiagramCiAttrVersion> getConditionClass() {
		return CVcDiagramCiAttrVersion.class;
	}


	@Override
	public String getTableName() {
		return "VC_DIAGRAM_CI_ATTR_VERSION";
	}


	@Override
	public boolean hasDataStatusField() {
		return false;
	}


	@Override
	public void setDataStatusValue(VcDiagramCiAttrVersion record, int status) {
	}


	@Override
	public void setDataStatusValue(CVcDiagramCiAttrVersion cdt, int status) {
	}


	@Override
	public void setCreatorValue(VcDiagramCiAttrVersion record, String creator) {
	}


	@Override
	public void setModifierValue(VcDiagramCiAttrVersion record, String modifier) {
	}


}


