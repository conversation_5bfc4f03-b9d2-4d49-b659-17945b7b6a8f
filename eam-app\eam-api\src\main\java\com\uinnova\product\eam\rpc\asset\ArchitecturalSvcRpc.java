package com.uinnova.product.eam.rpc.asset;

import com.uinnova.product.eam.api.IArchitecturalApiClient;
import com.uinnova.product.eam.base.model.AttrConfInfo;
import com.uinnova.product.eam.base.model.AttrDefInfo;
import com.uinnova.product.eam.base.model.FileResourceMeta;
import com.uinnova.product.eam.feign.client.ArchitecturalClient;
import com.uinnova.product.eam.model.asset.ArchitecturalDTO;
import com.uinnova.product.eam.model.asset.ArchitecturalNameCheckDTO;
import com.uinnova.product.eam.model.asset.ArchitecturalResolutionDTO;
import com.uinnova.product.eam.model.asset.SearchArchitecturalDTO;
import com.uino.bean.cmdb.base.ESCIInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;

@Service
public class ArchitecturalSvcRpc implements IArchitecturalApiClient {

    @Autowired
    private ArchitecturalClient architecturalClient;

    @Override
    public Long createArchitecturalResolution(ArchitecturalDTO architecturalDTO) {
        return architecturalClient.saveArchitectural(architecturalDTO);
    }

    @Override
    public List<Map<String, Object>> getInfoBySubsystemCode(ArchitecturalResolutionDTO architecturalResolutionDTO) {
        return architecturalClient.getInfoBySubsystemCode(architecturalResolutionDTO);
    }

    @Override
    public ArchitecturalResolutionDTO getInfoByCiId(Long ciId) {
        return architecturalClient.getInfoByCiId(ciId);
    }

    @Override
    public AttrDefInfo selectAttrConf(AttrConfInfo attrConfInfo) {
        return architecturalClient.selectAttrConf(attrConfInfo);
    }

    @Override
    public boolean checkArchitecturalName(ArchitecturalNameCheckDTO architecturalNameCheckDTO) {
        return architecturalClient.checkArchitecturalName(architecturalNameCheckDTO);
    }

    @Override
    public ESCIInfo searchArchitecturalDTO(SearchArchitecturalDTO searchArchitecturalDTO) {
        return architecturalClient.searchArchitecturalDTO(searchArchitecturalDTO);
    }

    @Override
    public List<Long> getResIdsById(Long architecturalId) {
        return architecturalClient.getResIdsById(architecturalId);
    }

    @Override
    public List<FileResourceMeta> download(List<Long> resIds) {
        return architecturalClient.download(resIds);
    }

    @Override
    public List<Long> changeSubsystemCode(String sourceCode, String targetCode, Long ciClassIdByName) {

        return Collections.emptyList();
    }
}
