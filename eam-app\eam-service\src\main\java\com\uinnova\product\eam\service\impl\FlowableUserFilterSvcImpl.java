package com.uinnova.product.eam.service.impl;

import com.uinnova.product.eam.comm.model.es.FlowableUser;
import com.uinnova.product.eam.service.IFlowableUserFilterService;
import com.uinnova.product.eam.service.es.FlowableUserFilterDao;
import com.uino.dao.util.ESUtil;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FlowableUserFilterSvcImpl implements IFlowableUserFilterService {

    @Autowired
    private FlowableUserFilterDao flowableUserFilterDao;


    @Override
    public Long addAgreeUser(FlowableUser flowableUser) {
        long id = ESUtil.getUUID();
        if (flowableUser.getId() == null) {
            flowableUser.setId(id);
        }
        return flowableUserFilterDao.saveOrUpdate(flowableUser);
    }

    @Override
    public List<FlowableUser> getAgreeUserList(FlowableUser flowableUser) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termQuery("processInstanceId.keyword", flowableUser.getProcessInstanceId()));
        boolQueryBuilder.must(QueryBuilders.termQuery("processTaskKey.keyword", flowableUser.getProcessTaskKey()));
        List<FlowableUser> result = flowableUserFilterDao.getListByQuery(boolQueryBuilder);
        return result;
    }
}
