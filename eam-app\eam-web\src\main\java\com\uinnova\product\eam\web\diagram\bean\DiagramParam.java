package com.uinnova.product.eam.web.diagram.bean;

import java.io.Serializable;
import java.util.List;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.comm.model.CVcDiagram;
import com.uinnova.product.eam.comm.model.CVcDiagramGroup;
import com.uinnova.product.eam.comm.model.DcCombDiagram;
import com.uinnova.product.eam.comm.model.VcDiagram;
import com.uinnova.product.eam.model.VcDiagramInfo;

/**
 * 参数bean
 * <AUTHOR>
 *
 */
public class DiagramParam implements Serializable {
    private static final long serialVersionUID = 1L;

    @Comment("ID")
    private Long id;

    @Comment("ID数组")
    private Long[] ids;

    @Comment("目录类型")
    private Integer dirType;

    @Comment("CODE值")
    private String code;

    @Comment("CI名称")
    private String ciName;

    @Comment("CI-CODE数组")
    private String[] ciCodes;

    @Comment("KPI名称")
    private String kpiName;

    @Comment("开始时间")
    private Long startTime;

    @Comment("结束时间")
    private Long endTime;

    @Comment("视图基本信息")
    private VcDiagram diagram;

    @Comment("视图查询条件")
    private CVcDiagram diagramCdt;

    @Comment("小组查询条件")
    private CVcDiagramGroup diagramGroupCdt;

    @Comment("视图详细信息")
    private VcDiagramInfo diagramInfo;

    @Comment("视图组信息对象集合")
    private List<DcCombDiagram> combDiagrams;

    @Comment("视图数量")
    private Long diagramCount;

    @Comment("最大数量")
    private Integer maxCount;

    @Comment("分享的视图数量")
    private Long sharedDiagramCount;

    @Comment("协作视图数量")
    private Long groupDiagramCount;

    @Comment("查询视图告警信息标识,是否查询告警详情1是0否")
    private Integer isAll;

    @Comment("小组查询时筛选视图类型1：单图，2：组合视图,3:模板")
    private Integer[] types;

    @Comment("筛选类型")
    private Integer type;

    @Comment("查询类型：0:名称,1:作者,2:标签,3:CI")
    private Integer queryType;

    @Comment("模糊查询")
    private String like;

    @Comment("查询告警是关系id数组")
    private Long[] rltIds;

    @Comment("是否返回diagramEle信息")
    private Boolean retEles;

    @Comment("code数组")
    private String[] codes;

    @Comment("是否返回xml信息")
    private Integer retXml;

    @Comment("xml信息")
    private String xml;

    @Comment("目录用户id")
    private Long dirUserId;

    @Comment("流程id")
    private Long processId;


    public Long getProcessId() {
        return processId;
    }

    public void setProcessId(Long processId) {
        this.processId = processId;
    }

    public Long getDirUserId() {
        return dirUserId;
    }

    public void setDirUserId(Long dirUserId) {
        this.dirUserId = dirUserId;
    }

    public Integer getDirType() {
        return dirType;
    }

    public void setDirType(Integer dirType) {
        this.dirType = dirType;
    }

    public String getXml() {
        return xml;
    }

    public void setXml(String xml) {
        this.xml = xml;
    }

    public String getLike() {
        return like;
    }

    public void setLike(String like) {
        this.like = like;
    }

    public Integer getQueryType() {
        return queryType;
    }

    public void setQueryType(Integer queryType) {
        this.queryType = queryType;
    }

    public Long getGroupDiagramCount() {
        return groupDiagramCount;
    }

    public void setGroupDiagramCount(Long groupDiagramCount) {
        this.groupDiagramCount = groupDiagramCount;
    }


    public Long getSharedDiagramCount() {
        return sharedDiagramCount;
    }


    public void setSharedDiagramCount(Long sharedDiagramCount) {
        this.sharedDiagramCount = sharedDiagramCount;
    }

    public Integer getType() {
        return type;
    }


    public void setType(Integer type) {
        this.type = type;
    }


    public Integer getRetXml() {
        return retXml;
    }


    public void setRetXml(Integer retXml) {
        this.retXml = retXml;
    }


    public String[] getCodes() {
        return codes;
    }


    public void setCodes(String[] codes) {
        this.codes = codes;
    }


    public Boolean getRetEles() {
        return retEles;
    }


    public void setRetEles(Boolean retEles) {
        this.retEles = retEles;
    }


    public Long[] getRltIds() {
        return rltIds;
    }


    public void setRltIds(Long[] rltIds) {
        this.rltIds = rltIds;
    }


    public Integer[] getTypes() {
        return types;
    }


    public void setTypes(Integer[] types) {
        this.types = types;
    }


    public CVcDiagramGroup getDiagramGroupCdt() {
        return diagramGroupCdt;
    }


    public void setDiagramGroupCdt(CVcDiagramGroup diagramGroupCdt) {
        this.diagramGroupCdt = diagramGroupCdt;
    }


    public Long getId() {
        return id;
    }


    public void setId(Long id) {
        this.id = id;
    }


    public Long[] getIds() {
        return ids;
    }


    public void setIds(Long[] ids) {
        this.ids = ids;
    }


    public String getCode() {
        return code;
    }


    public void setCode(String code) {
        this.code = code;
    }


    public String getCiName() {
        return ciName;
    }


    public void setCiName(String ciName) {
        this.ciName = ciName;
    }


    public String[] getCiCodes() {
        return ciCodes;
    }


    public void setCiCodes(String[] ciCodes) {
        this.ciCodes = ciCodes;
    }


    public String getKpiName() {
        return kpiName;
    }


    public void setKpiName(String kpiName) {
        this.kpiName = kpiName;
    }


    public Long getStartTime() {
        return startTime;
    }


    public void setStartTime(Long startTime) {
        this.startTime = startTime;
    }


    public Long getEndTime() {
        return endTime;
    }


    public void setEndTime(Long endTime) {
        this.endTime = endTime;
    }


    public VcDiagram getDiagram() {
        return diagram;
    }


    public void setDiagram(VcDiagram diagram) {
        this.diagram = diagram;
    }


    public CVcDiagram getDiagramCdt() {
        return diagramCdt;
    }


    public void setDiagramCdt(CVcDiagram diagramCdt) {
        this.diagramCdt = diagramCdt;
    }


    public VcDiagramInfo getDiagramInfo() {
        return diagramInfo;
    }


    public void setDiagramInfo(VcDiagramInfo diagramInfo) {
        this.diagramInfo = diagramInfo;
    }


    public List<DcCombDiagram> getCombDiagrams() {
        return combDiagrams;
    }


    public void setCombDiagrams(List<DcCombDiagram> combDiagrams) {
        this.combDiagrams = combDiagrams;
    }


    public Long getDiagramCount() {
        return diagramCount;
    }


    public void setDiagramCount(Long diagramCount) {
        this.diagramCount = diagramCount;
    }


    public Integer getMaxCount() {
        return maxCount;
    }


    public void setMaxCount(Integer maxCount) {
        this.maxCount = maxCount;
    }


    public Integer getIsAll() {
        return isAll;
    }


    public void setIsAll(Integer isAll) {
        this.isAll = isAll;
    }








}
