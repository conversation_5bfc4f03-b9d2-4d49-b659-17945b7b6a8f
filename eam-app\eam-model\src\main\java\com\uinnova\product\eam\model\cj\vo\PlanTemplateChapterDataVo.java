package com.uinnova.product.eam.model.cj.vo;

import com.uinnova.product.eam.model.cj.domain.ChapterResource;
import com.uinnova.product.eam.model.cj.domain.PlanTemplateChapter;
import com.uinnova.product.eam.model.cj.domain.PlanTemplateChapterData;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @description:
 * @author: Lc
 * @create: 2022-01-06 15:19
 */
@Data
public class PlanTemplateChapterDataVo implements Serializable {

    private PlanTemplateChapter chapter;

    private List<PlanTemplateChapterData> chapterDataList;

    private List<ChapterResource> chapterResource;

}
