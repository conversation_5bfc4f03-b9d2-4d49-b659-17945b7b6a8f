package com.uinnova.product.eam.service.diagram.impl;


import cn.hutool.core.date.DateUtil;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.binary.core.exception.MessageException;
import com.binary.core.util.BinaryUtils;
import com.binary.framework.web.RemoteResult;
import com.binary.jdbc.Page;
import com.uinnova.product.eam.base.diagram.model.*;
import com.uinnova.product.eam.db.diagram.es.ESDiagramDao;
import com.uinnova.product.eam.db.diagram.es.ESDiagramNodeDao;
import com.uinnova.product.eam.db.diagram.es.ESShapeDao;
import com.uinnova.product.eam.db.diagram.es.ESShapeDirDao;
import com.uinnova.product.eam.service.diagram.ESShapeDirSvc;
import com.uinnova.product.eam.service.diagram.ESShapeSvc;
import com.uinnova.product.vmdb.comm.i18n.MessageUtil;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClassDir;
import com.uinnova.product.vmdb.comm.model.image.CcImage;
import com.uino.bean.cmdb.base.CcCiClassDir;
import com.uino.bean.cmdb.business.ImageCount;
import com.uino.bean.cmdb.business.ImportDirMessage;
import com.uino.bean.cmdb.business.ImportResultMessage;
import com.uino.bean.cmdb.query.ESSearchImageBean;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.permission.query.SearchKeywordBean;
import com.uino.dao.util.ESUtil;
import com.uino.tarsier.tarsiercom.util.IdGenerator;
import com.uino.util.sys.SysUtil;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.sort.SortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;

@Service
public class ESShapeSvcImpl implements ESShapeSvc {

    @Value("${http.resource.space}")
    private String httpResouceUrl;

    @Autowired
    private ESShapeDao svc;

    @Autowired
    private ESShapeDirDao dirSvc;

    @Autowired
    private ESShapeDirSvc dSvc;

    @Autowired
    private ESDiagramDao esDiagramDao;

    @Autowired
    private ESDiagramNodeDao esDiagramNodeDao;

    @Value("${local.resource.space}")
    private String localPath;

    private static final Logger logger = LoggerFactory.getLogger(ESShapeSvcImpl.class);

    @Override
    public RemoteResult saveShape(ESUserShapeDTO esUserShapeDTO) {
        MessageUtil.checkEmpty(esUserShapeDTO, "esUserShapeDTO");
        MessageUtil.checkEmpty(esUserShapeDTO.getDirId(), "dirId");
//        boolean notVip = true;
        // 图形id为空
        if (StringUtils.isEmpty(esUserShapeDTO.getId())) {
            esUserShapeDTO.setId(IdGenerator.createGenerator().getID());
        }
        // 图形name为空
        if (StringUtils.isEmpty(esUserShapeDTO.getImgName())) {
            esUserShapeDTO.setImgName("myShape_" + esUserShapeDTO.getId());
        }
//        // 图形category为空
//        if (StringUtils.isEmpty(esUserShapeDTO.getCategory())) {
//            esUserShapeDTO.setCategory("myshape");
//        }
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        Long userId = currentUserInfo.getId();
        //后续可能有会员机制，非会员限制图形数量
//        ESUserShapeQuery esUserShapeQuery = new ESUserShapeQuery();
//        esUserShapeQuery.setUserId(userId);
//        long count = esShapeDao.countByCdt(esUserShapeQuery);
//        if (count > 9 && notVip) {
//            return new RemoteResult(false, 400, "新增图形个数超过限制:" + "9");
//        }
//        if (count > 60) {
//            return new RemoteResult(false, 401, "新增图形个数超过限制:" + "60");
//        }
        //保存图形
        ESUserShape esUserShape = new ESUserShape();
        BeanUtils.copyProperties(esUserShapeDTO, esUserShape);
        esUserShape.setUserId(userId);
        this.svc.saveOrUpdate(esUserShape);
        //返回图形id
        Map<String, Long> resultMap = new HashMap<>();
        resultMap.put("shapeId", esUserShapeDTO.getId());
        return new RemoteResult(resultMap);
    }


    @Override
    public StringBuilder addDenergy() {
        long startTime = System.currentTimeMillis();
        EamDiagramQuery diagramQuery = new EamDiagramQuery();
        long totalRows = esDiagramDao.countByCondition(ESUtil.cdtToBuilder(diagramQuery));
        StringBuilder resultStr = new StringBuilder("开始批量执行视图加密操作: ");
        resultStr.append("\r\n");
        resultStr.append("需要执行的总视图数量：" + totalRows);
        resultStr.append("\r\n");
        int i = (int) (totalRows / 1000);
        int num = 0;
        int buc = 0;
        //先一次修改1000条，最后做补偿处理
        SortOrder orderType = true ? SortOrder.ASC : SortOrder.DESC;
        List<SortBuilder<?>> sorts = new LinkedList();
        sorts.add(SortBuilders.fieldSort("id").order(orderType));
        BoolQueryBuilder query = ESUtil.cdtToBuilder(diagramQuery);
        for (int j = 0; j <= i; j++) {
            Page<ESDiagram> cdt = esDiagramDao.getSortListByQuery(j + 1, 1000, query, (List) sorts);
            List<ESDiagram> d = cdt.getData();
            if (!CollectionUtils.isEmpty(d)) {
                // 漏洞扫描 抽出try逻辑
                this.recordSaveActive(d, resultStr, j, cdt, num);

                /*try {
                    Integer integer = esDiagramDao.saveOrUpdateBatchWithOption(d);
                    if (integer != null && integer > 0) {
                        resultStr.append(new StringBuilder("循环次数").append(j + 1).append("---此次需要操作的视图数量：").append(cdt.getData().size()).toString());
                        num += cdt.getData().size();
                        resultStr.append("\r\n");
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }*/
            } else {
                break;
            }
        }

        try {
            resultStr.append("程序休眠20秒,等待数据处理......");
            Thread.sleep(20000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

        query.mustNot(QueryBuilders.existsQuery("dEnergy"));
        //做补偿处理，对未修改的重新添加加密字段
        List<ESDiagram> data = esDiagramDao.getListByQueryScroll(query);
        if (!CollectionUtils.isEmpty(data)) {
            Integer integer = esDiagramDao.saveOrUpdateBatchWithOption(data);
            buc += data.size();
            if (integer != null && integer > 0) {
                resultStr.append("\r\n");
            }
        }
        resultStr.append("\r\n");
        resultStr.append("每次1000个执行完成，执行总的视图数：" + num);
        resultStr.append("\r\n");
        resultStr.append("执行完成，补偿总的视图数：" + buc);
        resultStr.append("\r\n");
        long spendTime = DateUtil.spendMs(startTime);
//        resultStr.append("每个1000执行+补偿执行："+num + buc);
        resultStr.append("程序执行花费时间(以秒为单位)：").append(spendTime / 1000);
        logger.info(resultStr.toString());
        return resultStr;
    }

    private void recordSaveActive(List<ESDiagram> d, StringBuilder resultStr, int j, Page<ESDiagram> cdt, int num) {
        try {
            Integer integer = esDiagramDao.saveOrUpdateBatchWithOption(d);
            if (integer != null && integer > 0) {
                resultStr.append("循环次数" + (j + 1) + "---此次需要操作的视图数量：" + cdt.getData().size());
                num += cdt.getData().size();
                resultStr.append("\r\n");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public StringBuilder nodeToEnergy(Long key) {
        long startTime = System.currentTimeMillis();
        StringBuilder resultStr = new StringBuilder("开始批量执行视图加密操作: ");
        ESDiagramNodeQueryBean nodeQuery = new ESDiagramNodeQueryBean();
        if (key != null) {
            nodeQuery.setDiagramId(key);

        }
        BoolQueryBuilder query = ESUtil.cdtToBuilder(nodeQuery);
        long l = esDiagramNodeDao.countByCondition(query);
        SortOrder orderType = true ? SortOrder.ASC : SortOrder.DESC;
        List<SortBuilder<?>> sorts = new LinkedList();
        sorts.add(SortBuilders.fieldSort("id").order(orderType));
        List<ESDiagramNode> list = new ArrayList<>();
        List<ESDiagramNode> fList = new ArrayList<>();
        int num = 0;
        AtomicInteger count = new AtomicInteger();
        AtomicInteger sum = new AtomicInteger();
        int i = (int) (l / 1000);
        for (int j = 0; j <= i; j++) {
            Page<ESDiagramNode> cdt = esDiagramNodeDao.getSortListByQuery(j + 1, 1000, query, (List) sorts);
            List<ESDiagramNode> d = cdt.getData();
            if (!CollectionUtils.isEmpty(d)) {
                List<ESDiagramNode> collect = d.stream().filter(t ->
                        t.getNodeJson().contains("relationInfo") && t.getNodeJson().contains("document")).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(collect)) {
                    collect.forEach(t -> {
                        try {
                            HashMap<String, Object> ri = JSONObject.parseObject(t.getNodeJson(), HashMap.class);
                            RelationInfo info = JSON.toJavaObject((JSON) ri.get("relationInfo"), RelationInfo.class);
                            if (!judgeContainsStr(info.getValue())) {
                                info.setValue(SecureUtil.md5(String.valueOf(info.getValue())).substring(8, 24));
                                ri.put("relationInfo", info);
                                t.setNodeJson(JSONObject.toJSONString(ri));
                                t.setCiCode(ri.containsKey("ciCode") && !BinaryUtils.isEmpty(ri.get("ciCode")) ? String.valueOf(ri.get("ciCode")) : null);
                                list.add(t);
                                sum.addAndGet(1);
                            }
                        } catch (Exception e) {
                            fList.add(t);
                            count.addAndGet(1);
                            e.printStackTrace();
                        }
                    });
                    resultStr.append(fList);
                    num += collect.size();
                }

            } else {
                break;
            }
        }
        Integer integer = esDiagramNodeDao.saveOrUpdateBatch(list);
//        esDiagramNodeDao.deleteByIds(fList.stream().map(ESDiagramNode::getId).collect(Collectors.toList()));
        resultStr.append("\r\n");
        resultStr.append("执行总的node数：" + l);
        resultStr.append("\r\n");
        resultStr.append("执行完成，node包含关联视图的节点数：" + num);
        resultStr.append("\r\n");
        resultStr.append("执行完成，node包含关联视图的数,且value未做修改的节点数：" + sum);
        resultStr.append("\r\n");
        resultStr.append("执行完成，node包含关联视图的数,且value未做修改的节点数：" + list.size());
        resultStr.append("\r\n");
        resultStr.append("执行完成，修改失败数量：" + fList.size());
        resultStr.append("\r\n");
        long spendTime = DateUtil.spendMs(startTime);
        resultStr.append("程序执行花费时间(以秒为单位)：").append(spendTime / 1000);
        logger.info(resultStr.toString());
        return resultStr;
    }

    @Override
    public List<ImageCount> queryImageDirList(CCcCiClassDir cdt) {
        List<ImageCount> list = new ArrayList<>();
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        ESShapeDirQuery dirQuery = new ESShapeDirQuery();
        dirQuery.setUserId(currentUserInfo.getId());
        dirQuery.setDirName(cdt.getDirName());
        dirQuery.setParentId(0L);
        BoolQueryBuilder query = ESUtil.cdtToBuilder(dirQuery);
        long condition = this.dirSvc.countByCondition(query);
        // 查询我的形状数量,没有则默认新增一个
        if (condition == 0L) {
            ESShapeDir dir = new ESShapeDir();
            dir.setDirName("我的形状");
            this.dSvc.saveOrUpdate(dir);
        }
        // 查询我的形状列表, 并查询对应形状下的图形数量
        List<ESShapeDir> dirs = dirSvc.getSortListByQuery(1, 3000, query, "createTime", true).getData();
        if (!CollectionUtils.isEmpty(dirs)) {
            Map<String, Long> countsByDirId = svc.groupByCountField("dirId", QueryBuilders.matchAllQuery());
            Map<Long, List<ImageCount>> dirByParentMap = new HashMap<>();
            for (ESShapeDir dir : dirs) {
                CcCiClassDir copy = new CcCiClassDir();
                BeanUtils.copyProperties(dir, copy);

                Long count = countsByDirId.getOrDefault(new StringBuilder().append(dir.getId()).toString(), 0L);
                ImageCount ic = new ImageCount();
                ic.setDir(copy);
                ic.setImageCount(count);
                if (dir.getParentId() != null && dir.getParentId() != 0L) {
                    dirByParentMap.computeIfAbsent(dir.getParentId(), k -> new ArrayList<>()).add(ic);
                } else {
                    list.add(ic);
                }
            }
            for (ImageCount parent : list) {
                List<ImageCount> children = dirByParentMap.get(parent.getDir().getId());
                if (children != null) {
                    parent.getChildren().addAll(children);
                }
            }
        }
        return list;
    }

    @Override
    public Page<ESUserShape> queryImagePage(ESImageBean bean) {
        // 根据我的形状ID查询其下的图形
        Long dirId = bean.getDirId();
        MessageUtil.checkEmpty(dirId, "dirId");
        SysUser user = SysUtil.getCurrentUserInfo();
        ESUserShapeQuery shapeQuery = new ESUserShapeQuery();
        shapeQuery.setUserId(user.getId());
        shapeQuery.setDirId(dirId);
        shapeQuery.setImgName(bean.getKeyword());
        QueryBuilder query = ESUtil.cdtToBuilder(shapeQuery);
        if (query == null) {
            return new Page((long) bean.getPageNum(), (long) bean.getPageSize(), 0L, 0L, new ArrayList());
        } else {
            List<SortBuilder<?>> sorts = new LinkedList();
            sorts.add(SortBuilders.fieldSort("orderNo").order(SortOrder.ASC));
            Page<ESUserShape> page = this.svc.getSortListByQuery(bean.getPageNum(), bean.getPageSize(), query, sorts);
            return page;
        }
    }

    @Override
    public boolean deleteImage(CcImage image) {
        //删除一个图形
        Long imgId = image.getId();
        Assert.notNull(imgId, "X_PARAM_NOT_NULL${name:imgId}");
        SysUser user = SysUtil.getCurrentUserInfo();
        ESUserShape shape = this.svc.getById(imgId);
        if (shape != null && shape.getUserId().equals(user.getId())) {
            return this.svc.deleteById(imgId) == 1;
        } else {
            throw new MessageException("需要删除的图形，已删除或没有权限");
        }
    }

    @Override
    public long deleteDirImage(Long dirId) {
        //删除一个我的形状,对应删除其下图形
        Assert.notNull(dirId, "X_PARAM_NOT_NULL${name:dirId}");
        long count;
        SysUser user = SysUtil.getCurrentUserInfo();
        if (dirId == 1L) {
            throw MessageException.i18n("BS_MNAME_IMAGE_DELETE_ROOT_NOT_ALLOW");
        } else {
            //最后一个我的形状不允许删除
            ESShapeDirQuery dirQuery = new ESShapeDirQuery();
            dirQuery.setUserId(user.getId());
            dirQuery.setParentId(0L);
            BoolQueryBuilder query = ESUtil.cdtToBuilder(dirQuery);
            count = this.dirSvc.countByCondition(query);
            if (count <= 1) {
                return count;
            }
            List<Long> dirIds = new ArrayList();
            dirIds.add(dirId);
            ESShapeDir shapeDir = this.dirSvc.getById(dirId);
            if (shapeDir != null && shapeDir.getUserId().equals(user.getId())) {
                this.svc.deleteByQuery(QueryBuilders.termsQuery("dirId", dirIds), true);
                this.dirSvc.deleteByIds(dirIds);
            } else {
                throw new MessageException("需要删除的目录，已删除或没有权限");
            }
        }
        return count - 1;
    }


    @Override
    public Long updateImageRlt(ESUserShape image) {
        // 新增和修改我的图形,需要传我的形状ID
        Long dirId = image.getDirId();
        MessageUtil.checkEmpty(image, "image");
        MessageUtil.checkEmpty(dirId, "dirId");
        image.setImgName(StringUtils.isEmpty(image.getImgName()) ? "自定义形状" : image.getImgName());
//        Assert.isTrue(image.getImgName().trim().length() <= 20, "我的图形名称不可超过20位");
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        ESShapeDir shapeDir = this.dirSvc.getById(dirId);
        if (shapeDir != null && shapeDir.getUserId().equals(currentUserInfo.getId())) {
            Long imgId = image.getId();
            if (BinaryUtils.isEmpty(imgId)) {
                image.setId(IdGenerator.createGenerator().getID());
                image.setOrderNo(getNextOrderNo(dirId));
                image.setUserId(currentUserInfo.getId());
                return this.svc.saveOrUpdate(image);
            } else {
                ESUserShapeQuery sQuery = new ESUserShapeQuery();
                sQuery.setId(imgId);
                sQuery.setUserId(currentUserInfo.getId());
                List<ESUserShape> images = this.svc.getListByCdt(1, 1, sQuery).getData();
                Map<Long, ESUserShape> imgMap = BinaryUtils.toObjectMap(images, "id");
                if (!imgMap.containsKey(imgId)) {
                    throw new MessageException("BS_VERIFY_ERROR#{name:BS_MNAME_IMAGE}#{type:BS_MVTYPE_NOT_EXIST}${value:" + imgId + "}");
                } else {
                    ESUserShape img = imgMap.get(imgId);
                    img.setImgName(image.getImgName());
                    return this.svc.saveOrUpdate(img);
                }
            }
        } else {
            throw new MessageException("BS_VERIFY_ERROR#{name:BS_MNAME_IMAGE}#{type:BS_MVTYPE_NOT_EXIST}${value:" + dirId + "}");
        }
    }

    //    @Override
    public boolean sort1(ESUserShapeQuery cdt) {
        Long[] ids = cdt.getIds();
        MessageUtil.checkEmpty(ids, "ids");
        //查询ids
        ESUserShapeQuery query = new ESUserShapeQuery();
        query.setIds(ids);
        int length = ids.length;
        List<ESUserShape> listByCdt = this.svc.getListByCdt(query);
        if (!CollectionUtils.isEmpty(listByCdt) && listByCdt.size() == length) {
            Long[] userIds = listByCdt.stream().map(ESUserShape::getUserId).distinct().toArray(Long[]::new);
            Long[] dirs = listByCdt.stream().map(ESUserShape::getDirId).distinct().toArray(Long[]::new);
            SysUser user = SysUtil.getCurrentUserInfo();
            if (userIds != null && userIds.length == 1 && userIds[0].equals(user.getId()) && dirs != null && dirs.length == 1) {
                List<ESUserShape> esUserShapes = new ArrayList<>();
                for (int i = 0; i < length; i++) {
                    ESUserShape esUserShape = new ESUserShape();
                    esUserShape.setId(ids[i]);
                    esUserShape.setOrderNo((long) (length - i));
                    esUserShapes.add(esUserShape);
                }
                this.svc.saveOrUpdateBatch(esUserShapes);
            } else {
                throw new MessageException("排序失败！！！");
            }
        }
        return true;
    }

    @Override
    public boolean sort(ESUserShapeQuery cdt) {
        // 对我的形状下的图形进行排序
        Long dirId = cdt.getDirId();
        String sorts = cdt.getSorts();
        MessageUtil.checkEmpty(dirId, "dirId");
        MessageUtil.checkEmpty(sorts, "sorts");
        String[] split = sorts.split("-");
        int star = Integer.parseInt(split[0]);
        int end = Integer.parseInt(split[1]);
        boolean isEqual = (star - end) == 0 ? false : true;
        if (isEqual) {
            SysUser user = SysUtil.getCurrentUserInfo();
            ESUserShapeQuery shapeQuery = new ESUserShapeQuery();
            shapeQuery.setUserId(user.getId());
            shapeQuery.setDirId(dirId);
            QueryBuilder query = ESUtil.cdtToBuilder(shapeQuery);
            long l = this.svc.countByCondition(query);
            if (star > l || end > l) {
                throw new MessageException("排序失败！！！");
            }
            int vl = (star - end) > 0 ? 1 : -1;
            List<SortBuilder<?>> sort = new LinkedList();
            sort.add(SortBuilders.fieldSort("orderNo").order(SortOrder.ASC));
            Page<ESUserShape> data = this.svc.getSortListByQuery(1, (Math.max(star, end) + 1), query, sort);
            List<ESUserShape> list = data.getData();
            ArrayList<ESUserShape> dataList = new ArrayList<>();
            ESUserShape shape;
            shape = list.get(star);
            shape.setOrderNo(list.get(end).getOrderNo());
            dataList.add(shape);
            while (isEqual) {
                star -= vl;
                shape = list.get(star);
                shape.setOrderNo(shape.getOrderNo() + vl);
                dataList.add(shape);
                if ((star - end) == 0) {
                    isEqual = false;
                }
            }
            this.svc.saveOrUpdateBatch(dataList);
        }
        return true;
    }

    @Override
    public StringBuilder updateHttp(String userName, String ip) {

        long startTime = System.currentTimeMillis();
        StringBuilder resultStr = new StringBuilder("开始批量执行视图加密操作: ");
        ESUserShapeQuery nodeQuery = new ESUserShapeQuery();
        BoolQueryBuilder query = ESUtil.cdtToBuilder(nodeQuery);
        query.must();
        long l = svc.countByCondition(query);
        SortOrder orderType = true ? SortOrder.ASC : SortOrder.DESC;
        List<SortBuilder<?>> sorts = new LinkedList();
        sorts.add(SortBuilders.fieldSort("id").order(orderType));
        AtomicInteger count = new AtomicInteger();
        int i = (int) (l / 1000);
        for (int j = 0; j <= i; j++) {
            Page<ESUserShape> cdt = svc.getSortListByQuery(j + 1, 1000, query, (List) sorts);
            List<ESUserShape> d = cdt.getData();
            if (!CollectionUtils.isEmpty(d)) {
                d.forEach(t -> {
                    try {
                        t.setShapeJson(t.getShapeJson().replaceAll(userName, ip));
                    } catch (Exception e) {
                        count.addAndGet(1);
                        e.printStackTrace();
                    }
                });
                Integer integer = svc.saveOrUpdateBatch(d);
            } else {
                break;
            }
        }
        resultStr.append("\r\n");
        resultStr.append("执行总的node数：" + l);
        resultStr.append("\r\n");
        resultStr.append("执行完成，需要修改的的节点数：" + l);
        resultStr.append("\r\n");
        long spendTime = DateUtil.spendMs(startTime);
        resultStr.append("程序执行花费时间(以秒为单位)：").append(spendTime / 1000);
        logger.info(resultStr.toString());
        return resultStr;
    }

    @Override
    public List<ESUserShapeResult> queryAllImage(ESImageBean bean) {
        //查询我的形状目录列表
        SysUser user = SysUtil.getCurrentUserInfo();
        ESShapeDirQuery dirQuery = new ESShapeDirQuery();
        dirQuery.setUserId(user.getId());
        dirQuery.setParentId(0L);
        BoolQueryBuilder dQuery = ESUtil.cdtToBuilder(dirQuery);
        List<ESShapeDir> dirs = this.dirSvc.getSortListByQuery(1, 3000, dQuery, "createTime", true).getData();
        Map<Long, ESShapeDir> dirMap = BinaryUtils.toObjectMap(dirs, "id");
        // 查询我的形状
        ESUserShapeQuery shapeQuery = new ESUserShapeQuery();
        shapeQuery.setUserId(user.getId());
        shapeQuery.setImgName(bean.getKeyword());
        shapeQuery.setDirIds(dirMap.keySet().toArray(new Long[0]));
        QueryBuilder query = ESUtil.cdtToBuilder(shapeQuery);
        if (query == null) {
            return Collections.emptyList();
        } else {
            List<SortBuilder<?>> sorts = new LinkedList();
            ArrayList<ESUserShapeResult> list = new ArrayList<>();
            sorts.add(SortBuilders.fieldSort("orderNo").order(SortOrder.ASC));
            // 先不考虑用户形状数量超过3000的可能
            Page<ESUserShape> page = this.svc.getSortListByQuery(1, 3000, query, sorts);
            List<ESUserShape> data = page.getData();
            Map<Long, List<ESUserShape>> collect = data.stream().collect(Collectors.groupingBy(ESUserShape::getDirId));
            collect.forEach((key, value) -> {
                ESUserShapeResult result = new ESUserShapeResult();
                result.setDir(this.dirSvc.getById(key));
                result.setShapes(value);
                list.add(result);
            });
            return list;
        }
    }

    public boolean judgeContainsStr(String cardNum) {
        String regex = ".*[a-zA-Z]+.*";
        Matcher m = Pattern.compile(regex).matcher(cardNum);
        return m.matches();
    }

    /**
     * 获取下一个orderNo
     *
     * @param dirId
     * @author: jianghaojie
     * @date: 2020/8/6 10:45
     * @return: int
     */
    private long getNextOrderNo(long dirId) {
        BoolQueryBuilder countQuery = QueryBuilders.boolQuery();
        countQuery.must(QueryBuilders.termQuery("dirId", dirId));
        List<SortBuilder<?>> sorts = new LinkedList<>();
        sorts.add(SortBuilders.fieldSort("orderNo").order(SortOrder.DESC).unmappedType("long"));
        Page<ESUserShape> page = this.svc.getSortListByQuery(1, 1, QueryBuilders.constantScoreQuery(countQuery), sorts);
        if (page.getData().isEmpty()) {
            return 0;
        }
        if (page.getData().get(0).getOrderNo() == null) {
            return 0;
        }
        return page.getData().get(0).getOrderNo() + 1;
    }

    ///////////////////

    @Override
    public List<ESUserShape> queryMyShapes() {
        SysUser loginUser = SysUtil.getCurrentUserInfo();
        Long userId = loginUser.getId();
        ESUserShapeQuery esUserShapeQuery = new ESUserShapeQuery();
        esUserShapeQuery.setUserId(userId);
//        Long[] longs = new Long[]{7729965595959032L, 7729965595959031L};
//        esUserShapeQuery.setIds(longs);
        BoolQueryBuilder boolQueryBuilder = ESUtil.cdtToBuilder(esUserShapeQuery);
        Page<ESUserShape> esDiagramPage = this.svc.getSortListByQuery(1, 100, boolQueryBuilder, "createTime", false);
        //2.填充缩略图路径信息
        List<ESUserShape> esDiagramList = esDiagramPage.getData();
        return esDiagramList;
    }

    @Override
    public List<ESUserShape> queryImagePage(Integer pageNum, Integer pageSize, Long dirId, String keyword) {
        ESUserShapeQuery shapeQuery = new ESUserShapeQuery();
        shapeQuery.setDirId(dirId);
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        shapeQuery.setUserId(currentUserInfo.getId());
        BoolQueryBuilder query = ESUtil.cdtToBuilder(shapeQuery);
        if (!StringUtils.isEmpty(keyword)) {
            query.must(QueryBuilders.wildcardQuery("imgName.keyword", "*" + keyword + "*"));
        }
        SortOrder orderType = true ? SortOrder.ASC : SortOrder.DESC;
        List<SortBuilder<?>> sorts = new LinkedList();
        sorts.add(SortBuilders.fieldSort("orderNo").order(orderType));
        Page<ESUserShape> sortListByQuery = this.svc.getSortListByQuery(pageNum, pageSize, query, sorts);
        List<ESUserShape> data = sortListByQuery.getData();
        return data;
    }


    @Override
    public ResponseEntity<byte[]> exportImageZipByDirIds(Set<Long> dirIds) {
        return null;
    }

    @Override
    public ImportDirMessage import3DZipImage(MultipartFile file, Long dirId, Map<ZipEntry, byte[]> zipEntriesMap, boolean isCover) {
        return null;
    }

    @Override
    public ImportResultMessage importZipImage(Integer sourceType, MultipartFile file) {
        return null;
    }

    @Override
    public List<CcImage> queryTopImage(SearchKeywordBean bean) {
        return Collections.emptyList();
    }

    @Override
    public boolean importImage(Long dirId, MultipartFile file) {
        return false;
    }

    @Override
    public ImportDirMessage importImages(Long dirId, MultipartFile... files) {
        return null;
    }

    @Override
    public boolean replaceImage(Long imageId, MultipartFile file) {
        return false;
    }

    @Override
    public boolean replace3DImage(Long imageId, MultipartFile file) {
        return false;
    }

    @Override
    public boolean delete3DImage(CcImage image) {
        return false;
    }

    @Override
    public CcImage queryImageById(Long id) {
        return null;
    }

    @Override
    public Long countBySearchBean(ESSearchImageBean bean) {
        return null;
    }

    @Override
    public ResponseEntity<byte[]> downloadImageResource(List<Long> ids) {
        return null;
    }

}
