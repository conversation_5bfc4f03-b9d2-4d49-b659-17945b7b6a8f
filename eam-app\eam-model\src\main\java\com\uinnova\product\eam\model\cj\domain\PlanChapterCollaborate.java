package com.uinnova.product.eam.model.cj.domain;

import com.uinnova.product.eam.base.cj.BaseEntity;
import lombok.Data;

import java.util.List;

/**
 * 方案章节协作
 */
@Data
public class PlanChapterCollaborate extends BaseEntity {
    /** 方案id */
    private Long planId;
    /** 章节id */
    private Long chapterId;
    /** 分享的成员列表 */
    private List<String> shareMemberList;
    /** 是否完成 0：未完成 1：已完成 */
    private Integer complete;
}
