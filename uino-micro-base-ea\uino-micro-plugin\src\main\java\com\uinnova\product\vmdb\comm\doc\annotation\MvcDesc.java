package com.uinnova.product.vmdb.comm.doc.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface MvcDesc {

    /**
     * 责任人
     * 
     * @return
     */
    String author();

    /**
     * 描述
     * 
     * @return
     */
    String desc();

}
