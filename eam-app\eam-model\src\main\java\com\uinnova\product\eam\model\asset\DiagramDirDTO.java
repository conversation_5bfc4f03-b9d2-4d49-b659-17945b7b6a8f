package com.uinnova.product.eam.model.asset;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.io.Serializable;

@Data
public class DiagramDirDTO implements Serializable {


    private Long id;

    private String dirName;

    private Long dirType;

    private Long parentId;


    private Long userId;


    private Integer dirLvl;


    private String dirPath;


    private Integer orderNo;


    private Integer isLeaf;


    private String icon;


    private String dirDesc;


    private Long domainId;


    private Integer dataStatus;


    private String creator;


    private String modifier;


    private Long createTime;


    private Long modifyTime;

    private Long subjectId;

    private Long oldParentId;

    private Integer dirInit;

    private String esSysId;

    // 关联文件，1：企业级我的文件，2：系统级我的文件
    private Integer relationFile;

    private String sysType;

    // monet应用系统编码
    private String ciCode;
    @Comment("0表示普通文件夹，1表示分类实例文件夹，2表示分类文件夹")
    private Integer sysDir;
}
