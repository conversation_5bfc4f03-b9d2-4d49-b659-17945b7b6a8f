package com.uino.init.http.safe;

import com.uino.bean.permission.base.SysUser;
import com.uino.api.client.permission.IUserApiSvc;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;

/**
 * @Title: DeveloperEnableApi
 * @Description: Developer environment displays API documentation
 * @Author: YGQ
 * @Create: 2021-04-28 11:11
 **/
@Component
public class DeveloperEnableApi {

    private static final Logger log = LoggerFactory.getLogger(DeveloperEnableApi.class);
    private static Boolean isDeveloper;

    @Value("${api.enable:false}")
    private Boolean apiEnable;

    @Value("${api.enable.user-id:null}")
    private String apiEnableUserId;

    @Value("${api.enable.login-code:null}")
    private String apiEnableLoginCode;

    private final IUserApiSvc iUserApiSvc;

    public DeveloperEnableApi(IUserApiSvc iUserApiSvc) {
        this.iUserApiSvc = iUserApiSvc;
    }

    @PostConstruct
    public void init() {
        if (apiEnable && isDeveloper == null) {
            try {
                if (apiEnableUserId != null && apiEnableLoginCode != null) {
                    Long userId = Long.parseLong(apiEnableUserId);
                    SysUser user = iUserApiSvc.getUserInfoById(userId);
                    String localDeveloperL_G_C_D = apiEnableLoginCode.trim();
                    String userL_G_C_D = user.getLoginCode().trim();
                    if (localDeveloperL_G_C_D.equals(userL_G_C_D)) {
                        isDeveloper = true;
                        log.info("Detect that it is currently in developer mode, load local user: {}", userL_G_C_D);
                    }
                }
            } catch (Exception ignored) {
            }
        }
    }

    public Boolean isDevelopEnvironment() {
        return isDeveloper;
    }
}
