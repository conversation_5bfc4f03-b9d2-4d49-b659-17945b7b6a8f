package com.uinnova.product.eam.model;

import com.binary.framework.bean.annotation.Comment;
import com.uino.bean.cmdb.base.LibType;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class EamDiagramRelationSysCdt implements Serializable {

    @Comment("视图编码")
    private String diagramEnergy;


    @Comment("文件夹id")
    private Long dirId;


    @Comment("系统id")
    private String esSysId;

    @Comment("视图类型")
    private String diagramClassType ="flowDiagram";

    private LibType libType;

}
