package com.uinnova.product.eam.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.binary.core.exception.BinaryException;
import com.binary.core.util.BinaryUtils;
import com.uinnova.product.eam.base.util.EamUtil;
import com.uinnova.product.eam.comm.model.es.EamCategory;
import com.uinnova.product.eam.comm.model.es.EamHierarchyNavigation;
import com.uinnova.product.eam.model.dto.EamHierarchyDto;
import com.uinnova.product.eam.model.dto.EamHierarchyNavigationDto;
import com.uinnova.product.eam.model.enums.CategoryTypeEnum;
import com.uinnova.product.eam.service.EamCategorySvc;
import com.uinnova.product.eam.service.IBmNavigationHierarchySvc;
import com.uinnova.product.eam.service.es.BmNavigationHierarchyDao;
import com.uinnova.product.eam.service.es.EamCategoryDesignDao;
import com.uinnova.product.eam.service.es.EamCategoryPrivateDao;
import com.uino.bean.cmdb.base.LibType;
import com.uino.dao.util.ESUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class BmNavigationHierarchySvcImpl implements IBmNavigationHierarchySvc {

    @Resource
    private BmNavigationHierarchyDao navigationHierarchyDao;

    @Resource
    private EamCategoryPrivateDao categoryPrivateDao;

    @Resource
    private EamCategoryDesignDao categoryDesignDao;

    @Resource
    private EamCategorySvc eamCategorySvc;

    /**
     * 获取导航列表
     *
     * @param hierarchyDtoList 层级DTO列表
     * @param ownerCode 所有者代码
     * @return 导航层级DTO列表
     */
    @Override
    public List<EamHierarchyNavigationDto> getNavigationList(List<EamHierarchyDto> hierarchyDtoList, String ownerCode) {
        // 参数校验
        if (CollectionUtils.isEmpty(hierarchyDtoList)) {
            return Collections.emptyList();
        }

        // 1. 提取模型ID和层级ID
        Set<Long> modelIds = new HashSet<>();
        Set<Long> modelLvlIds = new HashSet<>();

        for (EamHierarchyDto dto : hierarchyDtoList) {
            if (dto.getModelId() != null) {
                modelIds.add(dto.getModelId());
            }
            if (dto.getId() != null) {
                modelLvlIds.add(dto.getId());
            }
        }

        if (modelIds.isEmpty() || modelLvlIds.isEmpty()) {
            return Collections.emptyList();
        }

        // 2. 确定库类型
        LibType libType = BinaryUtils.isEmpty(ownerCode) ? LibType.DESIGN : LibType.PRIVATE;

        // 3. 查询导航条关联数据
        List<EamHierarchyNavigation> existingNavigations = queryExistingNavigations(modelIds, modelLvlIds, ownerCode, libType);

        // 4. 创建导航条映射（模型ID-层级ID -> 导航条）
        Map<String, EamHierarchyNavigation> navigationMap = createNavigationMap(existingNavigations);

        // 5. 获取模型层级视图状况
        Map<Long, Map<Integer, Boolean>> modelLvlHasDiagram = getModelLvlHasDiagram(modelIds, ownerCode);

        // 6. 处理每个层级DTO，构建结果
        List<EamHierarchyNavigationDto> result = new ArrayList<>(hierarchyDtoList.size());
        List<EamHierarchyNavigation> needInitNavigationList = new ArrayList<>();

        for (EamHierarchyDto hierarchyDto : hierarchyDtoList) {
            // 复制基本属性
            EamHierarchyNavigationDto dto = EamUtil.copy(hierarchyDto, EamHierarchyNavigationDto.class);

            // 获取模型ID和层级ID
            Long modelId = hierarchyDto.getModelId();
            Long modelLvlId = hierarchyDto.getId();

            if (modelId == null || modelLvlId == null) {
                continue;
            }

            // 获取或创建导航条关联数据
            String navigationKey = modelId + "-" + modelLvlId;
            EamHierarchyNavigation navigation = navigationMap.get(navigationKey);

            if (navigation == null) {
                // 创建新的导航条关联数据
                navigation = createNewNavigation(modelId, modelLvlId, hierarchyDto, modelLvlHasDiagram, ownerCode, libType);
                needInitNavigationList.add(navigation);
            }

            // 设置完成状态
            dto.setCompletionStatus(navigation.getStatus());

            // 设置完成时间（如果状态为已完成）
            if (Integer.valueOf(2).equals(navigation.getStatus()) && navigation.getFinishTime() != null) {
                dto.setFinishTime(formatFinishTime(navigation.getFinishTime()));
            }

            result.add(dto);
        }

        // 7. 保存新创建的导航条关联数据
        if (!needInitNavigationList.isEmpty()) {
            log.info("初始化导航条关联数据，数量: {}", needInitNavigationList.size());
            navigationHierarchyDao.saveOrUpdateBatch(needInitNavigationList);
        } else {
            log.info("模型导航条无需更新");
        }

        return result;
    }

    @Override
    public Boolean changeHierarchyStatus(Long hierarchyId, Long modelId, Integer state, String ownerCode, LibType libType) {
        if (BinaryUtils.isEmpty(hierarchyId) || BinaryUtils.isEmpty(modelId) ||
                BinaryUtils.isEmpty(state) || BinaryUtils.isEmpty(ownerCode) || BinaryUtils.isEmpty(libType)) {
            throw new BinaryException("参数缺失或为空！");
        }
        // 获取关联表对应导航目录完成状态信息
        BoolQueryBuilder bool = QueryBuilders.boolQuery();
        bool.must(QueryBuilders.termQuery("modelId", modelId));
        bool.must(QueryBuilders.termQuery("modelLvlId", hierarchyId));
        if (libType == LibType.PRIVATE) {
            bool.must(QueryBuilders.termQuery("ownerCode.keyword", ownerCode));
        }
        bool.must(QueryBuilders.termQuery("libType.keyword", libType));

        EamHierarchyNavigation eamHierarchyNavigation = navigationHierarchyDao.selectOne(bool);
        if (BinaryUtils.isEmpty(eamHierarchyNavigation)) {
            throw new BinaryException("层级获取失败");
        }
        JSONObject json = JSON.parseObject(JSON.toJSONString(eamHierarchyNavigation));
        if (2 == state) {
            json.put("finishTime", ESUtil.getNumberDateTime());
        } else {
            json.put("finishTime", null);
        }
        json.put("status", state);
        navigationHierarchyDao.saveOrUpdate(json, Boolean.TRUE);
        return Boolean.TRUE;
    }

    @Override
    public Boolean deleteHierarchy(Long modelId, String ownerCode, LibType libType) {
        BoolQueryBuilder bool = QueryBuilders.boolQuery();
        bool.must(QueryBuilders.termQuery("modelId", modelId));
        if (libType == LibType.PRIVATE) {
            bool.must(QueryBuilders.termQuery("ownerCode.keyword", ownerCode));
        }
        bool.must(QueryBuilders.termQuery("libType.keyword", libType));
        List<EamHierarchyNavigation> deleteInfo = navigationHierarchyDao.getListByQuery(bool);
        if (CollectionUtils.isEmpty(deleteInfo)) {
            log.debug("层级导航信息不存在，无需删除");
            return Boolean.TRUE;
        } else {
            log.debug("删除层级导航信息，数量: {}，详情：{}", deleteInfo.size(), JSON.toJSONString(deleteInfo));
            navigationHierarchyDao.deleteByIds(deleteInfo.stream().map(EamHierarchyNavigation::getId).collect(Collectors.toList()));
            return Boolean.TRUE;
        }


    }

    /**
     * 查询已存在的导航条关联数据
     */
    private List<EamHierarchyNavigation> queryExistingNavigations(Set<Long> modelIds, Set<Long> modelLvlIds,
                                                                 String ownerCode, LibType libType) {
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery()
                .must(QueryBuilders.termsQuery("modelId", modelIds))
                .must(QueryBuilders.termsQuery("modelLvlId", modelLvlIds))
                .must(QueryBuilders.termQuery("libType.keyword", libType));

        if (LibType.PRIVATE.equals(libType) && !BinaryUtils.isEmpty(ownerCode)) {
            queryBuilder.must(QueryBuilders.termQuery("ownerCode.keyword", ownerCode));
        }

        return navigationHierarchyDao.getListByQuery(queryBuilder);
    }

    /**
     * 创建导航条映射
     */
    private Map<String, EamHierarchyNavigation> createNavigationMap(List<EamHierarchyNavigation> navigations) {
        if (CollectionUtils.isEmpty(navigations)) {
            return new HashMap<>();
        }

        return navigations.stream()
                .filter(nav -> nav.getModelId() != null && nav.getModelLvlId() != null)
                .collect(Collectors.toMap(
                    nav -> nav.getModelId() + "-" + nav.getModelLvlId(),
                    Function.identity(),
                    (existing, replacement) -> existing
                ));
    }

    /**
     * 创建新的导航条关联数据
     */
    private EamHierarchyNavigation createNewNavigation(Long modelId, Long modelLvlId, EamHierarchyDto hierarchyDto,
                                                     Map<Long, Map<Integer, Boolean>> modelLvlHasDiagram,
                                                     String ownerCode, LibType libType) {
        EamHierarchyNavigation navigation = new EamHierarchyNavigation();
        navigation.setLibType(libType);
        navigation.setId(ESUtil.getUUID());
        navigation.setFinishTime(null);
        navigation.setOwnerCode(ownerCode);
        navigation.setModelLvlId(modelLvlId);
        navigation.setModelId(modelId);

        // 确定初始状态：模型层级存在关联模型视图时为进行中(1)状态，否则为未开始(0)
        Map<Integer, Boolean> lvlStatusMap = modelLvlHasDiagram.get(modelId);
        Integer dirLvl = hierarchyDto.getDirLvl();

        boolean hasRelatedDiagram = false;
        if (lvlStatusMap != null && dirLvl != null && lvlStatusMap.get(dirLvl) != null) {
            hasRelatedDiagram = lvlStatusMap.get(dirLvl);
        }

        navigation.setStatus(hasRelatedDiagram ? 1 : 0);

        return navigation;
    }

    /**
     * 格式化完成时间
     */
    private String formatFinishTime(Long finishTime) {
        if (finishTime == null) {
            return null;
        }

        String timeStr = finishTime.toString();
        if (timeStr.length() < 8) {
            return timeStr;
        }

        return timeStr.substring(0, 4) + "-" +
               timeStr.substring(4, 6) + "-" +
               timeStr.substring(6, 8);
    }

    /**
     * 根据模型id获取对应模型下层级关联视图状况
     *
     * @param modelIds 模型ID集合
     * @param ownerCode 所有者代码
     * @return 模型ID -> {层级 -> 是否有视图} 的映射
     */
    private Map<Long, Map<Integer, Boolean>> getModelLvlHasDiagram(Collection<Long> modelIds, String ownerCode) {
        Map<Long, Map<Integer, Boolean>> result = new HashMap<>();

        // 参数校验
        if (CollectionUtils.isEmpty(modelIds)) {
            return result;
        }

        // 确定查询的库类型
        LibType libType = BinaryUtils.isEmpty(ownerCode) ? LibType.DESIGN : LibType.PRIVATE;

        // 构建查询条件
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery()
                .must(QueryBuilders.termsQuery("modelId", modelIds))
                .must(QueryBuilders.termQuery("dataStatus", 1)); // 只查询有效数据

        if (LibType.PRIVATE.equals(libType)) {
            queryBuilder.must(QueryBuilders.termQuery("ownerCode.keyword", ownerCode));
        }

        List<EamCategory> categoryList = LibType.DESIGN.equals(libType)
                ? categoryDesignDao.getListByQuery(queryBuilder)
                : categoryPrivateDao.getListByQuery(queryBuilder);

        if (CollectionUtils.isEmpty(categoryList)) {
            return result;
        }

        // 1. 提取模型根目录层级信息
        Map<Long, Integer> modelRootLevelMap = categoryList.stream()
                .filter(category -> CategoryTypeEnum.MODEL_ROOT.val() == category.getType())
                .collect(Collectors.toMap(
                        EamCategory::getModelId,
                        EamCategory::getDirLvl,
                        (existing, replacement) -> existing
                ));

        // 2. 按模型ID和目录层级分组非根目录
        Map<Long, Map<Integer, List<EamCategory>>> modelLevelCategoriesMap = categoryList.stream()
                .filter(category -> CategoryTypeEnum.MODEL_ROOT.val() != category.getType())
                .collect(Collectors.groupingBy(
                        EamCategory::getModelId,
                        Collectors.groupingBy(EamCategory::getDirLvl)
                ));

        // 3. 处理每个模型的层级视图状况
        for (Map.Entry<Long, Map<Integer, List<EamCategory>>> modelEntry : modelLevelCategoriesMap.entrySet()) {
            Long modelId = modelEntry.getKey();
            Map<Integer, List<EamCategory>> levelCategoriesMap = modelEntry.getValue();

            // 获取根目录层级，如果没有找到根目录，使用默认值0
            Integer rootLevel = modelRootLevelMap.getOrDefault(modelId, 0) + 1;

            // 创建该模型的层级视图状况映射
            Map<Integer, Boolean> modelLevelDiagramMap = new HashMap<>();

            // 处理每个层级
            for (Map.Entry<Integer, List<EamCategory>> levelEntry : levelCategoriesMap.entrySet()) {
                Integer level = levelEntry.getKey();
                List<EamCategory> levelCategories = levelEntry.getValue();

                // 计算相对层级
                Integer relativeLevel = level - rootLevel;

                // 检查该层级是否有视图
                boolean hasDiagram = levelCategories.stream()
                        .anyMatch(category -> !BinaryUtils.isEmpty(category.getDiagramId()));

                // 记录该层级的视图状况
                modelLevelDiagramMap.put(relativeLevel, hasDiagram);
            }

            // 将该模型的层级视图状况添加到结果中
            result.put(modelId, modelLevelDiagramMap);
        }
        return result;
    }
}
