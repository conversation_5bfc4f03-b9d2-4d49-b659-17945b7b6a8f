package com.uinnova.product.eam.model.cj.dto;

import com.uino.bean.cmdb.base.LibType;
import lombok.Builder;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 *
 * <p>
 * 重命名
 * <p>
 *
 * @autor songzhenqing
 * @date 2024/11/14
 */
@Data
public class RenameRequestDTO {
    /**
     * 重命名类型
     */
    private String type;
    /**
     * 需要重命名类型的id
     */
    @NotBlank(message = "重命名ID不能为空")
    private Long id;
    /**
     * 重命名之后的名字
     */
    @NotBlank(message = "重命名名字不能为空")
    private String rename;
    /**
     * 库 默认私有库
     */
    private LibType libType = LibType.PRIVATE;
}
