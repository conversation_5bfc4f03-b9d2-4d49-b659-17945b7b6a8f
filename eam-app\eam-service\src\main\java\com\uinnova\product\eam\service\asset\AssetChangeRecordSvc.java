package com.uinnova.product.eam.service.asset;

import com.uinnova.product.eam.comm.model.es.AssetChangeRecord;

import java.util.List;

/**
 * 架构资产流程记录表
 * <AUTHOR>
 */
public interface AssetChangeRecordSvc {
    /**
     * 保存变更信息
     * @param changeRecord
     * @return
     */
    Long saveChangeRecord(AssetChangeRecord changeRecord);

    /**
     * 根据CiCode获取资产流程信息
      * @param ciCode
     * @return
     */
    List<AssetChangeRecord> getAssetChangeRecordByCiCode(String ciCode);

    /**
     * 根据id获取
     * @param id
     * @return
     */
    AssetChangeRecord getById(Long id);


}
