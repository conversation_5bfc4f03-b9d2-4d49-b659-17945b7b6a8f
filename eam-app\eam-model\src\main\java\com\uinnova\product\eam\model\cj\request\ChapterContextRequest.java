package com.uinnova.product.eam.model.cj.request;

import com.uinnova.product.eam.model.cj.vo.ContextModuleVO;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 方案章节上下文请求参数
 *
 * <AUTHOR>
 */
@Data
public class ChapterContextRequest {

    /**
     * 所属章节id， 跟章节用同一个id
     */
    @NotNull(message = "章节id必填")
    private Long id;

    /**
     * 方案id
     */
    @NotNull(message = "方案id必填")
    private Long planId;

    /**
     * 章节名称
     */
    @NotBlank(message = "章节名称必填")
    private String name;

    /**
     * 是否可自行添加内容块
     */
    private Boolean userAddContent;

    /**
     * 章节说明
     */
    private String chapterDesc;

    /**
     * 上下文内容块
     */
    private List<ContextModuleVO> moduleList;

    /**
     * 引入章节的名称
     */
    private String introduceChapterName;
    /**
     * 引用方式  1表示插入 2表示覆盖
     */
    private Integer introduceWay;
    /**
     * 引入章节的方案的id
     */
    private Long introducePlanId;
    /**
     * 引入章节的id
     */
    private Long introduceChapterId;


}
