package com.uinnova.product.eam.model;

import com.uinnova.product.eam.base.diagram.model.ESSimpleDiagramDTO;
import com.uinnova.product.eam.comm.model.es.EamRecentlyView;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class RecentlyViewBo implements Serializable {
    private static final long serialVersionUID = 1905122041950251208L;
    List<EamRecentlyView> recentlyViewList;
    List<ESSimpleDiagramDTO> diagramDTOList;
}
