package com.uino.bean.tp.query;


import lombok.Data;

import java.util.List;

@Data
public class QueryDataTableDTO {

    //数据集ID
    private Long dataSetId;

    //第一个子节点id
    private Long classId;

    //表头
    private List<String> headList;

    //属性名称
    private String attrType;

    // 卡片名称导出用
    private String cardName;

    // 根节点名称 标题用
    private String rootName;

    // 筛选的内容
    private List<String> contentList;

}
