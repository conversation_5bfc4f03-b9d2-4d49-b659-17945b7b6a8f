package com.uino.bean.cmdb.business;

import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 导入ci关系-数据行结构数据传输类
 * 
 * <AUTHOR>
 *
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ImportCiRltDataDto {

    /**
     * 指定的入库id
     */
    private Long id;

    /**
     * 源ci所属分类code
     */
    private String sCiClassCode;

    /**
     * 源ciCode
     */
    private String sCiCode;

    /**
     * 目标ci所属分类code
     */
    private String tCiClassCode;

    /**
     * 目标ciCode
     */
    private String tCiCode;

    /**
     * 本关系所属关系分类code
     */
    private String rltClassCode;

    /**
     * 本条关系的属性
     */
    private Map<String, String> attrs;

    /**
     * 该条关系所属sheet中行号
     */
    private int sheetRowIndex;
}
