package com.binary.framework.bean;

import java.io.Serializable;

import com.binary.core.i18n.Language;

public interface User extends Serializable {
	
	
	/**
	 * 获取主键ID
	 * @return
	 */
	public Long getId();
	
	
	
	/**
	 * 获取用户代码
	 * @return
	 */
	public String getUserCode();
	
	
	
	/**
	 * 获取用户姓名
	 * @return
	 */
	public String getUserName();
	
	
	
	/**
	 * 获取登录代码
	 * @return
	 */
	public String getLoginCode();
	
	
	
	/**
	 * 获取认证识别码
	 * @return
	 */
	public String getAuthCode();
	
	
	
	
	/**
	 * 获取用户指定语言
	 * @return
	 */
	public Language getLanguage();
	
	
	
	
	/**
	 * 获取用户域ID
	 * @return
	 */
	public Long getDomainId();
	
	
	
	
	/**
	 * 获取用户类型
	 * @return
	 */
	public Integer getKind();
	
	
	
	
}
