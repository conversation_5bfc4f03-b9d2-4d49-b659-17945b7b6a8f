package com.uinnova.product.eam.comm.model;




import cn.hutool.crypto.SecureUtil;
import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;


@Comment("视图设计版本表[VC_DIAGRAM_VERSION]")
public class VcDiagramVersion implements EntityBean {
	private static final long serialVersionUID = 1L;


	@Comment("ID[ID]")
	@JsonIgnore
	private Long id;

	@Comment("源视图ID[DIAGRAM_ID]")
	@JsonIgnore
	private Long diagramId;


	@Comment("版本号[VERSION_NO]")
	private String versionNo;


	@Comment("版本时间[VERSION_TIME]")
	private Long versionTime;

	@Comment("视图ID加密字段")
	@JsonProperty("diagramId")
	private String dEnergy;

	@Comment("视图ID加密字段")
	@JsonProperty("id")
	private transient Object energy;

	@Comment("版本描述[VERSION_DESC]")
	private String versionDesc;


	@Comment("视图名称[NAME]")
	private String name;


	@Comment("所属用户[USER_ID]")
	private Long userId;


	@Comment("所属目录[DIR_ID]")
	private Long dirId;


	@Comment("视图描述[DIAGRAM_DESC]")
	private String diagramDesc;


	@Comment("视图SVG[DIAGRAM_SVG]")
	private String diagramSvg;


	@Comment("视图XML[DIAGRAM_XML]")
	private String diagramXml;


	@Comment("视图JSON[DIAGRAM_JSON]    视图json格式信息")
	private String diagramJson;


	@Comment("视图图标_1[ICON_1]")
	private String icon1;


	@Comment("视图图标_2[ICON_2]")
	private String icon2;


	@Comment("视图图标_3[ICON_3]")
	private String icon3;


	@Comment("视图图标_4[ICON_4]")
	private String icon4;


	@Comment("视图图标_5[ICON_5]")
	private String icon5;


	@Comment("视图状态[STATUS]    视图状态:1=正常 0=回收站")
	private Integer status;


	@Comment("CI3D坐标[CI_3D_POINT]")
	private String ci3dPoint;


	@Comment("CI3D坐标2[CI_3D_POINT2]")
	private String ci3dPoint2;


	@Comment("版本描述文件路径信息[VERSION_DESC_PATH]")
	private String versionDescPath;


	@Comment("所属域[DOMAIN_ID]")
	private Long domainId;


	@Comment("数据状态[DATA_STATUS]    数据状态:0=删除，1=正常")
	private Integer dataStatus;


	@Comment("创建人[CREATOR]")
	private String creator;


	@Comment("修改人[MODIFIER]")
	private String modifier;


	@Comment("创建时间[CREATE_TIME]    创建时间:yyyyMMddHHmmss")
	private Long createTime;


	@Comment("更新时间[MODIFY_TIME]    更新时间:yyyyMMddHHmmss")
	private Long modifyTime;


	@Comment("视图背景[DIAGRAM_BG_CSS]")
	private String diagramBgCss;

	@Comment("关联视图发布id[RELEASE_ID]")
	private Long releaseId;

	@Comment("已发布视图版本[RELEASE_VERSION]")
	private int releaseVersion;

	public String getdEnergy() {
		return SecureUtil.md5(String.valueOf(diagramId)).substring(8, 24);
	}

	public Object getEnergy() {
		return SecureUtil.md5(String.valueOf(id)).substring(8, 24);
	}

	public Long getId() {
		return this.id;
	}
	public void setId(Long id) {
		this.id = id;
	}


	public Long getDiagramId() {
		return this.diagramId;
	}
	public void setDiagramId(Long diagramId) {
		this.diagramId = diagramId;
	}


	public String getVersionNo() {
		return this.versionNo;
	}
	public void setVersionNo(String versionNo) {
		this.versionNo = versionNo;
	}


	public Long getVersionTime() {
		return this.versionTime;
	}
	public void setVersionTime(Long versionTime) {
		this.versionTime = versionTime;
	}


	public String getVersionDesc() {
		return this.versionDesc;
	}
	public void setVersionDesc(String versionDesc) {
		this.versionDesc = versionDesc;
	}


	public String getName() {
		return this.name;
	}
	public void setName(String name) {
		this.name = name;
	}


	public Long getUserId() {
		return this.userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}


	public Long getDirId() {
		return this.dirId;
	}
	public void setDirId(Long dirId) {
		this.dirId = dirId;
	}


	public String getDiagramDesc() {
		return this.diagramDesc;
	}
	public void setDiagramDesc(String diagramDesc) {
		this.diagramDesc = diagramDesc;
	}


	public String getDiagramSvg() {
		return this.diagramSvg;
	}
	public void setDiagramSvg(String diagramSvg) {
		this.diagramSvg = diagramSvg;
	}


	public String getDiagramXml() {
		return this.diagramXml;
	}
	public void setDiagramXml(String diagramXml) {
		this.diagramXml = diagramXml;
	}


	public String getDiagramJson() {
		return this.diagramJson;
	}
	public void setDiagramJson(String diagramJson) {
		this.diagramJson = diagramJson;
	}


	public String getIcon1() {
		return this.icon1;
	}
	public void setIcon1(String icon1) {
		this.icon1 = icon1;
	}


	public String getIcon2() {
		return this.icon2;
	}
	public void setIcon2(String icon2) {
		this.icon2 = icon2;
	}


	public String getIcon3() {
		return this.icon3;
	}
	public void setIcon3(String icon3) {
		this.icon3 = icon3;
	}


	public String getIcon4() {
		return this.icon4;
	}
	public void setIcon4(String icon4) {
		this.icon4 = icon4;
	}


	public String getIcon5() {
		return this.icon5;
	}
	public void setIcon5(String icon5) {
		this.icon5 = icon5;
	}


	public Integer getStatus() {
		return this.status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}


	public String getCi3dPoint() {
		return this.ci3dPoint;
	}
	public void setCi3dPoint(String ci3dPoint) {
		this.ci3dPoint = ci3dPoint;
	}


	public String getCi3dPoint2() {
		return this.ci3dPoint2;
	}
	public void setCi3dPoint2(String ci3dPoint2) {
		this.ci3dPoint2 = ci3dPoint2;
	}


	public String getVersionDescPath() {
		return this.versionDescPath;
	}
	public void setVersionDescPath(String versionDescPath) {
		this.versionDescPath = versionDescPath;
	}


	public Long getDomainId() {
		return this.domainId;
	}
	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}


	public Integer getDataStatus() {
		return this.dataStatus;
	}
	public void setDataStatus(Integer dataStatus) {
		this.dataStatus = dataStatus;
	}


	public String getCreator() {
		return this.creator;
	}
	public void setCreator(String creator) {
		this.creator = creator;
	}


	public String getModifier() {
		return this.modifier;
	}
	public void setModifier(String modifier) {
		this.modifier = modifier;
	}


	public Long getCreateTime() {
		return this.createTime;
	}
	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}


	public Long getModifyTime() {
		return this.modifyTime;
	}
	public void setModifyTime(Long modifyTime) {
		this.modifyTime = modifyTime;
	}


	public String getDiagramBgCss() {
		return this.diagramBgCss;
	}
	public void setDiagramBgCss(String diagramBgCss) {
		this.diagramBgCss = diagramBgCss;
	}

	public Long getReleaseId() {
		return releaseId;
	}
	public void setReleaseId(Long releaseId) {
		this.releaseId = releaseId;
	}

	public int getReleaseVersion() {
		return releaseVersion;
	}
	public void setReleaseVersion(int releaseVersion) {
		this.releaseVersion = releaseVersion;
	}
}


