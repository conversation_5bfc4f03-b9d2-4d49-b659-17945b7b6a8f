package com.uino.service.simulation.impl;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;

import com.uino.dao.util.ESUtil;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.MultiMatchQueryBuilder.Type;
import org.elasticsearch.index.query.Operator;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import com.binary.core.util.BinaryUtils;
import com.binary.jdbc.Page;
import com.uino.bean.monitor.base.SimulationRuleInfo;
import com.uino.bean.monitor.query.SimulationRuleSearchBean;
import com.uino.dao.BaseConst;
import com.uino.dao.simulation.ESSimulationRuleSvc;
import com.uino.service.simulation.ISimulationRuleSvc;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class SimulationRuleSvc implements ISimulationRuleSvc {

	@Autowired
	private ESSimulationRuleSvc ruleSvc;

	@Value("${monitor.select.include.domainId:true}")
	private boolean includeDomainId;

	@Override
	public Long saveOrUpdateSimulationRule(SimulationRuleInfo ruleInfo) {
		if (ruleInfo.getDomainId() == null) {
			ruleInfo.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
		}
	/*	// 基础信息校验
		ruleInfo.valid();
		// 规则校验
		this.validSimulationRule(ruleInfo);*/

		if(ruleInfo.getRuleType() == 1){
			ruleInfo.valid();
		}else{
			Assert.isTrue(ruleInfo.getClassId() != null || !BinaryUtils.isEmpty(ruleInfo.getCiIds()), "X_PARAM_NOT_NULL${name:classId/ciIds}");
			Assert.isTrue(ruleInfo.getRuleType() != null, "X_PARAM_NOT_NULL${name:ruleType}");
			Assert.isTrue(ruleInfo.startTime != null, "X_PARAM_NOT_NULL${name:startTime}");
			Assert.isTrue(!BinaryUtils.isEmpty(ruleInfo.getMetric()), "X_PARAM_NOT_NULL${name:metric}");
		}
		this.validSimulationRule(ruleInfo);
		if (ruleInfo.getId() != null) {
			try {
				SimulationRuleAutoTaskSvc.ES_CI_INFO_CACHE.invalidate(ruleInfo.getId());
			} catch (Exception e) {
				log.error("模拟规则[" + ruleInfo.getRuleName() + "]缓存清除失败！");
			}
		}
		//暂时:先删除再更新
		if (ruleInfo.getId() != null) {
			ruleSvc.deleteById(ruleInfo.getId());
		}
		return ruleSvc.saveOrUpdate(ruleInfo);
	}

	@Override
	public Page<SimulationRuleInfo> querySimulationRuleInfoPage(SimulationRuleSearchBean bean) {
		if (includeDomainId && bean.getDomainId() == null) {
			bean.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
		}
		BoolQueryBuilder query = QueryBuilders.boolQuery();
		query.must(QueryBuilders.termQuery("domainId", bean.getDomainId()));
		if (bean.getRuleStatus() != null) {
			query.must(QueryBuilders.termQuery("ruleStatus", bean.getRuleStatus()));
		}
		if (bean.getRuleType() != null) {
			query.must(QueryBuilders.termQuery("ruleType", bean.getRuleType()));
		}
		if (!BinaryUtils.isEmpty(bean.getKeyword())) {
			query.must(QueryBuilders.multiMatchQuery(bean.getKeyword(), "ruleName").operator(Operator.AND)
					.type(Type.PHRASE_PREFIX).lenient(true));
		}
		return ruleSvc.getSortListByQuery(bean.getPageNum(), bean.getPageSize(), query, "createTime", false);
	}

	@Override
	public List<SimulationRuleInfo> querySimulationRuleInfoList(SimulationRuleSearchBean bean) {
		bean.setPageNum(1);
		bean.setPageSize(10000);
		return this.querySimulationRuleInfoPage(bean).getData();
	}

	@Override
	public Integer deleteSimulationRuleById(Long id) {
		SimulationRuleInfo ruleInfo = ruleSvc.getById(id);
		Assert.notNull(ruleInfo, "模拟规则不存在");
		Assert.isTrue(ruleInfo.getRuleStatus() == 0, "启用状态下不可删除");
		try {
			SimulationRuleAutoTaskSvc.ES_CI_INFO_CACHE.invalidate(id);
		} catch (Exception e) {
			log.error("模拟规则[" + ruleInfo.getRuleName() + "]缓存清除失败！");
		}
		return ruleSvc.deleteById(id);
	}

	private void validSimulationRule(SimulationRuleInfo ruleInfo) {
		String ruleName = ruleInfo.getRuleName();
		Assert.isTrue(ruleName != null, "X_PARAM_NOT_NULL${name:ruleName}");
		if(ruleInfo.getRuleType() == 1) {
			Assert.isTrue(ruleInfo.getSendCycle().endsWith("h") || ruleInfo.getSendCycle().endsWith("m")
					|| ruleInfo.getSendCycle().endsWith("s"), "发送频率格式错误");

			if (ruleInfo.getStartTime() == null) {
				long startTime = new Date().getTime();
				ruleInfo.setStartTime(startTime);
			}

		}else if(ruleInfo.getRuleType() == 2){
			Assert.notNull(ruleInfo.getSendCycle(),"缺少发送周期");
			Assert.isTrue(ruleInfo.getSendCycle().endsWith("d") || ruleInfo.getSendCycle().endsWith("h"), "发送周期格式错误");
		}

		if (ruleInfo.getEndTime() != null && ruleInfo.getStartTime() != null) {
			Assert.isTrue(ruleInfo.getStartTime() <= ruleInfo.getEndTime(), "时间范围异常");
		}
		// 校验重复
		BoolQueryBuilder query = QueryBuilders.boolQuery();
		query.must(QueryBuilders.termQuery("domainId", ruleInfo.getDomainId()));
		query.must(QueryBuilders.termQuery("ruleName.keyword", ruleName));
		if (ruleInfo.getId() != null) {
			query.mustNot(QueryBuilders.termQuery("id", ruleInfo.getId()));
		} else {
			ruleInfo.setRuleStatus(1);
		}
		// 更新规则，重置执行次数
		ruleInfo.setIndex(0);
		List<SimulationRuleInfo> rules = ruleSvc.getListByQuery(query);
		Assert.isTrue(BinaryUtils.isEmpty(rules), "模拟规则[" + ruleName + "]已存在");
	}

}
