package com.uinnova.product.eam.model.cj.vo;


import com.binary.framework.bean.Condition;
import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 方案设计实例 EntityBean、Condition
 * <AUTHOR>
 */
@Data
public class PlanDesignInstanceVO implements EntityBean, Condition {

    /**
     * 主键
     */
    private Long id;

    /**
     * 业务主键
     */
    private Long businessId;

    /**
     * 方案设计名称
     */
    private String name;

    /**
     * 方案设计说明
     */
    private String explain;

    /**
     * 方案设计模板businessId
     */
    private Long templateId;

    /**
     * 方案设计模板名称
     */
    private String templateName;

    /**
     * 版本
     */
    private Integer version;

    /**
     * 是否是最新版本
     */
    private Boolean isCurrentVersion;

    /**
     * <p>状态</p>
     * <p>deleted：已删除</p>
     * <p>published：已发布</p>
     * <p>draft：草稿</p>
     */
    private String status;

    /**
     * 方案类型
     */
    private Long typeId;

    /**
     * 方案类型
     */
    private String typeName;

    /**
     * 主办系统id集合
     */
    private List<Map<String, Object>> systemList;

    /**
     * 创建人 账号
     */
    private String creatorCode;

    /**
     * 创建人 姓名
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 修改者 工号
     */
    private String modifierCode;

    /**
     * 修改者 姓名
     */
    private String modifierName;

    /**
     * 修改时间
     */
    private Long modifyTime;

    /**
     * 类型
     * plan：方案
     * diagram：视图
     */
    private String type;

    /**
     * 方案所属文件夹
     */
    private Long dirId;

    /**
     * 是否在流程审批中
     */
    private boolean processApproval;

    /**
     * true: 可编辑 false:不可编辑
     */
    private boolean edit;

    /**
     * 流程实例id
     */
    private String processInstanceId;

    /**
     * 任务定义主键
     */
    private String taskDefinitionKey;

    /**
     * 默认系统的ciCode
     */
    private String defaultSystemCiCode;

    /**
     * 默认系统的classId
     */
    private Long defaultSystemClassId;

    /**
     * 发布位置
     */
    private Long assetsDirId;

    /**
     * 发布位置路径
     */
    private String echoDirName;

    private Long oldDirId;

    /**
     * 是否可以操作批注
     */
    private Boolean handleAnnotation;

    /** 权限 */
    private Integer permission;

    @Comment("选择发布位置类型 方案模板配置了发布位置和类型")
    private Integer assetsDirType;

    /**
     * 资产类型：1:设计库 2:资产库
     */
    private Integer assetsType;

    /**
     * 区分方案所属领域
     */
    private Integer dirType;

    /**
     * 主办系统id集合
     */
    private List<String> ciCodeList;

    /**
     * 任务id
     */
    private String taskId;

    @Comment("分类目录id")
    private Long domainDirId;

    @Comment("流程发起人")
    private String submitter;
}
