package com.uinnova.product.eam.model.constants;

public class NoticeConstant {

    private NoticeConstant() {
    }

    /**
     * 消息
     */
    public static final String NOTICE_TYPE_MSG = "message";
    /**
     * 公告
     */
    public static final String NOTICE_TYPE_NOTICE = "notice";
    /**
     * 消息分类-系统通知
     */
    public static final String NOTICE_CLASSIFY_SYSTEM = "system";
    /**
     * 消息分类-工作流
     */
    public static final String NOTICE_CLASSIFY_WORKFLOW = "workflow";
    /**
     * 方案视图更新内容模板-设计空间
     */
    public static final String PLAN_DIAGRAM_UPDATE_PRIVATE_TEMPLATE = "您创建的方案 %s 中引用的视图已更新，请进入方案更新视图";
    /**
     * 方案视图更新内容模板-资产空间
     */
    public static final String PLAN_DIAGRAM_UPDATE_DESIGN_TEMPLATE = "您发布的方案 %s 中引用的视图已更新，请进入方案查看视图";
    /**
     * 方案视图更新内容模板
     */
    public static final String PLAN_NEWV_VERSION_CONTENT_TEMPLATE = "您创建的方案%s发布了新版本 %s";
    public static final String PLAN_NEWV_VERSION_CONTENT_TEMPLATE_KEYWORD = "查看最新版本";
    /**
     * 审批内容模板
     */
    public static final String WORKFLOW_CONTENT_TEMPLATE = "您%s的%s %s 已%s";
    /**
     * 与我协作内容模板
     */
    public static final String SHARE_CONTENT_TEMPLATE = "用户%s邀请您协作%s 请及时查看";
    /**
     * 交付物模板更新内容模板
     */
    public static final String DEV_TEMPLATE_UPDATE_CONTENT_TEMPLATE = "您%s的方案%s 的交付物模板已更新";
    /**
     * 方案章节分享协作
     */
    public static final String PLAN_CHAPTER_SHARE_COLLABORATE="用户%s邀请您编写方案，%s的%s，请及时查看";
    /**
     * 方案章节完成协作
     */
    public static final String PLAN_CHAPTER_COMPLETE_COLLABORATE="用户%s已完成方案，%s的%s的编写，请及时查看";
    /**
     * 视图/方案协作者提交审批，给创建者推送消息
     */
    public static final String SHARE_PUBLISH_ASSERT = "您创建的%s%s已由%s提交审批";

    public static final String CI_RLT_DEL = "您发布的制品%s，其中部分引用资产及关系被%s删除，请您及时更新并重新发布制品";
    /**
     * 标签
     */
    public static final String TAG_PLAN = "plan";
    public static final String TAG_DIAGRAM = "diagram";
    public static final String TAG_MODEL = "model";
    public static final String TAG_MATRIX = "matrix";
    /**
     * 资产类型(0私有库，1资产库)
     */
    public static final String SOURCE_TYPE = "sourceType";
    public static final String PLAN_ID = "planId";
    public static final String CHAPTER_ID = "chapterId";
    public static final String DIAGRAM_ID = "diagramId";
    public static final String MATRIX_ID = "matrixId";
    public static final String DIR_ID = "dirId";
    public static final String VIEW_TYPE = "viewType";
    public static final String USERNAME_KEYWORDS = "userName.keyword";
    public static final String VERSION = "version";
    /**
     * 消息类型
     */
    public static final String MSG_TYPE_PLAN_DIAGRAM_UPDATE = "planDiagramUpdate";
    public static final String MSG_TYPE_WORKFLOW = "workflow";
    public static final String MSG_TYPE_PLAN_NEWVERSION = "planNewVersion";
    public static final String MSG_TYPE_SHARE = "share";
    public static final String MSG_TYPE_DEVL_TMPLATE = "devlTmplateUpdate";
    public static final String MSG_TYPE_COMPLETE_COLLABORATE = "completeCollaborate";
    public static final String MSG_TYPE_SHARE_COLLABORATE = "shareCollaborate";
    public static final String MSG_TYPE_SHARE_PUBLISH_ASSERT = "sharePublishAssert";
    public static final String MSG_TYPE_CI_RLT_DEL = "ciRltDel";
    public static final String PLAN_DELETE_MSG = "方案已被删除";
}
