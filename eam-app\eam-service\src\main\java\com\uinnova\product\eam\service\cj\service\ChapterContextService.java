package com.uinnova.product.eam.service.cj.service;


import com.uinnova.product.eam.model.cj.domain.ChapterContext;
import com.uinnova.product.eam.model.cj.domain.ChapterInstance;
import com.uinnova.product.eam.model.cj.request.ChapterContextRequest;
import com.uinnova.product.eam.model.cj.request.DeleteModulesRequest;
import com.uinnova.product.eam.model.cj.request.UpdateVersionRequest;
import com.uinnova.product.eam.model.cj.vo.ChapterContextVO;

import java.util.List;
import java.util.Map;

/**
 * 方案章节上下文service接口
 *
 * <AUTHOR>
 */
public interface ChapterContextService {

    /**
     * 保存
     *
     * @param request {@link ChapterContextRequest}
     * @return contextId
     */
    Long save(ChapterContextRequest request);

    /**
     * 根据id查询
     *
     * @param chapterId 章节id
     * @return {@link ChapterContextVO}
     */
    ChapterContextVO findById(Long chapterId);

    /**
     * 模块删除
     *
     * @param request {@link DeleteModulesRequest}
     */
    void delModule(DeleteModulesRequest request);

    ChapterContext findIntroduceContextById(Long introduceChapterId);

    Boolean updateAllContextDiagramVersion(Long planId);

    Boolean updateSingleDiagramVersion(UpdateVersionRequest updateVersionRequest);


    /**
     * 根据方案id查询
     * @param planId 方案id
     * @return 章节详情集合
     */
    List<ChapterContextVO> findByPlanId(Long planId, String processInstanceId);

    /**
     * 获取所有章节上一章节及下一章节
     * @param chapterList 全部章节
     * @return 章节id->上下章节信息
     */
    Map<Long, ChapterContextVO> getChapterPointMap(List<ChapterInstance> chapterList);

    /**
     * 根据方案模板信息更新方案表格数据列宽信息
     * 将模板中的表格列宽信息更新到方案中
     *
     * @param oldContext 当前章节上下文
     */
    void updateTableColumnWidthValue(ChapterContext oldContext);
}
