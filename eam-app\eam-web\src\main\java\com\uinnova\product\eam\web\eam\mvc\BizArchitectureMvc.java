package com.uinnova.product.eam.web.eam.mvc;

import com.binary.framework.util.ControllerUtils;
import com.uinnova.product.eam.service.IBizArchitectureSvc;
import com.uinnova.product.eam.model.AppStrategyDto;
import com.uinnova.product.eam.model.BizNodeDto;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uino.bean.cmdb.base.LibType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 业务架构相关服务的MVC
 *
 * <AUTHOR>
 * @version 2020-8-19
 */
@Controller
@RequestMapping("/eam/biz")
public class BizArchitectureMvc {

    @Autowired
    IBizArchitectureSvc bizArchitectureSvc;

    @ModDesc(desc = "获取应用系统列表策略信息", pDesc = "对象库", pType = String.class, rDesc = "应用策略信息", rType = AppStrategyDto.class)
    @RequestMapping("/getBusinessTree")
    public void getBusinessTree(@RequestParam(defaultValue = "BASELINE") LibType libType,
                            HttpServletRequest request, HttpServletResponse response) {
        List<BizNodeDto> result = bizArchitectureSvc.getBusinessTree(libType);
        ControllerUtils.returnJson(request, response, result);
    }

}
