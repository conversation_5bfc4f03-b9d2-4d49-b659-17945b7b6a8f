package com.uinnova.product.eam.service.cj.dao;

import com.uinnova.product.eam.model.cj.domain.PlanArtifact;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;

/**
 * @description: 方案制品
 * @author: Lc
 * @create: 2022-06-14 15:13
 */
@Component
public class PlanArtifactDao extends AbstractESBaseDao<PlanArtifact, PlanArtifact> {

    @Override
    public String getIndex() {
        return "uino_cj_plan_artifact";
    }

    @Override
    public String getType() {
        return getIndex();
    }

    @PostConstruct
    public void init() {
        initIndex();
    }

}
