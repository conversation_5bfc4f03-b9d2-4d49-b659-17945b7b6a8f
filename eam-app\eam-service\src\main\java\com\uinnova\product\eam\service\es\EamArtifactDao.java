package com.uinnova.product.eam.service.es;

import com.uinnova.product.eam.comm.model.es.EamArtifact;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;

@Service
public class EamArtifactDao extends AbstractESBaseDao<EamArtifact, EamArtifact> {

    @Override
    public String getIndex() {
        return "uino_eam_artifact";
    }

    @Override
    public String getType() {
        return getIndex();
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }


}
