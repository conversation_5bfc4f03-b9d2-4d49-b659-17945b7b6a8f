package com.uinnova.product.eam.comm.model;




import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;


@Comment("主题字段表[VC_THEME_FIELD]")
public class CVcThemeField implements Condition {
	private static final long serialVersionUID = 1L;


	@Comment("ID[ID] operate-Equal[=]")
	private Long id;


	@Comment("ID[ID] operate-In[in]")
	private Long[] ids;


	@Comment("ID[ID] operate-GTEqual[>=]")
	private Long startId;

	@Comment("ID[ID] operate-LTEqual[<=]")
	private Long endId;


	@Comment("所属主题[THEME_ID] operate-Equal[=]")
	private Long themeId;


	@Comment("所属主题[THEME_ID] operate-In[in]")
	private Long[] themeIds;


	@Comment("所属主题[THEME_ID] operate-GTEqual[>=]")
	private Long startThemeId;

	@Comment("所属主题[THEME_ID] operate-LTEqual[<=]")
	private Long endThemeId;


	@Comment("所属分类[CLASS_ID] operate-Equal[=]")
	private Long classId;


	@Comment("所属分类[CLASS_ID] operate-In[in]")
	private Long[] classIds;


	@Comment("所属分类[CLASS_ID] operate-GTEqual[>=]")
	private Long startClassId;

	@Comment("所属分类[CLASS_ID] operate-LTEqual[<=]")
	private Long endClassId;


	@Comment("字段类型[FIELD_TYPE] operate-Equal[=]    字段类型:1=分类属性 2=KPI")
	private Integer fieldType;


	@Comment("字段类型[FIELD_TYPE] operate-In[in]    字段类型:1=分类属性 2=KPI")
	private Integer[] fieldTypes;


	@Comment("字段类型[FIELD_TYPE] operate-GTEqual[>=]    字段类型:1=分类属性 2=KPI")
	private Integer startFieldType;

	@Comment("字段类型[FIELD_TYPE] operate-LTEqual[<=]    字段类型:1=分类属性 2=KPI")
	private Integer endFieldType;


	@Comment("字段ID[FIELD_ID] operate-Equal[=]")
	private Long fieldId;


	@Comment("字段ID[FIELD_ID] operate-In[in]")
	private Long[] fieldIds;


	@Comment("字段ID[FIELD_ID] operate-GTEqual[>=]")
	private Long startFieldId;

	@Comment("字段ID[FIELD_ID] operate-LTEqual[<=]")
	private Long endFieldId;


	@Comment("字段名称[FIELD_NAME] operate-Like[like]")
	private String fieldName;


	@Comment("字段名称[FIELD_NAME] operate-Equal[=]")
	private String fieldNameEqual;


	@Comment("字段名称[FIELD_NAME] operate-In[in]")
	private String[] fieldNames;


	@Comment("字段描述[FIELD_DESC] operate-Like[like]")
	private String fieldDesc;


	@Comment("查看时是否显示[IS_SHOW] operate-Equal[=]    查看时是否显示0=否 1=是")
	private Integer isShow;


	@Comment("查看时是否显示[IS_SHOW] operate-In[in]    查看时是否显示0=否 1=是")
	private Integer[] isShows;


	@Comment("查看时是否显示[IS_SHOW] operate-GTEqual[>=]    查看时是否显示0=否 1=是")
	private Integer startIsShow;

	@Comment("查看时是否显示[IS_SHOW] operate-LTEqual[<=]    查看时是否显示0=否 1=是")
	private Integer endIsShow;


	@Comment("显示单位[SHOW_UNIT] operate-Like[like]    显示单位（纯大写）")
	private String showUnit;


	@Comment("显示单位[SHOW_UNIT] operate-Equal[=]    显示单位（纯大写）")
	private String showUnitEqual;


	@Comment("显示单位[SHOW_UNIT] operate-In[in]    显示单位（纯大写）")
	private String[] showUnits;


	@Comment("所属域[DOMAIN_ID] operate-Equal[=]")
	private Long domainId;


	@Comment("所属域[DOMAIN_ID] operate-In[in]")
	private Long[] domainIds;


	@Comment("所属域[DOMAIN_ID] operate-GTEqual[>=]")
	private Long startDomainId;

	@Comment("所属域[DOMAIN_ID] operate-LTEqual[<=]")
	private Long endDomainId;


	@Comment("数据状态[DATA_STATUS] operate-Equal[=]    数据状态:数据状态：1-正常 0-删除")
	private Integer dataStatus;


	@Comment("数据状态[DATA_STATUS] operate-In[in]    数据状态:数据状态：1-正常 0-删除")
	private Integer[] dataStatuss;


	@Comment("数据状态[DATA_STATUS] operate-GTEqual[>=]    数据状态:数据状态：1-正常 0-删除")
	private Integer startDataStatus;

	@Comment("数据状态[DATA_STATUS] operate-LTEqual[<=]    数据状态:数据状态：1-正常 0-删除")
	private Integer endDataStatus;


	@Comment("创建人[CREATOR] operate-Like[like]")
	private String creator;


	@Comment("创建人[CREATOR] operate-Equal[=]")
	private String creatorEqual;


	@Comment("创建人[CREATOR] operate-In[in]")
	private String[] creators;


	@Comment("修改人[MODIFIER] operate-Like[like]")
	private String modifier;


	@Comment("修改人[MODIFIER] operate-Equal[=]")
	private String modifierEqual;


	@Comment("修改人[MODIFIER] operate-In[in]")
	private String[] modifiers;


	@Comment("创建时间[CREATE_TIME] operate-Equal[=]    创建时间:yyyyMMddHHmmss")
	private Long createTime;


	@Comment("创建时间[CREATE_TIME] operate-In[in]    创建时间:yyyyMMddHHmmss")
	private Long[] createTimes;


	@Comment("创建时间[CREATE_TIME] operate-GTEqual[>=]    创建时间:yyyyMMddHHmmss")
	private Long startCreateTime;

	@Comment("创建时间[CREATE_TIME] operate-LTEqual[<=]    创建时间:yyyyMMddHHmmss")
	private Long endCreateTime;


	@Comment("修改时间[MODIFY_TIME] operate-Equal[=]    修改时间:yyyyMMddHHmmss")
	private Long modifyTime;


	@Comment("修改时间[MODIFY_TIME] operate-In[in]    修改时间:yyyyMMddHHmmss")
	private Long[] modifyTimes;


	@Comment("修改时间[MODIFY_TIME] operate-GTEqual[>=]    修改时间:yyyyMMddHHmmss")
	private Long startModifyTime;

	@Comment("修改时间[MODIFY_TIME] operate-LTEqual[<=]    修改时间:yyyyMMddHHmmss")
	private Long endModifyTime;




	public Long getId() {
		return this.id;
	}
	public void setId(Long id) {
		this.id = id;
	}


	public Long[] getIds() {
		return this.ids;
	}
	public void setIds(Long[] ids) {
		this.ids = ids;
	}


	public Long getStartId() {
		return this.startId;
	}
	public void setStartId(Long startId) {
		this.startId = startId;
	}


	public Long getEndId() {
		return this.endId;
	}
	public void setEndId(Long endId) {
		this.endId = endId;
	}


	public Long getThemeId() {
		return this.themeId;
	}
	public void setThemeId(Long themeId) {
		this.themeId = themeId;
	}


	public Long[] getThemeIds() {
		return this.themeIds;
	}
	public void setThemeIds(Long[] themeIds) {
		this.themeIds = themeIds;
	}


	public Long getStartThemeId() {
		return this.startThemeId;
	}
	public void setStartThemeId(Long startThemeId) {
		this.startThemeId = startThemeId;
	}


	public Long getEndThemeId() {
		return this.endThemeId;
	}
	public void setEndThemeId(Long endThemeId) {
		this.endThemeId = endThemeId;
	}


	public Long getClassId() {
		return this.classId;
	}
	public void setClassId(Long classId) {
		this.classId = classId;
	}


	public Long[] getClassIds() {
		return this.classIds;
	}
	public void setClassIds(Long[] classIds) {
		this.classIds = classIds;
	}


	public Long getStartClassId() {
		return this.startClassId;
	}
	public void setStartClassId(Long startClassId) {
		this.startClassId = startClassId;
	}


	public Long getEndClassId() {
		return this.endClassId;
	}
	public void setEndClassId(Long endClassId) {
		this.endClassId = endClassId;
	}


	public Integer getFieldType() {
		return this.fieldType;
	}
	public void setFieldType(Integer fieldType) {
		this.fieldType = fieldType;
	}


	public Integer[] getFieldTypes() {
		return this.fieldTypes;
	}
	public void setFieldTypes(Integer[] fieldTypes) {
		this.fieldTypes = fieldTypes;
	}


	public Integer getStartFieldType() {
		return this.startFieldType;
	}
	public void setStartFieldType(Integer startFieldType) {
		this.startFieldType = startFieldType;
	}


	public Integer getEndFieldType() {
		return this.endFieldType;
	}
	public void setEndFieldType(Integer endFieldType) {
		this.endFieldType = endFieldType;
	}


	public Long getFieldId() {
		return this.fieldId;
	}
	public void setFieldId(Long fieldId) {
		this.fieldId = fieldId;
	}


	public Long[] getFieldIds() {
		return this.fieldIds;
	}
	public void setFieldIds(Long[] fieldIds) {
		this.fieldIds = fieldIds;
	}


	public Long getStartFieldId() {
		return this.startFieldId;
	}
	public void setStartFieldId(Long startFieldId) {
		this.startFieldId = startFieldId;
	}


	public Long getEndFieldId() {
		return this.endFieldId;
	}
	public void setEndFieldId(Long endFieldId) {
		this.endFieldId = endFieldId;
	}


	public String getFieldName() {
		return this.fieldName;
	}
	public void setFieldName(String fieldName) {
		this.fieldName = fieldName;
	}


	public String getFieldNameEqual() {
		return this.fieldNameEqual;
	}
	public void setFieldNameEqual(String fieldNameEqual) {
		this.fieldNameEqual = fieldNameEqual;
	}


	public String[] getFieldNames() {
		return this.fieldNames;
	}
	public void setFieldNames(String[] fieldNames) {
		this.fieldNames = fieldNames;
	}


	public String getFieldDesc() {
		return this.fieldDesc;
	}
	public void setFieldDesc(String fieldDesc) {
		this.fieldDesc = fieldDesc;
	}


	public Integer getIsShow() {
		return this.isShow;
	}
	public void setIsShow(Integer isShow) {
		this.isShow = isShow;
	}


	public Integer[] getIsShows() {
		return this.isShows;
	}
	public void setIsShows(Integer[] isShows) {
		this.isShows = isShows;
	}


	public Integer getStartIsShow() {
		return this.startIsShow;
	}
	public void setStartIsShow(Integer startIsShow) {
		this.startIsShow = startIsShow;
	}


	public Integer getEndIsShow() {
		return this.endIsShow;
	}
	public void setEndIsShow(Integer endIsShow) {
		this.endIsShow = endIsShow;
	}


	public String getShowUnit() {
		return this.showUnit;
	}
	public void setShowUnit(String showUnit) {
		this.showUnit = showUnit;
	}


	public String getShowUnitEqual() {
		return this.showUnitEqual;
	}
	public void setShowUnitEqual(String showUnitEqual) {
		this.showUnitEqual = showUnitEqual;
	}


	public String[] getShowUnits() {
		return this.showUnits;
	}
	public void setShowUnits(String[] showUnits) {
		this.showUnits = showUnits;
	}


	public Long getDomainId() {
		return this.domainId;
	}
	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}


	public Long[] getDomainIds() {
		return this.domainIds;
	}
	public void setDomainIds(Long[] domainIds) {
		this.domainIds = domainIds;
	}


	public Long getStartDomainId() {
		return this.startDomainId;
	}
	public void setStartDomainId(Long startDomainId) {
		this.startDomainId = startDomainId;
	}


	public Long getEndDomainId() {
		return this.endDomainId;
	}
	public void setEndDomainId(Long endDomainId) {
		this.endDomainId = endDomainId;
	}


	public Integer getDataStatus() {
		return this.dataStatus;
	}
	public void setDataStatus(Integer dataStatus) {
		this.dataStatus = dataStatus;
	}


	public Integer[] getDataStatuss() {
		return this.dataStatuss;
	}
	public void setDataStatuss(Integer[] dataStatuss) {
		this.dataStatuss = dataStatuss;
	}


	public Integer getStartDataStatus() {
		return this.startDataStatus;
	}
	public void setStartDataStatus(Integer startDataStatus) {
		this.startDataStatus = startDataStatus;
	}


	public Integer getEndDataStatus() {
		return this.endDataStatus;
	}
	public void setEndDataStatus(Integer endDataStatus) {
		this.endDataStatus = endDataStatus;
	}


	public String getCreator() {
		return this.creator;
	}
	public void setCreator(String creator) {
		this.creator = creator;
	}


	public String getCreatorEqual() {
		return this.creatorEqual;
	}
	public void setCreatorEqual(String creatorEqual) {
		this.creatorEqual = creatorEqual;
	}


	public String[] getCreators() {
		return this.creators;
	}
	public void setCreators(String[] creators) {
		this.creators = creators;
	}


	public String getModifier() {
		return this.modifier;
	}
	public void setModifier(String modifier) {
		this.modifier = modifier;
	}


	public String getModifierEqual() {
		return this.modifierEqual;
	}
	public void setModifierEqual(String modifierEqual) {
		this.modifierEqual = modifierEqual;
	}


	public String[] getModifiers() {
		return this.modifiers;
	}
	public void setModifiers(String[] modifiers) {
		this.modifiers = modifiers;
	}


	public Long getCreateTime() {
		return this.createTime;
	}
	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}


	public Long[] getCreateTimes() {
		return this.createTimes;
	}
	public void setCreateTimes(Long[] createTimes) {
		this.createTimes = createTimes;
	}


	public Long getStartCreateTime() {
		return this.startCreateTime;
	}
	public void setStartCreateTime(Long startCreateTime) {
		this.startCreateTime = startCreateTime;
	}


	public Long getEndCreateTime() {
		return this.endCreateTime;
	}
	public void setEndCreateTime(Long endCreateTime) {
		this.endCreateTime = endCreateTime;
	}


	public Long getModifyTime() {
		return this.modifyTime;
	}
	public void setModifyTime(Long modifyTime) {
		this.modifyTime = modifyTime;
	}


	public Long[] getModifyTimes() {
		return this.modifyTimes;
	}
	public void setModifyTimes(Long[] modifyTimes) {
		this.modifyTimes = modifyTimes;
	}


	public Long getStartModifyTime() {
		return this.startModifyTime;
	}
	public void setStartModifyTime(Long startModifyTime) {
		this.startModifyTime = startModifyTime;
	}


	public Long getEndModifyTime() {
		return this.endModifyTime;
	}
	public void setEndModifyTime(Long endModifyTime) {
		this.endModifyTime = endModifyTime;
	}


}


