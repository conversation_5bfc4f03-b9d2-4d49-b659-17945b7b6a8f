package com.uinnova.product.eam.service.cj.dao;

import com.uinnova.product.eam.model.cj.domain.ChapterContext;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;


/**
 * 方案章节内容Dao
 * <AUTHOR>
 */
@Component
public class ChapterContextDao extends AbstractESBaseDao<ChapterContext, ChapterContext> {
    @Override
    public String getIndex() {
        return "uino_cj_plan_chapter_context";
    }

    @Override
    public String getType() {
        return getIndex();
    }

    @PostConstruct
    public void init() {
        initIndex();
    }
}
