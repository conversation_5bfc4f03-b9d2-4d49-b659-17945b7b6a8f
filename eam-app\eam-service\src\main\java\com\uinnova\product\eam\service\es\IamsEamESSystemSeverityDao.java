package com.uinnova.product.eam.service.es;

import com.uinnova.product.eam.comm.model.es.CEamSystemSeverity;
import com.uinnova.product.eam.comm.model.es.EamSystemSeverity;
import com.uino.dao.AbstractESBaseDao;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;

/**
 * EAM 子系统级别
 * <AUTHOR>
 */
@Service
public class IamsEamESSystemSeverityDao extends AbstractESBaseDao<EamSystemSeverity, CEamSystemSeverity> {

    Log logger = LogFactory.getLog(getClass());

    @PostConstruct
    public void init(){
        super.initIndex();
    }

    @Override
    public String getIndex() {
        return "uino_eam_system_severity";
    }

    @Override
    public String getType() {
        return getIndex();
    }

}
