package com.uino.service.simulation;

import com.binary.jdbc.Page;
import com.uino.bean.monitor.base.ESAlarm;
import com.uino.bean.monitor.buiness.AlarmInfo;
import com.uino.bean.monitor.buiness.AlarmQueryDto;
import com.uino.bean.monitor.buiness.SimulationAlarmBean;

/**
 * 告警服务类
 * 
 * <AUTHOR>
 *
 */
public interface IAlarmSvc {

    /**
     * 分页查询告警信息
     * 
     * @param queryDto
     * @return
     */
    public Page<AlarmInfo> searchAlarms(AlarmQueryDto queryDto);

    /**
     * 保存告警
     * 
     * @param saveDto
     */
    public void saveAlarm(ESAlarm saveDto);

    /**
     * 根据条件count 告警
     * 
     * @param queryDto
     * @return
     */
    public Long countByQuery(AlarmQueryDto queryDto);

	/**
	 * 查询打开的告警信息
	 * 
	 * @param metric
	 * @param classId
	 * @param objId
	 * @return
	 */
    ESAlarm queryOpenAlarm(String metric, Long classId, Long objId);

	/**
	 * 批量模拟告警信息
	 * 
	 * @param bean
	 */
	void simulationAlarms(SimulationAlarmBean bean);
}
