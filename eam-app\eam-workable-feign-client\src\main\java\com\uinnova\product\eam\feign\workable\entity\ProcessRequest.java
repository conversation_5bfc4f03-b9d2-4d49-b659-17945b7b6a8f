package com.uinnova.product.eam.feign.workable.entity;

import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 流程请求实体类
 *
 * <AUTHOR>
 * @since 2022/2/24 14:19
 */
@Data
public class ProcessRequest {

    /**
     * 流程定义key
     */
    private String processDefinitionKey;

    /**
     * 流程实例id
     */
    private String processInstanceId;

    /**
     * 流程实例名称
     */
    private String processInstanceName;

    /**
     * 业务id
     */
    private String businessKey;

    /**
     * 下一个审批人id
     */
    private String userId;

    /**
     * 发起人id
     */
    private String owner;

    /**
     * 流程路由使用到的数据
     */
    private Map<String, Object> routerVariables = new HashMap<>();

    /**
     * 删除原因
     */
    private String deleteReason;

    /**
     * 子流程用得到数据
     */
    private List<ChildProcessRequest> childProcessRequestList;

}
