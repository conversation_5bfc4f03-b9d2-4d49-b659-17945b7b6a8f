package com.uinnova.product.eam.model.bm;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.util.List;

/**
 * 数据更新信息
 * <AUTHOR>
 */
@Data
public class EamDataUpdateInfo {
    @Comment("ciCode")
    private String code;
    @Comment("架构/关系分类名称")
    private String className;
    @Comment("对象or关系")
    private Boolean ciFlag = true;
    @Comment("实例名称")
    private List<String> ciName;
    @Comment("新增")
    private Boolean add = true;
    @Comment("修改")
    private Boolean update = false;
    @Comment("删除")
    private Boolean delete = false;

    public void setAdd(Boolean add) {
        this.add = add;
        if(add){
            this.update = false;
            this.delete = false;
        }
    }

    public void setUpdate(Boolean update) {
        this.update = update;
        if(update){
            this.add = false;
            this.delete = false;
        }
    }

    public void setDelete(Boolean delete) {
        this.delete = delete;
        if(delete){
            this.add = false;
            this.update = false;
        }
    }

    public EamDataUpdateInfo(String code, String className, Boolean ciFlag, List<String> ciName) {
        this.code = code;
        this.className = className;
        this.ciFlag = ciFlag;
        this.ciName = ciName;
    }

    public EamDataUpdateInfo(String code, String className, Boolean ciFlag, List<String> ciName, Boolean add) {
        this.code = code;
        this.className = className;
        this.ciFlag = ciFlag;
        this.ciName = ciName;
        this.add = add;
    }
}
