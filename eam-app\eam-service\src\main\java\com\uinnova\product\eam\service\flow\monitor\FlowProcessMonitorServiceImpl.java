package com.uinnova.product.eam.service.flow.monitor;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.binary.core.lang.StringUtils;
import com.binary.jdbc.Page;
import com.uinnova.product.eam.comm.model.es.FlowSystemOperationData;
import com.uinnova.product.eam.comm.model.es.IndicatorDetectionInformationAssociation;
import com.uinnova.product.eam.model.FlowProcessSystemTreeDto;
import com.uinnova.product.eam.model.enums.FlowSystemType;
import com.uinnova.product.eam.service.es.FlowSystemOperationDao;
import com.uinnova.product.eam.service.exception.BusinessException;
import com.uinnova.product.eam.service.flow.base.AbstractFlowProcessSystemService;
import com.uinnova.product.eam.service.flow.basic.FlowProcessBasicServiceImpl;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClass;
import com.uinnova.product.vmdb.comm.model.ci.CcCiClass;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.base.LibType;
import com.uino.bean.cmdb.query.ESCISearchBean;
import com.uino.dao.util.ESUtil;
import com.uino.util.sys.SysUtil;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 流程监控统计服务
 * 负责指标监控、统计分析等功能
 */
@Service
@Slf4j
public class FlowProcessMonitorServiceImpl extends AbstractFlowProcessSystemService {


    private final static String PROCESS_MAPPING = "PROCESS_MAPPING_RELATIONSHIP";
    @Resource
    FlowProcessBasicServiceImpl flowProcessBasicService;

    @Resource
    protected FlowSystemOperationDao workFlowOperationDao;

    /**
     * 添加指标监测信息
     */
    public Long addMetricMonitoringInformation(List<IndicatorDetectionInformationAssociation> dto) {
        // 判断dto中的ciCode是否相等
        if (dto.stream().map(IndicatorDetectionInformationAssociation::getCiCode).distinct().count() > 1) {
            throw new BusinessException("检测信息关联的ciCode必须一致");
        }
        String loginCode = SysUtil.getCurrentUserInfo().getLoginCode();
        for (IndicatorDetectionInformationAssociation association : dto) {
            if (association.getId() == null) {
                // 新增数据，设置ID和创建人
                association.setId(ESUtil.getUUID());
                association.setCreator(loginCode);
            }
        }
        informationAssociationDao.saveOrUpdateBatch(dto);
        return 1L;
    }

    /**
     * 查询指标监测信息
     */
    public Map<String, Object> queryIndicatorMonitoringInformation(String ciCode) {
        Map<String, Object> map = new HashMap<>();
        String loginCode = SysUtil.getCurrentUserInfo().getLoginCode();
        // 查询CI信息
        CcCiInfo ciByCode = ciSwitchSvc.getCiByCode(ciCode, loginCode, LibType.PRIVATE);
        if (ciByCode == null) {
            throw new BusinessException("未找到数据");
        }
        // 构建查询条件：同时匹配ciCode和创建人
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery()
                .filter(QueryBuilders.termQuery("ciCode.keyword", ciCode))
                .filter(QueryBuilders.termQuery("creator.keyword", loginCode));
        // 查询监控信息
        List<IndicatorDetectionInformationAssociation> listByQuery = informationAssociationDao.getListByQuery(queryBuilder);
        Map<String, Object> detectionInfo = new HashMap<>();
        String[] keys = new String[]{"指标状态", "监测日期", "监测值"};
        detectionInfo.put("attrs", keys);
        List<Map> detectionList = new ArrayList<>();
        if (listByQuery != null) {
            // 获取目标值，可能为空
            String targetValueStr = ciByCode.getAttrs().get("目标值");
            String[] targetValues = null;
            if (targetValueStr != null && !StringUtils.isEmpty(targetValueStr)) {
                // 转换HTML实体
                targetValues = targetValueStr.split(",");
                for (int i = 0; i < targetValues.length; i++) {
                    targetValues[i] = targetValues[i]
                            .replace("&gt;", ">")
                            .replace("&lt;", "<")
                            .replace("&ge;", "≥")
                            .replace("&le;", "≤");
                }
            }
            for (IndicatorDetectionInformationAssociation detection : listByQuery) {
                Map<String, Object> detectionData = new LinkedHashMap<>();
                detectionData.put("id", detection.getId());
                // 判断监测状态
                if (targetValues == null) {
                    detectionData.put("指标状态", "");
                } else {
                    detectionData.put("指标状态", checkMetricStatus(detection.getDetectionValue(), targetValues));
                }
                detectionData.put("监测日期", detection.getDetectionDate());
                detectionData.put("监测值", detection.getDetectionValue());

                detectionList.add(detectionData);
            }
            // 按照检测日期升序排序
            Collections.sort(detectionList, (o1, o2) -> {
                Long date1 = (Long) o1.get("监测日期");
                Long date2 = (Long) o2.get("监测日期");
                return date1.compareTo(date2);
            });
        }
        detectionInfo.put("dataList", detectionList);
        map.put("检测信息", detectionInfo);
        map.put("指标信息", ciByCode);
        return map;
    }

    /**
     * 查询流程数量
     */
    public Map<String, Integer> queryProcessQuantity() {
        Map<String, Integer> result = new HashMap<>();
        result.put("流程", 0);
        result.put("流程组", 0);
        result.put("未发布", 0);
        result.put("审批中", 0);
        result.put("待发布", 0);
        result.put("已发布", 0);
        result.put("升版中", 0);

        Collection<FlowProcessSystemTreeDto> flowSystemTree = flowProcessBasicService.getFlowSystemTreeNew(false, true);
        if (flowSystemTree != null && !flowSystemTree.isEmpty()) {
            countProcessQuantity(flowSystemTree, result);
        }
        return result;
    }

    /**
     * 获取所有运行情况的流程
     */
    public List<FlowProcessSystemTreeDto> getAllFlowWhereRunSituation(String sourceId) {
        // 查询ci分类
        CCcCiClass cCcCiClass = new CCcCiClass();
        cCcCiClass.setClassCodes(new String[]{FlowSystemType.SUB_FLOW.getFlowSystemTypeName()});
        List<CcCiClassInfo> ccCiClassInfos = iciClassSvc.queryClassByCdt(cCcCiClass);
        // 查询ci信息
        CcCiClass ciClass = ccCiClassInfos.get(0).getCiClass();
        ESCISearchBean esciSearchBean = new ESCISearchBean();
        esciSearchBean.setPageNum(0);
        esciSearchBean.setPageSize(3000);
        esciSearchBean.setClassIds(Collections.singletonList(ciClass.getId()));
        Page<ESCIInfo> ccCiInfos1 = ciSwitchSvc.searchESCIByBean(esciSearchBean, LibType.DESIGN);
        List<ESCIInfo> list = ccCiInfos1.getData();
        Map<String, ESCIInfo> collect = list.stream().collect(Collectors.toMap(eSCIInfo -> eSCIInfo.getCiCode(), Function.identity()));
        //查询出所有运行情况的数据
        BoolQueryBuilder query = QueryBuilders.boolQuery();
//	    		.must(QueryBuilders.termQuery("sourceId.keyword", sourceId));
        List<FlowSystemOperationData> FlowSystemOperationList = workFlowOperationDao.getListByQuery(query);
        Map<String, List<FlowSystemOperationData>> FlowSystemOperationMap = FlowSystemOperationList.stream().collect(Collectors.groupingBy(data -> data.getProcessTypeId()));
        List<String> ids = getIds(FlowSystemOperationMap);
        //过滤出有运行情况的ci
        List<ESCIInfo> targetCis = new ArrayList<ESCIInfo>();
        for (String id : ids) {
            if (null != collect.get(id)) {
                targetCis.add(collect.get(id));
            }
        }
        // 封装返回结果
        Map<Long, FlowProcessSystemTreeDto> map = new HashMap<>();
        for (ESCIInfo ccCiInfo : targetCis) {
            FlowProcessSystemTreeDto dto = new FlowProcessSystemTreeDto();
            String responsiblePerson = (String) ccCiInfo.getAttrs().get("责任人");
            String owner = (String) ccCiInfo.getAttrs().get("所有者");
            String writer = (String) ccCiInfo.getAttrs().get("编写人");
            // 过滤责任人，所有者，编写人
            dto.setCiId(ccCiInfo.getId());
            dto.setCiCode(ccCiInfo.getCiCode());
            //新增审批状态和是否可编辑状态
            dto.setUpdateStatus(ccCiInfo.getUpdateStatus());
            dto.setProcessApprovalStatus(ccCiInfo.getProcessApprovalStatus());
            dto.addListMap("责任人", responsiblePerson);
            dto.addListMap("所有者", owner);
            dto.addListMap("编写人", writer);
            if (ccCiInfo.getAttrs().get("流程编码") == null) {
                dto.setFlowCode((String) ccCiInfo.getAttrs().get("编号"));
            } else if (ccCiInfo.getAttrs().get("流程编码") != null) {
                dto.setFlowCode((String) ccCiInfo.getAttrs().get("流程编码"));
                dto.setFlowSystemName((String) ccCiInfo.getAttrs().get("流程名称"));
            }
            dto.setFlowSystemType("流程");
            map.put(dto.getCiId(), dto);
        }
        return new ArrayList<>(map.values());
    }

    /**
     * 检查指标状态
     */
    private String checkMetricStatus(String detectionValue, String[] targetValues) {
        // 如果目标值为空，返回空状态
        if (targetValues == null) {
            return null;
        }
        try {
            double value = Double.parseDouble(detectionValue);
            for (String targetValue : targetValues) {
                // 提取比较符号和数值
                String operator = targetValue.length() > 1 && targetValue.charAt(1) == '=' ?
                        targetValue.substring(0, 2) : targetValue.substring(0, 1);
                double threshold = Double.parseDouble(targetValue.substring(operator.length()));
                boolean isNormal;
                switch (operator) {
                    case ">":
                        isNormal = value > threshold;
                        break;
                    case "<":
                        isNormal = value < threshold;
                        break;
                    case ">=":
                        isNormal = value >= threshold;
                        break;
                    case "<=":
                        isNormal = value <= threshold;
                        break;
                    case "=":
                        isNormal = value == threshold;
                        break;
                    default:
                        isNormal = false;
                }
                if (!isNormal) {
                    return "异常";
                }
            }
            return "正常";
        } catch (NumberFormatException e) {
            return "异常";
        }
    }

    /**
     * 获取目标流程id
     *
     * @param FlowSystemOperationMap
     * @return
     */
    private List<String> getIds(Map<String, List<FlowSystemOperationData>> FlowSystemOperationMap) {
        List<String> idList = new ArrayList<String>();
        String showType = bmConfigSvc.getConfigType(PROCESS_MAPPING);
        if (StringUtils.isBlank(showType)) {
            return new ArrayList<>();
        }
        JSONArray array = JSON.parseArray(showType);
        for (Object o : array) {
            JSONObject obj = (JSONObject) o;
            String sourceId = obj.getString("sourceId");
            String targetId = obj.getString("targetId");
            if (FlowSystemOperationMap.containsKey(targetId)) {
                idList.add(sourceId);
            }
        }
        return idList;
    }

    /**
     * 统计流程数量
     */
    private void countProcessQuantity(Collection<FlowProcessSystemTreeDto> flowSystemTree,
                                      Map<String, Integer> result) {
        if (flowSystemTree == null || flowSystemTree.isEmpty()) {
            return;
        }
        for (FlowProcessSystemTreeDto node : flowSystemTree) {
            if ("流程".equals(node.getFlowSystemType())) {
                result.put("流程", result.get("流程") + 1);
            } else if ("流程组".equals(node.getFlowSystemType())) {
                result.put("流程组", result.get("流程组") + 1);
            }
            if (node.getProcessApprovalStatus() == 0) {
                result.put("未发布", result.get("未发布") + 1);
            } else if (node.getProcessApprovalStatus() == 1) {
                result.put("审批中", result.get("审批中") + 1);
            } else if (node.getProcessApprovalStatus() == 2) {
                result.put("待发布", result.get("待发布") + 1);
            } else if (node.getProcessApprovalStatus() == 3) {
                result.put("已发布", result.get("已发布") + 1);
            } else if (node.getProcessApprovalStatus() == 4) {
                result.put("升版中", result.get("升版中") + 1);
            }
            if (node.getChild() != null && !node.getChild().isEmpty()) {
                countProcessQuantity(node.getChild(), result);
            }
        }
    }
}