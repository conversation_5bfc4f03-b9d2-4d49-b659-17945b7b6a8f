package com.uinnova.product.eam.service.resource;

import com.uinnova.product.eam.base.model.FileResourceMeta;
import com.uinnova.product.eam.base.model.ResourceInfo;
import com.uinnova.product.eam.comm.model.es.EamResource;
import com.uinnova.product.eam.model.vo.DefaultFileVo;
import com.uino.bean.permission.base.SysUser;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface IEamResourceSvc {

    /**
     * 保存或更新资源信息
     * @param resource 资源信息
     * @return id
     */
    Long saveOrUpdate(EamResource resource);

    /**
     * 保存或更新资源信息
     * @param name 资源名称
     * @param operator 操作人
     * @param resPath 资源路径
     * @return 资源id
     */
    Long saveOrUpdate(String name, String operator, String resPath);

    /**
     * 通过名称及id查询
     * @param ids id
     * @param words 名称
     * @return 资源信息
     */
    List<ResourceInfo> selectListByWords(List<Long> ids, List<String> words);

    /**
     * 通过名称查询资源
     * @param name 名称
     * @return 资源
     */
    List<EamResource> getByName(String name);

    /**
     * 文件上传
     *
     * @param files
     * @return
     */
    List<ResourceInfo> upload(MultipartFile[] files, SysUser sysUser);

    /**
     * 文件下载
     *
     * @param resIds
     * @return
     */
    List<FileResourceMeta> download(List<Long> resIds);

    /**
     * 通过业务类型查询资源
     * @param type 业务类型
     * @return 资源信息
     */
    List<FileResourceMeta> queryByType(Integer type);

    /**
     * 上传文件区分业务类型
     * @param files 文件
     * @param type 业务类型
     * @return 资源信息
     */
    List<ResourceInfo> uploadFileByType(MultipartFile[] files, Integer type);

    /**
     * 删除资源
     * @param id 资源id
     * @return 删除结果
     */
    Integer deleteResource(Long id);

    /**
     * 根据类型获取图片
     * @param type 业务类型：1制品，2品牌logo
     * @return 图片集合
     */
    List<DefaultFileVo> getImages(Integer type);
}
