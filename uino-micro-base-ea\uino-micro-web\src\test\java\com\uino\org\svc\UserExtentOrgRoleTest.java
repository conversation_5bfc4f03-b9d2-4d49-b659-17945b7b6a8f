package com.uino.org.svc;

import java.util.Collections;

import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

import com.uino.dao.permission.ESOrgSvc;
import com.uino.dao.permission.ESUserSvc;
import com.uino.service.permission.microservice.IUserSvc;
import com.uino.service.permission.microservice.impl.OrgSvc;
import com.uino.dao.permission.rlt.ESPerssionCommSvc;
import com.uino.bean.permission.base.SysOrg;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.permission.business.request.ExtendOrgRoleRequestDto;

public class UserExtentOrgRoleTest {
	@InjectMocks
	private OrgSvc orgSvc;
	private ESUserSvc esUserSvc;
	private ESOrgSvc esOrgSvc;
	private IUserSvc userSvc;
    private ESPerssionCommSvc perssionCommSvc;

	@Before
	public void before() {
		MockitoAnnotations.initMocks(this);
		userSvc = Mockito.mock(IUserSvc.class);
		esOrgSvc = Mockito.mock(ESOrgSvc.class);
		esUserSvc = Mockito.mock(ESUserSvc.class);
        perssionCommSvc = Mockito.mock(ESPerssionCommSvc.class);
		ReflectionTestUtils.setField(orgSvc, "esOrgSvc", esOrgSvc);
		ReflectionTestUtils.setField(orgSvc, "esUserSvc", esUserSvc);
		ReflectionTestUtils.setField(orgSvc, "userSvc", userSvc);
        ReflectionTestUtils.setField(orgSvc, "perssionCommSvc", perssionCommSvc);
		Mockito.when(esOrgSvc.getById(1L))
				.thenReturn(SysOrg.builder().id(1L).orgName("org").parentOrgId(0L).orgCode("org").build());
		Mockito.when(esUserSvc.getListByQuery(Mockito.any()))
				.thenReturn(Collections.singletonList(SysUser.builder().id(1L).build()));
        Mockito.when(perssionCommSvc.saveUserRoleRltBatch(Mockito.anyList())).thenReturn(1);
		Mockito.when(esOrgSvc.getInOrgRoleIds(Mockito.eq(1L))).thenReturn(Collections.singleton(1L));
	}

	@Test
	public void test01() {
		orgSvc.userExtentOrgRole(
				ExtendOrgRoleRequestDto.builder().orgId(1L).userIds(Collections.singleton(1L)).build());
	}

	@Test(expected = IllegalArgumentException.class)
	public void test02() {
		orgSvc.userExtentOrgRole(
				ExtendOrgRoleRequestDto.builder().orgId(2L).userIds(Collections.singleton(1L)).build());
	}
}
