package com.uino.bean.cmdb.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import java.util.Set;

/**
 * CI历史查询类
 * 
 * <AUTHOR>
 *
 */
@ApiModel(value = "CI历史查询类", description = "CI历史查询类")
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ESCIHistorySearchBean extends ESSearchBase {

    private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "对象id", example = "123")
    private Long ciId;

	@ApiModelProperty(value = "CI所属分类id", example = "123")
    private Long classId;

	@ApiModelProperty(value = "对象的ciCode", example = "abc")
    private String ciCode;

	@ApiModelProperty(value = "版本号", example = "1")
    private Long version;

	@ApiModelProperty(value = "变更操作:0=修改;1=删除", example = "0")
    private Set<Integer> actions;

}
