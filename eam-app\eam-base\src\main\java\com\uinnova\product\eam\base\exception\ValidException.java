package com.uinnova.product.eam.base.exception;

public class ValidException extends EamException {

    public static final long serialVersionUID = 1;

    public ValidException() {
        super();
    }

    public ValidException(String message) {
        super(message);
    }

    public ValidException(String message, Throwable cause) {
        super(message, cause);
    }

    public ValidException(Throwable cause) {
        super(cause);
    }
}
