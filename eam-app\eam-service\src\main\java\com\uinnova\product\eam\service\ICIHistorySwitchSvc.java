package com.uinnova.product.eam.service;

import com.uino.api.client.cmdb.ICIHistoryApiSvc;
import com.uino.bean.cmdb.base.ESCIHistoryInfo;
import com.uino.bean.cmdb.base.LibType;

import java.util.List;

public interface ICIHistorySwitchSvc {

    void delAll(List<Long> classIds, LibType libType);

    abstract ICIHistoryApiSvc getCiHistoryApiSvc();

    abstract ICIHistoryApiSvc getCiHistoryApiSvc(LibType libType);

    List<String> getCIVersionList(String ciCode, Long classId, LibType libType);

    ESCIHistoryInfo getCIInfoHistoryByCIVersion(String ciCode, Long classId, Long version, LibType libType);
}
