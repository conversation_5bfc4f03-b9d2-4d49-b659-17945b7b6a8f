package com.uinnova.product.vmdb.provider.ci.bean;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.vmdb.comm.model.classTpl.CcCiClassRltTpl;

import java.io.Serializable;

@Comment("分类模板中分类与分类关系封装")
public class CiClassRltTplInfo implements Serializable {

	private static final long serialVersionUID = 1L;

	@Comment("分类与分类的关系")
	private CcCiClassRltTpl ciClassRlt;

	@Comment("起始分类信息")
	private CcCiClassTplInfo sourceCiClassInfo;

	@Comment("结束分类信息")
	private CcCiClassTplInfo targetCiClassInfo;

	@Comment("关系分类信息")
	private CcCiClassTplInfo rltClassInfo;

	public CcCiClassRltTpl getCiClassRlt() {
		return ciClassRlt;
	}

	public void setCiClassRlt(CcCiClassRltTpl ciClassRlt) {
		this.ciClassRlt = ciClassRlt;
	}

	public CcCiClassTplInfo getSourceCiClassInfo() {
		return sourceCiClassInfo;
	}

	public void setSourceCiClassInfo(CcCiClassTplInfo sourceCiClassInfo) {
		this.sourceCiClassInfo = sourceCiClassInfo;
	}

	public CcCiClassTplInfo getTargetCiClassInfo() {
		return targetCiClassInfo;
	}

	public void setTargetCiClassInfo(CcCiClassTplInfo targetCiClassInfo) {
		this.targetCiClassInfo = targetCiClassInfo;
	}

	public CcCiClassTplInfo getRltClassInfo() {
		return rltClassInfo;
	}

	public void setRltClassInfo(CcCiClassTplInfo rltClassInfo) {
		this.rltClassInfo = rltClassInfo;
	}


}
