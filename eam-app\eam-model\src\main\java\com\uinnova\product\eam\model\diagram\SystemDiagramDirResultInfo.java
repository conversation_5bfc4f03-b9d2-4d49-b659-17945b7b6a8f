package com.uinnova.product.eam.model.diagram;

import com.binary.framework.bean.annotation.Comment;
import com.binary.jdbc.Page;
import com.uinnova.product.eam.base.diagram.model.ESSimpleDiagramDTO;
import com.uinnova.product.eam.model.vo.VcDiagramDirVo;
import com.uinnova.product.vmdb.provider.ci.bean.CiGroupPage;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
@Comment("查询文件夹和视图信息")
public class SystemDiagramDirResultInfo implements Serializable {
	private static final long serialVersionUID = 1L;

	@Comment("当前文件夹信息")
	private VcDiagramDirVo diagramDir;
	
	@Comment("子文件夹信息")
	private List<VcDiagramDirVo> childrenDirs;
	
	@Comment("子文件夹下是否有节点")
	private Map<Long,Boolean> dirHasNodeMap;
	
	@Comment("子文件夹视图数量信息")
	private Map<Long,Integer> dirDiagramCountMap;

	@Comment("视图信息，不分页时使用")
	private List<ESSimpleDiagramDTO> diagramInfos;
	
	@Comment("视图信息，分页时使用")
	private Page<ESSimpleDiagramDTO> diagramInfoPage;

	@Comment("系统详情")
	private List<CiGroupPage> ciGroupPage;
}
