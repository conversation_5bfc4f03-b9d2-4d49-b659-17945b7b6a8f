package com.uinnova.product.vmdb.provider.rule.bean;

import com.binary.core.util.BinaryUtils;
import com.uinnova.product.vmdb.comm.model.rule.CcRltRuleDef;
import com.uinnova.product.vmdb.comm.model.rule.CcRltRuleInst;

import java.util.ArrayList;
import java.util.List;

/**
 * 朋友圈实例扩展对象
 */
public class CcRltRuleInstExt extends CcRltRuleInst{
	private static final long serialVersionUID = 1L;
	
	/**
	 * 所属的的朋友圈规则定义
	 */
	private CcRltRuleDef rltRuleDef;
	/**
	 * 关注的ci列表
	 */
	private List<Long> ciIdList;
	
	public CcRltRuleInstExt() {}
	
	public CcRltRuleDef getRltRuleDef() {
		return rltRuleDef;
	}
	public void setRltRuleDef(CcRltRuleDef rltRuleDef) {
		this.rltRuleDef = rltRuleDef;
	}
	public List<Long> getCiIdList() {
		if (BinaryUtils.isEmpty(ciIdList) && !BinaryUtils.isEmpty(getCiIds(), true)) {
			ids2idList(getCiIds());
		}
		return ciIdList;
	}
	public void setCiIdList(List<Long> ciIdList) {
		this.ciIdList = ciIdList;
		if (!BinaryUtils.isEmpty(ciIdList)) {
			String ciIds = ",";
			for (Long id : ciIdList) {
				ciIds += id+",";
			}
			super.setCiIds(ciIds);
		}
	}
	@Override
	public void setCiIds(String ciIds) {
		super.setCiIds(ciIds);
		ids2idList(ciIds);
	}
	/**
	 * id构成的字符串转id列表
	 */
	private void ids2idList(String ciIds) {
		if (null!=ciIds) {
			String[] split = ciIds.split(",");
			this.ciIdList = new ArrayList<Long>(split.length);
			for (String string : split) {
				if (BinaryUtils.isEmpty(string, true)) continue;
				Long id = Long.parseLong(string.trim());
				ciIdList.add(id);
			}
		}
	}
	
	

	
}
