package com.uinnova.product.eam.web.integration.bean;

import com.binary.framework.bean.annotation.Comment;

public enum VcCiRltQ {

	@Comment("只有关系和属性值")
	CI_RLt_ATTR,
 
	
	
	@Comment("有关系和属性值,还有起始CI,结束CI信息,不包含分类信息")
	CI_RLt_CI,

	
	
	@Comment("有关系和属性值,还有起始CI,结束CI信息,不包含分类信息")
	CI_RLt_CI_Attr,
	

	@Comment("有关系和属性值,还有起始CI,结束CI包含分类的信息")
	CI_RLt_CI_CLASS,

	
	@Comment("有关系和属性值,还有起始CI,结束CI包含分类的信息")
	CI_RLt_CI_CLASS_ATTR_DEF;

}
