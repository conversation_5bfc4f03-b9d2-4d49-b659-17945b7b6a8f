package com.uino.util.encrypt.impl.type;


import org.jasypt.encryption.StringEncryptor;
import org.jasypt.encryption.pbe.PooledPBEStringEncryptor;
import org.jasypt.encryption.pbe.config.PBEConfig;
import org.jasypt.encryption.pbe.config.SimpleStringPBEConfig;
import org.springframework.stereotype.Component;

/**
 * @Title: JasyptUtils
 * @Author: YGQ
 * @Create: 2021-08-07 12:53
 **/
@Component
public class JasyptUtil {
    /**
     * {@link StringEncryptor} encryption and decryption。

     * @param secret secret key
     * @param str pending string
     * @param isEncrypt true encryption false decryption
     */
    public String stringEncryptor(String secret, String str, boolean isEncrypt) {
        try {
            PooledPBEStringEncryptor pbeStringEncryptor = new PooledPBEStringEncryptor();
            pbeStringEncryptor.setConfig(getSimpleStringPbeConfig(secret));
            return isEncrypt ? pbeStringEncryptor.encrypt(str) : pbeStringEncryptor.decrypt(str);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     *  {@link PBEConfig}
     * @param secretKey secret key
     */
    private SimpleStringPBEConfig getSimpleStringPbeConfig(String secretKey) {
        SimpleStringPBEConfig config = new SimpleStringPBEConfig();
        config.setPassword(secretKey);
        config.setPoolSize("5");
        config.setAlgorithm("PBEWITHHMACSHA512ANDAES_256");
        config.setKeyObtentionIterations("1000");
        config.setProviderName("SunJCE");
        // The encoding form of the string output. base 64, hexadecimal
        config.setStringOutputType("base64");
        return config;
    }

}
