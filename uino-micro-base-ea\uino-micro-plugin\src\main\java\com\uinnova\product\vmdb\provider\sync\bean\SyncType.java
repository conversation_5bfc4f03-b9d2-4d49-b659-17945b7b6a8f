package com.uinnova.product.vmdb.provider.sync.bean;


/**
 * 同步数据类型
 * <AUTHOR>
 */
public enum SyncType {

	
	
	/**
	 * 同步CI数据(CI数据有变化触发)
	 */
	CI,
	
	/**
	 * 同步CI数据到(ES环境中)
	 */
	CI_TO_ES,
	
	/**
	 * 同步CI关系数据到(ES环境中)
	 */
	CI_RLT_TO_ES,
	
	/**
	 * 同步CI分类数据(CI分类数据有变化触发)
	 */
	CI_CLASS_TO_ES,
	
	/**
	 * 通CITag到ES中
	 */
	TAG_RULE_TO_ES,
	
	
	/**
	 * 同步CI关系数据(CI关系数据有变化触发)
	 */
	CI_RLT,
	
	
	
	/**
	 * 朋友圈规则
	 */
	FRIEND_RULE,
	
	
	
	/**
	 * 标签规则
	 */
	TAG_RULE ,
	
	/**
	 * 视图和CI(包括ci_tag里的CI)
	 */
	DIAGRAM_CI,
	
	/**
	 * CI质量检测任务
	 */
	CI_QUALITY,
	
	
	/**
	 * 将CI同步到扩展表中
	 */
	DCV_CI_EXT,
	
	/**
	 * 动态分类树状图定时刷新任务
	 */
	DYNAMIC_CLASS_TREE,
	
	/**
	 * 自动构建CI关系数据
	 */
	AUTO_SAVE_CI_RLT,
	
	
	/**
	 * 自动清理data_status为0的数据
	 */
	AUTO_CLEAN_DATA,
	
	/**
	 * 自动修复数据
	 */
	AUTO_DATA_REPAIR,

	/**
	 * 自动清理CI操作日志
	 */
	AUTO_CLEAN_CI_LOG,
	
	/**
	 * 用户密码过期提醒
	 */
	USER_PASSWORD_EXPIRE;
	
}
