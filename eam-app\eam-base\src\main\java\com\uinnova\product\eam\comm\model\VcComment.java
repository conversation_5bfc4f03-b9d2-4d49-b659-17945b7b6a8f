package com.uinnova.product.eam.comm.model;




import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;


@Comment("视图评论表[VC_COMMENT]")
public class VcComment implements EntityBean {
	private static final long serialVersionUID = 1L;


	@Comment("ID[ID]")
	private Long id;


	@Comment("视图ID[DIAGRAM_ID]")
	private Long diagramId;


	@Comment("评论用户ID[USER_ID]")
	private Long userId;


	@Comment("评论用户代码[USER_CODE]")
	private String userCode;


	@Comment("评论用户姓名[USER_NAME]")
	private String userName;


	@Comment("评论时间[COM_TIME]")
	private Long comTime;


	@Comment("评论内容[COM_DESC]")
	private String comDesc;


	@Comment("所属域[DOMAIN_ID]")
	private Long domainId;


	@Comment("创建时间[CREATE_TIME]    yyyyMMddHHmmss")
	private Long createTime;


	@Comment("修改时间[MODIFY_TIME]    yyyyMMddHHmmss")
	private Long modifyTime;




	public Long getId() {
		return this.id;
	}
	public void setId(Long id) {
		this.id = id;
	}


	public Long getDiagramId() {
		return this.diagramId;
	}
	public void setDiagramId(Long diagramId) {
		this.diagramId = diagramId;
	}


	public Long getUserId() {
		return this.userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}


	public String getUserCode() {
		return this.userCode;
	}
	public void setUserCode(String userCode) {
		this.userCode = userCode;
	}


	public String getUserName() {
		return this.userName;
	}
	public void setUserName(String userName) {
		this.userName = userName;
	}


	public Long getComTime() {
		return this.comTime;
	}
	public void setComTime(Long comTime) {
		this.comTime = comTime;
	}


	public String getComDesc() {
		return this.comDesc;
	}
	public void setComDesc(String comDesc) {
		this.comDesc = comDesc;
	}


	public Long getDomainId() {
		return this.domainId;
	}
	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}


	public Long getCreateTime() {
		return this.createTime;
	}
	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}


	public Long getModifyTime() {
		return this.modifyTime;
	}
	public void setModifyTime(Long modifyTime) {
		this.modifyTime = modifyTime;
	}


}


