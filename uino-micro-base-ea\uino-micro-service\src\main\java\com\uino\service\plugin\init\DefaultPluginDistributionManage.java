package com.uino.service.plugin.init;

import com.uino.plugin.bean.OperatePluginDetails;
import com.uino.plugin.client.service.PluginSvc;
import lombok.extern.log4j.Log4j;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.multipart.MultipartFile;

import java.util.Collections;
import java.util.List;

/**
 * 默认分发操作中只对当前服务操作，故而通过注入svc的方式实现；
 * 如果多服务插件处理，请通过http请求处理
 *
 * <AUTHOR>
 * @Description
 * @Date 2021/9/27
 * @Version 1.0
 */
@Slf4j
public class DefaultPluginDistributionManage implements PluginDistributionManage {

    @Autowired(required = false)
    private PluginSvc pluginSvc;

    @Override
    public List<String> getServiceList() {
        if (pluginSvc == null) {
            return Collections.EMPTY_LIST;
        }else {
            return Collections.singletonList("current service");
        }
    }

    @Override
    public boolean synchronizePluginFile(String jarName) {
        return true;
    }

    @Override
    public List<OperatePluginDetails> uploadPlugin(List<String> ownedService, MultipartFile multipartFile) {
        if (pluginSvc == null) {
            log.error("没有可操作的服务");
            return Collections.singletonList(new OperatePluginDetails(multipartFile.getName(),null,null,false,"没有可操作的服务"));
        }
        return Collections.singletonList(pluginSvc.uploadPlugin(multipartFile));
    }

    @Override
    public List<OperatePluginDetails> loadPlugin(List<String> ownedService, String jarName) {
        if (pluginSvc == null) {
            log.error("没有可操作的服务");
            return Collections.singletonList(new OperatePluginDetails(jarName,null,null,false,"没有可操作的服务"));
        }
        return Collections.singletonList(pluginSvc.loadPlugin(jarName));
    }

    @Override
    public List<OperatePluginDetails> unloadAndDeletePlugin(List<String> ownedService, String jarName) {
        if (pluginSvc == null) {
            log.error("没有可操作的服务");
            return Collections.singletonList(new OperatePluginDetails(jarName,null,null,false,"没有可操作的服务"));
        }
        return Collections.singletonList(pluginSvc.unloadAndDeletePlugin(jarName));
    }


}
