package com.uinnova.product.eam.local.diagram;

import com.uinnova.product.eam.api.diagram.ExternalDataClient;
import com.uinnova.product.eam.model.diagram.EamHttpRequestParam;
import com.uinnova.product.eam.service.diagram.IExternalSvc;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @description:
 * @author: ManolinCoder
 * @time: 2021/9/26
 */
@Service
public class ExternalSvcLocal implements ExternalDataClient {
    @Autowired
    private IExternalSvc externalSvc;
    public Object requestByUrl(EamHttpRequestParam param){
        return externalSvc.requestByUrl(param);
    }
}
