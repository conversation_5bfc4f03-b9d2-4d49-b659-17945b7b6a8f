package com.uinnova.product.eam.db;

import java.util.List;

import com.binary.jdbc.Page;
import com.uinnova.product.eam.comm.bean.SceneDiagram;
import com.uinnova.product.eam.comm.model.CVcDiagram;
import com.uinnova.product.eam.comm.model.VcDiagram;
import com.uinnova.product.eam.db.bean.DiagramDirCount;
import com.uinnova.product.eam.db.bean.DiagramInfoCount;
import com.uinnova.product.eam.db.bean.DiagramTagGroup;
import com.uinnova.product.eam.db.bean.DiagramUserGroup;
import com.uinnova.product.eam.db.bean.GroupDiagramInfoCount;
import com.uinnova.product.eam.db.bean.UserDiagramInfoCount;
import com.uinnova.product.eam.db.support.dao.ComMyBatisBinaryDao;

/**
 * 视图设计表[VC_DIAGRAM]数据访问对象
 */
public interface VcDiagramDao extends ComMyBatisBinaryDao<VcDiagram, CVcDiagram> {

	/**
	 * 分页查询公开的视图..
	 * 
	 * @param pageNum
	 * @param pageSize
	 * @param like
	 *            视图名称or标签名称or小组名称or创建者名称
	 * @param cdt
	 * @param orders
	 * @return
	 */
	public Page<VcDiagram> selectOpenPageExt(long pageNum, long pageSize, String[] likes, Long[] userIds,
			Long[] groupIds, Long[] tagIds, CVcDiagram cdt, String orders);

	/**
	 * 查询用户可以看到的全部视图
	 * 
	 * @param pageNum
	 * @param pageSize
	 * @param userId
	 *            当前用户ID
	 * @param cdt
	 *            查询条件 应该带上 status = 1
	 * @param orders
	 *            视图的排序
	 * @return
	 */
	public Page<VcDiagram> selectWorkabilityPage(long pageNum, long pageSize, Long userId, CVcDiagram cdt,
			String orders);

	/**
	 * 通过CIid查询相关联的视图
	 * 
	 * @param ciIds
	 *            ci的id
	 * @param cdt
	 * @param orders
	 * @return
	 */
	public List<VcDiagram> selectListByCiIdsExt(Long[] ciIds, CVcDiagram cdt, String orders);

	/**
	 * 查询TAG下公开的视图的数量
	 * 
	 * @param domainId
	 * @return
	 */
	public List<DiagramTagGroup> selectOpenDiagramCountByTag(Long domainId, String like,
															 Long[] userIds);

	/**
	 * 查询User下公开的视图的数量
	 * 
	 * @param domainId
	 * @return
	 */
	public List<DiagramUserGroup> selectOpenDiagramCountByUser(Long domainId, String like, Long[] userIds);

	/**
	 * 分页查询公开的视图..
	 * 
	 * @param pageNum
	 * @param pageSize
	 * @param likes
	 * @param userIds
	 * @param groupIds
	 * @param tagIds
	 * @param cdt
	 * @param ciIdxExp
	 * @param ciTagIds
	 * @param orders
	 * @return
	 */
	public Page<VcDiagram> selectOpenDiagramPageByCiIndexExt(long pageNum, long pageSize, String[] likes,
			Long[] userIds, Long[] groupIds, Long[] tagIds, CVcDiagram cdt, String ciIdxExp, Long[] ciTagIds,
			String orders);

	/**
	 * 查询当前用户是否有视图编辑权限
	 * 
	 * @param diagramId
	 * @param userId
	 * @return 1有编辑权限,0没有
	 */
	public Integer selectEditAuthByDiagramId(Long domainId, Long diagramId, Long userId);

	/**
	 * 根据组合视图ID查询场景信息
	 * 
	 * @param domainId
	 *            数据域
	 * @param diagramIds
	 *            视图ID数组
	 * @param diagramType
	 *            视图类型
	 * @return 列表结果
	 */
	public List<SceneDiagram> selectSceneDiagramListByDids(Long domainId, Long[] diagramIds, Integer diagramType);

	/**
	 * 根据场景ID数组删除场景视图关联信息
	 * 
	 * @param domainId
	 *            数据域
	 * @param sceneIds
	 *            场景ID
	 */
	public Integer deleteSceneDiagramBySceneIds(Long domainId, Long[] sceneIds);

	/**
	 * 根据场景ID删除场景信息
	 * 
	 * @param domainId
	 *            数据域
	 * @param sceneIds
	 *            场景ID数组
	 */
	public Integer deleteSceneBySceneIds(Long domainId, Long[] sceneIds);

	/**
	 * 根据id更新视图查看次数
	 * 
	 * @param id
	 * @param domainId
	 * @return
	 */
	public Integer updateDiagramReadCountById(Long domainId, Long id);

	/**
	 * 根据appRltCiCode更新该字段为NULL
	 * 
	 * @param appRltCiCode
	 * @return
	 */
	public Integer updateAppRltCiCodeNullByRltCiCode(Long domainId, String appRltCiCode);

	/**
	 * 统计视图分析
	 * 
	 * @param domainId
	 * @param orders
	 * @return
	 */
	public List<DiagramInfoCount> countDiagramInfo(Long domainId, String orders, Long startDate, Long endDate);

	List<DiagramInfoCount> countDiagramInfoRead(Long domainId, Long startDate, Long endDate);

	List<DiagramInfoCount> countDiagramInfoEnsh(Long domainId, Long startDate, Long endDate);

	/**
	 * 按用户分组查询发布到广场的视图
	 * 
	 * @param domainId
	 * @return
	 */
	public List<UserDiagramInfoCount> countOpenDiagramGroupByUser(Long domainId, Long startDate, Long endDate);

	/**
	 * 按小组统计视图信息
	 * 
	 * @param domainId
	 * @param orders
	 * @return
	 */
	public List<GroupDiagramInfoCount> countGroupDiagramInfo(Long domainId, String orders, Long startDate, Long endDate);

	/**
	 * 按用户统计视图信息
	 * 
	 * @param domainId
	 * @param orders
	 * @return
	 */
	public List<UserDiagramInfoCount> countUserDiagramInfo(Long domainId, String orders, Long startDate, Long endDate);

	/**
	 * 带notIn的条件查询
	 * 
	 * @param pageNum
	 * @param pageSize
	 * @param cdt
	 * @param notInIds
	 * @param orders
	 * @return
	 */
	public Page<VcDiagram> selectPageByCdtAndNotInIds(long pageNum, long pageSize, Long[] notInIds, CVcDiagram cdt,
			String orders);

	/**
	 * 查询目录下视图的数量
	 * 
	 * @param domainId
	 * @param dirIds
	 * @param userId
	 * @return
	 */
	public List<DiagramDirCount> selectDirDiagramCountList(Long domainId, Long[] dirIds, Long userId);

	/**
	 * 统计所有单图和模板的视图数量
	 * 
	 * @param domainId
	 * @return
	 */
	public Long countAllDiagramCount(Long domainId);

	@Override
	List<VcDiagram> selectList(CVcDiagram cdt, String orders);
}
