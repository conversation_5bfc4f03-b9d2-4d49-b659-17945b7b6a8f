package com.uinnova.product.eam.web.cj;

import com.uinnova.product.eam.base.cj.ResultMsg;
import com.uinnova.product.eam.model.cj.domain.PlanTemplateChapter;
import com.uinnova.product.eam.model.cj.vo.PlanTemplateChapterDataVo;
import com.uinnova.product.eam.model.cj.vo.PlanTemplateChapterTreeVo;
import com.uinnova.product.eam.model.cj.vo.PlanTemplateChapterVo;
import com.uinnova.product.eam.model.cj.vo.ResetSortVo;
import com.uinnova.product.eam.service.cj.service.PlanTemplateChapterService;
import com.uino.bean.permission.base.SysUser;
import com.uino.util.sys.SysUtil;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * @description: 方案模板章节
 * @author: Lc
 * @create: 2022-01-05 17:00
 */
@RestController
@RequestMapping("/planTemplateChapter")
public class PlanTemplateChapterController {

    @Resource
    private PlanTemplateChapterService planTemplateChapterService;

    @PostMapping("/addOrUpdate")
    public ResultMsg addPlanTemplateChapter(@RequestBody PlanTemplateChapterVo planTemplateChapterVo) {
        SysUser user = SysUtil.getCurrentUserInfo();
        Long result = planTemplateChapterService.addPlanTemplateChapter(planTemplateChapterVo, user);
        return new ResultMsg(result);
    }

    @PostMapping("/findList")
    public ResultMsg findPlanTemplateChapterList(@RequestBody PlanTemplateChapter templateChapter) {
        List<PlanTemplateChapterTreeVo> chapterList = planTemplateChapterService.findPlanTemplateChapterList(templateChapter);
        return new ResultMsg(chapterList);
    }

    @GetMapping("/get/{id}")
    public ResultMsg getPlanTemplateChapter(@PathVariable("id") Long id) {
        PlanTemplateChapterDataVo chapterDataVo = planTemplateChapterService.getPlanTemplateChapter(id);
        return new ResultMsg(chapterDataVo);
    }

    @PostMapping("/delete")
    public ResultMsg deletePlanTemplateChapter(@RequestBody PlanTemplateChapter templateChapter) {
        boolean result = planTemplateChapterService.deletePlanTemplateChapter(templateChapter);
        return new ResultMsg(result);
    }


    @GetMapping("/copy")
    public ResultMsg copyPlanTemplateChapter(@RequestParam(value = "chapterId", required = true) Long chapterId,
                                             @RequestParam(value = "chapterName", required = false) String chapterName) {
        SysUser user = SysUtil.getCurrentUserInfo();
        Long result = planTemplateChapterService.copyPlanTemplateChapter(chapterId, chapterName, user);
        return new ResultMsg(result);
    }

    @PostMapping("/resetSort")
    public ResultMsg resetSort(@RequestBody ResetSortVo resetSortVo) {
        boolean result = planTemplateChapterService.resetSort(resetSortVo);
        return new ResultMsg(result);
    }

    @GetMapping("/deleteChapterData/{id}")
    public ResultMsg deletePlanTemplateChapterData(@PathVariable("id") Long id) {
        boolean result = planTemplateChapterService.deletePlanTemplateChapterData(id);
        return new ResultMsg(result);
    }

    @GetMapping("/getRemark/{chapterId}")
    public ResultMsg getRemark(@PathVariable("chapterId") Long chapterId) {
        PlanTemplateChapter chapter = planTemplateChapterService.getRemark(chapterId);
        return new ResultMsg(chapter);
    }

    @GetMapping("/checkViewType")
    public Boolean checkViewType(@RequestParam("viewType") String viewType) {
        return planTemplateChapterService.checkViewType(viewType);
    }

    /**
     * TODO 刷模板数据，制品和表格
     * @return
     */
    @GetMapping("/refreshTemplateData")
    public Boolean refreshTemplateData() {
        return planTemplateChapterService.refreshTemplateData();
    }
}
