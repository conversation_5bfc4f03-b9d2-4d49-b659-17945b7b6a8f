package com.uinnova.product.vmdb.provider.search.bean;

import java.io.Serializable;


public class CcCiSearchPage implements Serializable {
	private static final long serialVersionUID = 1L;
	
	

	private long pageNum;
	private long pageSize;
	
	private long totalRows;
	private long totalPages;
	
	
	private CcCiSearchData data;
	
	
	
	
	
	public CcCiSearchPage() {
	}
	
	public CcCiSearchPage(long pageNum, long pageSize, long totalRows, long totalPages, CcCiSearchData data) {
		this.pageNum = pageNum;
		this.pageSize = pageSize;
		this.totalRows = totalRows;
		this.totalPages = totalPages;
		this.data = data;
	}
	
	
	
	
	public long getPageNum() {
		return pageNum;
	}
	public void setPageNum(long pageNum) {
		this.pageNum = pageNum;
	}
	public long getPageSize() {
		return pageSize;
	}
	public void setPageSize(long pageSize) {
		this.pageSize = pageSize;
	}
	public long getTotalRows() {
		return totalRows;
	}
	public void setTotalRows(long totalRows) {
		this.totalRows = totalRows;
	}
	public long getTotalPages() {
		return totalPages;
	}
	public void setTotalPages(long totalPages) {
		this.totalPages = totalPages;
	}
	public CcCiSearchData getData() {
		return data;
	}
	public void setData(CcCiSearchData data) {
		this.data = data;
	}
	
	
	
	
	
	
	
	
	
}
