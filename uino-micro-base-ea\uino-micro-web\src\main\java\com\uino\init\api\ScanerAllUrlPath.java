package com.uino.init.api;

import com.uino.bean.permission.base.SysUrlPatchManager;
import com.uino.dao.permission.ESUrlPatchManagerSvc;
import com.uino.dao.util.ESUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.aop.support.AopUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.lang.reflect.Method;
import java.util.*;

/**
 * 扫描并生成所有url的路径
 *
 * <AUTHOR>
 * @since 2023/3/15 10:36
 */
@Component
@Slf4j
public class ScanerAllUrlPath implements ApplicationContextAware, CommandLineRunner {

    private ApplicationContext applicationContext;

    @Resource
    private ESUrlPatchManagerSvc esUrlPatchManagerSvc;

    @Value("${oauth.client.id}")
    private String moduleName;

    @Value("${sys.interface.generator.model:none}")
    private String generatorModel;

    @Override
    public void run(String... args) {

        if("none".equalsIgnoreCase(generatorModel)){
            return;
        }
        esUrlPatchManagerSvc.deleteByQuery(QueryBuilders.boolQuery().must(QueryBuilders.matchAllQuery()),Boolean.TRUE);
        //获取使用RestController注解标注的的所有controller类
        Map<String, Object> controllers = applicationContext.getBeansWithAnnotation(Controller.class);
        List<SysUrlPatchManager> sysUrlPatchManagers = new ArrayList<>(1000);
        //遍历每个controller层
        for (Map.Entry<String, Object> entry : controllers.entrySet()) {
            Object value = entry.getValue();
            Class<?> aClass = AopUtils.getTargetClass(value);
            String[] parentPath=null;
            RequestMapping controllerRequestMapping = aClass.getAnnotation(RequestMapping.class);
            if(controllerRequestMapping!=null){
                parentPath = controllerRequestMapping.value();
            }
            List<String> classTags = new ArrayList<>();
            Api apiAnnotation = aClass.getAnnotation(Api.class);
            if(apiAnnotation!=null){
                String[] tags = apiAnnotation.tags();
                classTags.addAll(Arrays.asList(tags));
            }

            Method[] declaredMethods = aClass.getDeclaredMethods();
            for (Method declaredMethod : declaredMethods) {
                String[] path;
                if(declaredMethod.isAnnotationPresent(RequestMapping.class)){
                    RequestMapping annotation = declaredMethod.getAnnotation(RequestMapping.class);
                    path = annotation.value();
                }else if(declaredMethod.isAnnotationPresent(GetMapping.class)){
                    GetMapping annotation =declaredMethod.getAnnotation(GetMapping.class);
                    path = annotation.value();
                }else if(declaredMethod.isAnnotationPresent(PostMapping.class)){
                    PostMapping annotation =declaredMethod.getAnnotation(PostMapping.class);
                    path = annotation.value();
                }else if(declaredMethod.isAnnotationPresent(PutMapping.class)){
                    PutMapping annotation =declaredMethod.getAnnotation(PutMapping.class);
                    path = annotation.value();
                }else if(declaredMethod.isAnnotationPresent(DeleteMapping.class)){
                    DeleteMapping annotation =declaredMethod.getAnnotation(DeleteMapping.class);
                    path = annotation.value();
                }else if(declaredMethod.isAnnotationPresent(PatchMapping.class)){
                    PatchMapping annotation =declaredMethod.getAnnotation(PatchMapping.class);
                    path = annotation.value();
                }else {
                    path = new String[0];
                }

                ApiOperation apiOperation = declaredMethod.getAnnotation(ApiOperation.class);
                Set<String> allTags = new HashSet<>();
                if(apiOperation!=null){
                    String[] methodTags = apiOperation.tags();
                    if(methodTags!=null&&methodTags.length>0){
                        allTags.addAll(Arrays.asList(methodTags));
                    }
                }
                allTags.addAll(classTags);
                Set<String> urlPatchSet = new HashSet<>();
                if(parentPath != null&&parentPath.length>0){
                    for (String s : parentPath) {
                        if(path.length>0){
                            for (String s1 : path) {
                                urlPatchSet.add(s+s1);
                            }
                        }
                    }
                }else {
                        urlPatchSet.addAll(Arrays.asList(path));
                }
                for (String s : urlPatchSet) {
                    SysUrlPatchManager sysUrlPatchManager = new SysUrlPatchManager();
                    sysUrlPatchManager.setId(ESUtil.getUUID());
                    sysUrlPatchManager.setUrlPatch(s);
                    sysUrlPatchManager.setProjectModule(moduleName);
                    allTags.removeIf(StringUtils::isEmpty);
                    sysUrlPatchManager.setTags(allTags);
                    sysUrlPatchManager.setClassName(aClass.getName());
                    sysUrlPatchManagers.add(sysUrlPatchManager);
                }
            }
        }
        log.info("共{}条urlpatch",sysUrlPatchManagers.size());
        esUrlPatchManagerSvc.saveOrUpdateBatch(sysUrlPatchManagers);
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext=applicationContext;
    }
}
