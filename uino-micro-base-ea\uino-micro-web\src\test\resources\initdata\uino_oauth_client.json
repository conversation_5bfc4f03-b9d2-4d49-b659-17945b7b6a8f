[{"id": 1, "clientCode": "tarsier-comm", "codeToTokenUrl": ["http://192.168.1.212:1536/tarsier-comm/getTokenByCode"], "secretRequired": true, "clientSecret": "tarsier-comm", "refreshTokenValiditySeconds": 86400, "accessTokenValiditySeconds": 43200, "scoped": true, "scopes": ["tarsier-comm", "tarsier"], "autoApprove": true, "attrs": {"clientIndexUrl": "http://192.168.1.212/examples/"}, "resourceIds": ["tarsier-comm"], "athorities": [], "authorizedGrantTypes": ["authorization_code", "refresh_token"], "domainId": 1, "creator": "init", "modifier": "init", "createTime": 20170516222748, "modifyTime": 20170516222748}]