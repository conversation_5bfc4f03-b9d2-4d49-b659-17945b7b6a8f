package com.uinnova.product.vmdb.comm.bean;




import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;


@Comment("模块菜单与url关联表[SYS_MODU_CODE_URL]")
public class CSysModuCodeUrl implements Condition {
    private static final long serialVersionUID = 1L;


    @Comment("ID[ID] operate-Equal[=]")
    private Long id;


    @Comment("ID[ID] operate-In[in]")
    private Long[] ids;


    @Comment("ID[ID] operate-GTEqual[>=]")
    private Long startId;

    @Comment("ID[ID] operate-LTEqual[<=]")
    private Long endId;


    @Comment("模块代码[MODU_CODE] operate-Like[like]")
    private String moduCode;


    @Comment("模块代码[MODU_CODE] operate-Equal[=]")
    private String moduCodeEqual;


    @Comment("模块代码[MODU_CODE] operate-In[in]")
    private String[] moduCodes;


    @Comment("URL[URL] operate-Like[like]")
    private String url;


    @Comment("所属域[DOMAIN_ID] operate-Equal[=]")
    private Long domainId;


    @Comment("所属域[DOMAIN_ID] operate-In[in]")
    private Long[] domainIds;


    @Comment("所属域[DOMAIN_ID] operate-GTEqual[>=]")
    private Long startDomainId;

    @Comment("所属域[DOMAIN_ID] operate-LTEqual[<=]")
    private Long endDomainId;


    @Comment("数据状态[DATA_STATUS] operate-Equal[=]    数据状态：1-正常 0-删除")
    private Integer dataStatus;


    @Comment("数据状态[DATA_STATUS] operate-In[in]    数据状态：1-正常 0-删除")
    private Integer[] dataStatuss;


    @Comment("数据状态[DATA_STATUS] operate-GTEqual[>=]    数据状态：1-正常 0-删除")
    private Integer startDataStatus;

    @Comment("数据状态[DATA_STATUS] operate-LTEqual[<=]    数据状态：1-正常 0-删除")
    private Integer endDataStatus;


    @Comment("创建时间[CREATE_TIME] operate-Equal[=]    yyyyMMddHHmmss")
    private Long createTime;


    @Comment("创建时间[CREATE_TIME] operate-In[in]    yyyyMMddHHmmss")
    private Long[] createTimes;


    @Comment("创建时间[CREATE_TIME] operate-GTEqual[>=]    yyyyMMddHHmmss")
    private Long startCreateTime;

    @Comment("创建时间[CREATE_TIME] operate-LTEqual[<=]    yyyyMMddHHmmss")
    private Long endCreateTime;


    @Comment("修改时间[MODIFY_TIME] operate-Equal[=]    yyyyMMddHHmmss")
    private Long modifyTime;


    @Comment("修改时间[MODIFY_TIME] operate-In[in]    yyyyMMddHHmmss")
    private Long[] modifyTimes;


    @Comment("修改时间[MODIFY_TIME] operate-GTEqual[>=]    yyyyMMddHHmmss")
    private Long startModifyTime;

    @Comment("修改时间[MODIFY_TIME] operate-LTEqual[<=]    yyyyMMddHHmmss")
    private Long endModifyTime;




    public Long getId() {
        return this.id;
    }
    public void setId(Long id) {
        this.id = id;
    }


    public Long[] getIds() {
        return this.ids;
    }
    public void setIds(Long[] ids) {
        this.ids = ids;
    }


    public Long getStartId() {
        return this.startId;
    }
    public void setStartId(Long startId) {
        this.startId = startId;
    }


    public Long getEndId() {
        return this.endId;
    }
    public void setEndId(Long endId) {
        this.endId = endId;
    }


    public String getModuCode() {
        return this.moduCode;
    }
    public void setModuCode(String moduCode) {
        this.moduCode = moduCode;
    }


    public String getModuCodeEqual() {
        return this.moduCodeEqual;
    }
    public void setModuCodeEqual(String moduCodeEqual) {
        this.moduCodeEqual = moduCodeEqual;
    }


    public String[] getModuCodes() {
        return this.moduCodes;
    }
    public void setModuCodes(String[] moduCodes) {
        this.moduCodes = moduCodes;
    }


    public String getUrl() {
        return this.url;
    }
    public void setUrl(String url) {
        this.url = url;
    }


    public Long getDomainId() {
        return this.domainId;
    }
    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }


    public Long[] getDomainIds() {
        return this.domainIds;
    }
    public void setDomainIds(Long[] domainIds) {
        this.domainIds = domainIds;
    }


    public Long getStartDomainId() {
        return this.startDomainId;
    }
    public void setStartDomainId(Long startDomainId) {
        this.startDomainId = startDomainId;
    }


    public Long getEndDomainId() {
        return this.endDomainId;
    }
    public void setEndDomainId(Long endDomainId) {
        this.endDomainId = endDomainId;
    }


    public Integer getDataStatus() {
        return this.dataStatus;
    }
    public void setDataStatus(Integer dataStatus) {
        this.dataStatus = dataStatus;
    }


    public Integer[] getDataStatuss() {
        return this.dataStatuss;
    }
    public void setDataStatuss(Integer[] dataStatuss) {
        this.dataStatuss = dataStatuss;
    }


    public Integer getStartDataStatus() {
        return this.startDataStatus;
    }
    public void setStartDataStatus(Integer startDataStatus) {
        this.startDataStatus = startDataStatus;
    }


    public Integer getEndDataStatus() {
        return this.endDataStatus;
    }
    public void setEndDataStatus(Integer endDataStatus) {
        this.endDataStatus = endDataStatus;
    }


    public Long getCreateTime() {
        return this.createTime;
    }
    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }


    public Long[] getCreateTimes() {
        return this.createTimes;
    }
    public void setCreateTimes(Long[] createTimes) {
        this.createTimes = createTimes;
    }


    public Long getStartCreateTime() {
        return this.startCreateTime;
    }
    public void setStartCreateTime(Long startCreateTime) {
        this.startCreateTime = startCreateTime;
    }


    public Long getEndCreateTime() {
        return this.endCreateTime;
    }
    public void setEndCreateTime(Long endCreateTime) {
        this.endCreateTime = endCreateTime;
    }


    public Long getModifyTime() {
        return this.modifyTime;
    }
    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }


    public Long[] getModifyTimes() {
        return this.modifyTimes;
    }
    public void setModifyTimes(Long[] modifyTimes) {
        this.modifyTimes = modifyTimes;
    }


    public Long getStartModifyTime() {
        return this.startModifyTime;
    }
    public void setStartModifyTime(Long startModifyTime) {
        this.startModifyTime = startModifyTime;
    }


    public Long getEndModifyTime() {
        return this.endModifyTime;
    }
    public void setEndModifyTime(Long endModifyTime) {
        this.endModifyTime = endModifyTime;
    }


}


