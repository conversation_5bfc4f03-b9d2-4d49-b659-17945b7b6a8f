package com.uinnova.product.eam.comm.model.es;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@Comment("分类操作配置表[UINO_EAM_CLASS_SETTING]")
public class ClassSetting implements Serializable{
    @Comment("分类操作设置ID")
    private Long id;
    @Comment("领域ID")
    private Long fieldId;
    @Comment("分类ID")
    private Long classId;
    @Comment("分类名称")
    private String className;
    @Comment("排序字段")
    private Integer order;
    @Comment("创建人")
    private String createUser;
    @Comment("修改人")
    private String modifyUser;
    @Comment("创建时间")
    private Long createTime;
    @Comment("修改时间")
    private Long modifyTime;
}
