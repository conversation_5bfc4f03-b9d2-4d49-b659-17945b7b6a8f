package com.uino.provider.feign.monitor;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import com.binary.jdbc.Page;
import com.uino.bean.chart.bean.UinoChartDataBean;
import com.uino.bean.monitor.base.ESPerformance;
import com.uino.bean.monitor.base.SimulationRuleInfo;
import com.uino.bean.monitor.buiness.ImportPerformanceReqDto;
import com.uino.bean.monitor.buiness.PerformanceQueryDto;
import com.uino.bean.tp.base.FinalPerformanceDTO;
import com.uino.provider.feign.config.BaseFeignConfig;

@Component
@FeignClient(name = "${feign-server-name:tarsier-platform}", path = "${feign-server-path:tarsier-platform}/feign/performance", configuration = {BaseFeignConfig.class})
public interface PerformanceFeign {

    /**
     * 导入性能数据
     * 
     * @param excelFile
     *            性能数据excel
     * @param objId
     *            性能数据导入对象id {@link ESPerformance#getObjId()}
     * @param objType
     *            性能数据导入对象类型{@link ESPerformance#getObjType()}
     */
    @PostMapping("importPerformance")
    public void importPerformance(@RequestParam(name = "file", required = false) MultipartFile excelFile,
			@RequestParam ImportPerformanceReqDto importDto);

    /**
     * 导入性能数据
     * 
     * @see IPerformanceSvc#importPerformance(MultipartFile, Long, Integer)
     * @param importDto
     *            导入信息
     */
    @PostMapping("importPerformanceByInfo")
    public void importPerformance(@RequestBody(required = false) ImportPerformanceReqDto importDto);

    /**
     * 导出性能对象的性能模板
     * 
     * @param objId
     *            性能数据导入对象id {@link ESPerformance#getObjId()}
     * @param objType
     *            性能数据导入对象类型{@link ESPerformance#getObjType()}
     * @return file bytes
     */
    @PostMapping("exportPerformanceTemplate")
    public ResponseEntity<byte[]> exportPerformanceTemplate(@RequestParam(name="domainId")Long domainId,@RequestParam(name = "objId", required = false) Long objId,
            @RequestParam(name = "objType", required = false) Integer objType);

    /**
     * 分页查询性能-
     * 
     * @param queryDto
     * @return
     */
    @PostMapping("searchPerformance")
	public Page<FinalPerformanceDTO> searchPerformance(@RequestBody(required = false) PerformanceQueryDto queryDto);

	/**
	 * 
	 * @description 查询性能数据历史曲线图
	 * @author: ZMJ
	 * @param queryDto
	 * @return
	 * @example
	 */
	@PostMapping("searchPerformanceGraph")
	public UinoChartDataBean<List<Double>> searchPerformanceGraph(@RequestBody PerformanceQueryDto queryDto);

    /**
     * 查询未关联ci的性能数据
     * @author: weixuesong
     * @date: 2021/1/29 14:25
     * @param queryDto
     * @return: com.binary.jdbc.Page<com.alibaba.fastjson.JSONObject>
     */
    @PostMapping("searchNoCiPerformance")
	Page<FinalPerformanceDTO> searchNoCiPerformance(PerformanceQueryDto queryDto);

    /**
     * 查询性能数据标签
     * @author: weixuesong
     * @date: 2021/2/24 17:33
     * @param classId
     * @return: java.util.List<java.lang.String>
     */
    @PostMapping("getPerfDataLabel")
    List<String> getPerfDataLabel(@RequestBody Long classId);

	/**
	 * 模拟性能数据
	 * 
	 * @param bean
	 */
	@PostMapping("simulationPerformance")
	void simulationPerformance(SimulationRuleInfo bean);
}
