package com.uinnova.product.eam.model.asset;

import lombok.Data;

import java.util.List;

@Data
public class EamQuestionDTO {

    private Integer id;

    private List<MetricItem> metrics;

    @Data
    public static class MetricItem{

        private String itemNo;

        private String item;

        private Integer itemRate;

        private List<ItemSelector> selectors;

    }

    @Data
    public static class ItemSelector{

        private String code;

        private String name;

        private Integer score;

        private Integer itemRate;

        private List<String> matchItems;

    }

}
