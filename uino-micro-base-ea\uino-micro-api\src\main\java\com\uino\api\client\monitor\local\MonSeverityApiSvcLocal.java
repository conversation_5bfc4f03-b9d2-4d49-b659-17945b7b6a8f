package com.uino.api.client.monitor.local;

import java.util.List;
import java.util.Set;

import com.uino.dao.BaseConst;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.binary.core.io.Resource;
import com.uino.service.simulation.IMonSysSeveritySvc;
import com.uino.bean.cmdb.business.ImportResultMessage;
import com.uino.bean.monitor.base.ESMonSysSeverityInfo;
import com.uino.api.client.monitor.IMonSeverityApiSvc;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
public class MonSeverityApiSvcLocal implements IMonSeverityApiSvc {

    @Autowired
    private IMonSysSeveritySvc monSysSeveritySvc;

    @Override
    public List<ESMonSysSeverityInfo> querySeverityList(String searchVal) {
        return monSysSeveritySvc.querySeverityList(BaseConst.DEFAULT_DOMAIN_ID, searchVal);
    }

    @Override
    public List<ESMonSysSeverityInfo> querySeverityList(Long domainId, String searchVal) {
        return monSysSeveritySvc.querySeverityList(domainId, searchVal);
    }

    @Override
    public Long saveOrUpdateSeverity(ESMonSysSeverityInfo saveDto) {
        return monSysSeveritySvc.saveOrUpdateSeverity(saveDto);
    }

    @Override
    public void deleteServrityByIds(Set<Long> delIds) {
        monSysSeveritySvc.deleteServrityByIds(delIds);
    }

    @Override
    public Resource exportServrityInfos(Boolean isTpl) {
        return monSysSeveritySvc.exportSeverityInfos(BaseConst.DEFAULT_DOMAIN_ID, isTpl);
    }

    @Override
    public Resource exportServrityInfos(Long domainId, Boolean isTpl) {
        return monSysSeveritySvc.exportSeverityInfos(domainId, isTpl);
    }

    @Override
    public ImportResultMessage importSeverityInfos(MultipartFile file) {
        return monSysSeveritySvc.importSeverityInfos(BaseConst.DEFAULT_DOMAIN_ID, file);
    }

    @Override
    public ImportResultMessage importSeverityInfos(Long domainId, MultipartFile file) {
        return monSysSeveritySvc.importSeverityInfos(domainId, file);
    }
}
