package com.uinnova.product.vmdb.provider.ci.bean;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.vmdb.comm.bean.CiAuthable;
import com.uinnova.product.vmdb.comm.model.ci.CcCi;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.comm.model.ci.CcCiClass;
import com.uinnova.product.vmdb.comm.model.ci.CcFixAttrMapping;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

public class CcCiInfo implements CiAuthable, Serializable {
    private static final long serialVersionUID = 1L;

    @Comment("CI对象")
    private CcCi ci;

    @Comment("CI属性")
    private Map<String, String> attrs;

    @Comment("当前CI所属分类")
    private CcCiClass ciClass;

    @Comment("常柱属性映射对象")
    private CcFixAttrMapping fixMapping;

    @Comment("当前CI属性定义")
    private List<CcCiAttrDef> attrDefs;

    @Comment("权限类型[AUTH_TYPE]    权限类型:3位二进制数表示 第1位=查询 第2位=修改 第3位=删除")
    private Integer authType;

    @Comment("看的权限")
    private Boolean see;

    @Comment("编辑的权限")
    private Boolean edit;

    @Comment("删除的权限")
    private Boolean delete;

    @Comment("文件目录id")
    private Long dirId;

    public CcCi getCi() {
        return ci;
    }

    public void setCi(CcCi ci) {
        this.ci = ci;
    }

    public Map<String, String> getAttrs() {
        return attrs;
    }

    public void setAttrs(Map<String, String> attrs) {
        this.attrs = attrs;
    }

    public CcCiClass getCiClass() {
        return ciClass;
    }

    public void setCiClass(CcCiClass ciClass) {
        this.ciClass = ciClass;
    }

    public List<CcCiAttrDef> getAttrDefs() {
        return attrDefs;
    }

    public void setAttrDefs(List<CcCiAttrDef> attrDefs) {
        this.attrDefs = attrDefs;
    }

    public CcFixAttrMapping getFixMapping() {
        return fixMapping;
    }

    public void setFixMapping(CcFixAttrMapping fixMapping) {
        this.fixMapping = fixMapping;
    }

    public Integer getAuthType() {
        return authType;
    }

    public void setAuthType(Integer authType) {
        this.authType = authType;
        if (authType != null) {
            if (authType < 1000) {
                this.see = authType / 100 == 1;
                this.edit = authType / 10 == 11;
                this.delete = authType % 10 == 1;
            } else {
                this.see = authType / 1000 == 1;
                this.edit = authType / 100 % 10 == 1;
                this.delete = authType / 10 % 10 == 1;
            }
        }
    }

    public Boolean getSee() {
        return see;
    }

    public void setSee(Boolean see) {
        this.see = see;
    }

    public Boolean getEdit() {
        return edit;
    }

    public void setEdit(Boolean edit) {
        this.edit = edit;
    }

    public Boolean getDelete() {
        return delete;
    }

    public void setDelete(Boolean delete) {
        this.delete = delete;
    }

    public Long getDirId() {
        return dirId;
    }

    public void setDirId(Long dirId) {
        this.dirId = dirId;
    }
}
