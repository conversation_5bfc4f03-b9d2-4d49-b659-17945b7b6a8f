package com.uino.provider.feign.plugin;

import com.binary.core.io.Resource;
import com.binary.jdbc.Page;
import com.uino.bean.plugin.base.ESPluginInfo;
import com.uino.bean.plugin.query.CPluginInfo;
import com.uino.provider.feign.config.BaseFeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Component
@FeignClient(name="${feign-server-name:tarsier-platform}",path="${feign-server-path:tarsier-platform}/feign/plugin",configuration = {BaseFeignConfig.class})
public interface PluginFeign {

    /**
     * 获取所有服务
     *
     * @return
     */
    @PostMapping("getServiceList")
    List<String> getServiceList();

    /**
     * 添加插件
     *
     * @param pluginInfo
     * @return
     */
    @PostMapping("saveOrUpdate")
    Long saveOrUpdate(@RequestBody ESPluginInfo pluginInfo);

    /**
     * 上传插件
     *
     * @param file
     * @return
     */
    @PostMapping("uploadPlugin")
    String uploadPlugin(@RequestParam(value="id") Long id, @RequestPart(value="file") MultipartFile file);

    /**
     * 同步插件
     *
     * @param file
     * @return
     */
    @PostMapping("syncPlugin")
    boolean syncPlugin(@RequestPart(value="file") MultipartFile file);

    /**
     * 下载插件
     *
     * @param id
     * @return
     */
    @PostMapping("downloadPlugin")
    Resource downloadPlugin(@RequestBody Long id);


    @PostMapping("queryList")
    Page<ESPluginInfo> queryList(@RequestParam(value="pageNum") int pageNum,@RequestParam(value="pageSize") int pageSize, @RequestBody CPluginInfo cPluginInfo);

    /**
     * 加载卸载插件
     *
     * @param id
     * @return
     */
    @PostMapping("loadOrUnloadPlugin")
    boolean loadOrUnloadPlugin(@RequestBody  Long id);


    /**
     * 删除插件
     *
     * @param id
     * @return
     */
    @PostMapping("deletePlugin")
    boolean deletePlugin(@RequestBody Long id);

    /**
     * 查询服务所要加载的插件名称集合
     *
     * @param serverName
     * @return
     */
    @PostMapping("queryListByOpenServer")
    List<String> queryListByOpenServer(@RequestBody String serverName);
}
