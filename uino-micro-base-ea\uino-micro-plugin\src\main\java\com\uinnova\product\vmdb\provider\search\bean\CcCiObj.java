package com.uinnova.product.vmdb.provider.search.bean;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.vmdb.comm.bean.CiAuthable;
import com.uinnova.product.vmdb.comm.model.ci.CcCi;

import java.io.Serializable;
import java.util.Map;

public class CcCiObj implements CiAuthable, Serializable {
    private static final long serialVersionUID = 1L;

    @Comment("CI对象")
    private CcCi ci;

    @Comment("CI属性")
    private Map<String, String> attrs;

    @Comment("权限类型[AUTH_TYPE]    权限类型:3位二进制数表示 第1位=查询 第2位=修改 第3位=删除")
    private Integer authType;

    @Comment("看的权限")
    private Boolean see;

    @Comment("添加的权限")
    private Boolean add;

    @Comment("编辑的权限")
    private Boolean edit;

    @Comment("删除的权限")
    private Boolean delete;

    public CcCi getCi() {
        return ci;
    }

    public void setCi(CcCi ci) {
        this.ci = ci;
    }

    public Map<String, String> getAttrs() {
        return attrs;
    }

    public void setAttrs(Map<String, String> attrs) {
        this.attrs = attrs;
    }

    public Integer getAuthType() {
        return authType;
    }

    public void setAuthType(Integer authType) {
        this.authType = authType;
        if (authType != null) {
            if (authType < 1000) {
                this.see = authType / 100 == 1;
                this.edit = authType / 10 == 11;
                this.delete = authType % 10 == 1;
                this.add = false;
            } else {
                this.see = authType / 1000 == 1;
                this.edit = authType / 100 % 10 == 1;
                this.delete = authType / 10 % 10 == 1;
                this.add = authType % 10 == 1;
            }
        }
    }

    public Boolean getSee() {
        return see;
    }

    public void setSee(Boolean see) {
        this.see = see;
    }

    public Boolean getAdd() {
        return add;
    }

    public void setAdd(Boolean add) {
        this.add = add;
    }

    public Boolean getEdit() {
        return edit;
    }

    public void setEdit(Boolean edit) {
        this.edit = edit;
    }

    public Boolean getDelete() {
        return delete;
    }

    public void setDelete(Boolean delete) {
        this.delete = delete;
    }

}
