package com.uinnova.product.eam.service.es;

import com.uinnova.product.eam.comm.model.es.CEamOpCounter;
import com.uinnova.product.eam.comm.model.es.EamOpCounter;
import com.uino.dao.AbstractESBaseDao;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;

/**
 * EAM 资源文件
 * <AUTHOR>
 */
@Service
public class IamsEamESOpCounterDao extends AbstractESBaseDao<EamOpCounter, CEamOpCounter> {

    Log logger = LogFactory.getLog(IamsEamESOpCounterDao.class);

    @Override
    public String getIndex() {
        return "uino_eam_op_counter";
    }

    @Override
    public String getType() {
        return getIndex();
    }

    @PostConstruct
    public void init(){
        super.initIndex();
    }
}
