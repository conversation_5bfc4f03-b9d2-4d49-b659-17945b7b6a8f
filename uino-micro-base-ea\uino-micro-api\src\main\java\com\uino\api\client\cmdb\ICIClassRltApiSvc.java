package com.uino.api.client.cmdb;

import java.util.List;

import com.binary.jdbc.Page;
import com.uino.bean.cmdb.base.ESCiClassRlt;
import com.uino.bean.cmdb.business.BindCIClassRltDto;
import com.uino.bean.cmdb.business.ClassRltQueryDto;

public interface ICIClassRltApiSvc {
	
	/**
     * 分页查询分类间关系
     * 
     * @param queryDto
     *            查询条件
     * @return 分页查询结果
     */
    public Page<ESCiClassRlt> queryClassRltPage(ClassRltQueryDto queryDto);

    /**
     * 不分页查询分类间关系
     * 
     * @param queryDto
     *            查询条件
     * @return 查询结果
     */
    public List<ESCiClassRlt> queryClassRlt(ClassRltQueryDto queryDto);

    /**
     * 判定关系是否存在
     * 
     * @param sourceClsId
     *            源分类id
     * @param targetClsId
     *            目标分类id
     * @param clsId
     *            关系分类id
     * @param onlyCurrentModel
     *            是否仅查看当前生效模型的关系(暂时不生效，目前只存储了当前)
     * @return true:存在false：不存在
     */
    public boolean existRlt(long sourceClsId, long targetClsId, long clsId, boolean onlyCurrentModel);

	/**
	 * 保存分类关系
	 * 
	 * @param classRlts
	 * @return
	 */
	public Integer saveRlts(List<ESCiClassRlt> classRlts);
	public Integer saveRlts(Long domainId, List<ESCiClassRlt> classRlts);

	/**
	 * 为指定的源分类保存分类关系-全量 <br>
	 * 源分类的所有分类关系会先清除再保存
	 * 
	 * @param dto
	 * @return
	 */
	public Integer saveRltsForClass(BindCIClassRltDto dto);

	/**
	 * 刪除分类关系（classId可传关系分类id/源分类id/目标分类id）
	 * 
	 * @return
	 */
	public Integer deleteRltsByClassId(Long classId);

	/**
	 * 删除分类关系
	 * 
	 * @param rlt
	 * @return
	 */
	public Integer deleteClassRlt(ESCiClassRlt rlt);
}
