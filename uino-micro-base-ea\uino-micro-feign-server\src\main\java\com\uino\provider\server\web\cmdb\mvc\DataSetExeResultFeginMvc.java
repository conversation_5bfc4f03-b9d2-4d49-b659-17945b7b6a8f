package com.uino.provider.server.web.cmdb.mvc;

import com.uino.service.cmdb.dataset.microservice.IDataSetExeResultSvc;
import com.uino.bean.cmdb.base.dataset.batch.DataSetExeResultSheet;
import com.uino.provider.feign.cmdb.DataSetExeResultFegin;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Title: DataSetExeResultFeginMvc
 * @Description:
 * @Author: YGQ
 * @Create: 2021-05-31 17:13
 **/
@RestController
@RequestMapping("feign/cmdb/dataset/result")
public class DataSetExeResultFeginMvc implements DataSetExeResultFegin {

    private final IDataSetExeResultSvc iDataSetExeResultSvc;

    @Autowired
    public DataSetExeResultFeginMvc(IDataSetExeResultSvc iDataSetExeResultSvc) {
        this.iDataSetExeResultSvc = iDataSetExeResultSvc;
    }

    @Override
    public void saveOrUpdateBatch(List<DataSetExeResultSheet> dataSetExeResultSheets) {
        iDataSetExeResultSvc.saveOrUpdateBatch(dataSetExeResultSheets);
    }

    @Override
    public void deleteByDataSetId(Long dataSetId) {
        iDataSetExeResultSvc.deleteByQuery(dataSetId);
    }
}
