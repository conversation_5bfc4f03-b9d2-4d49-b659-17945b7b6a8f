package com.uinnova.product.eam.model.dto;

import com.binary.framework.bean.annotation.Comment;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 版本标签
 * <AUTHOR>
 */
@Data
public class EamVersionTagDto {
    @Comment("主键id")
    private Long id;

    @Comment("关联的业务分支id")
    private Long branchId;

    @Comment("关联的业务分支名称")
    private String branchName;

    @Comment("版本号")
    private String tagName;

    @Comment("版本说明")
    private String message;

    @Comment("是否删除：0删除,1正常")
    private Integer dataStatus;

    @Comment("创建人")
    private String creator;

    @Comment("打标签日期")
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd")
    private Date date;
}
