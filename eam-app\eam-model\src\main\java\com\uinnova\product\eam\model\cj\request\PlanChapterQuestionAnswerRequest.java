package com.uinnova.product.eam.model.cj.request;

import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 方案设计分页查询请求参数
 * <AUTHOR>
 */
@Data
public class PlanChapterQuestionAnswerRequest implements Serializable{

    /** 问题主键 */
    private Long id;

    /** 状态 0:待整改 1:已整改 2:无需整改 3:例外问题*/
    private Integer state;

    /** 验证结果 0:待验证 1:通过 2:不通过 */
    private Integer check;

    /** 回复内容 */
    private String answer;
}
