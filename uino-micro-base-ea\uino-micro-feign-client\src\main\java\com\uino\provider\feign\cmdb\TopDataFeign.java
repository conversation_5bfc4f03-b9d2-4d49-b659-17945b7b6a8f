package com.uino.provider.feign.cmdb;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import com.uino.provider.feign.config.BaseFeignConfig;

@Component
@FeignClient(name = "${feign-server-name:tarsier-platform}", path = "${feign-server-path:tarsier-platform}/feign/topData", configuration = {
        BaseFeignConfig.class})
public interface TopDataFeign {

    /**
     * 取消置顶
     * 
     * @param topData
     *            取消置顶的数据
     * @param topDataType
     *            取消置顶的数据类型3：图标
     */
    @PostMapping(value = "unTop")
    public void unTop(@RequestParam(required = false, value = "domainId") Long domainId,
            @RequestParam(required = false, value = "topData") Long topData,
            @RequestParam(required = false, value = "topDataType") Long topDataType);

    /**
     * 置顶数据
     * 
     * @param topData
     * @param topDataType
     */
    @PostMapping(value = "top")
    public void top(@RequestParam(required = false, value = "doaminId") Long domainId,
            @RequestParam(required = false, value = "topData") Long topData,
            @RequestParam(required = false, value = "topDataType") Long topDataType);
}
