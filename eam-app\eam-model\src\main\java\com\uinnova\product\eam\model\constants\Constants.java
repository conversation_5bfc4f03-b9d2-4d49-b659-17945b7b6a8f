package com.uinnova.product.eam.model.constants;

/**
 * @description: 常量
 * @author: Lc
 * @create: 2022-01-14 13:37
 */
public class Constants {
    /**
     * 模板章节新增类型
     */
    public static final Integer CJ_DIR_TYPE = 11;
    /**
     * 业务架构设计-流程建模
     */
    public static final Integer PROCESS_MODEL = 10;
    /**
     * 业务架构设计-组件建模
     */
    public static final Integer COMPONENT_MODEL = 100;

    /**
     * 业务架构设计-组件建模
     */
    public static final Integer DATA_BUILD = 101;

    /**
     * 模板章节新增类型
     */
    public static final Integer TEMPLATE_CHAPTER_ADD_TYPE = 1;
    /**
     * 模板章节默认排序号
     */
    public static final Double TEMPLATE_CHAPTER_SORT_NUM = 1D;
    /** 模板章节状态 */
    public static final Integer TEMPLATE_CHAPTER_STATUS= 1;
    /** 模板章节默认必填 */
    public static final String TEMPLATE_CHAPTER_REQUIRED= "1";
    /** 模板章节默认名称 */
    public static final String TEMPLATE_CHAPTER_NAME= "自定义章节";
    /** 模板章节自行添加内容快默认值 */
    public static final String USER_ADD_CONTENT = "0";
    /** 模板章节默认父id */
    public static final Long TEMPLATE_CHAPTER_PARENT_ID = 0L;
    /** 模板章节默认层级 */
    public static final Integer TEMPLATE_CHAPTER_LEVEL = 1;
    /** 模板章节默认排序号 */
    public static final String TEMPLATE_SERIAL_NUM = "1";

    public static final String COPY_CONTENT = "copy";

    /**
     * 删除方案章节失败提示语
     */
    public static final String DEL_PLAN_CHAPTER_FAIL_MSG = "该章节为必填章节，不可删除";

    /** 方案删除状态 */
    public static final Integer DELETED_STATUS = 0;
    /** 方案未状态 */
    public static final Integer NO_RELEASE_STATUS = 1;
    /**
     * 方案已状态
     */
    public static final Integer IS_RELEASE_STATUS = 2;

    /**
     * 制品
     */
    public static final Integer DIAGRAM = 1;
    /**
     * 方案
     */
    public static final Integer PLAN = 2;

    public static final Integer CI = 3;
    public static final Integer MATRIX = 5;
    /**
     * 方案module不存在
     */
    public static final String PLAN_MODULE_NOT_FOUND = "章节内容块不存在";
    /**
     * 方案不存在
     */
    public static final String PLAN_NOT_FOUND = "方案不存在";

    public static final String SLASH = "/";

    /** 调用feign接口异常 */
    public static final String INVOKE_FEIGN_EXCEPTION = "调用feign接口异常";
    /** 发起流程 默认开启一级审批 */
    public static final String START_APPROVE = "架构评审";

    /** 回收站操作错误提示 */
    public static final String RECYCLE_BIN_OPT_UNSUPPORTED = "不支持的操作";

    /** 方案重命名失败：id必填 */
    public static final String PLAN_RENAME_FAILED_ID_REQUIRED = "方案重命名失败：id必填";
    /** 方案重命名失败：新名称必填 */
    public static final String PLAN_RENAME_FAILED_NEW_NAME_REQUIRED = "方案重命名失败：新名称必填";
    /** 批量移动失败 目标文件夹为空 */
    public static final String BATCH_MOVE_FAILED_TARGET_DIR_IS_NULL = "移动失败, 目标文件夹不能为空";

    /** 提交审批 */
    public static final Integer PLAN_APPROVAL = 1;
    public static final Integer QUESTION_APPROVAL = 2;
    public static final Integer ACTIVE_APPROVAL = 3;
    public static final Integer MATRIX_APPROVAL = 2;

    /** 方案再流程中是否可以编辑sign */
    public static final String PROCESS_EDIT_SIGN = "rectification";

    /** 中线 */
    public static final String CENTRE_LINE = "-";

    /** 数据表标识 */
    public static final String DATA_SIGN = "data";

    public static final String DUPLICATE_NAME_PLAN_RESTORE_FAILED_FORMAT = "因为名称重复部分方案还原失败, 还原失败的方案: %s";

    public static final String NO_SHARING_PERMISSION = "没有分享权限";
    /** 例外文件夹id */
    public static final Long EXCEPTION_FOLDER_ID = 1000L;
    /** 未在流程中 */
    public static final Integer FLOW_STATUS = 0;
    /** 锁定章节 */
    public static final String LOCK_CHAPTER_SET = "lock:chapter:set:";
    /** 锁定章节 */
    public static final String BEFORE_APPROVAL_PLAN = "before:approval:plan:";

    public static final String COLON = ":";

    public static final Integer HISTORICAL_VERSION = 0;
    public static final Integer DRAFT = 1;
    public static final Integer RELEASE = 2;

    public static final Integer DESIGN = 1;
    public static final Integer ASSETS = 2;

    // 增删改标识
    public static final Integer DELETE_SIGN = 0;
    public static final Integer ADD_SIGN = 1;
    public static final Integer UPDATE_SIGN = 2;

    public static final String TEMPLATE_ID_STRING ="templateId";

    public static final String MODIFY_TIME_ASC = "MODIFY_TIME ASC";
    public static final String MODIFY_TIME_DESC = "MODIFY_TIME DESC";
    public static final String CREATE_TIME_ASC = "CREATE_TIME ASC";
    public static final String CREATE_TIME_DESC = "CREATE_TIME DESC";

    // 仓颉文件夹类型
    public static final Integer DIR_TYPE = 11;
    // 发布后的视图
    public static final Integer IS_OPEN = 1;

    public static final Integer DELETE_LOGICAL = 1;

    public static final Integer DELETE_PHYSICAL = 2;
    public static final String ERROR_MATRIX_DATA_NULL = "复制错误：数据缺失或未初始化矩阵表格";
    public static final String ERROR_MATRIX_INSTANCE_NULL = "复制错误：未查询到矩阵数据";
    public static final String COPY_SUFFIX = "(副本)";

}
