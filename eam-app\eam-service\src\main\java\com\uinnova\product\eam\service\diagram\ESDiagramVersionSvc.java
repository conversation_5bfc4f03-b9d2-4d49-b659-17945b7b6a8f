package com.uinnova.product.eam.service.diagram;


import com.uinnova.product.eam.base.diagram.model.ESSimpleDiagram;
import com.uinnova.product.eam.base.diagram.model.VcDiagramVersion;
import com.uino.bean.permission.base.SysUser;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 视图历史记录操作svc
 * @Date 11:13 2021/7/7
 **/
public interface ESDiagramVersionSvc {
	/**
	 * <AUTHOR>
	 * @Description 根据id查询历史视图全量详细信息
	 * @Date 13:47 2021/7/2
	 * @Param [id]
	 * @return com.uinnova.project.base.diagram.comm.model.VcDiagramVersion
	 **/
    VcDiagramVersion queryDiagramVersionById(Long id);

    /**
     * <AUTHOR>
     * @Description 还原历史版本
     * @Date 13:48 2021/7/2
     * @Param [currDiagramId 当前视图id, historyVersionId 历史视图id]
     * @return java.lang.Long
     **/
    Long restoreDiagramByVersionId(Long currDiagramId, Long historyVersionId);

    /**
     * <AUTHOR>
     * @Description 通过当前版本新建视图
     * @Date 13:49 2021/7/2
     * @Param [diagramId]
     * @return java.lang.Long
     **/
    Long createDiagramByCurrVersion(Long diagramId, boolean versionFlag);

    /**
     * <AUTHOR>
     * @Description //保存当前视图的快照
     * @Date 10:33 2021/7/28
     * @Param [simpleDiagram]
     * @return java.lang.Long
     **/
    Boolean saveESDiagramVersion(ESSimpleDiagram simpleDiagram, SysUser currUser, SysUser lastModifier);

    /**
     *  创建当前（未公开）视图快照
     * @param presetIdMap <当前视图ID, 提前预设快照视图的ID>
     * @return
     */
    void createSnapshotByCurrVersion(Map<Long, Long> presetIdMap, Map<String, String> idAndNameMap, SysUser lastModifier, Boolean isAuto);

    /**
     *  根据主视图ID 获取版本信息
     * @param mainDiagramId
     * @return
     */
    List<VcDiagramVersion> queryDiagramVersionByMainId(Long mainDiagramId);

}
