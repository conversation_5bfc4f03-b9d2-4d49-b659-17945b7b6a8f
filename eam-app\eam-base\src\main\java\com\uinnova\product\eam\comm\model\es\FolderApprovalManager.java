package com.uinnova.product.eam.comm.model.es;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 文件夹审批用户配置
 * <AUTHOR>
 */
@Data
@Comment("[uino_eam_folder_approval_manager]")
public class FolderApprovalManager implements Serializable {

    @Comment("主键")
    private Long id;
    @Comment("目录id")
    private Long dirId;
    @Comment("领域id")
    private Long domainId;
    @Comment("审批规则：或签0")
    private Integer rule;
    @Comment("用户信息")
    private List<String> users;
    @Comment("创建人")
    private String creator;
    @Comment("创建时间")
    private Long createTime;
    @Comment("修改人")
    private String modifier;
    @Comment("修改时间")
    private Long modifyTime;

}
