package com.uino.provider.server.web.cmdb.mvc;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.binary.core.io.Resource;
import com.binary.jdbc.Page;
import com.google.common.collect.Sets;
import com.uinnova.product.vmdb.provider.rlt.bean.CcCiRltInfo;
import com.uino.bean.cmdb.base.ESCIRltInfo;
import com.uino.bean.cmdb.base.ESCIRltInfoHistory;
import com.uino.bean.cmdb.business.BindCiRltRequestDto;
import com.uino.bean.cmdb.business.DataModuleRltClassDto;
import com.uino.bean.cmdb.business.ImportExcelMessage;
import com.uino.bean.cmdb.business.ImportResultMessage;
import com.uino.bean.cmdb.query.ESAttrAggBean;
import com.uino.bean.cmdb.query.ESRltSearchBean;
import com.uino.provider.feign.cmdb.CIRltFeign;
import com.uino.service.cmdb.microservice.impl.CIRltSvc;
import com.uino.util.sys.SysUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("feign/ciRlt")
@Slf4j
public class CIRltFeignMvc implements CIRltFeign {

    @Autowired
    private CIRltSvc ciRltSvc;

    @Override
    public Long bindCiRlt(BindCiRltRequestDto bindCiRltRequestDto) {
        // TODO Auto-generated method stub
        return ciRltSvc.bindCiRlt(bindCiRltRequestDto);
    }

    @Override
    public ImportResultMessage bindCiRlts(Long domainId, JSONObject jsonObject) {
        List<BindCiRltRequestDto> bindCiRltRequestDtoList = JSONArray.parseArray(jsonObject.getString("bindRltDtos"),
                BindCiRltRequestDto.class);
        Boolean repeat = jsonObject.getBoolean("repeat");
        ImportResultMessage importResultMessage = ciRltSvc.bindCiRlts(domainId, new HashSet<>(bindCiRltRequestDtoList), repeat);
        return importResultMessage;
    }

    @Override
    public Integer delRltByCiId(Long ciId) {
        // TODO Auto-generated method stub
        return ciRltSvc.delRltByCiId(ciId);
    }

    @Override
    public Long updateCiRltAttr(Long ciRltId, Map<String, String> attrs) {
        // TODO Auto-generated method stub
        return ciRltSvc.updateCiRltAttr(ciRltId, attrs);
    }

    @Override
    public Page<CcCiRltInfo> searchRltByBean(ESRltSearchBean bean) {
        // TODO Auto-generated method stub
        return ciRltSvc.searchRltByBean(bean);
    }

    @Override
    public Integer clearRltByClassId(Long rltClassId) {
        // TODO Auto-generated method stub
        return ciRltSvc.clearRltByClassId(rltClassId);
    }

    @Override
    public ImportResultMessage importCiRlt(Long domainId, String excelFilePath, MultipartFile excelFile, Set<String> rltClsCodes) {
        // TODO Auto-generated method stub
        return ciRltSvc.importCiRlt(domainId, excelFilePath, excelFile, rltClsCodes);
    }

    @Override
    public Integer delRltByIdsOrRltCodes(Set<Long> rltIds, Set<String> rltCodes) {
        // TODO Auto-generated method stub
        return ciRltSvc.delRltByIdsOrRltCodes(rltIds, rltCodes, null);
    }

    @Override
    public Map<String, Boolean> comprehendRltExcel(MultipartFile excelFile) {
        // TODO Auto-generated method stub
        return ciRltSvc.comprehendRltExcel(excelFile);
    }

    @Override
    public ImportExcelMessage parseRltExcel(String excelFilePath, MultipartFile excelFile) {
        // TODO Auto-generated method stub
        return ciRltSvc.parseRltExcel(excelFilePath, excelFile);
    }

    @Override
    public ImportResultMessage bindCiRlts(Long domainId, Set<BindCiRltRequestDto> bindRltDtos) {
        return ciRltSvc.bindCiRlts(domainId, bindRltDtos);
    }

    @Override
    public List<CcCiRltInfo> searchRltByIds(Set<Long> ids) {
        // TODO Auto-generated method stub
        return ciRltSvc.searchRltByIds(ids);
    }

    @Override
    public Page<ESCIRltInfo> searchRlt(ESRltSearchBean bean) {
        // TODO Auto-generated method stub
        return ciRltSvc.searchRlt(bean);
    }

    @Override
    public Page<String> groupByField(Long domainId, ESAttrAggBean req) {
        // TODO Auto-generated method stub
        return ciRltSvc.groupByField(domainId, req);
    }

    @Override
    public Map<Long, Map<Long, Set<Long>>> getClassRltMapByClsQuery(JSONObject req) {
        // TODO Auto-generated method stub
        List<Long> clsIds = req.getJSONArray("clsIds").toJavaList(Long.class);
        List<Long> rltClsIds = req.getJSONArray("rltClsIds").toJavaList(Long.class);
        return ciRltSvc.getClassRltMapByClsQuery(new HashSet<>(clsIds), new HashSet<>(rltClsIds));
    }

    @Override
    public ResponseEntity<byte[]> exportCiRlt(String req) {
        JSONObject reqObj = JSON.parseObject(req);
        List<Long> clsIds = reqObj.getJSONArray("clsIds").toJavaList(Long.class);
        List<Long> rltIds = reqObj.getJSONArray("rltIds").toJavaList(Long.class);
        ESRltSearchBean bean = new ESRltSearchBean();
        bean.setRltClassIds(clsIds);
        bean.setRltId(Sets.newHashSet(rltIds));
        bean.setOwnerCode(SysUtil.getCurrentUserInfo().getLoginCode());
        Resource excel = ciRltSvc.exportCiRlt(bean);
        try {
            byte[] bytes = IOUtils.toByteArray(excel.getInputStream());
            return ResponseEntity.ok(bytes);
        } catch (IOException e) {
            throw  new RuntimeException(e);
        }

    }

    @Override
    public List<DataModuleRltClassDto> getClassRltList(Set<Long> classIds, Set<Long> rltClsIds) {
        return ciRltSvc.getClassRltList(classIds, rltClsIds);
    }

    @Override
    public Map<Long, List<ESCIRltInfoHistory>> getRltsHistrysDict(Set<Long> rltIds, boolean hasCurrent) {
        return ciRltSvc.getRltsHistrysDict(rltIds, hasCurrent);
    }

    @Override
    public Map<Long, Long> getRltIdMaxVersion(Set<Long> rltIds) {
        return ciRltSvc.getRltIdMaxVersion(rltIds);
    }

}
