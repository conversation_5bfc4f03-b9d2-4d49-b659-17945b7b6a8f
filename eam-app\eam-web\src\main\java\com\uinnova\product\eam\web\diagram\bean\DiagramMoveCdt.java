package com.uinnova.product.eam.web.diagram.bean;

import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;

public class DiagramMoveCdt implements Condition {

	private static final long serialVersionUID = 1L;

	@Comment("视图的新的名字")
	private String newName;
	@Comment("视图的新的dir")
	private Long newDirId;
	@Comment("视图的id")
	private Long diagramId;

	public String getNewName() {
		return newName;
	}

	public void setNewName(String newName) {
		this.newName = newName;
	}

	public Long getNewDirId() {
		return newDirId;
	}

	public void setNewDirId(Long newDirId) {
		this.newDirId = newDirId;
	}

	public Long getDiagramId() {
		return diagramId;
	}

	public void setDiagramId(Long diagramId) {
		this.diagramId = diagramId;
	}

}
