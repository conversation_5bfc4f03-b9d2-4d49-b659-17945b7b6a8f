package com.uino.provider.server.web.cmdb.mvc;

import com.binary.jdbc.Page;
import com.uino.service.cmdb.microservice.ICiRltAutoBuildSvc;
import com.uino.bean.cmdb.base.ESCiRltAutoBuild;
import com.uino.bean.cmdb.query.ESCiRltAutoBuildSearchBean;
import com.uino.provider.feign.cmdb.CiRltAutoBuildFeign;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Classname CiRltAutoBuildFeignMvc
 * @Description TODO
 * @Date 2020/6/30 15:37
 * <AUTHOR> sh
 */
@RestController
@RequestMapping("feign/ciRltAutoBuild")
public class CiRltAutoBuildFeignMvc implements CiRltAutoBuildFeign {

    @Autowired
    private ICiRltAutoBuildSvc iCiRltAutoBuildSvc;

    @Override
    public ESCiRltAutoBuild saveDefault(Long domainId, Long sourceCiClassId, Long targetCiClassId, Long rltClassId, Long visualModelId) {
        return iCiRltAutoBuildSvc.saveDefault(domainId, sourceCiClassId, targetCiClassId, rltClassId, visualModelId);
    }

    @Override
    public Long updateCiRltAutoBuild(ESCiRltAutoBuild esCiRltAutoBuild) {
        return iCiRltAutoBuildSvc.updateCiRltAutoBuild(esCiRltAutoBuild);
    }

    @Override
    public ESCiRltAutoBuild findCiRltAutoBuild(Long domainId, Long sourceCiClassId, Long targetCiClassId, Long rltClassId, Long visualModelId) {
        return iCiRltAutoBuildSvc.findCiRltAutoBuild(domainId, sourceCiClassId, targetCiClassId, rltClassId, visualModelId);
    }

    @Override
    public void deleteCiRltAutoBuild(Long sourceCiClassId, Long targetCiClassId, Long rltClassId, Long visualModelId) {
        iCiRltAutoBuildSvc.deleteCiRltAutoBuild(sourceCiClassId, targetCiClassId, rltClassId, visualModelId);
    }

    @Override
    public void runCiRltAutoBuildAll(Long domainId) {
        iCiRltAutoBuildSvc.runCiRltAutoBuildAll(domainId);
    }

    @Override
    public boolean runCiRltAutoBuildById(Long id) {
        return iCiRltAutoBuildSvc.runCiRltAutoBuildById(id);
    }

    @Override
    public Long saveCiRltAutoBuild(ESCiRltAutoBuild esCiRltAutoBuild) {
        return iCiRltAutoBuildSvc.saveCiRltAutoBuild(esCiRltAutoBuild);
    }

    @Override
    public Page<ESCiRltAutoBuild> queryCiRltAutoBuildPage(ESCiRltAutoBuildSearchBean searchBean) {
        return iCiRltAutoBuildSvc.queryCiRltAutoBuildPage(searchBean);
    }

    @Override
    public Integer deleteById(Long id) {
        return iCiRltAutoBuildSvc.deleteById(id);
    }

    @Override
    public Long saveOrUpdate(ESCiRltAutoBuild esCiRltAutoBuild) {
        return iCiRltAutoBuildSvc.saveOrUpdate(esCiRltAutoBuild);
    }
}
