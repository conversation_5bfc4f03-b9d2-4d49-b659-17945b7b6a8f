package com.uino.bean.sys.business;

import java.io.Serializable;
import java.util.LinkedList;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 
 * <AUTHOR>
 *
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value="更新日志信息类",description = "更新日志信息")
public class SysUpdateLogDto implements Serializable {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value="分类名称",example = "sport")
	private String className;

	@ApiModelProperty(value="日志信息")
	@Builder.Default
	private List<Info> infos = new LinkedList<>();

	@Builder
	@Data
	@NoArgsConstructor
	@AllArgsConstructor

	@ApiModel(value="日志信息详情",description = "日志信息详情")
	public static class Info {
		/**
		 * 修改类型 null:普通修改 0新增 1修复 2优化 3移除
		 */
		@ApiModelProperty(value="修改类型,null:普通修改 0新增 1修复 2优化 3移除",example = "1")
		private Integer type;
		/**
		 * 内容
		 */
		@ApiModelProperty(value="内容",example = "mole")
		private String content;
	}
}
