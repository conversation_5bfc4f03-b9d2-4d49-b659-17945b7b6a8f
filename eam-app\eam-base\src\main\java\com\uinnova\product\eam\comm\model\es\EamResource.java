package com.uinnova.product.eam.comm.model.es;




import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;
import lombok.Data;


@Comment("资源表[UINO_EAM_RESOURCE]")
@Data
public class EamResource implements EntityBean {

	private Long id;

	private String name;

	@Comment("业务类型：1=制品示例图 2=品牌logo图")
	private Integer type;

	private String resType;

	private String resPath;

	private String operator;

	private Long createTime;

	private Long modifyTime;

}


