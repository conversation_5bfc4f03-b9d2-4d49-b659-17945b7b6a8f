package com.uino.bean.tp.query;

import com.uino.bean.tp.enums.TpRuleTypeEnum;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value="标签查询条件类",description = "标签查询条件")
public class TpRuleReqDTO {
    /**
     * 规则类型 查询全部规则传空值
     */
    @ApiModelProperty(value="规则类型")
    private TpRuleTypeEnum ruleType;
    /**
     * 搜索信息 可以搜索任务名称或者指标名称
     */
    @ApiModelProperty(value="搜索信息")
    private String searchText;

    @ApiModelProperty(value="分类id",example = "123")
    private Long classId;

    @ApiModelProperty(value="所属域",example = "123")
    private Long domainId;

}
