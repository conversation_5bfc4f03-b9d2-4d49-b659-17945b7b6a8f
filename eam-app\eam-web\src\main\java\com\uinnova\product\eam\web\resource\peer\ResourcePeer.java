package com.uinnova.product.eam.web.resource.peer;

import com.uinnova.product.eam.api.IResourceAPIClient;
import com.uinnova.product.eam.base.model.FileResourceMeta;
import com.uinnova.product.eam.base.model.ResourceInfo;
import com.uinnova.product.eam.model.vo.DefaultFileVo;
import com.uino.bean.permission.base.SysUser;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;
import java.util.List;

@Service
public class ResourcePeer {
    @Resource
    private IResourceAPIClient resourceAPIClient;

    public List<ResourceInfo> upload(MultipartFile[] files, SysUser sysUser){
        return resourceAPIClient.upload(files, sysUser);
    }

    public List<FileResourceMeta> download(List<Long> ids) {
        return resourceAPIClient.download(ids);
    }

    /**
     * 上传文件区分业务类型
     * @param files 文件
     * @param type 业务类型
     * @return 资源信息
     */
    public List<ResourceInfo> uploadFileByType(MultipartFile[] files, Integer type) {
        return resourceAPIClient.uploadFileByType(files, type);
    }

    /**
     * 删除资源
     * @param id 资源id
     * @return 删除结果
     */
    public Integer deleteResource(Long id) {
        return resourceAPIClient.deleteResource(id);
    }

    public List<DefaultFileVo> getImages(Integer type) {
        return resourceAPIClient.getImages(type);
    }
}
