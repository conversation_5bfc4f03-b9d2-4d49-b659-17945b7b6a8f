package com.uino.provider.server.web.sys.mvc;

import com.uino.bean.sys.base.ESOperateLogModule;
import com.uino.provider.feign.sys.OperateLogModuleFeign;
import com.uino.service.sys.microservice.IOperateLogModuleSvc;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("feign/sys/opLogModule")
public class OperateLogModuleFeignMvc implements OperateLogModuleFeign {

    @Autowired
    private IOperateLogModuleSvc opLogModuleSvc;

    @Override
    public List<ESOperateLogModule> getAll() {
        return opLogModuleSvc.getAll();
    }

    @Override
    public ESOperateLogModule getModuleInfoByMvc(String mvcPath) {
        return opLogModuleSvc.getModuleInfoByMvc(mvcPath);
    }
}
