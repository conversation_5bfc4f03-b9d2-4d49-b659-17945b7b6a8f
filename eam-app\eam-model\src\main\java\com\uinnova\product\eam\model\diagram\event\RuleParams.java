package com.uinnova.product.eam.model.diagram.event;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.util.List;

@Data
public class RuleParams {

    private Long diagramId;
    private String sheetId;
    private String ciCode;
    private String ownerCode;
    private Long domainId;
    private DiagramRuleEnum ruleType;
    private List<String> expectKeys;
    private String srcCiCode;
    private String targetCiCode;

    private JSONObject config;

}
