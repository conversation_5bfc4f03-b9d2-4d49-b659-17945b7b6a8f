# 是否开启配置中心
spring.cloud.nacos.config.enabled=false
# 是否开启服务注册调用
spring.cloud.nacos.discovery.enabled=false
# 注册中心地址
spring.cloud.nacos.config.server-addr=192.168.21.223:8848
# 是否开启流量监控
spring.cloud.sentinel.enabled=false
# sentinel服务地址
spring.cloud.sentinel.transport.dashboard=192.168.21.223:8145
# sentinel回调本地端口
spring.cloud.sentinel.transport.port=8235

# 主配置文件
spring.cloud.nacos.config.extension-configs[0].data-id=common-web-first.properties
spring.cloud.nacos.config.extension-configs[0].group=common
spring.cloud.nacos.config.extension-configs[0].refresh=true
# 共享配置文件
spring.cloud.nacos.config.extension-configs[1].data-id=es-config.properties
spring.cloud.nacos.config.extension-configs[1].group=common
spring.cloud.nacos.config.extension-configs[1].refresh=true

spring.cloud.nacos.config.extension-configs[2].data-id=common-web-basic.properties
spring.cloud.nacos.config.extension-configs[2].group=common
spring.cloud.nacos.config.extension-configs[2].refresh=true
spring.application.name=common-web
spring.cloud.nacos.config.group=middle-platform
spring.cloud.nacos.discovery.group=middle-platform
spring.cloud.nacos.discovery.server-addr=${spring.cloud.nacos.config.server-addr}



