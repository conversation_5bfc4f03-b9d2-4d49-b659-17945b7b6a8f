package com.uinnova.product.eam.db.diagram.es;

import com.binary.core.util.BinaryUtils;
import com.uinnova.product.eam.base.diagram.model.ESDiagramLink;
import com.uinnova.product.eam.base.diagram.model.ESDiagramLinkQueryBean;
import com.uino.dao.AbstractESBaseDao;
import com.uino.service.util.FileUtil;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.stereotype.Repository;

import jakarta.annotation.PostConstruct;
import java.util.Collection;
import java.util.List;

/**
 * @Classname Es视图连线数据访问层
 * @Description 该类用来操作ES中的视图连线index
 * <AUTHOR>
 * @Date 2021-06-02-16:25
 */
@Repository
public class ESDiagramLinkDao extends AbstractESBaseDao<ESDiagramLink, ESDiagramLinkQueryBean> {
    @Override
    public String getIndex() {
        return "monet_diagram_link";
    }

    @Override
    public String getType() {
        return "_doc";
    }

    @PostConstruct
    public void init() {
        List<ESDiagramLink> list = FileUtil.getData("/initdata/uino_monet_diagram_link.json", ESDiagramLink.class);
        super.initIndex(list);
    }

    public void remove(Collection<Long> ids){
        if(BinaryUtils.isEmpty(ids)){
            return;
        }
        deleteByQuery(QueryBuilders.termsQuery("diagramId", ids),true);
    }
}
