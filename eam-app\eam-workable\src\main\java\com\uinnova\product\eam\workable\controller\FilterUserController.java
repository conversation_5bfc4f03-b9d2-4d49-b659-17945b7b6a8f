package com.uinnova.product.eam.workable.controller;

import com.uinnova.product.eam.workable.dto.FilterUserDto;
import com.uinnova.product.eam.workable.model.FilterUser;
import com.uinnova.product.eam.workable.service.FilterUserService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/filter")
public class FilterUserController {

    @Autowired
    private FilterUserService filterUserService;

    @PostMapping("/saveAgreeUser")
    public String saveAgreeUser(@RequestBody FilterUserDto filterUserDto) {
        FilterUser filterUser = new FilterUser();
        BeanUtils.copyProperties(filterUserDto, filterUser);
        return filterUserService.saveAgreeUser(filterUser);
    }

    @GetMapping("/getCurrentTaskAgreeUser")
    public List<FilterUser> getCurrentTaskAgreeUser(@RequestParam(name = "processInstanceId", required = true) String processInstanceId,
                                                    @RequestParam(name = "taskKey", required = true) String taskKey) {
        return filterUserService.getCurrentTaskAgreeUser(processInstanceId,taskKey);
    }
}
