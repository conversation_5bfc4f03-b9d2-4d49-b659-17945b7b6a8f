package com.uino.service.cmdb.dataset.microservice.impl;


import com.uinnova.product.vmdb.comm.model.ci.CCcCiClass;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uino.dao.cmdb.ESCISvc;
import com.uino.dao.cmdb.ESCmdbCommSvc;
import com.uino.service.cmdb.dataset.microservice.IGraphAnalysisBase;
import com.uino.service.cmdb.dataset.microservice.IGraphAnalysisSvc;
import com.uino.service.cmdb.dataset.microservice.IRelationRuleAnalysisSvc;
import com.uino.service.cmdb.microservice.ICIClassSvc;
import com.uino.service.cmdb.microservice.ICIRltSvc;
import com.uino.service.cmdb.microservice.ICISvc;
import com.uino.service.cmdb.microservice.IRltClassSvc;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.base.dataset.DataSetMallApiRelationRule;
import com.uino.bean.cmdb.base.dataset.DataSetMallApiRelationRuleExternal;
import com.uino.bean.cmdb.base.dataset.relation.RelationRuleAttrCdt;
import com.uino.bean.cmdb.base.dataset.relation.RelationRuleAttrCdtExternal;
import com.uino.bean.cmdb.base.dataset.relation.RelationRuleAttrCdtOp;
import com.uino.bean.cmdb.base.dataset.relation.RelationRuleLine;
import com.uino.bean.cmdb.base.dataset.relation.RelationRuleLineExternal;
import com.uino.bean.cmdb.base.dataset.relation.RelationRuleLineOp;
import com.uino.bean.cmdb.base.dataset.relation.RelationRuleNode;
import com.uino.bean.cmdb.base.dataset.relation.RelationRuleNodeExternal;
import com.uino.bean.cmdb.business.dataset.FriendInfo;
import com.uino.bean.cmdb.business.dataset.UpDownAttrCdt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Classname GraphAnalysisSvc
 * @Description TODO
 * @Date 2020/7/16 9:54
 * <AUTHOR> sh
 */
@Service
public class GraphAnalysisSvc implements IGraphAnalysisSvc {

    @Autowired
    private IGraphAnalysisBase graphAnalysisBase;
    
    @Autowired
    private IRelationRuleAnalysisSvc relationRuleAnalysisSvc;
    
    @Autowired
    private ICIClassSvc iciClassSvc;
    
    @Autowired
    private IRltClassSvc iRltClassSvc;
    
    @Autowired
    private ESCISvc esCiSvc;

    @Autowired
    private ICISvc iciSvc;

    @Autowired
    private ICIRltSvc iciRltSvc;
    
    @Autowired
    @Lazy
    private ESCmdbCommSvc commSvc;

    @Override
    public FriendInfo queryCiUpDownByCiIds(Long domainId, List<Long> startCiIds, List<UpDownAttrCdt> ciConditions, List<UpDownAttrCdt> rltConditions, List<Long> rltLvls, Integer upLevel, Integer downLevel, Boolean hasAttr) {
        return graphAnalysisBase.queryCiUpDownByCiIds(domainId, startCiIds, ciConditions, rltConditions, rltLvls, upLevel, downLevel, hasAttr, iciSvc, iciRltSvc);
    }

    @Override
    public FriendInfo queryCiUpDownByCiId(Long domainId, Long startCiId, List<UpDownAttrCdt> ciConditions, List<UpDownAttrCdt> rltConditions, List<Long> rltLvls, Integer upLevel, Integer downLevel, Boolean hasAttr) {
        return graphAnalysisBase.queryCiUpDownByCiId(domainId, startCiId, ciConditions, rltConditions, rltLvls, upLevel, downLevel, hasAttr, iciSvc, iciRltSvc);
    }

	@Override
	public FriendInfo queryFriendByCiUsingRule(List<String> enter, DataSetMallApiRelationRuleExternal rule) {
		CcCiInfo ciInfo = null;
		List<List<String>> ciPrimaryKeys = new ArrayList<List<String>>();
		ciPrimaryKeys.add(enter);
		//todo
		List<ESCIInfo> esCiInfos = esCiSvc.getCIInfoListByCIPrimaryKeys(1L,ciPrimaryKeys);
		if (esCiInfos.size()>0) {
			ESCIInfo esCiInfo = esCiInfos.get(0);
			ciInfo = commSvc.tranCcCiInfo(esCiInfo, false);
		}
		
		DataSetMallApiRelationRule friendRule = new DataSetMallApiRelationRule();
		try {
			friendRule.setDomainId(1L);
			friendRule.setPageNodeId(rule.getPageNodeId());
			Map<String, CcCiClassInfo> ciClassMap = new HashMap<String, CcCiClassInfo>();
			for (CcCiClassInfo ciClassInfo:iciClassSvc.queryCiClassInfoList(1L, new CCcCiClass(), null, true)) {
				ciClassMap.put(ciClassInfo.getCiClass().getClassName(), ciClassInfo);
			}
			Map<String, CcCiClassInfo> rltClassMap = new HashMap<String, CcCiClassInfo>();
			for (CcCiClassInfo rltClassInfo:iRltClassSvc.queryAllClasses(1L)) {
				rltClassMap.put(rltClassInfo.getCiClass().getClassName(), rltClassInfo);
			}
			Long enterClassId = null;
			List<RelationRuleNode> nodes = new ArrayList<RelationRuleNode>();
			for (RelationRuleNodeExternal nodeExternal:rule.getNodes()) {
				RelationRuleNode node = new RelationRuleNode();
				node.setPageNodeId(nodeExternal.getPageNodeId());
				node.setX(0D);
				node.setY(0D);
				if (ciClassMap.containsKey(nodeExternal.getClassName())) {
					node.setClassId(ciClassMap.get(nodeExternal.getClassName()).getCiClass().getId());
				} else {
					throw new Exception("wrong rule");
				}
				if (nodeExternal.getCdts()!=null && nodeExternal.getCdts().size()>0) {
					List<CcCiAttrDef> attrDefs = ciClassMap.get(nodeExternal.getClassName()).getAttrDefs();
					List<RelationRuleAttrCdt> cdts = new ArrayList<RelationRuleAttrCdt>();
					for (RelationRuleAttrCdtExternal cdtExternal:nodeExternal.getCdts()) {
						RelationRuleAttrCdt cdt = new RelationRuleAttrCdt();
						Long attrId = null;
						for (CcCiAttrDef attrDef:attrDefs) {
							if (attrDef.getProName().equals(cdtExternal.getAttrName())) {
								attrId = attrDef.getId();
								break;
							}
						}
						if (attrId!=null) {
							cdt.setAttrId(attrId);
						} else {
							throw new Exception("wrong rule");
						}
						if (cdtExternal.getOp().equals("equal")) {
							cdt.setOp(RelationRuleAttrCdtOp.Equal);
						} else if (cdtExternal.getOp().equals("like")) {
							cdt.setOp(RelationRuleAttrCdtOp.Like);
						} else {
							throw new Exception("wrong rule");
						}
						cdt.setValue(cdtExternal.getValue());
						cdts.add(cdt);
					}
					node.setCdts(cdts);
				}
				nodes.add(node);
				
				if (enterClassId==null) {
					if (nodeExternal.getPageNodeId()==rule.getPageNodeId()) {
						enterClassId = node.getClassId();
					}
				} else {
					if (nodeExternal.getPageNodeId()==rule.getPageNodeId()) {
						throw new Exception("wrong rule");
					}
				}
			}
			friendRule.setClassId(enterClassId);
			friendRule.setNodes(nodes);
			List<RelationRuleLine> lines = new ArrayList<RelationRuleLine>();
			for (RelationRuleLineExternal lineExternal:rule.getLines()) {
				RelationRuleLine line = new RelationRuleLine();
				line.setNodeStartId(lineExternal.getNodeStartId());
				line.setNodeEndId(lineExternal.getNodeEndId());
				line.setDirection(lineExternal.getDirection());
				if (lineExternal.getLineOp().equals("between")) {
					line.setLineOp(RelationRuleLineOp.Between);
				} else if (lineExternal.getLineOp().equals("gte")) {
					line.setLineOp(RelationRuleLineOp.Gte);
				} else if (lineExternal.getLineOp().equals("lte")) {
					line.setLineOp(RelationRuleLineOp.Lte);
				} else {
					throw new Exception("wrong rule");
				}
				line.setLineOpValue(lineExternal.getLineOpValue());
				if (rltClassMap.containsKey(lineExternal.getClassName())) {
					line.setClassId(rltClassMap.get(lineExternal.getClassName()).getCiClass().getId());
				} else {
					throw new Exception("wrong rule");
				}
				if (lineExternal.getCdts()!=null && lineExternal.getCdts().size()>0) {
					List<CcCiAttrDef> attrDefs = rltClassMap.get(lineExternal.getClassName()).getAttrDefs();
					List<RelationRuleAttrCdt> cdts = new ArrayList<RelationRuleAttrCdt>();
					for (RelationRuleAttrCdtExternal cdtExternal:lineExternal.getCdts()) {
						RelationRuleAttrCdt cdt = new RelationRuleAttrCdt();
						Long attrId = null;
						for (CcCiAttrDef attrDef:attrDefs) {
							if (attrDef.getProName().equals(cdtExternal.getAttrName())) {
								attrId = attrDef.getId();
								break;
							}
						}
						if (attrId!=null) {
							cdt.setAttrId(attrId);
						} else {
							throw new Exception("wrong rule");
						}
						if (cdtExternal.getOp().equals("equal")) {
							cdt.setOp(RelationRuleAttrCdtOp.Equal);
						} else if (cdtExternal.getOp().equals("like")) {
							cdt.setOp(RelationRuleAttrCdtOp.Like);
						} else {
							throw new Exception("wrong rule");
						}
						cdt.setValue(cdtExternal.getValue());
						cdts.add(cdt);
					}
					line.setCdts(cdts);
				}
				lines.add(line);
			}
			friendRule.setLines(lines);
		} catch (Exception e) {
			friendRule = null;
		}
		
		if (ciInfo!=null && friendRule!=null) {
			return relationRuleAnalysisSvc.queryCiFriendByCiIdAndRule(ciInfo, friendRule);
		} else {
			return null;
		}
	}
}
