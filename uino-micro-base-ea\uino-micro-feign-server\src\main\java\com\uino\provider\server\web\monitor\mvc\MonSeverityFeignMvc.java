package com.uino.provider.server.web.monitor.mvc;

import java.util.List;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.binary.core.io.Resource;
import com.uino.service.simulation.impl.MonSysSeveritySvc;
import com.uino.bean.cmdb.business.ImportResultMessage;
import com.uino.bean.monitor.base.ESMonSysSeverityInfo;
import com.uino.provider.feign.monitor.MonSeverityFeign;

@RestController
@RequestMapping("feign/monSeverity")
public class MonSeverityFeignMvc implements MonSeverityFeign {

    @Autowired
    private MonSysSeveritySvc svc;

    @Override
    public List<ESMonSysSeverityInfo> querySeverityList(Long domainId, String searchVal) {
        return svc.querySeverityList(domainId, searchVal);
    }

    @Override
    public Long saveOrUpdateSeverity(ESMonSysSeverityInfo saveDto) {
        return svc.saveOrUpdateSeverity(saveDto);
    }

    @Override
    public void deleteServrityByIds(Set<Long> delIds) {
        svc.deleteServrityByIds(delIds);
    }

    @Override
    public Resource exportSeverityInfos(Long domainId, Boolean isTpl) {
        return svc.exportSeverityInfos(domainId, isTpl);
    }

    @Override
    public ImportResultMessage importSeverityInfos(Long domainId, MultipartFile file) {
        return svc.importSeverityInfos(domainId, file);
    }
}
