package com.uino.bean.permission.business.request;

import java.util.HashSet;
import java.util.Set;
import java.util.regex.Pattern;

import org.springframework.util.Assert;

import com.uino.bean.permission.base.OAuthClientDetail;
import com.uino.bean.permission.business.IValidDto;

/**
 * 注册客户端请求
 * 
 * <AUTHOR>
 *
 */

public class RegisterClientReq extends OAuthClientDetail implements IValidDto {
	private static final long serialVersionUID = 1L;

	private static final Pattern URL_PATTERN = Pattern.compile("^((https|http)?://)[^\\s]+");

    private static final Set<String> AUTH_TYPE_SCOPES = new HashSet<>();
	static {
        AUTH_TYPE_SCOPES.add("authorization_code");
        AUTH_TYPE_SCOPES.add("client_credentials");
        AUTH_TYPE_SCOPES.add("password");
        AUTH_TYPE_SCOPES.add("refresh_token");
	}

	@Override
	public void valid() {
		Assert.notNull(super.getClientCode(), "clientcode_notnull");
        // if (super.isSecretRequired())
        // Assert.notNull(super.getClientSecret(), "client_secret_notnull");
		Assert.notEmpty(super.getCodeToTokenUrl(), "codetotoken_notnull");
		super.getCodeToTokenUrl().forEach(url -> {
            Assert.isTrue(URL_PATTERN.matcher(url).find(), url + "[error]");
		});
        if (super.isScoped()) {
            Assert.notEmpty(super.getScopes(), "scopes_notnull");
        }
		Assert.notEmpty(super.getAuthorizedGrantTypes(), "authorizedgranttypes_notnull");
		super.getAuthorizedGrantTypes().forEach(authType -> {
            Assert.isTrue(AUTH_TYPE_SCOPES.contains(authType), authType + "[error]");
		});
	}
}
