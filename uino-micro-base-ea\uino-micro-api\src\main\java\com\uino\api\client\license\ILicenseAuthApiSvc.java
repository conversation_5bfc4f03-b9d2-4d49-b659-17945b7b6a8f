package com.uino.api.client.license;

import java.util.List;

import com.binary.core.io.Resource;
import com.uinnova.product.vmdb.comm.model.license.CcLicenseAuthServer;
import com.uino.bean.license.BaseLicenseAuthInfo;

/**
 * 许可服务类
 * 
 * <AUTHOR>
 */
public interface ILicenseAuthApiSvc {

    /**
     * 查询授权许可信息
     * 
     * @return
     */
    BaseLicenseAuthInfo queryLicenseAuthInfo();

    /**
     * 查询服务器列表
     * 
     * @return
     */
    List<CcLicenseAuthServer> queryServerList();

    /**
     * 删除服务器
     * 
     * @param serverId
     * @return
     */
    Integer removeServer(Long serverId);

    /**
     * 创建客户识别码
     * 
     * @return
     */
    String createClientCode();

    /**
     * 注册授权
     * 
     * @param authCode 授权码
     * @param authUser 授权用户
     * @return
     */
    void registerLicense(String authCode, String authUser);

    /**
     * 跟据数据库中登记的授权许可证注册
     */
    void registerLicenseByDb();

    /**
     * 获取二维码
     * 
     * @param width
     * @param height
     * @return
     */
    Resource getQRCodeImage(Integer width, Integer height);

}
