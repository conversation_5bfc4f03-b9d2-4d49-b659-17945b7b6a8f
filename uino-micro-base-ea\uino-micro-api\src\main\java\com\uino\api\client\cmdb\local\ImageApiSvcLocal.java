package com.uino.api.client.cmdb.local;

import java.util.List;
import java.util.Set;

import com.binary.core.exception.MessageException;
import com.uino.dao.BaseConst;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClassDir;
import com.uino.service.cmdb.microservice.IImageSvc;
import com.uino.bean.cmdb.base.CcImage;
import com.uino.bean.cmdb.business.ImageCount;
import com.uino.bean.cmdb.business.ImportDirMessage;
import com.uino.bean.cmdb.business.ImportResultMessage;
import com.uino.bean.cmdb.query.ESSearchImageBean;
import com.uino.bean.permission.query.SearchKeywordBean;
import com.uino.api.client.cmdb.IImageApiSvc;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
public class ImageApiSvcLocal implements IImageApiSvc {

    @Autowired
    private IImageSvc svc;

    @Override
	public ImportResultMessage importZipImage(Integer sourceType, MultipartFile file) {
		return svc.importZipImage(BaseConst.DEFAULT_DOMAIN_ID,sourceType, file);
    }

    @Override
    public ImportResultMessage importZipImage(Long domainId, Integer sourceType, MultipartFile file) {
        return svc.importZipImage(domainId, sourceType, file);
    }

    @Override
    public List<ImageCount> queryImageDirList(CCcCiClassDir cdt) {
        return svc.queryImageDirList(BaseConst.DEFAULT_DOMAIN_ID, cdt);
    }

    @Override
	public List<ImageCount> queryImageDirList(Long domainId, CCcCiClassDir cdt) {
		return svc.queryImageDirList(domainId, cdt);
    }



    @Override
    public Page<CcImage> queryImagePage(ESSearchImageBean bean) {
        return svc.queryImagePage(bean);
    }




    @Override
    public boolean replaceImage(Long imageId, MultipartFile file) {
        return svc.replaceImage(imageId, file);
    }



    @Override
    public boolean deleteImage(CcImage image) {
        return svc.deleteImage(image);
    }

    @Override
    public boolean deleteDirImage(Long dirId) {
        return svc.deleteDirImage(dirId);
    }

    @Override
    public boolean importImage(Long dirId, MultipartFile file) {
        return svc.importImage(dirId, file);
    }

    @Override
    public ResponseEntity<byte[]> exportImageZipByDirIds(Set<Long> dirIds) {
        // TODO Auto-generated method stub
        return svc.exportImageZipByDirIds(dirIds);
    }

    @Override
    public List<CcImage> queryTopImage(SearchKeywordBean bean) {
        return svc.queryTopImage(bean);
    }



    @Override
    public ImportDirMessage import3DZipImage(MultipartFile file, Long dirId, boolean isCover) {
        // TODO Auto-generated method stub
        return svc.import3DZipImage(file, dirId, null, isCover);
    }



    @Override
    public Long updateImageRlt(CcImage image) {
        return svc.updateImageRlt(image);
    }

    @Override
    public boolean replace3DImage(Long imgId, MultipartFile file) {
        // TODO Auto-generated method stub
        return svc.replace3DImage(imgId, file);
    }

    @Override
    public boolean delete3DImage(CcImage image) {
        // TODO Auto-generated method stub
        return svc.delete3DImage(image);
    }

    @Override
    public CcImage queryImageById(Long id) {
        // TODO Auto-generated method stub
        return svc.queryImageById(id);
    }

    @Override
    public ImportDirMessage importImages(Long dirId, MultipartFile... files) {
        // TODO Auto-generated method stub
        return svc.importImages(dirId, files);
    }

    @Override
    public Long countBySearchBean(ESSearchImageBean bean) {
        return svc.countBySearchBean(bean);
    }

	@Override
	public ResponseEntity<byte[]> downloadImageResource(List<Long> ids) {
		return svc.downloadImageResource(ids);
	}

    @Override
    public Page<CcImage> queryImageByPath(ESSearchImageBean bean) {
        return svc.queryImageByPath(bean);
    }

    @Override
    public Boolean isShowDocumentAttribute() {
        return svc.isShowDocumentAttribute();
    }
}
