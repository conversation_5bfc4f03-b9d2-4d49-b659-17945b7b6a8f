package com.uino.util.sys;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Hashtable;
import java.util.Iterator;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import javax.naming.AuthenticationException;
import javax.naming.Context;
import javax.naming.NamingEnumeration;
import javax.naming.NamingException;
import javax.naming.directory.Attribute;
import javax.naming.directory.Attributes;
import javax.naming.directory.SearchControls;
import javax.naming.directory.SearchResult;
import javax.naming.ldap.InitialLdapContext;
import javax.naming.ldap.LdapContext;

import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.StringUtils;

import com.alibaba.fastjson.JSON;
import com.binary.framework.exception.ServiceException;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 
 * <AUTHOR>
 * @since 2020-08-12 新增ldaps临时解决方案. 预计在2020-08-14前废弃当前类
 */
@Slf4j
@RefreshScope
public class LdapUtil {

    @Value("${ldap.debug:false}")
    private boolean ldapDebug = false;

    private static Map<Long, LdapUtil> cache = new HashMap<Long, LdapUtil>();

    @Data
    public static class LdapsPro {

        private String trustStorePath;

        private String keyStorePassword;

        private Set<String> hosts;
    }

    private static volatile LdapsPro ldapsPro = null;
    static {
        initLdapsConfig();
    }

    public static void initLdapsConfig() {
        if (ldapsPro != null) {
            log.warn("ldaps config is inited!");
            return;
        }
        // 初始化相关配置.
        ClassPathResource a = new ClassPathResource("ldaps.conf");
        FileInputStream fis = null;
        try {
            if (a.exists()) {
                File file = a.getFile();
                if (file.isDirectory()) {
                    log.info("ldaps.conf is direcotry !");
                } else {
                    fis = new FileInputStream(file);
                    LdapsPro ldapsPro = JSON.parseObject(IOUtils.toString(fis), LdapsPro.class);
                    HashSet<String> hashSet = new HashSet<String>();
                    if (ldapsPro.getHosts() != null) {
                        for (String str : ldapsPro.getHosts()) {
                            if (str == null) {
                                continue;
                            }
                            str = str.trim();
                            hashSet.add(str);
                        }
                    }
                    ldapsPro.setHosts(hashSet);
                    LdapUtil.ldapsPro = ldapsPro;
                    File tsFile = new File(ldapsPro.trustStorePath);
                    if (!tsFile.exists()) {
                        log.error("trustStore 文件未找到[{}]", ldapsPro.trustStorePath);
                    } else {
                        ldapsPro.trustStorePath = tsFile.getAbsolutePath();
                        System.setProperty("com.sun.jndi.ldap.object.disableEndpointIdentification", "true");
                        // Modify at 2023-10-19，目前登录发现不再使用安全证书。需注释掉安全证书引用，否则登录会报错
                        //System.setProperty("javax.net.ssl.trustStore", ldapsPro.trustStorePath);
                        // TODO 加密
                        System.setProperty("javax.net.ssl.keyStorePassword", ldapsPro.keyStorePassword);
                    }
                }
            } else {
                log.info("ldaps.conf not exites !");
            }
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        } finally {
            if (fis != null) {
                try {
                    fis.close();
                } catch (IOException e) {
                }
            }
        }
        if (ldapsPro == null) {
            ldapsPro = new LdapsPro();
            ldapsPro.setHosts(new HashSet<String>(0));
        }
    }

    private LdapUtil() {
    }

    public static void clearCache() {
        cache.clear();
    }

    public static LdapUtil getLdapUtil(Long id) {
        synchronized (LdapUtil.class) {
            LdapUtil ldapUtil = cache.get(id);
            return ldapUtil;
        }
    }

    public static void remoteCacheById(Long id) {
        synchronized (LdapUtil.class) {
            cache.remove(id);
        }
    }

    /**
     * 保留指定的缓存,移除其他的缓存
     * 
     * @param ids
     *            需要保留的缓存,如果该值为空清楚全部缓存
     */
    public static void retainCahceByIdsRemoveOther(Set<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            clearCache();
            return;
        }
        synchronized (LdapUtil.class) {
            Iterator<Entry<Long, LdapUtil>> iterator = cache.entrySet().iterator();
            while (iterator.hasNext()) {
                Entry<Long, LdapUtil> next = iterator.next();
                if (!ids.contains(next.getKey())) {
                    iterator.remove();
                }
            }
        }
    }

    public static LdapUtil createTempLdapUtil(String hostName, String port, String baseDn, String userNameAttr,
            String loginUserDn, String password) {
        LdapUtil ldapUtil = new LdapUtil();
        ldapUtil.clearProps();
        String prx = ldapsPro.getHosts().contains(hostName) ? "ldaps://" : "ldap://";
        String url = prx + hostName + ":" + port;
        ldapUtil.setUrl(url);
        ldapUtil.setDomain(baseDn);
        ldapUtil.setSeachAttr(userNameAttr);
        ldapUtil.setBaseUserDomian(loginUserDn);
        password = SysUtil.EncryptDES.decryptDES(password);
        ldapUtil.setBaseUserPassword(password);
        return ldapUtil;
    }

    public static LdapUtil putLdapUtil(Long id, String hostName, String port, String baseDn, String userNameAttr,
            Map<String, String> attrMaps, String loginUserDn, String password, int timeout) {
        synchronized (LdapUtil.class) {
            LdapUtil ldapUtil = cache.get(id);
            if (ldapUtil == null) {
                ldapUtil = new LdapUtil();
                cache.put(id, ldapUtil);
            }
            ldapUtil.clearProps();
            ldapUtil.setId(id);
            String prx = ldapsPro.getHosts().contains(hostName) ? "ldaps://" : "ldap://";
            String url = prx + hostName + ":" + port;
            ldapUtil.setUrl(url);
            ldapUtil.setDomain(baseDn);
            ldapUtil.setSeachAttr(userNameAttr);
            ldapUtil.attrMaps = attrMaps;
            if (loginUserDn != null && password != null) {
                ldapUtil.setBaseUserDomian(loginUserDn);
                String pwd = SysUtil.EncryptDES.decryptDES(password);
                ldapUtil.setBaseUserPassword(pwd);
            }
            if (timeout >= 5000) {
                ldapUtil.setLdapTimeout(timeout + "");
            }
            return ldapUtil;
        }
    }

    private Long id;

    private String url = "";

    private String domain = "";

    private String baseUserDomian = "";

    private String baseUserPassword = "";

    private Hashtable<String, String> env = new Hashtable<String, String>();

    public String ldapTimeOut;

    private String seachAttr = "";

    private Map<String, String> attrMaps = new HashMap<String, String>();

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUrl() {
        return url;
    }

    public void setLdapTimeout(String ldapTimeOut) {
        this.ldapTimeOut = ldapTimeOut;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getDomain() {
        return domain;
    }

    public void setDomain(String domain) {
        this.domain = domain;
    }

    public String getBaseUserDomian() {
        return baseUserDomian;
    }

    public void setBaseUserDomian(String baseUserDomian) {
        this.baseUserDomian = baseUserDomian;
    }

    public String getBaseUserPassword() {
        return baseUserPassword;
    }

    public void setBaseUserPassword(String baseUserPassword) {
        this.baseUserPassword = baseUserPassword;
    }

    public Hashtable<String, String> getEnv() {
        return env;
    }

    public void setEnv(Hashtable<String, String> env) {
        this.env = env;
    }

    public String getSeachAttr() {
        return seachAttr;
    }

    public void setSeachAttr(String seachAttr) {
        this.seachAttr = seachAttr;
    }

    public Map<String, String> getAttrMaps() {
        return attrMaps;
    }

    public void setAttrMaps(Map<String, String> attrMaps) {
        this.attrMaps = attrMaps;
    }

    public Boolean hasBaseUserAndPwd() {
        return StringUtils.hasText(baseUserDomian) && StringUtils.hasText(baseUserPassword);
    }

    public LdapContext getLdapContext() {
        return getLdapContext(baseUserDomian, baseUserPassword);
    }

    public LdapContext getLdapContext(String baseUserDn, String basePassword) {
        // LDAP 工厂
        env.put(Context.INITIAL_CONTEXT_FACTORY, "com.sun.jndi.ldap.LdapCtxFactory");
        // LDAP访问安全级别
        env.put(Context.SECURITY_AUTHENTICATION, "simple");
        env.put(Context.PROVIDER_URL, url);
        if (url.startsWith("ldaps")) {
            env.put(Context.SECURITY_PROTOCOL, "ssl");
        }
        env.put("com.sun.jndi.ldap.connect.timeout", ldapTimeOut != null ? ldapTimeOut : "10000");
        // 填DN
        env.put(Context.SECURITY_PRINCIPAL, baseUserDn);
        // AD Password
        env.put(Context.SECURITY_CREDENTIALS, basePassword);
        env.put("java.naming.ldap.attributes.binary", "objectSidobjectGUID");
        try {
            return new InitialLdapContext(env, null);
        } catch (AuthenticationException e) {
            // 登录失败
        } catch (NamingException e1) {
            throw new ServiceException(e1);
        }
        return null;
    }

    /**
     * 验证登录
     * 
     * @param usernameDN
     * @param password
     * @return
     */
    public boolean loginValidate(String usernameDN, String password) {
        boolean ret = false;
        LdapContext ldapCtx = null;
        try {
            ldapCtx = getLdapContext(usernameDN, password);
            ret = ldapCtx != null;
        } finally {
            if (ldapCtx != null) {
                try {
                    ldapCtx.close();
                } catch (NamingException e) {
                }
            }
        }
        return ret;
    }

    /**
     * 搜索DN
     * 
     * @param ldapContext
     * @param username
     * @return
     */
    public String serachDN(LdapContext ldapContext, String username) {
        if (ldapContext == null) {
            ldapContext = getLdapContext();
        }
        if (ldapContext == null) { return null; }
        if (username == null) { return null; }
        SearchControls searchControls = new SearchControls();
        searchControls.setSearchScope(SearchControls.SUBTREE_SCOPE);
        searchControls.setReturningAttributes(new String[] {seachAttr});
        String filter = "(" + seachAttr + "=" + username + ")";
        try {
            NamingEnumeration<SearchResult> search = ldapContext.search(domain, filter, searchControls);
            while (search.hasMoreElements()) {
                return search.next().getNameInNamespace();
            }
        } catch (NamingException e) {
            log.error(e.getMessage());
        }
        return null;
    }

    /**
     * 搜索
     * 
     * @param ldapContext
     * @param username
     * @return
     */
    public Map<String, String> serachInfo(LdapContext ldapContext, String username, Set<String> ldapKeys) {
        // TODO 将搜索中的数据filter改为配置文件,这样将可以实现公用.
        if (ldapContext == null) {
            ldapContext = getLdapContext();
        }
        if (ldapContext == null) { return null; }
        if (username == null) { return null; }
        SearchControls searchControls = new SearchControls();
        searchControls.setSearchScope(SearchControls.SUBTREE_SCOPE);
        Map<String, String> ret = new HashMap<String, String>();
        String[] filters = null;
        if (ldapDebug) {
            ldapKeys.add("*"); // 查询全部的属性
        } else {
            ldapKeys.add("");
        }
        filters = ldapKeys.toArray(new String[] {});
        if (filters != null) {
            searchControls.setReturningAttributes(filters);
        }
        String filter = "(" + seachAttr + "=" + username + ")";
        try {
            NamingEnumeration<SearchResult> search = ldapContext.search(domain, filter, searchControls);
            while (search.hasMoreElements()) {
                SearchResult searchResult = search.next();
                Attributes attributes = searchResult.getAttributes();
                if (attributes != null) {
                    NamingEnumeration<? extends Attribute> all = attributes.getAll();
                    while (all.hasMoreElements()) {
                        Attribute attribute = all.nextElement();
                        ret.put(attribute.getID(), attribute.get().toString());
                    }
                }
                if (ldapDebug) {
                    log.debug("用户[" + username + "]查询ldap属性返回 " + JSON.toJSONString(ret));
                }
                return ret;
            }
        } catch (NamingException e) {
            log.error(e.getMessage());
        }
        return null;
    }

    public static void closeLdapCtx(LdapContext ldapContext) {
        if (ldapContext != null) {
            try {
                ldapContext.close();
            } catch (NamingException e) {
                log.error(e.getMessage());
            }
        }
    }

    public void clearProps() {
        url = "";
        domain = "";
        baseUserDomian = "";
        baseUserPassword = "";
        seachAttr = "";
    }
}
