package com.uino.bean.cmdb.base.dataset.relation;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class RelationRuleLine implements Serializable {

	private static final long serialVersionUID = 1L;

	private Long classId;
	private Long nodeStartId;
	private Long nodeEndId;
	//	方向
	private Boolean direction;
	private List<RelationRuleAttrCdt> cdts;
	private RelationRuleLineOp lineOp;
	private String lineOpValue;
	
	public RelationRuleLine() {
		
	}
	
	public RelationRuleLine(JSONObject json) {
		if (json.containsKey("classId")) {
			this.classId = json.getLong("classId");
		}
		if (json.containsKey("nodeStartId")) {
			this.nodeStartId = json.getLong("nodeStartId");
		}
		if (json.containsKey("nodeEndId")) {
			this.nodeEndId = json.getLong("nodeEndId");
		}
		if (json.containsKey("direction")) {
			this.direction = json.getBoolean("direction");
		}
		if (json.containsKey("cdts") && json.getJSONArray("cdts")!=null) {
			this.cdts = new ArrayList<RelationRuleAttrCdt>();
			for (int i=0;i<json.getJSONArray("cdts").size();i++) {
				this.cdts.add(new RelationRuleAttrCdt(json.getJSONArray("cdts").getJSONObject(i)));
			}
		}
		if (json.containsKey("lineOp")) {
			this.lineOp = RelationRuleLineOp.valueOf(json.getInteger("lineOp"));
		}
		if (json.containsKey("lineOpValue")) {
			this.lineOpValue = json.getString("lineOpValue");
		}
	}
	
	public Long getClassId() {
		return classId;
	}
	public void setClassId(Long classId) {
		this.classId = classId;
	}
	public Long getNodeStartId() {
		return nodeStartId;
	}
	public void setNodeStartId(Long nodeStartId) {
		this.nodeStartId = nodeStartId;
	}
	public Long getNodeEndId() {
		return nodeEndId;
	}
	public void setNodeEndId(Long nodeEndId) {
		this.nodeEndId = nodeEndId;
	}
	public Boolean getDirection() {
		return direction;
	}
	public void setDirection(Boolean direction) {
		this.direction = direction;
	}
	public List<RelationRuleAttrCdt> getCdts() {
		return cdts;
	}
	public void setCdts(List<RelationRuleAttrCdt> cdts) {
		this.cdts = cdts;
	}
	public RelationRuleLineOp getLineOp() {
		return lineOp;
	}
	public void setLineOp(RelationRuleLineOp lineOp) {
		this.lineOp = lineOp;
	}
	public String getLineOpValue() {
		return lineOpValue;
	}
	public void setLineOpValue(String lineOpValue) {
		this.lineOpValue = lineOpValue;
	}
	
	public JSONObject toJson() {
		JSONObject json = new JSONObject();
		json.put("classId", classId);
		json.put("nodeStartId", nodeStartId);
		json.put("nodeEndId", nodeEndId);
		json.put("direction", direction);
		if (this.cdts!=null) {
			JSONArray arr = new JSONArray();
			for (RelationRuleAttrCdt cdt:cdts) {
				arr.add(cdt.toJson());
			}
			json.put("cdts", arr);
		}
		if (this.lineOp!=null) {
			json.put("lineOp", this.lineOp.getOp());
		}
		json.put("lineOpValue", lineOpValue);
		return json;
	}
}
