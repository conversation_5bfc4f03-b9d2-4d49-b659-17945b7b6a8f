spring.profiles.include=minor

#服务的访问路径
server.servlet.context-path=/tarsier-eam-feign-server
oauth.open=true
#权限模块http server path
permission.http.prefix = http://**************:1515/tarsier-eam


#本地资源http服务地址
http.resource.space=http://**************/rsm

#本地资源存储地址
local.resource.space = /uinnova/uino/rsm

# Mysql数据库地址
ds.jdbc.vmdb.url=***************************************************************************************************************************************

##ElasticSearch地址
esIps=**************:9200

#调用公共组件的服务方式
base.load-type=local

#是否启用微服务注册中心
eureka.client.enabled=false

#对象管理勾选业务主键数量限制
uino.base.ci.primarykey.maxcount=5

#日志保留时长，单位：天
uino.log.clear.duration=7

#指标单位
kpi.units=度,斤
#颁发token时是否携带随机参数
oauth.token.random_param=false

uino.monitor.ep.exist=false
uino.monitor.event.send.url=

#配置对象管理显示库 // 私有库:PRIVATE，设计库：DESIGN，运行库/基线库：BASELINE。不配置默认为BASELINE
uino.eam.lib_type.show=PRIVATE,DESIGN,BASELINE

#允许跨域请求的主机地址
uino.eam.allowed.origins=*

#文档上传正则表达式
uino.eam.word_name_regex=.*[.](doc|docx|xls|xlsx|ppt|pptx|pdf|txt|jpg|jpeg|bmp|gif|png|ico|swf|eot|svg|ttf|woff|woff2)