package com.uinnova.product.eam.web.util.bean;

import java.util.List;

public class DiagramXmlAttrConditions {

	private List<DiagramXmlRuleConditions> rules;
	
	private Long classId;

	public List<DiagramXmlRuleConditions> getRules() {
		return rules;
	}

	public void setRules(List<DiagramXmlRuleConditions> rules) {
		this.rules = rules;
	}

	public Long getClassId() {
		return classId;
	}

	public void setClassId(Long classId) {
		this.classId = classId;
	}
	
	
}
