package com.uino.service.sys.microservice.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;
import java.util.concurrent.locks.ReentrantLock;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.uino.dao.sys.ESOperateLogSvc;
import com.uino.bean.sys.base.ESOperateLog;

import lombok.extern.slf4j.Slf4j;

/**
 * 操作日志写入者
 *
 * <AUTHOR>
 *
 */
@Slf4j
@Component
public class OperateLogWriter {

    private static final List<ESOperateLog> writeQueue = new LinkedList<>();

    private static final ReentrantLock lock = new ReentrantLock();

    @Autowired
    private ESOperateLogSvc logSvc;

    private static boolean keepExec = false;

    /**
     * 写入日志，实际为加入队列
     *
     * @param log
     */
    public void writeLog(ESOperateLog... logs) {
        try {
            lock.lock();
            writeQueue.addAll(Arrays.asList(logs));
        } catch (Exception e) {
            log.error("加入操作日志缓存队列异常", e);
        } finally {
            lock.unlock();
        }
    }

    @PostConstruct
    public void init() {
        keepExec = true;
        scan();
    }

    /**
     * 应用销毁时就一次性写入
     */
    @PreDestroy
    public void destroy() {
        keepExec = false;
        lock.lock();
        logSvc.saveOrUpdateBatchNoRefresh(writeQueue);
        lock.unlock();
    }

    /**
     * 扫描器
     */
    private void scan() {
        new Thread(new Runnable() {

            public void run() {
                while (keepExec) {
                    List<ESOperateLog> logs = null;
                    try {
                        logs = getWriteQueue();
                        if (logs != null && logs.size() > 0) {
                            logSvc.saveOrUpdateBatchNoRefresh(logs);
                        }
                    } catch (Exception e) {
                        log.error("操作日志写入异常【{}】【{}】", JSON.toJSONString(logs), e);
                        log.error("重新加入队列");
                        writeLog(logs.toArray(new ESOperateLog[logs.size()]));
                    } finally {
                        try {
                            Thread.sleep(5000L);
                        } catch (InterruptedException e) {
                            log.error("操作日志写入者扫描器休眠异常", e);
                        }
                    }
                }
            }
        }).start();
    }

    /**
     * 获取本次需写入的队列信息
     *
     * @return
     */
    private List<ESOperateLog> getWriteQueue() {
        try {
            lock.lock();
            if (writeQueue.size() <= 0) { return null; }
            return new ArrayList<>(writeQueue);
        } catch (Exception e) {
            log.error("获取写入队列异常", e);
        } finally {
            writeQueue.clear();
            lock.unlock();
        }
        return null;
    }
}
