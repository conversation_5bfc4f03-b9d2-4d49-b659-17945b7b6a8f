package com.uino.init;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import com.binary.core.exception.MessageException;
import com.binary.framework.web.ErrorCode;
import com.binary.framework.web.RemoteResult;
import com.uinnova.product.vmdb.comm.i18n.MessageUtil;
import com.uino.util.exception.LoginException;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.client.HttpClientErrorException;

/**
 * <p>
 * 异常处理控制器 MessageException异常保持原有逻辑，认为已经翻译好
 * <p>
 * 其他异常会获取异常的信息,将信息视为国际化code，尝试进行国际化翻译，若找不到则返回原文
 * <p>
 * 支持两种表达式(1、常规表达式${key:value}2、翻译表达式#{key:value})并且可以共存:
 * <p>
 * 1、常规表达式eg:
 * <p>
 * BS_USER_LOGIN_CODE_EXIST${loginCode:login001}==>登录名[login001]已存在
 * <p>
 * 2、翻译表达式eg:
 * <p>
 * X_PARAM_NOT_NULL#{name:BS_MNAME_USERID}==》[用戶ID]不可为空
 * <p>
 * 注意：两种表达式可同时出现，当表达式出现冲突时，翻译表达式优先级更高
 *
 * </p>
 *
 * <AUTHOR>
 */
@ControllerAdvice
@Slf4j
public class ExceptionController {
    {
        log.info("异常处理类注册成功");
    }

    // MessageException异常外其他异常处理国际化表达式
    private String i18ParamRegex = "\\$\\{.*\\}";
    private Pattern i18ParamPattern = Pattern.compile(i18ParamRegex);
    // MessageException异常外其他异常处理国际化表达式=针对参数也是其他国际化
    private String i18TranParamRegex = "\\#\\{.*?\\}";
    private Pattern i18TramParamPattern = Pattern.compile(i18TranParamRegex);

    @ResponseBody
    @ExceptionHandler(value = Exception.class)
    public ResponseEntity<RemoteResult> errorHandler(Exception ex) {
        RemoteResult result = null;
        HttpStatus status = HttpStatus.INTERNAL_SERVER_ERROR;
        if (ex instanceof MessageException) {
            // MessageException视为翻译好的，直接返回
            result = new RemoteResult(false, ErrorCode.SERVER_ERROR.getCode(), ex.getMessage());
        } else {
            // 其他异常尝试翻译，翻译不出来扔出原文,不用太在意，其他模块根本不支持其他异常国际化
            String assertMsg = ex.getMessage();
            // 尝试获取常规表达式参数映射map
            Map<String, String> params = getI18Param(assertMsg);
            // 删除所有常规表达式${}
            assertMsg = assertMsg.replaceAll(i18ParamRegex, "");
            // 尝试获取带翻译表达式映射map
            Map<String, String> tranParams = getI18TranParam(assertMsg);
            // 删除所有带翻译表达式#{}
            assertMsg = assertMsg.replaceAll(i18TranParamRegex, "");
            // 合并常规表达式参数字典和翻译表达式参数字典
            Map<String, String> i18Params = new HashMap<>();
            // 先放常规表达式再放翻译表达式，这样出现重复翻译表达式会覆盖常规表达式
            i18Params.putAll(params);
            i18Params.putAll(tranParams);
            // 断言异常进行翻译,国际化中没有返回原文
            String transMsg = MessageUtil.trans(assertMsg, i18Params);
            // 真正的返回消息
            String returnMsg = null;
            if (transMsg == null) {
                // 代表没翻译出来,打个日志帮助后端排查国际化，也有可能是该异常就不是已知要给出去的异常，所以打印一下异常本身
                log.error("未检测到国际化,返回原文,请检查国际化文件:" + ex.getMessage());
                log.error("server异常", ex);
                returnMsg = ex.getMessage();
            } else {
                // 翻译成功，丢回去翻译好的报文，翻译好的打印一条debug，代表是程序主动扔出去的
                log.debug(transMsg, ex);
                returnMsg = transMsg;
            }
            int errorCode = ErrorCode.SERVER_ERROR.getCode();
            // 未登录异常特殊处理
            if (ex instanceof LoginException) {
                errorCode = ErrorCode.NOT_LOGIN.getCode();
                status = HttpStatus.UNAUTHORIZED;
            }
            result = new RemoteResult(false, errorCode, returnMsg);
        }
        ResponseEntity<RemoteResult> resultResponseEntity = new ResponseEntity<>(result, status);
        return resultResponseEntity;
    }

    /**
     * 根据正则匹配内容返回列表
     *
     * @param msg eg: SAVE_ORG_PARNETID_NOEXIST${parentId:111}
     * @return
     */
    private Map<String, String> getI18Param(String msg) {
        Map<String, String> params = new HashMap<>();
        Matcher matcher = i18ParamPattern.matcher(msg);
        if (matcher != null) {
            while (matcher.find()) {
                try {
                    String content = matcher.group().substring(2, matcher.group().length() - 1);
                    String[] contents = content.split(":");
                    params.put(contents[0], contents[1]);
                } catch (Exception e) {
                    log.error("拼装国际化参数异常,非MessageException国际化翻译时，param表达式格式为${key:val}");
                }

            }
        }
        return params;
    }

    /**
     * 根据正则匹配内容返回列表-带参数翻译
     *
     * @param msg eg: X_PARAM_NOT_NULL#{name:BS_MNAME_USERID}
     * @return {name:用戶ID/userId}
     */

    private Map<String, String> getI18TranParam(String msg) {
        Map<String, String> params = new HashMap<>();
        Matcher matcher = i18TramParamPattern.matcher(msg);
        if (matcher != null) {
            while (matcher.find()) {
                try {
                    String content = matcher.group().substring(2, matcher.group().length() - 1);
                    String[] contents = content.split(":");
                    String param = contents[1];
                    String tranParam = MessageUtil.trans(param);
                    params.put(contents[0], tranParam == null ? param : tranParam);
                    if (tranParam == null) {
                        log.error("缺少{}国际化配置", param);
                    }
                } catch (Exception e) {
                    log.error("拼装国际化参数异常,非MessageException国际化翻译时，param表达式格式为#{key:val}");
                }

            }
        }
        return params;
    }

    @ExceptionHandler(value = MethodArgumentNotValidException.class)
    @ResponseBody
    public RemoteResult handleValidException(MethodArgumentNotValidException e){
        BindingResult bindingResult = e.getBindingResult();
        return getCommonResult(bindingResult);
    }

    @ExceptionHandler(value = HttpClientErrorException.class)
    @ResponseBody
    public ResponseEntity<String> handleHttpClientErrorException(HttpClientErrorException e){
        return new ResponseEntity<>(e.getMessage(),e.getStatusCode());
    }

    /**
     * 异常数据封装
     * @param bindingResult
     * @return RemoteResult
     */
    private RemoteResult getCommonResult(BindingResult bindingResult){
        String message = null;
        if(bindingResult.hasErrors()){
            FieldError fieldError = bindingResult.getFieldError();
            if(fieldError != null){
                message = "["+fieldError.getField()+"]"+fieldError.getDefaultMessage();
            }
        }
        return new RemoteResult(false, ErrorCode.SERVER_ERROR.getCode() , message);
    }
}
