package com.uino.monitor.tp.metric;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.binary.core.exception.MessageException;
import com.binary.core.lang.StringUtils;
import com.binary.core.util.BinaryUtils;
import com.binary.jdbc.Page;
import com.uino.bean.tp.base.FinalPerformanceDTO;
import com.uino.bean.tp.buiness.MetricDataAggFieldRespDTO;
import com.uino.bean.tp.buiness.PerfDataRespDTO;
import com.uino.bean.tp.query.MetricAttrValQueryDTO;
import com.uino.bean.tp.query.MetricDataAggFieldQueryDTO;
import com.uino.bean.tp.query.PerfDataReqDTO;
import com.uino.bean.tp.query.PerfDataReqDTOV2;
import com.uino.dao.AbstractSplitBaseDao;
import com.uino.dao.ESConst;
import com.uino.dao.util.ESUtil;
import com.uino.util.sys.ValidDtoUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.lucene.queryparser.classic.QueryParser;
import org.elasticsearch.action.admin.indices.create.CreateIndexRequest;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.core.TimeValue;
import org.elasticsearch.xcontent.XContentBuilder;
import org.elasticsearch.xcontent.XContentFactory;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.ConstantScoreQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.AggregationBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.BucketOrder;
import org.elasticsearch.search.aggregations.bucket.histogram.LongBounds;
import org.elasticsearch.search.aggregations.bucket.histogram.Histogram;
import org.elasticsearch.search.aggregations.bucket.histogram.ParsedDateHistogram;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedTerms;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 性能数据service
 *
 * @author: weixuesong
 * @create: 2020/06/09 16:55
 **/
@Service
@Slf4j
@RefreshScope
public class MetricDataSvc extends AbstractSplitBaseDao {

    @Override
    public String getIndexPrefix() {
        return ESConst.INDEX_METRIC_DATA_PREFIX;
    }

    @Override
    public String getType() {
        return "performance";
    }

    @Value("${base.performance.number.of.shards:1}")
    private Integer numberOfShards;

    @Value("${performance.save.method:synchronous}")
    private String saveMethod;

    /**
     * 设置setting
     *
     * @param createIndex
     * @author:
     * @date: 2020/9/3 14:49
     * @return: void
     */
    @Override
    protected void settings(CreateIndexRequest createIndex) {
        XContentBuilder setting;
        try {
            setting = XContentFactory.jsonBuilder();
            setting.startObject()
                        .field("number_of_shards", numberOfShards)
                    .endObject();
            createIndex.settings(setting);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new MessageException(e.getMessage());
        }
    }

    @Override
    public XContentBuilder getSelfMapping() {
        XContentBuilder mapping = null;
        try {
            mapping = XContentFactory.jsonBuilder();
            mapping.startObject()
                        .field("date_detection", false)
                        .startArray("dynamic_templates")
                            .startObject()
                                .startObject("strings")
                                    .field("match_mapping_type", "string")
                                    .startObject("mapping")
                                        .field("type", "keyword")
                                    .endObject()
                                .endObject()
                            .endObject()
                        .endArray()
                        .startObject("properties")
                            .startObject("time").field("type", "date").endObject()
                            .startObject("value").field("type", "double").endObject()
                        .endObject()
                    .endObject();
        } catch (Exception e) {
            log.error("Occurred an exception during get mapping", e);
        }
        return mapping;
    }

    public void saveMetricDataList(List<FinalPerformanceDTO> insertArray) {
        Map<String, JSONArray> saveMap = new HashMap<>();
        for (FinalPerformanceDTO perfData : insertArray) {
            long time = perfData.getTime();
            String index = getIndexByTime(time);
            JSONArray jsonValues = saveMap.get(index);
            if (jsonValues == null) {
                jsonValues = new JSONArray();
                saveMap.put(index, jsonValues);
            }
            jsonValues.add(perfData);
        }
        for (Map.Entry<String, JSONArray> saveEntry : saveMap.entrySet()) {
            //asyncSaveOrUpdateBatch(saveEntry.getValue(), saveEntry.getKey());

            if ("synchronous".equals(saveMethod)) {
                //同步写测试2000条约7秒，8个线程同时, 一个TP每秒约2300
                syncSaveBatch(saveEntry.getValue(), saveEntry.getKey());
            } else {
                //异步写测试,3个TP每秒大约6000，超过6000,ES写入不及时，导致ES写队列满
                asyncSaveBatch(saveEntry.getValue(), saveEntry.getKey());
            }
        }
    }

    /**
     * 同步步批量新增
     *
     * @param list 对象组
     * @return
     */
    @Override
    public boolean syncSaveBatch(JSONArray list, String index) {
        initIndexByName(index);
        BulkRequest bulkRequest = new BulkRequest();
        bulkRequest.timeout(TimeValue.timeValueMinutes(2));
        boolean flag = true;
        if (null != list && list.size() > 0) {
            for (int i = 0; i < list.size(); i++) {
                JSONObject obj = list.getJSONObject(i);
                String type = "".equals(getType()) ? index : getType();

                JSONObject otherFiledMap = obj.getJSONObject("otherFiledMap");
                if (null == otherFiledMap || otherFiledMap.isEmpty()) {
                    obj.remove("otherFiledMap");
                }

                JSONObject metricAttrs = obj.getJSONObject("metricAttrs");
                if (null == metricAttrs || metricAttrs.isEmpty()) {
                    obj.remove("metricAttrs");
                }

				String unit = obj.getString("unit");
                if (null == unit) {
                    obj.remove("unit");
                }

                IndexRequest indexRequest = new IndexRequest(index).id(String.valueOf(ESUtil.getUUID()));
                indexRequest.source(obj);
                bulkRequest.add(indexRequest);
            }

            try {
                BulkResponse bulkResponse = getClient().bulk(bulkRequest, RequestOptions.DEFAULT);
                if (bulkResponse.hasFailures()) {
                    flag = false;
                    log.error(bulkResponse.buildFailureMessage());
                }
            } catch (IOException e) {
                log.error(e.getMessage(), e);
                throw new MessageException(e.getMessage());
            }
        }
        return flag;
    }

    public PerfDataRespDTO queryPerfData(PerfDataReqDTO query) {
        PerfDataRespDTO resp = new PerfDataRespDTO();
        ValidDtoUtil.valid(query);
        BoolQueryBuilder perfQuery = getPerfQuery(query);
        // 不需要计算分数
        ConstantScoreQueryBuilder constantScoreQuery = QueryBuilders.constantScoreQuery(perfQuery);
        // 查询原数据
        if (Boolean.FALSE.equals(query.getAggFlag())) {
            Page<JSONObject> origingPage = getSortListByQuery(query.getPageNum(), query.getPageSize(), constantScoreQuery, "time", true);
            resp.setOriginData(origingPage);
            return resp;
        }
        // 查询聚合数据
        resp.setAggData(new ArrayList<>());
        // 聚合条件，先根据时间聚合，再stats聚合 bucket_key = Math.floor((value - offset) / interval) * interval + offset
        LongBounds longBounds = new LongBounds(query.getStartTime(), query.getEndTime());
        AggregationBuilder his = this.getDateHistogramAggregationBuilder("date_histogram", "time", null,
                query.getInterval(), null, longBounds);
        his.subAggregation(AggregationBuilders.stats("stats").field("value"));

        // 计算需要的索引 如果时间为空则返回全部的 性能数据索引
        String[] indices = getIndicesArrayByTime(query.getStartTime(), query.getEndTime());
        if (indices.length > 0) {
            SearchResponse search = search(getSearchRequest(constantScoreQuery, his, indices));
            // 解析结果,
            Aggregations aggs = search.getAggregations();
            ParsedDateHistogram terms = aggs.get("date_histogram");
            List<? extends Histogram.Bucket> buckets = terms.getBuckets();
            for (Histogram.Bucket bucket : buckets) {
                PerfDataRespDTO.PerfDataDTO perfData = new PerfDataRespDTO.PerfDataDTO();
                this.fillPerfData(perfData, bucket);
                // min max null时把avg改为null
                if (perfData.getMax() == null || perfData.getMin() == null) {
                    perfData.setAvg(null);
                    perfData.setSum(null);
                }
                DateTime time = (DateTime) bucket.getKey();
                perfData.setTime(time.getMillis());
                resp.getAggData().add(perfData);
            }
        }
        return resp;
    }

    private void fillPerfData( PerfDataRespDTO.PerfDataDTO perfData, Histogram.Bucket bucket) {
        Map<String, Double> statsAggMap = super.getStatsAgg(bucket, "stats");
        perfData.setMax(getValue(statsAggMap.get(STATS_AGG_MAX)));
        perfData.setMin(getValue(statsAggMap.get(STATS_AGG_MIN)));
        perfData.setAvg(getValue(statsAggMap.get(STATS_AGG_AVG)));
        perfData.setSum(getValue(statsAggMap.get(STATS_AGG_AVG)));
    }

    private BigDecimal getValue(double value) {
        return (Double.NEGATIVE_INFINITY == value || Double.POSITIVE_INFINITY == value) ? null
                : new BigDecimal(value).setScale(2, BigDecimal.ROUND_HALF_UP);
    }

    private BoolQueryBuilder getPerfQuery(PerfDataReqDTO query) {
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        if (!BinaryUtils.isEmpty(query.getCiCode())) {
            boolQuery.must(QueryBuilders.termQuery("ciCode", query.getCiCode()));
        }
        if (!BinaryUtils.isEmpty(query.getMetrics())) {
            boolQuery.must(QueryBuilders.termsQuery("metric", query.getMetrics()));
        }
        boolQuery.must(QueryBuilders.rangeQuery("time").gte(new Date(query.getStartTime())).lte(new Date(query.getEndTime())));
        String attrKey = query.getAttrKey();
        String attrValue = query.getAttrValue();
        if (!StringUtils.isEmpty(attrKey)) {
            boolQuery.must(QueryBuilders.termQuery("otherFiledMap." + attrKey, attrValue));
        }
        return boolQuery;
    }

    public PerfDataRespDTO queryPerfDataV2(PerfDataReqDTOV2 query) {
        PerfDataRespDTO resp = new PerfDataRespDTO();
        BoolQueryBuilder perfQuery = getPerfQueryV2(query);
        // 不需要计算分数
        ConstantScoreQueryBuilder constantScoreQuery = QueryBuilders.constantScoreQuery(perfQuery);
        // 查询原数据
        if (Boolean.FALSE.equals(query.getAggFlag())) {
            Page<JSONObject> origingPage = getSortListByQuery(query.getPageNum(), query.getPageSize(), constantScoreQuery, "time", true);
            resp.setOriginData(origingPage);
            return resp;
        }
        // 查询聚合数据
        resp.setAggData(new ArrayList<>());
        // 聚合条件，先根据时间聚合，再stats聚合 bucket_key = Math.floor((value - offset) / interval) * interval + offset
        LongBounds longBounds = new LongBounds(query.getStartTime(), query.getEndTime());

        // 设置偏移量
        int offset = getSecond(query.getStartTime());
        AggregationBuilder his = this.getDateHistogramAggregationBuilder("date_histogram", "time",
                "+" + offset + "s", null, query.getInterval(), longBounds);
        his.subAggregation(AggregationBuilders.stats("stats").field("value"));

        // 计算需要的索引 如果时间为空则返回全部的 性能数据索引
        try {
            String[] indices = getIndicesArrayByTime(query.getStartTime(), query.getEndTime());
            if (indices.length > 0) {
                SearchResponse search = search(getSearchRequest(constantScoreQuery, his, indices));
                // 解析结果,
                Aggregations aggs = search.getAggregations();
                ParsedDateHistogram terms = aggs.get("date_histogram");
                List<? extends Histogram.Bucket> buckets = terms.getBuckets();
                for (Histogram.Bucket bucket : buckets) {
                    PerfDataRespDTO.PerfDataDTO perfData = new PerfDataRespDTO.PerfDataDTO();
                    this.fillPerfData(perfData, bucket);
                    // min max null时把avg改为null
                    if (perfData.getMax() == null || perfData.getMin() == null) {
                        perfData.setAvg(null);
                        perfData.setSum(null);
                    }
                    DateTime time = (DateTime) bucket.getKey();
                    perfData.setTime(time.getMillis());
                    resp.getAggData().add(perfData);
                }
            }
        } catch (Exception e) {
            log.error(getIndexPrefix() + "startTime=" + query.getStartTime() + ",endTime=" + query.getEndTime() + "cache indices is empty");
        }
        // 删除超出范围的数据
        if (!resp.getAggData().isEmpty()) {
            PerfDataRespDTO.PerfDataDTO first = resp.getAggData().get(0);
            if (first.getTime() < query.getStartTime()) {
                resp.getAggData().remove(0);
            }
            PerfDataRespDTO.PerfDataDTO end = resp.getAggData().get(resp.getAggData().size() - 1);
            if (end.getTime() > query.getEndTime() && !resp.getAggData().isEmpty()) {
                resp.getAggData().remove(resp.getAggData().size() - 1);
            }
        }
        return resp;
    }

    /**
     * 根据时间戳获取秒数
     * @param millisecond
     * @return
     */
    private int getSecond(long millisecond) {
        Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(millisecond);
        int second = cal.get(Calendar.SECOND);
        int minute = cal.get(Calendar.MINUTE);
        return second + minute * 60;
    }

    private BoolQueryBuilder getPerfQueryV2(PerfDataReqDTOV2 query) {
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        if (!BinaryUtils.isEmpty(query.getMetrics())) {
            boolQuery.must(QueryBuilders.termsQuery("metric", query.getMetrics()));
        }
        if (!BinaryUtils.isEmpty(query.getClassIds())) {
            boolQuery.must(QueryBuilders.termsQuery("classId", query.getClassIds()));
        }
        if (!BinaryUtils.isEmpty(query.getCiId())) {
            boolQuery.must(QueryBuilders.termQuery("ciCode", query.getCiId()));
        }
        boolQuery.must(QueryBuilders.rangeQuery("time").gte(new Date(query.getStartTime())).lte(new Date(query.getEndTime())));
        if (!BinaryUtils.isEmpty(query.getAttrInfos())) {
            // 属性查询之间为or的关系
            for (PerfDataReqDTOV2.AttrInfo attrInfo : query.getAttrInfos()) {
                if (!BinaryUtils.isEmpty(attrInfo.getAttrValue())) {
                    boolQuery.should(QueryBuilders.termQuery("otherFiledMap." + attrInfo.getAttrKey(), attrInfo.getAttrValue()));
                } else {
                    boolQuery.must(QueryBuilders.existsQuery("otherFiledMap." + attrInfo.getAttrKey()));
                }
            }
        }
        return boolQuery;
    }

    public List<String> queryAttrValue(MetricAttrValQueryDTO query) {
        Assert.isTrue(!BinaryUtils.isEmpty(query.getClassId()), "X_PARAM_NOT_NULL${name:classId}");
        Assert.isTrue(!BinaryUtils.isEmpty(query.getAttrKey()), "X_PARAM_NOT_NULL${name:attrKey}");
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        ConstantScoreQueryBuilder constantScoreQuery = QueryBuilders.constantScoreQuery(boolQuery);
        boolQuery.must(QueryBuilders.termQuery("classId", query.getClassId()));
        if (!BinaryUtils.isEmpty(query.getMetric())) {
            boolQuery.must(QueryBuilders.termQuery("metric", query.getMetric()));
        }
        String word = query.getWord();
        if (!BinaryUtils.isEmpty(word)) {
            word = "*" + QueryParser.escape(word) + "*";
            boolQuery.must(QueryBuilders.wildcardQuery("otherFiledMap." + query.getAttrKey(), word));
        }
        List<Map<String, Long>> maps = groupByCountField(query.getAttrKey(), constantScoreQuery);
        return maps.stream().flatMap(map -> map.entrySet().stream().limit(1).map(Map.Entry::getKey)).collect(Collectors.toList());
    }

    public List<MetricDataAggFieldRespDTO> queryMetricDataAggField(MetricDataAggFieldQueryDTO query) {
        query.valid();
        List<MetricDataAggFieldRespDTO> resp = new ArrayList<>();
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        if (query.getConditions() != null) {
            for (MetricDataAggFieldQueryDTO.Condition condition : query.getConditions()) {
                boolQuery.must(QueryBuilders.termQuery(condition.getAttrKey(), condition.getAttrValue()));
            }
        }
        boolQuery.must(QueryBuilders.termQuery("metric", query.getMetric()));
        boolQuery.must(QueryBuilders.rangeQuery("time").gte(query.getStartTime()).lte(query.getEndTime()));
        TermsAggregationBuilder termsAggregationBuilder = AggregationBuilders.terms("field_agg")
                .field(query.getAggField()).size(query.getTopN())
                .subAggregation(AggregationBuilders.stats("stats_agg").field("value"))
                .order(BucketOrder.aggregation("stats_agg", query.getSortMod(), query.getIfAsc()));
        String[] indices = getIndicesArrayByTime(query.getStartTime(), query.getEndTime());
        SearchResponse search = search(getSearchRequest(boolQuery, termsAggregationBuilder, indices));
        Aggregations aggregations = search.getAggregations();
        ParsedTerms fieldAgg = aggregations.get("field_agg");
        List<? extends Terms.Bucket> buckets = fieldAgg.getBuckets();
        for (Terms.Bucket bucket : buckets) {
            Map<String, Double> statsAggMap = super.getStatsAgg(bucket, "stats_agg");
            MetricDataAggFieldRespDTO respDTO = new MetricDataAggFieldRespDTO();
            respDTO.setMax(getValue(statsAggMap.get(STATS_AGG_MAX)));
            respDTO.setMin(getValue(statsAggMap.get(STATS_AGG_MIN)));
            respDTO.setAvg(getValue(statsAggMap.get(STATS_AGG_AVG)));
            respDTO.setSum(getValue(statsAggMap.get(STATS_AGG_AVG)));
            respDTO.setKey(bucket.getKeyAsString());
            resp.add(respDTO);
        }
        return resp;
    }

}
