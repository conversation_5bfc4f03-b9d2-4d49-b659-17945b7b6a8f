<?xml version="1.0"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.uino</groupId>
        <artifactId>eam-base</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <artifactId>uino-eam-micro-bean</artifactId>
    <name>uino-eam-micro-bean</name>

    <dependencies>
       <dependency>
           <groupId>org.quartz-scheduler</groupId>
           <artifactId>quartz</artifactId>
       </dependency>
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-annotations</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.jdom</groupId>
            <artifactId>jdom2</artifactId>
            <version>2.0.6.1</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>
        <dependency>
            <groupId>com.uino</groupId>
            <artifactId>uino-eam-micro-plugin</artifactId>
            <version>${project.version}</version>
        </dependency>
    </dependencies>
</project>
