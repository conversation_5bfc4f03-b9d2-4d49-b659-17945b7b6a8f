package com.uinnova.product.eam.service.sys;

import cn.hutool.json.JSONObject;
import com.binary.jdbc.Page;
import com.uinnova.product.eam.base.diagram.model.ESEnterpriseSysUser;
import com.uinnova.product.eam.base.diagram.model.ESEnterpriseSysUserQuery;
import com.uinnova.product.eam.base.diagram.model.ESUploadManage;
import com.uinnova.product.eam.base.diagram.model.NewUserRecord;
import com.uinnova.product.eam.model.EamCurrentUserInfo;
import com.uinnova.product.vmdb.comm.model.image.CcImage;
import com.uino.bean.permission.base.SysUser;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 系统公共服务
 *
 * <AUTHOR>
 * @version 2020/7/28
 */
public interface IEamSysSvc {

    /**
     * 上传文件
     *
     * @param file
     *            待上传文件
     * @return 文件路径
     */
    public String uploadFile(MultipartFile file);

    EamCurrentUserInfo getCurrentUser();

    /**
     * 获取是否是新用户
     *
     * @param userId
     *
     * @return
     */
    List<NewUserRecord> getIsOlderUser(Long userId);
    /**
     * 设置老用户
     *
     * @param userId
     *
     * @return
     */
    Long setIsOlderUser(Long userId, JSONObject Object);

    /**
     * 根据平台产品ID获取系统用户信息
     *
     * @param userQuery
     *
     * @return
     */
    SysUser getUserIdByMmdId(ESEnterpriseSysUserQuery userQuery);

    /**
     * 根据userId获取mmdId
     *
     * @param userId
     *
     * @return
     */
    Long getMmdIdByUserId(Long userId);

    /**
     * 保存平台产品ID获取系统用户信息
     *
     * @param esEnterpriseSysUser
     *
     * @return
     */
    Long saveOrUpdate(ESEnterpriseSysUser esEnterpriseSysUser);

    /**
     * 上传文件
     *
     * @param file
     *            待上传文件
     * @return 文件路径
     */
    ESUploadManage upload(MultipartFile file);

    Page<ESUploadManage> queryData(int pageNum, int pageSize, String orders);

    Long deleteImage(CcImage image);
}
