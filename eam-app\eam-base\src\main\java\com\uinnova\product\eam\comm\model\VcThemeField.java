package com.uinnova.product.eam.comm.model;




import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;


@Comment("主题字段表[VC_THEME_FIELD]")
public class VcThemeField implements EntityBean {
	private static final long serialVersionUID = 1L;


	@Comment("ID[ID]")
	private Long id;


	@Comment("所属主题[THEME_ID]")
	private Long themeId;


	@Comment("所属分类[CLASS_ID]")
	private Long classId;


	@Comment("字段类型[FIELD_TYPE]    字段类型:1=分类属性 2=KPI")
	private Integer fieldType;


	@Comment("字段ID[FIELD_ID]")
	private Long fieldId;


	@Comment("字段名称[FIELD_NAME]")
	private String fieldName;


	@Comment("字段描述[FIELD_DESC]")
	private String fieldDesc;


	@Comment("查看时是否显示[IS_SHOW]    查看时是否显示0=否 1=是")
	private Integer isShow;


	@Comment("显示单位[SHOW_UNIT]    显示单位（纯大写）")
	private String showUnit;


	@Comment("所属域[DOMAIN_ID]")
	private Long domainId;


	@Comment("数据状态[DATA_STATUS]    数据状态:数据状态：1-正常 0-删除")
	private Integer dataStatus;


	@Comment("创建人[CREATOR]")
	private String creator;


	@Comment("修改人[MODIFIER]")
	private String modifier;


	@Comment("创建时间[CREATE_TIME]    创建时间:yyyyMMddHHmmss")
	private Long createTime;


	@Comment("修改时间[MODIFY_TIME]    修改时间:yyyyMMddHHmmss")
	private Long modifyTime;




	public Long getId() {
		return this.id;
	}
	public void setId(Long id) {
		this.id = id;
	}


	public Long getThemeId() {
		return this.themeId;
	}
	public void setThemeId(Long themeId) {
		this.themeId = themeId;
	}


	public Long getClassId() {
		return this.classId;
	}
	public void setClassId(Long classId) {
		this.classId = classId;
	}


	public Integer getFieldType() {
		return this.fieldType;
	}
	public void setFieldType(Integer fieldType) {
		this.fieldType = fieldType;
	}


	public Long getFieldId() {
		return this.fieldId;
	}
	public void setFieldId(Long fieldId) {
		this.fieldId = fieldId;
	}


	public String getFieldName() {
		return this.fieldName;
	}
	public void setFieldName(String fieldName) {
		this.fieldName = fieldName;
	}


	public String getFieldDesc() {
		return this.fieldDesc;
	}
	public void setFieldDesc(String fieldDesc) {
		this.fieldDesc = fieldDesc;
	}


	public Integer getIsShow() {
		return this.isShow;
	}
	public void setIsShow(Integer isShow) {
		this.isShow = isShow;
	}


	public String getShowUnit() {
		return this.showUnit;
	}
	public void setShowUnit(String showUnit) {
		this.showUnit = showUnit;
	}


	public Long getDomainId() {
		return this.domainId;
	}
	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}


	public Integer getDataStatus() {
		return this.dataStatus;
	}
	public void setDataStatus(Integer dataStatus) {
		this.dataStatus = dataStatus;
	}


	public String getCreator() {
		return this.creator;
	}
	public void setCreator(String creator) {
		this.creator = creator;
	}


	public String getModifier() {
		return this.modifier;
	}
	public void setModifier(String modifier) {
		this.modifier = modifier;
	}


	public Long getCreateTime() {
		return this.createTime;
	}
	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}


	public Long getModifyTime() {
		return this.modifyTime;
	}
	public void setModifyTime(Long modifyTime) {
		this.modifyTime = modifyTime;
	}


}


