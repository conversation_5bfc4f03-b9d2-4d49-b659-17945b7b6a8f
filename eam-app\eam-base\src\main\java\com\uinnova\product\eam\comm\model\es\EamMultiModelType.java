package com.uinnova.product.eam.comm.model.es;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

/**
 * 多模型类型
 *
 * <AUTHOR>
 */
@Data
public class EamMultiModelType {
    @Comment("主键id")
    private Long id;

    @Comment("领域id")
    private Long domainId;

    @Comment("模型树类型名称")
    private String name;

    @Comment("模型树类型标识")
    private String code;

    @Comment("创建时间")
    private Long createTime;

    @Comment("排序")
    private Integer order;

    @Comment("是否是初始化数据")
    private Boolean isInit;
}
