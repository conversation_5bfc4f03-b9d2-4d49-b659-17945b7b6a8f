package com.uino.bean.license;

import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

/**
 * description
 *
 * <AUTHOR>
 * @since 2022/4/27 18:41
 */
@Data
public class BaseLicenseAuth implements EntityBean {

    @Comment("ID[ID]")
    private Long id;

    @Comment("客户标识[CLIENT_CODE]")
    private String clientCode;

    @Comment("标识生成时间[CODE_TIME]")
    private Long codeTime;

    @Comment("标识生成人[CODE_USER]")
    private String codeUser;

    @Comment("受权码[AUTH_CODE]")
    private String authCode;

    @Comment("受权时间[AUTH_TIME]")
    private Long authTime;

    @Comment("累计使用时间[UT_TIME]    单位：分钟")
    private Integer utTime;

    @Comment("注册受权人[AUTH_USER]")
    private String authUser;

    @Comment("受权截止日期[AUTH_END_DATE]")
    private Integer authEndDate;

    @Comment("受权备注1[AUTH_CUSTOM_1]    用户数")
    private Long authCustom1;

    @Comment("受权备注2[AUTH_CUSTOM_2]    机柜数")
    private Long authCustom2;

    @Comment("受权备注3[AUTH_CUSTOM_3]    开门数")
    private Long authCustom3;

    @Comment("受权备注4[AUTH_CUSTOM_4]    场景数")
    private Long authCustom4;

    @Comment("受权备注5[AUTH_CUSTOM_5]    视图数")
    private Long authCustom5;

    @Comment("受权备注6[AUTH_CUSTOM_6]    使用时间")
    private Long authCustom6;

    @Comment("受权备注7[AUTH_CUSTOM_7]    冻结数据 >=1:冻结 0=不冻结")
    private Long authCustom7;

    @Comment("受权备注8[AUTH_CUSTOM_8]")
    private Long authCustom8;

    @Comment("受权备注9[AUTH_CUSTOM_9]")
    private Long authCustom9;

    @Comment("受权备注10[AUTH_CUSTOM_10]")
    private Long authCustom10;

    @Comment("受权备注11[AUTH_CUSTOM_11]")
    private Long authCustom11;

    @Comment("受权备注12[AUTH_CUSTOM_12]")
    private Long authCustom12;

    @Comment("受权备注13[AUTH_CUSTOM_13]")
    private Long authCustom13;

    @Comment("受权备注14[AUTH_CUSTOM_14]")
    private Long authCustom14;

    @Comment("受权备注15[AUTH_CUSTOM_15]")
    private Long authCustom15;

    @Comment("受权字符串备注1[AUTH_STRING_CUSTOM_1]")
    private String authStringCustom1;

    @Comment("受权字符串备注2[AUTH_STRING_CUSTOM_2]")
    private String authStringCustom2;

    @Comment("受权字符串备注3[AUTH_STRING_CUSTOM_3]")
    private String authStringCustom3;

    @Comment("受权字符串备注4[AUTH_STRING_CUSTOM_4]")
    private String authStringCustom4;

    @Comment("受权字符串备注5[AUTH_STRING_CUSTOM_5]")
    private String authStringCustom5;

    @Comment("变更版本号[UP_VERSION]")
    private Integer upVersion;

    @Comment("创建时间[CREATE_TIME]    yyyyMMddHHmmss")
    private Long createTime;

    @Comment("修改时间[MODIFY_TIME]    yyyyMMddHHmmss")
    private Long modifyTime;
}
