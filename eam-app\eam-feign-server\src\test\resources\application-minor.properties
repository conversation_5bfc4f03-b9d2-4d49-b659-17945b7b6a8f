#端口
server.port=1532

#服务名
spring.application.name=tarsier-eam-server

##数据公共属性(连接池配置)
ds.conn.pool.initialSize=0
ds.conn.pool.maxActive=20
ds.conn.pool.maxWait=30000
ds.conn.pool.maxIdle=20
ds.conn.pool.minIdle=0
ds.conn.pool.validationQuery=select 1
ds.conn.pool.maxOpenPreparedStatements=500
ds.jdbc.vmdb.printtype=NONE
ds.jdbc.vmdb.writertype=CONSOLE
ds.jdbc.vmdb.dsname=ds_vmdb
ds.jdbc.diagram.dsname=ds_vmdb
ds.jdbc.sys.dsname=ds_vmdb
ds.jdbc.monitor.dsname=ds_vmdb
ds.jdbc.monitor.emv.dsname=ds_vmdb
ds.jdbc.dcv.dsname=ds_vmdb
ds.jdbc.vmdb.dstype=MySQL5
ds.jdbc.vmdb.driver=com.mysql.cj.jdbc.Driver
ds.jdbc.vmdb.user=uinnova
ds.jdbc.vmdb.passwd=ENC(JiIIQjp80jzaI3qohk9rzGuQBv5mBFiW)
ds.jdbc.vmdb.validationQuery=select 1 from dual

##ElasticSearch数据库是否需要认证
isAuth=true
##ElasticSearch数据库用户名
esUser=uinnova
##ElasticSearch数据库密码
esPwd=ENC(p9nZzfKfCVu9bDHgBRy/dGo7L8OKQXP+)


#是否启用服务追踪
spring.sleuth.enabled=false
spring.sleuth.sampler.percentage=0.1

#熔断与监控
feign.hystrix.enabled=false

#注册中心


#请求连接的超时时间
feign.client.config.default.connectTimeout=120000
#请求处理的超时时间
feign.client.config.default.readTimeout=120000

#cloud是否传递上下文
hystrix.shareSecurityContext=true
#hystrix.command.default.execution.isolation.strategy = SEMAPHORE

#是否压缩request
feign.compression.request.enabled=true
#是否压缩response
feign.compression.response.enabled=true

#pv缓存路径
dmv.cache.pv.path=/uinnova/tarsier/data/DMV/cache/pvCountTemp.cache

#上传文件大小限制
spring.servlet.multipart.max-file-size=20MB
spring.servlet.multipart.max-request-size=100MB
#spring.mvc.resources.static-locations=file:./src/main/webapp
server.tomcat.basedir=./tmp

#是否覆盖同名bean
spring.main.allow-bean-definition-overriding=true

#同步请求超时时间，单位毫秒
spring.mvc.async.request-timeout=1000000

spring.devtools.restart.enabled=false
spring.main.lazy-initialization=true

#权限filter
license.ignore.filter.pattern=**.mp3;**.mp4;**.wav;**.js;**.css;**.jpg;**.jpeg;**.bmp;**.gif;**.png;**.ico;**.swf;**.eot;**.svg;**.ttf;**.woff;**.woff2;**.htm;**.html;**.txt;**.xml;**.json;**.map;/license/auth/**;/redirectAuth;/getTokenByCode;/permission/user/getCurrentUser;/permission/module/getModuleTree;/sys/getLogos;/**;

#关系遍历批处理任务的执行周期(cron表达式)
batch.process.relation.rule.cron = 0 0 */1 * * ?

# 接口日志最大限制，单位B，-1不限制
print.request.param.size=1024

#jackson序列化json时不返回null字段
spring.jackson.default-property-inclusion=non_null