package com.uinnova.product.eam.service.es;

import com.uinnova.product.eam.comm.bean.ResolutionDoc;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Repository;

import jakarta.annotation.PostConstruct;

/**
 * 架构决议文档信息
 * <AUTHOR>
 */
@Repository
public class IamsEsResolutionDocSvc extends AbstractESBaseDao<ResolutionDoc, ResolutionDoc> {
    @Override
    public String getIndex() {
        return "uino_eam_resolution_doc";
    }

    @Override
    public String getType() {
        return "_doc";
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }
}
