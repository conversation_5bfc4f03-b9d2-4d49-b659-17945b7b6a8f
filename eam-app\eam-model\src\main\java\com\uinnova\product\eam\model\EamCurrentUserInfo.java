package com.uinnova.product.eam.model;

/**
 * 
 * <AUTHOR>
 *
 */
public class EamCurrentUserInfo {

	private Long id;

	private String loginCode;
	private String userCode;
	private String userName;

	private String icon;
	private String language;
	private Long domainId;

	private Long loginTime;
	private Long lastLoginTime;

	private String roleName;

	public EamCurrentUserInfo(Long id, String loginCode, String userCode,
							  String userName, String icon, String language,
							  Long domainId, Long loginTime, Long lastLoginTime,String roleName) {
		this.id = id;
		this.loginCode = loginCode;
		this.userCode = userCode;
		this.userName = userName;
		this.icon = icon;
		this.language = language;
		this.domainId = domainId;
		this.loginTime = loginTime;
		this.lastLoginTime = lastLoginTime;
		this.roleName = roleName;
	}

	public EamCurrentUserInfo() {

	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getLoginCode() {
		return loginCode;
	}

	public void setLoginCode(String loginCode) {
		this.loginCode = loginCode;
	}

	public String getUserCode() {
		return userCode;
	}

	public void setUserCode(String userCode) {
		this.userCode = userCode;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getIcon() {
		return icon;
	}

	public void setIcon(String icon) {
		this.icon = icon;
	}

	public String getLanguage() {
		return language;
	}

	public void setLanguage(String language) {
		this.language = language;
	}

	public Long getDomainId() {
		return domainId;
	}

	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}

	public Long getLoginTime() {
		return loginTime;
	}

	public void setLoginTime(Long loginTime) {
		this.loginTime = loginTime;
	}

	public Long getLastLoginTime() {
		return lastLoginTime;
	}

	public void setLastLoginTime(Long lastLoginTime) {
		this.lastLoginTime = lastLoginTime;
	}

	public String getRoleName() {
		return roleName;
	}

	public void setRoleName(String roleName) {
		this.roleName = roleName;
	}
}
