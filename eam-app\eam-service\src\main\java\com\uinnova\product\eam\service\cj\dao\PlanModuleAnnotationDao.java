package com.uinnova.product.eam.service.cj.dao;

import com.uinnova.product.eam.model.cj.domain.PlanModuleAnnotationEntity;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;


/**
 * 方案批注Dao
 *
 * <AUTHOR>
 * @since 2022-3-1 20:40:43
 */
@Component
public class PlanModuleAnnotationDao extends AbstractESBaseDao<PlanModuleAnnotationEntity, PlanModuleAnnotationEntity> {
    @Override
    public String getIndex() {
        return "uino_cj_plan_module_annotation";
    }

    @Override
    public String getType() {
        return getIndex();
    }

    @PostConstruct
    public void init() {
        initIndex();
    }
}
