package com.uino.cmdb.rlt_class.mvc;

import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import com.alibaba.fastjson.JSON;
import com.binary.framework.web.RemoteResult;
import com.uinnova.product.vmdb.comm.model.ci.CcCiClass;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uino.StartBaseWebAppliaction;
import com.uino.dao.cmdb.ESRltClassSvc;

import lombok.extern.slf4j.Slf4j;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = { StartBaseWebAppliaction.class }, webEnvironment = WebEnvironment.RANDOM_PORT)
@Slf4j
@ActiveProfiles("provider-local")
public class GetRltClassByIdTest {
	@Autowired
	private TestRestTemplate restTemplate;
	@MockBean
	private ESRltClassSvc esRltClassSvc;
	private static String testUrl;

	@BeforeClass
	public static void initCls() {
		GetRltClassByIdTest.testUrl = "/cmdb/rltClass/getRltClassById";
	}

	@Before
	public void start() {
		CcCiClassInfo mockRltClsInfo = new CcCiClassInfo();
		CcCiClass clsInfo = new CcCiClass();
		mockRltClsInfo.setCiClass(clsInfo);
		clsInfo.setId(111L);
		clsInfo.setClassCode("111code");
		clsInfo.setClassName("111name");
		Mockito.when(esRltClassSvc.queryClassById(Mockito.eq(111L))).thenReturn(mockRltClsInfo);
	}

	@Test
	public void test01() {
		HttpHeaders headers = new HttpHeaders();
		headers.add("Content-Type", "application/json");
		String responseBodyStr = restTemplate.postForObject(testUrl, new HttpEntity<>("111", headers), String.class);
		RemoteResult responseBody = JSON.parseObject(responseBodyStr, RemoteResult.class);
		Assert.assertTrue(responseBody.isSuccess());
	}
}
