package com.uino.service.sys.microservice.impl;

import java.io.IOException;
import java.util.Objects;
import java.util.UUID;

import com.binary.core.exception.MessageException;
import com.uino.service.sys.microservice.ISysSvc;
import com.uino.util.rsm.RsmUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.binary.core.io.Resource;
import com.binary.core.io.support.FileResource;
import com.uino.service.util.FileUtil;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
@RefreshScope
public class SysSvc implements ISysSvc {

    @Autowired
    private RsmUtils rsmUtils;

    @Value("${local.resource.space:}")
    private String localPath;

    @Value("${uino.eam.word_name_regex:.*[.](?i)(doc|docx|xls|xlsx|ppt|pptx|pdf|txt|jpg|jpeg|bmp|gif|png|ico|swf|eot|svg|otf|ttc|ttf|woff|woff2)}")
    private String fileRegex;

    @Override
    public String uploadFile(MultipartFile file) {
        boolean matches = Objects.requireNonNull(file.getOriginalFilename()).matches(fileRegex);
        if (!matches) {
            throw new MessageException("文件格式错误");
        }
        String filePath = null;
        try {

            byte[] fileBytes = file.getBytes();
            String fileName = file.getOriginalFilename();
            filePath = this.uploadFile(fileBytes, fileName);
        } catch (IOException e) {
            log.error("获取上传文件流异常", e);
            throw new RuntimeException("获取上传文件流异常");
        }
        return filePath;
    }

    @Override
    public Resource downloadFile(String filePath) {

        // 先从对象存储获取文件，并写入到本地
        rsmUtils.downloadRsmAndUpdateLocalRsm(filePath);
        FileResource fileResource = new FileResource(localPath + filePath);
        if (!fileResource.getFile().exists()) {
            throw new MessageException("资源不存在");
        }
        return fileResource;
    }

    @Override
    public String uploadFile(byte[] fileBytes, String fileName) {
        String filePath = "/" + UUID.randomUUID().toString() + "/" + fileName;
        try {
            FileUtil.writeFile(filePath, fileBytes);
        } catch (IOException e) {
            log.error("获取上传文件流异常", e);
            throw new RuntimeException("获取上传文件流异常");
        }
        return filePath;
    }
}

