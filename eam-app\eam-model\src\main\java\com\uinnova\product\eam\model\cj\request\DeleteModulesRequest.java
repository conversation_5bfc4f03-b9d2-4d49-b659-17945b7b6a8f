package com.uinnova.product.eam.model.cj.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 方案内容模块删除请求参数
 *
 * <AUTHOR>
 */
@Data
public class DeleteModulesRequest {

    /**
     * 章节id
     */
    @NotNull(message = "章节Id必填")
    private Long chapterId;

    /**
     * 模块IdList
     */
    @JsonProperty("moduleIds")
    private List<Long> moduleIdList;
}
