package com.uinnova.product.vmdb.comm.model.es;

import com.binary.core.http.HttpClient;
import com.binary.json.JSON;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * 
 * <AUTHOR>
 *
 */
public class ESHttpSender {

    Logger logger = LoggerFactory.getLogger(ESHttpSender.class);
    HttpClient tagSaveOrUpdateClient;
    HttpClient ciSaveOrUpdateClient;
    HttpClient ciClsSaveOrUpdateClient;
    HttpClient tagDelClient;
    HttpClient ciDelClient;
    HttpClient ciClsDelClient;

    public ESHttpSender(String host) {
        tagSaveOrUpdateClient = HttpClient.getInstance(host + "/tag/saveOrUpdateBatch");
        ciSaveOrUpdateClient = HttpClient.getInstance(host + "/CI/saveOrUpdateBatch");
        ciClsSaveOrUpdateClient = HttpClient.getInstance(host + "/CIClass/saveOrUpdateBatch");
        tagDelClient = HttpClient.getInstance(host + "/tag/deleteByIDList");
        ciDelClient = HttpClient.getInstance(host + "/CI/deleteByIDList");
        ciClsDelClient = HttpClient.getInstance(host + "/CIClass/deleteByIDList");
    }

    public String sendTagData(List<EsTagInfo> tagInfos) {
        return tagSaveOrUpdateClient.rest((String) null, JSON.toString(tagInfos));
    }

    public String sendCiData(List<ESCiInfo> ciInfos) {
        return ciSaveOrUpdateClient.rest((String) null, JSON.toString(ciInfos));
    }

    public String sendCiClassData(List<ESCiClassInfo> ciClassInfos) {
        return ciClsSaveOrUpdateClient.rest((String) null, JSON.toString(ciClassInfos));
    }

    public String sendCiDel(String[] ids) {
        return ciDelClient.rest((String) null, JSON.toString(ids));
    }

    public String sendCiClassDel(String[] ids) {
        return ciClsDelClient.rest((String) null, JSON.toString(ids));
        // return "";
    }

    public String sendTagDel(String[] ids) {
        return tagDelClient.rest((String) null, JSON.toString(ids));
        // return "";
    }

}
