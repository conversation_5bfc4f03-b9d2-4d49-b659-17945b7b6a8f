package com.uinnova.product.vmdb.comm.model.classTpl;

import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;

/**
 * 
 * <AUTHOR>
 *
 */
@Comment("CI属性定义模版表[CC_CI_ATTR_DEF_TPL]")
public class CcCiAttrDefTpl implements EntityBean {
    private static final long serialVersionUID = 1L;

    @Comment("ID[ID]")
    private Long id;

    @Comment("模版ID[TPL_ID]")
    private Long tplId;

    @Comment("CI分类ID[CLASS_ID]")
    private Long classId;

    @Comment("分类类型[CI_TYPE]    分类类型:1=基础CI 2=关系CI")
    private Integer ciType;

    @Comment("属性名[PRO_NAME]")
    private String proName;

    @Comment("标准名[PRO_STD_NAME]    标准名:全部大写")
    private String proStdName;

    @Comment("属性类型[PRO_TYPE]    属性类型:1=整数 2=小数 3=短文本(<=200) 4=长文本(<=1000) 5=文章 6=枚举 7=日期 8=字典")
    private Integer proType;

    @Comment("字典类型来源类型[PRO_DROP_SOURCE_TYPE]    字典类型来源类型: 1=标签 2=CI分类")
    private Integer proDropSourceType;

    @Comment("字典类型来源ID[PRO_DROP_SOURCE_ID]")
    private Long proDropSourceId;

    @Comment("属性描述[PRO_DESC]")
    private String proDesc;

    @Comment("映射字段[MP_CI_FIELD]")
    private Integer mpCiField;

    @Comment("是否主键[IS_MAJOR]    是否主键:0=否，1=是")
    private Integer isMajor;

    @Comment("是否必填[IS_REQUIRED]    是否必填:0=否，1=是")
    private Integer isRequired;

    @Comment("是否作为CI显示[IS_CI_DISP]    是否作为CI显示:0=否，1=是")
    private Integer isCiDisp;

    @Comment("缺省值[DEF_VAL]")
    private String defVal;

    @Comment("枚举值[ENUM_VALUES]    枚举值:多个以逗号分隔")
    private String enumValues;

    @Comment("排列顺序[ORDER_NO]")
    private Integer orderNo;

    @Comment("所属域[DOMAIN_ID]")
    private Long domainId;

    @Comment("数据状态[DATA_STATUS]    数据状态:0=删除，1=正常")
    private Integer dataStatus;

    @Comment("创建人[CREATOR]")
    private String creator;

    @Comment("修改人[MODIFIER]")
    private String modifier;

    @Comment("创建时间[CREATE_TIME]    创建时间:yyyyMMddHHmmss")
    private Long createTime;

    @Comment("更新时间[MODIFY_TIME]    更新时间:yyyyMMddHHmmss")
    private Long modifyTime;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTplId() {
        return this.tplId;
    }

    public void setTplId(Long tplId) {
        this.tplId = tplId;
    }

    public Long getClassId() {
        return this.classId;
    }

    public void setClassId(Long classId) {
        this.classId = classId;
    }

    public Integer getCiType() {
        return this.ciType;
    }

    public void setCiType(Integer ciType) {
        this.ciType = ciType;
    }

    public String getProName() {
        return this.proName;
    }

    public void setProName(String proName) {
        this.proName = proName;
    }

    public String getProStdName() {
        return this.proStdName;
    }

    public void setProStdName(String proStdName) {
        this.proStdName = proStdName;
    }

    public Integer getProType() {
        return this.proType;
    }

    public void setProType(Integer proType) {
        this.proType = proType;
    }

    public Integer getProDropSourceType() {
        return this.proDropSourceType;
    }

    public void setProDropSourceType(Integer proDropSourceType) {
        this.proDropSourceType = proDropSourceType;
    }

    public Long getProDropSourceId() {
        return this.proDropSourceId;
    }

    public void setProDropSourceId(Long proDropSourceId) {
        this.proDropSourceId = proDropSourceId;
    }

    public String getProDesc() {
        return this.proDesc;
    }

    public void setProDesc(String proDesc) {
        this.proDesc = proDesc;
    }

    public Integer getMpCiField() {
        return this.mpCiField;
    }

    public void setMpCiField(Integer mpCiField) {
        this.mpCiField = mpCiField;
    }

    public Integer getIsMajor() {
        return this.isMajor;
    }

    public void setIsMajor(Integer isMajor) {
        this.isMajor = isMajor;
    }

    public Integer getIsRequired() {
        return this.isRequired;
    }

    public void setIsRequired(Integer isRequired) {
        this.isRequired = isRequired;
    }

    public Integer getIsCiDisp() {
        return this.isCiDisp;
    }

    public void setIsCiDisp(Integer isCiDisp) {
        this.isCiDisp = isCiDisp;
    }

    public String getDefVal() {
        return this.defVal;
    }

    public void setDefVal(String defVal) {
        this.defVal = defVal;
    }

    public String getEnumValues() {
        return this.enumValues;
    }

    public void setEnumValues(String enumValues) {
        this.enumValues = enumValues;
    }

    public Integer getOrderNo() {
        return this.orderNo;
    }

    public void setOrderNo(Integer orderNo) {
        this.orderNo = orderNo;
    }

    public Long getDomainId() {
        return this.domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }

    public Integer getDataStatus() {
        return this.dataStatus;
    }

    public void setDataStatus(Integer dataStatus) {
        this.dataStatus = dataStatus;
    }

    public String getCreator() {
        return this.creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getModifier() {
        return this.modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public Long getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getModifyTime() {
        return this.modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }

}
