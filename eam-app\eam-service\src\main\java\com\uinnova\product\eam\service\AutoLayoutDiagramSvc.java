package com.uinnova.product.eam.service;

import com.uinnova.product.eam.comm.model.es.AutoLayoutDiagramConf;
import com.uinnova.product.eam.model.AutoDiagramCiVo;
import com.uinnova.product.eam.model.EamAutoLayoutCdt;
import com.uinnova.product.eam.model.vo.AutoPhysicsConfig;
import com.uinnova.product.eam.model.vo.CiSimpleInfoVo;

import java.util.Collection;
import java.util.List;

public interface AutoLayoutDiagramSvc {

    Long savaOrUpdate(AutoLayoutDiagramConf layoutDiagramConf);

    /**
     * 批量保存更新
     * @param confList 配置
     */
    void saveOrUpdateBatch(List<AutoLayoutDiagramConf> confList);

    AutoLayoutDiagramConf getConfByDiagramIdAndSheetId(String diagramId, String sheetId);

    /**
     * 业务能力图-架构元素获取
     * @param classId 分类id
     * @param artifactId 制品id
     * @return 下级分类集合
     */
    List<CiSimpleInfoVo> getBusinessElement(Long classId, Long artifactId);

    /**
     * 业务能力图-获取架构元素
     * @param cdt 分类树
     * @return 数据
     */
    List<AutoDiagramCiVo> getBusinessData(List<EamAutoLayoutCdt> cdt);

    /**
     * 物理部署图-配置
     * @return 配置信息
     */
    AutoPhysicsConfig getPhysicsConfig();

    /**
     * 查询视图配置
     * @param diagramIds 视图id
     * @return 视图配置
     */
    List<AutoLayoutDiagramConf> getByDiagramIds(Collection<String> diagramIds);

}
