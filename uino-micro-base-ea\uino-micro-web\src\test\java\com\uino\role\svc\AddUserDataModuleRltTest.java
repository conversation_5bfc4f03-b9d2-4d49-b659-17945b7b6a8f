package com.uino.role.svc;

import java.util.LinkedList;
import java.util.List;

import com.uino.dao.BaseConst;
import org.junit.Before;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import com.uino.service.permission.microservice.impl.RoleSvc;
import com.uino.dao.permission.rlt.ESUserDataModuleRltSvc;
import com.uino.bean.permission.base.SysUserDataModuleRlt;

@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("provider-local")
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class AddUserDataModuleRltTest {
	@Autowired
	private RoleSvc testSvc;
	@MockBean
	private ESUserDataModuleRltSvc udRltSvc;

	@Before
	public void before() {
		Mockito.when(udRltSvc.saveOrUpdateBatch(Mockito.anyList())).thenReturn(1);
		Mockito.when(udRltSvc.deleteByQuery(Mockito.any(), Mockito.anyBoolean())).thenReturn(1);

	}

	@Test
	public void test01() {
		SysUserDataModuleRlt reqNode01 = SysUserDataModuleRlt.builder().userId(1L).dataModuleCode("code")
				.dataValue("dataval").uid("uuid").build();
		List<SysUserDataModuleRlt> req = new LinkedList<>();
		req.add(reqNode01);
		testSvc.addUserDataModuleRlt(BaseConst.DEFAULT_DOMAIN_ID,req);
	}

	@Test
	public void test02() {
		SysUserDataModuleRlt reqNode01 = SysUserDataModuleRlt.builder().userId(1L).dataModuleCode("code").dataValue("0")
				.uid("uuid").build();
		List<SysUserDataModuleRlt> req = new LinkedList<>();
		req.add(reqNode01);
		testSvc.addUserDataModuleRlt(BaseConst.DEFAULT_DOMAIN_ID,req);
	}

	@Test
	public void test03() {
		SysUserDataModuleRlt reqNode01 = SysUserDataModuleRlt.builder().userId(1L).dataModuleCode("code")
				.dataValue("01").uid("0").build();
		List<SysUserDataModuleRlt> req = new LinkedList<>();
		req.add(reqNode01);
		testSvc.addUserDataModuleRlt(BaseConst.DEFAULT_DOMAIN_ID,req);
	}

	@Test(expected = IllegalArgumentException.class)
	public void test04() {
		SysUserDataModuleRlt reqNode01 = SysUserDataModuleRlt.builder().dataModuleCode("code").dataValue("dataval")
				.uid("uuid").build();
		List<SysUserDataModuleRlt> req = new LinkedList<>();
		req.add(reqNode01);
		testSvc.addUserDataModuleRlt(BaseConst.DEFAULT_DOMAIN_ID,req);
	}

	@Test(expected = IllegalArgumentException.class)
	public void test05() {
		SysUserDataModuleRlt reqNode01 = SysUserDataModuleRlt.builder().userId(1L).dataValue("dataval").uid("uuid")
				.build();
		List<SysUserDataModuleRlt> req = new LinkedList<>();
		req.add(reqNode01);
		testSvc.addUserDataModuleRlt(BaseConst.DEFAULT_DOMAIN_ID,req);
	}

	@Test(expected = IllegalArgumentException.class)
	public void test06() {
		SysUserDataModuleRlt reqNode01 = SysUserDataModuleRlt.builder().userId(1L).dataModuleCode("code").uid("uuid")
				.build();
		List<SysUserDataModuleRlt> req = new LinkedList<>();
		req.add(reqNode01);
		testSvc.addUserDataModuleRlt(BaseConst.DEFAULT_DOMAIN_ID,req);
	}

	@Test(expected = IllegalArgumentException.class)
	public void test07() {
		SysUserDataModuleRlt reqNode01 = SysUserDataModuleRlt.builder().userId(1L).dataModuleCode("code")
				.dataValue("dataval").build();
		List<SysUserDataModuleRlt> req = new LinkedList<>();
		req.add(reqNode01);
		testSvc.addUserDataModuleRlt(BaseConst.DEFAULT_DOMAIN_ID,req);
	}

	@Test
	public void test08() {
		SysUserDataModuleRlt reqNode01 = SysUserDataModuleRlt.builder().userId(1L).dataModuleCode("code").dataValue("0")
				.uid("0").build();
		List<SysUserDataModuleRlt> req = new LinkedList<>();
		req.add(reqNode01);
		testSvc.addUserDataModuleRlt(BaseConst.DEFAULT_DOMAIN_ID,req);
	}
}
