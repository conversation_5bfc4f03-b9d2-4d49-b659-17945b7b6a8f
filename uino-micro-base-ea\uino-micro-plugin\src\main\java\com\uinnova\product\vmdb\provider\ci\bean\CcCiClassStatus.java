package com.uinnova.product.vmdb.provider.ci.bean;

import com.uinnova.product.vmdb.comm.model.ci.CcClassStatus;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

public final class CcCiClassStatus {
	
	private static ThreadLocal<Map<Long,CcClassStatus>> threadLocal = new ThreadLocal<Map<Long,CcClassStatus>>();
	
	private CcCiClassStatus() {}
	
	/**
	 * 分类正在删除中
	 */
	public static final int DELETING = 1;
	
	/**
	 * 分类正在更新中
	 */
	public static final int UPDATING = 2;


	/**
	 * 导入中
	 */
	public static final int IMPORTING_CI = 3;
	
	/**
	 * 导出中
	 */
	public static final int EXPORTING_CI = 4;
	
	
	/**
	 * 分类数据正在删除中
	 */
	public static final int DELETING_CI = 5;
	
	
	public static boolean contain(Long id) {
		Map<Long, CcClassStatus> map = threadLocal.get();
		if(map == null) return false;
		return map.containsKey(id);
	}
	
	public synchronized static void put(CcClassStatus status) {
		if(status == null) return;
		Map<Long, CcClassStatus> map = threadLocal.get();
		if(map == null) {
			map = new HashMap<Long, CcClassStatus>();
			threadLocal.set(map);
		}
		map.put(status.getId(), status);
	}
	
	public static CcClassStatus getById(Long id) {
		Map<Long, CcClassStatus> map = threadLocal.get();
		if (map == null) return null;
		return map.get(id);
	}
	
	public synchronized static CcClassStatus remove(Long id) {
		Map<Long, CcClassStatus> map = threadLocal.get();
		if (map == null) return null;
		return map.remove(id);
	}
	
	public synchronized static CcClassStatus removeByClass(Long domainId,Long classId) {
		Map<Long, CcClassStatus> map = threadLocal.get();
		Set<Long> keySet = map.keySet();
		Long dId = null;
		for (Long id : keySet) {
			CcClassStatus ccClassStatus = map.get(id);
			if(ccClassStatus != null && ccClassStatus.getClassId().equals(classId)) {
				dId = id;
				break;
			}
		}
		
		return map.remove(dId);
	}
}
