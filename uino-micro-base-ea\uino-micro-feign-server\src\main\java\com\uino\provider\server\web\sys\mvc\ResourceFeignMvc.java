package com.uino.provider.server.web.sys.mvc;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.uino.service.sys.microservice.impl.ResourceSvc;
import com.uino.bean.cmdb.base.ESResource;
import com.uino.provider.feign.sys.ResourceFeign;

import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("feign/resource")
@Slf4j
public class ResourceFeignMvc implements ResourceFeign {

    @Autowired
    private ResourceSvc svc;

    @Override
    public String saveSyncResourceInfo(String path, String publicUrl, boolean unzip, Integer optionType) {
        // TODO Auto-generated method stub
        return svc.saveSyncResourceInfo(path, publicUrl, unzip, optionType);
    }

    @Override
    public String saveSyncResourceInfo(String path, String publicUrl, boolean unzip, boolean currentDir,
            Integer optionType) {
        // TODO Auto-generated method stub
        return svc.saveSyncResourceInfo(path, publicUrl, unzip, currentDir, optionType);
    }

    @Override
    public void saveSyncResourceInfo(List<ESResource> saveDtos) {
        // TODO Auto-generated method stub
        svc.saveSyncResourceInfo(saveDtos);
    }

    @Override
    public List<ESResource> getWaitSyncResources(Long createTime) {
        // TODO Auto-generated method stub
        return svc.getWaitSyncResources(createTime);
    }
}
