package com.uino.util.message.queue.tools;

import lombok.Getter;
import lombok.Setter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Configuration;

/**
 * @Title: IDGenderConfig
 * @Description: ID generation
 * @Author: YGQ
 * @Create: 2021-05-24 12:36
 **/
@Getter
@Setter
@Configuration
@Conditional(EnableMessageQueue.class)
public class IDGenderConfig {

    /**
     * Data center [0,31] If it is not configured in the configuration file, it is 0
     */
    private long datacenterId;

    /**
     * Machine ID [0,31] is 0 if it is not configured in the configuration file
     */
    private long machineId;

    @Bean
    public SnowFlakeFactory getSnowFlakeFactory() {
        return new SnowFlakeFactory(datacenterId, machineId);
    }
}