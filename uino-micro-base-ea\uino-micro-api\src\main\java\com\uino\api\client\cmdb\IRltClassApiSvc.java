package com.uino.api.client.cmdb;

import java.util.List;
import java.util.Set;

import com.uino.bean.cmdb.business.ClassReOrderDTO;
import org.springframework.web.multipart.MultipartFile;

import com.binary.core.io.Resource;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClass;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uino.bean.cmdb.base.ESCIClassInfo;

/**
 * 关系分类相关服务
 * 
 * <AUTHOR>
 */
public interface IRltClassApiSvc {

    /**
     * 持久化关系分类
     * 
     * @param rltClass
     * @return
     */
    Long saveOrUpdate(ESCIClassInfo rltClass);

    /**
     * 根据关系分类ids删除关系分类
     * 
     * @param rltClassIds
     * @return
     */
    Integer deleteByIds(Set<Long> rltClassIds);

    /**
     * 获取所有关系分类
     * 
     * @return
     */
    List<CcCiClassInfo> queryAllClasses();
    List<CcCiClassInfo> queryAllClasses(Long domainId);

    /**
     * 根据关系分类id获取关系分类信息
     * 
     * @param rltClassId
     * @return
     */
    CcCiClassInfo getRltClassById(Long rltClassId);

    /**
     * 根据关系分类名称获取关系分类信息
     *
     * @param domainId 域id
     * @param className 分类名称
     * @return 分类信息
     */
    CcCiClassInfo getRltClassByName(Long domainId, String className);

    /**
     * 根据条件查询关系分类
     * 
     * @param cdt
     * @return
     */
    List<CcCiClassInfo> getRltClassByCdt(CCcCiClass cdt);

    /**
     * 导出属性模板
     * 
     * @param clsIds
     * @return
     */
    Resource exportAttrDefs(Set<Long> clsIds);

    /**
     * 导入属性
     * 
     * @param excelFile
     * @param clsId
     * @return
     */
    ESCIClassInfo importAttrDefs(MultipartFile excelFile, Long clsId);

    /**
     * 关系拖动排序
     * @author: weixuesong
     * @date: 2020/8/6 13:43
     * @param reOrderDTO
     * @return: void
     */
    boolean reOrder(ClassReOrderDTO reOrderDTO);

    /**
     * 初始化所有关系的orderNo
     * @author: weixuesong
     * @date: 2020/8/6 17:00
     * @return: boolean
     */
    boolean initAllOrderNo();
    boolean initAllOrderNo(Long domainId);
}
