package com.uino.plugin.client.init;

import com.uino.plugin.bean.OperatePluginDetails;
import com.uino.plugin.classloader.ClassLoaderAware;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description
 * @Date 2021/9/27
 * @Version 1.0
 */
@Component
public class DefaultClassLoaderAware implements ClassLoaderAware {

    private static Map<String, OperatePluginDetails> cacheResultMap = new HashMap<>();

    public static OperatePluginDetails getCacheResultMap(String fileName) {
        return cacheResultMap.get(fileName);
    }

    @Override
    public void afterLoaderSuccess(OperatePluginDetails operatePluginDetails) {
        cacheResultMap.put(operatePluginDetails.getFileName(), operatePluginDetails);
    }

    @Override
    public void afterUnLoaderSuccess(OperatePluginDetails operatePluginDetails) {
        cacheResultMap.put(operatePluginDetails.getFileName(), operatePluginDetails);
    }

    @Override
    public void afterLoaderFailed(OperatePluginDetails operatePluginDetails) {
        cacheResultMap.put(operatePluginDetails.getFileName(), operatePluginDetails);
    }

    @Override
    public void afterUnLoaderFailed(OperatePluginDetails operatePluginDetails) {
        cacheResultMap.put(operatePluginDetails.getFileName(), operatePluginDetails);
    }
}
