package com.uino.provider.server.web.permission.mvc;

import java.util.List;

import com.uino.bean.permission.query.CSysRoleDataModuleRlt;
import org.elasticsearch.index.query.QueryBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.binary.jdbc.Page;
import com.uino.bean.permission.base.SysDataModule;
import com.uino.bean.permission.base.SysRole;
import com.uino.bean.permission.base.SysRoleDataModuleRlt;
import com.uino.bean.permission.base.SysRoleModuleRlt;
import com.uino.bean.permission.base.SysUserDataModuleRlt;
import com.uino.bean.permission.base.SysUserModuleRlt;
import com.uino.bean.permission.base.SysUserRoleRlt;
import com.uino.bean.permission.business.DataRole;
import com.uino.bean.permission.business.ModuleNodeInfo;
import com.uino.bean.permission.business.request.OptionUserModuleAuthRequestDto;
import com.uino.bean.permission.query.CAuthBean;
import com.uino.bean.permission.query.SearchKeywordBean;
import com.uino.provider.feign.permission.RoleFeign;
import com.uino.service.permission.microservice.IRoleSvc;

/**
 * 
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("feign/role")
public class RoleFeignMvc implements RoleFeign {

    @Autowired
    private IRoleSvc roleSvc;

    @Override
    public Long saveOrUpdate(SysRole role) {
        return roleSvc.saveOrUpdate(role);
    }

    @Override
    public Integer saveOrUpdateBatch(Long domainId, List<SysRole> roles) {
        return roleSvc.saveOrUpdateBatch(domainId,roles);
    }

    @Override
    public Integer deleteById(Long roleId) {
        return roleSvc.deleteById(roleId);
    }

    @Override
    public Page<SysRole> getRolePageByQuery(SearchKeywordBean bean) {
        return roleSvc.getRolePageByQuery(bean);
    }

    @Override
    public Integer addRoleMenuRlt(Long domainId,List<SysRoleModuleRlt> bean) {
        return roleSvc.addRoleMenuRlt(domainId,bean);
    }

    @Override
	public Integer addRoleDataModuleRlt(Long domainId, List<SysRoleDataModuleRlt> rlts, boolean isComplete) {
		return roleSvc.addRoleDataModuleRlt(domainId, rlts, isComplete);
	}

	@Override
    public Integer addUserMenuRlt(Long domainId,List<SysUserModuleRlt> bean) {
        return roleSvc.addUserMenuRlt(domainId,bean);
    }

    @Override
    public Integer addUserDataModuleRlt(Long domainId,List<SysUserDataModuleRlt> bean) {
        return roleSvc.addUserDataModuleRlt(domainId,bean);
    }

    @Override
    public Long addDataModuleMenu(SysDataModule dataModule) {
        return roleSvc.addDataModuleMenu(dataModule);
    }

    @Override
    public List<DataRole> getDataRoleByCIClass(Long domainId) {
        return roleSvc.getDataRoleByCIClass(domainId);
    }

    @Override
    public List<DataRole> getDataRoleByCITag(Long domainId) {
        return roleSvc.getDataRoleByCITag(domainId);
    }

    @Override
    public List<SysDataModule> getAllDataRoleMenu(Long domainId) {
        return roleSvc.getAllDataRoleMenu(domainId);
    }

    @Override
    public ModuleNodeInfo getAllMenu(Long domainId) {
        return roleSvc.getAllMenu(domainId);
    }

    @Override
    public List<SysRoleModuleRlt> getAuthMenuByRoleId(Long roleId) {
        return roleSvc.getAuthMenuByRoleId(roleId);
    }

    @Override
    public List<SysRoleDataModuleRlt> getAuthDataRoleByRoleId(CAuthBean bean) {
        return roleSvc.getAuthDataRoleByRoleId(bean);
    }

    @Override
    public List<SysRoleDataModuleRlt> getAuthDataRoleByUserId(CAuthBean bean) {
        return roleSvc.getAuthDataRoleByUserId(bean);
    }

    @Override
    public List<SysRoleModuleRlt> getAuthMenuByUserId(Long userId) {
        return roleSvc.getAuthMenuByUserId(userId);
    }

    @Override
    public List<SysRole> getRolesByUserId(Long userId) {
        return roleSvc.getRolesByUserId(userId);
    }

    @Override
    public Object getDataModuleDataById(Long dataModuleId) {
        return roleSvc.getDataModuleDataById(dataModuleId);
    }

    @Override
    public List<SysRoleDataModuleRlt> getUserAuthDataRoleByUserId(CAuthBean bean) {
        return roleSvc.getUserAuthDataRoleByUserId(bean);
    }

    @Override
    public List<SysRoleDataModuleRlt> getRoleAuthDataRoleByUserId(CAuthBean bean) {
        return roleSvc.getRoleAuthDataRoleByUserId(bean);
    }

    @Override
    public List<SysRole> getRolesByOrgId(Long orgId) {
        return roleSvc.getRolesByOrgId(orgId);
    }

    @Override
    public long countByCondition(QueryBuilder query) {
        // TODO Auto-generated method stub
        return roleSvc.countByCondition(query);
    }

    @Override
    public List<SysRole> getRolesByQuery(QueryBuilder query) {
        // TODO Auto-generated method stub
        return roleSvc.getRolesByQuery(query);
    }

    @Override
    public void optionUserModuleAuth(OptionUserModuleAuthRequestDto req) {
        // TODO Auto-generated method stub
        roleSvc.optionUserModuleAuth(req);
    }

    @Override
    public List<SysUserRoleRlt> getUserRoleRltByRoleId(Long roleId) {
        // TODO Auto-generated method stub
        return roleSvc.getUserRoleRltByRoleId(roleId);
    }

    @Override
    public List<SysRoleDataModuleRlt> getRoleDataModuleRltByCdt(CSysRoleDataModuleRlt cdt) {
        return roleSvc.getRoleDataModuleRltByCdt(cdt);
    }

    @Override
    public Integer deleteRoleDataModuleRlt(CSysRoleDataModuleRlt cdt) {
        return roleSvc.deleteRoleDataModuleRlt(cdt);
    }
}
