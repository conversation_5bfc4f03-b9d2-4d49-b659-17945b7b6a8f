package com.uinnova.product.vmdb.comm.util;

import com.binary.core.http.HttpClient;
import com.binary.core.util.BinaryUtils;
import com.binary.json.JSON;
import com.uinnova.product.vmdb.comm.model.es.ESCiClassInfo;
import com.uinnova.product.vmdb.comm.model.es.ESCiInfo;
import com.uinnova.product.vmdb.comm.model.es.ESCiRltInfo;
import com.uinnova.product.vmdb.comm.model.es.EsTagInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.Map.Entry;

/**
 * 将基础数据(CI,CIRLT,TAG,CLASS)同步到ES中
 * 
 * <AUTHOR>
 *
 */
public class ESCommHttpSender {

    Logger logger = LoggerFactory.getLogger(ESCommHttpSender.class);

    private String esHost;

    public String getEsHost() {
        return esHost;
    }

    HttpClient tagSaveOrUpdateClient;
    HttpClient ciSaveOrUpdateClient;
    HttpClient ciRltSaveOrUpdateClient;
    HttpClient ciClsSaveOrUpdateClient;
    HttpClient tagDelClient;
    HttpClient ciDelClient;
    HttpClient ciRltDelClient;
    HttpClient ciClsDelClient;
    HttpClient ciDelByClassIdClient;

    public ESCommHttpSender(String host) {
        this.esHost = host;
        tagSaveOrUpdateClient = HttpClient.getInstance(host + "/tag/saveOrUpdateBatch");
        tagSaveOrUpdateClient.addRequestProperty("Content-Type", "application/json");
        ciSaveOrUpdateClient = HttpClient.getInstance(host + "/CI/saveOrUpdateBatch");
        ciSaveOrUpdateClient.addRequestProperty("Content-Type", "application/json");
        ciRltSaveOrUpdateClient = HttpClient.getInstance(host + "/CIRlt/saveOrUpdateBatch");
        ciRltSaveOrUpdateClient.addRequestProperty("Content-Type", "application/json");
        ciClsSaveOrUpdateClient = HttpClient.getInstance(host + "/CIClass/saveOrUpdateBatch");
        ciClsSaveOrUpdateClient.addRequestProperty("Content-Type", "application/json");
        tagDelClient = HttpClient.getInstance(host + "/tag/deleteByIDList");
        tagDelClient.addRequestProperty("Content-Type", "application/json");
        ciDelClient = HttpClient.getInstance(host + "/CI/deleteByIDList");
        ciDelClient.addRequestProperty("Content-Type", "application/json");
        ciRltDelClient = HttpClient.getInstance(host + "/CIRlt/deleteByIDList");
        ciRltDelClient.addRequestProperty("Content-Type", "application/json");
        ciClsDelClient = HttpClient.getInstance(host + "/CIClass/deleteByIDList");
        ciClsDelClient.addRequestProperty("Content-Type", "application/json");
        ciDelByClassIdClient = HttpClient.getInstance(host + "/CI/deleteByClassIds");
        ciDelByClassIdClient.addRequestProperty("Content-Type", "application/json");
    }

    public String sendTagData(List<EsTagInfo> tagInfos) {
        if (BinaryUtils.isEmpty(tagInfos)) {
            return "";
        }
        if (logger.isDebugEnabled()) {
            logger.info("sendTagData : " + JSON.toString(tagInfos));
        }
        return tagSaveOrUpdateClient.rest((String) null, JSON.toString(tagInfos));
    }

    public String sendCiData(ESCiInfo ciInfo) {
        if (ciInfo == null || BinaryUtils.isEmpty(ciInfo.getAttrs())) {
            return "";
        }
        toStdAttrs(ciInfo.getAttrs());
        if (BinaryUtils.isEmpty(ciInfo.getAttrs())) {
            return "";
        }
        HttpClient instance = HttpClient.getInstance(getEsHost() + "/CI/saveOrUpdate");
        if (logger.isDebugEnabled()) {
            logger.info("sendCiData : " + JSON.toString(ciInfo));
        }
        return instance.rest((String) null, JSON.toString(ciInfo));
    }

    public String sendCiData(List<ESCiInfo> ciInfos) {
        if (BinaryUtils.isEmpty(ciInfos)) {
            return "";
        }
        ArrayList<ESCiInfo> newList = new ArrayList<ESCiInfo>();
        for (ESCiInfo esCiInfo : ciInfos) {
            toStdAttrs(esCiInfo.getAttrs());
            if (!BinaryUtils.isEmpty(esCiInfo)) {
                newList.add(esCiInfo);
            }
        }
        if (newList.size() == 0)
            return "";

        if (logger.isDebugEnabled()) {
            logger.info("sendCiData : " + JSON.toString(ciInfos));
        }
        return ciSaveOrUpdateClient.rest((String) null, JSON.toString(newList));
    }

    public String sendCiDelByClassIds(List<Long> ids) {
        if (BinaryUtils.isEmpty(ids)) {
            return "";
        }
        return ciDelByClassIdClient.rest((String) null, JSON.toString(ids));
    }

    public String sendCiRltData(ESCiRltInfo ciInfo) {
        if (ciInfo == null) {
            return "";
        }
        toStdAttrs(ciInfo.getAttrs());
        HttpClient instance = HttpClient.getInstance(getEsHost() + "/CIRlt/saveOrUpdate");
        if (logger.isDebugEnabled()) {
            logger.info("sendCiRltData : " + JSON.toString(ciInfo));
        }
        return instance.rest((String) null, JSON.toString(ciInfo));
    }

    public String sendCiRltData(List<ESCiRltInfo> ciRltInfos) {
        if (BinaryUtils.isEmpty(ciRltInfos)) {
            return "";
        }
        for (ESCiRltInfo esCiRltInfo : ciRltInfos) {
            toStdAttrs(esCiRltInfo.getAttrs());
        }
        if (logger.isDebugEnabled()) {
            logger.info("sendCiRltData : " + JSON.toString(ciRltInfos));
        }
        return ciRltSaveOrUpdateClient.rest((String) null, JSON.toString(ciRltInfos));
    }

    public String sendCiClassData(List<ESCiClassInfo> ciClassInfos) {
        if (BinaryUtils.isEmpty(ciClassInfos)) {
            return "";
        }
        if (logger.isDebugEnabled()) {
            logger.info("sendCiClassData : " + JSON.toString(ciClassInfos));
        }
        return ciClsSaveOrUpdateClient.rest((String) null, JSON.toString(ciClassInfos));
    }

    public String sendCiDel(String[] ids) {
        if (logger.isDebugEnabled()) {
            logger.info("sendCiDel : " + JSON.toString(ids));
        }
        if (ids == null || ids.length == 0 || ids[0] == null) {
            return "false";
        } else {
            return ciDelClient.rest((String) null, JSON.toString(ids));
        }
    }

    public String sendCiRltDel(String[] ids) {
        if (logger.isDebugEnabled()) {
            logger.info("sendCiRltDel : " + JSON.toString(ids));
        }
        if (ids == null || ids.length == 0 || ids[0] == null) {
            return "false";
        } else {
            return ciRltDelClient.rest((String) null, JSON.toString(ids));
        }
    }

    public String sendCiClassDel(String[] ids) {
        if (logger.isDebugEnabled()) {
            logger.info("sendCiClassDel : " + JSON.toString(ids));
        }
        if (ids == null || ids.length == 0 || ids[0] == null) {
            return "false";
        } else {
            return ciClsDelClient.rest((String) null, JSON.toString(ids));
        }
    }

    public String sendTagDel(String[] ids) {
        if (logger.isDebugEnabled()) {
            logger.info("sendTagDel : " + JSON.toString(ids));
        }
        if (ids == null || ids.length == 0 || ids[0] == null) {
            return "false";
        } else {
            return tagDelClient.rest((String) null, JSON.toString(ids));
        }
    }

    private void toStdAttrs(Map<String, String> attrs) {
        if (attrs == null || attrs.size() == 0) {
            return;
        }
        Map<String, String> newAttrs = new HashMap<String, String>();
        Set<String> keySet = attrs.keySet();
        for (String attrName : keySet) {
            if (BinaryUtils.isEmpty(attrName)) {
                newAttrs.put(attrName.toUpperCase(), "");
            } else {
                newAttrs.put(attrName.toUpperCase(), attrs.get(attrName));
            }
        }
        attrs.clear();
        Set<Entry<String, String>> entrySet = newAttrs.entrySet();
        for (Entry<String, String> entry : entrySet) {
            attrs.put(entry.getKey(), entry.getValue());
        }
    }

}
