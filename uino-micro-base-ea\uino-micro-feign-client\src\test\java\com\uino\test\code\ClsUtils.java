package com.uino.test.code;

import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.core.type.classreading.CachingMetadataReaderFactory;
import org.springframework.core.type.classreading.MetadataReader;
import org.springframework.core.type.classreading.MetadataReaderFactory;
import org.springframework.util.ClassUtils;

public class ClsUtils {

    public static Resource[] findResource(String packageStr) throws Exception {
        String path = getParttern(packageStr);
        ResourcePatternResolver patternResolver = new PathMatchingResourcePatternResolver();
        return patternResolver.getResources(path);
    }

    public static Class<?>[] findClass(Resource[] resources) throws Exception {
        Class<?>[] ret = new Class<?>[resources.length];
        MetadataReaderFactory metadataReaderFactory = new CachingMetadataReaderFactory();
        for (int i = 0; i < resources.length; i++) {
            Resource resource = resources[i];
            MetadataReader metadataReader = metadataReaderFactory.getMetadataReader(resource);
            String className = metadataReader.getClassMetadata().getClassName();
            Class<?> clazz = ClassUtils.forName(className, ClassUtils.getDefaultClassLoader());
            ret[i] = clazz;
        }
        return ret;
    }

    public static String getParttern(String path) {
        return ResourcePatternResolver.CLASSPATH_ALL_URL_PREFIX + ClassUtils.convertClassNameToResourcePath(path);
    }
}
