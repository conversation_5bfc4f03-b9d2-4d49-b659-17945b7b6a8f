package com.uino.bean.sys.business;

import com.binary.framework.bean.annotation.Comment;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 查询条件
 * 
 * <AUTHOR>
 *
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Comment("登录集成查询条件")
@ApiModel(value="登录集成查询条件类",description = "登录集成查询条件")
public class LoginAuthConfigDto {

    @Builder.Default
    @ApiModelProperty(value="页码",example = "1")
    @Comment("页码")
    private int pageNum = 1;

    @ApiModelProperty(value="每页行数",example = "20")
    @Builder.Default
    @Comment(" 每页行数")
    private int pageSize = 20;

    @ApiModelProperty(value="协议名称",example = "uino")
    @Comment("协议名称")
    private String protoName;

    @ApiModelProperty(value = "协议状态",example = "0")
    @Comment("协议状态::0=无效 1=有效")
    private Integer protoStatus;
}
