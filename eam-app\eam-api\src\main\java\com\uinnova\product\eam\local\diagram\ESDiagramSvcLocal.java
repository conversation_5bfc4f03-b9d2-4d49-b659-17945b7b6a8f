package com.uinnova.product.eam.local.diagram;

import com.binary.core.exception.BinaryException;
import com.binary.jdbc.Page;
import com.binary.json.JSONArray;
import com.binary.json.JSONObject;
import com.uinnova.product.eam.api.diagram.ESDiagramApiClient;
import com.uinnova.product.eam.base.diagram.enums.DiagramCopyEnum;
import com.uinnova.product.eam.base.diagram.model.*;
import com.uinnova.product.eam.model.diagram.*;
import com.uinnova.product.eam.model.diagram.event.RuleParams;
import com.uinnova.product.eam.service.diagram.ESDiagramSvc;
import com.uinnova.product.eam.service.diagram.EsDiagramSvcV2;
import com.uinnova.product.eam.service.diagram.event.HiddenComponentLinkListener;
import org.elasticsearch.index.query.QueryBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Service
public class ESDiagramSvcLocal implements ESDiagramApiClient {

    @Autowired
    private ESDiagramSvc esDiagramSvc;
    @Autowired
    private EsDiagramSvcV2 diagramSvcV2;
    @Autowired
    private HiddenComponentLinkListener hiddenComponentLinkListener;

    @Override
    public Map<String, String> saveESDiagram(ESDiagramInfoDTO esDiagramInfo) {
        return esDiagramSvc.saveESDiagram(esDiagramInfo);
    }

    @Override
    public List<ESDiagramDTO> queryDiagramInfoByIds(Long[] diagramIds, String type, Boolean needAuth, Boolean asset) {
        return esDiagramSvc.queryDiagramInfoByIds(diagramIds, type, needAuth, asset);
    }

    @Override
    public String deleteDiagramByIds(JSONObject jsonObject, JSONArray opArr) {
        throw new BinaryException("批量删除暂不支持");
    }

    @Override
    public String deleteDiagramById(Long diagramId) {
        return esDiagramSvc.deleteDiagramById(diagramId);
    }

    @Override
    public Integer deleteDiagramByIds(Long[] diagramIds) {
        return esDiagramSvc.deleteDiagramByIds(diagramIds);
    }

    @Override
    public List<ESDiagramNode> selectNodeByDiagramIds(List<Long> diagramIds) {
        return esDiagramSvc.selectNodeByDiagramIds(diagramIds);
    }

    @Override
    public List<ESDiagram> selectDiagramsFromRecycle(List<Long> dirIds, String ownerCode, List<String> diagramIds) {
        return esDiagramSvc.selectDiagramsFromRecycle(dirIds,ownerCode,diagramIds);
    }

    @Override
    public List<ESDiagramLink> selectLinkByDiagramIds(List<Long> diagramIds) {
        return esDiagramSvc.selectLinkByDiagramIds(diagramIds);
    }

    @Override
    public List<ESDiagramNode> selectNodeByCiCodes(List<String> ciCodes, String ownerCode) {
        return esDiagramSvc.selectNodeByCiCodes(ciCodes, ownerCode);
    }

    @Override
    public List<ESDiagramLink> selectLinkByRltCiCodes(List<String> uniqueCodes) {
        return esDiagramSvc.selectLinkByRltCiCodes(uniqueCodes);
    }

    @Override
    public List<ESDiagramLink> selectLinkByRltCodes(Collection<String> uniqueCodes, String ownerCode) {
        return esDiagramSvc.selectLinkByRltCodes(uniqueCodes, ownerCode);
    }

    @Override
    public List<ESDiagram> selectByIds(Collection<String> diagramIds, List<Integer> dirTypes, List<Integer> isOpens) {
        return esDiagramSvc.selectByIds(diagramIds, dirTypes, isOpens);
    }

    @Override
    public List<ESDiagram> queryByArtifactIds(List<Long> artifactIds) {
        return diagramSvcV2.queryByArtifactIds(artifactIds);
    }

    @Override
    public List<ESDiagram> getByIds(Collection<Long> diagramIds, List<Integer> dirTypes, List<Integer> isOpens) {
        return esDiagramSvc.getByIds(diagramIds, dirTypes, isOpens);
    }

    @Override
    public List<ESDiagram> selectMyOwnDiagramList(String ownerCode, List<Integer> dirTypes) {
        return esDiagramSvc.selectMyOwnDiagramList(ownerCode, dirTypes);
    }

    @Override
    public void replaceLinkList(List<ESDiagramLink> linkList) {
        esDiagramSvc.replaceLinkList(linkList);
    }

    @Override
    public void replaceNodeList(List<ESDiagramNode> nodeList) {
        esDiagramSvc.replaceNodeList(nodeList);
    }

    @Override
    public int saveLinkList(List<ESDiagramLink> linkList) {
        return esDiagramSvc.saveLinkList(linkList);
    }

    @Override
    public int saveNodeList(List<ESDiagramNode> nodeList) {
        return esDiagramSvc.saveNodeList(nodeList);
    }

    @Override
    public int delLinkList(List<Long> ids, List<String> keys) {
        return esDiagramSvc.delLinkList(ids, keys);
    }

    @Override
    public int delNodeList(List<Long> ids, List<String> keys) {
        return esDiagramSvc.delNodeList(ids, keys);
    }

    @Override
    public DiagramPushResponse diagramPush(DiagramPushRequest request) {
        return diagramSvcV2.diagramPush(request);
    }

    @Override
    public DiagramPullResponse diagramPull(DiagramPullRequest request) {
        return diagramSvcV2.diagramPull(request);
    }

    @Override
    public Page<VcDiagramInfo> queryDiagramInfoPage(Long domainId, Integer pageNum, Integer pageSize, CVcDiagram cdt, String orders) {
        return esDiagramSvc.queryDiagramInfoPage(domainId, pageNum, pageSize, cdt, orders);
    }

    @Override
    public List<Long> saveESDiagramBatch(List<ESDiagramInfoDTO> esDiagramInfoList, String newName, Long newDirId, String type) {
        return esDiagramSvc.saveESDiagramBatch(esDiagramInfoList, newName, newDirId, type);
    }


    @Override
    public Map<String, List<ESResponseStruct>> saveOrUpdateDiagramComponent(String jsonStr) {
        // return esDiagramSvc.saveOrUpdateDiagramComponent(jsonStr);
        return null;
    }

    @Override
    public ESDiagramDTO queryESDiagramInfoById(Long diagramId, String type, Boolean versionFlag) {
        return esDiagramSvc.queryESDiagramInfoById(diagramId, type, versionFlag);
    }

    @Override
    public ESDiagramDTO updateFullDiagramNew(Long diagramId, ESDiagramInfoDTO esDiagramInfo) {
        return esDiagramSvc.updateFullDiagramNew(diagramId, esDiagramInfo);
    }

    @Override
    public String copyDiagramById(ESDiagramMoveCdt diagramMoveCdt) {
        return esDiagramSvc.copyDiagramById(diagramMoveCdt);
    }

    @Override
    public Integer moveDirAndDiagram(MoveDirAndDiagramCdt move) {
        // return esDiagramSvc.moveDirAndDiagram(move);
        return null;
    }

    @Override
    public List<Long> copyDiagramByIds(ESDiagramMoveCdt diagramMoveCdt) {
        return esDiagramSvc.copyDiagramByIds(diagramMoveCdt);
    }

    @Override
    public List<Long> copyDirById(Long targetDirId, Long[] dirIds, String[] diagramIds) {
        // return esDiagramSvc.copyDirById(targetDirId, dirIds, diagramIds);
        return null;
    }

    @Override
    public void processBatchThumbnail(ThumbnailBatch thumbnailBatch) {
        esDiagramSvc.processBatchThumbnail(thumbnailBatch);
    }

    @Override
    public Page<ESSimpleDiagramDTO> queryESDiagramInfoPage(Long domainId, Integer pageNum, Integer pageSize, CVcDiagram cdt, String orders) {
        return esDiagramSvc.queryESDiagramInfoPage(domainId, pageNum, pageSize, cdt, orders);
    }

    @Override
    public Long[] queryDiagramInfoBydEnergy(String[] diagramIds) {
        return esDiagramSvc.queryDiagramInfoBydEnergy(diagramIds);
    }

    @Override
    public Long queryDiagramInfoByEnergy(String diagramId) {
        return esDiagramSvc.queryDiagramInfoByEnergy(diagramId);
    }

    @Override
    public Long getCountByDirId(Long dirId, Integer type) {
        // return esDiagramSvc.getCountByDirId(dirId, type);
        return null;
    }

    @Override
    public void removeDiagrams(List<Long> dirIds, String ownerCode) {
        esDiagramSvc.removeDiagrams(dirIds, ownerCode);
    }

    @Override
    public List<ESDiagram> findEsDiagramList(Long dirId, String ownerCode) {
        return esDiagramSvc.findEsDiagramList(dirId, ownerCode);
    }

    @Override
    public ESDiagram getEsDiagram(String dEnergy, Integer isOpen) {
        return esDiagramSvc.getEsDiagram(dEnergy, isOpen);
    }

    @Override
    public int updateNameByDiagramId(String newName, String dEnergyId) {
        return esDiagramSvc.updateNameByDEnergyId(newName, dEnergyId);
    }

    @Override
    public List<ESDiagram> selectByDirIds(Collection<Long> dirIds, List<Integer> dirTypes, String ownerCode, List<Integer> isOpens) {
        return esDiagramSvc.selectByDirIds(dirIds, dirTypes, ownerCode, isOpens);
    }

    @Override
    public Boolean clearReleaseDiagramIdBydEnergyId(Collection<String> dEnergyId,String ownerCode) {
        return esDiagramSvc.clearReleaseDiagramIdBydEnergyId(dEnergyId,ownerCode);
    }

    @Override
    public List<Long> fxCopyDiagramByIds(ESDiagramMoveCdt diagramMoveCdt) {
        return esDiagramSvc.fxCopyDiagramByIds(diagramMoveCdt);
    }

    @Override
    public List<ESDiagram> selectByDirType(Integer dirTypes, String ownerCode, List<Integer> isOpens) {
        return esDiagramSvc.selectByDirType(dirTypes, ownerCode, isOpens);
    }

    @Override
    public int createHiddenLink(RuleParams params) {
        hiddenComponentLinkListener.process(params);
        return 1;
    }

    @Override
    public ESDiagram querySimpleDiagramInfoById(Long diagramId) {
        return esDiagramSvc.querySimpleDiagramInfoById(diagramId);
    }

    @Override
    public List<ESDiagram> queryDBDiagramInfoByIds(String[] toArray) {
        return esDiagramSvc.queryDBDiagramInfoByIds(toArray);
    }

    @Override
    public Long[] queryDBDiagramInfoBydEnergy(String[] diagramIds) {
        return esDiagramSvc.queryDBDiagramInfoBydEnergy(diagramIds);
    }

    @Override
    public Map<String, Set<DiagramRelationInfo>> getRelateInfoByDiagramIds(String diagramId, Integer browseStatus) {
        return esDiagramSvc.getRelateInfoByDiagramIds(diagramId, browseStatus);
    }

    @Override
    public Page<ESDiagram> selectListByQuery(Integer pageNum, Integer pageSize, QueryBuilder query) {
        return esDiagramSvc.selectListByQuery(pageNum, pageSize, query);
    }

    @Override
    public List<ESDiagramDTO> queryFullDiagramByIds(List<Long> diagramIds) {
        return diagramSvcV2.queryDiagramByIds(diagramIds);
    }

    @Override
    public List<ESDiagramDTO> queryFullDiagramByIds(Collection<String> diagramIds) {
        return diagramSvcV2.queryDiagramByIds(diagramIds);
    }

    @Override
    public Integer saveOrUpdateBatch(List<ESDiagram> list) {
        return esDiagramSvc.saveOrUpdateBatch(list);
    }

    @Override
    public String deleteDiagramWithType(List<String> diagramIds, Long delDirId, Integer type) {
        return diagramSvcV2.deleteDiagramWithType(diagramIds, delDirId, type);
    }

    @Override
    public Integer queryFlowStatusById(String diagramId) {
        return esDiagramSvc.queryFlowStatusById(diagramId);
    }

    @Override
    public Map<String, String> copyDiagramBatch(Map<String, Long> diagramDirIdMap, List<ESDiagram> diagramList, DiagramCopyEnum type) {
        return diagramSvcV2.copyDiagramBatch(diagramDirIdMap, diagramList, type);
    }


}
