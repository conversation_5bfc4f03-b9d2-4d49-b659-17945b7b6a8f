package com.uino.util.message.queue.service.consumer;


import com.uino.util.message.queue.MessageQueueConsumer;

/**
 * @Title: MessageQueueHandler
 * @Description:
 * @Author: YGQ
 * @Create: 2021-05-25 11:04
 **/
public interface MessageQueueHandler {
    /**
     * Message monitoring service implementation class,
     * the caller should implement this interface when using {@link MessageQueueConsumer} to monitor the received data,
     * And return true or false in the method.
     *
     * @param data Monitored message
     * @return boolean  true means the message was successfully received and confirmed,false means the message was not successfully received
     */
    boolean message(String data);
}
