package com.uino.web.exter.mvc;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.binary.framework.util.ControllerUtils;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClass;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uino.dao.cmdb.ESCmdbCommSvc;
import com.uino.bean.cmdb.base.ESCIClassInfo;
import com.uino.api.client.cmdb.IRltClassApiSvc;
import com.uino.web.BaseMvc;

/**
 * @Classname DixRltClassMvc
 * @Description TODO
 * @Date 2020/7/23 15:03
 * <AUTHOR> sh
 */
@RestController
@RequestMapping("/dix/rltClass")
public class DixRltClassMvc extends BaseMvc {

    @Autowired
    private IRltClassApiSvc rltClassApiSvc;

    @Autowired
    private ESCmdbCommSvc commSvc;

    /**
     * 获取所有关系分类
     *
     */
    @PostMapping("getAllRltClassInfo")
    @ModDesc(desc = "查询所有关系分类", pDesc = "无", rDesc = "关系分类列表", rType = List.class, rcType = CcCiClassInfo.class)
    public void getAllRltClassInfo() {
        List<CcCiClassInfo> results = rltClassApiSvc.queryAllClasses();
        ControllerUtils.returnJson(request, response, results);
    }

    /**
     * 根据关系分类名称筛选关系分类
     * 
     * @param clsName
     */
    @PostMapping("getRltClassInfo")
    public void getRltClassInfo(@RequestBody JSONObject body) throws Exception {
    	if (body.containsKey("name") && body.getString("name")!=null && !"".equals(body.getString("name").trim())) {
    		CCcCiClass cdt = new CCcCiClass();
            cdt.setClassNameEqual(body.getString("name").trim());
            List<CcCiClassInfo> classInfoList = rltClassApiSvc.getRltClassByCdt(cdt);
            if (classInfoList!=null && classInfoList.size()>0) {
            	ControllerUtils.returnJson(request, response, classInfoList.get(0));
            } else {
            	throw new Exception("rlt class is not exist");
            }
    	} else {
    		throw new Exception("lack of name");
    	}
    }

    @PostMapping("saveOrUpdateRltClassInfo")
    public void saveOrUpdateRltClassInfo(@RequestBody JSONObject body) throws Exception {
    	String className = null;
    	if (body.containsKey("className") && body.getString("className")!=null && !"".equals(body.getString("className").trim())) {
    		className = body.getString("className").trim();
    	} else {
    		throw new Exception("lack of className");
    	}
    	List<CcCiAttrDef> attrDefs = new ArrayList<CcCiAttrDef>();
    	if (body.containsKey("attrDefs") && body.getJSONArray("attrDefs")!=null) {
    		for (int i=0;i<body.getJSONArray("attrDefs").size();i++) {
    			JSONObject attrDefObj = body.getJSONArray("attrDefs").getJSONObject(i);
    			CcCiAttrDef attrDef = new CcCiAttrDef();
    			if (attrDefObj.containsKey("proName") && attrDefObj.getString("proName")!=null && !"".equals(attrDefObj.getString("proName").trim())) {
    				attrDef.setProName(attrDefObj.getString("proName").trim());
    			} else {
    				throw new Exception("attrDef is lack of proName");
    			}
    			if (attrDefObj.containsKey("proType") && attrDefObj.getInteger("proType")!=null) {
    				attrDef.setProType(attrDefObj.getInteger("proType"));
    			} else {
    				throw new Exception("attrDef is lack of proType");
    			}
    			if (attrDefObj.containsKey("isMajor") && attrDefObj.getBoolean("isMajor")!=null) {
    				if (attrDefObj.getBoolean("isMajor")) {
    					attrDef.setIsMajor(1);
    				} else {
    					attrDef.setIsMajor(0);
    				}
    			} else {
    				throw new Exception("attrDef is lack of isMajor");
    			}
    			attrDef.setIsCiDisp(0);
    			attrDef.setLineLabelAlign(1);;
    			attrDefs.add(attrDef);
    		}
    	} else {
    		throw new Exception("lack of attrDefs");
    	}
    	
    	ESCIClassInfo rltClassInfo = null;
		CCcCiClass cdt = new CCcCiClass();
		cdt.setClassNameEqual(className);
		List<CcCiClassInfo> classInfos = rltClassApiSvc.getRltClassByCdt(cdt);
		if (classInfos!=null && classInfos.size()>0) {
			rltClassInfo = commSvc.tranESCIClassInfo(classInfos.get(0));
			Map<String, CcCiAttrDef> oldAttrDefMap = new HashMap<String, CcCiAttrDef>();
			if (classInfos.get(0).getAttrDefs()!=null) {
				for (CcCiAttrDef oldAttrDef:classInfos.get(0).getAttrDefs()) {
					oldAttrDefMap.put(oldAttrDef.getProName(), oldAttrDef);
				}
			}
			List<CcCiAttrDef> newAttrDefs = new ArrayList<CcCiAttrDef>();
			for (CcCiAttrDef attrDef:attrDefs) {
				CcCiAttrDef oldAttrDef = oldAttrDefMap.get(attrDef.getProName());
				if (oldAttrDef!=null) {
					oldAttrDef.setProName(attrDef.getProName());
					oldAttrDef.setProType(attrDef.getProType());
					oldAttrDef.setIsMajor(attrDef.getIsMajor());
					newAttrDefs.add(oldAttrDef);
				} else {
					newAttrDefs.add(attrDef);
				}
			}
			attrDefs = newAttrDefs;
		} else {
			rltClassInfo = new ESCIClassInfo();
			rltClassInfo.setClassName(className);
			rltClassInfo.setClassCode(className);
			rltClassInfo.setParentId(0L);
			rltClassInfo.setLineBorder(1);
			rltClassInfo.setLineColor("#000");
			rltClassInfo.setLineDirect("none");
			rltClassInfo.setLineDispType(1);
			rltClassInfo.setLineType("solid");
		}
        rltClassInfo.setCcAttrDefs(attrDefs);
		rltClassApiSvc.saveOrUpdate(rltClassInfo);
		ControllerUtils.returnJson(request, response, true);
    }
    
    @PostMapping("deleteRltClassInfo")
    public void deleteRltClassInfo(@RequestBody JSONObject body) throws Exception {
    	if (body.containsKey("className") && body.getString("className")!=null && !"".equals(body.getString("className").trim())) {
    		CCcCiClass cdt = new CCcCiClass();
    		cdt.setClassNameEqual(body.getString("className").trim());
			List<CcCiClassInfo> classInfos = rltClassApiSvc.getRltClassByCdt(cdt);
			if (classInfos!=null && classInfos.size()>0) {
				Long classId = classInfos.get(0).getCiClass().getId();
				Set<Long> delIds = new HashSet<Long>();
				delIds.add(classId);
				if (rltClassApiSvc.deleteByIds(delIds)>0) {
					ControllerUtils.returnJson(request, response, true);
				} else {
					throw new Exception("fail to delete rlt class");
				}
			} else {
				throw new Exception("rlt class is not exist");
			}
    	} else {
    		throw new Exception("lack of className");
    	}
    }

    
}
