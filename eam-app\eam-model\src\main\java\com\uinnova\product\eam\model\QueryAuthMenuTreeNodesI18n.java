package com.uinnova.product.eam.model;

public class QueryAuthMenuTreeNodesI18n {
	private Long domainId; 
	private Long userId;
	private String[] rootCodes;
	private String curModuCode;
	
	private String selectedModuCode;
	private String menuNameLike;
	private String languageCode;
	public Long getDomainId() {
		return domainId;
	}
	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}
	public Long getUserId() {
		return userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}
	public String[] getRootCodes() {
		return rootCodes;
	}
	public void setRootCodes(String[] rootCodes) {
		this.rootCodes = rootCodes;
	}
	public String getCurModuCode() {
		return curModuCode;
	}
	public void setCurModuCode(String curModuCode) {
		this.curModuCode = curModuCode;
	}
	public String getSelectedModuCode() {
		return selectedModuCode;
	}
	public void setSelectedModuCode(String selectedModuCode) {
		this.selectedModuCode = selectedModuCode;
	}
	public String getMenuNameLike() {
		return menuNameLike;
	}
	public void setMenuNameLike(String menuNameLike) {
		this.menuNameLike = menuNameLike;
	}
	public String getLanguageCode() {
		return languageCode;
	}
	public void setLanguageCode(String languageCode) {
		this.languageCode = languageCode;
	}
	
}
