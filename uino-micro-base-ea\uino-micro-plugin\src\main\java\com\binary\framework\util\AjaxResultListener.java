package com.binary.framework.util;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

public interface AjaxResultListener {



	/***
	 * ajax返回结果写之前事件
	 * @param request
	 * @param response
	 * @param result
	 * @return 如果返回false则中断后续操作
	 */
	public boolean beforeWrite(HttpServletRequest request, HttpServletResponse response, Object result);





	/***
	 * ajax返回结果写之后事件
	 * @param request
	 * @param response
	 * @param result
	 */
	public void afterWrite(HttpServletRequest request, HttpServletResponse response, Object result);

}
