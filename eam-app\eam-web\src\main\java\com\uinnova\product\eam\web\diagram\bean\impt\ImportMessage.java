package com.uinnova.product.eam.web.diagram.bean.impt;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import com.binary.core.util.BinaryUtils;

public class ImportMessage {
	private String curProcessSheet;
	private boolean simpleMode;
	private Map<String, ImportSheetMessage> messageMap;
	private Map<String, Map<Integer, ImportRowMessage>> sheetRowMessageMap;
	private ImportSheetMessage simpleSheetMessage;
	public ImportMessage() {
		this.messageMap = new LinkedHashMap<String, ImportSheetMessage>();
		this.sheetRowMessageMap = new HashMap<String,Map<Integer, ImportRowMessage>>();
		simpleMode = true;
	}

	public ImportMessage(boolean simpleMode) {
		this.simpleMode = simpleMode;
		if (!simpleMode) {
			this.sheetRowMessageMap = new HashMap<String,Map<Integer, ImportRowMessage>>();
			this.messageMap = new HashMap<String, ImportSheetMessage>();
		}
	}

	/**
	 * 导入分类的状态
	 * @param sheetName 分类名或者sheet名字
	 * @param exist 分类是否存在
	 */
	public void processClass(String sheetName, boolean exist) {
		if (!simpleMode) {
			curProcessSheet = sheetName;
			ImportSheetMessage sheetMsg = getSheetMsg(sheetName);
			sheetMsg.setSuccess(exist ? 1 : 0);
		}
	}
	
	public void appendRowAttrs(Integer index, Map<String, String> attrs) {
		if (!simpleMode) {
			ImportRowMessage rowMsg = getRowMsg(index);
			rowMsg.setAttrs(attrs);
		}
	}
	
	public void appendRowMsg(Integer index, String fieldName, Integer errType) {
		appendRowMsg(index, fieldName, errType, null);
	}
	
	public void appendRowMsg(Integer index, String fieldName, Integer errType, Map<String, String> attrs) {
		if (!simpleMode) {
			ImportRowMessage rowMsg = getRowMsg(index);
			List<ImportCellMessage> messageItems = rowMsg.getMessageItems();
			
			//检查错误列表中是否已经存在相同的类型且相同字段的错误提示项
			boolean isErr = false;
			if(!BinaryUtils.isEmpty(messageItems)) {
				for (ImportCellMessage cm : messageItems) {
					Integer etype = cm.getErrorType();
					String fname = cm.getFieldName();
					if(BinaryUtils.isEmpty(etype) || BinaryUtils.isEmpty(fname)) continue;
					//如果错误列表有相同的错误信息则略过(避免错误提示重复)
					if(etype.intValue() == errType.intValue() && fname.equals(fieldName)) {
						isErr = true;
						break;
					}
				}
			}
			
			ImportCellMessage e = new ImportCellMessage();
			e.setErrorType(errType);
			e.setFieldName(fieldName);
			if(!isErr) messageItems.add(e);
			if (null != attrs) {
				rowMsg.setAttrs(attrs);
			}
		}
	}
	
	public void appendProcessResult(Integer insertNum,Integer updateNum,Integer ignoreNum) {
		if (!simpleMode) {
			ImportSheetMessage sheetMsg = getSheetMsg(curProcessSheet);
			sheetMsg.setIgnoreNum(ignoreNum);
			sheetMsg.setInsertNum(insertNum);
			sheetMsg.setUpdateNum(updateNum);
		}	
	}
	
	public void appendMessage(String processedMsg){
		if(simpleMode){
			simpleSheetMessage = new ImportSheetMessage();
			simpleSheetMessage.setSuccess(1);simpleSheetMessage.setProcessedMsg(processedMsg);
		}
	}
	
	public List<ImportSheetMessage> getResultMessage(){
		if(simpleMode){
			 ArrayList<ImportSheetMessage> ret = new ArrayList<ImportSheetMessage>();
			 ret.add(simpleSheetMessage);
			 return ret;
		}else{
			return new ArrayList<ImportSheetMessage>(messageMap.values());
		}
	}
	
	private ImportSheetMessage getSheetMsg(String sheetName) {
		ImportSheetMessage msg = messageMap.get(sheetName);
		if (msg == null) {
			msg = new ImportSheetMessage(sheetName);
			messageMap.put(sheetName, msg);
		}
		return msg;
	}

	private ImportRowMessage getRowMsg(Integer index) {
		Map<Integer, ImportRowMessage> map = this.sheetRowMessageMap.get(curProcessSheet);
		if(map == null) {
			map = new HashMap<Integer, ImportRowMessage>();
			this.sheetRowMessageMap.put(curProcessSheet, map);
		}
		ImportRowMessage rowMessage = map.get(index);
		if (rowMessage == null) {
			rowMessage = new ImportRowMessage();
			rowMessage.setRowNum(index);
			map.put(index, rowMessage);
			
			ImportSheetMessage messageResult = getSheetMsg(curProcessSheet);
			List<ImportRowMessage> rowMessages = messageResult.getRowMessages();
			if (rowMessages == null) {
				rowMessages = new ArrayList<ImportRowMessage>();
				messageResult.setRowMessages(rowMessages);
			}
			rowMessage.setMessageItems(new ArrayList<ImportCellMessage>());
			rowMessages.add(rowMessage);
		}
		
		return rowMessage;
	}

	@Override
	public String toString() {
		return super.toString();
//		StringBuffer msgAll = new StringBuffer(1000);
//		Set<String> keySet = sheetMsgMap.keySet();
//		for (String string : keySet) {
//			StringBuffer stringBuffer = sheetMsgMap.get(string);
//			msgAll.append(stringBuffer);
//			msgAll.append(NEW_LINE);
//			Map<Integer, StringBuffer> map = clsRowMsgMap.get(string);
//			if (map != null) {
//				ArrayList<Integer> rowNums = new ArrayList<Integer>(
//						map.keySet());
//				Collections.sort(rowNums, new Comparator<Integer>() {
//					@Override
//					public int compare(Integer o1, Integer o2) {
//						return o1 - o2;
//					}
//				});
//				for (Integer integer : rowNums) {
//					msgAll.append(map.get(integer));
//					msgAll.append(NEW_LINE);
//				}
//			}
//		}
//		return msgAll.toString();
	}

}
