<?xml version="1.0" encoding="UTF-8"?><!--Converted at: Sat Dec 08 16:22:32 CST 2018-->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="VC_CI_LINKED">


	<resultMap id="queryResult" type="com.uinnova.product.eam.comm.model.VcCiLinked">
		<result property="id" column="ID" jdbcType="BIGINT"/>	<!-- ID -->
		<result property="linkedName" column="LINKED_NAME" jdbcType="VARCHAR"/>	<!-- 链路名称 -->
		<result property="sourceType" column="SOURCE_TYPE" jdbcType="INTEGER"/>	<!-- 来源类型 -->
		<result property="isDisplay" column="IS_DISPLAY" jdbcType="INTEGER"/>	<!-- 是否显示 -->
		<result property="sourceId" column="SOURCE_ID" jdbcType="BIGINT"/>	<!-- 来源ID -->
		<result property="ciCodes" column="CI_CODES" jdbcType="VARCHAR"/>	<!-- CI代码列表 -->
		<result property="linkedColor" column="LINKED_COLOR" jdbcType="VARCHAR"/>	<!-- 链路颜色 -->
		<result property="custom1" column="CUSTOM_1" jdbcType="VARCHAR"/>	<!-- 备用_1 -->
		<result property="custom2" column="CUSTOM_2" jdbcType="VARCHAR"/>	<!-- 备用_2 -->
		<result property="custom3" column="CUSTOM_3" jdbcType="VARCHAR"/>	<!-- 备用_3 -->
		<result property="custom4" column="CUSTOM_4" jdbcType="VARCHAR"/>	<!-- 备用_4 -->
		<result property="custom5" column="CUSTOM_5" jdbcType="VARCHAR"/>	<!-- 备用_5 -->
		<result property="custom6" column="CUSTOM_6" jdbcType="VARCHAR"/>	<!-- 备用_6 -->
		<result property="domainId" column="DOMAIN_ID" jdbcType="BIGINT"/>	<!-- 所属域 -->
		<result property="dataStatus" column="DATA_STATUS" jdbcType="INTEGER"/>	<!-- 数据状态 -->
		<result property="creator" column="CREATOR" jdbcType="VARCHAR"/>	<!-- 创建人 -->
		<result property="modifier" column="MODIFIER" jdbcType="VARCHAR"/>	<!-- 修改人 -->
		<result property="createTime" column="CREATE_TIME" jdbcType="BIGINT"/>	<!-- 创建时间 -->
		<result property="modifyTime" column="MODIFY_TIME" jdbcType="BIGINT"/>	<!-- 修改时间 -->
	</resultMap>
	

	<sql id="sql_query_where">
		<if test="cdt != null and cdt.id != null">and
			ID = #{cdt.id:BIGINT}
		</if>
		<if test="ids != null and ids != ''">and
			ID in (${ids})
		</if>
		<if test="cdt != null and cdt.startId != null">and
			 ID &gt;= #{cdt.startId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endId != null">and
			 ID &lt;= #{cdt.endId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.linkedName != null and cdt.linkedName != ''">and
			LINKED_NAME like #{cdt.linkedName,jdbcType=VARCHAR} 
		</if>
		<if test="cdt != null and cdt.linkedNameEqual != null and cdt.linkedNameEqual != ''">and
			LINKED_NAME = #{cdt.linkedNameEqual,jdbcType=VARCHAR}
		</if>
		<if test="linkedNames != null and linkedNames != ''">and
			LINKED_NAME in (${linkedNames})
		</if>
		<if test="cdt != null and cdt.sourceType != null">and
			SOURCE_TYPE = #{cdt.sourceType:INTEGER}
		</if>
		<if test="sourceTypes != null and sourceTypes != ''">and
			SOURCE_TYPE in (${sourceTypes})
		</if>
		<if test="cdt != null and cdt.startSourceType != null">and
			 SOURCE_TYPE &gt;= #{cdt.startSourceType:INTEGER} 
		</if>
		<if test="cdt != null and cdt.endSourceType != null">and
			 SOURCE_TYPE &lt;= #{cdt.endSourceType:INTEGER} 
		</if>
		<if test="cdt != null and cdt.isDisplay != null">and
			IS_DISPLAY = #{cdt.isDisplay:INTEGER}
		</if>
		<if test="isDisplays != null and isDisplays != ''">and
			IS_DISPLAY in (${isDisplays})
		</if>
		<if test="cdt != null and cdt.startIsDisplay != null">and
			 IS_DISPLAY &gt;= #{cdt.startIsDisplay:INTEGER} 
		</if>
		<if test="cdt != null and cdt.endIsDisplay != null">and
			 IS_DISPLAY &lt;= #{cdt.endIsDisplay:INTEGER} 
		</if>
		<if test="cdt != null and cdt.sourceId != null">and
			SOURCE_ID = #{cdt.sourceId:BIGINT}
		</if>
		<if test="sourceIds != null and sourceIds != ''">and
			SOURCE_ID in (${sourceIds})
		</if>
		<if test="cdt != null and cdt.startSourceId != null">and
			 SOURCE_ID &gt;= #{cdt.startSourceId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endSourceId != null">and
			 SOURCE_ID &lt;= #{cdt.endSourceId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.ciCodes != null and cdt.ciCodes != ''">and
			CI_CODES like #{cdt.ciCodes,jdbcType=VARCHAR} 
		</if>
		<if test="cdt != null and cdt.linkedColor != null and cdt.linkedColor != ''">and
			LINKED_COLOR like #{cdt.linkedColor,jdbcType=VARCHAR} 
		</if>
		<if test="cdt != null and cdt.linkedColorEqual != null and cdt.linkedColorEqual != ''">and
			LINKED_COLOR = #{cdt.linkedColorEqual,jdbcType=VARCHAR}
		</if>
		<if test="linkedColors != null and linkedColors != ''">and
			LINKED_COLOR in (${linkedColors})
		</if>
		<if test="cdt != null and cdt.custom1 != null and cdt.custom1 != ''">and
			CUSTOM_1 like #{cdt.custom1,jdbcType=VARCHAR} 
		</if>
		<if test="cdt != null and cdt.custom1Equal != null and cdt.custom1Equal != ''">and
			CUSTOM_1 = #{cdt.custom1Equal,jdbcType=VARCHAR}
		</if>
		<if test="custom1s != null and custom1s != ''">and
			CUSTOM_1 in (${custom1s})
		</if>
		<if test="cdt != null and cdt.custom2 != null and cdt.custom2 != ''">and
			CUSTOM_2 like #{cdt.custom2,jdbcType=VARCHAR} 
		</if>
		<if test="cdt != null and cdt.custom2Equal != null and cdt.custom2Equal != ''">and
			CUSTOM_2 = #{cdt.custom2Equal,jdbcType=VARCHAR}
		</if>
		<if test="custom2s != null and custom2s != ''">and
			CUSTOM_2 in (${custom2s})
		</if>
		<if test="cdt != null and cdt.custom3 != null and cdt.custom3 != ''">and
			CUSTOM_3 like #{cdt.custom3,jdbcType=VARCHAR} 
		</if>
		<if test="cdt != null and cdt.custom3Equal != null and cdt.custom3Equal != ''">and
			CUSTOM_3 = #{cdt.custom3Equal,jdbcType=VARCHAR}
		</if>
		<if test="custom3s != null and custom3s != ''">and
			CUSTOM_3 in (${custom3s})
		</if>
		<if test="cdt != null and cdt.custom4 != null and cdt.custom4 != ''">and
			CUSTOM_4 like #{cdt.custom4,jdbcType=VARCHAR} 
		</if>
		<if test="cdt != null and cdt.custom4Equal != null and cdt.custom4Equal != ''">and
			CUSTOM_4 = #{cdt.custom4Equal,jdbcType=VARCHAR}
		</if>
		<if test="custom4s != null and custom4s != ''">and
			CUSTOM_4 in (${custom4s})
		</if>
		<if test="cdt != null and cdt.custom5 != null and cdt.custom5 != ''">and
			CUSTOM_5 like #{cdt.custom5,jdbcType=VARCHAR} 
		</if>
		<if test="cdt != null and cdt.custom5Equal != null and cdt.custom5Equal != ''">and
			CUSTOM_5 = #{cdt.custom5Equal,jdbcType=VARCHAR}
		</if>
		<if test="custom5s != null and custom5s != ''">and
			CUSTOM_5 in (${custom5s})
		</if>
		<if test="cdt != null and cdt.custom6 != null and cdt.custom6 != ''">and
			CUSTOM_6 like #{cdt.custom6,jdbcType=VARCHAR} 
		</if>
		<if test="cdt != null and cdt.custom6Equal != null and cdt.custom6Equal != ''">and
			CUSTOM_6 = #{cdt.custom6Equal,jdbcType=VARCHAR}
		</if>
		<if test="custom6s != null and custom6s != ''">and
			CUSTOM_6 in (${custom6s})
		</if>
		<if test="cdt != null and cdt.domainId != null">and
			DOMAIN_ID = #{cdt.domainId:BIGINT}
		</if>
		<if test="domainIds != null and domainIds != ''">and
			DOMAIN_ID in (${domainIds})
		</if>
		<if test="cdt != null and cdt.startDomainId != null">and
			 DOMAIN_ID &gt;= #{cdt.startDomainId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endDomainId != null">and
			 DOMAIN_ID &lt;= #{cdt.endDomainId:BIGINT} 
		</if>
		<if test="cdt != null and cdt.dataStatus != null">and
			DATA_STATUS = #{cdt.dataStatus:INTEGER}
		</if>
		<if test="dataStatuss != null and dataStatuss != ''">and
			DATA_STATUS in (${dataStatuss})
		</if>
		<if test="cdt != null and cdt.startDataStatus != null">and
			 DATA_STATUS &gt;= #{cdt.startDataStatus:INTEGER} 
		</if>
		<if test="cdt != null and cdt.endDataStatus != null">and
			 DATA_STATUS &lt;= #{cdt.endDataStatus:INTEGER} 
		</if>
		<if test="cdt != null and cdt.creator != null and cdt.creator != ''">and
			CREATOR like #{cdt.creator,jdbcType=VARCHAR} 
		</if>
		<if test="cdt != null and cdt.creatorEqual != null and cdt.creatorEqual != ''">and
			CREATOR = #{cdt.creatorEqual,jdbcType=VARCHAR}
		</if>
		<if test="creators != null and creators != ''">and
			CREATOR in (${creators})
		</if>
		<if test="cdt != null and cdt.modifier != null and cdt.modifier != ''">and
			MODIFIER like #{cdt.modifier,jdbcType=VARCHAR} 
		</if>
		<if test="cdt != null and cdt.modifierEqual != null and cdt.modifierEqual != ''">and
			MODIFIER = #{cdt.modifierEqual,jdbcType=VARCHAR}
		</if>
		<if test="modifiers != null and modifiers != ''">and
			MODIFIER in (${modifiers})
		</if>
		<if test="cdt != null and cdt.createTime != null">and
			CREATE_TIME = #{cdt.createTime:BIGINT}
		</if>
		<if test="createTimes != null and createTimes != ''">and
			CREATE_TIME in (${createTimes})
		</if>
		<if test="cdt != null and cdt.startCreateTime != null">and
			 CREATE_TIME &gt;= #{cdt.startCreateTime:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endCreateTime != null">and
			 CREATE_TIME &lt;= #{cdt.endCreateTime:BIGINT} 
		</if>
		<if test="cdt != null and cdt.modifyTime != null">and
			MODIFY_TIME = #{cdt.modifyTime:BIGINT}
		</if>
		<if test="modifyTimes != null and modifyTimes != ''">and
			MODIFY_TIME in (${modifyTimes})
		</if>
		<if test="cdt != null and cdt.startModifyTime != null">and
			 MODIFY_TIME &gt;= #{cdt.startModifyTime:BIGINT} 
		</if>
		<if test="cdt != null and cdt.endModifyTime != null">and
			 MODIFY_TIME &lt;= #{cdt.endModifyTime:BIGINT} 
		</if>
	</sql>
	

	<sql id="sql_update_columns">
		<if test="record != null and record.id != null"> 
			ID = #{record.id:BIGINT}
		,</if>
		<if test="record != null and record.linkedName != null"> 
			LINKED_NAME = #{record.linkedName,jdbcType=VARCHAR}
		,</if>
		<if test="record != null and record.sourceType != null"> 
			SOURCE_TYPE = #{record.sourceType:INTEGER}
		,</if>
		<if test="record != null and record.isDisplay != null"> 
			IS_DISPLAY = #{record.isDisplay:INTEGER}
		,</if>
		<if test="record != null and record.sourceId != null"> 
			SOURCE_ID = #{record.sourceId:BIGINT}
		,</if>
		<if test="record != null and record.ciCodes != null"> 
			CI_CODES = #{record.ciCodes,jdbcType=VARCHAR}
		,</if>
		<if test="record != null and record.linkedColor != null"> 
			LINKED_COLOR = #{record.linkedColor,jdbcType=VARCHAR}
		,</if>
		<if test="record != null and record.custom1 != null"> 
			CUSTOM_1 = #{record.custom1,jdbcType=VARCHAR}
		,</if>
		<if test="record != null and record.custom2 != null"> 
			CUSTOM_2 = #{record.custom2,jdbcType=VARCHAR}
		,</if>
		<if test="record != null and record.custom3 != null"> 
			CUSTOM_3 = #{record.custom3,jdbcType=VARCHAR}
		,</if>
		<if test="record != null and record.custom4 != null"> 
			CUSTOM_4 = #{record.custom4,jdbcType=VARCHAR}
		,</if>
		<if test="record != null and record.custom5 != null"> 
			CUSTOM_5 = #{record.custom5,jdbcType=VARCHAR}
		,</if>
		<if test="record != null and record.custom6 != null"> 
			CUSTOM_6 = #{record.custom6,jdbcType=VARCHAR}
		,</if>
		<if test="record != null and record.domainId != null"> 
			DOMAIN_ID = #{record.domainId:BIGINT}
		,</if>
		<if test="record != null and record.dataStatus != null"> 
			DATA_STATUS = #{record.dataStatus:INTEGER}
		,</if>
		<if test="record != null and record.creator != null"> 
			CREATOR = #{record.creator,jdbcType=VARCHAR}
		,</if>
		<if test="record != null and record.modifier != null"> 
			MODIFIER = #{record.modifier,jdbcType=VARCHAR}
		,</if>
		<if test="record != null and record.createTime != null"> 
			CREATE_TIME = #{record.createTime:BIGINT}
		,</if>
		<if test="record != null and record.modifyTime != null"> 
			MODIFY_TIME = #{record.modifyTime:BIGINT}
		,</if>
	</sql>
	

	<sql id="sql_query_columns">
		ID, LINKED_NAME, SOURCE_TYPE, IS_DISPLAY, SOURCE_ID, CI_CODES, 
		LINKED_COLOR, CUSTOM_1, CUSTOM_2, CUSTOM_3, CUSTOM_4, CUSTOM_5, 
		CUSTOM_6, DOMAIN_ID, DATA_STATUS, CREATOR, MODIFIER, CREATE_TIME, 
		MODIFY_TIME
	</sql>
	

	

	<select id="selectList" parameterType="java.util.Map" resultMap="queryResult">
		select 
			<include refid="VC_CI_LINKED.sql_query_columns"/>
		from VC_CI_LINKED 
			<where>
				<include refid="VC_CI_LINKED.sql_query_where"/>
			</where>
		order by 
			<if test="orders != null and orders != ''">
				${orders}
			</if>
			<if test="orders == null or orders == ''">
				ID
			</if>
	</select>
	<select id="selectCount" parameterType="java.util.Map" resultType="java.lang.Long">
		select count(1) from VC_CI_LINKED 
			<where>
				<include refid="VC_CI_LINKED.sql_query_where"/>
			</where>
	</select>
	<select id="selectById" parameterType="java.util.Map" resultMap="queryResult">
		select 
			<include refid="VC_CI_LINKED.sql_query_columns"/>
		from VC_CI_LINKED where ID=#{id:BIGINT} and DATA_STATUS=1  
	</select>
	

	

	<insert id="insert" parameterType="java.util.Map">
		insert into VC_CI_LINKED(
			ID, LINKED_NAME, SOURCE_TYPE, IS_DISPLAY, SOURCE_ID, 
			CI_CODES, LINKED_COLOR, CUSTOM_1, CUSTOM_2, CUSTOM_3, 
			CUSTOM_4, CUSTOM_5, CUSTOM_6, DOMAIN_ID, DATA_STATUS, 
			CREATOR, MODIFIER, CREATE_TIME, MODIFY_TIME)
		values (
			#{record.id:BIGINT}, #{record.linkedName,jdbcType=VARCHAR}, #{record.sourceType:INTEGER}, #{record.isDisplay:INTEGER}, #{record.sourceId:BIGINT}, 
			#{record.ciCodes,jdbcType=VARCHAR}, #{record.linkedColor,jdbcType=VARCHAR}, #{record.custom1,jdbcType=VARCHAR}, #{record.custom2,jdbcType=VARCHAR}, #{record.custom3,jdbcType=VARCHAR}, 
			#{record.custom4,jdbcType=VARCHAR}, #{record.custom5,jdbcType=VARCHAR}, #{record.custom6,jdbcType=VARCHAR}, #{record.domainId:BIGINT}, #{record.dataStatus:INTEGER}, 
			#{record.creator,jdbcType=VARCHAR}, #{record.modifier,jdbcType=VARCHAR}, #{record.createTime:BIGINT}, #{record.modifyTime:BIGINT})
	</insert>
	

	

	<update id="updateById" parameterType="java.util.Map">
		update VC_CI_LINKED
			<set> 
				<include refid="VC_CI_LINKED.sql_update_columns"/> 
			</set>
		where ID = #{id:BIGINT}
	</update>
	<update id="updateByCdt" parameterType="java.util.Map">
		update VC_CI_LINKED
			<set> 
				<include refid="VC_CI_LINKED.sql_update_columns"/> 
			</set>
			<where> 
				<include refid="VC_CI_LINKED.sql_query_where"/> 
			</where>
	</update>
	
	

	

	<delete id="deleteById" parameterType="java.util.Map">
		delete from VC_CI_LINKED where ID = #{id:BIGINT}
	</delete>
	<delete id="deleteByCdt" parameterType="java.util.Map">
		delete from VC_CI_LINKED
			<where> 
				<include refid="VC_CI_LINKED.sql_query_where"/> 
			</where>
	</delete>
	



</mapper>