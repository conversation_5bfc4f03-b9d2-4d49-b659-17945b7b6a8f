package com.uino.role.svc;

import java.util.Collections;

import org.junit.Before;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import com.uino.dao.permission.ESDataModuleSvc;
import com.uino.service.permission.microservice.impl.RoleSvc;
import com.uino.bean.permission.base.SysDataModule;

@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("provider-local")
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class AddDataModuleMenuTest {
	@Autowired
	private RoleSvc testSvc;
	@MockBean
	private ESDataModuleSvc dataModuleSvc;

	@Before
	public void before() {
		Mockito.when(dataModuleSvc.saveOrUpdate(Mockito.any())).thenReturn(1L);
		SysDataModule repDataModule = SysDataModule.builder().id(2L).build();
		Mockito.when(dataModuleSvc.getListByCdt(Mockito.any())).thenReturn(Collections.singletonList(repDataModule));
	}

	@Test
	public void test01() {
		SysDataModule req = SysDataModule.builder().dataModuleCode("code01").dataModuleName("dmName")
				.dataSourceUrl("/source/url").issee(1).iscreate(1).isupdate(1).isdelete(1).isenable(1L).displayType(1L)
				.build();
		testSvc.addDataModuleMenu(req);
	}

	@Test
	public void test02() {
		SysDataModule req = SysDataModule.builder().dataModuleCode("code01").dataModuleName("dmName")
				.dataSourceUrl("/source/url").build();
		testSvc.addDataModuleMenu(req);
	}

	@Test
	public void test03() {
		Mockito.when(dataModuleSvc.getListByCdt(Mockito.any())).thenReturn(null);
		SysDataModule req = SysDataModule.builder().dataModuleCode("code02").dataModuleName("dmName")
				.dataSourceUrl("/source/url").build();
		testSvc.addDataModuleMenu(req);
	}

	@Test(expected = IllegalArgumentException.class)
	public void test04() {
		SysDataModule req = SysDataModule.builder().dataModuleCode("code02").dataSourceUrl("/source/url").build();
		testSvc.addDataModuleMenu(req);
	}

	@Test(expected = IllegalArgumentException.class)
	public void test05() {
		SysDataModule req = SysDataModule.builder().dataModuleCode("code02").dataModuleName("dmName").build();
		testSvc.addDataModuleMenu(req);
	}
}
