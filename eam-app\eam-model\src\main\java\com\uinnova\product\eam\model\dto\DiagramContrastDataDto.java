package com.uinnova.product.eam.model.dto;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.base.diagram.model.ESDiagramDTO;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.base.ESCIRltInfo;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 *  视图双态对比 参数
 */
@Data
public class DiagramContrastDataDto {

    @Comment("对比数据-视图数据")
    private ESDiagramDTO esDiagramDTO;

    @Comment("对比数据-CI数据")
    private List<ESCIInfo> ciList;
    @Comment("对比数据-RLT数据")
    private List<ESCIRltInfo> rltList;


    @Comment("对比数据-CI数据 key - 主键 value - 节点key")
    private Map<String, String> ciAndLocMap;
    @Comment("对比数据-RLT数据 key - 主键 value - 节点key")
    private Map<String, String> rltAndLocMap;

    @Comment("对比数据-CI数据 key - 主键 value - 数据")
    private Map<String, ESCIInfo> ciMap;
    @Comment("对比数据-RLT数据")
    private List<ESCIRltInfo> rltMap;

}

