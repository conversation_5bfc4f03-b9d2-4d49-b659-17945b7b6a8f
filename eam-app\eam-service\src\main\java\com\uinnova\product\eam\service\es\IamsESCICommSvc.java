package com.uinnova.product.eam.service.es;

import com.binary.core.exception.BinaryException;
import com.binary.core.util.BinaryUtils;
import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uino.bean.cmdb.base.ESCIAttrTransConfig;
import com.uino.bean.cmdb.base.ESCIHistoryInfo;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.base.LibType;
import com.uino.bean.cmdb.enums.AttrNameKeyEnum;
import com.uino.bean.cmdb.query.ESAttrAggBean;
import com.uino.dao.cmdb.ESCIHistorySvc;
import com.uino.dao.cmdb.ESCISvc;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * ES-CI分类服务
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class IamsESCICommSvc {
    @Autowired
    private ESCISvc esCiSvc;

    @Autowired
    private IamsESCIPrivateSvc esCiPrivateSvcIams;

    @Autowired
    private IamsESCIDesignSvc esCiDesignSvcIams;

    @Autowired
    private ESCIHistorySvc ciHistorySvc;

    @Autowired
    private IamsESCIHistoryDesignSvc ciHistoryDesignSvc;

    public List<LibType> getLibTypes() {
        return Arrays.asList(LibType.BASELINE, LibType.PRIVATE, LibType.DESIGN);
    }

    public Map<Object, Long> countCIByQuery(QueryBuilder query, LibType libType) {
        if (LibType.PRIVATE.equals(libType)) {
            return esCiPrivateSvcIams.countCIByQuery(query);
        } else if (LibType.DESIGN.equals(libType)) {
            return esCiDesignSvcIams.countCIByQuery(query);
        } else {
            return esCiSvc.countCIByQuery(query);
        }
    }

    public Map<String, Object> getCIMapping() {
        return esCiSvc.getCIMapping();
    }

    public void transCIAttrs(List<ESCIInfo> esciInfos, boolean changeToShowName) {
        esCiSvc.transCIAttrs(esciInfos, changeToShowName);
    }

    public void updateByQuery(QueryBuilder query, String scriptStr, boolean isRefresh, LibType libType) {
        if (LibType.PRIVATE.equals(libType)) {
            esCiPrivateSvcIams.updateByQuery(query, scriptStr, isRefresh);
        } else if (LibType.DESIGN.equals(libType)) {
            esCiDesignSvcIams.updateByQuery(query, scriptStr, isRefresh);
        } else {
            esCiSvc.updateByQuery(query, scriptStr, isRefresh);
        }
    }

    public long countByCondition(QueryBuilder query, LibType libType) {
        if (LibType.PRIVATE.equals(libType)) {
            return esCiPrivateSvcIams.countByCondition(query);
        } else if (LibType.DESIGN.equals(libType)) {
            return esCiDesignSvcIams.countByCondition(query);
        } else if (libType == null) {
            long privateCount = esCiPrivateSvcIams.countByCondition(query);
            long designCount = esCiDesignSvcIams.countByCondition(query);
            long count = esCiSvc.countByCondition(query);
            return privateCount + designCount + count;
        } else {
            return esCiSvc.countByCondition(query);
        }
    }

    public void clearCIAttrByProName(Long classId, String proName, LibType libType) {
        if (LibType.PRIVATE.equals(libType)) {
            esCiPrivateSvcIams.clearCIAttrByProName(classId, proName);
        } else if (LibType.DESIGN.equals(libType)) {
            esCiDesignSvcIams.clearCIAttrByProName(classId, proName);
        } else {
            esCiSvc.clearCIAttrByProName(classId, proName);
        }
    }

    public void deleteHistoryByQuery(QueryBuilder query, boolean isRefresh, LibType libType) {
        if (LibType.PRIVATE.equals(libType)) {
            //
        } else if (LibType.DESIGN.equals(libType)) {
            ciHistoryDesignSvc.deleteByQuery(query, isRefresh);
        } else {
            ciHistorySvc.deleteByQuery(query, isRefresh);
        }
    }

    public void saveOrUpdateHistoryInfosBatch(List<ESCIInfo> historys, ESCIHistoryInfo.ActionType action, LibType libType) {
        if (LibType.PRIVATE.equals(libType)) {
            log.info("私有库暂不支持");
        } else if (LibType.DESIGN.equals(libType)) {
            ciHistoryDesignSvc.saveOrUpdateHistoryInfosBatch(historys, action);
        } else {
            ciHistorySvc.saveOrUpdateHistoryInfosBatch(historys, action);
        }
    }

    public void updateHistoryByQuery(QueryBuilder query, String script, boolean isRefresh, LibType libType) {
        if (LibType.PRIVATE.equals(libType)) {

        } else if (LibType.DESIGN.equals(libType)) {
            ciHistoryDesignSvc.updateByQuery(query, script, isRefresh);
        } else {
            ciHistorySvc.updateByQuery(query, script, isRefresh);
        }
    }

    public List<CcCiInfo> getCIInfosByQuery(QueryBuilder query, boolean isRefresh, LibType libType) {
        long count = this.countByCondition(query, libType);
        if (LibType.PRIVATE.equals(libType)) {
            return esCiPrivateSvcIams.getCIInfoPageByQuery(1, new BigDecimal(count).intValue(), query, false).getData();
        } else if (LibType.DESIGN.equals(libType)) {
            return esCiDesignSvcIams.getCIInfoPageByQuery(1, new BigDecimal(count).intValue(), query, false).getData();
        } else {
            return esCiSvc.getCIInfoPageByQuery(1, new BigDecimal(count).intValue(), query, false).getData();
        }
    }

    public List<ESCIInfo> getESInfosByQuery(QueryBuilder query, LibType libType) {
        long count = this.countByCondition(query, libType);
        if (LibType.PRIVATE.equals(libType)) {
            return esCiPrivateSvcIams.getListByQuery(1, new BigDecimal(count).intValue(), query).getData();
        } else if (LibType.DESIGN.equals(libType)) {
            return esCiDesignSvcIams.getListByQuery(1, new BigDecimal(count).intValue(), query).getData();
        } else {
            return esCiSvc.getListByQuery(1, new BigDecimal(count).intValue(), query).getData();
        }
    }

    public Page<String> queryAttrVal(Long domainId, ESAttrAggBean bean) {
        return esCiSvc.queryAttrVal(domainId,bean);
    }

    public Page<String> queryAttrVal(Long domainId, Long classId, List<String> attrNames, boolean needPage, ESAttrAggBean searchBean) {
        return esCiSvc.queryAttrVal(domainId, classId, attrNames, needPage, searchBean);
    }
}
