package com.uinnova.product.vmdb.comm.expression;

import java.io.Serializable;

/**
 * 
 * <AUTHOR>
 *
 */
public interface Field<E> extends Serializable {

    /**
     * 获取模型字段名称
     * 
     * @return
     */
    String getName();

    /**
     * 获取映射JAVA字段名称
     * 
     * @return
     */
    String getMappingName();

    /**
     * 等于
     */
    Expression<E> EQ(String value);

    /**
     * 不等于
     */
    Expression<E> NEQ(String value);

    /**
     * 小于
     */
    Expression<E> LT(String value);

    /**
     * 小于或等于
     */
    Expression<E> LTEQ(String value);

    /**
     * 大于
     */
    Expression<E> GT(String value);

    /**
     * 大于或等于
     */
    Expression<E> GTEQ(String value);

    /**
     * 模糊匹配...
     */
    Expression<E> LIKE(String value);

    /**
     * 字符包含
     * 
     * @param value
     * @return
     */
    Expression<E> INSTR(String value);

    /**
     * 包含...
     */
    Expression<E> IN(String[] values);

    /**
     * 不包含...
     */
    Expression<E> NIN(String[] values);

}
