package com.uino.service.sys.microservice;

import java.util.Collection;
import java.util.List;

import org.springframework.web.multipart.MultipartFile;

import com.binary.core.io.Resource;
import com.binary.jdbc.Page;
import com.uino.bean.cmdb.business.ImportResultMessage;
import com.uino.bean.cmdb.business.ImportSheetMessage;
import com.uino.bean.sys.base.ESDictionaryClassInfo;
import com.uino.bean.sys.base.ESDictionaryItemInfo;
import com.uino.bean.sys.business.DictionaryInfoDto;
import com.uino.bean.sys.query.ESDictionaryItemSearchBean;
import com.uino.bean.sys.query.ExportDictionaryDto;

public interface IDictionarySvc {

    /**
     * 保存字典定义
     * 
     * @param dictClassInfo
     * @return
     */
    Long saveDictionaryClassInfo(ESDictionaryClassInfo dictClassInfo);

    /**
     * 根据id获取字典定义
     * 
     * @param id
     * @return
     */
    ESDictionaryClassInfo getDictClassInfoById(Long dictClassId);

    /**
     * 获取字典定义列表
     * 
     * @return
     */
    List<DictionaryInfoDto> getDictionaryClassList(Long domainId);

    /**
     * 条件查询字典定义
     * 
     * @return
     */
    List<ESDictionaryClassInfo> queryDcitClassInfosByBean(ESDictionaryItemSearchBean bean);

    /**
     * 根据id删除字典定义
     * 
     * @param dictClassId
     * @return
     */
    Integer deleteDictClassInfoById(Long dictClassId);

    /**
     * 根据id获取字典项
     * 
     * @param id
     * @return
     */
    ESDictionaryItemInfo getDictItemInfoById(Long id);
    
    /**
     * 根据id获取字典项
     * 
     * @param ids
     *            null或空数组返回空数组
     * @return 
     */
    List<ESDictionaryItemInfo> getDictItemInfoByIds(Collection<Long> ids);
    
    
    /**
     * 条件查询字典项-分页
     * 
     * @param bean
     * @return
     */
    Page<ESDictionaryItemInfo> searchDictItemPageByBean(ESDictionaryItemSearchBean bean);

    /**
     * 查询字典项-分页-[ids]
     *
     * @param bean
     * @return
     */
    Page<ESDictionaryItemInfo> searchDictItemPageByIds(ESDictionaryItemSearchBean bean);

    /**
     * 条件查询字典项
     * 
     * @param bean
     * @return
     */
    List<ESDictionaryItemInfo> searchDictItemListByBean(ESDictionaryItemSearchBean bean);

    /**
     * 保存字典项
     * 
     * @param item
     * @return
     */
    Long saveOrUpdateDictionaryItem(ESDictionaryItemInfo item);

    /**
     * 批量保存字典项
     * 
     * @param dictClassId
     * @param items
     * @return
     */
    ImportSheetMessage saveDictionaryItemsBatch(Long dictClassId, List<ESDictionaryItemInfo> items);

    /**
     * 根据id删除字典项
     * 
     * @param ids
     * @return
     */
    Integer deleteItemByIds(Collection<Long> ids);

    /**
     * 导出字典数据
     * 
     * @param dto
     * @return
     */
    Resource exportDictionaryItems(ExportDictionaryDto dto);

    /**
     * 导入字典数据
     * 
     * @param dictClassId
     * @param file
     * @return
     */
    ImportResultMessage importDictionaryItems(Long dictClassId, MultipartFile file);

    /**
     * 查询引用字典值
     * 
     * @param bean
     * @return
     */
    List<String> getExteralDictValues(ESDictionaryItemSearchBean bean);

    List<String> getExteralDictValues(Long domainId, Long dictClassId, Long[] dictDefIds);

    String checkAttrUsedMethod(Long id);
}
