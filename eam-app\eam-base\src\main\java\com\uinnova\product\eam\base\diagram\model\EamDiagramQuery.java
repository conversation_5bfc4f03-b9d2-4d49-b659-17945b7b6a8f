package com.uinnova.product.eam.base.diagram.model;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

/**
 * @Classname
 * @Description TODO
 * <AUTHOR>
 * @Date 2021-06-02-16:31
 */
@Data
@Comment("视图ES查询类")
public class EamDiagramQuery {

    @Comment("ID[ID] operate-Equal[=]")
    private Long id;

    @Comment("ID[ID] operate-In[in]")
    private Long[] ids;

    @Comment("视图名称[NAME]")
    private String name;

    @Comment("所属用户[USER_ID]")
    private Long userId;

    @Comment("所属用户[USER_ID] operate-In[in]")
    private Long[] userIds;

    @Comment("所属目录[DIR_ID]")
    private Long dirId;

    @Comment("所属目录[DIR_ID]")
    private Long[] dirIds;

    @Comment("历史版本标识，0--主版本，1--历史版本")
    private Integer historyVersionFlag;

    @Comment("目录类型[DIR_TYPE]   ")
    private Integer dirType;

    @Comment("视图类型[DIAGRAM_TYPE]    视图类型:1=视图 3=公共模版 4=个人模板 5=wiki新建视图")
    private Integer diagramType;

    @Comment("视图类型[DIAGRAM_TYPE] operate-In[in]    视图类型:1=视图 3=公共模版 4=个人模板 5=wiki新建视图")
    private Integer[] diagramTypes;

    @Comment("视图描述[DIAGRAM_DESC]")
    private String diagramDesc;

    @Comment("视图SVG[DIAGRAM_SVG]")
    private String diagramSvg;

    @Comment("视图XML[DIAGRAM_XML]")
    private String diagramXml;

    @Comment("是否公开[IS_OPEN]    是否公开:1=开放 0=私有")
    private Integer isOpen;

    @Comment("公开时间[OPEN_TIME]")
    private Long openTime;

    @Comment("数据驱动类型[DATA_UP_TYPE]    数据驱动类型:1=不更新 2=标记提示 3=自动更新")
    private Integer dataUpType;

    @Comment("视图状态[STATUS]    视图状态:1=正常 0=回收站")
    private Integer status;

    @Comment("搜索字段[SEARCH_FIELD]    搜索字段:视图名称")
    private String searchField;

    @Comment("查看次数[READ_COUNT]")
    private Long readCount;

    @Comment("参照版本id[REFER_VERSION_ID]")
    private Long referVersionId;

    @Comment("所属域[DOMAIN_ID]")
    private Long domainId;

    @Comment("数据状态[DATA_STATUS]    数据状态:0=删除，1=正常")
    private Integer dataStatus;

    @Comment("创建人[CREATOR]")
    private String creator;

    @Comment("创建人[CREATOR]")
    private String creatorEqual;

    @Comment("创建人[CREATOR]")
    private String ownerCode;

    @Comment("创建人[CREATOR]")
    private String ownerCodeEqual;

    @Comment("创建人[CREATORS]")
    private String creators;

    @Comment("修改人[MODIFIER]")
    private String modifier;

    @Comment("创建时间[CREATE_TIME]    创建时间:yyyyMMddHHmmss")
    private Long createTime;

    @Comment("更新时间[MODIFY_TIME]    更新时间:yyyyMMddHHmmss")
    private Long modifyTime;

    @Comment("缩略图保存时间[THUMBNAIL_SAVE_ TIME]    yyyyMMddHHmmss")
    private Long thumbnailSaveTime;

    @Comment("主题文件夹[SUBJECT_ID]")
    private Long subjectId;

    private Long oldDirId;

    @Comment("转换为模板的时间[TEM_CONVERT_TIME]")
    private Long temConvertTime;

    @Comment("视图版本,用于兼容")
    private String version;

    @Comment("转换老视图标识，0-老视图")
    private Integer transFlag;

    @Comment("视图ID加密字段")
//    @JsonIgnore
    private String dEnergy;

    @Comment("视图ID加密字段")
//    @JsonIgnore
    private String dEnergyEqual;

    @Comment("视图ID加密字段数组")
    private String[] dEnergys;

    private String reserved1;
    private String reserved2;
    private String reserved3;

    @Comment("视图类型：1视图，2矩阵，3清单")
    private Integer queryType;

    @Comment("视图类型")
    private String viewType;

    @Comment("源发布视图id")
    private String releaseDiagramId;

    @Comment("预制视图发布ID")
    private String prepareDiagramId;

    @Comment("预制视图发布IDS")
    private String[] prepareDiagramIds;

    @Comment("视图是否处于流程中 1(可编辑) / 2(不可编辑) = 流程中 / 0 = 未在流程中")
    private Integer flowStatus;

    @Comment("视图转换成模板说明信息")
    private String describeInfo;

    @Comment("第一次视图转换成模板需要回显到主视图这 留着视图发布多次只更新关系库使用")
    private Long firstCopyDiagramId;

}
