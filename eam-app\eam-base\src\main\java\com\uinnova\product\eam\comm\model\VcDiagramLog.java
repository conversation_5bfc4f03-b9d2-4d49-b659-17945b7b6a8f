package com.uinnova.product.eam.comm.model;




import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;


@Comment("视图操作日志表[VC_DIAGRAM_LOG]")
public class VcDiagramLog implements EntityBean {
	private static final long serialVersionUID = 1L;


	@Comment("ID[ID]")
	private Long id;


	@Comment("视图ID[DIAGRAM_ID]")
	private Long diagramId;


	@Comment("日志时间[LOG_TIME]    日志时间:yyyyMMddHHmmss")
	private Long logTime;


	@Comment("操作人ID[OP_ID]")
	private Long opId;


	@Comment("操作人名称[OP_NAME]")
	private String opName;


	@Comment("来源类别[SOURCE_TYPE]    来源类别:1=本人,2=小组")
	private Integer sourceType;


	@Comment("操作类型[OP_TYPE]    1=创建,2=更新,3=删除)")
	private Integer opType;


	@Comment("日志描述[LOG_DESC]")
	private String logDesc;


	@Comment("所属域[DOMAIN_ID]")
	private Long domainId;


	@Comment("创建时间[CREATE_TIME]    创建时间:yyyyMMddHHmmss")
	private Long createTime;


	@Comment("修改时间[MODIFY_TIME]    修改时间:yyyyMMddHHmmss")
	private Long modifyTime;




	public Long getId() {
		return this.id;
	}
	public void setId(Long id) {
		this.id = id;
	}


	public Long getDiagramId() {
		return this.diagramId;
	}
	public void setDiagramId(Long diagramId) {
		this.diagramId = diagramId;
	}


	public Long getLogTime() {
		return this.logTime;
	}
	public void setLogTime(Long logTime) {
		this.logTime = logTime;
	}


	public Long getOpId() {
		return this.opId;
	}
	public void setOpId(Long opId) {
		this.opId = opId;
	}


	public String getOpName() {
		return this.opName;
	}
	public void setOpName(String opName) {
		this.opName = opName;
	}


	public Integer getSourceType() {
		return this.sourceType;
	}
	public void setSourceType(Integer sourceType) {
		this.sourceType = sourceType;
	}


	public Integer getOpType() {
		return this.opType;
	}
	public void setOpType(Integer opType) {
		this.opType = opType;
	}


	public String getLogDesc() {
		return this.logDesc;
	}
	public void setLogDesc(String logDesc) {
		this.logDesc = logDesc;
	}


	public Long getDomainId() {
		return this.domainId;
	}
	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}


	public Long getCreateTime() {
		return this.createTime;
	}
	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}


	public Long getModifyTime() {
		return this.modifyTime;
	}
	public void setModifyTime(Long modifyTime) {
		this.modifyTime = modifyTime;
	}


}


