package com.uino.org.svc;

import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

import com.uino.dao.permission.ESOrgSvc;
import com.uino.service.permission.microservice.impl.OrgSvc;
import com.uino.bean.permission.base.SysOrg;
import com.uino.bean.permission.business.request.InterchangeOrgNoRequestDto;

public class InterchangeOrgNoTest {
	@InjectMocks
	private OrgSvc orgSvc;
	private ESOrgSvc esOrgSvc;

	@Before
	public void before() {
		MockitoAnnotations.initMocks(this);
		esOrgSvc = Mockito.mock(ESOrgSvc.class);
		ReflectionTestUtils.setField(orgSvc, "esOrgSvc", esOrgSvc);
		Mockito.when(esOrgSvc.getById(Mockito.eq(1L))).thenReturn(SysOrg.builder().id(1L).orderNo(1).build());
		Mockito.when(esOrgSvc.getById(Mockito.eq(2L))).thenReturn(SysOrg.builder().id(2L).orderNo(2).build());
		Mockito.when(esOrgSvc.saveOrUpdateBatch(Mockito.anyList())).thenReturn(2);
	}

	@Test
	public void test01() {
		orgSvc.interchangeOrgNo(InterchangeOrgNoRequestDto.builder().sourceOrgId(1L).targetOrgId(2L).build());
	}

	@Test(expected = IllegalArgumentException.class)
	public void test02() {
		orgSvc.interchangeOrgNo(InterchangeOrgNoRequestDto.builder().sourceOrgId(3L).targetOrgId(2L).build());
	}

	@Test(expected = IllegalArgumentException.class)
	public void test03() {
		orgSvc.interchangeOrgNo(InterchangeOrgNoRequestDto.builder().sourceOrgId(1L).targetOrgId(3L).build());
	}

	@Test(expected = IllegalArgumentException.class)
	public void test04() {
		orgSvc.interchangeOrgNo(InterchangeOrgNoRequestDto.builder().targetOrgId(3L).build());
	}

	@Test(expected = IllegalArgumentException.class)
	public void test05() {
		orgSvc.interchangeOrgNo(InterchangeOrgNoRequestDto.builder().sourceOrgId(1L).build());
	}
}
