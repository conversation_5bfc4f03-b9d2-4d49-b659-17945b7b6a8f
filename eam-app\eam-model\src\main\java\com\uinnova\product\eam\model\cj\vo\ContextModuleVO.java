package com.uinnova.product.eam.model.cj.vo;

import com.uinnova.product.eam.model.cj.domain.PlanTemplateChapterData;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * 方案章节实上下文实际数据
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ContextModuleVO {

    /**
     * 模板块的id与模板相同，自定义的后端动态生成
     */
    private Long id;

    /**
     * 模块是否来自模板
     */
    private Boolean isFromTemplate;

    private Boolean isIntroduceContext;

    /**
     * 模板内容
     */
    private PlanTemplateChapterData moduleDefinition;

    /**
     * 批注个数
     */
    private Integer annotationCount;

    /**
     * 数据
     */
    private Map<String, Object> data;

    /**
     * 对应的模板是否已被删除 1: 已删除
     */
    private Integer templateDelete;
}

