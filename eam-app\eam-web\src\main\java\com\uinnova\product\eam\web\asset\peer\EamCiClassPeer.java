package com.uinnova.product.eam.web.asset.peer;

import com.uinnova.product.vmdb.comm.model.ci.CCcCiClass;
import com.uinnova.product.vmdb.comm.model.ci.CcCiClass;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uino.api.client.cmdb.ICIClassApiSvc;
import com.uino.bean.cmdb.base.ESCIClassInfo;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class EamCiClassPeer {

    @Resource
    private ICIClassApiSvc ciClassApiSvc;

    public CcCiClassInfo getCiClassInfoIdById(Long id) {
        CCcCiClass cCcCiClass = new CCcCiClass();
        cCcCiClass.setId(id);
        List<CcCiClassInfo> ccCiClassInfos = ciClassApiSvc.queryClassByCdt(cCcCiClass);
        return ccCiClassInfos.get(0);
    }

    public Long getCiClassIdByName(String ciClassName) {
        CCcCiClass cCcCiClass = new CCcCiClass();
        cCcCiClass.setClassNameEqual(ciClassName);
        List<CcCiClassInfo> ccCiClassInfos = ciClassApiSvc.queryClassByCdt(cCcCiClass);
        return ccCiClassInfos.get(0).getCiClass().getId();
    }

    public Map<String, Long> getIdMapByNames(List<String> ciClassNames) {
        CCcCiClass cCcCiClass = new CCcCiClass();
        cCcCiClass.setClassNames(ciClassNames.toArray(new String[ciClassNames.size()]));
        List<CcCiClassInfo> ccCiClassInfos = ciClassApiSvc.queryClassByCdt(cCcCiClass);
        return ccCiClassInfos.stream().map(CcCiClassInfo::getCiClass)
                .collect(Collectors.toMap(CcCiClass::getClassName,CcCiClass::getId, (key1, key2) -> key1));
    }

}
