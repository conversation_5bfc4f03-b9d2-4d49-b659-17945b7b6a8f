package com.uinnova.product.vmdb.comm.bean;

import com.binary.framework.bean.annotation.Comment;

import java.io.Serializable;
import java.util.List;

/**
 * 
 * <AUTHOR>
 *
 */
public class CcDynamicClassNode implements Serializable {
    private static final long serialVersionUID = 1L;

    @Comment("节点ID")
    private String id;

    @Comment("节点名称")
    private String text;

    @Comment("节点图标")
    private String icon;

    @Comment("节点类型 : 1=目录    2=标签	3=CI")
    private Integer type;

    @Comment("上级节点ID, 1级为0")
    private String parentId;

    @Comment("直属子节点列表")
    private List<CcDynamicClassNode> children;

    @Comment("绑定的数据ID, type=2为tagId, type=3为ciid")
    private Long bindId;

    @Comment("绑定数据")
    private Object data;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public List<CcDynamicClassNode> getChildren() {
        return children;
    }

    public void setChildren(List<CcDynamicClassNode> children) {
        this.children = children;
    }

    public Long getBindId() {
        return bindId;
    }

    public void setBindId(Long bindId) {
        this.bindId = bindId;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }

}
