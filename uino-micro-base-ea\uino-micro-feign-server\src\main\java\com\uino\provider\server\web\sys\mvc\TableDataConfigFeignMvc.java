package com.uino.provider.server.web.sys.mvc;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.uino.service.sys.microservice.ITableDataConfigSvc;
import com.uino.bean.cmdb.base.ESTableDataConfigInfo;
import com.uino.provider.feign.sys.TableDataConfigFeign;

/**
 * 
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("feign/sys/table")
public class TableDataConfigFeignMvc implements TableDataConfigFeign {

    @Autowired
    private ITableDataConfigSvc configSvc;

    @Override
    public ESTableDataConfigInfo getCIDataConfigInfo(Long domainId,String uid) {
        return configSvc.getCIDataConfigInfo(domainId,uid);
    }

    @Override
    public Long saveCIDataConfigInfo(ESTableDataConfigInfo configInfo) {
        return configSvc.saveCIDataConfigInfo(configInfo);
    }

}
