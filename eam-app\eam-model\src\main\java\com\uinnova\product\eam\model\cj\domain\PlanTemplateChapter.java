package com.uinnova.product.eam.model.cj.domain;

import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.base.cj.BaseEntity;
import com.uinnova.product.eam.model.cj.vo.PlanTemplateIntroduceChapterVo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 方案模板章节
 * @author: Lc
 * @create: 2022-01-05 16:42
 */
@Data
@Comment("方案模板章节")
public class PlanTemplateChapter extends BaseEntity implements Condition, Serializable {
    private static final long serialVersionUID = 1905122041950251207L;
    @Comment("业务id")
    private Long businessId;
    @Comment("章节名称")
    private String chapterName;
    @Comment("章节说明")
    private String chapterDesc;
    @Comment("是否必填  0:否 1:是")
    private String required;
    @Comment("用户自行添加内容快 0：否 1：是")
    private String userAddContent;
    @Comment("层级")
    private Integer level;
    @Comment("备注信息")
    private String remarkInfo;
    @Comment("所属父章节")
    private Long parentId;
    @Comment("排序号")
    private Double sortNum;
    @Comment("版本")
    private Integer version;
    @Comment("所属方案模板id")
    private Long planTemplateId;
    @Comment("状态 0:删除 1:在用")
    private Integer status;
    @Comment("引入章节信息")
    private List<PlanTemplateIntroduceChapterVo> planTemplateIntroduceChapterVoList;
    @Comment("引用按钮是否可操作， 0：不能操作， 1：可操作")
    private Integer introduceButton;
    @Comment("参考规范信息")
    private ReferencesInfo referencesInfo;

}
