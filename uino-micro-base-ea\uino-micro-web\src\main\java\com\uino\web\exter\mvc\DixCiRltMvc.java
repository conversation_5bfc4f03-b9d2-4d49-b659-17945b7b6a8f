package com.uino.web.exter.mvc;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.binary.framework.util.ControllerUtils;
import com.binary.jdbc.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClass;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uinnova.product.vmdb.comm.util.CommUtil;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uinnova.product.vmdb.provider.rlt.bean.CcCiRltInfo;
import com.uino.util.sys.BeanUtil;
import com.uino.util.sys.ValidDtoUtil;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.base.ESCIRltInfo;
import com.uino.bean.cmdb.business.BindCiRltRequestDto;
import com.uino.bean.cmdb.business.DelRltReqDto;
import com.uino.bean.cmdb.business.ImportResultMessage;
import com.uino.bean.cmdb.query.ESAttrBean;
import com.uino.bean.cmdb.query.ESRltSearchBean;
import com.uino.api.client.cmdb.ICIApiSvc;
import com.uino.api.client.cmdb.ICIRltApiSvc;
import com.uino.api.client.cmdb.IRltClassApiSvc;
import com.uino.web.BaseMvc;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * @Classname DixCiRltMvc
 * @Description ci关系接口
 * @Date 2020/7/23 14:26
 * <AUTHOR> sh
 */
@Slf4j
@RestController
@RequestMapping("/dix/ciRlt")
public class DixCiRltMvc extends BaseMvc {

    @Autowired
    private ICIRltApiSvc ciRltApiSvc;

    @Autowired
    private IRltClassApiSvc rltClassApi;

    @Autowired
    private ICIApiSvc ciApi;

    /**
     * 批量保存关系
     * <p>
     * 1: 获取入参钟需要使用的sourcecis/targetcis/rltClss，有不存在数据直接断言 <br/>
     * 2: 获取到这些数据的id，进行实体转换，直接调用api的批量绑定接口
     * </p>
     *
     * @param reqBeans
     */
    @PostMapping("bindCiRlts")
    @ModDesc(desc = "批量保存关系数据", pDesc = "关系数据传输对象", pType = Set.class, rDesc = "操作结果", rType = ImportResultMessage.class)
    public void bindCiRlts(@RequestBody List<DixBindCiRltRequestDto> reqBeans) {
        ControllerUtils.returnJson(request, response, this.bindCiRltsCore(getCIsByBindRltDto(reqBeans), reqBeans));
    }

    @PostMapping("saveOrUpdateRlts")
    @ModDesc(desc = "批量保存关系数据", pDesc = "关系数据传输对象", pType = Set.class, rDesc = "操作结果", rType = ImportResultMessage.class)
    public void saveOrUpdateRlts(@RequestBody String body) throws Exception {
        JSONObject json = JSONObject.parseObject(body);
        String relationName = "";
        if (json.containsKey("relationName") && json.getString("relationName")!=null && !"".equals(json.getString("relationName").trim())) {
        	relationName = json.getString("relationName").trim();
        } else {
        	throw new Exception("lack of relationName");
        }
        CCcCiClass cdt = new CCcCiClass();
        cdt.setClassNameEqual(relationName);
		List<CcCiClassInfo> rltClassInfos = rltClassApi.getRltClassByCdt(cdt);
		List<String> attrNames = new ArrayList<String>();
		List<String> subCodes = new ArrayList<String>();
		if (rltClassInfos!=null && rltClassInfos.size()>0) {
			if (rltClassInfos.get(0).getAttrDefs()!=null) {
				for (CcCiAttrDef attrDef:rltClassInfos.get(0).getAttrDefs()) {
					attrNames.add(attrDef.getProName());
					if (attrDef.getIsMajor()>0) {
						subCodes.add(attrDef.getProStdName());
					}
				}
			}
		}
        List<DixBindCiRltRequestDto> reqBeans  = new ArrayList<DixBindCiRltRequestDto>();
        if (json.containsKey("datas") && json.getJSONArray("datas")!=null) {
        	JSONArray datas = json.getJSONArray("datas");
        	for (int i=0;i<datas.size();i++) {
        		JSONObject data = datas.getJSONObject(i);
        		if (data.containsKey("sourcePrimaryKey") && data.getJSONArray("sourcePrimaryKey")!=null && data.getJSONArray("sourcePrimaryKey").size()>0 &&
        				data.containsKey("targetPrimaryKey") && data.getJSONArray("targetPrimaryKey")!=null && data.getJSONArray("targetPrimaryKey").size()>0) {
        			DixBindCiRltRequestDto reqBean = new DixBindCiRltRequestDto();
            		reqBean.setRltClassCode(relationName);
            		reqBean.setSourceCiPK(data.getJSONArray("sourcePrimaryKey").toString());
            		reqBean.setTargetCiPK(data.getJSONArray("targetPrimaryKey").toString());
            		Map<String, String> attrs = new HashMap<String, String>();
            		for (String attrName:attrNames) {
            			String value = "";
            			if (data.containsKey("attrs") && data.getJSONObject("attrs")!=null && data.getJSONObject("attrs").containsKey(attrName) && data.getJSONObject("attrs").getString(attrName)!=null) {
            				value = data.getJSONObject("attrs").getString(attrName);
            			}
            			attrs.put(attrName, value);
            		}
            		reqBean.setAttrs(attrs);
            		reqBeans.add(reqBean);
        		}
        	}
        }


        List<DixCIRltInfo> retRltInfos = saveOrUpdateRlts(reqBeans);
        JSONArray create = new JSONArray();
        if (retRltInfos!=null) {
        	for (DixCIRltInfo retRltInfo:retRltInfos) {
        		JSONObject rlt = new JSONObject();
        		rlt.put("relationName", relationName);
        		JSONArray sourcePrimaryKey = JSONArray.parseArray(retRltInfo.getSourcePK());
        		rlt.put("sourcePrimaryKey", sourcePrimaryKey);
        		JSONArray targetPrimaryKey = JSONArray.parseArray(retRltInfo.getTargetPK());
        		rlt.put("targetPrimaryKey", targetPrimaryKey);
        		JSONObject attrs = new JSONObject();
    			for (String subCode:subCodes) {
    				String value = null;
    				if (retRltInfo.getAttrs()!=null) {
    					value = retRltInfo.getAttrs().get(subCode);
    				}
    				attrs.put(subCode, value);
    			}
    			rlt.put("attrs", attrs);
    			create.add(rlt);
        	}
        }
        JSONObject ret = new JSONObject();
        ret.put("create", create);
        ret.put("modify", new JSONArray());
        ControllerUtils.returnJson(request, response, ret);
    }

    private List<DixCIRltInfo> saveOrUpdateRlts(List<DixBindCiRltRequestDto> reqBeans) {
    	List<ESCIInfo> cis = getCIsByBindRltDto(reqBeans);
        Map<String, String> codePkDict = Maps.newHashMap();
        cis.forEach(ci -> codePkDict.put(ci.getCiCode(), ci.getCiPrimaryKey()));
        ImportResultMessage res = this.bindCiRltsCore(cis, reqBeans);
        List<ESCIRltInfo> sucessRlts = res.getSucessRlts();
        List<DixCIRltInfo> returnVal = Lists.newArrayList();
        sucessRlts.forEach(rlt -> {
            DixCIRltInfo repDto = BeanUtil.converBean(rlt, DixCIRltInfo.class);
            returnVal.add(repDto);
            repDto.setSourcePK(codePkDict.get(rlt.getSourceCiCode()));
            repDto.setTargetPK(codePkDict.get(rlt.getTargetCiCode()));
        });
        return returnVal;
    }

    private List<ESCIInfo> getCIsByBindRltDto(List<DixBindCiRltRequestDto> reqBeans) {
        // 验证+获取入参中需要使用的ci以及分类
        Assert.notEmpty(reqBeans, "入参为空");
        Set<String> needCIPks = new HashSet<>();
        Set<String> clsCodes = new HashSet<>();
        reqBeans.forEach(reqBean -> {
            ValidDtoUtil.valid(reqBean);
            needCIPks.add(reqBean.getSourceCiPK());
            needCIPks.add(reqBean.getTargetCiPK());
            clsCodes.add(reqBean.getRltClassCode());
        });
        return ciApi.getESCIInfoListByCIPrimaryKeyStrs(needCIPks);
    }

    private ImportResultMessage bindCiRltsCore(List<ESCIInfo> cis, List<DixBindCiRltRequestDto> reqBeans) {
        // 验证+获取入参中需要使用的ci以及分类
        Assert.notEmpty(reqBeans, "入参为空");
        Set<String> needCIPks = new HashSet<>();
        Set<String> clsCodes = new HashSet<>();
        reqBeans.forEach(reqBean -> {
            ValidDtoUtil.valid(reqBean);
            needCIPks.add(reqBean.getSourceCiPK());
            needCIPks.add(reqBean.getTargetCiPK());
            clsCodes.add(reqBean.getRltClassCode());
        });
        // List<ESCIInfo> cis = getCIsByBindRltDto(reqBeans);
        Assert.isTrue(needCIPks.size() == cis.size(), "源/目标指定的业务主键有不存在ci，请检查数据");
        CCcCiClass rltClsQuery = new CCcCiClass();
        rltClsQuery.setClassCodes(clsCodes.toArray(new String[clsCodes.size()]));
        List<CcCiClassInfo> rltClss = rltClassApi.getRltClassByCdt(rltClsQuery);
        Assert.isTrue(rltClss.size() == clsCodes.size(), "指定的关系分类有不存在数据，请检查数据");
        // hashCode:[ci]字典
        Map<Integer, List<ESCIInfo>> hashCodeCIMap = cis.stream().collect(Collectors.groupingBy(ESCIInfo::getHashCode));
        // rltClsCode:rltClsId字典
        Map<String, Long> codeRltClsMap = new HashMap<>();
        rltClss.forEach(rltCls -> codeRltClsMap.put(rltCls.getCiClass().getClassCode().toUpperCase(),
                rltCls.getCiClass().getId()));
        // 进行实体转换，套用已发布api----实际上hashcode可能会重复，需要进一步判定获取，先这样
        reqBeans.forEach(req -> {
            List<String> sourceCIPk = JSON.parseArray(req.getSourceCiPK(), String.class);
            List<String> targetCIPk = JSON.parseArray(req.getTargetCiPK(), String.class);
            String rltClsCode = req.getRltClassCode();
            ESCIInfo sourceCI = hashCodeCIMap.get(CommUtil.getCiMajorHashCode(sourceCIPk)).get(0);
            ESCIInfo targetCI = hashCodeCIMap.get(CommUtil.getCiMajorHashCode(targetCIPk)).get(0);
            Long rltClsId = codeRltClsMap.get(rltClsCode.toUpperCase());
            req.setRltClassId(rltClsId);
            req.setSourceCiId(sourceCI.getId());
            req.setTargetCiId(targetCI.getId());
        });
        List<BindCiRltRequestDto> reqs = BeanUtil.converBean(reqBeans, BindCiRltRequestDto.class);
        ImportResultMessage importResultMessage = ciRltApiSvc.bindCiRlts(new HashSet<>(reqs), true);
        return importResultMessage;
    }

    @Data
    public static class DixCIRltInfo extends ESCIRltInfo {

        private static final long serialVersionUID = 1L;

        private String sourcePK;

        private String targetPK;
    }

    @Data
    public static class DixBindCiRltRequestDto extends BindCiRltRequestDto {

        private String rltClassCode;

        private String sourceCiPK;

        private String targetCiPK;

        @Override
        public void valid() {
            Assert.notNull(this.getSourceCiPK(), "sourceCiPK not null");
            Assert.notNull(this.getTargetCiPK(), "targetCiPK not null");
            Assert.notNull(this.getRltClassCode(), "rltClassCode not null");
        }
    }

    /**
     * 根据【关系ids OR 关系codes】解除ci关系
     *
     */
    @PostMapping("delRltByIdsOrRltCodes")
    @ModDesc(desc = "根据关系ids或codes解除关系", pDesc = "解除关系请求传输对象", pType = DelRltReqDto.class, rDesc = "操作是否成功,1=成功，0=失败", rType = Integer.class)
    public void delRltByIdsOrRltCodes(@RequestBody DelRltReqDto reqDto) {
        Integer re = ciRltApiSvc.delRltByIdsOrRltCodes(reqDto.getRltIds(), reqDto.getRltCodes());
        ControllerUtils.returnJson(request, response, re);
    }

    private Integer delRltByIds(Set<Long> rltIds) {
    	return ciRltApiSvc.delRltByIdsOrRltCodes(rltIds, null);
    }

    /**
     * 删除单条关系
     *
     */
    @PostMapping("deleteRlt")
    @ModDesc(desc = "删除单条关系", pDesc = "删除单条关系", pType = JSONObject.class, rDesc = "被删除的关系的源和目标CI业务主键和子CODE集合", rType = JSONObject.class)
    public void deleteRlt(@RequestBody JSONObject body) throws Exception {
    	ESRltSearchBean reqBean = new ESRltSearchBean();
    	String relationName = null;
        if (body.containsKey("relationName") && body.getString("relationName")!=null && !"".equals(body.getString("relationName").trim())) {
        	relationName = body.getString("relationName").trim();
        	reqBean.setClassCode(relationName);
        } else {
        	throw new Exception("lack of relationName");
        }
        JSONArray sourcePrimaryKey = null;
        if (body.containsKey("sourcePrimaryKey") && body.getJSONArray("sourcePrimaryKey")!=null && body.getJSONArray("sourcePrimaryKey").size()>0) {
        	sourcePrimaryKey = body.getJSONArray("sourcePrimaryKey");
        	Set<String> sourceCiPrimaryKeys = new HashSet<String>();
        	sourceCiPrimaryKeys.add(sourcePrimaryKey.toString());
			reqBean.setSourceCiPrimaryKeys(sourceCiPrimaryKeys);
        } else {
        	throw new Exception("lack of sourcePrimaryKey");
        }
        JSONArray targetPrimaryKey = null;
        if (body.containsKey("targetPrimaryKey") && body.getJSONArray("targetPrimaryKey")!=null && body.getJSONArray("targetPrimaryKey").size()>0) {
        	targetPrimaryKey = body.getJSONArray("targetPrimaryKey");
        	Set<String> targetCiPrimaryKeys = new HashSet<String>();
        	targetCiPrimaryKeys.add(targetPrimaryKey.toString());
			reqBean.setTargetCiPrimaryKeys(targetCiPrimaryKeys);
        } else {
        	throw new Exception("lack of targetPrimaryKey");
        }
        JSONObject attrs = null;
        if (body.containsKey("attrs") && body.getJSONObject("attrs")!=null && body.getJSONObject("attrs").size()>0) {
        	attrs = body.getJSONObject("attrs");
        	List<ESAttrBean> andAttrs = new ArrayList<ESAttrBean>();
        	for (String key:attrs.keySet()) {
        		String value = attrs.getString(key);
        		ESAttrBean andAttr = new ESAttrBean();
        		andAttr.setKey(key);
        		andAttr.setOptType(1);
        		andAttr.setValue(value);
        		andAttrs.add(andAttr);
        	}
			reqBean.setAndAttrs(andAttrs);
        }
        reqBean.setPageSize(10);
        reqBean.setPageNum(1);
        Page<CcCiRltInfo> rltInfoPage = searchRltByBeanPrivate(reqBean);
        Long rltId = null;
        if (rltInfoPage!=null && rltInfoPage.getData()!=null) {
        	if (rltInfoPage.getData().size()==1) {
        		rltId = rltInfoPage.getData().get(0).getCiRlt().getId();
        		Set<Long> rltIds = new HashSet<Long>();
        		rltIds.add(rltId);
        		Integer success = delRltByIds(rltIds);
        		JSONArray deletes = new JSONArray();
        		if (success==1) {
        			JSONObject delete = new JSONObject();
        			delete.put("relationName", relationName);
        			delete.put("sourcePrimaryKey", sourcePrimaryKey);
        			delete.put("targetPrimaryKey", targetPrimaryKey);
        			delete.put("attrs", attrs);
        			deletes.add(delete);
        		}
        		JSONObject ret = new JSONObject();
        		ret.put("delete", deletes);
        		ControllerUtils.returnJson(request, response, ret);
        	} else if (rltInfoPage.getData().size()==0) {
        		throw new Exception("relation is not exist");
        	} else {
        		throw new Exception("relation is not unique");
        	}
        } else {
        	throw new Exception("relation is not exist");
        }
    }

    @PostMapping("delRltByQuery")
    @ModDesc(desc = "根据条件删除关系", pDesc = "解除关系请求传输对象", pType = DelRltReqDto.class, rDesc = "操作是否成功,1=成功，0=失败", rType = Integer.class)
    public void delRltByIdsOrRltCodes(@RequestBody ESRltSearchBean reqDto) {
        reqDto.setPageNum(1);
        reqDto.setPageSize(1);
        Page<CcCiRltInfo> res = ciRltApiSvc.searchRltByBean(reqDto);
        Long size = res.getTotalRows();
        Integer result = 0;
        if (size > 0) {
            reqDto.setPageSize(size.intValue());
            res = ciRltApiSvc.searchRltByBean(reqDto);
            Set<Long> delIds = new HashSet<>();
            res.getData().forEach(rep -> delIds.add(rep.getCiRlt().getId()));
            result = ciRltApiSvc.delRltByIdsOrRltCodes(delIds, null);
        }
        // Integer re = ciRltApiSvc.delRltByIdsOrRltCodes(reqDto.getRltIds(),
        // reqDto.getRltCodes());
        ControllerUtils.returnJson(request, response, result);
    }

    /**
     * 查询ci关系-根据查询bean
     *
     * @param reqBean
     */
    @PostMapping("searchRltByBean")
    @ModDesc(desc = "条件查询关系数据", pDesc = "查询条件", pType = ESRltSearchBean.class, rDesc = "关系数据分页结果", rType = Page.class, rcType = CcCiRltInfo.class)
    public void searchRltByBean(@RequestBody ESRltSearchBean reqBean) {
        Page<CcCiRltInfo> results = searchRltByBeanPrivate(reqBean);
        ControllerUtils.returnJson(request, response, results);
    }

    private Page<CcCiRltInfo> searchRltByBeanPrivate(ESRltSearchBean reqBean) {
    	return ciRltApiSvc.searchRltByBean(reqBean);
    }
}
