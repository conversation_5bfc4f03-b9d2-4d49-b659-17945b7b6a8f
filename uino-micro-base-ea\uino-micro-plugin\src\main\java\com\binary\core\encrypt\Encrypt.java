package com.binary.core.encrypt;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.Map;


import com.binary.core.exception.BinaryException;
import com.binary.core.exception.EncryptException;
import com.binary.core.util.BinaryUtils;
import org.apache.commons.codec.binary.Base64;

public abstract class Encrypt {
	
	
	private static final Object syncobj = new Object();
	private static final Map<EncryptType, MessageDigest> digests = new HashMap<EncryptType, MessageDigest>();
	
	
	
	private static synchronized MessageDigest getMessageDigest(EncryptType type) {
		MessageDigest digest = digests.get(type);
		if(digest == null) {
//			synchronized (syncobj) {
				digest = digests.get(type);
				if(digest == null) {
					try {
						digest = MessageDigest.getInstance(type.name());
					} catch (NoSuchAlgorithmException e) {
						throw BinaryUtils.transException(e, EncryptException.class);
					}
					digests.put(type, digest);
				}
//			}
		}
		return digest;
	}
	
	
	
	
	/**
	 * MD5加密
	 * @param data : 被加密明文
	 * @return 密文二进制
	 */
	public static synchronized byte[] encryptBinary(String data) {
		return encryptBinary(data, EncryptType.MD5);
	}
	
	
	/**
	 * 加密
	 * @param data : 被加密明文
	 * @param encryptType : 加密类型
	 * @return 密文二进制
	 */
	public static synchronized byte[] encryptBinary(String data, EncryptType encryptType) {
		BinaryUtils.checkNull(encryptType, "encryptType");
		MessageDigest digest = getMessageDigest(encryptType);
		return digest.digest(data.getBytes());
	}
	
	
	
	
	/**
	 * 将byte[]数组转换成16进制数字符串
	 * @param array
	 * @return
	 */
	public static synchronized  String byte2String(byte[] array) {
//		StringBuilder sb = new StringBuilder();
//		for(int i=0; i<array.length; i++) {
//			int a = array[i];
//			if(a < 0) a += 256;
//			
//			String s = Integer.toHexString(a);
//			if(s.length() < 2) sb.append("0");
//			sb.append(s);
//		}
//		return sb.toString();
		char[] cs = new char[array.length*2];
		for(int i=0,j=0; i<cs.length; i+=2,j++) {
			int a = array[j];
			if(a < 0) a += 256;
			String s = Integer.toHexString(a);
			if(s.length() < 2) {
				cs[i] = '0';
				cs[i+1] = s.charAt(0);
			}else {
				cs[i] = s.charAt(0);
				cs[i+1] = s.charAt(1);
			}
		}
		return new String(cs);
	}
	
	
	
	
	
	
	/**
	 * 将16进制数字符串转换成byte[]数组
	 * @param s
	 * @return
	 */
	public static synchronized byte[] string2Byte(String hexString) {
		int length = hexString.length();
//		char[] cs = hexString.toCharArray();
		byte[] array = new byte[length/2];
		for(int i=0,j=0; i<length; i+=2,j++) {
//			String s = cs[i]+""+cs[i+1];
//			String s = new String(new char[]{[i], cs[i+1]});
			String s = hexString.substring(i, i+2);
			int a = Integer.valueOf(s, 16);
			array[j] = (byte)(a - 256);
		}
		return array;
	}
	
	
	
	/**
	 * MD5加密
	 * @param data : 被加密明文
	 * @return 密文
	 */
	public static synchronized String encrypt(String data) {
		return encrypt(data, EncryptType.MD5);
	}
	
	
	/**
	 * 加密
	 * @param data : 被加密明文
	 * @param encryptType : 加密类型
	 * @return 密文
	 */
	public static synchronized String encrypt(String data, EncryptType encryptType) {
		byte[] bys = encryptBinary(data, encryptType);
		return byte2String(bys);
	}
	
	
	
	
	
	/**
	 * MD5加密
	 * @param data : 被加密明文
	 * @param encryptCount : 连续加密次数
	 * @return 密文
	 */
	public static synchronized String encrypt(String data, int encryptCount) {
		if(encryptCount < 1) throw new EncryptException(" the encryptCount is error '"+encryptCount+"'! ");
		String code = encrypt(data);
		return encryptCount==1 ? code : encrypt(code, encryptCount-1);
	}
	
	
	
	/**
	 * SHA加密
	 * @param data : 被加密明文
	 * @param encryptType : 加密类型
	 * @param encryptCount : 连续加密次数
	 * @return 密文二进制
	 */
	public static synchronized String encrypt(String data, EncryptType encryptType, int encryptCount) {
		if(encryptCount < 1) throw new EncryptException(" the encryptCount is error '"+encryptCount+"'! ");
		String code = encrypt(data, encryptType);
		return encryptCount==1 ? code : encrypt(code, encryptType, encryptCount-1);
	}
	
	
	
	
	
	/**
	 * Base64加密
	 * @param data
	 * @return
	 */
	public static synchronized String encodeBase64(String data) {
		return encodeBase64(data, null);
	}
	
	
	/**
	 * Base64加密
	 * @param data
	 * @param charset 缺省为UTF-8
	 * @return
	 */
	public static synchronized String encodeBase64(String data, String charset) {
		if(BinaryUtils.isEmpty(charset)) charset = "UTF-8";
		
		try {
			return Base64.encodeBase64String(data.getBytes(charset));
		} catch (UnsupportedEncodingException e) {
			throw BinaryUtils.transException(e, BinaryException.class);
		}
	}
	
	
	
	
	
	/**
	 * Base64解密
	 * @param data
	 * @return
	 */
	public static synchronized String decodeBase64(String data) {
		return decodeBase64(data, null);
	}
	
	
	/**
	 * Base64解密
	 * @param data
	 * @param charset 缺省为UTF-8
	 * @return
	 */
	public static synchronized String decodeBase64(String data, String charset) {
		if(BinaryUtils.isEmpty(charset)) charset = "UTF-8";
		
		try {
			return new String(Base64.decodeBase64(data), charset);
		} catch (IOException e) {
			throw BinaryUtils.transException(e, BinaryException.class);
		}
	}
	
	
	
}




