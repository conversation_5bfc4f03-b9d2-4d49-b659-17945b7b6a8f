package com.uino.bean.cmdb.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 *
 */
@Data
@ApiModel(value="可视化模型类",description = "可视化模型信息")
public class ESVisualModel implements Serializable {
	
	@ApiModel(value="",description = "")
	public static class ImpactPath {

		@ApiModelProperty(value="")
		private Long focusClassId;

		@ApiModelProperty(value="源分类id",example = "123")
		private Long sourceClassId;

		@ApiModelProperty(value="目标分类id",example = "123")
		private Long targetClassId;

		@ApiModelProperty(value="关系分类id",example="123")
		private Long rltClassId;
		
		public Long getFocusClassId() {
			return focusClassId;
		}
		public void setFocusClassId(Long focusClassId) {
			this.focusClassId = focusClassId;
		}
		public Long getSourceClassId() {
			return sourceClassId;
		}
		public void setSourceClassId(Long sourceClassId) {
			this.sourceClassId = sourceClassId;
		}
		public Long getTargetClassId() {
			return targetClassId;
		}
		public void setTargetClassId(Long targetClassId) {
			this.targetClassId = targetClassId;
		}
		public Long getRltClassId() {
			return rltClassId;
		}
		public void setRltClassId(Long rltClassId) {
			this.rltClassId = rltClassId;
		}
		
	}

	private static final long serialVersionUID = 4138238110179618785L;

	@ApiModelProperty(value="id",example="123")
	private Long id;

	@ApiModelProperty(value="名称",example = "sport")
	private String name;

	@ApiModelProperty(value="查看权限角色id")
	private List<Long> viewRoleIds;

	@ApiModelProperty(value="维护权限角色id")
	private List<Long> editRoleIds;

	@ApiModelProperty(value="适用的组织部门id")
	private Long orgId;

	@ApiModelProperty(value="发布的版本号")
	private Long publishVersion;

	@ApiModelProperty(value="关联的发布后的元模型id")
	private Long linkPublishedId;

	@ApiModelProperty(value="json")
	private String json;

	@ApiModelProperty(value="影响路径")
	private List<ImpactPath> impactPaths;

	@ApiModelProperty(value="图的点位信息")
	private String thumbnail;

	@ApiModelProperty(value="是否可用",example = "true/false")
	private Boolean enable;

	@ApiModelProperty(value="是否是历史",example = "true/false")
	private Boolean history = Boolean.TRUE;

	@ApiModelProperty(value="是否是在审批中",example = "0未审批，1审批中不可编辑，2审批中可编辑")
	private Integer approve = 0;

	@ApiModelProperty(value="前端存元模型的元数据",example = "")
	private String selectedShapeGroupIds;

	@ApiModelProperty(value="前端存储页面配置字段",example = "123")
	private String pagesetting;

	@ApiModelProperty(value="引用元模型字段",example = "123")
	private Long[] referModelIds;

	@ApiModelProperty(value="所属域id",example = "123")
	private Long domainId;

	@ApiModelProperty(value="状态",example = "0")
	private Integer status = 1;

	@ApiModelProperty(value="创建时间")
	private Long createTime;

	@ApiModelProperty(value="修改时间")
	private Long modifyTime;

	@ApiModelProperty(value="是否选择",example = "true/false")
	private Boolean select;

	@ApiModelProperty(value="创建人",example = "admin")
	private String creater;

	@ApiModelProperty(value="修改人",example = "admin")
	private String modifier;

}
