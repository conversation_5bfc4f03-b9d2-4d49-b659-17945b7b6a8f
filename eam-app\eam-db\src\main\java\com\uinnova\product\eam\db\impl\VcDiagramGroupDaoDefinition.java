package com.uinnova.product.eam.db.impl;


import com.binary.framework.dao.DaoDefinition;

import com.uinnova.product.eam.comm.model.CVcDiagramGroup;
import com.uinnova.product.eam.comm.model.VcDiagramGroup;


/**
 * 视图组表[VC_DIAGRAM_GROUP]数据访问对象定义实现
 */
public class VcDiagramGroupDaoDefinition implements DaoDefinition<VcDiagramGroup, CVcDiagramGroup> {


	@Override
	public Class<VcDiagramGroup> getEntityClass() {
		return VcDiagramGroup.class;
	}


	@Override
	public Class<CVcDiagramGroup> getConditionClass() {
		return CVcDiagramGroup.class;
	}


	@Override
	public String getTableName() {
		return "VC_DIAGRAM_GROUP";
	}


	@Override
	public boolean hasDataStatusField() {
		return false;
	}


	@Override
	public void setDataStatusValue(VcDiagramGroup record, int status) {
	}


	@Override
	public void setDataStatusValue(CVcDiagramGroup cdt, int status) {
	}


	@Override
	public void setCreatorValue(VcDiagramGroup record, String creator) {
	}


	@Override
	public void setModifierValue(VcDiagramGroup record, String modifier) {
	}


}


