<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:flowable="http://flowable.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://www.flowable.org/processdef">
  <process id="visual_model_publish_approve" name="元模型发布审批" isExecutable="true">
    <documentation>元模型发布审批</documentation>
    <startEvent id="startEvent1" flowable:formFieldValidation="true"></startEvent>
    <userTask id="sid-E99DE732-055F-4B18-8C03-5E8803560F72" name="提交人（处理）" flowable:category="submit" flowable:formFieldValidation="true"></userTask>
    <exclusiveGateway id="sid-8FFD863B-FDD9-417A-82CB-CE3CFE13494F"></exclusiveGateway>
    <userTask id="sid-28EFA107-0C58-4619-BFFA-51A7B0F868E8" name="元模型审批角色（审批）" flowable:assignee="${assignee}" flowable:formFieldValidation="true">
      <extensionElements>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
      <multiInstanceLoopCharacteristics isSequential="false" flowable:collection="assigneeList" flowable:elementVariable="assignee">
        <completionCondition>${multiInstanceCompleteExecution.executeByOneUserConditionImmediately(execution)}</completionCondition>
      </multiInstanceLoopCharacteristics>
    </userTask>
    <exclusiveGateway id="sid-DAFCE9FF-E42F-4FDC-85AE-2761AB387D1A"></exclusiveGateway>
    <userTask id="sid-51B5227C-C4A2-4396-AA2E-5BCB490C0BD4" name="提交人（处理）" flowable:assignee="$INITIATOR" flowable:category="submit" flowable:formFieldValidation="true">
      <documentation>rectification</documentation>
      <extensionElements>
        <flowable:executionListener event="start" delegateExpression="${pushPlanNoticeListener}"></flowable:executionListener>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[true]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <endEvent id="sid-540F7416-E10E-4A4E-A406-55BB2705BE7E">
      <extensionElements>
        <flowable:executionListener event="end" delegateExpression="${pushPlanNoticeListener}"></flowable:executionListener>
      </extensionElements>
    </endEvent>
    <sequenceFlow id="sid-34395A7C-50A6-4319-B666-A4E80850BDEE" sourceRef="startEvent1" targetRef="sid-E99DE732-055F-4B18-8C03-5E8803560F72"></sequenceFlow>
    <sequenceFlow id="sid-FDA47F47-BB1C-4FFB-96F9-EC5F83A130AE" sourceRef="sid-E99DE732-055F-4B18-8C03-5E8803560F72" targetRef="sid-8FFD863B-FDD9-417A-82CB-CE3CFE13494F"></sequenceFlow>
    <endEvent id="sid-1B758465-4F5A-4869-8171-238BAF73D01F"></endEvent>
    <sequenceFlow id="sid-83836B10-FD6A-4A00-ADF2-58249D5544DE" sourceRef="sid-28EFA107-0C58-4619-BFFA-51A7B0F868E8" targetRef="sid-DAFCE9FF-E42F-4FDC-85AE-2761AB387D1A"></sequenceFlow>
    <sequenceFlow id="sid-51360696-D796-4E34-89DA-9906A9637126" name="同意" sourceRef="sid-8FFD863B-FDD9-417A-82CB-CE3CFE13494F" targetRef="sid-28EFA107-0C58-4619-BFFA-51A7B0F868E8">
      <documentation>元模型管理员</documentation>
      <extensionElements>
        <flowable:executionListener event="end" delegateExpression="${generateUserByRoleListener}"></flowable:executionListener>
      </extensionElements>
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${pass=='pass'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-206CA60A-2285-4144-9374-D7D9BA9429A1" name="驳回" sourceRef="sid-DAFCE9FF-E42F-4FDC-85AE-2761AB387D1A" targetRef="sid-51B5227C-C4A2-4396-AA2E-5BCB490C0BD4">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${goOut=='noPass'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-F2602296-C98B-4CCA-9203-0A19BB251975" name="重新提交" sourceRef="sid-51B5227C-C4A2-4396-AA2E-5BCB490C0BD4" targetRef="sid-8FFD863B-FDD9-417A-82CB-CE3CFE13494F"></sequenceFlow>
    <sequenceFlow id="sid-EF7EFC62-9828-413A-8F2B-DD394649DC8B" name="同意" sourceRef="sid-DAFCE9FF-E42F-4FDC-85AE-2761AB387D1A" targetRef="sid-540F7416-E10E-4A4E-A406-55BB2705BE7E">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${goOut=='pass'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-5AA45011-6CF2-4036-B10C-3FF1E6BDD452" name="终止" sourceRef="sid-8FFD863B-FDD9-417A-82CB-CE3CFE13494F" targetRef="sid-1B758465-4F5A-4869-8171-238BAF73D01F">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${pass=='cancel'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-10D31E8A-9E67-4571-AB80-E4725460129B" name="终止" sourceRef="sid-DAFCE9FF-E42F-4FDC-85AE-2761AB387D1A" targetRef="sid-1B758465-4F5A-4869-8171-238BAF73D01F">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${goOut=='cancel'}]]></conditionExpression>
    </sequenceFlow>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_visual_model_publish_approve">
    <bpmndi:BPMNPlane bpmnElement="visual_model_publish_approve" id="BPMNPlane_visual_model_publish_approve">
      <bpmndi:BPMNShape bpmnElement="startEvent1" id="BPMNShape_startEvent1">
        <omgdc:Bounds height="30.0" width="30.0" x="45.0" y="315.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-E99DE732-055F-4B18-8C03-5E8803560F72" id="BPMNShape_sid-E99DE732-055F-4B18-8C03-5E8803560F72">
        <omgdc:Bounds height="80.0" width="100.0" x="135.0" y="290.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-8FFD863B-FDD9-417A-82CB-CE3CFE13494F" id="BPMNShape_sid-8FFD863B-FDD9-417A-82CB-CE3CFE13494F">
        <omgdc:Bounds height="40.0" width="40.0" x="300.0" y="310.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-28EFA107-0C58-4619-BFFA-51A7B0F868E8" id="BPMNShape_sid-28EFA107-0C58-4619-BFFA-51A7B0F868E8">
        <omgdc:Bounds height="80.0" width="100.0" x="405.0" y="289.9999905824664"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-DAFCE9FF-E42F-4FDC-85AE-2761AB387D1A" id="BPMNShape_sid-DAFCE9FF-E42F-4FDC-85AE-2761AB387D1A">
        <omgdc:Bounds height="40.0" width="40.0" x="555.0" y="310.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-51B5227C-C4A2-4396-AA2E-5BCB490C0BD4" id="BPMNShape_sid-51B5227C-C4A2-4396-AA2E-5BCB490C0BD4">
        <omgdc:Bounds height="80.0" width="100.0" x="270.0" y="105.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-540F7416-E10E-4A4E-A406-55BB2705BE7E" id="BPMNShape_sid-540F7416-E10E-4A4E-A406-55BB2705BE7E">
        <omgdc:Bounds height="28.0" width="28.0" x="674.9999597668666" y="315.9999905824664"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-1B758465-4F5A-4869-8171-238BAF73D01F" id="BPMNShape_sid-1B758465-4F5A-4869-8171-238BAF73D01F">
        <omgdc:Bounds height="28.0" width="28.0" x="306.0" y="450.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="sid-F2602296-C98B-4CCA-9203-0A19BB251975" id="BPMNEdge_sid-F2602296-C98B-4CCA-9203-0A19BB251975">
        <omgdi:waypoint x="320.0" y="184.95"></omgdi:waypoint>
        <omgdi:waypoint x="320.0" y="310.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-10D31E8A-9E67-4571-AB80-E4725460129B" id="BPMNEdge_sid-10D31E8A-9E67-4571-AB80-E4725460129B">
        <omgdi:waypoint x="575.5" y="349.4429079341317"></omgdi:waypoint>
        <omgdi:waypoint x="575.5" y="464.0"></omgdi:waypoint>
        <omgdi:waypoint x="333.949921022402" y="464.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-206CA60A-2285-4144-9374-D7D9BA9429A1" id="BPMNEdge_sid-206CA60A-2285-4144-9374-D7D9BA9429A1">
        <omgdi:waypoint x="575.0" y="310.0"></omgdi:waypoint>
        <omgdi:waypoint x="575.0" y="145.0"></omgdi:waypoint>
        <omgdi:waypoint x="369.94999999996094" y="145.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-5AA45011-6CF2-4036-B10C-3FF1E6BDD452" id="BPMNEdge_sid-5AA45011-6CF2-4036-B10C-3FF1E6BDD452">
        <omgdi:waypoint x="320.42857142857144" y="349.5140683696469"></omgdi:waypoint>
        <omgdi:waypoint x="320.0522468303287" y="450.0000966591796"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-FDA47F47-BB1C-4FFB-96F9-EC5F83A130AE" id="BPMNEdge_sid-FDA47F47-BB1C-4FFB-96F9-EC5F83A130AE">
        <omgdi:waypoint x="234.9499999998759" y="330.0"></omgdi:waypoint>
        <omgdi:waypoint x="300.0" y="330.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-34395A7C-50A6-4319-B666-A4E80850BDEE" id="BPMNEdge_sid-34395A7C-50A6-4319-B666-A4E80850BDEE">
        <omgdi:waypoint x="74.94999883049303" y="330.0"></omgdi:waypoint>
        <omgdi:waypoint x="135.0" y="330.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-83836B10-FD6A-4A00-ADF2-58249D5544DE" id="BPMNEdge_sid-83836B10-FD6A-4A00-ADF2-58249D5544DE">
        <omgdi:waypoint x="504.95000000000005" y="329.99999450251477"></omgdi:waypoint>
        <omgdi:waypoint x="555.0" y="329.9999984304112"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-51360696-D796-4E34-89DA-9906A9637126" id="BPMNEdge_sid-51360696-D796-4E34-89DA-9906A9637126">
        <omgdi:waypoint x="339.93841681495843" y="329.99999780987616"></omgdi:waypoint>
        <omgdi:waypoint x="404.9999999998964" y="329.9999906864973"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-EF7EFC62-9828-413A-8F2B-DD394649DC8B" id="BPMNEdge_sid-EF7EFC62-9828-413A-8F2B-DD394649DC8B">
        <omgdi:waypoint x="594.9412593114827" y="329.99999834780067"></omgdi:waypoint>
        <omgdi:waypoint x="674.9999597668666" y="329.9999917348756"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>