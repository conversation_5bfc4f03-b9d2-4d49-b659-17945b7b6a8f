package com.uino.web.cmdb.mvc;

import java.util.List;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import com.uino.bean.permission.base.SysUser;
import com.uino.init.api.ApiResult;
import com.uino.init.api.ApiVersion;
import com.uino.util.sys.SysUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.binary.core.exception.MessageException;
import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uino.bean.cmdb.base.ESRltRuleInfo;
import com.uino.api.client.cmdb.IRltRuleApiSvc;
import com.uino.web.BaseMvc;

/**
 * 关系遍历
 *
 * <AUTHOR>
 * @data 2019/8/1 11:04.
 */
@ApiVersion(1)
@Api(value = "关系遍历", tags = {"关系管理"})
@RestController
@RequestMapping(value = "/cmdb/rltRule")
public class RltRuleMvc extends BaseMvc {

    @Autowired
    private IRltRuleApiSvc rltRuleApiSvc;

    /**
     * 新增或更新规则
     *
     * @param rltRuleInfo 规则
     */
    @ApiOperation(value="保存关系遍历规则")
    @PostMapping("saveOrUpdate")
    @ModDesc(desc = "保存关系遍历规则", pDesc = "规则数据对象", pType = ESRltRuleInfo.class, rDesc = "规则id", rType = Long.class)
    public ApiResult<Long> saveOrUpdate(@RequestBody ESRltRuleInfo rltRuleInfo, HttpServletRequest request, HttpServletResponse response) {
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        rltRuleInfo.setDomainId(currentUserInfo.getDomainId());
        Long id = rltRuleApiSvc.saveOrUpdate(rltRuleInfo);
        return ApiResult.ok(this).data(id);
    }

    /**
     * 删除关系遍历规则
     *
     * @param id 规则id
     */
    @ApiOperation(value="删除关系遍历规则")
    @PostMapping("delete")
    @ModDesc(desc = "删除关系遍历规则", pDesc = "规则id", pType = Long.class, rDesc = "操作是否成功", rType = Boolean.class)
    public ApiResult<Boolean> deleteById(@RequestBody Long id, HttpServletRequest request, HttpServletResponse response) {
        rltRuleApiSvc.deleteById(id);
        return ApiResult.ok(this).data(true);
    }

    /**
     * 根据id查询规则
     *
     * @param id id
     */
    @ApiOperation(value="根据id查询关系遍历规则")
    @PostMapping("getById")
    @ModDesc(desc = "跟据id查询关系遍历规则", pDesc = "规则id", pType = Long.class, rDesc = "关系遍历规则信息", rType = ESRltRuleInfo.class)
    public ApiResult<ESRltRuleInfo > queryInfoById(@RequestBody Long id, HttpServletRequest request, HttpServletResponse response) {
        ESRltRuleInfo rltRuleInfo = rltRuleApiSvc.queryInfoById(id);
        return ApiResult.ok(this).data(rltRuleInfo);
    }

    /**
     * 查询所有规则
     */
    @ApiOperation(value="查询所有关系遍历规则")
    @PostMapping("queryAll")
    @ModDesc(desc = "查询所有关系遍历规则", pDesc = "无", rDesc = "规则列表", rType = List.class, rcType = ESRltRuleInfo.class)
    public ApiResult<List<ESRltRuleInfo>> queryAll(HttpServletRequest request, HttpServletResponse response) {
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        List<ESRltRuleInfo> rltRuleInfos = rltRuleApiSvc.queryInfo(currentUserInfo.getDomainId());
        return ApiResult.ok(this).data(rltRuleInfos);
    }


    /**
     * 分页查询规则
     *
     * @param json json
     */
    @ApiOperation(value="分页查询关系遍历规则")
    @PostMapping("queryInfoPage")
    @ModDesc(desc = "分页查询关系遍历规则", pDesc = "查询条件", pType = JSONObject.class, rDesc = "规则分页查询结果", rType = Page.class, rcType = ESRltRuleInfo.class)
    public ApiResult<Page<ESRltRuleInfo>> queryInfoPage(@RequestBody JSONObject json, HttpServletRequest request, HttpServletResponse response) {
        int pageNum;
        if (json.containsKey("pageNum") && json.getInteger("pageNum") != null) {
            pageNum = json.getInteger("pageNum");
        } else {
            throw MessageException.i18n("BS_MVTYPE_ARG_ERROR");
        }
        int pageSize;
        if (json.containsKey("pageSize") && json.getInteger("pageSize") != null) {
            pageSize = json.getInteger("pageSize");
        } else {
            throw MessageException.i18n("BS_MVTYPE_ARG_ERROR");
        }
        String name = null;
        if (json.containsKey("name") && json.getString("name") != null) {
            name = json.getString("name");
        }
        String orders = null;
        if (json.containsKey("orders") && json.getString("orders") != null) {
            orders = json.getString("orders");
        }
        boolean isAsc = true;
        if (json.containsKey("isAsc") && json.getBoolean("isAsc") != null) {
            isAsc = json.getBoolean("isAsc");
        }
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        Page<ESRltRuleInfo> rltRuleInfoPage = rltRuleApiSvc.queryInfoPage(pageNum, pageSize, name, currentUserInfo.getDomainId(), orders, isAsc);
        return ApiResult.ok(this).data(rltRuleInfoPage);
    }


}
