package com.uino.api.client.monitor.local;

import java.util.List;

import com.uino.dao.BaseConst;
import org.elasticsearch.index.query.QueryBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.binary.jdbc.Page;
import com.uino.monitor.performance.IUinoPerformanceSvc;
import com.uino.bean.monitor.buiness.PerformanceQueryDto;
import com.uino.bean.tp.base.FinalPerformanceDTO;
import com.uino.api.client.monitor.IUinoPerformanceApiSvc;

@Service
public class UinoPerformanceApiSvcLocal implements IUinoPerformanceApiSvc {

	@Autowired
	private IUinoPerformanceSvc perfSvc;

	@Override
	public Boolean saveOrUpdatePerformance(FinalPerformanceDTO perf) {
		return perfSvc.saveOrUpdatePerformance(perf);
	}

	@Override
	public Page<FinalPerformanceDTO> queryPerformancePage(PerformanceQueryDto queryDto) {
		return perfSvc.queryPerformancePage(queryDto);
	}

	@Override
	public Boolean saveOrUpdateBatch(List<FinalPerformanceDTO> perfs) {
		return perfSvc.saveOrUpdateBatch(perfs);
	}

	@Override
	public Page<FinalPerformanceDTO> getSortListByQuery(int pageNum, int pageSize, QueryBuilder query, String order,
			Long timeStart, Long timeEnd, boolean asc) {
		return perfSvc.getSortListByQuery(BaseConst.DEFAULT_DOMAIN_ID,pageNum, pageSize, query, order, timeStart, timeEnd, asc);
	}

	@Override
	public Page<FinalPerformanceDTO> getSortListByQuery(Long domainId, int pageNum, int pageSize, QueryBuilder query, String order, Long timeStart, Long timeEnd, boolean asc) {
		return perfSvc.getSortListByQuery(domainId,pageNum, pageSize, query, order, timeStart, timeEnd, asc);
	}



	@Override
	public Page<FinalPerformanceDTO> getLastPerfListByQuery(int pageNum, int pageSize, QueryBuilder query, String order,
			boolean asc) {
		return perfSvc.getLastPerfListByQuery(BaseConst.DEFAULT_DOMAIN_ID,pageNum, pageSize, query, order, asc);
	}

	@Override
	public Page<FinalPerformanceDTO> getLastPerfListByQuery(Long domainId,int pageNum, int pageSize,  QueryBuilder query, String order, boolean asc) {
		return perfSvc.getLastPerfListByQuery(domainId,pageNum, pageSize, query, order, asc);
	}

	@Override
	public List<FinalPerformanceDTO> getCurrentPerformance(List<String> ciCodes, List<String> kpiCodes) {
		return perfSvc.getCurrentPerformance(BaseConst.DEFAULT_DOMAIN_ID,ciCodes, kpiCodes);
	}

	@Override
	public List<FinalPerformanceDTO> getCurrentPerformance(Long domainId, List<String> ciCodes, List<String> kpiCodes) {
		return perfSvc.getCurrentPerformance(domainId,ciCodes, kpiCodes);
	}

	@Override
	public List<FinalPerformanceDTO> getCurrentPerformanceByCiCodes(List<String> ciCodes) {
		return perfSvc.getCurrentPerformanceByCiCodes(BaseConst.DEFAULT_DOMAIN_ID,ciCodes);
	}

	@Override
	public List<FinalPerformanceDTO> getCurrentPerformanceByCiCodes(Long domainId, List<String> ciCodes) {
		return perfSvc.getCurrentPerformanceByCiCodes(domainId,ciCodes);
	}

	@Override
	public List<FinalPerformanceDTO> getPerformanceByConditional(String ciCode, String kpiCode, Long startTime,
			Long endTime) {
		return perfSvc.getPerformanceByConditional(BaseConst.DEFAULT_DOMAIN_ID,ciCode, kpiCode, startTime, endTime);
	}

	@Override
	public List<FinalPerformanceDTO> getPerformanceByConditional(Long domainId, String ciCode, String kpiCode, Long startTime, Long endTime) {
		return perfSvc.getPerformanceByConditional(domainId,ciCode, kpiCode, startTime, endTime);
	}

}
