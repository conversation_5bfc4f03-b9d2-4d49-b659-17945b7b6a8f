package com.uinnova.product.eam.service.es;

import com.uinnova.product.eam.comm.model.es.DiagramApproveRlt;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;


@Service
public class DiagramApproveRltDao extends AbstractESBaseDao<DiagramApproveRlt, DiagramApproveRlt> {

    @Override
    public String getIndex() {
        return "uino_diagram_approve_rlt";
    }

    @Override
    public String getType() {
        return getIndex();
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }


}

