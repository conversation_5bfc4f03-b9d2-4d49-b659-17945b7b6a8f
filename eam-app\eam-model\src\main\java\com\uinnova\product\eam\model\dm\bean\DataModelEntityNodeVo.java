package com.uinnova.product.eam.model.dm.bean;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * 数据建模键区实体
 * <AUTHOR>
 */
@Data
public class DataModelEntityNodeVo {
    private String ciCode;
    private Long classId;
    private String label;
    private String ciLabel;
    private String icon;
    private String shape;
    private Integer order;
    private String inheritId;
    private Boolean primaryKey;
    private Boolean isSelected;
    private Map<String, String> attrs = new HashMap<>();
}

