package com.uinnova.product.eam.comm.model;




import com.binary.framework.bean.Condition;
import com.binary.framework.bean.annotation.Comment;


@Comment("视图操作表[VC_DIAGRAM_OP]")
public class CVcDiagramOp implements Condition {
	private static final long serialVersionUID = 1L;


	@Comment("ID[ID] operate-Equal[=]")
	private Long id;


	@Comment("ID[ID] operate-In[in]")
	private Long[] ids;


	@Comment("ID[ID] operate-GTEqual[>=]")
	private Long startId;

	@Comment("ID[ID] operate-LTEqual[<=]")
	private Long endId;


	@Comment("视图ID[DIAGRAM_ID] operate-Equal[=]")
	private Long diagramId;


	@Comment("视图ID[DIAGRAM_ID] operate-In[in]")
	private Long[] diagramIds;


	@Comment("视图ID[DIAGRAM_ID] operate-GTEqual[>=]")
	private Long startDiagramId;

	@Comment("视图ID[DIAGRAM_ID] operate-LTEqual[<=]")
	private Long endDiagramId;


	@Comment("来源D[SOURCE_ID] operate-Equal[=]")
	private Long sourceId;


	@Comment("来源D[SOURCE_ID] operate-In[in]")
	private Long[] sourceIds;


	@Comment("来源D[SOURCE_ID] operate-GTEqual[>=]")
	private Long startSourceId;

	@Comment("来源D[SOURCE_ID] operate-LTEqual[<=]")
	private Long endSourceId;


	@Comment("来源类型[SOURCE_TYPE] operate-Equal[=]    1=用户，2=小组")
	private Integer sourceType;


	@Comment("来源类型[SOURCE_TYPE] operate-In[in]    1=用户，2=小组")
	private Integer[] sourceTypes;


	@Comment("来源类型[SOURCE_TYPE] operate-GTEqual[>=]    1=用户，2=小组")
	private Integer startSourceType;

	@Comment("来源类型[SOURCE_TYPE] operate-LTEqual[<=]    1=用户，2=小组")
	private Integer endSourceType;


	@Comment("操作类型[OP_TYPE] operate-Equal[=]    1=移动到网络运维")
	private Integer opType;


	@Comment("操作类型[OP_TYPE] operate-In[in]    1=移动到网络运维")
	private Integer[] opTypes;


	@Comment("操作类型[OP_TYPE] operate-GTEqual[>=]    1=移动到网络运维")
	private Integer startOpType;

	@Comment("操作类型[OP_TYPE] operate-LTEqual[<=]    1=移动到网络运维")
	private Integer endOpType;


	@Comment("操作描述[OP_DESC] operate-Like[like]")
	private String opDesc;


	@Comment("所属域[DOMAIN_ID] operate-Equal[=]")
	private Long domainId;


	@Comment("所属域[DOMAIN_ID] operate-In[in]")
	private Long[] domainIds;


	@Comment("所属域[DOMAIN_ID] operate-GTEqual[>=]")
	private Long startDomainId;

	@Comment("所属域[DOMAIN_ID] operate-LTEqual[<=]")
	private Long endDomainId;


	@Comment("创建时间[CREATE_TIME] operate-Equal[=]")
	private Long createTime;


	@Comment("创建时间[CREATE_TIME] operate-In[in]")
	private Long[] createTimes;


	@Comment("创建时间[CREATE_TIME] operate-GTEqual[>=]")
	private Long startCreateTime;

	@Comment("创建时间[CREATE_TIME] operate-LTEqual[<=]")
	private Long endCreateTime;


	@Comment("修改时间[MODIFY_TIME] operate-Equal[=]")
	private Long modifyTime;


	@Comment("修改时间[MODIFY_TIME] operate-In[in]")
	private Long[] modifyTimes;


	@Comment("修改时间[MODIFY_TIME] operate-GTEqual[>=]")
	private Long startModifyTime;

	@Comment("修改时间[MODIFY_TIME] operate-LTEqual[<=]")
	private Long endModifyTime;




	public Long getId() {
		return this.id;
	}
	public void setId(Long id) {
		this.id = id;
	}


	public Long[] getIds() {
		return this.ids;
	}
	public void setIds(Long[] ids) {
		this.ids = ids;
	}


	public Long getStartId() {
		return this.startId;
	}
	public void setStartId(Long startId) {
		this.startId = startId;
	}


	public Long getEndId() {
		return this.endId;
	}
	public void setEndId(Long endId) {
		this.endId = endId;
	}


	public Long getDiagramId() {
		return this.diagramId;
	}
	public void setDiagramId(Long diagramId) {
		this.diagramId = diagramId;
	}


	public Long[] getDiagramIds() {
		return this.diagramIds;
	}
	public void setDiagramIds(Long[] diagramIds) {
		this.diagramIds = diagramIds;
	}


	public Long getStartDiagramId() {
		return this.startDiagramId;
	}
	public void setStartDiagramId(Long startDiagramId) {
		this.startDiagramId = startDiagramId;
	}


	public Long getEndDiagramId() {
		return this.endDiagramId;
	}
	public void setEndDiagramId(Long endDiagramId) {
		this.endDiagramId = endDiagramId;
	}


	public Long getSourceId() {
		return this.sourceId;
	}
	public void setSourceId(Long sourceId) {
		this.sourceId = sourceId;
	}


	public Long[] getSourceIds() {
		return this.sourceIds;
	}
	public void setSourceIds(Long[] sourceIds) {
		this.sourceIds = sourceIds;
	}


	public Long getStartSourceId() {
		return this.startSourceId;
	}
	public void setStartSourceId(Long startSourceId) {
		this.startSourceId = startSourceId;
	}


	public Long getEndSourceId() {
		return this.endSourceId;
	}
	public void setEndSourceId(Long endSourceId) {
		this.endSourceId = endSourceId;
	}


	public Integer getSourceType() {
		return this.sourceType;
	}
	public void setSourceType(Integer sourceType) {
		this.sourceType = sourceType;
	}


	public Integer[] getSourceTypes() {
		return this.sourceTypes;
	}
	public void setSourceTypes(Integer[] sourceTypes) {
		this.sourceTypes = sourceTypes;
	}


	public Integer getStartSourceType() {
		return this.startSourceType;
	}
	public void setStartSourceType(Integer startSourceType) {
		this.startSourceType = startSourceType;
	}


	public Integer getEndSourceType() {
		return this.endSourceType;
	}
	public void setEndSourceType(Integer endSourceType) {
		this.endSourceType = endSourceType;
	}


	public Integer getOpType() {
		return this.opType;
	}
	public void setOpType(Integer opType) {
		this.opType = opType;
	}


	public Integer[] getOpTypes() {
		return this.opTypes;
	}
	public void setOpTypes(Integer[] opTypes) {
		this.opTypes = opTypes;
	}


	public Integer getStartOpType() {
		return this.startOpType;
	}
	public void setStartOpType(Integer startOpType) {
		this.startOpType = startOpType;
	}


	public Integer getEndOpType() {
		return this.endOpType;
	}
	public void setEndOpType(Integer endOpType) {
		this.endOpType = endOpType;
	}


	public String getOpDesc() {
		return this.opDesc;
	}
	public void setOpDesc(String opDesc) {
		this.opDesc = opDesc;
	}


	public Long getDomainId() {
		return this.domainId;
	}
	public void setDomainId(Long domainId) {
		this.domainId = domainId;
	}


	public Long[] getDomainIds() {
		return this.domainIds;
	}
	public void setDomainIds(Long[] domainIds) {
		this.domainIds = domainIds;
	}


	public Long getStartDomainId() {
		return this.startDomainId;
	}
	public void setStartDomainId(Long startDomainId) {
		this.startDomainId = startDomainId;
	}


	public Long getEndDomainId() {
		return this.endDomainId;
	}
	public void setEndDomainId(Long endDomainId) {
		this.endDomainId = endDomainId;
	}


	public Long getCreateTime() {
		return this.createTime;
	}
	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}


	public Long[] getCreateTimes() {
		return this.createTimes;
	}
	public void setCreateTimes(Long[] createTimes) {
		this.createTimes = createTimes;
	}


	public Long getStartCreateTime() {
		return this.startCreateTime;
	}
	public void setStartCreateTime(Long startCreateTime) {
		this.startCreateTime = startCreateTime;
	}


	public Long getEndCreateTime() {
		return this.endCreateTime;
	}
	public void setEndCreateTime(Long endCreateTime) {
		this.endCreateTime = endCreateTime;
	}


	public Long getModifyTime() {
		return this.modifyTime;
	}
	public void setModifyTime(Long modifyTime) {
		this.modifyTime = modifyTime;
	}


	public Long[] getModifyTimes() {
		return this.modifyTimes;
	}
	public void setModifyTimes(Long[] modifyTimes) {
		this.modifyTimes = modifyTimes;
	}


	public Long getStartModifyTime() {
		return this.startModifyTime;
	}
	public void setStartModifyTime(Long startModifyTime) {
		this.startModifyTime = startModifyTime;
	}


	public Long getEndModifyTime() {
		return this.endModifyTime;
	}
	public void setEndModifyTime(Long endModifyTime) {
		this.endModifyTime = endModifyTime;
	}


}


