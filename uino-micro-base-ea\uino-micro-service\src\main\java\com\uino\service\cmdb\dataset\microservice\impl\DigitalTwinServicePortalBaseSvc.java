package com.uino.service.cmdb.dataset.microservice.impl;

import com.alibaba.fastjson.JSON;
import com.uino.bean.cmdb.base.dataset.DataSetMallApi;
import com.uino.bean.cmdb.base.dataset.DataSetMallApiType;
import com.uino.bean.dataset.base.DigitalTwinServicePortalInfo;
import com.uino.dao.cmdb.dataset.ESDigitalTwinServicePortalBaseSvc;
import com.uino.dao.util.ESUtil;
import com.uino.service.cmdb.dataset.microservice.IDigitalTwinServicePortalBaseSvc;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class DigitalTwinServicePortalBaseSvc implements IDigitalTwinServicePortalBaseSvc {

    @Value("${request.uri:/twin/portal/execute}")
    private String requestURI;

    @Autowired
    private ESDigitalTwinServicePortalBaseSvc esDigitalTwinServicePortalSvc;

    @Override
    public Long saveOrUpdate(DataSetMallApi dataSetMallApi , Long id) {
        DigitalTwinServicePortalInfo info = new DigitalTwinServicePortalInfo();
        info.setId(ESUtil.getUUID());
        info.setDatasetId(id);
        info.setDomainId(dataSetMallApi.getDomainId());
        info.setCreateTime(new Date().getTime());
        info.setName(dataSetMallApi.getName());
        info.setType(dataSetMallApi.getType());
        info.setPermission("[\"管理员\"]");
        info.setRequestHeader("{\"contentType\":\"application/json;charset=UTF-8\",\"requestMethod\":\"POST\"}");
        info.setResponseCodeExplain("{\"errorCode\":\"500\",\"errorDesc\":\"服务异常请联系管理员\",\"errorInfo\":\"服务异常\"}");
        info.setServiceLabel(JSON.toJSONString(dataSetMallApi.getDataSetLabel()));
        info.setRealType(dataSetMallApi.getType());
        info.setRequestURL(requestURI);
        //info.setRequestQuery("");
        //info.setResponseParam("");
        String requestParam = "";
        String responseParamExample = "";
        if (DataSetMallApiType.Statistic.getCode().equals(dataSetMallApi.getType())){
            //统计类
            /*requestParam = "[{\"name\":\"id\",\"require\":true,\"type\":\"Long\",\"desc\":\"数据集ID\"},{\"name\":\"param\",\"require\":true," +
                    "\"params\":[{\"name\":\"type\",\"require\":true,\"type\":\"Integer\",\"desc\":\"服务类型\"},{\"name\":\"chartType\"," +
                    "\"require\":true,\"type\":\"Integer\",\"desc\":\"图表类型,0=柱状图,1=饼图,2=堆叠图，3=条形图,4=曲线图\"},{\"name\":\"dataSourceType\"," +
                    "\"require\":true,\"type\":\"Integer\",\"desc\":\"数据来源类型,1=配置数据集,2=配置数据\"},{\"name\":\"metricTagName\",\"require\":false," +
                    "\"type\":\"String\",\"desc\":\"标签名\"},{\"name\":\"dimensionClassIds\",\"require\":false,\"subType\":\"Long\",\"type\":\"Array\"," +
                    "\"desc\":\"维度分类id\"},{\"name\":\"statisticalType\",\"require\":false,\"type\":\"Integer\",\"desc\":\"统计类型 [0=计数(去重),1=求和，2=计数]\"}," +
                    "{\"name\":\"dataSourceId\",\"require\":false,\"type\":\"Long\",\"desc\":\"数据来源：配置数据集id/分类id\"},{\"name\":\"dimensionDefId\"," +
                    "\"require\":false,\"type\":\"Long\",\"desc\":\"维度属性id\"},{\"name\":\"statisticClassId\",\"require\":false,\"type\":\"Long\"," +
                    "\"desc\":\"度量分类id\"},{\"name\":\"statisticDefId\",\"require\":false,\"type\":\"Long\",\"desc\":\"度量属性id\"}],\"type\":\"Object\"" +
                    ",\"desc\":\"请求参数\"}]\n";*/
            responseParamExample = "{\"data\":{\"data\":[{\"data\":[1.0,3.0,5.0],\"name\":\"\"}],\"dimension\":[\"fw2\",\"fw1\",\"fw3\"]}}";
        } else if (DataSetMallApiType.Metrics.getCode().equals(dataSetMallApi.getType())){
            //指标类型
            /*requestParam = "[{\"name\":\"id\",\"require\":true,\"type\":\"Long\",\"desc\":\"数据集ID\"},{\"name\":\"param\",\"require\":true," +
                    "\"params\":[{\"name\":\"type\",\"require\":true,\"type\":\"Integer\",\"desc\":\"服务类型\"},{\"name\":\"metricId\"," +
                    "\"require\":true,\"type\":\"Long\",\"desc\":\"指标ID\"},{\"name\":\"classId\",\"require\":true,\"type\":\"Long\"," +
                    "\"desc\":\"分类ID\"},{\"name\":\"metricTagName\",\"require\":false,\"type\":\"String\",\"desc\":\"标签名\"},{\"name\":\"metricTagVals\"," +
                    "\"require\":false,\"subType\":\"String\",\"type\":\"Array\",\"desc\":\"标签值\"},{\"name\":\"timeFlag\",\"require\":false," +
                    "\"type\":\"String\",\"desc\":\"时间范围\"},{\"name\":\"aggFlag\",\"require\":false,\"type\":\"String\"," +
                    "\"desc\":\"输出方式[求和sum,最大值max,最小值min,平均值avg]\"},{\"name\":\"interval\",\"require\":false,\"type\":\"String\"," +
                    "\"desc\":\"汇聚时长\"}],\"type\":\"Object\",\"desc\":\"请求参数\"}]\n";*/
            responseParamExample = "{\"data\":{\"MetricName\":\"入流量\",\"MetricUnit\":\"Kbps\",\"XData\":[\"1625116200000\"," +
                    "\"1625116500000\",\"1625116800000\",\"1625117100000\",\"1625117400000\",\"1625117700000\",\"1625118000000\"," +
                    "\"1625118300000\",\"1625118600000\",\"1625118900000\",\"1625119200000\",\"1625119500000\"]," +
                    "\"ChartsData\":{\"agg\":[\"89\",\"56\",\"18\",\"27\",\"996\",\"883\",\"752\",\"83\",\"65\",\"56\",\"53\",\"48\"]}}}";
        } else {
            //配置类
            /*requestParam = "[{\"name\":\"id\",\"require\":true,\"type\":\"Long\",\"desc\":\"数据集ID\"},{\"name\":\"param\"," +
                    "\"require\":true,\"params\":[{\"name\":\"type\",\"require\":true,\"type\":\"Integer\",\"desc\":\"数据集类型\"}," +
                    "{\"name\":\"username\",\"require\":true,\"type\":\"String\",\"desc\":\"用户名\"},{\"name\":\"password\"," +
                    "\"require\":true,\"type\":\"String\",\"desc\":\"密码\"},{\"name\":\"pageNum\",\"require\":true," +
                    "\"type\":\"Integer\",\"desc\":\"页数\"},{\"name\":\"pageSize\",\"require\":true,\"type\":\"Integer\"," +
                    "\"desc\":\"每页条数\"}],\"type\":\"Object\",\"desc\":\"请求参数\"}]\n";*/
            requestParam = "[{\"name\":\"ciCode\",\"require\":false,\"type\":\"Long\",\"desc\":\"孪生体ID(ciCode)\"},{\"name\":\"pageNum\",\"require\":true,\"type\":\"Integer\",\"desc\":\"页数\"},{\"name\":\"pageSize\"," +
                    "\"require\":true,\"type\":\"Integer\",\"desc\":\"每页条数\"}]";
            responseParamExample = "{\"success\":true,\"code\":200,\"message\":\"\",\"data\":{\"data\":[{\"房屋_主键\":\"fw1\"," +
                    "\"泳池_主键\":\"yc1\"},{\"房屋_主键\":\"fw2\",\"泳池_主键\":\"yc2\"},{\"房屋_主键\":\"fw3\",\"泳池_主键\":\"yc3\"}]," +
                    "\"pageSize\":5,\"totalCount\":3,\"pageNum\":1},\"version\":\"v1\"}";
        }
        info.setRequestParam(requestParam);
        info.setResponseParamExample(responseParamExample);
        if (DataSetMallApiType.Statistic.getCode().equals(dataSetMallApi.getType())){
            info.setType(DataSetMallApiType.Metrics.getCode());
        }
        if (DataSetMallApiType.CiClass.getCode().equals(dataSetMallApi.getType()) ||
                DataSetMallApiType.RelClass.getCode().equals(dataSetMallApi.getType()) ||
                DataSetMallApiType.UpDownNFloor.getCode().equals(dataSetMallApi.getType())){
            info.setType(0);
        }
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("datasetId" , id));
        List<DigitalTwinServicePortalInfo> digitalTwinServiceList = esDigitalTwinServicePortalSvc.getListByQuery(query);
        if (!CollectionUtils.isEmpty(digitalTwinServiceList)){
            DigitalTwinServicePortalInfo digitalTwinServicePortalInfo = digitalTwinServiceList.get(0);
            info.setId(digitalTwinServicePortalInfo.getId());
        }
        return esDigitalTwinServicePortalSvc.saveOrUpdate(info);
    }

    @Override
    public void delete(Long datasetId) {
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("datasetId" , datasetId));
        List<DigitalTwinServicePortalInfo> digitalTwinServiceList = esDigitalTwinServicePortalSvc.getListByQuery(query);
        if (!CollectionUtils.isEmpty(digitalTwinServiceList)){
            List<Long> datasetIds = digitalTwinServiceList.stream()
                    .map(digitalTwinServicePortalInfo -> digitalTwinServicePortalInfo.getId())
                    .collect(Collectors.toList());
            esDigitalTwinServicePortalSvc.deleteByIds(datasetIds);
        }
    }
}
