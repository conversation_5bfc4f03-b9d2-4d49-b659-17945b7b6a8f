package com.uinnova.product.vmdb.provider.ci.bean;

import com.binary.framework.bean.annotation.Comment;

/**
 * CI查询项目
 * 
 * <AUTHOR>
 *
 */
public enum CiQ {

    /**
     * 是否查询CI分类
     */
    @Comment("是否查询CI分类")
    CLASS,

    /**
     * 是否查询CI属性
     */
    @Comment("是否查询CI属性")
    ATTR,

    /**
     * 是否查询属性定义
     */
    @Comment("是否查询属性定义")
    ATTR_DEF,

    /**
     * 是否查询常柱属性映射[已经无效]
     */
    @Comment("是否查询常柱属性映射[已经无效]")
    FIX_MAP

}
