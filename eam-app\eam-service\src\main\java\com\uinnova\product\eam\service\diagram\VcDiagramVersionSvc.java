package com.uinnova.product.eam.service.diagram;


import com.binary.jdbc.Page;
import com.uinnova.product.eam.base.diagram.mix.enums.DiagramQ;
import com.uinnova.product.eam.base.diagram.mix.model.VcDiagramVersionInfo;
import com.uinnova.product.eam.base.diagram.model.CVcDiagramVersion;
import com.uinnova.product.eam.base.diagram.model.VcDiagramVersion;
import com.uinnova.product.eam.model.diagram.VcDiagramInfo;

import java.util.List;

/**
 * 视图历史记录
 * 
 * <AUTHOR>
 * 
 */

public interface VcDiagramVersionSvc {

	/**
	 * 查询指定视图的历史记录
	 * 
	 * @param domainId
	 *            数据域
	 * @param cdt
	 *            查询条件
	 * @param orders
	 *            排序
	 * @return
	 */
	public List<VcDiagramVersion> queryVcDiagramVersionList(Long domainId,
															CVcDiagramVersion cdt, String orders);

	/**
	 * 
	 * @param domainId
	 *            数据域
	 * @param pageNum
	 *            起始页
	 * @param pageSize
	 *            分页大小
	 * @param cdt
	 *            查询条件
	 * @param orders
	 *            排序
	 * @return
	 */
	public Page<VcDiagramVersion> queryVcDiagramVersionPageList(Long domainId,
																Integer pageNum, Integer pageSize, CVcDiagramVersion cdt,
																String orders);
	
	/**
	 * 查询历史记录详细信息
	 * @param domainId 数据域
	 * @param id 历史视图ID
	 * @param diagramQs 查询类型
	 * @return 历史视图详细
	 */
	public VcDiagramVersionInfo queryDiagramVersionInfoById(Long domainId, Long id, DiagramQ[] diagramQs);
	
	
	/**
	 * 保存一个历史记录
	 * @param domainId 数据域
	 * @param diagramId 视图ID
	 * @return 历史ID
	 */
	public Long saveDiagramVersionByDiagramId(Long domainId, Long diagramId);
	
	/**
	 * 保存视图版本
	 * @param domainId 数据域
	 * @param diagramId 视图ID
	 * @param diagramInfo 视图详信息 
	 * @return 版本ID
	 */
	public Long saveDiagramVersionByDiagramInfo(Long domainId, Long diagramId, VcDiagramInfo diagramInfo);
	
	
	/**
	 * 删除一个历史记录
	 * 
	 * @param domainId
	 * @param id
	 * @return
	 */
	public Integer removeDiagramVersionById(Long domainId, Long id);
	
	/**
	 * 保存或者更新历史版本的基本信息
	 * @param domainId
	 * @param diagramVersion 历史版本基本信息
	 * @return 
	 * 
	 * */
	public Long saveOrUpdateDiagramVersion(Long domainId , VcDiagramVersion diagramVersion);

	/**
	 * 根据id更新历史版本描述和版本号信息
	 * @param domainId
	 * @param id
	 * @param versionDesc
	 * @param versionNo
	 * @return
	 * */
	public Long updateDiagramVersionDescAndVersionNo(Long domainId,Long id , String versionDesc,String versionNo);
}
