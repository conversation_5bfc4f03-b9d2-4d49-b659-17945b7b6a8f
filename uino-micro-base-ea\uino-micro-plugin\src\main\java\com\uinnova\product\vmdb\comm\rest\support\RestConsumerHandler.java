package com.uinnova.product.vmdb.comm.rest.support;

import com.uinnova.product.vmdb.comm.i18n.MessageUtil;

import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;
import java.util.HashSet;
import java.util.Set;

/**
 * 
 * <AUTHOR>
 *
 */
public class RestConsumerHandler implements InvocationHandler {

    private static final Set<String> overrideMethods = new HashSet<String>();

    static {
        overrideMethods.add("equals");
        overrideMethods.add("toString");
        overrideMethods.add("hashCode");
        overrideMethods.add("getClass");
    }

    private RestConsumerAware aware;

    public RestConsumerHandler(RestConsumerAware aware) {
        MessageUtil.checkEmpty(aware, "aware");
        this.aware = aware;
    }

    @Override
    public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
        String methodName = method.getName();
        if (overrideMethods.contains(methodName)) {
            return doLocalMethod(proxy, method, args);
        } else {
            return doRemoteMethod(proxy, method, args);
        }
    }

    protected Object doLocalMethod(Object proxy, Method method, Object[] args) {
        String name = method.getName();
        String eqStr = "equals";
        String tsStr = "toString";
        String hcStr = "hashCode";
        if (eqStr.equals(name)) {
            if (args == null || args.length == 0) {
                return false;
            } else {
                return proxy == args[0];
            }
        } else if (tsStr.equals(name)) {
            return proxy.getClass().getName() + "@" + Integer.toHexString(hashCode());
        } else if (hcStr.equals(name)) {
            return proxy.getClass().getName().hashCode();
        }
        return null;
    }

    protected Object doRemoteMethod(Object proxy, Method method, Object[] args) throws Throwable {
        return aware.getRestClient().rest(aware.getProxyClass(), method, args);
    }

}
