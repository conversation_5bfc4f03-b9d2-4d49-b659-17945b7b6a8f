package com.uinnova.product.eam.workable.config;

//import com.fasterxml.jackson.databind.ObjectMapper;
//import org.flowable.rest.service.api.RestResponseFactory;
import org.flowable.spring.SpringProcessEngineConfiguration;
import org.flowable.spring.boot.EngineConfigurationConfigurer;
//import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Flowable配置类
 * flowable官方文档：https://www.flowable.com/open-source/docs/bpmn/ch02-GettingStarted
 * 中文文档：https://tkjohn.github.io/flowable-userguide/#restApiChapter
 *
 * <AUTHOR>
 * @since 2022/2/22 11:10
 */
@Configuration
public class FlowabelConfig implements EngineConfigurationConfigurer<SpringProcessEngineConfiguration> {

    /**
     * 注册RestResponseFactory使flowable的rest接口暴露，配合swagger可以学习调用
     *
     * @return
     */
    //@Bean
    //public RestResponseFactory restResponseFactory() {
    //    return new RestResponseFactory(new ObjectMapper());
    //}

    @Override
    public void configure(SpringProcessEngineConfiguration engineConfiguration) {
        //解决生成的流程图中文乱码问题
        engineConfiguration.setActivityFontName("宋体");
        engineConfiguration.setLabelFontName("宋体");
        engineConfiguration.setAnnotationFontName("宋体");
    }
}
