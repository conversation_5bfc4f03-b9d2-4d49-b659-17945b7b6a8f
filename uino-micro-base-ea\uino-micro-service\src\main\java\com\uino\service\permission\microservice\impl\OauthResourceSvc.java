package com.uino.service.permission.microservice.impl;

import java.util.List;

import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.uino.dao.permission.ESOauthResourceSvc;
import com.uino.service.permission.microservice.IOauthResourceSvc;
import com.uino.bean.permission.base.OauthResourceDetail;
@Service
public class OauthResourceSvc implements IOauthResourceSvc{

	@Autowired
	private ESOauthResourceSvc oauthResourceSvc;
	@Override
	public boolean save(OauthResourceDetail saveDto) {
		//校验
		//保存
		oauthResourceSvc.saveOrUpdate(saveDto);
		return true;
	}

	@Override
	public List<OauthResourceDetail> list() {
		// TODO Auto-generated method stub
		return oauthResourceSvc.getListByQuery(QueryBuilders.boolQuery());
	}

	@Override
	public OauthResourceDetail getDetail(String name) {
		List<OauthResourceDetail> res = oauthResourceSvc.getListByQuery(QueryBuilders.termQuery("name.keyword", name));
        return res != null && res.size() > 0 ? res.get(0) : null;
	}

}
