package com.uino.plugin.classloader;

import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
public class ClassloaderRepository {

	private static Map<String, PluginClassLoader> classloaderMap = new ConcurrentHashMap<>();

//	public static boolean containsClassLoader(String jarPath) {
//		String key = jarPath.substring(jarPath.lastIndexOf("/") + 1, jarPath.lastIndexOf("."));
//		return responsityMap.containsKey(key);
//	}


//	/**
//	 * 加载/boot下的所有jar包，用于启动时初始加载
//	 */
//	public static void loadBoots() {
//		String pluginPath = PluginClassLoader._LOADING_PLUGIN_PATH;
//		log.info("加载全部插件，插件目录：" + pluginPath);
//		List<File> filesByPath = getJarFilesByPath(pluginPath);
//		if (!CollectionUtils.isEmpty(filesByPath)) {
//			for (File file : filesByPath) {
//				try {
//					loadJar(file.getPath());
//				} catch (Exception e){
//					log.error("Jar包加载失败，路径为：{}", file.getPath());
//				}
//			}
//		}
//	}
//
//	/**
//	 * 获取插件目录下所有文件最后修改时间
//	 * @return
//	 */
//	public static Map<String, Long> getJarLastModifiedMap() {
//		String pluginPath = PluginClassLoader._LOADING_PLUGIN_PATH;
//		Map<String, Long> lastModifiedMap = new HashMap<>();
//		if (pluginPath != null) {
//			List<File> fileList = getJarFilesByPath(pluginPath);
//			if (!CollectionUtils.isEmpty(fileList)) {
//				for (File file : fileList) {
//					lastModifiedMap.put(file.getPath().replace(pluginPath, ""), file.lastModified());
//				}
//			}
//		}
//		return lastModifiedMap;
//	}
//



	/**热加载Jar包
	 * @param jarPath
	 * @return
	 */
	public static boolean loadJar(String jarName) {
//		String moduleName = jarPath.substring(jarPath.lastIndexOf("/") + 1, jarPath.lastIndexOf("."));
//		moduleName = moduleName.substring(moduleName.lastIndexOf("\\") + 1);
		if (classloaderMap.containsKey(jarName)) {
			log.warn("已经加载过" + jarName+",先卸载再加载");
			unloadJar(jarName);
		}
		PluginClassLoader moduleClassLoader = new PluginClassLoader(Thread.currentThread().getContextClassLoader());
		classloaderMap.put(jarName, moduleClassLoader);
		return moduleClassLoader.loadJar(jarName);
	}

	/**热卸载Jar包
	 * @param jarPath
	 * @return
	 */
	public static boolean unloadJar(String jarName) {
		try {
			PluginClassLoader moduleClassLoader = null;
			if (classloaderMap.containsKey(jarName)) {
				moduleClassLoader = classloaderMap.get(jarName);
			} else {
				log.error("没有加载过" + jarName);
				return true;
			}
			boolean unloadSuccess = moduleClassLoader.unloadJar(jarName);
			// Jvm卸载class需满足条件：关闭classLoader
			moduleClassLoader.close();
			classloaderMap.remove(jarName);
			System.gc();
			return unloadSuccess;
		} catch (IOException e) {
			log.error("unloadJar IOException",e);
		}
		return true;
	}
}
