package com.uinnova.product.eam.web.eam.mvc;

import com.binary.framework.web.RemoteResult;
import com.uinnova.product.eam.model.TrialSaasTagInfo;
import com.uinnova.product.eam.model.TrialSaasVo;
import com.uinnova.product.eam.service.TrialSaasSvc;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/trial/saas")
public class TrialSaasMvc {

    @Autowired
    private TrialSaasSvc trialSaasSvc;

    @GetMapping("/login/check")
    public RemoteResult loginCheck(@RequestParam String loginCode) {
        TrialSaasVo trialSaasVo  = trialSaasSvc.loginCheck(loginCode);
        return new RemoteResult(trialSaasVo);
    }

    @GetMapping("/tag/info")
    public RemoteResult tagInfo() {
        TrialSaasTagInfo tagInfo  = trialSaasSvc.tagInfo();
        return new RemoteResult(tagInfo);
    }

    @GetMapping("/change/systype/{type}")
    public RemoteResult changeSysType(@PathVariable Integer type) {
        trialSaasSvc.changeSysType(type);
        return new RemoteResult("success");
    }
}
