package com.uino.service.cmdb.dataset.microservice.impl;

import com.uino.dao.cmdb.dataset.ESDataSetExeResultSheetSvc;
import com.uino.service.cmdb.dataset.microservice.IDataSetExeResultSvc;
import com.uino.bean.cmdb.base.dataset.batch.DataSetExeResultSheet;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.List;

/**
 * @Title: DataSetExeResultSvcImpl
 * @Description:
 * @Author: YGQ
 * @Create: 2021-05-31 16:14
 **/
@Service
public class DataSetExeResultSvcImpl implements IDataSetExeResultSvc {

    private final ESDataSetExeResultSheetSvc esDataSetExeResultSheetSvc;

    @Autowired
    public DataSetExeResultSvcImpl(ESDataSetExeResultSheetSvc esDataSetExeResultSheetSvc) {
        this.esDataSetExeResultSheetSvc = esDataSetExeResultSheetSvc;
    }

    @Override
    public void saveOrUpdateBatch(List<DataSetExeResultSheet> dataSetExeResultSheets) {
        esDataSetExeResultSheetSvc.saveOrUpdateBatch(dataSetExeResultSheets);
    }

    @Override
    public void deleteByQuery(Long dataSetId) {
        Assert.notNull(dataSetId, ">>> data set id is not empty");
        BoolQueryBuilder delQuery = QueryBuilders.boolQuery();
        delQuery.must(QueryBuilders.termQuery("dataSetId", dataSetId));
        esDataSetExeResultSheetSvc.deleteByQuery(delQuery, true);
    }
}
