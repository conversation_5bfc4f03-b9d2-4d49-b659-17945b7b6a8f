package com.uinnova.product.vmdb.comm.expression.support;

import com.uinnova.product.vmdb.comm.expression.Expression;
import com.uinnova.product.vmdb.comm.expression.FieldDefinition;

/**
 * 
 * <AUTHOR>
 *
 */
public class VarcharField<E> extends AbstractField<E> {
    private static final long serialVersionUID = 1L;

    public VarcharField(FieldDefinition<E> fieldDefinition) {
        super(fieldDefinition);
    }

    @Override
    public Expression<E> EQ(String value) {
        return FieldOperator.operator.equal(this, value);
    }

    @Override
    public Expression<E> NEQ(String value) {
        return FieldOperator.operator.notEqual(this, value);
    }

    @Override
    public Expression<E> LT(String value) {
        return FieldOperator.operator.less(this, value);
    }

    @Override
    public Expression<E> LTEQ(String value) {
        return FieldOperator.operator.lessEqual(this, value);
    }

    @Override
    public Expression<E> GT(String value) {
        return FieldOperator.operator.greater(this, value);
    }

    @Override
    public Expression<E> GTEQ(String value) {
        return FieldOperator.operator.greaterEqual(this, value);
    }

    @Override
    public Expression<E> LIKE(String value) {
        return FieldOperator.operator.like(this, value);
    }

    @Override
    public Expression<E> INSTR(String value) {
        return FieldOperator.operator.instr(this, value);
    }

    @Override
    public Expression<E> IN(String[] values) {
        return FieldOperator.operator.in(this, values);
    }

    @Override
    public Expression<E> NIN(String[] values) {
        return FieldOperator.operator.notIn(this, values);
    }

}
