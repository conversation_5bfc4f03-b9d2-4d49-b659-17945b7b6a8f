package com.uino.bean.express;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

@ApiModel(value="表达式计算结果",description = "表达式计算结果")
@Data
@Builder
public class ExpressExecuteResult {

    @ApiModelProperty("是否计算成功")
    Boolean success;

    @ApiModelProperty("计算结果")
    Object value;

    @ApiModelProperty("失败原因")
    String failureReason;

}
