package com.uinnova.product.eam.service;

import com.uinnova.product.eam.model.dto.EamHierarchyDto;
import com.uinnova.product.eam.model.dto.EamHierarchyNavigationDto;
import com.uino.bean.cmdb.base.LibType;

import java.util.List;

/**
 * 模型导航栏层级接口
 */
public interface IBmNavigationHierarchySvc {

    List<EamHierarchyNavigationDto> getNavigationList(List<EamHierarchyDto> hierarchyDtoList, String ownerCode);

    /**
     *  修改层级状态
     * @param hierarchyId
     * @param modelId
     * @param state
     * @param ownerCode
     * @return
     */
    Boolean changeHierarchyStatus(Long hierarchyId, Long modelId, Integer state, String ownerCode, LibType libType);

    /**
     *  删除层级导航
     * @param modelId
     * @param ownerCode
     * @param libType
     * @return
     */
    Boolean deleteHierarchy(Long modelId, String ownerCode, LibType libType);

}
