package com.binary.core.i18n;

import com.binary.core.exception.CoreException;

public class I18nException extends CoreException {
	private static final long serialVersionUID = 1L;

	
	
	public I18nException() {
		super();
	}
	
	public I18nException(String message) {
		super(message);
	}
	
	public I18nException(Throwable cause) {
		super(cause);
	}
	
	public I18nException(String message, Throwable cause) {
		super(message, cause);
	}

}
