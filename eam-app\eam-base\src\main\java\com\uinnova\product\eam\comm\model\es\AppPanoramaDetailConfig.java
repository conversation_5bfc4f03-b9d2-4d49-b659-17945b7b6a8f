package com.uinnova.product.eam.comm.model.es;

import com.alibaba.fastjson.JSONObject;
import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 应用全景详情配置
 * <AUTHOR>
 */
@Data
public class AppPanoramaDetailConfig {

    @Comment("主键id")
    private Long id;
    @Comment("广场资产配置ID")
    @NotNull(message = "广场卡片ID不可为空")
    private Long appSquareConfId;
    @Comment("资产分类标识")
    @NotNull(message = "资产分类不可为空")
    private String classCode;
    @Comment("资产图标")
    private Boolean AssetIcon;
//    @Comment("字段一")
//    private String attrFirst;
//    @Comment("字段一")
//    private String attrSecond;
//    @Comment("字段一")
//    private String attrThirdly;
//    @Comment("字段一")
//    private String attrFourthly;
    @Comment("字段信息")
    private List<JSONObject> introAttrs;
    @Comment("标签")
    private List<JSONObject> tagAttrs;
    @Comment("展示信息")
    private Long detailAttrConfId;

    @Comment("领域id")
    private Long domainId;
    @Comment("创建人")
    private String creator;
    @Comment("修改人")
    private String modifier;
    @Comment("创建时间")
    private Long createTime;
    @Comment("修改时间")
    private Long modifyTime;
}
