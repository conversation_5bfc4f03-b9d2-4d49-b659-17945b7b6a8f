package com.uinnova.product.eam.service.diagram;


import com.uinnova.product.eam.base.diagram.model.ESDiagramNode;
import com.uinnova.product.eam.base.diagram.model.ESResponseStruct;

import java.util.Collection;
import java.util.List;

/**
 * 视图节点接口
 * <AUTHOR>
 */
public interface ESDiagramNodeSvc {

    /**
     * 获取视图node节点
     * @param id 节点id
     * @return 视图node节点
     */
    List<ESDiagramNode> getNodeByIds(List<Long> id);
    /**
     * 获取视图node节点
     * @param diagramIds 视图id集合
     * @param sheetIds sheetId集合（可空）
     * @return 视图node节点
     */
    List<ESDiagramNode> getNodeByDiagram(Collection<Long> diagramIds, Collection<String> sheetIds);

    /**
     * 批量保存或更新视图节点
     * @param nodes 视图节点
     * @return 结果
     */
    Integer saveOrUpdateBatch(List<ESDiagramNode> nodes);

    /**
     * 批量通过视图id删除视图节点
     * @param diagramIds 视图id
     */
    void deleteByDiagramIds(Collection<Long> diagramIds);

    /**
     * 新增或更新node, flag=true标识是更新操作，反之为新增
     * @param diagramId 视图id
     * @param sheetId 分页id
     * @param opList 数据集
     * @param flag 是否是更新
     * @return 结果集
     */
    List<ESResponseStruct> saveOrUpdateNode(Long diagramId, String sheetId, String opList, Boolean flag);
}
