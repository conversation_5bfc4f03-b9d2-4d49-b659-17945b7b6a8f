package com.uinnova.product.eam.model.vo;

import com.uino.bean.cmdb.base.ESCIInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class EntityAndAttributeVo {


    @ApiModelProperty(value="ci实体信息")
    private ESCIInfo entityCi;

    @ApiModelProperty(value="ci属性信息")
    private List<ESCIInfo> attrCiList;

    public EntityAndAttributeVo() {
    }

    public EntityAndAttributeVo(ESCIInfo entityCi, List<ESCIInfo> attrCiList) {
        this.entityCi = entityCi;
        this.attrCiList = attrCiList;
    }
}
