package com.uinnova.product.eam.service.es;

import com.uinnova.product.eam.comm.bean.Standard;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Repository;

import jakarta.annotation.PostConstruct;

/**
 * 标准规范操作es的数据接口层
 *
 * <AUTHOR>
 * @version 2020-9-23
 */
@Deprecated
@Repository
public class IamsEsStandardSvc extends AbstractESBaseDao<Standard, Standard> {
    @Override
    public String getIndex() {
        return "uino_eam_standard";
    }

    @Override
    public String getType() {
        return "uino_eam_standard";
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }
}
