package com.uinnova.product.eam.base.exception;

public class EamException extends RuntimeException {

    public static final long serialVersionUID = 1;

    public EamException() {
        super();
    }

    public EamException(String message) {
        super(message);
    }

    public EamException(String message, Throwable cause) {
        super(message, cause);
    }

    public EamException(Throwable cause) {
        super(cause);
    }
}
