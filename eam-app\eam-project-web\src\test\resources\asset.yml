uinnova:
  app:
    #业务能力 人员矩阵查询的ci分类名称
    className: 应用系统
    #应用访问应用的关系分类名称
    relClassName: 应用访问
    #业务架构查询的ci分类名称
    bizClassName: EBPM-业务能力
  dict:
    className: 数据字典
  asset:
    #标准规范信息
    specification:
      className: 标准规范
      pageConfig: "{\"specialMeta\":{\"selectableAttrs\": [\"领域\",\"类型\",\"子类\"],\"dictType\": {\"领域\": {\"codeType\":\"DOMAIN\",\"parentAttr\":\"\"},\"类型\": {\"codeType\":\"DOC_TYPE\",\"parentAttr\":\"领域\"},\"子类\": {\"codeType\":\"DOC_CHILDREN_TYPE\",\"parentAttr\":\"类型\"}},\"classCode\":\"标准规范\",\"hiddenAttrs\":[\"PUBLISH_TIME\",\"TABLE_NAME\",\"EDITIONS\",\"最后编辑时间\",\"IAMEMPLOGINNO\"],\"callbackType\":{}},\"editionMeta\":{\"selectableAttrs\":[\"负责人\",\"负责部门\"],\"classCode\":\"标准规范-版本\",\"hiddenAttrs\":[\"负责人ID\",\"负责部门ID\",\"DOCS\",\"标准规范名称\",\"TABLE_NAME\"],\"callbackType\":{\"负责人\":\"USER\",\"负责部门\":\"ORG\"}}}"
      edition:
        className: 标准规范-版本
    #所属系统信息
    appSystem:
      className: 系统
    #子系统信息
    appSubSystem:
      className: 子系统
    #架构决议信息
    archResolution:
      className: 架构决议
      domainId: 1
      DOC: DOC
      subsystemCode: subsystemCode
      ciCode: ciCode
      id: id
      ResolutionName: 决议名称
      ResolutionTime: 决议时间
      ReviewMethod: 审议方式

      pageConfig: "{\"selectableAttrs\": [\"审议方式\"],\"classCode\": \"架构决议\",\"hiddenAttrs\": [\"SUBSYSTEMCODE\",\"主键\",\"DOC\"]}"
    #开源软件目录信息
    freeSoftware:
      className: 开源软件
      pageConfig: "{\"freeSoftware\":{\"selectableAttrs\":[\"领域\",\"子领域\",\"开源维护组织\",\"开源软件协议类型\",\"服务供应商\"],\"dictType\":{\"领域\":{\"codeType\":\"SW_DOMAIN\",\"parentAttr\":\"\"},\"子领域\":{\"codeType\":\"SW_SUBDOMAIN\",\"parentAttr\":\"领域\"},\"开源维护组织\":{\"codeType\":\"SW_ORG\",\"parentAttr\":\"\"},\"开源软件协议类型\":{\"codeType\":\"SW_ORGTYPE\",\"parentAttr\":\"开源维护组织\"},\"所适用的系统重要性等级\":{\"codeType\":\"SW_LVL\",\"parentAttr\":\"\"},\"CPU支持架构\":{\"codeType\":\"CPU_TYPE\",\"parentAttr\":\"\"},\"服务供应商\":{\"codeType\":\"SW_FACTORY\",\"parentAttr\":\"\"}},\"classCode\":\"开源软件\",\"hiddenAttrs\":[\"最后编辑时间\",\"IAMEMPLOGINNO\",\"文件ID\",\"流程状态\",\"流程ID\",\"附件ID\",\"EDITION_INFO\"],\"callbackType\":{\"责任人\":\"USER\",\"责任部门\":\"ORG\"},\"subScenarioAttrs\":[\"所适用的系统重要性等级\"],\"radioAttrs\":[\"二次开发\",\"网络开通\",\"密码存储\"],\"checkBoxAttrs\":[\"所适用的系统重要性等级\",\"CPU支持架构\"]},\"freeSoftwareEdition\":{\"classCode\":\"开源软件版本\",\"hiddenAttrs\":[\"DB_ID\",\"软件名称\",\"SORT_ID\",\"OS_ID\",\"SOFT_ID\"],\"selectableAttrs\":[\"生命周期阶段\"],\"dictType\":{\"生命周期阶段\":{\"codeType\":\"SW_LIFE\",\"parentAttr\":\"\"}}},\"fieldAttrs\":{\"基础信息\":[\"软件名称\",\"软件描述\",\"适用场景\",\"所适用的系统重要性等级\",\"领域\",\"子领域\",\"子领域描述\",\"社区成熟度和活跃度\",\"官方网站\",\"使用痛点\",\"改善目标\",\"可选服务供应商\",\"备注\"],\"版本信息\":[\"软件版本\",\"生命周期阶段\",\"推荐补丁\",\"最新补丁\",\"发布日期\",\"终止日期\",\"版本备注\",\"近期安全漏洞情况\",\"版本下载地址\"],\"其他信息\":[\"引入必要性\",\"同类开源软件对比\",\"二次开发\",\"二次开发内容描述\",\"网络开通\",\"网络开通信息描述\",\"密码存储\",\"密码加密方式\",\"依赖其他开源软件情况\",\"附件\"],\"技术支持\":[\"开源维护组织\",\"开源软件协议类型\",\"CPU支持架构\",\"责任部门\",\"责任人\",\"修订日期\",\"使用指引\",\"服务供应商\",\"联系方式\",\"申请试用项目名称\"]}}"
      edition:
        className: 开源软件版本
    #外购软件目录信息
    paySoftware:
      className: 外购软件
      pageConfig: "{\"paySoftware\":{\"selectableAttrs\":[\"领域\",\"子领域\",\"服务供应商\",\"产品可使用范围\"],\"dictType\":{\"领域\":{\"codeType\":\"SW_DOMAIN\",\"parentAttr\":\"\"},\"子领域\":{\"codeType\":\"SW_SUBDOMAIN\",\"parentAttr\":\"领域\"},\"服务供应商\":{\"codeType\":\"SW_FACTORY\",\"parentAttr\":\"\"},\"所适用的系统重要性等级\":{\"codeType\":\"SW_LVL\",\"parentAttr\":\"\"},\"CPU支持架构\":{\"codeType\":\"CPU_TYPE\",\"parentAttr\":\"\"},\"产品可使用范围\":{\"codeType\":\"SW_SCOPE\",\"parentAttr\":\"\"}},\"classCode\":\"外购软件\",\"hiddenAttrs\":[\"最后编辑时间\",\"IAMEMPLOGINNO\",\"文件ID\",\"EDITION_INFO\"],\"callbackType\":{\"责任人\":\"USER\",\"责任部门\":\"ORG\"},\"subScenarioAttrs\":[\"所适用的系统重要性等级\"],\"radioAttrs\":[],\"checkBoxAttrs\":[\"所适用的系统重要性等级\",\"CPU支持架构\"]},\"paySoftwareEdition\":{\"classCode\":\"外购软件版本\",\"hiddenAttrs\":[\"DB_ID\",\"软件名称\",\"SORT_ID\",\"OS_ID\",\"SOFT_ID\"],\"selectableAttrs\":[\"生命周期阶段\"],\"dictType\":{\"生命周期阶段\":{\"codeType\":\"SW_LIFE\",\"parentAttr\":\"\"}}},\"fieldAttrs\":{\"基础信息\":[\"软件名称\",\"软件描述\",\"适用场景\",\"所适用的系统重要性等级\",\"领域\",\"子领域\",\"子领域描述\",\"使用痛点\",\"改善目标\",\"备注\"],\"版本信息\": [\"软件版本\",\"生命周期阶段\", \"推荐补丁\", \"最新补丁\",\"发布日期\",\"终止日期\", \"版本备注\"],\"技术支持\":[\"服务供应商\",\"CPU支持架构\",\"产品可使用范围\",\"全行许可授权总数\",\"责任部门\",\"责任人\",\"修订日期\",\"使用指引\"]}}"
      edition:
        className: 外购软件版本
    #非功能指标信息
    noFunctionalItem:
      className: 非功能指标
      indicatorName: 指标名称
      indicatorClassName: 类名称
      subdivisionName: 细类名称
      iamEmpLoginNo: iamEmpLoginNo
      lastEditTime: 最后编辑时间
      dataDictionary: 数据字典
      domainId: 1
      pageConfig: "{\"nonFunctionalMeta\": {\"selectableAttrs\": [\"类名称\",\"细类名称\",\"负责人\",\"填写部门\"],\"dictType\": {\"类名称\": {\"codeType\": \"NONFUNCTION\", \"parentAttr\": \"\"},\"细类名称\": {\"codeType\": \"INDICATORS\",\"parentAttr\": \"类名称\"}},\"classCode\": \"非功能指标\",\"hiddenAttrs\": [\"负责人ID\",\"填写部门ID\",\"最后编辑时间\",\"IAMEMPLOGINNO\",\"数据字典\"],\"callbackType\": {\"负责人\": \"USER\",\"填写部门\": \"ORG\"}}}"
      searchAttrs: "{\"searchAttrs\":[\"指标名称\", \"指标描述\", \"指标值定义\", \"指标类型\", \"填写阶段\", \"负责人\", \"负责部门\",\"数据字典\"]}"
    noFunctionalMsg:
      className: 非功能信息
    # 应用系统信息
    HtAppSystem:
      classCode: DTEA.AA.应用系统
      queryConditions: [编号, 应用系统名称, 应用系统简称, 应用系统英文名称, 应用系统标识]
      appSystemConfiguration: APP_SYSTEM_CONFIG
      initAppSystemConfig: '{"loc1":"编号","loc2":"应用系统名称","loc3":"应用系统英文名称","loc4":"系统分类","loc5":"系统建设分类","loc6":"应用系统简介","loc7":["应用系统负责人","审批通过时间"]}'
  dataModel:
    conceptionEntity:
      name: 概念实体
      code: ConceptionEntity
    logicEntity:
      name: 逻辑实体
      code: LogicEntity
    attributes:
      name: 实体属性
      code: Attributes
    dataType:
      name: 数据类型
      code: DataType
    standard:
      name: 数据标准
      code: Standard
    standardClass:
      name: 数据标准分类
      code: StandardClass
    physicalEntity:
      name: 物理实体
      code: PhysicalEntity
    domain:
      name: 值域
      code: Domain
    domainClass:
      name: 域组
      code: DomainClass

