package com.uinnova.product.eam.service;

import com.uinnova.product.eam.comm.model.es.EamCategory;
import com.uinnova.product.eam.comm.model.es.FolderPermissionManager;
import com.uinnova.product.eam.model.dto.FolderPermissionManagerVo;

import java.util.List;
import java.util.Set;

/**
 * description
 *
 * <AUTHOR>
 * @since 2022/6/30 10:36
 */
public interface IFolderPermissionManagerService {


    /**
     * 批量保存文件夹权限数据
     * @param folderPermissionManagerVo
     */
    void saveFolderPermissions(FolderPermissionManagerVo folderPermissionManagerVo);

    void saveFolderPermissions(List<FolderPermissionManager> folderPermissionManagers);

    /**
     * 根据文件夹id查询权限列表信息
     * @param dirId
     * @return
     */
    List<FolderPermissionManager> getFolderPermissionsByDirId(Long dirId);

    /**
     * 获取需要继承的权限
     * @param dirId
     * @return
     */
    List<FolderPermissionManager> getNeedExtendFolderPermissionsByDirId(Long dirId);

    /**
     * 更新权限
     * @param folderPermissionManager
     */
    void updateFolderPermissionManager(FolderPermissionManager folderPermissionManager);

    /**
     * 删除文件夹权限会将后台文件夹集成的权限全部删除（不包括自定义权限）
     * @param id
     */
    void deleteFolderPermissions(Long id);

    /**
     * 删除某个文件夹权限包括所有子文件夹权限
     */
    void deleteFolderPermissionsIncludeChildByDirId(Long dirId);

    /**
     * 查询有查看权限的文件夹
     * @param loginCode
     * @param dirIds
     * @return
     */
    List<FolderPermissionManager> getFolderPermissionsByLoginCodeAndDirId(String loginCode, List<Long> dirIds);

    /**
     * 根据用户id和文件夹类型查询文件夹数据
     * @param loginCode
     * @param dirType
     * @return
     */
    List<FolderPermissionManager> getFolderPermissionsByDirTypeAndUser(String loginCode, int dirType);

    /**
     * 移动文件夹需要重置文件夹权限，原后代文件夹权限会全部重置
     * @param dirIds 要移动的文件夹id
     * @param parentDirId 要移动至文件夹的id
     */
    void moveDirChangeFolderPermissions(List<Long> dirIds,Long parentDirId);

    List<FolderPermissionManager> getFolderPermissionByRoleIds(Set<Long> roleIds);

    List<FolderPermissionManager> getFolderPermissionByDirIds(List<Long> dirIds);

    void setFolderPermissionIfNeed(EamCategory category, FolderPermissionManager folderPermissionManager);

    List<FolderPermissionManager> queryEmptyModelPermission();

    void deleteFolderPermissionsByDirIds(List<Long> dirIds);

    void deleteFolderPermissionsByIds(Set<Long> ids);
}
