package com.uinnova.product.vmdb.provider.kpi.bean;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.vmdb.comm.model.ci.CcCiClass;
import com.uinnova.product.vmdb.comm.model.kpi.CcKpi;
import com.uinnova.product.vmdb.comm.model.kpi.CcKpiCiGroup;
import com.uinnova.product.vmdb.comm.model.kpi.CcKpiClass;
import com.uinnova.product.vmdb.comm.model.rule.CcCiTagDef;

import java.io.Serializable;
import java.util.List;


public class CcKpiInfo implements Serializable {
	private static final long serialVersionUID = 1L;
	
	
	@Comment("KPI")
	private CcKpi kpi;

	@Comment("当前KPI对应分类")
	private CcKpiClass kpiClass;
	
	@Comment("KPI关联CI")
	private List<CcKpiCiGroup> kpiCiGroups;
	
	@Comment("分类对象分组信息")
	private List<CcCiClass> ciClasses;
	
	@Comment("标签对象分组信息")
	private List<CcCiTagDef> ciTagDefs;
	
	@Comment("关系对象分组信息")
	private List<CcCiClass> rltClasses;

	public CcKpi getKpi() {
		return kpi;
	}

	public void setKpi(CcKpi kpi) {
		this.kpi = kpi;
	}

	public CcKpiClass getKpiClass() {
		return kpiClass;
	}

	public void setKpiClass(CcKpiClass kpiClass) {
		this.kpiClass = kpiClass;
	}

	public List<CcKpiCiGroup> getKpiCiGroups() {
		return kpiCiGroups;
	}

	public void setKpiCiGroups(List<CcKpiCiGroup> kpiCiGroups) {
		this.kpiCiGroups = kpiCiGroups;
	}

	public List<CcCiClass> getCiClasses() {
		return ciClasses;
	}

	public void setCiClasses(List<CcCiClass> ciClasses) {
		this.ciClasses = ciClasses;
	}

	public List<CcCiTagDef> getCiTagDefs() {
		return ciTagDefs;
	}

	public void setCiTagDefs(List<CcCiTagDef> ciTagDefs) {
		this.ciTagDefs = ciTagDefs;
	}

	public List<CcCiClass> getRltClasses() {
		return rltClasses;
	}

	public void setRltClasses(List<CcCiClass> rltClasses) {
		this.rltClasses = rltClasses;
	}
	
 
	
	
	

}
