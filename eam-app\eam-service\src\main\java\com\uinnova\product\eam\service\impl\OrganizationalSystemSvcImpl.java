package com.uinnova.product.eam.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.shaded.com.google.gson.Gson;
import com.binary.core.exception.MessageException;
import com.binary.jdbc.Page;
import com.google.common.collect.Lists;
import com.uinnova.product.eam.model.DepartmentDto;
import com.uinnova.product.eam.model.ObjectMovingDto;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.uinnova.product.eam.model.vo.OrganizationalSystemParamVo;
import com.uinnova.product.eam.model.vo.OrganizationalSystemVo;
import com.uinnova.product.eam.service.ICIRltSwitchSvc;
import com.uinnova.product.eam.service.ICISwitchSvc;
import com.uinnova.product.eam.service.OrganizationalSystemSvc;
import com.uinnova.product.eam.service.asset.BmConfigSvc;
import com.uinnova.product.eam.service.es.IamsESCmdbCommDesignSvc;
import com.uinnova.product.eam.service.exception.BusinessException;
import com.uinnova.product.vmdb.comm.model.ci.CcCi;
import com.uinnova.product.vmdb.comm.model.ci.CcCiClass;
import com.uinnova.product.vmdb.comm.util.CommUtil;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uinnova.product.vmdb.provider.rlt.bean.CcCiRltInfo;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.base.LibType;
import com.uino.bean.cmdb.business.ImportExcelMessage;
import com.uino.bean.cmdb.query.ESAttrBean;
import com.uino.bean.cmdb.query.ESCISearchBean;
import com.uino.bean.cmdb.query.ESRltSearchBean;
import com.uino.bean.permission.base.SysOrg;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.permission.query.CSysOrg;
import com.uino.dao.util.ESUtil;
import com.uino.service.cmdb.microservice.ICIClassSvc;
import com.uino.service.cmdb.microservice.ICISvc;
import com.uino.service.cmdb.microservice.IRltClassSvc;
import com.uino.service.permission.microservice.impl.OrgSvc;
import com.uino.service.util.FileUtil;
import com.uino.util.excel.EasyExcelUtil;
import com.uino.util.rsm.RsmUtils;
import com.uino.util.sys.SysUtil;
import joptsimple.internal.Strings;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigInteger;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 组织体系
 *
 * <AUTHOR>
 * @since 2024/7/9 上午10:11
 */
@Slf4j
@Service
public class OrganizationalSystemSvcImpl implements OrganizationalSystemSvc {

    private static final String ORGANIZATIONAL_POSITION = "ORGANIZATIONAL_POSITION";

    @Resource
    private BmConfigSvc bmConfigSvc;

    @Resource
    private ICIClassSvc iciClassSvc;

    @Resource
    private ICISwitchSvc iciSwitchSvc;

    @Resource
    private ICIRltSwitchSvc iciRltSwitchSvc;

    @Autowired
    private OrgSvc orgSvc;


    @Resource
    private IRltClassSvc iRltClassSvc;

    @Autowired
    @Lazy
    IamsESCmdbCommDesignSvc commSvc;

    @Autowired
    private RsmUtils rsmUtils;
    @Value("${local.resource.space}")
    private String localPath;

    @Value("${http.resource.space}")
    private String httpPath;

    @Override
    public String getPositionName() {
        return bmConfigSvc.getConfigType(ORGANIZATIONAL_POSITION);
    }

    @Override
    public Boolean saveOrUpdate(OrganizationalSystemVo organizationalSystemVo) {
        if (organizationalSystemVo == null) {
            throw new BusinessException("参数不能为空!");
        }
        if (StringUtils.isEmpty(organizationalSystemVo.getNumber())) {
            throw new BusinessException("岗位编号不能为空!");
        }
        if (StringUtils.isEmpty(organizationalSystemVo.getName())) {
            throw new BusinessException("岗位名称不能为空!");
        }
        CcCiInfo ciInfo = null;
        if (!StringUtils.isEmpty(organizationalSystemVo.getCiCode())) {
            ciInfo = iciSwitchSvc.getCiByCode(organizationalSystemVo.getCiCode(), null, LibType.DESIGN);
        }
        CcCiClassInfo ciClass = iciClassSvc.getCiClassByClassCode("岗位");
        if (ciClass == null) {
            throw new BusinessException("未获取到组织体系关联岗位信息!");
        }
        if (ciInfo == null) {
            Map<String, String> attrs = new HashMap<>();
            attrs.put("岗位编号", organizationalSystemVo.getNumber());
            attrs.put("岗位名称", organizationalSystemVo.getName());
            attrs.put("岗位描述", organizationalSystemVo.getRemark());
            attrs.put("岗位类别", organizationalSystemVo.getCategory());
            attrs.put("所属部门", organizationalSystemVo.getDepartCiCode());

            CcCi ci = new CcCi();
            ci.setClassId(ciClass.getCiClass().getId());
            CcCiInfo ccCi = new CcCiInfo();
            ccCi.setAttrs(attrs);
            ccCi.setCi(ci);
            iciSwitchSvc.saveOrUpdateCI(ccCi, LibType.DESIGN);
        } else {
            Map<String, String> attrs = ciInfo.getAttrs();
            attrs.put("岗位编号", organizationalSystemVo.getNumber());
            attrs.put("岗位名称", organizationalSystemVo.getName());
            attrs.put("岗位描述", organizationalSystemVo.getRemark());
            attrs.put("岗位类别", organizationalSystemVo.getCategory());
            attrs.put("所属部门", organizationalSystemVo.getDepartCiCode());

            CcCi ci = ciInfo.getCi();
            CcCiInfo ccCi = new CcCiInfo();
            ccCi.setAttrs(attrs);
            ccCi.setCi(ci);
            iciSwitchSvc.saveOrUpdateCI(ccCi, LibType.DESIGN);
        }
        return true;
    }

    @Override
    public Boolean remove(String ciCode) {
        if (StringUtils.isEmpty(ciCode)) {
            throw new BusinessException("参数不能为空!");
        }
        CcCiInfo ciInfo = iciSwitchSvc.getCiByCode(ciCode, null, LibType.DESIGN);
        if (ciInfo == null) {
            return true;
        }
        if (ciInfo.getCi() != null && ciInfo.getCi().getId() != null) {
            iciSwitchSvc.removeById(ciInfo.getCi().getId(), null, LibType.DESIGN);
        }
        return true;
    }

    @Override
    public Page<ESCIInfo> findOrganizationalSystemList(OrganizationalSystemParamVo paramVo) {
        if (paramVo == null) {
            throw new BusinessException("参数不能为空!");
        }
        if (StringUtils.isEmpty(paramVo.getOrgId())) {
            throw new BusinessException("部门信息不能为空!");
        }

        CcCiClassInfo ciClass = iciClassSvc.getCiClassByClassCode("岗位");
        if (ciClass == null) {
            throw new BusinessException("未获取到岗位信息!");
        }

        // 查询当前组织和下级组织
        SysUser sysUser = SysUtil.getCurrentUserInfo();
        Set<Long> orgIds = new HashSet<>();
        CSysOrg cSysOrgCdt = new CSysOrg();
        cSysOrgCdt.setDomainId(sysUser.getDomainId());
        List<SysOrg> orgs = orgSvc.queryPageByCdt(1, 3000, cSysOrgCdt).getData();
        Map<Long, List<SysOrg>> orgIdChildsMap = new LinkedHashMap<>();
        if (orgs != null) {
            for (SysOrg org : orgs) {
                orgIdChildsMap.put(org.getParentOrgId(), orgIdChildsMap.get(org.getParentOrgId()) == null ? new ArrayList<>() : orgIdChildsMap.get(org.getParentOrgId()));
                orgIdChildsMap.get(org.getParentOrgId()).add(org);
            }
        }
        getAllChilds(orgIdChildsMap, orgIds, Long.valueOf(paramVo.getOrgId()));
        orgIds.add(Long.valueOf(paramVo.getOrgId()));

        ESCISearchBean esciSearchBean = new ESCISearchBean();
        if(StringUtils.isNoneBlank(paramVo.getWord())){
            esciSearchBean.setWords(Collections.singletonList(paramVo.getWord()));
        }
        esciSearchBean.setPageNum(paramVo.getPageNum());
        esciSearchBean.setPageSize(paramVo.getPageSize());
        esciSearchBean.setClassIds(Collections.singletonList(ciClass.getCiClass().getId()));
        List<ESAttrBean> orAttrs = new ArrayList<>();
        for (Long orgId : orgIds) {
            ESAttrBean esAttrBean = new ESAttrBean();
            esAttrBean.setKey("所属部门");
            esAttrBean.setValue(orgId);
            esAttrBean.setOptType(1);
            orAttrs.add(esAttrBean);
        }
        esciSearchBean.setOrAttrs(orAttrs);
        ICISvc ciSvc = iciSwitchSvc.getCiSvc(LibType.DESIGN);
        Page<ESCIInfo> esciInfoPage = ciSvc.searchESCIByBean(esciSearchBean);
        if (esciInfoPage != null && !CollectionUtils.isEmpty(esciInfoPage.getData())) {
            List<ESCIInfo> ciList = esciInfoPage.getData();
            Set<String> ciCodeSet = new HashSet<>();
            Set<Long> orgSet = new HashSet<>();
            for (ESCIInfo info : ciList) {
                if (!StringUtils.isEmpty(info.getCiCode())) {
                    ciCodeSet.add(info.getCiCode());
                }
                Map<String, Object> attrs = info.getAttrs();
                if (!StringUtils.isEmpty(String.valueOf(attrs.get("所属部门")))) {
                    orgSet.add(Long.valueOf(String.valueOf(attrs.get("所属部门"))));
                }
            }
            CcCiClassInfo rltClass = iciRltSwitchSvc.getRltClassByCode("拥有");
            Map<String, Set<String>> roleMap = new HashMap<>();
            if (rltClass != null) {
                ESRltSearchBean rltSearchBean = new ESRltSearchBean();
                rltSearchBean.setRltClassIds(Lists.newArrayList(rltClass.getCiClass().getId()));
                rltSearchBean.setSourceCiCodes(ciCodeSet);
                List<CcCiRltInfo> ccCiRltInfoList = iciRltSwitchSvc.searchRltByScroll(rltSearchBean, LibType.DESIGN);
                if (!CollectionUtils.isEmpty(ccCiRltInfoList)) {
                    for (CcCiRltInfo rltInfo : ccCiRltInfoList) {
                        CcCiInfo sourceCiInfo = rltInfo.getSourceCiInfo();
                        CcCiInfo targetCiInfo = rltInfo.getTargetCiInfo();
                        CcCiClass roleClass = targetCiInfo.getCiClass();
                        if (Objects.equals(roleClass.getClassCode(), "岗位角色")) {
                            Map<String, String> attrs = targetCiInfo.getAttrs();
                            String name = attrs.get("角色名称");
                            roleMap.computeIfAbsent(sourceCiInfo.getCi().getCiCode(), k -> new HashSet<>()).add(name);
                        }
                    }
                }
            }
            Map<Long, String> orgMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(orgSet)) {
                CSysOrg query = new CSysOrg();
                query.setIds(orgSet.toArray(new Long[0]));
                List<SysOrg> sysOrgs = orgSvc.queryListByCdt(query);
                if (!CollectionUtils.isEmpty(sysOrgs)) {
                    orgMap = sysOrgs.stream().collect(Collectors.toMap(item -> item.getId(), item -> item.getOrgName()));
                }
            }
            for (ESCIInfo ciInfo : ciList) {
                Set<String> roleSet = roleMap.get(ciInfo.getCiCode());
                Map<String, Object> attrs = ciInfo.getAttrs();
                if (!CollectionUtils.isEmpty(roleSet)) {
                    attrs.put("关联角色", Strings.join(roleSet, "，"));
                }
                Long orgId = Long.valueOf(String.valueOf(attrs.get("所属部门")));
                String orgName = orgMap.get(orgId);
                if (!StringUtils.isEmpty(orgName)) {
                    attrs.put("所属部门名称", orgName);
                }
            }
        }
        return esciInfoPage;
    }

    @Override
    public Boolean movePosition(ObjectMovingDto objectMovingDto) {
        //岗位
        CcCiInfo ciByCode = iciSwitchSvc.getCiByCode(objectMovingDto.getCiCode(), SysUtil.getCurrentUserInfo().getLoginCode(), LibType.DESIGN);
        //部门
        CcCiInfo ccCiInfo1 = iciSwitchSvc.getCiByCode(objectMovingDto.getTargetCiCode(), SysUtil.getCurrentUserInfo().getLoginCode(), LibType.DESIGN);
        if (ccCiInfo1==null){
            throw new BusinessException("目标部门已被删除，请重新选择！");
        }
        if (ciByCode != null){
            String s = ciByCode.getAttrs().get("所属部门");
            if (StringUtils.isNotBlank(s)&& !s.contains(ccCiInfo1.getCi().getCiCode())){
                Map<String, String> attrs = ciByCode.getAttrs();
                HashMap<String, String> ciInfo = new HashMap<>();
                ciInfo.put("ciCode", ccCiInfo1.getCi().getCiCode());
                String ciLabel = ccCiInfo1.getCi().getCiLabel();
                if (StringUtils.isEmpty(ciLabel)){
                    String ciPrimaryKey = ccCiInfo1.getCi().getCiPrimaryKey();
                    List<String> ciPrimaryList = JSONObject.parseArray(ciPrimaryKey, String.class);
                    ciPrimaryList.remove(0);
                    ciInfo.put("primary", ciPrimaryList.stream().collect(Collectors.joining("|")));
                }else{
                    List<String> ciPrimaryList = JSONObject.parseArray(ciLabel, String.class);
                    ciInfo.put("primary", ciPrimaryList.stream().collect(Collectors.joining("|")));
                }
                List<Map<String, String>> list = new ArrayList<>();
                list.add(ciInfo);

                // 使用 Gson 将列表转换为 JSON 字符串
                Gson gson = new Gson();
                String json = gson.toJson(list);
                attrs.put("所属部门",json);
                ciByCode.setAttrs(attrs);
                iciSwitchSvc.saveOrUpdateCI(ciByCode,LibType.DESIGN);
            }else{
                throw new BusinessException("该岗位已属于该部门，无需再移动！");
            }
        }else {
            throw new BusinessException("未找到该条数据，请重新选择！");
        }
        return true;
    }

    @Override
    public List<Map<String, String>> getDetail(String ciCode) {
        List<Map<String, String>> detailList = new ArrayList<>();

        // 提前获取必要的类信息
        CcCiClassInfo linkClass = iRltClassSvc.getRltClassByName(1L, "拥有");
        CcCiClassInfo activeClass = iciClassSvc.getCiClassByClassCode("岗位角色");
        CcCiClassInfo activities = iciClassSvc.getCiClassByClassCode("活动");
        CcCiClassInfo classInfo = iciClassSvc.getCiClassByClassCode("流程");
        CcCiClassInfo executeLinkClass = iRltClassSvc.getRltClassByName(1L, "由...执行");
        CcCiClassInfo containLinkClass = iRltClassSvc.getRltClassByName(1L, "包含");

        ESRltSearchBean esRltSearchBean = new ESRltSearchBean();
        // 源端是岗位
        esRltSearchBean.setSourceCiCodes(Collections.singleton(ciCode));
        // 关联关系
        esRltSearchBean.setRltClassIds(Collections.singletonList(linkClass.getCiClass().getId()));
        // 目标端是角色
        esRltSearchBean.setTargetClassIds(Collections.singletonList(activeClass.getCiClass().getId()));
        List<CcCiRltInfo> privateCcCiRltInfos = iciRltSwitchSvc.searchRltByScroll(esRltSearchBean, LibType.DESIGN);

        for (CcCiRltInfo privateCcCiRltInfo : privateCcCiRltInfos) {
            String roleCiCode = privateCcCiRltInfo.getTargetCiInfo().getCi().getCiCode();
            String associatedRole = privateCcCiRltInfo.getTargetCiInfo().getAttrs().get("角色名称");

            ESRltSearchBean esRltSearchBean1 = new ESRltSearchBean();
            // 目标端是角色
            esRltSearchBean1.setTargetCiCodes(Collections.singleton(roleCiCode));
            // 关联关系
            esRltSearchBean1.setRltClassIds(Collections.singletonList(executeLinkClass.getCiClass().getId()));
            // 源端是活动
            esRltSearchBean1.setSourceClassIds(Collections.singletonList(activities.getCiClass().getId()));
            List<CcCiRltInfo> privateCcCiRltInfos1 = iciRltSwitchSvc.searchRltByScroll(esRltSearchBean1, LibType.DESIGN);

            if (!CollectionUtils.isEmpty(privateCcCiRltInfos1)) {
                for (CcCiRltInfo privateCcCiRltInfo1 : privateCcCiRltInfos1) {
                    String activityCiCode = privateCcCiRltInfo1.getSourceCiInfo().getCi().getCiCode();
                    String activity = privateCcCiRltInfo1.getSourceCiInfo().getAttrs().get("活动名称");

                    ESRltSearchBean esRltSearchBean2 = new ESRltSearchBean();
                    // 目标端是活动
                    esRltSearchBean2.setTargetCiCodes(Collections.singleton(activityCiCode));
                    // 关联关系
                    esRltSearchBean2.setRltClassIds(Collections.singletonList(containLinkClass.getCiClass().getId()));
                    // 源端是流程
                    esRltSearchBean2.setSourceClassIds(Collections.singletonList(classInfo.getCiClass().getId()));
                    List<CcCiRltInfo> privateCcCiRltInfos2 = iciRltSwitchSvc.searchRltByScroll(esRltSearchBean2, LibType.DESIGN);

                    if (!CollectionUtils.isEmpty(privateCcCiRltInfos2)) {
                        for (CcCiRltInfo privateCcCiRltInfo2 : privateCcCiRltInfos2) {
                            String process = privateCcCiRltInfo2.getSourceCiInfo().getAttrs().get("流程名称");
                            Map<String, String> detailMap = new LinkedHashMap<>();
                            detailMap.put("关联角色", associatedRole);
                            detailMap.put("活动", activity);
                            detailMap.put("流程", process);
                            detailList.add(detailMap);
                        }
                    } else {
                        Map<String, String> detailMap = new LinkedHashMap<>();
                        detailMap.put("关联角色", associatedRole);
                        detailMap.put("活动", activity);
                        detailMap.put("流程", "");
                        detailList.add(detailMap);
                    }
                }
            } else {
                Map<String, String> detailMap = new LinkedHashMap<>();
                detailMap.put("关联角色", associatedRole);
                detailMap.put("活动", "");
                detailMap.put("流程", "");
                detailList.add(detailMap);
            }
        }

        return detailList;
    }

    @Override
    public List<DepartmentDto> getOrganizationalSystemTree() {
        CcCiClassInfo classInfo = iciClassSvc.getCiClassByClassCode("部门");
        ESCISearchBean esciSearchBean = new ESCISearchBean();
        esciSearchBean.setPageNum(0);
        esciSearchBean.setPageSize(3000);
        esciSearchBean.setClassIds(Collections.singletonList(classInfo.getCiClass().getId()));
        Page<ESCIInfo> esciInfoPage = iciSwitchSvc.searchESCIByBean(esciSearchBean, LibType.DESIGN);
        List<ESCIInfo> rootFlowList = esciInfoPage.getData();
        List<CcCiInfo> ccCiInfos = commSvc.transEsInfoList(rootFlowList, false);

        // 获取所有顶级部门
        List<CcCiInfo> topLevelDepartments = ccCiInfos.stream()
                .filter(esciInfo -> esciInfo.getAttrs().get("上级部门").equals(""))
                .collect(Collectors.toList());
        if (topLevelDepartments.isEmpty()){
            return new ArrayList<>();
        }
        List<DepartmentDto> departmentDtos = new ArrayList<>();
        for (CcCiInfo topLevelDepartment : topLevelDepartments) {
            DepartmentDto departmentDto = new DepartmentDto();
            departmentDto.setCiInfo(topLevelDepartment);
            departmentDto.setId(topLevelDepartment.getCi().getId());
            departmentDto.setCiCode(topLevelDepartment.getCi().getCiCode());
            departmentDto.setName(topLevelDepartment.getAttrs().get("部门名称"));
            departmentDto.setDepartmentPrimaryKey(topLevelDepartment.getAttrs().get("部门主键"));
            departmentDtos.add(departmentDto);
        }
        for (DepartmentDto departmentDto : departmentDtos) {
            List<DepartmentDto> children = findChildren(departmentDto, ccCiInfos);
            if (!children.isEmpty()) {
                departmentDto.setChildren(children);
            }
        }
        // 按照 createTime 排序
        departmentDtos.sort(Comparator.comparing(
                departmentDto -> departmentDto.getCiInfo().getCi().getCreateTime()));

        return departmentDtos;
    }

    private List<DepartmentDto> findChildren(DepartmentDto departmentDto, List<CcCiInfo> ccCiInfos) {
        String parentId = departmentDto.getDepartmentPrimaryKey();
        return ccCiInfos.stream()
                .filter(ccCiInfo -> parentId.equals(ccCiInfo.getAttrs().get("上级部门")))
                .map(ccCiInfo -> {
                    DepartmentDto childDto = new DepartmentDto();
                    childDto.setCiInfo(ccCiInfo);
                    childDto.setId(ccCiInfo.getCi().getId());
                    childDto.setName(ccCiInfo.getAttrs().get("部门名称"));
                    childDto.setCiCode(ccCiInfo.getCi().getCiCode());
                    childDto.setDepartmentPrimaryKey(ccCiInfo.getAttrs().get("部门主键"));
                    List<DepartmentDto> grandchildren = findChildren(childDto, ccCiInfos);
                    if (!grandchildren.isEmpty()) {
                        childDto.setChildren(grandchildren);
                    }
                    return childDto;
                })
                .collect(Collectors.toList());
    }

    @Override
    public Page<ESCIInfo> findOrganizationalSystemListNew(OrganizationalSystemParamVo paramVo) {
        if (paramVo == null) {
            throw new BusinessException("参数不能为空!");
        }
        if (StringUtils.isEmpty(paramVo.getCiCode())) {
            throw new BusinessException("部门信息不能为空!");
        }
        CcCiClassInfo ciClass = iciClassSvc.getCiClassByClassCode(paramVo.getName());
        if (ciClass == null) {
            throw new BusinessException("未获取到岗位信息!");
        }
        SysUser sysUser = SysUtil.getCurrentUserInfo();
        List<String> list = new ArrayList<>();
        CcCiInfo ciByCode = iciSwitchSvc.getCiByCode(paramVo.getCiCode(), sysUser.getLoginCode(), LibType.DESIGN);
        if (ciByCode == null) {
            throw new BusinessException("未获取到部门信息!");
        }
        ESCISearchBean esciSearchBean1 = new ESCISearchBean();

        ESAttrBean esAttrBean = new ESAttrBean();
        esAttrBean.setKey("上级部门");
        esAttrBean.setValue(ciByCode.getAttrs().get("部门主键"));
        esAttrBean.setOptType(1);
        esciSearchBean1.setAndAttrs(Collections.singletonList(esAttrBean));
        esciSearchBean1.setPageNum(1);
        esciSearchBean1.setPageSize(3000);
        esciSearchBean1.setClassIds(Collections.singletonList(ciByCode.getCiClass().getId()));
        Page<ESCIInfo> esciInfoPage1 = iciSwitchSvc.searchESCIByBean(esciSearchBean1, LibType.DESIGN);
        List<ESCIInfo> listByQuery = esciInfoPage1.getData();
        list.add(ciByCode.getCi().getCiCode());
        for (ESCIInfo esciInfo : listByQuery) {
            list.add(esciInfo.getCiCode());
        }

        ESCISearchBean esciSearchBean = new ESCISearchBean();
        if (StringUtils.isNoneBlank(paramVo.getWord())) {
            esciSearchBean.setWords(Collections.singletonList(paramVo.getWord()));
        }
        if (StringUtils.isNoneBlank(paramVo.getSortField())) {
            esciSearchBean.setSortField(paramVo.getSortField());
        }else {
            List<String> collect = ciClass.getAttrDefs().stream().map(c -> c.getProName()).collect(Collectors.toList());
            if (collect.contains("岗位编号")) {
                esciSearchBean.setSortField("attrs.岗位编号");
            } else {
                esciSearchBean.setSortField("modifyTime");
            }
        }
        esciSearchBean.setAsc(paramVo.getAsc()==null?true:paramVo.getAsc());
        esciSearchBean.setPageNum(paramVo.getPageNum());
        esciSearchBean.setPageSize(paramVo.getPageSize());
        esciSearchBean.setClassIds(Collections.singletonList(ciClass.getCiClass().getId()));

        List<ESAttrBean> orAttrs = list.stream()
                .map(departmentPrimaryKey -> {
                    ESAttrBean esAttrBean1 = new ESAttrBean();
                    esAttrBean1.setKey("所属部门");
                    esAttrBean1.setValue(departmentPrimaryKey);
                    esAttrBean1.setOptType(2);
                    return esAttrBean1;
                })
                .collect(Collectors.toList());
        esciSearchBean.setOrAttrs(orAttrs);

        ICISvc ciSvc = iciSwitchSvc.getCiSvc(LibType.DESIGN);
        Page<ESCIInfo> esciInfoPage = ciSvc.searchESCIByBean(esciSearchBean);
        if (esciInfoPage != null && !CollectionUtils.isEmpty(esciInfoPage.getData())) {
            List<ESCIInfo> ciList = esciInfoPage.getData();
            Set<String> ciCodeSet = new HashSet<>();
            CcCiClassInfo rltClass = iciRltSwitchSvc.getRltClassByCode("拥有");
            Map<String, Set<String>> roleMap = new HashMap<>();

            if (rltClass != null) {
                ESRltSearchBean rltSearchBean = new ESRltSearchBean();
                rltSearchBean.setRltClassIds(Lists.newArrayList(rltClass.getCiClass().getId()));
                rltSearchBean.setSourceCiCodes(ciCodeSet);
                List<CcCiRltInfo> ccCiRltInfoList = iciRltSwitchSvc.searchRltByScroll(rltSearchBean, LibType.DESIGN);

                if (!CollectionUtils.isEmpty(ccCiRltInfoList)) {
                    for (CcCiRltInfo rltInfo : ccCiRltInfoList) {
                        CcCiInfo sourceCiInfo = rltInfo.getSourceCiInfo();
                        CcCiInfo targetCiInfo = rltInfo.getTargetCiInfo();
                        CcCiClass roleClass = targetCiInfo.getCiClass();

                        if (Objects.equals(roleClass.getClassCode(), "岗位角色")) {
                            String roleName = targetCiInfo.getAttrs().get("角色名称");
                            roleMap.computeIfAbsent(sourceCiInfo.getCi().getCiCode(), k -> new HashSet<>()).add(roleName);
                        }
                    }
                }
            }

            for (ESCIInfo ciInfo : ciList) {
                Set<String> roleSet = roleMap.get(ciInfo.getCiCode());
                Map<String, Object> attrs = ciInfo.getAttrs();

                if (!CollectionUtils.isEmpty(roleSet)) {
                    attrs.put("关联角色", Strings.join(roleSet, "，"));
                }
            }
        }
        return esciInfoPage;
    }

    @Override
    public ImportExcelMessage importPostExcelBatch(MultipartFile file, Long id) {
        // 校验文件
        boolean isXlsx = FileUtil.ExcelUtil.validExcelImportFile(file);
        ImportExcelMessage message = new ImportExcelMessage();
        message.setOriginalFileName(file.getOriginalFilename());
        File excelFile;
        try {
            //上传
            String fileName = CommUtil.getExportFileName(SysUtil.StaticUtil.CI, isXlsx ? CommUtil.EXCEL07_XLSX_EXTENSION : CommUtil.EXCEL03_XLS_EXTENSION);
            String pathName = File.separator + LocalDate.now() + File.separator + fileName;
            excelFile = new File(localPath + pathName);
            FileUtil.writeFile(excelFile, file.getBytes());
            message.setFileName(pathName);
        } catch (Exception e) {
            throw new MessageException(e.getMessage());
        }
        List<String> sheetNames = EasyExcelUtil.getExcelAllSheetNames(excelFile);
        CcCiInfo ciInfoById = iciSwitchSvc.getCiInfoById(id, LibType.DESIGN);
        if (ciInfoById != null){
            // 创建一个临时文件来保存修改后的数据
            File tempFile = new File(localPath + "temp_" + excelFile.getName());

            // 使用 ExcelWriter 读取和写回整个文件
            try (ExcelWriter writer = EasyExcel.write(tempFile).build()) {
                Map<String, List<Map<Integer, String>>> allSheetData = new HashMap<>();

                for (String sheetName : sheetNames) {
                    List<Map<Integer, String>> dataMaps = EasyExcel.read(excelFile)
                            .sheet(sheetName)
                            .headRowNumber(0) // 假设表头在第一行
                            .doReadSync();

                    allSheetData.put(sheetName, dataMaps);

                    if (!sheetName.equals(SysUtil.StaticUtil.README_SHEETNAME)&&!sheetName.equals(SysUtil.StaticUtil.CI_DEF)) {
                        // 处理岗位页的数据
                        int departmentColumnIndex = -1;

                        Map<Integer, String> headerRow = dataMaps.get(0);
                        if (!dataMaps.isEmpty()) {
                            for (Map.Entry<Integer, String> entry : headerRow.entrySet()) {
                                if ("所属部门".equals(entry.getValue())) {
                                    departmentColumnIndex = entry.getKey();
                                    break;
                                }
                            }
                        }

                        if (departmentColumnIndex != -1) {
                            List<List<String>> data = new ArrayList<>();
                            // 将 Map 转换为 List
                            for (Map<Integer, String> rowMap : dataMaps) {
                                List<String> row = new ArrayList<>(Collections.nCopies(headerRow.size(), null));
                                for (Map.Entry<Integer, String> entry : rowMap.entrySet()) {
                                    row.set(entry.getKey(), entry.getValue());
                                }
                                data.add(row);
                            }

                            // 为“所属部门”列赋值
                            for (int i = 1; i < data.size(); i++) { // 从第二行开始处理数据
                                List<String> row = data.get(i);
                                if (StringUtils.isEmpty(row.get(departmentColumnIndex))) {
                                    row.set(departmentColumnIndex, ciInfoById.getAttrs().get("部门主键"));
                        }
                    }

                            // 更新数据
                            allSheetData.put(sheetName, convertToListOfMaps(data));
                        }
                    }
                }

                // 将所有工作表的数据写回文件
                for (Map.Entry<String, List<Map<Integer, String>>> entry : allSheetData.entrySet()) {
                    WriteSheet writeSheet = EasyExcel.writerSheet(entry.getKey()).build();
                    writer.write(entry.getValue(), writeSheet);
                }
            } catch (Exception e) {
                throw new MessageException(e.getMessage());
            }

            // 将临时文件的内容复制回原文件
            try (FileInputStream fis = new FileInputStream(tempFile);
                 FileOutputStream fos = new FileOutputStream(excelFile)) {
                byte[] buffer = new byte[1024];
                int length;
                while ((length = fis.read(buffer)) > 0) {
                    fos.write(buffer, 0, length);
                }
            } catch (IOException e) {
                throw new MessageException(e.getMessage());
            } finally {
                // 删除临时文件
                if (tempFile.exists()) {
                    tempFile.delete();
                }
            }
        }
        Assert.isTrue(sheetNames.contains(SysUtil.StaticUtil.CI_DEF), "文档内容格式不正确");
        sheetNames.remove(SysUtil.StaticUtil.README_SHEETNAME);
        sheetNames.remove(SysUtil.StaticUtil.CI_DEF);
        Assert.notEmpty(sheetNames, "数据为空，不可导入！");
        message.setSheetNames(sheetNames);
        FileUtil.ExcelUtil.setImportExcelData(message, excelFile);
        return message;
    }

    private List<Map<Integer, String>> convertToListOfMaps(List<List<String>> data) {
        List<Map<Integer, String>> result = new ArrayList<>();
        for (List<String> row : data) {
            Map<Integer, String> rowMap = new LinkedHashMap<>();
            for (int i = 0; i < row.size(); i++) {
                rowMap.put(i, row.get(i));
            }
            result.add(rowMap);
        }
        return result;
    }

    @SneakyThrows
    @Override
    public String exportPosition(HttpServletResponse response, String ciCode) {
        CcCiInfo ciInfo = iciSwitchSvc.getCiByCode(ciCode, SysUtil.getCurrentUserInfo().getLoginCode(), LibType.DESIGN);
        List<Map<String, String>> detail = getDetail(ciCode);
        if (ciInfo == null) {
            throw new BusinessException("获取数据错误!");
        }
        Map<String, String> attrs = ciInfo.getAttrs();
        String s = attrs.get("关联用户");
        String s1 = attrs.get("所属部门");
        String s2 = attrs.get("关联流程");

        ObjectMapper objectMapper = new ObjectMapper();
        if (StringUtils.isNotBlank(s)) {
            List<String> userNames = new ArrayList<>();
            JsonNode rootNode = objectMapper.readTree(s);
            for (JsonNode node : rootNode) {
                String userName = node.get("userName").asText();
                userNames.add(userName);
            }
            attrs.put("关联用户", String.join(",", userNames));
        }
        if (StringUtils.isNotBlank(s1)) {
            JsonNode rootNode = objectMapper.readTree(s1);
            for (JsonNode node : rootNode) {
                String primary = node.get("primary").asText();
                String[] parts = primary.split("\\|");
                if (parts.length > 1) {
                    attrs.put("所属部门", parts[1]);
                }else {
                    attrs.put("所属部门", primary);
                }
            }
        }
        if (StringUtils.isNotBlank(s2)) {
            List<String> primarys = new ArrayList<>();
            JsonNode rootNode = objectMapper.readTree(s2);
            for (JsonNode node : rootNode) {
                String primary = node.get("primary").asText();
                primarys.add(primary);
            }
            attrs.put("关联流程", String.join(",", primarys));
        }
        XWPFDocument document = this.getWord(attrs, detail);
        OutputStream out = null;
        try {
            Long dateTimeFolder = ESUtil.getNumberDate();
            File destFolder = new File(localPath + "/" + dateTimeFolder);
            if (!destFolder.exists()) {
                destFolder.mkdirs();
            }
            String destFileName =  ciInfo.getAttrs().get("岗位名称") + "岗位说明书.docx";
            File destFile = new File(destFolder, destFileName);
            out = new FileOutputStream(new File(destFile.getCanonicalPath()));
            document.write(out);
            rsmUtils.uploadRsmFromFile(destFile);
            return httpPath + "/" + dateTimeFolder + "/" + destFileName;
        } catch (Exception e) {
            log.info("导出数据失败!" + e);
            throw new BusinessException("导出数据失败!");
        } finally {
            try {
                if (document != null) {
                    document.close();
                }
                if (out != null) {
                    out.close();
                }
            } catch (IOException e) {
                throw new BusinessException("关闭流错误!");
            }
        }
    }

    private XWPFDocument getWord(Map<String, String> attrs, List<Map<String, String>> detail) {
        XWPFDocument document= new XWPFDocument();
        try {

            //添加word方案标题
            XWPFParagraph titleParagraph = document.createParagraph();
            titleParagraph.setAlignment(ParagraphAlignment.CENTER);
            XWPFRun titleParagraphRun = titleParagraph.createRun();
            titleParagraphRun.setText(attrs.get("岗位名称") + "岗位说明书");
            titleParagraphRun.setColor("000000");
            titleParagraphRun.setFontSize(22);


            // 基本信息表格
            createTable1(document, "基本信息", Arrays.asList(attrs));
            // 关联信息表格
            createTable(document, "关联岗位信息", detail);
            return document;
        } catch (BusinessException e) {
            log.info(e.getMessage() + e);
            throw new BusinessException(e.getMessage());
        } catch (Exception e) {
            log.info("导出岗位信息错误!" + e);
            throw new BusinessException("导出岗位信息错误!");
        }
    }

    private void createTable(XWPFDocument document, String tableName, List<Map<String, String>> maps) {
        // 表格标题
        XWPFParagraph tableParagraph = document.createParagraph();
        tableParagraph.setAlignment(ParagraphAlignment.LEFT); // 设置段落左对齐
        XWPFRun tableContext = tableParagraph.createRun();
        tableContext.setText("\r");
        tableContext.setText(tableName);
        tableContext.setFontFamily("微软雅黑");
        tableContext.setBold(true);
        tableContext.setFontSize(14);
        tableContext.setText("\r");

        // 创建表格
        XWPFTable table = document.createTable();
        CTTblWidth tableWidth = table.getCTTbl().addNewTblPr().addNewTblW();
        tableWidth.setType(STTblWidth.DXA);
        tableWidth.setW(BigInteger.valueOf(8000)); // 设置总宽度为 8000

        CTTblWidth tblInd = table.getCTTbl().getTblPr().getTblInd();
        if (tblInd == null) {
            tblInd = table.getCTTbl().getTblPr().addNewTblInd();
        }
        tblInd.setW(BigInteger.valueOf(0)); // 设置表格左边距为 0，使其与标题对齐
        table.getCTTbl().getTblPr().setTblInd(tblInd);
        // 表头
        XWPFTableRow headerRow = table.getRow(0);
        headerRow.setHeight(400);
        int colIndex = 0;

        // 检查 maps 是否为空
        boolean isMapsEmpty = maps.isEmpty();
        // 如果 maps 为空，使用默认表头
        List<String> defaultHeaders = Arrays.asList("关联角色", "活动", "关联流程");
        List<String> headers = isMapsEmpty ? defaultHeaders : maps.get(0).keySet().stream().collect(Collectors.toList());

        // 设置列宽
        CTTblGrid tblGrid = table.getCTTbl().addNewTblGrid();
        int columnCount = headers.size();
        BigInteger columnWidth = BigInteger.valueOf(8000).divide(BigInteger.valueOf(columnCount));
        for (int i = 0; i < columnCount; i++) {
            tblGrid.addNewGridCol().setW(columnWidth);
        }

        for (String key : headers) {
            if (colIndex == 0) {
                headerRow.getCell(0).setText(key);
            } else {
                headerRow.addNewTableCell().setText(key);
            }
            XWPFTableCell cell = headerRow.getTableCells().get(colIndex);
            CTTc cttc = cell.getCTTc();
            CTTcPr ctPr = cttc.addNewTcPr();
            ctPr.addNewVAlign().setVal(STVerticalJc.CENTER);
            cttc.getPList().get(0).addNewPPr().addNewJc().setVal(STJc.CENTER);
            cell.setColor("D9D9D9");
            cell.setWidth(columnWidth.toString());
            colIndex++;
        }

        // 表数据
        if (!isMapsEmpty) {
        for (int i = 0; i < maps.size(); i++) {
            XWPFTableRow dataRow = table.getRow(i + 1);
            if (dataRow == null) {
                dataRow = table.createRow();
            }
            dataRow.setHeight(400);
        colIndex = 0;
            for (String value : maps.get(i).values()) {
            dataRow.getCell(colIndex).setText(value);
            XWPFTableCell cell = dataRow.getTableCells().get(colIndex);
            CTTc cttc = cell.getCTTc();
            CTTcPr ctPr = cttc.addNewTcPr();
            ctPr.addNewVAlign().setVal(STVerticalJc.CENTER);
            cttc.getPList().get(0).addNewPPr().addNewJc().setVal(STJc.CENTER);
                    cell.setWidth(columnWidth.toString());
            colIndex++;
        }
    }
        } else {
            // 添加一行空的数据行
            XWPFTableRow emptyDataRow = table.createRow();
            emptyDataRow.setHeight(400);
            for (int i = 0; i < headers.size(); i++) {
                XWPFTableCell cell = emptyDataRow.getCell(i);
                cell.setText("");
                CTTc cttc = cell.getCTTc();
                CTTcPr ctPr = cttc.addNewTcPr();
                ctPr.addNewVAlign().setVal(STVerticalJc.CENTER);
                cttc.getPList().get(0).addNewPPr().addNewJc().setVal(STJc.CENTER);
                cell.setWidth(columnWidth.toString());
            }
    }
    }



    private void createTable1(XWPFDocument document, String tableName, List<Map<String, String>> maps) {
        // 表格标题
        XWPFParagraph tableParagraph = document.createParagraph();
        XWPFRun tableContext = tableParagraph.createRun();
        tableContext.setText("\r");
        tableContext.setText(tableName);
        tableContext.setFontFamily("微软雅黑");
        tableContext.setBold(true);
        tableContext.setFontSize(14);
        tableContext.setText("\r");
        // 获取数据
        Map<String, String> stringMap = maps.get(0);
        // 创建表格
        int numRows = (stringMap.size() + 1) / 2; // 计算行数
        XWPFTable table = document.createTable(numRows, 4);
        table.setTableAlignment(TableRowAlign.CENTER);//表格居中
        CTTblPr tblPr = table.getCTTbl().getTblPr();
        tblPr.getTblW().setType(STTblWidth.DXA);// 表格固定宽度
        tblPr.getTblW().setW(BigInteger.valueOf(8000));//表格总宽度
        CTTblLayoutType layoutType = table.getCTTbl().getTblPr().addNewTblLayout();
        layoutType.setType(STTblLayoutType.FIXED);//FIXED：列固定宽度 AUTOFIT：列自动宽度
        tblPr.addNewJc().setVal(STJc.LEFT);
        // 设置列宽
        CTTblGrid tblGrid = table.getCTTbl().addNewTblGrid();
        tblGrid.addNewGridCol().setW(BigInteger.valueOf(1000));
        tblGrid.addNewGridCol().setW(BigInteger.valueOf(3000));
        tblGrid.addNewGridCol().setW(BigInteger.valueOf(1000));
        tblGrid.addNewGridCol().setW(BigInteger.valueOf(3000));

        // 设置表格样式
        for (int i = 0; i < table.getNumberOfRows(); i++) {
            XWPFTableRow row = table.getRow(i);
            for (int j = 0; j < row.getTableCells().size(); j++) {
                XWPFTableCell cell = row.getCell(j);
                cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
                for (XWPFParagraph paragraph : cell.getParagraphs()) {
                    paragraph.setAlignment(ParagraphAlignment.CENTER);
                }
                if (j % 2 == 0) {
                    cell.setColor("D9D9D9");
                }
                // 设置行高
                CTTrPr trPr = row.getCtRow().addNewTrPr();
                CTHeight height = trPr.addNewTrHeight();
                height.setVal(BigInteger.valueOf(500)); // 设置初始行高为 360
            }
        }
        // 填充数据
        int rowIndex = 0;
        int colIndex = 0;
        for (Map.Entry<String, String> entry : stringMap.entrySet()) {
            if (colIndex >= 4) {
                colIndex = 0;
                rowIndex++;
            }
            XWPFTableCell keyCell = table.getRow(rowIndex).getCell(colIndex++);
            keyCell.setText(entry.getKey());
            XWPFTableCell valueCell = table.getRow(rowIndex).getCell(colIndex++);
            valueCell.setText(entry.getValue());
        }
    }


    /**
     * 获取指定组织所有子节点
     *
     * @param orgDict      组织id:childNodes字典
     * @param orgIds       待填充所有子节点orgIds=>涵盖自身
     * @param currentOrgId 当前指定组织
     */
    public void getAllChilds(Map<Long, List<SysOrg>> orgDict, Set<Long> orgIds, Long currentOrgId) {
        orgIds.add(currentOrgId);
        if (orgDict.get(currentOrgId) != null) {
            orgDict.get(currentOrgId).forEach(child -> {
                // 最后一层可能不再字典中，反正用的set再添一次
                orgIds.add(child.getId());
                getAllChilds(orgDict, orgIds, child.getId());
            });
        }
    }
}
