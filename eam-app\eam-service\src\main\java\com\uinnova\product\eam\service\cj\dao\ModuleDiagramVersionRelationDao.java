package com.uinnova.product.eam.service.cj.dao;

import com.uinnova.product.eam.model.cj.domain.ModuleDiagramVersionRelation;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;

/**
 * 这块表示视图版本和对应的数据块忽略视图最新版本关系表
 */
@Component
public class ModuleDiagramVersionRelationDao extends AbstractESBaseDao<ModuleDiagramVersionRelation, ModuleDiagramVersionRelation> {
    @Override
    public String getIndex() {
        return "uino_cj_diagram_relation_module";
    }

    @Override
    public String getType() {
        return this.getIndex();
    }

    @PostConstruct
    public void init() {
        initIndex();
    }
}
