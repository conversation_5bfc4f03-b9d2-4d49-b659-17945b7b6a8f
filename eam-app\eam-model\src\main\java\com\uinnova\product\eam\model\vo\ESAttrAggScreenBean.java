package com.uinnova.product.eam.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ESAttrAggScreenBean{

    @ApiModelProperty(value = "页码", example = "1",  required = true)
    private int pageNum = 1;

    @ApiModelProperty(value = "每页数量", example = "20",  required = true)
    private int pageSize = 20;
    /**
     * CI关系分类Id
     */
    @ApiModelProperty(value="Ci关系分类id",example = "123")
    private Long classId = 0L;

    /**
     *属性id
     */
    @ApiModelProperty(value="属性id",example = "123")
    private Long attrDefId;


    @ApiModelProperty(value="属性id列表")
    private Long[] attrDefIds;
    /**
     * 属性名称
     */

    private List<String> attrNames;
    private String attrName;

    /**
     * 检索值
     */
    private String like;

    @ApiModelProperty(value = "约束")
    private Long bindId;

    @ApiModelProperty(value = "用户标识")
    private String ownerCode;
}
