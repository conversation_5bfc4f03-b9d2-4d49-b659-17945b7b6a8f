package com.uinnova.product.eam.model.cj.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 回收站操作枚举类
 *
 * <AUTHOR>
 * @since 2022-3-8 15:26:18
 */
@Getter
@AllArgsConstructor
public enum RecycleBinOperationEnum {

    /**
     * 不存在的操作
     */
    NOT_FOUND(-99999),

    /**
     * 还原
     */
    RE_LIFE(2),

    /**
     * 彻底删除
     */
    REAL_DEL(1);

    /**
     * 操作代码
     */
    private Integer optCode;

    public static RecycleBinOperationEnum getByCode(int optCode) {
        for (RecycleBinOperationEnum value : RecycleBinOperationEnum.values()) {
            if (value.getOptCode() == optCode) {
                return value;
            }
        }

        return RecycleBinOperationEnum.NOT_FOUND;
    }
}
