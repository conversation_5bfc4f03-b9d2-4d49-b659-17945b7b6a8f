package com.uinnova.product.eam.model;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uino.bean.cmdb.base.ESCIInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


@Data
public class AutoDrawCIVo implements Serializable {

    @Comment("ci")
    private Object esciInfo;

    @Comment("子级")
    private List<Object> children;

    @Comment("名称")
    private String name;

    @Comment("下钻标识")
    private Boolean drillDown;

}
