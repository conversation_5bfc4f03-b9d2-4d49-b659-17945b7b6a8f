package com.uinnova.product.vmdb.provider.quality.bean;

import com.binary.core.i18n.Language;
import com.uinnova.product.vmdb.comm.util.SystemUtil;
import com.uinnova.product.vmdb.provider.quality.CiQualityDataSvc;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.List;

public class QualityDataTitleLabel {

    Logger logger = LoggerFactory.getLogger(CiQualityDataSvc.class);

    private boolean isEnEnv = false;

    private String[] keys = {"problemType", "className", "primaryKey", "proName", "proValue", "problemReason", "rltClassName", "oppositeClassName"};

    /**
     * 默认的使用中文作为显示对象
     */
    public QualityDataTitleLabel() {
    }

    public QualityDataTitleLabel(boolean useUserLanguage) {
        if (useUserLanguage) {
            try {
                Language language = SystemUtil.getLoginUser().getLanguage();
                if (Language.EN.equals(language)) isEnEnv = true;
            } catch (Exception e) {
                //没有就认为是中文环境
            }
        }
    }

    public String getProblemType() { return isEnEnv ? "Problem type" : "问题类型"; }
    public String getClassName() { return isEnEnv ? "Category name" : "分类名称"; }
    public String getPrimaryKey() { return isEnEnv ? "Primary key" : "业务主键"; }
    public String getAttrName() { return isEnEnv ? "Attribute name" : "属性名称"; }
    public String getAttrValue() { return isEnEnv ? "Attribute value" : "属性值"; }
    public String getProblemReason() { return isEnEnv ? "Cause of the problem" : "问题原因"; }
    public String getRltClassName() { return isEnEnv ? "Relationship name" : "关系名称"; }
    public String getOppositeClassName() { return isEnEnv ? "Opposite category" : "对端分类"; }

    public String getKey(Integer index) {
        List<String> keys = Arrays.asList(this.keys);
        return keys.get(index);
    }



}
