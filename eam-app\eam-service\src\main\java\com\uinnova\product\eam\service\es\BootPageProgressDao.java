package com.uinnova.product.eam.service.es;

import com.uinnova.product.eam.comm.model.es.BootPageProgress;
import com.uino.dao.AbstractESBaseDao;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;

@Service
public class BootPageProgressDao extends AbstractESBaseDao<BootPageProgress, BootPageProgress> {
    @Override
    public String getIndex() {
        return "uino_eam_boot_page_progress";
    }

    @Override
    public String getType() {
        return getIndex();
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }
}
