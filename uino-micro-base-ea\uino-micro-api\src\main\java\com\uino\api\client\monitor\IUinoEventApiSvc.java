package com.uino.api.client.monitor;

import com.binary.jdbc.Page;
import com.uino.bean.event.EventCiKpiCount;
import com.uino.bean.event.modeling.EventModel;
import com.uino.bean.event.param.EventCiKpiCountParam;
import com.uino.bean.event.param.EventCiSeverityQueryParam;
import com.uino.bean.monitor.base.ESAlarm;
import com.uino.bean.monitor.base.ESMonEapEvent;
import com.uino.bean.monitor.base.ESMonSysSeverityInfo;
import com.uino.bean.monitor.buiness.CurrentEventQueryDto;
import com.uino.bean.monitor.buiness.EventQueryDto;

import java.util.List;
import java.util.Set;

/**
 * 公共组件-通用告警操作接口
 * 
 * <AUTHOR>
 * @Date 2021-07-06
 */
public interface IUinoEventApiSvc {

	EventModel getEventModel();

	/**
	 * 分页查询告警信息
	 * @param queryDto
	 * @return
	 */
	Page<ESMonEapEvent> searchEventPage(EventQueryDto queryDto);

	/**
	 * 保存告警-模拟告警
	 * @param saveBean
	 */
	void saveAlarm(ESAlarm saveBean);

	/**
	 * 保存当前未关闭告警信息
	 * @param fmEvent
	 */
	void saveCurrentEvent(ESMonEapEvent fmEvent);

	/**
	 * 查询当前未关闭告警信息
	 * @param queryDto
	 * @return
	 */
	List<ESMonEapEvent> listCurrentEventByParam(CurrentEventQueryDto queryDto);

	/**
	 * EP查询当前未关闭告警信息(条件数组之间关系为或)
	 * @param queryDtos
	 * @return
	 */
	List<ESMonEapEvent> listCurrentEventByParam(List<CurrentEventQueryDto> queryDtos);

	/**
	 * 删除当前告警信息
	 * @param fmEvent
	 */
	void delCurrentEvent(ESMonEapEvent fmEvent);

	/**
	 * 根据id查询列表
	 * @param ids
	 * @return
	 */
	List<ESMonEapEvent> listCurrentEventByIds(Set<String> ids);

	/**
	 * 获取ciId集合下的告警信息
	 * @param ciIds
	 * @return
	 */
	List<ESMonEapEvent> listCurrentEventByCiIds(List<Long> ciIds);

	/**
	 * 根据id查询当前告警信息
	 * @param id
	 * @return
	 */
	ESMonEapEvent currentEventById(String id);

	/**
	 * 清除当前告警信息
	 */
	void clearCurrentEvent();
	void clearCurrentEvent(Long domainId);

	/**
	 * 保存历史告警信息
	 * @param esMonEapEvent
	 * @return
	 */
	ESMonEapEvent saveEventHis(ESMonEapEvent esMonEapEvent);

	/**
	 * 根据id查询历史告警信息
	 * @param id
	 * @return
	 */
	ESMonEapEvent hisEventById(String id);

	/**
	 * 获取ciCode集合下的告警信息
	 * @param ciCodes
	 * @return
	 */
	List<ESMonEapEvent> listCurrentEventByCiCodes(List<String> ciCodes);
	List<ESMonEapEvent> listCurrentEventByCiCodes(Long domainId, List<String> ciCodes);

	/**
	 * 查询告警级别列表
	 * @return
	 */
	List<ESMonSysSeverityInfo> getAllEventSeverity();
	List<ESMonSysSeverityInfo> getAllEventSeverity(Long domainId);

	/**
	 * 告警级别
	 * @param param
	 * @return ciCode及对应告警的最严重级别、数量、颜色的信息
	 */
	List<EventCiKpiCount> getEventCountByCiCodeAndKpiCodes(EventCiKpiCountParam param);
	List<EventCiKpiCount> getEventCountByCiCodeAndKpiCodes(Long domainId, EventCiKpiCountParam param);

	/**
	 * 批量保存告警信息
	 * @param events
	 */
	boolean saveEventBatch(List<ESMonEapEvent> events);

	/**
	 * 根据ciCode及告警等级查询当前告警信息（备注：告警状态参数status只针对ES中event索引查询，redis查询不生效）
	 * @param queryParam 封装查询参数（ciCode集合、severity集合、status集合）
	 * @return
	 */
	List<ESMonEapEvent> queryCurrentEventsByCiCodesAndServeritys(EventCiSeverityQueryParam queryParam);
	List<ESMonEapEvent> queryCurrentEventsByCiCodesAndServeritys(Long domainId, EventCiSeverityQueryParam queryParam);
}
