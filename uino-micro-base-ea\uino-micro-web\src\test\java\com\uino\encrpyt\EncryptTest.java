package com.uino.encrpyt;



import com.uino.StartBaseWebAppliaction;
import com.uino.util.encrypt.Encrypt;
import com.uino.util.encrypt.EncryptType;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;


/**
 * @Title: EncryptTest
 * @Author: YGQ
 * @Create: 2021-08-09 11:08
 **/
@RunWith(SpringRunner.class)
@WebAppConfiguration
@SpringBootTest(classes = {StartBaseWebAppliaction.class})
@ActiveProfiles("provider-local-dev")
@AutoConfigureMockMvc(addFilters = false)
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class EncryptTest {

    @Autowired
    private Encrypt encrypt;

    @Test
    public void aes() {
        String a1 = encrypt.encryptOrDecrypt(EncryptType.AesEncrypt, "123", "0123456789123456");
        System.out.println(a1);
        System.out.println(encrypt.encryptOrDecrypt(EncryptType.AesDecrypt, a1, "0123456789123456"));
    }

    @Test
    public void des() {
        String a1 = encrypt.encryptOrDecrypt(EncryptType.DesEncrypt, "123", "01234567");
        System.out.println(a1);
        System.out.println(encrypt.encryptOrDecrypt(EncryptType.DesDecrypt, a1, "01234567"));
    }


    @Test
    public void jayspt() {
        String a1 = encrypt.encryptOrDecrypt(EncryptType.JasyptEncrypt, "123", "01234567");
        System.out.println(a1);
        System.out.println(encrypt.encryptOrDecrypt(EncryptType.JasyptDecrypt, a1, "01234567"));
    }

    @Test
    public void base64() {
        String a1 = encrypt.encryptOrDecrypt(EncryptType.Base64Encrypt, "123123asfadsf");
        System.out.println(a1);
        System.out.println(encrypt.encryptOrDecrypt(EncryptType.Base64Decrypt, a1));
    }


}
