package com.uinnova.product.vmdb.comm.integration;

import com.binary.framework.exception.ServiceException;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

/**
 * 
 * <AUTHOR>
 *
 */
public class SpringContextAware implements ApplicationContextAware {

    private static ApplicationContext springContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        springContext = applicationContext;
    }

    public static ApplicationContext getSpringContext() {
        if (springContext == null) {
            throw new ServiceException(" not initialize com.uinnova.product.vmdb.comm.integration.SpringContextAware in springContext! ");
        }
        return springContext;
    }

}
