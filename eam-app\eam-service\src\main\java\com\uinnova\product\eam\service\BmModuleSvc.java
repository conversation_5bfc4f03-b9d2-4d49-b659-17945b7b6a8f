package com.uinnova.product.eam.service;

import com.uinnova.product.eam.base.model.ModuleFieldInfo;
import com.uinnova.product.eam.base.model.SpecificationInfo;
import com.uinnova.product.vmdb.comm.model.ci.CcCi;
import com.uinnova.product.vmdb.provider.rlt.bean.CcCiRltInfo;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.base.LibType;

import java.util.List;
import java.util.Map;

/**
 * 业务组件相关接口
 *
 * <AUTHOR>
 */
public interface BmModuleSvc {
    /**
     * 业务组件定义图根据图中任务查询所属领域
     *
     * @param diagramId
     */
    List<ESCIInfo> queryBelongToDomainByTask(String diagramId);

    /**
     * 业务领域与组件说明书
     *
     * @param diagramId
     * @return
     */
    SpecificationInfo domainAndModuleSpecification(String diagramId);

    /**
     * 业务组件发布
     *
     * @param diagramId
     * @param releaseDesc
     * @param dirId
     * @return
     */
    String releaseModuleDiagram(String diagramId, String releaseDesc, Long dirId, String targetCiCode);

    /**
     * 根据CI任务CiCode 创建任务与对应视图实体间的关系
     *
     * @param ciCode
     * @return
     */
    String createTestAndEntityRltByTestCiCode(String ciCode);

    /**
     * 根据源端CIcode，获取目标端为实体的元素
     * @param ciCode
     * @param ownerCode
     * @return
     */
    List<CcCiRltInfo> getTargetCiAndRltBySourceCiCode(String ciCode,String ownerCode);

    /**
     * 业务组件与领域关系图自动成图
     * @param diagramId
     * @param ciCode
     * @return
     */
    ModuleFieldInfo quickDrawing(String diagramId, String ciCode);

    /**
     * 校验业务组件所属领域
     * @param source
     * @param ciCodes
     * @return
     */
    Map<String, Boolean> checkModuleDomain(String source, List<String> ciCodes);
}
