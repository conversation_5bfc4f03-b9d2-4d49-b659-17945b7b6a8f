package com.uinnova.product.vmdb.provider.sys.bean;

import com.uinnova.product.vmdb.comm.model.ci.CcCiClass;
import com.uinnova.product.vmdb.comm.model.rule.CcCiRoleData;
import com.uinnova.product.vmdb.comm.model.rule.CcCiTagDef;

import java.io.Serializable;
import java.util.List;

public class SysDataAuth implements Serializable{
	private static final long serialVersionUID = 1L;
	
	/**
	 * 标签权限
	 */
	public static final int DATA_AUTH_TAG = 1;
	
	/**
	 * 分类权限
	 */
	public static final int DATA_AUTH_TYPE = 2;
	
	/**
	 * 树权限
	 */
	public static final int DATA_AUTH_TREE = 3;
	
	/**
	 * ID[ID](分类或者tagid)
	 */
	private Long id;
	
	/**
	 * 分类对象
	 */
	private CcCiClass ciClass;
	
	/**
	 * 标签对象
	 */
	private CcCiTagDef tagDef;
	
	/**
	 * 1:tag, 2:分类, 3:tree
	 */
	private Integer dataType;
	private Boolean see=false; //权限, 下同
	private Boolean edit=false;
	private Boolean delete=false;
	private Boolean add=false;
	private CcCiRoleData entityBean;
	
	
	
	public SysDataAuth(Integer dataType, Long id) {
		this.id = id;
		this.dataType = dataType;
	}
	
	public SysDataAuth(CcCiTagDef tagDef) {
		this.tagDef = tagDef;
		this.id = tagDef.getId();
		this.dataType = DATA_AUTH_TAG;
	}
	public SysDataAuth(CcCiClass ciClass) {
		this.ciClass = ciClass;
		this.id = ciClass.getId();
		this.dataType = DATA_AUTH_TYPE;
	}
	public SysDataAuth() {}
	
	public CcCiClass getCiClass() {
		return ciClass;
	}
	public void setCiClass(CcCiClass ciClass) {
		this.ciClass = ciClass;
	}
	public CcCiTagDef getTagDef() {
		return tagDef;
	}
	public void setTagDef(CcCiTagDef tagDef) {
		this.tagDef = tagDef;
	}
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Integer getDataType() {
		return dataType;
	}
	public void setDataType(Integer dataType) {
		this.dataType = dataType;
	}
	public Boolean getSee() {
		return see;
	}
	public void setSee(Boolean see) {
		this.see = see;
	}
	public Boolean getEdit() {
		return edit;
	}
	public void setEdit(Boolean edit) {
		this.edit = edit;
	}
	public Boolean getDelete() {
		return delete;
	}
	public void setDelete(Boolean delete) {
		this.delete = delete;
	}
	public Boolean getAdd() {
		return add;
	}
	public void setAdd(Boolean add) {
		this.add = add;
	}

	public CcCiRoleData createEntityBean() {
		if (entityBean==null) {
			Integer authType;
			if (see) {
				authType = 1000+(edit?100:0)+(delete?10:0)+(add?1:0);
			}else {
				authType = 0;
			}
			entityBean = new CcCiRoleData();
			entityBean.setAuthType(authType);
			entityBean.setDataResId(id);// dataId
			entityBean.setDataResType(dataType);
		}
		return entityBean;
	}
	
	/**
	 * 统一设置数据权限
	 * @param authType
	 */
	public void setAuth(Integer authType) {
		if (authType<1000) {
			this.see = authType / 100 == 1;
			this.edit = authType / 10 == 11;
			this.delete = authType % 10 == 1;
			this.add = false;
		}else {
			this.see = authType / 1000 == 1;
			this.edit = authType / 100 % 10 == 1;
			this.delete = authType / 10 % 10== 1;
			this.add = authType % 10 ==1;
		}
	}

	/**
	 * 多角色的同一个数据权限合并
	 * @param ccCiRoleDatas
	 */
	public void mergeAuth(List<CcCiRoleData> ccCiRoleDatas) {
		for (CcCiRoleData ccCiRoleData : ccCiRoleDatas) {
			Integer authType = ccCiRoleData.getAuthType();
			if (authType<1000) {
				authType*=10;
			}
			boolean see1,edit1,delete1,add1;
			see1 = authType / 1000 == 1;
			edit1 = authType / 100 % 10 == 1;
			delete1 = authType / 10 % 10== 1;
			add1 = authType % 10 ==1;
			this.see = see | see1;
			this.edit = edit | edit1;
			this.delete = delete | delete1;
			this.add = add | add1;
		}
		
	}
	
}
