package com.uinnova.product.eam.base.cj;

import lombok.Data;

import java.io.Serializable;

/**
 * @description: 响应状态信息
 * @author: Lc
 * @create: 2022-01-05 16:44
 */
@Data
public class ResultMsg implements Serializable{

	private boolean success;

	private String message;

	private Integer code;

	private Object data;

	public ResultMsg() {
	}
	public ResultMsg(Object object) {
		this.success = true;
		this.message = "请求成功";
		this.data = object;
		this.code = -1;
	}
	public ResultMsg(boolean success, String message) {
		this.success = success;
		this.message = message;
		this.data = null;
		this.code = -1;
	}
	public ResultMsg(String message, Object object) {
		this.success = true;
		this.message = message;
		this.data = object;
		this.code = -1;
	}

	public ResultMsg(boolean success, String message, Object object) {
		this.success = success;
		this.message = message;
		this.data = object;
		this.code = -1;
	}

	public ResultMsg(boolean success, Integer code , String message) {
		this.success = success;
		this.code = code;
		this.message = message;
	}

	public ResultMsg(boolean success, Integer code) {
		this.success = success;
		this.code = code;
	}

	public ResultMsg(boolean success, Integer code , String message, Object object) {
		this.success = success;
		this.code = code;
		this.message = message;
		this.data = object;
	}

	public static ResultMsg error(String message, Object object){
		return new ResultMsg(false, message, object);
	}

	public static ResultMsg error(String message){
		return new ResultMsg(false, message, null);
	}

	public static ResultMsg error(Integer code , String message){
		return new ResultMsg(false,code , message, null);
	}

	public static ResultMsg ok(){
		return new ResultMsg(true,-1, null,null);
	}

	public static ResultMsg ok(String message, Object object){
		return new ResultMsg(true,-1, message, object);
	}

	public static ResultMsg ok(String message){
		return new ResultMsg(true,-1, message,null);
	}

	public static ResultMsg getSuccessResult() {
		return new ResultMsg( Boolean.TRUE, "成功",null);
	}

	public static ResultMsg getSuccessResult(Object data) {
		return new ResultMsg( Boolean.TRUE,-1, "成功",data);
	}

	public static ResultMsg getErrorResult(String message) {
		return new ResultMsg( Boolean.FALSE,message,null);
	}
}
