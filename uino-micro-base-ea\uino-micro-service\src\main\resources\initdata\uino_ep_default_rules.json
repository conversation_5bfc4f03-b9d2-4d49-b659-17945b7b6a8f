[{"id": 1, "name": "默认压缩规则", "ruleType": 3, "priority": 1.0, "sourceIds": "[]", "action": "{'IDENTIFIER':'${SOURCECINA<PERSON>}_${SOURCEALERTKEY}','SOURCESEVERITY':'${SOURCESEVERITY}','SEVERITY':'${SEVERITY}','LASTOCCURRENCE':'${LASTOCCURRENCE}','SOURCEEVENTID':'${SOURCEEVENTID}'}", "actionHtml": "{'properties':[{'title':'原始事件级别 ','bdcolor':true,'name':'SourceSeverity'},{'title':'事件级别 ','bdcolor':true,'name':'Severity'},{'title':'最后发生时间','bdcolor':true,'name':'LastOccurrence'},{'title':'源事件ID','bdcolor':true,'name':'SourceEventID'}],'identifier':'${SOURCECINAME}_${SOURCEALERTKEY}'}", "active": 1, "activeTimeRange": 0, "description": "筛选事件范围：事件对象等于*", "selector": "((1==1)&&((1==1)))", "selectorHtml": "{'selector':[{'selectors':[{'property':'CINAME','operator':'==','value':'*','propertyDesc':'事件对象','operatorDesc':'等于'}]}]}", "domain": 1, "createPersonId": "admin", "createTime": 20170607154637, "modifyTime": 20180706153701, "dicModifyTime": 20180706153701}, {"id": 2, "name": "默认丰富规则", "ruleType": 12, "priority": 99999.99, "sourceIds": "[]", "action": "{'ciMatches':[{'matchType':0,'eventAttr':'DTName','ciAttr':'DTName','eventAttrValue':'孪生体名称'},{'matchType':0,'eventAttr':'ciItem','ciAttr':'ciItem','eventAttrValue':'IP地址'},{'matchType':0,'eventAttr':'ciOwner','ciAttr':'ciOwner','eventAttrValue':'负责人'},{'matchType':0,'eventAttr':'ciMgmtTeam','ciAttr':'ciMgmtTeam','eventAttrValue':'归属部门'},{'matchType':0,'eventAttr':'DTOwnedSpace','ciAttr':'DTOwnedSpace','eventAttrValue':'DT所属空间'},{'matchType':0,'eventAttr':'ciLocation','ciAttr':'ciLocation','eventAttrValue':'物理位置'},{'matchType':0,'eventAttr':'ciMgmtGroup','ciAttr':'ciMgmtGroup','eventAttrValue':'所属群组'}]}", "actionHtml": "{'ciMatches':[{'matchType':0,'eventAttr':'DTName','ciAttr':'DTName','eventAttrValue':'孪生体名称'},{'matchType':0,'eventAttr':'ciItem','ciAttr':'ciItem','eventAttrValue':'IP地址'},{'matchType':0,'eventAttr':'ciOwner','ciAttr':'ciOwner','eventAttrValue':'负责人'},{'matchType':0,'eventAttr':'ciMgmtTeam','ciAttr':'ciMgmtTeam','eventAttrValue':'归属部门'},{'matchType':0,'eventAttr':'DTOwnedSpace','ciAttr':'DTOwnedSpace','eventAttrValue':'DT所属空间'},{'matchType':0,'eventAttr':'ciLocation','ciAttr':'ciLocation','eventAttrValue':'物理位置'},{'matchType':0,'eventAttr':'ciMgmtGroup','ciAttr':'ciMgmtGroup','eventAttrValue':'所属群组'}]}", "active": 1, "activeTimeRange": 0, "description": "默认丰富规则", "selector": "((1==1)&&((1==1)))", "selectorHtml": "{}", "domain": 0, "createPersonId": "admin", "createTime": 20170817171240, "modifyTime": 20170822150750, "dicModifyTime": 20170822150750}]