package com.uino.dao.cmdb;

import java.util.List;

import jakarta.annotation.PostConstruct;

import org.springframework.stereotype.Service;

import com.uinnova.product.vmdb.comm.model.image.CCcImage;
import com.uino.dao.AbstractESBaseDao;
import com.uino.dao.ESConst;
import com.uino.util.sys.CommonFileUtil;
import com.uino.bean.cmdb.base.CcImage;

/**
 * 分类目录
 * 
 * <AUTHOR>
 */
@Service
public class ESImageSvc extends AbstractESBaseDao<CcImage, CCcImage> {

    @Override
    public String getIndex() {
        return ESConst.INDEX_CMDB_IMAGE;
    }

    @Override
    public String getType() {
        return ESConst.INDEX_CMDB_IMAGE;
    }

    @PostConstruct
    public void init() {
		List<CcImage> list = CommonFileUtil.getData("/initdata/uino_cmdb_image.json", CcImage.class);
        super.initIndex(list);
    }
}
