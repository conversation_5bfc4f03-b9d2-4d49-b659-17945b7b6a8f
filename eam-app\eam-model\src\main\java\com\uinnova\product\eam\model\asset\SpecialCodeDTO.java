package com.uinnova.product.eam.model.asset;

import com.binary.core.util.BinaryUtils;
import com.binary.framework.bean.annotation.Comment;
import com.uino.bean.cmdb.base.ESCIInfo;
import lombok.Data;

import java.io.Serializable;

@Data
public class SpecialCodeDTO implements Serializable {

    @Comment("标准规范对应ciCode")
    private String ciCode;

    @Comment("标准规范名称")
    private String specificationName;

    @Comment("创建人")
    private String creator;

    @Comment("创建时间")
    private Long createTime;

    public SpecialCodeDTO(){}

    public SpecialCodeDTO(ESCIInfo esciInfo){
        this.ciCode = esciInfo.getCiCode();
        this.specificationName = esciInfo.getCiLabel();
        this.createTime = BinaryUtils.toDateTime(esciInfo.getCreateTime()).getTime();
        this.creator = esciInfo.getCreator();
    }
}
