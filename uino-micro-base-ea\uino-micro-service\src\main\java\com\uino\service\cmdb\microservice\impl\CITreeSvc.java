package com.uino.service.cmdb.microservice.impl;

import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.comm.model.ci.CcCiAttrDef;
import com.uino.bean.cmdb.base.ESCIAttrTransConfig;
import com.uino.bean.cmdb.base.ESCIClassInfo;
import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.base.ESCITreeConfigInfo;
import com.uino.bean.cmdb.base.ESPropertyType;
import com.uino.bean.cmdb.business.CITreeNode;
import com.uino.bean.cmdb.query.ESAttrAggBean;
import com.uino.dao.BaseConst;
import com.uino.dao.cmdb.ESCIAttrTransConfigSvc;
import com.uino.dao.cmdb.ESCIClassSvc;
import com.uino.dao.cmdb.ESCISvc;
import com.uino.dao.cmdb.ESCITreeConfigInfoSvc;
import com.uino.service.cmdb.microservice.ICITreeSvc;
import com.uino.util.sys.ValidDtoUtil;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
public class CITreeSvc implements ICITreeSvc {

    @Autowired
    private ESCITreeConfigInfoSvc esciTreeConfigInfoSvc;

    @Autowired
    private ESCISvc esciSvc;

    @Autowired
    private ESCIClassSvc esciClassSvc;

    @Autowired
    private ESCIAttrTransConfigSvc attrConfigSvc;

    @Override
    public List<ESCITreeConfigInfo> getCITreeConfigs(Long domainId) {
        return esciTreeConfigInfoSvc.getSortListByQuery(1, 9999, QueryBuilders.termQuery("domainId", domainId), "createTime", true)
                .getData();
    }

    @Override
    public ESCITreeConfigInfo getCITreeConfig(Long id) {
        return esciTreeConfigInfoSvc.getById(id);
    }

    @Override
    public ESCITreeConfigInfo save(ESCITreeConfigInfo saveInfo) {
        ValidDtoUtil.valid(saveInfo);
        if (saveInfo.getDomainId() == null) {
            saveInfo.setDomainId(BaseConst.DEFAULT_DOMAIN_ID);
        }
        Long domainId = saveInfo.getDomainId();
        Long id = saveInfo.getId();
        String treeName = saveInfo.getName();
        List<Long> clsIds = saveInfo.getCiClsIds();
        List<String> attrNames = saveInfo.getLevelAttrNames();
        BoolQueryBuilder validQuery = QueryBuilders.boolQuery();
        validQuery.must(QueryBuilders.termQuery("domainId", domainId));
        if (id != null) {
            validQuery.mustNot(QueryBuilders.termQuery("id", id));
        }
        validQuery.must(QueryBuilders.termQuery("name.keyword", treeName));
        long validRes = esciTreeConfigInfoSvc.countByCondition(validQuery);
        Assert.isTrue(validRes <= 0, "名称重复");
        List<ESCIClassInfo> clsInfos = esciClassSvc.getListByQuery(QueryBuilders.termsQuery("id", clsIds));
        Assert.isTrue(clsInfos.size() == clsIds.size(), "传入了非法分类");
        attrNames.forEach(attrName -> {
            clsInfos.forEach(cls -> {
                List<CcCiAttrDef> validList = cls.getCcAttrDefs().stream()
                        .filter(def -> def.getProName().equals(attrName)).collect(Collectors.toList());
                Integer proType=validList.get(0).getProType();
                Assert.isTrue(validList.size() > 0, "属性【" + attrName + "】在分类【" + cls.getClassName() + "】中不存在");
                Assert.isTrue(proType != null && proType != ESPropertyType.PICTURE.getValue()
                        && proType != ESPropertyType.MODEL.getValue() && proType != ESPropertyType.DOCUMENT.getValue(),"属性[" + attrName.toUpperCase() + "]引用不合法");
            });
        });
        Long dataId = esciTreeConfigInfoSvc.saveOrUpdate(saveInfo);
        return esciTreeConfigInfoSvc.getById(dataId);
    }

    @Override
    public List<ESCITreeConfigInfo> delete(Long domainId, Long id) {
        esciTreeConfigInfoSvc.deleteById(id);
        return getCITreeConfigs(domainId);
    }

    @Override
    public CITreeNode getCITree(ESCITreeConfigInfo config, boolean hasNullNode, boolean returnAllCI) {
        ValidDtoUtil.valid(config);
        Long domainId = config.getDomainId() == null ? BaseConst.DEFAULT_DOMAIN_ID: config.getDomainId();
        String treeName = config.getName();
        List<Long> clsIds = config.getCiClsIds();
        List<String> attrNames = config.getLevelAttrNames();
        List<String> attrStdNames = new ArrayList<>();
        Map<String, List<Object>> attrStdNameValuesMap = new HashMap<>();
        attrNames.forEach(name -> {
            String stdName = name.toUpperCase();
            attrStdNameValuesMap.put(stdName, new ArrayList<>());
            clsIds.forEach(clsId -> {
                ESAttrAggBean queryAttr = new ESAttrAggBean();
                queryAttr.setAttrName(name);
                queryAttr.setClassId(clsId);
                queryAttr.setPageSize(99999);
                List<Object> values = esciSvc.queryAttrValObj(domainId, queryAttr).getData();
                attrStdNameValuesMap.get(stdName).addAll(values);
                // Map<String, Long> groupCountRes =
                // esciSvc.groupByCountField("attrs." + stdName + ".keyword",
                // QueryBuilders.termQuery("classId", clsId));
                // attrStdNameValuesMap.get(stdName).addAll(groupCountRes.keySet());
            });
            attrStdNameValuesMap.put(stdName, new ArrayList<>(new HashSet<>(attrStdNameValuesMap.get(stdName))));
            attrStdNames.add(stdName);
        });
        /// 组装属性树，再遍历最后一层挂ci
        // 当前层级
        int level = 0;
        CITreeNode tree = CITreeNode.builder().nodeName(treeName).level(level).build();
        // 上一级节点信息-当拼装完后这里就是最后一级的节点信息
        List<CITreeNode> beforeLevelNodes = new LinkedList<>();
        beforeLevelNodes.add(tree);
        // 一层一层的拼装
        for (int i = 0; i < attrStdNames.size(); i++) {
            List<CITreeNode> currentLevelNodes = new LinkedList<>();
            level++;
            String attrStdName = attrStdNames.get(i);
            List<Object> currentAttrVals = attrStdNameValuesMap.get(attrStdName);
            for (Object attrVal : currentAttrVals) {
                for (CITreeNode beforeLevelNode : beforeLevelNodes) {
                    CITreeNode node = CITreeNode.builder().nodeName(String.valueOf(attrVal)).nodeVal(attrVal)
                            .attrStdName(attrStdNames.get(level - 1)).level(level).parent(beforeLevelNode).build();
                    beforeLevelNode.getChildren().add(node);
                    currentLevelNodes.add(node);
                }
            }
            beforeLevelNodes = currentLevelNodes;
        }
        // 拼装完毕，挂载ci
        for (CITreeNode node : beforeLevelNodes) {
            BoolQueryBuilder query = QueryBuilders.boolQuery();
            Map<String, Object> queryMap = new HashMap<>();
            CITreeNode.buildQueryMap(queryMap, node);
            // attrConfigSvc.qu
            clsIds.forEach(clsId -> {
                BoolQueryBuilder childQuery = QueryBuilders.boolQuery();
                childQuery.must(QueryBuilders.termQuery("domainId", domainId));
                BoolQueryBuilder attrConfigQuery = QueryBuilders.boolQuery();
                attrConfigQuery.must(QueryBuilders.termQuery("classId", clsId));
                attrConfigQuery.must(QueryBuilders.termsQuery("sourceAttrName.keyword", queryMap.keySet()));
                List<ESCIAttrTransConfig> attrConfigs = attrConfigSvc.getListByQuery(attrConfigQuery);
                Map<String, ESCIAttrTransConfig> oldAttrConfigMap = new HashMap<>();
                attrConfigs.forEach(
                        attrConfig -> oldAttrConfigMap.put(attrConfig.getSourceAttrName().toUpperCase(), attrConfig));
                queryMap.forEach((attrStdName, attrVal) -> {
                    String targetName = oldAttrConfigMap.get(attrStdName) != null
                            ? oldAttrConfigMap.get(attrStdName).getTargetAttrName() : attrStdName;
                    if (attrVal instanceof String) {
                        childQuery.must(QueryBuilders.termQuery("attrs." + targetName + ".keyword", attrVal));
                    } else if (attrVal instanceof Number) {
                        childQuery.must(QueryBuilders.termQuery("attrs." + targetName, attrVal));
                    }
                });
                childQuery.must(QueryBuilders.termQuery("classId", clsId));
                query.should(childQuery);
            });
            Page<ESCIInfo> ciPageRes = null;
            if (returnAllCI) {
                ciPageRes = esciSvc.getListByQuery(1, 99999, query);
            } else {
                ciPageRes = esciSvc.getListByQuery(1, 60, query);
            }
            CITreeNode.recordCiNum(ciPageRes.getTotalRows(), node);
            node.setCiPageInfo(ciPageRes);
        }
        // 如果需要去除空节点这里移除
        if (!hasNullNode) {
            this.removeZeroNode(tree);
        }
        CITreeNode.ignoreColumn(tree);
        return tree;
    }

    /**
     * 移除ci数量为0的节点
     * 
     * @param node
     */
    private void removeZeroNode(CITreeNode node) {
        if (node.getLevel() == 0 && node.getCiNum() <= 0L) { return; }
        if (node.getChildren() != null && node.getChildren().size() > 0) {
            node.getChildren().removeIf(validNode -> validNode.getCiNum() <= 0);
        }
        if (node.getChildren() != null && node.getChildren().size() > 0) {
            node.getChildren().iterator().forEachRemaining(nextRemNode -> removeZeroNode(nextRemNode));
        }
    }
}
