package com.uinnova.product.eam.model.dto;

import com.alibaba.fastjson.JSONArray;
import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 数据超市表格请求接收类
 * <AUTHOR>
 * @date 2022/4/6
 */
@Data
public class EamTableFriendDto {
    @Comment("数据超市id")
    @NotNull(message = "id不能为空")
    private Long dataSetId;

    @Comment("数据超市sheet页Id")
    private String sheetId;

    @Comment("数据超市根节点id")
    @NotNull(message = "startCiId不能为空")
    private Long startCiId;

    @Comment("排序字段")
    private String sortCol;

    @Comment("是否降序")
    private Boolean isDesc = false;

    @Comment("查询条件")
    private JSONArray condition;
}
