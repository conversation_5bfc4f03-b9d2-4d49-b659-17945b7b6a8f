package com.uino.bean.cmdb.business.dataset;

import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uino.bean.cmdb.base.ESCIRltInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @Classname FriendInfo
 * @Description TODO
 * @Date 2020/6/3 11:25
 * <AUTHOR> sh
 */
@ApiModel(value="查询数据类",description = "查询数据信息")
public class FriendInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value="ci分类")
    private List<CcCiClassInfo> ciClassInfos;

    //节点上的Ci,value值为ciid
    @ApiModelProperty(value="节点上的Ci,value值为ci的id")
    private Map<Long, Set<Long>> ciIdByNodeMap;

    @ApiModelProperty(value = "ci节点")
    private List<CcCiInfo> ciNodes;

    @ApiModelProperty(value="ci关系线集合")
    private List<ESCIRltInfo> ciRltLines;

    public List<CcCiClassInfo> getCiClassInfos() {
        return ciClassInfos;
    }

    public void setCiClassInfos(List<CcCiClassInfo> ciClassInfos) {
        this.ciClassInfos = ciClassInfos;
    }

    public Map<Long, Set<Long>> getCiIdByNodeMap() {
        return ciIdByNodeMap;
    }

    public void setCiIdByNodeMap(Map<Long, Set<Long>> ciIdByNodeMap) {
        this.ciIdByNodeMap = ciIdByNodeMap;
    }

    public List<CcCiInfo> getCiNodes() {
        return ciNodes;
    }

    public void setCiNodes(List<CcCiInfo> ciNodes) {
        this.ciNodes = ciNodes;
    }

    public List<ESCIRltInfo> getCiRltLines() {
        return ciRltLines;
    }

    public void setCiRltLines(List<ESCIRltInfo> ciRltLines) {
        this.ciRltLines = ciRltLines;
    }
}
