package com.uinnova.product.vmdb.comm.util;

import com.binary.core.exception.MessageException;
import com.binary.core.util.BinaryUtils;
import com.binary.framework.Local;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.File;
import java.io.FileInputStream;
import java.util.*;

/**
 * 导出.XLSX格式Excel公共方法类
 * 
 * <AUTHOR>
 *
 */
public class ExportXlsxExcelUtil {

    /**
     * 创建SXSSFWorkbook对象,指定了缓存文件的位置(项目配置的资源临时目录)[待定]
     * 
     * @param rowAccessWindowSize
     *            指定到达多少条记录时写入文件一次(小于等于0时默认值200)
     * @return SXSSFWorkbook对象
     */
    public static SXSSFWorkbook createSXSSFWorkbook(int rowAccessWindowSize) {
        SXSSFWorkbook swb = null;
        try {
            if (rowAccessWindowSize <= 0) {
                rowAccessWindowSize = 200;
            }
            File file = new File(Local.getTmpSpace(), BinaryUtils.getUUID());
            if (!file.exists()) {
                file.createNewFile();
            }
            // 要有现成的模板文件
            File xlsx = new File(file, "export" + CommUtil.EXCEL07_XLSX_EXTENSION);
            XSSFWorkbook xssfWorkbook = new XSSFWorkbook(new FileInputStream(xlsx));
            swb = new SXSSFWorkbook(xssfWorkbook, rowAccessWindowSize, false);
        } catch (Exception e) {
            throw new MessageException("do createSXSSFWorkbook err!", e);
        }
        return swb;
    }

    /**
     * 设置列值样式
     * 
     * @param swb
     * @param fontSize
     *            字体大小
     * @param fontColor
     *            字体颜色
     * @param isBold
     *            是否加粗
     * @return
     */
    public static Font createCellFont(SXSSFWorkbook swb, short fontSize, short fontColor, Boolean isBold) {
        Font cellFont = swb.createFont();
        cellFont.setFontName("Dialog");
        if (fontSize != 0) {
            cellFont.setFontHeightInPoints(fontSize);
        }
        if (fontColor != 0) {
            cellFont.setColor(fontColor);
        }
        cellFont.setBold(isBold);
        return cellFont;
    }

    /**
     * 创建sheet并且设置好标题行
     * 
     * @param swb
     * @param sheetName
     *            sheet名字,默认是"Empty(编号)"
     * @param titleCellValues
     *            标题内容
     * @param majorCellValue
     *            主键列值
     * @return sheet对象
     */
    public static Sheet createExcelSheetAndTitle(SXSSFWorkbook swb, String sheetName, List<String> titleCellValues, String majorCellValue) {
        if (swb == null) {
            swb = new SXSSFWorkbook(800);
        }

        if (BinaryUtils.isEmpty(sheetName)) {
            int sheets = swb.getNumberOfSheets();
            sheetName = "Empty(" + (sheets + 1) + ")";
        }
        Sheet sheet = swb.createSheet(sheetName);
        sheet.setDefaultColumnWidth(30);

        // 没有标题内容则不创建标题
        if (!BinaryUtils.isEmpty(titleCellValues)) {
            Row titleRow = sheet.createRow(0);
//            titleRow.setHeight((short) 400);

            // 设置标题公共样式
            CellStyle majorCellStyle = createExcelTitleCellStyle(swb, true);
            CellStyle cellStyle = createExcelTitleCellStyle(swb, false);
            majorCellStyle.setWrapText(true);
            cellStyle.setWrapText(true);
//            cellStyle.setAlignment(HorizontalAlignment.CENTER);
            for (int cellnum = 0; cellnum < titleCellValues.size(); cellnum++) {
                Cell cell = titleRow.createCell(cellnum);

                String value = titleCellValues.get(cellnum);

                if (!BinaryUtils.isEmpty(majorCellValue) && value.equals(majorCellValue)) {
                    cell.setCellStyle(majorCellStyle);
                } else {
                    cell.setCellStyle(cellStyle);
                }

                cell.setCellValue(value);
            }

        }

        return sheet;
    }

    /**
     * 创建sheet并且设置好标题行<br />
     * (多列主键值,一列额外主键值,额外主键放最后一列)
     * 
     * @param swb
     * @param sheetName
     *            sheet名字,默认是"Empty(编号)"
     * @param titleCellValues
     *            标题内容(含主键列和所有业务主键列)
     * @param majorCellValue
     *            主键列值
     * @param pkbCellValues
     *            业务主键列
     * @return sheet对象
     */
    public static Sheet createExcelSheetAndTitle(SXSSFWorkbook swb, String sheetName, List<String> titleCellValues, String majorCellValue, Set<String> pkbCellValues) {
        if (swb == null) {
            swb = new SXSSFWorkbook(800);
        }
        if (BinaryUtils.isEmpty(sheetName)) {
            int sheets = swb.getNumberOfSheets();
            sheetName = "Empty(" + (sheets + 1) + ")";
        }
        if (pkbCellValues == null) {
            pkbCellValues = new HashSet<String>();
        }
        Sheet sheet = swb.createSheet(sheetName);
        sheet.setDefaultColumnWidth(30);

        // 没有标题内容则不创建标题
        if (!BinaryUtils.isEmpty(titleCellValues)) {
            Row titleRow = sheet.createRow(0);
            titleRow.setHeight((short) 400);

            // 设置标题公共样式
            CellStyle majorCellStyle = createExcelTitleCellStyle(swb, true);
            CellStyle cellStyle = createExcelTitleCellStyle(swb, false);
            CellStyle pkbCellStyle = createExcelPKBTitleCellStyle(swb);

            // 标记是否已经处理过ciCode列样式
            Boolean majorStyle = false;
            for (int cellnum = 0; cellnum < titleCellValues.size(); cellnum++) {
                Cell cell = titleRow.createCell(cellnum);
                String value = titleCellValues.get(cellnum);

                if (!BinaryUtils.isEmpty(majorCellValue) && value.equals(majorCellValue) && !majorStyle) {
                    cell.setCellStyle(majorCellStyle);
                    majorStyle = true;
                } else if (pkbCellValues.contains(value)) {
                    cell.setCellStyle(pkbCellStyle);
                } else {
                    cell.setCellStyle(cellStyle);
                }
                cell.setCellValue(value);
            }
        }
        return sheet;
    }

    /**
     * 获取标题列样式
     * 
     * @param swb
     * @param isMajor 是否ciCode
     *            是否主键列(主键列字体加粗置黄)
     * @return
     */
    public static CellStyle createExcelTitleCellStyle(SXSSFWorkbook swb, boolean isMajor) {
        Font cellFont = null;
        if (isMajor) {
            cellFont = createCellFont(swb, (short) 10, (short) 0, true);
        } else {
            cellFont = createCellFont(swb, (short) 10, (short) 0, false);
        }
        CellStyle cellStyle = swb.createCellStyle();
        cellStyle.setFont(cellFont);
        if(isMajor) {
        	cellStyle.setFillForegroundColor(IndexedColors.RED.index);
        } else {
        	cellStyle.setFillForegroundColor(IndexedColors.LIGHT_CORNFLOWER_BLUE.index);
        }
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);
        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        
        return cellStyle;
    }

    /**
     * 获取业务主键标题列样式
     * 
     * @param swb
     * @param isMajor
     *            是否主键列(主键列字体加粗置红)
     * @return
     */
    public static CellStyle createExcelPKBTitleCellStyle(SXSSFWorkbook swb) {
        Font cellFont = createCellFont(swb, (short) 10, (short) 0, true);
        CellStyle cellStyle = swb.createCellStyle();
        cellStyle.setFont(cellFont);

        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);
        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        cellStyle.setFillForegroundColor(IndexedColors.YELLOW.index);
        return cellStyle;
    }

    /**
     * 获取正文列样式
     * 
     * @param swb
     * @param fontColor
     *            字体颜色
     * @param isMajor
     *            是否主键列
     * @return
     */
    public static CellStyle createExcelCellStyle(SXSSFWorkbook swb, boolean isMajor) {
        Font cellFont = null;
        if (isMajor) {
            cellFont = createCellFont(swb, (short) 10, (short) 0, true);
        } else {
            cellFont = createCellFont(swb, (short) 10, (short) 0, false);
        }
        CellStyle cellStyle = swb.createCellStyle();
        cellStyle.setFont(cellFont);

        cellStyle.setAlignment(HorizontalAlignment.LEFT);
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);
        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setWrapText(true);
        return cellStyle;
    }

    /**
     * 获取业务主键正文列样式
     * 
     * @param swb
     * @param fontColor
     *            字体颜色 是否主键列
     * @return
     */
    public static CellStyle createExcelPKBCellStyle(SXSSFWorkbook swb) {
        Font cellFont = createCellFont(swb, (short) 10, (short) 0, true);
        CellStyle cellStyle = swb.createCellStyle();
        cellStyle.setFont(cellFont);

        cellStyle.setAlignment(HorizontalAlignment.LEFT);
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);
        cellStyle.setBorderTop(BorderStyle.THIN);
        return cellStyle;
    }

    /**
     * 将正文值集合中的Map的Key统一转成大写
     * 
     * @param commentValueMaps
     *            正文值结合
     * @return 新的正文值集合
     */
    @SuppressWarnings("rawtypes")
    public static List<Map<String, String>> commentValueMapKeyToUpperCase(List<Map<String, String>> commentValueMaps) {
        List<Map<String, String>> valueMaps = new ArrayList<Map<String, String>>();
        if (commentValueMaps == null || commentValueMaps.size() == 0) {
            return valueMaps;
        }

        for (Map<String, String> map : commentValueMaps) {
            if (map == null || map.isEmpty()) {
                continue;
            }

            Map<String, String> mapNew = new HashMap<String, String>();
            Iterator iter = map.entrySet().iterator();
            while (iter.hasNext()) {
                Map.Entry entry = (Map.Entry) iter.next();
                Object key = entry.getKey();
                Object val = entry.getValue();

                String keyNew = null;
                String valNew = "";
                if (!BinaryUtils.isEmpty(key)) {
                    keyNew = key.toString().toUpperCase();
                }
                if (!BinaryUtils.isEmpty(val)) {
                    valNew = val.toString();
                }
                if (!BinaryUtils.isEmpty(keyNew)) {
                    mapNew.put(keyNew, valNew);
                }
            }
            if (!mapNew.isEmpty()) {
                valueMaps.add(mapNew);
            }
        }

        return valueMaps;
    }

    /**
     * 写入正文内容
     * 
     * @param swb
     * @param sheet
     * @param writeTotalRows
     *            已经写入的总行数,也是开始写入行数
     * @param titleCellValues
     *            标题列值集合
     * @param majorCellValue
     *            主键列标题值
     * @param commentValueMaps
     *            正文Map集合:Map的Key必须是大写(值长度,不得超过最大最大值(16000),超过则不导入真实值)
     * @return 写入的总行数
     */
    public static Integer writeExcelComment(SXSSFWorkbook swb, Sheet sheet, int writeTotalRows, List<String> titleCellValues, String majorCellValue, List<Map<String, String>> commentValueMaps) {
        // 将集合中的Map的key转为大写
        List<Map<String, String>> commentValueNewMaps = commentValueMapKeyToUpperCase(commentValueMaps);
        // 内容和标题为空则不执行
        if (!BinaryUtils.isEmpty(titleCellValues) && commentValueNewMaps != null && commentValueNewMaps.size() > 0) {
            // 获取标题列总数
            int totalCellNum = titleCellValues.size();
            // 获取本次写入总行数
            int totalSize = commentValueNewMaps.size();
            // 计算写入截止行
            int writeEndRows = writeTotalRows + totalSize;

            // 设置正文公共样式
            CellStyle majorCellStyle = createExcelCellStyle(swb, true);
            CellStyle cellStyle = createExcelCellStyle(swb, false);

            // 记录取正文值的索引
            int rowIndex = 0;
            for (int rownum = writeTotalRows; rownum < writeEndRows; rownum++) {
                Row row = sheet.createRow(rownum);
                row.setHeight((short) 300);

                // 避免数组下标越界
                if (rowIndex == totalSize) {
                    break;
                }

                Map<String, String> attrs = commentValueNewMaps.get(rowIndex);
                rowIndex++;
                if (attrs == null || attrs.isEmpty()) {
                    continue;
                }

                for (int column = 0; column < totalCellNum; column++) {
                    Cell cell = row.createCell(column);

                    String key = titleCellValues.get(column).toUpperCase();
                    String value = attrs.get(key);

                    // 判断长度,不得超过最大最大值(32767),超过则不导入真实值,本系统取16000
                    if (!BinaryUtils.isEmpty(value) && value.length() > 16000) {
                        value = "（字数超过列的极限，无法导出!）";
                        cell.setCellStyle(majorCellStyle);
                    } else {
                        // 字数正常
                        // 设置单列字体样式
                        if (!BinaryUtils.isEmpty(majorCellValue) && key.equals(majorCellValue.toUpperCase())) {
                            cell.setCellStyle(majorCellStyle);
                        } else {
                            cell.setCellStyle(cellStyle);
                        }
                    }

                    cell.setCellValue(value);
                }

                writeTotalRows++;
            }

        }

        return writeTotalRows;

    }

    /**
     * 写入正文内容
     * 
     * @param swb
     * @param sheet
     * @param writeTotalRows
     *            已经写入的总行数,也是开始写入行数
     * @param titleCellValues
     *            标题列值集合
     * @param majorCellValue
     *            主键列标题值(可选)
     * @param pkbCellValues
     *            业务主键值(可选)
     * @param commentValueMaps
     *            正文Map集合:Map的Key必须是大写(值长度,不得超过最大最大值(16000),超过则不导入真实值)
     * @return 写入的总行数
     */
    public static Integer writeExcelComment(SXSSFWorkbook swb, Sheet sheet, int writeTotalRows, List<String> titleCellValues, String majorCellValue, Set<String> pkbCellValues, List<Map<String, String>> commentValueMaps) {
        Set<String> stdPkbCellValues = new HashSet<String>();
        if (pkbCellValues != null && !pkbCellValues.isEmpty()) {
            // 将业务主键属性转成大写
            for (String pkbkey : pkbCellValues) {
                stdPkbCellValues.add(pkbkey.toUpperCase());
            }
        }
        // 将集合中的Map的key转为大写
        List<Map<String, String>> commentValueNewMaps = commentValueMapKeyToUpperCase(commentValueMaps);
        // 内容和标题为空则不执行
        if (!BinaryUtils.isEmpty(titleCellValues) && commentValueNewMaps != null && commentValueNewMaps.size() > 0) {
            // 获取标题列总数
            int totalCellNum = titleCellValues.size();
            // 获取本次写入总行数
            int totalSize = commentValueNewMaps.size();
            // 计算写入截止行
            int writeEndRows = writeTotalRows + totalSize;

            // 设置正文公共样式
            CellStyle majorCellStyle = createExcelCellStyle(swb, true);
            CellStyle cellStyle = createExcelCellStyle(swb, false);
            CellStyle pkbCellStyle = createExcelPKBCellStyle(swb);

            // 记录取正文值的索引
            int rowIndex = 0;
            for (int rownum = writeTotalRows; rownum < writeEndRows; rownum++) {
                Row row = sheet.createRow(rownum);
                row.setHeight((short) 300);

                // 避免数组下标越界
                if (rowIndex == totalSize) {
                    break;
                }
                Map<String, String> attrs = commentValueNewMaps.get(rowIndex);
                rowIndex++;
                if (attrs == null || attrs.isEmpty()) {
                    continue;
                }

                for (int column = 0; column < totalCellNum; column++) {
                    Cell cell = row.createCell(column);
                    String key = titleCellValues.get(column).toUpperCase();
                    String value = attrs.get(key);

                    // 判断长度,不得超过最大最大值(32767),超过则不导入真实值,本系统取16000
                    if (!BinaryUtils.isEmpty(value) && value.length() > 16000) {
                        value = "（字数超过列的极限，无法导出!）";
                        cell.setCellStyle(majorCellStyle);
                    } else {
                        // 字数正常
                        // 设置单列字体样式
                        if (!BinaryUtils.isEmpty(majorCellValue) && key.equals(majorCellValue.toUpperCase())) {
                            cell.setCellStyle(majorCellStyle);
                        } else if (stdPkbCellValues.contains(key)) {
                            cell.setCellStyle(pkbCellStyle);
                        } else {
                            cell.setCellStyle(cellStyle);
                        }
                    }
                    cell.setCellValue(value);
                }
                writeTotalRows++;
            }

        }
        return writeTotalRows;
    }

    /**
     * 有序写入正文列值内容
     * 
     * @param swb
     * @param sheet
     * @param writeTotalRows
     *            已经写入的总行数,也是开始写入行数
     * @param titleCellValues
     *            标题列值集合
     * @param majorCellValue
     *            主键列标题值(可选)
     * @param pkbCellValues
     *            业务主键值(可选)
     * @param commentValues
     *            正文List集合:每一项为字符串数组,长度必须与总列数一致,否则忽略(值长度,不得超过最大最大值(16000),超过则不导入真实值)
     * @return 写入的总行数
     */
    public static Integer writeExcelArrayComment(SXSSFWorkbook swb, Sheet sheet, int writeTotalRows, List<String> titleCellValues, String majorCellValue, Set<String> pkbCellValues, List<String[]> commentValues) {
        Set<String> stdPkbCellValues = new HashSet<String>();
        if (pkbCellValues != null && !pkbCellValues.isEmpty()) {
            // 将业务主键属性转成大写
            for (String pkbkey : pkbCellValues) {
                stdPkbCellValues.add(pkbkey.toUpperCase());
            }
        }
        // 内容和标题为空则不执行
        if (!BinaryUtils.isEmpty(titleCellValues) && commentValues != null && commentValues.size() > 0) {
            // 获取标题列总数
            int totalCellNum = titleCellValues.size();
            // 获取本次写入总行数
            int totalSize = commentValues.size();
            // 计算写入截止行
            int writeEndRows = writeTotalRows + totalSize;

            // 设置正文公共样式
            CellStyle majorCellStyle = createExcelCellStyle(swb, true);
            CellStyle cellStyle = createExcelCellStyle(swb, false);
            CellStyle pkbCellStyle = createExcelPKBCellStyle(swb);

            majorCellStyle.setWrapText(true);
            cellStyle.setWrapText(true);
            pkbCellStyle.setWrapText(true);
//            cellStyle.setAlignment(HorizontalAlignment.CENTER);

            // 记录取正文值的索引
            int rowIndex = 0;
            for (int rownum = writeTotalRows; rownum < writeEndRows; rownum++) {
                Row row = sheet.createRow(rownum);
//                row.setHeight((short) 300);

                // 避免数组下标越界
                if (rowIndex == totalSize) {
                    break;
                }
                String[] attrs = commentValues.get(rowIndex);
                rowIndex++;
                if (attrs == null || attrs.length == 0 || attrs.length < totalCellNum) {
                    continue;
                }

                for (int column = 0; column < totalCellNum; column++) {
                    Cell cell = row.createCell(column);
                    String key = titleCellValues.get(column).toUpperCase();
                    String value = attrs[column];

                    // 判断长度,不得超过最大最大值(32767),超过则不导入真实值,本系统取16000
                    if (!BinaryUtils.isEmpty(value) && value.length() > 16000) {
                        value = "（字数超过列的极限，无法导出!）";
                        cell.setCellStyle(majorCellStyle);
                    } else {
                        // 字数正常
                        // 设置单列字体样式
                        if (!BinaryUtils.isEmpty(majorCellValue) && key.equals(majorCellValue.toUpperCase())) {
                            cell.setCellStyle(majorCellStyle);
                        } else if (stdPkbCellValues.contains(key)) {
                            cell.setCellStyle(pkbCellStyle);
                        } else {
                            cell.setCellStyle(cellStyle);
                        }
                    }
                    cell.setCellValue(value);
                }
                writeTotalRows++;
            }

        }
        return writeTotalRows;
    }

}
