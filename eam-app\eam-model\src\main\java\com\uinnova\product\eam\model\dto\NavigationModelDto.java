package com.uinnova.product.eam.model.dto;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

import java.util.List;

/**
 * 模型导航栏信息
 */
@Data
public class NavigationModelDto {

    @Comment("模型名称")
    private String modelName;

    @Comment("模型ID")
    private Long modelId;

    @Comment("层级信息")
    private List<EamHierarchyNavigationDto> eamHierarchyDtos;

    @Comment("模型修改时间")
    private Long modifyTime;
}
