package com.uinnova.product.eam.model.bm;

import com.uino.bean.cmdb.base.ESCIInfo;
import com.uino.bean.cmdb.base.SaveBatchCIContext;

/**
 * <AUTHOR>
 */
public class SavePrivateBatchCIContext extends SaveBatchCIContext {

    private Integer state;

    private String diagramId;

    public SavePrivateBatchCIContext(String ciCode, Long classId, ESCIInfo esCi) {
        super(ciCode, classId, esCi);
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public String getDiagramId() {
        return diagramId;
    }

    public void setDiagramId(String diagramId) {
        this.diagramId = diagramId;
    }

}
