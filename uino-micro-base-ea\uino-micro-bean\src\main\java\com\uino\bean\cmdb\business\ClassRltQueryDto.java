package com.uino.bean.cmdb.business;

import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value="分类关系查询类",description = "分类关系查询信息")
public class ClassRltQueryDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 仅当前模型
     * <p>
     * 暂时只存储了当前模型，为了方便扩展先留下此条件
     */
    @ApiModelProperty(value="仅当前模型",example = "true")
    private Boolean onlyCurrentModel;

    /**
     * 页码
     */
    @ApiModelProperty(value="页码",example = "1")
    @Builder.Default
    private int pageNum = 1;

    /**
     * 每页行数
     */
    @ApiModelProperty(value="每页行数",example="20")
    @Builder.Default
    private int pageSize = 20;

    /**
     * 排序字段
     */
    @ApiModelProperty(value="排序字段",example="id")
    @Builder.Default
    private String order = "id";

    /**
     * 是否升序
     */
    @ApiModelProperty(value="是否升序",example="true")
    private boolean asc;

    /**
     * 源分类id
     */
    @ApiModelProperty(value="源分类id",example="123")
    private Long sourceClassId;

    @ApiModelProperty(value="源分类id集合")
    private List<Long> sourceClassIds;
    /**
     * 目标分类id
     */
    @ApiModelProperty(value="目标分类id",example="123")
    private Long targetClassId;

    @ApiModelProperty(value="目标分类id集合")
    private List<Long> targetClassIds;

	@ApiModelProperty(value = "关系分类id")
	private Long rltClassId;

	@ApiModelProperty(value="关系分类id集合")
	private List<Long> rltClassIds;
	
	@ApiModelProperty(value = "源或目标分类id")
	private Long ciClassId;

    @ApiModelProperty(value = "所属域")
	private Long domainId;
}
