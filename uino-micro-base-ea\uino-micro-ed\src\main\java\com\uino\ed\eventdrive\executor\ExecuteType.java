package com.uino.ed.eventdrive.executor;

public enum  ExecuteType {
    /**运行模式，同步*/
    RM_SYNC(0,"同步执行"),
    /**运行模式，异步*/
    RM_ASYN(1,"异步执行"),
    /**运行模式，并发*/
    RM_CONC(2,"并发执行"),
    /**运行模式，异步并发*/
    RM_ASCO(3,"异步并发");

    private int code;
    private String description;
    ExecuteType(int code,String description){
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
