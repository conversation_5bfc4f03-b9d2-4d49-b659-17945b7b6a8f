package com.uinnova.product.eam.model;

import java.io.Serializable;
import java.util.List;

import com.binary.framework.bean.annotation.Comment;
import com.uinnova.product.eam.comm.model.VcDiagramCiAttrVersion;
import com.uinnova.product.eam.comm.model.VcDiagramEleVersion;
import com.uinnova.product.eam.comm.model.VcDiagramGroup;
import com.uinnova.product.eam.comm.model.VcDiagramTag;
import com.uinnova.product.eam.comm.model.VcDiagramVersion;
import com.uinnova.product.eam.comm.model.VcGroup;
import com.uinnova.product.eam.comm.model.VcTag;

public class VcDiagramVersionInfo implements Serializable {
	private static final long serialVersionUID = 1L;

	@Comment("历史视图")
	private VcDiagramVersion diagramVersion;
	
	@Comment("历史视图元素关系")
	private List<VcDiagramEleVersion> diagramEleVersions;
		
	@Comment("历史视图显示属性关系")
	private List<VcDiagramCiAttrVersion> ciAttrVersions;
	
	@Comment("视图标签关系")
	private List<VcDiagramTag> diagramTags;
	
	@Comment("标签")
	private List<VcTag> tags;
	
	@Comment("视图组关系")
	private List<VcDiagramGroup> diagramGroups;

	@Comment("视图组信息")
	private List<VcGroup> groups;
	
	@Comment("xml信息")
	private String xml;
	
	@Comment("svg信息")
	private String svg;

	@Comment("ci3dPoint")
	private String ci3dPoint;
	
	@Comment("ci 3d DMV坐标信息")
	private String ci3dPoint2;
	
	@Comment("更新的关系数据")
	private List<String> upRelations;
	
	@Comment("视图json信息")
	private String json;
	
	public String getJson() {
		return json;
	}

	public void setJson(String json) {
		this.json = json;
	}

	public String getCi3dPoint2() {
		return ci3dPoint2;
	}

	public void setCi3dPoint2(String ci3dPoint2) {
		this.ci3dPoint2 = ci3dPoint2;
	}

	public List<String> getUpRelations() {
		return upRelations;
	}

	public void setUpRelations(List<String> upRelations) {
		this.upRelations = upRelations;
	}
	
	public VcDiagramVersion getDiagramVersion() {
		return diagramVersion;
	}

	public void setDiagramVersion(VcDiagramVersion diagramVersion) {
		this.diagramVersion = diagramVersion;
	}

	public List<VcDiagramEleVersion> getDiagramEleVersions() {
		return diagramEleVersions;
	}

	public void setDiagramEleVersions(List<VcDiagramEleVersion> diagramEleVersions) {
		this.diagramEleVersions = diagramEleVersions;
	}

	public List<VcDiagramCiAttrVersion> getCiAttrVersions() {
		return ciAttrVersions;
	}

	public void setCiAttrVersions(List<VcDiagramCiAttrVersion> ciAttrVersions) {
		this.ciAttrVersions = ciAttrVersions;
	}

	public List<VcDiagramTag> getDiagramTags() {
		return diagramTags;
	}

	public void setDiagramTags(List<VcDiagramTag> diagramTags) {
		this.diagramTags = diagramTags;
	}

	public List<VcTag> getTags() {
		return tags;
	}

	public void setTags(List<VcTag> tags) {
		this.tags = tags;
	}

	public List<VcDiagramGroup> getDiagramGroups() {
		return diagramGroups;
	}

	public void setDiagramGroups(List<VcDiagramGroup> diagramGroups) {
		this.diagramGroups = diagramGroups;
	}

	public List<VcGroup> getGroups() {
		return groups;
	}

	public void setGroups(List<VcGroup> groups) {
		this.groups = groups;
	}

	public String getXml() {
		return xml;
	}

	public void setXml(String xml) {
		this.xml = xml;
	}

	public String getSvg() {
		return svg;
	}

	public void setSvg(String svg) {
		this.svg = svg;
	}

	public String getCi3dPoint() {
		return ci3dPoint;
	}

	public void setCi3dPoint(String ci3dPoint) {
		this.ci3dPoint = ci3dPoint;
	}
	
	
	
}
