package com.uino.cmdb.resource.svc;

import java.util.List;

import org.elasticsearch.index.query.QueryBuilders;

import com.uino.dao.sys.ESResourceSvc;
import com.uino.bean.cmdb.base.ESResource;

//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = { StartBaseWebAppliaction.class }, webEnvironment = WebEnvironment.RANDOM_PORT)
//@Slf4j
//@ActiveProfiles("test")
//@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class UpdatePublicUrlTest {
	// @Autowired
	private ESResourceSvc svc;

	// @Test
	public void test() {
		String url = "http://192.168.21.86/122/100000000000002.png";
		List<ESResource> rs = svc.getListByQuery(QueryBuilders.boolQuery());

		for (ESResource r : rs) {
			r.setPublicUrl(url);
		}
		svc.saveOrUpdateBatch(rs);
	}
}
