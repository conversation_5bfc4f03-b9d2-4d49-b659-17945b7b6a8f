package com.uino.provider.feign.cmdb;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.binary.jdbc.Page;
import com.uino.bean.cmdb.base.ESCiClassRlt;
import com.uino.bean.cmdb.business.BindCIClassRltDto;
import com.uino.bean.cmdb.business.ClassRltQueryDto;
import com.uino.provider.feign.config.BaseFeignConfig;

@Component
@FeignClient(name = "${feign-server-name:tarsier-platform}", path = "${feign-server-path:tarsier-platform}/feign/ciClassRlt", configuration = {BaseFeignConfig.class})
public interface CIClassRltFeign {

    /**
     * 分页查询分类间关系
     * 
     * @param queryDto
     *            查询条件
     * @return 分页查询结果
     */
    @PostMapping("queryClassRltPage")
    public Page<ESCiClassRlt> queryClassRltPage(@RequestBody(required = false) ClassRltQueryDto queryDto);

    /**
     * 不分页查询分类间关系
     * 
     * @param queryDto
     *            查询条件
     * @return 查询结果
     */
    @PostMapping("queryClassRlt")
    public List<ESCiClassRlt> queryClassRlt(@RequestBody(required = false) ClassRltQueryDto queryDto);

    @PostMapping("existRlt")
    public boolean existRlt(@RequestParam(required = false, name = "sourceClsId") long sourceClsId,
            @RequestParam(required = false, name = "targetClsId") long targetClsId,
            @RequestParam(required = false, name = "clsId") long clsId,
            @RequestParam(required = false, name = "onlyCurrentModel") boolean onlyCurrentModel);

	/**
	 * 保存分类关系-增量
	 * 
	 * @param classRlts
	 * @return
	 */
	@PostMapping("saveRlts")
	public Integer saveRlts(@RequestParam(value = "domainId") Long domainId, @RequestBody List<ESCiClassRlt> classRlts);

	/**
	 * 为指定的源分类保存分类关系-全量 <br>
	 * 源分类的所有分类关系会先清除再保存
	 * 
	 * @param dto
	 * @return
	 */
	@PostMapping("saveRltsForClass")
	public Integer saveRltsForClass(@RequestBody BindCIClassRltDto dto);

	/**
	 * 刪除分类关系（classId可传关系分类id/源分类id/目标分类id）
	 * 
	 * @return
	 */
	@PostMapping("deleteRltsByClassId")
	public Integer deleteRltsByClassId(Long classId);

	/**
	 * 删除分类关系
	 * 
	 * @param rlt
	 * @return
	 */
	@PostMapping("deleteClassRlt")
	public Integer deleteClassRlt(@RequestBody ESCiClassRlt rlt);
}
