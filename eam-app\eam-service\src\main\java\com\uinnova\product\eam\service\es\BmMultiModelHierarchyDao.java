package com.uinnova.product.eam.service.es;

import com.uinnova.product.eam.comm.model.es.CEamMultiModelHierarchy;
import com.uinnova.product.eam.comm.model.es.EamMultiModelHierarchy;
import com.uino.dao.AbstractESBaseDao;
import com.uino.service.util.FileUtil;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.util.List;

/**
 * 多模型层级
 *
 * <AUTHOR>
 */
@Service
public class BmMultiModelHierarchyDao extends AbstractESBaseDao<EamMultiModelHierarchy, CEamMultiModelHierarchy> {
    @Override
    public String getIndex() {
        return "uino_eam_multi_model_hierarchy";
    }

    @Override
    public String getType() {
        return "_doc";
    }

    @PostConstruct
    public void init() {
//        List<EamMultiModelHierarchy> list = FileUtil.getData("/initdata/uino_eam_multi_model_hierarchy.json", EamMultiModelHierarchy.class);
        super.initIndex();
    }
}
