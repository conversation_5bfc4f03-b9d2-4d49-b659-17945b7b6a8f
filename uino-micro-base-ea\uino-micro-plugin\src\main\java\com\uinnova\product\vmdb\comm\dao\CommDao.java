package com.uinnova.product.vmdb.comm.dao;

import com.binary.framework.bean.Condition;
import com.binary.framework.bean.EntityBean;
import com.binary.framework.dao.Dao;

import java.util.List;


/**
 * 
 * <AUTHOR>
 *
 */
public interface CommDao {

    /**
     * 更新树形数据级别信息(层级级别、层级路径、是否末级)
     * 
     * @param nodeId
     *            当前节点ID
     * @param oldParentId
     *            更新之前父级节点ID
     * @param newParentId
     *            更新之后父级节点ID
     * @param tableName
     *            树形数据对应表名
     * @param parentFieldName
     *            父级节点字段名 (数据库表字段名)
     * @param levelFieldName
     *            层级级别字段名 (数据库表字段名)
     * @param pathFieldName
     *            层级路径字段名 (数据库表字段名)
     * @param leafFieldName
     *            是否末级字段名 (数据库表字段名)
     * @param hasDataStatus
     *            是否含有DATA_STATUS字段
     */
    void updateTreeLevel(Long nodeId, Long oldParentId, Long newParentId, String tableName, String parentFieldName, String levelFieldName, String pathFieldName, String leafFieldName, boolean hasDataStatus);

    /**
     * 获取树形数据指定节点所有上级节点数据
     * 
     * @param dao
     *            对应DAO
     * @param nodeId
     *            指定节点
     * @param parentName
     *            上级节点字段名 (实体对像字段名)
     * @return 返回上级节点列表, 最顶级节点为列表每1个元素, 当前节点为最后一个元素
     */
    <T extends EntityBean, F extends Condition> List<T> getTreeAllParents(Dao<T, F> dao, Long nodeId, String parentName);

}
