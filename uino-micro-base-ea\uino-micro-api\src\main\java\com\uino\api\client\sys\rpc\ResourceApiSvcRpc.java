package com.uino.api.client.sys.rpc;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.uino.bean.cmdb.base.ESResource;
import com.uino.provider.feign.sys.ResourceFeign;
import com.uino.api.client.sys.IResourceApiSvc;

@Service
public class ResourceApiSvcRpc implements IResourceApiSvc {

    @Autowired
    private ResourceFeign svc;

    @Override
    public String saveSyncResourceInfo(String path, String publicUrl, boolean unzip, Integer optionType) {
        // TODO Auto-generated method stub
        return svc.saveSyncResourceInfo(path, publicUrl, unzip, optionType);
    }

    @Override
    public String saveSyncResourceInfo(String path, String publicUrl, boolean unzip, boolean currentDir,
            Integer optionType) {
        // TODO Auto-generated method stub
        return svc.saveSyncResourceInfo(path, publicUrl, unzip, currentDir, optionType);
    }

    @Override
    public void saveSyncResourceInfo(List<ESResource> saveDtos) {
        // TODO Auto-generated method stub
        svc.saveSyncResourceInfo(saveDtos);
    }

    @Override
    public List<ESResource> getWaitSyncResources(Long createTime) {
        // TODO Auto-generated method stub
        return svc.getWaitSyncResources(createTime);
    }
}
