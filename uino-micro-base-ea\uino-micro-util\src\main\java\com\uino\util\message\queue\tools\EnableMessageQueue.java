package com.uino.util.message.queue.tools;

import org.springframework.context.annotation.Condition;
import org.springframework.context.annotation.ConditionContext;
import org.springframework.core.type.AnnotatedTypeMetadata;

/**
 * Determine whether to load kafka according to whether kafka.server is configured in the configuration file
 *
 * @Author: YGQ
 * @Create: 2021-05-24 14:04
 **/
public class EnableMessageQueue implements Condition {
    @Override
    public boolean matches(ConditionContext context, AnnotatedTypeMetadata metadata) {
        boolean enableKafka = false;
        String kafkaServer = context.getEnvironment().getProperty("kafka.server");
        if (kafkaServer != null) {
            if (!"".equals(kafkaServer.trim())) {
                enableKafka = true;
            }
        }
        return enableKafka;
    }
}
