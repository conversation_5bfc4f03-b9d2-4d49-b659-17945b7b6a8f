package com.uinnova.product.eam.rpc.diagram;

import com.binary.core.io.support.ByteArrayResource;
import com.binary.framework.web.RemoteResult;
import com.binary.jdbc.Page;
import com.uinnova.product.eam.api.diagram.ESShapeApiClient;
import com.uinnova.product.eam.base.diagram.model.*;
import com.uinnova.product.eam.service.diagram.ESShapeDirSvc;
import com.uinnova.product.eam.service.diagram.ESShapeSvc;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClassDir;
import com.uinnova.product.vmdb.comm.model.image.CcImage;
import com.uino.bean.cmdb.business.ImageCount;
import com.uino.bean.cmdb.business.ImportDirMessage;
import com.uino.bean.cmdb.business.ImportResultMessage;
import com.uino.bean.cmdb.query.ESSearchImageBean;
import com.uino.bean.permission.query.SearchKeywordBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Set;


@Service
public class ESShapeSvcRpc implements ESShapeApiClient {

/*    @Autowired
    private VcShapeSvc vcShapeSvc;*/

    @Autowired
    private ESShapeSvc svc;

    @Autowired
    private ESShapeDirSvc dirSvc;

    @Override
    public Long saveOrUpdateDir(VcShapeDir dir) {
        // return vcShapeSvc.saveOrUpdateDir(dir);
        return null;
    }

    @Override
    public List<VcShapeDirInfo> queryImageDirList() {
        // return vcShapeSvc.queryImageDirList();
        return null;
    }

    @Override
    public List<ESUserShape> queryImagePage(Integer pageNum, Integer pageSize, Long dirId, String keyword) {
        return svc.queryImagePage(pageNum, pageSize, dirId, keyword);
    }

    @Override
    public RemoteResult saveOrUpdateShape(ESUserShapeDTO esUserShapeDTO) {
        return svc.saveShape(esUserShapeDTO);
    }


    public ImportResultMessage importZipImage(Integer sourceType, MultipartFile file) {
        return this.svc.importZipImage(sourceType, file);
    }

    public List<ImageCount> queryImageDirList(CCcCiClassDir cdt) {
        return this.svc.queryImageDirList(cdt);
    }

    public Page<ESUserShape> queryImagePage(ESImageBean bean) {
        return this.svc.queryImagePage(bean);
    }

    public boolean replaceImage(Long imageId, MultipartFile file) {
        return this.svc.replaceImage(imageId, file);
    }

    public boolean deleteImage(CcImage image) {
        return this.svc.deleteImage(image);
    }

    public long deleteDirImage(Long dirId) {
        return this.svc.deleteDirImage(dirId);
    }

    public boolean importImage(Long dirId, MultipartFile file) {
        return this.svc.importImage(dirId, file);
    }

    public ResponseEntity<byte[]> exportImageZipByDirIds(Set<Long> dirIds) {
        ResponseEntity<byte[]> httpResponse = this.svc.exportImageZipByDirIds(dirIds);
        String fileName = httpResponse.getHeaders().getContentDisposition().getFilename();
        new ByteArrayResource((byte[]) httpResponse.getBody(), fileName);
        return httpResponse;
    }

    public List<CcImage> queryTopImage(SearchKeywordBean bean) {
        return this.svc.queryTopImage(bean);
    }

    public ImportDirMessage import3DZipImage(MultipartFile file, Long dirId, boolean isCover) {
        return null;
    }

    public Long updateImageRlt(ESUserShape image) {
        return this.svc.updateImageRlt(image);
    }

    public boolean replace3DImage(Long imgId, MultipartFile file) {
        return this.svc.replace3DImage(imgId, file);
    }

    public boolean delete3DImage(CcImage image) {
        return this.svc.delete3DImage(image);
    }

    public CcImage queryImageById(Long id) {
        return this.svc.queryImageById(id);
    }

    public ImportDirMessage importImages(Long dirId, MultipartFile... files) {
        return this.svc.importImages(dirId, files);
    }

    public Long countBySearchBean(ESSearchImageBean bean) {
        return this.svc.countBySearchBean(bean);
    }

    public ResponseEntity<byte[]> downloadImageResource(List<Long> ids) {
        return this.svc.downloadImageResource(ids);
    }

    @Override
    public Long saveOrUpdate(ESShapeDir dir) {
        return this.dirSvc.saveOrUpdate(dir);
    }

    @Override
    public boolean sort(ESUserShapeQuery cdt) {
        return this.svc.sort(cdt);
    }

    @Override
    public List<ESUserShapeResult> queryAllImage(ESImageBean bean) {
        return this.svc.queryAllImage(bean);
    }
}
