package com.uinnova.product.vmdb.provider.sys.bean;

import com.binary.framework.bean.annotation.Comment;

import java.io.Serializable;
import java.util.List;

@Comment("组织和用户关系")
public class SysOrgUserRlt implements Serializable {

    private static final long serialVersionUID = 1L;

    @Comment("1:userIds是该组织新增的用户;2:userIds是该组织全部的用户")
    private Integer type;

    @Comment("组织ID")
    private Long orgId;

    @Comment("用户IDS")
    private List<Long> userIds;

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Long getOrgId() {
        return orgId;
    }

    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    public List<Long> getUserIds() {
        return userIds;
    }

    public void setUserIds(List<Long> userIds) {
        this.userIds = userIds;
    }

}
