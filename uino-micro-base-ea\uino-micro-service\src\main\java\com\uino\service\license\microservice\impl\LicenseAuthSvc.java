package com.uino.service.license.microservice.impl;

import com.binary.core.io.Resource;
import com.binary.core.io.ResourceResolver;
import com.binary.core.io.support.ByteArrayResource;
import com.binary.core.util.BinaryUtils;
import com.binary.framework.Local;
import com.binary.framework.exception.ServiceException;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import com.uinnova.product.vmdb.comm.license.IpAddress;
import com.uinnova.product.vmdb.comm.model.license.CCcLicenseAuthServer;
import com.uinnova.product.vmdb.comm.model.license.CcLicenseAuthServer;
import com.uino.bean.license.BaseLicenseAuth;
import com.uino.bean.license.BaseLicenseAuthInfo;
import com.uino.bean.permission.base.SysUser;
import com.uino.dao.license.ESLicenseAuthServerSvc;
import com.uino.dao.license.ESLicenseAuthSvc;
import com.uino.dao.util.ESUtil;
import com.uino.license.sdk.LicenseAuthorityAdapter;
import com.uino.license.sdk.license.License;
import com.uino.service.license.LicenseCumulativeTimer;
import com.uino.service.license.microservice.ILicenseAuthSvc;
import com.uino.util.sys.SysUtil;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.geom.RoundRectangle2D;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.List;

/**
 *
 * <AUTHOR>
 *
 */
@Service
@Slf4j
public class LicenseAuthSvc implements ILicenseAuthSvc {

    public static final Long LICENSE_AUTH_ID = 1L;

    @Autowired
    private ESLicenseAuthSvc licenseAuthSvc;

    @Autowired
    private ESLicenseAuthServerSvc licenseAuthServerSvc;

    @Autowired
    private LicenseAuthorityAdapter licenseAuthorityAdapter;

    private IpAddress address;

    public static final String PROP_SERVER_IP = "prop.license.authority.server.ip";

    private LicenseCumulativeTimer licenseCumulativeTimer;

    @Value("${integration.uinnova.cloud.registerLicense:http://cloud.uinnova.cn/dev-product-web/integration/dispatch/mc/1100203}")
    private String uinnovaCloudRegisterLicense;

    private String logoPath = "classpath:static_res/logo.png";

    @Override
    public void initialization() {
    	//初始化ThingJSAPI License
    	licenseAuthorityAdapter.init("", 0);

        // 登录当前系统信息
        BaseLicenseAuth auth = licenseAuthSvc.selectOneRow();
        long authID = licenseAuthorityAdapter.getAuthID();
        List<String> clientId = null;
        try {
            clientId = licenseAuthorityAdapter.getClientId();
        } catch (Exception e) {
            log.error("获取clientId失败",e);
        }
        String serverIp = clientId.get(0);
        String serverCode = clientId.get(1);
        CcLicenseAuthServer server = new CcLicenseAuthServer();
        server.setAuthId(authID);
        server.setServerIp(serverIp);
        server.setServerCode(serverCode);
        licenseAuthServerSvc.saveOrUpdate(server);

        // 重新生成ClientCode
        this.createClientCode();

        // 注册license
        String license = auth.getAuthCode();
        if (!BinaryUtils.isEmpty(license)) {
            try {
                licenseAuthorityAdapter.registerAuthCode(license);
            } catch (Throwable t) {
                log.error(" License authorization failed! ");
            }
        } else {
            log.error(" Product without authorization! ");
        }
        boolean licenseValid = licenseAuthorityAdapter.isValid();
        long lastUsedTime = 0;
        if ( auth.getUtTime() != null) {
            lastUsedTime = auth.getUtTime();
        }
        licenseCumulativeTimer = new LicenseCumulativeTimer(this, this.licenseAuthorityAdapter, licenseAuthSvc, lastUsedTime);

        if (licenseValid) {
            log.info(" License authorization successful. ");
        }

    }

    @Override
    public void initSelfClient() {
        // 登录当前系统信息
        BaseLicenseAuth auth = licenseAuthSvc.selectOneRow();
        List<String> clientId = null;
        try {
            clientId = licenseAuthorityAdapter.getClientId();
        } catch (Exception e) {
            log.info("获取clientId失败：",e);
        }
        String serverIp = clientId.get(0);
        String serverCode = clientId.get(1);
        CcLicenseAuthServer server = new CcLicenseAuthServer();
        server.setAuthId(auth.getId());
        server.setServerIp(serverIp);
        server.setServerCode(serverCode);
        Long saveId = licenseAuthServerSvc.saveOrUpdate(server);
        if (BinaryUtils.isEmpty(saveId)) {
            Local.commit();
        }

        // 重新生成ClientCode
        this.createClientCode();
    }

    @Override
    public BaseLicenseAuth queryLicenseAuth() {
        return licenseAuthSvc.selectOneRow();
    }

    @Override
    public BaseLicenseAuthInfo queryLicenseAuthInfo() {
        BaseLicenseAuth record = licenseAuthSvc.selectOneRow();

        CCcLicenseAuthServer cdt = new CCcLicenseAuthServer();
        cdt.setAuthId(record.getId());
        List<CcLicenseAuthServer> list = licenseAuthServerSvc.getListByCdt(cdt);

        BaseLicenseAuthInfo info = new BaseLicenseAuthInfo();
        info.setAuth(record);
        info.setServerList(list);

        String authCode = record.getAuthCode();
        Integer authEndDate = record.getAuthEndDate();

        // 状态, 1=已授权 2=未授权 3=已过期
        Integer status = 1;
        if (BinaryUtils.isEmpty(authCode) || BinaryUtils.isEmpty(authEndDate)) {
            status = 2;
        } else {
            int curr = BinaryUtils.getNumberDate();
            if (curr > authEndDate.intValue() || !licenseAuthorityAdapter.isValid()
                    || licenseCumulativeTimer.getCumulativeTime() / 60 / 24 > licenseAuthorityAdapter
                    .getEffectiveDate()) {
                status = 3;
            }
        }
        info.setStatus(status);
        return info;
    }

    @Override
    public List<CcLicenseAuthServer> queryServerList() {
        CCcLicenseAuthServer cdt = new CCcLicenseAuthServer();
        cdt.setAuthId(LICENSE_AUTH_ID);
        return licenseAuthServerSvc.getListByCdt(cdt);
    }

    @Override
    public Integer removeServer(Long serverId) {
        Assert.notNull(serverId, "BS_FIELD_EMPTY_VAL${field:serverId}");
        return licenseAuthServerSvc.deleteById(serverId);
    }

    @Override
    public String createClientCode() {
        BaseLicenseAuth record = licenseAuthSvc.selectOneRow();

        CCcLicenseAuthServer cdt = new CCcLicenseAuthServer();
        cdt.setAuthId(record.getId());
        List<CcLicenseAuthServer> list = licenseAuthServerSvc.getListByCdt(cdt);
        if (list.size() == 0) {
            throw new ServiceException(" not found install server! ");
        }
        String oldClientCode = record.getClientCode();
        String oldNativeCode = record.getCodeUser();

        ArrayList<Long> arrayList = new ArrayList<>();
        for (CcLicenseAuthServer ccLicenseAuthServer : list) {
            arrayList.add(ccLicenseAuthServer.getAuthId());
            arrayList.add(Long.valueOf(ccLicenseAuthServer.getServerCode()));
        }

        long[] clientIds = new long[list.size()*2];
        for (int i =0 ;i<arrayList.size();i++){
            clientIds[i] = arrayList.get(i);
        }
        Arrays.sort(clientIds);

        log.debug(">>>createClientCode");
        // String[] codes = licenseAuthorityAdapter.createNativeClientCode(clientIds);
        String[] codes = licenseAuthorityAdapter.createClientCode(clientIds,"Q1");
        String nativeCode = codes[0];
        String clientCode = codes[1];
        if (!BinaryUtils.isEmpty(oldClientCode) && !BinaryUtils.isEmpty(oldNativeCode)) {
            if (oldNativeCode.equals(nativeCode)) {
                return oldClientCode;
            }
        }

        BaseLicenseAuth up = new BaseLicenseAuth();
        up.setClientCode(clientCode);
        up.setCodeUser(nativeCode);
        up.setCodeTime(BinaryUtils.getNumberDateTime());
        up.setId(record.getId());
        licenseAuthSvc.saveOrUpdate(up);

        return clientCode;
    }

    @Override
    public void updateUtTime(Long utTime) {
        if (utTime == null || utTime.longValue() < 0) {
            throw new ServiceException(" is wrong utTime '" + utTime + "'! ");
        }

        BaseLicenseAuth record = new BaseLicenseAuth();
        record.setUtTime(utTime.intValue());
        record.setId(LICENSE_AUTH_ID);
        licenseAuthSvc.saveOrUpdate(record);
    }

    @Override
    public Integer updateUtTimeByAuthCode(String authCode, Long utTime) {
        Assert.notNull(authCode, "BS_FIELD_EMPTY_VAL${field:authCode}");

        if (utTime == null || utTime.longValue() < 0) {
            throw new ServiceException(" is wrong utTime '" + utTime + "'! ");
        }
        BaseLicenseAuth record = new BaseLicenseAuth();
        record.setUtTime(utTime.intValue());
        return licenseAuthSvc.updateByAuthCode(LICENSE_AUTH_ID, authCode, record);
    }

    @Override
    public void registerLicense(String authCode, String authUser) {
        Assert.notNull(authCode, "BS_FIELD_EMPTY_VAL${field:authCode}");

        authCode = authCode.trim();

        boolean ba = licenseAuthorityAdapter.verifyAuthCode(authCode);
        if (!ba) {
            throw new ServiceException("授权码不合法");
        }

        CCcLicenseAuthServer cdt = new CCcLicenseAuthServer();
        cdt.setAuthId(LICENSE_AUTH_ID);
        List<CcLicenseAuthServer> servers = licenseAuthServerSvc.getListByCdt(cdt);
        if (servers.size() == 0) {
            throw new ServiceException("服务器不存在");
        }
        if (BinaryUtils.isEmpty(authUser)) {
            try {
                SysUser sysUser = SysUtil.getCurrentUserInfo();
                authUser = sysUser == null ? "system" : sysUser.getLoginCode();
            } catch (Exception e) {
                // TODO: handle exception
            }
        }

        // 如果新注册码与旧注册码不一样, 则重置累计使用时间
        BaseLicenseAuth old = licenseAuthSvc.selectOneRow();
        String oldAuthCode = old.getAuthCode();

        BaseLicenseAuth record = new BaseLicenseAuth();
        record.setAuthCode(authCode);
        record.setAuthTime(BinaryUtils.getNumberDateTime());
        record.setAuthUser(authUser);
        if (!authCode.equals(oldAuthCode)) {
            record.setUtTime(0);
        }
        record.setId(LICENSE_AUTH_ID);
        licenseAuthSvc.saveOrUpdate(record);
        registerLicenseByDb();

        // 不用通知其他服务器, 其他服务器会定时刷license
        // registerAllServer(servers);

    }

    @Override
    public void registerLicenseByDb() {
        // 登录当前系统信息
        BaseLicenseAuth auth = licenseAuthSvc.selectOneRow();

        // 注册license
        String authCode = auth.getAuthCode();
        if (BinaryUtils.isEmpty(authCode)) {
            throw new ServiceException(" the authCode is empty! ");
        }
        // 客户端码
        String codeUser = auth.getCodeUser();
        if (BinaryUtils.isEmpty(codeUser)) {
            throw new ServiceException(" the codeUser is empty! ");
        }

        License license = null;
        license = licenseAuthorityAdapter.registerAuthCode(authCode);
        BaseLicenseAuth record = new BaseLicenseAuth();
        record.setAuthEndDate(license.getAuthEndDate());
        record.setUtTime(license.getAuthUseDays());
        record.setAuthEndDate(license.getAuthEndDate());
        record.setAuthCustom1(license.getCustom1());
        record.setAuthCustom2(license.getCustom2());
        record.setAuthCustom3(license.getCustom3());
        record.setAuthCustom4(license.getCustom4());
        record.setAuthCustom5(license.getCustom5());
        record.setAuthCustom6(license.getCustom6());
        record.setAuthCustom7(license.getCustom7());
        record.setAuthCustom8(license.getCustom8());
        record.setAuthCustom9(license.getCustom9());
        record.setAuthCustom10(license.getCustom10());
        record.setAuthCustom11(license.getCustom11());
        record.setAuthCustom12(license.getCustom12());
        record.setAuthCustom13(license.getCustom13());
        record.setAuthCustom14(license.getCustom14());
        record.setAuthCustom15(license.getCustom15());
        record.setId(LICENSE_AUTH_ID);

        record.setAuthStringCustom1(license.getStrCustom1());
        record.setAuthStringCustom2(license.getStrCustom2());
        record.setAuthStringCustom3(license.getStrCustom3());
        record.setAuthStringCustom4(license.getStrCustom4());
        record.setAuthStringCustom5(license.getStrCustom5());

        licenseAuthSvc.saveOrUpdate(record);
        this.licenseCumulativeTimer.setCumulativeTime(auth.getUtTime());
    }

    @Override
    public License getRealLicense() {
        License license = new License();

        license.setValid(licenseAuthorityAdapter.isValid());
        license.setAuthEndDate(licenseAuthorityAdapter.getEffectiveDate());
        license.setCustom1(licenseAuthorityAdapter.getCustom1());
        license.setCustom2(licenseAuthorityAdapter.getCustom2());
        license.setCustom3(licenseAuthorityAdapter.getCustom3());
        license.setCustom4(licenseAuthorityAdapter.getCustom4());
        license.setCustom5(licenseAuthorityAdapter.getCustom5());
        license.setCustom6(licenseAuthorityAdapter.getCustom6());
        license.setCustom7(licenseAuthorityAdapter.getCustom7());
        license.setCustom8(licenseAuthorityAdapter.getCustom8());
        license.setCustom9(licenseAuthorityAdapter.getCustom9());
        license.setCustom10(licenseAuthorityAdapter.getCustom10());
        license.setCustom11(licenseAuthorityAdapter.getCustom11());
        license.setCustom12(licenseAuthorityAdapter.getCustom12());
        license.setCustom13(licenseAuthorityAdapter.getCustom13());
        license.setCustom14(licenseAuthorityAdapter.getCustom14());
        license.setCustom15(licenseAuthorityAdapter.getCustom15());
        license.setStrCustom1(licenseAuthorityAdapter.getStrCustom1());
        license.setStrCustom2(licenseAuthorityAdapter.getStrCustom2());
        license.setStrCustom3(licenseAuthorityAdapter.getStrCustom3());
        license.setStrCustom4(licenseAuthorityAdapter.getStrCustom4());
        license.setStrCustom5(licenseAuthorityAdapter.getStrCustom5());
        license.setOnlineTime(this.licenseCumulativeTimer.getCumulativeTime());
        return license;
    }

    @Override
    public void setLicenseCumulativeTimerUpdated(Boolean updated) {
        this.licenseCumulativeTimer.setUpdateUtTime(Boolean.TRUE.equals(updated));
    }

    @Override
    public Resource getQRCodeImage(Integer width, Integer height) {
        BaseLicenseAuth auth = licenseAuthSvc.selectOneRow();
        String clientCode = auth.getClientCode();
        String url = uinnovaCloudRegisterLicense;
        if (!BinaryUtils.isEmpty(clientCode)) {
            url += url.indexOf('?') < 0 ? "?" : "&";
            url += "clientCode=" + clientCode;
        }
        if (width == null || width.intValue() < 1) {
            width = 260;
        }
        if (height == null || height.intValue() < 1) {
            height = 260;
        }
        return this.build(url, width, height);
    }

    @SuppressWarnings({"unchecked", "rawtypes"})
    private BufferedImage createImage(String content, int width, int height) throws Exception {
        Hashtable hints = new Hashtable();
        hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H);
        hints.put(EncodeHintType.CHARACTER_SET, Local.getCharset());
        hints.put(EncodeHintType.MARGIN, 1);
        BitMatrix bitMatrix = new MultiFormatWriter().encode(content, BarcodeFormat.QR_CODE, width, height, hints);
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        for (int x = 0; x < width; x++) {
            for (int y = 0; y < height; y++) {
                image.setRGB(x, y, bitMatrix.get(x, y) ? 0xFF000000 : 0xFFFFFFFF);
            }
        }
        return image;
    }

    public int daysBetween(String currentDate, String endDate) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        Calendar cal = Calendar.getInstance();
        int betweenDays = -1;
        try {
            cal.setTime(sdf.parse(currentDate));
            long currentTime = cal.getTimeInMillis();

            cal.setTime(sdf.parse(endDate));
            cal.add(Calendar.DAY_OF_MONTH, 1);
            long endTime = cal.getTimeInMillis();
            long day = (endTime - currentTime) / (1000 * 3600 * 24);
            if (day <= 0L) {
                return betweenDays;
            }else {
                return Integer.parseInt(String.valueOf(day));
            }

        } catch (ParseException e) {
            log.error("授权日期有误", e);
//            e.printStackTrace();
        }
        return betweenDays;
    }

    private void addLogo(BufferedImage source, int qrCodeWidth, int qrCodeHeight) throws Exception {
        Resource res = ResourceResolver.getResource(this.logoPath);
        InputStream is = null;
        Image logo = null;
        try {
            is = res.getInputStream();
            logo = ImageIO.read(is);
        } finally {
            if (is != null) {
                is.close();
            }
        }
        int width = logo.getWidth(null);
        int height = logo.getHeight(null);
        // 插入LOGO
        Graphics2D graph = source.createGraphics();
        int x = (qrCodeWidth - width) / 2;
        int y = (qrCodeHeight - height) / 2;
        graph.drawImage(logo, x, y, width, height, null);
        Shape shape = new RoundRectangle2D.Float(x, y, width, width, 6, 6);
        graph.setStroke(new BasicStroke(3f));
        graph.draw(shape);
        graph.dispose();
    }

    public Resource build(String content, int width, int height) {
        try {
            BufferedImage image = createImage(content, width, height);
            addLogo(image, width, height);
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(image, "PNG", baos);
            return new ByteArrayResource(baos.toByteArray(), BinaryUtils.getUUID() + ".png");
        } catch (Exception e) {
            throw BinaryUtils.transException(e, ServiceException.class);
        }
    }
}
