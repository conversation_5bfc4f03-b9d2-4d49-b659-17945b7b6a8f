package com.uinnova.product.eam.web.eam.mvc;

import com.binary.core.util.BinaryUtils;
import com.binary.framework.web.RemoteResult;
import com.uinnova.product.eam.comm.model.ArchDecisionDto;
import com.uinnova.product.eam.comm.model.DecisionParam;
import com.uinnova.product.eam.model.ArchDecisionResponse;
import com.uinnova.product.eam.service.IArchDecisionSvc;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 架构决策
 * <AUTHOR>
 * @date 2021/12/1
 */
@Slf4j
@RestController
@RequestMapping("/decision")
public class ArchDecisionMvc {
    @Autowired
    private IArchDecisionSvc decisionSvc;

    @PostMapping("/submitDecision")
    @ModDesc(desc = "架构决策提交", pDesc = "架构决策信息", rDesc = "结果", rType = RemoteResult.class)
    public RemoteResult submitDecision(@RequestBody ArchDecisionDto dto) {
        return new RemoteResult(decisionSvc.submitDecision(dto));
    }


    @PostMapping("/queryDecisionList")
    @ModDesc(desc = "架构决策列表页", pDesc = "条件查询参数", rDesc = "结果", rType = RemoteResult.class)
    public RemoteResult queryDecisionList(@RequestBody  @Validated DecisionParam decisionParam) {
        return new RemoteResult( decisionSvc.queryDecisionList(decisionParam));
    }

    @GetMapping("/getDecisionById")
    @ModDesc(desc = "查询架构决策", pDesc = "架构决策信息", rDesc = "结果", rType = RemoteResult.class)
    public RemoteResult getDecisionById(@RequestParam Long id) {
        ArchDecisionResponse result = decisionSvc.getDecisionById(id);
        return new RemoteResult(result);
    }

    @GetMapping("/getProductsInvolved")
    @ModDesc(desc = "查询涉及产品", pDesc = "", rDesc = "结果", rType = RemoteResult.class)
    public RemoteResult getProductsInvolved() {
        return new RemoteResult(decisionSvc.getProductsInvolved());
    }

    @GetMapping("/getCenterAndTeam")
    @ModDesc(desc = "查询中心及团队", pDesc = "", rDesc = "结果", rType = RemoteResult.class)
    public RemoteResult getCenterAndTeam() {
        return new RemoteResult(decisionSvc.getCenterAndTeam());
    }

    @PostMapping("/itemDone")
    @ModDesc(desc = "已阅接口", pDesc = "", rDesc = "结果", rType = RemoteResult.class)
    public RemoteResult itemDone(@RequestBody ArchDecisionDto dto) {
        Assert.notNull(dto.getTaskId(),"taskId不能为空");
        decisionSvc.itemDone(dto.getTaskId());
        return new RemoteResult("success");
    }

    @GetMapping("/getUserByRole")
    @ModDesc(desc = "查询指定角色权限的用户信息", pDesc = "", rDesc = "结果", rType = RemoteResult.class)
    public RemoteResult getUserByRole() {
        return new RemoteResult(decisionSvc.getUserByRole());
    }

    @GetMapping("/queryDecisionBySystemId")
    @ModDesc(desc = "通过应用系统查询架构决策列表", pDesc = "系统Id", rDesc = "结果", rType = RemoteResult.class)
    public RemoteResult queryDecisionBySystemId(@RequestParam(defaultValue = "1") Integer pageNum,
                                                @RequestParam(defaultValue = "20") Integer pageSize,
                                                @RequestParam String systemId) {
        BinaryUtils.checkEmpty(systemId, "系统id");
        return new RemoteResult(decisionSvc.queryDecisionBySystemId(pageNum, pageSize, systemId));
    }

    @GetMapping("/getApproverByTaskId")
    public RemoteResult getApproverByTaskId(@RequestParam String taskId) {
        String result = decisionSvc.getApproverByTaskId(taskId);
        return new RemoteResult(result);
    }
}
