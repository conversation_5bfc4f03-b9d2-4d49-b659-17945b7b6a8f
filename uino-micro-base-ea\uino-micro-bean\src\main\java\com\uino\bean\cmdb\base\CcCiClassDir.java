package com.uino.bean.cmdb.base;

import com.binary.framework.bean.EntityBean;
import com.binary.framework.bean.annotation.Comment;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 
 * <AUTHOR>
 *
 */
@ApiModel(value="CI分类目录表",description = "CI分类目录表")
@Comment("CI分类目录表[CC_CI_CLASS_DIR]")
public class CcCiClassDir implements EntityBean {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value="ID",example = "123")
    @Comment("ID[ID]")
    private Long id;

    @ApiModelProperty(value="目录名称",example = "fruit")
    @Comment("目录名称[DIR_NAME]")
    private String dirName;

	@ApiModelProperty(value="分类类型",example = "1")
    @Comment("分类类型[CI_TYPE]    1=基础CI 2=关系CI 3=2D图标 4=标签分组 5=3D图标 6=视频资源")
    private Integer ciType;

    @ApiModelProperty(value="上级目录ID",example = "123")
	@Comment("上级目录ID[PARENT_ID]")
    private Long parentId;

    @ApiModelProperty(value="分类层级级别",example = "1")
    @Comment("分类层级级别[DIR_LVL]")
    private Integer dirLvl;

    @ApiModelProperty(value="分类层级路径",example = "#1#2#7#")
    @Comment("分类层级路径[DIR_PATH]    例：#1#2#7#")
    private String dirPath;

    @ApiModelProperty(value="显示排序",example = "1")
    @Comment("显示排序[ORDER_NO]")
    private Integer orderNo;

    @ApiModelProperty(value="是否末级",example = "1")
    @Comment("是否末级[IS_LEAF]    1=是 0=否")
    private Integer isLeaf;

    @ApiModelProperty(value="目录图标")
    @Comment("目录图标[ICON]")
    private String icon;

    @ApiModelProperty(value="目录描述")
    @Comment("目录描述[DIR_DESC]")
    private String dirDesc;

    @ApiModelProperty(value = "目录颜色",example = "red")
    @Comment("目录颜色[DIR_COLOR]")
    private String dirColor;

    @ApiModelProperty(value="所属域id")
    @Comment("所属域[DOMAIN_ID]")
    private Long domainId;

    @ApiModelProperty(value="数据状态",example="1")
    @Comment("数据状态[DATA_STATUS]    0=删除，1=正常")
    private Integer dataStatus;

    @ApiModelProperty(value="创建人",example = "mike")
    @Comment("创建人[CREATOR]")
    private String creator;

    @ApiModelProperty(value="修改人",example = "mike")
    @Comment("修改人[MODIFIER]")
    private String modifier;

    @ApiModelProperty(value="创建时间")
    @Comment("创建时间[CREATE_TIME]    yyyyMMddHHmmss")
    private Long createTime;

    @ApiModelProperty(value="更新时间")
    @Comment("更新时间[MODIFY_TIME]    yyyyMMddHHmmss")
    private Long modifyTime;

    /**
     * 是否只读
     */
    @ApiModelProperty(value="是否只读",example = "true")
    private boolean readOnly;

    @Override
	public Long getId() {
        return this.id;
    }

    @Override
	public void setId(Long id) {
        this.id = id;
    }

    public String getDirName() {
        return this.dirName;
    }

    public void setDirName(String dirName) {
        this.dirName = dirName;
    }

    public Integer getCiType() {
        return this.ciType;
    }

    public void setCiType(Integer ciType) {
        this.ciType = ciType;
    }

    public Long getParentId() {
        return this.parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public Integer getDirLvl() {
        return this.dirLvl;
    }

    public void setDirLvl(Integer dirLvl) {
        this.dirLvl = dirLvl;
    }

    public String getDirPath() {
        return this.dirPath;
    }

    public void setDirPath(String dirPath) {
        this.dirPath = dirPath;
    }

    public Integer getOrderNo() {
        return this.orderNo;
    }

    public void setOrderNo(Integer orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getIsLeaf() {
        return this.isLeaf;
    }

    public void setIsLeaf(Integer isLeaf) {
        this.isLeaf = isLeaf;
    }

    public String getIcon() {
        return this.icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getDirDesc() {
        return this.dirDesc;
    }

    public void setDirDesc(String dirDesc) {
        this.dirDesc = dirDesc;
    }

    public String getDirColor() {
        return this.dirColor;
    }

    public void setDirColor(String dirColor) {
        this.dirColor = dirColor;
    }

    public Long getDomainId() {
        return this.domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }

    public Integer getDataStatus() {
        return this.dataStatus;
    }

    public void setDataStatus(Integer dataStatus) {
        this.dataStatus = dataStatus;
    }

    public String getCreator() {
        return this.creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getModifier() {
        return this.modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    @Override
	public Long getCreateTime() {
        return this.createTime;
    }

    @Override
	public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    @Override
	public Long getModifyTime() {
        return this.modifyTime;
    }

    @Override
	public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }

    public boolean isReadOnly() {
        return readOnly;
    }

    public void setReadOnly(boolean readOnly) {
        this.readOnly = readOnly;
    }
}
