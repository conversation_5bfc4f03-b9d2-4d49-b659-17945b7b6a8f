package com.binary.jdbc.adapter;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


public class Table implements Serializable {
	private static final long serialVersionUID = 1L;
	
		
	private String tableCat;
	private String tableSchem;
	private String tableName;
	private String tableType;
	private String remarks;
	private String typeCat;
	private String typeSchem;
	private String typeName;
	private String selfReferencingColName;
	private String refGeneration;
	
	
	private Map<String,Column> columnsMap = new HashMap<String,Column>();
	private List<Column> columns;
	
	
		
	
	public String getTableSchem() {
		return tableSchem;
	}
	public void setTableSchem(String tableSchem) {
		this.tableSchem = tableSchem;
	}
	public String getTableName() {
		return tableName;
	}
	public void setTableName(String tableName) {
		this.tableName = tableName;
	}
	public String getTableType() {
		return tableType;
	}
	public void setTableType(String tableType) {
		this.tableType = tableType;
	}
	public List<Column> getColumns() {
		return columns;
	}
	
	
	public String getRemarks() {
		return remarks;
	}
	public void setRemarks(String remarks) {
		this.remarks = remarks;
	}
		
	public String getTableCat() {
		return tableCat;
	}
	public void setTableCat(String tableCat) {
		this.tableCat = tableCat;
	}
	public String getTypeCat() {
		return typeCat;
	}
	public void setTypeCat(String typeCat) {
		this.typeCat = typeCat;
	}
	public String getTypeSchem() {
		return typeSchem;
	}
	public void setTypeSchem(String typeSchem) {
		this.typeSchem = typeSchem;
	}
	public String getTypeName() {
		return typeName;
	}
	public void setTypeName(String typeName) {
		this.typeName = typeName;
	}
	public String getSelfReferencingColName() {
		return selfReferencingColName;
	}
	public void setSelfReferencingColName(String selfReferencingColName) {
		this.selfReferencingColName = selfReferencingColName;
	}
	public String getRefGeneration() {
		return refGeneration;
	}
	public void setRefGeneration(String refGeneration) {
		this.refGeneration = refGeneration;
	}
	public Map<String, Column> getColumnsMap() {
		return columnsMap;
	}
	public void setColumnsMap(Map<String, Column> columnsMap) {
		this.columnsMap = columnsMap;
	}
	public void setColumns(List<Column> columns) {
		this.columns = columns;
		this.columnsMap.clear();
		if(columns != null) {
			for(int i=0; i<columns.size(); i++) {
				Column c = (Column) columns.get(i);
				this.columnsMap.put(c.getColumnName().toUpperCase(), c);
			}
		}
	}
	
	
	
	public boolean containsColumn(String columnName) {
		return this.columnsMap.containsKey(columnName.toUpperCase());
	}
	
	
	
	public Column getColumn(String columnName) {
		return (Column) this.columnsMap.get(columnName.toUpperCase());
	}
	
	
	
	
	
	
}
