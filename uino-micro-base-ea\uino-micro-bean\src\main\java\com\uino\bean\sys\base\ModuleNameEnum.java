package com.uino.bean.sys.base;

/**
 * 
 * <AUTHOR>
 *
 */
public enum ModuleNameEnum {
    /**
     * 公共组件模块名称
     */
    COMM,
    
    /**
     * CMV模块名称
     */
    CMV,

    /**
     * DMV模块名称
     */
    DMV,

    /**
     * DCV模块名称
     */
    DCV,

    /**
     * EMV模块名称
     */
    EMV,

    /**
     * PMV模块名称
     */
    PMV,

    /**
     * SMV模块名称
     */
    SMV,

    /**
     * MOM模块名称
     */
    MOM,

    /**
     * PROXIMA模块名称
     */
    PROXIMA,

    /**
     * 中台模块名称
     */
    MIDDLE_PLATFORM,
    /**
     * EAM项目
     */
    EAM;
  

    public static ModuleNameEnum valueOfMvc(String mvc) {
        if (mvc == null || (mvc = mvc.trim()).length() == 0) {
            return null;
        }
        String commPackageStart = "com.uino.web";
        String dcvPackageStart = "com.uinnova.product.dcv";
        String cmvPackageStart = "com.uinnova.product.vmdb.web";
        String dmvPackageStart = "com.uinnova.product.diagram.web";
        String emvPackageStart = "com.uinnova.product.monitor.web";
        String pmvPackageStart = "com.uinnova.product.pmv.web";
        String momPackageStart = "com.uinnova.product.mom.web";
        String proximaPackageStart = "com.thingjs.framework.web";
        String middlePlatformPackageStart = "com.uinnova.middleplatform.web";
        String eamPackageStart = "com.uinnova.product.eam.web";
        if (mvc.startsWith(commPackageStart)) {
            return COMM;
        } else if (mvc.startsWith(dcvPackageStart)) {
            return DCV;
        } else if (mvc.startsWith(cmvPackageStart)) {
            return CMV;
        } else if (mvc.startsWith(dmvPackageStart)) {
            return DMV;
        } else if (mvc.startsWith(emvPackageStart)) {
            return EMV;
        } else if (mvc.startsWith(pmvPackageStart)) {
            return PMV;
        } else if (mvc.startsWith(momPackageStart)) {
            return MOM;
        } else if (mvc.startsWith(proximaPackageStart)) {
            return PROXIMA;
        }else if (mvc.startsWith(middlePlatformPackageStart)) {
            return MIDDLE_PLATFORM;
        } else if (mvc.startsWith(eamPackageStart)) {
            return EAM;
        }
        return null;
    }
}
