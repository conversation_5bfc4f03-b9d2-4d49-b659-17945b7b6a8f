package com.uinnova.product.eam.base.model;

import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import lombok.Data;

import java.util.Collections;
import java.util.List;

/**
 * 组件与领域关系图自动成图
 * <AUTHOR>
 * @date 2022/3/22
 */
@Data
public class ModuleFieldInfo {
    /**
     * 自由组件
     */
    private List<CcCiInfo> inside = Collections.emptyList();
    /**
     * 外部组件
     */
    private List<CcCiInfo> outside = Collections.emptyList();
}
