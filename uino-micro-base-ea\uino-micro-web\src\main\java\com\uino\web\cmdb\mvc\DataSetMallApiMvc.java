package com.uino.web.cmdb.mvc;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import com.binary.framework.exception.ServiceException;
import com.uinnova.product.vmdb.comm.model.ci.CcCiClass;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiInfo;
import com.uino.bean.cmdb.base.dataset.batch.DataSetTableResult;
import com.uino.bean.cmdb.business.dataset.*;
import com.uino.bean.dataset.base.DataSetPathInfoVo;
import com.uino.bean.dataset.base.DataSetThumbnailDTO;
import com.uino.bean.tp.query.QueryDataTableDTO;
import com.uino.init.api.ApiResult;
import com.uino.init.api.ApiVersion;
import com.uino.service.cmdb.dataset.microservice.IBatchProcessSvc;
import io.swagger.annotations.Api;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.binary.core.io.FileSystem;
import com.binary.core.io.Resource;
import com.binary.core.io.support.ByteArrayResource;
import com.binary.core.util.BinaryUtils;
import com.binary.framework.exception.ControllerException;
import com.uinnova.product.vmdb.comm.doc.annotation.ModDesc;
import com.uino.util.sys.SysUtil;
import com.uino.bean.cmdb.base.dataset.DataSetMallApiType;
import com.uino.bean.cmdb.base.dataset.chart.Chart;
import com.uino.bean.permission.base.SysUser;
import com.uino.bean.tp.query.MetricAttrValQueryDTO;
import com.uino.bean.tp.query.TpRuleReqDTO;
import com.uino.api.client.cmdb.IDataSetApiSvc;
import com.uino.web.BaseMvc;

import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * @Classname DataSetMallApiController
 * @Description 数据集
 * @Date 2020/3/19 15:18
 * @Created by sh
 */
@ApiVersion(1)
@Api(value = "数据集", tags = {"数据超市"})
@RestController
@RequestMapping("cmdb/dataSet")
@Slf4j
public class DataSetMallApiMvc extends BaseMvc {

    @Autowired
    private IDataSetApiSvc dataSetApiSvc;

    @RequestMapping(value = {"/saveOrUpdate"}, method = RequestMethod.POST)
    @ApiOperation("保存数据集")
    public ApiResult<Long> saveOrUpdate(HttpServletRequest request, HttpServletResponse response,
                                        @RequestBody JSONObject body, @RequestParam(value = "isBatch", required = false) Boolean isBatch,
                                        @RequestParam(defaultValue = "true") Boolean needCheck) {
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        body.put("domainId", currentUserInfo.getDomainId());
        body.put("needCheck", needCheck);
        return ApiResult.ok(this).data(dataSetApiSvc.saveOrUpdateDataSet(body, true));
    }

    @PostMapping("updateDataSetById")
    public ApiResult<String> updateDataSetById( @RequestBody JSONObject body){
        Long id = body.getLong("id");
        dataSetApiSvc.updateDataSetById(id);
        return ApiResult.ok(this).data("success");
    }

    @RequestMapping(value = {"/thumbnail"}, method = RequestMethod.POST)
    @ApiOperation("保存更新数据超市缩略图")
    public ApiResult<String> updateThumbnail(@RequestBody DataSetThumbnailDTO body) {
        return ApiResult.ok(this).data(dataSetApiSvc.updateThumbnail(body));
    }

    @RequestMapping(value = {"/pathInfo"}, method = RequestMethod.POST)
    @ApiOperation("路径及缩略图信息")
    public ApiResult<List<DataSetPathInfoVo>> getPathInfo(@RequestBody List<Long> dataSetIds) {
        return ApiResult.ok(this).data(dataSetApiSvc.getPathInfo(dataSetIds));
    }

    @ApiOperation(value="查询数据集列表")
    @RequestMapping(value = {"/findDataSet"}, method = RequestMethod.POST)
    @ModDesc(desc = "查询数据集列表", pDesc = "body", pType = JSONObject.class, rDesc = "查询结果", rType = List.class)
    public ApiResult<List<JSONObject>> findDataSet(HttpServletRequest request, HttpServletResponse response, @RequestBody JSONObject body) {
        String name = body.getString("name");
        Boolean isMyself = body.getBooleanValue("isMyself");
        List<DataSetMallApiType> typeList = new ArrayList<DataSetMallApiType>();
        if (body.containsKey("types")) {
            List<Integer> types = JSONArray.parseArray(JSON.toJSONString(body.getJSONArray("types")), Integer.class);
            typeList = types.stream().map(type -> DataSetMallApiType.valueOf(type)).filter(dataType -> dataType != null).collect(Collectors.toList());
        }
        List<JSONObject> dataSet = dataSetApiSvc.findDataSet(name, isMyself, "", typeList);
        return ApiResult.ok(this).data(dataSet);
    }

    @ApiOperation(value="查询数据集列表")
    @RequestMapping(value = {"/findDataSetByRelationRule"}, method = RequestMethod.POST)
    @ModDesc(desc = "查询数据集列表", pDesc = "body", pType = JSONObject.class, rDesc = "查询结果", rType = List.class)
    public ApiResult<List<JSONObject>> findDataSetByRelationRule(HttpServletRequest request, HttpServletResponse response, @RequestBody JSONObject body) {
        String name = body.getString("name");
        Boolean isMyself = body.getBooleanValue("isMyself");
        List<JSONObject> dataSet = dataSetApiSvc.findDataSet(name, isMyself, "", Collections.singletonList(DataSetMallApiType.RelationRule));
        return ApiResult.ok(this).data(dataSet);
    }

    @RequestMapping(value = {"/findDataSetById"}, method = RequestMethod.POST)
    @ApiOperation("查询id查询数据集")
    public ApiResult<JSONObject> findDataSetById(HttpServletRequest request, HttpServletResponse response, @RequestBody JSONObject body) {
        Long id = body.getLong("id");
        JSONObject jsonObject = dataSetApiSvc.findDataSetById(id);
        return ApiResult.ok(this).data(jsonObject);
    }

    @RequestMapping(value = {"/delete"}, method = RequestMethod.POST)
    @ApiOperation("删除数据集")
    public ApiResult<Boolean> delete(HttpServletRequest request, HttpServletResponse response, @RequestBody String body) {
        JSONObject jsonObject = JSONObject.parseObject(body);
        Long id = jsonObject.getLong("id");
        dataSetApiSvc.delete(id);
        return ApiResult.ok(this).data(true);

    }

    /**
     * 此方法参数api页面执行和生成代码执行略有不同，根据id区分参数是否多一层param
     *
     * @param body
     */
    @ApiOperation(value="查询数据集执行结果")
    @RequestMapping(value = {"/execute"}, method = RequestMethod.POST)
    @ModDesc(desc = "查询数据集执行结果", pDesc = "body", pType = JSONObject.class, rDesc = "查询结果", rType = String.class)
    public ApiResult<JSONObject> execute(HttpServletRequest request, HttpServletResponse response, @RequestBody(required = false) JSONObject body, @RequestParam(value = "time", required = false) String time,
        @RequestParam(value = "id", required = false) Long id) {
        JSONObject result = null;
        String language = request.getHeader("language");
        JSONObject param = body == null ? new JSONObject() : body;
        if (id == null) {
            Assert.notNull(body, "参数不可为空");
            id = body.getLong("id");
            param = body.containsKey("param") ? body.getJSONObject("param") : body;
        }
        if (!BinaryUtils.isEmpty(time)) {
            param.put("time", time);
        }
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        result = dataSetApiSvc.execute(currentUserInfo.getDomainId(),language, id, param);
        String s = result.toJSONString();
        return ApiResult.ok(this).data(JSONObject.parseObject(s));
    }
    @ApiOperation(value="查询数据集执行结果")
    @RequestMapping(value = {"/realTimeExecute"}, method = RequestMethod.POST)
    @ModDesc(desc = "查询数据集执行结果", pDesc = "body", pType = JSONObject.class, rDesc = "查询结果", rType = String.class)
    public ApiResult<JSONObject> realTimeExecute(HttpServletRequest request, @RequestBody JSONObject body) {
        String language = request.getHeader("language");
        JSONObject result = null;
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        if (body.containsKey("id")) {
            result = dataSetApiSvc.realTimeExecute(currentUserInfo.getDomainId(), language, body.getLong("id"), body.getJSONObject("param"));
        } else {
            result = dataSetApiSvc.realTimeExecute(currentUserInfo.getDomainId(), language, null, body);
        }
        String s = result.toJSONString();
        return ApiResult.ok(this).data(JSONObject.parseObject(s));
    }

    @RequestMapping(value = {"/share"}, method = RequestMethod.POST)
    @ApiOperation("分享")
    public ApiResult<Boolean> shareDataSet(@RequestBody String body) {
        JSONObject jsonObject = JSONObject.parseObject(body);
        Long id = jsonObject.getLong("id");
        int shareLevel = jsonObject.getIntValue("shareLevel");
        dataSetApiSvc.shareDataSet(id, shareLevel);
        return ApiResult.ok(this).data(true);

    }

    @RequestMapping(value = {"/queryFriend"}, method = RequestMethod.POST)
    @ApiOperation("根据规则和入口查询关系")
    public ApiResult<FriendInfo> queryFriendByStartCiId(@RequestBody String body) {

        JSONObject jsonObject = JSONObject.parseObject(body);
        Long id = jsonObject.getLong("id");
        Long startCiId = jsonObject.getLong("startCiId");
        FriendInfo friendInfo = dataSetApiSvc.queryFriendByStartCiId(id, startCiId);
        //将ciClass放入对应ci对象中
        Map<Long, CcCiClass> classMap = friendInfo.getCiClassInfos().stream().map(CcCiClassInfo::getCiClass).collect(Collectors.toMap(CcCiClass::getId, each -> each, (key1, key2) -> key2));
        for(CcCiInfo ciInfo : friendInfo.getCiNodes()){
            Long classId = ciInfo.getCi().getClassId();
            CcCiClass ciClass = classMap.get(classId);
            if(!BinaryUtils.isEmpty(ciClass)){
                ciInfo.setCiClass(ciClass);
            }
        }
        return ApiResult.ok(this).data(friendInfo);
    }

    @RequestMapping(value = {"/queryFriendList"}, method = RequestMethod.POST)
    @ApiOperation("根据规则和入口查询关系-多节点")
    public ApiResult<FriendInfo> queryFriendByStartCiIds(@RequestBody List<FriendInfoRequestDto> body) {
        FriendBatchInfo friendInfo = dataSetApiSvc.queryFriendByStartCiIds(body);
        return ApiResult.ok(this).data(friendInfo);
    }

    @RequestMapping(value = {"/queryRuleNodeCiNum"}, method = RequestMethod.POST)
    @ApiOperation("查询规则节点ci个数")
    public ApiResult<Map<Long, Integer>> queryRuleNodeCiNum(@RequestBody String body) {
        JSONObject jsonObject = JSONObject.parseObject(body);
        Long id = jsonObject.getLong("id");
        Map<Long, Integer> nodeCiNum = dataSetApiSvc.queryRuleNodeCiNum(id);
        return ApiResult.ok(this).data(nodeCiNum);
    }

    @RequestMapping(value = {"/queryDisassembleFriendInfoDataByPath"}, method = RequestMethod.POST)
    @ApiOperation("根据规则和入口查询关系")
    public ApiResult<RltRuleTableData> queryDisassembleFriendInfoDataByPath(@RequestBody String body) {
        JSONObject jsonObject = JSONObject.parseObject(body);
        Long id = jsonObject.getLong("id");
        Long startCiId = jsonObject.getLong("startCiId");
        RltRuleTableData rltRuleTableData = dataSetApiSvc.queryDisassembleFriendInfoDataByPath(id, startCiId);
        return ApiResult.ok(this).data(rltRuleTableData);
    }

    @RequestMapping(value = {"/queryRuleLegitimateCi"}, method = RequestMethod.POST)
    @ApiOperation("查询所有符合规则的入口ci")
    public ApiResult<JSONObject> queryRuleLegitimateCi(@RequestBody String body) {
        JSONObject json = JSONObject.parseObject(body);
        Long id = json.getLong("id");
        String name = json.getString("name");
        if(BinaryUtils.isEmpty(id) && BinaryUtils.isEmpty(name)){
            throw new ServiceException("无效的参数!");
        }
        Integer pageNum = json.getInteger("pageNum");
        Integer pageSize = json.getInteger("pageSize");
        String like = json.getString("like");
        JSONObject jsonObject = dataSetApiSvc.queryRuleLegitimateCi(pageNum, pageSize, id, name, like);
        return ApiResult.ok(this).data(jsonObject);
    }

    @RequestMapping(value = {"/countStatistics"}, method = RequestMethod.POST)
    @ApiOperation("统计关系遍历结果")
    public ApiResult<JSONObject> countStatistics(@RequestBody String body) throws Exception {
        JSONObject json = JSONObject.parseObject(body);
        Long id = json.getLong("id");
        Chart chart = JSONObject.parseObject(json.getString("chart"), Chart.class);
        JSONObject jsonObject = dataSetApiSvc.countStatistics(id, chart);
        return ApiResult.ok(this).data(jsonObject);

    }


    @RequestMapping(value = {"/getSheetList"}, method = RequestMethod.POST)
    @ApiOperation("获取数据集下包含的Sheet")
    public ApiResult<List<Map<String, Object>>> getSheetList(@RequestBody String body) {
        JSONObject jsonObject = JSONObject.parseObject(body);
        Long id = jsonObject.getLong("id");
        List<Map<String, Object>> dataSetSheets = dataSetApiSvc.getDataSetSheets(id);
        return ApiResult.ok(this).data(dataSetSheets);
    }

    @RequestMapping(value = {"/getQueryCondition"}, method = RequestMethod.POST)
    @ApiOperation("获取数据集的查询条件")
    public ApiResult<List<JSONObject>> getCondition(@RequestBody String body) {
        JSONObject jsonObject = JSONObject.parseObject(body);
        Long dataSetId = jsonObject.getLong("dataSetId");
        String sheetId = jsonObject.getString("sheetId");
        List<JSONObject> queryCondition = dataSetApiSvc.getQueryCondition(dataSetId, sheetId);
        return ApiResult.ok(this).data(queryCondition);
    }

    @RequestMapping(value = {"/getResultBySheet"}, method = RequestMethod.POST)
    @ApiOperation("查询遍历结果")
    public ApiResult<DataSetExeResultSheetPage> getResultBySheet(@RequestBody String body) {
        JSONObject jsonObject = JSONObject.parseObject(body);
        Long dataSetId = jsonObject.getLong("dataSetId");
        String sheetId = jsonObject.getString("sheetId");
        int pageNum = jsonObject.getInteger("pageNum");
        int pageSize = jsonObject.getInteger("pageSize");

        String sortCol = jsonObject.containsKey("sortCol") ? jsonObject.getString("sortCol") : null;
        boolean isDesc = jsonObject.containsKey("isDesc") ? jsonObject.getBoolean("isDesc") : false;
        JSONArray condition = jsonObject.getJSONArray("condition");
        SysUser user = SysUtil.getCurrentUserInfo();
        DataSetExeResultSheetPage dataSetExeResultSheetPage = dataSetApiSvc.queryDataSetResultBySheet(user.getDomainId(), dataSetId,
                sheetId, pageNum, pageSize, sortCol, isDesc, condition, user.getLoginCode());
        return ApiResult.ok(this).data(dataSetExeResultSheetPage);
    }

    @RequestMapping(value = {"/getResultUsingRule"}, method = RequestMethod.POST)
    @ApiOperation("根据ruleInfo查询全部表格形式数据")
    public ApiResult<List<DataSetExeResultSheetPage>> getResultUsingRule(@RequestBody String body) {
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        JSONObject jsonObject = JSONObject.parseObject(body);

        Integer limit = jsonObject.getInteger("limit");
        JSONObject rule = jsonObject.getJSONObject("rule");
        rule.put("domainId", currentUserInfo.getDomainId());
        List<DataSetExeResultSheetPage> sheets = dataSetApiSvc.getResultUsingRule(rule, limit);
        return ApiResult.ok(this).data(sheets);
    }

    @GetMapping(value = {"/getResultUsingRuleByDataSetId"})
    @ApiOperation("根据ruleInfo查询全部表格形式数据")
    public ApiResult<List<DataSetExeResultSheetPage>> getResultUsingRuleByDataSetId(@RequestParam("dataSetId") Long dataSetId) {
        // 通过id获取数据集
        JSONObject ruleJson = dataSetApiSvc.findDataSetById(dataSetId);

        // 通过数据集获取数据
        if (ruleJson != null) {
            Integer limit = 200;
            SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
            ruleJson.put("domainId", currentUserInfo.getDomainId());
            List<DataSetExeResultSheetPage> sheets = dataSetApiSvc.getResultUsingRule(ruleJson, limit);
            return ApiResult.ok(this).data(sheets);
        }
        return ApiResult.ok(this);
    }

    @GetMapping(value = {"/findDataSetRuleList"})
    @ApiOperation("根据ruleInfo查询全部表格形式数据")
    public ApiResult<List<DataSetExeResultSheetPage>> findDataSetRuleList(@RequestParam("dataSetId") Long dataSetId) {
        Integer limit = 100;
        JSONObject ruleJson = new JSONObject();
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        ruleJson.put("domainId", currentUserInfo.getDomainId());
        ruleJson.put("loginCode", currentUserInfo.getLoginCode());
        ruleJson.put("dataSetId", dataSetId);
        List<DataSetExeResultSheetPage> pathList = dataSetApiSvc.findDataSetRuleList(ruleJson, limit);
        return ApiResult.ok(this).data(pathList);
    }

    @RequestMapping(value = {"/isTaskRunning"}, method = RequestMethod.POST)
    @ApiOperation("判断数据集的批处理任务是否在运行中")
    public ApiResult<Integer> isTaskRunning(@RequestBody String body) {
        JSONObject jsonObject = JSONObject.parseObject(body);
        Long dataSetId = jsonObject.getLong("id");
        int taskRunning = dataSetApiSvc.isTaskRunning(dataSetId);
        return ApiResult.ok(this).data(taskRunning);

    }

    @RequestMapping(value = {"/downloadResultBySheet"}, method = RequestMethod.POST)
    @ApiOperation("根据规则下载表格结果")
    public void downloadResultBySheet(@RequestBody String body) throws IOException {
//        long start = System.currentTimeMillis();
        JSONObject jsonObject = JSONObject.parseObject(body);
        Long dataSetId = jsonObject.getLong("dataSetId");
        String sheetId = jsonObject.getString("sheetId");
        String sortCol = jsonObject.containsKey("sortCol") ? jsonObject.getString("sortCol") : null;
        boolean isDesc = jsonObject.containsKey("isDesc") ? jsonObject.getBoolean("isDesc") : false;
        JSONArray condition = jsonObject.getJSONArray("condition");
        Resource resource = dataSetApiSvc.downloadSheetData(dataSetId, sheetId, sortCol, isDesc, condition);
        String fileName = resource.getName();
        Resource res = new ByteArrayResource(IOUtils.toByteArray(resource.getInputStream()), fileName);
//        logger.info("Download excel cost：{}", System.currentTimeMillis() - start);

        if (res != null && res.exists()) {
            response.setContentType("application/octet-stream");
            response.addHeader("Content-Disposition", "attachment; filename=" + fileName);
            try {
                OutputStream os = response.getOutputStream();
                InputStream is = res.getInputStream();
                try {
                    FileSystem.copy(is, os);
                    os.flush();
                } finally {
                    if (is != null) {
                        is.close();
                    }
                }
            } catch (IOException var12) {
                throw new ControllerException(var12);
            }
        } else {
            throw new ControllerException(" is not found resource '" + res + "'! ");
        }
    }

    @ApiOperation(value="获取请求接口代码")
    @RequestMapping(value = {"/getCode"}, method = RequestMethod.POST)
//    @ApiImplicitParam(name = "body", paramType = "body",
//            value = "查询格式:<br/>{<br/>"
//                    + "    \"type\":0,<br>"
//                    + "    \"url\":\"http://127.0.0.1/cmdb/dataSet/execute\",<br/>"
//                    + "    \"codeLang\":0,<br/>"
//                    + "    \"username\":\"admin\",<br/>"
//                    + "    \"password\":\"password\",<br/>"
//                    + "    \"pageNum\":1,<br/>"
//                    + "    \"pageSize\":10<br/>"
//                    + "}")
    @ModDesc(desc = "获取请求接口代码", pDesc = "body", pType = JSONObject.class, rDesc = "查询结果", rType = String.class)
    public ApiResult<String> getCode(@RequestBody String body) throws Exception {
        String code = dataSetApiSvc.getCode(body);
        return ApiResult.ok(this).data(code);
    }

    @RequestMapping(value = {"/groupDataSetMallApiLogCount"}, method = RequestMethod.POST)
    @ApiOperation("数据集请求次数统计")
    public ApiResult<List<Map<String, Object>>> groupDataSetMallApiLogCount(@RequestBody String body) {
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        List<Map<String, Object>> ret = dataSetApiSvc.groupDataSetMallApiLogCount(currentUserInfo.getDomainId());
        return ApiResult.ok(this).data(ret);
    }

    @ApiOperation(value="获取指标标签")
    @RequestMapping(value="/queryTpMetricLabelList",method=RequestMethod.POST)
    @ModDesc(desc = "获取指标标签", pDesc = "标签查询条件", pType = TpRuleReqDTO.class, rDesc = "指标标签集合", rType = List.class, rcType = String.class)
    public ApiResult<List<String>> getTpMetricLabelDTOList(HttpServletRequest request, HttpServletResponse response, @RequestBody TpRuleReqDTO ruleReqDTO) {
        SysUser currentUserInfo = SysUtil.getCurrentUserInfo();
        ruleReqDTO.setDomainId(currentUserInfo.getDomainId());
        return ApiResult.ok(this).data(dataSetApiSvc.getTpMetricLabelDTOList(ruleReqDTO));
    }

    @ApiOperation(value="获取指标标签值")
    @RequestMapping(value="/queryMetricAttrValue",method=RequestMethod.POST)
    @ModDesc(desc = "获取指标标签值", pDesc = "查询条件", pType = MetricAttrValQueryDTO.class, rDesc = "标签值集合", rType = List.class, rcType = String.class)
    public ApiResult< List<String>> queryMetricAttrValue(HttpServletRequest request, HttpServletResponse response, @RequestBody MetricAttrValQueryDTO bean) {
        return ApiResult.ok(this).data(dataSetApiSvc.queryMetricAttrValue(bean));
    }


    /**
     * 根据报表格式查询数据
     *
     * @return
     */
    @ApiOperation(value = "根据报表格式查询数据")
    @RequestMapping(value = {"/queryCiTableList"}, method = RequestMethod.POST)
    @ModDesc(desc = "根据报表格式查询数据", pDesc = "body", pType = JSONObject.class, rDesc = "查询结果", rType = String.class)
    public ApiResult<List<DataSetTableResult>> queryCiTableList(@RequestBody(required = false) QueryDataTableDTO body) {
        List<DataSetTableResult> ret = dataSetApiSvc.queryCiTableList(body);
        return ApiResult.ok(this).data(ret);
    }

    /**
     * 报表格式数据导出
     *
     * @return
     */
    @ApiOperation(value = "报表格式数据导出")
    @RequestMapping(value = {"/ciTableListExport"}, method = RequestMethod.POST)
    @ModDesc(desc = "报表格式数据导出", pDesc = "body", pType = JSONObject.class, rDesc = "查询结果", rType = String.class)
    public ResponseEntity<byte[]> ciTableListExport(@RequestBody(required = false) QueryDataTableDTO body) {
        if (BinaryUtils.isEmpty(body.getCardName())) {
            Assert.notNull(body, "卡片名称不可为空！");
        }
        List<DataSetTableResult> ret = dataSetApiSvc.queryCiTableList(body);
        return dataSetApiSvc.ciTableListExport(ret, body);
    }
}
