package com.uinnova.product.eam.model.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ReleaseDiagramDTO {
    private String diagramId;
    private String viewType;
    private Long dirType;
    private String ownerCode;
    private String assetCode;

    // 发布说明
    private String releaseDesc;
    // 架构资产目录文件id
    private Long dirId;
    // 视图名称
    private List<String> names;
    // 组件建模指定发布到的视图id
    private String releaseDiagramId;
    // 批量发布ids
    private List<String> diagramIds;
    // 业务主键ids
    private String prepareId;
    // 检出类型 1-检出 2-检出另存为
    private Integer actionType;
}
