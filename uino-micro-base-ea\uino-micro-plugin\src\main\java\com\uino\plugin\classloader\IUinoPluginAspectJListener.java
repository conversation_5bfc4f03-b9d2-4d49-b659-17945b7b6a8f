package com.uino.plugin.classloader;

import java.util.Map;

/**
 * 插件实现切面接口
 * 由于目前热加载不支持加载切面，故采用listener接口形式，在ControllerAspect中调用此接口的热加载实现
 * 需要配合{@link org.springframework.stereotype.Component}和{@link com.uino.plugin.classloader.annotation.UinoPluginAspectJ}注解
 */
public interface IUinoPluginAspectJListener {

    /**
     * 调用方法之前执行
     * @param paramMap 方法传参，移除request、response、文件等
     */
    public default void doBefore(Map<String, Object> paramMap){

    }

    /**
     * 调用方法之后执行
     * @param paramMap 方法传参，移除request、response、文件等
     * @param result 方法返回参数
     */
    public default void doAfter(Map<String, Object> paramMap, String result) {

    }

}
