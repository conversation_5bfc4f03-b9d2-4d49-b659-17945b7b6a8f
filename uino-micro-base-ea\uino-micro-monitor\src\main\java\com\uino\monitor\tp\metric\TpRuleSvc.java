package com.uino.monitor.tp.metric;

import java.util.Arrays;
import java.util.List;

import jakarta.annotation.PostConstruct;

import com.uino.dao.BaseConst;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.MultiMatchQueryBuilder;
import org.elasticsearch.index.query.Operator;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.stereotype.Repository;

import com.binary.core.util.BinaryUtils;
import com.uino.dao.AbstractESBaseDao;
import com.uino.dao.ESConst;
import com.uino.bean.tp.base.TpRuleDTO;
import com.uino.bean.tp.query.CTpRuleDTO;
import com.uino.bean.tp.query.TpRuleReqDTO;

@Repository
public class TpRuleSvc extends AbstractESBaseDao<TpRuleDTO, CTpRuleDTO> {

    @Override
    public String getIndex() {
        return ESConst.INDEX_TP_RULE;
    }

    @Override
    public String getType() {
        return ESConst.INDEX_TP_RULE;
    }

    @PostConstruct
    public void init() {
        super.initIndex();
    }

    public List<TpRuleDTO> getTpRuleList(TpRuleReqDTO body) {
        Long domainId = body.getDomainId() ==null  ? BaseConst.DEFAULT_DOMAIN_ID : body.getDomainId();
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.must(QueryBuilders.termQuery("domainId", domainId));
        if (!BinaryUtils.isEmpty(body.getSearchText())) {
            boolQuery
                .must(QueryBuilders.multiMatchQuery(body.getSearchText(), "description", "ruleName", "creator").operator(Operator.AND).type(MultiMatchQueryBuilder.Type.PHRASE_PREFIX).lenient(true));
        }
        if (!BinaryUtils.isEmpty(body.getRuleType())) {
            boolQuery.must(QueryBuilders.termQuery("ruleType.keyword", body.getRuleType()));
        }
        if (!BinaryUtils.isEmpty(body.getClassId())) {
            boolQuery.must(QueryBuilders.termQuery("classId", body.getClassId()));
        }
        return this.getSortListByQuery(1, 10000, boolQuery, Arrays.asList(SortBuilders.fieldSort("createTime").order(SortOrder.DESC).unmappedType("integer"))).getData();
    }
}
