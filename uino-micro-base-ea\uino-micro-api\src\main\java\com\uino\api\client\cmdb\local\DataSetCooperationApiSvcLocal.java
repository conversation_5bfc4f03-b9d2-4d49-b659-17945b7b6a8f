package com.uino.api.client.cmdb.local;

import com.alibaba.fastjson.JSONObject;
import com.uino.service.cmdb.dataset.microservice.IDataSetCooperationSvc;
import com.uino.api.client.cmdb.IDataSetCooperationApiSvc;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Classname DataSetCooperationApiSvcLocal
 * @Description TODO
 * @Date 2020/6/5 11:18
 * <AUTHOR> sh
 */
@Service
public class DataSetCooperationApiSvcLocal implements IDataSetCooperationApiSvc {

    @Autowired
    private IDataSetCooperationSvc dataSetCooperationSvc;
    @Override
    public List<JSONObject> findByDataSetId(Long dataSetId) {
        return dataSetCooperationSvc.findByDataSetId(dataSetId);
    }

    @Override
    public boolean updateCooperation(Long dataSetId, List<JSONObject> coordinationUserCodeList) {
        return dataSetCooperationSvc.updateCooperation(dataSetId,coordinationUserCodeList);
    }
}
