package com.uinnova.product.eam.service.asset;

import com.binary.core.exception.BinaryException;
import com.uinnova.product.eam.base.model.AttrConfInfo;
import com.uinnova.product.eam.base.model.AttrDefInfo;
import com.uinnova.product.eam.base.model.FileResourceMeta;
import com.uinnova.product.eam.model.asset.ArchitecturalDTO;
import com.uinnova.product.eam.model.asset.ArchitecturalNameCheckDTO;
import com.uinnova.product.eam.model.asset.ArchitecturalResolutionDTO;
import com.uinnova.product.eam.model.asset.SearchArchitecturalDTO;
import com.uino.bean.cmdb.base.ESCIInfo;

import java.util.List;
import java.util.Map;

public interface ArchitecturalSvc {
    Long createArchitecturalResolution(ArchitecturalDTO architecturalDTO);
    List<Map<String,Object>> getInfoBySubsystemCode(ArchitecturalResolutionDTO architecturalResolutionDTO);
    ArchitecturalResolutionDTO getInfoByCiId(Long ciId);

    AttrDefInfo selectAttrConf(AttrConfInfo attrConfInfo);

    boolean checkArchitecturalName(ArchitecturalNameCheckDTO architecturalNameCheckDTO);

    ESCIInfo searchArchitecturalDTO(SearchArchitecturalDTO searchArchitecturalDTO);

    List<Long> getResIdsById(Long architecturalId);

    List<FileResourceMeta> download(List<Long> resIds);

    List<Long> changeSubsystemCode(String sourceCode, String targetCode, Long ciClassIdByName) throws BinaryException;
}
