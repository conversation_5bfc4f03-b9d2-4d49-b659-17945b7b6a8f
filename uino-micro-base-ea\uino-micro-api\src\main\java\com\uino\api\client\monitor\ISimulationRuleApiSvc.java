package com.uino.api.client.monitor;

import java.util.List;

import com.binary.jdbc.Page;
import com.uino.bean.monitor.base.SimulationRuleInfo;
import com.uino.bean.monitor.query.SimulationRuleSearchBean;

/**
 * 
 * <AUTHOR>
 * @Date 2021-08-13
 */
public interface ISimulationRuleApiSvc {

	/**
	 * 分页查询模拟规则
	 * 
	 * @param bean
	 * @return
	 */
	Page<SimulationRuleInfo> querySimulationRuleInfoPage(SimulationRuleSearchBean bean);

	/**
	 * 查询模拟规则列表
	 * 
	 * @param bean
	 * @return
	 */
	List<SimulationRuleInfo> querySimulationRuleInfoList(SimulationRuleSearchBean bean);

	/**
	 * 保存数据模拟规则
	 * 
	 * @param ruleInfo
	 * @return
	 */
	Long saveOrUpdateSimulationRule(SimulationRuleInfo ruleInfo);

	/**
	 * 根据id删除数据模拟规则
	 * 
	 * @param id
	 * @return
	 */
	Integer deleteSimulationRuleById(Long id);
}
