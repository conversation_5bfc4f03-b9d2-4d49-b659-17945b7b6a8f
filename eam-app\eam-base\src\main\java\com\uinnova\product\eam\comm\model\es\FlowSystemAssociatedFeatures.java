package com.uinnova.product.eam.comm.model.es;

import com.uinnova.product.eam.base.cj.BaseEntity;
import lombok.*;

import java.util.Objects;

/**
 * description
 *
 * <AUTHOR>
 * @since 2024/5/27 14:52
 */
@Getter
@Setter
@RequiredArgsConstructor
@ToString
public class FlowSystemAssociatedFeatures {

    private Long id;

    private String ciCode;

    private String classCode;

    private String linkedCiCode;

    private Integer status;

    private String modifier;

    private String creator;

    private Long versionId;


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        FlowSystemAssociatedFeatures that = (FlowSystemAssociatedFeatures) o;
        return Objects.equals(ciCode, that.ciCode) && Objects.equals(classCode, that.classCode) && Objects.equals(linkedCiCode, that.linkedCiCode);
    }

    @Override
    public int hashCode() {
        return Objects.hash(ciCode, classCode, linkedCiCode);
    }
}
