package com.uino.api.client.sys.local;

import com.binary.jdbc.Page;
import com.uino.api.client.sys.ITenantDomainApiSvc;
import com.uino.bean.sys.base.TenantDomain;
import com.uino.service.sys.microservice.ITenantDomainSvc;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class TenantDomainApiSvcLocal implements ITenantDomainApiSvc {

    @Autowired
    private ITenantDomainSvc svc;

    @Override
    public Long saveOrUpdate(TenantDomain tenantDomain) {
        return svc.saveOrUpdate(tenantDomain);
    }

    @Override
    public Page<TenantDomain> queryPage(int pageNum, int pageSize, String name) {
        return svc.queryPage(pageNum, pageSize, name);
    }

    @Override
    public Long deleteById(Long id) {
        return svc.deleteById(id);
    }

    @Override
    public Long startOrStop(Long id) {
        return svc.startOrStop(id);
    }

    @Override
    public boolean resetPasswdByAdmin(Long id) {
        return svc.resetPasswdByAdmin(id);
    }

    @Override
    public List<TenantDomain> queryAvailableList() {
        return svc.queryAvailableList();
    }
}
