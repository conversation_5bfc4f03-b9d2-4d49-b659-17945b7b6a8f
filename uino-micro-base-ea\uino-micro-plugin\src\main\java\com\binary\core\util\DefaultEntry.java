package com.binary.core.util;

import java.io.Serializable;
import java.util.Map.Entry;

public class DefaultEntry<K, V> implements Entry<K, V>, Serializable {
	private static final long serialVersionUID = 1L;
	
	
	private K key;
	private V value;
	
	
	
	public DefaultEntry() {
	}
	public DefaultEntry(K key, V value) {
		this.key = key;
		this.value = value;
	}
	
	
	
	
	public K getKey() {
		return key;
	}
	public void setKey(K key) {
		this.key = key;
	}
	public V getValue() {
		return value;
	}
	public V setValue(V value) {
		V old = this.value;
		this.value = value;
		return old;
	}
	
	
	
	
	
}
