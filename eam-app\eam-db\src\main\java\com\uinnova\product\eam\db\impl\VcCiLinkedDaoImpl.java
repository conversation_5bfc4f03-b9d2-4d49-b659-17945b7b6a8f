package com.uinnova.product.eam.db.impl;


import com.uinnova.product.eam.comm.model.CVcCiLinked;
import com.uinnova.product.eam.comm.model.VcCiLinked;
import com.uinnova.product.eam.db.VcCiLinkedDao;
import com.uinnova.product.eam.db.support.dao.mybatis.ComMyBatisBinaryDaoImpl;


/**
 * CI链路表[VC_CI_LINKED]数据访问对象实现
 */
public class VcCiLinkedDaoImpl extends ComMyBatisBinaryDaoImpl<VcCiLinked, CVcCiLinked> implements VcCiLinkedDao {

//    @Override
//    public String getTableName() {
//        return "IAMS." + getDaoDefinition().getTableName();
//    }
}


