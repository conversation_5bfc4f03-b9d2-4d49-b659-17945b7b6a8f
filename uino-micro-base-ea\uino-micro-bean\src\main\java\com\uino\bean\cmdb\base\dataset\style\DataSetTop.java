package com.uino.bean.cmdb.base.dataset.style;

import com.alibaba.fastjson.JSONObject;

/**
 * @Classname TopDataSet
 * @Description 置顶
 * @Date 2020/3/18 21:27
 * @Created by sh
 */
public class DataSetTop {
    //    用户code，规则id，时间
    private Long id;
    private String userCode;
    private Long dataSetMallApiId;
    private Long createTime;
    private Long modifyTime;
    private Long domainId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getDataSetMallApiId() {
        return dataSetMallApiId;
    }

    public void setDataSetMallApiId(Long dataSetMallApiId) {
        this.dataSetMallApiId = dataSetMallApiId;
    }

    public String getUserCode() {
        return userCode;
    }

    public void setUserCode(String userCode) {
        this.userCode = userCode;
    }


    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Long modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Long getDomainId() {
        return domainId;
    }

    public void setDomainId(Long domainId) {
        this.domainId = domainId;
    }

    public DataSetTop() {
    }

    public DataSetTop(JSONObject json) {
        if (json.containsKey("id")) {
            this.id = json.getLong("id");
        }
        if (json.containsKey("domainId")) {
            this.domainId = json.getLong("domainId");
        }
        if (json.containsKey("userCode")) {
            this.userCode = json.getString("userCode");
        }
        if (json.containsKey("dataSetMallApiId")) {
            this.dataSetMallApiId = json.getLong("dataSetMallApiId");
        }
        if (json.containsKey("createTime")) {
            this.createTime = json.getLong("createTime");
        }
        if (json.containsKey("modifyTime")) {
            this.modifyTime = json.getLong("modifyTime");
        }
    }

    public JSONObject toJson() {
        JSONObject json = new JSONObject();
        json.put("id", id);
        json.put("domainId", domainId);
        json.put("userCode", userCode);
        json.put("dataSetMallApiId", dataSetMallApiId);
        json.put("createTime", createTime);
        json.put("modifyTime", modifyTime);
        return json;
    }
}
