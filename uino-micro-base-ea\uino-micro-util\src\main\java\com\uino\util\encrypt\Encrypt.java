package com.uino.util.encrypt;

/**
 * @Title: Encrypt
 * @Description:
 * @Author: YGQ
 * @Create: 2021-08-08 23:12
 **/
public interface Encrypt {

    /**
     * encrypt and decode
     *
     * @param type      encryption and decryption type {@link EncryptType}
     * @param str       pending string
     * @param publicKey public key optional
     *                  <p>
     *                  {@code EncryptType.AesEncrypt}, public key needs 16 bits
     *                  <p>
     *                  {@code EncryptType.AesDecrypt}, public key needs 16 bits
     *                  <p>
     *                  {@code EncryptType.DesEncrypt}, public key needs 8 bits
     *                  <p>
     *                  {@code EncryptType.DesDecrypt}, public key needs 8 bits
     *                  <p>
     *                  {@code EncryptType.JasyptEncrypt}, need public key
     *                  <p>
     *                  {@code EncryptType.JasyptDecrypt}, need public key
     *                  <p>
     *                  {@code EncryptType.RsaEncrypt}, need public key
     *                  <p>
     *                  {@code EncryptType.RsaDecrypt}, need public key
     *                  <p>
     *                  {@code EncryptType.Base64Encrypt}
     *                  <p>
     *                  {@code EncryptType.Base64Decrypt}
     * @return {@link String}
     */
    String encryptOrDecrypt(EncryptType type, String str, String... publicKey);
}
