package com.uinnova.product.eam.web.diagram.bean;

import java.util.List;

import com.binary.framework.bean.annotation.Comment;
import com.binary.jdbc.Page;
import com.uinnova.product.eam.comm.model.VcDiagramDir;
import com.uinnova.product.eam.model.VcDiagramInfo;

@Comment("带置顶功能的视图信息返回结果")
public class VcDiagramInfoResultWithTopping {
	
	@Comment("置顶的视图")
	private List<VcDiagramInfo> toppingDiagrams;
	
	@Comment("分页查询的视图")
	private Page<VcDiagramInfo> diagramPageInfos;
	
	@Comment("子文件夹信息")
	private List<VcDiagramDir> childrenDirs;
	
	public List<VcDiagramDir> getChildrenDirs() {
		return childrenDirs;
	}

	public void setChildrenDirs(List<VcDiagramDir> childrenDirs) {
		this.childrenDirs = childrenDirs;
	}
	
	public List<VcDiagramInfo> getToppingDiagrams() {
		return toppingDiagrams;
	}

	public void setToppingDiagrams(List<VcDiagramInfo> toppingDiagrams) {
		this.toppingDiagrams = toppingDiagrams;
	}

	public Page<VcDiagramInfo> getDiagramPageInfos() {
		return diagramPageInfos;
	}

	public void setDiagramPageInfos(Page<VcDiagramInfo> diagramPageInfos) {
		this.diagramPageInfos = diagramPageInfos;
	}
	
	

}
