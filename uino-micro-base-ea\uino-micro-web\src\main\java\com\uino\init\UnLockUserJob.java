package com.uino.init;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.uino.api.client.permission.IUserApiSvc;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RefreshScope
public class UnLockUserJob {

	/**
	 * 用户锁定时长-单位秒
	 */
	@Value("${user.lock.duration:600}")
	private long userLockDuration;
	@Autowired
	private IUserApiSvc userApi;

	@Scheduled(fixedDelay = 1000L * 10, initialDelay = 1000L * 10)
	public void syncUnLock() {
		try {
			userApi.unLockUsersByDuration(userLockDuration);
		} catch (Exception e) {
			log.error("解锁用户发生异常", e);
		}
	}
}
