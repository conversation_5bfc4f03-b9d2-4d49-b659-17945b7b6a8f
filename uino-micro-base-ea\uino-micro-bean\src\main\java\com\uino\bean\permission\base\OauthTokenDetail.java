package com.uino.bean.permission.base;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * token存储详情实体
 * 
 * <AUTHOR>
 *
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OauthTokenDetail implements Serializable {
	private static final long serialVersionUID = 1L;
	/**
	 * 主键
	 */
	private Long id;

	/**
	 * token唯一标识
	 */
	private String tokenCode;
	/**
	 * token值，应该是一个json，存进去看看
	 */
	private byte[] tokenVal;

	/**
	 * 权限id？还没太明白具体用途
	 */
	private String authenticationId;

	/**
	 * 用户logincode
	 */
	private String userName;

	/**
	 * 客户端标识
	 */
	private String client;

	/**
	 * 应该是对这个token的权限表述，暂存储字符串
	 */
	private byte[] authentication;

	/**
	 * 刷新token（暂不提供刷新token方式）
	 */
	private String refreshToken;

	/**
	 * token过期时间
	 */
	private long expirationTime;

	/** 所属域 */
	private Long domainId;

	/** 创建人 */
	private String creator;

	/** 修改人 */
	private String modifier;

	/** 创建时间 */
	private Long createTime;

	/** 修改时间 */
	private Long modifyTime;
}
