package com.uinnova.product.eam.web.eam.mvc;

import com.binary.framework.web.RemoteResult;
import com.uinnova.product.eam.model.BusinessConfigResponseVo;
import com.uinnova.product.eam.model.BusinessConfigVo;
import com.uinnova.product.eam.service.BusinessConfigSvc;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * @description: 功能配置
 * @author: Lc
 * @create: 2022-08-05 18:24
 */
@RestController
@RequestMapping("/business/config")
public class BusinessConfigMvc {

    @Resource
    private BusinessConfigSvc businessConfigSvc;

    @GetMapping("/getBusinessConfigList")
    public RemoteResult getBusinessConfigList() {
        List<BusinessConfigResponseVo> businessConfigList = businessConfigSvc.getBusinessConfigList();
        return new RemoteResult(businessConfigList);
    }

    @PostMapping("/exportConfig")
    public ResponseEntity<byte[]> exportConfig(@RequestBody BusinessConfigVo businessConfigVo) {
        return businessConfigSvc.exportConfig(businessConfigVo);
    }

}
