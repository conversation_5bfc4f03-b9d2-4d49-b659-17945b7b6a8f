package com.uinnova.product.eam.web.asset.bean;

import com.uinnova.product.eam.base.model.ResourceInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
public class ArchitecturalResolutionVO implements Serializable {
    private Long id;
    private Long classId;
    private String subsystemCode;
    private String ciCode;
    private List<ResourceInfo> resources;
    private Map<String,String> attrs;
}
