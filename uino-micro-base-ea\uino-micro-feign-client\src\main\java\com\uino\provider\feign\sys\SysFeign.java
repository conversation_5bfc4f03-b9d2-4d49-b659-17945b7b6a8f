package com.uino.provider.feign.sys;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import com.binary.core.io.Resource;
import com.uino.provider.feign.config.BaseFeignConfig;

@Component
@FeignClient(name = "${feign-server-name:tarsier-platform}", path = "${feign-server-path:tarsier-platform}/feign/sys", configuration = {
        BaseFeignConfig.class})
public interface SysFeign {

    /**
     * 上传文件
     * 
     * @param file
     *            待上传文件
     * @return 文件路径
     */
    @PostMapping("uploadFile")
    public String uploadFile(@RequestParam(required = false, value = "file") MultipartFile file);

    @PostMapping("downloadFile")
    public Resource downloadFile(@RequestParam(value = "filePath") String filePath);

    /**
     * 上传文件
     * 
     * @param fileBytes
     *            待上传文件字节流
     * @param fileName
     *            文件名称
     * @return 文件路径
     */
    @PostMapping("uploadFile/{fileName}")
    public String uploadFile(@RequestBody(required = false) byte[] fileBytes,
            @PathVariable(value = "fileName") String fileName);
}
