package com.uino.provider.server.web.cmdb.mvc;

import java.util.Collection;
import java.util.List;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.binary.jdbc.Page;
import com.uinnova.product.vmdb.comm.model.ci.CCcCiClass;
import com.uinnova.product.vmdb.provider.ci.bean.CcCiClassInfo;
import com.uino.service.cmdb.microservice.ICIClassSvc;
import com.uino.bean.cmdb.base.ESCIClassInfo;
import com.uino.bean.cmdb.business.ClassNodeInfo;
import com.uino.bean.cmdb.business.ClassReOrderDTO;
import com.uino.bean.cmdb.business.ImportResultMessage;
import com.uino.bean.cmdb.query.ESCIClassSearchBean;
import com.uino.provider.feign.cmdb.CIClassFeign;

/**
 * 
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("feign/ciClass")
public class CIClassFeignMvc implements CIClassFeign {

    @Autowired
    private ICIClassSvc classSvc;

    @Override
    public List<ClassNodeInfo> getClassTree(Long domainId) {
        return classSvc.getClassTree(domainId);
    }

    @Override
    public ESCIClassInfo queryESClassInfoById(Long id) {
        return classSvc.queryESClassInfoById(id);
    }

    @Override
    public CcCiClassInfo queryClassById(Long id) {
        return classSvc.queryClassInfoById(id);
    }

    @Override
    public List<CcCiClassInfo> queryClassByCdtWithoutBody() {
        return classSvc.queryClassByCdt(null);
    }

    @Override
    public List<CcCiClassInfo> queryClassByCdt(CCcCiClass cdt) {
        return classSvc.queryClassByCdt(cdt);
    }

    @Override
    public List<CcCiClassInfo> queryCiClassInfoListWithoutBody(Long domainId, String orders, Boolean isAsc) {
        return classSvc.queryCiClassInfoList(domainId, null, orders, isAsc);
    }

    @Override
    public List<CcCiClassInfo> queryCiClassInfoList(Long domainId, CCcCiClass cdt, String orders, Boolean isAsc) {
        return classSvc.queryCiClassInfoList(domainId, cdt, orders, isAsc);
    }

    @Override
    public List<CcCiClassInfo> queryCiClassInfoListBySearchBean(ESCIClassSearchBean bean) {
        return classSvc.queryCiClassInfoListBySearchBean(bean);
    }

    @Override
    public List<ESCIClassInfo> queryESCiClassInfoListBySearchBean(ESCIClassSearchBean bean) {
        return classSvc.queryESCiClassInfoListBySearchBean(bean);
    }

    @Override
	public Page<ESCIClassInfo> queryESCiClassInfoPageBySearchBean(ESCIClassSearchBean bean) {
		return classSvc.queryESCiClassInfoPageBySearchBean(bean);
	}

	@Override
    public Long saveOrUpdateCIClass(CcCiClassInfo record) {
        return classSvc.saveOrUpdate(record);
    }

    @Override
    public Long saveOrUpdateESCIClass(ESCIClassInfo record) {
        return classSvc.saveOrUpdate(record);
    }

    @Override
    public Integer saveOrUpdateBatch(Long domainId, List<CcCiClassInfo> clsInfos) {
        return classSvc.saveOrUpdateBatch(domainId, clsInfos);
    }

    @Override
    public Integer removeCIClassById(Long id) {
        return classSvc.deleteById(id);
    }

    @Override
    public ImportResultMessage importCiClassAttr(MultipartFile file, Long ciClsId) {
        return classSvc.importCiClassAttr(file, ciClsId);
    }

    @Override
    public ResponseEntity<byte[]> exportClassAttrExcel(Set<Long> clsIds, Boolean isBatchTpl) {
        return classSvc.exportClassAttrExcel(clsIds, isBatchTpl);
    }

    @Override
    public boolean reOrder(ClassReOrderDTO reOrderDTO) {
        return classSvc.reOrder(reOrderDTO);
    }

    @Override
    public boolean initAllOrderNo(Long domainId) {
        return classSvc.initAllOrderNo(domainId);
    }

    @Override
    public List<ESCIClassInfo> getTargetAttrDefsByClassIds(Long domainId, Collection<Long> classIds) {
        return classSvc.getTargetAttrDefsByClassIds(domainId, classIds);
    }

	@Override
	public List<String> queryAttrDefGroupList(Long domainId) {
		return classSvc.queryAttrDefGroupList(domainId);
	}

    @Override
    public String getHttpResourceSpace() {
        return classSvc.getHttpResourceSpace();
    }

    @Override
    public Boolean isShow3dAttribute() {
        return classSvc.isShow3dAttribute();
    }

    @Override
    public List<ESCIClassInfo> queryEsClassInfoByDtClassIds(List<String> dtClassIds, Long domainId) {
        return classSvc.queryEsClassInfoByDtClassIds(dtClassIds, domainId);
    }
}
