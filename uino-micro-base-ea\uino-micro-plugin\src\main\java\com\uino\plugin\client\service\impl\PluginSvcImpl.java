package com.uino.plugin.client.service.impl;

import com.uino.plugin.bean.OperatePluginDetails;
import com.uino.plugin.client.init.DefaultClassLoaderAware;
import com.uino.plugin.client.service.PluginSvc;
import com.uino.plugin.classloader.ClassloaderRepository;
import com.uino.plugin.classloader.ExceptionInfoUtil;
import com.uino.plugin.classloader.PluginClassLoader;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;

/**
 * <AUTHOR>
 * @Description
 * @Date 2021/9/26
 * @Version 1.0
 */
@Slf4j
@Service
public class PluginSvcImpl implements PluginSvc {

    @Override
    public OperatePluginDetails uploadPlugin(MultipartFile file) {
        String fileName = null;
        try {
            byte[] fileBytes = file.getBytes();
            fileName = file.getOriginalFilename();
            FileOutputStream fos = new FileOutputStream(PluginClassLoader._LOADING_PLUGIN_PATH + fileName);
            fos.write(fileBytes);
            fos.close();
        } catch (IOException e) {
            log.error("获取上传文件流异常", e);
            return new OperatePluginDetails(fileName, null, null, false, ExceptionInfoUtil.getExceptionAllInfo(e));
        }
        return new OperatePluginDetails(fileName, null, null, true, null);
    }

    @Override
    public OperatePluginDetails loadPlugin(String jarName) {
        boolean loadJar;
        try {
            loadJar = ClassloaderRepository.loadJar(jarName);
        }catch (Throwable e){
            String exceptionMessage = ExceptionInfoUtil.getExceptionAllInfo(e);
            log.error("load plugin: {} error, message: {}", jarName, exceptionMessage);
            return new OperatePluginDetails(jarName, null, null, false, exceptionMessage);
        }
        if (loadJar) {
            return new OperatePluginDetails(jarName, null, null, true, null);
        } else {
            File file = new File(PluginClassLoader._LOADING_PLUGIN_PATH + jarName);
            if (file.exists()) {
                file.delete();
            }
            return DefaultClassLoaderAware.getCacheResultMap(jarName);
        }
    }

    @Override
    public OperatePluginDetails unloadAndDeletePlugin(String jarName) {
        boolean unloadJar = ClassloaderRepository.unloadJar(jarName);
        File file = new File(PluginClassLoader._LOADING_PLUGIN_PATH + jarName);
        if (file.exists()) {
            file.delete();
        }
        if (unloadJar) {
            return new OperatePluginDetails(jarName, null, null, true, null);
        } else {
            return DefaultClassLoaderAware.getCacheResultMap(jarName);
        }
    }
}
