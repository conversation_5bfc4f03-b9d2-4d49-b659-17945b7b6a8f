package com.uino.bean.cmdb.base.dataset.relation;

public enum RelationRuleLineOp {
	//
	Between(1),
	<PERSON><PERSON>(2),
	<PERSON><PERSON>(3);
	
	private Integer code;
	
	private RelationRuleLineOp(Integer code) {
		this.code = code;
	}
	
	public Integer getOp() {
		return this.code;
	}
	
	public static RelationRuleLineOp valueOf(Integer code) {
		if (Between.code.equals(code)) {
            return Between;
        } else if (Gte.code.equals(code)) {
            return Gte;
        } else if (Lte.code.equals(code)) {
            return Lte;
        } else {
            return null;
        }
	}
}
