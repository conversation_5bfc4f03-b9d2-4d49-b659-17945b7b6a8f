package com.uino.tarsier.tarsiercom.util;

import java.util.Iterator;
import java.util.Properties;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.jasypt.encryption.pbe.PooledPBEStringEncryptor;
import org.jasypt.encryption.pbe.config.SimpleStringPBEConfig;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.BeanInitializationException;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.beans.factory.config.PropertyPlaceholderConfigurer;


public class PropertyPlaceholderConfigurerExt extends PropertyPlaceholderConfigurer{
	
	public final static String JASY_SALT = "dix@Uinnova";
	
	private PooledPBEStringEncryptor encryptor = null;
	
	@Override
	protected void processProperties(ConfigurableListableBeanFactory beanFactory, Properties props)
            throws BeansException {

		String strPattern = "^\\s*ENC\\((.*)\\)"; 
			
		
		try {
			
			encryptor = createDefault();
			
			Pattern pattern =Pattern.compile(strPattern);
			
			Iterator<Object> itPropsKey = props.keySet().iterator();
			while(itPropsKey.hasNext()) {
				String key = String.valueOf(itPropsKey.next());
				String value = String.valueOf(props.get(key));
				
				//如果ENC(***)格式, 做解密处理
				Matcher matcher1 = pattern.matcher(value);
				if(matcher1.matches()) {
					String encrypted = matcher1.group(1);					
					//解密
					String orign = decrypt(encrypted);
					props.setProperty(key, orign);
				}
			}
			
			super.processProperties(beanFactory, props);
			
		} catch (Exception exp) {
			throw new BeanInitializationException(exp.getMessage(), exp);
		}
	}
	
	public String decrypt(String encrypted){
		if(encryptor==null) {
			encryptor = createDefault();
		}
		String result = encryptor.decrypt(encrypted);
		return result;
	}
	
	public String encrypt(String orign){
		if(encryptor==null) {
			encryptor = createDefault();
		}
		String result = encryptor.encrypt(orign);
		return result;
	} 
	
    private PooledPBEStringEncryptor createDefault() {
        PooledPBEStringEncryptor encryptor = new PooledPBEStringEncryptor();
        SimpleStringPBEConfig config = new SimpleStringPBEConfig();
        config.setPassword(JASY_SALT);
        config.setAlgorithm("PBEWithMD5AndDES");
        config.setKeyObtentionIterations("1000");
        config.setPoolSize("1");
        config.setProviderName("SunJCE");
        config.setSaltGeneratorClassName("org.jasypt.salt.RandomSaltGenerator");
        config.setIvGeneratorClassName("org.jasypt.salt.NoOpIVGenerator");
        config.setStringOutputType("base64");
        encryptor.setConfig(config);
        return encryptor;
    }
}
