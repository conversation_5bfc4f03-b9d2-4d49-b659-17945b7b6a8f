package com.uino.service.permission.microservice;

import java.util.List;

import com.uino.bean.permission.base.OAuthClientDetail;
import com.uino.bean.permission.business.request.RegisterClientReq;

/**
 * 
 * <AUTHOR>
 *
 */
public interface IOAuthClientSvc {
	/**
	 * 根据客户端code获取客户端详情
	 * 
	 * @param clientCode
	 * @return
	 */
	public OAuthClientDetail getClientInfoByCode(String clientCode);

	/**
	 * 获取所有客户端
	 * 
	 * @return
	 */
	public List<OAuthClientDetail> getAllClient();

	/**
	 * 注册客户端
	 * 
	 * @param req
	 */
	public void register(RegisterClientReq req);

	/**
	 * 根据code移除客户端
	 * 
	 * @param clientCode
	 */
	public void removeClientInfoByCode(String clientCode);

}
