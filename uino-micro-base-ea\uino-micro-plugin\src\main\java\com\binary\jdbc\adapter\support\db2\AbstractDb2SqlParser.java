package com.binary.jdbc.adapter.support.db2;

import com.binary.jdbc.adapter.SqlDissolver;
import com.binary.jdbc.adapter.support.AbstractSqlParser;
import com.binary.jdbc.exception.SqlParserException;

public abstract class AbstractDb2SqlParser extends AbstractSqlParser {

	
	

	@Override
	public String parseCountSql(String sql) {
		return "select count(1) from ("+sql+")";
	}
	

	
	@Override
	public String parseSimpleCountSql(String sql) {
		return parseCountSql(sql);
	}
	
	

	
	@Override
	public String parsePagingSql(SqlDissolver dissolver, String orderByFields, long pageNum, long pageSize) {
		return paging(dissolver, orderByFields, pageNum, pageSize);
	}
	
	
	
	private String paging(SqlDissolver dissolver, String orderByFields, long pageNum, long pageSize) {
		if(orderByFields==null || (orderByFields=orderByFields.trim()).length()==0) throw new SqlParserException(" the orderByFields is NULL argument! ");
		String[] fields = dissolver.getFields();
		String selectSql = dissolver.getSelectSql();
		String fromSql = dissolver.getFromSql();
		
		StringBuffer sb = new StringBuffer();
		sb.append("select ");
		
		for(int i=0; i<fields.length; i++) {
			if(i > 0) sb.append(", ");
			sb.append(fields[i]);
		}
		
		String baseSelect = sb.substring(0, sb.length());
		long startRow = (pageNum - 1) * pageSize + 1;
		long endRow = startRow + pageSize;
		
		sb.delete(0, sb.length());
		sb.append(baseSelect).append(" from (").append(selectSql).append(", ROW_NUMBER() OVER(order by ").append(orderByFields).append(") alias_for_rownum ").append(fromSql)
					.append(" order by ").append(orderByFields).append(") where alias_for_rownum<").append(endRow).append(" and alias_for_rownum>=").append(startRow);
		return sb.toString();
	}
	
	
	
}
